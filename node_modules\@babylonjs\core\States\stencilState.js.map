{"version": 3, "file": "stencilState.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/States/stencilState.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAGjD;;IAEI;AACJ,MAAM,OAAO,YAAY;IAQrB;QACI,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,IAAI,CAAC;QACvC,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC,kBAAkB,GAAG,YAAY,CAAC,OAAO,CAAC;IACnD,CAAC;IAGD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;IACtB,CAAC;IAGD,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,cAAc,CAAC,KAAa;QACnC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAGD,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,eAAe,CAAC,KAAa;QACpC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAGD,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAa;QACzC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC/B,CAAC;IAGD,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAa;QACvC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAGD,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED,IAAW,yBAAyB,CAAC,KAAa;QAC9C,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACpC,CAAC;IAGD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;IACtB,CAAC;IAGD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,WAAW,CAAC,KAAc;QACjC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;;AA9FD,2JAA2J;AACpI,mBAAM,GAAG,SAAS,CAAC,MAAM,CAAC;AACjD,4EAA4E;AACrD,iBAAI,GAAG,SAAS,CAAC,IAAI,CAAC;AAC7C,gFAAgF;AACzD,oBAAO,GAAG,SAAS,CAAC,OAAO,CAAC", "sourcesContent": ["import { Constants } from \"../Engines/constants\";\r\nimport type { IStencilState } from \"./IStencilState\";\r\n\r\n/**\r\n * @internal\r\n **/\r\nexport class StencilState implements IStencilState {\r\n    /** Passed to depthFunction or stencilFunction to specify depth or stencil tests will always pass. i.e. Pixels will be drawn in the order they are drawn */\r\n    public static readonly ALWAYS = Constants.ALWAYS;\r\n    /** Passed to stencilOperation to specify that stencil value must be kept */\r\n    public static readonly KEEP = Constants.KEEP;\r\n    /** Passed to stencilOperation to specify that stencil value must be replaced */\r\n    public static readonly REPLACE = Constants.REPLACE;\r\n\r\n    public constructor() {\r\n        this.reset();\r\n    }\r\n\r\n    public reset() {\r\n        this.enabled = false;\r\n        this.mask = 0xff;\r\n\r\n        this.func = StencilState.ALWAYS;\r\n        this.funcRef = 1;\r\n        this.funcMask = 0xff;\r\n\r\n        this.opStencilFail = StencilState.KEEP;\r\n        this.opDepthFail = StencilState.KEEP;\r\n        this.opStencilDepthPass = StencilState.REPLACE;\r\n    }\r\n\r\n    public func: number;\r\n    public get stencilFunc(): number {\r\n        return this.func;\r\n    }\r\n\r\n    public set stencilFunc(value: number) {\r\n        this.func = value;\r\n    }\r\n\r\n    public funcRef: number;\r\n    public get stencilFuncRef(): number {\r\n        return this.funcRef;\r\n    }\r\n\r\n    public set stencilFuncRef(value: number) {\r\n        this.funcRef = value;\r\n    }\r\n\r\n    public funcMask: number;\r\n    public get stencilFuncMask(): number {\r\n        return this.funcMask;\r\n    }\r\n\r\n    public set stencilFuncMask(value: number) {\r\n        this.funcMask = value;\r\n    }\r\n\r\n    public opStencilFail: number;\r\n    public get stencilOpStencilFail(): number {\r\n        return this.opStencilFail;\r\n    }\r\n\r\n    public set stencilOpStencilFail(value: number) {\r\n        this.opStencilFail = value;\r\n    }\r\n\r\n    public opDepthFail: number;\r\n    public get stencilOpDepthFail(): number {\r\n        return this.opDepthFail;\r\n    }\r\n\r\n    public set stencilOpDepthFail(value: number) {\r\n        this.opDepthFail = value;\r\n    }\r\n\r\n    public opStencilDepthPass: number;\r\n    public get stencilOpStencilDepthPass(): number {\r\n        return this.opStencilDepthPass;\r\n    }\r\n\r\n    public set stencilOpStencilDepthPass(value: number) {\r\n        this.opStencilDepthPass = value;\r\n    }\r\n\r\n    public mask: number;\r\n    public get stencilMask(): number {\r\n        return this.mask;\r\n    }\r\n\r\n    public set stencilMask(value: number) {\r\n        this.mask = value;\r\n    }\r\n\r\n    public enabled: boolean;\r\n    public get stencilTest(): boolean {\r\n        return this.enabled;\r\n    }\r\n\r\n    public set stencilTest(value: boolean) {\r\n        this.enabled = value;\r\n    }\r\n}\r\n"]}