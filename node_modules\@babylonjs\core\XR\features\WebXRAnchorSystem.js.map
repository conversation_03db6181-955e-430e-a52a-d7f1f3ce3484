{"version": 3, "file": "WebXRAnchorSystem.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRAnchorSystem.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAEjF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAEtE,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AA4EzC,IAAI,gBAAgB,GAAG,CAAC,CAAC;AAEzB;;;GAGG;AACH,MAAM,OAAO,iBAAkB,SAAQ,oBAAoB;IAkCvD;;;OAGG;IACH,IAAW,6BAA6B,CAAC,cAAgC;QACrE,IAAI,CAAC,8BAA8B,GAAG,cAAc,CAAC;IACzD,CAAC;IAED;;;;OAIG;IACH,YACI,iBAAsC,EAC9B,WAAsC,EAAE;QAEhD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFjB,aAAQ,GAAR,QAAQ,CAAgC;QAhD5C,uBAAkB,GAAgB,IAAI,GAAG,EAAE,CAAC;QAE5C,oBAAe,GAAwB,EAAE,CAAC;QAI1C,mBAAc,GAAyB,EAAE,CAAC;QAalD;;WAEG;QACI,4BAAuB,GAA6B,IAAI,UAAU,EAAE,CAAC;QAC5E;;WAEG;QACI,8BAAyB,GAA6B,IAAI,UAAU,EAAE,CAAC;QAC9E;;;WAGG;QACI,8BAAyB,GAA6B,IAAI,UAAU,EAAE,CAAC;QAuBtE,eAAU,GAAG,IAAI,OAAO,EAAE,CAAC;QAC3B,mBAAc,GAAG,IAAI,UAAU,EAAE,CAAC;QAJtC,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;IACzC,CAAC;IAKO,0BAA0B,CAAC,QAAiB,EAAE,kBAA8B;QAChF,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACnC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,EAAE;YACpD,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;SAC/B;QACD,OAAO;YACH,QAAQ,EAAE,IAAI,CAAC,UAAU;YACzB,kBAAkB,EAAE,IAAI,CAAC,cAAc;SAC1C,CAAC;IACN,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,qCAAqC,CAC9C,aAA8B,EAC9B,WAAoB,IAAI,OAAO,EAAE,EACjC,qBAAiC,IAAI,UAAU,EAAE;QAEjD,+CAA+C;QAC/C,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;QAC9D,4BAA4B;QAC5B,MAAM,CAAC,GAAG,IAAI,gBAAgB,CAC1B,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,EACpE,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAC7G,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,EAAE;YACzC,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;SACtE;aAAM;YACH,IAAI;gBACA,MAAM,YAAY,GAAG,MAAM,aAAa,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACrE,OAAO,IAAI,OAAO,CAAe,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACjD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;wBACrB,YAAY;wBACZ,QAAQ,EAAE,KAAK;wBACf,SAAS,EAAE,IAAI;wBACf,gBAAgB,EAAE,CAAC;wBACnB,OAAO;wBACP,MAAM;qBACT,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;aACN;YAAC,OAAO,KAAK,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;aAC1B;SACJ;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACI,KAAK,CAAC,mCAAmC,CAC5C,QAAiB,EACjB,qBAAiC,IAAI,UAAU,EAAE,EACjD,yBAAyB,GAAG,KAAK;QAEjC,+CAA+C;QAC/C,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;QAC9D,4BAA4B;QAC5B,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CACzC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,EACpE,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAC7G,CAAC;QACF,MAAM,QAAQ,GACV,yBAAyB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY;YAC7E,CAAC,CAAC,MAAM,IAAI,CAAC,6BAA6B,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;YACjG,CAAC,CAAC,SAAS,CAAC;QACpB,oDAAoD;QACpD,OAAO,IAAI,OAAO,CAAe,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACjD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBACrB,YAAY,EAAE,QAAQ;gBACtB,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE,KAAK;gBAChB,gBAAgB;gBAChB,OAAO;gBACP,MAAM;aACT,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,gCAAgC,EAAE;YACjD,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;gBAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC;gBAC5C,IAAI,QAAQ,EAAE;oBACV,IAAI;wBACA,oCAAoC;wBACpC,QAAQ,CAAC,MAAM,EAAE,CAAC;qBACrB;oBAAC,OAAO,CAAC,EAAE;wBACR,QAAQ;qBACX;oBACD,8DAA8D;oBAC9D,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;iBAC5D;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;IAC3C,CAAC;IAES,UAAU,CAAC,KAAc;QAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;YAC1B,OAAO;SACV;QAED,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC;QAC5C,IAAI,cAAc,EAAE;YAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe;iBAChC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;iBACxD,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACZ,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACnD,OAAO,KAAK,CAAC;YACjB,CAAC,CAAC,CAAC;YACP,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBACvB,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,GAAG,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrE,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBACvD,UAAU,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;YACH,yBAAyB;YACzB,cAAc,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAChC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;oBACxC,MAAM,SAAS,GAA0B;wBACrC,EAAE,EAAE,gBAAgB,EAAE;wBACtB,QAAQ,EAAE,QAAQ;wBAClB,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,EAAE;qBAClC,CAAC;oBACF,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;oBACzE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAClC,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;oBACrD,yDAAyD;oBACzD,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,YAAY,KAAK,QAAQ,CAAC,CAAC;oBACrG,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBAC1B,IAAI,MAAM,EAAE;wBACR,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;wBACvB,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;qBAC1B;iBACJ;qBAAM;oBACH,MAAM,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;oBACrD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;oBAC3C,IAAI;wBACA,6BAA6B;wBAC7B,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;wBACvD,IAAI,MAAM,CAAC,YAAY,EAAE;4BACrB,MAAM,CAAC,YAAY,CAAC,kBAAkB,GAAG,MAAM,CAAC,YAAY,CAAC,kBAAkB,IAAI,IAAI,UAAU,EAAE,CAAC;4BACpG,MAAM,CAAC,oBAAoB,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;yBAC5I;wBACD,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;qBAC1D;oBAAC,OAAO,CAAC,EAAE;wBACR,KAAK,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;qBAC7C;iBACJ;YACL,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC;SAC5C;QAED,yBAAyB;QACzB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YACzC,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE;gBACnD,IAAI,CAAC,6BAA6B,CAAC,YAAY,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC,IAAI,CACzE,CAAC,YAAY,EAAE,EAAE;oBACb,YAAY,CAAC,YAAY,GAAG,YAAY,CAAC;gBAC7C,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;oBACN,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC;oBAC7B,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC/B,CAAC,CACJ,CAAC;gBACF,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC;aACjC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACK,uBAAuB,CAAC,QAAkB;QAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAClD,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBAC/C,OAAO,CAAC,CAAC;aACZ;SACJ;QACD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAEO,wBAAwB,CAAC,QAAkB,EAAE,MAA6B,EAAE,OAAgB;QAChG,SAAS;QACT,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAC1F,IAAI,IAAI,EAAE;YACN,MAAM,GAAG,GAAG,MAAM,CAAC,oBAAoB,IAAI,IAAI,MAAM,EAAE,CAAC;YACxD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;YACrD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBACpD,GAAG,CAAC,4BAA4B,EAAE,CAAC;aACtC;YACD,MAAM,CAAC,oBAAoB,GAAG,GAAG,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE;gBAChC,mFAAmF;aACtF;iBAAM;gBACH,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,CAAC;aAC1E;SACJ;QAED,OAAqB,MAAM,CAAC;IAChC,CAAC;IAEO,KAAK,CAAC,6BAA6B,CAAC,gBAAkC,EAAE,OAAgB;QAC5F,IAAI,OAAO,CAAC,YAAY,EAAE;YACtB,IAAI;gBACA,OAAO,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,8BAA8B,IAAI,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;aAC/H;YAAC,OAAO,KAAK,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;aAC1B;SACJ;aAAM;YACH,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;IACL,CAAC;;AA1TD;;GAEG;AACoB,sBAAI,GAAG,gBAAgB,CAAC,aAAa,AAAjC,CAAkC;AAC7D;;;;GAIG;AACoB,yBAAO,GAAG,CAAC,AAAJ,CAAK;AAoTvC,sBAAsB;AACtB,oBAAoB,CAAC,eAAe,CAChC,iBAAiB,CAAC,IAAI,EACtB,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,iBAAiB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AAClE,CAAC,EACD,iBAAiB,CAAC,OAAO,CAC5B,CAAC", "sourcesContent": ["import { WebXRFeatureName, WebXRFeaturesManager } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { Matrix, Vector3, Quaternion } from \"../../Maths/math.vector\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport type { IWebXRHitResult } from \"./WebXRHitTest\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n\r\n/**\r\n * Configuration options of the anchor system\r\n */\r\nexport interface IWebXRAnchorSystemOptions {\r\n    /**\r\n     * a node that will be used to convert local to world coordinates\r\n     */\r\n    worldParentNode?: TransformNode;\r\n\r\n    /**\r\n     * If set to true a reference of the created anchors will be kept until the next session starts\r\n     * If not defined, anchors will be removed from the array when the feature is detached or the session ended.\r\n     */\r\n    doNotRemoveAnchorsOnSessionEnded?: boolean;\r\n}\r\n\r\n/**\r\n * A babylon container for an XR Anchor\r\n */\r\nexport interface IWebXRAnchor {\r\n    /**\r\n     * A babylon-assigned ID for this anchor\r\n     */\r\n    id: number;\r\n    /**\r\n     * Transformation matrix to apply to an object attached to this anchor\r\n     */\r\n    transformationMatrix: Matrix;\r\n    /**\r\n     * The native anchor object\r\n     */\r\n    xrAnchor: XRAnchor;\r\n\r\n    /**\r\n     * if defined, this object will be constantly updated by the anchor's position and rotation\r\n     */\r\n    attachedNode?: TransformNode;\r\n\r\n    /**\r\n     * Remove this anchor from the scene\r\n     */\r\n    remove(): void;\r\n}\r\n\r\n/**\r\n * An internal interface for a future (promise based) anchor\r\n */\r\ninterface IWebXRFutureAnchor {\r\n    /**\r\n     * The native anchor\r\n     */\r\n    nativeAnchor?: XRAnchor;\r\n    /**\r\n     * Was this request submitted to the xr frame?\r\n     */\r\n    submitted: boolean;\r\n    /**\r\n     * Was this promise resolved already?\r\n     */\r\n    resolved: boolean;\r\n    /**\r\n     * A resolve function\r\n     */\r\n    resolve: (xrAnchor: IWebXRAnchor) => void;\r\n    /**\r\n     * A reject function\r\n     */\r\n    reject: (msg?: string) => void;\r\n    /**\r\n     * The XR Transformation of the future anchor\r\n     */\r\n    xrTransformation: XRRigidTransform;\r\n}\r\n\r\nlet anchorIdProvider = 0;\r\n\r\n/**\r\n * An implementation of the anchor system for WebXR.\r\n * For further information see https://github.com/immersive-web/anchors/\r\n */\r\nexport class WebXRAnchorSystem extends WebXRAbstractFeature {\r\n    private _lastFrameDetected: XRAnchorSet = new Set();\r\n\r\n    private _trackedAnchors: Array<IWebXRAnchor> = [];\r\n\r\n    private _referenceSpaceForFrameAnchors: XRReferenceSpace;\r\n\r\n    private _futureAnchors: IWebXRFutureAnchor[] = [];\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.ANCHOR_SYSTEM;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * Observers registered here will be executed when a new anchor was added to the session\r\n     */\r\n    public onAnchorAddedObservable: Observable<IWebXRAnchor> = new Observable();\r\n    /**\r\n     * Observers registered here will be executed when an anchor was removed from the session\r\n     */\r\n    public onAnchorRemovedObservable: Observable<IWebXRAnchor> = new Observable();\r\n    /**\r\n     * Observers registered here will be executed when an existing anchor updates\r\n     * This can execute N times every frame\r\n     */\r\n    public onAnchorUpdatedObservable: Observable<IWebXRAnchor> = new Observable();\r\n\r\n    /**\r\n     * Set the reference space to use for anchor creation, when not using a hit test.\r\n     * Will default to the session's reference space if not defined\r\n     */\r\n    public set referenceSpaceForFrameAnchors(referenceSpace: XRReferenceSpace) {\r\n        this._referenceSpaceForFrameAnchors = referenceSpace;\r\n    }\r\n\r\n    /**\r\n     * constructs a new anchor system\r\n     * @param _xrSessionManager an instance of WebXRSessionManager\r\n     * @param _options configuration object for this feature\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        private _options: IWebXRAnchorSystemOptions = {}\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"anchors\";\r\n    }\r\n\r\n    private _tmpVector = new Vector3();\r\n    private _tmpQuaternion = new Quaternion();\r\n\r\n    private _populateTmpTransformation(position: Vector3, rotationQuaternion: Quaternion) {\r\n        this._tmpVector.copyFrom(position);\r\n        this._tmpQuaternion.copyFrom(rotationQuaternion);\r\n        if (!this._xrSessionManager.scene.useRightHandedSystem) {\r\n            this._tmpVector.z *= -1;\r\n            this._tmpQuaternion.z *= -1;\r\n            this._tmpQuaternion.w *= -1;\r\n        }\r\n        return {\r\n            position: this._tmpVector,\r\n            rotationQuaternion: this._tmpQuaternion,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Create a new anchor point using a hit test result at a specific point in the scene\r\n     * An anchor is tracked only after it is added to the trackerAnchors in xrFrame. The promise returned here does not yet guaranty that.\r\n     * Use onAnchorAddedObservable to get newly added anchors if you require tracking guaranty.\r\n     *\r\n     * @param hitTestResult The hit test result to use for this anchor creation\r\n     * @param position an optional position offset for this anchor\r\n     * @param rotationQuaternion an optional rotation offset for this anchor\r\n     * @returns A promise that fulfills when babylon has created the corresponding WebXRAnchor object and tracking has begun\r\n     */\r\n    public async addAnchorPointUsingHitTestResultAsync(\r\n        hitTestResult: IWebXRHitResult,\r\n        position: Vector3 = new Vector3(),\r\n        rotationQuaternion: Quaternion = new Quaternion()\r\n    ): Promise<IWebXRAnchor> {\r\n        // convert to XR space (right handed) if needed\r\n        this._populateTmpTransformation(position, rotationQuaternion);\r\n        // the matrix that we'll use\r\n        const m = new XRRigidTransform(\r\n            { x: this._tmpVector.x, y: this._tmpVector.y, z: this._tmpVector.z },\r\n            { x: this._tmpQuaternion.x, y: this._tmpQuaternion.y, z: this._tmpQuaternion.z, w: this._tmpQuaternion.w }\r\n        );\r\n        if (!hitTestResult.xrHitResult.createAnchor) {\r\n            this.detach();\r\n            throw new Error(\"Anchors not enabled in this environment/browser\");\r\n        } else {\r\n            try {\r\n                const nativeAnchor = await hitTestResult.xrHitResult.createAnchor(m);\r\n                return new Promise<IWebXRAnchor>((resolve, reject) => {\r\n                    this._futureAnchors.push({\r\n                        nativeAnchor,\r\n                        resolved: false,\r\n                        submitted: true,\r\n                        xrTransformation: m,\r\n                        resolve,\r\n                        reject,\r\n                    });\r\n                });\r\n            } catch (error) {\r\n                throw new Error(error);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Add a new anchor at a specific position and rotation\r\n     * This function will add a new anchor per default in the next available frame. Unless forced, the createAnchor function\r\n     * will be called in the next xrFrame loop to make sure that the anchor can be created correctly.\r\n     * An anchor is tracked only after it is added to the trackerAnchors in xrFrame. The promise returned here does not yet guaranty that.\r\n     * Use onAnchorAddedObservable to get newly added anchors if you require tracking guaranty.\r\n     *\r\n     * @param position the position in which to add an anchor\r\n     * @param rotationQuaternion an optional rotation for the anchor transformation\r\n     * @param forceCreateInCurrentFrame force the creation of this anchor in the current frame. Must be called inside xrFrame loop!\r\n     * @returns A promise that fulfills when babylon has created the corresponding WebXRAnchor object and tracking has begun\r\n     */\r\n    public async addAnchorAtPositionAndRotationAsync(\r\n        position: Vector3,\r\n        rotationQuaternion: Quaternion = new Quaternion(),\r\n        forceCreateInCurrentFrame = false\r\n    ): Promise<IWebXRAnchor> {\r\n        // convert to XR space (right handed) if needed\r\n        this._populateTmpTransformation(position, rotationQuaternion);\r\n        // the matrix that we'll use\r\n        const xrTransformation = new XRRigidTransform(\r\n            { x: this._tmpVector.x, y: this._tmpVector.y, z: this._tmpVector.z },\r\n            { x: this._tmpQuaternion.x, y: this._tmpQuaternion.y, z: this._tmpQuaternion.z, w: this._tmpQuaternion.w }\r\n        );\r\n        const xrAnchor =\r\n            forceCreateInCurrentFrame && this.attached && this._xrSessionManager.currentFrame\r\n                ? await this._createAnchorAtTransformation(xrTransformation, this._xrSessionManager.currentFrame)\r\n                : undefined;\r\n        // add the transformation to the future anchors list\r\n        return new Promise<IWebXRAnchor>((resolve, reject) => {\r\n            this._futureAnchors.push({\r\n                nativeAnchor: xrAnchor,\r\n                resolved: false,\r\n                submitted: false,\r\n                xrTransformation,\r\n                resolve,\r\n                reject,\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Get the list of anchors currently being tracked by the system\r\n     */\r\n    public get anchors(): IWebXRAnchor[] {\r\n        return this._trackedAnchors;\r\n    }\r\n\r\n    /**\r\n     * detach this feature.\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n\r\n        if (!this._options.doNotRemoveAnchorsOnSessionEnded) {\r\n            while (this._trackedAnchors.length) {\r\n                const toRemove = this._trackedAnchors.pop();\r\n                if (toRemove) {\r\n                    try {\r\n                        // try to natively remove it as well\r\n                        toRemove.remove();\r\n                    } catch (e) {\r\n                        // no-op\r\n                    }\r\n                    // as the xr frame loop is removed, we need to notify manually\r\n                    this.onAnchorRemovedObservable.notifyObservers(toRemove);\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached\r\n     */\r\n    public dispose(): void {\r\n        this._futureAnchors.length = 0;\r\n        super.dispose();\r\n        this.onAnchorAddedObservable.clear();\r\n        this.onAnchorRemovedObservable.clear();\r\n        this.onAnchorUpdatedObservable.clear();\r\n    }\r\n\r\n    protected _onXRFrame(frame: XRFrame) {\r\n        if (!this.attached || !frame) {\r\n            return;\r\n        }\r\n\r\n        const trackedAnchors = frame.trackedAnchors;\r\n        if (trackedAnchors) {\r\n            const toRemove = this._trackedAnchors\r\n                .filter((anchor) => !trackedAnchors.has(anchor.xrAnchor))\r\n                .map((anchor) => {\r\n                    const index = this._trackedAnchors.indexOf(anchor);\r\n                    return index;\r\n                });\r\n            let idxTracker = 0;\r\n            toRemove.forEach((index) => {\r\n                const anchor = this._trackedAnchors.splice(index - idxTracker, 1)[0];\r\n                this.onAnchorRemovedObservable.notifyObservers(anchor);\r\n                idxTracker++;\r\n            });\r\n            // now check for new ones\r\n            trackedAnchors.forEach((xrAnchor) => {\r\n                if (!this._lastFrameDetected.has(xrAnchor)) {\r\n                    const newAnchor: Partial<IWebXRAnchor> = {\r\n                        id: anchorIdProvider++,\r\n                        xrAnchor: xrAnchor,\r\n                        remove: () => xrAnchor.delete(),\r\n                    };\r\n                    const anchor = this._updateAnchorWithXRFrame(xrAnchor, newAnchor, frame);\r\n                    this._trackedAnchors.push(anchor);\r\n                    this.onAnchorAddedObservable.notifyObservers(anchor);\r\n                    // search for the future anchor promise that matches this\r\n                    const results = this._futureAnchors.filter((futureAnchor) => futureAnchor.nativeAnchor === xrAnchor);\r\n                    const result = results[0];\r\n                    if (result) {\r\n                        result.resolve(anchor);\r\n                        result.resolved = true;\r\n                    }\r\n                } else {\r\n                    const index = this._findIndexInAnchorArray(xrAnchor);\r\n                    const anchor = this._trackedAnchors[index];\r\n                    try {\r\n                        // anchors update every frame\r\n                        this._updateAnchorWithXRFrame(xrAnchor, anchor, frame);\r\n                        if (anchor.attachedNode) {\r\n                            anchor.attachedNode.rotationQuaternion = anchor.attachedNode.rotationQuaternion || new Quaternion();\r\n                            anchor.transformationMatrix.decompose(anchor.attachedNode.scaling, anchor.attachedNode.rotationQuaternion, anchor.attachedNode.position);\r\n                        }\r\n                        this.onAnchorUpdatedObservable.notifyObservers(anchor);\r\n                    } catch (e) {\r\n                        Tools.Warn(`Anchor could not be updated`);\r\n                    }\r\n                }\r\n            });\r\n            this._lastFrameDetected = trackedAnchors;\r\n        }\r\n\r\n        // process future anchors\r\n        this._futureAnchors.forEach((futureAnchor) => {\r\n            if (!futureAnchor.resolved && !futureAnchor.submitted) {\r\n                this._createAnchorAtTransformation(futureAnchor.xrTransformation, frame).then(\r\n                    (nativeAnchor) => {\r\n                        futureAnchor.nativeAnchor = nativeAnchor;\r\n                    },\r\n                    (error) => {\r\n                        futureAnchor.resolved = true;\r\n                        futureAnchor.reject(error);\r\n                    }\r\n                );\r\n                futureAnchor.submitted = true;\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * avoiding using Array.find for global support.\r\n     * @param xrAnchor the plane to find in the array\r\n     * @returns the index of the anchor in the array or -1 if not found\r\n     */\r\n    private _findIndexInAnchorArray(xrAnchor: XRAnchor) {\r\n        for (let i = 0; i < this._trackedAnchors.length; ++i) {\r\n            if (this._trackedAnchors[i].xrAnchor === xrAnchor) {\r\n                return i;\r\n            }\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    private _updateAnchorWithXRFrame(xrAnchor: XRAnchor, anchor: Partial<IWebXRAnchor>, xrFrame: XRFrame): IWebXRAnchor {\r\n        // matrix\r\n        const pose = xrFrame.getPose(xrAnchor.anchorSpace, this._xrSessionManager.referenceSpace);\r\n        if (pose) {\r\n            const mat = anchor.transformationMatrix || new Matrix();\r\n            Matrix.FromArrayToRef(pose.transform.matrix, 0, mat);\r\n            if (!this._xrSessionManager.scene.useRightHandedSystem) {\r\n                mat.toggleModelMatrixHandInPlace();\r\n            }\r\n            anchor.transformationMatrix = mat;\r\n            if (!this._options.worldParentNode) {\r\n                // Logger.Warn(\"Please provide a world parent node to apply world transformation\");\r\n            } else {\r\n                mat.multiplyToRef(this._options.worldParentNode.getWorldMatrix(), mat);\r\n            }\r\n        }\r\n\r\n        return <IWebXRAnchor>anchor;\r\n    }\r\n\r\n    private async _createAnchorAtTransformation(xrTransformation: XRRigidTransform, xrFrame: XRFrame) {\r\n        if (xrFrame.createAnchor) {\r\n            try {\r\n                return xrFrame.createAnchor(xrTransformation, this._referenceSpaceForFrameAnchors ?? this._xrSessionManager.referenceSpace);\r\n            } catch (error) {\r\n                throw new Error(error);\r\n            }\r\n        } else {\r\n            this.detach();\r\n            throw new Error(\"Anchors are not enabled in your browser\");\r\n        }\r\n    }\r\n}\r\n\r\n// register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRAnchorSystem.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRAnchorSystem(xrSessionManager, options);\r\n    },\r\n    WebXRAnchorSystem.Version\r\n);\r\n"]}