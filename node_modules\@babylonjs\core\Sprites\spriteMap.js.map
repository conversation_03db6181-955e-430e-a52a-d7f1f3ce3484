{"version": 3, "file": "spriteMap.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Sprites/spriteMap.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACxD,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,UAAU,EAAE,MAAM,kCAAkC,CAAC;AAC9D,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAI7D,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,+BAA+B,CAAC;AACvC,OAAO,6BAA6B,CAAC;AA6ErC;;GAEG;AACH,MAAM,OAAO,SAAS;IAgBlB,kDAAkD;IAClD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED,0CAA0C;IAC1C,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED,0CAA0C;IAC1C,IAAW,QAAQ,CAAC,CAAU;QAC1B,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;IAC9B,CAAC;IAED,0CAA0C;IAC1C,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED,0CAA0C;IAC1C,IAAW,QAAQ,CAAC,CAAU;QAC1B,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;IAC9B,CAAC;IAED,2BAA2B;IAC3B,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,2BAA2B;IAC3B,IAAW,YAAY,CAAC,CAAa;QACjC,MAAM,MAAM,GAAG,CAAE,CAAC,QAAS,CAAC,WAAW,CAAC;QACxC,MAAM,EAAE,GAAG,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAClE,CAAC;IAuBD;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,SAA2B,EAAE,WAAoB,EAAE,OAA0B,EAAE,KAAY;QACjH,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAE/B;;WAEG;QACH,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3D,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,SAAS,CAAC;QAC7D,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QAClE,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QAClE,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC;QAC7C,OAAO,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,CAAC,CAAC;QAC7D,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;QACzC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC;QACvC,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEtE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE3C,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;SACxD;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QAE3D,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,OAAO,CAAC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;QAErD,IAAI,OAAO,CAAC,KAAK,EAAE;YACf,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACjC;QAED,OAAO,CAAC,IAAI,CAAC,gCAAgC,OAAO,CAAC,kBAAkB,IAAI,CAAC,CAAC;QAE7E,MAAM,YAAY,GAAW,MAAM,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;QAEzE,IAAI,iBAAyB,CAAC;QAC9B,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,yBAAyB,EAAE;YACxD,iBAAiB,GAAG,EAAE,CAAC;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACzC,iBAAiB,IAAI,OAAO,CAAC,yCAAyC,CAAC,yCAAyC,CAAC;aACpH;SACJ;aAAM;YACH,iBAAiB,GAAG,aAAa,CAAC;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACzC,iBAAiB,IAAI,OAAO,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,GAAG,uCAAuC,CAAC;gBAClH,iBAAiB,IAAI,QAAQ,CAAC;aACjC;YACD,iBAAiB,IAAI,GAAG,CAAC;SAC5B;QAED,MAAM,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,yBAAyB,EAAE,iBAAiB,CAAC,CAAC;QAElI,IAAI,CAAC,SAAS,GAAG,IAAI,cAAc,CAC/B,YAAY,GAAG,IAAI,CAAC,IAAI,EACxB,IAAI,CAAC,MAAM,EACX;YACI,MAAM,EAAE,WAAW;YACnB,QAAQ,EAAE,WAAW,GAAG,IAAI,CAAC,IAAI;SACpC,EACD;YACI,OAAO;YACP,UAAU,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC;YACxC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,OAAO,CAAC;YAC7J,QAAQ,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC;YACjE,iBAAiB,EAAE,IAAI;SAC1B,CACJ,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QAEf,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACzD,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;QAC1D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,YAAY,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QAC5D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;QAE7D,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,MAAM,iBAAiB,GAAG,GAAG,EAAE;YAC3B,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE;gBAChD,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;oBAC3B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC;oBAC7I,OAAO;iBACV;aACJ;YACD,IAAI,QAAQ,GAAG,GAAG,EAAE;gBAChB,UAAU,CAAC,GAAG,EAAE;oBACZ,QAAQ,EAAE,CAAC;oBACX,iBAAiB,EAAE,CAAC;gBACxB,CAAC,EAAE,GAAG,CAAC,CAAC;aACX;QACL,CAAC,CAAC;QAEF,iBAAiB,EAAE,CAAC;QAEpB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;QAC7D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACtD,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAE5C,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,IAAI,GAAG,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;QAClF,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QAEvC,MAAM,UAAU,GAAG,GAAG,EAAE;YACpB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,CAAC;YACrD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACrD,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAClC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;QACzB,MAAM,QAAQ,GAA0B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,EAAE;YAC1G,IAAI,IAAI,KAAK,GAAG,EAAE;gBACd,OAAO,KAAK,CAAC;aAChB;YACD,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE;YAC/D,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAC9B;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,EAAE,CAAC;QAChD,IAAI,MAAM,EAAE;YACR,OAAO,MAAM,CAAC;SACjB;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IAED;;;;;;;;;;;OAWG;IACK,kBAAkB;QACtB,MAAM,IAAI,GAAG,EAAE,CAAC;QAChB,eAAe;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO;YAC9B,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,kBAAkB;YACzC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,8BAA8B;YACrD,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,0IAA0I;SACpK;QACD,aAAa;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;YACvC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YACnC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC;YAChD,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE7C,OAAO;YACP,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAClB,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtB,kBAAkB;YAClB,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAC/C,8BAA8B;YAC9B,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAC1C,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3C,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;SAC9C;QAED,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QAE1C,MAAM,CAAC,GAAG,UAAU,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,iBAAiB,CAAC,CAAC;QAEtJ,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;;OAKG;IACK,iBAAiB,CAAC,MAAW,EAAE,SAAiB,CAAC;QACrD,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAU,CAAC,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAU,CAAC,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,MAAM,EAAE;YACT,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC/B,IAAI,MAAM,IAAI,CAAC,EAAE;gBACb,EAAE,GAAG,CAAC,CAAC;aACV;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;gBAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;oBACjC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC1B;aACJ;SACJ;aAAM;YACH,IAAI,GAAG,MAAM,CAAC;SACjB;QAED,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,UAAU,CAAC,iBAAiB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,iBAAiB,CAAC,CAAC;QAE3I,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,SAAiB,CAAC,EAAE,GAAwB,EAAE,OAAe,CAAC;QAC7E,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAE,CAAC,QAAS,CAAC,WAAW,CAAC;QAC7D,IAAI,MAAM,KAAK,IAAI,EAAE;YACjB,OAAO;SACV;QAED,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,IAAI,GAAG,YAAY,OAAO,EAAE;YACxB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACf;aAAM;YACH,CAAC,GAAG,GAAG,CAAC;SACX;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAU,CAAC,CAAC,IAAI,CAAC,CAAC;QAE3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/B,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxB,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,EAAE,GAAW,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAC9C,MAAc,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;SAC9B;QAED,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9D,CAAC;IAED;;;;OAIG;IACK,0BAA0B,CAAC,MAAiC;QAChE,MAAM,IAAI,GAAG,EAAE,CAAC;QAChB,IAAI,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,EAAE;YACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;gBACvC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtB,IAAI,KAAK,GAAG,CAAC,CAAC;gBACd,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,CAAC,CAAC,EAAE;oBACnD,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACtB,KAAK,EAAE,CAAC;iBACX;aACJ;YACD,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;SACvC;aAAM;YACH,UAAU,GAAG,MAAM,CAAC;SACvB;QAED,MAAM,CAAC,GAAG,UAAU,CAAC,iBAAiB,CAClC,UAAU,EACV,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,CAAC,EACpC,IAAI,CAAC,MAAM,EACX,KAAK,EACL,KAAK,EACL,OAAO,CAAC,eAAe,EACvB,MAAM,CAAC,iBAAiB,CAC3B,CAAC;QAEF,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;;;;OAOG;IACI,kBAAkB,CAAC,SAAiB,CAAC,EAAE,SAAiB,CAAC,EAAE,SAAiB,CAAC,EAAE,OAAe,CAAC,EAAE,QAAgB,CAAC;QACrH,MAAM,MAAM,GAAQ,IAAI,CAAC,aAAc,CAAC,QAAS,CAAC,WAAW,CAAC;QAC9D,MAAM,EAAE,GAAW,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,MAAM,CAAC;QAC9D,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QACD,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;QACpB,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QACtB,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QACvB,MAAM,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACI,YAAY;QACf,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,CAAC,GAAG,CAAC,EAAE;gBACP,IAAI,IAAI,MAAM,CAAC;aAClB;YAED,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAE,CAAC,QAAS,CAAC,WAAY,CAAC,QAAQ,EAAE,CAAC;SAChE;QACD,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QAClD,aAAa,CAAC,IAAI,GAAG,kCAAkC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;QAC1E,aAAa,CAAC,MAAM,GAAG,QAAQ,CAAC;QAChC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACjD,aAAa,CAAC,KAAK,EAAE,CAAC;QACtB,aAAa,CAAC,MAAM,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,GAAW;QAC3B,MAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;QACjC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAErB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAQ,CAAC,UAAU,IAAI,CAAC,CAAC;QAE1C,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;YACd,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;gBAC1B,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACzC,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBACpC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aACzB;YACD,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9D,CAAC,CAAC;QACF,GAAG,CAAC,IAAI,EAAE,CAAC;IACf,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACzB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YAC1B,EAAE,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;CACJ", "sourcesContent": ["import { Engine } from \"../Engines/engine\";\r\nimport type { IDisposable, Scene } from \"../scene\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Vector2, Vector3 } from \"../Maths/math.vector\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { RawTexture } from \"../Materials/Textures/rawTexture\";\r\nimport { ShaderMaterial } from \"../Materials/shaderMaterial\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { PickingInfo } from \"../Collisions/pickingInfo\";\r\nimport type { ISpriteJSONSprite, ISpriteJSONAtlas } from \"./ISprites\";\r\nimport { Effect } from \"../Materials/effect\";\r\n\r\nimport { CreatePlane } from \"../Meshes/Builders/planeBuilder\";\r\nimport \"../Shaders/spriteMap.fragment\";\r\nimport \"../Shaders/spriteMap.vertex\";\r\n\r\n/**\r\n * Defines the basic options interface of a SpriteMap\r\n */\r\nexport interface ISpriteMapOptions {\r\n    /**\r\n     * Vector2 of the number of cells in the grid.\r\n     */\r\n    stageSize?: Vector2;\r\n\r\n    /**\r\n     * Vector2 of the size of the output plane in World Units.\r\n     */\r\n    outputSize?: Vector2;\r\n\r\n    /**\r\n     * Vector3 of the position of the output plane in World Units.\r\n     */\r\n    outputPosition?: Vector3;\r\n\r\n    /**\r\n     * Vector3 of the rotation of the output plane.\r\n     */\r\n    outputRotation?: Vector3;\r\n\r\n    /**\r\n     * number of layers that the system will reserve in resources.\r\n     */\r\n    layerCount?: number;\r\n\r\n    /**\r\n     * number of max animation frames a single cell will reserve in resources.\r\n     */\r\n    maxAnimationFrames?: number;\r\n\r\n    /**\r\n     * number cell index of the base tile when the system compiles.\r\n     */\r\n    baseTile?: number;\r\n\r\n    /**\r\n     * boolean flip the sprite after its been repositioned by the framing data.\r\n     */\r\n    flipU?: boolean;\r\n\r\n    /**\r\n     * Vector3 scalar of the global RGB values of the SpriteMap.\r\n     */\r\n    colorMultiply?: Vector3;\r\n}\r\n\r\n/**\r\n * Defines the IDisposable interface in order to be cleanable from resources.\r\n */\r\nexport interface ISpriteMap extends IDisposable {\r\n    /**\r\n     * String name of the SpriteMap.\r\n     */\r\n    name: string;\r\n\r\n    /**\r\n     * The JSON Array file from a https://www.codeandweb.com/texturepacker export.  Or similar structure.\r\n     */\r\n    atlasJSON: ISpriteJSONAtlas;\r\n\r\n    /**\r\n     * Texture of the SpriteMap.\r\n     */\r\n    spriteSheet: Texture;\r\n\r\n    /**\r\n     * The parameters to initialize the SpriteMap with.\r\n     */\r\n    options: ISpriteMapOptions;\r\n}\r\n\r\n/**\r\n * Class used to manage a grid restricted sprite deployment on an Output plane.\r\n */\r\nexport class SpriteMap implements ISpriteMap {\r\n    /** The Name of the spriteMap */\r\n    public name: string;\r\n\r\n    /** The JSON file with the frame and meta data */\r\n    public atlasJSON: ISpriteJSONAtlas;\r\n\r\n    /** The systems Sprite Sheet Texture */\r\n    public spriteSheet: Texture;\r\n\r\n    /** Arguments passed with the Constructor */\r\n    public options: ISpriteMapOptions;\r\n\r\n    /** Public Sprite Storage array, parsed from atlasJSON */\r\n    public sprites: Array<ISpriteJSONSprite>;\r\n\r\n    /** Returns the Number of Sprites in the System */\r\n    public get spriteCount(): number {\r\n        return this.sprites.length;\r\n    }\r\n\r\n    /** Returns the Position of Output Plane*/\r\n    public get position(): Vector3 {\r\n        return this._output.position;\r\n    }\r\n\r\n    /** Returns the Position of Output Plane*/\r\n    public set position(v: Vector3) {\r\n        this._output.position = v;\r\n    }\r\n\r\n    /** Returns the Rotation of Output Plane*/\r\n    public get rotation(): Vector3 {\r\n        return this._output.rotation;\r\n    }\r\n\r\n    /** Returns the Rotation of Output Plane*/\r\n    public set rotation(v: Vector3) {\r\n        this._output.rotation = v;\r\n    }\r\n\r\n    /** Sets the AnimationMap*/\r\n    public get animationMap() {\r\n        return this._animationMap;\r\n    }\r\n\r\n    /** Sets the AnimationMap*/\r\n    public set animationMap(v: RawTexture) {\r\n        const buffer = v!._texture!._bufferView;\r\n        const am = this._createTileAnimationBuffer(buffer);\r\n        this._animationMap.dispose();\r\n        this._animationMap = am;\r\n        this._material.setTexture(\"animationMap\", this._animationMap);\r\n    }\r\n\r\n    /** Scene that the SpriteMap was created in */\r\n    private _scene: Scene;\r\n\r\n    /** Texture Buffer of Float32 that holds tile frame data*/\r\n    private _frameMap: RawTexture;\r\n\r\n    /** Texture Buffers of Float32 that holds tileMap data*/\r\n    private _tileMaps: RawTexture[];\r\n\r\n    /** Texture Buffer of Float32 that holds Animation Data*/\r\n    private _animationMap: RawTexture;\r\n\r\n    /** Custom ShaderMaterial Central to the System*/\r\n    private _material: ShaderMaterial;\r\n\r\n    /** Custom ShaderMaterial Central to the System*/\r\n    private _output: Mesh;\r\n\r\n    /** Systems Time Ticker*/\r\n    private _time: number;\r\n\r\n    /**\r\n     * Creates a new SpriteMap\r\n     * @param name defines the SpriteMaps Name\r\n     * @param atlasJSON is the JSON file that controls the Sprites Frames and Meta\r\n     * @param spriteSheet is the Texture that the Sprites are on.\r\n     * @param options a basic deployment configuration\r\n     * @param scene The Scene that the map is deployed on\r\n     */\r\n    constructor(name: string, atlasJSON: ISpriteJSONAtlas, spriteSheet: Texture, options: ISpriteMapOptions, scene: Scene) {\r\n        this.name = name;\r\n        this.sprites = [];\r\n        this.atlasJSON = atlasJSON;\r\n        this.sprites = this.atlasJSON[\"frames\"];\r\n        this.spriteSheet = spriteSheet;\r\n\r\n        /**\r\n         * Run through the options and set what ever defaults are needed that where not declared.\r\n         */\r\n        this.options = options;\r\n        options.stageSize = options.stageSize || new Vector2(1, 1);\r\n        options.outputSize = options.outputSize || options.stageSize;\r\n        options.outputPosition = options.outputPosition || Vector3.Zero();\r\n        options.outputRotation = options.outputRotation || Vector3.Zero();\r\n        options.layerCount = options.layerCount || 1;\r\n        options.maxAnimationFrames = options.maxAnimationFrames || 0;\r\n        options.baseTile = options.baseTile || 0;\r\n        options.flipU = options.flipU || false;\r\n        options.colorMultiply = options.colorMultiply || new Vector3(1, 1, 1);\r\n\r\n        this._scene = scene;\r\n\r\n        this._frameMap = this._createFrameBuffer();\r\n\r\n        this._tileMaps = new Array();\r\n        for (let i = 0; i < options.layerCount; i++) {\r\n            this._tileMaps.push(this._createTileBuffer(null, i));\r\n        }\r\n\r\n        this._animationMap = this._createTileAnimationBuffer(null);\r\n\r\n        const defines = [];\r\n        defines.push(\"#define LAYERS \" + options.layerCount);\r\n\r\n        if (options.flipU) {\r\n            defines.push(\"#define FLIPU\");\r\n        }\r\n\r\n        defines.push(`#define MAX_ANIMATION_FRAMES ${options.maxAnimationFrames}.0`);\r\n\r\n        const shaderString: string = Effect.ShadersStore[\"spriteMapPixelShader\"];\r\n\r\n        let layerSampleString: string;\r\n        if (!scene.getEngine()._features.supportSwitchCaseInShader) {\r\n            layerSampleString = \"\";\r\n            for (let i = 0; i < options.layerCount; i++) {\r\n                layerSampleString += `if (${i} == i) { frameID = texture2D(tileMaps[${i}], (tileID + 0.5) / stageSize, 0.).x; }`;\r\n            }\r\n        } else {\r\n            layerSampleString = \"switch(i) {\";\r\n            for (let i = 0; i < options.layerCount; i++) {\r\n                layerSampleString += \"case \" + i + \" : frameID = texture(tileMaps[\" + i + \"], (tileID + 0.5) / stageSize, 0.).x;\";\r\n                layerSampleString += \"break;\";\r\n            }\r\n            layerSampleString += \"}\";\r\n        }\r\n\r\n        Effect.ShadersStore[\"spriteMap\" + this.name + \"PixelShader\"] = shaderString.replace(\"#define LAYER_ID_SWITCH\", layerSampleString);\r\n\r\n        this._material = new ShaderMaterial(\r\n            \"spriteMap:\" + this.name,\r\n            this._scene,\r\n            {\r\n                vertex: \"spriteMap\",\r\n                fragment: \"spriteMap\" + this.name,\r\n            },\r\n            {\r\n                defines,\r\n                attributes: [\"position\", \"normal\", \"uv\"],\r\n                uniforms: [\"worldViewProjection\", \"time\", \"stageSize\", \"outputSize\", \"spriteMapSize\", \"spriteCount\", \"time\", \"colorMul\", \"mousePosition\", \"curTile\", \"flipU\"],\r\n                samplers: [\"spriteSheet\", \"frameMap\", \"tileMaps\", \"animationMap\"],\r\n                needAlphaBlending: true,\r\n            }\r\n        );\r\n\r\n        this._time = 0;\r\n\r\n        this._material.setFloat(\"spriteCount\", this.spriteCount);\r\n        this._material.setVector2(\"stageSize\", options.stageSize);\r\n        this._material.setVector2(\"outputSize\", options.outputSize);\r\n        this._material.setTexture(\"spriteSheet\", this.spriteSheet);\r\n        this._material.setVector2(\"spriteMapSize\", new Vector2(1, 1));\r\n        this._material.setVector3(\"colorMul\", options.colorMultiply);\r\n\r\n        let tickSave = 0;\r\n\r\n        const bindSpriteTexture = () => {\r\n            if (this.spriteSheet && this.spriteSheet.isReady()) {\r\n                if (this.spriteSheet._texture) {\r\n                    this._material.setVector2(\"spriteMapSize\", new Vector2(this.spriteSheet._texture.baseWidth || 1, this.spriteSheet._texture.baseHeight || 1));\r\n                    return;\r\n                }\r\n            }\r\n            if (tickSave < 100) {\r\n                setTimeout(() => {\r\n                    tickSave++;\r\n                    bindSpriteTexture();\r\n                }, 100);\r\n            }\r\n        };\r\n\r\n        bindSpriteTexture();\r\n\r\n        this._material.setVector3(\"colorMul\", options.colorMultiply);\r\n        this._material.setTexture(\"frameMap\", this._frameMap);\r\n        this._material.setTextureArray(\"tileMaps\", this._tileMaps);\r\n        this._material.setTexture(\"animationMap\", this._animationMap);\r\n        this._material.setFloat(\"time\", this._time);\r\n\r\n        this._output = CreatePlane(name + \":output\", { size: 1, updatable: true }, scene);\r\n        this._output.scaling.x = options.outputSize.x;\r\n        this._output.scaling.y = options.outputSize.y;\r\n        this.position = options.outputPosition;\r\n        this.rotation = options.outputRotation;\r\n\r\n        const obfunction = () => {\r\n            this._time += this._scene.getEngine().getDeltaTime();\r\n            this._material.setFloat(\"time\", this._time);\r\n        };\r\n\r\n        this._scene.onBeforeRenderObservable.add(obfunction);\r\n        this._output.material = this._material;\r\n    }\r\n\r\n    /**\r\n     * Returns tileID location\r\n     * @returns Vector2 the cell position ID\r\n     */\r\n    public getTileID(): Vector2 {\r\n        const p = this.getMousePosition();\r\n        p.multiplyInPlace(this.options.stageSize || Vector2.Zero());\r\n        p.x = Math.floor(p.x);\r\n        p.y = Math.floor(p.y);\r\n        return p;\r\n    }\r\n\r\n    /**\r\n     * Gets the UV location of the mouse over the SpriteMap.\r\n     * @returns Vector2 the UV position of the mouse interaction\r\n     */\r\n    public getMousePosition(): Vector2 {\r\n        const out = this._output;\r\n        const pickinfo: Nullable<PickingInfo> = this._scene.pick(this._scene.pointerX, this._scene.pointerY, (mesh) => {\r\n            if (mesh !== out) {\r\n                return false;\r\n            }\r\n            return true;\r\n        });\r\n\r\n        if (!pickinfo || !pickinfo.hit || !pickinfo.getTextureCoordinates) {\r\n            return new Vector2(-1, -1);\r\n        }\r\n\r\n        const coords = pickinfo.getTextureCoordinates();\r\n        if (coords) {\r\n            return coords;\r\n        }\r\n\r\n        return new Vector2(-1, -1);\r\n    }\r\n\r\n    /**\r\n     * Creates the \"frame\" texture Buffer\r\n     * -------------------------------------\r\n     * Structure of frames\r\n     *  \"filename\": \"Falling-Water-2.png\",\r\n     * \"frame\": {\"x\":69,\"y\":103,\"w\":24,\"h\":32},\r\n     * \"rotated\": true,\r\n     * \"trimmed\": true,\r\n     * \"spriteSourceSize\": {\"x\":4,\"y\":0,\"w\":24,\"h\":32},\r\n     * \"sourceSize\": {\"w\":32,\"h\":32}\r\n     * @returns RawTexture of the frameMap\r\n     */\r\n    private _createFrameBuffer(): RawTexture {\r\n        const data = [];\r\n        //Do two Passes\r\n        for (let i = 0; i < this.spriteCount; i++) {\r\n            data.push(0, 0, 0, 0); //frame\r\n            data.push(0, 0, 0, 0); //spriteSourceSize\r\n            data.push(0, 0, 0, 0); //sourceSize, rotated, trimmed\r\n            data.push(0, 0, 0, 0); //Keep it pow2 cause I\"m cool like that... it helps with sampling accuracy as well. Plus then we have 4 other parameters for future stuff.\r\n        }\r\n        //Second Pass\r\n        for (let i = 0; i < this.spriteCount; i++) {\r\n            const f = this.sprites[i][\"frame\"];\r\n            const sss = this.sprites[i][\"spriteSourceSize\"];\r\n            const ss = this.sprites[i][\"sourceSize\"];\r\n            const r = this.sprites[i][\"rotated\"] ? 1 : 0;\r\n            const t = this.sprites[i][\"trimmed\"] ? 1 : 0;\r\n\r\n            //frame\r\n            data[i * 4] = f.x;\r\n            data[i * 4 + 1] = f.y;\r\n            data[i * 4 + 2] = f.w;\r\n            data[i * 4 + 3] = f.h;\r\n            //spriteSourceSize\r\n            data[i * 4 + this.spriteCount * 4] = sss.x;\r\n            data[i * 4 + 1 + this.spriteCount * 4] = sss.y;\r\n            data[i * 4 + 3 + this.spriteCount * 4] = sss.h;\r\n            //sourceSize, rotated, trimmed\r\n            data[i * 4 + this.spriteCount * 8] = ss.w;\r\n            data[i * 4 + 1 + this.spriteCount * 8] = ss.h;\r\n            data[i * 4 + 2 + this.spriteCount * 8] = r;\r\n            data[i * 4 + 3 + this.spriteCount * 8] = t;\r\n        }\r\n\r\n        const floatArray = new Float32Array(data);\r\n\r\n        const t = RawTexture.CreateRGBATexture(floatArray, this.spriteCount, 4, this._scene, false, false, Texture.NEAREST_NEAREST, Engine.TEXTURETYPE_FLOAT);\r\n\r\n        return t;\r\n    }\r\n\r\n    /**\r\n     * Creates the tileMap texture Buffer\r\n     * @param buffer normally and array of numbers, or a false to generate from scratch\r\n     * @param _layer indicates what layer for a logic trigger dealing with the baseTile.  The system uses this\r\n     * @returns RawTexture of the tileMap\r\n     */\r\n    private _createTileBuffer(buffer: any, _layer: number = 0): RawTexture {\r\n        let data = [];\r\n        const _ty = this.options.stageSize!.y || 0;\r\n        const _tx = this.options.stageSize!.x || 0;\r\n\r\n        if (!buffer) {\r\n            let bt = this.options.baseTile;\r\n            if (_layer != 0) {\r\n                bt = 0;\r\n            }\r\n\r\n            for (let y = 0; y < _ty; y++) {\r\n                for (let x = 0; x < _tx * 4; x += 4) {\r\n                    data.push(bt, 0, 0, 0);\r\n                }\r\n            }\r\n        } else {\r\n            data = buffer;\r\n        }\r\n\r\n        const floatArray = new Float32Array(data);\r\n        const t = RawTexture.CreateRGBATexture(floatArray, _tx, _ty, this._scene, false, false, Texture.NEAREST_NEAREST, Engine.TEXTURETYPE_FLOAT);\r\n\r\n        return t;\r\n    }\r\n\r\n    /**\r\n     * Modifies the data of the tileMaps\r\n     * @param _layer is the ID of the layer you want to edit on the SpriteMap\r\n     * @param pos is the iVector2 Coordinates of the Tile\r\n     * @param tile The SpriteIndex of the new Tile\r\n     */\r\n    public changeTiles(_layer: number = 0, pos: Vector2 | Vector2[], tile: number = 0): void {\r\n        const buffer = this._tileMaps[_layer]!._texture!._bufferView;\r\n        if (buffer === null) {\r\n            return;\r\n        }\r\n\r\n        let p = [];\r\n        if (pos instanceof Vector2) {\r\n            p.push(pos);\r\n        } else {\r\n            p = pos;\r\n        }\r\n\r\n        const _tx = this.options.stageSize!.x || 0;\r\n\r\n        for (let i = 0; i < p.length; i++) {\r\n            const _p = p[i];\r\n            _p.x = Math.floor(_p.x);\r\n            _p.y = Math.floor(_p.y);\r\n            const id: number = _p.x * 4 + _p.y * (_tx * 4);\r\n            (buffer as any)[id] = tile;\r\n        }\r\n\r\n        const t = this._createTileBuffer(buffer);\r\n        this._tileMaps[_layer].dispose();\r\n        this._tileMaps[_layer] = t;\r\n        this._material.setTextureArray(\"tileMap\", this._tileMaps);\r\n    }\r\n\r\n    /**\r\n     * Creates the animationMap texture Buffer\r\n     * @param buffer normally and array of numbers, or a false to generate from scratch\r\n     * @returns RawTexture of the animationMap\r\n     */\r\n    private _createTileAnimationBuffer(buffer: Nullable<ArrayBufferView>): RawTexture {\r\n        const data = [];\r\n        let floatArray;\r\n        if (!buffer) {\r\n            for (let i = 0; i < this.spriteCount; i++) {\r\n                data.push(0, 0, 0, 0);\r\n                let count = 1;\r\n                while (count < (this.options.maxAnimationFrames || 4)) {\r\n                    data.push(0, 0, 0, 0);\r\n                    count++;\r\n                }\r\n            }\r\n            floatArray = new Float32Array(data);\r\n        } else {\r\n            floatArray = buffer;\r\n        }\r\n\r\n        const t = RawTexture.CreateRGBATexture(\r\n            floatArray,\r\n            this.spriteCount,\r\n            this.options.maxAnimationFrames || 4,\r\n            this._scene,\r\n            false,\r\n            false,\r\n            Texture.NEAREST_NEAREST,\r\n            Engine.TEXTURETYPE_FLOAT\r\n        );\r\n\r\n        return t;\r\n    }\r\n\r\n    /**\r\n     * Modifies the data of the animationMap\r\n     * @param cellID is the Index of the Sprite\r\n     * @param _frame is the target Animation frame\r\n     * @param toCell is the Target Index of the next frame of the animation\r\n     * @param time is a value between 0-1 that is the trigger for when the frame should change tiles\r\n     * @param speed is a global scalar of the time variable on the map.\r\n     */\r\n    public addAnimationToTile(cellID: number = 0, _frame: number = 0, toCell: number = 0, time: number = 0, speed: number = 1): void {\r\n        const buffer: any = this._animationMap!._texture!._bufferView;\r\n        const id: number = cellID * 4 + this.spriteCount * 4 * _frame;\r\n        if (!buffer) {\r\n            return;\r\n        }\r\n        buffer[id] = toCell;\r\n        buffer[id + 1] = time;\r\n        buffer[id + 2] = speed;\r\n        const t = this._createTileAnimationBuffer(buffer);\r\n        this._animationMap.dispose();\r\n        this._animationMap = t;\r\n        this._material.setTexture(\"animationMap\", this._animationMap);\r\n    }\r\n\r\n    /**\r\n     * Exports the .tilemaps file\r\n     */\r\n    public saveTileMaps(): void {\r\n        let maps = \"\";\r\n        for (let i = 0; i < this._tileMaps.length; i++) {\r\n            if (i > 0) {\r\n                maps += \"\\n\\r\";\r\n            }\r\n\r\n            maps += this._tileMaps[i]!._texture!._bufferView!.toString();\r\n        }\r\n        const hiddenElement = document.createElement(\"a\");\r\n        hiddenElement.href = \"data:octet/stream;charset=utf-8,\" + encodeURI(maps);\r\n        hiddenElement.target = \"_blank\";\r\n        hiddenElement.download = this.name + \".tilemaps\";\r\n        hiddenElement.click();\r\n        hiddenElement.remove();\r\n    }\r\n\r\n    /**\r\n     * Imports the .tilemaps file\r\n     * @param url of the .tilemaps file\r\n     */\r\n    public loadTileMaps(url: string): void {\r\n        const xhr = new XMLHttpRequest();\r\n        xhr.open(\"GET\", url);\r\n\r\n        const _lc = this.options!.layerCount || 0;\r\n\r\n        xhr.onload = () => {\r\n            const data = xhr.response.split(\"\\n\\r\");\r\n            for (let i = 0; i < _lc; i++) {\r\n                const d = data[i].split(\",\").map(Number);\r\n                const t = this._createTileBuffer(d);\r\n                this._tileMaps[i].dispose();\r\n                this._tileMaps[i] = t;\r\n            }\r\n            this._material.setTextureArray(\"tileMap\", this._tileMaps);\r\n        };\r\n        xhr.send();\r\n    }\r\n\r\n    /**\r\n     * Release associated resources\r\n     */\r\n    public dispose(): void {\r\n        this._output.dispose();\r\n        this._material.dispose();\r\n        this._animationMap.dispose();\r\n        this._tileMaps.forEach((tm) => {\r\n            tm.dispose();\r\n        });\r\n        this._frameMap.dispose();\r\n    }\r\n}\r\n"]}