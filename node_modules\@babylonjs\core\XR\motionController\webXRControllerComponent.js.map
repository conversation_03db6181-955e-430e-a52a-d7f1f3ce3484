{"version": 3, "file": "webXRControllerComponent.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/motionController/webXRControllerComponent.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAoDnD;;GAEG;AACH,MAAM,OAAO,wBAAwB;IA2CjC;;;;;;;;OAQG;IACH;IACI;;OAEG;IACI,EAAU;IACjB;;OAEG;IACI,IAAmC,EAClC,eAAuB,CAAC,CAAC,EACzB,eAAyB,EAAE;QAN5B,OAAE,GAAF,EAAE,CAAQ;QAIV,SAAI,GAAJ,IAAI,CAA+B;QAClC,iBAAY,GAAZ,YAAY,CAAa;QACzB,iBAAY,GAAZ,YAAY,CAAe;QA7D/B,UAAK,GAAoC;YAC7C,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;SACP,CAAC;QACM,aAAQ,GAA2C,EAAE,CAAC;QACtD,kBAAa,GAAW,CAAC,CAAC;QAC1B,gBAAW,GAAY,KAAK,CAAC;QAC7B,aAAQ,GAAY,KAAK,CAAC;QAC1B,aAAQ,GAAY,KAAK,CAAC;QAuBlC;;;WAGG;QACI,iCAA4B,GAAyC,IAAI,UAAU,EAAE,CAAC;QAC7F;;;WAGG;QACI,mCAA8B,GAAyC,IAAI,UAAU,EAAE,CAAC;IAsB5F,CAAC;IAEJ;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;IAChD,CAAC;IAED;;;OAGG;IACI,MAAM;QACT,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,OAAO,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,gBAAgD;QAC1D,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QAEnB,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YACjB,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC3D,0CAA0C;YAC1C,IAAI,CAAC,MAAM,EAAE;gBACT,OAAO;aACV;YACD,IAAI,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,KAAK,EAAE;gBACrC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG;oBACjB,OAAO,EAAE,MAAM,CAAC,KAAK;oBACrB,QAAQ,EAAE,IAAI,CAAC,aAAa;iBAC/B,CAAC;gBACF,aAAa,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC;aACrC;YACD,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,OAAO,EAAE;gBAClC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG;oBACnB,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;iBAC1B,CAAC;gBACF,aAAa,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;aAClC;YACD,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,OAAO,EAAE;gBAClC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG;oBACnB,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;iBAC1B,CAAC;gBACF,aAAa,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;aAClC;SACJ;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACf,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC9D,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG;oBAChB,OAAO,EAAE;wBACL,CAAC,EAAE,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBAC9C,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;qBAClB;oBACD,QAAQ,EAAE;wBACN,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;wBACf,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;qBAClB;iBACJ,CAAC;gBACF,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,UAAU,GAAG,IAAI,CAAC;aACrB;YAED,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC9D,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;oBACnB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC7E;qBAAM;oBACH,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG;wBAChB,OAAO,EAAE;4BACL,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;4BACf,CAAC,EAAE,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;yBACjD;wBACD,QAAQ,EAAE;4BACN,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;4BACf,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;yBAClB;qBACJ,CAAC;iBACL;gBACD,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,UAAU,GAAG,IAAI,CAAC;aACrB;SACJ;QAED,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAC7D;QACD,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACjE;IACL,CAAC;;AA9MD;;GAEG;AACW,oCAAW,GAAkC,QAAQ,AAA1C,CAA2C;AACpE;;GAEG;AACW,qCAAY,GAAkC,SAAS,AAA3C,CAA4C;AACtE;;GAEG;AACW,wCAAe,GAAkC,YAAY,AAA9C,CAA+C;AAC5E;;GAEG;AACW,sCAAa,GAAkC,UAAU,AAA5C,CAA6C;AACxE;;GAEG;AACW,qCAAY,GAAkC,SAAS,AAA3C,CAA4C", "sourcesContent": ["import type { IMinimalMotionControllerObject, MotionControllerComponentType } from \"./webXRAbstractMotionController\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { IDisposable } from \"../../scene\";\r\n\r\n/**\r\n * X-Y values for axes in WebXR\r\n */\r\nexport interface IWebXRMotionControllerAxesValue {\r\n    /**\r\n     * The value of the x axis\r\n     */\r\n    x: number;\r\n    /**\r\n     * The value of the y-axis\r\n     */\r\n    y: number;\r\n}\r\n\r\n/**\r\n * changed / previous values for the values of this component\r\n */\r\nexport interface IWebXRMotionControllerComponentChangesValues<T> {\r\n    /**\r\n     * current (this frame) value\r\n     */\r\n    current: T;\r\n    /**\r\n     * previous (last change) value\r\n     */\r\n    previous: T;\r\n}\r\n\r\n/**\r\n * Represents changes in the component between current frame and last values recorded\r\n */\r\nexport interface IWebXRMotionControllerComponentChanges {\r\n    /**\r\n     * will be populated with previous and current values if axes changed\r\n     */\r\n    axes?: IWebXRMotionControllerComponentChangesValues<IWebXRMotionControllerAxesValue>;\r\n    /**\r\n     * will be populated with previous and current values if pressed changed\r\n     */\r\n    pressed?: IWebXRMotionControllerComponentChangesValues<boolean>;\r\n    /**\r\n     * will be populated with previous and current values if touched changed\r\n     */\r\n    touched?: IWebXRMotionControllerComponentChangesValues<boolean>;\r\n    /**\r\n     * will be populated with previous and current values if value changed\r\n     */\r\n    value?: IWebXRMotionControllerComponentChangesValues<number>;\r\n}\r\n/**\r\n * This class represents a single component (for example button or thumbstick) of a motion controller\r\n */\r\nexport class WebXRControllerComponent implements IDisposable {\r\n    private _axes: IWebXRMotionControllerAxesValue = {\r\n        x: 0,\r\n        y: 0,\r\n    };\r\n    private _changes: IWebXRMotionControllerComponentChanges = {};\r\n    private _currentValue: number = 0;\r\n    private _hasChanges: boolean = false;\r\n    private _pressed: boolean = false;\r\n    private _touched: boolean = false;\r\n\r\n    /**\r\n     * button component type\r\n     */\r\n    public static BUTTON_TYPE: MotionControllerComponentType = \"button\";\r\n    /**\r\n     * squeeze component type\r\n     */\r\n    public static SQUEEZE_TYPE: MotionControllerComponentType = \"squeeze\";\r\n    /**\r\n     * Thumbstick component type\r\n     */\r\n    public static THUMBSTICK_TYPE: MotionControllerComponentType = \"thumbstick\";\r\n    /**\r\n     * Touchpad component type\r\n     */\r\n    public static TOUCHPAD_TYPE: MotionControllerComponentType = \"touchpad\";\r\n    /**\r\n     * trigger component type\r\n     */\r\n    public static TRIGGER_TYPE: MotionControllerComponentType = \"trigger\";\r\n\r\n    /**\r\n     * If axes are available for this component (like a touchpad or thumbstick) the observers will be notified when\r\n     * the axes data changes\r\n     */\r\n    public onAxisValueChangedObservable: Observable<{ x: number; y: number }> = new Observable();\r\n    /**\r\n     * Observers registered here will be triggered when the state of a button changes\r\n     * State change is either pressed / touched / value\r\n     */\r\n    public onButtonStateChangedObservable: Observable<WebXRControllerComponent> = new Observable();\r\n\r\n    /**\r\n     * Creates a new component for a motion controller.\r\n     * It is created by the motion controller itself\r\n     *\r\n     * @param id the id of this component\r\n     * @param type the type of the component\r\n     * @param _buttonIndex index in the buttons array of the gamepad\r\n     * @param _axesIndices indices of the values in the axes array of the gamepad\r\n     */\r\n    constructor(\r\n        /**\r\n         * the id of this component\r\n         */\r\n        public id: string,\r\n        /**\r\n         * the type of the component\r\n         */\r\n        public type: MotionControllerComponentType,\r\n        private _buttonIndex: number = -1,\r\n        private _axesIndices: number[] = []\r\n    ) {}\r\n\r\n    /**\r\n     * The current axes data. If this component has no axes it will still return an object { x: 0, y: 0 }\r\n     */\r\n    public get axes(): IWebXRMotionControllerAxesValue {\r\n        return this._axes;\r\n    }\r\n\r\n    /**\r\n     * Get the changes. Elements will be populated only if they changed with their previous and current value\r\n     */\r\n    public get changes(): IWebXRMotionControllerComponentChanges {\r\n        return this._changes;\r\n    }\r\n\r\n    /**\r\n     * Return whether or not the component changed the last frame\r\n     */\r\n    public get hasChanges(): boolean {\r\n        return this._hasChanges;\r\n    }\r\n\r\n    /**\r\n     * is the button currently pressed\r\n     */\r\n    public get pressed(): boolean {\r\n        return this._pressed;\r\n    }\r\n\r\n    /**\r\n     * is the button currently touched\r\n     */\r\n    public get touched(): boolean {\r\n        return this._touched;\r\n    }\r\n\r\n    /**\r\n     * Get the current value of this component\r\n     */\r\n    public get value(): number {\r\n        return this._currentValue;\r\n    }\r\n\r\n    /**\r\n     * Dispose this component\r\n     */\r\n    public dispose(): void {\r\n        this.onAxisValueChangedObservable.clear();\r\n        this.onButtonStateChangedObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Are there axes correlating to this component\r\n     * @returns true is axes data is available\r\n     */\r\n    public isAxes(): boolean {\r\n        return this._axesIndices.length !== 0;\r\n    }\r\n\r\n    /**\r\n     * Is this component a button (hence - pressable)\r\n     * @returns true if can be pressed\r\n     */\r\n    public isButton(): boolean {\r\n        return this._buttonIndex !== -1;\r\n    }\r\n\r\n    /**\r\n     * update this component using the gamepad object it is in. Called on every frame\r\n     * @param nativeController the native gamepad controller object\r\n     */\r\n    public update(nativeController: IMinimalMotionControllerObject) {\r\n        let buttonUpdated = false;\r\n        let axesUpdate = false;\r\n        this._hasChanges = false;\r\n        this._changes = {};\r\n\r\n        if (this.isButton()) {\r\n            const button = nativeController.buttons[this._buttonIndex];\r\n            // defensive, in case a profile was forced\r\n            if (!button) {\r\n                return;\r\n            }\r\n            if (this._currentValue !== button.value) {\r\n                this.changes.value = {\r\n                    current: button.value,\r\n                    previous: this._currentValue,\r\n                };\r\n                buttonUpdated = true;\r\n                this._currentValue = button.value;\r\n            }\r\n            if (this._touched !== button.touched) {\r\n                this.changes.touched = {\r\n                    current: button.touched,\r\n                    previous: this._touched,\r\n                };\r\n                buttonUpdated = true;\r\n                this._touched = button.touched;\r\n            }\r\n            if (this._pressed !== button.pressed) {\r\n                this.changes.pressed = {\r\n                    current: button.pressed,\r\n                    previous: this._pressed,\r\n                };\r\n                buttonUpdated = true;\r\n                this._pressed = button.pressed;\r\n            }\r\n        }\r\n\r\n        if (this.isAxes()) {\r\n            if (this._axes.x !== nativeController.axes[this._axesIndices[0]]) {\r\n                this.changes.axes = {\r\n                    current: {\r\n                        x: nativeController.axes[this._axesIndices[0]],\r\n                        y: this._axes.y,\r\n                    },\r\n                    previous: {\r\n                        x: this._axes.x,\r\n                        y: this._axes.y,\r\n                    },\r\n                };\r\n                this._axes.x = nativeController.axes[this._axesIndices[0]];\r\n                axesUpdate = true;\r\n            }\r\n\r\n            if (this._axes.y !== nativeController.axes[this._axesIndices[1]]) {\r\n                if (this.changes.axes) {\r\n                    this.changes.axes.current.y = nativeController.axes[this._axesIndices[1]];\r\n                } else {\r\n                    this.changes.axes = {\r\n                        current: {\r\n                            x: this._axes.x,\r\n                            y: nativeController.axes[this._axesIndices[1]],\r\n                        },\r\n                        previous: {\r\n                            x: this._axes.x,\r\n                            y: this._axes.y,\r\n                        },\r\n                    };\r\n                }\r\n                this._axes.y = nativeController.axes[this._axesIndices[1]];\r\n                axesUpdate = true;\r\n            }\r\n        }\r\n\r\n        if (buttonUpdated) {\r\n            this._hasChanges = true;\r\n            this.onButtonStateChangedObservable.notifyObservers(this);\r\n        }\r\n        if (axesUpdate) {\r\n            this._hasChanges = true;\r\n            this.onAxisValueChangedObservable.notifyObservers(this._axes);\r\n        }\r\n    }\r\n}\r\n"]}