{"version": 3, "file": "noiseBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Meshes/Node/Blocks/noiseBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AAEzD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,qCAAqC,EAAE,MAAM,2CAA2C,CAAC;AAClG,OAAO,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AACrD,OAAO,EAAE,MAAM,EAAE,MAAM,4BAA4B,CAAC;AACpD,OAAO,EAAE,6BAA6B,EAAE,MAAM,wCAAwC,CAAC;AAEvF;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,iBAAiB;IAC7C;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAEZ,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QAClG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAElF,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3F,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE9F,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;IAC/E,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEO,SAAS,CAAC,KAAa,EAAE,SAAiB;QAC9C,OAAO,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;IAC5C,CAAC;IAEO,UAAU,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC5D,MAAM,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;QACpB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/D,CAAC;IAEO,KAAK,CAAC,CAAS;QACnB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IACrD,CAAC;IAEO,cAAc,CAAC,CAAS,EAAE,CAAS;QACvC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;IAEO,KAAK,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU;QAC5C,IAAI,CAAS,EAAE,CAAS,EAAE,CAAS,CAAC;QACpC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;QAEvC,CAAC,IAAI,EAAE,CAAC;QACR,CAAC,IAAI,EAAE,CAAC;QACR,CAAC,IAAI,EAAE,CAAC;QAER,CAAC,IAAI,CAAC,CAAC;QACP,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAChC,CAAC,IAAI,CAAC,CAAC;QACP,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAChC,CAAC,IAAI,CAAC,CAAC;QACP,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAChC,CAAC,IAAI,CAAC,CAAC;QACP,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAChC,CAAC,IAAI,CAAC,CAAC;QACP,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,CAAC,IAAI,CAAC,CAAC;QACP,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAChC,CAAC,IAAI,CAAC,CAAC;QACP,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhC,OAAO,CAAC,CAAC;IACb,CAAC;IAEO,IAAI,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACxI,MAAM,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;QACnB,MAAM,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;QACnB,MAAM,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;QACnB,OAAO,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5H,CAAC;IAEO,YAAY,CAAC,QAAiB;QAClC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtD,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1B,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1B,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAE1B,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACzB,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACzB,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAEzB,OAAO,IAAI,CAAC,IAAI,CACZ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAChD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EACxD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EACxD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAChE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EACxD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAChE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAChE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EACxE,CAAC,EACD,CAAC,EACD,CAAC,CACJ,CAAC;IACN,CAAC;IAEO,aAAa,CAAC,QAAiB;QACnC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;IAC/C,CAAC;IAEO,OAAO,CAAC,QAAiB;QAC7B,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;IACpD,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,OAAe,EAAE,SAAiB,EAAE,SAAkB,EAAE,MAAe,EAAE,KAAa;QAC/F,MAAM,QAAQ,GAAG,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAE7H,IAAI,MAAM,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,IAAI,MAAM,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QACzC,MAAM,IAAI,GAAG,OAAO,GAAG,CAAC,CAAC;QAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE;YAC5B,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YAC/C,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;YACf,MAAM,IAAI,GAAG,CAAC;YACd,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YACzC,MAAM,IAAI,GAAG,CAAC;SACjB;QAED,MAAM,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,GAAG,IAAI,GAAG,EAAE;YACZ,OAAO,GAAG,GAAG,MAAM,CAAC;SACvB;QAED,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/C,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACzB,GAAG,IAAI,MAAM,CAAC;QACd,IAAI,IAAI,MAAM,GAAG,GAAG,CAAC;QACrB,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;IAC1C,CAAC;IAES,WAAW;QACjB,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;YACpC,MAAM,QAAQ,GAAG,KAAK,CAAC,kBAAkB,CAAC,6BAA6B,CAAC,SAAS,CAAY,CAAC;YAC9F,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAE1D,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAY,CAAC;YAC/D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAElD,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC,CAAC;IACN,CAAC;CACJ;AAED,aAAa,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC", "sourcesContent": ["import { NodeGeo<PERSON><PERSON><PERSON> } from \"../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../nodeGeometryBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"../Enums/nodeGeometryConnectionPointTypes\";\r\nimport { Vector3 } from \"../../../Maths/math.vector\";\r\nimport { <PERSON><PERSON><PERSON> } from \"../../../Maths/math.scalar\";\r\nimport { NodeGeometryContextualSources } from \"../Enums/nodeGeometryContextualSources\";\r\n\r\n/**\r\n * Block used to get a noise value\r\n */\r\nexport class NoiseBlock extends NodeGeometryBlock {\r\n    /**\r\n     * Create a new NoiseBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"offset\", NodeGeometryBlockConnectionPointTypes.Vector3, true, Vector3.Zero());\r\n        this.registerInput(\"scale\", NodeGeometryBlockConnectionPointTypes.Float, true, 1);\r\n\r\n        this.registerInput(\"octaves\", NodeGeometryBlockConnectionPointTypes.Float, true, 2, 0, 16);\r\n        this.registerInput(\"roughness\", NodeGeometryBlockConnectionPointTypes.Float, true, 0.5, 0, 1);\r\n\r\n        this.registerOutput(\"output\", NodeGeometryBlockConnectionPointTypes.Float);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"NoiseBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the offset input component\r\n     */\r\n    public get offset(): NodeGeometryConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the scale input component\r\n     */\r\n    public get scale(): NodeGeometryConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the octaves input component\r\n     */\r\n    public get octaves(): NodeGeometryConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the roughtness input component\r\n     */\r\n    public get roughness(): NodeGeometryConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the geometry output component\r\n     */\r\n    public get output(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    private _negateIf(value: number, condition: number) {\r\n        return condition !== 0 ? -value : value;\r\n    }\r\n\r\n    private _noiseGrad(hash: number, x: number, y: number, z: number) {\r\n        const h = hash & 15;\r\n        const u = h < 8 ? x : y;\r\n        const vt = h === 12 || h == 14 ? x : z;\r\n        const v = h < 4 ? y : vt;\r\n        return this._negateIf(u, h & u) + this._negateIf(v, h & 2);\r\n    }\r\n\r\n    private _fade(t: number) {\r\n        return t * t * t * (t * (t * 6.0 - 15.0) + 10.0);\r\n    }\r\n\r\n    private _hashBitRotate(x: number, k: number) {\r\n        return (x << k) | (x >> (32 - k));\r\n    }\r\n\r\n    private _hash(kx: number, ky: number, kz: number) {\r\n        let a: number, b: number, c: number;\r\n        a = b = c = 0xdeadbeef + (3 << 2) + 13;\r\n\r\n        c += kz;\r\n        b += ky;\r\n        a += kx;\r\n\r\n        c ^= b;\r\n        c -= this._hashBitRotate(b, 14);\r\n        a ^= c;\r\n        a -= this._hashBitRotate(c, 11);\r\n        b ^= a;\r\n        b -= this._hashBitRotate(a, 25);\r\n        c ^= b;\r\n        c -= this._hashBitRotate(b, 16);\r\n        a ^= c;\r\n        a -= this._hashBitRotate(c, 4);\r\n        b ^= a;\r\n        b -= this._hashBitRotate(a, 14);\r\n        c ^= b;\r\n        c -= this._hashBitRotate(b, 24);\r\n\r\n        return c;\r\n    }\r\n\r\n    private _mix(v0: number, v1: number, v2: number, v3: number, v4: number, v5: number, v6: number, v7: number, x: number, y: number, z: number) {\r\n        const x1 = 1.0 - x;\r\n        const y1 = 1.0 - y;\r\n        const z1 = 1.0 - z;\r\n        return z1 * (y1 * (v0 * x1 + v1 * x) + y * (v2 * x1 + v3 * x)) + z * (y1 * (v4 * x1 + v5 * x) + y * (v6 * x1 + v7 * x));\r\n    }\r\n\r\n    private _perlinNoise(position: Vector3) {\r\n        const X = (position.x | 0) - (position.x < 0 ? 1 : 0);\r\n        const Y = (position.y | 0) - (position.y < 0 ? 1 : 0);\r\n        const Z = (position.z | 0) - (position.z < 0 ? 1 : 0);\r\n\r\n        const fx = position.x - X;\r\n        const fy = position.y - Y;\r\n        const fz = position.z - Z;\r\n\r\n        const u = this._fade(fx);\r\n        const v = this._fade(fy);\r\n        const w = this._fade(fz);\r\n\r\n        return this._mix(\r\n            this._noiseGrad(this._hash(X, Y, Z), fx, fy, fz),\r\n            this._noiseGrad(this._hash(X + 1, Y, Z), fx - 1, fy, fz),\r\n            this._noiseGrad(this._hash(X, Y + 1, Z), fx, fy - 1, fz),\r\n            this._noiseGrad(this._hash(X + 1, Y + 1, Z), fx - 1, fy - 1, fz),\r\n            this._noiseGrad(this._hash(X, Y, Z + 1), fx, fy, fz - 1),\r\n            this._noiseGrad(this._hash(X + 1, Y, Z + 1), fx - 1, fy, fz - 1),\r\n            this._noiseGrad(this._hash(X, Y + 1, Z + 1), fx, fy - 1, fz - 1),\r\n            this._noiseGrad(this._hash(X + 1, Y + 1, Z + 1), fx - 1, fy - 1, fz - 1),\r\n            u,\r\n            v,\r\n            w\r\n        );\r\n    }\r\n\r\n    private _perlinSigned(position: Vector3) {\r\n        return this._perlinNoise(position) * 0.982;\r\n    }\r\n\r\n    private _perlin(position: Vector3) {\r\n        return this._perlinSigned(position) / 2.0 + 0.5;\r\n    }\r\n\r\n    /**\r\n     * Gets a perlin noise value\r\n     * @param octaves number of octaves\r\n     * @param roughness roughness\r\n     * @param _position position vector\r\n     * @param offset offset vector\r\n     * @param scale scale value\r\n     * @returns a value between 0 and 1\r\n     * @see Based on https://github.com/blender/blender/blob/main/source/blender/blenlib/intern/noise.cc#L533\r\n     */\r\n    public noise(octaves: number, roughness: number, _position: Vector3, offset: Vector3, scale: number) {\r\n        const position = new Vector3(_position.x * scale + offset.x, _position.y * scale + offset.y, _position.z * scale + offset.z);\r\n\r\n        let fscale = 1.0;\r\n        let amp = 1.0;\r\n        let maxamp = 0.0;\r\n        let sum = 0.0;\r\n        octaves = Scalar.Clamp(octaves, 0, 15.0);\r\n        const step = octaves | 0;\r\n\r\n        for (let i = 0; i <= step; i++) {\r\n            const t = this._perlin(position.scale(fscale));\r\n            sum += t * amp;\r\n            maxamp += amp;\r\n            amp *= Scalar.Clamp(roughness, 0.0, 1.0);\r\n            fscale *= 2.0;\r\n        }\r\n\r\n        const rmd = octaves - Math.floor(octaves);\r\n        if (rmd == 0.0) {\r\n            return sum / maxamp;\r\n        }\r\n\r\n        const t = this._perlin(position.scale(fscale));\r\n        let sum2 = sum + t * amp;\r\n        sum /= maxamp;\r\n        sum2 /= maxamp + amp;\r\n        return (1.0 - rmd) * sum + rmd * sum2;\r\n    }\r\n\r\n    protected _buildBlock() {\r\n        this.output._storedFunction = (state) => {\r\n            const position = state.getContextualValue(NodeGeometryContextualSources.Positions) as Vector3;\r\n            const octaves = this.octaves.getConnectedValue(state);\r\n            const roughness = this.roughness.getConnectedValue(state);\r\n\r\n            const offset = this.offset.getConnectedValue(state) as Vector3;\r\n            const scale = this.scale.getConnectedValue(state);\r\n\r\n            return this.noise(octaves, roughness, position, offset, scale);\r\n        };\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.NoiseBlock\", NoiseBlock);\r\n"]}