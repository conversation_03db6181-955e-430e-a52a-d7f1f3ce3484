{"version": 3, "file": "volumetricLightScatteringPostProcess.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/volumetricLightScatteringPostProcess.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,kBAAkB,EAAE,SAAS,EAAE,wBAAwB,EAAE,MAAM,oBAAoB,CAAC;AAE7F,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAKtD,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,mBAAmB,EAAE,MAAM,2CAA2C,CAAC;AAChF,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAGjD,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAE9D,OAAO,yBAAyB,CAAC;AACjC,OAAO,+CAA+C,CAAC;AACvD,OAAO,iDAAiD,CAAC;AACzD,OAAO,mDAAmD,CAAC;AAC3D,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,QAAQ,EAAE,MAAM,wBAAwB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAIlD,OAAO,EAAE,0BAA0B,EAAE,MAAM,uCAAuC,CAAC;AAEnF;;GAEG;AACH,MAAM,OAAO,oCAAqC,SAAQ,WAAW;IAmCjE;;;OAGG;IACH,IAAW,eAAe;QACtB,MAAM,CAAC,IAAI,CAAC,gHAAgH,CAAC,CAAC;QAC9H,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAW,eAAe,CAAC,eAAwB;QAC/C,MAAM,CAAC,IAAI,CAAC,gHAAgH,CAAC,CAAC;IAClI,CAAC;IAuCD;;;;;;;;;;;OAWG;IACH,YACI,IAAY,EACZ,KAAU,EACV,MAAwB,EACxB,IAAW,EACX,UAAkB,GAAG,EACrB,eAAuB,OAAO,CAAC,qBAAqB,EACpD,MAAe,EACf,QAAkB,EAClB,KAAa;QAEb,KAAK,CACD,IAAI,EACJ,2BAA2B,EAC3B,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,sBAAsB,EAAE,SAAS,CAAC,EAClE,CAAC,wBAAwB,CAAC,EAC1B,KAAK,CAAC,gBAAgB,IAAI,KAAK,EAC/B,MAAM,EACN,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,sBAAsB,GAAG,OAAO,CACnC,CAAC;QAnHE,uBAAkB,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAOrD;;WAEG;QAEI,uBAAkB,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpD;;WAEG;QAEI,0BAAqB,GAAY,KAAK,CAAC;QAE9C;;WAEG;QAEI,WAAM,GAAY,IAAI,CAAC;QAqB9B;;WAEG;QAEI,mBAAc,GAAmB,EAAE,CAAC;QAE3C;;;WAGG;QAEI,mBAAc,GAAmB,EAAE,CAAC;QAE3C;;WAEG;QAEI,aAAQ,GAAG,GAAG,CAAC;QAEtB;;WAEG;QAEI,UAAK,GAAG,OAAO,CAAC;QAEvB;;WAEG;QAEI,WAAM,GAAG,OAAO,CAAC;QAExB;;WAEG;QAEI,YAAO,GAAG,KAAK,CAAC;QAqCnB,KAAK,GAAG,MAAM,EAAE,QAAQ,EAAE,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,iCAAiC;QAErF,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;QAEtG,iBAAiB;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,oCAAoC,CAAC,iBAAiB,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QAEnH,YAAY;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,CAAC;QAElD,IAAI,CAAC,UAAU,GAAG,CAAC,MAAc,EAAE,EAAE;YACjC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACnB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACxB;YAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAC3B,CAAC,CAAC;QAEF,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAc,EAAE,EAAE;YAC1C,IAAI,CAAC,4BAA4B,CAAQ,KAAK,CAAC,CAAC;YAEhD,MAAM,CAAC,UAAU,CAAC,wBAAwB,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAChF,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,sBAAsB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,sCAAsC,CAAC;IAClD,CAAC;IAEO,QAAQ,CAAC,OAAgB,EAAE,YAAqB;QACpD,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QAE/B,8BAA8B;QAC9B,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;YACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,6BAA6B,CAAC,sBAAsB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,CAAC;QAEnI,IAAI,iBAAiB,EAAE;YACnB,OAAO,iBAAiB,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;SAC3E;QAED,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAC5C,MAAM,QAAQ,GAAQ,OAAO,CAAC,WAAW,EAAE,CAAC;QAE5C,aAAa;QACb,IAAI,QAAQ,EAAE;YACV,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE;gBAC7B,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;aACrC;YAED,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;gBACjD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBAClC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aAC/B;YACD,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE;gBAClD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;gBACnC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aAC/B;SACJ;QAED,QAAQ;QACR,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAChD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACxE,OAAO,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAChG;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;SAClD;QAED,YAAY;QACZ,IAAI,YAAY,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YACpC,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE;gBAC7C,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;aAC1C;SACJ;QAED,qBAAqB;QACrB,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAE,CAAC;QAC9D,MAAM,aAAa,GAAG,WAAW,CAAC,OAAO,CAAC;QAC1C,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,aAAa,KAAK,IAAI,EAAE;YACxB,WAAW,CAAC,SAAS,CACjB,IAAI;iBACC,QAAQ,EAAE;iBACV,SAAS,EAAE;iBACX,YAAY,CACT,+BAA+B,EAC/B,OAAO,EACP,CAAC,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,eAAe,CAAC,EACtD,CAAC,gBAAgB,CAAC,EAClB,IAAI,EACJ,SAAS,EACT,SAAS,EACT,SAAS,EACT,EAAE,2BAA2B,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAC3D,EACL,IAAI,CACP,CAAC;SACL;QAED,OAAO,WAAW,CAAC,MAAO,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;IAED;;;OAGG;IACI,qBAAqB,CAAC,QAAiB;QAC1C,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,qBAAqB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,MAAc;QACzB,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACnG,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;YACjB,MAAM,CAAC,QAAQ,EAAE,CAAC,mBAAmB,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;SAC7D;QAED,IAAI,CAAC,6BAA6B,CAAC,OAAO,EAAE,CAAC;QAC7C,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED,kBAAkB;IACV,aAAa,CAAC,IAAkB;QACpC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YAC9J,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,WAAW,CAAC,KAAY,EAAE,KAAa;QAC3C,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,CAAC,6BAA6B,GAAG,IAAI,mBAAmB,CACxD,8BAA8B,EAC9B,EAAE,KAAK,EAAE,MAAM,CAAC,cAAc,EAAE,GAAG,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,eAAe,EAAE,GAAG,KAAK,EAAE,EACpF,KAAK,EACL,KAAK,EACL,IAAI,EACJ,SAAS,CAAC,wBAAwB,CACrC,CAAC;QACF,IAAI,CAAC,6BAA6B,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACrE,IAAI,CAAC,6BAA6B,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACrE,IAAI,CAAC,6BAA6B,CAAC,UAAU,GAAG,IAAI,CAAC;QACrD,IAAI,CAAC,6BAA6B,CAAC,eAAe,GAAG,KAAK,CAAC;QAC3D,IAAI,CAAC,6BAA6B,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAE/D,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;SACvE;aAAM;YACH,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;SACtE;QAED,uCAAuC;QACvC,MAAM,aAAa,GAAG,CAAC,OAAgB,EAAQ,EAAE;YAC7C,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACjD,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACjD,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE;gBACnC,OAAO;aACV;YAED,aAAa,CAAC,6BAA6B,CAAC,qBAAqB,GAAG,KAAK,CAAC;YAE1E,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YAEvC,IAAI,CAAC,QAAQ,EAAE;gBACX,OAAO;aACV;YAED,MAAM,KAAK,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC;YACvC,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;YAEjC,UAAU;YACV,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;YAEnG,qBAAqB;YACrB,MAAM,KAAK,GAAG,aAAa,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC;YAEjG,IAAI,KAAK,CAAC,UAAU,EAAE;gBAClB,OAAO;aACV;YAED,MAAM,0BAA0B,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,aAAa,CAAC,gBAAgB,CAAC,CAAC;YAExJ,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,0BAA0B,CAAC,EAAE;gBACpD,MAAM,iBAAiB,GAAG,aAAa,CAAC,6BAA6B,CAAC,sBAAsB,EAAE,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;gBAE3H,IAAI,WAAW,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;gBAC5C,IAAI,aAAa,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;oBAC7C,WAAW,GAAG,QAAQ,CAAC,eAAe,EAAE,CAAC;iBAC5C;gBAED,IAAI,CAAC,WAAW,EAAE;oBACd,OAAO;iBACV;gBAED,MAAM,MAAM,GAAG,WAAW,CAAC,MAAO,CAAC;gBAEnC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBACjC,IAAI,CAAC,0BAA0B,EAAE;oBAC7B,aAAa,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;iBAC3D;gBAED,IAAI,aAAa,KAAK,IAAI,CAAC,IAAI,EAAE;oBAC7B,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,EAAE,aAAa,CAAC,CAAC;iBAChE;qBAAM,IAAI,iBAAiB,EAAE;oBAC1B,iBAAiB,CAAC,cAAc,CAAC,aAAa,CAAC,cAAc,EAAE,EAAE,aAAqB,EAAE,OAAO,CAAC,CAAC;iBACpG;qBAAM;oBACH,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;oBAE/D,aAAa;oBACb,IAAI,QAAQ,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE;wBACzC,MAAM,YAAY,GAAG,QAAQ,CAAC,mBAAmB,EAAE,CAAC;wBAEpD,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;wBAElD,IAAI,YAAY,EAAE;4BACd,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,YAAY,CAAC,gBAAgB,EAAE,CAAC,CAAC;yBACtE;qBACJ;oBAED,QAAQ;oBACR,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,wBAAwB,IAAI,aAAa,CAAC,QAAQ,EAAE;wBAC5F,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,aAAa,CAAC,QAAQ,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC;qBAC5F;iBACJ;gBAED,IAAI,0BAA0B,IAAI,aAAa,CAAC,gBAAgB,EAAE;oBAC9D,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC;iBAC7D;gBAED,OAAO;gBACP,aAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,gBAAgB,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;oBAChJ,IAAI,CAAC,UAAU,EAAE;wBACb,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;qBACpC;gBACL,CAAC,CAAC,CAAC;aACN;QACL,CAAC,CAAC;QAEF,kCAAkC;QAClC,IAAI,oBAA4B,CAAC;QACjC,MAAM,eAAe,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAEvD,IAAI,CAAC,6BAA6B,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAS,EAAE;YACvE,oBAAoB,GAAG,KAAK,CAAC,UAAU,CAAC;YACxC,KAAK,CAAC,UAAU,GAAG,eAAe,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,6BAA6B,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAS,EAAE;YACtE,KAAK,CAAC,UAAU,GAAG,oBAAoB,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,6BAA6B,CAAC,qBAAqB,GAAG,CAAC,IAAkB,EAAE,WAAmB,EAAE,OAAiB,EAAE,EAAE;YACtH,IAAI,CAAC,OAAO,IAAI,WAAW,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;oBAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAClC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;oBACvC,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;oBAEjD,IAAI,CAAC,QAAQ,EAAE;wBACX,SAAS;qBACZ;oBAED,MAAM,KAAK,GAAG,aAAa,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC;oBACjG,MAAM,0BAA0B,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,aAAa,CAAC,gBAAgB,CAAC,CAAC;oBAExJ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,0BAA0B,CAAC,EAAE;wBACrD,OAAO,KAAK,CAAC;qBAChB;iBACJ;aACJ;YAED,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC;QAEF,IAAI,CAAC,6BAA6B,CAAC,oBAAoB,GAAG,CACtD,eAAoC,EACpC,kBAAuC,EACvC,oBAAyC,EACzC,kBAAuC,EACnC,EAAE;YACN,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;YACjC,IAAI,KAAa,CAAC;YAElB,IAAI,kBAAkB,CAAC,MAAM,EAAE;gBAC3B,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC5B,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBACxD,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;iBACjD;gBACD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;aAC9B;YAED,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACrD,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aAC9C;YAED,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACxD,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aACjD;YAED,IAAI,oBAAoB,CAAC,MAAM,EAAE;gBAC7B,kBAAkB;gBAClB,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBAC1D,MAAM,OAAO,GAAG,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACjD,MAAM,YAAY,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;oBAE/C,IAAI,YAAY,IAAI,KAAK,CAAC,YAAY,EAAE;wBACpC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;wBACnD,OAAO,CAAC,iBAAiB,GAAG,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;qBACtH;iBACJ;gBAED,MAAM,WAAW,GAAG,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,oBAAoB,CAAC,MAAM,CAAC,CAAC;gBACpF,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;oBACtB,oBAAoB;oBACpB,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,EAAE;wBAC/B,OAAO,CAAC,CAAC;qBACZ;oBACD,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,EAAE;wBAC/B,OAAO,CAAC,CAAC,CAAC;qBACb;oBAED,0BAA0B;oBAC1B,IAAI,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,EAAE;wBAC3C,OAAO,CAAC,CAAC;qBACZ;oBACD,IAAI,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,EAAE;wBAC3C,OAAO,CAAC,CAAC,CAAC;qBACb;oBAED,OAAO,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC;gBAEH,oBAAoB;gBACpB,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;gBAC7C,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBACjD,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;iBACrC;gBACD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;aAChD;QACL,CAAC,CAAC;IACN,CAAC;IAEO,4BAA4B,CAAC,KAAY;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAC7C,IAAI,YAAqB,CAAC;QAE1B,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC;SAC1C;aAAM,IAAI,IAAI,CAAC,YAAY,EAAE;YAC1B,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;SAC7C;aAAM;YACH,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;SAC1F;QAED,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAExF,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QACzD,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAE1D,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;SAC/D;IACL,CAAC;IAED,iBAAiB;IACjB;;;;;OAKG;IACI,MAAM,CAAC,iBAAiB,CAAC,IAAY,EAAE,KAAY;QACtD,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,iBAAiB,CAAC;QAEpD,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,IAAI,GAAG,UAAU,EAAE,KAAK,CAAC,CAAC;QAChE,QAAQ,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AA9gBU;IADN,kBAAkB,EAAE;gFAC+B;AAM7C;IADN,SAAS,EAAE;mFACkC;AAMvC;IADN,SAAS,EAAE;oEACkB;AAMvB;IADN,wBAAwB,EAAE;kEACT;AAmBX;IADN,SAAS,EAAE;4EAC+B;AAOpC;IADN,SAAS,EAAE;4EAC+B;AAMpC;IADN,SAAS,EAAE;sEACU;AAMf;IADN,SAAS,EAAE;mEACW;AAMhB;IADN,SAAS,EAAE;oEACY;AAMjB;IADN,SAAS,EAAE;qEACW;AA4c3B,aAAa,CAAC,8CAA8C,EAAE,oCAAoC,CAAC,CAAC", "sourcesContent": ["import { serializeAsVector3, serialize, serializeAsMeshReference } from \"../Misc/decorators\";\r\nimport type { SmartArray } from \"../Misc/smartArray\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { Vector2, Vector3, Matrix } from \"../Maths/math.vector\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Material } from \"../Materials/material\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport { PostProcess } from \"./postProcess\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { Scene } from \"../scene\";\r\n\r\nimport { <PERSON>reate<PERSON>lane } from \"../Meshes/Builders/planeBuilder\";\r\n\r\nimport \"../Shaders/depth.vertex\";\r\nimport \"../Shaders/volumetricLightScattering.fragment\";\r\nimport \"../Shaders/volumetricLightScatteringPass.vertex\";\r\nimport \"../Shaders/volumetricLightScatteringPass.fragment\";\r\nimport { Color4, Color3 } from \"../Maths/math.color\";\r\nimport { Viewport } from \"../Maths/math.viewport\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport type { Nullable } from \"../types\";\r\n\r\nimport type { Engine } from \"../Engines/engine\";\r\nimport { PushAttributesForInstances } from \"../Materials/materialHelper.functions\";\r\n\r\n/**\r\n *  Inspired by https://developer.nvidia.com/gpugems/gpugems3/part-ii-light-and-shadows/chapter-13-volumetric-light-scattering-post-process\r\n */\r\nexport class VolumetricLightScatteringPostProcess extends PostProcess {\r\n    // Members\r\n    private _volumetricLightScatteringRTT: RenderTargetTexture;\r\n    private _viewPort: Viewport;\r\n    private _screenCoordinates: Vector2 = Vector2.Zero();\r\n\r\n    /**\r\n     * If not undefined, the mesh position is computed from the attached node position\r\n     */\r\n    public attachedNode: { position: Vector3 };\r\n\r\n    /**\r\n     * Custom position of the mesh. Used if \"useCustomMeshPosition\" is set to \"true\"\r\n     */\r\n    @serializeAsVector3()\r\n    public customMeshPosition: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * Set if the post-process should use a custom position for the light source (true) or the internal mesh position (false)\r\n     */\r\n    @serialize()\r\n    public useCustomMeshPosition: boolean = false;\r\n\r\n    /**\r\n     * If the post-process should inverse the light scattering direction\r\n     */\r\n    @serialize()\r\n    public invert: boolean = true;\r\n\r\n    /**\r\n     * The internal mesh used by the post-process\r\n     */\r\n    @serializeAsMeshReference()\r\n    public mesh: Mesh;\r\n\r\n    /**\r\n     * @internal\r\n     * VolumetricLightScatteringPostProcess.useDiffuseColor is no longer used, use the mesh material directly instead\r\n     */\r\n    public get useDiffuseColor(): boolean {\r\n        Logger.Warn(\"VolumetricLightScatteringPostProcess.useDiffuseColor is no longer used, use the mesh material directly instead\");\r\n        return false;\r\n    }\r\n\r\n    public set useDiffuseColor(useDiffuseColor: boolean) {\r\n        Logger.Warn(\"VolumetricLightScatteringPostProcess.useDiffuseColor is no longer used, use the mesh material directly instead\");\r\n    }\r\n\r\n    /**\r\n     * Array containing the excluded meshes not rendered in the internal pass\r\n     */\r\n    @serialize()\r\n    public excludedMeshes: AbstractMesh[] = [];\r\n\r\n    /**\r\n     * Array containing the only meshes rendered in the internal pass.\r\n     * If this array is not empty, only the meshes from this array are rendered in the internal pass\r\n     */\r\n    @serialize()\r\n    public includedMeshes: AbstractMesh[] = [];\r\n\r\n    /**\r\n     * Controls the overall intensity of the post-process\r\n     */\r\n    @serialize()\r\n    public exposure = 0.3;\r\n\r\n    /**\r\n     * Dissipates each sample's contribution in range [0, 1]\r\n     */\r\n    @serialize()\r\n    public decay = 0.96815;\r\n\r\n    /**\r\n     * Controls the overall intensity of each sample\r\n     */\r\n    @serialize()\r\n    public weight = 0.58767;\r\n\r\n    /**\r\n     * Controls the density of each sample\r\n     */\r\n    @serialize()\r\n    public density = 0.926;\r\n\r\n    /**\r\n     * @constructor\r\n     * @param name The post-process name\r\n     * @param ratio The size of the post-process and/or internal pass (0.5 means that your postprocess will have a width = canvas.width 0.5 and a height = canvas.height 0.5)\r\n     * @param camera The camera that the post-process will be attached to\r\n     * @param mesh The mesh used to create the light scattering\r\n     * @param samples The post-process quality, default 100\r\n     * @param samplingMode The post-process filtering mode\r\n     * @param engine The babylon engine\r\n     * @param reusable If the post-process is reusable\r\n     * @param scene The constructor needs a scene reference to initialize internal components. If \"camera\" is null a \"scene\" must be provided\r\n     */\r\n    constructor(\r\n        name: string,\r\n        ratio: any,\r\n        camera: Nullable<Camera>,\r\n        mesh?: Mesh,\r\n        samples: number = 100,\r\n        samplingMode: number = Texture.BILINEAR_SAMPLINGMODE,\r\n        engine?: Engine,\r\n        reusable?: boolean,\r\n        scene?: Scene\r\n    ) {\r\n        super(\r\n            name,\r\n            \"volumetricLightScattering\",\r\n            [\"decay\", \"exposure\", \"weight\", \"meshPositionOnScreen\", \"density\"],\r\n            [\"lightScatteringSampler\"],\r\n            ratio.postProcessRatio || ratio,\r\n            camera,\r\n            samplingMode,\r\n            engine,\r\n            reusable,\r\n            \"#define NUM_SAMPLES \" + samples\r\n        );\r\n        scene = camera?.getScene() ?? scene ?? this._scene; // parameter \"scene\" can be null.\r\n\r\n        engine = scene.getEngine();\r\n        this._viewPort = new Viewport(0, 0, 1, 1).toGlobal(engine.getRenderWidth(), engine.getRenderHeight());\r\n\r\n        // Configure mesh\r\n        this.mesh = mesh ?? VolumetricLightScatteringPostProcess.CreateDefaultMesh(\"VolumetricLightScatteringMesh\", scene);\r\n\r\n        // Configure\r\n        this._createPass(scene, ratio.passRatio || ratio);\r\n\r\n        this.onActivate = (camera: Camera) => {\r\n            if (!this.isSupported) {\r\n                this.dispose(camera);\r\n            }\r\n\r\n            this.onActivate = null;\r\n        };\r\n\r\n        this.onApplyObservable.add((effect: Effect) => {\r\n            this._updateMeshScreenCoordinates(<Scene>scene);\r\n\r\n            effect.setTexture(\"lightScatteringSampler\", this._volumetricLightScatteringRTT);\r\n            effect.setFloat(\"exposure\", this.exposure);\r\n            effect.setFloat(\"decay\", this.decay);\r\n            effect.setFloat(\"weight\", this.weight);\r\n            effect.setFloat(\"density\", this.density);\r\n            effect.setVector2(\"meshPositionOnScreen\", this._screenCoordinates);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"VolumetricLightScatteringPostProcess\"\r\n     * @returns \"VolumetricLightScatteringPostProcess\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"VolumetricLightScatteringPostProcess\";\r\n    }\r\n\r\n    private _isReady(subMesh: SubMesh, useInstances: boolean): boolean {\r\n        const mesh = subMesh.getMesh();\r\n\r\n        // Render this.mesh as default\r\n        if (mesh === this.mesh && mesh.material) {\r\n            return mesh.material.isReady(mesh);\r\n        }\r\n\r\n        const renderingMaterial = mesh._internalAbstractMeshDataInfo._materialForRenderPass?.[this._scene.getEngine().currentRenderPassId];\r\n\r\n        if (renderingMaterial) {\r\n            return renderingMaterial.isReadyForSubMesh(mesh, subMesh, useInstances);\r\n        }\r\n\r\n        const defines = [];\r\n        const attribs = [VertexBuffer.PositionKind];\r\n        const material: any = subMesh.getMaterial();\r\n\r\n        // Alpha test\r\n        if (material) {\r\n            if (material.needAlphaTesting()) {\r\n                defines.push(\"#define ALPHATEST\");\r\n            }\r\n\r\n            if (mesh.isVerticesDataPresent(VertexBuffer.UVKind)) {\r\n                attribs.push(VertexBuffer.UVKind);\r\n                defines.push(\"#define UV1\");\r\n            }\r\n            if (mesh.isVerticesDataPresent(VertexBuffer.UV2Kind)) {\r\n                attribs.push(VertexBuffer.UV2Kind);\r\n                defines.push(\"#define UV2\");\r\n            }\r\n        }\r\n\r\n        // Bones\r\n        if (mesh.useBones && mesh.computeBonesUsingShaders) {\r\n            attribs.push(VertexBuffer.MatricesIndicesKind);\r\n            attribs.push(VertexBuffer.MatricesWeightsKind);\r\n            defines.push(\"#define NUM_BONE_INFLUENCERS \" + mesh.numBoneInfluencers);\r\n            defines.push(\"#define BonesPerMesh \" + (mesh.skeleton ? mesh.skeleton.bones.length + 1 : 0));\r\n        } else {\r\n            defines.push(\"#define NUM_BONE_INFLUENCERS 0\");\r\n        }\r\n\r\n        // Instances\r\n        if (useInstances) {\r\n            defines.push(\"#define INSTANCES\");\r\n            PushAttributesForInstances(attribs);\r\n            if (subMesh.getRenderingMesh().hasThinInstances) {\r\n                defines.push(\"#define THIN_INSTANCES\");\r\n            }\r\n        }\r\n\r\n        // Get correct effect\r\n        const drawWrapper = subMesh._getDrawWrapper(undefined, true)!;\r\n        const cachedDefines = drawWrapper.defines;\r\n        const join = defines.join(\"\\n\");\r\n        if (cachedDefines !== join) {\r\n            drawWrapper.setEffect(\r\n                mesh\r\n                    .getScene()\r\n                    .getEngine()\r\n                    .createEffect(\r\n                        \"volumetricLightScatteringPass\",\r\n                        attribs,\r\n                        [\"world\", \"mBones\", \"viewProjection\", \"diffuseMatrix\"],\r\n                        [\"diffuseSampler\"],\r\n                        join,\r\n                        undefined,\r\n                        undefined,\r\n                        undefined,\r\n                        { maxSimultaneousMorphTargets: mesh.numBoneInfluencers }\r\n                    ),\r\n                join\r\n            );\r\n        }\r\n\r\n        return drawWrapper.effect!.isReady();\r\n    }\r\n\r\n    /**\r\n     * Sets the new light position for light scattering effect\r\n     * @param position The new custom light position\r\n     */\r\n    public setCustomMeshPosition(position: Vector3): void {\r\n        this.customMeshPosition = position;\r\n    }\r\n\r\n    /**\r\n     * Returns the light position for light scattering effect\r\n     * @returns Vector3 The custom light position\r\n     */\r\n    public getCustomMeshPosition(): Vector3 {\r\n        return this.customMeshPosition;\r\n    }\r\n\r\n    /**\r\n     * Disposes the internal assets and detaches the post-process from the camera\r\n     * @param camera The camera from which to detach the post-process\r\n     */\r\n    public dispose(camera: Camera): void {\r\n        const rttIndex = camera.getScene().customRenderTargets.indexOf(this._volumetricLightScatteringRTT);\r\n        if (rttIndex !== -1) {\r\n            camera.getScene().customRenderTargets.splice(rttIndex, 1);\r\n        }\r\n\r\n        this._volumetricLightScatteringRTT.dispose();\r\n        super.dispose(camera);\r\n    }\r\n\r\n    /**\r\n     * Returns the render target texture used by the post-process\r\n     * @returns the render target texture used by the post-process\r\n     */\r\n    public getPass(): RenderTargetTexture {\r\n        return this._volumetricLightScatteringRTT;\r\n    }\r\n\r\n    // Private methods\r\n    private _meshExcluded(mesh: AbstractMesh) {\r\n        if ((this.includedMeshes.length > 0 && this.includedMeshes.indexOf(mesh) === -1) || (this.excludedMeshes.length > 0 && this.excludedMeshes.indexOf(mesh) !== -1)) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    private _createPass(scene: Scene, ratio: number): void {\r\n        const engine = scene.getEngine();\r\n\r\n        this._volumetricLightScatteringRTT = new RenderTargetTexture(\r\n            \"volumetricLightScatteringMap\",\r\n            { width: engine.getRenderWidth() * ratio, height: engine.getRenderHeight() * ratio },\r\n            scene,\r\n            false,\r\n            true,\r\n            Constants.TEXTURETYPE_UNSIGNED_INT\r\n        );\r\n        this._volumetricLightScatteringRTT.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n        this._volumetricLightScatteringRTT.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n        this._volumetricLightScatteringRTT.renderList = null;\r\n        this._volumetricLightScatteringRTT.renderParticles = false;\r\n        this._volumetricLightScatteringRTT.ignoreCameraViewport = true;\r\n\r\n        const camera = this.getCamera();\r\n        if (camera) {\r\n            camera.customRenderTargets.push(this._volumetricLightScatteringRTT);\r\n        } else {\r\n            scene.customRenderTargets.push(this._volumetricLightScatteringRTT);\r\n        }\r\n\r\n        // Custom render function for submeshes\r\n        const renderSubMesh = (subMesh: SubMesh): void => {\r\n            const renderingMesh = subMesh.getRenderingMesh();\r\n            const effectiveMesh = subMesh.getEffectiveMesh();\r\n            if (this._meshExcluded(renderingMesh)) {\r\n                return;\r\n            }\r\n\r\n            effectiveMesh._internalAbstractMeshDataInfo._isActiveIntermediate = false;\r\n\r\n            const material = subMesh.getMaterial();\r\n\r\n            if (!material) {\r\n                return;\r\n            }\r\n\r\n            const scene = renderingMesh.getScene();\r\n            const engine = scene.getEngine();\r\n\r\n            // Culling\r\n            engine.setState(material.backFaceCulling, undefined, undefined, undefined, material.cullBackFaces);\r\n\r\n            // Managing instances\r\n            const batch = renderingMesh._getInstancesRenderList(subMesh._id, !!subMesh.getReplacementMesh());\r\n\r\n            if (batch.mustReturn) {\r\n                return;\r\n            }\r\n\r\n            const hardwareInstancedRendering = engine.getCaps().instancedArrays && (batch.visibleInstances[subMesh._id] !== null || renderingMesh.hasThinInstances);\r\n\r\n            if (this._isReady(subMesh, hardwareInstancedRendering)) {\r\n                const renderingMaterial = effectiveMesh._internalAbstractMeshDataInfo._materialForRenderPass?.[engine.currentRenderPassId];\r\n\r\n                let drawWrapper = subMesh._getDrawWrapper();\r\n                if (renderingMesh === this.mesh && !drawWrapper) {\r\n                    drawWrapper = material._getDrawWrapper();\r\n                }\r\n\r\n                if (!drawWrapper) {\r\n                    return;\r\n                }\r\n\r\n                const effect = drawWrapper.effect!;\r\n\r\n                engine.enableEffect(drawWrapper);\r\n                if (!hardwareInstancedRendering) {\r\n                    renderingMesh._bind(subMesh, effect, material.fillMode);\r\n                }\r\n\r\n                if (renderingMesh === this.mesh) {\r\n                    material.bind(effectiveMesh.getWorldMatrix(), renderingMesh);\r\n                } else if (renderingMaterial) {\r\n                    renderingMaterial.bindForSubMesh(effectiveMesh.getWorldMatrix(), effectiveMesh as Mesh, subMesh);\r\n                } else {\r\n                    effect.setMatrix(\"viewProjection\", scene.getTransformMatrix());\r\n\r\n                    // Alpha test\r\n                    if (material && material.needAlphaTesting()) {\r\n                        const alphaTexture = material.getAlphaTestTexture();\r\n\r\n                        effect.setTexture(\"diffuseSampler\", alphaTexture);\r\n\r\n                        if (alphaTexture) {\r\n                            effect.setMatrix(\"diffuseMatrix\", alphaTexture.getTextureMatrix());\r\n                        }\r\n                    }\r\n\r\n                    // Bones\r\n                    if (renderingMesh.useBones && renderingMesh.computeBonesUsingShaders && renderingMesh.skeleton) {\r\n                        effect.setMatrices(\"mBones\", renderingMesh.skeleton.getTransformMatrices(renderingMesh));\r\n                    }\r\n                }\r\n\r\n                if (hardwareInstancedRendering && renderingMesh.hasThinInstances) {\r\n                    effect.setMatrix(\"world\", effectiveMesh.getWorldMatrix());\r\n                }\r\n\r\n                // Draw\r\n                renderingMesh._processRendering(effectiveMesh, subMesh, effect, Material.TriangleFillMode, batch, hardwareInstancedRendering, (isInstance, world) => {\r\n                    if (!isInstance) {\r\n                        effect.setMatrix(\"world\", world);\r\n                    }\r\n                });\r\n            }\r\n        };\r\n\r\n        // Render target texture callbacks\r\n        let savedSceneClearColor: Color4;\r\n        const sceneClearColor = new Color4(0.0, 0.0, 0.0, 1.0);\r\n\r\n        this._volumetricLightScatteringRTT.onBeforeRenderObservable.add((): void => {\r\n            savedSceneClearColor = scene.clearColor;\r\n            scene.clearColor = sceneClearColor;\r\n        });\r\n\r\n        this._volumetricLightScatteringRTT.onAfterRenderObservable.add((): void => {\r\n            scene.clearColor = savedSceneClearColor;\r\n        });\r\n\r\n        this._volumetricLightScatteringRTT.customIsReadyFunction = (mesh: AbstractMesh, refreshRate: number, preWarm?: boolean) => {\r\n            if ((preWarm || refreshRate === 0) && mesh.subMeshes) {\r\n                for (let i = 0; i < mesh.subMeshes.length; ++i) {\r\n                    const subMesh = mesh.subMeshes[i];\r\n                    const material = subMesh.getMaterial();\r\n                    const renderingMesh = subMesh.getRenderingMesh();\r\n\r\n                    if (!material) {\r\n                        continue;\r\n                    }\r\n\r\n                    const batch = renderingMesh._getInstancesRenderList(subMesh._id, !!subMesh.getReplacementMesh());\r\n                    const hardwareInstancedRendering = engine.getCaps().instancedArrays && (batch.visibleInstances[subMesh._id] !== null || renderingMesh.hasThinInstances);\r\n\r\n                    if (!this._isReady(subMesh, hardwareInstancedRendering)) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n\r\n            return true;\r\n        };\r\n\r\n        this._volumetricLightScatteringRTT.customRenderFunction = (\r\n            opaqueSubMeshes: SmartArray<SubMesh>,\r\n            alphaTestSubMeshes: SmartArray<SubMesh>,\r\n            transparentSubMeshes: SmartArray<SubMesh>,\r\n            depthOnlySubMeshes: SmartArray<SubMesh>\r\n        ): void => {\r\n            const engine = scene.getEngine();\r\n            let index: number;\r\n\r\n            if (depthOnlySubMeshes.length) {\r\n                engine.setColorWrite(false);\r\n                for (index = 0; index < depthOnlySubMeshes.length; index++) {\r\n                    renderSubMesh(depthOnlySubMeshes.data[index]);\r\n                }\r\n                engine.setColorWrite(true);\r\n            }\r\n\r\n            for (index = 0; index < opaqueSubMeshes.length; index++) {\r\n                renderSubMesh(opaqueSubMeshes.data[index]);\r\n            }\r\n\r\n            for (index = 0; index < alphaTestSubMeshes.length; index++) {\r\n                renderSubMesh(alphaTestSubMeshes.data[index]);\r\n            }\r\n\r\n            if (transparentSubMeshes.length) {\r\n                // Sort sub meshes\r\n                for (index = 0; index < transparentSubMeshes.length; index++) {\r\n                    const submesh = transparentSubMeshes.data[index];\r\n                    const boundingInfo = submesh.getBoundingInfo();\r\n\r\n                    if (boundingInfo && scene.activeCamera) {\r\n                        submesh._alphaIndex = submesh.getMesh().alphaIndex;\r\n                        submesh._distanceToCamera = boundingInfo.boundingSphere.centerWorld.subtract(scene.activeCamera.position).length();\r\n                    }\r\n                }\r\n\r\n                const sortedArray = transparentSubMeshes.data.slice(0, transparentSubMeshes.length);\r\n                sortedArray.sort((a, b) => {\r\n                    // Alpha index first\r\n                    if (a._alphaIndex > b._alphaIndex) {\r\n                        return 1;\r\n                    }\r\n                    if (a._alphaIndex < b._alphaIndex) {\r\n                        return -1;\r\n                    }\r\n\r\n                    // Then distance to camera\r\n                    if (a._distanceToCamera < b._distanceToCamera) {\r\n                        return 1;\r\n                    }\r\n                    if (a._distanceToCamera > b._distanceToCamera) {\r\n                        return -1;\r\n                    }\r\n\r\n                    return 0;\r\n                });\r\n\r\n                // Render sub meshes\r\n                engine.setAlphaMode(Constants.ALPHA_COMBINE);\r\n                for (index = 0; index < sortedArray.length; index++) {\r\n                    renderSubMesh(sortedArray[index]);\r\n                }\r\n                engine.setAlphaMode(Constants.ALPHA_DISABLE);\r\n            }\r\n        };\r\n    }\r\n\r\n    private _updateMeshScreenCoordinates(scene: Scene): void {\r\n        const transform = scene.getTransformMatrix();\r\n        let meshPosition: Vector3;\r\n\r\n        if (this.useCustomMeshPosition) {\r\n            meshPosition = this.customMeshPosition;\r\n        } else if (this.attachedNode) {\r\n            meshPosition = this.attachedNode.position;\r\n        } else {\r\n            meshPosition = this.mesh.parent ? this.mesh.getAbsolutePosition() : this.mesh.position;\r\n        }\r\n\r\n        const pos = Vector3.Project(meshPosition, Matrix.Identity(), transform, this._viewPort);\r\n\r\n        this._screenCoordinates.x = pos.x / this._viewPort.width;\r\n        this._screenCoordinates.y = pos.y / this._viewPort.height;\r\n\r\n        if (this.invert) {\r\n            this._screenCoordinates.y = 1.0 - this._screenCoordinates.y;\r\n        }\r\n    }\r\n\r\n    // Static methods\r\n    /**\r\n     * Creates a default mesh for the Volumeric Light Scattering post-process\r\n     * @param name The mesh name\r\n     * @param scene The scene where to create the mesh\r\n     * @returns the default mesh\r\n     */\r\n    public static CreateDefaultMesh(name: string, scene: Scene): Mesh {\r\n        const mesh = CreatePlane(name, { size: 1 }, scene);\r\n        mesh.billboardMode = AbstractMesh.BILLBOARDMODE_ALL;\r\n\r\n        const material = new StandardMaterial(name + \"Material\", scene);\r\n        material.emissiveColor = new Color3(1, 1, 1);\r\n\r\n        mesh.material = material;\r\n\r\n        return mesh;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.VolumetricLightScatteringPostProcess\", VolumetricLightScatteringPostProcess);\r\n"]}