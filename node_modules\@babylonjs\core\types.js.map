{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../dev/core/src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\n/** <PERSON><PERSON> type for value that can be null */\r\nexport type Nullable<T> = T | null;\r\n/**\r\n * <PERSON>as type for number that are floats\r\n * @ignorenaming\r\n */\r\nexport type float = number;\r\n/**\r\n * <PERSON>as type for number that are doubles.\r\n * @ignorenaming\r\n */\r\nexport type double = number;\r\n/**\r\n * <PERSON>as type for number that are integer\r\n * @ignorenaming\r\n */\r\nexport type int = number;\r\n\r\n// Tuple manipulation\r\n/**\r\n * Empty\r\n */\r\nexport type Empty = [];\r\n\r\n/**\r\n * Removes the first element of T and shifts\r\n */\r\nexport type Shift<T> = T extends unknown[] ? (((...x: T) => void) extends (h: any, ...t: infer I) => void ? I : []) : unknown;\r\n\r\n/**\r\n * Gets the first element of T\r\n */\r\nexport type First<T> = T extends unknown[] ? (((...x: T) => void) extends (h: infer I, ...t: any) => void ? I : []) : never;\r\n\r\n/**\r\n * Inserts A into T at the start of T\r\n */\r\nexport type Unshift<T, A> = T extends unknown[] ? (((h: A, ...t: T) => void) extends (...i: infer I) => void ? I : unknown) : never;\r\n\r\n/**\r\n * Removes the last element of T\r\n */\r\nexport type Pop<T> = T extends unknown[] ? (((...x: T) => void) extends (...i: [...infer I, any]) => void ? I : unknown) : never;\r\n\r\n/**\r\n * Gets the last element of T\r\n */\r\nexport type Last<T> = T extends unknown[] ? (((...x: T) => void) extends (...i: [...infer H, infer I]) => void ? I : unknown) : never;\r\n\r\n/**\r\n * Appends A to T\r\n */\r\nexport type Push<T, A> = T extends unknown[] ? (((...a: [...T, A]) => void) extends (...i: infer I) => void ? I : unknown) : never;\r\n\r\n/**\r\n * Concats A and B\r\n */\r\nexport type Concat<A, B> = { 0: A; 1: Concat<Unshift<A, 0>, Shift<B>> }[Empty extends B ? 0 : 1];\r\n\r\n/**\r\n * Extracts from A what is not B\r\n *\r\n * @remarks\r\n * It does not remove duplicates (so Remove\\<[0, 0, 0], [0, 0]\\> yields [0]). This is intended and necessary behavior.\r\n */\r\nexport type Remove<A, B> = { 0: A; 1: Remove<Shift<A>, Shift<B>> }[Empty extends B ? 0 : 1];\r\n\r\n/**\r\n * The length of T\r\n */\r\nexport type Length<T> = T extends { length: number } ? T[\"length\"] : never;\r\n\r\ntype _FromLength<N extends number, R = Empty> = { 0: R; 1: _FromLength<N, Unshift<R, 0>> }[Length<R> extends N ? 0 : 1];\r\n\r\n/**\r\n * Creates a tuple of length N\r\n */\r\nexport type FromLength<N extends number> = _FromLength<N>;\r\n\r\n// compile-time math\r\n\r\n/**\r\n * Increments N\r\n */\r\nexport type Increment<N extends number> = Length<Unshift<_FromLength<N>, 0>>;\r\n\r\n/**\r\n * Decrements N\r\n */\r\nexport type Decrement<N extends number> = Length<Shift<_FromLength<N>>>;\r\n\r\n/**\r\n * Gets the sum of A and B\r\n */\r\nexport type Add<A extends number, B extends number> = Length<Concat<_FromLength<A>, _FromLength<B>>>;\r\n\r\n/**\r\n * Subtracts B from A\r\n */\r\nexport type Subtract<A extends number, B extends number> = Length<Remove<_FromLength<A>, _FromLength<B>>>;\r\n\r\n/**\r\n * Gets the type of an array's members\r\n */\r\nexport type Member<T, D = null> = D extends 0 ? T : T extends (infer U)[] ? Member<U, D extends number ? Decrement<D> : null> : T;\r\n\r\n/**\r\n * Flattens an array\r\n */\r\nexport type FlattenArray<A extends unknown[], D = null> = A extends (infer U)[] ? Member<Exclude<U, A>, D>[] : A extends unknown[] ? { [K in keyof A]: Member<A[K], D> } : A;\r\n\r\n/**\r\n * Whether T is a tuple\r\n */\r\nexport type IsTuple<T> = T extends [] ? false : T extends [infer Head, ...infer Rest] ? true : false;\r\n\r\n/**\r\n * Flattens a tuple\r\n */\r\nexport type FlattenTuple<A extends unknown[]> = A extends [infer U, ...infer Rest] ? (U extends unknown[] ? [...U, ...FlattenTuple<Rest>] : [U, ...FlattenTuple<Rest>]) : [];\r\n\r\n/**\r\n * Flattens an array or tuple\r\n */\r\nexport type Flatten<A extends unknown[]> = IsTuple<A> extends true ? FlattenTuple<A> : FlattenArray<A>;\r\n\r\ntype _Tuple<T, N extends number, R extends unknown[] = Empty> = R[\"length\"] extends N ? R : _Tuple<T, N, [T, ...R]>;\r\n\r\n/**\r\n * Creates a tuple of T with length N\r\n */\r\nexport type Tuple<T, N extends number> = _Tuple<T, N>;\r\n\r\n/** Alias type for number array or Float32Array */\r\nexport type FloatArray = number[] | Float32Array;\r\n/** Alias type for number array or Float32Array or Int32Array or Uint32Array or Uint16Array */\r\nexport type IndicesArray = number[] | Int32Array | Uint32Array | Uint16Array;\r\n\r\n/**\r\n * Alias for types that can be used by a Buffer or VertexBuffer.\r\n */\r\nexport type DataArray = number[] | ArrayBuffer | ArrayBufferView;\r\n\r\n/**\r\n * Alias type for primitive types\r\n * @ignorenaming\r\n */\r\ntype Primitive = undefined | null | boolean | string | number | Function | Element;\r\n\r\n/**\r\n * Type modifier to make all the properties of an object Readonly\r\n */\r\nexport type Immutable<T> = T extends Primitive\r\n    ? T\r\n    : T extends Array<infer U>\r\n      ? ReadonlyArray<U>\r\n      : /* T extends Map<infer K, infer V> ? ReadonlyMap<K, V> : // es2015+ only */\r\n        DeepImmutable<T>;\r\n\r\n/**\r\n * Type modifier to make all the properties of an object Readonly recursively\r\n */\r\nexport type DeepImmutable<T> = T extends Primitive\r\n    ? T\r\n    : T extends Array<infer U>\r\n      ? DeepImmutableArray<U>\r\n      : /* T extends Map<infer K, infer V> ? DeepImmutableMap<K, V> : // es2015+ only */\r\n        DeepImmutableObject<T>;\r\n\r\n/**\r\n * Type modifier to make object properties readonly.\r\n */\r\nexport type DeepImmutableObject<T> = { readonly [K in keyof T]: DeepImmutable<T[K]> };\r\n\r\n/** @internal */\r\ninterface DeepImmutableArray<T> extends ReadonlyArray<DeepImmutable<T>> {}\r\n/** @internal */\r\n/* interface DeepImmutableMap<K, V> extends ReadonlyMap<DeepImmutable<K>, DeepImmutable<V>> {} // es2015+ only */\r\n\r\nexport type Constructor<C extends new (...args: any[]) => any, I extends InstanceType<C> = InstanceType<C>> = {\r\n    new (...args: ConstructorParameters<C>): I;\r\n};\r\n\r\n/**\r\n * Alias type for image sources\r\n */\r\nexport type ImageSource = ImageBitmap | ImageData | HTMLImageElement | HTMLCanvasElement | HTMLVideoElement | OffscreenCanvas;\r\n"]}