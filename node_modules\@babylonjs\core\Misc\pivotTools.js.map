{"version": 3, "file": "pivotTools.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/pivotTools.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAGvD;;;GAGG;AACH,MAAM,OAAO,UAAU;IAQnB;;OAEG;IACI,MAAM,CAAC,yBAAyB,CAAC,IAAmB;QACvD,IAAI,IAAI,IAAI,UAAU,CAAC,YAAY,KAAK,CAAC,EAAE;YACvC,wCAAwC;YACxC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;YACnD,UAAU,CAAC,6BAA6B,GAAG,IAAI,CAAC,wBAAwB,CAAC;YACzE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;gBACpD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;gBAC7C,UAAU,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;gBAC5F,UAAU,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACnD,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACzD,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;gBACzE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;aACxD;SACJ;QACD,UAAU,CAAC,YAAY,EAAE,CAAC;IAC9B,CAAC;IACD;;OAEG;IACI,MAAM,CAAC,kBAAkB,CAAC,IAAmB;QAChD,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,UAAU,CAAC,YAAY,KAAK,CAAC,EAAE;YAC7F,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;YAC9C,IAAI,CAAC,wBAAwB,GAAG,UAAU,CAAC,6BAA6B,CAAC;YACzE,UAAU,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACnD,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzD,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YACzE,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;SAC7D;QACD,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;;AAvCD,0EAA0E;AAC1E,+EAA+E;AAChE,uBAAY,GAAG,CAAC,CAAC;AACjB,yBAAc,GAAG,IAAI,OAAO,EAAE,CAAC;AAC/B,4BAAiB,GAAG,IAAI,OAAO,EAAE,CAAC;AAClC,0BAAe,GAAG,IAAI,OAAO,EAAE,CAAC;AAChC,wCAA6B,GAAG,KAAK,CAAC", "sourcesContent": ["import { Vector3, Matrix } from \"../Maths/math.vector\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\n\r\n/**\r\n * Class containing a set of static utilities functions for managing Pivots\r\n * @internal\r\n */\r\nexport class PivotTools {\r\n    // Stores the state of the pivot cache (_oldPivotPoint, _pivotTranslation)\r\n    // store/remove pivot point should only be applied during their outermost calls\r\n    private static _PivotCached = 0;\r\n    private static _OldPivotPoint = new Vector3();\r\n    private static _PivotTranslation = new Vector3();\r\n    private static _PivotTmpVector = new Vector3();\r\n    private static _PivotPostMultiplyPivotMatrix = false;\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _RemoveAndStorePivotPoint(mesh: TransformNode) {\r\n        if (mesh && PivotTools._PivotCached === 0) {\r\n            // Save old pivot and set pivot to 0,0,0\r\n            mesh.getPivotPointToRef(PivotTools._OldPivotPoint);\r\n            PivotTools._PivotPostMultiplyPivotMatrix = mesh._postMultiplyPivotMatrix;\r\n            if (!PivotTools._OldPivotPoint.equalsToFloats(0, 0, 0)) {\r\n                mesh.setPivotMatrix(Matrix.IdentityReadOnly);\r\n                PivotTools._OldPivotPoint.subtractToRef(mesh.getPivotPoint(), PivotTools._PivotTranslation);\r\n                PivotTools._PivotTmpVector.copyFromFloats(1, 1, 1);\r\n                PivotTools._PivotTmpVector.subtractInPlace(mesh.scaling);\r\n                PivotTools._PivotTmpVector.multiplyInPlace(PivotTools._PivotTranslation);\r\n                mesh.position.addInPlace(PivotTools._PivotTmpVector);\r\n            }\r\n        }\r\n        PivotTools._PivotCached++;\r\n    }\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _RestorePivotPoint(mesh: TransformNode) {\r\n        if (mesh && !PivotTools._OldPivotPoint.equalsToFloats(0, 0, 0) && PivotTools._PivotCached === 1) {\r\n            mesh.setPivotPoint(PivotTools._OldPivotPoint);\r\n            mesh._postMultiplyPivotMatrix = PivotTools._PivotPostMultiplyPivotMatrix;\r\n            PivotTools._PivotTmpVector.copyFromFloats(1, 1, 1);\r\n            PivotTools._PivotTmpVector.subtractInPlace(mesh.scaling);\r\n            PivotTools._PivotTmpVector.multiplyInPlace(PivotTools._PivotTranslation);\r\n            mesh.position.subtractInPlace(PivotTools._PivotTmpVector);\r\n        }\r\n        this._PivotCached--;\r\n    }\r\n}\r\n"]}