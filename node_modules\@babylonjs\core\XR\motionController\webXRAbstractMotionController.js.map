{"version": 3, "file": "webXRAbstractMotionController.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/motionController/webXRAbstractMotionController.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,wBAAwB,EAAE,MAAM,4BAA4B,CAAC;AACtE,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAGxD,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAC9D,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AAyNzC;;;;GAIG;AACH,MAAM,OAAgB,6BAA6B;IA4C/C;;;;;;;;OAQG;IACH;IACI,gEAAgE;IACtD,KAAY;IACtB,gEAAgE;IACtD,MAA+B;IACzC;;OAEG;IACI,aAA6C;IACpD;;OAEG;IACI,UAAsC;IAC7C;;OAEG;IACI,2BAAoC,KAAK,EACxC,gBAIN;QAnBQ,UAAK,GAAL,KAAK,CAAO;QAEZ,WAAM,GAAN,MAAM,CAAyB;QAIlC,kBAAa,GAAb,aAAa,CAAgC;QAI7C,eAAU,GAAV,UAAU,CAA4B;QAItC,6BAAwB,GAAxB,wBAAwB,CAAiB;QACxC,qBAAgB,GAAhB,gBAAgB,CAItB;QAzEE,mBAAc,GAAG,CAAC,EAAU,EAAE,EAAE;YACpC,IAAI,CAAC,EAAE,EAAE;gBACL,OAAO;aACV;YACD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;YAC/B,MAAM,WAAW,GAAG,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC;YACvD,kBAAkB;YAClB,MAAM,IAAI,GAAa,EAAE,CAAC;YAC1B,IAAI,YAAY,CAAC,cAAc,CAAC,KAAK,KAAK,SAAS,IAAI,YAAY,CAAC,cAAc,CAAC,KAAK,KAAK,SAAS,EAAE;gBACpG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,EAAE,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;aACnF;YAED,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,IAAI,wBAAwB,CAAC,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QACpF,CAAC,CAAC;QAEM,gBAAW,GAAY,KAAK,CAAC;QAErC;;;WAGG;QACa,eAAU,GAEtB,EAAE,CAAC;QAEP;;WAEG;QACI,qBAAgB,GAAY,KAAK,CAAC;QACzC;;WAEG;QACI,4BAAuB,GAA8C,IAAI,UAAU,EAAE,CAAC;QA0CzF,4BAA4B;QAC5B,IAAI,MAAM,CAAC,UAAU,EAAE;YACnB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC/D;QACD,gCAAgC;IACpC,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QACxE,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBACxD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC1E;IACL,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,IAAmC;QAC7D,OAAO,IAAI,CAAC,eAAe,EAAE;aACxB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;aAChC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;IACxD,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,EAAU;QAC1B,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,IAAmC;QACzD,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;IACxD,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;IAC5D,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,SAAS;QAClB,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACvD,IAAI,aAAa,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACtD,oCAAoC;QACpC,IAAI,UAAU,EAAE;YACZ,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;SACjD;aAAM;YACH,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC9C;QACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,YAAY,GAAG,CAAC,MAAsB,EAAE,EAAE;gBAC5C,IAAI,UAAU,EAAE;oBACZ,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;iBACtC;qBAAM;oBACH,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;iBAC7B;gBACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACnD,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,CAAC;YACF,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,2BAA2B;gBAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;oBAC7C,OAAO,CAAC,CAAC,QAAQ,KAAK,aAAa,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,CAAC;gBAClF,CAAC,CAAC,CAAC;gBACH,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;oBACV,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;oBACzD,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;oBAC9B,OAAO;oBACP,gCAAgC;iBACnC;aACJ;YACD,WAAW,CAAC,UAAU,CAClB,EAAE,EACF,aAAa,CAAC,IAAI,EAClB,aAAa,CAAC,QAAQ,EACtB,IAAI,CAAC,KAAK,EACV,CAAC,MAAM,EAAE,EAAE;gBACP,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;wBACvB,GAAG,aAAa;wBAChB,MAAM;qBACT,CAAC,CAAC;iBACN;gBACD,YAAY,CAAC,MAAM,CAAC,CAAC;YACzB,CAAC,EACD,IAAI,EACJ,CAAC,MAAa,EAAE,OAAe,EAAE,EAAE;gBAC/B,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACpB,MAAM,CAAC,IAAI,CAAC,+CAA+C,IAAI,CAAC,SAAS,4BAA4B,aAAa,CAAC,IAAI,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACpJ,MAAM,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC,CACJ,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,OAAgB;QACrC,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;QACzF,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,KAAa,EAAE,QAAgB,EAAE,sBAA8B,CAAC;QACzE,IAAI,IAAI,CAAC,aAAa,CAAC,eAAe,IAAI,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,mBAAmB,CAAC,EAAE;YAC/F,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;SACzF;aAAM;YACH,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACjC;IACL,CAAC;IAED,sGAAsG;IAC5F,eAAe,CAAC,IAAkB,EAAE,IAAY;QACtD,OAAiC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC;IAED,qGAAqG;IAC3F,wBAAwB,CAAC,IAAkB,EAAE,IAAY;QAC/D,OAAiC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACtF,CAAC;IAED;;;;;OAKG;IACO,cAAc,CAAC,OAAiC,EAAE,SAAiB,EAAE,mBAA6B;QACxG,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YAC5D,OAAO;SACV;QAED,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,kBAAkB,EAAE;YACrH,OAAO;SACV;QAED,qEAAqE;QACrE,MAAM,SAAS,GAAG,mBAAmB,CAAC,CAAC,CAAC,SAAS,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;QAC1E,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAC/I,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACjH,CAAC;IAED;;;OAGG;IACH,gEAAgE;IACtD,WAAW,CAAC,OAAgB;QAClC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO;SACV;QACD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IA+BO,0BAA0B;QAC9B,OAAO;YACH,QAAQ,EAAE,iBAAiB;YAC3B,IAAI,EAAE,4CAA4C;SACrD,CAAC;IACN,CAAC;IAEO,qBAAqB,CAAC,MAAsB;QAChD,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAE7E,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACd,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;gBACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACjC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACjF,CAAC;CACJ", "sourcesContent": ["import type { IDisposable, Scene } from \"../../scene\";\r\nimport { WebXRControllerComponent } from \"./webXRControllerComponent\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport { SceneLoader } from \"../../Loading/sceneLoader\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Quaternion, Vector3 } from \"../../Maths/math.vector\";\r\nimport { Mesh } from \"../../Meshes/mesh\";\r\n\r\n/**\r\n * Handedness type in xrInput profiles. These can be used to define layouts in the Layout Map.\r\n */\r\nexport type MotionControllerHandedness = \"none\" | \"left\" | \"right\";\r\n/**\r\n * The type of components available in motion controllers.\r\n * This is not the name of the component.\r\n */\r\nexport type MotionControllerComponentType = \"trigger\" | \"squeeze\" | \"touchpad\" | \"thumbstick\" | \"button\";\r\n\r\n/**\r\n * The state of a controller component\r\n */\r\nexport type MotionControllerComponentStateType = \"default\" | \"touched\" | \"pressed\";\r\n\r\n/**\r\n * The schema of motion controller layout.\r\n * No object will be initialized using this interface\r\n * This is used just to define the profile.\r\n */\r\nexport interface IMotionControllerLayout {\r\n    /**\r\n     * Path to load the assets. Usually relative to the base path\r\n     */\r\n    assetPath: string;\r\n    /**\r\n     * Available components (unsorted)\r\n     */\r\n    components: {\r\n        /**\r\n         * A map of component Ids\r\n         */\r\n        [componentId: string]: {\r\n            /**\r\n             * The type of input the component outputs\r\n             */\r\n            type: MotionControllerComponentType;\r\n            /**\r\n             * The indices of this component in the gamepad object\r\n             */\r\n            gamepadIndices: {\r\n                /**\r\n                 * Index of button\r\n                 */\r\n                button?: number;\r\n                /**\r\n                 * If available, index of x-axis\r\n                 */\r\n                xAxis?: number;\r\n                /**\r\n                 * If available, index of y-axis\r\n                 */\r\n                yAxis?: number;\r\n            };\r\n            /**\r\n             * The mesh's root node name\r\n             */\r\n            rootNodeName: string;\r\n            /**\r\n             * Animation definitions for this model\r\n             */\r\n            visualResponses: {\r\n                [stateKey: string]: {\r\n                    /**\r\n                     * What property will be animated\r\n                     */\r\n                    componentProperty: \"xAxis\" | \"yAxis\" | \"button\" | \"state\";\r\n                    /**\r\n                     * What states influence this visual response\r\n                     */\r\n                    states: MotionControllerComponentStateType[];\r\n                    /**\r\n                     * Type of animation - movement or visibility\r\n                     */\r\n                    valueNodeProperty: \"transform\" | \"visibility\";\r\n                    /**\r\n                     * Base node name to move. Its position will be calculated according to the min and max nodes\r\n                     */\r\n                    valueNodeName?: string;\r\n                    /**\r\n                     * Minimum movement node\r\n                     */\r\n                    minNodeName?: string;\r\n                    /**\r\n                     * Max movement node\r\n                     */\r\n                    maxNodeName?: string;\r\n                };\r\n            };\r\n            /**\r\n             * If touch enabled, what is the name of node to display user feedback\r\n             */\r\n            touchPointNodeName?: string;\r\n        };\r\n    };\r\n    /**\r\n     * Is it xr standard mapping or not\r\n     */\r\n    gamepadMapping: \"\" | \"xr-standard\";\r\n    /**\r\n     * Base root node of this entire model\r\n     */\r\n    rootNodeName: string;\r\n    /**\r\n     * Defines the main button component id\r\n     */\r\n    selectComponentId: string;\r\n}\r\n\r\n/**\r\n * A definition for the layout map in the input profile\r\n */\r\nexport interface IMotionControllerLayoutMap {\r\n    /**\r\n     * Layouts with handedness type as a key\r\n     */\r\n    [handedness: string /* handedness */]: IMotionControllerLayout;\r\n}\r\n\r\n/**\r\n * The XR Input profile schema\r\n * Profiles can be found here:\r\n * https://github.com/immersive-web/webxr-input-profiles/tree/master/packages/registry/profiles\r\n */\r\nexport interface IMotionControllerProfile {\r\n    /**\r\n     * fallback profiles for this profileId\r\n     */\r\n    fallbackProfileIds: string[];\r\n    /**\r\n     * The layout map, with handedness as key\r\n     */\r\n    layouts: IMotionControllerLayoutMap;\r\n    /**\r\n     * The id of this profile\r\n     * correlates to the profile(s) in the xrInput.profiles array\r\n     */\r\n    profileId: string;\r\n}\r\n\r\n/**\r\n * A helper-interface for the 3 meshes needed for controller button animation\r\n * The meshes are provided to the _lerpButtonTransform function to calculate the current position of the value mesh\r\n */\r\nexport interface IMotionControllerButtonMeshMap {\r\n    /**\r\n     * the mesh that defines the pressed value mesh position.\r\n     * This is used to find the max-position of this button\r\n     */\r\n    pressedMesh: AbstractMesh;\r\n    /**\r\n     * the mesh that defines the unpressed value mesh position.\r\n     * This is used to find the min (or initial) position of this button\r\n     */\r\n    unpressedMesh: AbstractMesh;\r\n    /**\r\n     * The mesh that will be changed when value changes\r\n     */\r\n    valueMesh: AbstractMesh;\r\n}\r\n\r\n/**\r\n * A helper-interface for the 3 meshes needed for controller axis animation.\r\n * This will be expanded when touchpad animations are fully supported\r\n * The meshes are provided to the _lerpAxisTransform function to calculate the current position of the value mesh\r\n */\r\nexport interface IMotionControllerMeshMap {\r\n    /**\r\n     * the mesh that defines the maximum value mesh position.\r\n     */\r\n    maxMesh?: AbstractMesh;\r\n    /**\r\n     * the mesh that defines the minimum value mesh position.\r\n     */\r\n    minMesh?: AbstractMesh;\r\n    /**\r\n     * The mesh that will be changed when axis value changes\r\n     */\r\n    valueMesh?: AbstractMesh;\r\n}\r\n\r\n/**\r\n * The elements needed for change-detection of the gamepad objects in motion controllers\r\n */\r\nexport interface IMinimalMotionControllerObject {\r\n    /**\r\n     * Available axes of this controller\r\n     */\r\n    axes: number[];\r\n    /**\r\n     * An array of available buttons\r\n     */\r\n    buttons: Array<{\r\n        /**\r\n         * Value of the button/trigger\r\n         */\r\n        value: number;\r\n        /**\r\n         * If the button/trigger is currently touched\r\n         */\r\n        touched: boolean;\r\n        /**\r\n         * If the button/trigger is currently pressed\r\n         */\r\n        pressed: boolean;\r\n    }>;\r\n\r\n    /**\r\n     * EXPERIMENTAL haptic support.\r\n     */\r\n    hapticActuators?: Array<{\r\n        pulse: (value: number, duration: number) => Promise<boolean>;\r\n    }>;\r\n}\r\n\r\n/**\r\n * An Abstract Motion controller\r\n * This class receives an xrInput and a profile layout and uses those to initialize the components\r\n * Each component has an observable to check for changes in value and state\r\n */\r\nexport abstract class WebXRAbstractMotionController implements IDisposable {\r\n    private _initComponent = (id: string) => {\r\n        if (!id) {\r\n            return;\r\n        }\r\n        const componentDef = this.layout.components[id];\r\n        const type = componentDef.type;\r\n        const buttonIndex = componentDef.gamepadIndices.button;\r\n        // search for axes\r\n        const axes: number[] = [];\r\n        if (componentDef.gamepadIndices.xAxis !== undefined && componentDef.gamepadIndices.yAxis !== undefined) {\r\n            axes.push(componentDef.gamepadIndices.xAxis, componentDef.gamepadIndices.yAxis);\r\n        }\r\n\r\n        this.components[id] = new WebXRControllerComponent(id, type, buttonIndex, axes);\r\n    };\r\n\r\n    private _modelReady: boolean = false;\r\n\r\n    /**\r\n     * A map of components (WebXRControllerComponent) in this motion controller\r\n     * Components have a ComponentType and can also have both button and axis definitions\r\n     */\r\n    public readonly components: {\r\n        [id: string]: WebXRControllerComponent;\r\n    } = {};\r\n\r\n    /**\r\n     * Disable the model's animation. Can be set at any time.\r\n     */\r\n    public disableAnimation: boolean = false;\r\n    /**\r\n     * Observers registered here will be triggered when the model of this controller is done loading\r\n     */\r\n    public onModelLoadedObservable: Observable<WebXRAbstractMotionController> = new Observable();\r\n    /**\r\n     * The profile id of this motion controller\r\n     */\r\n    public abstract profileId: string;\r\n    /**\r\n     * The root mesh of the model. It is null if the model was not yet initialized\r\n     */\r\n    public rootMesh: Nullable<AbstractMesh>;\r\n\r\n    /**\r\n     * constructs a new abstract motion controller\r\n     * @param scene the scene to which the model of the controller will be added\r\n     * @param layout The profile layout to load\r\n     * @param gamepadObject The gamepad object correlating to this controller\r\n     * @param handedness handedness (left/right/none) of this controller\r\n     * @param _doNotLoadControllerMesh set this flag to ignore the mesh loading\r\n     * @param _controllerCache a cache holding controller models already loaded in this session\r\n     */\r\n    constructor(\r\n        // eslint-disable-next-line @typescript-eslint/naming-convention\r\n        protected scene: Scene,\r\n        // eslint-disable-next-line @typescript-eslint/naming-convention\r\n        protected layout: IMotionControllerLayout,\r\n        /**\r\n         * The gamepad object correlating to this controller\r\n         */\r\n        public gamepadObject: IMinimalMotionControllerObject,\r\n        /**\r\n         * handedness (left/right/none) of this controller\r\n         */\r\n        public handedness: MotionControllerHandedness,\r\n        /**\r\n         * @internal\r\n         */\r\n        public _doNotLoadControllerMesh: boolean = false,\r\n        private _controllerCache?: Array<{\r\n            filename: string;\r\n            path: string;\r\n            meshes: AbstractMesh[];\r\n        }>\r\n    ) {\r\n        // initialize the components\r\n        if (layout.components) {\r\n            Object.keys(layout.components).forEach(this._initComponent);\r\n        }\r\n        // Model is loaded in WebXRInput\r\n    }\r\n\r\n    /**\r\n     * Dispose this controller, the model mesh and all its components\r\n     */\r\n    public dispose(): void {\r\n        this.getComponentIds().forEach((id) => this.getComponent(id).dispose());\r\n        if (this.rootMesh) {\r\n            this.rootMesh.getChildren(undefined, true).forEach((node) => {\r\n                node.setEnabled(false);\r\n            });\r\n            this.rootMesh.dispose(!!this._controllerCache, !this._controllerCache);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns all components of specific type\r\n     * @param type the type to search for\r\n     * @returns an array of components with this type\r\n     */\r\n    public getAllComponentsOfType(type: MotionControllerComponentType): WebXRControllerComponent[] {\r\n        return this.getComponentIds()\r\n            .map((id) => this.components[id])\r\n            .filter((component) => component.type === type);\r\n    }\r\n\r\n    /**\r\n     * get a component based an its component id as defined in layout.components\r\n     * @param id the id of the component\r\n     * @returns the component correlates to the id or undefined if not found\r\n     */\r\n    public getComponent(id: string): WebXRControllerComponent {\r\n        return this.components[id];\r\n    }\r\n\r\n    /**\r\n     * Get the list of components available in this motion controller\r\n     * @returns an array of strings correlating to available components\r\n     */\r\n    public getComponentIds(): string[] {\r\n        return Object.keys(this.components);\r\n    }\r\n\r\n    /**\r\n     * Get the first component of specific type\r\n     * @param type type of component to find\r\n     * @returns a controller component or null if not found\r\n     */\r\n    public getComponentOfType(type: MotionControllerComponentType): Nullable<WebXRControllerComponent> {\r\n        return this.getAllComponentsOfType(type)[0] || null;\r\n    }\r\n\r\n    /**\r\n     * Get the main (Select) component of this controller as defined in the layout\r\n     * @returns the main component of this controller\r\n     */\r\n    public getMainComponent(): WebXRControllerComponent {\r\n        return this.getComponent(this.layout.selectComponentId);\r\n    }\r\n\r\n    /**\r\n     * Loads the model correlating to this controller\r\n     * When the mesh is loaded, the onModelLoadedObservable will be triggered\r\n     * @returns A promise fulfilled with the result of the model loading\r\n     */\r\n    public async loadModel(): Promise<boolean> {\r\n        const useGeneric = !this._getModelLoadingConstraints();\r\n        let loadingParams = this._getGenericFilenameAndPath();\r\n        // Checking if GLB loader is present\r\n        if (useGeneric) {\r\n            Logger.Warn(\"Falling back to generic models\");\r\n        } else {\r\n            loadingParams = this._getFilenameAndPath();\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n            const meshesLoaded = (meshes: AbstractMesh[]) => {\r\n                if (useGeneric) {\r\n                    this._getGenericParentMesh(meshes);\r\n                } else {\r\n                    this._setRootMesh(meshes);\r\n                }\r\n                this._processLoadedModel(meshes);\r\n                this._modelReady = true;\r\n                this.onModelLoadedObservable.notifyObservers(this);\r\n                resolve(true);\r\n            };\r\n            if (this._controllerCache) {\r\n                // look for it in the cache\r\n                const found = this._controllerCache.filter((c) => {\r\n                    return c.filename === loadingParams.filename && c.path === loadingParams.path;\r\n                });\r\n                if (found[0]) {\r\n                    found[0].meshes.forEach((mesh) => mesh.setEnabled(true));\r\n                    meshesLoaded(found[0].meshes);\r\n                    return;\r\n                    // found, don't continue to load\r\n                }\r\n            }\r\n            SceneLoader.ImportMesh(\r\n                \"\",\r\n                loadingParams.path,\r\n                loadingParams.filename,\r\n                this.scene,\r\n                (meshes) => {\r\n                    if (this._controllerCache) {\r\n                        this._controllerCache.push({\r\n                            ...loadingParams,\r\n                            meshes,\r\n                        });\r\n                    }\r\n                    meshesLoaded(meshes);\r\n                },\r\n                null,\r\n                (_scene: Scene, message: string) => {\r\n                    Logger.Log(message);\r\n                    Logger.Warn(`Failed to retrieve controller model of type ${this.profileId} from the remote server: ${loadingParams.path}${loadingParams.filename}`);\r\n                    reject(message);\r\n                }\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Update this model using the current XRFrame\r\n     * @param xrFrame the current xr frame to use and update the model\r\n     */\r\n    public updateFromXRFrame(xrFrame: XRFrame): void {\r\n        this.getComponentIds().forEach((id) => this.getComponent(id).update(this.gamepadObject));\r\n        this.updateModel(xrFrame);\r\n    }\r\n\r\n    /**\r\n     * Backwards compatibility due to a deeply-integrated typo\r\n     */\r\n    public get handness() {\r\n        return this.handedness;\r\n    }\r\n\r\n    /**\r\n     * Pulse (vibrate) this controller\r\n     * If the controller does not support pulses, this function will fail silently and return Promise<false> directly after called\r\n     * Consecutive calls to this function will cancel the last pulse call\r\n     *\r\n     * @param value the strength of the pulse in 0.0...1.0 range\r\n     * @param duration Duration of the pulse in milliseconds\r\n     * @param hapticActuatorIndex optional index of actuator (will usually be 0)\r\n     * @returns a promise that will send true when the pulse has ended and false if the device doesn't support pulse or an error accrued\r\n     */\r\n    public pulse(value: number, duration: number, hapticActuatorIndex: number = 0): Promise<boolean> {\r\n        if (this.gamepadObject.hapticActuators && this.gamepadObject.hapticActuators[hapticActuatorIndex]) {\r\n            return this.gamepadObject.hapticActuators[hapticActuatorIndex].pulse(value, duration);\r\n        } else {\r\n            return Promise.resolve(false);\r\n        }\r\n    }\r\n\r\n    // Look through all children recursively. This will return null if no mesh exists with the given name.\r\n    protected _getChildByName(node: AbstractMesh, name: string): AbstractMesh | undefined {\r\n        return <AbstractMesh | undefined>node.getChildren((n) => n.name === name, false)[0];\r\n    }\r\n\r\n    // Look through only immediate children. This will return null if no mesh exists with the given name.\r\n    protected _getImmediateChildByName(node: AbstractMesh, name: string): AbstractMesh | undefined {\r\n        return <AbstractMesh | undefined>node.getChildren((n) => n.name == name, true)[0];\r\n    }\r\n\r\n    /**\r\n     * Moves the axis on the controller mesh based on its current state\r\n     * @param axisMap\r\n     * @param axisValue the value of the axis which determines the meshes new position\r\n     * @internal\r\n     */\r\n    protected _lerpTransform(axisMap: IMotionControllerMeshMap, axisValue: number, fixValueCoordinates?: boolean): void {\r\n        if (!axisMap.minMesh || !axisMap.maxMesh || !axisMap.valueMesh) {\r\n            return;\r\n        }\r\n\r\n        if (!axisMap.minMesh.rotationQuaternion || !axisMap.maxMesh.rotationQuaternion || !axisMap.valueMesh.rotationQuaternion) {\r\n            return;\r\n        }\r\n\r\n        // Convert from gamepad value range (-1 to +1) to lerp range (0 to 1)\r\n        const lerpValue = fixValueCoordinates ? axisValue * 0.5 + 0.5 : axisValue;\r\n        Quaternion.SlerpToRef(axisMap.minMesh.rotationQuaternion, axisMap.maxMesh.rotationQuaternion, lerpValue, axisMap.valueMesh.rotationQuaternion);\r\n        Vector3.LerpToRef(axisMap.minMesh.position, axisMap.maxMesh.position, lerpValue, axisMap.valueMesh.position);\r\n    }\r\n\r\n    /**\r\n     * Update the model itself with the current frame data\r\n     * @param xrFrame the frame to use for updating the model mesh\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected updateModel(xrFrame: XRFrame): void {\r\n        if (!this._modelReady) {\r\n            return;\r\n        }\r\n        this._updateModel(xrFrame);\r\n    }\r\n\r\n    /**\r\n     * Get the filename and path for this controller's model\r\n     * @returns a map of filename and path\r\n     */\r\n    protected abstract _getFilenameAndPath(): { filename: string; path: string };\r\n    /**\r\n     * This function is called before the mesh is loaded. It checks for loading constraints.\r\n     * For example, this function can check if the GLB loader is available\r\n     * If this function returns false, the generic controller will be loaded instead\r\n     * @returns Is the client ready to load the mesh\r\n     */\r\n    protected abstract _getModelLoadingConstraints(): boolean;\r\n    /**\r\n     * This function will be called after the model was successfully loaded and can be used\r\n     * for mesh transformations before it is available for the user\r\n     * @param meshes the loaded meshes\r\n     */\r\n    protected abstract _processLoadedModel(meshes: AbstractMesh[]): void;\r\n    /**\r\n     * Set the root mesh for this controller. Important for the WebXR controller class\r\n     * @param meshes the loaded meshes\r\n     */\r\n    protected abstract _setRootMesh(meshes: AbstractMesh[]): void;\r\n    /**\r\n     * A function executed each frame that updates the mesh (if needed)\r\n     * @param xrFrame the current xrFrame\r\n     */\r\n    protected abstract _updateModel(xrFrame: XRFrame): void;\r\n\r\n    private _getGenericFilenameAndPath(): { filename: string; path: string } {\r\n        return {\r\n            filename: \"generic.babylon\",\r\n            path: \"https://controllers.babylonjs.com/generic/\",\r\n        };\r\n    }\r\n\r\n    private _getGenericParentMesh(meshes: AbstractMesh[]): void {\r\n        this.rootMesh = new Mesh(this.profileId + \" \" + this.handedness, this.scene);\r\n\r\n        meshes.forEach((mesh) => {\r\n            if (!mesh.parent) {\r\n                mesh.isPickable = false;\r\n                mesh.setParent(this.rootMesh);\r\n            }\r\n        });\r\n\r\n        this.rootMesh.rotationQuaternion = Quaternion.FromEulerAngles(0, Math.PI, 0);\r\n    }\r\n}\r\n"]}