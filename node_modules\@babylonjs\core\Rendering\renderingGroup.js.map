{"version": 3, "file": "renderingGroup.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Rendering/renderingGroup.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,qBAAqB,EAAE,MAAM,oBAAoB,CAAC;AAIvE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAI/C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAKjD;;;;;GAKG;AACH,MAAM,OAAO,cAAc;IA0BvB;;;OAGG;IACH,IAAW,mBAAmB,CAAC,KAAmD;QAC9E,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;SACrC;aAAM;YACH,IAAI,CAAC,oBAAoB,GAAG,cAAc,CAAC,kBAAkB,CAAC;SACjE;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC;IAClD,CAAC;IAED;;;OAGG;IACH,IAAW,sBAAsB,CAAC,KAAmD;QACjF,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;SACxC;aAAM;YACH,IAAI,CAAC,uBAAuB,GAAG,cAAc,CAAC,kBAAkB,CAAC;SACpE;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC;IACxD,CAAC;IAED;;;OAGG;IACH,IAAW,wBAAwB,CAAC,KAAmD;QACnF,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;SAC1C;aAAM;YACH,IAAI,CAAC,yBAAyB,GAAG,cAAc,CAAC,6BAA6B,CAAC;SACjF;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,CAAC;IAC5D,CAAC;IAED;;;;;;;OAOG;IACH,YACW,KAAa,EACpB,KAAY,EACZ,sBAAoE,IAAI,EACxE,yBAAuE,IAAI,EAC3E,2BAAyE,IAAI;QAJtE,UAAK,GAAL,KAAK,CAAQ;QAvEhB,qBAAgB,GAAG,IAAI,UAAU,CAAU,GAAG,CAAC,CAAC;QAChD,0BAAqB,GAAG,IAAI,UAAU,CAAU,GAAG,CAAC,CAAC;QACrD,wBAAmB,GAAG,IAAI,UAAU,CAAU,GAAG,CAAC,CAAC;QACnD,wBAAmB,GAAG,IAAI,UAAU,CAAU,GAAG,CAAC,CAAC;QACnD,qBAAgB,GAAG,IAAI,UAAU,CAAkB,GAAG,CAAC,CAAC;QACxD,oBAAe,GAAG,IAAI,UAAU,CAAiB,GAAG,CAAC,CAAC;QAU9D,gBAAgB;QACT,WAAM,GAAG,IAAI,CAAC;QAErB,gBAAgB;QACT,oBAAe,GAAG,IAAI,qBAAqB,CAAiB,EAAE,CAAC,CAAC;QA0DnE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;QACrD,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;IAC7D,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CACT,oBAOC,EACD,aAAsB,EACtB,eAAwB,EACxB,YAAsC;QAEtC,IAAI,oBAAoB,EAAE;YACtB,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC5H,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,aAAa;QACb,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;YACvC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAChD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAC9B;QAED,SAAS;QACT,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACpC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC7C;QAED,aAAa;QACb,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;YACvC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACnD;QAED,MAAM,YAAY,GAAG,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAC/C,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE/B,UAAU;QACV,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;QAED,YAAY;QACZ,IAAI,eAAe,EAAE;YACjB,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;SACvC;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAI,CAAC,4BAA4B,EAAE,CAAC;SACvC;QAED,cAAc;QACd,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,+BAA+B,EAAE;YACxF,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YACtC,IAAI,IAAI,CAAC,MAAM,CAAC,+BAA+B,EAAE;gBAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAC5F,IAAI,cAAc,CAAC,MAAM,EAAE;oBACvB,sEAAsE;oBACtE,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;iBAC3C;aACJ;iBAAM;gBACH,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;aACvD;YACD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;SAChD;QAED,yEAAyE;QACzE,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE/B,QAAQ;QACR,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;YAC7B,KAAK,IAAI,kBAAkB,GAAG,CAAC,EAAE,kBAAkB,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,kBAAkB,EAAE,EAAE;gBACrG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAM,EAAE,CAAC;aAC1D;YAED,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;SAChD;QAED,yBAAyB;QACzB,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACK,mBAAmB,CAAC,SAA8B;QACtD,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IACxG,CAAC;IAED;;;OAGG;IACK,sBAAsB,CAAC,SAA8B;QACzD,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IAC3G,CAAC;IAED;;;OAGG;IACK,wBAAwB,CAAC,SAA8B;QAC3D,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,yBAAyB,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAC5G,CAAC;IAED;;;;;;OAMG;IACK,MAAM,CAAC,aAAa,CACxB,SAA8B,EAC9B,aAA2D,EAC3D,MAAwB,EACxB,WAAoB;QAEpB,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,OAAgB,CAAC;QACrB,MAAM,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC;QAEnF,IAAI,WAAW,EAAE;YACb,OAAO,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;gBAC5C,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACnC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;gBACnD,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,cAAc,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;aACtH;SACJ;QAED,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAE5H,IAAI,aAAa,EAAE;YACf,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACnC;QAED,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC;QAClD,KAAK,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;YAC1D,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;YAEhC,IAAI,KAAK,CAAC,kCAAkC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;gBACxF,SAAS;aACZ;YAED,IAAI,WAAW,EAAE;gBACb,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBAEvC,IAAI,QAAQ,IAAI,QAAQ,CAAC,gBAAgB,EAAE;oBACvC,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC;oBAC/C,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;oBAC5B,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;oBAC7C,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACtB,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;iBAC9B;aACJ;YAED,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;SAC/B;IACL,CAAC;IAED;;;;;;;OAOG;IACH,gEAAgE;IACzD,MAAM,CAAC,6BAA6B,CAAC,CAAU,EAAE,CAAU;QAC9D,oBAAoB;QACpB,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,EAAE;YAC/B,OAAO,CAAC,CAAC;SACZ;QACD,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,EAAE;YAC/B,OAAO,CAAC,CAAC,CAAC;SACb;QAED,0BAA0B;QAC1B,OAAO,cAAc,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;;OAOG;IACH,gEAAgE;IACzD,MAAM,CAAC,sBAAsB,CAAC,CAAU,EAAE,CAAU;QACvD,0BAA0B;QAC1B,IAAI,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,EAAE;YAC3C,OAAO,CAAC,CAAC;SACZ;QACD,IAAI,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,EAAE;YAC3C,OAAO,CAAC,CAAC,CAAC;SACb;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;;;;OAOG;IACH,gEAAgE;IACzD,MAAM,CAAC,sBAAsB,CAAC,CAAU,EAAE,CAAU;QACvD,0BAA0B;QAC1B,IAAI,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,EAAE;YAC3C,OAAO,CAAC,CAAC,CAAC;SACb;QACD,IAAI,CAAC,CAAC,iBAAiB,GAAG,CAAC,CAAC,iBAAiB,EAAE;YAC3C,OAAO,CAAC,CAAC;SACZ;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,kBAAkB,CAAC,CAAU,EAAE,CAAU;QACnD,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;QAE1B,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE;YAClC,OAAO,KAAK,CAAC,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC;SAC5D;QAED,OAAO,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,cAAc;QACjB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IACjC,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;QACrC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;QACnC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;QACnC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QAC/B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,OAAgB,EAAE,IAAmB,EAAE,QAA6B;QAChF,yCAAyC;QACzC,IAAI,IAAI,KAAK,SAAS,EAAE;YACpB,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QACD,IAAI,QAAQ,KAAK,SAAS,EAAE;YACxB,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;SACpC;QAED,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC7C,OAAO;SACV;QAED,IAAI,QAAQ,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE;YACzC,cAAc;YACd,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC5C;aAAM,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE;YACpC,aAAa;YACb,IAAI,QAAQ,CAAC,gBAAgB,EAAE;gBAC3B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAC1C;YAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC1C;aAAM;YACH,IAAI,QAAQ,CAAC,gBAAgB,EAAE;gBAC3B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAC1C;YAED,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;SACjD;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE;YACtD,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC7D;QAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAEM,eAAe,CAAC,aAA6B;QAChD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAEM,iBAAiB,CAAC,cAA+B;QACpD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC3C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAEO,gBAAgB,CAAC,YAAsC;QAC3D,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACpC,OAAO;SACV;QAED,YAAY;QACZ,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAC9C,IAAI,CAAC,MAAM,CAAC,oCAAoC,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9E,KAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,aAAa,EAAE,EAAE;YACvF,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEjE,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gBAC3E,SAAS;aACZ;YAED,MAAM,OAAO,GAAQ,cAAc,CAAC,OAAO,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC5E,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;aACzE;SACJ;QACD,IAAI,CAAC,MAAM,CAAC,mCAAmC,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjF,CAAC;IAEO,cAAc;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;YAClE,OAAO;SACV;QAED,UAAU;QACV,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAC9C,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5E,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;YACrD,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEpD,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gBAC1E,aAAa,CAAC,MAAM,EAAE,CAAC;aAC1B;SACJ;QACD,IAAI,CAAC,MAAM,CAAC,iCAAiC,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC/E,CAAC;;AAldc,0BAAW,GAA2B,OAAO,CAAC,IAAI,EAAE,AAAzC,CAA0C", "sourcesContent": ["import { SmartArray, SmartArrayNoDuplicate } from \"../Misc/smartArray\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Nullable, DeepImmutable } from \"../types\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { IParticleSystem } from \"../Particles/IParticleSystem\";\r\nimport type { IEdgesRenderer } from \"./edgesRenderer\";\r\nimport type { ISpriteManager } from \"../Sprites/spriteManager\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { Material } from \"../Materials/material\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\n\r\n/**\r\n * This represents the object necessary to create a rendering group.\r\n * This is exclusively used and created by the rendering manager.\r\n * To modify the behavior, you use the available helpers in your scene or meshes.\r\n * @internal\r\n */\r\nexport class RenderingGroup {\r\n    private static _ZeroVector: DeepImmutable<Vector3> = Vector3.Zero();\r\n    private _scene: Scene;\r\n    private _opaqueSubMeshes = new SmartArray<SubMesh>(256);\r\n    private _transparentSubMeshes = new SmartArray<SubMesh>(256);\r\n    private _alphaTestSubMeshes = new SmartArray<SubMesh>(256);\r\n    private _depthOnlySubMeshes = new SmartArray<SubMesh>(256);\r\n    private _particleSystems = new SmartArray<IParticleSystem>(256);\r\n    private _spriteManagers = new SmartArray<ISpriteManager>(256);\r\n\r\n    private _opaqueSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number>;\r\n    private _alphaTestSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number>;\r\n    private _transparentSortCompareFn: (a: SubMesh, b: SubMesh) => number;\r\n\r\n    private _renderOpaque: (subMeshes: SmartArray<SubMesh>) => void;\r\n    private _renderAlphaTest: (subMeshes: SmartArray<SubMesh>) => void;\r\n    private _renderTransparent: (subMeshes: SmartArray<SubMesh>) => void;\r\n\r\n    /** @internal */\r\n    public _empty = true;\r\n\r\n    /** @internal */\r\n    public _edgesRenderers = new SmartArrayNoDuplicate<IEdgesRenderer>(16);\r\n\r\n    public onBeforeTransparentRendering: () => void;\r\n\r\n    /**\r\n     * Set the opaque sort comparison function.\r\n     * If null the sub meshes will be render in the order they were created\r\n     */\r\n    public set opaqueSortCompareFn(value: Nullable<(a: SubMesh, b: SubMesh) => number>) {\r\n        if (value) {\r\n            this._opaqueSortCompareFn = value;\r\n        } else {\r\n            this._opaqueSortCompareFn = RenderingGroup.PainterSortCompare;\r\n        }\r\n        this._renderOpaque = this._renderOpaqueSorted;\r\n    }\r\n\r\n    /**\r\n     * Set the alpha test sort comparison function.\r\n     * If null the sub meshes will be render in the order they were created\r\n     */\r\n    public set alphaTestSortCompareFn(value: Nullable<(a: SubMesh, b: SubMesh) => number>) {\r\n        if (value) {\r\n            this._alphaTestSortCompareFn = value;\r\n        } else {\r\n            this._alphaTestSortCompareFn = RenderingGroup.PainterSortCompare;\r\n        }\r\n        this._renderAlphaTest = this._renderAlphaTestSorted;\r\n    }\r\n\r\n    /**\r\n     * Set the transparent sort comparison function.\r\n     * If null the sub meshes will be render in the order they were created\r\n     */\r\n    public set transparentSortCompareFn(value: Nullable<(a: SubMesh, b: SubMesh) => number>) {\r\n        if (value) {\r\n            this._transparentSortCompareFn = value;\r\n        } else {\r\n            this._transparentSortCompareFn = RenderingGroup.defaultTransparentSortCompare;\r\n        }\r\n        this._renderTransparent = this._renderTransparentSorted;\r\n    }\r\n\r\n    /**\r\n     * Creates a new rendering group.\r\n     * @param index The rendering group index\r\n     * @param scene\r\n     * @param opaqueSortCompareFn The opaque sort comparison function. If null no order is applied\r\n     * @param alphaTestSortCompareFn The alpha test sort comparison function. If null no order is applied\r\n     * @param transparentSortCompareFn The transparent sort comparison function. If null back to front + alpha index sort is applied\r\n     */\r\n    constructor(\r\n        public index: number,\r\n        scene: Scene,\r\n        opaqueSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null,\r\n        alphaTestSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null,\r\n        transparentSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null\r\n    ) {\r\n        this._scene = scene;\r\n\r\n        this.opaqueSortCompareFn = opaqueSortCompareFn;\r\n        this.alphaTestSortCompareFn = alphaTestSortCompareFn;\r\n        this.transparentSortCompareFn = transparentSortCompareFn;\r\n    }\r\n\r\n    /**\r\n     * Render all the sub meshes contained in the group.\r\n     * @param customRenderFunction Used to override the default render behaviour of the group.\r\n     * @param renderSprites\r\n     * @param renderParticles\r\n     * @param activeMeshes\r\n     */\r\n    public render(\r\n        customRenderFunction: Nullable<\r\n            (\r\n                opaqueSubMeshes: SmartArray<SubMesh>,\r\n                transparentSubMeshes: SmartArray<SubMesh>,\r\n                alphaTestSubMeshes: SmartArray<SubMesh>,\r\n                depthOnlySubMeshes: SmartArray<SubMesh>\r\n            ) => void\r\n        >,\r\n        renderSprites: boolean,\r\n        renderParticles: boolean,\r\n        activeMeshes: Nullable<AbstractMesh[]>\r\n    ): void {\r\n        if (customRenderFunction) {\r\n            customRenderFunction(this._opaqueSubMeshes, this._alphaTestSubMeshes, this._transparentSubMeshes, this._depthOnlySubMeshes);\r\n            return;\r\n        }\r\n\r\n        const engine = this._scene.getEngine();\r\n\r\n        // Depth only\r\n        if (this._depthOnlySubMeshes.length !== 0) {\r\n            engine.setColorWrite(false);\r\n            this._renderAlphaTest(this._depthOnlySubMeshes);\r\n            engine.setColorWrite(true);\r\n        }\r\n\r\n        // Opaque\r\n        if (this._opaqueSubMeshes.length !== 0) {\r\n            this._renderOpaque(this._opaqueSubMeshes);\r\n        }\r\n\r\n        // Alpha test\r\n        if (this._alphaTestSubMeshes.length !== 0) {\r\n            this._renderAlphaTest(this._alphaTestSubMeshes);\r\n        }\r\n\r\n        const stencilState = engine.getStencilBuffer();\r\n        engine.setStencilBuffer(false);\r\n\r\n        // Sprites\r\n        if (renderSprites) {\r\n            this._renderSprites();\r\n        }\r\n\r\n        // Particles\r\n        if (renderParticles) {\r\n            this._renderParticles(activeMeshes);\r\n        }\r\n\r\n        if (this.onBeforeTransparentRendering) {\r\n            this.onBeforeTransparentRendering();\r\n        }\r\n\r\n        // Transparent\r\n        if (this._transparentSubMeshes.length !== 0 || this._scene.useOrderIndependentTransparency) {\r\n            engine.setStencilBuffer(stencilState);\r\n            if (this._scene.useOrderIndependentTransparency) {\r\n                const excludedMeshes = this._scene.depthPeelingRenderer!.render(this._transparentSubMeshes);\r\n                if (excludedMeshes.length) {\r\n                    // Render leftover meshes that could not be processed by depth peeling\r\n                    this._renderTransparent(excludedMeshes);\r\n                }\r\n            } else {\r\n                this._renderTransparent(this._transparentSubMeshes);\r\n            }\r\n            engine.setAlphaMode(Constants.ALPHA_DISABLE);\r\n        }\r\n\r\n        // Set back stencil to false in case it changes before the edge renderer.\r\n        engine.setStencilBuffer(false);\r\n\r\n        // Edges\r\n        if (this._edgesRenderers.length) {\r\n            for (let edgesRendererIndex = 0; edgesRendererIndex < this._edgesRenderers.length; edgesRendererIndex++) {\r\n                this._edgesRenderers.data[edgesRendererIndex].render();\r\n            }\r\n\r\n            engine.setAlphaMode(Constants.ALPHA_DISABLE);\r\n        }\r\n\r\n        // Restore Stencil state.\r\n        engine.setStencilBuffer(stencilState);\r\n    }\r\n\r\n    /**\r\n     * Renders the opaque submeshes in the order from the opaqueSortCompareFn.\r\n     * @param subMeshes The submeshes to render\r\n     */\r\n    private _renderOpaqueSorted(subMeshes: SmartArray<SubMesh>): void {\r\n        RenderingGroup._RenderSorted(subMeshes, this._opaqueSortCompareFn, this._scene.activeCamera, false);\r\n    }\r\n\r\n    /**\r\n     * Renders the opaque submeshes in the order from the alphatestSortCompareFn.\r\n     * @param subMeshes The submeshes to render\r\n     */\r\n    private _renderAlphaTestSorted(subMeshes: SmartArray<SubMesh>): void {\r\n        RenderingGroup._RenderSorted(subMeshes, this._alphaTestSortCompareFn, this._scene.activeCamera, false);\r\n    }\r\n\r\n    /**\r\n     * Renders the opaque submeshes in the order from the transparentSortCompareFn.\r\n     * @param subMeshes The submeshes to render\r\n     */\r\n    private _renderTransparentSorted(subMeshes: SmartArray<SubMesh>): void {\r\n        RenderingGroup._RenderSorted(subMeshes, this._transparentSortCompareFn, this._scene.activeCamera, true);\r\n    }\r\n\r\n    /**\r\n     * Renders the submeshes in a specified order.\r\n     * @param subMeshes The submeshes to sort before render\r\n     * @param sortCompareFn The comparison function use to sort\r\n     * @param camera The camera position use to preprocess the submeshes to help sorting\r\n     * @param transparent Specifies to activate blending if true\r\n     */\r\n    private static _RenderSorted(\r\n        subMeshes: SmartArray<SubMesh>,\r\n        sortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number>,\r\n        camera: Nullable<Camera>,\r\n        transparent: boolean\r\n    ): void {\r\n        let subIndex = 0;\r\n        let subMesh: SubMesh;\r\n        const cameraPosition = camera ? camera.globalPosition : RenderingGroup._ZeroVector;\r\n\r\n        if (transparent) {\r\n            for (; subIndex < subMeshes.length; subIndex++) {\r\n                subMesh = subMeshes.data[subIndex];\r\n                subMesh._alphaIndex = subMesh.getMesh().alphaIndex;\r\n                subMesh._distanceToCamera = Vector3.Distance(subMesh.getBoundingInfo().boundingSphere.centerWorld, cameraPosition);\r\n            }\r\n        }\r\n\r\n        const sortedArray = subMeshes.length === subMeshes.data.length ? subMeshes.data : subMeshes.data.slice(0, subMeshes.length);\r\n\r\n        if (sortCompareFn) {\r\n            sortedArray.sort(sortCompareFn);\r\n        }\r\n\r\n        const scene = sortedArray[0].getMesh().getScene();\r\n        for (subIndex = 0; subIndex < sortedArray.length; subIndex++) {\r\n            subMesh = sortedArray[subIndex];\r\n\r\n            if (scene._activeMeshesFrozenButKeepClipping && !subMesh.isInFrustum(scene._frustumPlanes)) {\r\n                continue;\r\n            }\r\n\r\n            if (transparent) {\r\n                const material = subMesh.getMaterial();\r\n\r\n                if (material && material.needDepthPrePass) {\r\n                    const engine = material.getScene().getEngine();\r\n                    engine.setColorWrite(false);\r\n                    engine.setAlphaMode(Constants.ALPHA_DISABLE);\r\n                    subMesh.render(false);\r\n                    engine.setColorWrite(true);\r\n                }\r\n            }\r\n\r\n            subMesh.render(transparent);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Build in function which can be applied to ensure meshes of a special queue (opaque, alpha test, transparent)\r\n     * are rendered back to front if in the same alpha index.\r\n     *\r\n     * @param a The first submesh\r\n     * @param b The second submesh\r\n     * @returns The result of the comparison\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static defaultTransparentSortCompare(a: SubMesh, b: SubMesh): number {\r\n        // Alpha index first\r\n        if (a._alphaIndex > b._alphaIndex) {\r\n            return 1;\r\n        }\r\n        if (a._alphaIndex < b._alphaIndex) {\r\n            return -1;\r\n        }\r\n\r\n        // Then distance to camera\r\n        return RenderingGroup.backToFrontSortCompare(a, b);\r\n    }\r\n\r\n    /**\r\n     * Build in function which can be applied to ensure meshes of a special queue (opaque, alpha test, transparent)\r\n     * are rendered back to front.\r\n     *\r\n     * @param a The first submesh\r\n     * @param b The second submesh\r\n     * @returns The result of the comparison\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static backToFrontSortCompare(a: SubMesh, b: SubMesh): number {\r\n        // Then distance to camera\r\n        if (a._distanceToCamera < b._distanceToCamera) {\r\n            return 1;\r\n        }\r\n        if (a._distanceToCamera > b._distanceToCamera) {\r\n            return -1;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Build in function which can be applied to ensure meshes of a special queue (opaque, alpha test, transparent)\r\n     * are rendered front to back (prevent overdraw).\r\n     *\r\n     * @param a The first submesh\r\n     * @param b The second submesh\r\n     * @returns The result of the comparison\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static frontToBackSortCompare(a: SubMesh, b: SubMesh): number {\r\n        // Then distance to camera\r\n        if (a._distanceToCamera < b._distanceToCamera) {\r\n            return -1;\r\n        }\r\n        if (a._distanceToCamera > b._distanceToCamera) {\r\n            return 1;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Build in function which can be applied to ensure meshes of a special queue (opaque, alpha test, transparent)\r\n     * are grouped by material then geometry.\r\n     *\r\n     * @param a The first submesh\r\n     * @param b The second submesh\r\n     * @returns The result of the comparison\r\n     */\r\n    public static PainterSortCompare(a: SubMesh, b: SubMesh): number {\r\n        const meshA = a.getMesh();\r\n        const meshB = b.getMesh();\r\n\r\n        if (meshA.material && meshB.material) {\r\n            return meshA.material.uniqueId - meshB.material.uniqueId;\r\n        }\r\n\r\n        return meshA.uniqueId - meshB.uniqueId;\r\n    }\r\n\r\n    /**\r\n     * Resets the different lists of submeshes to prepare a new frame.\r\n     */\r\n    public prepare(): void {\r\n        this._opaqueSubMeshes.reset();\r\n        this._transparentSubMeshes.reset();\r\n        this._alphaTestSubMeshes.reset();\r\n        this._depthOnlySubMeshes.reset();\r\n        this._particleSystems.reset();\r\n        this.prepareSprites();\r\n        this._edgesRenderers.reset();\r\n        this._empty = true;\r\n    }\r\n\r\n    /**\r\n     * Resets the different lists of sprites to prepare a new frame.\r\n     */\r\n    public prepareSprites(): void {\r\n        this._spriteManagers.reset();\r\n    }\r\n\r\n    public dispose(): void {\r\n        this._opaqueSubMeshes.dispose();\r\n        this._transparentSubMeshes.dispose();\r\n        this._alphaTestSubMeshes.dispose();\r\n        this._depthOnlySubMeshes.dispose();\r\n        this._particleSystems.dispose();\r\n        this._spriteManagers.dispose();\r\n        this._edgesRenderers.dispose();\r\n    }\r\n\r\n    /**\r\n     * Inserts the submesh in its correct queue depending on its material.\r\n     * @param subMesh The submesh to dispatch\r\n     * @param [mesh] Optional reference to the submeshes's mesh. Provide if you have an exiting reference to improve performance.\r\n     * @param [material] Optional reference to the submeshes's material. Provide if you have an exiting reference to improve performance.\r\n     */\r\n    public dispatch(subMesh: SubMesh, mesh?: AbstractMesh, material?: Nullable<Material>): void {\r\n        // Get mesh and materials if not provided\r\n        if (mesh === undefined) {\r\n            mesh = subMesh.getMesh();\r\n        }\r\n        if (material === undefined) {\r\n            material = subMesh.getMaterial();\r\n        }\r\n\r\n        if (material === null || material === undefined) {\r\n            return;\r\n        }\r\n\r\n        if (material.needAlphaBlendingForMesh(mesh)) {\r\n            // Transparent\r\n            this._transparentSubMeshes.push(subMesh);\r\n        } else if (material.needAlphaTesting()) {\r\n            // Alpha test\r\n            if (material.needDepthPrePass) {\r\n                this._depthOnlySubMeshes.push(subMesh);\r\n            }\r\n\r\n            this._alphaTestSubMeshes.push(subMesh);\r\n        } else {\r\n            if (material.needDepthPrePass) {\r\n                this._depthOnlySubMeshes.push(subMesh);\r\n            }\r\n\r\n            this._opaqueSubMeshes.push(subMesh); // Opaque\r\n        }\r\n\r\n        mesh._renderingGroup = this;\r\n\r\n        if (mesh._edgesRenderer && mesh._edgesRenderer.isEnabled) {\r\n            this._edgesRenderers.pushNoDuplicate(mesh._edgesRenderer);\r\n        }\r\n\r\n        this._empty = false;\r\n    }\r\n\r\n    public dispatchSprites(spriteManager: ISpriteManager) {\r\n        this._spriteManagers.push(spriteManager);\r\n        this._empty = false;\r\n    }\r\n\r\n    public dispatchParticles(particleSystem: IParticleSystem) {\r\n        this._particleSystems.push(particleSystem);\r\n        this._empty = false;\r\n    }\r\n\r\n    private _renderParticles(activeMeshes: Nullable<AbstractMesh[]>): void {\r\n        if (this._particleSystems.length === 0) {\r\n            return;\r\n        }\r\n\r\n        // Particles\r\n        const activeCamera = this._scene.activeCamera;\r\n        this._scene.onBeforeParticlesRenderingObservable.notifyObservers(this._scene);\r\n        for (let particleIndex = 0; particleIndex < this._particleSystems.length; particleIndex++) {\r\n            const particleSystem = this._particleSystems.data[particleIndex];\r\n\r\n            if ((activeCamera && activeCamera.layerMask & particleSystem.layerMask) === 0) {\r\n                continue;\r\n            }\r\n\r\n            const emitter: any = particleSystem.emitter;\r\n            if (!emitter.position || !activeMeshes || activeMeshes.indexOf(emitter) !== -1) {\r\n                this._scene._activeParticles.addCount(particleSystem.render(), false);\r\n            }\r\n        }\r\n        this._scene.onAfterParticlesRenderingObservable.notifyObservers(this._scene);\r\n    }\r\n\r\n    private _renderSprites(): void {\r\n        if (!this._scene.spritesEnabled || this._spriteManagers.length === 0) {\r\n            return;\r\n        }\r\n\r\n        // Sprites\r\n        const activeCamera = this._scene.activeCamera;\r\n        this._scene.onBeforeSpritesRenderingObservable.notifyObservers(this._scene);\r\n        for (let id = 0; id < this._spriteManagers.length; id++) {\r\n            const spriteManager = this._spriteManagers.data[id];\r\n\r\n            if ((activeCamera && activeCamera.layerMask & spriteManager.layerMask) !== 0) {\r\n                spriteManager.render();\r\n            }\r\n        }\r\n        this._scene.onAfterSpritesRenderingObservable.notifyObservers(this._scene);\r\n    }\r\n}\r\n"]}