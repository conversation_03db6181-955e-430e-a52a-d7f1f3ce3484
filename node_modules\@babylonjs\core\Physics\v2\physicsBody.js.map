{"version": 3, "file": "physicsBody.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Physics/v2/physicsBody.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAE3D,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAM1E,OAAO,EAAE,KAAK,EAAE,iCAA6B;AAO7C;;;;GAIG;AACH,MAAM,OAAO,WAAW;IAqDpB;;;;;;;;;;;;;OAaG;IACH,YAAY,aAA4B,EAAE,UAA6B,EAAE,YAAqB,EAAE,KAAY;QAlE5G;;WAEG;QACI,gBAAW,GAAQ,SAAS,CAAC;QACpC;;WAEG;QACI,yBAAoB,GAAe,EAAE,CAAC;QAS7C;;WAEG;QACK,wBAAmB,GAAY,KAAK,CAAC;QAC7C;;WAEG;QACK,6BAAwB,GAAY,KAAK,CAAC;QAKlD;;;WAGG;QACH,mBAAc,GAAY,IAAI,CAAC;QAE/B;;WAEG;QACH,gBAAW,GAAY,KAAK,CAAC;QASrB,gBAAW,GAAG,KAAK,CAAC;QAEpB,WAAM,GAA2B,IAAI,CAAC;QAmB1C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QACD,MAAM,aAAa,GAAG,KAAK,CAAC,gBAAgB,EAAmB,CAAC;QAChE,IAAI,CAAC,aAAa,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACnD;QACD,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,aAAa,CAAC,gBAAgB,EAAE,IAAI,CAAC,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;SACvE;QACD,MAAM,aAAa,GAAG,aAAa,CAAC,gBAAgB,EAAE,CAAC;QACvD,IAAI,CAAC,aAAa,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACnD;QAED,IAAI,CAAC,cAAc,GAAG,aAAuC,CAAC;QAC9D,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE;YACnC,aAAa,CAAC,kBAAkB,GAAG,UAAU,CAAC,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SAC/I;QAED,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC;QAEhC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,6DAA6D;QAC7D,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI,iBAAiB,CAAC,OAAO,CAAC;QAE3D,aAAa;QACb,MAAM,CAAC,GAAG,aAAqB,CAAC;QAChC,IAAI,CAAC,CAAC,gBAAgB,EAAE;YACpB,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;SAC9D;aAAM;YACH,kBAAkB;YAClB,IAAI,aAAa,CAAC,MAAM,EAAE;gBACtB,0IAA0I;gBAC1I,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC1C;YACD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,aAAa,CAAC,gBAAgB,EAAE,aAAa,CAAC,0BAA0B,CAAC,CAAC;SAC5H;QACD,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC;QACjC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE5B,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YACnE,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,aAA4B;QACrC,MAAM,UAAU,GAAG,IAAI,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;QACzH,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAC9B,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QACvD,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;QACrD,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QACvD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,MAAM,CAAC,GAAG,IAAI,CAAC,aAAqB,CAAC;QACrC,IAAI,CAAC,CAAC,gBAAgB,EAAE;YACpB,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;SACpD;IACL,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;;;OAMG;IACH,IAAW,KAAK,CAAC,KAA6B;QAC1C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SAC7C;IACL,CAAC;IAED;;;;;;;;OAQG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;;;OAQG;IACI,YAAY,CAAC,SAAiB,EAAE,aAAsB;QACzD,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IACrE,CAAC;IAED;;;;;;;;;OASG;IACI,YAAY,CAAC,aAAsB;QACtC,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IACjE,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,UAA6B,EAAE,aAAsB;QACtE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;IACvE,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,aAAsB;QACvC,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;;;OAQG;IACI,qBAAqB,CAAC,aAAsB;QAC/C,OAAO,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;;;;OAQG;IACI,iBAAiB,CAAC,SAAgC,EAAE,aAAsB;QAC7E,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;;;;;OASG;IACI,iBAAiB,CAAC,aAAsB;QAC3C,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IACtE,CAAC;IAED;;;;;;;;;OASG;IACI,gBAAgB,CAAC,OAAe,EAAE,aAAsB;QAC3D,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;IACvE,CAAC;IAED;;;;;;;;OAQG;IACI,gBAAgB,CAAC,aAAsB;QAC1C,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IACrE,CAAC;IAED;;;;;;;;OAQG;IACI,iBAAiB,CAAC,OAAe,EAAE,aAAsB;QAC5D,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;IACxE,CAAC;IAED;;;;;;;;;OASG;IACI,iBAAiB,CAAC,aAAsB;QAC3C,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IACtE,CAAC;IAED;;;;;;;;;OASG;IACI,iBAAiB,CAAC,MAAe,EAAE,aAAsB;QAC5D,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IACvE,CAAC;IAED;;;;;;;OAOG;IACI,sBAAsB,CAAC,MAAe,EAAE,aAAsB;QACjE,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;;;OAOG;IACI,iBAAiB,CAAC,aAAsB;QAC3C,MAAM,GAAG,GAAG,IAAI,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,sBAAsB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;QAChD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;;;;OAQG;IACI,kBAAkB,CAAC,MAAe,EAAE,aAAsB;QAC7D,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IACxE,CAAC;IAED;;;;;;;OAOG;IACI,uBAAuB,CAAC,MAAe,EAAE,aAAsB;QAClE,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IAC7E,CAAC;IAED;;;;;;;OAOG;IACI,kBAAkB,CAAC,aAAsB;QAC5C,MAAM,GAAG,GAAG,IAAI,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,uBAAuB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;QACjD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;;;;;OASG;IACI,YAAY,CAAC,OAAgB,EAAE,QAAiB,EAAE,aAAsB;QAC3E,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;IAC7E,CAAC;IAED;;;;;;;;;OASG;IACI,UAAU,CAAC,KAAc,EAAE,QAAiB,EAAE,aAAsB;QACvE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;IACzE,CAAC;IAED;;;;;;OAMG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACI,sBAAsB;QACzB,OAAO,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED;;;OAGG;IACI,2BAA2B;QAC9B,OAAO,IAAI,CAAC,cAAc,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC;IAED;;;OAGG;IACI,2BAA2B,CAAC,OAAgB;QAC/C,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC;QACnC,IAAI,CAAC,cAAc,CAAC,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACnE,CAAC;IAED;;;OAGG;IACI,gCAAgC,CAAC,OAAgB;QACpD,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC;QACxC,IAAI,CAAC,cAAc,CAAC,gCAAgC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,aAAsB;QAC9C,MAAM,GAAG,GAAG,IAAI,OAAO,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC,yBAAyB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACI,yBAAyB,CAAC,GAAY,EAAE,aAAsB;QACjE,IAAI,IAAI,CAAC,oBAAoB,EAAE,MAAM,GAAG,CAAC,EAAE;YACvC,MAAM,KAAK,GAAG,aAAa,IAAI,CAAC,CAAC;YACjC,MAAM,UAAU,GAAI,IAAI,CAAC,aAAsB,CAAC,wBAAwB,CAAC,UAAU,CAAC;YACpF,IAAI,UAAU,EAAE;gBACZ,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;aAClG;SACJ;aAAM;YACH,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;SAC7C;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;;;;OAQG;IACI,aAAa,CAAC,SAAsB,EAAE,UAA6B,EAAE,aAAsB,EAAE,kBAA2B;QAC3H,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,kBAAkB,CAAC,CAAC;IACtG,CAAC;IAED;;;;;;;;OAQG;IACI,YAAY,CAAC,IAAU,EAAE,QAAsB,EAAE,UAAmB,EAAE,WAAoB,EAAE,cAA2B,EAAE,QAAkB;QAC9I,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC;QAEhC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,cAAc,EAAE;gBAChB,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC1C,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACjE,QAAQ,CAAC,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACnE;iBAAM;gBACH,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACnF;SACJ;QAED,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEtC,IAAI,CAAC,QAAQ,EAAE;YACX,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACjC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;YACf,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;YACf,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;SAClB;QAED,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAE7C,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,CAAC,IAAI,UAAU,EAAE;YACnE,WAAW,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;SACrC;QAED,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE;YACnD,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC;YACjC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC;YACjC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC;SACpC;QAED,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,QAA6D;QACxF,IAAI,IAAI,CAAC,oBAAoB,EAAE,MAAM,GAAG,CAAC,EAAE;YACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACvD,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aACrB;SACJ;aAAM;YACH,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SAC7B;IACL,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,MAAc,EAAE,aAAsB;QAC1D,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IACtE,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,aAAsB;QAC1C,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IACrE,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,QAAiB,EAAE,QAAoB,EAAE,aAAsB;QACrF,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;IACpF,CAAC;IAED;;;OAGG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,OAAO;SACV;QACD,qEAAqE;QACrE,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;SAC3C;QACD,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACzE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SACpC;QACD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACtB,CAAC;CACJ", "sourcesContent": ["import type { IBasePhysicsCollisionEvent, IPhysicsCollisionEvent, IPhysicsEnginePluginV2, PhysicsMassProperties } from \"./IPhysicsEnginePlugin\";\r\nimport { PhysicsMotionType } from \"./IPhysicsEnginePlugin\";\r\nimport type { PhysicsShape } from \"./physicsShape\";\r\nimport { Vector3, Quaternion, TmpVectors } from \"../../Maths/math.vector\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { PhysicsEngine } from \"./physicsEngine\";\r\nimport type { Nullable } from \"core/types\";\r\nimport type { PhysicsConstraint } from \"./physicsConstraint\";\r\nimport type { Bone } from \"core/Bones/bone\";\r\nimport { Space } from \"core/Maths/math.axis\";\r\nimport type { Observable, Observer } from \"../../Misc/observable\";\r\nimport type { Node } from \"../../node\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\n\r\n/**\r\n * PhysicsBody is useful for creating a physics body that can be used in a physics engine. It allows\r\n * the user to set the mass and velocity of the body, which can then be used to calculate the\r\n * motion of the body in the physics engine.\r\n */\r\nexport class PhysicsBody {\r\n    /**\r\n     * V2 Physics plugin private data for single Transform\r\n     */\r\n    public _pluginData: any = undefined;\r\n    /**\r\n     * V2 Physics plugin private data for instances\r\n     */\r\n    public _pluginDataInstances: Array<any> = [];\r\n    /**\r\n     * The V2 plugin used to create and manage this Physics Body\r\n     */\r\n    private _physicsPlugin: IPhysicsEnginePluginV2;\r\n    /**\r\n     * The engine used to create and manage this Physics Body\r\n     */\r\n    private _physicsEngine: PhysicsEngine;\r\n    /**\r\n     * If the collision callback is enabled\r\n     */\r\n    private _collisionCBEnabled: boolean = false;\r\n    /**\r\n     * If the collision ended callback is enabled\r\n     */\r\n    private _collisionEndedCBEnabled: boolean = false;\r\n    /**\r\n     * The transform node associated with this Physics Body\r\n     */\r\n    transformNode: TransformNode;\r\n    /**\r\n     * Disable pre-step that consists in updating Physics Body from Transform Node Translation/Orientation.\r\n     * True by default for maximum performance.\r\n     */\r\n    disablePreStep: boolean = true;\r\n\r\n    /**\r\n     * Disable sync from physics to transformNode. This value is set to true at body creation when the body is not dynamic.\r\n     */\r\n    disableSync: boolean = false;\r\n\r\n    /**\r\n     * Physics engine will try to make this body sleeping and not active\r\n     */\r\n    public startAsleep: boolean;\r\n\r\n    private _nodeDisposeObserver: Nullable<Observer<Node>>;\r\n\r\n    private _isDisposed = false;\r\n\r\n    private _shape: Nullable<PhysicsShape> = null;\r\n\r\n    private _motionType: PhysicsMotionType;\r\n\r\n    /**\r\n     * Constructs a new physics body for the given node.\r\n     * @param transformNode - The Transform Node to construct the physics body for. For better performance, it is advised that this node does not have a parent.\r\n     * @param motionType - The motion type of the physics body. The options are:\r\n     *  - PhysicsMotionType.STATIC - Static bodies are not moving and unaffected by forces or collisions. They are good for level boundaries or terrain.\r\n     *  - PhysicsMotionType.DYNAMIC - Dynamic bodies are fully simulated. They can move and collide with other objects.\r\n     *  - PhysicsMotionType.ANIMATED - They behave like dynamic bodies, but they won't be affected by other bodies, but still push other bodies out of the way.\r\n     * @param startsAsleep - Whether the physics body should start in a sleeping state (not a guarantee). Defaults to false.\r\n     * @param scene - The scene containing the physics engine.\r\n     *\r\n     * This code is useful for creating a physics body for a given Transform Node in a scene.\r\n     * It checks the version of the physics engine and the physics plugin, and initializes the body accordingly.\r\n     * It also sets the node's rotation quaternion if it is not already set. Finally, it adds the body to the physics engine.\r\n     */\r\n    constructor(transformNode: TransformNode, motionType: PhysicsMotionType, startsAsleep: boolean, scene: Scene) {\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        const physicsEngine = scene.getPhysicsEngine() as PhysicsEngine;\r\n        if (!physicsEngine) {\r\n            throw new Error(\"No Physics Engine available.\");\r\n        }\r\n        this._physicsEngine = physicsEngine;\r\n        if (physicsEngine.getPluginVersion() != 2) {\r\n            throw new Error(\"Plugin version is incorrect. Expected version 2.\");\r\n        }\r\n        const physicsPlugin = physicsEngine.getPhysicsPlugin();\r\n        if (!physicsPlugin) {\r\n            throw new Error(\"No Physics Plugin available.\");\r\n        }\r\n\r\n        this._physicsPlugin = physicsPlugin as IPhysicsEnginePluginV2;\r\n        if (!transformNode.rotationQuaternion) {\r\n            transformNode.rotationQuaternion = Quaternion.FromEulerAngles(transformNode.rotation.x, transformNode.rotation.y, transformNode.rotation.z);\r\n        }\r\n\r\n        this.startAsleep = startsAsleep;\r\n\r\n        this._motionType = motionType;\r\n\r\n        // only dynamic body needs sync from physics to transformNode\r\n        this.disableSync = motionType != PhysicsMotionType.DYNAMIC;\r\n\r\n        // instances?\r\n        const m = transformNode as Mesh;\r\n        if (m.hasThinInstances) {\r\n            this._physicsPlugin.initBodyInstances(this, motionType, m);\r\n        } else {\r\n            // single instance\r\n            if (transformNode.parent) {\r\n                // Force computation of world matrix so that the parent transforms are correctly reflected in absolutePosition/absoluteRotationQuaternion.\r\n                transformNode.computeWorldMatrix(true);\r\n            }\r\n            this._physicsPlugin.initBody(this, motionType, transformNode.absolutePosition, transformNode.absoluteRotationQuaternion);\r\n        }\r\n        this.transformNode = transformNode;\r\n        transformNode.physicsBody = this;\r\n        physicsEngine.addBody(this);\r\n\r\n        this._nodeDisposeObserver = transformNode.onDisposeObservable.add(() => {\r\n            this.dispose();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"PhysicsBody\".\r\n     * @returns \"PhysicsBody\"\r\n     */\r\n    public getClassName() {\r\n        return \"PhysicsBody\";\r\n    }\r\n\r\n    /**\r\n     * Clone the PhysicsBody to a new body and assign it to the transformNode parameter\r\n     * @param transformNode transformNode that will be used for the cloned PhysicsBody\r\n     * @returns the newly cloned PhysicsBody\r\n     */\r\n    public clone(transformNode: TransformNode): PhysicsBody {\r\n        const clonedBody = new PhysicsBody(transformNode, this.getMotionType(), this.startAsleep, this.transformNode.getScene());\r\n        clonedBody.shape = this.shape;\r\n        clonedBody.setMassProperties(this.getMassProperties());\r\n        clonedBody.setLinearDamping(this.getLinearDamping());\r\n        clonedBody.setAngularDamping(this.getAngularDamping());\r\n        return clonedBody;\r\n    }\r\n\r\n    /**\r\n     * If a physics body is connected to an instanced node, update the number physic instances to match the number of node instances.\r\n     */\r\n    public updateBodyInstances() {\r\n        const m = this.transformNode as Mesh;\r\n        if (m.hasThinInstances) {\r\n            this._physicsPlugin.updateBodyInstances(this, m);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * This returns the number of internal instances of the physics body\r\n     */\r\n    public get numInstances(): number {\r\n        return this._pluginDataInstances.length;\r\n    }\r\n\r\n    /**\r\n     * Get the motion type of the physics body. Can be STATIC, DYNAMIC, or ANIMATED.\r\n     */\r\n    public get motionType(): PhysicsMotionType {\r\n        return this._motionType;\r\n    }\r\n\r\n    /**\r\n     * Sets the shape of the physics body.\r\n     * @param shape - The shape of the physics body.\r\n     *\r\n     * This method is useful for setting the shape of the physics body, which is necessary for the physics engine to accurately simulate the body's behavior.\r\n     * The shape is used to calculate the body's mass, inertia, and other properties.\r\n     */\r\n    public set shape(shape: Nullable<PhysicsShape>) {\r\n        this._shape = shape;\r\n        if (shape) {\r\n            this._physicsPlugin.setShape(this, shape);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Retrieves the physics shape associated with this object.\r\n     *\r\n     * @returns The physics shape associated with this object, or `undefined` if no\r\n     * shape is associated.\r\n     *\r\n     * This method is useful for retrieving the physics shape associated with this object,\r\n     * which can be used to apply physical forces to the object or to detect collisions.\r\n     */\r\n    public get shape(): Nullable<PhysicsShape> {\r\n        return this._shape;\r\n    }\r\n\r\n    /**\r\n     * Sets the event mask for the physics engine.\r\n     *\r\n     * @param eventMask - A bitmask that determines which events will be sent to the physics engine.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to set the event mask for.\r\n     *\r\n     * This method is useful for setting the event mask for the physics engine, which determines which events\r\n     * will be sent to the physics engine. This allows the user to control which events the physics engine will respond to.\r\n     */\r\n    public setEventMask(eventMask: number, instanceIndex?: number) {\r\n        this._physicsPlugin.setEventMask(this, eventMask, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Gets the event mask of the physics engine.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to get the event mask for.\r\n     * @returns The event mask of the physics engine.\r\n     *\r\n     * This method is useful for getting the event mask of the physics engine,\r\n     * which is used to determine which events the engine will respond to.\r\n     * This is important for ensuring that the engine is responding to the correct events and not\r\n     * wasting resources on unnecessary events.\r\n     */\r\n    public getEventMask(instanceIndex?: number): number {\r\n        return this._physicsPlugin.getEventMask(this, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Sets the motion type of the physics body. Can be STATIC, DYNAMIC, or ANIMATED.\r\n     * @param motionType - The motion type to set.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to set the motion type for.\r\n     */\r\n    public setMotionType(motionType: PhysicsMotionType, instanceIndex?: number) {\r\n        this._physicsPlugin.setMotionType(this, motionType, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Gets the motion type of the physics body. Can be STATIC, DYNAMIC, or ANIMATED.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to get the motion type for.\r\n     * @returns The motion type of the physics body.\r\n     */\r\n    public getMotionType(instanceIndex?: number): PhysicsMotionType {\r\n        return this._physicsPlugin.getMotionType(this, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Computes the mass properties of the physics object, based on the set of physics shapes this body uses.\r\n     * This method is useful for computing the initial mass properties of a physics object, such as its mass,\r\n     * inertia, and center of mass; these values are important for accurately simulating the physics of the\r\n     * object in the physics engine, and computing values based on the shape will provide you with reasonable\r\n     * initial values, which you can then customize.\r\n     * @param instanceIndex - The index of the instance to compute the mass properties for.\r\n     * @returns The mass properties of the object.\r\n     */\r\n    public computeMassProperties(instanceIndex?: number): PhysicsMassProperties {\r\n        return this._physicsPlugin.computeMassProperties(this, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Sets the mass properties of the physics object.\r\n     *\r\n     * @param massProps - The mass properties to set.\r\n     * @param instanceIndex - The index of the instance to set the mass properties for. If not defined, the mass properties will be set for all instances.\r\n     *\r\n     * This method is useful for setting the mass properties of a physics object, such as its mass,\r\n     * inertia, and center of mass. This is important for accurately simulating the physics of the object in the physics engine.\r\n     */\r\n    public setMassProperties(massProps: PhysicsMassProperties, instanceIndex?: number): void {\r\n        this._physicsPlugin.setMassProperties(this, massProps, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Retrieves the mass properties of the object.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to get the mass properties for.\r\n     * @returns The mass properties of the object.\r\n     *\r\n     * This method is useful for physics simulations, as it allows the user to\r\n     * retrieve the mass properties of the object, such as its mass, center of mass,\r\n     * and moment of inertia. This information is necessary for accurate physics\r\n     * simulations.\r\n     */\r\n    public getMassProperties(instanceIndex?: number): PhysicsMassProperties {\r\n        return this._physicsPlugin.getMassProperties(this, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Sets the linear damping of the physics body.\r\n     *\r\n     * @param damping - The linear damping value.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to set the linear damping for.\r\n     *\r\n     * This method is useful for controlling the linear damping of the physics body,\r\n     * which is the rate at which the body's velocity decreases over time. This is useful for simulating\r\n     * the effects of air resistance or other forms of friction.\r\n     */\r\n    public setLinearDamping(damping: number, instanceIndex?: number) {\r\n        this._physicsPlugin.setLinearDamping(this, damping, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Gets the linear damping of the physics body.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to get the linear damping for.\r\n     * @returns The linear damping of the physics body.\r\n     *\r\n     * This method is useful for retrieving the linear damping of the physics body, which is the amount of\r\n     * resistance the body has to linear motion. This is useful for simulating realistic physics behavior\r\n     * in a game.\r\n     */\r\n    public getLinearDamping(instanceIndex?: number): number {\r\n        return this._physicsPlugin.getLinearDamping(this, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Sets the angular damping of the physics body.\r\n     * @param damping The angular damping of the body.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to set the angular damping for.\r\n     *\r\n     * This method is useful for controlling the angular velocity of a physics body.\r\n     * By setting the damping, the body's angular velocity will be reduced over time, simulating the effect of friction.\r\n     * This can be used to create realistic physical behavior in a physics engine.\r\n     */\r\n    public setAngularDamping(damping: number, instanceIndex?: number) {\r\n        this._physicsPlugin.setAngularDamping(this, damping, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Gets the angular damping of the physics body.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to get the angular damping for.\r\n     *\r\n     * @returns The angular damping of the physics body.\r\n     *\r\n     * This method is useful for getting the angular damping of the physics body,\r\n     * which is the rate of reduction of the angular velocity over time.\r\n     * This is important for simulating realistic physics behavior in a game.\r\n     */\r\n    public getAngularDamping(instanceIndex?: number): number {\r\n        return this._physicsPlugin.getAngularDamping(this, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Sets the linear velocity of the physics object.\r\n     * @param linVel - The linear velocity to set.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to set the linear velocity for.\r\n     *\r\n     * This method is useful for setting the linear velocity of a physics object,\r\n     * which is necessary for simulating realistic physics in a game engine.\r\n     * By setting the linear velocity, the physics object will move in the direction and speed specified by the vector.\r\n     * This allows for realistic physics simulations, such as simulating the motion of a ball rolling down a hill.\r\n     */\r\n    public setLinearVelocity(linVel: Vector3, instanceIndex?: number): void {\r\n        this._physicsPlugin.setLinearVelocity(this, linVel, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Gets the linear velocity of the physics body and stores it in the given vector3.\r\n     * @param linVel - The vector3 to store the linear velocity in.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to get the linear velocity for.\r\n     *\r\n     * This method is useful for getting the linear velocity of a physics body in a physics engine.\r\n     * This can be used to determine the speed and direction of the body, which can be used to calculate the motion of the body.\r\n     */\r\n    public getLinearVelocityToRef(linVel: Vector3, instanceIndex?: number): void {\r\n        this._physicsPlugin.getLinearVelocityToRef(this, linVel, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Gets the linear velocity of the physics body as a new vector3.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to get the linear velocity for.\r\n     * @returns The linear velocity of the physics body.\r\n     *\r\n     * This method is useful for getting the linear velocity of a physics body in a physics engine.\r\n     * This can be used to determine the speed and direction of the body, which can be used to calculate the motion of the body.\r\n     */\r\n    public getLinearVelocity(instanceIndex?: number): Vector3 {\r\n        const ref = new Vector3();\r\n        this.getLinearVelocityToRef(ref, instanceIndex);\r\n        return ref;\r\n    }\r\n\r\n    /**\r\n     * Sets the angular velocity of the physics object.\r\n     * @param angVel - The angular velocity to set.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to set the angular velocity for.\r\n     *\r\n     * This method is useful for setting the angular velocity of a physics object, which is necessary for\r\n     * simulating realistic physics behavior. The angular velocity is used to determine the rate of rotation of the object,\r\n     * which is important for simulating realistic motion.\r\n     */\r\n    public setAngularVelocity(angVel: Vector3, instanceIndex?: number): void {\r\n        this._physicsPlugin.setAngularVelocity(this, angVel, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Gets the angular velocity of the physics body and stores it in the given vector3.\r\n     * @param angVel - The vector3 to store the angular velocity in.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to get the angular velocity for.\r\n     *\r\n     * This method is useful for getting the angular velocity of a physics body, which can be used to determine the body's\r\n     * rotational speed. This information can be used to create realistic physics simulations.\r\n     */\r\n    public getAngularVelocityToRef(angVel: Vector3, instanceIndex?: number): void {\r\n        this._physicsPlugin.getAngularVelocityToRef(this, angVel, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Gets the angular velocity of the physics body as a new vector3.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to get the angular velocity for.\r\n     * @returns The angular velocity of the physics body.\r\n     *\r\n     * This method is useful for getting the angular velocity of a physics body, which can be used to determine the body's\r\n     * rotational speed. This information can be used to create realistic physics simulations.\r\n     */\r\n    public getAngularVelocity(instanceIndex?: number): Vector3 {\r\n        const ref = new Vector3();\r\n        this.getAngularVelocityToRef(ref, instanceIndex);\r\n        return ref;\r\n    }\r\n\r\n    /**\r\n     * Applies an impulse to the physics object.\r\n     *\r\n     * @param impulse The impulse vector.\r\n     * @param location The location of the impulse.\r\n     * @param instanceIndex For a instanced body, the instance to where the impulse should be applied. If not specified, the impulse is applied to all instances.\r\n     *\r\n     * This method is useful for applying an impulse to a physics object, which can be used to simulate physical forces such as gravity,\r\n     * collisions, and explosions. This can be used to create realistic physics simulations in a game or other application.\r\n     */\r\n    public applyImpulse(impulse: Vector3, location: Vector3, instanceIndex?: number): void {\r\n        this._physicsPlugin.applyImpulse(this, impulse, location, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Applies a force to the physics object.\r\n     *\r\n     * @param force The force vector.\r\n     * @param location The location of the force.\r\n     * @param instanceIndex For a instanced body, the instance to where the force should be applied. If not specified, the force is applied to all instances.\r\n     *\r\n     * This method is useful for applying a force to a physics object, which can be used to simulate physical forces such as gravity,\r\n     * collisions, and explosions. This can be used to create realistic physics simulations in a game or other application.\r\n     */\r\n    public applyForce(force: Vector3, location: Vector3, instanceIndex?: number): void {\r\n        this._physicsPlugin.applyForce(this, force, location, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Retrieves the geometry of the body from the physics plugin.\r\n     *\r\n     * @returns The geometry of the body.\r\n     *\r\n     * This method is useful for retrieving the geometry of the body from the physics plugin, which can be used for various physics calculations.\r\n     */\r\n    public getGeometry(): {} {\r\n        return this._physicsPlugin.getBodyGeometry(this);\r\n    }\r\n\r\n    /**\r\n     * Returns an observable that will be notified for when a collision starts or continues for this PhysicsBody\r\n     * @returns Observable\r\n     */\r\n    public getCollisionObservable(): Observable<IPhysicsCollisionEvent> {\r\n        return this._physicsPlugin.getCollisionObservable(this);\r\n    }\r\n\r\n    /**\r\n     * Returns an observable that will be notified when the body has finished colliding with another body\r\n     * @returns\r\n     */\r\n    public getCollisionEndedObservable(): Observable<IBasePhysicsCollisionEvent> {\r\n        return this._physicsPlugin.getCollisionEndedObservable(this);\r\n    }\r\n\r\n    /**\r\n     * Enable or disable collision callback for this PhysicsBody.\r\n     * @param enabled true if PhysicsBody's collision will rise a collision event and notifies the observable\r\n     */\r\n    public setCollisionCallbackEnabled(enabled: boolean): void {\r\n        this._collisionCBEnabled = enabled;\r\n        this._physicsPlugin.setCollisionCallbackEnabled(this, enabled);\r\n    }\r\n\r\n    /**\r\n     * Enable or disable collision ended callback for this PhysicsBody.\r\n     * @param enabled true if PhysicsBody's collision ended will rise a collision event and notifies the observable\r\n     */\r\n    public setCollisionEndedCallbackEnabled(enabled: boolean): void {\r\n        this._collisionEndedCBEnabled = enabled;\r\n        this._physicsPlugin.setCollisionEndedCallbackEnabled(this, enabled);\r\n    }\r\n\r\n    /**\r\n     * Get the center of the object in world space.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to get the center for.\r\n     * @returns geometric center of the associated mesh\r\n     */\r\n    public getObjectCenterWorld(instanceIndex?: number): Vector3 {\r\n        const ref = new Vector3();\r\n        return this.getObjectCenterWorldToRef(ref, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Get the center of the object in world space.\r\n     * @param ref - The vector3 to store the result in.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to get the center for.\r\n     * @returns geometric center of the associated mesh\r\n     */\r\n    public getObjectCenterWorldToRef(ref: Vector3, instanceIndex?: number): Vector3 {\r\n        if (this._pluginDataInstances?.length > 0) {\r\n            const index = instanceIndex || 0;\r\n            const matrixData = (this.transformNode as Mesh)._thinInstanceDataStorage.matrixData;\r\n            if (matrixData) {\r\n                ref.set(matrixData[index * 16 + 12], matrixData[index * 16 + 13], matrixData[index * 16 + 14]);\r\n            }\r\n        } else {\r\n            ref.copyFrom(this.transformNode.position);\r\n        }\r\n        return ref;\r\n    }\r\n\r\n    /**\r\n     * Adds a constraint to the physics engine.\r\n     *\r\n     * @param childBody - The body to which the constraint will be applied.\r\n     * @param constraint - The constraint to be applied.\r\n     * @param instanceIndex - If this body is instanced, the index of the instance to which the constraint will be applied. If not specified, no constraint will be applied.\r\n     * @param childInstanceIndex - If the child body is instanced, the index of the instance to which the constraint will be applied. If not specified, no constraint will be applied.\r\n     *\r\n     */\r\n    public addConstraint(childBody: PhysicsBody, constraint: PhysicsConstraint, instanceIndex?: number, childInstanceIndex?: number): void {\r\n        this._physicsPlugin.addConstraint(this, childBody, constraint, instanceIndex, childInstanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Sync with a bone\r\n     * @param bone The bone that the impostor will be synced to.\r\n     * @param boneMesh The mesh that the bone is influencing.\r\n     * @param jointPivot The pivot of the joint / bone in local space.\r\n     * @param distToJoint Optional distance from the impostor to the joint.\r\n     * @param adjustRotation Optional quaternion for adjusting the local rotation of the bone.\r\n     * @param boneAxis Optional vector3 axis the bone is aligned with\r\n     */\r\n    public syncWithBone(bone: Bone, boneMesh: AbstractMesh, jointPivot: Vector3, distToJoint?: number, adjustRotation?: Quaternion, boneAxis?: Vector3) {\r\n        const mesh = this.transformNode;\r\n\r\n        if (mesh.rotationQuaternion) {\r\n            if (adjustRotation) {\r\n                const tempQuat = TmpVectors.Quaternion[0];\r\n                bone.getRotationQuaternionToRef(Space.WORLD, boneMesh, tempQuat);\r\n                tempQuat.multiplyToRef(adjustRotation, mesh.rotationQuaternion);\r\n            } else {\r\n                bone.getRotationQuaternionToRef(Space.WORLD, boneMesh, mesh.rotationQuaternion);\r\n            }\r\n        }\r\n\r\n        const pos = TmpVectors.Vector3[0];\r\n        const boneDir = TmpVectors.Vector3[1];\r\n\r\n        if (!boneAxis) {\r\n            boneAxis = TmpVectors.Vector3[2];\r\n            boneAxis.x = 0;\r\n            boneAxis.y = 1;\r\n            boneAxis.z = 0;\r\n        }\r\n\r\n        bone.getDirectionToRef(boneAxis, boneMesh, boneDir);\r\n        bone.getAbsolutePositionToRef(boneMesh, pos);\r\n\r\n        if ((distToJoint === undefined || distToJoint === null) && jointPivot) {\r\n            distToJoint = jointPivot.length();\r\n        }\r\n\r\n        if (distToJoint !== undefined && distToJoint !== null) {\r\n            pos.x += boneDir.x * distToJoint;\r\n            pos.y += boneDir.y * distToJoint;\r\n            pos.z += boneDir.z * distToJoint;\r\n        }\r\n\r\n        mesh.setAbsolutePosition(pos);\r\n    }\r\n\r\n    /**\r\n     * Executes a callback on the body or all of the instances of a body\r\n     * @param callback the callback to execute\r\n     */\r\n    public iterateOverAllInstances(callback: (body: PhysicsBody, instanceIndex?: number) => void) {\r\n        if (this._pluginDataInstances?.length > 0) {\r\n            for (let i = 0; i < this._pluginDataInstances.length; i++) {\r\n                callback(this, i);\r\n            }\r\n        } else {\r\n            callback(this, undefined);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the gravity factor of the physics body\r\n     * @param factor the gravity factor to set\r\n     * @param instanceIndex the instance of the body to set, if undefined all instances will be set\r\n     */\r\n    public setGravityFactor(factor: number, instanceIndex?: number) {\r\n        this._physicsPlugin.setGravityFactor(this, factor, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Gets the gravity factor of the physics body\r\n     * @param instanceIndex the instance of the body to get, if undefined the value of first instance will be returned\r\n     * @returns the gravity factor\r\n     */\r\n    public getGravityFactor(instanceIndex?: number): number {\r\n        return this._physicsPlugin.getGravityFactor(this, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Set the target transformation (position and rotation) of the body, such that the body will set its velocity to reach that target\r\n     * @param position The target position\r\n     * @param rotation The target rotation\r\n     * @param instanceIndex The index of the instance in an instanced body\r\n     */\r\n    public setTargetTransform(position: Vector3, rotation: Quaternion, instanceIndex?: number) {\r\n        this._physicsPlugin.setTargetTransform(this, position, rotation, instanceIndex);\r\n    }\r\n\r\n    /**\r\n     * Returns if the body has been disposed.\r\n     * @returns true if disposed, false otherwise.\r\n     */\r\n    public get isDisposed() {\r\n        return this._isDisposed;\r\n    }\r\n\r\n    /**\r\n     * Disposes the body from the physics engine.\r\n     *\r\n     * This method is useful for cleaning up the physics engine when a body is no longer needed. Disposing the body will free up resources and prevent memory leaks.\r\n     */\r\n    public dispose() {\r\n        if (this._isDisposed) {\r\n            return;\r\n        }\r\n        // Disable collisions CB so it doesn't fire when the body is disposed\r\n        if (this._collisionCBEnabled) {\r\n            this.setCollisionCallbackEnabled(false);\r\n        }\r\n        if (this._collisionEndedCBEnabled) {\r\n            this.setCollisionEndedCallbackEnabled(false);\r\n        }\r\n        if (this._nodeDisposeObserver) {\r\n            this.transformNode.onDisposeObservable.remove(this._nodeDisposeObserver);\r\n            this._nodeDisposeObserver = null;\r\n        }\r\n        this._physicsEngine.removeBody(this);\r\n        this._physicsPlugin.removeBody(this);\r\n        this._physicsPlugin.disposeBody(this);\r\n        this.transformNode.physicsBody = null;\r\n        this._pluginData = null;\r\n        this._pluginDataInstances.length = 0;\r\n        this._isDisposed = true;\r\n        this.shape = null;\r\n    }\r\n}\r\n"]}