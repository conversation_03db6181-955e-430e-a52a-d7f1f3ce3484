{"version": 3, "file": "videoRecorder.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/videoRecorder.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAgEhC;;;;;GAKG;AACH,MAAM,OAAO,aAAa;IAOtB;;;;;OAKG;IACI,MAAM,CAAC,WAAW,CAAC,MAAc,EAAE,MAA0B;QAChE,MAAM,YAAY,GAAG,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;QAC3D,OAAO,CAAC,CAAC,YAAY,IAAI,OAAa,YAAa,CAAC,aAAa,KAAK,UAAU,CAAC;IACrF,CAAC;IAWD;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;IACtD,CAAC;IAED;;;;OAIG;IACH,YAAY,MAAc,EAAE,UAAyC,EAAE;QACnE,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE;YACpD,4CAA4C;YAC5C,MAAM,iDAAiD,CAAC;SAC3D;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;QAC7D,IAAI,CAAC,MAAM,EAAE;YACT,4CAA4C;YAC5C,MAAM,sDAAsD,CAAC;SAChE;QAED,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;QAEjC,IAAI,CAAC,QAAQ,GAAG;YACZ,GAAG,aAAa,CAAC,eAAe;YAChC,GAAG,OAAO;SACb,CAAC;QAEF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC7D,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC3B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;gBAC3C,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aAC1B;SACJ;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,aAAa,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QACtF,IAAI,CAAC,cAAc,CAAC,eAAe,GAAG,CAAC,GAAU,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACrF,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,CAAC,GAAe,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAC1E,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACvC,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO;SACV;QAED,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC;IAED;;;;;;;OAOG;IACI,cAAc,CAAC,WAA6B,gBAAgB,EAAE,WAAW,GAAG,CAAC;QAChF,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACvC,4CAA4C;YAC5C,MAAM,oCAAoC,CAAC;SAC9C;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,4CAA4C;YAC5C,MAAM,+BAA+B,CAAC;SACzC;QAED,IAAI,WAAW,GAAG,CAAC,EAAE;YACjB,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,aAAa,EAAE,CAAC;YACzB,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,CAAC;SAC1B;QAED,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAE1D,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;YACxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAEO,oBAAoB,CAAC,KAAU;QACnC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;YACrB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACzC;IACL,CAAC;IAEO,YAAY,CAAC,KAAiB;QAClC,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SAC7B;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;SAC3B;IACL,CAAC;IAEO,WAAW;QACf,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACnD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;SAC9B;QAED,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAExC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SAC/C;IACL,CAAC;;AAxKuB,6BAAe,GAAG;IACtC,QAAQ,EAAE,YAAY;IACtB,GAAG,EAAE,EAAE;IACP,gBAAgB,EAAE,IAAI;CACzB,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\n/* eslint-disable no-var */\r\nimport type { Nullable } from \"../types\";\r\nimport { Tools } from \"./tools\";\r\nimport type { Engine } from \"../Engines/engine\";\r\n\r\ninterface MediaRecorder {\r\n    /** Starts recording */\r\n    start(timeSlice: number): void;\r\n    /** Stops recording */\r\n    stop(): void;\r\n\r\n    /** Event raised when an error arised. */\r\n    onerror: (event: ErrorEvent) => void;\r\n    /** Event raised when the recording stops. */\r\n    onstop: (event: Event) => void;\r\n    /** Event raised when a new chunk of data is available and should be tracked. */\r\n    ondataavailable: (event: Event) => void;\r\n}\r\n\r\ninterface MediaRecorderOptions {\r\n    /** The mime type you want to use as the recording container for the new MediaRecorder. */\r\n    mimeType?: string;\r\n    /** The chosen bitrate for the audio component of the media. */\r\n    audioBitsPerSecond?: number;\r\n    /** The chosen bitrate for the video component of the media. */\r\n    videoBitsPerSecond?: number;\r\n    /** The chosen bitrate for the audio and video components of the media. This can be specified instead of the above two properties.\r\n     * If this is specified along with one or the other of the above properties, this will be used for the one that isn't specified. */\r\n    bitsPerSecond?: number;\r\n}\r\n\r\ninterface MediaRecorderConstructor {\r\n    /**\r\n     * A reference to the prototype.\r\n     */\r\n    readonly prototype: MediaRecorder;\r\n\r\n    /**\r\n     * Creates a new MediaRecorder.\r\n     * @param stream Defines the stream to record.\r\n     * @param options Defines the options for the recorder available in the type MediaRecorderOptions.\r\n     */\r\n    new (stream: MediaStream, options?: MediaRecorderOptions): MediaRecorder;\r\n}\r\n\r\n/**\r\n * MediaRecorder object available in some browsers.\r\n */\r\ndeclare var MediaRecorder: MediaRecorderConstructor;\r\n\r\n/**\r\n * This represents the different options available for the video capture.\r\n */\r\nexport interface VideoRecorderOptions {\r\n    /** The canvas you want to record */\r\n    canvas?: HTMLCanvasElement;\r\n    /** Defines the mime type of the video. */\r\n    mimeType: string;\r\n    /** Defines the FPS the video should be recorded at. */\r\n    fps: number;\r\n    /** Defines the chunk size for the recording data. */\r\n    recordChunckSize: number;\r\n    /** The audio tracks to attach to the recording. */\r\n    audioTracks?: MediaStreamTrack[];\r\n}\r\n\r\n/**\r\n * This can help with recording videos from BabylonJS.\r\n * This is based on the available WebRTC functionalities of the browser.\r\n *\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToVideo\r\n */\r\nexport class VideoRecorder {\r\n    private static readonly _DefaultOptions = {\r\n        mimeType: \"video/webm\",\r\n        fps: 25,\r\n        recordChunckSize: 3000,\r\n    };\r\n\r\n    /**\r\n     * Returns whether or not the VideoRecorder is available in your browser.\r\n     * @param engine Defines the Babylon Engine.\r\n     * @param canvas Defines the canvas to record. If not provided, the engine canvas will be used.\r\n     * @returns true if supported otherwise false.\r\n     */\r\n    public static IsSupported(engine: Engine, canvas?: HTMLCanvasElement): boolean {\r\n        const targetCanvas = canvas ?? engine.getRenderingCanvas();\r\n        return !!targetCanvas && typeof (<any>targetCanvas).captureStream === \"function\";\r\n    }\r\n\r\n    private readonly _options: VideoRecorderOptions;\r\n    private _canvas: Nullable<HTMLCanvasElement>;\r\n    private _mediaRecorder: Nullable<MediaRecorder>;\r\n\r\n    private _recordedChunks: any[];\r\n    private _fileName: Nullable<string>;\r\n    private _resolve: Nullable<(blob: Blob) => void>;\r\n    private _reject: Nullable<(error: any) => void>;\r\n\r\n    /**\r\n     * True when a recording is already in progress.\r\n     */\r\n    public get isRecording(): boolean {\r\n        return !!this._canvas && this._canvas.isRecording;\r\n    }\r\n\r\n    /**\r\n     * Create a new VideoCapture object which can help converting what you see in Babylon to a video file.\r\n     * @param engine Defines the BabylonJS Engine you wish to record.\r\n     * @param options Defines options that can be used to customize the capture.\r\n     */\r\n    constructor(engine: Engine, options: Partial<VideoRecorderOptions> = {}) {\r\n        if (!VideoRecorder.IsSupported(engine, options.canvas)) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"Your browser does not support recording so far.\";\r\n        }\r\n\r\n        const canvas = options.canvas ?? engine.getRenderingCanvas();\r\n        if (!canvas) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"The babylon engine must have a canvas to be recorded\";\r\n        }\r\n\r\n        this._canvas = canvas;\r\n        this._canvas.isRecording = false;\r\n\r\n        this._options = {\r\n            ...VideoRecorder._DefaultOptions,\r\n            ...options,\r\n        };\r\n\r\n        const stream = this._canvas.captureStream(this._options.fps);\r\n        if (this._options.audioTracks) {\r\n            for (const track of this._options.audioTracks) {\r\n                stream.addTrack(track);\r\n            }\r\n        }\r\n\r\n        this._mediaRecorder = new MediaRecorder(stream, { mimeType: this._options.mimeType });\r\n        this._mediaRecorder.ondataavailable = (evt: Event) => this._handleDataAvailable(evt);\r\n        this._mediaRecorder.onerror = (evt: ErrorEvent) => this._handleError(evt);\r\n        this._mediaRecorder.onstop = () => this._handleStop();\r\n    }\r\n\r\n    /**\r\n     * Stops the current recording before the default capture timeout passed in the startRecording function.\r\n     */\r\n    public stopRecording(): void {\r\n        if (!this._canvas || !this._mediaRecorder) {\r\n            return;\r\n        }\r\n\r\n        if (!this.isRecording) {\r\n            return;\r\n        }\r\n\r\n        this._canvas.isRecording = false;\r\n        this._mediaRecorder.stop();\r\n    }\r\n\r\n    /**\r\n     * Starts recording the canvas for a max duration specified in parameters.\r\n     * @param fileName Defines the name of the file to be downloaded when the recording stop.\r\n     * If null no automatic download will start and you can rely on the promise to get the data back.\r\n     * @param maxDuration Defines the maximum recording time in seconds.\r\n     * It defaults to 7 seconds. A value of zero will not stop automatically, you would need to call stopRecording manually.\r\n     * @returns A promise callback at the end of the recording with the video data in Blob.\r\n     */\r\n    public startRecording(fileName: Nullable<string> = \"babylonjs.webm\", maxDuration = 7): Promise<Blob> {\r\n        if (!this._canvas || !this._mediaRecorder) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"Recorder has already been disposed\";\r\n        }\r\n\r\n        if (this.isRecording) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"Recording already in progress\";\r\n        }\r\n\r\n        if (maxDuration > 0) {\r\n            setTimeout(() => {\r\n                this.stopRecording();\r\n            }, maxDuration * 1000);\r\n        }\r\n\r\n        this._fileName = fileName;\r\n        this._recordedChunks = [];\r\n        this._resolve = null;\r\n        this._reject = null;\r\n\r\n        this._canvas.isRecording = true;\r\n        this._mediaRecorder.start(this._options.recordChunckSize);\r\n\r\n        return new Promise<Blob>((resolve, reject) => {\r\n            this._resolve = resolve;\r\n            this._reject = reject;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Releases internal resources used during the recording.\r\n     */\r\n    public dispose() {\r\n        this._canvas = null;\r\n        this._mediaRecorder = null;\r\n\r\n        this._recordedChunks = [];\r\n        this._fileName = null;\r\n        this._resolve = null;\r\n        this._reject = null;\r\n    }\r\n\r\n    private _handleDataAvailable(event: any): void {\r\n        if (event.data.size > 0) {\r\n            this._recordedChunks.push(event.data);\r\n        }\r\n    }\r\n\r\n    private _handleError(event: ErrorEvent): void {\r\n        this.stopRecording();\r\n\r\n        if (this._reject) {\r\n            this._reject(event.error);\r\n        } else {\r\n            throw new event.error();\r\n        }\r\n    }\r\n\r\n    private _handleStop(): void {\r\n        this.stopRecording();\r\n\r\n        const superBuffer = new Blob(this._recordedChunks);\r\n        if (this._resolve) {\r\n            this._resolve(superBuffer);\r\n        }\r\n\r\n        window.URL.createObjectURL(superBuffer);\r\n\r\n        if (this._fileName) {\r\n            Tools.Download(superBuffer, this._fileName);\r\n        }\r\n    }\r\n}\r\n"]}