{"version": 3, "file": "pbrAnisotropicConfiguration.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/PBR/pbrAnisotropicConfiguration.ts"], "names": [], "mappings": ";AAAA,yDAAyD;AACzD,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAE5G,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACpD,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAK9D,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAKrD,OAAO,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAE3F;;GAEG;AACH,MAAM,OAAO,0BAA2B,SAAQ,eAAe;IAA/D;;QACW,gBAAW,GAAG,KAAK,CAAC;QACpB,wBAAmB,GAAG,KAAK,CAAC;QAC5B,gCAA2B,GAAG,CAAC,CAAC;QAChC,uBAAkB,GAAG,KAAK,CAAC;QAC3B,YAAO,GAAG,KAAK,CAAC;IAC3B,CAAC;CAAA;AAED;;GAEG;AACH,MAAM,OAAO,2BAA4B,SAAQ,kBAAkB;IAsB/D;;OAEG;IACH,IAAW,KAAK,CAAC,KAAa;QAC1B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;IAuBD,gBAAgB;IACT,gCAAgC;QACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,IAAI,CAAC,wCAAwC,EAAE,CAAC;IACpD,CAAC;IAKD,gBAAgB;IACT,4BAA4B;QAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,IAAI,CAAC,oCAAoC,EAAE,CAAC;IAChD,CAAC;IAED,YAAY,QAAyB,EAAE,eAAe,GAAG,IAAI;QACzD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,EAAE,GAAG,EAAE,IAAI,0BAA0B,EAAE,EAAE,eAAe,CAAC,CAAC;QA1EtF,eAAU,GAAG,KAAK,CAAC;QAC3B;;WAEG;QAGI,cAAS,GAAG,KAAK,CAAC;QAEzB;;WAEG;QAEI,cAAS,GAAW,CAAC,CAAC;QAE7B;;;WAGG;QAEI,cAAS,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAkB7B,aAAQ,GAA0B,IAAI,CAAC;QAC/C;;;;WAIG;QAGI,YAAO,GAA0B,IAAI,CAAC;QAErC,YAAO,GAAG,KAAK,CAAC;QACxB;;WAEG;QAGI,WAAM,GAAY,KAAK,CAAC;QAuB3B,IAAI,CAAC,wCAAwC,GAAG,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QAC9G,IAAI,CAAC,oCAAoC,GAAG,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;IAC3G,CAAC;IAEM,iBAAiB,CAAC,OAAmC,EAAE,KAAY;QACtE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,IAAI,KAAK,CAAC,eAAe,EAAE;gBACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,yBAAyB,EAAE;oBAC1D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE;wBACvC,OAAO,KAAK,CAAC;qBAChB;iBACJ;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,8BAA8B,CAAC,OAAmC,EAAE,KAAY,EAAE,IAAkB;QACvG,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;YACtC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE;gBAC1E,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACxB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;aAC1B;YAED,IAAI,OAAO,CAAC,iBAAiB,EAAE;gBAC3B,IAAI,KAAK,CAAC,eAAe,EAAE;oBACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,yBAAyB,EAAE;wBAC1D,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;qBAC5E;yBAAM;wBACH,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;qBACvC;iBACJ;aACJ;YAED,IAAI,OAAO,CAAC,aAAa,EAAE;gBACvB,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC;aAC7C;SACJ;aAAM;YACH,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;YAC5B,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACpC,OAAO,CAAC,2BAA2B,GAAG,CAAC,CAAC;YACxC,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;SACtC;IACL,CAAC;IAEM,cAAc,CAAC,aAA4B,EAAE,KAAY;QAC5D,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO;SACV;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAEzC,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YAC7D,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,yBAAyB,EAAE;gBAC1D,aAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACpG,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;aACjE;YAED,aAAa;YACb,aAAa,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SACjG;QAED,WAAW;QACX,IAAI,KAAK,CAAC,eAAe,EAAE;YACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,yBAAyB,EAAE;gBAC1D,aAAa,CAAC,UAAU,CAAC,mBAAmB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aAChE;SACJ;IACL,CAAC;IAEM,UAAU,CAAC,OAAoB;QAClC,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,iBAAiB,CAAC,cAA6B;QAClD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACtC;IACL,CAAC;IAEM,cAAc,CAAC,WAA0B;QAC5C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAClF,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACnC;IACL,CAAC;IAEM,OAAO,CAAC,oBAA8B;QACzC,IAAI,oBAAoB,EAAE;YACtB,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;aAC3B;SACJ;IACL,CAAC;IAEM,YAAY;QACf,OAAO,6BAA6B,CAAC;IACzC,CAAC;IAEM,YAAY,CAAC,OAAmC,EAAE,SAA0B,EAAE,WAAmB;QACpG,IAAI,OAAO,CAAC,WAAW,EAAE;YACrB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,aAAa,CAAC,CAAC;SACvD;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAEM,WAAW,CAAC,QAAkB;QACjC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACvC,CAAC;IAEM,WAAW;QACd,OAAO;YACH,GAAG,EAAE;gBACD,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC9C,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBACnD,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;aACvD;SACJ,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QACnD,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAEpC,yBAAyB;QACzB,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;YAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACtB;IACL,CAAC;CACJ;AArNU;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;8DAC5B;AAMlB;IADN,SAAS,EAAE;8DACiB;AAOtB;IADN,kBAAkB,EAAE;8DACgB;AA0B9B;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;4DACR;AAQtC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,8BAA8B,CAAC;2DAClB", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { serialize, expandToProperty, serializeAsVector2, serializeAsTexture } from \"../../Misc/decorators\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport { VertexBuffer } from \"../../Buffers/buffer\";\r\nimport { Vector2 } from \"../../Maths/math.vector\";\r\nimport { MaterialFlags } from \"../../Materials/materialFlags\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { IAnimatable } from \"../../Animations/animatable.interface\";\r\nimport type { EffectFallbacks } from \"../effectFallbacks\";\r\nimport { MaterialPluginBase } from \"../materialPluginBase\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { MaterialDefines } from \"../materialDefines\";\r\n\r\nimport type { Scene } from \"../../scene\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport { BindTextureMatrix, PrepareDefinesForMergedUV } from \"../materialHelper.functions\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class MaterialAnisotropicDefines extends MaterialDefines {\r\n    public ANISOTROPIC = false;\r\n    public ANISOTROPIC_TEXTURE = false;\r\n    public ANISOTROPIC_TEXTUREDIRECTUV = 0;\r\n    public ANISOTROPIC_LEGACY = false;\r\n    public MAINUV1 = false;\r\n}\r\n\r\n/**\r\n * Plugin that implements the anisotropic component of the PBR material\r\n */\r\nexport class PBRAnisotropicConfiguration extends MaterialPluginBase {\r\n    private _isEnabled = false;\r\n    /**\r\n     * Defines if the anisotropy is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isEnabled = false;\r\n\r\n    /**\r\n     * Defines the anisotropy strength (between 0 and 1) it defaults to 1.\r\n     */\r\n    @serialize()\r\n    public intensity: number = 1;\r\n\r\n    /**\r\n     * Defines if the effect is along the tangents, bitangents or in between.\r\n     * By default, the effect is \"stretching\" the highlights along the tangents.\r\n     */\r\n    @serializeAsVector2()\r\n    public direction = new Vector2(1, 0);\r\n\r\n    /**\r\n     * Sets the anisotropy direction as an angle.\r\n     */\r\n    public set angle(value: number) {\r\n        this.direction.x = Math.cos(value);\r\n        this.direction.y = Math.sin(value);\r\n    }\r\n\r\n    /**\r\n     * Gets the anisotropy angle value in radians.\r\n     * @returns the anisotropy angle value in radians.\r\n     */\r\n    public get angle(): number {\r\n        return Math.atan2(this.direction.y, this.direction.x);\r\n    }\r\n\r\n    private _texture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the anisotropy values in a texture.\r\n     * rg is direction (like normal from -1 to 1)\r\n     * b is a intensity\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public texture: Nullable<BaseTexture> = null;\r\n\r\n    private _legacy = false;\r\n    /**\r\n     * Defines if the anisotropy is in legacy mode for backwards compatibility before 6.4.0.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public legacy: boolean = false;\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsTexturesDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsTexturesDirty(): void {\r\n        this._enable(this._isEnabled);\r\n        this._internalMarkAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsMiscDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsMiscDirty(): void {\r\n        this._enable(this._isEnabled);\r\n        this._internalMarkAllSubMeshesAsMiscDirty();\r\n    }\r\n\r\n    constructor(material: PBRBaseMaterial, addToPluginList = true) {\r\n        super(material, \"PBRAnisotropic\", 110, new MaterialAnisotropicDefines(), addToPluginList);\r\n\r\n        this._internalMarkAllSubMeshesAsTexturesDirty = material._dirtyCallbacks[Constants.MATERIAL_TextureDirtyFlag];\r\n        this._internalMarkAllSubMeshesAsMiscDirty = material._dirtyCallbacks[Constants.MATERIAL_MiscDirtyFlag];\r\n    }\r\n\r\n    public isReadyForSubMesh(defines: MaterialAnisotropicDefines, scene: Scene): boolean {\r\n        if (!this._isEnabled) {\r\n            return true;\r\n        }\r\n\r\n        if (defines._areTexturesDirty) {\r\n            if (scene.texturesEnabled) {\r\n                if (this._texture && MaterialFlags.AnisotropicTextureEnabled) {\r\n                    if (!this._texture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public prepareDefinesBeforeAttributes(defines: MaterialAnisotropicDefines, scene: Scene, mesh: AbstractMesh): void {\r\n        if (this._isEnabled) {\r\n            defines.ANISOTROPIC = this._isEnabled;\r\n            if (this._isEnabled && !mesh.isVerticesDataPresent(VertexBuffer.TangentKind)) {\r\n                defines._needUVs = true;\r\n                defines.MAINUV1 = true;\r\n            }\r\n\r\n            if (defines._areTexturesDirty) {\r\n                if (scene.texturesEnabled) {\r\n                    if (this._texture && MaterialFlags.AnisotropicTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._texture, defines, \"ANISOTROPIC_TEXTURE\");\r\n                    } else {\r\n                        defines.ANISOTROPIC_TEXTURE = false;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (defines._areMiscDirty) {\r\n                defines.ANISOTROPIC_LEGACY = this._legacy;\r\n            }\r\n        } else {\r\n            defines.ANISOTROPIC = false;\r\n            defines.ANISOTROPIC_TEXTURE = false;\r\n            defines.ANISOTROPIC_TEXTUREDIRECTUV = 0;\r\n            defines.ANISOTROPIC_LEGACY = false;\r\n        }\r\n    }\r\n\r\n    public bindForSubMesh(uniformBuffer: UniformBuffer, scene: Scene): void {\r\n        if (!this._isEnabled) {\r\n            return;\r\n        }\r\n\r\n        const isFrozen = this._material.isFrozen;\r\n\r\n        if (!uniformBuffer.useUbo || !isFrozen || !uniformBuffer.isSync) {\r\n            if (this._texture && MaterialFlags.AnisotropicTextureEnabled) {\r\n                uniformBuffer.updateFloat2(\"vAnisotropyInfos\", this._texture.coordinatesIndex, this._texture.level);\r\n                BindTextureMatrix(this._texture, uniformBuffer, \"anisotropy\");\r\n            }\r\n\r\n            // Anisotropy\r\n            uniformBuffer.updateFloat3(\"vAnisotropy\", this.direction.x, this.direction.y, this.intensity);\r\n        }\r\n\r\n        // Textures\r\n        if (scene.texturesEnabled) {\r\n            if (this._texture && MaterialFlags.AnisotropicTextureEnabled) {\r\n                uniformBuffer.setTexture(\"anisotropySampler\", this._texture);\r\n            }\r\n        }\r\n    }\r\n\r\n    public hasTexture(texture: BaseTexture): boolean {\r\n        if (this._texture === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public getActiveTextures(activeTextures: BaseTexture[]): void {\r\n        if (this._texture) {\r\n            activeTextures.push(this._texture);\r\n        }\r\n    }\r\n\r\n    public getAnimatables(animatables: IAnimatable[]): void {\r\n        if (this._texture && this._texture.animations && this._texture.animations.length > 0) {\r\n            animatables.push(this._texture);\r\n        }\r\n    }\r\n\r\n    public dispose(forceDisposeTextures?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            if (this._texture) {\r\n                this._texture.dispose();\r\n            }\r\n        }\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"PBRAnisotropicConfiguration\";\r\n    }\r\n\r\n    public addFallbacks(defines: MaterialAnisotropicDefines, fallbacks: EffectFallbacks, currentRank: number): number {\r\n        if (defines.ANISOTROPIC) {\r\n            fallbacks.addFallback(currentRank++, \"ANISOTROPIC\");\r\n        }\r\n        return currentRank;\r\n    }\r\n\r\n    public getSamplers(samplers: string[]): void {\r\n        samplers.push(\"anisotropySampler\");\r\n    }\r\n\r\n    public getUniforms(): { ubo?: Array<{ name: string; size: number; type: string }>; vertex?: string; fragment?: string } {\r\n        return {\r\n            ubo: [\r\n                { name: \"vAnisotropy\", size: 3, type: \"vec3\" },\r\n                { name: \"vAnisotropyInfos\", size: 2, type: \"vec2\" },\r\n                { name: \"anisotropyMatrix\", size: 16, type: \"mat4\" },\r\n            ],\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Parses a anisotropy Configuration from a serialized object.\r\n     * @param source - Serialized object.\r\n     * @param scene Defines the scene we are parsing for\r\n     * @param rootUrl Defines the rootUrl to load from\r\n     */\r\n    public parse(source: any, scene: Scene, rootUrl: string): void {\r\n        super.parse(source, scene, rootUrl);\r\n\r\n        // Backward compatibility\r\n        if (source.legacy === undefined) {\r\n            this.legacy = true;\r\n        }\r\n    }\r\n}\r\n"]}