{"version": 3, "file": "translationBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Meshes/Node/Blocks/Matrices/translationBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,qCAAqC,EAAE,MAAM,8CAA8C,CAAC;AAErG,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAEhE;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,iBAAiB;IACnD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAEZ,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,qCAAqC,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,MAAM,CAAC,CAAC;IAChF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa;QAChB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;YAC/B,MAAM,gBAAgB,GAAG,IAAI,kBAAkB,CAAC,aAAa,CAAC,CAAC;YAC/D,gBAAgB,CAAC,KAAK,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9C,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACvD;IACL,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;YACpC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,KAAK,CAAY,CAAC;YACnE,OAAO,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC;IACN,CAAC;CACJ;AAED,aAAa,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC", "sourcesContent": ["import { NodeGeometry<PERSON>lock } from \"../../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../../nodeGeometryBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"../../Enums/nodeGeometryConnectionPointTypes\";\r\nimport type { NodeGeometryBuildState } from \"../../nodeGeometryBuildState\";\r\nimport { GeometryInputBlock } from \"../geometryInputBlock\";\r\nimport { Matrix, Vector3 } from \"../../../../Maths/math.vector\";\r\n\r\n/**\r\n * Block used to get a translation matrix\r\n */\r\nexport class TranslationBlock extends NodeGeometryBlock {\r\n    /**\r\n     * Create a new TranslationBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"translation\", NodeGeometryBlockConnectionPointTypes.Vector3, false, Vector3.Zero());\r\n        this.registerOutput(\"matrix\", NodeGeometryBlockConnectionPointTypes.Matrix);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"TranslationBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the translation input component\r\n     */\r\n    public get translation(): NodeGeometryConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the matrix output component\r\n     */\r\n    public get matrix(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public autoConfigure() {\r\n        if (!this.translation.isConnected) {\r\n            const translationInput = new GeometryInputBlock(\"Translation\");\r\n            translationInput.value = new Vector3(0, 0, 0);\r\n            translationInput.output.connectTo(this.translation);\r\n        }\r\n    }\r\n\r\n    protected _buildBlock(state: NodeGeometryBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        this.matrix._storedFunction = (state) => {\r\n            const value = this.translation.getConnectedValue(state) as Vector3;\r\n            return Matrix.Translation(value.x, value.y, value.z);\r\n        };\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.TranslationBlock\", TranslationBlock);\r\n"]}