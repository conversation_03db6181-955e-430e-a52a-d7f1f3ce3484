{"version": 3, "file": "webXRGenericMotionController.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/motionController/webXRGenericMotionController.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAGhF,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAErD;;GAEG;AACH,MAAM,OAAO,mCAAoC,SAAQ,6BAA6B;IAQlF,YAAY,KAAY,EAAE,aAA6C,EAAE,UAAsC;QAC3G,KAAK,CAAC,KAAK,EAAE,oBAAoB,CAAC,UAAU,CAAC,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;QAHvE,cAAS,GAAG,mCAAmC,CAAC,SAAS,CAAC;IAIjE,CAAC;IAES,mBAAmB;QACzB,OAAO;YACH,QAAQ,EAAE,iBAAiB;YAC3B,IAAI,EAAE,4CAA4C;SACrD,CAAC;IACN,CAAC;IAES,2BAA2B;QACjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,6DAA6D;IACnD,mBAAmB,CAAC,MAAsB;QAChD,gBAAgB;IACpB,CAAC;IAES,YAAY,CAAC,MAAsB;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAE7E,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACpB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACd,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACjC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACjF,CAAC;IAES,YAAY;QAClB,QAAQ;IACZ,CAAC;;AA1CD;;GAEG;AACW,6CAAS,GAAG,iBAAiB,AAApB,CAAqB;AA0ChD,gJAAgJ;AAChJ,MAAM,oBAAoB,GAA+B;IACrD,IAAI,EAAE;QACF,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE;YACR,gEAAgE;YAChE,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;SACJ;QACD,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE,sBAAsB;QACpC,SAAS,EAAE,UAAU;KACxB;IACD,KAAK,EAAE;QACH,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE;YACR,gEAAgE;YAChE,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;SACJ;QACD,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE,uBAAuB;QACrC,SAAS,EAAE,WAAW;KACzB;IACD,IAAI,EAAE;QACF,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE;YACR,gEAAgE;YAChE,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;SACJ;QACD,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE,sBAAsB;QACpC,SAAS,EAAE,UAAU;KACxB;CACJ,CAAC", "sourcesContent": ["import type { IMinimalMotionControllerObject, MotionControllerHandedness, IMotionControllerLayoutMap } from \"./webXRAbstractMotionController\";\r\nimport { WebXRAbstractMotionController } from \"./webXRAbstractMotionController\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Mesh } from \"../../Meshes/mesh\";\r\nimport { Quaternion } from \"../../Maths/math.vector\";\r\n\r\n/**\r\n * A generic trigger-only motion controller for WebXR\r\n */\r\nexport class WebXRGenericTriggerMotionController extends WebXRAbstractMotionController {\r\n    /**\r\n     * Static version of the profile id of this controller\r\n     */\r\n    public static ProfileId = \"generic-trigger\";\r\n\r\n    public profileId = WebXRGenericTriggerMotionController.ProfileId;\r\n\r\n    constructor(scene: Scene, gamepadObject: IMinimalMotionControllerObject, handedness: MotionControllerHandedness) {\r\n        super(scene, GenericTriggerLayout[handedness], gamepadObject, handedness);\r\n    }\r\n\r\n    protected _getFilenameAndPath(): { filename: string; path: string } {\r\n        return {\r\n            filename: \"generic.babylon\",\r\n            path: \"https://controllers.babylonjs.com/generic/\",\r\n        };\r\n    }\r\n\r\n    protected _getModelLoadingConstraints(): boolean {\r\n        return true;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _processLoadedModel(meshes: AbstractMesh[]): void {\r\n        // nothing to do\r\n    }\r\n\r\n    protected _setRootMesh(meshes: AbstractMesh[]): void {\r\n        this.rootMesh = new Mesh(this.profileId + \" \" + this.handedness, this.scene);\r\n\r\n        meshes.forEach((mesh) => {\r\n            mesh.isPickable = false;\r\n            if (!mesh.parent) {\r\n                mesh.setParent(this.rootMesh);\r\n            }\r\n        });\r\n\r\n        this.rootMesh.rotationQuaternion = Quaternion.FromEulerAngles(0, Math.PI, 0);\r\n    }\r\n\r\n    protected _updateModel(): void {\r\n        // no-op\r\n    }\r\n}\r\n\r\n// https://github.com/immersive-web/webxr-input-profiles/blob/master/packages/registry/profiles/generic/generic-trigger-touchpad-thumbstick.json\r\nconst GenericTriggerLayout: IMotionControllerLayoutMap = {\r\n    left: {\r\n        selectComponentId: \"xr-standard-trigger\",\r\n        components: {\r\n            // eslint-disable-next-line @typescript-eslint/naming-convention\r\n            \"xr-standard-trigger\": {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 0,\r\n                },\r\n                rootNodeName: \"xr_standard_trigger\",\r\n                visualResponses: {},\r\n            },\r\n        },\r\n        gamepadMapping: \"xr-standard\",\r\n        rootNodeName: \"generic-trigger-left\",\r\n        assetPath: \"left.glb\",\r\n    },\r\n    right: {\r\n        selectComponentId: \"xr-standard-trigger\",\r\n        components: {\r\n            // eslint-disable-next-line @typescript-eslint/naming-convention\r\n            \"xr-standard-trigger\": {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 0,\r\n                },\r\n                rootNodeName: \"xr_standard_trigger\",\r\n                visualResponses: {},\r\n            },\r\n        },\r\n        gamepadMapping: \"xr-standard\",\r\n        rootNodeName: \"generic-trigger-right\",\r\n        assetPath: \"right.glb\",\r\n    },\r\n    none: {\r\n        selectComponentId: \"xr-standard-trigger\",\r\n        components: {\r\n            // eslint-disable-next-line @typescript-eslint/naming-convention\r\n            \"xr-standard-trigger\": {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 0,\r\n                },\r\n                rootNodeName: \"xr_standard_trigger\",\r\n                visualResponses: {},\r\n            },\r\n        },\r\n        gamepadMapping: \"xr-standard\",\r\n        rootNodeName: \"generic-trigger-none\",\r\n        assetPath: \"none.glb\",\r\n    },\r\n};\r\n"]}