{"version": 3, "file": "rawTexture2DArray.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/rawTexture2DArray.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,4CAA4C,CAAC;AAKpD;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,OAAO;IAG1C;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,YACI,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,KAAa;IACb,6CAA6C;IACtC,MAAc,EACrB,KAAY,EACZ,kBAA2B,IAAI,EAC/B,UAAmB,KAAK,EACxB,eAAuB,OAAO,CAAC,sBAAsB,EACrD,WAAW,GAAG,SAAS,CAAC,wBAAwB,EAChD,aAAsB;QAEtB,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;QARvC,WAAM,GAAN,MAAM,CAAQ;QAUrB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,uBAAuB,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;QAExK,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,IAAqB;QAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QACD,IAAI,CAAC,UAAU,EAAG,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,QAAS,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC5I,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,MAAM,CAAC,iBAAiB,CAC3B,IAAqB,EACrB,KAAa,EACb,MAAc,EACd,KAAa,EACb,KAAY,EACZ,kBAA2B,IAAI,EAC/B,UAAmB,KAAK,EACxB,eAAuB,SAAS,CAAC,8BAA8B,EAC/D,OAAe,SAAS,CAAC,wBAAwB;QAEjD,OAAO,IAAI,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,kBAAkB,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;IAChJ,CAAC;CACJ", "sourcesContent": ["import { Texture } from \"./texture\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport \"../../Engines/Extensions/engine.rawTexture\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\nimport type { Scene } from \"../../scene\";\r\n\r\n/**\r\n * Class used to store 2D array textures containing user data\r\n */\r\nexport class RawTexture2DArray extends Texture {\r\n    private _depth: number;\r\n\r\n    /**\r\n     * Gets the number of layers of the texture\r\n     */\r\n    public get depth() {\r\n        return this._depth;\r\n    }\r\n\r\n    /**\r\n     * Create a new RawTexture2DArray\r\n     * @param data defines the data of the texture\r\n     * @param width defines the width of the texture\r\n     * @param height defines the height of the texture\r\n     * @param depth defines the number of layers of the texture\r\n     * @param format defines the texture format to use\r\n     * @param scene defines the hosting scene\r\n     * @param generateMipMaps defines a boolean indicating if mip levels should be generated (true by default)\r\n     * @param invertY defines if texture must be stored with Y axis inverted\r\n     * @param samplingMode defines the sampling mode to use (Texture.TRILINEAR_SAMPLINGMODE by default)\r\n     * @param textureType defines the texture Type (Engine.TEXTURETYPE_UNSIGNED_INT, Engine.TEXTURETYPE_FLOAT...)\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     */\r\n    constructor(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        depth: number,\r\n        /** Gets or sets the texture format to use */\r\n        public format: number,\r\n        scene: Scene,\r\n        generateMipMaps: boolean = true,\r\n        invertY: boolean = false,\r\n        samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE,\r\n        textureType = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        creationFlags?: number\r\n    ) {\r\n        super(null, scene, !generateMipMaps, invertY);\r\n\r\n        this._texture = scene.getEngine().createRawTexture2DArray(data, width, height, depth, format, generateMipMaps, invertY, samplingMode, null, textureType, creationFlags);\r\n\r\n        this._depth = depth;\r\n        this.is2DArray = true;\r\n    }\r\n\r\n    /**\r\n     * Update the texture with new data\r\n     * @param data defines the data to store in the texture\r\n     */\r\n    public update(data: ArrayBufferView): void {\r\n        if (!this._texture) {\r\n            return;\r\n        }\r\n        this._getEngine()!.updateRawTexture2DArray(this._texture, data, this._texture.format, this._texture!.invertY, null, this._texture.type);\r\n    }\r\n\r\n    /**\r\n     * Creates a RGBA texture from some data.\r\n     * @param data Define the texture data\r\n     * @param width Define the width of the texture\r\n     * @param height Define the height of the texture\r\n     * @param depth defines the number of layers of the texture\r\n     * @param scene defines the scene the texture will belong to\r\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\r\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\r\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\r\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\r\n     * @returns the RGBA texture\r\n     */\r\n    public static CreateRGBATexture(\r\n        data: ArrayBufferView,\r\n        width: number,\r\n        height: number,\r\n        depth: number,\r\n        scene: Scene,\r\n        generateMipMaps: boolean = true,\r\n        invertY: boolean = false,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        type: number = Constants.TEXTURETYPE_UNSIGNED_INT\r\n    ): RawTexture2DArray {\r\n        return new RawTexture2DArray(data, width, height, depth, Constants.TEXTUREFORMAT_RGBA, scene, generateMipMaps, invertY, samplingMode, type);\r\n    }\r\n}\r\n"]}