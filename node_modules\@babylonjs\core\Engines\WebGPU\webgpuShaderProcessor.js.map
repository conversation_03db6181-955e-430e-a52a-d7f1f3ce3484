{"version": 3, "file": "webgpuShaderProcessor.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuShaderProcessor.ts"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,wCAAwC;AACxC,OAAO,EAAE,MAAM,EAAE,6BAAyB;AAC1C,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAGhE,OAAO,KAAK,eAAe,MAAM,mBAAmB,CAAC;AAGrD,gBAAgB;AAChB,MAAM,OAAgB,qBAAqB;IAA3C;QA4EW,mBAAc,GAAG,cAAc,CAAC,IAAI,CAAC;QAE5C,sKAAsK;QACtK,qDAAqD;QAC9C,yCAAoC,GAA+B,EAAE,CAAC;IAsOjF,CAAC;IA/Na,wBAAwB,CAAC,IAAY,EAAE,WAAmB,EAAE,aAAwC;QAC1G,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,CAAC,IAAI,EAAE,WAAW,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;QAEnF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5E,IAAI,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;gBACjE,OAAO;aACV;SACJ;QAED,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAChD,IAAI;YACJ,IAAI,EAAE,WAAW;YACjB,MAAM;SACT,CAAC,CAAC;IACP,CAAC;IAES,iBAAiB;QACvB,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,MAAM,EAAE;YACxD,OAAO,EAAE,CAAC;SACb;QACD,MAAM,IAAI,GAAG,qBAAqB,CAAC,gBAAgB,CAAC;QACpD,IAAI,YAAY,GAAG,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACxE,IAAI,CAAC,YAAY,EAAE;YACf,YAAY,GAAG;gBACX,OAAO,EAAE,IAAI,CAAC,wBAAwB,CAAC,qBAAqB,EAAE;aACjE,CAAC;YACF,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;YACpE,IAAI,CAAC,4BAA4B,CAAC,IAAI,EAAE,YAAY,EAAE,eAAe,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACvG,IAAI,CAAC,4BAA4B,CAAC,IAAI,EAAE,YAAY,EAAE,eAAe,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAC3G;QAED,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;IAC7D,CAAC;IAES,oBAAoB;QAC1B,8EAA8E;QAC9E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClF,MAAM,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC9E,IAAI,aAAa,KAAK,SAAS,EAAE;gBAC7B,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;gBAC7D,SAAS;aACZ;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzE,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;gBAC3F,MAAM,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC3H,IAAI,KAAK,EAAE;oBACP,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,cAAc,EAAE;wBAChE,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,IAAI,CAAC,oBAAqB,CAAC,CAAC;qBAC1E;yBAAM,IAAI,KAAK,CAAC,OAAO,EAAE;wBACtB,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBACzD;yBAAM,IAAI,KAAK,CAAC,MAAM,EAAE;wBACrB,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBACxD;iBACJ;aACJ;SACJ;IACL,CAAC;IAES,0BAA0B;QAChC,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC;QAExE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClF,MAAM,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAE9E,MAAM,OAAO,GAAwB,EAAE,CAAC;YACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEzE,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,eAAe,EAAE;oBACjF,OAAO,CAAC,IAAI,CAAC;wBACT,OAAO,EAAE,KAAK,CAAC,OAAO;wBACtB,QAAQ,EAAE,SAAgB;qBAC7B,CAAC,CAAC;iBACN;qBAAM,IAAI,KAAK,CAAC,MAAM,EAAE;oBACrB,OAAO,CAAC,IAAI,CAAC;wBACT,OAAO,EAAE,KAAK,CAAC,OAAO;wBACtB,QAAQ,EAAE;4BACN,MAAM,EAAE,SAAgB;4BACxB,MAAM,EAAE,CAAC;4BACT,IAAI,EAAE,CAAC;yBACV;qBACJ,CAAC,CAAC;iBACN;aACJ;YAED,gBAAgB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;SACjC;IACL,CAAC;IAES,6BAA6B,CACnC,IAAY,EACZ,WAAqC,EACrC,YAAoB,EACpB,SAA4C,EAC5C,MAAkC,EAClC,QAAiB;QAEjB,wCAAwC;QACxC,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtE,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,EAAE;YACnE,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YACtE,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;SAC3E;QACD,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,EAAE;YACnF,IAAI,GAAG,CAAC;YACR,IAAI,SAAS,KAAK,IAAI,EAAE;gBACpB,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;oBACxE,OAAO,EAAE,YAAY;oBACrB,UAAU,EAAE,CAAC;oBACb,eAAe,EAAE,EAAE;iBACtB,CAAC,CAAC;aACN;iBAAM,IAAI,MAAM,EAAE;gBACf,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;oBACxE,OAAO,EAAE,YAAY;oBACrB,UAAU,EAAE,CAAC;oBACb,cAAc,EAAE;wBACZ,MAAM,EAAE,eAAe,CAAC,oBAAoB,CAAC,SAAS;wBACtD,MAAM;wBACN,aAAa,EAAE,SAAS;qBAC3B;iBACJ,CAAC,CAAC;aACN;iBAAM;gBACH,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;oBACxE,OAAO,EAAE,YAAY;oBACrB,UAAU,EAAE,CAAC;oBACb,OAAO,EAAE;wBACL,UAAU,EAAE,WAAW,CAAC,UAAU;wBAClC,aAAa,EAAE,SAAS;wBACxB,YAAY,EAAE,KAAK;qBACtB;iBACJ,CAAC,CAAC;aACN;YACD,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC;YAC5E,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,EAAE,oBAAoB,EAAE,WAAW,EAAE,CAAC;SAClJ;QAED,YAAY,GAAG,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC;QACtG,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,UAAU,IAAI,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC;SACnI;aAAM;YACH,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,UAAU,IAAI,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC;SACrI;IACL,CAAC;IAES,6BAA6B,CAAC,IAAY,EAAE,WAAqC,EAAE,QAAiB;QAC1G,wCAAwC;QACxC,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC;QACvD,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,EAAE;YACnE,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YACtE,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;SAC3E;QACD,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,EAAE;YACnF,MAAM,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;gBAC9E,OAAO,EAAE,YAAY;gBACrB,UAAU,EAAE,CAAC;gBACb,OAAO,EAAE;oBACL,IAAI,EAAE,WAAW,CAAC,IAAI;iBACzB;aACJ,CAAC,CAAC;YACH,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;SAC/G;QAED,YAAY,GAAG,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC;QACtG,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,UAAU,IAAI,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC;SACnI;aAAM;YACH,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,UAAU,IAAI,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC;SACrI;IACL,CAAC;IAES,4BAA4B,CAAC,IAAY,EAAE,iBAA0C,EAAE,UAAgC,EAAE,QAAiB;QAChJ,wCAAwC;QACxC,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,iBAAiB,CAAC,OAAO,CAAC;QAC7D,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,EAAE;YACnE,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YACtE,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;SAC3E;QACD,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,EAAE;YACnF,MAAM,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;gBAC9E,OAAO,EAAE,YAAY;gBACrB,UAAU,EAAE,CAAC;gBACb,MAAM,EAAE;oBACJ,IAAI,EAAE,UAAU;iBACnB;aACJ,CAAC,CAAC;YACH,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;SAC/G;QAED,YAAY,GAAG,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC;QACtG,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,UAAU,IAAI,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC;SACnI;aAAM;YACH,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,UAAU,IAAI,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC;SACrI;IACL,CAAC;IAES,4BAA4B,CAAC,IAAY,EAAE,YAAoB,EAAE,YAAqB,EAAE,UAAmB;QACjH,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACrC,IAAI,GAAG,GAAG,CAAC,EAAE;YACT,MAAM,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;YAC7E,OAAO,IAAI,CAAC;SACf;QACD,IAAI,YAAY,EAAE;YACd,oCAAoC;YACpC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAE;YACzD,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE;gBACnB,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;gBACzC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gBACtC,IAAI,GAAG,KAAK,GAAG,YAAY,GAAG,KAAK,CAAC;aACvC;SACJ;QAED,IAAI,UAAU,EAAE;YACZ,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC/C,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;YAC3C,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC;SAC9B;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;;AApTsB,uCAAiB,GAAG,SAAS,AAAZ,CAAa;AAC9B,sCAAgB,GAAG,UAAU,AAAb,CAAc;AAC9B,sCAAgB,GAAG,WAAW,AAAd,CAAe;AAExC,kCAAY,GAA+B;IACrD,aAAa;IACb,IAAI,EAAE,CAAC;IACP,GAAG,EAAE,CAAC;IACN,KAAK,EAAE,CAAC;IACR,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;IACR,KAAK,EAAE,CAAC;IACR,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;IACR,KAAK,EAAE,CAAC;IACR,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;IACR,KAAK,EAAE,CAAC;IACR,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,EAAE;IACR,IAAI,EAAE,EAAE;IAER,aAAa;IACb,GAAG,EAAE,CAAC;IACN,GAAG,EAAE,CAAC;IACN,GAAG,EAAE,CAAC;IACN,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;CACb,AAzByB,CAyBxB;AAEF,gEAAgE;AAC/C,wDAAkC,GAA8B;IAC7E,SAAS,EAAE,WAAW;IACtB,cAAc,EAAE,gBAAgB;IAChC,eAAe,EAAE,iBAAiB;IAClC,oBAAoB,EAAE,sBAAsB;IAC5C,WAAW,EAAE,aAAa;IAC1B,SAAS,EAAE,WAAW;CACzB,AAPkD,CAOjD;AAEF,gEAAgE;AAC/C,oDAA8B,GAA8B;IACzE,SAAS,EAAE,WAAW;IACtB,cAAc,EAAE,gBAAgB;IAChC,eAAe,EAAE,WAAW;IAC5B,oBAAoB,EAAE,gBAAgB;IACtC,WAAW,EAAE,aAAa;IAC1B,gBAAgB,EAAE,kBAAkB;IACpC,SAAS,EAAE,WAAW;CACzB,AAR8C,CAQ7C;AAEF,gEAAgE;AAC/C,iEAA2C,GAA+C;IACvG,WAAW,EAAE,eAAe,CAAC,oBAAoB,CAAC,IAAI;IACtD,gBAAgB,EAAE,eAAe,CAAC,oBAAoB,CAAC,SAAS;IAChE,SAAS,EAAE,eAAe,CAAC,oBAAoB,CAAC,GAAG;IACnD,cAAc,EAAE,eAAe,CAAC,oBAAoB,CAAC,QAAQ;IAC7D,SAAS,EAAE,eAAe,CAAC,oBAAoB,CAAC,GAAG;CACtD,AAN2D,CAM1D;AAEF,uFAAuF;AACvF,gEAAgE;AAC/C,oDAA8B,GAA8B;IACzE,eAAe,EAAE,eAAe;IAChC,oBAAoB,EAAE,eAAe;CACxC,AAH8C,CAG7C;AAEF,gEAAgE;AAC/C,6DAAuC,GAA+B;IACnF,aAAa,EAAE,IAAI;IACnB,kBAAkB,EAAE,IAAI;IACxB,OAAO,EAAE,KAAK;CACjB,AAJuD,CAItD", "sourcesContent": ["/* eslint-disable baby<PERSON>js/available */\r\n/* eslint-disable jsdoc/require-jsdoc */\r\nimport { Logger } from \"core/Misc/logger\";\r\nimport { ShaderLanguage } from \"../../Materials/shaderLanguage\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { IShaderProcessor } from \"../Processors/iShaderProcessor\";\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport type { WebGPUSamplerDescription, WebGPUShaderProcessingContext, WebGPUTextureDescription, WebGPUBufferDescription } from \"./webgpuShaderProcessingContext\";\r\n\r\n/** @internal */\r\nexport abstract class WebGPUShaderProcessor implements IShaderProcessor {\r\n    public static readonly AutoSamplerSuffix = \"Sampler\";\r\n    public static readonly LeftOvertUBOName = \"LeftOver\";\r\n    public static readonly InternalsUBOName = \"Internals\";\r\n\r\n    public static UniformSizes: { [type: string]: number } = {\r\n        // GLSL types\r\n        bool: 1,\r\n        int: 1,\r\n        float: 1,\r\n        vec2: 2,\r\n        ivec2: 2,\r\n        uvec2: 2,\r\n        vec3: 3,\r\n        ivec3: 3,\r\n        uvec3: 3,\r\n        vec4: 4,\r\n        ivec4: 4,\r\n        uvec4: 4,\r\n        mat2: 4,\r\n        mat3: 12,\r\n        mat4: 16,\r\n\r\n        // WGSL types\r\n        i32: 1,\r\n        u32: 1,\r\n        f32: 1,\r\n        mat2x2: 4,\r\n        mat3x3: 12,\r\n        mat4x4: 16,\r\n    };\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected static _SamplerFunctionByWebGLSamplerType: { [key: string]: string } = {\r\n        sampler2D: \"sampler2D\",\r\n        sampler2DArray: \"sampler2DArray\",\r\n        sampler2DShadow: \"sampler2DShadow\",\r\n        sampler2DArrayShadow: \"sampler2DArrayShadow\",\r\n        samplerCube: \"samplerCube\",\r\n        sampler3D: \"sampler3D\",\r\n    };\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected static _TextureTypeByWebGLSamplerType: { [key: string]: string } = {\r\n        sampler2D: \"texture2D\",\r\n        sampler2DArray: \"texture2DArray\",\r\n        sampler2DShadow: \"texture2D\",\r\n        sampler2DArrayShadow: \"texture2DArray\",\r\n        samplerCube: \"textureCube\",\r\n        samplerCubeArray: \"textureCubeArray\",\r\n        sampler3D: \"texture3D\",\r\n    };\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected static _GpuTextureViewDimensionByWebGPUTextureType: { [key: string]: GPUTextureViewDimension } = {\r\n        textureCube: WebGPUConstants.TextureViewDimension.Cube,\r\n        textureCubeArray: WebGPUConstants.TextureViewDimension.CubeArray,\r\n        texture2D: WebGPUConstants.TextureViewDimension.E2d,\r\n        texture2DArray: WebGPUConstants.TextureViewDimension.E2dArray,\r\n        texture3D: WebGPUConstants.TextureViewDimension.E3d,\r\n    };\r\n\r\n    // if the webgl sampler type is not listed in this array, \"sampler\" is taken by default\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected static _SamplerTypeByWebGLSamplerType: { [key: string]: string } = {\r\n        sampler2DShadow: \"samplerShadow\",\r\n        sampler2DArrayShadow: \"samplerShadow\",\r\n    };\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected static _IsComparisonSamplerByWebGPUSamplerType: { [key: string]: boolean } = {\r\n        samplerShadow: true,\r\n        samplerArrayShadow: true,\r\n        sampler: false,\r\n    };\r\n\r\n    public shaderLanguage = ShaderLanguage.GLSL;\r\n\r\n    // this object is populated only with vertex kinds known by the engine (position, uv, ...) and only if the type of the corresponding vertex buffer is an integer type)\r\n    // if the type is a signed type, the value is negated\r\n    public vertexBufferKindToNumberOfComponents: { [kind: string]: number } = {};\r\n\r\n    protected _webgpuProcessingContext: WebGPUShaderProcessingContext;\r\n\r\n    protected abstract _getArraySize(name: string, type: string, preProcessors: { [key: string]: string }): [string, string, number];\r\n    protected abstract _generateLeftOverUBOCode(name: string, uniformBufferDescription: WebGPUBufferDescription): string;\r\n\r\n    protected _addUniformToLeftOverUBO(name: string, uniformType: string, preProcessors: { [key: string]: string }): void {\r\n        let length = 0;\r\n\r\n        [name, uniformType, length] = this._getArraySize(name, uniformType, preProcessors);\r\n\r\n        for (let i = 0; i < this._webgpuProcessingContext.leftOverUniforms.length; i++) {\r\n            if (this._webgpuProcessingContext.leftOverUniforms[i].name === name) {\r\n                return;\r\n            }\r\n        }\r\n\r\n        this._webgpuProcessingContext.leftOverUniforms.push({\r\n            name,\r\n            type: uniformType,\r\n            length,\r\n        });\r\n    }\r\n\r\n    protected _buildLeftOverUBO(): string {\r\n        if (!this._webgpuProcessingContext.leftOverUniforms.length) {\r\n            return \"\";\r\n        }\r\n        const name = WebGPUShaderProcessor.LeftOvertUBOName;\r\n        let availableUBO = this._webgpuProcessingContext.availableBuffers[name];\r\n        if (!availableUBO) {\r\n            availableUBO = {\r\n                binding: this._webgpuProcessingContext.getNextFreeUBOBinding(),\r\n            };\r\n            this._webgpuProcessingContext.availableBuffers[name] = availableUBO;\r\n            this._addBufferBindingDescription(name, availableUBO, WebGPUConstants.BufferBindingType.Uniform, true);\r\n            this._addBufferBindingDescription(name, availableUBO, WebGPUConstants.BufferBindingType.Uniform, false);\r\n        }\r\n\r\n        return this._generateLeftOverUBOCode(name, availableUBO);\r\n    }\r\n\r\n    protected _collectBindingNames(): void {\r\n        // collect all the binding names for faster processing in WebGPUCacheBindGroup\r\n        for (let i = 0; i < this._webgpuProcessingContext.bindGroupLayoutEntries.length; i++) {\r\n            const setDefinition = this._webgpuProcessingContext.bindGroupLayoutEntries[i];\r\n            if (setDefinition === undefined) {\r\n                this._webgpuProcessingContext.bindGroupLayoutEntries[i] = [];\r\n                continue;\r\n            }\r\n            for (let j = 0; j < setDefinition.length; j++) {\r\n                const entry = this._webgpuProcessingContext.bindGroupLayoutEntries[i][j];\r\n                const name = this._webgpuProcessingContext.bindGroupLayoutEntryInfo[i][entry.binding].name;\r\n                const nameInArrayOfTexture = this._webgpuProcessingContext.bindGroupLayoutEntryInfo[i][entry.binding].nameInArrayOfTexture;\r\n                if (entry) {\r\n                    if (entry.texture || entry.externalTexture || entry.storageTexture) {\r\n                        this._webgpuProcessingContext.textureNames.push(nameInArrayOfTexture!);\r\n                    } else if (entry.sampler) {\r\n                        this._webgpuProcessingContext.samplerNames.push(name);\r\n                    } else if (entry.buffer) {\r\n                        this._webgpuProcessingContext.bufferNames.push(name);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected _preCreateBindGroupEntries(): void {\r\n        const bindGroupEntries = this._webgpuProcessingContext.bindGroupEntries;\r\n\r\n        for (let i = 0; i < this._webgpuProcessingContext.bindGroupLayoutEntries.length; i++) {\r\n            const setDefinition = this._webgpuProcessingContext.bindGroupLayoutEntries[i];\r\n\r\n            const entries: GPUBindGroupEntry[] = [];\r\n            for (let j = 0; j < setDefinition.length; j++) {\r\n                const entry = this._webgpuProcessingContext.bindGroupLayoutEntries[i][j];\r\n\r\n                if (entry.sampler || entry.texture || entry.storageTexture || entry.externalTexture) {\r\n                    entries.push({\r\n                        binding: entry.binding,\r\n                        resource: undefined as any,\r\n                    });\r\n                } else if (entry.buffer) {\r\n                    entries.push({\r\n                        binding: entry.binding,\r\n                        resource: {\r\n                            buffer: undefined as any,\r\n                            offset: 0,\r\n                            size: 0,\r\n                        },\r\n                    });\r\n                }\r\n            }\r\n\r\n            bindGroupEntries[i] = entries;\r\n        }\r\n    }\r\n\r\n    protected _addTextureBindingDescription(\r\n        name: string,\r\n        textureInfo: WebGPUTextureDescription,\r\n        textureIndex: number,\r\n        dimension: Nullable<GPUTextureViewDimension>,\r\n        format: Nullable<GPUTextureFormat>,\r\n        isVertex: boolean\r\n    ): void {\r\n        // eslint-disable-next-line prefer-const\r\n        let { groupIndex, bindingIndex } = textureInfo.textures[textureIndex];\r\n        if (!this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex]) {\r\n            this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex] = [];\r\n            this._webgpuProcessingContext.bindGroupLayoutEntryInfo[groupIndex] = [];\r\n        }\r\n        if (!this._webgpuProcessingContext.bindGroupLayoutEntryInfo[groupIndex][bindingIndex]) {\r\n            let len;\r\n            if (dimension === null) {\r\n                len = this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex].push({\r\n                    binding: bindingIndex,\r\n                    visibility: 0,\r\n                    externalTexture: {},\r\n                });\r\n            } else if (format) {\r\n                len = this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex].push({\r\n                    binding: bindingIndex,\r\n                    visibility: 0,\r\n                    storageTexture: {\r\n                        access: WebGPUConstants.StorageTextureAccess.WriteOnly,\r\n                        format,\r\n                        viewDimension: dimension,\r\n                    },\r\n                });\r\n            } else {\r\n                len = this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex].push({\r\n                    binding: bindingIndex,\r\n                    visibility: 0,\r\n                    texture: {\r\n                        sampleType: textureInfo.sampleType,\r\n                        viewDimension: dimension,\r\n                        multisampled: false,\r\n                    },\r\n                });\r\n            }\r\n            const textureName = textureInfo.isTextureArray ? name + textureIndex : name;\r\n            this._webgpuProcessingContext.bindGroupLayoutEntryInfo[groupIndex][bindingIndex] = { name, index: len - 1, nameInArrayOfTexture: textureName };\r\n        }\r\n\r\n        bindingIndex = this._webgpuProcessingContext.bindGroupLayoutEntryInfo[groupIndex][bindingIndex].index;\r\n        if (isVertex) {\r\n            this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex][bindingIndex].visibility |= WebGPUConstants.ShaderStage.Vertex;\r\n        } else {\r\n            this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex][bindingIndex].visibility |= WebGPUConstants.ShaderStage.Fragment;\r\n        }\r\n    }\r\n\r\n    protected _addSamplerBindingDescription(name: string, samplerInfo: WebGPUSamplerDescription, isVertex: boolean): void {\r\n        // eslint-disable-next-line prefer-const\r\n        let { groupIndex, bindingIndex } = samplerInfo.binding;\r\n        if (!this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex]) {\r\n            this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex] = [];\r\n            this._webgpuProcessingContext.bindGroupLayoutEntryInfo[groupIndex] = [];\r\n        }\r\n        if (!this._webgpuProcessingContext.bindGroupLayoutEntryInfo[groupIndex][bindingIndex]) {\r\n            const len = this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex].push({\r\n                binding: bindingIndex,\r\n                visibility: 0,\r\n                sampler: {\r\n                    type: samplerInfo.type,\r\n                },\r\n            });\r\n            this._webgpuProcessingContext.bindGroupLayoutEntryInfo[groupIndex][bindingIndex] = { name, index: len - 1 };\r\n        }\r\n\r\n        bindingIndex = this._webgpuProcessingContext.bindGroupLayoutEntryInfo[groupIndex][bindingIndex].index;\r\n        if (isVertex) {\r\n            this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex][bindingIndex].visibility |= WebGPUConstants.ShaderStage.Vertex;\r\n        } else {\r\n            this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex][bindingIndex].visibility |= WebGPUConstants.ShaderStage.Fragment;\r\n        }\r\n    }\r\n\r\n    protected _addBufferBindingDescription(name: string, uniformBufferInfo: WebGPUBufferDescription, bufferType: GPUBufferBindingType, isVertex: boolean): void {\r\n        // eslint-disable-next-line prefer-const\r\n        let { groupIndex, bindingIndex } = uniformBufferInfo.binding;\r\n        if (!this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex]) {\r\n            this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex] = [];\r\n            this._webgpuProcessingContext.bindGroupLayoutEntryInfo[groupIndex] = [];\r\n        }\r\n        if (!this._webgpuProcessingContext.bindGroupLayoutEntryInfo[groupIndex][bindingIndex]) {\r\n            const len = this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex].push({\r\n                binding: bindingIndex,\r\n                visibility: 0,\r\n                buffer: {\r\n                    type: bufferType,\r\n                },\r\n            });\r\n            this._webgpuProcessingContext.bindGroupLayoutEntryInfo[groupIndex][bindingIndex] = { name, index: len - 1 };\r\n        }\r\n\r\n        bindingIndex = this._webgpuProcessingContext.bindGroupLayoutEntryInfo[groupIndex][bindingIndex].index;\r\n        if (isVertex) {\r\n            this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex][bindingIndex].visibility |= WebGPUConstants.ShaderStage.Vertex;\r\n        } else {\r\n            this._webgpuProcessingContext.bindGroupLayoutEntries[groupIndex][bindingIndex].visibility |= WebGPUConstants.ShaderStage.Fragment;\r\n        }\r\n    }\r\n\r\n    protected _injectStartingAndEndingCode(code: string, mainFuncDecl: string, startingCode?: string, endingCode?: string): string {\r\n        let idx = code.indexOf(mainFuncDecl);\r\n        if (idx < 0) {\r\n            Logger.Error(`No \"main\" function found in shader code! Processing aborted.`);\r\n            return code;\r\n        }\r\n        if (startingCode) {\r\n            // eslint-disable-next-line no-empty\r\n            while (idx++ < code.length && code.charAt(idx) != \"{\") {}\r\n            if (idx < code.length) {\r\n                const part1 = code.substring(0, idx + 1);\r\n                const part2 = code.substring(idx + 1);\r\n                code = part1 + startingCode + part2;\r\n            }\r\n        }\r\n\r\n        if (endingCode) {\r\n            const lastClosingCurly = code.lastIndexOf(\"}\");\r\n            code = code.substring(0, lastClosingCurly);\r\n            code += endingCode + \"\\n}\";\r\n        }\r\n\r\n        return code;\r\n    }\r\n}\r\n"]}