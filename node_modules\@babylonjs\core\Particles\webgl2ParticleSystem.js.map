{"version": 3, "file": "webgl2ParticleSystem.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Particles/webgl2ParticleSystem.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAG7C,OAAO,EAAE,qBAAqB,EAAE,MAAM,sCAAsC,CAAC;AAI7E,OAAO,EAAE,iCAAiC,EAAE,MAAM,gDAAgD,CAAC;AACnG,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAElD,OAAO,wCAAwC,CAAC;AAChD,OAAO,sCAAsC,CAAC;AAI9C,gBAAgB;AAChB,MAAM,OAAO,oBAAoB;IAW7B,YAAY,MAAyB,EAAE,MAAkB;QANjD,eAAU,GAA6B,EAAE,CAAC;QAC1C,eAAU,GAA6B,EAAE,CAAC;QAGlC,sBAAiB,GAAG,KAAK,CAAC;QAGtC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,CAAC,oBAAoB,GAAG;YACxB,UAAU,EAAE;gBACR,UAAU;gBACV,iBAAiB;gBACjB,KAAK;gBACL,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,WAAW;gBACX,kBAAkB;gBAClB,OAAO;gBACP,WAAW;gBACX,iBAAiB;gBACjB,mBAAmB;gBACnB,mBAAmB;aACtB;YACD,aAAa,EAAE;gBACX,cAAc;gBACd,WAAW;gBACX,WAAW;gBACX,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,WAAW;gBACX,YAAY;gBACZ,SAAS;gBACT,WAAW;gBACX,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,QAAQ;gBACR,qBAAqB;gBACrB,QAAQ;gBACR,WAAW;gBACX,YAAY;gBACZ,YAAY;gBACZ,aAAa;gBACb,WAAW;gBACX,eAAe;gBACf,sBAAsB;aACzB;YACD,mBAAmB,EAAE,EAAE;YACvB,QAAQ,EAAE;gBACN,eAAe;gBACf,gBAAgB;gBAChB,qBAAqB;gBACrB,6BAA6B;gBAC7B,yBAAyB;gBACzB,8BAA8B;gBAC9B,cAAc;gBACd,qBAAqB;aACxB;YACD,OAAO,EAAE,EAAE;YACX,SAAS,EAAE,IAAI;YACf,UAAU,EAAE,IAAI;YAChB,OAAO,EAAE,IAAI;YACb,eAAe,EAAE,IAAI;YACrB,qBAAqB,EAAE,CAAC;YACxB,yBAAyB,EAAE,EAAE;SAChC,CAAC;IACN,CAAC;IAEM,WAAW;QACd,IAAI,CAAC,aAAa,GAAG,SAAgB,CAAC;QACtC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,CAAC;IAEM,qBAAqB;QACxB,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;IAChC,CAAC;IAEM,mBAAmB;QACtB,OAAO,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,KAAK,CAAC;IAClD,CAAC;IAEM,kBAAkB,CAAC,OAAe;QACrC,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,GAAG,CAAC,aAAa,CAAC,CAAC;QACtE,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnE,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpE,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpE,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpE,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEzE,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,YAAY,qBAAqB,EAAE;YACnE,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAClF;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YACtC,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACxE;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;YACjC,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SACnF;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACjF,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;SACpF;QAED,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAErE,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;YACtC,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACzE,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;gBACpC,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;aAClF;SACJ;QAED,IAAI,CAAC,oBAAoB,CAAC,OAAO,GAAG,OAAO,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAE/F,OAAO,IAAI,iCAAiC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACrE,CAAC;IAEM,mBAAmB,CAAC,YAAoB,EAAE,mBAAoD;QACjG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC;QAE1D,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,MAAO,CAAC,CAAC,CAAC;QAChJ,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEnC,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;IACpD,CAAC;IAEM,oBAAoB,CAAC,IAAc;QACtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,eAAe,CAAC,KAAa,EAAE,MAAc,EAAE,WAAiC;QACnF,IAAI,WAAW,EAAE;YACb,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;SAC5E;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;SACpE;IACL,CAAC;IAEM,uBAAuB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAiB,CAAC;QAEtC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE9C,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,8EAA8E,CAAC,CAAC;SACnG;IACL,CAAC;IAEM,oBAAoB,CAAC,KAAa,EAAE,YAAoB,EAAE,kBAA0B;QACvF,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAE9E,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;YACpC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;SAC5F;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,6BAA6B,EAAE;YAC5C,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,6BAA6B,EAAE,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;SAC5G;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE;YACxC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,yBAAyB,EAAE,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;SACpG;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,8BAA8B,EAAE;YAC7C,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,8BAA8B,EAAE,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;SAC9G;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;YACpC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;SAC5F;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YAC3B,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;SAC5E;QAED,kBAAkB;QAClB,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;QAEjE,SAAS;QACT,MAAM,MAAM,GAAG,IAAI,CAAC,OAAiB,CAAC;QAEtC,MAAM,CAAC,2BAA2B,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC;QAC7D,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACjC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,0BAA0B,EAAE,CAAC,EAAE,kBAAkB,CAAC,CAAC;QACnF,MAAM,CAAC,oBAAoB,EAAE,CAAC;QAC9B,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAChC,MAAM,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEM,cAAc,KAAU,CAAC;IAEzB,oBAAoB;QACvB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACzD,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;SACjE;QACD,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QAE3B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACzD,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;SACjE;QACD,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,CAAC;IAEO,gBAAgB,CAAC,MAAc;QACnC,MAAM,mBAAmB,GAAoC,EAAE,CAAC;QAChE,mBAAmB,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE9E,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,mBAAmB,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QACzE,MAAM,IAAI,CAAC,CAAC;QACZ,mBAAmB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3E,MAAM,IAAI,CAAC,CAAC;QACZ,mBAAmB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3E,MAAM,IAAI,CAAC,CAAC;QACZ,mBAAmB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3E,MAAM,IAAI,CAAC,CAAC;QACZ,mBAAmB,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QACrF,MAAM,IAAI,CAAC,CAAC;QAEZ,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,YAAY,qBAAqB,EAAE;YACnE,mBAAmB,CAAC,iBAAiB,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;YACjG,MAAM,IAAI,CAAC,CAAC;SACf;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YACtC,mBAAmB,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;YAC7E,MAAM,IAAI,CAAC,CAAC;SACf;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;YACjC,mBAAmB,CAAC,kBAAkB,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;YACnG,MAAM,IAAI,CAAC,CAAC;SACf;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YAC3B,mBAAmB,CAAC,mBAAmB,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;YACrG,MAAM,IAAI,CAAC,CAAC;YACZ,mBAAmB,CAAC,mBAAmB,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;YACrG,MAAM,IAAI,CAAC,CAAC;SACf;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,6BAA6B,EAAE;YAC5C,mBAAmB,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;YAC7E,MAAM,IAAI,CAAC,CAAC;SACf;aAAM;YACH,mBAAmB,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;YAC7E,MAAM,IAAI,CAAC,CAAC;SACf;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE;YACvC,mBAAmB,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;YACrF,MAAM,IAAI,CAAC,CAAC;YACZ,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;gBACpC,mBAAmB,CAAC,iBAAiB,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;gBACjG,MAAM,IAAI,CAAC,CAAC;aACf;SACJ;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAChG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEnC,OAAO,GAAG,CAAC;IACf,CAAC;CACJ;AAED,aAAa,CAAC,8BAA8B,EAAE,oBAAoB,CAAC,CAAC", "sourcesContent": ["import type { VertexB<PERSON>er, Buffer } from \"../Buffers/buffer\";\r\nimport type { ThinEngine } from \"../Engines/thinEngine\";\r\nimport type { IEffectCreationOptions } from \"../Materials/effect\";\r\nimport { Effect } from \"../Materials/effect\";\r\nimport type { IGPUParticleSystemPlatform } from \"./IGPUParticleSystemPlatform\";\r\n\r\nimport { CustomParticleEmitter } from \"./EmitterTypes/customParticleEmitter\";\r\nimport type { GPUParticleSystem } from \"./gpuParticleSystem\";\r\nimport type { DataArray, Nullable } from \"../types\";\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport { UniformBufferEffectCommonAccessor } from \"../Materials/uniformBufferEffectCommonAccessor\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\n\r\nimport \"../Shaders/gpuUpdateParticles.fragment\";\r\nimport \"../Shaders/gpuUpdateParticles.vertex\";\r\n\r\nimport type { Engine } from \"../Engines/engine\";\r\n\r\n/** @internal */\r\nexport class WebGL2ParticleSystem implements IGPUParticleSystemPlatform {\r\n    private _parent: GPUParticleSystem;\r\n    private _engine: ThinEngine;\r\n    private _updateEffect: Effect;\r\n    private _updateEffectOptions: IEffectCreationOptions;\r\n    private _renderVAO: WebGLVertexArrayObject[] = [];\r\n    private _updateVAO: WebGLVertexArrayObject[] = [];\r\n    private _renderVertexBuffers: { [key: string]: VertexBuffer };\r\n\r\n    public readonly alignDataInBuffer = false;\r\n\r\n    constructor(parent: GPUParticleSystem, engine: ThinEngine) {\r\n        this._parent = parent;\r\n        this._engine = engine;\r\n\r\n        this._updateEffectOptions = {\r\n            attributes: [\r\n                \"position\",\r\n                \"initialPosition\",\r\n                \"age\",\r\n                \"life\",\r\n                \"seed\",\r\n                \"size\",\r\n                \"color\",\r\n                \"direction\",\r\n                \"initialDirection\",\r\n                \"angle\",\r\n                \"cellIndex\",\r\n                \"cellStartOffset\",\r\n                \"noiseCoordinates1\",\r\n                \"noiseCoordinates2\",\r\n            ],\r\n            uniformsNames: [\r\n                \"currentCount\",\r\n                \"timeDelta\",\r\n                \"emitterWM\",\r\n                \"lifeTime\",\r\n                \"color1\",\r\n                \"color2\",\r\n                \"sizeRange\",\r\n                \"scaleRange\",\r\n                \"gravity\",\r\n                \"emitPower\",\r\n                \"direction1\",\r\n                \"direction2\",\r\n                \"minEmitBox\",\r\n                \"maxEmitBox\",\r\n                \"radius\",\r\n                \"directionRandomizer\",\r\n                \"height\",\r\n                \"coneAngle\",\r\n                \"stopFactor\",\r\n                \"angleRange\",\r\n                \"radiusRange\",\r\n                \"cellInfos\",\r\n                \"noiseStrength\",\r\n                \"limitVelocityDamping\",\r\n            ],\r\n            uniformBuffersNames: [],\r\n            samplers: [\r\n                \"randomSampler\",\r\n                \"randomSampler2\",\r\n                \"sizeGradientSampler\",\r\n                \"angularSpeedGradientSampler\",\r\n                \"velocityGradientSampler\",\r\n                \"limitVelocityGradientSampler\",\r\n                \"noiseSampler\",\r\n                \"dragGradientSampler\",\r\n            ],\r\n            defines: \"\",\r\n            fallbacks: null,\r\n            onCompiled: null,\r\n            onError: null,\r\n            indexParameters: null,\r\n            maxSimultaneousLights: 0,\r\n            transformFeedbackVaryings: [],\r\n        };\r\n    }\r\n\r\n    public contextLost(): void {\r\n        this._updateEffect = undefined as any;\r\n        this._renderVAO.length = 0;\r\n        this._updateVAO.length = 0;\r\n    }\r\n\r\n    public isUpdateBufferCreated(): boolean {\r\n        return !!this._updateEffect;\r\n    }\r\n\r\n    public isUpdateBufferReady(): boolean {\r\n        return this._updateEffect?.isReady() ?? false;\r\n    }\r\n\r\n    public createUpdateBuffer(defines: string): UniformBufferEffectCommonAccessor {\r\n        this._updateEffectOptions.transformFeedbackVaryings = [\"outPosition\"];\r\n        this._updateEffectOptions.transformFeedbackVaryings.push(\"outAge\");\r\n        this._updateEffectOptions.transformFeedbackVaryings.push(\"outSize\");\r\n        this._updateEffectOptions.transformFeedbackVaryings.push(\"outLife\");\r\n        this._updateEffectOptions.transformFeedbackVaryings.push(\"outSeed\");\r\n        this._updateEffectOptions.transformFeedbackVaryings.push(\"outDirection\");\r\n\r\n        if (this._parent.particleEmitterType instanceof CustomParticleEmitter) {\r\n            this._updateEffectOptions.transformFeedbackVaryings.push(\"outInitialPosition\");\r\n        }\r\n\r\n        if (!this._parent._colorGradientsTexture) {\r\n            this._updateEffectOptions.transformFeedbackVaryings.push(\"outColor\");\r\n        }\r\n\r\n        if (!this._parent._isBillboardBased) {\r\n            this._updateEffectOptions.transformFeedbackVaryings.push(\"outInitialDirection\");\r\n        }\r\n\r\n        if (this._parent.noiseTexture) {\r\n            this._updateEffectOptions.transformFeedbackVaryings.push(\"outNoiseCoordinates1\");\r\n            this._updateEffectOptions.transformFeedbackVaryings.push(\"outNoiseCoordinates2\");\r\n        }\r\n\r\n        this._updateEffectOptions.transformFeedbackVaryings.push(\"outAngle\");\r\n\r\n        if (this._parent.isAnimationSheetEnabled) {\r\n            this._updateEffectOptions.transformFeedbackVaryings.push(\"outCellIndex\");\r\n            if (this._parent.spriteRandomStartCell) {\r\n                this._updateEffectOptions.transformFeedbackVaryings.push(\"outCellStartOffset\");\r\n            }\r\n        }\r\n\r\n        this._updateEffectOptions.defines = defines;\r\n        this._updateEffect = new Effect(\"gpuUpdateParticles\", this._updateEffectOptions, this._engine);\r\n\r\n        return new UniformBufferEffectCommonAccessor(this._updateEffect);\r\n    }\r\n\r\n    public createVertexBuffers(updateBuffer: Buffer, renderVertexBuffers: { [key: string]: VertexBuffer }): void {\r\n        this._updateVAO.push(this._createUpdateVAO(updateBuffer));\r\n\r\n        this._renderVAO.push(this._engine.recordVertexArrayObject(renderVertexBuffers, null, this._parent._getWrapper(this._parent.blendMode).effect!));\r\n        this._engine.bindArrayBuffer(null);\r\n\r\n        this._renderVertexBuffers = renderVertexBuffers;\r\n    }\r\n\r\n    public createParticleBuffer(data: number[]): DataArray | DataBuffer {\r\n        return data;\r\n    }\r\n\r\n    public bindDrawBuffers(index: number, effect: Effect, indexBuffer: Nullable<DataBuffer>): void {\r\n        if (indexBuffer) {\r\n            this._engine.bindBuffers(this._renderVertexBuffers, indexBuffer, effect);\r\n        } else {\r\n            this._engine.bindVertexArrayObject(this._renderVAO[index], null);\r\n        }\r\n    }\r\n\r\n    public preUpdateParticleBuffer(): void {\r\n        const engine = this._engine as Engine;\r\n\r\n        this._engine.enableEffect(this._updateEffect);\r\n\r\n        if (!engine.setState) {\r\n            throw new Error(\"GPU particles cannot work without a full Engine. ThinEngine is not supported\");\r\n        }\r\n    }\r\n\r\n    public updateParticleBuffer(index: number, targetBuffer: Buffer, currentActiveCount: number): void {\r\n        this._updateEffect.setTexture(\"randomSampler\", this._parent._randomTexture);\r\n        this._updateEffect.setTexture(\"randomSampler2\", this._parent._randomTexture2);\r\n\r\n        if (this._parent._sizeGradientsTexture) {\r\n            this._updateEffect.setTexture(\"sizeGradientSampler\", this._parent._sizeGradientsTexture);\r\n        }\r\n\r\n        if (this._parent._angularSpeedGradientsTexture) {\r\n            this._updateEffect.setTexture(\"angularSpeedGradientSampler\", this._parent._angularSpeedGradientsTexture);\r\n        }\r\n\r\n        if (this._parent._velocityGradientsTexture) {\r\n            this._updateEffect.setTexture(\"velocityGradientSampler\", this._parent._velocityGradientsTexture);\r\n        }\r\n\r\n        if (this._parent._limitVelocityGradientsTexture) {\r\n            this._updateEffect.setTexture(\"limitVelocityGradientSampler\", this._parent._limitVelocityGradientsTexture);\r\n        }\r\n\r\n        if (this._parent._dragGradientsTexture) {\r\n            this._updateEffect.setTexture(\"dragGradientSampler\", this._parent._dragGradientsTexture);\r\n        }\r\n\r\n        if (this._parent.noiseTexture) {\r\n            this._updateEffect.setTexture(\"noiseSampler\", this._parent.noiseTexture);\r\n        }\r\n\r\n        // Bind source VAO\r\n        this._engine.bindVertexArrayObject(this._updateVAO[index], null);\r\n\r\n        // Update\r\n        const engine = this._engine as Engine;\r\n\r\n        engine.bindTransformFeedbackBuffer(targetBuffer.getBuffer());\r\n        engine.setRasterizerState(false);\r\n        engine.beginTransformFeedback(true);\r\n        engine.drawArraysType(Constants.MATERIAL_PointListDrawMode, 0, currentActiveCount);\r\n        engine.endTransformFeedback();\r\n        engine.setRasterizerState(true);\r\n        engine.bindTransformFeedbackBuffer(null);\r\n    }\r\n\r\n    public releaseBuffers(): void {}\r\n\r\n    public releaseVertexBuffers(): void {\r\n        for (let index = 0; index < this._updateVAO.length; index++) {\r\n            this._engine.releaseVertexArrayObject(this._updateVAO[index]);\r\n        }\r\n        this._updateVAO.length = 0;\r\n\r\n        for (let index = 0; index < this._renderVAO.length; index++) {\r\n            this._engine.releaseVertexArrayObject(this._renderVAO[index]);\r\n        }\r\n        this._renderVAO.length = 0;\r\n    }\r\n\r\n    private _createUpdateVAO(source: Buffer): WebGLVertexArrayObject {\r\n        const updateVertexBuffers: { [key: string]: VertexBuffer } = {};\r\n        updateVertexBuffers[\"position\"] = source.createVertexBuffer(\"position\", 0, 3);\r\n\r\n        let offset = 3;\r\n        updateVertexBuffers[\"age\"] = source.createVertexBuffer(\"age\", offset, 1);\r\n        offset += 1;\r\n        updateVertexBuffers[\"size\"] = source.createVertexBuffer(\"size\", offset, 3);\r\n        offset += 3;\r\n        updateVertexBuffers[\"life\"] = source.createVertexBuffer(\"life\", offset, 1);\r\n        offset += 1;\r\n        updateVertexBuffers[\"seed\"] = source.createVertexBuffer(\"seed\", offset, 4);\r\n        offset += 4;\r\n        updateVertexBuffers[\"direction\"] = source.createVertexBuffer(\"direction\", offset, 3);\r\n        offset += 3;\r\n\r\n        if (this._parent.particleEmitterType instanceof CustomParticleEmitter) {\r\n            updateVertexBuffers[\"initialPosition\"] = source.createVertexBuffer(\"initialPosition\", offset, 3);\r\n            offset += 3;\r\n        }\r\n\r\n        if (!this._parent._colorGradientsTexture) {\r\n            updateVertexBuffers[\"color\"] = source.createVertexBuffer(\"color\", offset, 4);\r\n            offset += 4;\r\n        }\r\n\r\n        if (!this._parent._isBillboardBased) {\r\n            updateVertexBuffers[\"initialDirection\"] = source.createVertexBuffer(\"initialDirection\", offset, 3);\r\n            offset += 3;\r\n        }\r\n\r\n        if (this._parent.noiseTexture) {\r\n            updateVertexBuffers[\"noiseCoordinates1\"] = source.createVertexBuffer(\"noiseCoordinates1\", offset, 3);\r\n            offset += 3;\r\n            updateVertexBuffers[\"noiseCoordinates2\"] = source.createVertexBuffer(\"noiseCoordinates2\", offset, 3);\r\n            offset += 3;\r\n        }\r\n\r\n        if (this._parent._angularSpeedGradientsTexture) {\r\n            updateVertexBuffers[\"angle\"] = source.createVertexBuffer(\"angle\", offset, 1);\r\n            offset += 1;\r\n        } else {\r\n            updateVertexBuffers[\"angle\"] = source.createVertexBuffer(\"angle\", offset, 2);\r\n            offset += 2;\r\n        }\r\n\r\n        if (this._parent._isAnimationSheetEnabled) {\r\n            updateVertexBuffers[\"cellIndex\"] = source.createVertexBuffer(\"cellIndex\", offset, 1);\r\n            offset += 1;\r\n            if (this._parent.spriteRandomStartCell) {\r\n                updateVertexBuffers[\"cellStartOffset\"] = source.createVertexBuffer(\"cellStartOffset\", offset, 1);\r\n                offset += 1;\r\n            }\r\n        }\r\n\r\n        const vao = this._engine.recordVertexArrayObject(updateVertexBuffers, null, this._updateEffect);\r\n        this._engine.bindArrayBuffer(null);\r\n\r\n        return vao;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.WebGL2ParticleSystem\", WebGL2ParticleSystem);\r\n"]}