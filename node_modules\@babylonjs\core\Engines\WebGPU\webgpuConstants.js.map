{"version": 3, "file": "webgpuConstants.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuConstants.ts"], "names": [], "mappings": "AAAA,gBAAgB;AAChB,yCAAyC;AACzC,MAAM,CAAN,IAAY,eAGX;AAHD,WAAY,eAAe;IACvB,yCAAsB,CAAA;IACtB,uDAAoC,CAAA;AACxC,CAAC,EAHW,eAAe,KAAf,eAAe,QAG1B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,WAYX;AAZD,WAAY,WAAW;IACnB,sDAAuC,CAAA;IACvC,6DAA8C,CAAA;IAC9C,8DAA+C,CAAA;IAC/C,kEAAmD,CAAA;IACnD,kEAAmD,CAAA;IACnD,iDAAkC,CAAA;IAClC,gEAAiD,CAAA;IACjD,uCAAwB,CAAA;IACxB,mEAAoD,CAAA;IACpD,uDAAwC,CAAA;IACxC,uDAAwC,CAAA;AAC5C,CAAC,EAZW,WAAW,KAAX,WAAW,QAYtB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,cAIX;AAJD,WAAY,cAAc;IACtB,uCAAqB,CAAA;IACrB,qCAAmB,CAAA;IACnB,mCAAiB,CAAA;AACrB,CAAC,EAJW,cAAc,KAAd,cAAc,QAIzB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,WAWX;AAXD,WAAY,WAAW;IACnB,mDAAW,CAAA;IACX,qDAAY,CAAA;IACZ,mDAAW,CAAA;IACX,mDAAW,CAAA;IACX,gDAAU,CAAA;IACV,kDAAW,CAAA;IACX,oDAAY,CAAA;IACZ,qDAAa,CAAA;IACb,uDAAc,CAAA;IACd,+DAAkB,CAAA;AACtB,CAAC,EAXW,WAAW,KAAX,WAAW,QAWtB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,OAGX;AAHD,WAAY,OAAO;IACf,qCAAQ,CAAA;IACR,uCAAS,CAAA;AACb,CAAC,EAHW,OAAO,KAAP,OAAO,QAGlB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,gBAIX;AAJD,WAAY,gBAAgB;IACxB,8BAAU,CAAA;IACV,8BAAU,CAAA;IACV,8BAAU,CAAA;AACd,CAAC,EAJW,gBAAgB,KAAhB,gBAAgB,QAI3B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,YAMX;AAND,WAAY,YAAY;IACpB,qDAAW,CAAA;IACX,qDAAW,CAAA;IACX,mEAAkB,CAAA;IAClB,mEAAkB,CAAA;IAClB,wEAAqB,CAAA;AACzB,CAAC,EANW,YAAY,KAAZ,YAAY,QAMvB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,oBAOX;AAPD,WAAY,oBAAoB;IAC5B,kCAAU,CAAA;IACV,kCAAU,CAAA;IACV,6CAAqB,CAAA;IACrB,qCAAa,CAAA;IACb,gDAAwB,CAAA;IACxB,kCAAU,CAAA;AACd,CAAC,EAPW,oBAAoB,KAApB,oBAAoB,QAO/B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,aAIX;AAJD,WAAY,aAAa;IACrB,4BAAW,CAAA;IACX,6CAA4B,CAAA;IAC5B,yCAAwB,CAAA;AAC5B,CAAC,EAJW,aAAa,KAAb,aAAa,QAIxB;AAED;;;GAGG;AACH,MAAM,CAAN,IAAY,aAuHX;AAvHD,WAAY,aAAa;IACrB,gBAAgB;IAChB,oCAAmB,CAAA;IACnB,oCAAmB,CAAA;IACnB,kCAAiB,CAAA;IACjB,kCAAiB,CAAA;IAEjB,iBAAiB;IACjB,oCAAmB,CAAA;IACnB,oCAAmB,CAAA;IACnB,sCAAqB,CAAA;IACrB,sCAAqB,CAAA;IACrB,sCAAqB,CAAA;IACrB,oCAAmB,CAAA;IACnB,oCAAmB,CAAA;IAEnB,iBAAiB;IACjB,oCAAmB,CAAA;IACnB,oCAAmB,CAAA;IACnB,sCAAqB,CAAA;IACrB,sCAAqB,CAAA;IACrB,sCAAqB,CAAA;IACrB,wCAAuB,CAAA;IACvB,0CAAyB,CAAA;IACzB,mDAAkC,CAAA;IAClC,0CAAyB,CAAA;IACzB,wCAAuB,CAAA;IACvB,wCAAuB,CAAA;IACvB,0CAAyB,CAAA;IACzB,mDAAkC,CAAA;IAClC,wBAAwB;IACxB,8CAA6B,CAAA;IAC7B,4CAA2B,CAAA;IAC3B,8CAA6B,CAAA;IAC7B,gDAA+B,CAAA;IAE/B,iBAAiB;IACjB,sCAAqB,CAAA;IACrB,sCAAqB,CAAA;IACrB,wCAAuB,CAAA;IACvB,0CAAyB,CAAA;IACzB,0CAAyB,CAAA;IACzB,4CAA2B,CAAA;IAE3B,kBAAkB;IAClB,0CAAyB,CAAA;IACzB,0CAAyB,CAAA;IACzB,4CAA2B,CAAA;IAE3B,4BAA4B;IAC5B,sCAAqB,CAAA;IACrB,8CAA6B,CAAA;IAC7B,4CAA2B,CAAA;IAC3B,6DAA4C,CAAA;IAC5C,8CAA6B,CAAA;IAE7B,mEAAmE;IACnE,mEAAmE;IACnE,gDAA+B,CAAA;IAC/B,yDAAwC,CAAA;IACxC,gDAA+B,CAAA;IAC/B,yDAAwC,CAAA;IACxC,gDAA+B,CAAA;IAC/B,yDAAwC,CAAA;IACxC,0CAAyB,CAAA;IACzB,0CAAyB,CAAA;IACzB,4CAA2B,CAAA;IAC3B,4CAA2B,CAAA;IAC3B,kDAAiC,CAAA;IACjC,gDAA+B,CAAA;IAC/B,gDAA+B,CAAA;IAC/B,yDAAwC,CAAA;IAExC,uEAAuE;IACvE,mEAAmE;IACnE,iDAAgC,CAAA;IAChC,0DAAyC,CAAA;IACzC,qDAAoC,CAAA;IACpC,8DAA6C,CAAA;IAC7C,mDAAkC,CAAA;IAClC,4DAA2C,CAAA;IAC3C,6CAA4B,CAAA;IAC5B,6CAA4B,CAAA;IAC5B,+CAA8B,CAAA;IAC9B,+CAA8B,CAAA;IAE9B,uEAAuE;IACvE,mEAAmE;IACnE,gDAA+B,CAAA;IAC/B,yDAAwC,CAAA;IACxC,gDAA+B,CAAA;IAC/B,yDAAwC,CAAA;IACxC,gDAA+B,CAAA;IAC/B,yDAAwC,CAAA;IACxC,gDAA+B,CAAA;IAC/B,yDAAwC,CAAA;IACxC,gDAA+B,CAAA;IAC/B,yDAAwC,CAAA;IACxC,gDAA+B,CAAA;IAC/B,yDAAwC,CAAA;IACxC,gDAA+B,CAAA;IAC/B,yDAAwC,CAAA;IACxC,gDAA+B,CAAA;IAC/B,yDAAwC,CAAA;IACxC,kDAAiC,CAAA;IACjC,2DAA0C,CAAA;IAC1C,kDAAiC,CAAA;IACjC,2DAA0C,CAAA;IAC1C,kDAAiC,CAAA;IACjC,2DAA0C,CAAA;IAC1C,oDAAmC,CAAA;IACnC,6DAA4C,CAAA;IAC5C,oDAAmC,CAAA;IACnC,6DAA4C,CAAA;IAC5C,oDAAmC,CAAA;IACnC,6DAA4C,CAAA;IAE5C,kCAAkC;IAClC,+DAA8C,CAAA;AAClD,CAAC,EAvHW,aAAa,KAAb,aAAa,QAuHxB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,WAIX;AAJD,WAAY,WAAW;IACnB,4CAA6B,CAAA;IAC7B,gCAAiB,CAAA;IACjB,6CAA8B,CAAA;AAClC,CAAC,EAJW,WAAW,KAAX,WAAW,QAItB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,UAGX;AAHD,WAAY,UAAU;IAClB,iCAAmB,CAAA;IACnB,+BAAiB,CAAA;AACrB,CAAC,EAHW,UAAU,KAAV,UAAU,QAGrB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,gBAGX;AAHD,WAAY,gBAAgB;IACxB,uCAAmB,CAAA;IACnB,qCAAiB,CAAA;AACrB,CAAC,EAHW,gBAAgB,KAAhB,gBAAgB,QAG3B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,eASX;AATD,WAAY,eAAe;IACvB,kCAAe,CAAA;IACf,gCAAa,CAAA;IACb,kCAAe,CAAA;IACf,2CAAwB,CAAA;IACxB,sCAAmB,CAAA;IACnB,yCAAsB,CAAA;IACtB,iDAA8B,CAAA;IAC9B,oCAAiB,CAAA;AACrB,CAAC,EATW,eAAe,KAAf,eAAe,QAS1B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,WAIX;AAJD,WAAY,WAAW;IACnB,iDAAU,CAAA;IACV,qDAAY,CAAA;IACZ,mDAAW,CAAA;AACf,CAAC,EAJW,WAAW,KAAX,WAAW,QAItB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,iBAIX;AAJD,WAAY,iBAAiB;IACzB,wCAAmB,CAAA;IACnB,wCAAmB,CAAA;IACnB,0DAAqC,CAAA;AACzC,CAAC,EAJW,iBAAiB,KAAjB,iBAAiB,QAI5B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,kBAIX;AAJD,WAAY,kBAAkB;IAC1B,6CAAuB,CAAA;IACvB,oDAA8B,CAAA;IAC9B,+CAAyB,CAAA;AAC7B,CAAC,EAJW,kBAAkB,KAAlB,kBAAkB,QAI7B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,iBAMX;AAND,WAAY,iBAAiB;IACzB,oCAAe,CAAA;IACf,6DAAwC,CAAA;IACxC,oCAAe,CAAA;IACf,kCAAa,CAAA;IACb,kCAAa,CAAA;AACjB,CAAC,EANW,iBAAiB,KAAjB,iBAAiB,QAM5B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,oBAIX;AAJD,WAAY,oBAAoB;IAC5B,gDAAwB,CAAA;IACxB,8CAAsB,CAAA;IACtB,gDAAwB,CAAA;AAC5B,CAAC,EAJW,oBAAoB,KAApB,oBAAoB,QAI/B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,sBAIX;AAJD,WAAY,sBAAsB;IAC9B,yCAAe,CAAA;IACf,6CAAmB,CAAA;IACnB,uCAAa,CAAA;AACjB,CAAC,EAJW,sBAAsB,KAAtB,sBAAsB,QAIjC;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,mBAGX;AAHD,WAAY,mBAAmB;IAC3B,gDAAyB,CAAA;IACzB,4CAAqB,CAAA;AACzB,CAAC,EAHW,mBAAmB,KAAnB,mBAAmB,QAG9B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,cAEX;AAFD,WAAY,cAAc;IACtB,+BAAa,CAAA;AACjB,CAAC,EAFW,cAAc,KAAd,cAAc,QAEzB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,iBAMX;AAND,WAAY,iBAAiB;IACzB,6CAAwB,CAAA;IACxB,2CAAsB,CAAA;IACtB,6CAAwB,CAAA;IACxB,mDAA8B,CAAA;IAC9B,qDAAgC,CAAA;AACpC,CAAC,EANW,iBAAiB,KAAjB,iBAAiB,QAM5B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,SAGX;AAHD,WAAY,SAAS;IACjB,wBAAW,CAAA;IACX,sBAAS,CAAA;AACb,CAAC,EAHW,SAAS,KAAT,SAAS,QAGpB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,QAIX;AAJD,WAAY,QAAQ;IAChB,yBAAa,CAAA;IACb,2BAAe,CAAA;IACf,yBAAa,CAAA;AACjB,CAAC,EAJW,QAAQ,KAAR,QAAQ,QAInB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,UAMX;AAND,WAAY,UAAU;IAClB,yCAAO,CAAA;IACP,6CAAS,CAAA;IACT,2CAAQ,CAAA;IACR,6CAAS,CAAA;IACT,0CAAQ,CAAA;AACZ,CAAC,EANW,UAAU,KAAV,UAAU,QAMrB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,WAcX;AAdD,WAAY,WAAW;IACnB,4BAAa,CAAA;IACb,0BAAW,CAAA;IACX,0BAAW,CAAA;IACX,4CAA6B,CAAA;IAC7B,qCAAsB,CAAA;IACtB,uDAAwC,CAAA;IACxC,0BAAW,CAAA;IACX,4CAA6B,CAAA;IAC7B,qCAAsB,CAAA;IACtB,uDAAwC,CAAA;IACxC,wDAAyC,CAAA;IACzC,oCAAqB,CAAA;IACrB,sDAAuC,CAAA;AAC3C,CAAC,EAdW,WAAW,KAAX,WAAW,QActB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,cAMX;AAND,WAAY,cAAc;IACtB,6BAAW,CAAA;IACX,uCAAqB,CAAA;IACrB,sDAAoC,CAAA;IACpC,6BAAW,CAAA;IACX,6BAAW,CAAA;AACf,CAAC,EANW,cAAc,KAAd,cAAc,QAMzB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,gBASX;AATD,WAAY,gBAAgB;IACxB,iCAAa,CAAA;IACb,iCAAa,CAAA;IACb,uCAAmB,CAAA;IACnB,qCAAiB,CAAA;IACjB,sDAAkC,CAAA;IAClC,sDAAkC,CAAA;IAClC,oDAAgC,CAAA;IAChC,oDAAgC,CAAA;AACpC,CAAC,EATW,gBAAgB,KAAhB,gBAAgB,QAS3B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,WAGX;AAHD,WAAY,WAAW;IACnB,gCAAiB,CAAA;IACjB,gCAAiB,CAAA;AACrB,CAAC,EAHW,WAAW,KAAX,WAAW,QAGtB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,YAgCX;AAhCD,WAAY,YAAY;IACpB,mCAAmB,CAAA;IACnB,mCAAmB,CAAA;IACnB,mCAAmB,CAAA;IACnB,mCAAmB,CAAA;IACnB,qCAAqB,CAAA;IACrB,qCAAqB,CAAA;IACrB,qCAAqB,CAAA;IACrB,qCAAqB,CAAA;IACrB,qCAAqB,CAAA;IACrB,qCAAqB,CAAA;IACrB,qCAAqB,CAAA;IACrB,qCAAqB,CAAA;IACrB,uCAAuB,CAAA;IACvB,uCAAuB,CAAA;IACvB,uCAAuB,CAAA;IACvB,uCAAuB,CAAA;IACvB,uCAAuB,CAAA;IACvB,uCAAuB,CAAA;IACvB,mCAAmB,CAAA;IACnB,uCAAuB,CAAA;IACvB,uCAAuB,CAAA;IACvB,uCAAuB,CAAA;IACvB,iCAAiB,CAAA;IACjB,qCAAqB,CAAA;IACrB,qCAAqB,CAAA;IACrB,qCAAqB,CAAA;IACrB,iCAAiB,CAAA;IACjB,qCAAqB,CAAA;IACrB,qCAAqB,CAAA;IACrB,qCAAqB,CAAA;IACrB,mDAAmC,CAAA;AACvC,CAAC,EAhCW,YAAY,KAAZ,YAAY,QAgCvB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,cAGX;AAHD,WAAY,cAAc;IACtB,mCAAiB,CAAA;IACjB,uCAAqB,CAAA;AACzB,CAAC,EAHW,cAAc,KAAd,cAAc,QAGzB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,4BAGX;AAHD,WAAY,4BAA4B;IACpC,uDAAuB,CAAA;IACvB,2CAAW,CAAA;AACf,CAAC,EAHW,4BAA4B,KAA5B,4BAA4B,QAGvC;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,2BAGX;AAHD,WAAY,2BAA2B;IACnC,sDAAuB,CAAA;IACvB,0CAAW,CAAA;AACf,CAAC,EAHW,2BAA2B,KAA3B,2BAA2B,QAGtC;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,MAGX;AAHD,WAAY,MAAM;IACd,uBAAa,CAAA;IACb,yBAAe,CAAA;AACnB,CAAC,EAHW,MAAM,KAAN,MAAM,QAGjB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,OAGX;AAHD,WAAY,OAAO;IACf,0BAAe,CAAA;IACf,8BAAmB,CAAA;AACvB,CAAC,EAHW,OAAO,KAAP,OAAO,QAGlB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,SAGX;AAHD,WAAY,SAAS;IACjB,oCAAuB,CAAA;IACvB,oCAAuB,CAAA;AAC3B,CAAC,EAHW,SAAS,KAAT,SAAS,QAGpB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,eAGX;AAHD,WAAY,eAAe;IACvB,oCAAiB,CAAA;IACjB,kDAA+B,CAAA;AACnC,CAAC,EAHW,eAAe,KAAf,eAAe,QAG1B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,gBAGX;AAHD,WAAY,gBAAgB;IACxB,uCAAmB,CAAA;IACnB,2CAAuB,CAAA;AAC3B,CAAC,EAHW,gBAAgB,KAAhB,gBAAgB,QAG3B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,WAIX;AAJD,WAAY,WAAW;IACnB,wCAAyB,CAAA;IACzB,4CAA6B,CAAA;IAC7B,oCAAqB,CAAA;AACzB,CAAC,EAJW,WAAW,KAAX,WAAW,QAItB", "sourcesContent": ["/** @internal */\r\n// eslint-disable-next-line import/export\r\nexport enum PowerPreference {\r\n    LowPower = \"low-power\",\r\n    HighPerformance = \"high-performance\",\r\n}\r\n\r\n/** @internal */\r\nexport enum FeatureName {\r\n    DepthClipControl = \"depth-clip-control\",\r\n    Depth32FloatStencil8 = \"depth32float-stencil8\",\r\n    TextureCompressionBC = \"texture-compression-bc\",\r\n    TextureCompressionETC2 = \"texture-compression-etc2\",\r\n    TextureCompressionASTC = \"texture-compression-astc\",\r\n    TimestampQuery = \"timestamp-query\",\r\n    IndirectFirstInstance = \"indirect-first-instance\",\r\n    ShaderF16 = \"shader-f16\",\r\n    RG11B10UFloatRenderable = \"rg11b10ufloat-renderable\",\r\n    BGRA8UnormStorage = \"bgra8unorm-storage\",\r\n    Float32Filterable = \"float32-filterable\",\r\n}\r\n\r\n/** @internal */\r\nexport enum BufferMapState {\r\n    Unmapped = \"unmapped\",\r\n    Pending = \"pending\",\r\n    Mapped = \"mapped\",\r\n}\r\n\r\n/** @internal */\r\nexport enum BufferUsage {\r\n    MapRead = 1,\r\n    MapWrite = 2,\r\n    CopySrc = 4,\r\n    CopyDst = 8,\r\n    Index = 16,\r\n    Vertex = 32,\r\n    Uniform = 64,\r\n    Storage = 128,\r\n    Indirect = 256,\r\n    QueryResolve = 512,\r\n}\r\n\r\n/** @internal */\r\nexport enum MapMode {\r\n    Read = 1,\r\n    Write = 2,\r\n}\r\n\r\n/** @internal */\r\nexport enum TextureDimension {\r\n    E1d = \"1d\",\r\n    E2d = \"2d\",\r\n    E3d = \"3d\",\r\n}\r\n\r\n/** @internal */\r\nexport enum TextureUsage {\r\n    CopySrc = 1,\r\n    CopyDst = 2,\r\n    TextureBinding = 4,\r\n    StorageBinding = 8,\r\n    RenderAttachment = 16,\r\n}\r\n\r\n/** @internal */\r\nexport enum TextureViewDimension {\r\n    E1d = \"1d\",\r\n    E2d = \"2d\",\r\n    E2dArray = \"2d-array\",\r\n    Cube = \"cube\",\r\n    CubeArray = \"cube-array\",\r\n    E3d = \"3d\",\r\n}\r\n\r\n/** @internal */\r\nexport enum TextureAspect {\r\n    All = \"all\",\r\n    StencilOnly = \"stencil-only\",\r\n    DepthOnly = \"depth-only\",\r\n}\r\n\r\n/**\r\n * Comments taken from https://github.com/gfx-rs/wgpu/blob/master/wgpu-types/src/lib.rs\r\n * @internal\r\n */\r\nexport enum TextureFormat {\r\n    // 8-bit formats\r\n    R8Unorm = \"r8unorm\", // Red channel only. 8 bit integer per channel. [0, 255] converted to/from float [0, 1] in shader.\r\n    R8Snorm = \"r8snorm\", // Red channel only. 8 bit integer per channel. [-127, 127] converted to/from float [-1, 1] in shader.\r\n    R8Uint = \"r8uint\", // Red channel only. 8 bit integer per channel. Unsigned in shader.\r\n    R8Sint = \"r8sint\", // Red channel only. 8 bit integer per channel. Signed in shader.\r\n\r\n    // 16-bit formats\r\n    R16Uint = \"r16uint\", // Red channel only. 16 bit integer per channel. Unsigned in shader.\r\n    R16Sint = \"r16sint\", // Red channel only. 16 bit integer per channel. Signed in shader.\r\n    R16Float = \"r16float\", // Red channel only. 16 bit float per channel. Float in shader.\r\n    RG8Unorm = \"rg8unorm\", // Red and green channels. 8 bit integer per channel. [0, 255] converted to/from float [0, 1] in shader.\r\n    RG8Snorm = \"rg8snorm\", // Red and green channels. 8 bit integer per channel. [-127, 127] converted to/from float [-1, 1] in shader.\r\n    RG8Uint = \"rg8uint\", // Red and green channels. 8 bit integer per channel. Unsigned in shader.\r\n    RG8Sint = \"rg8sint\", // Red and green channels. 8 bit integer per channel. Signed in shader.\r\n\r\n    // 32-bit formats\r\n    R32Uint = \"r32uint\", // Red channel only. 32 bit integer per channel. Unsigned in shader.\r\n    R32Sint = \"r32sint\", // Red channel only. 32 bit integer per channel. Signed in shader.\r\n    R32Float = \"r32float\", // Red channel only. 32 bit float per channel. Float in shader.\r\n    RG16Uint = \"rg16uint\", // Red and green channels. 16 bit integer per channel. Unsigned in shader.\r\n    RG16Sint = \"rg16sint\", // Red and green channels. 16 bit integer per channel. Signed in shader.\r\n    RG16Float = \"rg16float\", // Red and green channels. 16 bit float per channel. Float in shader.\r\n    RGBA8Unorm = \"rgba8unorm\", // Red, green, blue, and alpha channels. 8 bit integer per channel. [0, 255] converted to/from float [0, 1] in shader.\r\n    RGBA8UnormSRGB = \"rgba8unorm-srgb\", // Red, green, blue, and alpha channels. 8 bit integer per channel. Srgb-color [0, 255] converted to/from linear-color float [0, 1] in shader.\r\n    RGBA8Snorm = \"rgba8snorm\", // Red, green, blue, and alpha channels. 8 bit integer per channel. [-127, 127] converted to/from float [-1, 1] in shader.\r\n    RGBA8Uint = \"rgba8uint\", // Red, green, blue, and alpha channels. 8 bit integer per channel. Unsigned in shader.\r\n    RGBA8Sint = \"rgba8sint\", // Red, green, blue, and alpha channels. 8 bit integer per channel. Signed in shader.\r\n    BGRA8Unorm = \"bgra8unorm\", // Blue, green, red, and alpha channels. 8 bit integer per channel. [0, 255] converted to/from float [0, 1] in shader.\r\n    BGRA8UnormSRGB = \"bgra8unorm-srgb\", // Blue, green, red, and alpha channels. 8 bit integer per channel. Srgb-color [0, 255] converted to/from linear-color float [0, 1] in shader.\r\n    // Packed 32-bit formats\r\n    RGB9E5UFloat = \"rgb9e5ufloat\", // Packed unsigned float with 9 bits mantisa for each RGB component, then a common 5 bits exponent\r\n    RGB10A2UINT = \"rgb10a2uint\", // Red, green, blue, and alpha channels. 10 bit integer for RGB channels, 2 bit integer for alpha channel. [0, 1023] ([0, 3] for alpha).\r\n    RGB10A2Unorm = \"rgb10a2unorm\", // Red, green, blue, and alpha channels. 10 bit integer for RGB channels, 2 bit integer for alpha channel. [0, 1023] ([0, 3] for alpha) converted to/from float [0, 1] in shader.\r\n    RG11B10UFloat = \"rg11b10ufloat\", // Red, green, and blue channels. 11 bit float with no sign bit for RG channels. 10 bit float with no sign bit for blue channel. Float in shader.\r\n\r\n    // 64-bit formats\r\n    RG32Uint = \"rg32uint\", // Red and green channels. 32 bit integer per channel. Unsigned in shader.\r\n    RG32Sint = \"rg32sint\", // Red and green channels. 32 bit integer per channel. Signed in shader.\r\n    RG32Float = \"rg32float\", // Red and green channels. 32 bit float per channel. Float in shader.\r\n    RGBA16Uint = \"rgba16uint\", // Red, green, blue, and alpha channels. 16 bit integer per channel. Unsigned in shader.\r\n    RGBA16Sint = \"rgba16sint\", // Red, green, blue, and alpha channels. 16 bit integer per channel. Signed in shader.\r\n    RGBA16Float = \"rgba16float\", // Red, green, blue, and alpha channels. 16 bit float per channel. Float in shader.\r\n\r\n    // 128-bit formats\r\n    RGBA32Uint = \"rgba32uint\", // Red, green, blue, and alpha channels. 32 bit integer per channel. Unsigned in shader.\r\n    RGBA32Sint = \"rgba32sint\", // Red, green, blue, and alpha channels. 32 bit integer per channel. Signed in shader.\r\n    RGBA32Float = \"rgba32float\", // Red, green, blue, and alpha channels. 32 bit float per channel. Float in shader.\r\n\r\n    // Depth and stencil formats\r\n    Stencil8 = \"stencil8\",\r\n    Depth16Unorm = \"depth16unorm\",\r\n    Depth24Plus = \"depth24plus\", // Special depth format with at least 24 bit integer depth.\r\n    Depth24PlusStencil8 = \"depth24plus-stencil8\", // Special depth/stencil format with at least 24 bit integer depth and 8 bits integer stencil.\r\n    Depth32Float = \"depth32float\", // Special depth format with 32 bit floating point depth.\r\n\r\n    // BC compressed formats usable if \"texture-compression-bc\" is both\r\n    // supported by the device/user agent and enabled in requestDevice.\r\n    BC1RGBAUnorm = \"bc1-rgba-unorm\", // 4x4 block compressed texture. 8 bytes per block (4 bit/px). 4 color + alpha pallet. 5 bit R + 6 bit G + 5 bit B + 1 bit alpha. Also known as DXT1.\r\n    BC1RGBAUnormSRGB = \"bc1-rgba-unorm-srgb\", // 4x4 block compressed texture. 8 bytes per block (4 bit/px). 4 color + alpha pallet. 5 bit R + 6 bit G + 5 bit B + 1 bit alpha. Also known as DXT1.\r\n    BC2RGBAUnorm = \"bc2-rgba-unorm\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). 4 color pallet. 5 bit R + 6 bit G + 5 bit B + 4 bit alpha. Also known as DXT3.\r\n    BC2RGBAUnormSRGB = \"bc2-rgba-unorm-srgb\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). 4 color pallet. 5 bit R + 6 bit G + 5 bit B + 4 bit alpha. Also known as DXT3.\r\n    BC3RGBAUnorm = \"bc3-rgba-unorm\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). 4 color pallet + 8 alpha pallet. 5 bit R + 6 bit G + 5 bit B + 8 bit alpha. Also known as DXT5.\r\n    BC3RGBAUnormSRGB = \"bc3-rgba-unorm-srgb\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). 4 color pallet + 8 alpha pallet. 5 bit R + 6 bit G + 5 bit B + 8 bit alpha. Also known as DXT5.\r\n    BC4RUnorm = \"bc4-r-unorm\", // 4x4 block compressed texture. 8 bytes per block (4 bit/px). 8 color pallet. 8 bit R. Also known as RGTC1.\r\n    BC4RSnorm = \"bc4-r-snorm\", // 4x4 block compressed texture. 8 bytes per block (4 bit/px). 8 color pallet. 8 bit R. Also known as RGTC1.\r\n    BC5RGUnorm = \"bc5-rg-unorm\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). 8 color red pallet + 8 color green pallet. 8 bit RG. Also known as RGTC2.\r\n    BC5RGSnorm = \"bc5-rg-snorm\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). 8 color red pallet + 8 color green pallet. 8 bit RG. Also known as RGTC2.\r\n    BC6HRGBUFloat = \"bc6h-rgb-ufloat\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). Variable sized pallet. 16 bit unsigned float RGB. Float in shader. Also known as BPTC (float).\r\n    BC6HRGBFloat = \"bc6h-rgb-float\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). Variable sized pallet. 16 bit signed float RGB. Float in shader. Also known as BPTC (float).\r\n    BC7RGBAUnorm = \"bc7-rgba-unorm\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). Variable sized pallet. 8 bit integer RGBA. Also known as BPTC (unorm).\r\n    BC7RGBAUnormSRGB = \"bc7-rgba-unorm-srgb\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). Variable sized pallet. 8 bit integer RGBA. Also known as BPTC (unorm).\r\n\r\n    // ETC2 compressed formats usable if \"texture-compression-etc2\" is both\r\n    // supported by the device/user agent and enabled in requestDevice.\r\n    ETC2RGB8Unorm = \"etc2-rgb8unorm\", // 4x4 block compressed texture. 8 bytes per block (4 bit/px). Complex pallet. 8 bit integer RGB.\r\n    ETC2RGB8UnormSRGB = \"etc2-rgb8unorm-srgb\", // 4x4 block compressed texture. 8 bytes per block (4 bit/px). Complex pallet. 8 bit integer RGB.\r\n    ETC2RGB8A1Unorm = \"etc2-rgb8a1unorm\", // 4x4 block compressed texture. 8 bytes per block (4 bit/px). Complex pallet. 8 bit integer RGB + 1 bit alpha.\r\n    ETC2RGB8A1UnormSRGB = \"etc2-rgb8a1unorm-srgb\", // 4x4 block compressed texture. 8 bytes per block (4 bit/px). Complex pallet. 8 bit integer RGB + 1 bit alpha.\r\n    ETC2RGBA8Unorm = \"etc2-rgba8unorm\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). Complex pallet. 8 bit integer RGB + 8 bit alpha.\r\n    ETC2RGBA8UnormSRGB = \"etc2-rgba8unorm-srgb\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). Complex pallet. 8 bit integer RGB + 8 bit alpha.\r\n    EACR11Unorm = \"eac-r11unorm\", // 4x4 block compressed texture. 8 bytes per block (4 bit/px). Complex pallet. 11 bit integer R.\r\n    EACR11Snorm = \"eac-r11snorm\", // 4x4 block compressed texture. 8 bytes per block (4 bit/px). Complex pallet. 11 bit integer R.\r\n    EACRG11Unorm = \"eac-rg11unorm\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). Complex pallet. 11 bit integer R + 11 bit integer G.\r\n    EACRG11Snorm = \"eac-rg11snorm\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). Complex pallet. 11 bit integer R + 11 bit integer G.\r\n\r\n    // ASTC compressed formats usable if \"texture-compression-astc\" is both\r\n    // supported by the device/user agent and enabled in requestDevice.\r\n    ASTC4x4Unorm = \"astc-4x4-unorm\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC4x4UnormSRGB = \"astc-4x4-unorm-srgb\", // 4x4 block compressed texture. 16 bytes per block (8 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC5x4Unorm = \"astc-5x4-unorm\", // 5x4 block compressed texture. 16 bytes per block (6.4 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC5x4UnormSRGB = \"astc-5x4-unorm-srgb\", // 5x4 block compressed texture. 16 bytes per block (6.4 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC5x5Unorm = \"astc-5x5-unorm\", // 5x5 block compressed texture. 16 bytes per block (5.12 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC5x5UnormSRGB = \"astc-5x5-unorm-srgb\", // 5x5 block compressed texture. 16 bytes per block (5.12 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC6x5Unorm = \"astc-6x5-unorm\", // 6x5 block compressed texture. 16 bytes per block (4.27 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC6x5UnormSRGB = \"astc-6x5-unorm-srgb\", // 6x5 block compressed texture. 16 bytes per block (4.27 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC6x6Unorm = \"astc-6x6-unorm\", // 6x6 block compressed texture. 16 bytes per block (3.56 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC6x6UnormSRGB = \"astc-6x6-unorm-srgb\", // 6x6 block compressed texture. 16 bytes per block (3.56 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC8x5Unorm = \"astc-8x5-unorm\", // 8x5 block compressed texture. 16 bytes per block (3.2 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC8x5UnormSRGB = \"astc-8x5-unorm-srgb\", // 8x5 block compressed texture. 16 bytes per block (3.2 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC8x6Unorm = \"astc-8x6-unorm\", // 8x6 block compressed texture. 16 bytes per block (2.67 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC8x6UnormSRGB = \"astc-8x6-unorm-srgb\", // 8x6 block compressed texture. 16 bytes per block (2.67 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC8x8Unorm = \"astc-8x8-unorm\", // 8x8 block compressed texture. 16 bytes per block (2 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC8x8UnormSRGB = \"astc-8x8-unorm-srgb\", // 8x8 block compressed texture. 16 bytes per block (2 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC10x5Unorm = \"astc-10x5-unorm\", // 10x5 block compressed texture. 16 bytes per block (2.56 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC10x5UnormSRGB = \"astc-10x5-unorm-srgb\", // 10x5 block compressed texture. 16 bytes per block (2.56 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC10x6Unorm = \"astc-10x6-unorm\", // 10x6 block compressed texture. 16 bytes per block (2.13 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC10x6UnormSRGB = \"astc-10x6-unorm-srgb\", // 10x6 block compressed texture. 16 bytes per block (2.13 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC10x8Unorm = \"astc-10x8-unorm\", // 10x8 block compressed texture. 16 bytes per block (1.6 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC10x8UnormSRGB = \"astc-10x8-unorm-srgb\", // 10x8 block compressed texture. 16 bytes per block (1.6 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC10x10Unorm = \"astc-10x10-unorm\", // 10x10 block compressed texture. 16 bytes per block (1.28 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC10x10UnormSRGB = \"astc-10x10-unorm-srgb\", // 10x10 block compressed texture. 16 bytes per block (1.28 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC12x10Unorm = \"astc-12x10-unorm\", // 12x10 block compressed texture. 16 bytes per block (1.07 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC12x10UnormSRGB = \"astc-12x10-unorm-srgb\", // 12x10 block compressed texture. 16 bytes per block (1.07 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC12x12Unorm = \"astc-12x12-unorm\", // 12x12 block compressed texture. 16 bytes per block (0.89 bit/px). Complex pallet. 8 bit integer RGBA.\r\n    ASTC12x12UnormSRGB = \"astc-12x12-unorm-srgb\", // 12x12 block compressed texture. 16 bytes per block (0.89 bit/px). Complex pallet. 8 bit integer RGBA.\r\n\r\n    // \"depth32float-stencil8\" feature\r\n    Depth32FloatStencil8 = \"depth32float-stencil8\",\r\n}\r\n\r\n/** @internal */\r\nexport enum AddressMode {\r\n    ClampToEdge = \"clamp-to-edge\",\r\n    Repeat = \"repeat\",\r\n    MirrorRepeat = \"mirror-repeat\",\r\n}\r\n\r\n/** @internal */\r\nexport enum FilterMode {\r\n    Nearest = \"nearest\",\r\n    Linear = \"linear\",\r\n}\r\n\r\n/** @internal */\r\nexport enum MipmapFilterMode {\r\n    Nearest = \"nearest\",\r\n    Linear = \"linear\",\r\n}\r\n\r\n/** @internal */\r\nexport enum CompareFunction {\r\n    Never = \"never\",\r\n    Less = \"less\",\r\n    Equal = \"equal\",\r\n    LessEqual = \"less-equal\",\r\n    Greater = \"greater\",\r\n    NotEqual = \"not-equal\",\r\n    GreaterEqual = \"greater-equal\",\r\n    Always = \"always\",\r\n}\r\n\r\n/** @internal */\r\nexport enum ShaderStage {\r\n    Vertex = 1,\r\n    Fragment = 2,\r\n    Compute = 4,\r\n}\r\n\r\n/** @internal */\r\nexport enum BufferBindingType {\r\n    Uniform = \"uniform\",\r\n    Storage = \"storage\",\r\n    ReadOnlyStorage = \"read-only-storage\",\r\n}\r\n\r\n/** @internal */\r\nexport enum SamplerBindingType {\r\n    Filtering = \"filtering\",\r\n    NonFiltering = \"non-filtering\",\r\n    Comparison = \"comparison\",\r\n}\r\n\r\n/** @internal */\r\nexport enum TextureSampleType {\r\n    Float = \"float\",\r\n    UnfilterableFloat = \"unfilterable-float\",\r\n    Depth = \"depth\",\r\n    Sint = \"sint\",\r\n    Uint = \"uint\",\r\n}\r\n\r\n/** @internal */\r\nexport enum StorageTextureAccess {\r\n    WriteOnly = \"write-only\",\r\n    ReadOnly = \"read-only\",\r\n    ReadWrite = \"read-write\",\r\n}\r\n\r\n/** @internal */\r\nexport enum CompilationMessageType {\r\n    Error = \"error\",\r\n    Warning = \"warning\",\r\n    Info = \"info\",\r\n}\r\n\r\n/** @internal */\r\nexport enum PipelineErrorReason {\r\n    Validation = \"validation\",\r\n    Internal = \"internal\",\r\n}\r\n\r\n/** @internal */\r\nexport enum AutoLayoutMode {\r\n    Auto = \"auto\",\r\n}\r\n\r\n/** @internal */\r\nexport enum PrimitiveTopology {\r\n    PointList = \"point-list\",\r\n    LineList = \"line-list\",\r\n    LineStrip = \"line-strip\",\r\n    TriangleList = \"triangle-list\",\r\n    TriangleStrip = \"triangle-strip\",\r\n}\r\n\r\n/** @internal */\r\nexport enum FrontFace {\r\n    CCW = \"ccw\",\r\n    CW = \"cw\",\r\n}\r\n\r\n/** @internal */\r\nexport enum CullMode {\r\n    None = \"none\",\r\n    Front = \"front\",\r\n    Back = \"back\",\r\n}\r\n\r\n/** @internal */\r\nexport enum ColorWrite {\r\n    Red = 1,\r\n    Green = 2,\r\n    Blue = 4,\r\n    Alpha = 8,\r\n    All = 15,\r\n}\r\n\r\n/** @internal */\r\nexport enum BlendFactor {\r\n    Zero = \"zero\",\r\n    One = \"one\",\r\n    Src = \"src\",\r\n    OneMinusSrc = \"one-minus-src\",\r\n    SrcAlpha = \"src-alpha\",\r\n    OneMinusSrcAlpha = \"one-minus-src-alpha\",\r\n    Dst = \"dst\",\r\n    OneMinusDst = \"one-minus-dst\",\r\n    DstAlpha = \"dst-alpha\",\r\n    OneMinusDstAlpha = \"one-minus-dst-alpha\",\r\n    SrcAlphaSaturated = \"src-alpha-saturated\",\r\n    Constant = \"constant\",\r\n    OneMinusConstant = \"one-minus-constant\",\r\n}\r\n\r\n/** @internal */\r\nexport enum BlendOperation {\r\n    Add = \"add\",\r\n    Subtract = \"subtract\",\r\n    ReverseSubtract = \"reverse-subtract\",\r\n    Min = \"min\",\r\n    Max = \"max\",\r\n}\r\n\r\n/** @internal */\r\nexport enum StencilOperation {\r\n    Keep = \"keep\",\r\n    Zero = \"zero\",\r\n    Replace = \"replace\",\r\n    Invert = \"invert\",\r\n    IncrementClamp = \"increment-clamp\",\r\n    DecrementClamp = \"decrement-clamp\",\r\n    IncrementWrap = \"increment-wrap\",\r\n    DecrementWrap = \"decrement-wrap\",\r\n}\r\n\r\n/** @internal */\r\nexport enum IndexFormat {\r\n    Uint16 = \"uint16\",\r\n    Uint32 = \"uint32\",\r\n}\r\n\r\n/** @internal */\r\nexport enum VertexFormat {\r\n    Uint8x2 = \"uint8x2\",\r\n    Uint8x4 = \"uint8x4\",\r\n    Sint8x2 = \"sint8x2\",\r\n    Sint8x4 = \"sint8x4\",\r\n    Unorm8x2 = \"unorm8x2\",\r\n    Unorm8x4 = \"unorm8x4\",\r\n    Snorm8x2 = \"snorm8x2\",\r\n    Snorm8x4 = \"snorm8x4\",\r\n    Uint16x2 = \"uint16x2\",\r\n    Uint16x4 = \"uint16x4\",\r\n    Sint16x2 = \"sint16x2\",\r\n    Sint16x4 = \"sint16x4\",\r\n    Unorm16x2 = \"unorm16x2\",\r\n    Unorm16x4 = \"unorm16x4\",\r\n    Snorm16x2 = \"snorm16x2\",\r\n    Snorm16x4 = \"snorm16x4\",\r\n    Float16x2 = \"float16x2\",\r\n    Float16x4 = \"float16x4\",\r\n    Float32 = \"float32\",\r\n    Float32x2 = \"float32x2\",\r\n    Float32x3 = \"float32x3\",\r\n    Float32x4 = \"float32x4\",\r\n    Uint32 = \"uint32\",\r\n    Uint32x2 = \"uint32x2\",\r\n    Uint32x3 = \"uint32x3\",\r\n    Uint32x4 = \"uint32x4\",\r\n    Sint32 = \"sint32\",\r\n    Sint32x2 = \"sint32x2\",\r\n    Sint32x3 = \"sint32x3\",\r\n    Sint32x4 = \"sint32x4\",\r\n    UNORM10x10x10x2 = \"unorm10-10-10-2\",\r\n}\r\n\r\n/** @internal */\r\nexport enum VertexStepMode {\r\n    Vertex = \"vertex\",\r\n    Instance = \"instance\",\r\n}\r\n\r\n/** @internal */\r\nexport enum ComputePassTimestampLocation {\r\n    Beginning = \"beginning\",\r\n    End = \"end\",\r\n}\r\n\r\n/** @internal */\r\nexport enum RenderPassTimestampLocation {\r\n    Beginning = \"beginning\",\r\n    End = \"end\",\r\n}\r\n\r\n/** @internal */\r\nexport enum LoadOp {\r\n    Load = \"load\",\r\n    Clear = \"clear\",\r\n}\r\n\r\n/** @internal */\r\nexport enum StoreOp {\r\n    Store = \"store\",\r\n    Discard = \"discard\",\r\n}\r\n\r\n/** @internal */\r\nexport enum QueryType {\r\n    Occlusion = \"occlusion\",\r\n    Timestamp = \"timestamp\",\r\n}\r\n\r\n/** @internal */\r\nexport enum CanvasAlphaMode {\r\n    Opaque = \"opaque\",\r\n    Premultiplied = \"premultiplied\",\r\n}\r\n\r\n/** @internal */\r\nexport enum DeviceLostReason {\r\n    Unknown = \"unknown\",\r\n    Destroyed = \"destroyed\",\r\n}\r\n\r\n/** @internal */\r\nexport enum ErrorFilter {\r\n    Validation = \"validation\",\r\n    OutOfMemory = \"out-of-memory\",\r\n    Internal = \"internal\",\r\n}\r\n"]}