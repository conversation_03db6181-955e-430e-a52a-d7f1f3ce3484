{"version": 3, "file": "WebXRBackgroundRemover.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRBackgroundRemover.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAGjF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AA8B9D;;GAEG;AACH,MAAM,OAAO,sBAAuB,SAAQ,oBAAoB;IAiB5D;;;;OAIG;IACH,YACI,iBAAsC;IACtC;;OAEG;IACa,UAA0C,EAAE;QAE5D,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFT,YAAO,GAAP,OAAO,CAAqC;QAfhE;;WAEG;QACI,uCAAkC,GAAwB,IAAI,UAAU,EAAE,CAAC;IAelF,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAChC,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,CAAC;IACpD,CAAC;IAES,UAAU,CAAC,QAAiB;QAClC,QAAQ;IACZ,CAAC;IAEO,mBAAmB,CAAC,QAAiB;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;QAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;YACvC,IAAI,IAAI,CAAC,OAAO,CAAC,6BAA6B,EAAE;gBAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC,MAAM,EAAE;oBACnD,MAAM,gBAAgB,GAAG,KAAK,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;oBACjE,IAAI,gBAAgB,EAAE;wBAClB,gBAAgB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;qBACzC;iBACJ;gBACD,IAAI,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC,MAAM,EAAE;oBACnD,MAAM,eAAe,GAAG,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;oBAC/D,IAAI,eAAe,EAAE;wBACjB,eAAe,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;qBACxC;iBACJ;aACJ;iBAAM;gBACH,MAAM,gBAAgB,GAAG,KAAK,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;gBACjE,IAAI,gBAAgB,EAAE;oBAClB,gBAAgB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;iBACzC;aACJ;SACJ;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YAC/B,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC9E;QAED,IAAI,CAAC,kCAAkC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;IACtE,CAAC;;AA9FD;;GAEG;AACoB,2BAAI,GAAG,gBAAgB,CAAC,kBAAkB,AAAtC,CAAuC;AAClE;;;;GAIG;AACoB,8BAAO,GAAG,CAAC,AAAJ,CAAK;AAwFvC,qBAAqB;AACrB,oBAAoB,CAAC,eAAe,CAChC,sBAAsB,CAAC,IAAI,EAC3B,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,sBAAsB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AACvE,CAAC,EACD,sBAAsB,CAAC,OAAO,EAC9B,IAAI,CACP,CAAC", "sourcesContent": ["import { WebXRFeaturesManager, WebXRFeatureName } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\n\r\n/**\r\n * Options interface for the background remover plugin\r\n */\r\nexport interface IWebXRBackgroundRemoverOptions {\r\n    /**\r\n     * Further background meshes to disable when entering AR\r\n     */\r\n    backgroundMeshes?: AbstractMesh[];\r\n    /**\r\n     * flags to configure the removal of the environment helper.\r\n     * If not set, the entire background will be removed. If set, flags should be set as well.\r\n     */\r\n    environmentHelperRemovalFlags?: {\r\n        /**\r\n         * Should the skybox be removed (default false)\r\n         */\r\n        skyBox?: boolean;\r\n        /**\r\n         * Should the ground be removed (default false)\r\n         */\r\n        ground?: boolean;\r\n    };\r\n    /**\r\n     * don't disable the environment helper\r\n     */\r\n    ignoreEnvironmentHelper?: boolean;\r\n}\r\n\r\n/**\r\n * A module that will automatically disable background meshes when entering AR and will enable them when leaving AR.\r\n */\r\nexport class WebXRBackgroundRemover extends WebXRAbstractFeature {\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.BACKGROUND_REMOVER;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * registered observers will be triggered when the background state changes\r\n     */\r\n    public onBackgroundStateChangedObservable: Observable<boolean> = new Observable();\r\n\r\n    /**\r\n     * constructs a new background remover module\r\n     * @param _xrSessionManager the session manager for this module\r\n     * @param options read-only options to be used in this module\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        /**\r\n         * read-only options to be used in this module\r\n         */\r\n        public readonly options: IWebXRBackgroundRemoverOptions = {}\r\n    ) {\r\n        super(_xrSessionManager);\r\n    }\r\n\r\n    /**\r\n     * attach this feature\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public attach(): boolean {\r\n        this._setBackgroundState(false);\r\n        return super.attach();\r\n    }\r\n\r\n    /**\r\n     * detach this feature.\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public detach(): boolean {\r\n        this._setBackgroundState(true);\r\n        return super.detach();\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n        this.onBackgroundStateChangedObservable.clear();\r\n    }\r\n\r\n    protected _onXRFrame(_xrFrame: XRFrame) {\r\n        // no-op\r\n    }\r\n\r\n    private _setBackgroundState(newState: boolean) {\r\n        const scene = this._xrSessionManager.scene;\r\n        if (!this.options.ignoreEnvironmentHelper) {\r\n            if (this.options.environmentHelperRemovalFlags) {\r\n                if (this.options.environmentHelperRemovalFlags.skyBox) {\r\n                    const backgroundSkybox = scene.getMeshByName(\"BackgroundSkybox\");\r\n                    if (backgroundSkybox) {\r\n                        backgroundSkybox.setEnabled(newState);\r\n                    }\r\n                }\r\n                if (this.options.environmentHelperRemovalFlags.ground) {\r\n                    const backgroundPlane = scene.getMeshByName(\"BackgroundPlane\");\r\n                    if (backgroundPlane) {\r\n                        backgroundPlane.setEnabled(newState);\r\n                    }\r\n                }\r\n            } else {\r\n                const backgroundHelper = scene.getMeshByName(\"BackgroundHelper\");\r\n                if (backgroundHelper) {\r\n                    backgroundHelper.setEnabled(newState);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this.options.backgroundMeshes) {\r\n            this.options.backgroundMeshes.forEach((mesh) => mesh.setEnabled(newState));\r\n        }\r\n\r\n        this.onBackgroundStateChangedObservable.notifyObservers(newState);\r\n    }\r\n}\r\n\r\n//register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRBackgroundRemover.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRBackgroundRemover(xrSessionManager, options);\r\n    },\r\n    WebXRBackgroundRemover.Version,\r\n    true\r\n);\r\n"]}