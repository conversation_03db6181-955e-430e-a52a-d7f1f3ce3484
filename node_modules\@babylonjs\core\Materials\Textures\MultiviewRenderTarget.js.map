{"version": 3, "file": "MultiviewRenderTarget.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/MultiviewRenderTarget.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,iCAAiC,CAAC;AAEtE,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAEpD;;;GAGG;AACH,MAAM,OAAO,qBAAsB,SAAQ,mBAAmB;IAC1D,IAAW,OAAO,CAAC,KAAa;QAC5B,wGAAwG;QACxG,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACH,YAAY,KAAa,EAAE,OAAuE,GAAG;QACjG,KAAK,CAAC,eAAe,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC,wBAAwB,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAC5I,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,EAAG,CAAC,SAAS,EAAE,CAAC,kCAAkC,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;QACpI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,OAAQ,CAAC;QAC5C,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC,kBAAkB,CAAC;QACpD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,EAAG,CAAC,OAAO,EAAE,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC;QACvE,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO;SACV;QACD,IAAI,CAAC,QAAQ,EAAG,CAAC,SAAS,EAAE,CAAC,wBAAwB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9E,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,CAAC,CAAC;IACb,CAAC;CACJ", "sourcesContent": ["import { RenderTargetTexture } from \"../Textures/renderTargetTexture\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Constants } from \"../../Engines/constants\";\r\n\r\n/**\r\n * Renders to multiple views with a single draw call\r\n * @see https://www.khronos.org/registry/webgl/extensions/OVR_multiview2/\r\n */\r\nexport class MultiviewRenderTarget extends RenderTargetTexture {\r\n    public set samples(value: number) {\r\n        // We override this setter because multisampling is handled by framebufferTextureMultisampleMultiviewOVR\r\n        this._samples = value;\r\n    }\r\n\r\n    public get samples(): number {\r\n        return this._samples;\r\n    }\r\n\r\n    /**\r\n     * Creates a multiview render target\r\n     * @param scene scene used with the render target\r\n     * @param size the size of the render target (used for each view)\r\n     */\r\n    constructor(scene?: Scene, size: number | { width: number; height: number } | { ratio: number } = 512) {\r\n        super(\"multiview rtt\", size, scene, false, true, Constants.TEXTURETYPE_UNSIGNED_INT, false, undefined, false, false, true, undefined, true);\r\n        this._renderTarget = this.getScene()!.getEngine().createMultiviewRenderTargetTexture(this.getRenderWidth(), this.getRenderHeight());\r\n        this._texture = this._renderTarget.texture!;\r\n        this._texture.isMultiview = true;\r\n        this._texture.format = Constants.TEXTUREFORMAT_RGBA;\r\n        this.samples = this._getEngine()!.getCaps().maxSamples || this.samples;\r\n        this._texture.samples = this._samples;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindFrameBuffer() {\r\n        if (!this._renderTarget) {\r\n            return;\r\n        }\r\n        this.getScene()!.getEngine().bindMultiviewFramebuffer(this._renderTarget);\r\n    }\r\n\r\n    /**\r\n     * Gets the number of views the corresponding to the texture (eg. a MultiviewRenderTarget will have > 1)\r\n     * @returns the view count\r\n     */\r\n    public getViewCount() {\r\n        return 2;\r\n    }\r\n}\r\n"]}