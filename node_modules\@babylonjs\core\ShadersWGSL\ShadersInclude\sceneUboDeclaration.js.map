{"version": 3, "file": "sceneUboDeclaration.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/ShadersWGSL/ShadersInclude/sceneUboDeclaration.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAExD,MAAM,IAAI,GAAG,qBAAqB,CAAC;AACnC,MAAM,MAAM,GAAG;;;;;;;CAOd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACpD,gBAAgB;AAChB,MAAM,CAAC,MAAM,mBAAmB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore\";\n\nconst name = \"sceneUboDeclaration\";\nconst shader = `struct Scene {viewProjection : mat4x4<f32>,\n#ifdef MULTIVIEW\nviewProjectionR : mat4x4<f32>,\n#endif \nview : mat4x4<f32>,\nprojection : mat4x4<f32>,\nvEyePosition : vec4<f32>,};var<uniform> scene : Scene;\n`;\n// Sideeffect\nShaderStore.IncludesShadersStoreWGSL[name] = shader;\n/** @internal */\nexport const sceneUboDeclaration = { name, shader };\n"]}