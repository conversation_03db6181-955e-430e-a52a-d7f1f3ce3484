{"version": 3, "file": "polygonMesh.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Meshes/polygonMesh.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACxD,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AAEvD,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAC3C,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAGrD;;GAEG;AACH,MAAM,cAAe,SAAQ,OAAO;IAChC,YACI,QAAiB;IACjB,2BAA2B;IACpB,KAAa;QAEpB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;QAFvB,UAAK,GAAL,KAAK,CAAQ;IAGxB,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,aAAa;IAAnB;QACI,aAAQ,GAAG,EAAsB,CAAC;IAwCtC,CAAC;IAtCG,GAAG,CAAC,cAA8B;QAC9B,MAAM,MAAM,GAAqB,EAAE,CAAC;QACpC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC7B,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACjE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,aAAa;QACT,MAAM,IAAI,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,MAAM,IAAI,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5B,IAAI;YACJ,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE;gBAClB,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;aACpB;iBAAM,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE;gBACzB,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;aACpB;YAED,IAAI;YACJ,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE;gBAClB,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;aACpB;iBAAM,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE;gBACzB,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;aACpB;QACL,CAAC,CAAC,CAAC;QAEH,OAAO;YACH,GAAG,EAAE,IAAI;YACT,GAAG,EAAE,IAAI;YACT,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YACtB,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;SAC1B,CAAC;IACN,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,OAAO;IAChB;;;;;;;OAOG;IACH,MAAM,CAAC,SAAS,CAAC,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY;QACnE,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAChH,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,MAAM,CAAC,MAAc,EAAE,KAAa,CAAC,EAAE,KAAa,CAAC,EAAE,gBAAwB,EAAE;QACpF,MAAM,MAAM,GAAc,EAAE,CAAC;QAE7B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;QAEhD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,EAAE,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;YACvF,KAAK,IAAI,SAAS,CAAC;SACtB;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,KAAa;QACtB,MAAM,MAAM,GAAG,KAAK;aACf,KAAK,CAAC,aAAa,CAAC;aACpB,GAAG,CAAC,UAAU,CAAC;aACf,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAClC,IAAI,CAAS,CAAC;QACd,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;YAClD,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SACtD;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,UAAU,CAAC,CAAS,EAAE,CAAS;QAClC,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClC,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,kBAAkB;IAWnB,YAAY,CAAC,MAAiB;QAClC,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE;YACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SAChC;IACL,CAAC;IAOD;;;;;;OAMG;IACH,YAAY,IAAY,EAAE,QAAiC,EAAE,KAAa,EAAE,eAAe,GAAG,MAAM;QA5B5F,YAAO,GAAG,IAAI,aAAa,EAAE,CAAC;QAC9B,mBAAc,GAAG,IAAI,aAAa,EAAE,CAAC;QACrC,WAAM,GAAG,IAAI,KAAK,EAAiB,CAAC;QAKpC,aAAQ,GAAa,IAAI,KAAK,EAAU,CAAC;QACzC,YAAO,GAAa,IAAI,KAAK,EAAU,CAAC;QAqB5C,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;QACjC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QAEpD,IAAI,MAAiB,CAAC;QACtB,IAAI,QAAQ,YAAY,KAAK,EAAE;YAC3B,MAAM,GAAW,QAAS,CAAC,SAAS,EAAE,CAAC;SAC1C;aAAM;YACH,MAAM,GAAc,QAAQ,CAAC;SAChC;QAED,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAE1B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACzB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEhC,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,WAAW,EAAE;YACvC,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;SACvE;IACL,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,IAAe;QACnB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACvB,MAAM,UAAU,GAAG,IAAI,aAAa,EAAE,CAAC;QACvC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAExB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,YAAqB,KAAK,EAAE,QAAgB,CAAC,EAAE,qBAA6B,CAAC;QAC/E,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEjD,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;QAEnE,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,EAAY,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAC7F,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,EAAY,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACzF,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,MAAM,EAAY,UAAU,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QACjF,MAAM,CAAC,UAAU,CAAW,UAAU,CAAC,OAAO,CAAC,CAAC;QAEhD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACH,eAAe,CAAC,QAAgB,CAAC,EAAE,qBAA6B,CAAC;QAC7D,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAEhC,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,MAAM,GAAG,GAAa,EAAE,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAC5C,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAChC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YACxB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAE3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACjC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SACxB;QAED,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,4BAA4B;YAEzE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBAChC,+BAA+B;gBAC/B,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBACzB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;YAChG,CAAC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE;gBACpC,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC1B,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC1B,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAE1B,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,cAAc,CAAC,CAAC;gBAClC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,cAAc,CAAC,CAAC;gBAClC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,cAAc,CAAC,CAAC;aACrC;YAED,eAAe;YACf,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;YAE/G,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBACzB,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC;YACnG,CAAC,CAAC,CAAC;SACN;QAED,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;QACzB,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;QACzB,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;QAEjB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;;;;;;OAWG;IACK,QAAQ,CAAC,SAAgB,EAAE,OAAc,EAAE,GAAU,EAAE,OAAc,EAAE,MAAW,EAAE,MAAqB,EAAE,KAAa,EAAE,IAAa,EAAE,kBAA0B;QACvK,IAAI,UAAU,GAAW,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9C,IAAI,OAAO,GAAW,CAAC,CAAC;QACxB,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,MAAM,CAAC,GAAmB,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,EAAE,GAAmB,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAE7E,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAC9B,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAEnC,MAAM,EAAE,GAAmB,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACtG,MAAM,EAAE,GAAmB,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAE7E,IAAI,EAAE,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,IAAI,EAAE,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACnD,IAAI,EAAE,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAErD,IAAI,CAAC,IAAI,EAAE;gBACP,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aACrB;YAED,MAAM,OAAO,GAAG,EAAE,CAAC,cAAc,EAAE,CAAC;YACpC,IAAI,OAAO,GAAG,EAAE,CAAC,cAAc,EAAE,CAAC;YAClC,IAAI,OAAO,GAAG,EAAE,CAAC,cAAc,EAAE,CAAC;YAElC,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC3C,IAAI,IAAI,GAAG,kBAAkB,EAAE;gBAC3B,IAAI,IAAI,GAAG,OAAO,GAAG,CAAC,EAAE;oBACpB,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;iBACvF;qBAAM;oBACH,uCAAuC;oBACvC,OAAO,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC;iBACpC;aACJ;iBAAM;gBACH,OAAO,GAAG,OAAO,CAAC;aACrB;YAED,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACjC,IAAI,IAAI,GAAG,kBAAkB,EAAE;gBAC3B,IAAI,IAAI,GAAG,OAAO,GAAG,CAAC,EAAE;oBACpB,eAAe;oBACf,OAAO,GAAG,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;iBACvF;qBAAM;oBACH,uCAAuC;oBACvC,OAAO,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC;iBACpC;aACJ;iBAAM;gBACH,OAAO,GAAG,OAAO,CAAC;aACrB;YAED,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACpC,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACpC,OAAO,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC;YACvB,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACpC,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAEpC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;YAE9C,IAAI,CAAC,IAAI,EAAE;gBACP,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACzB,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAE7B,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;aAChC;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACzB,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAE7B,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;aAChC;YACD,UAAU,IAAI,CAAC,CAAC;SACnB;IACL,CAAC;CACJ", "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Vector3, Vector2 } from \"../Maths/math.vector\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { VertexData } from \"../Meshes/mesh.vertexData\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Path2 } from \"../Maths/math.path\";\r\nimport { Epsilon } from \"../Maths/math.constants\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\n\r\ndeclare let earcut: any;\r\n/**\r\n * Vector2 wth index property\r\n */\r\nclass IndexedVector2 extends Vector2 {\r\n    constructor(\r\n        original: Vector2,\r\n        /** Index of the vector2 */\r\n        public index: number\r\n    ) {\r\n        super(original.x, original.y);\r\n    }\r\n}\r\n\r\n/**\r\n * Defines points to create a polygon\r\n */\r\nclass PolygonPoints {\r\n    elements = [] as IndexedVector2[];\r\n\r\n    add(originalPoints: Array<Vector2>): Array<IndexedVector2> {\r\n        const result: IndexedVector2[] = [];\r\n        originalPoints.forEach((point) => {\r\n            const newPoint = new IndexedVector2(point, this.elements.length);\r\n            result.push(newPoint);\r\n            this.elements.push(newPoint);\r\n        });\r\n\r\n        return result;\r\n    }\r\n\r\n    computeBounds(): { min: Vector2; max: Vector2; width: number; height: number } {\r\n        const lmin = new Vector2(this.elements[0].x, this.elements[0].y);\r\n        const lmax = new Vector2(this.elements[0].x, this.elements[0].y);\r\n\r\n        this.elements.forEach((point) => {\r\n            // x\r\n            if (point.x < lmin.x) {\r\n                lmin.x = point.x;\r\n            } else if (point.x > lmax.x) {\r\n                lmax.x = point.x;\r\n            }\r\n\r\n            // y\r\n            if (point.y < lmin.y) {\r\n                lmin.y = point.y;\r\n            } else if (point.y > lmax.y) {\r\n                lmax.y = point.y;\r\n            }\r\n        });\r\n\r\n        return {\r\n            min: lmin,\r\n            max: lmax,\r\n            width: lmax.x - lmin.x,\r\n            height: lmax.y - lmin.y,\r\n        };\r\n    }\r\n}\r\n\r\n/**\r\n * Polygon\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param#non-regular-polygon\r\n */\r\nexport class Polygon {\r\n    /**\r\n     * Creates a rectangle\r\n     * @param xmin bottom X coord\r\n     * @param ymin bottom Y coord\r\n     * @param xmax top X coord\r\n     * @param ymax top Y coord\r\n     * @returns points that make the resulting rectangle\r\n     */\r\n    static Rectangle(xmin: number, ymin: number, xmax: number, ymax: number): Vector2[] {\r\n        return [new Vector2(xmin, ymin), new Vector2(xmax, ymin), new Vector2(xmax, ymax), new Vector2(xmin, ymax)];\r\n    }\r\n\r\n    /**\r\n     * Creates a circle\r\n     * @param radius radius of circle\r\n     * @param cx scale in x\r\n     * @param cy scale in y\r\n     * @param numberOfSides number of sides that make up the circle\r\n     * @returns points that make the resulting circle\r\n     */\r\n    static Circle(radius: number, cx: number = 0, cy: number = 0, numberOfSides: number = 32): Vector2[] {\r\n        const result: Vector2[] = [];\r\n\r\n        let angle = 0;\r\n        const increment = (Math.PI * 2) / numberOfSides;\r\n\r\n        for (let i = 0; i < numberOfSides; i++) {\r\n            result.push(new Vector2(cx + Math.cos(angle) * radius, cy + Math.sin(angle) * radius));\r\n            angle -= increment;\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Creates a polygon from input string\r\n     * @param input Input polygon data\r\n     * @returns the parsed points\r\n     */\r\n    static Parse(input: string): Vector2[] {\r\n        const floats = input\r\n            .split(/[^-+eE.\\d]+/)\r\n            .map(parseFloat)\r\n            .filter((val) => !isNaN(val));\r\n        let i: number;\r\n        const result = [];\r\n        for (i = 0; i < (floats.length & 0x7ffffffe); i += 2) {\r\n            result.push(new Vector2(floats[i], floats[i + 1]));\r\n        }\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Starts building a polygon from x and y coordinates\r\n     * @param x x coordinate\r\n     * @param y y coordinate\r\n     * @returns the started path2\r\n     */\r\n    static StartingAt(x: number, y: number): Path2 {\r\n        return Path2.StartingAt(x, y);\r\n    }\r\n}\r\n\r\n/**\r\n * Builds a polygon\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param/polyMeshBuilder\r\n */\r\nexport class PolygonMeshBuilder {\r\n    private _points = new PolygonPoints();\r\n    private _outlinepoints = new PolygonPoints();\r\n    private _holes = new Array<PolygonPoints>();\r\n\r\n    private _name: string;\r\n    private _scene: Nullable<Scene>;\r\n\r\n    private _epoints: number[] = new Array<number>();\r\n    private _eholes: number[] = new Array<number>();\r\n\r\n    private _addToepoint(points: Vector2[]) {\r\n        for (const p of points) {\r\n            this._epoints.push(p.x, p.y);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Babylon reference to the earcut plugin.\r\n     */\r\n    public bjsEarcut: any;\r\n\r\n    /**\r\n     * Creates a PolygonMeshBuilder\r\n     * @param name name of the builder\r\n     * @param contours Path of the polygon\r\n     * @param scene scene to add to when creating the mesh\r\n     * @param earcutInjection can be used to inject your own earcut reference\r\n     */\r\n    constructor(name: string, contours: Path2 | Vector2[] | any, scene?: Scene, earcutInjection = earcut) {\r\n        this.bjsEarcut = earcutInjection;\r\n        this._name = name;\r\n        this._scene = scene || EngineStore.LastCreatedScene;\r\n\r\n        let points: Vector2[];\r\n        if (contours instanceof Path2) {\r\n            points = (<Path2>contours).getPoints();\r\n        } else {\r\n            points = <Vector2[]>contours;\r\n        }\r\n\r\n        this._addToepoint(points);\r\n\r\n        this._points.add(points);\r\n        this._outlinepoints.add(points);\r\n\r\n        if (typeof this.bjsEarcut === \"undefined\") {\r\n            Logger.Warn(\"Earcut was not found, the polygon will not be built.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds a hole within the polygon\r\n     * @param hole Array of points defining the hole\r\n     * @returns this\r\n     */\r\n    addHole(hole: Vector2[]): PolygonMeshBuilder {\r\n        this._points.add(hole);\r\n        const holepoints = new PolygonPoints();\r\n        holepoints.add(hole);\r\n        this._holes.push(holepoints);\r\n\r\n        this._eholes.push(this._epoints.length / 2);\r\n        this._addToepoint(hole);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Creates the polygon\r\n     * @param updatable If the mesh should be updatable\r\n     * @param depth The depth of the mesh created\r\n     * @param smoothingThreshold Dot product threshold for smoothed normals\r\n     * @returns the created mesh\r\n     */\r\n    build(updatable: boolean = false, depth: number = 0, smoothingThreshold: number = 2): Mesh {\r\n        const result = new Mesh(this._name, this._scene);\r\n\r\n        const vertexData = this.buildVertexData(depth, smoothingThreshold);\r\n\r\n        result.setVerticesData(VertexBuffer.PositionKind, <number[]>vertexData.positions, updatable);\r\n        result.setVerticesData(VertexBuffer.NormalKind, <number[]>vertexData.normals, updatable);\r\n        result.setVerticesData(VertexBuffer.UVKind, <number[]>vertexData.uvs, updatable);\r\n        result.setIndices(<number[]>vertexData.indices);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Creates the polygon\r\n     * @param depth The depth of the mesh created\r\n     * @param smoothingThreshold Dot product threshold for smoothed normals\r\n     * @returns the created VertexData\r\n     */\r\n    buildVertexData(depth: number = 0, smoothingThreshold: number = 2): VertexData {\r\n        const result = new VertexData();\r\n\r\n        const normals: number[] = [];\r\n        const positions: number[] = [];\r\n        const uvs: number[] = [];\r\n\r\n        const bounds = this._points.computeBounds();\r\n        this._points.elements.forEach((p) => {\r\n            normals.push(0, 1.0, 0);\r\n            positions.push(p.x, 0, p.y);\r\n            uvs.push((p.x - bounds.min.x) / bounds.width, (p.y - bounds.min.y) / bounds.height);\r\n        });\r\n\r\n        const indices: number[] = [];\r\n\r\n        const res = this.bjsEarcut(this._epoints, this._eholes, 2);\r\n\r\n        for (let i = 0; i < res.length; i++) {\r\n            indices.push(res[i]);\r\n        }\r\n\r\n        if (depth > 0) {\r\n            const positionscount = positions.length / 3; //get the current pointcount\r\n\r\n            this._points.elements.forEach((p) => {\r\n                //add the elements at the depth\r\n                normals.push(0, -1.0, 0);\r\n                positions.push(p.x, -depth, p.y);\r\n                uvs.push(1 - (p.x - bounds.min.x) / bounds.width, 1 - (p.y - bounds.min.y) / bounds.height);\r\n            });\r\n\r\n            const totalCount = indices.length;\r\n            for (let i = 0; i < totalCount; i += 3) {\r\n                const i0 = indices[i + 0];\r\n                const i1 = indices[i + 1];\r\n                const i2 = indices[i + 2];\r\n\r\n                indices.push(i2 + positionscount);\r\n                indices.push(i1 + positionscount);\r\n                indices.push(i0 + positionscount);\r\n            }\r\n\r\n            //Add the sides\r\n            this._addSide(positions, normals, uvs, indices, bounds, this._outlinepoints, depth, false, smoothingThreshold);\r\n\r\n            this._holes.forEach((hole) => {\r\n                this._addSide(positions, normals, uvs, indices, bounds, hole, depth, true, smoothingThreshold);\r\n            });\r\n        }\r\n\r\n        result.indices = indices;\r\n        result.positions = positions;\r\n        result.normals = normals;\r\n        result.uvs = uvs;\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Adds a side to the polygon\r\n     * @param positions points that make the polygon\r\n     * @param normals normals of the polygon\r\n     * @param uvs uvs of the polygon\r\n     * @param indices indices of the polygon\r\n     * @param bounds bounds of the polygon\r\n     * @param points points of the polygon\r\n     * @param depth depth of the polygon\r\n     * @param flip flip of the polygon\r\n     * @param smoothingThreshold\r\n     */\r\n    private _addSide(positions: any[], normals: any[], uvs: any[], indices: any[], bounds: any, points: PolygonPoints, depth: number, flip: boolean, smoothingThreshold: number) {\r\n        let startIndex: number = positions.length / 3;\r\n        let ulength: number = 0;\r\n        for (let i: number = 0; i < points.elements.length; i++) {\r\n            const p: IndexedVector2 = points.elements[i];\r\n            const p1: IndexedVector2 = points.elements[(i + 1) % points.elements.length];\r\n\r\n            positions.push(p.x, 0, p.y);\r\n            positions.push(p.x, -depth, p.y);\r\n            positions.push(p1.x, 0, p1.y);\r\n            positions.push(p1.x, -depth, p1.y);\r\n\r\n            const p0: IndexedVector2 = points.elements[(i + points.elements.length - 1) % points.elements.length];\r\n            const p2: IndexedVector2 = points.elements[(i + 2) % points.elements.length];\r\n\r\n            let vc = new Vector3(-(p1.y - p.y), 0, p1.x - p.x);\r\n            let vp = new Vector3(-(p.y - p0.y), 0, p.x - p0.x);\r\n            let vn = new Vector3(-(p2.y - p1.y), 0, p2.x - p1.x);\r\n\r\n            if (!flip) {\r\n                vc = vc.scale(-1);\r\n                vp = vp.scale(-1);\r\n                vn = vn.scale(-1);\r\n            }\r\n\r\n            const vc_norm = vc.normalizeToNew();\r\n            let vp_norm = vp.normalizeToNew();\r\n            let vn_norm = vn.normalizeToNew();\r\n\r\n            const dotp = Vector3.Dot(vp_norm, vc_norm);\r\n            if (dotp > smoothingThreshold) {\r\n                if (dotp < Epsilon - 1) {\r\n                    vp_norm = new Vector3(p.x, 0, p.y).subtract(new Vector3(p1.x, 0, p1.y)).normalize();\r\n                } else {\r\n                    // cheap average weighed by side length\r\n                    vp_norm = vp.add(vc).normalize();\r\n                }\r\n            } else {\r\n                vp_norm = vc_norm;\r\n            }\r\n\r\n            const dotn = Vector3.Dot(vn, vc);\r\n            if (dotn > smoothingThreshold) {\r\n                if (dotn < Epsilon - 1) {\r\n                    // back to back\r\n                    vn_norm = new Vector3(p1.x, 0, p1.y).subtract(new Vector3(p.x, 0, p.y)).normalize();\r\n                } else {\r\n                    // cheap average weighed by side length\r\n                    vn_norm = vn.add(vc).normalize();\r\n                }\r\n            } else {\r\n                vn_norm = vc_norm;\r\n            }\r\n\r\n            uvs.push(ulength / bounds.width, 0);\r\n            uvs.push(ulength / bounds.width, 1);\r\n            ulength += vc.length();\r\n            uvs.push(ulength / bounds.width, 0);\r\n            uvs.push(ulength / bounds.width, 1);\r\n\r\n            normals.push(vp_norm.x, vp_norm.y, vp_norm.z);\r\n            normals.push(vp_norm.x, vp_norm.y, vp_norm.z);\r\n            normals.push(vn_norm.x, vn_norm.y, vn_norm.z);\r\n            normals.push(vn_norm.x, vn_norm.y, vn_norm.z);\r\n\r\n            if (!flip) {\r\n                indices.push(startIndex);\r\n                indices.push(startIndex + 1);\r\n                indices.push(startIndex + 2);\r\n\r\n                indices.push(startIndex + 1);\r\n                indices.push(startIndex + 3);\r\n                indices.push(startIndex + 2);\r\n            } else {\r\n                indices.push(startIndex);\r\n                indices.push(startIndex + 2);\r\n                indices.push(startIndex + 1);\r\n\r\n                indices.push(startIndex + 1);\r\n                indices.push(startIndex + 2);\r\n                indices.push(startIndex + 3);\r\n            }\r\n            startIndex += 4;\r\n        }\r\n    }\r\n}\r\n"]}