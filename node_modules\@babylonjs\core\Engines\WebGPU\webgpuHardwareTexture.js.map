{"version": 3, "file": "webgpuHardwareTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuHardwareTexture.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAEjD,gEAAgE;AAChE,OAAO,KAAK,eAAe,MAAM,mBAAmB,CAAC;AACrD,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAE5D,gBAAgB;AAChB,MAAM,OAAO,qBAAqB;IA0B9B,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAEM,cAAc,CAAC,KAAK,GAAG,CAAC;QAC3B,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;IACpD,CAAC;IAEM,cAAc,CAAC,OAAmB,EAAE,KAAK,GAAG,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;SAChC;QAED,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;SAC1C;QAED,IAAI,CAAC,kBAAmB,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;IAC9C,CAAC;IAEM,kBAAkB;QACrB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3C,OAAO,EAAE,OAAO,EAAE,CAAC;aACtB;YACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAClC;IACL,CAAC;IAQD,YAAY,kBAAwC,IAAI;QAJjD,WAAM,GAAqB,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;QACpE,kBAAa,GAAG,CAAC,CAAC;QAClB,4BAAuB,GAAG,CAAC,CAAC;QAG/B,IAAI,CAAC,cAAc,GAAG,eAAe,CAAC;QACtC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAEM,GAAG,CAAC,eAA2B;QAClC,IAAI,CAAC,cAAc,GAAG,eAAe,CAAC;IAC1C,CAAC;IAEM,QAAQ,CAAC,cAAsB,EAAE,eAAwB,EAAE,SAAkB,EAAE,MAAe,EAAE,IAAa,EAAE,KAAa,EAAE,MAAc,EAAE,KAAa;QAC9J,IAAI,aAAa,GAA4B,eAAe,CAAC,oBAAoB,CAAC,GAAG,CAAC;QACtF,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,MAAM,EAAE;YACR,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC;YACvH,eAAe,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;SACtC;aAAM,IAAI,IAAI,EAAE;YACb,aAAa,GAAG,eAAe,CAAC,oBAAoB,CAAC,GAAG,CAAC;SAC5D;aAAM,IAAI,SAAS,EAAE;YAClB,aAAa,GAAG,eAAe,CAAC,oBAAoB,CAAC,QAAQ,CAAC;YAC9D,eAAe,GAAG,KAAK,CAAC;SAC3B;QAED,MAAM,MAAM,GAAG,mBAAmB,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnE,MAAM,MAAM,GAAG,mBAAmB,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,GAAG,CAAC;QAExJ,IAAI,CAAC,UAAU,CAAC;YACZ,KAAK,EAAE,cAAc,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,GAAG,eAAe,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,IAAI,MAAM,IAAI,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,IAC9J,IAAI,CAAC,MACT,IAAI,aAAa,EAAE;YACnB,MAAM;YACN,SAAS,EAAE,aAAa;YACxB,aAAa,EAAE,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9E,cAAc,EAAE,CAAC;YACjB,YAAY,EAAE,CAAC;YACf,eAAe;YACf,MAAM;SACT,CAAC,CAAC;IACP,CAAC;IAEM,UAAU,CAAC,UAAqC,EAAE,oBAAoB,GAAG,KAAK;QACjF,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAe,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QACxD,IAAI,oBAAoB,IAAI,UAAU,EAAE;YACpC,MAAM,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC;YAChD,UAAU,CAAC,aAAa,GAAG,CAAC,CAAC;YAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAe,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAClE,UAAU,CAAC,aAAa,GAAG,cAAc,CAAC;SAC7C;IACL,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,CAAC;QAC/B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,uBAAuB,EAAE,OAAO,EAAE,CAAC;QACxC,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;CACJ", "sourcesContent": ["/* eslint-disable jsdoc/require-jsdoc */\r\n/* eslint-disable babylonjs/available */\r\nimport type { HardwareTextureWrapper } from \"../../Materials/Textures/hardwareTextureWrapper\";\r\nimport { <PERSON>ala<PERSON> } from \"../../Maths/math.scalar\";\r\nimport type { Nullable } from \"../../types\";\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport { WebGPUTextureHelper } from \"./webgpuTextureHelper\";\r\n\r\n/** @internal */\r\nexport class WebGPUHardwareTexture implements HardwareTextureWrapper {\r\n    /**\r\n     * Cache of RenderPassDescriptor and BindGroup used when generating mipmaps (see WebGPUTextureHelper.generateMipmaps)\r\n     * @internal\r\n     */\r\n    public _mipmapGenRenderPassDescr: GPURenderPassDescriptor[][];\r\n    /** @internal */\r\n    public _mipmapGenBindGroup: GPUBindGroup[][];\r\n\r\n    /**\r\n     * Cache for the invertYPreMultiplyAlpha function (see WebGPUTextureHelper)\r\n     * @internal\r\n     */\r\n    public _copyInvertYTempTexture?: GPUTexture;\r\n    /** @internal */\r\n    public _copyInvertYRenderPassDescr: GPURenderPassDescriptor;\r\n    /** @internal */\r\n    public _copyInvertYBindGroup: GPUBindGroup;\r\n    /** @internal */\r\n    public _copyInvertYBindGroupWithOfst: GPUBindGroup;\r\n\r\n    private _webgpuTexture: Nullable<GPUTexture>;\r\n    // There can be multiple MSAA textures for a single WebGPU texture because different layers of a 2DArrayTexture / 3DTexture\r\n    // or different faces of a cube texture can be bound to different render targets at the same time (in a multi RenderTargetWrapper)\r\n    private _webgpuMSAATexture: Nullable<GPUTexture[]>;\r\n\r\n    public get underlyingResource(): Nullable<GPUTexture> {\r\n        return this._webgpuTexture;\r\n    }\r\n\r\n    public getMSAATexture(index = 0): Nullable<GPUTexture> {\r\n        return this._webgpuMSAATexture?.[index] ?? null;\r\n    }\r\n\r\n    public setMSAATexture(texture: GPUTexture, index = -1) {\r\n        if (!this._webgpuMSAATexture) {\r\n            this._webgpuMSAATexture = [];\r\n        }\r\n\r\n        if (index === -1) {\r\n            index = this._webgpuMSAATexture.length;\r\n        }\r\n\r\n        this._webgpuMSAATexture![index] = texture;\r\n    }\r\n\r\n    public releaseMSAATexture() {\r\n        if (this._webgpuMSAATexture) {\r\n            for (const texture of this._webgpuMSAATexture) {\r\n                texture?.destroy();\r\n            }\r\n            this._webgpuMSAATexture = null;\r\n        }\r\n    }\r\n\r\n    public view: Nullable<GPUTextureView>;\r\n    public viewForWriting: Nullable<GPUTextureView>;\r\n    public format: GPUTextureFormat = WebGPUConstants.TextureFormat.RGBA8Unorm;\r\n    public textureUsages = 0;\r\n    public textureAdditionalUsages = 0;\r\n\r\n    constructor(existingTexture: Nullable<GPUTexture> = null) {\r\n        this._webgpuTexture = existingTexture;\r\n        this._webgpuMSAATexture = null;\r\n        this.view = null;\r\n        this.viewForWriting = null;\r\n    }\r\n\r\n    public set(hardwareTexture: GPUTexture): void {\r\n        this._webgpuTexture = hardwareTexture;\r\n    }\r\n\r\n    public setUsage(_textureSource: number, generateMipMaps: boolean, is2DArray: boolean, isCube: boolean, is3D: boolean, width: number, height: number, depth: number): void {\r\n        let viewDimension: GPUTextureViewDimension = WebGPUConstants.TextureViewDimension.E2d;\r\n        let arrayLayerCount = 1;\r\n        if (isCube) {\r\n            viewDimension = is2DArray ? WebGPUConstants.TextureViewDimension.CubeArray : WebGPUConstants.TextureViewDimension.Cube;\r\n            arrayLayerCount = 6 * (depth || 1);\r\n        } else if (is3D) {\r\n            viewDimension = WebGPUConstants.TextureViewDimension.E3d;\r\n        } else if (is2DArray) {\r\n            viewDimension = WebGPUConstants.TextureViewDimension.E2dArray;\r\n            arrayLayerCount = depth;\r\n        }\r\n\r\n        const format = WebGPUTextureHelper.GetDepthFormatOnly(this.format);\r\n        const aspect = WebGPUTextureHelper.HasDepthAndStencilAspects(this.format) ? WebGPUConstants.TextureAspect.DepthOnly : WebGPUConstants.TextureAspect.All;\r\n\r\n        this.createView({\r\n            label: `TextureView${is3D ? \"3D\" : isCube ? \"Cube\" : \"2D\"}${is2DArray ? \"_Array\" + arrayLayerCount : \"\"}_${width}x${height}_${generateMipMaps ? \"wmips\" : \"womips\"}_${\r\n                this.format\r\n            }_${viewDimension}`,\r\n            format,\r\n            dimension: viewDimension,\r\n            mipLevelCount: generateMipMaps ? Scalar.ILog2(Math.max(width, height)) + 1 : 1,\r\n            baseArrayLayer: 0,\r\n            baseMipLevel: 0,\r\n            arrayLayerCount,\r\n            aspect,\r\n        });\r\n    }\r\n\r\n    public createView(descriptor?: GPUTextureViewDescriptor, createViewForWriting = false): void {\r\n        this.view = this._webgpuTexture!.createView(descriptor);\r\n        if (createViewForWriting && descriptor) {\r\n            const saveNumMipMaps = descriptor.mipLevelCount;\r\n            descriptor.mipLevelCount = 1;\r\n            this.viewForWriting = this._webgpuTexture!.createView(descriptor);\r\n            descriptor.mipLevelCount = saveNumMipMaps;\r\n        }\r\n    }\r\n\r\n    public reset(): void {\r\n        this._webgpuTexture = null;\r\n        this._webgpuMSAATexture = null;\r\n        this.view = null;\r\n        this.viewForWriting = null;\r\n    }\r\n\r\n    public release(): void {\r\n        this._webgpuTexture?.destroy();\r\n        this.releaseMSAATexture();\r\n        this._copyInvertYTempTexture?.destroy();\r\n        this.reset();\r\n    }\r\n}\r\n"]}