{"version": 3, "file": "WebXREyeTracking.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXREyeTracking.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AACjF,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAC9D,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;AAGxC;;;GAGG;AACH,MAAM,OAAO,gBAAiB,SAAQ,oBAAoB;IA4BtD;;;OAGG;IACH,YAAY,iBAAsC;QAC9C,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAlB7B;;WAEG;QACa,mCAA8B,GAAoB,IAAI,UAAU,EAAE,CAAC;QACnF;;WAEG;QACa,iCAA4B,GAAqB,IAAI,UAAU,EAAE,CAAC;QAClF;;WAEG;QACa,uCAAkC,GAAoB,IAAI,UAAU,EAAE,CAAC;QA2E/E,8BAAyB,GAAG,CAAC,KAA+B,EAAE,EAAE;YACpE,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,SAAS,CAAC;YACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3D,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvE,CAAC,CAAC;QAEM,4BAAuB,GAAG,GAAG,EAAE;YACnC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,4BAA4B,CAAC,eAAe,EAAE,CAAC;QACxD,CAAC,CAAC;QA7EE,IAAI,CAAC,mBAAmB,GAAG,cAAc,CAAC;QAC1C,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE;YAChC,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;aAAM;YACH,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,EAAE;gBAChD,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACvG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAEnG,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;QAC5C,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,CAAC;IACpD,CAAC;IAED;;;OAGG;IACH,IAAW,cAAc;QACrB,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,UAAU;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAES,UAAU,CAAC,KAAc;QAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;YAC1B,OAAO;SACV;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,QAAQ,EAAE;YACvC,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YACxF,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;gBAClK,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;gBACxC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;gBAE7D,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,EAAE;oBACpD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC7B,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBACjC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBAEjC,OAAO,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;iBAChH;qBAAM;oBACH,OAAO,CAAC,0BAA0B,CAAC,uBAAuB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;iBACjH;gBAED,IAAI,CAAC,kCAAkC,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC1E;SACJ;IACL,CAAC;IAcO,KAAK;QACT,kCAAkC;QAClC,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;YACjC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACpG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACnG;IACL,CAAC;;AAnHD;;GAEG;AACoB,qBAAI,GAAG,gBAAgB,CAAC,YAAY,AAAhC,CAAiC;AAC5D;;;;GAIG;AACoB,wBAAO,GAAG,CAAC,AAAJ,CAAK;AA6GvC,oBAAoB,CAAC,eAAe,CAChC,gBAAgB,CAAC,IAAI,EACrB,CAAC,gBAAgB,EAAE,EAAE;IACjB,OAAO,GAAG,EAAE,CAAC,IAAI,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AACxD,CAAC,EACD,gBAAgB,CAAC,OAAO,EACxB,KAAK,CACR,CAAC", "sourcesContent": ["import { WebXRFeaturesManager, WebXRFeatureName } from \"../webXRFeaturesManager\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { Vector3, TmpVectors } from \"../../Maths/math.vector\";\r\nimport { Ray } from \"../../Culling/ray\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * The WebXR Eye Tracking feature grabs eye data from the device and provides it in an easy-access format.\r\n * Currently only enabled for BabylonNative applications.\r\n */\r\nexport class WebXREyeTracking extends WebXRAbstractFeature {\r\n    private _latestEyeSpace: Nullable<XRSpace>;\r\n    private _gazeRay: Nullable<Ray>;\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.EYE_TRACKING;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * This observable will notify registered observers when eye tracking starts\r\n     */\r\n    public readonly onEyeTrackingStartedObservable: Observable<Ray> = new Observable();\r\n    /**\r\n     * This observable will notify registered observers when eye tracking ends\r\n     */\r\n    public readonly onEyeTrackingEndedObservable: Observable<void> = new Observable();\r\n    /**\r\n     * This observable will notify registered observers on each frame that has valid tracking\r\n     */\r\n    public readonly onEyeTrackingFrameUpdateObservable: Observable<Ray> = new Observable();\r\n\r\n    /**\r\n     * Creates a new instance of the XR eye tracking feature.\r\n     * @param _xrSessionManager An instance of WebXRSessionManager.\r\n     */\r\n    constructor(_xrSessionManager: WebXRSessionManager) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"eye-tracking\";\r\n        if (this._xrSessionManager.session) {\r\n            this._init();\r\n        } else {\r\n            this._xrSessionManager.onXRSessionInit.addOnce(() => {\r\n                this._init();\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached.\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n\r\n        this._xrSessionManager.session.removeEventListener(\"eyetrackingstart\", this._eyeTrackingStartListener);\r\n        this._xrSessionManager.session.removeEventListener(\"eyetrackingend\", this._eyeTrackingEndListener);\r\n\r\n        this.onEyeTrackingStartedObservable.clear();\r\n        this.onEyeTrackingEndedObservable.clear();\r\n        this.onEyeTrackingFrameUpdateObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Returns whether the gaze data is valid or not\r\n     * @returns true if the data is valid\r\n     */\r\n    public get isEyeGazeValid(): boolean {\r\n        return !!this._gazeRay;\r\n    }\r\n\r\n    /**\r\n     * Get a reference to the gaze ray. This data is valid while eye tracking persists, and will be set to null when gaze data is no longer available\r\n     * @returns a reference to the gaze ray if it exists and is valid, returns null otherwise.\r\n     */\r\n    public getEyeGaze(): Nullable<Ray> {\r\n        return this._gazeRay;\r\n    }\r\n\r\n    protected _onXRFrame(frame: XRFrame) {\r\n        if (!this.attached || !frame) {\r\n            return;\r\n        }\r\n\r\n        if (this._latestEyeSpace && this._gazeRay) {\r\n            const pose = frame.getPose(this._latestEyeSpace, this._xrSessionManager.referenceSpace);\r\n            if (pose) {\r\n                this._gazeRay.origin.set(pose.transform.position.x, pose.transform.position.y, pose.transform.position.z).scaleInPlace(this._xrSessionManager.worldScalingFactor);\r\n                const quat = pose.transform.orientation;\r\n                TmpVectors.Quaternion[0].set(quat.x, quat.y, quat.z, quat.w);\r\n\r\n                if (!this._xrSessionManager.scene.useRightHandedSystem) {\r\n                    this._gazeRay.origin.z *= -1;\r\n                    TmpVectors.Quaternion[0].z *= -1;\r\n                    TmpVectors.Quaternion[0].w *= -1;\r\n\r\n                    Vector3.LeftHandedForwardReadOnly.rotateByQuaternionToRef(TmpVectors.Quaternion[0], this._gazeRay.direction);\r\n                } else {\r\n                    Vector3.RightHandedForwardReadOnly.rotateByQuaternionToRef(TmpVectors.Quaternion[0], this._gazeRay.direction);\r\n                }\r\n\r\n                this.onEyeTrackingFrameUpdateObservable.notifyObservers(this._gazeRay);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _eyeTrackingStartListener = (event: XREyeTrackingSourceEvent) => {\r\n        this._latestEyeSpace = event.gazeSpace;\r\n        this._gazeRay = new Ray(Vector3.Zero(), Vector3.Forward());\r\n        this.onEyeTrackingStartedObservable.notifyObservers(this._gazeRay);\r\n    };\r\n\r\n    private _eyeTrackingEndListener = () => {\r\n        this._latestEyeSpace = null;\r\n        this._gazeRay = null;\r\n        this.onEyeTrackingEndedObservable.notifyObservers();\r\n    };\r\n\r\n    private _init() {\r\n        // Only supported by BabylonNative\r\n        if (this._xrSessionManager.isNative) {\r\n            this._xrSessionManager.session.addEventListener(\"eyetrackingstart\", this._eyeTrackingStartListener);\r\n            this._xrSessionManager.session.addEventListener(\"eyetrackingend\", this._eyeTrackingEndListener);\r\n        }\r\n    }\r\n}\r\n\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXREyeTracking.Name,\r\n    (xrSessionManager) => {\r\n        return () => new WebXREyeTracking(xrSessionManager);\r\n    },\r\n    WebXREyeTracking.Version,\r\n    false\r\n);\r\n"]}