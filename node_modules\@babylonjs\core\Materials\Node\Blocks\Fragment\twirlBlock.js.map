{"version": 3, "file": "twirlBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Fragment/twirlBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAExD;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,iBAAiB;IAC7C;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC3E,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC5E,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;IAC1E,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa;QAChB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC1B,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC7C,WAAW,CAAC,KAAK,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE1C,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC5B,MAAM,aAAa,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;YACjD,aAAa,CAAC,KAAK,GAAG,GAAG,CAAC;YAE1B,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACjD;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC1B,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC7C,WAAW,CAAC,KAAK,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEtC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC7C;IACL,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,SAAS,GAAG,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACtD,MAAM,SAAS,GAAG,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACtD,MAAM,KAAK,GAAG,KAAK,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,KAAK,GAAG,KAAK,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,UAAU,GAAG,KAAK,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAExD,KAAK,CAAC,iBAAiB,IAAI;mBAChB,SAAS,MAAM,IAAI,CAAC,KAAK,CAAC,sBAAsB,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB;oBACvF,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC,sBAAsB,aAAa,SAAS;oBACzE,KAAK,UAAU,SAAS,OAAO,SAAS,YAAY,SAAS,OAAO,SAAS;oBAC7E,KAAK,UAAU,SAAS,OAAO,SAAS,YAAY,SAAS,OAAO,SAAS;mBAC9E,UAAU,WAAW,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB,QAAQ,IAAI,CAAC,MAAM,CAAC,sBAAsB,OAAO,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB,QAAQ,IAAI,CAAC,MAAM,CAAC,sBAAsB;SACpN,CAAC;QAEF,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC1B,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,UAAU,KAAK,CAAC;SAC9F;QAED,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE;YACrB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,UAAU,OAAO,CAAC;SAC3F;QAED,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE;YACrB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,UAAU,OAAO,CAAC;SAC3F;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { Vector2 } from \"../../../../Maths/math.vector\";\r\n\r\n/**\r\n * Block used to generate a twirl\r\n */\r\nexport class TwirlBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new TwirlBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerInput(\"input\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerInput(\"strength\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerInput(\"center\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerInput(\"offset\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerOutput(\"x\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"y\", NodeMaterialBlockConnectionPointTypes.Float);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"TwirlBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the input component\r\n     */\r\n    public get input(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the strength component\r\n     */\r\n    public get strength(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the center component\r\n     */\r\n    public get center(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the offset component\r\n     */\r\n    public get offset(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the x output component\r\n     */\r\n    public get x(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the y output component\r\n     */\r\n    public get y(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    public autoConfigure() {\r\n        if (!this.center.isConnected) {\r\n            const centerInput = new InputBlock(\"center\");\r\n            centerInput.value = new Vector2(0.5, 0.5);\r\n\r\n            centerInput.output.connectTo(this.center);\r\n        }\r\n\r\n        if (!this.strength.isConnected) {\r\n            const strengthInput = new InputBlock(\"strength\");\r\n            strengthInput.value = 1.0;\r\n\r\n            strengthInput.output.connectTo(this.strength);\r\n        }\r\n\r\n        if (!this.offset.isConnected) {\r\n            const offsetInput = new InputBlock(\"offset\");\r\n            offsetInput.value = new Vector2(0, 0);\r\n\r\n            offsetInput.output.connectTo(this.offset);\r\n        }\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const tempDelta = state._getFreeVariableName(\"delta\");\r\n        const tempAngle = state._getFreeVariableName(\"angle\");\r\n        const tempX = state._getFreeVariableName(\"x\");\r\n        const tempY = state._getFreeVariableName(\"y\");\r\n        const tempResult = state._getFreeVariableName(\"result\");\r\n\r\n        state.compilationString += `\r\n            vec2 ${tempDelta} = ${this.input.associatedVariableName} - ${this.center.associatedVariableName};\r\n            float ${tempAngle} = ${this.strength.associatedVariableName} * length(${tempDelta});\r\n            float ${tempX} = cos(${tempAngle}) * ${tempDelta}.x - sin(${tempAngle}) * ${tempDelta}.y;\r\n            float ${tempY} = sin(${tempAngle}) * ${tempDelta}.x + cos(${tempAngle}) * ${tempDelta}.y;\r\n            vec2 ${tempResult} = vec2(${tempX} + ${this.center.associatedVariableName}.x + ${this.offset.associatedVariableName}.x, ${tempY} + ${this.center.associatedVariableName}.y + ${this.offset.associatedVariableName}.y);\r\n        `;\r\n\r\n        if (this.output.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(this.output, state) + ` = ${tempResult};\\n`;\r\n        }\r\n\r\n        if (this.x.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(this.x, state) + ` = ${tempResult}.x;\\n`;\r\n        }\r\n\r\n        if (this.y.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(this.y, state) + ` = ${tempResult}.y;\\n`;\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.TwirlBlock\", TwirlBlock);\r\n"]}