{"version": 3, "file": "subSurfaceSceneComponent.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Rendering/subSurfaceSceneComponent.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AAEjC,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AACpE,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,wCAAwC;AACxC,aAAa,CAAC,SAAS,CAAC,uBAAuB,CAAC,eAAe,EAAE,CAAC,UAAe,EAAE,KAAY,EAAE,EAAE;IAC/F,qBAAqB;IACrB,IAAI,UAAU,CAAC,wBAAwB,KAAK,SAAS,IAAI,UAAU,CAAC,wBAAwB,KAAK,IAAI,EAAE;QACnG,KAAK,CAAC,0BAA0B,EAAE,CAAC;QACnC,IAAI,KAAK,CAAC,uBAAuB,EAAE;YAC/B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,wBAAwB,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE;gBAC5F,MAAM,KAAK,GAAG,UAAU,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;gBACzD,KAAK,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aAC5F;SACJ;KACJ;AACL,CAAC,CAAC,CAAC;AAyBH,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,yBAAyB,EAAE;IAC9D,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IACD,GAAG,EAAE,UAAuB,KAAwC;QAChE,IAAI,KAAK,EAAE;YACP,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;gBAC9B,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;aACzC;SACJ;IACL,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,KAAK,CAAC,SAAS,CAAC,0BAA0B,GAAG;IACzC,IAAI,IAAI,CAAC,wBAAwB,EAAE;QAC/B,OAAO,IAAI,CAAC,wBAAwB,CAAC;KACxC;IAED,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACrD,IAAI,eAAe,EAAE;QACjB,IAAI,CAAC,wBAAwB,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAClE,eAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC,wBAAwB,CAAC;KACxC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,2BAA2B,GAAG;IAC1C,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;QAChC,OAAO;KACV;IAED,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,CAAC;IACxC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;AACzC,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,OAAO,wBAAwB;IAWjC;;;OAGG;IACH,YAAY,KAAY;QAdxB;;WAEG;QACa,SAAI,GAAG,uBAAuB,CAAC,oBAAoB,CAAC;QAYhE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,QAAQ,KAAU,CAAC;IAE1B;;;OAGG;IACI,SAAS,CAAC,mBAAwB;QACrC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;YACrC,OAAO;SACV;QAED,MAAM,wBAAwB,GAAG,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,wBAAwB,CAAC;QAC7F,mBAAmB,CAAC,wBAAwB,GAAG,EAAE,CAAC;QAElD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,wBAAwB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtD,mBAAmB,CAAC,wBAAwB,CAAC,IAAI,CAAC;gBAC9C,CAAC,EAAE,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChC,CAAC,EAAE,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChC,CAAC,EAAE,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;aACnC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,gBAAgB;IACpB,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,uCAAuC;QACvC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;YAC7B,OAAO;SACV;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;YACpC,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,yBAAyB,EAAE,CAAC;SAClE;IACL,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,mCAAmC;IACvC,CAAC;IAED;;OAEG;IACI,OAAO;QACV,mCAAmC;IACvC,CAAC;CACJ;AAED,uBAAuB,CAAC,6BAA6B,GAAG,CAAC,KAAY,EAAE,EAAE;IACrE,gDAAgD;IAChD,IAAI,SAAS,GAAG,KAAK,CAAC,aAAa,CAAC,uBAAuB,CAAC,eAAe,CAA6B,CAAC;IACzG,IAAI,CAAC,SAAS,EAAE;QACZ,SAAS,GAAG,IAAI,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAChD,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;KAClC;AACL,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Scene } from \"../scene\";\r\nimport type { ISceneSerializableComponent } from \"../sceneComponent\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport { SubSurfaceConfiguration } from \"./subSurfaceConfiguration\";\r\nimport { AbstractScene } from \"../abstractScene\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\n\r\n// Adds the parser to the scene parsers.\r\nAbstractScene.AddParser(SceneComponentConstants.NAME_SUBSURFACE, (parsedData: any, scene: Scene) => {\r\n    // Diffusion profiles\r\n    if (parsedData.ssDiffusionProfileColors !== undefined && parsedData.ssDiffusionProfileColors !== null) {\r\n        scene.enableSubSurfaceForPrePass();\r\n        if (scene.subSurfaceConfiguration) {\r\n            for (let index = 0, cache = parsedData.ssDiffusionProfileColors.length; index < cache; index++) {\r\n                const color = parsedData.ssDiffusionProfileColors[index];\r\n                scene.subSurfaceConfiguration.addDiffusionProfile(new Color3(color.r, color.g, color.b));\r\n            }\r\n        }\r\n    }\r\n});\r\n\r\ndeclare module \"../abstractScene\" {\r\n    export interface AbstractScene {\r\n        /** @internal (Backing field) */\r\n        _subSurfaceConfiguration: Nullable<SubSurfaceConfiguration>;\r\n\r\n        /**\r\n         * Gets or Sets the current prepass renderer associated to the scene.\r\n         */\r\n        subSurfaceConfiguration: Nullable<SubSurfaceConfiguration>;\r\n\r\n        /**\r\n         * Enables the subsurface effect for prepass\r\n         * @returns the SubSurfaceConfiguration\r\n         */\r\n        enableSubSurfaceForPrePass(): Nullable<SubSurfaceConfiguration>;\r\n\r\n        /**\r\n         * Disables the subsurface effect for prepass\r\n         */\r\n        disableSubSurfaceForPrePass(): void;\r\n    }\r\n}\r\n\r\nObject.defineProperty(Scene.prototype, \"subSurfaceConfiguration\", {\r\n    get: function (this: Scene) {\r\n        return this._subSurfaceConfiguration;\r\n    },\r\n    set: function (this: Scene, value: Nullable<SubSurfaceConfiguration>) {\r\n        if (value) {\r\n            if (this.enablePrePassRenderer()) {\r\n                this._subSurfaceConfiguration = value;\r\n            }\r\n        }\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nScene.prototype.enableSubSurfaceForPrePass = function (): Nullable<SubSurfaceConfiguration> {\r\n    if (this._subSurfaceConfiguration) {\r\n        return this._subSurfaceConfiguration;\r\n    }\r\n\r\n    const prePassRenderer = this.enablePrePassRenderer();\r\n    if (prePassRenderer) {\r\n        this._subSurfaceConfiguration = new SubSurfaceConfiguration(this);\r\n        prePassRenderer.addEffectConfiguration(this._subSurfaceConfiguration);\r\n        return this._subSurfaceConfiguration;\r\n    }\r\n\r\n    return null;\r\n};\r\n\r\nScene.prototype.disableSubSurfaceForPrePass = function (): void {\r\n    if (!this._subSurfaceConfiguration) {\r\n        return;\r\n    }\r\n\r\n    this._subSurfaceConfiguration.dispose();\r\n    this._subSurfaceConfiguration = null;\r\n};\r\n\r\n/**\r\n * Defines the Geometry Buffer scene component responsible to manage a G-Buffer useful\r\n * in several rendering techniques.\r\n */\r\nexport class SubSurfaceSceneComponent implements ISceneSerializableComponent {\r\n    /**\r\n     * The component name helpful to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_PREPASSRENDERER;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene: Scene) {\r\n        this.scene = scene;\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {}\r\n\r\n    /**\r\n     * Serializes the component data to the specified json object\r\n     * @param serializationObject The object to serialize to\r\n     */\r\n    public serialize(serializationObject: any): void {\r\n        if (!this.scene.subSurfaceConfiguration) {\r\n            return;\r\n        }\r\n\r\n        const ssDiffusionProfileColors = this.scene.subSurfaceConfiguration.ssDiffusionProfileColors;\r\n        serializationObject.ssDiffusionProfileColors = [];\r\n\r\n        for (let i = 0; i < ssDiffusionProfileColors.length; i++) {\r\n            serializationObject.ssDiffusionProfileColors.push({\r\n                r: ssDiffusionProfileColors[i].r,\r\n                g: ssDiffusionProfileColors[i].g,\r\n                b: ssDiffusionProfileColors[i].b,\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds all the elements from the container to the scene\r\n     */\r\n    public addFromContainer(): void {\r\n        // Nothing to do\r\n    }\r\n\r\n    /**\r\n     * Removes all the elements in the container from the scene\r\n     */\r\n    public removeFromContainer(): void {\r\n        // Make sure nothing will be serialized\r\n        if (!this.scene.prePassRenderer) {\r\n            return;\r\n        }\r\n\r\n        if (this.scene.subSurfaceConfiguration) {\r\n            this.scene.subSurfaceConfiguration.clearAllDiffusionProfiles();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        // Nothing to do for this component\r\n    }\r\n\r\n    /**\r\n     * Disposes the component and the associated resources\r\n     */\r\n    public dispose(): void {\r\n        // Nothing to do for this component\r\n    }\r\n}\r\n\r\nSubSurfaceConfiguration._SceneComponentInitialization = (scene: Scene) => {\r\n    // Register the G Buffer component to the scene.\r\n    let component = scene._getComponent(SceneComponentConstants.NAME_SUBSURFACE) as SubSurfaceSceneComponent;\r\n    if (!component) {\r\n        component = new SubSurfaceSceneComponent(scene);\r\n        scene._addComponent(component);\r\n    }\r\n};\r\n"]}