{"version": 3, "file": "particleSystem.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Particles/particleSystem.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAE1D,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC1D,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAK/C,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAI7C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AACvE,OAAO,EAAE,mBAAmB,EAAE,MAAM,oCAAoC,CAAC;AACzE,OAAO,EAAE,qBAAqB,EAAE,MAAM,sCAAsC,CAAC;AAC7E,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AAEvE,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAC3E,OAAO,EAAE,0BAA0B,EAAE,MAAM,2CAA2C,CAAC;AACvF,OAAO,EAAE,6BAA6B,EAAE,qBAAqB,EAAE,MAAM,sCAAsC,CAAC;AAC5G,OAAO,EAAE,+BAA+B,EAAE,uBAAuB,EAAE,MAAM,wCAAwC,CAAC;AAClH,OAAO,EAAE,mBAAmB,EAAE,MAAM,oCAAoC,CAAC;AACzE,OAAO,EACH,iBAAiB,EACjB,qBAAqB,EACrB,6BAA6B,EAC7B,2BAA2B,EAC3B,wBAAwB,EACxB,kBAAkB,EAClB,mBAAmB,GACtB,MAAM,4BAA4B,CAAC;AAEpC;;;;;GAKG;AACH,MAAM,OAAO,cAAe,SAAQ,kBAAkB;IAAtD;;QA4BI;;;WAGG;QACI,6BAAwB,GAAG,KAAK,CAAC;QA4JjC,sBAAiB,GAAiC,CAAC,QAAQ,EAAE,EAAE;YAClE,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtD,OAAO;aACV;YACD,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAE3E,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACpD,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc,CAAC,GAAG,EAAE;oBACxC,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;oBACrC,QAAQ,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;oBACrD,SAAS,CAAC,cAAc,CAAC,mBAAmB,GAAG,IAAI,CAAC;oBACpD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;oBACrD,SAAS,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;iBACpC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;IAo0BN,CAAC;IA1+BG;;;;;OAKG;IACI,kBAAkB,CAAC,UAAmB,EAAE,UAAmB;QAC9D,MAAM,eAAe,GAAG,kBAAkB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QACnE,IAAI,CAAC,mBAAmB,GAAG,eAAe,CAAC;QAC3C,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;OAKG;IACI,wBAAwB,CAAC,MAAM,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC;QACvD,MAAM,eAAe,GAAG,wBAAwB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QACtE,IAAI,CAAC,mBAAmB,GAAG,eAAe,CAAC;QAC3C,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC;QAClD,MAAM,eAAe,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QACjE,IAAI,CAAC,mBAAmB,GAAG,eAAe,CAAC;QAC3C,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG;IACI,2BAA2B,CAAC,MAAM,GAAG,CAAC,EAAE,UAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,UAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnH,MAAM,eAAe,GAAG,2BAA2B,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACpF,IAAI,CAAC,mBAAmB,GAAG,eAAe,CAAC;QAC3C,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;;;OAOG;IACI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,EAAE,mBAAmB,GAAG,CAAC;QACzF,MAAM,eAAe,GAAG,qBAAqB,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,mBAAmB,CAAC,CAAC;QAChG,IAAI,CAAC,mBAAmB,GAAG,eAAe,CAAC;QAC3C,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;;;;OAQG;IACI,6BAA6B,CAChC,MAAM,GAAG,CAAC,EACV,MAAM,GAAG,CAAC,EACV,WAAW,GAAG,CAAC,EACf,UAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EACnC,UAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QAEnC,MAAM,eAAe,GAAG,6BAA6B,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAC3G,IAAI,CAAC,mBAAmB,GAAG,eAAe,CAAC;QAC3C,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC;QACpD,MAAM,eAAe,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACzD,IAAI,CAAC,mBAAmB,GAAG,eAAe,CAAC;QAC3C,OAAO,eAAe,CAAC;IAC3B,CAAC;IACD;;;;;;;OAOG;IACI,gBAAgB,CAAC,UAAmB,EAAE,UAAmB,EAAE,UAAmB,EAAE,UAAmB;QACtG,MAAM,eAAe,GAAG,IAAI,kBAAkB,EAAE,CAAC;QACjD,IAAI,CAAC,mBAAmB,GAAG,eAAe,CAAC;QAC3C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,OAAO,eAAe,CAAC;IAC3B,CAAC;IAEO,+BAA+B;QACnC,IAAI,CAAC,YAAY,GAAG,IAAI,KAAK,EAAqB,CAAC;QACnD,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACpC,IAAI,UAAU,YAAY,cAAc,EAAE;oBACtC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;iBACxD;qBAAM,IAAI,UAAU,YAAY,UAAU,EAAE;oBACzC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;iBACxC;qBAAM,IAAI,UAAU,YAAY,KAAK,EAAE;oBACpC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBACtC;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAEO,gBAAgB;QACpB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO;SACV;QACD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YACxC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,GAAG,EAAsB,CAAC;IACnD,CAAC;IAEO,eAAe;QACnB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,OAAO;SACV;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACtE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC9D;QAED,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAmBM,SAAS;QACZ,wEAAwE;QACxE,IAAI,CAAC,+BAA+B,EAAE,CAAC;QAEvC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE;YACpD,IAAI,CAAC,gBAAgB,GAAG,EAAsB,CAAC;SAClD;IACL,CAAC;IAEM,SAAS,CAAC,eAAwB;QACrC,IAAI,eAAe,EAAE;YACjB,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;IACL,CAAC;IAEM,gBAAgB,CAAC,QAAkB;QACtC,kBAAkB;QAClB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YACnD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;YAC5F,QAAQ,CAAC,oBAAoB,GAAG,EAAE,CAAC;YACnC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC/B,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc,CAAC,QAAQ,EAAE;oBAC7C,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;oBAClB,QAAQ,CAAC,oBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACpE,UAAU,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;iBACrC;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED,gBAAgB;IACT,UAAU,CAAC,0BAA0B,GAAG,KAAK,EAAE,qBAAqB,GAAG,KAAK;QAC/E,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACxC,IAAI,CAAC,+BAA+B,EAAE,CAAC;SAC1C;QAED,IAAI,0BAA0B,EAAE;YAC5B,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjC,IAAI,QAAQ,CAAC,oBAAoB,EAAE;oBAC/B,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;wBACnE,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;qBAC9C;iBACJ;YACL,CAAC,CAAC,CAAC;SACN;QAED,IAAI,qBAAqB,EAAE;YACvB,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;oBAC3D,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;iBACtC;aACJ;SACJ;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YAC/C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC3D,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;oBAC/C,UAAU,CAAC,OAAO,EAAE,CAAC;iBACxB;aACJ;YAED,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;SACzB;QAED,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,OAAO,IAAK,IAAI,CAAC,OAAwB,CAAC,OAAO,EAAE;YAC1E,IAAI,CAAC,OAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC9C;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,oBAAyB,EAAE,cAA+B,EAAE,aAAiC,EAAE,OAAe;QAC/H,IAAI,KAAsB,CAAC;QAE3B,IAAI,aAAa,YAAY,UAAU,EAAE;YACrC,KAAK,GAAG,IAAI,CAAC;SAChB;aAAM;YACH,KAAK,GAAG,aAAsB,CAAC;SAClC;QAED,MAAM,aAAa,GAAG,QAAQ,CAAC,iBAAiB,CAAC,CAAC;QAClD,IAAI,aAAa,IAAI,KAAK,EAAE;YACxB,UAAU;YACV,IAAI,oBAAoB,CAAC,OAAO,EAAE;gBAC9B,cAAc,CAAC,eAAe,GAAG,aAAa,CAAC,KAAK,CAAC,oBAAoB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAgB,CAAC;aACrH;iBAAM,IAAI,oBAAoB,CAAC,WAAW,EAAE;gBACzC,cAAc,CAAC,eAAe,GAAG,IAAI,aAAa,CAC9C,OAAO,GAAG,oBAAoB,CAAC,WAAW,EAC1C,KAAK,EACL,KAAK,EACL,oBAAoB,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CACnF,CAAC;gBACF,cAAc,CAAC,eAAgB,CAAC,IAAI,GAAG,oBAAoB,CAAC,WAAW,CAAC;aAC3E;SACJ;QAED,UAAU;QACV,IAAI,CAAC,oBAAoB,CAAC,SAAS,IAAI,oBAAoB,CAAC,SAAS,KAAK,CAAC,IAAI,oBAAoB,CAAC,OAAO,KAAK,SAAS,EAAE;YACvH,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;SAC3C;aAAM,IAAI,oBAAoB,CAAC,SAAS,IAAI,KAAK,EAAE;YAChD,cAAc,CAAC,OAAO,GAAG,KAAK,CAAC,eAAe,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;SAClF;aAAM;YACH,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;SAC5E;QAED,cAAc,CAAC,OAAO,GAAG,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC;QAExD,QAAQ;QACR,IAAI,oBAAoB,CAAC,gBAAgB,KAAK,SAAS,EAAE;YACrD,cAAc,CAAC,gBAAgB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC;SAC3E;QAED,IAAI,oBAAoB,CAAC,gBAAgB,KAAK,SAAS,EAAE;YACrD,cAAc,CAAC,gBAAgB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC;SAC3E;QAED,IAAI,oBAAoB,CAAC,aAAa,KAAK,SAAS,EAAE;YAClD,cAAc,CAAC,aAAa,GAAG,oBAAoB,CAAC,aAAa,CAAC;SACrE;QAED,IAAI,oBAAoB,CAAC,mBAAmB,KAAK,SAAS,EAAE;YACxD,cAAc,CAAC,mBAAmB,GAAG,oBAAoB,CAAC,mBAAmB,CAAC;SACjF;QAED,aAAa;QACb,IAAI,oBAAoB,CAAC,UAAU,EAAE;YACjC,KAAK,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,oBAAoB,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,EAAE,EAAE;gBACpG,MAAM,eAAe,GAAG,oBAAoB,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;gBACxE,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAAmB,CAAC,CAAC;gBACpD,IAAI,aAAa,EAAE;oBACf,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;iBACxE;aACJ;YACD,cAAc,CAAC,qBAAqB,GAAG,oBAAoB,CAAC,qBAAqB,CAAC;YAClF,cAAc,CAAC,kBAAkB,GAAG,oBAAoB,CAAC,kBAAkB,CAAC;YAC5E,cAAc,CAAC,gBAAgB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC;YACxE,cAAc,CAAC,kBAAkB,GAAG,oBAAoB,CAAC,kBAAkB,CAAC;SAC/E;QAED,IAAI,oBAAoB,CAAC,WAAW,IAAI,KAAK,EAAE;YAC3C,KAAK,CAAC,cAAc,CAChB,cAAc,EACd,oBAAoB,CAAC,eAAe,EACpC,oBAAoB,CAAC,aAAa,EAClC,oBAAoB,CAAC,eAAe,EACpC,oBAAoB,CAAC,gBAAgB,IAAI,GAAG,CAC/C,CAAC;SACL;QAED,kBAAkB;QAClB,cAAc,CAAC,UAAU,GAAG,oBAAoB,CAAC,UAAU,GAAG,CAAC,CAAC;QAChE,cAAc,CAAC,eAAe,GAAG,oBAAoB,CAAC,eAAe,CAAC;QACtE,cAAc,CAAC,eAAe,GAAG,oBAAoB,CAAC,eAAe,CAAC;QACtE,cAAc,CAAC,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC;QACtD,cAAc,CAAC,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC;QAEtD,IAAI,oBAAoB,CAAC,SAAS,EAAE;YAChC,cAAc,CAAC,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC;YAC1D,cAAc,CAAC,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC;YAC1D,cAAc,CAAC,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC;YAC1D,cAAc,CAAC,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC;SAC7D;QAED,IAAI,oBAAoB,CAAC,aAAa,KAAK,SAAS,EAAE;YAClD,cAAc,CAAC,aAAa,GAAG,oBAAoB,CAAC,aAAa,CAAC;YAClE,cAAc,CAAC,iBAAiB,GAAG,oBAAoB,CAAC,iBAAiB,CAAC;SAC7E;QAED,IAAI,oBAAoB,CAAC,kBAAkB,KAAK,SAAS,EAAE;YACvD,cAAc,CAAC,kBAAkB,GAAG,oBAAoB,CAAC,kBAAkB,CAAC;YAC5E,cAAc,CAAC,kBAAkB,GAAG,oBAAoB,CAAC,kBAAkB,CAAC;SAC/E;QAED,cAAc,CAAC,WAAW,GAAG,oBAAoB,CAAC,WAAW,CAAC;QAC9D,cAAc,CAAC,WAAW,GAAG,oBAAoB,CAAC,WAAW,CAAC;QAC9D,cAAc,CAAC,YAAY,GAAG,oBAAoB,CAAC,YAAY,CAAC;QAChE,cAAc,CAAC,YAAY,GAAG,oBAAoB,CAAC,YAAY,CAAC;QAChE,cAAc,CAAC,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC;QACxD,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,oBAAoB,CAAC,aAAa,EAAE;YACpC,cAAc,CAAC,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;SACxF;QACD,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QACtE,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QACtE,cAAc,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAC5E,cAAc,CAAC,WAAW,GAAG,oBAAoB,CAAC,WAAW,CAAC;QAC9D,cAAc,CAAC,kBAAkB,GAAG,oBAAoB,CAAC,kBAAkB,CAAC;QAC5E,cAAc,CAAC,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC;QAE1D,IAAI,oBAAoB,CAAC,cAAc,EAAE;YACrC,KAAK,MAAM,aAAa,IAAI,oBAAoB,CAAC,cAAc,EAAE;gBAC7D,cAAc,CAAC,gBAAgB,CAC3B,aAAa,CAAC,QAAQ,EACtB,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,EACtC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAC5E,CAAC;aACL;SACJ;QAED,IAAI,oBAAoB,CAAC,aAAa,EAAE;YACpC,KAAK,MAAM,YAAY,IAAI,oBAAoB,CAAC,aAAa,EAAE;gBAC3D,cAAc,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;aAC/F;YACD,cAAc,CAAC,gBAAgB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC;SAC3E;QAED,IAAI,oBAAoB,CAAC,mBAAmB,EAAE;YAC1C,KAAK,MAAM,kBAAkB,IAAI,oBAAoB,CAAC,mBAAmB,EAAE;gBACvE,cAAc,CAAC,qBAAqB,CAChC,kBAAkB,CAAC,QAAQ,EAC3B,kBAAkB,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC,MAAM,EACjG,kBAAkB,CAAC,OAAO,CAC7B,CAAC;aACL;SACJ;QAED,IAAI,oBAAoB,CAAC,mBAAmB,EAAE;YAC1C,KAAK,MAAM,kBAAkB,IAAI,oBAAoB,CAAC,mBAAmB,EAAE;gBACvE,cAAc,CAAC,qBAAqB,CAChC,kBAAkB,CAAC,QAAQ,EAC3B,kBAAkB,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC,MAAM,EACjG,kBAAkB,CAAC,OAAO,CAC7B,CAAC;aACL;SACJ;QAED,IAAI,oBAAoB,CAAC,aAAa,EAAE;YACpC,KAAK,MAAM,YAAY,IAAI,oBAAoB,CAAC,aAAa,EAAE;gBAC3D,cAAc,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;aAChK;SACJ;QAED,IAAI,oBAAoB,CAAC,qBAAqB,EAAE;YAC5C,KAAK,MAAM,oBAAoB,IAAI,oBAAoB,CAAC,qBAAqB,EAAE;gBAC3E,cAAc,CAAC,uBAAuB,CAClC,oBAAoB,CAAC,QAAQ,EAC7B,oBAAoB,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,oBAAoB,CAAC,MAAM,EACvG,oBAAoB,CAAC,OAAO,CAC/B,CAAC;aACL;SACJ;QAED,IAAI,oBAAoB,CAAC,iBAAiB,EAAE;YACxC,KAAK,MAAM,gBAAgB,IAAI,oBAAoB,CAAC,iBAAiB,EAAE;gBACnE,cAAc,CAAC,mBAAmB,CAC9B,gBAAgB,CAAC,QAAQ,EACzB,gBAAgB,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,EAC3F,gBAAgB,CAAC,OAAO,CAC3B,CAAC;aACL;SACJ;QAED,IAAI,oBAAoB,CAAC,aAAa,EAAE;YACpC,KAAK,MAAM,YAAY,IAAI,oBAAoB,CAAC,aAAa,EAAE;gBAC3D,cAAc,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;aAChK;SACJ;QAED,IAAI,oBAAoB,CAAC,iBAAiB,EAAE;YACxC,KAAK,MAAM,gBAAgB,IAAI,oBAAoB,CAAC,iBAAiB,EAAE;gBACnE,cAAc,CAAC,mBAAmB,CAC9B,gBAAgB,CAAC,QAAQ,EACzB,gBAAgB,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,EAC3F,gBAAgB,CAAC,OAAO,CAC3B,CAAC;aACL;SACJ;QAED,IAAI,oBAAoB,CAAC,kBAAkB,EAAE;YACzC,KAAK,MAAM,iBAAiB,IAAI,oBAAoB,CAAC,kBAAkB,EAAE;gBACrE,cAAc,CAAC,oBAAoB,CAC/B,iBAAiB,CAAC,QAAQ,EAC1B,iBAAiB,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,EAC9F,iBAAiB,CAAC,OAAO,CAC5B,CAAC;aACL;SACJ;QAED,IAAI,oBAAoB,CAAC,iBAAiB,EAAE;YACxC,KAAK,MAAM,gBAAgB,IAAI,oBAAoB,CAAC,iBAAiB,EAAE;gBACnE,cAAc,CAAC,mBAAmB,CAC9B,gBAAgB,CAAC,QAAQ,EACzB,gBAAgB,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,EAC3F,gBAAgB,CAAC,OAAO,CAC3B,CAAC;aACL;SACJ;QAED,IAAI,oBAAoB,CAAC,sBAAsB,EAAE;YAC7C,KAAK,MAAM,qBAAqB,IAAI,oBAAoB,CAAC,sBAAsB,EAAE;gBAC7E,cAAc,CAAC,wBAAwB,CACnC,qBAAqB,CAAC,QAAQ,EAC9B,qBAAqB,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB,CAAC,MAAM,EAC1G,qBAAqB,CAAC,OAAO,CAChC,CAAC;aACL;YACD,cAAc,CAAC,oBAAoB,GAAG,oBAAoB,CAAC,oBAAoB,CAAC;SACnF;QAED,IAAI,oBAAoB,CAAC,YAAY,IAAI,KAAK,EAAE;YAC5C,MAAM,aAAa,GAAG,QAAQ,CAAC,2BAA2B,CAAC,CAAC;YAC5D,cAAc,CAAC,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,oBAAoB,CAAC,YAAY,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACxG;QAED,UAAU;QACV,IAAI,WAAiC,CAAC;QACtC,IAAI,oBAAoB,CAAC,mBAAmB,EAAE;YAC1C,QAAQ,oBAAoB,CAAC,mBAAmB,CAAC,IAAI,EAAE;gBACnD,KAAK,uBAAuB;oBACxB,WAAW,GAAG,IAAI,qBAAqB,EAAE,CAAC;oBAC1C,MAAM;gBACV,KAAK,+BAA+B;oBAChC,WAAW,GAAG,IAAI,6BAA6B,EAAE,CAAC;oBAClD,MAAM;gBACV,KAAK,aAAa,CAAC;gBACnB,KAAK,qBAAqB;oBACtB,WAAW,GAAG,IAAI,mBAAmB,EAAE,CAAC;oBACxC,MAAM;gBACV,KAAK,yBAAyB;oBAC1B,WAAW,GAAG,IAAI,uBAAuB,EAAE,CAAC;oBAC5C,MAAM;gBACV,KAAK,iCAAiC;oBAClC,WAAW,GAAG,IAAI,+BAA+B,EAAE,CAAC;oBACpD,MAAM;gBACV,KAAK,4BAA4B;oBAC7B,WAAW,GAAG,IAAI,0BAA0B,EAAE,CAAC;oBAC/C,MAAM;gBACV,KAAK,sBAAsB;oBACvB,WAAW,GAAG,IAAI,oBAAoB,EAAE,CAAC;oBACzC,MAAM;gBACV,KAAK,qBAAqB;oBACtB,WAAW,GAAG,IAAI,mBAAmB,EAAE,CAAC;oBACxC,MAAM;gBACV,KAAK,uBAAuB;oBACxB,WAAW,GAAG,IAAI,qBAAqB,EAAE,CAAC;oBAC1C,MAAM;gBACV,KAAK,YAAY,CAAC;gBAClB,KAAK,oBAAoB,CAAC;gBAC1B;oBACI,WAAW,GAAG,IAAI,kBAAkB,EAAE,CAAC;oBACvC,MAAM;aACb;YAED,WAAW,CAAC,KAAK,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;SACtE;aAAM;YACH,WAAW,GAAG,IAAI,kBAAkB,EAAE,CAAC;YACvC,WAAW,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;SAClD;QACD,cAAc,CAAC,mBAAmB,GAAG,WAAW,CAAC;QAEjD,kBAAkB;QAClB,cAAc,CAAC,iBAAiB,GAAG,oBAAoB,CAAC,iBAAiB,CAAC;QAC1E,cAAc,CAAC,eAAe,GAAG,oBAAoB,CAAC,eAAe,CAAC;QACtE,cAAc,CAAC,cAAc,GAAG,oBAAoB,CAAC,cAAc,IAAI,IAAI,CAAC;QAC5E,cAAc,CAAC,eAAe,GAAG,oBAAoB,CAAC,eAAe,CAAC;QACtE,cAAc,CAAC,gBAAgB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC;QACxE,cAAc,CAAC,qBAAqB,GAAG,oBAAoB,CAAC,qBAAqB,CAAC;QAClF,cAAc,CAAC,qBAAqB,GAAG,oBAAoB,CAAC,qBAAqB,CAAC;QAElF,cAAc,CAAC,aAAa,GAAG,oBAAoB,CAAC,aAAa,IAAI,KAAK,CAAC;QAC3E,cAAc,CAAC,eAAe,GAAG,oBAAoB,CAAC,eAAe,IAAI,CAAC,CAAC,CAAC;IAChF,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,KAAK,CAAC,oBAAyB,EAAE,aAAiC,EAAE,OAAe,EAAE,UAAU,GAAG,KAAK,EAAE,QAAiB;QACpI,MAAM,IAAI,GAAG,oBAAoB,CAAC,IAAI,CAAC;QACvC,IAAI,MAAM,GAAqB,IAAI,CAAC;QACpC,IAAI,OAAO,GAAQ,IAAI,CAAC;QACxB,IAAI,MAAkB,CAAC;QACvB,IAAI,KAAsB,CAAC;QAE3B,IAAI,aAAa,YAAY,UAAU,EAAE;YACrC,MAAM,GAAG,aAAa,CAAC;SAC1B;aAAM;YACH,KAAK,GAAG,aAAsB,CAAC;YAC/B,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;SAC9B;QAED,IAAI,oBAAoB,CAAC,YAAY,IAAK,MAAc,CAAC,wBAAwB,EAAE;YAC/E,OAAO,GAAG,oBAAoB,CAAC,YAAY,CAAC;YAC5C,MAAM,OAAO,GAAW,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjH,MAAM,GAAI,MAAc,CAAC,wBAAwB,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,EAAE,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;SAClK;QACD,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,QAAQ,IAAI,oBAAoB,CAAC,QAAQ,EAAE,aAAa,EAAE,MAAM,EAAE,oBAAoB,CAAC,uBAAuB,CAAC,CAAC;QAChK,cAAc,CAAC,YAAY,GAAG,OAAO,CAAC;QACtC,cAAc,CAAC,QAAQ,GAAG,OAAO,CAAC;QAElC,IAAI,oBAAoB,CAAC,EAAE,EAAE;YACzB,cAAc,CAAC,EAAE,GAAG,oBAAoB,CAAC,EAAE,CAAC;SAC/C;QAED,cAAc;QACd,IAAI,oBAAoB,CAAC,WAAW,EAAE;YAClC,cAAc,CAAC,WAAW,GAAG,EAAE,CAAC;YAChC,KAAK,MAAM,IAAI,IAAI,oBAAoB,CAAC,WAAW,EAAE;gBACjD,MAAM,SAAS,GAAG,EAAE,CAAC;gBACrB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;oBACpB,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC;iBACjE;gBAED,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9C;SACJ;QAED,cAAc,CAAC,MAAM,CAAC,oBAAoB,EAAE,cAAc,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAEpF,IAAI,oBAAoB,CAAC,WAAW,EAAE;YAClC,cAAc,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;SACnF;QAED,IAAI,oBAAoB,CAAC,WAAW,EAAE;YAClC,cAAc,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;SACpF;QAED,aAAa;QACb,IAAI,oBAAoB,CAAC,gBAAgB,EAAE;YACvC,cAAc,CAAC,gBAAgB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC;SAC3E;QAED,IAAI,CAAC,UAAU,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE;YACjD,cAAc,CAAC,KAAK,EAAE,CAAC;SAC1B;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,gBAAgB,GAAG,KAAK;QACrC,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QAEpC,cAAc,CAAC,UAAU,CAAC,mBAAmB,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAEvE,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC7D,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACrD,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC7D,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAE7D,cAAc;QACd,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,mBAAmB,CAAC,WAAW,GAAG,EAAE,CAAC;YAErC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACpB,IAAI,CAAC,+BAA+B,EAAE,CAAC;aAC1C;YAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;gBAClC,MAAM,IAAI,GAAG,EAAE,CAAC;gBAChB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;oBACpB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;iBAC9C;gBAED,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC9C;SACJ;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,UAAU,CAAC,mBAAwB,EAAE,cAA+B,EAAE,gBAAyB;QACzG,mBAAmB,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC;QAC/C,mBAAmB,CAAC,EAAE,GAAG,cAAc,CAAC,EAAE,CAAC;QAE3C,mBAAmB,CAAC,QAAQ,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC;QAE5D,mBAAmB,CAAC,aAAa,GAAG,cAAc,CAAC,aAAa,CAAC;QACjE,mBAAmB,CAAC,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;QAErE,UAAU;QACV,IAAmB,cAAc,CAAC,OAAQ,CAAC,QAAQ,EAAE;YACjD,MAAM,WAAW,GAAiB,cAAc,CAAC,OAAO,CAAC;YACzD,mBAAmB,CAAC,SAAS,GAAG,WAAW,CAAC,EAAE,CAAC;SAClD;aAAM;YACH,MAAM,eAAe,GAAY,cAAc,CAAC,OAAO,CAAC;YACxD,mBAAmB,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,EAAE,CAAC;SAC3D;QAED,UAAU;QACV,IAAI,cAAc,CAAC,mBAAmB,EAAE;YACpC,mBAAmB,CAAC,mBAAmB,GAAG,cAAc,CAAC,mBAAmB,CAAC,SAAS,EAAE,CAAC;SAC5F;QAED,IAAI,cAAc,CAAC,eAAe,EAAE;YAChC,IAAI,gBAAgB,EAAE;gBAClB,mBAAmB,CAAC,OAAO,GAAG,cAAc,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;aAC5E;iBAAM;gBACH,mBAAmB,CAAC,WAAW,GAAG,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC;gBACtE,mBAAmB,CAAC,OAAO,GAAG,CAAC,CAAE,cAAc,CAAC,eAAuB,CAAC,QAAQ,CAAC;aACpF;SACJ;QAED,mBAAmB,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;QAErD,aAAa;QACb,mBAAmB,CAAC,0BAA0B,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;QACpF,mBAAmB,CAAC,qBAAqB,GAAG,cAAc,CAAC,qBAAqB,CAAC;QACjF,mBAAmB,CAAC,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;QAC3E,mBAAmB,CAAC,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,CAAC;QACvE,mBAAmB,CAAC,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;QAE3E,kBAAkB;QAClB,mBAAmB,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;QAC3D,mBAAmB,CAAC,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,CAAC;QACvE,mBAAmB,CAAC,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,CAAC;QACvE,mBAAmB,CAAC,aAAa,GAAG,cAAc,CAAC,aAAa,CAAC;QACjE,mBAAmB,CAAC,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;QACrE,mBAAmB,CAAC,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;QACrE,mBAAmB,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;QACrD,mBAAmB,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;QACrD,mBAAmB,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;QACzD,mBAAmB,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;QACzD,mBAAmB,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;QACzD,mBAAmB,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;QACzD,mBAAmB,CAAC,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC;QAC/D,mBAAmB,CAAC,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC;QAC/D,mBAAmB,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW,CAAC;QAC7D,mBAAmB,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW,CAAC;QAC7D,mBAAmB,CAAC,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC;QACvD,mBAAmB,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC/D,mBAAmB,CAAC,aAAa,GAAG,cAAc,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC3E,mBAAmB,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAC7D,mBAAmB,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAC7D,mBAAmB,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACnE,mBAAmB,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW,CAAC;QAC7D,mBAAmB,CAAC,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;QAC3E,mBAAmB,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;QACzD,mBAAmB,CAAC,aAAa,GAAG,cAAc,CAAC,aAAa,CAAC;QACjE,mBAAmB,CAAC,iBAAiB,GAAG,cAAc,CAAC,iBAAiB,CAAC;QACzE,mBAAmB,CAAC,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;QAC3E,mBAAmB,CAAC,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;QAC3E,mBAAmB,CAAC,iBAAiB,GAAG,cAAc,CAAC,iBAAiB,CAAC;QACzE,mBAAmB,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;QACnE,mBAAmB,CAAC,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;QACrE,mBAAmB,CAAC,qBAAqB,GAAG,cAAc,CAAC,qBAAqB,CAAC;QACjF,mBAAmB,CAAC,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;QACrE,mBAAmB,CAAC,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,CAAC;QACvE,mBAAmB,CAAC,qBAAqB,GAAG,cAAc,CAAC,qBAAqB,CAAC;QACjF,mBAAmB,CAAC,uBAAuB,GAAG,cAAc,CAAC,uBAAuB,CAAC;QACrF,mBAAmB,CAAC,mBAAmB,GAAG,cAAc,CAAC,mBAAmB,CAAC;QAE7E,MAAM,cAAc,GAAG,cAAc,CAAC,iBAAiB,EAAE,CAAC;QAC1D,IAAI,cAAc,EAAE;YAChB,mBAAmB,CAAC,cAAc,GAAG,EAAE,CAAC;YACxC,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE;gBACxC,MAAM,kBAAkB,GAAQ;oBAC5B,QAAQ,EAAE,aAAa,CAAC,QAAQ;oBAChC,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC,OAAO,EAAE;iBACzC,CAAC;gBAEF,IAAI,aAAa,CAAC,MAAM,EAAE;oBACtB,kBAAkB,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;iBAC9D;qBAAM;oBACH,kBAAkB,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;iBAC9D;gBAED,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC/D;SACJ;QAED,MAAM,aAAa,GAAG,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACxD,IAAI,aAAa,EAAE;YACf,mBAAmB,CAAC,aAAa,GAAG,EAAE,CAAC;YACvC,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;gBACtC,MAAM,kBAAkB,GAAQ;oBAC5B,QAAQ,EAAE,YAAY,CAAC,QAAQ;oBAC/B,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE;iBACtC,CAAC;gBAEF,mBAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC9D;YACD,mBAAmB,CAAC,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,CAAC;SAC1E;QAED,MAAM,mBAAmB,GAAG,cAAc,CAAC,sBAAsB,EAAE,CAAC;QACpE,IAAI,mBAAmB,EAAE;YACrB,mBAAmB,CAAC,mBAAmB,GAAG,EAAE,CAAC;YAC7C,KAAK,MAAM,kBAAkB,IAAI,mBAAmB,EAAE;gBAClD,MAAM,kBAAkB,GAAQ;oBAC5B,QAAQ,EAAE,kBAAkB,CAAC,QAAQ;oBACrC,OAAO,EAAE,kBAAkB,CAAC,OAAO;iBACtC,CAAC;gBAEF,IAAI,kBAAkB,CAAC,OAAO,KAAK,SAAS,EAAE;oBAC1C,kBAAkB,CAAC,OAAO,GAAG,kBAAkB,CAAC,OAAO,CAAC;iBAC3D;qBAAM;oBACH,kBAAkB,CAAC,OAAO,GAAG,kBAAkB,CAAC,OAAO,CAAC;iBAC3D;gBAED,mBAAmB,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACpE;SACJ;QAED,MAAM,mBAAmB,GAAG,cAAc,CAAC,sBAAsB,EAAE,CAAC;QACpE,IAAI,mBAAmB,EAAE;YACrB,mBAAmB,CAAC,mBAAmB,GAAG,EAAE,CAAC;YAC7C,KAAK,MAAM,kBAAkB,IAAI,mBAAmB,EAAE;gBAClD,MAAM,kBAAkB,GAAQ;oBAC5B,QAAQ,EAAE,kBAAkB,CAAC,QAAQ;oBACrC,OAAO,EAAE,kBAAkB,CAAC,OAAO;iBACtC,CAAC;gBAEF,IAAI,kBAAkB,CAAC,OAAO,KAAK,SAAS,EAAE;oBAC1C,kBAAkB,CAAC,OAAO,GAAG,kBAAkB,CAAC,OAAO,CAAC;iBAC3D;qBAAM;oBACH,kBAAkB,CAAC,OAAO,GAAG,kBAAkB,CAAC,OAAO,CAAC;iBAC3D;gBAED,mBAAmB,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACpE;SACJ;QAED,MAAM,aAAa,GAAG,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACxD,IAAI,aAAa,EAAE;YACf,mBAAmB,CAAC,aAAa,GAAG,EAAE,CAAC;YACvC,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;gBACtC,MAAM,kBAAkB,GAAQ;oBAC5B,QAAQ,EAAE,YAAY,CAAC,QAAQ;oBAC/B,OAAO,EAAE,YAAY,CAAC,OAAO;iBAChC,CAAC;gBAEF,IAAI,YAAY,CAAC,OAAO,KAAK,SAAS,EAAE;oBACpC,kBAAkB,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;iBACrD;qBAAM;oBACH,kBAAkB,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;iBACrD;gBAED,mBAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC9D;SACJ;QAED,MAAM,qBAAqB,GAAG,cAAc,CAAC,wBAAwB,EAAE,CAAC;QACxE,IAAI,qBAAqB,EAAE;YACvB,mBAAmB,CAAC,qBAAqB,GAAG,EAAE,CAAC;YAC/C,KAAK,MAAM,oBAAoB,IAAI,qBAAqB,EAAE;gBACtD,MAAM,kBAAkB,GAAQ;oBAC5B,QAAQ,EAAE,oBAAoB,CAAC,QAAQ;oBACvC,OAAO,EAAE,oBAAoB,CAAC,OAAO;iBACxC,CAAC;gBAEF,IAAI,oBAAoB,CAAC,OAAO,KAAK,SAAS,EAAE;oBAC5C,kBAAkB,CAAC,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC;iBAC7D;qBAAM;oBACH,kBAAkB,CAAC,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC;iBAC7D;gBAED,mBAAmB,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACtE;SACJ;QAED,MAAM,iBAAiB,GAAG,cAAc,CAAC,oBAAoB,EAAE,CAAC;QAChE,IAAI,iBAAiB,EAAE;YACnB,mBAAmB,CAAC,iBAAiB,GAAG,EAAE,CAAC;YAC3C,KAAK,MAAM,gBAAgB,IAAI,iBAAiB,EAAE;gBAC9C,MAAM,kBAAkB,GAAQ;oBAC5B,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;oBACnC,OAAO,EAAE,gBAAgB,CAAC,OAAO;iBACpC,CAAC;gBAEF,IAAI,gBAAgB,CAAC,OAAO,KAAK,SAAS,EAAE;oBACxC,kBAAkB,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;iBACzD;qBAAM;oBACH,kBAAkB,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;iBACzD;gBAED,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAClE;SACJ;QAED,MAAM,aAAa,GAAG,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACxD,IAAI,aAAa,EAAE;YACf,mBAAmB,CAAC,aAAa,GAAG,EAAE,CAAC;YACvC,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;gBACtC,MAAM,kBAAkB,GAAQ;oBAC5B,QAAQ,EAAE,YAAY,CAAC,QAAQ;oBAC/B,OAAO,EAAE,YAAY,CAAC,OAAO;iBAChC,CAAC;gBAEF,IAAI,YAAY,CAAC,OAAO,KAAK,SAAS,EAAE;oBACpC,kBAAkB,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;iBACrD;qBAAM;oBACH,kBAAkB,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;iBACrD;gBAED,mBAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC9D;SACJ;QAED,MAAM,iBAAiB,GAAG,cAAc,CAAC,oBAAoB,EAAE,CAAC;QAChE,IAAI,iBAAiB,EAAE;YACnB,mBAAmB,CAAC,iBAAiB,GAAG,EAAE,CAAC;YAC3C,KAAK,MAAM,gBAAgB,IAAI,iBAAiB,EAAE;gBAC9C,MAAM,kBAAkB,GAAQ;oBAC5B,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;oBACnC,OAAO,EAAE,gBAAgB,CAAC,OAAO;iBACpC,CAAC;gBAEF,IAAI,gBAAgB,CAAC,OAAO,KAAK,SAAS,EAAE;oBACxC,kBAAkB,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;iBACzD;qBAAM;oBACH,kBAAkB,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;iBACzD;gBAED,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAClE;SACJ;QAED,MAAM,kBAAkB,GAAG,cAAc,CAAC,qBAAqB,EAAE,CAAC;QAClE,IAAI,kBAAkB,EAAE;YACpB,mBAAmB,CAAC,kBAAkB,GAAG,EAAE,CAAC;YAC5C,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE;gBAChD,MAAM,kBAAkB,GAAQ;oBAC5B,QAAQ,EAAE,iBAAiB,CAAC,QAAQ;oBACpC,OAAO,EAAE,iBAAiB,CAAC,OAAO;iBACrC,CAAC;gBAEF,IAAI,iBAAiB,CAAC,OAAO,KAAK,SAAS,EAAE;oBACzC,kBAAkB,CAAC,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC;iBAC1D;qBAAM;oBACH,kBAAkB,CAAC,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC;iBAC1D;gBAED,mBAAmB,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACnE;SACJ;QAED,MAAM,iBAAiB,GAAG,cAAc,CAAC,oBAAoB,EAAE,CAAC;QAChE,IAAI,iBAAiB,EAAE;YACnB,mBAAmB,CAAC,iBAAiB,GAAG,EAAE,CAAC;YAC3C,KAAK,MAAM,gBAAgB,IAAI,iBAAiB,EAAE;gBAC9C,MAAM,kBAAkB,GAAQ;oBAC5B,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;oBACnC,OAAO,EAAE,gBAAgB,CAAC,OAAO;iBACpC,CAAC;gBAEF,IAAI,gBAAgB,CAAC,OAAO,KAAK,SAAS,EAAE;oBACxC,kBAAkB,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;iBACzD;qBAAM;oBACH,kBAAkB,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;iBACzD;gBAED,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAClE;SACJ;QAED,MAAM,sBAAsB,GAAG,cAAc,CAAC,yBAAyB,EAAE,CAAC;QAC1E,IAAI,sBAAsB,EAAE;YACxB,mBAAmB,CAAC,sBAAsB,GAAG,EAAE,CAAC;YAChD,KAAK,MAAM,qBAAqB,IAAI,sBAAsB,EAAE;gBACxD,MAAM,kBAAkB,GAAQ;oBAC5B,QAAQ,EAAE,qBAAqB,CAAC,QAAQ;oBACxC,OAAO,EAAE,qBAAqB,CAAC,OAAO;iBACzC,CAAC;gBAEF,IAAI,qBAAqB,CAAC,OAAO,KAAK,SAAS,EAAE;oBAC7C,kBAAkB,CAAC,OAAO,GAAG,qBAAqB,CAAC,OAAO,CAAC;iBAC9D;qBAAM;oBACH,kBAAkB,CAAC,OAAO,GAAG,qBAAqB,CAAC,OAAO,CAAC;iBAC9D;gBAED,mBAAmB,CAAC,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACvE;YAED,mBAAmB,CAAC,oBAAoB,GAAG,cAAc,CAAC,oBAAoB,CAAC;SAClF;QAED,IAAI,cAAc,CAAC,YAAY,EAAE;YAC7B,mBAAmB,CAAC,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;SAC9E;IACL,CAAC;IAED,QAAQ;IACR;;;;;;OAMG;IACI,KAAK,CAAC,IAAY,EAAE,UAAe,EAAE,YAAY,GAAG,KAAK;QAC5D,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,IAAI,OAAO,GAAQ,IAAI,CAAC;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAiB,CAAC;QACtC,IAAI,MAAM,CAAC,wBAAwB,EAAE;YACjC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE;gBAC3B,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;gBAC5B,MAAM,OAAO,GAAW,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACjH,MAAM,MAAM,GAAG,MAAM,CAAC,wBAAwB,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,EAAE,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAC5J,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;oBACZ,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;iBACnC;qBAAM;oBACH,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;iBAC7B;aACJ;SACJ;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QACnD,MAAM,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/F,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,MAAM,CAAC,YAAY,GAAG,OAAO,CAAC;QAC9B,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC;QAEhC,IAAI,UAAU,KAAK,SAAS,EAAE;YAC1B,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC;SAC7B;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;SACnD;QAED,MAAM,CAAC,OAAO,GAAG,UAAU,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,MAAM,CAAC,KAAK,EAAE,CAAC;SAClB;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;;AA7gCD;;GAEG;AACoB,8BAAe,GAAG,SAAS,CAAC,yBAAyB,AAAtC,CAAuC;AAC7E;;GAEG;AACoB,gCAAiB,GAAG,SAAS,CAAC,2BAA2B,AAAxC,CAAyC;AACjF;;GAEG;AACoB,sCAAuB,GAAG,SAAS,CAAC,iCAAiC,AAA9C,CAA+C;AAC7F;;GAEG;AACoB,4CAA6B,GAAG,SAAS,CAAC,uCAAuC,AAApD,CAAqD;AAigC7G,UAAU,CAAC,oBAAoB,GAAG,cAAc,CAAC,KAAK,CAAC", "sourcesContent": ["import { ThinParticleSystem } from \"./thinParticleSystem\";\r\nimport type { IParticleEmitterType } from \"./EmitterTypes/IParticleEmitterType\";\r\nimport { SubEmitter, SubEmitterType } from \"./subEmitter\";\r\nimport { Color3, Color4 } from \"../Maths/math.color\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { IParticleSystem } from \"./IParticleSystem\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { ThinEngine } from \"../Engines/thinEngine\";\r\nimport { GetClass } from \"../Misc/typeStore\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport type { Particle } from \"./particle\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\nimport { MeshParticleEmitter } from \"./EmitterTypes/meshParticleEmitter\";\r\nimport { CustomParticleEmitter } from \"./EmitterTypes/customParticleEmitter\";\r\nimport { BoxParticleEmitter } from \"./EmitterTypes/boxParticleEmitter\";\r\nimport type { Engine } from \"../Engines/engine\";\r\nimport { PointParticleEmitter } from \"./EmitterTypes/pointParticleEmitter\";\r\nimport { HemisphericParticleEmitter } from \"./EmitterTypes/hemisphericParticleEmitter\";\r\nimport { SphereDirectedParticleEmitter, SphereParticleEmitter } from \"./EmitterTypes/sphereParticleEmitter\";\r\nimport { CylinderDirectedParticleEmitter, CylinderParticleEmitter } from \"./EmitterTypes/cylinderParticleEmitter\";\r\nimport { ConeParticleEmitter } from \"./EmitterTypes/coneParticleEmitter\";\r\nimport {\r\n    CreateConeEmitter,\r\n    CreateCylinderEmitter,\r\n    CreateDirectedCylinderEmitter,\r\n    CreateDirectedSphereEmitter,\r\n    CreateHemisphericEmitter,\r\n    CreatePointEmitter,\r\n    CreateSphereEmitter,\r\n} from \"./particleSystem.functions\";\r\n\r\n/**\r\n * This represents a particle system in Babylon.\r\n * Particles are often small sprites used to simulate hard-to-reproduce phenomena like fire, smoke, water, or abstract visual effects like magic glitter and faery dust.\r\n * Particles can take different shapes while emitted like box, sphere, cone or you can write your custom function.\r\n * @example https://doc.babylonjs.com/features/featuresDeepDive/particles/particle_system/particle_system_intro\r\n */\r\nexport class ParticleSystem extends ThinParticleSystem {\r\n    /**\r\n     * Billboard mode will only apply to Y axis\r\n     */\r\n    public static readonly BILLBOARDMODE_Y = Constants.PARTICLES_BILLBOARDMODE_Y;\r\n    /**\r\n     * Billboard mode will apply to all axes\r\n     */\r\n    public static readonly BILLBOARDMODE_ALL = Constants.PARTICLES_BILLBOARDMODE_ALL;\r\n    /**\r\n     * Special billboard mode where the particle will be biilboard to the camera but rotated to align with direction\r\n     */\r\n    public static readonly BILLBOARDMODE_STRETCHED = Constants.PARTICLES_BILLBOARDMODE_STRETCHED;\r\n    /**\r\n     * Special billboard mode where the particle will be billboard to the camera but only around the axis of the direction of particle emission\r\n     */\r\n    public static readonly BILLBOARDMODE_STRETCHED_LOCAL = Constants.PARTICLES_BILLBOARDMODE_STRETCHED_LOCAL;\r\n\r\n    // Sub-emitters\r\n    private _rootParticleSystem: Nullable<ParticleSystem>;\r\n\r\n    /**\r\n     * The Sub-emitters templates that will be used to generate the sub particle system to be associated with the system, this property is used by the root particle system only.\r\n     * When a particle is spawned, an array will be chosen at random and all the emitters in that array will be attached to the particle.  (Default: [])\r\n     */\r\n    public subEmitters: Array<ParticleSystem | SubEmitter | Array<SubEmitter>>;\r\n    // the subEmitters field above converted to a constant type\r\n    private _subEmitters: Array<Array<SubEmitter>>;\r\n    /**\r\n     * @internal\r\n     * If the particle systems emitter should be disposed when the particle system is disposed\r\n     */\r\n    public _disposeEmitterOnDispose = false;\r\n    /**\r\n     * The current active Sub-systems, this property is used by the root particle system only.\r\n     */\r\n    public activeSubSystems: Array<ParticleSystem>;\r\n    /**\r\n     * Creates a Point Emitter for the particle system (emits directly from the emitter position)\r\n     * @param direction1 Particles are emitted between the direction1 and direction2 from within the box\r\n     * @param direction2 Particles are emitted between the direction1 and direction2 from within the box\r\n     * @returns the emitter\r\n     */\r\n    public createPointEmitter(direction1: Vector3, direction2: Vector3): PointParticleEmitter {\r\n        const particleEmitter = CreatePointEmitter(direction1, direction2);\r\n        this.particleEmitterType = particleEmitter;\r\n        return particleEmitter;\r\n    }\r\n\r\n    /**\r\n     * Creates a Hemisphere Emitter for the particle system (emits along the hemisphere radius)\r\n     * @param radius The radius of the hemisphere to emit from\r\n     * @param radiusRange The range of the hemisphere to emit from [0-1] 0 Surface Only, 1 Entire Radius\r\n     * @returns the emitter\r\n     */\r\n    public createHemisphericEmitter(radius = 1, radiusRange = 1): HemisphericParticleEmitter {\r\n        const particleEmitter = CreateHemisphericEmitter(radius, radiusRange);\r\n        this.particleEmitterType = particleEmitter;\r\n        return particleEmitter;\r\n    }\r\n\r\n    /**\r\n     * Creates a Sphere Emitter for the particle system (emits along the sphere radius)\r\n     * @param radius The radius of the sphere to emit from\r\n     * @param radiusRange The range of the sphere to emit from [0-1] 0 Surface Only, 1 Entire Radius\r\n     * @returns the emitter\r\n     */\r\n    public createSphereEmitter(radius = 1, radiusRange = 1): SphereParticleEmitter {\r\n        const particleEmitter = CreateSphereEmitter(radius, radiusRange);\r\n        this.particleEmitterType = particleEmitter;\r\n        return particleEmitter;\r\n    }\r\n\r\n    /**\r\n     * Creates a Directed Sphere Emitter for the particle system (emits between direction1 and direction2)\r\n     * @param radius The radius of the sphere to emit from\r\n     * @param direction1 Particles are emitted between the direction1 and direction2 from within the sphere\r\n     * @param direction2 Particles are emitted between the direction1 and direction2 from within the sphere\r\n     * @returns the emitter\r\n     */\r\n    public createDirectedSphereEmitter(radius = 1, direction1 = new Vector3(0, 1.0, 0), direction2 = new Vector3(0, 1.0, 0)): SphereDirectedParticleEmitter {\r\n        const particleEmitter = CreateDirectedSphereEmitter(radius, direction1, direction2);\r\n        this.particleEmitterType = particleEmitter;\r\n        return particleEmitter;\r\n    }\r\n\r\n    /**\r\n     * Creates a Cylinder Emitter for the particle system (emits from the cylinder to the particle position)\r\n     * @param radius The radius of the emission cylinder\r\n     * @param height The height of the emission cylinder\r\n     * @param radiusRange The range of emission [0-1] 0 Surface only, 1 Entire Radius\r\n     * @param directionRandomizer How much to randomize the particle direction [0-1]\r\n     * @returns the emitter\r\n     */\r\n    public createCylinderEmitter(radius = 1, height = 1, radiusRange = 1, directionRandomizer = 0): CylinderParticleEmitter {\r\n        const particleEmitter = CreateCylinderEmitter(radius, height, radiusRange, directionRandomizer);\r\n        this.particleEmitterType = particleEmitter;\r\n        return particleEmitter;\r\n    }\r\n\r\n    /**\r\n     * Creates a Directed Cylinder Emitter for the particle system (emits between direction1 and direction2)\r\n     * @param radius The radius of the cylinder to emit from\r\n     * @param height The height of the emission cylinder\r\n     * @param radiusRange the range of the emission cylinder [0-1] 0 Surface only, 1 Entire Radius (1 by default)\r\n     * @param direction1 Particles are emitted between the direction1 and direction2 from within the cylinder\r\n     * @param direction2 Particles are emitted between the direction1 and direction2 from within the cylinder\r\n     * @returns the emitter\r\n     */\r\n    public createDirectedCylinderEmitter(\r\n        radius = 1,\r\n        height = 1,\r\n        radiusRange = 1,\r\n        direction1 = new Vector3(0, 1.0, 0),\r\n        direction2 = new Vector3(0, 1.0, 0)\r\n    ): CylinderDirectedParticleEmitter {\r\n        const particleEmitter = CreateDirectedCylinderEmitter(radius, height, radiusRange, direction1, direction2);\r\n        this.particleEmitterType = particleEmitter;\r\n        return particleEmitter;\r\n    }\r\n\r\n    /**\r\n     * Creates a Cone Emitter for the particle system (emits from the cone to the particle position)\r\n     * @param radius The radius of the cone to emit from\r\n     * @param angle The base angle of the cone\r\n     * @returns the emitter\r\n     */\r\n    public createConeEmitter(radius = 1, angle = Math.PI / 4): ConeParticleEmitter {\r\n        const particleEmitter = CreateConeEmitter(radius, angle);\r\n        this.particleEmitterType = particleEmitter;\r\n        return particleEmitter;\r\n    }\r\n    /**\r\n     * Creates a Box Emitter for the particle system. (emits between direction1 and direction2 from withing the box defined by minEmitBox and maxEmitBox)\r\n     * @param direction1 Particles are emitted between the direction1 and direction2 from within the box\r\n     * @param direction2 Particles are emitted between the direction1 and direction2 from within the box\r\n     * @param minEmitBox Particles are emitted from the box between minEmitBox and maxEmitBox\r\n     * @param maxEmitBox  Particles are emitted from the box between minEmitBox and maxEmitBox\r\n     * @returns the emitter\r\n     */\r\n    public createBoxEmitter(direction1: Vector3, direction2: Vector3, minEmitBox: Vector3, maxEmitBox: Vector3): BoxParticleEmitter {\r\n        const particleEmitter = new BoxParticleEmitter();\r\n        this.particleEmitterType = particleEmitter;\r\n        this.direction1 = direction1;\r\n        this.direction2 = direction2;\r\n        this.minEmitBox = minEmitBox;\r\n        this.maxEmitBox = maxEmitBox;\r\n        return particleEmitter;\r\n    }\r\n\r\n    private _prepareSubEmitterInternalArray() {\r\n        this._subEmitters = new Array<Array<SubEmitter>>();\r\n        if (this.subEmitters) {\r\n            this.subEmitters.forEach((subEmitter) => {\r\n                if (subEmitter instanceof ParticleSystem) {\r\n                    this._subEmitters.push([new SubEmitter(subEmitter)]);\r\n                } else if (subEmitter instanceof SubEmitter) {\r\n                    this._subEmitters.push([subEmitter]);\r\n                } else if (subEmitter instanceof Array) {\r\n                    this._subEmitters.push(subEmitter);\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    private _stopSubEmitters(): void {\r\n        if (!this.activeSubSystems) {\r\n            return;\r\n        }\r\n        this.activeSubSystems.forEach((subSystem) => {\r\n            subSystem.stop(true);\r\n        });\r\n        this.activeSubSystems = [] as ParticleSystem[];\r\n    }\r\n\r\n    private _removeFromRoot(): void {\r\n        if (!this._rootParticleSystem) {\r\n            return;\r\n        }\r\n\r\n        const index = this._rootParticleSystem.activeSubSystems.indexOf(this);\r\n        if (index !== -1) {\r\n            this._rootParticleSystem.activeSubSystems.splice(index, 1);\r\n        }\r\n\r\n        this._rootParticleSystem = null;\r\n    }\r\n\r\n    public _emitFromParticle: (particle: Particle) => void = (particle) => {\r\n        if (!this._subEmitters || this._subEmitters.length === 0) {\r\n            return;\r\n        }\r\n        const templateIndex = Math.floor(Math.random() * this._subEmitters.length);\r\n\r\n        this._subEmitters[templateIndex].forEach((subEmitter) => {\r\n            if (subEmitter.type === SubEmitterType.END) {\r\n                const subSystem = subEmitter.clone();\r\n                particle._inheritParticleInfoToSubEmitter(subSystem);\r\n                subSystem.particleSystem._rootParticleSystem = this;\r\n                this.activeSubSystems.push(subSystem.particleSystem);\r\n                subSystem.particleSystem.start();\r\n            }\r\n        });\r\n    };\r\n\r\n    public _preStart() {\r\n        // Convert the subEmitters field to the constant type field _subEmitters\r\n        this._prepareSubEmitterInternalArray();\r\n\r\n        if (this._subEmitters && this._subEmitters.length != 0) {\r\n            this.activeSubSystems = [] as ParticleSystem[];\r\n        }\r\n    }\r\n\r\n    public _postStop(stopSubEmitters: boolean) {\r\n        if (stopSubEmitters) {\r\n            this._stopSubEmitters();\r\n        }\r\n    }\r\n\r\n    public _prepareParticle(particle: Particle): void {\r\n        // Attach emitters\r\n        if (this._subEmitters && this._subEmitters.length > 0) {\r\n            const subEmitters = this._subEmitters[Math.floor(Math.random() * this._subEmitters.length)];\r\n            particle._attachedSubEmitters = [];\r\n            subEmitters.forEach((subEmitter) => {\r\n                if (subEmitter.type === SubEmitterType.ATTACHED) {\r\n                    const newEmitter = subEmitter.clone();\r\n                    (<Array<SubEmitter>>particle._attachedSubEmitters).push(newEmitter);\r\n                    newEmitter.particleSystem.start();\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _onDispose(disposeAttachedSubEmitters = false, disposeEndSubEmitters = false) {\r\n        this._removeFromRoot();\r\n\r\n        if (this.subEmitters && !this._subEmitters) {\r\n            this._prepareSubEmitterInternalArray();\r\n        }\r\n\r\n        if (disposeAttachedSubEmitters) {\r\n            this.particles?.forEach((particle) => {\r\n                if (particle._attachedSubEmitters) {\r\n                    for (let i = particle._attachedSubEmitters.length - 1; i >= 0; i -= 1) {\r\n                        particle._attachedSubEmitters[i].dispose();\r\n                    }\r\n                }\r\n            });\r\n        }\r\n\r\n        if (disposeEndSubEmitters) {\r\n            if (this.activeSubSystems) {\r\n                for (let i = this.activeSubSystems.length - 1; i >= 0; i -= 1) {\r\n                    this.activeSubSystems[i].dispose();\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._subEmitters && this._subEmitters.length) {\r\n            for (let index = 0; index < this._subEmitters.length; index++) {\r\n                for (const subEmitter of this._subEmitters[index]) {\r\n                    subEmitter.dispose();\r\n                }\r\n            }\r\n\r\n            this._subEmitters = [];\r\n            this.subEmitters = [];\r\n        }\r\n\r\n        if (this._disposeEmitterOnDispose && this.emitter && (this.emitter as AbstractMesh).dispose) {\r\n            (<AbstractMesh>this.emitter).dispose(true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _Parse(parsedParticleSystem: any, particleSystem: IParticleSystem, sceneOrEngine: Scene | ThinEngine, rootUrl: string) {\r\n        let scene: Nullable<Scene>;\r\n\r\n        if (sceneOrEngine instanceof ThinEngine) {\r\n            scene = null;\r\n        } else {\r\n            scene = sceneOrEngine as Scene;\r\n        }\r\n\r\n        const internalClass = GetClass(\"BABYLON.Texture\");\r\n        if (internalClass && scene) {\r\n            // Texture\r\n            if (parsedParticleSystem.texture) {\r\n                particleSystem.particleTexture = internalClass.Parse(parsedParticleSystem.texture, scene, rootUrl) as BaseTexture;\r\n            } else if (parsedParticleSystem.textureName) {\r\n                particleSystem.particleTexture = new internalClass(\r\n                    rootUrl + parsedParticleSystem.textureName,\r\n                    scene,\r\n                    false,\r\n                    parsedParticleSystem.invertY !== undefined ? parsedParticleSystem.invertY : true\r\n                );\r\n                particleSystem.particleTexture!.name = parsedParticleSystem.textureName;\r\n            }\r\n        }\r\n\r\n        // Emitter\r\n        if (!parsedParticleSystem.emitterId && parsedParticleSystem.emitterId !== 0 && parsedParticleSystem.emitter === undefined) {\r\n            particleSystem.emitter = Vector3.Zero();\r\n        } else if (parsedParticleSystem.emitterId && scene) {\r\n            particleSystem.emitter = scene.getLastMeshById(parsedParticleSystem.emitterId);\r\n        } else {\r\n            particleSystem.emitter = Vector3.FromArray(parsedParticleSystem.emitter);\r\n        }\r\n\r\n        particleSystem.isLocal = !!parsedParticleSystem.isLocal;\r\n\r\n        // Misc.\r\n        if (parsedParticleSystem.renderingGroupId !== undefined) {\r\n            particleSystem.renderingGroupId = parsedParticleSystem.renderingGroupId;\r\n        }\r\n\r\n        if (parsedParticleSystem.isBillboardBased !== undefined) {\r\n            particleSystem.isBillboardBased = parsedParticleSystem.isBillboardBased;\r\n        }\r\n\r\n        if (parsedParticleSystem.billboardMode !== undefined) {\r\n            particleSystem.billboardMode = parsedParticleSystem.billboardMode;\r\n        }\r\n\r\n        if (parsedParticleSystem.useLogarithmicDepth !== undefined) {\r\n            particleSystem.useLogarithmicDepth = parsedParticleSystem.useLogarithmicDepth;\r\n        }\r\n\r\n        // Animations\r\n        if (parsedParticleSystem.animations) {\r\n            for (let animationIndex = 0; animationIndex < parsedParticleSystem.animations.length; animationIndex++) {\r\n                const parsedAnimation = parsedParticleSystem.animations[animationIndex];\r\n                const internalClass = GetClass(\"BABYLON.Animation\");\r\n                if (internalClass) {\r\n                    particleSystem.animations.push(internalClass.Parse(parsedAnimation));\r\n                }\r\n            }\r\n            particleSystem.beginAnimationOnStart = parsedParticleSystem.beginAnimationOnStart;\r\n            particleSystem.beginAnimationFrom = parsedParticleSystem.beginAnimationFrom;\r\n            particleSystem.beginAnimationTo = parsedParticleSystem.beginAnimationTo;\r\n            particleSystem.beginAnimationLoop = parsedParticleSystem.beginAnimationLoop;\r\n        }\r\n\r\n        if (parsedParticleSystem.autoAnimate && scene) {\r\n            scene.beginAnimation(\r\n                particleSystem,\r\n                parsedParticleSystem.autoAnimateFrom,\r\n                parsedParticleSystem.autoAnimateTo,\r\n                parsedParticleSystem.autoAnimateLoop,\r\n                parsedParticleSystem.autoAnimateSpeed || 1.0\r\n            );\r\n        }\r\n\r\n        // Particle system\r\n        particleSystem.startDelay = parsedParticleSystem.startDelay | 0;\r\n        particleSystem.minAngularSpeed = parsedParticleSystem.minAngularSpeed;\r\n        particleSystem.maxAngularSpeed = parsedParticleSystem.maxAngularSpeed;\r\n        particleSystem.minSize = parsedParticleSystem.minSize;\r\n        particleSystem.maxSize = parsedParticleSystem.maxSize;\r\n\r\n        if (parsedParticleSystem.minScaleX) {\r\n            particleSystem.minScaleX = parsedParticleSystem.minScaleX;\r\n            particleSystem.maxScaleX = parsedParticleSystem.maxScaleX;\r\n            particleSystem.minScaleY = parsedParticleSystem.minScaleY;\r\n            particleSystem.maxScaleY = parsedParticleSystem.maxScaleY;\r\n        }\r\n\r\n        if (parsedParticleSystem.preWarmCycles !== undefined) {\r\n            particleSystem.preWarmCycles = parsedParticleSystem.preWarmCycles;\r\n            particleSystem.preWarmStepOffset = parsedParticleSystem.preWarmStepOffset;\r\n        }\r\n\r\n        if (parsedParticleSystem.minInitialRotation !== undefined) {\r\n            particleSystem.minInitialRotation = parsedParticleSystem.minInitialRotation;\r\n            particleSystem.maxInitialRotation = parsedParticleSystem.maxInitialRotation;\r\n        }\r\n\r\n        particleSystem.minLifeTime = parsedParticleSystem.minLifeTime;\r\n        particleSystem.maxLifeTime = parsedParticleSystem.maxLifeTime;\r\n        particleSystem.minEmitPower = parsedParticleSystem.minEmitPower;\r\n        particleSystem.maxEmitPower = parsedParticleSystem.maxEmitPower;\r\n        particleSystem.emitRate = parsedParticleSystem.emitRate;\r\n        particleSystem.gravity = Vector3.FromArray(parsedParticleSystem.gravity);\r\n        if (parsedParticleSystem.noiseStrength) {\r\n            particleSystem.noiseStrength = Vector3.FromArray(parsedParticleSystem.noiseStrength);\r\n        }\r\n        particleSystem.color1 = Color4.FromArray(parsedParticleSystem.color1);\r\n        particleSystem.color2 = Color4.FromArray(parsedParticleSystem.color2);\r\n        particleSystem.colorDead = Color4.FromArray(parsedParticleSystem.colorDead);\r\n        particleSystem.updateSpeed = parsedParticleSystem.updateSpeed;\r\n        particleSystem.targetStopDuration = parsedParticleSystem.targetStopDuration;\r\n        particleSystem.blendMode = parsedParticleSystem.blendMode;\r\n\r\n        if (parsedParticleSystem.colorGradients) {\r\n            for (const colorGradient of parsedParticleSystem.colorGradients) {\r\n                particleSystem.addColorGradient(\r\n                    colorGradient.gradient,\r\n                    Color4.FromArray(colorGradient.color1),\r\n                    colorGradient.color2 ? Color4.FromArray(colorGradient.color2) : undefined\r\n                );\r\n            }\r\n        }\r\n\r\n        if (parsedParticleSystem.rampGradients) {\r\n            for (const rampGradient of parsedParticleSystem.rampGradients) {\r\n                particleSystem.addRampGradient(rampGradient.gradient, Color3.FromArray(rampGradient.color));\r\n            }\r\n            particleSystem.useRampGradients = parsedParticleSystem.useRampGradients;\r\n        }\r\n\r\n        if (parsedParticleSystem.colorRemapGradients) {\r\n            for (const colorRemapGradient of parsedParticleSystem.colorRemapGradients) {\r\n                particleSystem.addColorRemapGradient(\r\n                    colorRemapGradient.gradient,\r\n                    colorRemapGradient.factor1 !== undefined ? colorRemapGradient.factor1 : colorRemapGradient.factor,\r\n                    colorRemapGradient.factor2\r\n                );\r\n            }\r\n        }\r\n\r\n        if (parsedParticleSystem.alphaRemapGradients) {\r\n            for (const alphaRemapGradient of parsedParticleSystem.alphaRemapGradients) {\r\n                particleSystem.addAlphaRemapGradient(\r\n                    alphaRemapGradient.gradient,\r\n                    alphaRemapGradient.factor1 !== undefined ? alphaRemapGradient.factor1 : alphaRemapGradient.factor,\r\n                    alphaRemapGradient.factor2\r\n                );\r\n            }\r\n        }\r\n\r\n        if (parsedParticleSystem.sizeGradients) {\r\n            for (const sizeGradient of parsedParticleSystem.sizeGradients) {\r\n                particleSystem.addSizeGradient(sizeGradient.gradient, sizeGradient.factor1 !== undefined ? sizeGradient.factor1 : sizeGradient.factor, sizeGradient.factor2);\r\n            }\r\n        }\r\n\r\n        if (parsedParticleSystem.angularSpeedGradients) {\r\n            for (const angularSpeedGradient of parsedParticleSystem.angularSpeedGradients) {\r\n                particleSystem.addAngularSpeedGradient(\r\n                    angularSpeedGradient.gradient,\r\n                    angularSpeedGradient.factor1 !== undefined ? angularSpeedGradient.factor1 : angularSpeedGradient.factor,\r\n                    angularSpeedGradient.factor2\r\n                );\r\n            }\r\n        }\r\n\r\n        if (parsedParticleSystem.velocityGradients) {\r\n            for (const velocityGradient of parsedParticleSystem.velocityGradients) {\r\n                particleSystem.addVelocityGradient(\r\n                    velocityGradient.gradient,\r\n                    velocityGradient.factor1 !== undefined ? velocityGradient.factor1 : velocityGradient.factor,\r\n                    velocityGradient.factor2\r\n                );\r\n            }\r\n        }\r\n\r\n        if (parsedParticleSystem.dragGradients) {\r\n            for (const dragGradient of parsedParticleSystem.dragGradients) {\r\n                particleSystem.addDragGradient(dragGradient.gradient, dragGradient.factor1 !== undefined ? dragGradient.factor1 : dragGradient.factor, dragGradient.factor2);\r\n            }\r\n        }\r\n\r\n        if (parsedParticleSystem.emitRateGradients) {\r\n            for (const emitRateGradient of parsedParticleSystem.emitRateGradients) {\r\n                particleSystem.addEmitRateGradient(\r\n                    emitRateGradient.gradient,\r\n                    emitRateGradient.factor1 !== undefined ? emitRateGradient.factor1 : emitRateGradient.factor,\r\n                    emitRateGradient.factor2\r\n                );\r\n            }\r\n        }\r\n\r\n        if (parsedParticleSystem.startSizeGradients) {\r\n            for (const startSizeGradient of parsedParticleSystem.startSizeGradients) {\r\n                particleSystem.addStartSizeGradient(\r\n                    startSizeGradient.gradient,\r\n                    startSizeGradient.factor1 !== undefined ? startSizeGradient.factor1 : startSizeGradient.factor,\r\n                    startSizeGradient.factor2\r\n                );\r\n            }\r\n        }\r\n\r\n        if (parsedParticleSystem.lifeTimeGradients) {\r\n            for (const lifeTimeGradient of parsedParticleSystem.lifeTimeGradients) {\r\n                particleSystem.addLifeTimeGradient(\r\n                    lifeTimeGradient.gradient,\r\n                    lifeTimeGradient.factor1 !== undefined ? lifeTimeGradient.factor1 : lifeTimeGradient.factor,\r\n                    lifeTimeGradient.factor2\r\n                );\r\n            }\r\n        }\r\n\r\n        if (parsedParticleSystem.limitVelocityGradients) {\r\n            for (const limitVelocityGradient of parsedParticleSystem.limitVelocityGradients) {\r\n                particleSystem.addLimitVelocityGradient(\r\n                    limitVelocityGradient.gradient,\r\n                    limitVelocityGradient.factor1 !== undefined ? limitVelocityGradient.factor1 : limitVelocityGradient.factor,\r\n                    limitVelocityGradient.factor2\r\n                );\r\n            }\r\n            particleSystem.limitVelocityDamping = parsedParticleSystem.limitVelocityDamping;\r\n        }\r\n\r\n        if (parsedParticleSystem.noiseTexture && scene) {\r\n            const internalClass = GetClass(\"BABYLON.ProceduralTexture\");\r\n            particleSystem.noiseTexture = internalClass.Parse(parsedParticleSystem.noiseTexture, scene, rootUrl);\r\n        }\r\n\r\n        // Emitter\r\n        let emitterType: IParticleEmitterType;\r\n        if (parsedParticleSystem.particleEmitterType) {\r\n            switch (parsedParticleSystem.particleEmitterType.type) {\r\n                case \"SphereParticleEmitter\":\r\n                    emitterType = new SphereParticleEmitter();\r\n                    break;\r\n                case \"SphereDirectedParticleEmitter\":\r\n                    emitterType = new SphereDirectedParticleEmitter();\r\n                    break;\r\n                case \"ConeEmitter\":\r\n                case \"ConeParticleEmitter\":\r\n                    emitterType = new ConeParticleEmitter();\r\n                    break;\r\n                case \"CylinderParticleEmitter\":\r\n                    emitterType = new CylinderParticleEmitter();\r\n                    break;\r\n                case \"CylinderDirectedParticleEmitter\":\r\n                    emitterType = new CylinderDirectedParticleEmitter();\r\n                    break;\r\n                case \"HemisphericParticleEmitter\":\r\n                    emitterType = new HemisphericParticleEmitter();\r\n                    break;\r\n                case \"PointParticleEmitter\":\r\n                    emitterType = new PointParticleEmitter();\r\n                    break;\r\n                case \"MeshParticleEmitter\":\r\n                    emitterType = new MeshParticleEmitter();\r\n                    break;\r\n                case \"CustomParticleEmitter\":\r\n                    emitterType = new CustomParticleEmitter();\r\n                    break;\r\n                case \"BoxEmitter\":\r\n                case \"BoxParticleEmitter\":\r\n                default:\r\n                    emitterType = new BoxParticleEmitter();\r\n                    break;\r\n            }\r\n\r\n            emitterType.parse(parsedParticleSystem.particleEmitterType, scene);\r\n        } else {\r\n            emitterType = new BoxParticleEmitter();\r\n            emitterType.parse(parsedParticleSystem, scene);\r\n        }\r\n        particleSystem.particleEmitterType = emitterType;\r\n\r\n        // Animation sheet\r\n        particleSystem.startSpriteCellID = parsedParticleSystem.startSpriteCellID;\r\n        particleSystem.endSpriteCellID = parsedParticleSystem.endSpriteCellID;\r\n        particleSystem.spriteCellLoop = parsedParticleSystem.spriteCellLoop ?? true;\r\n        particleSystem.spriteCellWidth = parsedParticleSystem.spriteCellWidth;\r\n        particleSystem.spriteCellHeight = parsedParticleSystem.spriteCellHeight;\r\n        particleSystem.spriteCellChangeSpeed = parsedParticleSystem.spriteCellChangeSpeed;\r\n        particleSystem.spriteRandomStartCell = parsedParticleSystem.spriteRandomStartCell;\r\n\r\n        particleSystem.disposeOnStop = parsedParticleSystem.disposeOnStop ?? false;\r\n        particleSystem.manualEmitCount = parsedParticleSystem.manualEmitCount ?? -1;\r\n    }\r\n\r\n    /**\r\n     * Parses a JSON object to create a particle system.\r\n     * @param parsedParticleSystem The JSON object to parse\r\n     * @param sceneOrEngine The scene or the engine to create the particle system in\r\n     * @param rootUrl The root url to use to load external dependencies like texture\r\n     * @param doNotStart Ignore the preventAutoStart attribute and does not start\r\n     * @param capacity defines the system capacity (if null or undefined the sotred capacity will be used)\r\n     * @returns the Parsed particle system\r\n     */\r\n    public static Parse(parsedParticleSystem: any, sceneOrEngine: Scene | ThinEngine, rootUrl: string, doNotStart = false, capacity?: number): ParticleSystem {\r\n        const name = parsedParticleSystem.name;\r\n        let custom: Nullable<Effect> = null;\r\n        let program: any = null;\r\n        let engine: ThinEngine;\r\n        let scene: Nullable<Scene>;\r\n\r\n        if (sceneOrEngine instanceof ThinEngine) {\r\n            engine = sceneOrEngine;\r\n        } else {\r\n            scene = sceneOrEngine as Scene;\r\n            engine = scene.getEngine();\r\n        }\r\n\r\n        if (parsedParticleSystem.customShader && (engine as any).createEffectForParticles) {\r\n            program = parsedParticleSystem.customShader;\r\n            const defines: string = program.shaderOptions.defines.length > 0 ? program.shaderOptions.defines.join(\"\\n\") : \"\";\r\n            custom = (engine as any).createEffectForParticles(program.shaderPath.fragmentElement, program.shaderOptions.uniforms, program.shaderOptions.samplers, defines);\r\n        }\r\n        const particleSystem = new ParticleSystem(name, capacity || parsedParticleSystem.capacity, sceneOrEngine, custom, parsedParticleSystem.isAnimationSheetEnabled);\r\n        particleSystem.customShader = program;\r\n        particleSystem._rootUrl = rootUrl;\r\n\r\n        if (parsedParticleSystem.id) {\r\n            particleSystem.id = parsedParticleSystem.id;\r\n        }\r\n\r\n        // SubEmitters\r\n        if (parsedParticleSystem.subEmitters) {\r\n            particleSystem.subEmitters = [];\r\n            for (const cell of parsedParticleSystem.subEmitters) {\r\n                const cellArray = [];\r\n                for (const sub of cell) {\r\n                    cellArray.push(SubEmitter.Parse(sub, sceneOrEngine, rootUrl));\r\n                }\r\n\r\n                particleSystem.subEmitters.push(cellArray);\r\n            }\r\n        }\r\n\r\n        ParticleSystem._Parse(parsedParticleSystem, particleSystem, sceneOrEngine, rootUrl);\r\n\r\n        if (parsedParticleSystem.textureMask) {\r\n            particleSystem.textureMask = Color4.FromArray(parsedParticleSystem.textureMask);\r\n        }\r\n\r\n        if (parsedParticleSystem.worldOffset) {\r\n            particleSystem.worldOffset = Vector3.FromArray(parsedParticleSystem.worldOffset);\r\n        }\r\n\r\n        // Auto start\r\n        if (parsedParticleSystem.preventAutoStart) {\r\n            particleSystem.preventAutoStart = parsedParticleSystem.preventAutoStart;\r\n        }\r\n\r\n        if (!doNotStart && !particleSystem.preventAutoStart) {\r\n            particleSystem.start();\r\n        }\r\n\r\n        return particleSystem;\r\n    }\r\n\r\n    /**\r\n     * Serializes the particle system to a JSON object\r\n     * @param serializeTexture defines if the texture must be serialized as well\r\n     * @returns the JSON object\r\n     */\r\n    public serialize(serializeTexture = false): any {\r\n        const serializationObject: any = {};\r\n\r\n        ParticleSystem._Serialize(serializationObject, this, serializeTexture);\r\n\r\n        serializationObject.textureMask = this.textureMask.asArray();\r\n        serializationObject.customShader = this.customShader;\r\n        serializationObject.preventAutoStart = this.preventAutoStart;\r\n        serializationObject.worldOffset = this.worldOffset.asArray();\r\n\r\n        // SubEmitters\r\n        if (this.subEmitters) {\r\n            serializationObject.subEmitters = [];\r\n\r\n            if (!this._subEmitters) {\r\n                this._prepareSubEmitterInternalArray();\r\n            }\r\n\r\n            for (const subs of this._subEmitters) {\r\n                const cell = [];\r\n                for (const sub of subs) {\r\n                    cell.push(sub.serialize(serializeTexture));\r\n                }\r\n\r\n                serializationObject.subEmitters.push(cell);\r\n            }\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _Serialize(serializationObject: any, particleSystem: IParticleSystem, serializeTexture: boolean) {\r\n        serializationObject.name = particleSystem.name;\r\n        serializationObject.id = particleSystem.id;\r\n\r\n        serializationObject.capacity = particleSystem.getCapacity();\r\n\r\n        serializationObject.disposeOnStop = particleSystem.disposeOnStop;\r\n        serializationObject.manualEmitCount = particleSystem.manualEmitCount;\r\n\r\n        // Emitter\r\n        if ((<AbstractMesh>particleSystem.emitter).position) {\r\n            const emitterMesh = <AbstractMesh>particleSystem.emitter;\r\n            serializationObject.emitterId = emitterMesh.id;\r\n        } else {\r\n            const emitterPosition = <Vector3>particleSystem.emitter;\r\n            serializationObject.emitter = emitterPosition.asArray();\r\n        }\r\n\r\n        // Emitter\r\n        if (particleSystem.particleEmitterType) {\r\n            serializationObject.particleEmitterType = particleSystem.particleEmitterType.serialize();\r\n        }\r\n\r\n        if (particleSystem.particleTexture) {\r\n            if (serializeTexture) {\r\n                serializationObject.texture = particleSystem.particleTexture.serialize();\r\n            } else {\r\n                serializationObject.textureName = particleSystem.particleTexture.name;\r\n                serializationObject.invertY = !!(particleSystem.particleTexture as any)._invertY;\r\n            }\r\n        }\r\n\r\n        serializationObject.isLocal = particleSystem.isLocal;\r\n\r\n        // Animations\r\n        SerializationHelper.AppendSerializedAnimations(particleSystem, serializationObject);\r\n        serializationObject.beginAnimationOnStart = particleSystem.beginAnimationOnStart;\r\n        serializationObject.beginAnimationFrom = particleSystem.beginAnimationFrom;\r\n        serializationObject.beginAnimationTo = particleSystem.beginAnimationTo;\r\n        serializationObject.beginAnimationLoop = particleSystem.beginAnimationLoop;\r\n\r\n        // Particle system\r\n        serializationObject.startDelay = particleSystem.startDelay;\r\n        serializationObject.renderingGroupId = particleSystem.renderingGroupId;\r\n        serializationObject.isBillboardBased = particleSystem.isBillboardBased;\r\n        serializationObject.billboardMode = particleSystem.billboardMode;\r\n        serializationObject.minAngularSpeed = particleSystem.minAngularSpeed;\r\n        serializationObject.maxAngularSpeed = particleSystem.maxAngularSpeed;\r\n        serializationObject.minSize = particleSystem.minSize;\r\n        serializationObject.maxSize = particleSystem.maxSize;\r\n        serializationObject.minScaleX = particleSystem.minScaleX;\r\n        serializationObject.maxScaleX = particleSystem.maxScaleX;\r\n        serializationObject.minScaleY = particleSystem.minScaleY;\r\n        serializationObject.maxScaleY = particleSystem.maxScaleY;\r\n        serializationObject.minEmitPower = particleSystem.minEmitPower;\r\n        serializationObject.maxEmitPower = particleSystem.maxEmitPower;\r\n        serializationObject.minLifeTime = particleSystem.minLifeTime;\r\n        serializationObject.maxLifeTime = particleSystem.maxLifeTime;\r\n        serializationObject.emitRate = particleSystem.emitRate;\r\n        serializationObject.gravity = particleSystem.gravity.asArray();\r\n        serializationObject.noiseStrength = particleSystem.noiseStrength.asArray();\r\n        serializationObject.color1 = particleSystem.color1.asArray();\r\n        serializationObject.color2 = particleSystem.color2.asArray();\r\n        serializationObject.colorDead = particleSystem.colorDead.asArray();\r\n        serializationObject.updateSpeed = particleSystem.updateSpeed;\r\n        serializationObject.targetStopDuration = particleSystem.targetStopDuration;\r\n        serializationObject.blendMode = particleSystem.blendMode;\r\n        serializationObject.preWarmCycles = particleSystem.preWarmCycles;\r\n        serializationObject.preWarmStepOffset = particleSystem.preWarmStepOffset;\r\n        serializationObject.minInitialRotation = particleSystem.minInitialRotation;\r\n        serializationObject.maxInitialRotation = particleSystem.maxInitialRotation;\r\n        serializationObject.startSpriteCellID = particleSystem.startSpriteCellID;\r\n        serializationObject.spriteCellLoop = particleSystem.spriteCellLoop;\r\n        serializationObject.endSpriteCellID = particleSystem.endSpriteCellID;\r\n        serializationObject.spriteCellChangeSpeed = particleSystem.spriteCellChangeSpeed;\r\n        serializationObject.spriteCellWidth = particleSystem.spriteCellWidth;\r\n        serializationObject.spriteCellHeight = particleSystem.spriteCellHeight;\r\n        serializationObject.spriteRandomStartCell = particleSystem.spriteRandomStartCell;\r\n        serializationObject.isAnimationSheetEnabled = particleSystem.isAnimationSheetEnabled;\r\n        serializationObject.useLogarithmicDepth = particleSystem.useLogarithmicDepth;\r\n\r\n        const colorGradients = particleSystem.getColorGradients();\r\n        if (colorGradients) {\r\n            serializationObject.colorGradients = [];\r\n            for (const colorGradient of colorGradients) {\r\n                const serializedGradient: any = {\r\n                    gradient: colorGradient.gradient,\r\n                    color1: colorGradient.color1.asArray(),\r\n                };\r\n\r\n                if (colorGradient.color2) {\r\n                    serializedGradient.color2 = colorGradient.color2.asArray();\r\n                } else {\r\n                    serializedGradient.color2 = colorGradient.color1.asArray();\r\n                }\r\n\r\n                serializationObject.colorGradients.push(serializedGradient);\r\n            }\r\n        }\r\n\r\n        const rampGradients = particleSystem.getRampGradients();\r\n        if (rampGradients) {\r\n            serializationObject.rampGradients = [];\r\n            for (const rampGradient of rampGradients) {\r\n                const serializedGradient: any = {\r\n                    gradient: rampGradient.gradient,\r\n                    color: rampGradient.color.asArray(),\r\n                };\r\n\r\n                serializationObject.rampGradients.push(serializedGradient);\r\n            }\r\n            serializationObject.useRampGradients = particleSystem.useRampGradients;\r\n        }\r\n\r\n        const colorRemapGradients = particleSystem.getColorRemapGradients();\r\n        if (colorRemapGradients) {\r\n            serializationObject.colorRemapGradients = [];\r\n            for (const colorRemapGradient of colorRemapGradients) {\r\n                const serializedGradient: any = {\r\n                    gradient: colorRemapGradient.gradient,\r\n                    factor1: colorRemapGradient.factor1,\r\n                };\r\n\r\n                if (colorRemapGradient.factor2 !== undefined) {\r\n                    serializedGradient.factor2 = colorRemapGradient.factor2;\r\n                } else {\r\n                    serializedGradient.factor2 = colorRemapGradient.factor1;\r\n                }\r\n\r\n                serializationObject.colorRemapGradients.push(serializedGradient);\r\n            }\r\n        }\r\n\r\n        const alphaRemapGradients = particleSystem.getAlphaRemapGradients();\r\n        if (alphaRemapGradients) {\r\n            serializationObject.alphaRemapGradients = [];\r\n            for (const alphaRemapGradient of alphaRemapGradients) {\r\n                const serializedGradient: any = {\r\n                    gradient: alphaRemapGradient.gradient,\r\n                    factor1: alphaRemapGradient.factor1,\r\n                };\r\n\r\n                if (alphaRemapGradient.factor2 !== undefined) {\r\n                    serializedGradient.factor2 = alphaRemapGradient.factor2;\r\n                } else {\r\n                    serializedGradient.factor2 = alphaRemapGradient.factor1;\r\n                }\r\n\r\n                serializationObject.alphaRemapGradients.push(serializedGradient);\r\n            }\r\n        }\r\n\r\n        const sizeGradients = particleSystem.getSizeGradients();\r\n        if (sizeGradients) {\r\n            serializationObject.sizeGradients = [];\r\n            for (const sizeGradient of sizeGradients) {\r\n                const serializedGradient: any = {\r\n                    gradient: sizeGradient.gradient,\r\n                    factor1: sizeGradient.factor1,\r\n                };\r\n\r\n                if (sizeGradient.factor2 !== undefined) {\r\n                    serializedGradient.factor2 = sizeGradient.factor2;\r\n                } else {\r\n                    serializedGradient.factor2 = sizeGradient.factor1;\r\n                }\r\n\r\n                serializationObject.sizeGradients.push(serializedGradient);\r\n            }\r\n        }\r\n\r\n        const angularSpeedGradients = particleSystem.getAngularSpeedGradients();\r\n        if (angularSpeedGradients) {\r\n            serializationObject.angularSpeedGradients = [];\r\n            for (const angularSpeedGradient of angularSpeedGradients) {\r\n                const serializedGradient: any = {\r\n                    gradient: angularSpeedGradient.gradient,\r\n                    factor1: angularSpeedGradient.factor1,\r\n                };\r\n\r\n                if (angularSpeedGradient.factor2 !== undefined) {\r\n                    serializedGradient.factor2 = angularSpeedGradient.factor2;\r\n                } else {\r\n                    serializedGradient.factor2 = angularSpeedGradient.factor1;\r\n                }\r\n\r\n                serializationObject.angularSpeedGradients.push(serializedGradient);\r\n            }\r\n        }\r\n\r\n        const velocityGradients = particleSystem.getVelocityGradients();\r\n        if (velocityGradients) {\r\n            serializationObject.velocityGradients = [];\r\n            for (const velocityGradient of velocityGradients) {\r\n                const serializedGradient: any = {\r\n                    gradient: velocityGradient.gradient,\r\n                    factor1: velocityGradient.factor1,\r\n                };\r\n\r\n                if (velocityGradient.factor2 !== undefined) {\r\n                    serializedGradient.factor2 = velocityGradient.factor2;\r\n                } else {\r\n                    serializedGradient.factor2 = velocityGradient.factor1;\r\n                }\r\n\r\n                serializationObject.velocityGradients.push(serializedGradient);\r\n            }\r\n        }\r\n\r\n        const dragGradients = particleSystem.getDragGradients();\r\n        if (dragGradients) {\r\n            serializationObject.dragGradients = [];\r\n            for (const dragGradient of dragGradients) {\r\n                const serializedGradient: any = {\r\n                    gradient: dragGradient.gradient,\r\n                    factor1: dragGradient.factor1,\r\n                };\r\n\r\n                if (dragGradient.factor2 !== undefined) {\r\n                    serializedGradient.factor2 = dragGradient.factor2;\r\n                } else {\r\n                    serializedGradient.factor2 = dragGradient.factor1;\r\n                }\r\n\r\n                serializationObject.dragGradients.push(serializedGradient);\r\n            }\r\n        }\r\n\r\n        const emitRateGradients = particleSystem.getEmitRateGradients();\r\n        if (emitRateGradients) {\r\n            serializationObject.emitRateGradients = [];\r\n            for (const emitRateGradient of emitRateGradients) {\r\n                const serializedGradient: any = {\r\n                    gradient: emitRateGradient.gradient,\r\n                    factor1: emitRateGradient.factor1,\r\n                };\r\n\r\n                if (emitRateGradient.factor2 !== undefined) {\r\n                    serializedGradient.factor2 = emitRateGradient.factor2;\r\n                } else {\r\n                    serializedGradient.factor2 = emitRateGradient.factor1;\r\n                }\r\n\r\n                serializationObject.emitRateGradients.push(serializedGradient);\r\n            }\r\n        }\r\n\r\n        const startSizeGradients = particleSystem.getStartSizeGradients();\r\n        if (startSizeGradients) {\r\n            serializationObject.startSizeGradients = [];\r\n            for (const startSizeGradient of startSizeGradients) {\r\n                const serializedGradient: any = {\r\n                    gradient: startSizeGradient.gradient,\r\n                    factor1: startSizeGradient.factor1,\r\n                };\r\n\r\n                if (startSizeGradient.factor2 !== undefined) {\r\n                    serializedGradient.factor2 = startSizeGradient.factor2;\r\n                } else {\r\n                    serializedGradient.factor2 = startSizeGradient.factor1;\r\n                }\r\n\r\n                serializationObject.startSizeGradients.push(serializedGradient);\r\n            }\r\n        }\r\n\r\n        const lifeTimeGradients = particleSystem.getLifeTimeGradients();\r\n        if (lifeTimeGradients) {\r\n            serializationObject.lifeTimeGradients = [];\r\n            for (const lifeTimeGradient of lifeTimeGradients) {\r\n                const serializedGradient: any = {\r\n                    gradient: lifeTimeGradient.gradient,\r\n                    factor1: lifeTimeGradient.factor1,\r\n                };\r\n\r\n                if (lifeTimeGradient.factor2 !== undefined) {\r\n                    serializedGradient.factor2 = lifeTimeGradient.factor2;\r\n                } else {\r\n                    serializedGradient.factor2 = lifeTimeGradient.factor1;\r\n                }\r\n\r\n                serializationObject.lifeTimeGradients.push(serializedGradient);\r\n            }\r\n        }\r\n\r\n        const limitVelocityGradients = particleSystem.getLimitVelocityGradients();\r\n        if (limitVelocityGradients) {\r\n            serializationObject.limitVelocityGradients = [];\r\n            for (const limitVelocityGradient of limitVelocityGradients) {\r\n                const serializedGradient: any = {\r\n                    gradient: limitVelocityGradient.gradient,\r\n                    factor1: limitVelocityGradient.factor1,\r\n                };\r\n\r\n                if (limitVelocityGradient.factor2 !== undefined) {\r\n                    serializedGradient.factor2 = limitVelocityGradient.factor2;\r\n                } else {\r\n                    serializedGradient.factor2 = limitVelocityGradient.factor1;\r\n                }\r\n\r\n                serializationObject.limitVelocityGradients.push(serializedGradient);\r\n            }\r\n\r\n            serializationObject.limitVelocityDamping = particleSystem.limitVelocityDamping;\r\n        }\r\n\r\n        if (particleSystem.noiseTexture) {\r\n            serializationObject.noiseTexture = particleSystem.noiseTexture.serialize();\r\n        }\r\n    }\r\n\r\n    // Clone\r\n    /**\r\n     * Clones the particle system.\r\n     * @param name The name of the cloned object\r\n     * @param newEmitter The new emitter to use\r\n     * @param cloneTexture Also clone the textures if true\r\n     * @returns the cloned particle system\r\n     */\r\n    public clone(name: string, newEmitter: any, cloneTexture = false): ParticleSystem {\r\n        const custom = { ...this._customWrappers };\r\n        let program: any = null;\r\n        const engine = this._engine as Engine;\r\n        if (engine.createEffectForParticles) {\r\n            if (this.customShader != null) {\r\n                program = this.customShader;\r\n                const defines: string = program.shaderOptions.defines.length > 0 ? program.shaderOptions.defines.join(\"\\n\") : \"\";\r\n                const effect = engine.createEffectForParticles(program.shaderPath.fragmentElement, program.shaderOptions.uniforms, program.shaderOptions.samplers, defines);\r\n                if (!custom[0]) {\r\n                    this.setCustomEffect(effect, 0);\r\n                } else {\r\n                    custom[0].effect = effect;\r\n                }\r\n            }\r\n        }\r\n\r\n        const serialization = this.serialize(cloneTexture);\r\n        const result = ParticleSystem.Parse(serialization, this._scene || this._engine, this._rootUrl);\r\n        result.name = name;\r\n        result.customShader = program;\r\n        result._customWrappers = custom;\r\n\r\n        if (newEmitter === undefined) {\r\n            newEmitter = this.emitter;\r\n        }\r\n\r\n        if (this.noiseTexture) {\r\n            result.noiseTexture = this.noiseTexture.clone();\r\n        }\r\n\r\n        result.emitter = newEmitter;\r\n        if (!this.preventAutoStart) {\r\n            result.start();\r\n        }\r\n\r\n        return result;\r\n    }\r\n}\r\n\r\nSubEmitter._ParseParticleSystem = ParticleSystem.Parse;\r\n"]}