{"version": 3, "file": "outline.vertex.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Shaders/outline.vertex.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,mCAAmC,CAAC;AAC3C,OAAO,kDAAkD,CAAC;AAC1D,OAAO,sDAAsD,CAAC;AAC9D,OAAO,gDAAgD,CAAC;AACxD,OAAO,6CAA6C,CAAC;AACrD,OAAO,uCAAuC,CAAC;AAC/C,OAAO,sCAAsC,CAAC;AAC9C,OAAO,2CAA2C,CAAC;AACnD,OAAO,qCAAqC,CAAC;AAC7C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,8BAA8B,CAAC;AACtC,OAAO,uCAAuC,CAAC;AAC/C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,iCAAiC,CAAC;AAEzC,MAAM,IAAI,GAAG,qBAAqB,CAAC;AACnC,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2Cd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACxC,gBAAgB;AAChB,MAAM,CAAC,MAAM,mBAAmB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore\";\nimport \"./ShadersInclude/bonesDeclaration\";\nimport \"./ShadersInclude/bakedVertexAnimationDeclaration\";\nimport \"./ShadersInclude/morphTargetsVertexGlobalDeclaration\";\nimport \"./ShadersInclude/morphTargetsVertexDeclaration\";\nimport \"./ShadersInclude/clipPlaneVertexDeclaration\";\nimport \"./ShadersInclude/instancesDeclaration\";\nimport \"./ShadersInclude/logDepthDeclaration\";\nimport \"./ShadersInclude/morphTargetsVertexGlobal\";\nimport \"./ShadersInclude/morphTargetsVertex\";\nimport \"./ShadersInclude/instancesVertex\";\nimport \"./ShadersInclude/bonesVertex\";\nimport \"./ShadersInclude/bakedVertexAnimation\";\nimport \"./ShadersInclude/clipPlaneVertex\";\nimport \"./ShadersInclude/logDepthVertex\";\n\nconst name = \"outlineVertexShader\";\nconst shader = `attribute vec3 position;attribute vec3 normal;\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<morphTargetsVertexGlobalDeclaration>\n#include<morphTargetsVertexDeclaration>[0..maxSimultaneousMorphTargets]\n#include<clipPlaneVertexDeclaration>\nuniform float offset;\n#include<instancesDeclaration>\nuniform mat4 viewProjection;\n#ifdef ALPHATEST\nvarying vec2 vUV;uniform mat4 diffuseMatrix;\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#endif\n#include<logDepthDeclaration>\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void)\n{vec3 positionUpdated=position;vec3 normalUpdated=normal;\n#ifdef UV1\nvec2 uvUpdated=uv;\n#endif\n#include<morphTargetsVertexGlobal>\n#include<morphTargetsVertex>[0..maxSimultaneousMorphTargets]\nvec3 offsetPosition=positionUpdated+(normalUpdated*offset);\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nvec4 worldPos=finalWorld*vec4(offsetPosition,1.0);gl_Position=viewProjection*worldPos;\n#ifdef ALPHATEST\n#ifdef UV1\nvUV=vec2(diffuseMatrix*vec4(uvUpdated,1.0,0.0));\n#endif\n#ifdef UV2\nvUV=vec2(diffuseMatrix*vec4(uv2,1.0,0.0));\n#endif\n#endif\n#include<clipPlaneVertex>\n#include<logDepthVertex>\n}\n`;\n// Sideeffect\nShaderStore.ShadersStore[name] = shader;\n/** @internal */\nexport const outlineVertexShader = { name, shader };\n"]}