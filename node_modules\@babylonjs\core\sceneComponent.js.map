{"version": 3, "file": "sceneComponent.js", "sourceRoot": "", "sources": ["../../../dev/core/src/sceneComponent.ts"], "names": [], "mappings": "AAcA;;;GAGG;AACH,MAAM,OAAO,uBAAuB;;AACT,wCAAgB,GAAG,aAAa,CAAC;AACjC,kCAAU,GAAG,OAAO,CAAC;AACrB,4CAAoB,GAAG,iBAAiB,CAAC;AACzC,gDAAwB,GAAG,qBAAqB,CAAC;AACjD,2CAAmB,GAAG,gBAAgB,CAAC;AACvC,oCAAY,GAAG,SAAS,CAAC;AACzB,gDAAwB,GAAG,qBAAqB,CAAC;AACjD,mDAA2B,GAAG,wBAAwB,CAAC;AACvD,4CAAoB,GAAG,iBAAiB,CAAC;AACzC,0CAAkB,GAAG,eAAe,CAAC;AACrC,iDAAyB,GAAG,sBAAsB,CAAC;AACnD,6DAAqC,GAAG,kCAAkC,CAAC;AAC3E,mCAAW,GAAG,QAAQ,CAAC;AACvB,uCAAe,GAAG,YAAY,CAAC;AAC/B,4CAAoB,GAAG,SAAS,CAAC;AACjC,8CAAsB,GAAG,mBAAmB,CAAC;AAC7C,4CAAoB,GAAG,iBAAiB,CAAC;AACzC,mCAAW,GAAG,QAAQ,CAAC;AACvB,0CAAkB,GAAG,eAAe,CAAC;AACrC,kCAAU,GAAG,OAAO,CAAC;AACrB,0CAAkB,GAAG,eAAe,CAAC;AAErC,uDAA+B,GAAG,CAAC,CAAC;AAEpC,yEAAiD,GAAG,CAAC,CAAC;AAEtD,gEAAwC,GAAG,CAAC,CAAC;AAE7C,8DAAsC,GAAG,CAAC,CAAC;AAE3C,+DAAuC,GAAG,CAAC,CAAC;AAE5C,qDAA6B,GAAG,CAAC,CAAC;AAClC,yDAAiC,GAAG,CAAC,CAAC;AACtC,mDAA2B,GAAG,CAAC,CAAC;AAEhC,2DAAmC,GAAG,CAAC,CAAC;AACxC,yDAAiC,GAAG,CAAC,CAAC;AAEtC,wDAAgC,GAAG,CAAC,CAAC;AACrC,wDAAgC,GAAG,CAAC,CAAC;AAErC,uDAA+B,GAAG,CAAC,CAAC;AACpC,uDAA+B,GAAG,CAAC,CAAC;AAEpC,qEAA6C,GAAG,CAAC,CAAC;AAClD,wEAAgD,GAAG,CAAC,CAAC;AAErD,mEAA2C,GAAG,CAAC,CAAC;AAChD,uDAA+B,GAAG,CAAC,CAAC;AAEpC,0DAAkC,GAAG,CAAC,CAAC;AACvC,gDAAwB,GAAG,CAAC,CAAC;AAE7B,4DAAoC,GAAG,CAAC,CAAC;AAEzC,0DAAkC,GAAG,CAAC,CAAC;AACvC,wDAAgC,GAAG,CAAC,CAAC;AAErC,oDAA4B,GAAG,CAAC,CAAC;AACjC,wDAAgC,GAAG,CAAC,CAAC;AACrC,4DAAoC,GAAG,CAAC,CAAC;AACzC,6DAAqC,GAAG,CAAC,CAAC;AAC1C,kDAA0B,GAAG,CAAC,CAAC;AAC/B,0DAAkC,GAAG,CAAC,CAAC;AAEvC,yDAAiC,GAAG,CAAC,CAAC;AAEtC,+DAAuC,GAAG,CAAC,CAAC;AAE5C,8CAAsB,GAAG,CAAC,CAAC;AAE3B,8DAAsC,GAAG,CAAC,CAAC;AAC3C,uEAA+C,GAAG,CAAC,CAAC;AACpD,gEAAwC,GAAG,CAAC,CAAC;AAC7C,iFAAyD,GAAG,CAAC,CAAC;AAE9D,0EAAkD,GAAG,CAAC,CAAC;AACvD,0EAAkD,GAAG,CAAC,CAAC;AAEvD,+CAAuB,GAAG,CAAC,CAAC;AAC5B,+CAAuB,GAAG,CAAC,CAAC;AAC5B,6CAAqB,GAAG,CAAC,CAAC;AAuIrD;;;GAGG;AACH,MAAM,OAAO,KAA0B,SAAQ,KAA+D;IAC1G;;;OAGG;IACH,YAAoB,KAAkE;QAClF,KAAK,CAAC,GAAS,KAAM,CAAC,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,MAAM;QACT,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,KAAa,EAAE,SAA0B,EAAE,MAAS;QACpE,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;QAChC,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzB,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;YACtB,IAAI,KAAK,GAAG,QAAQ,EAAE;gBAClB,MAAM;aACT;SACJ;QACD,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IACpB,CAAC;CACJ", "sourcesContent": ["import type { Scene } from \"./scene\";\r\nimport type { SmartArrayNoDuplicate } from \"./Misc/smartArray\";\r\nimport type { Nullable } from \"./types\";\r\nimport type { PickingInfo } from \"./Collisions/pickingInfo\";\r\nimport type { AbstractScene } from \"./abstractScene\";\r\nimport type { IPointerEvent } from \"./Events/deviceInputEvents\";\r\n\r\nimport type { Mesh } from \"./Meshes/mesh\";\r\nimport type { Effect } from \"./Materials/effect\";\r\nimport type { Camera } from \"./Cameras/camera\";\r\nimport type { AbstractMesh } from \"./Meshes/abstractMesh\";\r\nimport type { SubMesh } from \"./Meshes/subMesh\";\r\nimport type { RenderTargetTexture } from \"./Materials/Textures/renderTargetTexture\";\r\n\r\n/**\r\n * Groups all the scene component constants in one place to ease maintenance.\r\n * @internal\r\n */\r\nexport class SceneComponentConstants {\r\n    public static readonly NAME_EFFECTLAYER = \"EffectLayer\";\r\n    public static readonly NAME_LAYER = \"Layer\";\r\n    public static readonly NAME_LENSFLARESYSTEM = \"LensFlareSystem\";\r\n    public static readonly NAME_BOUNDINGBOXRENDERER = \"BoundingBoxRenderer\";\r\n    public static readonly NAME_PARTICLESYSTEM = \"ParticleSystem\";\r\n    public static readonly NAME_GAMEPAD = \"Gamepad\";\r\n    public static readonly NAME_SIMPLIFICATIONQUEUE = \"SimplificationQueue\";\r\n    public static readonly NAME_GEOMETRYBUFFERRENDERER = \"GeometryBufferRenderer\";\r\n    public static readonly NAME_PREPASSRENDERER = \"PrePassRenderer\";\r\n    public static readonly NAME_DEPTHRENDERER = \"DepthRenderer\";\r\n    public static readonly NAME_DEPTHPEELINGRENDERER = \"DepthPeelingRenderer\";\r\n    public static readonly NAME_POSTPROCESSRENDERPIPELINEMANAGER = \"PostProcessRenderPipelineManager\";\r\n    public static readonly NAME_SPRITE = \"Sprite\";\r\n    public static readonly NAME_SUBSURFACE = \"SubSurface\";\r\n    public static readonly NAME_OUTLINERENDERER = \"Outline\";\r\n    public static readonly NAME_PROCEDURALTEXTURE = \"ProceduralTexture\";\r\n    public static readonly NAME_SHADOWGENERATOR = \"ShadowGenerator\";\r\n    public static readonly NAME_OCTREE = \"Octree\";\r\n    public static readonly NAME_PHYSICSENGINE = \"PhysicsEngine\";\r\n    public static readonly NAME_AUDIO = \"Audio\";\r\n    public static readonly NAME_FLUIDRENDERER = \"FluidRenderer\";\r\n\r\n    public static readonly STEP_ISREADYFORMESH_EFFECTLAYER = 0;\r\n\r\n    public static readonly STEP_BEFOREEVALUATEACTIVEMESH_BOUNDINGBOXRENDERER = 0;\r\n\r\n    public static readonly STEP_EVALUATESUBMESH_BOUNDINGBOXRENDERER = 0;\r\n\r\n    public static readonly STEP_PREACTIVEMESH_BOUNDINGBOXRENDERER = 0;\r\n\r\n    public static readonly STEP_CAMERADRAWRENDERTARGET_EFFECTLAYER = 1;\r\n\r\n    public static readonly STEP_BEFORECAMERADRAW_PREPASS = 0;\r\n    public static readonly STEP_BEFORECAMERADRAW_EFFECTLAYER = 1;\r\n    public static readonly STEP_BEFORECAMERADRAW_LAYER = 2;\r\n\r\n    public static readonly STEP_BEFORERENDERTARGETDRAW_PREPASS = 0;\r\n    public static readonly STEP_BEFORERENDERTARGETDRAW_LAYER = 1;\r\n\r\n    public static readonly STEP_BEFORERENDERINGMESH_PREPASS = 0;\r\n    public static readonly STEP_BEFORERENDERINGMESH_OUTLINE = 1;\r\n\r\n    public static readonly STEP_AFTERRENDERINGMESH_PREPASS = 0;\r\n    public static readonly STEP_AFTERRENDERINGMESH_OUTLINE = 1;\r\n\r\n    public static readonly STEP_AFTERRENDERINGGROUPDRAW_EFFECTLAYER_DRAW = 0;\r\n    public static readonly STEP_AFTERRENDERINGGROUPDRAW_BOUNDINGBOXRENDERER = 1;\r\n\r\n    public static readonly STEP_BEFORECAMERAUPDATE_SIMPLIFICATIONQUEUE = 0;\r\n    public static readonly STEP_BEFORECAMERAUPDATE_GAMEPAD = 1;\r\n\r\n    public static readonly STEP_BEFORECLEAR_PROCEDURALTEXTURE = 0;\r\n    public static readonly STEP_BEFORECLEAR_PREPASS = 1;\r\n\r\n    public static readonly STEP_BEFORERENDERTARGETCLEAR_PREPASS = 0;\r\n\r\n    public static readonly STEP_AFTERRENDERTARGETDRAW_PREPASS = 0;\r\n    public static readonly STEP_AFTERRENDERTARGETDRAW_LAYER = 1;\r\n\r\n    public static readonly STEP_AFTERCAMERADRAW_PREPASS = 0;\r\n    public static readonly STEP_AFTERCAMERADRAW_EFFECTLAYER = 1;\r\n    public static readonly STEP_AFTERCAMERADRAW_LENSFLARESYSTEM = 2;\r\n    public static readonly STEP_AFTERCAMERADRAW_EFFECTLAYER_DRAW = 3;\r\n    public static readonly STEP_AFTERCAMERADRAW_LAYER = 4;\r\n    public static readonly STEP_AFTERCAMERADRAW_FLUIDRENDERER = 5;\r\n\r\n    public static readonly STEP_AFTERCAMERAPOSTPROCESS_LAYER = 0;\r\n\r\n    public static readonly STEP_AFTERRENDERTARGETPOSTPROCESS_LAYER = 0;\r\n\r\n    public static readonly STEP_AFTERRENDER_AUDIO = 0;\r\n\r\n    public static readonly STEP_GATHERRENDERTARGETS_DEPTHRENDERER = 0;\r\n    public static readonly STEP_GATHERRENDERTARGETS_GEOMETRYBUFFERRENDERER = 1;\r\n    public static readonly STEP_GATHERRENDERTARGETS_SHADOWGENERATOR = 2;\r\n    public static readonly STEP_GATHERRENDERTARGETS_POSTPROCESSRENDERPIPELINEMANAGER = 3;\r\n\r\n    public static readonly STEP_GATHERACTIVECAMERARENDERTARGETS_DEPTHRENDERER = 0;\r\n    public static readonly STEP_GATHERACTIVECAMERARENDERTARGETS_FLUIDRENDERER = 1;\r\n\r\n    public static readonly STEP_POINTERMOVE_SPRITE = 0;\r\n    public static readonly STEP_POINTERDOWN_SPRITE = 0;\r\n    public static readonly STEP_POINTERUP_SPRITE = 0;\r\n}\r\n\r\n/**\r\n * This represents a scene component.\r\n *\r\n * This is used to decouple the dependency the scene is having on the different workloads like\r\n * layers, post processes...\r\n */\r\nexport interface ISceneComponent {\r\n    /**\r\n     * The name of the component. Each component must have a unique name.\r\n     */\r\n    name: string;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    scene: Scene;\r\n\r\n    /**\r\n     * Register the component to one instance of a scene.\r\n     */\r\n    register(): void;\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    rebuild(): void;\r\n\r\n    /**\r\n     * Disposes the component and the associated ressources.\r\n     */\r\n    dispose(): void;\r\n}\r\n\r\n/**\r\n * This represents a SERIALIZABLE scene component.\r\n *\r\n * This extends Scene Component to add Serialization methods on top.\r\n */\r\nexport interface ISceneSerializableComponent extends ISceneComponent {\r\n    /**\r\n     * Adds all the elements from the container to the scene\r\n     * @param container the container holding the elements\r\n     */\r\n    addFromContainer(container: AbstractScene): void;\r\n\r\n    /**\r\n     * Removes all the elements in the container from the scene\r\n     * @param container contains the elements to remove\r\n     * @param dispose if the removed element should be disposed (default: false)\r\n     */\r\n    removeFromContainer(container: AbstractScene, dispose?: boolean): void;\r\n\r\n    /**\r\n     * Serializes the component data to the specified json object\r\n     * @param serializationObject The object to serialize to\r\n     */\r\n    serialize(serializationObject: any): void;\r\n}\r\n\r\n/**\r\n * Strong typing of a Mesh related stage step action\r\n */\r\nexport type MeshStageAction = (mesh: AbstractMesh, hardwareInstancedRendering: boolean) => boolean;\r\n\r\n/**\r\n * Strong typing of a Evaluate Sub Mesh related stage step action\r\n */\r\nexport type EvaluateSubMeshStageAction = (mesh: AbstractMesh, subMesh: SubMesh) => void;\r\n\r\n/**\r\n * Strong typing of a pre active Mesh related stage step action\r\n */\r\nexport type PreActiveMeshStageAction = (mesh: AbstractMesh) => void;\r\n\r\n/**\r\n * Strong typing of a Camera related stage step action\r\n */\r\nexport type CameraStageAction = (camera: Camera) => void;\r\n\r\n/**\r\n * Strong typing of a Camera Frame buffer related stage step action\r\n */\r\nexport type CameraStageFrameBufferAction = (camera: Camera) => boolean;\r\n\r\n/**\r\n * Strong typing of a Render Target related stage step action\r\n */\r\nexport type RenderTargetStageAction = (renderTarget: RenderTargetTexture, faceIndex?: number, layer?: number) => void;\r\n\r\n/**\r\n * Strong typing of a RenderingGroup related stage step action\r\n */\r\nexport type RenderingGroupStageAction = (renderingGroupId: number) => void;\r\n\r\n/**\r\n * Strong typing of a Mesh Render related stage step action\r\n */\r\nexport type RenderingMeshStageAction = (mesh: Mesh, subMesh: SubMesh, batch: any, effect: Nullable<Effect>) => void;\r\n\r\n/**\r\n * Strong typing of a simple stage step action\r\n */\r\nexport type SimpleStageAction = () => void;\r\n\r\n/**\r\n * Strong typing of a render target action.\r\n */\r\nexport type RenderTargetsStageAction = (renderTargets: SmartArrayNoDuplicate<RenderTargetTexture>) => void;\r\n\r\n/**\r\n * Strong typing of a pointer move action.\r\n */\r\nexport type PointerMoveStageAction = (\r\n    unTranslatedPointerX: number,\r\n    unTranslatedPointerY: number,\r\n    pickResult: Nullable<PickingInfo>,\r\n    isMeshPicked: boolean,\r\n    element: Nullable<HTMLElement>\r\n) => Nullable<PickingInfo>;\r\n\r\n/**\r\n * Strong typing of a pointer up/down action.\r\n */\r\nexport type PointerUpDownStageAction = (\r\n    unTranslatedPointerX: number,\r\n    unTranslatedPointerY: number,\r\n    pickResult: Nullable<PickingInfo>,\r\n    evt: IPointerEvent,\r\n    doubleClick: boolean\r\n) => Nullable<PickingInfo>;\r\n\r\n/**\r\n * Representation of a stage in the scene (Basically a list of ordered steps)\r\n * @internal\r\n */\r\nexport class Stage<T extends Function> extends Array<{ index: number; component: ISceneComponent; action: T }> {\r\n    /**\r\n     * Hide ctor from the rest of the world.\r\n     * @param items The items to add.\r\n     */\r\n    private constructor(items?: { index: number; component: ISceneComponent; action: T }[]) {\r\n        super(...(<any>items));\r\n    }\r\n\r\n    /**\r\n     * Creates a new Stage.\r\n     * @returns A new instance of a Stage\r\n     */\r\n    static Create<T extends Function>(): Stage<T> {\r\n        return Object.create(Stage.prototype);\r\n    }\r\n\r\n    /**\r\n     * Registers a step in an ordered way in the targeted stage.\r\n     * @param index Defines the position to register the step in\r\n     * @param component Defines the component attached to the step\r\n     * @param action Defines the action to launch during the step\r\n     */\r\n    public registerStep(index: number, component: ISceneComponent, action: T): void {\r\n        let i = 0;\r\n        let maxIndex = Number.MAX_VALUE;\r\n        for (; i < this.length; i++) {\r\n            const step = this[i];\r\n            maxIndex = step.index;\r\n            if (index < maxIndex) {\r\n                break;\r\n            }\r\n        }\r\n        this.splice(i, 0, { index, component, action: action.bind(component) });\r\n    }\r\n\r\n    /**\r\n     * Clears all the steps from the stage.\r\n     */\r\n    public clear(): void {\r\n        this.length = 0;\r\n    }\r\n}\r\n"]}