{"version": 3, "file": "particleSystemSet.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Particles/particleSystemSet.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,YAAY,EAAE,MAAM,kCAAkC,CAAC;AAEhE,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAE7D,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAGjE,uDAAuD;AACvD,MAAM,uCAAuC;CAI5C;AAED;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAA9B;QAQY,wBAAmB,GAAG,IAAI,CAAC;QAEnC;;WAEG;QACI,YAAO,GAAsB,EAAE,CAAC;IAsJ3C,CAAC;IApJG;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAuC;QAC1D,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,YAAY,EAAE;YAC/C,IAAK,IAAI,CAAC,YAA6B,CAAC,OAAO,EAAE;gBAC5C,IAAI,CAAC,YAA6B,CAAC,OAAO,EAAE,CAAC;aACjD;YACD,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;SACpC;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;SAC1B;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC9B,CAAC;IAED;;;;;;;;OAQG;IACI,kBAAkB,CAAC,OAA8D,EAAE,gBAAwB,EAAE,KAAY;QAC5H,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,YAAY,EAAE;YAC/C,IAAK,IAAI,CAAC,YAA6B,CAAC,OAAO,EAAE;gBAC5C,IAAI,CAAC,YAA6B,CAAC,OAAO,EAAE,CAAC;aACjD;SACJ;QAED,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAEhC,IAAI,CAAC,uBAAuB,GAAG;YAC3B,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,OAAO;YAChB,gBAAgB,EAAE,gBAAgB;SACrC,CAAC;QAEF,MAAM,WAAW,GAAG,YAAY,CAAC,eAAe,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QACrH,WAAW,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QAEhD,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QACtE,QAAQ,CAAC,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;QACvC,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEhC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC;SAChC;QAED,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,OAAsB;QAC/B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,IAAI,OAAO,EAAE;gBACT,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;aAC5B;YACD,MAAM,CAAC,KAAK,EAAE,CAAC;SAClB;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,MAAM,CAAC,OAAO,EAAE,CAAC;SACpB;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QAExB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAK,IAAI,CAAC,YAA6B,CAAC,OAAO,EAAE;gBAC5C,IAAI,CAAC,YAA6B,CAAC,OAAO,EAAE,CAAC;aACjD;YACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;IACL,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,gBAAgB,GAAG,KAAK;QACrC,MAAM,MAAM,GAAQ,EAAE,CAAC;QAEvB,MAAM,CAAC,OAAO,GAAG,EAAE,CAAC;QACpB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;SAC3D;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC;SACjD;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,KAAK,CAAC,IAAS,EAAE,KAAY,EAAE,GAAG,GAAG,KAAK,EAAE,QAAiB;QACvE,MAAM,MAAM,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACvC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElD,KAAK,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QAE9C,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;SAC7J;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;YACrC,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;gBACvB,KAAK,QAAQ;oBACT,MAAM,CAAC,kBAAkB,CACrB;wBACI,QAAQ,EAAE,OAAO,CAAC,QAAQ;wBAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;wBAC1B,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC;qBACzC,EACD,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAC7B,KAAK,CACR,CAAC;oBACF,MAAM;aACb;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;;AAjKD;;GAEG;AACW,+BAAa,GAAG,wCAAwC,AAA3C,CAA4C", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { CreateSphere } from \"../Meshes/Builders/sphereBuilder\";\r\nimport type { IParticleSystem } from \"./IParticleSystem\";\r\nimport { GPUParticleSystem } from \"./gpuParticleSystem\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { ParticleSystem } from \"../Particles/particleSystem\";\r\nimport type { Scene, IDisposable } from \"../scene\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport type { Vector3 } from \"../Maths/math.vector\";\r\n\r\n/** Internal class used to store shapes for emitters */\r\nclass ParticleSystemSetEmitterCreationOptions {\r\n    public kind: string;\r\n    public options: any;\r\n    public renderingGroupId: number;\r\n}\r\n\r\n/**\r\n * Represents a set of particle systems working together to create a specific effect\r\n */\r\nexport class ParticleSystemSet implements IDisposable {\r\n    /**\r\n     * Gets or sets base Assets URL\r\n     */\r\n    public static BaseAssetsUrl = \"https://assets.babylonjs.com/particles\";\r\n\r\n    private _emitterCreationOptions: ParticleSystemSetEmitterCreationOptions;\r\n    private _emitterNode: Nullable<AbstractMesh | Vector3>;\r\n    private _emitterNodeIsOwned = true;\r\n\r\n    /**\r\n     * Gets the particle system list\r\n     */\r\n    public systems: IParticleSystem[] = [];\r\n\r\n    /**\r\n     * Gets or sets the emitter node used with this set\r\n     */\r\n    public get emitterNode(): Nullable<AbstractMesh | Vector3> {\r\n        return this._emitterNode;\r\n    }\r\n\r\n    public set emitterNode(value: Nullable<AbstractMesh | Vector3>) {\r\n        if (this._emitterNodeIsOwned && this._emitterNode) {\r\n            if ((this._emitterNode as AbstractMesh).dispose) {\r\n                (this._emitterNode as AbstractMesh).dispose();\r\n            }\r\n            this._emitterNodeIsOwned = false;\r\n        }\r\n\r\n        for (const system of this.systems) {\r\n            system.emitter = value;\r\n        }\r\n\r\n        this._emitterNode = value;\r\n    }\r\n\r\n    /**\r\n     * Creates a new emitter mesh as a sphere\r\n     * @param options defines the options used to create the sphere\r\n     * @param options.diameter\r\n     * @param options.segments\r\n     * @param options.color\r\n     * @param renderingGroupId defines the renderingGroupId to use for the sphere\r\n     * @param scene defines the hosting scene\r\n     */\r\n    public setEmitterAsSphere(options: { diameter: number; segments: number; color: Color3 }, renderingGroupId: number, scene: Scene) {\r\n        if (this._emitterNodeIsOwned && this._emitterNode) {\r\n            if ((this._emitterNode as AbstractMesh).dispose) {\r\n                (this._emitterNode as AbstractMesh).dispose();\r\n            }\r\n        }\r\n\r\n        this._emitterNodeIsOwned = true;\r\n\r\n        this._emitterCreationOptions = {\r\n            kind: \"Sphere\",\r\n            options: options,\r\n            renderingGroupId: renderingGroupId,\r\n        };\r\n\r\n        const emitterMesh = CreateSphere(\"emitterSphere\", { diameter: options.diameter, segments: options.segments }, scene);\r\n        emitterMesh.renderingGroupId = renderingGroupId;\r\n\r\n        const material = new StandardMaterial(\"emitterSphereMaterial\", scene);\r\n        material.emissiveColor = options.color;\r\n        emitterMesh.material = material;\r\n\r\n        for (const system of this.systems) {\r\n            system.emitter = emitterMesh;\r\n        }\r\n\r\n        this._emitterNode = emitterMesh;\r\n    }\r\n\r\n    /**\r\n     * Starts all particle systems of the set\r\n     * @param emitter defines an optional mesh to use as emitter for the particle systems\r\n     */\r\n    public start(emitter?: AbstractMesh): void {\r\n        for (const system of this.systems) {\r\n            if (emitter) {\r\n                system.emitter = emitter;\r\n            }\r\n            system.start();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Release all associated resources\r\n     */\r\n    public dispose(): void {\r\n        for (const system of this.systems) {\r\n            system.dispose();\r\n        }\r\n\r\n        this.systems.length = 0;\r\n\r\n        if (this._emitterNode) {\r\n            if ((this._emitterNode as AbstractMesh).dispose) {\r\n                (this._emitterNode as AbstractMesh).dispose();\r\n            }\r\n            this._emitterNode = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serialize the set into a JSON compatible object\r\n     * @param serializeTexture defines if the texture must be serialized as well\r\n     * @returns a JSON compatible representation of the set\r\n     */\r\n    public serialize(serializeTexture = false): any {\r\n        const result: any = {};\r\n\r\n        result.systems = [];\r\n        for (const system of this.systems) {\r\n            result.systems.push(system.serialize(serializeTexture));\r\n        }\r\n\r\n        if (this._emitterNode) {\r\n            result.emitter = this._emitterCreationOptions;\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Parse a new ParticleSystemSet from a serialized source\r\n     * @param data defines a JSON compatible representation of the set\r\n     * @param scene defines the hosting scene\r\n     * @param gpu defines if we want GPU particles or CPU particles\r\n     * @param capacity defines the system capacity (if null or undefined the sotred capacity will be used)\r\n     * @returns a new ParticleSystemSet\r\n     */\r\n    public static Parse(data: any, scene: Scene, gpu = false, capacity?: number): ParticleSystemSet {\r\n        const result = new ParticleSystemSet();\r\n        const rootUrl = this.BaseAssetsUrl + \"/textures/\";\r\n\r\n        scene = scene || EngineStore.LastCreatedScene;\r\n\r\n        for (const system of data.systems) {\r\n            result.systems.push(gpu ? GPUParticleSystem.Parse(system, scene, rootUrl, true, capacity) : ParticleSystem.Parse(system, scene, rootUrl, true, capacity));\r\n        }\r\n\r\n        if (data.emitter) {\r\n            const options = data.emitter.options;\r\n            switch (data.emitter.kind) {\r\n                case \"Sphere\":\r\n                    result.setEmitterAsSphere(\r\n                        {\r\n                            diameter: options.diameter,\r\n                            segments: options.segments,\r\n                            color: Color3.FromArray(options.color),\r\n                        },\r\n                        data.emitter.renderingGroupId,\r\n                        scene\r\n                    );\r\n                    break;\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n}\r\n"]}