{"version": 3, "file": "particleTextureBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Particle/particleTextureBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAIhF,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAGjD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAC;AAKpD;;GAEG;AACH,MAAM,OAAO,oBAAqB,SAAQ,iBAAiB;IAqBvD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAzB3C,iBAAY,GAAG,gBAAgB,CAAC;QAUxC;;WAEG;QACI,wBAAmB,GAAG,KAAK,CAAC;QAEnC;;WAEG;QACI,yBAAoB,GAAG,KAAK,CAAC;QAShC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,qCAAqC,CAAC,UAAU,EAAE,KAAK,EAAE,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QAE9H,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAC5G,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAC3G,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAExG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,CACtD,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,CAChJ,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,EAAE;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,KAA6B;QAC3C,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;IACjD,CAAC;IAEM,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,EAAE,CAAC,IAAI;QACnH,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE;YACtB,IAAI,OAAO,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,aAAa,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAEhI,IAAI,CAAC,OAAO,EAAE;gBACV,OAAO,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC/B,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;aACzC;YACD,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACrC;IACL,CAAC;IAEM,cAAc,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QAC9F,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QACzE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;IAC7E,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE;YACtD,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,YAAY,CAAC,KAA6B,EAAE,MAAmC,EAAE,OAAe;QACpG,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC,gBAAgB,IAAI,OAAO,KAAK,CAAC;QAE5G,KAAK,CAAC,iBAAiB,IAAI,UAAU,IAAI,CAAC,iBAAiB,IAAI,CAAC;QAChE,KAAK,CAAC,iBAAiB,IAAI,GAAG,MAAM,CAAC,sBAAsB,mBAAmB,MAAM,CAAC,sBAAsB,MAAM,CAAC;QAClH,KAAK,CAAC,iBAAiB,IAAI,UAAU,CAAC;QAEtC,KAAK,CAAC,iBAAiB,IAAI,UAAU,IAAI,CAAC,gBAAgB,IAAI,CAAC;QAC/D,KAAK,CAAC,iBAAiB,IAAI,GAAG,MAAM,CAAC,sBAAsB,oBAAoB,MAAM,CAAC,sBAAsB,MAAM,CAAC;QACnH,KAAK,CAAC,iBAAiB,IAAI,UAAU,CAAC;IAC1C,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,EAAE;YAClD,OAAO;SACV;QAED,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAEtE,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAExC,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAC9D,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAE5D,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAE5D,KAAK,CAAC,iBAAiB,IAAI,QAAQ,IAAI,CAAC,gBAAgB,gBAAgB,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,EAAE,CAAC,sBAAsB,MAAM,CAAC;QAEnI,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,MAAM,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;aACjD;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACnE,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC9C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;SAC1D;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,mBAAmB,CAAC;QACnE,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;QAEvE,IAAI,mBAAmB,CAAC,OAAO,EAAE;YAC7B,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YAChF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAY,CAAC;SACxF;IACL,CAAC;CACJ;AAED,aAAa,CAAC,8BAA8B,EAAE,oBAAoB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport type { NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport type { BaseTexture } from \"../../../Textures/baseTexture\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { Texture } from \"../../../Textures/texture\";\r\nimport type { Scene } from \"../../../../scene\";\r\n\r\nimport type { NodeMaterial } from \"../../nodeMaterial\";\r\n\r\n/**\r\n * Base block used for the particle texture\r\n */\r\nexport class ParticleTextureBlock extends NodeMaterialBlock {\r\n    private _samplerName = \"diffuseSampler\";\r\n    private _linearDefineName: string;\r\n    private _gammaDefineName: string;\r\n    private _tempTextureRead: string;\r\n\r\n    /**\r\n     * Gets or sets the texture associated with the node\r\n     */\r\n    public texture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if content needs to be converted to gamma space\r\n     */\r\n    public convertToGammaSpace = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if content needs to be converted to linear space\r\n     */\r\n    public convertToLinearSpace = false;\r\n\r\n    /**\r\n     * Create a new ParticleTextureBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = false;\r\n\r\n        this.registerInput(\"uv\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false, NodeMaterialBlockTargets.VertexAndFragment);\r\n\r\n        this.registerOutput(\"rgba\", NodeMaterialBlockConnectionPointTypes.Color4, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"rgb\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"r\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"g\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"b\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"a\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this._inputs[0].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Vector2 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"ParticleTextureBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the uv input component\r\n     */\r\n    public get uv(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgba output component\r\n     */\r\n    public get rgba(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgb output component\r\n     */\r\n    public get rgb(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the r output component\r\n     */\r\n    public get r(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the g output component\r\n     */\r\n    public get g(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the b output component\r\n     */\r\n    public get b(): NodeMaterialConnectionPoint {\r\n        return this._outputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the a output component\r\n     */\r\n    public get a(): NodeMaterialConnectionPoint {\r\n        return this._outputs[5];\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"diffuseSampler\");\r\n    }\r\n\r\n    public autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.uv.isConnected) {\r\n            let uvInput = material.getInputBlockByPredicate((b) => b.isAttribute && b.name === \"particle_uv\" && additionalFilteringInfo(b));\r\n\r\n            if (!uvInput) {\r\n                uvInput = new InputBlock(\"uv\");\r\n                uvInput.setAsAttribute(\"particle_uv\");\r\n            }\r\n            uvInput.output.connectTo(this.uv);\r\n        }\r\n    }\r\n\r\n    public prepareDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        defines.setValue(this._linearDefineName, this.convertToGammaSpace, true);\r\n        defines.setValue(this._gammaDefineName, this.convertToLinearSpace, true);\r\n    }\r\n\r\n    public isReady() {\r\n        if (this.texture && !this.texture.isReadyOrNotBlocking()) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    private _writeOutput(state: NodeMaterialBuildState, output: NodeMaterialConnectionPoint, swizzle: string) {\r\n        state.compilationString += `${this._declareOutput(output, state)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n\r\n        state.compilationString += `#ifdef ${this._linearDefineName}\\n`;\r\n        state.compilationString += `${output.associatedVariableName} = toGammaSpace(${output.associatedVariableName});\\n`;\r\n        state.compilationString += `#endif\\n`;\r\n\r\n        state.compilationString += `#ifdef ${this._gammaDefineName}\\n`;\r\n        state.compilationString += `${output.associatedVariableName} = toLinearSpace(${output.associatedVariableName});\\n`;\r\n        state.compilationString += `#endif\\n`;\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Vertex) {\r\n            return;\r\n        }\r\n\r\n        this._tempTextureRead = state._getFreeVariableName(\"tempTextureRead\");\r\n\r\n        state._emit2DSampler(this._samplerName);\r\n\r\n        state.sharedData.blockingBlocks.push(this);\r\n        state.sharedData.textureBlocks.push(this);\r\n        state.sharedData.blocksWithDefines.push(this);\r\n\r\n        this._linearDefineName = state._getFreeDefineName(\"ISLINEAR\");\r\n        this._gammaDefineName = state._getFreeDefineName(\"ISGAMMA\");\r\n\r\n        const comments = `//${this.name}`;\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n\r\n        state.compilationString += `vec4 ${this._tempTextureRead} = texture2D(${this._samplerName}, ${this.uv.associatedVariableName});\\n`;\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints) {\r\n                this._writeOutput(state, output, output.name);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.convertToGammaSpace = this.convertToGammaSpace;\r\n        serializationObject.convertToLinearSpace = this.convertToLinearSpace;\r\n        if (this.texture && !this.texture.isRenderTarget) {\r\n            serializationObject.texture = this.texture.serialize();\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.convertToGammaSpace = serializationObject.convertToGammaSpace;\r\n        this.convertToLinearSpace = !!serializationObject.convertToLinearSpace;\r\n\r\n        if (serializationObject.texture) {\r\n            rootUrl = serializationObject.texture.url.indexOf(\"data:\") === 0 ? \"\" : rootUrl;\r\n            this.texture = Texture.Parse(serializationObject.texture, scene, rootUrl) as Texture;\r\n        }\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ParticleTextureBlock\", ParticleTextureBlock);\r\n"]}