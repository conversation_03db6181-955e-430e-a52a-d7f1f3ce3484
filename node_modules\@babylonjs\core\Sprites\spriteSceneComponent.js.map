{"version": 3, "file": "spriteSceneComponent.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Sprites/spriteSceneComponent.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AAGjC,OAAO,EAAE,GAAG,EAAE,MAAM,gBAAgB,CAAC;AAErC,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAExD,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAwFjD,KAAK,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,GAAQ,EAAE,SAAuC,EAAE,SAAmB,EAAE,MAAe;IACpI,IAAI,CAAC,WAAW,EAAE;QACd,OAAO,IAAI,CAAC;KACf;IAED,IAAI,WAAW,GAAG,IAAI,CAAC;IAEvB,IAAI,CAAC,MAAM,EAAE;QACT,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,IAAI,CAAC;SACf;QACD,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;KAC9B;IAED,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;QACvD,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;YAC/E,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAEvD,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;gBAC3B,SAAS;aACZ;YAED,MAAM,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YAC3E,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;gBACxB,SAAS;aACZ;YAED,IAAI,CAAC,SAAS,IAAI,WAAW,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,EAAE;gBAC9E,SAAS;aACZ;YAED,WAAW,GAAG,MAAM,CAAC;YAErB,IAAI,SAAS,EAAE;gBACX,MAAM;aACT;SACJ;KACJ;IAED,OAAO,WAAW,IAAI,IAAI,WAAW,EAAE,CAAC;AAC5C,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,GAAQ,EAAE,SAAuC,EAAE,MAAe;IACpH,IAAI,CAAC,WAAW,EAAE;QACd,OAAO,IAAI,CAAC;KACf;IAED,IAAI,YAAY,GAAkB,EAAE,CAAC;IAErC,IAAI,CAAC,MAAM,EAAE;QACT,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,IAAI,CAAC;SACf;QACD,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;KAC9B;IAED,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;QACvD,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;YAC/E,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAEvD,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;gBAC3B,SAAS;aACZ;YAED,MAAM,OAAO,GAAG,aAAa,CAAC,eAAe,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;YAEtE,IAAI,OAAO,KAAK,IAAI,EAAE;gBAClB,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aAC/C;SACJ;KACJ;IAED,OAAO,YAAY,CAAC;AACxB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,CAAS,EAAE,CAAS,EAAE,SAAuC,EAAE,SAAmB,EAAE,MAAe;IACtI,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;QAC7B,OAAO,IAAI,CAAC;KACf;IAED,IAAI,CAAC,kCAAkC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;IAElF,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,qBAAqB,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IACnG,IAAI,MAAM,EAAE;QACR,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,6BAA6B,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;KACjE;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,GAAQ,EAAE,SAAuC,EAAE,SAAmB,EAAE,MAAe;IACjI,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;QAC7B,OAAO,IAAI,CAAC;KACf;IAED,IAAI,CAAC,MAAM,EAAE;QACT,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,IAAI,CAAC;SACf;QACD,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;KAC9B;IAED,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAE5E,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,qBAAqB,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IACnG,IAAI,MAAM,EAAE;QACR,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;KACpB;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,CAAS,EAAE,CAAS,EAAE,SAAuC,EAAE,MAAe;IACtH,IAAI,CAAC,kCAAkC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,qBAAsB,EAAE,MAAM,CAAC,CAAC;IAEnF,OAAO,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,qBAAsB,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAC1F,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,sBAAsB,GAAG,UAAU,GAAQ,EAAE,SAAuC,EAAE,MAAe;IACjH,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;QAC7B,OAAO,IAAI,CAAC;KACf;IAED,IAAI,CAAC,MAAM,EAAE;QACT,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,IAAI,CAAC;SACf;QACD,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;KAC9B;IAED,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAE5E,OAAO,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,qBAAqB,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AACzF,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,MAAwB;IACrE,IAAI,IAAI,CAAC,kBAAkB,KAAK,MAAM,EAAE;QACpC,OAAO;KACV;IAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE;QAClE,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,0BAA0B,EAAE,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC;KAC9J;IAED,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC;IACjC,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE;QAClE,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,2BAA2B,EAAE,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC;KAC/J;AACL,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,oBAAoB,GAAG;IACnC,OAAO,IAAI,CAAC,kBAAkB,CAAC;AACnC,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IAc7B;;;OAGG;IACH,YAAY,KAAY;QAjBxB;;WAEG;QACa,SAAI,GAAG,uBAAuB,CAAC,WAAW,CAAC;QAevD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,EAAsB,CAAC;QACnD,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3D,IAAI,CAAC,KAAK,CAAC,kCAAkC,GAAG,IAAI,UAAU,EAAS,CAAC;QACxE,IAAI,CAAC,KAAK,CAAC,iCAAiC,GAAG,IAAI,UAAU,EAAS,CAAC;QACvE,IAAI,CAAC,gBAAgB,GAAG,CAAC,MAAc,EAAW,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;gBACvB,OAAO,KAAK,CAAC;aAChB;YACD,OAAO,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,aAAa,CAAC,kBAAkB,CAAC;QACxE,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,uBAAuB,CAAC,uBAAuB,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACpH,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,uBAAuB,CAAC,uBAAuB,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACpH,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,YAAY,CAAC,uBAAuB,CAAC,qBAAqB,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAClH,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,gCAAgC;IACpC,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,KAAK,EAAE,CAAC;QACtD,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,KAAK,EAAE,CAAC;QAErD,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;QACjD,IAAI,CAAC,cAAc,EAAE;YACjB,OAAO;SACV;QACD,OAAO,cAAc,CAAC,MAAM,EAAE;YAC1B,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;SAC/B;IACL,CAAC;IAEO,qBAAqB,CAAC,mBAA0C,EAAE,CAAS,EAAE,CAAS,EAAE,SAAmB,EAAE,MAAe;QAChI,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,gBAAgB,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QACrF,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,GAAG,GAAG,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;SACrE;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,YAAY,CAChB,oBAA4B,EAC5B,oBAA4B,EAC5B,UAAiC,EACjC,YAAqB,EACrB,OAA8B;QAE9B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,YAAY,EAAE;YACd,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;SACpC;aAAM;YACH,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,KAAK,EAAE,KAAK,CAAC,sBAAsB,IAAI,SAAS,CAAC,CAAC;YAElJ,IAAI,UAAU,IAAI,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,YAAY,EAAE;gBACzD,KAAK,CAAC,oBAAoB,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;gBACpD,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,OAAO,EAAE;oBACtC,IAAI,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAC,kBAAkB,CAAC,aAAa,IAAI,KAAK,CAAC,kBAAkB,CAAC,aAAa,CAAC,WAAW,EAAE;wBAC1H,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,kBAAkB,CAAC,aAAa,CAAC,WAAW,CAAC;qBAC7E;yBAAM;wBACH,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC;qBAC5C;iBACJ;aACJ;iBAAM;gBACH,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;aACpC;SACJ;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,YAAY,CAAC,oBAA4B,EAAE,oBAA4B,EAAE,UAAiC,EAAE,GAAkB;QAClI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC/B,IAAI,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;YACzD,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,EAAE,KAAK,CAAC,sBAAsB,IAAI,SAAS,CAAC,CAAC;YAEnJ,IAAI,UAAU,IAAI,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,YAAY,EAAE;gBACzD,IAAI,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE;oBACvC,KAAK,CAAC,iBAAiB,GAAG,UAAU,CAAC,YAAY,CAAC;oBAClD,QAAQ,GAAG,CAAC,MAAM,EAAE;wBAChB,KAAK,CAAC;4BACF,UAAU,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAChD,SAAS,CAAC,wBAAwB,EAClC,WAAW,CAAC,mBAAmB,CAAC,UAAU,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CACvE,CAAC;4BACF,MAAM;wBACV,KAAK,CAAC;4BACF,UAAU,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAChD,SAAS,CAAC,0BAA0B,EACpC,WAAW,CAAC,mBAAmB,CAAC,UAAU,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CACvE,CAAC;4BACF,MAAM;wBACV,KAAK,CAAC;4BACF,UAAU,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAChD,SAAS,CAAC,yBAAyB,EACnC,WAAW,CAAC,mBAAmB,CAAC,UAAU,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CACvE,CAAC;4BACF,MAAM;qBACb;oBACD,IAAI,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE;wBACvC,UAAU,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAChD,SAAS,CAAC,wBAAwB,EAClC,WAAW,CAAC,mBAAmB,CAAC,UAAU,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CACvE,CAAC;qBACL;iBACJ;aACJ;SACJ;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,UAAU,CACd,oBAA4B,EAC5B,oBAA4B,EAC5B,UAAiC,EACjC,GAAkB,EAClB,WAAoB;QAEpB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;YACzD,MAAM,gBAAgB,GAAG,KAAK,CAAC,UAAU,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,EAAE,KAAK,CAAC,sBAAsB,IAAI,SAAS,CAAC,CAAC;YAE/J,IAAI,gBAAgB,EAAE;gBAClB,IAAI,gBAAgB,CAAC,GAAG,IAAI,gBAAgB,CAAC,YAAY,EAAE;oBACvD,IAAI,gBAAgB,CAAC,YAAY,CAAC,aAAa,EAAE;wBAC7C,gBAAgB,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CACtD,SAAS,CAAC,sBAAsB,EAChC,WAAW,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CAC7E,CAAC;wBAEF,IAAI,gBAAgB,CAAC,YAAY,CAAC,aAAa,EAAE;4BAC7C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,EAAE,EAAE;gCAC/C,gBAAgB,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CACtD,SAAS,CAAC,oBAAoB,EAC9B,WAAW,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CAC7E,CAAC;6BACL;4BAED,IAAI,WAAW,EAAE;gCACb,gBAAgB,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CACtD,SAAS,CAAC,0BAA0B,EACpC,WAAW,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CAC7E,CAAC;6BACL;yBACJ;qBACJ;iBACJ;gBACD,IAAI,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,iBAAiB,CAAC,aAAa,IAAI,KAAK,CAAC,iBAAiB,KAAK,gBAAgB,CAAC,YAAY,EAAE;oBAC/H,KAAK,CAAC,iBAAiB,CAAC,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,uBAAuB,EAAE,WAAW,CAAC,mBAAmB,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;iBACjK;aACJ;SACJ;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Scene } from \"../scene\";\r\nimport type { Sprite } from \"./sprite\";\r\nimport type { ISpriteManager } from \"./spriteManager\";\r\nimport { Ray } from \"../Culling/ray\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { PickingInfo } from \"../Collisions/pickingInfo\";\r\nimport type { ISceneComponent } from \"../sceneComponent\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport { ActionEvent } from \"../Actions/actionEvent\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { IPointerEvent } from \"../Events/deviceInputEvents\";\r\n\r\ndeclare module \"../scene\" {\r\n    export interface Scene {\r\n        /** @internal */\r\n        _pointerOverSprite: Nullable<Sprite>;\r\n\r\n        /** @internal */\r\n        _pickedDownSprite: Nullable<Sprite>;\r\n\r\n        /** @internal */\r\n        _tempSpritePickingRay: Nullable<Ray>;\r\n\r\n        /**\r\n         * All of the sprite managers added to this scene\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/sprites\r\n         */\r\n        spriteManagers?: Array<ISpriteManager>;\r\n\r\n        /**\r\n         * An event triggered when sprites rendering is about to start\r\n         * Note: This event can be trigger more than once per frame (because sprites can be rendered by render target textures as well)\r\n         */\r\n        onBeforeSpritesRenderingObservable: Observable<Scene>;\r\n\r\n        /**\r\n         * An event triggered when sprites rendering is done\r\n         * Note: This event can be trigger more than once per frame (because sprites can be rendered by render target textures as well)\r\n         */\r\n        onAfterSpritesRenderingObservable: Observable<Scene>;\r\n\r\n        /** @internal */\r\n        _internalPickSprites(ray: Ray, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo>;\r\n\r\n        /** Launch a ray to try to pick a sprite in the scene\r\n         * @param x position on screen\r\n         * @param y position on screen\r\n         * @param predicate Predicate function used to determine eligible sprites. Can be set to null. In this case, a sprite must have isPickable set to true\r\n         * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n         * @param camera camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n         * @returns a PickingInfo\r\n         */\r\n        pickSprite(x: number, y: number, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo>;\r\n\r\n        /** Use the given ray to pick a sprite in the scene\r\n         * @param ray The ray (in world space) to use to pick meshes\r\n         * @param predicate Predicate function used to determine eligible sprites. Can be set to null. In this case, a sprite must have isPickable set to true\r\n         * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n         * @param camera camera to use. Can be set to null. In this case, the scene.activeCamera will be used\r\n         * @returns a PickingInfo\r\n         */\r\n        pickSpriteWithRay(ray: Ray, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo>;\r\n\r\n        /** @internal */\r\n        _internalMultiPickSprites(ray: Ray, predicate?: (sprite: Sprite) => boolean, camera?: Camera): Nullable<PickingInfo[]>;\r\n\r\n        /** Launch a ray to try to pick sprites in the scene\r\n         * @param x position on screen\r\n         * @param y position on screen\r\n         * @param predicate Predicate function used to determine eligible sprites. Can be set to null. In this case, a sprite must have isPickable set to true\r\n         * @param camera camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n         * @returns a PickingInfo array\r\n         */\r\n        multiPickSprite(x: number, y: number, predicate?: (sprite: Sprite) => boolean, camera?: Camera): Nullable<PickingInfo[]>;\r\n\r\n        /** Use the given ray to pick sprites in the scene\r\n         * @param ray The ray (in world space) to use to pick meshes\r\n         * @param predicate Predicate function used to determine eligible sprites. Can be set to null. In this case, a sprite must have isPickable set to true\r\n         * @param camera camera to use. Can be set to null. In this case, the scene.activeCamera will be used\r\n         * @returns a PickingInfo array\r\n         */\r\n        multiPickSpriteWithRay(ray: Ray, predicate?: (sprite: Sprite) => boolean, camera?: Camera): Nullable<PickingInfo[]>;\r\n\r\n        /**\r\n         * Force the sprite under the pointer\r\n         * @param sprite defines the sprite to use\r\n         */\r\n        setPointerOverSprite(sprite: Nullable<Sprite>): void;\r\n\r\n        /**\r\n         * Gets the sprite under the pointer\r\n         * @returns a Sprite or null if no sprite is under the pointer\r\n         */\r\n        getPointerOverSprite(): Nullable<Sprite>;\r\n    }\r\n}\r\n\r\nScene.prototype._internalPickSprites = function (ray: Ray, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo> {\r\n    if (!PickingInfo) {\r\n        return null;\r\n    }\r\n\r\n    let pickingInfo = null;\r\n\r\n    if (!camera) {\r\n        if (!this.activeCamera) {\r\n            return null;\r\n        }\r\n        camera = this.activeCamera;\r\n    }\r\n\r\n    if (this.spriteManagers && this.spriteManagers.length > 0) {\r\n        for (let spriteIndex = 0; spriteIndex < this.spriteManagers.length; spriteIndex++) {\r\n            const spriteManager = this.spriteManagers[spriteIndex];\r\n\r\n            if (!spriteManager.isPickable) {\r\n                continue;\r\n            }\r\n\r\n            const result = spriteManager.intersects(ray, camera, predicate, fastCheck);\r\n            if (!result || !result.hit) {\r\n                continue;\r\n            }\r\n\r\n            if (!fastCheck && pickingInfo != null && result.distance >= pickingInfo.distance) {\r\n                continue;\r\n            }\r\n\r\n            pickingInfo = result;\r\n\r\n            if (fastCheck) {\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    return pickingInfo || new PickingInfo();\r\n};\r\n\r\nScene.prototype._internalMultiPickSprites = function (ray: Ray, predicate?: (sprite: Sprite) => boolean, camera?: Camera): Nullable<PickingInfo[]> {\r\n    if (!PickingInfo) {\r\n        return null;\r\n    }\r\n\r\n    let pickingInfos: PickingInfo[] = [];\r\n\r\n    if (!camera) {\r\n        if (!this.activeCamera) {\r\n            return null;\r\n        }\r\n        camera = this.activeCamera;\r\n    }\r\n\r\n    if (this.spriteManagers && this.spriteManagers.length > 0) {\r\n        for (let spriteIndex = 0; spriteIndex < this.spriteManagers.length; spriteIndex++) {\r\n            const spriteManager = this.spriteManagers[spriteIndex];\r\n\r\n            if (!spriteManager.isPickable) {\r\n                continue;\r\n            }\r\n\r\n            const results = spriteManager.multiIntersects(ray, camera, predicate);\r\n\r\n            if (results !== null) {\r\n                pickingInfos = pickingInfos.concat(results);\r\n            }\r\n        }\r\n    }\r\n\r\n    return pickingInfos;\r\n};\r\n\r\nScene.prototype.pickSprite = function (x: number, y: number, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo> {\r\n    if (!this._tempSpritePickingRay) {\r\n        return null;\r\n    }\r\n\r\n    this.createPickingRayInCameraSpaceToRef(x, y, this._tempSpritePickingRay, camera);\r\n\r\n    const result = this._internalPickSprites(this._tempSpritePickingRay, predicate, fastCheck, camera);\r\n    if (result) {\r\n        result.ray = this.createPickingRayInCameraSpace(x, y, camera);\r\n    }\r\n\r\n    return result;\r\n};\r\n\r\nScene.prototype.pickSpriteWithRay = function (ray: Ray, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo> {\r\n    if (!this._tempSpritePickingRay) {\r\n        return null;\r\n    }\r\n\r\n    if (!camera) {\r\n        if (!this.activeCamera) {\r\n            return null;\r\n        }\r\n        camera = this.activeCamera;\r\n    }\r\n\r\n    Ray.TransformToRef(ray, camera.getViewMatrix(), this._tempSpritePickingRay);\r\n\r\n    const result = this._internalPickSprites(this._tempSpritePickingRay, predicate, fastCheck, camera);\r\n    if (result) {\r\n        result.ray = ray;\r\n    }\r\n\r\n    return result;\r\n};\r\n\r\nScene.prototype.multiPickSprite = function (x: number, y: number, predicate?: (sprite: Sprite) => boolean, camera?: Camera): Nullable<PickingInfo[]> {\r\n    this.createPickingRayInCameraSpaceToRef(x, y, this._tempSpritePickingRay!, camera);\r\n\r\n    return this._internalMultiPickSprites(this._tempSpritePickingRay!, predicate, camera);\r\n};\r\n\r\nScene.prototype.multiPickSpriteWithRay = function (ray: Ray, predicate?: (sprite: Sprite) => boolean, camera?: Camera): Nullable<PickingInfo[]> {\r\n    if (!this._tempSpritePickingRay) {\r\n        return null;\r\n    }\r\n\r\n    if (!camera) {\r\n        if (!this.activeCamera) {\r\n            return null;\r\n        }\r\n        camera = this.activeCamera;\r\n    }\r\n\r\n    Ray.TransformToRef(ray, camera.getViewMatrix(), this._tempSpritePickingRay);\r\n\r\n    return this._internalMultiPickSprites(this._tempSpritePickingRay, predicate, camera);\r\n};\r\n\r\nScene.prototype.setPointerOverSprite = function (sprite: Nullable<Sprite>): void {\r\n    if (this._pointerOverSprite === sprite) {\r\n        return;\r\n    }\r\n\r\n    if (this._pointerOverSprite && this._pointerOverSprite.actionManager) {\r\n        this._pointerOverSprite.actionManager.processTrigger(Constants.ACTION_OnPointerOutTrigger, ActionEvent.CreateNewFromSprite(this._pointerOverSprite, this));\r\n    }\r\n\r\n    this._pointerOverSprite = sprite;\r\n    if (this._pointerOverSprite && this._pointerOverSprite.actionManager) {\r\n        this._pointerOverSprite.actionManager.processTrigger(Constants.ACTION_OnPointerOverTrigger, ActionEvent.CreateNewFromSprite(this._pointerOverSprite, this));\r\n    }\r\n};\r\n\r\nScene.prototype.getPointerOverSprite = function (): Nullable<Sprite> {\r\n    return this._pointerOverSprite;\r\n};\r\n\r\n/**\r\n * Defines the sprite scene component responsible to manage sprites\r\n * in a given scene.\r\n */\r\nexport class SpriteSceneComponent implements ISceneComponent {\r\n    /**\r\n     * The component name helpfull to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_SPRITE;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    /** @internal */\r\n    private _spritePredicate: (sprite: Sprite) => boolean;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene: Scene) {\r\n        this.scene = scene;\r\n        this.scene.spriteManagers = [] as ISpriteManager[];\r\n        this.scene._tempSpritePickingRay = Ray ? Ray.Zero() : null;\r\n        this.scene.onBeforeSpritesRenderingObservable = new Observable<Scene>();\r\n        this.scene.onAfterSpritesRenderingObservable = new Observable<Scene>();\r\n        this._spritePredicate = (sprite: Sprite): boolean => {\r\n            if (!sprite.actionManager) {\r\n                return false;\r\n            }\r\n            return sprite.isPickable && sprite.actionManager.hasPointerTriggers;\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {\r\n        this.scene._pointerMoveStage.registerStep(SceneComponentConstants.STEP_POINTERMOVE_SPRITE, this, this._pointerMove);\r\n        this.scene._pointerDownStage.registerStep(SceneComponentConstants.STEP_POINTERDOWN_SPRITE, this, this._pointerDown);\r\n        this.scene._pointerUpStage.registerStep(SceneComponentConstants.STEP_POINTERUP_SPRITE, this, this._pointerUp);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        /** Nothing to do for sprites */\r\n    }\r\n\r\n    /**\r\n     * Disposes the component and the associated resources.\r\n     */\r\n    public dispose(): void {\r\n        this.scene.onBeforeSpritesRenderingObservable.clear();\r\n        this.scene.onAfterSpritesRenderingObservable.clear();\r\n\r\n        const spriteManagers = this.scene.spriteManagers;\r\n        if (!spriteManagers) {\r\n            return;\r\n        }\r\n        while (spriteManagers.length) {\r\n            spriteManagers[0].dispose();\r\n        }\r\n    }\r\n\r\n    private _pickSpriteButKeepRay(originalPointerInfo: Nullable<PickingInfo>, x: number, y: number, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo> {\r\n        const result = this.scene.pickSprite(x, y, this._spritePredicate, fastCheck, camera);\r\n        if (result) {\r\n            result.ray = originalPointerInfo ? originalPointerInfo.ray : null;\r\n        }\r\n        return result;\r\n    }\r\n\r\n    private _pointerMove(\r\n        unTranslatedPointerX: number,\r\n        unTranslatedPointerY: number,\r\n        pickResult: Nullable<PickingInfo>,\r\n        isMeshPicked: boolean,\r\n        element: Nullable<HTMLElement>\r\n    ): Nullable<PickingInfo> {\r\n        const scene = this.scene;\r\n        if (isMeshPicked) {\r\n            scene.setPointerOverSprite(null);\r\n        } else {\r\n            pickResult = this._pickSpriteButKeepRay(pickResult, unTranslatedPointerX, unTranslatedPointerY, false, scene.cameraToUseForPointers || undefined);\r\n\r\n            if (pickResult && pickResult.hit && pickResult.pickedSprite) {\r\n                scene.setPointerOverSprite(pickResult.pickedSprite);\r\n                if (!scene.doNotHandleCursors && element) {\r\n                    if (scene._pointerOverSprite && scene._pointerOverSprite.actionManager && scene._pointerOverSprite.actionManager.hoverCursor) {\r\n                        element.style.cursor = scene._pointerOverSprite.actionManager.hoverCursor;\r\n                    } else {\r\n                        element.style.cursor = scene.hoverCursor;\r\n                    }\r\n                }\r\n            } else {\r\n                scene.setPointerOverSprite(null);\r\n            }\r\n        }\r\n\r\n        return pickResult;\r\n    }\r\n\r\n    private _pointerDown(unTranslatedPointerX: number, unTranslatedPointerY: number, pickResult: Nullable<PickingInfo>, evt: IPointerEvent): Nullable<PickingInfo> {\r\n        const scene = this.scene;\r\n        scene._pickedDownSprite = null;\r\n        if (scene.spriteManagers && scene.spriteManagers.length > 0) {\r\n            pickResult = scene.pickSprite(unTranslatedPointerX, unTranslatedPointerY, this._spritePredicate, false, scene.cameraToUseForPointers || undefined);\r\n\r\n            if (pickResult && pickResult.hit && pickResult.pickedSprite) {\r\n                if (pickResult.pickedSprite.actionManager) {\r\n                    scene._pickedDownSprite = pickResult.pickedSprite;\r\n                    switch (evt.button) {\r\n                        case 0:\r\n                            pickResult.pickedSprite.actionManager.processTrigger(\r\n                                Constants.ACTION_OnLeftPickTrigger,\r\n                                ActionEvent.CreateNewFromSprite(pickResult.pickedSprite, scene, evt)\r\n                            );\r\n                            break;\r\n                        case 1:\r\n                            pickResult.pickedSprite.actionManager.processTrigger(\r\n                                Constants.ACTION_OnCenterPickTrigger,\r\n                                ActionEvent.CreateNewFromSprite(pickResult.pickedSprite, scene, evt)\r\n                            );\r\n                            break;\r\n                        case 2:\r\n                            pickResult.pickedSprite.actionManager.processTrigger(\r\n                                Constants.ACTION_OnRightPickTrigger,\r\n                                ActionEvent.CreateNewFromSprite(pickResult.pickedSprite, scene, evt)\r\n                            );\r\n                            break;\r\n                    }\r\n                    if (pickResult.pickedSprite.actionManager) {\r\n                        pickResult.pickedSprite.actionManager.processTrigger(\r\n                            Constants.ACTION_OnPickDownTrigger,\r\n                            ActionEvent.CreateNewFromSprite(pickResult.pickedSprite, scene, evt)\r\n                        );\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return pickResult;\r\n    }\r\n\r\n    private _pointerUp(\r\n        unTranslatedPointerX: number,\r\n        unTranslatedPointerY: number,\r\n        pickResult: Nullable<PickingInfo>,\r\n        evt: IPointerEvent,\r\n        doubleClick: boolean\r\n    ): Nullable<PickingInfo> {\r\n        const scene = this.scene;\r\n        if (scene.spriteManagers && scene.spriteManagers.length > 0) {\r\n            const spritePickResult = scene.pickSprite(unTranslatedPointerX, unTranslatedPointerY, this._spritePredicate, false, scene.cameraToUseForPointers || undefined);\r\n\r\n            if (spritePickResult) {\r\n                if (spritePickResult.hit && spritePickResult.pickedSprite) {\r\n                    if (spritePickResult.pickedSprite.actionManager) {\r\n                        spritePickResult.pickedSprite.actionManager.processTrigger(\r\n                            Constants.ACTION_OnPickUpTrigger,\r\n                            ActionEvent.CreateNewFromSprite(spritePickResult.pickedSprite, scene, evt)\r\n                        );\r\n\r\n                        if (spritePickResult.pickedSprite.actionManager) {\r\n                            if (!this.scene._inputManager._isPointerSwiping()) {\r\n                                spritePickResult.pickedSprite.actionManager.processTrigger(\r\n                                    Constants.ACTION_OnPickTrigger,\r\n                                    ActionEvent.CreateNewFromSprite(spritePickResult.pickedSprite, scene, evt)\r\n                                );\r\n                            }\r\n\r\n                            if (doubleClick) {\r\n                                spritePickResult.pickedSprite.actionManager.processTrigger(\r\n                                    Constants.ACTION_OnDoublePickTrigger,\r\n                                    ActionEvent.CreateNewFromSprite(spritePickResult.pickedSprite, scene, evt)\r\n                                );\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                if (scene._pickedDownSprite && scene._pickedDownSprite.actionManager && scene._pickedDownSprite !== spritePickResult.pickedSprite) {\r\n                    scene._pickedDownSprite.actionManager.processTrigger(Constants.ACTION_OnPickOutTrigger, ActionEvent.CreateNewFromSprite(scene._pickedDownSprite, scene, evt));\r\n                }\r\n            }\r\n        }\r\n\r\n        return pickResult;\r\n    }\r\n}\r\n"]}