{"version": 3, "file": "postProcessManager.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/postProcessManager.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAMjD;;;GAGG;AACH,MAAM,OAAO,kBAAkB;IAK3B;;;OAGG;IACH,YAAY,KAAY;QANhB,mBAAc,GAA8C,EAAE,CAAC;QAOnE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAEO,eAAe;QACnB,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE;YAChD,OAAO;SACV;QAED,MAAM;QACN,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAErB,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,YAAY,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAEjJ,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAEO,iBAAiB;QACrB,UAAU;QACV,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC3E,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAE1D,IAAI,CAAC,EAAE,EAAE;YACL,OAAO;SACV;QACD,EAAE,CAAC,QAAQ,EAAE,CAAC;QACd,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED,UAAU;IACV;;;;;;OAMG;IACI,aAAa,CAAC,gBAA2C,IAAI,EAAE,gBAAyC,IAAI;QAC/G,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QACxC,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,KAAK,CAAC;SAChB;QAED,aAAa,GAAG,aAAa,IAA6B,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE;YACtF,OAAO,EAAE,IAAI,IAAI,CAAC;QACtB,CAAC,CAAC,CAAC;QAEP,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;YACnF,OAAO,KAAK,CAAC;SAChB;QAED,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,aAAa,EAAE,aAAa,KAAK,IAAI,IAAI,aAAa,KAAK,SAAS,CAAC,CAAC;QACxG,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG;IACI,YAAY,CACf,aAA4B,EAC5B,gBAA+C,IAAI,EACnD,uBAAuB,GAAG,KAAK,EAC/B,SAAS,GAAG,CAAC,EACb,QAAQ,GAAG,CAAC,EACZ,mBAAmB,GAAG,KAAK;QAE3B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACvD,IAAI,KAAK,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClC,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;aACvF;iBAAM;gBACH,IAAI,aAAa,EAAE;oBACf,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,uBAAuB,EAAE,QAAQ,CAAC,CAAC;iBAC7G;qBAAM,IAAI,CAAC,mBAAmB,EAAE;oBAC7B,MAAM,CAAC,yBAAyB,EAAE,CAAC;iBACtC;gBACD,MAAM,CAAC,kBAAkB,EAAE,CAAC,gBAAgB,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC;aACnF;YAED,MAAM,EAAE,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;YAChC,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;YAE1B,IAAI,MAAM,EAAE;gBACR,EAAE,CAAC,wBAAwB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBAEpD,OAAO;gBACP,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAEnE,aAAa;gBACb,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEnE,EAAE,CAAC,uBAAuB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;aACtD;SACJ;QAED,uBAAuB;QACvB,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC5B,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;;;;;;;OAQG;IACI,cAAc,CACjB,YAAsB,EACtB,aAAmC,EACnC,SAAkB,EAClB,aAAkC,EAClC,uBAAuB,GAAG,KAAK;QAE/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAExC,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QAED,aAAa,GAAG,aAAa,IAAwB,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE;YACjF,OAAO,EAAE,IAAI,IAAI,CAAC;QACtB,CAAC,CAAC,CAAC;QACP,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;YACjE,OAAO;SACV;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,aAAa,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,EAAE,KAAK,EAAE,EAAE;YAClE,MAAM,EAAE,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;YAEhC,IAAI,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;gBACjB,EAAE,CAAC,cAAc,GAAG,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;aACzF;iBAAM;gBACH,IAAI,aAAa,EAAE;oBACf,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,uBAAuB,CAAC,CAAC;oBAChG,EAAE,CAAC,cAAc,GAAG,aAAa,CAAC;iBACrC;qBAAM;oBACH,MAAM,CAAC,yBAAyB,EAAE,CAAC;oBACnC,EAAE,CAAC,cAAc,GAAG,IAAI,CAAC;iBAC5B;gBACD,MAAM,CAAC,kBAAkB,EAAE,CAAC,gBAAgB,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC;aACnF;YAED,IAAI,YAAY,EAAE;gBACd,MAAM;aACT;YAED,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;YAE1B,IAAI,MAAM,EAAE;gBACR,EAAE,CAAC,wBAAwB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBAEpD,OAAO;gBACP,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAEnE,aAAa;gBACb,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEnE,EAAE,CAAC,uBAAuB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;aACtD;SACJ;QAED,iBAAiB;QACjB,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC5B,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC3B,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,OAAO;QACV,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAC9D,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;SACzD;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;IACL,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { InternalTexture } from \"../Materials/Textures/internalTexture\";\r\nimport type { PostProcess } from \"./postProcess\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport type { RenderTargetWrapper } from \"../Engines/renderTargetWrapper\";\r\n\r\nimport type { Scene } from \"../scene\";\r\n\r\n/**\r\n * PostProcessManager is used to manage one or more post processes or post process pipelines\r\n * See https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/usePostProcesses\r\n */\r\nexport class PostProcessManager {\r\n    private _scene: Scene;\r\n    private _indexBuffer: Nullable<DataBuffer>;\r\n    private _vertexBuffers: { [key: string]: Nullable<VertexBuffer> } = {};\r\n\r\n    /**\r\n     * Creates a new instance PostProcess\r\n     * @param scene The scene that the post process is associated with.\r\n     */\r\n    constructor(scene: Scene) {\r\n        this._scene = scene;\r\n    }\r\n\r\n    private _prepareBuffers(): void {\r\n        if (this._vertexBuffers[VertexBuffer.PositionKind]) {\r\n            return;\r\n        }\r\n\r\n        // VBO\r\n        const vertices = [];\r\n        vertices.push(1, 1);\r\n        vertices.push(-1, 1);\r\n        vertices.push(-1, -1);\r\n        vertices.push(1, -1);\r\n\r\n        this._vertexBuffers[VertexBuffer.PositionKind] = new VertexBuffer(this._scene.getEngine(), vertices, VertexBuffer.PositionKind, false, false, 2);\r\n\r\n        this._buildIndexBuffer();\r\n    }\r\n\r\n    private _buildIndexBuffer(): void {\r\n        // Indices\r\n        const indices = [];\r\n        indices.push(0);\r\n        indices.push(1);\r\n        indices.push(2);\r\n\r\n        indices.push(0);\r\n        indices.push(2);\r\n        indices.push(3);\r\n\r\n        this._indexBuffer = this._scene.getEngine().createIndexBuffer(indices);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the vertex buffers of the manager.\r\n     * @internal\r\n     */\r\n    public _rebuild(): void {\r\n        const vb = this._vertexBuffers[VertexBuffer.PositionKind];\r\n\r\n        if (!vb) {\r\n            return;\r\n        }\r\n        vb._rebuild();\r\n        this._buildIndexBuffer();\r\n    }\r\n\r\n    // Methods\r\n    /**\r\n     * Prepares a frame to be run through a post process.\r\n     * @param sourceTexture The input texture to the post processes. (default: null)\r\n     * @param postProcesses An array of post processes to be run. (default: null)\r\n     * @returns True if the post processes were able to be run.\r\n     * @internal\r\n     */\r\n    public _prepareFrame(sourceTexture: Nullable<InternalTexture> = null, postProcesses: Nullable<PostProcess[]> = null): boolean {\r\n        const camera = this._scene.activeCamera;\r\n        if (!camera) {\r\n            return false;\r\n        }\r\n\r\n        postProcesses = postProcesses || <Nullable<PostProcess[]>>camera._postProcesses.filter((pp) => {\r\n                return pp != null;\r\n            });\r\n\r\n        if (!postProcesses || postProcesses.length === 0 || !this._scene.postProcessesEnabled) {\r\n            return false;\r\n        }\r\n\r\n        postProcesses[0].activate(camera, sourceTexture, postProcesses !== null && postProcesses !== undefined);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Manually render a set of post processes to a texture.\r\n     * Please note, the frame buffer won't be unbound after the call in case you have more render to do.\r\n     * @param postProcesses An array of post processes to be run.\r\n     * @param targetTexture The render target wrapper to render to.\r\n     * @param forceFullscreenViewport force gl.viewport to be full screen eg. 0,0,textureWidth,textureHeight\r\n     * @param faceIndex defines the face to render to if a cubemap is defined as the target\r\n     * @param lodLevel defines which lod of the texture to render to\r\n     * @param doNotBindFrambuffer If set to true, assumes that the framebuffer has been bound previously\r\n     */\r\n    public directRender(\r\n        postProcesses: PostProcess[],\r\n        targetTexture: Nullable<RenderTargetWrapper> = null,\r\n        forceFullscreenViewport = false,\r\n        faceIndex = 0,\r\n        lodLevel = 0,\r\n        doNotBindFrambuffer = false\r\n    ): void {\r\n        const engine = this._scene.getEngine();\r\n\r\n        for (let index = 0; index < postProcesses.length; index++) {\r\n            if (index < postProcesses.length - 1) {\r\n                postProcesses[index + 1].activate(this._scene.activeCamera, targetTexture?.texture);\r\n            } else {\r\n                if (targetTexture) {\r\n                    engine.bindFramebuffer(targetTexture, faceIndex, undefined, undefined, forceFullscreenViewport, lodLevel);\r\n                } else if (!doNotBindFrambuffer) {\r\n                    engine.restoreDefaultFramebuffer();\r\n                }\r\n                engine._debugInsertMarker?.(`post process ${postProcesses[index].name} output`);\r\n            }\r\n\r\n            const pp = postProcesses[index];\r\n            const effect = pp.apply();\r\n\r\n            if (effect) {\r\n                pp.onBeforeRenderObservable.notifyObservers(effect);\r\n\r\n                // VBOs\r\n                this._prepareBuffers();\r\n                engine.bindBuffers(this._vertexBuffers, this._indexBuffer, effect);\r\n\r\n                // Draw order\r\n                engine.drawElementsType(Constants.MATERIAL_TriangleFillMode, 0, 6);\r\n\r\n                pp.onAfterRenderObservable.notifyObservers(effect);\r\n            }\r\n        }\r\n\r\n        // Restore depth buffer\r\n        engine.setDepthBuffer(true);\r\n        engine.setDepthWrite(true);\r\n    }\r\n\r\n    /**\r\n     * Finalize the result of the output of the postprocesses.\r\n     * @param doNotPresent If true the result will not be displayed to the screen.\r\n     * @param targetTexture The render target wrapper to render to.\r\n     * @param faceIndex The index of the face to bind the target texture to.\r\n     * @param postProcesses The array of post processes to render.\r\n     * @param forceFullscreenViewport force gl.viewport to be full screen eg. 0,0,textureWidth,textureHeight (default: false)\r\n     * @internal\r\n     */\r\n    public _finalizeFrame(\r\n        doNotPresent?: boolean,\r\n        targetTexture?: RenderTargetWrapper,\r\n        faceIndex?: number,\r\n        postProcesses?: Array<PostProcess>,\r\n        forceFullscreenViewport = false\r\n    ): void {\r\n        const camera = this._scene.activeCamera;\r\n\r\n        if (!camera) {\r\n            return;\r\n        }\r\n\r\n        postProcesses = postProcesses || <Array<PostProcess>>camera._postProcesses.filter((pp) => {\r\n                return pp != null;\r\n            });\r\n        if (postProcesses.length === 0 || !this._scene.postProcessesEnabled) {\r\n            return;\r\n        }\r\n        const engine = this._scene.getEngine();\r\n\r\n        for (let index = 0, len = postProcesses.length; index < len; index++) {\r\n            const pp = postProcesses[index];\r\n\r\n            if (index < len - 1) {\r\n                pp._outputTexture = postProcesses[index + 1].activate(camera, targetTexture?.texture);\r\n            } else {\r\n                if (targetTexture) {\r\n                    engine.bindFramebuffer(targetTexture, faceIndex, undefined, undefined, forceFullscreenViewport);\r\n                    pp._outputTexture = targetTexture;\r\n                } else {\r\n                    engine.restoreDefaultFramebuffer();\r\n                    pp._outputTexture = null;\r\n                }\r\n                engine._debugInsertMarker?.(`post process ${postProcesses[index].name} output`);\r\n            }\r\n\r\n            if (doNotPresent) {\r\n                break;\r\n            }\r\n\r\n            const effect = pp.apply();\r\n\r\n            if (effect) {\r\n                pp.onBeforeRenderObservable.notifyObservers(effect);\r\n\r\n                // VBOs\r\n                this._prepareBuffers();\r\n                engine.bindBuffers(this._vertexBuffers, this._indexBuffer, effect);\r\n\r\n                // Draw order\r\n                engine.drawElementsType(Constants.MATERIAL_TriangleFillMode, 0, 6);\r\n\r\n                pp.onAfterRenderObservable.notifyObservers(effect);\r\n            }\r\n        }\r\n\r\n        // Restore states\r\n        engine.setDepthBuffer(true);\r\n        engine.setDepthWrite(true);\r\n        engine.setAlphaMode(Constants.ALPHA_DISABLE);\r\n    }\r\n\r\n    /**\r\n     * Disposes of the post process manager.\r\n     */\r\n    public dispose(): void {\r\n        const buffer = this._vertexBuffers[VertexBuffer.PositionKind];\r\n        if (buffer) {\r\n            buffer.dispose();\r\n            this._vertexBuffers[VertexBuffer.PositionKind] = null;\r\n        }\r\n\r\n        if (this._indexBuffer) {\r\n            this._scene.getEngine()._releaseBuffer(this._indexBuffer);\r\n            this._indexBuffer = null;\r\n        }\r\n    }\r\n}\r\n"]}