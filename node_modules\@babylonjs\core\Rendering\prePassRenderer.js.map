{"version": 3, "file": "prePassRenderer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Rendering/prePassRenderer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,2CAA2C,CAAC;AAGhF,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAGjD,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAI7C,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAIjD,OAAO,EAAE,sBAAsB,EAAE,MAAM,qCAAqC,CAAC;AAE7E;;;;;GAKG;AACH,MAAM,OAAO,eAAe;IAwCxB;;OAEG;IACH,IAAW,2BAA2B;QAClC,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED,IAAW,2BAA2B,CAAC,KAAc;QACjD,IAAI,IAAI,CAAC,4BAA4B,KAAK,KAAK,EAAE;YAC7C,OAAO;SACV;QAED,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;QAC1C,IAAI,CAAC,+BAA+B,EAAE,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,IAAY;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAClC,CAAC;IAED,IAAW,OAAO,CAAC,CAAS;QACxB,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC;IAC/B,CAAC;IAID;;;OAGG;IACH,IAAW,+BAA+B;QACtC,OAAO,IAAI,CAAC,gCAAgC,CAAC;IACjD,CAAC;IAED,IAAW,+BAA+B,CAAC,KAAc;QACrD,IAAI,IAAI,CAAC,gCAAgC,KAAK,KAAK,EAAE;YACjD,OAAO;SACV;QAED,IAAI,CAAC,gCAAgC,GAAG,KAAK,CAAC;QAC9C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAoED;;;;OAIG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,mBAAkD;QACtE,IAAI,mBAAmB,EAAE;YACrB,IAAI,CAAC,cAAc,GAAG,mBAAmB,CAAC;SAC7C;aAAM;YACH,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,YAAY,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;SACjH;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,SAAS,CAAC;IAClD,CAAC;IASO,kCAAkC;QACtC,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE;YACxC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,4BAA4B,EAAE,CAAC;YAElE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACvB,gBAAgB;gBAChB,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;gBAC7C,OAAO;aACV;YAED,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;SACnD;aAAM;YACH,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC;aACjD;YACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE,CAAC;SAC/C;IACL,CAAC;IAiBD;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IASD;;;OAGG;IACH,YAAY,KAAY;QApPxB;;WAEG;QACI,wBAAmB,GAAmB,EAAE,CAAC;QAEhD;;;;WAIG;QACI,sBAAiB,GAAe,EAAE,CAAC;QAK1C;;WAEG;QACI,aAAQ,GAAW,CAAC,CAAC;QAEpB,cAAS,GAAa,EAAE,CAAC;QACzB,gBAAW,GAAa,EAAE,CAAC;QAC3B,eAAU,GAAa,EAAE,CAAC;QAC1B,cAAS,GAAa,EAAE,CAAC;QACzB,oBAAe,GAAa,EAAE,CAAC;QAM/B,iCAA4B,GAAG,KAAK,CAAC;QAsCrC,qCAAgC,GAAG,KAAK,CAAC;QAyEzC,aAAQ,GAAY,IAAI,CAAC;QAOjC;;WAEG;QACK,0BAAqB,GAAiC,EAAE,CAAC;QAmCjE;;WAEG;QACI,qCAAgC,GAAG,IAAI,CAAC;QAwB/C;;WAEG;QACI,kBAAa,GAA0B,EAAE,CAAC;QAEhC,gBAAW,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC,qBAAgB,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,6EAA6E;QAEnI,aAAQ,GAAY,KAAK,CAAC;QAE1B,iCAA4B,GAAG,KAAK,CAAC;QAU7C;;;;WAIG;QACI,0BAAqB,GAAG,KAAK,CAAC;QAOjC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,IAAI,GAAG,SAAS,CAAC,yBAAyB,CAAC;QAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE;YACnF,IAAI,GAAG,SAAS,CAAC,iBAAiB,CAAC;SACtC;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE;YAClG,IAAI,GAAG,SAAS,CAAC,sBAAsB,CAAC;SAC3C;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAC5D,MAAM,MAAM,GAAG,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACxD,IAAI,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,iBAAiB,EAAE;gBACxE,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjF,IACI,CAAC,MAAM,KAAK,SAAS,CAAC,eAAe,IAAI,MAAM,KAAK,SAAS,CAAC,gBAAgB,IAAI,MAAM,KAAK,SAAS,CAAC,kBAAkB,CAAC;oBAC1H,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,2BAA2B,EACjD;oBACE,wKAAwK;oBACxK,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC,sBAAsB,CAAC;iBAChH;aACJ;SACJ;QAED,eAAe,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAClE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC;IACzC,CAAC;IAED;;;;;;;OAOG;IACI,mBAAmB,CAAC,IAAY,EAAE,mBAAkD;QACvF,MAAM,EAAE,GAAG,IAAI,mBAAmB,CAAC,IAAI,EAAE,mBAAmB,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE;YAC5J,eAAe,EAAE,KAAK;YACtB,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;YACnD,WAAW,EAAE,SAAS,CAAC,wBAAwB;YAC/C,KAAK,EAAE,EAAE;YACT,kCAAkC,EAAE,IAAI;SAC3C,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAE5B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,yHAAyH;YACzH,IAAI,CAAC,OAAO,EAAE,CAAC;SAClB;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,oBAAoB,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAAC,MAAc,EAAE,OAAgB;QAC5D,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,gBAAgB,GAAG,QAAQ,IAAI,QAAQ,CAAC,gBAAgB,CAAC;QAC/D,MAAM,QAAQ,GAAG,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAE7E,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;YAC7C,IAAI,MAAM,CAAC,YAAY,IAAI,gBAAgB,IAAI,CAAC,QAAQ,EAAE;gBACtD,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;aAC9D;iBAAM;gBACH,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;oBACnC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;iBAC1D;qBAAM;oBACH,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC;iBAC1C;gBAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,QAAQ,EAAE;oBAC9D,IAAI,CAAC,eAAe,CAAC,UAAW,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;iBACrE;aACJ;SACJ;IACL,CAAC;IAEO,wBAAwB;QAC5B,MAAM,iBAAiB,GAAG,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAG,CAAC,KAAK,CAAC,CAAC;QAC5B,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,CAAC;QACjC,MAAM,aAAa,GAAG,CAAC,IAAI,CAAC,CAAC;QAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE;YACpC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7B,IAAI,CAAC,GAAG,CAAC,EAAE;gBACP,IAAI,IAAI,CAAC,gCAAgC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,0BAA0B,EAAE;oBACtG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACxB,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAC/B;qBAAM;oBACH,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACvB,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAChC;gBACD,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC7B;SACJ;QAED,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;QAClF,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QACtE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;QAChF,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;IAC9E,CAAC;IAEO,YAAY;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5D,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;SACxE;QAED,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,UAAU,GAAG,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACzD,IAAI,CAAC,SAAS,GAAG,CAAC,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,CAAC;QAC7F,IAAI,CAAC,WAAW,GAAG,CAAC,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAC,CAAC;QACjG,IAAI,CAAC,SAAS,GAAG,CAAC,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,CAAC;QAC7F,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;IACtB,CAAC;IAEO,2BAA2B;QAC/B,IAAI,CAAC,kCAAkC,EAAE,CAAC;QAE1C,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;YAEpC,MAAM,iBAAiB,GAAG,EAAE,CAAC;YAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC7C,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACjC;YAED,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAG,CAAC,CAAC;YAEhF,MAAM,OAAO,GAAG;gBACZ;oBACI,eAAe,EAAE,SAAS,CAAC,0BAA0B;oBACrD,sBAAsB,EAAE,sBAAsB,CAAC,kBAAkB;iBACpE;gBACD;oBACI,eAAe,EAAE,SAAS,CAAC,2BAA2B;oBACtD,sBAAsB,EAAE,sBAAsB,CAAC,mBAAmB;iBACrE;gBACD;oBACI,eAAe,EAAE,SAAS,CAAC,6BAA6B;oBACxD,sBAAsB,EAAE,sBAAsB,CAAC,qBAAqB;iBACvE;gBACD;oBACI,eAAe,EAAE,SAAS,CAAC,iCAAiC;oBAC5D,sBAAsB,EAAE,sBAAsB,CAAC,yBAAyB;iBAC3E;gBACD;oBACI,eAAe,EAAE,SAAS,CAAC,6BAA6B;oBACxD,sBAAsB,EAAE,sBAAsB,CAAC,qBAAqB;iBACvE;aACJ,CAAC;YAEF,4CAA4C;YAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;gBAClE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;oBACd,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;oBACjF,iBAAiB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;iBACnC;aACJ;YAED,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC,CAAC;SAC5F;IACL,CAAC;IAED;;OAEG;IACI,kBAAkB;QACrB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,IAAI,CAAC,mBAAmB,EAAE;YACzE,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;gBACnC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;aAC1D;iBAAM;gBACH,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC;aAC1C;SACJ;IACL,CAAC;IAED;;OAEG;IACH,6DAA6D;IACtD,WAAW,CAAC,MAAe,EAAE,SAAkB,EAAE,KAAc;QAClE,wEAAwE;QAExE,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,OAAO,EAAE,CAAC;SAClB;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;YAChD,OAAO;SACV;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,EAAE,CAAC;SACxC;QAED,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC;IAEO,aAAa,CAAC,mBAAwC,EAAE,SAAkB,EAAE,KAAc;QAC9F,IAAI,mBAAmB,CAAC,mBAAmB,EAAE;YACzC,mBAAmB,CAAC,mBAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,mBAAmB,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC;SACxJ;aAAM,IAAI,IAAI,CAAC,+BAA+B,CAAC,MAAM,EAAE;YACpD,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;SAClD;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC;SAC5C;IACL,CAAC;IAED;;;;;;OAMG;IACI,eAAe,CAAC,EAAuB;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC,YAAa,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB,CAAC,mBAAwC,EAAE,SAAkB;QACrF,MAAM,OAAO,GAAG,IAAI,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC;QACxD,MAAM,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC;QAE7J,sDAAsD;QACtD,IAAI,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,+BAA+B,CAAC;QAE3E,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,0BAA0B,CAAC,CAAC,CAAC;SAChG;QAED,kCAAkC;QAClC,IAAI,gBAAgB,CAAC,MAAM,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;YAC1G,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,gBAAgB,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;SAClG;IACL,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,SAAkB,EAAE,KAAc;QAChD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;YAC9C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YAC1D,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;SAC7D;IACL,CAAC;IAED;;;OAGG;IACI,MAAM;QACT,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;YAC9C,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAExB,4DAA4D;YAC5D,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACrD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YACzD,IAAI,IAAI,CAAC,gCAAgC,EAAE;gBACvC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC1D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACjE;YACD,uEAAuE;YACvE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC1D;IACL,CAAC;IAED,6DAA6D;IACrD,gBAAgB;QACpB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;YAC9C,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YACjC,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;YACzD,IAAI,eAAe,EAAE;gBACjB,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;aACjD;SACJ;IACL,CAAC;IAEO,WAAW,CAAC,OAAgB;QAChC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAEO,uBAAuB,CAAC,mBAAwC,EAAE,OAAgB;QACtF,mBAAmB,CAAC,OAAO,GAAG,OAAO,CAAC;QACtC,IAAI,CAAC,OAAO,EAAE;YACV,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;SACpD;IACL,CAAC;IAED;;;;;;OAMG;IACI,sBAAsB,CAAC,GAA+B;QACzD,mBAAmB;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxD,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE;gBACjD,OAAO,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;aACxC;SACJ;QAED,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrC,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,IAAY;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxD,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;gBAC7C,OAAO,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;aACxC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,OAAO;QACX,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxD,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE;gBACvC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC;aACxE;SACJ;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,IAAI,CAAC,QAAQ,KAAK,gBAAgB,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,EAAE;gBACrF,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC;aACxJ;YAED,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,sBAAsB,EAAE,CAAC;YAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACxD,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE;oBACvC,qEAAqE;oBACrE,oIAAoI;oBACpI,gFAAgF;oBAChF,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,WAAW,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE;wBAC/F,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,iBAAkB,EAAE,CAAC;qBACtD;oBAED,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;wBAC3C,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,WAAY,CAAC,CAAC;qBAC1G;iBACJ;aACJ;SACJ;QAED,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAEO,QAAQ;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAExB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;SAC9D;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxD,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC;SACjD;IACL,CAAC;IAEO,uBAAuB,CAAC,mBAAwC,EAAE,MAAe;QACrF,IAAI,MAAM,EAAE;YACR,OAAO,MAAM,CAAC,cAAc,CAAC;SAChC;aAAM,IAAI,mBAAmB,CAAC,mBAAmB,EAAE;YAChD,IAAI,mBAAmB,CAAC,mBAAmB,CAAC,sBAAsB,EAAE;gBAChE,MAAM,MAAM,GAAG,mBAAmB,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBACtJ,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;aAC9C;iBAAM,IAAI,mBAAmB,CAAC,mBAAmB,CAAC,aAAa,EAAE;gBAC9D,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,aAAa,CAAC;aAChE;iBAAM;gBACH,OAAO,EAAE,CAAC;aACb;SACJ;aAAM;YACH,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;SAClF;IACL,CAAC;IAEO,uBAAuB,CAAC,mBAAwC,EAAE,MAAe;QACrF,4FAA4F;QAC5F,MAAM,eAAe,GAAG,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACrJ,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;QACjG,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,+BAA+B,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE;YACtF,OAAO,EAAE,IAAI,IAAI,CAAC;QACtB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;QAE7B,MAAM,wBAAwB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAChG,IAAI,CAAC,4BAA4B,GAAG,CAAC,wBAAwB,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,eAAe,CAAC;QAEjJ,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACtF,MAAM,cAAc,GAAG,mBAAmB,CAAC,+BAA+B,IAAI,mBAAmB,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC;QACrI,IAAI,OAAO,GAAG,IAAI,CAAC;QAEnB,oDAAoD;QACpD,IAAI,CAAC,MAAM,CAAC,4BAA4B,CAAC,kBAAkB,GAAG,IAAI,CAAC,4BAA4B,IAAI,wBAAwB,CAAC;QAE5H,sCAAsC;QACtC,IAAI,IAAI,CAAC,4BAA4B,IAAI,CAAC,mBAAmB,CAAC,0BAA0B,EAAE;YACtF,mBAAmB,CAAC,wBAAwB,EAAE,CAAC;SAClD;QAED,mEAAmE;QACnE,IAAI,cAAc,EAAE;YAChB,OAAO,GAAG,cAAc,CAAC;SAC5B;aAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE;YAC1C,OAAO,GAAG,mBAAmB,CAAC,0BAA0B,CAAC;SAC5D;aAAM,IAAI,aAAa,EAAE;YACtB,OAAO,GAAG,aAAa,CAAC;SAC3B;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;IAEO,oBAAoB,CAAC,mBAAwC,EAAE,WAAkC;QACrG,IAAI,WAAW,EAAE;YACb,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC;YAC9B,WAAW,CAAC,YAAY,GAAG,mBAAmB,CAAC,YAAa,CAAC;SAChE;QAED,IAAI,mBAAmB,CAAC,kBAAkB,KAAK,WAAW,EAAE;YACxD,IAAI,mBAAmB,CAAC,kBAAkB,EAAE;gBACxC,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;aACpD;YACD,mBAAmB,CAAC,kBAAkB,GAAG,WAAW,CAAC;SACxD;QAED,IAAI,mBAAmB,CAAC,qBAAqB,EAAE;YAC3C,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACnC,mBAAmB,CAAC,qBAAqB,GAAG,KAAK,CAAC;SACrD;IACL,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,mBAAwC;QAClE,IAAI,mBAAmB,CAAC,kBAAkB,EAAE;YACxC,mBAAmB,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;YACxD,mBAAmB,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,CAAC;YACpE,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC;SACjD;IACL,CAAC;IAEO,qBAAqB;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxD,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,oBAAoB,EAAE;gBAC7F,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,mBAAmB,CAAC,aAAsC;QAC9D,IAAI,mBAAmB,GAAG,KAAK,CAAC;QAChC,IAAI,aAAa,EAAE;YACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3C,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,KAAK,4BAA4B,EAAE;oBACnE,mBAAmB,GAAG,IAAI,CAAC;oBAC3B,MAAM;iBACT;aACJ;SACJ;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACK,oBAAoB,CAAC,aAAsC;QAC/D,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;YAC7D,IAAI,aAAa,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;gBACjC,OAAO,aAAa,CAAC,OAAO,CAAC,CAAC;aACjC;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,WAAW;QACd,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;;OAGG;IACK,eAAe,CAAC,KAAe;QACnC,mEAAmE;QACnE,IAAI,CAAC,MAAM,CAAC,0BAA0B,GAAG,KAAK,CAAC;QAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtB,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACnC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBACpD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE3B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;gBAC/D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;gBACnE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;gBAC/D,IAAI,CAAC,QAAQ,EAAE,CAAC;aACnB;YAED,IAAI,IAAI,KAAK,SAAS,CAAC,6BAA6B,EAAE;gBAClD,IAAI,CAAC,MAAM,CAAC,0BAA0B,GAAG,IAAI,CAAC;aACjD;SACJ;IACL,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,OAAO,EAAE,CAAC;SAClB;IACL,CAAC;IAEO,OAAO;QACX,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,4BAA4B,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAEpE,IAAI,IAAI,CAAC,MAAM,CAAC,qBAAqB,IAAI,IAAI,CAAC,MAAM,CAAC,+BAA+B,EAAE;YAClF,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAC3D,aAAa,GAAG,IAAI,CAAC;SACxB;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnD,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;gBACnD,aAAa,GAAG,IAAI,CAAC;aACxB;SACJ;QAED,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;SACtD;QAED,IAAI,aAAa,CAAC;QAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,mBAAmB,EAAE;gBAC3C,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;aACvE;iBAAM;gBACH,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBACxC,IAAI,CAAC,MAAM,EAAE;oBACT,SAAS;iBACZ;gBAED,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC;aACzC;YAED,IAAI,CAAC,aAAa,EAAE;gBAChB,SAAS;aACZ;YAED,aAAa,GAA4B,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE;gBACjE,OAAO,EAAE,IAAI,IAAI,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,IAAI,aAAa,EAAE;gBACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC3C,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;wBAC3C,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;wBAC1D,aAAa,GAAG,IAAI,CAAC;qBACxB;iBACJ;gBAED,IAAI,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,EAAE;oBACzC,IAAI,CAAC,MAAM,CAAC,4BAA4B,CAAC,kBAAkB,GAAG,IAAI,CAAC;iBACtE;aACJ;SACJ;QAED,IAAI,CAAC,+BAA+B,EAAE,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,OAAO,EAAE,CAAC;SAClB;IACL,CAAC;IAEO,+BAA+B;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;SACvD;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACrD,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;SACnC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxD,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE;gBACvC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,OAAQ,EAAE,CAAC;aAC5C;SACJ;IACL,CAAC;;AAp4BD;;GAEG;AACW,6CAA6B,GAA2B,CAAC,CAAC,EAAE,EAAE;IACxE,MAAM,WAAW,CAAC,+BAA+B,CAAC,CAAC;AACvD,CAAC,AAF0C,CAEzC;AAyFF;;GAEG;AACW,8BAAc,GAAG;IAC3B;QACI,OAAO,EAAE,SAAS,CAAC,+BAA+B;QAClD,IAAI,EAAE,SAAS,CAAC,sBAAsB;QACtC,MAAM,EAAE,SAAS,CAAC,kBAAkB;QACpC,IAAI,EAAE,oBAAoB;KAC7B;IACD;QACI,OAAO,EAAE,SAAS,CAAC,6BAA6B;QAChD,IAAI,EAAE,SAAS,CAAC,sBAAsB;QACtC,MAAM,EAAE,SAAS,CAAC,kBAAkB;QACpC,IAAI,EAAE,kBAAkB;KAC3B;IACD;QACI,OAAO,EAAE,SAAS,CAAC,6BAA6B;QAChD,IAAI,EAAE,SAAS,CAAC,wBAAwB;QACxC,MAAM,EAAE,SAAS,CAAC,kBAAkB;QACpC,IAAI,EAAE,kBAAkB;KAC3B;IACD;QACI,OAAO,EAAE,SAAS,CAAC,iCAAiC;QACpD,IAAI,EAAE,SAAS,CAAC,wBAAwB;QACxC,MAAM,EAAE,SAAS,CAAC,kBAAkB;QACpC,IAAI,EAAE,sBAAsB;KAC/B;IACD;QACI,OAAO,EAAE,SAAS,CAAC,0BAA0B;QAC7C,IAAI,EAAE,SAAS,CAAC,sBAAsB;QACtC,MAAM,EAAE,SAAS,CAAC,kBAAkB;QACpC,IAAI,EAAE,eAAe;KACxB;IACD;QACI,OAAO,EAAE,SAAS,CAAC,0BAA0B;QAC7C,IAAI,EAAE,SAAS,CAAC,iBAAiB;QACjC,MAAM,EAAE,SAAS,CAAC,eAAe;QACjC,IAAI,EAAE,eAAe;KACxB;IACD;QACI,OAAO,EAAE,SAAS,CAAC,2BAA2B;QAC9C,IAAI,EAAE,SAAS,CAAC,sBAAsB;QACtC,MAAM,EAAE,SAAS,CAAC,kBAAkB;QACpC,IAAI,EAAE,gBAAgB;KACzB;IACD;QACI,OAAO,EAAE,SAAS,CAAC,gCAAgC;QACnD,IAAI,EAAE,SAAS,CAAC,wBAAwB;QACxC,MAAM,EAAE,SAAS,CAAC,kBAAkB;QACpC,IAAI,EAAE,gBAAgB;KACzB;CACJ,AAjD2B,CAiD1B", "sourcesContent": ["import { PrePassRenderTarget } from \"../Materials/Textures/prePassRenderTarget\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Engine } from \"../Engines/engine\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { PostProcess } from \"../PostProcesses/postProcess\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { _WarnImport } from \"../Misc/devTools\";\r\nimport { Color4 } from \"../Maths/math.color\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { Material } from \"../Materials/material\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { PrePassEffectConfiguration } from \"./prePassEffectConfiguration\";\r\nimport type { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport { GeometryBufferRenderer } from \"../Rendering/geometryBufferRenderer\";\r\n\r\n/**\r\n * Renders a pre pass of the scene\r\n * This means every mesh in the scene will be rendered to a render target texture\r\n * And then this texture will be composited to the rendering canvas with post processes\r\n * It is necessary for effects like subsurface scattering or deferred shading\r\n */\r\nexport class PrePassRenderer {\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _SceneComponentInitialization: (scene: Scene) => void = (_) => {\r\n        throw _WarnImport(\"PrePassRendererSceneComponent\");\r\n    };\r\n\r\n    /**\r\n     * To save performance, we can excluded skinned meshes from the prepass\r\n     */\r\n    public excludedSkinnedMesh: AbstractMesh[] = [];\r\n\r\n    /**\r\n     * Force material to be excluded from the prepass\r\n     * Can be useful when `useGeometryBufferFallback` is set to `true`\r\n     * and you don't want a material to show in the effect.\r\n     */\r\n    public excludedMaterials: Material[] = [];\r\n\r\n    private _scene: Scene;\r\n    private _engine: Engine;\r\n\r\n    /**\r\n     * Number of textures in the multi render target texture where the scene is directly rendered\r\n     */\r\n    public mrtCount: number = 0;\r\n\r\n    private _mrtTypes: number[] = [];\r\n    private _mrtFormats: number[] = [];\r\n    private _mrtLayout: number[] = [];\r\n    private _mrtNames: string[] = [];\r\n    private _textureIndices: number[] = [];\r\n\r\n    private _multiRenderAttachments: number[];\r\n    private _defaultAttachments: number[];\r\n    private _clearAttachments: number[];\r\n    private _clearDepthAttachments: number[];\r\n    private _generateNormalsInWorldSpace = false;\r\n\r\n    /**\r\n     * Indicates if the prepass renderer is generating normals in world space or camera space (default: camera space)\r\n     */\r\n    public get generateNormalsInWorldSpace() {\r\n        return this._generateNormalsInWorldSpace;\r\n    }\r\n\r\n    public set generateNormalsInWorldSpace(value: boolean) {\r\n        if (this._generateNormalsInWorldSpace === value) {\r\n            return;\r\n        }\r\n\r\n        this._generateNormalsInWorldSpace = value;\r\n        this._markAllMaterialsAsPrePassDirty();\r\n    }\r\n\r\n    /**\r\n     * Returns the index of a texture in the multi render target texture array.\r\n     * @param type Texture type\r\n     * @returns The index\r\n     */\r\n    public getIndex(type: number): number {\r\n        return this._textureIndices[type];\r\n    }\r\n\r\n    /**\r\n     * How many samples are used for MSAA of the scene render target\r\n     */\r\n    public get samples() {\r\n        return this.defaultRT.samples;\r\n    }\r\n\r\n    public set samples(n: number) {\r\n        this.defaultRT.samples = n;\r\n    }\r\n\r\n    private _useSpecificClearForDepthTexture = false;\r\n\r\n    /**\r\n     * If set to true (default: false), the depth texture will be cleared with the depth value corresponding to the far plane (1 in normal mode, 0 in reverse depth buffer mode)\r\n     * If set to false, the depth texture is always cleared with 0.\r\n     */\r\n    public get useSpecificClearForDepthTexture() {\r\n        return this._useSpecificClearForDepthTexture;\r\n    }\r\n\r\n    public set useSpecificClearForDepthTexture(value: boolean) {\r\n        if (this._useSpecificClearForDepthTexture === value) {\r\n            return;\r\n        }\r\n\r\n        this._useSpecificClearForDepthTexture = value;\r\n        this._isDirty = true;\r\n    }\r\n\r\n    /**\r\n     * Describes the types and formats of the textures used by the pre-pass renderer\r\n     */\r\n    public static TextureFormats = [\r\n        {\r\n            purpose: Constants.PREPASS_IRRADIANCE_TEXTURE_TYPE,\r\n            type: Constants.TEXTURETYPE_HALF_FLOAT,\r\n            format: Constants.TEXTUREFORMAT_RGBA,\r\n            name: \"prePass_Irradiance\",\r\n        },\r\n        {\r\n            purpose: Constants.PREPASS_POSITION_TEXTURE_TYPE,\r\n            type: Constants.TEXTURETYPE_HALF_FLOAT,\r\n            format: Constants.TEXTUREFORMAT_RGBA,\r\n            name: \"prePass_Position\",\r\n        },\r\n        {\r\n            purpose: Constants.PREPASS_VELOCITY_TEXTURE_TYPE,\r\n            type: Constants.TEXTURETYPE_UNSIGNED_INT,\r\n            format: Constants.TEXTUREFORMAT_RGBA,\r\n            name: \"prePass_Velocity\",\r\n        },\r\n        {\r\n            purpose: Constants.PREPASS_REFLECTIVITY_TEXTURE_TYPE,\r\n            type: Constants.TEXTURETYPE_UNSIGNED_INT,\r\n            format: Constants.TEXTUREFORMAT_RGBA,\r\n            name: \"prePass_Reflectivity\",\r\n        },\r\n        {\r\n            purpose: Constants.PREPASS_COLOR_TEXTURE_TYPE,\r\n            type: Constants.TEXTURETYPE_HALF_FLOAT,\r\n            format: Constants.TEXTUREFORMAT_RGBA,\r\n            name: \"prePass_Color\",\r\n        },\r\n        {\r\n            purpose: Constants.PREPASS_DEPTH_TEXTURE_TYPE,\r\n            type: Constants.TEXTURETYPE_FLOAT,\r\n            format: Constants.TEXTUREFORMAT_R,\r\n            name: \"prePass_Depth\",\r\n        },\r\n        {\r\n            purpose: Constants.PREPASS_NORMAL_TEXTURE_TYPE,\r\n            type: Constants.TEXTURETYPE_HALF_FLOAT,\r\n            format: Constants.TEXTUREFORMAT_RGBA,\r\n            name: \"prePass_Normal\",\r\n        },\r\n        {\r\n            purpose: Constants.PREPASS_ALBEDO_SQRT_TEXTURE_TYPE,\r\n            type: Constants.TEXTURETYPE_UNSIGNED_INT,\r\n            format: Constants.TEXTUREFORMAT_RGBA,\r\n            name: \"prePass_Albedo\",\r\n        },\r\n    ];\r\n\r\n    private _isDirty: boolean = true;\r\n\r\n    /**\r\n     * The render target where the scene is directly rendered\r\n     */\r\n    public defaultRT: PrePassRenderTarget;\r\n\r\n    /**\r\n     * Configuration for prepass effects\r\n     */\r\n    private _effectConfigurations: PrePassEffectConfiguration[] = [];\r\n\r\n    /**\r\n     * @returns the prepass render target for the rendering pass.\r\n     * If we are currently rendering a render target, it returns the PrePassRenderTarget\r\n     * associated with that render target. Otherwise, it returns the scene default PrePassRenderTarget\r\n     */\r\n    public getRenderTarget(): PrePassRenderTarget {\r\n        return this._currentTarget;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Managed by the scene component\r\n     * @param prePassRenderTarget\r\n     */\r\n    public _setRenderTarget(prePassRenderTarget: Nullable<PrePassRenderTarget>): void {\r\n        if (prePassRenderTarget) {\r\n            this._currentTarget = prePassRenderTarget;\r\n        } else {\r\n            this._currentTarget = this.defaultRT;\r\n            this._engine.currentRenderPassId = this._scene.activeCamera?.renderPassId ?? this._currentTarget.renderPassId;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns true if the currently rendered prePassRenderTarget is the one\r\n     * associated with the scene.\r\n     */\r\n    public get currentRTisSceneRT(): boolean {\r\n        return this._currentTarget === this.defaultRT;\r\n    }\r\n\r\n    private _geometryBuffer: Nullable<GeometryBufferRenderer>;\r\n\r\n    /**\r\n     * Prevents the PrePassRenderer from using the GeometryBufferRenderer as a fallback\r\n     */\r\n    public doNotUseGeometryRendererFallback = true;\r\n\r\n    private _refreshGeometryBufferRendererLink() {\r\n        if (!this.doNotUseGeometryRendererFallback) {\r\n            this._geometryBuffer = this._scene.enableGeometryBufferRenderer();\r\n\r\n            if (!this._geometryBuffer) {\r\n                // Not supported\r\n                this.doNotUseGeometryRendererFallback = true;\r\n                return;\r\n            }\r\n\r\n            this._geometryBuffer._linkPrePassRenderer(this);\r\n        } else {\r\n            if (this._geometryBuffer) {\r\n                this._geometryBuffer._unlinkPrePassRenderer();\r\n            }\r\n            this._geometryBuffer = null;\r\n            this._scene.disableGeometryBufferRenderer();\r\n        }\r\n    }\r\n\r\n    private _currentTarget: PrePassRenderTarget;\r\n\r\n    /**\r\n     * All the render targets generated by prepass\r\n     */\r\n    public renderTargets: PrePassRenderTarget[] = [];\r\n\r\n    private readonly _clearColor = new Color4(0, 0, 0, 0);\r\n    private readonly _clearDepthColor = new Color4(1e8, 0, 0, 1); // \"infinity\" value - depth in the depth texture is view.z, not a 0..1 value!\r\n\r\n    private _enabled: boolean = false;\r\n\r\n    private _needsCompositionForThisPass = false;\r\n    private _postProcessesSourceForThisPass: Nullable<PostProcess>[];\r\n\r\n    /**\r\n     * Indicates if the prepass is enabled\r\n     */\r\n    public get enabled() {\r\n        return this._enabled;\r\n    }\r\n\r\n    /**\r\n     * Set to true to disable gamma transform in PrePass.\r\n     * Can be useful in case you already proceed to gamma transform on a material level\r\n     * and your post processes don't need to be in linear color space.\r\n     */\r\n    public disableGammaTransform = false;\r\n\r\n    /**\r\n     * Instantiates a prepass renderer\r\n     * @param scene The scene\r\n     */\r\n    constructor(scene: Scene) {\r\n        this._scene = scene;\r\n        this._engine = scene.getEngine();\r\n\r\n        let type = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n        if (this._engine._caps.textureFloat && this._engine._caps.textureFloatLinearFiltering) {\r\n            type = Constants.TEXTURETYPE_FLOAT;\r\n        } else if (this._engine._caps.textureHalfFloat && this._engine._caps.textureHalfFloatLinearFiltering) {\r\n            type = Constants.TEXTURETYPE_HALF_FLOAT;\r\n        }\r\n\r\n        for (let i = 0; i < PrePassRenderer.TextureFormats.length; ++i) {\r\n            const format = PrePassRenderer.TextureFormats[i].format;\r\n            if (PrePassRenderer.TextureFormats[i].type === Constants.TEXTURETYPE_FLOAT) {\r\n                PrePassRenderer.TextureFormats[Constants.PREPASS_DEPTH_TEXTURE_TYPE].type = type;\r\n                if (\r\n                    (format === Constants.TEXTUREFORMAT_R || format === Constants.TEXTUREFORMAT_RG || format === Constants.TEXTUREFORMAT_RGBA) &&\r\n                    !this._engine._caps.supportFloatTexturesResolve\r\n                ) {\r\n                    // We don't know in advance if the texture will be used as a resolve target, so we revert to half_float if the extension to resolve full float textures is not supported\r\n                    PrePassRenderer.TextureFormats[Constants.PREPASS_DEPTH_TEXTURE_TYPE].type = Constants.TEXTURETYPE_HALF_FLOAT;\r\n                }\r\n            }\r\n        }\r\n\r\n        PrePassRenderer._SceneComponentInitialization(this._scene);\r\n        this.defaultRT = this._createRenderTarget(\"sceneprePassRT\", null);\r\n        this._currentTarget = this.defaultRT;\r\n    }\r\n\r\n    /**\r\n     * Creates a new PrePassRenderTarget\r\n     * This should be the only way to instantiate a `PrePassRenderTarget`\r\n     * @param name Name of the `PrePassRenderTarget`\r\n     * @param renderTargetTexture RenderTarget the `PrePassRenderTarget` will be attached to.\r\n     * Can be `null` if the created `PrePassRenderTarget` is attached to the scene (default framebuffer).\r\n     * @internal\r\n     */\r\n    public _createRenderTarget(name: string, renderTargetTexture: Nullable<RenderTargetTexture>): PrePassRenderTarget {\r\n        const rt = new PrePassRenderTarget(name, renderTargetTexture, { width: this._engine.getRenderWidth(), height: this._engine.getRenderHeight() }, 0, this._scene, {\r\n            generateMipMaps: false,\r\n            generateStencilBuffer: this._engine.isStencilEnable,\r\n            defaultType: Constants.TEXTURETYPE_UNSIGNED_INT,\r\n            types: [],\r\n            drawOnlyOnFirstAttachmentByDefault: true,\r\n        });\r\n\r\n        this.renderTargets.push(rt);\r\n\r\n        if (this._enabled) {\r\n            // The pre-pass renderer is already enabled, so make sure we create the render target with the correct number of textures\r\n            this._update();\r\n        }\r\n\r\n        return rt;\r\n    }\r\n\r\n    /**\r\n     * Indicates if rendering a prepass is supported\r\n     */\r\n    public get isSupported() {\r\n        return this._scene.getEngine().getCaps().drawBuffersExtension;\r\n    }\r\n\r\n    /**\r\n     * Sets the proper output textures to draw in the engine.\r\n     * @param effect The effect that is drawn. It can be or not be compatible with drawing to several output textures.\r\n     * @param subMesh Submesh on which the effect is applied\r\n     */\r\n    public bindAttachmentsForEffect(effect: Effect, subMesh: SubMesh) {\r\n        const material = subMesh.getMaterial();\r\n        const isPrePassCapable = material && material.isPrePassCapable;\r\n        const excluded = material && this.excludedMaterials.indexOf(material) !== -1;\r\n\r\n        if (this.enabled && this._currentTarget.enabled) {\r\n            if (effect._multiTarget && isPrePassCapable && !excluded) {\r\n                this._engine.bindAttachments(this._multiRenderAttachments);\r\n            } else {\r\n                if (this._engine._currentRenderTarget) {\r\n                    this._engine.bindAttachments(this._defaultAttachments);\r\n                } else {\r\n                    this._engine.restoreSingleAttachment();\r\n                }\r\n\r\n                if (this._geometryBuffer && this.currentRTisSceneRT && !excluded) {\r\n                    this._geometryBuffer.renderList!.push(subMesh.getRenderingMesh());\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private _reinitializeAttachments() {\r\n        const multiRenderLayout = [];\r\n        const clearLayout = [false];\r\n        const clearDepthLayout = [false];\r\n        const defaultLayout = [true];\r\n\r\n        for (let i = 0; i < this.mrtCount; i++) {\r\n            multiRenderLayout.push(true);\r\n\r\n            if (i > 0) {\r\n                if (this._useSpecificClearForDepthTexture && this._mrtLayout[i] === Constants.PREPASS_DEPTH_TEXTURE_TYPE) {\r\n                    clearLayout.push(false);\r\n                    clearDepthLayout.push(true);\r\n                } else {\r\n                    clearLayout.push(true);\r\n                    clearDepthLayout.push(false);\r\n                }\r\n                defaultLayout.push(false);\r\n            }\r\n        }\r\n\r\n        this._multiRenderAttachments = this._engine.buildTextureLayout(multiRenderLayout);\r\n        this._clearAttachments = this._engine.buildTextureLayout(clearLayout);\r\n        this._clearDepthAttachments = this._engine.buildTextureLayout(clearDepthLayout);\r\n        this._defaultAttachments = this._engine.buildTextureLayout(defaultLayout);\r\n    }\r\n\r\n    private _resetLayout() {\r\n        for (let i = 0; i < PrePassRenderer.TextureFormats.length; i++) {\r\n            this._textureIndices[PrePassRenderer.TextureFormats[i].purpose] = -1;\r\n        }\r\n\r\n        this._textureIndices[Constants.PREPASS_COLOR_TEXTURE_TYPE] = 0;\r\n        this._mrtLayout = [Constants.PREPASS_COLOR_TEXTURE_TYPE];\r\n        this._mrtTypes = [PrePassRenderer.TextureFormats[Constants.PREPASS_COLOR_TEXTURE_TYPE].type];\r\n        this._mrtFormats = [PrePassRenderer.TextureFormats[Constants.PREPASS_COLOR_TEXTURE_TYPE].format];\r\n        this._mrtNames = [PrePassRenderer.TextureFormats[Constants.PREPASS_COLOR_TEXTURE_TYPE].name];\r\n        this.mrtCount = 1;\r\n    }\r\n\r\n    private _updateGeometryBufferLayout() {\r\n        this._refreshGeometryBufferRendererLink();\r\n\r\n        if (this._geometryBuffer) {\r\n            this._geometryBuffer._resetLayout();\r\n\r\n            const texturesActivated = [];\r\n\r\n            for (let i = 0; i < this._mrtLayout.length; i++) {\r\n                texturesActivated.push(false);\r\n            }\r\n\r\n            this._geometryBuffer._linkInternalTexture(this.defaultRT.getInternalTexture()!);\r\n\r\n            const matches = [\r\n                {\r\n                    prePassConstant: Constants.PREPASS_DEPTH_TEXTURE_TYPE,\r\n                    geometryBufferConstant: GeometryBufferRenderer.DEPTH_TEXTURE_TYPE,\r\n                },\r\n                {\r\n                    prePassConstant: Constants.PREPASS_NORMAL_TEXTURE_TYPE,\r\n                    geometryBufferConstant: GeometryBufferRenderer.NORMAL_TEXTURE_TYPE,\r\n                },\r\n                {\r\n                    prePassConstant: Constants.PREPASS_POSITION_TEXTURE_TYPE,\r\n                    geometryBufferConstant: GeometryBufferRenderer.POSITION_TEXTURE_TYPE,\r\n                },\r\n                {\r\n                    prePassConstant: Constants.PREPASS_REFLECTIVITY_TEXTURE_TYPE,\r\n                    geometryBufferConstant: GeometryBufferRenderer.REFLECTIVITY_TEXTURE_TYPE,\r\n                },\r\n                {\r\n                    prePassConstant: Constants.PREPASS_VELOCITY_TEXTURE_TYPE,\r\n                    geometryBufferConstant: GeometryBufferRenderer.VELOCITY_TEXTURE_TYPE,\r\n                },\r\n            ];\r\n\r\n            // replace textures in the geometryBuffer RT\r\n            for (let i = 0; i < matches.length; i++) {\r\n                const index = this._mrtLayout.indexOf(matches[i].prePassConstant);\r\n                if (index !== -1) {\r\n                    this._geometryBuffer._forceTextureType(matches[i].geometryBufferConstant, index);\r\n                    texturesActivated[index] = true;\r\n                }\r\n            }\r\n\r\n            this._geometryBuffer._setAttachments(this._engine.buildTextureLayout(texturesActivated));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Restores attachments for single texture draw.\r\n     */\r\n    public restoreAttachments() {\r\n        if (this.enabled && this._currentTarget.enabled && this._defaultAttachments) {\r\n            if (this._engine._currentRenderTarget) {\r\n                this._engine.bindAttachments(this._defaultAttachments);\r\n            } else {\r\n                this._engine.restoreSingleAttachment();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public _beforeDraw(camera?: Camera, faceIndex?: number, layer?: number) {\r\n        // const previousEnabled = this._enabled && this._currentTarget.enabled;\r\n\r\n        if (this._isDirty) {\r\n            this._update();\r\n        }\r\n\r\n        if (!this._enabled || !this._currentTarget.enabled) {\r\n            return;\r\n        }\r\n\r\n        if (this._geometryBuffer) {\r\n            this._geometryBuffer.renderList = [];\r\n        }\r\n\r\n        this._setupOutputForThisPass(this._currentTarget, camera);\r\n    }\r\n\r\n    private _prepareFrame(prePassRenderTarget: PrePassRenderTarget, faceIndex?: number, layer?: number) {\r\n        if (prePassRenderTarget.renderTargetTexture) {\r\n            prePassRenderTarget.renderTargetTexture._prepareFrame(this._scene, faceIndex, layer, prePassRenderTarget.renderTargetTexture.useCameraPostProcesses);\r\n        } else if (this._postProcessesSourceForThisPass.length) {\r\n            this._scene.postProcessManager._prepareFrame();\r\n        } else {\r\n            this._engine.restoreDefaultFramebuffer();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets an intermediary texture between prepass and postprocesses. This texture\r\n     * will be used as input for post processes\r\n     * @param rt The render target texture to use\r\n     * @returns true if there are postprocesses that will use this texture,\r\n     * false if there is no postprocesses - and the function has no effect\r\n     */\r\n    public setCustomOutput(rt: RenderTargetTexture) {\r\n        const firstPP = this._postProcessesSourceForThisPass[0];\r\n        if (!firstPP) {\r\n            return false;\r\n        }\r\n\r\n        firstPP.inputTexture = rt.renderTarget!;\r\n\r\n        return true;\r\n    }\r\n\r\n    private _renderPostProcesses(prePassRenderTarget: PrePassRenderTarget, faceIndex?: number) {\r\n        const firstPP = this._postProcessesSourceForThisPass[0];\r\n        const outputTexture = firstPP ? firstPP.inputTexture : prePassRenderTarget.renderTargetTexture ? prePassRenderTarget.renderTargetTexture.renderTarget : null;\r\n\r\n        // Build post process chain for this prepass post draw\r\n        let postProcessChain = this._currentTarget._beforeCompositionPostProcesses;\r\n\r\n        if (this._needsCompositionForThisPass) {\r\n            postProcessChain = postProcessChain.concat([this._currentTarget.imageProcessingPostProcess]);\r\n        }\r\n\r\n        // Activates and renders the chain\r\n        if (postProcessChain.length) {\r\n            this._scene.postProcessManager._prepareFrame(this._currentTarget.renderTarget?.texture, postProcessChain);\r\n            this._scene.postProcessManager.directRender(postProcessChain, outputTexture, false, faceIndex);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _afterDraw(faceIndex?: number, layer?: number) {\r\n        if (this._enabled && this._currentTarget.enabled) {\r\n            this._prepareFrame(this._currentTarget, faceIndex, layer);\r\n            this._renderPostProcesses(this._currentTarget, faceIndex);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clears the current prepass render target (in the sense of settings pixels to the scene clear color value)\r\n     * @internal\r\n     */\r\n    public _clear() {\r\n        if (this._enabled && this._currentTarget.enabled) {\r\n            this._bindFrameBuffer();\r\n\r\n            // Clearing other attachment with 0 on all other attachments\r\n            this._engine.bindAttachments(this._clearAttachments);\r\n            this._engine.clear(this._clearColor, true, false, false);\r\n            if (this._useSpecificClearForDepthTexture) {\r\n                this._engine.bindAttachments(this._clearDepthAttachments);\r\n                this._engine.clear(this._clearDepthColor, true, false, false);\r\n            }\r\n            // Regular clear color with the scene clear color of the 1st attachment\r\n            this._engine.bindAttachments(this._defaultAttachments);\r\n        }\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _bindFrameBuffer() {\r\n        if (this._enabled && this._currentTarget.enabled) {\r\n            this._currentTarget._checkSize();\r\n            const internalTexture = this._currentTarget.renderTarget;\r\n            if (internalTexture) {\r\n                this._engine.bindFramebuffer(internalTexture);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _setEnabled(enabled: boolean) {\r\n        this._enabled = enabled;\r\n    }\r\n\r\n    private _setRenderTargetEnabled(prePassRenderTarget: PrePassRenderTarget, enabled: boolean) {\r\n        prePassRenderTarget.enabled = enabled;\r\n        if (!enabled) {\r\n            this._unlinkInternalTexture(prePassRenderTarget);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds an effect configuration to the prepass render target.\r\n     * If an effect has already been added, it won't add it twice and will return the configuration\r\n     * already present.\r\n     * @param cfg the effect configuration\r\n     * @returns the effect configuration now used by the prepass\r\n     */\r\n    public addEffectConfiguration(cfg: PrePassEffectConfiguration): PrePassEffectConfiguration {\r\n        // Do not add twice\r\n        for (let i = 0; i < this._effectConfigurations.length; i++) {\r\n            if (this._effectConfigurations[i].name === cfg.name) {\r\n                return this._effectConfigurations[i];\r\n            }\r\n        }\r\n\r\n        this._effectConfigurations.push(cfg);\r\n        return cfg;\r\n    }\r\n\r\n    /**\r\n     * Retrieves an effect configuration by name\r\n     * @param name the name of the effect configuration\r\n     * @returns the effect configuration, or null if not present\r\n     */\r\n    public getEffectConfiguration(name: string): Nullable<PrePassEffectConfiguration> {\r\n        for (let i = 0; i < this._effectConfigurations.length; i++) {\r\n            if (this._effectConfigurations[i].name === name) {\r\n                return this._effectConfigurations[i];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private _enable() {\r\n        const previousMrtCount = this.mrtCount;\r\n\r\n        for (let i = 0; i < this._effectConfigurations.length; i++) {\r\n            if (this._effectConfigurations[i].enabled) {\r\n                this._enableTextures(this._effectConfigurations[i].texturesRequired);\r\n            }\r\n        }\r\n\r\n        for (let i = 0; i < this.renderTargets.length; i++) {\r\n            if (this.mrtCount !== previousMrtCount || this.renderTargets[i].count !== this.mrtCount) {\r\n                this.renderTargets[i].updateCount(this.mrtCount, { types: this._mrtTypes, formats: this._mrtFormats }, this._mrtNames.concat(\"prePass_DepthBuffer\"));\r\n            }\r\n\r\n            this.renderTargets[i]._resetPostProcessChain();\r\n\r\n            for (let j = 0; j < this._effectConfigurations.length; j++) {\r\n                if (this._effectConfigurations[j].enabled) {\r\n                    // TODO : subsurface scattering has 1 scene-wide effect configuration\r\n                    // solution : do not stock postProcess on effectConfiguration, but in the prepassRenderTarget (hashmap configuration => postProcess)\r\n                    // And call createPostProcess whenever the post process does not exist in the RT\r\n                    if (!this._effectConfigurations[j].postProcess && this._effectConfigurations[j].createPostProcess) {\r\n                        this._effectConfigurations[j].createPostProcess!();\r\n                    }\r\n\r\n                    if (this._effectConfigurations[j].postProcess) {\r\n                        this.renderTargets[i]._beforeCompositionPostProcesses.push(this._effectConfigurations[j].postProcess!);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        this._reinitializeAttachments();\r\n        this._setEnabled(true);\r\n        this._updateGeometryBufferLayout();\r\n    }\r\n\r\n    private _disable() {\r\n        this._setEnabled(false);\r\n\r\n        for (let i = 0; i < this.renderTargets.length; i++) {\r\n            this._setRenderTargetEnabled(this.renderTargets[i], false);\r\n        }\r\n\r\n        this._resetLayout();\r\n\r\n        for (let i = 0; i < this._effectConfigurations.length; i++) {\r\n            this._effectConfigurations[i].enabled = false;\r\n        }\r\n    }\r\n\r\n    private _getPostProcessesSource(prePassRenderTarget: PrePassRenderTarget, camera?: Camera): Nullable<PostProcess>[] {\r\n        if (camera) {\r\n            return camera._postProcesses;\r\n        } else if (prePassRenderTarget.renderTargetTexture) {\r\n            if (prePassRenderTarget.renderTargetTexture.useCameraPostProcesses) {\r\n                const camera = prePassRenderTarget.renderTargetTexture.activeCamera ? prePassRenderTarget.renderTargetTexture.activeCamera : this._scene.activeCamera;\r\n                return camera ? camera._postProcesses : [];\r\n            } else if (prePassRenderTarget.renderTargetTexture.postProcesses) {\r\n                return prePassRenderTarget.renderTargetTexture.postProcesses;\r\n            } else {\r\n                return [];\r\n            }\r\n        } else {\r\n            return this._scene.activeCamera ? this._scene.activeCamera._postProcesses : [];\r\n        }\r\n    }\r\n\r\n    private _setupOutputForThisPass(prePassRenderTarget: PrePassRenderTarget, camera?: Camera) {\r\n        // Order is : draw ===> prePassRenderTarget._postProcesses ==> ipp ==> camera._postProcesses\r\n        const secondaryCamera = camera && this._scene.activeCameras && !!this._scene.activeCameras.length && this._scene.activeCameras.indexOf(camera) !== 0;\r\n        this._postProcessesSourceForThisPass = this._getPostProcessesSource(prePassRenderTarget, camera);\r\n        this._postProcessesSourceForThisPass = this._postProcessesSourceForThisPass.filter((pp) => {\r\n            return pp != null;\r\n        });\r\n        this._scene.autoClear = true;\r\n\r\n        const cameraHasImageProcessing = this._hasImageProcessing(this._postProcessesSourceForThisPass);\r\n        this._needsCompositionForThisPass = !cameraHasImageProcessing && !this.disableGammaTransform && this._needsImageProcessing() && !secondaryCamera;\r\n\r\n        const firstCameraPP = this._getFirstPostProcess(this._postProcessesSourceForThisPass);\r\n        const firstPrePassPP = prePassRenderTarget._beforeCompositionPostProcesses && prePassRenderTarget._beforeCompositionPostProcesses[0];\r\n        let firstPP = null;\r\n\r\n        // Setting the scene-wide post process configuration\r\n        this._scene.imageProcessingConfiguration.applyByPostProcess = this._needsCompositionForThisPass || cameraHasImageProcessing;\r\n\r\n        // Create composition effect if needed\r\n        if (this._needsCompositionForThisPass && !prePassRenderTarget.imageProcessingPostProcess) {\r\n            prePassRenderTarget._createCompositionEffect();\r\n        }\r\n\r\n        // Setting the prePassRenderTarget as input texture of the first PP\r\n        if (firstPrePassPP) {\r\n            firstPP = firstPrePassPP;\r\n        } else if (this._needsCompositionForThisPass) {\r\n            firstPP = prePassRenderTarget.imageProcessingPostProcess;\r\n        } else if (firstCameraPP) {\r\n            firstPP = firstCameraPP;\r\n        }\r\n\r\n        this._bindFrameBuffer();\r\n        this._linkInternalTexture(prePassRenderTarget, firstPP);\r\n    }\r\n\r\n    private _linkInternalTexture(prePassRenderTarget: PrePassRenderTarget, postProcess: Nullable<PostProcess>) {\r\n        if (postProcess) {\r\n            postProcess.autoClear = false;\r\n            postProcess.inputTexture = prePassRenderTarget.renderTarget!;\r\n        }\r\n\r\n        if (prePassRenderTarget._outputPostProcess !== postProcess) {\r\n            if (prePassRenderTarget._outputPostProcess) {\r\n                this._unlinkInternalTexture(prePassRenderTarget);\r\n            }\r\n            prePassRenderTarget._outputPostProcess = postProcess;\r\n        }\r\n\r\n        if (prePassRenderTarget._internalTextureDirty) {\r\n            this._updateGeometryBufferLayout();\r\n            prePassRenderTarget._internalTextureDirty = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _unlinkInternalTexture(prePassRenderTarget: PrePassRenderTarget) {\r\n        if (prePassRenderTarget._outputPostProcess) {\r\n            prePassRenderTarget._outputPostProcess.autoClear = true;\r\n            prePassRenderTarget._outputPostProcess.restoreDefaultInputTexture();\r\n            prePassRenderTarget._outputPostProcess = null;\r\n        }\r\n    }\r\n\r\n    private _needsImageProcessing(): boolean {\r\n        for (let i = 0; i < this._effectConfigurations.length; i++) {\r\n            if (this._effectConfigurations[i].enabled && this._effectConfigurations[i].needsImageProcessing) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    private _hasImageProcessing(postProcesses: Nullable<PostProcess>[]): boolean {\r\n        let isIPPAlreadyPresent = false;\r\n        if (postProcesses) {\r\n            for (let i = 0; i < postProcesses.length; i++) {\r\n                if (postProcesses[i]?.getClassName() === \"ImageProcessingPostProcess\") {\r\n                    isIPPAlreadyPresent = true;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n\r\n        return isIPPAlreadyPresent;\r\n    }\r\n\r\n    /**\r\n     * Internal, gets the first post proces.\r\n     * @param postProcesses\r\n     * @returns the first post process to be run on this camera.\r\n     */\r\n    private _getFirstPostProcess(postProcesses: Nullable<PostProcess>[]): Nullable<PostProcess> {\r\n        for (let ppIndex = 0; ppIndex < postProcesses.length; ppIndex++) {\r\n            if (postProcesses[ppIndex] !== null) {\r\n                return postProcesses[ppIndex];\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Marks the prepass renderer as dirty, triggering a check if the prepass is necessary for the next rendering.\r\n     */\r\n    public markAsDirty() {\r\n        this._isDirty = true;\r\n    }\r\n\r\n    /**\r\n     * Enables a texture on the MultiRenderTarget for prepass\r\n     * @param types\r\n     */\r\n    private _enableTextures(types: number[]) {\r\n        // For velocity : enable storage of previous matrices for instances\r\n        this._scene.needsPreviousWorldMatrices = false;\r\n\r\n        for (let i = 0; i < types.length; i++) {\r\n            const type = types[i];\r\n\r\n            if (this._textureIndices[type] === -1) {\r\n                this._textureIndices[type] = this._mrtLayout.length;\r\n                this._mrtLayout.push(type);\r\n\r\n                this._mrtTypes.push(PrePassRenderer.TextureFormats[type].type);\r\n                this._mrtFormats.push(PrePassRenderer.TextureFormats[type].format);\r\n                this._mrtNames.push(PrePassRenderer.TextureFormats[type].name);\r\n                this.mrtCount++;\r\n            }\r\n\r\n            if (type === Constants.PREPASS_VELOCITY_TEXTURE_TYPE) {\r\n                this._scene.needsPreviousWorldMatrices = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Makes sure that the prepass renderer is up to date if it has been dirtified.\r\n     */\r\n    public update() {\r\n        if (this._isDirty) {\r\n            this._update();\r\n        }\r\n    }\r\n\r\n    private _update() {\r\n        this._disable();\r\n        let enablePrePass = false;\r\n        this._scene.imageProcessingConfiguration.applyByPostProcess = false;\r\n\r\n        if (this._scene._depthPeelingRenderer && this._scene.useOrderIndependentTransparency) {\r\n            this._scene._depthPeelingRenderer.setPrePassRenderer(this);\r\n            enablePrePass = true;\r\n        }\r\n\r\n        for (let i = 0; i < this._scene.materials.length; i++) {\r\n            if (this._scene.materials[i].setPrePassRenderer(this)) {\r\n                enablePrePass = true;\r\n            }\r\n        }\r\n\r\n        if (enablePrePass) {\r\n            this._setRenderTargetEnabled(this.defaultRT, true);\r\n        }\r\n\r\n        let postProcesses;\r\n\r\n        for (let i = 0; i < this.renderTargets.length; i++) {\r\n            if (this.renderTargets[i].renderTargetTexture) {\r\n                postProcesses = this._getPostProcessesSource(this.renderTargets[i]);\r\n            } else {\r\n                const camera = this._scene.activeCamera;\r\n                if (!camera) {\r\n                    continue;\r\n                }\r\n\r\n                postProcesses = camera._postProcesses;\r\n            }\r\n\r\n            if (!postProcesses) {\r\n                continue;\r\n            }\r\n\r\n            postProcesses = <Nullable<PostProcess[]>>postProcesses.filter((pp) => {\r\n                return pp != null;\r\n            });\r\n\r\n            if (postProcesses) {\r\n                for (let j = 0; j < postProcesses.length; j++) {\r\n                    if (postProcesses[j].setPrePassRenderer(this)) {\r\n                        this._setRenderTargetEnabled(this.renderTargets[i], true);\r\n                        enablePrePass = true;\r\n                    }\r\n                }\r\n\r\n                if (this._hasImageProcessing(postProcesses)) {\r\n                    this._scene.imageProcessingConfiguration.applyByPostProcess = true;\r\n                }\r\n            }\r\n        }\r\n\r\n        this._markAllMaterialsAsPrePassDirty();\r\n        this._isDirty = false;\r\n\r\n        if (enablePrePass) {\r\n            this._enable();\r\n        }\r\n    }\r\n\r\n    private _markAllMaterialsAsPrePassDirty() {\r\n        const materials = this._scene.materials;\r\n\r\n        for (let i = 0; i < materials.length; i++) {\r\n            materials[i].markAsDirty(Material.PrePassDirtyFlag);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disposes the prepass renderer.\r\n     */\r\n    public dispose() {\r\n        for (let i = this.renderTargets.length - 1; i >= 0; i--) {\r\n            this.renderTargets[i].dispose();\r\n        }\r\n\r\n        for (let i = 0; i < this._effectConfigurations.length; i++) {\r\n            if (this._effectConfigurations[i].dispose) {\r\n                this._effectConfigurations[i].dispose!();\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}