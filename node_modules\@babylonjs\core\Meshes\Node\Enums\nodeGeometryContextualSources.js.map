{"version": 3, "file": "nodeGeometryContextualSources.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Meshes/Node/Enums/nodeGeometryContextualSources.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,CAAN,IAAY,6BAmCX;AAnCD,WAAY,6BAA6B;IACrC,WAAW;IACX,iFAAa,CAAA;IACb,gBAAgB;IAChB,2FAAkB,CAAA;IAClB,cAAc;IACd,uFAAgB,CAAA;IAChB,eAAe;IACf,yFAAiB,CAAA;IACjB,SAAS;IACT,6EAAW,CAAA;IACX,UAAU;IACV,+EAAY,CAAA;IACZ,UAAU;IACV,+EAAY,CAAA;IACZ,UAAU;IACV,+EAAY,CAAA;IACZ,UAAU;IACV,+EAAY,CAAA;IACZ,UAAU;IACV,+EAAY,CAAA;IACZ,aAAa;IACb,sFAAe,CAAA;IACf,eAAe;IACf,0FAAiB,CAAA;IACjB,aAAa;IACb,sFAAe,CAAA;IACf,iBAAiB;IACjB,8FAAmB,CAAA;IACnB,mBAAmB;IACnB,kGAAqB,CAAA;IACrB,aAAa;IACb,sFAAe,CAAA;IACf,iBAAiB;IACjB,8FAAmB,CAAA;AACvB,CAAC,EAnCW,6BAA6B,KAA7B,6BAA6B,QAmCxC", "sourcesContent": ["/**\r\n * Defines the kind of contextual sources for node geometry\r\n */\r\nexport enum NodeGeometryContextualSources {\r\n    /** None */\r\n    None = 0x0000,\r\n    /** Positions */\r\n    Positions = 0x0001,\r\n    /** Normals */\r\n    Normals = 0x0002,\r\n    /** Tangents */\r\n    Tangents = 0x0003,\r\n    /** UV */\r\n    UV = 0x0004,\r\n    /** UV2 */\r\n    UV2 = 0x0005,\r\n    /** UV3 */\r\n    UV3 = 0x0006,\r\n    /** UV4 */\r\n    UV4 = 0x0007,\r\n    /** UV5 */\r\n    UV5 = 0x0008,\r\n    /** UV6 */\r\n    UV6 = 0x0009,\r\n    /** Colors */\r\n    Colors = 0x000a,\r\n    /** VertexID */\r\n    VertexID = 0x000b,\r\n    /** FaceID */\r\n    FaceID = 0x000c,\r\n    /** GeometryID */\r\n    GeometryID = 0x000d,\r\n    /** CollectionID */\r\n    CollectionID = 0x000e,\r\n    /** LoopID */\r\n    LoopID = 0x000f,\r\n    /** InstanceID */\r\n    InstanceID = 0x0010,\r\n}\r\n"]}