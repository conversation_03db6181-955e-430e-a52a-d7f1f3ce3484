{"version": 3, "file": "rawCubeTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/rawCubeTexture.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AAC1E,OAAO,EAAE,gBAAgB,IAAI,uBAAuB,EAAE,MAAM,oCAAoC,CAAC;AAIjG,OAAO,EAAE,qBAAqB,EAAE,MAAM,mBAAmB,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,4CAA4C,CAAC;AAGpD;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,WAAW;IAC3C;;;;;;;;;;;OAWG;IACH,YACI,KAAY,EACZ,IAAiC,EACjC,IAAY,EACZ,SAAiB,SAAS,CAAC,kBAAkB,EAC7C,OAAe,SAAS,CAAC,wBAAwB,EACjD,kBAA2B,KAAK,EAChC,UAAmB,KAAK,EACxB,eAAuB,SAAS,CAAC,8BAA8B,EAC/D,cAAgC,IAAI;QAEpC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAEjB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;IAC1I,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,IAAuB,EAAE,MAAc,EAAE,IAAY,EAAE,OAAgB,EAAE,cAAgC,IAAI;QACtH,IAAI,CAAC,QAAS,CAAC,SAAS,EAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;IAC1H,CAAC;IAED;;;;;;;OAOG;IACI,eAAe,CAAC,IAAyB,EAAE,sBAAqD,IAAI,EAAE,WAAmB,GAAG,EAAE,YAAoB,CAAC;QACtJ,OAAO,uBAAuB,CAAC,IAAI,CAAC,QAAS,EAAE,IAAI,EAAE,mBAAmB,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAClH,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,OAAO,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE;YAClC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAG,CAAC;YAC/B,MAAM,eAAe,GAAG,IAAI,CAAC,QAAS,CAAC;YAEvC,MAAM,OAAO,GAAG,IAAI,cAAc,CAC9B,KAAK,EACL,eAAe,CAAC,gBAAiB,EACjC,eAAe,CAAC,KAAK,EACrB,eAAe,CAAC,MAAM,EACtB,eAAe,CAAC,IAAI,EACpB,eAAe,CAAC,eAAe,EAC/B,eAAe,CAAC,OAAO,EACvB,eAAe,CAAC,YAAY,EAC5B,eAAe,CAAC,YAAY,CAC/B,CAAC;YAEF,IAAI,eAAe,CAAC,MAAM,KAAK,qBAAqB,CAAC,WAAW,EAAE;gBAC9D,OAAO,CAAC,eAAe,CACnB,eAAe,CAAC,qBAAsB,EACtC,eAAe,CAAC,oBAAoB,EACpC,eAAe,CAAC,mBAAmB,EACnC,eAAe,CAAC,oBAAoB,CACvC,CAAC;aACL;YAED,OAAO,OAAO,CAAC;QACnB,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;CACJ", "sourcesContent": ["import { Serial<PERSON>Helper } from \"../../Misc/decorators.serialization\";\r\nimport { _UpdateRGBDAsync as UpdateRGBDAsyncEnvTools } from \"../../Misc/environmentTextureTools\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { SphericalPolynomial } from \"../../Maths/sphericalPolynomial\";\r\nimport { InternalTextureSource } from \"./internalTexture\";\r\nimport { CubeTexture } from \"./cubeTexture\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport \"../../Engines/Extensions/engine.rawTexture\";\r\nimport type { Engine } from \"../../Engines/engine\";\r\n\r\n/**\r\n * Raw cube texture where the raw buffers are passed in\r\n */\r\nexport class RawCubeTexture extends CubeTexture {\r\n    /**\r\n     * Creates a cube texture where the raw buffers are passed in.\r\n     * @param scene defines the scene the texture is attached to\r\n     * @param data defines the array of data to use to create each face\r\n     * @param size defines the size of the textures\r\n     * @param format defines the format of the data\r\n     * @param type defines the type of the data (like Engine.TEXTURETYPE_UNSIGNED_INT)\r\n     * @param generateMipMaps  defines if the engine should generate the mip levels\r\n     * @param invertY defines if data must be stored with Y axis inverted\r\n     * @param samplingMode defines the required sampling mode (like Texture.NEAREST_SAMPLINGMODE)\r\n     * @param compression defines the compression used (null by default)\r\n     */\r\n    constructor(\r\n        scene: Scene,\r\n        data: Nullable<ArrayBufferView[]>,\r\n        size: number,\r\n        format: number = Constants.TEXTUREFORMAT_RGBA,\r\n        type: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        generateMipMaps: boolean = false,\r\n        invertY: boolean = false,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        compression: Nullable<string> = null\r\n    ) {\r\n        super(\"\", scene);\r\n\r\n        this._texture = scene.getEngine().createRawCubeTexture(data, size, format, type, generateMipMaps, invertY, samplingMode, compression);\r\n    }\r\n\r\n    /**\r\n     * Updates the raw cube texture.\r\n     * @param data defines the data to store\r\n     * @param format defines the data format\r\n     * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_INT by default)\r\n     * @param invertY defines if data must be stored with Y axis inverted\r\n     * @param compression defines the compression used (null by default)\r\n     */\r\n    public update(data: ArrayBufferView[], format: number, type: number, invertY: boolean, compression: Nullable<string> = null): void {\r\n        (this._texture!.getEngine() as Engine).updateRawCubeTexture(this._texture!, data, format, type, invertY, compression);\r\n    }\r\n\r\n    /**\r\n     * Updates a raw cube texture with RGBD encoded data.\r\n     * @param data defines the array of data [mipmap][face] to use to create each face\r\n     * @param sphericalPolynomial defines the spherical polynomial for irradiance\r\n     * @param lodScale defines the scale applied to environment texture. This manages the range of LOD level used for IBL according to the roughness\r\n     * @param lodOffset defines the offset applied to environment texture. This manages first LOD level used for IBL according to the roughness\r\n     * @returns a promise that resolves when the operation is complete\r\n     */\r\n    public updateRGBDAsync(data: ArrayBufferView[][], sphericalPolynomial: Nullable<SphericalPolynomial> = null, lodScale: number = 0.8, lodOffset: number = 0): Promise<void> {\r\n        return UpdateRGBDAsyncEnvTools(this._texture!, data, sphericalPolynomial, lodScale, lodOffset).then(() => {});\r\n    }\r\n\r\n    /**\r\n     * Clones the raw cube texture.\r\n     * @returns a new cube texture\r\n     */\r\n    public clone(): CubeTexture {\r\n        return SerializationHelper.Clone(() => {\r\n            const scene = this.getScene()!;\r\n            const internalTexture = this._texture!;\r\n\r\n            const texture = new RawCubeTexture(\r\n                scene,\r\n                internalTexture._bufferViewArray!,\r\n                internalTexture.width,\r\n                internalTexture.format,\r\n                internalTexture.type,\r\n                internalTexture.generateMipMaps,\r\n                internalTexture.invertY,\r\n                internalTexture.samplingMode,\r\n                internalTexture._compression\r\n            );\r\n\r\n            if (internalTexture.source === InternalTextureSource.CubeRawRGBD) {\r\n                texture.updateRGBDAsync(\r\n                    internalTexture._bufferViewArrayArray!,\r\n                    internalTexture._sphericalPolynomial,\r\n                    internalTexture._lodGenerationScale,\r\n                    internalTexture._lodGenerationOffset\r\n                );\r\n            }\r\n\r\n            return texture;\r\n        }, this);\r\n    }\r\n}\r\n"]}