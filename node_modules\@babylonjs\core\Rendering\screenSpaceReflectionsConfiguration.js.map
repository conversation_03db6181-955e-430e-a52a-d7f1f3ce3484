{"version": 3, "file": "screenSpaceReflectionsConfiguration.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Rendering/screenSpaceReflectionsConfiguration.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAGjD;;;GAGG;AACH,MAAM,OAAO,mCAAmC;IAAhD;QACI;;WAEG;QACI,YAAO,GAAG,KAAK,CAAC;QAEvB;;WAEG;QACI,SAAI,GAAG,wBAAwB,CAAC;QAEvC;;WAEG;QACa,qBAAgB,GAAa,CAAC,SAAS,CAAC,2BAA2B,EAAE,SAAS,CAAC,iCAAiC,EAAE,SAAS,CAAC,6BAA6B,CAAC,CAAC;IAC/K,CAAC;CAAA", "sourcesContent": ["import { Constants } from \"../Engines/constants\";\r\nimport type { PrePassEffectConfiguration } from \"./prePassEffectConfiguration\";\r\n\r\n/**\r\n * Contains all parameters needed for the prepass to perform\r\n * screen space reflections\r\n */\r\nexport class ScreenSpaceReflectionsConfiguration implements PrePassEffectConfiguration {\r\n    /**\r\n     * Is ssr enabled\r\n     */\r\n    public enabled = false;\r\n\r\n    /**\r\n     * Name of the configuration\r\n     */\r\n    public name = \"screenSpaceReflections\";\r\n\r\n    /**\r\n     * Textures that should be present in the MRT for this effect to work\r\n     */\r\n    public readonly texturesRequired: number[] = [Constants.PREPASS_NORMAL_TEXTURE_TYPE, Constants.PREPASS_REFLECTIVITY_TEXTURE_TYPE, Constants.PREPASS_POSITION_TEXTURE_TYPE];\r\n}\r\n"]}