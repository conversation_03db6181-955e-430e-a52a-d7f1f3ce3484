{"version": 3, "file": "teleportOutBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Meshes/Node/Blocks/Teleport/teleportOutBlock.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,qCAAqC,EAAE,MAAM,8CAA8C,CAAC;AACrG,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAK5D;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,iBAAiB;IAMnD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAVhB,gBAAgB;QACT,gBAAW,GAA8B,IAAI,CAAC;QACrD,gBAAgB;QACT,4BAAuB,GAAqB,IAAI,CAAC;QASpD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,YAAY,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED,8BAA8B;IACvB,MAAM;QACT,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO;SACV;QACD,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAES,WAAW;QACjB,aAAa;QACb,+BAA+B;IACnC,CAAC;IAES,gBAAgB,CAAC,KAA6B;QACpD,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SAChC;IACL,CAAC;IAEM,SAAS,CAAC,WAAqB,EAAE,aAAkC;QACtE,IAAI,UAAU,GAAW,EAAE,CAAC;QAC5B,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC/C,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;aACvE;SACJ;QAED,OAAO,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;IACpE,CAAC;IAEM,6BAA6B,CAAC,aAAkC;QACnE,IAAI,UAAU,GAAG,KAAK,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;QAEpE,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;SAC9E;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QAE5B,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,KAAyB,CAAC,CAAC;SAC/D;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAC7C,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,UAAU,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,qBAAqB,IAAI,CAAC,iBAAiB,MAAM,CAAC;SACvG;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE,CAAC;QAEjE,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB;QACxC,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAExC,IAAI,CAAC,uBAAuB,GAAG,mBAAmB,CAAC,UAAU,CAAC;IAClE,CAAC;CACJ;AAED,aAAa,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../../../types\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"../../Enums/nodeGeometryConnectionPointTypes\";\r\nimport { NodeGeometryBlock } from \"../../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../../nodeGeometryBlockConnectionPoint\";\r\nimport type { TeleportInBlock } from \"./teleportInBlock\";\r\nimport type { NodeGeometryBuildState } from \"../../nodeGeometryBuildState\";\r\n\r\n/**\r\n * Defines a block used to receive a value from a teleport entry point\r\n */\r\nexport class TeleportOutBlock extends NodeGeometryBlock {\r\n    /** @internal */\r\n    public _entryPoint: Nullable<TeleportInBlock> = null;\r\n    /** @internal */\r\n    public _tempEntryPointUniqueId: Nullable<number> = null;\r\n\r\n    /**\r\n     * Create a new TeleportOutBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this._isTeleportOut = true;\r\n\r\n        this.registerOutput(\"output\", NodeGeometryBlockConnectionPointTypes.BasedOnInput);\r\n    }\r\n\r\n    /**\r\n     * Gets the entry point\r\n     */\r\n    public get entryPoint() {\r\n        return this._entryPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"TeleportOutBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /** Detach from entry point */\r\n    public detach() {\r\n        if (!this._entryPoint) {\r\n            return;\r\n        }\r\n        this._entryPoint.detachFromEndpoint(this);\r\n    }\r\n\r\n    protected _buildBlock() {\r\n        // Do nothing\r\n        // All work done by the emitter\r\n    }\r\n\r\n    protected _customBuildStep(state: NodeGeometryBuildState): void {\r\n        if (this.entryPoint) {\r\n            this.entryPoint.build(state);\r\n        }\r\n    }\r\n\r\n    public _dumpCode(uniqueNames: string[], alreadyDumped: NodeGeometryBlock[]) {\r\n        let codeString: string = \"\";\r\n        if (this.entryPoint) {\r\n            if (alreadyDumped.indexOf(this.entryPoint) === -1) {\r\n                codeString += this.entryPoint._dumpCode(uniqueNames, alreadyDumped);\r\n            }\r\n        }\r\n\r\n        return codeString + super._dumpCode(uniqueNames, alreadyDumped);\r\n    }\r\n\r\n    public _dumpCodeForOutputConnections(alreadyDumped: NodeGeometryBlock[]) {\r\n        let codeString = super._dumpCodeForOutputConnections(alreadyDumped);\r\n\r\n        if (this.entryPoint) {\r\n            codeString += this.entryPoint._dumpCodeForOutputConnections(alreadyDumped);\r\n        }\r\n\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Clone the current block to a new identical block\r\n     * @returns a copy of the current block\r\n     */\r\n    public clone() {\r\n        const clone = super.clone();\r\n\r\n        if (this.entryPoint) {\r\n            this.entryPoint.attachToEndpoint(clone as TeleportOutBlock);\r\n        }\r\n\r\n        return clone;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n        if (this.entryPoint) {\r\n            codeString += `${this.entryPoint._codeVariableName}.attachToEndpoint(${this._codeVariableName});\\n`;\r\n        }\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Serializes this block in a JSON representation\r\n     * @returns the serialized block object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.entryPoint = this.entryPoint?.uniqueId ?? \"\";\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any) {\r\n        super._deserialize(serializationObject);\r\n\r\n        this._tempEntryPointUniqueId = serializationObject.entryPoint;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.TeleportOutBlock\", TeleportOutBlock);\r\n"]}