{"version": 3, "file": "spriteManager.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Sprites/spriteManager.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AACnE,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAExD,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAGlD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AA2FjD;;;GAGG;AACH,MAAM,OAAO,aAAa;IA6BtB;;OAEG;IACH,IAAW,SAAS,CAAC,QAAoB;QACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC5D;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrE,CAAC;IAOD;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,eAAe,CAAC,OAAkB,CAAC;IACnD,CAAC;IACD,IAAW,OAAO,CAAC,KAAc;QAC7B,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACxC,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IAED,6DAA6D;IAC7D,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;IAC1C,CAAC;IACD,IAAW,SAAS,CAAC,KAAa;QAC9B,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,KAAK,CAAC;IAC3C,CAAC;IAED,8DAA8D;IAC9D,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;IAC3C,CAAC;IACD,IAAW,UAAU,CAAC,KAAa;QAC/B,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED,8FAA8F;IAC9F,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;IAC3C,CAAC;IACD,IAAW,UAAU,CAAC,KAAc;QAChC,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;IAC1C,CAAC;IACD,IAAW,SAAS,CAAC,SAAiB;QAClC,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/C,CAAC;IAGD;;;OAGG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED,IAAW,iBAAiB,CAAC,KAAc;QACvC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,eAAe,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnD,CAAC;IAED;;;;OAIG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;IAC7C,CAAC;IAED,IAAW,YAAY,CAAC,KAAc;QAClC,IAAI,CAAC,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1C,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,8BAA8B,EAAE;YACjF,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;SAC7E;IACL,CAAC;IAcD;;;;;;;;;;;OAWG;IACH;IACI,iCAAiC;IAC1B,IAAY,EACnB,MAAc,EACd,QAAgB,EAChB,QAAa,EACb,KAAY,EACZ,UAAkB,IAAI,EACtB,eAAuB,OAAO,CAAC,sBAAsB,EACrD,aAAsB,KAAK,EAC3B,aAAyB,IAAI;QARtB,SAAI,GAAJ,IAAI,CAAQ;QAnKvB,+BAA+B;QACxB,YAAO,GAAa,EAAE,CAAC;QAC9B,yDAAyD;QAClD,qBAAgB,GAAG,CAAC,CAAC;QAC5B,qCAAqC;QAC9B,cAAS,GAAW,UAAU,CAAC;QACtC,oEAAoE;QAC7D,eAAU,GAAG,KAAK,CAAC;QAE1B;;WAEG;QACI,aAAQ,GAAQ,IAAI,CAAC;QAE5B,gBAAgB;QACT,mBAAc,GAAG,KAAK,CAAC;QAE9B;;WAEG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAiB,CAAC;QAuFrD,uBAAkB,GAAY,KAAK,CAAC;QAmC5C,wDAAwD;QAChD,oBAAe,GAAY,KAAK,CAAC;QA8VjC,kBAAa,GAAG,CAAC,MAAkB,EAAE,QAAe,EAAQ,EAAE;YAClE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;gBACjB,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;aACxB;YACD,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC;YAC7B,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE;gBACrE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;aACtD;YACD,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC;YAC1E,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC3E,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC;QA5UE,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,WAAW,CAAC,gBAAiB,CAAC;SACzC;QAED,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YAC3D,KAAK,CAAC,aAAa,CAAC,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;SACxD;QACD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAE5E,IAAI,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;YAChC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;SACrC;aAAM,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC/B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC1B,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;SAC9B;aAAM;YACH,IAAI,CAAC,eAAe,GAAQ,IAAI,CAAC;YACjC,OAAO;SACV;QAED,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QAEzC,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;SACxE;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;SACxC;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,eAAe,CAAC;IAC3B,CAAC;IAEO,WAAW,CAAC,MAAc,EAAE,UAAe;QAC/C,IAAI,UAAU,KAAK,IAAI,EAAE;YACrB,IAAI;gBACA,6FAA6F;gBAC7F,IAAI,QAAa,CAAC;gBAClB,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;oBAChC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;iBACrC;qBAAM;oBACH,QAAQ,GAAG,UAAU,CAAC;iBACzB;gBAED,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE;oBACxB,MAAM,SAAS,GAAQ,EAAE,CAAC;oBAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC7C,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;wBAC9B,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;4BACxC,MAAM,IAAI,KAAK,CAAC,6FAA6F,CAAC,CAAC;yBAClH;wBAED,MAAM,IAAI,GAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5C,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;qBACxB;oBACD,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC;iBAC/B;gBAED,MAAM,SAAS,GAAa,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAE7D,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC;aACpC;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;gBAC7B,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;aAC7F;SACJ;aAAM;YACH,MAAM,EAAE,GAAG,KAAK,CAAC;YACjB,IAAI,EAAU,CAAC;YACf,GAAG;gBACC,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC;gBAClB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACnB,QAAQ,EAAE,CAAC,SAAS,GAAG,CAAC,EAAE;YAC3B,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;YACtD,MAAM,OAAO,GAAG,GAAG,EAAE;gBACjB,MAAM,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;gBACtD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YACjC,CAAC,CAAC;YACF,MAAM,MAAM,GAAG,CAAC,IAA0B,EAAE,EAAE;gBAC1C,IAAI;oBACA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAc,CAAC,CAAC;oBAC5C,MAAM,SAAS,GAAa,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;oBAC7D,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;oBAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC5B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC;iBACpC;gBAAC,OAAO,CAAC,EAAE;oBACR,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;oBACzB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;oBAC7B,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;iBACjG;YACL,CAAC,CAAC;YACF,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACzE;IACL,CAAC;IAEO,kBAAkB,CAAC,MAAc,EAAE,GAAQ,EAAE,QAAgB,EAAE,GAAY,EAAE,GAAY;QAC7F,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YAC7C,OAAO,IAAI,CAAC;SACf;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,eAAe,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAClF,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;SACvD;QAED,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE3C,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAErC,YAAY,CAAC,SAAS,EAAE,CAAC;QACzB,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACpC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEpC,MAAM,aAAa,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACjE,MAAM,aAAa,GAAG,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAEvE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,KAAK,GAAG,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACpF,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,MAAM,GAAG,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAErF,MAAM,KAAK,GAAG,IAAI,CAAC,eAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAEzE,OAAO,KAAK,GAAG,GAAG,CAAC;IACvB,CAAC;IAED;;;;;;;OAOG;IACI,UAAU,CAAC,GAAQ,EAAE,MAAc,EAAE,SAAuC,EAAE,SAAmB;QACpG,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3D,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC3B,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC3B,IAAI,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;QAChC,IAAI,aAAa,GAAqB,IAAI,CAAC;QAC3C,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,mBAAmB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClD,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAC1C,IAAI,SAAS,GAAQ,GAAG,CAAC;QACzB,IAAI,SAAS,GAAQ,GAAG,CAAC;QAEzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE;gBACT,SAAS;aACZ;YAED,IAAI,SAAS,EAAE;gBACX,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;oBACpB,SAAS;iBACZ;aACJ;iBAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;gBAC3B,SAAS;aACZ;YAED,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC;YAEpF,IAAI,MAAM,CAAC,KAAK,EAAE;gBACd,sEAAsE;gBACtE,MAAM,CAAC,gBAAgB,CAAC,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjG,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/F,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE3D,2CAA2C;gBAC3C,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/E,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE/E,SAAS,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;gBACxB,OAAO,CAAC,yBAAyB,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;gBACtF,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;aAC1F;iBAAM;gBACH,SAAS,GAAG,GAAG,CAAC;aACnB;YAED,GAAG,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAC/H,GAAG,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAE/H,IAAI,SAAS,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;gBACzC,MAAM,eAAe,GAAG,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;gBAEhF,IAAI,QAAQ,GAAG,eAAe,EAAE;oBAC5B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;wBACxE,SAAS;qBACZ;oBAED,SAAS,GAAG,SAAS,CAAC;oBACtB,QAAQ,GAAG,eAAe,CAAC;oBAC3B,aAAa,GAAG,MAAM,CAAC;oBAEvB,IAAI,SAAS,EAAE;wBACX,MAAM;qBACT;iBACJ;aACJ;SACJ;QAED,IAAI,aAAa,EAAE;YACf,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;YAEjC,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC;YAClB,MAAM,CAAC,YAAY,GAAG,aAAa,CAAC;YACpC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAE3B,mBAAmB;YACnB,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACxC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACxC,SAAS,CAAC,SAAS,EAAE,CAAC;YACtB,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAEjC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC,oBAAoB,CAAC,WAAW,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAErF,OAAO,MAAM,CAAC;SACjB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,eAAe,CAAC,GAAQ,EAAE,MAAc,EAAE,SAAuC;QACpF,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3D,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC3B,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC3B,IAAI,QAAgB,CAAC;QACrB,MAAM,OAAO,GAA4B,EAAE,CAAC;QAC5C,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClE,MAAM,mBAAmB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAE1C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE;gBACT,SAAS;aACZ;YAED,IAAI,SAAS,EAAE;gBACX,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;oBACpB,SAAS;iBACZ;aACJ;iBAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;gBAC3B,SAAS;aACZ;YAED,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC;YAEpF,GAAG,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAC/H,GAAG,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAE/H,IAAI,GAAG,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;gBACnC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;gBAE7D,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;oBAC3D,SAAS;iBACZ;gBAED,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;gBACjC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAErB,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC;gBAClB,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC;gBAC7B,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAE3B,mBAAmB;gBACnB,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACxC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAClC,SAAS,CAAC,SAAS,EAAE,CAAC;gBACtB,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAEjC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gBAC5C,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC,oBAAoB,CAAC,WAAW,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aACxF;SACJ;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,MAAM;QACT,QAAQ;QACR,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACpF,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QACxC,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;SAC5I;aAAM;YACH,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC;SACxH;IACL,CAAC;IAgBD;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,eAAgB,GAAG,IAAI,CAAC;SACtC;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,oBAAoB;QACpB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;YAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACvD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC/C;QAED,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QAEjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,gBAAgB,GAAG,KAAK;QACrC,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7C,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC/D,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QAErD,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,gBAAgB,EAAE;gBAClB,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;aAC1D;iBAAM;gBACH,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBACnD,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;aACvD;SACJ;QAED,mBAAmB,CAAC,OAAO,GAAG,EAAE,CAAC;QAEjC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;SACxD;QAED,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE7C,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,aAAkB,EAAE,KAAY,EAAE,OAAe;QACjE,MAAM,OAAO,GAAG,IAAI,aAAa,CAC7B,aAAa,CAAC,IAAI,EAClB,EAAE,EACF,aAAa,CAAC,QAAQ,EACtB;YACI,KAAK,EAAE,aAAa,CAAC,SAAS;YAC9B,MAAM,EAAE,aAAa,CAAC,UAAU;SACnC,EACD,KAAK,CACR,CAAC;QAEF,IAAI,aAAa,CAAC,UAAU,KAAK,SAAS,EAAE;YACxC,OAAO,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;SACjD;QACD,IAAI,aAAa,CAAC,SAAS,KAAK,SAAS,EAAE;YACvC,OAAO,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;SAC/C;QACD,IAAI,aAAa,CAAC,iBAAiB,KAAK,SAAS,EAAE;YAC/C,OAAO,CAAC,iBAAiB,GAAG,aAAa,CAAC,iBAAiB,CAAC;SAC/D;QACD,IAAI,aAAa,CAAC,YAAY,KAAK,SAAS,EAAE;YAC1C,OAAO,CAAC,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;SACrD;QAED,IAAI,aAAa,CAAC,QAAQ,KAAK,SAAS,EAAE;YACtC,OAAO,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;SAC7C;QAED,IAAI,aAAa,CAAC,OAAO,EAAE;YACvB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAY,CAAC;SACrF;aAAM,IAAI,aAAa,CAAC,WAAW,EAAE;YAClC,OAAO,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,GAAG,aAAa,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACvJ;QAED,KAAK,MAAM,YAAY,IAAI,aAAa,CAAC,OAAO,EAAE;YAC9C,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;SACvC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,kBAAkB,CAAC,IAAsB,EAAE,GAAW,EAAE,KAAY,EAAE,UAAkB,EAAE;QACpG,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE;oBACzB,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE;wBACvB,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBAC7D,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,IAAI,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;wBAExG,IAAI,IAAI,EAAE;4BACN,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;yBACtB;wBAED,OAAO,CAAC,MAAM,CAAC,CAAC;qBACnB;yBAAM;wBACH,MAAM,CAAC,mCAAmC,CAAC,CAAC;qBAC/C;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YACzB,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,qBAAqB,CAAC,SAAiB,EAAE,KAAY,EAAE,UAAkB,EAAE;QACrF,IAAI,SAAS,KAAK,QAAQ,EAAE;YACxB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,aAAa,CAAC,wBAAwB,EAAE,gDAAgD,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;SACzI;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE;oBACzB,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE;wBACvB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;wBACzE,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;wBAC9D,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,IAAI,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;wBAExG,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;wBAE7B,OAAO,CAAC,MAAM,CAAC,CAAC;qBACnB;yBAAM;wBACH,MAAM,CAAC,6BAA6B,GAAG,SAAS,CAAC,CAAC;qBACrD;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;YAC1E,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;;AAxrBD,sCAAsC;AACxB,wBAAU,GAAG,SAAS,CAAC,UAAU,AAAvB,CAAwB;AAyrBhD;;;;;;;GAOG;AACW,oCAAsB,GAAG,aAAa,CAAC,qBAAqB,AAAtC,CAAuC", "sourcesContent": ["import type { IDisposable, Scene } from \"../scene\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Vector3, TmpVectors, Matrix } from \"../Maths/math.vector\";\r\nimport { Sprite } from \"./sprite\";\r\nimport { SpriteSceneComponent } from \"./spriteSceneComponent\";\r\nimport { PickingInfo } from \"../Collisions/pickingInfo\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport { WebRequest } from \"../Misc/webRequest\";\r\nimport { SpriteRenderer } from \"./spriteRenderer\";\r\nimport type { ThinSprite } from \"./thinSprite\";\r\nimport type { ISize } from \"../Maths/math.size\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { Constants } from \"../Engines/constants\";\r\n\r\nimport type { Ray } from \"../Culling/ray\";\r\n\r\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Reflect\r\ndeclare const Reflect: any;\r\n\r\n/**\r\n * Defines the minimum interface to fulfill in order to be a sprite manager.\r\n */\r\nexport interface ISpriteManager extends IDisposable {\r\n    /**\r\n     * Gets manager's name\r\n     */\r\n    name: string;\r\n\r\n    /**\r\n     * Restricts the camera to viewing objects with the same layerMask.\r\n     * A camera with a layerMask of 1 will render spriteManager.layerMask & camera.layerMask!== 0\r\n     */\r\n    layerMask: number;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the mesh can be picked (by scene.pick for instance or through actions). Default is true\r\n     */\r\n    isPickable: boolean;\r\n\r\n    /**\r\n     * Gets the hosting scene\r\n     */\r\n    scene: Scene;\r\n\r\n    /**\r\n     * Specifies the rendering group id for this mesh (0 by default)\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/advanced/transparent_rendering#rendering-groups\r\n     */\r\n    renderingGroupId: number;\r\n\r\n    /**\r\n     * Defines the list of sprites managed by the manager.\r\n     */\r\n    sprites: Array<Sprite>;\r\n\r\n    /**\r\n     * Gets or sets the spritesheet texture\r\n     */\r\n    texture: Texture;\r\n\r\n    /** Defines the default width of a cell in the spritesheet */\r\n    cellWidth: number;\r\n    /** Defines the default height of a cell in the spritesheet */\r\n    cellHeight: number;\r\n\r\n    /** @internal */\r\n    _wasDispatched: boolean;\r\n\r\n    /**\r\n     * Tests the intersection of a sprite with a specific ray.\r\n     * @param ray The ray we are sending to test the collision\r\n     * @param camera The camera space we are sending rays in\r\n     * @param predicate A predicate allowing excluding sprites from the list of object to test\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @returns picking info or null.\r\n     */\r\n    intersects(ray: Ray, camera: Camera, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean): Nullable<PickingInfo>;\r\n\r\n    /**\r\n     * Intersects the sprites with a ray\r\n     * @param ray defines the ray to intersect with\r\n     * @param camera defines the current active camera\r\n     * @param predicate defines a predicate used to select candidate sprites\r\n     * @returns null if no hit or a PickingInfo array\r\n     */\r\n    multiIntersects(ray: Ray, camera: Camera, predicate?: (sprite: Sprite) => boolean): Nullable<PickingInfo[]>;\r\n\r\n    /**\r\n     * Renders the list of sprites on screen.\r\n     */\r\n    render(): void;\r\n\r\n    /**\r\n     * Rebuilds the manager (after a context lost, for eg)\r\n     */\r\n    rebuild(): void;\r\n\r\n    /**\r\n     * Serializes the sprite manager to a JSON object\r\n     */\r\n    serialize(serializeTexture?: boolean): any;\r\n}\r\n\r\n/**\r\n * Class used to manage multiple sprites on the same spritesheet\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/sprites\r\n */\r\nexport class SpriteManager implements ISpriteManager {\r\n    /** Define the Url to load snippets */\r\n    public static SnippetUrl = Constants.SnippetUrl;\r\n\r\n    /** Snippet ID if the manager was created from the snippet server */\r\n    public snippetId: string;\r\n\r\n    /** Gets the list of sprites */\r\n    public sprites: Sprite[] = [];\r\n    /** Gets or sets the rendering group id (0 by default) */\r\n    public renderingGroupId = 0;\r\n    /** Gets or sets camera layer mask */\r\n    public layerMask: number = 0x0fffffff;\r\n    /** Gets or sets a boolean indicating if the sprites are pickable */\r\n    public isPickable = false;\r\n\r\n    /**\r\n     * Gets or sets an object used to store user defined information for the sprite manager\r\n     */\r\n    public metadata: any = null;\r\n\r\n    /** @internal */\r\n    public _wasDispatched = false;\r\n\r\n    /**\r\n     * An event triggered when the manager is disposed.\r\n     */\r\n    public onDisposeObservable = new Observable<SpriteManager>();\r\n\r\n    /**\r\n     * Callback called when the manager is disposed\r\n     */\r\n    public set onDispose(callback: () => void) {\r\n        if (this._onDisposeObserver) {\r\n            this.onDisposeObservable.remove(this._onDisposeObserver);\r\n        }\r\n        this._onDisposeObserver = this.onDisposeObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the unique id of the sprite\r\n     */\r\n    public uniqueId: number;\r\n\r\n    /**\r\n     * Gets the array of sprites\r\n     */\r\n    public get children() {\r\n        return this.sprites;\r\n    }\r\n\r\n    /**\r\n     * Gets the hosting scene\r\n     */\r\n    public get scene() {\r\n        return this._scene;\r\n    }\r\n\r\n    /**\r\n     * Gets the capacity of the manager\r\n     */\r\n    public get capacity() {\r\n        return this._spriteRenderer.capacity;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the spritesheet texture\r\n     */\r\n    public get texture(): Texture {\r\n        return this._spriteRenderer.texture as Texture;\r\n    }\r\n    public set texture(value: Texture) {\r\n        value.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n        value.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n        this._spriteRenderer.texture = value;\r\n        this._textureContent = null;\r\n    }\r\n\r\n    /** Defines the default width of a cell in the spritesheet */\r\n    public get cellWidth(): number {\r\n        return this._spriteRenderer.cellWidth;\r\n    }\r\n    public set cellWidth(value: number) {\r\n        this._spriteRenderer.cellWidth = value;\r\n    }\r\n\r\n    /** Defines the default height of a cell in the spritesheet */\r\n    public get cellHeight(): number {\r\n        return this._spriteRenderer.cellHeight;\r\n    }\r\n    public set cellHeight(value: number) {\r\n        this._spriteRenderer.cellHeight = value;\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating if the manager must consider scene fog when rendering */\r\n    public get fogEnabled(): boolean {\r\n        return this._spriteRenderer.fogEnabled;\r\n    }\r\n    public set fogEnabled(value: boolean) {\r\n        this._spriteRenderer.fogEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Blend mode use to render the particle, it can be any of\r\n     * the static Constants.ALPHA_x properties provided in this class.\r\n     * Default value is Constants.ALPHA_COMBINE\r\n     */\r\n    public get blendMode() {\r\n        return this._spriteRenderer.blendMode;\r\n    }\r\n    public set blendMode(blendMode: number) {\r\n        this._spriteRenderer.blendMode = blendMode;\r\n    }\r\n\r\n    private _disableDepthWrite: boolean = false;\r\n    /** Disables writing to the depth buffer when rendering the sprites.\r\n     *  It can be handy to disable depth writing when using textures without alpha channel\r\n     *  and setting some specific blend modes.\r\n     */\r\n    public get disableDepthWrite() {\r\n        return this._disableDepthWrite;\r\n    }\r\n\r\n    public set disableDepthWrite(value: boolean) {\r\n        this._disableDepthWrite = value;\r\n        this._spriteRenderer.disableDepthWrite = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the renderer must render sprites with pixel perfect rendering\r\n     * In this mode, sprites are rendered as \"pixel art\", which means that they appear as pixelated but remain stable when moving or when rotated or scaled.\r\n     * Note that for this mode to work as expected, the sprite texture must use the BILINEAR sampling mode, not NEAREST!\r\n     */\r\n    public get pixelPerfect() {\r\n        return this._spriteRenderer.pixelPerfect;\r\n    }\r\n\r\n    public set pixelPerfect(value: boolean) {\r\n        this._spriteRenderer.pixelPerfect = value;\r\n        if (value && this.texture.samplingMode !== Constants.TEXTURE_TRILINEAR_SAMPLINGMODE) {\r\n            this.texture.updateSamplingMode(Constants.TEXTURE_TRILINEAR_SAMPLINGMODE);\r\n        }\r\n    }\r\n\r\n    private _spriteRenderer: SpriteRenderer;\r\n    /** Associative array from JSON sprite data file */\r\n    private _cellData: any;\r\n    /** Array of sprite names from JSON sprite data file */\r\n    private _spriteMap: Array<string>;\r\n    /** True when packed cell data from JSON file is ready*/\r\n    private _packedAndReady: boolean = false;\r\n    private _textureContent: Nullable<Uint8Array>;\r\n    private _onDisposeObserver: Nullable<Observer<SpriteManager>>;\r\n    private _fromPacked: boolean;\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * Creates a new sprite manager\r\n     * @param name defines the manager's name\r\n     * @param imgUrl defines the sprite sheet url\r\n     * @param capacity defines the maximum allowed number of sprites\r\n     * @param cellSize defines the size of a sprite cell\r\n     * @param scene defines the hosting scene\r\n     * @param epsilon defines the epsilon value to align texture (0.01 by default)\r\n     * @param samplingMode defines the sampling mode to use with spritesheet\r\n     * @param fromPacked set to false; do not alter\r\n     * @param spriteJSON null otherwise a JSON object defining sprite sheet data; do not alter\r\n     */\r\n    constructor(\r\n        /** defines the manager's name */\r\n        public name: string,\r\n        imgUrl: string,\r\n        capacity: number,\r\n        cellSize: any,\r\n        scene: Scene,\r\n        epsilon: number = 0.01,\r\n        samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE,\r\n        fromPacked: boolean = false,\r\n        spriteJSON: any | null = null\r\n    ) {\r\n        if (!scene) {\r\n            scene = EngineStore.LastCreatedScene!;\r\n        }\r\n\r\n        if (!scene._getComponent(SceneComponentConstants.NAME_SPRITE)) {\r\n            scene._addComponent(new SpriteSceneComponent(scene));\r\n        }\r\n        this._fromPacked = fromPacked;\r\n\r\n        this._scene = scene;\r\n        const engine = this._scene.getEngine();\r\n        this._spriteRenderer = new SpriteRenderer(engine, capacity, epsilon, scene);\r\n\r\n        if (cellSize.width && cellSize.height) {\r\n            this.cellWidth = cellSize.width;\r\n            this.cellHeight = cellSize.height;\r\n        } else if (cellSize !== undefined) {\r\n            this.cellWidth = cellSize;\r\n            this.cellHeight = cellSize;\r\n        } else {\r\n            this._spriteRenderer = <any>null;\r\n            return;\r\n        }\r\n\r\n        this._scene.spriteManagers && this._scene.spriteManagers.push(this);\r\n        this.uniqueId = this.scene.getUniqueId();\r\n\r\n        if (imgUrl) {\r\n            this.texture = new Texture(imgUrl, scene, true, false, samplingMode);\r\n        }\r\n\r\n        if (this._fromPacked) {\r\n            this._makePacked(imgUrl, spriteJSON);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"SpriteManager\"\r\n     * @returns \"SpriteManager\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"SpriteManager\";\r\n    }\r\n\r\n    private _makePacked(imgUrl: string, spriteJSON: any) {\r\n        if (spriteJSON !== null) {\r\n            try {\r\n                //Get the JSON and Check its structure.  If its an array parse it if its a JSON string etc...\r\n                let celldata: any;\r\n                if (typeof spriteJSON === \"string\") {\r\n                    celldata = JSON.parse(spriteJSON);\r\n                } else {\r\n                    celldata = spriteJSON;\r\n                }\r\n\r\n                if (celldata.frames.length) {\r\n                    const frametemp: any = {};\r\n                    for (let i = 0; i < celldata.frames.length; i++) {\r\n                        const _f = celldata.frames[i];\r\n                        if (typeof Object.keys(_f)[0] !== \"string\") {\r\n                            throw new Error(\"Invalid JSON Format.  Check the frame values and make sure the name is the first parameter.\");\r\n                        }\r\n\r\n                        const name: string = _f[Object.keys(_f)[0]];\r\n                        frametemp[name] = _f;\r\n                    }\r\n                    celldata.frames = frametemp;\r\n                }\r\n\r\n                const spritemap = <string[]>Reflect.ownKeys(celldata.frames);\r\n\r\n                this._spriteMap = spritemap;\r\n                this._packedAndReady = true;\r\n                this._cellData = celldata.frames;\r\n            } catch (e) {\r\n                this._fromPacked = false;\r\n                this._packedAndReady = false;\r\n                throw new Error(\"Invalid JSON from string. Spritesheet managed with constant cell size.\");\r\n            }\r\n        } else {\r\n            const re = /\\./g;\r\n            let li: number;\r\n            do {\r\n                li = re.lastIndex;\r\n                re.test(imgUrl);\r\n            } while (re.lastIndex > 0);\r\n            const jsonUrl = imgUrl.substring(0, li - 1) + \".json\";\r\n            const onerror = () => {\r\n                Logger.Error(\"JSON ERROR: Unable to load JSON file.\");\r\n                this._fromPacked = false;\r\n                this._packedAndReady = false;\r\n            };\r\n            const onload = (data: string | ArrayBuffer) => {\r\n                try {\r\n                    const celldata = JSON.parse(data as string);\r\n                    const spritemap = <string[]>Reflect.ownKeys(celldata.frames);\r\n                    this._spriteMap = spritemap;\r\n                    this._packedAndReady = true;\r\n                    this._cellData = celldata.frames;\r\n                } catch (e) {\r\n                    this._fromPacked = false;\r\n                    this._packedAndReady = false;\r\n                    throw new Error(\"Invalid JSON format. Please check documentation for format specifications.\");\r\n                }\r\n            };\r\n            Tools.LoadFile(jsonUrl, onload, undefined, undefined, false, onerror);\r\n        }\r\n    }\r\n\r\n    private _checkTextureAlpha(sprite: Sprite, ray: Ray, distance: number, min: Vector3, max: Vector3) {\r\n        if (!sprite.useAlphaForPicking || !this.texture) {\r\n            return true;\r\n        }\r\n\r\n        const textureSize = this.texture.getSize();\r\n        if (!this._textureContent) {\r\n            this._textureContent = new Uint8Array(textureSize.width * textureSize.height * 4);\r\n            this.texture.readPixels(0, 0, this._textureContent);\r\n        }\r\n\r\n        const contactPoint = TmpVectors.Vector3[0];\r\n\r\n        contactPoint.copyFrom(ray.direction);\r\n\r\n        contactPoint.normalize();\r\n        contactPoint.scaleInPlace(distance);\r\n        contactPoint.addInPlace(ray.origin);\r\n\r\n        const contactPointU = (contactPoint.x - min.x) / (max.x - min.x);\r\n        const contactPointV = 1.0 - (contactPoint.y - min.y) / (max.y - min.y);\r\n\r\n        const u = (sprite._xOffset * textureSize.width + contactPointU * sprite._xSize) | 0;\r\n        const v = (sprite._yOffset * textureSize.height + contactPointV * sprite._ySize) | 0;\r\n\r\n        const alpha = this._textureContent![(u + v * textureSize.width) * 4 + 3];\r\n\r\n        return alpha > 0.5;\r\n    }\r\n\r\n    /**\r\n     * Intersects the sprites with a ray\r\n     * @param ray defines the ray to intersect with\r\n     * @param camera defines the current active camera\r\n     * @param predicate defines a predicate used to select candidate sprites\r\n     * @param fastCheck defines if a fast check only must be done (the first potential sprite is will be used and not the closer)\r\n     * @returns null if no hit or a PickingInfo\r\n     */\r\n    public intersects(ray: Ray, camera: Camera, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean): Nullable<PickingInfo> {\r\n        const count = Math.min(this.capacity, this.sprites.length);\r\n        const min = Vector3.Zero();\r\n        const max = Vector3.Zero();\r\n        let distance = Number.MAX_VALUE;\r\n        let currentSprite: Nullable<Sprite> = null;\r\n        const pickedPoint = TmpVectors.Vector3[0];\r\n        const cameraSpacePosition = TmpVectors.Vector3[1];\r\n        const cameraView = camera.getViewMatrix();\r\n        let activeRay: Ray = ray;\r\n        let pickedRay: Ray = ray;\r\n\r\n        for (let index = 0; index < count; index++) {\r\n            const sprite = this.sprites[index];\r\n            if (!sprite) {\r\n                continue;\r\n            }\r\n\r\n            if (predicate) {\r\n                if (!predicate(sprite)) {\r\n                    continue;\r\n                }\r\n            } else if (!sprite.isPickable) {\r\n                continue;\r\n            }\r\n\r\n            Vector3.TransformCoordinatesToRef(sprite.position, cameraView, cameraSpacePosition);\r\n\r\n            if (sprite.angle) {\r\n                // Create a rotation matrix to rotate the ray to the sprite's rotation\r\n                Matrix.TranslationToRef(-cameraSpacePosition.x, -cameraSpacePosition.y, 0, TmpVectors.Matrix[1]);\r\n                Matrix.TranslationToRef(cameraSpacePosition.x, cameraSpacePosition.y, 0, TmpVectors.Matrix[2]);\r\n                Matrix.RotationZToRef(-sprite.angle, TmpVectors.Matrix[3]);\r\n\r\n                // inv translation x rotation x translation\r\n                TmpVectors.Matrix[1].multiplyToRef(TmpVectors.Matrix[3], TmpVectors.Matrix[4]);\r\n                TmpVectors.Matrix[4].multiplyToRef(TmpVectors.Matrix[2], TmpVectors.Matrix[0]);\r\n\r\n                activeRay = ray.clone();\r\n                Vector3.TransformCoordinatesToRef(ray.origin, TmpVectors.Matrix[0], activeRay.origin);\r\n                Vector3.TransformNormalToRef(ray.direction, TmpVectors.Matrix[0], activeRay.direction);\r\n            } else {\r\n                activeRay = ray;\r\n            }\r\n\r\n            min.copyFromFloats(cameraSpacePosition.x - sprite.width / 2, cameraSpacePosition.y - sprite.height / 2, cameraSpacePosition.z);\r\n            max.copyFromFloats(cameraSpacePosition.x + sprite.width / 2, cameraSpacePosition.y + sprite.height / 2, cameraSpacePosition.z);\r\n\r\n            if (activeRay.intersectsBoxMinMax(min, max)) {\r\n                const currentDistance = Vector3.Distance(cameraSpacePosition, activeRay.origin);\r\n\r\n                if (distance > currentDistance) {\r\n                    if (!this._checkTextureAlpha(sprite, activeRay, currentDistance, min, max)) {\r\n                        continue;\r\n                    }\r\n\r\n                    pickedRay = activeRay;\r\n                    distance = currentDistance;\r\n                    currentSprite = sprite;\r\n\r\n                    if (fastCheck) {\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (currentSprite) {\r\n            const result = new PickingInfo();\r\n\r\n            cameraView.invertToRef(TmpVectors.Matrix[0]);\r\n            result.hit = true;\r\n            result.pickedSprite = currentSprite;\r\n            result.distance = distance;\r\n\r\n            // Get picked point\r\n            const direction = TmpVectors.Vector3[2];\r\n            direction.copyFrom(pickedRay.direction);\r\n            direction.normalize();\r\n            direction.scaleInPlace(distance);\r\n\r\n            pickedRay.origin.addToRef(direction, pickedPoint);\r\n            result.pickedPoint = Vector3.TransformCoordinates(pickedPoint, TmpVectors.Matrix[0]);\r\n\r\n            return result;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Intersects the sprites with a ray\r\n     * @param ray defines the ray to intersect with\r\n     * @param camera defines the current active camera\r\n     * @param predicate defines a predicate used to select candidate sprites\r\n     * @returns null if no hit or a PickingInfo array\r\n     */\r\n    public multiIntersects(ray: Ray, camera: Camera, predicate?: (sprite: Sprite) => boolean): Nullable<PickingInfo[]> {\r\n        const count = Math.min(this.capacity, this.sprites.length);\r\n        const min = Vector3.Zero();\r\n        const max = Vector3.Zero();\r\n        let distance: number;\r\n        const results: Nullable<PickingInfo[]> = [];\r\n        const pickedPoint = TmpVectors.Vector3[0].copyFromFloats(0, 0, 0);\r\n        const cameraSpacePosition = TmpVectors.Vector3[1].copyFromFloats(0, 0, 0);\r\n        const cameraView = camera.getViewMatrix();\r\n\r\n        for (let index = 0; index < count; index++) {\r\n            const sprite = this.sprites[index];\r\n            if (!sprite) {\r\n                continue;\r\n            }\r\n\r\n            if (predicate) {\r\n                if (!predicate(sprite)) {\r\n                    continue;\r\n                }\r\n            } else if (!sprite.isPickable) {\r\n                continue;\r\n            }\r\n\r\n            Vector3.TransformCoordinatesToRef(sprite.position, cameraView, cameraSpacePosition);\r\n\r\n            min.copyFromFloats(cameraSpacePosition.x - sprite.width / 2, cameraSpacePosition.y - sprite.height / 2, cameraSpacePosition.z);\r\n            max.copyFromFloats(cameraSpacePosition.x + sprite.width / 2, cameraSpacePosition.y + sprite.height / 2, cameraSpacePosition.z);\r\n\r\n            if (ray.intersectsBoxMinMax(min, max)) {\r\n                distance = Vector3.Distance(cameraSpacePosition, ray.origin);\r\n\r\n                if (!this._checkTextureAlpha(sprite, ray, distance, min, max)) {\r\n                    continue;\r\n                }\r\n\r\n                const result = new PickingInfo();\r\n                results.push(result);\r\n\r\n                cameraView.invertToRef(TmpVectors.Matrix[0]);\r\n                result.hit = true;\r\n                result.pickedSprite = sprite;\r\n                result.distance = distance;\r\n\r\n                // Get picked point\r\n                const direction = TmpVectors.Vector3[2];\r\n                direction.copyFrom(ray.direction);\r\n                direction.normalize();\r\n                direction.scaleInPlace(distance);\r\n\r\n                ray.origin.addToRef(direction, pickedPoint);\r\n                result.pickedPoint = Vector3.TransformCoordinates(pickedPoint, TmpVectors.Matrix[0]);\r\n            }\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Render all child sprites\r\n     */\r\n    public render(): void {\r\n        // Check\r\n        if (this._fromPacked && (!this._packedAndReady || !this._spriteMap || !this._cellData)) {\r\n            return;\r\n        }\r\n\r\n        const engine = this._scene.getEngine();\r\n        const deltaTime = engine.getDeltaTime();\r\n        if (this._packedAndReady) {\r\n            this._spriteRenderer.render(this.sprites, deltaTime, this._scene.getViewMatrix(), this._scene.getProjectionMatrix(), this._customUpdate);\r\n        } else {\r\n            this._spriteRenderer.render(this.sprites, deltaTime, this._scene.getViewMatrix(), this._scene.getProjectionMatrix());\r\n        }\r\n    }\r\n\r\n    private _customUpdate = (sprite: ThinSprite, baseSize: ISize): void => {\r\n        if (!sprite.cellRef) {\r\n            sprite.cellIndex = 0;\r\n        }\r\n        const num = sprite.cellIndex;\r\n        if (typeof num === \"number\" && isFinite(num) && Math.floor(num) === num) {\r\n            sprite.cellRef = this._spriteMap[sprite.cellIndex];\r\n        }\r\n        sprite._xOffset = this._cellData[sprite.cellRef].frame.x / baseSize.width;\r\n        sprite._yOffset = this._cellData[sprite.cellRef].frame.y / baseSize.height;\r\n        sprite._xSize = this._cellData[sprite.cellRef].frame.w;\r\n        sprite._ySize = this._cellData[sprite.cellRef].frame.h;\r\n    };\r\n\r\n    /**\r\n     * Rebuilds the manager (after a context lost, for eg)\r\n     */\r\n    public rebuild(): void {\r\n        this._spriteRenderer?.rebuild();\r\n    }\r\n\r\n    /**\r\n     * Release associated resources\r\n     */\r\n    public dispose(): void {\r\n        if (this._spriteRenderer) {\r\n            this._spriteRenderer.dispose();\r\n            (<any>this._spriteRenderer) = null;\r\n        }\r\n\r\n        this._textureContent = null;\r\n\r\n        // Remove from scene\r\n        if (this._scene.spriteManagers) {\r\n            const index = this._scene.spriteManagers.indexOf(this);\r\n            this._scene.spriteManagers.splice(index, 1);\r\n        }\r\n\r\n        // Callback\r\n        this.onDisposeObservable.notifyObservers(this);\r\n        this.onDisposeObservable.clear();\r\n\r\n        this.metadata = null;\r\n    }\r\n\r\n    /**\r\n     * Serializes the sprite manager to a JSON object\r\n     * @param serializeTexture defines if the texture must be serialized as well\r\n     * @returns the JSON object\r\n     */\r\n    public serialize(serializeTexture = false): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.capacity = this.capacity;\r\n        serializationObject.cellWidth = this.cellWidth;\r\n        serializationObject.cellHeight = this.cellHeight;\r\n        serializationObject.fogEnabled = this.fogEnabled;\r\n        serializationObject.blendMode = this.blendMode;\r\n        serializationObject.disableDepthWrite = this.disableDepthWrite;\r\n        serializationObject.pixelPerfect = this.pixelPerfect;\r\n\r\n        if (this.texture) {\r\n            if (serializeTexture) {\r\n                serializationObject.texture = this.texture.serialize();\r\n            } else {\r\n                serializationObject.textureUrl = this.texture.name;\r\n                serializationObject.invertY = this.texture._invertY;\r\n            }\r\n        }\r\n\r\n        serializationObject.sprites = [];\r\n\r\n        for (const sprite of this.sprites) {\r\n            serializationObject.sprites.push(sprite.serialize());\r\n        }\r\n\r\n        serializationObject.metadata = this.metadata;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parses a JSON object to create a new sprite manager.\r\n     * @param parsedManager The JSON object to parse\r\n     * @param scene The scene to create the sprite manager\r\n     * @param rootUrl The root url to use to load external dependencies like texture\r\n     * @returns the new sprite manager\r\n     */\r\n    public static Parse(parsedManager: any, scene: Scene, rootUrl: string): SpriteManager {\r\n        const manager = new SpriteManager(\r\n            parsedManager.name,\r\n            \"\",\r\n            parsedManager.capacity,\r\n            {\r\n                width: parsedManager.cellWidth,\r\n                height: parsedManager.cellHeight,\r\n            },\r\n            scene\r\n        );\r\n\r\n        if (parsedManager.fogEnabled !== undefined) {\r\n            manager.fogEnabled = parsedManager.fogEnabled;\r\n        }\r\n        if (parsedManager.blendMode !== undefined) {\r\n            manager.blendMode = parsedManager.blendMode;\r\n        }\r\n        if (parsedManager.disableDepthWrite !== undefined) {\r\n            manager.disableDepthWrite = parsedManager.disableDepthWrite;\r\n        }\r\n        if (parsedManager.pixelPerfect !== undefined) {\r\n            manager.pixelPerfect = parsedManager.pixelPerfect;\r\n        }\r\n\r\n        if (parsedManager.metadata !== undefined) {\r\n            manager.metadata = parsedManager.metadata;\r\n        }\r\n\r\n        if (parsedManager.texture) {\r\n            manager.texture = Texture.Parse(parsedManager.texture, scene, rootUrl) as Texture;\r\n        } else if (parsedManager.textureName) {\r\n            manager.texture = new Texture(rootUrl + parsedManager.textureUrl, scene, false, parsedManager.invertY !== undefined ? parsedManager.invertY : true);\r\n        }\r\n\r\n        for (const parsedSprite of parsedManager.sprites) {\r\n            Sprite.Parse(parsedSprite, manager);\r\n        }\r\n\r\n        return manager;\r\n    }\r\n\r\n    /**\r\n     * Creates a sprite manager from a snippet saved in a remote file\r\n     * @param name defines the name of the sprite manager to create (can be null or empty to use the one from the json data)\r\n     * @param url defines the url to load from\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a promise that will resolve to the new sprite manager\r\n     */\r\n    public static ParseFromFileAsync(name: Nullable<string>, url: string, scene: Scene, rootUrl: string = \"\"): Promise<SpriteManager> {\r\n        return new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        const serializationObject = JSON.parse(request.responseText);\r\n                        const output = SpriteManager.Parse(serializationObject, scene || EngineStore.LastCreatedScene, rootUrl);\r\n\r\n                        if (name) {\r\n                            output.name = name;\r\n                        }\r\n\r\n                        resolve(output);\r\n                    } else {\r\n                        reject(\"Unable to load the sprite manager\");\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", url);\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a sprite manager from a snippet saved by the sprite editor\r\n     * @param snippetId defines the snippet to load (can be set to _BLANK to create a default one)\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a promise that will resolve to the new sprite manager\r\n     */\r\n    public static ParseFromSnippetAsync(snippetId: string, scene: Scene, rootUrl: string = \"\"): Promise<SpriteManager> {\r\n        if (snippetId === \"_BLANK\") {\r\n            return Promise.resolve(new SpriteManager(\"Default sprite manager\", \"//playground.babylonjs.com/textures/player.png\", 500, 64, scene));\r\n        }\r\n\r\n        return new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        const snippet = JSON.parse(JSON.parse(request.responseText).jsonPayload);\r\n                        const serializationObject = JSON.parse(snippet.spriteManager);\r\n                        const output = SpriteManager.Parse(serializationObject, scene || EngineStore.LastCreatedScene, rootUrl);\r\n\r\n                        output.snippetId = snippetId;\r\n\r\n                        resolve(output);\r\n                    } else {\r\n                        reject(\"Unable to load the snippet \" + snippetId);\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", this.SnippetUrl + \"/\" + snippetId.replace(/#/g, \"/\"));\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a sprite manager from a snippet saved by the sprite editor\r\n     * @deprecated Please use ParseFromSnippetAsync instead\r\n     * @param snippetId defines the snippet to load (can be set to _BLANK to create a default one)\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a promise that will resolve to the new sprite manager\r\n     */\r\n    public static CreateFromSnippetAsync = SpriteManager.ParseFromSnippetAsync;\r\n}\r\n"]}