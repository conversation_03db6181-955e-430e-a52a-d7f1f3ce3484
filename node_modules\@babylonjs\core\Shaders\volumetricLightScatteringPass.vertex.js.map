{"version": 3, "file": "volumetricLightScatteringPass.vertex.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Shaders/volumetricLightScatteringPass.vertex.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,mCAAmC,CAAC;AAC3C,OAAO,kDAAkD,CAAC;AAC1D,OAAO,sDAAsD,CAAC;AAC9D,OAAO,gDAAgD,CAAC;AACxD,OAAO,uCAAuC,CAAC;AAC/C,OAAO,2CAA2C,CAAC;AACnD,OAAO,qCAAqC,CAAC;AAC7C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,8BAA8B,CAAC;AACtC,OAAO,uCAAuC,CAAC;AAE/C,MAAM,IAAI,GAAG,2CAA2C,CAAC;AACzD,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqCd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACxC,gBAAgB;AAChB,MAAM,CAAC,MAAM,yCAAyC,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore\";\nimport \"./ShadersInclude/bonesDeclaration\";\nimport \"./ShadersInclude/bakedVertexAnimationDeclaration\";\nimport \"./ShadersInclude/morphTargetsVertexGlobalDeclaration\";\nimport \"./ShadersInclude/morphTargetsVertexDeclaration\";\nimport \"./ShadersInclude/instancesDeclaration\";\nimport \"./ShadersInclude/morphTargetsVertexGlobal\";\nimport \"./ShadersInclude/morphTargetsVertex\";\nimport \"./ShadersInclude/instancesVertex\";\nimport \"./ShadersInclude/bonesVertex\";\nimport \"./ShadersInclude/bakedVertexAnimation\";\n\nconst name = \"volumetricLightScatteringPassVertexShader\";\nconst shader = `attribute vec3 position;\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<morphTargetsVertexGlobalDeclaration>\n#include<morphTargetsVertexDeclaration>[0..maxSimultaneousMorphTargets]\n#include<instancesDeclaration>\nuniform mat4 viewProjection;uniform vec2 depthValues;\n#if defined(ALPHATEST) || defined(NEED_UV)\nvarying vec2 vUV;uniform mat4 diffuseMatrix;\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#endif\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void)\n{vec3 positionUpdated=position;\n#if (defined(ALPHATEST) || defined(NEED_UV)) && defined(UV1)\nvec2 uvUpdated=uv;\n#endif\n#include<morphTargetsVertexGlobal>\n#include<morphTargetsVertex>[0..maxSimultaneousMorphTargets]\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\ngl_Position=viewProjection*finalWorld*vec4(positionUpdated,1.0);\n#if defined(ALPHATEST) || defined(BASIC_RENDER)\n#ifdef UV1\nvUV=vec2(diffuseMatrix*vec4(uvUpdated,1.0,0.0));\n#endif\n#ifdef UV2\nvUV=vec2(diffuseMatrix*vec4(uv2,1.0,0.0));\n#endif\n#endif\n}\n`;\n// Sideeffect\nShaderStore.ShadersStore[name] = shader;\n/** @internal */\nexport const volumetricLightScatteringPassVertexShader = { name, shader };\n"]}