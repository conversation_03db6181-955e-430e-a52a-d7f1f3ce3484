{"version": 3, "file": "physicsConstraint.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Physics/v2/physicsConstraint.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,MAAM,wBAAwB,CAAC;AAEtF;;;;GAIG;AACH,MAAM,OAAO,iBAAiB;IAiB1B;;;;;;;;OAQG;IACH,YAAY,IAA2B,EAAE,OAAoC,EAAE,KAAY;QAzB3F;;WAEG;QACI,gBAAW,GAAQ,SAAS,CAAC;QAuBhC,IAAI,CAAC,KAAK,EAAE;YACR,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;SAC1E;QACD,MAAM,aAAa,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC/C,IAAI,CAAC,aAAa,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACnD;QACD,IAAI,aAAa,CAAC,gBAAgB,EAAE,IAAI,CAAC,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;SACvE;QACD,MAAM,aAAa,GAAG,aAAa,CAAC,gBAAgB,EAAE,CAAC;QACvD,IAAI,CAAC,aAAa,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACnD;QAED,IAAI,CAAC,cAAc,GAAG,aAAuC,CAAC;QAC9D,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,IAAW,SAAS,CAAC,SAAkB;QACnC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IAED;;;OAGG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IAED;;;;;OAKG;IACH,IAAW,mBAAmB,CAAC,SAAkB;QAC7C,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;;OAGG;IACI,wBAAwB;QAC3B,OAAO,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;IAC9D,CAAC;IAED;;;;OAIG;IACI,OAAO;QACV,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,gBAAgB;CAuB5B;AAED;;;;;GAKG;AACH,MAAM,OAAO,qBAAsB,SAAQ,iBAAiB;IAMxD,YAAY,gBAA6C,EAAE,MAA0B,EAAE,KAAY;QAC/F,KAAK,CAAC,qBAAqB,CAAC,OAAO,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;QAC9D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,IAA2B,EAAE,QAAgB;QAChE,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,IAA2B;QAC9C,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;OASG;IACI,WAAW,CAAC,IAA2B,EAAE,SAAyC;QACrF,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;OAMG;IACI,WAAW,CAAC,IAA2B;QAC1C,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,IAA2B,EAAE,QAAgB;QAChE,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,IAA2B;QAC9C,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;OAQG;IACI,eAAe,CAAC,IAA2B,EAAE,KAAa;QAC7D,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,IAA2B;QAC9C,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,IAA2B,EAAE,SAAqC;QACtF,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;OAMG;IACI,gBAAgB,CAAC,IAA2B;QAC/C,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;OAMG;IACI,kBAAkB,CAAC,IAA2B,EAAE,MAAc;QACjE,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAC/D,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,IAA2B;QACjD,OAAO,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACI,oBAAoB,CAAC,IAA2B,EAAE,QAAgB;QACrE,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IACnE,CAAC;IAED;;;;;OAKG;IACI,oBAAoB,CAAC,IAA2B;QACnD,OAAO,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;CACJ;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,OAAO,uBAAwB,SAAQ,iBAAiB;IAC1D,YAAY,MAAe,EAAE,MAAe,EAAE,KAAc,EAAE,KAAc,EAAE,KAAY;QACtF,KAAK,CAAC,qBAAqB,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;IACxH,CAAC;CACJ;AAED;;;;;;;;;GASG;AACH,MAAM,OAAO,kBAAmB,SAAQ,iBAAiB;IACrD,YAAY,WAAmB,EAAE,KAAY;QACzC,KAAK,CAAC,qBAAqB,CAAC,QAAQ,EAAE,EAAE,WAAW,EAAE,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;IAC/E,CAAC;CACJ;AAED;;;;;;;;;;GAUG;AACH,MAAM,OAAO,eAAgB,SAAQ,iBAAiB;IAClD,YAAY,MAAe,EAAE,MAAe,EAAE,KAAc,EAAE,KAAc,EAAE,KAAY;QACtF,KAAK,CAAC,qBAAqB,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;IAC9G,CAAC;CACJ;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,OAAO,gBAAiB,SAAQ,iBAAiB;IACnD,YAAY,MAAe,EAAE,MAAe,EAAE,KAAc,EAAE,KAAc,EAAE,KAAY;QACtF,KAAK,CAAC,qBAAqB,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;IAC/G,CAAC;CACJ;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,OAAO,cAAe,SAAQ,iBAAiB;IACjD,YAAY,MAAe,EAAE,MAAe,EAAE,KAAc,EAAE,KAAc,EAAE,KAAY;QACtF,KAAK,CAAC,qBAAqB,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;IAC7G,CAAC;CACJ;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,OAAO,mBAAoB,SAAQ,iBAAiB;IACtD,YAAY,MAAe,EAAE,MAAe,EAAE,KAAc,EAAE,KAAc,EAAE,KAAY;QACtF,KAAK,CAAC,qBAAqB,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;IAClH,CAAC;CACJ;AAED;;;;;;;;;;;;;;GAcG;AACH,MAAM,OAAO,gBAAiB,SAAQ,qBAAqB;IACvD,YAAY,MAAe,EAAE,MAAe,EAAE,KAAc,EAAE,KAAc,EAAE,WAAmB,EAAE,WAAmB,EAAE,SAAiB,EAAE,OAAe,EAAE,KAAY;QACpK,KAAK,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,qBAAqB,CAAC,eAAe,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACxK,CAAC;CACJ", "sourcesContent": ["import type { Scene } from \"../../scene\";\r\nimport type { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { IPhysicsEnginePluginV2, PhysicsConstraintParameters, PhysicsConstraintAxisLimitMode, PhysicsConstraintMotorType, ConstrainedBodyPair } from \"./IPhysicsEnginePlugin\";\r\nimport { PhysicsConstraintAxis, PhysicsConstraintType } from \"./IPhysicsEnginePlugin\";\r\n\r\n/**\r\n * This is a holder class for the physics constraint created by the physics plugin\r\n * It holds a set of functions to control the underlying constraint\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport class PhysicsConstraint {\r\n    /**\r\n     * V2 Physics plugin private data for a physics material\r\n     */\r\n    public _pluginData: any = undefined;\r\n    /**\r\n     * The V2 plugin used to create and manage this Physics Body\r\n     */\r\n    protected _physicsPlugin: IPhysicsEnginePluginV2;\r\n    protected _options: PhysicsConstraintParameters;\r\n    protected _type: PhysicsConstraintType;\r\n    /**\r\n     * @internal\r\n     * The internal options that were used to init the constraint\r\n     */\r\n    public _initOptions?: PhysicsConstraintParameters;\r\n\r\n    /**\r\n     * Constructs a new constraint for the physics constraint.\r\n     * @param type The type of constraint to create.\r\n     * @param options The options for the constraint.\r\n     * @param scene The scene the constraint belongs to.\r\n     *\r\n     * This code is useful for creating a new constraint for the physics engine. It checks if the scene has a physics engine, and if the plugin version is correct.\r\n     * If all checks pass, it initializes the constraint with the given type and options.\r\n     */\r\n    constructor(type: PhysicsConstraintType, options: PhysicsConstraintParameters, scene: Scene) {\r\n        if (!scene) {\r\n            throw new Error(\"Missing scene parameter for constraint constructor.\");\r\n        }\r\n        const physicsEngine = scene.getPhysicsEngine();\r\n        if (!physicsEngine) {\r\n            throw new Error(\"No Physics Engine available.\");\r\n        }\r\n        if (physicsEngine.getPluginVersion() != 2) {\r\n            throw new Error(\"Plugin version is incorrect. Expected version 2.\");\r\n        }\r\n        const physicsPlugin = physicsEngine.getPhysicsPlugin();\r\n        if (!physicsPlugin) {\r\n            throw new Error(\"No Physics Plugin available.\");\r\n        }\r\n\r\n        this._physicsPlugin = physicsPlugin as IPhysicsEnginePluginV2;\r\n        this._options = options;\r\n        this._type = type;\r\n    }\r\n\r\n    /**\r\n     * Gets the type of the constraint.\r\n     *\r\n     * @returns The type of the constraint.\r\n     *\r\n     */\r\n    public get type(): PhysicsConstraintType {\r\n        return this._type;\r\n    }\r\n\r\n    /**\r\n     * Retrieves the options of the physics constraint.\r\n     *\r\n     * @returns The physics constraint parameters.\r\n     *\r\n     */\r\n    public get options(): PhysicsConstraintParameters {\r\n        return this._options;\r\n    }\r\n\r\n    /**\r\n     * Enable/disable the constraint\r\n     * @param isEnabled value for the constraint\r\n     */\r\n    public set isEnabled(isEnabled: boolean) {\r\n        this._physicsPlugin.setEnabled(this, isEnabled);\r\n    }\r\n\r\n    /**\r\n     *\r\n     * @returns true if constraint is enabled\r\n     */\r\n    public get isEnabled(): boolean {\r\n        return this._physicsPlugin.getEnabled(this);\r\n    }\r\n\r\n    /**\r\n     * Enables or disables collisions for the physics engine.\r\n     *\r\n     * @param isEnabled - A boolean value indicating whether collisions should be enabled or disabled.\r\n     *\r\n     */\r\n    public set isCollisionsEnabled(isEnabled: boolean) {\r\n        this._physicsPlugin.setCollisionsEnabled(this, isEnabled);\r\n    }\r\n\r\n    /**\r\n     * Gets whether collisions are enabled for this physics object.\r\n     *\r\n     * @returns `true` if collisions are enabled, `false` otherwise.\r\n     *\r\n     */\r\n    public get isCollisionsEnabled(): boolean {\r\n        return this._physicsPlugin.getCollisionsEnabled(this);\r\n    }\r\n\r\n    /**\r\n     * Gets all bodies that are using this constraint\r\n     * @returns\r\n     */\r\n    public getBodiesUsingConstraint(): ConstrainedBodyPair[] {\r\n        return this._physicsPlugin.getBodiesUsingConstraint(this);\r\n    }\r\n\r\n    /**\r\n     * Disposes the constraint from the physics engine.\r\n     *\r\n     * This method is useful for cleaning up the physics engine when a body is no longer needed. Disposing the body will free up resources and prevent memory leaks.\r\n     */\r\n    public dispose(): void {\r\n        this._physicsPlugin.disposeConstraint(this);\r\n    }\r\n}\r\n\r\n/**\r\n * This describes a single limit used by Physics6DoFConstraint\r\n */\r\nexport class Physics6DoFLimit {\r\n    /**\r\n     * The axis ID to limit\r\n     */\r\n    axis: PhysicsConstraintAxis;\r\n    /**\r\n     * An optional minimum limit for the axis.\r\n     * Corresponds to a distance in meters for linear axes, an angle in radians for angular axes.\r\n     */\r\n    minLimit?: number;\r\n    /**\r\n     * An optional maximum limit for the axis.\r\n     * Corresponds to a distance in meters for linear axes, an angle in radians for angular axes.\r\n     */\r\n    maxLimit?: number;\r\n    /**\r\n     * The stiffness of the constraint.\r\n     */\r\n    stiffness?: number;\r\n    /**\r\n     * A constraint parameter that specifies damping.\r\n     */\r\n    damping?: number;\r\n}\r\n\r\n/**\r\n * A generic constraint, which can be used to build more complex constraints than those specified\r\n * in PhysicsConstraintType. The axis and pivot options in PhysicsConstraintParameters define the space\r\n * the constraint operates in. This constraint contains a set of limits, which restrict the\r\n * relative movement of the bodies in that coordinate system\r\n */\r\nexport class Physics6DoFConstraint extends PhysicsConstraint {\r\n    /**\r\n     * The collection of limits which this constraint will apply\r\n     */\r\n    public limits: Physics6DoFLimit[];\r\n\r\n    constructor(constraintParams: PhysicsConstraintParameters, limits: Physics6DoFLimit[], scene: Scene) {\r\n        super(PhysicsConstraintType.SIX_DOF, constraintParams, scene);\r\n        this.limits = limits;\r\n    }\r\n\r\n    /**\r\n     * Sets the friction of the given axis of the physics engine.\r\n     * @param axis - The axis of the physics engine to set the friction for.\r\n     * @param friction - The friction to set for the given axis.\r\n     *\r\n     */\r\n    public setAxisFriction(axis: PhysicsConstraintAxis, friction: number): void {\r\n        this._physicsPlugin.setAxisFriction(this, axis, friction);\r\n    }\r\n\r\n    /**\r\n     * Gets the friction of the given axis of the physics engine.\r\n     * @param axis - The axis of the physics engine.\r\n     * @returns The friction of the given axis, or null if the constraint hasn't been initialized yet.\r\n     *\r\n     */\r\n    public getAxisFriction(axis: PhysicsConstraintAxis): Nullable<number> {\r\n        return this._physicsPlugin.getAxisFriction(this, axis);\r\n    }\r\n\r\n    /**\r\n     * Sets the limit mode for the given axis of the constraint.\r\n     * @param axis The axis to set the limit mode for.\r\n     * @param limitMode The limit mode to set.\r\n     *\r\n     * This method is useful for setting the limit mode for a given axis of the constraint. This is important for\r\n     * controlling the behavior of the physics engine when the constraint is reached. By setting the limit mode,\r\n     * the engine can be configured to either stop the motion of the objects, or to allow them to continue\r\n     * moving beyond the constraint.\r\n     */\r\n    public setAxisMode(axis: PhysicsConstraintAxis, limitMode: PhysicsConstraintAxisLimitMode): void {\r\n        this._physicsPlugin.setAxisMode(this, axis, limitMode);\r\n    }\r\n\r\n    /**\r\n     * Gets the limit mode of the given axis of the constraint.\r\n     *\r\n     * @param axis - The axis of the constraint.\r\n     * @returns The limit mode of the given axis, or null if the constraint hasn't been initialized yet.\r\n     *\r\n     */\r\n    public getAxisMode(axis: PhysicsConstraintAxis): Nullable<PhysicsConstraintAxisLimitMode> {\r\n        return this._physicsPlugin.getAxisMode(this, axis);\r\n    }\r\n\r\n    /**\r\n     * Sets the minimum limit of a given axis of a constraint.\r\n     * @param axis - The axis of the constraint.\r\n     * @param minLimit - The minimum limit of the axis.\r\n     *\r\n     */\r\n    public setAxisMinLimit(axis: PhysicsConstraintAxis, minLimit: number): void {\r\n        this._physicsPlugin.setAxisMinLimit(this, axis, minLimit);\r\n    }\r\n\r\n    /**\r\n     * Gets the minimum limit of the given axis of the physics engine.\r\n     * @param axis - The axis of the physics engine.\r\n     * @returns The minimum limit of the given axis, or null if the constraint hasn't been initialized yet.\r\n     *\r\n     */\r\n    public getAxisMinLimit(axis: PhysicsConstraintAxis): Nullable<number> {\r\n        return this._physicsPlugin.getAxisMinLimit(this, axis);\r\n    }\r\n\r\n    /**\r\n     * Sets the maximum limit of the given axis for the physics engine.\r\n     * @param axis - The axis to set the limit for.\r\n     * @param limit - The maximum limit of the axis.\r\n     *\r\n     * This method is useful for setting the maximum limit of the given axis for the physics engine,\r\n     * which can be used to control the movement of the physics object. This helps to ensure that the\r\n     * physics object does not move beyond the given limit.\r\n     */\r\n    public setAxisMaxLimit(axis: PhysicsConstraintAxis, limit: number): void {\r\n        this._physicsPlugin.setAxisMaxLimit(this, axis, limit);\r\n    }\r\n\r\n    /**\r\n     * Gets the maximum limit of the given axis of the physics engine.\r\n     * @param axis - The axis of the physics engine.\r\n     * @returns The maximum limit of the given axis, or null if the constraint hasn't been initialized yet.\r\n     *\r\n     */\r\n    public getAxisMaxLimit(axis: PhysicsConstraintAxis): Nullable<number> {\r\n        return this._physicsPlugin.getAxisMaxLimit(this, axis);\r\n    }\r\n\r\n    /**\r\n     * Sets the motor type of the given axis of the constraint.\r\n     * @param axis - The axis of the constraint.\r\n     * @param motorType - The type of motor to use.\r\n     */\r\n    public setAxisMotorType(axis: PhysicsConstraintAxis, motorType: PhysicsConstraintMotorType): void {\r\n        this._physicsPlugin.setAxisMotorType(this, axis, motorType);\r\n    }\r\n\r\n    /**\r\n     * Gets the motor type of the specified axis of the constraint.\r\n     *\r\n     * @param axis - The axis of the constraint.\r\n     * @returns The motor type of the specified axis, or null if the constraint hasn't been initialized yet.\r\n     *\r\n     */\r\n    public getAxisMotorType(axis: PhysicsConstraintAxis): Nullable<PhysicsConstraintMotorType> {\r\n        return this._physicsPlugin.getAxisMotorType(this, axis);\r\n    }\r\n\r\n    /**\r\n     * Sets the target velocity of the motor associated with the given axis of the constraint.\r\n     * @param axis - The axis of the constraint.\r\n     * @param target - The target velocity of the motor.\r\n     *\r\n     * This method is useful for setting the target velocity of the motor associated with the given axis of the constraint.\r\n     */\r\n    public setAxisMotorTarget(axis: PhysicsConstraintAxis, target: number): void {\r\n        this._physicsPlugin.setAxisMotorTarget(this, axis, target);\r\n    }\r\n\r\n    /**\r\n     * Gets the target velocity of the motor associated to the given constraint axis.\r\n     * @param axis - The constraint axis associated to the motor.\r\n     * @returns The target velocity of the motor, or null if the constraint hasn't been initialized yet.\r\n     *\r\n     */\r\n    public getAxisMotorTarget(axis: PhysicsConstraintAxis): Nullable<number> {\r\n        return this._physicsPlugin.getAxisMotorTarget(this, axis);\r\n    }\r\n\r\n    /**\r\n     * Sets the maximum force of the motor of the given axis of the constraint.\r\n     * @param axis - The axis of the constraint.\r\n     * @param maxForce - The maximum force of the motor.\r\n     *\r\n     */\r\n    public setAxisMotorMaxForce(axis: PhysicsConstraintAxis, maxForce: number): void {\r\n        this._physicsPlugin.setAxisMotorMaxForce(this, axis, maxForce);\r\n    }\r\n\r\n    /**\r\n     * Gets the maximum force of the motor of the given axis of the constraint.\r\n     * @param axis - The axis of the constraint.\r\n     * @returns The maximum force of the motor, or null if the constraint hasn't been initialized yet.\r\n     *\r\n     */\r\n    public getAxisMotorMaxForce(axis: PhysicsConstraintAxis): Nullable<number> {\r\n        return this._physicsPlugin.getAxisMotorMaxForce(this, axis);\r\n    }\r\n}\r\n\r\n/**\r\n * Represents a Ball and Socket Constraint, used to simulate a joint\r\n *\r\n * @param pivotA - The first pivot, defined locally in the first body frame\r\n * @param pivotB - The second pivot, defined locally in the second body frame\r\n * @param axisA - The axis of the first body\r\n * @param axisB - The axis of the second body\r\n * @param scene - The scene the constraint is applied to\r\n * @returns The Ball and Socket Constraint\r\n *\r\n * This class is useful for simulating a joint between two bodies in a physics engine.\r\n * It allows for the two bodies to move relative to each other in a way that mimics a ball and socket joint, such as a shoulder or hip joint.\r\n */\r\nexport class BallAndSocketConstraint extends PhysicsConstraint {\r\n    constructor(pivotA: Vector3, pivotB: Vector3, axisA: Vector3, axisB: Vector3, scene: Scene) {\r\n        super(PhysicsConstraintType.BALL_AND_SOCKET, { pivotA: pivotA, pivotB: pivotB, axisA: axisA, axisB: axisB }, scene);\r\n    }\r\n}\r\n\r\n/**\r\n * Creates a distance constraint.\r\n * @param maxDistance distance between bodies\r\n * @param scene The scene the constraint belongs to\r\n * @returns DistanceConstraint\r\n *\r\n * This code is useful for creating a distance constraint in a physics engine.\r\n * A distance constraint is a type of constraint that keeps two objects at a certain distance from each other.\r\n * The scene is used to add the constraint to the physics engine.\r\n */\r\nexport class DistanceConstraint extends PhysicsConstraint {\r\n    constructor(maxDistance: number, scene: Scene) {\r\n        super(PhysicsConstraintType.DISTANCE, { maxDistance: maxDistance }, scene);\r\n    }\r\n}\r\n\r\n/**\r\n * Creates a HingeConstraint, which is a type of PhysicsConstraint.\r\n *\r\n * @param pivotA - The first pivot point, in world space.\r\n * @param pivotB - The second pivot point, in world space.\r\n * @param scene - The scene the constraint is used in.\r\n * @returns The new HingeConstraint.\r\n *\r\n * This code is useful for creating a HingeConstraint, which is a type of PhysicsConstraint.\r\n * This constraint is used to simulate a hinge joint between two rigid bodies, allowing them to rotate around a single axis.\r\n */\r\nexport class HingeConstraint extends PhysicsConstraint {\r\n    constructor(pivotA: Vector3, pivotB: Vector3, axisA: Vector3, axisB: Vector3, scene: Scene) {\r\n        super(PhysicsConstraintType.HINGE, { pivotA: pivotA, pivotB: pivotB, axisA: axisA, axisB: axisB }, scene);\r\n    }\r\n}\r\n\r\n/**\r\n * Creates a SliderConstraint, which is a type of PhysicsConstraint.\r\n *\r\n * @param pivotA - The first pivot of the constraint, in world space.\r\n * @param pivotB - The second pivot of the constraint, in world space.\r\n * @param axisA - The first axis of the constraint, in world space.\r\n * @param axisB - The second axis of the constraint, in world space.\r\n * @param scene - The scene the constraint belongs to.\r\n * @returns The created SliderConstraint.\r\n *\r\n * This code is useful for creating a SliderConstraint, which is a type of PhysicsConstraint.\r\n * It allows the user to specify the two pivots and two axes of the constraint in world space, as well as the scene the constraint belongs to.\r\n * This is useful for creating a constraint between two rigid bodies that allows them to move along a certain axis.\r\n */\r\nexport class SliderConstraint extends PhysicsConstraint {\r\n    constructor(pivotA: Vector3, pivotB: Vector3, axisA: Vector3, axisB: Vector3, scene: Scene) {\r\n        super(PhysicsConstraintType.SLIDER, { pivotA: pivotA, pivotB: pivotB, axisA: axisA, axisB: axisB }, scene);\r\n    }\r\n}\r\n\r\n/**\r\n * Creates a LockConstraint, which is a type of PhysicsConstraint.\r\n *\r\n * @param pivotA - The first pivot of the constraint in local space.\r\n * @param pivotB - The second pivot of the constraint in local space.\r\n * @param axisA - The first axis of the constraint in local space.\r\n * @param axisB - The second axis of the constraint in local space.\r\n * @param scene - The scene the constraint belongs to.\r\n * @returns The created LockConstraint.\r\n *\r\n * This code is useful for creating a LockConstraint, which is a type of PhysicsConstraint.\r\n * It takes in two pivots and two axes in local space, as well as the scene the constraint belongs to, and creates a LockConstraint.\r\n */\r\nexport class LockConstraint extends PhysicsConstraint {\r\n    constructor(pivotA: Vector3, pivotB: Vector3, axisA: Vector3, axisB: Vector3, scene: Scene) {\r\n        super(PhysicsConstraintType.LOCK, { pivotA: pivotA, pivotB: pivotB, axisA: axisA, axisB: axisB }, scene);\r\n    }\r\n}\r\n\r\n/**\r\n * Creates a PrismaticConstraint, which is a type of PhysicsConstraint.\r\n *\r\n * @param pivotA - The first pivot of the constraint in local space.\r\n * @param pivotB - The second pivot of the constraint in local space.\r\n * @param axisA - The first axis of the constraint in local space.\r\n * @param axisB - The second axis of the constraint in local space.\r\n * @param scene - The scene the constraint belongs to.\r\n * @returns The created LockConstraint.\r\n *\r\n * This code is useful for creating a PrismaticConstraint, which is a type of PhysicsConstraint.\r\n * It takes in two pivots and two axes in local space, as well as the scene the constraint belongs to, and creates a PrismaticConstraint.\r\n */\r\nexport class PrismaticConstraint extends PhysicsConstraint {\r\n    constructor(pivotA: Vector3, pivotB: Vector3, axisA: Vector3, axisB: Vector3, scene: Scene) {\r\n        super(PhysicsConstraintType.PRISMATIC, { pivotA: pivotA, pivotB: pivotB, axisA: axisA, axisB: axisB }, scene);\r\n    }\r\n}\r\n\r\n/**\r\n * Creates a SpringConstraint, which is a type of Physics6DoFConstraint. This constraint applies a force at the ends which is proportional\r\n * to the distance between ends, and a stiffness and damping factor. The force is calculated as (stiffness * positionError) - (damping * velocity)\r\n *\r\n * @param pivotA - The first pivot of the constraint in local space.\r\n * @param pivotB - The second pivot of the constraint in local space.\r\n * @param axisA - The first axis of the constraint in local space.\r\n * @param axisB - The second axis of the constraint in local space.\r\n * @param minDistance - The minimum distance between the two pivots.\r\n * @param maxDistance - The maximum distance between the two pivots.\r\n * @param stiffness - The stiffness of the spring.\r\n * @param damping - The damping of the spring.\r\n * @param scene - The scene the constraint belongs to.\r\n * @returns The created SpringConstraint.\r\n */\r\nexport class SpringConstraint extends Physics6DoFConstraint {\r\n    constructor(pivotA: Vector3, pivotB: Vector3, axisA: Vector3, axisB: Vector3, minDistance: number, maxDistance: number, stiffness: number, damping: number, scene: Scene) {\r\n        super({ pivotA, pivotB, axisA, axisB }, [{ axis: PhysicsConstraintAxis.LINEAR_DISTANCE, minLimit: minDistance, maxLimit: maxDistance, stiffness, damping }], scene);\r\n    }\r\n}\r\n"]}