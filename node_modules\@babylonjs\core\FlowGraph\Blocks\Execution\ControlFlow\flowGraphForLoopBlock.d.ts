import type { FlowGraphSignalConnection } from "../../../flowGraphSignalConnection";
import type { FlowGraphDataConnection } from "../../../flowGraphDataConnection";
import { FlowGraphExecutionBlockWithOutSignal } from "../../../flowGraphExecutionBlockWithOutSignal.js";
import type { FlowGraphContext } from "../../../flowGraphContext";
import type { IFlowGraphBlockConfiguration } from "../../../flowGraphBlock";
/**
 * @experimental
 * Block that executes an action in a loop.
 */
export declare class FlowGraphForLoopBlock extends FlowGraphExecutionBlockWithOutSignal {
    /**
     * Input connection: The start index of the loop.
     */
    readonly startIndex: FlowGraphDataConnection<number>;
    /**
     * Input connection: The end index of the loop.
     */
    readonly endIndex: FlowGraphDataConnection<number>;
    /**
     * Input connection: The step of the loop.
     */
    readonly step: FlowGraphDataConnection<number>;
    /**
     * Output connection: The current index of the loop.
     */
    readonly index: FlowGraphDataConnection<number>;
    /**
     * Output connection: The signal that is activated when the loop body is executed.
     */
    readonly onLoop: FlowGraphSignalConnection;
    constructor(config?: IFlowGraphBlockConfiguration);
    private _executeLoop;
    /**
     * @internal
     */
    _execute(context: FlowGraphContext): void;
    /**
     * @returns class name of the block.
     */
    getClassName(): string;
}
