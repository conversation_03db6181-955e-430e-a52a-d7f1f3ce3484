{"version": 3, "file": "refractionPostProcess.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/refractionPostProcess.ts"], "names": [], "mappings": ";AAGA,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAExD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAG5C,OAAO,gCAAgC,CAAC;AACxC,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AAKvE;;;GAGG;AACH,MAAM,OAAO,qBAAsB,SAAQ,WAAW;IAoBlD;;;OAGG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,iBAAiB,CAAC,KAAc;QACvC,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAChD,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;SAC9B;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,uBAAuB,CAAC;IACnC,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,YACI,IAAY,EACZ,oBAA4B,EAC5B,KAAa,EACb,KAAa,EACb,UAAkB,EAClB,OAAoC,EACpC,MAAwB,EACxB,YAAqB,EACrB,MAAe,EACf,QAAkB;QAElB,KAAK,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE,CAAC,mBAAmB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QArEpI,0BAAqB,GAAG,IAAI,CAAC;QAuEjC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QAEjD,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE;YAC1C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,OAAO,CAAC,oBAAoB,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAc,EAAE,EAAE;YAC1C,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAE/C,MAAM,CAAC,UAAU,CAAC,mBAAmB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACP,CAAC;IAED,UAAU;IACV;;;OAGG;IACI,OAAO,CAAC,MAAc;QACzB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAChD,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YACrB,IAAI,CAAC,WAAY,GAAG,IAAI,CAAC;SAClC;QAED,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,iBAAsB,EAAE,YAAoB,EAAE,KAAY,EAAE,OAAe;QAC5F,OAAO,mBAAmB,CAAC,KAAK,CAC5B,GAAG,EAAE;YACD,OAAO,IAAI,qBAAqB,CAC5B,iBAAiB,CAAC,IAAI,EACtB,iBAAiB,CAAC,oBAAoB,EACtC,iBAAiB,CAAC,KAAK,EACvB,iBAAiB,CAAC,KAAK,EACvB,iBAAiB,CAAC,UAAU,EAC5B,iBAAiB,CAAC,OAAO,EACzB,YAAY,EACZ,iBAAiB,CAAC,wBAAwB,EAC1C,KAAK,CAAC,SAAS,EAAE,EACjB,iBAAiB,CAAC,QAAQ,CAC7B,CAAC;QACN,CAAC,EACD,iBAAiB,EACjB,KAAK,EACL,OAAO,CACV,CAAC;IACN,CAAC;CACJ;AA3HU;IADN,SAAS,EAAE;oDACS;AAId;IADN,SAAS,EAAE;oDACS;AAId;IADN,SAAS,EAAE;yDACc;AAInB;IADN,SAAS,EAAE;mEACwB;AAiHxC,aAAa,CAAC,+BAA+B,EAAE,qBAAqB,CAAC,CAAC", "sourcesContent": ["import type { Color3 } from \"../Maths/math.color\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport type { PostProcessOptions } from \"./postProcess\";\r\nimport { PostProcess } from \"./postProcess\";\r\nimport type { Engine } from \"../Engines/engine\";\r\n\r\nimport \"../Shaders/refraction.fragment\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport { serialize } from \"../Misc/decorators\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\nimport type { Nullable } from \"../types\";\r\n\r\nimport type { Scene } from \"../scene\";\r\n\r\n/**\r\n * Post process which applies a refraction texture\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/usePostProcesses#refraction\r\n */\r\nexport class RefractionPostProcess extends PostProcess {\r\n    private _refTexture: Texture;\r\n    private _ownRefractionTexture = true;\r\n\r\n    /** the base color of the refraction (used to taint the rendering) */\r\n    @serialize()\r\n    public color: Color3;\r\n\r\n    /** simulated refraction depth */\r\n    @serialize()\r\n    public depth: number;\r\n\r\n    /** the coefficient of the base color (0 to remove base color tainting) */\r\n    @serialize()\r\n    public colorLevel: number;\r\n\r\n    /** Gets the url used to load the refraction texture */\r\n    @serialize()\r\n    public refractionTextureUrl: string;\r\n\r\n    /**\r\n     * Gets or sets the refraction texture\r\n     * Please note that you are responsible for disposing the texture if you set it manually\r\n     */\r\n    public get refractionTexture(): Texture {\r\n        return this._refTexture;\r\n    }\r\n\r\n    public set refractionTexture(value: Texture) {\r\n        if (this._refTexture && this._ownRefractionTexture) {\r\n            this._refTexture.dispose();\r\n        }\r\n\r\n        this._refTexture = value;\r\n        this._ownRefractionTexture = false;\r\n    }\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"RefractionPostProcess\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"RefractionPostProcess\";\r\n    }\r\n\r\n    /**\r\n     * Initializes the RefractionPostProcess\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/usePostProcesses#refraction\r\n     * @param name The name of the effect.\r\n     * @param refractionTextureUrl Url of the refraction texture to use\r\n     * @param color the base color of the refraction (used to taint the rendering)\r\n     * @param depth simulated refraction depth\r\n     * @param colorLevel the coefficient of the base color (0 to remove base color tainting)\r\n     * @param options The required width/height ratio to downsize to before computing the render pass.\r\n     * @param camera The camera to apply the render pass to.\r\n     * @param samplingMode The sampling mode to be used when computing the pass. (default: 0)\r\n     * @param engine The engine which the post process will be applied. (default: current engine)\r\n     * @param reusable If the post process can be reused on the same frame. (default: false)\r\n     */\r\n    constructor(\r\n        name: string,\r\n        refractionTextureUrl: string,\r\n        color: Color3,\r\n        depth: number,\r\n        colorLevel: number,\r\n        options: number | PostProcessOptions,\r\n        camera: Nullable<Camera>,\r\n        samplingMode?: number,\r\n        engine?: Engine,\r\n        reusable?: boolean\r\n    ) {\r\n        super(name, \"refraction\", [\"baseColor\", \"depth\", \"colorLevel\"], [\"refractionSampler\"], options, camera, samplingMode, engine, reusable);\r\n\r\n        this.color = color;\r\n        this.depth = depth;\r\n        this.colorLevel = colorLevel;\r\n        this.refractionTextureUrl = refractionTextureUrl;\r\n\r\n        this.onActivateObservable.add((cam: Camera) => {\r\n            this._refTexture = this._refTexture || new Texture(refractionTextureUrl, cam.getScene());\r\n        });\r\n\r\n        this.onApplyObservable.add((effect: Effect) => {\r\n            effect.setColor3(\"baseColor\", this.color);\r\n            effect.setFloat(\"depth\", this.depth);\r\n            effect.setFloat(\"colorLevel\", this.colorLevel);\r\n\r\n            effect.setTexture(\"refractionSampler\", this._refTexture);\r\n        });\r\n    }\r\n\r\n    // Methods\r\n    /**\r\n     * Disposes of the post process\r\n     * @param camera Camera to dispose post process on\r\n     */\r\n    public dispose(camera: Camera): void {\r\n        if (this._refTexture && this._ownRefractionTexture) {\r\n            this._refTexture.dispose();\r\n            (<any>this._refTexture) = null;\r\n        }\r\n\r\n        super.dispose(camera);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _Parse(parsedPostProcess: any, targetCamera: Camera, scene: Scene, rootUrl: string) {\r\n        return SerializationHelper.Parse(\r\n            () => {\r\n                return new RefractionPostProcess(\r\n                    parsedPostProcess.name,\r\n                    parsedPostProcess.refractionTextureUrl,\r\n                    parsedPostProcess.color,\r\n                    parsedPostProcess.depth,\r\n                    parsedPostProcess.colorLevel,\r\n                    parsedPostProcess.options,\r\n                    targetCamera,\r\n                    parsedPostProcess.renderTargetSamplingMode,\r\n                    scene.getEngine(),\r\n                    parsedPostProcess.reusable\r\n                );\r\n            },\r\n            parsedPostProcess,\r\n            scene,\r\n            rootUrl\r\n        );\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.RefractionPostProcess\", RefractionPostProcess);\r\n"]}