{"version": 3, "file": "screenSpaceCurvaturePostProcess.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/screenSpaceCurvaturePostProcess.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAIxC,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAGjD,OAAO,mDAAmD,CAAC;AAC3D,OAAO,0CAA0C,CAAC;AAClD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AAKvE;;GAEG;AACH,MAAM,OAAO,+BAAgC,SAAQ,WAAW;IAe5D;;;OAGG;IACI,YAAY;QACf,OAAO,iCAAiC,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;;OAWG;IACH,YACI,IAAY,EACZ,KAAY,EACZ,OAAoC,EACpC,MAAwB,EACxB,YAAqB,EACrB,MAAe,EACf,QAAkB,EAClB,cAAsB,SAAS,CAAC,wBAAwB,EACxD,gBAAgB,GAAG,KAAK;QAExB,KAAK,CACD,IAAI,EACJ,sBAAsB,EACtB,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,EACvC,CAAC,gBAAgB,EAAE,eAAe,CAAC,EACnC,OAAO,EACP,MAAM,EACN,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,SAAS,EACT,WAAW,EACX,SAAS,EACT,IAAI,EACJ,gBAAgB,CACnB,CAAC;QA5DN;;WAEG;QAEI,UAAK,GAAW,CAAC,CAAC;QAEzB;;WAEG;QAEI,WAAM,GAAW,CAAC,CAAC;QAoDtB,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC,4BAA4B,EAAE,CAAC;QAEpE,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,wEAAwE;YACxE,MAAM,CAAC,KAAK,CAAC,mHAAmH,CAAC,CAAC;SACrI;aAAM;YACH,IAAI,IAAI,CAAC,uBAAuB,CAAC,2BAA2B,EAAE;gBAC1D,MAAM,CAAC,KAAK,CAAC,qHAAqH,CAAC,CAAC;aACvI;YAED,yCAAyC;YACzC,IAAI,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;gBAC9B,MAAM,CAAC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;gBAClF,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;gBAErF,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAwB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC7E,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;YACtD,CAAC,CAAC;SACL;IACL,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,WAAW;QACzB,MAAM,MAAM,GAAG,WAAW,CAAC,iBAAiB,CAAC;QAC7C,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC,oBAAoB,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,iBAAsB,EAAE,YAAoB,EAAE,KAAY,EAAE,OAAe;QAC5F,OAAO,mBAAmB,CAAC,KAAK,CAC5B,GAAG,EAAE;YACD,OAAO,IAAI,+BAA+B,CACtC,iBAAiB,CAAC,IAAI,EACtB,KAAK,EACL,iBAAiB,CAAC,OAAO,EACzB,YAAY,EACZ,iBAAiB,CAAC,wBAAwB,EAC1C,KAAK,CAAC,SAAS,EAAE,EACjB,iBAAiB,CAAC,WAAW,EAC7B,iBAAiB,CAAC,QAAQ,CAC7B,CAAC;QACN,CAAC,EACD,iBAAiB,EACjB,KAAK,EACL,OAAO,CACV,CAAC;IACN,CAAC;CACJ;AAjHU;IADN,SAAS,EAAE;8DACa;AAMlB;IADN,SAAS,EAAE;+DACc;AA6G9B,aAAa,CAAC,yCAAyC,EAAE,+BAA+B,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport type { PostProcessOptions } from \"./postProcess\";\r\nimport { PostProcess } from \"./postProcess\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { GeometryBufferRenderer } from \"../Rendering/geometryBufferRenderer\";\r\n\r\nimport \"../Rendering/geometryBufferRendererSceneComponent\";\r\nimport \"../Shaders/screenSpaceCurvature.fragment\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport { serialize } from \"../Misc/decorators\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\n\r\nimport type { Engine } from \"../Engines/engine\";\r\nimport type { Scene } from \"../scene\";\r\n\r\n/**\r\n * The Screen Space curvature effect can help highlighting ridge and valley of a model.\r\n */\r\nexport class ScreenSpaceCurvaturePostProcess extends PostProcess {\r\n    /**\r\n     * Defines how much ridge the curvature effect displays.\r\n     */\r\n    @serialize()\r\n    public ridge: number = 1;\r\n\r\n    /**\r\n     * Defines how much valley the curvature effect displays.\r\n     */\r\n    @serialize()\r\n    public valley: number = 1;\r\n\r\n    private _geometryBufferRenderer: Nullable<GeometryBufferRenderer>;\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"ScreenSpaceCurvaturePostProcess\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"ScreenSpaceCurvaturePostProcess\";\r\n    }\r\n\r\n    /**\r\n     * Creates a new instance ScreenSpaceCurvaturePostProcess\r\n     * @param name The name of the effect.\r\n     * @param scene The scene containing the objects to blur according to their velocity.\r\n     * @param options The required width/height ratio to downsize to before computing the render pass.\r\n     * @param camera The camera to apply the render pass to.\r\n     * @param samplingMode The sampling mode to be used when computing the pass. (default: 0)\r\n     * @param engine The engine which the post process will be applied. (default: current engine)\r\n     * @param reusable If the post process can be reused on the same frame. (default: false)\r\n     * @param textureType Type of textures used when performing the post process. (default: 0)\r\n     * @param blockCompilation If compilation of the shader should not be done in the constructor. The updateEffect method can be used to compile the shader at a later time. (default: false)\r\n     */\r\n    constructor(\r\n        name: string,\r\n        scene: Scene,\r\n        options: number | PostProcessOptions,\r\n        camera: Nullable<Camera>,\r\n        samplingMode?: number,\r\n        engine?: Engine,\r\n        reusable?: boolean,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        blockCompilation = false\r\n    ) {\r\n        super(\r\n            name,\r\n            \"screenSpaceCurvature\",\r\n            [\"curvature_ridge\", \"curvature_valley\"],\r\n            [\"textureSampler\", \"normalSampler\"],\r\n            options,\r\n            camera,\r\n            samplingMode,\r\n            engine,\r\n            reusable,\r\n            undefined,\r\n            textureType,\r\n            undefined,\r\n            null,\r\n            blockCompilation\r\n        );\r\n\r\n        this._geometryBufferRenderer = scene.enableGeometryBufferRenderer();\r\n\r\n        if (!this._geometryBufferRenderer) {\r\n            // Geometry buffer renderer is not supported. So, work as a passthrough.\r\n            Logger.Error(\"Multiple Render Target support needed for screen space curvature post process. Please use IsSupported test first.\");\r\n        } else {\r\n            if (this._geometryBufferRenderer.generateNormalsInWorldSpace) {\r\n                Logger.Error(\"ScreenSpaceCurvaturePostProcess does not support generateNormalsInWorldSpace=true for the geometry buffer renderer!\");\r\n            }\r\n\r\n            // Geometry buffer renderer is supported.\r\n            this.onApply = (effect: Effect) => {\r\n                effect.setFloat(\"curvature_ridge\", 0.5 / Math.max(this.ridge * this.ridge, 1e-4));\r\n                effect.setFloat(\"curvature_valley\", 0.7 / Math.max(this.valley * this.valley, 1e-4));\r\n\r\n                const normalTexture = this._geometryBufferRenderer!.getGBuffer().textures[1];\r\n                effect.setTexture(\"normalSampler\", normalTexture);\r\n            };\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Support test.\r\n     */\r\n    public static get IsSupported(): boolean {\r\n        const engine = EngineStore.LastCreatedEngine;\r\n        if (!engine) {\r\n            return false;\r\n        }\r\n\r\n        return engine.getCaps().drawBuffersExtension;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _Parse(parsedPostProcess: any, targetCamera: Camera, scene: Scene, rootUrl: string) {\r\n        return SerializationHelper.Parse(\r\n            () => {\r\n                return new ScreenSpaceCurvaturePostProcess(\r\n                    parsedPostProcess.name,\r\n                    scene,\r\n                    parsedPostProcess.options,\r\n                    targetCamera,\r\n                    parsedPostProcess.renderTargetSamplingMode,\r\n                    scene.getEngine(),\r\n                    parsedPostProcess.textureType,\r\n                    parsedPostProcess.reusable\r\n                );\r\n            },\r\n            parsedPostProcess,\r\n            scene,\r\n            rootUrl\r\n        );\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ScreenSpaceCurvaturePostProcess\", ScreenSpaceCurvaturePostProcess);\r\n"]}