{"version": 3, "file": "shaderDefineAndOperator.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Engines/Processors/Expressions/Operators/shaderDefineAndOperator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,2BAA2B,CAAC;AAEnE,gBAAgB;AAChB,MAAM,OAAO,uBAAwB,SAAQ,sBAAsB;IAIxD,MAAM,CAAC,aAAwC;QAClD,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAC7F,CAAC;CACJ", "sourcesContent": ["import { ShaderDefineExpression } from \"../shaderDefineExpression\";\r\n\r\n/** @internal */\r\nexport class ShaderDefineAndOperator extends ShaderDefineExpression {\r\n    public leftOperand: ShaderDefineExpression;\r\n    public rightOperand: ShaderDefineExpression;\r\n\r\n    public isTrue(preprocessors: { [key: string]: string }): boolean {\r\n        return this.leftOperand.isTrue(preprocessors) && this.rightOperand.isTrue(preprocessors);\r\n    }\r\n}\r\n"]}