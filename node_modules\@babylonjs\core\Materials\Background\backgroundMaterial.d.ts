import type { Nullable, int, float } from "../../types";
import type { Scene } from "../../scene";
import type { Matrix } from "../../Maths/math.vector";
import { Vector3 } from "../../Maths/math.vector";
import type { SubMesh } from "../../Meshes/subMesh";
import type { AbstractMesh } from "../../Meshes/abstractMesh";
import type { Mesh } from "../../Meshes/mesh";
import { PushMaterial } from "../../Materials/pushMaterial";
import type { ColorCurves } from "../../Materials/colorCurves";
import { ImageProcessingConfiguration } from "../../Materials/imageProcessingConfiguration";
import type { BaseTexture } from "../../Materials/Textures/baseTexture";
import type { IShadowLight } from "../../Lights/shadowLight";
import { Color3 } from "../../Maths/math.color";
import "../../Shaders/background.fragment";
import "../../Shaders/background.vertex";
/**
 * Background material used to create an efficient environment around your scene.
 */
export declare class BackgroundMaterial extends PushMaterial {
    /**
     * Standard reflectance value at parallel view angle.
     */
    static StandardReflectance0: number;
    /**
     * Standard reflectance value at grazing angle.
     */
    static StandardReflectance90: number;
    protected _primaryColor: Color3;
    /**
     * Key light Color (multiply against the environment texture)
     */
    primaryColor: Color3;
    protected __perceptualColor: Nullable<Color3>;
    /**
     * Experimental Internal Use Only.
     *
     * Key light Color in "perceptual value" meaning the color you would like to see on screen.
     * This acts as a helper to set the primary color to a more "human friendly" value.
     * Conversion to linear space as well as exposure and tone mapping correction will be applied to keep the
     * output color as close as possible from the chosen value.
     * (This does not account for contrast color grading and color curves as they are considered post effect and not directly
     * part of lighting setup.)
     */
    get _perceptualColor(): Nullable<Color3>;
    set _perceptualColor(value: Nullable<Color3>);
    protected _primaryColorShadowLevel: float;
    /**
     * Defines the level of the shadows (dark area of the reflection map) in order to help scaling the colors.
     * The color opposite to the primary color is used at the level chosen to define what the black area would look.
     */
    get primaryColorShadowLevel(): float;
    set primaryColorShadowLevel(value: float);
    protected _primaryColorHighlightLevel: float;
    /**
     * Defines the level of the highlights (highlight area of the reflection map) in order to help scaling the colors.
     * The primary color is used at the level chosen to define what the white area would look.
     */
    get primaryColorHighlightLevel(): float;
    set primaryColorHighlightLevel(value: float);
    protected _reflectionTexture: Nullable<BaseTexture>;
    /**
     * Reflection Texture used in the material.
     * Should be author in a specific way for the best result (refer to the documentation).
     */
    reflectionTexture: Nullable<BaseTexture>;
    protected _reflectionBlur: float;
    /**
     * Reflection Texture level of blur.
     *
     * Can be use to reuse an existing HDR Texture and target a specific LOD to prevent authoring the
     * texture twice.
     */
    reflectionBlur: float;
    protected _diffuseTexture: Nullable<BaseTexture>;
    /**
     * Diffuse Texture used in the material.
     * Should be author in a specific way for the best result (refer to the documentation).
     */
    diffuseTexture: Nullable<BaseTexture>;
    protected _shadowLights: Nullable<IShadowLight[]>;
    /**
     * Specify the list of lights casting shadow on the material.
     * All scene shadow lights will be included if null.
     */
    shadowLights: Nullable<IShadowLight[]>;
    protected _shadowLevel: float;
    /**
     * Helps adjusting the shadow to a softer level if required.
     * 0 means black shadows and 1 means no shadows.
     */
    shadowLevel: float;
    protected _sceneCenter: Vector3;
    /**
     * In case of opacity Fresnel or reflection falloff, this is use as a scene center.
     * It is usually zero but might be interesting to modify according to your setup.
     */
    sceneCenter: Vector3;
    protected _opacityFresnel: boolean;
    /**
     * This helps specifying that the material is falling off to the sky box at grazing angle.
     * This helps ensuring a nice transition when the camera goes under the ground.
     */
    opacityFresnel: boolean;
    protected _reflectionFresnel: boolean;
    /**
     * This helps specifying that the material is falling off from diffuse to the reflection texture at grazing angle.
     * This helps adding a mirror texture on the ground.
     */
    reflectionFresnel: boolean;
    protected _reflectionFalloffDistance: number;
    /**
     * This helps specifying the falloff radius off the reflection texture from the sceneCenter.
     * This helps adding a nice falloff effect to the reflection if used as a mirror for instance.
     */
    reflectionFalloffDistance: number;
    protected _reflectionAmount: number;
    /**
     * This specifies the weight of the reflection against the background in case of reflection Fresnel.
     */
    reflectionAmount: number;
    protected _reflectionReflectance0: number;
    /**
     * This specifies the weight of the reflection at grazing angle.
     */
    reflectionReflectance0: number;
    protected _reflectionReflectance90: number;
    /**
     * This specifies the weight of the reflection at a perpendicular point of view.
     */
    reflectionReflectance90: number;
    /**
     * Sets the reflection reflectance fresnel values according to the default standard
     * empirically know to work well :-)
     */
    set reflectionStandardFresnelWeight(value: number);
    protected _useRGBColor: boolean;
    /**
     * Helps to directly use the maps channels instead of their level.
     */
    useRGBColor: boolean;
    protected _enableNoise: boolean;
    /**
     * This helps reducing the banding effect that could occur on the background.
     */
    enableNoise: boolean;
    /**
     * The current fov(field of view) multiplier, 0.0 - 2.0. Defaults to 1.0. Lower values "zoom in" and higher values "zoom out".
     * Best used when trying to implement visual zoom effects like fish-eye or binoculars while not adjusting camera fov.
     * Recommended to be keep at 1.0 except for special cases.
     */
    get fovMultiplier(): number;
    set fovMultiplier(value: number);
    private _fovMultiplier;
    /**
     * Enable the FOV adjustment feature controlled by fovMultiplier.
     */
    useEquirectangularFOV: boolean;
    private _maxSimultaneousLights;
    /**
     * Number of Simultaneous lights allowed on the material.
     */
    maxSimultaneousLights: int;
    private _shadowOnly;
    /**
     * Make the material only render shadows
     */
    shadowOnly: boolean;
    /**
     * Default configuration related to image processing available in the Background Material.
     */
    protected _imageProcessingConfiguration: ImageProcessingConfiguration;
    /**
     * Keep track of the image processing observer to allow dispose and replace.
     */
    private _imageProcessingObserver;
    /**
     * Attaches a new image processing configuration to the PBR Material.
     * @param configuration (if null the scene configuration will be use)
     */
    protected _attachImageProcessingConfiguration(configuration: Nullable<ImageProcessingConfiguration>): void;
    /**
     * Gets the image processing configuration used either in this material.
     */
    get imageProcessingConfiguration(): Nullable<ImageProcessingConfiguration>;
    /**
     * Sets the Default image processing configuration used either in the this material.
     *
     * If sets to null, the scene one is in use.
     */
    set imageProcessingConfiguration(value: Nullable<ImageProcessingConfiguration>);
    /**
     * Gets whether the color curves effect is enabled.
     */
    get cameraColorCurvesEnabled(): boolean;
    /**
     * Sets whether the color curves effect is enabled.
     */
    set cameraColorCurvesEnabled(value: boolean);
    /**
     * Gets whether the color grading effect is enabled.
     */
    get cameraColorGradingEnabled(): boolean;
    /**
     * Gets whether the color grading effect is enabled.
     */
    set cameraColorGradingEnabled(value: boolean);
    /**
     * Gets whether tonemapping is enabled or not.
     */
    get cameraToneMappingEnabled(): boolean;
    /**
     * Sets whether tonemapping is enabled or not
     */
    set cameraToneMappingEnabled(value: boolean);
    /**
     * The camera exposure used on this material.
     * This property is here and not in the camera to allow controlling exposure without full screen post process.
     * This corresponds to a photographic exposure.
     */
    get cameraExposure(): float;
    /**
     * The camera exposure used on this material.
     * This property is here and not in the camera to allow controlling exposure without full screen post process.
     * This corresponds to a photographic exposure.
     */
    set cameraExposure(value: float);
    /**
     * Gets The camera contrast used on this material.
     */
    get cameraContrast(): float;
    /**
     * Sets The camera contrast used on this material.
     */
    set cameraContrast(value: float);
    /**
     * Gets the Color Grading 2D Lookup Texture.
     */
    get cameraColorGradingTexture(): Nullable<BaseTexture>;
    /**
     * Sets the Color Grading 2D Lookup Texture.
     */
    set cameraColorGradingTexture(value: Nullable<BaseTexture>);
    /**
     * The color grading curves provide additional color adjustment that is applied after any color grading transform (3D LUT).
     * They allow basic adjustment of saturation and small exposure adjustments, along with color filter tinting to provide white balance adjustment or more stylistic effects.
     * These are similar to controls found in many professional imaging or colorist software. The global controls are applied to the entire image. For advanced tuning, extra controls are provided to adjust the shadow, midtone and highlight areas of the image;
     * corresponding to low luminance, medium luminance, and high luminance areas respectively.
     */
    get cameraColorCurves(): Nullable<ColorCurves>;
    /**
     * The color grading curves provide additional color adjustment that is applied after any color grading transform (3D LUT).
     * They allow basic adjustment of saturation and small exposure adjustments, along with color filter tinting to provide white balance adjustment or more stylistic effects.
     * These are similar to controls found in many professional imaging or colorist software. The global controls are applied to the entire image. For advanced tuning, extra controls are provided to adjust the shadow, midtone and highlight areas of the image;
     * corresponding to low luminance, medium luminance, and high luminance areas respectively.
     */
    set cameraColorCurves(value: Nullable<ColorCurves>);
    /**
     * Due to a bug in iOS10, video tags (which are using the background material) are in BGR and not RGB.
     * Setting this flag to true (not done automatically!) will convert it back to RGB.
     */
    switchToBGR: boolean;
    private _enableGroundProjection;
    /**
     * Enables the ground projection mode on the material.
     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/skybox#ground-projection
     */
    enableGroundProjection: boolean;
    /**
     * Defines the radius of the projected ground if enableGroundProjection is true.
     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/skybox#ground-projection
     */
    projectedGroundRadius: number;
    /**
     * Defines the height of the projected ground if enableGroundProjection is true.
     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/skybox#ground-projection
     */
    projectedGroundHeight: number;
    private _renderTargets;
    private _reflectionControls;
    private _white;
    private _primaryShadowColor;
    private _primaryHighlightColor;
    /**
     * Instantiates a Background Material in the given scene
     * @param name The friendly name of the material
     * @param scene The scene to add the material to
     */
    constructor(name: string, scene?: Scene);
    /**
     * Gets a boolean indicating that current material needs to register RTT
     */
    get hasRenderTargetTextures(): boolean;
    /**
     * The entire material has been created in order to prevent overdraw.
     * @returns false
     */
    needAlphaTesting(): boolean;
    /**
     * The entire material has been created in order to prevent overdraw.
     * @returns true if blending is enable
     */
    needAlphaBlending(): boolean;
    /**
     * Checks whether the material is ready to be rendered for a given mesh.
     * @param mesh The mesh to render
     * @param subMesh The submesh to check against
     * @param useInstances Specify wether or not the material is used with instances
     * @returns true if all the dependencies are ready (Textures, Effects...)
     */
    isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances?: boolean): boolean;
    /**
     * Compute the primary color according to the chosen perceptual color.
     */
    private _computePrimaryColorFromPerceptualColor;
    /**
     * Compute the highlights and shadow colors according to their chosen levels.
     */
    private _computePrimaryColors;
    /**
     * Build the uniform buffer used in the material.
     */
    buildUniformLayout(): void;
    /**
     * Unbind the material.
     */
    unbind(): void;
    /**
     * Bind only the world matrix to the material.
     * @param world The world matrix to bind.
     */
    bindOnlyWorldMatrix(world: Matrix): void;
    /**
     * Bind the material for a dedicated submesh (every used meshes will be considered opaque).
     * @param world The world matrix to bind.
     * @param mesh the mesh to bind for.
     * @param subMesh The submesh to bind for.
     */
    bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void;
    /**
     * Checks to see if a texture is used in the material.
     * @param texture - Base texture to use.
     * @returns - Boolean specifying if a texture is used in the material.
     */
    hasTexture(texture: BaseTexture): boolean;
    /**
     * Dispose the material.
     * @param forceDisposeEffect Force disposal of the associated effect.
     * @param forceDisposeTextures Force disposal of the associated textures.
     */
    dispose(forceDisposeEffect?: boolean, forceDisposeTextures?: boolean): void;
    /**
     * Clones the material.
     * @param name The cloned name.
     * @returns The cloned material.
     */
    clone(name: string): BackgroundMaterial;
    /**
     * Serializes the current material to its JSON representation.
     * @returns The JSON representation.
     */
    serialize(): any;
    /**
     * Gets the class name of the material
     * @returns "BackgroundMaterial"
     */
    getClassName(): string;
    /**
     * Parse a JSON input to create back a background material.
     * @param source The JSON data to parse
     * @param scene The scene to create the parsed material in
     * @param rootUrl The root url of the assets the material depends upon
     * @returns the instantiated BackgroundMaterial.
     */
    static Parse(source: any, scene: Scene, rootUrl: string): BackgroundMaterial;
}
