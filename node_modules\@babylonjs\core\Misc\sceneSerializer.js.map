{"version": 3, "file": "sceneSerializer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/sceneSerializer.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAI3D,OAAO,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAC;AACjE,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAKxD,OAAO,EAAE,MAAM,EAAE,oBAAyB;AAE1C,IAAI,oBAAoB,GAAe,EAAE,CAAC;AAC1C,MAAM,iBAAiB,GAAG,CAAC,QAAkB,EAAE,uBAA4B,EAAO,EAAE;IAChF,IAAI,QAAQ,CAAC,cAAc,EAAE;QACzB,OAAO;KACV;IAED,uBAAuB,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC,CAAC;IAEnE,oBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;AACpD,CAAC,CAAC;AAEF,MAAM,aAAa,GAAG,CAAC,IAAU,EAAE,kBAAuB,EAAO,EAAE;IAC/D,MAAM,mBAAmB,GAAQ,EAAE,CAAC;IAEpC,WAAW;IACX,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;IAChC,IAAI,QAAQ,EAAE;QACV,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;YAC/C,+IAA+I;YAC/I,iBAAiB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,UAAU,CAAC,CAAC;SAC9D;KACJ;IAED,SAAS;IACT,IAAI,IAAI,CAAC,SAAS,EAAE;QAChB,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;KACvC;IAED,OAAO,mBAAmB,CAAC;AAC/B,CAAC,CAAC;AAEF,MAAM,kBAAkB,GAAG,CAAC,IAAU,EAAE,mBAAwB,EAAE,EAAE;IAChE,IAAK,IAAa,CAAC,OAAO,EAAE;QACxB,MAAM,IAAI,GAAG,IAAY,CAAC;QAC1B,0CAA0C;QAC1C,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,CAAC,qBAAqB,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,CAAC,mBAAmB,EAAE;YAClH,MAAM,iBAAiB,GAAG,CAAC,QAAkB,EAAE,EAAE;gBAC7C,mBAAmB,CAAC,SAAS,GAAG,mBAAmB,CAAC,SAAS,IAAI,EAAE,CAAC;gBACpE,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAa,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAgB,IAAI,CAAC,QAAS,CAAC,EAAE,CAAC,EAAE;oBAClH,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;iBAC5D;YACL,CAAC,CAAC;YAEF,oBAAoB;YACpB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;gBAChD,IAAI,IAAI,CAAC,QAAQ,YAAY,aAAa,EAAE;oBACxC,mBAAmB,CAAC,cAAc,GAAG,mBAAmB,CAAC,cAAc,IAAI,EAAE,CAAC;oBAC9E,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,GAAa,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAgB,IAAI,CAAC,QAAS,CAAC,EAAE,CAAC,EAAE;wBACtG,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;wBACnE,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;4BAClD,IAAI,WAAW,EAAE;gCACb,iBAAiB,CAAC,WAAW,CAAC,CAAC;6BAClC;yBACJ;qBACJ;iBACJ;qBAAM;oBACH,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACpC;aACJ;iBAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACvB,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC,CAAC;aACtD;YAED,oBAAoB;YACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;YAChC,IAAI,QAAQ,EAAE;gBACV,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE;oBACjC,mBAAmB,CAAC,UAAU,GAAG,EAAE,CAAC;oBAEpC,mBAAmB,CAAC,UAAU,CAAC,KAAK,GAAG,EAAE,CAAC;oBAC1C,mBAAmB,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE,CAAC;oBAC5C,mBAAmB,CAAC,UAAU,CAAC,SAAS,GAAG,EAAE,CAAC;oBAC9C,mBAAmB,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE,CAAC;oBAC5C,mBAAmB,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE,CAAC;oBAC5C,mBAAmB,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,CAAC;oBAC3C,mBAAmB,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,CAAC;oBAC/C,mBAAmB,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,CAAC;iBAClD;gBAED,iBAAiB,CAAC,QAAQ,EAAE,mBAAmB,CAAC,UAAU,CAAC,CAAC;aAC/D;YACD,YAAY;YACZ,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;gBAChD,mBAAmB,CAAC,SAAS,GAAG,mBAAmB,CAAC,SAAS,IAAI,EAAE,CAAC;gBACpE,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;aACjE;YAED,2BAA2B;YAC3B,mBAAmB,CAAC,MAAM,GAAG,mBAAmB,CAAC,MAAM,IAAI,EAAE,CAAC;YAC9D,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC,CAAC;SAC7E;KACJ;SAAM,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,eAAe,EAAE;QAChD,MAAM,aAAa,GAAG,IAAqB,CAAC;QAC5C,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC;KACtE;SAAM,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QACrD,MAAM,MAAM,GAAG,IAAc,CAAC;QAC9B,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;KACxD;SAAM,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;QACpD,MAAM,KAAK,GAAG,IAAa,CAAC;QAC5B,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;KACtD;AACL,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,eAAe;IACxB;;OAEG;IACI,MAAM,CAAC,UAAU;QACpB,oBAAoB,GAAG,EAAE,CAAC;IAC9B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,SAAS,CAAC,KAAY;QAChC,OAAO,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC;IAEO,MAAM,CAAC,UAAU,CAAC,KAAY,EAAE,sBAAsB,GAAG,IAAI;QACjE,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QAEpC,IAAI,sBAAsB,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,sBAAsB,IAAI,OAAO,CAAC,qBAAqB,EAAE;YAChH,MAAM,CAAC,IAAI,CAAC,oIAAoI,CAAC,CAAC;SACrJ;QAED,eAAe,CAAC,UAAU,EAAE,CAAC;QAE7B,QAAQ;QACR,mBAAmB,CAAC,wBAAwB,GAAG,KAAK,CAAC,wBAAwB,CAAC;QAC9E,mBAAmB,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;QAChD,mBAAmB,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC5D,mBAAmB,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAChE,mBAAmB,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACtD,mBAAmB,CAAC,iBAAiB,GAAG,KAAK,CAAC,iBAAiB,CAAC;QAChE,mBAAmB,CAAC,oBAAoB,GAAG,KAAK,CAAC,oBAAoB,CAAC;QAEtE,MAAM;QACN,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE;YACtC,mBAAmB,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YAC5C,mBAAmB,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACxD,mBAAmB,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;YAC9C,mBAAmB,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAC1C,mBAAmB,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;SACrD;QAED,SAAS;QACT,IAAI,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,gBAAgB,EAAE,EAAE;YACpD,MAAM,YAAY,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAE9C,IAAI,YAAY,EAAE;gBACd,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC1C,mBAAmB,CAAC,cAAc,GAAG,YAAY,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBACpE,mBAAmB,CAAC,aAAa,GAAG,YAAY,CAAC,oBAAoB,EAAE,CAAC;aAC3E;SACJ;QAED,WAAW;QACX,IAAI,KAAK,CAAC,QAAQ,EAAE;YAChB,mBAAmB,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;SACjD;QAED,gBAAgB;QAChB,mBAAmB,CAAC,mBAAmB,GAAG,EAAE,CAAC;QAC7C,KAAK,MAAM,YAAY,IAAI,KAAK,CAAC,MAAM,EAAE;YACrC,MAAM,OAAO,GAAU,YAAa,CAAC,kBAAkB,CAAC;YAExD,IAAI,OAAO,EAAE;gBACT,mBAAmB,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;aACrE;SACJ;QAED,SAAS;QACT,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAChC,IAAI,KAAa,CAAC;QAClB,IAAI,KAAY,CAAC;QACjB,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAClD,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAE5B,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;gBACvB,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;aACtD;SACJ;QAED,UAAU;QACV,mBAAmB,CAAC,OAAO,GAAG,EAAE,CAAC;QACjC,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACnD,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAEpC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;gBACxB,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;aACxD;SACJ;QAED,IAAI,KAAK,CAAC,YAAY,EAAE;YACpB,mBAAmB,CAAC,cAAc,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;SAC9D;QAED,aAAa;QACb,mBAAmB,CAAC,0BAA0B,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC;QAE3E,mBAAmB;QACnB,IAAI,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3D,mBAAmB,CAAC,eAAe,GAAG,EAAE,CAAC;YACzC,KAAK,IAAI,mBAAmB,GAAG,CAAC,EAAE,mBAAmB,GAAG,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,mBAAmB,EAAE,EAAE;gBACzG,MAAM,cAAc,GAAG,KAAK,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;gBAElE,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC;aACxE;SACJ;QAED,oBAAoB;QACpB,IAAI,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7D,mBAAmB,CAAC,gBAAgB,GAAG,EAAE,CAAC;YAE1C,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC5D,MAAM,eAAe,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBACtD,mBAAmB,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC;aAC1E;SACJ;QAED,YAAY;QACZ,mBAAmB,CAAC,SAAS,GAAG,EAAE,CAAC;QACnC,mBAAmB,CAAC,cAAc,GAAG,EAAE,CAAC;QACxC,IAAI,QAAkB,CAAC;QACvB,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;gBAC1B,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;aAC5D;SACJ;QAED,iBAAiB;QACjB,mBAAmB,CAAC,cAAc,GAAG,EAAE,CAAC;QACxC,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC1D,MAAM,aAAa,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAClD,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC;SACtE;QAED,sBAAsB;QACtB,IAAI,KAAK,CAAC,kBAAkB,EAAE;YAC1B,IAAK,KAAK,CAAC,kBAAkC,CAAC,MAAM,EAAE;gBAClD,mBAAmB,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;aACjF;iBAAM;gBACH,mBAAmB,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBACvE,mBAAmB,CAAC,2BAA2B,GAAI,KAAK,CAAC,kBAAkC,CAAC,SAAS,CAAC;aACzG;SACJ;QAED,wBAAwB;QACxB,mBAAmB,CAAC,oBAAoB,GAAG,KAAK,CAAC,oBAAoB,CAAC;QAEtE,YAAY;QACZ,mBAAmB,CAAC,SAAS,GAAG,EAAE,CAAC;QACnC,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;gBAC1B,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;aAC5D;SACJ;QAED,kBAAkB;QAClB,mBAAmB,CAAC,cAAc,GAAG,EAAE,CAAC;QACxC,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC1D,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE;gBAC7C,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;aACpF;SACJ;QAED,aAAa;QACb,mBAAmB,CAAC,UAAU,GAAG,EAAE,CAAC;QAEpC,mBAAmB,CAAC,UAAU,CAAC,KAAK,GAAG,EAAE,CAAC;QAC1C,mBAAmB,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE,CAAC;QAC5C,mBAAmB,CAAC,UAAU,CAAC,SAAS,GAAG,EAAE,CAAC;QAC9C,mBAAmB,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE,CAAC;QAC5C,mBAAmB,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE,CAAC;QAC5C,mBAAmB,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,CAAC;QAC3C,mBAAmB,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,CAAC;QAC/C,mBAAmB,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,CAAC;QAE/C,oBAAoB,GAAG,EAAE,CAAC;QAC1B,MAAM,UAAU,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;QACzC,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChD,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YAEnC,IAAI,QAAQ,CAAC,OAAO,EAAE,EAAE;gBACpB,iBAAiB,CAAC,QAAQ,EAAE,mBAAmB,CAAC,UAAU,CAAC,CAAC;aAC/D;SACJ;QAED,SAAS;QACT,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAChC,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAClD,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEzC,IAAI,YAAY,YAAY,IAAI,EAAE;gBAC9B,MAAM,IAAI,GAAG,YAAY,CAAC;gBAC1B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;oBACtB,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,CAAC,qBAAqB,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,CAAC,mBAAmB,EAAE;wBAClH,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC,CAAC;qBAC7E;iBACJ;aACJ;SACJ;QAED,oBAAoB;QACpB,mBAAmB,CAAC,eAAe,GAAG,EAAE,CAAC;QACzC,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;SAC3F;QAED,iBAAiB;QACjB,mBAAmB,CAAC,aAAa,GAAG,EAAE,CAAC;QACvC,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACzD,mBAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;SAClF;QAED,iBAAiB;QACjB,IAAI,KAAK,CAAC,aAAa,EAAE;YACrB,mBAAmB,CAAC,OAAO,GAAG,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SACxE;QAED,aAAa;QACb,KAAK,MAAM,SAAS,IAAI,KAAK,CAAC,uBAAuB,EAAE;YACnD,SAAS,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;SAC5C;QAED,UAAU;QACV,IAAI,KAAK,CAAC,cAAc,EAAE;YACtB,mBAAmB,CAAC,cAAc,GAAG,EAAE,CAAC;YACxC,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC1D,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;aACxF;SACJ;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,cAAc,CAAC,KAAY;QACrC,MAAM,mBAAmB,GAAG,eAAe,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAErE,MAAM,QAAQ,GAAwB,EAAE,CAAC;QAEzC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC;QAErD,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,mBAAmB,CAAC,CAAC;IACjE,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,GAAQ,EAAE,QAA6B;QACnE,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACjC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBACjB,IAAI,CAAC,YAAY,OAAO,EAAE;oBACtB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;iBACvD;qBAAM,IAAI,CAAC,YAAY,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;oBAChD,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;iBACtC;aACJ;SACJ;aAAM,IAAI,GAAG,YAAY,MAAM,EAAE;YAC9B,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;gBACpB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;oBACjD,MAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;oBACpB,IAAI,CAAC,YAAY,OAAO,EAAE;wBACtB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;qBAC1D;yBAAM,IAAI,CAAC,YAAY,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;wBAChD,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;qBACtC;iBACJ;aACJ;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,aAAa,CAAC,WAAgB,CAAC,oBAAoB,EAAE,cAAuB,KAAK,EAAE,eAAwB,KAAK;QAC1H,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QACpC,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAChC,mBAAmB,CAAC,cAAc,GAAG,EAAE,CAAC;QACxC,mBAAmB,CAAC,OAAO,GAAG,EAAE,CAAC;QACjC,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAEhC,eAAe,CAAC,UAAU,EAAE,CAAC;QAE7B,WAAW,GAAG,WAAW,YAAY,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QAEzE,IAAI,WAAW,IAAI,YAAY,EAAE;YAC7B,0EAA0E;YAC1E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACzC,IAAI,YAAY,EAAE;oBACd,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC,IAAU,EAAE,EAAE;wBACnD,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;4BACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;yBAC1B;oBACL,CAAC,CAAC,CAAC;iBACN;gBACD,wDAAwD;gBACxD,IAAI,WAAW,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,EAAE;oBACjI,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;iBAC3C;aACJ;SACJ;QAED,WAAW,CAAC,OAAO,CAAC,CAAC,IAAU,EAAE,EAAE;YAC/B,kBAAkB,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,OAAO,mBAAmB,CAAC;IAC/B,CAAC;CACJ", "sourcesContent": ["import type { Geometry } from \"../Meshes/geometry\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { MultiMaterial } from \"../Materials/multiMaterial\";\r\nimport type { Material } from \"../Materials/material\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Light } from \"../Lights/light\";\r\nimport { SerializationHelper } from \"./decorators.serialization\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport type { CubeTexture } from \"../Materials/Textures/cubeTexture\";\r\nimport type { Node } from \"../node\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\nlet serializedGeometries: Geometry[] = [];\r\nconst SerializeGeometry = (geometry: Geometry, serializationGeometries: any): any => {\r\n    if (geometry.doNotSerialize) {\r\n        return;\r\n    }\r\n\r\n    serializationGeometries.vertexData.push(geometry.serializeVerticeData());\r\n\r\n    (<any>serializedGeometries)[geometry.id] = true;\r\n};\r\n\r\nconst SerializeMesh = (mesh: Mesh, serializationScene: any): any => {\r\n    const serializationObject: any = {};\r\n\r\n    // Geometry\r\n    const geometry = mesh._geometry;\r\n    if (geometry) {\r\n        if (!mesh.getScene().getGeometryById(geometry.id)) {\r\n            // Geometry was in the memory but not added to the scene, nevertheless it's better to serialize to be able to reload the mesh with its geometry\r\n            SerializeGeometry(geometry, serializationScene.geometries);\r\n        }\r\n    }\r\n\r\n    // Custom\r\n    if (mesh.serialize) {\r\n        mesh.serialize(serializationObject);\r\n    }\r\n\r\n    return serializationObject;\r\n};\r\n\r\nconst FinalizeSingleNode = (node: Node, serializationObject: any) => {\r\n    if ((node as Mesh)._isMesh) {\r\n        const mesh = node as Mesh;\r\n        //only works if the mesh is already loaded\r\n        if (mesh.delayLoadState === Constants.DELAYLOADSTATE_LOADED || mesh.delayLoadState === Constants.DELAYLOADSTATE_NONE) {\r\n            const serializeMaterial = (material: Material) => {\r\n                serializationObject.materials = serializationObject.materials || [];\r\n                if (mesh.material && !serializationObject.materials.some((mat: Material) => mat.id === (<Material>mesh.material).id)) {\r\n                    serializationObject.materials.push(material.serialize());\r\n                }\r\n            };\r\n\r\n            //serialize material\r\n            if (mesh.material && !mesh.material.doNotSerialize) {\r\n                if (mesh.material instanceof MultiMaterial) {\r\n                    serializationObject.multiMaterials = serializationObject.multiMaterials || [];\r\n                    if (!serializationObject.multiMaterials.some((mat: Material) => mat.id === (<Material>mesh.material).id)) {\r\n                        serializationObject.multiMaterials.push(mesh.material.serialize());\r\n                        for (const submaterial of mesh.material.subMaterials) {\r\n                            if (submaterial) {\r\n                                serializeMaterial(submaterial);\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    serializeMaterial(mesh.material);\r\n                }\r\n            } else if (!mesh.material) {\r\n                serializeMaterial(mesh.getScene().defaultMaterial);\r\n            }\r\n\r\n            //serialize geometry\r\n            const geometry = mesh._geometry;\r\n            if (geometry) {\r\n                if (!serializationObject.geometries) {\r\n                    serializationObject.geometries = {};\r\n\r\n                    serializationObject.geometries.boxes = [];\r\n                    serializationObject.geometries.spheres = [];\r\n                    serializationObject.geometries.cylinders = [];\r\n                    serializationObject.geometries.toruses = [];\r\n                    serializationObject.geometries.grounds = [];\r\n                    serializationObject.geometries.planes = [];\r\n                    serializationObject.geometries.torusKnots = [];\r\n                    serializationObject.geometries.vertexData = [];\r\n                }\r\n\r\n                SerializeGeometry(geometry, serializationObject.geometries);\r\n            }\r\n            // Skeletons\r\n            if (mesh.skeleton && !mesh.skeleton.doNotSerialize) {\r\n                serializationObject.skeletons = serializationObject.skeletons || [];\r\n                serializationObject.skeletons.push(mesh.skeleton.serialize());\r\n            }\r\n\r\n            //serialize the actual mesh\r\n            serializationObject.meshes = serializationObject.meshes || [];\r\n            serializationObject.meshes.push(SerializeMesh(mesh, serializationObject));\r\n        }\r\n    } else if (node.getClassName() === \"TransformNode\") {\r\n        const transformNode = node as TransformNode;\r\n        serializationObject.transformNodes.push(transformNode.serialize());\r\n    } else if (node.getClassName().indexOf(\"Camera\") !== -1) {\r\n        const camera = node as Camera;\r\n        serializationObject.cameras.push(camera.serialize());\r\n    } else if (node.getClassName().indexOf(\"Light\") !== -1) {\r\n        const light = node as Light;\r\n        serializationObject.lights.push(light.serialize());\r\n    }\r\n};\r\n\r\n/**\r\n * Class used to serialize a scene into a string\r\n */\r\nexport class SceneSerializer {\r\n    /**\r\n     * Clear cache used by a previous serialization\r\n     */\r\n    public static ClearCache(): void {\r\n        serializedGeometries = [];\r\n    }\r\n\r\n    /**\r\n     * Serialize a scene into a JSON compatible object\r\n     * Note that if the current engine does not support synchronous texture reading (like WebGPU), you should use SerializeAsync instead\r\n     * as else you may not retrieve the proper base64 encoded texture data (when using the Texture.ForceSerializeBuffers flag)\r\n     * @param scene defines the scene to serialize\r\n     * @returns a JSON compatible object\r\n     */\r\n    public static Serialize(scene: Scene): any {\r\n        return SceneSerializer._Serialize(scene);\r\n    }\r\n\r\n    private static _Serialize(scene: Scene, checkSyncReadSupported = true): any {\r\n        const serializationObject: any = {};\r\n\r\n        if (checkSyncReadSupported && !scene.getEngine()._features.supportSyncTextureRead && Texture.ForceSerializeBuffers) {\r\n            Logger.Warn(\"The serialization object may not contain the proper base64 encoded texture data! You should use the SerializeAsync method instead.\");\r\n        }\r\n\r\n        SceneSerializer.ClearCache();\r\n\r\n        // Scene\r\n        serializationObject.useDelayedTextureLoading = scene.useDelayedTextureLoading;\r\n        serializationObject.autoClear = scene.autoClear;\r\n        serializationObject.clearColor = scene.clearColor.asArray();\r\n        serializationObject.ambientColor = scene.ambientColor.asArray();\r\n        serializationObject.gravity = scene.gravity.asArray();\r\n        serializationObject.collisionsEnabled = scene.collisionsEnabled;\r\n        serializationObject.useRightHandedSystem = scene.useRightHandedSystem;\r\n\r\n        // Fog\r\n        if (scene.fogMode && scene.fogMode !== 0) {\r\n            serializationObject.fogMode = scene.fogMode;\r\n            serializationObject.fogColor = scene.fogColor.asArray();\r\n            serializationObject.fogStart = scene.fogStart;\r\n            serializationObject.fogEnd = scene.fogEnd;\r\n            serializationObject.fogDensity = scene.fogDensity;\r\n        }\r\n\r\n        //Physics\r\n        if (scene.isPhysicsEnabled && scene.isPhysicsEnabled()) {\r\n            const physicEngine = scene.getPhysicsEngine();\r\n\r\n            if (physicEngine) {\r\n                serializationObject.physicsEnabled = true;\r\n                serializationObject.physicsGravity = physicEngine.gravity.asArray();\r\n                serializationObject.physicsEngine = physicEngine.getPhysicsPluginName();\r\n            }\r\n        }\r\n\r\n        // Metadata\r\n        if (scene.metadata) {\r\n            serializationObject.metadata = scene.metadata;\r\n        }\r\n\r\n        // Morph targets\r\n        serializationObject.morphTargetManagers = [];\r\n        for (const abstractMesh of scene.meshes) {\r\n            const manager = (<Mesh>abstractMesh).morphTargetManager;\r\n\r\n            if (manager) {\r\n                serializationObject.morphTargetManagers.push(manager.serialize());\r\n            }\r\n        }\r\n\r\n        // Lights\r\n        serializationObject.lights = [];\r\n        let index: number;\r\n        let light: Light;\r\n        for (index = 0; index < scene.lights.length; index++) {\r\n            light = scene.lights[index];\r\n\r\n            if (!light.doNotSerialize) {\r\n                serializationObject.lights.push(light.serialize());\r\n            }\r\n        }\r\n\r\n        // Cameras\r\n        serializationObject.cameras = [];\r\n        for (index = 0; index < scene.cameras.length; index++) {\r\n            const camera = scene.cameras[index];\r\n\r\n            if (!camera.doNotSerialize) {\r\n                serializationObject.cameras.push(camera.serialize());\r\n            }\r\n        }\r\n\r\n        if (scene.activeCamera) {\r\n            serializationObject.activeCameraID = scene.activeCamera.id;\r\n        }\r\n\r\n        // Animations\r\n        SerializationHelper.AppendSerializedAnimations(scene, serializationObject);\r\n\r\n        // Animation Groups\r\n        if (scene.animationGroups && scene.animationGroups.length > 0) {\r\n            serializationObject.animationGroups = [];\r\n            for (let animationGroupIndex = 0; animationGroupIndex < scene.animationGroups.length; animationGroupIndex++) {\r\n                const animationGroup = scene.animationGroups[animationGroupIndex];\r\n\r\n                serializationObject.animationGroups.push(animationGroup.serialize());\r\n            }\r\n        }\r\n\r\n        // Reflection probes\r\n        if (scene.reflectionProbes && scene.reflectionProbes.length > 0) {\r\n            serializationObject.reflectionProbes = [];\r\n\r\n            for (index = 0; index < scene.reflectionProbes.length; index++) {\r\n                const reflectionProbe = scene.reflectionProbes[index];\r\n                serializationObject.reflectionProbes.push(reflectionProbe.serialize());\r\n            }\r\n        }\r\n\r\n        // Materials\r\n        serializationObject.materials = [];\r\n        serializationObject.multiMaterials = [];\r\n        let material: Material;\r\n        for (index = 0; index < scene.materials.length; index++) {\r\n            material = scene.materials[index];\r\n            if (!material.doNotSerialize) {\r\n                serializationObject.materials.push(material.serialize());\r\n            }\r\n        }\r\n\r\n        // MultiMaterials\r\n        serializationObject.multiMaterials = [];\r\n        for (index = 0; index < scene.multiMaterials.length; index++) {\r\n            const multiMaterial = scene.multiMaterials[index];\r\n            serializationObject.multiMaterials.push(multiMaterial.serialize());\r\n        }\r\n\r\n        // Environment texture\r\n        if (scene.environmentTexture) {\r\n            if ((scene.environmentTexture as CubeTexture)._files) {\r\n                serializationObject.environmentTexture = scene.environmentTexture.serialize();\r\n            } else {\r\n                serializationObject.environmentTexture = scene.environmentTexture.name;\r\n                serializationObject.environmentTextureRotationY = (scene.environmentTexture as CubeTexture).rotationY;\r\n            }\r\n        }\r\n\r\n        // Environment Intensity\r\n        serializationObject.environmentIntensity = scene.environmentIntensity;\r\n\r\n        // Skeletons\r\n        serializationObject.skeletons = [];\r\n        for (index = 0; index < scene.skeletons.length; index++) {\r\n            const skeleton = scene.skeletons[index];\r\n            if (!skeleton.doNotSerialize) {\r\n                serializationObject.skeletons.push(skeleton.serialize());\r\n            }\r\n        }\r\n\r\n        // Transform nodes\r\n        serializationObject.transformNodes = [];\r\n        for (index = 0; index < scene.transformNodes.length; index++) {\r\n            if (!scene.transformNodes[index].doNotSerialize) {\r\n                serializationObject.transformNodes.push(scene.transformNodes[index].serialize());\r\n            }\r\n        }\r\n\r\n        // Geometries\r\n        serializationObject.geometries = {};\r\n\r\n        serializationObject.geometries.boxes = [];\r\n        serializationObject.geometries.spheres = [];\r\n        serializationObject.geometries.cylinders = [];\r\n        serializationObject.geometries.toruses = [];\r\n        serializationObject.geometries.grounds = [];\r\n        serializationObject.geometries.planes = [];\r\n        serializationObject.geometries.torusKnots = [];\r\n        serializationObject.geometries.vertexData = [];\r\n\r\n        serializedGeometries = [];\r\n        const geometries = scene.getGeometries();\r\n        for (index = 0; index < geometries.length; index++) {\r\n            const geometry = geometries[index];\r\n\r\n            if (geometry.isReady()) {\r\n                SerializeGeometry(geometry, serializationObject.geometries);\r\n            }\r\n        }\r\n\r\n        // Meshes\r\n        serializationObject.meshes = [];\r\n        for (index = 0; index < scene.meshes.length; index++) {\r\n            const abstractMesh = scene.meshes[index];\r\n\r\n            if (abstractMesh instanceof Mesh) {\r\n                const mesh = abstractMesh;\r\n                if (!mesh.doNotSerialize) {\r\n                    if (mesh.delayLoadState === Constants.DELAYLOADSTATE_LOADED || mesh.delayLoadState === Constants.DELAYLOADSTATE_NONE) {\r\n                        serializationObject.meshes.push(SerializeMesh(mesh, serializationObject));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Particles Systems\r\n        serializationObject.particleSystems = [];\r\n        for (index = 0; index < scene.particleSystems.length; index++) {\r\n            serializationObject.particleSystems.push(scene.particleSystems[index].serialize(false));\r\n        }\r\n\r\n        // Post processes\r\n        serializationObject.postProcesses = [];\r\n        for (index = 0; index < scene.postProcesses.length; index++) {\r\n            serializationObject.postProcesses.push(scene.postProcesses[index].serialize());\r\n        }\r\n\r\n        // Action Manager\r\n        if (scene.actionManager) {\r\n            serializationObject.actions = scene.actionManager.serialize(\"scene\");\r\n        }\r\n\r\n        // Components\r\n        for (const component of scene._serializableComponents) {\r\n            component.serialize(serializationObject);\r\n        }\r\n\r\n        // Sprites\r\n        if (scene.spriteManagers) {\r\n            serializationObject.spriteManagers = [];\r\n            for (index = 0; index < scene.spriteManagers.length; index++) {\r\n                serializationObject.spriteManagers.push(scene.spriteManagers[index].serialize(true));\r\n            }\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Serialize a scene into a JSON compatible object\r\n     * @param scene defines the scene to serialize\r\n     * @returns a JSON promise compatible object\r\n     */\r\n    public static SerializeAsync(scene: Scene): Promise<any> {\r\n        const serializationObject = SceneSerializer._Serialize(scene, false);\r\n\r\n        const promises: Array<Promise<any>> = [];\r\n\r\n        this._CollectPromises(serializationObject, promises);\r\n\r\n        return Promise.all(promises).then(() => serializationObject);\r\n    }\r\n\r\n    private static _CollectPromises(obj: any, promises: Array<Promise<any>>): void {\r\n        if (Array.isArray(obj)) {\r\n            for (let i = 0; i < obj.length; ++i) {\r\n                const o = obj[i];\r\n                if (o instanceof Promise) {\r\n                    promises.push(o.then((res: any) => (obj[i] = res)));\r\n                } else if (o instanceof Object || Array.isArray(o)) {\r\n                    this._CollectPromises(o, promises);\r\n                }\r\n            }\r\n        } else if (obj instanceof Object) {\r\n            for (const name in obj) {\r\n                if (Object.prototype.hasOwnProperty.call(obj, name)) {\r\n                    const o = obj[name];\r\n                    if (o instanceof Promise) {\r\n                        promises.push(o.then((res: any) => (obj[name] = res)));\r\n                    } else if (o instanceof Object || Array.isArray(o)) {\r\n                        this._CollectPromises(o, promises);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serialize a mesh into a JSON compatible object\r\n     * @param toSerialize defines the mesh to serialize\r\n     * @param withParents defines if parents must be serialized as well\r\n     * @param withChildren defines if children must be serialized as well\r\n     * @returns a JSON compatible object\r\n     */\r\n    public static SerializeMesh(toSerialize: any /* Mesh || Mesh[] */, withParents: boolean = false, withChildren: boolean = false): any {\r\n        const serializationObject: any = {};\r\n        serializationObject.meshes = [];\r\n        serializationObject.transformNodes = [];\r\n        serializationObject.cameras = [];\r\n        serializationObject.lights = [];\r\n\r\n        SceneSerializer.ClearCache();\r\n\r\n        toSerialize = toSerialize instanceof Array ? toSerialize : [toSerialize];\r\n\r\n        if (withParents || withChildren) {\r\n            //deliberate for loop! not for each, appended should be processed as well.\r\n            for (let i = 0; i < toSerialize.length; ++i) {\r\n                if (withChildren) {\r\n                    toSerialize[i].getDescendants().forEach((node: Node) => {\r\n                        if (toSerialize.indexOf(node) < 0 && !node.doNotSerialize) {\r\n                            toSerialize.push(node);\r\n                        }\r\n                    });\r\n                }\r\n                //make sure the array doesn't contain the object already\r\n                if (withParents && toSerialize[i].parent && toSerialize.indexOf(toSerialize[i].parent) < 0 && !toSerialize[i].parent.doNotSerialize) {\r\n                    toSerialize.push(toSerialize[i].parent);\r\n                }\r\n            }\r\n        }\r\n\r\n        toSerialize.forEach((mesh: Node) => {\r\n            FinalizeSingleNode(mesh, serializationObject);\r\n        });\r\n\r\n        return serializationObject;\r\n    }\r\n}\r\n"]}