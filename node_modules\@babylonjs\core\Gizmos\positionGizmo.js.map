{"version": 3, "file": "positionGizmo.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Gizmos/positionGizmo.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE/C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAK7C,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AA2DzE;;GAEG;AACH,MAAM,OAAO,aAAc,SAAQ,KAAK;IAiDpC,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAW,YAAY,CAAC,IAA4B;QAChD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5G,IAAI,KAAK,CAAC,SAAS,EAAE;gBACjB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;aAC7B;iBAAM;gBACH,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAW,YAAY,CAAC,IAAoB;QACxC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5G,IAAI,KAAK,CAAC,SAAS,EAAE;gBACjB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;aAC7B;iBAAM;gBACH,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;IAC7K,CAAC;IAED,IAAW,UAAU;QACjB,OAAO,CACH,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ;YACjC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ;YACjC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ;YACjC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,QAAQ;YACtC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,QAAQ;YACtC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,QAAQ,CACzC,CAAC;IACN,CAAC;IAED,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED,IAAW,uBAAuB,CAAC,aAAwC;QACvE,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5G,KAAK,CAAC,uBAAuB,GAAG,aAAa,CAAC;QAClD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACH,YAAY,aAAmC,oBAAoB,CAAC,mBAAmB,EAAE,YAAoB,CAAC,EAAE,YAA2B,EAAE,OAA8B;QACvK,KAAK,CAAC,UAAU,CAAC,CAAC;QAzFtB;;WAEG;QACO,kBAAa,GAA2B,IAAI,CAAC;QAC7C,kBAAa,GAAmB,IAAI,CAAC;QAErC,iBAAY,GAA4B,EAAE,CAAC;QAErD,oCAAoC;QAC1B,oBAAe,GAA8B,IAAI,GAAG,EAAE,CAAC;QAEjE,6DAA6D;QACtD,0BAAqB,GAAG,IAAI,UAAU,EAAE,CAAC;QAChD,mEAAmE;QAC5D,qBAAgB,GAAG,IAAI,UAAU,EAAE,CAAC;QAC3C,4EAA4E;QACrE,wBAAmB,GAAG,IAAI,UAAU,EAAE,CAAC;QAE9C;;WAEG;QACO,wBAAmB,GAAG,KAAK,CAAC;QAqElC,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAC5G,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAC9G,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAE7G,IAAI,CAAC,WAAW,GAAG,IAAI,cAAc,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5G,IAAI,CAAC,WAAW,GAAG,IAAI,cAAc,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC9G,IAAI,CAAC,WAAW,GAAG,IAAI,cAAc,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAE7G,IAAI,CAAC,uBAAuB,GAAG,OAAO,EAAE,uBAAuB,CAAC;QAEhE,oBAAoB;QACpB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5G,KAAK,CAAC,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC9C,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YACH,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACzC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YACH,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC5C,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,YAAY,EAAE;YACd,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACrD;aAAM;YACH,uDAAuD;YACvD,KAAK,CAAC,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;SACpE;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,kBAAkB,CAAC,KAAc;QACxC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACrE,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;gBACxB,IAAI,KAAK,EAAE;oBACP,IAAI,KAAK,CAAC,YAAY,EAAE;wBACpB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;qBAC1C;yBAAM;wBACH,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;qBAC1C;iBACJ;aACJ;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IACD,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;;OAGG;IACH,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IAED,IAAW,wBAAwB,CAAC,wBAA8C;QAC9E,IAAI,CAAC,yBAAyB,GAAG,wBAAwB,CAAC;QAC1D,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5G,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;aAC7D;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACH,IAAW,sCAAsC,CAAC,KAAc;QAC5D,IAAI,CAAC,uCAAuC,GAAG,KAAK,CAAC;QACrD,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5G,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,sCAAsC,GAAG,KAAK,CAAC;aACxD;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD,IAAW,sCAAsC;QAC7C,OAAO,IAAI,CAAC,uCAAuC,CAAC;IACxD,CAAC;IAED,IAAW,sCAAsC,CAAC,KAAc;QAC5D,IAAI,CAAC,uCAAuC,GAAG,KAAK,CAAC;QACrD,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5G,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,sCAAsC,GAAG,KAAK,CAAC;aACxD;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD,IAAW,sCAAsC;QAC7C,OAAO,IAAI,CAAC,uCAAuC,CAAC;IACxD,CAAC;IAED,IAAW,WAAW,CAAC,KAAuB;QAC1C,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5G,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;QAC9B,CAAC,CAAC,CAAC;IACP,CAAC;IACD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,IAAW,eAAe,CAAC,eAAqC;QAC5D,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5G,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;QAC5C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,IAAW,WAAW,CAAC,KAAc;QACjC,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;SACnC;IACL,CAAC;IACD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACnC,CAAC;IACD;;OAEG;IACH,IAAW,YAAY,CAAC,KAAa;QACjC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5G,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;aAC9B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU,CAAC,KAAa;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5G,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;aAC5B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,IAAU,EAAE,KAAqB;QACnD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IACD;;OAEG;IACI,WAAW;QACd,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QAC5C,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QAC5C,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,OAAO;QACV,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5G,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,OAAO,EAAE,CAAC;aACnB;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YAC9B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,MAAM,CAAC,KAAK,CACR,wNAAwN,CAC3N,CAAC;IACN,CAAC;CACJ", "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Quaternion } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Node } from \"../node\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { GizmoAnchorPoint, GizmoCoordinatesMode, GizmoAxisCache, IGizmo } from \"./gizmo\";\r\nimport { Gizmo } from \"./gizmo\";\r\nimport type { IAxisDragGizmo } from \"./axisDragGizmo\";\r\nimport { AxisDragGizmo } from \"./axisDragGizmo\";\r\nimport type { IPlaneDragGizmo } from \"./planeDragGizmo\";\r\nimport { PlaneDragGizmo } from \"./planeDragGizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport type { GizmoManager } from \"./gizmoManager\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\n\r\n/**\r\n * Interface for position gizmo\r\n */\r\nexport interface IPositionGizmo extends IGizmo {\r\n    /** Internal gizmo used for interactions on the x axis */\r\n    xGizmo: IAxisDragGizmo;\r\n    /** Internal gizmo used for interactions on the y axis */\r\n    yGizmo: IAxisDragGizmo;\r\n    /** Internal gizmo used for interactions on the z axis */\r\n    zGizmo: IAxisDragGizmo;\r\n    /** Internal gizmo used for interactions on the yz plane */\r\n    xPlaneGizmo: IPlaneDragGizmo;\r\n    /** Internal gizmo used for interactions on the xz plane */\r\n    yPlaneGizmo: IPlaneDragGizmo;\r\n    /** Internal gizmo used for interactions on the xy plane */\r\n    zPlaneGizmo: IPlaneDragGizmo;\r\n    /** True when the mouse pointer is dragging a gizmo mesh */\r\n    readonly isDragging: boolean;\r\n    /** Fires an event when any of it's sub gizmos are dragged */\r\n    onDragStartObservable: Observable<unknown>;\r\n    /** Fires an event when any of it's sub gizmos are being dragged */\r\n    onDragObservable: Observable<unknown>;\r\n    /** Fires an event when any of it's sub gizmos are released from dragging */\r\n    onDragEndObservable: Observable<unknown>;\r\n    /**\r\n     * If the planar drag gizmo is enabled\r\n     * setting this will enable/disable XY, XZ and YZ planes regardless of individual gizmo settings.\r\n     */\r\n    planarGizmoEnabled: boolean;\r\n    /** Drag distance in babylon units that the gizmo will snap to when dragged */\r\n    snapDistance: number;\r\n    /**\r\n     * Builds Gizmo Axis Cache to enable features such as hover state preservation and graying out other axis during manipulation\r\n     * @param mesh Axis gizmo mesh\r\n     * @param cache Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    addToAxisCache(mesh: Mesh, cache: GizmoAxisCache): void;\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    releaseDrag(): void;\r\n}\r\n\r\n/**\r\n * Additional options for the position gizmo\r\n */\r\nexport interface PositionGizmoOptions {\r\n    /**\r\n     * Additional transform applied to the gizmo.\r\n     * @See Gizmo.additionalTransformNode for more detail\r\n     */\r\n    additionalTransformNode?: TransformNode;\r\n}\r\n\r\n/**\r\n * Gizmo that enables dragging a mesh along 3 axis\r\n */\r\nexport class PositionGizmo extends Gizmo implements IPositionGizmo {\r\n    /**\r\n     * Internal gizmo used for interactions on the x axis\r\n     */\r\n    public xGizmo: IAxisDragGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the y axis\r\n     */\r\n    public yGizmo: IAxisDragGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the z axis\r\n     */\r\n    public zGizmo: IAxisDragGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the yz plane\r\n     */\r\n    public xPlaneGizmo: IPlaneDragGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the xz plane\r\n     */\r\n    public yPlaneGizmo: IPlaneDragGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the xy plane\r\n     */\r\n    public zPlaneGizmo: IPlaneDragGizmo;\r\n\r\n    /**\r\n     * protected variables\r\n     */\r\n    protected _meshAttached: Nullable<AbstractMesh> = null;\r\n    protected _nodeAttached: Nullable<Node> = null;\r\n    protected _snapDistance: number;\r\n    protected _observables: Observer<PointerInfo>[] = [];\r\n\r\n    /** Node Caching for quick lookup */\r\n    protected _gizmoAxisCache: Map<Mesh, GizmoAxisCache> = new Map();\r\n\r\n    /** Fires an event when any of it's sub gizmos are dragged */\r\n    public onDragStartObservable = new Observable();\r\n    /** Fires an event when any of it's sub gizmos are being dragged */\r\n    public onDragObservable = new Observable();\r\n    /** Fires an event when any of it's sub gizmos are released from dragging */\r\n    public onDragEndObservable = new Observable();\r\n\r\n    /**\r\n     * If set to true, planar drag is enabled\r\n     */\r\n    protected _planarGizmoEnabled = false;\r\n\r\n    public get attachedMesh() {\r\n        return this._meshAttached;\r\n    }\r\n    public set attachedMesh(mesh: Nullable<AbstractMesh>) {\r\n        this._meshAttached = mesh;\r\n        this._nodeAttached = mesh;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo].forEach((gizmo) => {\r\n            if (gizmo.isEnabled) {\r\n                gizmo.attachedMesh = mesh;\r\n            } else {\r\n                gizmo.attachedMesh = null;\r\n            }\r\n        });\r\n    }\r\n\r\n    public get attachedNode() {\r\n        return this._nodeAttached;\r\n    }\r\n    public set attachedNode(node: Nullable<Node>) {\r\n        this._meshAttached = null;\r\n        this._nodeAttached = node;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo].forEach((gizmo) => {\r\n            if (gizmo.isEnabled) {\r\n                gizmo.attachedNode = node;\r\n            } else {\r\n                gizmo.attachedNode = null;\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * True when the mouse pointer is hovering a gizmo mesh\r\n     */\r\n    public get isHovered() {\r\n        return this.xGizmo.isHovered || this.yGizmo.isHovered || this.zGizmo.isHovered || this.xPlaneGizmo.isHovered || this.yPlaneGizmo.isHovered || this.zPlaneGizmo.isHovered;\r\n    }\r\n\r\n    public get isDragging() {\r\n        return (\r\n            this.xGizmo.dragBehavior.dragging ||\r\n            this.yGizmo.dragBehavior.dragging ||\r\n            this.zGizmo.dragBehavior.dragging ||\r\n            this.xPlaneGizmo.dragBehavior.dragging ||\r\n            this.yPlaneGizmo.dragBehavior.dragging ||\r\n            this.zPlaneGizmo.dragBehavior.dragging\r\n        );\r\n    }\r\n\r\n    public get additionalTransformNode() {\r\n        return this._additionalTransformNode;\r\n    }\r\n\r\n    public set additionalTransformNode(transformNode: TransformNode | undefined) {\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo].forEach((gizmo) => {\r\n            gizmo.additionalTransformNode = transformNode;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a PositionGizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param thickness display gizmo axis thickness\r\n     * @param gizmoManager\r\n     * @param options More options\r\n     */\r\n    constructor(gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer, thickness: number = 1, gizmoManager?: GizmoManager, options?: PositionGizmoOptions) {\r\n        super(gizmoLayer);\r\n        this.xGizmo = new AxisDragGizmo(new Vector3(1, 0, 0), Color3.Red().scale(0.5), gizmoLayer, this, thickness);\r\n        this.yGizmo = new AxisDragGizmo(new Vector3(0, 1, 0), Color3.Green().scale(0.5), gizmoLayer, this, thickness);\r\n        this.zGizmo = new AxisDragGizmo(new Vector3(0, 0, 1), Color3.Blue().scale(0.5), gizmoLayer, this, thickness);\r\n\r\n        this.xPlaneGizmo = new PlaneDragGizmo(new Vector3(1, 0, 0), Color3.Red().scale(0.5), this.gizmoLayer, this);\r\n        this.yPlaneGizmo = new PlaneDragGizmo(new Vector3(0, 1, 0), Color3.Green().scale(0.5), this.gizmoLayer, this);\r\n        this.zPlaneGizmo = new PlaneDragGizmo(new Vector3(0, 0, 1), Color3.Blue().scale(0.5), this.gizmoLayer, this);\r\n\r\n        this.additionalTransformNode = options?.additionalTransformNode;\r\n\r\n        // Relay drag events\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo].forEach((gizmo) => {\r\n            gizmo.dragBehavior.onDragStartObservable.add(() => {\r\n                this.onDragStartObservable.notifyObservers({});\r\n            });\r\n            gizmo.dragBehavior.onDragObservable.add(() => {\r\n                this.onDragObservable.notifyObservers({});\r\n            });\r\n            gizmo.dragBehavior.onDragEndObservable.add(() => {\r\n                this.onDragEndObservable.notifyObservers({});\r\n            });\r\n        });\r\n\r\n        this.attachedMesh = null;\r\n\r\n        if (gizmoManager) {\r\n            gizmoManager.addToAxisCache(this._gizmoAxisCache);\r\n        } else {\r\n            // Only subscribe to pointer event if gizmoManager isnt\r\n            Gizmo.GizmoAxisPointerObserver(gizmoLayer, this._gizmoAxisCache);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If the planar drag gizmo is enabled\r\n     * setting this will enable/disable XY, XZ and YZ planes regardless of individual gizmo settings.\r\n     */\r\n    public set planarGizmoEnabled(value: boolean) {\r\n        this._planarGizmoEnabled = value;\r\n        [this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.isEnabled = value;\r\n                if (value) {\r\n                    if (gizmo.attachedMesh) {\r\n                        gizmo.attachedMesh = this.attachedMesh;\r\n                    } else {\r\n                        gizmo.attachedNode = this.attachedNode;\r\n                    }\r\n                }\r\n            }\r\n        }, this);\r\n    }\r\n    public get planarGizmoEnabled(): boolean {\r\n        return this._planarGizmoEnabled;\r\n    }\r\n\r\n    /**\r\n     * posture that the gizmo will be display\r\n     * When set null, default value will be used (Quaternion(0, 0, 0, 1))\r\n     */\r\n    public get customRotationQuaternion(): Nullable<Quaternion> {\r\n        return this._customRotationQuaternion;\r\n    }\r\n\r\n    public set customRotationQuaternion(customRotationQuaternion: Nullable<Quaternion>) {\r\n        this._customRotationQuaternion = customRotationQuaternion;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.customRotationQuaternion = customRotationQuaternion;\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * If set the gizmo's rotation will be updated to match the attached mesh each frame (Default: true)\r\n     * NOTE: This is only possible for meshes with uniform scaling, as otherwise it's not possible to decompose the rotation\r\n     */\r\n    public set updateGizmoRotationToMatchAttachedMesh(value: boolean) {\r\n        this._updateGizmoRotationToMatchAttachedMesh = value;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.updateGizmoRotationToMatchAttachedMesh = value;\r\n            }\r\n        });\r\n    }\r\n    public get updateGizmoRotationToMatchAttachedMesh() {\r\n        return this._updateGizmoRotationToMatchAttachedMesh;\r\n    }\r\n\r\n    public set updateGizmoPositionToMatchAttachedMesh(value: boolean) {\r\n        this._updateGizmoPositionToMatchAttachedMesh = value;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.updateGizmoPositionToMatchAttachedMesh = value;\r\n            }\r\n        });\r\n    }\r\n    public get updateGizmoPositionToMatchAttachedMesh() {\r\n        return this._updateGizmoPositionToMatchAttachedMesh;\r\n    }\r\n\r\n    public set anchorPoint(value: GizmoAnchorPoint) {\r\n        this._anchorPoint = value;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo].forEach((gizmo) => {\r\n            gizmo.anchorPoint = value;\r\n        });\r\n    }\r\n    public get anchorPoint() {\r\n        return this._anchorPoint;\r\n    }\r\n\r\n    /**\r\n     * Set the coordinate system to use. By default it's local.\r\n     * But it's possible for a user to tweak so its local for translation and world for rotation.\r\n     * In that case, setting the coordinate system will change `updateGizmoRotationToMatchAttachedMesh` and `updateGizmoPositionToMatchAttachedMesh`\r\n     */\r\n    public set coordinatesMode(coordinatesMode: GizmoCoordinatesMode) {\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo].forEach((gizmo) => {\r\n            gizmo.coordinatesMode = coordinatesMode;\r\n        });\r\n    }\r\n\r\n    public set updateScale(value: boolean) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.updateScale = value;\r\n            this.yGizmo.updateScale = value;\r\n            this.zGizmo.updateScale = value;\r\n        }\r\n    }\r\n    public get updateScale() {\r\n        return this.xGizmo.updateScale;\r\n    }\r\n    /**\r\n     * Drag distance in babylon units that the gizmo will snap to when dragged (Default: 0)\r\n     */\r\n    public set snapDistance(value: number) {\r\n        this._snapDistance = value;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.snapDistance = value;\r\n            }\r\n        });\r\n    }\r\n    public get snapDistance() {\r\n        return this._snapDistance;\r\n    }\r\n\r\n    /**\r\n     * Ratio for the scale of the gizmo (Default: 1)\r\n     */\r\n    public set scaleRatio(value: number) {\r\n        this._scaleRatio = value;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.scaleRatio = value;\r\n            }\r\n        });\r\n    }\r\n    public get scaleRatio() {\r\n        return this._scaleRatio;\r\n    }\r\n\r\n    /**\r\n     * Builds Gizmo Axis Cache to enable features such as hover state preservation and graying out other axis during manipulation\r\n     * @param mesh Axis gizmo mesh\r\n     * @param cache Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    public addToAxisCache(mesh: Mesh, cache: GizmoAxisCache) {\r\n        this._gizmoAxisCache.set(mesh, cache);\r\n    }\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    public releaseDrag() {\r\n        this.xGizmo.dragBehavior.releaseDrag();\r\n        this.yGizmo.dragBehavior.releaseDrag();\r\n        this.zGizmo.dragBehavior.releaseDrag();\r\n        this.xPlaneGizmo.dragBehavior.releaseDrag();\r\n        this.yPlaneGizmo.dragBehavior.releaseDrag();\r\n        this.zPlaneGizmo.dragBehavior.releaseDrag();\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public dispose() {\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.dispose();\r\n            }\r\n        });\r\n        this._observables.forEach((obs) => {\r\n            this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(obs);\r\n        });\r\n        this.onDragStartObservable.clear();\r\n        this.onDragObservable.clear();\r\n        this.onDragEndObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * CustomMeshes are not supported by this gizmo\r\n     */\r\n    public setCustomMesh() {\r\n        Logger.Error(\r\n            \"Custom meshes are not supported on this gizmo, please set the custom meshes on the gizmos contained within this one (gizmo.xGizmo, gizmo.yGizmo, gizmo.zGizmo,gizmo.xPlaneGizmo, gizmo.yPlaneGizmo, gizmo.zPlaneGizmo)\"\r\n        );\r\n    }\r\n}\r\n"]}