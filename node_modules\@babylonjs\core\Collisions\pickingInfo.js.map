{"version": 3, "file": "pickingInfo.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Collisions/pickingInfo.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAGpE,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAKjD;;;GAGG;AACH,MAAM,OAAO,WAAW;IAAxB;QACI;;WAEG;QACI,QAAG,GAAG,KAAK,CAAC;QACnB;;WAEG;QACI,aAAQ,GAAG,CAAC,CAAC;QACpB;;WAEG;QACI,gBAAW,GAAsB,IAAI,CAAC;QAC7C;;WAEG;QACI,eAAU,GAA2B,IAAI,CAAC;QACjD,sIAAsI;QAC/H,OAAE,GAAG,CAAC,CAAC;QACd,sIAAsI;QAC/H,OAAE,GAAG,CAAC,CAAC;QACd,oHAAoH;QAC7G,WAAM,GAAG,CAAC,CAAC,CAAC;QACnB,uHAAuH;QAChH,kBAAa,GAAG,CAAC,CAAC,CAAC;QAC1B,wCAAwC;QACjC,cAAS,GAAG,CAAC,CAAC;QACrB,6EAA6E;QACtE,iBAAY,GAAqB,IAAI,CAAC;QAC7C,+FAA+F;QACxF,sBAAiB,GAAG,CAAC,CAAC,CAAC;QAC9B;;WAEG;QACI,QAAG,GAAkB,IAAI,CAAC;QACjC;;WAEG;QACI,eAAU,GAA2B,IAAI,CAAC;QACjD;;WAEG;QACI,iBAAY,GAA4B,IAAI,CAAC;QACpD;;;WAGG;QACI,kBAAa,GAA4B,IAAI,CAAC;IAoIzD,CAAC;IAlIG;;;;;;OAMG;IACI,SAAS,CAAC,mBAAmB,GAAG,KAAK,EAAE,kBAAkB,GAAG,IAAI;QACnE,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,EAAE;YAC7G,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAE3C,IAAI,OAAO,EAAE,MAAM,KAAK,CAAC,EAAE;YACvB,OAAO,GAAG,IAAI,CAAC;SAClB;QAED,IAAI,MAAe,CAAC;QAEpB,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEnC,IAAI,kBAAkB,EAAE;YACpB,MAAM,OAAO,GAAe,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAErF,IAAI,OAAO,GAAG,OAAO;gBACjB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;gBACrE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC5H,IAAI,OAAO,GAAG,OAAO;gBACjB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;gBACzE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9I,IAAI,OAAO,GAAG,OAAO;gBACjB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;gBACzE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAE9I,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;YAEjD,MAAM,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;SACjI;aAAM;YACH,MAAM,SAAS,GAAe,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAEzF,MAAM,OAAO,GAAG,OAAO;gBACnB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;gBACvE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAClI,MAAM,OAAO,GAAG,OAAO;gBACnB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;gBAC3E,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACpJ,MAAM,OAAO,GAAG,OAAO;gBACnB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;gBAC3E,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAEpJ,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAEvC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACtC;QAED,MAAM,sBAAsB,GAAG,CAAC,UAAwB,EAAE,CAAU,EAAE,EAAE;YACpE,IAAI,EAAE,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;YAErC,IAAI,UAAU,CAAC,iBAAiB,EAAE;gBAC9B,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAClC,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC1B,EAAE,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACrC,EAAE,CAAC,MAAM,EAAE,CAAC;gBACZ,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAExC,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aAC7B;YAED,OAAO,CAAC,oBAAoB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC;QAEF,IAAI,mBAAmB,EAAE;YACrB,sBAAsB,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;SACnD;QAED,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,MAAM,0BAA0B,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAE1E,IAAI,CAAC,mBAAmB,EAAE;gBACtB,4GAA4G;gBAC5G,sBAAsB,CAAC,IAAI,CAAC,UAAU,EAAE,0BAA0B,CAAC,CAAC;aACvE;YAED,+DAA+D;YAC/D,IAAI,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;gBACjE,MAAM,CAAC,aAAa,EAAE,CAAC;aAC1B;SACJ;QAED,MAAM,CAAC,SAAS,EAAE,CAAC;QAEnB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,KAAK,GAAG,YAAY,CAAC,MAAM;QACpD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,KAAK,CAAC,EAAE;YACnE,OAAO,IAAI,CAAC;SACf;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,IAAI,CAAC;SACf;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACnD,IAAI,CAAC,GAAG,EAAE;YACN,OAAO,IAAI,CAAC;SACf;QAED,IAAI,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/D,IAAI,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACnE,IAAI,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEnE,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzB,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzB,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;QAEzC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC;CACJ", "sourcesContent": ["import type { Nullable, FloatArray } from \"../types\";\r\nimport { Vector3, Vector2, TmpVectors } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport type { Sprite } from \"../Sprites/sprite\";\r\n\r\nimport type { Ray } from \"../Culling/ray\";\r\n\r\n/**\r\n * Information about the result of picking within a scene\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/interactions/picking_collisions\r\n */\r\nexport class PickingInfo {\r\n    /**\r\n     * If the pick collided with an object\r\n     */\r\n    public hit = false;\r\n    /**\r\n     * Distance away where the pick collided\r\n     */\r\n    public distance = 0;\r\n    /**\r\n     * The location of pick collision\r\n     */\r\n    public pickedPoint: Nullable<Vector3> = null;\r\n    /**\r\n     * The mesh corresponding the pick collision\r\n     */\r\n    public pickedMesh: Nullable<AbstractMesh> = null;\r\n    /** (See getTextureCoordinates) The barycentric U coordinate that is used when calculating the texture coordinates of the collision.*/\r\n    public bu = 0;\r\n    /** (See getTextureCoordinates) The barycentric V coordinate that is used when calculating the texture coordinates of the collision.*/\r\n    public bv = 0;\r\n    /** The index of the face on the mesh that was picked, or the index of the Line if the picked Mesh is a LinesMesh */\r\n    public faceId = -1;\r\n    /** The index of the face on the subMesh that was picked, or the index of the Line if the picked Mesh is a LinesMesh */\r\n    public subMeshFaceId = -1;\r\n    /** Id of the submesh that was picked */\r\n    public subMeshId = 0;\r\n    /** If a sprite was picked, this will be the sprite the pick collided with */\r\n    public pickedSprite: Nullable<Sprite> = null;\r\n    /** If we are picking a mesh with thin instance, this will give you the picked thin instance */\r\n    public thinInstanceIndex = -1;\r\n    /**\r\n     * The ray that was used to perform the picking.\r\n     */\r\n    public ray: Nullable<Ray> = null;\r\n    /**\r\n     * If a mesh was used to do the picking (eg. 6dof controller) as a \"near interaction\", this will be populated.\r\n     */\r\n    public originMesh: Nullable<AbstractMesh> = null;\r\n    /**\r\n     * The aim-space transform of the input used for picking, if it is an XR input source.\r\n     */\r\n    public aimTransform: Nullable<TransformNode> = null;\r\n    /**\r\n     * The grip-space transform of the input used for picking, if it is an XR input source.\r\n     * Some XR sources, such as input coming from head mounted displays, do not have this.\r\n     */\r\n    public gripTransform: Nullable<TransformNode> = null;\r\n\r\n    /**\r\n     * Gets the normal corresponding to the face the pick collided with\r\n     * @param useWorldCoordinates If the resulting normal should be relative to the world (default: false)\r\n     * @param useVerticesNormals If the vertices normals should be used to calculate the normal instead of the normal map (default: true)\r\n     * @returns The normal corresponding to the face the pick collided with\r\n     * @remarks Note that the returned normal will always point towards the picking ray.\r\n     */\r\n    public getNormal(useWorldCoordinates = false, useVerticesNormals = true): Nullable<Vector3> {\r\n        if (!this.pickedMesh || (useVerticesNormals && !this.pickedMesh.isVerticesDataPresent(VertexBuffer.NormalKind))) {\r\n            return null;\r\n        }\r\n\r\n        let indices = this.pickedMesh.getIndices();\r\n\r\n        if (indices?.length === 0) {\r\n            indices = null;\r\n        }\r\n\r\n        let result: Vector3;\r\n\r\n        const tmp0 = TmpVectors.Vector3[0];\r\n        const tmp1 = TmpVectors.Vector3[1];\r\n        const tmp2 = TmpVectors.Vector3[2];\r\n\r\n        if (useVerticesNormals) {\r\n            const normals = <FloatArray>this.pickedMesh.getVerticesData(VertexBuffer.NormalKind);\r\n\r\n            let normal0 = indices\r\n                ? Vector3.FromArrayToRef(normals, indices[this.faceId * 3] * 3, tmp0)\r\n                : tmp0.copyFromFloats(normals[this.faceId * 3 * 3], normals[this.faceId * 3 * 3 + 1], normals[this.faceId * 3 * 3 + 2]);\r\n            let normal1 = indices\r\n                ? Vector3.FromArrayToRef(normals, indices[this.faceId * 3 + 1] * 3, tmp1)\r\n                : tmp1.copyFromFloats(normals[(this.faceId * 3 + 1) * 3], normals[(this.faceId * 3 + 1) * 3 + 1], normals[(this.faceId * 3 + 1) * 3 + 2]);\r\n            let normal2 = indices\r\n                ? Vector3.FromArrayToRef(normals, indices[this.faceId * 3 + 2] * 3, tmp2)\r\n                : tmp2.copyFromFloats(normals[(this.faceId * 3 + 2) * 3], normals[(this.faceId * 3 + 2) * 3 + 1], normals[(this.faceId * 3 + 2) * 3 + 2]);\r\n\r\n            normal0 = normal0.scale(this.bu);\r\n            normal1 = normal1.scale(this.bv);\r\n            normal2 = normal2.scale(1.0 - this.bu - this.bv);\r\n\r\n            result = new Vector3(normal0.x + normal1.x + normal2.x, normal0.y + normal1.y + normal2.y, normal0.z + normal1.z + normal2.z);\r\n        } else {\r\n            const positions = <FloatArray>this.pickedMesh.getVerticesData(VertexBuffer.PositionKind);\r\n\r\n            const vertex1 = indices\r\n                ? Vector3.FromArrayToRef(positions, indices[this.faceId * 3] * 3, tmp0)\r\n                : tmp0.copyFromFloats(positions[this.faceId * 3 * 3], positions[this.faceId * 3 * 3 + 1], positions[this.faceId * 3 * 3 + 2]);\r\n            const vertex2 = indices\r\n                ? Vector3.FromArrayToRef(positions, indices[this.faceId * 3 + 1] * 3, tmp1)\r\n                : tmp1.copyFromFloats(positions[(this.faceId * 3 + 1) * 3], positions[(this.faceId * 3 + 1) * 3 + 1], positions[(this.faceId * 3 + 1) * 3 + 2]);\r\n            const vertex3 = indices\r\n                ? Vector3.FromArrayToRef(positions, indices[this.faceId * 3 + 2] * 3, tmp2)\r\n                : tmp2.copyFromFloats(positions[(this.faceId * 3 + 2) * 3], positions[(this.faceId * 3 + 2) * 3 + 1], positions[(this.faceId * 3 + 2) * 3 + 2]);\r\n\r\n            const p1p2 = vertex1.subtract(vertex2);\r\n            const p3p2 = vertex3.subtract(vertex2);\r\n\r\n            result = Vector3.Cross(p1p2, p3p2);\r\n        }\r\n\r\n        const transformNormalToWorld = (pickedMesh: AbstractMesh, n: Vector3) => {\r\n            let wm = pickedMesh.getWorldMatrix();\r\n\r\n            if (pickedMesh.nonUniformScaling) {\r\n                TmpVectors.Matrix[0].copyFrom(wm);\r\n                wm = TmpVectors.Matrix[0];\r\n                wm.setTranslationFromFloats(0, 0, 0);\r\n                wm.invert();\r\n                wm.transposeToRef(TmpVectors.Matrix[1]);\r\n\r\n                wm = TmpVectors.Matrix[1];\r\n            }\r\n\r\n            Vector3.TransformNormalToRef(n, wm, n);\r\n        };\r\n\r\n        if (useWorldCoordinates) {\r\n            transformNormalToWorld(this.pickedMesh, result);\r\n        }\r\n\r\n        if (this.ray) {\r\n            const normalForDirectionChecking = TmpVectors.Vector3[0].copyFrom(result);\r\n\r\n            if (!useWorldCoordinates) {\r\n                // the normal has not been transformed to world space as part as the normal processing, so we must do it now\r\n                transformNormalToWorld(this.pickedMesh, normalForDirectionChecking);\r\n            }\r\n\r\n            // Flip the normal if the picking ray is in the same direction.\r\n            if (Vector3.Dot(normalForDirectionChecking, this.ray.direction) > 0) {\r\n                result.negateInPlace();\r\n            }\r\n        }\r\n\r\n        result.normalize();\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Gets the texture coordinates of where the pick occurred\r\n     * @param uvSet The UV set to use to calculate the texture coordinates (default: VertexBuffer.UVKind)\r\n     * @returns The vector containing the coordinates of the texture\r\n     */\r\n    public getTextureCoordinates(uvSet = VertexBuffer.UVKind): Nullable<Vector2> {\r\n        if (!this.pickedMesh || !this.pickedMesh.isVerticesDataPresent(uvSet)) {\r\n            return null;\r\n        }\r\n\r\n        const indices = this.pickedMesh.getIndices();\r\n        if (!indices) {\r\n            return null;\r\n        }\r\n\r\n        const uvs = this.pickedMesh.getVerticesData(uvSet);\r\n        if (!uvs) {\r\n            return null;\r\n        }\r\n\r\n        let uv0 = Vector2.FromArray(uvs, indices[this.faceId * 3] * 2);\r\n        let uv1 = Vector2.FromArray(uvs, indices[this.faceId * 3 + 1] * 2);\r\n        let uv2 = Vector2.FromArray(uvs, indices[this.faceId * 3 + 2] * 2);\r\n\r\n        uv0 = uv0.scale(this.bu);\r\n        uv1 = uv1.scale(this.bv);\r\n        uv2 = uv2.scale(1.0 - this.bu - this.bv);\r\n\r\n        return new Vector2(uv0.x + uv1.x + uv2.x, uv0.y + uv1.y + uv2.y);\r\n    }\r\n}\r\n"]}