{"version": 3, "file": "smartArray.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/smartArray.ts"], "names": [], "mappings": "AAeA;;GAEG;AACH,MAAM,OAAO,UAAU;IAanB;;;OAGG;IACH,YAAY,QAAgB;QAX5B;;WAEG;QACI,WAAM,GAAW,CAAC,CAAC;QAStB,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChC,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,IAAI,CAAC,KAAQ;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC;QAEjC,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;SACzB;IACL,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,IAA0B;QACrC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC9C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;SAC1B;IACL,CAAC;IAED;;;OAGG;IACI,IAAI,CAAC,SAAiC;QACzC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;SACxB;IACL,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,KAAU;QACpB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,OAAO;SACV;QACD,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACvD;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;SAC3D;IACL,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,KAAQ;QACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE1C,IAAI,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE;YACzB,OAAO,CAAC,CAAC,CAAC;SACb;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,KAAQ;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IACtC,CAAC;;AAED,UAAU;AACK,oBAAS,GAAG,CAAC,AAAJ,CAAK;AAGjC;;;GAGG;AACH,MAAM,OAAO,qBAAyB,SAAQ,UAAa;IAA3D;;QACY,iBAAY,GAAG,CAAC,CAAC;IAyD7B,CAAC;IAvDG;;;;OAIG;IACI,IAAI,CAAC,KAAQ;QAChB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAElB,IAAI,CAAO,KAAM,CAAC,iBAAiB,EAAE;YAC3B,KAAM,CAAC,iBAAiB,GAAG,EAAE,CAAC;SACvC;QAEK,KAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;IACjE,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,KAAQ;QAC3B,IAAU,KAAM,CAAC,iBAAiB,IAAU,KAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,YAAY,EAAE;YAClG,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,KAAK;QACR,KAAK,CAAC,KAAK,EAAE,CAAC;QACd,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,KAAU;QACnC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,OAAO;SACV;QACD,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACvD;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC/C,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;YAC1C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAC9B;IACL,CAAC;CACJ", "sourcesContent": ["/**\r\n * Defines an array and its length.\r\n * It can be helpful to group result from both Arrays and smart arrays in one structure.\r\n */\r\nexport interface ISmartArrayLike<T> {\r\n    /**\r\n     * The data of the array.\r\n     */\r\n    data: Array<T>;\r\n    /**\r\n     * The active length of the array.\r\n     */\r\n    length: number;\r\n}\r\n\r\n/**\r\n * Defines an GC Friendly array where the backfield array do not shrink to prevent over allocations.\r\n */\r\nexport class SmartArray<T> implements ISmartArrayLike<T> {\r\n    /**\r\n     * The full set of data from the array.\r\n     */\r\n    public data: Array<T>;\r\n\r\n    /**\r\n     * The active length of the array.\r\n     */\r\n    public length: number = 0;\r\n\r\n    protected _id: number;\r\n\r\n    /**\r\n     * Instantiates a Smart Array.\r\n     * @param capacity defines the default capacity of the array.\r\n     */\r\n    constructor(capacity: number) {\r\n        this.data = new Array(capacity);\r\n        this._id = SmartArray._GlobalId++;\r\n    }\r\n\r\n    /**\r\n     * Pushes a value at the end of the active data.\r\n     * @param value defines the object to push in the array.\r\n     */\r\n    public push(value: T): void {\r\n        this.data[this.length++] = value;\r\n\r\n        if (this.length > this.data.length) {\r\n            this.data.length *= 2;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Iterates over the active data and apply the lambda to them.\r\n     * @param func defines the action to apply on each value.\r\n     */\r\n    public forEach(func: (content: T) => void): void {\r\n        for (let index = 0; index < this.length; index++) {\r\n            func(this.data[index]);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sorts the full sets of data.\r\n     * @param compareFn defines the comparison function to apply.\r\n     */\r\n    public sort(compareFn: (a: T, b: T) => number): void {\r\n        this.data.sort(compareFn);\r\n    }\r\n\r\n    /**\r\n     * Resets the active data to an empty array.\r\n     */\r\n    public reset(): void {\r\n        this.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Releases all the data from the array as well as the array.\r\n     */\r\n    public dispose(): void {\r\n        this.reset();\r\n\r\n        if (this.data) {\r\n            this.data.length = 0;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Concats the active data with a given array.\r\n     * @param array defines the data to concatenate with.\r\n     */\r\n    public concat(array: any): void {\r\n        if (array.length === 0) {\r\n            return;\r\n        }\r\n        if (this.length + array.length > this.data.length) {\r\n            this.data.length = (this.length + array.length) * 2;\r\n        }\r\n\r\n        for (let index = 0; index < array.length; index++) {\r\n            this.data[this.length++] = (array.data || array)[index];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the position of a value in the active data.\r\n     * @param value defines the value to find the index for\r\n     * @returns the index if found in the active data otherwise -1\r\n     */\r\n    public indexOf(value: T): number {\r\n        const position = this.data.indexOf(value);\r\n\r\n        if (position >= this.length) {\r\n            return -1;\r\n        }\r\n\r\n        return position;\r\n    }\r\n\r\n    /**\r\n     * Returns whether an element is part of the active data.\r\n     * @param value defines the value to look for\r\n     * @returns true if found in the active data otherwise false\r\n     */\r\n    public contains(value: T): boolean {\r\n        return this.indexOf(value) !== -1;\r\n    }\r\n\r\n    // Statics\r\n    private static _GlobalId = 0;\r\n}\r\n\r\n/**\r\n * Defines an GC Friendly array where the backfield array do not shrink to prevent over allocations.\r\n * The data in this array can only be present once\r\n */\r\nexport class SmartArrayNoDuplicate<T> extends SmartArray<T> {\r\n    private _duplicateId = 0;\r\n\r\n    /**\r\n     * Pushes a value at the end of the active data.\r\n     * THIS DOES NOT PREVENT DUPPLICATE DATA\r\n     * @param value defines the object to push in the array.\r\n     */\r\n    public push(value: T): void {\r\n        super.push(value);\r\n\r\n        if (!(<any>value).__smartArrayFlags) {\r\n            (<any>value).__smartArrayFlags = {};\r\n        }\r\n\r\n        (<any>value).__smartArrayFlags[this._id] = this._duplicateId;\r\n    }\r\n\r\n    /**\r\n     * Pushes a value at the end of the active data.\r\n     * If the data is already present, it won t be added again\r\n     * @param value defines the object to push in the array.\r\n     * @returns true if added false if it was already present\r\n     */\r\n    public pushNoDuplicate(value: T): boolean {\r\n        if ((<any>value).__smartArrayFlags && (<any>value).__smartArrayFlags[this._id] === this._duplicateId) {\r\n            return false;\r\n        }\r\n        this.push(value);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Resets the active data to an empty array.\r\n     */\r\n    public reset(): void {\r\n        super.reset();\r\n        this._duplicateId++;\r\n    }\r\n\r\n    /**\r\n     * Concats the active data with a given array.\r\n     * This ensures no duplicate will be present in the result.\r\n     * @param array defines the data to concatenate with.\r\n     */\r\n    public concatWithNoDuplicate(array: any): void {\r\n        if (array.length === 0) {\r\n            return;\r\n        }\r\n        if (this.length + array.length > this.data.length) {\r\n            this.data.length = (this.length + array.length) * 2;\r\n        }\r\n\r\n        for (let index = 0; index < array.length; index++) {\r\n            const item = (array.data || array)[index];\r\n            this.pushNoDuplicate(item);\r\n        }\r\n    }\r\n}\r\n"]}