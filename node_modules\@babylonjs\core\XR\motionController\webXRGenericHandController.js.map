{"version": 3, "file": "webXRGenericHandController.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/motionController/webXRGenericHandController.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAGhF,OAAO,EAAE,4BAA4B,EAAE,MAAM,gCAAgC,CAAC;AAE9E;;GAEG;AACH,MAAM,OAAO,0BAA2B,SAAQ,6BAA6B;IAGzE;;;;;OAKG;IACH,YAAY,KAAY,EAAE,aAA6C,EAAE,UAAsC;QAC3G,uEAAuE;QACvE,KAAK,CAAC,KAAK,EAAE,6BAA6B,CAAC,UAAU,CAAC,EAAE,aAAa,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAVtF,cAAS,GAAG,2BAA2B,CAAC;IAW/C,CAAC;IAES,mBAAmB;QACzB,OAAO;YACH,QAAQ,EAAE,iBAAiB;YAC3B,IAAI,EAAE,4CAA4C;SACrD,CAAC;IACN,CAAC;IAES,2BAA2B;QACjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB,CAAC,OAAuB;QACjD,QAAQ;IACZ,CAAC;IAES,YAAY,CAAC,MAAsB;QACzC,QAAQ;IACZ,CAAC;IAES,YAAY;QAClB,QAAQ;IACZ,CAAC;CACJ;AAED,wBAAwB;AACxB,4BAA4B,CAAC,kBAAkB,CAAC,2BAA2B,EAAE,CAAC,OAAsB,EAAE,KAAY,EAAE,EAAE;IAClH,OAAO,IAAI,0BAA0B,CAAC,KAAK,EAAO,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;AAC3F,CAAC,CAAC,CAAC;AAEH,oIAAoI;AACpI,MAAM,6BAA6B,GAA+B;IAC9D,IAAI,EAAE;QACF,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE;YACR,gEAAgE;YAChE,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;YACD,KAAK,EAAE;gBACH,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,OAAO;gBACrB,eAAe,EAAE,EAAE;aACtB;SACJ;QACD,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE,gCAAgC;QAC9C,SAAS,EAAE,UAAU;KACxB;IACD,KAAK,EAAE;QACH,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE;YACR,gEAAgE;YAChE,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;YACD,KAAK,EAAE;gBACH,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,OAAO;gBACrB,eAAe,EAAE,EAAE;aACtB;SACJ;QACD,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE,iCAAiC;QAC/C,SAAS,EAAE,WAAW;KACzB;IACD,IAAI,EAAE;QACF,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE;YACR,gEAAgE;YAChE,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;YACD,KAAK,EAAE;gBACH,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,OAAO;gBACrB,eAAe,EAAE,EAAE;aACtB;SACJ;QACD,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE,gCAAgC;QAC9C,SAAS,EAAE,UAAU;KACxB;CACJ,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport type { IMotionControllerLayoutMap, IMinimalMotionControllerObject, MotionControllerHandedness } from \"./webXRAbstractMotionController\";\r\nimport { WebXRAbstractMotionController } from \"./webXRAbstractMotionController\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { WebXRMotionControllerManager } from \"./webXRMotionControllerManager\";\r\n\r\n/**\r\n * A generic hand controller class that supports select and a secondary grasp\r\n */\r\nexport class WebXRGenericHandController extends WebXRAbstractMotionController {\r\n    public profileId = \"generic-hand-select-grasp\";\r\n\r\n    /**\r\n     * Create a new hand controller object, without loading a controller model\r\n     * @param scene the scene to use to create this controller\r\n     * @param gamepadObject the corresponding gamepad object\r\n     * @param handedness the handedness of the controller\r\n     */\r\n    constructor(scene: Scene, gamepadObject: IMinimalMotionControllerObject, handedness: MotionControllerHandedness) {\r\n        // Don't load the controller model - for now, hands have no real model.\r\n        super(scene, GenericHandSelectGraspProfile[handedness], gamepadObject, handedness, true);\r\n    }\r\n\r\n    protected _getFilenameAndPath(): { filename: string; path: string } {\r\n        return {\r\n            filename: \"generic.babylon\",\r\n            path: \"https://controllers.babylonjs.com/generic/\",\r\n        };\r\n    }\r\n\r\n    protected _getModelLoadingConstraints(): boolean {\r\n        return true;\r\n    }\r\n\r\n    protected _processLoadedModel(_meshes: AbstractMesh[]): void {\r\n        // no-op\r\n    }\r\n\r\n    protected _setRootMesh(meshes: AbstractMesh[]): void {\r\n        // no-op\r\n    }\r\n\r\n    protected _updateModel(): void {\r\n        // no-op\r\n    }\r\n}\r\n\r\n// register the profiles\r\nWebXRMotionControllerManager.RegisterController(\"generic-hand-select-grasp\", (xrInput: XRInputSource, scene: Scene) => {\r\n    return new WebXRGenericHandController(scene, <any>xrInput.gamepad, xrInput.handedness);\r\n});\r\n\r\n// https://github.com/immersive-web/webxr-input-profiles/blob/main/packages/registry/profiles/generic/generic-hand-select-grasp.json\r\nconst GenericHandSelectGraspProfile: IMotionControllerLayoutMap = {\r\n    left: {\r\n        selectComponentId: \"xr-standard-trigger\",\r\n        components: {\r\n            // eslint-disable-next-line @typescript-eslint/naming-convention\r\n            \"xr-standard-trigger\": {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 0,\r\n                },\r\n                rootNodeName: \"xr-standard-trigger\",\r\n                visualResponses: {},\r\n            },\r\n            grasp: {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 4,\r\n                },\r\n                rootNodeName: \"grasp\",\r\n                visualResponses: {},\r\n            },\r\n        },\r\n        gamepadMapping: \"xr-standard\",\r\n        rootNodeName: \"generic-hand-select-grasp-left\",\r\n        assetPath: \"left.glb\",\r\n    },\r\n    right: {\r\n        selectComponentId: \"xr-standard-trigger\",\r\n        components: {\r\n            // eslint-disable-next-line @typescript-eslint/naming-convention\r\n            \"xr-standard-trigger\": {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 0,\r\n                },\r\n                rootNodeName: \"xr-standard-trigger\",\r\n                visualResponses: {},\r\n            },\r\n            grasp: {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 4,\r\n                },\r\n                rootNodeName: \"grasp\",\r\n                visualResponses: {},\r\n            },\r\n        },\r\n        gamepadMapping: \"xr-standard\",\r\n        rootNodeName: \"generic-hand-select-grasp-right\",\r\n        assetPath: \"right.glb\",\r\n    },\r\n    none: {\r\n        selectComponentId: \"xr-standard-trigger\",\r\n        components: {\r\n            // eslint-disable-next-line @typescript-eslint/naming-convention\r\n            \"xr-standard-trigger\": {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 0,\r\n                },\r\n                rootNodeName: \"xr-standard-trigger\",\r\n                visualResponses: {},\r\n            },\r\n            grasp: {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 4,\r\n                },\r\n                rootNodeName: \"grasp\",\r\n                visualResponses: {},\r\n            },\r\n        },\r\n        gamepadMapping: \"xr-standard\",\r\n        rootNodeName: \"generic-hand-select-grasp-none\",\r\n        assetPath: \"none.glb\",\r\n    },\r\n};\r\n"]}