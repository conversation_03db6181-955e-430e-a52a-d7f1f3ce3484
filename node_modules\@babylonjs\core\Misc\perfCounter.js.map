{"version": 3, "file": "perfCounter.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/perfCounter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD;;;;;;;GAOG;AACH,MAAM,OAAO,WAAW;IAMpB;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACH;QACI,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,QAAgB,EAAE,WAAoB;QAClD,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;YACtB,OAAO;SACV;QACD,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC;QAC1B,IAAI,WAAW,EAAE;YACb,IAAI,CAAC,YAAY,EAAE,CAAC;SACvB;IACL,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;YACtB,OAAO;SACV;QACD,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC,GAAG,CAAC;IAClD,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,WAAoB,IAAI;QACzC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;YACtB,OAAO;SACV;QAED,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;QAED,MAAM,WAAW,GAAG,aAAa,CAAC,GAAG,CAAC;QACtC,IAAI,CAAC,QAAQ,GAAG,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAExD,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,YAAY,EAAE,CAAC;SACvB;IACL,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,QAAQ,CAAC;QACxC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,QAAQ,CAAC;QAE1C,iBAAiB;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE/D,kBAAkB;QAClB,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;QAC9B,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,EAAE;YAChC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC;YAC1E,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;YACxB,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;YAC7B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;SAC/B;IACL,CAAC;;AAzJD;;GAEG;AACW,mBAAO,GAAG,IAAI,CAAC", "sourcesContent": ["import { PrecisionDate } from \"./precisionDate\";\r\n\r\n/**\r\n * This class is used to track a performance counter which is number based.\r\n * The user has access to many properties which give statistics of different nature.\r\n *\r\n * The implementer can track two kinds of Performance Counter: time and count.\r\n * For time you can optionally call fetchNewFrame() to notify the start of a new frame to monitor, then call beginMonitoring() to start and endMonitoring() to record the lapsed time. endMonitoring takes a newFrame parameter for you to specify if the monitored time should be set for a new frame or accumulated to the current frame being monitored.\r\n * For count you first have to call fetchNewFrame() to notify the start of a new frame to monitor, then call addCount() how many time required to increment the count value you monitor.\r\n */\r\nexport class PerfCounter {\r\n    /**\r\n     * Gets or sets a global boolean to turn on and off all the counters\r\n     */\r\n    public static Enabled = true;\r\n\r\n    /**\r\n     * Returns the smallest value ever\r\n     */\r\n    public get min(): number {\r\n        return this._min;\r\n    }\r\n\r\n    /**\r\n     * Returns the biggest value ever\r\n     */\r\n    public get max(): number {\r\n        return this._max;\r\n    }\r\n\r\n    /**\r\n     * Returns the average value since the performance counter is running\r\n     */\r\n    public get average(): number {\r\n        return this._average;\r\n    }\r\n\r\n    /**\r\n     * Returns the average value of the last second the counter was monitored\r\n     */\r\n    public get lastSecAverage(): number {\r\n        return this._lastSecAverage;\r\n    }\r\n\r\n    /**\r\n     * Returns the current value\r\n     */\r\n    public get current(): number {\r\n        return this._current;\r\n    }\r\n\r\n    /**\r\n     * Gets the accumulated total\r\n     */\r\n    public get total(): number {\r\n        return this._totalAccumulated;\r\n    }\r\n\r\n    /**\r\n     * Gets the total value count\r\n     */\r\n    public get count(): number {\r\n        return this._totalValueCount;\r\n    }\r\n\r\n    /**\r\n     * Creates a new counter\r\n     */\r\n    constructor() {\r\n        this._startMonitoringTime = 0;\r\n        this._min = 0;\r\n        this._max = 0;\r\n        this._average = 0;\r\n        this._lastSecAverage = 0;\r\n        this._current = 0;\r\n        this._totalValueCount = 0;\r\n        this._totalAccumulated = 0;\r\n        this._lastSecAccumulated = 0;\r\n        this._lastSecTime = 0;\r\n        this._lastSecValueCount = 0;\r\n    }\r\n\r\n    /**\r\n     * Call this method to start monitoring a new frame.\r\n     * This scenario is typically used when you accumulate monitoring time many times for a single frame, you call this method at the start of the frame, then beginMonitoring to start recording and endMonitoring(false) to accumulated the recorded time to the PerfCounter or addCount() to accumulate a monitored count.\r\n     */\r\n    public fetchNewFrame() {\r\n        this._totalValueCount++;\r\n        this._current = 0;\r\n        this._lastSecValueCount++;\r\n    }\r\n\r\n    /**\r\n     * Call this method to monitor a count of something (e.g. mesh drawn in viewport count)\r\n     * @param newCount the count value to add to the monitored count\r\n     * @param fetchResult true when it's the last time in the frame you add to the counter and you wish to update the statistics properties (min/max/average), false if you only want to update statistics.\r\n     */\r\n    public addCount(newCount: number, fetchResult: boolean) {\r\n        if (!PerfCounter.Enabled) {\r\n            return;\r\n        }\r\n        this._current += newCount;\r\n        if (fetchResult) {\r\n            this._fetchResult();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Start monitoring this performance counter\r\n     */\r\n    public beginMonitoring() {\r\n        if (!PerfCounter.Enabled) {\r\n            return;\r\n        }\r\n        this._startMonitoringTime = PrecisionDate.Now;\r\n    }\r\n\r\n    /**\r\n     * Compute the time lapsed since the previous beginMonitoring() call.\r\n     * @param newFrame true by default to fetch the result and monitor a new frame, if false the time monitored will be added to the current frame counter\r\n     */\r\n    public endMonitoring(newFrame: boolean = true) {\r\n        if (!PerfCounter.Enabled) {\r\n            return;\r\n        }\r\n\r\n        if (newFrame) {\r\n            this.fetchNewFrame();\r\n        }\r\n\r\n        const currentTime = PrecisionDate.Now;\r\n        this._current = currentTime - this._startMonitoringTime;\r\n\r\n        if (newFrame) {\r\n            this._fetchResult();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Call this method to end the monitoring of a frame.\r\n     * This scenario is typically used when you accumulate monitoring time many times for a single frame, you call this method at the end of the frame, after beginMonitoring to start recording and endMonitoring(false) to accumulated the recorded time to the PerfCounter or addCount() to accumulate a monitored count.\r\n     */\r\n    public endFrame() {\r\n        this._fetchResult();\r\n    }\r\n\r\n    /** @internal */\r\n    public _fetchResult() {\r\n        this._totalAccumulated += this._current;\r\n        this._lastSecAccumulated += this._current;\r\n\r\n        // Min/Max update\r\n        this._min = Math.min(this._min, this._current);\r\n        this._max = Math.max(this._max, this._current);\r\n        this._average = this._totalAccumulated / this._totalValueCount;\r\n\r\n        // Reset last sec?\r\n        const now = PrecisionDate.Now;\r\n        if (now - this._lastSecTime > 1000) {\r\n            this._lastSecAverage = this._lastSecAccumulated / this._lastSecValueCount;\r\n            this._lastSecTime = now;\r\n            this._lastSecAccumulated = 0;\r\n            this._lastSecValueCount = 0;\r\n        }\r\n    }\r\n\r\n    private _startMonitoringTime: number;\r\n    private _min: number;\r\n    private _max: number;\r\n    private _average: number;\r\n    private _current: number;\r\n    private _totalValueCount: number;\r\n    private _totalAccumulated: number;\r\n    private _lastSecAverage: number;\r\n    private _lastSecAccumulated: number;\r\n    private _lastSecTime: number;\r\n    private _lastSecValueCount: number;\r\n}\r\n"]}