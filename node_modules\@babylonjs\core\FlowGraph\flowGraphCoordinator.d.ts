import { Observable } from "../Misc/observable.js";
import type { Scene } from "../scene";
import { FlowGraph } from "./flowGraph";
import type { IPathToObjectConverter } from "../ObjectModel/objectModelInterfaces";
import type { IObjectAccessor } from "./typeDefinitions";
/**
 * @experimental
 * Parameters used to create a flow graph engine.
 */
export interface IFlowGraphCoordinatorConfiguration {
    /**
     * The scene that the flow graph engine belongs to.
     */
    scene: Scene;
}
/**
 * @experimental
 * Parameters used to parse a flow graph coordinator.
 */
export interface FlowGraphCoordinatorParseOptions {
    /**
     * A function that will be called to parse the value of a property.
     * @param key the key of the property
     * @param serializationObject the serialization object where the property is located
     * @param scene the scene that the block is being parsed in
     */
    valueParseFunction?: (key: string, serializationObject: any, scene: Scene) => any;
    /**
     * The path converter to use to convert the path to an object accessor.
     */
    pathConverter: IPathToObjectConverter<IObjectAccessor>;
    /**
     * The scene that the flow graph coordinator belongs to.
     */
    scene: Scene;
}
/**
 * This class holds all of the existing flow graphs and is responsible for creating new ones.
 * It also handles starting/stopping multiple graphs and communication between them through an Event Coordinator
 */
export declare class FlowGraphCoordinator {
    /**
     * the configuration of the block
     */
    config: IFlowGraphCoordinatorConfiguration;
    /**
     * @internal
     * A list of all the coordinators per scene. Will be used by the inspector
     */
    static readonly SceneCoordinators: Map<Scene, FlowGraphCoordinator[]>;
    private readonly _flowGraphs;
    private _customEventsMap;
    constructor(
    /**
     * the configuration of the block
     */
    config: IFlowGraphCoordinatorConfiguration);
    /**
     * Creates a new flow graph and adds it to the list of existing flow graphs
     * @returns a new flow graph
     */
    createGraph(): FlowGraph;
    /**
     * Removes a flow graph from the list of existing flow graphs and disposes it
     * @param graph the graph to remove
     */
    removeGraph(graph: FlowGraph): void;
    /**
     * Starts all graphs
     */
    start(): void;
    /**
     * Disposes all graphs
     */
    dispose(): void;
    /**
     * Serializes this coordinator to a JSON object.
     * @param serializationObject the object to serialize to
     * @param valueSerializeFunction the function to use to serialize the value
     */
    serialize(serializationObject: any, valueSerializeFunction?: (key: string, value: any, serializationObject: any) => void): void;
    /**
     * Parses a serialized coordinator.
     * @param serializedObject the object to parse
     * @param options the options to use when parsing
     * @returns the parsed coordinator
     */
    static Parse(serializedObject: any, options: FlowGraphCoordinatorParseOptions): FlowGraphCoordinator;
    /**
     * Gets the list of flow graphs
     */
    get flowGraphs(): FlowGraph[];
    /**
     * Get an observable that will be notified when the event with the given id is fired.
     * @param id the id of the event
     * @returns the observable for the event
     */
    getCustomEventObservable(id: string): Observable<any>;
    /**
     * Notifies the observable for the given event id with the given data.
     * @param id the id of the event
     * @param data the data to send with the event
     */
    notifyCustomEvent(id: string, data: any): void;
}
