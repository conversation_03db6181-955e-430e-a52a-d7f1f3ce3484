{"version": 3, "file": "tools.functions.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/tools.functions.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAAC,KAAa;IACzC,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,GAAG;QACC,KAAK,IAAI,CAAC,CAAC;KACd,QAAQ,KAAK,GAAG,KAAK,EAAE;IAExB,OAAO,KAAK,KAAK,KAAK,CAAC;AAC3B,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa;IACnD,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACvC,CAAC", "sourcesContent": ["/**\r\n * Function indicating if a number is an exponent of 2\r\n * @param value defines the value to test\r\n * @returns true if the value is an exponent of 2\r\n */\r\nexport function IsExponentOfTwo(value: number): boolean {\r\n    let count = 1;\r\n\r\n    do {\r\n        count *= 2;\r\n    } while (count < value);\r\n\r\n    return count === value;\r\n}\r\n\r\n/**\r\n * Interpolates between a and b via alpha\r\n * @param a The lower value (returned when alpha = 0)\r\n * @param b The upper value (returned when alpha = 1)\r\n * @param alpha The interpolation-factor\r\n * @returns The mixed value\r\n */\r\nexport function Mix(a: number, b: number, alpha: number): number {\r\n    return a * (1 - alpha) + b * alpha;\r\n}\r\n"]}