{"version": 3, "file": "packer.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Textures/Packer/packer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAEjD,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAGvD,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAEnD,OAAO,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AACrD,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,2BAA2B,CAAC;AAC3D,OAAO,EAAE,kBAAkB,EAAE,MAAM,SAAS,CAAC;AAC7C,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AAiG5C;;;GAGG;AACH,MAAM,OAAO,aAAa;IA0CtB;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,MAAsB,EAAE,OAA8B,EAAE,KAAY;QAC1F,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB;;WAEG;QACH,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI;YACnC,gBAAgB;YAChB,aAAa;YACb,gBAAgB;YAChB,iBAAiB;YACjB,iBAAiB;YACjB,gBAAgB;YAChB,mBAAmB;YACnB,mBAAmB;YACnB,iBAAiB;SACpB,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,YAAY,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC;QACjE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,aAAa,CAAC,YAAY,CAAC;QAExE,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,aAAa,CAAC,aAAa,EAAE;YACrD,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,IAAI,CAAC;QACxE,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC;QAClE,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QAEpB,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC;QAE1D,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,IAAI,EAAE;YAClC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC;SAC1E;QAED,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC;QACvD,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,MAAM,CAAC;QAEhE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAEnF,iCAAiC;QACjC,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,KAAK,CAAC,EAAE;YAC9B,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;QAED,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,aAAa,CAAC,UAAU,CAAC;QAEhF,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,aAAa,CAAC,WAAW,EAAE;YACxD,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;SACrF;QAED,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACK,aAAa,CAAC,OAAmB;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACrC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAEtC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEzB,MAAM,EAAE,GAAG,IAAI,cAAc,CACzB,IAAI,CAAC,IAAI,GAAG,eAAe,GAAG,OAAO,GAAG,KAAK,EAC7C,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,EACrC,IAAI,CAAC,KAAK,EACV,IAAI,EAAE,eAAe;YACrB,OAAO,CAAC,sBAAsB,EAC9B,MAAM,CAAC,kBAAkB,CAC5B,CAAC;YAEF,MAAM,GAAG,GAAG,EAAE,CAAC,UAAU,EAAE,CAAC;YAC5B,GAAG,CAAC,SAAS,GAAG,eAAe,CAAC;YAChC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;YACvC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,IAAI,CAAC,IAAY,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;SACpC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACnC,MAAM,GAAG,GAAG,QAAQ,GAAG,CAAC,GAAG,OAAO,CAAC;QAEnC,MAAM,IAAI,GAAG,GAAG,EAAE;YACd,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,KAAK,CAAC,CAAC;QAC7G,CAAC,CAAC;QAEF,qBAAqB;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;YACjC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,GAAG,GAAG,CAAC,CAAC,QAAQ,CAAC;YAEvB,uCAAuC;YACvC,oDAAoD;YACpD,wDAAwD;YACxD,2DAA2D;YAE3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnC,MAAM,WAAW,GAAG,IAAI,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACtE,MAAM,GAAG,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC;gBACrC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBAEvC,MAAM,QAAQ,GAAG,GAAG,EAAE;oBAClB,SAAS,EAAE,CAAC;oBACZ,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAC1B,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;oBAE9C,YAAY;oBACZ,MAAM,EAAE,GAAI,IAAI,CAAC,IAAY,CAAC,OAAO,CAAC,CAAC;oBACvC,MAAM,GAAG,GAAG,EAAE,CAAC,UAAU,EAAE,CAAC;oBAC5B,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBACjE,WAAW,CAAC,OAAO,EAAE,CAAC;oBACtB,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACjB,IAAI,SAAS,IAAI,SAAS,EAAE;wBACxB,IAAI,EAAE,CAAC;wBACP,OAAO,EAAE,CAAC;wBACV,OAAO;qBACV;gBACL,CAAC,CAAC;gBAEF,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC;gBACrC,IAAI,CAAC,GAAG,IAAK,GAAW,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;oBACxC,GAAG,CAAC,SAAS,GAAG,eAAe,CAAC;oBAEhC,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;wBACzB,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,eAAyB,CAAC;qBAC1D;oBAED,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;oBAE7B,QAAQ,EAAE,CAAC;iBACd;qBAAM;oBACH,MAAM,UAAU,GAAI,GAAW,CAAC,OAAO,CAAC,CAAC;oBACzC,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;oBAExB,IAAI,UAAU,YAAY,cAAc,EAAE;wBACtC,GAAG,CAAC,GAAG,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;qBACnE;yBAAM;wBACH,GAAG,CAAC,GAAG,GAAG,UAAW,CAAC,GAAG,CAAC;qBAC7B;oBACD,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBAEpC,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;wBACd,GAAG,CAAC,SAAS,GAAG,eAAe,CAAC;wBAChC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;wBAC7B,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBAE1B,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;wBACpC,MAAM,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;wBAEjF,QAAQ,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;4BAC9B,WAAW;4BACX,KAAK,CAAC;gCACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oCACxB,GAAG,CAAC,SAAS,CACT,GAAG,EACH,CAAC,EACD,CAAC,EACD,GAAG,CAAC,KAAK,EACT,GAAG,CAAC,MAAM,EACV,OAAO,GAAG,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,EACnC,OAAO,GAAG,QAAQ,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,EAC7C,QAAQ,EACR,QAAQ,CACX,CAAC;iCACL;gCACD,MAAM;4BACV,aAAa;4BACb,KAAK,CAAC;gCACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;oCAC9B,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,OAAO,GAAG,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;oCAElH,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,GAAG,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;oCAEpG,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;oCAEtF,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;iCACvG;gCAED,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,OAAO,GAAG,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gCAEpJ,MAAM;4BACV,YAAY;4BACZ,KAAK,CAAC;gCACF,GAAG,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;gCAC5E,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gCAC9B,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gCACpD,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,OAAO,GAAG,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gCAEpJ,MAAM;yBACb;wBAED,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;wBAEnC,QAAQ,EAAE,CAAC;oBACf,CAAC,CAAC;iBACL;aACJ;SACJ;IACL,CAAC;IAED;;;OAGG;IACK,cAAc;QAClB,MAAM,UAAU,GAAW,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC;QACnD,MAAM,QAAQ,GAAW,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC;QACrD,MAAM,OAAO,GAAW,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC;QAEhD,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACzB,KAAK,CAAC,CAAC,CAAC;gBACJ,cAAc;gBACd,OAAO,IAAI,OAAO,CAAC,QAAQ,GAAG,UAAU,GAAG,CAAC,GAAG,OAAO,GAAG,UAAU,EAAE,QAAQ,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;aAChG;YACD,KAAK,CAAC,CAAC,CAAC;gBACJ,QAAQ;gBACR,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAChE,MAAM,IAAI,GAAG,QAAQ,GAAG,SAAS,GAAG,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;gBAC5D,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aAClC;YACD,KAAK,CAAC,CAAC,CAAC;gBACJ,QAAQ;gBACR,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;gBACtC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC;gBACzD,OAAO,IAAI,OAAO,CAAC,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,OAAO,GAAG,IAAI,EAAE,QAAQ,GAAG,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC;aACtG;SACJ;QAED,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;;;OAOG;IACK,sBAAsB,CAAC,QAAgB,EAAE,OAAe,EAAE,MAAe,EAAE,OAAgB,EAAE,MAAe;QAChH,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;YACjC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAEzB,MAAM,KAAK,GAAG,IAAI,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC,EAAE,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAEpE,MAAM,OAAO,GAAY,OAAO,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxD,MAAM,WAAW,GAAY,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACrD,MAAM,MAAM,GAAY,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAEjD,MAAM,KAAK,GAAuB,IAAI,kBAAkB,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAE3E,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAExB,mBAAmB;YACnB,IAAI,MAAM,EAAE;gBACR,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACzB,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;aACpC;SACJ;IACL,CAAC;IAED;;;;OAIG;IACK,eAAe,CAAC,KAAa;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QACtC,IAAI,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;QAEzB,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACzB,KAAK,CAAC,CAAC,CAAC;gBACJ,cAAc;gBACd,MAAM,GAAG,CAAC,GAAG,UAAU,CAAC;gBACxB,OAAO,IAAI,OAAO,CAAC,KAAK,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;aACzC;YACD,KAAK,CAAC,CAAC,CAAC;gBACJ,QAAQ;gBACR,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAChE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC;gBACtC,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,SAAS,CAAC;gBAClC,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC;gBACvB,OAAO,IAAI,OAAO,CAAC,KAAK,GAAG,MAAM,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC;aACtD;YACD,KAAK,CAAC,CAAC,CAAC;gBACJ,QAAQ;gBACR,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;gBACtC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC;gBACzD,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;gBACnC,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,MAAM,CAAC;gBAC/B,MAAM,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC;gBAC3C,OAAO,IAAI,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;aAC1D;SACJ;QAED,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACK,aAAa,CAAC,IAAkB,EAAE,OAAe;QACrD,MAAM,KAAK,GAAwB,IAAI,CAAC,MAAc,CAAC,OAAO,CAAC,CAAC;QAChE,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;QAC7E,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,IAAI,IAAK,CAAC,MAAM,EAAE;YACd,OAAO,GAAG,IAAK,CAAC,MAAM,IAAI,CAAC,CAAC;SAC/B;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE;YACjC,KAAK,CAAC,IAAI,CAAE,IAAY,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAG,IAAY,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SACxH;QAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC5E,CAAC;IAED;;;;OAIG;IACK,wBAAwB,CAAC,CAAe,EAAE,QAAiB,KAAK;QACpE,MAAM,GAAG,GAAG,CAAC,CAAC,QAAQ,CAAC;QACvB,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAErC,MAAM,QAAQ,GAAG,CAAC,EAAO,EAAE,EAAE;YACzB,IAAI,EAAE,CAAC,OAAO,EAAE;gBACZ,EAAE,CAAC,OAAO,EAAE,CAAC;aAChB;QACL,CAAC,CAAC;QAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACzB,IAAI,CAAC,KAAK,EAAE;gBACR,IAAI,CAAC,GAAG,EAAE;oBACN,OAAO;iBACV;gBACD,IAAK,GAAW,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;oBAChC,QAAQ,CAAE,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC;oBAC/B,GAAW,CAAC,OAAO,CAAC,GAAI,IAAI,CAAC,IAAY,CAAC,OAAO,CAAC,CAAC;iBACvD;aACJ;iBAAM;gBACH,IAAK,GAAW,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE;oBAChC,QAAQ,CAAE,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC;iBACnC;gBACA,GAAW,CAAC,OAAO,CAAC,GAAI,IAAI,CAAC,IAAY,CAAC,OAAO,CAAC,CAAC;aACvD;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,CAAe,EAAE,OAAe,EAAE,iBAA0B,KAAK;QACnF,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAC/B,IAAI,cAAc,EAAE;YAChB,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SAC1C;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI;gBACA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC1B,sBAAsB;oBACtB,OAAO,EAAE,CAAC;oBACV,OAAO;iBACV;gBACD,IAAI,IAAI,GAAG,CAAC,CAAC;gBACb,MAAM,SAAS,GAAG,CAAC,GAAa,EAAE,EAAE;oBAChC,IAAI,EAAE,CAAC;oBACP,kEAAkE;oBAClE,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;wBAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;4BAC9C,MAAM,KAAK,GAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;4BAC1C,MAAM,CAAC,GAA8B,GAAW,CAAC,KAAK,CAAC,CAAC;4BAExD,IAAI,CAAC,KAAK,IAAI,EAAE;gCACZ,IAAI,CAAE,IAAI,CAAC,IAAY,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;oCACzC,IAAI,CAAC,IAAY,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;iCAClD;gCAED,IAAI,CAAC,UAAU,EAAE,CAAC;6BACrB;yBACJ;wBAED,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;4BAC7B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;yBAC/B;qBACJ;gBACL,CAAC,CAAC;gBAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACzC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC5B,MAAM,QAAQ,GAAuB,IAAI,CAAC,QAAQ,CAAC;oBAEnD,IAAI,CAAC,QAAQ,EAAE;wBACX,IAAI,EAAE,CAAC;wBACP,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;4BAC7B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;yBACtC;wBACD,SAAS;qBACZ;oBAED,QAAQ,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;wBAC3C,SAAS,CAAC,QAAoB,CAAC,CAAC;oBACpC,CAAC,CAAC,CAAC;iBACN;aACJ;YAAC,OAAO,CAAC,EAAE;gBACR,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;aACpB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,OAAO;QACV,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,CAAC,IAAY,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;SACzC;IACL,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,YAAoB,KAAK,EAAE,UAAkB,CAAC;QAC1D,UAAU,CAAC,GAAG,EAAE;YACZ,MAAM,IAAI,GAAG;gBACT,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,EAAE;gBACR,OAAO,EAAE,EAAE;gBACX,MAAM,EAAE,EAAE;aACb,CAAC;YAEF,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI;gBACA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACnC,MAAM,OAAO,GAAW,KAAK,CAAC,CAAC,CAAC,CAAC;oBACjC,MAAM,EAAE,GAAI,IAAI,CAAC,IAAY,CAAC,OAAO,CAAC,CAAC;oBACtC,IAAI,CAAC,IAAY,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,EAAE,OAAO,CAAC,CAAC;iBACjG;gBACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACnC,MAAM,GAAG,GAAW,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC5B,IAAI,CAAC,OAAe,CAAC,GAAG,CAAC,GAAI,IAAI,CAAC,OAAe,CAAC,GAAG,CAAC,CAAC;iBAC3D;gBACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACzC,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACzB,IAAI,CAAC,MAAwB,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;iBACzF;aACJ;YAAC,OAAO,GAAG,EAAE;gBACV,MAAM,CAAC,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC,CAAC;gBAC1C,OAAO;aACV;YAED,MAAM,IAAI,GAAG,+BAA+B,GAAG,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YACjG,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YACvC,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC9B,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC,CAAC;YAC/D,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAC9B,EAAE,CAAC,KAAK,EAAE,CAAC;YACX,EAAE,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,IAAY;QAC9B,IAAI;YACA,MAAM,UAAU,GAAuB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACxD,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;YAC5B,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAEjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,IAAI,CAAC,OAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAI,UAAU,CAAC,OAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;aACjF;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;gBAClD,MAAM,KAAK,GAAuB,IAAI,kBAAkB,CACpD,CAAC,GAAG,CAAC,EACL,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAC3D,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAClE,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC3B;YAED,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACtC,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC9E,IAAI,CAAC,IAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;aACxC;SACJ;QAAC,OAAO,GAAG,EAAE;YACV,MAAM,CAAC,IAAI,CAAC,8BAA8B,GAAG,GAAG,CAAC,CAAC;SACrD;IACL,CAAC;;AAlkBD,+BAA+B;AACR,0BAAY,GAAG,CAAC,CAAC;AACxC,+BAA+B;AACR,2BAAa,GAAG,CAAC,CAAC;AACzC,+BAA+B;AACR,2BAAa,GAAG,CAAC,CAAC;AAEzC,+BAA+B;AACR,wBAAU,GAAG,CAAC,CAAC;AACtC,+BAA+B;AACR,0BAAY,GAAG,CAAC,CAAC;AACxC,+BAA+B;AACR,yBAAW,GAAG,CAAC,CAAC", "sourcesContent": ["import { Engine } from \"../../../Engines/engine\";\r\nimport type { AbstractMesh } from \"../../../Meshes/abstractMesh\";\r\nimport { VertexBuffer } from \"../../../Buffers/buffer\";\r\nimport type { Scene } from \"../../../scene\";\r\nimport type { Material } from \"../../material\";\r\nimport { Texture } from \"../texture\";\r\nimport { DynamicTexture } from \"../dynamicTexture\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport { Vector2 } from \"../../../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../../../Maths/math.color\";\r\nimport { TexturePackerFrame } from \"./frame\";\r\nimport { Logger } from \"../../../Misc/logger\";\r\nimport { Tools } from \"../../../Misc/tools\";\r\n\r\n/**\r\n * Defines the basic options interface of a TexturePacker\r\n */\r\nexport interface ITexturePackerOptions {\r\n    /**\r\n     * Custom targets for the channels of a texture packer.  Default is all the channels of the Standard Material\r\n     */\r\n    map?: string[];\r\n\r\n    /**\r\n     * the UV input targets, as a single value for all meshes. Defaults to VertexBuffer.UVKind\r\n     */\r\n    uvsIn?: string;\r\n\r\n    /**\r\n     * the UV output targets, as a single value for all meshes.  Defaults to VertexBuffer.UVKind\r\n     */\r\n    uvsOut?: string;\r\n\r\n    /**\r\n     * number representing the layout style. Defaults to LAYOUT_STRIP\r\n     */\r\n    layout?: number;\r\n\r\n    /**\r\n     * number of columns if using custom column count layout(2).  This defaults to 4.\r\n     */\r\n    colnum?: number;\r\n\r\n    /**\r\n     * flag to update the input meshes to the new packed texture after compilation. Defaults to true.\r\n     */\r\n    updateInputMeshes?: boolean;\r\n\r\n    /**\r\n     * boolean flag to dispose all the source textures.  Defaults to true.\r\n     */\r\n    disposeSources?: boolean;\r\n\r\n    /**\r\n     * Fills the blank cells in a set to the customFillColor.  Defaults to true.\r\n     */\r\n    fillBlanks?: boolean;\r\n\r\n    /**\r\n     * string value representing the context fill style color.  Defaults to 'black'.\r\n     */\r\n    customFillColor?: string;\r\n\r\n    /**\r\n     * Width and Height Value of each Frame in the TexturePacker Sets\r\n     */\r\n    frameSize?: number;\r\n\r\n    /**\r\n     * Ratio of the value to add padding wise to each cell.  Defaults to 0.0115\r\n     */\r\n    paddingRatio?: number;\r\n\r\n    /**\r\n     * Number that declares the fill method for the padding gutter.\r\n     */\r\n    paddingMode?: number;\r\n\r\n    /**\r\n     * If in SUBUV_COLOR padding mode what color to use.\r\n     */\r\n    paddingColor?: Color3 | Color4;\r\n}\r\n\r\n/**\r\n * Defines the basic interface of a TexturePacker JSON File\r\n */\r\nexport interface ITexturePackerJSON {\r\n    /**\r\n     * The frame ID\r\n     */\r\n    name: string;\r\n\r\n    /**\r\n     * The base64 channel data\r\n     */\r\n    sets: any;\r\n\r\n    /**\r\n     * The options of the Packer\r\n     */\r\n    options: ITexturePackerOptions;\r\n\r\n    /**\r\n     * The frame data of the Packer\r\n     */\r\n    frames: Array<number>;\r\n}\r\n\r\n/**\r\n * This is a support class that generates a series of packed texture sets.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction\r\n */\r\nexport class TexturePacker {\r\n    /** Packer Layout Constant 0 */\r\n    public static readonly LAYOUT_STRIP = 0;\r\n    /** Packer Layout Constant 1 */\r\n    public static readonly LAYOUT_POWER2 = 1;\r\n    /** Packer Layout Constant 2 */\r\n    public static readonly LAYOUT_COLNUM = 2;\r\n\r\n    /** Packer Layout Constant 0 */\r\n    public static readonly SUBUV_WRAP = 0;\r\n    /** Packer Layout Constant 1 */\r\n    public static readonly SUBUV_EXTEND = 1;\r\n    /** Packer Layout Constant 2 */\r\n    public static readonly SUBUV_COLOR = 2;\r\n\r\n    /** The Name of the Texture Package */\r\n    public name: string;\r\n\r\n    /** The scene scope of the TexturePacker */\r\n    public scene: Scene;\r\n\r\n    /** The Meshes to target */\r\n    public meshes: AbstractMesh[];\r\n\r\n    /** Arguments passed with the Constructor */\r\n    public options: ITexturePackerOptions;\r\n\r\n    /** The promise that is started upon initialization */\r\n    public promise: Nullable<Promise<TexturePacker | string>>;\r\n\r\n    /** The Container object for the channel sets that are generated */\r\n    public sets: object;\r\n\r\n    /** The Container array for the frames that are generated */\r\n    public frames: TexturePackerFrame[];\r\n\r\n    /** The expected number of textures the system is parsing. */\r\n    private _expecting: number;\r\n\r\n    /** The padding value from Math.ceil(frameSize * paddingRatio) */\r\n    private _paddingValue: number;\r\n\r\n    /**\r\n     * Initializes a texture package series from an array of meshes or a single mesh.\r\n     * @param name The name of the package\r\n     * @param meshes The target meshes to compose the package from\r\n     * @param options The arguments that texture packer should follow while building.\r\n     * @param scene The scene which the textures are scoped to.\r\n     * @returns TexturePacker\r\n     */\r\n    constructor(name: string, meshes: AbstractMesh[], options: ITexturePackerOptions, scene: Scene) {\r\n        this.name = name;\r\n        this.meshes = meshes;\r\n        this.scene = scene;\r\n\r\n        /**\r\n         * Run through the options and set what ever defaults are needed that where not declared.\r\n         */\r\n        this.options = options;\r\n        this.options.map = this.options.map ?? [\r\n            \"ambientTexture\",\r\n            \"bumpTexture\",\r\n            \"diffuseTexture\",\r\n            \"emissiveTexture\",\r\n            \"lightmapTexture\",\r\n            \"opacityTexture\",\r\n            \"reflectionTexture\",\r\n            \"refractionTexture\",\r\n            \"specularTexture\",\r\n        ];\r\n\r\n        this.options.uvsIn = this.options.uvsIn ?? VertexBuffer.UVKind;\r\n        this.options.uvsOut = this.options.uvsOut ?? VertexBuffer.UVKind;\r\n        this.options.layout = this.options.layout ?? TexturePacker.LAYOUT_STRIP;\r\n\r\n        if (this.options.layout === TexturePacker.LAYOUT_COLNUM) {\r\n            this.options.colnum = this.options.colnum ?? 8;\r\n        }\r\n\r\n        this.options.updateInputMeshes = this.options.updateInputMeshes ?? true;\r\n        this.options.disposeSources = this.options.disposeSources ?? true;\r\n        this._expecting = 0;\r\n\r\n        this.options.fillBlanks = this.options.fillBlanks ?? true;\r\n\r\n        if (this.options.fillBlanks === true) {\r\n            this.options.customFillColor = this.options.customFillColor ?? \"black\";\r\n        }\r\n\r\n        this.options.frameSize = this.options.frameSize ?? 256;\r\n        this.options.paddingRatio = this.options.paddingRatio ?? 0.0115;\r\n\r\n        this._paddingValue = Math.ceil(this.options.frameSize * this.options.paddingRatio);\r\n\r\n        //Make it an even padding Number.\r\n        if (this._paddingValue % 2 !== 0) {\r\n            this._paddingValue++;\r\n        }\r\n\r\n        this.options.paddingMode = this.options.paddingMode ?? TexturePacker.SUBUV_WRAP;\r\n\r\n        if (this.options.paddingMode === TexturePacker.SUBUV_COLOR) {\r\n            this.options.paddingColor = this.options.paddingColor ?? new Color4(0, 0, 0, 1.0);\r\n        }\r\n\r\n        this.sets = {};\r\n        this.frames = [];\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Starts the package process\r\n     * @param resolve The promises resolution function\r\n     */\r\n    private _createFrames(resolve: () => void) {\r\n        const dtSize = this._calculateSize();\r\n        const dtUnits = new Vector2(1, 1).divide(dtSize);\r\n        let doneCount = 0;\r\n        const expecting = this._expecting;\r\n        const meshLength = this.meshes.length;\r\n\r\n        const sKeys = Object.keys(this.sets);\r\n        for (let i = 0; i < sKeys.length; i++) {\r\n            const setName = sKeys[i];\r\n\r\n            const dt = new DynamicTexture(\r\n                this.name + \".TexturePack.\" + setName + \"Set\",\r\n                { width: dtSize.x, height: dtSize.y },\r\n                this.scene,\r\n                true, //Generate Mips\r\n                Texture.TRILINEAR_SAMPLINGMODE,\r\n                Engine.TEXTUREFORMAT_RGBA\r\n            );\r\n\r\n            const dtx = dt.getContext();\r\n            dtx.fillStyle = \"rgba(0,0,0,0)\";\r\n            dtx.fillRect(0, 0, dtSize.x, dtSize.y);\r\n            dt.update(false);\r\n            (this.sets as any)[setName] = dt;\r\n        }\r\n\r\n        const baseSize = this.options.frameSize || 256;\r\n        const padding = this._paddingValue;\r\n        const tcs = baseSize + 2 * padding;\r\n\r\n        const done = () => {\r\n            this._calculateMeshUVFrames(baseSize, padding, dtSize, dtUnits, this.options.updateInputMeshes || false);\r\n        };\r\n\r\n        //Update the Textures\r\n        for (let i = 0; i < meshLength; i++) {\r\n            const m = this.meshes[i];\r\n            const mat = m.material;\r\n\r\n            //Check if the material has the texture\r\n            //Create a temporary canvas the same size as 1 frame\r\n            //Then apply the texture to the center and the 8 offsets\r\n            //Copy the Context and place in the correct frame on the DT\r\n\r\n            for (let j = 0; j < sKeys.length; j++) {\r\n                const tempTexture = new DynamicTexture(\"temp\", tcs, this.scene, true);\r\n                const tcx = tempTexture.getContext();\r\n                const offset = this._getFrameOffset(i);\r\n\r\n                const updateDt = () => {\r\n                    doneCount++;\r\n                    tempTexture.update(false);\r\n                    const iDat = tcx.getImageData(0, 0, tcs, tcs);\r\n\r\n                    //Update Set\r\n                    const dt = (this.sets as any)[setName];\r\n                    const dtx = dt.getContext();\r\n                    dtx.putImageData(iDat, dtSize.x * offset.x, dtSize.y * offset.y);\r\n                    tempTexture.dispose();\r\n                    dt.update(false);\r\n                    if (doneCount == expecting) {\r\n                        done();\r\n                        resolve();\r\n                        return;\r\n                    }\r\n                };\r\n\r\n                const setName = sKeys[j] || \"_blank\";\r\n                if (!mat || (mat as any)[setName] === null) {\r\n                    tcx.fillStyle = \"rgba(0,0,0,0)\";\r\n\r\n                    if (this.options.fillBlanks) {\r\n                        tcx.fillStyle = this.options.customFillColor as string;\r\n                    }\r\n\r\n                    tcx.fillRect(0, 0, tcs, tcs);\r\n\r\n                    updateDt();\r\n                } else {\r\n                    const setTexture = (mat as any)[setName];\r\n                    const img = new Image();\r\n\r\n                    if (setTexture instanceof DynamicTexture) {\r\n                        img.src = setTexture.getContext().canvas.toDataURL(\"image/png\");\r\n                    } else {\r\n                        img.src = setTexture!.url;\r\n                    }\r\n                    Tools.SetCorsBehavior(img.src, img);\r\n\r\n                    img.onload = () => {\r\n                        tcx.fillStyle = \"rgba(0,0,0,0)\";\r\n                        tcx.fillRect(0, 0, tcs, tcs);\r\n                        tempTexture.update(false);\r\n\r\n                        tcx.setTransform(1, 0, 0, -1, 0, 0);\r\n                        const cellOffsets = [0, 0, 1, 0, 1, 1, 0, 1, -1, 1, -1, 0, -1 - 1, 0, -1, 1, -1];\r\n\r\n                        switch (this.options.paddingMode) {\r\n                            //Wrap Mode\r\n                            case 0:\r\n                                for (let i = 0; i < 9; i++) {\r\n                                    tcx.drawImage(\r\n                                        img,\r\n                                        0,\r\n                                        0,\r\n                                        img.width,\r\n                                        img.height,\r\n                                        padding + baseSize * cellOffsets[i],\r\n                                        padding + baseSize * cellOffsets[i + 1] - tcs,\r\n                                        baseSize,\r\n                                        baseSize\r\n                                    );\r\n                                }\r\n                                break;\r\n                            //Extend Mode\r\n                            case 1:\r\n                                for (let i = 0; i < padding; i++) {\r\n                                    tcx.drawImage(img, 0, 0, img.width, img.height, i + baseSize * cellOffsets[0], padding - tcs, baseSize, baseSize);\r\n\r\n                                    tcx.drawImage(img, 0, 0, img.width, img.height, padding * 2 - i, padding - tcs, baseSize, baseSize);\r\n\r\n                                    tcx.drawImage(img, 0, 0, img.width, img.height, padding, i - tcs, baseSize, baseSize);\r\n\r\n                                    tcx.drawImage(img, 0, 0, img.width, img.height, padding, padding * 2 - i - tcs, baseSize, baseSize);\r\n                                }\r\n\r\n                                tcx.drawImage(img, 0, 0, img.width, img.height, padding + baseSize * cellOffsets[0], padding + baseSize * cellOffsets[1] - tcs, baseSize, baseSize);\r\n\r\n                                break;\r\n                            //Color Mode\r\n                            case 2:\r\n                                tcx.fillStyle = (this.options.paddingColor || Color3.Black()).toHexString();\r\n                                tcx.fillRect(0, 0, tcs, -tcs);\r\n                                tcx.clearRect(padding, padding, baseSize, baseSize);\r\n                                tcx.drawImage(img, 0, 0, img.width, img.height, padding + baseSize * cellOffsets[0], padding + baseSize * cellOffsets[1] - tcs, baseSize, baseSize);\r\n\r\n                                break;\r\n                        }\r\n\r\n                        tcx.setTransform(1, 0, 0, 1, 0, 0);\r\n\r\n                        updateDt();\r\n                    };\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Calculates the Size of the Channel Sets\r\n     * @returns Vector2\r\n     */\r\n    private _calculateSize(): Vector2 {\r\n        const meshLength: number = this.meshes.length || 0;\r\n        const baseSize: number = this.options.frameSize || 0;\r\n        const padding: number = this._paddingValue || 0;\r\n\r\n        switch (this.options.layout) {\r\n            case 0: {\r\n                //STRIP_LAYOUT\r\n                return new Vector2(baseSize * meshLength + 2 * padding * meshLength, baseSize + 2 * padding);\r\n            }\r\n            case 1: {\r\n                //POWER2\r\n                const sqrtCount = Math.max(2, Math.ceil(Math.sqrt(meshLength)));\r\n                const size = baseSize * sqrtCount + 2 * padding * sqrtCount;\r\n                return new Vector2(size, size);\r\n            }\r\n            case 2: {\r\n                //COLNUM\r\n                const cols = this.options.colnum || 1;\r\n                const rowCnt = Math.max(1, Math.ceil(meshLength / cols));\r\n                return new Vector2(baseSize * cols + 2 * padding * cols, baseSize * rowCnt + 2 * padding * rowCnt);\r\n            }\r\n        }\r\n\r\n        return Vector2.Zero();\r\n    }\r\n\r\n    /**\r\n     * Calculates the UV data for the frames.\r\n     * @param baseSize the base frameSize\r\n     * @param padding the base frame padding\r\n     * @param dtSize size of the Dynamic Texture for that channel\r\n     * @param dtUnits is 1/dtSize\r\n     * @param update flag to update the input meshes\r\n     */\r\n    private _calculateMeshUVFrames(baseSize: number, padding: number, dtSize: Vector2, dtUnits: Vector2, update: boolean) {\r\n        const meshLength = this.meshes.length;\r\n\r\n        for (let i = 0; i < meshLength; i++) {\r\n            const m = this.meshes[i];\r\n\r\n            const scale = new Vector2(baseSize / dtSize.x, baseSize / dtSize.y);\r\n\r\n            const pOffset: Vector2 = dtUnits.clone().scale(padding);\r\n            const frameOffset: Vector2 = this._getFrameOffset(i);\r\n            const offset: Vector2 = frameOffset.add(pOffset);\r\n\r\n            const frame: TexturePackerFrame = new TexturePackerFrame(i, scale, offset);\r\n\r\n            this.frames.push(frame);\r\n\r\n            //Update Output UVs\r\n            if (update) {\r\n                this._updateMeshUV(m, i);\r\n                this._updateTextureReferences(m);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Calculates the frames Offset.\r\n     * @param index of the frame\r\n     * @returns Vector2\r\n     */\r\n    private _getFrameOffset(index: number): Vector2 {\r\n        const meshLength = this.meshes.length;\r\n        let uvStep, yStep, xStep;\r\n\r\n        switch (this.options.layout) {\r\n            case 0: {\r\n                //STRIP_LAYOUT\r\n                uvStep = 1 / meshLength;\r\n                return new Vector2(index * uvStep, 0);\r\n            }\r\n            case 1: {\r\n                //POWER2\r\n                const sqrtCount = Math.max(2, Math.ceil(Math.sqrt(meshLength)));\r\n                yStep = Math.floor(index / sqrtCount);\r\n                xStep = index - yStep * sqrtCount;\r\n                uvStep = 1 / sqrtCount;\r\n                return new Vector2(xStep * uvStep, yStep * uvStep);\r\n            }\r\n            case 2: {\r\n                //COLNUM\r\n                const cols = this.options.colnum || 1;\r\n                const rowCnt = Math.max(1, Math.ceil(meshLength / cols));\r\n                xStep = Math.floor(index / rowCnt);\r\n                yStep = index - xStep * rowCnt;\r\n                uvStep = new Vector2(1 / cols, 1 / rowCnt);\r\n                return new Vector2(xStep * uvStep.x, yStep * uvStep.y);\r\n            }\r\n        }\r\n\r\n        return Vector2.Zero();\r\n    }\r\n\r\n    /**\r\n     * Updates a Mesh to the frame data\r\n     * @param mesh that is the target\r\n     * @param frameID or the frame index\r\n     */\r\n    private _updateMeshUV(mesh: AbstractMesh, frameID: number): void {\r\n        const frame: TexturePackerFrame = (this.frames as any)[frameID];\r\n        const uvIn = mesh.getVerticesData(this.options.uvsIn || VertexBuffer.UVKind);\r\n        const uvOut = [];\r\n        let toCount = 0;\r\n\r\n        if (uvIn!.length) {\r\n            toCount = uvIn!.length || 0;\r\n        }\r\n\r\n        for (let i = 0; i < toCount; i += 2) {\r\n            uvOut.push((uvIn as any)[i] * frame.scale.x + frame.offset.x, (uvIn as any)[i + 1] * frame.scale.y + frame.offset.y);\r\n        }\r\n\r\n        mesh.setVerticesData(this.options.uvsOut || VertexBuffer.UVKind, uvOut);\r\n    }\r\n\r\n    /**\r\n     * Updates a Meshes materials to use the texture packer channels\r\n     * @param m is the mesh to target\r\n     * @param force all channels on the packer to be set.\r\n     */\r\n    private _updateTextureReferences(m: AbstractMesh, force: boolean = false): void {\r\n        const mat = m.material;\r\n        const sKeys = Object.keys(this.sets);\r\n\r\n        const _dispose = (_t: any) => {\r\n            if (_t.dispose) {\r\n                _t.dispose();\r\n            }\r\n        };\r\n\r\n        for (let i = 0; i < sKeys.length; i++) {\r\n            const setName = sKeys[i];\r\n            if (!force) {\r\n                if (!mat) {\r\n                    return;\r\n                }\r\n                if ((mat as any)[setName] !== null) {\r\n                    _dispose((mat as any)[setName]);\r\n                    (mat as any)[setName] = (this.sets as any)[setName];\r\n                }\r\n            } else {\r\n                if ((mat as any)[setName] !== null) {\r\n                    _dispose((mat as any)[setName]);\r\n                }\r\n                (mat as any)[setName] = (this.sets as any)[setName];\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Public method to set a Mesh to a frame\r\n     * @param m that is the target\r\n     * @param frameID or the frame index\r\n     * @param updateMaterial trigger for if the Meshes attached Material be updated?\r\n     */\r\n    public setMeshToFrame(m: AbstractMesh, frameID: number, updateMaterial: boolean = false): void {\r\n        this._updateMeshUV(m, frameID);\r\n        if (updateMaterial) {\r\n            this._updateTextureReferences(m, true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Starts the async promise to compile the texture packer.\r\n     * @returns Promise<void>\r\n     */\r\n    public processAsync(): Promise<void> {\r\n        return new Promise((resolve, reject) => {\r\n            try {\r\n                if (this.meshes.length === 0) {\r\n                    //Must be a JSON load!\r\n                    resolve();\r\n                    return;\r\n                }\r\n                let done = 0;\r\n                const doneCheck = (mat: Material) => {\r\n                    done++;\r\n                    //Check Status of all Textures on all meshes, till they are ready.\r\n                    if (this.options.map) {\r\n                        for (let j = 0; j < this.options.map.length; j++) {\r\n                            const index: string = this.options.map[j];\r\n                            const t: Texture | DynamicTexture = (mat as any)[index];\r\n\r\n                            if (t !== null) {\r\n                                if (!(this.sets as any)[this.options.map[j]]) {\r\n                                    (this.sets as any)[this.options.map[j]] = true;\r\n                                }\r\n\r\n                                this._expecting++;\r\n                            }\r\n                        }\r\n\r\n                        if (done === this.meshes.length) {\r\n                            this._createFrames(resolve);\r\n                        }\r\n                    }\r\n                };\r\n\r\n                for (let i = 0; i < this.meshes.length; i++) {\r\n                    const mesh = this.meshes[i];\r\n                    const material: Nullable<Material> = mesh.material;\r\n\r\n                    if (!material) {\r\n                        done++;\r\n                        if (done === this.meshes.length) {\r\n                            return this._createFrames(resolve);\r\n                        }\r\n                        continue;\r\n                    }\r\n\r\n                    material.forceCompilationAsync(mesh).then(() => {\r\n                        doneCheck(material as Material);\r\n                    });\r\n                }\r\n            } catch (e) {\r\n                return reject(e);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Disposes all textures associated with this packer\r\n     */\r\n    public dispose(): void {\r\n        const sKeys = Object.keys(this.sets);\r\n        for (let i = 0; i < sKeys.length; i++) {\r\n            const channel = sKeys[i];\r\n            (this.sets as any)[channel].dispose();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Starts the download process for all the channels converting them to base64 data and embedding it all in a JSON file.\r\n     * @param imageType is the image type to use.\r\n     * @param quality of the image if downloading as jpeg, Ranges from >0 to 1.\r\n     */\r\n    public download(imageType: string = \"png\", quality: number = 1): void {\r\n        setTimeout(() => {\r\n            const pack = {\r\n                name: this.name,\r\n                sets: {},\r\n                options: {},\r\n                frames: [],\r\n            };\r\n\r\n            const sKeys = Object.keys(this.sets);\r\n            const oKeys = Object.keys(this.options);\r\n            try {\r\n                for (let i = 0; i < sKeys.length; i++) {\r\n                    const channel: string = sKeys[i];\r\n                    const dt = (this.sets as any)[channel];\r\n                    (pack.sets as any)[channel] = dt.getContext().canvas.toDataURL(\"image/\" + imageType, quality);\r\n                }\r\n                for (let i = 0; i < oKeys.length; i++) {\r\n                    const opt: string = oKeys[i];\r\n                    (pack.options as any)[opt] = (this.options as any)[opt];\r\n                }\r\n                for (let i = 0; i < this.frames.length; i++) {\r\n                    const _f = this.frames[i];\r\n                    (pack.frames as Array<number>).push(_f.scale.x, _f.scale.y, _f.offset.x, _f.offset.y);\r\n                }\r\n            } catch (err) {\r\n                Logger.Warn(\"Unable to download: \" + err);\r\n                return;\r\n            }\r\n\r\n            const data = \"data:text/json;charset=utf-8,\" + encodeURIComponent(JSON.stringify(pack, null, 4));\r\n            const _a = document.createElement(\"a\");\r\n            _a.setAttribute(\"href\", data);\r\n            _a.setAttribute(\"download\", this.name + \"_texurePackage.json\");\r\n            document.body.appendChild(_a);\r\n            _a.click();\r\n            _a.remove();\r\n        }, 0);\r\n    }\r\n\r\n    /**\r\n     * Public method to load a texturePacker JSON file.\r\n     * @param data of the JSON file in string format.\r\n     */\r\n    public updateFromJSON(data: string): void {\r\n        try {\r\n            const parsedData: ITexturePackerJSON = JSON.parse(data);\r\n            this.name = parsedData.name;\r\n            const _options = Object.keys(parsedData.options);\r\n\r\n            for (let i = 0; i < _options.length; i++) {\r\n                (this.options as any)[_options[i]] = (parsedData.options as any)[_options[i]];\r\n            }\r\n\r\n            for (let i = 0; i < parsedData.frames.length; i += 4) {\r\n                const frame: TexturePackerFrame = new TexturePackerFrame(\r\n                    i / 4,\r\n                    new Vector2(parsedData.frames[i], parsedData.frames[i + 1]),\r\n                    new Vector2(parsedData.frames[i + 2], parsedData.frames[i + 3])\r\n                );\r\n                this.frames.push(frame);\r\n            }\r\n\r\n            const channels = Object.keys(parsedData.sets);\r\n\r\n            for (let i = 0; i < channels.length; i++) {\r\n                const _t = new Texture(parsedData.sets[channels[i]], this.scene, false, false);\r\n                (this.sets as any)[channels[i]] = _t;\r\n            }\r\n        } catch (err) {\r\n            Logger.Warn(\"Unable to update from JSON: \" + err);\r\n        }\r\n    }\r\n}\r\n"]}