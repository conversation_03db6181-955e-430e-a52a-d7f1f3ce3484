{"version": 3, "file": "replaceColorBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/replaceColorBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAGvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,iBAAiB;IACpD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAE9C,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAC9E,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAClF,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,YAAY,CAAC,CAAC;QAElF,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAC/F,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,MAAM,CAAC,CAAC;QAChG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAC/F,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,MAAM,CAAC,CAAC;QAChG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAC/F,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,MAAM,CAAC,CAAC;IACpG,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEhC,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;QACtE,KAAK,CAAC,iBAAiB,IAAI,cAAc,IAAI,CAAC,KAAK,CAAC,sBAAsB,MAAM,IAAI,CAAC,SAAS,CAAC,sBAAsB,OAAO,IAAI,CAAC,QAAQ,CAAC,sBAAsB,OAAO,CAAC;QACxK,KAAK,CAAC,iBAAiB,IAAI,GAAG,MAAM,CAAC,sBAAsB,MAAM,IAAI,CAAC,WAAW,CAAC,sBAAsB,KAAK,CAAC;QAC9G,KAAK,CAAC,iBAAiB,IAAI,YAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,GAAG,MAAM,CAAC,sBAAsB,MAAM,IAAI,CAAC,KAAK,CAAC,sBAAsB,KAAK,CAAC;QACxG,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC;QACjC,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,2BAA2B,EAAE,iBAAiB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\n/**\r\n * Block used to replace a color by another one\r\n */\r\nexport class ReplaceColorBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new ReplaceColorBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerInput(\"value\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerInput(\"reference\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerInput(\"distance\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerInput(\"replacement\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.BasedOnInput);\r\n\r\n        this._outputs[0]._typeConnectionSource = this._inputs[0];\r\n        this._linkConnectionTypes(0, 1);\r\n        this._linkConnectionTypes(0, 3);\r\n\r\n        this._inputs[0].excludedConnectionPointTypes.push(NodeMaterialBlockConnectionPointTypes.Float);\r\n        this._inputs[0].excludedConnectionPointTypes.push(NodeMaterialBlockConnectionPointTypes.Matrix);\r\n        this._inputs[1].excludedConnectionPointTypes.push(NodeMaterialBlockConnectionPointTypes.Float);\r\n        this._inputs[1].excludedConnectionPointTypes.push(NodeMaterialBlockConnectionPointTypes.Matrix);\r\n        this._inputs[3].excludedConnectionPointTypes.push(NodeMaterialBlockConnectionPointTypes.Float);\r\n        this._inputs[3].excludedConnectionPointTypes.push(NodeMaterialBlockConnectionPointTypes.Matrix);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"ReplaceColorBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the value input component\r\n     */\r\n    public get value(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the reference input component\r\n     */\r\n    public get reference(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the distance input component\r\n     */\r\n    public get distance(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the replacement input component\r\n     */\r\n    public get replacement(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const output = this._outputs[0];\r\n\r\n        state.compilationString += this._declareOutput(output, state) + `;\\n`;\r\n        state.compilationString += `if (length(${this.value.associatedVariableName} - ${this.reference.associatedVariableName}) < ${this.distance.associatedVariableName}) {\\n`;\r\n        state.compilationString += `${output.associatedVariableName} = ${this.replacement.associatedVariableName};\\n`;\r\n        state.compilationString += `} else {\\n`;\r\n        state.compilationString += `${output.associatedVariableName} = ${this.value.associatedVariableName};\\n`;\r\n        state.compilationString += `}\\n`;\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ReplaceColorBlock\", ReplaceColorBlock);\r\n"]}