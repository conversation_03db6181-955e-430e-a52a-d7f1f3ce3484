{"version": 3, "file": "webXRRenderTargetTextureProvider.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/XR/webXRRenderTargetTextureProvider.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,oBAAoB,EAAE,MAAM,uCAAuC,CAAC;AAE7E,OAAO,EAAE,eAAe,EAAE,qBAAqB,EAAE,MAAM,uCAAuC,CAAC;AAC/F,OAAO,EAAE,qBAAqB,EAAE,MAAM,6CAA6C,CAAC;AACpF,OAAO,EAAE,mBAAmB,EAAE,MAAM,2CAA2C,CAAC;AAgChF;;;GAGG;AACH,MAAM,OAAgB,qCAAqC;IAUvD,YACqB,MAAa,EACd,YAA+B;QAD9B,WAAM,GAAN,MAAM,CAAO;QACd,iBAAY,GAAZ,YAAY,CAAmB;QAPzC,0BAAqB,GAAG,IAAI,KAAK,EAAuB,CAAC;QAS/D,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;IACtC,CAAC;IAEO,sBAAsB,CAAC,WAA8C,EAAE,OAAqB;QAChG,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC/F,eAAe,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;QAC1C,eAAe,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QAC5C,eAAe,CAAC,gBAAgB,GAAG,IAAI,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACvF,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC;QAC/B,OAAO,eAAe,CAAC;IAC3B,CAAC;IAES,0BAA0B,CAChC,KAAa,EACb,MAAc,EACd,WAAuC,EACvC,YAA2B,EAC3B,mBAAkC,EAClC,SAAmB;QAEnB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;SACzC;QAED,MAAM,WAAW,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;QAEtC,yDAAyD;QACzD,MAAM,mBAAmB,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,mBAAmB,CAAC,wBAAwB,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1K,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,YAAwC,CAAC;QACzF,mBAAmB,CAAC,QAAQ,GAAG,mBAAmB,CAAC,OAAO,CAAC;QAC3D,4FAA4F;QAC5F,IAAI,WAAW,IAAI,CAAC,YAAY,EAAE;YAC9B,mBAAmB,CAAC,YAAY,GAAG,WAAW,CAAC;SAClD;QAED,0BAA0B;QAC1B,IAAI,YAAY,EAAE;YACd,IAAI,SAAS,EAAE;gBACX,mBAAmB,CAAC,kBAAkB,GAAG,YAAY,CAAC;aACzD;iBAAM;gBACH,MAAM,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;gBAC/E,mBAAmB,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;gBACnD,mBAAmB,CAAC,QAAQ,GAAG,eAAe,CAAC;aAClD;SACJ;QAED,IAAI,mBAAmB,EAAE;YACrB,IAAI,SAAS,EAAE;gBACX,mBAAmB,CAAC,yBAAyB,GAAG,mBAAmB,CAAC;aACvE;iBAAM;gBACH,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;aAC5G;SACJ;QAED,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;QAEvC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAErD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAES,2BAA2B,CAAC,mBAAwC;QAC1E,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9F,mBAAmB,CAAC,OAAO,EAAE,CAAC;IAClC,CAAC;IAEM,wBAAwB;QAC3B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1C,CAAC;CACJ", "sourcesContent": ["import type { Engine } from \"../Engines/engine\";\r\nimport { WebGLHardwareTexture } from \"../Engines/WebGL/webGLHardwareTexture\";\r\nimport type { WebGLRenderTargetWrapper } from \"../Engines/WebGL/webGLRenderTargetWrapper\";\r\nimport { InternalTexture, InternalTextureSource } from \"../Materials/Textures/internalTexture\";\r\nimport { MultiviewRenderTarget } from \"../Materials/Textures/MultiviewRenderTarget\";\r\nimport { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport type { Viewport } from \"../Maths/math.viewport\";\r\nimport type { IDisposable, Scene } from \"../scene\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { WebXRLayerWrapper } from \"./webXRLayerWrapper\";\r\n\r\n/**\r\n * An interface for objects that provide render target textures for XR rendering.\r\n */\r\nexport interface IWebXRRenderTargetTextureProvider extends IDisposable {\r\n    /**\r\n     * Attempts to set the framebuffer-size-normalized viewport to be rendered this frame for this view.\r\n     * In the event of a failure, the supplied viewport is not updated.\r\n     * @param viewport the viewport to which the view will be rendered\r\n     * @param view the view for which to set the viewport\r\n     * @returns whether the operation was successful\r\n     */\r\n    trySetViewportForView(viewport: Viewport, view: XRView): boolean;\r\n    /**\r\n     * Gets the correct render target texture to be rendered this frame for this eye\r\n     * @param eye the eye for which to get the render target\r\n     * @returns the render target for the specified eye or null if not available\r\n     */\r\n    getRenderTargetTextureForEye(eye: XREye): Nullable<RenderTargetTexture>;\r\n    /**\r\n     * Gets the correct render target texture to be rendered this frame for this view\r\n     * @param view the view for which to get the render target\r\n     * @returns the render target for the specified view or null if not available\r\n     */\r\n    getRenderTargetTextureForView(view: XRView): Nullable<RenderTargetTexture>;\r\n}\r\n\r\n/**\r\n * Provides render target textures and other important rendering information for a given XRLayer.\r\n * @internal\r\n */\r\nexport abstract class WebXRLayerRenderTargetTextureProvider implements IWebXRRenderTargetTextureProvider {\r\n    public abstract trySetViewportForView(viewport: Viewport, view: XRView): boolean;\r\n    public abstract getRenderTargetTextureForEye(eye: XREye): Nullable<RenderTargetTexture>;\r\n    public abstract getRenderTargetTextureForView(view: XRView): Nullable<RenderTargetTexture>;\r\n\r\n    protected _renderTargetTextures = new Array<RenderTargetTexture>();\r\n    protected _framebufferDimensions: Nullable<{ framebufferWidth: number; framebufferHeight: number }>;\r\n\r\n    private _engine: Engine;\r\n\r\n    constructor(\r\n        private readonly _scene: Scene,\r\n        public readonly layerWrapper: WebXRLayerWrapper\r\n    ) {\r\n        this._engine = _scene.getEngine();\r\n    }\r\n\r\n    private _createInternalTexture(textureSize: { width: number; height: number }, texture: WebGLTexture): InternalTexture {\r\n        const internalTexture = new InternalTexture(this._engine, InternalTextureSource.Unknown, true);\r\n        internalTexture.width = textureSize.width;\r\n        internalTexture.height = textureSize.height;\r\n        internalTexture._hardwareTexture = new WebGLHardwareTexture(texture, this._engine._gl);\r\n        internalTexture.isReady = true;\r\n        return internalTexture;\r\n    }\r\n\r\n    protected _createRenderTargetTexture(\r\n        width: number,\r\n        height: number,\r\n        framebuffer: Nullable<WebGLFramebuffer>,\r\n        colorTexture?: WebGLTexture,\r\n        depthStencilTexture?: WebGLTexture,\r\n        multiview?: boolean\r\n    ): RenderTargetTexture {\r\n        if (!this._engine) {\r\n            throw new Error(\"Engine is disposed\");\r\n        }\r\n\r\n        const textureSize = { width, height };\r\n\r\n        // Create render target texture from the internal texture\r\n        const renderTargetTexture = multiview ? new MultiviewRenderTarget(this._scene, textureSize) : new RenderTargetTexture(\"XR renderTargetTexture\", textureSize, this._scene);\r\n        const renderTargetWrapper = renderTargetTexture.renderTarget as WebGLRenderTargetWrapper;\r\n        renderTargetWrapper._samples = renderTargetTexture.samples;\r\n        // Set the framebuffer, make sure it works in all scenarios - emulator, no layers and layers\r\n        if (framebuffer || !colorTexture) {\r\n            renderTargetWrapper._framebuffer = framebuffer;\r\n        }\r\n\r\n        // Create internal texture\r\n        if (colorTexture) {\r\n            if (multiview) {\r\n                renderTargetWrapper._colorTextureArray = colorTexture;\r\n            } else {\r\n                const internalTexture = this._createInternalTexture(textureSize, colorTexture);\r\n                renderTargetWrapper.setTexture(internalTexture, 0);\r\n                renderTargetTexture._texture = internalTexture;\r\n            }\r\n        }\r\n\r\n        if (depthStencilTexture) {\r\n            if (multiview) {\r\n                renderTargetWrapper._depthStencilTextureArray = depthStencilTexture;\r\n            } else {\r\n                renderTargetWrapper._depthStencilTexture = this._createInternalTexture(textureSize, depthStencilTexture);\r\n            }\r\n        }\r\n\r\n        renderTargetTexture.disableRescaling();\r\n\r\n        this._renderTargetTextures.push(renderTargetTexture);\r\n\r\n        return renderTargetTexture;\r\n    }\r\n\r\n    protected _destroyRenderTargetTexture(renderTargetTexture: RenderTargetTexture) {\r\n        this._renderTargetTextures.splice(this._renderTargetTextures.indexOf(renderTargetTexture), 1);\r\n        renderTargetTexture.dispose();\r\n    }\r\n\r\n    public getFramebufferDimensions(): Nullable<{ framebufferWidth: number; framebufferHeight: number }> {\r\n        return this._framebufferDimensions;\r\n    }\r\n\r\n    public dispose() {\r\n        this._renderTargetTextures.forEach((rtt) => rtt.dispose());\r\n        this._renderTargetTextures.length = 0;\r\n    }\r\n}\r\n"]}