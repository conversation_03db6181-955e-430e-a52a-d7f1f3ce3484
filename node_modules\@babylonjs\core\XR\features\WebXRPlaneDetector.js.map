{"version": 3, "file": "WebXRPlaneDetector.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRPlaneDetector.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAGjF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAiD9D,IAAI,eAAe,GAAG,CAAC,CAAC;AAExB;;;GAGG;AACH,MAAM,OAAO,kBAAmB,SAAQ,oBAAoB;IA8BxD;;;;OAIG;IACH,YACI,iBAAsC,EAC9B,WAAuC,EAAE;QAEjD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFjB,aAAQ,GAAR,QAAQ,CAAiC;QApC7C,oBAAe,GAAuB,EAAE,CAAC;QACzC,aAAQ,GAAY,KAAK,CAAC;QAC1B,uBAAkB,GAAe,IAAI,GAAG,EAAE,CAAC;QAanD;;WAEG;QACI,2BAAsB,GAA4B,IAAI,UAAU,EAAE,CAAC;QAC1E;;WAEG;QACI,6BAAwB,GAA4B,IAAI,UAAU,EAAE,CAAC;QAC5E;;;WAGG;QACI,6BAAwB,GAA4B,IAAI,UAAU,EAAE,CAAC;QAYxE,IAAI,CAAC,mBAAmB,GAAG,iBAAiB,CAAC;QAC7C,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE;YAChC,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;aAAM;YACH,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,EAAE;gBAChD,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,+BAA+B,EAAE;YAChD,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;gBAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC;gBAC5C,IAAI,QAAQ,EAAE;oBACV,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;iBAC3D;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,YAAY;QACf,OAAO,OAAO,OAAO,KAAK,WAAW,CAAC;IAC1C,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,mBAAmB;QAC5B,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,mBAAmB,EAAE;YACpD,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;SAC/D;QACD,OAAO,OAAO,CAAC,MAAM,CAAC,sDAAsD,CAAC,CAAC;IAClF,CAAC;IAES,UAAU,CAAC,KAAc;QAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;YAC5C,OAAO;SACV;QAED,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,gBAAgB,EAAE,cAAc,CAAC;QACtF,IAAI,cAAc,EAAE;YAChB,iEAAiE;YACjE,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;gBACvE,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;gBAC7C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;oBACpC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;oBAC3C,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;iBACxD;aACJ;YAED,yBAAyB;YACzB,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC/B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBACvC,MAAM,QAAQ,GAAyB;wBACnC,EAAE,EAAE,eAAe,EAAE;wBACrB,OAAO,EAAE,OAAO;wBAChB,iBAAiB,EAAE,EAAE;qBACxB,CAAC;oBACF,MAAM,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;oBACrE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACjC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;iBACtD;qBAAM;oBACH,WAAW;oBACX,IAAI,OAAO,CAAC,eAAe,KAAK,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE;wBACrE,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;wBACnD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;wBAC1C,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;wBACpD,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;qBACxD;iBACJ;YACL,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC;SAC5C;IACL,CAAC;IAEO,KAAK;QACT,MAAM,YAAY,GAAG,GAAG,EAAE;YACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;gBAC7B,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;aACnC;QACL,CAAC,CAAC;QAEF,kCAAkC;QAClC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,mCAAmC,EAAE;YACvJ,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,mCAAmC,CAAC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;SAC9G;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,wBAAwB,EAAE;YAC1D,YAAY,EAAE,CAAC;YACf,OAAO;SACV;QACD,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,wBAAwB,CAAC,EAAE,mBAAmB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QACpG,YAAY,EAAE,CAAC;IACnB,CAAC;IAEO,uBAAuB,CAAC,OAAgB,EAAE,KAA2B,EAAE,OAAgB;QAC3F,KAAK,CAAC,iBAAiB,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACtD,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrF,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QACH,SAAS;QACT,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QACxF,IAAI,IAAI,EAAE;YACN,MAAM,GAAG,GAAG,KAAK,CAAC,oBAAoB,IAAI,IAAI,MAAM,EAAE,CAAC;YACvD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;YACrD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBACpD,GAAG,CAAC,4BAA4B,EAAE,CAAC;aACtC;YACD,KAAK,CAAC,oBAAoB,GAAG,GAAG,CAAC;YACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE;gBAC/B,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,CAAC;aAC1E;SACJ;QAED,OAAoB,KAAK,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACK,sBAAsB,CAAC,OAAgB;QAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAClD,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,OAAO,EAAE;gBAC7C,OAAO,CAAC,CAAC;aACZ;SACJ;QACD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;;AArMD;;GAEG;AACoB,uBAAI,GAAG,gBAAgB,CAAC,eAAe,AAAnC,CAAoC;AAC/D;;;;GAIG;AACoB,0BAAO,GAAG,CAAC,AAAJ,CAAK;AA+LvC,qBAAqB;AACrB,oBAAoB,CAAC,eAAe,CAChC,kBAAkB,CAAC,IAAI,EACvB,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,kBAAkB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AACnE,CAAC,EACD,kBAAkB,CAAC,OAAO,CAC7B,CAAC", "sourcesContent": ["import { WebXRFeaturesManager, WebXRFeatureName } from \"../webXRFeaturesManager\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { Vector3, Matrix } from \"../../Maths/math.vector\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\n\r\ndeclare const XRPlane: XRPlane;\r\n\r\n/**\r\n * Options used in the plane detector module\r\n */\r\nexport interface IWebXRPlaneDetectorOptions {\r\n    /**\r\n     * The node to use to transform the local results to world coordinates\r\n     */\r\n    worldParentNode?: TransformNode;\r\n    /**\r\n     * If set to true a reference of the created planes will be kept until the next session starts\r\n     * If not defined, planes will be removed from the array when the feature is detached or the session ended.\r\n     */\r\n    doNotRemovePlanesOnSessionEnded?: boolean;\r\n    /**\r\n     * Preferred detector configuration, not all preferred options will be supported by all platforms.\r\n     */\r\n    preferredDetectorOptions?: XRGeometryDetectorOptions;\r\n}\r\n\r\n/**\r\n * A babylon interface for a WebXR plane.\r\n * A Plane is actually a polygon, built from N points in space\r\n *\r\n * Supported in chrome 79, not supported in canary 81 ATM\r\n */\r\nexport interface IWebXRPlane {\r\n    /**\r\n     * a babylon-assigned ID for this polygon\r\n     */\r\n    id: number;\r\n    /**\r\n     * an array of vector3 points in babylon space. right/left hand system is taken into account.\r\n     */\r\n    polygonDefinition: Array<Vector3>;\r\n    /**\r\n     * A transformation matrix to apply on the mesh that will be built using the polygonDefinition\r\n     * Local vs. World are decided if worldParentNode was provided or not in the options when constructing the module\r\n     */\r\n    transformationMatrix: Matrix;\r\n    /**\r\n     * the native xr-plane object\r\n     */\r\n    xrPlane: XRPlane;\r\n}\r\n\r\nlet planeIdProvider = 0;\r\n\r\n/**\r\n * The plane detector is used to detect planes in the real world when in AR\r\n * For more information see https://github.com/immersive-web/real-world-geometry/\r\n */\r\nexport class WebXRPlaneDetector extends WebXRAbstractFeature {\r\n    private _detectedPlanes: Array<IWebXRPlane> = [];\r\n    private _enabled: boolean = false;\r\n    private _lastFrameDetected: XRPlaneSet = new Set();\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.PLANE_DETECTION;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * Observers registered here will be executed when a new plane was added to the session\r\n     */\r\n    public onPlaneAddedObservable: Observable<IWebXRPlane> = new Observable();\r\n    /**\r\n     * Observers registered here will be executed when a plane is no longer detected in the session\r\n     */\r\n    public onPlaneRemovedObservable: Observable<IWebXRPlane> = new Observable();\r\n    /**\r\n     * Observers registered here will be executed when an existing plane updates (for example - expanded)\r\n     * This can execute N times every frame\r\n     */\r\n    public onPlaneUpdatedObservable: Observable<IWebXRPlane> = new Observable();\r\n\r\n    /**\r\n     * construct a new Plane Detector\r\n     * @param _xrSessionManager an instance of xr Session manager\r\n     * @param _options configuration to use when constructing this feature\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        private _options: IWebXRPlaneDetectorOptions = {}\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"plane-detection\";\r\n        if (this._xrSessionManager.session) {\r\n            this._init();\r\n        } else {\r\n            this._xrSessionManager.onXRSessionInit.addOnce(() => {\r\n                this._init();\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * detach this feature.\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n\r\n        if (!this._options.doNotRemovePlanesOnSessionEnded) {\r\n            while (this._detectedPlanes.length) {\r\n                const toRemove = this._detectedPlanes.pop();\r\n                if (toRemove) {\r\n                    this.onPlaneRemovedObservable.notifyObservers(toRemove);\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n        this.onPlaneAddedObservable.clear();\r\n        this.onPlaneRemovedObservable.clear();\r\n        this.onPlaneUpdatedObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Check if the needed objects are defined.\r\n     * This does not mean that the feature is enabled, but that the objects needed are well defined.\r\n     * @returns true if the initial compatibility test passed\r\n     */\r\n    public isCompatible(): boolean {\r\n        return typeof XRPlane !== \"undefined\";\r\n    }\r\n\r\n    /**\r\n     * Enable room capture mode.\r\n     * When enabled and supported by the system,\r\n     * the detectedPlanes array will be populated with the detected room boundaries\r\n     * @see https://immersive-web.github.io/real-world-geometry/plane-detection.html#dom-xrsession-initiateroomcapture\r\n     * @returns true if plane detection is enabled and supported. Will reject if not supported.\r\n     */\r\n    public async initiateRoomCapture(): Promise<void> {\r\n        if (this._xrSessionManager.session.initiateRoomCapture) {\r\n            return this._xrSessionManager.session.initiateRoomCapture();\r\n        }\r\n        return Promise.reject(\"initiateRoomCapture is not supported on this session\");\r\n    }\r\n\r\n    protected _onXRFrame(frame: XRFrame) {\r\n        if (!this.attached || !this._enabled || !frame) {\r\n            return;\r\n        }\r\n\r\n        const detectedPlanes = frame.detectedPlanes || frame.worldInformation?.detectedPlanes;\r\n        if (detectedPlanes) {\r\n            // remove all planes that are not currently detected in the frame\r\n            for (let planeIdx = 0; planeIdx < this._detectedPlanes.length; planeIdx++) {\r\n                const plane = this._detectedPlanes[planeIdx];\r\n                if (!detectedPlanes.has(plane.xrPlane)) {\r\n                    this._detectedPlanes.splice(planeIdx--, 1);\r\n                    this.onPlaneRemovedObservable.notifyObservers(plane);\r\n                }\r\n            }\r\n\r\n            // now check for new ones\r\n            detectedPlanes.forEach((xrPlane) => {\r\n                if (!this._lastFrameDetected.has(xrPlane)) {\r\n                    const newPlane: Partial<IWebXRPlane> = {\r\n                        id: planeIdProvider++,\r\n                        xrPlane: xrPlane,\r\n                        polygonDefinition: [],\r\n                    };\r\n                    const plane = this._updatePlaneWithXRPlane(xrPlane, newPlane, frame);\r\n                    this._detectedPlanes.push(plane);\r\n                    this.onPlaneAddedObservable.notifyObservers(plane);\r\n                } else {\r\n                    // updated?\r\n                    if (xrPlane.lastChangedTime === this._xrSessionManager.currentTimestamp) {\r\n                        const index = this._findIndexInPlaneArray(xrPlane);\r\n                        const plane = this._detectedPlanes[index];\r\n                        this._updatePlaneWithXRPlane(xrPlane, plane, frame);\r\n                        this.onPlaneUpdatedObservable.notifyObservers(plane);\r\n                    }\r\n                }\r\n            });\r\n            this._lastFrameDetected = detectedPlanes;\r\n        }\r\n    }\r\n\r\n    private _init() {\r\n        const internalInit = () => {\r\n            this._enabled = true;\r\n            if (this._detectedPlanes.length) {\r\n                this._detectedPlanes.length = 0;\r\n            }\r\n        };\r\n\r\n        // Only supported by BabylonNative\r\n        if (!!this._xrSessionManager.isNative && !!this._options.preferredDetectorOptions && !!this._xrSessionManager.session.trySetPreferredPlaneDetectorOptions) {\r\n            this._xrSessionManager.session.trySetPreferredPlaneDetectorOptions(this._options.preferredDetectorOptions);\r\n        }\r\n\r\n        if (!this._xrSessionManager.session.updateWorldTrackingState) {\r\n            internalInit();\r\n            return;\r\n        }\r\n        this._xrSessionManager.session.updateWorldTrackingState({ planeDetectionState: { enabled: true } });\r\n        internalInit();\r\n    }\r\n\r\n    private _updatePlaneWithXRPlane(xrPlane: XRPlane, plane: Partial<IWebXRPlane>, xrFrame: XRFrame): IWebXRPlane {\r\n        plane.polygonDefinition = xrPlane.polygon.map((xrPoint) => {\r\n            const rightHandedSystem = this._xrSessionManager.scene.useRightHandedSystem ? 1 : -1;\r\n            return new Vector3(xrPoint.x, xrPoint.y, xrPoint.z * rightHandedSystem);\r\n        });\r\n        // matrix\r\n        const pose = xrFrame.getPose(xrPlane.planeSpace, this._xrSessionManager.referenceSpace);\r\n        if (pose) {\r\n            const mat = plane.transformationMatrix || new Matrix();\r\n            Matrix.FromArrayToRef(pose.transform.matrix, 0, mat);\r\n            if (!this._xrSessionManager.scene.useRightHandedSystem) {\r\n                mat.toggleModelMatrixHandInPlace();\r\n            }\r\n            plane.transformationMatrix = mat;\r\n            if (this._options.worldParentNode) {\r\n                mat.multiplyToRef(this._options.worldParentNode.getWorldMatrix(), mat);\r\n            }\r\n        }\r\n\r\n        return <IWebXRPlane>plane;\r\n    }\r\n\r\n    /**\r\n     * avoiding using Array.find for global support.\r\n     * @param xrPlane the plane to find in the array\r\n     * @returns the index of the plane in the array or -1 if not found\r\n     */\r\n    private _findIndexInPlaneArray(xrPlane: XRPlane) {\r\n        for (let i = 0; i < this._detectedPlanes.length; ++i) {\r\n            if (this._detectedPlanes[i].xrPlane === xrPlane) {\r\n                return i;\r\n            }\r\n        }\r\n        return -1;\r\n    }\r\n}\r\n\r\n//register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRPlaneDetector.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRPlaneDetector(xrSessionManager, options);\r\n    },\r\n    WebXRPlaneDetector.Version\r\n);\r\n"]}