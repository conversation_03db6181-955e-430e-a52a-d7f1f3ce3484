{"version": 3, "file": "tensor.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Maths/tensor.ts"], "names": [], "mappings": ";AAkeA,wDAAwD", "sourcesContent": ["import type { DeepImmutable, Flatten, FloatArray, Length } from \"../types\";\r\n/**\r\n * Computes the tensor dimension of a multi-dimensional array\r\n */\r\nexport type Dimension<T> = T extends Array<infer U> ? [Length<T>, ...Dimension<U>] : T extends readonly [infer U, ...infer R] ? [Length<T>, ...Dimension<U>] : [];\r\n\r\n/**\r\n * Possible values for a Tensor\r\n */\r\nexport type TensorValue = number[] | TensorValue[];\r\n\r\n/**\r\n * Extracts the value type of a Tensor\r\n */\r\nexport type ValueOfTensor<T = unknown> = T extends Tensor<infer V> ? V : TensorValue;\r\n\r\n/**\r\n * Describes a mathematical tensor.\r\n * @see https://wikipedia.org/wiki/Tensor\r\n */\r\nexport interface Tensor<V extends TensorValue = TensorValue> {\r\n    /**\r\n     * An array of the size of each dimension.\r\n     * For example, [3] for a Vector3 and [4,4] for a Matrix\r\n     * @remarks\r\n     * This is to allow implementations with using a getter\r\n     */\r\n    readonly dimension: Readonly<Dimension<V>>;\r\n\r\n    /**\r\n     * The rank of the tensor. This is the same as the length of the tensor's dimension array.\r\n     * @remarks\r\n     * This is to allow implementations with using a getter\r\n     */\r\n    readonly rank: number;\r\n\r\n    /**\r\n     * Gets class name\r\n     * @returns the class name\r\n     */\r\n\r\n    getClassName(): string;\r\n\r\n    /**\r\n     * Gets current instance hash code\r\n     * @returns the instance hash code as a number\r\n     */\r\n    getHashCode(): number;\r\n\r\n    /**\r\n     * Sets the instance coordinates in the given array from the given index.\r\n     * @param array defines the source array\r\n     * @param index defines the offset in source array\r\n     * @returns the current instance\r\n     */\r\n    toArray(array: FloatArray, index?: number): this;\r\n\r\n    /**\r\n     * Update the current instance from an array\r\n     * @param array defines the destination array\r\n     * @param index defines the offset in the destination array\r\n     * @returns the current instance\r\n     */\r\n    fromArray(array: DeepImmutable<FloatArray>, index?: number): this;\r\n\r\n    /**\r\n     * Copy the current instance to an array\r\n     * @returns a new array with the instance coordinates.\r\n     */\r\n    asArray(): Flatten<V>;\r\n\r\n    /**\r\n     * Sets the current instance coordinates with the given source coordinates\r\n     * @param source defines the source instance\r\n     * @returns the current updated instance\r\n     */\r\n    copyFrom(source: DeepImmutable<this>): this;\r\n\r\n    /**\r\n     * Sets the instance coordinates with the given floats\r\n     * @returns the current updated instance\r\n     */\r\n\r\n    copyFromFloats(...floats: Flatten<V>): this;\r\n\r\n    /**\r\n     * Sets the instance coordinates with the given floats\r\n     * @returns the current updated instance\r\n     */\r\n    set(...values: Flatten<V>): this;\r\n\r\n    /**\r\n     * Sets the instance coordinates to the given value\r\n     * @returns the current updated instance\r\n     */\r\n    setAll(value: number): this;\r\n\r\n    /**\r\n     * Add another instance with the current one\r\n     * @param other defines the other instance\r\n     * @returns a new instance set with the addition of the current instance and the given one coordinates\r\n     */\r\n    add(other: DeepImmutable<this>): this;\r\n\r\n    /**\r\n     * Sets the \"result\" coordinates with the addition of the current instance and the given one coordinates\r\n     * @param other defines the other instance\r\n     * @param result defines the target instance\r\n     * @returns result input\r\n     */\r\n    addToRef(other: DeepImmutable<this>, result: this): this;\r\n\r\n    /**\r\n     * Set the instance coordinates by adding the given instance coordinates\r\n     * @param other defines the other instance\r\n     * @returns the current updated instance\r\n     */\r\n    addInPlace(other: DeepImmutable<this>): this;\r\n\r\n    /**\r\n     * Adds the given coordinates to the current instance\r\n     * @param floats the floats to add\r\n     * @returns the current updated instance\r\n     */\r\n    addInPlaceFromFloats(...floats: Flatten<V>): this;\r\n\r\n    /**\r\n     * Returns a new instance set with the subtracted coordinates of other's coordinates from the current coordinates.\r\n     * @param other defines the other instance\r\n     * @returns a new instance\r\n     */\r\n    subtract(other: DeepImmutable<this>): this;\r\n\r\n    /**\r\n     * Sets the \"result\" coordinates with the subtraction of the other's coordinates from the current coordinates.\r\n     * @param other defines the other instance\r\n     * @param result defines the target instance\r\n     * @returns result input\r\n     */\r\n    subtractToRef(other: DeepImmutable<this>, result: this): this;\r\n\r\n    /**\r\n     * Sets the current instance coordinates by subtracting from it the given one coordinates\r\n     * @param other defines the other instance\r\n     * @returns the current updated instance\r\n     */\r\n    subtractInPlace(other: DeepImmutable<this>): this;\r\n\r\n    /**\r\n     * Returns a new instance set with the subtraction of the given floats from the current instance coordinates\r\n     * @param floats the coordinates to subtract\r\n     * @returns the resulting instance\r\n     */\r\n    subtractFromFloats(...floats: Flatten<V>): this;\r\n\r\n    /**\r\n     * Subtracts the given floats from the current instance coordinates and set the given instance \"result\" with this result\r\n     * Note: Implementation uses array magic so types may be confusing.\r\n     * @param args the coordinates to subtract with the last element as the result\r\n     * @returns the result\r\n     */\r\n    subtractFromFloatsToRef(...args: [...Flatten<V>, this]): this;\r\n\r\n    /**\r\n     * Returns a new instance set with the multiplication of the current instance and the given one coordinates\r\n     * @param other defines the other instance\r\n     * @returns a new instance\r\n     */\r\n    multiply(other: DeepImmutable<this>): this;\r\n\r\n    /**\r\n     * Sets \"result\" coordinates with the multiplication of the current instance and the given one coordinates\r\n     * @param other defines the other instance\r\n     * @param result defines the target instance\r\n     * @returns result input\r\n     */\r\n    multiplyToRef(other: DeepImmutable<this>, result: this): this;\r\n\r\n    /**\r\n     * Multiplies in place the current instance coordinates by the given ones\r\n     * @param other defines the other instance\r\n     * @returns the current updated instance\r\n     */\r\n    multiplyInPlace(other: DeepImmutable<this>): this;\r\n\r\n    /**\r\n     * Gets a new instance set with the instance coordinates multiplied by the given floats\r\n     * @returns a new instance\r\n     */\r\n    multiplyByFloats(...floats: Flatten<V>): this;\r\n\r\n    /**\r\n     * Returns a new instance set with the instance coordinates divided by the given one coordinates\r\n     * @param other defines the other instance\r\n     * @returns a new instance\r\n     */\r\n    divide(other: DeepImmutable<this>): this;\r\n\r\n    /**\r\n     * Sets the \"result\" coordinates with the instance coordinates divided by the given one coordinates\r\n     * @param other defines the other instance\r\n     * @param result defines the target instance\r\n     * @returns result input\r\n     */\r\n    divideToRef(other: DeepImmutable<this>, result: this): this;\r\n\r\n    /**\r\n     * Divides the current instance coordinates by the given ones\r\n     * @param other defines the other instance\r\n     * @returns the current updated instance\r\n     */\r\n    divideInPlace(other: DeepImmutable<this>): this;\r\n\r\n    /**\r\n     * Updates the current instance with the minmal coordinate values between its and the given instance ones.\r\n     * @param other defines the other instance\r\n     * @returns this current updated instance\r\n     */\r\n    minimizeInPlace(other: DeepImmutable<this>): this;\r\n\r\n    /**\r\n     * Updates the current instance with the minmal coordinate values between its and the given floats.\r\n     * @param floats defines the floats to compare against\r\n     * @returns this current updated instance\r\n     */\r\n    minimizeInPlaceFromFloats(...floats: Flatten<V>): this;\r\n\r\n    /**\r\n     * Updates the current instance with the maximal coordinate values between its and the given instance ones.\r\n     * @param other defines the other instance\r\n     * @returns this current updated instance\r\n     */\r\n    maximizeInPlace(other: DeepImmutable<this>): this;\r\n\r\n    /**\r\n     * Updates the current instance with the maximal coordinate values between its and the given floats.\r\n     * @param floats defines the floats to compare against\r\n     * @returns this current updated instance\r\n     */\r\n    maximizeInPlaceFromFloats(...floats: Flatten<V>): this;\r\n\r\n    /**\r\n     * Gets a new instance with current instance negated coordinates\r\n     * @returns a new instance\r\n     */\r\n    negate(): this;\r\n\r\n    /**\r\n     * Negate this instance in place\r\n     * @returns this\r\n     */\r\n    negateInPlace(): this;\r\n\r\n    /**\r\n     * Negate the current instance and stores the result in the given instance \"result\" coordinates\r\n     * @param result defines the instance object where to store the result\r\n     * @returns the result\r\n     */\r\n    negateToRef(result: this): this;\r\n\r\n    /**\r\n     * Multiply the instance coordinates by\r\n     * @param scale defines the scaling factor\r\n     * @returns the current updated instance\r\n     */\r\n    scaleInPlace(scale: number): this;\r\n\r\n    /**\r\n     * Returns a new instance scaled by \"scale\" from the current instance\r\n     * @param scale defines the scaling factor\r\n     * @returns a new instance\r\n     */\r\n    scale(scale: number): this;\r\n\r\n    /**\r\n     * Scale the current instance values by a factor to a given instance\r\n     * @param scale defines the scale factor\r\n     * @param result defines the instance object where to store the result\r\n     * @returns result input\r\n     */\r\n    scaleToRef(scale: number, result: this): this;\r\n\r\n    /**\r\n     * Scale the current instance values by a factor and add the result to a given instance\r\n     * @param scale defines the scale factor\r\n     * @param result defines the instance object where to store the result\r\n     * @returns result input\r\n     */\r\n    scaleAndAddToRef(scale: number, result: this): this;\r\n\r\n    /**\r\n     * Gets a boolean if two instances are equals\r\n     * @param other defines the other instance\r\n     * @returns true if the given instance coordinates strictly equal the current instance ones\r\n     */\r\n    equals(other: DeepImmutable<this>): boolean;\r\n\r\n    /**\r\n     * Gets a boolean if two instances are equals (using an epsilon value)\r\n     * @param other defines the other instance\r\n     * @param epsilon defines the minimal distance to consider equality\r\n     * @returns true if the given instance coordinates are close to the current ones by a distance of epsilon.\r\n     */\r\n    equalsWithEpsilon(other: DeepImmutable<this>, epsilon?: number): boolean;\r\n\r\n    /**\r\n     * Returns true if the current Vectoe coordinates equals the given floats\r\n     * @param floats defines the coordinates to compare against\r\n     * @returns true if both instances are equal\r\n     */\r\n    equalsToFloats(...floats: Flatten<V>): boolean;\r\n\r\n    /**\r\n     * Gets a new instance from current instance floored values\r\n     * eg (1.2, 2.31) returns (1, 2)\r\n     * @returns a new instance\r\n     */\r\n    floor(): this;\r\n\r\n    /**\r\n     * Gets the current instance's floored values and stores them in result\r\n     * @param result the instance to store the result in\r\n     * @returns the result instance\r\n     */\r\n    floorToRef(result: this): this;\r\n\r\n    /**\r\n     * Gets a new instance from current instance fractional values\r\n     * eg (1.2, 2.31) returns (0.2, 0.31)\r\n     * @returns a new instance\r\n     */\r\n    fract(): this;\r\n\r\n    /**\r\n     * Gets the current instance's fractional values and stores them in result\r\n     * @param result the instance to store the result in\r\n     * @returns the result instance\r\n     */\r\n    fractToRef(result: this): this;\r\n\r\n    /**\r\n     * Gets a new instance copied from the instance\r\n     * @returns a new instance\r\n     */\r\n    clone(): this;\r\n}\r\n\r\n/* eslint-disable @typescript-eslint/naming-convention */\r\n/**\r\n * Static side of Tensor\r\n */\r\nexport interface TensorStatic<T extends Tensor<any[]>> {\r\n    /**\r\n     * Creates a new instance from the given coordinates\r\n     */\r\n    new (...coords: Flatten<ValueOfTensor<T>>): T;\r\n\r\n    /**\r\n     * So [[static]].prototype has typings, instead of just any\r\n     */\r\n    prototype: T;\r\n\r\n    /**\r\n     * Returns a new instance with random values between min and max\r\n     * @param min the minimum random value\r\n     * @param max the maximum random value\r\n     * @returns a instance with random values between min and max\r\n     */\r\n    Random(min?: number, max?: number): T;\r\n\r\n    /**\r\n     * Returns a new instance with random values between min and max\r\n     * @param min the minimum random value\r\n     * @param max the maximum random value\r\n     * @param result the result to store the random values in\r\n     * @returns the updated result instance\r\n     */\r\n    RandomToRef(min: number | undefined, max: number | undefined, result: T): T;\r\n\r\n    /**\r\n     * Gets a new instance from the given index element of the given array\r\n     * @param array defines the data source\r\n     * @param offset defines the offset in the data source\r\n     * @returns a new instance\r\n     */\r\n    FromArray(array: DeepImmutable<FloatArray>, offset?: number): T;\r\n\r\n    /**\r\n     * Sets \"result\" from the given index element of the given array\r\n     * @param array defines the data source\r\n     * @param offset defines the offset in the data source\r\n     * @param result defines the target instance\r\n     * @returns result input\r\n     */\r\n    FromArrayToRef(array: DeepImmutable<FloatArray>, offset: number, result: T): T;\r\n\r\n    /**\r\n     * Sets the given instance \"result\" with the given floats.\r\n     * @param args defines the coordinates of the source with the last paramater being the result\r\n     */\r\n    FromFloatsToRef(...args: [...Flatten<ValueOfTensor<T>>, T]): T;\r\n\r\n    /**\r\n     * Gets the dot product of the instance \"left\" and the instance \"right\"\r\n     * @param left defines first instance\r\n     * @param right defines second instance\r\n     * @returns the dot product (float)\r\n     */\r\n    Dot(left: DeepImmutable<T>, right: DeepImmutable<T>): number;\r\n\r\n    /**\r\n     * Gets a new instance set with the minimal coordinate values from the \"left\" and \"right\" instances\r\n     * @param left defines 1st instance\r\n     * @param right defines 2nd instance\r\n     * @returns a new instance\r\n     */\r\n    Minimize(left: DeepImmutable<T>, right: DeepImmutable<T>): T;\r\n\r\n    /**\r\n     * Gets a new instance set with the maximal coordinate values from the \"left\" and \"right\" instances\r\n     * @param left defines 1st instance\r\n     * @param right defines 2nd instance\r\n     * @returns a new instance\r\n     */\r\n    Maximize(left: DeepImmutable<T>, right: DeepImmutable<T>): T;\r\n\r\n    /**\r\n     * Gets the distance between the instances \"value1\" and \"value2\"\r\n     * @param value1 defines first instance\r\n     * @param value2 defines second instance\r\n     * @returns the distance between instances\r\n     */\r\n    Distance(value1: DeepImmutable<T>, value2: DeepImmutable<T>): number;\r\n\r\n    /**\r\n     * Returns the squared distance between the instances \"value1\" and \"value2\"\r\n     * @param value1 defines first instance\r\n     * @param value2 defines second instance\r\n     * @returns the squared distance between instances\r\n     */\r\n    DistanceSquared(value1: DeepImmutable<T>, value2: DeepImmutable<T>): number;\r\n\r\n    /**\r\n     * Gets a new instance located at the center of the instances \"value1\" and \"value2\"\r\n     * @param value1 defines first instance\r\n     * @param value2 defines second instance\r\n     * @returns a new instance\r\n     */\r\n    Center(value1: DeepImmutable<T>, value2: DeepImmutable<T>): T;\r\n\r\n    /**\r\n     * Gets the center of the instances \"value1\" and \"value2\" and stores the result in the instance \"ref\"\r\n     * @param value1 defines first instance\r\n     * @param value2 defines second instance\r\n     * @param ref defines third instance\r\n     * @returns ref\r\n     */\r\n    CenterToRef(value1: DeepImmutable<T>, value2: DeepImmutable<T>, ref: T): T;\r\n\r\n    /**\r\n     * Returns a new instance set with same the coordinates than \"value\" ones if the instance \"value\" is in the square defined by \"min\" and \"max\".\r\n     * If a coordinate of \"value\" is lower than \"min\" coordinates, the returned instance is given this \"min\" coordinate.\r\n     * If a coordinate of \"value\" is greater than \"max\" coordinates, the returned instance is given this \"max\" coordinate\r\n     * @param value defines the value to clamp\r\n     * @param min defines the lower limit\r\n     * @param max defines the upper limit\r\n     * @returns a new instance\r\n     */\r\n    Clamp(value: DeepImmutable<T>, min: DeepImmutable<T>, max: DeepImmutable<T>): T;\r\n\r\n    /**\r\n     * Returns a new instance set with same the coordinates than \"value\" ones if the instance \"value\" is in the square defined by \"min\" and \"max\".\r\n     * If a coordinate of \"value\" is lower than \"min\" coordinates, the returned instance is given this \"min\" coordinate.\r\n     * If a coordinate of \"value\" is greater than \"max\" coordinates, the returned instance is given this \"max\" coordinate\r\n     * @param value defines the value to clamp\r\n     * @param min defines the lower limit\r\n     * @param max defines the upper limit\r\n     * @param result defines the instance where to store the result\r\n     * @returns the updated result instance\r\n     */\r\n    ClampToRef(value: DeepImmutable<T>, min: DeepImmutable<T>, max: DeepImmutable<T>, result: T): T;\r\n}\r\n/* eslint-enable @typescript-eslint/naming-convention */\r\n"]}