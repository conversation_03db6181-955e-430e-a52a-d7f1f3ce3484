{"version": 3, "file": "stringDictionary.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/stringDictionary.ts"], "names": [], "mappings": "AAEA;;;;GAIG;AACH,MAAM,OAAO,gBAAgB;IAA7B;QA8KY,WAAM,GAAG,CAAC,CAAC;QACX,UAAK,GAAyB,EAAE,CAAC;IAC7C,CAAC;IA/KG;;;;OAIG;IACI,QAAQ,CAAC,MAA2B;QACvC,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACI,GAAG,CAAC,GAAW;QAClB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,GAAG,KAAK,SAAS,EAAE;YACnB,OAAO,GAAG,CAAC;SACd;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;OAOG;IACI,mBAAmB,CAAC,GAAW,EAAE,OAA2B;QAC/D,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACxB,IAAI,GAAG,KAAK,SAAS,EAAE;YACnB,OAAO,GAAG,CAAC;SACd;QAED,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QACnB,IAAI,GAAG,EAAE;YACL,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;SACtB;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,GAAW,EAAE,GAAM;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,MAAM,KAAK,SAAS,EAAE;YACtB,OAAO,MAAM,CAAC;SACjB;QAED,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACnB,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,GAAW;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC;IACzC,CAAC;IAED;;;;;OAKG;IACI,GAAG,CAAC,GAAW,EAAE,KAAQ;QAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;YAC/B,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACxB,EAAE,IAAI,CAAC,MAAM,CAAC;QACd,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,GAAG,CAAC,GAAW,EAAE,KAAQ;QAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;YAC/B,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACxB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,GAAW;QAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,GAAG,KAAK,SAAS,EAAE;YACnB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACvB,EAAE,IAAI,CAAC,MAAM,CAAC;YACd,OAAO,GAAG,CAAC;SACd;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,GAAW;QACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACpB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACvB,EAAE,IAAI,CAAC,MAAM,CAAC;YACd,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,QAAuC;QAClD,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE;YAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;SACtB;IACL,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAO,QAAuC;QACtD,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE;YAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC/B,IAAI,GAAG,EAAE;gBACL,OAAO,GAAG,CAAC;aACd;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;CAIJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\n\r\n/**\r\n * This class implement a typical dictionary using a string as key and the generic type T as value.\r\n * The underlying implementation relies on an associative array to ensure the best performances.\r\n * The value can be anything including 'null' but except 'undefined'\r\n */\r\nexport class StringDictionary<T> {\r\n    /**\r\n     * This will clear this dictionary and copy the content from the 'source' one.\r\n     * If the T value is a custom object, it won't be copied/cloned, the same object will be used\r\n     * @param source the dictionary to take the content from and copy to this dictionary\r\n     */\r\n    public copyFrom(source: StringDictionary<T>) {\r\n        this.clear();\r\n        source.forEach((t, v) => this.add(t, v));\r\n    }\r\n\r\n    /**\r\n     * Get a value based from its key\r\n     * @param key the given key to get the matching value from\r\n     * @returns the value if found, otherwise undefined is returned\r\n     */\r\n    public get(key: string): T | undefined {\r\n        const val = this._data[key];\r\n        if (val !== undefined) {\r\n            return val;\r\n        }\r\n        return undefined;\r\n    }\r\n\r\n    /**\r\n     * Get a value from its key or add it if it doesn't exist.\r\n     * This method will ensure you that a given key/data will be present in the dictionary.\r\n     * @param key the given key to get the matching value from\r\n     * @param factory the factory that will create the value if the key is not present in the dictionary.\r\n     * The factory will only be invoked if there's no data for the given key.\r\n     * @returns the value corresponding to the key.\r\n     */\r\n    public getOrAddWithFactory(key: string, factory: (key: string) => T): T {\r\n        let val = this.get(key);\r\n        if (val !== undefined) {\r\n            return val;\r\n        }\r\n\r\n        val = factory(key);\r\n        if (val) {\r\n            this.add(key, val);\r\n        }\r\n\r\n        return val;\r\n    }\r\n\r\n    /**\r\n     * Get a value from its key if present in the dictionary otherwise add it\r\n     * @param key the key to get the value from\r\n     * @param val if there's no such key/value pair in the dictionary add it with this value\r\n     * @returns the value corresponding to the key\r\n     */\r\n    public getOrAdd(key: string, val: T): T {\r\n        const curVal = this.get(key);\r\n        if (curVal !== undefined) {\r\n            return curVal;\r\n        }\r\n\r\n        this.add(key, val);\r\n        return val;\r\n    }\r\n\r\n    /**\r\n     * Check if there's a given key in the dictionary\r\n     * @param key the key to check for\r\n     * @returns true if the key is present, false otherwise\r\n     */\r\n    public contains(key: string): boolean {\r\n        return this._data[key] !== undefined;\r\n    }\r\n\r\n    /**\r\n     * Add a new key and its corresponding value\r\n     * @param key the key to add\r\n     * @param value the value corresponding to the key\r\n     * @returns true if the operation completed successfully, false if we couldn't insert the key/value because there was already this key in the dictionary\r\n     */\r\n    public add(key: string, value: T): boolean {\r\n        if (this._data[key] !== undefined) {\r\n            return false;\r\n        }\r\n        this._data[key] = value;\r\n        ++this._count;\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Update a specific value associated to a key\r\n     * @param key defines the key to use\r\n     * @param value defines the value to store\r\n     * @returns true if the value was updated (or false if the key was not found)\r\n     */\r\n    public set(key: string, value: T): boolean {\r\n        if (this._data[key] === undefined) {\r\n            return false;\r\n        }\r\n        this._data[key] = value;\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Get the element of the given key and remove it from the dictionary\r\n     * @param key defines the key to search\r\n     * @returns the value associated with the key or null if not found\r\n     */\r\n    public getAndRemove(key: string): Nullable<T> {\r\n        const val = this.get(key);\r\n        if (val !== undefined) {\r\n            delete this._data[key];\r\n            --this._count;\r\n            return val;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Remove a key/value from the dictionary.\r\n     * @param key the key to remove\r\n     * @returns true if the item was successfully deleted, false if no item with such key exist in the dictionary\r\n     */\r\n    public remove(key: string): boolean {\r\n        if (this.contains(key)) {\r\n            delete this._data[key];\r\n            --this._count;\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Clear the whole content of the dictionary\r\n     */\r\n    public clear() {\r\n        this._data = {};\r\n        this._count = 0;\r\n    }\r\n\r\n    /**\r\n     * Gets the current count\r\n     */\r\n    public get count() {\r\n        return this._count;\r\n    }\r\n\r\n    /**\r\n     * Execute a callback on each key/val of the dictionary.\r\n     * Note that you can remove any element in this dictionary in the callback implementation\r\n     * @param callback the callback to execute on a given key/value pair\r\n     */\r\n    public forEach(callback: (key: string, val: T) => void) {\r\n        for (const cur in this._data) {\r\n            const val = this._data[cur];\r\n            callback(cur, val);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Execute a callback on every occurrence of the dictionary until it returns a valid TRes object.\r\n     * If the callback returns null or undefined the method will iterate to the next key/value pair\r\n     * Note that you can remove any element in this dictionary in the callback implementation\r\n     * @param callback the callback to execute, if it return a valid T instanced object the enumeration will stop and the object will be returned\r\n     * @returns the first item\r\n     */\r\n    public first<TRes>(callback: (key: string, val: T) => TRes) {\r\n        for (const cur in this._data) {\r\n            const val = this._data[cur];\r\n            const res = callback(cur, val);\r\n            if (res) {\r\n                return res;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private _count = 0;\r\n    private _data: { [key: string]: T } = {};\r\n}\r\n"]}