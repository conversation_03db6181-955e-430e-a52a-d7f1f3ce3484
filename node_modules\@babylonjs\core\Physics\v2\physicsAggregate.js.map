{"version": 3, "file": "physicsAggregate.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Physics/v2/physicsAggregate.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAC1E,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AACjD,OAAO,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAC;AAM7E,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAkExD;;;;;;GAMG;AACH,MAAM,OAAO,gBAAgB;IAoBzB;IACI;;OAEG;IACI,aAA4B;IACnC;;OAEG;IACI,IAAqC,EACpC,WAAuC,EAAE,IAAI,EAAE,CAAC,EAAE,EAClD,MAAc;QANf,kBAAa,GAAb,aAAa,CAAe;QAI5B,SAAI,GAAJ,IAAI,CAAiC;QACpC,aAAQ,GAAR,QAAQ,CAA0C;QAClD,WAAM,GAAN,MAAM,CAAQ;QAdlB,8BAAyB,GAAG,IAAI,CAAC;QAgBrC,eAAe;QACf,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,MAAM,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC;YACvE,OAAO;SACV;QACD,MAAM,CAAC,GAAG,aAAqB,CAAC;QAChC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE;YAC7E,MAAM,CAAC,IAAI,CACP,qKAAqK,CACxK,CAAC;SACL;QAED,iCAAiC;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,aAAa,CAAC,QAAQ,EAAE;YACxC,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC;SAC1C;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,OAAO;SACV;QAED,wBAAwB;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;QAClE,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAChF,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;QAEzF,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC;QACnG,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,KAAK,CAAC;QACvD,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,aAAa,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACjF,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAK,IAAY,CAAC,YAAY,IAAK,IAAY,CAAC,YAAY,EAAE,KAAK,cAAc,EAAE;YAC/E,IAAI,CAAC,KAAK,GAAG,IAAoB,CAAC;YAClC,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;SAC1C;aAAM;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,YAAY,CAAC,EAAE,IAAI,EAAE,IAAwB,EAAE,UAAU,EAAE,IAAI,CAAC,QAAe,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SACpH;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YAC9B,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;SAC/B;QAED,IAAI,CAAC,QAAQ,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAC7F,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEpC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QAE1D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YACxE,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,qBAAqB;QACzB,IAAK,IAAI,CAAC,aAA8B,CAAC,kBAAkB,EAAE;YACzD,OAAQ,IAAI,CAAC,aAA8B,CAAC,kBAAkB,EAAE,CAAC,WAAW,CAAC;SAChF;aAAM;YACH,OAAO,IAAI,WAAW,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;SACrF;IACL,CAAC;IAEO,YAAY,CAAC,IAAmB;QACpC,OAAQ,IAAY,EAAE,gBAAgB,EAAE,GAAG,CAAC,CAAC;IACjD,CAAC;IAEO,eAAe;QACnB,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACxC,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;QAChC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACxB,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QACpD,0FAA0F;QAC1F,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEhC,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QACzB,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAEhD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACvB,MAAM,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YAC3B,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACnD,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;SACjC;QAED,QAAQ,IAAI,CAAC,IAAI,EAAE;YACf,KAAK,gBAAgB,CAAC,MAAM;gBACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;oBACnI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;iBACxC;qBAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;oBAC9B,MAAM,CAAC,IAAI,CAAC,8GAA8G,CAAC,CAAC;oBAC5H,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;iBACxE;gBACD,MAAM;YACV,KAAK,gBAAgB,CAAC,OAAO;gBACzB;oBACI,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;oBAChC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,SAAS,CAAC;oBACzD,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC;oBACpF,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC;iBACnG;gBACD,MAAM;YACV,KAAK,gBAAgB,CAAC,QAAQ;gBAC1B;oBACI,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;oBAChC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,SAAS,CAAC;oBACzD,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACxE,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACvF;gBACD,MAAM;YACV,KAAK,gBAAgB,CAAC,IAAI,CAAC;YAC3B,KAAK,gBAAgB,CAAC,WAAW;gBAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;oBAC9D,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,aAAqB,CAAC;iBACnD;qBAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;oBACtE,MAAM,IAAI,KAAK,CACX,oJAAoJ,CACvJ,CAAC;iBACL;gBACD,MAAM;YACV,KAAK,gBAAgB,CAAC,GAAG;gBACrB,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC9F,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACzE,MAAM;SACb;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC9E,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SACpC;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACpB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;SACxB;IACL,CAAC;CACJ", "sourcesContent": ["import { PhysicsBody } from \"./physicsBody\";\r\nimport type { PhysicsMaterial } from \"./physicsMaterial\";\r\nimport { PhysicsShape } from \"./physicsShape\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { Quaternion, TmpVectors, Vector3 } from \"../../Maths/math.vector\";\r\nimport { <PERSON>ala<PERSON> } from \"../../Maths/math.scalar\";\r\nimport { PhysicsMotionType, PhysicsShapeType } from \"./IPhysicsEnginePlugin\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Node } from \"../../node\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { BoundingBox } from \"../../Culling/boundingBox\";\r\n\r\n/**\r\n * The interface for the physics aggregate parameters\r\n */\r\nexport interface PhysicsAggregateParameters {\r\n    /**\r\n     * The mass of the physics aggregate\r\n     */\r\n    mass: number;\r\n\r\n    /**\r\n     * The friction of the physics aggregate\r\n     */\r\n    friction?: number;\r\n\r\n    /**\r\n     * The coefficient of restitution of the physics aggregate\r\n     */\r\n    restitution?: number;\r\n\r\n    /**\r\n     * Radius for sphere, cylinder and capsule\r\n     */\r\n    radius?: number;\r\n\r\n    /**\r\n     * Starting point for cylinder/capsule\r\n     */\r\n    pointA?: Vector3;\r\n\r\n    /**\r\n     * Ending point for cylinder/capsule\r\n     */\r\n    pointB?: Vector3;\r\n\r\n    /**\r\n     * Extents for box\r\n     */\r\n    extents?: Vector3;\r\n\r\n    /**\r\n     * Orientation for box\r\n     */\r\n    rotation?: Quaternion;\r\n\r\n    /**\r\n     * mesh local center\r\n     */\r\n    center?: Vector3;\r\n\r\n    /**\r\n     * mesh object. Used for mesh and convex hull aggregates.\r\n     */\r\n    mesh?: Mesh;\r\n\r\n    /**\r\n     * Physics engine will try to make this body sleeping and not active\r\n     */\r\n    startAsleep?: boolean;\r\n\r\n    /**\r\n     * If true, mark the created shape as a trigger shape\r\n     */\r\n    isTriggerShape?: boolean;\r\n}\r\n/**\r\n * Helper class to create and interact with a PhysicsAggregate.\r\n * This is a transition object that works like Physics Plugin V1 Impostors.\r\n * This helper instanciate all mandatory physics objects to get a body/shape and material.\r\n * It's less efficient that handling body and shapes independently but for prototyping or\r\n * a small numbers of physics objects, it's good enough.\r\n */\r\nexport class PhysicsAggregate {\r\n    /**\r\n     * The body that is associated with this aggregate\r\n     */\r\n    public body: PhysicsBody;\r\n\r\n    /**\r\n     * The shape that is associated with this aggregate\r\n     */\r\n    public shape: PhysicsShape;\r\n\r\n    /**\r\n     * The material that is associated with this aggregate\r\n     */\r\n    public material: PhysicsMaterial;\r\n\r\n    private _disposeShapeWhenDisposed = true;\r\n\r\n    private _nodeDisposeObserver: Nullable<Observer<Node>>;\r\n\r\n    constructor(\r\n        /**\r\n         * The physics-enabled object used as the physics aggregate\r\n         */\r\n        public transformNode: TransformNode,\r\n        /**\r\n         * The type of the physics aggregate\r\n         */\r\n        public type: PhysicsShapeType | PhysicsShape,\r\n        private _options: PhysicsAggregateParameters = { mass: 0 },\r\n        private _scene?: Scene\r\n    ) {\r\n        //sanity check!\r\n        if (!this.transformNode) {\r\n            Logger.Error(\"No object was provided. A physics object is obligatory\");\r\n            return;\r\n        }\r\n        const m = transformNode as Mesh;\r\n        if (this.transformNode.parent && this._options.mass !== 0 && m.hasThinInstances) {\r\n            Logger.Warn(\r\n                \"A physics body has been created for an object which has a parent and thin instances. Babylon physics currently works in local space so unexpected issues may occur.\"\r\n            );\r\n        }\r\n\r\n        // Legacy support for old syntax.\r\n        if (!this._scene && transformNode.getScene) {\r\n            this._scene = transformNode.getScene();\r\n        }\r\n\r\n        if (!this._scene) {\r\n            return;\r\n        }\r\n\r\n        //default options params\r\n        this._options.mass = _options.mass === void 0 ? 0 : _options.mass;\r\n        this._options.friction = _options.friction === void 0 ? 0.2 : _options.friction;\r\n        this._options.restitution = _options.restitution === void 0 ? 0.2 : _options.restitution;\r\n\r\n        const motionType = this._options.mass === 0 ? PhysicsMotionType.STATIC : PhysicsMotionType.DYNAMIC;\r\n        const startAsleep = this._options.startAsleep ?? false;\r\n        this.body = new PhysicsBody(transformNode, motionType, startAsleep, this._scene);\r\n        this._addSizeOptions();\r\n        if ((type as any).getClassName && (type as any).getClassName() === \"PhysicsShape\") {\r\n            this.shape = type as PhysicsShape;\r\n            this._disposeShapeWhenDisposed = false;\r\n        } else {\r\n            this.shape = new PhysicsShape({ type: type as PhysicsShapeType, parameters: this._options as any }, this._scene);\r\n        }\r\n\r\n        if (this._options.isTriggerShape) {\r\n            this.shape.isTrigger = true;\r\n        }\r\n\r\n        this.material = { friction: this._options.friction, restitution: this._options.restitution };\r\n        this.body.shape = this.shape;\r\n        this.shape.material = this.material;\r\n\r\n        this.body.setMassProperties({ mass: this._options.mass });\r\n\r\n        this._nodeDisposeObserver = this.transformNode.onDisposeObservable.add(() => {\r\n            this.dispose();\r\n        });\r\n    }\r\n\r\n    private _getObjectBoundingBox() {\r\n        if ((this.transformNode as AbstractMesh).getRawBoundingInfo) {\r\n            return (this.transformNode as AbstractMesh).getRawBoundingInfo().boundingBox;\r\n        } else {\r\n            return new BoundingBox(new Vector3(-0.5, -0.5, -0.5), new Vector3(0.5, 0.5, 0.5));\r\n        }\r\n    }\r\n\r\n    private _hasVertices(node: TransformNode): boolean {\r\n        return (node as any)?.getTotalVertices() > 0;\r\n    }\r\n\r\n    private _addSizeOptions(): void {\r\n        this.transformNode.computeWorldMatrix(true);\r\n        const bb = this._getObjectBoundingBox();\r\n        const extents = TmpVectors.Vector3[0];\r\n        extents.copyFrom(bb.extendSize);\r\n        extents.scaleInPlace(2);\r\n        extents.multiplyInPlace(this.transformNode.scaling);\r\n        // In case we had any negative scaling, we need to take the absolute value of the extents.\r\n        extents.x = Math.abs(extents.x);\r\n        extents.y = Math.abs(extents.y);\r\n        extents.z = Math.abs(extents.z);\r\n\r\n        const min = TmpVectors.Vector3[1];\r\n        min.copyFrom(bb.minimum);\r\n        min.multiplyInPlace(this.transformNode.scaling);\r\n\r\n        if (!this._options.center) {\r\n            const center = new Vector3();\r\n            center.copyFrom(bb.center);\r\n            center.multiplyInPlace(this.transformNode.scaling);\r\n            this._options.center = center;\r\n        }\r\n\r\n        switch (this.type) {\r\n            case PhysicsShapeType.SPHERE:\r\n                if (!this._options.radius && Scalar.WithinEpsilon(extents.x, extents.y, 0.0001) && Scalar.WithinEpsilon(extents.x, extents.z, 0.0001)) {\r\n                    this._options.radius = extents.x / 2;\r\n                } else if (!this._options.radius) {\r\n                    Logger.Warn(\"Non uniform scaling is unsupported for sphere shapes. Setting the radius to the biggest bounding box extent.\");\r\n                    this._options.radius = Math.max(extents.x, extents.y, extents.z) / 2;\r\n                }\r\n                break;\r\n            case PhysicsShapeType.CAPSULE:\r\n                {\r\n                    const capRadius = extents.x / 2;\r\n                    this._options.radius = this._options.radius ?? capRadius;\r\n                    this._options.pointA = this._options.pointA ?? new Vector3(0, min.y + capRadius, 0);\r\n                    this._options.pointB = this._options.pointB ?? new Vector3(0, min.y + extents.y - capRadius, 0);\r\n                }\r\n                break;\r\n            case PhysicsShapeType.CYLINDER:\r\n                {\r\n                    const capRadius = extents.x / 2;\r\n                    this._options.radius = this._options.radius ?? capRadius;\r\n                    this._options.pointA = this._options.pointA ?? new Vector3(0, min.y, 0);\r\n                    this._options.pointB = this._options.pointB ?? new Vector3(0, min.y + extents.y, 0);\r\n                }\r\n                break;\r\n            case PhysicsShapeType.MESH:\r\n            case PhysicsShapeType.CONVEX_HULL:\r\n                if (!this._options.mesh && this._hasVertices(this.transformNode)) {\r\n                    this._options.mesh = this.transformNode as Mesh;\r\n                } else if (!this._options.mesh || !this._hasVertices(this._options.mesh)) {\r\n                    throw new Error(\r\n                        \"No valid mesh was provided for mesh or convex hull shape parameter. Please provide a mesh with valid geometry (number of vertices greater than 0).\"\r\n                    );\r\n                }\r\n                break;\r\n            case PhysicsShapeType.BOX:\r\n                this._options.extents = this._options.extents ?? new Vector3(extents.x, extents.y, extents.z);\r\n                this._options.rotation = this._options.rotation ?? Quaternion.Identity();\r\n                break;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Releases the body, shape and material\r\n     */\r\n    public dispose(): void {\r\n        if (this._nodeDisposeObserver) {\r\n            this.body.transformNode.onDisposeObservable.remove(this._nodeDisposeObserver);\r\n            this._nodeDisposeObserver = null;\r\n        }\r\n        this.body.dispose();\r\n        if (this._disposeShapeWhenDisposed) {\r\n            this.shape.dispose();\r\n        }\r\n    }\r\n}\r\n"]}