import type { Nullable } from "../../types";
import type { Scene } from "../../scene";
import { Matrix, Vector3 } from "../../Maths/math.vector";
import type { SubMesh } from "../../Meshes/subMesh";
import type { Effect } from "../../Materials/effect";
import "../../Shaders/shadowMap.fragment";
import "../../Shaders/shadowMap.vertex";
import "../../Shaders/depthBoxBlur.fragment";
import { ShadowGenerator } from "./shadowGenerator";
import type { DirectionalLight } from "../directionalLight";
import { BoundingInfo } from "../../Culling/boundingInfo";
import type { DepthRenderer } from "../../Rendering/depthRenderer";
import type { Camera } from "../../Cameras/camera";
/**
 * A CSM implementation allowing casting shadows on large scenes.
 * Documentation : https://doc.babylonjs.com/babylon101/cascadedShadows
 * Based on: https://github.com/TheRealMJP/Shadows and https://johanmedestrom.wordpress.com/2016/03/18/opengl-cascaded-shadow-maps/
 */
export declare class CascadedShadowGenerator extends ShadowGenerator {
    private static readonly _FrustumCornersNDCSpace;
    /**
     * Name of the CSM class
     */
    static CLASSNAME: string;
    /**
     * Defines the default number of cascades used by the CSM.
     */
    static readonly DEFAULT_CASCADES_COUNT = 4;
    /**
     * Defines the minimum number of cascades used by the CSM.
     */
    static MIN_CASCADES_COUNT: number;
    /**
     * Defines the maximum number of cascades used by the CSM.
     */
    static MAX_CASCADES_COUNT: number;
    protected _validateFilter(filter: number): number;
    /**
     * Gets or sets the actual darkness of the soft shadows while using PCSS filtering (value between 0. and 1.)
     */
    penumbraDarkness: number;
    private _numCascades;
    /**
     * Gets or set the number of cascades used by the CSM.
     */
    get numCascades(): number;
    set numCascades(value: number);
    /**
     * Sets this to true if you want that the edges of the shadows don't "swimm" / "shimmer" when rotating the camera.
     * The trade off is that you lose some precision in the shadow rendering when enabling this setting.
     */
    stabilizeCascades: boolean;
    private _freezeShadowCastersBoundingInfo;
    private _freezeShadowCastersBoundingInfoObservable;
    /**
     * Enables or disables the shadow casters bounding info computation.
     * If your shadow casters don't move, you can disable this feature.
     * If it is enabled, the bounding box computation is done every frame.
     */
    get freezeShadowCastersBoundingInfo(): boolean;
    set freezeShadowCastersBoundingInfo(freeze: boolean);
    private _scbiMin;
    private _scbiMax;
    protected _computeShadowCastersBoundingInfo(): void;
    protected _shadowCastersBoundingInfo: BoundingInfo;
    /**
     * Gets or sets the shadow casters bounding info.
     * If you provide your own shadow casters bounding info, first enable freezeShadowCastersBoundingInfo
     * so that the system won't overwrite the bounds you provide
     */
    get shadowCastersBoundingInfo(): BoundingInfo;
    set shadowCastersBoundingInfo(boundingInfo: BoundingInfo);
    protected _breaksAreDirty: boolean;
    protected _minDistance: number;
    protected _maxDistance: number;
    /**
     * Sets the minimal and maximal distances to use when computing the cascade breaks.
     *
     * The values of min / max are typically the depth zmin and zmax values of your scene, for a given frame.
     * If you don't know these values, simply leave them to their defaults and don't call this function.
     * @param min minimal distance for the breaks (default to 0.)
     * @param max maximal distance for the breaks (default to 1.)
     */
    setMinMaxDistance(min: number, max: number): void;
    /** Gets the minimal distance used in the cascade break computation */
    get minDistance(): number;
    /** Gets the maximal distance used in the cascade break computation */
    get maxDistance(): number;
    /**
     * Gets the class name of that object
     * @returns "CascadedShadowGenerator"
     */
    getClassName(): string;
    private _cascadeMinExtents;
    private _cascadeMaxExtents;
    /**
     * Gets a cascade minimum extents
     * @param cascadeIndex index of the cascade
     * @returns the minimum cascade extents
     */
    getCascadeMinExtents(cascadeIndex: number): Nullable<Vector3>;
    /**
     * Gets a cascade maximum extents
     * @param cascadeIndex index of the cascade
     * @returns the maximum cascade extents
     */
    getCascadeMaxExtents(cascadeIndex: number): Nullable<Vector3>;
    private _cascades;
    private _currentLayer;
    private _viewSpaceFrustumsZ;
    private _viewMatrices;
    private _projectionMatrices;
    private _transformMatrices;
    private _transformMatricesAsArray;
    private _frustumLengths;
    private _lightSizeUVCorrection;
    private _depthCorrection;
    private _frustumCornersWorldSpace;
    private _frustumCenter;
    private _shadowCameraPos;
    private _shadowMaxZ;
    /**
     * Gets the shadow max z distance. It's the limit beyond which shadows are not displayed.
     * It defaults to camera.maxZ
     */
    get shadowMaxZ(): number;
    /**
     * Sets the shadow max z distance.
     */
    set shadowMaxZ(value: number);
    protected _debug: boolean;
    /**
     * Gets or sets the debug flag.
     * When enabled, the cascades are materialized by different colors on the screen.
     */
    get debug(): boolean;
    set debug(dbg: boolean);
    private _depthClamp;
    /**
     * Gets or sets the depth clamping value.
     *
     * When enabled, it improves the shadow quality because the near z plane of the light frustum don't need to be adjusted
     * to account for the shadow casters far away.
     *
     * Note that this property is incompatible with PCSS filtering, so it won't be used in that case.
     */
    get depthClamp(): boolean;
    set depthClamp(value: boolean);
    private _cascadeBlendPercentage;
    /**
     * Gets or sets the percentage of blending between two cascades (value between 0. and 1.).
     * It defaults to 0.1 (10% blending).
     */
    get cascadeBlendPercentage(): number;
    set cascadeBlendPercentage(value: number);
    private _lambda;
    /**
     * Gets or set the lambda parameter.
     * This parameter is used to split the camera frustum and create the cascades.
     * It's a value between 0. and 1.: If 0, the split is a uniform split of the frustum, if 1 it is a logarithmic split.
     * For all values in-between, it's a linear combination of the uniform and logarithm split algorithm.
     */
    get lambda(): number;
    set lambda(value: number);
    /**
     * Gets the view matrix corresponding to a given cascade
     * @param cascadeNum cascade to retrieve the view matrix from
     * @returns the cascade view matrix
     */
    getCascadeViewMatrix(cascadeNum: number): Nullable<Matrix>;
    /**
     * Gets the projection matrix corresponding to a given cascade
     * @param cascadeNum cascade to retrieve the projection matrix from
     * @returns the cascade projection matrix
     */
    getCascadeProjectionMatrix(cascadeNum: number): Nullable<Matrix>;
    /**
     * Gets the transformation matrix corresponding to a given cascade
     * @param cascadeNum cascade to retrieve the transformation matrix from
     * @returns the cascade transformation matrix
     */
    getCascadeTransformMatrix(cascadeNum: number): Nullable<Matrix>;
    private _depthRenderer;
    /**
     * Sets the depth renderer to use when autoCalcDepthBounds is enabled.
     *
     * Note that if no depth renderer is set, a new one will be automatically created internally when necessary.
     *
     * You should call this function if you already have a depth renderer enabled in your scene, to avoid
     * doing multiple depth rendering each frame. If you provide your own depth renderer, make sure it stores linear depth!
     * @param depthRenderer The depth renderer to use when autoCalcDepthBounds is enabled. If you pass null or don't call this function at all, a depth renderer will be automatically created
     */
    setDepthRenderer(depthRenderer: Nullable<DepthRenderer>): void;
    private _depthReducer;
    private _autoCalcDepthBounds;
    /**
     * Gets or sets the autoCalcDepthBounds property.
     *
     * When enabled, a depth rendering pass is first performed (with an internally created depth renderer or with the one
     * you provide by calling setDepthRenderer). Then, a min/max reducing is applied on the depth map to compute the
     * minimal and maximal depth of the map and those values are used as inputs for the setMinMaxDistance() function.
     * It can greatly enhance the shadow quality, at the expense of more GPU works.
     * When using this option, you should increase the value of the lambda parameter, and even set it to 1 for best results.
     */
    get autoCalcDepthBounds(): boolean;
    set autoCalcDepthBounds(value: boolean);
    /**
     * Defines the refresh rate of the min/max computation used when autoCalcDepthBounds is set to true
     * Use 0 to compute just once, 1 to compute on every frame, 2 to compute every two frames and so on...
     * Note that if you provided your own depth renderer through a call to setDepthRenderer, you are responsible
     * for setting the refresh rate on the renderer yourself!
     */
    get autoCalcDepthBoundsRefreshRate(): number;
    set autoCalcDepthBoundsRefreshRate(value: number);
    /**
     * Create the cascade breaks according to the lambda, shadowMaxZ and min/max distance properties, as well as the camera near and far planes.
     * This function is automatically called when updating lambda, shadowMaxZ and min/max distances, however you should call it yourself if
     * you change the camera near/far planes!
     */
    splitFrustum(): void;
    private _splitFrustum;
    private _computeMatrices;
    private _computeFrustumInWorldSpace;
    private _computeCascadeFrustum;
    protected _recreateSceneUBOs(): void;
    /**
     *  Support test.
     */
    static get IsSupported(): boolean;
    /**
     * @internal
     */
    static _SceneComponentInitialization: (scene: Scene) => void;
    /**
     * Creates a Cascaded Shadow Generator object.
     * A ShadowGenerator is the required tool to use the shadows.
     * Each directional light casting shadows needs to use its own ShadowGenerator.
     * Documentation : https://doc.babylonjs.com/babylon101/cascadedShadows
     * @param mapSize The size of the texture what stores the shadows. Example : 1024.
     * @param light The directional light object generating the shadows.
     * @param usefulFloatFirst By default the generator will try to use half float textures but if you need precision (for self shadowing for instance), you can use this option to enforce full float texture.
     * @param camera Camera associated with this shadow generator (default: null). If null, takes the scene active camera at the time we need to access it
     * @param useRedTextureType Forces the generator to use a Red instead of a RGBA type for the shadow map texture format (default: true)
     */
    constructor(mapSize: number, light: DirectionalLight, usefulFloatFirst?: boolean, camera?: Nullable<Camera>, useRedTextureType?: boolean);
    protected _initializeGenerator(): void;
    protected _createTargetRenderTexture(): void;
    protected _initializeShadowMap(): void;
    protected _bindCustomEffectForRenderSubMeshForShadowMap(subMesh: SubMesh, effect: Effect): void;
    protected _isReadyCustomDefines(defines: any): void;
    /**
     * Prepare all the defines in a material relying on a shadow map at the specified light index.
     * @param defines Defines of the material we want to update
     * @param lightIndex Index of the light in the enabled light list of the material
     */
    prepareDefines(defines: any, lightIndex: number): void;
    /**
     * Binds the shadow related information inside of an effect (information like near, far, darkness...
     * defined in the generator but impacting the effect).
     * @param lightIndex Index of the light in the enabled light list of the material owning the effect
     * @param effect The effect we are binfing the information for
     */
    bindShadowLight(lightIndex: string, effect: Effect): void;
    /**
     * Gets the transformation matrix of the first cascade used to project the meshes into the map from the light point of view.
     * (eq to view projection * shadow projection matrices)
     * @returns The transform matrix used to create the shadow map
     */
    getTransformMatrix(): Matrix;
    /**
     * Disposes the ShadowGenerator.
     * Returns nothing.
     */
    dispose(): void;
    /**
     * Serializes the shadow generator setup to a json object.
     * @returns The serialized JSON object
     */
    serialize(): any;
    /**
     * Parses a serialized ShadowGenerator and returns a new ShadowGenerator.
     * @param parsedShadowGenerator The JSON object to parse
     * @param scene The scene to create the shadow map for
     * @returns The parsed shadow generator
     */
    static Parse(parsedShadowGenerator: any, scene: Scene): ShadowGenerator;
}
