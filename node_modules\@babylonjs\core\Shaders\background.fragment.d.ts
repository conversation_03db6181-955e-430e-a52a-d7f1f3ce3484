import "./ShadersInclude/backgroundFragmentDeclaration";
import "./ShadersInclude/backgroundUboDeclaration";
import "./ShadersInclude/helperFunctions";
import "./ShadersInclude/reflectionFunction";
import "./ShadersInclude/imageProcessingDeclaration";
import "./ShadersInclude/lightFragmentDeclaration";
import "./ShadersInclude/lightUboDeclaration";
import "./ShadersInclude/lightsFragmentFunctions";
import "./ShadersInclude/shadowsFragmentFunctions";
import "./ShadersInclude/imageProcessingFunctions";
import "./ShadersInclude/logDepthDeclaration";
import "./ShadersInclude/clipPlaneFragmentDeclaration";
import "./ShadersInclude/fogFragmentDeclaration";
import "./ShadersInclude/clipPlaneFragment";
import "./ShadersInclude/lightFragment";
import "./ShadersInclude/logDepthFragment";
import "./ShadersInclude/fogFragment";
/** @internal */
export declare const backgroundPixelShader: {
    name: string;
    shader: string;
};
