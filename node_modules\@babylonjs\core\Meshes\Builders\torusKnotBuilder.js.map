{"version": 3, "file": "torusKnotBuilder.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Builders/torusKnotBuilder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAEzE,kIAAkI;AAClI;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAM,UAAU,yBAAyB,CAAC,OAUzC;IACG,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,MAAM,SAAS,GAAa,EAAE,CAAC;IAC/B,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,MAAM,GAAG,GAAa,EAAE,CAAC;IAEzB,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;IACnC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,GAAG,CAAC;IACjC,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,EAAE,CAAC;IACpD,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,EAAE,CAAC;IACtD,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;IACzB,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;IACzB,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,IAAI,UAAU,CAAC,WAAW,CAAC;IAE9G,SAAS;IACT,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE;QAC7B,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC3B,MAAM,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAChC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE7B,MAAM,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;QACxC,MAAM,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC;QACxC,MAAM,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC;QAE5C,OAAO,IAAI,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACnC,CAAC,CAAC;IAEF,WAAW;IACX,IAAI,CAAS,CAAC;IACd,IAAI,CAAS,CAAC;IACd,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,cAAc,EAAE,CAAC,EAAE,EAAE;QAClC,MAAM,IAAI,GAAG,CAAC,GAAG,cAAc,CAAC;QAChC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACpD,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QAC5B,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC7B,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEnB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACrC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAE/B,KAAK,CAAC,SAAS,EAAE,CAAC;QAClB,CAAC,CAAC,SAAS,EAAE,CAAC;QAEd,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC;YACjC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,eAAe,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;YACjD,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAE9B,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/C,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/C,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAE/C,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC;YAC7B,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC;SAC9G;KACJ;IAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;QACjC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC;YACxC,MAAM,CAAC,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,CAAC;YAClC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe,GAAG,CAAC,CAAC;YACxC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe,GAAG,KAAK,CAAC;YAC5C,MAAM,CAAC,GAAG,CAAC,GAAG,eAAe,GAAG,KAAK,CAAC;YAEtC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACnB;KACJ;IAED,UAAU;IACV,UAAU,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAEvD,QAAQ;IACR,UAAU,CAAC,aAAa,CAAC,eAAe,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IAE/G,SAAS;IACT,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IAEpC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;IACjC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;IAErB,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,UAAU,eAAe,CAC3B,IAAY,EACZ,UAWI,EAAE,EACN,KAAa;IAEb,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAExC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACnF,SAAS,CAAC,+BAA+B,GAAG,OAAO,CAAC,eAAe,CAAC;IAEpE,MAAM,UAAU,GAAG,yBAAyB,CAAC,OAAO,CAAC,CAAC;IAEtD,UAAU,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAErD,OAAO,SAAS,CAAC;AACrB,CAAC;AACD;;;GAGG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG;IAC5B,gEAAgE;IAChE,eAAe;CAClB,CAAC;AAEF,UAAU,CAAC,eAAe,GAAG,yBAAyB,CAAC;AAEvD,IAAI,CAAC,eAAe,GAAG,CACnB,IAAY,EACZ,MAAc,EACd,IAAY,EACZ,cAAsB,EACtB,eAAuB,EACvB,CAAS,EACT,CAAS,EACT,KAAa,EACb,SAAmB,EACnB,eAAwB,EACpB,EAAE;IACN,MAAM,OAAO,GAAG;QACZ,MAAM;QACN,IAAI;QACJ,cAAc;QACd,eAAe;QACf,CAAC;QACD,CAAC;QACD,eAAe;QACf,SAAS;KACZ,CAAC;IAEF,OAAO,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AACjD,CAAC,CAAC", "sourcesContent": ["import type { Vector4 } from \"../../Maths/math.vector\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { Mesh } from \"../mesh\";\r\nimport { VertexData } from \"../mesh.vertexData\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { CompatibilityOptions } from \"../../Compat/compatibilityOptions\";\r\n\r\n// based on http://code.google.com/p/away3d/source/browse/trunk/fp10/Away3D/src/away3d/primitives/TorusKnot.as?spec=svn2473&r=2473\r\n/**\r\n * Creates the VertexData for a TorusKnot\r\n * @param options an object used to set the following optional parameters for the TorusKnot, required but can be empty\r\n * * radius the radius of the torus knot, optional, default 2\r\n * * tube the thickness of the tube, optional, default 0.5\r\n * * radialSegments the number of sides on each tube segments, optional, default 32\r\n * * tubularSegments the number of tubes to decompose the knot into, optional, default 32\r\n * * p the number of windings around the z axis, optional,  default 2\r\n * * q the number of windings around the x axis, optional,  default 3\r\n * * sideOrientation optional and takes the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n * * frontUvs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the front side, optional, default vector4 (0, 0, 1, 1)\r\n * * backUVs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the back side, optional, default vector4 (0, 0, 1, 1)\r\n * @param options.radius\r\n * @param options.tube\r\n * @param options.radialSegments\r\n * @param options.tubularSegments\r\n * @param options.p\r\n * @param options.q\r\n * @param options.sideOrientation\r\n * @param options.frontUVs\r\n * @param options.backUVs\r\n * @returns the VertexData of the Torus Knot\r\n */\r\nexport function CreateTorusKnotVertexData(options: {\r\n    radius?: number;\r\n    tube?: number;\r\n    radialSegments?: number;\r\n    tubularSegments?: number;\r\n    p?: number;\r\n    q?: number;\r\n    sideOrientation?: number;\r\n    frontUVs?: Vector4;\r\n    backUVs?: Vector4;\r\n}): VertexData {\r\n    const indices: number[] = [];\r\n    const positions: number[] = [];\r\n    const normals: number[] = [];\r\n    const uvs: number[] = [];\r\n\r\n    const radius = options.radius || 2;\r\n    const tube = options.tube || 0.5;\r\n    const radialSegments = options.radialSegments || 32;\r\n    const tubularSegments = options.tubularSegments || 32;\r\n    const p = options.p || 2;\r\n    const q = options.q || 3;\r\n    const sideOrientation = options.sideOrientation === 0 ? 0 : options.sideOrientation || VertexData.DEFAULTSIDE;\r\n\r\n    // Helper\r\n    const getPos = (angle: number) => {\r\n        const cu = Math.cos(angle);\r\n        const su = Math.sin(angle);\r\n        const quOverP = (q / p) * angle;\r\n        const cs = Math.cos(quOverP);\r\n\r\n        const tx = radius * (2 + cs) * 0.5 * cu;\r\n        const ty = radius * (2 + cs) * su * 0.5;\r\n        const tz = radius * Math.sin(quOverP) * 0.5;\r\n\r\n        return new Vector3(tx, ty, tz);\r\n    };\r\n\r\n    // Vertices\r\n    let i: number;\r\n    let j: number;\r\n    for (i = 0; i <= radialSegments; i++) {\r\n        const modI = i % radialSegments;\r\n        const u = (modI / radialSegments) * 2 * p * Math.PI;\r\n        const p1 = getPos(u);\r\n        const p2 = getPos(u + 0.01);\r\n        const tang = p2.subtract(p1);\r\n        let n = p2.add(p1);\r\n\r\n        const bitan = Vector3.Cross(tang, n);\r\n        n = Vector3.Cross(bitan, tang);\r\n\r\n        bitan.normalize();\r\n        n.normalize();\r\n\r\n        for (j = 0; j < tubularSegments; j++) {\r\n            const modJ = j % tubularSegments;\r\n            const v = (modJ / tubularSegments) * 2 * Math.PI;\r\n            const cx = -tube * Math.cos(v);\r\n            const cy = tube * Math.sin(v);\r\n\r\n            positions.push(p1.x + cx * n.x + cy * bitan.x);\r\n            positions.push(p1.y + cx * n.y + cy * bitan.y);\r\n            positions.push(p1.z + cx * n.z + cy * bitan.z);\r\n\r\n            uvs.push(i / radialSegments);\r\n            uvs.push(CompatibilityOptions.UseOpenGLOrientationForUV ? 1.0 - j / tubularSegments : j / tubularSegments);\r\n        }\r\n    }\r\n\r\n    for (i = 0; i < radialSegments; i++) {\r\n        for (j = 0; j < tubularSegments; j++) {\r\n            const jNext = (j + 1) % tubularSegments;\r\n            const a = i * tubularSegments + j;\r\n            const b = (i + 1) * tubularSegments + j;\r\n            const c = (i + 1) * tubularSegments + jNext;\r\n            const d = i * tubularSegments + jNext;\r\n\r\n            indices.push(d);\r\n            indices.push(b);\r\n            indices.push(a);\r\n            indices.push(d);\r\n            indices.push(c);\r\n            indices.push(b);\r\n        }\r\n    }\r\n\r\n    // Normals\r\n    VertexData.ComputeNormals(positions, indices, normals);\r\n\r\n    // Sides\r\n    VertexData._ComputeSides(sideOrientation, positions, indices, normals, uvs, options.frontUVs, options.backUVs);\r\n\r\n    // Result\r\n    const vertexData = new VertexData();\r\n\r\n    vertexData.indices = indices;\r\n    vertexData.positions = positions;\r\n    vertexData.normals = normals;\r\n    vertexData.uvs = uvs;\r\n\r\n    return vertexData;\r\n}\r\n\r\n/**\r\n * Creates a torus knot mesh\r\n * * The parameter `radius` sets the global radius size (float) of the torus knot (default 2)\r\n * * The parameter `radialSegments` sets the number of sides on each tube segments (positive integer, default 32)\r\n * * The parameter `tubularSegments` sets the number of tubes to decompose the knot into (positive integer, default 32)\r\n * * The parameters `p` and `q` are the number of windings on each axis (positive integers, default 2 and 3)\r\n * * You can also set the mesh side orientation with the values : BABYLON.Mesh.FRONTSIDE (default), BABYLON.Mesh.BACKSIDE or BABYLON.Mesh.DOUBLESIDE\r\n * * If you create a double-sided mesh, you can choose what parts of the texture image to crop and stick respectively on the front and the back sides with the parameters `frontUVs` and `backUVs` (Vector4). Detail here : https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation\r\n * * The mesh can be set to updatable with the boolean parameter `updatable` (default false) if its internal geometry is supposed to change once created.\r\n * @param name defines the name of the mesh\r\n * @param options defines the options used to create the mesh\r\n * @param options.radius\r\n * @param options.tube\r\n * @param options.radialSegments\r\n * @param options.tubularSegments\r\n * @param options.p\r\n * @param options.q\r\n * @param options.updatable\r\n * @param options.sideOrientation\r\n * @param options.frontUVs\r\n * @param options.backUVs\r\n * @param scene defines the hosting scene\r\n * @returns the torus knot mesh\r\n * @see  https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#torus-knot\r\n */\r\nexport function CreateTorusKnot(\r\n    name: string,\r\n    options: {\r\n        radius?: number;\r\n        tube?: number;\r\n        radialSegments?: number;\r\n        tubularSegments?: number;\r\n        p?: number;\r\n        q?: number;\r\n        updatable?: boolean;\r\n        sideOrientation?: number;\r\n        frontUVs?: Vector4;\r\n        backUVs?: Vector4;\r\n    } = {},\r\n    scene?: Scene\r\n): Mesh {\r\n    const torusKnot = new Mesh(name, scene);\r\n\r\n    options.sideOrientation = Mesh._GetDefaultSideOrientation(options.sideOrientation);\r\n    torusKnot._originalBuilderSideOrientation = options.sideOrientation;\r\n\r\n    const vertexData = CreateTorusKnotVertexData(options);\r\n\r\n    vertexData.applyToMesh(torusKnot, options.updatable);\r\n\r\n    return torusKnot;\r\n}\r\n/**\r\n * Class containing static functions to help procedurally build meshes\r\n * @deprecated use CreateTorusKnot instead\r\n */\r\nexport const TorusKnotBuilder = {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    CreateTorusKnot,\r\n};\r\n\r\nVertexData.CreateTorusKnot = CreateTorusKnotVertexData;\r\n\r\nMesh.CreateTorusKnot = (\r\n    name: string,\r\n    radius: number,\r\n    tube: number,\r\n    radialSegments: number,\r\n    tubularSegments: number,\r\n    p: number,\r\n    q: number,\r\n    scene?: Scene,\r\n    updatable?: boolean,\r\n    sideOrientation?: number\r\n): Mesh => {\r\n    const options = {\r\n        radius,\r\n        tube,\r\n        radialSegments,\r\n        tubularSegments,\r\n        p,\r\n        q,\r\n        sideOrientation,\r\n        updatable,\r\n    };\r\n\r\n    return CreateTorusKnot(name, options, scene);\r\n};\r\n"]}