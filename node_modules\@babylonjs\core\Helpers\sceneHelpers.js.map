{"version": 3, "file": "sceneHelpers.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Helpers/sceneHelpers.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAG/C,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAC3D,OAAO,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAE9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;AAG7D,OAAO,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AAEtE,OAAO,gDAAgD,CAAC;AACxD,OAAO,gDAAgD,CAAC;AACxD,OAAO,gDAAgD,CAAC;AACxD,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAE1D,OAAO,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;AAEtE,gBAAgB;AAChB,kCAAkC;AAClC,MAAM,CAAC,IAAI,0BAA0B,GAAG,IAAI,CAAC;AAoE7C,KAAK,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,OAAO,GAAG,KAAK;IAC1D,0CAA0C;IAC1C,IAAI,OAAO,EAAE;QACT,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;aAC5B;SACJ;KACJ;IAED,QAAQ;IACR,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAC1B,IAAI,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;KAC7D;AACL,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,qBAAqB,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,oBAAoB,GAAG,KAAK;IACxH,2CAA2C;IAC3C,IAAI,OAAO,EAAE;QACT,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;KACJ;IAED,SAAS;IACT,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;QACpB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACxF,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAC9D,MAAM,WAAW,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAE/D,IAAI,MAAoB,CAAC;QACzB,IAAI,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;QACtC,wBAAwB;QACxB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACnB,MAAM,GAAG,CAAC,CAAC;YACX,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACvC;QACD,IAAI,qBAAqB,EAAE;YACvB,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;YACtH,eAAe,CAAC,gBAAgB,GAAG,MAAM,GAAG,IAAI,CAAC;YACjD,eAAe,CAAC,cAAc,GAAG,GAAG,GAAG,MAAM,CAAC;YAC9C,MAAM,GAAG,eAAe,CAAC;SAC5B;aAAM;YACH,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,gBAAgB,EAAE,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;YAC9G,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAClC,MAAM,GAAG,UAAU,CAAC;SACvB;QACD,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;QAC5B,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;QAC5B,MAAM,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAE3B,IAAI,oBAAoB,EAAE;YACtB,MAAM,CAAC,aAAa,EAAE,CAAC;SAC1B;KACJ;AACL,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,0BAA0B,GAAG,UAAU,qBAAqB,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,oBAAoB,GAAG,KAAK;IAC/H,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACjC,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,EAAE,OAAO,EAAE,oBAAoB,CAAC,CAAC;AACnF,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,kBAAgC,EAAE,GAAG,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC,EAAE,mBAAmB,GAAG,IAAI;IAC7I,IAAI,CAAC,kBAAkB,EAAE;QACrB,MAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC;KACf;IAED,IAAI,mBAAmB,EAAE;QACrB,IAAI,kBAAkB,EAAE;YACpB,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;SAChD;KACJ;IAED,SAAS;IACT,MAAM,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC;IAChE,IAAI,GAAG,EAAE;QACL,MAAM,iBAAiB,GAAG,IAAI,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC1D,iBAAiB,CAAC,eAAe,GAAG,KAAK,CAAC;QAC1C,iBAAiB,CAAC,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,EAAE,CAAC;QACjE,IAAI,iBAAiB,CAAC,iBAAiB,EAAE;YACrC,iBAAiB,CAAC,iBAAiB,CAAC,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC;SAC7E;QACD,iBAAiB,CAAC,YAAY,GAAG,GAAG,GAAG,IAAI,CAAC;QAC5C,iBAAiB,CAAC,eAAe,GAAG,IAAI,CAAC;QACzC,iBAAiB,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC1C,SAAS,CAAC,QAAQ,GAAG,iBAAiB,CAAC;KAC1C;SAAM;QACH,MAAM,cAAc,GAAG,IAAI,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC5D,cAAc,CAAC,eAAe,GAAG,KAAK,CAAC;QACvC,cAAc,CAAC,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAC9D,IAAI,cAAc,CAAC,iBAAiB,EAAE;YAClC,cAAc,CAAC,iBAAiB,CAAC,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC;SAC1E;QACD,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC;QACtC,SAAS,CAAC,QAAQ,GAAG,cAAc,CAAC;KACvC;IACD,SAAS,CAAC,UAAU,GAAG,KAAK,CAAC;IAC7B,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAClC,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAClC,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAAU,OAA2C;IAC5F,IAAI,iBAAiB,EAAE;QACnB,OAAO,IAAI,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;KAC/C;IACD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,eAA0C,EAAE;IAC9F,OAAO,IAAI,kBAAkB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;AACtD,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,8BAA8B,GAAG,UAAU,UAAyC,EAAE;IAClG,OAAO,sBAAsB,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;QACrE,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC", "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Scene } from \"../scene\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport { PBRMaterial } from \"../Materials/PBR/pbrMaterial\";\r\nimport { HemisphericLight } from \"../Lights/hemisphericLight\";\r\nimport type { IEnvironmentHelperOptions } from \"./environmentHelper\";\r\nimport { EnvironmentHelper } from \"./environmentHelper\";\r\nimport { FreeCamera } from \"../Cameras/freeCamera\";\r\nimport { ArcRotateCamera } from \"../Cameras/arcRotateCamera\";\r\nimport type { TargetCamera } from \"../Cameras/targetCamera\";\r\nimport type { VRExperienceHelperOptions } from \"../Cameras/VR/vrExperienceHelper\";\r\nimport { VRExperienceHelper } from \"../Cameras/VR/vrExperienceHelper\";\r\n\r\nimport \"../Materials/Textures/Loaders/ddsTextureLoader\";\r\nimport \"../Materials/Textures/Loaders/envTextureLoader\";\r\nimport \"../Materials/Textures/Loaders/ktxTextureLoader\";\r\nimport { CreateBox } from \"../Meshes/Builders/boxBuilder\";\r\nimport type { WebXRDefaultExperienceOptions } from \"../XR/webXRDefaultExperience\";\r\nimport { WebXRDefaultExperience } from \"../XR/webXRDefaultExperience\";\r\n\r\n/** @internal */\r\n// eslint-disable-next-line no-var\r\nexport var _forceSceneHelpersToBundle = true;\r\n\r\ndeclare module \"../scene\" {\r\n    export interface Scene {\r\n        /**\r\n         * Creates a default light for the scene.\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/fastBuildWorld#create-default-light\r\n         * @param replace has the default false, when true replaces the existing lights in the scene with a hemispheric light\r\n         */\r\n        createDefaultLight(replace?: boolean): void;\r\n\r\n        /**\r\n         * Creates a default camera for the scene.\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/fastBuildWorld#create-default-camera\r\n         * @param createArcRotateCamera has the default false which creates a free camera, when true creates an arc rotate camera\r\n         * @param replace has default false, when true replaces the active camera in the scene\r\n         * @param attachCameraControls has default false, when true attaches camera controls to the canvas.\r\n         */\r\n        createDefaultCamera(createArcRotateCamera?: boolean, replace?: boolean, attachCameraControls?: boolean): void;\r\n\r\n        /**\r\n         * Creates a default camera and a default light.\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/fastBuildWorld#create-default-camera-or-light\r\n         * @param createArcRotateCamera has the default false which creates a free camera, when true creates an arc rotate camera\r\n         * @param replace has the default false, when true replaces the active camera/light in the scene\r\n         * @param attachCameraControls has the default false, when true attaches camera controls to the canvas.\r\n         */\r\n        createDefaultCameraOrLight(createArcRotateCamera?: boolean, replace?: boolean, attachCameraControls?: boolean): void;\r\n\r\n        /**\r\n         * Creates a new sky box\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/fastBuildWorld#create-default-skybox\r\n         * @param environmentTexture defines the texture to use as environment texture\r\n         * @param pbr has default false which requires the StandardMaterial to be used, when true PBRMaterial must be used\r\n         * @param scale defines the overall scale of the skybox\r\n         * @param blur is only available when pbr is true, default is 0, no blur, maximum value is 1\r\n         * @param setGlobalEnvTexture has default true indicating that scene.environmentTexture must match the current skybox texture\r\n         * @returns a new mesh holding the sky box\r\n         */\r\n        createDefaultSkybox(environmentTexture?: BaseTexture, pbr?: boolean, scale?: number, blur?: number, setGlobalEnvTexture?: boolean): Nullable<Mesh>;\r\n\r\n        /**\r\n         * Creates a new environment\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/fastBuildWorld#create-default-environment\r\n         * @param options defines the options you can use to configure the environment\r\n         * @returns the new EnvironmentHelper\r\n         */\r\n        createDefaultEnvironment(options?: Partial<IEnvironmentHelperOptions>): Nullable<EnvironmentHelper>;\r\n\r\n        /**\r\n         * Creates a new VREXperienceHelper\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/webVRHelper\r\n         * @param webVROptions defines the options used to create the new VREXperienceHelper\r\n         * @deprecated Please use createDefaultXRExperienceAsync instead\r\n         * @returns a new VREXperienceHelper\r\n         */\r\n        createDefaultVRExperience(webVROptions?: VRExperienceHelperOptions): VRExperienceHelper;\r\n\r\n        /**\r\n         * Creates a new WebXRDefaultExperience\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/webXR/introToWebXR\r\n         * @param options experience options\r\n         * @returns a promise for a new WebXRDefaultExperience\r\n         */\r\n        createDefaultXRExperienceAsync(options?: WebXRDefaultExperienceOptions): Promise<WebXRDefaultExperience>;\r\n    }\r\n}\r\n\r\nScene.prototype.createDefaultLight = function (replace = false): void {\r\n    // Dispose existing light in replace mode.\r\n    if (replace) {\r\n        if (this.lights) {\r\n            for (let i = 0; i < this.lights.length; i++) {\r\n                this.lights[i].dispose();\r\n            }\r\n        }\r\n    }\r\n\r\n    // Light\r\n    if (this.lights.length === 0) {\r\n        new HemisphericLight(\"default light\", Vector3.Up(), this);\r\n    }\r\n};\r\n\r\nScene.prototype.createDefaultCamera = function (createArcRotateCamera = false, replace = false, attachCameraControls = false): void {\r\n    // Dispose existing camera in replace mode.\r\n    if (replace) {\r\n        if (this.activeCamera) {\r\n            this.activeCamera.dispose();\r\n            this.activeCamera = null;\r\n        }\r\n    }\r\n\r\n    // Camera\r\n    if (!this.activeCamera) {\r\n        const worldExtends = this.getWorldExtends((mesh) => mesh.isVisible && mesh.isEnabled());\r\n        const worldSize = worldExtends.max.subtract(worldExtends.min);\r\n        const worldCenter = worldExtends.min.add(worldSize.scale(0.5));\r\n\r\n        let camera: TargetCamera;\r\n        let radius = worldSize.length() * 1.5;\r\n        // empty scene scenario!\r\n        if (!isFinite(radius)) {\r\n            radius = 1;\r\n            worldCenter.copyFromFloats(0, 0, 0);\r\n        }\r\n        if (createArcRotateCamera) {\r\n            const arcRotateCamera = new ArcRotateCamera(\"default camera\", -(Math.PI / 2), Math.PI / 2, radius, worldCenter, this);\r\n            arcRotateCamera.lowerRadiusLimit = radius * 0.01;\r\n            arcRotateCamera.wheelPrecision = 100 / radius;\r\n            camera = arcRotateCamera;\r\n        } else {\r\n            const freeCamera = new FreeCamera(\"default camera\", new Vector3(worldCenter.x, worldCenter.y, -radius), this);\r\n            freeCamera.setTarget(worldCenter);\r\n            camera = freeCamera;\r\n        }\r\n        camera.minZ = radius * 0.01;\r\n        camera.maxZ = radius * 1000;\r\n        camera.speed = radius * 0.2;\r\n        this.activeCamera = camera;\r\n\r\n        if (attachCameraControls) {\r\n            camera.attachControl();\r\n        }\r\n    }\r\n};\r\n\r\nScene.prototype.createDefaultCameraOrLight = function (createArcRotateCamera = false, replace = false, attachCameraControls = false): void {\r\n    this.createDefaultLight(replace);\r\n    this.createDefaultCamera(createArcRotateCamera, replace, attachCameraControls);\r\n};\r\n\r\nScene.prototype.createDefaultSkybox = function (environmentTexture?: BaseTexture, pbr = false, scale = 1000, blur = 0, setGlobalEnvTexture = true): Nullable<Mesh> {\r\n    if (!environmentTexture) {\r\n        Logger.Warn(\"Can not create default skybox without environment texture.\");\r\n        return null;\r\n    }\r\n\r\n    if (setGlobalEnvTexture) {\r\n        if (environmentTexture) {\r\n            this.environmentTexture = environmentTexture;\r\n        }\r\n    }\r\n\r\n    // Skybox\r\n    const hdrSkybox = CreateBox(\"hdrSkyBox\", { size: scale }, this);\r\n    if (pbr) {\r\n        const hdrSkyboxMaterial = new PBRMaterial(\"skyBox\", this);\r\n        hdrSkyboxMaterial.backFaceCulling = false;\r\n        hdrSkyboxMaterial.reflectionTexture = environmentTexture.clone();\r\n        if (hdrSkyboxMaterial.reflectionTexture) {\r\n            hdrSkyboxMaterial.reflectionTexture.coordinatesMode = Texture.SKYBOX_MODE;\r\n        }\r\n        hdrSkyboxMaterial.microSurface = 1.0 - blur;\r\n        hdrSkyboxMaterial.disableLighting = true;\r\n        hdrSkyboxMaterial.twoSidedLighting = true;\r\n        hdrSkybox.material = hdrSkyboxMaterial;\r\n    } else {\r\n        const skyboxMaterial = new StandardMaterial(\"skyBox\", this);\r\n        skyboxMaterial.backFaceCulling = false;\r\n        skyboxMaterial.reflectionTexture = environmentTexture.clone();\r\n        if (skyboxMaterial.reflectionTexture) {\r\n            skyboxMaterial.reflectionTexture.coordinatesMode = Texture.SKYBOX_MODE;\r\n        }\r\n        skyboxMaterial.disableLighting = true;\r\n        hdrSkybox.material = skyboxMaterial;\r\n    }\r\n    hdrSkybox.isPickable = false;\r\n    hdrSkybox.infiniteDistance = true;\r\n    hdrSkybox.ignoreCameraMaxZ = true;\r\n    return hdrSkybox;\r\n};\r\n\r\nScene.prototype.createDefaultEnvironment = function (options: Partial<IEnvironmentHelperOptions>): Nullable<EnvironmentHelper> {\r\n    if (EnvironmentHelper) {\r\n        return new EnvironmentHelper(options, this);\r\n    }\r\n    return null;\r\n};\r\n\r\nScene.prototype.createDefaultVRExperience = function (webVROptions: VRExperienceHelperOptions = {}): VRExperienceHelper {\r\n    return new VRExperienceHelper(this, webVROptions);\r\n};\r\n\r\nScene.prototype.createDefaultXRExperienceAsync = function (options: WebXRDefaultExperienceOptions = {}): Promise<WebXRDefaultExperience> {\r\n    return WebXRDefaultExperience.CreateAsync(this, options).then((helper) => {\r\n        return helper;\r\n    });\r\n};\r\n"]}