{"version": 3, "file": "scaleGizmo.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Gizmos/scaleGizmo.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE/C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,gBAAgB,EAAE,MAAM,sCAAsC,CAAC;AAExE,OAAO,EAAE,oBAAoB,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEtD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAIzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AA4DjE;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,KAAK;IAkCjC,4EAA4E;IAC5E,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,8DAA8D;IAC9D,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,qEAAqE;IACrE,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAQD,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAW,YAAY,CAAC,IAA4B;QAChD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9E,IAAI,KAAK,CAAC,SAAS,EAAE;gBACjB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;aAC7B;iBAAM;gBACH,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAW,YAAY,CAAC,IAAoB;QACxC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9E,IAAI,KAAK,CAAC,SAAS,EAAE;gBACjB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;aAC7B;iBAAM;gBACH,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,IAAW,WAAW,CAAC,KAAc;QACjC,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;SACnC;IACL,CAAC;IACD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACnC,CAAC;IACD;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC;IACvK,CAAC;IAED,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED,IAAW,uBAAuB,CAAC,aAAwC;QACvE,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9E,KAAK,CAAC,uBAAuB,GAAG,aAAa,CAAC;QAClD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACH,YAAY,aAAmC,oBAAoB,CAAC,mBAAmB,EAAE,YAAoB,CAAC,EAAE,YAA2B,EAAE,OAA2B;QACpK,KAAK,CAAC,UAAU,CAAC,CAAC;QA5GZ,kBAAa,GAA2B,IAAI,CAAC;QAC7C,kBAAa,GAAmB,IAAI,CAAC;QAErC,qBAAgB,GAAY,KAAK,CAAC;QAGlC,iBAAY,GAAW,CAAC,CAAC;QAIzB,iBAAY,GAA4B,EAAE,CAAC;QAErD,oCAAoC;QAC1B,oBAAe,GAA8B,IAAI,GAAG,EAAE,CAAC;QAgBjE,6DAA6D;QACtD,0BAAqB,GAAG,IAAI,UAAU,EAAE,CAAC;QAChD,mEAAmE;QAC5D,qBAAgB,GAAG,IAAI,UAAU,EAAE,CAAC;QAC3C,4EAA4E;QACrE,wBAAmB,GAAG,IAAI,UAAU,EAAE,CAAC;QA2E1C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACxD,IAAI,CAAC,MAAM,GAAG,IAAI,cAAc,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAC7G,IAAI,CAAC,MAAM,GAAG,IAAI,cAAc,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAC/G,IAAI,CAAC,MAAM,GAAG,IAAI,cAAc,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAE9G,IAAI,CAAC,uBAAuB,GAAG,OAAO,EAAE,uBAAuB,CAAC;QAEhE,oBAAoB;QACpB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9E,KAAK,CAAC,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC9C,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YACH,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACzC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YACH,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC5C,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,YAAY,EAAE;YACd,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACrD;aAAM;YACH,uDAAuD;YACvD,KAAK,CAAC,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;SACpE;IACL,CAAC;IAED;;;OAGG;IACO,uBAAuB;QAC7B,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACpF,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAEnD,IAAI,CAAC,cAAc,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAClF,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;QAEnD,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACpF,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QACnD,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,GAAG,CAAC;QAElC,MAAM,iBAAiB,GAAG,IAAI,cAAc,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACpH,iBAAiB,CAAC,sCAAsC,GAAG,KAAK,CAAC;QACjE,iBAAiB,CAAC,cAAc,GAAG,IAAI,CAAC;QACxC,IAAI,CAAC,mBAAmB,GAAG,gBAAgB,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,iBAAiB,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACpH,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,mBAAmB,CAAC,UAAU,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,WAAW,GAAG,gBAAgB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,iBAAiB,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACrG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpD,iBAAiB,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QAChE,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;QACrD,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE7E,MAAM,KAAK,GAAmB;YAC1B,WAAW,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC;YACzD,cAAc,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC;YAC1C,QAAQ,EAAE,IAAI,CAAC,gBAAgB;YAC/B,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,MAAM,EAAE,KAAK;YACb,YAAY,EAAE,iBAAiB,CAAC,YAAY;SAC/C,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAExD,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED,IAAW,sCAAsC,CAAC,KAAc;QAC5D,IAAI,CAAC,KAAK,EAAE;YACR,MAAM,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAC;SAC5G;aAAM;YACH,IAAI,CAAC,uCAAuC,GAAG,KAAK,CAAC;YACrD,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC9E,IAAI,KAAK,EAAE;oBACP,KAAK,CAAC,sCAAsC,GAAG,KAAK,CAAC;iBACxD;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IACD,IAAW,sCAAsC;QAC7C,OAAO,IAAI,CAAC,uCAAuC,CAAC;IACxD,CAAC;IAED,IAAW,WAAW,CAAC,KAAuB;QAC1C,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9E,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACH,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IAED,IAAW,wBAAwB,CAAC,wBAA8C;QAC9E,IAAI,CAAC,yBAAyB,GAAG,wBAAwB,CAAC;QAC1D,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9E,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;aAC7D;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACH,IAAW,eAAe,CAAC,eAAqC;QAC5D,IAAI,eAAe,IAAI,oBAAoB,CAAC,KAAK,EAAE;YAC/C,MAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;SACvF;QACD,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9E,KAAK,CAAC,eAAe,GAAG,oBAAoB,CAAC,KAAK,CAAC;QACvD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,IAAW,YAAY,CAAC,KAAa;QACjC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9E,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;aAC9B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,eAAe,CAAC,KAAc;QACrC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9E,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;aACjC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IACD;;OAEG;IACH,IAAW,UAAU,CAAC,KAAa;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9E,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;aAC5B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9E,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,IAAU,EAAE,KAAqB;QACnD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,WAAW;QACd,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,OAAO;QACV,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9E,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,OAAO,EAAE,CAAC;aACnB;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YAC9B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACzD,IAAI,GAAG,EAAE;gBACL,GAAG,CAAC,OAAO,EAAE,CAAC;aACjB;QACL,CAAC,CAAC,CAAC;QACH,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACjF,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,OAAO,EAAE,CAAC;aAClB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;CACJ", "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Quaternion } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { CreatePolyhedron } from \"../Meshes/Builders/polyhedronBuilder\";\r\nimport type { GizmoAnchorPoint, GizmoAxisCache, IGizmo } from \"./gizmo\";\r\nimport { GizmoCoordinatesMode, Gizmo } from \"./gizmo\";\r\nimport type { IAxisScaleGizmo } from \"./axisScaleGizmo\";\r\nimport { AxisScaleGizmo } from \"./axisScaleGizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { Node } from \"../node\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport type { GizmoManager } from \"./gizmoManager\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\n\r\n/**\r\n * Interface for scale gizmo\r\n */\r\nexport interface IScaleGizmo extends IGizmo {\r\n    /** True when the mouse pointer is dragging a gizmo mesh */\r\n    readonly isDragging: boolean;\r\n    /** Internal gizmo used for interactions on the x axis */\r\n    xGizmo: IAxisScaleGizmo;\r\n    /** Internal gizmo used for interactions on the y axis */\r\n    yGizmo: IAxisScaleGizmo;\r\n    /** Internal gizmo used for interactions on the z axis */\r\n    zGizmo: IAxisScaleGizmo;\r\n    /** Internal gizmo used to scale all axis equally*/\r\n    uniformScaleGizmo: IAxisScaleGizmo;\r\n    /** Fires an event when any of it's sub gizmos are dragged */\r\n    onDragStartObservable: Observable<unknown>;\r\n    /** Fires an event when any of it's sub gizmos are being dragged */\r\n    onDragObservable: Observable<unknown>;\r\n    /** Fires an event when any of it's sub gizmos are released from dragging */\r\n    onDragEndObservable: Observable<unknown>;\r\n    /** Drag distance in babylon units that the gizmo will snap to when dragged */\r\n    snapDistance: number;\r\n    /** Incremental snap scaling. When true, with a snapDistance of 0.1, scaling will be 1.1,1.2,1.3 instead of, when false: 1.1,1.21,1.33,... */\r\n    incrementalSnap: boolean;\r\n    /** Sensitivity factor for dragging */\r\n    sensitivity: number;\r\n    /**\r\n     * Builds Gizmo Axis Cache to enable features such as hover state preservation and graying out other axis during manipulation\r\n     * @param mesh Axis gizmo mesh\r\n     * @param cache Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    addToAxisCache(mesh: Mesh, cache: GizmoAxisCache): void;\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    releaseDrag(): void;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    coloredMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is hovered with mouse*/\r\n    hoverMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is disabled. typically grey.*/\r\n    disableMaterial: StandardMaterial;\r\n}\r\n\r\n/**\r\n * Additional options for the scale gizmo\r\n */\r\nexport interface ScaleGizmoOptions {\r\n    /**\r\n     * Additional transform applied to the gizmo.\r\n     * @See Gizmo.additionalTransformNode for more detail\r\n     */\r\n    additionalTransformNode?: TransformNode;\r\n}\r\n\r\n/**\r\n * Gizmo that enables scaling a mesh along 3 axis\r\n */\r\nexport class ScaleGizmo extends Gizmo implements IScaleGizmo {\r\n    /**\r\n     * Internal gizmo used for interactions on the x axis\r\n     */\r\n    public xGizmo: IAxisScaleGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the y axis\r\n     */\r\n    public yGizmo: IAxisScaleGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the z axis\r\n     */\r\n    public zGizmo: IAxisScaleGizmo;\r\n\r\n    /**\r\n     * Internal gizmo used to scale all axis equally\r\n     */\r\n    public uniformScaleGizmo: IAxisScaleGizmo;\r\n\r\n    protected _meshAttached: Nullable<AbstractMesh> = null;\r\n    protected _nodeAttached: Nullable<Node> = null;\r\n    protected _snapDistance: number;\r\n    protected _incrementalSnap: boolean = false;\r\n    protected _uniformScalingMesh: Mesh;\r\n    protected _octahedron: Mesh;\r\n    protected _sensitivity: number = 1;\r\n    protected _coloredMaterial: StandardMaterial;\r\n    protected _hoverMaterial: StandardMaterial;\r\n    protected _disableMaterial: StandardMaterial;\r\n    protected _observables: Observer<PointerInfo>[] = [];\r\n\r\n    /** Node Caching for quick lookup */\r\n    protected _gizmoAxisCache: Map<Mesh, GizmoAxisCache> = new Map();\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    public get coloredMaterial() {\r\n        return this._coloredMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is hovered with mouse*/\r\n    public get hoverMaterial() {\r\n        return this._hoverMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is disabled. typically grey.*/\r\n    public get disableMaterial() {\r\n        return this._disableMaterial;\r\n    }\r\n    /** Fires an event when any of it's sub gizmos are dragged */\r\n    public onDragStartObservable = new Observable();\r\n    /** Fires an event when any of it's sub gizmos are being dragged */\r\n    public onDragObservable = new Observable();\r\n    /** Fires an event when any of it's sub gizmos are released from dragging */\r\n    public onDragEndObservable = new Observable();\r\n\r\n    public get attachedMesh() {\r\n        return this._meshAttached;\r\n    }\r\n    public set attachedMesh(mesh: Nullable<AbstractMesh>) {\r\n        this._meshAttached = mesh;\r\n        this._nodeAttached = mesh;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo].forEach((gizmo) => {\r\n            if (gizmo.isEnabled) {\r\n                gizmo.attachedMesh = mesh;\r\n            } else {\r\n                gizmo.attachedMesh = null;\r\n            }\r\n        });\r\n    }\r\n\r\n    public get attachedNode() {\r\n        return this._nodeAttached;\r\n    }\r\n    public set attachedNode(node: Nullable<Node>) {\r\n        this._meshAttached = null;\r\n        this._nodeAttached = node;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo].forEach((gizmo) => {\r\n            if (gizmo.isEnabled) {\r\n                gizmo.attachedNode = node;\r\n            } else {\r\n                gizmo.attachedNode = null;\r\n            }\r\n        });\r\n    }\r\n\r\n    public set updateScale(value: boolean) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.updateScale = value;\r\n            this.yGizmo.updateScale = value;\r\n            this.zGizmo.updateScale = value;\r\n        }\r\n    }\r\n    public get updateScale() {\r\n        return this.xGizmo.updateScale;\r\n    }\r\n    /**\r\n     * True when the mouse pointer is hovering a gizmo mesh\r\n     */\r\n    public get isHovered() {\r\n        return this.xGizmo.isHovered || this.yGizmo.isHovered || this.zGizmo.isHovered;\r\n    }\r\n\r\n    /**\r\n     * True when the mouse pointer is dragging a gizmo mesh\r\n     */\r\n    public get isDragging() {\r\n        return this.xGizmo.dragBehavior.dragging || this.yGizmo.dragBehavior.dragging || this.zGizmo.dragBehavior.dragging || this.uniformScaleGizmo.dragBehavior.dragging;\r\n    }\r\n\r\n    public get additionalTransformNode() {\r\n        return this._additionalTransformNode;\r\n    }\r\n\r\n    public set additionalTransformNode(transformNode: TransformNode | undefined) {\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo].forEach((gizmo) => {\r\n            gizmo.additionalTransformNode = transformNode;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a ScaleGizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param thickness display gizmo axis thickness\r\n     * @param gizmoManager\r\n     * @param options More options\r\n     */\r\n    constructor(gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer, thickness: number = 1, gizmoManager?: GizmoManager, options?: ScaleGizmoOptions) {\r\n        super(gizmoLayer);\r\n        this.uniformScaleGizmo = this._createUniformScaleMesh();\r\n        this.xGizmo = new AxisScaleGizmo(new Vector3(1, 0, 0), Color3.Red().scale(0.5), gizmoLayer, this, thickness);\r\n        this.yGizmo = new AxisScaleGizmo(new Vector3(0, 1, 0), Color3.Green().scale(0.5), gizmoLayer, this, thickness);\r\n        this.zGizmo = new AxisScaleGizmo(new Vector3(0, 0, 1), Color3.Blue().scale(0.5), gizmoLayer, this, thickness);\r\n\r\n        this.additionalTransformNode = options?.additionalTransformNode;\r\n\r\n        // Relay drag events\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo].forEach((gizmo) => {\r\n            gizmo.dragBehavior.onDragStartObservable.add(() => {\r\n                this.onDragStartObservable.notifyObservers({});\r\n            });\r\n            gizmo.dragBehavior.onDragObservable.add(() => {\r\n                this.onDragObservable.notifyObservers({});\r\n            });\r\n            gizmo.dragBehavior.onDragEndObservable.add(() => {\r\n                this.onDragEndObservable.notifyObservers({});\r\n            });\r\n        });\r\n\r\n        this.attachedMesh = null;\r\n        this.attachedNode = null;\r\n\r\n        if (gizmoManager) {\r\n            gizmoManager.addToAxisCache(this._gizmoAxisCache);\r\n        } else {\r\n            // Only subscribe to pointer event if gizmoManager isnt\r\n            Gizmo.GizmoAxisPointerObserver(gizmoLayer, this._gizmoAxisCache);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Create Geometry for Gizmo\r\n     */\r\n    protected _createUniformScaleMesh(): AxisScaleGizmo {\r\n        this._coloredMaterial = new StandardMaterial(\"\", this.gizmoLayer.utilityLayerScene);\r\n        this._coloredMaterial.diffuseColor = Color3.Gray();\r\n\r\n        this._hoverMaterial = new StandardMaterial(\"\", this.gizmoLayer.utilityLayerScene);\r\n        this._hoverMaterial.diffuseColor = Color3.Yellow();\r\n\r\n        this._disableMaterial = new StandardMaterial(\"\", this.gizmoLayer.utilityLayerScene);\r\n        this._disableMaterial.diffuseColor = Color3.Gray();\r\n        this._disableMaterial.alpha = 0.4;\r\n\r\n        const uniformScaleGizmo = new AxisScaleGizmo(new Vector3(0, 1, 0), Color3.Gray().scale(0.5), this.gizmoLayer, this);\r\n        uniformScaleGizmo.updateGizmoRotationToMatchAttachedMesh = false;\r\n        uniformScaleGizmo.uniformScaling = true;\r\n        this._uniformScalingMesh = CreatePolyhedron(\"uniform\", { type: 1 }, uniformScaleGizmo.gizmoLayer.utilityLayerScene);\r\n        this._uniformScalingMesh.scaling.scaleInPlace(0.01);\r\n        this._uniformScalingMesh.visibility = 0;\r\n        this._octahedron = CreatePolyhedron(\"\", { type: 1 }, uniformScaleGizmo.gizmoLayer.utilityLayerScene);\r\n        this._octahedron.scaling.scaleInPlace(0.007);\r\n        this._uniformScalingMesh.addChild(this._octahedron);\r\n        uniformScaleGizmo.setCustomMesh(this._uniformScalingMesh, true);\r\n        const light = this.gizmoLayer._getSharedGizmoLight();\r\n        light.includedOnlyMeshes = light.includedOnlyMeshes.concat(this._octahedron);\r\n\r\n        const cache: GizmoAxisCache = {\r\n            gizmoMeshes: [this._octahedron, this._uniformScalingMesh],\r\n            colliderMeshes: [this._uniformScalingMesh],\r\n            material: this._coloredMaterial,\r\n            hoverMaterial: this._hoverMaterial,\r\n            disableMaterial: this._disableMaterial,\r\n            active: false,\r\n            dragBehavior: uniformScaleGizmo.dragBehavior,\r\n        };\r\n\r\n        this.addToAxisCache(uniformScaleGizmo._rootMesh, cache);\r\n\r\n        return uniformScaleGizmo;\r\n    }\r\n\r\n    public set updateGizmoRotationToMatchAttachedMesh(value: boolean) {\r\n        if (!value) {\r\n            Logger.Warn(\"Setting updateGizmoRotationToMatchAttachedMesh = false on scaling gizmo is not supported.\");\r\n        } else {\r\n            this._updateGizmoRotationToMatchAttachedMesh = value;\r\n            [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo].forEach((gizmo) => {\r\n                if (gizmo) {\r\n                    gizmo.updateGizmoRotationToMatchAttachedMesh = value;\r\n                }\r\n            });\r\n        }\r\n    }\r\n    public get updateGizmoRotationToMatchAttachedMesh() {\r\n        return this._updateGizmoRotationToMatchAttachedMesh;\r\n    }\r\n\r\n    public set anchorPoint(value: GizmoAnchorPoint) {\r\n        this._anchorPoint = value;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.anchorPoint = value;\r\n            }\r\n        });\r\n    }\r\n    public get anchorPoint() {\r\n        return this._anchorPoint;\r\n    }\r\n\r\n    /**\r\n     * posture that the gizmo will be display\r\n     * When set null, default value will be used (Quaternion(0, 0, 0, 1))\r\n     */\r\n    public get customRotationQuaternion(): Nullable<Quaternion> {\r\n        return this._customRotationQuaternion;\r\n    }\r\n\r\n    public set customRotationQuaternion(customRotationQuaternion: Nullable<Quaternion>) {\r\n        this._customRotationQuaternion = customRotationQuaternion;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.customRotationQuaternion = customRotationQuaternion;\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Set the coordinate system to use. By default it's local.\r\n     * But it's possible for a user to tweak so its local for translation and world for rotation.\r\n     * In that case, setting the coordinate system will change `updateGizmoRotationToMatchAttachedMesh` and `updateGizmoPositionToMatchAttachedMesh`\r\n     */\r\n    public set coordinatesMode(coordinatesMode: GizmoCoordinatesMode) {\r\n        if (coordinatesMode == GizmoCoordinatesMode.World) {\r\n            Logger.Warn(\"Setting coordinates Mode to world on scaling gizmo is not supported.\");\r\n        }\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo].forEach((gizmo) => {\r\n            gizmo.coordinatesMode = GizmoCoordinatesMode.Local;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Drag distance in babylon units that the gizmo will snap to when dragged (Default: 0)\r\n     */\r\n    public set snapDistance(value: number) {\r\n        this._snapDistance = value;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.snapDistance = value;\r\n            }\r\n        });\r\n    }\r\n    public get snapDistance() {\r\n        return this._snapDistance;\r\n    }\r\n\r\n    /**\r\n     * Incremental snap scaling (default is false). When true, with a snapDistance of 0.1, scaling will be 1.1,1.2,1.3 instead of, when false: 1.1,1.21,1.33,...\r\n     */\r\n    public set incrementalSnap(value: boolean) {\r\n        this._incrementalSnap = value;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.incrementalSnap = value;\r\n            }\r\n        });\r\n    }\r\n    public get incrementalSnap() {\r\n        return this._incrementalSnap;\r\n    }\r\n    /**\r\n     * Ratio for the scale of the gizmo (Default: 1)\r\n     */\r\n    public set scaleRatio(value: number) {\r\n        this._scaleRatio = value;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.scaleRatio = value;\r\n            }\r\n        });\r\n    }\r\n    public get scaleRatio() {\r\n        return this._scaleRatio;\r\n    }\r\n\r\n    /**\r\n     * Sensitivity factor for dragging (Default: 1)\r\n     */\r\n    public set sensitivity(value: number) {\r\n        this._sensitivity = value;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.sensitivity = value;\r\n            }\r\n        });\r\n    }\r\n    public get sensitivity() {\r\n        return this._sensitivity;\r\n    }\r\n\r\n    /**\r\n     * Builds Gizmo Axis Cache to enable features such as hover state preservation and graying out other axis during manipulation\r\n     * @param mesh Axis gizmo mesh\r\n     * @param cache Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    public addToAxisCache(mesh: Mesh, cache: GizmoAxisCache) {\r\n        this._gizmoAxisCache.set(mesh, cache);\r\n    }\r\n\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    public releaseDrag() {\r\n        this.xGizmo.dragBehavior.releaseDrag();\r\n        this.yGizmo.dragBehavior.releaseDrag();\r\n        this.zGizmo.dragBehavior.releaseDrag();\r\n        this.uniformScaleGizmo.dragBehavior.releaseDrag();\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public dispose() {\r\n        [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.dispose();\r\n            }\r\n        });\r\n        this._observables.forEach((obs) => {\r\n            this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(obs);\r\n        });\r\n        this.onDragStartObservable.clear();\r\n        this.onDragObservable.clear();\r\n        this.onDragEndObservable.clear();\r\n        [this._uniformScalingMesh, this._octahedron].forEach((msh) => {\r\n            if (msh) {\r\n                msh.dispose();\r\n            }\r\n        });\r\n        [this._coloredMaterial, this._hoverMaterial, this._disableMaterial].forEach((matl) => {\r\n            if (matl) {\r\n                matl.dispose();\r\n            }\r\n        });\r\n    }\r\n}\r\n"]}