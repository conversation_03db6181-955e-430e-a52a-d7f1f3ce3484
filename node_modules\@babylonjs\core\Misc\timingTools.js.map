{"version": 3, "file": "timingTools.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/timingTools.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAC;AAEtD;;GAEG;AACH,MAAM,OAAO,WAAW;IACpB;;;OAGG;IACI,MAAM,CAAC,YAAY,CAAC,MAAkB;QACzC,IAAI,mBAAmB,EAAE,IAAI,MAAM,CAAC,YAAY,EAAE;YAC9C,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;SAC/B;aAAM;YACH,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SACzB;IACL,CAAC;CACJ", "sourcesContent": ["import { IsWindowObjectExist } from \"./domManagement\";\r\n\r\n/**\r\n * Class used to provide helper for timing\r\n */\r\nexport class TimingTools {\r\n    /**\r\n     * Polyfill for setImmediate\r\n     * @param action defines the action to execute after the current execution block\r\n     */\r\n    public static SetImmediate(action: () => void) {\r\n        if (IsWindowObjectExist() && window.setImmediate) {\r\n            window.setImmediate(action);\r\n        } else {\r\n            setTimeout(action, 1);\r\n        }\r\n    }\r\n}\r\n"]}