{"version": 3, "file": "postProcessRenderPipelineManager.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/PostProcesses/RenderPipeline/postProcessRenderPipelineManager.ts"], "names": [], "mappings": "AAEA;;;GAGG;AACH,MAAM,OAAO,gCAAgC;IAGzC;;;OAGG;IACH;QACI,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,MAAM,MAAM,GAAG,EAAE,CAAC;QAElB,KAAK,MAAM,kBAAkB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACpD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,EAAE;gBACjF,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;gBAC3D,IAAI,QAAQ,CAAC,WAAW,EAAE;oBACtB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACzB;aACJ;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,cAAyC;QACxD,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC;IACjE,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,kBAA0B;QAC5C,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;IACrD,CAAC;IAED;;;;;OAKG;IACI,6BAA6B,CAAC,kBAA0B,EAAE,OAAgC,EAAE,SAAkB,KAAK;QACtH,MAAM,cAAc,GAA8B,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QAE5F,IAAI,CAAC,cAAc,EAAE;YACjB,OAAO;SACV;QAED,cAAc,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAED;;;;OAIG;IACI,+BAA+B,CAAC,kBAA0B,EAAE,OAAgC;QAC/F,MAAM,cAAc,GAA8B,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QAE5F,IAAI,CAAC,cAAc,EAAE;YACjB,OAAO;SACV;QAED,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACI,sBAAsB,CAAC,kBAA0B,EAAE,gBAAwB,EAAE,OAAgC;QAChH,MAAM,cAAc,GAA8B,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QAE5F,IAAI,CAAC,cAAc,EAAE;YACjB,OAAO;SACV;QAED,cAAc,CAAC,aAAa,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;OAKG;IACI,uBAAuB,CAAC,kBAA0B,EAAE,gBAAwB,EAAE,OAAgC;QACjH,MAAM,cAAc,GAA8B,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QAE5F,IAAI,CAAC,cAAc,EAAE;YACjB,OAAO;SACV;QAED,cAAc,CAAC,cAAc,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,MAAM;QACT,KAAK,MAAM,kBAAkB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACpD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,EAAE;gBACjF,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;gBAC3D,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;oBACvB,QAAQ,CAAC,OAAO,EAAE,CAAC;oBACnB,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;iBACpD;qBAAM;oBACH,QAAQ,CAAC,OAAO,EAAE,CAAC;iBACtB;aACJ;SACJ;IACL,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,KAAK,MAAM,kBAAkB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACpD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,EAAE;gBACjF,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;gBAC3D,QAAQ,CAAC,QAAQ,EAAE,CAAC;aACvB;SACJ;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,MAAM,kBAAkB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACpD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,EAAE;gBACjF,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;gBAC3D,QAAQ,CAAC,OAAO,EAAE,CAAC;aACtB;SACJ;IACL,CAAC;CACJ", "sourcesContent": ["import type { Camera } from \"../../Cameras/camera\";\r\nimport type { PostProcessRenderPipeline } from \"./postProcessRenderPipeline\";\r\n/**\r\n * PostProcessRenderPipelineManager class\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/postProcessRenderPipeline\r\n */\r\nexport class PostProcessRenderPipelineManager {\r\n    private _renderPipelines: { [Key: string]: PostProcessRenderPipeline };\r\n\r\n    /**\r\n     * Initializes a PostProcessRenderPipelineManager\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/postProcessRenderPipeline\r\n     */\r\n    constructor() {\r\n        this._renderPipelines = {};\r\n    }\r\n\r\n    /**\r\n     * Gets the list of supported render pipelines\r\n     */\r\n    public get supportedPipelines(): PostProcessRenderPipeline[] {\r\n        const result = [];\r\n\r\n        for (const renderPipelineName in this._renderPipelines) {\r\n            if (Object.prototype.hasOwnProperty.call(this._renderPipelines, renderPipelineName)) {\r\n                const pipeline = this._renderPipelines[renderPipelineName];\r\n                if (pipeline.isSupported) {\r\n                    result.push(pipeline);\r\n                }\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Adds a pipeline to the manager\r\n     * @param renderPipeline The pipeline to add\r\n     */\r\n    public addPipeline(renderPipeline: PostProcessRenderPipeline): void {\r\n        this._renderPipelines[renderPipeline._name] = renderPipeline;\r\n    }\r\n\r\n    /**\r\n     * Remove the pipeline from the manager\r\n     * @param renderPipelineName the name of the pipeline to remove\r\n     */\r\n    public removePipeline(renderPipelineName: string): void {\r\n        delete this._renderPipelines[renderPipelineName];\r\n    }\r\n\r\n    /**\r\n     * Attaches a camera to the pipeline\r\n     * @param renderPipelineName The name of the pipeline to attach to\r\n     * @param cameras the camera to attach\r\n     * @param unique if the camera can be attached multiple times to the pipeline\r\n     */\r\n    public attachCamerasToRenderPipeline(renderPipelineName: string, cameras: any | Camera[] | Camera, unique: boolean = false): void {\r\n        const renderPipeline: PostProcessRenderPipeline = this._renderPipelines[renderPipelineName];\r\n\r\n        if (!renderPipeline) {\r\n            return;\r\n        }\r\n\r\n        renderPipeline._attachCameras(cameras, unique);\r\n    }\r\n\r\n    /**\r\n     * Detaches a camera from the pipeline\r\n     * @param renderPipelineName The name of the pipeline to detach from\r\n     * @param cameras the camera to detach\r\n     */\r\n    public detachCamerasFromRenderPipeline(renderPipelineName: string, cameras: any | Camera[] | Camera): void {\r\n        const renderPipeline: PostProcessRenderPipeline = this._renderPipelines[renderPipelineName];\r\n\r\n        if (!renderPipeline) {\r\n            return;\r\n        }\r\n\r\n        renderPipeline._detachCameras(cameras);\r\n    }\r\n\r\n    /**\r\n     * Enables an effect by name on a pipeline\r\n     * @param renderPipelineName the name of the pipeline to enable the effect in\r\n     * @param renderEffectName the name of the effect to enable\r\n     * @param cameras the cameras that the effect should be enabled on\r\n     */\r\n    public enableEffectInPipeline(renderPipelineName: string, renderEffectName: string, cameras: any | Camera[] | Camera): void {\r\n        const renderPipeline: PostProcessRenderPipeline = this._renderPipelines[renderPipelineName];\r\n\r\n        if (!renderPipeline) {\r\n            return;\r\n        }\r\n\r\n        renderPipeline._enableEffect(renderEffectName, cameras);\r\n    }\r\n\r\n    /**\r\n     * Disables an effect by name on a pipeline\r\n     * @param renderPipelineName the name of the pipeline to disable the effect in\r\n     * @param renderEffectName the name of the effect to disable\r\n     * @param cameras the cameras that the effect should be disabled on\r\n     */\r\n    public disableEffectInPipeline(renderPipelineName: string, renderEffectName: string, cameras: any | Camera[] | Camera): void {\r\n        const renderPipeline: PostProcessRenderPipeline = this._renderPipelines[renderPipelineName];\r\n\r\n        if (!renderPipeline) {\r\n            return;\r\n        }\r\n\r\n        renderPipeline._disableEffect(renderEffectName, cameras);\r\n    }\r\n\r\n    /**\r\n     * Updates the state of all contained render pipelines and disposes of any non supported pipelines\r\n     */\r\n    public update(): void {\r\n        for (const renderPipelineName in this._renderPipelines) {\r\n            if (Object.prototype.hasOwnProperty.call(this._renderPipelines, renderPipelineName)) {\r\n                const pipeline = this._renderPipelines[renderPipelineName];\r\n                if (!pipeline.isSupported) {\r\n                    pipeline.dispose();\r\n                    delete this._renderPipelines[renderPipelineName];\r\n                } else {\r\n                    pipeline._update();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuild(): void {\r\n        for (const renderPipelineName in this._renderPipelines) {\r\n            if (Object.prototype.hasOwnProperty.call(this._renderPipelines, renderPipelineName)) {\r\n                const pipeline = this._renderPipelines[renderPipelineName];\r\n                pipeline._rebuild();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disposes of the manager and pipelines\r\n     */\r\n    public dispose(): void {\r\n        for (const renderPipelineName in this._renderPipelines) {\r\n            if (Object.prototype.hasOwnProperty.call(this._renderPipelines, renderPipelineName)) {\r\n                const pipeline = this._renderPipelines[renderPipelineName];\r\n                pipeline.dispose();\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}