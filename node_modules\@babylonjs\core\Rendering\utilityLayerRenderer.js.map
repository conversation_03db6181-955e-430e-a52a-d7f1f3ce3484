{"version": 3, "file": "utilityLayerRenderer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Rendering/utilityLayerRenderer.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AAGjC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AACzE,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAExD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAC9D,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE/C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAG7C;;GAEG;AACH,MAAM,OAAO,oBAAoB;IAW7B;;;;OAIG;IACI,eAAe,CAAC,sBAAgC;QACnD,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,OAAO,IAAI,CAAC,aAAa,CAAC;SAC7B;aAAM;YACH,IAAI,SAAiB,CAAC;YACtB,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjF,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;aAC7F;iBAAM;gBACH,SAAS,GAAW,IAAI,CAAC,aAAa,CAAC,YAAa,CAAC;aACxD;YAED,IAAI,sBAAsB,IAAI,SAAS,IAAI,SAAS,CAAC,WAAW,EAAE;gBAC9D,OAAO,SAAS,CAAC,SAAU,CAAC;aAC/B;YACD,OAAO,SAAS,CAAC;SACpB;IACL,CAAC;IACD;;;OAGG;IACI,eAAe,CAAC,GAAqB;QACxC,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,oBAAoB;QACvB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,IAAI,CAAC,iBAAiB,GAAG,IAAI,gBAAgB,CAAC,oBAAoB,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAClH,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,CAAC,CAAC;YACrC,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;SACtD;QACD,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAMD;;OAEG;IACI,MAAM,KAAK,mBAAmB;QACjC,IAAI,oBAAoB,CAAC,oBAAoB,IAAI,IAAI,EAAE;YACnD,OAAO,oBAAoB,CAAC,mCAAmC,CAAC,WAAW,CAAC,gBAAiB,CAAC,CAAC;SAClG;QAED,OAAO,oBAAoB,CAAC,oBAAoB,CAAC;IACrD,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,mCAAmC,CAAC,KAAY;QAC1D,oBAAoB,CAAC,oBAAoB,GAAG,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC5E,oBAAoB,CAAC,oBAAoB,CAAC,aAAa,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;YACrF,oBAAoB,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,OAAO,oBAAoB,CAAC,oBAAoB,CAAC;IACrD,CAAC;IACD;;OAEG;IACI,MAAM,KAAK,4BAA4B;QAC1C,IAAI,oBAAoB,CAAC,6BAA6B,IAAI,IAAI,EAAE;YAC5D,oBAAoB,CAAC,6BAA6B,GAAG,IAAI,oBAAoB,CAAC,WAAW,CAAC,gBAAiB,CAAC,CAAC;YAC7G,oBAAoB,CAAC,6BAA6B,CAAC,iBAAiB,CAAC,wBAAwB,GAAG,KAAK,CAAC;YACtG,oBAAoB,CAAC,6BAA6B,CAAC,aAAa,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;gBAC9F,oBAAoB,CAAC,6BAA6B,GAAG,IAAI,CAAC;YAC9D,CAAC,CAAC,CAAC;SACN;QACD,OAAO,oBAAoB,CAAC,6BAA6B,CAAC;IAC9D,CAAC;IAqCD;;;;OAIG;IACH;IACI,yDAAyD;IAClD,aAAoB,EAC3B,eAAwB,IAAI;QADrB,kBAAa,GAAb,aAAa,CAAO;QAzIvB,qBAAgB,GAAqC,EAAE,CAAC;QACxD,uBAAkB,GAAqC,EAAE,CAAC;QAK1D,sBAAiB,GAA+B,IAAI,CAAC;QAErD,kBAAa,GAAqB,IAAI,CAAC;QA6C/C;;WAEG;QACI,0BAAqB,GAAG,IAAI,CAAC;QA4CpC;;WAEG;QACI,iBAAY,GAAY,IAAI,CAAC;QACpC;;WAEG;QACI,+BAA0B,GAAG,IAAI,CAAC;QAEzC;;WAEG;QACI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG;QACI,mBAAc,GAAG,IAAI,CAAC;QAE7B;;WAEG;QACI,2BAAsB,GAAG,IAAI,UAAU,EAAU,CAAC;QAkBrD,6IAA6I;QAC7I,IAAI,CAAC,iBAAiB,GAAG,IAAI,KAAK,CAAC,aAAa,CAAC,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QACjF,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,GAAG,aAAa,CAAC,oBAAoB,CAAC;QACjF,IAAI,CAAC,iBAAiB,CAAC,2BAA2B,GAAG,KAAK,CAAC;QAE3D,4BAA4B;QAC5B,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAEpD,mGAAmG;QACnG,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC;QAEvC,IAAI,YAAY,EAAE;YACd,IAAI,CAAC,wBAAwB,GAAG,aAAa,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE;gBACxF,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE;oBACtC,OAAO;iBACV;gBACD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;oBACtB,OAAO;iBACV;gBAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;oBACxB,IACI,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW;wBACrD,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,SAAS;wBACnD,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW;wBACrD,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,gBAAgB,EAC5D;wBACE,OAAO;qBACV;iBACJ;gBACD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;gBACzD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;gBACzD,MAAM,YAAY,GAAkB,cAAc,CAAC,KAAK,CAAC;gBACzD,IAAI,aAAc,CAAC,iBAAiB,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;oBAC1D,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;oBACtD,OAAO;iBACV;gBAED,MAAM,uBAAuB,GAAG,CAAC,KAAY,EAAE,EAAE;oBAC7C,IAAI,SAAS,GAAG,IAAI,CAAC;oBAErB,IAAI,cAAc,CAAC,0BAA0B,EAAE;wBAC3C,IAAI,cAAc,CAAC,0BAA0B,CAAC,UAAW,CAAC,QAAQ,EAAE,IAAI,KAAK,EAAE;4BAC3E,SAAS,GAAG,cAAc,CAAC,0BAA0B,CAAC;yBACzD;6BAAM;4BACH,SAAS,GAAG,IAAI,WAAW,EAAE,CAAC;yBACjC;qBACJ;yBAAM,IAAI,KAAK,KAAK,IAAI,CAAC,iBAAiB,IAAI,cAAc,CAAC,mBAAmB,EAAE;wBAC/E,SAAS,GAAG,cAAc,CAAC,mBAAmB,CAAC;qBAClD;yBAAM;wBACH,IAAI,oBAAoB,GAAqB,IAAI,CAAC;wBAClD,mDAAmD;wBACnD,+CAA+C;wBAC/C,yEAAyE;wBACzE,8DAA8D;wBAC9D,mEAAmE;wBACnE,0DAA0D;wBAC1D,IAAI,IAAI,CAAC,aAAa,EAAE;4BACpB,oBAAoB,GAAG,KAAK,CAAC,aAAa,CAAC;4BAC3C,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;4BACzC,cAAc,CAAC,GAAG,GAAG,IAAI,CAAC;yBAC7B;wBACD,SAAS,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;wBACpI,IAAI,oBAAoB,EAAE;4BACtB,KAAK,CAAC,aAAa,GAAG,oBAAoB,CAAC;yBAC9C;qBACJ;oBAED,OAAO,SAAS,CAAC;gBACrB,CAAC,CAAC;gBAEF,MAAM,gBAAgB,GAAG,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAEzE,IAAI,CAAC,cAAc,CAAC,GAAG,IAAI,gBAAgB,EAAE;oBACzC,cAAc,CAAC,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC;iBAC7C;gBAED,wCAAwC;gBACxC,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;gBAE9E,kEAAkE;gBAClE,IAAI,IAAI,CAAC,0BAA0B,IAAI,cAAc,CAAC,IAAI,IAAI,iBAAiB,CAAC,WAAW,EAAE;oBACzF,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE;wBACzC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,eAAe,CACtD,IAAI,WAAW,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAC5E,cAAc,CAAC,IAAI,CACtB,CAAC;qBACL;oBACD,IAAI,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,SAAS,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;wBACtG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;qBACzD;oBACD,OAAO;iBACV;gBAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,IAAI,IAAI,CAAC,qBAAqB,EAAE;oBAC/E,6GAA6G;oBAC7G,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,GAAG,EAAE;wBAC1C,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE;4BACzC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,eAAe,CACtD,IAAI,WAAW,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAC5E,cAAc,CAAC,IAAI,CACtB,CAAC;yBACL;wBACD,cAAc,CAAC,uBAAuB,GAAG,IAAI,CAAC;qBACjD;iBACJ;qBAAM;oBACH,MAAM,iBAAiB,GAAG,uBAAuB,CAAC,aAAa,CAAC,CAAC;oBACjE,MAAM,YAAY,GAAkB,cAAc,CAAC,KAAK,CAAC;oBAEzD,oHAAoH;oBACpH,IAAI,iBAAiB,IAAI,gBAAgB,EAAE;wBACvC,2BAA2B;wBAC3B,IAAI,gBAAgB,CAAC,QAAQ,KAAK,CAAC,IAAI,iBAAiB,CAAC,UAAU,EAAE;4BACjE,IAAI,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;gCAChG,uDAAuD;gCACvD,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,iBAAiB,EAAE,YAAY,CAAC,CAAC;gCACvE,cAAc,CAAC,uBAAuB,GAAG,IAAI,CAAC;6BACjD;iCAAM,IAAI,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,EAAE;gCAC9D,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;6BACxD;iCAAM,IAAI,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,IAAI,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,SAAS,EAAE;gCACrH,IAAI,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;oCACjD,iGAAiG;oCACjG,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;oCACpE,OAAO,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;iCAC1D;gCACD,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,iBAAiB,EAAE,YAAY,CAAC,CAAC;6BAC1E;yBACJ;6BAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,IAAI,iBAAiB,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAE;4BACvJ,iGAAiG;4BACjG,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,gBAAgB,EAAE,YAAY,CAAC,CAAC;4BACtE,0DAA0D;4BAC1D,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE;gCACzC,cAAc,CAAC,uBAAuB,GAAG,gBAAgB,CAAC,QAAQ,GAAG,CAAC,CAAC;6BAC1E;yBACJ;6BAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,gBAAgB,CAAC,QAAQ,IAAI,iBAAiB,CAAC,QAAQ,EAAE;4BAClH,gEAAgE;4BAEhE,uDAAuD;4BACvD,IAAI,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;gCAChG,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,iBAAiB,EAAE,YAAY,CAAC,CAAC;gCACvE,cAAc,CAAC,uBAAuB,GAAG,IAAI,CAAC;6BACjD;iCAAM;gCACH,IAAI,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,IAAI,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,SAAS,EAAE;oCAC9G,IAAI,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;wCACjD,iGAAiG;wCACjG,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;wCACpE,OAAO,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;qCAC1D;iCACJ;gCACD,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,gBAAgB,EAAE,YAAY,CAAC,CAAC;6BACzE;yBACJ;wBAED,IAAI,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,SAAS,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;4BACtG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;yBACzD;qBACJ;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,sHAAsH;YACtH,IAAI,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,aAAa,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;aAC/F;SACJ;QAED,4DAA4D;QAC5D,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,KAAK,CAAC;QAEzC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,aAAa,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACxF,wDAAwD;YACxD,IAAI,IAAI,CAAC,YAAY,IAAI,MAAM,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;gBACvD,IAAI,CAAC,MAAM,EAAE,CAAC;aACjB;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YACzE,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEO,gBAAgB,CAAC,cAA8B,EAAE,QAAqB,EAAE,YAA2B;QACvG,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE;YACzC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,WAAW,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC;YACtJ,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;SAC1D;IACL,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE;YACrC,iDAAiD;YACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YAChE,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;YACnD,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC;YACvC,IAAI,MAAM,CAAC,UAAU,EAAE;gBACnB,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC;aACrD;YACD,IAAI,MAAM,CAAC,WAAW,EAAE;gBACpB,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC;aACtD;YAED,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAErC,wCAAwC;YACxC,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC;YACzB,IAAI,MAAM,CAAC,UAAU,EAAE;gBACnB,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC;aACvC;YACD,IAAI,MAAM,CAAC,WAAW,EAAE;gBACpB,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QAEpC,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,aAAa,CAAC,6BAA6B,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACtF;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SAC7E;QACD,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;SACnF;QACD,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;IACrC,CAAC;IAEO,aAAa;QACjB,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACvE,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IACjE,CAAC;;AA3XD,gBAAgB;AACF,yCAAoB,GAAmC,IAAI,AAAvC,CAAwC;AAC1E,gBAAgB;AACF,kDAA6B,GAAmC,IAAI,AAAvC,CAAwC", "sourcesContent": ["import type { IDisposable } from \"../scene\";\r\nimport { Scene } from \"../scene\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { PointerInfoPre } from \"../Events/pointerEvents\";\r\nimport { PointerInfo, PointerEventTypes } from \"../Events/pointerEvents\";\r\nimport { PickingInfo } from \"../Collisions/pickingInfo\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { HemisphericLight } from \"../Lights/hemisphericLight\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport type { IPointerEvent } from \"../Events/deviceInputEvents\";\r\n\r\n/**\r\n * Renders a layer on top of an existing scene\r\n */\r\nexport class UtilityLayerRenderer implements IDisposable {\r\n    private _pointerCaptures: { [pointerId: number]: boolean } = {};\r\n    private _lastPointerEvents: { [pointerId: number]: boolean } = {};\r\n    /** @internal */\r\n    public static _DefaultUtilityLayer: Nullable<UtilityLayerRenderer> = null;\r\n    /** @internal */\r\n    public static _DefaultKeepDepthUtilityLayer: Nullable<UtilityLayerRenderer> = null;\r\n    private _sharedGizmoLight: Nullable<HemisphericLight> = null;\r\n\r\n    private _renderCamera: Nullable<Camera> = null;\r\n\r\n    /**\r\n     * Gets the camera that is used to render the utility layer (when not set, this will be the last active camera)\r\n     * @param getRigParentIfPossible if the current active camera is a rig camera, should its parent camera be returned\r\n     * @returns the camera that is used when rendering the utility layer\r\n     */\r\n    public getRenderCamera(getRigParentIfPossible?: boolean) {\r\n        if (this._renderCamera) {\r\n            return this._renderCamera;\r\n        } else {\r\n            let activeCam: Camera;\r\n            if (this.originalScene.activeCameras && this.originalScene.activeCameras.length > 1) {\r\n                activeCam = this.originalScene.activeCameras[this.originalScene.activeCameras.length - 1];\r\n            } else {\r\n                activeCam = <Camera>this.originalScene.activeCamera!;\r\n            }\r\n\r\n            if (getRigParentIfPossible && activeCam && activeCam.isRigCamera) {\r\n                return activeCam.rigParent!;\r\n            }\r\n            return activeCam;\r\n        }\r\n    }\r\n    /**\r\n     * Sets the camera that should be used when rendering the utility layer (If set to null the last active camera will be used)\r\n     * @param cam the camera that should be used when rendering the utility layer\r\n     */\r\n    public setRenderCamera(cam: Nullable<Camera>) {\r\n        this._renderCamera = cam;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Light which used by gizmos to get light shading\r\n     */\r\n    public _getSharedGizmoLight(): HemisphericLight {\r\n        if (!this._sharedGizmoLight) {\r\n            this._sharedGizmoLight = new HemisphericLight(\"shared gizmo light\", new Vector3(0, 1, 0), this.utilityLayerScene);\r\n            this._sharedGizmoLight.intensity = 2;\r\n            this._sharedGizmoLight.groundColor = Color3.Gray();\r\n        }\r\n        return this._sharedGizmoLight;\r\n    }\r\n\r\n    /**\r\n     * If the picking should be done on the utility layer prior to the actual scene (Default: true)\r\n     */\r\n    public pickUtilitySceneFirst = true;\r\n    /**\r\n     * A shared utility layer that can be used to overlay objects into a scene (Depth map of the previous scene is cleared before drawing on top of it)\r\n     */\r\n    public static get DefaultUtilityLayer(): UtilityLayerRenderer {\r\n        if (UtilityLayerRenderer._DefaultUtilityLayer == null) {\r\n            return UtilityLayerRenderer._CreateDefaultUtilityLayerFromScene(EngineStore.LastCreatedScene!);\r\n        }\r\n\r\n        return UtilityLayerRenderer._DefaultUtilityLayer;\r\n    }\r\n\r\n    /**\r\n     * Creates an utility layer, and set it as a default utility layer\r\n     * @param scene associated scene\r\n     * @internal\r\n     */\r\n    public static _CreateDefaultUtilityLayerFromScene(scene: Scene): UtilityLayerRenderer {\r\n        UtilityLayerRenderer._DefaultUtilityLayer = new UtilityLayerRenderer(scene);\r\n        UtilityLayerRenderer._DefaultUtilityLayer.originalScene.onDisposeObservable.addOnce(() => {\r\n            UtilityLayerRenderer._DefaultUtilityLayer = null;\r\n        });\r\n\r\n        return UtilityLayerRenderer._DefaultUtilityLayer;\r\n    }\r\n    /**\r\n     * A shared utility layer that can be used to embed objects into a scene (Depth map of the previous scene is not cleared before drawing on top of it)\r\n     */\r\n    public static get DefaultKeepDepthUtilityLayer(): UtilityLayerRenderer {\r\n        if (UtilityLayerRenderer._DefaultKeepDepthUtilityLayer == null) {\r\n            UtilityLayerRenderer._DefaultKeepDepthUtilityLayer = new UtilityLayerRenderer(EngineStore.LastCreatedScene!);\r\n            UtilityLayerRenderer._DefaultKeepDepthUtilityLayer.utilityLayerScene.autoClearDepthAndStencil = false;\r\n            UtilityLayerRenderer._DefaultKeepDepthUtilityLayer.originalScene.onDisposeObservable.addOnce(() => {\r\n                UtilityLayerRenderer._DefaultKeepDepthUtilityLayer = null;\r\n            });\r\n        }\r\n        return UtilityLayerRenderer._DefaultKeepDepthUtilityLayer;\r\n    }\r\n\r\n    /**\r\n     * The scene that is rendered on top of the original scene\r\n     */\r\n    public utilityLayerScene: Scene;\r\n\r\n    /**\r\n     *  If the utility layer should automatically be rendered on top of existing scene\r\n     */\r\n    public shouldRender: boolean = true;\r\n    /**\r\n     * If set to true, only pointer down onPointerObservable events will be blocked when picking is occluded by original scene\r\n     */\r\n    public onlyCheckPointerDownEvents = true;\r\n\r\n    /**\r\n     * If set to false, only pointerUp, pointerDown and pointerMove will be sent to the utilityLayerScene (false by default)\r\n     */\r\n    public processAllEvents = false;\r\n\r\n    /**\r\n     * Set to false to disable picking\r\n     */\r\n    public pickingEnabled = true;\r\n\r\n    /**\r\n     * Observable raised when the pointer moves from the utility layer scene to the main scene\r\n     */\r\n    public onPointerOutObservable = new Observable<number>();\r\n\r\n    /** Gets or sets a predicate that will be used to indicate utility meshes present in the main scene */\r\n    public mainSceneTrackerPredicate: (mesh: Nullable<AbstractMesh>) => boolean;\r\n\r\n    private _afterRenderObserver: Nullable<Observer<Camera>>;\r\n    private _sceneDisposeObserver: Nullable<Observer<Scene>>;\r\n    private _originalPointerObserver: Nullable<Observer<PointerInfoPre>>;\r\n    /**\r\n     * Instantiates a UtilityLayerRenderer\r\n     * @param originalScene the original scene that will be rendered on top of\r\n     * @param handleEvents boolean indicating if the utility layer should handle events\r\n     */\r\n    constructor(\r\n        /** the original scene that will be rendered on top of */\r\n        public originalScene: Scene,\r\n        handleEvents: boolean = true\r\n    ) {\r\n        // Create scene which will be rendered in the foreground and remove it from being referenced by engine to avoid interfering with existing app\r\n        this.utilityLayerScene = new Scene(originalScene.getEngine(), { virtual: true });\r\n        this.utilityLayerScene.useRightHandedSystem = originalScene.useRightHandedSystem;\r\n        this.utilityLayerScene._allowPostProcessClearColor = false;\r\n\r\n        // Deactivate post processes\r\n        this.utilityLayerScene.postProcessesEnabled = false;\r\n\r\n        // Detach controls on utility scene, events will be fired by logic below to handle picking priority\r\n        this.utilityLayerScene.detachControl();\r\n\r\n        if (handleEvents) {\r\n            this._originalPointerObserver = originalScene.onPrePointerObservable.add((prePointerInfo) => {\r\n                if (!this.utilityLayerScene.activeCamera) {\r\n                    return;\r\n                }\r\n                if (!this.pickingEnabled) {\r\n                    return;\r\n                }\r\n\r\n                if (!this.processAllEvents) {\r\n                    if (\r\n                        prePointerInfo.type !== PointerEventTypes.POINTERMOVE &&\r\n                        prePointerInfo.type !== PointerEventTypes.POINTERUP &&\r\n                        prePointerInfo.type !== PointerEventTypes.POINTERDOWN &&\r\n                        prePointerInfo.type !== PointerEventTypes.POINTERDOUBLETAP\r\n                    ) {\r\n                        return;\r\n                    }\r\n                }\r\n                this.utilityLayerScene.pointerX = originalScene.pointerX;\r\n                this.utilityLayerScene.pointerY = originalScene.pointerY;\r\n                const pointerEvent = <IPointerEvent>prePointerInfo.event;\r\n                if (originalScene!.isPointerCaptured(pointerEvent.pointerId)) {\r\n                    this._pointerCaptures[pointerEvent.pointerId] = false;\r\n                    return;\r\n                }\r\n\r\n                const getNearPickDataForScene = (scene: Scene) => {\r\n                    let scenePick = null;\r\n\r\n                    if (prePointerInfo.nearInteractionPickingInfo) {\r\n                        if (prePointerInfo.nearInteractionPickingInfo.pickedMesh!.getScene() == scene) {\r\n                            scenePick = prePointerInfo.nearInteractionPickingInfo;\r\n                        } else {\r\n                            scenePick = new PickingInfo();\r\n                        }\r\n                    } else if (scene !== this.utilityLayerScene && prePointerInfo.originalPickingInfo) {\r\n                        scenePick = prePointerInfo.originalPickingInfo;\r\n                    } else {\r\n                        let previousActiveCamera: Nullable<Camera> = null;\r\n                        // If a camera is set for rendering with this layer\r\n                        // it will also be used for the ray computation\r\n                        // To preserve back compat and because scene.pick always use activeCamera\r\n                        // it's substituted temporarily and a new scenePick is forced.\r\n                        // otherwise, the ray with previously active camera is always used.\r\n                        // It's set back to previous activeCamera after operation.\r\n                        if (this._renderCamera) {\r\n                            previousActiveCamera = scene._activeCamera;\r\n                            scene._activeCamera = this._renderCamera;\r\n                            prePointerInfo.ray = null;\r\n                        }\r\n                        scenePick = prePointerInfo.ray ? scene.pickWithRay(prePointerInfo.ray) : scene.pick(originalScene.pointerX, originalScene.pointerY);\r\n                        if (previousActiveCamera) {\r\n                            scene._activeCamera = previousActiveCamera;\r\n                        }\r\n                    }\r\n\r\n                    return scenePick;\r\n                };\r\n\r\n                const utilityScenePick = getNearPickDataForScene(this.utilityLayerScene);\r\n\r\n                if (!prePointerInfo.ray && utilityScenePick) {\r\n                    prePointerInfo.ray = utilityScenePick.ray;\r\n                }\r\n\r\n                // always fire the prepointer observable\r\n                this.utilityLayerScene.onPrePointerObservable.notifyObservers(prePointerInfo);\r\n\r\n                // allow every non pointer down event to flow to the utility layer\r\n                if (this.onlyCheckPointerDownEvents && prePointerInfo.type != PointerEventTypes.POINTERDOWN) {\r\n                    if (!prePointerInfo.skipOnPointerObservable) {\r\n                        this.utilityLayerScene.onPointerObservable.notifyObservers(\r\n                            new PointerInfo(prePointerInfo.type, prePointerInfo.event, utilityScenePick),\r\n                            prePointerInfo.type\r\n                        );\r\n                    }\r\n                    if (prePointerInfo.type === PointerEventTypes.POINTERUP && this._pointerCaptures[pointerEvent.pointerId]) {\r\n                        this._pointerCaptures[pointerEvent.pointerId] = false;\r\n                    }\r\n                    return;\r\n                }\r\n\r\n                if (this.utilityLayerScene.autoClearDepthAndStencil || this.pickUtilitySceneFirst) {\r\n                    // If this layer is an overlay, check if this layer was hit and if so, skip pointer events for the main scene\r\n                    if (utilityScenePick && utilityScenePick.hit) {\r\n                        if (!prePointerInfo.skipOnPointerObservable) {\r\n                            this.utilityLayerScene.onPointerObservable.notifyObservers(\r\n                                new PointerInfo(prePointerInfo.type, prePointerInfo.event, utilityScenePick),\r\n                                prePointerInfo.type\r\n                            );\r\n                        }\r\n                        prePointerInfo.skipOnPointerObservable = true;\r\n                    }\r\n                } else {\r\n                    const originalScenePick = getNearPickDataForScene(originalScene);\r\n                    const pointerEvent = <IPointerEvent>prePointerInfo.event;\r\n\r\n                    // If the layer can be occluded by the original scene, only fire pointer events to the first layer that hit they ray\r\n                    if (originalScenePick && utilityScenePick) {\r\n                        // No pick in utility scene\r\n                        if (utilityScenePick.distance === 0 && originalScenePick.pickedMesh) {\r\n                            if (this.mainSceneTrackerPredicate && this.mainSceneTrackerPredicate(originalScenePick.pickedMesh)) {\r\n                                // We touched an utility mesh present in the main scene\r\n                                this._notifyObservers(prePointerInfo, originalScenePick, pointerEvent);\r\n                                prePointerInfo.skipOnPointerObservable = true;\r\n                            } else if (prePointerInfo.type === PointerEventTypes.POINTERDOWN) {\r\n                                this._pointerCaptures[pointerEvent.pointerId] = true;\r\n                            } else if (prePointerInfo.type === PointerEventTypes.POINTERMOVE || prePointerInfo.type === PointerEventTypes.POINTERUP) {\r\n                                if (this._lastPointerEvents[pointerEvent.pointerId]) {\r\n                                    // We need to send a last pointerup to the utilityLayerScene to make sure animations can complete\r\n                                    this.onPointerOutObservable.notifyObservers(pointerEvent.pointerId);\r\n                                    delete this._lastPointerEvents[pointerEvent.pointerId];\r\n                                }\r\n                                this._notifyObservers(prePointerInfo, originalScenePick, pointerEvent);\r\n                            }\r\n                        } else if (!this._pointerCaptures[pointerEvent.pointerId] && (utilityScenePick.distance < originalScenePick.distance || originalScenePick.distance === 0)) {\r\n                            // We pick something in utility scene or the pick in utility is closer than the one in main scene\r\n                            this._notifyObservers(prePointerInfo, utilityScenePick, pointerEvent);\r\n                            // If a previous utility layer set this, do not unset this\r\n                            if (!prePointerInfo.skipOnPointerObservable) {\r\n                                prePointerInfo.skipOnPointerObservable = utilityScenePick.distance > 0;\r\n                            }\r\n                        } else if (!this._pointerCaptures[pointerEvent.pointerId] && utilityScenePick.distance >= originalScenePick.distance) {\r\n                            // We have a pick in both scenes but main is closer than utility\r\n\r\n                            // We touched an utility mesh present in the main scene\r\n                            if (this.mainSceneTrackerPredicate && this.mainSceneTrackerPredicate(originalScenePick.pickedMesh)) {\r\n                                this._notifyObservers(prePointerInfo, originalScenePick, pointerEvent);\r\n                                prePointerInfo.skipOnPointerObservable = true;\r\n                            } else {\r\n                                if (prePointerInfo.type === PointerEventTypes.POINTERMOVE || prePointerInfo.type === PointerEventTypes.POINTERUP) {\r\n                                    if (this._lastPointerEvents[pointerEvent.pointerId]) {\r\n                                        // We need to send a last pointerup to the utilityLayerScene to make sure animations can complete\r\n                                        this.onPointerOutObservable.notifyObservers(pointerEvent.pointerId);\r\n                                        delete this._lastPointerEvents[pointerEvent.pointerId];\r\n                                    }\r\n                                }\r\n                                this._notifyObservers(prePointerInfo, utilityScenePick, pointerEvent);\r\n                            }\r\n                        }\r\n\r\n                        if (prePointerInfo.type === PointerEventTypes.POINTERUP && this._pointerCaptures[pointerEvent.pointerId]) {\r\n                            this._pointerCaptures[pointerEvent.pointerId] = false;\r\n                        }\r\n                    }\r\n                }\r\n            });\r\n\r\n            // As a newly added utility layer will be rendered over the screen last, it's pointer events should be processed first\r\n            if (this._originalPointerObserver) {\r\n                originalScene.onPrePointerObservable.makeObserverTopPriority(this._originalPointerObserver);\r\n            }\r\n        }\r\n\r\n        // Render directly on top of existing scene without clearing\r\n        this.utilityLayerScene.autoClear = false;\r\n\r\n        this._afterRenderObserver = this.originalScene.onAfterRenderCameraObservable.add((camera) => {\r\n            // Only render when the render camera finishes rendering\r\n            if (this.shouldRender && camera == this.getRenderCamera()) {\r\n                this.render();\r\n            }\r\n        });\r\n\r\n        this._sceneDisposeObserver = this.originalScene.onDisposeObservable.add(() => {\r\n            this.dispose();\r\n        });\r\n\r\n        this._updateCamera();\r\n    }\r\n\r\n    private _notifyObservers(prePointerInfo: PointerInfoPre, pickInfo: PickingInfo, pointerEvent: IPointerEvent) {\r\n        if (!prePointerInfo.skipOnPointerObservable) {\r\n            this.utilityLayerScene.onPointerObservable.notifyObservers(new PointerInfo(prePointerInfo.type, prePointerInfo.event, pickInfo), prePointerInfo.type);\r\n            this._lastPointerEvents[pointerEvent.pointerId] = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Renders the utility layers scene on top of the original scene\r\n     */\r\n    public render() {\r\n        this._updateCamera();\r\n        if (this.utilityLayerScene.activeCamera) {\r\n            // Set the camera's scene to utility layers scene\r\n            const oldScene = this.utilityLayerScene.activeCamera.getScene();\r\n            const camera = this.utilityLayerScene.activeCamera;\r\n            camera._scene = this.utilityLayerScene;\r\n            if (camera.leftCamera) {\r\n                camera.leftCamera._scene = this.utilityLayerScene;\r\n            }\r\n            if (camera.rightCamera) {\r\n                camera.rightCamera._scene = this.utilityLayerScene;\r\n            }\r\n\r\n            this.utilityLayerScene.render(false);\r\n\r\n            // Reset camera's scene back to original\r\n            camera._scene = oldScene;\r\n            if (camera.leftCamera) {\r\n                camera.leftCamera._scene = oldScene;\r\n            }\r\n            if (camera.rightCamera) {\r\n                camera.rightCamera._scene = oldScene;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disposes of the renderer\r\n     */\r\n    public dispose() {\r\n        this.onPointerOutObservable.clear();\r\n\r\n        if (this._afterRenderObserver) {\r\n            this.originalScene.onAfterCameraRenderObservable.remove(this._afterRenderObserver);\r\n        }\r\n        if (this._sceneDisposeObserver) {\r\n            this.originalScene.onDisposeObservable.remove(this._sceneDisposeObserver);\r\n        }\r\n        if (this._originalPointerObserver) {\r\n            this.originalScene.onPrePointerObservable.remove(this._originalPointerObserver);\r\n        }\r\n        this.utilityLayerScene.dispose();\r\n    }\r\n\r\n    private _updateCamera() {\r\n        this.utilityLayerScene.cameraToUseForPointers = this.getRenderCamera();\r\n        this.utilityLayerScene.activeCamera = this.getRenderCamera();\r\n    }\r\n}\r\n"]}