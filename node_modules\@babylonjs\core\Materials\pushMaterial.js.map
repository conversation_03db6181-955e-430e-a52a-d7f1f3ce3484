{"version": 3, "file": "pushMaterial.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Materials/pushMaterial.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAG9C,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAGjD;;;GAGG;AACH,MAAM,OAAO,YAAa,SAAQ,QAAQ;IAKtC,YAAY,IAAY,EAAE,KAAa,EAAE,sBAAsB,GAAG,IAAI;QAClE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAHb,kBAAa,GAAW,IAAI,MAAM,EAAE,CAAC;QAI3C,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;IAC1D,CAAC;IAEM,SAAS;QACZ,OAAO,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAc,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,EAAG,CAAC;IACnF,CAAC;IAEM,OAAO,CAAC,IAAmB,EAAE,YAAsB;QACtD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAChD,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;IACzE,CAAC;IAES,kBAAkB,CAAC,OAAgB;QACzC,MAAM,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC;QACxC,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,EAAE;YAC1D,IAAI,OAAO,CAAC,SAAS,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,EAAE;gBACrD,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,KAAa;QACpC,IAAI,CAAC,aAAc,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,YAAoB;QAC5C,IAAI,CAAC,aAAc,CAAC,SAAS,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;IAChE,CAAC;IAEM,IAAI,CAAC,KAAa,EAAE,IAAW;QAClC,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QAED,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC;IAES,UAAU,CAAC,IAAW,EAAE,SAA2B,IAAI,EAAE,OAAiB;QAChF,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,QAAQ,EAAE,CAAC,aAAa,GAAG,MAAM,CAAC;QACvC,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,YAAY,CAAC,sBAAsB,GAAG,KAAK,CAAC;SACvD;aAAM;YACH,IAAI,CAAC,YAAY,CAAC,sBAAsB,GAAG,KAAK,CAAC;SACpD;IACL,CAAC;IAES,WAAW,CAAC,KAAY,EAAE,MAAc,EAAE,OAAgB,EAAE,UAAU,GAAG,CAAC;QAChF,OAAO,OAAO,CAAC,YAAY,CAAC,sBAAsB,IAAI,KAAK,CAAC,uBAAuB,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAClH,CAAC;IAEM,OAAO,CAAC,kBAA4B,EAAE,oBAA8B,EAAE,cAAwB;QACjG,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAC/B,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAAC;IAC5E,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Matrix } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport { Material } from \"../Materials/material\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\n/**\r\n * Base class of materials working in push mode in babylon JS\r\n * @internal\r\n */\r\nexport class PushMaterial extends Material {\r\n    protected _activeEffect?: Effect;\r\n\r\n    protected _normalMatrix: Matrix = new Matrix();\r\n\r\n    constructor(name: string, scene?: Scene, storeEffectOnSubMeshes = true) {\r\n        super(name, scene);\r\n        this._storeEffectOnSubMeshes = storeEffectOnSubMeshes;\r\n    }\r\n\r\n    public getEffect(): Effect {\r\n        return this._storeEffectOnSubMeshes ? this._activeEffect! : super.getEffect()!;\r\n    }\r\n\r\n    public isReady(mesh?: AbstractMesh, useInstances?: boolean): boolean {\r\n        if (!mesh) {\r\n            return false;\r\n        }\r\n\r\n        if (!this._storeEffectOnSubMeshes) {\r\n            return true;\r\n        }\r\n\r\n        if (!mesh.subMeshes || mesh.subMeshes.length === 0) {\r\n            return true;\r\n        }\r\n\r\n        return this.isReadyForSubMesh(mesh, mesh.subMeshes[0], useInstances);\r\n    }\r\n\r\n    protected _isReadyForSubMesh(subMesh: SubMesh) {\r\n        const defines = subMesh.materialDefines;\r\n        if (!this.checkReadyOnEveryCall && subMesh.effect && defines) {\r\n            if (defines._renderId === this.getScene().getRenderId()) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Binds the given world matrix to the active effect\r\n     *\r\n     * @param world the matrix to bind\r\n     */\r\n    public bindOnlyWorldMatrix(world: Matrix): void {\r\n        this._activeEffect!.setMatrix(\"world\", world);\r\n    }\r\n\r\n    /**\r\n     * Binds the given normal matrix to the active effect\r\n     *\r\n     * @param normalMatrix the matrix to bind\r\n     */\r\n    public bindOnlyNormalMatrix(normalMatrix: Matrix): void {\r\n        this._activeEffect!.setMatrix(\"normalMatrix\", normalMatrix);\r\n    }\r\n\r\n    public bind(world: Matrix, mesh?: Mesh): void {\r\n        if (!mesh) {\r\n            return;\r\n        }\r\n\r\n        this.bindForSubMesh(world, mesh, mesh.subMeshes[0]);\r\n    }\r\n\r\n    protected _afterBind(mesh?: Mesh, effect: Nullable<Effect> = null, subMesh?: SubMesh): void {\r\n        super._afterBind(mesh, effect, subMesh);\r\n        this.getScene()._cachedEffect = effect;\r\n        if (subMesh) {\r\n            subMesh._drawWrapper._forceRebindOnNextCall = false;\r\n        } else {\r\n            this._drawWrapper._forceRebindOnNextCall = false;\r\n        }\r\n    }\r\n\r\n    protected _mustRebind(scene: Scene, effect: Effect, subMesh: SubMesh, visibility = 1): boolean {\r\n        return subMesh._drawWrapper._forceRebindOnNextCall || scene.isCachedMaterialInvalid(this, effect, visibility);\r\n    }\r\n\r\n    public dispose(forceDisposeEffect?: boolean, forceDisposeTextures?: boolean, notBoundToMesh?: boolean) {\r\n        this._activeEffect = undefined;\r\n        super.dispose(forceDisposeEffect, forceDisposeTextures, notBoundToMesh);\r\n    }\r\n}\r\n"]}