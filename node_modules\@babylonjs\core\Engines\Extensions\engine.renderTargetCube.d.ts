import type { RenderTargetWrapper } from "../renderTargetWrapper";
import type { RenderTargetCreationOptions } from "../../Materials/Textures/textureCreationOptions";
declare module "../../Engines/thinEngine" {
    interface ThinEngine {
        /**
         * Creates a new render target cube wrapper
         * @param size defines the size of the texture
         * @param options defines the options used to create the texture
         * @returns a new render target cube wrapper
         */
        createRenderTargetCubeTexture(size: number, options?: RenderTargetCreationOptions): RenderTargetWrapper;
    }
}
