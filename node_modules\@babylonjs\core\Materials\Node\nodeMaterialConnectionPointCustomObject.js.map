{"version": 3, "file": "nodeMaterialConnectionPointCustomObject.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Node/nodeMaterialConnectionPointCustomObject.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,2BAA2B,EAAE,8CAA8C,EAAE,MAAM,oCAAoC,CAAC;AAGjI;;GAEG;AACH,MAAM,OAAO,uCAAqE,SAAQ,2BAA2B;IACjH;;;;;;;OAOG;IACH,YACI,IAAY,EACZ,UAA6B,EAC7B,SAA+C;IAC/C,YAAY;IACL,UAAqC,EACpC,UAAkB;QAE1B,KAAK,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QAH5B,eAAU,GAAV,UAAU,CAA2B;QACpC,eAAU,GAAV,UAAU,CAAQ;QAI1B,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,eAA4C;QACvE,OAAO,eAAe,YAAY,uCAAuC,IAAI,eAAe,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU;YACvH,CAAC,CAAC,8CAA8C,CAAC,UAAU;YAC3D,CAAC,CAAC,8CAA8C,CAAC,gBAAgB,CAAC;IAC1E,CAAC;IAED;;;;OAIG;IACI,sBAAsB;QACzB,OAAO,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC;CACJ", "sourcesContent": ["import type { NodeMaterialBlock } from \"./nodeMaterialBlock\";\r\nimport type { NodeMaterialConnectionPointDirection } from \"./nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPoint, NodeMaterialConnectionPointCompatibilityStates } from \"./nodeMaterialBlockConnectionPoint\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * Defines a connection point to be used for points with a custom object type\r\n */\r\nexport class NodeMaterialConnectionPointCustomObject<T extends NodeMaterialBlock> extends NodeMaterialConnectionPoint {\r\n    /**\r\n     * Creates a new connection point\r\n     * @param name defines the connection point name\r\n     * @param ownerBlock defines the block hosting this connection point\r\n     * @param direction defines the direction of the connection point\r\n     * @param _blockType\r\n     * @param _blockName\r\n     */\r\n    public constructor(\r\n        name: string,\r\n        ownerBlock: NodeMaterialBlock,\r\n        direction: NodeMaterialConnectionPointDirection,\r\n        // @internal\r\n        public _blockType: new (...args: any[]) => T,\r\n        private _blockName: string\r\n    ) {\r\n        super(name, ownerBlock, direction);\r\n\r\n        this.needDualDirectionValidation = true;\r\n    }\r\n\r\n    /**\r\n     * Gets a number indicating if the current point can be connected to another point\r\n     * @param connectionPoint defines the other connection point\r\n     * @returns a number defining the compatibility state\r\n     */\r\n    public checkCompatibilityState(connectionPoint: NodeMaterialConnectionPoint): NodeMaterialConnectionPointCompatibilityStates {\r\n        return connectionPoint instanceof NodeMaterialConnectionPointCustomObject && connectionPoint._blockName === this._blockName\r\n            ? NodeMaterialConnectionPointCompatibilityStates.Compatible\r\n            : NodeMaterialConnectionPointCompatibilityStates.TypeIncompatible;\r\n    }\r\n\r\n    /**\r\n     * Creates a block suitable to be used as an input for this input point.\r\n     * If null is returned, a block based on the point type will be created.\r\n     * @returns The returned string parameter is the name of the output point of NodeMaterialBlock (first parameter of the returned array) that can be connected to the input\r\n     */\r\n    public createCustomInputBlock(): Nullable<[NodeMaterialBlock, string]> {\r\n        return [new this._blockType(this._blockName), this.name];\r\n    }\r\n}\r\n"]}