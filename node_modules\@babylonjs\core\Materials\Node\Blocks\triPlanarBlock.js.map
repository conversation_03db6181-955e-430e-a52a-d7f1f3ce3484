{"version": 3, "file": "triPlanarBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/triPlanarBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAEvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAE7E,OAAO,EAAE,oCAAoC,EAAE,MAAM,qCAAqC,CAAC;AAG3F,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAG/C,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AAEjD,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AACvD,OAAO,iDAAiD,CAAC;AACzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAC3D,OAAO,EAAE,uCAAuC,EAAE,MAAM,4CAA4C,CAAC;AACrG,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAC3D,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,mCAAmC,CAAC;AAEnG;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,iBAAiB;IAejD;;OAEG;IACH,IAAW,OAAO;QACd,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACzB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,UAA+B,CAAA,CAAC,OAAO,CAAC;SAC/E;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,OAA0B;QACzC,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;YAC3B,OAAO;SACV;QAED,MAAM,KAAK,GAAG,OAAO,EAAE,QAAQ,EAAE,IAAI,WAAW,CAAC,gBAAgB,CAAC;QAElE,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE;YACnB,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,QAAS,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,OAAO,IAAI,KAAK,EAAE;YAClB,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC1B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,UAA+B,CAAA,CAAC,OAAO,CAAC;SAChF;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,IAAI,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE;YAC3B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,UAA+B,CAAA,CAAC,OAAO,CAAC;SAChF;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,oBAAoB,CAAC,eAAsD;QACjF,OAAO,eAAe,EAAE,WAAW,CAAC,CAAC,CAAE,eAAe,CAAC,cAAe,CAAC,UAA+B,CAAC,CAAC,CAAC,IAAI,CAAC;IAClH,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChE,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC,WAAW,CAAC;SACvC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW,IAAI,IAAI,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW,IAAI,IAAI,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACnC,CAAC;IAGD;;OAEG;IACH,IAAW,mBAAmB,CAAC,KAAc;QACzC,IAAI,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE;YACrC,OAAO;SACV;QAED,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,WAAW,CAAC,gBAAgB,CAAC;YACtE,KAAK,EAAE,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACxE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IACD,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAGD;;OAEG;IACH,IAAW,oBAAoB,CAAC,KAAc;QAC1C,IAAI,KAAK,KAAK,IAAI,CAAC,qBAAqB,EAAE;YACtC,OAAO;SACV;QAED,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,WAAW,CAAC,gBAAgB,CAAC;YACtE,KAAK,EAAE,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACxE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IACD,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAOD;;;;OAIG;IACH,YAAmB,IAAY,EAAE,WAAW,GAAG,KAAK;QAChD,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QApJlD;;WAEG;QAEI,kBAAa,GAAY,KAAK,CAAC;QA2F9B,yBAAoB,GAAG,KAAK,CAAC;QAqB7B,0BAAqB,GAAG,KAAK,CAAC;QAqBtC;;WAEG;QACI,+BAA0B,GAAG,KAAK,CAAC;QAUtC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,qCAAqC,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACxF,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,qCAAqC,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACtF,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACnF,IAAI,CAAC,aAAa,CACd,QAAQ,EACR,qCAAqC,CAAC,MAAM,EAC5C,IAAI,EACJ,wBAAwB,CAAC,iBAAiB,EAC1C,IAAI,uCAAuC,CAAC,QAAQ,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,EAAE,gBAAgB,EAAE,kBAAkB,CAAC,CAChJ,CAAC;QACF,IAAI,CAAC,aAAa,CACd,SAAS,EACT,qCAAqC,CAAC,MAAM,EAC5C,IAAI,EACJ,wBAAwB,CAAC,iBAAiB,EAC1C,IAAI,uCAAuC,CAAC,SAAS,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,EAAE,gBAAgB,EAAE,kBAAkB,CAAC,CACjJ,CAAC;QACF,IAAI,CAAC,WAAW,EAAE;YACd,IAAI,CAAC,aAAa,CACd,SAAS,EACT,qCAAqC,CAAC,MAAM,EAC5C,IAAI,EACJ,wBAAwB,CAAC,iBAAiB,EAC1C,IAAI,uCAAuC,CAAC,SAAS,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,EAAE,gBAAgB,EAAE,kBAAkB,CAAC,CACjJ,CAAC;SACL;QAED,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAC5G,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAC3G,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAExG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAE5G,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,CACtD,qCAAqC,CAAC,MAAM,GAAG,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,CAC/I,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,CACtD,qCAAqC,CAAC,MAAM,GAAG,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,CAC/I,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,cAAc,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QAC9F,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAC5B,OAAO;SACV;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;QACrF,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;QAEtF,0EAA0E;QAC1E,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACxD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC5D,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE;YACtD,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,IAAI,CAAC,MAAc;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QAED,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE3D,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACtD;IACL,CAAC;IAES,sBAAsB,CAAC,KAA6B;QAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,WAAW,CAAC;QACtD,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,WAAW,CAAC;QAEtD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,KAAK,CAAC;QAE7F,MAAM,CAAC,GAAG,KAAK,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,KAAK,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,KAAK,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,KAAK,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,KAAK,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,GAAG,GAAG,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC9C,MAAM,GAAG,GAAG,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC9C,MAAM,GAAG,GAAG,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAE9C,KAAK,CAAC,iBAAiB,IAAI;mBAChB,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB;;mBAEzC,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,sBAAsB;mBAC7C,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,sBAAsB;mBAC7C,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,sBAAsB;SACvD,CAAC;QAEF,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,KAAK,CAAC,iBAAiB,IAAI;kBACrB,GAAG,SAAS,GAAG;;sBAEX,CAAC;sBACD,GAAG,SAAS,GAAG;;sBAEf,CAAC;sBACD,GAAG,SAAS,GAAG;;sBAEf,CAAC;sBACD,GAAG,SAAS,GAAG;;aAExB,CAAC;SACL;QAED,KAAK,CAAC,iBAAiB,IAAI;mBAChB,CAAC,gBAAgB,WAAW,KAAK,GAAG;mBACpC,CAAC,gBAAgB,YAAY,KAAK,GAAG;mBACrC,CAAC,gBAAgB,YAAY,KAAK,GAAG;;;mBAGrC,CAAC,cAAc,CAAC,WAAW,SAAS;;;mBAGpC,IAAI,CAAC,gBAAgB,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC;SACrG,CAAC;IACN,CAAC;IAEO,uBAAuB,CAAC,KAA6B,EAAE,MAAmC,EAAE,OAAe;QAC/G,IAAI,OAAO,KAAK,GAAG,EAAE;YACjB,6CAA6C;YAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;gBAC3C,KAAK,CAAC,iBAAiB,IAAI,UAAU,IAAI,CAAC,iBAAiB;sBACrD,MAAM,CAAC,sBAAsB,mBAAmB,MAAM,CAAC,sBAAsB;;iBAElF,CAAC;aACL;YAED,KAAK,CAAC,iBAAiB,IAAI,UAAU,IAAI,CAAC,gBAAgB;kBACpD,MAAM,CAAC,sBAAsB,oBAAoB,MAAM,CAAC,sBAAsB;;aAEnF,CAAC;SACL;IACL,CAAC;IAEO,YAAY,CAAC,KAA6B,EAAE,MAAmC,EAAE,OAAe;QACpG,IAAI,UAAU,GAAG,EAAE,CAAC;QAEpB,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClC,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC9C;QAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC,gBAAgB,IAAI,OAAO,GAAG,UAAU,KAAK,CAAC;QACzH,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,cAAe,CAAC,UAA8B,CAAC;SAClF;aAAM;YACH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;QAED,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAEtE,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE1D,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QACtE,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAC9D,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAE5D,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;YAEtE,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC3C;QAED,eAAe;QACf,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAE5D,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QAE7D,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAEnC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;gBAChD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;aACjD;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,0BAA0B,IAAI,CAAC,mBAAmB,KAAK,CAAC;QAC/F,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,2BAA2B,IAAI,CAAC,oBAAoB,KAAK,CAAC;QACjG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,iCAAiC,IAAI,CAAC,0BAA0B,KAAK,CAAC;QAC7G,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,oBAAoB,IAAI,CAAC,aAAa,KAAK,CAAC;QAEnF,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO,UAAU,CAAC;SACrB;QAED,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,mCAAmC,IAAI,CAAC,OAAO,CAAC,IAAI,YAAY,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,YAAY,MAAM,CAAC;QAC1L,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,oBAAoB,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,CAAC;QACnF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,oBAAoB,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,CAAC;QACnF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,mBAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,mBAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,mBAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,sBAAsB,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAC;QACvF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,sBAAsB,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAC;QACvF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,qBAAqB,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC;QACrF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,qBAAqB,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC;QACrF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,8BAA8B,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,CAAC;QAEvG,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACnE,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,0BAA0B,GAAG,IAAI,CAAC,0BAA0B,CAAC;QACjF,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvD,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,cAAc,EAAE;YACxH,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;SAC1D;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,mBAAmB,CAAC;QACnE,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;QACvE,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,mBAAmB,CAAC,0BAA0B,CAAC;QACnF,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,mBAAmB,CAAC,aAAa,CAAC;QAEzD,IAAI,mBAAmB,CAAC,OAAO,IAAI,CAAC,YAAY,CAAC,wBAAwB,IAAI,mBAAmB,CAAC,OAAO,CAAC,GAAG,KAAK,SAAS,EAAE;YACxH,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YAChF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAY,CAAC;SACxF;IACL,CAAC;CACJ;AAtfU;IADN,sBAAsB,CAAC,iBAAiB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;qDACjF;AAwf1C,aAAa,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport type { AbstractMesh } from \"../../../Meshes/abstractMesh\";\r\nimport type { NodeMaterialDefines } from \"../nodeMaterial\";\r\nimport { NodeMaterial } from \"../nodeMaterial\";\r\nimport type { Effect } from \"../../effect\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport { Texture } from \"../../Textures/texture\";\r\nimport type { Scene } from \"../../../scene\";\r\nimport { Constants } from \"../../../Engines/constants\";\r\nimport \"../../../Shaders/ShadersInclude/helperFunctions\";\r\nimport { ImageSourceBlock } from \"./Dual/imageSourceBlock\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../nodeMaterialConnectionPointCustomObject\";\r\nimport { EngineStore } from \"../../../Engines/engineStore\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../Decorators/nodeDecorator\";\r\n\r\n/**\r\n * Block used to read a texture with triplanar mapping (see \"boxmap\" in https://iquilezles.org/articles/biplanar/)\r\n */\r\nexport class TriPlanarBlock extends NodeMaterialBlock {\r\n    private _linearDefineName: string;\r\n    private _gammaDefineName: string;\r\n    protected _tempTextureRead: string;\r\n    private _samplerName: string;\r\n    private _textureInfoName: string;\r\n    private _imageSource: Nullable<ImageSourceBlock>;\r\n\r\n    /**\r\n     * Project the texture(s) for a better fit to a cube\r\n     */\r\n    @editableInPropertyPage(\"Project as cube\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public projectAsCube: boolean = false;\r\n\r\n    protected _texture: Nullable<Texture>;\r\n    /**\r\n     * Gets or sets the texture associated with the node\r\n     */\r\n    public get texture(): Nullable<Texture> {\r\n        if (this.source.isConnected) {\r\n            return (this.source.connectedPoint?.ownerBlock as ImageSourceBlock).texture;\r\n        }\r\n        return this._texture;\r\n    }\r\n\r\n    public set texture(texture: Nullable<Texture>) {\r\n        if (this._texture === texture) {\r\n            return;\r\n        }\r\n\r\n        const scene = texture?.getScene() ?? EngineStore.LastCreatedScene;\r\n\r\n        if (!texture && scene) {\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this._texture!);\r\n            });\r\n        }\r\n\r\n        this._texture = texture;\r\n\r\n        if (texture && scene) {\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(texture);\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the textureY associated with the node\r\n     */\r\n    public get textureY(): Nullable<Texture> {\r\n        if (this.sourceY.isConnected) {\r\n            return (this.sourceY.connectedPoint?.ownerBlock as ImageSourceBlock).texture;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets the textureZ associated with the node\r\n     */\r\n    public get textureZ(): Nullable<Texture> {\r\n        if (this.sourceZ?.isConnected) {\r\n            return (this.sourceY.connectedPoint?.ownerBlock as ImageSourceBlock).texture;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    protected _getImageSourceBlock(connectionPoint: Nullable<NodeMaterialConnectionPoint>): Nullable<ImageSourceBlock> {\r\n        return connectionPoint?.isConnected ? (connectionPoint.connectedPoint!.ownerBlock as ImageSourceBlock) : null;\r\n    }\r\n\r\n    /**\r\n     * Gets the sampler name associated with this texture\r\n     */\r\n    public get samplerName(): string {\r\n        const imageSourceBlock = this._getImageSourceBlock(this.source);\r\n        if (imageSourceBlock) {\r\n            return imageSourceBlock.samplerName;\r\n        }\r\n        return this._samplerName;\r\n    }\r\n\r\n    /**\r\n     * Gets the samplerY name associated with this texture\r\n     */\r\n    public get samplerYName(): Nullable<string> {\r\n        return this._getImageSourceBlock(this.sourceY)?.samplerName ?? null;\r\n    }\r\n\r\n    /**\r\n     * Gets the samplerZ name associated with this texture\r\n     */\r\n    public get samplerZName(): Nullable<string> {\r\n        return this._getImageSourceBlock(this.sourceZ)?.samplerName ?? null;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that this block is linked to an ImageSourceBlock\r\n     */\r\n    public get hasImageSource(): boolean {\r\n        return this.source.isConnected;\r\n    }\r\n\r\n    private _convertToGammaSpace = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if content needs to be converted to gamma space\r\n     */\r\n    public set convertToGammaSpace(value: boolean) {\r\n        if (value === this._convertToGammaSpace) {\r\n            return;\r\n        }\r\n\r\n        this._convertToGammaSpace = value;\r\n        if (this.texture) {\r\n            const scene = this.texture.getScene() ?? EngineStore.LastCreatedScene;\r\n            scene?.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this.texture!);\r\n            });\r\n        }\r\n    }\r\n    public get convertToGammaSpace(): boolean {\r\n        return this._convertToGammaSpace;\r\n    }\r\n\r\n    private _convertToLinearSpace = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if content needs to be converted to linear space\r\n     */\r\n    public set convertToLinearSpace(value: boolean) {\r\n        if (value === this._convertToLinearSpace) {\r\n            return;\r\n        }\r\n\r\n        this._convertToLinearSpace = value;\r\n        if (this.texture) {\r\n            const scene = this.texture.getScene() ?? EngineStore.LastCreatedScene;\r\n            scene?.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this.texture!);\r\n            });\r\n        }\r\n    }\r\n    public get convertToLinearSpace(): boolean {\r\n        return this._convertToLinearSpace;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if multiplication of texture with level should be disabled\r\n     */\r\n    public disableLevelMultiplication = false;\r\n\r\n    /**\r\n     * Create a new TriPlanarBlock\r\n     * @param name defines the block name\r\n     * @param hideSourceZ defines a boolean indicating that normal Z should not be used (false by default)\r\n     */\r\n    public constructor(name: string, hideSourceZ = false) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerInput(\"position\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false);\r\n        this.registerInput(\"normal\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false);\r\n        this.registerInput(\"sharpness\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\r\n            \"source\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.VertexAndFragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"source\", this, NodeMaterialConnectionPointDirection.Input, ImageSourceBlock, \"ImageSourceBlock\")\r\n        );\r\n        this.registerInput(\r\n            \"sourceY\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.VertexAndFragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"sourceY\", this, NodeMaterialConnectionPointDirection.Input, ImageSourceBlock, \"ImageSourceBlock\")\r\n        );\r\n        if (!hideSourceZ) {\r\n            this.registerInput(\r\n                \"sourceZ\",\r\n                NodeMaterialBlockConnectionPointTypes.Object,\r\n                true,\r\n                NodeMaterialBlockTargets.VertexAndFragment,\r\n                new NodeMaterialConnectionPointCustomObject(\"sourceZ\", this, NodeMaterialConnectionPointDirection.Input, ImageSourceBlock, \"ImageSourceBlock\")\r\n            );\r\n        }\r\n\r\n        this.registerOutput(\"rgba\", NodeMaterialBlockConnectionPointTypes.Color4, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"rgb\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"r\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"g\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"b\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"a\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerOutput(\"level\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this._inputs[0].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n        this._inputs[1].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"TriPlanarBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the position input component\r\n     */\r\n    public get position(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the normal input component\r\n     */\r\n    public get normal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the sharpness input component\r\n     */\r\n    public get sharpness(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the source input component\r\n     */\r\n    public get source(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the sourceY input component\r\n     */\r\n    public get sourceY(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the sourceZ input component\r\n     */\r\n    public get sourceZ(): Nullable<NodeMaterialConnectionPoint> {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgba output component\r\n     */\r\n    public get rgba(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgb output component\r\n     */\r\n    public get rgb(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the r output component\r\n     */\r\n    public get r(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the g output component\r\n     */\r\n    public get g(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the b output component\r\n     */\r\n    public get b(): NodeMaterialConnectionPoint {\r\n        return this._outputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the a output component\r\n     */\r\n    public get a(): NodeMaterialConnectionPoint {\r\n        return this._outputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the level output component\r\n     */\r\n    public get level(): NodeMaterialConnectionPoint {\r\n        return this._outputs[6];\r\n    }\r\n\r\n    public prepareDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        if (!defines._areTexturesDirty) {\r\n            return;\r\n        }\r\n\r\n        const toGamma = this.convertToGammaSpace && this.texture && !this.texture.gammaSpace;\r\n        const toLinear = this.convertToLinearSpace && this.texture && this.texture.gammaSpace;\r\n\r\n        // Not a bug... Name defines the texture space not the required conversion\r\n        defines.setValue(this._linearDefineName, toGamma, true);\r\n        defines.setValue(this._gammaDefineName, toLinear, true);\r\n    }\r\n\r\n    public isReady() {\r\n        if (this.texture && !this.texture.isReadyOrNotBlocking()) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public bind(effect: Effect) {\r\n        if (!this.texture) {\r\n            return;\r\n        }\r\n\r\n        effect.setFloat(this._textureInfoName, this.texture.level);\r\n\r\n        if (!this._imageSource) {\r\n            effect.setTexture(this._samplerName, this.texture);\r\n        }\r\n    }\r\n\r\n    protected _generateTextureLookup(state: NodeMaterialBuildState): void {\r\n        const samplerName = this.samplerName;\r\n        const samplerYName = this.samplerYName ?? samplerName;\r\n        const samplerZName = this.samplerZName ?? samplerName;\r\n\r\n        const sharpness = this.sharpness.isConnected ? this.sharpness.associatedVariableName : \"1.0\";\r\n\r\n        const x = state._getFreeVariableName(\"x\");\r\n        const y = state._getFreeVariableName(\"y\");\r\n        const z = state._getFreeVariableName(\"z\");\r\n        const w = state._getFreeVariableName(\"w\");\r\n        const n = state._getFreeVariableName(\"n\");\r\n        const uvx = state._getFreeVariableName(\"uvx\");\r\n        const uvy = state._getFreeVariableName(\"uvy\");\r\n        const uvz = state._getFreeVariableName(\"uvz\");\r\n\r\n        state.compilationString += `\r\n            vec3 ${n} = ${this.normal.associatedVariableName}.xyz;\r\n\r\n            vec2 ${uvx} = ${this.position.associatedVariableName}.yz;\r\n            vec2 ${uvy} = ${this.position.associatedVariableName}.zx;\r\n            vec2 ${uvz} = ${this.position.associatedVariableName}.xy;\r\n        `;\r\n\r\n        if (this.projectAsCube) {\r\n            state.compilationString += `\r\n                ${uvx}.xy = ${uvx}.yx;\r\n\r\n                if (${n}.x >= 0.0) {\r\n                    ${uvx}.x = -${uvx}.x;\r\n                }\r\n                if (${n}.y < 0.0) {\r\n                    ${uvy}.y = -${uvy}.y;\r\n                }\r\n                if (${n}.z < 0.0) {\r\n                    ${uvz}.x = -${uvz}.x;\r\n                }\r\n            `;\r\n        }\r\n\r\n        state.compilationString += `\r\n            vec4 ${x} = texture2D(${samplerName}, ${uvx});\r\n            vec4 ${y} = texture2D(${samplerYName}, ${uvy});\r\n            vec4 ${z} = texture2D(${samplerZName}, ${uvz});\r\n           \r\n            // blend weights\r\n            vec3 ${w} = pow(abs(${n}), vec3(${sharpness}));\r\n\r\n            // blend and return\r\n            vec4 ${this._tempTextureRead} = (${x}*${w}.x + ${y}*${w}.y + ${z}*${w}.z) / (${w}.x + ${w}.y + ${w}.z);        \r\n        `;\r\n    }\r\n\r\n    private _generateConversionCode(state: NodeMaterialBuildState, output: NodeMaterialConnectionPoint, swizzle: string): void {\r\n        if (swizzle !== \"a\") {\r\n            // no conversion if the output is \"a\" (alpha)\r\n            if (!this.texture || !this.texture.gammaSpace) {\r\n                state.compilationString += `#ifdef ${this._linearDefineName}\r\n                    ${output.associatedVariableName} = toGammaSpace(${output.associatedVariableName});\r\n                    #endif\r\n                `;\r\n            }\r\n\r\n            state.compilationString += `#ifdef ${this._gammaDefineName}\r\n                ${output.associatedVariableName} = toLinearSpace(${output.associatedVariableName});\r\n                #endif\r\n            `;\r\n        }\r\n    }\r\n\r\n    private _writeOutput(state: NodeMaterialBuildState, output: NodeMaterialConnectionPoint, swizzle: string) {\r\n        let complement = \"\";\r\n\r\n        if (!this.disableLevelMultiplication) {\r\n            complement = ` * ${this._textureInfoName}`;\r\n        }\r\n\r\n        state.compilationString += `${this._declareOutput(output, state)} = ${this._tempTextureRead}.${swizzle}${complement};\\n`;\r\n        this._generateConversionCode(state, output, swizzle);\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (this.source.isConnected) {\r\n            this._imageSource = this.source.connectedPoint!.ownerBlock as ImageSourceBlock;\r\n        } else {\r\n            this._imageSource = null;\r\n        }\r\n\r\n        this._textureInfoName = state._getFreeVariableName(\"textureInfoName\");\r\n\r\n        this.level.associatedVariableName = this._textureInfoName;\r\n\r\n        this._tempTextureRead = state._getFreeVariableName(\"tempTextureRead\");\r\n        this._linearDefineName = state._getFreeDefineName(\"ISLINEAR\");\r\n        this._gammaDefineName = state._getFreeDefineName(\"ISGAMMA\");\r\n\r\n        if (!this._imageSource) {\r\n            this._samplerName = state._getFreeVariableName(this.name + \"Sampler\");\r\n\r\n            state._emit2DSampler(this._samplerName);\r\n        }\r\n\r\n        // Declarations\r\n        state.sharedData.blockingBlocks.push(this);\r\n        state.sharedData.textureBlocks.push(this);\r\n        state.sharedData.blocksWithDefines.push(this);\r\n        state.sharedData.bindableBlocks.push(this);\r\n\r\n        const comments = `//${this.name}`;\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n\r\n        state._emitUniformFromString(this._textureInfoName, \"float\");\r\n\r\n        this._generateTextureLookup(state);\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints && output.name !== \"level\") {\r\n                this._writeOutput(state, output, output.name);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        codeString += `${this._codeVariableName}.convertToGammaSpace = ${this.convertToGammaSpace};\\n`;\r\n        codeString += `${this._codeVariableName}.convertToLinearSpace = ${this.convertToLinearSpace};\\n`;\r\n        codeString += `${this._codeVariableName}.disableLevelMultiplication = ${this.disableLevelMultiplication};\\n`;\r\n        codeString += `${this._codeVariableName}.projectAsCube = ${this.projectAsCube};\\n`;\r\n\r\n        if (!this.texture) {\r\n            return codeString;\r\n        }\r\n\r\n        codeString += `${this._codeVariableName}.texture = new BABYLON.Texture(\"${this.texture.name}\", null, ${this.texture.noMipmap}, ${this.texture.invertY}, ${this.texture.samplingMode});\\n`;\r\n        codeString += `${this._codeVariableName}.texture.wrapU = ${this.texture.wrapU};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.wrapV = ${this.texture.wrapV};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.uAng = ${this.texture.uAng};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.vAng = ${this.texture.vAng};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.wAng = ${this.texture.wAng};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.uOffset = ${this.texture.uOffset};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.vOffset = ${this.texture.vOffset};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.uScale = ${this.texture.uScale};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.vScale = ${this.texture.vScale};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.coordinatesMode = ${this.texture.coordinatesMode};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.convertToGammaSpace = this.convertToGammaSpace;\r\n        serializationObject.convertToLinearSpace = this.convertToLinearSpace;\r\n        serializationObject.disableLevelMultiplication = this.disableLevelMultiplication;\r\n        serializationObject.projectAsCube = this.projectAsCube;\r\n        if (!this.hasImageSource && this.texture && !this.texture.isRenderTarget && this.texture.getClassName() !== \"VideoTexture\") {\r\n            serializationObject.texture = this.texture.serialize();\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.convertToGammaSpace = serializationObject.convertToGammaSpace;\r\n        this.convertToLinearSpace = !!serializationObject.convertToLinearSpace;\r\n        this.disableLevelMultiplication = !!serializationObject.disableLevelMultiplication;\r\n        this.projectAsCube = !!serializationObject.projectAsCube;\r\n\r\n        if (serializationObject.texture && !NodeMaterial.IgnoreTexturesAtLoadTime && serializationObject.texture.url !== undefined) {\r\n            rootUrl = serializationObject.texture.url.indexOf(\"data:\") === 0 ? \"\" : rootUrl;\r\n            this.texture = Texture.Parse(serializationObject.texture, scene, rootUrl) as Texture;\r\n        }\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.TriPlanarBlock\", TriPlanarBlock);\r\n"]}