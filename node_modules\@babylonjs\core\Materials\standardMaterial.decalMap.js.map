{"version": 3, "file": "standardMaterial.decalMap.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Materials/standardMaterial.decalMap.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,qBAAqB,EAAE,MAAM,kCAAkC,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AActD,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,SAAS,EAAE,UAAU,EAAE;IAC1D,GAAG,EAAE;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,IAAI,CAAC,yBAAyB,EAAE;gBAChC,qGAAqG;gBACrG,+EAA+E;gBAC/E,OAAO,IAAI,CAAC;aACf;YAED,IAAI,CAAC,SAAS,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC;SACpD;QACD,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { DecalMapConfiguration } from \"./material.decalMapConfiguration\";\r\nimport { StandardMaterial } from \"./standardMaterial\";\r\n\r\ndeclare module \"./standardMaterial\" {\r\n    export interface StandardMaterial {\r\n        /** @internal */\r\n        _decalMap: Nullable<DecalMapConfiguration>;\r\n\r\n        /**\r\n         * Defines the decal map parameters for the material.\r\n         */\r\n        decalMap: Nullable<DecalMapConfiguration>;\r\n    }\r\n}\r\n\r\nObject.defineProperty(StandardMaterial.prototype, \"decalMap\", {\r\n    get: function (this: StandardMaterial) {\r\n        if (!this._decalMap) {\r\n            if (this._uniformBufferLayoutBuilt) {\r\n                // Material already used to display a mesh, so it's invalid to add the decal map plugin at that point\r\n                // Returns null instead of having new DecalMapConfiguration throws an exception\r\n                return null;\r\n            }\r\n\r\n            this._decalMap = new DecalMapConfiguration(this);\r\n        }\r\n        return this._decalMap;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n"]}