{"version": 3, "file": "sceneLoader.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Loading/sceneLoader.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAMrD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAItD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAIpD,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAEzD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AA+Q1C;;GAEG;AACH,MAAM,CAAN,IAAY,oCAoBX;AApBD,WAAY,oCAAoC;IAC5C;;OAEG;IACH,iGAAS,CAAA;IAET;;OAEG;IACH,+FAAQ,CAAA;IAER;;OAEG;IACH,+FAAQ,CAAA;IAER;;OAEG;IACH,mGAAU,CAAA;AACd,CAAC,EApBW,oCAAoC,KAApC,oCAAoC,QAoB/C;AA2CD;;;GAGG;AACH,MAAM,OAAO,WAAW;IAqBpB;;OAEG;IACI,MAAM,KAAK,mCAAmC;QACjD,OAAO,gBAAgB,CAAC,mCAAmC,CAAC;IAChE,CAAC;IAEM,MAAM,KAAK,mCAAmC,CAAC,KAAc;QAChE,gBAAgB,CAAC,mCAAmC,GAAG,KAAK,CAAC;IACjE,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,iBAAiB;QAC/B,OAAO,gBAAgB,CAAC,iBAAiB,CAAC;IAC9C,CAAC;IAEM,MAAM,KAAK,iBAAiB,CAAC,KAAc;QAC9C,gBAAgB,CAAC,iBAAiB,GAAG,KAAK,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACH,gEAAgE;IACzD,MAAM,KAAK,YAAY;QAC1B,OAAO,gBAAgB,CAAC,YAAY,CAAC;IACzC,CAAC;IAED,gEAAgE;IACzD,MAAM,KAAK,YAAY,CAAC,KAAa;QACxC,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,sBAAsB;QACpC,OAAO,gBAAgB,CAAC,sBAAsB,CAAC;IACnD,CAAC;IAEM,MAAM,KAAK,sBAAsB,CAAC,KAAc;QACnD,gBAAgB,CAAC,sBAAsB,GAAG,KAAK,CAAC;IACpD,CAAC;IAaD;;;OAGG;IACI,MAAM,CAAC,gBAAgB;QAC1B,OAAO,WAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;IACtD,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,SAAiB;QACnD,MAAM,gBAAgB,GAAG,WAAW,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACnE,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QACD,MAAM,CAAC,IAAI,CACP,kCAAkC;YAC9B,SAAS;YACT,gLAAgL,CACvL,CAAC;QACF,OAAO,WAAW,CAAC,gBAAgB,EAAE,CAAC;IAC1C,CAAC;IAEO,MAAM,CAAC,uBAAuB,CAAC,IAAY;QAC/C,KAAK,MAAM,SAAS,IAAI,WAAW,CAAC,kBAAkB,EAAE;YACpD,MAAM,MAAM,GAAG,WAAW,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;YAEhE,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;gBACpD,OAAO,WAAW,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;aACpD;SACJ;QAED,OAAO,WAAW,CAAC,gBAAgB,EAAE,CAAC;IAC1C,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,aAAqB;QACtD,MAAM,mBAAmB,GAAG,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEvD,IAAI,mBAAmB,KAAK,CAAC,CAAC,EAAE;YAC5B,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;SACnE;QAED,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAEnD,MAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3F,OAAO,WAAW,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;IACzD,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,aAAqB;QAC/C,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,EAAE;YACxC,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAClC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,QAAmB,EAAE,OAAgB,EAAE,SAAe;QACrF,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;QACjE,IAAI,YAAY,GAAG,sBAAsB,GAAG,QAAQ,CAAC;QAErD,IAAI,OAAO,EAAE;YACT,YAAY,IAAI,KAAK,OAAO,EAAE,CAAC;SAClC;aAAM,IAAI,SAAS,EAAE;YAClB,YAAY,IAAI,KAAK,SAAS,EAAE,CAAC;SACpC;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAEO,MAAM,CAAC,SAAS,CACpB,QAAmB,EACnB,KAAY,EACZ,SAA0G,EAC1G,UAAoE,EACpE,OAAoD,EACpD,SAAqB,EACrB,eAAiC,EACjC,IAAY;QAEZ,MAAM,UAAU,GAAG,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAE5D,IAAI,QAAQ,CAAC,OAAO,IAAI,CAAC,eAAe,EAAE;YACtC,4CAA4C;YAC5C,MAAM,8EAA8E,CAAC;SACxF;QAED,MAAM,gBAAgB,GAAG,eAAe;YACpC,CAAC,CAAC,WAAW,CAAC,sBAAsB,CAAC,eAAe,CAAC;YACrD,CAAC,CAAC,UAAU;gBACV,CAAC,CAAC,WAAW,CAAC,uBAAuB,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACnD,CAAC,CAAC,WAAW,CAAC,qBAAqB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAExD,IAAI,QAAQ,CAAC,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;YAChD,4CAA4C;YAC5C,MAAM,8FAA8F,CAAC;SACxG;QAED,IAAI,MAAoD,CAAC;QAEzD,IAAK,gBAAgB,CAAC,MAAoC,CAAC,YAAY,KAAK,SAAS,EAAE;YACnF,MAAM,GAAI,gBAAgB,CAAC,MAAoC,CAAC,YAAY,EAAE,CAAC;SAClF;aAAM;YACH,MAAM,GAAQ,gBAAgB,CAAC,MAAM,CAAC;SACzC;QAED,IAAI,CAAC,MAAM,EAAE;YACT,4CAA4C;YAC5C,MAAM,4JAA4J,CAAC;SACtK;QAED,WAAW,CAAC,2BAA2B,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAEhE,4EAA4E;QAC5E,+EAA+E;QAC/E,IAAI,UAAU,IAAI,CAAC,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;YAChH,IAAI,MAAM,CAAC,UAAU,EAAE;gBACnB,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACpD,IAAI,MAAM,CAAC,IAAI,EAAE;oBACb,MAAM;yBACD,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE;wBAChB,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;oBAC5B,CAAC,CAAC;yBACD,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE;wBAClB,OAAO,CAAC,oCAAoC,GAAG,KAAK,EAAE,KAAK,CAAC,CAAC;oBACjE,CAAC,CAAC,CAAC;iBACV;qBAAM;oBACH,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;iBAC7B;aACJ;iBAAM;gBACH,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;aACjC;YACD,OAAO,MAAM,CAAC;SACjB;QAED,MAAM,cAAc,GAAG,gBAAgB,CAAC,QAAQ,CAAC;QAEjD,MAAM,YAAY,GAAG,CAAC,IAAS,EAAE,WAAoB,EAAE,EAAE;YACrD,IAAI,KAAK,CAAC,UAAU,EAAE;gBAClB,OAAO,CAAC,yBAAyB,CAAC,CAAC;gBACnC,OAAO;aACV;YAED,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QACzC,CAAC,CAAC;QAEF,IAAI,OAAO,GAA2B,IAAI,CAAC;QAC3C,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,MAAM,mBAAmB,GAAI,MAAc,CAAC,mBAA+E,CAAC;QAC5H,IAAI,mBAAmB,EAAE;YACrB,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACzB,cAAc,GAAG,IAAI,CAAC;gBAEtB,IAAI,OAAO,EAAE;oBACT,OAAO,CAAC,KAAK,EAAE,CAAC;oBAChB,OAAO,GAAG,IAAI,CAAC;iBAClB;gBAED,SAAS,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;SACN;QAED,MAAM,eAAe,GAAG,GAAG,EAAE;YACzB,IAAI,cAAc,EAAE;gBAChB,OAAO;aACV;YAED,MAAM,aAAa,GAAG,CAAC,OAAoB,EAAE,SAAyB,EAAE,EAAE;gBACtE,OAAO,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;YAC5C,CAAC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC,OAAO,EAAE;gBACtC,4CAA4C;gBAC5C,MAAM,kDAAkD,CAAC;aAC5D;YAED,OAAO,GAAG,MAAM,CAAC,QAAQ;gBACrB,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,EAAE,IAAI,CAAC;gBAC5J,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;QACxH,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,IAAI,oBAAoB,GAAG,MAAM,CAAC,oBAAoB,CAAC;QACvD,IAAI,oBAAoB,EAAE;YACtB,4BAA4B;YAC5B,IAAI,cAAc,GAAG,KAAK,CAAC;YAC3B,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,mCAAmC,EAAE;gBAC3D,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;oBAC1B,cAAc,GAAG,IAAI,CAAC;oBACtB,MAAM;iBACT;aACJ;YAED,oBAAoB,GAAG,CAAC,cAAc,CAAC;SAC1C;QAED,IAAI,oBAAoB,IAAI,MAAM,CAAC,sBAAsB,EAAE;YACvD,iGAAiG;YACjG,KAAK,CAAC,eAAe,GAAG,MAAM,CAAC,sBAAsB,CAAC,QAAQ,CAAC,GAAG,EAAE,eAAe,EAAE,MAAM,CAAC,oBAAoB,CAAC,CAAC;SACrH;aAAM;YACH,eAAe,EAAE,CAAC;SACrB;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,OAAe,EAAE,aAA8C;QACvF,IAAI,GAAW,CAAC;QAChB,IAAI,IAAY,CAAC;QACjB,IAAI,IAAI,GAAmB,IAAI,CAAC;QAChC,IAAI,OAAO,GAA8B,IAAI,CAAC;QAE9C,IAAI,CAAC,aAAa,EAAE;YAChB,GAAG,GAAG,OAAO,CAAC;YACd,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAClC,OAAO,GAAG,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SAC1C;aAAM,IAAK,aAAsB,CAAC,IAAI,EAAE;YACrC,MAAM,SAAS,GAAG,aAAqB,CAAC;YACxC,GAAG,GAAG,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;YACtB,IAAI,GAAG,SAAS,CAAC;SACpB;aAAM,IAAI,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE;YAC1C,GAAG,GAAG,EAAE,CAAC;YACT,IAAI,GAAG,UAAU,EAAE,CAAC;YACpB,OAAO,GAAG,aAAgC,CAAC;SAC9C;aAAM,IAAI,OAAO,aAAa,KAAK,QAAQ,IAAI,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC/E,GAAG,GAAG,aAAa,CAAC;YACpB,IAAI,GAAG,EAAE,CAAC;SACb;aAAM;YACH,MAAM,QAAQ,GAAG,aAAuB,CAAC;YACzC,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC/B,KAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,OAAO,IAAI,CAAC;aACf;YAED,GAAG,GAAG,OAAO,GAAG,QAAQ,CAAC;YACzB,IAAI,GAAG,QAAQ,CAAC;SACnB;QAED,OAAO;YACH,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,OAAO;SACV,CAAC;IACN,CAAC;IAED,mBAAmB;IAEnB;;;;OAIG;IACI,MAAM,CAAC,qBAAqB,CAAC,SAAiB;QACjD,OAAO,WAAW,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;IAChE,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,6BAA6B,CAAC,SAAiB;QACzD,OAAO,CAAC,CAAC,WAAW,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,cAAc,CAAC,MAAoD;QAC7E,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ,EAAE;YACvC,MAAM,SAAS,GAAW,MAAM,CAAC,UAAU,CAAC;YAC5C,WAAW,CAAC,kBAAkB,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,GAAG;gBACtD,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,KAAK;aAClB,CAAC;SACL;aAAM;YACH,MAAM,UAAU,GAAiC,MAAM,CAAC,UAAU,CAAC;YACnE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;gBAC1C,WAAW,CAAC,kBAAkB,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,GAAG;oBACtD,MAAM,EAAE,MAAM;oBACd,QAAQ,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,QAAQ;iBAC3C,CAAC;YACN,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,MAAM,CAAC,UAAU,CACpB,SAAc,EACd,OAAe,EACf,gBAAiD,EAAE,EACnD,QAAyB,WAAW,CAAC,gBAAgB,EACrD,YAAkD,IAAI,EACtD,aAAmE,IAAI,EACvE,UAA8E,IAAI,EAClF,kBAAoC,IAAI,EACxC,OAAe,EAAE;QAEjB,IAAI,CAAC,KAAK,EAAE;YACR,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YACrD,OAAO,IAAI,CAAC;SACf;QAED,MAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAClE,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,IAAI,CAAC;SACf;QAED,MAAM,YAAY,GAAG,EAAE,CAAC;QACxB,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAEnC,MAAM,cAAc,GAAG,GAAG,EAAE;YACxB,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,MAAM,YAAY,GAAG,CAAC,OAAgB,EAAE,SAAe,EAAE,EAAE;YACvD,MAAM,YAAY,GAAG,WAAW,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAEnF,IAAI,OAAO,EAAE;gBACT,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,IAAI,YAAY,CAAC,YAAY,EAAE,UAAU,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,CAAC;aACxG;iBAAM;gBACH,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAC3B,kCAAkC;aACrC;YAED,cAAc,EAAE,CAAC;QACrB,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,UAAU;YAC9B,CAAC,CAAC,CAAC,KAAgC,EAAE,EAAE;gBACjC,IAAI;oBACA,UAAU,CAAC,KAAK,CAAC,CAAC;iBACrB;gBAAC,OAAO,CAAC,EAAE;oBACR,YAAY,CAAC,gCAAgC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;iBACzD;YACL,CAAC;YACH,CAAC,CAAC,SAAS,CAAC;QAEhB,MAAM,cAAc,GAA+B,CAAC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,EAAE;YAC3J,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAE7C,IAAI,SAAS,EAAE;gBACX,IAAI;oBACA,SAAS,CAAC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;iBACtH;gBAAC,OAAO,CAAC,EAAE;oBACR,YAAY,CAAC,+BAA+B,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;iBACxD;aACJ;YAED,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,OAAO,WAAW,CAAC,SAAS,CACxB,QAAQ,EACR,KAAK,EACL,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE;YAC1B,IAAI,MAAM,CAAC,cAAc,EAAE;gBACvB,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;aAC3E;YAED,IAAU,MAAO,CAAC,UAAU,EAAE;gBAC1B,MAAM,YAAY,GAAuB,MAAM,CAAC;gBAChD,MAAM,MAAM,GAAmB,EAAE,CAAC;gBAClC,MAAM,eAAe,GAAsB,EAAE,CAAC;gBAC9C,MAAM,SAAS,GAAe,EAAE,CAAC;gBAEjC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,CAAC,EAAE;oBACtH,OAAO;iBACV;gBAED,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;gBACtC,cAAc,CAAC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;aAC1E;iBAAM;gBACH,MAAM,aAAa,GAA4B,MAAM,CAAC;gBACtD,aAAa;qBACR,eAAe,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,CAAC,IAAI,CAAC;qBACzF,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;oBACb,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;oBACtC,cAAc,CACV,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,eAAe,EACtB,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,eAAe,EACtB,MAAM,CAAC,cAAc,EACrB,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,cAAc,CACxB,CAAC;gBACN,CAAC,CAAC;qBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBACb,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBACvC,CAAC,CAAC,CAAC;aACV;QACL,CAAC,EACD,eAAe,EACf,YAAY,EACZ,cAAc,EACd,eAAe,EACf,IAAI,CACP,CAAC;IACN,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAC,eAAe,CACzB,SAAc,EACd,OAAe,EACf,gBAAiD,EAAE,EACnD,QAAyB,WAAW,CAAC,gBAAgB,EACrD,aAAmE,IAAI,EACvE,kBAAoC,IAAI,EACxC,OAAe,EAAE;QAEjB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,WAAW,CAAC,UAAU,CAClB,SAAS,EACT,OAAO,EACP,aAAa,EACb,KAAK,EACL,CAAC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,EAAE;gBACxG,OAAO,CAAC;oBACJ,MAAM,EAAE,MAAM;oBACd,eAAe,EAAE,eAAe;oBAChC,SAAS,EAAE,SAAS;oBACpB,eAAe,EAAE,eAAe;oBAChC,cAAc,EAAE,cAAc;oBAC9B,UAAU,EAAE,UAAU;oBACtB,MAAM,EAAE,MAAM;oBACd,cAAc,EAAE,cAAc;iBACjC,CAAC,CAAC;YACP,CAAC,EACD,UAAU,EACV,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE;gBAC1B,MAAM,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5C,CAAC,EACD,eAAe,EACf,IAAI,CACP,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,IAAI,CACd,OAAe,EACf,gBAAiD,EAAE,EACnD,SAA2B,WAAW,CAAC,iBAAiB,EACxD,YAA8C,IAAI,EAClD,aAAmE,IAAI,EACvE,UAA8E,IAAI,EAClF,kBAAoC,IAAI,EACxC,OAAe,EAAE;QAEjB,IAAI,CAAC,MAAM,EAAE;YACT,KAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC;SACf;QAED,OAAO,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;IAChI,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAC,SAAS,CACnB,OAAe,EACf,gBAAiD,EAAE,EACnD,SAA2B,WAAW,CAAC,iBAAiB,EACxD,aAAmE,IAAI,EACvE,kBAAoC,IAAI,EACxC,OAAe,EAAE;QAEjB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,WAAW,CAAC,IAAI,CACZ,OAAO,EACP,aAAa,EACb,MAAM,EACN,CAAC,KAAK,EAAE,EAAE;gBACN,OAAO,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC,EACD,UAAU,EACV,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE;gBAC1B,MAAM,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5C,CAAC,EACD,eAAe,EACf,IAAI,CACP,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,MAAM,CAChB,OAAe,EACf,gBAAiD,EAAE,EACnD,QAAyB,WAAW,CAAC,gBAAgB,EACrD,YAA8C,IAAI,EAClD,aAAmE,IAAI,EACvE,UAA8E,IAAI,EAClF,kBAAoC,IAAI,EACxC,OAAe,EAAE;QAEjB,IAAI,CAAC,KAAK,EAAE;YACR,MAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YAChD,OAAO,IAAI,CAAC;SACf;QAED,MAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAClE,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,IAAI,CAAC;SACf;QAED,MAAM,YAAY,GAAG,EAAE,CAAC;QACxB,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAEnC,MAAM,cAAc,GAAG,GAAG,EAAE;YACxB,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,IAAI,WAAW,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC9D,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAClC,KAAK,CAAC,SAAS,EAAE,CAAC,gBAAgB,EAAE,CAAC;YACrC,KAAK,CAAC,gBAAgB,CAAC,GAAG,EAAE;gBACxB,KAAK,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC;gBAClC,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;YACvC,CAAC,CAAC,CAAC;SACN;QAED,MAAM,YAAY,GAAG,CAAC,OAAgB,EAAE,SAAe,EAAE,EAAE;YACvD,MAAM,YAAY,GAAG,WAAW,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAEnF,IAAI,OAAO,EAAE;gBACT,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,IAAI,YAAY,CAAC,YAAY,EAAE,UAAU,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,CAAC;aACxG;iBAAM;gBACH,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAC3B,kCAAkC;aACrC;YAED,cAAc,EAAE,CAAC;QACrB,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,UAAU;YAC9B,CAAC,CAAC,CAAC,KAAgC,EAAE,EAAE;gBACjC,IAAI;oBACA,UAAU,CAAC,KAAK,CAAC,CAAC;iBACrB;gBAAC,OAAO,CAAC,EAAE;oBACR,YAAY,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;iBACnD;YACL,CAAC;YACH,CAAC,CAAC,SAAS,CAAC;QAEhB,MAAM,cAAc,GAAG,GAAG,EAAE;YACxB,IAAI,SAAS,EAAE;gBACX,IAAI;oBACA,SAAS,CAAC,KAAK,CAAC,CAAC;iBACpB;gBAAC,OAAO,CAAC,EAAE;oBACR,YAAY,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;iBAClD;aACJ;YAED,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,OAAO,WAAW,CAAC,SAAS,CACxB,QAAQ,EACR,KAAK,EACL,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;YACb,IAAU,MAAO,CAAC,IAAI,EAAE;gBACpB,MAAM,YAAY,GAAuB,MAAM,CAAC;gBAChD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,YAAY,CAAC,EAAE;oBACjE,OAAO;iBACV;gBAED,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;gBACtC,cAAc,EAAE,CAAC;aACpB;iBAAM;gBACH,MAAM,aAAa,GAA4B,MAAM,CAAC;gBACtD,aAAa;qBACR,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,CAAC,IAAI,CAAC;qBACxE,IAAI,CAAC,GAAG,EAAE;oBACP,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;oBACtC,cAAc,EAAE,CAAC;gBACrB,CAAC,CAAC;qBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBACb,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBACvC,CAAC,CAAC,CAAC;aACV;QACL,CAAC,EACD,eAAe,EACf,YAAY,EACZ,cAAc,EACd,eAAe,EACf,IAAI,CACP,CAAC;IACN,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAC,WAAW,CACrB,OAAe,EACf,gBAAiD,EAAE,EACnD,QAAyB,WAAW,CAAC,gBAAgB,EACrD,aAAmE,IAAI,EACvE,kBAAoC,IAAI,EACxC,OAAe,EAAE;QAEjB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,WAAW,CAAC,MAAM,CACd,OAAO,EACP,aAAa,EACb,KAAK,EACL,CAAC,KAAK,EAAE,EAAE;gBACN,OAAO,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC,EACD,UAAU,EACV,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE;gBAC1B,MAAM,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5C,CAAC,EACD,eAAe,EACf,IAAI,CACP,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,kBAAkB,CAC5B,OAAe,EACf,gBAAiD,EAAE,EACnD,QAAyB,WAAW,CAAC,gBAAgB,EACrD,YAAwD,IAAI,EAC5D,aAAmE,IAAI,EACvE,UAA8E,IAAI,EAClF,kBAAoC,IAAI,EACxC,OAAe,EAAE;QAEjB,IAAI,CAAC,KAAK,EAAE;YACR,MAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC;SACf;QAED,MAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAClE,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,IAAI,CAAC;SACf;QAED,MAAM,YAAY,GAAG,EAAE,CAAC;QACxB,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAEnC,MAAM,cAAc,GAAG,GAAG,EAAE;YACxB,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,MAAM,YAAY,GAAG,CAAC,OAAgB,EAAE,SAAe,EAAE,EAAE;YACvD,MAAM,YAAY,GAAG,WAAW,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAEnF,IAAI,OAAO,EAAE;gBACT,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,IAAI,YAAY,CAAC,YAAY,EAAE,UAAU,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,CAAC;aACxG;iBAAM;gBACH,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAC3B,kCAAkC;aACrC;YAED,cAAc,EAAE,CAAC;QACrB,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,UAAU;YAC9B,CAAC,CAAC,CAAC,KAAgC,EAAE,EAAE;gBACjC,IAAI;oBACA,UAAU,CAAC,KAAK,CAAC,CAAC;iBACrB;gBAAC,OAAO,CAAC,EAAE;oBACR,YAAY,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;iBACnD;YACL,CAAC;YACH,CAAC,CAAC,SAAS,CAAC;QAEhB,MAAM,cAAc,GAAG,CAAC,MAAsB,EAAE,EAAE;YAC9C,IAAI,SAAS,EAAE;gBACX,IAAI;oBACA,SAAS,CAAC,MAAM,CAAC,CAAC;iBACrB;gBAAC,OAAO,CAAC,EAAE;oBACR,YAAY,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;iBAClD;aACJ;YAED,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,OAAO,WAAW,CAAC,SAAS,CACxB,QAAQ,EACR,KAAK,EACL,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;YACb,IAAU,MAAO,CAAC,kBAAkB,EAAE;gBAClC,MAAM,YAAY,GAAuB,MAAM,CAAC;gBAChD,MAAM,cAAc,GAAG,YAAY,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBACpG,IAAI,CAAC,cAAc,EAAE;oBACjB,OAAO;iBACV;gBACD,cAAc,CAAC,iBAAiB,EAAE,CAAC;gBACnC,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;gBACtC,cAAc,CAAC,cAAc,CAAC,CAAC;aAClC;iBAAM,IAAU,MAAO,CAAC,uBAAuB,EAAE;gBAC9C,MAAM,aAAa,GAA4B,MAAM,CAAC;gBACtD,aAAa;qBACR,uBAAuB,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,CAAC,IAAI,CAAC;qBACtF,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE;oBACrB,cAAc,CAAC,iBAAiB,EAAE,CAAC;oBACnC,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;oBACtC,cAAc,CAAC,cAAc,CAAC,CAAC;gBACnC,CAAC,CAAC;qBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBACb,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBACvC,CAAC,CAAC,CAAC;aACV;iBAAM;gBACH,YAAY,CAAC,oIAAoI,CAAC,CAAC;aACtJ;QACL,CAAC,EACD,eAAe,EACf,YAAY,EACZ,cAAc,EACd,eAAe,EACf,IAAI,CACP,CAAC;IACN,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,uBAAuB,CACjC,OAAe,EACf,gBAA+B,EAAE,EACjC,QAAyB,WAAW,CAAC,gBAAgB,EACrD,aAAmE,IAAI,EACvE,kBAAoC,IAAI;QAExC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,WAAW,CAAC,kBAAkB,CAC1B,OAAO,EACP,aAAa,EACb,KAAK,EACL,CAAC,cAAc,EAAE,EAAE;gBACf,OAAO,CAAC,cAAc,CAAC,CAAC;YAC5B,CAAC,EACD,UAAU,EACV,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE;gBAC1B,MAAM,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5C,CAAC,EACD,eAAe,CAClB,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,MAAM,CAAC,gBAAgB,CAC1B,OAAe,EACf,gBAA+B,EAAE,EACjC,QAAyB,WAAW,CAAC,gBAAgB,EACrD,mBAAmB,GAAG,IAAI,EAC1B,yBAAyB,GAAG,oCAAoC,CAAC,KAAK,EACtE,kBAAkD,IAAI,EACtD,YAA8C,IAAI,EAClD,aAAmE,IAAI,EACvE,UAA8E,IAAI,EAClF,kBAAoC,IAAI;QAExC,IAAI,CAAC,KAAK,EAAE;YACR,MAAM,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;YACzD,OAAO;SACV;QAED,IAAI,mBAAmB,EAAE;YACrB,iEAAiE;YACjE,KAAK,MAAM,UAAU,IAAI,KAAK,CAAC,WAAW,EAAE;gBACxC,UAAU,CAAC,KAAK,EAAE,CAAC;aACtB;YACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC1B,KAAK,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;gBACrD,cAAc,CAAC,OAAO,EAAE,CAAC;YAC7B,CAAC,CAAC,CAAC;YACH,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;YAC/B,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBACnB,IAAI,IAAI,CAAC,UAAU,EAAE;oBACjB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;iBACxB;YACL,CAAC,CAAC,CAAC;SACN;aAAM;YACH,QAAQ,yBAAyB,EAAE;gBAC/B,KAAK,oCAAoC,CAAC,KAAK;oBAC3C,KAAK,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;wBACrD,cAAc,CAAC,OAAO,EAAE,CAAC;oBAC7B,CAAC,CAAC,CAAC;oBACH,MAAM;gBACV,KAAK,oCAAoC,CAAC,IAAI;oBAC1C,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;wBAC7C,cAAc,CAAC,IAAI,EAAE,CAAC;oBAC1B,CAAC,CAAC,CAAC;oBACH,MAAM;gBACV,KAAK,oCAAoC,CAAC,IAAI;oBAC1C,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;wBAC7C,cAAc,CAAC,KAAK,EAAE,CAAC;wBACvB,cAAc,CAAC,OAAO,EAAE,CAAC;oBAC7B,CAAC,CAAC,CAAC;oBACH,MAAM;gBACV,KAAK,oCAAoC,CAAC,MAAM;oBAC5C,gBAAgB;oBAChB,MAAM;gBACV;oBACI,MAAM,CAAC,KAAK,CAAC,8CAA8C,GAAG,yBAAyB,GAAG,GAAG,CAAC,CAAC;oBAC/F,OAAO;aACd;SACJ;QAED,MAAM,8BAA8B,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;QAEhE,MAAM,sBAAsB,GAAG,CAAC,SAAyB,EAAE,EAAE;YACzD,SAAS,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,8BAA8B,CAAC,EAAE,eAAe,CAAC,CAAC;YAE7G,SAAS,CAAC,OAAO,EAAE,CAAC;YAEpB,KAAK,CAAC,iCAAiC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAE/D,IAAI,SAAS,EAAE;gBACX,SAAS,CAAC,KAAK,CAAC,CAAC;aACpB;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,sBAAsB,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;IACzH,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,MAAM,CAAC,qBAAqB,CAC/B,OAAe,EACf,gBAA+B,EAAE,EACjC,QAAyB,WAAW,CAAC,gBAAgB,EACrD,mBAAmB,GAAG,IAAI,EAC1B,yBAAyB,GAAG,oCAAoC,CAAC,KAAK,EACtE,kBAAkD,IAAI;IACtD,6DAA6D;IAC7D,YAA8C,IAAI,EAClD,aAAmE,IAAI;IACvE,6DAA6D;IAC7D,UAA8E,IAAI,EAClF,kBAAoC,IAAI;QAExC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,WAAW,CAAC,gBAAgB,CACxB,OAAO,EACP,aAAa,EACb,KAAK,EACL,mBAAmB,EACnB,yBAAyB,EACzB,eAAe,EACf,CAAC,MAAa,EAAE,EAAE;gBACd,OAAO,CAAC,MAAM,CAAC,CAAC;YACpB,CAAC,EACD,UAAU,EACV,CAAC,MAAa,EAAE,OAAe,EAAE,SAAc,EAAE,EAAE;gBAC/C,MAAM,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5C,CAAC,EACD,eAAe,CAClB,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;;AA9gCD;;GAEG;AACoB,sBAAU,GAAG,SAAS,CAAC,sBAAsB,CAAC;AAErE;;GAEG;AACoB,2BAAe,GAAG,SAAS,CAAC,2BAA2B,CAAC;AAE/E;;GAEG;AACoB,2BAAe,GAAG,SAAS,CAAC,2BAA2B,CAAC;AAE/E;;GAEG;AACoB,4BAAgB,GAAG,SAAS,CAAC,4BAA4B,CAAC;AAiDjF,UAAU;AAEV;;GAEG;AACW,uCAA2B,GAAG,IAAI,UAAU,EAAgD,CAAC;AAE5F,8BAAkB,GAA+C,EAAE,CAAC;AAEpE,iCAAqB,GAAG,KAAK,CAAC", "sourcesContent": ["import { Tools } from \"../Misc/tools\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Scene } from \"../scene\";\r\nimport { Engine } from \"../Engines/engine\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { AnimationGroup } from \"../Animations/animationGroup\";\r\nimport type { AssetContainer } from \"../assetContainer\";\r\nimport type { IParticleSystem } from \"../Particles/IParticleSystem\";\r\nimport type { Skeleton } from \"../Bones/skeleton\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { SceneLoaderFlags } from \"./sceneLoaderFlags\";\r\nimport type { IFileRequest } from \"../Misc/fileRequest\";\r\nimport type { WebRequest } from \"../Misc/webRequest\";\r\nimport type { LoadFileError } from \"../Misc/fileTools\";\r\nimport { IsBase64DataUrl } from \"../Misc/fileTools\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { Geometry } from \"../Meshes/geometry\";\r\nimport type { Light } from \"../Lights/light\";\r\nimport { RuntimeError, ErrorCodes } from \"../Misc/error\";\r\nimport type { ISpriteManager } from \"../Sprites/spriteManager\";\r\nimport { RandomGUID } from \"../Misc/guid\";\r\n\r\n/**\r\n * Type used for the success callback of ImportMesh\r\n */\r\nexport type SceneLoaderSuccessCallback = (\r\n    meshes: AbstractMesh[],\r\n    particleSystems: IParticleSystem[],\r\n    skeletons: Skeleton[],\r\n    animationGroups: AnimationGroup[],\r\n    transformNodes: TransformNode[],\r\n    geometries: Geometry[],\r\n    lights: Light[],\r\n    spriteManagers: ISpriteManager[]\r\n) => void;\r\n\r\n/**\r\n * Interface used for the result of ImportMeshAsync\r\n */\r\nexport interface ISceneLoaderAsyncResult {\r\n    /**\r\n     * The array of loaded meshes\r\n     */\r\n    readonly meshes: AbstractMesh[];\r\n\r\n    /**\r\n     * The array of loaded particle systems\r\n     */\r\n    readonly particleSystems: IParticleSystem[];\r\n\r\n    /**\r\n     * The array of loaded skeletons\r\n     */\r\n    readonly skeletons: Skeleton[];\r\n\r\n    /**\r\n     * The array of loaded animation groups\r\n     */\r\n    readonly animationGroups: AnimationGroup[];\r\n\r\n    /**\r\n     * The array of loaded transform nodes\r\n     */\r\n    readonly transformNodes: TransformNode[];\r\n\r\n    /**\r\n     * The array of loaded geometries\r\n     */\r\n    readonly geometries: Geometry[];\r\n\r\n    /**\r\n     * The array of loaded lights\r\n     */\r\n    readonly lights: Light[];\r\n\r\n    /**\r\n     * The array of loaded sprite managers\r\n     */\r\n    readonly spriteManagers: ISpriteManager[];\r\n}\r\n\r\n/**\r\n * Interface used to represent data loading progression\r\n */\r\nexport interface ISceneLoaderProgressEvent {\r\n    /**\r\n     * Defines if data length to load can be evaluated\r\n     */\r\n    readonly lengthComputable: boolean;\r\n\r\n    /**\r\n     * Defines the loaded data length\r\n     */\r\n    readonly loaded: number;\r\n\r\n    /**\r\n     * Defines the data length to load\r\n     */\r\n    readonly total: number;\r\n}\r\n\r\n/**\r\n * Interface used by SceneLoader plugins to define supported file extensions\r\n */\r\nexport interface ISceneLoaderPluginExtensions {\r\n    /**\r\n     * Defines the list of supported extensions\r\n     */\r\n    [extension: string]: {\r\n        isBinary: boolean;\r\n    };\r\n}\r\n\r\n/**\r\n * Interface used by SceneLoader plugin factory\r\n */\r\nexport interface ISceneLoaderPluginFactory {\r\n    /**\r\n     * Defines the name of the factory\r\n     */\r\n    name: string;\r\n\r\n    /**\r\n     * Function called to create a new plugin\r\n     * @returns the new plugin\r\n     */\r\n    createPlugin(): ISceneLoaderPlugin | ISceneLoaderPluginAsync;\r\n\r\n    /**\r\n     * The callback that returns true if the data can be directly loaded.\r\n     * @param data string containing the file data\r\n     * @returns if the data can be loaded directly\r\n     */\r\n    canDirectLoad?(data: string): boolean;\r\n}\r\n\r\n/**\r\n * Interface used to define the base of ISceneLoaderPlugin and ISceneLoaderPluginAsync\r\n */\r\nexport interface ISceneLoaderPluginBase {\r\n    /**\r\n     * The friendly name of this plugin.\r\n     */\r\n    name: string;\r\n\r\n    /**\r\n     * The file extensions supported by this plugin.\r\n     */\r\n    extensions: string | ISceneLoaderPluginExtensions;\r\n\r\n    /**\r\n     * The callback called when loading from a url.\r\n     * @param scene scene loading this url\r\n     * @param fileOrUrl file or url to load\r\n     * @param rootUrl root url to use to load assets\r\n     * @param onSuccess callback called when the file successfully loads\r\n     * @param onProgress callback called while file is loading (if the server supports this mode)\r\n     * @param useArrayBuffer defines a boolean indicating that date must be returned as ArrayBuffer\r\n     * @param onError callback called when the file fails to load\r\n     * @param name defines the name of the file when loading a binary file\r\n     * @returns a file request object\r\n     */\r\n    loadFile?(\r\n        scene: Scene,\r\n        fileOrUrl: File | string | ArrayBufferView,\r\n        rootUrl: string,\r\n        onSuccess: (data: any, responseURL?: string) => void,\r\n        onProgress?: (ev: ISceneLoaderProgressEvent) => void,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (request?: WebRequest, exception?: LoadFileError) => void,\r\n        name?: string\r\n    ): Nullable<IFileRequest>;\r\n\r\n    /**\r\n     * The callback that returns true if the data can be directly loaded.\r\n     * @param data string containing the file data\r\n     * @returns if the data can be loaded directly\r\n     */\r\n    canDirectLoad?(data: string): boolean;\r\n\r\n    /**\r\n     * The callback that returns the data to pass to the plugin if the data can be directly loaded.\r\n     * @param scene scene loading this data\r\n     * @param data string containing the data\r\n     * @returns data to pass to the plugin\r\n     */\r\n    directLoad?(scene: Scene, data: string): any;\r\n\r\n    /**\r\n     * The callback that allows custom handling of the root url based on the response url.\r\n     * @param rootUrl the original root url\r\n     * @param responseURL the response url if available\r\n     * @returns the new root url\r\n     */\r\n    rewriteRootURL?(rootUrl: string, responseURL?: string): string;\r\n}\r\n\r\n/**\r\n * Interface used to define a SceneLoader plugin\r\n */\r\nexport interface ISceneLoaderPlugin extends ISceneLoaderPluginBase {\r\n    /**\r\n     * Import meshes into a scene.\r\n     * @param meshesNames An array of mesh names, a single mesh name, or empty string for all meshes that filter what meshes are imported\r\n     * @param scene The scene to import into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @param meshes The meshes array to import into\r\n     * @param particleSystems The particle systems array to import into\r\n     * @param skeletons The skeletons array to import into\r\n     * @param onError The callback when import fails\r\n     * @returns True if successful or false otherwise\r\n     */\r\n    importMesh(\r\n        meshesNames: any,\r\n        scene: Scene,\r\n        data: any,\r\n        rootUrl: string,\r\n        meshes: AbstractMesh[],\r\n        particleSystems: IParticleSystem[],\r\n        skeletons: Skeleton[],\r\n        onError?: (message: string, exception?: any) => void\r\n    ): boolean;\r\n\r\n    /**\r\n     * Load into a scene.\r\n     * @param scene The scene to load into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @param onError The callback when import fails\r\n     * @returns True if successful or false otherwise\r\n     */\r\n    load(scene: Scene, data: any, rootUrl: string, onError?: (message: string, exception?: any) => void): boolean;\r\n\r\n    /**\r\n     * Load into an asset container.\r\n     * @param scene The scene to load into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @param onError The callback when import fails\r\n     * @returns The loaded asset container\r\n     */\r\n    loadAssetContainer(scene: Scene, data: any, rootUrl: string, onError?: (message: string, exception?: any) => void): AssetContainer;\r\n}\r\n\r\n/**\r\n * Interface used to define an async SceneLoader plugin\r\n */\r\nexport interface ISceneLoaderPluginAsync extends ISceneLoaderPluginBase {\r\n    /**\r\n     * Import meshes into a scene.\r\n     * @param meshesNames An array of mesh names, a single mesh name, or empty string for all meshes that filter what meshes are imported\r\n     * @param scene The scene to import into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @param onProgress The callback when the load progresses\r\n     * @param fileName Defines the name of the file to load\r\n     * @returns The loaded objects (e.g. meshes, particle systems, skeletons, animation groups, etc.)\r\n     */\r\n    importMeshAsync(\r\n        meshesNames: any,\r\n        scene: Scene,\r\n        data: any,\r\n        rootUrl: string,\r\n        onProgress?: (event: ISceneLoaderProgressEvent) => void,\r\n        fileName?: string\r\n    ): Promise<ISceneLoaderAsyncResult>;\r\n\r\n    /**\r\n     * Load into a scene.\r\n     * @param scene The scene to load into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @param onProgress The callback when the load progresses\r\n     * @param fileName Defines the name of the file to load\r\n     * @returns Nothing\r\n     */\r\n    loadAsync(scene: Scene, data: any, rootUrl: string, onProgress?: (event: ISceneLoaderProgressEvent) => void, fileName?: string): Promise<void>;\r\n\r\n    /**\r\n     * Load into an asset container.\r\n     * @param scene The scene to load into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @param onProgress The callback when the load progresses\r\n     * @param fileName Defines the name of the file to load\r\n     * @returns The loaded asset container\r\n     */\r\n    loadAssetContainerAsync(scene: Scene, data: any, rootUrl: string, onProgress?: (event: ISceneLoaderProgressEvent) => void, fileName?: string): Promise<AssetContainer>;\r\n}\r\n\r\n/**\r\n * Mode that determines how to handle old animation groups before loading new ones.\r\n */\r\nexport enum SceneLoaderAnimationGroupLoadingMode {\r\n    /**\r\n     * Reset all old animations to initial state then dispose them.\r\n     */\r\n    Clean = 0,\r\n\r\n    /**\r\n     * Stop all old animations.\r\n     */\r\n    Stop = 1,\r\n\r\n    /**\r\n     * Restart old animations from first frame.\r\n     */\r\n    Sync = 2,\r\n\r\n    /**\r\n     * Old animations remains untouched.\r\n     */\r\n    NoSync = 3,\r\n}\r\n\r\n/**\r\n * Defines a plugin registered by the SceneLoader\r\n */\r\ninterface IRegisteredPlugin {\r\n    /**\r\n     * Defines the plugin to use\r\n     */\r\n    plugin: ISceneLoaderPlugin | ISceneLoaderPluginAsync | ISceneLoaderPluginFactory;\r\n    /**\r\n     * Defines if the plugin supports binary data\r\n     */\r\n    isBinary: boolean;\r\n}\r\n\r\n/**\r\n * Defines file information\r\n */\r\ninterface IFileInfo {\r\n    /**\r\n     * Gets the file url\r\n     */\r\n    url: string;\r\n    /**\r\n     * Gets the root url\r\n     */\r\n    rootUrl: string;\r\n    /**\r\n     * Gets filename\r\n     */\r\n    name: string;\r\n    /**\r\n     * Gets the file\r\n     */\r\n    file: Nullable<File>;\r\n\r\n    /**\r\n     * Gets raw binary data.\r\n     */\r\n    rawData: Nullable<ArrayBufferView>;\r\n}\r\n\r\n/**\r\n * Class used to load scene from various file formats using registered plugins\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/importers/loadingFileTypes\r\n */\r\nexport class SceneLoader {\r\n    /**\r\n     * No logging while loading\r\n     */\r\n    public static readonly NO_LOGGING = Constants.SCENELOADER_NO_LOGGING;\r\n\r\n    /**\r\n     * Minimal logging while loading\r\n     */\r\n    public static readonly MINIMAL_LOGGING = Constants.SCENELOADER_MINIMAL_LOGGING;\r\n\r\n    /**\r\n     * Summary logging while loading\r\n     */\r\n    public static readonly SUMMARY_LOGGING = Constants.SCENELOADER_SUMMARY_LOGGING;\r\n\r\n    /**\r\n     * Detailed logging while loading\r\n     */\r\n    public static readonly DETAILED_LOGGING = Constants.SCENELOADER_DETAILED_LOGGING;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if entire scene must be loaded even if scene contains incremental data\r\n     */\r\n    public static get ForceFullSceneLoadingForIncremental() {\r\n        return SceneLoaderFlags.ForceFullSceneLoadingForIncremental;\r\n    }\r\n\r\n    public static set ForceFullSceneLoadingForIncremental(value: boolean) {\r\n        SceneLoaderFlags.ForceFullSceneLoadingForIncremental = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if loading screen must be displayed while loading a scene\r\n     */\r\n    public static get ShowLoadingScreen(): boolean {\r\n        return SceneLoaderFlags.ShowLoadingScreen;\r\n    }\r\n\r\n    public static set ShowLoadingScreen(value: boolean) {\r\n        SceneLoaderFlags.ShowLoadingScreen = value;\r\n    }\r\n\r\n    /**\r\n     * Defines the current logging level (while loading the scene)\r\n     * @ignorenaming\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static get loggingLevel(): number {\r\n        return SceneLoaderFlags.loggingLevel;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static set loggingLevel(value: number) {\r\n        SceneLoaderFlags.loggingLevel = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or set a boolean indicating if matrix weights must be cleaned upon loading\r\n     */\r\n    public static get CleanBoneMatrixWeights(): boolean {\r\n        return SceneLoaderFlags.CleanBoneMatrixWeights;\r\n    }\r\n\r\n    public static set CleanBoneMatrixWeights(value: boolean) {\r\n        SceneLoaderFlags.CleanBoneMatrixWeights = value;\r\n    }\r\n\r\n    // Members\r\n\r\n    /**\r\n     * Event raised when a plugin is used to load a scene\r\n     */\r\n    public static OnPluginActivatedObservable = new Observable<ISceneLoaderPlugin | ISceneLoaderPluginAsync>();\r\n\r\n    private static _RegisteredPlugins: { [extension: string]: IRegisteredPlugin } = {};\r\n\r\n    private static _ShowingLoadingScreen = false;\r\n\r\n    /**\r\n     * Gets the default plugin (used to load Babylon files)\r\n     * @returns the .babylon plugin\r\n     */\r\n    public static GetDefaultPlugin(): IRegisteredPlugin {\r\n        return SceneLoader._RegisteredPlugins[\".babylon\"];\r\n    }\r\n\r\n    private static _GetPluginForExtension(extension: string): IRegisteredPlugin {\r\n        const registeredPlugin = SceneLoader._RegisteredPlugins[extension];\r\n        if (registeredPlugin) {\r\n            return registeredPlugin;\r\n        }\r\n        Logger.Warn(\r\n            \"Unable to find a plugin to load \" +\r\n                extension +\r\n                \" files. Trying to use .babylon default plugin. To load from a specific filetype (eg. gltf) see: https://doc.babylonjs.com/features/featuresDeepDive/importers/loadingFileTypes\"\r\n        );\r\n        return SceneLoader.GetDefaultPlugin();\r\n    }\r\n\r\n    private static _GetPluginForDirectLoad(data: string): IRegisteredPlugin {\r\n        for (const extension in SceneLoader._RegisteredPlugins) {\r\n            const plugin = SceneLoader._RegisteredPlugins[extension].plugin;\r\n\r\n            if (plugin.canDirectLoad && plugin.canDirectLoad(data)) {\r\n                return SceneLoader._RegisteredPlugins[extension];\r\n            }\r\n        }\r\n\r\n        return SceneLoader.GetDefaultPlugin();\r\n    }\r\n\r\n    private static _GetPluginForFilename(sceneFilename: string): IRegisteredPlugin {\r\n        const queryStringPosition = sceneFilename.indexOf(\"?\");\r\n\r\n        if (queryStringPosition !== -1) {\r\n            sceneFilename = sceneFilename.substring(0, queryStringPosition);\r\n        }\r\n\r\n        const dotPosition = sceneFilename.lastIndexOf(\".\");\r\n\r\n        const extension = sceneFilename.substring(dotPosition, sceneFilename.length).toLowerCase();\r\n        return SceneLoader._GetPluginForExtension(extension);\r\n    }\r\n\r\n    private static _GetDirectLoad(sceneFilename: string): Nullable<string> {\r\n        if (sceneFilename.substr(0, 5) === \"data:\") {\r\n            return sceneFilename.substr(5);\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private static _FormatErrorMessage(fileInfo: IFileInfo, message?: string, exception?: any): string {\r\n        const fromLoad = fileInfo.rawData ? \"binary data\" : fileInfo.url;\r\n        let errorMessage = \"Unable to load from \" + fromLoad;\r\n\r\n        if (message) {\r\n            errorMessage += `: ${message}`;\r\n        } else if (exception) {\r\n            errorMessage += `: ${exception}`;\r\n        }\r\n\r\n        return errorMessage;\r\n    }\r\n\r\n    private static _LoadData(\r\n        fileInfo: IFileInfo,\r\n        scene: Scene,\r\n        onSuccess: (plugin: ISceneLoaderPlugin | ISceneLoaderPluginAsync, data: any, responseURL?: string) => void,\r\n        onProgress: ((event: ISceneLoaderProgressEvent) => void) | undefined,\r\n        onError: (message?: string, exception?: any) => void,\r\n        onDispose: () => void,\r\n        pluginExtension: Nullable<string>,\r\n        name: string\r\n    ): Nullable<ISceneLoaderPlugin | ISceneLoaderPluginAsync> {\r\n        const directLoad = SceneLoader._GetDirectLoad(fileInfo.url);\r\n\r\n        if (fileInfo.rawData && !pluginExtension) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"When using ArrayBufferView to load data the file extension must be provided.\";\r\n        }\r\n\r\n        const registeredPlugin = pluginExtension\r\n            ? SceneLoader._GetPluginForExtension(pluginExtension)\r\n            : directLoad\r\n              ? SceneLoader._GetPluginForDirectLoad(fileInfo.url)\r\n              : SceneLoader._GetPluginForFilename(fileInfo.url);\r\n\r\n        if (fileInfo.rawData && !registeredPlugin.isBinary) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"Loading from ArrayBufferView can not be used with plugins that don't support binary loading.\";\r\n        }\r\n\r\n        let plugin: ISceneLoaderPlugin | ISceneLoaderPluginAsync;\r\n\r\n        if ((registeredPlugin.plugin as ISceneLoaderPluginFactory).createPlugin !== undefined) {\r\n            plugin = (registeredPlugin.plugin as ISceneLoaderPluginFactory).createPlugin();\r\n        } else {\r\n            plugin = <any>registeredPlugin.plugin;\r\n        }\r\n\r\n        if (!plugin) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"The loader plugin corresponding to the file type you are trying to load has not been found. If using es6, please import the plugin you wish to use before.\";\r\n        }\r\n\r\n        SceneLoader.OnPluginActivatedObservable.notifyObservers(plugin);\r\n\r\n        // Check if we have a direct load url. If the plugin is registered to handle\r\n        // it or it's not a base64 data url, then pass it through the direct load path.\r\n        if (directLoad && ((plugin.canDirectLoad && plugin.canDirectLoad(fileInfo.url)) || !IsBase64DataUrl(fileInfo.url))) {\r\n            if (plugin.directLoad) {\r\n                const result = plugin.directLoad(scene, directLoad);\r\n                if (result.then) {\r\n                    result\r\n                        .then((data: any) => {\r\n                            onSuccess(plugin, data);\r\n                        })\r\n                        .catch((error: any) => {\r\n                            onError(\"Error in directLoad of _loadData: \" + error, error);\r\n                        });\r\n                } else {\r\n                    onSuccess(plugin, result);\r\n                }\r\n            } else {\r\n                onSuccess(plugin, directLoad);\r\n            }\r\n            return plugin;\r\n        }\r\n\r\n        const useArrayBuffer = registeredPlugin.isBinary;\r\n\r\n        const dataCallback = (data: any, responseURL?: string) => {\r\n            if (scene.isDisposed) {\r\n                onError(\"Scene has been disposed\");\r\n                return;\r\n            }\r\n\r\n            onSuccess(plugin, data, responseURL);\r\n        };\r\n\r\n        let request: Nullable<IFileRequest> = null;\r\n        let pluginDisposed = false;\r\n        const onDisposeObservable = (plugin as any).onDisposeObservable as Observable<ISceneLoaderPlugin | ISceneLoaderPluginAsync>;\r\n        if (onDisposeObservable) {\r\n            onDisposeObservable.add(() => {\r\n                pluginDisposed = true;\r\n\r\n                if (request) {\r\n                    request.abort();\r\n                    request = null;\r\n                }\r\n\r\n                onDispose();\r\n            });\r\n        }\r\n\r\n        const manifestChecked = () => {\r\n            if (pluginDisposed) {\r\n                return;\r\n            }\r\n\r\n            const errorCallback = (request?: WebRequest, exception?: LoadFileError) => {\r\n                onError(request?.statusText, exception);\r\n            };\r\n\r\n            if (!plugin.loadFile && fileInfo.rawData) {\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"Plugin does not support loading ArrayBufferView.\";\r\n            }\r\n\r\n            request = plugin.loadFile\r\n                ? plugin.loadFile(scene, fileInfo.rawData || fileInfo.file || fileInfo.url, fileInfo.rootUrl, dataCallback, onProgress, useArrayBuffer, errorCallback, name)\r\n                : scene._loadFile(fileInfo.file || fileInfo.url, dataCallback, onProgress, true, useArrayBuffer, errorCallback);\r\n        };\r\n\r\n        const engine = scene.getEngine();\r\n        let canUseOfflineSupport = engine.enableOfflineSupport;\r\n        if (canUseOfflineSupport) {\r\n            // Also check for exceptions\r\n            let exceptionFound = false;\r\n            for (const regex of scene.disableOfflineSupportExceptionRules) {\r\n                if (regex.test(fileInfo.url)) {\r\n                    exceptionFound = true;\r\n                    break;\r\n                }\r\n            }\r\n\r\n            canUseOfflineSupport = !exceptionFound;\r\n        }\r\n\r\n        if (canUseOfflineSupport && Engine.OfflineProviderFactory) {\r\n            // Checking if a manifest file has been set for this scene and if offline mode has been requested\r\n            scene.offlineProvider = Engine.OfflineProviderFactory(fileInfo.url, manifestChecked, engine.disableManifestCheck);\r\n        } else {\r\n            manifestChecked();\r\n        }\r\n\r\n        return plugin;\r\n    }\r\n\r\n    private static _GetFileInfo(rootUrl: string, sceneFilename: string | File | ArrayBufferView): Nullable<IFileInfo> {\r\n        let url: string;\r\n        let name: string;\r\n        let file: Nullable<File> = null;\r\n        let rawData: Nullable<ArrayBufferView> = null;\r\n\r\n        if (!sceneFilename) {\r\n            url = rootUrl;\r\n            name = Tools.GetFilename(rootUrl);\r\n            rootUrl = Tools.GetFolderPath(rootUrl);\r\n        } else if ((sceneFilename as File).name) {\r\n            const sceneFile = sceneFilename as File;\r\n            url = `file:${sceneFile.name}`;\r\n            name = sceneFile.name;\r\n            file = sceneFile;\r\n        } else if (ArrayBuffer.isView(sceneFilename)) {\r\n            url = \"\";\r\n            name = RandomGUID();\r\n            rawData = sceneFilename as ArrayBufferView;\r\n        } else if (typeof sceneFilename === \"string\" && sceneFilename.startsWith(\"data:\")) {\r\n            url = sceneFilename;\r\n            name = \"\";\r\n        } else {\r\n            const filename = sceneFilename as string;\r\n            if (filename.substr(0, 1) === \"/\") {\r\n                Tools.Error(\"Wrong sceneFilename parameter\");\r\n                return null;\r\n            }\r\n\r\n            url = rootUrl + filename;\r\n            name = filename;\r\n        }\r\n\r\n        return {\r\n            url: url,\r\n            rootUrl: rootUrl,\r\n            name: name,\r\n            file: file,\r\n            rawData,\r\n        };\r\n    }\r\n\r\n    // Public functions\r\n\r\n    /**\r\n     * Gets a plugin that can load the given extension\r\n     * @param extension defines the extension to load\r\n     * @returns a plugin or null if none works\r\n     */\r\n    public static GetPluginForExtension(extension: string): ISceneLoaderPlugin | ISceneLoaderPluginAsync | ISceneLoaderPluginFactory {\r\n        return SceneLoader._GetPluginForExtension(extension).plugin;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the given extension can be loaded\r\n     * @param extension defines the extension to load\r\n     * @returns true if the extension is supported\r\n     */\r\n    public static IsPluginForExtensionAvailable(extension: string): boolean {\r\n        return !!SceneLoader._RegisteredPlugins[extension];\r\n    }\r\n\r\n    /**\r\n     * Adds a new plugin to the list of registered plugins\r\n     * @param plugin defines the plugin to add\r\n     */\r\n    public static RegisterPlugin(plugin: ISceneLoaderPlugin | ISceneLoaderPluginAsync): void {\r\n        if (typeof plugin.extensions === \"string\") {\r\n            const extension = <string>plugin.extensions;\r\n            SceneLoader._RegisteredPlugins[extension.toLowerCase()] = {\r\n                plugin: plugin,\r\n                isBinary: false,\r\n            };\r\n        } else {\r\n            const extensions = <ISceneLoaderPluginExtensions>plugin.extensions;\r\n            Object.keys(extensions).forEach((extension) => {\r\n                SceneLoader._RegisteredPlugins[extension.toLowerCase()] = {\r\n                    plugin: plugin,\r\n                    isBinary: extensions[extension].isBinary,\r\n                };\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Import meshes into a scene\r\n     * @param meshNames an array of mesh names, a single mesh name, or empty string for all meshes that filter what meshes are imported\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene the instance of BABYLON.Scene to append to\r\n     * @param onSuccess a callback with a list of imported meshes, particleSystems, skeletons, and animationGroups when import succeeds\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param onError a callback with the scene, a message, and possibly an exception when import fails\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the name of the file, if the data is binary\r\n     * @returns The loaded plugin\r\n     */\r\n    public static ImportMesh(\r\n        meshNames: any,\r\n        rootUrl: string,\r\n        sceneFilename: string | File | ArrayBufferView = \"\",\r\n        scene: Nullable<Scene> = EngineStore.LastCreatedScene,\r\n        onSuccess: Nullable<SceneLoaderSuccessCallback> = null,\r\n        onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n        onError: Nullable<(scene: Scene, message: string, exception?: any) => void> = null,\r\n        pluginExtension: Nullable<string> = null,\r\n        name: string = \"\"\r\n    ): Nullable<ISceneLoaderPlugin | ISceneLoaderPluginAsync> {\r\n        if (!scene) {\r\n            Logger.Error(\"No scene available to import mesh to\");\r\n            return null;\r\n        }\r\n\r\n        const fileInfo = SceneLoader._GetFileInfo(rootUrl, sceneFilename);\r\n        if (!fileInfo) {\r\n            return null;\r\n        }\r\n\r\n        const loadingToken = {};\r\n        scene.addPendingData(loadingToken);\r\n\r\n        const disposeHandler = () => {\r\n            scene.removePendingData(loadingToken);\r\n        };\r\n\r\n        const errorHandler = (message?: string, exception?: any) => {\r\n            const errorMessage = SceneLoader._FormatErrorMessage(fileInfo, message, exception);\r\n\r\n            if (onError) {\r\n                onError(scene, errorMessage, new RuntimeError(errorMessage, ErrorCodes.SceneLoaderError, exception));\r\n            } else {\r\n                Logger.Error(errorMessage);\r\n                // should the exception be thrown?\r\n            }\r\n\r\n            disposeHandler();\r\n        };\r\n\r\n        const progressHandler = onProgress\r\n            ? (event: ISceneLoaderProgressEvent) => {\r\n                  try {\r\n                      onProgress(event);\r\n                  } catch (e) {\r\n                      errorHandler(\"Error in onProgress callback: \" + e, e);\r\n                  }\r\n              }\r\n            : undefined;\r\n\r\n        const successHandler: SceneLoaderSuccessCallback = (meshes, particleSystems, skeletons, animationGroups, transformNodes, geometries, lights, spriteManagers) => {\r\n            scene.importedMeshesFiles.push(fileInfo.url);\r\n\r\n            if (onSuccess) {\r\n                try {\r\n                    onSuccess(meshes, particleSystems, skeletons, animationGroups, transformNodes, geometries, lights, spriteManagers);\r\n                } catch (e) {\r\n                    errorHandler(\"Error in onSuccess callback: \" + e, e);\r\n                }\r\n            }\r\n\r\n            scene.removePendingData(loadingToken);\r\n        };\r\n\r\n        return SceneLoader._LoadData(\r\n            fileInfo,\r\n            scene,\r\n            (plugin, data, responseURL) => {\r\n                if (plugin.rewriteRootURL) {\r\n                    fileInfo.rootUrl = plugin.rewriteRootURL(fileInfo.rootUrl, responseURL);\r\n                }\r\n\r\n                if ((<any>plugin).importMesh) {\r\n                    const syncedPlugin = <ISceneLoaderPlugin>plugin;\r\n                    const meshes: AbstractMesh[] = [];\r\n                    const particleSystems: IParticleSystem[] = [];\r\n                    const skeletons: Skeleton[] = [];\r\n\r\n                    if (!syncedPlugin.importMesh(meshNames, scene, data, fileInfo.rootUrl, meshes, particleSystems, skeletons, errorHandler)) {\r\n                        return;\r\n                    }\r\n\r\n                    scene.loadingPluginName = plugin.name;\r\n                    successHandler(meshes, particleSystems, skeletons, [], [], [], [], []);\r\n                } else {\r\n                    const asyncedPlugin = <ISceneLoaderPluginAsync>plugin;\r\n                    asyncedPlugin\r\n                        .importMeshAsync(meshNames, scene, data, fileInfo.rootUrl, progressHandler, fileInfo.name)\r\n                        .then((result) => {\r\n                            scene.loadingPluginName = plugin.name;\r\n                            successHandler(\r\n                                result.meshes,\r\n                                result.particleSystems,\r\n                                result.skeletons,\r\n                                result.animationGroups,\r\n                                result.transformNodes,\r\n                                result.geometries,\r\n                                result.lights,\r\n                                result.spriteManagers\r\n                            );\r\n                        })\r\n                        .catch((error) => {\r\n                            errorHandler(error.message, error);\r\n                        });\r\n                }\r\n            },\r\n            progressHandler,\r\n            errorHandler,\r\n            disposeHandler,\r\n            pluginExtension,\r\n            name\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Import meshes into a scene\r\n     * @param meshNames an array of mesh names, a single mesh name, or empty string for all meshes that filter what meshes are imported\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene the instance of BABYLON.Scene to append to\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the name of the file\r\n     * @returns The loaded list of imported meshes, particle systems, skeletons, and animation groups\r\n     */\r\n    public static ImportMeshAsync(\r\n        meshNames: any,\r\n        rootUrl: string,\r\n        sceneFilename: string | File | ArrayBufferView = \"\",\r\n        scene: Nullable<Scene> = EngineStore.LastCreatedScene,\r\n        onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n        pluginExtension: Nullable<string> = null,\r\n        name: string = \"\"\r\n    ): Promise<ISceneLoaderAsyncResult> {\r\n        return new Promise((resolve, reject) => {\r\n            SceneLoader.ImportMesh(\r\n                meshNames,\r\n                rootUrl,\r\n                sceneFilename,\r\n                scene,\r\n                (meshes, particleSystems, skeletons, animationGroups, transformNodes, geometries, lights, spriteManagers) => {\r\n                    resolve({\r\n                        meshes: meshes,\r\n                        particleSystems: particleSystems,\r\n                        skeletons: skeletons,\r\n                        animationGroups: animationGroups,\r\n                        transformNodes: transformNodes,\r\n                        geometries: geometries,\r\n                        lights: lights,\r\n                        spriteManagers: spriteManagers,\r\n                    });\r\n                },\r\n                onProgress,\r\n                (scene, message, exception) => {\r\n                    reject(exception || new Error(message));\r\n                },\r\n                pluginExtension,\r\n                name\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Load a scene\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param engine is the instance of BABYLON.Engine to use to create the scene\r\n     * @param onSuccess a callback with the scene when import succeeds\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param onError a callback with the scene, a message, and possibly an exception when import fails\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the filename, if the data is binary\r\n     * @returns The loaded plugin\r\n     */\r\n    public static Load(\r\n        rootUrl: string,\r\n        sceneFilename: string | File | ArrayBufferView = \"\",\r\n        engine: Nullable<Engine> = EngineStore.LastCreatedEngine,\r\n        onSuccess: Nullable<(scene: Scene) => void> = null,\r\n        onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n        onError: Nullable<(scene: Scene, message: string, exception?: any) => void> = null,\r\n        pluginExtension: Nullable<string> = null,\r\n        name: string = \"\"\r\n    ): Nullable<ISceneLoaderPlugin | ISceneLoaderPluginAsync> {\r\n        if (!engine) {\r\n            Tools.Error(\"No engine available\");\r\n            return null;\r\n        }\r\n\r\n        return SceneLoader.Append(rootUrl, sceneFilename, new Scene(engine), onSuccess, onProgress, onError, pluginExtension, name);\r\n    }\r\n\r\n    /**\r\n     * Load a scene\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param engine is the instance of BABYLON.Engine to use to create the scene\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the filename, if the data is binary\r\n     * @returns The loaded scene\r\n     */\r\n    public static LoadAsync(\r\n        rootUrl: string,\r\n        sceneFilename: string | File | ArrayBufferView = \"\",\r\n        engine: Nullable<Engine> = EngineStore.LastCreatedEngine,\r\n        onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n        pluginExtension: Nullable<string> = null,\r\n        name: string = \"\"\r\n    ): Promise<Scene> {\r\n        return new Promise((resolve, reject) => {\r\n            SceneLoader.Load(\r\n                rootUrl,\r\n                sceneFilename,\r\n                engine,\r\n                (scene) => {\r\n                    resolve(scene);\r\n                },\r\n                onProgress,\r\n                (scene, message, exception) => {\r\n                    reject(exception || new Error(message));\r\n                },\r\n                pluginExtension,\r\n                name\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Append a scene\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene is the instance of BABYLON.Scene to append to\r\n     * @param onSuccess a callback with the scene when import succeeds\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param onError a callback with the scene, a message, and possibly an exception when import fails\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the name of the file, if the data is binary\r\n     * @returns The loaded plugin\r\n     */\r\n    public static Append(\r\n        rootUrl: string,\r\n        sceneFilename: string | File | ArrayBufferView = \"\",\r\n        scene: Nullable<Scene> = EngineStore.LastCreatedScene,\r\n        onSuccess: Nullable<(scene: Scene) => void> = null,\r\n        onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n        onError: Nullable<(scene: Scene, message: string, exception?: any) => void> = null,\r\n        pluginExtension: Nullable<string> = null,\r\n        name: string = \"\"\r\n    ): Nullable<ISceneLoaderPlugin | ISceneLoaderPluginAsync> {\r\n        if (!scene) {\r\n            Logger.Error(\"No scene available to append to\");\r\n            return null;\r\n        }\r\n\r\n        const fileInfo = SceneLoader._GetFileInfo(rootUrl, sceneFilename);\r\n        if (!fileInfo) {\r\n            return null;\r\n        }\r\n\r\n        const loadingToken = {};\r\n        scene.addPendingData(loadingToken);\r\n\r\n        const disposeHandler = () => {\r\n            scene.removePendingData(loadingToken);\r\n        };\r\n\r\n        if (SceneLoader.ShowLoadingScreen && !this._ShowingLoadingScreen) {\r\n            this._ShowingLoadingScreen = true;\r\n            scene.getEngine().displayLoadingUI();\r\n            scene.executeWhenReady(() => {\r\n                scene.getEngine().hideLoadingUI();\r\n                this._ShowingLoadingScreen = false;\r\n            });\r\n        }\r\n\r\n        const errorHandler = (message?: string, exception?: any) => {\r\n            const errorMessage = SceneLoader._FormatErrorMessage(fileInfo, message, exception);\r\n\r\n            if (onError) {\r\n                onError(scene, errorMessage, new RuntimeError(errorMessage, ErrorCodes.SceneLoaderError, exception));\r\n            } else {\r\n                Logger.Error(errorMessage);\r\n                // should the exception be thrown?\r\n            }\r\n\r\n            disposeHandler();\r\n        };\r\n\r\n        const progressHandler = onProgress\r\n            ? (event: ISceneLoaderProgressEvent) => {\r\n                  try {\r\n                      onProgress(event);\r\n                  } catch (e) {\r\n                      errorHandler(\"Error in onProgress callback\", e);\r\n                  }\r\n              }\r\n            : undefined;\r\n\r\n        const successHandler = () => {\r\n            if (onSuccess) {\r\n                try {\r\n                    onSuccess(scene);\r\n                } catch (e) {\r\n                    errorHandler(\"Error in onSuccess callback\", e);\r\n                }\r\n            }\r\n\r\n            scene.removePendingData(loadingToken);\r\n        };\r\n\r\n        return SceneLoader._LoadData(\r\n            fileInfo,\r\n            scene,\r\n            (plugin, data) => {\r\n                if ((<any>plugin).load) {\r\n                    const syncedPlugin = <ISceneLoaderPlugin>plugin;\r\n                    if (!syncedPlugin.load(scene, data, fileInfo.rootUrl, errorHandler)) {\r\n                        return;\r\n                    }\r\n\r\n                    scene.loadingPluginName = plugin.name;\r\n                    successHandler();\r\n                } else {\r\n                    const asyncedPlugin = <ISceneLoaderPluginAsync>plugin;\r\n                    asyncedPlugin\r\n                        .loadAsync(scene, data, fileInfo.rootUrl, progressHandler, fileInfo.name)\r\n                        .then(() => {\r\n                            scene.loadingPluginName = plugin.name;\r\n                            successHandler();\r\n                        })\r\n                        .catch((error) => {\r\n                            errorHandler(error.message, error);\r\n                        });\r\n                }\r\n            },\r\n            progressHandler,\r\n            errorHandler,\r\n            disposeHandler,\r\n            pluginExtension,\r\n            name\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Append a scene\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene is the instance of BABYLON.Scene to append to\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the name of the file, if the data is binary\r\n     * @returns The given scene\r\n     */\r\n    public static AppendAsync(\r\n        rootUrl: string,\r\n        sceneFilename: string | File | ArrayBufferView = \"\",\r\n        scene: Nullable<Scene> = EngineStore.LastCreatedScene,\r\n        onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n        pluginExtension: Nullable<string> = null,\r\n        name: string = \"\"\r\n    ): Promise<Scene> {\r\n        return new Promise((resolve, reject) => {\r\n            SceneLoader.Append(\r\n                rootUrl,\r\n                sceneFilename,\r\n                scene,\r\n                (scene) => {\r\n                    resolve(scene);\r\n                },\r\n                onProgress,\r\n                (scene, message, exception) => {\r\n                    reject(exception || new Error(message));\r\n                },\r\n                pluginExtension,\r\n                name\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Load a scene into an asset container\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene is the instance of BABYLON.Scene to append to (default: last created scene)\r\n     * @param onSuccess a callback with the scene when import succeeds\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param onError a callback with the scene, a message, and possibly an exception when import fails\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the filename, if the data is binary\r\n     * @returns The loaded plugin\r\n     */\r\n    public static LoadAssetContainer(\r\n        rootUrl: string,\r\n        sceneFilename: string | File | ArrayBufferView = \"\",\r\n        scene: Nullable<Scene> = EngineStore.LastCreatedScene,\r\n        onSuccess: Nullable<(assets: AssetContainer) => void> = null,\r\n        onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n        onError: Nullable<(scene: Scene, message: string, exception?: any) => void> = null,\r\n        pluginExtension: Nullable<string> = null,\r\n        name: string = \"\"\r\n    ): Nullable<ISceneLoaderPlugin | ISceneLoaderPluginAsync> {\r\n        if (!scene) {\r\n            Logger.Error(\"No scene available to load asset container to\");\r\n            return null;\r\n        }\r\n\r\n        const fileInfo = SceneLoader._GetFileInfo(rootUrl, sceneFilename);\r\n        if (!fileInfo) {\r\n            return null;\r\n        }\r\n\r\n        const loadingToken = {};\r\n        scene.addPendingData(loadingToken);\r\n\r\n        const disposeHandler = () => {\r\n            scene.removePendingData(loadingToken);\r\n        };\r\n\r\n        const errorHandler = (message?: string, exception?: any) => {\r\n            const errorMessage = SceneLoader._FormatErrorMessage(fileInfo, message, exception);\r\n\r\n            if (onError) {\r\n                onError(scene, errorMessage, new RuntimeError(errorMessage, ErrorCodes.SceneLoaderError, exception));\r\n            } else {\r\n                Logger.Error(errorMessage);\r\n                // should the exception be thrown?\r\n            }\r\n\r\n            disposeHandler();\r\n        };\r\n\r\n        const progressHandler = onProgress\r\n            ? (event: ISceneLoaderProgressEvent) => {\r\n                  try {\r\n                      onProgress(event);\r\n                  } catch (e) {\r\n                      errorHandler(\"Error in onProgress callback\", e);\r\n                  }\r\n              }\r\n            : undefined;\r\n\r\n        const successHandler = (assets: AssetContainer) => {\r\n            if (onSuccess) {\r\n                try {\r\n                    onSuccess(assets);\r\n                } catch (e) {\r\n                    errorHandler(\"Error in onSuccess callback\", e);\r\n                }\r\n            }\r\n\r\n            scene.removePendingData(loadingToken);\r\n        };\r\n\r\n        return SceneLoader._LoadData(\r\n            fileInfo,\r\n            scene,\r\n            (plugin, data) => {\r\n                if ((<any>plugin).loadAssetContainer) {\r\n                    const syncedPlugin = <ISceneLoaderPlugin>plugin;\r\n                    const assetContainer = syncedPlugin.loadAssetContainer(scene, data, fileInfo.rootUrl, errorHandler);\r\n                    if (!assetContainer) {\r\n                        return;\r\n                    }\r\n                    assetContainer.populateRootNodes();\r\n                    scene.loadingPluginName = plugin.name;\r\n                    successHandler(assetContainer);\r\n                } else if ((<any>plugin).loadAssetContainerAsync) {\r\n                    const asyncedPlugin = <ISceneLoaderPluginAsync>plugin;\r\n                    asyncedPlugin\r\n                        .loadAssetContainerAsync(scene, data, fileInfo.rootUrl, progressHandler, fileInfo.name)\r\n                        .then((assetContainer) => {\r\n                            assetContainer.populateRootNodes();\r\n                            scene.loadingPluginName = plugin.name;\r\n                            successHandler(assetContainer);\r\n                        })\r\n                        .catch((error) => {\r\n                            errorHandler(error.message, error);\r\n                        });\r\n                } else {\r\n                    errorHandler(\"LoadAssetContainer is not supported by this plugin. Plugin did not provide a loadAssetContainer or loadAssetContainerAsync method.\");\r\n                }\r\n            },\r\n            progressHandler,\r\n            errorHandler,\r\n            disposeHandler,\r\n            pluginExtension,\r\n            name\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Load a scene into an asset container\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene (default: empty string)\r\n     * @param scene is the instance of Scene to append to\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @returns The loaded asset container\r\n     */\r\n    public static LoadAssetContainerAsync(\r\n        rootUrl: string,\r\n        sceneFilename: string | File = \"\",\r\n        scene: Nullable<Scene> = EngineStore.LastCreatedScene,\r\n        onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n        pluginExtension: Nullable<string> = null\r\n    ): Promise<AssetContainer> {\r\n        return new Promise((resolve, reject) => {\r\n            SceneLoader.LoadAssetContainer(\r\n                rootUrl,\r\n                sceneFilename,\r\n                scene,\r\n                (assetContainer) => {\r\n                    resolve(assetContainer);\r\n                },\r\n                onProgress,\r\n                (scene, message, exception) => {\r\n                    reject(exception || new Error(message));\r\n                },\r\n                pluginExtension\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Import animations from a file into a scene\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene is the instance of BABYLON.Scene to append to (default: last created scene)\r\n     * @param overwriteAnimations when true, animations are cleaned before importing new ones. Animations are appended otherwise\r\n     * @param animationGroupLoadingMode defines how to handle old animations groups before importing new ones\r\n     * @param targetConverter defines a function used to convert animation targets from loaded scene to current scene (default: search node by name)\r\n     * @param onSuccess a callback with the scene when import succeeds\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param onError a callback with the scene, a message, and possibly an exception when import fails\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     */\r\n    public static ImportAnimations(\r\n        rootUrl: string,\r\n        sceneFilename: string | File = \"\",\r\n        scene: Nullable<Scene> = EngineStore.LastCreatedScene,\r\n        overwriteAnimations = true,\r\n        animationGroupLoadingMode = SceneLoaderAnimationGroupLoadingMode.Clean,\r\n        targetConverter: Nullable<(target: any) => any> = null,\r\n        onSuccess: Nullable<(scene: Scene) => void> = null,\r\n        onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n        onError: Nullable<(scene: Scene, message: string, exception?: any) => void> = null,\r\n        pluginExtension: Nullable<string> = null\r\n    ): void {\r\n        if (!scene) {\r\n            Logger.Error(\"No scene available to load animations to\");\r\n            return;\r\n        }\r\n\r\n        if (overwriteAnimations) {\r\n            // Reset, stop and dispose all animations before loading new ones\r\n            for (const animatable of scene.animatables) {\r\n                animatable.reset();\r\n            }\r\n            scene.stopAllAnimations();\r\n            scene.animationGroups.slice().forEach((animationGroup) => {\r\n                animationGroup.dispose();\r\n            });\r\n            const nodes = scene.getNodes();\r\n            nodes.forEach((node) => {\r\n                if (node.animations) {\r\n                    node.animations = [];\r\n                }\r\n            });\r\n        } else {\r\n            switch (animationGroupLoadingMode) {\r\n                case SceneLoaderAnimationGroupLoadingMode.Clean:\r\n                    scene.animationGroups.slice().forEach((animationGroup) => {\r\n                        animationGroup.dispose();\r\n                    });\r\n                    break;\r\n                case SceneLoaderAnimationGroupLoadingMode.Stop:\r\n                    scene.animationGroups.forEach((animationGroup) => {\r\n                        animationGroup.stop();\r\n                    });\r\n                    break;\r\n                case SceneLoaderAnimationGroupLoadingMode.Sync:\r\n                    scene.animationGroups.forEach((animationGroup) => {\r\n                        animationGroup.reset();\r\n                        animationGroup.restart();\r\n                    });\r\n                    break;\r\n                case SceneLoaderAnimationGroupLoadingMode.NoSync:\r\n                    // nothing to do\r\n                    break;\r\n                default:\r\n                    Logger.Error(\"Unknown animation group loading mode value '\" + animationGroupLoadingMode + \"'\");\r\n                    return;\r\n            }\r\n        }\r\n\r\n        const startingIndexForNewAnimatables = scene.animatables.length;\r\n\r\n        const onAssetContainerLoaded = (container: AssetContainer) => {\r\n            container.mergeAnimationsTo(scene, scene.animatables.slice(startingIndexForNewAnimatables), targetConverter);\r\n\r\n            container.dispose();\r\n\r\n            scene.onAnimationFileImportedObservable.notifyObservers(scene);\r\n\r\n            if (onSuccess) {\r\n                onSuccess(scene);\r\n            }\r\n        };\r\n\r\n        this.LoadAssetContainer(rootUrl, sceneFilename, scene, onAssetContainerLoaded, onProgress, onError, pluginExtension);\r\n    }\r\n\r\n    /**\r\n     * Import animations from a file into a scene\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene is the instance of BABYLON.Scene to append to (default: last created scene)\r\n     * @param overwriteAnimations when true, animations are cleaned before importing new ones. Animations are appended otherwise\r\n     * @param animationGroupLoadingMode defines how to handle old animations groups before importing new ones\r\n     * @param targetConverter defines a function used to convert animation targets from loaded scene to current scene (default: search node by name)\r\n     * @param onSuccess a callback with the scene when import succeeds\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param onError a callback with the scene, a message, and possibly an exception when import fails\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @returns the updated scene with imported animations\r\n     */\r\n    public static ImportAnimationsAsync(\r\n        rootUrl: string,\r\n        sceneFilename: string | File = \"\",\r\n        scene: Nullable<Scene> = EngineStore.LastCreatedScene,\r\n        overwriteAnimations = true,\r\n        animationGroupLoadingMode = SceneLoaderAnimationGroupLoadingMode.Clean,\r\n        targetConverter: Nullable<(target: any) => any> = null,\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        onSuccess: Nullable<(scene: Scene) => void> = null,\r\n        onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        onError: Nullable<(scene: Scene, message: string, exception?: any) => void> = null,\r\n        pluginExtension: Nullable<string> = null\r\n    ): Promise<Scene> {\r\n        return new Promise((resolve, reject) => {\r\n            SceneLoader.ImportAnimations(\r\n                rootUrl,\r\n                sceneFilename,\r\n                scene,\r\n                overwriteAnimations,\r\n                animationGroupLoadingMode,\r\n                targetConverter,\r\n                (_scene: Scene) => {\r\n                    resolve(_scene);\r\n                },\r\n                onProgress,\r\n                (_scene: Scene, message: string, exception: any) => {\r\n                    reject(exception || new Error(message));\r\n                },\r\n                pluginExtension\r\n            );\r\n        });\r\n    }\r\n}\r\n"]}