{"version": 3, "file": "postProcessRenderEffect.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/PostProcesses/RenderPipeline/postProcessRenderEffect.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAIzC;;;;GAIG;AACH,MAAM,OAAO,uBAAuB;IAehC;;;;;;;OAOG;IACH,YAAY,MAAc,EAAE,IAAY,EAAE,gBAAkE,EAAE,cAAwB;QAClI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,cAAc,IAAI,IAAI,CAAC;QAE9C,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAE1C,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAE5B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,EAAE;gBAClE,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACvC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;oBACnD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE;wBAC3B,OAAO,KAAK,CAAC;qBAChB;iBACJ;aACJ;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,OAAO,KAAU,CAAC;IAczB;;;;OAIG;IACI,cAAc,CAAC,OAAY;QAC9B,IAAI,SAAS,CAAC;QAEd,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEvD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACvB,IAAI,CAAC,MAAM,EAAE;gBACT,SAAS;aACZ;YAED,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;YAE/B,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,SAAS,GAAG,CAAC,CAAC;aACjB;iBAAM;gBACH,SAAS,GAAG,UAAU,CAAC;aAC1B;YAED,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;gBACjC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC7C,IAAI,WAAW,EAAE;oBACb,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;iBAC7F;aACJ;YAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;gBACrC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;aAC3C;YAED,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,WAAwB,EAAE,EAAE;gBAChE,MAAM,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBAEpD,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;gBAC5B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC;aACtC;SACJ;IACL,CAAC;IAcD;;;;OAIG;IACI,cAAc,CAAC,OAAY;QAC9B,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEvD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,MAAM,GAAW,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,UAAU,GAAW,MAAM,CAAC,IAAI,CAAC;YACvC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;YAEjF,IAAI,aAAa,EAAE;gBACf,aAAa,CAAC,OAAO,CAAC,CAAC,WAAwB,EAAE,EAAE;oBAC/C,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBAC1C,CAAC,CAAC,CAAC;aACN;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;gBAC3B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;aACpC;YAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;SAC7C;IACL,CAAC;IAcD;;;;OAIG;IACI,OAAO,CAAC,OAAY;QACvB,MAAM,IAAI,GAA4B,KAAK,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhF,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;YAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YAExD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChE,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpD,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACjD,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE;oBACnD,IAAK,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;iBACxE;aACJ;SACJ;IACL,CAAC;IAcD;;;;OAIG;IACI,QAAQ,CAAC,OAAY;QACxB,MAAM,IAAI,GAA4B,KAAK,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhF,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;YAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;gBAC/E,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,MAAe;QACnC,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;SACjC;aAAM;YACH,IAAI,CAAC,MAAM,EAAE;gBACT,OAAO,IAAI,CAAC;aACf;YACD,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC3C;IACL,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport type { PostProcess } from \"../../PostProcesses/postProcess\";\r\nimport type { Engine } from \"../../Engines/engine\";\r\n/**\r\n * This represents a set of one or more post processes in Babylon.\r\n * A post process can be used to apply a shader to a texture after it is rendered.\r\n * @example https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/postProcessRenderPipeline\r\n */\r\nexport class PostProcessRenderEffect {\r\n    private _postProcesses: { [Key: string]: Array<PostProcess> };\r\n    private _getPostProcesses: () => Nullable<PostProcess | Array<PostProcess>>;\r\n\r\n    private _singleInstance: boolean;\r\n\r\n    private _cameras: { [key: string]: Nullable<Camera> };\r\n    private _indicesForCamera: { [key: string]: number[] };\r\n\r\n    /**\r\n     * Name of the effect\r\n     * @internal\r\n     */\r\n    public _name: string;\r\n\r\n    /**\r\n     * Instantiates a post process render effect.\r\n     * A post process can be used to apply a shader to a texture after it is rendered.\r\n     * @param engine The engine the effect is tied to\r\n     * @param name The name of the effect\r\n     * @param getPostProcesses A function that returns a set of post processes which the effect will run in order to be run.\r\n     * @param singleInstance False if this post process can be run on multiple cameras. (default: true)\r\n     */\r\n    constructor(engine: Engine, name: string, getPostProcesses: () => Nullable<PostProcess | Array<PostProcess>>, singleInstance?: boolean) {\r\n        this._name = name;\r\n        this._singleInstance = singleInstance || true;\r\n\r\n        this._getPostProcesses = getPostProcesses;\r\n\r\n        this._cameras = {};\r\n        this._indicesForCamera = {};\r\n\r\n        this._postProcesses = {};\r\n    }\r\n\r\n    /**\r\n     * Checks if all the post processes in the effect are supported.\r\n     */\r\n    public get isSupported(): boolean {\r\n        for (const index in this._postProcesses) {\r\n            if (Object.prototype.hasOwnProperty.call(this._postProcesses, index)) {\r\n                const pps = this._postProcesses[index];\r\n                for (let ppIndex = 0; ppIndex < pps.length; ppIndex++) {\r\n                    if (!pps[ppIndex].isSupported) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Updates the current state of the effect\r\n     * @internal\r\n     */\r\n    public _update(): void {}\r\n\r\n    /**\r\n     * Attaches the effect on cameras\r\n     * @param cameras The camera to attach to.\r\n     * @internal\r\n     */\r\n    public _attachCameras(cameras: Camera): void;\r\n    /**\r\n     * Attaches the effect on cameras\r\n     * @param cameras The camera to attach to.\r\n     * @internal\r\n     */\r\n    public _attachCameras(cameras: Camera[]): void;\r\n    /**\r\n     * Attaches the effect on cameras\r\n     * @param cameras The camera to attach to.\r\n     * @internal\r\n     */\r\n    public _attachCameras(cameras: any): void {\r\n        let cameraKey;\r\n\r\n        const cams = Tools.MakeArray(cameras || this._cameras);\r\n\r\n        if (!cams) {\r\n            return;\r\n        }\r\n\r\n        for (let i = 0; i < cams.length; i++) {\r\n            const camera = cams[i];\r\n            if (!camera) {\r\n                continue;\r\n            }\r\n\r\n            const cameraName = camera.name;\r\n\r\n            if (this._singleInstance) {\r\n                cameraKey = 0;\r\n            } else {\r\n                cameraKey = cameraName;\r\n            }\r\n\r\n            if (!this._postProcesses[cameraKey]) {\r\n                const postProcess = this._getPostProcesses();\r\n                if (postProcess) {\r\n                    this._postProcesses[cameraKey] = Array.isArray(postProcess) ? postProcess : [postProcess];\r\n                }\r\n            }\r\n\r\n            if (!this._indicesForCamera[cameraName]) {\r\n                this._indicesForCamera[cameraName] = [];\r\n            }\r\n\r\n            this._postProcesses[cameraKey].forEach((postProcess: PostProcess) => {\r\n                const index = camera.attachPostProcess(postProcess);\r\n\r\n                this._indicesForCamera[cameraName].push(index);\r\n            });\r\n\r\n            if (!this._cameras[cameraName]) {\r\n                this._cameras[cameraName] = camera;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Detaches the effect on cameras\r\n     * @param cameras The camera to detach from.\r\n     * @internal\r\n     */\r\n    public _detachCameras(cameras: Camera): void;\r\n    /**\r\n     * Detaches the effect on cameras\r\n     * @param cameras The camera to detach from.\r\n     * @internal\r\n     */\r\n    public _detachCameras(cameras: Camera[]): void;\r\n    /**\r\n     * Detaches the effect on cameras\r\n     * @param cameras The camera to detach from.\r\n     * @internal\r\n     */\r\n    public _detachCameras(cameras: any): void {\r\n        const cams = Tools.MakeArray(cameras || this._cameras);\r\n\r\n        if (!cams) {\r\n            return;\r\n        }\r\n\r\n        for (let i = 0; i < cams.length; i++) {\r\n            const camera: Camera = cams[i];\r\n            const cameraName: string = camera.name;\r\n            const postProcesses = this._postProcesses[this._singleInstance ? 0 : cameraName];\r\n\r\n            if (postProcesses) {\r\n                postProcesses.forEach((postProcess: PostProcess) => {\r\n                    camera.detachPostProcess(postProcess);\r\n                });\r\n            }\r\n\r\n            if (this._cameras[cameraName]) {\r\n                this._cameras[cameraName] = null;\r\n            }\r\n\r\n            delete this._indicesForCamera[cameraName];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enables the effect on given cameras\r\n     * @param cameras The camera to enable.\r\n     * @internal\r\n     */\r\n    public _enable(cameras: Camera): void;\r\n    /**\r\n     * Enables the effect on given cameras\r\n     * @param cameras The camera to enable.\r\n     * @internal\r\n     */\r\n    public _enable(cameras: Nullable<Camera[]>): void;\r\n    /**\r\n     * Enables the effect on given cameras\r\n     * @param cameras The camera to enable.\r\n     * @internal\r\n     */\r\n    public _enable(cameras: any): void {\r\n        const cams: Nullable<Array<Camera>> = Tools.MakeArray(cameras || this._cameras);\r\n\r\n        if (!cams) {\r\n            return;\r\n        }\r\n\r\n        for (let i = 0; i < cams.length; i++) {\r\n            const camera = cams[i];\r\n            const cameraName = camera.name;\r\n            const cameraKey = this._singleInstance ? 0 : cameraName;\r\n\r\n            for (let j = 0; j < this._indicesForCamera[cameraName].length; j++) {\r\n                const index = this._indicesForCamera[cameraName][j];\r\n                const postProcess = camera._postProcesses[index];\r\n                if (postProcess === undefined || postProcess === null) {\r\n                    cams![i].attachPostProcess(this._postProcesses[cameraKey][j], index);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disables the effect on the given cameras\r\n     * @param cameras The camera to disable.\r\n     * @internal\r\n     */\r\n    public _disable(cameras: Camera): void;\r\n    /**\r\n     * Disables the effect on the given cameras\r\n     * @param cameras The camera to disable.\r\n     * @internal\r\n     */\r\n    public _disable(cameras: Nullable<Camera[]>): void;\r\n    /**\r\n     * Disables the effect on the given cameras\r\n     * @param cameras The camera to disable.\r\n     * @internal\r\n     */\r\n    public _disable(cameras: any): void {\r\n        const cams: Nullable<Array<Camera>> = Tools.MakeArray(cameras || this._cameras);\r\n\r\n        if (!cams) {\r\n            return;\r\n        }\r\n\r\n        for (let i = 0; i < cams.length; i++) {\r\n            const camera = cams[i];\r\n            const cameraName = camera.name;\r\n            this._postProcesses[this._singleInstance ? 0 : cameraName].forEach((postProcess) => {\r\n                camera.detachPostProcess(postProcess);\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a list of the post processes contained in the effect.\r\n     * @param camera The camera to get the post processes on.\r\n     * @returns The list of the post processes in the effect.\r\n     */\r\n    public getPostProcesses(camera?: Camera): Nullable<Array<PostProcess>> {\r\n        if (this._singleInstance) {\r\n            return this._postProcesses[0];\r\n        } else {\r\n            if (!camera) {\r\n                return null;\r\n            }\r\n            return this._postProcesses[camera.name];\r\n        }\r\n    }\r\n}\r\n"]}