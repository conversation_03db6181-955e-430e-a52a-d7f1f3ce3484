{"version": 3, "file": "thinTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/thinTexture.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAGpD,OAAO,EAAE,IAAI,EAAE,MAAM,uBAAuB,CAAC;AAK7C;;;GAGG;AACH,MAAM,OAAO,WAAW;IAEpB;;;;;;OAMG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,IAAW,KAAK,CAAC,KAAa;QAC1B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAGD;;;;;;OAMG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,IAAW,KAAK,CAAC,KAAa;QAC1B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAuBD;;;OAGG;IACH,IAAW,eAAe;QACtB,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IAChC,CAAC;IAED,gEAAgE;IAChE,IAAc,MAAM,CAAC,KAAc;QAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED,gEAAgE;IAChE,IAAc,IAAI,CAAC,KAAc;QAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;IACnC,CAAC;IAED,gEAAgE;IAChE,IAAc,SAAS,CAAC,KAAc;QAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,aAAa,CAAC;IACzB,CAAC;IAUO,MAAM,CAAC,sBAAsB,CAAC,OAAkE;QACpG,OAAQ,OAA+B,EAAE,WAAW,KAAK,SAAS,CAAC;IACvE,CAAC;IAED;;;;;OAKG;IACH,YAAY,eAAgE;QAnJlE,WAAM,GAAG,SAAS,CAAC,wBAAwB,CAAC;QAgB5C,WAAM,GAAG,SAAS,CAAC,wBAAwB,CAAC;QAgBtD;;;;;;WAMG;QACI,UAAK,GAAG,SAAS,CAAC,wBAAwB,CAAC;QAElD;;;;WAIG;QACI,8BAAyB,GAAG,CAAC,CAAC;QAErC;;WAEG;QACI,mBAAc,GAAG,SAAS,CAAC,mBAAmB,CAAC;QA8EtD,gBAAgB;QACT,aAAQ,GAA8B,IAAI,CAAC;QAExC,YAAO,GAAyB,IAAI,CAAC;QAEvC,gBAAW,GAAU,IAAI,CAAC,IAAI,EAAE,CAAC;QACjC,oBAAe,GAAU,IAAI,CAAC,IAAI,EAAE,CAAC;QA8F7C,gBAAgB;QACN,yBAAoB,GAAG,SAAS,CAAC,6BAA6B,CAAC;QAlFrE,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;QAChH,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;SAC5C;IACL,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,CAAC,wBAAwB,EAAE;YAC5D,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;SAChC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,SAAS,KAAU,CAAC;IAE3B;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;gBACrB,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAC7C,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC/C,OAAO,IAAI,CAAC,WAAW,CAAC;aAC3B;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;gBACrB,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAC7C,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAC9C,OAAO,IAAI,CAAC,WAAW,CAAC;aAC3B;SACJ;QAED,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACI,WAAW;QACd,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACnC,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,CAAC,CAAC;YAC/B,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;YAChC,OAAO,IAAI,CAAC,eAAe,CAAC;SAC/B;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;YACrB,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YACjD,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YAClD,OAAO,IAAI,CAAC,eAAe,CAAC;SAC/B;QAED,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QACrD,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;QACvD,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAKD;;OAEG;IACH,IAAW,YAAY;QACnB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO,IAAI,CAAC,oBAAoB,CAAC;SACpC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;IACtC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACI,kBAAkB,CAAC,YAAoB;QAC1C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SACvE;IACL,CAAC;IAED;;OAEG;IACI,sBAAsB;QACzB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACxB;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACvB;IACL,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport { Constants } from \"../../Engines/constants\";\r\n\r\nimport type { ISize } from \"../../Maths/math.size\";\r\nimport { Size } from \"../../Maths/math.size\";\r\n\r\nimport type { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { RenderTargetWrapper } from \"core/Engines/renderTargetWrapper\";\r\n\r\n/**\r\n * Base class of all the textures in babylon.\r\n * It groups all the common properties required to work with Thin Engine.\r\n */\r\nexport class ThinTexture {\r\n    protected _wrapU = Constants.TEXTURE_WRAP_ADDRESSMODE;\r\n    /**\r\n     * | Value | Type               | Description |\r\n     * | ----- | ------------------ | ----------- |\r\n     * | 0     | CLAMP_ADDRESSMODE  |             |\r\n     * | 1     | WRAP_ADDRESSMODE   |             |\r\n     * | 2     | MIRROR_ADDRESSMODE |             |\r\n     */\r\n    public get wrapU() {\r\n        return this._wrapU;\r\n    }\r\n\r\n    public set wrapU(value: number) {\r\n        this._wrapU = value;\r\n    }\r\n\r\n    protected _wrapV = Constants.TEXTURE_WRAP_ADDRESSMODE;\r\n    /**\r\n     * | Value | Type               | Description |\r\n     * | ----- | ------------------ | ----------- |\r\n     * | 0     | CLAMP_ADDRESSMODE  |             |\r\n     * | 1     | WRAP_ADDRESSMODE   |             |\r\n     * | 2     | MIRROR_ADDRESSMODE |             |\r\n     */\r\n    public get wrapV() {\r\n        return this._wrapV;\r\n    }\r\n\r\n    public set wrapV(value: number) {\r\n        this._wrapV = value;\r\n    }\r\n\r\n    /**\r\n     * | Value | Type               | Description |\r\n     * | ----- | ------------------ | ----------- |\r\n     * | 0     | CLAMP_ADDRESSMODE  |             |\r\n     * | 1     | WRAP_ADDRESSMODE   |             |\r\n     * | 2     | MIRROR_ADDRESSMODE |             |\r\n     */\r\n    public wrapR = Constants.TEXTURE_WRAP_ADDRESSMODE;\r\n\r\n    /**\r\n     * With compliant hardware and browser (supporting anisotropic filtering)\r\n     * this defines the level of anisotropic filtering in the texture.\r\n     * The higher the better but the slower. This defaults to 4 as it seems to be the best tradeoff.\r\n     */\r\n    public anisotropicFilteringLevel = 4;\r\n\r\n    /**\r\n     * Define the current state of the loading sequence when in delayed load mode.\r\n     */\r\n    public delayLoadState = Constants.DELAYLOADSTATE_NONE;\r\n\r\n    /**\r\n     * How a texture is mapped.\r\n     * Unused in thin texture mode.\r\n     */\r\n    public get coordinatesMode(): number {\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Define if the texture is a cube texture or if false a 2d texture.\r\n     */\r\n    public get isCube(): boolean {\r\n        if (!this._texture) {\r\n            return false;\r\n        }\r\n\r\n        return this._texture.isCube;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected set isCube(value: boolean) {\r\n        if (!this._texture) {\r\n            return;\r\n        }\r\n\r\n        this._texture.isCube = value;\r\n    }\r\n\r\n    /**\r\n     * Define if the texture is a 3d texture (webgl 2) or if false a 2d texture.\r\n     */\r\n    public get is3D(): boolean {\r\n        if (!this._texture) {\r\n            return false;\r\n        }\r\n\r\n        return this._texture.is3D;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected set is3D(value: boolean) {\r\n        if (!this._texture) {\r\n            return;\r\n        }\r\n\r\n        this._texture.is3D = value;\r\n    }\r\n\r\n    /**\r\n     * Define if the texture is a 2d array texture (webgl 2) or if false a 2d texture.\r\n     */\r\n    public get is2DArray(): boolean {\r\n        if (!this._texture) {\r\n            return false;\r\n        }\r\n\r\n        return this._texture.is2DArray;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected set is2DArray(value: boolean) {\r\n        if (!this._texture) {\r\n            return;\r\n        }\r\n\r\n        this._texture.is2DArray = value;\r\n    }\r\n\r\n    /**\r\n     * Get the class name of the texture.\r\n     * @returns \"ThinTexture\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"ThinTexture\";\r\n    }\r\n\r\n    /** @internal */\r\n    public _texture: Nullable<InternalTexture> = null;\r\n\r\n    protected _engine: Nullable<ThinEngine> = null;\r\n\r\n    private _cachedSize: ISize = Size.Zero();\r\n    private _cachedBaseSize: ISize = Size.Zero();\r\n\r\n    private static _IsRenderTargetWrapper(texture: Nullable<InternalTexture> | Nullable<RenderTargetWrapper>): texture is RenderTargetWrapper {\r\n        return (texture as RenderTargetWrapper)?._shareDepth !== undefined;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new ThinTexture.\r\n     * Base class of all the textures in babylon.\r\n     * This can be used as an internal texture wrapper in ThinEngine to benefit from the cache\r\n     * @param internalTexture Define the internalTexture to wrap. You can also pass a RenderTargetWrapper, in which case the texture will be the render target's texture\r\n     */\r\n    constructor(internalTexture: Nullable<InternalTexture | RenderTargetWrapper>) {\r\n        this._texture = ThinTexture._IsRenderTargetWrapper(internalTexture) ? internalTexture.texture : internalTexture;\r\n        if (this._texture) {\r\n            this._engine = this._texture.getEngine();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get if the texture is ready to be used (downloaded, converted, mip mapped...).\r\n     * @returns true if fully ready\r\n     */\r\n    public isReady(): boolean {\r\n        if (this.delayLoadState === Constants.DELAYLOADSTATE_NOTLOADED) {\r\n            this.delayLoad();\r\n            return false;\r\n        }\r\n\r\n        if (this._texture) {\r\n            return this._texture.isReady;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Triggers the load sequence in delayed load mode.\r\n     */\r\n    public delayLoad(): void {}\r\n\r\n    /**\r\n     * Get the underlying lower level texture from Babylon.\r\n     * @returns the internal texture\r\n     */\r\n    public getInternalTexture(): Nullable<InternalTexture> {\r\n        return this._texture;\r\n    }\r\n\r\n    /**\r\n     * Get the size of the texture.\r\n     * @returns the texture size.\r\n     */\r\n    public getSize(): ISize {\r\n        if (this._texture) {\r\n            if (this._texture.width) {\r\n                this._cachedSize.width = this._texture.width;\r\n                this._cachedSize.height = this._texture.height;\r\n                return this._cachedSize;\r\n            }\r\n\r\n            if (this._texture._size) {\r\n                this._cachedSize.width = this._texture._size;\r\n                this._cachedSize.height = this._texture._size;\r\n                return this._cachedSize;\r\n            }\r\n        }\r\n\r\n        return this._cachedSize;\r\n    }\r\n\r\n    /**\r\n     * Get the base size of the texture.\r\n     * It can be different from the size if the texture has been resized for POT for instance\r\n     * @returns the base size\r\n     */\r\n    public getBaseSize(): ISize {\r\n        if (!this.isReady() || !this._texture) {\r\n            this._cachedBaseSize.width = 0;\r\n            this._cachedBaseSize.height = 0;\r\n            return this._cachedBaseSize;\r\n        }\r\n\r\n        if (this._texture._size) {\r\n            this._cachedBaseSize.width = this._texture._size;\r\n            this._cachedBaseSize.height = this._texture._size;\r\n            return this._cachedBaseSize;\r\n        }\r\n\r\n        this._cachedBaseSize.width = this._texture.baseWidth;\r\n        this._cachedBaseSize.height = this._texture.baseHeight;\r\n        return this._cachedBaseSize;\r\n    }\r\n\r\n    /** @internal */\r\n    protected _initialSamplingMode = Constants.TEXTURE_BILINEAR_SAMPLINGMODE;\r\n\r\n    /**\r\n     * Get the current sampling mode associated with the texture.\r\n     */\r\n    public get samplingMode(): number {\r\n        if (!this._texture) {\r\n            return this._initialSamplingMode;\r\n        }\r\n\r\n        return this._texture.samplingMode;\r\n    }\r\n\r\n    /**\r\n     * Update the sampling mode of the texture.\r\n     * Default is Trilinear mode.\r\n     *\r\n     * | Value | Type               | Description |\r\n     * | ----- | ------------------ | ----------- |\r\n     * | 1     | NEAREST_SAMPLINGMODE or NEAREST_NEAREST_MIPLINEAR  | Nearest is: mag = nearest, min = nearest, mip = linear |\r\n     * | 2     | BILINEAR_SAMPLINGMODE or LINEAR_LINEAR_MIPNEAREST | Bilinear is: mag = linear, min = linear, mip = nearest |\r\n     * | 3     | TRILINEAR_SAMPLINGMODE or LINEAR_LINEAR_MIPLINEAR | Trilinear is: mag = linear, min = linear, mip = linear |\r\n     * | 4     | NEAREST_NEAREST_MIPNEAREST |             |\r\n     * | 5    | NEAREST_LINEAR_MIPNEAREST |             |\r\n     * | 6    | NEAREST_LINEAR_MIPLINEAR |             |\r\n     * | 7    | NEAREST_LINEAR |             |\r\n     * | 8    | NEAREST_NEAREST |             |\r\n     * | 9   | LINEAR_NEAREST_MIPNEAREST |             |\r\n     * | 10   | LINEAR_NEAREST_MIPLINEAR |             |\r\n     * | 11   | LINEAR_LINEAR |             |\r\n     * | 12   | LINEAR_NEAREST |             |\r\n     *\r\n     *    > _mag_: magnification filter (close to the viewer)\r\n     *    > _min_: minification filter (far from the viewer)\r\n     *    > _mip_: filter used between mip map levels\r\n     *@param samplingMode Define the new sampling mode of the texture\r\n     */\r\n    public updateSamplingMode(samplingMode: number): void {\r\n        if (this._texture && this._engine) {\r\n            this._engine.updateTextureSamplingMode(samplingMode, this._texture);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Release and destroy the underlying lower level texture aka internalTexture.\r\n     */\r\n    public releaseInternalTexture(): void {\r\n        if (this._texture) {\r\n            this._texture.dispose();\r\n            this._texture = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Dispose the texture and release its associated resources.\r\n     */\r\n    public dispose(): void {\r\n        if (this._texture) {\r\n            this.releaseInternalTexture();\r\n            this._engine = null;\r\n        }\r\n    }\r\n}\r\n"]}