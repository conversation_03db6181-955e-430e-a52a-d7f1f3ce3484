{"version": 3, "file": "timeToken.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Instrumentation/timeToken.ts"], "names": [], "mappings": "AACA;;IAEI;AACJ,gEAAgE;AAChE,MAAM,OAAO,UAAU;IAAvB;QAIW,2BAAsB,GAAG,KAAK,CAAC;IAC1C,CAAC;CAAA", "sourcesContent": ["import type { Nullable } from \"../types\";\r\n/**\r\n * @internal\r\n **/\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class _TimeToken {\r\n    public _startTimeQuery: Nullable<WebGLQuery>;\r\n    public _endTimeQuery: Nullable<WebGLQuery>;\r\n    public _timeElapsedQuery: Nullable<WebGLQuery>;\r\n    public _timeElapsedQueryEnded = false;\r\n}\r\n"]}