import type { IFlowGraphBlockConfiguration } from "../../../flowGraphBlock";
import { FlowGraphBinaryOperationBlock } from "../flowGraphBinaryOperationBlock";
import { FlowGraphConstantOperationBlock } from "../flowGraphConstantOperationBlock";
import { Matrix, Vector2, Vector3 } from "../../../../Maths/math.vector";
import { FlowGraphUnaryOperationBlock } from "../flowGraphUnaryOperationBlock";
import { FlowGraphTernaryOperationBlock } from "../flowGraphTernaryOperationBlock";
import { FlowGraphInteger } from "../../../flowGraphInteger";
/**
 * @experimental
 * Polymorphic add block.
 */
export declare class FlowGraphAddBlock extends FlowGraphBinaryOperationBlock<any, any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicAdd;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Polymorphic add block.
 */
export declare class FlowGraphSubtractBlock extends FlowGraphBinaryOperationBlock<any, any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicAdd;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Polymorphic multiply block.
 * In case of matrix, it is a component wise multiplication.
 */
export declare class FlowGraphMultiplyBlock extends FlowGraphBinaryOperationBlock<any, any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicMultiply;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Polymorphic division block.
 */
export declare class FlowGraphDivideBlock extends FlowGraphBinaryOperationBlock<any, any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicDivide;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Random number between 0 and 1.
 */
export declare class FlowGraphRandomBlock extends FlowGraphConstantOperationBlock<number> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Dot product block.
 */
export declare class FlowGraphDotBlock extends FlowGraphBinaryOperationBlock<any, any, number> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicDot;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * E constant.
 */
export declare class FlowGraphEBlock extends FlowGraphConstantOperationBlock<number> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Pi constant.
 */
export declare class FlowGraphPiBlock extends FlowGraphConstantOperationBlock<number> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Positive inf constant.
 */
export declare class FlowGraphInfBlock extends FlowGraphConstantOperationBlock<number> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * NaN constant.
 */
export declare class FlowGraphNaNBlock extends FlowGraphConstantOperationBlock<number> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Absolute value block.
 */
export declare class FlowGraphAbsBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicAbs;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Sign block.
 */
export declare class FlowGraphSignBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicSign;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Truncation block.
 */
export declare class FlowGraphTruncBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicTrunc;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Floor block.
 */
export declare class FlowGraphFloorBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicFloor;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Ceiling block.
 */
export declare class FlowGraphCeilBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicCeiling;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Fract block.
 */
export declare class FlowGraphFractBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicFract;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Negation block.
 */
export declare class FlowGraphNegBlock extends FlowGraphUnaryOperationBlock<any, any> {
    /**
     * construct a new negation block.
     * @param config optional configuration
     */
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicNeg;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Remainder block.
 */
export declare class FlowGraphRemainderBlock extends FlowGraphBinaryOperationBlock<any, any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicRemainder;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Min block.
 */
export declare class FlowGraphMinBlock extends FlowGraphBinaryOperationBlock<any, any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicMin;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Max block
 */
export declare class FlowGraphMaxBlock extends FlowGraphBinaryOperationBlock<any, any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicMax;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Clamp block.
 */
export declare class FlowGraphClampBlock extends FlowGraphTernaryOperationBlock<any, any, any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicClamp;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Saturate block.
 */
export declare class FlowGraphSaturateBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicSaturate;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Interpolate block.
 */
export declare class FlowGraphInterpolateBlock extends FlowGraphTernaryOperationBlock<any, any, any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _interpolate;
    private _polymorphicInterpolate;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Equals block.
 */
export declare class FlowGraphEqBlock extends FlowGraphBinaryOperationBlock<any, any, boolean> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicEq;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Less than block.
 */
export declare class FlowGraphLessThanBlock extends FlowGraphBinaryOperationBlock<any, any, boolean> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicLessThan;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Less than or equal block.
 */
export declare class FlowGraphLessThanOrEqualBlock extends FlowGraphBinaryOperationBlock<any, any, boolean> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicLessThanOrEqual;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Greater than block.
 */
export declare class FlowGraphGreaterThanBlock extends FlowGraphBinaryOperationBlock<any, any, boolean> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicGreaterThan;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Greater than or equal block.
 */
export declare class FlowGraphGreaterThanOrEqualBlock extends FlowGraphBinaryOperationBlock<any, any, boolean> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicGreaterThanOrEqual;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Is NaN block.
 */
export declare class FlowGraphIsNanBlock extends FlowGraphUnaryOperationBlock<any, boolean> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicIsNan;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Is Inf block.
 */
export declare class FlowGraphIsInfBlock extends FlowGraphUnaryOperationBlock<any, boolean> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicIsInf;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Convert degrees to radians block.
 */
export declare class FlowGraphDegToRadBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _degToRad;
    private _polymorphicDegToRad;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Convert radians to degrees block.
 */
export declare class FlowGraphRadToDegBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _radToDeg;
    private _polymorphicRadToDeg;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Sin block.
 */
export declare class FlowGraphSinBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicSin;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Cos block.
 */
export declare class FlowGraphCosBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicCos;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Tan block.
 */
export declare class FlowGraphTanBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicTan;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Arcsin block.
 */
export declare class FlowGraphAsinBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicAsin;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Arccos block.
 */
export declare class FlowGraphAcosBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicAcos;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Arctan block.
 */
export declare class FlowGraphAtanBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicAtan;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Arctan2 block.
 */
export declare class FlowGraphAtan2Block extends FlowGraphBinaryOperationBlock<any, any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicAtan2;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Hyperbolic sin block.
 */
export declare class FlowGraphSinhBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicSinh;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Hyperbolic cos block.
 */
export declare class FlowGraphCoshBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicCosh;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Hyperbolic tan block.
 */
export declare class FlowGraphTanhBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicTanh;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Hyperbolic arcsin block.
 */
export declare class FlowGraphAsinhBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicAsinh;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Hyperbolic arccos block.
 */
export declare class FlowGraphAcoshBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicAcosh;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Hyperbolic arctan block.
 */
export declare class FlowGraphAtanhBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicAtanh;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Exponential block.
 */
export declare class FlowGraphExpBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicExp;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Logarithm block.
 */
export declare class FlowGraphLogBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicLog;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Base 2 logarithm block.
 */
export declare class FlowGraphLog2Block extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicLog2;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Base 10 logarithm block.
 */
export declare class FlowGraphLog10Block extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicLog10;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Square root block.
 */
export declare class FlowGraphSqrtBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicSqrt;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Cube root block.
 */
export declare class FlowGraphCubeRootBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicCubeRoot;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Power block.
 */
export declare class FlowGraphPowBlock extends FlowGraphBinaryOperationBlock<any, any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicPow;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Vector length block.
 */
export declare class FlowGraphLengthBlock extends FlowGraphUnaryOperationBlock<any, number> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicLength;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Vector normalize block.
 */
export declare class FlowGraphNormalizeBlock extends FlowGraphUnaryOperationBlock<any, any> {
    constructor(config?: IFlowGraphBlockConfiguration);
    private _polymorphicNormalize;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Cross product block.
 */
export declare class FlowGraphCrossBlock extends FlowGraphBinaryOperationBlock<Vector3, Vector3, Vector3> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * 2D rotation block.
 */
export declare class FlowGraphRotate2DBlock extends FlowGraphBinaryOperationBlock<Vector2, number, Vector2> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * 3D rotation block.
 */
export declare class FlowGraphRotate3DBlock extends FlowGraphTernaryOperationBlock<Vector3, Vector3, number, Vector3> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Transposes a matrix.
 */
export declare class FlowGraphTransposeBlock extends FlowGraphUnaryOperationBlock<Matrix, Matrix> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Gets the determinant of a matrix.
 */
export declare class FlowGraphDeterminantBlock extends FlowGraphUnaryOperationBlock<Matrix, number> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Inverts a matrix.
 */
export declare class FlowGraphInvertMatrixBlock extends FlowGraphUnaryOperationBlock<Matrix, Matrix> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Multiplies two matrices.
 */
export declare class FlowGraphMatMulBlock extends FlowGraphBinaryOperationBlock<Matrix, Matrix, Matrix> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Bitwise NOT operation
 */
export declare class FlowGraphBitwiseNotBlock extends FlowGraphUnaryOperationBlock<FlowGraphInteger, FlowGraphInteger> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Bitwise AND operation
 */
export declare class FlowGraphBitwiseAndBlock extends FlowGraphBinaryOperationBlock<FlowGraphInteger, FlowGraphInteger, FlowGraphInteger> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Bitwise OR operation
 */
export declare class FlowGraphBitwiseOrBlock extends FlowGraphBinaryOperationBlock<FlowGraphInteger, FlowGraphInteger, FlowGraphInteger> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Bitwise XOR operation
 */
export declare class FlowGraphBitwiseXorBlock extends FlowGraphBinaryOperationBlock<FlowGraphInteger, FlowGraphInteger, FlowGraphInteger> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Bitwise left shift operation
 */
export declare class FlowGraphBitwiseLeftShiftBlock extends FlowGraphBinaryOperationBlock<FlowGraphInteger, FlowGraphInteger, FlowGraphInteger> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Bitwise right shift operation
 */
export declare class FlowGraphBitwiseRightShiftBlock extends FlowGraphBinaryOperationBlock<FlowGraphInteger, FlowGraphInteger, FlowGraphInteger> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Count leading zeros operation
 */
export declare class FlowGraphCountLeadingZerosBlock extends FlowGraphUnaryOperationBlock<FlowGraphInteger, FlowGraphInteger> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Count trailing zeros operation
 */
export declare class FlowGraphCountTrailingZerosBlock extends FlowGraphUnaryOperationBlock<FlowGraphInteger, FlowGraphInteger> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
/**
 * @experimental
 * Count one bits operation
 */
export declare class FlowGraphCountOneBitsBlock extends FlowGraphUnaryOperationBlock<FlowGraphInteger, FlowGraphInteger> {
    constructor(config?: IFlowGraphBlockConfiguration);
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
