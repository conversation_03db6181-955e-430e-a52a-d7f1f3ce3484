{"version": 3, "file": "WebXRNearInteraction.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRNearInteraction.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAIjF,OAAO,EAAE,YAAY,EAAE,MAAM,qCAAqC,CAAC;AAOnE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAC1E,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;AACxC,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAC3D,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,oBAAoB,EAAE,MAAM,sCAAsC,CAAC;AAE5E,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAE9D,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AACpE,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,YAAY,EAAE,MAAM,mCAAmC,CAAC;AAEjE,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACxE,eAAe;AACf,OAAO,8BAA8B,CAAC;AA6BtC,oGAAoG;AACpG,IAAK,2BAaJ;AAbD,WAAK,2BAA2B;IAC5B;;OAEG;IACH,yFAAU,CAAA;IACV;;OAEG;IACH,+EAAK,CAAA;IACL;;OAEG;IACH,+EAAK,CAAA;AACT,CAAC,EAbI,2BAA2B,KAA3B,2BAA2B,QAa/B;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,uBAaX;AAbD,WAAY,uBAAuB;IAC/B;;OAEG;IACH,6EAAY,CAAA;IACZ;;OAEG;IACH,yGAA0B,CAAA;IAC1B;;OAEG;IACH,+FAAqB,CAAA;AACzB,CAAC,EAbW,uBAAuB,KAAvB,uBAAuB,QAalC;AAiDD;;GAEG;AACH,MAAM,OAAO,oBAAqB,SAAQ,oBAAoB;IAoG1D;;;;OAIG;IACH,YACI,iBAAsC,EACrB,QAAsC;QAEvD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFR,aAAQ,GAAR,QAAQ,CAA8B;QAxGnD,YAAO,GAAQ,IAAI,GAAG,CAAC,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,CAAC,CAAC;QAErD,sBAAiB,GAAG,CAAC,YAA8B,EAAE,EAAE;YAC3D,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;gBAC1C,mBAAmB;gBACnB,OAAO;aACV;YACD,qBAAqB;YACrB,MAAM,EAAE,kBAAkB,EAAE,0BAA0B,EAAE,4BAA4B,EAAE,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAC3H,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAEhD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG;gBACvC,YAAY;gBACZ,gBAAgB,EAAE,IAAI;gBACtB,yBAAyB,EAAE,IAAI;gBAC/B,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI;gBACf,kBAAkB;gBAClB,0BAA0B,EAAE,0BAA0B;gBACtD,4BAA4B,EAAE,4BAA4B;gBAC1D,qBAAqB,EAAE,2BAA2B,CAAC,UAAU;gBAC7D,OAAO,EAAE,IAAI,GAAG,CAAC,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,CAAC;gBAC9C,gBAAgB,EAAE,KAAK;gBACvB,eAAe,EAAE,KAAK;gBACtB,eAAe,EAAE,KAAK;gBACtB,aAAa,EAAE,KAAK;gBACpB,EAAE,EAAE,oBAAoB,CAAC,UAAU,EAAE;gBACrC,oBAAoB,EAAE,aAAa;aACtC,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,mBAAmB;gBACxD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,mBAAmB;oBAC5D,IAAI,CAAC,iBAAiB,CAAC,mCAAmC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;wBACtE,IAAI,MAAM,CAAC,cAAc,KAAK,MAAM,CAAC,mBAAmB,EAAE;4BACtD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;4BACtE,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;4BAExE,MAAM,EAAE,kBAAkB,EAAE,0BAA0B,EAAE,4BAA4B,EAAE,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;4BAC3H,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;4BACjF,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,0BAA0B,GAAG,0BAA0B,CAAC;4BACjG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,4BAA4B,GAAG,4BAA4B,CAAC;4BACrG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,oBAAoB,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;yBAC7F;oBACL,CAAC,CAAC,CAAC;YAEP,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IACI,CAAC,IAAI,CAAC,QAAQ,CAAC,qCAAqC;oBACpD,IAAI,CAAC,QAAQ,CAAC,mBAAmB;oBACjC,YAAY,CAAC,WAAW,CAAC,UAAU,KAAK,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAC3E;oBACE,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC,QAAQ,CAAC;iBACpD;aACJ;iBAAM;gBACH,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,qCAAqC,EAAE;oBACtD,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC,QAAQ,CAAC;iBACpD;aACJ;YACD,QAAQ,YAAY,CAAC,WAAW,CAAC,aAAa,EAAE;gBAC5C,KAAK,iBAAiB;oBAClB,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAC;gBACzD,KAAK,MAAM;oBACP,OAAO,IAAI,CAAC;gBAChB,KAAK,QAAQ;oBACT,OAAO,IAAI,CAAC;aACnB;QACL,CAAC,CAAC;QAEM,iBAAY,GAEhB,EAAE,CAAC;QAKC,2BAAsB,GAA8C,IAAI,CAAC;QAajF;;WAEG;QACI,8BAAyB,GAAW,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACrE;;WAEG;QACI,6BAAwB,GAAW,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QA8LnD,iBAAY,GAAG,GAAG,CAAC;QACnB,gBAAW,GAAG,IAAI,CAAC;QACnB,0BAAqB,GAAG,IAAI,CAAC,CAAC,8GAA8G;QAC5I,yBAAoB,GAAG,CAAC,CAAC;QArLtC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;QAC3C,IAAI,IAAI,CAAC,QAAQ,CAAC,6BAA6B,KAAK,SAAS,EAAE;YAC3D,IAAI,CAAC,QAAQ,CAAC,6BAA6B,GAAG,uBAAuB,CAAC,iBAAiB,CAAC;SAC3F;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE;YACrC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC;SACrE;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,2BAA2B,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,6BAA6B,EAAE,CAAC,UAAU,EAAE,EAAE;YAC3F,wBAAwB;YACxB,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,gCAAgC,GAAG,IAAI,CAAC;QACpD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YACpD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,YAAoB;QAC3C,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE;YACjC,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,gBAAgB,CAAC;SAC3D;aAAM;YACH,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAED;;;;;OAKG;IACI,0BAA0B,CAAC,EAAU;QACxC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAClC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,IAAI,IAAI,CAAC;aAC1D;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,wBAAwB,CAAC,qBAAgE;QAC5F,IAAI,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;IACxD,CAAC;IAED;;;;OAIG;IACK,kBAAkB,CAAC,IAAkB;QACzC,OAAO,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC;IACxF,CAAC;IAED;;;;OAIG;IACK,kBAAkB,CAAC,IAAkB;QACzC,OAAO,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC;IACzF,CAAC;IAED;;;;OAIG;IACK,yBAAyB,CAAC,IAAkB;QAChD,OAAO,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC;IAClH,CAAC;IAEO,6BAA6B,CAAC,IAAkB,EAAE,YAAoB;QAC1E,IAAI,MAAM,GAAkB,IAAI,CAAC;QAEjC,OAAO,MAAM,EAAE;YACX,IAAI,MAAM,CAAC,iBAAiB,IAAI,MAAM,CAAC,iBAAiB,CAAC,eAAe,IAAI,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC,oBAAoB,KAAK,YAAY,EAAE;gBACxJ,OAAO,KAAK,CAAC;aAChB;YACD,MAAM,GAAG,MAAM,CAAC,MAAuB,CAAC;SAC3C;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,0BAA0B,CAAC,cAA8B,EAAE,QAAqC;QACpG,IACI,cAAc,CAAC,qBAAqB,KAAK,QAAQ;YACjD,IAAI,CAAC,QAAQ,CAAC,6BAA6B,KAAK,uBAAuB,CAAC,iBAAiB;YACzF,CAAC,CAAC,cAAc,CAAC,YAAY,EAAE,WAAW,CAAC,IAAI,EACjD;YACE,OAAO;SACV;QAED,2FAA2F;QAC3F,IAAI,QAAQ,GAAG,cAAc,CAAC,qBAAqB,EAAE;YACjD,QAAQ,cAAc,CAAC,qBAAqB,EAAE;gBAC1C,KAAK,2BAA2B,CAAC,UAAU,CAAC,CAAC;oBACzC,cAAc,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;oBAClD,IAAI,QAAQ,KAAK,2BAA2B,CAAC,KAAK,EAAE;wBAChD,MAAM;qBACT;iBACJ;gBACD,0CAA0C;gBAC1C,KAAK,2BAA2B,CAAC,KAAK,CAAC,CAAC;oBACpC,cAAc,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;oBAChD,IAAI,QAAQ,KAAK,2BAA2B,CAAC,KAAK,EAAE;wBAChD,MAAM;qBACT;iBACJ;aACJ;SACJ;aAAM;YACH,QAAQ,cAAc,CAAC,qBAAqB,EAAE;gBAC1C,KAAK,2BAA2B,CAAC,KAAK,CAAC,CAAC;oBACpC,cAAc,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;oBACjD,IAAI,QAAQ,KAAK,2BAA2B,CAAC,KAAK,EAAE;wBAChD,MAAM;qBACT;iBACJ;gBACD,0CAA0C;gBAC1C,KAAK,2BAA2B,CAAC,KAAK,CAAC,CAAC;oBACpC,cAAc,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAC;oBACnD,IAAI,QAAQ,KAAK,2BAA2B,CAAC,UAAU,EAAE;wBACrD,MAAM;qBACT;iBACJ;aACJ;SACJ;QAED,cAAc,CAAC,qBAAqB,GAAG,QAAQ,CAAC;IACpD,CAAC;IAOO,kBAAkB,CAAC,EAAU,EAAE,QAAiB,EAAE,WAAuB;QAC7E,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAE7C,kJAAkJ;QAClJ,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACjD,WAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjE,IAAI,IAAI,CAAC,QAAQ,CAAC,6BAA6B,KAAK,uBAAuB,CAAC,iBAAiB,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,WAAW,CAAC,IAAI,EAAE;YAC7I,kEAAkE;YAClE,cAAc,CAAC,YAAa,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACnE,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;SAChF;QAED,cAAc,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;QAC1H,cAAc,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;IAC/I,CAAC;IAES,UAAU,CAAC,QAAiB;QAClC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YAC1C,wCAAwC;YACxC,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAC7C,MAAM,QAAQ,GAAG,cAAc,CAAC,YAAY,EAAE,WAAW,CAAC,IAAI,CAAC;YAC/D,iFAAiF;YACjF,IACI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,qCAAqC,IAAI,EAAE,KAAK,IAAI,CAAC,mBAAmB,CAAC;gBACzF,CAAC,cAAc,CAAC,YAAY;gBAC5B,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,6BAA6B,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,EACnH;gBACE,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC;gBAC3B,OAAO;aACV;YACD,cAAc,CAAC,gBAAgB,GAAG,KAAK,CAAC;YACxC,cAAc,CAAC,eAAe,GAAG,KAAK,CAAC;YAEvC,qCAAqC;YACrC,IAAI,cAAc,CAAC,YAAY,EAAE;gBAC7B,IAAI,QAAQ,EAAE;oBACV,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;oBACpD,IAAI,UAAU,EAAE;wBACZ,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAa,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;wBAC/F,IAAI,YAAY,IAAI,YAAY,CAAC,SAAS,EAAE;4BACxC,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACpE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC;4BACvJ,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CACxB,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,EACpC,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,EACpC,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,GAAG,iBAAiB,EACxD,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,GAAG,iBAAiB,CAC3D,CAAC;4BAEF,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;yBAChF;qBACJ;iBACJ;qBAAM,IAAI,cAAc,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,6BAA6B,KAAK,uBAAuB,CAAC,QAAQ,EAAE;oBAC5I,IAAI,cAAc,GAAG,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC;oBACzD,IAAI,cAAc,CAAC,YAAY,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,6BAA6B,KAAK,uBAAuB,CAAC,sBAAsB,EAAE;wBACpI,cAAc,GAAG,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC;qBACrD;oBAED,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,cAAc,CAAC,QAAQ,EAAE,cAAc,CAAC,kBAAmB,CAAC,CAAC;iBAC5F;aACJ;iBAAM;gBACH,OAAO;aACV;YAED,MAAM,gBAAgB,GAAG,CAAC,iBAAwC,EAAE,gBAAuC,EAAyB,EAAE;gBAClI,IAAI,IAAI,GAAG,IAAI,CAAC;gBAChB,IAAI,CAAC,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;oBAC5C,0BAA0B;oBAC1B,IAAI,GAAG,iBAAiB,CAAC;iBAC5B;qBAAM,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE;oBACrD,2BAA2B;oBAC3B,IAAI,GAAG,gBAAgB,CAAC;iBAC3B;qBAAM,IAAI,gBAAgB,CAAC,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,EAAE;oBAC/D,iCAAiC;oBACjC,IAAI,GAAG,gBAAgB,CAAC;iBAC3B;qBAAM;oBACH,kCAAkC;oBAClC,IAAI,GAAG,iBAAiB,CAAC;iBAC5B;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC,CAAC;YACF,MAAM,2BAA2B,GAAG,CAAC,mBAA0C,EAAe,EAAE;gBAC5F,IAAI,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;gBAE/B,IAAI,uBAAuB,GAAG,KAAK,CAAC;gBACpC,MAAM,eAAe,GAAG,mBAAmB,IAAI,mBAAmB,CAAC,WAAW,IAAI,mBAAmB,CAAC,GAAG,CAAC;gBAC1G,IAAI,mBAAmB,EAAE,WAAW,EAAE;oBAClC,uBAAuB,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,IAAI,mBAAmB,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,IAAI,mBAAmB,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC;iBAC3J;gBACD,IAAI,eAAe,IAAI,CAAC,uBAAuB,EAAE;oBAC7C,MAAM,GAAG,mBAAoB,CAAC;iBACjC;gBACD,OAAO,MAAM,CAAC;YAClB,CAAC,CAAC;YAEF,+HAA+H;YAC/H,sGAAsG;YACtG,wGAAwG;YACxG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE;gBACjC,IAAI,IAAI,GAAG,IAAI,CAAC;gBAEhB,yBAAyB;gBACzB,IAAI,qBAAqB,GAAG,IAAI,CAAC;gBACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBAC1D,qBAAqB,GAAG,IAAI,CAAC,eAAe,CACxC,cAAc,EACd,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAC7D,IAAI,CAAC,kBAAkB,EACvB,CAAC,IAAkB,EAAE,EAAE,CAAC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAC/D,CAAC;iBACL;gBACD,MAAM,sBAAsB,GAAG,IAAI,CAAC,eAAe,CAC/C,cAAc,EACd,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAC7D,IAAI,CAAC,MAAM,EACX,CAAC,IAAkB,EAAE,EAAE,CAAC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAC/D,CAAC;gBAEF,MAAM,aAAa,GAAG,gBAAgB,CAAC,sBAAsB,EAAE,qBAAqB,CAAC,CAAC;gBACtF,IAAI,aAAa,IAAI,aAAa,CAAC,GAAG,EAAE;oBACpC,IAAI,GAAG,2BAA2B,CAAC,aAAa,CAAC,CAAC;oBAClD,IAAI,IAAI,CAAC,GAAG,EAAE;wBACV,cAAc,CAAC,gBAAgB,GAAG,IAAI,CAAC;qBAC1C;iBACJ;gBAED,wBAAwB;gBACxB,IAAI,cAAc,CAAC,gBAAgB,EAAE;oBACjC,IAAI,oBAAoB,GAAG,IAAI,CAAC;oBAChC,MAAM,MAAM,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;oBACtH,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,IAAI,CAAC,kBAAkB,EAAE;wBAC1D,oBAAoB,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,MAAM,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAkB,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;qBACvJ;oBACD,MAAM,qBAAqB,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,IAAkB,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC/I,MAAM,QAAQ,GAAG,gBAAgB,CAAC,qBAAqB,EAAE,oBAAoB,CAAC,CAAC;oBAC/E,MAAM,QAAQ,GAAG,2BAA2B,CAAC,QAAQ,CAAC,CAAC;oBACvD,IAAI,QAAQ,CAAC,GAAG,EAAE;wBACd,oDAAoD;wBACpD,IAAI,GAAG,QAAQ,CAAC;wBAChB,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC;qBACzC;iBACJ;gBAED,cAAc,CAAC,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC;gBAC/C,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC;gBAE3B,4BAA4B;gBAC5B,IAAI,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,WAAW,IAAI,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE;oBACnF,cAAc,CAAC,gBAAgB,GAAG,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC;oBACjE,cAAc,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACvF,cAAc,CAAC,oBAAoB,CAAC,SAAS,GAAG,IAAI,CAAC;oBAErD,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE;wBACrE,IAAI,CAAC,sBAAsB,CAAC,uCAAuC,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;qBAChG;iBACJ;qBAAM;oBACH,cAAc,CAAC,gBAAgB,GAAG,IAAI,CAAC;oBACvC,cAAc,CAAC,oBAAoB,CAAC,SAAS,GAAG,KAAK,CAAC;oBAEtD,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE;wBACrE,IAAI,CAAC,sBAAsB,CAAC,uCAAuC,CAAC,cAAc,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;qBACjG;iBACJ;aACJ;YAED,qFAAqF;YACrF,IAAI,KAAK,GAAG,2BAA2B,CAAC,UAAU,CAAC;YACnD,IAAI,cAAc,CAAC,eAAe,IAAI,cAAc,CAAC,eAAe,EAAE;gBAClE,KAAK,GAAG,2BAA2B,CAAC,KAAK,CAAC;aAC7C;iBAAM,IAAI,cAAc,CAAC,gBAAgB,EAAE;gBACxC,KAAK,GAAG,2BAA2B,CAAC,KAAK,CAAC;aAC7C;YACD,IAAI,CAAC,0BAA0B,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACP,CAAC;IAED,IAAY,kBAAkB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,oBAAoB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;IAC/G,CAAC;IAEO,kBAAkB;QACtB,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,oBAAoB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QAC1K,MAAM,aAAa,GAAG,YAAY,CAC9B,iBAAiB,EACjB;YACI,QAAQ,EAAE,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB;SACnE,EACD,eAAe,CAClB,CAAC;QACF,aAAa,CAAC,gCAAgC,EAAE,CAAC;QACjD,aAAa,CAAC,UAAU,GAAG,KAAK,CAAC;QACjC,aAAa,CAAC,SAAS,GAAG,KAAK,CAAC;QAChC,aAAa,CAAC,kBAAkB,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QACzD,MAAM,SAAS,GAAG,IAAI,gBAAgB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QACrE,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QACzC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,yBAAyB,CAAC;QACzD,SAAS,CAAC,eAAe,GAAG,KAAK,CAAC;QAClC,aAAa,CAAC,QAAQ,GAAG,SAAS,CAAC;QAEnC,OAAO,aAAa,CAAC;IACzB,CAAC;IAEO,oCAAoC,CAAC,EAAU;QACnD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO,IAAI,CAAC,sBAAsB,CAAC,uCAAuC,CAAC,EAAE,CAAC,CAAC;SAClF;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,0BAA0B,CAAC,YAA8B;QAC7D,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAChE,MAAM,gBAAgB,GAAqB;YACvC,SAAS,EAAE,cAAc,CAAC,EAAE;YAC5B,WAAW,EAAE,SAAS;SACzB,CAAC;QACF,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YACjF,IACI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,qCAAqC,IAAI,YAAY,CAAC,QAAQ,KAAK,IAAI,CAAC,mBAAmB,CAAC;gBAC5G,CAAC,cAAc,CAAC,YAAY;gBAC5B,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,6BAA6B,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,EACvJ;gBACE,OAAO;aACV;YACD,IAAI,cAAc,CAAC,IAAI,EAAE;gBACrB,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,cAAc,CAAC,OAAO,CAAC;aACpD;YAED,IAAI,cAAc,CAAC,IAAI,IAAI,IAAI,CAAC,oCAAoC,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE;gBACrF,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;aAC1E;YAED,0BAA0B;YAC1B,IAAI,cAAc,CAAC,eAAe,IAAI,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE;gBAClF,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE;oBAC3C,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;oBACvE,cAAc,CAAC,yBAAyB,GAAG,cAAc,CAAC,gBAAgB,CAAC;oBAC3E,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC;iBACvC;aACJ;iBAAM,IAAI,cAAc,CAAC,yBAAyB,IAAI,cAAc,CAAC,SAAS,EAAE;gBAC7E,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;gBAC1E,cAAc,CAAC,aAAa,GAAG,KAAK,CAAC;gBACrC,cAAc,CAAC,yBAAyB,GAAG,IAAI,CAAC;aACnD;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,CAAC,OAAgB,EAAE,EAAE;YACnC,IACI,IAAI,CAAC,QAAQ,CAAC,qCAAqC;gBACnD,CAAC,YAAY,CAAC,QAAQ,KAAK,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,oCAAoC,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,EACtH;gBACE,IAAI,cAAc,CAAC,IAAI,EAAE;oBACrB,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,cAAc,CAAC,OAAO,CAAC;iBACpD;gBACD,IAAI,OAAO,IAAI,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE;oBAC/H,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC;oBACtC,cAAc,CAAC,oBAAoB,CAAC,SAAS,GAAG,KAAK,CAAC;oBACtD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;oBACvE,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC;iBACvC;qBAAM,IAAI,CAAC,OAAO,IAAI,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,eAAe,EAAE;oBAC1E,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;oBACrE,cAAc,CAAC,aAAa,GAAG,KAAK,CAAC;oBACrC,cAAc,CAAC,eAAe,GAAG,KAAK,CAAC;oBACvC,cAAc,CAAC,oBAAoB,CAAC,SAAS,GAAG,IAAI,CAAC;iBACxD;aACJ;iBAAM;gBACH,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,qCAAqC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE;oBACxG,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC,QAAQ,CAAC;iBACpD;aACJ;QACL,CAAC,CAAC;QAEF,IAAI,YAAY,CAAC,WAAW,CAAC,OAAO,EAAE;YAClC,MAAM,IAAI,GAAG,CAAC,gBAA+C,EAAE,EAAE;gBAC7D,cAAc,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;gBACzE,IAAI,cAAc,CAAC,gBAAgB,EAAE;oBACjC,cAAc,CAAC,8BAA8B,GAAG,cAAc,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;wBAC7H,IAAI,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE;4BAC3B,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;4BAClD,SAAS,CAAC,OAAO,CAAC,CAAC;yBACtB;oBACL,CAAC,CAAC,CAAC;iBACN;qBAAM;oBACH,cAAc,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;oBACxE,cAAc,CAAC,uBAAuB,GAAG,cAAc,CAAC,kBAAkB,CAAC,8BAA8B,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;wBACxH,IAAI,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE;4BAC3B,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;4BAClD,SAAS,CAAC,OAAO,CAAC,CAAC;yBACtB;oBACL,CAAC,CAAC,CAAC;iBACN;YACL,CAAC,CAAC;YACF,IAAI,YAAY,CAAC,gBAAgB,EAAE;gBAC/B,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;aACvC;iBAAM;gBACH,YAAY,CAAC,gCAAgC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aAC3D;SACJ;aAAM;YACH,oCAAoC;YACpC,MAAM,mBAAmB,GAAG,CAAC,KAAyB,EAAE,EAAE;gBACtD,IACI,cAAc,CAAC,YAAY;oBAC3B,KAAK,CAAC,WAAW,KAAK,cAAc,CAAC,YAAY,CAAC,WAAW;oBAC7D,cAAc,CAAC,IAAI;oBACnB,IAAI,CAAC,oCAAoC,CAAC,cAAc,CAAC,EAAE,CAAC;oBAC5D,cAAc,CAAC,gBAAgB;oBAC/B,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAC1D;oBACE,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC;oBACtC,cAAc,CAAC,oBAAoB,CAAC,SAAS,GAAG,KAAK,CAAC;oBACtD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;oBACvE,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC;iBACvC;YACL,CAAC,CAAC;YAEF,MAAM,iBAAiB,GAAG,CAAC,KAAyB,EAAE,EAAE;gBACpD,IACI,cAAc,CAAC,YAAY;oBAC3B,KAAK,CAAC,WAAW,KAAK,cAAc,CAAC,YAAY,CAAC,WAAW;oBAC7D,cAAc,CAAC,IAAI;oBACnB,IAAI,CAAC,oCAAoC,CAAC,cAAc,CAAC,EAAE,CAAC,EAC9D;oBACE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;oBACrE,cAAc,CAAC,eAAe,GAAG,KAAK,CAAC;oBACvC,cAAc,CAAC,oBAAoB,CAAC,SAAS,GAAG,IAAI,CAAC;oBACrD,cAAc,CAAC,aAAa,GAAG,KAAK,CAAC;iBACxC;YACL,CAAC,CAAC;YAEF,cAAc,CAAC,cAAc,GAAG;gBAC5B,SAAS,EAAE,iBAAiB;gBAC5B,WAAW,EAAE,mBAAmB;aACnC,CAAC;YAEF,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,gBAAgB,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;YACpF,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;SACnF;IACL,CAAC;IAEO,iBAAiB,CAAC,oBAA4B;QAClD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QAC/D,IAAI,CAAC,cAAc,EAAE;YACjB,OAAO;SACV;QACD,IAAI,cAAc,CAAC,gBAAgB,EAAE;YACjC,IAAI,cAAc,CAAC,8BAA8B,EAAE;gBAC/C,cAAc,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,MAAM,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAC;aACxH;SACJ;QACD,IAAI,cAAc,CAAC,kBAAkB,EAAE;YACnC,IAAI,cAAc,CAAC,uBAAuB,EAAE;gBACxC,cAAc,CAAC,kBAAkB,CAAC,8BAA8B,CAAC,MAAM,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;aACnH;SACJ;QACD,IAAI,cAAc,CAAC,eAAe,EAAE;YAChC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;SACrF;QACD,IAAI,cAAc,CAAC,cAAc,EAAE;YAC/B,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,SAAiB,EAAE,EAAE;gBACrE,MAAM,IAAI,GAAG,cAAc,CAAC,cAAc,IAAI,cAAc,CAAC,cAAc,CAAC,SAAwB,CAAC,CAAC;gBACtG,IAAI,IAAI,EAAE;oBACN,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,mBAAmB,CAAC,SAAwB,EAAE,IAAW,CAAC,CAAC;iBAC7F;YACL,CAAC,CAAC,CAAC;SACN;QACD,cAAc,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QAC5C,cAAc,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;QAE9C,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,GAAG,EAAE;YACrC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;gBAC/B,OAAO;aACV;YACD,sFAAsF;YACtF,MAAM,gBAAgB,GAAqB;gBACvC,SAAS,EAAE,cAAc,CAAC,EAAE;gBAC5B,WAAW,EAAE,SAAS;aACzB,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,WAAW,EAAE,EAAE,gBAAgB,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,8BAA8B;QAC9B,IAAI,cAAc,CAAC,mBAAmB,EAAE;YACpC,IAAI,CAAC,iBAAiB,CAAC,mCAAmC,CAAC,MAAM,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;SACzG;QAED,sBAAsB;QACtB,OAAO,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QAC/C,IAAI,IAAI,CAAC,mBAAmB,KAAK,oBAAoB,EAAE;YACnD,8BAA8B;YAC9B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5C,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;aACtC;iBAAM;gBACH,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;aACjC;SACJ;IACL,CAAC;IAEO,0BAA0B;QAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;QAC7D,sDAAsD;QACtD,MAAM,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,oBAAoB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QAE5K,MAAM,kBAAkB,GAAG,YAAY,CAAC,YAAY,EAAE,EAAE,QAAQ,EAAE,CAAC,GAAG,UAAU,EAAE,EAAE,iBAAiB,CAAC,CAAC;QACvG,kBAAkB,CAAC,SAAS,GAAG,KAAK,CAAC;QAErC,mDAAmD;QACnD,IAAI,IAAI,CAAC,QAAQ,CAAC,2BAA2B,EAAE;YAC3C,kBAAkB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,2BAA2B,CAAC;SAC3E;aAAM;YACH,YAAY,CAAC,qBAAqB,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,EAAE;gBACpF,kBAAkB,CAAC,QAAQ,GAAG,YAAY,CAAC;YAC/C,CAAC,CAAC,CAAC;SACN;QAED,MAAM,cAAc,GAAG,IAAI,aAAa,EAAE,CAAC;QAC3C,cAAc,CAAC,aAAa,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;QAElE,2EAA2E;QAC3E,gIAAgI;QAChI,4GAA4G;QAC5G,MAAM,YAAY,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAC9I,MAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACvD,MAAM,YAAY,GAAG,IAAI,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAC3F,MAAM,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACnE,MAAM,wBAAwB,GAAG,IAAI,OAAO,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,qBAAqB,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAC3I,MAAM,wBAAwB,GAAG,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACtE,MAAM,2BAA2B,GAAG,IAAI,OAAO,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,wBAAwB,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QACvJ,MAAM,wBAAwB,GAAG,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACtE,MAAM,2BAA2B,GAAG,IAAI,OAAO,CAAC,wBAAwB,EAAE,wBAAwB,EAAE,wBAAwB,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAEvJ,MAAM,SAAS,GAAG;YACd,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE;YACjC,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE;YACjD,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;SACrC,CAAC;QACF,MAAM,WAAW,GAAG;YAChB,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE;YACjC,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE;YACjD,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;SACrC,CAAC;QACF,MAAM,WAAW,GAAG;YAChB,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,YAAY,EAAE;YACzC,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,wBAAwB,EAAE;YAC9C,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;SACrC,CAAC;QACF,MAAM,aAAa,GAAG;YAClB,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE;YACjC,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,YAAY,EAAE;YAC1C,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,YAAY,EAAE;SAC7C,CAAC;QAEF,MAAM,WAAW,GAAG,IAAI,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,CAAC,qBAAqB,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACjI,MAAM,aAAa,GAAG,IAAI,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,CAAC,qBAAqB,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACrI,MAAM,aAAa,GAAG,IAAI,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,CAAC,qBAAqB,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACrI,MAAM,eAAe,GAAG,IAAI,SAAS,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,CAAC,qBAAqB,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAEzI,WAAW,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAC9C,aAAa,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAChD,aAAa,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAChD,eAAe,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAElD,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC/B,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACnC,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACnC,eAAe,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAEvC,MAAM,0BAA0B,GAAG,CAAC,OAAgB,EAAE,EAAE;YACpD,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC;YACrD,iBAAiB,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1F,CAAC,CAAC;QAEF,MAAM,4BAA4B,GAAG,CAAC,WAAoB,EAAE,EAAE;YAC1D,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC;YAC7D,IAAI,WAAW,EAAE;gBACb,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;aACvC;YACD,iBAAiB,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE;gBACvF,IAAI,CAAC,WAAW,EAAE;oBACd,kBAAkB,CAAC,SAAS,GAAG,KAAK,CAAC;iBACxC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QAEF,OAAO,EAAE,kBAAkB,EAAE,0BAA0B,EAAE,4BAA4B,EAAE,CAAC;IAC5F,CAAC;IAEO,eAAe,CAAC,cAA8B,EAAE,MAAc,EAAE,UAAiB,EAAE,SAA0C;QACjI,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;QACtC,WAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ,CAAC;QAEjC,IAAI,cAAc,CAAC,kBAAkB,IAAI,cAAc,CAAC,YAAY,EAAE;YAClE,MAAM,QAAQ,GAAG,cAAc,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC5D,MAAM,MAAM,GAAG,cAAc,CAAC,yBAAyB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE1E,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;gBACvE,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC1C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,IAAI,EAAE,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;oBACrG,SAAS;iBACZ;gBACD,MAAM,MAAM,GAAG,oBAAoB,CAAC,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAErE,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,EAAE;oBAChE,WAAW,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;oBAC7B,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC;oBAC9B,WAAW,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;oBAC7C,WAAW,CAAC,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC;oBAC/D,WAAW,CAAC,aAAa,GAAG,cAAc,CAAC,YAAY,CAAC,IAAI,IAAI,IAAI,CAAC;oBACrE,WAAW,CAAC,UAAU,GAAG,cAAc,CAAC,kBAAkB,CAAC;oBAC3D,WAAW,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;oBACvC,WAAW,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;oBAC3B,WAAW,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;oBAC3B,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;oBACnC,WAAW,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;iBAC5C;aACJ;SACJ;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,kBAAkB,CAAC,IAAkB,EAAE,MAAsB,EAAE,gBAAgB,GAAG,KAAK;QACjG,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,MAAM,EAAE,GAAG,IAAI,WAAW,EAAE,CAAC;QAC7B,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAE5C,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE;YAC9B,OAAO,EAAE,CAAC;SACb;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,YAAY,EAAE;YAClC,OAAO,EAAE,CAAC;SACb;QAED,IAAI,CAAC,gBAAgB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE;YACtF,OAAO,EAAE,CAAC;SACb;QAED,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;QAE1D,IAAI,QAAQ,GAAG,CAAC,QAAQ,CAAC;QACzB,IAAI,GAAG,EAAE,yBAAyB,EAAE,0BAA0B,EAAE,gBAAgB,CAAC;QACjF,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACzC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QAC5C,WAAW,CAAC,MAAM,EAAE,CAAC;QACrB,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;QAEtE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACnD,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;YAEjC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAa,IAAI,CAAC,UAAU,EAAgB,IAAI,CAAC,UAAU,EAAE,EAAE,MAAM,CAAC,CAAC;YAElG,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,CAAC;YACzE,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAE9C,kCAAkC;YAClC,0BAA0B,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;YAClF,yBAAyB,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;YACxF,IAAI,yBAAyB,KAAK,CAAC,CAAC,IAAI,0BAA0B,KAAK,CAAC,CAAC,IAAI,0BAA0B,GAAG,yBAAyB,EAAE;gBACjI,GAAG,GAAG,CAAC,CAAC;gBACR,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aAClC;YAED,IAAI,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,GAAG,QAAQ,EAAE;gBAC9B,QAAQ,GAAG,GAAG,CAAC;gBAEf,0DAA0D;gBAC1D,GAAG,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;gBACrD,MAAM,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,CAAC;gBAC7B,gBAAgB,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBAE/C,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;aAC3B;SACJ;QAED,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE;YAC1B,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC;YACd,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACvB,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC;YACrB,EAAE,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;YAChC,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,EAAE,KAAK,IAAI,IAAI,gBAAgB,CAAC,EAAE,KAAK,IAAI,EAAE;gBAClF,EAAE,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;gBACpC,EAAE,CAAC,SAAS,GAAG,gBAAgB,CAAC,SAAS,CAAC;gBAC1C,EAAE,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,CAAC;gBAC5B,EAAE,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,CAAC;aAC/B;SACJ;QAED,OAAO,EAAE,CAAC;IACd,CAAC;;AA53Bc,+BAAU,GAAG,GAAG,AAAN,CAAO;AA+EhC;;GAEG;AACoB,yBAAI,GAAG,gBAAgB,CAAC,gBAAgB,AAApC,CAAqC;AAChE;;;;GAIG;AACoB,4BAAO,GAAG,CAAC,AAAJ,CAAK;AAuyBvC,qBAAqB;AACrB,oBAAoB,CAAC,eAAe,CAChC,oBAAoB,CAAC,IAAI,EACzB,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,oBAAoB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AACrE,CAAC,EACD,oBAAoB,CAAC,OAAO,EAC5B,IAAI,CACP,CAAC", "sourcesContent": ["import { WebXRFeaturesManager, WebXRFeatureName } from \"../webXRFeaturesManager\";\r\nimport type { WebXRControllerPointerSelection } from \"./WebXRControllerPointerSelection\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { CreateSphere } from \"../../Meshes/Builders/sphereBuilder\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { WebXRInput } from \"../webXRInput\";\r\nimport type { WebXRInputSource } from \"../webXRInputSource\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { WebXRControllerComponent } from \"../motionController/webXRControllerComponent\";\r\nimport type { IndicesArray, Nullable } from \"../../types\";\r\nimport { Vector3, Quaternion, TmpVectors } from \"../../Maths/math.vector\";\r\nimport { Ray } from \"../../Culling/ray\";\r\nimport { PickingInfo } from \"../../Collisions/pickingInfo\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport { UtilityLayerRenderer } from \"../../Rendering/utilityLayerRenderer\";\r\nimport type { WebXRAbstractMotionController } from \"../motionController/webXRAbstractMotionController\";\r\nimport { BoundingSphere } from \"../../Culling/boundingSphere\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { StandardMaterial } from \"../../Materials/standardMaterial\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport { NodeMaterial } from \"../../Materials/Node/nodeMaterial\";\r\nimport type { Material } from \"../../Materials/material\";\r\nimport { Animation } from \"../../Animations/animation\";\r\nimport { QuadraticEase, EasingFunction } from \"../../Animations/easing\";\r\n// side effects\r\nimport \"../../Meshes/subMesh.project\";\r\n\r\ntype ControllerData = {\r\n    xrController?: WebXRInputSource;\r\n    squeezeComponent?: WebXRControllerComponent;\r\n    selectionComponent?: WebXRControllerComponent;\r\n    onButtonChangedObserver?: Nullable<Observer<WebXRControllerComponent>>;\r\n    onSqueezeButtonChangedObserver?: Nullable<Observer<WebXRControllerComponent>>;\r\n    onFrameObserver?: Nullable<Observer<XRFrame>>;\r\n    meshUnderPointer: Nullable<AbstractMesh>;\r\n    nearInteractionTargetMesh: Nullable<AbstractMesh>;\r\n    pick: Nullable<PickingInfo>;\r\n    stalePick: Nullable<PickingInfo>;\r\n    id: number;\r\n    touchCollisionMesh: AbstractMesh;\r\n    touchCollisionMeshFunction: (isTouch: boolean) => void;\r\n    hydrateCollisionMeshFunction: (isHydration: boolean) => void;\r\n    currentAnimationState: ControllerOrbAnimationState;\r\n    grabRay: Ray;\r\n    nearInteraction: boolean;\r\n    hoverInteraction: boolean;\r\n    grabInteraction: boolean;\r\n    downTriggered: boolean;\r\n    // event support\r\n    eventListeners?: { [event in XREventType]?: (event: XRInputSourceEvent) => void };\r\n    pickedPointVisualCue: AbstractMesh;\r\n    _worldScaleObserver?: Nullable<Observer<{ previousScaleFactor: number; newScaleFactor: number }>>;\r\n};\r\n\r\n// Tracks the interaction animation state when using a motion controller with a near interaction orb\r\nenum ControllerOrbAnimationState {\r\n    /**\r\n     * Orb is invisible\r\n     */\r\n    DEHYDRATED,\r\n    /**\r\n     * Orb is visible and inside the hover range\r\n     */\r\n    HOVER,\r\n    /**\r\n     * Orb is visible and touching a near interaction target\r\n     */\r\n    TOUCH,\r\n}\r\n\r\n/**\r\n * Where should the near interaction mesh be attached to when using a motion controller for near interaction\r\n */\r\nexport enum WebXRNearControllerMode {\r\n    /**\r\n     * Motion controllers will not support near interaction\r\n     */\r\n    DISABLED = 0,\r\n    /**\r\n     * The interaction point for motion controllers will be inside of them\r\n     */\r\n    CENTERED_ON_CONTROLLER = 1,\r\n    /**\r\n     * The interaction point for motion controllers will be in front of the controller\r\n     */\r\n    CENTERED_IN_FRONT = 2,\r\n}\r\n\r\n/**\r\n * Options interface for the near interaction module\r\n */\r\nexport interface IWebXRNearInteractionOptions {\r\n    /**\r\n     * If provided, this scene will be used to render meshes.\r\n     */\r\n    customUtilityLayerScene?: Scene;\r\n    /**\r\n     * Should meshes created here be added to a utility layer or the main scene\r\n     */\r\n    useUtilityLayer?: boolean;\r\n    /**\r\n     * The xr input to use with this near interaction\r\n     */\r\n    xrInput: WebXRInput;\r\n    /**\r\n     * Enable near interaction on all controllers instead of switching between them\r\n     */\r\n    enableNearInteractionOnAllControllers?: boolean;\r\n    /**\r\n     * The preferred hand to give the near interaction to. This will be prioritized when the controller initialize.\r\n     * If switch is enabled, it will still allow the user to switch between the different controllers\r\n     */\r\n    preferredHandedness?: XRHandedness;\r\n    /**\r\n     * Disable switching the near interaction from one controller to the other.\r\n     * If the preferred hand is set it will be fixed on this hand, and if not it will be fixed on the first controller added to the scene\r\n     */\r\n    disableSwitchOnClick?: boolean;\r\n\r\n    /**\r\n     * Far interaction feature to toggle when near interaction takes precedence\r\n     */\r\n    farInteractionFeature?: WebXRControllerPointerSelection;\r\n\r\n    /**\r\n     * Near interaction mode for motion controllers\r\n     */\r\n    nearInteractionControllerMode?: WebXRNearControllerMode;\r\n\r\n    /**\r\n     * Optional material for the motion controller orb, if enabled\r\n     */\r\n    motionControllerOrbMaterial?: Material;\r\n}\r\n\r\n/**\r\n * A module that will enable near interaction near interaction for hands and motion controllers of XR Input Sources\r\n */\r\nexport class WebXRNearInteraction extends WebXRAbstractFeature {\r\n    private static _IdCounter = 200;\r\n\r\n    private _tmpRay: Ray = new Ray(new Vector3(), new Vector3());\r\n\r\n    private _attachController = (xrController: WebXRInputSource) => {\r\n        if (this._controllers[xrController.uniqueId]) {\r\n            // already attached\r\n            return;\r\n        }\r\n        // get two new meshes\r\n        const { touchCollisionMesh, touchCollisionMeshFunction, hydrateCollisionMeshFunction } = this._generateNewTouchPointMesh();\r\n        const selectionMesh = this._generateVisualCue();\r\n\r\n        this._controllers[xrController.uniqueId] = {\r\n            xrController,\r\n            meshUnderPointer: null,\r\n            nearInteractionTargetMesh: null,\r\n            pick: null,\r\n            stalePick: null,\r\n            touchCollisionMesh,\r\n            touchCollisionMeshFunction: touchCollisionMeshFunction,\r\n            hydrateCollisionMeshFunction: hydrateCollisionMeshFunction,\r\n            currentAnimationState: ControllerOrbAnimationState.DEHYDRATED,\r\n            grabRay: new Ray(new Vector3(), new Vector3()),\r\n            hoverInteraction: false,\r\n            nearInteraction: false,\r\n            grabInteraction: false,\r\n            downTriggered: false,\r\n            id: WebXRNearInteraction._IdCounter++,\r\n            pickedPointVisualCue: selectionMesh,\r\n        };\r\n\r\n        this._controllers[xrController.uniqueId]._worldScaleObserver =\r\n            this._controllers[xrController.uniqueId]._worldScaleObserver ||\r\n            this._xrSessionManager.onWorldScaleFactorChangedObservable.add((values) => {\r\n                if (values.newScaleFactor !== values.previousScaleFactor) {\r\n                    this._controllers[xrController.uniqueId].touchCollisionMesh.dispose();\r\n                    this._controllers[xrController.uniqueId].pickedPointVisualCue.dispose();\r\n\r\n                    const { touchCollisionMesh, touchCollisionMeshFunction, hydrateCollisionMeshFunction } = this._generateNewTouchPointMesh();\r\n                    this._controllers[xrController.uniqueId].touchCollisionMesh = touchCollisionMesh;\r\n                    this._controllers[xrController.uniqueId].touchCollisionMeshFunction = touchCollisionMeshFunction;\r\n                    this._controllers[xrController.uniqueId].hydrateCollisionMeshFunction = hydrateCollisionMeshFunction;\r\n                    this._controllers[xrController.uniqueId].pickedPointVisualCue = this._generateVisualCue();\r\n                }\r\n            });\r\n\r\n        if (this._attachedController) {\r\n            if (\r\n                !this._options.enableNearInteractionOnAllControllers &&\r\n                this._options.preferredHandedness &&\r\n                xrController.inputSource.handedness === this._options.preferredHandedness\r\n            ) {\r\n                this._attachedController = xrController.uniqueId;\r\n            }\r\n        } else {\r\n            if (!this._options.enableNearInteractionOnAllControllers) {\r\n                this._attachedController = xrController.uniqueId;\r\n            }\r\n        }\r\n        switch (xrController.inputSource.targetRayMode) {\r\n            case \"tracked-pointer\":\r\n                return this._attachNearInteractionMode(xrController);\r\n            case \"gaze\":\r\n                return null;\r\n            case \"screen\":\r\n                return null;\r\n        }\r\n    };\r\n\r\n    private _controllers: {\r\n        [controllerUniqueId: string]: ControllerData;\r\n    } = {};\r\n    private _scene: Scene;\r\n\r\n    private _attachedController: string;\r\n\r\n    private _farInteractionFeature: Nullable<WebXRControllerPointerSelection> = null;\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.NEAR_INTERACTION;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * default color of the selection ring\r\n     */\r\n    public selectionMeshDefaultColor: Color3 = new Color3(0.8, 0.8, 0.8);\r\n    /**\r\n     * This color will be applied to the selection ring when selection is triggered\r\n     */\r\n    public selectionMeshPickedColor: Color3 = new Color3(0.3, 0.3, 1.0);\r\n\r\n    /**\r\n     * constructs a new background remover module\r\n     * @param _xrSessionManager the session manager for this module\r\n     * @param _options read-only options to be used in this module\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        private readonly _options: IWebXRNearInteractionOptions\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this._scene = this._xrSessionManager.scene;\r\n        if (this._options.nearInteractionControllerMode === undefined) {\r\n            this._options.nearInteractionControllerMode = WebXRNearControllerMode.CENTERED_IN_FRONT;\r\n        }\r\n\r\n        if (this._options.farInteractionFeature) {\r\n            this._farInteractionFeature = this._options.farInteractionFeature;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Attach this feature\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public attach(): boolean {\r\n        if (!super.attach()) {\r\n            return false;\r\n        }\r\n\r\n        this._options.xrInput.controllers.forEach(this._attachController);\r\n        this._addNewAttachObserver(this._options.xrInput.onControllerAddedObservable, this._attachController);\r\n        this._addNewAttachObserver(this._options.xrInput.onControllerRemovedObservable, (controller) => {\r\n            // REMOVE the controller\r\n            this._detachController(controller.uniqueId);\r\n        });\r\n\r\n        this._scene.constantlyUpdateMeshUnderPointer = true;\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Detach this feature.\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n\r\n        Object.keys(this._controllers).forEach((controllerId) => {\r\n            this._detachController(controllerId);\r\n        });\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Will get the mesh under a specific pointer.\r\n     * `scene.meshUnderPointer` will only return one mesh - either left or right.\r\n     * @param controllerId the controllerId to check\r\n     * @returns The mesh under pointer or null if no mesh is under the pointer\r\n     */\r\n    public getMeshUnderPointer(controllerId: string): Nullable<AbstractMesh> {\r\n        if (this._controllers[controllerId]) {\r\n            return this._controllers[controllerId].meshUnderPointer;\r\n        } else {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the xr controller that correlates to the pointer id in the pointer event\r\n     *\r\n     * @param id the pointer id to search for\r\n     * @returns the controller that correlates to this id or null if not found\r\n     */\r\n    public getXRControllerByPointerId(id: number): Nullable<WebXRInputSource> {\r\n        const keys = Object.keys(this._controllers);\r\n\r\n        for (let i = 0; i < keys.length; ++i) {\r\n            if (this._controllers[keys[i]].id === id) {\r\n                return this._controllers[keys[i]].xrController || null;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * This function sets webXRControllerPointerSelection feature that will be disabled when\r\n     * the hover range is reached for a mesh and will be reattached when not in hover range.\r\n     * This is used to remove the selection rays when moving.\r\n     * @param farInteractionFeature the feature to disable when finger is in hover range for a mesh\r\n     */\r\n    public setFarInteractionFeature(farInteractionFeature: Nullable<WebXRControllerPointerSelection>) {\r\n        this._farInteractionFeature = farInteractionFeature;\r\n    }\r\n\r\n    /**\r\n     * Filter used for near interaction pick and hover\r\n     * @param mesh the mesh candidate to be pick-filtered\r\n     * @returns if the mesh should be included in the list of candidate meshes for near interaction\r\n     */\r\n    private _nearPickPredicate(mesh: AbstractMesh): boolean {\r\n        return mesh.isEnabled() && mesh.isVisible && mesh.isPickable && mesh.isNearPickable;\r\n    }\r\n\r\n    /**\r\n     * Filter used for near interaction grab\r\n     * @param mesh the mesh candidate to be pick-filtered\r\n     * @returns if the mesh should be included in the list of candidate meshes for near interaction\r\n     */\r\n    private _nearGrabPredicate(mesh: AbstractMesh): boolean {\r\n        return mesh.isEnabled() && mesh.isVisible && mesh.isPickable && mesh.isNearGrabbable;\r\n    }\r\n\r\n    /**\r\n     * Filter used for any near interaction\r\n     * @param mesh the mesh candidate to be pick-filtered\r\n     * @returns if the mesh should be included in the list of candidate meshes for near interaction\r\n     */\r\n    private _nearInteractionPredicate(mesh: AbstractMesh): boolean {\r\n        return mesh.isEnabled() && mesh.isVisible && mesh.isPickable && (mesh.isNearPickable || mesh.isNearGrabbable);\r\n    }\r\n\r\n    private _controllerAvailablePredicate(mesh: AbstractMesh, controllerId: string): boolean {\r\n        let parent: TransformNode = mesh;\r\n\r\n        while (parent) {\r\n            if (parent.reservedDataStore && parent.reservedDataStore.nearInteraction && parent.reservedDataStore.nearInteraction.excludedControllerId === controllerId) {\r\n                return false;\r\n            }\r\n            parent = parent.parent as TransformNode;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    private _handleTransitionAnimation(controllerData: ControllerData, newState: ControllerOrbAnimationState) {\r\n        if (\r\n            controllerData.currentAnimationState === newState ||\r\n            this._options.nearInteractionControllerMode !== WebXRNearControllerMode.CENTERED_IN_FRONT ||\r\n            !!controllerData.xrController?.inputSource.hand\r\n        ) {\r\n            return;\r\n        }\r\n\r\n        // Don't always break to allow for animation fallthrough on rare cases of multi-transitions\r\n        if (newState > controllerData.currentAnimationState) {\r\n            switch (controllerData.currentAnimationState) {\r\n                case ControllerOrbAnimationState.DEHYDRATED: {\r\n                    controllerData.hydrateCollisionMeshFunction(true);\r\n                    if (newState === ControllerOrbAnimationState.HOVER) {\r\n                        break;\r\n                    }\r\n                }\r\n                // eslint-disable-next-line no-fallthrough\r\n                case ControllerOrbAnimationState.HOVER: {\r\n                    controllerData.touchCollisionMeshFunction(true);\r\n                    if (newState === ControllerOrbAnimationState.TOUCH) {\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            switch (controllerData.currentAnimationState) {\r\n                case ControllerOrbAnimationState.TOUCH: {\r\n                    controllerData.touchCollisionMeshFunction(false);\r\n                    if (newState === ControllerOrbAnimationState.HOVER) {\r\n                        break;\r\n                    }\r\n                }\r\n                // eslint-disable-next-line no-fallthrough\r\n                case ControllerOrbAnimationState.HOVER: {\r\n                    controllerData.hydrateCollisionMeshFunction(false);\r\n                    if (newState === ControllerOrbAnimationState.DEHYDRATED) {\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        controllerData.currentAnimationState = newState;\r\n    }\r\n\r\n    private readonly _hoverRadius = 0.1;\r\n    private readonly _pickRadius = 0.02;\r\n    private readonly _controllerPickRadius = 0.03; // The radius is slightly larger here to make it easier to manipulate since it's not tied to the hand position\r\n    private readonly _nearGrabLengthScale = 5;\r\n\r\n    private _processTouchPoint(id: string, position: Vector3, orientation: Quaternion) {\r\n        const controllerData = this._controllers[id];\r\n\r\n        // Position and orientation could be temporary values, se we take care of them before calling any functions that use temporary vectors/quaternions\r\n        controllerData.grabRay.origin.copyFrom(position);\r\n        orientation.toEulerAnglesToRef(TmpVectors.Vector3[0]);\r\n        controllerData.grabRay.direction.copyFrom(TmpVectors.Vector3[0]);\r\n\r\n        if (this._options.nearInteractionControllerMode === WebXRNearControllerMode.CENTERED_IN_FRONT && !controllerData.xrController?.inputSource.hand) {\r\n            // offset the touch point in the direction the transform is facing\r\n            controllerData.xrController!.getWorldPointerRayToRef(this._tmpRay);\r\n            controllerData.grabRay.origin.addInPlace(this._tmpRay.direction.scale(0.05));\r\n        }\r\n\r\n        controllerData.grabRay.length = this._nearGrabLengthScale * this._hoverRadius * this._xrSessionManager.worldScalingFactor;\r\n        controllerData.touchCollisionMesh.position.copyFrom(controllerData.grabRay.origin).scaleInPlace(this._xrSessionManager.worldScalingFactor);\r\n    }\r\n\r\n    protected _onXRFrame(_xrFrame: XRFrame) {\r\n        Object.keys(this._controllers).forEach((id) => {\r\n            // only do this for the selected pointer\r\n            const controllerData = this._controllers[id];\r\n            const handData = controllerData.xrController?.inputSource.hand;\r\n            // If near interaction is not enabled/available for this controller, return early\r\n            if (\r\n                (!this._options.enableNearInteractionOnAllControllers && id !== this._attachedController) ||\r\n                !controllerData.xrController ||\r\n                (!handData && (!this._options.nearInteractionControllerMode || !controllerData.xrController.inputSource.gamepad))\r\n            ) {\r\n                controllerData.pick = null;\r\n                return;\r\n            }\r\n            controllerData.hoverInteraction = false;\r\n            controllerData.nearInteraction = false;\r\n\r\n            // Every frame check collisions/input\r\n            if (controllerData.xrController) {\r\n                if (handData) {\r\n                    const xrIndexTip = handData.get(\"index-finger-tip\");\r\n                    if (xrIndexTip) {\r\n                        const indexTipPose = _xrFrame.getJointPose!(xrIndexTip, this._xrSessionManager.referenceSpace);\r\n                        if (indexTipPose && indexTipPose.transform) {\r\n                            const axisRHSMultiplier = this._scene.useRightHandedSystem ? 1 : -1;\r\n                            TmpVectors.Vector3[0].set(indexTipPose.transform.position.x, indexTipPose.transform.position.y, indexTipPose.transform.position.z * axisRHSMultiplier);\r\n                            TmpVectors.Quaternion[0].set(\r\n                                indexTipPose.transform.orientation.x,\r\n                                indexTipPose.transform.orientation.y,\r\n                                indexTipPose.transform.orientation.z * axisRHSMultiplier,\r\n                                indexTipPose.transform.orientation.w * axisRHSMultiplier\r\n                            );\r\n\r\n                            this._processTouchPoint(id, TmpVectors.Vector3[0], TmpVectors.Quaternion[0]);\r\n                        }\r\n                    }\r\n                } else if (controllerData.xrController.inputSource.gamepad && this._options.nearInteractionControllerMode !== WebXRNearControllerMode.DISABLED) {\r\n                    let controllerPose = controllerData.xrController.pointer;\r\n                    if (controllerData.xrController.grip && this._options.nearInteractionControllerMode === WebXRNearControllerMode.CENTERED_ON_CONTROLLER) {\r\n                        controllerPose = controllerData.xrController.grip;\r\n                    }\r\n\r\n                    this._processTouchPoint(id, controllerPose.position, controllerPose.rotationQuaternion!);\r\n                }\r\n            } else {\r\n                return;\r\n            }\r\n\r\n            const accuratePickInfo = (originalScenePick: Nullable<PickingInfo>, utilityScenePick: Nullable<PickingInfo>): Nullable<PickingInfo> => {\r\n                let pick = null;\r\n                if (!utilityScenePick || !utilityScenePick.hit) {\r\n                    // No hit in utility scene\r\n                    pick = originalScenePick;\r\n                } else if (!originalScenePick || !originalScenePick.hit) {\r\n                    // No hit in original scene\r\n                    pick = utilityScenePick;\r\n                } else if (utilityScenePick.distance < originalScenePick.distance) {\r\n                    // Hit is closer in utility scene\r\n                    pick = utilityScenePick;\r\n                } else {\r\n                    // Hit is closer in original scene\r\n                    pick = originalScenePick;\r\n                }\r\n                return pick;\r\n            };\r\n            const populateNearInteractionInfo = (nearInteractionInfo: Nullable<PickingInfo>): PickingInfo => {\r\n                let result = new PickingInfo();\r\n\r\n                let nearInteractionAtOrigin = false;\r\n                const nearInteraction = nearInteractionInfo && nearInteractionInfo.pickedPoint && nearInteractionInfo.hit;\r\n                if (nearInteractionInfo?.pickedPoint) {\r\n                    nearInteractionAtOrigin = nearInteractionInfo.pickedPoint.x === 0 && nearInteractionInfo.pickedPoint.y === 0 && nearInteractionInfo.pickedPoint.z === 0;\r\n                }\r\n                if (nearInteraction && !nearInteractionAtOrigin) {\r\n                    result = nearInteractionInfo!;\r\n                }\r\n                return result;\r\n            };\r\n\r\n            // Don't perform touch logic while grabbing, to prevent triggering touch interactions while in the middle of a grab interaction\r\n            // Dont update cursor logic either - the cursor should already be visible for the grab to be in range,\r\n            // and in order to maintain its position on the target mesh it is parented for the duration of the grab.\r\n            if (!controllerData.grabInteraction) {\r\n                let pick = null;\r\n\r\n                // near interaction hover\r\n                let utilitySceneHoverPick = null;\r\n                if (this._options.useUtilityLayer && this._utilityLayerScene) {\r\n                    utilitySceneHoverPick = this._pickWithSphere(\r\n                        controllerData,\r\n                        this._hoverRadius * this._xrSessionManager.worldScalingFactor,\r\n                        this._utilityLayerScene,\r\n                        (mesh: AbstractMesh) => this._nearInteractionPredicate(mesh)\r\n                    );\r\n                }\r\n                const originalSceneHoverPick = this._pickWithSphere(\r\n                    controllerData,\r\n                    this._hoverRadius * this._xrSessionManager.worldScalingFactor,\r\n                    this._scene,\r\n                    (mesh: AbstractMesh) => this._nearInteractionPredicate(mesh)\r\n                );\r\n\r\n                const hoverPickInfo = accuratePickInfo(originalSceneHoverPick, utilitySceneHoverPick);\r\n                if (hoverPickInfo && hoverPickInfo.hit) {\r\n                    pick = populateNearInteractionInfo(hoverPickInfo);\r\n                    if (pick.hit) {\r\n                        controllerData.hoverInteraction = true;\r\n                    }\r\n                }\r\n\r\n                // near interaction pick\r\n                if (controllerData.hoverInteraction) {\r\n                    let utilitySceneNearPick = null;\r\n                    const radius = (handData ? this._pickRadius : this._controllerPickRadius) * this._xrSessionManager.worldScalingFactor;\r\n                    if (this._options.useUtilityLayer && this._utilityLayerScene) {\r\n                        utilitySceneNearPick = this._pickWithSphere(controllerData, radius, this._utilityLayerScene, (mesh: AbstractMesh) => this._nearPickPredicate(mesh));\r\n                    }\r\n                    const originalSceneNearPick = this._pickWithSphere(controllerData, radius, this._scene, (mesh: AbstractMesh) => this._nearPickPredicate(mesh));\r\n                    const pickInfo = accuratePickInfo(originalSceneNearPick, utilitySceneNearPick);\r\n                    const nearPick = populateNearInteractionInfo(pickInfo);\r\n                    if (nearPick.hit) {\r\n                        // Near pick takes precedence over hover interaction\r\n                        pick = nearPick;\r\n                        controllerData.nearInteraction = true;\r\n                    }\r\n                }\r\n\r\n                controllerData.stalePick = controllerData.pick;\r\n                controllerData.pick = pick;\r\n\r\n                // Update mesh under pointer\r\n                if (controllerData.pick && controllerData.pick.pickedPoint && controllerData.pick.hit) {\r\n                    controllerData.meshUnderPointer = controllerData.pick.pickedMesh;\r\n                    controllerData.pickedPointVisualCue.position.copyFrom(controllerData.pick.pickedPoint);\r\n                    controllerData.pickedPointVisualCue.isVisible = true;\r\n\r\n                    if (this._farInteractionFeature && this._farInteractionFeature.attached) {\r\n                        this._farInteractionFeature._setPointerSelectionDisabledByPointerId(controllerData.id, true);\r\n                    }\r\n                } else {\r\n                    controllerData.meshUnderPointer = null;\r\n                    controllerData.pickedPointVisualCue.isVisible = false;\r\n\r\n                    if (this._farInteractionFeature && this._farInteractionFeature.attached) {\r\n                        this._farInteractionFeature._setPointerSelectionDisabledByPointerId(controllerData.id, false);\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Update the interaction animation. Only updates if the visible touch mesh is active\r\n            let state = ControllerOrbAnimationState.DEHYDRATED;\r\n            if (controllerData.grabInteraction || controllerData.nearInteraction) {\r\n                state = ControllerOrbAnimationState.TOUCH;\r\n            } else if (controllerData.hoverInteraction) {\r\n                state = ControllerOrbAnimationState.HOVER;\r\n            }\r\n            this._handleTransitionAnimation(controllerData, state);\r\n        });\r\n    }\r\n\r\n    private get _utilityLayerScene() {\r\n        return this._options.customUtilityLayerScene || UtilityLayerRenderer.DefaultUtilityLayer.utilityLayerScene;\r\n    }\r\n\r\n    private _generateVisualCue() {\r\n        const sceneToRenderTo = this._options.useUtilityLayer ? this._options.customUtilityLayerScene || UtilityLayerRenderer.DefaultUtilityLayer.utilityLayerScene : this._scene;\r\n        const selectionMesh = CreateSphere(\r\n            \"nearInteraction\",\r\n            {\r\n                diameter: 0.0035 * 3 * this._xrSessionManager.worldScalingFactor,\r\n            },\r\n            sceneToRenderTo\r\n        );\r\n        selectionMesh.bakeCurrentTransformIntoVertices();\r\n        selectionMesh.isPickable = false;\r\n        selectionMesh.isVisible = false;\r\n        selectionMesh.rotationQuaternion = Quaternion.Identity();\r\n        const targetMat = new StandardMaterial(\"targetMat\", sceneToRenderTo);\r\n        targetMat.specularColor = Color3.Black();\r\n        targetMat.emissiveColor = this.selectionMeshDefaultColor;\r\n        targetMat.backFaceCulling = false;\r\n        selectionMesh.material = targetMat;\r\n\r\n        return selectionMesh;\r\n    }\r\n\r\n    private _isControllerReadyForNearInteraction(id: number) {\r\n        if (this._farInteractionFeature) {\r\n            return this._farInteractionFeature._getPointerSelectionDisabledByPointerId(id);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    private _attachNearInteractionMode(xrController: WebXRInputSource) {\r\n        const controllerData = this._controllers[xrController.uniqueId];\r\n        const pointerEventInit: PointerEventInit = {\r\n            pointerId: controllerData.id,\r\n            pointerType: \"xr-near\",\r\n        };\r\n        controllerData.onFrameObserver = this._xrSessionManager.onXRFrameObservable.add(() => {\r\n            if (\r\n                (!this._options.enableNearInteractionOnAllControllers && xrController.uniqueId !== this._attachedController) ||\r\n                !controllerData.xrController ||\r\n                (!controllerData.xrController.inputSource.hand && (!this._options.nearInteractionControllerMode || !controllerData.xrController.inputSource.gamepad))\r\n            ) {\r\n                return;\r\n            }\r\n            if (controllerData.pick) {\r\n                controllerData.pick.ray = controllerData.grabRay;\r\n            }\r\n\r\n            if (controllerData.pick && this._isControllerReadyForNearInteraction(controllerData.id)) {\r\n                this._scene.simulatePointerMove(controllerData.pick, pointerEventInit);\r\n            }\r\n\r\n            // Near pick pointer event\r\n            if (controllerData.nearInteraction && controllerData.pick && controllerData.pick.hit) {\r\n                if (!controllerData.nearInteractionTargetMesh) {\r\n                    this._scene.simulatePointerDown(controllerData.pick, pointerEventInit);\r\n                    controllerData.nearInteractionTargetMesh = controllerData.meshUnderPointer;\r\n                    controllerData.downTriggered = true;\r\n                }\r\n            } else if (controllerData.nearInteractionTargetMesh && controllerData.stalePick) {\r\n                this._scene.simulatePointerUp(controllerData.stalePick, pointerEventInit);\r\n                controllerData.downTriggered = false;\r\n                controllerData.nearInteractionTargetMesh = null;\r\n            }\r\n        });\r\n\r\n        const grabCheck = (pressed: boolean) => {\r\n            if (\r\n                this._options.enableNearInteractionOnAllControllers ||\r\n                (xrController.uniqueId === this._attachedController && this._isControllerReadyForNearInteraction(controllerData.id))\r\n            ) {\r\n                if (controllerData.pick) {\r\n                    controllerData.pick.ray = controllerData.grabRay;\r\n                }\r\n                if (pressed && controllerData.pick && controllerData.meshUnderPointer && this._nearGrabPredicate(controllerData.meshUnderPointer)) {\r\n                    controllerData.grabInteraction = true;\r\n                    controllerData.pickedPointVisualCue.isVisible = false;\r\n                    this._scene.simulatePointerDown(controllerData.pick, pointerEventInit);\r\n                    controllerData.downTriggered = true;\r\n                } else if (!pressed && controllerData.pick && controllerData.grabInteraction) {\r\n                    this._scene.simulatePointerUp(controllerData.pick, pointerEventInit);\r\n                    controllerData.downTriggered = false;\r\n                    controllerData.grabInteraction = false;\r\n                    controllerData.pickedPointVisualCue.isVisible = true;\r\n                }\r\n            } else {\r\n                if (pressed && !this._options.enableNearInteractionOnAllControllers && !this._options.disableSwitchOnClick) {\r\n                    this._attachedController = xrController.uniqueId;\r\n                }\r\n            }\r\n        };\r\n\r\n        if (xrController.inputSource.gamepad) {\r\n            const init = (motionController: WebXRAbstractMotionController) => {\r\n                controllerData.squeezeComponent = motionController.getComponent(\"grasp\");\r\n                if (controllerData.squeezeComponent) {\r\n                    controllerData.onSqueezeButtonChangedObserver = controllerData.squeezeComponent.onButtonStateChangedObservable.add((component) => {\r\n                        if (component.changes.pressed) {\r\n                            const pressed = component.changes.pressed.current;\r\n                            grabCheck(pressed);\r\n                        }\r\n                    });\r\n                } else {\r\n                    controllerData.selectionComponent = motionController.getMainComponent();\r\n                    controllerData.onButtonChangedObserver = controllerData.selectionComponent.onButtonStateChangedObservable.add((component) => {\r\n                        if (component.changes.pressed) {\r\n                            const pressed = component.changes.pressed.current;\r\n                            grabCheck(pressed);\r\n                        }\r\n                    });\r\n                }\r\n            };\r\n            if (xrController.motionController) {\r\n                init(xrController.motionController);\r\n            } else {\r\n                xrController.onMotionControllerInitObservable.add(init);\r\n            }\r\n        } else {\r\n            // use the select and squeeze events\r\n            const selectStartListener = (event: XRInputSourceEvent) => {\r\n                if (\r\n                    controllerData.xrController &&\r\n                    event.inputSource === controllerData.xrController.inputSource &&\r\n                    controllerData.pick &&\r\n                    this._isControllerReadyForNearInteraction(controllerData.id) &&\r\n                    controllerData.meshUnderPointer &&\r\n                    this._nearGrabPredicate(controllerData.meshUnderPointer)\r\n                ) {\r\n                    controllerData.grabInteraction = true;\r\n                    controllerData.pickedPointVisualCue.isVisible = false;\r\n                    this._scene.simulatePointerDown(controllerData.pick, pointerEventInit);\r\n                    controllerData.downTriggered = true;\r\n                }\r\n            };\r\n\r\n            const selectEndListener = (event: XRInputSourceEvent) => {\r\n                if (\r\n                    controllerData.xrController &&\r\n                    event.inputSource === controllerData.xrController.inputSource &&\r\n                    controllerData.pick &&\r\n                    this._isControllerReadyForNearInteraction(controllerData.id)\r\n                ) {\r\n                    this._scene.simulatePointerUp(controllerData.pick, pointerEventInit);\r\n                    controllerData.grabInteraction = false;\r\n                    controllerData.pickedPointVisualCue.isVisible = true;\r\n                    controllerData.downTriggered = false;\r\n                }\r\n            };\r\n\r\n            controllerData.eventListeners = {\r\n                selectend: selectEndListener,\r\n                selectstart: selectStartListener,\r\n            };\r\n\r\n            this._xrSessionManager.session.addEventListener(\"selectstart\", selectStartListener);\r\n            this._xrSessionManager.session.addEventListener(\"selectend\", selectEndListener);\r\n        }\r\n    }\r\n\r\n    private _detachController(xrControllerUniqueId: string) {\r\n        const controllerData = this._controllers[xrControllerUniqueId];\r\n        if (!controllerData) {\r\n            return;\r\n        }\r\n        if (controllerData.squeezeComponent) {\r\n            if (controllerData.onSqueezeButtonChangedObserver) {\r\n                controllerData.squeezeComponent.onButtonStateChangedObservable.remove(controllerData.onSqueezeButtonChangedObserver);\r\n            }\r\n        }\r\n        if (controllerData.selectionComponent) {\r\n            if (controllerData.onButtonChangedObserver) {\r\n                controllerData.selectionComponent.onButtonStateChangedObservable.remove(controllerData.onButtonChangedObserver);\r\n            }\r\n        }\r\n        if (controllerData.onFrameObserver) {\r\n            this._xrSessionManager.onXRFrameObservable.remove(controllerData.onFrameObserver);\r\n        }\r\n        if (controllerData.eventListeners) {\r\n            Object.keys(controllerData.eventListeners).forEach((eventName: string) => {\r\n                const func = controllerData.eventListeners && controllerData.eventListeners[eventName as XREventType];\r\n                if (func) {\r\n                    this._xrSessionManager.session.removeEventListener(eventName as XREventType, func as any);\r\n                }\r\n            });\r\n        }\r\n        controllerData.touchCollisionMesh.dispose();\r\n        controllerData.pickedPointVisualCue.dispose();\r\n\r\n        this._xrSessionManager.runInXRFrame(() => {\r\n            if (!controllerData.downTriggered) {\r\n                return;\r\n            }\r\n            // Fire a pointerup in case controller was detached before a pointerup event was fired\r\n            const pointerEventInit: PointerEventInit = {\r\n                pointerId: controllerData.id,\r\n                pointerType: \"xr-near\",\r\n            };\r\n            this._scene.simulatePointerUp(new PickingInfo(), pointerEventInit);\r\n        });\r\n\r\n        // remove world scale observer\r\n        if (controllerData._worldScaleObserver) {\r\n            this._xrSessionManager.onWorldScaleFactorChangedObservable.remove(controllerData._worldScaleObserver);\r\n        }\r\n\r\n        // remove from the map\r\n        delete this._controllers[xrControllerUniqueId];\r\n        if (this._attachedController === xrControllerUniqueId) {\r\n            // check for other controllers\r\n            const keys = Object.keys(this._controllers);\r\n            if (keys.length) {\r\n                this._attachedController = keys[0];\r\n            } else {\r\n                this._attachedController = \"\";\r\n            }\r\n        }\r\n    }\r\n\r\n    private _generateNewTouchPointMesh() {\r\n        const worldScale = this._xrSessionManager.worldScalingFactor;\r\n        // populate information for near hover, pick and pinch\r\n        const meshCreationScene = this._options.useUtilityLayer ? this._options.customUtilityLayerScene || UtilityLayerRenderer.DefaultUtilityLayer.utilityLayerScene : this._scene;\r\n\r\n        const touchCollisionMesh = CreateSphere(\"PickSphere\", { diameter: 1 * worldScale }, meshCreationScene);\r\n        touchCollisionMesh.isVisible = false;\r\n\r\n        // Generate the material for the touch mesh visuals\r\n        if (this._options.motionControllerOrbMaterial) {\r\n            touchCollisionMesh.material = this._options.motionControllerOrbMaterial;\r\n        } else {\r\n            NodeMaterial.ParseFromSnippetAsync(\"8RUNKL#3\", meshCreationScene).then((nodeMaterial) => {\r\n                touchCollisionMesh.material = nodeMaterial;\r\n            });\r\n        }\r\n\r\n        const easingFunction = new QuadraticEase();\r\n        easingFunction.setEasingMode(EasingFunction.EASINGMODE_EASEINOUT);\r\n\r\n        // Adjust the visual size based off of the size of the touch collision orb.\r\n        // Having the size perfectly match for hover gives a more accurate tell for when the user will start interacting with the target\r\n        // Sizes for other states are somewhat arbitrary, as they are based on what feels nice during an interaction\r\n        const hoverSizeVec = new Vector3(this._controllerPickRadius, this._controllerPickRadius, this._controllerPickRadius).scaleInPlace(worldScale);\r\n        const touchSize = this._controllerPickRadius * (4 / 3);\r\n        const touchSizeVec = new Vector3(touchSize, touchSize, touchSize).scaleInPlace(worldScale);\r\n        const hydrateTransitionSize = this._controllerPickRadius * (7 / 6);\r\n        const hydrateTransitionSizeVec = new Vector3(hydrateTransitionSize, hydrateTransitionSize, hydrateTransitionSize).scaleInPlace(worldScale);\r\n        const touchHoverTransitionSize = this._controllerPickRadius * (4 / 5);\r\n        const touchHoverTransitionSizeVec = new Vector3(touchHoverTransitionSize, touchHoverTransitionSize, touchHoverTransitionSize).scaleInPlace(worldScale);\r\n        const hoverTouchTransitionSize = this._controllerPickRadius * (3 / 2);\r\n        const hoverTouchTransitionSizeVec = new Vector3(hoverTouchTransitionSize, hoverTouchTransitionSize, hoverTouchTransitionSize).scaleInPlace(worldScale);\r\n\r\n        const touchKeys = [\r\n            { frame: 0, value: hoverSizeVec },\r\n            { frame: 10, value: hoverTouchTransitionSizeVec },\r\n            { frame: 18, value: touchSizeVec },\r\n        ];\r\n        const releaseKeys = [\r\n            { frame: 0, value: touchSizeVec },\r\n            { frame: 10, value: touchHoverTransitionSizeVec },\r\n            { frame: 18, value: hoverSizeVec },\r\n        ];\r\n        const hydrateKeys = [\r\n            { frame: 0, value: Vector3.ZeroReadOnly },\r\n            { frame: 12, value: hydrateTransitionSizeVec },\r\n            { frame: 15, value: hoverSizeVec },\r\n        ];\r\n        const dehydrateKeys = [\r\n            { frame: 0, value: hoverSizeVec },\r\n            { frame: 10, value: Vector3.ZeroReadOnly },\r\n            { frame: 15, value: Vector3.ZeroReadOnly },\r\n        ];\r\n\r\n        const touchAction = new Animation(\"touch\", \"scaling\", 60, Animation.ANIMATIONTYPE_VECTOR3, Animation.ANIMATIONLOOPMODE_CONSTANT);\r\n        const releaseAction = new Animation(\"release\", \"scaling\", 60, Animation.ANIMATIONTYPE_VECTOR3, Animation.ANIMATIONLOOPMODE_CONSTANT);\r\n        const hydrateAction = new Animation(\"hydrate\", \"scaling\", 60, Animation.ANIMATIONTYPE_VECTOR3, Animation.ANIMATIONLOOPMODE_CONSTANT);\r\n        const dehydrateAction = new Animation(\"dehydrate\", \"scaling\", 60, Animation.ANIMATIONTYPE_VECTOR3, Animation.ANIMATIONLOOPMODE_CONSTANT);\r\n\r\n        touchAction.setEasingFunction(easingFunction);\r\n        releaseAction.setEasingFunction(easingFunction);\r\n        hydrateAction.setEasingFunction(easingFunction);\r\n        dehydrateAction.setEasingFunction(easingFunction);\r\n\r\n        touchAction.setKeys(touchKeys);\r\n        releaseAction.setKeys(releaseKeys);\r\n        hydrateAction.setKeys(hydrateKeys);\r\n        dehydrateAction.setKeys(dehydrateKeys);\r\n\r\n        const touchCollisionMeshFunction = (isTouch: boolean) => {\r\n            const action = isTouch ? touchAction : releaseAction;\r\n            meshCreationScene.beginDirectAnimation(touchCollisionMesh, [action], 0, 18, false, 1);\r\n        };\r\n\r\n        const hydrateCollisionMeshFunction = (isHydration: boolean) => {\r\n            const action = isHydration ? hydrateAction : dehydrateAction;\r\n            if (isHydration) {\r\n                touchCollisionMesh.isVisible = true;\r\n            }\r\n            meshCreationScene.beginDirectAnimation(touchCollisionMesh, [action], 0, 15, false, 1, () => {\r\n                if (!isHydration) {\r\n                    touchCollisionMesh.isVisible = false;\r\n                }\r\n            });\r\n        };\r\n\r\n        return { touchCollisionMesh, touchCollisionMeshFunction, hydrateCollisionMeshFunction };\r\n    }\r\n\r\n    private _pickWithSphere(controllerData: ControllerData, radius: number, sceneToUse: Scene, predicate: (mesh: AbstractMesh) => boolean): Nullable<PickingInfo> {\r\n        const pickingInfo = new PickingInfo();\r\n        pickingInfo.distance = +Infinity;\r\n\r\n        if (controllerData.touchCollisionMesh && controllerData.xrController) {\r\n            const position = controllerData.touchCollisionMesh.position;\r\n            const sphere = BoundingSphere.CreateFromCenterAndRadius(position, radius);\r\n\r\n            for (let meshIndex = 0; meshIndex < sceneToUse.meshes.length; meshIndex++) {\r\n                const mesh = sceneToUse.meshes[meshIndex];\r\n                if (!predicate(mesh) || !this._controllerAvailablePredicate(mesh, controllerData.xrController.uniqueId)) {\r\n                    continue;\r\n                }\r\n                const result = WebXRNearInteraction.PickMeshWithSphere(mesh, sphere);\r\n\r\n                if (result && result.hit && result.distance < pickingInfo.distance) {\r\n                    pickingInfo.hit = result.hit;\r\n                    pickingInfo.pickedMesh = mesh;\r\n                    pickingInfo.pickedPoint = result.pickedPoint;\r\n                    pickingInfo.aimTransform = controllerData.xrController.pointer;\r\n                    pickingInfo.gripTransform = controllerData.xrController.grip || null;\r\n                    pickingInfo.originMesh = controllerData.touchCollisionMesh;\r\n                    pickingInfo.distance = result.distance;\r\n                    pickingInfo.bu = result.bu;\r\n                    pickingInfo.bv = result.bv;\r\n                    pickingInfo.faceId = result.faceId;\r\n                    pickingInfo.subMeshId = result.subMeshId;\r\n                }\r\n            }\r\n        }\r\n        return pickingInfo;\r\n    }\r\n\r\n    /**\r\n     * Picks a mesh with a sphere\r\n     * @param mesh the mesh to pick\r\n     * @param sphere picking sphere in world coordinates\r\n     * @param skipBoundingInfo a boolean indicating if we should skip the bounding info check\r\n     * @returns the picking info\r\n     */\r\n    public static PickMeshWithSphere(mesh: AbstractMesh, sphere: BoundingSphere, skipBoundingInfo = false): PickingInfo {\r\n        const subMeshes = mesh.subMeshes;\r\n        const pi = new PickingInfo();\r\n        const boundingInfo = mesh.getBoundingInfo();\r\n\r\n        if (!mesh._generatePointsArray()) {\r\n            return pi;\r\n        }\r\n\r\n        if (!mesh.subMeshes || !boundingInfo) {\r\n            return pi;\r\n        }\r\n\r\n        if (!skipBoundingInfo && !BoundingSphere.Intersects(boundingInfo.boundingSphere, sphere)) {\r\n            return pi;\r\n        }\r\n\r\n        const result = TmpVectors.Vector3[0];\r\n        const tmpVec = TmpVectors.Vector3[1];\r\n        const tmpRay = new Ray(Vector3.Zero(), Vector3.Zero(), 1);\r\n\r\n        let distance = +Infinity;\r\n        let tmp, tmpDistanceSphereToCenter, tmpDistanceSurfaceToCenter, intersectionInfo;\r\n        const center = TmpVectors.Vector3[2];\r\n        const worldToMesh = TmpVectors.Matrix[0];\r\n        worldToMesh.copyFrom(mesh.getWorldMatrix());\r\n        worldToMesh.invert();\r\n        Vector3.TransformCoordinatesToRef(sphere.center, worldToMesh, center);\r\n\r\n        for (let index = 0; index < subMeshes.length; index++) {\r\n            const subMesh = subMeshes[index];\r\n\r\n            subMesh.projectToRef(center, <Vector3[]>mesh._positions, <IndicesArray>mesh.getIndices(), tmpVec);\r\n\r\n            Vector3.TransformCoordinatesToRef(tmpVec, mesh.getWorldMatrix(), tmpVec);\r\n            tmp = Vector3.Distance(tmpVec, sphere.center);\r\n\r\n            // Check for finger inside of mesh\r\n            tmpDistanceSurfaceToCenter = Vector3.Distance(tmpVec, mesh.getAbsolutePosition());\r\n            tmpDistanceSphereToCenter = Vector3.Distance(sphere.center, mesh.getAbsolutePosition());\r\n            if (tmpDistanceSphereToCenter !== -1 && tmpDistanceSurfaceToCenter !== -1 && tmpDistanceSurfaceToCenter > tmpDistanceSphereToCenter) {\r\n                tmp = 0;\r\n                tmpVec.copyFrom(sphere.center);\r\n            }\r\n\r\n            if (tmp !== -1 && tmp < distance) {\r\n                distance = tmp;\r\n\r\n                // ray between the sphere center and the point on the mesh\r\n                Ray.CreateFromToToRef(sphere.center, tmpVec, tmpRay);\r\n                tmpRay.length = distance * 2;\r\n                intersectionInfo = tmpRay.intersectsMesh(mesh);\r\n\r\n                result.copyFrom(tmpVec);\r\n            }\r\n        }\r\n\r\n        if (distance < sphere.radius) {\r\n            pi.hit = true;\r\n            pi.distance = distance;\r\n            pi.pickedMesh = mesh;\r\n            pi.pickedPoint = result.clone();\r\n            if (intersectionInfo && intersectionInfo.bu !== null && intersectionInfo.bv !== null) {\r\n                pi.faceId = intersectionInfo.faceId;\r\n                pi.subMeshId = intersectionInfo.subMeshId;\r\n                pi.bu = intersectionInfo.bu;\r\n                pi.bv = intersectionInfo.bv;\r\n            }\r\n        }\r\n\r\n        return pi;\r\n    }\r\n}\r\n\r\n//Register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRNearInteraction.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRNearInteraction(xrSessionManager, options);\r\n    },\r\n    WebXRNearInteraction.Version,\r\n    true\r\n);\r\n"]}