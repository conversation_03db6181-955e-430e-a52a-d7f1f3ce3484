{"version": 3, "file": "physicsShapeCastQuery.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Physics/physicsShapeCastQuery.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Quaternion, Vector3 } from \"../Maths/math.vector\";\r\nimport type { PhysicsShape } from \"./v2/physicsShape\";\r\nimport type { PhysicsBody } from \"./v2/physicsBody\";\r\n\r\n/**\r\n * Shape cast query\r\n */\r\nexport interface IPhysicsShapeCastQuery {\r\n    /**\r\n     * The shape to query with\r\n     */\r\n    shape: PhysicsShape;\r\n    /**\r\n     * The rotation of the shape\r\n     */\r\n    rotation: Quaternion;\r\n    /**\r\n     * The start position of the query\r\n     */\r\n    startPosition: Vector3;\r\n    /**\r\n     * The end position of the query\r\n     */\r\n    endPosition: Vector3;\r\n    /**\r\n     * Should trigger collisions be considered in the query?\r\n     */\r\n    shouldHitTriggers: boolean;\r\n    /**\r\n     * Ignores the body passed if it is in the query\r\n     */\r\n    ignoreBody?: PhysicsBody;\r\n}\r\n"]}