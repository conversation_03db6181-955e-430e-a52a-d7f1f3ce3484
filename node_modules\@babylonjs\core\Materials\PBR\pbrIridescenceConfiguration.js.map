{"version": 3, "file": "pbrIridescenceConfiguration.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/PBR/pbrIridescenceConfiguration.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAExF,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAKjD,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAKrD,OAAO,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAE3F;;GAEG;AACH,MAAM,OAAO,0BAA2B,SAAQ,eAAe;IAA/D;;QACW,gBAAW,GAAG,KAAK,CAAC;QACpB,wBAAmB,GAAG,KAAK,CAAC;QAC5B,gCAA2B,GAAG,CAAC,CAAC;QAChC,kCAA6B,GAAG,KAAK,CAAC;QACtC,0CAAqC,GAAG,CAAC,CAAC;QAC1C,+CAA0C,GAAG,KAAK,CAAC;IAC9D,CAAC;CAAA;AAED;;GAEG;AACH,MAAM,OAAO,2BAA4B,SAAQ,kBAAkB;IA2E/D,gBAAgB;IACT,gCAAgC;QACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,IAAI,CAAC,wCAAwC,EAAE,CAAC;IACpD,CAAC;IAED,YAAY,QAAyB,EAAE,eAAe,GAAG,IAAI;QACzD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,EAAE,GAAG,EAAE,IAAI,0BAA0B,EAAE,EAAE,eAAe,CAAC,CAAC;QA1DtF,eAAU,GAAG,KAAK,CAAC;QAC3B;;WAEG;QAGI,cAAS,GAAG,KAAK,CAAC;QAEzB;;WAEG;QAEI,cAAS,GAAW,CAAC,CAAC;QAE7B;;WAEG;QAEI,qBAAgB,GAAW,2BAA2B,CAAC,wBAAwB,CAAC;QAEvF;;WAEG;QAEI,qBAAgB,GAAW,2BAA2B,CAAC,wBAAwB,CAAC;QAEvF;;WAEG;QAEI,sBAAiB,GAAW,2BAA2B,CAAC,yBAAyB,CAAC;QAEjF,aAAQ,GAA0B,IAAI,CAAC;QAC/C;;WAEG;QAGI,YAAO,GAA0B,IAAI,CAAC;QAErC,sBAAiB,GAA0B,IAAI,CAAC;QACxD;;WAEG;QAGI,qBAAgB,GAA0B,IAAI,CAAC;QAclD,IAAI,CAAC,wCAAwC,GAAG,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IAClH,CAAC;IAEM,iBAAiB,CAAC,OAAmC,EAAE,KAAY;QACtE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,IAAI,KAAK,CAAC,eAAe,EAAE;gBACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,yBAAyB,EAAE;oBAC1D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE;wBACvC,OAAO,KAAK,CAAC;qBAChB;iBACJ;gBAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,aAAa,CAAC,yBAAyB,EAAE;oBACnE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE;wBAChD,OAAO,KAAK,CAAC;qBAChB;iBACJ;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,8BAA8B,CAAC,OAAmC,EAAE,KAAY;QACnF,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;YAC3B,OAAO,CAAC,0CAA0C;gBAC9C,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,2BAA2B,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAE/J,IAAI,OAAO,CAAC,iBAAiB,EAAE;gBAC3B,IAAI,KAAK,CAAC,eAAe,EAAE;oBACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,yBAAyB,EAAE;wBAC1D,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;qBAC5E;yBAAM;wBACH,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;qBACvC;oBAED,IAAI,CAAC,OAAO,CAAC,0CAA0C,IAAI,IAAI,CAAC,iBAAiB,IAAI,aAAa,CAAC,yBAAyB,EAAE;wBAC1H,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,+BAA+B,CAAC,CAAC;qBAC/F;yBAAM;wBACH,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;qBACjD;iBACJ;aACJ;SACJ;aAAM;YACH,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;YAC5B,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACpC,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;YAC9C,OAAO,CAAC,0CAA0C,GAAG,KAAK,CAAC;YAC3D,OAAO,CAAC,2BAA2B,GAAG,CAAC,CAAC;YACxC,OAAO,CAAC,qCAAqC,GAAG,CAAC,CAAC;SACrD;IACL,CAAC;IAEM,cAAc,CAAC,aAA4B,EAAE,KAAY,EAAE,MAAc,EAAE,OAAgB;QAC9F,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO;SACV;QAED,MAAM,OAAO,GAAG,OAAQ,CAAC,eAAwD,CAAC;QAElF,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAEzC,MAAM,iBAAiB,GAAG,OAAO,CAAC,0CAA0C,CAAC;QAE7E,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YAC7D,IAAI,iBAAiB,IAAI,aAAa,CAAC,yBAAyB,EAAE;gBAC9D,aAAa,CAAC,YAAY,CAAC,mBAAmB,EAAE,IAAI,CAAC,QAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,QAAS,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC/G,iBAAiB,CAAC,IAAI,CAAC,QAAS,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;aACnE;iBAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,aAAa,CAAC,yBAAyB,EAAE;gBAC7F,aAAa,CAAC,YAAY,CACtB,mBAAmB,EACnB,IAAI,CAAC,QAAQ,EAAE,gBAAgB,IAAI,CAAC,EACpC,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,EACzB,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,IAAI,CAAC,EAC7C,IAAI,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,CACrC,CAAC;gBACF,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACf,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;iBAClE;gBACD,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC,0CAA0C,EAAE;oBACrG,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,EAAE,sBAAsB,CAAC,CAAC;iBACpF;aACJ;YAED,4BAA4B;YAC5B,aAAa,CAAC,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC1I;QAED,WAAW;QACX,IAAI,KAAK,CAAC,eAAe,EAAE;YACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,yBAAyB,EAAE;gBAC1D,aAAa,CAAC,UAAU,CAAC,oBAAoB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aACjE;YAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC,0CAA0C,IAAI,aAAa,CAAC,yBAAyB,EAAE;gBAChJ,aAAa,CAAC,UAAU,CAAC,6BAA6B,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;aACnF;SACJ;IACL,CAAC;IAEM,UAAU,CAAC,OAAoB;QAClC,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,OAAO,EAAE;YACpC,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,iBAAiB,CAAC,cAA6B;QAClD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACtC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC/C;IACL,CAAC;IAEM,cAAc,CAAC,WAA0B;QAC5C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAClF,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7G,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC5C;IACL,CAAC;IAEM,OAAO,CAAC,oBAA8B;QACzC,IAAI,oBAAoB,EAAE;YACtB,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;SACrC;IACL,CAAC;IAEM,YAAY;QACf,OAAO,6BAA6B,CAAC;IACzC,CAAC;IAEM,YAAY,CAAC,OAAmC,EAAE,SAA0B,EAAE,WAAmB;QACpG,IAAI,OAAO,CAAC,WAAW,EAAE;YACrB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,aAAa,CAAC,CAAC;SACvD;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAEM,WAAW,CAAC,QAAkB;QACjC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE,6BAA6B,CAAC,CAAC;IACvE,CAAC;IAEM,WAAW;QACd,OAAO;YACH,GAAG,EAAE;gBACD,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBACrD,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBACpD,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBACrD,EAAE,IAAI,EAAE,4BAA4B,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;aACjE;SACJ,CAAC;IACN,CAAC;;AAzPD;;;;GAIG;AACoB,oDAAwB,GAAG,GAAG,AAAN,CAAO;AAEtD;;;;GAIG;AACoB,oDAAwB,GAAG,GAAG,AAAN,CAAO;AAEtD;;;;GAIG;AACoB,qDAAyB,GAAG,GAAG,AAAN,CAAO;AAQhD;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;8DAC5B;AAMlB;IADN,SAAS,EAAE;8DACiB;AAMtB;IADN,SAAS,EAAE;qEAC2E;AAMhF;IADN,SAAS,EAAE;qEAC2E;AAMhF;IADN,SAAS,EAAE;sEAC6E;AAQlF;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;4DACR;AAQtC;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;qEACC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"../../types\";\r\nimport { serialize, serializeAsTexture, expandToProperty } from \"../../Misc/decorators\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { MaterialFlags } from \"../materialFlags\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport type { IAnimatable } from \"../../Animations/animatable.interface\";\r\nimport type { EffectFallbacks } from \"../effectFallbacks\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { MaterialPluginBase } from \"../materialPluginBase\";\r\nimport { MaterialDefines } from \"../materialDefines\";\r\n\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport { BindTextureMatrix, PrepareDefinesForMergedUV } from \"../materialHelper.functions\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class MaterialIridescenceDefines extends MaterialDefines {\r\n    public IRIDESCENCE = false;\r\n    public IRIDESCENCE_TEXTURE = false;\r\n    public IRIDESCENCE_TEXTUREDIRECTUV = 0;\r\n    public IRIDESCENCE_THICKNESS_TEXTURE = false;\r\n    public IRIDESCENCE_THICKNESS_TEXTUREDIRECTUV = 0;\r\n    public IRIDESCENCE_USE_THICKNESS_FROM_MAINTEXTURE = false;\r\n}\r\n\r\n/**\r\n * Plugin that implements the iridescence (thin film) component of the PBR material\r\n */\r\nexport class PBRIridescenceConfiguration extends MaterialPluginBase {\r\n    protected _material: PBRBaseMaterial;\r\n\r\n    /**\r\n     * The default minimum thickness of the thin-film layer given in nanometers (nm).\r\n     * Defaults to 100 nm.\r\n     * @internal\r\n     */\r\n    public static readonly _DefaultMinimumThickness = 100;\r\n\r\n    /**\r\n     * The default maximum thickness of the thin-film layer given in nanometers (nm).\r\n     * Defaults to 400 nm.\r\n     * @internal\r\n     */\r\n    public static readonly _DefaultMaximumThickness = 400;\r\n\r\n    /**\r\n     * The default index of refraction of the thin-film layer.\r\n     * Defaults to 1.3\r\n     * @internal\r\n     */\r\n    public static readonly _DefaultIndexOfRefraction = 1.3;\r\n\r\n    private _isEnabled = false;\r\n    /**\r\n     * Defines if the iridescence is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isEnabled = false;\r\n\r\n    /**\r\n     * Defines the iridescence layer strength (between 0 and 1) it defaults to 1.\r\n     */\r\n    @serialize()\r\n    public intensity: number = 1;\r\n\r\n    /**\r\n     * Defines the minimum thickness of the thin-film layer given in nanometers (nm).\r\n     */\r\n    @serialize()\r\n    public minimumThickness: number = PBRIridescenceConfiguration._DefaultMinimumThickness;\r\n\r\n    /**\r\n     * Defines the maximum thickness of the thin-film layer given in nanometers (nm). This will be the thickness used if not thickness texture has been set.\r\n     */\r\n    @serialize()\r\n    public maximumThickness: number = PBRIridescenceConfiguration._DefaultMaximumThickness;\r\n\r\n    /**\r\n     * Defines the maximum thickness of the thin-film layer given in nanometers (nm).\r\n     */\r\n    @serialize()\r\n    public indexOfRefraction: number = PBRIridescenceConfiguration._DefaultIndexOfRefraction;\r\n\r\n    private _texture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the iridescence intensity in a texture (red channel)\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public texture: Nullable<BaseTexture> = null;\r\n\r\n    private _thicknessTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the iridescence thickness in a texture (green channel)\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public thicknessTexture: Nullable<BaseTexture> = null;\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsTexturesDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsTexturesDirty(): void {\r\n        this._enable(this._isEnabled);\r\n        this._internalMarkAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    constructor(material: PBRBaseMaterial, addToPluginList = true) {\r\n        super(material, \"PBRIridescence\", 110, new MaterialIridescenceDefines(), addToPluginList);\r\n\r\n        this._internalMarkAllSubMeshesAsTexturesDirty = material._dirtyCallbacks[Constants.MATERIAL_TextureDirtyFlag];\r\n    }\r\n\r\n    public isReadyForSubMesh(defines: MaterialIridescenceDefines, scene: Scene): boolean {\r\n        if (!this._isEnabled) {\r\n            return true;\r\n        }\r\n\r\n        if (defines._areTexturesDirty) {\r\n            if (scene.texturesEnabled) {\r\n                if (this._texture && MaterialFlags.IridescenceTextureEnabled) {\r\n                    if (!this._texture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._thicknessTexture && MaterialFlags.IridescenceTextureEnabled) {\r\n                    if (!this._thicknessTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public prepareDefinesBeforeAttributes(defines: MaterialIridescenceDefines, scene: Scene): void {\r\n        if (this._isEnabled) {\r\n            defines.IRIDESCENCE = true;\r\n            defines.IRIDESCENCE_USE_THICKNESS_FROM_MAINTEXTURE =\r\n                this._texture !== null && this._texture._texture === this._thicknessTexture?._texture && this._texture.checkTransformsAreIdentical(this._thicknessTexture);\r\n\r\n            if (defines._areTexturesDirty) {\r\n                if (scene.texturesEnabled) {\r\n                    if (this._texture && MaterialFlags.IridescenceTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._texture, defines, \"IRIDESCENCE_TEXTURE\");\r\n                    } else {\r\n                        defines.IRIDESCENCE_TEXTURE = false;\r\n                    }\r\n\r\n                    if (!defines.IRIDESCENCE_USE_THICKNESS_FROM_MAINTEXTURE && this._thicknessTexture && MaterialFlags.IridescenceTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._thicknessTexture, defines, \"IRIDESCENCE_THICKNESS_TEXTURE\");\r\n                    } else {\r\n                        defines.IRIDESCENCE_THICKNESS_TEXTURE = false;\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            defines.IRIDESCENCE = false;\r\n            defines.IRIDESCENCE_TEXTURE = false;\r\n            defines.IRIDESCENCE_THICKNESS_TEXTURE = false;\r\n            defines.IRIDESCENCE_USE_THICKNESS_FROM_MAINTEXTURE = false;\r\n            defines.IRIDESCENCE_TEXTUREDIRECTUV = 0;\r\n            defines.IRIDESCENCE_THICKNESS_TEXTUREDIRECTUV = 0;\r\n        }\r\n    }\r\n\r\n    public bindForSubMesh(uniformBuffer: UniformBuffer, scene: Scene, engine: Engine, subMesh: SubMesh): void {\r\n        if (!this._isEnabled) {\r\n            return;\r\n        }\r\n\r\n        const defines = subMesh!.materialDefines as unknown as MaterialIridescenceDefines;\r\n\r\n        const isFrozen = this._material.isFrozen;\r\n\r\n        const identicalTextures = defines.IRIDESCENCE_USE_THICKNESS_FROM_MAINTEXTURE;\r\n\r\n        if (!uniformBuffer.useUbo || !isFrozen || !uniformBuffer.isSync) {\r\n            if (identicalTextures && MaterialFlags.IridescenceTextureEnabled) {\r\n                uniformBuffer.updateFloat4(\"vIridescenceInfos\", this._texture!.coordinatesIndex, this._texture!.level, -1, -1);\r\n                BindTextureMatrix(this._texture!, uniformBuffer, \"iridescence\");\r\n            } else if ((this._texture || this._thicknessTexture) && MaterialFlags.IridescenceTextureEnabled) {\r\n                uniformBuffer.updateFloat4(\r\n                    \"vIridescenceInfos\",\r\n                    this._texture?.coordinatesIndex ?? 0,\r\n                    this._texture?.level ?? 0,\r\n                    this._thicknessTexture?.coordinatesIndex ?? 0,\r\n                    this._thicknessTexture?.level ?? 0\r\n                );\r\n                if (this._texture) {\r\n                    BindTextureMatrix(this._texture, uniformBuffer, \"iridescence\");\r\n                }\r\n                if (this._thicknessTexture && !identicalTextures && !defines.IRIDESCENCE_USE_THICKNESS_FROM_MAINTEXTURE) {\r\n                    BindTextureMatrix(this._thicknessTexture, uniformBuffer, \"iridescenceThickness\");\r\n                }\r\n            }\r\n\r\n            // Clear Coat General params\r\n            uniformBuffer.updateFloat4(\"vIridescenceParams\", this.intensity, this.indexOfRefraction, this.minimumThickness, this.maximumThickness);\r\n        }\r\n\r\n        // Textures\r\n        if (scene.texturesEnabled) {\r\n            if (this._texture && MaterialFlags.IridescenceTextureEnabled) {\r\n                uniformBuffer.setTexture(\"iridescenceSampler\", this._texture);\r\n            }\r\n\r\n            if (this._thicknessTexture && !identicalTextures && !defines.IRIDESCENCE_USE_THICKNESS_FROM_MAINTEXTURE && MaterialFlags.IridescenceTextureEnabled) {\r\n                uniformBuffer.setTexture(\"iridescenceThicknessSampler\", this._thicknessTexture);\r\n            }\r\n        }\r\n    }\r\n\r\n    public hasTexture(texture: BaseTexture): boolean {\r\n        if (this._texture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._thicknessTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public getActiveTextures(activeTextures: BaseTexture[]): void {\r\n        if (this._texture) {\r\n            activeTextures.push(this._texture);\r\n        }\r\n\r\n        if (this._thicknessTexture) {\r\n            activeTextures.push(this._thicknessTexture);\r\n        }\r\n    }\r\n\r\n    public getAnimatables(animatables: IAnimatable[]): void {\r\n        if (this._texture && this._texture.animations && this._texture.animations.length > 0) {\r\n            animatables.push(this._texture);\r\n        }\r\n\r\n        if (this._thicknessTexture && this._thicknessTexture.animations && this._thicknessTexture.animations.length > 0) {\r\n            animatables.push(this._thicknessTexture);\r\n        }\r\n    }\r\n\r\n    public dispose(forceDisposeTextures?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            this._texture?.dispose();\r\n            this._thicknessTexture?.dispose();\r\n        }\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"PBRIridescenceConfiguration\";\r\n    }\r\n\r\n    public addFallbacks(defines: MaterialIridescenceDefines, fallbacks: EffectFallbacks, currentRank: number): number {\r\n        if (defines.IRIDESCENCE) {\r\n            fallbacks.addFallback(currentRank++, \"IRIDESCENCE\");\r\n        }\r\n        return currentRank;\r\n    }\r\n\r\n    public getSamplers(samplers: string[]): void {\r\n        samplers.push(\"iridescenceSampler\", \"iridescenceThicknessSampler\");\r\n    }\r\n\r\n    public getUniforms(): { ubo?: Array<{ name: string; size: number; type: string }>; vertex?: string; fragment?: string } {\r\n        return {\r\n            ubo: [\r\n                { name: \"vIridescenceParams\", size: 4, type: \"vec4\" },\r\n                { name: \"vIridescenceInfos\", size: 4, type: \"vec4\" },\r\n                { name: \"iridescenceMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"iridescenceThicknessMatrix\", size: 16, type: \"mat4\" },\r\n            ],\r\n        };\r\n    }\r\n}\r\n"]}