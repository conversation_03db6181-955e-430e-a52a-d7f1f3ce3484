{"version": 3, "file": "videoDome.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Helpers/videoDome.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAExD,OAAO,EAAE,YAAY,EAAE,MAAM,oCAAoC,CAAC;AAClE,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAI5D;;;;;GAKG;AACH,MAAM,OAAO,SAAU,SAAQ,WAAyB;IAcpD;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IACD;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;;OAGG;IACH,IAAW,SAAS,CAAC,KAAa;QAC9B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAKS,YAAY,CAAC,aAAmD,EAAE,KAAY,EAAE,OAAY;QAClG,MAAM,WAAW,GAAyB,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,iBAAiB,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC;QAC9I,MAAM,OAAO,GAAG,IAAI,YAAY,CAC5B,CAAC,IAAI,CAAC,IAAI,IAAI,WAAW,CAAC,GAAG,UAAU,EACvC,aAAa,EACb,KAAK,EACL,OAAO,CAAC,eAAe,EACvB,IAAI,CAAC,iBAAiB,EACtB,OAAO,CAAC,sBAAsB,EAC9B,WAAW,CACd,CAAC;QACF,yBAAyB;QACzB,IAAI,OAAO,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC3D,IAAI,CAAC,QAAQ,EAAE,UAAU,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC1E,CAAC,EAAE,iBAAiB,CAAC,WAAW,CAAC,CAAC;SACrC;QACD,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE;YACtD,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,YAAsB,EAAE,0BAA0B,GAAG,KAAK;QACrE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC7D,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC9D,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,0BAA0B,CAAC,CAAC;IAC5D,CAAC;;AApED;;GAEG;AACoB,yBAAe,GAAG,WAAW,CAAC,eAAe,CAAC;AACrE;;GAEG;AACoB,wBAAc,GAAG,WAAW,CAAC,cAAc,CAAC;AACnE;;GAEG;AACoB,yBAAe,GAAG,WAAW,CAAC,eAAe,CAAC", "sourcesContent": ["import type { Scene } from \"../scene\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport type { VideoTextureSettings } from \"../Materials/Textures/videoTexture\";\r\nimport { VideoTexture } from \"../Materials/Textures/videoTexture\";\r\nimport { TextureDome } from \"./textureDome\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../Events/pointerEvents\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Observer } from \"../Misc/observable\";\r\n\r\n/**\r\n * Display a 360/180 degree video on an approximately spherical surface, useful for VR applications or skyboxes.\r\n * As a subclass of TransformNode, this allow parenting to the camera or multiple videos with different locations in the scene.\r\n * This class achieves its effect with a VideoTexture and a correctly configured BackgroundMaterial on an inverted sphere.\r\n * Potential additions to this helper include zoom and and non-infinite distance rendering effects.\r\n */\r\nexport class VideoDome extends TextureDome<VideoTexture> {\r\n    /**\r\n     * Define the video source as a Monoscopic panoramic 360 video.\r\n     */\r\n    public static readonly MODE_MONOSCOPIC = TextureDome.MODE_MONOSCOPIC;\r\n    /**\r\n     * Define the video source as a Stereoscopic TopBottom/OverUnder panoramic 360 video.\r\n     */\r\n    public static readonly MODE_TOPBOTTOM = TextureDome.MODE_TOPBOTTOM;\r\n    /**\r\n     * Define the video source as a Stereoscopic Side by Side panoramic 360 video.\r\n     */\r\n    public static readonly MODE_SIDEBYSIDE = TextureDome.MODE_SIDEBYSIDE;\r\n\r\n    /**\r\n     * Get the video texture associated with this video dome\r\n     */\r\n    public get videoTexture(): VideoTexture {\r\n        return this._texture;\r\n    }\r\n    /**\r\n     * Get the video mode of this dome\r\n     */\r\n    public get videoMode(): number {\r\n        return this.textureMode;\r\n    }\r\n    /**\r\n     * Set the video mode of this dome.\r\n     * @see textureMode\r\n     */\r\n    public set videoMode(value: number) {\r\n        this.textureMode = value;\r\n    }\r\n\r\n    private _pointerObserver: Nullable<Observer<PointerInfo>>;\r\n    private _textureObserver: Nullable<Observer<Texture>>;\r\n\r\n    protected _initTexture(urlsOrElement: string | string[] | HTMLVideoElement, scene: Scene, options: any): VideoTexture {\r\n        const tempOptions: VideoTextureSettings = { loop: options.loop, autoPlay: options.autoPlay, autoUpdateTexture: true, poster: options.poster };\r\n        const texture = new VideoTexture(\r\n            (this.name || \"videoDome\") + \"_texture\",\r\n            urlsOrElement,\r\n            scene,\r\n            options.generateMipMaps,\r\n            this._useDirectMapping,\r\n            Texture.TRILINEAR_SAMPLINGMODE,\r\n            tempOptions\r\n        );\r\n        // optional configuration\r\n        if (options.clickToPlay) {\r\n            this._pointerObserver = scene.onPointerObservable.add((data) => {\r\n                data.pickInfo?.pickedMesh === this.mesh && this._texture.video.play();\r\n            }, PointerEventTypes.POINTERDOWN);\r\n        }\r\n        this._textureObserver = texture.onLoadObservable.add(() => {\r\n            this.onLoadObservable.notifyObservers();\r\n        });\r\n        return texture;\r\n    }\r\n\r\n    /**\r\n     * Releases resources associated with this node.\r\n     * @param doNotRecurse Set to true to not recurse into each children (recurse into each children by default)\r\n     * @param disposeMaterialAndTextures Set to true to also dispose referenced materials and textures (false by default)\r\n     */\r\n    public dispose(doNotRecurse?: boolean, disposeMaterialAndTextures = false): void {\r\n        this._texture.onLoadObservable.remove(this._textureObserver);\r\n        this._scene.onPointerObservable.remove(this._pointerObserver);\r\n        super.dispose(doNotRecurse, disposeMaterialAndTextures);\r\n    }\r\n}\r\n"]}