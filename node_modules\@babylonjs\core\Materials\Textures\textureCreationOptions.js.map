{"version": 3, "file": "textureCreationOptions.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/textureCreationOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { InternalTexture } from \"./internalTexture\";\r\n\r\n/**\r\n * Define options used to create an internal texture\r\n */\r\nexport interface InternalTextureCreationOptions {\r\n    /**\r\n     * Specifies if mipmaps must be created. If undefined, the value from generateMipMaps is taken instead\r\n     */\r\n    createMipMaps?: boolean;\r\n    /**\r\n     * Specifies if mipmaps must be generated\r\n     */\r\n    generateMipMaps?: boolean;\r\n    /** Defines texture type (int by default) */\r\n    type?: number;\r\n    /** Defines sampling mode (trilinear by default) */\r\n    samplingMode?: number;\r\n    /** Defines format (RGBA by default) */\r\n    format?: number;\r\n    /** Defines sample count (1 by default) */\r\n    samples?: number;\r\n    /** Texture creation flags */\r\n    creationFlags?: number;\r\n    /** Creates the RTT in sRGB space */\r\n    useSRGBBuffer?: boolean;\r\n    /** Label of the texture (used for debugging only) */\r\n    label?: string;\r\n}\r\n\r\n/**\r\n * Define options used to create a render target texture\r\n */\r\nexport interface RenderTargetCreationOptions extends InternalTextureCreationOptions {\r\n    /** Specifies whether or not a depth should be allocated in the texture (true by default) */\r\n    generateDepthBuffer?: boolean;\r\n    /** Specifies whether or not a stencil should be allocated in the texture (false by default)*/\r\n    generateStencilBuffer?: boolean;\r\n    /** Specifies that no color target should be bound to the render target (useful if you only want to write to the depth buffer, for eg) */\r\n    noColorAttachment?: boolean;\r\n    /** Specifies the internal texture to use directly instead of creating one (ignores `noColorAttachment` flag when set) **/\r\n    colorAttachment?: InternalTexture;\r\n}\r\n\r\n/**\r\n * Define options used to create a depth texture\r\n */\r\nexport interface DepthTextureCreationOptions {\r\n    /** Specifies whether or not a stencil should be allocated in the texture */\r\n    generateStencil?: boolean;\r\n    /** Specifies whether or not bilinear filtering is enable on the texture */\r\n    bilinearFiltering?: boolean;\r\n    /** Specifies the comparison function to set on the texture. If 0 or undefined, the texture is not in comparison mode */\r\n    comparisonFunction?: number;\r\n    /** Specifies if the created texture is a cube texture */\r\n    isCube?: boolean;\r\n    /** Specifies the sample count of the depth/stencil texture texture */\r\n    samples?: number;\r\n    /** Specifies the depth texture format to use */\r\n    depthTextureFormat?: number;\r\n    /** Label of the texture (used for debugging only) */\r\n    label?: string;\r\n}\r\n\r\n/**\r\n * Type used to define a texture size (either with a number or with a rect width and height)\r\n */\r\nexport type TextureSize = number | { width: number; height: number; layers?: number };\r\n"]}