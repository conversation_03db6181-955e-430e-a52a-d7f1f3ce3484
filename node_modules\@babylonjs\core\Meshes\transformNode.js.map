{"version": 3, "file": "transformNode.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Meshes/transformNode.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,MAAM,oBAAoB,CAAC;AAC1F,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AACvE,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAIhD,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAC/E,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAG/B,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAC3C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,MAAM,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;AAEjH;;;GAGG;AACH,MAAM,OAAO,aAAc,SAAQ,IAAI;IA0DnC;;;;;;;;;;;OAWG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,KAAa;QAClC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;YAC/B,OAAO;SACV;QACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,MAAM,CAAC,oBAAoB,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;QAC1G,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAGD;;;OAGG;IACH,IAAW,kCAAkC;QACzC,OAAO,IAAI,CAAC,mCAAmC,CAAC;IACpD,CAAC;IAED,IAAW,kCAAkC,CAAC,KAAc;QACxD,IAAI,KAAK,KAAK,IAAI,CAAC,mCAAmC,EAAE;YACpD,OAAO;SACV;QACD,IAAI,CAAC,mCAAmC,GAAG,KAAK,CAAC;QACjD,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAEO,wBAAwB;QAC5B,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,KAAK,aAAa,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,kCAAkC,CAAC;IACxI,CAAC;IAWD;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED,IAAW,gBAAgB,CAAC,KAAc;QACtC,IAAI,IAAI,CAAC,iBAAiB,KAAK,KAAK,EAAE;YAClC,OAAO;SACV;QAED,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnC,CAAC;IAwCD,YAAY,IAAY,EAAE,QAAyB,IAAI,EAAE,MAAM,GAAG,IAAI;QAClE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QApIf,aAAQ,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAChC,QAAG,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,WAAM,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEtC,aAAa;QAEL,cAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAG3B,cAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAG3B,wBAAmB,GAAyB,IAAI,CAAC;QAG/C,aAAQ,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;QAC3B,4BAAuB,GAA4B,IAAI,CAAC;QAExD,sBAAiB,GAAG,KAAK,CAAC;QAG1B,mBAAc,GAAG,aAAa,CAAC,kBAAkB,CAAC;QA2BlD,wCAAmC,GAAG,KAAK,CAAC;QAqBpD;;WAEG;QAEI,uBAAkB,GAAG,CAAC,CAAC;QAGtB,sBAAiB,GAAG,KAAK,CAAC;QAiBlC;;;WAGG;QAEI,4BAAuB,GAAG,KAAK,CAAC;QAEvC;;WAEG;QAEI,8CAAyC,GAAG,KAAK,CAAC;QAEzD,QAAQ;QACR,gBAAgB;QACT,gBAAW,GAAqB,IAAI,CAAC;QAC5C,gBAAgB;QACT,iBAAY,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAE5B,oBAAe,GAAG,KAAK,CAAC;QACxB,sBAAiB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACnC,qBAAgB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,gCAA2B,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QACpD,iBAAY,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAEzC,gBAAgB;QACT,6BAAwB,GAAG,KAAK,CAAC;QAE9B,yBAAoB,GAAG,KAAK,CAAC;QAEvC,gBAAgB;QACT,qCAAgC,GAAG,CAAC,CAAC,CAAC;QAE7C;;WAEG;QACI,uCAAkC,GAAG,IAAI,UAAU,EAAiB,CAAC;QAsoBpE,uBAAkB,GAAG,KAAK,CAAC;QAjoB/B,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,QAAQ,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;SAC1C;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,WAAoB;QACpC,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,8BAA8B;QACjC,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED;;;OAGG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,WAAoB;QACpC,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC;QAC7B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,UAAmB;QAClC,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED,IAAW,kBAAkB,CAAC,UAAgC;QAC1D,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC;QACtC,4BAA4B;QAC5B,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SAC9B;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,CAAC,8BAA8B,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtI,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAW,EAAE;QACT,OAAO,CAAC,8BAA8B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACjF,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACpI,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;IACnC,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,MAAc;QAClC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;SACf;QACD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;SACxC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,gBAAgB;IACT,eAAe;QAClB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,cAAc,KAAK,aAAa,CAAC,kBAAkB,EAAE;YACzG,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,KAAK,CAAC,kBAAkB,EAAE;YAC1B,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;YACzB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;YACxB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;YAC5F,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gBAAgB;IACT,UAAU;QACb,KAAK,CAAC,UAAU,EAAE,CAAC;QAEnB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC;QACjC,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;QACzB,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC/B,KAAK,CAAC,oBAAoB,GAAG,KAAK,CAAC;QACnC,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACtC,CAAC;IAED;;;OAGG;IACH,IAAW,eAAe;QACtB,IAAI,CAAC,+BAA+B,EAAE,CAAC;QACvC,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,IAAW,0BAA0B;QACjC,IAAI,CAAC,+BAA+B,EAAE,CAAC;QACvC,OAAO,IAAI,CAAC,2BAA2B,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,MAAc;QACvC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,MAA6B,EAAE,uBAAuB,GAAG,IAAI;QAC/E,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACnC,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;QAEvD,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACtC,IAAI,CAAC,wBAAwB,GAAG,uBAAuB,CAAC;QAExD,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3B,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aAC/D;iBAAM;gBACH,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;aAC3D;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;;;;OAOG;IACI,oBAAoB,CACvB,YAAqC,IAAI,EACzC,OAA4E,EAC5E,gBAAwE;QAExE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAE/F,IAAI,KAAK,EAAE;YACP,IAAI,gBAAgB,EAAE;gBAClB,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aACjC;SACJ;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;YACnD,KAAK,CAAC,oBAAoB,CAAC,KAAK,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;SAChE;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,iBAAmC,IAAI,EAAE,SAAS,GAAG,KAAK;QAC/E,IAAI,cAAc,EAAE;YAChB,IAAI,SAAS,EAAE;gBACX,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACzB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;gBAC7E,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBAClF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aACjC;iBAAM;gBACH,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC;gBACnC,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACjH,IAAI,CAAC,wBAAwB,EAAE,CAAC;aACnC;SACJ;aAAM;YACH,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAC,mEAAmE;YACtG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;SACjC;QACD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACtB,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,gBAAyB;QAChD,IAAI,CAAC,gBAAgB,EAAE;YACnB,OAAO,IAAI,CAAC;SACf;QACD,IAAI,iBAAiB,CAAC;QACtB,IAAI,iBAAiB,CAAC;QACtB,IAAI,iBAAiB,CAAC;QACtB,IAAI,gBAAgB,CAAC,CAAC,KAAK,SAAS,EAAE;YAClC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,OAAO,IAAI,CAAC;aACf;YACD,iBAAiB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACjC,iBAAiB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACjC,iBAAiB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;SACpC;aAAM;YACH,iBAAiB,GAAG,gBAAgB,CAAC,CAAC,CAAC;YACvC,iBAAiB,GAAG,gBAAgB,CAAC,CAAC,CAAC;YACvC,iBAAiB,GAAG,gBAAgB,CAAC,CAAC,CAAC;SAC1C;QACD,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,MAAM,uBAAuB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC;YAClE,OAAO,CAAC,mCAAmC,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,uBAAuB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SAChJ;aAAM;YACH,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,iBAAiB,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,iBAAiB,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,iBAAiB,CAAC;SACvC;QAED,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,0BAA0B,CAAC,OAAgB;QAC9C,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,gCAAgC;QACnC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,MAAM,mBAAmB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;QACnD,OAAO,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC;IACvE,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,OAAgB;QACpC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC;IAChB,CAAC;IAID;;;;;;;;OAQG;IACI,MAAM,CAAC,WAAoB,EAAE,SAAiB,CAAC,EAAE,WAAmB,CAAC,EAAE,UAAkB,CAAC,EAAE,QAAe,KAAK,CAAC,KAAK;QACzH,MAAM,EAAE,GAAG,aAAa,CAAC,kBAAkB,CAAC;QAC5C,MAAM,GAAG,GAAG,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC/E,WAAW,CAAC,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QACnC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAEjD,uCAAuC;QACvC,IAAI,KAAK,KAAK,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YACtC,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,kDAAkD;gBAClD,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC5C,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;gBAEzD,iFAAiF;gBACjF,MAAM,oBAAoB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAClD,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAC;gBAC1E,oBAAoB,CAAC,MAAM,EAAE,CAAC;gBAC9B,cAAc,CAAC,aAAa,CAAC,oBAAoB,EAAE,cAAc,CAAC,CAAC;gBACnE,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;aAC9D;iBAAM;gBACH,kDAAkD;gBAClD,MAAM,kBAAkB,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACpD,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;gBACnE,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC5C,kBAAkB,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;gBAEpD,iFAAiF;gBACjF,MAAM,oBAAoB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAClD,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAC;gBAC1E,oBAAoB,CAAC,MAAM,EAAE,CAAC;gBAC9B,cAAc,CAAC,aAAa,CAAC,oBAAoB,EAAE,cAAc,CAAC,CAAC;gBACnE,kBAAkB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;gBACtD,kBAAkB,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACxD;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,SAAkB;QAClC,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAE1C,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;;OAOG;IACI,iBAAiB,CAAC,SAAkB,EAAE,MAAe;QACxD,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,CAAC;QACvE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACI,YAAY,CAAC,SAAkB,EAAE,SAAiB,CAAC,EAAE,WAAmB,CAAC,EAAE,UAAkB,CAAC;QACjG,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAChE,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAC7E,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC5C,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,UAAU,CAAC,yBAAyB,CAAC,GAAG,GAAG,MAAM,EAAE,KAAK,GAAG,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC1G;aAAM;YACH,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,QAAQ,CAAC;YACnC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC;YAC/B,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC;SAC7B;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,KAAc,EAAE,QAAe,KAAK,CAAC,KAAK;QAC3D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE;YACpC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;SACjC;QAED,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEjC,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;YACtB,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAClC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACrB,KAAK,GAAG,OAAO,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SACrD;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IACvF,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC/B,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,MAAe;QACrC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACpC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACpC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,qBAAqB;QACxB,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;QACvC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,0BAA0B,CAAC,MAAe;QAC7C,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAChC,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,QAAiB;QAChC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO,IAAI,CAAC;SACf;QAED,4CAA4C;QAC5C,sEAAsE;QACtE,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;aAC/B;SACJ;QACD,OAAO,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;;;;;;OAWG;IACI,SAAS,CAAC,IAAoB,EAAE,sBAA+B,KAAK,EAAE,WAAW,GAAG,KAAK;QAC5F,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACvB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC9C,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QACtC,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAE9B,IAAI,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAC9C,IAAI,CAAC,eAAe,EAAE;YAClB,eAAe,GAAG,aAAa,CAAC,YAAY,CAAC;YAC7C,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;SAC/G;QAED,8CAA8C;QAC9C,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAClF,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,cAAc,CAAC,CAAC;SACtF;QAED,wEAAwE;QACxE,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;YAC3D,cAAc,CAAC,aAAa,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;SACjE;QACD,cAAc,CAAC,SAAS,CAAC,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAEhG,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;SAClD;aAAM;YACH,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAEnB,IAAI,WAAW,EAAE;YACb,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC1C;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAGD;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,6BAA6B,CAAC,KAAc;QAC/C,IAAI,IAAI,CAAC,kBAAkB,KAAK,KAAK,EAAE;YACnC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,IAAU,EAAE,qBAAoC;QAChE,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC,MAAM,CAAC;QACrD,IAAI,CAAC,uBAAuB,GAAG,qBAAqB,CAAC;QACrD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAEnB,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,gDAAgD;QAElF,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;YACzC,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC,CAAC;SACjC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,qBAAqB,GAAG,KAAK;QAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,IAAI,qBAAqB,EAAE;gBACvB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iCAAiC,CAAC;aACxD;YACD,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;YAChD,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC,CAAC;SACjC;QACD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACpC,IAAI,qBAAqB,EAAE;YACvB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iCAAiC,CAAC;SACxD;aAAM;YACH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACtB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAGD;;;;;;;;;OASG;IACI,MAAM,CAAC,IAAa,EAAE,MAAc,EAAE,KAAa;QACtD,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YACvD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAC3B;QACD,IAAI,kBAA8B,CAAC;QACnC,IAAI,CAAC,KAAK,IAAK,KAAa,KAAK,KAAK,CAAC,KAAK,EAAE;YAC1C,kBAAkB,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,kBAAkB,CAAC,CAAC;YAClG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACtF;aAAM;YACH,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;gBACvD,MAAM,uBAAuB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACrD,iBAAiB,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC;gBACvD,IAAI,GAAG,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;gBAE9D,IAAI,iBAAiB,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;oBACrC,MAAM,IAAI,CAAC,CAAC,CAAC;iBAChB;aACJ;YACD,kBAAkB,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,kBAAkB,CAAC,CAAC;YAClG,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACtF;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG;IACI,YAAY,CAAC,KAAc,EAAE,IAAa,EAAE,MAAc;QAC7D,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC7G,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAC3B;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACxC,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,gBAAgB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE/C,MAAM,aAAa,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAE/C,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;QACpD,MAAM,oBAAoB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;QACxD,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;QACjD,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa;QAEvD,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAC9C,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI;QACvF,MAAM,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,oBAAoB,CAAC,CAAC,CAAC,KAAK;QAC9F,MAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI;QAE5D,oBAAoB,CAAC,aAAa,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS;QAC1E,WAAW,CAAC,aAAa,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC,CAAC,aAAa;QAExE,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;QAEnE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;QAC3C,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAE9E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACI,SAAS,CAAC,IAAa,EAAE,QAAgB,EAAE,KAAa;QAC3D,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,CAAC,KAAK,IAAK,KAAa,KAAK,KAAK,CAAC,KAAK,EAAE;YAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,gCAAgC,EAAE,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAC/E,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;SAC3C;aAAM;YACH,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC;SAChF;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACI,WAAW,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QAC9C,IAAI,kBAAkB,CAAC;QACvB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;SAChD;aAAM;YACH,kBAAkB,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC9C,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;SAC/G;QACD,MAAM,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC9C,UAAU,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;QAC5D,kBAAkB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,kBAAkB,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACxD;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACO,mBAAmB;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACI,4BAA4B;QAC/B,OAAO,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,aAAa,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;IAC9J,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,QAAiB,KAAK,EAAE,SAA2B,IAAI;QAC7E,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC7C,OAAO,IAAI,CAAC,YAAY,CAAC;SAC5B;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC;QACtD,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,gBAAgB,KAAK,eAAe,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE;YAClG,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;YACxC,OAAO,IAAI,CAAC,YAAY,CAAC;SAC5B;QAED,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC;QAEhD,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC;QACjC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC/C,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;QAEhC,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE1C,UAAU;QACV,MAAM,OAAO,GAAY,aAAa,CAAC,WAAW,CAAC;QACnD,IAAI,WAAW,GAAY,IAAI,CAAC,SAAS,CAAC;QAE1C,cAAc;QACd,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,EAAE;gBACxB,MAAM,iBAAiB,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;gBAClD,MAAM,oBAAoB,GAAG,IAAI,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEpH,WAAW,GAAG,aAAa,CAAC,eAAe,CAAC;gBAC5C,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;aAC/J;SACJ;QAED,UAAU;QACV,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAExJ,WAAW;QACX,IAAI,QAAoB,CAAC;QACzB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,QAAQ,GAAG,KAAK,CAAC;YAC1C,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACpC,IAAI,IAAI,CAAC,yCAAyC,EAAE;gBAChD,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;gBAC1C,IAAI,GAAG,EAAE;oBACL,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC1C;aACJ;SACJ;aAAM;YACH,QAAQ,GAAG,aAAa,CAAC,YAAY,CAAC;YACtC,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SACxG;QAED,UAAU;QACV,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;YAElE,WAAW;YACX,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5C,QAAQ,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YAE1C,4BAA4B;YAC5B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,WAAW,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACnE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAEtE,uCAAuC;YACvC,IAAI,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;aAChF;YAED,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;SAC3F;aAAM;YACH,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;SAC1E;QAED,SAAS;QACT,IAAI,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE;YACjC,IAAI,KAAK,EAAE;gBACP,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;aACpC;YACD,IAAI,KAAK,CAAC,gBAAgB,EAAE;gBACxB,IAAI,IAAI,CAAC,uBAAuB,EAAE;oBAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAc,CAAC;oBACjC,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,CAAC;oBAC7B,IAAI,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC5G;qBAAM;oBACH,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;iBAC1D;gBAED,8CAA8C;gBAC9C,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC1C,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACpC,MAAM,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC7C,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;gBAChE,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;gBAEjD,IAAI,aAAa,CAAC,6BAA6B,EAAE;oBAC7C,8EAA8E;oBAC9E,IAAI,CAAC,SAAS,CAAC,4BAA4B,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;oBACtE,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;iBACjD;gBAED,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;aAC5E;iBAAM;gBACH,IAAI,IAAI,CAAC,uBAAuB,EAAE;oBAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAc,CAAC;oBACjC,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,CAAC;oBAC7B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7E,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;iBACxG;qBAAM;oBACH,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;iBAC/E;aACJ;YACD,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAChC;aAAM;YACH,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACjD;QAED,sGAAsG;QACtG,IAAI,KAAK,CAAC,gBAAgB,IAAI,MAAM,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE;YACvF,MAAM,iBAAiB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,CAAC,mBAAmB;YAE7E,yBAAyB;YACzB,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;YAEtD,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBAClC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,eAAe,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7E;YAED,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvD,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAEvD,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,iBAAiB,CAAC,KAAK,aAAa,CAAC,iBAAiB,EAAE;gBAC5F,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;gBAC/E,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC1C,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;gBAEzD,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,eAAe,CAAC,KAAK,aAAa,CAAC,eAAe,EAAE;oBACxF,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;iBACrB;gBAED,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,eAAe,CAAC,KAAK,aAAa,CAAC,eAAe,EAAE;oBACxF,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;iBACrB;gBAED,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,eAAe,CAAC,KAAK,aAAa,CAAC,eAAe,EAAE;oBACxF,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;iBACrB;gBAED,MAAM,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aACvG;YACD,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACpD,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAEzE,sBAAsB;YACtB,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3D;QACD,wCAAwC;aACnC,IAAI,KAAK,CAAC,gBAAgB,IAAI,MAAM,IAAI,KAAK,CAAC,oBAAoB,EAAE;YACrE,MAAM,iBAAiB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAChD,mBAAmB;YACnB,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;YAEzD,yCAAyC;YACzC,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;YAC7C,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC5C,OAAO,CAAC,yBAAyB,CAAC,cAAc,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;YACvF,aAAa,CAAC,SAAS,EAAE,CAAC;YAE1B,sCAAsC;YACtC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;YACxE,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAC7F,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAChD,UAAU,CAAC,yBAAyB,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YAE9E,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,iBAAiB,CAAC,KAAK,aAAa,CAAC,iBAAiB,EAAE;gBAC5F,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC1C,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;gBAEzD,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,eAAe,CAAC,KAAK,aAAa,CAAC,eAAe,EAAE;oBACxF,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;iBACrB;gBAED,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,eAAe,CAAC,KAAK,aAAa,CAAC,eAAe,EAAE;oBACxF,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;iBACrB;gBAED,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,eAAe,CAAC,KAAK,aAAa,CAAC,eAAe,EAAE;oBACxF,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;iBACrB;gBAED,MAAM,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aACvG;iBAAM;gBACH,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9E;YAED,qBAAqB;YACrB,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAEpD,yDAAyD;YACzD,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAEzE,sBAAsB;YACtB,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3D;QAED,gBAAgB;QAChB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,QAAQ,CAAC,EAAE;gBACnD,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC;aAC5C;iBAAM,IAAI,MAAM,IAAoB,MAAO,CAAC,kBAAkB,EAAE;gBAC7D,IAAI,CAAC,6BAA6B,CAAiB,MAAO,CAAC,kBAAkB,CAAC,CAAC;aAClF;iBAAM;gBACH,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;aAC7C;SACJ;aAAM;YACH,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEhC,oBAAoB;QACpB,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjH,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAE/B,YAAY;QACZ,IAAI,CAAC,kCAAkC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE9D,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACvD;QAED,wBAAwB;QACxB,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC;QAE3C,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,wBAAiC,IAAI;QACzD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,qBAAqB,EAAE;YACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACtC,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAkB,CAAC;gBAC3C,IAAI,KAAK,EAAE;oBACP,KAAK,CAAC,kBAAkB,EAAE,CAAC;oBAC3B,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACzC,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;oBACjE,MAAM,qBAAqB,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBACvD,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,qBAAqB,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;oBAC5E,IAAI,KAAK,CAAC,kBAAkB,EAAE;wBAC1B,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;qBAC5D;yBAAM;wBACH,qBAAqB,CAAC,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;qBAC5D;iBACJ;aACJ;SACJ;QACD,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEtC,mCAAmC;QACnC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;SACnD;QACD,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;IAC1C,CAAC;IAES,wBAAwB,KAAU,CAAC;IAE7C;;;;;OAKG;IACI,8BAA8B,CAAC,IAAmC;QACrE,IAAI,CAAC,kCAAkC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,gCAAgC,CAAC,IAAmC;QACvE,IAAI,CAAC,kCAAkC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAAC,SAA2B,IAAI;QAC3D,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,GAAW,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC;SACjD;QAED,OAAO,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;IAC5F,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,SAA2B,IAAI;QACtD,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,GAAW,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC;SACjD;QACD,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,EAAE,CAAC;IAC/E,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,IAAY,EAAE,SAAyB,EAAE,kBAA4B;QAC9E,MAAM,MAAM,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAE/F,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC;QAEjB,IAAI,SAAS,EAAE;YACX,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;SAC7B;QAED,IAAI,CAAC,kBAAkB,EAAE;YACrB,WAAW;YACX,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC3D,MAAM,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAEvC,IAAU,KAAM,CAAC,KAAK,EAAE;oBACd,KAAM,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;iBACvD;aACJ;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,0BAAgC;QAC7C,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,0BAA0B,CAAC,CAAC;QAC5F,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAC/C,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE7C,SAAS;QACT,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;SACvD;QAED,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,CAAC;QAElE,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjD,aAAa;QACb,mBAAmB,CAAC,0BAA0B,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;QAC1E,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAE7D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED,UAAU;IACV;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,MAAM,aAAa,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,aAAa,CAAC,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAE/I,IAAI,mBAAmB,CAAC,WAAW,EAAE;YACjC,aAAa,CAAC,qBAAqB,CAAC,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC;SAC1F;aAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE;YACxC,aAAa,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC;SACnF;QAED,aAAa,CAAC,UAAU,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAExD,aAAa,CAAC,sBAAsB,GAAG,mBAAmB,CAAC,QAAQ,CAAC;QAEpE,SAAS;QACT,IAAI,mBAAmB,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC5C,aAAa,CAAC,gBAAgB,GAAG,mBAAmB,CAAC,QAAQ,CAAC;SACjE;QAED,IAAI,mBAAmB,CAAC,mBAAmB,KAAK,SAAS,EAAE;YACvD,aAAa,CAAC,2BAA2B,GAAG,mBAAmB,CAAC,mBAAmB,CAAC;SACvF;QAED,aAAa;QACb,IAAI,mBAAmB,CAAC,UAAU,EAAE;YAChC,KAAK,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,mBAAmB,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,EAAE,EAAE;gBACnG,MAAM,eAAe,GAAG,mBAAmB,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;gBACvE,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAAmB,CAAC,CAAC;gBACpD,IAAI,aAAa,EAAE;oBACf,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;iBACvE;aACJ;YACD,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;SACxE;QAED,IAAI,mBAAmB,CAAC,WAAW,EAAE;YACjC,KAAK,CAAC,cAAc,CAChB,aAAa,EACb,mBAAmB,CAAC,eAAe,EACnC,mBAAmB,CAAC,aAAa,EACjC,mBAAmB,CAAC,eAAe,EACnC,mBAAmB,CAAC,gBAAgB,IAAI,GAAG,CAC9C,CAAC;SACL;QAED,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACI,sBAAsB,CAAC,qBAA+B,EAAE,SAAmC;QAC9F,MAAM,OAAO,GAAyB,EAAE,CAAC;QACzC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,qBAAqB,EAAE,CAAC,IAAU,EAAE,EAAE;YAChE,OAAO,CAAC,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,YAAY,aAAa,CAAC;QAC5E,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,YAAsB,EAAE,0BAA0B,GAAG,KAAK;QACrE,aAAa;QACb,IAAI,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAEpC,oBAAoB;QACpB,IAAI,CAAC,QAAQ,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACjE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACZ,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACzD;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;QAED,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,CAAC;QAEhD,IAAI,YAAY,EAAE;YACd,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YACzD,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE;gBACxC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;gBAC5B,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC1C;SACJ;QAED,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,0BAA0B,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;OAMG;IACI,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,EAAE,cAAc,GAAG,KAAK,EAAE,SAAqD;QAC/H,IAAI,cAAc,GAAsB,IAAI,CAAC;QAC7C,IAAI,wBAAwB,GAAyB,IAAI,CAAC;QAE1D,IAAI,cAAc,EAAE;YAChB,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;gBAC3D,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACtD;iBAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACtB,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACvC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACzC;SACJ;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;QACxF,MAAM,OAAO,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAClE,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;QAE/D,IAAI,YAAY,KAAK,CAAC,EAAE;YACpB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,KAAK,GAAG,CAAC,GAAG,YAAY,CAAC;QAE/B,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEjC,IAAI,cAAc,EAAE;YAChB,IAAI,IAAI,CAAC,kBAAkB,IAAI,wBAAwB,EAAE;gBACrD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;aAC9D;iBAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,cAAc,EAAE;gBACxC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;aAC1C;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,+BAA+B;QACnC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACrF,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACjC;IACL,CAAC;;AAtiDD,UAAU;AACV;;GAEG;AACW,gCAAkB,GAAG,CAAC,AAAJ,CAAK;AACrC;;GAEG;AACW,6BAAe,GAAG,CAAC,AAAJ,CAAK;AAClC;;GAEG;AACW,6BAAe,GAAG,CAAC,AAAJ,CAAK;AAClC;;GAEG;AACW,6BAAe,GAAG,CAAC,AAAJ,CAAK;AAClC;;GAEG;AACW,+BAAiB,GAAG,CAAC,AAAJ,CAAK;AACpC;;GAEG;AACW,wCAA0B,GAAG,GAAG,AAAN,CAAO;AAC/C;;GAEG;AACW,2CAA6B,GAAY,KAAK,AAAjB,CAAkB;AAE9C,0BAAY,GAAG,UAAU,CAAC,IAAI,EAAE,AAApB,CAAqB;AACjC,yBAAW,GAAG,OAAO,CAAC,IAAI,EAAE,AAAjB,CAAkB;AAC7B,6BAAe,GAAG,OAAO,CAAC,IAAI,EAAE,AAAjB,CAAkB;AAmhBjC,gCAAkB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,AAAvB,CAAwB;AAsT1C,gCAAkB,GAAG,IAAI,UAAU,EAAE,AAAnB,CAAoB;AAj0B7C;IADP,kBAAkB,CAAC,UAAU,CAAC;gDACI;AAG3B;IADP,kBAAkB,CAAC,UAAU,CAAC;gDACI;AAG3B;IADP,qBAAqB,CAAC,oBAAoB,CAAC;0DACa;AAG/C;IADT,kBAAkB,CAAC,SAAS,CAAC;+CACK;AAM3B;IADP,SAAS,CAAC,eAAe,CAAC;qDAC+B;AAoDnD;IADN,SAAS,EAAE;yDACkB;AAGtB;IADP,SAAS,CAAC,kBAAkB,CAAC;wDACI;AAsB3B;IADN,SAAS,EAAE;8DAC2B;AAMhC;IADN,SAAS,EAAE;gFAC6C", "sourcesContent": ["import type { DeepImmutable, Nullable } from \"../types\";\r\nimport { serialize, serializeAsVector3, serializeAsQuaternion } from \"../Misc/decorators\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\nimport { Observable } from \"../Misc/observable\";\r\n\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Quaternion, Matrix, Vector3, TmpVectors } from \"../Maths/math.vector\";\r\nimport { Node } from \"../node\";\r\nimport type { Bone } from \"../Bones/bone\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Space } from \"../Maths/math.axis\";\r\nimport { GetClass } from \"../Misc/typeStore\";\r\n\r\nconst convertRHSToLHS = Matrix.Compose(Vector3.One(), Quaternion.FromEulerAngles(0, Math.PI, 0), Vector3.Zero());\r\n\r\n/**\r\n * A TransformNode is an object that is not rendered but can be used as a center of transformation. This can decrease memory usage and increase rendering speed compared to using an empty mesh as a parent and is less complicated than using a pivot matrix.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/transforms/parent_pivot/transform_node\r\n */\r\nexport class TransformNode extends Node {\r\n    // Statics\r\n    /**\r\n     * Object will not rotate to face the camera\r\n     */\r\n    public static BILLBOARDMODE_NONE = 0;\r\n    /**\r\n     * Object will rotate to face the camera but only on the x axis\r\n     */\r\n    public static BILLBOARDMODE_X = 1;\r\n    /**\r\n     * Object will rotate to face the camera but only on the y axis\r\n     */\r\n    public static BILLBOARDMODE_Y = 2;\r\n    /**\r\n     * Object will rotate to face the camera but only on the z axis\r\n     */\r\n    public static BILLBOARDMODE_Z = 4;\r\n    /**\r\n     * Object will rotate to face the camera\r\n     */\r\n    public static BILLBOARDMODE_ALL = 7;\r\n    /**\r\n     * Object will rotate to face the camera's position instead of orientation\r\n     */\r\n    public static BILLBOARDMODE_USE_POSITION = 128;\r\n    /**\r\n     * Child transform with Billboard flags should or should not apply parent rotation (default if off)\r\n     */\r\n    public static BillboardUseParentOrientation: boolean = false;\r\n\r\n    private static _TmpRotation = Quaternion.Zero();\r\n    private static _TmpScaling = Vector3.Zero();\r\n    private static _TmpTranslation = Vector3.Zero();\r\n\r\n    private _forward = new Vector3(0, 0, 1);\r\n    private _up = new Vector3(0, 1, 0);\r\n    private _right = new Vector3(1, 0, 0);\r\n\r\n    // Properties\r\n    @serializeAsVector3(\"position\")\r\n    private _position = Vector3.Zero();\r\n\r\n    @serializeAsVector3(\"rotation\")\r\n    private _rotation = Vector3.Zero();\r\n\r\n    @serializeAsQuaternion(\"rotationQuaternion\")\r\n    private _rotationQuaternion: Nullable<Quaternion> = null;\r\n\r\n    @serializeAsVector3(\"scaling\")\r\n    protected _scaling = Vector3.One();\r\n    private _transformToBoneReferal: Nullable<TransformNode> = null;\r\n    private _currentParentWhenAttachingToBone: Nullable<Node>;\r\n    private _isAbsoluteSynced = false;\r\n\r\n    @serialize(\"billboardMode\")\r\n    private _billboardMode = TransformNode.BILLBOARDMODE_NONE;\r\n\r\n    /**\r\n     * Gets or sets the billboard mode. Default is 0.\r\n     *\r\n     * | Value | Type | Description |\r\n     * | --- | --- | --- |\r\n     * | 0 | BILLBOARDMODE_NONE |  |\r\n     * | 1 | BILLBOARDMODE_X |  |\r\n     * | 2 | BILLBOARDMODE_Y |  |\r\n     * | 4 | BILLBOARDMODE_Z |  |\r\n     * | 7 | BILLBOARDMODE_ALL |  |\r\n     *\r\n     */\r\n    public get billboardMode() {\r\n        return this._billboardMode;\r\n    }\r\n\r\n    public set billboardMode(value: number) {\r\n        if (this._billboardMode === value) {\r\n            return;\r\n        }\r\n        this._billboardMode = value;\r\n        this._cache.useBillboardPosition = (this._billboardMode & TransformNode.BILLBOARDMODE_USE_POSITION) !== 0;\r\n        this._computeUseBillboardPath();\r\n    }\r\n\r\n    private _preserveParentRotationForBillboard = false;\r\n    /**\r\n     * Gets or sets a boolean indicating that parent rotation should be preserved when using billboards.\r\n     * This could be useful for glTF objects where parent rotation helps converting from right handed to left handed\r\n     */\r\n    public get preserveParentRotationForBillboard() {\r\n        return this._preserveParentRotationForBillboard;\r\n    }\r\n\r\n    public set preserveParentRotationForBillboard(value: boolean) {\r\n        if (value === this._preserveParentRotationForBillboard) {\r\n            return;\r\n        }\r\n        this._preserveParentRotationForBillboard = value;\r\n        this._computeUseBillboardPath();\r\n    }\r\n\r\n    private _computeUseBillboardPath(): void {\r\n        this._cache.useBillboardPath = this._billboardMode !== TransformNode.BILLBOARDMODE_NONE && !this.preserveParentRotationForBillboard;\r\n    }\r\n\r\n    /**\r\n     * Multiplication factor on scale x/y/z when computing the world matrix. Eg. for a 1x1x1 cube setting this to 2 will make it a 2x2x2 cube\r\n     */\r\n    @serialize()\r\n    public scalingDeterminant = 1;\r\n\r\n    @serialize(\"infiniteDistance\")\r\n    private _infiniteDistance = false;\r\n\r\n    /**\r\n     * Gets or sets the distance of the object to max, often used by skybox\r\n     */\r\n    public get infiniteDistance() {\r\n        return this._infiniteDistance;\r\n    }\r\n\r\n    public set infiniteDistance(value: boolean) {\r\n        if (this._infiniteDistance === value) {\r\n            return;\r\n        }\r\n\r\n        this._infiniteDistance = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that non uniform scaling (when at least one component is different from others) should be ignored.\r\n     * By default the system will update normals to compensate\r\n     */\r\n    @serialize()\r\n    public ignoreNonUniformScaling = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that even if rotationQuaternion is defined, you can keep updating rotation property and Babylon.js will just mix both\r\n     */\r\n    @serialize()\r\n    public reIntegrateRotationIntoRotationQuaternion = false;\r\n\r\n    // Cache\r\n    /** @internal */\r\n    public _poseMatrix: Nullable<Matrix> = null;\r\n    /** @internal */\r\n    public _localMatrix = Matrix.Zero();\r\n\r\n    private _usePivotMatrix = false;\r\n    private _absolutePosition = Vector3.Zero();\r\n    private _absoluteScaling = Vector3.Zero();\r\n    private _absoluteRotationQuaternion = Quaternion.Identity();\r\n    private _pivotMatrix = Matrix.Identity();\r\n    private _pivotMatrixInverse: Matrix;\r\n    /** @internal */\r\n    public _postMultiplyPivotMatrix = false;\r\n\r\n    protected _isWorldMatrixFrozen = false;\r\n\r\n    /** @internal */\r\n    public _indexInSceneTransformNodesArray = -1;\r\n\r\n    /**\r\n     * An event triggered after the world matrix is updated\r\n     */\r\n    public onAfterWorldMatrixUpdateObservable = new Observable<TransformNode>();\r\n\r\n    constructor(name: string, scene: Nullable<Scene> = null, isPure = true) {\r\n        super(name, scene);\r\n\r\n        if (isPure) {\r\n            this.getScene().addTransformNode(this);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"TransformNode\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"TransformNode\";\r\n    }\r\n\r\n    /**\r\n     * Gets or set the node position (default is (0.0, 0.0, 0.0))\r\n     */\r\n    public get position(): Vector3 {\r\n        return this._position;\r\n    }\r\n\r\n    public set position(newPosition: Vector3) {\r\n        this._position = newPosition;\r\n        this._isDirty = true;\r\n    }\r\n\r\n    /**\r\n     * return true if a pivot has been set\r\n     * @returns true if a pivot matrix is used\r\n     */\r\n    public isUsingPivotMatrix(): boolean {\r\n        return this._usePivotMatrix;\r\n    }\r\n\r\n    /**\r\n     * @returns true if pivot matrix must be cancelled in the world matrix. When this parameter is set to true (default), the inverse of the pivot matrix is also applied at the end to cancel the transformation effect.\r\n     */\r\n    public isUsingPostMultiplyPivotMatrix(): boolean {\r\n        return this._postMultiplyPivotMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the rotation property : a Vector3 defining the rotation value in radians around each local axis X, Y, Z  (default is (0.0, 0.0, 0.0)).\r\n     * If rotation quaternion is set, this Vector3 will be ignored and copy from the quaternion\r\n     */\r\n    public get rotation(): Vector3 {\r\n        return this._rotation;\r\n    }\r\n\r\n    public set rotation(newRotation: Vector3) {\r\n        this._rotation = newRotation;\r\n        this._rotationQuaternion = null;\r\n        this._isDirty = true;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the scaling property : a Vector3 defining the node scaling along each local axis X, Y, Z (default is (1.0, 1.0, 1.0)).\r\n     */\r\n    public get scaling(): Vector3 {\r\n        return this._scaling;\r\n    }\r\n\r\n    public set scaling(newScaling: Vector3) {\r\n        this._scaling = newScaling;\r\n        this._isDirty = true;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the rotation Quaternion property : this a Quaternion object defining the node rotation by using a unit quaternion (undefined by default, but can be null).\r\n     * If set, only the rotationQuaternion is then used to compute the node rotation (ie. node.rotation will be ignored)\r\n     */\r\n    public get rotationQuaternion(): Nullable<Quaternion> {\r\n        return this._rotationQuaternion;\r\n    }\r\n\r\n    public set rotationQuaternion(quaternion: Nullable<Quaternion>) {\r\n        this._rotationQuaternion = quaternion;\r\n        //reset the rotation vector.\r\n        if (quaternion) {\r\n            this._rotation.setAll(0.0);\r\n        }\r\n        this._isDirty = true;\r\n    }\r\n\r\n    /**\r\n     * The forward direction of that transform in world space.\r\n     */\r\n    public get forward(): Vector3 {\r\n        Vector3.TransformNormalFromFloatsToRef(0, 0, this.getScene().useRightHandedSystem ? -1.0 : 1.0, this.getWorldMatrix(), this._forward);\r\n        return this._forward.normalize();\r\n    }\r\n\r\n    /**\r\n     * The up direction of that transform in world space.\r\n     */\r\n    public get up(): Vector3 {\r\n        Vector3.TransformNormalFromFloatsToRef(0, 1, 0, this.getWorldMatrix(), this._up);\r\n        return this._up.normalize();\r\n    }\r\n\r\n    /**\r\n     * The right direction of that transform in world space.\r\n     */\r\n    public get right(): Vector3 {\r\n        Vector3.TransformNormalFromFloatsToRef(this.getScene().useRightHandedSystem ? -1.0 : 1.0, 0, 0, this.getWorldMatrix(), this._right);\r\n        return this._right.normalize();\r\n    }\r\n\r\n    /**\r\n     * Copies the parameter passed Matrix into the mesh Pose matrix.\r\n     * @param matrix the matrix to copy the pose from\r\n     * @returns this TransformNode.\r\n     */\r\n    public updatePoseMatrix(matrix: Matrix): TransformNode {\r\n        if (!this._poseMatrix) {\r\n            this._poseMatrix = matrix.clone();\r\n            return this;\r\n        }\r\n        this._poseMatrix.copyFrom(matrix);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Returns the mesh Pose matrix.\r\n     * @returns the pose matrix\r\n     */\r\n    public getPoseMatrix(): Matrix {\r\n        if (!this._poseMatrix) {\r\n            this._poseMatrix = Matrix.Identity();\r\n        }\r\n        return this._poseMatrix;\r\n    }\r\n\r\n    /** @internal */\r\n    public _isSynchronized(): boolean {\r\n        const cache = this._cache;\r\n\r\n        if (this._billboardMode !== cache.billboardMode || this._billboardMode !== TransformNode.BILLBOARDMODE_NONE) {\r\n            return false;\r\n        }\r\n\r\n        if (cache.pivotMatrixUpdated) {\r\n            return false;\r\n        }\r\n\r\n        if (this._infiniteDistance) {\r\n            return false;\r\n        }\r\n\r\n        if (this._position._isDirty) {\r\n            return false;\r\n        }\r\n\r\n        if (this._scaling._isDirty) {\r\n            return false;\r\n        }\r\n\r\n        if ((this._rotationQuaternion && this._rotationQuaternion._isDirty) || this._rotation._isDirty) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /** @internal */\r\n    public _initCache() {\r\n        super._initCache();\r\n\r\n        const cache = this._cache;\r\n        cache.localMatrixUpdated = false;\r\n        cache.billboardMode = -1;\r\n        cache.infiniteDistance = false;\r\n        cache.useBillboardPosition = false;\r\n        cache.useBillboardPath = false;\r\n    }\r\n\r\n    /**\r\n     * Returns the current mesh absolute position.\r\n     * Returns a Vector3.\r\n     */\r\n    public get absolutePosition(): Vector3 {\r\n        return this.getAbsolutePosition();\r\n    }\r\n\r\n    /**\r\n     * Returns the current mesh absolute scaling.\r\n     * Returns a Vector3.\r\n     */\r\n    public get absoluteScaling(): Vector3 {\r\n        this._syncAbsoluteScalingAndRotation();\r\n        return this._absoluteScaling;\r\n    }\r\n\r\n    /**\r\n     * Returns the current mesh absolute rotation.\r\n     * Returns a Quaternion.\r\n     */\r\n    public get absoluteRotationQuaternion(): Quaternion {\r\n        this._syncAbsoluteScalingAndRotation();\r\n        return this._absoluteRotationQuaternion;\r\n    }\r\n\r\n    /**\r\n     * Sets a new matrix to apply before all other transformation\r\n     * @param matrix defines the transform matrix\r\n     * @returns the current TransformNode\r\n     */\r\n    public setPreTransformMatrix(matrix: Matrix): TransformNode {\r\n        return this.setPivotMatrix(matrix, false);\r\n    }\r\n\r\n    /**\r\n     * Sets a new pivot matrix to the current node\r\n     * @param matrix defines the new pivot matrix to use\r\n     * @param postMultiplyPivotMatrix defines if the pivot matrix must be cancelled in the world matrix. When this parameter is set to true (default), the inverse of the pivot matrix is also applied at the end to cancel the transformation effect\r\n     * @returns the current TransformNode\r\n     */\r\n    public setPivotMatrix(matrix: DeepImmutable<Matrix>, postMultiplyPivotMatrix = true): TransformNode {\r\n        this._pivotMatrix.copyFrom(matrix);\r\n        this._usePivotMatrix = !this._pivotMatrix.isIdentity();\r\n\r\n        this._cache.pivotMatrixUpdated = true;\r\n        this._postMultiplyPivotMatrix = postMultiplyPivotMatrix;\r\n\r\n        if (this._postMultiplyPivotMatrix) {\r\n            if (!this._pivotMatrixInverse) {\r\n                this._pivotMatrixInverse = Matrix.Invert(this._pivotMatrix);\r\n            } else {\r\n                this._pivotMatrix.invertToRef(this._pivotMatrixInverse);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Returns the mesh pivot matrix.\r\n     * Default : Identity.\r\n     * @returns the matrix\r\n     */\r\n    public getPivotMatrix(): Matrix {\r\n        return this._pivotMatrix;\r\n    }\r\n\r\n    /**\r\n     * Instantiate (when possible) or clone that node with its hierarchy\r\n     * @param newParent defines the new parent to use for the instance (or clone)\r\n     * @param options defines options to configure how copy is done\r\n     * @param options.doNotInstantiate defines if the model must be instantiated or just cloned\r\n     * @param onNewNodeCreated defines an option callback to call when a clone or an instance is created\r\n     * @returns an instance (or a clone) of the current node with its hierarchy\r\n     */\r\n    public instantiateHierarchy(\r\n        newParent: Nullable<TransformNode> = null,\r\n        options?: { doNotInstantiate: boolean | ((node: TransformNode) => boolean) },\r\n        onNewNodeCreated?: (source: TransformNode, clone: TransformNode) => void\r\n    ): Nullable<TransformNode> {\r\n        const clone = this.clone(\"Clone of \" + (this.name || this.id), newParent || this.parent, true);\r\n\r\n        if (clone) {\r\n            if (onNewNodeCreated) {\r\n                onNewNodeCreated(this, clone);\r\n            }\r\n        }\r\n\r\n        for (const child of this.getChildTransformNodes(true)) {\r\n            child.instantiateHierarchy(clone, options, onNewNodeCreated);\r\n        }\r\n\r\n        return clone;\r\n    }\r\n\r\n    /**\r\n     * Prevents the World matrix to be computed any longer\r\n     * @param newWorldMatrix defines an optional matrix to use as world matrix\r\n     * @param decompose defines whether to decompose the given newWorldMatrix or directly assign\r\n     * @returns the TransformNode.\r\n     */\r\n    public freezeWorldMatrix(newWorldMatrix: Nullable<Matrix> = null, decompose = false): TransformNode {\r\n        if (newWorldMatrix) {\r\n            if (decompose) {\r\n                this._rotation.setAll(0);\r\n                this._rotationQuaternion = this._rotationQuaternion || Quaternion.Identity();\r\n                newWorldMatrix.decompose(this._scaling, this._rotationQuaternion, this._position);\r\n                this.computeWorldMatrix(true);\r\n            } else {\r\n                this._worldMatrix = newWorldMatrix;\r\n                this._absolutePosition.copyFromFloats(this._worldMatrix.m[12], this._worldMatrix.m[13], this._worldMatrix.m[14]);\r\n                this._afterComputeWorldMatrix();\r\n            }\r\n        } else {\r\n            this._isWorldMatrixFrozen = false; // no guarantee world is not already frozen, switch off temporarily\r\n            this.computeWorldMatrix(true);\r\n        }\r\n        this._isDirty = false;\r\n        this._isWorldMatrixFrozen = true;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Allows back the World matrix computation.\r\n     * @returns the TransformNode.\r\n     */\r\n    public unfreezeWorldMatrix() {\r\n        this._isWorldMatrixFrozen = false;\r\n        this.computeWorldMatrix(true);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * True if the World matrix has been frozen.\r\n     */\r\n    public get isWorldMatrixFrozen(): boolean {\r\n        return this._isWorldMatrixFrozen;\r\n    }\r\n\r\n    /**\r\n     * Returns the mesh absolute position in the World.\r\n     * @returns a Vector3.\r\n     */\r\n    public getAbsolutePosition(): Vector3 {\r\n        this.computeWorldMatrix();\r\n        return this._absolutePosition;\r\n    }\r\n\r\n    /**\r\n     * Sets the mesh absolute position in the World from a Vector3 or an Array(3).\r\n     * @param absolutePosition the absolute position to set\r\n     * @returns the TransformNode.\r\n     */\r\n    public setAbsolutePosition(absolutePosition: Vector3): TransformNode {\r\n        if (!absolutePosition) {\r\n            return this;\r\n        }\r\n        let absolutePositionX;\r\n        let absolutePositionY;\r\n        let absolutePositionZ;\r\n        if (absolutePosition.x === undefined) {\r\n            if (arguments.length < 3) {\r\n                return this;\r\n            }\r\n            absolutePositionX = arguments[0];\r\n            absolutePositionY = arguments[1];\r\n            absolutePositionZ = arguments[2];\r\n        } else {\r\n            absolutePositionX = absolutePosition.x;\r\n            absolutePositionY = absolutePosition.y;\r\n            absolutePositionZ = absolutePosition.z;\r\n        }\r\n        if (this.parent) {\r\n            const invertParentWorldMatrix = TmpVectors.Matrix[0];\r\n            this.parent.getWorldMatrix().invertToRef(invertParentWorldMatrix);\r\n            Vector3.TransformCoordinatesFromFloatsToRef(absolutePositionX, absolutePositionY, absolutePositionZ, invertParentWorldMatrix, this.position);\r\n        } else {\r\n            this.position.x = absolutePositionX;\r\n            this.position.y = absolutePositionY;\r\n            this.position.z = absolutePositionZ;\r\n        }\r\n\r\n        this._absolutePosition.copyFrom(absolutePosition);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the mesh position in its local space.\r\n     * @param vector3 the position to set in localspace\r\n     * @returns the TransformNode.\r\n     */\r\n    public setPositionWithLocalVector(vector3: Vector3): TransformNode {\r\n        this.computeWorldMatrix();\r\n        this.position = Vector3.TransformNormal(vector3, this._localMatrix);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Returns the mesh position in the local space from the current World matrix values.\r\n     * @returns a new Vector3.\r\n     */\r\n    public getPositionExpressedInLocalSpace(): Vector3 {\r\n        this.computeWorldMatrix();\r\n        const invLocalWorldMatrix = TmpVectors.Matrix[0];\r\n        this._localMatrix.invertToRef(invLocalWorldMatrix);\r\n        return Vector3.TransformNormal(this.position, invLocalWorldMatrix);\r\n    }\r\n\r\n    /**\r\n     * Translates the mesh along the passed Vector3 in its local space.\r\n     * @param vector3 the distance to translate in localspace\r\n     * @returns the TransformNode.\r\n     */\r\n    public locallyTranslate(vector3: Vector3): TransformNode {\r\n        this.computeWorldMatrix(true);\r\n        this.position = Vector3.TransformCoordinates(vector3, this._localMatrix);\r\n        return this;\r\n    }\r\n\r\n    private static _LookAtVectorCache = new Vector3(0, 0, 0);\r\n\r\n    /**\r\n     * Orients a mesh towards a target point. Mesh must be drawn facing user.\r\n     * @param targetPoint the position (must be in same space as current mesh) to look at\r\n     * @param yawCor optional yaw (y-axis) correction in radians\r\n     * @param pitchCor optional pitch (x-axis) correction in radians\r\n     * @param rollCor optional roll (z-axis) correction in radians\r\n     * @param space the chosen space of the target\r\n     * @returns the TransformNode.\r\n     */\r\n    public lookAt(targetPoint: Vector3, yawCor: number = 0, pitchCor: number = 0, rollCor: number = 0, space: Space = Space.LOCAL): TransformNode {\r\n        const dv = TransformNode._LookAtVectorCache;\r\n        const pos = space === Space.LOCAL ? this.position : this.getAbsolutePosition();\r\n        targetPoint.subtractToRef(pos, dv);\r\n        this.setDirection(dv, yawCor, pitchCor, rollCor);\r\n\r\n        // Correct for parent's rotation offset\r\n        if (space === Space.WORLD && this.parent) {\r\n            if (this.rotationQuaternion) {\r\n                // Get local rotation matrix of the looking object\r\n                const rotationMatrix = TmpVectors.Matrix[0];\r\n                this.rotationQuaternion.toRotationMatrix(rotationMatrix);\r\n\r\n                // Offset rotation by parent's inverted rotation matrix to correct in world space\r\n                const parentRotationMatrix = TmpVectors.Matrix[1];\r\n                this.parent.getWorldMatrix().getRotationMatrixToRef(parentRotationMatrix);\r\n                parentRotationMatrix.invert();\r\n                rotationMatrix.multiplyToRef(parentRotationMatrix, rotationMatrix);\r\n                this.rotationQuaternion.fromRotationMatrix(rotationMatrix);\r\n            } else {\r\n                // Get local rotation matrix of the looking object\r\n                const quaternionRotation = TmpVectors.Quaternion[0];\r\n                Quaternion.FromEulerVectorToRef(this.rotation, quaternionRotation);\r\n                const rotationMatrix = TmpVectors.Matrix[0];\r\n                quaternionRotation.toRotationMatrix(rotationMatrix);\r\n\r\n                // Offset rotation by parent's inverted rotation matrix to correct in world space\r\n                const parentRotationMatrix = TmpVectors.Matrix[1];\r\n                this.parent.getWorldMatrix().getRotationMatrixToRef(parentRotationMatrix);\r\n                parentRotationMatrix.invert();\r\n                rotationMatrix.multiplyToRef(parentRotationMatrix, rotationMatrix);\r\n                quaternionRotation.fromRotationMatrix(rotationMatrix);\r\n                quaternionRotation.toEulerAnglesToRef(this.rotation);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Returns a new Vector3 that is the localAxis, expressed in the mesh local space, rotated like the mesh.\r\n     * This Vector3 is expressed in the World space.\r\n     * @param localAxis axis to rotate\r\n     * @returns a new Vector3 that is the localAxis, expressed in the mesh local space, rotated like the mesh.\r\n     */\r\n    public getDirection(localAxis: Vector3): Vector3 {\r\n        const result = Vector3.Zero();\r\n\r\n        this.getDirectionToRef(localAxis, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Sets the Vector3 \"result\" as the rotated Vector3 \"localAxis\" in the same rotation than the mesh.\r\n     * localAxis is expressed in the mesh local space.\r\n     * result is computed in the World space from the mesh World matrix.\r\n     * @param localAxis axis to rotate\r\n     * @param result the resulting transformnode\r\n     * @returns this TransformNode.\r\n     */\r\n    public getDirectionToRef(localAxis: Vector3, result: Vector3): TransformNode {\r\n        Vector3.TransformNormalToRef(localAxis, this.getWorldMatrix(), result);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets this transform node rotation to the given local axis.\r\n     * @param localAxis the axis in local space\r\n     * @param yawCor optional yaw (y-axis) correction in radians\r\n     * @param pitchCor optional pitch (x-axis) correction in radians\r\n     * @param rollCor optional roll (z-axis) correction in radians\r\n     * @returns this TransformNode\r\n     */\r\n    public setDirection(localAxis: Vector3, yawCor: number = 0, pitchCor: number = 0, rollCor: number = 0): TransformNode {\r\n        const yaw = -Math.atan2(localAxis.z, localAxis.x) + Math.PI / 2;\r\n        const len = Math.sqrt(localAxis.x * localAxis.x + localAxis.z * localAxis.z);\r\n        const pitch = -Math.atan2(localAxis.y, len);\r\n        if (this.rotationQuaternion) {\r\n            Quaternion.RotationYawPitchRollToRef(yaw + yawCor, pitch + pitchCor, rollCor, this.rotationQuaternion);\r\n        } else {\r\n            this.rotation.x = pitch + pitchCor;\r\n            this.rotation.y = yaw + yawCor;\r\n            this.rotation.z = rollCor;\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets a new pivot point to the current node\r\n     * @param point defines the new pivot point to use\r\n     * @param space defines if the point is in world or local space (local by default)\r\n     * @returns the current TransformNode\r\n     */\r\n    public setPivotPoint(point: Vector3, space: Space = Space.LOCAL): TransformNode {\r\n        if (this.getScene().getRenderId() == 0) {\r\n            this.computeWorldMatrix(true);\r\n        }\r\n\r\n        const wm = this.getWorldMatrix();\r\n\r\n        if (space == Space.WORLD) {\r\n            const tmat = TmpVectors.Matrix[0];\r\n            wm.invertToRef(tmat);\r\n            point = Vector3.TransformCoordinates(point, tmat);\r\n        }\r\n\r\n        return this.setPivotMatrix(Matrix.Translation(-point.x, -point.y, -point.z), true);\r\n    }\r\n\r\n    /**\r\n     * Returns a new Vector3 set with the mesh pivot point coordinates in the local space.\r\n     * @returns the pivot point\r\n     */\r\n    public getPivotPoint(): Vector3 {\r\n        const point = Vector3.Zero();\r\n        this.getPivotPointToRef(point);\r\n        return point;\r\n    }\r\n\r\n    /**\r\n     * Sets the passed Vector3 \"result\" with the coordinates of the mesh pivot point in the local space.\r\n     * @param result the vector3 to store the result\r\n     * @returns this TransformNode.\r\n     */\r\n    public getPivotPointToRef(result: Vector3): TransformNode {\r\n        result.x = -this._pivotMatrix.m[12];\r\n        result.y = -this._pivotMatrix.m[13];\r\n        result.z = -this._pivotMatrix.m[14];\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Returns a new Vector3 set with the mesh pivot point World coordinates.\r\n     * @returns a new Vector3 set with the mesh pivot point World coordinates.\r\n     */\r\n    public getAbsolutePivotPoint(): Vector3 {\r\n        const point = Vector3.Zero();\r\n        this.getAbsolutePivotPointToRef(point);\r\n        return point;\r\n    }\r\n\r\n    /**\r\n     * Sets the Vector3 \"result\" coordinates with the mesh pivot point World coordinates.\r\n     * @param result vector3 to store the result\r\n     * @returns this TransformNode.\r\n     */\r\n    public getAbsolutePivotPointToRef(result: Vector3): TransformNode {\r\n        this.getPivotPointToRef(result);\r\n        Vector3.TransformCoordinatesToRef(result, this.getWorldMatrix(), result);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Flag the transform node as dirty (Forcing it to update everything)\r\n     * @param property if set to \"rotation\" the objects rotationQuaternion will be set to null\r\n     * @returns this  node\r\n     */\r\n    public markAsDirty(property?: string): Node {\r\n        if (this._isDirty) {\r\n            return this;\r\n        }\r\n\r\n        // We need to explicitly update the children\r\n        // as the scene.evaluateActiveMeshes will not poll the transform nodes\r\n        if (this._children) {\r\n            for (const child of this._children) {\r\n                child.markAsDirty(property);\r\n            }\r\n        }\r\n        return super.markAsDirty(property);\r\n    }\r\n\r\n    /**\r\n     * Defines the passed node as the parent of the current node.\r\n     * The node will remain exactly where it is and its position / rotation will be updated accordingly.\r\n     * Note that if the mesh has a pivot matrix / point defined it will be applied after the parent was updated.\r\n     * In that case the node will not remain in the same space as it is, as the pivot will be applied.\r\n     * To avoid this, you can set updatePivot to true and the pivot will be updated to identity\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/transforms/parent_pivot/parent\r\n     * @param node the node ot set as the parent\r\n     * @param preserveScalingSign if true, keep scaling sign of child. Otherwise, scaling sign might change.\r\n     * @param updatePivot if true, update the pivot matrix to keep the node in the same space as before\r\n     * @returns this TransformNode.\r\n     */\r\n    public setParent(node: Nullable<Node>, preserveScalingSign: boolean = false, updatePivot = false): TransformNode {\r\n        if (!node && !this.parent) {\r\n            return this;\r\n        }\r\n\r\n        const quatRotation = TmpVectors.Quaternion[0];\r\n        const position = TmpVectors.Vector3[0];\r\n        const scale = TmpVectors.Vector3[1];\r\n        const invParentMatrix = TmpVectors.Matrix[1];\r\n        Matrix.IdentityToRef(invParentMatrix);\r\n        const composedMatrix = TmpVectors.Matrix[0];\r\n        this.computeWorldMatrix(true);\r\n\r\n        let currentRotation = this.rotationQuaternion;\r\n        if (!currentRotation) {\r\n            currentRotation = TransformNode._TmpRotation;\r\n            Quaternion.RotationYawPitchRollToRef(this._rotation.y, this._rotation.x, this._rotation.z, currentRotation);\r\n        }\r\n\r\n        // current global transformation without pivot\r\n        Matrix.ComposeToRef(this.scaling, currentRotation, this.position, composedMatrix);\r\n        if (this.parent) {\r\n            composedMatrix.multiplyToRef(this.parent.computeWorldMatrix(true), composedMatrix);\r\n        }\r\n\r\n        // is a node was set, calculate the difference between this and the node\r\n        if (node) {\r\n            node.computeWorldMatrix(true).invertToRef(invParentMatrix);\r\n            composedMatrix.multiplyToRef(invParentMatrix, composedMatrix);\r\n        }\r\n        composedMatrix.decompose(scale, quatRotation, position, preserveScalingSign ? this : undefined);\r\n\r\n        if (this.rotationQuaternion) {\r\n            this.rotationQuaternion.copyFrom(quatRotation);\r\n        } else {\r\n            quatRotation.toEulerAnglesToRef(this.rotation);\r\n        }\r\n\r\n        this.scaling.copyFrom(scale);\r\n        this.position.copyFrom(position);\r\n\r\n        this.parent = node;\r\n\r\n        if (updatePivot) {\r\n            this.setPivotMatrix(Matrix.Identity());\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    private _nonUniformScaling = false;\r\n    /**\r\n     * True if the scaling property of this object is non uniform eg. (1,2,1)\r\n     */\r\n    public get nonUniformScaling(): boolean {\r\n        return this._nonUniformScaling;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _updateNonUniformScalingState(value: boolean): boolean {\r\n        if (this._nonUniformScaling === value) {\r\n            return false;\r\n        }\r\n\r\n        this._nonUniformScaling = value;\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Attach the current TransformNode to another TransformNode associated with a bone\r\n     * @param bone Bone affecting the TransformNode\r\n     * @param affectedTransformNode TransformNode associated with the bone\r\n     * @returns this object\r\n     */\r\n    public attachToBone(bone: Bone, affectedTransformNode: TransformNode): TransformNode {\r\n        this._currentParentWhenAttachingToBone = this.parent;\r\n        this._transformToBoneReferal = affectedTransformNode;\r\n        this.parent = bone;\r\n\r\n        bone.getSkeleton().prepare(true); // make sure bone.getFinalMatrix() is up to date\r\n\r\n        if (bone.getFinalMatrix().determinant() < 0) {\r\n            this.scalingDeterminant *= -1;\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Detach the transform node if its associated with a bone\r\n     * @param resetToPreviousParent Indicates if the parent that was in effect when attachToBone was called should be set back or if we should set parent to null instead (defaults to the latter)\r\n     * @returns this object\r\n     */\r\n    public detachFromBone(resetToPreviousParent = false): TransformNode {\r\n        if (!this.parent) {\r\n            if (resetToPreviousParent) {\r\n                this.parent = this._currentParentWhenAttachingToBone;\r\n            }\r\n            return this;\r\n        }\r\n\r\n        if (this.parent.getWorldMatrix().determinant() < 0) {\r\n            this.scalingDeterminant *= -1;\r\n        }\r\n        this._transformToBoneReferal = null;\r\n        if (resetToPreviousParent) {\r\n            this.parent = this._currentParentWhenAttachingToBone;\r\n        } else {\r\n            this.parent = null;\r\n        }\r\n        return this;\r\n    }\r\n\r\n    private static _RotationAxisCache = new Quaternion();\r\n    /**\r\n     * Rotates the mesh around the axis vector for the passed angle (amount) expressed in radians, in the given space.\r\n     * space (default LOCAL) can be either Space.LOCAL, either Space.WORLD.\r\n     * Note that the property `rotationQuaternion` is then automatically updated and the property `rotation` is set to (0,0,0) and no longer used.\r\n     * The passed axis is also normalized.\r\n     * @param axis the axis to rotate around\r\n     * @param amount the amount to rotate in radians\r\n     * @param space Space to rotate in (Default: local)\r\n     * @returns the TransformNode.\r\n     */\r\n    public rotate(axis: Vector3, amount: number, space?: Space): TransformNode {\r\n        axis.normalize();\r\n        if (!this.rotationQuaternion) {\r\n            this.rotationQuaternion = this.rotation.toQuaternion();\r\n            this.rotation.setAll(0);\r\n        }\r\n        let rotationQuaternion: Quaternion;\r\n        if (!space || (space as any) === Space.LOCAL) {\r\n            rotationQuaternion = Quaternion.RotationAxisToRef(axis, amount, TransformNode._RotationAxisCache);\r\n            this.rotationQuaternion.multiplyToRef(rotationQuaternion, this.rotationQuaternion);\r\n        } else {\r\n            if (this.parent) {\r\n                const parentWorldMatrix = this.parent.getWorldMatrix();\r\n                const invertParentWorldMatrix = TmpVectors.Matrix[0];\r\n                parentWorldMatrix.invertToRef(invertParentWorldMatrix);\r\n                axis = Vector3.TransformNormal(axis, invertParentWorldMatrix);\r\n\r\n                if (parentWorldMatrix.determinant() < 0) {\r\n                    amount *= -1;\r\n                }\r\n            }\r\n            rotationQuaternion = Quaternion.RotationAxisToRef(axis, amount, TransformNode._RotationAxisCache);\r\n            rotationQuaternion.multiplyToRef(this.rotationQuaternion, this.rotationQuaternion);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Rotates the mesh around the axis vector for the passed angle (amount) expressed in radians, in world space.\r\n     * Note that the property `rotationQuaternion` is then automatically updated and the property `rotation` is set to (0,0,0) and no longer used.\r\n     * The passed axis is also normalized. .\r\n     * Method is based on http://www.euclideanspace.com/maths/geometry/affine/aroundPoint/index.htm\r\n     * @param point the point to rotate around\r\n     * @param axis the axis to rotate around\r\n     * @param amount the amount to rotate in radians\r\n     * @returns the TransformNode\r\n     */\r\n    public rotateAround(point: Vector3, axis: Vector3, amount: number): TransformNode {\r\n        axis.normalize();\r\n        if (!this.rotationQuaternion) {\r\n            this.rotationQuaternion = Quaternion.RotationYawPitchRoll(this.rotation.y, this.rotation.x, this.rotation.z);\r\n            this.rotation.setAll(0);\r\n        }\r\n\r\n        const tmpVector = TmpVectors.Vector3[0];\r\n        const finalScale = TmpVectors.Vector3[1];\r\n        const finalTranslation = TmpVectors.Vector3[2];\r\n\r\n        const finalRotation = TmpVectors.Quaternion[0];\r\n\r\n        const translationMatrix = TmpVectors.Matrix[0]; // T\r\n        const translationMatrixInv = TmpVectors.Matrix[1]; // T'\r\n        const rotationMatrix = TmpVectors.Matrix[2]; // R\r\n        const finalMatrix = TmpVectors.Matrix[3]; // T' x R x T\r\n\r\n        point.subtractToRef(this.position, tmpVector);\r\n        Matrix.TranslationToRef(tmpVector.x, tmpVector.y, tmpVector.z, translationMatrix); // T\r\n        Matrix.TranslationToRef(-tmpVector.x, -tmpVector.y, -tmpVector.z, translationMatrixInv); // T'\r\n        Matrix.RotationAxisToRef(axis, amount, rotationMatrix); // R\r\n\r\n        translationMatrixInv.multiplyToRef(rotationMatrix, finalMatrix); // T' x R\r\n        finalMatrix.multiplyToRef(translationMatrix, finalMatrix); // T' x R x T\r\n\r\n        finalMatrix.decompose(finalScale, finalRotation, finalTranslation);\r\n\r\n        this.position.addInPlace(finalTranslation);\r\n        finalRotation.multiplyToRef(this.rotationQuaternion, this.rotationQuaternion);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Translates the mesh along the axis vector for the passed distance in the given space.\r\n     * space (default LOCAL) can be either Space.LOCAL, either Space.WORLD.\r\n     * @param axis the axis to translate in\r\n     * @param distance the distance to translate\r\n     * @param space Space to rotate in (Default: local)\r\n     * @returns the TransformNode.\r\n     */\r\n    public translate(axis: Vector3, distance: number, space?: Space): TransformNode {\r\n        const displacementVector = axis.scale(distance);\r\n        if (!space || (space as any) === Space.LOCAL) {\r\n            const tempV3 = this.getPositionExpressedInLocalSpace().add(displacementVector);\r\n            this.setPositionWithLocalVector(tempV3);\r\n        } else {\r\n            this.setAbsolutePosition(this.getAbsolutePosition().add(displacementVector));\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds a rotation step to the mesh current rotation.\r\n     * x, y, z are Euler angles expressed in radians.\r\n     * This methods updates the current mesh rotation, either mesh.rotation, either mesh.rotationQuaternion if it's set.\r\n     * This means this rotation is made in the mesh local space only.\r\n     * It's useful to set a custom rotation order different from the BJS standard one YXZ.\r\n     * Example : this rotates the mesh first around its local X axis, then around its local Z axis, finally around its local Y axis.\r\n     * ```javascript\r\n     * mesh.addRotation(x1, 0, 0).addRotation(0, 0, z2).addRotation(0, 0, y3);\r\n     * ```\r\n     * Note that `addRotation()` accumulates the passed rotation values to the current ones and computes the .rotation or .rotationQuaternion updated values.\r\n     * Under the hood, only quaternions are used. So it's a little faster is you use .rotationQuaternion because it doesn't need to translate them back to Euler angles.\r\n     * @param x Rotation to add\r\n     * @param y Rotation to add\r\n     * @param z Rotation to add\r\n     * @returns the TransformNode.\r\n     */\r\n    public addRotation(x: number, y: number, z: number): TransformNode {\r\n        let rotationQuaternion;\r\n        if (this.rotationQuaternion) {\r\n            rotationQuaternion = this.rotationQuaternion;\r\n        } else {\r\n            rotationQuaternion = TmpVectors.Quaternion[1];\r\n            Quaternion.RotationYawPitchRollToRef(this.rotation.y, this.rotation.x, this.rotation.z, rotationQuaternion);\r\n        }\r\n        const accumulation = TmpVectors.Quaternion[0];\r\n        Quaternion.RotationYawPitchRollToRef(y, x, z, accumulation);\r\n        rotationQuaternion.multiplyInPlace(accumulation);\r\n        if (!this.rotationQuaternion) {\r\n            rotationQuaternion.toEulerAnglesToRef(this.rotation);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    protected _getEffectiveParent(): Nullable<Node> {\r\n        return this.parent;\r\n    }\r\n\r\n    /**\r\n     * Returns whether the transform node world matrix computation needs the camera information to be computed.\r\n     * This is the case when the node is a billboard or has an infinite distance for instance.\r\n     * @returns true if the world matrix computation needs the camera information to be computed\r\n     */\r\n    public isWorldMatrixCameraDependent(): boolean {\r\n        return (this._infiniteDistance && !this.parent) || (this._billboardMode !== TransformNode.BILLBOARDMODE_NONE && !this.preserveParentRotationForBillboard);\r\n    }\r\n\r\n    /**\r\n     * Computes the world matrix of the node\r\n     * @param force defines if the cache version should be invalidated forcing the world matrix to be created from scratch\r\n     * @param camera defines the camera used if different from the scene active camera (This is used with modes like Billboard or infinite distance)\r\n     * @returns the world matrix\r\n     */\r\n    public computeWorldMatrix(force: boolean = false, camera: Nullable<Camera> = null): Matrix {\r\n        if (this._isWorldMatrixFrozen && !this._isDirty) {\r\n            return this._worldMatrix;\r\n        }\r\n\r\n        const currentRenderId = this.getScene().getRenderId();\r\n        if (!this._isDirty && !force && (this._currentRenderId === currentRenderId || this.isSynchronized())) {\r\n            this._currentRenderId = currentRenderId;\r\n            return this._worldMatrix;\r\n        }\r\n\r\n        camera = camera || this.getScene().activeCamera;\r\n\r\n        this._updateCache();\r\n        const cache = this._cache;\r\n        cache.pivotMatrixUpdated = false;\r\n        cache.billboardMode = this.billboardMode;\r\n        cache.infiniteDistance = this.infiniteDistance;\r\n        cache.parent = this._parentNode;\r\n\r\n        this._currentRenderId = currentRenderId;\r\n        this._childUpdateId += 1;\r\n        this._isDirty = false;\r\n        this._position._isDirty = false;\r\n        this._rotation._isDirty = false;\r\n        this._scaling._isDirty = false;\r\n        const parent = this._getEffectiveParent();\r\n\r\n        // Scaling\r\n        const scaling: Vector3 = TransformNode._TmpScaling;\r\n        let translation: Vector3 = this._position;\r\n\r\n        // Translation\r\n        if (this._infiniteDistance) {\r\n            if (!this.parent && camera) {\r\n                const cameraWorldMatrix = camera.getWorldMatrix();\r\n                const cameraGlobalPosition = new Vector3(cameraWorldMatrix.m[12], cameraWorldMatrix.m[13], cameraWorldMatrix.m[14]);\r\n\r\n                translation = TransformNode._TmpTranslation;\r\n                translation.copyFromFloats(this._position.x + cameraGlobalPosition.x, this._position.y + cameraGlobalPosition.y, this._position.z + cameraGlobalPosition.z);\r\n            }\r\n        }\r\n\r\n        // Scaling\r\n        scaling.copyFromFloats(this._scaling.x * this.scalingDeterminant, this._scaling.y * this.scalingDeterminant, this._scaling.z * this.scalingDeterminant);\r\n\r\n        // Rotation\r\n        let rotation: Quaternion;\r\n        if (this._rotationQuaternion) {\r\n            this._rotationQuaternion._isDirty = false;\r\n            rotation = this._rotationQuaternion;\r\n            if (this.reIntegrateRotationIntoRotationQuaternion) {\r\n                const len = this.rotation.lengthSquared();\r\n                if (len) {\r\n                    this._rotationQuaternion.multiplyInPlace(Quaternion.RotationYawPitchRoll(this._rotation.y, this._rotation.x, this._rotation.z));\r\n                    this._rotation.copyFromFloats(0, 0, 0);\r\n                }\r\n            }\r\n        } else {\r\n            rotation = TransformNode._TmpRotation;\r\n            Quaternion.RotationYawPitchRollToRef(this._rotation.y, this._rotation.x, this._rotation.z, rotation);\r\n        }\r\n\r\n        // Compose\r\n        if (this._usePivotMatrix) {\r\n            const scaleMatrix = TmpVectors.Matrix[1];\r\n            Matrix.ScalingToRef(scaling.x, scaling.y, scaling.z, scaleMatrix);\r\n\r\n            // Rotation\r\n            const rotationMatrix = TmpVectors.Matrix[0];\r\n            rotation.toRotationMatrix(rotationMatrix);\r\n\r\n            // Composing transformations\r\n            this._pivotMatrix.multiplyToRef(scaleMatrix, TmpVectors.Matrix[4]);\r\n            TmpVectors.Matrix[4].multiplyToRef(rotationMatrix, this._localMatrix);\r\n\r\n            // Post multiply inverse of pivotMatrix\r\n            if (this._postMultiplyPivotMatrix) {\r\n                this._localMatrix.multiplyToRef(this._pivotMatrixInverse, this._localMatrix);\r\n            }\r\n\r\n            this._localMatrix.addTranslationFromFloats(translation.x, translation.y, translation.z);\r\n        } else {\r\n            Matrix.ComposeToRef(scaling, rotation, translation, this._localMatrix);\r\n        }\r\n\r\n        // Parent\r\n        if (parent && parent.getWorldMatrix) {\r\n            if (force) {\r\n                parent.computeWorldMatrix(force);\r\n            }\r\n            if (cache.useBillboardPath) {\r\n                if (this._transformToBoneReferal) {\r\n                    const bone = this.parent as Bone;\r\n                    bone.getSkeleton().prepare();\r\n                    bone.getFinalMatrix().multiplyToRef(this._transformToBoneReferal.getWorldMatrix(), TmpVectors.Matrix[7]);\r\n                } else {\r\n                    TmpVectors.Matrix[7].copyFrom(parent.getWorldMatrix());\r\n                }\r\n\r\n                // Extract scaling and translation from parent\r\n                const translation = TmpVectors.Vector3[5];\r\n                const scale = TmpVectors.Vector3[6];\r\n                const orientation = TmpVectors.Quaternion[0];\r\n                TmpVectors.Matrix[7].decompose(scale, orientation, translation);\r\n                Matrix.ScalingToRef(scale.x, scale.y, scale.z, TmpVectors.Matrix[7]);\r\n                TmpVectors.Matrix[7].setTranslation(translation);\r\n\r\n                if (TransformNode.BillboardUseParentOrientation) {\r\n                    // set localMatrix translation to be transformed against parent's orientation.\r\n                    this._position.applyRotationQuaternionToRef(orientation, translation);\r\n                    this._localMatrix.setTranslation(translation);\r\n                }\r\n\r\n                this._localMatrix.multiplyToRef(TmpVectors.Matrix[7], this._worldMatrix);\r\n            } else {\r\n                if (this._transformToBoneReferal) {\r\n                    const bone = this.parent as Bone;\r\n                    bone.getSkeleton().prepare();\r\n                    this._localMatrix.multiplyToRef(bone.getFinalMatrix(), TmpVectors.Matrix[6]);\r\n                    TmpVectors.Matrix[6].multiplyToRef(this._transformToBoneReferal.getWorldMatrix(), this._worldMatrix);\r\n                } else {\r\n                    this._localMatrix.multiplyToRef(parent.getWorldMatrix(), this._worldMatrix);\r\n                }\r\n            }\r\n            this._markSyncedWithParent();\r\n        } else {\r\n            this._worldMatrix.copyFrom(this._localMatrix);\r\n        }\r\n\r\n        // Billboarding based on camera orientation (testing PG:http://www.babylonjs-playground.com/#UJEIL#13)\r\n        if (cache.useBillboardPath && camera && this.billboardMode && !cache.useBillboardPosition) {\r\n            const storedTranslation = TmpVectors.Vector3[0];\r\n            this._worldMatrix.getTranslationToRef(storedTranslation); // Save translation\r\n\r\n            // Cancel camera rotation\r\n            TmpVectors.Matrix[1].copyFrom(camera.getViewMatrix());\r\n\r\n            if (this._scene.useRightHandedSystem) {\r\n                TmpVectors.Matrix[1].multiplyToRef(convertRHSToLHS, TmpVectors.Matrix[1]);\r\n            }\r\n\r\n            TmpVectors.Matrix[1].setTranslationFromFloats(0, 0, 0);\r\n            TmpVectors.Matrix[1].invertToRef(TmpVectors.Matrix[0]);\r\n\r\n            if ((this.billboardMode & TransformNode.BILLBOARDMODE_ALL) !== TransformNode.BILLBOARDMODE_ALL) {\r\n                TmpVectors.Matrix[0].decompose(undefined, TmpVectors.Quaternion[0], undefined);\r\n                const eulerAngles = TmpVectors.Vector3[1];\r\n                TmpVectors.Quaternion[0].toEulerAnglesToRef(eulerAngles);\r\n\r\n                if ((this.billboardMode & TransformNode.BILLBOARDMODE_X) !== TransformNode.BILLBOARDMODE_X) {\r\n                    eulerAngles.x = 0;\r\n                }\r\n\r\n                if ((this.billboardMode & TransformNode.BILLBOARDMODE_Y) !== TransformNode.BILLBOARDMODE_Y) {\r\n                    eulerAngles.y = 0;\r\n                }\r\n\r\n                if ((this.billboardMode & TransformNode.BILLBOARDMODE_Z) !== TransformNode.BILLBOARDMODE_Z) {\r\n                    eulerAngles.z = 0;\r\n                }\r\n\r\n                Matrix.RotationYawPitchRollToRef(eulerAngles.y, eulerAngles.x, eulerAngles.z, TmpVectors.Matrix[0]);\r\n            }\r\n            this._worldMatrix.setTranslationFromFloats(0, 0, 0);\r\n            this._worldMatrix.multiplyToRef(TmpVectors.Matrix[0], this._worldMatrix);\r\n\r\n            // Restore translation\r\n            this._worldMatrix.setTranslation(TmpVectors.Vector3[0]);\r\n        }\r\n        // Billboarding based on camera position\r\n        else if (cache.useBillboardPath && camera && cache.useBillboardPosition) {\r\n            const storedTranslation = TmpVectors.Vector3[0];\r\n            // Save translation\r\n            this._worldMatrix.getTranslationToRef(storedTranslation);\r\n\r\n            // Compute camera position in local space\r\n            const cameraPosition = camera.globalPosition;\r\n            this._worldMatrix.invertToRef(TmpVectors.Matrix[1]);\r\n            const camInObjSpace = TmpVectors.Vector3[1];\r\n            Vector3.TransformCoordinatesToRef(cameraPosition, TmpVectors.Matrix[1], camInObjSpace);\r\n            camInObjSpace.normalize();\r\n\r\n            // Find the lookAt info in local space\r\n            const yaw = -Math.atan2(camInObjSpace.z, camInObjSpace.x) + Math.PI / 2;\r\n            const len = Math.sqrt(camInObjSpace.x * camInObjSpace.x + camInObjSpace.z * camInObjSpace.z);\r\n            const pitch = -Math.atan2(camInObjSpace.y, len);\r\n            Quaternion.RotationYawPitchRollToRef(yaw, pitch, 0, TmpVectors.Quaternion[0]);\r\n\r\n            if ((this.billboardMode & TransformNode.BILLBOARDMODE_ALL) !== TransformNode.BILLBOARDMODE_ALL) {\r\n                const eulerAngles = TmpVectors.Vector3[1];\r\n                TmpVectors.Quaternion[0].toEulerAnglesToRef(eulerAngles);\r\n\r\n                if ((this.billboardMode & TransformNode.BILLBOARDMODE_X) !== TransformNode.BILLBOARDMODE_X) {\r\n                    eulerAngles.x = 0;\r\n                }\r\n\r\n                if ((this.billboardMode & TransformNode.BILLBOARDMODE_Y) !== TransformNode.BILLBOARDMODE_Y) {\r\n                    eulerAngles.y = 0;\r\n                }\r\n\r\n                if ((this.billboardMode & TransformNode.BILLBOARDMODE_Z) !== TransformNode.BILLBOARDMODE_Z) {\r\n                    eulerAngles.z = 0;\r\n                }\r\n\r\n                Matrix.RotationYawPitchRollToRef(eulerAngles.y, eulerAngles.x, eulerAngles.z, TmpVectors.Matrix[0]);\r\n            } else {\r\n                Matrix.FromQuaternionToRef(TmpVectors.Quaternion[0], TmpVectors.Matrix[0]);\r\n            }\r\n\r\n            // Cancel translation\r\n            this._worldMatrix.setTranslationFromFloats(0, 0, 0);\r\n\r\n            // Rotate according to lookat (diff from local to lookat)\r\n            this._worldMatrix.multiplyToRef(TmpVectors.Matrix[0], this._worldMatrix);\r\n\r\n            // Restore translation\r\n            this._worldMatrix.setTranslation(TmpVectors.Vector3[0]);\r\n        }\r\n\r\n        // Normal matrix\r\n        if (!this.ignoreNonUniformScaling) {\r\n            if (this._scaling.isNonUniformWithinEpsilon(0.000001)) {\r\n                this._updateNonUniformScalingState(true);\r\n            } else if (parent && (<TransformNode>parent)._nonUniformScaling) {\r\n                this._updateNonUniformScalingState((<TransformNode>parent)._nonUniformScaling);\r\n            } else {\r\n                this._updateNonUniformScalingState(false);\r\n            }\r\n        } else {\r\n            this._updateNonUniformScalingState(false);\r\n        }\r\n\r\n        this._afterComputeWorldMatrix();\r\n\r\n        // Absolute position\r\n        this._absolutePosition.copyFromFloats(this._worldMatrix.m[12], this._worldMatrix.m[13], this._worldMatrix.m[14]);\r\n        this._isAbsoluteSynced = false;\r\n\r\n        // Callbacks\r\n        this.onAfterWorldMatrixUpdateObservable.notifyObservers(this);\r\n\r\n        if (!this._poseMatrix) {\r\n            this._poseMatrix = Matrix.Invert(this._worldMatrix);\r\n        }\r\n\r\n        // Cache the determinant\r\n        this._worldMatrixDeterminantIsDirty = true;\r\n\r\n        return this._worldMatrix;\r\n    }\r\n\r\n    /**\r\n     * Resets this nodeTransform's local matrix to Matrix.Identity().\r\n     * @param independentOfChildren indicates if all child nodeTransform's world-space transform should be preserved.\r\n     */\r\n    public resetLocalMatrix(independentOfChildren: boolean = true): void {\r\n        this.computeWorldMatrix();\r\n        if (independentOfChildren) {\r\n            const children = this.getChildren();\r\n            for (let i = 0; i < children.length; ++i) {\r\n                const child = children[i] as TransformNode;\r\n                if (child) {\r\n                    child.computeWorldMatrix();\r\n                    const bakedMatrix = TmpVectors.Matrix[0];\r\n                    child._localMatrix.multiplyToRef(this._localMatrix, bakedMatrix);\r\n                    const tmpRotationQuaternion = TmpVectors.Quaternion[0];\r\n                    bakedMatrix.decompose(child.scaling, tmpRotationQuaternion, child.position);\r\n                    if (child.rotationQuaternion) {\r\n                        child.rotationQuaternion.copyFrom(tmpRotationQuaternion);\r\n                    } else {\r\n                        tmpRotationQuaternion.toEulerAnglesToRef(child.rotation);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        this.scaling.copyFromFloats(1, 1, 1);\r\n        this.position.copyFromFloats(0, 0, 0);\r\n        this.rotation.copyFromFloats(0, 0, 0);\r\n\r\n        //only if quaternion is already set\r\n        if (this.rotationQuaternion) {\r\n            this.rotationQuaternion = Quaternion.Identity();\r\n        }\r\n        this._worldMatrix = Matrix.Identity();\r\n    }\r\n\r\n    protected _afterComputeWorldMatrix(): void {}\r\n\r\n    /**\r\n     * If you'd like to be called back after the mesh position, rotation or scaling has been updated.\r\n     * @param func callback function to add\r\n     *\r\n     * @returns the TransformNode.\r\n     */\r\n    public registerAfterWorldMatrixUpdate(func: (mesh: TransformNode) => void): TransformNode {\r\n        this.onAfterWorldMatrixUpdateObservable.add(func);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Removes a registered callback function.\r\n     * @param func callback function to remove\r\n     * @returns the TransformNode.\r\n     */\r\n    public unregisterAfterWorldMatrixUpdate(func: (mesh: TransformNode) => void): TransformNode {\r\n        this.onAfterWorldMatrixUpdateObservable.removeCallback(func);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets the position of the current mesh in camera space\r\n     * @param camera defines the camera to use\r\n     * @returns a position\r\n     */\r\n    public getPositionInCameraSpace(camera: Nullable<Camera> = null): Vector3 {\r\n        if (!camera) {\r\n            camera = <Camera>this.getScene().activeCamera;\r\n        }\r\n\r\n        return Vector3.TransformCoordinates(this.getAbsolutePosition(), camera.getViewMatrix());\r\n    }\r\n\r\n    /**\r\n     * Returns the distance from the mesh to the active camera\r\n     * @param camera defines the camera to use\r\n     * @returns the distance\r\n     */\r\n    public getDistanceToCamera(camera: Nullable<Camera> = null): number {\r\n        if (!camera) {\r\n            camera = <Camera>this.getScene().activeCamera;\r\n        }\r\n        return this.getAbsolutePosition().subtract(camera.globalPosition).length();\r\n    }\r\n\r\n    /**\r\n     * Clone the current transform node\r\n     * @param name Name of the new clone\r\n     * @param newParent New parent for the clone\r\n     * @param doNotCloneChildren Do not clone children hierarchy\r\n     * @returns the new transform node\r\n     */\r\n    public clone(name: string, newParent: Nullable<Node>, doNotCloneChildren?: boolean): Nullable<TransformNode> {\r\n        const result = SerializationHelper.Clone(() => new TransformNode(name, this.getScene()), this);\r\n\r\n        result.name = name;\r\n        result.id = name;\r\n\r\n        if (newParent) {\r\n            result.parent = newParent;\r\n        }\r\n\r\n        if (!doNotCloneChildren) {\r\n            // Children\r\n            const directDescendants = this.getDescendants(true);\r\n            for (let index = 0; index < directDescendants.length; index++) {\r\n                const child = directDescendants[index];\r\n\r\n                if ((<any>child).clone) {\r\n                    (<any>child).clone(name + \".\" + child.name, result);\r\n                }\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Serializes the objects information.\r\n     * @param currentSerializationObject defines the object to serialize in\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(currentSerializationObject?: any): any {\r\n        const serializationObject = SerializationHelper.Serialize(this, currentSerializationObject);\r\n        serializationObject.type = this.getClassName();\r\n        serializationObject.uniqueId = this.uniqueId;\r\n\r\n        // Parent\r\n        if (this.parent) {\r\n            this.parent._serializeAsParent(serializationObject);\r\n        }\r\n\r\n        serializationObject.localMatrix = this.getPivotMatrix().asArray();\r\n\r\n        serializationObject.isEnabled = this.isEnabled();\r\n\r\n        // Animations\r\n        SerializationHelper.AppendSerializedAnimations(this, serializationObject);\r\n        serializationObject.ranges = this.serializeAnimationRanges();\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * Returns a new TransformNode object parsed from the source provided.\r\n     * @param parsedTransformNode is the source.\r\n     * @param scene the scene the object belongs to\r\n     * @param rootUrl is a string, it's the root URL to prefix the `delayLoadingFile` property with\r\n     * @returns a new TransformNode object parsed from the source provided.\r\n     */\r\n    public static Parse(parsedTransformNode: any, scene: Scene, rootUrl: string): TransformNode {\r\n        const transformNode = SerializationHelper.Parse(() => new TransformNode(parsedTransformNode.name, scene), parsedTransformNode, scene, rootUrl);\r\n\r\n        if (parsedTransformNode.localMatrix) {\r\n            transformNode.setPreTransformMatrix(Matrix.FromArray(parsedTransformNode.localMatrix));\r\n        } else if (parsedTransformNode.pivotMatrix) {\r\n            transformNode.setPivotMatrix(Matrix.FromArray(parsedTransformNode.pivotMatrix));\r\n        }\r\n\r\n        transformNode.setEnabled(parsedTransformNode.isEnabled);\r\n\r\n        transformNode._waitingParsedUniqueId = parsedTransformNode.uniqueId;\r\n\r\n        // Parent\r\n        if (parsedTransformNode.parentId !== undefined) {\r\n            transformNode._waitingParentId = parsedTransformNode.parentId;\r\n        }\r\n\r\n        if (parsedTransformNode.parentInstanceIndex !== undefined) {\r\n            transformNode._waitingParentInstanceIndex = parsedTransformNode.parentInstanceIndex;\r\n        }\r\n\r\n        // Animations\r\n        if (parsedTransformNode.animations) {\r\n            for (let animationIndex = 0; animationIndex < parsedTransformNode.animations.length; animationIndex++) {\r\n                const parsedAnimation = parsedTransformNode.animations[animationIndex];\r\n                const internalClass = GetClass(\"BABYLON.Animation\");\r\n                if (internalClass) {\r\n                    transformNode.animations.push(internalClass.Parse(parsedAnimation));\r\n                }\r\n            }\r\n            Node.ParseAnimationRanges(transformNode, parsedTransformNode, scene);\r\n        }\r\n\r\n        if (parsedTransformNode.autoAnimate) {\r\n            scene.beginAnimation(\r\n                transformNode,\r\n                parsedTransformNode.autoAnimateFrom,\r\n                parsedTransformNode.autoAnimateTo,\r\n                parsedTransformNode.autoAnimateLoop,\r\n                parsedTransformNode.autoAnimateSpeed || 1.0\r\n            );\r\n        }\r\n\r\n        return transformNode;\r\n    }\r\n\r\n    /**\r\n     * Get all child-transformNodes of this node\r\n     * @param directDescendantsOnly defines if true only direct descendants of 'this' will be considered, if false direct and also indirect (children of children, an so on in a recursive manner) descendants of 'this' will be considered\r\n     * @param predicate defines an optional predicate that will be called on every evaluated child, the predicate must return true for a given child to be part of the result, otherwise it will be ignored\r\n     * @returns an array of TransformNode\r\n     */\r\n    public getChildTransformNodes(directDescendantsOnly?: boolean, predicate?: (node: Node) => boolean): TransformNode[] {\r\n        const results: Array<TransformNode> = [];\r\n        this._getDescendants(results, directDescendantsOnly, (node: Node) => {\r\n            return (!predicate || predicate(node)) && node instanceof TransformNode;\r\n        });\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Releases resources associated with this transform node.\r\n     * @param doNotRecurse Set to true to not recurse into each children (recurse into each children by default)\r\n     * @param disposeMaterialAndTextures Set to true to also dispose referenced materials and textures (false by default)\r\n     */\r\n    public dispose(doNotRecurse?: boolean, disposeMaterialAndTextures = false): void {\r\n        // Animations\r\n        this.getScene().stopAnimation(this);\r\n\r\n        // Remove from scene\r\n        this.getScene().removeTransformNode(this);\r\n\r\n        if (this._parentContainer) {\r\n            const index = this._parentContainer.transformNodes.indexOf(this);\r\n            if (index > -1) {\r\n                this._parentContainer.transformNodes.splice(index, 1);\r\n            }\r\n            this._parentContainer = null;\r\n        }\r\n\r\n        this.onAfterWorldMatrixUpdateObservable.clear();\r\n\r\n        if (doNotRecurse) {\r\n            const transformNodes = this.getChildTransformNodes(true);\r\n            for (const transformNode of transformNodes) {\r\n                transformNode.parent = null;\r\n                transformNode.computeWorldMatrix(true);\r\n            }\r\n        }\r\n\r\n        super.dispose(doNotRecurse, disposeMaterialAndTextures);\r\n    }\r\n\r\n    /**\r\n     * Uniformly scales the mesh to fit inside of a unit cube (1 X 1 X 1 units)\r\n     * @param includeDescendants Use the hierarchy's bounding box instead of the mesh's bounding box. Default is false\r\n     * @param ignoreRotation ignore rotation when computing the scale (ie. object will be axis aligned). Default is false\r\n     * @param predicate predicate that is passed in to getHierarchyBoundingVectors when selecting which object should be included when scaling\r\n     * @returns the current mesh\r\n     */\r\n    public normalizeToUnitCube(includeDescendants = true, ignoreRotation = false, predicate?: Nullable<(node: AbstractMesh) => boolean>): TransformNode {\r\n        let storedRotation: Nullable<Vector3> = null;\r\n        let storedRotationQuaternion: Nullable<Quaternion> = null;\r\n\r\n        if (ignoreRotation) {\r\n            if (this.rotationQuaternion) {\r\n                storedRotationQuaternion = this.rotationQuaternion.clone();\r\n                this.rotationQuaternion.copyFromFloats(0, 0, 0, 1);\r\n            } else if (this.rotation) {\r\n                storedRotation = this.rotation.clone();\r\n                this.rotation.copyFromFloats(0, 0, 0);\r\n            }\r\n        }\r\n\r\n        const boundingVectors = this.getHierarchyBoundingVectors(includeDescendants, predicate);\r\n        const sizeVec = boundingVectors.max.subtract(boundingVectors.min);\r\n        const maxDimension = Math.max(sizeVec.x, sizeVec.y, sizeVec.z);\r\n\r\n        if (maxDimension === 0) {\r\n            return this;\r\n        }\r\n\r\n        const scale = 1 / maxDimension;\r\n\r\n        this.scaling.scaleInPlace(scale);\r\n\r\n        if (ignoreRotation) {\r\n            if (this.rotationQuaternion && storedRotationQuaternion) {\r\n                this.rotationQuaternion.copyFrom(storedRotationQuaternion);\r\n            } else if (this.rotation && storedRotation) {\r\n                this.rotation.copyFrom(storedRotation);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    private _syncAbsoluteScalingAndRotation(): void {\r\n        if (!this._isAbsoluteSynced) {\r\n            this._worldMatrix.decompose(this._absoluteScaling, this._absoluteRotationQuaternion);\r\n            this._isAbsoluteSynced = true;\r\n        }\r\n    }\r\n}\r\n"]}