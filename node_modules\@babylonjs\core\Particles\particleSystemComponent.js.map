{"version": 3, "file": "particleSystemComponent.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Particles/particleSystemComponent.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AAEtC,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAEjD,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAG5D,OAAO,6BAA6B,CAAC;AAGrC,yCAAyC;AACzC,aAAa,CAAC,SAAS,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,CAAC,UAAe,EAAE,KAAY,EAAE,SAAyB,EAAE,OAAe,EAAE,EAAE;IAC/I,MAAM,gBAAgB,GAAG,aAAa,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,CAAC;IAExG,IAAI,CAAC,gBAAgB,EAAE;QACnB,OAAO;KACV;IAED,oBAAoB;IACpB,IAAI,UAAU,CAAC,eAAe,KAAK,SAAS,IAAI,UAAU,CAAC,eAAe,KAAK,IAAI,EAAE;QACjF,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE;YACnF,MAAM,oBAAoB,GAAG,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC/D,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;SAC1F;KACJ;AACL,CAAC,CAAC,CAAC;AAEH,aAAa,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,CAAC,oBAAyB,EAAE,KAAY,EAAE,OAAe,EAAE,EAAE;IACxI,IAAI,oBAAoB,CAAC,mBAAmB,EAAE;QAC1C,MAAM,EAAE,GAAG,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACzE,OAAO,EAAE,CAAC;KACb;SAAM;QACH,MAAM,EAAE,GAAG,cAAc,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACtE,OAAO,EAAE,CAAC;KACb;AACL,CAAC,CAAC,CAAC;AA+BH,MAAM,CAAC,SAAS,CAAC,wBAAwB,GAAG,UACxC,YAAoB,EACpB,gBAA0B,EAAE,EAC5B,WAAqB,EAAE,EACvB,OAAO,GAAG,EAAE,EACZ,SAA2B,EAC3B,UAAqC,EACrC,OAAkD,EAClD,cAAgC;IAEhC,IAAI,wBAAwB,GAAkB,EAAE,CAAC;IACjD,IAAI,oBAAoB,GAAkB,EAAE,CAAC;IAC7C,MAAM,WAAW,GAAkB,EAAE,CAAC;IAEtC,IAAI,cAAc,EAAE;QAChB,cAAc,CAAC,qCAAqC,CAAC,oBAAoB,EAAE,wBAAwB,EAAE,WAAW,CAAC,CAAC;KACrH;SAAM;QACH,wBAAwB,GAAG,cAAc,CAAC,2BAA2B,EAAE,CAAC;QACxE,oBAAoB,GAAG,cAAc,CAAC,yBAAyB,EAAE,CAAC;KACrE;IAED,IAAI,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;QACtC,OAAO,IAAI,uBAAuB,CAAC;KACtC;IAED,IAAI,cAAc,EAAE,uBAAuB,EAAE;QACzC,IAAI,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;YACzC,OAAO,IAAI,0BAA0B,CAAC;SACzC;KACJ;IAED,IAAI,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE;QAC3C,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;KACnC;IAED,OAAO,IAAI,CAAC,YAAY,CACpB;QACI,MAAM,EAAE,cAAc,EAAE,gBAAgB,IAAI,WAAW;QACvD,eAAe,EAAE,YAAY;KAChC,EACD,wBAAwB,EACxB,oBAAoB,CAAC,MAAM,CAAC,aAAa,CAAC,EAC1C,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,EAC5B,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,CACV,CAAC;AACN,CAAC,CAAC;AAkBF,IAAI,CAAC,SAAS,CAAC,yBAAyB,GAAG;IACvC,MAAM,OAAO,GAAsB,EAAE,CAAC;IACtC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QACzE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC9D,IAAI,cAAc,CAAC,OAAO,KAAK,IAAI,EAAE;YACjC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAChC;KACJ;IACD,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,kCAAkC,GAAG;IAChD,MAAM,OAAO,GAAsB,EAAE,CAAC;IACtC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEvB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QACzE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC9D,MAAM,OAAO,GAAQ,cAAc,CAAC,OAAO,CAAC;QAE5C,IAAI,OAAO,CAAC,QAAQ,IAAI,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;YACzD,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAChC;KACJ;IAED,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC", "sourcesContent": ["import { Mesh } from \"../Meshes/mesh\";\r\nimport type { IParticleSystem } from \"./IParticleSystem\";\r\nimport { GPUParticleSystem } from \"./gpuParticleSystem\";\r\nimport { AbstractScene } from \"../abstractScene\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Engine } from \"../Engines/engine\";\r\nimport { ParticleSystem } from \"./particleSystem\";\r\nimport type { Scene } from \"../scene\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport type { AssetContainer } from \"../assetContainer\";\r\n\r\nimport \"../Shaders/particles.vertex\";\r\nimport type { EffectFallbacks } from \"../Materials/effectFallbacks\";\r\n\r\n// Adds the parsers to the scene parsers.\r\nAbstractScene.AddParser(SceneComponentConstants.NAME_PARTICLESYSTEM, (parsedData: any, scene: Scene, container: AssetContainer, rootUrl: string) => {\r\n    const individualParser = AbstractScene.GetIndividualParser(SceneComponentConstants.NAME_PARTICLESYSTEM);\r\n\r\n    if (!individualParser) {\r\n        return;\r\n    }\r\n\r\n    // Particles Systems\r\n    if (parsedData.particleSystems !== undefined && parsedData.particleSystems !== null) {\r\n        for (let index = 0, cache = parsedData.particleSystems.length; index < cache; index++) {\r\n            const parsedParticleSystem = parsedData.particleSystems[index];\r\n            container.particleSystems.push(individualParser(parsedParticleSystem, scene, rootUrl));\r\n        }\r\n    }\r\n});\r\n\r\nAbstractScene.AddIndividualParser(SceneComponentConstants.NAME_PARTICLESYSTEM, (parsedParticleSystem: any, scene: Scene, rootUrl: string) => {\r\n    if (parsedParticleSystem.activeParticleCount) {\r\n        const ps = GPUParticleSystem.Parse(parsedParticleSystem, scene, rootUrl);\r\n        return ps;\r\n    } else {\r\n        const ps = ParticleSystem.Parse(parsedParticleSystem, scene, rootUrl);\r\n        return ps;\r\n    }\r\n});\r\n\r\ndeclare module \"../Engines/engine\" {\r\n    export interface Engine {\r\n        /**\r\n         * Create an effect to use with particle systems.\r\n         * Please note that some parameters like animation sheets or not being billboard are not supported in this configuration, except if you pass\r\n         * the particle system for which you want to create a custom effect in the last parameter\r\n         * @param fragmentName defines the base name of the effect (The name of file without .fragment.fx)\r\n         * @param uniformsNames defines a list of attribute names\r\n         * @param samplers defines an array of string used to represent textures\r\n         * @param defines defines the string containing the defines to use to compile the shaders\r\n         * @param fallbacks defines the list of potential fallbacks to use if shader compilation fails\r\n         * @param onCompiled defines a function to call when the effect creation is successful\r\n         * @param onError defines a function to call when the effect creation has failed\r\n         * @param particleSystem the particle system you want to create the effect for\r\n         * @returns the new Effect\r\n         */\r\n        createEffectForParticles(\r\n            fragmentName: string,\r\n            uniformsNames: string[],\r\n            samplers: string[],\r\n            defines: string,\r\n            fallbacks?: EffectFallbacks,\r\n            onCompiled?: (effect: Effect) => void,\r\n            onError?: (effect: Effect, errors: string) => void,\r\n            particleSystem?: IParticleSystem\r\n        ): Effect;\r\n    }\r\n}\r\n\r\nEngine.prototype.createEffectForParticles = function (\r\n    fragmentName: string,\r\n    uniformsNames: string[] = [],\r\n    samplers: string[] = [],\r\n    defines = \"\",\r\n    fallbacks?: EffectFallbacks,\r\n    onCompiled?: (effect: Effect) => void,\r\n    onError?: (effect: Effect, errors: string) => void,\r\n    particleSystem?: IParticleSystem\r\n): Effect {\r\n    let attributesNamesOrOptions: Array<string> = [];\r\n    let effectCreationOption: Array<string> = [];\r\n    const allSamplers: Array<string> = [];\r\n\r\n    if (particleSystem) {\r\n        particleSystem.fillUniformsAttributesAndSamplerNames(effectCreationOption, attributesNamesOrOptions, allSamplers);\r\n    } else {\r\n        attributesNamesOrOptions = ParticleSystem._GetAttributeNamesOrOptions();\r\n        effectCreationOption = ParticleSystem._GetEffectCreationOptions();\r\n    }\r\n\r\n    if (defines.indexOf(\" BILLBOARD\") === -1) {\r\n        defines += \"\\n#define BILLBOARD\\n\";\r\n    }\r\n\r\n    if (particleSystem?.isAnimationSheetEnabled) {\r\n        if (defines.indexOf(\" ANIMATESHEET\") === -1) {\r\n            defines += \"\\n#define ANIMATESHEET\\n\";\r\n        }\r\n    }\r\n\r\n    if (samplers.indexOf(\"diffuseSampler\") === -1) {\r\n        samplers.push(\"diffuseSampler\");\r\n    }\r\n\r\n    return this.createEffect(\r\n        {\r\n            vertex: particleSystem?.vertexShaderName ?? \"particles\",\r\n            fragmentElement: fragmentName,\r\n        },\r\n        attributesNamesOrOptions,\r\n        effectCreationOption.concat(uniformsNames),\r\n        allSamplers.concat(samplers),\r\n        defines,\r\n        fallbacks,\r\n        onCompiled,\r\n        onError\r\n    );\r\n};\r\n\r\ndeclare module \"../Meshes/mesh\" {\r\n    export interface Mesh {\r\n        /**\r\n         * Returns an array populated with IParticleSystem objects whose the mesh is the emitter\r\n         * @returns an array of IParticleSystem\r\n         */\r\n        getEmittedParticleSystems(): IParticleSystem[];\r\n\r\n        /**\r\n         * Returns an array populated with IParticleSystem objects whose the mesh or its children are the emitter\r\n         * @returns an array of IParticleSystem\r\n         */\r\n        getHierarchyEmittedParticleSystems(): IParticleSystem[];\r\n    }\r\n}\r\n\r\nMesh.prototype.getEmittedParticleSystems = function (): IParticleSystem[] {\r\n    const results: IParticleSystem[] = [];\r\n    for (let index = 0; index < this.getScene().particleSystems.length; index++) {\r\n        const particleSystem = this.getScene().particleSystems[index];\r\n        if (particleSystem.emitter === this) {\r\n            results.push(particleSystem);\r\n        }\r\n    }\r\n    return results;\r\n};\r\n\r\nMesh.prototype.getHierarchyEmittedParticleSystems = function (): IParticleSystem[] {\r\n    const results: IParticleSystem[] = [];\r\n    const descendants = this.getDescendants();\r\n    descendants.push(this);\r\n\r\n    for (let index = 0; index < this.getScene().particleSystems.length; index++) {\r\n        const particleSystem = this.getScene().particleSystems[index];\r\n        const emitter: any = particleSystem.emitter;\r\n\r\n        if (emitter.position && descendants.indexOf(emitter) !== -1) {\r\n            results.push(particleSystem);\r\n        }\r\n    }\r\n\r\n    return results;\r\n};\r\n"]}