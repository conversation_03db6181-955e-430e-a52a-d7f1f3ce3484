{"version": 3, "file": "subSurfaceConfiguration.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Rendering/subSurfaceConfiguration.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,+BAA+B,EAAE,MAAM,kDAAkD,CAAC;AACnG,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAE5D,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEjD;;;GAGG;AACH,MAAM,OAAO,uBAAuB;IAiBhC;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IA2CD;;;OAGG;IACH,YAAY,KAAY;QA3EhB,kBAAa,GAAa,EAAE,CAAC;QAC7B,mBAAc,GAAa,EAAE,CAAC;QAC9B,kBAAa,GAAa,EAAE,CAAC;QA4BrC;;WAEG;QACI,YAAO,GAAG,KAAK,CAAC;QAEvB;;WAEG;QACI,yBAAoB,GAAG,IAAI,CAAC;QAEnC;;WAEG;QACI,SAAI,GAAG,uBAAuB,CAAC,eAAe,CAAC;QAEtD;;;;;WAKG;QACI,6BAAwB,GAAa,EAAE,CAAC;QAE/C;;;WAGG;QACI,kBAAa,GAAW,CAAC,CAAC;QAEjC;;WAEG;QACa,qBAAgB,GAAa;YACzC,SAAS,CAAC,0BAA0B;YACpC,SAAS,CAAC,gCAAgC;YAC1C,SAAS,CAAC,0BAA0B;YACpC,SAAS,CAAC,+BAA+B;SAC5C,CAAC;QASE,mCAAmC;QACnC,IAAI,CAAC,mBAAmB,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,uBAAuB,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACvE,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,KAAa;QACpC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE;YAC/B,wCAAwC;YACxC,MAAM,CAAC,KAAK,CAAC,+DAA+D,CAAC,CAAC;YAC9E,OAAO,CAAC,CAAC,CAAC,kBAAkB;SAC/B;QAED,qBAAqB;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACpD,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;gBACjI,OAAO,CAAC,CAAC;aACZ;SACJ;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACvE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC,CAAC;QACpE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE1C,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IACzC,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,IAAI,CAAC,WAAW,GAAG,IAAI,+BAA+B,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;QACzI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC;QAEnC,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,yBAAyB;QAC5B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,wBAAwB,GAAG,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;SAC9B;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACI,6BAA6B,CAAC,KAAa;QAC9C,MAAM,GAAG,GAAG,KAAK,CAAC;QAClB,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QAElE,OAAO,IAAI,CAAC,6BAA6B,CAAC,GAAG,EAAE,qBAAqB,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;;;;OAQG;IACK,6BAA6B,CAAC,CAAS,EAAE,IAAY;QACzD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,sBAAsB;QAEjC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,WAAW;QAC9C,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW;QAChC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,0BAA0B;QAC/C,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAEpC,OAAO,CAAC,GAAG,IAAI,CAAC;IACpB,CAAC;;AA5LD;;GAEG;AACW,qDAA6B,GAA2B,CAAC,CAAC,EAAE,EAAE;IACxE,MAAM,WAAW,CAAC,0BAA0B,CAAC,CAAC;AAClD,CAAC,AAF0C,CAEzC", "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { SubSurfaceScatteringPostProcess } from \"../PostProcesses/subSurfaceScatteringPostProcess\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport type { PrePassEffectConfiguration } from \"./prePassEffectConfiguration\";\r\nimport { _WarnImport } from \"../Misc/devTools\";\r\nimport { Constants } from \"../Engines/constants\";\r\n\r\n/**\r\n * Contains all parameters needed for the prepass to perform\r\n * screen space subsurface scattering\r\n */\r\nexport class SubSurfaceConfiguration implements PrePassEffectConfiguration {\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _SceneComponentInitialization: (scene: Scene) => void = (_) => {\r\n        throw _WarnImport(\"SubSurfaceSceneComponent\");\r\n    };\r\n\r\n    private _ssDiffusionS: number[] = [];\r\n    private _ssFilterRadii: number[] = [];\r\n    private _ssDiffusionD: number[] = [];\r\n\r\n    /**\r\n     * Post process to attach for screen space subsurface scattering\r\n     */\r\n    public postProcess: SubSurfaceScatteringPostProcess;\r\n\r\n    /**\r\n     * Diffusion profile color for subsurface scattering\r\n     */\r\n    public get ssDiffusionS() {\r\n        return this._ssDiffusionS;\r\n    }\r\n\r\n    /**\r\n     * Diffusion profile max color channel value for subsurface scattering\r\n     */\r\n    public get ssDiffusionD() {\r\n        return this._ssDiffusionD;\r\n    }\r\n\r\n    /**\r\n     * Diffusion profile filter radius for subsurface scattering\r\n     */\r\n    public get ssFilterRadii() {\r\n        return this._ssFilterRadii;\r\n    }\r\n\r\n    /**\r\n     * Is subsurface enabled\r\n     */\r\n    public enabled = false;\r\n\r\n    /**\r\n     * Does the output of this prepass need to go through imageprocessing\r\n     */\r\n    public needsImageProcessing = true;\r\n\r\n    /**\r\n     * Name of the configuration\r\n     */\r\n    public name = SceneComponentConstants.NAME_SUBSURFACE;\r\n\r\n    /**\r\n     * Diffusion profile colors for subsurface scattering\r\n     * You can add one diffusion color using `addDiffusionProfile` on `scene.prePassRenderer`\r\n     * See ...\r\n     * Note that you can only store up to 5 of them\r\n     */\r\n    public ssDiffusionProfileColors: Color3[] = [];\r\n\r\n    /**\r\n     * Defines the ratio real world => scene units.\r\n     * Used for subsurface scattering\r\n     */\r\n    public metersPerUnit: number = 1;\r\n\r\n    /**\r\n     * Textures that should be present in the MRT for this effect to work\r\n     */\r\n    public readonly texturesRequired: number[] = [\r\n        Constants.PREPASS_DEPTH_TEXTURE_TYPE,\r\n        Constants.PREPASS_ALBEDO_SQRT_TEXTURE_TYPE,\r\n        Constants.PREPASS_COLOR_TEXTURE_TYPE,\r\n        Constants.PREPASS_IRRADIANCE_TEXTURE_TYPE,\r\n    ];\r\n\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * Builds a subsurface configuration object\r\n     * @param scene The scene\r\n     */\r\n    constructor(scene: Scene) {\r\n        // Adding default diffusion profile\r\n        this.addDiffusionProfile(new Color3(1, 1, 1));\r\n        this._scene = scene;\r\n\r\n        SubSurfaceConfiguration._SceneComponentInitialization(this._scene);\r\n    }\r\n\r\n    /**\r\n     * Adds a new diffusion profile.\r\n     * Useful for more realistic subsurface scattering on diverse materials.\r\n     * @param color The color of the diffusion profile. Should be the average color of the material.\r\n     * @returns The index of the diffusion profile for the material subsurface configuration\r\n     */\r\n    public addDiffusionProfile(color: Color3): number {\r\n        if (this.ssDiffusionD.length >= 5) {\r\n            // We only suppport 5 diffusion profiles\r\n            Logger.Error(\"You already reached the maximum number of diffusion profiles.\");\r\n            return 0; // default profile\r\n        }\r\n\r\n        // Do not add doubles\r\n        for (let i = 0; i < this._ssDiffusionS.length / 3; i++) {\r\n            if (this._ssDiffusionS[i * 3] === color.r && this._ssDiffusionS[i * 3 + 1] === color.g && this._ssDiffusionS[i * 3 + 2] === color.b) {\r\n                return i;\r\n            }\r\n        }\r\n\r\n        this._ssDiffusionS.push(color.r, color.b, color.g);\r\n        this._ssDiffusionD.push(Math.max(Math.max(color.r, color.b), color.g));\r\n        this._ssFilterRadii.push(this.getDiffusionProfileParameters(color));\r\n        this.ssDiffusionProfileColors.push(color);\r\n\r\n        return this._ssDiffusionD.length - 1;\r\n    }\r\n\r\n    /**\r\n     * Creates the sss post process\r\n     * @returns The created post process\r\n     */\r\n    public createPostProcess(): SubSurfaceScatteringPostProcess {\r\n        this.postProcess = new SubSurfaceScatteringPostProcess(\"subSurfaceScattering\", this._scene, 1, null, undefined, this._scene.getEngine());\r\n        this.postProcess.autoClear = false;\r\n\r\n        return this.postProcess;\r\n    }\r\n\r\n    /**\r\n     * Deletes all diffusion profiles.\r\n     * Note that in order to render subsurface scattering, you should have at least 1 diffusion profile.\r\n     */\r\n    public clearAllDiffusionProfiles() {\r\n        this._ssDiffusionD = [];\r\n        this._ssDiffusionS = [];\r\n        this._ssFilterRadii = [];\r\n        this.ssDiffusionProfileColors = [];\r\n    }\r\n\r\n    /**\r\n     * Disposes this object\r\n     */\r\n    public dispose() {\r\n        this.clearAllDiffusionProfiles();\r\n        if (this.postProcess) {\r\n            this.postProcess.dispose();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * https://zero-radiance.github.io/post/sampling-diffusion/\r\n     *\r\n     * Importance sample the normalized diffuse reflectance profile for the computed value of 's'.\r\n     * ------------------------------------------------------------------------------------\r\n     * R[r, phi, s]   = s * (Exp[-r * s] + Exp[-r * s / 3]) / (8 * Pi * r)\r\n     * PDF[r, phi, s] = r * R[r, phi, s]\r\n     * CDF[r, s]      = 1 - 1/4 * Exp[-r * s] - 3/4 * Exp[-r * s / 3]\r\n     * ------------------------------------------------------------------------------------\r\n     * We importance sample the color channel with the widest scattering distance.\r\n     */\r\n    public getDiffusionProfileParameters(color: Color3) {\r\n        const cdf = 0.997;\r\n        const maxScatteringDistance = Math.max(color.r, color.g, color.b);\r\n\r\n        return this._sampleBurleyDiffusionProfile(cdf, maxScatteringDistance);\r\n    }\r\n\r\n    /**\r\n     * Performs sampling of a Normalized Burley diffusion profile in polar coordinates.\r\n     * 'u' is the random number (the value of the CDF): [0, 1).\r\n     * rcp(s) = 1 / ShapeParam = ScatteringDistance.\r\n     * Returns the sampled radial distance, s.t. (u = 0 -> r = 0) and (u = 1 -> r = Inf).\r\n     * @param u\r\n     * @param rcpS\r\n     * @returns The sampled radial distance\r\n     */\r\n    private _sampleBurleyDiffusionProfile(u: number, rcpS: number) {\r\n        u = 1 - u; // Convert CDF to CCDF\r\n\r\n        const g = 1 + 4 * u * (2 * u + Math.sqrt(1 + 4 * u * u));\r\n        const n = Math.pow(g, -1.0 / 3.0); // g^(-1/3)\r\n        const p = g * n * n; // g^(+1/3)\r\n        const c = 1 + p + n; // 1 + g^(+1/3) + g^(-1/3)\r\n        const x = 3 * Math.log(c / (4 * u));\r\n\r\n        return x * rcpS;\r\n    }\r\n}\r\n"]}