{"version": 3, "file": "nativeRenderTargetWrapper.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Native/nativeRenderTargetWrapper.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;AAI7D,MAAM,OAAO,yBAA0B,SAAQ,mBAAmB;IAQ9D,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,YAAY,CAAC,WAAwC;QAC5D,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAC/D;QACD,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC;IACrC,CAAC;IAED,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IAED,IAAW,wBAAwB,CAAC,uBAAoD;QACpF,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;SAC3E;QACD,IAAI,CAAC,yBAAyB,GAAG,uBAAuB,CAAC;IAC7D,CAAC;IAED,YAAY,OAAgB,EAAE,MAAe,EAAE,IAAiB,EAAE,MAAoB;QAClF,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QA5BzC,gEAAgE;QACxD,kBAAa,GAAgC,IAAI,CAAC;QAC1D,gEAAgE;QACxD,8BAAyB,GAAgC,IAAI,CAAC;QA0BlE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAEM,OAAO,CAAC,uBAAuB,GAAG,KAAK;QAC1C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAErC,KAAK,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;IAC3C,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { TextureSize } from \"../../Materials/Textures/textureCreationOptions\";\r\nimport { RenderTargetWrapper } from \"../renderTargetWrapper\";\r\nimport type { NativeEngine } from \"../nativeEngine\";\r\nimport type { NativeFramebuffer } from \"./nativeInterfaces\";\r\n\r\nexport class NativeRenderTargetWrapper extends RenderTargetWrapper {\r\n    public override readonly _engine: NativeEngine;\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __framebuffer: Nullable<NativeFramebuffer> = null;\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __framebufferDepthStencil: Nullable<NativeFramebuffer> = null;\r\n\r\n    public get _framebuffer(): Nullable<NativeFramebuffer> {\r\n        return this.__framebuffer;\r\n    }\r\n\r\n    public set _framebuffer(framebuffer: Nullable<NativeFramebuffer>) {\r\n        if (this.__framebuffer) {\r\n            this._engine._releaseFramebufferObjects(this.__framebuffer);\r\n        }\r\n        this.__framebuffer = framebuffer;\r\n    }\r\n\r\n    public get _framebufferDepthStencil(): Nullable<NativeFramebuffer> {\r\n        return this.__framebufferDepthStencil;\r\n    }\r\n\r\n    public set _framebufferDepthStencil(framebufferDepthStencil: Nullable<NativeFramebuffer>) {\r\n        if (this.__framebufferDepthStencil) {\r\n            this._engine._releaseFramebufferObjects(this.__framebufferDepthStencil);\r\n        }\r\n        this.__framebufferDepthStencil = framebufferDepthStencil;\r\n    }\r\n\r\n    constructor(isMulti: boolean, isCube: boolean, size: TextureSize, engine: NativeEngine) {\r\n        super(isMulti, isCube, size, engine);\r\n        this._engine = engine;\r\n    }\r\n\r\n    public dispose(disposeOnlyFramebuffers = false): void {\r\n        this._framebuffer = null;\r\n        this._framebufferDepthStencil = null;\r\n\r\n        super.dispose(disposeOnlyFramebuffers);\r\n    }\r\n}\r\n"]}