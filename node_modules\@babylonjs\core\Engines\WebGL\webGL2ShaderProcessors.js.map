{"version": 3, "file": "webGL2ShaderProcessors.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGL/webGL2ShaderProcessors.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAGhE,MAAM,YAAY,GAAG,0BAA0B,CAAC;AAEhD,gBAAgB;AAChB,MAAM,OAAO,qBAAqB;IAAlC;QACW,mBAAc,GAAG,cAAc,CAAC,IAAI,CAAC;IA0ChD,CAAC;IAxCU,kBAAkB,CAAC,SAAiB;QACvC,OAAO,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IAChD,CAAC;IAEM,YAAY,CAAC,OAAe,EAAE,WAAoB;QACrD,OAAO,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACtC,CAAC;IAEM,gBAAgB,CAAC,OAAe,EAAE,UAAmB;QACxD,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACjE,CAAC;IAEM,aAAa,CAAC,IAAY,EAAE,OAAiB,EAAE,UAAmB;QACrE,MAAM,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,0CAA0C,CAAC,KAAK,CAAC,CAAC,CAAC;QAE/F,oBAAoB;QACpB,MAAM,KAAK,GAAG,gJAAgJ,CAAC;QAC/J,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAE/B,uBAAuB;QACvB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;QACnD,IAAI,UAAU,EAAE;YACZ,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,KAAK,CAAC,CAAC,CAAC;YAEzE,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;YAC5D,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;YACrD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;YACxD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;YACpD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAClD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,uBAAuB,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,8CAA8C,CAAC,GAAG,YAAY,CAAC,CAAC;SAC1J;aAAM;YACH,MAAM,qBAAqB,GAAG,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1E,IAAI,qBAAqB,EAAE;gBACvB,OAAO,sEAAsE,GAAG,IAAI,CAAC;aACxF;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ", "sourcesContent": ["import { ShaderLanguage } from \"../../Materials/shaderLanguage\";\r\nimport type { IShaderProcessor } from \"../Processors/iShaderProcessor\";\r\n\r\nconst varyingRegex = /(flat\\s)?\\s*varying\\s*.*/;\r\n\r\n/** @internal */\r\nexport class WebGL2ShaderProcessor implements IShaderProcessor {\r\n    public shaderLanguage = ShaderLanguage.GLSL;\r\n\r\n    public attributeProcessor(attribute: string) {\r\n        return attribute.replace(\"attribute\", \"in\");\r\n    }\r\n\r\n    public varyingCheck(varying: string, _isFragment: boolean) {\r\n        return varyingRegex.test(varying);\r\n    }\r\n\r\n    public varyingProcessor(varying: string, isFragment: boolean) {\r\n        return varying.replace(\"varying\", isFragment ? \"in\" : \"out\");\r\n    }\r\n\r\n    public postProcessor(code: string, defines: string[], isFragment: boolean) {\r\n        const hasDrawBuffersExtension = code.search(/#extension.+GL_EXT_draw_buffers.+require/) !== -1;\r\n\r\n        // Remove extensions\r\n        const regex = /#extension.+(GL_OVR_multiview2|GL_OES_standard_derivatives|GL_EXT_shader_texture_lod|GL_EXT_frag_depth|GL_EXT_draw_buffers).+(enable|require)/g;\r\n        code = code.replace(regex, \"\");\r\n\r\n        // Replace instructions\r\n        code = code.replace(/texture2D\\s*\\(/g, \"texture(\");\r\n        if (isFragment) {\r\n            const hasOutput = code.search(/layout *\\(location *= *0\\) *out/g) !== -1;\r\n\r\n            code = code.replace(/texture2DLodEXT\\s*\\(/g, \"textureLod(\");\r\n            code = code.replace(/textureCubeLodEXT\\s*\\(/g, \"textureLod(\");\r\n            code = code.replace(/textureCube\\s*\\(/g, \"texture(\");\r\n            code = code.replace(/gl_FragDepthEXT/g, \"gl_FragDepth\");\r\n            code = code.replace(/gl_FragColor/g, \"glFragColor\");\r\n            code = code.replace(/gl_FragData/g, \"glFragData\");\r\n            code = code.replace(/void\\s+?main\\s*\\(/g, (hasDrawBuffersExtension || hasOutput ? \"\" : \"layout(location = 0) out vec4 glFragColor;\\n\") + \"void main(\");\r\n        } else {\r\n            const hasMultiviewExtension = defines.indexOf(\"#define MULTIVIEW\") !== -1;\r\n            if (hasMultiviewExtension) {\r\n                return \"#extension GL_OVR_multiview2 : require\\nlayout (num_views = 2) in;\\n\" + code;\r\n            }\r\n        }\r\n\r\n        return code;\r\n    }\r\n}\r\n"]}