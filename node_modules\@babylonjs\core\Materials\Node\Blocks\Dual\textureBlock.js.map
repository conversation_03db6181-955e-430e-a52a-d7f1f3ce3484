{"version": 3, "file": "textureBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Dual/textureBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,oCAAoC,EAAE,MAAM,wCAAwC,CAAC;AAG9F,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAGjD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAC;AAEpD,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAC1D,OAAO,oDAAoD,CAAC;AAC5D,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AACxG,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAG9D;;GAEG;AACH,MAAM,OAAO,YAAa,SAAQ,iBAAiB;IAe/C;;OAEG;IACH,IAAW,OAAO;QACd,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACzB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,UAA+B,CAAA,CAAC,OAAO,CAAC;SAC/E;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,OAA0B;QACzC,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;YAC3B,OAAO;SACV;QAED,MAAM,KAAK,GAAG,OAAO,EAAE,QAAQ,EAAE,IAAI,WAAW,CAAC,gBAAgB,CAAC;QAElE,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE;YACnB,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,QAAS,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,OAAO,IAAI,KAAK,EAAE;YAClB,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,KAAuD;QACzF,OAAO,KAAK,EAAE,YAAY,EAAE,KAAK,qBAAqB,CAAC;IAC3D,CAAC;IAED,IAAY,gBAAgB;QACxB,OAAO,YAAY,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;gBACzD,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;aACxC;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;gBAC5B,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;aACvE;SACJ;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACnC,CAAC;IAGD;;OAEG;IACH,IAAW,mBAAmB,CAAC,KAAc;QACzC,IAAI,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE;YACrC,OAAO;SACV;QAED,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,WAAW,CAAC,gBAAgB,CAAC;YACtE,KAAK,EAAE,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACxE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IACD,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAGD;;OAEG;IACH,IAAW,oBAAoB,CAAC,KAAc;QAC1C,IAAI,KAAK,KAAK,IAAI,CAAC,qBAAqB,EAAE;YACtC,OAAO;SACV;QAED,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,WAAW,CAAC,gBAAgB,CAAC;YACtE,KAAK,EAAE,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACxE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IACD,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAOD;;;;OAIG;IACH,YAAmB,IAAY,EAAE,YAAY,GAAG,KAAK;QACjD,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QArDvG,yBAAoB,GAAG,KAAK,CAAC;QAqB7B,0BAAqB,GAAG,KAAK,CAAC;QAqBtC;;WAEG;QACI,+BAA0B,GAAG,KAAK,CAAC;QAUtC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,qCAAqC,CAAC,UAAU,EAAE,KAAK,EAAE,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QAC9H,IAAI,CAAC,aAAa,CACd,QAAQ,EACR,qCAAqC,CAAC,MAAM,EAC5C,IAAI,EACJ,wBAAwB,CAAC,iBAAiB,EAC1C,IAAI,uCAAuC,CAAC,QAAQ,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,EAAE,gBAAgB,EAAE,kBAAkB,CAAC,CAChJ,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAE7E,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAC5G,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAC3G,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAExG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAE5G,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,CACtD,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,CAChJ,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,CAAC,YAAY,CAAC;IACtD,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,EAAE;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED,IAAW,MAAM;QACb,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,OAAO,wBAAwB,CAAC,QAAQ,CAAC;SAC5C;QAED,mIAAmI;QACnI,sDAAsD;QACtD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE;YACtB,OAAO,wBAAwB,CAAC,iBAAiB,CAAC;SACrD;QAED,IAAI,IAAI,CAAC,EAAE,CAAC,WAAY,CAAC,OAAO,EAAE;YAC9B,OAAO,wBAAwB,CAAC,iBAAiB,CAAC;SACrD;QAED,IAAI,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;QAEpC,OAAO,MAAM,EAAE;YACX,IAAI,MAAM,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;gBACrD,OAAO,wBAAwB,CAAC,QAAQ,CAAC;aAC5C;YAED,IAAI,MAAM,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,EAAE;gBACnD,OAAO,wBAAwB,CAAC,iBAAiB,CAAC;aACrD;YAED,IAAI,MAAM,CAAC,MAAM,KAAK,wBAAwB,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,KAAK,wBAAwB,CAAC,iBAAiB,EAAE;gBACpH,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC;gBAEtC,IAAI,WAAW,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;oBAC1D,OAAO,wBAAwB,CAAC,QAAQ,CAAC;iBAC5C;gBAED,MAAM,GAAG,IAAI,CAAC;gBACd,KAAK,MAAM,KAAK,IAAI,WAAW,CAAC,MAAM,EAAE;oBACpC,IAAI,KAAK,CAAC,cAAc,EAAE;wBACtB,MAAM,GAAG,KAAK,CAAC,cAAc,CAAC;wBAC9B,MAAM;qBACT;iBACJ;aACJ;SACJ;QAED,OAAO,wBAAwB,CAAC,iBAAiB,CAAC;IACtD,CAAC;IAED,IAAW,MAAM,CAAC,KAA+B,IAAG,CAAC;IAE9C,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,EAAE,CAAC,IAAI;QACnH,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE;YACtB,IAAI,QAAQ,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,EAAE;gBACjD,MAAM,OAAO,GAAG,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEnG,IAAI,OAAO,EAAE;oBACT,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;iBAC3B;aACJ;iBAAM;gBACH,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,KAAK,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC;gBAE1F,IAAI,OAAO,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,aAAa,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEhI,IAAI,CAAC,OAAO,EAAE;oBACV,OAAO,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;oBAC/B,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;iBACzC;gBACD,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACrC;SACJ;IACL,CAAC;IAEM,iBAAiB,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QACjG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAC5B,OAAO;SACV;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE;YACtC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SACzD;IACL,CAAC;IAEM,cAAc,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QAC9F,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAC5B,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YACjD,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;gBAChD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;aACxD;YACD,OAAO;SACV;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;QACrF,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;QAEtF,0EAA0E;QAC1E,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACxD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAExD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,eAAe,EAAE,EAAE;gBACpD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBACzC,IAAI,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,SAAS,EAAE;oBAC9C,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;iBACzD;aACJ;iBAAM;gBACH,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;gBAChD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;aACxD;SACJ;IACL,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE;YACtD,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,IAAI,CAAC,MAAc;QACtB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;SACjF;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACtD;IACL,CAAC;IAED,IAAY,QAAQ;QAChB,OAAO,IAAI,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,CAAC;IAC7D,CAAC;IAEO,iBAAiB,CAAC,KAA6B;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC;QAExB,wBAAwB;QACxB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;QAC3D,IAAI,CAAC,iBAAiB,GAAG,OAAO,GAAG,OAAO,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC;QAEhF,IAAI,CAAC,WAAW,GAAG,OAAO,GAAG,OAAO,CAAC,sBAAsB,CAAC;QAC5D,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QACtE,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAC5E,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAEtE,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE1D,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAChF,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAE/E,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,EAAE,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAEnF,KAAK,CAAC,iBAAiB,IAAI,UAAU,IAAI,CAAC,WAAW,IAAI,CAAC;QAC1D,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,kBAAkB,WAAW,IAAI,CAAC,qBAAqB,WAAW,OAAO,CAAC,sBAAsB,oBAAoB,CAAC;QACxJ,KAAK,CAAC,iBAAiB,IAAI,iBAAiB,IAAI,CAAC,iBAAiB,KAAK,CAAC;QACxE,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,WAAW,MAAM,OAAO,CAAC,sBAAsB,QAAQ,CAAC;QAC3F,KAAK,CAAC,iBAAiB,IAAI,UAAU,CAAC;QAEtC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,yBAAyB,CAAC,EAAE;YACzD,OAAO;SACV;QAED,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAEpC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;gBAChD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACvD;SACJ;IACL,CAAC;IAEO,OAAO,CAAC,MAAc;QAC1B,IAAI,MAAM,GAAG,MAAM,CAAC;QAEpB,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,IAAI,KAAK,CAAC;QAErE,IAAI,gBAAgB,EAAE;YAClB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,CAAC;YACpF,MAAM,GAAG,QAAQ,MAAM,KAAK,UAAU,GAAG,CAAC;SAC7C;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,IAAY,YAAY;QACpB,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,WAAW,CAAC;IAClE,CAAC;IAED,IAAY,iBAAiB;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC9E,CAAC;IAEO,sBAAsB,CAAC,KAA6B;QACxD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QAErC,KAAK,CAAC,iBAAiB,IAAI,UAAU,IAAI,CAAC,WAAW,IAAI,CAAC;QAC1D,KAAK,CAAC,iBAAiB,IAAI,QAAQ,IAAI,CAAC,gBAAgB,MAAM,IAAI,CAAC,YAAY,IAAI,WAAW,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC,iBAAiB,MAAM,CAAC;QACxK,KAAK,CAAC,iBAAiB,IAAI,iBAAiB,IAAI,CAAC,iBAAiB,KAAK,CAAC;QACxE,KAAK,CAAC,iBAAiB,IAAI,QAAQ,IAAI,CAAC,gBAAgB,MAAM,IAAI,CAAC,YAAY,IAAI,WAAW,KAAK,IAAI,CAAC,OAAO,CAC3G,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,sBAAsB,CACvE,GAAG,IAAI,CAAC,iBAAiB,MAAM,CAAC;QACjC,KAAK,CAAC,iBAAiB,IAAI,UAAU,CAAC;IAC1C,CAAC;IAEO,iBAAiB,CAAC,KAA6B,EAAE,UAAU,GAAG,KAAK;QACvE,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC;QAExB,IAAI,UAAU,EAAE;YACZ,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;gBACpD,OAAO;aACV;YAED,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;YACnC,OAAO;SACV;QAED,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACjE,KAAK,CAAC,iBAAiB,IAAI,QAAQ,IAAI,CAAC,gBAAgB,MAAM,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,GAChJ,IAAI,CAAC,iBACT,MAAM,CAAC;YACP,OAAO;SACV;QAED,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAEO,uBAAuB,CAAC,KAA6B,EAAE,MAAmC,EAAE,OAAe;QAC/G,IAAI,OAAO,KAAK,GAAG,EAAE;YACjB,6CAA6C;YAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;gBAC3C,KAAK,CAAC,iBAAiB,IAAI,UAAU,IAAI,CAAC,iBAAiB;sBACrD,MAAM,CAAC,sBAAsB,mBAAmB,MAAM,CAAC,sBAAsB;;iBAElF,CAAC;aACL;YAED,KAAK,CAAC,iBAAiB,IAAI,UAAU,IAAI,CAAC,gBAAgB;kBACpD,MAAM,CAAC,sBAAsB,oBAAoB,MAAM,CAAC,sBAAsB;;aAEnF,CAAC;SACL;IACL,CAAC;IAEO,YAAY,CAAC,KAA6B,EAAE,MAAmC,EAAE,OAAe,EAAE,UAAU,GAAG,KAAK;QACxH,IAAI,UAAU,EAAE;YACZ,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;gBACpD,OAAO;aACV;YAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC,gBAAgB,IAAI,OAAO,KAAK,CAAC;YAC5G,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YACrD,OAAO;SACV;QAED,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACjE,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC,gBAAgB,IAAI,OAAO,KAAK,CAAC;YAC5G,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YACrD,OAAO;SACV;QACD,IAAI,UAAU,GAAG,EAAE,CAAC;QAEpB,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClC,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC9C;QAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC,gBAAgB,IAAI,OAAO,GAAG,UAAU,KAAK,CAAC;QACzH,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,cAAe,CAAC,UAA8B,CAAC;SAClF;aAAM;YACH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YAC9H,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;YACtE,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAC9D,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;SAC/D;QAED,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,CAAC,EAAE;YAC/I,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACpB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;gBAEtE,IAAI,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE;oBACpC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;iBAChD;qBAAM;oBACH,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;iBAC3C;aACJ;YAED,eAAe;YACf,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC9C;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACpD,SAAS;YACT,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9B,OAAO;SACV;QAED,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,2BAA2B,CAAC,EAAE;YAC3D,OAAO;SACV;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACrC,uBAAuB;YACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE;gBACpC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aAChD;iBAAM;gBACH,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aAC3C;SACJ;QAED,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAE5D,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;SAChE;QAED,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAE9B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;gBAChD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;aACjD;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,0BAA0B,IAAI,CAAC,mBAAmB,KAAK,CAAC;QAC/F,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,2BAA2B,IAAI,CAAC,oBAAoB,KAAK,CAAC;QACjG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,iCAAiC,IAAI,CAAC,0BAA0B,KAAK,CAAC;QAE7G,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO,UAAU,CAAC;SACrB;QAED,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,mCAAmC,IAAI,CAAC,OAAO,CAAC,IAAI,YAAY,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,YAAY,MAAM,CAAC;QAC1L,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,oBAAoB,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,CAAC;QACnF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,oBAAoB,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,CAAC;QACnF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,mBAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,mBAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,mBAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,sBAAsB,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAC;QACvF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,sBAAsB,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAC;QACvF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,qBAAqB,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC;QACrF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,qBAAqB,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC;QACrF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,8BAA8B,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,CAAC;QAEvG,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACnE,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QACtD,mBAAmB,CAAC,0BAA0B,GAAG,IAAI,CAAC,0BAA0B,CAAC;QACjF,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,cAAc,EAAE;YACxH,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;SAC1D;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,mBAAmB,CAAC;QACnE,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;QACvE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,mBAAmB,CAAC,YAAY,CAAC;QACxD,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,mBAAmB,CAAC,0BAA0B,CAAC;QAEnF,IAAI,mBAAmB,CAAC,OAAO,IAAI,CAAC,YAAY,CAAC,wBAAwB,IAAI,mBAAmB,CAAC,OAAO,CAAC,GAAG,KAAK,SAAS,EAAE;YACxH,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YAChF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAY,CAAC;SACxF;IACL,CAAC;CACJ;AAED,aAAa,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport type { NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { NodeMaterial } from \"../../nodeMaterial\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { Texture } from \"../../../Textures/texture\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { NodeMaterialModes } from \"../../Enums/nodeMaterialModes\";\r\nimport { Constants } from \"../../../../Engines/constants\";\r\nimport \"../../../../Shaders/ShadersInclude/helperFunctions\";\r\nimport { ImageSourceBlock } from \"./imageSourceBlock\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport { EngineStore } from \"../../../../Engines/engineStore\";\r\nimport type { PrePassTextureBlock } from \"../Input/prePassTextureBlock\";\r\n\r\n/**\r\n * Block used to read a texture from a sampler\r\n */\r\nexport class TextureBlock extends NodeMaterialBlock {\r\n    private _defineName: string;\r\n    private _linearDefineName: string;\r\n    private _gammaDefineName: string;\r\n    private _tempTextureRead: string;\r\n    private _samplerName: string;\r\n    private _transformedUVName: string;\r\n    private _textureTransformName: string;\r\n    private _textureInfoName: string;\r\n    private _mainUVName: string;\r\n    private _mainUVDefineName: string;\r\n    private _fragmentOnly: boolean;\r\n    private _imageSource: Nullable<ImageSourceBlock | PrePassTextureBlock>;\r\n\r\n    protected _texture: Nullable<Texture>;\r\n    /**\r\n     * Gets or sets the texture associated with the node\r\n     */\r\n    public get texture(): Nullable<Texture> {\r\n        if (this.source.isConnected) {\r\n            return (this.source.connectedPoint?.ownerBlock as ImageSourceBlock).texture;\r\n        }\r\n        return this._texture;\r\n    }\r\n\r\n    public set texture(texture: Nullable<Texture>) {\r\n        if (this._texture === texture) {\r\n            return;\r\n        }\r\n\r\n        const scene = texture?.getScene() ?? EngineStore.LastCreatedScene;\r\n\r\n        if (!texture && scene) {\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this._texture!);\r\n            });\r\n        }\r\n\r\n        this._texture = texture;\r\n\r\n        if (texture && scene) {\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(texture);\r\n            });\r\n        }\r\n    }\r\n\r\n    private static _IsPrePassTextureBlock(block: Nullable<ImageSourceBlock | PrePassTextureBlock>): block is PrePassTextureBlock {\r\n        return block?.getClassName() === \"PrePassTextureBlock\";\r\n    }\r\n\r\n    private get _isSourcePrePass() {\r\n        return TextureBlock._IsPrePassTextureBlock(this._imageSource);\r\n    }\r\n\r\n    /**\r\n     * Gets the sampler name associated with this texture\r\n     */\r\n    public get samplerName(): string {\r\n        if (this._imageSource) {\r\n            if (!TextureBlock._IsPrePassTextureBlock(this._imageSource)) {\r\n                return this._imageSource.samplerName;\r\n            }\r\n            if (this.source.connectedPoint) {\r\n                return this._imageSource.getSamplerName(this.source.connectedPoint);\r\n            }\r\n        }\r\n        return this._samplerName;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that this block is linked to an ImageSourceBlock\r\n     */\r\n    public get hasImageSource(): boolean {\r\n        return this.source.isConnected;\r\n    }\r\n\r\n    private _convertToGammaSpace = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if content needs to be converted to gamma space\r\n     */\r\n    public set convertToGammaSpace(value: boolean) {\r\n        if (value === this._convertToGammaSpace) {\r\n            return;\r\n        }\r\n\r\n        this._convertToGammaSpace = value;\r\n        if (this.texture) {\r\n            const scene = this.texture.getScene() ?? EngineStore.LastCreatedScene;\r\n            scene?.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this.texture!);\r\n            });\r\n        }\r\n    }\r\n    public get convertToGammaSpace(): boolean {\r\n        return this._convertToGammaSpace;\r\n    }\r\n\r\n    private _convertToLinearSpace = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if content needs to be converted to linear space\r\n     */\r\n    public set convertToLinearSpace(value: boolean) {\r\n        if (value === this._convertToLinearSpace) {\r\n            return;\r\n        }\r\n\r\n        this._convertToLinearSpace = value;\r\n        if (this.texture) {\r\n            const scene = this.texture.getScene() ?? EngineStore.LastCreatedScene;\r\n            scene?.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this.texture!);\r\n            });\r\n        }\r\n    }\r\n    public get convertToLinearSpace(): boolean {\r\n        return this._convertToLinearSpace;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if multiplication of texture with level should be disabled\r\n     */\r\n    public disableLevelMultiplication = false;\r\n\r\n    /**\r\n     * Create a new TextureBlock\r\n     * @param name defines the block name\r\n     * @param fragmentOnly\r\n     */\r\n    public constructor(name: string, fragmentOnly = false) {\r\n        super(name, fragmentOnly ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.VertexAndFragment);\r\n\r\n        this._fragmentOnly = fragmentOnly;\r\n\r\n        this.registerInput(\"uv\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false, NodeMaterialBlockTargets.VertexAndFragment);\r\n        this.registerInput(\r\n            \"source\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.VertexAndFragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"source\", this, NodeMaterialConnectionPointDirection.Input, ImageSourceBlock, \"ImageSourceBlock\")\r\n        );\r\n        this.registerInput(\"layer\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"lod\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n\r\n        this.registerOutput(\"rgba\", NodeMaterialBlockConnectionPointTypes.Color4, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"rgb\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"r\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"g\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"b\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"a\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerOutput(\"level\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this._inputs[0].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Vector2 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n\r\n        this._inputs[0]._prioritizeVertex = !fragmentOnly;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"TextureBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the uv input component\r\n     */\r\n    public get uv(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the source input component\r\n     */\r\n    public get source(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the layer input component\r\n     */\r\n    public get layer(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the LOD input component\r\n     */\r\n    public get lod(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgba output component\r\n     */\r\n    public get rgba(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgb output component\r\n     */\r\n    public get rgb(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the r output component\r\n     */\r\n    public get r(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the g output component\r\n     */\r\n    public get g(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the b output component\r\n     */\r\n    public get b(): NodeMaterialConnectionPoint {\r\n        return this._outputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the a output component\r\n     */\r\n    public get a(): NodeMaterialConnectionPoint {\r\n        return this._outputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the level output component\r\n     */\r\n    public get level(): NodeMaterialConnectionPoint {\r\n        return this._outputs[6];\r\n    }\r\n\r\n    public get target() {\r\n        if (this._fragmentOnly) {\r\n            return NodeMaterialBlockTargets.Fragment;\r\n        }\r\n\r\n        // TextureBlock has a special optimizations for uvs that come from the vertex shaders as they can be packed into a single varyings.\r\n        // But we need to detect uvs coming from fragment then\r\n        if (!this.uv.isConnected) {\r\n            return NodeMaterialBlockTargets.VertexAndFragment;\r\n        }\r\n\r\n        if (this.uv.sourceBlock!.isInput) {\r\n            return NodeMaterialBlockTargets.VertexAndFragment;\r\n        }\r\n\r\n        let parent = this.uv.connectedPoint;\r\n\r\n        while (parent) {\r\n            if (parent.target === NodeMaterialBlockTargets.Fragment) {\r\n                return NodeMaterialBlockTargets.Fragment;\r\n            }\r\n\r\n            if (parent.target === NodeMaterialBlockTargets.Vertex) {\r\n                return NodeMaterialBlockTargets.VertexAndFragment;\r\n            }\r\n\r\n            if (parent.target === NodeMaterialBlockTargets.Neutral || parent.target === NodeMaterialBlockTargets.VertexAndFragment) {\r\n                const parentBlock = parent.ownerBlock;\r\n\r\n                if (parentBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n                    return NodeMaterialBlockTargets.Fragment;\r\n                }\r\n\r\n                parent = null;\r\n                for (const input of parentBlock.inputs) {\r\n                    if (input.connectedPoint) {\r\n                        parent = input.connectedPoint;\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return NodeMaterialBlockTargets.VertexAndFragment;\r\n    }\r\n\r\n    public set target(value: NodeMaterialBlockTargets) {}\r\n\r\n    public autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.uv.isConnected) {\r\n            if (material.mode === NodeMaterialModes.PostProcess) {\r\n                const uvInput = material.getBlockByPredicate((b) => b.name === \"uv\" && additionalFilteringInfo(b));\r\n\r\n                if (uvInput) {\r\n                    uvInput.connectTo(this);\r\n                }\r\n            } else {\r\n                const attributeName = material.mode === NodeMaterialModes.Particle ? \"particle_uv\" : \"uv\";\r\n\r\n                let uvInput = material.getInputBlockByPredicate((b) => b.isAttribute && b.name === attributeName && additionalFilteringInfo(b));\r\n\r\n                if (!uvInput) {\r\n                    uvInput = new InputBlock(\"uv\");\r\n                    uvInput.setAsAttribute(attributeName);\r\n                }\r\n                uvInput.output.connectTo(this.uv);\r\n            }\r\n        }\r\n    }\r\n\r\n    public initializeDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        if (!defines._areTexturesDirty) {\r\n            return;\r\n        }\r\n\r\n        if (this._mainUVDefineName !== undefined) {\r\n            defines.setValue(this._mainUVDefineName, false, true);\r\n        }\r\n    }\r\n\r\n    public prepareDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        if (!defines._areTexturesDirty) {\r\n            return;\r\n        }\r\n\r\n        if (!this.texture || !this.texture.getTextureMatrix) {\r\n            if (this._isMixed) {\r\n                defines.setValue(this._defineName, false, true);\r\n                defines.setValue(this._mainUVDefineName, true, true);\r\n            }\r\n            return;\r\n        }\r\n\r\n        const toGamma = this.convertToGammaSpace && this.texture && !this.texture.gammaSpace;\r\n        const toLinear = this.convertToLinearSpace && this.texture && this.texture.gammaSpace;\r\n\r\n        // Not a bug... Name defines the texture space not the required conversion\r\n        defines.setValue(this._linearDefineName, toGamma, true);\r\n        defines.setValue(this._gammaDefineName, toLinear, true);\r\n\r\n        if (this._isMixed) {\r\n            if (!this.texture.getTextureMatrix().isIdentityAs3x2()) {\r\n                defines.setValue(this._defineName, true);\r\n                if (defines[this._mainUVDefineName] == undefined) {\r\n                    defines.setValue(this._mainUVDefineName, false, true);\r\n                }\r\n            } else {\r\n                defines.setValue(this._defineName, false, true);\r\n                defines.setValue(this._mainUVDefineName, true, true);\r\n            }\r\n        }\r\n    }\r\n\r\n    public isReady() {\r\n        if (this._isSourcePrePass) {\r\n            return true;\r\n        }\r\n\r\n        if (this.texture && !this.texture.isReadyOrNotBlocking()) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public bind(effect: Effect) {\r\n        if (this._isSourcePrePass) {\r\n            effect.setFloat(this._textureInfoName, 1);\r\n        }\r\n\r\n        if (!this.texture) {\r\n            return;\r\n        }\r\n\r\n        if (this._isMixed) {\r\n            effect.setFloat(this._textureInfoName, this.texture.level);\r\n            effect.setMatrix(this._textureTransformName, this.texture.getTextureMatrix());\r\n        }\r\n\r\n        if (!this._imageSource) {\r\n            effect.setTexture(this._samplerName, this.texture);\r\n        }\r\n    }\r\n\r\n    private get _isMixed() {\r\n        return this.target !== NodeMaterialBlockTargets.Fragment;\r\n    }\r\n\r\n    private _injectVertexCode(state: NodeMaterialBuildState) {\r\n        const uvInput = this.uv;\r\n\r\n        // Inject code in vertex\r\n        this._defineName = state._getFreeDefineName(\"UVTRANSFORM\");\r\n        this._mainUVDefineName = \"VMAIN\" + uvInput.associatedVariableName.toUpperCase();\r\n\r\n        this._mainUVName = \"vMain\" + uvInput.associatedVariableName;\r\n        this._transformedUVName = state._getFreeVariableName(\"transformedUV\");\r\n        this._textureTransformName = state._getFreeVariableName(\"textureTransform\");\r\n        this._textureInfoName = state._getFreeVariableName(\"textureInfoName\");\r\n\r\n        this.level.associatedVariableName = this._textureInfoName;\r\n\r\n        state._emitVaryingFromString(this._transformedUVName, \"vec2\", this._defineName);\r\n        state._emitVaryingFromString(this._mainUVName, \"vec2\", this._mainUVDefineName);\r\n\r\n        state._emitUniformFromString(this._textureTransformName, \"mat4\", this._defineName);\r\n\r\n        state.compilationString += `#ifdef ${this._defineName}\\n`;\r\n        state.compilationString += `${this._transformedUVName} = vec2(${this._textureTransformName} * vec4(${uvInput.associatedVariableName}.xy, 1.0, 0.0));\\n`;\r\n        state.compilationString += `#elif defined(${this._mainUVDefineName})\\n`;\r\n        state.compilationString += `${this._mainUVName} = ${uvInput.associatedVariableName}.xy;\\n`;\r\n        state.compilationString += `#endif\\n`;\r\n\r\n        if (!this._outputs.some((o) => o.isConnectedInVertexShader)) {\r\n            return;\r\n        }\r\n\r\n        this._writeTextureRead(state, true);\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints && output.name !== \"level\") {\r\n                this._writeOutput(state, output, output.name, true);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _getUVW(uvName: string): string {\r\n        let coords = uvName;\r\n\r\n        const is2DArrayTexture = this._texture?._texture?.is2DArray ?? false;\r\n\r\n        if (is2DArrayTexture) {\r\n            const layerValue = this.layer.isConnected ? this.layer.associatedVariableName : \"0\";\r\n            coords = `vec3(${uvName}, ${layerValue})`;\r\n        }\r\n\r\n        return coords;\r\n    }\r\n\r\n    private get _samplerFunc() {\r\n        return this.lod.isConnected ? \"texture2DLodEXT\" : \"texture2D\";\r\n    }\r\n\r\n    private get _samplerLodSuffix() {\r\n        return this.lod.isConnected ? `, ${this.lod.associatedVariableName}` : \"\";\r\n    }\r\n\r\n    private _generateTextureLookup(state: NodeMaterialBuildState): void {\r\n        const samplerName = this.samplerName;\r\n\r\n        state.compilationString += `#ifdef ${this._defineName}\\n`;\r\n        state.compilationString += `vec4 ${this._tempTextureRead} = ${this._samplerFunc}(${samplerName}, ${this._getUVW(this._transformedUVName)}${this._samplerLodSuffix});\\n`;\r\n        state.compilationString += `#elif defined(${this._mainUVDefineName})\\n`;\r\n        state.compilationString += `vec4 ${this._tempTextureRead} = ${this._samplerFunc}(${samplerName}, ${this._getUVW(\r\n            this._mainUVName ? this._mainUVName : this.uv.associatedVariableName\r\n        )}${this._samplerLodSuffix});\\n`;\r\n        state.compilationString += `#endif\\n`;\r\n    }\r\n\r\n    private _writeTextureRead(state: NodeMaterialBuildState, vertexMode = false) {\r\n        const uvInput = this.uv;\r\n\r\n        if (vertexMode) {\r\n            if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n                return;\r\n            }\r\n\r\n            this._generateTextureLookup(state);\r\n            return;\r\n        }\r\n\r\n        if (this.uv.ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += `vec4 ${this._tempTextureRead} = ${this._samplerFunc}(${this.samplerName}, ${this._getUVW(uvInput.associatedVariableName)}${\r\n                this._samplerLodSuffix\r\n            });\\n`;\r\n            return;\r\n        }\r\n\r\n        this._generateTextureLookup(state);\r\n    }\r\n\r\n    private _generateConversionCode(state: NodeMaterialBuildState, output: NodeMaterialConnectionPoint, swizzle: string): void {\r\n        if (swizzle !== \"a\") {\r\n            // no conversion if the output is \"a\" (alpha)\r\n            if (!this.texture || !this.texture.gammaSpace) {\r\n                state.compilationString += `#ifdef ${this._linearDefineName}\r\n                    ${output.associatedVariableName} = toGammaSpace(${output.associatedVariableName});\r\n                    #endif\r\n                `;\r\n            }\r\n\r\n            state.compilationString += `#ifdef ${this._gammaDefineName}\r\n                ${output.associatedVariableName} = toLinearSpace(${output.associatedVariableName});\r\n                #endif\r\n            `;\r\n        }\r\n    }\r\n\r\n    private _writeOutput(state: NodeMaterialBuildState, output: NodeMaterialConnectionPoint, swizzle: string, vertexMode = false) {\r\n        if (vertexMode) {\r\n            if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n                return;\r\n            }\r\n\r\n            state.compilationString += `${this._declareOutput(output, state)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n            this._generateConversionCode(state, output, swizzle);\r\n            return;\r\n        }\r\n\r\n        if (this.uv.ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += `${this._declareOutput(output, state)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n            this._generateConversionCode(state, output, swizzle);\r\n            return;\r\n        }\r\n        let complement = \"\";\r\n\r\n        if (!this.disableLevelMultiplication) {\r\n            complement = ` * ${this._textureInfoName}`;\r\n        }\r\n\r\n        state.compilationString += `${this._declareOutput(output, state)} = ${this._tempTextureRead}.${swizzle}${complement};\\n`;\r\n        this._generateConversionCode(state, output, swizzle);\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (this.source.isConnected) {\r\n            this._imageSource = this.source.connectedPoint!.ownerBlock as ImageSourceBlock;\r\n        } else {\r\n            this._imageSource = null;\r\n        }\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Vertex || this._fragmentOnly || state.target === NodeMaterialBlockTargets.Fragment) {\r\n            this._tempTextureRead = state._getFreeVariableName(\"tempTextureRead\");\r\n            this._linearDefineName = state._getFreeDefineName(\"ISLINEAR\");\r\n            this._gammaDefineName = state._getFreeDefineName(\"ISGAMMA\");\r\n        }\r\n\r\n        if ((!this._isMixed && state.target === NodeMaterialBlockTargets.Fragment) || (this._isMixed && state.target === NodeMaterialBlockTargets.Vertex)) {\r\n            if (!this._imageSource) {\r\n                this._samplerName = state._getFreeVariableName(this.name + \"Sampler\");\r\n\r\n                if (this._texture?._texture?.is2DArray) {\r\n                    state._emit2DArraySampler(this._samplerName);\r\n                } else {\r\n                    state._emit2DSampler(this._samplerName);\r\n                }\r\n            }\r\n\r\n            // Declarations\r\n            state.sharedData.blockingBlocks.push(this);\r\n            state.sharedData.textureBlocks.push(this);\r\n            state.sharedData.blocksWithDefines.push(this);\r\n            state.sharedData.bindableBlocks.push(this);\r\n        }\r\n\r\n        if (state.target !== NodeMaterialBlockTargets.Fragment) {\r\n            // Vertex\r\n            this._injectVertexCode(state);\r\n            return;\r\n        }\r\n\r\n        // Fragment\r\n        if (!this._outputs.some((o) => o.isConnectedInFragmentShader)) {\r\n            return;\r\n        }\r\n\r\n        if (this._isMixed && !this._imageSource) {\r\n            // Reexport the sampler\r\n            if (this._texture?._texture?.is2DArray) {\r\n                state._emit2DArraySampler(this._samplerName);\r\n            } else {\r\n                state._emit2DSampler(this._samplerName);\r\n            }\r\n        }\r\n\r\n        const comments = `//${this.name}`;\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n\r\n        if (this._isMixed) {\r\n            state._emitUniformFromString(this._textureInfoName, \"float\");\r\n        }\r\n\r\n        this._writeTextureRead(state);\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints && output.name !== \"level\") {\r\n                this._writeOutput(state, output, output.name);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        codeString += `${this._codeVariableName}.convertToGammaSpace = ${this.convertToGammaSpace};\\n`;\r\n        codeString += `${this._codeVariableName}.convertToLinearSpace = ${this.convertToLinearSpace};\\n`;\r\n        codeString += `${this._codeVariableName}.disableLevelMultiplication = ${this.disableLevelMultiplication};\\n`;\r\n\r\n        if (!this.texture) {\r\n            return codeString;\r\n        }\r\n\r\n        codeString += `${this._codeVariableName}.texture = new BABYLON.Texture(\"${this.texture.name}\", null, ${this.texture.noMipmap}, ${this.texture.invertY}, ${this.texture.samplingMode});\\n`;\r\n        codeString += `${this._codeVariableName}.texture.wrapU = ${this.texture.wrapU};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.wrapV = ${this.texture.wrapV};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.uAng = ${this.texture.uAng};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.vAng = ${this.texture.vAng};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.wAng = ${this.texture.wAng};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.uOffset = ${this.texture.uOffset};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.vOffset = ${this.texture.vOffset};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.uScale = ${this.texture.uScale};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.vScale = ${this.texture.vScale};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.coordinatesMode = ${this.texture.coordinatesMode};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.convertToGammaSpace = this.convertToGammaSpace;\r\n        serializationObject.convertToLinearSpace = this.convertToLinearSpace;\r\n        serializationObject.fragmentOnly = this._fragmentOnly;\r\n        serializationObject.disableLevelMultiplication = this.disableLevelMultiplication;\r\n        if (!this.hasImageSource && this.texture && !this.texture.isRenderTarget && this.texture.getClassName() !== \"VideoTexture\") {\r\n            serializationObject.texture = this.texture.serialize();\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.convertToGammaSpace = serializationObject.convertToGammaSpace;\r\n        this.convertToLinearSpace = !!serializationObject.convertToLinearSpace;\r\n        this._fragmentOnly = !!serializationObject.fragmentOnly;\r\n        this.disableLevelMultiplication = !!serializationObject.disableLevelMultiplication;\r\n\r\n        if (serializationObject.texture && !NodeMaterial.IgnoreTexturesAtLoadTime && serializationObject.texture.url !== undefined) {\r\n            rootUrl = serializationObject.texture.url.indexOf(\"data:\") === 0 ? \"\" : rootUrl;\r\n            this.texture = Texture.Parse(serializationObject.texture, scene, rootUrl) as Texture;\r\n        }\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.TextureBlock\", TextureBlock);\r\n"]}