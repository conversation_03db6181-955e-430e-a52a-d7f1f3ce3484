{"version": 3, "file": "shaderMaterial.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Materials/shaderMaterial.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AAEvE,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAIrF,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAEjD,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAExD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAKhD,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,iCAAiC,EAAE,MAAM,2BAA2B,CAAC;AAGnH,OAAO,EACH,mBAAmB,EACnB,iBAAiB,EACjB,YAAY,EACZ,yBAAyB,EACzB,sBAAsB,EACtB,wCAAwC,EACxC,0BAA0B,GAC7B,MAAM,4BAA4B,CAAC;AAEpC,MAAM,yBAAyB,GAAG,EAAE,MAAM,EAAE,IAAyB,EAAE,OAAO,EAAE,IAAoC,EAAE,CAAC;AAmEvH;;;;;;GAMG;AACH,MAAM,OAAO,cAAe,SAAQ,YAAY;IA4C5C;;;;;;;;;;;;;;OAcG;IACH,YAAY,IAAY,EAAE,KAAY,EAAE,UAAe,EAAE,UAA2C,EAAE,EAAE,sBAAsB,GAAG,IAAI;QACjI,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,sBAAsB,CAAC,CAAC;QAzDvC,cAAS,GAAoC,EAAE,CAAC;QAChD,mBAAc,GAAsC,EAAE,CAAC;QACvD,sBAAiB,GAAwC,EAAE,CAAC;QAC5D,YAAO,GAA+B,EAAE,CAAC;QACzC,UAAK,GAA+B,EAAE,CAAC;QACvC,WAAM,GAA+B,EAAE,CAAC;QACxC,kBAAa,GAAiC,EAAE,CAAC;QACjD,aAAQ,GAA+B,EAAE,CAAC;QAC1C,mBAAc,GAAiC,EAAE,CAAC;QAClD,aAAQ,GAA+B,EAAE,CAAC;QAC1C,mBAAc,GAAiC,EAAE,CAAC;QAClD,cAAS,GAAgC,EAAE,CAAC;QAC5C,cAAS,GAAgC,EAAE,CAAC;QAC5C,cAAS,GAAgC,EAAE,CAAC;QAC5C,iBAAY,GAAmC,EAAE,CAAC;QAClD,uBAAkB,GAAiC,EAAE,CAAC;QACtD,cAAS,GAA+B,EAAE,CAAC;QAC3C,kBAAa,GAAqD,EAAE,CAAC;QACrE,iBAAY,GAAqD,EAAE,CAAC;QACpE,iBAAY,GAAqD,EAAE,CAAC;QACpE,oBAAe,GAAiC,EAAE,CAAC;QACnD,oBAAe,GAAiC,EAAE,CAAC;QACnD,oBAAe,GAAiC,EAAE,CAAC;QACnD,oBAAe,GAAsC,EAAE,CAAC;QACxD,qBAAgB,GAAuC,EAAE,CAAC;QAC1D,oBAAe,GAAsC,EAAE,CAAC;QACxD,2BAAsB,GAAG,IAAI,MAAM,EAAE,CAAC;QACtC,qCAAgC,GAAG,IAAI,MAAM,EAAE,CAAC;QAChD,eAAU,GAAG,KAAK,CAAC;QAE3B;;WAEG;QACI,yCAAoC,GAAG,KAAK,CAAC;QAyBhD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,IAAI,CAAC,QAAQ,GAAG;YACZ,iBAAiB,EAAE,KAAK;YACxB,gBAAgB,EAAE,KAAK;YACvB,UAAU,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC;YACxC,QAAQ,EAAE,CAAC,qBAAqB,CAAC;YACjC,cAAc,EAAE,EAAE;YAClB,QAAQ,EAAE,EAAE;YACZ,gBAAgB,EAAE,EAAE;YACpB,cAAc,EAAE,EAAE;YAClB,cAAc,EAAE,EAAE;YAClB,OAAO,EAAE,EAAE;YACX,YAAY,EAAE,KAAK;YACnB,GAAG,OAAO;SACb,CAAC;IACN,CAAC;IAED;;;OAGG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,IAAW,UAAU,CAAC,UAAe;QACjC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACI,YAAY;QACf,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,OAAO,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC;IAC/D,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;IAC1C,CAAC;IAEO,aAAa,CAAC,WAAmB;QACrC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;YACpD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC5C;IACL,CAAC;IAED;;;;;OAKG;IACI,UAAU,CAAC,IAAY,EAAE,OAAoB;QAChD,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC7C,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrC;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;QAE/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,IAAY,EAAE,QAAuB;QACxD,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC7C,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAEzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;QAErC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,IAAY,EAAE,OAAwB;QAC5D,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACrD,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7C;QACD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;QAEvC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,IAAY,EAAE,KAAa;QACvC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE3B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,IAAY,EAAE,KAAa;QACrC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAEzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,IAAY,EAAE,KAAa;QACtC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE1B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY,EAAE,KAAe;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAEjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY,EAAE,KAAa;QACxC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE5B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,IAAY,EAAE,KAAe;QAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YACpD,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;YAC/B,OAAO,GAAG,CAAC;QACf,CAAC,EAAE,EAAE,CAAC,CAAC;QACP,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY,EAAE,KAAa;QACxC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE5B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,IAAY,EAAE,KAAe;QAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YACpD,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;YAC/B,OAAO,GAAG,CAAC;QACf,CAAC,EAAE,EAAE,CAAC,CAAC;QACP,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,UAAU,CAAC,IAAY,EAAE,KAAc;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,UAAU,CAAC,IAAY,EAAE,KAAc;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,UAAU,CAAC,IAAY,EAAE,KAAc;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,IAAY,EAAE,KAAiB;QAChD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAEhC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,IAAY,EAAE,KAAmB;QACvD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE;YAC7D,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;YACpC,OAAO,GAAG,CAAC;QACf,CAAC,EAAE,EAAE,CAAC,CAAC;QACP,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY,EAAE,KAAa;QACxC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,IAAY,EAAE,KAAe;QAC5C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAEzB,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;QAEzD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC/C,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;YAE5B,MAAM,CAAC,WAAW,CAAC,YAAY,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC;SAChD;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,IAAY,EAAE,KAAmC;QACjE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAEhC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,IAAY,EAAE,KAAmC;QACjE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAEhC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY,EAAE,KAAe;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAEnC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY,EAAE,KAAe;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAEnC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY,EAAE,KAAe;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAEnC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,IAAY,EAAE,MAAqB;QACvD,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACnD,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC3C;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;QAEpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,IAAY,EAAE,OAAuB;QAC1D,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACnD,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC3C;QACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;QAEtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,IAAY,EAAE,MAAqB;QACvD,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACnD,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC3C;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;QAEpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG;IACI,SAAS,CAAC,MAAc,EAAE,KAAuB;QACpD,mDAAmD;QACnD,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC;QAC1C,MAAM,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,MAAM,IAAI,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;QAC1G,IAAI,iBAAiB,IAAI,CAAC,EAAE;YACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;SACrD;QAED,oFAAoF;QACpF,IAAI,OAAO,KAAK,KAAK,SAAS,IAAI,KAAK,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC;SACjD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAAE,YAAsB;QACjF,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;OAMG;IACI,OAAO,CAAC,IAAmB,EAAE,YAAsB,EAAE,OAAiB;QACzE,MAAM,sBAAsB,GAAG,OAAO,IAAI,IAAI,CAAC,uBAAuB,CAAC;QAEvE,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,MAAM,WAAW,GAAG,sBAAsB,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;YACtF,IAAI,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,mBAAmB,IAAI,WAAW,CAAC,4BAA4B,KAAK,YAAY,EAAE;gBACpH,OAAO,IAAI,CAAC;aACf;SACJ;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,YAAY;QACZ,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;QAExC,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,EAC7B,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EACjC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,EAC7C,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAEtC,mBAAmB;QACnB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,SAAS,IAAI,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,YAAY,CAAC,kBAAkB,IAAI,KAAK,CAAC,YAAY,CAAC,kBAAkB,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE;YACvJ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClC,IAAI,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE;gBACzF,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;aACpC;SACJ;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACrJ,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC7B;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAClE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;SACjD;QAED,IAAI,IAAI,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;YAC5D,IAAI,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;gBAChD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;aACxC;YACD,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SACvC;QAED,IAAI,YAAY,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClC,0BAA0B,CAAC,OAAO,EAAE,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAC/E,IAAI,IAAI,EAAE,gBAAgB,EAAE;gBACxB,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBACvC,IAAI,IAAI,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE;oBACpE,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;oBAC7C,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;iBAC1C;aACJ;SACJ;QAED,QAAQ;QACR,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACzE,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;YAC/C,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;gBAC7B,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;gBACpD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;aACvD;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAE/B,OAAO,CAAC,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACxE,SAAS,CAAC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAE1C,IAAI,QAAQ,CAAC,yBAAyB,EAAE;gBACpC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAEpC,IAAI,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC7C,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;iBACrC;gBAED,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;oBACtD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;iBAC9C;aACJ;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBAEpE,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;oBACnC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBAC3B;aACJ;SACJ;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;SAClD;QAED,QAAQ;QACR,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAQ,IAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9D,IAAI,OAAO,EAAE;YACT,MAAM,EAAE,GAAG,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;YACxE,MAAM,OAAO,GAAG,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;YACtF,MAAM,MAAM,GAAG,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;YACnF,cAAc,GAAG,OAAO,CAAC,iBAAiB,IAAI,OAAO,CAAC,cAAc,CAAC;YACrE,IAAI,EAAE,EAAE;gBACJ,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;aAC3C;YACD,IAAI,OAAO,EAAE;gBACT,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;aAChD;YACD,IAAI,MAAM,EAAE;gBACR,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;aAC/C;YACD,IAAI,cAAc,GAAG,CAAC,EAAE;gBACpB,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;aACxC;YACD,IAAI,OAAO,CAAC,wBAAwB,EAAE;gBAClC,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAE7C,IAAI,QAAQ,CAAC,OAAO,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC,EAAE;oBACtD,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;iBAC9C;gBAED,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE;oBACvD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;iBAC/C;aACJ;YACD,OAAO,CAAC,IAAI,CAAC,gCAAgC,GAAG,cAAc,CAAC,CAAC;YAChE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,cAAc,EAAE,KAAK,EAAE,EAAE;gBACjD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;gBAEhD,IAAI,MAAM,EAAE;oBACR,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC;iBACjD;gBAED,IAAI,OAAO,EAAE;oBACT,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;iBAClD;gBAED,IAAI,EAAE,EAAE;oBACJ,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;iBACnD;aACJ;YACD,IAAI,cAAc,GAAG,CAAC,EAAE;gBACpB,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;gBAC5B,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBACvC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAClC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBACxC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;aAC9C;SACJ;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;SACnD;QAED,yBAAyB;QACzB,IAAI,IAAI,EAAE;YACN,MAAM,UAAU,GAAU,IAAK,CAAC,2BAA2B,CAAC;YAE5D,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE;gBACpC,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBACvD,IAAI,QAAQ,CAAC,OAAO,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC,EAAE;oBACzD,QAAQ,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;iBACjD;gBACD,IAAI,QAAQ,CAAC,OAAO,CAAC,yCAAyC,CAAC,KAAK,CAAC,CAAC,EAAE;oBACpE,QAAQ,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;iBAC5D;gBACD,IAAI,QAAQ,CAAC,OAAO,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,EAAE;oBACrD,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;iBAC7C;gBAED,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC,EAAE;oBACtE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;iBAC9D;aACJ;YAED,wCAAwC,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;SACpE;QAED,WAAW;QACX,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;gBACjC,OAAO,KAAK,CAAC;aAChB;SACJ;QAED,aAAa;QACb,IAAI,IAAI,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAC3C,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACrC;QAED,cAAc;QACd,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,KAAK,KAAK,EAAE;YACtC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAE/B,iCAAiC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAC3D;QAED,MAAM;QACN,IAAI,KAAK,CAAC,UAAU,IAAI,IAAI,EAAE,QAAQ,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,YAAY,EAAE;YAC5E,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC5B,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBACjC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACzB;YACD,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;gBACtC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAC9B;YACD,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;gBACtC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAC9B;SACJ;QAED,OAAO;QACP,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACzC,IAAI,QAAQ,CAAC,OAAO,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,EAAE;gBACrD,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;aAC7C;SACJ;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC5B,cAAc,GAAG,cAAc,CAAC,KAAK,EAAE,CAAC;YACxC,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC5B,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SAC/G;QAED,MAAM,WAAW,GAAG,sBAAsB,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;QAC1G,MAAM,cAAc,GAAG,WAAW,EAAE,MAAM,IAAI,IAAI,CAAC;QACnD,MAAM,eAAe,GAAG,WAAW,EAAE,OAAO,IAAI,IAAI,CAAC;QACrD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,MAAM,GAAG,cAAc,CAAC;QAC5B,IAAI,eAAe,KAAK,IAAI,EAAE;YAC1B,MAAM,GAAG,MAAM,CAAC,YAAY,CACxB,UAAU,EACc;gBACpB,UAAU,EAAE,OAAO;gBACnB,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,cAAc;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,eAAe,EAAE,EAAE,2BAA2B,EAAE,cAAc,EAAE;gBAChE,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc;aAC/C,EACD,MAAM,CACT,CAAC;YAEF,IAAI,sBAAsB,EAAE;gBACxB,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAC1D;iBAAM,IAAI,WAAW,EAAE;gBACpB,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;aACvC;YAED,IAAI,IAAI,CAAC,0BAA0B,EAAE;gBACjC,yBAAyB,CAAC,MAAM,GAAG,MAAM,CAAC;gBAC1C,yBAAyB,CAAC,OAAO,GAAG,OAAO,IAAI,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;gBAC1E,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC;aAC9E;SACJ;QAED,WAAY,CAAC,4BAA4B,GAAG,CAAC,CAAC,YAAY,CAAC;QAE3D,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,IAAI,EAAE;YAC5B,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,cAAc,KAAK,MAAM,EAAE;YAC3B,KAAK,CAAC,mBAAmB,EAAE,CAAC;SAC/B;QAED,WAAY,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,KAAa,EAAE,cAAiC;QACvE,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,MAAM,MAAM,GAAG,cAAc,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;QAElD,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;YAChD,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SACpC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;YACpD,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACxE,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;SAC9D;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;YAC9D,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACvF,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,IAAI,CAAC,gCAAgC,CAAC,CAAC;SAClF;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/C,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;SACnD;IACL,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,KAAa,EAAE,IAAU,EAAE,OAAgB;QAC7D,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,oBAAoB,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;;OAMG;IACI,IAAI,CAAC,KAAa,EAAE,IAAW,EAAE,cAAiC,EAAE,OAAiB;QACxF,aAAa;QACb,MAAM,sBAAsB,GAAG,OAAO,IAAI,IAAI,CAAC,uBAAuB,CAAC;QACvE,MAAM,MAAM,GAAG,cAAc,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAE9F,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;QAEhD,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;QAEpD,IAAI,WAAW,GAAG,KAAK,CAAC;QAExB,IAAI,MAAM,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,sBAAsB,EAAE;YACnG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBAC5C,MAAM,UAAU,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBACrC,QAAQ,UAAU,EAAE;oBAChB,KAAK,MAAM;wBACP,IAAI,IAAI,EAAE;4BACN,IAAI,CAAC,oBAAoB,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;4BACzD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;yBAChC;wBACD,MAAM;oBACV,KAAK,OAAO;wBACR,sBAAsB,CAAC,MAAM,EAAE,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC;wBAC9D,KAAK,CAAC,gBAAgB,EAAE,CAAC;wBACzB,WAAW,GAAG,IAAI,CAAC;wBACnB,MAAM;iBACb;aACJ;SACJ;QAED,MAAM,UAAU,GAAG,IAAI,IAAI,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC;QAEnJ,IAAI,MAAM,IAAI,UAAU,EAAE;YACtB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC/D,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;aACnD;YAED,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;gBACrE,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC;aAC/D;YAED,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE;gBACzE,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;gBAC/D,IAAI,IAAI,CAAC,UAAU,EAAE;oBACjB,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,CAAC,CAAC;iBAChE;aACJ;YAED,IAAI,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC/E,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,KAAK,CAAC,YAAa,CAAC,cAAc,CAAC,CAAC;aAC3E;YAED,QAAQ;YACR,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAElC,aAAa;YACb,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAEnC,OAAO;YACP,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;aAClG;YAED,MAAM;YACN,IAAI,IAAI,EAAE;gBACN,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;aAC1C;YAED,IAAI,IAAY,CAAC;YACjB,UAAU;YACV,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;gBACzB,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;aACjD;YAED,iBAAiB;YACjB,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;gBAC9B,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;aAC3D;YAED,mBAAmB;YACnB,KAAK,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACjC,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;aACjE;YAED,MAAM;YACN,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;gBACrB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;aACzC;YAED,OAAO;YACP,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;gBACtB,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;aAC3C;YAED,QAAQ;YACR,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;gBACvB,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;aAC7C;YAED,SAAS;YACT,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;gBAC7B,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;aACnD;YAED,SAAS;YACT,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACxB,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;aAC/C;YAED,cAAc;YACd,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;gBAC9B,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;aACrD;YAED,SAAS;YACT,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACxB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAClC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;aAC9D;YAED,cAAc;YACd,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;gBAC9B,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;aACrD;YAED,UAAU;YACV,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;gBACzB,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;aACjD;YAED,UAAU;YACV,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;gBACzB,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;aACjD;YAED,UAAU;YACV,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;gBACzB,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;aACjD;YAED,aAAa;YACb,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;gBAC5B,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;aACvD;YAED,SAAS;YACT,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;gBACzB,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;aAChD;YAED,cAAc;YACd,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;gBAC7B,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;aACtD;YAED,aAAa;YACb,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;gBAC5B,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;aACtD;YAED,aAAa;YACb,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;gBAC5B,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;aACtD;YAED,eAAe;YACf,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;gBAC/B,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;aACtD;YAED,eAAe;YACf,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;gBAC/B,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;aACtD;YAED,eAAe;YACf,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;gBAC/B,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;aACtD;YAED,kBAAkB;YAClB,KAAK,IAAI,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAClC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;aACzD;YAED,kBAAkB;YAClB,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;gBAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC;gBACtD,IAAI,MAAM,EAAE;oBACR,MAAM,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;iBAC1C;aACJ;YAED,WAAW;YACX,KAAK,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBAChC,MAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;aAC/D;YAED,kBAAkB;YAClB,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;gBAC/B,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;aAC7D;SACJ;QAED,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAClD,gBAAgB;YAChB,MAAM,OAAO,GAAU,IAAK,CAAC,kBAAkB,CAAC;YAChD,IAAI,OAAO,IAAI,OAAO,CAAC,cAAc,GAAG,CAAC,EAAE;gBACvC,yBAAyB,CAAO,IAAI,EAAE,MAAM,CAAC,CAAC;aACjD;YAED,MAAM,UAAU,GAAU,IAAK,CAAC,2BAA2B,CAAC;YAE5D,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE;gBACpC,MAAM,WAAW,GAAG,sBAAsB,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;gBACtF,IAAI,CAAC,2BAA2B,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,WAAW,CAAC,4BAA4B,CAAC,CAAC;aAC9F;SACJ;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,MAAM,cAAc,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAEjD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YAC/B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;SAC7C;QAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YACpC,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACxC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC/C,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;aACrC;SACJ;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,OAAoB;QAClC,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,OAAO,EAAE;gBAClC,OAAO,IAAI,CAAC;aACf;SACJ;QAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YACpC,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACxC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC/C,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,OAAO,EAAE;oBAC1B,OAAO,IAAI,CAAC;iBACf;aACJ;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,IAAY;QACrB,MAAM,MAAM,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC,EAAE,IAAI,CAAC,CAAC;QAE/J,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC;QAEjB,mBAAmB;QACnB,IAAI,OAAO,MAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;YACxC,MAAM,CAAC,WAAW,GAAG,EAAE,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;SAClD;QAED,UAAU;QACV,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEpC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAyC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACrF,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC1C,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACf,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aAC5D;QACL,CAAC,CAAC,CAAC;QAEH,UAAU;QACV,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEpC,UAAU;QACV,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;YAC9B,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SAC/C;QAED,eAAe;QACf,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,cAAc,EAAE;YACnC,MAAM,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;SACzD;QAED,mBAAmB;QACnB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACtC,MAAM,CAAC,kBAAkB,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC;SAC/D;QAED,MAAM;QACN,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE;YAC1B,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;SACvC;QAED,OAAO;QACP,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE;YAC3B,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;SACzC;QAED,QAAQ;QACR,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE;YAC5B,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;SAC3C;QAED,SAAS;QACT,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE;YAClC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;SAClD;QAED,SAAS;QACT,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC7B,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;SAC7C;QAED,cAAc;QACd,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,cAAc,EAAE;YACnC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;SACzD;QAED,SAAS;QACT,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC7B,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;SAC7C;QAED,cAAc;QACd,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,cAAc,EAAE;YACnC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;SACzD;QAED,UAAU;QACV,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;YAC9B,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SAC/C;QAED,UAAU;QACV,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;YAC9B,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SAC/C;QAED,UAAU;QACV,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;YAC9B,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SAC/C;QAED,aAAa;QACb,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,EAAE;YACjC,MAAM,CAAC,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;SACrD;QAED,kBAAkB;QAClB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACvC,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;SACjE;QAED,SAAS;QACT,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;YAC9B,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SAC9C;QAED,cAAc;QACd,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE;YAClC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;SAC/D;QAED,aAAa;QACb,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,EAAE;YACjC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;SACpD;QAED,aAAa;QACb,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,EAAE;YACjC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;SACpD;QAED,eAAe;QACf,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE;YACpC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;SACpD;QAED,eAAe;QACf,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE;YACpC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;SACpD;QAED,eAAe;QACf,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE;YACpC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;SACpD;QAED,kBAAkB;QAClB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE;YACpC,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;SAC3D;QAED,WAAW;QACX,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACrC,MAAM,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;SAC7D;QAED,iBAAiB;QACjB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE;YACpC,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;SAC3D;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,kBAA4B,EAAE,oBAA8B,EAAE,cAAwB;QACjG,IAAI,oBAAoB,EAAE;YACtB,IAAI,IAAY,CAAC;YACjB,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;gBACzB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;aAClC;YAED,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;gBAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBACxC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBAC/C,KAAK,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;iBAC1B;aACJ;SACJ;QAED,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QAEpB,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAAC;IAC5E,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,mBAAmB,CAAC,UAAU,GAAG,wBAAwB,CAAC;QAC1D,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE7C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5C,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAClD,mBAAmB,CAAC,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAE1E,IAAI,IAAY,CAAC;QAEjB,UAAU;QACV,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAEvD,UAAU;QACV,mBAAmB,CAAC,QAAQ,GAAG,EAAE,CAAC;QAClC,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YACzB,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC;SACzE;QAED,iBAAiB;QACjB,mBAAmB,CAAC,aAAa,GAAG,EAAE,CAAC;QACvC,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YAC9B,mBAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACxC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC/C,mBAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;aAC1E;SACJ;QAED,MAAM;QACN,mBAAmB,CAAC,IAAI,GAAG,EAAE,CAAC;QAC9B,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;YACrB,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACrD;QAED,OAAO;QACP,mBAAmB,CAAC,KAAK,GAAG,EAAE,CAAC;QAC/B,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;YACtB,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACvD;QAED,QAAQ;QACR,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAChC,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;YACvB,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACzD;QAED,SAAS;QACT,mBAAmB,CAAC,YAAY,GAAG,EAAE,CAAC;QACtC,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;YAC7B,mBAAmB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SACrE;QAED,SAAS;QACT,mBAAmB,CAAC,OAAO,GAAG,EAAE,CAAC;QACjC,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;YACxB,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;SACrE;QAED,eAAe;QACf,mBAAmB,CAAC,aAAa,GAAG,EAAE,CAAC;QACvC,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YAC9B,mBAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SACvE;QAED,SAAS;QACT,mBAAmB,CAAC,OAAO,GAAG,EAAE,CAAC;QACjC,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;YACxB,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;SACrE;QAED,eAAe;QACf,mBAAmB,CAAC,aAAa,GAAG,EAAE,CAAC;QACvC,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YAC9B,mBAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SACvE;QAED,UAAU;QACV,mBAAmB,CAAC,QAAQ,GAAG,EAAE,CAAC;QAClC,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YACzB,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;SACvE;QAED,UAAU;QACV,mBAAmB,CAAC,QAAQ,GAAG,EAAE,CAAC;QAClC,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YACzB,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;SACvE;QAED,UAAU;QACV,mBAAmB,CAAC,QAAQ,GAAG,EAAE,CAAC;QAClC,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YACzB,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;SACvE;QAED,aAAa;QACb,mBAAmB,CAAC,WAAW,GAAG,EAAE,CAAC;QACrC,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;YAC5B,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;SAC7E;QAED,SAAS;QACT,mBAAmB,CAAC,QAAQ,GAAG,EAAE,CAAC;QAClC,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YACzB,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;SACvE;QAED,cAAc;QACd,mBAAmB,CAAC,WAAW,GAAG,EAAE,CAAC;QACrC,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;YAC7B,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SACpE;QAED,aAAa;QACb,mBAAmB,CAAC,WAAW,GAAG,EAAE,CAAC;QACrC,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;YAC5B,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;SACnE;QAED,aAAa;QACb,mBAAmB,CAAC,WAAW,GAAG,EAAE,CAAC;QACrC,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;YAC5B,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;SACnE;QAED,eAAe;QACf,mBAAmB,CAAC,cAAc,GAAG,EAAE,CAAC;QACxC,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;YAC/B,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SACzE;QAED,eAAe;QACf,mBAAmB,CAAC,cAAc,GAAG,EAAE,CAAC;QACxC,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;YAC/B,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SACzE;QAED,eAAe;QACf,mBAAmB,CAAC,cAAc,GAAG,EAAE,CAAC;QACxC,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;YAC/B,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SACzE;QAED,kBAAkB;QAClB,mBAAmB,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC3C,KAAK,IAAI,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAClC,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;SAC/E;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,MAAM,QAAQ,GAAG,mBAAmB,CAAC,KAAK,CACtC,GAAG,EAAE,CAAC,IAAI,cAAc,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,sBAAsB,CAAC,EAC9G,MAAM,EACN,KAAK,EACL,OAAO,CACV,CAAC;QAEF,IAAI,IAAY,CAAC;QAEjB,UAAU;QACV,IAAI,MAAM,CAAC,OAAO,EAAE;YAChB,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAC1D;QAED,UAAU;QACV,KAAK,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;YAC1B,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAW,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;SAC5F;QAED,iBAAiB;QACjB,KAAK,IAAI,IAAI,MAAM,CAAC,aAAa,EAAE;YAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,YAAY,GAAc,EAAE,CAAC;YAEnC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC/C,YAAY,CAAC,IAAI,CAAU,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;aAC3E;YACD,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;SAChD;QAED,MAAM;QACN,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE;YACtB,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SAC5C;QAED,OAAO;QACP,KAAK,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;YACvB,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;SAC9C;QAED,QAAQ;QACR,KAAK,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE;YACxB,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;SAChD;QAED,SAAS;QACT,KAAK,IAAI,IAAI,MAAM,CAAC,YAAY,EAAE;YAC9B,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;SACvD;QAED,SAAS;QACT,KAAK,IAAI,IAAI,MAAM,CAAC,OAAO,EAAE;YACzB,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACpE;QAED,gBAAgB;QAChB,KAAK,IAAI,IAAI,MAAM,CAAC,aAAa,EAAE;YAC/B,MAAM,MAAM,GAAa,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC;iBAC9C,MAAM,CAAC,CAAC,GAAyB,EAAE,GAAW,EAAE,CAAS,EAAE,EAAE;gBAC1D,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACb,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;iBACnB;qBAAM;oBACH,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACjC;gBACD,OAAO,GAAG,CAAC;YACf,CAAC,EAAE,EAAE,CAAC;iBACL,GAAG,CAAC,CAAC,KAAwB,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;YAChE,QAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SACzC;QAED,SAAS;QACT,KAAK,IAAI,IAAI,MAAM,CAAC,OAAO,EAAE;YACzB,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACpE;QAED,gBAAgB;QAChB,KAAK,IAAI,IAAI,MAAM,CAAC,aAAa,EAAE;YAC/B,MAAM,MAAM,GAAa,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC;iBAC9C,MAAM,CAAC,CAAC,GAAyB,EAAE,GAAW,EAAE,CAAS,EAAE,EAAE;gBAC1D,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACb,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;iBACnB;qBAAM;oBACH,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACjC;gBACD,OAAO,GAAG,CAAC;YACf,CAAC,EAAE,EAAE,CAAC;iBACL,GAAG,CAAC,CAAC,KAAwB,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;YAChE,QAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SACzC;QAED,UAAU;QACV,KAAK,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;YAC1B,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACvE;QAED,UAAU;QACV,KAAK,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;YAC1B,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACvE;QAED,UAAU;QACV,KAAK,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;YAC1B,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACvE;QAED,aAAa;QACb,KAAK,IAAI,IAAI,MAAM,CAAC,WAAW,EAAE;YAC7B,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAChF;QAED,SAAS;QACT,KAAK,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;YAC1B,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACrE;QAED,cAAc;QACd,KAAK,IAAI,IAAI,MAAM,CAAC,WAAW,EAAE;YAC7B,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;SAC7E;QAED,aAAa;QACb,KAAK,IAAI,IAAI,MAAM,CAAC,WAAW,EAAE;YAC7B,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;SACzD;QAED,aAAa;QACb,KAAK,IAAI,IAAI,MAAM,CAAC,WAAW,EAAE;YAC7B,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;SACzD;QAED,eAAe;QACf,KAAK,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE;YAChC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;SACzD;QAED,eAAe;QACf,KAAK,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE;YAChC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;SACzD;QAED,eAAe;QACf,KAAK,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE;YAChC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;SACzD;QAED,kBAAkB;QAClB,KAAK,IAAI,IAAI,MAAM,CAAC,iBAAiB,EAAE;YACnC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;SAC5D;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,kBAAkB,CAAC,IAAsB,EAAE,GAAW,EAAE,KAAY,EAAE,OAAO,GAAG,EAAE;QAC5F,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE;oBACzB,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE;wBACvB,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,IAAI,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;wBAE/F,IAAI,IAAI,EAAE;4BACN,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;yBACtB;wBAED,OAAO,CAAC,MAAM,CAAC,CAAC;qBACnB;yBAAM;wBACH,MAAM,CAAC,mCAAmC,CAAC,CAAC;qBAC/C;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YACzB,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,qBAAqB,CAAC,SAAiB,EAAE,KAAY,EAAE,OAAO,GAAG,EAAE;QAC7E,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE;oBACzB,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE;wBACvB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;wBACzE,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;wBAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,IAAI,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;wBAE/F,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;wBAE7B,OAAO,CAAC,MAAM,CAAC,CAAC;qBACnB;yBAAM;wBACH,MAAM,CAAC,6BAA6B,GAAG,SAAS,CAAC,CAAC;qBACrD;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;YAC1E,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;;AAzpDD,sCAAsC;AACxB,yBAAU,GAAG,SAAS,CAAC,UAAU,AAAvB,CAAwB;AA0pDhD;;;;;;;GAOG;AACW,qCAAsB,GAAG,cAAc,CAAC,qBAAqB,AAAvC,CAAwC;AAGhF,aAAa,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "sourcesContent": ["import { SerializationHelper } from \"../Misc/decorators.serialization\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Scene } from \"../scene\";\r\nimport { Matrix, Vector3, Vector2, Vector4, Quaternion } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport type { Effect, IEffectCreationOptions } from \"./effect\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport { Color3, Color4 } from \"../Maths/math.color\";\r\nimport { EffectFallbacks } from \"./effectFallbacks\";\r\nimport { WebRequest } from \"../Misc/webRequest\";\r\nimport type { ShaderLanguage } from \"./shaderLanguage\";\r\nimport type { UniformBuffer } from \"./uniformBuffer\";\r\nimport type { TextureSampler } from \"./Textures/textureSampler\";\r\nimport type { StorageBuffer } from \"../Buffers/storageBuffer\";\r\nimport { PushMaterial } from \"./pushMaterial\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { addClipPlaneUniforms, bindClipPlane, prepareStringDefinesForClipPlanes } from \"./clipPlaneMaterialHelper\";\r\n\r\nimport type { ExternalTexture } from \"./Textures/externalTexture\";\r\nimport {\r\n    BindBonesParameters,\r\n    BindFogParameters,\r\n    BindLogDepth,\r\n    BindMorphTargetParameters,\r\n    BindSceneUniformBuffer,\r\n    PrepareAttributesForBakedVertexAnimation,\r\n    PushAttributesForInstances,\r\n} from \"./materialHelper.functions\";\r\n\r\nconst onCreatedEffectParameters = { effect: null as unknown as Effect, subMesh: null as unknown as Nullable<SubMesh> };\r\n\r\n/**\r\n * Defines the options associated with the creation of a shader material.\r\n */\r\nexport interface IShaderMaterialOptions {\r\n    /**\r\n     * Does the material work in alpha blend mode\r\n     */\r\n    needAlphaBlending: boolean;\r\n\r\n    /**\r\n     * Does the material work in alpha test mode\r\n     */\r\n    needAlphaTesting: boolean;\r\n\r\n    /**\r\n     * The list of attribute names used in the shader\r\n     */\r\n    attributes: string[];\r\n\r\n    /**\r\n     * The list of uniform names used in the shader\r\n     */\r\n    uniforms: string[];\r\n\r\n    /**\r\n     * The list of UBO names used in the shader\r\n     */\r\n    uniformBuffers: string[];\r\n\r\n    /**\r\n     * The list of sampler (texture) names used in the shader\r\n     */\r\n    samplers: string[];\r\n\r\n    /**\r\n     * The list of external texture names used in the shader\r\n     */\r\n    externalTextures: string[];\r\n\r\n    /**\r\n     * The list of sampler object names used in the shader\r\n     */\r\n    samplerObjects: string[];\r\n\r\n    /**\r\n     * The list of storage buffer names used in the shader\r\n     */\r\n    storageBuffers: string[];\r\n\r\n    /**\r\n     * The list of defines used in the shader\r\n     */\r\n    defines: string[];\r\n\r\n    /**\r\n     * Defines if clip planes have to be turned on: true to turn them on, false to turn them off and null to turn them on/off depending on the scene configuration (scene.clipPlaneX)\r\n     */\r\n    useClipPlane: Nullable<boolean>;\r\n\r\n    /**\r\n     * The language the shader is written in (default: GLSL)\r\n     */\r\n    shaderLanguage?: ShaderLanguage;\r\n}\r\n\r\n/**\r\n * The ShaderMaterial object has the necessary methods to pass data from your scene to the Vertex and Fragment Shaders and returns a material that can be applied to any mesh.\r\n *\r\n * This returned material effects how the mesh will look based on the code in the shaders.\r\n *\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/shaders/shaderMaterial\r\n */\r\nexport class ShaderMaterial extends PushMaterial {\r\n    private _shaderPath: any;\r\n    private _options: IShaderMaterialOptions;\r\n    private _textures: { [name: string]: BaseTexture } = {};\r\n    private _textureArrays: { [name: string]: BaseTexture[] } = {};\r\n    private _externalTextures: { [name: string]: ExternalTexture } = {};\r\n    private _floats: { [name: string]: number } = {};\r\n    private _ints: { [name: string]: number } = {};\r\n    private _uints: { [name: string]: number } = {};\r\n    private _floatsArrays: { [name: string]: number[] } = {};\r\n    private _colors3: { [name: string]: Color3 } = {};\r\n    private _colors3Arrays: { [name: string]: number[] } = {};\r\n    private _colors4: { [name: string]: Color4 } = {};\r\n    private _colors4Arrays: { [name: string]: number[] } = {};\r\n    private _vectors2: { [name: string]: Vector2 } = {};\r\n    private _vectors3: { [name: string]: Vector3 } = {};\r\n    private _vectors4: { [name: string]: Vector4 } = {};\r\n    private _quaternions: { [name: string]: Quaternion } = {};\r\n    private _quaternionsArrays: { [name: string]: number[] } = {};\r\n    private _matrices: { [name: string]: Matrix } = {};\r\n    private _matrixArrays: { [name: string]: Float32Array | Array<number> } = {};\r\n    private _matrices3x3: { [name: string]: Float32Array | Array<number> } = {};\r\n    private _matrices2x2: { [name: string]: Float32Array | Array<number> } = {};\r\n    private _vectors2Arrays: { [name: string]: number[] } = {};\r\n    private _vectors3Arrays: { [name: string]: number[] } = {};\r\n    private _vectors4Arrays: { [name: string]: number[] } = {};\r\n    private _uniformBuffers: { [name: string]: UniformBuffer } = {};\r\n    private _textureSamplers: { [name: string]: TextureSampler } = {};\r\n    private _storageBuffers: { [name: string]: StorageBuffer } = {};\r\n    private _cachedWorldViewMatrix = new Matrix();\r\n    private _cachedWorldViewProjectionMatrix = new Matrix();\r\n    private _multiview = false;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _materialHelperNeedsPreviousMatrices = false;\r\n\r\n    /** Define the Url to load snippets */\r\n    public static SnippetUrl = Constants.SnippetUrl;\r\n\r\n    /** Snippet ID if the material was created from the snippet server */\r\n    public snippetId: string;\r\n\r\n    /**\r\n     * Instantiate a new shader material.\r\n     * The ShaderMaterial object has the necessary methods to pass data from your scene to the Vertex and Fragment Shaders and returns a material that can be applied to any mesh.\r\n     * This returned material effects how the mesh will look based on the code in the shaders.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/shaders/shaderMaterial\r\n     * @param name Define the name of the material in the scene\r\n     * @param scene Define the scene the material belongs to\r\n     * @param shaderPath Defines  the route to the shader code in one of three ways:\r\n     *  * object: \\{ vertex: \"custom\", fragment: \"custom\" \\}, used with Effect.ShadersStore[\"customVertexShader\"] and Effect.ShadersStore[\"customFragmentShader\"]\r\n     *  * object: \\{ vertexElement: \"vertexShaderCode\", fragmentElement: \"fragmentShaderCode\" \\}, used with shader code in script tags\r\n     *  * object: \\{ vertexSource: \"vertex shader code string\", fragmentSource: \"fragment shader code string\" \\} using with strings containing the shaders code\r\n     *  * string: \"./COMMON_NAME\", used with external files COMMON_NAME.vertex.fx and COMMON_NAME.fragment.fx in index.html folder.\r\n     * @param options Define the options used to create the shader\r\n     * @param storeEffectOnSubMeshes true to store effect on submeshes, false to store the effect directly in the material class.\r\n     */\r\n    constructor(name: string, scene: Scene, shaderPath: any, options: Partial<IShaderMaterialOptions> = {}, storeEffectOnSubMeshes = true) {\r\n        super(name, scene, storeEffectOnSubMeshes);\r\n        this._shaderPath = shaderPath;\r\n\r\n        this._options = {\r\n            needAlphaBlending: false,\r\n            needAlphaTesting: false,\r\n            attributes: [\"position\", \"normal\", \"uv\"],\r\n            uniforms: [\"worldViewProjection\"],\r\n            uniformBuffers: [],\r\n            samplers: [],\r\n            externalTextures: [],\r\n            samplerObjects: [],\r\n            storageBuffers: [],\r\n            defines: [],\r\n            useClipPlane: false,\r\n            ...options,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the shader path used to define the shader code\r\n     * It can be modified to trigger a new compilation\r\n     */\r\n    public get shaderPath(): any {\r\n        return this._shaderPath;\r\n    }\r\n\r\n    /**\r\n     * Sets the shader path used to define the shader code\r\n     * It can be modified to trigger a new compilation\r\n     */\r\n    public set shaderPath(shaderPath: any) {\r\n        this._shaderPath = shaderPath;\r\n    }\r\n\r\n    /**\r\n     * Gets the options used to compile the shader.\r\n     * They can be modified to trigger a new compilation\r\n     */\r\n    public get options(): IShaderMaterialOptions {\r\n        return this._options;\r\n    }\r\n\r\n    /**\r\n     * is multiview set to true?\r\n     */\r\n    public get isMultiview(): boolean {\r\n        return this._multiview;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name of the material e.g. \"ShaderMaterial\"\r\n     * Mainly use in serialization.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"ShaderMaterial\";\r\n    }\r\n\r\n    /**\r\n     * Specifies if the material will require alpha blending\r\n     * @returns a boolean specifying if alpha blending is needed\r\n     */\r\n    public needAlphaBlending(): boolean {\r\n        return this.alpha < 1.0 || this._options.needAlphaBlending;\r\n    }\r\n\r\n    /**\r\n     * Specifies if this material should be rendered in alpha test mode\r\n     * @returns a boolean specifying if an alpha test is needed.\r\n     */\r\n    public needAlphaTesting(): boolean {\r\n        return this._options.needAlphaTesting;\r\n    }\r\n\r\n    private _checkUniform(uniformName: string): void {\r\n        if (this._options.uniforms.indexOf(uniformName) === -1) {\r\n            this._options.uniforms.push(uniformName);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set a texture in the shader.\r\n     * @param name Define the name of the uniform samplers as defined in the shader\r\n     * @param texture Define the texture to bind to this sampler\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setTexture(name: string, texture: BaseTexture): ShaderMaterial {\r\n        if (this._options.samplers.indexOf(name) === -1) {\r\n            this._options.samplers.push(name);\r\n        }\r\n        this._textures[name] = texture;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a texture array in the shader.\r\n     * @param name Define the name of the uniform sampler array as defined in the shader\r\n     * @param textures Define the list of textures to bind to this sampler\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setTextureArray(name: string, textures: BaseTexture[]): ShaderMaterial {\r\n        if (this._options.samplers.indexOf(name) === -1) {\r\n            this._options.samplers.push(name);\r\n        }\r\n\r\n        this._checkUniform(name);\r\n\r\n        this._textureArrays[name] = textures;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set an internal texture in the shader.\r\n     * @param name Define the name of the uniform samplers as defined in the shader\r\n     * @param texture Define the texture to bind to this sampler\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setExternalTexture(name: string, texture: ExternalTexture): ShaderMaterial {\r\n        if (this._options.externalTextures.indexOf(name) === -1) {\r\n            this._options.externalTextures.push(name);\r\n        }\r\n        this._externalTextures[name] = texture;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a float in the shader.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setFloat(name: string, value: number): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._floats[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a int in the shader.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setInt(name: string, value: number): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._ints[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a unsigned int in the shader.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setUInt(name: string, value: number): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._uints[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set an array of floats in the shader.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setFloats(name: string, value: number[]): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._floatsArrays[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec3 in the shader from a Color3.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setColor3(name: string, value: Color3): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._colors3[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec3 array in the shader from a Color3 array.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setColor3Array(name: string, value: Color3[]): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._colors3Arrays[name] = value.reduce((arr, color) => {\r\n            color.toArray(arr, arr.length);\r\n            return arr;\r\n        }, []);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec4 in the shader from a Color4.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setColor4(name: string, value: Color4): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._colors4[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec4 array in the shader from a Color4 array.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setColor4Array(name: string, value: Color4[]): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._colors4Arrays[name] = value.reduce((arr, color) => {\r\n            color.toArray(arr, arr.length);\r\n            return arr;\r\n        }, []);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec2 in the shader from a Vector2.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setVector2(name: string, value: Vector2): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._vectors2[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec3 in the shader from a Vector3.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setVector3(name: string, value: Vector3): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._vectors3[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec4 in the shader from a Vector4.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setVector4(name: string, value: Vector4): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._vectors4[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec4 in the shader from a Quaternion.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setQuaternion(name: string, value: Quaternion): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._quaternions[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec4 array in the shader from a Quaternion array.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setQuaternionArray(name: string, value: Quaternion[]): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._quaternionsArrays[name] = value.reduce((arr, quaternion) => {\r\n            quaternion.toArray(arr, arr.length);\r\n            return arr;\r\n        }, []);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a mat4 in the shader from a Matrix.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setMatrix(name: string, value: Matrix): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._matrices[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a float32Array in the shader from a matrix array.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setMatrices(name: string, value: Matrix[]): ShaderMaterial {\r\n        this._checkUniform(name);\r\n\r\n        const float32Array = new Float32Array(value.length * 16);\r\n\r\n        for (let index = 0; index < value.length; index++) {\r\n            const matrix = value[index];\r\n\r\n            matrix.copyToArray(float32Array, index * 16);\r\n        }\r\n\r\n        this._matrixArrays[name] = float32Array;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a mat3 in the shader from a Float32Array.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setMatrix3x3(name: string, value: Float32Array | Array<number>): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._matrices3x3[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a mat2 in the shader from a Float32Array.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setMatrix2x2(name: string, value: Float32Array | Array<number>): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._matrices2x2[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec2 array in the shader from a number array.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setArray2(name: string, value: number[]): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._vectors2Arrays[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec3 array in the shader from a number array.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setArray3(name: string, value: number[]): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._vectors3Arrays[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec4 array in the shader from a number array.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setArray4(name: string, value: number[]): ShaderMaterial {\r\n        this._checkUniform(name);\r\n        this._vectors4Arrays[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a uniform buffer in the shader\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param buffer Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setUniformBuffer(name: string, buffer: UniformBuffer): ShaderMaterial {\r\n        if (this._options.uniformBuffers.indexOf(name) === -1) {\r\n            this._options.uniformBuffers.push(name);\r\n        }\r\n        this._uniformBuffers[name] = buffer;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a texture sampler in the shader\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param sampler Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setTextureSampler(name: string, sampler: TextureSampler): ShaderMaterial {\r\n        if (this._options.samplerObjects.indexOf(name) === -1) {\r\n            this._options.samplerObjects.push(name);\r\n        }\r\n        this._textureSamplers[name] = sampler;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a storage buffer in the shader\r\n     * @param name Define the name of the storage buffer as defined in the shader\r\n     * @param buffer Define the value to give to the uniform\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setStorageBuffer(name: string, buffer: StorageBuffer): ShaderMaterial {\r\n        if (this._options.storageBuffers.indexOf(name) === -1) {\r\n            this._options.storageBuffers.push(name);\r\n        }\r\n        this._storageBuffers[name] = buffer;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds, removes, or replaces the specified shader define and value.\r\n     * * setDefine(\"MY_DEFINE\", true); // enables a boolean define\r\n     * * setDefine(\"MY_DEFINE\", \"0.5\"); // adds \"#define MY_DEFINE 0.5\" to the shader (or sets and replaces the value of any existing define with that name)\r\n     * * setDefine(\"MY_DEFINE\", false); // disables and removes the define\r\n     * Note if the active defines do change, the shader will be recompiled and this can be expensive.\r\n     * @param define the define name e.g., \"OUTPUT_TO_SRGB\" or \"#define OUTPUT_TO_SRGB\". If the define was passed into the constructor already, the version used should match that, and in either case, it should not include any appended value.\r\n     * @param value either the value of the define (e.g. a numerical value) or for booleans, true if the define should be enabled or false if it should be disabled\r\n     * @returns the material itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setDefine(define: string, value: boolean | string): ShaderMaterial {\r\n        // First remove any existing define with this name.\r\n        const defineName = define.trimEnd() + \" \";\r\n        const existingDefineIdx = this.options.defines.findIndex((x) => x === define || x.startsWith(defineName));\r\n        if (existingDefineIdx >= 0) {\r\n            this.options.defines.splice(existingDefineIdx, 1);\r\n        }\r\n\r\n        // Then add the new define value. (If it's a boolean value and false, don't add it.)\r\n        if (typeof value !== \"boolean\" || value) {\r\n            this.options.defines.push(defineName + value);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Specifies that the submesh is ready to be used\r\n     * @param mesh defines the mesh to check\r\n     * @param subMesh defines which submesh to check\r\n     * @param useInstances specifies that instances should be used\r\n     * @returns a boolean indicating that the submesh is ready or not\r\n     */\r\n    public isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances?: boolean): boolean {\r\n        return this.isReady(mesh, useInstances, subMesh);\r\n    }\r\n\r\n    /**\r\n     * Checks if the material is ready to render the requested mesh\r\n     * @param mesh Define the mesh to render\r\n     * @param useInstances Define whether or not the material is used with instances\r\n     * @param subMesh defines which submesh to render\r\n     * @returns true if ready, otherwise false\r\n     */\r\n    public isReady(mesh?: AbstractMesh, useInstances?: boolean, subMesh?: SubMesh): boolean {\r\n        const storeEffectOnSubMeshes = subMesh && this._storeEffectOnSubMeshes;\r\n\r\n        if (this.isFrozen) {\r\n            const drawWrapper = storeEffectOnSubMeshes ? subMesh._drawWrapper : this._drawWrapper;\r\n            if (drawWrapper.effect && drawWrapper._wasPreviouslyReady && drawWrapper._wasPreviouslyUsingInstances === useInstances) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        const scene = this.getScene();\r\n        const engine = scene.getEngine();\r\n\r\n        // Instances\r\n        const defines = [];\r\n        const attribs = [];\r\n        const fallbacks = new EffectFallbacks();\r\n\r\n        let shaderName = this._shaderPath,\r\n            uniforms = this._options.uniforms,\r\n            uniformBuffers = this._options.uniformBuffers,\r\n            samplers = this._options.samplers;\r\n\r\n        // global multiview\r\n        if (engine.getCaps().multiview && scene.activeCamera && scene.activeCamera.outputRenderTarget && scene.activeCamera.outputRenderTarget.getViewCount() > 1) {\r\n            this._multiview = true;\r\n            defines.push(\"#define MULTIVIEW\");\r\n            if (uniforms.indexOf(\"viewProjection\") !== -1 && uniforms.indexOf(\"viewProjectionR\") === -1) {\r\n                uniforms.push(\"viewProjectionR\");\r\n            }\r\n        }\r\n\r\n        for (let index = 0; index < this._options.defines.length; index++) {\r\n            const defineToAdd = this._options.defines[index].indexOf(\"#define\") === 0 ? this._options.defines[index] : `#define ${this._options.defines[index]}`;\r\n            defines.push(defineToAdd);\r\n        }\r\n\r\n        for (let index = 0; index < this._options.attributes.length; index++) {\r\n            attribs.push(this._options.attributes[index]);\r\n        }\r\n\r\n        if (mesh && mesh.isVerticesDataPresent(VertexBuffer.ColorKind)) {\r\n            if (attribs.indexOf(VertexBuffer.ColorKind) === -1) {\r\n                attribs.push(VertexBuffer.ColorKind);\r\n            }\r\n            defines.push(\"#define VERTEXCOLOR\");\r\n        }\r\n\r\n        if (useInstances) {\r\n            defines.push(\"#define INSTANCES\");\r\n            PushAttributesForInstances(attribs, this._materialHelperNeedsPreviousMatrices);\r\n            if (mesh?.hasThinInstances) {\r\n                defines.push(\"#define THIN_INSTANCES\");\r\n                if (mesh && mesh.isVerticesDataPresent(VertexBuffer.ColorInstanceKind)) {\r\n                    attribs.push(VertexBuffer.ColorInstanceKind);\r\n                    defines.push(\"#define INSTANCESCOLOR\");\r\n                }\r\n            }\r\n        }\r\n\r\n        // Bones\r\n        if (mesh && mesh.useBones && mesh.computeBonesUsingShaders && mesh.skeleton) {\r\n            attribs.push(VertexBuffer.MatricesIndicesKind);\r\n            attribs.push(VertexBuffer.MatricesWeightsKind);\r\n            if (mesh.numBoneInfluencers > 4) {\r\n                attribs.push(VertexBuffer.MatricesIndicesExtraKind);\r\n                attribs.push(VertexBuffer.MatricesWeightsExtraKind);\r\n            }\r\n\r\n            const skeleton = mesh.skeleton;\r\n\r\n            defines.push(\"#define NUM_BONE_INFLUENCERS \" + mesh.numBoneInfluencers);\r\n            fallbacks.addCPUSkinningFallback(0, mesh);\r\n\r\n            if (skeleton.isUsingTextureForMatrices) {\r\n                defines.push(\"#define BONETEXTURE\");\r\n\r\n                if (uniforms.indexOf(\"boneTextureWidth\") === -1) {\r\n                    uniforms.push(\"boneTextureWidth\");\r\n                }\r\n\r\n                if (this._options.samplers.indexOf(\"boneSampler\") === -1) {\r\n                    this._options.samplers.push(\"boneSampler\");\r\n                }\r\n            } else {\r\n                defines.push(\"#define BonesPerMesh \" + (skeleton.bones.length + 1));\r\n\r\n                if (uniforms.indexOf(\"mBones\") === -1) {\r\n                    uniforms.push(\"mBones\");\r\n                }\r\n            }\r\n        } else {\r\n            defines.push(\"#define NUM_BONE_INFLUENCERS 0\");\r\n        }\r\n\r\n        // Morph\r\n        let numInfluencers = 0;\r\n        const manager = mesh ? (<Mesh>mesh).morphTargetManager : null;\r\n        if (manager) {\r\n            const uv = manager.supportsUVs && defines.indexOf(\"#define UV1\") !== -1;\r\n            const tangent = manager.supportsTangents && defines.indexOf(\"#define TANGENT\") !== -1;\r\n            const normal = manager.supportsNormals && defines.indexOf(\"#define NORMAL\") !== -1;\r\n            numInfluencers = manager.numMaxInfluencers || manager.numInfluencers;\r\n            if (uv) {\r\n                defines.push(\"#define MORPHTARGETS_UV\");\r\n            }\r\n            if (tangent) {\r\n                defines.push(\"#define MORPHTARGETS_TANGENT\");\r\n            }\r\n            if (normal) {\r\n                defines.push(\"#define MORPHTARGETS_NORMAL\");\r\n            }\r\n            if (numInfluencers > 0) {\r\n                defines.push(\"#define MORPHTARGETS\");\r\n            }\r\n            if (manager.isUsingTextureForTargets) {\r\n                defines.push(\"#define MORPHTARGETS_TEXTURE\");\r\n\r\n                if (uniforms.indexOf(\"morphTargetTextureIndices\") === -1) {\r\n                    uniforms.push(\"morphTargetTextureIndices\");\r\n                }\r\n\r\n                if (this._options.samplers.indexOf(\"morphTargets\") === -1) {\r\n                    this._options.samplers.push(\"morphTargets\");\r\n                }\r\n            }\r\n            defines.push(\"#define NUM_MORPH_INFLUENCERS \" + numInfluencers);\r\n            for (let index = 0; index < numInfluencers; index++) {\r\n                attribs.push(VertexBuffer.PositionKind + index);\r\n\r\n                if (normal) {\r\n                    attribs.push(VertexBuffer.NormalKind + index);\r\n                }\r\n\r\n                if (tangent) {\r\n                    attribs.push(VertexBuffer.TangentKind + index);\r\n                }\r\n\r\n                if (uv) {\r\n                    attribs.push(VertexBuffer.UVKind + \"_\" + index);\r\n                }\r\n            }\r\n            if (numInfluencers > 0) {\r\n                uniforms = uniforms.slice();\r\n                uniforms.push(\"morphTargetInfluences\");\r\n                uniforms.push(\"morphTargetCount\");\r\n                uniforms.push(\"morphTargetTextureInfo\");\r\n                uniforms.push(\"morphTargetTextureIndices\");\r\n            }\r\n        } else {\r\n            defines.push(\"#define NUM_MORPH_INFLUENCERS 0\");\r\n        }\r\n\r\n        // Baked Vertex Animation\r\n        if (mesh) {\r\n            const bvaManager = (<Mesh>mesh).bakedVertexAnimationManager;\r\n\r\n            if (bvaManager && bvaManager.isEnabled) {\r\n                defines.push(\"#define BAKED_VERTEX_ANIMATION_TEXTURE\");\r\n                if (uniforms.indexOf(\"bakedVertexAnimationSettings\") === -1) {\r\n                    uniforms.push(\"bakedVertexAnimationSettings\");\r\n                }\r\n                if (uniforms.indexOf(\"bakedVertexAnimationTextureSizeInverted\") === -1) {\r\n                    uniforms.push(\"bakedVertexAnimationTextureSizeInverted\");\r\n                }\r\n                if (uniforms.indexOf(\"bakedVertexAnimationTime\") === -1) {\r\n                    uniforms.push(\"bakedVertexAnimationTime\");\r\n                }\r\n\r\n                if (this._options.samplers.indexOf(\"bakedVertexAnimationTexture\") === -1) {\r\n                    this._options.samplers.push(\"bakedVertexAnimationTexture\");\r\n                }\r\n            }\r\n\r\n            PrepareAttributesForBakedVertexAnimation(attribs, mesh, defines);\r\n        }\r\n\r\n        // Textures\r\n        for (const name in this._textures) {\r\n            if (!this._textures[name].isReady()) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        // Alpha test\r\n        if (mesh && this._shouldTurnAlphaTestOn(mesh)) {\r\n            defines.push(\"#define ALPHATEST\");\r\n        }\r\n\r\n        // Clip planes\r\n        if (this._options.useClipPlane !== false) {\r\n            addClipPlaneUniforms(uniforms);\r\n\r\n            prepareStringDefinesForClipPlanes(this, scene, defines);\r\n        }\r\n\r\n        // Fog\r\n        if (scene.fogEnabled && mesh?.applyFog && scene.fogMode !== Scene.FOGMODE_NONE) {\r\n            defines.push(\"#define FOG\");\r\n            if (uniforms.indexOf(\"view\") === -1) {\r\n                uniforms.push(\"view\");\r\n            }\r\n            if (uniforms.indexOf(\"vFogInfos\") === -1) {\r\n                uniforms.push(\"vFogInfos\");\r\n            }\r\n            if (uniforms.indexOf(\"vFogColor\") === -1) {\r\n                uniforms.push(\"vFogColor\");\r\n            }\r\n        }\r\n\r\n        // Misc\r\n        if (this._useLogarithmicDepth) {\r\n            defines.push(\"#define LOGARITHMICDEPTH\");\r\n            if (uniforms.indexOf(\"logarithmicDepthConstant\") === -1) {\r\n                uniforms.push(\"logarithmicDepthConstant\");\r\n            }\r\n        }\r\n\r\n        if (this.customShaderNameResolve) {\r\n            uniforms = uniforms.slice();\r\n            uniformBuffers = uniformBuffers.slice();\r\n            samplers = samplers.slice();\r\n            shaderName = this.customShaderNameResolve(shaderName, uniforms, uniformBuffers, samplers, defines, attribs);\r\n        }\r\n\r\n        const drawWrapper = storeEffectOnSubMeshes ? subMesh._getDrawWrapper(undefined, true) : this._drawWrapper;\r\n        const previousEffect = drawWrapper?.effect ?? null;\r\n        const previousDefines = drawWrapper?.defines ?? null;\r\n        const join = defines.join(\"\\n\");\r\n\r\n        let effect = previousEffect;\r\n        if (previousDefines !== join) {\r\n            effect = engine.createEffect(\r\n                shaderName,\r\n                <IEffectCreationOptions>{\r\n                    attributes: attribs,\r\n                    uniformsNames: uniforms,\r\n                    uniformBuffersNames: uniformBuffers,\r\n                    samplers: samplers,\r\n                    defines: join,\r\n                    fallbacks: fallbacks,\r\n                    onCompiled: this.onCompiled,\r\n                    onError: this.onError,\r\n                    indexParameters: { maxSimultaneousMorphTargets: numInfluencers },\r\n                    shaderLanguage: this._options.shaderLanguage,\r\n                },\r\n                engine\r\n            );\r\n\r\n            if (storeEffectOnSubMeshes) {\r\n                subMesh.setEffect(effect, join, this._materialContext);\r\n            } else if (drawWrapper) {\r\n                drawWrapper.setEffect(effect, join);\r\n            }\r\n\r\n            if (this._onEffectCreatedObservable) {\r\n                onCreatedEffectParameters.effect = effect;\r\n                onCreatedEffectParameters.subMesh = subMesh ?? mesh?.subMeshes[0] ?? null;\r\n                this._onEffectCreatedObservable.notifyObservers(onCreatedEffectParameters);\r\n            }\r\n        }\r\n\r\n        drawWrapper!._wasPreviouslyUsingInstances = !!useInstances;\r\n\r\n        if (!effect?.isReady() ?? true) {\r\n            return false;\r\n        }\r\n\r\n        if (previousEffect !== effect) {\r\n            scene.resetCachedMaterial();\r\n        }\r\n\r\n        drawWrapper!._wasPreviouslyReady = true;\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Binds the world matrix to the material\r\n     * @param world defines the world transformation matrix\r\n     * @param effectOverride - If provided, use this effect instead of internal effect\r\n     */\r\n    public bindOnlyWorldMatrix(world: Matrix, effectOverride?: Nullable<Effect>): void {\r\n        const scene = this.getScene();\r\n\r\n        const effect = effectOverride ?? this.getEffect();\r\n\r\n        if (!effect) {\r\n            return;\r\n        }\r\n\r\n        if (this._options.uniforms.indexOf(\"world\") !== -1) {\r\n            effect.setMatrix(\"world\", world);\r\n        }\r\n\r\n        if (this._options.uniforms.indexOf(\"worldView\") !== -1) {\r\n            world.multiplyToRef(scene.getViewMatrix(), this._cachedWorldViewMatrix);\r\n            effect.setMatrix(\"worldView\", this._cachedWorldViewMatrix);\r\n        }\r\n\r\n        if (this._options.uniforms.indexOf(\"worldViewProjection\") !== -1) {\r\n            world.multiplyToRef(scene.getTransformMatrix(), this._cachedWorldViewProjectionMatrix);\r\n            effect.setMatrix(\"worldViewProjection\", this._cachedWorldViewProjectionMatrix);\r\n        }\r\n\r\n        if (this._options.uniforms.indexOf(\"view\") !== -1) {\r\n            effect.setMatrix(\"view\", scene.getViewMatrix());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Binds the submesh to this material by preparing the effect and shader to draw\r\n     * @param world defines the world transformation matrix\r\n     * @param mesh defines the mesh containing the submesh\r\n     * @param subMesh defines the submesh to bind the material to\r\n     */\r\n    public bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void {\r\n        this.bind(world, mesh, subMesh._drawWrapperOverride?.effect, subMesh);\r\n    }\r\n\r\n    /**\r\n     * Binds the material to the mesh\r\n     * @param world defines the world transformation matrix\r\n     * @param mesh defines the mesh to bind the material to\r\n     * @param effectOverride - If provided, use this effect instead of internal effect\r\n     * @param subMesh defines the submesh to bind the material to\r\n     */\r\n    public bind(world: Matrix, mesh?: Mesh, effectOverride?: Nullable<Effect>, subMesh?: SubMesh): void {\r\n        // Std values\r\n        const storeEffectOnSubMeshes = subMesh && this._storeEffectOnSubMeshes;\r\n        const effect = effectOverride ?? (storeEffectOnSubMeshes ? subMesh.effect : this.getEffect());\r\n\r\n        if (!effect) {\r\n            return;\r\n        }\r\n\r\n        const scene = this.getScene();\r\n\r\n        this._activeEffect = effect;\r\n\r\n        this.bindOnlyWorldMatrix(world, effectOverride);\r\n\r\n        const uniformBuffers = this._options.uniformBuffers;\r\n\r\n        let useSceneUBO = false;\r\n\r\n        if (effect && uniformBuffers && uniformBuffers.length > 0 && scene.getEngine().supportsUniformBuffers) {\r\n            for (let i = 0; i < uniformBuffers.length; ++i) {\r\n                const bufferName = uniformBuffers[i];\r\n                switch (bufferName) {\r\n                    case \"Mesh\":\r\n                        if (mesh) {\r\n                            mesh.getMeshUniformBuffer().bindToEffect(effect, \"Mesh\");\r\n                            mesh.transferToEffect(world);\r\n                        }\r\n                        break;\r\n                    case \"Scene\":\r\n                        BindSceneUniformBuffer(effect, scene.getSceneUniformBuffer());\r\n                        scene.finalizeSceneUbo();\r\n                        useSceneUBO = true;\r\n                        break;\r\n                }\r\n            }\r\n        }\r\n\r\n        const mustRebind = mesh && storeEffectOnSubMeshes ? this._mustRebind(scene, effect, subMesh, mesh.visibility) : scene.getCachedMaterial() !== this;\r\n\r\n        if (effect && mustRebind) {\r\n            if (!useSceneUBO && this._options.uniforms.indexOf(\"view\") !== -1) {\r\n                effect.setMatrix(\"view\", scene.getViewMatrix());\r\n            }\r\n\r\n            if (!useSceneUBO && this._options.uniforms.indexOf(\"projection\") !== -1) {\r\n                effect.setMatrix(\"projection\", scene.getProjectionMatrix());\r\n            }\r\n\r\n            if (!useSceneUBO && this._options.uniforms.indexOf(\"viewProjection\") !== -1) {\r\n                effect.setMatrix(\"viewProjection\", scene.getTransformMatrix());\r\n                if (this._multiview) {\r\n                    effect.setMatrix(\"viewProjectionR\", scene._transformMatrixR);\r\n                }\r\n            }\r\n\r\n            if (scene.activeCamera && this._options.uniforms.indexOf(\"cameraPosition\") !== -1) {\r\n                effect.setVector3(\"cameraPosition\", scene.activeCamera!.globalPosition);\r\n            }\r\n\r\n            // Bones\r\n            BindBonesParameters(mesh, effect);\r\n\r\n            // Clip plane\r\n            bindClipPlane(effect, this, scene);\r\n\r\n            // Misc\r\n            if (this._useLogarithmicDepth) {\r\n                BindLogDepth(storeEffectOnSubMeshes ? subMesh.materialDefines : effect.defines, effect, scene);\r\n            }\r\n\r\n            // Fog\r\n            if (mesh) {\r\n                BindFogParameters(scene, mesh, effect);\r\n            }\r\n\r\n            let name: string;\r\n            // Texture\r\n            for (name in this._textures) {\r\n                effect.setTexture(name, this._textures[name]);\r\n            }\r\n\r\n            // Texture arrays\r\n            for (name in this._textureArrays) {\r\n                effect.setTextureArray(name, this._textureArrays[name]);\r\n            }\r\n\r\n            // External texture\r\n            for (name in this._externalTextures) {\r\n                effect.setExternalTexture(name, this._externalTextures[name]);\r\n            }\r\n\r\n            // Int\r\n            for (name in this._ints) {\r\n                effect.setInt(name, this._ints[name]);\r\n            }\r\n\r\n            // UInt\r\n            for (name in this._uints) {\r\n                effect.setUInt(name, this._uints[name]);\r\n            }\r\n\r\n            // Float\r\n            for (name in this._floats) {\r\n                effect.setFloat(name, this._floats[name]);\r\n            }\r\n\r\n            // Floats\r\n            for (name in this._floatsArrays) {\r\n                effect.setArray(name, this._floatsArrays[name]);\r\n            }\r\n\r\n            // Color3\r\n            for (name in this._colors3) {\r\n                effect.setColor3(name, this._colors3[name]);\r\n            }\r\n\r\n            // Color3Array\r\n            for (name in this._colors3Arrays) {\r\n                effect.setArray3(name, this._colors3Arrays[name]);\r\n            }\r\n\r\n            // Color4\r\n            for (name in this._colors4) {\r\n                const color = this._colors4[name];\r\n                effect.setFloat4(name, color.r, color.g, color.b, color.a);\r\n            }\r\n\r\n            // Color4Array\r\n            for (name in this._colors4Arrays) {\r\n                effect.setArray4(name, this._colors4Arrays[name]);\r\n            }\r\n\r\n            // Vector2\r\n            for (name in this._vectors2) {\r\n                effect.setVector2(name, this._vectors2[name]);\r\n            }\r\n\r\n            // Vector3\r\n            for (name in this._vectors3) {\r\n                effect.setVector3(name, this._vectors3[name]);\r\n            }\r\n\r\n            // Vector4\r\n            for (name in this._vectors4) {\r\n                effect.setVector4(name, this._vectors4[name]);\r\n            }\r\n\r\n            // Quaternion\r\n            for (name in this._quaternions) {\r\n                effect.setQuaternion(name, this._quaternions[name]);\r\n            }\r\n\r\n            // Matrix\r\n            for (name in this._matrices) {\r\n                effect.setMatrix(name, this._matrices[name]);\r\n            }\r\n\r\n            // MatrixArray\r\n            for (name in this._matrixArrays) {\r\n                effect.setMatrices(name, this._matrixArrays[name]);\r\n            }\r\n\r\n            // Matrix 3x3\r\n            for (name in this._matrices3x3) {\r\n                effect.setMatrix3x3(name, this._matrices3x3[name]);\r\n            }\r\n\r\n            // Matrix 2x2\r\n            for (name in this._matrices2x2) {\r\n                effect.setMatrix2x2(name, this._matrices2x2[name]);\r\n            }\r\n\r\n            // Vector2Array\r\n            for (name in this._vectors2Arrays) {\r\n                effect.setArray2(name, this._vectors2Arrays[name]);\r\n            }\r\n\r\n            // Vector3Array\r\n            for (name in this._vectors3Arrays) {\r\n                effect.setArray3(name, this._vectors3Arrays[name]);\r\n            }\r\n\r\n            // Vector4Array\r\n            for (name in this._vectors4Arrays) {\r\n                effect.setArray4(name, this._vectors4Arrays[name]);\r\n            }\r\n\r\n            // QuaternionArray\r\n            for (name in this._quaternionsArrays) {\r\n                effect.setArray4(name, this._quaternionsArrays[name]);\r\n            }\r\n\r\n            // Uniform buffers\r\n            for (name in this._uniformBuffers) {\r\n                const buffer = this._uniformBuffers[name].getBuffer();\r\n                if (buffer) {\r\n                    effect.bindUniformBuffer(buffer, name);\r\n                }\r\n            }\r\n\r\n            // Samplers\r\n            for (name in this._textureSamplers) {\r\n                effect.setTextureSampler(name, this._textureSamplers[name]);\r\n            }\r\n\r\n            // Storage buffers\r\n            for (name in this._storageBuffers) {\r\n                effect.setStorageBuffer(name, this._storageBuffers[name]);\r\n            }\r\n        }\r\n\r\n        if (effect && mesh && (mustRebind || !this.isFrozen)) {\r\n            // Morph targets\r\n            const manager = (<Mesh>mesh).morphTargetManager;\r\n            if (manager && manager.numInfluencers > 0) {\r\n                BindMorphTargetParameters(<Mesh>mesh, effect);\r\n            }\r\n\r\n            const bvaManager = (<Mesh>mesh).bakedVertexAnimationManager;\r\n\r\n            if (bvaManager && bvaManager.isEnabled) {\r\n                const drawWrapper = storeEffectOnSubMeshes ? subMesh._drawWrapper : this._drawWrapper;\r\n                mesh.bakedVertexAnimationManager?.bind(effect, !!drawWrapper._wasPreviouslyUsingInstances);\r\n            }\r\n        }\r\n\r\n        this._afterBind(mesh, effect, subMesh);\r\n    }\r\n\r\n    /**\r\n     * Gets the active textures from the material\r\n     * @returns an array of textures\r\n     */\r\n    public getActiveTextures(): BaseTexture[] {\r\n        const activeTextures = super.getActiveTextures();\r\n\r\n        for (const name in this._textures) {\r\n            activeTextures.push(this._textures[name]);\r\n        }\r\n\r\n        for (const name in this._textureArrays) {\r\n            const array = this._textureArrays[name];\r\n            for (let index = 0; index < array.length; index++) {\r\n                activeTextures.push(array[index]);\r\n            }\r\n        }\r\n\r\n        return activeTextures;\r\n    }\r\n\r\n    /**\r\n     * Specifies if the material uses a texture\r\n     * @param texture defines the texture to check against the material\r\n     * @returns a boolean specifying if the material uses the texture\r\n     */\r\n    public hasTexture(texture: BaseTexture): boolean {\r\n        if (super.hasTexture(texture)) {\r\n            return true;\r\n        }\r\n\r\n        for (const name in this._textures) {\r\n            if (this._textures[name] === texture) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        for (const name in this._textureArrays) {\r\n            const array = this._textureArrays[name];\r\n            for (let index = 0; index < array.length; index++) {\r\n                if (array[index] === texture) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Makes a duplicate of the material, and gives it a new name\r\n     * @param name defines the new name for the duplicated material\r\n     * @returns the cloned material\r\n     */\r\n    public clone(name: string): ShaderMaterial {\r\n        const result = SerializationHelper.Clone(() => new ShaderMaterial(name, this.getScene(), this._shaderPath, this._options, this._storeEffectOnSubMeshes), this);\r\n\r\n        result.name = name;\r\n        result.id = name;\r\n\r\n        // Shader code path\r\n        if (typeof result._shaderPath === \"object\") {\r\n            result._shaderPath = { ...result._shaderPath };\r\n        }\r\n\r\n        // Options\r\n        this._options = { ...this._options };\r\n\r\n        (Object.keys(this._options) as Array<keyof IShaderMaterialOptions>).forEach((propName) => {\r\n            const propValue = this._options[propName];\r\n            if (Array.isArray(propValue)) {\r\n                (<string[]>this._options[propName]) = propValue.slice(0);\r\n            }\r\n        });\r\n\r\n        // Stencil\r\n        this.stencil.copyTo(result.stencil);\r\n\r\n        // Texture\r\n        for (const key in this._textures) {\r\n            result.setTexture(key, this._textures[key]);\r\n        }\r\n\r\n        // TextureArray\r\n        for (const key in this._textureArrays) {\r\n            result.setTextureArray(key, this._textureArrays[key]);\r\n        }\r\n\r\n        // External texture\r\n        for (const key in this._externalTextures) {\r\n            result.setExternalTexture(key, this._externalTextures[key]);\r\n        }\r\n\r\n        // Int\r\n        for (const key in this._ints) {\r\n            result.setInt(key, this._ints[key]);\r\n        }\r\n\r\n        // UInt\r\n        for (const key in this._uints) {\r\n            result.setUInt(key, this._uints[key]);\r\n        }\r\n\r\n        // Float\r\n        for (const key in this._floats) {\r\n            result.setFloat(key, this._floats[key]);\r\n        }\r\n\r\n        // Floats\r\n        for (const key in this._floatsArrays) {\r\n            result.setFloats(key, this._floatsArrays[key]);\r\n        }\r\n\r\n        // Color3\r\n        for (const key in this._colors3) {\r\n            result.setColor3(key, this._colors3[key]);\r\n        }\r\n\r\n        // Color3Array\r\n        for (const key in this._colors3Arrays) {\r\n            result._colors3Arrays[key] = this._colors3Arrays[key];\r\n        }\r\n\r\n        // Color4\r\n        for (const key in this._colors4) {\r\n            result.setColor4(key, this._colors4[key]);\r\n        }\r\n\r\n        // Color4Array\r\n        for (const key in this._colors4Arrays) {\r\n            result._colors4Arrays[key] = this._colors4Arrays[key];\r\n        }\r\n\r\n        // Vector2\r\n        for (const key in this._vectors2) {\r\n            result.setVector2(key, this._vectors2[key]);\r\n        }\r\n\r\n        // Vector3\r\n        for (const key in this._vectors3) {\r\n            result.setVector3(key, this._vectors3[key]);\r\n        }\r\n\r\n        // Vector4\r\n        for (const key in this._vectors4) {\r\n            result.setVector4(key, this._vectors4[key]);\r\n        }\r\n\r\n        // Quaternion\r\n        for (const key in this._quaternions) {\r\n            result.setQuaternion(key, this._quaternions[key]);\r\n        }\r\n\r\n        // QuaternionArray\r\n        for (const key in this._quaternionsArrays) {\r\n            result._quaternionsArrays[key] = this._quaternionsArrays[key];\r\n        }\r\n\r\n        // Matrix\r\n        for (const key in this._matrices) {\r\n            result.setMatrix(key, this._matrices[key]);\r\n        }\r\n\r\n        // MatrixArray\r\n        for (const key in this._matrixArrays) {\r\n            result._matrixArrays[key] = this._matrixArrays[key].slice();\r\n        }\r\n\r\n        // Matrix 3x3\r\n        for (const key in this._matrices3x3) {\r\n            result.setMatrix3x3(key, this._matrices3x3[key]);\r\n        }\r\n\r\n        // Matrix 2x2\r\n        for (const key in this._matrices2x2) {\r\n            result.setMatrix2x2(key, this._matrices2x2[key]);\r\n        }\r\n\r\n        // Vector2Array\r\n        for (const key in this._vectors2Arrays) {\r\n            result.setArray2(key, this._vectors2Arrays[key]);\r\n        }\r\n\r\n        // Vector3Array\r\n        for (const key in this._vectors3Arrays) {\r\n            result.setArray3(key, this._vectors3Arrays[key]);\r\n        }\r\n\r\n        // Vector4Array\r\n        for (const key in this._vectors4Arrays) {\r\n            result.setArray4(key, this._vectors4Arrays[key]);\r\n        }\r\n\r\n        // Uniform buffers\r\n        for (const key in this._uniformBuffers) {\r\n            result.setUniformBuffer(key, this._uniformBuffers[key]);\r\n        }\r\n\r\n        // Samplers\r\n        for (const key in this._textureSamplers) {\r\n            result.setTextureSampler(key, this._textureSamplers[key]);\r\n        }\r\n\r\n        // Storag buffers\r\n        for (const key in this._storageBuffers) {\r\n            result.setStorageBuffer(key, this._storageBuffers[key]);\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Disposes the material\r\n     * @param forceDisposeEffect specifies if effects should be forcefully disposed\r\n     * @param forceDisposeTextures specifies if textures should be forcefully disposed\r\n     * @param notBoundToMesh specifies if the material that is being disposed is known to be not bound to any mesh\r\n     */\r\n    public dispose(forceDisposeEffect?: boolean, forceDisposeTextures?: boolean, notBoundToMesh?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            let name: string;\r\n            for (name in this._textures) {\r\n                this._textures[name].dispose();\r\n            }\r\n\r\n            for (name in this._textureArrays) {\r\n                const array = this._textureArrays[name];\r\n                for (let index = 0; index < array.length; index++) {\r\n                    array[index].dispose();\r\n                }\r\n            }\r\n        }\r\n\r\n        this._textures = {};\r\n\r\n        super.dispose(forceDisposeEffect, forceDisposeTextures, notBoundToMesh);\r\n    }\r\n\r\n    /**\r\n     * Serializes this material in a JSON representation\r\n     * @returns the serialized material object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n        serializationObject.customType = \"BABYLON.ShaderMaterial\";\r\n        serializationObject.uniqueId = this.uniqueId;\r\n\r\n        serializationObject.options = this._options;\r\n        serializationObject.shaderPath = this._shaderPath;\r\n        serializationObject.storeEffectOnSubMeshes = this._storeEffectOnSubMeshes;\r\n\r\n        let name: string;\r\n\r\n        // Stencil\r\n        serializationObject.stencil = this.stencil.serialize();\r\n\r\n        // Texture\r\n        serializationObject.textures = {};\r\n        for (name in this._textures) {\r\n            serializationObject.textures[name] = this._textures[name].serialize();\r\n        }\r\n\r\n        // Texture arrays\r\n        serializationObject.textureArrays = {};\r\n        for (name in this._textureArrays) {\r\n            serializationObject.textureArrays[name] = [];\r\n            const array = this._textureArrays[name];\r\n            for (let index = 0; index < array.length; index++) {\r\n                serializationObject.textureArrays[name].push(array[index].serialize());\r\n            }\r\n        }\r\n\r\n        // Int\r\n        serializationObject.ints = {};\r\n        for (name in this._ints) {\r\n            serializationObject.ints[name] = this._ints[name];\r\n        }\r\n\r\n        // UInt\r\n        serializationObject.uints = {};\r\n        for (name in this._uints) {\r\n            serializationObject.uints[name] = this._uints[name];\r\n        }\r\n\r\n        // Float\r\n        serializationObject.floats = {};\r\n        for (name in this._floats) {\r\n            serializationObject.floats[name] = this._floats[name];\r\n        }\r\n\r\n        // Floats\r\n        serializationObject.floatsArrays = {};\r\n        for (name in this._floatsArrays) {\r\n            serializationObject.floatsArrays[name] = this._floatsArrays[name];\r\n        }\r\n\r\n        // Color3\r\n        serializationObject.colors3 = {};\r\n        for (name in this._colors3) {\r\n            serializationObject.colors3[name] = this._colors3[name].asArray();\r\n        }\r\n\r\n        // Color3 array\r\n        serializationObject.colors3Arrays = {};\r\n        for (name in this._colors3Arrays) {\r\n            serializationObject.colors3Arrays[name] = this._colors3Arrays[name];\r\n        }\r\n\r\n        // Color4\r\n        serializationObject.colors4 = {};\r\n        for (name in this._colors4) {\r\n            serializationObject.colors4[name] = this._colors4[name].asArray();\r\n        }\r\n\r\n        // Color4 array\r\n        serializationObject.colors4Arrays = {};\r\n        for (name in this._colors4Arrays) {\r\n            serializationObject.colors4Arrays[name] = this._colors4Arrays[name];\r\n        }\r\n\r\n        // Vector2\r\n        serializationObject.vectors2 = {};\r\n        for (name in this._vectors2) {\r\n            serializationObject.vectors2[name] = this._vectors2[name].asArray();\r\n        }\r\n\r\n        // Vector3\r\n        serializationObject.vectors3 = {};\r\n        for (name in this._vectors3) {\r\n            serializationObject.vectors3[name] = this._vectors3[name].asArray();\r\n        }\r\n\r\n        // Vector4\r\n        serializationObject.vectors4 = {};\r\n        for (name in this._vectors4) {\r\n            serializationObject.vectors4[name] = this._vectors4[name].asArray();\r\n        }\r\n\r\n        // Quaternion\r\n        serializationObject.quaternions = {};\r\n        for (name in this._quaternions) {\r\n            serializationObject.quaternions[name] = this._quaternions[name].asArray();\r\n        }\r\n\r\n        // Matrix\r\n        serializationObject.matrices = {};\r\n        for (name in this._matrices) {\r\n            serializationObject.matrices[name] = this._matrices[name].asArray();\r\n        }\r\n\r\n        // MatrixArray\r\n        serializationObject.matrixArray = {};\r\n        for (name in this._matrixArrays) {\r\n            serializationObject.matrixArray[name] = this._matrixArrays[name];\r\n        }\r\n\r\n        // Matrix 3x3\r\n        serializationObject.matrices3x3 = {};\r\n        for (name in this._matrices3x3) {\r\n            serializationObject.matrices3x3[name] = this._matrices3x3[name];\r\n        }\r\n\r\n        // Matrix 2x2\r\n        serializationObject.matrices2x2 = {};\r\n        for (name in this._matrices2x2) {\r\n            serializationObject.matrices2x2[name] = this._matrices2x2[name];\r\n        }\r\n\r\n        // Vector2Array\r\n        serializationObject.vectors2Arrays = {};\r\n        for (name in this._vectors2Arrays) {\r\n            serializationObject.vectors2Arrays[name] = this._vectors2Arrays[name];\r\n        }\r\n\r\n        // Vector3Array\r\n        serializationObject.vectors3Arrays = {};\r\n        for (name in this._vectors3Arrays) {\r\n            serializationObject.vectors3Arrays[name] = this._vectors3Arrays[name];\r\n        }\r\n\r\n        // Vector4Array\r\n        serializationObject.vectors4Arrays = {};\r\n        for (name in this._vectors4Arrays) {\r\n            serializationObject.vectors4Arrays[name] = this._vectors4Arrays[name];\r\n        }\r\n\r\n        // QuaternionArray\r\n        serializationObject.quaternionsArrays = {};\r\n        for (name in this._quaternionsArrays) {\r\n            serializationObject.quaternionsArrays[name] = this._quaternionsArrays[name];\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Creates a shader material from parsed shader material data\r\n     * @param source defines the JSON representation of the material\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a new material\r\n     */\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): ShaderMaterial {\r\n        const material = SerializationHelper.Parse(\r\n            () => new ShaderMaterial(source.name, scene, source.shaderPath, source.options, source.storeEffectOnSubMeshes),\r\n            source,\r\n            scene,\r\n            rootUrl\r\n        );\r\n\r\n        let name: string;\r\n\r\n        // Stencil\r\n        if (source.stencil) {\r\n            material.stencil.parse(source.stencil, scene, rootUrl);\r\n        }\r\n\r\n        // Texture\r\n        for (name in source.textures) {\r\n            material.setTexture(name, <Texture>Texture.Parse(source.textures[name], scene, rootUrl));\r\n        }\r\n\r\n        // Texture arrays\r\n        for (name in source.textureArrays) {\r\n            const array = source.textureArrays[name];\r\n            const textureArray: Texture[] = [];\r\n\r\n            for (let index = 0; index < array.length; index++) {\r\n                textureArray.push(<Texture>Texture.Parse(array[index], scene, rootUrl));\r\n            }\r\n            material.setTextureArray(name, textureArray);\r\n        }\r\n\r\n        // Int\r\n        for (name in source.ints) {\r\n            material.setInt(name, source.ints[name]);\r\n        }\r\n\r\n        // UInt\r\n        for (name in source.uints) {\r\n            material.setUInt(name, source.uints[name]);\r\n        }\r\n\r\n        // Float\r\n        for (name in source.floats) {\r\n            material.setFloat(name, source.floats[name]);\r\n        }\r\n\r\n        // Floats\r\n        for (name in source.floatsArrays) {\r\n            material.setFloats(name, source.floatsArrays[name]);\r\n        }\r\n\r\n        // Color3\r\n        for (name in source.colors3) {\r\n            material.setColor3(name, Color3.FromArray(source.colors3[name]));\r\n        }\r\n\r\n        // Color3 arrays\r\n        for (name in source.colors3Arrays) {\r\n            const colors: Color3[] = source.colors3Arrays[name]\r\n                .reduce((arr: Array<Array<number>>, num: number, i: number) => {\r\n                    if (i % 3 === 0) {\r\n                        arr.push([num]);\r\n                    } else {\r\n                        arr[arr.length - 1].push(num);\r\n                    }\r\n                    return arr;\r\n                }, [])\r\n                .map((color: ArrayLike<number>) => Color3.FromArray(color));\r\n            material.setColor3Array(name, colors);\r\n        }\r\n\r\n        // Color4\r\n        for (name in source.colors4) {\r\n            material.setColor4(name, Color4.FromArray(source.colors4[name]));\r\n        }\r\n\r\n        // Color4 arrays\r\n        for (name in source.colors4Arrays) {\r\n            const colors: Color4[] = source.colors4Arrays[name]\r\n                .reduce((arr: Array<Array<number>>, num: number, i: number) => {\r\n                    if (i % 4 === 0) {\r\n                        arr.push([num]);\r\n                    } else {\r\n                        arr[arr.length - 1].push(num);\r\n                    }\r\n                    return arr;\r\n                }, [])\r\n                .map((color: ArrayLike<number>) => Color4.FromArray(color));\r\n            material.setColor4Array(name, colors);\r\n        }\r\n\r\n        // Vector2\r\n        for (name in source.vectors2) {\r\n            material.setVector2(name, Vector2.FromArray(source.vectors2[name]));\r\n        }\r\n\r\n        // Vector3\r\n        for (name in source.vectors3) {\r\n            material.setVector3(name, Vector3.FromArray(source.vectors3[name]));\r\n        }\r\n\r\n        // Vector4\r\n        for (name in source.vectors4) {\r\n            material.setVector4(name, Vector4.FromArray(source.vectors4[name]));\r\n        }\r\n\r\n        // Quaternion\r\n        for (name in source.quaternions) {\r\n            material.setQuaternion(name, Quaternion.FromArray(source.quaternions[name]));\r\n        }\r\n\r\n        // Matrix\r\n        for (name in source.matrices) {\r\n            material.setMatrix(name, Matrix.FromArray(source.matrices[name]));\r\n        }\r\n\r\n        // MatrixArray\r\n        for (name in source.matrixArray) {\r\n            material._matrixArrays[name] = new Float32Array(source.matrixArray[name]);\r\n        }\r\n\r\n        // Matrix 3x3\r\n        for (name in source.matrices3x3) {\r\n            material.setMatrix3x3(name, source.matrices3x3[name]);\r\n        }\r\n\r\n        // Matrix 2x2\r\n        for (name in source.matrices2x2) {\r\n            material.setMatrix2x2(name, source.matrices2x2[name]);\r\n        }\r\n\r\n        // Vector2Array\r\n        for (name in source.vectors2Arrays) {\r\n            material.setArray2(name, source.vectors2Arrays[name]);\r\n        }\r\n\r\n        // Vector3Array\r\n        for (name in source.vectors3Arrays) {\r\n            material.setArray3(name, source.vectors3Arrays[name]);\r\n        }\r\n\r\n        // Vector4Array\r\n        for (name in source.vectors4Arrays) {\r\n            material.setArray4(name, source.vectors4Arrays[name]);\r\n        }\r\n\r\n        // QuaternionArray\r\n        for (name in source.quaternionsArrays) {\r\n            material.setArray4(name, source.quaternionsArrays[name]);\r\n        }\r\n\r\n        return material;\r\n    }\r\n\r\n    /**\r\n     * Creates a new ShaderMaterial from a snippet saved in a remote file\r\n     * @param name defines the name of the ShaderMaterial to create (can be null or empty to use the one from the json data)\r\n     * @param url defines the url to load from\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a promise that will resolve to the new ShaderMaterial\r\n     */\r\n    public static ParseFromFileAsync(name: Nullable<string>, url: string, scene: Scene, rootUrl = \"\"): Promise<ShaderMaterial> {\r\n        return new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        const serializationObject = JSON.parse(request.responseText);\r\n                        const output = this.Parse(serializationObject, scene || EngineStore.LastCreatedScene, rootUrl);\r\n\r\n                        if (name) {\r\n                            output.name = name;\r\n                        }\r\n\r\n                        resolve(output);\r\n                    } else {\r\n                        reject(\"Unable to load the ShaderMaterial\");\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", url);\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a ShaderMaterial from a snippet saved by the Inspector\r\n     * @param snippetId defines the snippet to load\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a promise that will resolve to the new ShaderMaterial\r\n     */\r\n    public static ParseFromSnippetAsync(snippetId: string, scene: Scene, rootUrl = \"\"): Promise<ShaderMaterial> {\r\n        return new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        const snippet = JSON.parse(JSON.parse(request.responseText).jsonPayload);\r\n                        const serializationObject = JSON.parse(snippet.shaderMaterial);\r\n                        const output = this.Parse(serializationObject, scene || EngineStore.LastCreatedScene, rootUrl);\r\n\r\n                        output.snippetId = snippetId;\r\n\r\n                        resolve(output);\r\n                    } else {\r\n                        reject(\"Unable to load the snippet \" + snippetId);\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", this.SnippetUrl + \"/\" + snippetId.replace(/#/g, \"/\"));\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a ShaderMaterial from a snippet saved by the Inspector\r\n     * @deprecated Please use ParseFromSnippetAsync instead\r\n     * @param snippetId defines the snippet to load\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a promise that will resolve to the new ShaderMaterial\r\n     */\r\n    public static CreateFromSnippetAsync = ShaderMaterial.ParseFromSnippetAsync;\r\n}\r\n\r\nRegisterClass(\"BABYLON.ShaderMaterial\", ShaderMaterial);\r\n"]}