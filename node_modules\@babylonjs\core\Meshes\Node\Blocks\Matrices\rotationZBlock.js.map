{"version": 3, "file": "rotationZBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Meshes/Node/Blocks/Matrices/rotationZBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,qCAAqC,EAAE,MAAM,8CAA8C,CAAC;AAErG,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,MAAM,EAAE,MAAM,+BAA+B,CAAC;AAEvD;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,iBAAiB;IACjD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAEZ,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QACnF,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,MAAM,CAAC,CAAC;IAChF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa;QAChB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YACzB,MAAM,UAAU,GAAG,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACnD,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;YACrB,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC3C;IACL,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;YACpC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC;IACN,CAAC;CACJ;AAED,aAAa,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "sourcesContent": ["import { NodeGeometry<PERSON>lock } from \"../../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../../nodeGeometryBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"../../Enums/nodeGeometryConnectionPointTypes\";\r\nimport type { NodeGeometryBuildState } from \"../../nodeGeometryBuildState\";\r\nimport { GeometryInputBlock } from \"../geometryInputBlock\";\r\nimport { Matrix } from \"../../../../Maths/math.vector\";\r\n\r\n/**\r\n * Block used to get a rotation matrix on Z Axis\r\n */\r\nexport class RotationZBlock extends NodeGeometryBlock {\r\n    /**\r\n     * Create a new RotationZBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"angle\", NodeGeometryBlockConnectionPointTypes.Float, false, 0);\r\n        this.registerOutput(\"matrix\", NodeGeometryBlockConnectionPointTypes.Matrix);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"RotationZBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the angle input component\r\n     */\r\n    public get angle(): NodeGeometryConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the matrix output component\r\n     */\r\n    public get matrix(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public autoConfigure() {\r\n        if (!this.angle.isConnected) {\r\n            const angleInput = new GeometryInputBlock(\"Angle\");\r\n            angleInput.value = 0;\r\n            angleInput.output.connectTo(this.angle);\r\n        }\r\n    }\r\n\r\n    protected _buildBlock(state: NodeGeometryBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        this.matrix._storedFunction = (state) => {\r\n            return Matrix.RotationZ(this.angle.getConnectedValue(state));\r\n        };\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.RotationZBlock\", RotationZBlock);\r\n"]}