{"version": 3, "file": "thinEngine.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Engines/thinEngine.ts"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAG5C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAO/C,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,iBAAiB,EAAE,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AACtD,OAAO,EAAE,UAAU,EAAE,MAAM,6BAA6B,CAAC;AACzD,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,eAAe,EAAE,qBAAqB,EAAE,MAAM,uCAAuC,CAAC;AAI/F,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AACjF,OAAO,EAAE,oBAAoB,EAAE,MAAM,+BAA+B,CAAC;AACrE,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AACvE,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAElE,OAAO,EAAE,oBAAoB,EAAE,MAAM,8BAA8B,CAAC;AAOpE,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAGpE,OAAO,EAAE,oBAAoB,EAAE,MAAM,8BAA8B,CAAC;AACpE,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAIvD,OAAO,EAAE,oBAAoB,EAAE,MAAM,gCAAgC,CAAC;AAKtE,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAQ7D,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAYtD;;GAEG;AACH,MAAM,aAAa;CASlB;AAqHD;;GAEG;AACH,MAAM,OAAO,UAAU;IAyBnB;;OAEG;IACH,8CAA8C;IACvC,MAAM,KAAK,UAAU;QACxB,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,OAAO;QACrB,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,IAAI,WAAW,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC;QAEhD,IAAI,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE;YAClC,WAAW,IAAI,gCAAgC,CAAC;SACnD;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAKD;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAa;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAID,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IASD;;OAEG;IACI,MAAM,KAAK,iBAAiB;QAC/B,OAAO,MAAM,CAAC,iBAAiB,CAAC;IACpC,CAAC;IACM,MAAM,KAAK,iBAAiB,CAAC,KAAa;QAC7C,MAAM,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACrC,CAAC;IAID;;OAEG;IACI,mBAAmB,CAAC,cAA8B;QACrD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAgCD;;;OAGG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED,IAAW,qBAAqB,CAAC,UAAU;QACvC,IAAI,UAAU,KAAK,IAAI,CAAC,sBAAsB,EAAE;YAC5C,OAAO;SACV;QAED,IAAI,CAAC,sBAAsB,GAAG,UAAU,CAAC;QAEzC,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC;SACxD;aAAM;YACH,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC;SACxD;IACL,CAAC;IAuBD;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAYD;;;OAGG;IACH,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC;IAChE,CAAC;IAmBD;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAGD,gBAAgB;IAChB,IAAW,6BAA6B;QACpC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,4BAA4B,IAAI,IAAI,CAAC,4BAA4B,CAAC,CAAC;IAC5F,CAAC;IAED;;;OAGG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC;IAC3D,CAAC;IAyBD;;;OAGG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAkBD;;;OAGG;IACH,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED,IAAW,sBAAsB,CAAC,KAAc;QAC5C,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;IACzC,CAAC;IA6GD,IAAc,iCAAiC;QAC3C,OAAO,KAAK,CAAC;IACjB,CAAC;IAID;;;;OAIG;IACH,IAAW,2BAA2B,CAAC,UAA6E;QAChH,IAAI,CAAC,4BAA4B,GAAG,UAAU,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,kBAAkB,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,4BAA4B,CAAC,CAAC;SAC3J;QAED,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,kBAAkB,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,4BAA4B,CAAC,CAAC;SAClK;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB;QAC1B,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,CACpD,IAAI,UAAU,CAAC,CAAC,CAAC,EACjB,CAAC,EACD,CAAC,EACD,CAAC,EACD,SAAS,CAAC,kBAAkB,EAC5B,KAAK,EACL,KAAK,EACL,SAAS,CAAC,4BAA4B,CACzC,CAAC;SACL;QAED,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,QAAQ,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAC9E,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAC9C,QAAQ,EACR,CAAC,EACD,SAAS,CAAC,kBAAkB,EAC5B,SAAS,CAAC,wBAAwB,EAClC,KAAK,EACL,KAAK,EACL,SAAS,CAAC,4BAA4B,CACzC,CAAC;SACL;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAcD;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAID;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;;OAGG;IACH,IAAW,iBAAiB;QACxB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAW,iBAAiB,CAAC,QAAQ;QACjC,mDAAmD;IACvD,CAAC;IAGD;;OAEG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED,IAAW,qBAAqB,CAAC,IAAY;QACzC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;IACvC,CAAC;IAOD;;OAEG;IACI,sBAAsB;QACzB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnC,CAAC;IAIO,MAAM,CAAC,aAAa,CAAC,KAAa,EAAE,MAAc;QACtD,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;YACjC,OAAsB,IAAI,eAAe,CAAC,KAAK,EAAE,MAAM,CAAE,CAAC;SAC7D;QACD,MAAM,MAAM,GAAkB,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAE,CAAC;QAChE,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;QACrB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,KAAa,EAAE,MAAc;QAC7C,OAAO,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,OAAO,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;;;;;OAMG;IACH,YACI,eAA+G,EAC/G,SAAmB,EACnB,OAAuB,EACvB,kBAA4B;QAliBhC,gBAAgB;QACN,UAAK,GAAG,OAAO,CAAC;QAoBhB,gBAAW,GAAG,KAAK,CAAC;QAgC9B;;WAEG;QACI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG;QACI,iBAAY,GAAG,KAAK,CAAC;QAE5B;;;WAGG;QACI,kBAAa,GAAsB,IAAI,CAAC;QAE/C;;WAEG;QACI,2BAAsB,GAAG,IAAI,CAAC;QAErC;;WAEG;QACI,kCAA6B,GAAG,KAAK,CAAC;QAE7C,iGAAiG;QAC1F,2BAAsB,GAAG,KAAK,CAAC;QAE9B,2BAAsB,GAAG,KAAK,CAAC;QAuBvC;;WAEG;QACa,oBAAe,GAAY,KAAK,CAAC;QAEjD;;WAEG;QACa,wBAAmB,GAAY,IAAI,CAAC;QAEpD;;WAEG;QACI,0BAAqB,GAAG,KAAK,CAAC;QAErC;;WAEG;QACa,wBAAmB,GAAG,IAAI,UAAU,EAAc,CAAC;QAE3D,aAAQ,GAAG,CAAC,CAAC;QAarB,gBAAgB;QACT,oBAAe,GAAG,IAAI,KAAK,EAAiB,CAAC;QACpD,gBAAgB;QACT,oBAAe,GAAG,IAAI,KAAK,EAAiB,CAAC;QAcpD,gBAAgB;QACT,kBAAa,GAAG,GAAG,CAAC;QAEjB,wBAAmB,GAAG,KAAK,CAAC;QAkB5B,iCAA4B,GAAG,IAAI,CAAC;QAc9C,gBAAgB;QACT,WAAM,GAAG,KAAK,CAAC;QAEtB,gBAAgB;QACT,kBAAa,GAAG,KAAK,CAAC;QAiBnB,uBAAkB,GAAG,IAAI,KAAK,EAAc,CAAC;QAUvD,eAAe;QACf;;WAEG;QACI,4BAAuB,GAAG,IAAI,UAAU,EAAc,CAAC;QAC9D;;WAEG;QACI,gCAA2B,GAAG,IAAI,UAAU,EAAc,CAAC;QAGxD,oBAAe,GAAG,KAAK,CAAC;QAElC,gBAAgB;QACT,4BAAuB,GAAG,KAAK,CAAC;QAcvC;;WAEG;QACI,8BAAyB,GAAG,KAAK,CAAC;QAEzC,SAAS;QACT,gBAAgB;QACN,gBAAW,GAAG,IAAI,CAAC;QAC7B,gBAAgB;QACN,uBAAkB,GAAG,IAAI,CAAC;QACpC,gBAAgB;QACN,uBAAkB,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACvD,gBAAgB;QACN,0BAAqB,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC7D,gBAAgB;QACN,kBAAa,GAAG,IAAI,YAAY,EAAE,CAAC;QAC7C,gBAAgB;QACT,gBAAW,GAAG,IAAI,UAAU,EAAE,CAAC;QACtC,gBAAgB;QACT,eAAU,GAAG,SAAS,CAAC,SAAS,CAAC;QACxC,gBAAgB;QACT,mBAAc,GAAG,SAAS,CAAC,aAAa,CAAC;QAEhD,QAAQ;QACR,gBAAgB;QACT,2BAAsB,GAAG,IAAI,KAAK,EAAmB,CAAC;QAC7D,gBAAgB;QACT,8BAAyB,GAAG,IAAI,KAAK,EAAuB,CAAC;QACpE,gBAAgB;QACN,mBAAc,GAAG,CAAC,CAAC;QACrB,2BAAsB,GAAG,CAAC,CAAC,CAAC;QACpC,gBAAgB;QACN,wBAAmB,GAAiD,EAAE,CAAC;QAQvE,qBAAgB,GAA8B,EAAE,CAAC;QACnD,+BAA0B,GAAc,EAAE,CAAC;QAWnD,gBAAgB;QACT,yBAAoB,GAAkC,IAAI,CAAC;QAC1D,6BAAwB,GAAG,KAAK,CAAC;QAC/B,wBAAmB,GAAG,IAAI,KAAK,EAAwB,CAAC;QAClE,gBAAgB;QACT,wBAAmB,GAA+B,IAAI,CAAC;QAC9D,gBAAgB;QACT,sBAAiB,GAA+B,IAAI,CAAC;QACpD,2BAAsB,GAAG,IAAI,KAAK,EAAiB,CAAC;QACpD,8BAAyB,GAAG,IAAI,KAAK,EAAU,CAAC;QAChD,4BAAuB,GAAG,IAAI,KAAK,EAAc,CAAC;QAQ1D,gBAAgB;QACT,yBAAoB,GAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QAEpD,yBAAoB,GAAG,KAAK,CAAC;QAC7B,8BAAyB,GAAG,KAAK,CAAC;QAO1C,gBAAgB;QACT,kBAAa,GAAW,CAAC,CAAC;QAEzB,0BAAqB,GAAG,IAAI,KAAK,EAAU,CAAC;QAC5C,6BAAwB,GAAG,CAAC,CAAC;QAC7B,4BAAuB,GAAqB,IAAI,CAAC;QAEjD,oBAAe,GAAG,IAAI,KAAK,EAAgB,CAAC;QAEpD;;WAEG;QACI,uBAAkB,GAAY,KAAK,CAAC;QAC3C,gBAAgB;QACN,0BAAqB,GAAW,GAAG,CAAC;QAE9C,gBAAgB;QACT,yBAAoB,GAAsC,IAAI,CAAC;QAEtE;;WAEG;QACI,oBAAe,GAAoB;YACtC,QAAQ,EAAE,KAAK;SAClB,CAAC;QAuFF;;WAEG;QACI,uBAAkB,GAAY,IAAI,CAAC;QAE1C;;WAEG;QACI,kCAA6B,GAAG,IAAI,UAAU,EAAW,CAAC;QAEjE,gBAAgB;QACN,cAAS,GAAY,KAAK,CAAC;QA6B3B,2BAAsB,GAAG,SAAS,CAAC,0BAA0B,CAAC;QA+iC9D,oBAAe,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QA+xF/C,uBAAkB,GAAsB,IAAI,CAAC;QAErD;;;;WAIG;QACI,4BAAuB,GAAG,IAAI,CAAC;QA+gB5B,mBAAc,GAA4C,EAAE,CAAC;QAjyInE,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;QAEnC,IAAI,MAAM,GAAgC,IAAI,CAAC;QAE/C,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAExB,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;QAEhC,qCAAqC;QACrC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,IAAI,KAAK,CAAC;QAEtD,IAAI,CAAC,qBAAqB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAE9D,uBAAuB,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAE7E,OAAO,CAAC,SAAS,GAAG,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC;QACnD,OAAO,CAAC,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,IAAI,KAAK,CAAC;QACvE,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,IAAI,CAAC,CAAC;QACzD,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC;QAC9C,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC;QAClD,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC;QAE1C,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,kBAAkB,EAAE,YAAY,IAAI,IAAI,CAAC;QACtE,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,kBAAkB,EAAE,gBAAgB,IAAI,IAAI,CAAC;QAC9E,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,IAAI,CAAC;QAC7D,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC,uBAAuB,IAAI,KAAK,CAAC;QACxE,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC;QAChE,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAEvD,WAAW;QACX,kBAAkB,GAAG,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,IAAI,KAAK,CAAC;QAE/E,MAAM,gBAAgB,GAAG,mBAAmB,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAEtF,MAAM,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,IAAI,gBAAgB,CAAC;QACtE,IAAI,CAAC,qBAAqB,GAAG,kBAAkB,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAC3G,IAAI,CAAC,qBAAqB,GAAG,gBAAgB,CAAC;QAE9C,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO;SACV;QAED,IAAK,eAAuB,CAAC,UAAU,EAAE;YACrC,MAAM,GAAsB,eAAe,CAAC;YAC5C,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;YAE/B,IAAI,OAAO,CAAC,qBAAqB,KAAK,SAAS,EAAE;gBAC7C,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC;aACzC;YAED,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;gBACpC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;aAC/B;YAED,aAAa;YACb,IAAI,SAAS,IAAI,SAAS,CAAC,SAAS,EAAE;gBAClC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAE1B,MAAM,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC;gBAC/B,KAAK,MAAM,SAAS,IAAI,UAAU,CAAC,aAAa,EAAE;oBAC9C,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC;oBAC1B,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;oBAClC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;oBAE9B,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;wBAChB,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,iBAAiB,EAAE;4BAClD,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;4BAClC,MAAM,UAAU,GAAG,SAAS,CAAC,iBAAiB,CAAC;4BAE/C,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;4BAClC,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;4BAE/B,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gCAC/B,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gCAC5D,IAAI,aAAa,IAAI,UAAU,EAAE;oCAC7B,SAAS;iCACZ;6BACJ;yBACJ;wBAED,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;4BAC1B,QAAQ,MAAM,EAAE;gCACZ,KAAK,eAAe;oCAChB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;oCAClC,MAAM;gCACV,KAAK,KAAK;oCACN,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;oCACtC,MAAM;gCACV,KAAK,WAAW;oCACZ,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;oCAC1B,MAAM;gCACV,KAAK,gBAAgB;oCACjB,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC;oCACjC,MAAM;6BACb;yBACJ;qBACJ;iBACJ;aACJ;YAED,eAAe;YACf,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAC/B,IAAI,CAAC,cAAc,GAAG,CAAC,GAAU,EAAE,EAAE;oBACjC,GAAG,CAAC,cAAc,EAAE,CAAC;oBACrB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC5B,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;oBAEnC,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACvD,CAAC,CAAC;gBAEF,IAAI,CAAC,kBAAkB,GAAG,GAAG,EAAE;oBAC3B,IAAI,CAAC,8BAA8B,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;gBACrE,CAAC,CAAC;gBAEF,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;gBACxE,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,EAAE,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;gBAEhF,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,kBAAkB,CAAC;aAC3E;YAED,yDAAyD;YACzD,IAAI,CAAC,aAAa,GAAG,gCAAgC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAChF,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;aAChC;YAED,KAAK;YACL,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;gBAC/B,IAAI;oBACA,IAAI,CAAC,GAAG,GAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC,CAAC;oBAC5G,IAAI,IAAI,CAAC,GAAG,EAAE;wBACV,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;wBACzB,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;wBAEpC,qDAAqD;wBACrD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE;4BACvB,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;4BACzB,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;yBACvC;qBACJ;iBACJ;gBAAC,OAAO,CAAC,EAAE;oBACR,aAAa;iBAChB;aACJ;YAED,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,IAAI,CAAC,MAAM,EAAE;oBACT,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;iBAChE;gBACD,IAAI;oBACA,IAAI,CAAC,GAAG,GAA2B,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC,CAAC;iBAChI;gBAAC,OAAO,CAAC,EAAE;oBACR,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;iBAC1C;aACJ;YAED,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACX,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;aAC1C;SACJ;aAAM;YACH,IAAI,CAAC,GAAG,GAA2B,eAAe,CAAC;YACnD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,MAA2B,CAAC;YAE7D,IAAK,IAAI,CAAC,GAAW,CAAC,8BAA8B,EAAE;gBAClD,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;gBACzB,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;aACvC;iBAAM;gBACH,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;aACvC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE,CAAC;YACnD,IAAI,UAAU,EAAE;gBACZ,OAAO,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;aACxC;SACJ;QAED,wEAAwE;QACxE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEjF,IAAI,OAAO,CAAC,sBAAsB,KAAK,SAAS,EAAE;YAC9C,IAAI,CAAC,4BAA4B,GAAG,OAAO,CAAC,sBAAsB,CAAC;SACtE;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,0BAA0B;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE;YAClD,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,IAAI,aAAa,EAAE,CAAC;SACxD;QAED,mBAAmB;QACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;QAEzG,iDAAiD;QACjD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAEvF,iDAAiD;QACjD,6DAA6D;QAE7D,yCAAyC;QACzC,wCAAwC;QACxC,+BAA+B;QAC/B,QAAQ;QACR,IAAI;QAEJ,MAAM,YAAY,GAAG,eAAe,UAAU,CAAC,OAAO,EAAE,CAAC;QACzD,MAAM,CAAC,GAAG,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAEpD,wCAAwC;QACxC,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE;YAC7D,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SACnE;IACL,CAAC;IAES,kBAAkB;QACxB,IAAI,CAAC,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,EAAE;YACrC,OAAO;SACV;QAED,gDAAgD;QAChD,IAAI,CAAC,eAAe,GAAG,GAAG,EAAE;YACxB,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;YACtC,IAAI,CAAC,eAAe,CAAC,QAAQ;gBACzB,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAClC,uIAAuI;oBACvI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,mBAAmB,EAAE,IAAI,YAAY,IAAI,QAAQ,CAAC,CAAC;QAC/F,CAAC,CAAC;QAEF,6BAA6B;QAC7B,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,2GAA2G;QAC3G,IAAI,mBAAmB,EAAE,EAAE;YACvB,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;SAC3D;IACL,CAAC;IAES,oBAAoB;QAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IACrC,CAAC;IAES,yBAAyB;QAC/B,8CAA8C;QAC9C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAEtB,kBAAkB;QAClB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,sBAAsB,EAAE,EAAE,CAAC;QAEhC,QAAQ;QACR,gMAAgM;QAChM,oMAAoM;QAEpM,kBAAkB;QAClB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,mBAAmB;QACnB,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,mBAAmB;QACnB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,mBAAmB;QACnB,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpC,0EAA0E;QAC1E,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAES,oBAAoB;QAC1B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,iCAAiC,CAAC,CAAC;QAC3D,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IACjC,CAAC;IAES,8BAA8B,CAAC,UAAsB;QAC3D,4DAA4D;QAC5D,UAAU,CAAC,KAAK,IAAI,EAAE;YAClB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,kFAAkF;YACvI,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC;YACpD,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC;YACpD,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;YAEnD,kBAAkB;YAClB,MAAM,UAAU,EAAE,CAAC;YACnB,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAEjC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC;YAC9C,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC;YAC9C,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC;YAE7C,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC;IAED;;;OAGG;IACO,WAAW,CAAC,MAAyB;QAC3C,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,cAA8B;QAC7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,wBAAwB;QAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC,CAAC,iDAAiD;QAE3G,KAAK,MAAM,eAAe,IAAI,YAAY,EAAE;YACxC,eAAe,CAAC,QAAQ,EAAE,CAAC;SAC9B;IACL,CAAC;IAEO,4BAA4B;QAChC,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC,CAAC,iDAAiD;QAE9G,KAAK,MAAM,mBAAmB,IAAI,YAAY,EAAE;YAC5C,mBAAmB,CAAC,QAAQ,EAAE,CAAC;SAClC;IACL,CAAC;IAEO,eAAe;QACnB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACrC,MAAM,MAAM,GAAW,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAElD,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAC,oHAAoH;YACpJ,MAAM,CAAC,cAAc,EAAE,CAAC;SAC3B;QAED,MAAM,CAAC,UAAU,EAAE,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACrC,MAAM,MAAM,GAAW,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAElD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;gBACnB,OAAO,KAAK,CAAC;aAChB;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,eAAe;QACrB,WAAW;QACX,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,eAAe,EAAE;YAC9C,aAAa,CAAC,wBAAwB,EAAE,CAAC;SAC5C;IACL,CAAC;IAES,gBAAgB,KAAU,CAAC;IAE3B,cAAc;QACpB,OAAO;QACP,IAAI,CAAC,KAAK,GAAG;YACT,qBAAqB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC;YAC9E,6BAA6B,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,gCAAgC,CAAC;YAC/F,0BAA0B,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,8BAA8B,CAAC;YAC1F,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;YAChE,UAAU,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YACpF,qBAAqB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC;YAChF,oBAAoB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC;YAC3E,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC;YACpE,iBAAiB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC;YACtE,yBAAyB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC;YACvF,uBAAuB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,0BAA0B,CAAC;YACnF,qBAAqB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,6BAA6B,CAAC,IAAI,SAAS;YACxF,mBAAmB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,0BAA0B,CAAC,KAAK,IAAI;YACzG,aAAa,EAAE,CAAC;YAChB,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,+BAA+B,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,sCAAsC,CAAC;YAC7H,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,8BAA8B,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,qCAAqC,CAAC;YAC3H,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,+BAA+B,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,sCAAsC,CAAC;YAC7H,gEAAgE;YAChE,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oCAAoC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,2CAA2C,CAAC;YAC5I,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,gCAAgC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,uCAAuC,CAAC;YAChI,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,+BAA+B,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,sCAAsC,CAAC;YAC7H,IAAI,EACA,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,8BAA8B,CAAC;gBACrD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,qCAAqC,CAAC;gBAC5D,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,gCAAgC,CAAC;YAC3D,iCAAiC,EAC7B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,gCAAgC,CAAC;gBACvD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,uCAAuC,CAAC;gBAC9D,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oCAAoC,CAAC;YAC/D,WAAW,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,wBAAwB,CAAC,KAAK,IAAI;YAC/F,sBAAsB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,IAAI;YAClG,4BAA4B,EAAE,KAAK;YACnC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,iCAAiC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,0BAA0B,CAAC;YACzH,qBAAqB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC;YAC7C,4BAA4B,EAAE,KAAK;YACnC,oBAAoB,EAAE,KAAK;YAC3B,cAAc,EAAE,CAAC;YACjB,gBAAgB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;YAC/F,2BAA2B,EAAE,KAAK;YAClC,wBAAwB,EAAE,KAAK;YAC/B,oBAAoB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,6BAA6B,CAAC,CAAC;YACxG,YAAY,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YACjG,gBAAgB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YAC1G,sBAAsB,EAAE,KAAK;YAC7B,2BAA2B,EAAE,KAAK;YAClC,kBAAkB,EAAE,KAAK;YACzB,+BAA+B,EAAE,KAAK;YACtC,iBAAiB,EAAE,KAAK;YACxB,eAAe,EAAE,KAAK;YACtB,UAAU,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YACpG,UAAU,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACpC,WAAW,EAAE,KAAK;YAClB,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,gBAAgB,CAAC;YAClD,eAAe,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,kBAAkB,CAAC;YAC1D,qBAAqB,EAAE,KAAK;YAC5B,kBAAkB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC;YAC1C,gBAAgB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC;YACxC,qBAAqB,EAAE,KAAK;YAC5B,kBAAkB,EAAE,KAAK;YACzB,yBAAyB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC;YACjD,eAAe,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC;YACvC,2BAA2B,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,GAAG;YACpH,yBAAyB,EAAE,KAAK;SACnC,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,2BAA2B,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;QACrE,IAAI,CAAC,KAAK,CAAC,wBAAwB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;QAElE,QAAQ;QACR,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE1D,MAAM,YAAY,GAAQ,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC;QAC7E,IAAI,YAAY,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,uBAAuB,CAAC,CAAC;YAC/E,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;SAC9E;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,gBAAgB,CAAC;SAC/E;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,kBAAkB,CAAC;SACrF;QAED,YAAY;QACZ,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,KAAK,MAAM,EAAE;YACpC,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC,qCAAqC;SAC1E;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,MAAM,EAAE;YAC7B,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,qEAAqE;SACnG;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,MAAM,EAAE;YAC7B,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,qEAAqE;SACnG;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,gBAAgB,KAAK,KAAK,EAAE;YACrC,IAAI,CAAC,GAAG,CAAC,gBAAgB,GAAG,KAAK,CAAC;SACrC;QAED,aAAa;QACb,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;YACvB,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE;gBAC1B,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAS,IAAI,CAAC,KAAK,CAAC,UAAW,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;aAC5F;YACD,gDAAgD;YAChD,IAAI,CAAC,KAAK,CAAC,4BAA4B,GAAG,CAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,sBAAsB,CAAY,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;SACzK;QAED,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,iCAAiC;YACnE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,8BAA8B,CAAC;YACpG,CAAC,CAAC,CAAC,CAAC;QACR,IAAI,CAAC,KAAK,CAAC,2BAA2B,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACrI,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAC9G,IAAI,CAAC,KAAK,CAAC,+BAA+B;YACtC,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAErI,qBAAqB;QACrB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;YACjB,IAAI,CAAC,GAAG,CAAC,oCAAoC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oCAAoC,CAAC;SACxG;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;YACjB,IAAI,CAAC,GAAG,CAAC,oCAAoC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oCAAoC,CAAC;SACxG;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YACtB,IAAI,CAAC,GAAG,CAAC,6BAA6B,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,6BAA6B,CAAC;YAC5F,IAAI,CAAC,GAAG,CAAC,mCAAmC,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,mCAAmC,CAAC;YACxG,IAAI,CAAC,GAAG,CAAC,mCAAmC,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,mCAAmC,CAAC;SAC3G;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;YACjB,IAAI,CAAC,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC;YACvE,IAAI,CAAC,GAAG,CAAC,gCAAgC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gCAAgC,CAAC;SAChG;QAED,kFAAkF;QAClF,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YACxB,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,KAAK,MAAM,EAAE;gBACpC,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,MAAM,CAAC;aACpC;SACJ;QACD,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAC3G,eAAe;QACf,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACvC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,uBAAuB,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;SAClJ;aAAM;YACH,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;YAEzE,IAAI,oBAAoB,KAAK,IAAI,EAAE;gBAC/B,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;gBACvC,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACvF,IAAI,CAAC,GAAG,CAAC,gBAAwB,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;gBAE1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;oBACnB,IAAI,CAAC,GAAI,CAAC,kBAAkB,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAS,oBAAqB,CAAC,kBAAkB,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC;iBACvH;aACJ;SACJ;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,IAAI,CAAC;SAC3C;aAAM;YACH,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;YAE3E,IAAI,qBAAqB,IAAI,IAAI,EAAE;gBAC/B,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,IAAI,CAAC;gBACxC,IAAI,CAAC,GAAG,CAAC,iBAAiB,GAAG,qBAAqB,CAAC,uBAAuB,CAAC;aAC9E;SACJ;QAED,sBAAsB;QACtB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC;SACxC;aAAM,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACvC;aAAM;YACH,MAAM,0BAA0B,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,yBAAyB,CAAC,CAAC;YAEpF,IAAI,0BAA0B,IAAI,IAAI,EAAE;gBACpC,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBACpC,IAAI,CAAC,GAAG,CAAC,iBAAiB,GAAG,0BAA0B,CAAC,oBAAoB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBAC9G,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,0BAA0B,CAAC,kBAAkB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBAC1G,IAAI,CAAC,GAAG,CAAC,iBAAiB,GAAG,0BAA0B,CAAC,oBAAoB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;aACjH;SACJ;QAED,kBAAkB;QAClB,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC;SACrC;aAAM;YACH,MAAM,iBAAiB,GAA2B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;YAElG,IAAI,iBAAiB,IAAI,IAAI,EAAE;gBAC3B,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC;gBAClC,IAAI,CAAC,GAAG,CAAC,mBAAmB,GAAG,iBAAiB,CAAC,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAClG,IAAI,CAAC,GAAG,CAAC,qBAAqB,GAAG,iBAAiB,CAAC,0BAA0B,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBACtG,IAAI,CAAC,GAAG,CAAC,mBAAmB,GAAG,iBAAiB,CAAC,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;aACrG;iBAAM;gBACH,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;aACtC;SACJ;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,wBAAwB,EAAE;YACnC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACnG,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAEvG,IAAI,WAAW,IAAI,aAAa,EAAE;gBAC9B,IAAI,CAAC,KAAK,CAAC,4BAA4B,GAAG,WAAW,CAAC,SAAS,KAAK,CAAC,IAAI,aAAa,CAAC,SAAS,KAAK,CAAC,CAAC;aAC1G;SACJ;QAED,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;SACjC;aAAM;YACH,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;YACvE,IAAI,oBAAoB,IAAI,IAAI,EAAE;gBAC9B,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;gBAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,oBAAoB,CAAC,OAA4C,CAAC;gBACjF,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,oBAAoB,CAAC,OAA4C,CAAC;aACpF;SACJ;QAED,eAAe;QACf,6EAA6E;QAC7E,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;YAChC,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;gBACxB,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBACrC,IAAI,CAAC,sBAAsB,GAAG;oBAC1B,IAAI,EAAE,sBAAsB,CAAC,IAAI;oBACjC,KAAK,EAAE,sBAAsB,CAAC,KAAK;oBACnC,YAAY,EAAE,sBAAsB,CAAC,YAAY;iBACpD,CAAC;aACL;iBAAM;gBACH,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAExD,IAAI,aAAa,IAAI,IAAI,EAAE;oBACvB,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC;oBACrC,IAAI,CAAC,sBAAsB,GAAG;wBAC1B,IAAI,EAAE,aAAa,CAAC,QAAqE;wBACzF,KAAK,EAAE,aAAa,CAAC,cAAkF;wBACvG,YAAY,EAAE,aAAa,CAAC,cAA2F;qBAC1H,CAAC;iBACL;aACJ;YACD,kEAAkE;YAClE,mHAAmH;YACnH,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;SACnJ;QAED,eAAe;QACf,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;QACzC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QACpD,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;QAEzC,eAAe;QACf,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC;QACzE,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,wBAAwB,EAAE,IAAI,EAAE,EAAE;YAC7D,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzC;QAED,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EAAE;YACjC,yEAAyE;YACzE,IAAI,CAAC,KAAK,CAAC,yBAAyB,GAAG,IAAI,CAAC;SAC/C;IACL,CAAC;IAES,aAAa;QACnB,IAAI,CAAC,SAAS,GAAG;YACb,+BAA+B,EAAE,OAAO,gBAAgB,KAAK,WAAW;YACxE,yCAAyC,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACnE,0BAA0B,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACpD,qBAAqB,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YAC/C,4BAA4B,EAAE,KAAK;YACnC,wBAAwB,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YAClD,gBAAgB,EAAE,KAAK;YACvB,4BAA4B,EAAE,KAAK;YACnC,UAAU,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACpC,aAAa,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACvC,iBAAiB,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YAC3C,+BAA+B,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACzD,WAAW,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACrC,YAAY,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACtC,6BAA6B,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACvD,yBAAyB,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACnD,sBAAsB,EAAE,IAAI;YAC5B,oBAAoB,EAAE,IAAI;YAC1B,kBAAkB,EAAE,IAAI;YACxB,sBAAsB,EAAE,KAAK;YAC7B,8BAA8B,EAAE,KAAK;YACrC,mBAAmB,EAAE,KAAK;YAC1B,uBAAuB,EAAE,IAAI;YAC7B,8CAA8C,EAAE,KAAK;YACrD,0BAA0B,EAAE,KAAK;SACpC,CAAC;IACN,CAAC;IAED;;;OAGG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,gBAAgB;IACT,qBAAqB;QACxB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,OAAO;SACV;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAErD,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;SAClC;IACL,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,mBAAmB,EAAE;YACxC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC,EAAE;gBACtE,SAAS;aACZ;YACD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;SACxC;QAED,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO;YACH,MAAM,EAAE,IAAI,CAAC,SAAS;YACtB,QAAQ,EAAE,IAAI,CAAC,WAAW;YAC1B,OAAO,EAAE,IAAI,CAAC,UAAU;SAC3B,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACI,uBAAuB,CAAC,KAAa;QACxC,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,IAAI,CAAC,MAAM,EAAE,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACI,uBAAuB;QAC1B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,sBAAsB;QACzB,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,cAA2B;QAC7C,IAAI,CAAC,cAAc,EAAE;YACjB,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;YACnC,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO;SACV;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAE9D,IAAI,KAAK,IAAI,CAAC,EAAE;YACZ,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACzC,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,IAAI,CAAC,EAAE;gBACrC,IAAI,CAAC,YAAY,EAAE,CAAC;aACvB;SACJ;IACL,CAAC;IAES,YAAY;QAClB,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE;YAC1B,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;YAC3C,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;YAEvB,IAAI,CAAC,mBAAmB,EAAE,EAAE;gBACxB,IAAI,OAAO,oBAAoB,KAAK,UAAU,EAAE;oBAC5C,OAAO,oBAAoB,CAAC,eAAe,CAAC,CAAC;iBAChD;aACJ;iBAAM;gBACH,MAAM,EAAE,oBAAoB,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,IAAI,MAAM,CAAC;gBAChE,IAAI,OAAO,oBAAoB,KAAK,UAAU,EAAE;oBAC5C,OAAO,oBAAoB,CAAC,eAAe,CAAC,CAAC;iBAChD;aACJ;YACD,OAAO,YAAY,CAAC,eAAe,CAAC,CAAC;SACxC;IACL,CAAC;IAED,gBAAgB;IACT,WAAW;QACd,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,YAAY,GAAG,IAAI,CAAC;YACxB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,mBAAmB,CAAC,EAAE;gBAChF,YAAY,GAAG,KAAK,CAAC;aACxB;YAED,IAAI,YAAY,EAAE;gBACd,kBAAkB;gBAClB,IAAI,CAAC,UAAU,EAAE,CAAC;gBAElB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBACjE,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;oBAEtD,cAAc,EAAE,CAAC;iBACpB;gBAED,UAAU;gBACV,IAAI,CAAC,QAAQ,EAAE,CAAC;aACnB;SACJ;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE;YAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;SAC7F;IACL,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,IAAI,CAAC,mBAAmB,EAAE,EAAE;YACxB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,WAAW,EAAE;YACjH,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,WAAW,CAAC;SAC1D;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,SAAS,GAAG,KAAK;QACnC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;SAC1C;QAED,OAAO,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC;IAChI,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,SAAS,GAAG,KAAK;QACpC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;SAC3C;QAED,OAAO,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC;IAClI,CAAC;IAED;;;OAGG;IACO,cAAc,CAAC,oBAAyB,EAAE,SAAe;QAC/D,OAAO,UAAU,CAAC,aAAa,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC;IACrE,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,cAA0B;QAC3C,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE;YACxD,OAAO;SACV;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE7C,sDAAsD;QACtD,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE;YAClE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;SAC7F;IACL,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,KAA4B,EAAE,UAAmB,EAAE,KAAc,EAAE,UAAmB,KAAK;QACpG,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC;QAC5E,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC,mIAAmI;QAE1L,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QAEtE,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,UAAU,IAAI,KAAK,EAAE;YACrB,IAAI,kBAAkB,GAAG,IAAI,CAAC;YAC9B,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,MAAM,CAAC;gBAChE,IACI,aAAa,KAAK,SAAS,CAAC,yBAAyB;oBACrD,aAAa,KAAK,SAAS,CAAC,wBAAwB;oBACpD,aAAa,KAAK,SAAS,CAAC,yBAAyB;oBACrD,aAAa,KAAK,SAAS,CAAC,0BAA0B,EACxD;oBACE,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC;oBAC5D,IAAI,WAAW,KAAK,SAAS,CAAC,4BAA4B,IAAI,WAAW,KAAK,SAAS,CAAC,0BAA0B,EAAE;wBAChH,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACpD,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACpD,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACpD,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACpD,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC,qBAAqB,CAAC,CAAC;wBAC7E,kBAAkB,GAAG,KAAK,CAAC;qBAC9B;yBAAM;wBACH,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACnD,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACnD,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACnD,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACnD,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC,oBAAoB,CAAC,CAAC;wBAC3E,kBAAkB,GAAG,KAAK,CAAC;qBAC9B;iBACJ;aACJ;YAED,IAAI,kBAAkB,EAAE;gBACpB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBACtF,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;aACrC;SACJ;QAED,IAAI,KAAK,EAAE;YACP,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;gBACpD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;aAC5B;iBAAM;gBACH,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;aAC5B;YACD,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;SACrC;QACD,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACzB,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC;SACvC;QACD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAID;;OAEG;IACI,SAAS,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc;QAChE,IAAI,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE;YACvI,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,MAAM,CAAC;YAEhC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;SAC1C;IACL,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,QAAuB,EAAE,aAAsB,EAAE,cAAuB;QACvF,MAAM,KAAK,GAAG,aAAa,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;QACrD,MAAM,MAAM,GAAG,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;QACxD,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;QAC1B,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAEhC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,MAAM,EAAE,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC5F,CAAC;IAED;;OAEG;IACI,UAAU,KAAU,CAAC;IAE5B;;OAEG;IACI,QAAQ;QACX,+CAA+C;QAC/C,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;QACD,IAAI,CAAC,QAAQ,EAAE,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,YAAY,GAAG,KAAK;QAC9B,IAAI,KAAa,CAAC;QAClB,IAAI,MAAc,CAAC;QAEnB,gEAAgE;QAChE,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,MAAM,gBAAgB,GAAG,mBAAmB,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YACtF,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,GAAG,gBAAgB,CAAC;YAClE,IAAI,CAAC,qBAAqB,GAAG,gBAAgB,CAAC;YAC9C,IAAI,CAAC,qBAAqB,IAAI,WAAW,CAAC;SAC7C;QAED,IAAI,mBAAmB,EAAE,IAAI,mBAAmB,EAAE,EAAE;YAChD,gEAAgE;YAChE,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,qBAAqB;oBAC5D,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,EAAE;oBAC/C,CAAC,CAAC;wBACI,+DAA+D;wBAC/D,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,qBAAqB;wBAC/D,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB;qBACpE,CAAC;gBACR,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,IAAI,YAAY,CAAC,KAAK,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI,GAAG,CAAC;gBACtG,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,IAAI,YAAY,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,IAAI,GAAG,CAAC;aAC7G;iBAAM;gBACH,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC;gBAC1B,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC;aAC/B;SACJ;aAAM;YACH,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;YAClE,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;SACvE;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,qBAAqB,EAAE,MAAM,GAAG,IAAI,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC;IACxG,CAAC;IAED;;;;;;OAMG;IACI,OAAO,CAAC,KAAa,EAAE,MAAc,EAAE,YAAY,GAAG,KAAK;QAC9D,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,KAAK,CAAC;SAChB;QAED,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;QAClB,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC;QAEpB,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,MAAM,EAAE;YACnG,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC;QACpC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;QAEtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG;IACI,eAAe,CAClB,SAA8B,EAC9B,YAAoB,CAAC,EACrB,aAAsB,EACtB,cAAuB,EACvB,uBAAiC,EACjC,QAAQ,GAAG,CAAC,EACZ,KAAK,GAAG,CAAC;QAET,MAAM,cAAc,GAAG,SAAqC,CAAC;QAE7D,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACrD;QACD,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAE9H,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YACpB,IAAI,SAAS,CAAC,SAAS,EAAE;gBACrB,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,SAAS,CAAC,OAAQ,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;aAC9I;iBAAM,IAAI,SAAS,CAAC,MAAM,EAAE;gBACzB,EAAE,CAAC,oBAAoB,CACnB,EAAE,CAAC,WAAW,EACd,EAAE,CAAC,iBAAiB,EACpB,EAAE,CAAC,2BAA2B,GAAG,SAAS,EAC1C,SAAS,CAAC,OAAQ,CAAC,gBAAgB,EAAE,kBAAkB,EACvD,QAAQ,CACX,CAAC;aACL;iBAAM,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE;gBAChD,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,OAAQ,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;gBAChJ,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC;aACzC;SACJ;QAED,MAAM,mBAAmB,GAAG,SAAS,CAAC,oBAAoB,CAAC;QAC3D,IAAI,mBAAmB,EAAE;YACrB,MAAM,UAAU,GAAG,SAAS,CAAC,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC;YACjH,IAAI,SAAS,CAAC,SAAS,EAAE;gBACrB,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,mBAAmB,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;aACrI;iBAAM,IAAI,SAAS,CAAC,MAAM,EAAE;gBACzB,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,2BAA2B,GAAG,SAAS,EAAE,mBAAmB,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;aACvK;iBAAM;gBACH,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,UAAU,EAAE,mBAAmB,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;aAC1I;SACJ;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,uBAAuB,EAAE;YAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;SACzE;aAAM;YACH,IAAI,CAAC,aAAa,EAAE;gBAChB,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC;gBAChC,IAAI,QAAQ,EAAE;oBACV,aAAa,GAAG,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;iBACzD;aACJ;YACD,IAAI,CAAC,cAAc,EAAE;gBACjB,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC;gBAClC,IAAI,QAAQ,EAAE;oBACV,cAAc,GAAG,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;iBAC3D;aACJ;YAED,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;SACvD;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAED;;;;;;;;;OASG;IACI,QAAQ,CAAC,OAAgB,EAAE,UAAkB,CAAC,EAAE,KAAe,EAAE,WAAW,GAAG,KAAK,EAAE,aAAuB,EAAE,OAAuB,EAAE,eAAuB,CAAC;QACnK,UAAU;QACV,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,EAAE;YACnD,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,OAAO,CAAC;SAC1C;QAED,YAAY;QACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,IAAI,aAAa,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;QAC9F,IAAI,IAAI,CAAC,kBAAkB,CAAC,QAAQ,KAAK,QAAQ,IAAI,KAAK,EAAE;YACxD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,QAAQ,CAAC;SAC/C;QAED,WAAW;QACX,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAEnC,aAAa;QACb,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QAC3D,IAAI,IAAI,CAAC,kBAAkB,CAAC,SAAS,KAAK,SAAS,IAAI,KAAK,EAAE;YAC1D,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC;SACjD;QAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,GAAG,OAAO,CAAC;IACzD,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,MAAe;QACjC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,MAAM,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,KAAa;QAC3B,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;IAClF,CAAC;IAED;;;OAGG;IACI,UAAU;QACb,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;QAChD,OAAO,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;IAC3D,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,KAAa;QAChC,IAAI,CAAC,kBAAkB,CAAC,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;IACvF,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC;QAC1D,OAAO,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC;IACrE,CAAC;IAED;;OAEG;IACI,uBAAuB,CAAC,WAAuC;QAClE,IAAI,IAAI,CAAC,mBAAmB,KAAK,WAAW,EAAE;YAC1C,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC;SAC1C;IACL,CAAC;IAED,gBAAgB;IACT,uCAAuC;QAC1C,OAAO,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,OAAwB;QAC3C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAC9D,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,OAA4B,EAAE,sBAAsB,GAAG,KAAK,EAAE,cAA2B;QAC9G,MAAM,cAAc,GAAG,OAAmC,CAAC;QAE3D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,kDAAkD;QAClD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,IAAI,cAAc,CAAC,gBAAgB,EAAE;YACjC,IAAI,OAAO,CAAC,OAAO,EAAE;gBACjB,0EAA0E;gBAC1E,IAAI,CAAC,qCAAqC,CAAC,OAAO,EAAE,sBAAsB,EAAE,cAAc,CAAC,CAAC;gBAC5F,OAAO;aACV;YACD,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,cAAc,CAAC,gBAAgB,CAAC,CAAC;YACzE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,cAAc,CAAC,YAAY,CAAC,CAAC;YACrE,EAAE,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;SACjI;QAED,IAAI,OAAO,CAAC,OAAO,EAAE,eAAe,IAAI,CAAC,sBAAsB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YAChF,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SACzC;QAED,IAAI,cAAc,EAAE;YAChB,IAAI,cAAc,CAAC,gBAAgB,EAAE;gBACjC,+BAA+B;gBAC/B,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;aAC7D;YACD,cAAc,EAAE,CAAC;SACpB;QAED,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,yBAAyB;QAC5B,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACrD;aAAM;YACH,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAED,OAAO;IAEP,gBAAgB;IACN,yBAAyB;QAC/B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IACrC,CAAC;IAED;;;;;;OAMG;IACI,kBAAkB,CAAC,IAAwB,EAAE,UAAoB,EAAE,MAAe;QACrF,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAChE,CAAC;IAEO,mBAAmB,CAAC,IAAwB,EAAE,KAAa;QAC/D,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;QAEpC,IAAI,CAAC,GAAG,EAAE;YACN,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACrD;QAED,MAAM,UAAU,GAAG,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAEjC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC1B,IAAI,IAAI,YAAY,KAAK,EAAE;gBACvB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC1E,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;aACzC;iBAAM;gBACH,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAe,IAAI,EAAE,KAAK,CAAC,CAAC;gBACrE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;aACzC;SACJ;aAAM;YACH,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;YACxE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;SAC9B;QAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC;QAC1B,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACI,yBAAyB,CAAC,IAAwB,EAAE,MAAe;QACtE,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACjE,CAAC;IAES,wBAAwB;QAC9B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACnC,CAAC;IAED;;;;;;OAMG;IACI,iBAAiB,CAAC,OAAqB,EAAE,SAAmB,EAAE,MAAe;QAChF,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;QACpC,MAAM,UAAU,GAAG,IAAI,eAAe,CAAC,GAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,GAAG,EAAE;YACN,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAEjC,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACnH,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC;QAC1B,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC;QACnD,OAAO,UAAU,CAAC;IACtB,CAAC;IAES,mBAAmB,CAAC,OAAqB;QAC/C,MAAM,eAAe,GAAI,OAA2C,CAAC,iBAAiB,CAAC;QACvF,IAAI,eAAe,KAAK,CAAC,EAAE;YACvB,OAAO,OAAsB,CAAC;SACjC;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YACxB,IAAI,OAAO,YAAY,WAAW,EAAE;gBAChC,OAAO,OAAO,CAAC;aAClB;iBAAM;gBACH,uDAAuD;gBACvD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBACjD,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,EAAE;wBACzB,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;qBACnC;iBACJ;gBAED,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;aACnC;SACJ;QAED,iFAAiF;QACjF,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,MAA4B;QAC/C,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACnC;QACD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACpD,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,eAAiC,EAAE,SAAiB,EAAE,KAAa;QACvF,MAAM,OAAO,GAAI,eAAwC,CAAC,OAAQ,CAAC;QAEnE,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAE1E,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;IAED,gEAAgE;IACtD,eAAe,CAAC,MAA4B;QAClD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACnC;QACD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IAC5D,CAAC;IAEO,WAAW,CAAC,MAA4B,EAAE,MAAc;QAC5D,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,MAAM,EAAE;YAC1E,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACvE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;SAC7C;IACL,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,IAAkB;QACvC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IAC3D,CAAC;IAEO,oBAAoB,CAAC,MAAkB,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,UAAmB,EAAE,MAAc,EAAE,MAAc;QAC1I,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,EAAE;YACV,OAAO;SACV;QAED,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,OAAO,GAAG,IAAI,CAAC;YACf,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;YACtB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;YACrB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YACpB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YACpB,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;YAChC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;YACxB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;YACxB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;SAC3B;aAAM;YACH,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;gBAC3B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;gBACxB,OAAO,GAAG,IAAI,CAAC;aAClB;YACD,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,EAAE;gBACvB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;gBACpB,OAAO,GAAG,IAAI,CAAC;aAClB;YACD,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,EAAE;gBACvB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;gBACpB,OAAO,GAAG,IAAI,CAAC;aAClB;YACD,IAAI,OAAO,CAAC,UAAU,KAAK,UAAU,EAAE;gBACnC,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;gBAChC,OAAO,GAAG,IAAI,CAAC;aAClB;YACD,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;gBAC3B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;gBACxB,OAAO,GAAG,IAAI,CAAC;aAClB;YACD,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;gBAC3B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;gBACxB,OAAO,GAAG,IAAI,CAAC;aAClB;SACJ;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACtC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC7B,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,YAAY,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;gBACzD,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;aACnE;iBAAM;gBACH,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;aAC9E;SACJ;IACL,CAAC;IAED;;OAEG;IACI,yBAAyB,CAAC,WAAiC;QAC9D,IAAI,WAAW,IAAI,IAAI,EAAE;YACrB,OAAO;SACV;QACD,IAAI,IAAI,CAAC,kBAAkB,KAAK,WAAW,EAAE;YACzC,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;YACtC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAClC,IAAI,CAAC,wBAAwB,GAAG,WAAW,CAAC,QAAQ,CAAC;SACxD;IACL,CAAC;IAEO,4BAA4B,CAChC,aAAwD,EACxD,MAAc,EACd,qBAAkE;QAElE,MAAM,UAAU,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAC;QAE/C,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACnC;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACpD,MAAM,KAAK,GAAG,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAEjD,IAAI,KAAK,IAAI,CAAC,EAAE;gBACZ,MAAM,EAAE,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;gBAC7B,IAAI,YAAY,GAA2B,IAAI,CAAC;gBAEhD,IAAI,qBAAqB,EAAE;oBACvB,YAAY,GAAG,qBAAqB,CAAC,EAAE,CAAC,CAAC;iBAC5C;gBAED,IAAI,CAAC,YAAY,EAAE;oBACf,YAAY,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;iBACpC;gBAED,IAAI,CAAC,YAAY,EAAE;oBACf,SAAS;iBACZ;gBAED,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;oBAC5B,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;iBACjD;gBAED,MAAM,MAAM,GAAG,YAAY,CAAC,SAAS,EAAE,CAAC;gBACxC,IAAI,MAAM,EAAE;oBACR,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,YAAY,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;oBAE/J,IAAI,YAAY,CAAC,cAAc,EAAE,EAAE;wBAC/B,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,KAAK,EAAE,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC;wBACvE,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;4BAC5B,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;4BAC3C,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;yBAC7C;qBACJ;iBACJ;aACJ;SACJ;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,uBAAuB,CAC1B,aAA8C,EAC9C,WAAiC,EACjC,MAAc,EACd,qBAAkE;QAElE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;QAEzC,IAAI,CAAC,GAAG,EAAE;YACN,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAE9B,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;QACtC,IAAI,CAAC,4BAA4B,CAAC,aAAa,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAEhF,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAElC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE/B,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACI,qBAAqB,CAAC,iBAAyC,EAAE,WAAiC;QACrG,IAAI,IAAI,CAAC,wBAAwB,KAAK,iBAAiB,EAAE;YACrD,IAAI,CAAC,wBAAwB,GAAG,iBAAiB,CAAC;YAElD,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;YAC5C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACjC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAE/B,IAAI,CAAC,wBAAwB,GAAG,WAAW,IAAI,IAAI,IAAI,WAAW,CAAC,QAAQ,CAAC;YAC5E,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;SACzC;IACL,CAAC;IAED;;;;;;;OAOG;IACI,mBAAmB,CAAC,YAAwB,EAAE,WAAuB,EAAE,iBAA2B,EAAE,gBAAwB,EAAE,MAAc;QAC/I,IAAI,IAAI,CAAC,oBAAoB,KAAK,YAAY,IAAI,IAAI,CAAC,6BAA6B,KAAK,MAAM,EAAE;YAC7F,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC;YACzC,IAAI,CAAC,6BAA6B,GAAG,MAAM,CAAC;YAE5C,MAAM,eAAe,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAC;YAEpD,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE3B,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,EAAE,KAAK,EAAE,EAAE;gBAClD,IAAI,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE;oBAClC,MAAM,KAAK,GAAG,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;oBAEjD,IAAI,KAAK,IAAI,CAAC,EAAE;wBACZ,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;wBACxC,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;wBAC9C,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,KAAK,EAAE,iBAAiB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;qBAC7H;oBAED,MAAM,IAAI,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;iBAC1C;aACJ;SACJ;QAED,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;IAChD,CAAC;IAEO,wBAAwB;QAC5B,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;YAChC,OAAO;SACV;QAED,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;;;;;OAMG;IACI,WAAW,CACd,aAAwD,EACxD,WAAiC,EACjC,MAAc,EACd,qBAAkE;QAElE,IAAI,IAAI,CAAC,oBAAoB,KAAK,aAAa,IAAI,IAAI,CAAC,6BAA6B,KAAK,MAAM,EAAE;YAC9F,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC;YAC1C,IAAI,CAAC,6BAA6B,GAAG,MAAM,CAAC;YAE5C,IAAI,CAAC,4BAA4B,CAAC,aAAa,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;SACnF;QAED,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,wBAAwB;QAC3B,IAAI,WAAW,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;YACrE,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;YACxD,IAAI,WAAW,IAAI,eAAe,IAAI,eAAe,CAAC,UAAU,EAAE;gBAC9D,WAAW,GAAG,eAAe,CAAC;gBAC9B,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;aACzC;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;YACzD,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;SACnD;QACD,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACI,wBAAwB,CAAC,GAA2B;QACvD,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,MAAkB;QACpC,MAAM,CAAC,UAAU,EAAE,CAAC;QAEpB,IAAI,MAAM,CAAC,UAAU,KAAK,CAAC,EAAE;YACzB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAES,aAAa,CAAC,MAAkB;QACtC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;IACrD,CAAC;IAED;;;;;OAKG;IACI,4BAA4B,CAAC,eAA2B,EAAE,IAAkB,EAAE,eAAqD;QACtI,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;QACtC,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;SAC1D;QAED,IAAU,eAAe,CAAC,CAAC,CAAE,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/C,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,eAAsB,EAAE,IAAI,CAAC,CAAC;SAC3E;aAAM;YACH,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE;gBACpC,MAAM,cAAc,GAAW,eAAe,CAAC,KAAK,CAAC,CAAC;gBAEtD,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,cAAc,CAAC,EAAE;oBAClD,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;oBACjD,IAAI,CAAC,0BAA0B,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;iBAC1D;gBAED,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,cAAc,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC;gBACrG,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;gBAChD,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACpD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aACtD;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,eAA2B,EAAE,cAAyC,EAAE,aAAa,GAAG,IAAI;QACnH,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;QAEtC,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,aAAa,EAAE;YACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5C,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM,IAAI,EAAE,CAAC,aAAa,GAAG,CAAC,CAAC;aAClC;SACJ;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,EAAE,CAAC,KAAK,KAAK,SAAS,EAAE;gBACxB,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,cAAe,CAAC,0BAA0B,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;aAChF;YAED,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,EAAE;gBACd,SAAS;aACZ;YAED,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;gBAC5C,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC3C,IAAI,CAAC,0BAA0B,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;aACpD;YAED,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,aAAa,EAAE,EAAE,CAAC,aAAa,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,UAAU,IAAI,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;YACtJ,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;YAClF,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;YAC9C,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACtD;IACL,CAAC;IAED;;;OAGG;IACI,8BAA8B,CAAC,IAAY;QAC9C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QAC/E,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACI,wBAAwB,CAAC,iBAAyB;QACrD,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,KAAa,CAAC;QAClB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/E,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAE9C,WAAW,GAAG,IAAI,CAAC;YACnB,KAAK,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;SACrE;QAED,IAAI,WAAW,EAAE;YACb,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;YACnD,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;SACnD;IACL,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,iBAAyB;QACpD,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QACrD,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,GAAG,KAAK,CAAC;QAC3D,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IAClE,CAAC;IAED;;;;;;OAMG;IACI,IAAI,CAAC,YAAqB,EAAE,UAAkB,EAAE,UAAkB,EAAE,cAAuB;QAC9F,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC,CAAC,SAAS,CAAC,0BAA0B,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IAC7J,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,aAAqB,EAAE,aAAqB,EAAE,cAAuB;QACxF,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,sBAAsB,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IACxG,CAAC;IAED;;;;;;OAMG;IACI,aAAa,CAAC,YAAqB,EAAE,aAAqB,EAAE,aAAqB,EAAE,cAAuB;QAC7G,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC,CAAC,SAAS,CAAC,0BAA0B,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IACjK,CAAC;IAED;;;;;;OAMG;IACI,gBAAgB,CAAC,QAAgB,EAAE,UAAkB,EAAE,UAAkB,EAAE,cAAuB;QACrG,eAAe;QACf,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,SAAS;QAET,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;QACpG,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,IAAI,cAAc,EAAE;YAChB,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,GAAG,IAAI,EAAE,cAAc,CAAC,CAAC;SACxG;aAAM;YACH,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,GAAG,IAAI,CAAC,CAAC;SAC/E;IACL,CAAC;IAED;;;;;;OAMG;IACI,cAAc,CAAC,QAAgB,EAAE,aAAqB,EAAE,aAAqB,EAAE,cAAuB;QACzG,eAAe;QACf,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,cAAc,EAAE;YAChB,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;SACxF;aAAM;YACH,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;SAC/D;IACL,CAAC;IAEO,SAAS,CAAC,QAAgB;QAC9B,QAAQ,QAAQ,EAAE;YACd,iBAAiB;YACjB,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;YAC9B,KAAK,SAAS,CAAC,sBAAsB;gBACjC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YAC3B,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAC1B,aAAa;YACb,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YAC3B,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAC1B,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;YAC9B,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;YAC/B,KAAK,SAAS,CAAC,8BAA8B;gBACzC,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;YACnC,KAAK,SAAS,CAAC,4BAA4B;gBACvC,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC;YACjC;gBACI,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;SACjC;IACL,CAAC;IAED,gBAAgB;IACN,eAAe;QACrB,kCAAkC;IACtC,CAAC;IAED,UAAU;IAEV;;OAEG;IACI,cAAc,CAAC,MAAc;QAChC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC7C;QACD,MAAM,eAAe,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAC;QACpD,IAAI,eAAe,EAAE;YACjB,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;SAChD;IACL,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,eAAiC;QAC3D,MAAM,oBAAoB,GAAG,eAAuC,CAAC;QACrE,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,OAAO,EAAE;YACtD,oBAAoB,CAAC,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;YAE7D,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;SACxD;IACL,CAAC;IAED,gBAAgB;IACT,iBAAiB,CAAC,OAAmC;QACxD,IAAI,OAAO,EAAE;YACT,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,OAAO,CAAC,oBAAoB,CAAC,GAAG,EAAE,CAAC;aACtC;iBAAM;gBACH,OAAO,OAAO,CAAC,oBAAoB,CAAC,CAAC;aACxC;YACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,OAAO,CAAC,yBAAyB,CAAC,GAAG,EAAE,CAAC;aAC3C;iBAAM;gBACH,OAAO,OAAO,CAAC,yBAAyB,CAAC,CAAC;aAC7C;YACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,OAAO,CAAC,4BAA4B,CAAC,GAAG,EAAE,CAAC;aAC9C;iBAAM;gBACH,OAAO,OAAO,CAAC,4BAA4B,CAAC,CAAC;aAChD;YACD,OAAO;SACV;aAAM;YACH,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,CAAC,IAAI,4BAA4B,CAAC;aACrC;YACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,EAAE;oBACH,CAAC,IAAI,IAAI,CAAC;iBACb;gBACD,CAAC,IAAI,iCAAiC,CAAC;aAC1C;YACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,EAAE;oBACH,CAAC,IAAI,IAAI,CAAC;iBACb;gBACD,CAAC,IAAI,oCAAoC,CAAC;aAC7C;YACD,OAAO,CAAC,CAAC;SACZ;IACL,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,YAAY,CACf,QAAa,EACb,wBAA2D,EAC3D,qBAA4C,EAC5C,QAAmB,EACnB,OAAgB,EAChB,SAA4B,EAC5B,UAA+C,EAC/C,OAA4D,EAC5D,eAAqB,EACrB,cAAc,GAAG,cAAc,CAAC,IAAI;QAEpC,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC;QACtH,MAAM,QAAQ,GAAG,QAAQ,CAAC,eAAe,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC;QAChI,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAG,CAAC;QAEhD,IAAI,WAAW,GAAG,OAAO,IAA6B,wBAAyB,CAAC,OAAO,IAAI,EAAE,CAAC;QAE9F,IAAI,aAAa,EAAE;YACf,WAAW,IAAI,aAAa,CAAC;SAChC;QAED,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,GAAG,QAAQ,GAAG,GAAG,GAAG,WAAW,CAAC;QACzD,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC7B,MAAM,cAAc,GAAW,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC3D,IAAI,UAAU,IAAI,cAAc,CAAC,OAAO,EAAE,EAAE;gBACxC,UAAU,CAAC,cAAc,CAAC,CAAC;aAC9B;YAED,OAAO,cAAc,CAAC;SACzB;QACD,MAAM,MAAM,GAAG,IAAI,MAAM,CACrB,QAAQ,EACR,wBAAwB,EACxB,qBAAqB,EACrB,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,eAAe,EACf,IAAI,EACJ,cAAc,CACjB,CAAC;QACF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;QAErC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,gEAAgE;IACtD,MAAM,CAAC,kBAAkB,CAAC,MAAc,EAAE,OAAyB,EAAE,gBAAwB,EAAE;QACrG,OAAO,aAAa,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;IACpE,CAAC;IAEO,cAAc,CAAC,MAAc,EAAE,IAAY,EAAE,OAAyB,EAAE,aAAqB;QACjG,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,kBAAkB,CAAC,MAAM,EAAE,OAAO,EAAE,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC;IACvG,CAAC;IAEO,iBAAiB,CAAC,MAAc,EAAE,IAAY;QAClD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC;QAE1F,IAAI,CAAC,MAAM,EAAE;YACT,IAAI,KAAK,GAAW,EAAE,CAAC,QAAQ,CAAC;YAChC,IAAI,SAAS,GAAW,EAAE,CAAC,QAAQ,CAAC;YACpC,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE;gBAChD,KAAK,GAAG,SAAS,CAAC;aACrB;YAED,MAAM,IAAI,KAAK,CACX,4CAA4C,IAAI,4BAA4B,KAAK,sBAAsB,EAAE,CAAC,aAAa,EAAE,qBAAqB,IAAI,CAAC,eAAe,EAAE,CACvK,CAAC;SACL;QAED,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAChC,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAEzB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,MAAmB;QACvC,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;;;;OAQG;IACI,sBAAsB,CACzB,eAAiC,EACjC,UAAkB,EAClB,YAAoB,EACpB,OAA+B,EAC/B,4BAAgD,IAAI;QAEpD,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC;QAE9B,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAClE,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAExE,OAAO,IAAI,CAAC,oBAAoB,CAAC,eAAuC,EAAE,YAAY,EAAE,cAAc,EAAE,OAAO,EAAE,yBAAyB,CAAC,CAAC;IAChJ,CAAC;IAED;;;;;;;;;OASG;IACI,mBAAmB,CACtB,eAAiC,EACjC,UAAkB,EAClB,YAAoB,EACpB,OAAyB,EACzB,OAA+B,EAC/B,4BAAgD,IAAI;QAEpD,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC;QAE9B,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,oCAAoC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzF,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;QACvF,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;QAE7F,OAAO,IAAI,CAAC,oBAAoB,CAAC,eAAuC,EAAE,YAAY,EAAE,cAAc,EAAE,OAAO,EAAE,yBAAyB,CAAC,CAAC;IAChJ,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,IAAY;QAChC,yCAAyC;QACzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,uBAA0D;QACnF,MAAM,eAAe,GAAG,IAAI,oBAAoB,EAAE,CAAC;QACnD,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC;QAE9B,IAAI,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE;YAClC,eAAe,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAC7C;QAED,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,qBAAqB;QACxB,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,OAAO,SAAS,CAAC;IACrB,CAAC;IAES,oBAAoB,CAC1B,eAAqC,EACrC,YAAyB,EACzB,cAA2B,EAC3B,OAA8B,EAC9B,4BAAgD,IAAI;QAEpD,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC9C,eAAe,CAAC,OAAO,GAAG,aAAa,CAAC;QAExC,IAAI,CAAC,aAAa,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;SAC/C;QAED,OAAO,CAAC,YAAY,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;QAClD,OAAO,CAAC,YAAY,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;QAEpD,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAEnC,eAAe,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,eAAe,CAAC,YAAY,GAAG,YAAY,CAAC;QAC5C,eAAe,CAAC,cAAc,GAAG,cAAc,CAAC;QAEhD,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE;YACrC,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,CAAC;SAClD;QAED,OAAO,aAAa,CAAC;IACzB,CAAC;IAES,wBAAwB,CAAC,eAAqC;QACpE,MAAM,OAAO,GAAG,eAAe,CAAC,OAAQ,CAAC;QACzC,MAAM,YAAY,GAAG,eAAe,CAAC,YAAa,CAAC;QACnD,MAAM,cAAc,GAAG,eAAe,CAAC,cAAe,CAAC;QACvD,MAAM,OAAO,GAAG,eAAe,CAAC,OAAQ,CAAC;QAEzC,MAAM,MAAM,GAAG,OAAO,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QACzE,IAAI,CAAC,MAAM,EAAE;YACT,gBAAgB;YAChB,SAAS;YACT,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;gBACrE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;gBACpD,IAAI,GAAG,EAAE;oBACL,eAAe,CAAC,sBAAsB,GAAG,GAAG,CAAC;oBAC7C,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC;iBAC3C;aACJ;YAED,WAAW;YACX,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;gBACvE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;gBACtD,IAAI,GAAG,EAAE;oBACL,eAAe,CAAC,wBAAwB,GAAG,GAAG,CAAC;oBAC/C,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,GAAG,CAAC,CAAC;iBAC7C;aACJ;YAED,MAAM,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACjD,IAAI,KAAK,EAAE;gBACP,eAAe,CAAC,gBAAgB,GAAG,KAAK,CAAC;gBACzC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;aAC1B;SACJ;QAED,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACjC,MAAM,SAAS,GAAG,OAAO,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;YAEhF,IAAI,CAAC,SAAS,EAAE;gBACZ,MAAM,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBACjD,IAAI,KAAK,EAAE;oBACP,eAAe,CAAC,sBAAsB,GAAG,KAAK,CAAC;oBAC/C,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;iBAC1B;aACJ;SACJ;QAED,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QACnC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QAErC,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC;QACzC,eAAe,CAAC,cAAc,GAAG,SAAS,CAAC;QAE3C,IAAI,eAAe,CAAC,UAAU,EAAE;YAC5B,eAAe,CAAC,UAAU,EAAE,CAAC;YAC7B,eAAe,CAAC,UAAU,GAAG,SAAS,CAAC;SAC1C;IACL,CAAC;IAED;;OAEG;IACI,uBAAuB,CAC1B,eAAiC,EACjC,gBAAwB,EACxB,kBAA0B,EAC1B,WAAoB,EACpB,mBAA2B,EAC3B,qBAA6B,EAC7B,aAAkB,EAClB,OAAyB,EACzB,yBAA6C,EAC7C,GAAW;QAEX,MAAM,mBAAmB,GAAG,eAAuC,CAAC;QAEpE,IAAI,WAAW,EAAE;YACb,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,SAAS,EAAE,yBAAyB,CAAC,CAAC;SAC9J;aAAM;YACH,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,OAAO,EAAE,SAAS,EAAE,yBAAyB,CAAC,CAAC;SACpK;QACD,mBAAmB,CAAC,OAAO,CAAC,wBAAwB,GAAG,aAAa,CAAC;IACzE,CAAC;IAED;;OAEG;IACI,yBAAyB,CAAC,eAAiC;QAC9D,MAAM,oBAAoB,GAAG,eAAuC,CAAC;QACrE,IAAI,IAAI,CAAC,WAAW,IAAI,oBAAoB,CAAC,WAAW,EAAE;YACtD,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,OAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAsB,CAAC,qBAAqB,CAAC,EAAE;YACtH,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,oCAAoC,CAAC,eAAiC,EAAE,MAAkB;QAC7F,MAAM,oBAAoB,GAAG,eAAuC,CAAC;QAErE,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,EAAE;YAC1C,MAAM,EAAE,CAAC;YACT,OAAO;SACV;QAED,MAAM,UAAU,GAAG,oBAAoB,CAAC,UAAU,CAAC;QAEnD,IAAI,UAAU,EAAE;YACZ,oBAAoB,CAAC,UAAU,GAAG,GAAG,EAAE;gBACnC,UAAW,EAAE,CAAC;gBACd,MAAM,EAAE,CAAC;YACb,CAAC,CAAC;SACL;aAAM;YACH,oBAAoB,CAAC,UAAU,GAAG,MAAM,CAAC;SAC5C;IACL,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,eAAiC,EAAE,aAAuB;QACzE,MAAM,OAAO,GAAG,IAAI,KAAK,EAAkC,CAAC;QAC5D,MAAM,oBAAoB,GAAG,eAAuC,CAAC;QAErE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACvD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,OAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SAClG;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,eAAiC,EAAE,eAAyB;QAC7E,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,oBAAoB,GAAG,eAAuC,CAAC;QAErE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACzD,IAAI;gBACA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,OAAQ,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACnG;YAAC,OAAO,CAAC,EAAE;gBACR,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aACpB;SACJ;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,MAAsC;QACtD,MAAM,GAAG,MAAM,KAAK,IAAI,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,mEAAmE;QAEvJ,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,cAAc,EAAE;YAC3C,OAAO;SACV;QAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,GAAG,SAAS,CAAC;QAEvD,MAAM,GAAG,MAAgB,CAAC;QAE1B,cAAc;QACd,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAE1B,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;QAE7B,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SACzB;QACD,IAAI,MAAM,CAAC,iBAAiB,EAAE;YAC1B,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SACpD;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,OAAuC,EAAE,KAAa;QAChE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEnC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,OAAO,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS;QACxE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACI,OAAO,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACnF,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG;IACI,OAAO,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC9F,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,OAAuC,EAAE,KAAiB;QACzE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,OAAuC,EAAE,KAAiB;QAC1E,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,OAAuC,EAAE,KAAiB;QAC1E,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,OAAuC,EAAE,KAAiB;QAC1E,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,OAAuC,EAAE,KAAa;QACjE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,QAAQ,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS;QACzE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEnC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACI,QAAQ,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACpF,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG;IACI,QAAQ,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC/F,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,OAAuC,EAAE,KAAkB;QAC3E,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,OAAuC,EAAE,KAAkB;QAC5E,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,OAAuC,EAAE,KAAkB;QAC5E,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,OAAuC,EAAE,KAAkB;QAC5E,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,OAAuC,EAAE,KAA8B;QACnF,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YAClB,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,OAAuC,EAAE,KAA8B;QACpF,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAO,KAAK,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,OAAuC,EAAE,KAA8B;QACpF,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAO,KAAK,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,OAAuC,EAAE,KAA8B;QACpF,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAO,KAAK,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,OAAuC,EAAE,QAAmC;QAC3F,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,OAAuC,EAAE,MAAoB;QAC7E,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,OAAuC,EAAE,MAAoB;QAC7E,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,OAAuC,EAAE,KAAa;QAClE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEnC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS;QAC1E,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACI,SAAS,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACrF,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG;IACI,SAAS,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAChG,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,SAAS;IAET;;OAEG;IACI,WAAW;QACd,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEjC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAChC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;YAChC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;SACtD;IACL,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,MAAe;QAChC,IAAI,MAAM,KAAK,IAAI,CAAC,WAAW,EAAE;YAC7B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;SAC7B;IACL,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAED,WAAW;IAEX;;;OAGG;IACI,0BAA0B;QAC7B,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,UAAoB;QAClC,IAAI,IAAI,CAAC,6BAA6B,IAAI,CAAC,UAAU,EAAE;YACnD,OAAO;SACV;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;QAE3B,8CAA8C;QAC9C,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEhC,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;YAEnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YAEpD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC;YACtC,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,aAAa,CAAC;YAE9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAE/B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAE/B,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACjF,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;YAEjE,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;YACtC,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC9B;QAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;QAC1C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,YAAoB,EAAE,eAAwB;QACxE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,IAAI,SAAS,GAAW,EAAE,CAAC,OAAO,CAAC;QACnC,IAAI,SAAS,GAAW,EAAE,CAAC,OAAO,CAAC;QAEnC,QAAQ,YAAY,EAAE;YAClB,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,IAAI,eAAe,EAAE;oBACjB,SAAS,GAAG,EAAE,CAAC,qBAAqB,CAAC;iBACxC;qBAAM;oBACH,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;iBACzB;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,+BAA+B;gBAC1C,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,IAAI,eAAe,EAAE;oBACjB,SAAS,GAAG,EAAE,CAAC,oBAAoB,CAAC;iBACvC;qBAAM;oBACH,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;iBACzB;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,iCAAiC;gBAC5C,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,IAAI,eAAe,EAAE;oBACjB,SAAS,GAAG,EAAE,CAAC,qBAAqB,CAAC;iBACxC;qBAAM;oBACH,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;iBAC1B;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,IAAI,eAAe,EAAE;oBACjB,SAAS,GAAG,EAAE,CAAC,sBAAsB,CAAC;iBACzC;qBAAM;oBACH,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;iBAC1B;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,iCAAiC;gBAC5C,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,IAAI,eAAe,EAAE;oBACjB,SAAS,GAAG,EAAE,CAAC,qBAAqB,CAAC;iBACxC;qBAAM;oBACH,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;iBACzB;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,IAAI,eAAe,EAAE;oBACjB,SAAS,GAAG,EAAE,CAAC,oBAAoB,CAAC;iBACvC;qBAAM;oBACH,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;iBACzB;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,sBAAsB;gBACjC,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,MAAM;YACV,KAAK,SAAS,CAAC,uBAAuB;gBAClC,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,MAAM;YACV,KAAK,SAAS,CAAC,iCAAiC;gBAC5C,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,IAAI,eAAe,EAAE;oBACjB,SAAS,GAAG,EAAE,CAAC,sBAAsB,CAAC;iBACzC;qBAAM;oBACH,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;iBAC1B;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,IAAI,eAAe,EAAE;oBACjB,SAAS,GAAG,EAAE,CAAC,qBAAqB,CAAC;iBACxC;qBAAM;oBACH,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;iBAC1B;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,qBAAqB;gBAChC,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,MAAM;YACV,KAAK,SAAS,CAAC,sBAAsB;gBACjC,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,MAAM;SACb;QAED,OAAO;YACH,GAAG,EAAE,SAAS;YACd,GAAG,EAAE,SAAS;SACjB,CAAC;IACN,CAAC;IAED,gBAAgB;IACN,cAAc;QACpB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;QAEzC,IAAI,CAAC,OAAO,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;SAC/C;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,gBAAgB;IACT,sBAAsB;QACzB,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACrE,CAAC;IAED;;;;;;;;OAQG;IACI,sBAAsB,CACzB,IAAiB,EACjB,OAAiD,EACjD,uBAAuB,GAAG,IAAI,EAC9B,MAAM,GAAG,qBAAqB,CAAC,OAAO;QAEtC,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,IAAI,GAAG,SAAS,CAAC,wBAAwB,CAAC;QAC9C,IAAI,YAAY,GAAG,SAAS,CAAC,8BAA8B,CAAC;QAC5D,IAAI,MAAM,GAAG,SAAS,CAAC,kBAAkB,CAAC;QAC1C,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,KAAyB,CAAC;QAC9B,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YACtD,eAAe,GAAG,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;YAC5C,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;YACtF,YAAY,GAAG,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;YACpH,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;YACtF,aAAa,GAAG,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;YACpF,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC;YAC/B,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;SACzB;aAAM;YACH,eAAe,GAAG,CAAC,CAAC,OAAO,CAAC;SAC/B;QAED,aAAa,KAAb,aAAa,GAAK,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAC;QAE5F,IAAI,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE;YACjF,yEAAyE;YACzE,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;SACzD;aAAM,IAAI,IAAI,KAAK,SAAS,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE;YACjG,2EAA2E;YAC3E,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;SACzD;QACD,IAAI,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;YAClE,IAAI,GAAG,SAAS,CAAC,wBAAwB,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;SAC7F;QAED,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClD,MAAM,KAAK,GAAwD,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;QAC/F,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAY,IAAI,CAAC;QACjG,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAI,CAAC,CAAC;QACtF,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;QAC3E,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC;QAClE,MAAM,WAAW,GAAG,IAAI,CAAC,iCAAiC,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QACxF,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAEpD,OAAO;QACP,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAE3C,IAAI,MAAM,KAAK,CAAC,EAAE;YACd,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YACzB,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;SACtG;aAAM;YACH,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;SAC9F;QAED,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QAC9D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QAE9D,UAAU;QACV,IAAI,eAAe,EAAE;YACjB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;SACnC;QAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAExC,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC;QACvC,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC;QACvB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;QAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QAEtB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1C,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,aAAsB,EAAE,QAAiB;QAC9D,uHAAuH;QACvH,OAAO,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,CAAC;IAClH,CAAC;IAES,kBAAkB,CACxB,GAAqB,EACrB,QAAiB,EACjB,OAAgB,EAChB,KAA2B,EAC3B,eAAuB,SAAS,CAAC,8BAA8B,EAC/D,SAAuD,IAAI,EAC3D,UAA+D,IAAI,EACnE,cAiBS,EACT,6BAOY,EACZ,SAAmG,IAAI,EACvG,WAAsC,IAAI,EAC1C,SAA2B,IAAI,EAC/B,kBAAoC,IAAI,EACxC,QAAiB,EACjB,aAAmB,EACnB,aAAuB;QAEvB,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAChB,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,CAAC;QAC9C,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,CAAC;QAC9C,MAAM,QAAQ,GAAG,QAAQ,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;QAE5D,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC,GAAG,CAAC,CAAC;QAE3F,IAAI,OAAO,KAAK,QAAQ,EAAE;YACtB,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gDAAgD;SACzF;QAED,MAAM,WAAW,GAAG,GAAG,CAAC;QACxB,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE;YAChE,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;SACxC;QAED,IAAI,WAAW,KAAK,GAAG,EAAE;YACrB,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;SACtC;QAED,4CAA4C;QAC5C,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,SAAS,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7G,IAAI,MAAM,GAAqC,IAAI,CAAC;QAEpD,sBAAsB;QACtB,MAAM,gBAAgB,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEhD,IAAI,gBAAgB,GAAG,CAAC,CAAC,EAAE;YACvB,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SACvC;QAED,KAAK,MAAM,eAAe,IAAI,UAAU,CAAC,eAAe,EAAE;YACtD,IAAI,eAAe,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE;gBAC9C,MAAM,GAAG,eAAe,CAAC;gBACzB,MAAM;aACT;SACJ;QAED,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;SACjC;QACD,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;QAClB,OAAO,CAAC,eAAe,GAAG,CAAC,QAAQ,CAAC;QACpC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAE3E,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,mEAAmE;YACnE,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC;SAC5B;QAED,IAAI,cAAc,GAAwC,IAAI,CAAC;QAC/D,IAAI,MAAM,IAAI,CAAC,QAAQ,EAAE;YACrB,cAAc,GAAG,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SAC3D;QAED,IAAI,CAAC,QAAQ,EAAE;YACX,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC7C;QAED,MAAM,eAAe,GAAG,CAAC,OAAgB,EAAE,SAAe,EAAE,EAAE;YAC1D,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;aACpC;YAED,IAAI,GAAG,KAAK,WAAW,EAAE;gBACrB,IAAI,cAAc,EAAE;oBAChB,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;iBACrD;gBAED,IAAI,WAAW,CAAC,kBAAkB,IAAI,GAAG,KAAK,WAAW,CAAC,eAAe,EAAE;oBACvE,IAAI,CAAC,kBAAkB,CACnB,WAAW,CAAC,eAAe,EAC3B,QAAQ,EACR,OAAO,CAAC,OAAO,EACf,KAAK,EACL,YAAY,EACZ,IAAI,EACJ,OAAO,EACP,cAAc,EACd,6BAA6B,EAC7B,MAAM,EACN,OAAO,CACV,CAAC;iBACL;gBAED,OAAO,GAAG,CAAC,OAAO,IAAI,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAChH,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;gBAClE,IAAI,OAAO,EAAE;oBACT,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;iBAC/B;aACJ;iBAAM;gBACH,qEAAqE;gBACrE,MAAM,CAAC,IAAI,CAAC,kBAAkB,GAAG,qBAAqB,WAAW,EAAE,CAAC,CAAC;gBACrE,IAAI,CAAC,kBAAkB,CACnB,WAAW,EACX,QAAQ,EACR,OAAO,CAAC,OAAO,EACf,KAAK,EACL,YAAY,EACZ,MAAM,EACN,OAAO,EACP,cAAc,EACd,6BAA6B,EAC7B,MAAM,EACN,OAAO,EACP,MAAM,EACN,eAAe,EACf,QAAQ,EACR,aAAa,EACb,aAAa,CAChB,CAAC;aACL;QACL,CAAC,CAAC;QAEF,mCAAmC;QACnC,IAAI,MAAM,EAAE;YACR,MAAM,QAAQ,GAAG,CAAC,IAAqB,EAAE,EAAE;gBACvC,MAAO,CAAC,QAAQ,CACZ,IAAI,EACJ,OAAO,EACP,CAAC,KAAa,EAAE,MAAc,EAAE,UAAmB,EAAE,YAAqB,EAAE,IAAgB,EAAE,UAAU,EAAE,EAAE;oBACxG,IAAI,UAAU,EAAE;wBACZ,eAAe,CAAC,mCAAmC,CAAC,CAAC;qBACxD;yBAAM;wBACH,cAAc,CACV,OAAO,EACP,SAAS,EACT,KAAK,EACL,EAAE,KAAK,EAAE,MAAM,EAAE,EACjB,OAAO,CAAC,OAAO,EACf,CAAC,UAAU,EACX,YAAY,EACZ,GAAG,EAAE;4BACD,IAAI,EAAE,CAAC;4BACP,OAAO,KAAK,CAAC;wBACjB,CAAC,EACD,YAAY,CACf,CAAC;qBACL;gBACL,CAAC,EACD,aAAa,CAChB,CAAC;YACN,CAAC,CAAC;YAEF,IAAI,CAAC,MAAM,EAAE;gBACT,IAAI,CAAC,SAAS,CACV,GAAG,EACH,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,IAAmB,CAAC,CAAC,EACvD,SAAS,EACT,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,EACzC,IAAI,EACJ,CAAC,OAAqB,EAAE,SAAe,EAAE,EAAE;oBACvC,eAAe,CAAC,iBAAiB,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC;gBAC1F,CAAC,CACJ,CAAC;aACL;iBAAM;gBACH,IAAI,MAAM,YAAY,WAAW,EAAE;oBAC/B,QAAQ,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;iBACpC;qBAAM,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;oBACnC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBACpB;qBAAM;oBACH,IAAI,OAAO,EAAE;wBACT,OAAO,CAAC,kEAAkE,EAAE,IAAI,CAAC,CAAC;qBACrF;iBACJ;aACJ;SACJ;aAAM;YACH,MAAM,MAAM,GAAG,CAAC,GAAmC,EAAE,EAAE;gBACnD,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;oBAC3C,+DAA+D;oBAC/D,kCAAkC;oBAClC,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC;iBACzB;gBAED,cAAc,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,6BAA6B,EAAE,YAAY,CAAC,CAAC;YAClI,CAAC,CAAC;YACF,uFAAuF;YACvF,yFAAyF;YAEzF,IAAI,CAAC,QAAQ,IAAI,QAAQ,EAAE;gBACvB,IAAI,MAAM,IAAI,CAAC,OAA0B,MAAO,CAAC,QAAQ,KAAK,QAAQ,IAAkB,MAAO,CAAC,KAAK,CAAC,EAAE;oBACpG,MAAM,CAAmB,MAAM,CAAC,CAAC;iBACpC;qBAAM;oBACH,UAAU,CAAC,mBAAmB,CAC1B,GAAG,EACH,MAAM,EACN,eAAe,EACf,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,EACpC,QAAQ,EACR,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,gBAAgB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CACrG,CAAC;iBACL;aACJ;iBAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,YAAY,WAAW,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,MAAM,YAAY,IAAI,EAAE;gBAC5H,UAAU,CAAC,mBAAmB,CAC1B,MAAM,EACN,MAAM,EACN,eAAe,EACf,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,EACpC,QAAQ,EACR,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,gBAAgB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CACrG,CAAC;aACL;iBAAM,IAAI,MAAM,EAAE;gBACf,MAAM,CAAC,MAAM,CAAC,CAAC;aAClB;SACJ;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACI,aAAa,CAChB,GAAqB,EACrB,QAAiB,EACjB,OAAgB,EAChB,KAA2B,EAC3B,eAAuB,SAAS,CAAC,8BAA8B,EAC/D,SAAuD,IAAI,EAC3D,UAA+D,IAAI,EACnE,SAAmG,IAAI,EACvG,WAAsC,IAAI,EAC1C,SAA2B,IAAI,EAC/B,kBAAoC,IAAI,EACxC,QAAiB,EACjB,aAAmB,EACnB,aAAsB,EACtB,aAAuB;QAEvB,OAAO,IAAI,CAAC,kBAAkB,CAC1B,GAAG,EACH,QAAQ,EACR,OAAO,EACP,KAAK,EACL,YAAY,EACZ,MAAM,EACN,OAAO,EACP,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,CAAC,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,oBAAoB,EAAE,EAAE;YACnE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;YACpB,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,KAAK,QAAQ,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,CAAC;YAEjE,OAAO,CAAC,cAAc,GAAG,aAAa,IAAI,CAAC,CAAC;YAE5C,MAAM,GAAG,GAAG,IAAI,CAAC,sCAAsC,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;YACnG,IAAI,KAAK,EAAE;gBACP,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAU,CAAC,CAAC;gBACtF,OAAO,KAAK,CAAC;aAChB;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;YAEjD,IAAI,GAAG,CAAC,KAAK,GAAG,cAAc,IAAI,GAAG,CAAC,MAAM,GAAG,cAAc,IAAI,CAAC,IAAI,CAAC,iCAAiC,EAAE;gBACtG,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;oBAC/C,OAAO,KAAK,CAAC;iBAChB;gBAED,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,QAAQ,CAAC;gBACrC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC;gBAEvC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAU,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;gBACnG,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,cAAgC,CAAC,CAAC;gBAEjH,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC;gBACzB,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;gBAE3B,OAAO,KAAK,CAAC;aAChB;iBAAM;gBACH,2EAA2E;gBAC3E,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC;gBACrE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;gBACvD,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAU,CAAC,CAAC;gBAEtF,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE;oBAC1D,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;oBAC7B,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;oBAExD,oBAAoB,EAAE,CAAC;gBAC3B,CAAC,CAAC,CAAC;aACN;YAED,OAAO,IAAI,CAAC;QAChB,CAAC,EACD,MAAM,EACN,QAAQ,EACR,MAAM,EACN,eAAe,EACf,QAAQ,EACR,aAAa,EACb,aAAa,CAChB,CAAC;IACN,CAAC;IAED;;;;;;;;;OASG;IACI,sCAAsC,CAAC,aAA+B,EAAE,aAAqB,EAAE,aAAsB;QACxH,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE;YACvD,aAAa,GAAG,aAAa,KAAK,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC;SAC3H;QAED,IAAI,MAAc,EAAE,cAAsB,CAAC;QAC3C,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE;YACzB,uIAAuI;YACvI,qIAAqI;YACrI,wBAAwB;YACxB,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;YAC/D,cAAc,GAAG,MAAM,CAAC;SAC3B;aAAM;YACH,0GAA0G;YAC1G,8EAA8E;YAC9E,0HAA0H;YAC1H,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACvD,cAAc,GAAG,IAAI,CAAC,iCAAiC,CAAC,SAAS,CAAC,yBAAyB,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;SAC9H;QAED,OAAO;YACH,cAAc;YACd,MAAM;YACN,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa;SAC/B,CAAC;IACN,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAC,mBAAmB,CAC7B,KAAoD,EACpD,MAAqD,EACrD,OAAoD,EACpD,eAA2C,EAC3C,QAAiB,EACjB,kBAAuC;QAEvC,MAAM,WAAW,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,MAAuB,EAAE,WAA4B,EAAE,KAAoB,EAAE,cAAsB,EAAE,UAAsB,IAAS,CAAC;IAE5J,uDAAuD;IACvD;;;;;;;;;;;;;;OAcG;IACI,gBAAgB,CACnB,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,MAAc,EACd,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI,EACpC,OAAe,SAAS,CAAC,wBAAwB,EACjD,aAAa,GAAG,CAAC,EACjB,gBAAyB,KAAK;QAE9B,MAAM,WAAW,CAAC,mBAAmB,CAAC,CAAC;IAC3C,CAAC;IAED,uDAAuD;IACvD;;;;;;;;;;;OAWG;IACI,oBAAoB,CACvB,IAAiC,EACjC,IAAY,EACZ,MAAc,EACd,IAAY,EACZ,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI;QAEpC,MAAM,WAAW,CAAC,mBAAmB,CAAC,CAAC;IAC3C,CAAC;IAED,uDAAuD;IACvD;;;;;;;;;;;;;OAaG;IACI,kBAAkB,CACrB,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,KAAa,EACb,MAAc,EACd,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI,EACpC,WAAW,GAAG,SAAS,CAAC,wBAAwB;QAEhD,MAAM,WAAW,CAAC,mBAAmB,CAAC,CAAC;IAC3C,CAAC;IAED,uDAAuD;IACvD;;;;;;;;;;;;;OAaG;IACI,uBAAuB,CAC1B,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,KAAa,EACb,MAAc,EACd,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI,EACpC,WAAW,GAAG,SAAS,CAAC,wBAAwB;QAEhD,MAAM,WAAW,CAAC,mBAAmB,CAAC,CAAC;IAC3C,CAAC;IAWD;;OAEG;IACI,YAAY,CAAC,KAAc;QAC9B,IAAI,IAAI,CAAC,kBAAkB,KAAK,KAAK,EAAE;YACnC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAElE,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;aACnC;SACJ;IACL,CAAC;IAED,gBAAgB;IACT,oBAAoB;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC5D,CAAC;IAEO,iBAAiB,CAAC,OAAwB;QAC9C,IAAI,OAAO,CAAC,MAAM,EAAE;YAChB,OAAO,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;SACpC;aAAM,IAAI,OAAO,CAAC,IAAI,EAAE;YACrB,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;SAC9B;aAAM,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,WAAW,EAAE;YACjD,OAAO,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;SACpC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACI,yBAAyB,CAAC,YAAoB,EAAE,OAAwB,EAAE,kBAA2B,KAAK;QAC7G,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,OAAO,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC;QAEjG,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAC5F,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAEnF,IAAI,eAAe,EAAE;YACjB,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;SACnC;QAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAExC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACI,uBAAuB,CAAC,OAAwB,EAAE,KAAa,EAAE,MAAc,EAAE,QAAgB,CAAC,IAAS,CAAC;IAEnH;;;;;;OAMG;IACI,yBAAyB,CAAC,OAAwB,EAAE,KAAuB,EAAE,QAA0B,IAAI,EAAE,QAA0B,IAAI;QAC9I,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAE/C,IAAI,KAAK,KAAK,IAAI,EAAE;YAChB,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;YAC5G,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;SAChC;QACD,IAAI,KAAK,KAAK,IAAI,EAAE;YAChB,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;YAC5G,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;SAChC;QACD,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE;YACvD,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;YAC5G,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;SAChC;QAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,yBAAyB,CAC5B,eAAgC,EAChC,IAAiE,EACjE,eAAwB,EACxB,iBAA0B,EAC1B,kBAA0B,EAC1B,OAAO,GAAG,CAAC;QAEX,MAAM,KAAK,GAAwD,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;QAC/F,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAY,IAAI,CAAC;QACjG,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAI,CAAC,CAAC;QAEtF,eAAe,CAAC,SAAS,GAAG,KAAK,CAAC;QAClC,eAAe,CAAC,UAAU,GAAG,MAAM,CAAC;QACpC,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;QAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;QAChC,eAAe,CAAC,SAAS,GAAG,MAAM,GAAG,CAAC,CAAC;QACvC,eAAe,CAAC,KAAK,GAAG,MAAM,CAAC;QAC/B,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC;QAC/B,eAAe,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,eAAe,CAAC,eAAe,GAAG,KAAK,CAAC;QACxC,eAAe,CAAC,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC;QACpI,eAAe,CAAC,IAAI,GAAG,SAAS,CAAC,wBAAwB,CAAC;QAC1D,eAAe,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAEzD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QACvD,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAC5F,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;QACxE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;QACxE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QAC9D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QAE9D,yDAAyD;QACzD,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE;YACvB,IAAI,kBAAkB,KAAK,CAAC,EAAE;gBAC1B,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;gBACpE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;aAC9D;iBAAM;gBACH,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;gBACtE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,EAAE,CAAC,sBAAsB,CAAC,CAAC;aAChF;SACJ;IACL,CAAC;IAED;;OAEG;IACI,sCAAsC,CACzC,OAAwB,EACxB,cAAsB,EACtB,KAAa,EACb,MAAc,EACd,IAAqB,EACrB,YAAoB,CAAC,EACrB,MAAc,CAAC;QAEf,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,IAAI,MAAM,GAAW,EAAE,CAAC,UAAU,CAAC;QACnC,IAAI,OAAO,CAAC,MAAM,EAAE;YAChB,MAAM,GAAG,EAAE,CAAC,2BAA2B,GAAG,SAAS,CAAC;SACvD;QAED,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,QAAQ,cAAc,EAAE;gBACpB,KAAK,SAAS,CAAC,kCAAkC,CAAC;gBAClD,KAAK,SAAS,CAAC,uCAAuC;oBAClD,8EAA8E;oBAC9E,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;wBACjB,cAAc,GAAG,EAAE,CAAC,qBAAqB,CAAC;qBAC7C;yBAAM;wBACH,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;qBAClC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,uCAAuC;oBAClD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;wBACjB,cAAc,GAAG,EAAE,CAAC,gCAAgC,CAAC;qBACxD;yBAAM;wBACH,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;qBAClC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,wCAAwC;oBACnD,cAAc,GAAG,EAAE,CAAC,oCAAoC,CAAC;oBACzD,MAAM;gBACV,KAAK,SAAS,CAAC,sCAAsC;oBACjD,cAAc,GAAG,EAAE,CAAC,oCAAoC,CAAC;oBACzD,MAAM;gBACV,KAAK,SAAS,CAAC,sCAAsC;oBACjD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;wBACtB,cAAc,GAAG,EAAE,CAAC,6BAA6B,CAAC;qBACrD;yBAAM;wBACH,oCAAoC;wBACpC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;qBAClC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,uCAAuC;oBAClD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;wBACtB,cAAc,GAAG,EAAE,CAAC,mCAAmC,CAAC;qBAC3D;yBAAM;wBACH,oCAAoC;wBACpC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;qBAClC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,uCAAuC;oBAClD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;wBACtB,cAAc,GAAG,EAAE,CAAC,mCAAmC,CAAC;qBAC3D;yBAAM;wBACH,oCAAoC;wBACpC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;qBAClC;oBACD,MAAM;gBACV;oBACI,+FAA+F;oBAC/F,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;oBAC/B,MAAM;aACb;SACJ;QAED,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAY,IAAI,CAAC,CAAC;IACjG,CAAC;IAED;;OAEG;IACI,4BAA4B,CAC/B,OAAwB,EACxB,SAA0B,EAC1B,YAAoB,CAAC,EACrB,MAAc,CAAC,EACf,qBAA8B,EAC9B,wBAAwB,GAAG,KAAK;QAEhC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5D,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,cAAc,GAChB,qBAAqB,KAAK,SAAS;YAC/B,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC;YAC9F,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;QAEjF,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAEnC,IAAI,MAAM,GAAW,EAAE,CAAC,UAAU,CAAC;QACnC,IAAI,OAAO,CAAC,MAAM,EAAE;YAChB,MAAM,GAAG,EAAE,CAAC,2BAA2B,GAAG,SAAS,CAAC;SACvD;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QACrE,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QACvE,MAAM,KAAK,GAAG,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACrG,MAAM,MAAM,GAAG,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAExG,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;IACjG,CAAC;IAED;;;;;;;;;;;OAWG;IACI,iBAAiB,CACpB,OAAwB,EACxB,SAA0B,EAC1B,OAAe,EACf,OAAe,EACf,KAAa,EACb,MAAc,EACd,YAAoB,CAAC,EACrB,MAAc,CAAC,EACf,eAAe,GAAG,KAAK;QAEvB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5D,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAEvD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAEnC,IAAI,gBAAgB,GAAW,EAAE,CAAC,UAAU,CAAC;QAC7C,IAAI,MAAM,GAAW,EAAE,CAAC,UAAU,CAAC;QACnC,IAAI,OAAO,CAAC,MAAM,EAAE;YAChB,MAAM,GAAG,EAAE,CAAC,2BAA2B,GAAG,SAAS,CAAC;YACpD,gBAAgB,GAAG,EAAE,CAAC,gBAAgB,CAAC;SAC1C;QAED,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAE3D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;QAE/F,IAAI,eAAe,EAAE;YACjB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;SACnC;QAED,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,+BAA+B,CAAC,OAAwB,EAAE,SAA0B,EAAE,YAAoB,CAAC,EAAE,MAAc,CAAC;QAC/H,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC;QAExE,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;QAEtE,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAES,gCAAgC,CAAC,OAAwB,EAAE,KAA2B,EAAE,QAAiB,EAAE,YAAqB,EAAE,YAAoB;QAC5J,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,IAAI,CAAC,EAAE,EAAE;YACL,OAAO;SACV;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,CAAC;QAErE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACpE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAEpE,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,EAAE;YAC5B,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;SACpC;QAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAE/C,4BAA4B;QAC5B,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;SACpC;QAED,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACpD,OAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;IACvC,CAAC;IAEO,oBAAoB,CACxB,OAAwB,EACxB,SAAiB,EACjB,KAA2B,EAC3B,GAAuE,EACvE,OAAgB,EAChB,QAAiB,EACjB,YAAqB,EACrB,eAOY,EACZ,eAAuB,SAAS,CAAC,8BAA8B;QAE/D,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,cAAc,CAAC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,UAAU,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACrI,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,UAAU,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAExI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,IAAI,CAAC,EAAE,EAAE;YACL,OAAO;SACV;QAED,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YAC3B,6BAA6B;YAC7B,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;aACpC;YAED,OAAO;SACV;QAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACxD,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAEzE,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC;QAC9B,OAAO,CAAC,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC;QAChC,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC;QACzB,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;QAC3B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC;QACxF,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,KAAK,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC;QAEvK,IACI,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE;YAC/D,IAAI,CAAC,gCAAgC,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;QAChG,CAAC,CAAC,EACJ;YACE,+CAA+C;YAC/C,OAAO;SACV;QAED,IAAI,CAAC,gCAAgC,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;IAChG,CAAC;IAED;;OAEG;IACI,iCAAiC,CACpC,qBAA8B,EAC9B,mBAA4B,EAC5B,KAAa,EACb,MAAc,EACd,OAAO,GAAG,CAAC;QAEX,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,kCAAkC;QAClC,IAAI,qBAAqB,IAAI,mBAAmB,EAAE;YAC9C,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,CAAC,aAAa,EAAE,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,wBAAwB,CAAC,CAAC;SAC/H;QACD,IAAI,mBAAmB,EAAE;YACrB,IAAI,WAAW,GAAW,EAAE,CAAC,iBAAiB,CAAC;YAC/C,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;gBACxB,WAAW,GAAG,EAAE,CAAC,kBAAkB,CAAC;aACvC;YAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC;SAC1G;QACD,IAAI,qBAAqB,EAAE;YACvB,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC;SACxH;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,mBAAmB,CACtB,KAAa,EACb,MAAc,EACd,OAAe,EACf,cAAsB,EACtB,gBAAwB,EACxB,UAAkB,EAClB,YAAY,GAAG,IAAI;QAEnB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,MAAM,YAAY,GAAG,EAAE,CAAC,kBAAkB,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,gBAAgB,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;IACtI,CAAC;IAEM,mBAAmB,CACtB,YAAyC,EACzC,KAAa,EACb,MAAc,EACd,OAAe,EACf,cAAsB,EACtB,gBAAwB,EACxB,UAAkB,EAClB,YAAY,GAAG,IAAI;QAEnB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,EAAE,CAAC,gBAAgB,CAAC,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QAEnD,IAAI,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC,8BAA8B,EAAE;YAClD,EAAE,CAAC,8BAA8B,CAAC,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;SAChG;aAAM;YACH,EAAE,CAAC,mBAAmB,CAAC,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;SAC1E;QAED,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QAEtF,IAAI,YAAY,EAAE;YACd,EAAE,CAAC,gBAAgB,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;SAC9C;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,OAAwB;QAC3C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC;QAElE,kBAAkB;QAClB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAChD;QAED,iCAAiC;QACjC,IAAI,OAAO,CAAC,eAAe,EAAE;YACzB,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;SACrC;QACD,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;SACpC;QACD,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;SACpC;QAED,6BAA6B;QAC7B,IAAI,OAAO,CAAC,kBAAkB,EAAE;YAC5B,OAAO,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;SACxC;IACL,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,SAA8B;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAChE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACnD;IACL,CAAC;IAES,cAAc,CAAC,OAA+B;QACpD,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SACnC;IACL,CAAC;IAES,WAAW,CAAC,OAAqB;QACvC,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE;YAClC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC7B,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;SAClC;IACL,CAAC;IAID;;;OAGG;IACI,YAAY,CAAC,MAAc;QAC9B,MAAM,oBAAoB,GAAG,MAAM,CAAC,kBAAkB,EAA0B,CAAC;QACjF,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC,OAAQ,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACtC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAClD,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAEnD,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;aACxC;SACJ;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAEO,uBAAuB;QAC3B,IAAI,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,cAAc,EAAE;YACrD,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;YAChE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,cAAc,CAAC;SACrD;IACL,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,MAAc,EAAE,OAAkC,EAAE,oBAAoB,GAAG,KAAK,EAAE,KAAK,GAAG,KAAK;QACvH,IAAI,kBAAkB,GAAG,KAAK,CAAC;QAC/B,MAAM,qBAAqB,GAAG,OAAO,IAAI,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC;QACzE,IAAI,oBAAoB,IAAI,qBAAqB,EAAE;YAC/C,IAAI,CAAC,cAAc,GAAG,OAAQ,CAAC,kBAAkB,CAAC;SACrD;QAED,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE1E,IAAI,mBAAmB,KAAK,OAAO,IAAI,KAAK,EAAE;YAC1C,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAE/B,IAAI,OAAO,IAAI,OAAO,CAAC,WAAW,EAAE;gBAChC,4EAA4E;gBAC5E,MAAM,CAAC,KAAK,CAAC,CAAC,uDAAuD,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;gBACzF,4CAA4C;gBAC5C,MAAM,uDAAuD,CAAC;aACjE;iBAAM;gBACH,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,kBAAkB,IAAI,IAAI,CAAC,CAAC;aACvF;YAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC;YAExD,IAAI,OAAO,EAAE;gBACT,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC;aACpD;SACJ;aAAM,IAAI,oBAAoB,EAAE;YAC7B,kBAAkB,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,uBAAuB,EAAE,CAAC;SAClC;QAED,IAAI,qBAAqB,IAAI,CAAC,oBAAoB,EAAE;YAChD,IAAI,CAAC,4BAA4B,CAAC,OAAQ,CAAC,kBAAkB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;SACvF;QAED,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,OAAe,EAAE,OAAkC,EAAE,IAAY;QACjF,IAAI,OAAO,KAAK,SAAS,EAAE;YACvB,OAAO;SACV;QAED,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,kBAAkB,GAAG,OAAO,CAAC;SACxC;QAED,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAC9B,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;QAC/E,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE,EAAE;YACtE,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;YAC9B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YACrD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;YAC3D,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE;gBACvB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;gBACrD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;aAC9D;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,UAAU,CAAC,OAAe,EAAE,OAAuC,EAAE,OAA8B,EAAE,IAAY;QACpH,IAAI,OAAO,KAAK,SAAS,EAAE;YACvB,OAAO;SACV;QAED,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;SAC1C;QAED,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;IAEO,4BAA4B,CAAC,UAAkB,EAAE,WAAmB;QACxE,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,aAAa,KAAK,WAAW,EAAE;YACnD,OAAO;SACV;QACD,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QACzC,OAAO,CAAC,aAAa,GAAG,WAAW,CAAC;IACxC,CAAC;IAEO,mBAAmB,CAAC,IAAY;QACpC,QAAQ,IAAI,EAAE;YACV,KAAK,SAAS,CAAC,wBAAwB;gBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YAC3B,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;YAClC,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC;SACvC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;IAC3B,CAAC;IAES,WAAW,CAAC,OAAe,EAAE,OAA8B,EAAE,oBAAoB,GAAG,KAAK,EAAE,mBAAmB,GAAG,KAAK,EAAE,IAAI,GAAG,EAAE;QACvI,aAAa;QACb,IAAI,CAAC,OAAO,EAAE;YACV,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE;gBAC3C,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;gBAC9B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;gBACrD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;gBAC3D,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE;oBACvB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;oBACrD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;iBAC9D;aACJ;YACD,OAAO,KAAK,CAAC;SAChB;QAED,QAAQ;QACR,IAAmB,OAAQ,CAAC,KAAK,EAAE;YAC/B,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;YAC9B,MAAM,oBAAoB,GAAkB,OAAQ,CAAC,kBAAkB,EAAE,CAAC;YAC1E,IAAI,oBAAoB,EAAE;gBACtB,oBAAoB,CAAC,kBAAkB,GAAG,OAAO,CAAC;aACrD;YACc,OAAQ,CAAC,MAAM,EAAE,CAAC;SACpC;aAAM,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,CAAC,wBAAwB,EAAE;YACtE,gBAAgB;YAChB,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,eAAgC,CAAC;QACrC,IAAI,mBAAmB,EAAE;YACrB,eAAe,GAAyB,OAAQ,CAAC,mBAAoB,CAAC;SACzE;aAAM,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE;YAC1B,eAAe,GAAoB,OAAO,CAAC,kBAAkB,EAAE,CAAC;SACnE;aAAM,IAAI,OAAO,CAAC,MAAM,EAAE;YACvB,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;SAC3C;aAAM,IAAI,OAAO,CAAC,IAAI,EAAE;YACrB,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC;SACzC;aAAM,IAAI,OAAO,CAAC,SAAS,EAAE;YAC1B,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC;SAC9C;aAAM;YACH,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC;SACvC;QAED,IAAI,CAAC,oBAAoB,IAAI,eAAe,EAAE;YAC1C,eAAe,CAAC,kBAAkB,GAAG,OAAO,CAAC;SAChD;QAED,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,KAAK,eAAe,EAAE;YACvD,IAAI,CAAC,oBAAoB,EAAE;gBACvB,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;aAClF;YAED,UAAU,GAAG,KAAK,CAAC;SACtB;QAED,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QACvD,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,eAAe,EAAE,oBAAoB,CAAC,CAAC;SAC5E;QAED,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE;YACjD,sFAAsF;YACtF,IAAI,eAAe,CAAC,MAAM,IAAI,eAAe,CAAC,sBAAsB,KAAK,OAAO,CAAC,eAAe,EAAE;gBAC9F,eAAe,CAAC,sBAAsB,GAAG,OAAO,CAAC,eAAe,CAAC;gBAEjE,MAAM,eAAe,GACjB,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,kBAAkB,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,mBAAmB;oBACjH,CAAC,CAAC,SAAS,CAAC,wBAAwB;oBACpC,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC;gBAC9C,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC;gBAChC,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC;aACnC;YAED,IAAI,eAAe,CAAC,YAAY,KAAK,OAAO,CAAC,KAAK,EAAE;gBAChD,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC7C,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,eAAe,CAAC,CAAC;aAC/H;YAED,IAAI,eAAe,CAAC,YAAY,KAAK,OAAO,CAAC,KAAK,EAAE;gBAChD,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC7C,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,eAAe,CAAC,CAAC;aAC/H;YAED,IAAI,eAAe,CAAC,IAAI,IAAI,eAAe,CAAC,YAAY,KAAK,OAAO,CAAC,KAAK,EAAE;gBACxE,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC7C,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,eAAe,CAAC,CAAC;aAC/H;YAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,eAAe,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;SACzF;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,eAAe,CAAC,OAAe,EAAE,OAAuC,EAAE,QAAuB,EAAE,IAAY;QAClH,IAAI,OAAO,KAAK,SAAS,IAAI,CAAC,OAAO,EAAE;YACnC,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,EAAE;YACtE,IAAI,CAAC,aAAa,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;SACxD;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC;YAEjD,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC;gBACpC,OAAO,CAAC,kBAAkB,GAAG,OAAO,GAAG,CAAC,CAAC;aAC5C;iBAAM;gBACH,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aAC9B;SACJ;QACD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAEjD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;SACtE;IACL,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,MAAc,EAAE,eAAgC,EAAE,yBAAiC;QAC3G,MAAM,0BAA0B,GAAG,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC;QAChF,IACI,eAAe,CAAC,YAAY,KAAK,SAAS,CAAC,gCAAgC;YAC3E,eAAe,CAAC,YAAY,KAAK,SAAS,CAAC,+BAA+B;YAC1E,eAAe,CAAC,YAAY,KAAK,SAAS,CAAC,qBAAqB,EAClE;YACE,yBAAyB,GAAG,CAAC,CAAC,CAAC,+EAA+E;SACjH;QAED,IAAI,0BAA0B,IAAI,eAAe,CAAC,gCAAgC,KAAK,yBAAyB,EAAE;YAC9G,IAAI,CAAC,yBAAyB,CAC1B,MAAM,EACN,0BAA0B,CAAC,0BAA0B,EACrD,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAC7D,eAAe,CAClB,CAAC;YACF,eAAe,CAAC,gCAAgC,GAAG,yBAAyB,CAAC;SAChF;IACL,CAAC;IAEO,yBAAyB,CAAC,MAAc,EAAE,SAAiB,EAAE,KAAa,EAAE,OAAwB;QACxG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAEO,2BAA2B,CAAC,MAAc,EAAE,SAAiB,EAAE,KAAa,EAAE,OAAyB;QAC3G,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;SAC1D;QACD,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;YAEvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE;gBAClD,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;aACnC;YACD,OAAO;SACV;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;YACtE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE;gBACzE,SAAS;aACZ;YAED,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;SACnC;IACL,CAAC;IAED;;OAEG;IACI,cAAc;QACjB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACtC,MAAM,oBAAoB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAA0B,CAAC;YACtG,IAAI,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAC;SACrD;QAED,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,oBAAoB;QACpB,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACpC,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;SAC9C;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B;QACD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC7C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACjC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACtD;QAED,kBAAkB;QAClB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;QAE/B,SAAS;QACT,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAEzB,SAAS;QACT,IAAI,mBAAmB,EAAE,EAAE;YACvB,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;oBAC/B,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;oBACnF,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,sBAAsB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;iBAC9F;gBAED,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;aAC9D;SACJ;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,MAAM,CAAC,UAAU,EAAE,CAAC;QAEpB,wBAAwB;QACxB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,eAAe,EAAE;YACxC,OAAO,CAAC,KAAK,EAAE,CAAC;SACnB;QAED,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE;YAC5C,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oBAAoB,CAAC,EAAE,WAAW,EAAE,CAAC;SAC9D;IACL,CAAC;IAED;;;OAGG;IACI,sBAAsB,CAAC,QAA4C;QACtE,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,kBAAkB,EAAO,QAAQ,EAAE,KAAK,CAAC,CAAC;SACpF;IACL,CAAC;IAED;;;OAGG;IACI,0BAA0B,CAAC,QAA4C;QAC1E,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,sBAAsB,EAAO,QAAQ,EAAE,KAAK,CAAC,CAAC;SACxF;IACL,CAAC;IAED;;;;OAIG;IACI,QAAQ;QACX,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;IAC/B,CAAC;IAEO,4BAA4B;QAChC,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YACxB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;SACtC;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;IACrE,CAAC;IAEO,gCAAgC;QACpC,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YACxB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;SACtC;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;IAC1E,CAAC;IAED,qGAAqG;IAC7F,uBAAuB,CAAC,IAAY;QACxC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,uBAAuB;QACvB,oCAAoC;QACpC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,GAAE;QAExC,IAAI,UAAU,GAAG,IAAI,CAAC;QAEtB,MAAM,OAAO,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;QACnC,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACvC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;QACvI,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;QACnE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;QAEnE,MAAM,EAAE,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;QAClC,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QACvC,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;QACzF,MAAM,MAAM,GAAG,EAAE,CAAC,sBAAsB,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;QAEzD,UAAU,GAAG,UAAU,IAAI,MAAM,KAAK,EAAE,CAAC,oBAAoB,CAAC;QAC9D,UAAU,GAAG,UAAU,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC;QAEzD,oDAAoD;QACpD,IAAI,UAAU,EAAE;YACZ,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;YAC9B,UAAU,GAAG,UAAU,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC;SAC5D;QAED,iIAAiI;QACjI,IAAI,UAAU,EAAE;YACZ,6HAA6H;YAC7H,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACzC,MAAM,UAAU,GAAG,EAAE,CAAC,IAAI,CAAC;YAC3B,MAAM,QAAQ,GAAG,EAAE,CAAC,aAAa,CAAC;YAClC,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;YACjC,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YACxD,UAAU,GAAG,UAAU,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC;SAC5D;QAED,UAAU;QACV,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC1B,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;QACzB,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAEzC,0BAA0B;QAC1B,oCAAoC;QACpC,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,GAAE;QAEvD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,IAAY;QACpC,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE;YAC1B,QAAQ,IAAI,EAAE;gBACV,KAAK,SAAS,CAAC,iBAAiB;oBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;gBAC1B,KAAK,SAAS,CAAC,sBAAsB;oBACjC,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;gBACnC,KAAK,SAAS,CAAC,yBAAyB;oBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;gBAClC,KAAK,SAAS,CAAC,kCAAkC;oBAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC;gBAC3C,KAAK,SAAS,CAAC,kCAAkC;oBAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC;gBAC3C,KAAK,SAAS,CAAC,gCAAgC;oBAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC;aAC5C;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;SACjC;QAED,QAAQ,IAAI,EAAE;YACV,KAAK,SAAS,CAAC,gBAAgB;gBAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;YACzB,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;YAClC,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAC1B,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;YACnC,KAAK,SAAS,CAAC,eAAe;gBAC1B,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YACxB,KAAK,SAAS,CAAC,4BAA4B,EAAE,yBAAyB;gBAClE,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC;YACjC,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAC1B,KAAK,SAAS,CAAC,sBAAsB;gBACjC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;YAC/B,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC;YAC3C,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC;YAC3C,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC;YACzC,KAAK,SAAS,CAAC,uCAAuC;gBAClD,OAAO,IAAI,CAAC,GAAG,CAAC,2BAA2B,CAAC;YAChD,KAAK,SAAS,CAAC,6BAA6B;gBACxC,OAAO,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC;YACtC,KAAK,SAAS,CAAC,wCAAwC;gBACnD,OAAO,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC;YACjD,KAAK,SAAS,CAAC,oCAAoC;gBAC/C,OAAO,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC;YAC7C,KAAK,SAAS,CAAC,0CAA0C;gBACrD,OAAO,IAAI,CAAC,GAAG,CAAC,8BAA8B,CAAC;SACtD;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,MAAc,EAAE,aAAa,GAAG,KAAK;QAC3D,IAAI,cAAc,GAAW,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QAEtG,QAAQ,MAAM,EAAE;YACZ,KAAK,SAAS,CAAC,mBAAmB;gBAC9B,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;gBAChC,MAAM;YACV,KAAK,SAAS,CAAC,uBAAuB;gBAClC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;gBACpC,MAAM;YACV,KAAK,SAAS,CAAC,6BAA6B;gBACxC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC;gBAC1C,MAAM;YACV,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;gBAC9B,MAAM;YACV,KAAK,SAAS,CAAC,gBAAgB;gBAC3B,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,MAAM;YACV,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,cAAc,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;gBACjF,MAAM;YACV,KAAK,SAAS,CAAC,kBAAkB;gBAC7B,cAAc,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC1F,MAAM;SACb;QAED,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YACxB,QAAQ,MAAM,EAAE;gBACZ,KAAK,SAAS,CAAC,yBAAyB;oBACpC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;oBACtC,MAAM;gBACV,KAAK,SAAS,CAAC,wBAAwB;oBACnC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;oBACrC,MAAM;gBACV,KAAK,SAAS,CAAC,yBAAyB;oBACpC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;oBACtC,MAAM;gBACV,KAAK,SAAS,CAAC,0BAA0B;oBACrC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC;oBACvC,MAAM;aACb;SACJ;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,iCAAiC,CAAC,IAAY,EAAE,MAAe,EAAE,aAAa,GAAG,KAAK;QACzF,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE;YAC1B,IAAI,MAAM,KAAK,SAAS,EAAE;gBACtB,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,mBAAmB;wBAC9B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,uBAAuB;wBAClC,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;oBAC9B,KAAK,SAAS,CAAC,6BAA6B;wBACxC,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC;oBACpC,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;iBAC9E;aACJ;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;SACxB;QAED,QAAQ,IAAI,EAAE;YACV,KAAK,SAAS,CAAC,gBAAgB;gBAC3B,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;oBAC7B,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;oBAC9B,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;oBAC/B,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;oBACxB,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;oBACzB,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;iBACnC;YACL,KAAK,SAAS,CAAC,yBAAyB;gBACpC,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACvB,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;oBACxB,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qDAAqD;oBACnI,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,oEAAoE;oBAC1J,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;oBACzB,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B,KAAK,SAAS,CAAC,mBAAmB;wBAC9B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,uBAAuB;wBAClC,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;oBAC9B,KAAK,SAAS,CAAC,6BAA6B;wBACxC,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC;oBACpC;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;iBAC7B;YACL,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;oBACzB,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;iBAC/B;YACL,KAAK,SAAS,CAAC,0BAA0B;gBACrC,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;oBAC7B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;iBAChC;YACL,KAAK,SAAS,CAAC,eAAe;gBAC1B,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;oBACzB,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;iBAC/B;YACL,KAAK,SAAS,CAAC,4BAA4B,EAAE,yBAAyB;gBAClE,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;oBAC7B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;iBAChC;YACL,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yCAAyC;oBACnE,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,0CAA0C;oBACrE,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,uEAAuE;oBACnG,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,4CAA4C;oBACzE;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;iBAC/B;YACL,KAAK,SAAS,CAAC,sBAAsB;gBACjC,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;oBACzB,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,+DAA+D;oBAC3F,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;iBAC/B;YACL,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YAC3B,KAAK,SAAS,CAAC,wCAAwC;gBACnD,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;YACnC,KAAK,SAAS,CAAC,oCAAoC;gBAC/C,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YAC5B,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAC1B,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YAC5B,KAAK,SAAS,CAAC,uCAAuC;gBAClD,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,4CAA4C;oBAC1E,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;oBAC/B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;iBAChC;SACR;QAED,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;IACrF,CAAC;IAED;;OAEG;IACI,SAAS,CACZ,GAAW,EACX,SAAqE,EACrE,UAAgC,EAChC,eAAkC,EAClC,cAAwB,EACxB,OAA0D;QAE1D,MAAM,OAAO,GAAG,UAAU,CAAC,kBAAkB,CAAC,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;QACpH,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACzC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAC,kBAAkB,CAC5B,GAAW,EACX,SAAqE,EACrE,UAAwC,EACxC,eAAkC,EAClC,cAAwB,EACxB,OAAmE;QAEnE,MAAM,WAAW,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;IAED;;;;;;;;;OASG;IACI,UAAU,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAE,QAAQ,GAAG,IAAI,EAAE,aAAa,GAAG,IAAI;QACxG,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACvD,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,MAAM,GAAG,KAAK,GAAG,WAAW,CAAC,CAAC;QAC1D,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;QACD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAC/E,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAOD;;OAEG;IACI,MAAM,KAAK,gBAAgB;QAC9B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,WAAW;QACzB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,kBAAkB;IACjD,CAAC;IAED;;;;OAIG;IACH,gEAAgE;IACzD,MAAM,CAAC,WAAW;QACrB,IAAI,IAAI,CAAC,0BAA0B,KAAK,IAAI,EAAE;YAC1C,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,iDAAiD;SAC7F;QAED,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;YAC5B,IAAI;gBACA,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5C,MAAM,EAAE,GAAG,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,IAAK,UAAkB,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC;gBAElG,IAAI,CAAC,YAAY,GAAG,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC;aACpE;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;aAC7B;SACJ;QAED,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,yBAAyB;QACvC,IAAI,IAAI,CAAC,0BAA0B,KAAK,IAAI,EAAE;YAC1C,IAAI;gBACA,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5C,MAAM,EAAE,GACJ,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,4BAA4B,EAAE,IAAI,EAAE,CAAC;oBACrE,UAAkB,CAAC,UAAU,CAAC,oBAAoB,EAAE,EAAE,4BAA4B,EAAE,IAAI,EAAE,CAAC,CAAC;gBAEjG,IAAI,CAAC,0BAA0B,GAAG,CAAC,EAAE,CAAC;aACzC;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;aAC3C;SACJ;QAED,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,UAAU,CAAC,CAAS;QAC9B,CAAC,EAAE,CAAC;QACJ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACZ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACZ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACZ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACZ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACb,CAAC,EAAE,CAAC;QACJ,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,QAAQ,CAAC,CAAS;QAC5B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACjB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACjB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACjB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACjB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAClB,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,UAAU,CAAC,CAAS;QAC9B,MAAM,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,gBAAgB,CAAC,KAAa,EAAE,GAAW,EAAE,IAAI,GAAG,SAAS,CAAC,iBAAiB;QACzF,IAAI,GAAG,CAAC;QAER,QAAQ,IAAI,EAAE;YACV,KAAK,SAAS,CAAC,eAAe;gBAC1B,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACjC,MAAM;YACV,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,GAAG,GAAG,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACnC,MAAM;YACV,KAAK,SAAS,CAAC,iBAAiB,CAAC;YACjC;gBACI,GAAG,GAAG,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACnC,MAAM;SACb;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAC9B,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,aAAa,CAAC,IAAgB,EAAE,SAAe;QACzD,sJAAsJ;QACtJ,uJAAuJ;QACvJ,aAAa;QAEb,IAAI,CAAC,mBAAmB,EAAE,EAAE;YACxB,IAAI,OAAO,qBAAqB,KAAK,UAAU,EAAE;gBAC7C,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC;aACtC;SACJ;aAAM;YACH,MAAM,EAAE,qBAAqB,EAAE,GAAG,SAAS,IAAI,MAAM,CAAC;YACtD,IAAI,OAAO,qBAAqB,KAAK,UAAU,EAAE;gBAC7C,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC;aACtC;SACJ;QAED,uCAAuC;QACvC,sJAAsJ;QACtJ,OAAO,UAAU,CAAC,IAAI,EAAE,EAAE,CAAsB,CAAC;IACrD,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE;YAC9D,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC;SAC9C;QAED,OAAO,mBAAmB,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;IACnD,CAAC;;AA72Lc,gCAAqB,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,AAArB,CAAsB;AAC3C,+BAAoB,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,AAApB,CAAqB;AAExD,sFAAsF;AACxE,wBAAa,GAAG;IAC1B,EAAE,GAAG,EAAE,aAAa,EAAE,OAAO,EAAE,wBAAwB,EAAE,iBAAiB,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,eAAe,CAAC,EAAE;IAC7G,EAAE,GAAG,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,eAAe,CAAC,EAAE;IACzF,EAAE,GAAG,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,eAAe,CAAC,EAAE;IACzF,EAAE,GAAG,EAAE,oBAAoB,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC,EAAE;IACvF,EAAE,GAAG,EAAE,oBAAoB,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC,EAAE;IACvF,EAAE,GAAG,EAAE,oBAAoB,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC,EAAE;IACvF,EAAE,GAAG,EAAE,mBAAmB,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC,EAAE;IACtF,EAAE,GAAG,EAAE,mBAAmB,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC,EAAE;IACtF,EAAE,GAAG,EAAE,gBAAgB,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,eAAe,CAAC,EAAE;IAC7F,EAAE,GAAG,EAAE,0BAA0B,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,eAAe,CAAC,EAAE;IACvG,0BAA0B;IAC1B,EAAE,GAAG,EAAE,+BAA+B,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC,EAAE;IAC1H,2CAA2C;IAC3C,EAAE,GAAG,EAAE,+BAA+B,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC,EAAE;CAC7H,AAf0B,CAezB;AAEF,gBAAgB;AACF,0BAAe,GAA6B,EAAE,AAA/B,CAAgC;AAyD7D,4CAA4C;AAE5C;;GAEG;AACW,4BAAiB,GAAG,KAAK,AAAR,CAAS;AAinLxC,UAAU;AAEK,uBAAY,GAAsB,IAAI,AAA1B,CAA2B;AACvC,qCAA0B,GAAsB,IAAI,AAA1B,CAA2B", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { EngineStore } from \"./engineStore\";\r\nimport type { IInternalTextureLoader } from \"../Materials/Textures/internalTextureLoader\";\r\nimport type { IEffectCreationOptions } from \"../Materials/effect\";\r\nimport { Effect } from \"../Materials/effect\";\r\nimport { _WarnImport } from \"../Misc/devTools\";\r\nimport type { IShaderProcessor } from \"./Processors/iShaderProcessor\";\r\nimport type { ShaderProcessingContext } from \"./Processors/shaderProcessingOptions\";\r\nimport type { UniformBuffer } from \"../Materials/uniformBuffer\";\r\nimport type { Nullable, DataArray, IndicesArray, FloatArray, DeepImmutable } from \"../types\";\r\nimport type { EngineCapabilities } from \"./engineCapabilities\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { DepthCullingState } from \"../States/depthCullingState\";\r\nimport { StencilState } from \"../States/stencilState\";\r\nimport { AlphaState } from \"../States/alphaCullingState\";\r\nimport { Constants } from \"./constants\";\r\nimport { InternalTexture, InternalTextureSource } from \"../Materials/Textures/internalTexture\";\r\nimport type { IViewportLike, IColor4Like } from \"../Maths/math.like\";\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport type { IFileRequest } from \"../Misc/fileRequest\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { IsDocumentAvailable, IsWindowObjectExist } from \"../Misc/domManagement\";\r\nimport { WebGLShaderProcessor } from \"./WebGL/webGLShaderProcessors\";\r\nimport { WebGL2ShaderProcessor } from \"./WebGL/webGL2ShaderProcessors\";\r\nimport { WebGLDataBuffer } from \"../Meshes/WebGL/webGLDataBuffer\";\r\nimport type { IPipelineContext } from \"./IPipelineContext\";\r\nimport { WebGLPipelineContext } from \"./WebGL/webGLPipelineContext\";\r\nimport type { VertexBuffer } from \"../Buffers/buffer\";\r\nimport type { InstancingAttributeInfo } from \"./instancingAttributeInfo\";\r\nimport type { ThinTexture } from \"../Materials/Textures/thinTexture\";\r\nimport type { IOfflineProvider } from \"../Offline/IOfflineProvider\";\r\nimport type { IEffectFallbacks } from \"../Materials/iEffectFallbacks\";\r\nimport type { IWebRequest } from \"../Misc/interfaces/iWebRequest\";\r\nimport { PerformanceConfigurator } from \"./performanceConfigurator\";\r\nimport type { EngineFeatures } from \"./engineFeatures\";\r\nimport type { HardwareTextureWrapper } from \"../Materials/Textures/hardwareTextureWrapper\";\r\nimport { WebGLHardwareTexture } from \"./WebGL/webGLHardwareTexture\";\r\nimport { DrawWrapper } from \"../Materials/drawWrapper\";\r\nimport type { IMaterialContext } from \"./IMaterialContext\";\r\nimport type { IDrawContext } from \"./IDrawContext\";\r\nimport type { ICanvas, ICanvasRenderingContext, IImage } from \"./ICanvas\";\r\nimport { StencilStateComposer } from \"../States/stencilStateComposer\";\r\nimport type { StorageBuffer } from \"../Buffers/storageBuffer\";\r\nimport type { IAudioEngineOptions } from \"../Audio/Interfaces/IAudioEngineOptions\";\r\nimport type { IStencilState } from \"../States/IStencilState\";\r\nimport type { InternalTextureCreationOptions, TextureSize } from \"../Materials/Textures/textureCreationOptions\";\r\nimport { ShaderLanguage } from \"../Materials/shaderLanguage\";\r\nimport type { RenderTargetWrapper } from \"./renderTargetWrapper\";\r\nimport type { WebGLRenderTargetWrapper } from \"./WebGL/webGLRenderTargetWrapper\";\r\nimport type { VideoTexture } from \"../Materials/Textures/videoTexture\";\r\nimport type { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport type { WebRequest } from \"../Misc/webRequest\";\r\nimport type { LoadFileError } from \"../Misc/fileTools\";\r\nimport type { Texture } from \"../Materials/Textures/texture\";\r\nimport { PrecisionDate } from \"../Misc/precisionDate\";\r\n\r\n/**\r\n * Defines the interface used by objects working like Scene\r\n * @internal\r\n */\r\nexport interface ISceneLike {\r\n    addPendingData(data: any): void;\r\n    removePendingData(data: any): void;\r\n    offlineProvider: IOfflineProvider;\r\n}\r\n\r\n/**\r\n * Keeps track of all the buffer info used in engine.\r\n */\r\nclass BufferPointer {\r\n    public active: boolean;\r\n    public index: number;\r\n    public size: number;\r\n    public type: number;\r\n    public normalized: boolean;\r\n    public stride: number;\r\n    public offset: number;\r\n    public buffer: WebGLBuffer;\r\n}\r\n\r\n/**\r\n * Information about the current host\r\n */\r\nexport interface HostInformation {\r\n    /**\r\n     * Defines if the current host is a mobile\r\n     */\r\n    isMobile: boolean;\r\n}\r\n\r\n/** Interface defining initialization parameters for ThinEngine class */\r\nexport interface ThinEngineOptions {\r\n    /**\r\n     * Defines if the engine should no exceed a specified device ratio\r\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Window/devicePixelRatio\r\n     */\r\n    limitDeviceRatio?: number;\r\n    /**\r\n     * Defines if webaudio should be initialized as well\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n     */\r\n    audioEngine?: boolean;\r\n    /**\r\n     * Specifies options for the audio engine\r\n     */\r\n    audioEngineOptions?: IAudioEngineOptions;\r\n\r\n    /**\r\n     * Defines if animations should run using a deterministic lock step\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     */\r\n    deterministicLockstep?: boolean;\r\n    /** Defines the maximum steps to use with deterministic lock step mode */\r\n    lockstepMaxSteps?: number;\r\n    /** Defines the seconds between each deterministic lock step */\r\n    timeStep?: number;\r\n    /**\r\n     * Defines that engine should ignore context lost events\r\n     * If this event happens when this parameter is true, you will have to reload the page to restore rendering\r\n     */\r\n    doNotHandleContextLost?: boolean;\r\n    /**\r\n     * Defines that engine should ignore modifying touch action attribute and style\r\n     * If not handle, you might need to set it up on your side for expected touch devices behavior.\r\n     */\r\n    doNotHandleTouchAction?: boolean;\r\n\r\n    /**\r\n     * Make the matrix computations to be performed in 64 bits instead of 32 bits. False by default\r\n     */\r\n    useHighPrecisionMatrix?: boolean;\r\n\r\n    /**\r\n     * Defines whether to adapt to the device's viewport characteristics (default: false)\r\n     */\r\n    adaptToDeviceRatio?: boolean;\r\n\r\n    /**\r\n     * True if the more expensive but exact conversions should be used for transforming colors to and from linear space within shaders.\r\n     * Otherwise, the default is to use a cheaper approximation.\r\n     */\r\n    useExactSrgbConversions?: boolean;\r\n\r\n    /**\r\n     * Defines whether MSAA is enabled on the canvas.\r\n     */\r\n    antialias?: boolean;\r\n\r\n    /**\r\n     * Defines whether the stencil buffer should be enabled.\r\n     */\r\n    stencil?: boolean;\r\n\r\n    /**\r\n     * Defines whether the canvas should be created in \"premultiplied\" mode (if false, the canvas is created in the \"opaque\" mode) (true by default)\r\n     */\r\n    premultipliedAlpha?: boolean;\r\n}\r\n\r\n/** Interface defining initialization parameters for Engine class */\r\nexport interface EngineOptions extends ThinEngineOptions, WebGLContextAttributes {\r\n    /**\r\n     * Defines if webgl2 should be turned off even if supported\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2\r\n     */\r\n    disableWebGL2Support?: boolean;\r\n\r\n    /**\r\n     * Defines that engine should compile shaders with high precision floats (if supported). True by default\r\n     */\r\n    useHighPrecisionFloats?: boolean;\r\n    /**\r\n     * Make the canvas XR Compatible for XR sessions\r\n     */\r\n    xrCompatible?: boolean;\r\n\r\n    /**\r\n     * Will prevent the system from falling back to software implementation if a hardware device cannot be created\r\n     */\r\n    failIfMajorPerformanceCaveat?: boolean;\r\n\r\n    /**\r\n     * If sRGB Buffer support is not set during construction, use this value to force a specific state\r\n     * This is added due to an issue when processing textures in chrome/edge/firefox\r\n     * This will not influence NativeEngine and WebGPUEngine which set the behavior to true during construction.\r\n     */\r\n    forceSRGBBufferSupportState?: boolean;\r\n\r\n    /**\r\n     * Defines if the gl context should be released.\r\n     * It's false by default for backward compatibility, but you should probably pass true (see https://registry.khronos.org/webgl/extensions/WEBGL_lose_context/)\r\n     */\r\n    loseContextOnDispose?: boolean;\r\n}\r\n\r\n/**\r\n * The base engine class (root of all engines)\r\n */\r\nexport class ThinEngine {\r\n    private static _TempClearColorUint32 = new Uint32Array(4);\r\n    private static _TempClearColorInt32 = new Int32Array(4);\r\n\r\n    /** Use this array to turn off some WebGL2 features on known buggy browsers version */\r\n    public static ExceptionList = [\r\n        { key: \"Chrome/63.0\", capture: \"63\\\\.0\\\\.3239\\\\.(\\\\d+)\", captureConstraint: 108, targets: [\"uniformBuffer\"] },\r\n        { key: \"Firefox/58\", capture: null, captureConstraint: null, targets: [\"uniformBuffer\"] },\r\n        { key: \"Firefox/59\", capture: null, captureConstraint: null, targets: [\"uniformBuffer\"] },\r\n        { key: \"Chrome/72.+?Mobile\", capture: null, captureConstraint: null, targets: [\"vao\"] },\r\n        { key: \"Chrome/73.+?Mobile\", capture: null, captureConstraint: null, targets: [\"vao\"] },\r\n        { key: \"Chrome/74.+?Mobile\", capture: null, captureConstraint: null, targets: [\"vao\"] },\r\n        { key: \"Mac OS.+Chrome/71\", capture: null, captureConstraint: null, targets: [\"vao\"] },\r\n        { key: \"Mac OS.+Chrome/72\", capture: null, captureConstraint: null, targets: [\"vao\"] },\r\n        { key: \"Mac OS.+Chrome\", capture: null, captureConstraint: null, targets: [\"uniformBuffer\"] },\r\n        { key: \"Chrome/12\\\\d\\\\..+?Mobile\", capture: null, captureConstraint: null, targets: [\"uniformBuffer\"] },\r\n        // desktop osx safari 15.4\r\n        { key: \".*AppleWebKit.*(15.4).*Safari\", capture: null, captureConstraint: null, targets: [\"antialias\", \"maxMSAASamples\"] },\r\n        // mobile browsers using safari 15.4 on ios\r\n        { key: \".*(15.4).*AppleWebKit.*Safari\", capture: null, captureConstraint: null, targets: [\"antialias\", \"maxMSAASamples\"] },\r\n    ];\r\n\r\n    /** @internal */\r\n    public static _TextureLoaders: IInternalTextureLoader[] = [];\r\n\r\n    /**\r\n     * Returns the current npm package of the sdk\r\n     */\r\n    // Not mixed with Version for tooling purpose.\r\n    public static get NpmPackage(): string {\r\n        return \"babylonjs@6.49.0\";\r\n    }\r\n\r\n    /**\r\n     * Returns the current version of the framework\r\n     */\r\n    public static get Version(): string {\r\n        return \"6.49.0\";\r\n    }\r\n\r\n    /**\r\n     * Returns a string describing the current engine\r\n     */\r\n    public get description(): string {\r\n        let description = this.name + this.webGLVersion;\r\n\r\n        if (this._caps.parallelShaderCompile) {\r\n            description += \" - Parallel shader compilation\";\r\n        }\r\n\r\n        return description;\r\n    }\r\n\r\n    /** @internal */\r\n    protected _name = \"WebGL\";\r\n\r\n    /**\r\n     * Gets or sets the name of the engine\r\n     */\r\n    public get name(): string {\r\n        return this._name;\r\n    }\r\n\r\n    public set name(value: string) {\r\n        this._name = value;\r\n    }\r\n\r\n    /**\r\n     * Returns the version of the engine\r\n     */\r\n    public get version(): number {\r\n        return this._webGLVersion;\r\n    }\r\n\r\n    protected _isDisposed = false;\r\n\r\n    public get isDisposed(): boolean {\r\n        return this._isDisposed;\r\n    }\r\n\r\n    // Updatable statics so stick with vars here\r\n\r\n    /**\r\n     * Gets or sets the epsilon value used by collision engine\r\n     */\r\n    public static CollisionsEpsilon = 0.001;\r\n\r\n    /**\r\n     * Gets or sets the relative url used to load shaders if using the engine in non-minified mode\r\n     */\r\n    public static get ShadersRepository(): string {\r\n        return Effect.ShadersRepository;\r\n    }\r\n    public static set ShadersRepository(value: string) {\r\n        Effect.ShadersRepository = value;\r\n    }\r\n\r\n    protected _shaderProcessor: Nullable<IShaderProcessor>;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getShaderProcessor(shaderLanguage: ShaderLanguage): Nullable<IShaderProcessor> {\r\n        return this._shaderProcessor;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean that indicates if textures must be forced to power of 2 size even if not required\r\n     */\r\n    public forcePOTTextures = false;\r\n\r\n    /**\r\n     * Gets a boolean indicating if the engine is currently rendering in fullscreen mode\r\n     */\r\n    public isFullscreen = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if back faces must be culled. If false, front faces are culled instead (true by default)\r\n     * If non null, this takes precedence over the value from the material\r\n     */\r\n    public cullBackFaces: Nullable<boolean> = null;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the engine must keep rendering even if the window is not in foreground\r\n     */\r\n    public renderEvenInBackground = true;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that cache can be kept between frames\r\n     */\r\n    public preventCacheWipeBetweenFrames = false;\r\n\r\n    /** Gets or sets a boolean indicating if the engine should validate programs after compilation */\r\n    public validateShaderPrograms = false;\r\n\r\n    private _useReverseDepthBuffer = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if depth buffer should be reverse, going from far to near.\r\n     * This can provide greater z depth for distant objects.\r\n     */\r\n    public get useReverseDepthBuffer(): boolean {\r\n        return this._useReverseDepthBuffer;\r\n    }\r\n\r\n    public set useReverseDepthBuffer(useReverse) {\r\n        if (useReverse === this._useReverseDepthBuffer) {\r\n            return;\r\n        }\r\n\r\n        this._useReverseDepthBuffer = useReverse;\r\n\r\n        if (useReverse) {\r\n            this._depthCullingState.depthFunc = Constants.GEQUAL;\r\n        } else {\r\n            this._depthCullingState.depthFunc = Constants.LEQUAL;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Indicates if the z range in NDC space is 0..1 (value: true) or -1..1 (value: false)\r\n     */\r\n    public readonly isNDCHalfZRange: boolean = false;\r\n\r\n    /**\r\n     * Indicates that the origin of the texture/framebuffer space is the bottom left corner. If false, the origin is top left\r\n     */\r\n    public readonly hasOriginBottomLeft: boolean = true;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that uniform buffers must be disabled even if they are supported\r\n     */\r\n    public disableUniformBuffers = false;\r\n\r\n    /**\r\n     * An event triggered when the engine is disposed.\r\n     */\r\n    public readonly onDisposeObservable = new Observable<ThinEngine>();\r\n\r\n    private _frameId = 0;\r\n    /**\r\n     * Gets the current frame id\r\n     */\r\n    public get frameId(): number {\r\n        return this._frameId;\r\n    }\r\n\r\n    /**\r\n     * The time (in milliseconds elapsed since the current page has been loaded) when the engine was initialized\r\n     */\r\n    public readonly startTime: number;\r\n\r\n    /** @internal */\r\n    public _uniformBuffers = new Array<UniformBuffer>();\r\n    /** @internal */\r\n    public _storageBuffers = new Array<StorageBuffer>();\r\n\r\n    /**\r\n     * Gets a boolean indicating that the engine supports uniform buffers\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n     */\r\n    public get supportsUniformBuffers(): boolean {\r\n        return this.webGLVersion > 1 && !this.disableUniformBuffers;\r\n    }\r\n\r\n    // Private Members\r\n\r\n    /** @internal */\r\n    public _gl: WebGL2RenderingContext;\r\n    /** @internal */\r\n    public _webGLVersion = 1.0;\r\n    protected _renderingCanvas: Nullable<HTMLCanvasElement>;\r\n    protected _windowIsBackground = false;\r\n    protected _creationOptions: EngineOptions;\r\n    protected _audioContext: Nullable<AudioContext>;\r\n    protected _audioDestination: Nullable<AudioDestinationNode | MediaStreamAudioDestinationNode>;\r\n    /** @internal */\r\n    public _glSRGBExtensionValues: {\r\n        SRGB: typeof WebGL2RenderingContext.SRGB;\r\n        SRGB8: typeof WebGL2RenderingContext.SRGB8 | EXT_sRGB[\"SRGB_ALPHA_EXT\"];\r\n        SRGB8_ALPHA8: typeof WebGL2RenderingContext.SRGB8_ALPHA8 | EXT_sRGB[\"SRGB_ALPHA_EXT\"];\r\n    };\r\n    /**\r\n     * Gets the options used for engine creation\r\n     * @returns EngineOptions object\r\n     */\r\n    public getCreationOptions() {\r\n        return this._creationOptions;\r\n    }\r\n\r\n    protected _highPrecisionShadersAllowed = true;\r\n    /** @internal */\r\n    public get _shouldUseHighPrecisionShader(): boolean {\r\n        return !!(this._caps.highPrecisionShaderSupported && this._highPrecisionShadersAllowed);\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that only power of 2 textures are supported\r\n     * Please note that you can still use non power of 2 textures but in this case the engine will forcefully convert them\r\n     */\r\n    public get needPOTTextures(): boolean {\r\n        return this._webGLVersion < 2 || this.forcePOTTextures;\r\n    }\r\n\r\n    /** @internal */\r\n    public _badOS = false;\r\n\r\n    /** @internal */\r\n    public _badDesktopOS = false;\r\n\r\n    /** @internal */\r\n    public _hardwareScalingLevel: number;\r\n    /** @internal */\r\n    public _caps: EngineCapabilities;\r\n    /** @internal */\r\n    public _features: EngineFeatures;\r\n    protected _isStencilEnable: boolean;\r\n\r\n    private _glVersion: string;\r\n    private _glRenderer: string;\r\n    private _glVendor: string;\r\n\r\n    /** @internal */\r\n    public _videoTextureSupported: boolean;\r\n\r\n    protected _activeRenderLoops = new Array<() => void>();\r\n\r\n    /**\r\n     * Gets the list of current active render loop functions\r\n     * @returns a read only array with the current render loop functions\r\n     */\r\n    public get activeRenderLoops(): ReadonlyArray<() => void> {\r\n        return this._activeRenderLoops;\r\n    }\r\n\r\n    // Lost context\r\n    /**\r\n     * Observable signaled when a context lost event is raised\r\n     */\r\n    public onContextLostObservable = new Observable<ThinEngine>();\r\n    /**\r\n     * Observable signaled when a context restored event is raised\r\n     */\r\n    public onContextRestoredObservable = new Observable<ThinEngine>();\r\n    private _onContextLost: (evt: Event) => void;\r\n    private _onContextRestored: (evt: Event) => void;\r\n    protected _contextWasLost = false;\r\n\r\n    /** @internal */\r\n    public _doNotHandleContextLost = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if resources should be retained to be able to handle context lost events\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimize_your_scene#handling-webgl-context-lost\r\n     */\r\n    public get doNotHandleContextLost(): boolean {\r\n        return this._doNotHandleContextLost;\r\n    }\r\n\r\n    public set doNotHandleContextLost(value: boolean) {\r\n        this._doNotHandleContextLost = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that vertex array object must be disabled even if they are supported\r\n     */\r\n    public disableVertexArrayObjects = false;\r\n\r\n    // States\r\n    /** @internal */\r\n    protected _colorWrite = true;\r\n    /** @internal */\r\n    protected _colorWriteChanged = true;\r\n    /** @internal */\r\n    protected _depthCullingState = new DepthCullingState();\r\n    /** @internal */\r\n    protected _stencilStateComposer = new StencilStateComposer();\r\n    /** @internal */\r\n    protected _stencilState = new StencilState();\r\n    /** @internal */\r\n    public _alphaState = new AlphaState();\r\n    /** @internal */\r\n    public _alphaMode = Constants.ALPHA_ADD;\r\n    /** @internal */\r\n    public _alphaEquation = Constants.ALPHA_DISABLE;\r\n\r\n    // Cache\r\n    /** @internal */\r\n    public _internalTexturesCache = new Array<InternalTexture>();\r\n    /** @internal */\r\n    public _renderTargetWrapperCache = new Array<RenderTargetWrapper>();\r\n    /** @internal */\r\n    protected _activeChannel = 0;\r\n    private _currentTextureChannel = -1;\r\n    /** @internal */\r\n    protected _boundTexturesCache: { [key: string]: Nullable<InternalTexture> } = {};\r\n    protected _currentEffect: Nullable<Effect>;\r\n    /** @internal */\r\n    public _currentDrawContext: IDrawContext;\r\n    /** @internal */\r\n    public _currentMaterialContext: IMaterialContext;\r\n    /** @internal */\r\n    protected _currentProgram: Nullable<WebGLProgram>;\r\n    protected _compiledEffects: { [key: string]: Effect } = {};\r\n    private _vertexAttribArraysEnabled: boolean[] = [];\r\n    /** @internal */\r\n    protected _cachedViewport: Nullable<IViewportLike>;\r\n    private _cachedVertexArrayObject: Nullable<WebGLVertexArrayObject>;\r\n    /** @internal */\r\n    protected _cachedVertexBuffers: any;\r\n    /** @internal */\r\n    protected _cachedIndexBuffer: Nullable<DataBuffer>;\r\n    /** @internal */\r\n    protected _cachedEffectForVertexBuffers: Nullable<Effect>;\r\n\r\n    /** @internal */\r\n    public _currentRenderTarget: Nullable<RenderTargetWrapper> = null;\r\n    private _uintIndicesCurrentlySet = false;\r\n    protected _currentBoundBuffer = new Array<Nullable<DataBuffer>>();\r\n    /** @internal */\r\n    public _currentFramebuffer: Nullable<WebGLFramebuffer> = null;\r\n    /** @internal */\r\n    public _dummyFramebuffer: Nullable<WebGLFramebuffer> = null;\r\n    private _currentBufferPointers = new Array<BufferPointer>();\r\n    private _currentInstanceLocations = new Array<number>();\r\n    private _currentInstanceBuffers = new Array<DataBuffer>();\r\n    private _textureUnits: Int32Array;\r\n\r\n    /** @internal */\r\n    public _workingCanvas: Nullable<ICanvas>;\r\n    /** @internal */\r\n    public _workingContext: Nullable<ICanvasRenderingContext>;\r\n\r\n    /** @internal */\r\n    public _boundRenderFunction: any = () => this._renderLoop();\r\n\r\n    private _vaoRecordInProgress = false;\r\n    private _mustWipeVertexAttributes = false;\r\n\r\n    private _emptyTexture: Nullable<InternalTexture>;\r\n    private _emptyCubeTexture: Nullable<InternalTexture>;\r\n    private _emptyTexture3D: Nullable<InternalTexture>;\r\n    private _emptyTexture2DArray: Nullable<InternalTexture>;\r\n\r\n    /** @internal */\r\n    public _frameHandler: number = 0;\r\n\r\n    private _nextFreeTextureSlots = new Array<number>();\r\n    private _maxSimultaneousTextures = 0;\r\n    private _maxMSAASamplesOverride: Nullable<number> = null;\r\n\r\n    private _activeRequests = new Array<IFileRequest>();\r\n\r\n    /**\r\n     * If set to true zooming in and out in the browser will rescale the hardware-scaling correctly.\r\n     */\r\n    public adaptToDeviceRatio: boolean = false;\r\n    /** @internal */\r\n    protected _lastDevicePixelRatio: number = 1.0;\r\n\r\n    /** @internal */\r\n    public _transformTextureUrl: Nullable<(url: string) => string> = null;\r\n\r\n    /**\r\n     * Gets information about the current host\r\n     */\r\n    public hostInformation: HostInformation = {\r\n        isMobile: false,\r\n    };\r\n\r\n    protected get _supportsHardwareTextureRescaling() {\r\n        return false;\r\n    }\r\n\r\n    protected _framebufferDimensionsObject: Nullable<{ framebufferWidth: number; framebufferHeight: number }>;\r\n\r\n    /**\r\n     * sets the object from which width and height will be taken from when getting render width and height\r\n     * Will fallback to the gl object\r\n     * @param dimensions the framebuffer width and height that will be used.\r\n     */\r\n    public set framebufferDimensionsObject(dimensions: Nullable<{ framebufferWidth: number; framebufferHeight: number }>) {\r\n        this._framebufferDimensionsObject = dimensions;\r\n    }\r\n\r\n    /**\r\n     * Gets the current viewport\r\n     */\r\n    public get currentViewport(): Nullable<IViewportLike> {\r\n        return this._cachedViewport;\r\n    }\r\n\r\n    /**\r\n     * Gets the default empty texture\r\n     */\r\n    public get emptyTexture(): InternalTexture {\r\n        if (!this._emptyTexture) {\r\n            this._emptyTexture = this.createRawTexture(new Uint8Array(4), 1, 1, Constants.TEXTUREFORMAT_RGBA, false, false, Constants.TEXTURE_NEAREST_SAMPLINGMODE);\r\n        }\r\n\r\n        return this._emptyTexture;\r\n    }\r\n\r\n    /**\r\n     * Gets the default empty 3D texture\r\n     */\r\n    public get emptyTexture3D(): InternalTexture {\r\n        if (!this._emptyTexture3D) {\r\n            this._emptyTexture3D = this.createRawTexture3D(new Uint8Array(4), 1, 1, 1, Constants.TEXTUREFORMAT_RGBA, false, false, Constants.TEXTURE_NEAREST_SAMPLINGMODE);\r\n        }\r\n\r\n        return this._emptyTexture3D;\r\n    }\r\n\r\n    /**\r\n     * Gets the default empty 2D array texture\r\n     */\r\n    public get emptyTexture2DArray(): InternalTexture {\r\n        if (!this._emptyTexture2DArray) {\r\n            this._emptyTexture2DArray = this.createRawTexture2DArray(\r\n                new Uint8Array(4),\r\n                1,\r\n                1,\r\n                1,\r\n                Constants.TEXTUREFORMAT_RGBA,\r\n                false,\r\n                false,\r\n                Constants.TEXTURE_NEAREST_SAMPLINGMODE\r\n            );\r\n        }\r\n\r\n        return this._emptyTexture2DArray;\r\n    }\r\n\r\n    /**\r\n     * Gets the default empty cube texture\r\n     */\r\n    public get emptyCubeTexture(): InternalTexture {\r\n        if (!this._emptyCubeTexture) {\r\n            const faceData = new Uint8Array(4);\r\n            const cubeData = [faceData, faceData, faceData, faceData, faceData, faceData];\r\n            this._emptyCubeTexture = this.createRawCubeTexture(\r\n                cubeData,\r\n                1,\r\n                Constants.TEXTUREFORMAT_RGBA,\r\n                Constants.TEXTURETYPE_UNSIGNED_INT,\r\n                false,\r\n                false,\r\n                Constants.TEXTURE_NEAREST_SAMPLINGMODE\r\n            );\r\n        }\r\n\r\n        return this._emptyCubeTexture;\r\n    }\r\n\r\n    /**\r\n     * Defines whether the engine has been created with the premultipliedAlpha option on or not.\r\n     */\r\n    public premultipliedAlpha: boolean = true;\r\n\r\n    /**\r\n     * Observable event triggered before each texture is initialized\r\n     */\r\n    public onBeforeTextureInitObservable = new Observable<Texture>();\r\n\r\n    /** @internal */\r\n    protected _isWebGPU: boolean = false;\r\n    /**\r\n     * Gets a boolean indicating if the engine runs in WebGPU or not.\r\n     */\r\n    public get isWebGPU(): boolean {\r\n        return this._isWebGPU;\r\n    }\r\n\r\n    /** @internal */\r\n    protected _shaderPlatformName: string;\r\n    /**\r\n     * Gets the shader platform name used by the effects.\r\n     */\r\n    public get shaderPlatformName(): string {\r\n        return this._shaderPlatformName;\r\n    }\r\n\r\n    /**\r\n     * Enables or disables the snapshot rendering mode\r\n     * Note that the WebGL engine does not support snapshot rendering so setting the value won't have any effect for this engine\r\n     */\r\n    public get snapshotRendering(): boolean {\r\n        return false;\r\n    }\r\n\r\n    public set snapshotRendering(activate) {\r\n        // WebGL engine does not support snapshot rendering\r\n    }\r\n\r\n    protected _snapshotRenderingMode = Constants.SNAPSHOTRENDERING_STANDARD;\r\n    /**\r\n     * Gets or sets the snapshot rendering mode\r\n     */\r\n    public get snapshotRenderingMode(): number {\r\n        return this._snapshotRenderingMode;\r\n    }\r\n\r\n    public set snapshotRenderingMode(mode: number) {\r\n        this._snapshotRenderingMode = mode;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the exact sRGB conversions or faster approximations are used for converting to and from linear space.\r\n     */\r\n    public readonly useExactSrgbConversions: boolean;\r\n\r\n    /**\r\n     * Creates a new snapshot at the next frame using the current snapshotRenderingMode\r\n     */\r\n    public snapshotRenderingReset(): void {\r\n        this.snapshotRendering = false;\r\n    }\r\n\r\n    private _checkForMobile: () => void;\r\n\r\n    private static _CreateCanvas(width: number, height: number): ICanvas {\r\n        if (typeof document === \"undefined\") {\r\n            return <ICanvas>(<any>new OffscreenCanvas(width, height));\r\n        }\r\n        const canvas = <ICanvas>(<any>document.createElement(\"canvas\"));\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n        return canvas;\r\n    }\r\n\r\n    /**\r\n     * Create a canvas. This method is overridden by other engines\r\n     * @param width width\r\n     * @param height height\r\n     * @returns ICanvas interface\r\n     */\r\n    public createCanvas(width: number, height: number): ICanvas {\r\n        return ThinEngine._CreateCanvas(width, height);\r\n    }\r\n\r\n    /**\r\n     * Create an image to use with canvas\r\n     * @returns IImage interface\r\n     */\r\n    public createCanvasImage(): IImage {\r\n        return document.createElement(\"img\");\r\n    }\r\n\r\n    /**\r\n     * Creates a new engine\r\n     * @param canvasOrContext defines the canvas or WebGL context to use for rendering. If you provide a WebGL context, Babylon.js will not hook events on the canvas (like pointers, keyboards, etc...) so no event observables will be available. This is mostly used when Babylon.js is used as a plugin on a system which already used the WebGL context\r\n     * @param antialias defines enable antialiasing (default: false)\r\n     * @param options defines further options to be sent to the getContext() function\r\n     * @param adaptToDeviceRatio defines whether to adapt to the device's viewport characteristics (default: false)\r\n     */\r\n    constructor(\r\n        canvasOrContext: Nullable<HTMLCanvasElement | OffscreenCanvas | WebGLRenderingContext | WebGL2RenderingContext>,\r\n        antialias?: boolean,\r\n        options?: EngineOptions,\r\n        adaptToDeviceRatio?: boolean\r\n    ) {\r\n        this.startTime = PrecisionDate.Now;\r\n\r\n        let canvas: Nullable<HTMLCanvasElement> = null;\r\n\r\n        options = options || {};\r\n\r\n        this._creationOptions = options;\r\n\r\n        // Save this off for use in resize().\r\n        this.adaptToDeviceRatio = adaptToDeviceRatio ?? false;\r\n\r\n        this._stencilStateComposer.stencilGlobal = this._stencilState;\r\n\r\n        PerformanceConfigurator.SetMatrixPrecision(!!options.useHighPrecisionMatrix);\r\n\r\n        options.antialias = antialias ?? options.antialias;\r\n        options.deterministicLockstep = options.deterministicLockstep ?? false;\r\n        options.lockstepMaxSteps = options.lockstepMaxSteps ?? 4;\r\n        options.timeStep = options.timeStep ?? 1 / 60;\r\n        options.audioEngine = options.audioEngine ?? true;\r\n        options.stencil = options.stencil ?? true;\r\n\r\n        this._audioContext = options.audioEngineOptions?.audioContext ?? null;\r\n        this._audioDestination = options.audioEngineOptions?.audioDestination ?? null;\r\n        this.premultipliedAlpha = options.premultipliedAlpha ?? true;\r\n        this.useExactSrgbConversions = options.useExactSrgbConversions ?? false;\r\n        this._doNotHandleContextLost = !!options.doNotHandleContextLost;\r\n        this._isStencilEnable = options.stencil ? true : false;\r\n\r\n        // Viewport\r\n        adaptToDeviceRatio = adaptToDeviceRatio || options.adaptToDeviceRatio || false;\r\n\r\n        const devicePixelRatio = IsWindowObjectExist() ? window.devicePixelRatio || 1.0 : 1.0;\r\n\r\n        const limitDeviceRatio = options.limitDeviceRatio || devicePixelRatio;\r\n        this._hardwareScalingLevel = adaptToDeviceRatio ? 1.0 / Math.min(limitDeviceRatio, devicePixelRatio) : 1.0;\r\n        this._lastDevicePixelRatio = devicePixelRatio;\r\n\r\n        if (!canvasOrContext) {\r\n            return;\r\n        }\r\n\r\n        if ((canvasOrContext as any).getContext) {\r\n            canvas = <HTMLCanvasElement>canvasOrContext;\r\n            this._renderingCanvas = canvas;\r\n\r\n            if (options.preserveDrawingBuffer === undefined) {\r\n                options.preserveDrawingBuffer = false;\r\n            }\r\n\r\n            if (options.xrCompatible === undefined) {\r\n                options.xrCompatible = true;\r\n            }\r\n\r\n            // Exceptions\r\n            if (navigator && navigator.userAgent) {\r\n                this._setupMobileChecks();\r\n\r\n                const ua = navigator.userAgent;\r\n                for (const exception of ThinEngine.ExceptionList) {\r\n                    const key = exception.key;\r\n                    const targets = exception.targets;\r\n                    const check = new RegExp(key);\r\n\r\n                    if (check.test(ua)) {\r\n                        if (exception.capture && exception.captureConstraint) {\r\n                            const capture = exception.capture;\r\n                            const constraint = exception.captureConstraint;\r\n\r\n                            const regex = new RegExp(capture);\r\n                            const matches = regex.exec(ua);\r\n\r\n                            if (matches && matches.length > 0) {\r\n                                const capturedValue = parseInt(matches[matches.length - 1]);\r\n                                if (capturedValue >= constraint) {\r\n                                    continue;\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        for (const target of targets) {\r\n                            switch (target) {\r\n                                case \"uniformBuffer\":\r\n                                    this.disableUniformBuffers = true;\r\n                                    break;\r\n                                case \"vao\":\r\n                                    this.disableVertexArrayObjects = true;\r\n                                    break;\r\n                                case \"antialias\":\r\n                                    options.antialias = false;\r\n                                    break;\r\n                                case \"maxMSAASamples\":\r\n                                    this._maxMSAASamplesOverride = 1;\r\n                                    break;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Context lost\r\n            if (!this._doNotHandleContextLost) {\r\n                this._onContextLost = (evt: Event) => {\r\n                    evt.preventDefault();\r\n                    this._contextWasLost = true;\r\n                    Logger.Warn(\"WebGL context lost.\");\r\n\r\n                    this.onContextLostObservable.notifyObservers(this);\r\n                };\r\n\r\n                this._onContextRestored = () => {\r\n                    this._restoreEngineAfterContextLost(() => this._initGLContext());\r\n                };\r\n\r\n                canvas.addEventListener(\"webglcontextlost\", this._onContextLost, false);\r\n                canvas.addEventListener(\"webglcontextrestored\", this._onContextRestored, false);\r\n\r\n                options.powerPreference = options.powerPreference || \"high-performance\";\r\n            }\r\n\r\n            // Detect if we are running on a faulty buggy desktop OS.\r\n            this._badDesktopOS = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\r\n            if (this._badDesktopOS) {\r\n                options.xrCompatible = false;\r\n            }\r\n\r\n            // GL\r\n            if (!options.disableWebGL2Support) {\r\n                try {\r\n                    this._gl = <any>(canvas.getContext(\"webgl2\", options) || canvas.getContext(\"experimental-webgl2\", options));\r\n                    if (this._gl) {\r\n                        this._webGLVersion = 2.0;\r\n                        this._shaderPlatformName = \"WEBGL2\";\r\n\r\n                        // Prevent weird browsers to lie (yeah that happens!)\r\n                        if (!this._gl.deleteQuery) {\r\n                            this._webGLVersion = 1.0;\r\n                            this._shaderPlatformName = \"WEBGL1\";\r\n                        }\r\n                    }\r\n                } catch (e) {\r\n                    // Do nothing\r\n                }\r\n            }\r\n\r\n            if (!this._gl) {\r\n                if (!canvas) {\r\n                    throw new Error(\"The provided canvas is null or undefined.\");\r\n                }\r\n                try {\r\n                    this._gl = <WebGL2RenderingContext>(canvas.getContext(\"webgl\", options) || canvas.getContext(\"experimental-webgl\", options));\r\n                } catch (e) {\r\n                    throw new Error(\"WebGL not supported\");\r\n                }\r\n            }\r\n\r\n            if (!this._gl) {\r\n                throw new Error(\"WebGL not supported\");\r\n            }\r\n        } else {\r\n            this._gl = <WebGL2RenderingContext>canvasOrContext;\r\n            this._renderingCanvas = this._gl.canvas as HTMLCanvasElement;\r\n\r\n            if ((this._gl as any).renderbufferStorageMultisample) {\r\n                this._webGLVersion = 2.0;\r\n                this._shaderPlatformName = \"WEBGL2\";\r\n            } else {\r\n                this._shaderPlatformName = \"WEBGL1\";\r\n            }\r\n\r\n            const attributes = this._gl.getContextAttributes();\r\n            if (attributes) {\r\n                options.stencil = attributes.stencil;\r\n            }\r\n        }\r\n\r\n        // Ensures a consistent color space unpacking of textures cross browser.\r\n        this._gl.pixelStorei(this._gl.UNPACK_COLORSPACE_CONVERSION_WEBGL, this._gl.NONE);\r\n\r\n        if (options.useHighPrecisionFloats !== undefined) {\r\n            this._highPrecisionShadersAllowed = options.useHighPrecisionFloats;\r\n        }\r\n\r\n        this.resize();\r\n\r\n        this._initGLContext();\r\n        this._initFeatures();\r\n\r\n        // Prepare buffer pointers\r\n        for (let i = 0; i < this._caps.maxVertexAttribs; i++) {\r\n            this._currentBufferPointers[i] = new BufferPointer();\r\n        }\r\n\r\n        // Shader processor\r\n        this._shaderProcessor = this.webGLVersion > 1 ? new WebGL2ShaderProcessor() : new WebGLShaderProcessor();\r\n\r\n        // Detect if we are running on a faulty buggy OS.\r\n        this._badOS = /iPad/i.test(navigator.userAgent) || /iPhone/i.test(navigator.userAgent);\r\n\r\n        // Starting with iOS 14, we can trust the browser\r\n        // let matches = navigator.userAgent.match(/Version\\/(\\d+)/);\r\n\r\n        // if (matches && matches.length === 2) {\r\n        //     if (parseInt(matches[1]) >= 14) {\r\n        //         this._badOS = false;\r\n        //     }\r\n        // }\r\n\r\n        const versionToLog = `Babylon.js v${ThinEngine.Version}`;\r\n        Logger.Log(versionToLog + ` - ${this.description}`);\r\n\r\n        // Check setAttribute in case of workers\r\n        if (this._renderingCanvas && this._renderingCanvas.setAttribute) {\r\n            this._renderingCanvas.setAttribute(\"data-engine\", versionToLog);\r\n        }\r\n    }\r\n\r\n    protected _setupMobileChecks(): void {\r\n        if (!(navigator && navigator.userAgent)) {\r\n            return;\r\n        }\r\n\r\n        // Function to check if running on mobile device\r\n        this._checkForMobile = () => {\r\n            const currentUA = navigator.userAgent;\r\n            this.hostInformation.isMobile =\r\n                currentUA.indexOf(\"Mobile\") !== -1 ||\r\n                // Needed for iOS 13+ detection on iPad (inspired by solution from https://stackoverflow.com/questions/9038625/detect-if-device-is-ios)\r\n                (currentUA.indexOf(\"Mac\") !== -1 && IsDocumentAvailable() && \"ontouchend\" in document);\r\n        };\r\n\r\n        // Set initial isMobile value\r\n        this._checkForMobile();\r\n\r\n        // Set up event listener to check when window is resized (used to get emulator activation to work properly)\r\n        if (IsWindowObjectExist()) {\r\n            window.addEventListener(\"resize\", this._checkForMobile);\r\n        }\r\n    }\r\n\r\n    protected _clearEmptyResources(): void {\r\n        this._dummyFramebuffer = null;\r\n        this._emptyTexture = null;\r\n        this._emptyCubeTexture = null;\r\n        this._emptyTexture3D = null;\r\n        this._emptyTexture2DArray = null;\r\n    }\r\n\r\n    protected _rebuildGraphicsResources(): void {\r\n        // Ensure webgl and engine states are matching\r\n        this.wipeCaches(true);\r\n\r\n        // Rebuild effects\r\n        this._rebuildEffects();\r\n        this._rebuildComputeEffects?.();\r\n\r\n        // Note:\r\n        //  The call to _rebuildBuffers must be made before the call to _rebuildInternalTextures because in the process of _rebuildBuffers the buffers used by the post process managers will be rebuilt\r\n        //  and we may need to use the post process manager of the scene during _rebuildInternalTextures (in WebGL1, non-POT textures are rescaled using a post process + post process manager of the scene)\r\n\r\n        // Rebuild buffers\r\n        this._rebuildBuffers();\r\n        // Rebuild textures\r\n        this._rebuildInternalTextures();\r\n        // Rebuild textures\r\n        this._rebuildTextures();\r\n        // Rebuild textures\r\n        this._rebuildRenderTargetWrappers();\r\n\r\n        // Reset engine states after all the buffer/textures/... have been rebuilt\r\n        this.wipeCaches(true);\r\n    }\r\n\r\n    protected _flagContextRestored(): void {\r\n        Logger.Warn(this.name + \" context successfully restored.\");\r\n        this.onContextRestoredObservable.notifyObservers(this);\r\n        this._contextWasLost = false;\r\n    }\r\n\r\n    protected _restoreEngineAfterContextLost(initEngine: () => void): void {\r\n        // Adding a timeout to avoid race condition at browser level\r\n        setTimeout(async () => {\r\n            this._clearEmptyResources();\r\n\r\n            const depthTest = this._depthCullingState.depthTest; // backup those values because the call to initEngine / wipeCaches will reset them\r\n            const depthFunc = this._depthCullingState.depthFunc;\r\n            const depthMask = this._depthCullingState.depthMask;\r\n            const stencilTest = this._stencilState.stencilTest;\r\n\r\n            // Rebuild context\r\n            await initEngine();\r\n            this._rebuildGraphicsResources();\r\n\r\n            this._depthCullingState.depthTest = depthTest;\r\n            this._depthCullingState.depthFunc = depthFunc;\r\n            this._depthCullingState.depthMask = depthMask;\r\n            this._stencilState.stencilTest = stencilTest;\r\n\r\n            this._flagContextRestored();\r\n        }, 0);\r\n    }\r\n\r\n    /**\r\n     * Shared initialization across engines types.\r\n     * @param canvas The canvas associated with this instance of the engine.\r\n     */\r\n    protected _sharedInit(canvas: HTMLCanvasElement) {\r\n        this._renderingCanvas = canvas;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getShaderProcessingContext(shaderLanguage: ShaderLanguage): Nullable<ShaderProcessingContext> {\r\n        return null;\r\n    }\r\n\r\n    private _rebuildInternalTextures(): void {\r\n        const currentState = this._internalTexturesCache.slice(); // Do a copy because the rebuild will add proxies\r\n\r\n        for (const internalTexture of currentState) {\r\n            internalTexture._rebuild();\r\n        }\r\n    }\r\n\r\n    private _rebuildRenderTargetWrappers(): void {\r\n        const currentState = this._renderTargetWrapperCache.slice(); // Do a copy because the rebuild will add proxies\r\n\r\n        for (const renderTargetWrapper of currentState) {\r\n            renderTargetWrapper._rebuild();\r\n        }\r\n    }\r\n\r\n    private _rebuildEffects(): void {\r\n        for (const key in this._compiledEffects) {\r\n            const effect = <Effect>this._compiledEffects[key];\r\n\r\n            effect._pipelineContext = null; // because _prepareEffect will try to dispose this pipeline before recreating it and that would lead to webgl errors\r\n            effect._prepareEffect();\r\n        }\r\n\r\n        Effect.ResetCache();\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if all created effects are ready\r\n     * @returns true if all effects are ready\r\n     */\r\n    public areAllEffectsReady(): boolean {\r\n        for (const key in this._compiledEffects) {\r\n            const effect = <Effect>this._compiledEffects[key];\r\n\r\n            if (!effect.isReady()) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    protected _rebuildBuffers(): void {\r\n        // Uniforms\r\n        for (const uniformBuffer of this._uniformBuffers) {\r\n            uniformBuffer._rebuildAfterContextLost();\r\n        }\r\n    }\r\n\r\n    protected _rebuildTextures(): void {}\r\n\r\n    protected _initGLContext(): void {\r\n        // Caps\r\n        this._caps = {\r\n            maxTexturesImageUnits: this._gl.getParameter(this._gl.MAX_TEXTURE_IMAGE_UNITS),\r\n            maxCombinedTexturesImageUnits: this._gl.getParameter(this._gl.MAX_COMBINED_TEXTURE_IMAGE_UNITS),\r\n            maxVertexTextureImageUnits: this._gl.getParameter(this._gl.MAX_VERTEX_TEXTURE_IMAGE_UNITS),\r\n            maxTextureSize: this._gl.getParameter(this._gl.MAX_TEXTURE_SIZE),\r\n            maxSamples: this._webGLVersion > 1 ? this._gl.getParameter(this._gl.MAX_SAMPLES) : 1,\r\n            maxCubemapTextureSize: this._gl.getParameter(this._gl.MAX_CUBE_MAP_TEXTURE_SIZE),\r\n            maxRenderTextureSize: this._gl.getParameter(this._gl.MAX_RENDERBUFFER_SIZE),\r\n            maxVertexAttribs: this._gl.getParameter(this._gl.MAX_VERTEX_ATTRIBS),\r\n            maxVaryingVectors: this._gl.getParameter(this._gl.MAX_VARYING_VECTORS),\r\n            maxFragmentUniformVectors: this._gl.getParameter(this._gl.MAX_FRAGMENT_UNIFORM_VECTORS),\r\n            maxVertexUniformVectors: this._gl.getParameter(this._gl.MAX_VERTEX_UNIFORM_VECTORS),\r\n            parallelShaderCompile: this._gl.getExtension(\"KHR_parallel_shader_compile\") || undefined,\r\n            standardDerivatives: this._webGLVersion > 1 || this._gl.getExtension(\"OES_standard_derivatives\") !== null,\r\n            maxAnisotropy: 1,\r\n            astc: this._gl.getExtension(\"WEBGL_compressed_texture_astc\") || this._gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_astc\"),\r\n            bptc: this._gl.getExtension(\"EXT_texture_compression_bptc\") || this._gl.getExtension(\"WEBKIT_EXT_texture_compression_bptc\"),\r\n            s3tc: this._gl.getExtension(\"WEBGL_compressed_texture_s3tc\") || this._gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_s3tc\"),\r\n            // eslint-disable-next-line @typescript-eslint/naming-convention\r\n            s3tc_srgb: this._gl.getExtension(\"WEBGL_compressed_texture_s3tc_srgb\") || this._gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_s3tc_srgb\"),\r\n            pvrtc: this._gl.getExtension(\"WEBGL_compressed_texture_pvrtc\") || this._gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_pvrtc\"),\r\n            etc1: this._gl.getExtension(\"WEBGL_compressed_texture_etc1\") || this._gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_etc1\"),\r\n            etc2:\r\n                this._gl.getExtension(\"WEBGL_compressed_texture_etc\") ||\r\n                this._gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_etc\") ||\r\n                this._gl.getExtension(\"WEBGL_compressed_texture_es3_0\"), // also a requirement of OpenGL ES 3\r\n            textureAnisotropicFilterExtension:\r\n                this._gl.getExtension(\"EXT_texture_filter_anisotropic\") ||\r\n                this._gl.getExtension(\"WEBKIT_EXT_texture_filter_anisotropic\") ||\r\n                this._gl.getExtension(\"MOZ_EXT_texture_filter_anisotropic\"),\r\n            uintIndices: this._webGLVersion > 1 || this._gl.getExtension(\"OES_element_index_uint\") !== null,\r\n            fragmentDepthSupported: this._webGLVersion > 1 || this._gl.getExtension(\"EXT_frag_depth\") !== null,\r\n            highPrecisionShaderSupported: false,\r\n            timerQuery: this._gl.getExtension(\"EXT_disjoint_timer_query_webgl2\") || this._gl.getExtension(\"EXT_disjoint_timer_query\"),\r\n            supportOcclusionQuery: this._webGLVersion > 1,\r\n            canUseTimestampForTimerQuery: false,\r\n            drawBuffersExtension: false,\r\n            maxMSAASamples: 1,\r\n            colorBufferFloat: !!(this._webGLVersion > 1 && this._gl.getExtension(\"EXT_color_buffer_float\")),\r\n            supportFloatTexturesResolve: false,\r\n            rg11b10ufColorRenderable: false,\r\n            colorBufferHalfFloat: !!(this._webGLVersion > 1 && this._gl.getExtension(\"EXT_color_buffer_half_float\")),\r\n            textureFloat: this._webGLVersion > 1 || this._gl.getExtension(\"OES_texture_float\") ? true : false,\r\n            textureHalfFloat: this._webGLVersion > 1 || this._gl.getExtension(\"OES_texture_half_float\") ? true : false,\r\n            textureHalfFloatRender: false,\r\n            textureFloatLinearFiltering: false,\r\n            textureFloatRender: false,\r\n            textureHalfFloatLinearFiltering: false,\r\n            vertexArrayObject: false,\r\n            instancedArrays: false,\r\n            textureLOD: this._webGLVersion > 1 || this._gl.getExtension(\"EXT_shader_texture_lod\") ? true : false,\r\n            texelFetch: this._webGLVersion !== 1,\r\n            blendMinMax: false,\r\n            multiview: this._gl.getExtension(\"OVR_multiview2\"),\r\n            oculusMultiview: this._gl.getExtension(\"OCULUS_multiview\"),\r\n            depthTextureExtension: false,\r\n            canUseGLInstanceID: this._webGLVersion > 1,\r\n            canUseGLVertexID: this._webGLVersion > 1,\r\n            supportComputeShaders: false,\r\n            supportSRGBBuffers: false,\r\n            supportTransformFeedbacks: this._webGLVersion > 1,\r\n            textureMaxLevel: this._webGLVersion > 1,\r\n            texture2DArrayMaxLayerCount: this._webGLVersion > 1 ? this._gl.getParameter(this._gl.MAX_ARRAY_TEXTURE_LAYERS) : 128,\r\n            disableMorphTargetTexture: false,\r\n        };\r\n\r\n        this._caps.supportFloatTexturesResolve = this._caps.colorBufferFloat;\r\n        this._caps.rg11b10ufColorRenderable = this._caps.colorBufferFloat;\r\n\r\n        // Infos\r\n        this._glVersion = this._gl.getParameter(this._gl.VERSION);\r\n\r\n        const rendererInfo: any = this._gl.getExtension(\"WEBGL_debug_renderer_info\");\r\n        if (rendererInfo != null) {\r\n            this._glRenderer = this._gl.getParameter(rendererInfo.UNMASKED_RENDERER_WEBGL);\r\n            this._glVendor = this._gl.getParameter(rendererInfo.UNMASKED_VENDOR_WEBGL);\r\n        }\r\n\r\n        if (!this._glVendor) {\r\n            this._glVendor = this._gl.getParameter(this._gl.VENDOR) || \"Unknown vendor\";\r\n        }\r\n\r\n        if (!this._glRenderer) {\r\n            this._glRenderer = this._gl.getParameter(this._gl.RENDERER) || \"Unknown renderer\";\r\n        }\r\n\r\n        // Constants\r\n        if (this._gl.HALF_FLOAT_OES !== 0x8d61) {\r\n            this._gl.HALF_FLOAT_OES = 0x8d61; // Half floating-point type (16-bit).\r\n        }\r\n        if (this._gl.RGBA16F !== 0x881a) {\r\n            this._gl.RGBA16F = 0x881a; // RGBA 16-bit floating-point color-renderable internal sized format.\r\n        }\r\n        if (this._gl.RGBA32F !== 0x8814) {\r\n            this._gl.RGBA32F = 0x8814; // RGBA 32-bit floating-point color-renderable internal sized format.\r\n        }\r\n        if (this._gl.DEPTH24_STENCIL8 !== 35056) {\r\n            this._gl.DEPTH24_STENCIL8 = 35056;\r\n        }\r\n\r\n        // Extensions\r\n        if (this._caps.timerQuery) {\r\n            if (this._webGLVersion === 1) {\r\n                this._gl.getQuery = (<any>this._caps.timerQuery).getQueryEXT.bind(this._caps.timerQuery);\r\n            }\r\n            // WebGLQuery casted to number to avoid TS error\r\n            this._caps.canUseTimestampForTimerQuery = ((this._gl.getQuery(this._caps.timerQuery.TIMESTAMP_EXT, this._caps.timerQuery.QUERY_COUNTER_BITS_EXT) as number) ?? 0) > 0;\r\n        }\r\n\r\n        this._caps.maxAnisotropy = this._caps.textureAnisotropicFilterExtension\r\n            ? this._gl.getParameter(this._caps.textureAnisotropicFilterExtension.MAX_TEXTURE_MAX_ANISOTROPY_EXT)\r\n            : 0;\r\n        this._caps.textureFloatLinearFiltering = this._caps.textureFloat && this._gl.getExtension(\"OES_texture_float_linear\") ? true : false;\r\n        this._caps.textureFloatRender = this._caps.textureFloat && this._canRenderToFloatFramebuffer() ? true : false;\r\n        this._caps.textureHalfFloatLinearFiltering =\r\n            this._webGLVersion > 1 || (this._caps.textureHalfFloat && this._gl.getExtension(\"OES_texture_half_float_linear\")) ? true : false;\r\n\r\n        // Compressed formats\r\n        if (this._caps.astc) {\r\n            this._gl.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR = this._caps.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR;\r\n        }\r\n        if (this._caps.bptc) {\r\n            this._gl.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT = this._caps.bptc.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT;\r\n        }\r\n        if (this._caps.s3tc_srgb) {\r\n            this._gl.COMPRESSED_SRGB_S3TC_DXT1_EXT = this._caps.s3tc_srgb.COMPRESSED_SRGB_S3TC_DXT1_EXT;\r\n            this._gl.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT = this._caps.s3tc_srgb.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT;\r\n            this._gl.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT = this._caps.s3tc_srgb.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT;\r\n        }\r\n        if (this._caps.etc2) {\r\n            this._gl.COMPRESSED_SRGB8_ETC2 = this._caps.etc2.COMPRESSED_SRGB8_ETC2;\r\n            this._gl.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC = this._caps.etc2.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC;\r\n        }\r\n\r\n        // Checks if some of the format renders first to allow the use of webgl inspector.\r\n        if (this._webGLVersion > 1) {\r\n            if (this._gl.HALF_FLOAT_OES !== 0x140b) {\r\n                this._gl.HALF_FLOAT_OES = 0x140b;\r\n            }\r\n        }\r\n        this._caps.textureHalfFloatRender = this._caps.textureHalfFloat && this._canRenderToHalfFloatFramebuffer();\r\n        // Draw buffers\r\n        if (this._webGLVersion > 1) {\r\n            this._caps.drawBuffersExtension = true;\r\n            this._caps.maxMSAASamples = this._maxMSAASamplesOverride !== null ? this._maxMSAASamplesOverride : this._gl.getParameter(this._gl.MAX_SAMPLES);\r\n        } else {\r\n            const drawBuffersExtension = this._gl.getExtension(\"WEBGL_draw_buffers\");\r\n\r\n            if (drawBuffersExtension !== null) {\r\n                this._caps.drawBuffersExtension = true;\r\n                this._gl.drawBuffers = drawBuffersExtension.drawBuffersWEBGL.bind(drawBuffersExtension);\r\n                (this._gl.DRAW_FRAMEBUFFER as any) = this._gl.FRAMEBUFFER;\r\n\r\n                for (let i = 0; i < 16; i++) {\r\n                    (<any>this._gl)[\"COLOR_ATTACHMENT\" + i + \"_WEBGL\"] = (<any>drawBuffersExtension)[\"COLOR_ATTACHMENT\" + i + \"_WEBGL\"];\r\n                }\r\n            }\r\n        }\r\n\r\n        // Depth Texture\r\n        if (this._webGLVersion > 1) {\r\n            this._caps.depthTextureExtension = true;\r\n        } else {\r\n            const depthTextureExtension = this._gl.getExtension(\"WEBGL_depth_texture\");\r\n\r\n            if (depthTextureExtension != null) {\r\n                this._caps.depthTextureExtension = true;\r\n                this._gl.UNSIGNED_INT_24_8 = depthTextureExtension.UNSIGNED_INT_24_8_WEBGL;\r\n            }\r\n        }\r\n\r\n        // Vertex array object\r\n        if (this.disableVertexArrayObjects) {\r\n            this._caps.vertexArrayObject = false;\r\n        } else if (this._webGLVersion > 1) {\r\n            this._caps.vertexArrayObject = true;\r\n        } else {\r\n            const vertexArrayObjectExtension = this._gl.getExtension(\"OES_vertex_array_object\");\r\n\r\n            if (vertexArrayObjectExtension != null) {\r\n                this._caps.vertexArrayObject = true;\r\n                this._gl.createVertexArray = vertexArrayObjectExtension.createVertexArrayOES.bind(vertexArrayObjectExtension);\r\n                this._gl.bindVertexArray = vertexArrayObjectExtension.bindVertexArrayOES.bind(vertexArrayObjectExtension);\r\n                this._gl.deleteVertexArray = vertexArrayObjectExtension.deleteVertexArrayOES.bind(vertexArrayObjectExtension);\r\n            }\r\n        }\r\n\r\n        // Instances count\r\n        if (this._webGLVersion > 1) {\r\n            this._caps.instancedArrays = true;\r\n        } else {\r\n            const instanceExtension = <ANGLE_instanced_arrays>this._gl.getExtension(\"ANGLE_instanced_arrays\");\r\n\r\n            if (instanceExtension != null) {\r\n                this._caps.instancedArrays = true;\r\n                this._gl.drawArraysInstanced = instanceExtension.drawArraysInstancedANGLE.bind(instanceExtension);\r\n                this._gl.drawElementsInstanced = instanceExtension.drawElementsInstancedANGLE.bind(instanceExtension);\r\n                this._gl.vertexAttribDivisor = instanceExtension.vertexAttribDivisorANGLE.bind(instanceExtension);\r\n            } else {\r\n                this._caps.instancedArrays = false;\r\n            }\r\n        }\r\n\r\n        if (this._gl.getShaderPrecisionFormat) {\r\n            const vertexhighp = this._gl.getShaderPrecisionFormat(this._gl.VERTEX_SHADER, this._gl.HIGH_FLOAT);\r\n            const fragmenthighp = this._gl.getShaderPrecisionFormat(this._gl.FRAGMENT_SHADER, this._gl.HIGH_FLOAT);\r\n\r\n            if (vertexhighp && fragmenthighp) {\r\n                this._caps.highPrecisionShaderSupported = vertexhighp.precision !== 0 && fragmenthighp.precision !== 0;\r\n            }\r\n        }\r\n\r\n        if (this._webGLVersion > 1) {\r\n            this._caps.blendMinMax = true;\r\n        } else {\r\n            const blendMinMaxExtension = this._gl.getExtension(\"EXT_blend_minmax\");\r\n            if (blendMinMaxExtension != null) {\r\n                this._caps.blendMinMax = true;\r\n                this._gl.MAX = blendMinMaxExtension.MAX_EXT as typeof WebGL2RenderingContext.MAX;\r\n                this._gl.MIN = blendMinMaxExtension.MIN_EXT as typeof WebGL2RenderingContext.MIN;\r\n            }\r\n        }\r\n\r\n        // sRGB buffers\r\n        // only run this if not already set to true (in the constructor, for example)\r\n        if (!this._caps.supportSRGBBuffers) {\r\n            if (this._webGLVersion > 1) {\r\n                this._caps.supportSRGBBuffers = true;\r\n                this._glSRGBExtensionValues = {\r\n                    SRGB: WebGL2RenderingContext.SRGB,\r\n                    SRGB8: WebGL2RenderingContext.SRGB8,\r\n                    SRGB8_ALPHA8: WebGL2RenderingContext.SRGB8_ALPHA8,\r\n                };\r\n            } else {\r\n                const sRGBExtension = this._gl.getExtension(\"EXT_sRGB\");\r\n\r\n                if (sRGBExtension != null) {\r\n                    this._caps.supportSRGBBuffers = true;\r\n                    this._glSRGBExtensionValues = {\r\n                        SRGB: sRGBExtension.SRGB_EXT as typeof WebGL2RenderingContext.SRGB | EXT_sRGB[\"SRGB_EXT\"],\r\n                        SRGB8: sRGBExtension.SRGB_ALPHA_EXT as typeof WebGL2RenderingContext.SRGB8 | EXT_sRGB[\"SRGB_ALPHA_EXT\"],\r\n                        SRGB8_ALPHA8: sRGBExtension.SRGB_ALPHA_EXT as typeof WebGL2RenderingContext.SRGB8_ALPHA8 | EXT_sRGB[\"SRGB8_ALPHA8_EXT\"],\r\n                    };\r\n                }\r\n            }\r\n            // take into account the forced state that was provided in options\r\n            // When the issue in angle/chrome is fixed the flag should be taken into account only when it is explicitly defined\r\n            this._caps.supportSRGBBuffers = this._caps.supportSRGBBuffers && !!(this._creationOptions && this._creationOptions.forceSRGBBufferSupportState);\r\n        }\r\n\r\n        // Depth buffer\r\n        this._depthCullingState.depthTest = true;\r\n        this._depthCullingState.depthFunc = this._gl.LEQUAL;\r\n        this._depthCullingState.depthMask = true;\r\n\r\n        // Texture maps\r\n        this._maxSimultaneousTextures = this._caps.maxCombinedTexturesImageUnits;\r\n        for (let slot = 0; slot < this._maxSimultaneousTextures; slot++) {\r\n            this._nextFreeTextureSlots.push(slot);\r\n        }\r\n\r\n        if (this._glRenderer === \"Mali-G72\") {\r\n            // Overcome a bug when using a texture to store morph targets on Mali-G72\r\n            this._caps.disableMorphTargetTexture = true;\r\n        }\r\n    }\r\n\r\n    protected _initFeatures(): void {\r\n        this._features = {\r\n            forceBitmapOverHTMLImageElement: typeof HTMLImageElement === \"undefined\",\r\n            supportRenderAndCopyToLodForFloatTextures: this._webGLVersion !== 1,\r\n            supportDepthStencilTexture: this._webGLVersion !== 1,\r\n            supportShadowSamplers: this._webGLVersion !== 1,\r\n            uniformBufferHardCheckMatrix: false,\r\n            allowTexturePrefiltering: this._webGLVersion !== 1,\r\n            trackUbosInFrame: false,\r\n            checkUbosContentBeforeUpload: false,\r\n            supportCSM: this._webGLVersion !== 1,\r\n            basisNeedsPOT: this._webGLVersion === 1,\r\n            support3DTextures: this._webGLVersion !== 1,\r\n            needTypeSuffixInShaderConstants: this._webGLVersion !== 1,\r\n            supportMSAA: this._webGLVersion !== 1,\r\n            supportSSAO2: this._webGLVersion !== 1,\r\n            supportExtendedTextureFormats: this._webGLVersion !== 1,\r\n            supportSwitchCaseInShader: this._webGLVersion !== 1,\r\n            supportSyncTextureRead: true,\r\n            needsInvertingBitmap: true,\r\n            useUBOBindingCache: true,\r\n            needShaderCodeInlining: false,\r\n            needToAlwaysBindUniformBuffers: false,\r\n            supportRenderPasses: false,\r\n            supportSpriteInstancing: true,\r\n            forceVertexBufferStrideAndOffsetMultiple4Bytes: false,\r\n            _collectUbosUpdatedInFrame: false,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets version of the current webGL context\r\n     * Keep it for back compat - use version instead\r\n     */\r\n    public get webGLVersion(): number {\r\n        return this._webGLVersion;\r\n    }\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"Engine\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"ThinEngine\";\r\n    }\r\n\r\n    /**\r\n     * Returns true if the stencil buffer has been enabled through the creation option of the context.\r\n     */\r\n    public get isStencilEnable(): boolean {\r\n        return this._isStencilEnable;\r\n    }\r\n\r\n    /** @internal */\r\n    public _prepareWorkingCanvas(): void {\r\n        if (this._workingCanvas) {\r\n            return;\r\n        }\r\n\r\n        this._workingCanvas = this.createCanvas(1, 1);\r\n        const context = this._workingCanvas.getContext(\"2d\");\r\n\r\n        if (context) {\r\n            this._workingContext = context;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Reset the texture cache to empty state\r\n     */\r\n    public resetTextureCache() {\r\n        for (const key in this._boundTexturesCache) {\r\n            if (!Object.prototype.hasOwnProperty.call(this._boundTexturesCache, key)) {\r\n                continue;\r\n            }\r\n            this._boundTexturesCache[key] = null;\r\n        }\r\n\r\n        this._currentTextureChannel = -1;\r\n    }\r\n\r\n    /**\r\n     * Gets an object containing information about the current engine context\r\n     * @returns an object containing the vendor, the renderer and the version of the current engine context\r\n     */\r\n    public getInfo() {\r\n        return this.getGlInfo();\r\n    }\r\n\r\n    /**\r\n     * Gets an object containing information about the current webGL context\r\n     * @returns an object containing the vendor, the renderer and the version of the current webGL context\r\n     */\r\n    public getGlInfo() {\r\n        return {\r\n            vendor: this._glVendor,\r\n            renderer: this._glRenderer,\r\n            version: this._glVersion,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Defines the hardware scaling level.\r\n     * By default the hardware scaling level is computed from the window device ratio.\r\n     * if level = 1 then the engine will render at the exact resolution of the canvas. If level = 0.5 then the engine will render at twice the size of the canvas.\r\n     * @param level defines the level to use\r\n     */\r\n    public setHardwareScalingLevel(level: number): void {\r\n        this._hardwareScalingLevel = level;\r\n        this.resize();\r\n    }\r\n\r\n    /**\r\n     * Gets the current hardware scaling level.\r\n     * By default the hardware scaling level is computed from the window device ratio.\r\n     * if level = 1 then the engine will render at the exact resolution of the canvas. If level = 0.5 then the engine will render at twice the size of the canvas.\r\n     * @returns a number indicating the current hardware scaling level\r\n     */\r\n    public getHardwareScalingLevel(): number {\r\n        return this._hardwareScalingLevel;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of loaded textures\r\n     * @returns an array containing all loaded textures\r\n     */\r\n    public getLoadedTexturesCache(): InternalTexture[] {\r\n        return this._internalTexturesCache;\r\n    }\r\n\r\n    /**\r\n     * Gets the object containing all engine capabilities\r\n     * @returns the EngineCapabilities object\r\n     */\r\n    public getCaps(): EngineCapabilities {\r\n        return this._caps;\r\n    }\r\n\r\n    /**\r\n     * stop executing a render loop function and remove it from the execution array\r\n     * @param renderFunction defines the function to be removed. If not provided all functions will be removed.\r\n     */\r\n    public stopRenderLoop(renderFunction?: () => void): void {\r\n        if (!renderFunction) {\r\n            this._activeRenderLoops.length = 0;\r\n            this._cancelFrame();\r\n            return;\r\n        }\r\n\r\n        const index = this._activeRenderLoops.indexOf(renderFunction);\r\n\r\n        if (index >= 0) {\r\n            this._activeRenderLoops.splice(index, 1);\r\n            if (this._activeRenderLoops.length == 0) {\r\n                this._cancelFrame();\r\n            }\r\n        }\r\n    }\r\n\r\n    protected _cancelFrame() {\r\n        if (this._frameHandler !== 0) {\r\n            const handlerToCancel = this._frameHandler;\r\n            this._frameHandler = 0;\r\n\r\n            if (!IsWindowObjectExist()) {\r\n                if (typeof cancelAnimationFrame === \"function\") {\r\n                    return cancelAnimationFrame(handlerToCancel);\r\n                }\r\n            } else {\r\n                const { cancelAnimationFrame } = this.getHostWindow() || window;\r\n                if (typeof cancelAnimationFrame === \"function\") {\r\n                    return cancelAnimationFrame(handlerToCancel);\r\n                }\r\n            }\r\n            return clearTimeout(handlerToCancel);\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _renderLoop(): void {\r\n        this._frameHandler = 0;\r\n\r\n        if (!this._contextWasLost) {\r\n            let shouldRender = true;\r\n            if (this._isDisposed || (!this.renderEvenInBackground && this._windowIsBackground)) {\r\n                shouldRender = false;\r\n            }\r\n\r\n            if (shouldRender) {\r\n                // Start new frame\r\n                this.beginFrame();\r\n\r\n                for (let index = 0; index < this._activeRenderLoops.length; index++) {\r\n                    const renderFunction = this._activeRenderLoops[index];\r\n\r\n                    renderFunction();\r\n                }\r\n\r\n                // Present\r\n                this.endFrame();\r\n            }\r\n        }\r\n\r\n        if (this._frameHandler === 0) {\r\n            this._frameHandler = this._queueNewFrame(this._boundRenderFunction, this.getHostWindow());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the HTML canvas attached with the current webGL context\r\n     * @returns a HTML canvas\r\n     */\r\n    public getRenderingCanvas(): Nullable<HTMLCanvasElement> {\r\n        return this._renderingCanvas;\r\n    }\r\n\r\n    /**\r\n     * Gets the audio context specified in engine initialization options\r\n     * @returns an Audio Context\r\n     */\r\n    public getAudioContext(): Nullable<AudioContext> {\r\n        return this._audioContext;\r\n    }\r\n\r\n    /**\r\n     * Gets the audio destination specified in engine initialization options\r\n     * @returns an audio destination node\r\n     */\r\n    public getAudioDestination(): Nullable<AudioDestinationNode | MediaStreamAudioDestinationNode> {\r\n        return this._audioDestination;\r\n    }\r\n\r\n    /**\r\n     * Gets host window\r\n     * @returns the host window object\r\n     */\r\n    public getHostWindow(): Nullable<Window> {\r\n        if (!IsWindowObjectExist()) {\r\n            return null;\r\n        }\r\n\r\n        if (this._renderingCanvas && this._renderingCanvas.ownerDocument && this._renderingCanvas.ownerDocument.defaultView) {\r\n            return this._renderingCanvas.ownerDocument.defaultView;\r\n        }\r\n\r\n        return window;\r\n    }\r\n\r\n    /**\r\n     * Gets the current render width\r\n     * @param useScreen defines if screen size must be used (or the current render target if any)\r\n     * @returns a number defining the current render width\r\n     */\r\n    public getRenderWidth(useScreen = false): number {\r\n        if (!useScreen && this._currentRenderTarget) {\r\n            return this._currentRenderTarget.width;\r\n        }\r\n\r\n        return this._framebufferDimensionsObject ? this._framebufferDimensionsObject.framebufferWidth : this._gl.drawingBufferWidth;\r\n    }\r\n\r\n    /**\r\n     * Gets the current render height\r\n     * @param useScreen defines if screen size must be used (or the current render target if any)\r\n     * @returns a number defining the current render height\r\n     */\r\n    public getRenderHeight(useScreen = false): number {\r\n        if (!useScreen && this._currentRenderTarget) {\r\n            return this._currentRenderTarget.height;\r\n        }\r\n\r\n        return this._framebufferDimensionsObject ? this._framebufferDimensionsObject.framebufferHeight : this._gl.drawingBufferHeight;\r\n    }\r\n\r\n    /**\r\n     * Can be used to override the current requestAnimationFrame requester.\r\n     * @internal\r\n     */\r\n    protected _queueNewFrame(bindedRenderFunction: any, requester?: any): number {\r\n        return ThinEngine.QueueNewFrame(bindedRenderFunction, requester);\r\n    }\r\n\r\n    /**\r\n     * Register and execute a render loop. The engine can have more than one render function\r\n     * @param renderFunction defines the function to continuously execute\r\n     */\r\n    public runRenderLoop(renderFunction: () => void): void {\r\n        if (this._activeRenderLoops.indexOf(renderFunction) !== -1) {\r\n            return;\r\n        }\r\n\r\n        this._activeRenderLoops.push(renderFunction);\r\n\r\n        // On the first added function, start the render loop.\r\n        if (this._activeRenderLoops.length === 1 && this._frameHandler === 0) {\r\n            this._frameHandler = this._queueNewFrame(this._boundRenderFunction, this.getHostWindow());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clear the current render buffer or the current render target (if any is set up)\r\n     * @param color defines the color to use\r\n     * @param backBuffer defines if the back buffer must be cleared\r\n     * @param depth defines if the depth buffer must be cleared\r\n     * @param stencil defines if the stencil buffer must be cleared\r\n     */\r\n    public clear(color: Nullable<IColor4Like>, backBuffer: boolean, depth: boolean, stencil: boolean = false): void {\r\n        const useStencilGlobalOnly = this.stencilStateComposer.useStencilGlobalOnly;\r\n        this.stencilStateComposer.useStencilGlobalOnly = true; // make sure the stencil mask is coming from the global stencil and not from a material (effect) which would currently be in effect\r\n\r\n        this.applyStates();\r\n\r\n        this.stencilStateComposer.useStencilGlobalOnly = useStencilGlobalOnly;\r\n\r\n        let mode = 0;\r\n        if (backBuffer && color) {\r\n            let setBackBufferColor = true;\r\n            if (this._currentRenderTarget) {\r\n                const textureFormat = this._currentRenderTarget.texture?.format;\r\n                if (\r\n                    textureFormat === Constants.TEXTUREFORMAT_RED_INTEGER ||\r\n                    textureFormat === Constants.TEXTUREFORMAT_RG_INTEGER ||\r\n                    textureFormat === Constants.TEXTUREFORMAT_RGB_INTEGER ||\r\n                    textureFormat === Constants.TEXTUREFORMAT_RGBA_INTEGER\r\n                ) {\r\n                    const textureType = this._currentRenderTarget.texture?.type;\r\n                    if (textureType === Constants.TEXTURETYPE_UNSIGNED_INTEGER || textureType === Constants.TEXTURETYPE_UNSIGNED_SHORT) {\r\n                        ThinEngine._TempClearColorUint32[0] = color.r * 255;\r\n                        ThinEngine._TempClearColorUint32[1] = color.g * 255;\r\n                        ThinEngine._TempClearColorUint32[2] = color.b * 255;\r\n                        ThinEngine._TempClearColorUint32[3] = color.a * 255;\r\n                        this._gl.clearBufferuiv(this._gl.COLOR, 0, ThinEngine._TempClearColorUint32);\r\n                        setBackBufferColor = false;\r\n                    } else {\r\n                        ThinEngine._TempClearColorInt32[0] = color.r * 255;\r\n                        ThinEngine._TempClearColorInt32[1] = color.g * 255;\r\n                        ThinEngine._TempClearColorInt32[2] = color.b * 255;\r\n                        ThinEngine._TempClearColorInt32[3] = color.a * 255;\r\n                        this._gl.clearBufferiv(this._gl.COLOR, 0, ThinEngine._TempClearColorInt32);\r\n                        setBackBufferColor = false;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (setBackBufferColor) {\r\n                this._gl.clearColor(color.r, color.g, color.b, color.a !== undefined ? color.a : 1.0);\r\n                mode |= this._gl.COLOR_BUFFER_BIT;\r\n            }\r\n        }\r\n\r\n        if (depth) {\r\n            if (this.useReverseDepthBuffer) {\r\n                this._depthCullingState.depthFunc = this._gl.GEQUAL;\r\n                this._gl.clearDepth(0.0);\r\n            } else {\r\n                this._gl.clearDepth(1.0);\r\n            }\r\n            mode |= this._gl.DEPTH_BUFFER_BIT;\r\n        }\r\n        if (stencil) {\r\n            this._gl.clearStencil(0);\r\n            mode |= this._gl.STENCIL_BUFFER_BIT;\r\n        }\r\n        this._gl.clear(mode);\r\n    }\r\n\r\n    protected _viewportCached = { x: 0, y: 0, z: 0, w: 0 };\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _viewport(x: number, y: number, width: number, height: number): void {\r\n        if (x !== this._viewportCached.x || y !== this._viewportCached.y || width !== this._viewportCached.z || height !== this._viewportCached.w) {\r\n            this._viewportCached.x = x;\r\n            this._viewportCached.y = y;\r\n            this._viewportCached.z = width;\r\n            this._viewportCached.w = height;\r\n\r\n            this._gl.viewport(x, y, width, height);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the WebGL's viewport\r\n     * @param viewport defines the viewport element to be used\r\n     * @param requiredWidth defines the width required for rendering. If not provided the rendering canvas' width is used\r\n     * @param requiredHeight defines the height required for rendering. If not provided the rendering canvas' height is used\r\n     */\r\n    public setViewport(viewport: IViewportLike, requiredWidth?: number, requiredHeight?: number): void {\r\n        const width = requiredWidth || this.getRenderWidth();\r\n        const height = requiredHeight || this.getRenderHeight();\r\n        const x = viewport.x || 0;\r\n        const y = viewport.y || 0;\r\n\r\n        this._cachedViewport = viewport;\r\n\r\n        this._viewport(x * width, y * height, width * viewport.width, height * viewport.height);\r\n    }\r\n\r\n    /**\r\n     * Begin a new frame\r\n     */\r\n    public beginFrame(): void {}\r\n\r\n    /**\r\n     * Enf the current frame\r\n     */\r\n    public endFrame(): void {\r\n        // Force a flush in case we are using a bad OS.\r\n        if (this._badOS) {\r\n            this.flushFramebuffer();\r\n        }\r\n        this._frameId++;\r\n    }\r\n\r\n    /**\r\n     * Resize the view according to the canvas' size\r\n     * @param forceSetSize true to force setting the sizes of the underlying canvas\r\n     */\r\n    public resize(forceSetSize = false): void {\r\n        let width: number;\r\n        let height: number;\r\n\r\n        // Re-query hardware scaling level to handle zoomed-in resizing.\r\n        if (this.adaptToDeviceRatio) {\r\n            const devicePixelRatio = IsWindowObjectExist() ? window.devicePixelRatio || 1.0 : 1.0;\r\n            const changeRatio = this._lastDevicePixelRatio / devicePixelRatio;\r\n            this._lastDevicePixelRatio = devicePixelRatio;\r\n            this._hardwareScalingLevel *= changeRatio;\r\n        }\r\n\r\n        if (IsWindowObjectExist() && IsDocumentAvailable()) {\r\n            // make sure it is a Node object, and is a part of the document.\r\n            if (this._renderingCanvas) {\r\n                const boundingRect = this._renderingCanvas.getBoundingClientRect\r\n                    ? this._renderingCanvas.getBoundingClientRect()\r\n                    : {\r\n                          // fallback to last solution in case the function doesn't exist\r\n                          width: this._renderingCanvas.width * this._hardwareScalingLevel,\r\n                          height: this._renderingCanvas.height * this._hardwareScalingLevel,\r\n                      };\r\n                width = this._renderingCanvas.clientWidth || boundingRect.width || this._renderingCanvas.width || 100;\r\n                height = this._renderingCanvas.clientHeight || boundingRect.height || this._renderingCanvas.height || 100;\r\n            } else {\r\n                width = window.innerWidth;\r\n                height = window.innerHeight;\r\n            }\r\n        } else {\r\n            width = this._renderingCanvas ? this._renderingCanvas.width : 100;\r\n            height = this._renderingCanvas ? this._renderingCanvas.height : 100;\r\n        }\r\n\r\n        this.setSize(width / this._hardwareScalingLevel, height / this._hardwareScalingLevel, forceSetSize);\r\n    }\r\n\r\n    /**\r\n     * Force a specific size of the canvas\r\n     * @param width defines the new canvas' width\r\n     * @param height defines the new canvas' height\r\n     * @param forceSetSize true to force setting the sizes of the underlying canvas\r\n     * @returns true if the size was changed\r\n     */\r\n    public setSize(width: number, height: number, forceSetSize = false): boolean {\r\n        if (!this._renderingCanvas) {\r\n            return false;\r\n        }\r\n\r\n        width = width | 0;\r\n        height = height | 0;\r\n\r\n        if (!forceSetSize && this._renderingCanvas.width === width && this._renderingCanvas.height === height) {\r\n            return false;\r\n        }\r\n\r\n        this._renderingCanvas.width = width;\r\n        this._renderingCanvas.height = height;\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Binds the frame buffer to the specified texture.\r\n     * @param rtWrapper The render target wrapper to render to\r\n     * @param faceIndex The face of the texture to render to in case of cube texture and if the render target wrapper is not a multi render target\r\n     * @param requiredWidth The width of the target to render to\r\n     * @param requiredHeight The height of the target to render to\r\n     * @param forceFullscreenViewport Forces the viewport to be the entire texture/screen if true\r\n     * @param lodLevel Defines the lod level to bind to the frame buffer\r\n     * @param layer Defines the 2d array index to bind to the frame buffer if the render target wrapper is not a multi render target\r\n     */\r\n    public bindFramebuffer(\r\n        rtWrapper: RenderTargetWrapper,\r\n        faceIndex: number = 0,\r\n        requiredWidth?: number,\r\n        requiredHeight?: number,\r\n        forceFullscreenViewport?: boolean,\r\n        lodLevel = 0,\r\n        layer = 0\r\n    ): void {\r\n        const webglRTWrapper = rtWrapper as WebGLRenderTargetWrapper;\r\n\r\n        if (this._currentRenderTarget) {\r\n            this.unBindFramebuffer(this._currentRenderTarget);\r\n        }\r\n        this._currentRenderTarget = rtWrapper;\r\n        this._bindUnboundFramebuffer(webglRTWrapper._MSAAFramebuffer ? webglRTWrapper._MSAAFramebuffer : webglRTWrapper._framebuffer);\r\n\r\n        const gl = this._gl;\r\n        if (!rtWrapper.isMulti) {\r\n            if (rtWrapper.is2DArray) {\r\n                gl.framebufferTextureLayer(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, rtWrapper.texture!._hardwareTexture?.underlyingResource, lodLevel, layer);\r\n            } else if (rtWrapper.isCube) {\r\n                gl.framebufferTexture2D(\r\n                    gl.FRAMEBUFFER,\r\n                    gl.COLOR_ATTACHMENT0,\r\n                    gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex,\r\n                    rtWrapper.texture!._hardwareTexture?.underlyingResource,\r\n                    lodLevel\r\n                );\r\n            } else if (webglRTWrapper._currentLOD !== lodLevel) {\r\n                gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, rtWrapper.texture!._hardwareTexture?.underlyingResource, lodLevel);\r\n                webglRTWrapper._currentLOD = lodLevel;\r\n            }\r\n        }\r\n\r\n        const depthStencilTexture = rtWrapper._depthStencilTexture;\r\n        if (depthStencilTexture) {\r\n            const attachment = rtWrapper._depthStencilTextureWithStencil ? gl.DEPTH_STENCIL_ATTACHMENT : gl.DEPTH_ATTACHMENT;\r\n            if (rtWrapper.is2DArray) {\r\n                gl.framebufferTextureLayer(gl.FRAMEBUFFER, attachment, depthStencilTexture._hardwareTexture?.underlyingResource, lodLevel, layer);\r\n            } else if (rtWrapper.isCube) {\r\n                gl.framebufferTexture2D(gl.FRAMEBUFFER, attachment, gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex, depthStencilTexture._hardwareTexture?.underlyingResource, lodLevel);\r\n            } else {\r\n                gl.framebufferTexture2D(gl.FRAMEBUFFER, attachment, gl.TEXTURE_2D, depthStencilTexture._hardwareTexture?.underlyingResource, lodLevel);\r\n            }\r\n        }\r\n\r\n        if (this._cachedViewport && !forceFullscreenViewport) {\r\n            this.setViewport(this._cachedViewport, requiredWidth, requiredHeight);\r\n        } else {\r\n            if (!requiredWidth) {\r\n                requiredWidth = rtWrapper.width;\r\n                if (lodLevel) {\r\n                    requiredWidth = requiredWidth / Math.pow(2, lodLevel);\r\n                }\r\n            }\r\n            if (!requiredHeight) {\r\n                requiredHeight = rtWrapper.height;\r\n                if (lodLevel) {\r\n                    requiredHeight = requiredHeight / Math.pow(2, lodLevel);\r\n                }\r\n            }\r\n\r\n            this._viewport(0, 0, requiredWidth, requiredHeight);\r\n        }\r\n\r\n        this.wipeCaches();\r\n    }\r\n\r\n    /**\r\n     * Set various states to the webGL context\r\n     * @param culling defines culling state: true to enable culling, false to disable it\r\n     * @param zOffset defines the value to apply to zOffset (0 by default)\r\n     * @param force defines if states must be applied even if cache is up to date\r\n     * @param reverseSide defines if culling must be reversed (CCW if false, CW if true)\r\n     * @param cullBackFaces true to cull back faces, false to cull front faces (if culling is enabled)\r\n     * @param stencil stencil states to set\r\n     * @param zOffsetUnits defines the value to apply to zOffsetUnits (0 by default)\r\n     */\r\n    public setState(culling: boolean, zOffset: number = 0, force?: boolean, reverseSide = false, cullBackFaces?: boolean, stencil?: IStencilState, zOffsetUnits: number = 0): void {\r\n        // Culling\r\n        if (this._depthCullingState.cull !== culling || force) {\r\n            this._depthCullingState.cull = culling;\r\n        }\r\n\r\n        // Cull face\r\n        const cullFace = this.cullBackFaces ?? cullBackFaces ?? true ? this._gl.BACK : this._gl.FRONT;\r\n        if (this._depthCullingState.cullFace !== cullFace || force) {\r\n            this._depthCullingState.cullFace = cullFace;\r\n        }\r\n\r\n        // Z offset\r\n        this.setZOffset(zOffset);\r\n        this.setZOffsetUnits(zOffsetUnits);\r\n\r\n        // Front face\r\n        const frontFace = reverseSide ? this._gl.CW : this._gl.CCW;\r\n        if (this._depthCullingState.frontFace !== frontFace || force) {\r\n            this._depthCullingState.frontFace = frontFace;\r\n        }\r\n\r\n        this._stencilStateComposer.stencilMaterial = stencil;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if depth testing is enabled\r\n     * @returns the current state\r\n     */\r\n    public getDepthBuffer(): boolean {\r\n        return this._depthCullingState.depthTest;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable depth buffering\r\n     * @param enable defines the state to set\r\n     */\r\n    public setDepthBuffer(enable: boolean): void {\r\n        this._depthCullingState.depthTest = enable;\r\n    }\r\n\r\n    /**\r\n     * Set the z offset Factor to apply to current rendering\r\n     * @param value defines the offset to apply\r\n     */\r\n    public setZOffset(value: number): void {\r\n        this._depthCullingState.zOffset = this.useReverseDepthBuffer ? -value : value;\r\n    }\r\n\r\n    /**\r\n     * Gets the current value of the zOffset Factor\r\n     * @returns the current zOffset Factor state\r\n     */\r\n    public getZOffset(): number {\r\n        const zOffset = this._depthCullingState.zOffset;\r\n        return this.useReverseDepthBuffer ? -zOffset : zOffset;\r\n    }\r\n\r\n    /**\r\n     * Set the z offset Units to apply to current rendering\r\n     * @param value defines the offset to apply\r\n     */\r\n    public setZOffsetUnits(value: number): void {\r\n        this._depthCullingState.zOffsetUnits = this.useReverseDepthBuffer ? -value : value;\r\n    }\r\n\r\n    /**\r\n     * Gets the current value of the zOffset Units\r\n     * @returns the current zOffset Units state\r\n     */\r\n    public getZOffsetUnits(): number {\r\n        const zOffsetUnits = this._depthCullingState.zOffsetUnits;\r\n        return this.useReverseDepthBuffer ? -zOffsetUnits : zOffsetUnits;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindUnboundFramebuffer(framebuffer: Nullable<WebGLFramebuffer>) {\r\n        if (this._currentFramebuffer !== framebuffer) {\r\n            this._gl.bindFramebuffer(this._gl.FRAMEBUFFER, framebuffer);\r\n            this._currentFramebuffer = framebuffer;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _currentFrameBufferIsDefaultFrameBuffer() {\r\n        return this._currentFramebuffer === null;\r\n    }\r\n\r\n    /**\r\n     * Generates the mipmaps for a texture\r\n     * @param texture texture to generate the mipmaps for\r\n     */\r\n    public generateMipmaps(texture: InternalTexture): void {\r\n        this._bindTextureDirectly(this._gl.TEXTURE_2D, texture, true);\r\n        this._gl.generateMipmap(this._gl.TEXTURE_2D);\r\n        this._bindTextureDirectly(this._gl.TEXTURE_2D, null);\r\n    }\r\n\r\n    /**\r\n     * Unbind the current render target texture from the webGL context\r\n     * @param texture defines the render target wrapper to unbind\r\n     * @param disableGenerateMipMaps defines a boolean indicating that mipmaps must not be generated\r\n     * @param onBeforeUnbind defines a function which will be called before the effective unbind\r\n     */\r\n    public unBindFramebuffer(texture: RenderTargetWrapper, disableGenerateMipMaps = false, onBeforeUnbind?: () => void): void {\r\n        const webglRTWrapper = texture as WebGLRenderTargetWrapper;\r\n\r\n        this._currentRenderTarget = null;\r\n\r\n        // If MSAA, we need to bitblt back to main texture\r\n        const gl = this._gl;\r\n        if (webglRTWrapper._MSAAFramebuffer) {\r\n            if (texture.isMulti) {\r\n                // This texture is part of a MRT texture, we need to treat all attachments\r\n                this.unBindMultiColorAttachmentFramebuffer(texture, disableGenerateMipMaps, onBeforeUnbind);\r\n                return;\r\n            }\r\n            gl.bindFramebuffer(gl.READ_FRAMEBUFFER, webglRTWrapper._MSAAFramebuffer);\r\n            gl.bindFramebuffer(gl.DRAW_FRAMEBUFFER, webglRTWrapper._framebuffer);\r\n            gl.blitFramebuffer(0, 0, texture.width, texture.height, 0, 0, texture.width, texture.height, gl.COLOR_BUFFER_BIT, gl.NEAREST);\r\n        }\r\n\r\n        if (texture.texture?.generateMipMaps && !disableGenerateMipMaps && !texture.isCube) {\r\n            this.generateMipmaps(texture.texture);\r\n        }\r\n\r\n        if (onBeforeUnbind) {\r\n            if (webglRTWrapper._MSAAFramebuffer) {\r\n                // Bind the correct framebuffer\r\n                this._bindUnboundFramebuffer(webglRTWrapper._framebuffer);\r\n            }\r\n            onBeforeUnbind();\r\n        }\r\n\r\n        this._bindUnboundFramebuffer(null);\r\n    }\r\n\r\n    /**\r\n     * Force a webGL flush (ie. a flush of all waiting webGL commands)\r\n     */\r\n    public flushFramebuffer(): void {\r\n        this._gl.flush();\r\n    }\r\n\r\n    /**\r\n     * Unbind the current render target and bind the default framebuffer\r\n     */\r\n    public restoreDefaultFramebuffer(): void {\r\n        if (this._currentRenderTarget) {\r\n            this.unBindFramebuffer(this._currentRenderTarget);\r\n        } else {\r\n            this._bindUnboundFramebuffer(null);\r\n        }\r\n        if (this._cachedViewport) {\r\n            this.setViewport(this._cachedViewport);\r\n        }\r\n\r\n        this.wipeCaches();\r\n    }\r\n\r\n    // VBOs\r\n\r\n    /** @internal */\r\n    protected _resetVertexBufferBinding(): void {\r\n        this.bindArrayBuffer(null);\r\n        this._cachedVertexBuffers = null;\r\n    }\r\n\r\n    /**\r\n     * Creates a vertex buffer\r\n     * @param data the data or size for the vertex buffer\r\n     * @param _updatable whether the buffer should be created as updatable\r\n     * @param _label defines the label of the buffer (for debug purpose)\r\n     * @returns the new WebGL static buffer\r\n     */\r\n    public createVertexBuffer(data: DataArray | number, _updatable?: boolean, _label?: string): DataBuffer {\r\n        return this._createVertexBuffer(data, this._gl.STATIC_DRAW);\r\n    }\r\n\r\n    private _createVertexBuffer(data: DataArray | number, usage: number): DataBuffer {\r\n        const vbo = this._gl.createBuffer();\r\n\r\n        if (!vbo) {\r\n            throw new Error(\"Unable to create vertex buffer\");\r\n        }\r\n\r\n        const dataBuffer = new WebGLDataBuffer(vbo);\r\n        this.bindArrayBuffer(dataBuffer);\r\n\r\n        if (typeof data !== \"number\") {\r\n            if (data instanceof Array) {\r\n                this._gl.bufferData(this._gl.ARRAY_BUFFER, new Float32Array(data), usage);\r\n                dataBuffer.capacity = data.length * 4;\r\n            } else {\r\n                this._gl.bufferData(this._gl.ARRAY_BUFFER, <ArrayBuffer>data, usage);\r\n                dataBuffer.capacity = data.byteLength;\r\n            }\r\n        } else {\r\n            this._gl.bufferData(this._gl.ARRAY_BUFFER, new Uint8Array(data), usage);\r\n            dataBuffer.capacity = data;\r\n        }\r\n\r\n        this._resetVertexBufferBinding();\r\n\r\n        dataBuffer.references = 1;\r\n        return dataBuffer;\r\n    }\r\n\r\n    /**\r\n     * Creates a dynamic vertex buffer\r\n     * @param data the data for the dynamic vertex buffer\r\n     * @param _label defines the label of the buffer (for debug purpose)\r\n     * @returns the new WebGL dynamic buffer\r\n     */\r\n    public createDynamicVertexBuffer(data: DataArray | number, _label?: string): DataBuffer {\r\n        return this._createVertexBuffer(data, this._gl.DYNAMIC_DRAW);\r\n    }\r\n\r\n    protected _resetIndexBufferBinding(): void {\r\n        this.bindIndexBuffer(null);\r\n        this._cachedIndexBuffer = null;\r\n    }\r\n\r\n    /**\r\n     * Creates a new index buffer\r\n     * @param indices defines the content of the index buffer\r\n     * @param updatable defines if the index buffer must be updatable\r\n     * @param _label defines the label of the buffer (for debug purpose)\r\n     * @returns a new webGL buffer\r\n     */\r\n    public createIndexBuffer(indices: IndicesArray, updatable?: boolean, _label?: string): DataBuffer {\r\n        const vbo = this._gl.createBuffer();\r\n        const dataBuffer = new WebGLDataBuffer(vbo!);\r\n\r\n        if (!vbo) {\r\n            throw new Error(\"Unable to create index buffer\");\r\n        }\r\n\r\n        this.bindIndexBuffer(dataBuffer);\r\n\r\n        const data = this._normalizeIndexData(indices);\r\n        this._gl.bufferData(this._gl.ELEMENT_ARRAY_BUFFER, data, updatable ? this._gl.DYNAMIC_DRAW : this._gl.STATIC_DRAW);\r\n        this._resetIndexBufferBinding();\r\n        dataBuffer.references = 1;\r\n        dataBuffer.is32Bits = data.BYTES_PER_ELEMENT === 4;\r\n        return dataBuffer;\r\n    }\r\n\r\n    protected _normalizeIndexData(indices: IndicesArray): Uint16Array | Uint32Array {\r\n        const bytesPerElement = (indices as Exclude<IndicesArray, number[]>).BYTES_PER_ELEMENT;\r\n        if (bytesPerElement === 2) {\r\n            return indices as Uint16Array;\r\n        }\r\n\r\n        // Check 32 bit support\r\n        if (this._caps.uintIndices) {\r\n            if (indices instanceof Uint32Array) {\r\n                return indices;\r\n            } else {\r\n                // number[] or Int32Array, check if 32 bit is necessary\r\n                for (let index = 0; index < indices.length; index++) {\r\n                    if (indices[index] >= 65535) {\r\n                        return new Uint32Array(indices);\r\n                    }\r\n                }\r\n\r\n                return new Uint16Array(indices);\r\n            }\r\n        }\r\n\r\n        // No 32 bit support, force conversion to 16 bit (values greater 16 bit are lost)\r\n        return new Uint16Array(indices);\r\n    }\r\n\r\n    /**\r\n     * Bind a webGL buffer to the webGL context\r\n     * @param buffer defines the buffer to bind\r\n     */\r\n    public bindArrayBuffer(buffer: Nullable<DataBuffer>): void {\r\n        if (!this._vaoRecordInProgress) {\r\n            this._unbindVertexArrayObject();\r\n        }\r\n        this._bindBuffer(buffer, this._gl.ARRAY_BUFFER);\r\n    }\r\n\r\n    /**\r\n     * Bind a specific block at a given index in a specific shader program\r\n     * @param pipelineContext defines the pipeline context to use\r\n     * @param blockName defines the block name\r\n     * @param index defines the index where to bind the block\r\n     */\r\n    public bindUniformBlock(pipelineContext: IPipelineContext, blockName: string, index: number): void {\r\n        const program = (pipelineContext as WebGLPipelineContext).program!;\r\n\r\n        const uniformLocation = this._gl.getUniformBlockIndex(program, blockName);\r\n\r\n        this._gl.uniformBlockBinding(program, uniformLocation, index);\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected bindIndexBuffer(buffer: Nullable<DataBuffer>): void {\r\n        if (!this._vaoRecordInProgress) {\r\n            this._unbindVertexArrayObject();\r\n        }\r\n        this._bindBuffer(buffer, this._gl.ELEMENT_ARRAY_BUFFER);\r\n    }\r\n\r\n    private _bindBuffer(buffer: Nullable<DataBuffer>, target: number): void {\r\n        if (this._vaoRecordInProgress || this._currentBoundBuffer[target] !== buffer) {\r\n            this._gl.bindBuffer(target, buffer ? buffer.underlyingResource : null);\r\n            this._currentBoundBuffer[target] = buffer;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * update the bound buffer with the given data\r\n     * @param data defines the data to update\r\n     */\r\n    public updateArrayBuffer(data: Float32Array): void {\r\n        this._gl.bufferSubData(this._gl.ARRAY_BUFFER, 0, data);\r\n    }\r\n\r\n    private _vertexAttribPointer(buffer: DataBuffer, indx: number, size: number, type: number, normalized: boolean, stride: number, offset: number): void {\r\n        const pointer = this._currentBufferPointers[indx];\r\n        if (!pointer) {\r\n            return;\r\n        }\r\n\r\n        let changed = false;\r\n        if (!pointer.active) {\r\n            changed = true;\r\n            pointer.active = true;\r\n            pointer.index = indx;\r\n            pointer.size = size;\r\n            pointer.type = type;\r\n            pointer.normalized = normalized;\r\n            pointer.stride = stride;\r\n            pointer.offset = offset;\r\n            pointer.buffer = buffer;\r\n        } else {\r\n            if (pointer.buffer !== buffer) {\r\n                pointer.buffer = buffer;\r\n                changed = true;\r\n            }\r\n            if (pointer.size !== size) {\r\n                pointer.size = size;\r\n                changed = true;\r\n            }\r\n            if (pointer.type !== type) {\r\n                pointer.type = type;\r\n                changed = true;\r\n            }\r\n            if (pointer.normalized !== normalized) {\r\n                pointer.normalized = normalized;\r\n                changed = true;\r\n            }\r\n            if (pointer.stride !== stride) {\r\n                pointer.stride = stride;\r\n                changed = true;\r\n            }\r\n            if (pointer.offset !== offset) {\r\n                pointer.offset = offset;\r\n                changed = true;\r\n            }\r\n        }\r\n\r\n        if (changed || this._vaoRecordInProgress) {\r\n            this.bindArrayBuffer(buffer);\r\n            if (type === this._gl.UNSIGNED_INT || type === this._gl.INT) {\r\n                this._gl.vertexAttribIPointer(indx, size, type, stride, offset);\r\n            } else {\r\n                this._gl.vertexAttribPointer(indx, size, type, normalized, stride, offset);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindIndexBufferWithCache(indexBuffer: Nullable<DataBuffer>): void {\r\n        if (indexBuffer == null) {\r\n            return;\r\n        }\r\n        if (this._cachedIndexBuffer !== indexBuffer) {\r\n            this._cachedIndexBuffer = indexBuffer;\r\n            this.bindIndexBuffer(indexBuffer);\r\n            this._uintIndicesCurrentlySet = indexBuffer.is32Bits;\r\n        }\r\n    }\r\n\r\n    private _bindVertexBuffersAttributes(\r\n        vertexBuffers: { [key: string]: Nullable<VertexBuffer> },\r\n        effect: Effect,\r\n        overrideVertexBuffers?: { [kind: string]: Nullable<VertexBuffer> }\r\n    ): void {\r\n        const attributes = effect.getAttributesNames();\r\n\r\n        if (!this._vaoRecordInProgress) {\r\n            this._unbindVertexArrayObject();\r\n        }\r\n\r\n        this.unbindAllAttributes();\r\n\r\n        for (let index = 0; index < attributes.length; index++) {\r\n            const order = effect.getAttributeLocation(index);\r\n\r\n            if (order >= 0) {\r\n                const ai = attributes[index];\r\n                let vertexBuffer: Nullable<VertexBuffer> = null;\r\n\r\n                if (overrideVertexBuffers) {\r\n                    vertexBuffer = overrideVertexBuffers[ai];\r\n                }\r\n\r\n                if (!vertexBuffer) {\r\n                    vertexBuffer = vertexBuffers[ai];\r\n                }\r\n\r\n                if (!vertexBuffer) {\r\n                    continue;\r\n                }\r\n\r\n                this._gl.enableVertexAttribArray(order);\r\n                if (!this._vaoRecordInProgress) {\r\n                    this._vertexAttribArraysEnabled[order] = true;\r\n                }\r\n\r\n                const buffer = vertexBuffer.getBuffer();\r\n                if (buffer) {\r\n                    this._vertexAttribPointer(buffer, order, vertexBuffer.getSize(), vertexBuffer.type, vertexBuffer.normalized, vertexBuffer.byteStride, vertexBuffer.byteOffset);\r\n\r\n                    if (vertexBuffer.getIsInstanced()) {\r\n                        this._gl.vertexAttribDivisor(order, vertexBuffer.getInstanceDivisor());\r\n                        if (!this._vaoRecordInProgress) {\r\n                            this._currentInstanceLocations.push(order);\r\n                            this._currentInstanceBuffers.push(buffer);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Records a vertex array object\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2#vertex-array-objects\r\n     * @param vertexBuffers defines the list of vertex buffers to store\r\n     * @param indexBuffer defines the index buffer to store\r\n     * @param effect defines the effect to store\r\n     * @param overrideVertexBuffers defines optional list of avertex buffers that overrides the entries in vertexBuffers\r\n     * @returns the new vertex array object\r\n     */\r\n    public recordVertexArrayObject(\r\n        vertexBuffers: { [key: string]: VertexBuffer },\r\n        indexBuffer: Nullable<DataBuffer>,\r\n        effect: Effect,\r\n        overrideVertexBuffers?: { [kind: string]: Nullable<VertexBuffer> }\r\n    ): WebGLVertexArrayObject {\r\n        const vao = this._gl.createVertexArray();\r\n\r\n        if (!vao) {\r\n            throw new Error(\"Unable to create VAO\");\r\n        }\r\n\r\n        this._vaoRecordInProgress = true;\r\n\r\n        this._gl.bindVertexArray(vao);\r\n\r\n        this._mustWipeVertexAttributes = true;\r\n        this._bindVertexBuffersAttributes(vertexBuffers, effect, overrideVertexBuffers);\r\n\r\n        this.bindIndexBuffer(indexBuffer);\r\n\r\n        this._vaoRecordInProgress = false;\r\n        this._gl.bindVertexArray(null);\r\n\r\n        return vao;\r\n    }\r\n\r\n    /**\r\n     * Bind a specific vertex array object\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2#vertex-array-objects\r\n     * @param vertexArrayObject defines the vertex array object to bind\r\n     * @param indexBuffer defines the index buffer to bind\r\n     */\r\n    public bindVertexArrayObject(vertexArrayObject: WebGLVertexArrayObject, indexBuffer: Nullable<DataBuffer>): void {\r\n        if (this._cachedVertexArrayObject !== vertexArrayObject) {\r\n            this._cachedVertexArrayObject = vertexArrayObject;\r\n\r\n            this._gl.bindVertexArray(vertexArrayObject);\r\n            this._cachedVertexBuffers = null;\r\n            this._cachedIndexBuffer = null;\r\n\r\n            this._uintIndicesCurrentlySet = indexBuffer != null && indexBuffer.is32Bits;\r\n            this._mustWipeVertexAttributes = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Bind webGl buffers directly to the webGL context\r\n     * @param vertexBuffer defines the vertex buffer to bind\r\n     * @param indexBuffer defines the index buffer to bind\r\n     * @param vertexDeclaration defines the vertex declaration to use with the vertex buffer\r\n     * @param vertexStrideSize defines the vertex stride of the vertex buffer\r\n     * @param effect defines the effect associated with the vertex buffer\r\n     */\r\n    public bindBuffersDirectly(vertexBuffer: DataBuffer, indexBuffer: DataBuffer, vertexDeclaration: number[], vertexStrideSize: number, effect: Effect): void {\r\n        if (this._cachedVertexBuffers !== vertexBuffer || this._cachedEffectForVertexBuffers !== effect) {\r\n            this._cachedVertexBuffers = vertexBuffer;\r\n            this._cachedEffectForVertexBuffers = effect;\r\n\r\n            const attributesCount = effect.getAttributesCount();\r\n\r\n            this._unbindVertexArrayObject();\r\n            this.unbindAllAttributes();\r\n\r\n            let offset = 0;\r\n            for (let index = 0; index < attributesCount; index++) {\r\n                if (index < vertexDeclaration.length) {\r\n                    const order = effect.getAttributeLocation(index);\r\n\r\n                    if (order >= 0) {\r\n                        this._gl.enableVertexAttribArray(order);\r\n                        this._vertexAttribArraysEnabled[order] = true;\r\n                        this._vertexAttribPointer(vertexBuffer, order, vertexDeclaration[index], this._gl.FLOAT, false, vertexStrideSize, offset);\r\n                    }\r\n\r\n                    offset += vertexDeclaration[index] * 4;\r\n                }\r\n            }\r\n        }\r\n\r\n        this._bindIndexBufferWithCache(indexBuffer);\r\n    }\r\n\r\n    private _unbindVertexArrayObject(): void {\r\n        if (!this._cachedVertexArrayObject) {\r\n            return;\r\n        }\r\n\r\n        this._cachedVertexArrayObject = null;\r\n        this._gl.bindVertexArray(null);\r\n    }\r\n\r\n    /**\r\n     * Bind a list of vertex buffers to the webGL context\r\n     * @param vertexBuffers defines the list of vertex buffers to bind\r\n     * @param indexBuffer defines the index buffer to bind\r\n     * @param effect defines the effect associated with the vertex buffers\r\n     * @param overrideVertexBuffers defines optional list of avertex buffers that overrides the entries in vertexBuffers\r\n     */\r\n    public bindBuffers(\r\n        vertexBuffers: { [key: string]: Nullable<VertexBuffer> },\r\n        indexBuffer: Nullable<DataBuffer>,\r\n        effect: Effect,\r\n        overrideVertexBuffers?: { [kind: string]: Nullable<VertexBuffer> }\r\n    ): void {\r\n        if (this._cachedVertexBuffers !== vertexBuffers || this._cachedEffectForVertexBuffers !== effect) {\r\n            this._cachedVertexBuffers = vertexBuffers;\r\n            this._cachedEffectForVertexBuffers = effect;\r\n\r\n            this._bindVertexBuffersAttributes(vertexBuffers, effect, overrideVertexBuffers);\r\n        }\r\n\r\n        this._bindIndexBufferWithCache(indexBuffer);\r\n    }\r\n\r\n    /**\r\n     * Unbind all instance attributes\r\n     */\r\n    public unbindInstanceAttributes() {\r\n        let boundBuffer;\r\n        for (let i = 0, ul = this._currentInstanceLocations.length; i < ul; i++) {\r\n            const instancesBuffer = this._currentInstanceBuffers[i];\r\n            if (boundBuffer != instancesBuffer && instancesBuffer.references) {\r\n                boundBuffer = instancesBuffer;\r\n                this.bindArrayBuffer(instancesBuffer);\r\n            }\r\n            const offsetLocation = this._currentInstanceLocations[i];\r\n            this._gl.vertexAttribDivisor(offsetLocation, 0);\r\n        }\r\n        this._currentInstanceBuffers.length = 0;\r\n        this._currentInstanceLocations.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Release and free the memory of a vertex array object\r\n     * @param vao defines the vertex array object to delete\r\n     */\r\n    public releaseVertexArrayObject(vao: WebGLVertexArrayObject) {\r\n        this._gl.deleteVertexArray(vao);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseBuffer(buffer: DataBuffer): boolean {\r\n        buffer.references--;\r\n\r\n        if (buffer.references === 0) {\r\n            this._deleteBuffer(buffer);\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    protected _deleteBuffer(buffer: DataBuffer): void {\r\n        this._gl.deleteBuffer(buffer.underlyingResource);\r\n    }\r\n\r\n    /**\r\n     * Update the content of a webGL buffer used with instantiation and bind it to the webGL context\r\n     * @param instancesBuffer defines the webGL buffer to update and bind\r\n     * @param data defines the data to store in the buffer\r\n     * @param offsetLocations defines the offsets or attributes information used to determine where data must be stored in the buffer\r\n     */\r\n    public updateAndBindInstancesBuffer(instancesBuffer: DataBuffer, data: Float32Array, offsetLocations: number[] | InstancingAttributeInfo[]): void {\r\n        this.bindArrayBuffer(instancesBuffer);\r\n        if (data) {\r\n            this._gl.bufferSubData(this._gl.ARRAY_BUFFER, 0, data);\r\n        }\r\n\r\n        if ((<any>offsetLocations[0]).index !== undefined) {\r\n            this.bindInstancesBuffer(instancesBuffer, offsetLocations as any, true);\r\n        } else {\r\n            for (let index = 0; index < 4; index++) {\r\n                const offsetLocation = <number>offsetLocations[index];\r\n\r\n                if (!this._vertexAttribArraysEnabled[offsetLocation]) {\r\n                    this._gl.enableVertexAttribArray(offsetLocation);\r\n                    this._vertexAttribArraysEnabled[offsetLocation] = true;\r\n                }\r\n\r\n                this._vertexAttribPointer(instancesBuffer, offsetLocation, 4, this._gl.FLOAT, false, 64, index * 16);\r\n                this._gl.vertexAttribDivisor(offsetLocation, 1);\r\n                this._currentInstanceLocations.push(offsetLocation);\r\n                this._currentInstanceBuffers.push(instancesBuffer);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Bind the content of a webGL buffer used with instantiation\r\n     * @param instancesBuffer defines the webGL buffer to bind\r\n     * @param attributesInfo defines the offsets or attributes information used to determine where data must be stored in the buffer\r\n     * @param computeStride defines Whether to compute the strides from the info or use the default 0\r\n     */\r\n    public bindInstancesBuffer(instancesBuffer: DataBuffer, attributesInfo: InstancingAttributeInfo[], computeStride = true): void {\r\n        this.bindArrayBuffer(instancesBuffer);\r\n\r\n        let stride = 0;\r\n        if (computeStride) {\r\n            for (let i = 0; i < attributesInfo.length; i++) {\r\n                const ai = attributesInfo[i];\r\n                stride += ai.attributeSize * 4;\r\n            }\r\n        }\r\n\r\n        for (let i = 0; i < attributesInfo.length; i++) {\r\n            const ai = attributesInfo[i];\r\n            if (ai.index === undefined) {\r\n                ai.index = this._currentEffect!.getAttributeLocationByName(ai.attributeName);\r\n            }\r\n\r\n            if (ai.index < 0) {\r\n                continue;\r\n            }\r\n\r\n            if (!this._vertexAttribArraysEnabled[ai.index]) {\r\n                this._gl.enableVertexAttribArray(ai.index);\r\n                this._vertexAttribArraysEnabled[ai.index] = true;\r\n            }\r\n\r\n            this._vertexAttribPointer(instancesBuffer, ai.index, ai.attributeSize, ai.attributeType || this._gl.FLOAT, ai.normalized || false, stride, ai.offset);\r\n            this._gl.vertexAttribDivisor(ai.index, ai.divisor === undefined ? 1 : ai.divisor);\r\n            this._currentInstanceLocations.push(ai.index);\r\n            this._currentInstanceBuffers.push(instancesBuffer);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disable the instance attribute corresponding to the name in parameter\r\n     * @param name defines the name of the attribute to disable\r\n     */\r\n    public disableInstanceAttributeByName(name: string) {\r\n        if (!this._currentEffect) {\r\n            return;\r\n        }\r\n\r\n        const attributeLocation = this._currentEffect.getAttributeLocationByName(name);\r\n        this.disableInstanceAttribute(attributeLocation);\r\n    }\r\n\r\n    /**\r\n     * Disable the instance attribute corresponding to the location in parameter\r\n     * @param attributeLocation defines the attribute location of the attribute to disable\r\n     */\r\n    public disableInstanceAttribute(attributeLocation: number) {\r\n        let shouldClean = false;\r\n        let index: number;\r\n        while ((index = this._currentInstanceLocations.indexOf(attributeLocation)) !== -1) {\r\n            this._currentInstanceLocations.splice(index, 1);\r\n            this._currentInstanceBuffers.splice(index, 1);\r\n\r\n            shouldClean = true;\r\n            index = this._currentInstanceLocations.indexOf(attributeLocation);\r\n        }\r\n\r\n        if (shouldClean) {\r\n            this._gl.vertexAttribDivisor(attributeLocation, 0);\r\n            this.disableAttributeByIndex(attributeLocation);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disable the attribute corresponding to the location in parameter\r\n     * @param attributeLocation defines the attribute location of the attribute to disable\r\n     */\r\n    public disableAttributeByIndex(attributeLocation: number) {\r\n        this._gl.disableVertexAttribArray(attributeLocation);\r\n        this._vertexAttribArraysEnabled[attributeLocation] = false;\r\n        this._currentBufferPointers[attributeLocation].active = false;\r\n    }\r\n\r\n    /**\r\n     * Send a draw order\r\n     * @param useTriangles defines if triangles must be used to draw (else wireframe will be used)\r\n     * @param indexStart defines the starting index\r\n     * @param indexCount defines the number of index to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public draw(useTriangles: boolean, indexStart: number, indexCount: number, instancesCount?: number): void {\r\n        this.drawElementsType(useTriangles ? Constants.MATERIAL_TriangleFillMode : Constants.MATERIAL_WireFrameFillMode, indexStart, indexCount, instancesCount);\r\n    }\r\n\r\n    /**\r\n     * Draw a list of points\r\n     * @param verticesStart defines the index of first vertex to draw\r\n     * @param verticesCount defines the count of vertices to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawPointClouds(verticesStart: number, verticesCount: number, instancesCount?: number): void {\r\n        this.drawArraysType(Constants.MATERIAL_PointFillMode, verticesStart, verticesCount, instancesCount);\r\n    }\r\n\r\n    /**\r\n     * Draw a list of unindexed primitives\r\n     * @param useTriangles defines if triangles must be used to draw (else wireframe will be used)\r\n     * @param verticesStart defines the index of first vertex to draw\r\n     * @param verticesCount defines the count of vertices to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawUnIndexed(useTriangles: boolean, verticesStart: number, verticesCount: number, instancesCount?: number): void {\r\n        this.drawArraysType(useTriangles ? Constants.MATERIAL_TriangleFillMode : Constants.MATERIAL_WireFrameFillMode, verticesStart, verticesCount, instancesCount);\r\n    }\r\n\r\n    /**\r\n     * Draw a list of indexed primitives\r\n     * @param fillMode defines the primitive to use\r\n     * @param indexStart defines the starting index\r\n     * @param indexCount defines the number of index to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawElementsType(fillMode: number, indexStart: number, indexCount: number, instancesCount?: number): void {\r\n        // Apply states\r\n        this.applyStates();\r\n\r\n        this._reportDrawCall();\r\n\r\n        // Render\r\n\r\n        const drawMode = this._drawMode(fillMode);\r\n        const indexFormat = this._uintIndicesCurrentlySet ? this._gl.UNSIGNED_INT : this._gl.UNSIGNED_SHORT;\r\n        const mult = this._uintIndicesCurrentlySet ? 4 : 2;\r\n        if (instancesCount) {\r\n            this._gl.drawElementsInstanced(drawMode, indexCount, indexFormat, indexStart * mult, instancesCount);\r\n        } else {\r\n            this._gl.drawElements(drawMode, indexCount, indexFormat, indexStart * mult);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Draw a list of unindexed primitives\r\n     * @param fillMode defines the primitive to use\r\n     * @param verticesStart defines the index of first vertex to draw\r\n     * @param verticesCount defines the count of vertices to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawArraysType(fillMode: number, verticesStart: number, verticesCount: number, instancesCount?: number): void {\r\n        // Apply states\r\n        this.applyStates();\r\n\r\n        this._reportDrawCall();\r\n\r\n        const drawMode = this._drawMode(fillMode);\r\n        if (instancesCount) {\r\n            this._gl.drawArraysInstanced(drawMode, verticesStart, verticesCount, instancesCount);\r\n        } else {\r\n            this._gl.drawArrays(drawMode, verticesStart, verticesCount);\r\n        }\r\n    }\r\n\r\n    private _drawMode(fillMode: number): number {\r\n        switch (fillMode) {\r\n            // Triangle views\r\n            case Constants.MATERIAL_TriangleFillMode:\r\n                return this._gl.TRIANGLES;\r\n            case Constants.MATERIAL_PointFillMode:\r\n                return this._gl.POINTS;\r\n            case Constants.MATERIAL_WireFrameFillMode:\r\n                return this._gl.LINES;\r\n            // Draw modes\r\n            case Constants.MATERIAL_PointListDrawMode:\r\n                return this._gl.POINTS;\r\n            case Constants.MATERIAL_LineListDrawMode:\r\n                return this._gl.LINES;\r\n            case Constants.MATERIAL_LineLoopDrawMode:\r\n                return this._gl.LINE_LOOP;\r\n            case Constants.MATERIAL_LineStripDrawMode:\r\n                return this._gl.LINE_STRIP;\r\n            case Constants.MATERIAL_TriangleStripDrawMode:\r\n                return this._gl.TRIANGLE_STRIP;\r\n            case Constants.MATERIAL_TriangleFanDrawMode:\r\n                return this._gl.TRIANGLE_FAN;\r\n            default:\r\n                return this._gl.TRIANGLES;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    protected _reportDrawCall() {\r\n        // Will be implemented by children\r\n    }\r\n\r\n    // Shaders\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseEffect(effect: Effect): void {\r\n        if (this._compiledEffects[effect._key]) {\r\n            delete this._compiledEffects[effect._key];\r\n        }\r\n        const pipelineContext = effect.getPipelineContext();\r\n        if (pipelineContext) {\r\n            this._deletePipelineContext(pipelineContext);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _deletePipelineContext(pipelineContext: IPipelineContext): void {\r\n        const webGLPipelineContext = pipelineContext as WebGLPipelineContext;\r\n        if (webGLPipelineContext && webGLPipelineContext.program) {\r\n            webGLPipelineContext.program.__SPECTOR_rebuildProgram = null;\r\n\r\n            this._gl.deleteProgram(webGLPipelineContext.program);\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _getGlobalDefines(defines?: { [key: string]: string }): string | undefined {\r\n        if (defines) {\r\n            if (this.isNDCHalfZRange) {\r\n                defines[\"IS_NDC_HALF_ZRANGE\"] = \"\";\r\n            } else {\r\n                delete defines[\"IS_NDC_HALF_ZRANGE\"];\r\n            }\r\n            if (this.useReverseDepthBuffer) {\r\n                defines[\"USE_REVERSE_DEPTHBUFFER\"] = \"\";\r\n            } else {\r\n                delete defines[\"USE_REVERSE_DEPTHBUFFER\"];\r\n            }\r\n            if (this.useExactSrgbConversions) {\r\n                defines[\"USE_EXACT_SRGB_CONVERSIONS\"] = \"\";\r\n            } else {\r\n                delete defines[\"USE_EXACT_SRGB_CONVERSIONS\"];\r\n            }\r\n            return;\r\n        } else {\r\n            let s = \"\";\r\n            if (this.isNDCHalfZRange) {\r\n                s += \"#define IS_NDC_HALF_ZRANGE\";\r\n            }\r\n            if (this.useReverseDepthBuffer) {\r\n                if (s) {\r\n                    s += \"\\n\";\r\n                }\r\n                s += \"#define USE_REVERSE_DEPTHBUFFER\";\r\n            }\r\n            if (this.useExactSrgbConversions) {\r\n                if (s) {\r\n                    s += \"\\n\";\r\n                }\r\n                s += \"#define USE_EXACT_SRGB_CONVERSIONS\";\r\n            }\r\n            return s;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create a new effect (used to store vertex/fragment shaders)\r\n     * @param baseName defines the base name of the effect (The name of file without .fragment.fx or .vertex.fx)\r\n     * @param attributesNamesOrOptions defines either a list of attribute names or an IEffectCreationOptions object\r\n     * @param uniformsNamesOrEngine defines either a list of uniform names or the engine to use\r\n     * @param samplers defines an array of string used to represent textures\r\n     * @param defines defines the string containing the defines to use to compile the shaders\r\n     * @param fallbacks defines the list of potential fallbacks to use if shader compilation fails\r\n     * @param onCompiled defines a function to call when the effect creation is successful\r\n     * @param onError defines a function to call when the effect creation has failed\r\n     * @param indexParameters defines an object containing the index values to use to compile shaders (like the maximum number of simultaneous lights)\r\n     * @param shaderLanguage the language the shader is written in (default: GLSL)\r\n     * @returns the new Effect\r\n     */\r\n    public createEffect(\r\n        baseName: any,\r\n        attributesNamesOrOptions: string[] | IEffectCreationOptions,\r\n        uniformsNamesOrEngine: string[] | ThinEngine,\r\n        samplers?: string[],\r\n        defines?: string,\r\n        fallbacks?: IEffectFallbacks,\r\n        onCompiled?: Nullable<(effect: Effect) => void>,\r\n        onError?: Nullable<(effect: Effect, errors: string) => void>,\r\n        indexParameters?: any,\r\n        shaderLanguage = ShaderLanguage.GLSL\r\n    ): Effect {\r\n        const vertex = baseName.vertexElement || baseName.vertex || baseName.vertexToken || baseName.vertexSource || baseName;\r\n        const fragment = baseName.fragmentElement || baseName.fragment || baseName.fragmentToken || baseName.fragmentSource || baseName;\r\n        const globalDefines = this._getGlobalDefines()!;\r\n\r\n        let fullDefines = defines ?? (<IEffectCreationOptions>attributesNamesOrOptions).defines ?? \"\";\r\n\r\n        if (globalDefines) {\r\n            fullDefines += globalDefines;\r\n        }\r\n\r\n        const name = vertex + \"+\" + fragment + \"@\" + fullDefines;\r\n        if (this._compiledEffects[name]) {\r\n            const compiledEffect = <Effect>this._compiledEffects[name];\r\n            if (onCompiled && compiledEffect.isReady()) {\r\n                onCompiled(compiledEffect);\r\n            }\r\n\r\n            return compiledEffect;\r\n        }\r\n        const effect = new Effect(\r\n            baseName,\r\n            attributesNamesOrOptions,\r\n            uniformsNamesOrEngine,\r\n            samplers,\r\n            this,\r\n            defines,\r\n            fallbacks,\r\n            onCompiled,\r\n            onError,\r\n            indexParameters,\r\n            name,\r\n            shaderLanguage\r\n        );\r\n        this._compiledEffects[name] = effect;\r\n\r\n        return effect;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected static _ConcatenateShader(source: string, defines: Nullable<string>, shaderVersion: string = \"\"): string {\r\n        return shaderVersion + (defines ? defines + \"\\n\" : \"\") + source;\r\n    }\r\n\r\n    private _compileShader(source: string, type: string, defines: Nullable<string>, shaderVersion: string): WebGLShader {\r\n        return this._compileRawShader(ThinEngine._ConcatenateShader(source, defines, shaderVersion), type);\r\n    }\r\n\r\n    private _compileRawShader(source: string, type: string): WebGLShader {\r\n        const gl = this._gl;\r\n\r\n        const shader = gl.createShader(type === \"vertex\" ? gl.VERTEX_SHADER : gl.FRAGMENT_SHADER);\r\n\r\n        if (!shader) {\r\n            let error: GLenum = gl.NO_ERROR;\r\n            let tempError: GLenum = gl.NO_ERROR;\r\n            while ((tempError = gl.getError()) !== gl.NO_ERROR) {\r\n                error = tempError;\r\n            }\r\n\r\n            throw new Error(\r\n                `Something went wrong while creating a gl ${type} shader object. gl error=${error}, gl isContextLost=${gl.isContextLost()}, _contextWasLost=${this._contextWasLost}`\r\n            );\r\n        }\r\n\r\n        gl.shaderSource(shader, source);\r\n        gl.compileShader(shader);\r\n\r\n        return shader;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getShaderSource(shader: WebGLShader): Nullable<string> {\r\n        return this._gl.getShaderSource(shader);\r\n    }\r\n\r\n    /**\r\n     * Directly creates a webGL program\r\n     * @param pipelineContext  defines the pipeline context to attach to\r\n     * @param vertexCode defines the vertex shader code to use\r\n     * @param fragmentCode defines the fragment shader code to use\r\n     * @param context defines the webGL context to use (if not set, the current one will be used)\r\n     * @param transformFeedbackVaryings defines the list of transform feedback varyings to use\r\n     * @returns the new webGL program\r\n     */\r\n    public createRawShaderProgram(\r\n        pipelineContext: IPipelineContext,\r\n        vertexCode: string,\r\n        fragmentCode: string,\r\n        context?: WebGLRenderingContext,\r\n        transformFeedbackVaryings: Nullable<string[]> = null\r\n    ): WebGLProgram {\r\n        context = context || this._gl;\r\n\r\n        const vertexShader = this._compileRawShader(vertexCode, \"vertex\");\r\n        const fragmentShader = this._compileRawShader(fragmentCode, \"fragment\");\r\n\r\n        return this._createShaderProgram(pipelineContext as WebGLPipelineContext, vertexShader, fragmentShader, context, transformFeedbackVaryings);\r\n    }\r\n\r\n    /**\r\n     * Creates a webGL program\r\n     * @param pipelineContext  defines the pipeline context to attach to\r\n     * @param vertexCode  defines the vertex shader code to use\r\n     * @param fragmentCode defines the fragment shader code to use\r\n     * @param defines defines the string containing the defines to use to compile the shaders\r\n     * @param context defines the webGL context to use (if not set, the current one will be used)\r\n     * @param transformFeedbackVaryings defines the list of transform feedback varyings to use\r\n     * @returns the new webGL program\r\n     */\r\n    public createShaderProgram(\r\n        pipelineContext: IPipelineContext,\r\n        vertexCode: string,\r\n        fragmentCode: string,\r\n        defines: Nullable<string>,\r\n        context?: WebGLRenderingContext,\r\n        transformFeedbackVaryings: Nullable<string[]> = null\r\n    ): WebGLProgram {\r\n        context = context || this._gl;\r\n\r\n        const shaderVersion = this._webGLVersion > 1 ? \"#version 300 es\\n#define WEBGL2 \\n\" : \"\";\r\n        const vertexShader = this._compileShader(vertexCode, \"vertex\", defines, shaderVersion);\r\n        const fragmentShader = this._compileShader(fragmentCode, \"fragment\", defines, shaderVersion);\r\n\r\n        return this._createShaderProgram(pipelineContext as WebGLPipelineContext, vertexShader, fragmentShader, context, transformFeedbackVaryings);\r\n    }\r\n\r\n    /**\r\n     * Inline functions in shader code that are marked to be inlined\r\n     * @param code code to inline\r\n     * @returns inlined code\r\n     */\r\n    public inlineShaderCode(code: string): string {\r\n        // no inlining needed in the WebGL engine\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * Creates a new pipeline context\r\n     * @param shaderProcessingContext defines the shader processing context used during the processing if available\r\n     * @returns the new pipeline\r\n     */\r\n    public createPipelineContext(shaderProcessingContext: Nullable<ShaderProcessingContext>): IPipelineContext {\r\n        const pipelineContext = new WebGLPipelineContext();\r\n        pipelineContext.engine = this;\r\n\r\n        if (this._caps.parallelShaderCompile) {\r\n            pipelineContext.isParallelCompiled = true;\r\n        }\r\n\r\n        return pipelineContext;\r\n    }\r\n\r\n    /**\r\n     * Creates a new material context\r\n     * @returns the new context\r\n     */\r\n    public createMaterialContext(): IMaterialContext | undefined {\r\n        return undefined;\r\n    }\r\n\r\n    /**\r\n     * Creates a new draw context\r\n     * @returns the new context\r\n     */\r\n    public createDrawContext(): IDrawContext | undefined {\r\n        return undefined;\r\n    }\r\n\r\n    protected _createShaderProgram(\r\n        pipelineContext: WebGLPipelineContext,\r\n        vertexShader: WebGLShader,\r\n        fragmentShader: WebGLShader,\r\n        context: WebGLRenderingContext,\r\n        transformFeedbackVaryings: Nullable<string[]> = null\r\n    ): WebGLProgram {\r\n        const shaderProgram = context.createProgram();\r\n        pipelineContext.program = shaderProgram;\r\n\r\n        if (!shaderProgram) {\r\n            throw new Error(\"Unable to create program\");\r\n        }\r\n\r\n        context.attachShader(shaderProgram, vertexShader);\r\n        context.attachShader(shaderProgram, fragmentShader);\r\n\r\n        context.linkProgram(shaderProgram);\r\n\r\n        pipelineContext.context = context;\r\n        pipelineContext.vertexShader = vertexShader;\r\n        pipelineContext.fragmentShader = fragmentShader;\r\n\r\n        if (!pipelineContext.isParallelCompiled) {\r\n            this._finalizePipelineContext(pipelineContext);\r\n        }\r\n\r\n        return shaderProgram;\r\n    }\r\n\r\n    protected _finalizePipelineContext(pipelineContext: WebGLPipelineContext) {\r\n        const context = pipelineContext.context!;\r\n        const vertexShader = pipelineContext.vertexShader!;\r\n        const fragmentShader = pipelineContext.fragmentShader!;\r\n        const program = pipelineContext.program!;\r\n\r\n        const linked = context.getProgramParameter(program, context.LINK_STATUS);\r\n        if (!linked) {\r\n            // Get more info\r\n            // Vertex\r\n            if (!this._gl.getShaderParameter(vertexShader, this._gl.COMPILE_STATUS)) {\r\n                const log = this._gl.getShaderInfoLog(vertexShader);\r\n                if (log) {\r\n                    pipelineContext.vertexCompilationError = log;\r\n                    throw new Error(\"VERTEX SHADER \" + log);\r\n                }\r\n            }\r\n\r\n            // Fragment\r\n            if (!this._gl.getShaderParameter(fragmentShader, this._gl.COMPILE_STATUS)) {\r\n                const log = this._gl.getShaderInfoLog(fragmentShader);\r\n                if (log) {\r\n                    pipelineContext.fragmentCompilationError = log;\r\n                    throw new Error(\"FRAGMENT SHADER \" + log);\r\n                }\r\n            }\r\n\r\n            const error = context.getProgramInfoLog(program);\r\n            if (error) {\r\n                pipelineContext.programLinkError = error;\r\n                throw new Error(error);\r\n            }\r\n        }\r\n\r\n        if (this.validateShaderPrograms) {\r\n            context.validateProgram(program);\r\n            const validated = context.getProgramParameter(program, context.VALIDATE_STATUS);\r\n\r\n            if (!validated) {\r\n                const error = context.getProgramInfoLog(program);\r\n                if (error) {\r\n                    pipelineContext.programValidationError = error;\r\n                    throw new Error(error);\r\n                }\r\n            }\r\n        }\r\n\r\n        context.deleteShader(vertexShader);\r\n        context.deleteShader(fragmentShader);\r\n\r\n        pipelineContext.vertexShader = undefined;\r\n        pipelineContext.fragmentShader = undefined;\r\n\r\n        if (pipelineContext.onCompiled) {\r\n            pipelineContext.onCompiled();\r\n            pipelineContext.onCompiled = undefined;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _preparePipelineContext(\r\n        pipelineContext: IPipelineContext,\r\n        vertexSourceCode: string,\r\n        fragmentSourceCode: string,\r\n        createAsRaw: boolean,\r\n        rawVertexSourceCode: string,\r\n        rawFragmentSourceCode: string,\r\n        rebuildRebind: any,\r\n        defines: Nullable<string>,\r\n        transformFeedbackVaryings: Nullable<string[]>,\r\n        key: string\r\n    ) {\r\n        const webGLRenderingState = pipelineContext as WebGLPipelineContext;\r\n\r\n        if (createAsRaw) {\r\n            webGLRenderingState.program = this.createRawShaderProgram(webGLRenderingState, vertexSourceCode, fragmentSourceCode, undefined, transformFeedbackVaryings);\r\n        } else {\r\n            webGLRenderingState.program = this.createShaderProgram(webGLRenderingState, vertexSourceCode, fragmentSourceCode, defines, undefined, transformFeedbackVaryings);\r\n        }\r\n        webGLRenderingState.program.__SPECTOR_rebuildProgram = rebuildRebind;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _isRenderingStateCompiled(pipelineContext: IPipelineContext): boolean {\r\n        const webGLPipelineContext = pipelineContext as WebGLPipelineContext;\r\n        if (this._isDisposed || webGLPipelineContext._isDisposed) {\r\n            return false;\r\n        }\r\n        if (this._gl.getProgramParameter(webGLPipelineContext.program!, this._caps.parallelShaderCompile!.COMPLETION_STATUS_KHR)) {\r\n            this._finalizePipelineContext(webGLPipelineContext);\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _executeWhenRenderingStateIsCompiled(pipelineContext: IPipelineContext, action: () => void) {\r\n        const webGLPipelineContext = pipelineContext as WebGLPipelineContext;\r\n\r\n        if (!webGLPipelineContext.isParallelCompiled) {\r\n            action();\r\n            return;\r\n        }\r\n\r\n        const oldHandler = webGLPipelineContext.onCompiled;\r\n\r\n        if (oldHandler) {\r\n            webGLPipelineContext.onCompiled = () => {\r\n                oldHandler!();\r\n                action();\r\n            };\r\n        } else {\r\n            webGLPipelineContext.onCompiled = action;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the list of webGL uniform locations associated with a specific program based on a list of uniform names\r\n     * @param pipelineContext defines the pipeline context to use\r\n     * @param uniformsNames defines the list of uniform names\r\n     * @returns an array of webGL uniform locations\r\n     */\r\n    public getUniforms(pipelineContext: IPipelineContext, uniformsNames: string[]): Nullable<WebGLUniformLocation>[] {\r\n        const results = new Array<Nullable<WebGLUniformLocation>>();\r\n        const webGLPipelineContext = pipelineContext as WebGLPipelineContext;\r\n\r\n        for (let index = 0; index < uniformsNames.length; index++) {\r\n            results.push(this._gl.getUniformLocation(webGLPipelineContext.program!, uniformsNames[index]));\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of active attributes for a given webGL program\r\n     * @param pipelineContext defines the pipeline context to use\r\n     * @param attributesNames defines the list of attribute names to get\r\n     * @returns an array of indices indicating the offset of each attribute\r\n     */\r\n    public getAttributes(pipelineContext: IPipelineContext, attributesNames: string[]): number[] {\r\n        const results = [];\r\n        const webGLPipelineContext = pipelineContext as WebGLPipelineContext;\r\n\r\n        for (let index = 0; index < attributesNames.length; index++) {\r\n            try {\r\n                results.push(this._gl.getAttribLocation(webGLPipelineContext.program!, attributesNames[index]));\r\n            } catch (e) {\r\n                results.push(-1);\r\n            }\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Activates an effect, making it the current one (ie. the one used for rendering)\r\n     * @param effect defines the effect to activate\r\n     */\r\n    public enableEffect(effect: Nullable<Effect | DrawWrapper>): void {\r\n        effect = effect !== null && DrawWrapper.IsWrapper(effect) ? effect.effect : effect; // get only the effect, we don't need a Wrapper in the WebGL engine\r\n\r\n        if (!effect || effect === this._currentEffect) {\r\n            return;\r\n        }\r\n\r\n        this._stencilStateComposer.stencilMaterial = undefined;\r\n\r\n        effect = effect as Effect;\r\n\r\n        // Use program\r\n        this.bindSamplers(effect);\r\n\r\n        this._currentEffect = effect;\r\n\r\n        if (effect.onBind) {\r\n            effect.onBind(effect);\r\n        }\r\n        if (effect._onBindObservable) {\r\n            effect._onBindObservable.notifyObservers(effect);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a number (int)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param value defines the int number to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setInt(uniform: Nullable<WebGLUniformLocation>, value: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform1i(uniform, value);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a int2\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setInt2(uniform: Nullable<WebGLUniformLocation>, x: number, y: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform2i(uniform, x, y);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a int3\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setInt3(uniform: Nullable<WebGLUniformLocation>, x: number, y: number, z: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform3i(uniform, x, y, z);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a int4\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @param w defines the 4th component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setInt4(uniform: Nullable<WebGLUniformLocation>, x: number, y: number, z: number, w: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform4i(uniform, x, y, z, w);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of int32\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setIntArray(uniform: Nullable<WebGLUniformLocation>, array: Int32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform1iv(uniform, array);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of int32 (stored as vec2)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setIntArray2(uniform: Nullable<WebGLUniformLocation>, array: Int32Array): boolean {\r\n        if (!uniform || array.length % 2 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform2iv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of int32 (stored as vec3)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setIntArray3(uniform: Nullable<WebGLUniformLocation>, array: Int32Array): boolean {\r\n        if (!uniform || array.length % 3 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform3iv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of int32 (stored as vec4)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setIntArray4(uniform: Nullable<WebGLUniformLocation>, array: Int32Array): boolean {\r\n        if (!uniform || array.length % 4 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform4iv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a number (unsigned int)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param value defines the unsigned int number to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setUInt(uniform: Nullable<WebGLUniformLocation>, value: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform1ui(uniform, value);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a unsigned int2\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setUInt2(uniform: Nullable<WebGLUniformLocation>, x: number, y: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform2ui(uniform, x, y);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a unsigned int3\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setUInt3(uniform: Nullable<WebGLUniformLocation>, x: number, y: number, z: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform3ui(uniform, x, y, z);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a unsigned int4\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @param w defines the 4th component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setUInt4(uniform: Nullable<WebGLUniformLocation>, x: number, y: number, z: number, w: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform4ui(uniform, x, y, z, w);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of unsigned int32\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of unsigned int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setUIntArray(uniform: Nullable<WebGLUniformLocation>, array: Uint32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform1uiv(uniform, array);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of unsigned int32 (stored as vec2)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of unsigned int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setUIntArray2(uniform: Nullable<WebGLUniformLocation>, array: Uint32Array): boolean {\r\n        if (!uniform || array.length % 2 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform2uiv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of unsigned int32 (stored as vec3)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of unsigned int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setUIntArray3(uniform: Nullable<WebGLUniformLocation>, array: Uint32Array): boolean {\r\n        if (!uniform || array.length % 3 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform3uiv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of unsigned int32 (stored as vec4)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of unsigned int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setUIntArray4(uniform: Nullable<WebGLUniformLocation>, array: Uint32Array): boolean {\r\n        if (!uniform || array.length % 4 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform4uiv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of number\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of number to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setArray(uniform: Nullable<WebGLUniformLocation>, array: number[] | Float32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        if (array.length < 1) {\r\n            return false;\r\n        }\r\n        this._gl.uniform1fv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of number (stored as vec2)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of number to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setArray2(uniform: Nullable<WebGLUniformLocation>, array: number[] | Float32Array): boolean {\r\n        if (!uniform || array.length % 2 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform2fv(uniform, <any>array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of number (stored as vec3)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of number to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setArray3(uniform: Nullable<WebGLUniformLocation>, array: number[] | Float32Array): boolean {\r\n        if (!uniform || array.length % 3 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform3fv(uniform, <any>array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of number (stored as vec4)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of number to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setArray4(uniform: Nullable<WebGLUniformLocation>, array: number[] | Float32Array): boolean {\r\n        if (!uniform || array.length % 4 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform4fv(uniform, <any>array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of float32 (stored as matrices)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param matrices defines the array of float32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setMatrices(uniform: Nullable<WebGLUniformLocation>, matrices: DeepImmutable<FloatArray>): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniformMatrix4fv(uniform, false, matrices);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a matrix (3x3)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param matrix defines the Float32Array representing the 3x3 matrix to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setMatrix3x3(uniform: Nullable<WebGLUniformLocation>, matrix: Float32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniformMatrix3fv(uniform, false, matrix);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a matrix (2x2)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param matrix defines the Float32Array representing the 2x2 matrix to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setMatrix2x2(uniform: Nullable<WebGLUniformLocation>, matrix: Float32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniformMatrix2fv(uniform, false, matrix);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a number (float)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param value defines the float number to store\r\n     * @returns true if the value was transferred\r\n     */\r\n    public setFloat(uniform: Nullable<WebGLUniformLocation>, value: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform1f(uniform, value);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a vec2\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setFloat2(uniform: Nullable<WebGLUniformLocation>, x: number, y: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform2f(uniform, x, y);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a vec3\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setFloat3(uniform: Nullable<WebGLUniformLocation>, x: number, y: number, z: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform3f(uniform, x, y, z);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a vec4\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @param w defines the 4th component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setFloat4(uniform: Nullable<WebGLUniformLocation>, x: number, y: number, z: number, w: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform4f(uniform, x, y, z, w);\r\n\r\n        return true;\r\n    }\r\n\r\n    // States\r\n\r\n    /**\r\n     * Apply all cached states (depth, culling, stencil and alpha)\r\n     */\r\n    public applyStates() {\r\n        this._depthCullingState.apply(this._gl);\r\n        this._stencilStateComposer.apply(this._gl);\r\n        this._alphaState.apply(this._gl);\r\n\r\n        if (this._colorWriteChanged) {\r\n            this._colorWriteChanged = false;\r\n            const enable = this._colorWrite;\r\n            this._gl.colorMask(enable, enable, enable, enable);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enable or disable color writing\r\n     * @param enable defines the state to set\r\n     */\r\n    public setColorWrite(enable: boolean): void {\r\n        if (enable !== this._colorWrite) {\r\n            this._colorWriteChanged = true;\r\n            this._colorWrite = enable;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if color writing is enabled\r\n     * @returns the current color writing state\r\n     */\r\n    public getColorWrite(): boolean {\r\n        return this._colorWrite;\r\n    }\r\n\r\n    /**\r\n     * Gets the depth culling state manager\r\n     */\r\n    public get depthCullingState(): DepthCullingState {\r\n        return this._depthCullingState;\r\n    }\r\n\r\n    /**\r\n     * Gets the alpha state manager\r\n     */\r\n    public get alphaState(): AlphaState {\r\n        return this._alphaState;\r\n    }\r\n\r\n    /**\r\n     * Gets the stencil state manager\r\n     */\r\n    public get stencilState(): StencilState {\r\n        return this._stencilState;\r\n    }\r\n\r\n    /**\r\n     * Gets the stencil state composer\r\n     */\r\n    public get stencilStateComposer(): StencilStateComposer {\r\n        return this._stencilStateComposer;\r\n    }\r\n\r\n    // Textures\r\n\r\n    /**\r\n     * Clears the list of texture accessible through engine.\r\n     * This can help preventing texture load conflict due to name collision.\r\n     */\r\n    public clearInternalTexturesCache() {\r\n        this._internalTexturesCache.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Force the entire cache to be cleared\r\n     * You should not have to use this function unless your engine needs to share the webGL context with another engine\r\n     * @param bruteForce defines a boolean to force clearing ALL caches (including stencil, detoh and alpha states)\r\n     */\r\n    public wipeCaches(bruteForce?: boolean): void {\r\n        if (this.preventCacheWipeBetweenFrames && !bruteForce) {\r\n            return;\r\n        }\r\n        this._currentEffect = null;\r\n        this._viewportCached.x = 0;\r\n        this._viewportCached.y = 0;\r\n        this._viewportCached.z = 0;\r\n        this._viewportCached.w = 0;\r\n\r\n        // Done before in case we clean the attributes\r\n        this._unbindVertexArrayObject();\r\n\r\n        if (bruteForce) {\r\n            this._currentProgram = null;\r\n            this.resetTextureCache();\r\n\r\n            this._stencilStateComposer.reset();\r\n\r\n            this._depthCullingState.reset();\r\n            this._depthCullingState.depthFunc = this._gl.LEQUAL;\r\n\r\n            this._alphaState.reset();\r\n            this._alphaMode = Constants.ALPHA_ADD;\r\n            this._alphaEquation = Constants.ALPHA_DISABLE;\r\n\r\n            this._colorWrite = true;\r\n            this._colorWriteChanged = true;\r\n\r\n            this._unpackFlipYCached = null;\r\n\r\n            this._gl.pixelStorei(this._gl.UNPACK_COLORSPACE_CONVERSION_WEBGL, this._gl.NONE);\r\n            this._gl.pixelStorei(this._gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, 0);\r\n\r\n            this._mustWipeVertexAttributes = true;\r\n            this.unbindAllAttributes();\r\n        }\r\n\r\n        this._resetVertexBufferBinding();\r\n        this._cachedIndexBuffer = null;\r\n        this._cachedEffectForVertexBuffers = null;\r\n        this.bindIndexBuffer(null);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getSamplingParameters(samplingMode: number, generateMipMaps: boolean): { min: number; mag: number } {\r\n        const gl = this._gl;\r\n        let magFilter: GLenum = gl.NEAREST;\r\n        let minFilter: GLenum = gl.NEAREST;\r\n\r\n        switch (samplingMode) {\r\n            case Constants.TEXTURE_LINEAR_LINEAR_MIPNEAREST:\r\n                magFilter = gl.LINEAR;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.LINEAR_MIPMAP_NEAREST;\r\n                } else {\r\n                    minFilter = gl.LINEAR;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_LINEAR_MIPLINEAR:\r\n                magFilter = gl.LINEAR;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.LINEAR_MIPMAP_LINEAR;\r\n                } else {\r\n                    minFilter = gl.LINEAR;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_NEAREST_MIPLINEAR:\r\n                magFilter = gl.NEAREST;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.NEAREST_MIPMAP_LINEAR;\r\n                } else {\r\n                    minFilter = gl.NEAREST;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_NEAREST_MIPNEAREST:\r\n                magFilter = gl.NEAREST;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.NEAREST_MIPMAP_NEAREST;\r\n                } else {\r\n                    minFilter = gl.NEAREST;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_LINEAR_MIPNEAREST:\r\n                magFilter = gl.NEAREST;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.LINEAR_MIPMAP_NEAREST;\r\n                } else {\r\n                    minFilter = gl.LINEAR;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_LINEAR_MIPLINEAR:\r\n                magFilter = gl.NEAREST;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.LINEAR_MIPMAP_LINEAR;\r\n                } else {\r\n                    minFilter = gl.LINEAR;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_LINEAR:\r\n                magFilter = gl.NEAREST;\r\n                minFilter = gl.LINEAR;\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_NEAREST:\r\n                magFilter = gl.NEAREST;\r\n                minFilter = gl.NEAREST;\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_NEAREST_MIPNEAREST:\r\n                magFilter = gl.LINEAR;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.NEAREST_MIPMAP_NEAREST;\r\n                } else {\r\n                    minFilter = gl.NEAREST;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_NEAREST_MIPLINEAR:\r\n                magFilter = gl.LINEAR;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.NEAREST_MIPMAP_LINEAR;\r\n                } else {\r\n                    minFilter = gl.NEAREST;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_LINEAR:\r\n                magFilter = gl.LINEAR;\r\n                minFilter = gl.LINEAR;\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_NEAREST:\r\n                magFilter = gl.LINEAR;\r\n                minFilter = gl.NEAREST;\r\n                break;\r\n        }\r\n\r\n        return {\r\n            min: minFilter,\r\n            mag: magFilter,\r\n        };\r\n    }\r\n\r\n    /** @internal */\r\n    protected _createTexture(): WebGLTexture {\r\n        const texture = this._gl.createTexture();\r\n\r\n        if (!texture) {\r\n            throw new Error(\"Unable to create texture\");\r\n        }\r\n\r\n        return texture;\r\n    }\r\n\r\n    /** @internal */\r\n    public _createHardwareTexture(): HardwareTextureWrapper {\r\n        return new WebGLHardwareTexture(this._createTexture(), this._gl);\r\n    }\r\n\r\n    /**\r\n     * Creates an internal texture without binding it to a framebuffer\r\n     * @internal\r\n     * @param size defines the size of the texture\r\n     * @param options defines the options used to create the texture\r\n     * @param delayGPUTextureCreation true to delay the texture creation the first time it is really needed. false to create it right away\r\n     * @param source source type of the texture\r\n     * @returns a new internal texture\r\n     */\r\n    public _createInternalTexture(\r\n        size: TextureSize,\r\n        options: boolean | InternalTextureCreationOptions,\r\n        delayGPUTextureCreation = true,\r\n        source = InternalTextureSource.Unknown\r\n    ): InternalTexture {\r\n        let generateMipMaps = false;\r\n        let type = Constants.TEXTURETYPE_UNSIGNED_INT;\r\n        let samplingMode = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE;\r\n        let format = Constants.TEXTUREFORMAT_RGBA;\r\n        let useSRGBBuffer = false;\r\n        let samples = 1;\r\n        let label: string | undefined;\r\n        if (options !== undefined && typeof options === \"object\") {\r\n            generateMipMaps = !!options.generateMipMaps;\r\n            type = options.type === undefined ? Constants.TEXTURETYPE_UNSIGNED_INT : options.type;\r\n            samplingMode = options.samplingMode === undefined ? Constants.TEXTURE_TRILINEAR_SAMPLINGMODE : options.samplingMode;\r\n            format = options.format === undefined ? Constants.TEXTUREFORMAT_RGBA : options.format;\r\n            useSRGBBuffer = options.useSRGBBuffer === undefined ? false : options.useSRGBBuffer;\r\n            samples = options.samples ?? 1;\r\n            label = options.label;\r\n        } else {\r\n            generateMipMaps = !!options;\r\n        }\r\n\r\n        useSRGBBuffer &&= this._caps.supportSRGBBuffers && (this.webGLVersion > 1 || this.isWebGPU);\r\n\r\n        if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n            // if floating point linear (gl.FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        } else if (type === Constants.TEXTURETYPE_HALF_FLOAT && !this._caps.textureHalfFloatLinearFiltering) {\r\n            // if floating point linear (HALF_FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        }\r\n        if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloat) {\r\n            type = Constants.TEXTURETYPE_UNSIGNED_INT;\r\n            Logger.Warn(\"Float textures are not supported. Type forced to TEXTURETYPE_UNSIGNED_BYTE\");\r\n        }\r\n\r\n        const gl = this._gl;\r\n        const texture = new InternalTexture(this, source);\r\n        const width = (<{ width: number; height: number; layers?: number }>size).width || <number>size;\r\n        const height = (<{ width: number; height: number; layers?: number }>size).height || <number>size;\r\n        const layers = (<{ width: number; height: number; layers?: number }>size).layers || 0;\r\n        const filters = this._getSamplingParameters(samplingMode, generateMipMaps);\r\n        const target = layers !== 0 ? gl.TEXTURE_2D_ARRAY : gl.TEXTURE_2D;\r\n        const sizedFormat = this._getRGBABufferInternalSizedFormat(type, format, useSRGBBuffer);\r\n        const internalFormat = this._getInternalFormat(format);\r\n        const textureType = this._getWebGLTextureType(type);\r\n\r\n        // Bind\r\n        this._bindTextureDirectly(target, texture);\r\n\r\n        if (layers !== 0) {\r\n            texture.is2DArray = true;\r\n            gl.texImage3D(target, 0, sizedFormat, width, height, layers, 0, internalFormat, textureType, null);\r\n        } else {\r\n            gl.texImage2D(target, 0, sizedFormat, width, height, 0, internalFormat, textureType, null);\r\n        }\r\n\r\n        gl.texParameteri(target, gl.TEXTURE_MAG_FILTER, filters.mag);\r\n        gl.texParameteri(target, gl.TEXTURE_MIN_FILTER, filters.min);\r\n        gl.texParameteri(target, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n        gl.texParameteri(target, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n\r\n        // MipMaps\r\n        if (generateMipMaps) {\r\n            this._gl.generateMipmap(target);\r\n        }\r\n\r\n        this._bindTextureDirectly(target, null);\r\n\r\n        texture._useSRGBBuffer = useSRGBBuffer;\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.width = width;\r\n        texture.height = height;\r\n        texture.depth = layers;\r\n        texture.isReady = true;\r\n        texture.samples = samples;\r\n        texture.generateMipMaps = generateMipMaps;\r\n        texture.samplingMode = samplingMode;\r\n        texture.type = type;\r\n        texture.format = format;\r\n        texture.label = label;\r\n\r\n        this._internalTexturesCache.push(texture);\r\n\r\n        return texture;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getUseSRGBBuffer(useSRGBBuffer: boolean, noMipmap: boolean): boolean {\r\n        // Generating mipmaps for sRGB textures is not supported in WebGL1 so we must disable the support if mipmaps is enabled\r\n        return useSRGBBuffer && this._caps.supportSRGBBuffers && (this.webGLVersion > 1 || this.isWebGPU || noMipmap);\r\n    }\r\n\r\n    protected _createTextureBase(\r\n        url: Nullable<string>,\r\n        noMipmap: boolean,\r\n        invertY: boolean,\r\n        scene: Nullable<ISceneLike>,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        onLoad: Nullable<(texture: InternalTexture) => void> = null,\r\n        onError: Nullable<(message: string, exception: any) => void> = null,\r\n        prepareTexture: (\r\n            texture: InternalTexture,\r\n            extension: string,\r\n            scene: Nullable<ISceneLike>,\r\n            img: HTMLImageElement | ImageBitmap | { width: number; height: number },\r\n            invertY: boolean,\r\n            noMipmap: boolean,\r\n            isCompressed: boolean,\r\n            processFunction: (\r\n                width: number,\r\n                height: number,\r\n                img: HTMLImageElement | ImageBitmap | { width: number; height: number },\r\n                extension: string,\r\n                texture: InternalTexture,\r\n                continuationCallback: () => void\r\n            ) => boolean,\r\n            samplingMode: number\r\n        ) => void,\r\n        prepareTextureProcessFunction: (\r\n            width: number,\r\n            height: number,\r\n            img: HTMLImageElement | ImageBitmap | { width: number; height: number },\r\n            extension: string,\r\n            texture: InternalTexture,\r\n            continuationCallback: () => void\r\n        ) => boolean,\r\n        buffer: Nullable<string | ArrayBuffer | ArrayBufferView | HTMLImageElement | Blob | ImageBitmap> = null,\r\n        fallback: Nullable<InternalTexture> = null,\r\n        format: Nullable<number> = null,\r\n        forcedExtension: Nullable<string> = null,\r\n        mimeType?: string,\r\n        loaderOptions?: any,\r\n        useSRGBBuffer?: boolean\r\n    ): InternalTexture {\r\n        url = url || \"\";\r\n        const fromData = url.substr(0, 5) === \"data:\";\r\n        const fromBlob = url.substr(0, 5) === \"blob:\";\r\n        const isBase64 = fromData && url.indexOf(\";base64,\") !== -1;\r\n\r\n        const texture = fallback ? fallback : new InternalTexture(this, InternalTextureSource.Url);\r\n\r\n        if (texture !== fallback) {\r\n            texture.label = url.substring(0, 60); // default label, can be overriden by the caller\r\n        }\r\n\r\n        const originalUrl = url;\r\n        if (this._transformTextureUrl && !isBase64 && !fallback && !buffer) {\r\n            url = this._transformTextureUrl(url);\r\n        }\r\n\r\n        if (originalUrl !== url) {\r\n            texture._originalUrl = originalUrl;\r\n        }\r\n\r\n        // establish the file extension, if possible\r\n        const lastDot = url.lastIndexOf(\".\");\r\n        let extension = forcedExtension ? forcedExtension : lastDot > -1 ? url.substring(lastDot).toLowerCase() : \"\";\r\n        let loader: Nullable<IInternalTextureLoader> = null;\r\n\r\n        // Remove query string\r\n        const queryStringIndex = extension.indexOf(\"?\");\r\n\r\n        if (queryStringIndex > -1) {\r\n            extension = extension.split(\"?\")[0];\r\n        }\r\n\r\n        for (const availableLoader of ThinEngine._TextureLoaders) {\r\n            if (availableLoader.canLoad(extension, mimeType)) {\r\n                loader = availableLoader;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (scene) {\r\n            scene.addPendingData(texture);\r\n        }\r\n        texture.url = url;\r\n        texture.generateMipMaps = !noMipmap;\r\n        texture.samplingMode = samplingMode;\r\n        texture.invertY = invertY;\r\n        texture._useSRGBBuffer = this._getUseSRGBBuffer(!!useSRGBBuffer, noMipmap);\r\n\r\n        if (!this._doNotHandleContextLost) {\r\n            // Keep a link to the buffer only if we plan to handle context lost\r\n            texture._buffer = buffer;\r\n        }\r\n\r\n        let onLoadObserver: Nullable<Observer<InternalTexture>> = null;\r\n        if (onLoad && !fallback) {\r\n            onLoadObserver = texture.onLoadedObservable.add(onLoad);\r\n        }\r\n\r\n        if (!fallback) {\r\n            this._internalTexturesCache.push(texture);\r\n        }\r\n\r\n        const onInternalError = (message?: string, exception?: any) => {\r\n            if (scene) {\r\n                scene.removePendingData(texture);\r\n            }\r\n\r\n            if (url === originalUrl) {\r\n                if (onLoadObserver) {\r\n                    texture.onLoadedObservable.remove(onLoadObserver);\r\n                }\r\n\r\n                if (EngineStore.UseFallbackTexture && url !== EngineStore.FallbackTexture) {\r\n                    this._createTextureBase(\r\n                        EngineStore.FallbackTexture,\r\n                        noMipmap,\r\n                        texture.invertY,\r\n                        scene,\r\n                        samplingMode,\r\n                        null,\r\n                        onError,\r\n                        prepareTexture,\r\n                        prepareTextureProcessFunction,\r\n                        buffer,\r\n                        texture\r\n                    );\r\n                }\r\n\r\n                message = (message || \"Unknown error\") + (EngineStore.UseFallbackTexture ? \" - Fallback texture was used\" : \"\");\r\n                texture.onErrorObservable.notifyObservers({ message, exception });\r\n                if (onError) {\r\n                    onError(message, exception);\r\n                }\r\n            } else {\r\n                // fall back to the original url if the transformed url fails to load\r\n                Logger.Warn(`Failed to load ${url}, falling back to ${originalUrl}`);\r\n                this._createTextureBase(\r\n                    originalUrl,\r\n                    noMipmap,\r\n                    texture.invertY,\r\n                    scene,\r\n                    samplingMode,\r\n                    onLoad,\r\n                    onError,\r\n                    prepareTexture,\r\n                    prepareTextureProcessFunction,\r\n                    buffer,\r\n                    texture,\r\n                    format,\r\n                    forcedExtension,\r\n                    mimeType,\r\n                    loaderOptions,\r\n                    useSRGBBuffer\r\n                );\r\n            }\r\n        };\r\n\r\n        // processing for non-image formats\r\n        if (loader) {\r\n            const callback = (data: ArrayBufferView) => {\r\n                loader!.loadData(\r\n                    data,\r\n                    texture,\r\n                    (width: number, height: number, loadMipmap: boolean, isCompressed: boolean, done: () => void, loadFailed) => {\r\n                        if (loadFailed) {\r\n                            onInternalError(\"TextureLoader failed to load data\");\r\n                        } else {\r\n                            prepareTexture(\r\n                                texture,\r\n                                extension,\r\n                                scene,\r\n                                { width, height },\r\n                                texture.invertY,\r\n                                !loadMipmap,\r\n                                isCompressed,\r\n                                () => {\r\n                                    done();\r\n                                    return false;\r\n                                },\r\n                                samplingMode\r\n                            );\r\n                        }\r\n                    },\r\n                    loaderOptions\r\n                );\r\n            };\r\n\r\n            if (!buffer) {\r\n                this._loadFile(\r\n                    url,\r\n                    (data) => callback(new Uint8Array(data as ArrayBuffer)),\r\n                    undefined,\r\n                    scene ? scene.offlineProvider : undefined,\r\n                    true,\r\n                    (request?: IWebRequest, exception?: any) => {\r\n                        onInternalError(\"Unable to load \" + (request ? request.responseURL : url, exception));\r\n                    }\r\n                );\r\n            } else {\r\n                if (buffer instanceof ArrayBuffer) {\r\n                    callback(new Uint8Array(buffer));\r\n                } else if (ArrayBuffer.isView(buffer)) {\r\n                    callback(buffer);\r\n                } else {\r\n                    if (onError) {\r\n                        onError(\"Unable to load: only ArrayBuffer or ArrayBufferView is supported\", null);\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            const onload = (img: HTMLImageElement | ImageBitmap) => {\r\n                if (fromBlob && !this._doNotHandleContextLost) {\r\n                    // We need to store the image if we need to rebuild the texture\r\n                    // in case of a webgl context lost\r\n                    texture._buffer = img;\r\n                }\r\n\r\n                prepareTexture(texture, extension, scene, img, texture.invertY, noMipmap, false, prepareTextureProcessFunction, samplingMode);\r\n            };\r\n            // According to the WebGL spec section 6.10, ImageBitmaps must be inverted on creation.\r\n            // So, we pass imageOrientation to _FileToolsLoadImage() as it may create an ImageBitmap.\r\n\r\n            if (!fromData || isBase64) {\r\n                if (buffer && (typeof (<HTMLImageElement>buffer).decoding === \"string\" || (<ImageBitmap>buffer).close)) {\r\n                    onload(<HTMLImageElement>buffer);\r\n                } else {\r\n                    ThinEngine._FileToolsLoadImage(\r\n                        url,\r\n                        onload,\r\n                        onInternalError,\r\n                        scene ? scene.offlineProvider : null,\r\n                        mimeType,\r\n                        texture.invertY && this._features.needsInvertingBitmap ? { imageOrientation: \"flipY\" } : undefined\r\n                    );\r\n                }\r\n            } else if (typeof buffer === \"string\" || buffer instanceof ArrayBuffer || ArrayBuffer.isView(buffer) || buffer instanceof Blob) {\r\n                ThinEngine._FileToolsLoadImage(\r\n                    buffer,\r\n                    onload,\r\n                    onInternalError,\r\n                    scene ? scene.offlineProvider : null,\r\n                    mimeType,\r\n                    texture.invertY && this._features.needsInvertingBitmap ? { imageOrientation: \"flipY\" } : undefined\r\n                );\r\n            } else if (buffer) {\r\n                onload(buffer);\r\n            }\r\n        }\r\n\r\n        return texture;\r\n    }\r\n\r\n    /**\r\n     * Usually called from Texture.ts.\r\n     * Passed information to create a WebGLTexture\r\n     * @param url defines a value which contains one of the following:\r\n     * * A conventional http URL, e.g. 'http://...' or 'file://...'\r\n     * * A base64 string of in-line texture data, e.g. 'data:image/jpg;base64,/...'\r\n     * * An indicator that data being passed using the buffer parameter, e.g. 'data:mytexture.jpg'\r\n     * @param noMipmap defines a boolean indicating that no mipmaps shall be generated.  Ignored for compressed textures.  They must be in the file\r\n     * @param invertY when true, image is flipped when loaded.  You probably want true. Certain compressed textures may invert this if their default is inverted (eg. ktx)\r\n     * @param scene needed for loading to the correct scene\r\n     * @param samplingMode mode with should be used sample / access the texture (Default: Texture.TRILINEAR_SAMPLINGMODE)\r\n     * @param onLoad optional callback to be called upon successful completion\r\n     * @param onError optional callback to be called upon failure\r\n     * @param buffer a source of a file previously fetched as either a base64 string, an ArrayBuffer (compressed or image format), HTMLImageElement (image format), or a Blob\r\n     * @param fallback an internal argument in case the function must be called again, due to etc1 not having alpha capabilities\r\n     * @param format internal format.  Default: RGB when extension is '.jpg' else RGBA.  Ignored for compressed textures\r\n     * @param forcedExtension defines the extension to use to pick the right loader\r\n     * @param mimeType defines an optional mime type\r\n     * @param loaderOptions options to be passed to the loader\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n     * @returns a InternalTexture for assignment back into BABYLON.Texture\r\n     */\r\n    public createTexture(\r\n        url: Nullable<string>,\r\n        noMipmap: boolean,\r\n        invertY: boolean,\r\n        scene: Nullable<ISceneLike>,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        onLoad: Nullable<(texture: InternalTexture) => void> = null,\r\n        onError: Nullable<(message: string, exception: any) => void> = null,\r\n        buffer: Nullable<string | ArrayBuffer | ArrayBufferView | HTMLImageElement | Blob | ImageBitmap> = null,\r\n        fallback: Nullable<InternalTexture> = null,\r\n        format: Nullable<number> = null,\r\n        forcedExtension: Nullable<string> = null,\r\n        mimeType?: string,\r\n        loaderOptions?: any,\r\n        creationFlags?: number,\r\n        useSRGBBuffer?: boolean\r\n    ): InternalTexture {\r\n        return this._createTextureBase(\r\n            url,\r\n            noMipmap,\r\n            invertY,\r\n            scene,\r\n            samplingMode,\r\n            onLoad,\r\n            onError,\r\n            this._prepareWebGLTexture.bind(this),\r\n            (potWidth, potHeight, img, extension, texture, continuationCallback) => {\r\n                const gl = this._gl;\r\n                const isPot = img.width === potWidth && img.height === potHeight;\r\n\r\n                texture._creationFlags = creationFlags ?? 0;\r\n\r\n                const tip = this._getTexImageParametersForCreateTexture(format, extension, texture._useSRGBBuffer);\r\n                if (isPot) {\r\n                    gl.texImage2D(gl.TEXTURE_2D, 0, tip.internalFormat, tip.format, tip.type, img as any);\r\n                    return false;\r\n                }\r\n\r\n                const maxTextureSize = this._caps.maxTextureSize;\r\n\r\n                if (img.width > maxTextureSize || img.height > maxTextureSize || !this._supportsHardwareTextureRescaling) {\r\n                    this._prepareWorkingCanvas();\r\n                    if (!this._workingCanvas || !this._workingContext) {\r\n                        return false;\r\n                    }\r\n\r\n                    this._workingCanvas.width = potWidth;\r\n                    this._workingCanvas.height = potHeight;\r\n\r\n                    this._workingContext.drawImage(img as any, 0, 0, img.width, img.height, 0, 0, potWidth, potHeight);\r\n                    gl.texImage2D(gl.TEXTURE_2D, 0, tip.internalFormat, tip.format, tip.type, this._workingCanvas as TexImageSource);\r\n\r\n                    texture.width = potWidth;\r\n                    texture.height = potHeight;\r\n\r\n                    return false;\r\n                } else {\r\n                    // Using shaders when possible to rescale because canvas.drawImage is lossy\r\n                    const source = new InternalTexture(this, InternalTextureSource.Temp);\r\n                    this._bindTextureDirectly(gl.TEXTURE_2D, source, true);\r\n                    gl.texImage2D(gl.TEXTURE_2D, 0, tip.internalFormat, tip.format, tip.type, img as any);\r\n\r\n                    this._rescaleTexture(source, texture, scene, tip.format, () => {\r\n                        this._releaseTexture(source);\r\n                        this._bindTextureDirectly(gl.TEXTURE_2D, texture, true);\r\n\r\n                        continuationCallback();\r\n                    });\r\n                }\r\n\r\n                return true;\r\n            },\r\n            buffer,\r\n            fallback,\r\n            format,\r\n            forcedExtension,\r\n            mimeType,\r\n            loaderOptions,\r\n            useSRGBBuffer\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Calls to the GL texImage2D and texImage3D functions require three arguments describing the pixel format of the texture.\r\n     * createTexture derives these from the babylonFormat and useSRGBBuffer arguments and also the file extension of the URL it's working with.\r\n     * This function encapsulates that derivation for easy unit testing.\r\n     * @param babylonFormat Babylon's format enum, as specified in ITextureCreationOptions.\r\n     * @param fileExtension The file extension including the dot, e.g. .jpg.\r\n     * @param useSRGBBuffer Use SRGB not linear.\r\n     * @returns The options to pass to texImage2D or texImage3D calls.\r\n     * @internal\r\n     */\r\n    public _getTexImageParametersForCreateTexture(babylonFormat: Nullable<number>, fileExtension: string, useSRGBBuffer: boolean): TexImageParameters {\r\n        if (babylonFormat === undefined || babylonFormat === null) {\r\n            babylonFormat = fileExtension === \".jpg\" && !useSRGBBuffer ? Constants.TEXTUREFORMAT_RGB : Constants.TEXTUREFORMAT_RGBA;\r\n        }\r\n\r\n        let format: number, internalFormat: number;\r\n        if (this.webGLVersion === 1) {\r\n            // In WebGL 1, format and internalFormat must be the same and taken from a limited set of values, see https://docs.gl/es2/glTexImage2D.\r\n            // The SRGB extension (https://developer.mozilla.org/en-US/docs/Web/API/EXT_sRGB) adds some extra values, hence passing useSRGBBuffer\r\n            // to getInternalFormat.\r\n            format = this._getInternalFormat(babylonFormat, useSRGBBuffer);\r\n            internalFormat = format;\r\n        } else {\r\n            // In WebGL 2, format has a wider range of values and internal format can be one of the sized formats, see\r\n            // https://registry.khronos.org/OpenGL-Refpages/es3.0/html/glTexImage2D.xhtml.\r\n            // SRGB is included in the sized format and should not be passed in \"format\", hence always passing useSRGBBuffer as false.\r\n            format = this._getInternalFormat(babylonFormat, false);\r\n            internalFormat = this._getRGBABufferInternalSizedFormat(Constants.TEXTURETYPE_UNSIGNED_BYTE, babylonFormat, useSRGBBuffer);\r\n        }\r\n\r\n        return {\r\n            internalFormat,\r\n            format,\r\n            type: this._gl.UNSIGNED_BYTE,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Loads an image as an HTMLImageElement.\r\n     * @param input url string, ArrayBuffer, or Blob to load\r\n     * @param onLoad callback called when the image successfully loads\r\n     * @param onError callback called when the image fails to load\r\n     * @param offlineProvider offline provider for caching\r\n     * @param mimeType optional mime type\r\n     * @param imageBitmapOptions optional the options to use when creating an ImageBitmap\r\n     * @returns the HTMLImageElement of the loaded image\r\n     * @internal\r\n     */\r\n    public static _FileToolsLoadImage(\r\n        input: string | ArrayBuffer | ArrayBufferView | Blob,\r\n        onLoad: (img: HTMLImageElement | ImageBitmap) => void,\r\n        onError: (message?: string, exception?: any) => void,\r\n        offlineProvider: Nullable<IOfflineProvider>,\r\n        mimeType?: string,\r\n        imageBitmapOptions?: ImageBitmapOptions\r\n    ): Nullable<HTMLImageElement> {\r\n        throw _WarnImport(\"FileTools\");\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _rescaleTexture(source: InternalTexture, destination: InternalTexture, scene: Nullable<any>, internalFormat: number, onComplete: () => void): void {}\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Creates a raw texture\r\n     * @param data defines the data to store in the texture\r\n     * @param width defines the width of the texture\r\n     * @param height defines the height of the texture\r\n     * @param format defines the format of the data\r\n     * @param generateMipMaps defines if the engine should generate the mip levels\r\n     * @param invertY defines if data must be stored with Y axis inverted\r\n     * @param samplingMode defines the required sampling mode (Texture.NEAREST_SAMPLINGMODE by default)\r\n     * @param compression defines the compression used (null by default)\r\n     * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_INT by default)\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n     * @returns the raw texture inside an InternalTexture\r\n     */\r\n    public createRawTexture(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        format: number,\r\n        generateMipMaps: boolean,\r\n        invertY: boolean,\r\n        samplingMode: number,\r\n        compression: Nullable<string> = null,\r\n        type: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        creationFlags = 0,\r\n        useSRGBBuffer: boolean = false\r\n    ): InternalTexture {\r\n        throw _WarnImport(\"Engine.RawTexture\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Creates a new raw cube texture\r\n     * @param data defines the array of data to use to create each face\r\n     * @param size defines the size of the textures\r\n     * @param format defines the format of the data\r\n     * @param type defines the type of the data (like Engine.TEXTURETYPE_UNSIGNED_INT)\r\n     * @param generateMipMaps  defines if the engine should generate the mip levels\r\n     * @param invertY defines if data must be stored with Y axis inverted\r\n     * @param samplingMode defines the required sampling mode (like Texture.NEAREST_SAMPLINGMODE)\r\n     * @param compression defines the compression used (null by default)\r\n     * @returns the cube texture as an InternalTexture\r\n     */\r\n    public createRawCubeTexture(\r\n        data: Nullable<ArrayBufferView[]>,\r\n        size: number,\r\n        format: number,\r\n        type: number,\r\n        generateMipMaps: boolean,\r\n        invertY: boolean,\r\n        samplingMode: number,\r\n        compression: Nullable<string> = null\r\n    ): InternalTexture {\r\n        throw _WarnImport(\"Engine.RawTexture\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Creates a new raw 3D texture\r\n     * @param data defines the data used to create the texture\r\n     * @param width defines the width of the texture\r\n     * @param height defines the height of the texture\r\n     * @param depth defines the depth of the texture\r\n     * @param format defines the format of the texture\r\n     * @param generateMipMaps defines if the engine must generate mip levels\r\n     * @param invertY defines if data must be stored with Y axis inverted\r\n     * @param samplingMode defines the required sampling mode (like Texture.NEAREST_SAMPLINGMODE)\r\n     * @param compression defines the compressed used (can be null)\r\n     * @param textureType defines the compressed used (can be null)\r\n     * @returns a new raw 3D texture (stored in an InternalTexture)\r\n     */\r\n    public createRawTexture3D(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        depth: number,\r\n        format: number,\r\n        generateMipMaps: boolean,\r\n        invertY: boolean,\r\n        samplingMode: number,\r\n        compression: Nullable<string> = null,\r\n        textureType = Constants.TEXTURETYPE_UNSIGNED_INT\r\n    ): InternalTexture {\r\n        throw _WarnImport(\"Engine.RawTexture\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Creates a new raw 2D array texture\r\n     * @param data defines the data used to create the texture\r\n     * @param width defines the width of the texture\r\n     * @param height defines the height of the texture\r\n     * @param depth defines the number of layers of the texture\r\n     * @param format defines the format of the texture\r\n     * @param generateMipMaps defines if the engine must generate mip levels\r\n     * @param invertY defines if data must be stored with Y axis inverted\r\n     * @param samplingMode defines the required sampling mode (like Texture.NEAREST_SAMPLINGMODE)\r\n     * @param compression defines the compressed used (can be null)\r\n     * @param textureType defines the compressed used (can be null)\r\n     * @returns a new raw 2D array texture (stored in an InternalTexture)\r\n     */\r\n    public createRawTexture2DArray(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        depth: number,\r\n        format: number,\r\n        generateMipMaps: boolean,\r\n        invertY: boolean,\r\n        samplingMode: number,\r\n        compression: Nullable<string> = null,\r\n        textureType = Constants.TEXTURETYPE_UNSIGNED_INT\r\n    ): InternalTexture {\r\n        throw _WarnImport(\"Engine.RawTexture\");\r\n    }\r\n\r\n    private _unpackFlipYCached: Nullable<boolean> = null;\r\n\r\n    /**\r\n     * In case you are sharing the context with other applications, it might\r\n     * be interested to not cache the unpack flip y state to ensure a consistent\r\n     * value would be set.\r\n     */\r\n    public enableUnpackFlipYCached = true;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _unpackFlipY(value: boolean): void {\r\n        if (this._unpackFlipYCached !== value) {\r\n            this._gl.pixelStorei(this._gl.UNPACK_FLIP_Y_WEBGL, value ? 1 : 0);\r\n\r\n            if (this.enableUnpackFlipYCached) {\r\n                this._unpackFlipYCached = value;\r\n            }\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _getUnpackAlignement(): number {\r\n        return this._gl.getParameter(this._gl.UNPACK_ALIGNMENT);\r\n    }\r\n\r\n    private _getTextureTarget(texture: InternalTexture): number {\r\n        if (texture.isCube) {\r\n            return this._gl.TEXTURE_CUBE_MAP;\r\n        } else if (texture.is3D) {\r\n            return this._gl.TEXTURE_3D;\r\n        } else if (texture.is2DArray || texture.isMultiview) {\r\n            return this._gl.TEXTURE_2D_ARRAY;\r\n        }\r\n        return this._gl.TEXTURE_2D;\r\n    }\r\n\r\n    /**\r\n     * Update the sampling mode of a given texture\r\n     * @param samplingMode defines the required sampling mode\r\n     * @param texture defines the texture to update\r\n     * @param generateMipMaps defines whether to generate mipmaps for the texture\r\n     */\r\n    public updateTextureSamplingMode(samplingMode: number, texture: InternalTexture, generateMipMaps: boolean = false): void {\r\n        const target = this._getTextureTarget(texture);\r\n        const filters = this._getSamplingParameters(samplingMode, texture.useMipMaps || generateMipMaps);\r\n\r\n        this._setTextureParameterInteger(target, this._gl.TEXTURE_MAG_FILTER, filters.mag, texture);\r\n        this._setTextureParameterInteger(target, this._gl.TEXTURE_MIN_FILTER, filters.min);\r\n\r\n        if (generateMipMaps) {\r\n            texture.generateMipMaps = true;\r\n            this._gl.generateMipmap(target);\r\n        }\r\n\r\n        this._bindTextureDirectly(target, null);\r\n\r\n        texture.samplingMode = samplingMode;\r\n    }\r\n\r\n    /**\r\n     * Update the dimensions of a texture\r\n     * @param texture texture to update\r\n     * @param width new width of the texture\r\n     * @param height new height of the texture\r\n     * @param depth new depth of the texture\r\n     */\r\n    public updateTextureDimensions(texture: InternalTexture, width: number, height: number, depth: number = 1): void {}\r\n\r\n    /**\r\n     * Update the sampling mode of a given texture\r\n     * @param texture defines the texture to update\r\n     * @param wrapU defines the texture wrap mode of the u coordinates\r\n     * @param wrapV defines the texture wrap mode of the v coordinates\r\n     * @param wrapR defines the texture wrap mode of the r coordinates\r\n     */\r\n    public updateTextureWrappingMode(texture: InternalTexture, wrapU: Nullable<number>, wrapV: Nullable<number> = null, wrapR: Nullable<number> = null): void {\r\n        const target = this._getTextureTarget(texture);\r\n\r\n        if (wrapU !== null) {\r\n            this._setTextureParameterInteger(target, this._gl.TEXTURE_WRAP_S, this._getTextureWrapMode(wrapU), texture);\r\n            texture._cachedWrapU = wrapU;\r\n        }\r\n        if (wrapV !== null) {\r\n            this._setTextureParameterInteger(target, this._gl.TEXTURE_WRAP_T, this._getTextureWrapMode(wrapV), texture);\r\n            texture._cachedWrapV = wrapV;\r\n        }\r\n        if ((texture.is2DArray || texture.is3D) && wrapR !== null) {\r\n            this._setTextureParameterInteger(target, this._gl.TEXTURE_WRAP_R, this._getTextureWrapMode(wrapR), texture);\r\n            texture._cachedWrapR = wrapR;\r\n        }\r\n\r\n        this._bindTextureDirectly(target, null);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setupDepthStencilTexture(\r\n        internalTexture: InternalTexture,\r\n        size: number | { width: number; height: number; layers?: number },\r\n        generateStencil: boolean,\r\n        bilinearFiltering: boolean,\r\n        comparisonFunction: number,\r\n        samples = 1\r\n    ): void {\r\n        const width = (<{ width: number; height: number; layers?: number }>size).width || <number>size;\r\n        const height = (<{ width: number; height: number; layers?: number }>size).height || <number>size;\r\n        const layers = (<{ width: number; height: number; layers?: number }>size).layers || 0;\r\n\r\n        internalTexture.baseWidth = width;\r\n        internalTexture.baseHeight = height;\r\n        internalTexture.width = width;\r\n        internalTexture.height = height;\r\n        internalTexture.is2DArray = layers > 0;\r\n        internalTexture.depth = layers;\r\n        internalTexture.isReady = true;\r\n        internalTexture.samples = samples;\r\n        internalTexture.generateMipMaps = false;\r\n        internalTexture.samplingMode = bilinearFiltering ? Constants.TEXTURE_BILINEAR_SAMPLINGMODE : Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        internalTexture.type = Constants.TEXTURETYPE_UNSIGNED_INT;\r\n        internalTexture._comparisonFunction = comparisonFunction;\r\n\r\n        const gl = this._gl;\r\n        const target = this._getTextureTarget(internalTexture);\r\n        const samplingParameters = this._getSamplingParameters(internalTexture.samplingMode, false);\r\n        gl.texParameteri(target, gl.TEXTURE_MAG_FILTER, samplingParameters.mag);\r\n        gl.texParameteri(target, gl.TEXTURE_MIN_FILTER, samplingParameters.min);\r\n        gl.texParameteri(target, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n        gl.texParameteri(target, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n\r\n        // TEXTURE_COMPARE_FUNC/MODE are only availble in WebGL2.\r\n        if (this.webGLVersion > 1) {\r\n            if (comparisonFunction === 0) {\r\n                gl.texParameteri(target, gl.TEXTURE_COMPARE_FUNC, Constants.LEQUAL);\r\n                gl.texParameteri(target, gl.TEXTURE_COMPARE_MODE, gl.NONE);\r\n            } else {\r\n                gl.texParameteri(target, gl.TEXTURE_COMPARE_FUNC, comparisonFunction);\r\n                gl.texParameteri(target, gl.TEXTURE_COMPARE_MODE, gl.COMPARE_REF_TO_TEXTURE);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadCompressedDataToTextureDirectly(\r\n        texture: InternalTexture,\r\n        internalFormat: number,\r\n        width: number,\r\n        height: number,\r\n        data: ArrayBufferView,\r\n        faceIndex: number = 0,\r\n        lod: number = 0\r\n    ) {\r\n        const gl = this._gl;\r\n\r\n        let target: GLenum = gl.TEXTURE_2D;\r\n        if (texture.isCube) {\r\n            target = gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex;\r\n        }\r\n\r\n        if (texture._useSRGBBuffer) {\r\n            switch (internalFormat) {\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGB8_ETC2:\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGB_ETC1_WEBGL:\r\n                    // Note, if using ETC1 and sRGB is requested, this will use ETC2 if available.\r\n                    if (this._caps.etc2) {\r\n                        internalFormat = gl.COMPRESSED_SRGB8_ETC2;\r\n                    } else {\r\n                        texture._useSRGBBuffer = false;\r\n                    }\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGBA8_ETC2_EAC:\r\n                    if (this._caps.etc2) {\r\n                        internalFormat = gl.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC;\r\n                    } else {\r\n                        texture._useSRGBBuffer = false;\r\n                    }\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_BPTC_UNORM:\r\n                    internalFormat = gl.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT;\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_ASTC_4x4:\r\n                    internalFormat = gl.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR;\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGB_S3TC_DXT1:\r\n                    if (this._caps.s3tc_srgb) {\r\n                        internalFormat = gl.COMPRESSED_SRGB_S3TC_DXT1_EXT;\r\n                    } else {\r\n                        // S3TC sRGB extension not supported\r\n                        texture._useSRGBBuffer = false;\r\n                    }\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT1:\r\n                    if (this._caps.s3tc_srgb) {\r\n                        internalFormat = gl.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT;\r\n                    } else {\r\n                        // S3TC sRGB extension not supported\r\n                        texture._useSRGBBuffer = false;\r\n                    }\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT5:\r\n                    if (this._caps.s3tc_srgb) {\r\n                        internalFormat = gl.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT;\r\n                    } else {\r\n                        // S3TC sRGB extension not supported\r\n                        texture._useSRGBBuffer = false;\r\n                    }\r\n                    break;\r\n                default:\r\n                    // We don't support a sRGB format corresponding to internalFormat, so revert to non sRGB format\r\n                    texture._useSRGBBuffer = false;\r\n                    break;\r\n            }\r\n        }\r\n\r\n        this._gl.compressedTexImage2D(target, lod, internalFormat, width, height, 0, <DataView>data);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadDataToTextureDirectly(\r\n        texture: InternalTexture,\r\n        imageData: ArrayBufferView,\r\n        faceIndex: number = 0,\r\n        lod: number = 0,\r\n        babylonInternalFormat?: number,\r\n        useTextureWidthAndHeight = false\r\n    ): void {\r\n        const gl = this._gl;\r\n\r\n        const textureType = this._getWebGLTextureType(texture.type);\r\n        const format = this._getInternalFormat(texture.format);\r\n        const internalFormat =\r\n            babylonInternalFormat === undefined\r\n                ? this._getRGBABufferInternalSizedFormat(texture.type, texture.format, texture._useSRGBBuffer)\r\n                : this._getInternalFormat(babylonInternalFormat, texture._useSRGBBuffer);\r\n\r\n        this._unpackFlipY(texture.invertY);\r\n\r\n        let target: GLenum = gl.TEXTURE_2D;\r\n        if (texture.isCube) {\r\n            target = gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex;\r\n        }\r\n\r\n        const lodMaxWidth = Math.round(Math.log(texture.width) * Math.LOG2E);\r\n        const lodMaxHeight = Math.round(Math.log(texture.height) * Math.LOG2E);\r\n        const width = useTextureWidthAndHeight ? texture.width : Math.pow(2, Math.max(lodMaxWidth - lod, 0));\r\n        const height = useTextureWidthAndHeight ? texture.height : Math.pow(2, Math.max(lodMaxHeight - lod, 0));\r\n\r\n        gl.texImage2D(target, lod, internalFormat, width, height, 0, format, textureType, imageData);\r\n    }\r\n\r\n    /**\r\n     * Update a portion of an internal texture\r\n     * @param texture defines the texture to update\r\n     * @param imageData defines the data to store into the texture\r\n     * @param xOffset defines the x coordinates of the update rectangle\r\n     * @param yOffset defines the y coordinates of the update rectangle\r\n     * @param width defines the width of the update rectangle\r\n     * @param height defines the height of the update rectangle\r\n     * @param faceIndex defines the face index if texture is a cube (0 by default)\r\n     * @param lod defines the lod level to update (0 by default)\r\n     * @param generateMipMaps defines whether to generate mipmaps or not\r\n     */\r\n    public updateTextureData(\r\n        texture: InternalTexture,\r\n        imageData: ArrayBufferView,\r\n        xOffset: number,\r\n        yOffset: number,\r\n        width: number,\r\n        height: number,\r\n        faceIndex: number = 0,\r\n        lod: number = 0,\r\n        generateMipMaps = false\r\n    ): void {\r\n        const gl = this._gl;\r\n\r\n        const textureType = this._getWebGLTextureType(texture.type);\r\n        const format = this._getInternalFormat(texture.format);\r\n\r\n        this._unpackFlipY(texture.invertY);\r\n\r\n        let targetForBinding: GLenum = gl.TEXTURE_2D;\r\n        let target: GLenum = gl.TEXTURE_2D;\r\n        if (texture.isCube) {\r\n            target = gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex;\r\n            targetForBinding = gl.TEXTURE_CUBE_MAP;\r\n        }\r\n\r\n        this._bindTextureDirectly(targetForBinding, texture, true);\r\n\r\n        gl.texSubImage2D(target, lod, xOffset, yOffset, width, height, format, textureType, imageData);\r\n\r\n        if (generateMipMaps) {\r\n            this._gl.generateMipmap(target);\r\n        }\r\n\r\n        this._bindTextureDirectly(targetForBinding, null);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadArrayBufferViewToTexture(texture: InternalTexture, imageData: ArrayBufferView, faceIndex: number = 0, lod: number = 0): void {\r\n        const gl = this._gl;\r\n        const bindTarget = texture.isCube ? gl.TEXTURE_CUBE_MAP : gl.TEXTURE_2D;\r\n\r\n        this._bindTextureDirectly(bindTarget, texture, true);\r\n\r\n        this._uploadDataToTextureDirectly(texture, imageData, faceIndex, lod);\r\n\r\n        this._bindTextureDirectly(bindTarget, null, true);\r\n    }\r\n\r\n    protected _prepareWebGLTextureContinuation(texture: InternalTexture, scene: Nullable<ISceneLike>, noMipmap: boolean, isCompressed: boolean, samplingMode: number): void {\r\n        const gl = this._gl;\r\n        if (!gl) {\r\n            return;\r\n        }\r\n\r\n        const filters = this._getSamplingParameters(samplingMode, !noMipmap);\r\n\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, filters.mag);\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, filters.min);\r\n\r\n        if (!noMipmap && !isCompressed) {\r\n            gl.generateMipmap(gl.TEXTURE_2D);\r\n        }\r\n\r\n        this._bindTextureDirectly(gl.TEXTURE_2D, null);\r\n\r\n        // this.resetTextureCache();\r\n        if (scene) {\r\n            scene.removePendingData(texture);\r\n        }\r\n\r\n        texture.onLoadedObservable.notifyObservers(texture);\r\n        texture.onLoadedObservable.clear();\r\n    }\r\n\r\n    private _prepareWebGLTexture(\r\n        texture: InternalTexture,\r\n        extension: string,\r\n        scene: Nullable<ISceneLike>,\r\n        img: HTMLImageElement | ImageBitmap | { width: number; height: number },\r\n        invertY: boolean,\r\n        noMipmap: boolean,\r\n        isCompressed: boolean,\r\n        processFunction: (\r\n            width: number,\r\n            height: number,\r\n            img: HTMLImageElement | ImageBitmap | { width: number; height: number },\r\n            extension: string,\r\n            texture: InternalTexture,\r\n            continuationCallback: () => void\r\n        ) => boolean,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE\r\n    ): void {\r\n        const maxTextureSize = this.getCaps().maxTextureSize;\r\n        const potWidth = Math.min(maxTextureSize, this.needPOTTextures ? ThinEngine.GetExponentOfTwo(img.width, maxTextureSize) : img.width);\r\n        const potHeight = Math.min(maxTextureSize, this.needPOTTextures ? ThinEngine.GetExponentOfTwo(img.height, maxTextureSize) : img.height);\r\n\r\n        const gl = this._gl;\r\n        if (!gl) {\r\n            return;\r\n        }\r\n\r\n        if (!texture._hardwareTexture) {\r\n            //  this.resetTextureCache();\r\n            if (scene) {\r\n                scene.removePendingData(texture);\r\n            }\r\n\r\n            return;\r\n        }\r\n\r\n        this._bindTextureDirectly(gl.TEXTURE_2D, texture, true);\r\n        this._unpackFlipY(invertY === undefined ? true : invertY ? true : false);\r\n\r\n        texture.baseWidth = img.width;\r\n        texture.baseHeight = img.height;\r\n        texture.width = potWidth;\r\n        texture.height = potHeight;\r\n        texture.isReady = true;\r\n        texture.type = texture.type !== -1 ? texture.type : Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n        texture.format = texture.format !== -1 ? texture.format : extension === \".jpg\" && !texture._useSRGBBuffer ? Constants.TEXTUREFORMAT_RGB : Constants.TEXTUREFORMAT_RGBA;\r\n\r\n        if (\r\n            processFunction(potWidth, potHeight, img, extension, texture, () => {\r\n                this._prepareWebGLTextureContinuation(texture, scene, noMipmap, isCompressed, samplingMode);\r\n            })\r\n        ) {\r\n            // Returning as texture needs extra async steps\r\n            return;\r\n        }\r\n\r\n        this._prepareWebGLTextureContinuation(texture, scene, noMipmap, isCompressed, samplingMode);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setupFramebufferDepthAttachments(\r\n        generateStencilBuffer: boolean,\r\n        generateDepthBuffer: boolean,\r\n        width: number,\r\n        height: number,\r\n        samples = 1\r\n    ): Nullable<WebGLRenderbuffer> {\r\n        const gl = this._gl;\r\n\r\n        // Create the depth/stencil buffer\r\n        if (generateStencilBuffer && generateDepthBuffer) {\r\n            return this._createRenderBuffer(width, height, samples, gl.DEPTH_STENCIL, gl.DEPTH24_STENCIL8, gl.DEPTH_STENCIL_ATTACHMENT);\r\n        }\r\n        if (generateDepthBuffer) {\r\n            let depthFormat: GLenum = gl.DEPTH_COMPONENT16;\r\n            if (this._webGLVersion > 1) {\r\n                depthFormat = gl.DEPTH_COMPONENT32F;\r\n            }\r\n\r\n            return this._createRenderBuffer(width, height, samples, depthFormat, depthFormat, gl.DEPTH_ATTACHMENT);\r\n        }\r\n        if (generateStencilBuffer) {\r\n            return this._createRenderBuffer(width, height, samples, gl.STENCIL_INDEX8, gl.STENCIL_INDEX8, gl.STENCIL_ATTACHMENT);\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _createRenderBuffer(\r\n        width: number,\r\n        height: number,\r\n        samples: number,\r\n        internalFormat: number,\r\n        msInternalFormat: number,\r\n        attachment: number,\r\n        unbindBuffer = true\r\n    ): Nullable<WebGLRenderbuffer> {\r\n        const gl = this._gl;\r\n        const renderBuffer = gl.createRenderbuffer();\r\n        return this._updateRenderBuffer(renderBuffer, width, height, samples, internalFormat, msInternalFormat, attachment, unbindBuffer);\r\n    }\r\n\r\n    public _updateRenderBuffer(\r\n        renderBuffer: Nullable<WebGLRenderbuffer>,\r\n        width: number,\r\n        height: number,\r\n        samples: number,\r\n        internalFormat: number,\r\n        msInternalFormat: number,\r\n        attachment: number,\r\n        unbindBuffer = true\r\n    ): Nullable<WebGLRenderbuffer> {\r\n        const gl = this._gl;\r\n\r\n        gl.bindRenderbuffer(gl.RENDERBUFFER, renderBuffer);\r\n\r\n        if (samples > 1 && gl.renderbufferStorageMultisample) {\r\n            gl.renderbufferStorageMultisample(gl.RENDERBUFFER, samples, msInternalFormat, width, height);\r\n        } else {\r\n            gl.renderbufferStorage(gl.RENDERBUFFER, internalFormat, width, height);\r\n        }\r\n\r\n        gl.framebufferRenderbuffer(gl.FRAMEBUFFER, attachment, gl.RENDERBUFFER, renderBuffer);\r\n\r\n        if (unbindBuffer) {\r\n            gl.bindRenderbuffer(gl.RENDERBUFFER, null);\r\n        }\r\n\r\n        return renderBuffer;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseTexture(texture: InternalTexture): void {\r\n        this._deleteTexture(texture._hardwareTexture?.underlyingResource);\r\n\r\n        // Unbind channels\r\n        this.unbindAllTextures();\r\n\r\n        const index = this._internalTexturesCache.indexOf(texture);\r\n        if (index !== -1) {\r\n            this._internalTexturesCache.splice(index, 1);\r\n        }\r\n\r\n        // Integrated fixed lod samplers.\r\n        if (texture._lodTextureHigh) {\r\n            texture._lodTextureHigh.dispose();\r\n        }\r\n        if (texture._lodTextureMid) {\r\n            texture._lodTextureMid.dispose();\r\n        }\r\n        if (texture._lodTextureLow) {\r\n            texture._lodTextureLow.dispose();\r\n        }\r\n\r\n        // Integrated irradiance map.\r\n        if (texture._irradianceTexture) {\r\n            texture._irradianceTexture.dispose();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseRenderTargetWrapper(rtWrapper: RenderTargetWrapper): void {\r\n        const index = this._renderTargetWrapperCache.indexOf(rtWrapper);\r\n        if (index !== -1) {\r\n            this._renderTargetWrapperCache.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    protected _deleteTexture(texture: Nullable<WebGLTexture>): void {\r\n        if (texture) {\r\n            this._gl.deleteTexture(texture);\r\n        }\r\n    }\r\n\r\n    protected _setProgram(program: WebGLProgram): void {\r\n        if (this._currentProgram !== program) {\r\n            this._gl.useProgram(program);\r\n            this._currentProgram = program;\r\n        }\r\n    }\r\n\r\n    protected _boundUniforms: { [key: number]: WebGLUniformLocation } = {};\r\n\r\n    /**\r\n     * Binds an effect to the webGL context\r\n     * @param effect defines the effect to bind\r\n     */\r\n    public bindSamplers(effect: Effect): void {\r\n        const webGLPipelineContext = effect.getPipelineContext() as WebGLPipelineContext;\r\n        this._setProgram(webGLPipelineContext.program!);\r\n        const samplers = effect.getSamplers();\r\n        for (let index = 0; index < samplers.length; index++) {\r\n            const uniform = effect.getUniform(samplers[index]);\r\n\r\n            if (uniform) {\r\n                this._boundUniforms[index] = uniform;\r\n            }\r\n        }\r\n        this._currentEffect = null;\r\n    }\r\n\r\n    private _activateCurrentTexture() {\r\n        if (this._currentTextureChannel !== this._activeChannel) {\r\n            this._gl.activeTexture(this._gl.TEXTURE0 + this._activeChannel);\r\n            this._currentTextureChannel = this._activeChannel;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindTextureDirectly(target: number, texture: Nullable<InternalTexture>, forTextureDataUpdate = false, force = false): boolean {\r\n        let wasPreviouslyBound = false;\r\n        const isTextureForRendering = texture && texture._associatedChannel > -1;\r\n        if (forTextureDataUpdate && isTextureForRendering) {\r\n            this._activeChannel = texture!._associatedChannel;\r\n        }\r\n\r\n        const currentTextureBound = this._boundTexturesCache[this._activeChannel];\r\n\r\n        if (currentTextureBound !== texture || force) {\r\n            this._activateCurrentTexture();\r\n\r\n            if (texture && texture.isMultiview) {\r\n                //this._gl.bindTexture(target, texture ? texture._colorTextureArray : null);\r\n                Logger.Error([\"_bindTextureDirectly called with a multiview texture!\", target, texture]);\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"_bindTextureDirectly called with a multiview texture!\";\r\n            } else {\r\n                this._gl.bindTexture(target, texture?._hardwareTexture?.underlyingResource ?? null);\r\n            }\r\n\r\n            this._boundTexturesCache[this._activeChannel] = texture;\r\n\r\n            if (texture) {\r\n                texture._associatedChannel = this._activeChannel;\r\n            }\r\n        } else if (forTextureDataUpdate) {\r\n            wasPreviouslyBound = true;\r\n            this._activateCurrentTexture();\r\n        }\r\n\r\n        if (isTextureForRendering && !forTextureDataUpdate) {\r\n            this._bindSamplerUniformToChannel(texture!._associatedChannel, this._activeChannel);\r\n        }\r\n\r\n        return wasPreviouslyBound;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindTexture(channel: number, texture: Nullable<InternalTexture>, name: string): void {\r\n        if (channel === undefined) {\r\n            return;\r\n        }\r\n\r\n        if (texture) {\r\n            texture._associatedChannel = channel;\r\n        }\r\n\r\n        this._activeChannel = channel;\r\n        const target = texture ? this._getTextureTarget(texture) : this._gl.TEXTURE_2D;\r\n        this._bindTextureDirectly(target, texture);\r\n    }\r\n\r\n    /**\r\n     * Unbind all textures from the webGL context\r\n     */\r\n    public unbindAllTextures(): void {\r\n        for (let channel = 0; channel < this._maxSimultaneousTextures; channel++) {\r\n            this._activeChannel = channel;\r\n            this._bindTextureDirectly(this._gl.TEXTURE_2D, null);\r\n            this._bindTextureDirectly(this._gl.TEXTURE_CUBE_MAP, null);\r\n            if (this.webGLVersion > 1) {\r\n                this._bindTextureDirectly(this._gl.TEXTURE_3D, null);\r\n                this._bindTextureDirectly(this._gl.TEXTURE_2D_ARRAY, null);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a texture to the according uniform.\r\n     * @param channel The texture channel\r\n     * @param uniform The uniform to set\r\n     * @param texture The texture to apply\r\n     * @param name The name of the uniform in the effect\r\n     */\r\n    public setTexture(channel: number, uniform: Nullable<WebGLUniformLocation>, texture: Nullable<ThinTexture>, name: string): void {\r\n        if (channel === undefined) {\r\n            return;\r\n        }\r\n\r\n        if (uniform) {\r\n            this._boundUniforms[channel] = uniform;\r\n        }\r\n\r\n        this._setTexture(channel, texture);\r\n    }\r\n\r\n    private _bindSamplerUniformToChannel(sourceSlot: number, destination: number) {\r\n        const uniform = this._boundUniforms[sourceSlot];\r\n        if (!uniform || uniform._currentState === destination) {\r\n            return;\r\n        }\r\n        this._gl.uniform1i(uniform, destination);\r\n        uniform._currentState = destination;\r\n    }\r\n\r\n    private _getTextureWrapMode(mode: number): number {\r\n        switch (mode) {\r\n            case Constants.TEXTURE_WRAP_ADDRESSMODE:\r\n                return this._gl.REPEAT;\r\n            case Constants.TEXTURE_CLAMP_ADDRESSMODE:\r\n                return this._gl.CLAMP_TO_EDGE;\r\n            case Constants.TEXTURE_MIRROR_ADDRESSMODE:\r\n                return this._gl.MIRRORED_REPEAT;\r\n        }\r\n        return this._gl.REPEAT;\r\n    }\r\n\r\n    protected _setTexture(channel: number, texture: Nullable<ThinTexture>, isPartOfTextureArray = false, depthStencilTexture = false, name = \"\"): boolean {\r\n        // Not ready?\r\n        if (!texture) {\r\n            if (this._boundTexturesCache[channel] != null) {\r\n                this._activeChannel = channel;\r\n                this._bindTextureDirectly(this._gl.TEXTURE_2D, null);\r\n                this._bindTextureDirectly(this._gl.TEXTURE_CUBE_MAP, null);\r\n                if (this.webGLVersion > 1) {\r\n                    this._bindTextureDirectly(this._gl.TEXTURE_3D, null);\r\n                    this._bindTextureDirectly(this._gl.TEXTURE_2D_ARRAY, null);\r\n                }\r\n            }\r\n            return false;\r\n        }\r\n\r\n        // Video\r\n        if ((<VideoTexture>texture).video) {\r\n            this._activeChannel = channel;\r\n            const videoInternalTexture = (<VideoTexture>texture).getInternalTexture();\r\n            if (videoInternalTexture) {\r\n                videoInternalTexture._associatedChannel = channel;\r\n            }\r\n            (<VideoTexture>texture).update();\r\n        } else if (texture.delayLoadState === Constants.DELAYLOADSTATE_NOTLOADED) {\r\n            // Delay loading\r\n            texture.delayLoad();\r\n            return false;\r\n        }\r\n\r\n        let internalTexture: InternalTexture;\r\n        if (depthStencilTexture) {\r\n            internalTexture = (<RenderTargetTexture>texture).depthStencilTexture!;\r\n        } else if (texture.isReady()) {\r\n            internalTexture = <InternalTexture>texture.getInternalTexture();\r\n        } else if (texture.isCube) {\r\n            internalTexture = this.emptyCubeTexture;\r\n        } else if (texture.is3D) {\r\n            internalTexture = this.emptyTexture3D;\r\n        } else if (texture.is2DArray) {\r\n            internalTexture = this.emptyTexture2DArray;\r\n        } else {\r\n            internalTexture = this.emptyTexture;\r\n        }\r\n\r\n        if (!isPartOfTextureArray && internalTexture) {\r\n            internalTexture._associatedChannel = channel;\r\n        }\r\n\r\n        let needToBind = true;\r\n        if (this._boundTexturesCache[channel] === internalTexture) {\r\n            if (!isPartOfTextureArray) {\r\n                this._bindSamplerUniformToChannel(internalTexture._associatedChannel, channel);\r\n            }\r\n\r\n            needToBind = false;\r\n        }\r\n\r\n        this._activeChannel = channel;\r\n        const target = this._getTextureTarget(internalTexture);\r\n        if (needToBind) {\r\n            this._bindTextureDirectly(target, internalTexture, isPartOfTextureArray);\r\n        }\r\n\r\n        if (internalTexture && !internalTexture.isMultiview) {\r\n            // CUBIC_MODE and SKYBOX_MODE both require CLAMP_TO_EDGE.  All other modes use REPEAT.\r\n            if (internalTexture.isCube && internalTexture._cachedCoordinatesMode !== texture.coordinatesMode) {\r\n                internalTexture._cachedCoordinatesMode = texture.coordinatesMode;\r\n\r\n                const textureWrapMode =\r\n                    texture.coordinatesMode !== Constants.TEXTURE_CUBIC_MODE && texture.coordinatesMode !== Constants.TEXTURE_SKYBOX_MODE\r\n                        ? Constants.TEXTURE_WRAP_ADDRESSMODE\r\n                        : Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n                texture.wrapU = textureWrapMode;\r\n                texture.wrapV = textureWrapMode;\r\n            }\r\n\r\n            if (internalTexture._cachedWrapU !== texture.wrapU) {\r\n                internalTexture._cachedWrapU = texture.wrapU;\r\n                this._setTextureParameterInteger(target, this._gl.TEXTURE_WRAP_S, this._getTextureWrapMode(texture.wrapU), internalTexture);\r\n            }\r\n\r\n            if (internalTexture._cachedWrapV !== texture.wrapV) {\r\n                internalTexture._cachedWrapV = texture.wrapV;\r\n                this._setTextureParameterInteger(target, this._gl.TEXTURE_WRAP_T, this._getTextureWrapMode(texture.wrapV), internalTexture);\r\n            }\r\n\r\n            if (internalTexture.is3D && internalTexture._cachedWrapR !== texture.wrapR) {\r\n                internalTexture._cachedWrapR = texture.wrapR;\r\n                this._setTextureParameterInteger(target, this._gl.TEXTURE_WRAP_R, this._getTextureWrapMode(texture.wrapR), internalTexture);\r\n            }\r\n\r\n            this._setAnisotropicLevel(target, internalTexture, texture.anisotropicFilteringLevel);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Sets an array of texture to the webGL context\r\n     * @param channel defines the channel where the texture array must be set\r\n     * @param uniform defines the associated uniform location\r\n     * @param textures defines the array of textures to bind\r\n     * @param name name of the channel\r\n     */\r\n    public setTextureArray(channel: number, uniform: Nullable<WebGLUniformLocation>, textures: ThinTexture[], name: string): void {\r\n        if (channel === undefined || !uniform) {\r\n            return;\r\n        }\r\n\r\n        if (!this._textureUnits || this._textureUnits.length !== textures.length) {\r\n            this._textureUnits = new Int32Array(textures.length);\r\n        }\r\n        for (let i = 0; i < textures.length; i++) {\r\n            const texture = textures[i].getInternalTexture();\r\n\r\n            if (texture) {\r\n                this._textureUnits[i] = channel + i;\r\n                texture._associatedChannel = channel + i;\r\n            } else {\r\n                this._textureUnits[i] = -1;\r\n            }\r\n        }\r\n        this._gl.uniform1iv(uniform, this._textureUnits);\r\n\r\n        for (let index = 0; index < textures.length; index++) {\r\n            this._setTexture(this._textureUnits[index], textures[index], true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setAnisotropicLevel(target: number, internalTexture: InternalTexture, anisotropicFilteringLevel: number) {\r\n        const anisotropicFilterExtension = this._caps.textureAnisotropicFilterExtension;\r\n        if (\r\n            internalTexture.samplingMode !== Constants.TEXTURE_LINEAR_LINEAR_MIPNEAREST &&\r\n            internalTexture.samplingMode !== Constants.TEXTURE_LINEAR_LINEAR_MIPLINEAR &&\r\n            internalTexture.samplingMode !== Constants.TEXTURE_LINEAR_LINEAR\r\n        ) {\r\n            anisotropicFilteringLevel = 1; // Forcing the anisotropic to 1 because else webgl will force filters to linear\r\n        }\r\n\r\n        if (anisotropicFilterExtension && internalTexture._cachedAnisotropicFilteringLevel !== anisotropicFilteringLevel) {\r\n            this._setTextureParameterFloat(\r\n                target,\r\n                anisotropicFilterExtension.TEXTURE_MAX_ANISOTROPY_EXT,\r\n                Math.min(anisotropicFilteringLevel, this._caps.maxAnisotropy),\r\n                internalTexture\r\n            );\r\n            internalTexture._cachedAnisotropicFilteringLevel = anisotropicFilteringLevel;\r\n        }\r\n    }\r\n\r\n    private _setTextureParameterFloat(target: number, parameter: number, value: number, texture: InternalTexture): void {\r\n        this._bindTextureDirectly(target, texture, true, true);\r\n        this._gl.texParameterf(target, parameter, value);\r\n    }\r\n\r\n    private _setTextureParameterInteger(target: number, parameter: number, value: number, texture?: InternalTexture) {\r\n        if (texture) {\r\n            this._bindTextureDirectly(target, texture, true, true);\r\n        }\r\n        this._gl.texParameteri(target, parameter, value);\r\n    }\r\n\r\n    /**\r\n     * Unbind all vertex attributes from the webGL context\r\n     */\r\n    public unbindAllAttributes() {\r\n        if (this._mustWipeVertexAttributes) {\r\n            this._mustWipeVertexAttributes = false;\r\n\r\n            for (let i = 0; i < this._caps.maxVertexAttribs; i++) {\r\n                this.disableAttributeByIndex(i);\r\n            }\r\n            return;\r\n        }\r\n\r\n        for (let i = 0, ul = this._vertexAttribArraysEnabled.length; i < ul; i++) {\r\n            if (i >= this._caps.maxVertexAttribs || !this._vertexAttribArraysEnabled[i]) {\r\n                continue;\r\n            }\r\n\r\n            this.disableAttributeByIndex(i);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Force the engine to release all cached effects. This means that next effect compilation will have to be done completely even if a similar effect was already compiled\r\n     */\r\n    public releaseEffects() {\r\n        for (const name in this._compiledEffects) {\r\n            const webGLPipelineContext = this._compiledEffects[name].getPipelineContext() as WebGLPipelineContext;\r\n            this._deletePipelineContext(webGLPipelineContext);\r\n        }\r\n\r\n        this._compiledEffects = {};\r\n    }\r\n\r\n    /**\r\n     * Dispose and release all associated resources\r\n     */\r\n    public dispose(): void {\r\n        this._isDisposed = true;\r\n        this.stopRenderLoop();\r\n\r\n        // Clear observables\r\n        if (this.onBeforeTextureInitObservable) {\r\n            this.onBeforeTextureInitObservable.clear();\r\n        }\r\n\r\n        // Empty texture\r\n        if (this._emptyTexture) {\r\n            this._releaseTexture(this._emptyTexture);\r\n            this._emptyTexture = null;\r\n        }\r\n        if (this._emptyCubeTexture) {\r\n            this._releaseTexture(this._emptyCubeTexture);\r\n            this._emptyCubeTexture = null;\r\n        }\r\n\r\n        if (this._dummyFramebuffer) {\r\n            this._gl.deleteFramebuffer(this._dummyFramebuffer);\r\n        }\r\n\r\n        // Release effects\r\n        this.releaseEffects();\r\n        this.releaseComputeEffects?.();\r\n\r\n        // Unbind\r\n        this.unbindAllAttributes();\r\n        this._boundUniforms = {};\r\n\r\n        // Events\r\n        if (IsWindowObjectExist()) {\r\n            if (this._renderingCanvas) {\r\n                if (!this._doNotHandleContextLost) {\r\n                    this._renderingCanvas.removeEventListener(\"webglcontextlost\", this._onContextLost);\r\n                    this._renderingCanvas.removeEventListener(\"webglcontextrestored\", this._onContextRestored);\r\n                }\r\n\r\n                window.removeEventListener(\"resize\", this._checkForMobile);\r\n            }\r\n        }\r\n\r\n        this._workingCanvas = null;\r\n        this._workingContext = null;\r\n        this._currentBufferPointers.length = 0;\r\n        this._renderingCanvas = null;\r\n        this._currentProgram = null;\r\n        this._boundRenderFunction = null;\r\n\r\n        Effect.ResetCache();\r\n\r\n        // Abort active requests\r\n        for (const request of this._activeRequests) {\r\n            request.abort();\r\n        }\r\n\r\n        this.onDisposeObservable.notifyObservers(this);\r\n        this.onDisposeObservable.clear();\r\n\r\n        if (this._creationOptions.loseContextOnDispose) {\r\n            this._gl.getExtension(\"WEBGL_lose_context\")?.loseContext();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Attach a new callback raised when context lost event is fired\r\n     * @param callback defines the callback to call\r\n     */\r\n    public attachContextLostEvent(callback: (event: WebGLContextEvent) => void): void {\r\n        if (this._renderingCanvas) {\r\n            this._renderingCanvas.addEventListener(\"webglcontextlost\", <any>callback, false);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Attach a new callback raised when context restored event is fired\r\n     * @param callback defines the callback to call\r\n     */\r\n    public attachContextRestoredEvent(callback: (event: WebGLContextEvent) => void): void {\r\n        if (this._renderingCanvas) {\r\n            this._renderingCanvas.addEventListener(\"webglcontextrestored\", <any>callback, false);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the current error code of the webGL context\r\n     * @returns the error code\r\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebGLRenderingContext/getError\r\n     */\r\n    public getError(): number {\r\n        return this._gl.getError();\r\n    }\r\n\r\n    private _canRenderToFloatFramebuffer(): boolean {\r\n        if (this._webGLVersion > 1) {\r\n            return this._caps.colorBufferFloat;\r\n        }\r\n        return this._canRenderToFramebuffer(Constants.TEXTURETYPE_FLOAT);\r\n    }\r\n\r\n    private _canRenderToHalfFloatFramebuffer(): boolean {\r\n        if (this._webGLVersion > 1) {\r\n            return this._caps.colorBufferFloat;\r\n        }\r\n        return this._canRenderToFramebuffer(Constants.TEXTURETYPE_HALF_FLOAT);\r\n    }\r\n\r\n    // Thank you : http://stackoverflow.com/questions/28827511/webgl-ios-render-to-floating-point-texture\r\n    private _canRenderToFramebuffer(type: number): boolean {\r\n        const gl = this._gl;\r\n\r\n        //clear existing errors\r\n        // eslint-disable-next-line no-empty\r\n        while (gl.getError() !== gl.NO_ERROR) {}\r\n\r\n        let successful = true;\r\n\r\n        const texture = gl.createTexture();\r\n        gl.bindTexture(gl.TEXTURE_2D, texture);\r\n        gl.texImage2D(gl.TEXTURE_2D, 0, this._getRGBABufferInternalSizedFormat(type), 1, 1, 0, gl.RGBA, this._getWebGLTextureType(type), null);\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);\r\n\r\n        const fb = gl.createFramebuffer();\r\n        gl.bindFramebuffer(gl.FRAMEBUFFER, fb);\r\n        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);\r\n        const status = gl.checkFramebufferStatus(gl.FRAMEBUFFER);\r\n\r\n        successful = successful && status === gl.FRAMEBUFFER_COMPLETE;\r\n        successful = successful && gl.getError() === gl.NO_ERROR;\r\n\r\n        //try render by clearing frame buffer's color buffer\r\n        if (successful) {\r\n            gl.clear(gl.COLOR_BUFFER_BIT);\r\n            successful = successful && gl.getError() === gl.NO_ERROR;\r\n        }\r\n\r\n        //try reading from frame to ensure render occurs (just creating the FBO is not sufficient to determine if rendering is supported)\r\n        if (successful) {\r\n            //in practice it's sufficient to just read from the backbuffer rather than handle potentially issues reading from the texture\r\n            gl.bindFramebuffer(gl.FRAMEBUFFER, null);\r\n            const readFormat = gl.RGBA;\r\n            const readType = gl.UNSIGNED_BYTE;\r\n            const buffer = new Uint8Array(4);\r\n            gl.readPixels(0, 0, 1, 1, readFormat, readType, buffer);\r\n            successful = successful && gl.getError() === gl.NO_ERROR;\r\n        }\r\n\r\n        //clean up\r\n        gl.deleteTexture(texture);\r\n        gl.deleteFramebuffer(fb);\r\n        gl.bindFramebuffer(gl.FRAMEBUFFER, null);\r\n\r\n        //clear accumulated errors\r\n        // eslint-disable-next-line no-empty\r\n        while (!successful && gl.getError() !== gl.NO_ERROR) {}\r\n\r\n        return successful;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getWebGLTextureType(type: number): number {\r\n        if (this._webGLVersion === 1) {\r\n            switch (type) {\r\n                case Constants.TEXTURETYPE_FLOAT:\r\n                    return this._gl.FLOAT;\r\n                case Constants.TEXTURETYPE_HALF_FLOAT:\r\n                    return this._gl.HALF_FLOAT_OES;\r\n                case Constants.TEXTURETYPE_UNSIGNED_BYTE:\r\n                    return this._gl.UNSIGNED_BYTE;\r\n                case Constants.TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4:\r\n                    return this._gl.UNSIGNED_SHORT_4_4_4_4;\r\n                case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1:\r\n                    return this._gl.UNSIGNED_SHORT_5_5_5_1;\r\n                case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_6_5:\r\n                    return this._gl.UNSIGNED_SHORT_5_6_5;\r\n            }\r\n            return this._gl.UNSIGNED_BYTE;\r\n        }\r\n\r\n        switch (type) {\r\n            case Constants.TEXTURETYPE_BYTE:\r\n                return this._gl.BYTE;\r\n            case Constants.TEXTURETYPE_UNSIGNED_BYTE:\r\n                return this._gl.UNSIGNED_BYTE;\r\n            case Constants.TEXTURETYPE_SHORT:\r\n                return this._gl.SHORT;\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT:\r\n                return this._gl.UNSIGNED_SHORT;\r\n            case Constants.TEXTURETYPE_INT:\r\n                return this._gl.INT;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INTEGER: // Refers to UNSIGNED_INT\r\n                return this._gl.UNSIGNED_INT;\r\n            case Constants.TEXTURETYPE_FLOAT:\r\n                return this._gl.FLOAT;\r\n            case Constants.TEXTURETYPE_HALF_FLOAT:\r\n                return this._gl.HALF_FLOAT;\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4:\r\n                return this._gl.UNSIGNED_SHORT_4_4_4_4;\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1:\r\n                return this._gl.UNSIGNED_SHORT_5_5_5_1;\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_6_5:\r\n                return this._gl.UNSIGNED_SHORT_5_6_5;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_2_10_10_10_REV:\r\n                return this._gl.UNSIGNED_INT_2_10_10_10_REV;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_24_8:\r\n                return this._gl.UNSIGNED_INT_24_8;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_10F_11F_11F_REV:\r\n                return this._gl.UNSIGNED_INT_10F_11F_11F_REV;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_5_9_9_9_REV:\r\n                return this._gl.UNSIGNED_INT_5_9_9_9_REV;\r\n            case Constants.TEXTURETYPE_FLOAT_32_UNSIGNED_INT_24_8_REV:\r\n                return this._gl.FLOAT_32_UNSIGNED_INT_24_8_REV;\r\n        }\r\n\r\n        return this._gl.UNSIGNED_BYTE;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getInternalFormat(format: number, useSRGBBuffer = false): number {\r\n        let internalFormat: GLenum = useSRGBBuffer ? this._glSRGBExtensionValues.SRGB8_ALPHA8 : this._gl.RGBA;\r\n\r\n        switch (format) {\r\n            case Constants.TEXTUREFORMAT_ALPHA:\r\n                internalFormat = this._gl.ALPHA;\r\n                break;\r\n            case Constants.TEXTUREFORMAT_LUMINANCE:\r\n                internalFormat = this._gl.LUMINANCE;\r\n                break;\r\n            case Constants.TEXTUREFORMAT_LUMINANCE_ALPHA:\r\n                internalFormat = this._gl.LUMINANCE_ALPHA;\r\n                break;\r\n            case Constants.TEXTUREFORMAT_RED:\r\n                internalFormat = this._gl.RED;\r\n                break;\r\n            case Constants.TEXTUREFORMAT_RG:\r\n                internalFormat = this._gl.RG;\r\n                break;\r\n            case Constants.TEXTUREFORMAT_RGB:\r\n                internalFormat = useSRGBBuffer ? this._glSRGBExtensionValues.SRGB : this._gl.RGB;\r\n                break;\r\n            case Constants.TEXTUREFORMAT_RGBA:\r\n                internalFormat = useSRGBBuffer ? this._glSRGBExtensionValues.SRGB8_ALPHA8 : this._gl.RGBA;\r\n                break;\r\n        }\r\n\r\n        if (this._webGLVersion > 1) {\r\n            switch (format) {\r\n                case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                    internalFormat = this._gl.RED_INTEGER;\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                    internalFormat = this._gl.RG_INTEGER;\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                    internalFormat = this._gl.RGB_INTEGER;\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                    internalFormat = this._gl.RGBA_INTEGER;\r\n                    break;\r\n            }\r\n        }\r\n\r\n        return internalFormat;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getRGBABufferInternalSizedFormat(type: number, format?: number, useSRGBBuffer = false): number {\r\n        if (this._webGLVersion === 1) {\r\n            if (format !== undefined) {\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_ALPHA:\r\n                        return this._gl.ALPHA;\r\n                    case Constants.TEXTUREFORMAT_LUMINANCE:\r\n                        return this._gl.LUMINANCE;\r\n                    case Constants.TEXTUREFORMAT_LUMINANCE_ALPHA:\r\n                        return this._gl.LUMINANCE_ALPHA;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        return useSRGBBuffer ? this._glSRGBExtensionValues.SRGB : this._gl.RGB;\r\n                }\r\n            }\r\n            return this._gl.RGBA;\r\n        }\r\n\r\n        switch (type) {\r\n            case Constants.TEXTURETYPE_BYTE:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return this._gl.R8_SNORM;\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return this._gl.RG8_SNORM;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        return this._gl.RGB8_SNORM;\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return this._gl.R8I;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return this._gl.RG8I;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        return this._gl.RGB8I;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGBA8I;\r\n                    default:\r\n                        return this._gl.RGBA8_SNORM;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_BYTE:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return this._gl.R8;\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return this._gl.RG8;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        return useSRGBBuffer ? this._glSRGBExtensionValues.SRGB8 : this._gl.RGB8; // By default. Other possibilities are RGB565, SRGB8.\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return useSRGBBuffer ? this._glSRGBExtensionValues.SRGB8_ALPHA8 : this._gl.RGBA8; // By default. Other possibilities are RGB5_A1, RGBA4, SRGB8_ALPHA8.\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return this._gl.R8UI;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return this._gl.RG8UI;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        return this._gl.RGB8UI;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGBA8UI;\r\n                    case Constants.TEXTUREFORMAT_ALPHA:\r\n                        return this._gl.ALPHA;\r\n                    case Constants.TEXTUREFORMAT_LUMINANCE:\r\n                        return this._gl.LUMINANCE;\r\n                    case Constants.TEXTUREFORMAT_LUMINANCE_ALPHA:\r\n                        return this._gl.LUMINANCE_ALPHA;\r\n                    default:\r\n                        return this._gl.RGBA8;\r\n                }\r\n            case Constants.TEXTURETYPE_SHORT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return this._gl.R16I;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return this._gl.RG16I;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        return this._gl.RGB16I;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGBA16I;\r\n                    default:\r\n                        return this._gl.RGBA16I;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return this._gl.R16UI;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return this._gl.RG16UI;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        return this._gl.RGB16UI;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGBA16UI;\r\n                    default:\r\n                        return this._gl.RGBA16UI;\r\n                }\r\n            case Constants.TEXTURETYPE_INT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return this._gl.R32I;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return this._gl.RG32I;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        return this._gl.RGB32I;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGBA32I;\r\n                    default:\r\n                        return this._gl.RGBA32I;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_INTEGER: // Refers to UNSIGNED_INT\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return this._gl.R32UI;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return this._gl.RG32UI;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        return this._gl.RGB32UI;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGBA32UI;\r\n                    default:\r\n                        return this._gl.RGBA32UI;\r\n                }\r\n            case Constants.TEXTURETYPE_FLOAT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return this._gl.R32F; // By default. Other possibility is R16F.\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return this._gl.RG32F; // By default. Other possibility is RG16F.\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        return this._gl.RGB32F; // By default. Other possibilities are RGB16F, R11F_G11F_B10F, RGB9_E5.\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return this._gl.RGBA32F; // By default. Other possibility is RGBA16F.\r\n                    default:\r\n                        return this._gl.RGBA32F;\r\n                }\r\n            case Constants.TEXTURETYPE_HALF_FLOAT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return this._gl.R16F;\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return this._gl.RG16F;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        return this._gl.RGB16F; // By default. Other possibilities are R11F_G11F_B10F, RGB9_E5.\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return this._gl.RGBA16F;\r\n                    default:\r\n                        return this._gl.RGBA16F;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_6_5:\r\n                return this._gl.RGB565;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_10F_11F_11F_REV:\r\n                return this._gl.R11F_G11F_B10F;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_5_9_9_9_REV:\r\n                return this._gl.RGB9_E5;\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4:\r\n                return this._gl.RGBA4;\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1:\r\n                return this._gl.RGB5_A1;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_2_10_10_10_REV:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return this._gl.RGB10_A2; // By default. Other possibility is RGB5_A1.\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGB10_A2UI;\r\n                    default:\r\n                        return this._gl.RGB10_A2;\r\n                }\r\n        }\r\n\r\n        return useSRGBBuffer ? this._glSRGBExtensionValues.SRGB8_ALPHA8 : this._gl.RGBA8;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadFile(\r\n        url: string,\r\n        onSuccess: (data: string | ArrayBuffer, responseURL?: string) => void,\r\n        onProgress?: (data: any) => void,\r\n        offlineProvider?: IOfflineProvider,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (request?: IWebRequest, exception?: any) => void\r\n    ): IFileRequest {\r\n        const request = ThinEngine._FileToolsLoadFile(url, onSuccess, onProgress, offlineProvider, useArrayBuffer, onError);\r\n        this._activeRequests.push(request);\r\n        request.onCompleteObservable.add((request) => {\r\n            this._activeRequests.splice(this._activeRequests.indexOf(request), 1);\r\n        });\r\n        return request;\r\n    }\r\n\r\n    /**\r\n     * Loads a file from a url\r\n     * @param url url to load\r\n     * @param onSuccess callback called when the file successfully loads\r\n     * @param onProgress callback called while file is loading (if the server supports this mode)\r\n     * @param offlineProvider defines the offline provider for caching\r\n     * @param useArrayBuffer defines a boolean indicating that date must be returned as ArrayBuffer\r\n     * @param onError callback called when the file fails to load\r\n     * @returns a file request object\r\n     * @internal\r\n     */\r\n    public static _FileToolsLoadFile(\r\n        url: string,\r\n        onSuccess: (data: string | ArrayBuffer, responseURL?: string) => void,\r\n        onProgress?: (ev: ProgressEvent) => void,\r\n        offlineProvider?: IOfflineProvider,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (request?: WebRequest, exception?: LoadFileError) => void\r\n    ): IFileRequest {\r\n        throw _WarnImport(\"FileTools\");\r\n    }\r\n\r\n    /**\r\n     * Reads pixels from the current frame buffer. Please note that this function can be slow\r\n     * @param x defines the x coordinate of the rectangle where pixels must be read\r\n     * @param y defines the y coordinate of the rectangle where pixels must be read\r\n     * @param width defines the width of the rectangle where pixels must be read\r\n     * @param height defines the height of the rectangle where pixels must be read\r\n     * @param hasAlpha defines whether the output should have alpha or not (defaults to true)\r\n     * @param flushRenderer true to flush the renderer from the pending commands before reading the pixels\r\n     * @returns a ArrayBufferView promise (Uint8Array) containing RGBA colors\r\n     */\r\n    public readPixels(x: number, y: number, width: number, height: number, hasAlpha = true, flushRenderer = true): Promise<ArrayBufferView> {\r\n        const numChannels = hasAlpha ? 4 : 3;\r\n        const format = hasAlpha ? this._gl.RGBA : this._gl.RGB;\r\n        const data = new Uint8Array(height * width * numChannels);\r\n        if (flushRenderer) {\r\n            this.flushFramebuffer();\r\n        }\r\n        this._gl.readPixels(x, y, width, height, format, this._gl.UNSIGNED_BYTE, data);\r\n        return Promise.resolve(data);\r\n    }\r\n\r\n    // Statics\r\n\r\n    private static _IsSupported: Nullable<boolean> = null;\r\n    private static _HasMajorPerformanceCaveat: Nullable<boolean> = null;\r\n\r\n    /**\r\n     * Gets a Promise<boolean> indicating if the engine can be instantiated (ie. if a webGL context can be found)\r\n     */\r\n    public static get IsSupportedAsync(): Promise<boolean> {\r\n        return Promise.resolve(this.isSupported());\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the engine can be instantiated (ie. if a webGL context can be found)\r\n     */\r\n    public static get IsSupported(): boolean {\r\n        return this.isSupported(); // Backward compat\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the engine can be instantiated (ie. if a webGL context can be found)\r\n     * @returns true if the engine can be created\r\n     * @ignorenaming\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static isSupported(): boolean {\r\n        if (this._HasMajorPerformanceCaveat !== null) {\r\n            return !this._HasMajorPerformanceCaveat; // We know it is performant so WebGL is supported\r\n        }\r\n\r\n        if (this._IsSupported === null) {\r\n            try {\r\n                const tempcanvas = this._CreateCanvas(1, 1);\r\n                const gl = tempcanvas.getContext(\"webgl\") || (tempcanvas as any).getContext(\"experimental-webgl\");\r\n\r\n                this._IsSupported = gl != null && !!window.WebGLRenderingContext;\r\n            } catch (e) {\r\n                this._IsSupported = false;\r\n            }\r\n        }\r\n\r\n        return this._IsSupported;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the engine can be instantiated on a performant device (ie. if a webGL context can be found and it does not use a slow implementation)\r\n     */\r\n    public static get HasMajorPerformanceCaveat(): boolean {\r\n        if (this._HasMajorPerformanceCaveat === null) {\r\n            try {\r\n                const tempcanvas = this._CreateCanvas(1, 1);\r\n                const gl =\r\n                    tempcanvas.getContext(\"webgl\", { failIfMajorPerformanceCaveat: true }) ||\r\n                    (tempcanvas as any).getContext(\"experimental-webgl\", { failIfMajorPerformanceCaveat: true });\r\n\r\n                this._HasMajorPerformanceCaveat = !gl;\r\n            } catch (e) {\r\n                this._HasMajorPerformanceCaveat = false;\r\n            }\r\n        }\r\n\r\n        return this._HasMajorPerformanceCaveat;\r\n    }\r\n\r\n    /**\r\n     * Find the next highest power of two.\r\n     * @param x Number to start search from.\r\n     * @returns Next highest power of two.\r\n     */\r\n    public static CeilingPOT(x: number): number {\r\n        x--;\r\n        x |= x >> 1;\r\n        x |= x >> 2;\r\n        x |= x >> 4;\r\n        x |= x >> 8;\r\n        x |= x >> 16;\r\n        x++;\r\n        return x;\r\n    }\r\n\r\n    /**\r\n     * Find the next lowest power of two.\r\n     * @param x Number to start search from.\r\n     * @returns Next lowest power of two.\r\n     */\r\n    public static FloorPOT(x: number): number {\r\n        x = x | (x >> 1);\r\n        x = x | (x >> 2);\r\n        x = x | (x >> 4);\r\n        x = x | (x >> 8);\r\n        x = x | (x >> 16);\r\n        return x - (x >> 1);\r\n    }\r\n\r\n    /**\r\n     * Find the nearest power of two.\r\n     * @param x Number to start search from.\r\n     * @returns Next nearest power of two.\r\n     */\r\n    public static NearestPOT(x: number): number {\r\n        const c = ThinEngine.CeilingPOT(x);\r\n        const f = ThinEngine.FloorPOT(x);\r\n        return c - x > x - f ? f : c;\r\n    }\r\n\r\n    /**\r\n     * Get the closest exponent of two\r\n     * @param value defines the value to approximate\r\n     * @param max defines the maximum value to return\r\n     * @param mode defines how to define the closest value\r\n     * @returns closest exponent of two of the given value\r\n     */\r\n    public static GetExponentOfTwo(value: number, max: number, mode = Constants.SCALEMODE_NEAREST): number {\r\n        let pot;\r\n\r\n        switch (mode) {\r\n            case Constants.SCALEMODE_FLOOR:\r\n                pot = ThinEngine.FloorPOT(value);\r\n                break;\r\n            case Constants.SCALEMODE_NEAREST:\r\n                pot = ThinEngine.NearestPOT(value);\r\n                break;\r\n            case Constants.SCALEMODE_CEILING:\r\n            default:\r\n                pot = ThinEngine.CeilingPOT(value);\r\n                break;\r\n        }\r\n\r\n        return Math.min(pot, max);\r\n    }\r\n\r\n    /**\r\n     * Queue a new function into the requested animation frame pool (ie. this function will be executed by the browser (or the javascript engine) for the next frame)\r\n     * @param func - the function to be called\r\n     * @param requester - the object that will request the next frame. Falls back to window.\r\n     * @returns frame number\r\n     */\r\n    public static QueueNewFrame(func: () => void, requester?: any): number {\r\n        // Note that there is kind of a typing issue here, as `setTimeout` might return something else than a number (NodeJs returns a NodeJS.Timeout object).\r\n        // Also if the global `requestAnimationFrame`'s returnType is number, `requester.requestPostAnimationFrame` and `requester.requestAnimationFrame` types\r\n        // are `any`.\r\n\r\n        if (!IsWindowObjectExist()) {\r\n            if (typeof requestAnimationFrame === \"function\") {\r\n                return requestAnimationFrame(func);\r\n            }\r\n        } else {\r\n            const { requestAnimationFrame } = requester || window;\r\n            if (typeof requestAnimationFrame === \"function\") {\r\n                return requestAnimationFrame(func);\r\n            }\r\n        }\r\n\r\n        // fallback to the global `setTimeout`.\r\n        // In most cases (aka in the browser), `window` is the global object, so instead of calling `window.setTimeout` we could call the global `setTimeout`.\r\n        return setTimeout(func, 16) as unknown as number;\r\n    }\r\n\r\n    /**\r\n     * Gets host document\r\n     * @returns the host document object\r\n     */\r\n    public getHostDocument(): Nullable<Document> {\r\n        if (this._renderingCanvas && this._renderingCanvas.ownerDocument) {\r\n            return this._renderingCanvas.ownerDocument;\r\n        }\r\n\r\n        return IsDocumentAvailable() ? document : null;\r\n    }\r\n}\r\n\r\ninterface TexImageParameters {\r\n    internalFormat: number;\r\n    format: number;\r\n    type: number;\r\n}\r\n"]}