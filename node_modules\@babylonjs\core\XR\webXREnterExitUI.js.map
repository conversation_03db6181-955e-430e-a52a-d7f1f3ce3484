{"version": 3, "file": "webXREnterExitUI.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/XR/webXREnterExitUI.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAIhD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC;;GAEG;AACH,MAAM,OAAO,sBAAsB;IAC/B;;;;;OAKG;IACH;IACI,qBAAqB;IACd,OAAoB;IAC3B,+CAA+C;IACxC,WAA0B;IACjC,2BAA2B;IACpB,kBAAwC;QAJxC,YAAO,GAAP,OAAO,CAAa;QAEpB,gBAAW,GAAX,WAAW,CAAe;QAE1B,uBAAkB,GAAlB,kBAAkB,CAAsB;IAChD,CAAC;IAEJ;;;OAGG;IACH,6DAA6D;IACtD,MAAM,CAAC,YAA8C,IAAG,CAAC;CACnE;AAED;;GAEG;AACH,MAAM,OAAO,uBAAuB;CAyCnC;AACD;;GAEG;AACH,MAAM,OAAO,gBAAgB;IAmBzB;;;;;OAKG;IACH,YACY,MAAa;IACrB,+CAA+C;IACxC,OAAgC;QAF/B,WAAM,GAAN,MAAM,CAAO;QAEd,YAAO,GAAP,OAAO,CAAyB;QA3BnC,kBAAa,GAAqC,IAAI,CAAC;QACvD,aAAQ,GAAkC,EAAE,CAAC;QAQrD;;;;;;WAMG;QACI,kCAA6B,GAAG,IAAI,UAAU,EAAoC,CAAC;QAqJ1F,6DAA6D;QACrD,sBAAiB,GAAG,CAAC,GAA2B,EAAE,EAAE;YACxD,wCAAwC;YACxC,6GAA6G;YAE7G,mEAAmE;YACnE,iDAAiD;YACjD,qBAAqB;YACrB,yCAAyC;YACzC,iDAAiD;YACjD,iDAAiD;YACjD,2BAA2B;YAC3B,YAAY;YACZ,wBAAwB;YACxB,UAAU;YACV,SAAS;YAET,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;aACnC;QACL,CAAC,CAAC;QA5JE,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAEhD,oCAAoC;QACpC,IAAI,CAAC,OAAO,CAAC,yBAAyB,IAAK,SAAiB,CAAC,EAAE,EAAE;YAC5D,SAAiB,CAAC,EAAE,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACpF;QAED,oCAAoC;QACpC,uCAAuC;QACvC,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;YAC/B,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,KAAK,OAAO,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,KAAK,WAAW,EAAE;gBACrG,KAAK,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;gBAClD,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;aAC1D;SACJ;QAED,IAAI,OAAO,CAAC,aAAa,EAAE;YACvB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,aAAa,CAAC;SACzC;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,0DAA0D,CAAC;YACxF,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,cAAc,CAAC;YAC1D,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,aAAa,CAAC;YACvE,MAAM,GAAG,GACL,OAAO,aAAa,KAAK,WAAW;gBAChC,CAAC,CAAC,+CAA+C;gBACjD,CAAC,CAAC,yiDAAyiD,CAAC;YACpjD,IAAI,GAAG,GACH,yLAAyL;gBACzL,GAAG;gBACH,gUAAgU,CAAC;YACrU,GAAG,IAAI,mJAAmJ,CAAC;YAE3J,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAC9C,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;YAChD,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC;YACnC,MAAM,CAAC,KAAK,GAAG,GAAG,WAAW,MAAM,kBAAkB,EAAE,CAAC;YACxD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,MAAM,EAAE,WAAW,EAAE,kBAAkB,CAAC,CAAC,CAAC;YACxF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,UAAU,YAAoC;gBAC3F,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,YAAY,KAAK,IAAI,IAAI,YAAY,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;gBAC1F,MAAM,CAAC,SAAS,GAAG,eAAe,GAAG,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC/F,CAAC,CAAC;YACF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SAC7B;QAED,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,EAAE,CAAC;QAC1D,IAAI,YAAY,IAAI,YAAY,CAAC,UAAU,EAAE;YACzC,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClD,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;gBACpC,IAAI,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,cAAc,CAAC,MAA6B,EAAE,YAAgC;QACvF,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,MAAM,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YAChD,OAAO,MAAM,CAAC,cAAc,CAAC,uBAAuB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC1C,IAAI,KAAK,IAAI,UAAU,CAAC,SAAS,EAAE;gBAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACrD,OAAO,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE;YAC7B,IAAI,SAAS,EAAE;gBACX,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;gBACnD,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aACjF;iBAAM;gBACH,KAAK,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,WAAW,4BAA4B,CAAC,CAAC;aACzF;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,KAAY,EAAE,MAA6B,EAAE,OAAgC;QACzG,MAAM,EAAE,GAAG,IAAI,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAChD,MAAM,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,YAAY,IAAI,SAAS,CAAC,CAAC;QACnE,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,MAAc,CAAC;QACjD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,EAAE;YACxC,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SAC7B;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,UAAU,CAAC,SAAS,EAAE;YACnD,IAAI;gBACA,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,CAAC,aAAa,EAAE;oBACvH,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;oBAC/C,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;iBAClD,CAAC,CAAC;gBACH,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;aAC3C;YAAC,OAAO,CAAC,EAAE;gBACR,8BAA8B;gBAC9B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;gBAC3C,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC;gBAChC,OAAO,CAAC,KAAK,GAAG,8BAA8B,GAAG,SAAS,CAAC;gBAC3D,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBAClC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;oBACtB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;iBAC3B;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,EAAE,CAAC;QAC/D,IAAI,YAAY,IAAI,YAAY,CAAC,UAAU,IAAI,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAC3F,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACrD;QACD,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC1C,SAAiB,CAAC,EAAE,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACxF,CAAC;IAwBO,cAAc,CAAC,YAA8C;QACjE,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACxB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC3E,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { IDisposable, Scene } from \"../scene\";\r\nimport type { WebXRExperienceHelper } from \"./webXRExperienceHelper\";\r\nimport type { WebXRRenderTarget } from \"./webXRTypes\";\r\nimport { WebXRState } from \"./webXRTypes\";\r\nimport { Tools } from \"../Misc/tools\";\r\n/**\r\n * <PERSON><PERSON> which can be used to enter a different mode of XR\r\n */\r\nexport class WebXREnterExitUIButton {\r\n    /**\r\n     * Creates a WebXREnterExitUIButton\r\n     * @param element button element\r\n     * @param sessionMode XR initialization session mode\r\n     * @param referenceSpaceType the type of reference space to be used\r\n     */\r\n    constructor(\r\n        /** button element */\r\n        public element: HTMLElement,\r\n        /** XR initialization options for the button */\r\n        public sessionMode: XRSessionMode,\r\n        /** Reference space type */\r\n        public referenceSpaceType: XRReferenceSpaceType\r\n    ) {}\r\n\r\n    /**\r\n     * Extendable function which can be used to update the button's visuals when the state changes\r\n     * @param activeButton the current active button in the UI\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public update(activeButton: Nullable<WebXREnterExitUIButton>) {}\r\n}\r\n\r\n/**\r\n * Options to create the webXR UI\r\n */\r\nexport class WebXREnterExitUIOptions {\r\n    /**\r\n     * User provided buttons to enable/disable WebXR. The system will provide default if not set\r\n     */\r\n    customButtons?: Array<WebXREnterExitUIButton>;\r\n    /**\r\n     * A reference space type to use when creating the default button.\r\n     * Default is local-floor\r\n     */\r\n    referenceSpaceType?: XRReferenceSpaceType;\r\n    /**\r\n     * Context to enter xr with\r\n     */\r\n    renderTarget?: Nullable<WebXRRenderTarget>;\r\n    /**\r\n     * A session mode to use when creating the default button.\r\n     * Default is immersive-vr\r\n     */\r\n    sessionMode?: XRSessionMode;\r\n\r\n    /**\r\n     * A list of optional features to init the session with\r\n     */\r\n    optionalFeatures?: string[];\r\n\r\n    /**\r\n     * A list of optional features to init the session with\r\n     */\r\n    requiredFeatures?: string[];\r\n\r\n    /**\r\n     * If set, the `sessiongranted` event will not be registered. `sessiongranted` is used to move seamlessly between WebXR experiences.\r\n     * If set to true the user will be forced to press the \"enter XR\" button even if sessiongranted event was triggered.\r\n     * If not set and a sessiongranted event was triggered, the XR session will start automatically.\r\n     */\r\n    ignoreSessionGrantedEvent?: boolean;\r\n\r\n    /**\r\n     * If defined, this function will be executed if the UI encounters an error when entering XR\r\n     */\r\n    onError?: (error: any) => void;\r\n}\r\n/**\r\n * UI to allow the user to enter/exit XR mode\r\n */\r\nexport class WebXREnterExitUI implements IDisposable {\r\n    private _activeButton: Nullable<WebXREnterExitUIButton> = null;\r\n    private _buttons: Array<WebXREnterExitUIButton> = [];\r\n    private _helper: WebXRExperienceHelper;\r\n    private _renderTarget?: WebXRRenderTarget;\r\n    /**\r\n     * The HTML Div Element to which buttons are added.\r\n     */\r\n    public readonly overlay: HTMLDivElement;\r\n\r\n    /**\r\n     * Fired every time the active button is changed.\r\n     *\r\n     * When xr is entered via a button that launches xr that button will be the callback parameter\r\n     *\r\n     * When exiting xr the callback parameter will be null)\r\n     */\r\n    public activeButtonChangedObservable = new Observable<Nullable<WebXREnterExitUIButton>>();\r\n\r\n    /**\r\n     * Construct a new EnterExit UI class\r\n     *\r\n     * @param _scene babylon scene object to use\r\n     * @param options (read-only) version of the options passed to this UI\r\n     */\r\n    public constructor(\r\n        private _scene: Scene,\r\n        /** version of the options passed to this UI */\r\n        public options: WebXREnterExitUIOptions\r\n    ) {\r\n        this.overlay = document.createElement(\"div\");\r\n        this.overlay.classList.add(\"xr-button-overlay\");\r\n\r\n        // prepare for session granted event\r\n        if (!options.ignoreSessionGrantedEvent && (navigator as any).xr) {\r\n            (navigator as any).xr.addEventListener(\"sessiongranted\", this._onSessionGranted);\r\n        }\r\n\r\n        // if served over HTTP, warn people.\r\n        // Hopefully the browsers will catch up\r\n        if (typeof window !== \"undefined\") {\r\n            if (window.location && window.location.protocol === \"http:\" && window.location.hostname !== \"localhost\") {\r\n                Tools.Warn(\"WebXR can only be served over HTTPS\");\r\n                throw new Error(\"WebXR can only be served over HTTPS\");\r\n            }\r\n        }\r\n\r\n        if (options.customButtons) {\r\n            this._buttons = options.customButtons;\r\n        } else {\r\n            this.overlay.style.cssText = \"z-index:11;position: absolute; right: 20px;bottom: 50px;\";\r\n            const sessionMode = options.sessionMode || \"immersive-vr\";\r\n            const referenceSpaceType = options.referenceSpaceType || \"local-floor\";\r\n            const url =\r\n                typeof SVGSVGElement === \"undefined\"\r\n                    ? \"https://cdn.babylonjs.com/Assets/vrButton.png\"\r\n                    : \"data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%222048%22%20height%3D%221152%22%20viewBox%3D%220%200%202048%201152%22%20version%3D%221.1%22%3E%3Cpath%20transform%3D%22rotate%28180%201024%2C576.0000000000001%29%22%20d%3D%22m1109%2C896q17%2C0%2030%2C-12t13%2C-30t-12.5%2C-30.5t-30.5%2C-12.5l-170%2C0q-18%2C0%20-30.5%2C12.5t-12.5%2C30.5t13%2C30t30%2C12l170%2C0zm-85%2C256q59%2C0%20132.5%2C-1.5t154.5%2C-5.5t164.5%2C-11.5t163%2C-20t150%2C-30t124.5%2C-41.5q23%2C-11%2042%2C-24t38%2C-30q27%2C-25%2041%2C-61.5t14%2C-72.5l0%2C-257q0%2C-123%20-47%2C-232t-128%2C-190t-190%2C-128t-232%2C-47l-81%2C0q-37%2C0%20-68.5%2C14t-60.5%2C34.5t-55.5%2C45t-53%2C45t-53%2C34.5t-55.5%2C14t-55.5%2C-14t-53%2C-34.5t-53%2C-45t-55.5%2C-45t-60.5%2C-34.5t-68.5%2C-14l-81%2C0q-123%2C0%20-232%2C47t-190%2C128t-128%2C190t-47%2C232l0%2C257q0%2C68%2038%2C115t97%2C73q54%2C24%20124.5%2C41.5t150%2C30t163%2C20t164.5%2C11.5t154.5%2C5.5t132.5%2C1.5zm939%2C-298q0%2C39%20-24.5%2C67t-58.5%2C42q-54%2C23%20-122%2C39.5t-143.5%2C28t-155.5%2C19t-157%2C11t-148.5%2C5t-129.5%2C1.5q-59%2C0%20-130%2C-1.5t-148%2C-5t-157%2C-11t-155.5%2C-19t-143.5%2C-28t-122%2C-39.5q-34%2C-14%20-58.5%2C-42t-24.5%2C-67l0%2C-257q0%2C-106%2040.5%2C-199t110%2C-162.5t162.5%2C-109.5t199%2C-40l81%2C0q27%2C0%2052%2C14t50%2C34.5t51%2C44.5t55.5%2C44.5t63.5%2C34.5t74%2C14t74%2C-14t63.5%2C-34.5t55.5%2C-44.5t51%2C-44.5t50%2C-34.5t52%2C-14l14%2C0q37%2C0%2070%2C0.5t64.5%2C4.5t63.5%2C12t68%2C23q71%2C30%20128.5%2C78.5t98.5%2C110t63.5%2C133.5t22.5%2C149l0%2C257z%22%20fill%3D%22white%22%20/%3E%3C/svg%3E%0A\";\r\n            let css =\r\n                \".babylonVRicon { color: #868686; border-color: #868686; border-style: solid; margin-left: 10px; height: 50px; width: 80px; background-color: rgba(51,51,51,0.7); background-image: url(\" +\r\n                url +\r\n                \"); background-size: 80%; background-repeat:no-repeat; background-position: center; border: none; outline: none; transition: transform 0.125s ease-out } .babylonVRicon:hover { transform: scale(1.05) } .babylonVRicon:active {background-color: rgba(51,51,51,1) } .babylonVRicon:focus {background-color: rgba(51,51,51,1) }\";\r\n            css += '.babylonVRicon.vrdisplaypresenting { background-image: none;} .vrdisplaypresenting::after { content: \"EXIT\"} .xr-error::after { content: \"ERROR\"}';\r\n\r\n            const style = document.createElement(\"style\");\r\n            style.appendChild(document.createTextNode(css));\r\n            document.getElementsByTagName(\"head\")[0].appendChild(style);\r\n            const hmdBtn = document.createElement(\"button\");\r\n            hmdBtn.className = \"babylonVRicon\";\r\n            hmdBtn.title = `${sessionMode} - ${referenceSpaceType}`;\r\n            this._buttons.push(new WebXREnterExitUIButton(hmdBtn, sessionMode, referenceSpaceType));\r\n            this._buttons[this._buttons.length - 1].update = function (activeButton: WebXREnterExitUIButton) {\r\n                this.element.style.display = activeButton === null || activeButton === this ? \"\" : \"none\";\r\n                hmdBtn.className = \"babylonVRicon\" + (activeButton === this ? \" vrdisplaypresenting\" : \"\");\r\n            };\r\n            this._updateButtons(null);\r\n        }\r\n\r\n        const renderCanvas = _scene.getEngine().getInputElement();\r\n        if (renderCanvas && renderCanvas.parentNode) {\r\n            renderCanvas.parentNode.appendChild(this.overlay);\r\n            _scene.onDisposeObservable.addOnce(() => {\r\n                this.dispose();\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the helper to be used with this UI component.\r\n     * The UI is bound to an experience helper. If not provided the UI can still be used but the events should be registered by the developer.\r\n     *\r\n     * @param helper the experience helper to attach\r\n     * @param renderTarget an optional render target (in case it is created outside of the helper scope)\r\n     * @returns a promise that resolves when the ui is ready\r\n     */\r\n    public async setHelperAsync(helper: WebXRExperienceHelper, renderTarget?: WebXRRenderTarget): Promise<void> {\r\n        this._helper = helper;\r\n        this._renderTarget = renderTarget;\r\n        const supportedPromises = this._buttons.map((btn) => {\r\n            return helper.sessionManager.isSessionSupportedAsync(btn.sessionMode);\r\n        });\r\n        helper.onStateChangedObservable.add((state) => {\r\n            if (state == WebXRState.NOT_IN_XR) {\r\n                this._updateButtons(null);\r\n            }\r\n        });\r\n        const results = await Promise.all(supportedPromises);\r\n        results.forEach((supported, i) => {\r\n            if (supported) {\r\n                this.overlay.appendChild(this._buttons[i].element);\r\n                this._buttons[i].element.onclick = this._enterXRWithButtonIndex.bind(this, i);\r\n            } else {\r\n                Tools.Warn(`Session mode \"${this._buttons[i].sessionMode}\" not supported in browser`);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates UI to allow the user to enter/exit XR mode\r\n     * @param scene the scene to add the ui to\r\n     * @param helper the xr experience helper to enter/exit xr with\r\n     * @param options options to configure the UI\r\n     * @returns the created ui\r\n     */\r\n    public static async CreateAsync(scene: Scene, helper: WebXRExperienceHelper, options: WebXREnterExitUIOptions): Promise<WebXREnterExitUI> {\r\n        const ui = new WebXREnterExitUI(scene, options);\r\n        await ui.setHelperAsync(helper, options.renderTarget || undefined);\r\n        return ui;\r\n    }\r\n\r\n    private async _enterXRWithButtonIndex(idx: number = 0) {\r\n        if (this._helper.state == WebXRState.IN_XR) {\r\n            await this._helper.exitXRAsync();\r\n            this._updateButtons(null);\r\n        } else if (this._helper.state == WebXRState.NOT_IN_XR) {\r\n            try {\r\n                await this._helper.enterXRAsync(this._buttons[idx].sessionMode, this._buttons[idx].referenceSpaceType, this._renderTarget, {\r\n                    optionalFeatures: this.options.optionalFeatures,\r\n                    requiredFeatures: this.options.requiredFeatures,\r\n                });\r\n                this._updateButtons(this._buttons[idx]);\r\n            } catch (e) {\r\n                // make sure button is visible\r\n                this._updateButtons(null);\r\n                const element = this._buttons[idx].element;\r\n                const prevTitle = element.title;\r\n                element.title = \"Error entering XR session : \" + prevTitle;\r\n                element.classList.add(\"xr-error\");\r\n                if (this.options.onError) {\r\n                    this.options.onError(e);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disposes of the XR UI component\r\n     */\r\n    public dispose() {\r\n        const renderCanvas = this._scene.getEngine().getInputElement();\r\n        if (renderCanvas && renderCanvas.parentNode && renderCanvas.parentNode.contains(this.overlay)) {\r\n            renderCanvas.parentNode.removeChild(this.overlay);\r\n        }\r\n        this.activeButtonChangedObservable.clear();\r\n        (navigator as any).xr.removeEventListener(\"sessiongranted\", this._onSessionGranted);\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _onSessionGranted = (evt: { session: XRSession }) => {\r\n        // This section is for future reference.\r\n        // As per specs, evt.session.mode should have the supported session mode, but no browser supports it for now.\r\n\r\n        // // check if the session granted is the same as the one requested\r\n        // const grantedMode = (evt.session as any).mode;\r\n        // if (grantedMode) {\r\n        //     this._buttons.some((btn, idx) => {\r\n        //         if (btn.sessionMode === grantedMode) {\r\n        //             this._enterXRWithButtonIndex(idx);\r\n        //             return true;\r\n        //         }\r\n        //         return false;\r\n        //     });\r\n        // } else\r\n\r\n        if (this._helper) {\r\n            this._enterXRWithButtonIndex(0);\r\n        }\r\n    };\r\n\r\n    private _updateButtons(activeButton: Nullable<WebXREnterExitUIButton>) {\r\n        this._activeButton = activeButton;\r\n        this._buttons.forEach((b) => {\r\n            b.update(this._activeButton);\r\n        });\r\n        this.activeButtonChangedObservable.notifyObservers(this._activeButton);\r\n    }\r\n}\r\n"]}