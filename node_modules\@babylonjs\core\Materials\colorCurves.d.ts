import type { Effect } from "../Materials/effect";
/**
 * The color grading curves provide additional color adjustment that is applied after any color grading transform (3D LUT).
 * They allow basic adjustment of saturation and small exposure adjustments, along with color filter tinting to provide white balance adjustment or more stylistic effects.
 * These are similar to controls found in many professional imaging or colorist software. The global controls are applied to the entire image. For advanced tuning, extra controls are provided to adjust the shadow, midtone and highlight areas of the image;
 * corresponding to low luminance, medium luminance, and high luminance areas respectively.
 */
export declare class ColorCurves {
    private _dirty;
    private _tempColor;
    private _globalCurve;
    private _highlightsCurve;
    private _midtonesCurve;
    private _shadowsCurve;
    private _positiveCurve;
    private _negativeCurve;
    private _globalHue;
    private _globalDensity;
    private _globalSaturation;
    private _globalExposure;
    /**
     * Gets the global Hue value.
     * The hue value is a standard HSB hue in the range [0,360] where 0=red, 120=green and 240=blue. The default value is 30 degrees (orange).
     */
    get globalHue(): number;
    /**
     * Sets the global Hue value.
     * The hue value is a standard HSB hue in the range [0,360] where 0=red, 120=green and 240=blue. The default value is 30 degrees (orange).
     */
    set globalHue(value: number);
    /**
     * Gets the global Density value.
     * The density value is in range [-100,+100] where 0 means the color filter has no effect and +100 means the color filter has maximum effect.
     * Values less than zero provide a filter of opposite hue.
     */
    get globalDensity(): number;
    /**
     * Sets the global Density value.
     * The density value is in range [-100,+100] where 0 means the color filter has no effect and +100 means the color filter has maximum effect.
     * Values less than zero provide a filter of opposite hue.
     */
    set globalDensity(value: number);
    /**
     * Gets the global Saturation value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase saturation and negative values decrease saturation.
     */
    get globalSaturation(): number;
    /**
     * Sets the global Saturation value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase saturation and negative values decrease saturation.
     */
    set globalSaturation(value: number);
    /**
     * Gets the global Exposure value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase exposure and negative values decrease exposure.
     */
    get globalExposure(): number;
    /**
     * Sets the global Exposure value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase exposure and negative values decrease exposure.
     */
    set globalExposure(value: number);
    private _highlightsHue;
    private _highlightsDensity;
    private _highlightsSaturation;
    private _highlightsExposure;
    /**
     * Gets the highlights Hue value.
     * The hue value is a standard HSB hue in the range [0,360] where 0=red, 120=green and 240=blue. The default value is 30 degrees (orange).
     */
    get highlightsHue(): number;
    /**
     * Sets the highlights Hue value.
     * The hue value is a standard HSB hue in the range [0,360] where 0=red, 120=green and 240=blue. The default value is 30 degrees (orange).
     */
    set highlightsHue(value: number);
    /**
     * Gets the highlights Density value.
     * The density value is in range [-100,+100] where 0 means the color filter has no effect and +100 means the color filter has maximum effect.
     * Values less than zero provide a filter of opposite hue.
     */
    get highlightsDensity(): number;
    /**
     * Sets the highlights Density value.
     * The density value is in range [-100,+100] where 0 means the color filter has no effect and +100 means the color filter has maximum effect.
     * Values less than zero provide a filter of opposite hue.
     */
    set highlightsDensity(value: number);
    /**
     * Gets the highlights Saturation value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase saturation and negative values decrease saturation.
     */
    get highlightsSaturation(): number;
    /**
     * Sets the highlights Saturation value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase saturation and negative values decrease saturation.
     */
    set highlightsSaturation(value: number);
    /**
     * Gets the highlights Exposure value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase exposure and negative values decrease exposure.
     */
    get highlightsExposure(): number;
    /**
     * Sets the highlights Exposure value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase exposure and negative values decrease exposure.
     */
    set highlightsExposure(value: number);
    private _midtonesHue;
    private _midtonesDensity;
    private _midtonesSaturation;
    private _midtonesExposure;
    /**
     * Gets the midtones Hue value.
     * The hue value is a standard HSB hue in the range [0,360] where 0=red, 120=green and 240=blue. The default value is 30 degrees (orange).
     */
    get midtonesHue(): number;
    /**
     * Sets the midtones Hue value.
     * The hue value is a standard HSB hue in the range [0,360] where 0=red, 120=green and 240=blue. The default value is 30 degrees (orange).
     */
    set midtonesHue(value: number);
    /**
     * Gets the midtones Density value.
     * The density value is in range [-100,+100] where 0 means the color filter has no effect and +100 means the color filter has maximum effect.
     * Values less than zero provide a filter of opposite hue.
     */
    get midtonesDensity(): number;
    /**
     * Sets the midtones Density value.
     * The density value is in range [-100,+100] where 0 means the color filter has no effect and +100 means the color filter has maximum effect.
     * Values less than zero provide a filter of opposite hue.
     */
    set midtonesDensity(value: number);
    /**
     * Gets the midtones Saturation value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase saturation and negative values decrease saturation.
     */
    get midtonesSaturation(): number;
    /**
     * Sets the midtones Saturation value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase saturation and negative values decrease saturation.
     */
    set midtonesSaturation(value: number);
    /**
     * Gets the midtones Exposure value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase exposure and negative values decrease exposure.
     */
    get midtonesExposure(): number;
    /**
     * Sets the midtones Exposure value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase exposure and negative values decrease exposure.
     */
    set midtonesExposure(value: number);
    private _shadowsHue;
    private _shadowsDensity;
    private _shadowsSaturation;
    private _shadowsExposure;
    /**
     * Gets the shadows Hue value.
     * The hue value is a standard HSB hue in the range [0,360] where 0=red, 120=green and 240=blue. The default value is 30 degrees (orange).
     */
    get shadowsHue(): number;
    /**
     * Sets the shadows Hue value.
     * The hue value is a standard HSB hue in the range [0,360] where 0=red, 120=green and 240=blue. The default value is 30 degrees (orange).
     */
    set shadowsHue(value: number);
    /**
     * Gets the shadows Density value.
     * The density value is in range [-100,+100] where 0 means the color filter has no effect and +100 means the color filter has maximum effect.
     * Values less than zero provide a filter of opposite hue.
     */
    get shadowsDensity(): number;
    /**
     * Sets the shadows Density value.
     * The density value is in range [-100,+100] where 0 means the color filter has no effect and +100 means the color filter has maximum effect.
     * Values less than zero provide a filter of opposite hue.
     */
    set shadowsDensity(value: number);
    /**
     * Gets the shadows Saturation value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase saturation and negative values decrease saturation.
     */
    get shadowsSaturation(): number;
    /**
     * Sets the shadows Saturation value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase saturation and negative values decrease saturation.
     */
    set shadowsSaturation(value: number);
    /**
     * Gets the shadows Exposure value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase exposure and negative values decrease exposure.
     */
    get shadowsExposure(): number;
    /**
     * Sets the shadows Exposure value.
     * This is an adjustment value in the range [-100,+100], where the default value of 0.0 makes no adjustment, positive values increase exposure and negative values decrease exposure.
     */
    set shadowsExposure(value: number);
    /**
     * Returns the class name
     * @returns The class name
     */
    getClassName(): string;
    /**
     * Binds the color curves to the shader.
     * @param colorCurves The color curve to bind
     * @param effect The effect to bind to
     * @param positiveUniform The positive uniform shader parameter
     * @param neutralUniform The neutral uniform shader parameter
     * @param negativeUniform The negative uniform shader parameter
     */
    static Bind(colorCurves: ColorCurves, effect: Effect, positiveUniform?: string, neutralUniform?: string, negativeUniform?: string): void;
    /**
     * Prepare the list of uniforms associated with the ColorCurves effects.
     * @param uniformsList The list of uniforms used in the effect
     */
    static PrepareUniforms: (uniformsList: string[]) => void;
    /**
     * Returns color grading data based on a hue, density, saturation and exposure value.
     * @param hue
     * @param density
     * @param saturation The saturation.
     * @param exposure The exposure.
     * @param result The result data container.
     */
    private _getColorGradingDataToRef;
    /**
     * Takes an input slider value and returns an adjusted value that provides extra control near the centre.
     * @param value The input slider value in range [-100,100].
     * @returns Adjusted value.
     */
    private static _ApplyColorGradingSliderNonlinear;
    /**
     * Returns an RGBA Color4 based on Hue, Saturation and Brightness (also referred to as value, HSV).
     * @param hue The hue (H) input.
     * @param saturation The saturation (S) input.
     * @param brightness The brightness (B) input.
     * @param result
     * @result An RGBA color represented as Vector4.
     */
    private static _FromHSBToRef;
    /**
     * Returns a value clamped between min and max
     * @param value The value to clamp
     * @param min The minimum of value
     * @param max The maximum of value
     * @returns The clamped value.
     */
    private static _Clamp;
    /**
     * Clones the current color curve instance.
     * @returns The cloned curves
     */
    clone(): ColorCurves;
    /**
     * Serializes the current color curve instance to a json representation.
     * @returns a JSON representation
     */
    serialize(): any;
    /**
     * Parses the color curve from a json representation.
     * @param source the JSON source to parse
     * @returns The parsed curves
     */
    static Parse(source: any): ColorCurves;
}
