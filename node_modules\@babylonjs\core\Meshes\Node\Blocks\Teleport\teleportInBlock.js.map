{"version": 3, "file": "teleportInBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Meshes/Node/Blocks/Teleport/teleportInBlock.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,qCAAqC,EAAE,MAAM,8CAA8C,CAAC;AACrG,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAI5D;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,iBAAiB;IAGlD,0CAA0C;IAC1C,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAZR,eAAU,GAAuB,EAAE,CAAC;QAaxC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;IAClF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAEM,SAAS,CAAC,WAAqB,EAAE,aAAkC;QACtE,IAAI,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;QAE7D,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;gBACxC,UAAU,IAAI,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;aAChE;SACJ;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,IAAY;QAClC,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;YAC9B,OAAO,IAAI,CAAC;SACf;QAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;gBACnC,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,KAAwB;QAC1C,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI,QAAQ,KAAK,KAAK,EAAE;gBACpB,OAAO,IAAI,CAAC;aACf;YAED,IAAI,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBAChC,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAAC,SAAgD;QAC5E,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;YACjB,OAAO,IAAI,CAAC;SACf;QAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,MAAM,UAAU,GAAG,QAAQ,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;YAEhE,IAAI,UAAU,EAAE;gBACZ,OAAO,UAAU,CAAC;aACrB;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,QAA0B;QAC9C,QAAQ,CAAC,MAAM,EAAE,CAAC;QAElB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/B,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;QAC5B,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7D,QAAQ,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACxC,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,kBAAkB,CAAC,QAA0B;QAChD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACjC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAClD,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;SAC/B;IACL,CAAC;IAES,WAAW;QACjB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,QAAQ,CAAC,MAAM,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;gBACxC,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC/C,CAAC,CAAC;SACL;IACL,CAAC;CACJ;AAED,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"../../Enums/nodeGeometryConnectionPointTypes\";\r\nimport { NodeGeometryBlock } from \"../../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../../nodeGeometryBlockConnectionPoint\";\r\nimport type { TeleportOutBlock } from \"./teleportOutBlock\";\r\n\r\n/**\r\n * Defines a block used to teleport a value to an endpoint\r\n */\r\nexport class TeleportInBlock extends NodeGeometryBlock {\r\n    private _endpoints: TeleportOutBlock[] = [];\r\n\r\n    /** Gets the list of attached endpoints */\r\n    public get endpoints() {\r\n        return this._endpoints;\r\n    }\r\n\r\n    /**\r\n     * Create a new TeleportInBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n        this._isTeleportIn = true;\r\n\r\n        this.registerInput(\"input\", NodeGeometryBlockConnectionPointTypes.AutoDetect);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"TeleportInBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the input component\r\n     */\r\n    public get input(): NodeGeometryConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    public _dumpCode(uniqueNames: string[], alreadyDumped: NodeGeometryBlock[]) {\r\n        let codeString = super._dumpCode(uniqueNames, alreadyDumped);\r\n\r\n        for (const endpoint of this.endpoints) {\r\n            if (alreadyDumped.indexOf(endpoint) === -1) {\r\n                codeString += endpoint._dumpCode(uniqueNames, alreadyDumped);\r\n            }\r\n        }\r\n\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Checks if the current block is an ancestor of a given type\r\n     * @param type defines the potential type to check\r\n     * @returns true if block is a descendant\r\n     */\r\n    public isAnAncestorOfType(type: string): boolean {\r\n        if (this.getClassName() === type) {\r\n            return true;\r\n        }\r\n\r\n        for (const endpoint of this.endpoints) {\r\n            if (endpoint.isAnAncestorOfType(type)) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Checks if the current block is an ancestor of a given block\r\n     * @param block defines the potential descendant block to check\r\n     * @returns true if block is a descendant\r\n     */\r\n    public isAnAncestorOf(block: NodeGeometryBlock): boolean {\r\n        for (const endpoint of this.endpoints) {\r\n            if (endpoint === block) {\r\n                return true;\r\n            }\r\n\r\n            if (endpoint.isAnAncestorOf(block)) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Get the first descendant using a predicate\r\n     * @param predicate defines the predicate to check\r\n     * @returns descendant or null if none found\r\n     */\r\n    public getDescendantOfPredicate(predicate: (block: NodeGeometryBlock) => boolean): Nullable<NodeGeometryBlock> {\r\n        if (predicate(this)) {\r\n            return this;\r\n        }\r\n\r\n        for (const endpoint of this.endpoints) {\r\n            const descendant = endpoint.getDescendantOfPredicate(predicate);\r\n\r\n            if (descendant) {\r\n                return descendant;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Add an enpoint to this block\r\n     * @param endpoint define the endpoint to attach to\r\n     */\r\n    public attachToEndpoint(endpoint: TeleportOutBlock) {\r\n        endpoint.detach();\r\n\r\n        this._endpoints.push(endpoint);\r\n        endpoint._entryPoint = this;\r\n        endpoint._outputs[0]._typeConnectionSource = this._inputs[0];\r\n        endpoint._tempEntryPointUniqueId = null;\r\n        endpoint.name = \"> \" + this.name;\r\n    }\r\n\r\n    /**\r\n     * Remove enpoint from this block\r\n     * @param endpoint define the endpoint to remove\r\n     */\r\n    public detachFromEndpoint(endpoint: TeleportOutBlock) {\r\n        const index = this._endpoints.indexOf(endpoint);\r\n\r\n        if (index !== -1) {\r\n            this._endpoints.splice(index, 1);\r\n            endpoint._outputs[0]._typeConnectionSource = null;\r\n            endpoint._entryPoint = null;\r\n        }\r\n    }\r\n\r\n    protected _buildBlock() {\r\n        for (const endpoint of this._endpoints) {\r\n            endpoint.output._storedFunction = (state) => {\r\n                return this.input.getConnectedValue(state);\r\n            };\r\n        }\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.TeleportInBlock\", TeleportInBlock);\r\n"]}