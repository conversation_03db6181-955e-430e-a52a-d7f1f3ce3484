{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/FlowGraph/utils.ts"], "names": [], "mappings": "AAEA;;;;;;GAMG;AACH,MAAM,UAAU,gBAAgB,CAAC,KAAW,EAAE,KAAW;IACrD,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,KAAK,IAAI,gBAAgB,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACjG,CAAC", "sourcesContent": ["import type { Node } from \"../node\";\r\n\r\n/**\r\n * @internal\r\n * Returns if mesh1 is a descendant of mesh2\r\n * @param mesh1\r\n * @param mesh2\r\n * @returns\r\n */\r\nexport function _isADescendantOf(mesh1: Node, mesh2: Node): boolean {\r\n    return !!(mesh1.parent && (mesh1.parent === mesh2 || _isADescendantOf(mesh1.parent, mesh2)));\r\n}\r\n"]}