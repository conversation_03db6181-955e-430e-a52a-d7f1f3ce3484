{"version": 3, "file": "shaderDefineIsDefinedOperator.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Engines/Processors/Expressions/Operators/shaderDefineIsDefinedOperator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,2BAA2B,CAAC;AAEnE,gBAAgB;AAChB,MAAM,OAAO,6BAA8B,SAAQ,sBAAsB;IACrE,YACW,MAAc,EACd,MAAe,KAAK;QAE3B,KAAK,EAAE,CAAC;QAHD,WAAM,GAAN,MAAM,CAAQ;QACd,QAAG,GAAH,GAAG,CAAiB;IAG/B,CAAC;IAEM,MAAM,CAAC,aAAwC;QAClD,IAAI,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC;QAEzD,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,SAAS,GAAG,CAAC,SAAS,CAAC;SAC1B;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;CACJ", "sourcesContent": ["import { ShaderDefineExpression } from \"../shaderDefineExpression\";\r\n\r\n/** @internal */\r\nexport class ShaderDefineIsDefinedOperator extends ShaderDefineExpression {\r\n    public constructor(\r\n        public define: string,\r\n        public not: boolean = false\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    public isTrue(preprocessors: { [key: string]: string }) {\r\n        let condition = preprocessors[this.define] !== undefined;\r\n\r\n        if (this.not) {\r\n            condition = !condition;\r\n        }\r\n\r\n        return condition;\r\n    }\r\n}\r\n"]}