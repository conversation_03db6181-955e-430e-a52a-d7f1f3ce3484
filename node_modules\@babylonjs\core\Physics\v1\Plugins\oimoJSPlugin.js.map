{"version": 3, "file": "oimoJSPlugin.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Physics/v1/Plugins/oimoJSPlugin.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAE/C,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,4BAA4B,CAAC;AAEjE,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAC;AAElE,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAIxD,gBAAgB;AAChB,MAAM,OAAO,YAAY;IAQrB,YACY,wBAAiC,IAAI,EAC7C,UAAmB,EACnB,aAAa,GAAG,IAAI;QAFZ,0BAAqB,GAArB,qBAAqB,CAAgB;QAP1C,SAAI,GAAW,cAAc,CAAC;QAI7B,mBAAc,GAAW,CAAC,GAAG,EAAE,CAAC;QAmChC,uBAAkB,GAA2B,EAAE,CAAC;QA8MhD,uBAAkB,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QA1OjD,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;YAChC,UAAU,EAAE,UAAU;SACzB,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,GAAG,IAAI,oBAAoB,EAAE,CAAC;IACrD,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,CAAC,CAAC;IACb,CAAC;IAEM,UAAU,CAAC,OAAgB;QAC9B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEM,WAAW,CAAC,QAAgB;QAC/B,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACnC,CAAC;IAEM,WAAW;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC/B,CAAC;IAIM,WAAW,CAAC,KAAa,EAAE,SAAiC;QAC/D,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ;YAChC,QAAQ,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC;QAC/E,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAElB,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC3B,QAAQ,CAAC,SAAS,EAAE,CAAC;YACrB,oCAAoC;YACpC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;QAElC,OAAO,OAAO,KAAK,IAAI,EAAE;YACrB,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACxE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;gBACvB,SAAS;aACZ;YACD,yDAAyD;YACzD,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAClE,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEvE,IAAI,CAAC,YAAY,IAAI,CAAC,iBAAiB,EAAE;gBACrC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;gBACvB,SAAS;aACZ;YAED,YAAY,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,iBAAiB,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;YACpH,iBAAiB,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;YACpH,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;SAC1B;IACL,CAAC;IAEM,YAAY,CAAC,QAAyB,EAAE,KAAc,EAAE,YAAqB;QAChF,MAAM,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC;QACvC,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;IACxH,CAAC;IACM,UAAU,CAAC,QAAyB,EAAE,KAAc,EAAE,YAAqB;QAC9E,MAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;QAC3E,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;IACrD,CAAC;IACM,mBAAmB,CAAC,QAAyB;QAChD,sEAAsE;QACtE,IAAI,QAAQ,CAAC,MAAM,EAAE;YACjB,IAAI,QAAQ,CAAC,WAAW,EAAE;gBACtB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;gBACjC,sBAAsB;gBACtB,QAAQ,CAAC,WAAW,EAAE,CAAC;aAC1B;YACD,OAAO;SACV;QAED,IAAI,QAAQ,CAAC,kBAAkB,EAAE,EAAE;YAC/B,MAAM,UAAU,GAAQ;gBACpB,IAAI,EAAE,QAAQ,CAAC,QAAQ;gBACvB,+CAA+C;gBAC/C,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAC7G,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,EAAE;gBACR,GAAG,EAAE,EAAE;gBACP,QAAQ,EAAE,EAAE;gBACZ,GAAG,EAAE,EAAE;gBACP,QAAQ,EAAE,EAAE;gBACZ,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;gBACrC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAClC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACvC,WAAW,EAAE,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC;gBAC7C,mCAAmC;gBACnC,KAAK,EAAE,IAAI,CAAC,KAAK;aACpB,CAAC;YAEF,MAAM,SAAS,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7B,MAAM,UAAU,GAAG,CAAC,MAA6B,EAAE,EAAE;gBACjD,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;oBACxB,OAAO;iBACV;gBACD,MAAM,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC;oBACvC,IAAI,CAAC,CAAC,eAAe,EAAE;wBACnB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;wBAClC,4BAA4B;qBAC/B;gBACL,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;YACF,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAE5B,MAAM,gBAAgB,GAAG,CAAC,KAAa,EAAU,EAAE;gBAC/C,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACpC,CAAC,CAAC;YAEF,MAAM,gBAAgB,GAAe,IAAI,UAAU,EAAE,CAAC;YAEtD,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBACpB,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,kBAAkB,EAAE;oBAC9B,OAAO;iBACV;gBACD,8BAA8B;gBAC9B,MAAM,aAAa,GAAG,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC;gBAClD,gBAAgB,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAEzC,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5C,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAElC,MAAM,GAAG,GAAG,gBAAgB,CAAC,aAAa,EAAE,CAAC;gBAC7C,MAAM,eAAe,GAAG,CAAC,CAAC,gBAAgB,EAAE,CAAC;gBAE7C,gDAAgD;gBAChD,MAAM,QAAQ,GAAG,qBAAqB,CAAC;gBAEvC,IAAI,CAAC,KAAK,QAAQ,EAAE;oBAChB,MAAM,MAAM,GAAG,QAAQ,CAAC,eAAe,EAAE,CAAC;oBAE1C,QAAQ,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBACvF,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBAE/D,yCAAyC;oBACzC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC9B,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC9B,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC9B,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAElC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;iBACrC;qBAAM;oBACH,MAAM,aAAa,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;oBAChD,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAC1C,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAC1C,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAE1C,gCAAgC;oBAEhC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;iBAClF;gBAED,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;gBAEvD,gBAAgB;gBAChB,QAAQ,CAAC,CAAC,IAAI,EAAE;oBACZ,KAAK,eAAe,CAAC,gBAAgB;wBACjC,MAAM,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;oBAChF,0CAA0C;oBAC1C,KAAK,eAAe,CAAC,cAAc,CAAC,CAAC;wBACjC,MAAM,OAAO,GAAG,eAAe,CAAC,CAAC,CAAC;wBAClC,MAAM,OAAO,GAAG,eAAe,CAAC,CAAC,CAAC;wBAClC,MAAM,OAAO,GAAG,eAAe,CAAC,CAAC,CAAC;wBAElC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;wBAE3G,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAC/B,uDAAuD;wBACvD,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC3B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC3B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC3B,MAAM;qBACT;oBACD,KAAK,eAAe,CAAC,gBAAgB,CAAC,CAAC;wBACnC,MAAM,KAAK,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBACtD,MAAM,KAAK,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAClD,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBACjC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAC5B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAC5B,+DAA+D;wBAC/D,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAC5B,MAAM;qBACT;oBACD,KAAK,eAAe,CAAC,aAAa,CAAC;oBACnC,KAAK,eAAe,CAAC,WAAW,CAAC;oBACjC,OAAO,CAAC,CAAC;wBACL,MAAM,KAAK,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAClD,MAAM,KAAK,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAClD,MAAM,KAAK,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAElD,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAC5B,uBAAuB;wBACvB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAC5B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAC5B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBAC5B,UAAU;wBACV,kCAAkC;wBAClC,GAAG;wBACH,MAAM;qBACT;iBACJ;gBAED,iCAAiC;gBACjC,CAAC,CAAC,MAAM,CAAC,kBAAkB,GAAG,aAAa,CAAC;YAChD,CAAC,CAAC,CAAC;YACH,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAClD,uEAAuE;YACvE,QAAQ,CAAC,WAAW,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;YACvD,kEAAkE;YAClE,QAAQ,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;SAC1C;aAAM;YACH,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACnD;QAED,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAEnD,yFAAyF;QACzF,yGAAyG;IAC7G,CAAC;IAIM,iBAAiB,CAAC,QAAyB;QAC9C,iCAAiC;QACjC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IACrD,CAAC;IAEM,aAAa,CAAC,aAAmC;QACpD,MAAM,QAAQ,GAAG,aAAa,CAAC,YAAY,CAAC,WAAW,CAAC;QACxD,MAAM,aAAa,GAAG,aAAa,CAAC,iBAAiB,CAAC,WAAW,CAAC;QAElE,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,EAAE;YAC7B,OAAO;SACV;QACD,MAAM,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC;QAChD,MAAM,OAAO,GAAG,SAAS,CAAC,YAAY,IAAI,EAAE,CAAC;QAC7C,IAAI,IAAI,CAAC;QACT,MAAM,eAAe,GAAQ;YACzB,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,aAAa;YAEpB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAChF,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1F,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAClF,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAE5F,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS;YACnD,MAAM,EAAE,OAAO,CAAC,MAAM;YAEtB,kCAAkC;YAClC,KAAK,EAAE,IAAI,CAAC,KAAK;SACpB,CAAC;QACF,QAAQ,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE;YAC9B,KAAK,YAAY,CAAC,kBAAkB;gBAChC,IAAI,GAAG,WAAW,CAAC;gBACnB,MAAM;YACV,KAAK,YAAY,CAAC,WAAW,CAAC,CAAC;gBAC3B,MAAM,CAAC,IAAI,CAAC,mFAAmF,CAAC,CAAC;gBACjG,MAAM,UAAU,GAAoB,SAAS,CAAC;gBAC9C,eAAe,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM,IAAI,eAAe,CAAC,GAAG,CAAC;gBAC/D,2DAA2D;gBAC3D,eAAe,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC;aAC5E;YACD,0CAA0C;YAC1C,KAAK,YAAY,CAAC,aAAa;gBAC3B,IAAI,GAAG,eAAe,CAAC;gBACvB,eAAe,CAAC,GAAG,GAAuB,SAAU,CAAC,WAAW,CAAC;gBACjE,MAAM;YACV,KAAK,YAAY,CAAC,cAAc;gBAC5B,IAAI,GAAG,aAAa,CAAC;gBACrB,MAAM;YACV,KAAK,YAAY,CAAC,WAAW;gBACzB,IAAI,GAAG,YAAY,CAAC;gBACpB,MAAM;YACV,KAAK,YAAY,CAAC,UAAU;gBACxB,IAAI,GAAG,YAAY,CAAC;gBACpB,MAAM;YACV,KAAK,YAAY,CAAC,UAAU,CAAC;YAC7B;gBACI,IAAI,GAAG,YAAY,CAAC;gBACpB,MAAM;SACb;QACD,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC;QAC5B,aAAa,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IACvE,CAAC;IAEM,WAAW,CAAC,aAAmC;QAClD,kEAAkE;QAClE,qCAAqC;QACrC,2BAA2B;QAC3B,IAAI;YACA,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;SAC5D;QAAC,OAAO,CAAC,EAAE;YACR,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAClB;IACL,CAAC;IAEM,WAAW;QACd,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC;IACtC,CAAC;IAEM,gCAAgC,CAAC,QAAyB;QAC7D,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE;YAChC,IAAI,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE;gBAClC,IAAI,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzC,OAAO,MAAM,CAAC,IAAI,EAAE;oBAChB,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC;iBACxB;gBACD,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aACzF;iBAAM;gBACH,MAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;gBAC/C,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;aACrD;YAED,IAAI,QAAQ,CAAC,MAAM,CAAC,kBAAkB,EAAE;gBACpC,MAAM,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;gBAClD,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;aAC1E;SACJ;IACL,CAAC;IAEM,4BAA4B,CAAC,QAAyB,EAAE,WAAoB,EAAE,WAAuB;QACxG,MAAM,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC;QAClC,4CAA4C;QAC5C,IAAI,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE;YAClC,OAAO;SACV;QACD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;QACjF,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;;;;;OAMG;IAEI,iBAAiB,CAAC,QAAyB,EAAE,QAAiB;QACjE,QAAQ,CAAC,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IAChF,CAAC;IAEM,kBAAkB,CAAC,QAAyB,EAAE,QAAiB;QAClE,QAAQ,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC;IAEM,iBAAiB,CAAC,QAAyB;QAC9C,MAAM,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,cAAc,CAAC;QAC9C,IAAI,CAAC,CAAC,EAAE;YACJ,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;IACM,kBAAkB,CAAC,QAAyB;QAC/C,MAAM,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,CAAC,EAAE;YACJ,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;IAEM,WAAW,CAAC,QAAyB,EAAE,IAAY;QACtD,MAAM,UAAU,GAAY,IAAI,KAAK,CAAC,CAAC;QACvC,6DAA6D;QAC7D,gDAAgD;QAChD,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5D,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC3D,CAAC;IAEM,WAAW,CAAC,QAAyB;QACxC,OAAO,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC;IAC/C,CAAC;IAEM,eAAe,CAAC,QAAyB;QAC5C,OAAO,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;IAChD,CAAC;IAEM,eAAe,CAAC,QAAyB,EAAE,QAAgB;QAC9D,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACpD,CAAC;IAEM,kBAAkB,CAAC,QAAyB;QAC/C,OAAO,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC;IACnD,CAAC;IAEM,kBAAkB,CAAC,QAAyB,EAAE,WAAmB;QACpE,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC;IAC1D,CAAC;IAEM,SAAS,CAAC,QAAyB;QACtC,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IACjC,CAAC;IAEM,UAAU,CAAC,QAAyB;QACvC,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IACjC,CAAC;IAEM,mBAAmB,CAAC,KAAmB,EAAE,WAAmB,EAAE,WAAoB;QACrF,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU,GAAG,WAAW,CAAC;QACvD,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE;YACxB,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU,GAAG,WAAW,CAAC;SAC1D;IACL,CAAC;IAEM,QAAQ,CAAC,KAAyB,EAAE,KAAa,EAAE,KAAc,EAAE,UAAmB;QACzF,IAAI,KAAK,KAAK,SAAS,EAAE;YACrB,MAAM,CAAC,IAAI,CAAC,0FAA0F,CAAC,CAAC;SAC3G;aAAM;YACH,KAAK,GAAG,GAAG,CAAC;SACf;QACD,KAAK,IAAI,CAAC,CAAC,CAAC;QAEZ,mDAAmD;QACnD,MAAM,KAAK,GAAG,UAAU;YACpB,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,qBAAqB;YAC1C,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,qBAAqB,IAAI,KAAK,CAAC,YAAY,CAAC,oBAAoB,IAAI,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC;QAC3H,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SAChC;IACL,CAAC;IAEM,QAAQ,CAAC,KAAyB,EAAE,UAAkB,EAAE,UAAmB,EAAE,UAAmB;QACnG,mDAAmD;QACnD,MAAM,KAAK,GAAG,UAAU;YACpB,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,qBAAqB;YAC1C,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,qBAAqB,IAAI,KAAK,CAAC,YAAY,CAAC,oBAAoB,IAAI,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC;QAC3H,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,UAAU,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;SAChF;IACL,CAAC;IAEM,oBAAoB,CAAC,IAAkB,EAAE,QAAyB;QACrE,MAAM,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC;QAElC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAElC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;SAClD;IACL,CAAC;IAEM,SAAS,CAAC,QAAyB;QACtC,OAAO,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC;IAC9C,CAAC;IAEM,eAAe,CAAC,QAAyB,EAAE,MAAe;QAC7D,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC;QAC1C,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;QAC/B,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;QAChC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;IACnC,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,IAAa,EAAE,EAAW;QACrC,MAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;QAE7E,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAEpC,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,IAAa,EAAE,EAAW,EAAE,MAA4B;QACxE,MAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;QAE7E,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC3B,CAAC;CACJ", "sourcesContent": ["import type { IPhysicsEnabledObject } from \"../physicsImpostor\";\r\nimport { PhysicsImpostor } from \"../physicsImpostor\";\r\nimport type { IMotorEnabledJoint, DistanceJointData, SpringJointData } from \"../physicsJoint\";\r\nimport { PhysicsJoint } from \"../physicsJoint\";\r\nimport type { AbstractMesh } from \"../../../Meshes/abstractMesh\";\r\nimport { Vector3, Quaternion } from \"../../../Maths/math.vector\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport { Logger } from \"../../../Misc/logger\";\r\nimport { PhysicsRaycastResult } from \"../../physicsRaycastResult\";\r\nimport type { IPhysicsEnginePlugin, PhysicsImpostorJoint } from \"../IPhysicsEnginePlugin\";\r\nimport { Epsilon } from \"../../../Maths/math.constants\";\r\n\r\ndeclare let OIMO: any;\r\n\r\n/** @internal */\r\nexport class OimoJSPlugin implements IPhysicsEnginePlugin {\r\n    public world: any;\r\n    public name: string = \"OimoJSPlugin\";\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public BJSOIMO: any;\r\n    private _raycastResult: PhysicsRaycastResult;\r\n    private _fixedTimeStep: number = 1 / 60;\r\n\r\n    constructor(\r\n        private _useDeltaForWorldStep: boolean = true,\r\n        iterations?: number,\r\n        oimoInjection = OIMO\r\n    ) {\r\n        this.BJSOIMO = oimoInjection;\r\n        this.world = new this.BJSOIMO.World({\r\n            iterations: iterations,\r\n        });\r\n        this.world.clear();\r\n        this._raycastResult = new PhysicsRaycastResult();\r\n    }\r\n\r\n    /**\r\n     *\r\n     * @returns plugin version\r\n     */\r\n    public getPluginVersion(): number {\r\n        return 1;\r\n    }\r\n\r\n    public setGravity(gravity: Vector3) {\r\n        this.world.gravity.set(gravity.x, gravity.y, gravity.z);\r\n    }\r\n\r\n    public setTimeStep(timeStep: number) {\r\n        this.world.timeStep = timeStep;\r\n    }\r\n\r\n    public getTimeStep(): number {\r\n        return this.world.timeStep;\r\n    }\r\n\r\n    private _tmpImpostorsArray: Array<PhysicsImpostor> = [];\r\n\r\n    public executeStep(delta: number, impostors: Array<PhysicsImpostor>) {\r\n        impostors.forEach(function (impostor) {\r\n            impostor.beforeStep();\r\n        });\r\n\r\n        this.world.timeStep = this._useDeltaForWorldStep ? delta : this._fixedTimeStep;\r\n        this.world.step();\r\n\r\n        impostors.forEach((impostor) => {\r\n            impostor.afterStep();\r\n            //update the ordered impostors array\r\n            this._tmpImpostorsArray[impostor.uniqueId] = impostor;\r\n        });\r\n\r\n        //check for collisions\r\n        let contact = this.world.contacts;\r\n\r\n        while (contact !== null) {\r\n            if (contact.touching && !contact.body1.sleeping && !contact.body2.sleeping) {\r\n                contact = contact.next;\r\n                continue;\r\n            }\r\n            //is this body colliding with any other? get the impostor\r\n            const mainImpostor = this._tmpImpostorsArray[+contact.body1.name];\r\n            const collidingImpostor = this._tmpImpostorsArray[+contact.body2.name];\r\n\r\n            if (!mainImpostor || !collidingImpostor) {\r\n                contact = contact.next;\r\n                continue;\r\n            }\r\n\r\n            mainImpostor.onCollide({ body: collidingImpostor.physicsBody, point: null, distance: 0, impulse: 0, normal: null });\r\n            collidingImpostor.onCollide({ body: mainImpostor.physicsBody, point: null, distance: 0, impulse: 0, normal: null });\r\n            contact = contact.next;\r\n        }\r\n    }\r\n\r\n    public applyImpulse(impostor: PhysicsImpostor, force: Vector3, contactPoint: Vector3) {\r\n        const mass = impostor.physicsBody.mass;\r\n        impostor.physicsBody.applyImpulse(contactPoint.scale(this.world.invScale), force.scale(this.world.invScale * mass));\r\n    }\r\n    public applyForce(impostor: PhysicsImpostor, force: Vector3, contactPoint: Vector3) {\r\n        Logger.Warn(\"Oimo doesn't support applying force. Using impulse instead.\");\r\n        this.applyImpulse(impostor, force, contactPoint);\r\n    }\r\n    public generatePhysicsBody(impostor: PhysicsImpostor) {\r\n        //parent-child relationship. Does this impostor has a parent impostor?\r\n        if (impostor.parent) {\r\n            if (impostor.physicsBody) {\r\n                this.removePhysicsBody(impostor);\r\n                //TODO is that needed?\r\n                impostor.forceUpdate();\r\n            }\r\n            return;\r\n        }\r\n\r\n        if (impostor.isBodyInitRequired()) {\r\n            const bodyConfig: any = {\r\n                name: impostor.uniqueId,\r\n                //Oimo must have mass, also for static objects.\r\n                config: [impostor.getParam(\"mass\") || 0.001, impostor.getParam(\"friction\"), impostor.getParam(\"restitution\")],\r\n                size: [],\r\n                type: [],\r\n                pos: [],\r\n                posShape: [],\r\n                rot: [],\r\n                rotShape: [],\r\n                move: impostor.getParam(\"mass\") !== 0,\r\n                density: impostor.getParam(\"mass\"),\r\n                friction: impostor.getParam(\"friction\"),\r\n                restitution: impostor.getParam(\"restitution\"),\r\n                //Supporting older versions of Oimo\r\n                world: this.world,\r\n            };\r\n\r\n            const impostors = [impostor];\r\n            const addToArray = (parent: IPhysicsEnabledObject) => {\r\n                if (!parent.getChildMeshes) {\r\n                    return;\r\n                }\r\n                parent.getChildMeshes().forEach(function (m) {\r\n                    if (m.physicsImpostor) {\r\n                        impostors.push(m.physicsImpostor);\r\n                        //m.physicsImpostor._init();\r\n                    }\r\n                });\r\n            };\r\n            addToArray(impostor.object);\r\n\r\n            const checkWithEpsilon = (value: number): number => {\r\n                return Math.max(value, Epsilon);\r\n            };\r\n\r\n            const globalQuaternion: Quaternion = new Quaternion();\r\n\r\n            impostors.forEach((i) => {\r\n                if (!i.object.rotationQuaternion) {\r\n                    return;\r\n                }\r\n                //get the correct bounding box\r\n                const oldQuaternion = i.object.rotationQuaternion;\r\n                globalQuaternion.copyFrom(oldQuaternion);\r\n\r\n                i.object.rotationQuaternion.set(0, 0, 0, 1);\r\n                i.object.computeWorldMatrix(true);\r\n\r\n                const rot = globalQuaternion.toEulerAngles();\r\n                const impostorExtents = i.getObjectExtents();\r\n\r\n                // eslint-disable-next-line no-loss-of-precision\r\n                const radToDeg = 57.295779513082320876;\r\n\r\n                if (i === impostor) {\r\n                    const center = impostor.getObjectCenter();\r\n\r\n                    impostor.object.getAbsolutePivotPoint().subtractToRef(center, this._tmpPositionVector);\r\n                    this._tmpPositionVector.divideInPlace(impostor.object.scaling);\r\n\r\n                    //Can also use Array.prototype.push.apply\r\n                    bodyConfig.pos.push(center.x);\r\n                    bodyConfig.pos.push(center.y);\r\n                    bodyConfig.pos.push(center.z);\r\n                    bodyConfig.posShape.push(0, 0, 0);\r\n\r\n                    bodyConfig.rotShape.push(0, 0, 0);\r\n                } else {\r\n                    const localPosition = i.object.position.clone();\r\n                    bodyConfig.posShape.push(localPosition.x);\r\n                    bodyConfig.posShape.push(localPosition.y);\r\n                    bodyConfig.posShape.push(localPosition.z);\r\n\r\n                    // bodyConfig.pos.push(0, 0, 0);\r\n\r\n                    bodyConfig.rotShape.push(rot.x * radToDeg, rot.y * radToDeg, rot.z * radToDeg);\r\n                }\r\n\r\n                i.object.rotationQuaternion.copyFrom(globalQuaternion);\r\n\r\n                // register mesh\r\n                switch (i.type) {\r\n                    case PhysicsImpostor.ParticleImpostor:\r\n                        Logger.Warn(\"No Particle support in OIMO.js. using SphereImpostor instead\");\r\n                    // eslint-disable-next-line no-fallthrough\r\n                    case PhysicsImpostor.SphereImpostor: {\r\n                        const radiusX = impostorExtents.x;\r\n                        const radiusY = impostorExtents.y;\r\n                        const radiusZ = impostorExtents.z;\r\n\r\n                        const size = Math.max(checkWithEpsilon(radiusX), checkWithEpsilon(radiusY), checkWithEpsilon(radiusZ)) / 2;\r\n\r\n                        bodyConfig.type.push(\"sphere\");\r\n                        //due to the way oimo works with compounds, add 3 times\r\n                        bodyConfig.size.push(size);\r\n                        bodyConfig.size.push(size);\r\n                        bodyConfig.size.push(size);\r\n                        break;\r\n                    }\r\n                    case PhysicsImpostor.CylinderImpostor: {\r\n                        const sizeX = checkWithEpsilon(impostorExtents.x) / 2;\r\n                        const sizeY = checkWithEpsilon(impostorExtents.y);\r\n                        bodyConfig.type.push(\"cylinder\");\r\n                        bodyConfig.size.push(sizeX);\r\n                        bodyConfig.size.push(sizeY);\r\n                        //due to the way oimo works with compounds, add one more value.\r\n                        bodyConfig.size.push(sizeY);\r\n                        break;\r\n                    }\r\n                    case PhysicsImpostor.PlaneImpostor:\r\n                    case PhysicsImpostor.BoxImpostor:\r\n                    default: {\r\n                        const sizeX = checkWithEpsilon(impostorExtents.x);\r\n                        const sizeY = checkWithEpsilon(impostorExtents.y);\r\n                        const sizeZ = checkWithEpsilon(impostorExtents.z);\r\n\r\n                        bodyConfig.type.push(\"box\");\r\n                        //if (i === impostor) {\r\n                        bodyConfig.size.push(sizeX);\r\n                        bodyConfig.size.push(sizeY);\r\n                        bodyConfig.size.push(sizeZ);\r\n                        //} else {\r\n                        //    bodyConfig.size.push(0,0,0);\r\n                        //}\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                //actually not needed, but hey...\r\n                i.object.rotationQuaternion = oldQuaternion;\r\n            });\r\n            impostor.physicsBody = this.world.add(bodyConfig);\r\n            // set the quaternion, ignoring the previously defined (euler) rotation\r\n            impostor.physicsBody.resetQuaternion(globalQuaternion);\r\n            // update with delta 0, so the body will receive the new rotation.\r\n            impostor.physicsBody.updatePosition(0);\r\n        } else {\r\n            this._tmpPositionVector.copyFromFloats(0, 0, 0);\r\n        }\r\n\r\n        impostor.setDeltaPosition(this._tmpPositionVector);\r\n\r\n        //this._tmpPositionVector.addInPlace(impostor.mesh.getBoundingInfo().boundingBox.center);\r\n        //this.setPhysicsBodyTransformation(impostor, this._tmpPositionVector, impostor.mesh.rotationQuaternion);\r\n    }\r\n\r\n    private _tmpPositionVector: Vector3 = Vector3.Zero();\r\n\r\n    public removePhysicsBody(impostor: PhysicsImpostor) {\r\n        //impostor.physicsBody.dispose();\r\n        this.world.removeRigidBody(impostor.physicsBody);\r\n    }\r\n\r\n    public generateJoint(impostorJoint: PhysicsImpostorJoint) {\r\n        const mainBody = impostorJoint.mainImpostor.physicsBody;\r\n        const connectedBody = impostorJoint.connectedImpostor.physicsBody;\r\n\r\n        if (!mainBody || !connectedBody) {\r\n            return;\r\n        }\r\n        const jointData = impostorJoint.joint.jointData;\r\n        const options = jointData.nativeParams || {};\r\n        let type;\r\n        const nativeJointData: any = {\r\n            body1: mainBody,\r\n            body2: connectedBody,\r\n\r\n            axe1: options.axe1 || (jointData.mainAxis ? jointData.mainAxis.asArray() : null),\r\n            axe2: options.axe2 || (jointData.connectedAxis ? jointData.connectedAxis.asArray() : null),\r\n            pos1: options.pos1 || (jointData.mainPivot ? jointData.mainPivot.asArray() : null),\r\n            pos2: options.pos2 || (jointData.connectedPivot ? jointData.connectedPivot.asArray() : null),\r\n\r\n            min: options.min,\r\n            max: options.max,\r\n            collision: options.collision || jointData.collision,\r\n            spring: options.spring,\r\n\r\n            //supporting older version of Oimo\r\n            world: this.world,\r\n        };\r\n        switch (impostorJoint.joint.type) {\r\n            case PhysicsJoint.BallAndSocketJoint:\r\n                type = \"jointBall\";\r\n                break;\r\n            case PhysicsJoint.SpringJoint: {\r\n                Logger.Warn(\"OIMO.js doesn't support Spring Constraint. Simulating using DistanceJoint instead\");\r\n                const springData = <SpringJointData>jointData;\r\n                nativeJointData.min = springData.length || nativeJointData.min;\r\n                //Max should also be set, just make sure it is at least min\r\n                nativeJointData.max = Math.max(nativeJointData.min, nativeJointData.max);\r\n            }\r\n            // eslint-disable-next-line no-fallthrough\r\n            case PhysicsJoint.DistanceJoint:\r\n                type = \"jointDistance\";\r\n                nativeJointData.max = (<DistanceJointData>jointData).maxDistance;\r\n                break;\r\n            case PhysicsJoint.PrismaticJoint:\r\n                type = \"jointPrisme\";\r\n                break;\r\n            case PhysicsJoint.SliderJoint:\r\n                type = \"jointSlide\";\r\n                break;\r\n            case PhysicsJoint.WheelJoint:\r\n                type = \"jointWheel\";\r\n                break;\r\n            case PhysicsJoint.HingeJoint:\r\n            default:\r\n                type = \"jointHinge\";\r\n                break;\r\n        }\r\n        nativeJointData.type = type;\r\n        impostorJoint.joint.physicsJoint = this.world.add(nativeJointData);\r\n    }\r\n\r\n    public removeJoint(impostorJoint: PhysicsImpostorJoint) {\r\n        //Bug in Oimo prevents us from disposing a joint in the playground\r\n        //joint.joint.physicsJoint.dispose();\r\n        //So we will bruteforce it!\r\n        try {\r\n            this.world.removeJoint(impostorJoint.joint.physicsJoint);\r\n        } catch (e) {\r\n            Logger.Warn(e);\r\n        }\r\n    }\r\n\r\n    public isSupported(): boolean {\r\n        return this.BJSOIMO !== undefined;\r\n    }\r\n\r\n    public setTransformationFromPhysicsBody(impostor: PhysicsImpostor) {\r\n        if (!impostor.physicsBody.sleeping) {\r\n            if (impostor.physicsBody.shapes.next) {\r\n                let parent = impostor.physicsBody.shapes;\r\n                while (parent.next) {\r\n                    parent = parent.next;\r\n                }\r\n                impostor.object.position.set(parent.position.x, parent.position.y, parent.position.z);\r\n            } else {\r\n                const pos = impostor.physicsBody.getPosition();\r\n                impostor.object.position.set(pos.x, pos.y, pos.z);\r\n            }\r\n\r\n            if (impostor.object.rotationQuaternion) {\r\n                const quat = impostor.physicsBody.getQuaternion();\r\n                impostor.object.rotationQuaternion.set(quat.x, quat.y, quat.z, quat.w);\r\n            }\r\n        }\r\n    }\r\n\r\n    public setPhysicsBodyTransformation(impostor: PhysicsImpostor, newPosition: Vector3, newRotation: Quaternion) {\r\n        const body = impostor.physicsBody;\r\n        // disable bidirectional for compound meshes\r\n        if (impostor.physicsBody.shapes.next) {\r\n            return;\r\n        }\r\n        body.position.set(newPosition.x, newPosition.y, newPosition.z);\r\n        body.orientation.set(newRotation.x, newRotation.y, newRotation.z, newRotation.w);\r\n        body.syncShapes();\r\n        body.awake();\r\n    }\r\n\r\n    /*private _getLastShape(body: any): any {\r\n        var lastShape = body.shapes;\r\n        while (lastShape.next) {\r\n            lastShape = lastShape.next;\r\n        }\r\n        return lastShape;\r\n    }*/\r\n\r\n    public setLinearVelocity(impostor: PhysicsImpostor, velocity: Vector3) {\r\n        impostor.physicsBody.linearVelocity.set(velocity.x, velocity.y, velocity.z);\r\n    }\r\n\r\n    public setAngularVelocity(impostor: PhysicsImpostor, velocity: Vector3) {\r\n        impostor.physicsBody.angularVelocity.set(velocity.x, velocity.y, velocity.z);\r\n    }\r\n\r\n    public getLinearVelocity(impostor: PhysicsImpostor): Nullable<Vector3> {\r\n        const v = impostor.physicsBody.linearVelocity;\r\n        if (!v) {\r\n            return null;\r\n        }\r\n        return new Vector3(v.x, v.y, v.z);\r\n    }\r\n    public getAngularVelocity(impostor: PhysicsImpostor): Nullable<Vector3> {\r\n        const v = impostor.physicsBody.angularVelocity;\r\n        if (!v) {\r\n            return null;\r\n        }\r\n        return new Vector3(v.x, v.y, v.z);\r\n    }\r\n\r\n    public setBodyMass(impostor: PhysicsImpostor, mass: number) {\r\n        const staticBody: boolean = mass === 0;\r\n        //this will actually set the body's density and not its mass.\r\n        //But this is how oimo treats the mass variable.\r\n        impostor.physicsBody.shapes.density = staticBody ? 1 : mass;\r\n        impostor.physicsBody.setupMass(staticBody ? 0x2 : 0x1);\r\n    }\r\n\r\n    public getBodyMass(impostor: PhysicsImpostor): number {\r\n        return impostor.physicsBody.shapes.density;\r\n    }\r\n\r\n    public getBodyFriction(impostor: PhysicsImpostor): number {\r\n        return impostor.physicsBody.shapes.friction;\r\n    }\r\n\r\n    public setBodyFriction(impostor: PhysicsImpostor, friction: number) {\r\n        impostor.physicsBody.shapes.friction = friction;\r\n    }\r\n\r\n    public getBodyRestitution(impostor: PhysicsImpostor): number {\r\n        return impostor.physicsBody.shapes.restitution;\r\n    }\r\n\r\n    public setBodyRestitution(impostor: PhysicsImpostor, restitution: number) {\r\n        impostor.physicsBody.shapes.restitution = restitution;\r\n    }\r\n\r\n    public sleepBody(impostor: PhysicsImpostor) {\r\n        impostor.physicsBody.sleep();\r\n    }\r\n\r\n    public wakeUpBody(impostor: PhysicsImpostor) {\r\n        impostor.physicsBody.awake();\r\n    }\r\n\r\n    public updateDistanceJoint(joint: PhysicsJoint, maxDistance: number, minDistance?: number) {\r\n        joint.physicsJoint.limitMotor.upperLimit = maxDistance;\r\n        if (minDistance !== void 0) {\r\n            joint.physicsJoint.limitMotor.lowerLimit = minDistance;\r\n        }\r\n    }\r\n\r\n    public setMotor(joint: IMotorEnabledJoint, speed: number, force?: number, motorIndex?: number) {\r\n        if (force !== undefined) {\r\n            Logger.Warn(\"OimoJS plugin currently has unexpected behavior when using setMotor with force parameter\");\r\n        } else {\r\n            force = 1e6;\r\n        }\r\n        speed *= -1;\r\n\r\n        //TODO separate rotational and transational motors.\r\n        const motor = motorIndex\r\n            ? joint.physicsJoint.rotationalLimitMotor2\r\n            : joint.physicsJoint.rotationalLimitMotor1 || joint.physicsJoint.rotationalLimitMotor || joint.physicsJoint.limitMotor;\r\n        if (motor) {\r\n            motor.setMotor(speed, force);\r\n        }\r\n    }\r\n\r\n    public setLimit(joint: IMotorEnabledJoint, upperLimit: number, lowerLimit?: number, motorIndex?: number) {\r\n        //TODO separate rotational and transational motors.\r\n        const motor = motorIndex\r\n            ? joint.physicsJoint.rotationalLimitMotor2\r\n            : joint.physicsJoint.rotationalLimitMotor1 || joint.physicsJoint.rotationalLimitMotor || joint.physicsJoint.limitMotor;\r\n        if (motor) {\r\n            motor.setLimit(upperLimit, lowerLimit === void 0 ? -upperLimit : lowerLimit);\r\n        }\r\n    }\r\n\r\n    public syncMeshWithImpostor(mesh: AbstractMesh, impostor: PhysicsImpostor) {\r\n        const body = impostor.physicsBody;\r\n\r\n        mesh.position.x = body.position.x;\r\n        mesh.position.y = body.position.y;\r\n        mesh.position.z = body.position.z;\r\n\r\n        if (mesh.rotationQuaternion) {\r\n            mesh.rotationQuaternion.x = body.orientation.x;\r\n            mesh.rotationQuaternion.y = body.orientation.y;\r\n            mesh.rotationQuaternion.z = body.orientation.z;\r\n            mesh.rotationQuaternion.w = body.orientation.w;\r\n        }\r\n    }\r\n\r\n    public getRadius(impostor: PhysicsImpostor): number {\r\n        return impostor.physicsBody.shapes.radius;\r\n    }\r\n\r\n    public getBoxSizeToRef(impostor: PhysicsImpostor, result: Vector3): void {\r\n        const shape = impostor.physicsBody.shapes;\r\n        result.x = shape.halfWidth * 2;\r\n        result.y = shape.halfHeight * 2;\r\n        result.z = shape.halfDepth * 2;\r\n    }\r\n\r\n    public dispose() {\r\n        this.world.clear();\r\n    }\r\n\r\n    /**\r\n     * Does a raycast in the physics world\r\n     * @param from when should the ray start?\r\n     * @param to when should the ray end?\r\n     * @returns PhysicsRaycastResult\r\n     */\r\n    public raycast(from: Vector3, to: Vector3): PhysicsRaycastResult {\r\n        Logger.Warn(\"raycast is not currently supported by the Oimo physics plugin\");\r\n\r\n        this._raycastResult.reset(from, to);\r\n\r\n        return this._raycastResult;\r\n    }\r\n\r\n    /**\r\n     * Does a raycast in the physics world\r\n     * @param from when should the ray start?\r\n     * @param to when should the ray end?\r\n     * @param result resulting PhysicsRaycastResult\r\n     */\r\n    public raycastToRef(from: Vector3, to: Vector3, result: PhysicsRaycastResult): void {\r\n        Logger.Warn(\"raycast is not currently supported by the Oimo physics plugin\");\r\n\r\n        result.reset(from, to);\r\n    }\r\n}\r\n"]}