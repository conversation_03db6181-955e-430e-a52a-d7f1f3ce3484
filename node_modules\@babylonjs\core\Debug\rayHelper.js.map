{"version": 3, "file": "rayHelper.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Debug/rayHelper.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAM/C,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAG9D;;;;GAIG;AACH,MAAM,OAAO,SAAS;IAiBlB;;;;;;OAMG;IACI,MAAM,CAAC,aAAa,CAAC,GAAQ,EAAE,KAAY,EAAE,KAAa;QAC7D,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;QAElC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAE1B,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;OAMG;IACH,YAAY,GAAQ;QAChB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,IAAI,CAAC,KAAY,EAAE,KAAc;QACpC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,GAAG,EAAE;YACnC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;YAErB,IAAI,CAAC,eAAe,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YACpB,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACnF,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;YAC9F,IAAI,CAAC,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC;YAEpC,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aAC1D;SACJ;QAED,IAAI,KAAK,IAAI,IAAI,CAAC,WAAW,EAAE;YAC3B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SAC1C;IACL,CAAC;IAED;;OAEG;IACI,IAAI;QACP,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACzD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;aAC3B;YAED,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;SAC3B;IACL,CAAC;IAEO,OAAO;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAErB,IAAI,CAAC,GAAG,EAAE;YACN,OAAO;SACV;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAE1C,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9B,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACxB,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE7B,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE3C,WAAW,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAE7G,IAAI,CAAC,WAAW,EAAE,mBAAmB,EAAE,CAAC;IAC5C,CAAC;IAED;;;;;;OAMG;IACI,YAAY,CAAC,IAAkB,EAAE,kBAA4B,EAAE,eAAyB,EAAE,MAAe;QAC5G,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAErB,IAAI,CAAC,GAAG,EAAE;YACN,OAAO;SACV;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;YAChB,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;SAClC;QAED,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YACb,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;SAC/B;QAED,IAAI,MAAM,EAAE;YACR,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC;SACvB;QAED,IAAI,CAAC,eAAe,EAAE;YAClB,eAAe,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;SACpC;QAED,IAAI,CAAC,kBAAkB,EAAE;YACrB,6CAA6C;YAC7C,kBAAkB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAC9C;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;SACjC;QAED,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC,KAAK,EAAE,CAAC;YACtD,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;SACnD;aAAM;YACH,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;YACtD,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;SACnD;QAED,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;YACnG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;SACjG;QAED,yEAAyE;QACzE,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,cAAc;QACjB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,EAAE;YACrC,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACzE,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;aACvE;YACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACtB;IACL,CAAC;IAEO,aAAa;QACjB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAErB,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,GAAG,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,EAAE;YACnC,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO;SACV;QAED,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;QAChF,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;IAChH,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;IACpB,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Ray } from \"../Culling/ray\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Color3 } from \"../Maths/math.color\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { LinesMesh } from \"../Meshes/linesMesh\";\r\n\r\nimport { CreateLines } from \"../Meshes/Builders/linesBuilder\";\r\nimport type { Observer } from \"../Misc/observable\";\r\n\r\n/**\r\n * As raycast might be hard to debug, the RayHelper can help rendering the different rays\r\n * in order to better appreciate the issue one might have.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/interactions/picking_collisions#debugging\r\n */\r\nexport class RayHelper {\r\n    /**\r\n     * Defines the ray we are currently trying to visualize.\r\n     */\r\n    public ray: Nullable<Ray>;\r\n\r\n    private _renderPoints: Vector3[];\r\n    private _renderLine: Nullable<LinesMesh>;\r\n    private _renderFunction: Nullable<() => void>;\r\n    private _scene: Nullable<Scene>;\r\n\r\n    private _onAfterRenderObserver: Nullable<Observer<Scene>>;\r\n    private _onAfterStepObserver: Nullable<Observer<Scene>>;\r\n    private _attachedToMesh: Nullable<AbstractMesh>;\r\n    private _meshSpaceDirection: Vector3;\r\n    private _meshSpaceOrigin: Vector3;\r\n\r\n    /**\r\n     * Helper function to create a colored helper in a scene in one line.\r\n     * @param ray Defines the ray we are currently trying to visualize\r\n     * @param scene Defines the scene the ray is used in\r\n     * @param color Defines the color we want to see the ray in\r\n     * @returns The newly created ray helper.\r\n     */\r\n    public static CreateAndShow(ray: Ray, scene: Scene, color: Color3): RayHelper {\r\n        const helper = new RayHelper(ray);\r\n\r\n        helper.show(scene, color);\r\n\r\n        return helper;\r\n    }\r\n\r\n    /**\r\n     * Instantiate a new ray helper.\r\n     * As raycast might be hard to debug, the RayHelper can help rendering the different rays\r\n     * in order to better appreciate the issue one might have.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/interactions/picking_collisions#debugging\r\n     * @param ray Defines the ray we are currently trying to visualize\r\n     */\r\n    constructor(ray: Ray) {\r\n        this.ray = ray;\r\n    }\r\n\r\n    /**\r\n     * Shows the ray we are willing to debug.\r\n     * @param scene Defines the scene the ray needs to be rendered in\r\n     * @param color Defines the color the ray needs to be rendered in\r\n     */\r\n    public show(scene: Scene, color?: Color3): void {\r\n        if (!this._renderFunction && this.ray) {\r\n            const ray = this.ray;\r\n\r\n            this._renderFunction = () => this._render();\r\n            this._scene = scene;\r\n            this._renderPoints = [ray.origin, ray.origin.add(ray.direction.scale(ray.length))];\r\n            this._renderLine = CreateLines(\"ray\", { points: this._renderPoints, updatable: true }, scene);\r\n            this._renderLine.isPickable = false;\r\n\r\n            if (this._renderFunction) {\r\n                this._scene.registerBeforeRender(this._renderFunction);\r\n            }\r\n        }\r\n\r\n        if (color && this._renderLine) {\r\n            this._renderLine.color.copyFrom(color);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Hides the ray we are debugging.\r\n     */\r\n    public hide(): void {\r\n        if (this._renderFunction && this._scene) {\r\n            this._scene.unregisterBeforeRender(this._renderFunction);\r\n            this._scene = null;\r\n            this._renderFunction = null;\r\n            if (this._renderLine) {\r\n                this._renderLine.dispose();\r\n                this._renderLine = null;\r\n            }\r\n\r\n            this._renderPoints = [];\r\n        }\r\n    }\r\n\r\n    private _render(): void {\r\n        const ray = this.ray;\r\n\r\n        if (!ray) {\r\n            return;\r\n        }\r\n\r\n        const point = this._renderPoints[1];\r\n        const len = Math.min(ray.length, 1000000);\r\n\r\n        point.copyFrom(ray.direction);\r\n        point.scaleInPlace(len);\r\n        point.addInPlace(ray.origin);\r\n\r\n        this._renderPoints[0].copyFrom(ray.origin);\r\n\r\n        CreateLines(\"ray\", { points: this._renderPoints, updatable: true, instance: this._renderLine }, this._scene);\r\n\r\n        this._renderLine?.refreshBoundingInfo();\r\n    }\r\n\r\n    /**\r\n     * Attach a ray helper to a mesh so that we can easily see its orientation for instance or information like its normals.\r\n     * @param mesh Defines the mesh we want the helper attached to\r\n     * @param meshSpaceDirection Defines the direction of the Ray in mesh space (local space of the mesh node)\r\n     * @param meshSpaceOrigin Defines the origin of the Ray in mesh space (local space of the mesh node)\r\n     * @param length Defines the length of the ray\r\n     */\r\n    public attachToMesh(mesh: AbstractMesh, meshSpaceDirection?: Vector3, meshSpaceOrigin?: Vector3, length?: number): void {\r\n        this._attachedToMesh = mesh;\r\n\r\n        const ray = this.ray;\r\n\r\n        if (!ray) {\r\n            return;\r\n        }\r\n\r\n        if (!ray.direction) {\r\n            ray.direction = Vector3.Zero();\r\n        }\r\n\r\n        if (!ray.origin) {\r\n            ray.origin = Vector3.Zero();\r\n        }\r\n\r\n        if (length) {\r\n            ray.length = length;\r\n        }\r\n\r\n        if (!meshSpaceOrigin) {\r\n            meshSpaceOrigin = Vector3.Zero();\r\n        }\r\n\r\n        if (!meshSpaceDirection) {\r\n            // -1 so that this will work with Mesh.lookAt\r\n            meshSpaceDirection = new Vector3(0, 0, -1);\r\n        }\r\n\r\n        if (!this._scene) {\r\n            this._scene = mesh.getScene();\r\n        }\r\n\r\n        if (!this._meshSpaceDirection) {\r\n            this._meshSpaceDirection = meshSpaceDirection.clone();\r\n            this._meshSpaceOrigin = meshSpaceOrigin.clone();\r\n        } else {\r\n            this._meshSpaceDirection.copyFrom(meshSpaceDirection);\r\n            this._meshSpaceOrigin.copyFrom(meshSpaceOrigin);\r\n        }\r\n\r\n        if (!this._onAfterRenderObserver) {\r\n            this._onAfterRenderObserver = this._scene.onBeforeRenderObservable.add(() => this._updateToMesh());\r\n            this._onAfterStepObserver = this._scene.onAfterStepObservable.add(() => this._updateToMesh());\r\n        }\r\n\r\n        // force world matrix computation before the first ray helper computation\r\n        this._attachedToMesh.computeWorldMatrix(true);\r\n\r\n        this._updateToMesh();\r\n    }\r\n\r\n    /**\r\n     * Detach the ray helper from the mesh it has previously been attached to.\r\n     */\r\n    public detachFromMesh(): void {\r\n        if (this._attachedToMesh && this._scene) {\r\n            if (this._onAfterRenderObserver) {\r\n                this._scene.onBeforeRenderObservable.remove(this._onAfterRenderObserver);\r\n                this._scene.onAfterStepObservable.remove(this._onAfterStepObserver);\r\n            }\r\n            this._attachedToMesh = null;\r\n            this._onAfterRenderObserver = null;\r\n            this._onAfterStepObserver = null;\r\n            this._scene = null;\r\n        }\r\n    }\r\n\r\n    private _updateToMesh(): void {\r\n        const ray = this.ray;\r\n\r\n        if (!this._attachedToMesh || !ray) {\r\n            return;\r\n        }\r\n\r\n        if (this._attachedToMesh.isDisposed()) {\r\n            this.detachFromMesh();\r\n            return;\r\n        }\r\n\r\n        this._attachedToMesh.getDirectionToRef(this._meshSpaceDirection, ray.direction);\r\n        Vector3.TransformCoordinatesToRef(this._meshSpaceOrigin, this._attachedToMesh.getWorldMatrix(), ray.origin);\r\n    }\r\n\r\n    /**\r\n     * Dispose the helper and release its associated resources.\r\n     */\r\n    public dispose(): void {\r\n        this.hide();\r\n        this.detachFromMesh();\r\n        this.ray = null;\r\n    }\r\n}\r\n"]}