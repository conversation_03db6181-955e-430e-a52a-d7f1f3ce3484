{"version": 3, "file": "ribbonBuilder.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Builders/ribbonBuilder.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAErD,OAAO,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,SAAS,CAAC;AACrD,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAEzE;;;;;;;;;;;;;;GAcG;AACH,MAAM,UAAU,sBAAsB,CAAC,OAWtC;IACG,IAAI,SAAS,GAAgB,OAAO,CAAC,SAAS,CAAC;IAC/C,MAAM,UAAU,GAAY,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC;IACxD,MAAM,SAAS,GAAY,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC;IACtD,MAAM,QAAQ,GAAY,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC;IACpD,MAAM,aAAa,GAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAClE,IAAI,MAAM,GAAW,OAAO,CAAC,MAAM,IAAI,aAAa,CAAC;IACrD,MAAM,GAAG,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,qCAAqC;IAC3G,MAAM,eAAe,GAAW,OAAO,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,IAAI,UAAU,CAAC,WAAW,CAAC;IACtH,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC;IAC7B,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;IAEpC,MAAM,SAAS,GAAa,EAAE,CAAC;IAC/B,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,MAAM,GAAG,GAAa,EAAE,CAAC;IAEzB,MAAM,EAAE,GAAe,EAAE,CAAC,CAAC,uFAAuF;IAClH,MAAM,EAAE,GAAe,EAAE,CAAC,CAAC,wGAAwG;IACnI,MAAM,cAAc,GAAa,EAAE,CAAC,CAAC,+CAA+C;IACpF,MAAM,cAAc,GAAa,EAAE,CAAC,CAAC,6FAA6F;IAClI,IAAI,KAAa,CAAC,CAAC,gDAAgD;IACnE,MAAM,EAAE,GAAa,EAAE,CAAC,CAAC,gDAAgD;IACzE,MAAM,GAAG,GAAa,EAAE,CAAC,CAAC,uFAAuF;IACjH,IAAI,CAAS,CAAC,CAAC,gBAAgB;IAC/B,IAAI,CAAS,CAAC,CAAC,iBAAiB;IAChC,IAAI,CAAS,CAAC,CAAC,iBAAiB;IAEhC,8BAA8B;IAC9B,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;QACtB,MAAM,GAAG,GAAc,EAAE,CAAC;QAC1B,MAAM,GAAG,GAAc,EAAE,CAAC;QAC1B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/C,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;SACtC;QACD,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;KAC1B;IAED,yCAAyC;IACzC,IAAI,GAAG,GAAW,CAAC,CAAC;IACpB,MAAM,aAAa,GAAW,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,0CAA0C;IAC3F,IAAI,IAAe,CAAC;IACpB,IAAI,CAAS,CAAC;IACd,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAC5B,IAAI,MAAc,CAAC;IACnB,IAAI,IAAY,CAAC;IACjB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACtB,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACZ,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QAChB,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9B,CAAC,GAAG,CAAC,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,EAAE;YACV,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,GAAG,CAAC,EAAE;gBACP,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;gBAChD,IAAI,GAAG,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBAClC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjB,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;aAC5B;YACD,CAAC,EAAE,CAAC;SACP;QAED,IAAI,SAAS,EAAE;YACX,2DAA2D;YAC3D,CAAC,EAAE,CAAC;YACJ,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YAC5C,IAAI,GAAG,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAClC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjB,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;SAC5B;QAED,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC;QAC1B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QACb,GAAG,IAAI,CAAC,GAAG,aAAa,CAAC;KAC5B;IAED,yBAAyB;IACzB,IAAI,KAAgB,CAAC;IACrB,IAAI,KAAgB,CAAC;IACrB,IAAI,OAAO,GAAsB,IAAI,CAAC;IACtC,IAAI,OAAO,GAAsB,IAAI,CAAC;IACtC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,aAAa,EAAE,CAAC,EAAE,EAAE;QACxC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACtB,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACZ,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACvC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACrB,KAAK,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACzB,IAAI,CAAC,KAAK,KAAK,EAAE;gBACb,YAAY;gBACZ,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACnB,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;aACtB;iBAAM;gBACH,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACnB,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;aACtB;YACD,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;YAC5C,IAAI,GAAG,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAClC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjB,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;SAC5B;QAED,IAAI,UAAU,IAAI,OAAO,IAAI,OAAO,EAAE;YAClC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACrB,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACrB,IAAI,CAAC,KAAK,KAAK,EAAE;gBACb,YAAY;gBACZ,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;aACtB;YACD,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;YAC5C,IAAI,GAAG,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAClC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;SAC5B;KACJ;IAED,MAAM;IACN,IAAI,CAAS,CAAC;IACd,IAAI,CAAS,CAAC;IACd,IAAI,QAAQ,EAAE;QACV,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACjH;KACJ;SAAM;QACH,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,GAAG,aAAa,EAAE,CAAC,EAAE,EAAE;gBACxC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;gBAClE,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;gBAClE,IAAI,QAAQ,EAAE;oBACV,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBAClB;qBAAM;oBACH,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC7E;aACJ;SACJ;KACJ;IAED,UAAU;IACV,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa;IACpB,IAAI,EAAE,GAAW,CAAC,CAAC,CAAC,wBAAwB;IAC5C,IAAI,EAAE,GAAW,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe;IAC3C,IAAI,EAAE,GAAW,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe;IAC/C,IAAI,GAAG,GAAW,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,0BAA0B;IAC/D,IAAI,IAAI,GAAW,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;IAC5C,MAAM,OAAO,GAAW,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,gCAAgC;IAEhG,OAAO,EAAE,IAAI,GAAG,IAAI,CAAC,GAAG,OAAO,EAAE;QAC7B,sDAAsD;QACtD,yHAAyH;QAEzH,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,GAAG,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;QACpC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC;QAC/C,EAAE,IAAI,CAAC,CAAC;QACR,IAAI,EAAE,KAAK,GAAG,EAAE;YACZ,2EAA2E;YAC3E,CAAC,EAAE,CAAC;YACJ,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,wDAAwD;gBACxD,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBACvB,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBACf,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aAClB;iBAAM;gBACH,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC3B,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBACf,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;aACtB;YACD,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACZ,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;SACrC;KACJ;IAED,UAAU;IACV,UAAU,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAEvD,IAAI,SAAS,EAAE;QACX,uEAAuE;QACvE,IAAI,UAAU,GAAW,CAAC,CAAC;QAC3B,IAAI,SAAS,GAAW,CAAC,CAAC;QAC1B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACxB,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE;gBAC1B,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;aACpC;iBAAM;gBACH,SAAS,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;aAClC;YACD,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC;YACvE,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YACnF,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YACnF,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;YACzC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;YACjD,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;SACpD;KACJ;IAED,QAAQ;IACR,UAAU,CAAC,aAAa,CAAC,eAAe,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IAE/G,SAAS;IACT,IAAI,MAAM,GAA2B,IAAI,CAAC;IAC1C,IAAI,YAAY,EAAE;QACd,MAAM,GAAG,IAAI,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACzC;KACJ;IAED,SAAS;IACT,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IACpC,MAAM,WAAW,GAAG,IAAI,YAAY,CAAC,SAAS,CAAC,CAAC;IAChD,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;IAC5C,MAAM,KAAK,GAAG,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;IAEpC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,SAAS,GAAG,WAAW,CAAC;IACnC,UAAU,CAAC,OAAO,GAAG,SAAS,CAAC;IAC/B,UAAU,CAAC,GAAG,GAAG,KAAK,CAAC;IACvB,IAAI,MAAM,EAAE;QACR,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;KAClD;IAED,IAAI,SAAS,EAAE;QACL,UAAW,CAAC,IAAI,GAAG,GAAG,CAAC;KAChC;IAED,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAM,UAAU,YAAY,CACxB,IAAY,EACZ,OAaC,EACD,QAAyB,IAAI;IAE7B,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;IACpC,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IACtC,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;IACpC,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACjF,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IAClC,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;IAEpC,IAAI,QAAQ,EAAE;QACV,kCAAkC;QAClC,iCAAiC;QACjC,4FAA4F;QAC5F,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC/D,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAChE,MAAM,gBAAgB,GAAG,CAAC,SAAqB,EAAE,EAAE;YAC/C,IAAI,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAChC,MAAM,IAAI,GAAS,QAAQ,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,CAAC;YACV,MAAM,EAAE,GAAG,IAAI,CAAC,+BAA+B,KAAK,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5E,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;gBAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;oBACvC,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC1B,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;oBACtB,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;wBAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;wBAC1B,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;wBAC3B,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;wBAC/B,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;wBAC/B,OAAO,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;wBACzE,OAAO,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;wBACzE,CAAC,IAAI,CAAC,CAAC;qBACV;oBACD,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE;wBAClE,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;wBAC1B,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;wBAC3B,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;wBAC/B,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;wBAC/B,CAAC,IAAI,CAAC,CAAC;qBACV;iBACJ;aACJ;QACL,CAAC,CAAC;QACF,MAAM,SAAS,GAAe,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAClF,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC5B,IAAI,QAAQ,CAAC,eAAe,EAAE;YAC1B,QAAQ,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;SACnF;aAAM;YACH,QAAQ,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;SACvE;QACD,QAAQ,CAAC,kBAAkB,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAChF,IAAI,OAAO,CAAC,MAAM,EAAE;YAChB,MAAM,MAAM,GAAe,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC5E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,UAAU,IAAI,CAAC,EAAE;gBAC7E,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAChC,MAAM,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBAC7B,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBACjC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBACjC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;aACpC;YACD,QAAQ,CAAC,kBAAkB,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SAC7E;QACD,IAAI,OAAO,CAAC,GAAG,EAAE;YACb,MAAM,GAAG,GAAe,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YACtE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9B,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC/G;YACD,QAAQ,CAAC,kBAAkB,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SACvE;QACD,IAAI,CAAC,QAAQ,CAAC,gBAAgB,IAAI,QAAQ,CAAC,kBAAkB,EAAE;YAC3D,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC;YACtC,MAAM,OAAO,GAAe,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAC9E,MAAM,MAAM,GAAG,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,QAAQ,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YACtF,UAAU,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAE/D,IAAI,QAAQ,CAAC,oBAAoB,IAAI,QAAQ,CAAC,oBAAoB,CAAC,SAAS,EAAE;gBAC1E,IAAI,UAAU,GAAW,CAAC,CAAC;gBAC3B,IAAI,SAAS,GAAW,CAAC,CAAC;gBAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACvC,UAAU,GAAG,QAAQ,CAAC,oBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBACvD,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE;wBAC1B,SAAS,GAAG,CAAC,QAAQ,CAAC,oBAAqB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;qBACnE;yBAAM;wBACH,SAAS,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;qBAClC;oBACD,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC;oBACvE,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;oBACnF,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;oBACnF,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;oBACzC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;oBACjD,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;iBACpD;aACJ;YACD,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE;gBAC5B,QAAQ,CAAC,kBAAkB,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aAC/E;SACJ;QAED,OAAO,QAAQ,CAAC;KACnB;SAAM;QACH,sBAAsB;QAEtB,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACrC,MAAM,CAAC,+BAA+B,GAAG,eAAe,CAAC;QACzD,MAAM,CAAC,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAEzD,MAAM,UAAU,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,SAAS,EAAE;YACX,MAAM,CAAC,oBAAoB,CAAC,GAAG,GAAS,UAAW,CAAC,IAAI,CAAC;SAC5D;QACD,MAAM,CAAC,oBAAoB,CAAC,SAAS,GAAG,SAAS,CAAC;QAClD,MAAM,CAAC,oBAAoB,CAAC,UAAU,GAAG,UAAU,CAAC;QAEpD,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAE1C,OAAO,MAAM,CAAC;KACjB;AACL,CAAC;AACD;;;GAGG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG;IACzB,gEAAgE;IAChE,YAAY;CACf,CAAC;AAEF,UAAU,CAAC,YAAY,GAAG,sBAAsB,CAAC;AAEjD,IAAI,CAAC,YAAY,GAAG,CAChB,IAAY,EACZ,SAAsB,EACtB,aAAsB,KAAK,EAC3B,SAAkB,EAClB,MAAc,EACd,KAAa,EACb,YAAqB,KAAK,EAC1B,eAAwB,EACxB,QAAe,EACjB,EAAE;IACA,OAAO,YAAY,CACf,IAAI,EACJ;QACI,SAAS,EAAE,SAAS;QACpB,UAAU,EAAE,UAAU;QACtB,SAAS,EAAE,SAAS;QACpB,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,SAAS;QACpB,eAAe,EAAE,eAAe;QAChC,QAAQ,EAAE,QAAQ;KACrB,EACD,KAAK,CACR,CAAC;AACN,CAAC,CAAC", "sourcesContent": ["import type { Nullable, FloatArray } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Vector3, Vector2, Vector4 } from \"../../Maths/math.vector\";\r\nimport { TmpVectors } from \"../../Maths/math.vector\";\r\nimport type { Color4 } from \"../../Maths/math.color\";\r\nimport { Mesh, _CreationDataStorage } from \"../mesh\";\r\nimport { VertexBuffer } from \"../../Buffers/buffer\";\r\nimport { VertexData } from \"../mesh.vertexData\";\r\nimport { CompatibilityOptions } from \"../../Compat/compatibilityOptions\";\r\n\r\n/**\r\n * Creates the VertexData for a Ribbon\r\n * @param options an object used to set the following optional parameters for the ribbon, required but can be empty\r\n * * pathArray array of paths, each of which an array of successive Vector3\r\n * * closeArray creates a seam between the first and the last paths of the pathArray, optional, default false\r\n * * closePath creates a seam between the first and the last points of each path of the path array, optional, default false\r\n * * offset a positive integer, only used when pathArray contains a single path (offset = 10 means the point 1 is joined to the point 11), default rounded half size of the pathArray length\r\n * * sideOrientation optional and takes the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n * * frontUvs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the front side, optional, default vector4 (0, 0, 1, 1)\r\n * * backUVs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the back side, optional, default vector4 (0, 0, 1, 1)\r\n * * invertUV swaps in the U and V coordinates when applying a texture, optional, default false\r\n * * uvs a linear array, of length 2 * number of vertices, of custom UV values, optional\r\n * * colors a linear array, of length 4 * number of vertices, of custom color values, optional\r\n * @returns the VertexData of the ribbon\r\n */\r\nexport function CreateRibbonVertexData(options: {\r\n    pathArray: Vector3[][];\r\n    closeArray?: boolean;\r\n    closePath?: boolean;\r\n    offset?: number;\r\n    sideOrientation?: number;\r\n    frontUVs?: Vector4;\r\n    backUVs?: Vector4;\r\n    invertUV?: boolean;\r\n    uvs?: Vector2[];\r\n    colors?: Color4[];\r\n}): VertexData {\r\n    let pathArray: Vector3[][] = options.pathArray;\r\n    const closeArray: boolean = options.closeArray || false;\r\n    const closePath: boolean = options.closePath || false;\r\n    const invertUV: boolean = options.invertUV || false;\r\n    const defaultOffset: number = Math.floor(pathArray[0].length / 2);\r\n    let offset: number = options.offset || defaultOffset;\r\n    offset = offset > defaultOffset ? defaultOffset : Math.floor(offset); // offset max allowed : defaultOffset\r\n    const sideOrientation: number = options.sideOrientation === 0 ? 0 : options.sideOrientation || VertexData.DEFAULTSIDE;\r\n    const customUV = options.uvs;\r\n    const customColors = options.colors;\r\n\r\n    const positions: number[] = [];\r\n    const indices: number[] = [];\r\n    const normals: number[] = [];\r\n    const uvs: number[] = [];\r\n\r\n    const us: number[][] = []; // us[path_id] = [uDist1, uDist2, uDist3 ... ] distances between points on path path_id\r\n    const vs: number[][] = []; // vs[i] = [vDist1, vDist2, vDist3, ... ] distances between points i of consecutive paths from pathArray\r\n    const uTotalDistance: number[] = []; // uTotalDistance[p] : total distance of path p\r\n    const vTotalDistance: number[] = []; //  vTotalDistance[i] : total distance between points i of first and last path from pathArray\r\n    let minlg: number; // minimal length among all paths from pathArray\r\n    const lg: number[] = []; // array of path lengths : nb of vertex per path\r\n    const idx: number[] = []; // array of path indexes : index of each path (first vertex) in the total vertex number\r\n    let p: number; // path iterator\r\n    let i: number; // point iterator\r\n    let j: number; // point iterator\r\n\r\n    // if single path in pathArray\r\n    if (pathArray.length < 2) {\r\n        const ar1: Vector3[] = [];\r\n        const ar2: Vector3[] = [];\r\n        for (i = 0; i < pathArray[0].length - offset; i++) {\r\n            ar1.push(pathArray[0][i]);\r\n            ar2.push(pathArray[0][i + offset]);\r\n        }\r\n        pathArray = [ar1, ar2];\r\n    }\r\n\r\n    // positions and horizontal distances (u)\r\n    let idc: number = 0;\r\n    const closePathCorr: number = closePath ? 1 : 0; // the final index will be +1 if closePath\r\n    let path: Vector3[];\r\n    let l: number;\r\n    minlg = pathArray[0].length;\r\n    let vectlg: number;\r\n    let dist: number;\r\n    for (p = 0; p < pathArray.length; p++) {\r\n        uTotalDistance[p] = 0;\r\n        us[p] = [0];\r\n        path = pathArray[p];\r\n        l = path.length;\r\n        minlg = minlg < l ? minlg : l;\r\n\r\n        j = 0;\r\n        while (j < l) {\r\n            positions.push(path[j].x, path[j].y, path[j].z);\r\n            if (j > 0) {\r\n                vectlg = path[j].subtract(path[j - 1]).length();\r\n                dist = vectlg + uTotalDistance[p];\r\n                us[p].push(dist);\r\n                uTotalDistance[p] = dist;\r\n            }\r\n            j++;\r\n        }\r\n\r\n        if (closePath) {\r\n            // an extra hidden vertex is added in the \"positions\" array\r\n            j--;\r\n            positions.push(path[0].x, path[0].y, path[0].z);\r\n            vectlg = path[j].subtract(path[0]).length();\r\n            dist = vectlg + uTotalDistance[p];\r\n            us[p].push(dist);\r\n            uTotalDistance[p] = dist;\r\n        }\r\n\r\n        lg[p] = l + closePathCorr;\r\n        idx[p] = idc;\r\n        idc += l + closePathCorr;\r\n    }\r\n\r\n    // vertical distances (v)\r\n    let path1: Vector3[];\r\n    let path2: Vector3[];\r\n    let vertex1: Nullable<Vector3> = null;\r\n    let vertex2: Nullable<Vector3> = null;\r\n    for (i = 0; i < minlg + closePathCorr; i++) {\r\n        vTotalDistance[i] = 0;\r\n        vs[i] = [0];\r\n        for (p = 0; p < pathArray.length - 1; p++) {\r\n            path1 = pathArray[p];\r\n            path2 = pathArray[p + 1];\r\n            if (i === minlg) {\r\n                // closePath\r\n                vertex1 = path1[0];\r\n                vertex2 = path2[0];\r\n            } else {\r\n                vertex1 = path1[i];\r\n                vertex2 = path2[i];\r\n            }\r\n            vectlg = vertex2.subtract(vertex1).length();\r\n            dist = vectlg + vTotalDistance[i];\r\n            vs[i].push(dist);\r\n            vTotalDistance[i] = dist;\r\n        }\r\n\r\n        if (closeArray && vertex2 && vertex1) {\r\n            path1 = pathArray[p];\r\n            path2 = pathArray[0];\r\n            if (i === minlg) {\r\n                // closePath\r\n                vertex2 = path2[0];\r\n            }\r\n            vectlg = vertex2.subtract(vertex1).length();\r\n            dist = vectlg + vTotalDistance[i];\r\n            vTotalDistance[i] = dist;\r\n        }\r\n    }\r\n\r\n    // uvs\r\n    let u: number;\r\n    let v: number;\r\n    if (customUV) {\r\n        for (p = 0; p < customUV.length; p++) {\r\n            uvs.push(customUV[p].x, CompatibilityOptions.UseOpenGLOrientationForUV ? 1.0 - customUV[p].y : customUV[p].y);\r\n        }\r\n    } else {\r\n        for (p = 0; p < pathArray.length; p++) {\r\n            for (i = 0; i < minlg + closePathCorr; i++) {\r\n                u = uTotalDistance[p] != 0.0 ? us[p][i] / uTotalDistance[p] : 0.0;\r\n                v = vTotalDistance[i] != 0.0 ? vs[i][p] / vTotalDistance[i] : 0.0;\r\n                if (invertUV) {\r\n                    uvs.push(v, u);\r\n                } else {\r\n                    uvs.push(u, CompatibilityOptions.UseOpenGLOrientationForUV ? 1.0 - v : v);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // indices\r\n    p = 0; // path index\r\n    let pi: number = 0; // positions array index\r\n    let l1: number = lg[p] - 1; // path1 length\r\n    let l2: number = lg[p + 1] - 1; // path2 length\r\n    let min: number = l1 < l2 ? l1 : l2; // current path stop index\r\n    let shft: number = idx[1] - idx[0]; // shift\r\n    const path1nb: number = closeArray ? lg.length : lg.length - 1; // number of path1 to iterate\ton\r\n\r\n    while (pi <= min && p < path1nb) {\r\n        //  stay under min and don't go over next to last path\r\n        // draw two triangles between path1 (p1) and path2 (p2) : (p1.pi, p2.pi, p1.pi+1) and (p2.pi+1, p1.pi+1, p2.pi) clockwise\r\n\r\n        indices.push(pi, pi + shft, pi + 1);\r\n        indices.push(pi + shft + 1, pi + 1, pi + shft);\r\n        pi += 1;\r\n        if (pi === min) {\r\n            // if end of one of two consecutive paths reached, go to next existing path\r\n            p++;\r\n            if (p === lg.length - 1) {\r\n                // last path of pathArray reached <=> closeArray == true\r\n                shft = idx[0] - idx[p];\r\n                l1 = lg[p] - 1;\r\n                l2 = lg[0] - 1;\r\n            } else {\r\n                shft = idx[p + 1] - idx[p];\r\n                l1 = lg[p] - 1;\r\n                l2 = lg[p + 1] - 1;\r\n            }\r\n            pi = idx[p];\r\n            min = l1 < l2 ? l1 + pi : l2 + pi;\r\n        }\r\n    }\r\n\r\n    // normals\r\n    VertexData.ComputeNormals(positions, indices, normals);\r\n\r\n    if (closePath) {\r\n        // update both the first and last vertex normals to their average value\r\n        let indexFirst: number = 0;\r\n        let indexLast: number = 0;\r\n        for (p = 0; p < pathArray.length; p++) {\r\n            indexFirst = idx[p] * 3;\r\n            if (p + 1 < pathArray.length) {\r\n                indexLast = (idx[p + 1] - 1) * 3;\r\n            } else {\r\n                indexLast = normals.length - 3;\r\n            }\r\n            normals[indexFirst] = (normals[indexFirst] + normals[indexLast]) * 0.5;\r\n            normals[indexFirst + 1] = (normals[indexFirst + 1] + normals[indexLast + 1]) * 0.5;\r\n            normals[indexFirst + 2] = (normals[indexFirst + 2] + normals[indexLast + 2]) * 0.5;\r\n            normals[indexLast] = normals[indexFirst];\r\n            normals[indexLast + 1] = normals[indexFirst + 1];\r\n            normals[indexLast + 2] = normals[indexFirst + 2];\r\n        }\r\n    }\r\n\r\n    // sides\r\n    VertexData._ComputeSides(sideOrientation, positions, indices, normals, uvs, options.frontUVs, options.backUVs);\r\n\r\n    // Colors\r\n    let colors: Nullable<Float32Array> = null;\r\n    if (customColors) {\r\n        colors = new Float32Array(customColors.length * 4);\r\n        for (let c = 0; c < customColors.length; c++) {\r\n            colors[c * 4] = customColors[c].r;\r\n            colors[c * 4 + 1] = customColors[c].g;\r\n            colors[c * 4 + 2] = customColors[c].b;\r\n            colors[c * 4 + 3] = customColors[c].a;\r\n        }\r\n    }\r\n\r\n    // Result\r\n    const vertexData = new VertexData();\r\n    const positions32 = new Float32Array(positions);\r\n    const normals32 = new Float32Array(normals);\r\n    const uvs32 = new Float32Array(uvs);\r\n\r\n    vertexData.indices = indices;\r\n    vertexData.positions = positions32;\r\n    vertexData.normals = normals32;\r\n    vertexData.uvs = uvs32;\r\n    if (colors) {\r\n        vertexData.set(colors, VertexBuffer.ColorKind);\r\n    }\r\n\r\n    if (closePath) {\r\n        (<any>vertexData)._idx = idx;\r\n    }\r\n\r\n    return vertexData;\r\n}\r\n\r\n/**\r\n * Creates a ribbon mesh. The ribbon is a parametric shape.  It has no predefined shape. Its final shape will depend on the input parameters\r\n * * The parameter `pathArray` is a required array of paths, what are each an array of successive Vector3. The pathArray parameter depicts the ribbon geometry\r\n * * The parameter `closeArray` (boolean, default false) creates a seam between the first and the last paths of the path array\r\n * * The parameter `closePath` (boolean, default false) creates a seam between the first and the last points of each path of the path array\r\n * * The parameter `offset` (positive integer, default : rounded half size of the pathArray length), is taken in account only if the `pathArray` is containing a single path\r\n * * It's the offset to join the points from the same path. Ex : offset = 10 means the point 1 is joined to the point 11\r\n * * The optional parameter `instance` is an instance of an existing Ribbon object to be updated with the passed `pathArray` parameter : https://doc.babylonjs.com/features/featuresDeepDive/mesh/dynamicMeshMorph#ribbon\r\n * * You can also set the mesh side orientation with the values : BABYLON.Mesh.FRONTSIDE (default), BABYLON.Mesh.BACKSIDE or BABYLON.Mesh.DOUBLESIDE\r\n * * If you create a double-sided mesh, you can choose what parts of the texture image to crop and stick respectively on the front and the back sides with the parameters `frontUVs` and `backUVs` (Vector4). Detail here : https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation\r\n * * The optional parameter `invertUV` (boolean, default false) swaps in the geometry the U and V coordinates to apply a texture\r\n * * The parameter `uvs` is an optional flat array of `Vector2` to update/set each ribbon vertex with its own custom UV values instead of the computed ones\r\n * * The parameters `colors` is an optional flat array of `Color4` to set/update each ribbon vertex with its own custom color values\r\n * * Note that if you use the parameters `uvs` or `colors`, the passed arrays must be populated with the right number of elements, it is to say the number of ribbon vertices. Remember that if you set `closePath` to `true`, there's one extra vertex per path in the geometry\r\n * * Moreover, you can use the parameter `color` with `instance` (to update the ribbon), only if you previously used it at creation time\r\n * * The mesh can be set to updatable with the boolean parameter `updatable` (default false) if its internal geometry is supposed to change once created\r\n * @param name defines the name of the mesh\r\n * @param options defines the options used to create the mesh\r\n * @param scene defines the hosting scene\r\n * @returns the ribbon mesh\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param/ribbon_extra\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param\r\n */\r\nexport function CreateRibbon(\r\n    name: string,\r\n    options: {\r\n        pathArray: Vector3[][];\r\n        closeArray?: boolean;\r\n        closePath?: boolean;\r\n        offset?: number;\r\n        updatable?: boolean;\r\n        sideOrientation?: number;\r\n        frontUVs?: Vector4;\r\n        backUVs?: Vector4;\r\n        instance?: Mesh;\r\n        invertUV?: boolean;\r\n        uvs?: Vector2[];\r\n        colors?: Color4[];\r\n    },\r\n    scene: Nullable<Scene> = null\r\n): Mesh {\r\n    const pathArray = options.pathArray;\r\n    const closeArray = options.closeArray;\r\n    const closePath = options.closePath;\r\n    const sideOrientation = Mesh._GetDefaultSideOrientation(options.sideOrientation);\r\n    const instance = options.instance;\r\n    const updatable = options.updatable;\r\n\r\n    if (instance) {\r\n        // existing ribbon instance update\r\n        // positionFunction : ribbon case\r\n        // only pathArray and sideOrientation parameters are taken into account for positions update\r\n        const minimum = TmpVectors.Vector3[0].setAll(Number.MAX_VALUE);\r\n        const maximum = TmpVectors.Vector3[1].setAll(-Number.MAX_VALUE);\r\n        const positionFunction = (positions: FloatArray) => {\r\n            let minlg = pathArray[0].length;\r\n            const mesh = <Mesh>instance;\r\n            let i = 0;\r\n            const ns = mesh._originalBuilderSideOrientation === Mesh.DOUBLESIDE ? 2 : 1;\r\n            for (let si = 1; si <= ns; ++si) {\r\n                for (let p = 0; p < pathArray.length; ++p) {\r\n                    const path = pathArray[p];\r\n                    const l = path.length;\r\n                    minlg = minlg < l ? minlg : l;\r\n                    for (let j = 0; j < minlg; ++j) {\r\n                        const pathPoint = path[j];\r\n                        positions[i] = pathPoint.x;\r\n                        positions[i + 1] = pathPoint.y;\r\n                        positions[i + 2] = pathPoint.z;\r\n                        minimum.minimizeInPlaceFromFloats(pathPoint.x, pathPoint.y, pathPoint.z);\r\n                        maximum.maximizeInPlaceFromFloats(pathPoint.x, pathPoint.y, pathPoint.z);\r\n                        i += 3;\r\n                    }\r\n                    if (mesh._creationDataStorage && mesh._creationDataStorage.closePath) {\r\n                        const pathPoint = path[0];\r\n                        positions[i] = pathPoint.x;\r\n                        positions[i + 1] = pathPoint.y;\r\n                        positions[i + 2] = pathPoint.z;\r\n                        i += 3;\r\n                    }\r\n                }\r\n            }\r\n        };\r\n        const positions = <FloatArray>instance.getVerticesData(VertexBuffer.PositionKind);\r\n        positionFunction(positions);\r\n        if (instance.hasBoundingInfo) {\r\n            instance.getBoundingInfo().reConstruct(minimum, maximum, instance._worldMatrix);\r\n        } else {\r\n            instance.buildBoundingInfo(minimum, maximum, instance._worldMatrix);\r\n        }\r\n        instance.updateVerticesData(VertexBuffer.PositionKind, positions, false, false);\r\n        if (options.colors) {\r\n            const colors = <FloatArray>instance.getVerticesData(VertexBuffer.ColorKind);\r\n            for (let c = 0, colorIndex = 0; c < options.colors.length; c++, colorIndex += 4) {\r\n                const color = options.colors[c];\r\n                colors[colorIndex] = color.r;\r\n                colors[colorIndex + 1] = color.g;\r\n                colors[colorIndex + 2] = color.b;\r\n                colors[colorIndex + 3] = color.a;\r\n            }\r\n            instance.updateVerticesData(VertexBuffer.ColorKind, colors, false, false);\r\n        }\r\n        if (options.uvs) {\r\n            const uvs = <FloatArray>instance.getVerticesData(VertexBuffer.UVKind);\r\n            for (let i = 0; i < options.uvs.length; i++) {\r\n                uvs[i * 2] = options.uvs[i].x;\r\n                uvs[i * 2 + 1] = CompatibilityOptions.UseOpenGLOrientationForUV ? 1.0 - options.uvs[i].y : options.uvs[i].y;\r\n            }\r\n            instance.updateVerticesData(VertexBuffer.UVKind, uvs, false, false);\r\n        }\r\n        if (!instance.areNormalsFrozen || instance.isFacetDataEnabled) {\r\n            const indices = instance.getIndices();\r\n            const normals = <FloatArray>instance.getVerticesData(VertexBuffer.NormalKind);\r\n            const params = instance.isFacetDataEnabled ? instance.getFacetDataParameters() : null;\r\n            VertexData.ComputeNormals(positions, indices, normals, params);\r\n\r\n            if (instance._creationDataStorage && instance._creationDataStorage.closePath) {\r\n                let indexFirst: number = 0;\r\n                let indexLast: number = 0;\r\n                for (let p = 0; p < pathArray.length; p++) {\r\n                    indexFirst = instance._creationDataStorage!.idx[p] * 3;\r\n                    if (p + 1 < pathArray.length) {\r\n                        indexLast = (instance._creationDataStorage!.idx[p + 1] - 1) * 3;\r\n                    } else {\r\n                        indexLast = normals.length - 3;\r\n                    }\r\n                    normals[indexFirst] = (normals[indexFirst] + normals[indexLast]) * 0.5;\r\n                    normals[indexFirst + 1] = (normals[indexFirst + 1] + normals[indexLast + 1]) * 0.5;\r\n                    normals[indexFirst + 2] = (normals[indexFirst + 2] + normals[indexLast + 2]) * 0.5;\r\n                    normals[indexLast] = normals[indexFirst];\r\n                    normals[indexLast + 1] = normals[indexFirst + 1];\r\n                    normals[indexLast + 2] = normals[indexFirst + 2];\r\n                }\r\n            }\r\n            if (!instance.areNormalsFrozen) {\r\n                instance.updateVerticesData(VertexBuffer.NormalKind, normals, false, false);\r\n            }\r\n        }\r\n\r\n        return instance;\r\n    } else {\r\n        // new ribbon creation\r\n\r\n        const ribbon = new Mesh(name, scene);\r\n        ribbon._originalBuilderSideOrientation = sideOrientation;\r\n        ribbon._creationDataStorage = new _CreationDataStorage();\r\n\r\n        const vertexData = CreateRibbonVertexData(options);\r\n        if (closePath) {\r\n            ribbon._creationDataStorage.idx = (<any>vertexData)._idx;\r\n        }\r\n        ribbon._creationDataStorage.closePath = closePath;\r\n        ribbon._creationDataStorage.closeArray = closeArray;\r\n\r\n        vertexData.applyToMesh(ribbon, updatable);\r\n\r\n        return ribbon;\r\n    }\r\n}\r\n/**\r\n * Class containing static functions to help procedurally build meshes\r\n * @deprecated use CreateRibbon directly\r\n */\r\nexport const RibbonBuilder = {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    CreateRibbon,\r\n};\r\n\r\nVertexData.CreateRibbon = CreateRibbonVertexData;\r\n\r\nMesh.CreateRibbon = (\r\n    name: string,\r\n    pathArray: Vector3[][],\r\n    closeArray: boolean = false,\r\n    closePath: boolean,\r\n    offset: number,\r\n    scene?: Scene,\r\n    updatable: boolean = false,\r\n    sideOrientation?: number,\r\n    instance?: Mesh\r\n) => {\r\n    return CreateRibbon(\r\n        name,\r\n        {\r\n            pathArray: pathArray,\r\n            closeArray: closeArray,\r\n            closePath: closePath,\r\n            offset: offset,\r\n            updatable: updatable,\r\n            sideOrientation: sideOrientation,\r\n            instance: instance,\r\n        },\r\n        scene\r\n    );\r\n};\r\n"]}