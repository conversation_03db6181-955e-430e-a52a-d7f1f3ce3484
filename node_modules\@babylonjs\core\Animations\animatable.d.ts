import { Animation } from "./animation";
import { RuntimeAnimation } from "./runtimeAnimation";
import type { Nullable } from "../types";
import { Observable } from "../Misc/observable";
import { Scene } from "../scene";
import { Matrix, Quaternion, Vector3 } from "../Maths/math.vector";
import type { Node } from "../node";
/**
 * Class used to store an actual running animation
 */
export declare class Animatable {
    /** defines the target object */
    target: any;
    /** defines the starting frame number (default is 0) */
    fromFrame: number;
    /** defines the ending frame number (default is 100) */
    toFrame: number;
    /** defines if the animation must loop (default is false)  */
    loopAnimation: boolean;
    /** defines a callback to call when animation ends if it is not looping */
    onAnimationEnd?: Nullable<() => void> | undefined;
    /** defines a callback to call when animation loops */
    onAnimationLoop?: Nullable<() => void> | undefined;
    /** defines whether the animation should be evaluated additively */
    isAdditive: boolean;
    /** defines the order in which this animatable should be processed in the list of active animatables (default: 0) */
    playOrder: number;
    private _localDelayOffset;
    private _pausedDelay;
    private _manualJumpDelay;
    /** @hidden */
    _runtimeAnimations: RuntimeAnimation[];
    private _paused;
    private _scene;
    private _speedRatio;
    private _weight;
    private _syncRoot;
    private _frameToSyncFromJump;
    private _goToFrame;
    /**
     * Gets or sets a boolean indicating if the animatable must be disposed and removed at the end of the animation.
     * This will only apply for non looping animation (default is true)
     */
    disposeOnEnd: boolean;
    /**
     * Gets a boolean indicating if the animation has started
     */
    animationStarted: boolean;
    /**
     * Observer raised when the animation ends
     */
    onAnimationEndObservable: Observable<Animatable>;
    /**
     * Observer raised when the animation loops
     */
    onAnimationLoopObservable: Observable<Animatable>;
    /**
     * Gets the root Animatable used to synchronize and normalize animations
     */
    get syncRoot(): Nullable<Animatable>;
    /**
     * Gets the current frame of the first RuntimeAnimation
     * Used to synchronize Animatables
     */
    get masterFrame(): number;
    /**
     * Gets or sets the animatable weight (-1.0 by default meaning not weighted)
     */
    get weight(): number;
    set weight(value: number);
    /**
     * Gets or sets the speed ratio to apply to the animatable (1.0 by default)
     */
    get speedRatio(): number;
    set speedRatio(value: number);
    /**
     * Gets the elapsed time since the animatable started in milliseconds
     */
    get elapsedTime(): number;
    /**
     * Creates a new Animatable
     * @param scene defines the hosting scene
     * @param target defines the target object
     * @param fromFrame defines the starting frame number (default is 0)
     * @param toFrame defines the ending frame number (default is 100)
     * @param loopAnimation defines if the animation must loop (default is false)
     * @param speedRatio defines the factor to apply to animation speed (default is 1)
     * @param onAnimationEnd defines a callback to call when animation ends if it is not looping
     * @param animations defines a group of animation to add to the new Animatable
     * @param onAnimationLoop defines a callback to call when animation loops
     * @param isAdditive defines whether the animation should be evaluated additively
     * @param playOrder defines the order in which this animatable should be processed in the list of active animatables (default: 0)
     */
    constructor(scene: Scene, 
    /** defines the target object */
    target: any, 
    /** defines the starting frame number (default is 0) */
    fromFrame?: number, 
    /** defines the ending frame number (default is 100) */
    toFrame?: number, 
    /** defines if the animation must loop (default is false)  */
    loopAnimation?: boolean, speedRatio?: number, 
    /** defines a callback to call when animation ends if it is not looping */
    onAnimationEnd?: Nullable<() => void> | undefined, animations?: Animation[], 
    /** defines a callback to call when animation loops */
    onAnimationLoop?: Nullable<() => void> | undefined, 
    /** defines whether the animation should be evaluated additively */
    isAdditive?: boolean, 
    /** defines the order in which this animatable should be processed in the list of active animatables (default: 0) */
    playOrder?: number);
    /**
     * Synchronize and normalize current Animatable with a source Animatable
     * This is useful when using animation weights and when animations are not of the same length
     * @param root defines the root Animatable to synchronize with (null to stop synchronizing)
     * @returns the current Animatable
     */
    syncWith(root: Nullable<Animatable>): Animatable;
    /**
     * Gets the list of runtime animations
     * @returns an array of RuntimeAnimation
     */
    getAnimations(): RuntimeAnimation[];
    /**
     * Adds more animations to the current animatable
     * @param target defines the target of the animations
     * @param animations defines the new animations to add
     */
    appendAnimations(target: any, animations: Animation[]): void;
    /**
     * Gets the source animation for a specific property
     * @param property defines the property to look for
     * @returns null or the source animation for the given property
     */
    getAnimationByTargetProperty(property: string): Nullable<Animation>;
    /**
     * Gets the runtime animation for a specific property
     * @param property defines the property to look for
     * @returns null or the runtime animation for the given property
     */
    getRuntimeAnimationByTargetProperty(property: string): Nullable<RuntimeAnimation>;
    /**
     * Resets the animatable to its original state
     */
    reset(): void;
    /**
     * Allows the animatable to blend with current running animations
     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending
     * @param blendingSpeed defines the blending speed to use
     */
    enableBlending(blendingSpeed: number): void;
    /**
     * Disable animation blending
     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending
     */
    disableBlending(): void;
    /**
     * Jump directly to a given frame
     * @param frame defines the frame to jump to
     */
    goToFrame(frame: number): void;
    /**
     * Returns true if the animations for this animatable are paused
     */
    get paused(): boolean;
    /**
     * Pause the animation
     */
    pause(): void;
    /**
     * Restart the animation
     */
    restart(): void;
    private _raiseOnAnimationEnd;
    /**
     * Stop and delete the current animation
     * @param animationName defines a string used to only stop some of the runtime animations instead of all
     * @param targetMask a function that determines if the animation should be stopped based on its target (all animations will be stopped if both this and animationName are empty)
     * @param useGlobalSplice if true, the animatables will be removed by the caller of this function (false by default)
     */
    stop(animationName?: string, targetMask?: (target: any) => boolean, useGlobalSplice?: boolean): void;
    /**
     * Wait asynchronously for the animation to end
     * @returns a promise which will be fulfilled when the animation ends
     */
    waitAsync(): Promise<Animatable>;
    /**
     * @internal
     */
    _animate(delay: number): boolean;
}
declare module "../scene" {
    interface Scene {
        /** @internal */
        _registerTargetForLateAnimationBinding(runtimeAnimation: RuntimeAnimation, originalValue: any): void;
        /** @internal */
        _processLateAnimationBindingsForMatrices(holder: {
            totalWeight: number;
            totalAdditiveWeight: number;
            animations: RuntimeAnimation[];
            additiveAnimations: RuntimeAnimation[];
            originalValue: Matrix;
        }): any;
        /** @internal */
        _processLateAnimationBindingsForQuaternions(holder: {
            totalWeight: number;
            totalAdditiveWeight: number;
            animations: RuntimeAnimation[];
            additiveAnimations: RuntimeAnimation[];
            originalValue: Quaternion;
        }, refQuaternion: Quaternion): Quaternion;
        /** @internal */
        _processLateAnimationBindings(): void;
        /**
         * Sort active animatables based on their playOrder property
         */
        sortActiveAnimatables(): void;
        /**
         * Will start the animation sequence of a given target
         * @param target defines the target
         * @param from defines from which frame should animation start
         * @param to defines until which frame should animation run.
         * @param weight defines the weight to apply to the animation (1.0 by default)
         * @param loop defines if the animation loops
         * @param speedRatio defines the speed in which to run the animation (1.0 by default)
         * @param onAnimationEnd defines the function to be executed when the animation ends
         * @param animatable defines an animatable object. If not provided a new one will be created from the given params
         * @param targetMask defines if the target should be animated if animations are present (this is called recursively on descendant animatables regardless of return value)
         * @param onAnimationLoop defines the callback to call when an animation loops
         * @param isAdditive defines whether the animation should be evaluated additively (false by default)
         * @returns the animatable object created for this animation
         */
        beginWeightedAnimation(target: any, from: number, to: number, weight: number, loop?: boolean, speedRatio?: number, onAnimationEnd?: () => void, animatable?: Animatable, targetMask?: (target: any) => boolean, onAnimationLoop?: () => void, isAdditive?: boolean): Animatable;
        /**
         * Will start the animation sequence of a given target
         * @param target defines the target
         * @param from defines from which frame should animation start
         * @param to defines until which frame should animation run.
         * @param loop defines if the animation loops
         * @param speedRatio defines the speed in which to run the animation (1.0 by default)
         * @param onAnimationEnd defines the function to be executed when the animation ends
         * @param animatable defines an animatable object. If not provided a new one will be created from the given params
         * @param stopCurrent defines if the current animations must be stopped first (true by default)
         * @param targetMask defines if the target should be animate if animations are present (this is called recursively on descendant animatables regardless of return value)
         * @param onAnimationLoop defines the callback to call when an animation loops
         * @param isAdditive defines whether the animation should be evaluated additively (false by default)
         * @returns the animatable object created for this animation
         */
        beginAnimation(target: any, from: number, to: number, loop?: boolean, speedRatio?: number, onAnimationEnd?: () => void, animatable?: Animatable, stopCurrent?: boolean, targetMask?: (target: any) => boolean, onAnimationLoop?: () => void, isAdditive?: boolean): Animatable;
        /**
         * Will start the animation sequence of a given target and its hierarchy
         * @param target defines the target
         * @param directDescendantsOnly if true only direct descendants will be used, if false direct and also indirect (children of children, an so on in a recursive manner) descendants will be used.
         * @param from defines from which frame should animation start
         * @param to defines until which frame should animation run.
         * @param loop defines if the animation loops
         * @param speedRatio defines the speed in which to run the animation (1.0 by default)
         * @param onAnimationEnd defines the function to be executed when the animation ends
         * @param animatable defines an animatable object. If not provided a new one will be created from the given params
         * @param stopCurrent defines if the current animations must be stopped first (true by default)
         * @param targetMask defines if the target should be animated if animations are present (this is called recursively on descendant animatables regardless of return value)
         * @param onAnimationLoop defines the callback to call when an animation loops
         * @param isAdditive defines whether the animation should be evaluated additively (false by default)
         * @returns the list of created animatables
         */
        beginHierarchyAnimation(target: any, directDescendantsOnly: boolean, from: number, to: number, loop?: boolean, speedRatio?: number, onAnimationEnd?: () => void, animatable?: Animatable, stopCurrent?: boolean, targetMask?: (target: any) => boolean, onAnimationLoop?: () => void, isAdditive?: boolean): Animatable[];
        /**
         * Begin a new animation on a given node
         * @param target defines the target where the animation will take place
         * @param animations defines the list of animations to start
         * @param from defines the initial value
         * @param to defines the final value
         * @param loop defines if you want animation to loop (off by default)
         * @param speedRatio defines the speed ratio to apply to all animations
         * @param onAnimationEnd defines the callback to call when an animation ends (will be called once per node)
         * @param onAnimationLoop defines the callback to call when an animation loops
         * @param isAdditive defines whether the animation should be evaluated additively (false by default)
         * @returns the list of created animatables
         */
        beginDirectAnimation(target: any, animations: Animation[], from: number, to: number, loop?: boolean, speedRatio?: number, onAnimationEnd?: () => void, onAnimationLoop?: () => void, isAdditive?: boolean): Animatable;
        /**
         * Begin a new animation on a given node and its hierarchy
         * @param target defines the root node where the animation will take place
         * @param directDescendantsOnly if true only direct descendants will be used, if false direct and also indirect (children of children, an so on in a recursive manner) descendants will be used.
         * @param animations defines the list of animations to start
         * @param from defines the initial value
         * @param to defines the final value
         * @param loop defines if you want animation to loop (off by default)
         * @param speedRatio defines the speed ratio to apply to all animations
         * @param onAnimationEnd defines the callback to call when an animation ends (will be called once per node)
         * @param onAnimationLoop defines the callback to call when an animation loops
         * @param isAdditive defines whether the animation should be evaluated additively (false by default)
         * @returns the list of animatables created for all nodes
         */
        beginDirectHierarchyAnimation(target: Node, directDescendantsOnly: boolean, animations: Animation[], from: number, to: number, loop?: boolean, speedRatio?: number, onAnimationEnd?: () => void, onAnimationLoop?: () => void, isAdditive?: boolean): Animatable[];
        /**
         * Gets the animatable associated with a specific target
         * @param target defines the target of the animatable
         * @returns the required animatable if found
         */
        getAnimatableByTarget(target: any): Nullable<Animatable>;
        /**
         * Gets all animatables associated with a given target
         * @param target defines the target to look animatables for
         * @returns an array of Animatables
         */
        getAllAnimatablesByTarget(target: any): Array<Animatable>;
        /**
         * Stops and removes all animations that have been applied to the scene
         */
        stopAllAnimations(): void;
        /**
         * Gets the current delta time used by animation engine
         */
        deltaTime: number;
    }
}
declare module "../Bones/bone" {
    interface Bone {
        /**
         * Copy an animation range from another bone
         * @param source defines the source bone
         * @param rangeName defines the range name to copy
         * @param frameOffset defines the frame offset
         * @param rescaleAsRequired defines if rescaling must be applied if required
         * @param skelDimensionsRatio defines the scaling ratio
         * @returns true if operation was successful
         */
        copyAnimationRange(source: Bone, rangeName: string, frameOffset: number, rescaleAsRequired: boolean, skelDimensionsRatio: Nullable<Vector3>): boolean;
    }
}
