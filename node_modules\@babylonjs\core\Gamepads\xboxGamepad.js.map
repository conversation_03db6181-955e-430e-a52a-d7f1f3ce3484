{"version": 3, "file": "xboxGamepad.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Gamepads/xboxGamepad.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;AAC9C;;GAEG;AACH,MAAM,CAAN,IAAY,aAqBX;AArBD,WAAY,aAAa;IACrB,QAAQ;IACR,2CAAK,CAAA;IACL,QAAQ;IACR,2CAAK,CAAA;IACL,QAAQ;IACR,2CAAK,CAAA;IACL,QAAQ;IACR,2CAAK,CAAA;IACL,kBAAkB;IAClB,6CAAM,CAAA;IACN,mBAAmB;IACnB,6CAAM,CAAA;IACN,WAAW;IACX,iDAAQ,CAAA;IACR,YAAY;IACZ,mDAAS,CAAA;IACT,iBAAiB;IACjB,4DAAc,CAAA;IACd,kBAAkB;IAClB,8DAAe,CAAA;AACnB,CAAC,EArBW,aAAa,KAAb,aAAa,QAqBxB;AAED,uCAAuC;AACvC,MAAM,CAAN,IAAY,WASX;AATD,WAAY,WAAW;IACnB,SAAS;IACT,0CAAO,CAAA;IACP,WAAW;IACX,8CAAS,CAAA;IACT,WAAW;IACX,8CAAS,CAAA;IACT,YAAY;IACZ,gDAAU,CAAA;AACd,CAAC,EATW,WAAW,KAAX,WAAW,QAStB;AAED;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,OAAO;IAuCnC;;;;;;OAMG;IACH,YAAY,EAAU,EAAE,KAAa,EAAE,OAAY,EAAE,UAAmB,KAAK;QACzE,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QA9ClC,iBAAY,GAAW,CAAC,CAAC;QACzB,kBAAa,GAAW,CAAC,CAAC;QAUlC,iDAAiD;QAC1C,2BAAsB,GAAG,IAAI,UAAU,EAAiB,CAAC;QAChE,kDAAkD;QAC3C,yBAAoB,GAAG,IAAI,UAAU,EAAiB,CAAC;QAC9D,8CAA8C;QACvC,wBAAmB,GAAG,IAAI,UAAU,EAAe,CAAC;QAC3D,+CAA+C;QACxC,sBAAiB,GAAG,IAAI,UAAU,EAAe,CAAC;QAEjD,aAAQ,GAAW,CAAC,CAAC;QACrB,aAAQ,GAAW,CAAC,CAAC;QACrB,aAAQ,GAAW,CAAC,CAAC;QACrB,aAAQ,GAAW,CAAC,CAAC;QACrB,gBAAW,GAAW,CAAC,CAAC;QACxB,iBAAY,GAAW,CAAC,CAAC;QACzB,cAAS,GAAW,CAAC,CAAC;QACtB,cAAS,GAAW,CAAC,CAAC;QAEtB,qBAAgB,GAAW,CAAC,CAAC;QAC7B,sBAAiB,GAAW,CAAC,CAAC;QAC9B,YAAO,GAAW,CAAC,CAAC;QACpB,cAAS,GAAW,CAAC,CAAC;QACtB,cAAS,GAAW,CAAC,CAAC;QACtB,eAAU,GAAW,CAAC,CAAC;QAEvB,kBAAa,GAAY,KAAK,CAAC;QAWnC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,oBAAoB,CAAC,QAAiC;QACzD,IAAI,CAAC,qBAAqB,GAAG,QAAQ,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACI,qBAAqB,CAAC,QAAiC;QAC1D,IAAI,CAAC,sBAAsB,GAAG,QAAQ,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IACD;;OAEG;IACH,IAAW,WAAW,CAAC,QAAgB;QACnC,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE;YAC9D,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;SACxC;QACD,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD;;OAEG;IACH,IAAW,YAAY,CAAC,QAAgB;QACpC,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,aAAa,KAAK,QAAQ,EAAE;YAChE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;SACzC;QACD,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,QAAgD;QAChE,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,QAAiD;QAC/D,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,QAA4C;QAC1D,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,QAAQ,CAAC,QAA6C;QACzD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;IAEO,eAAe,CAAC,QAAgB,EAAE,YAAoB,EAAE,UAAyB;QACrF,IAAI,QAAQ,KAAK,YAAY,EAAE;YAC3B,IAAI,QAAQ,KAAK,CAAC,EAAE;gBAChB,IAAI,IAAI,CAAC,aAAa,EAAE;oBACpB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;iBAClC;gBAED,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;aAC3D;YACD,IAAI,QAAQ,KAAK,CAAC,EAAE;gBAChB,IAAI,IAAI,CAAC,WAAW,EAAE;oBAClB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;iBAChC;gBAED,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;aACzD;SACJ;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,aAAa,CAAC,QAAgB,EAAE,YAAoB,EAAE,UAAuB;QACjF,IAAI,QAAQ,KAAK,YAAY,EAAE;YAC3B,IAAI,QAAQ,KAAK,CAAC,EAAE;gBAChB,IAAI,IAAI,CAAC,WAAW,EAAE;oBAClB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;iBAChC;gBAED,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;aACxD;YACD,IAAI,QAAQ,KAAK,CAAC,EAAE;gBAChB,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;iBAC9B;gBAED,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;aACtD;SACJ;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IACD;;OAEG;IACH,IAAW,OAAO,CAAC,KAAK;QACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IACD;;OAEG;IACH,IAAW,OAAO,CAAC,KAAK;QACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IACD;;OAEG;IACH,IAAW,OAAO,CAAC,KAAK;QACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IACD;;OAEG;IACH,IAAW,OAAO,CAAC,KAAK;QACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IACD;;OAEG;IACH,IAAW,WAAW,CAAC,KAAK;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC;IAC5F,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;OAEG;IACH,IAAW,UAAU,CAAC,KAAK;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IACD;;OAEG;IACH,IAAW,QAAQ,CAAC,KAAK;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IACD;;OAEG;IACH,IAAW,QAAQ,CAAC,KAAK;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IACD;;OAEG;IACH,IAAW,eAAe,CAAC,KAAK;QAC5B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;IACxG,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IACD;;OAEG;IACH,IAAW,gBAAgB,CAAC,KAAK;QAC7B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE,aAAa,CAAC,UAAU,CAAC,CAAC;IAC3G,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IACD;;OAEG;IACH,IAAW,MAAM,CAAC,KAAK;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IACD;;OAEG;IACH,IAAW,QAAQ,CAAC,KAAK;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IACD;;OAEG;IACH,IAAW,QAAQ,CAAC,KAAK;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD;;OAEG;IACH,IAAW,SAAS,CAAC,KAAK;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACI,MAAM;QACT,KAAK,CAAC,MAAM,EAAE,CAAC;QACf,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACpD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACpD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACpD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACrD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACrD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACxD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACzD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACvD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACxD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;YAC7D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;YAC9D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;YACpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;YACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;YACtD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;SAC1D;aAAM;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACpD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACpD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACpD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACrD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACrD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACxD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACzD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACvD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACxD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;YAC7D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;YAC9D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;YACpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;YACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;YACtD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;SAC1D;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;IACnC,CAAC;CACJ", "sourcesContent": ["import { Observable } from \"../Misc/observable\";\r\nimport { Gamepad } from \"../Gamepads/gamepad\";\r\n/**\r\n * Defines supported buttons for XBox360 compatible gamepads\r\n */\r\nexport enum Xbox360Button {\r\n    /** A */\r\n    A = 0,\r\n    /** B */\r\n    B = 1,\r\n    /** X */\r\n    X = 2,\r\n    /** Y */\r\n    Y = 3,\r\n    /** Left button */\r\n    LB = 4,\r\n    /** Right button */\r\n    RB = 5,\r\n    /** Back */\r\n    Back = 8,\r\n    /** Start */\r\n    Start = 9,\r\n    /** Left stick */\r\n    LeftStick = 10,\r\n    /** Right stick */\r\n    RightStick = 11,\r\n}\r\n\r\n/** Defines values for XBox360 DPad  */\r\nexport enum Xbox360Dpad {\r\n    /** Up */\r\n    Up = 12,\r\n    /** Down */\r\n    Down = 13,\r\n    /** Left */\r\n    Left = 14,\r\n    /** Right */\r\n    Right = 15,\r\n}\r\n\r\n/**\r\n * Defines a XBox360 gamepad\r\n */\r\nexport class Xbox360Pad extends Gamepad {\r\n    private _leftTrigger: number = 0;\r\n    private _rightTrigger: number = 0;\r\n\r\n    private _onlefttriggerchanged: (value: number) => void;\r\n    private _onrighttriggerchanged: (value: number) => void;\r\n\r\n    private _onbuttondown: (buttonPressed: Xbox360Button) => void;\r\n    private _onbuttonup: (buttonReleased: Xbox360Button) => void;\r\n    private _ondpaddown: (dPadPressed: Xbox360Dpad) => void;\r\n    private _ondpadup: (dPadReleased: Xbox360Dpad) => void;\r\n\r\n    /** Observable raised when a button is pressed */\r\n    public onButtonDownObservable = new Observable<Xbox360Button>();\r\n    /** Observable raised when a button is released */\r\n    public onButtonUpObservable = new Observable<Xbox360Button>();\r\n    /** Observable raised when a pad is pressed */\r\n    public onPadDownObservable = new Observable<Xbox360Dpad>();\r\n    /** Observable raised when a pad is released */\r\n    public onPadUpObservable = new Observable<Xbox360Dpad>();\r\n\r\n    private _buttonA: number = 0;\r\n    private _buttonB: number = 0;\r\n    private _buttonX: number = 0;\r\n    private _buttonY: number = 0;\r\n    private _buttonBack: number = 0;\r\n    private _buttonStart: number = 0;\r\n    private _buttonLB: number = 0;\r\n    private _buttonRB: number = 0;\r\n\r\n    private _buttonLeftStick: number = 0;\r\n    private _buttonRightStick: number = 0;\r\n    private _dPadUp: number = 0;\r\n    private _dPadDown: number = 0;\r\n    private _dPadLeft: number = 0;\r\n    private _dPadRight: number = 0;\r\n\r\n    private _isXboxOnePad: boolean = false;\r\n\r\n    /**\r\n     * Creates a new XBox360 gamepad object\r\n     * @param id defines the id of this gamepad\r\n     * @param index defines its index\r\n     * @param gamepad defines the internal HTML gamepad object\r\n     * @param xboxOne defines if it is a XBox One gamepad\r\n     */\r\n    constructor(id: string, index: number, gamepad: any, xboxOne: boolean = false) {\r\n        super(id, index, gamepad, 0, 1, 2, 3);\r\n        this.type = Gamepad.XBOX;\r\n        this._isXboxOnePad = xboxOne;\r\n    }\r\n\r\n    /**\r\n     * Defines the callback to call when left trigger is pressed\r\n     * @param callback defines the callback to use\r\n     */\r\n    public onlefttriggerchanged(callback: (value: number) => void) {\r\n        this._onlefttriggerchanged = callback;\r\n    }\r\n\r\n    /**\r\n     * Defines the callback to call when right trigger is pressed\r\n     * @param callback defines the callback to use\r\n     */\r\n    public onrighttriggerchanged(callback: (value: number) => void) {\r\n        this._onrighttriggerchanged = callback;\r\n    }\r\n\r\n    /**\r\n     * Gets the left trigger value\r\n     */\r\n    public get leftTrigger(): number {\r\n        return this._leftTrigger;\r\n    }\r\n    /**\r\n     * Sets the left trigger value\r\n     */\r\n    public set leftTrigger(newValue: number) {\r\n        if (this._onlefttriggerchanged && this._leftTrigger !== newValue) {\r\n            this._onlefttriggerchanged(newValue);\r\n        }\r\n        this._leftTrigger = newValue;\r\n    }\r\n\r\n    /**\r\n     * Gets the right trigger value\r\n     */\r\n    public get rightTrigger(): number {\r\n        return this._rightTrigger;\r\n    }\r\n    /**\r\n     * Sets the right trigger value\r\n     */\r\n    public set rightTrigger(newValue: number) {\r\n        if (this._onrighttriggerchanged && this._rightTrigger !== newValue) {\r\n            this._onrighttriggerchanged(newValue);\r\n        }\r\n        this._rightTrigger = newValue;\r\n    }\r\n\r\n    /**\r\n     * Defines the callback to call when a button is pressed\r\n     * @param callback defines the callback to use\r\n     */\r\n    public onbuttondown(callback: (buttonPressed: Xbox360Button) => void) {\r\n        this._onbuttondown = callback;\r\n    }\r\n\r\n    /**\r\n     * Defines the callback to call when a button is released\r\n     * @param callback defines the callback to use\r\n     */\r\n    public onbuttonup(callback: (buttonReleased: Xbox360Button) => void) {\r\n        this._onbuttonup = callback;\r\n    }\r\n\r\n    /**\r\n     * Defines the callback to call when a pad is pressed\r\n     * @param callback defines the callback to use\r\n     */\r\n    public ondpaddown(callback: (dPadPressed: Xbox360Dpad) => void) {\r\n        this._ondpaddown = callback;\r\n    }\r\n\r\n    /**\r\n     * Defines the callback to call when a pad is released\r\n     * @param callback defines the callback to use\r\n     */\r\n    public ondpadup(callback: (dPadReleased: Xbox360Dpad) => void) {\r\n        this._ondpadup = callback;\r\n    }\r\n\r\n    private _setButtonValue(newValue: number, currentValue: number, buttonType: Xbox360Button): number {\r\n        if (newValue !== currentValue) {\r\n            if (newValue === 1) {\r\n                if (this._onbuttondown) {\r\n                    this._onbuttondown(buttonType);\r\n                }\r\n\r\n                this.onButtonDownObservable.notifyObservers(buttonType);\r\n            }\r\n            if (newValue === 0) {\r\n                if (this._onbuttonup) {\r\n                    this._onbuttonup(buttonType);\r\n                }\r\n\r\n                this.onButtonUpObservable.notifyObservers(buttonType);\r\n            }\r\n        }\r\n        return newValue;\r\n    }\r\n\r\n    private _setDPadValue(newValue: number, currentValue: number, buttonType: Xbox360Dpad): number {\r\n        if (newValue !== currentValue) {\r\n            if (newValue === 1) {\r\n                if (this._ondpaddown) {\r\n                    this._ondpaddown(buttonType);\r\n                }\r\n\r\n                this.onPadDownObservable.notifyObservers(buttonType);\r\n            }\r\n            if (newValue === 0) {\r\n                if (this._ondpadup) {\r\n                    this._ondpadup(buttonType);\r\n                }\r\n\r\n                this.onPadUpObservable.notifyObservers(buttonType);\r\n            }\r\n        }\r\n        return newValue;\r\n    }\r\n\r\n    /**\r\n     * Gets the value of the `A` button\r\n     */\r\n    public get buttonA(): number {\r\n        return this._buttonA;\r\n    }\r\n    /**\r\n     * Sets the value of the `A` button\r\n     */\r\n    public set buttonA(value) {\r\n        this._buttonA = this._setButtonValue(value, this._buttonA, Xbox360Button.A);\r\n    }\r\n\r\n    /**\r\n     * Gets the value of the `B` button\r\n     */\r\n    public get buttonB(): number {\r\n        return this._buttonB;\r\n    }\r\n    /**\r\n     * Sets the value of the `B` button\r\n     */\r\n    public set buttonB(value) {\r\n        this._buttonB = this._setButtonValue(value, this._buttonB, Xbox360Button.B);\r\n    }\r\n\r\n    /**\r\n     * Gets the value of the `X` button\r\n     */\r\n    public get buttonX(): number {\r\n        return this._buttonX;\r\n    }\r\n    /**\r\n     * Sets the value of the `X` button\r\n     */\r\n    public set buttonX(value) {\r\n        this._buttonX = this._setButtonValue(value, this._buttonX, Xbox360Button.X);\r\n    }\r\n\r\n    /**\r\n     * Gets the value of the `Y` button\r\n     */\r\n    public get buttonY(): number {\r\n        return this._buttonY;\r\n    }\r\n    /**\r\n     * Sets the value of the `Y` button\r\n     */\r\n    public set buttonY(value) {\r\n        this._buttonY = this._setButtonValue(value, this._buttonY, Xbox360Button.Y);\r\n    }\r\n\r\n    /**\r\n     * Gets the value of the `Start` button\r\n     */\r\n    public get buttonStart(): number {\r\n        return this._buttonStart;\r\n    }\r\n    /**\r\n     * Sets the value of the `Start` button\r\n     */\r\n    public set buttonStart(value) {\r\n        this._buttonStart = this._setButtonValue(value, this._buttonStart, Xbox360Button.Start);\r\n    }\r\n\r\n    /**\r\n     * Gets the value of the `Back` button\r\n     */\r\n    public get buttonBack(): number {\r\n        return this._buttonBack;\r\n    }\r\n    /**\r\n     * Sets the value of the `Back` button\r\n     */\r\n    public set buttonBack(value) {\r\n        this._buttonBack = this._setButtonValue(value, this._buttonBack, Xbox360Button.Back);\r\n    }\r\n\r\n    /**\r\n     * Gets the value of the `Left` button\r\n     */\r\n    public get buttonLB(): number {\r\n        return this._buttonLB;\r\n    }\r\n    /**\r\n     * Sets the value of the `Left` button\r\n     */\r\n    public set buttonLB(value) {\r\n        this._buttonLB = this._setButtonValue(value, this._buttonLB, Xbox360Button.LB);\r\n    }\r\n\r\n    /**\r\n     * Gets the value of the `Right` button\r\n     */\r\n    public get buttonRB(): number {\r\n        return this._buttonRB;\r\n    }\r\n    /**\r\n     * Sets the value of the `Right` button\r\n     */\r\n    public set buttonRB(value) {\r\n        this._buttonRB = this._setButtonValue(value, this._buttonRB, Xbox360Button.RB);\r\n    }\r\n\r\n    /**\r\n     * Gets the value of the Left joystick\r\n     */\r\n    public get buttonLeftStick(): number {\r\n        return this._buttonLeftStick;\r\n    }\r\n    /**\r\n     * Sets the value of the Left joystick\r\n     */\r\n    public set buttonLeftStick(value) {\r\n        this._buttonLeftStick = this._setButtonValue(value, this._buttonLeftStick, Xbox360Button.LeftStick);\r\n    }\r\n\r\n    /**\r\n     * Gets the value of the Right joystick\r\n     */\r\n    public get buttonRightStick(): number {\r\n        return this._buttonRightStick;\r\n    }\r\n    /**\r\n     * Sets the value of the Right joystick\r\n     */\r\n    public set buttonRightStick(value) {\r\n        this._buttonRightStick = this._setButtonValue(value, this._buttonRightStick, Xbox360Button.RightStick);\r\n    }\r\n\r\n    /**\r\n     * Gets the value of D-pad up\r\n     */\r\n    public get dPadUp(): number {\r\n        return this._dPadUp;\r\n    }\r\n    /**\r\n     * Sets the value of D-pad up\r\n     */\r\n    public set dPadUp(value) {\r\n        this._dPadUp = this._setDPadValue(value, this._dPadUp, Xbox360Dpad.Up);\r\n    }\r\n\r\n    /**\r\n     * Gets the value of D-pad down\r\n     */\r\n    public get dPadDown(): number {\r\n        return this._dPadDown;\r\n    }\r\n    /**\r\n     * Sets the value of D-pad down\r\n     */\r\n    public set dPadDown(value) {\r\n        this._dPadDown = this._setDPadValue(value, this._dPadDown, Xbox360Dpad.Down);\r\n    }\r\n\r\n    /**\r\n     * Gets the value of D-pad left\r\n     */\r\n    public get dPadLeft(): number {\r\n        return this._dPadLeft;\r\n    }\r\n    /**\r\n     * Sets the value of D-pad left\r\n     */\r\n    public set dPadLeft(value) {\r\n        this._dPadLeft = this._setDPadValue(value, this._dPadLeft, Xbox360Dpad.Left);\r\n    }\r\n\r\n    /**\r\n     * Gets the value of D-pad right\r\n     */\r\n    public get dPadRight(): number {\r\n        return this._dPadRight;\r\n    }\r\n    /**\r\n     * Sets the value of D-pad right\r\n     */\r\n    public set dPadRight(value) {\r\n        this._dPadRight = this._setDPadValue(value, this._dPadRight, Xbox360Dpad.Right);\r\n    }\r\n\r\n    /**\r\n     * Force the gamepad to synchronize with device values\r\n     */\r\n    public update() {\r\n        super.update();\r\n        if (this._isXboxOnePad) {\r\n            this.buttonA = this.browserGamepad.buttons[0].value;\r\n            this.buttonB = this.browserGamepad.buttons[1].value;\r\n            this.buttonX = this.browserGamepad.buttons[2].value;\r\n            this.buttonY = this.browserGamepad.buttons[3].value;\r\n            this.buttonLB = this.browserGamepad.buttons[4].value;\r\n            this.buttonRB = this.browserGamepad.buttons[5].value;\r\n            this.leftTrigger = this.browserGamepad.buttons[6].value;\r\n            this.rightTrigger = this.browserGamepad.buttons[7].value;\r\n            this.buttonBack = this.browserGamepad.buttons[8].value;\r\n            this.buttonStart = this.browserGamepad.buttons[9].value;\r\n            this.buttonLeftStick = this.browserGamepad.buttons[10].value;\r\n            this.buttonRightStick = this.browserGamepad.buttons[11].value;\r\n            this.dPadUp = this.browserGamepad.buttons[12].value;\r\n            this.dPadDown = this.browserGamepad.buttons[13].value;\r\n            this.dPadLeft = this.browserGamepad.buttons[14].value;\r\n            this.dPadRight = this.browserGamepad.buttons[15].value;\r\n        } else {\r\n            this.buttonA = this.browserGamepad.buttons[0].value;\r\n            this.buttonB = this.browserGamepad.buttons[1].value;\r\n            this.buttonX = this.browserGamepad.buttons[2].value;\r\n            this.buttonY = this.browserGamepad.buttons[3].value;\r\n            this.buttonLB = this.browserGamepad.buttons[4].value;\r\n            this.buttonRB = this.browserGamepad.buttons[5].value;\r\n            this.leftTrigger = this.browserGamepad.buttons[6].value;\r\n            this.rightTrigger = this.browserGamepad.buttons[7].value;\r\n            this.buttonBack = this.browserGamepad.buttons[8].value;\r\n            this.buttonStart = this.browserGamepad.buttons[9].value;\r\n            this.buttonLeftStick = this.browserGamepad.buttons[10].value;\r\n            this.buttonRightStick = this.browserGamepad.buttons[11].value;\r\n            this.dPadUp = this.browserGamepad.buttons[12].value;\r\n            this.dPadDown = this.browserGamepad.buttons[13].value;\r\n            this.dPadLeft = this.browserGamepad.buttons[14].value;\r\n            this.dPadRight = this.browserGamepad.buttons[15].value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disposes the gamepad\r\n     */\r\n    public dispose() {\r\n        super.dispose();\r\n        this.onButtonDownObservable.clear();\r\n        this.onButtonUpObservable.clear();\r\n        this.onPadDownObservable.clear();\r\n        this.onPadUpObservable.clear();\r\n    }\r\n}\r\n"]}