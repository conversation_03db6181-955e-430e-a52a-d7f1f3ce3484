{"version": 3, "file": "physicsImpostor.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Physics/v1/physicsImpostor.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAE9D,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AAOzC,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,KAAK,EAAE,MAAM,uBAAuB,CAAC;AA6K9C,IAAI,CAAC,sBAAsB,GAAG,UAAU,KAAY,EAAE,YAAmC,EAAE,UAAe;IACtG,OAAO,IAAI,eAAe,CACtB,YAAY,EACZ,UAAU,CAAC,eAAe,EAC1B;QACI,IAAI,EAAE,UAAU,CAAC,WAAW;QAC5B,QAAQ,EAAE,UAAU,CAAC,eAAe;QACpC,WAAW,EAAE,UAAU,CAAC,kBAAkB;KAC7C,EACD,KAAK,CACR,CAAC;AACN,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,OAAO,eAAe;IA0CxB;;OAEG;IACH,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACJ,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9F,CAAC;IAED,IAAI,IAAI,CAAC,KAAa;QAClB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClG,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ,CAAC,KAAa;QACtB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QACD,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrG,CAAC;IAED;;OAEG;IACH,IAAI,WAAW,CAAC,KAAa;QACzB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QACD,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACR,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO,CAAC,CAAC;SACZ;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;YACzB,OAAO,CAAC,CAAC;SACZ;QACD,OAAO,MAAM,CAAC,eAAgB,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ,CAAC,KAAa;QACtB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;YACzB,OAAO;SACV;QACD,MAAM,CAAC,eAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO,CAAC,CAAC;SACZ;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE;YAC1B,OAAO,CAAC,CAAC;SACZ;QACD,OAAO,MAAM,CAAC,gBAAiB,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,SAAS,CAAC,KAAa;QACvB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE;YAC1B,OAAO;SACV;QACD,MAAM,CAAC,gBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QAClB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO,CAAC,CAAC;SACZ;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE;YACnC,OAAO,CAAC,CAAC;SACZ;QACD,OAAO,MAAM,CAAC,yBAA0B,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB,CAAC,KAAa;QAChC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE;YACnC,OAAO;SACV;QACD,MAAM,CAAC,yBAA0B,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB;QAClB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO,CAAC,CAAC;SACZ;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE;YACnC,OAAO,CAAC,CAAC;SACZ;QACD,OAAO,MAAM,CAAC,yBAA0B,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAI,kBAAkB,CAAC,KAAa;QAChC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE;YACnC,OAAO;SACV;QACD,MAAM,CAAC,yBAA0B,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC;IAuBD;;;;;;OAMG;IACH;IACI;;OAEG;IACI,MAA6B;IACpC;;OAEG;IACI,IAAY,EACX,WAAsC,EAAE,IAAI,EAAE,CAAC,EAAE,EACjD,MAAc;QANf,WAAM,GAAN,MAAM,CAAuB;QAI7B,SAAI,GAAJ,IAAI,CAAQ;QACX,aAAQ,GAAR,QAAQ,CAAyC;QACjD,WAAM,GAAN,MAAM,CAAQ;QAzO1B,gBAAgB;QACT,gBAAW,GAAQ,EAAE,CAAC;QAKrB,wBAAmB,GAAY,KAAK,CAAC;QAErC,kCAA6B,GAAG,IAAI,KAAK,EAAuC,CAAC;QACjF,iCAA4B,GAAG,IAAI,KAAK,EAAuC,CAAC;QACxF,gBAAgB;QACT,+BAA0B,GAG5B,EAAE,CAAC;QAEA,mBAAc,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAUzC,gBAAW,GAAG,KAAK,CAAC;QA+K5B;;WAEG;QACI,SAAI,GAAY,KAAK,CAAC;QAE7B;;WAEG;QACI,aAAQ,GAAW,CAAC,CAAC;QA6Y5B,iDAAiD;QACjD,8DAA8D;QACtD,aAAQ,GAAe,IAAI,UAAU,EAAE,CAAC;QACxC,cAAS,GAAe,IAAI,UAAU,EAAE,CAAC;QAqBjD;;WAEG;QACI,eAAU,GAAG,GAAG,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACtB,OAAO;aACV;YAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,wBAAwB;gBACzB,IAAI,CAAC,MAAM,CAAC,kBAAkB;gBAC9B,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;YAChH,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;gBACtD,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC9E;iBAAM;gBACH,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,IAAI,UAAU,EAAE,CAAC,CAAC;aAC9E;YACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,kCAAkC,EAAE;gBACnD,IAAI,CAAC,MAAM,CAAC,kBAAkB;oBAC1B,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,4BAA4B,CAAC,IAAI,EAAE,iCAAiC,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aACrK;YAED,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAChD,IAAI,CAAC,IAAI,CAAC,CAAC;YACf,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QAEF;;WAEG;QACI,cAAS,GAAG,GAAG,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACtB,OAAO;aACV;YAED,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC/C,IAAI,CAAC,IAAI,CAAC,CAAC;YACf,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,gCAAgC,CAAC,IAAI,CAAC,CAAC;YAC9E,qEAAqE;YACrE,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;gBACtD,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;gBACjC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;aAC/F;YACD,0EAA0E;YAC1E,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACtD,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;gBACpI,IAAI,CAAC,cAAc,CAAC,4BAA4B,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACzD;iBAAM;gBACH,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;aACjD;YACD,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC;QAEF;;WAEG;QACI,mBAAc,GAAiF,IAAI,CAAC;QAE3G;;;WAGG;QACI,cAAS,GAAG,CAAC,CAAwG,EAAE,EAAE;YAC5H,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACjE,OAAO;aACV;YAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACtB,OAAO;aACV;YACD,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC7E,IAAI,aAAa,EAAE;gBACf,2CAA2C;gBAC3C,IAAI,IAAI,CAAC,cAAc,EAAE;oBACrB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;iBAC5C;gBACD,IAAI,CAAC,0BAA0B;qBAC1B,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE;oBACZ,OAAO,GAAG,CAAC,cAAc,CAAC,OAAO,CAAkB,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC7E,CAAC,CAAC;qBACD,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;oBACb,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAmB,aAAa,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;gBACjG,CAAC,CAAC,CAAC;aACV;QACL,CAAC,CAAC;QAteE,eAAe;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC;YACvE,OAAO;SACV;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE;YAC3C,MAAM,CAAC,IAAI,CAAC,sJAAsJ,CAAC,CAAC;SACvK;QAED,iCAAiC;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE;YACjC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;SACnC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,OAAO;SACV;QAED,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,EAAE;YACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SACpB;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAS,CAAC;QAC5D,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,MAAM,CAAC,KAAK,CAAC,qFAAqF,CAAC,CAAC;SACvG;aAAM;YACH,yCAAyC;YACzC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;gBACjC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;oBACtB,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;iBAC5I;qBAAM;oBACH,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;iBACrD;aACJ;YACD,wBAAwB;YACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;YAClE,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAChF,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;YACzF,IAAI,IAAI,CAAC,IAAI,EAAE;gBACX,gCAAgC;gBAChC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrE,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAChF,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACjF,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,QAAQ,CAAC,kBAAkB,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC;gBAC7G,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,QAAQ,CAAC,kBAAkB,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC;gBAC7G,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;gBACvF,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACxE,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAC3E,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACrE,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;aAC3E;YACD,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;YAClB,qGAAqG;YACrG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;gBACnD,IAAI,CAAC,KAAK,EAAE,CAAC;aAChB;iBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE;gBAC3C,MAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;aAC7F;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,KAAK;QACR,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QAED,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACxD,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YACnE,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACzC;IACL,CAAC;IAEO,iBAAiB;QACrB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,YAAY,YAAY,EAAE;YAC5C,MAAM,UAAU,GAA+B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YAClE,OAAO,UAAU,CAAC,eAAe,CAAC;SACrC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;IAC/G,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;YAC5C,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;SAC7B;IACL,CAAC;IAED;;OAEG;IAEH;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;IACtG,CAAC;IAED;;;OAGG;IACH,IAAW,MAAM;QACb,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,IAAW,MAAM,CAAC,KAAgC;QAC9C,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,WAAW,CAAC,WAAgB;QACnC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,cAAc,EAAE;YAC1C,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;SAClE;QACD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;YAC7B,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;YACzC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAC5C,gBAAgB;YAChB,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,eAAe,CAAC,mBAAmB,CAAC;YACrE,6CAA6C;YAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAC3F,IAAI,WAAW,EAAE;gBACb,WAAW,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;aACxD;YACD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;YACnD,uCAAuC;YACvC,MAAM,IAAI,GAAG,YAAY,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACnF,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1B,yBAAyB;YACzB,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,CAAC,CAAC;YACnC,kDAAkD;YAClD,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACvE,OAAO,IAAI,CAAC;SACf;aAAM;YACH,OAAO,eAAe,CAAC,mBAAmB,CAAC;SAC9C;IACL,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;YAC7B,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;YACnD,OAAO,YAAY,CAAC,WAAW,CAAC,WAAW,CAAC;SAC/C;aAAM;YACH,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;SAC/B;IACL,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,SAAiB;QAC7B,OAAa,IAAI,CAAC,QAAS,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,SAAiB,EAAE,KAAa;QACtC,IAAI,CAAC,QAAS,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,IAAY;QACvB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE;YAChC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SAC/B;QACD,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAClE;IACL,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IACjH,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,QAA2B;QAChD,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAC5E;IACL,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IAClH,CAAC;IAED;;;OAGG;IACI,kBAAkB,CAAC,QAA2B;QACjD,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAC7E;IACL,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,IAA4C;QACrE,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;SACxE;IACL,CAAC;IAED;;;OAGG;IACI,yBAAyB,CAAC,IAAyC;QACtE,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG;IACI,2BAA2B,CAAC,IAAyC;QACxE,MAAM,KAAK,GAAG,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE/D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACvD;aAAM;YACH,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;SACnD;IACL,CAAC;IAED;;;OAGG;IACI,wBAAwB,CAAC,IAAyC;QACrE,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG;IACI,0BAA0B,CAAC,IAAyC;QACvE,MAAM,KAAK,GAAG,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE9D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACtD;aAAM;YACH,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;SACnD;IACL,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAC3B,cAAwD,EACxD,IAAqG;QAErG,MAAM,mBAAmB,GAA2B,cAAc,YAAY,KAAK,CAAC,CAAC,CAAyB,cAAc,CAAC,CAAC,CAAC,CAAkB,cAAc,CAAC,CAAC;QACjK,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,cAAc,EAAE,mBAAmB,EAAE,CAAC,CAAC;IAClG,CAAC;IAED;;;;OAIG;IACI,0BAA0B,CAC7B,cAAwD,EACxD,IAA8H;QAE9H,MAAM,mBAAmB,GAA2B,cAAc,YAAY,KAAK,CAAC,CAAC,CAAyB,cAAc,CAAC,CAAC,CAAC,CAAkB,cAAc,CAAC,CAAC;QACjK,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;QACf,MAAM,KAAK,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YAC9D,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,KAAK,mBAAmB,CAAC,MAAM,EAAE;gBACvF,yBAAyB;gBACzB,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAE;oBACrD,OAAO,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;gBACtD,CAAC,CAAC,CAAC;gBACH,IAAI,QAAQ,EAAE;oBACV,KAAK,GAAG,GAAG,CAAC;iBACf;gBACD,OAAO,QAAQ,CAAC;aACnB;YACD,OAAO,KAAK,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACpD;aAAM;YACH,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;SACnD;IACL,CAAC;IAOD;;;OAGG;IACI,kBAAkB;QACrB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAChC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,OAAO,MAAM,EAAE;YACX,IAAI,MAAM,CAAC,kBAAkB,EAAE;gBAC3B,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;aACtD;iBAAM;gBACH,UAAU,CAAC,yBAAyB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;aACjH;YACD,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;SAC1B;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IA+FD;;;;;OAKG;IACI,UAAU,CAAC,KAAc,EAAE,YAAqB;QACnD,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;SAChF;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,KAAc,EAAE,YAAqB;QACrD,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;SAClF;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,WAAW,CAAC,aAA8B,EAAE,SAAiB,EAAE,SAA2B;QAC7F,MAAM,KAAK,GAAG,IAAI,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACrD,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAEpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,aAA8B,EAAE,KAAmB;QAC/D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACd,aAAa,EAAE,aAAa;YAC5B,KAAK,EAAE,KAAK;SACf,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;SAC5D;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG;IACI,SAAS,CAAC,aAA8B,EAAE,KAAa,EAAE,MAAc,EAAE,SAAiB,EAAE,8BAAuC;QACtI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO,IAAI,CAAC;SACf;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YACtB,OAAO,IAAI,CAAC;SACf;QACD,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,MAAM,CAAC,YAAa,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,8BAA8B,CAAC,CAAC;SACvG;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACI,OAAO,CAAC,aAA8B,EAAE,MAAc,EAAE,SAAiB,EAAE,8BAAuC;QACrH,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO,IAAI,CAAC;SACf;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YACtB,OAAO,IAAI,CAAC;SACf;QACD,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,MAAM,CAAC,UAAW,CAAC,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,8BAA8B,CAAC,CAAC;SAC9F;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SAC1D;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,MAAM;QACT,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAC3D;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,SAAgC;QACzC,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IACI,OAAO,EAAC,mCAAmC;QAC9C,+CAA+C;QAC/C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QAED,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACvB,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;aACnE;QACL,CAAC,CAAC,CAAC;QACH,0BAA0B;QAC1B,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;SAC7B;aAAM;YACH;;;;;;;gBAOI;SACP;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,QAAiB;QACrC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,QAAoB;QACxC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,cAAc,GAAG,IAAI,UAAU,EAAE,CAAC;SAC1C;QACD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,MAAe;QAClC,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SACxE;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5F,CAAC;IAED;;;;;;;OAOG;IACI,oBAAoB,CAAC,IAAU,EAAE,QAAsB,EAAE,UAAmB,EAAE,WAAoB,EAAE,cAA2B;QAClI,MAAM,OAAO,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC5C,MAAM,IAAI,GAAiB,IAAI,CAAC,MAAM,CAAC;QAEvC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,cAAc,EAAE;gBAChB,MAAM,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;gBAC1C,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;gBAChE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;aAC/D;iBAAM;gBACH,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;aAC9E;SACJ;QAED,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QAEd,IAAI,UAAU,EAAE;YACZ,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;YACzB,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;YACzB,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;YAEzB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YAEnD,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE;gBACnD,WAAW,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;aACrC;YAED,OAAO,CAAC,CAAC,IAAI,WAAW,CAAC;YACzB,OAAO,CAAC,CAAC,IAAI,WAAW,CAAC;YACzB,OAAO,CAAC,CAAC,IAAI,WAAW,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YAClB,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;YAC/C,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;SAC/C;aAAM;YACH,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;YACzD,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC;YACjC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC;YACjC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC;SACpC;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,oBAAoB,CAAC,IAAU,EAAE,QAAsB,EAAE,UAAmB,EAAE,WAAoB,EAAE,cAA2B,EAAE,QAAkB;QACtJ,MAAM,IAAI,GAAiB,IAAI,CAAC,MAAM,CAAC;QAEvC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,cAAc,EAAE;gBAChB,MAAM,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;gBAC1C,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACjE,QAAQ,CAAC,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACnE;iBAAM;gBACH,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACnF;SACJ;QAED,MAAM,GAAG,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACxC,MAAM,OAAO,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE5C,IAAI,CAAC,QAAQ,EAAE;YACX,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACvC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;YACf,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;YACf,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;SAClB;QAED,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAE7C,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,CAAC,IAAI,UAAU,EAAE;YACnE,WAAW,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;SACrC;QAED,IAAI,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,IAAI,EAAE;YACnD,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC;YACjC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC;YACjC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,WAAW,CAAC;SACpC;QAED,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;;AAthCD;;GAEG;AACW,mCAAmB,GAAY,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,AAAhC,CAAiC;AAElE;;GAEG;AACW,mCAAmB,GAAG,UAAU,CAAC,QAAQ,EAAE,AAAxB,CAAyB;AA8B3C,wBAAQ,GAAc,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,AAApD,CAAqD;AAC7D,wBAAQ,GAAe,UAAU,CAAC,QAAQ,EAAE,AAApC,CAAqC;AAi/B5D,gBAAgB;AAChB;;GAEG;AACW,0BAAU,GAAG,CAAC,AAAJ,CAAK;AAC7B;;GAEG;AACW,8BAAc,GAAG,CAAC,AAAJ,CAAK;AACjC;;GAEG;AACW,2BAAW,GAAG,CAAC,AAAJ,CAAK;AAC9B;;GAEG;AACW,6BAAa,GAAG,CAAC,AAAJ,CAAK;AAChC;;GAEG;AACW,4BAAY,GAAG,CAAC,AAAJ,CAAK;AAC/B;;GAEG;AACW,+BAAe,GAAG,CAAC,AAAJ,CAAK;AAClC;;GAEG;AACW,gCAAgB,GAAG,CAAC,AAAJ,CAAK;AACnC;;GAEG;AACW,gCAAgB,GAAG,CAAC,AAAJ,CAAK;AACnC;;GAEG;AACW,iCAAiB,GAAG,CAAC,AAAJ,CAAK;AACpC;;GAEG;AACW,kCAAkB,GAAG,EAAE,AAAL,CAAM;AACtC;;GAEG;AACW,8BAAc,GAAG,GAAG,AAAN,CAAO;AACnC;;GAEG;AACW,4BAAY,GAAG,GAAG,AAAN,CAAO;AACjC;;GAEG;AACW,6BAAa,GAAG,GAAG,AAAN,CAAO;AAClC;;GAEG;AACW,gCAAgB,GAAG,GAAG,AAAN,CAAO", "sourcesContent": ["import type { Nullable, IndicesArray } from \"../../types\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport { ArrayTools } from \"../../Misc/arrayTools\";\r\nimport type { Matrix } from \"../../Maths/math.vector\";\r\nimport { Vector3, Quaternion } from \"../../Maths/math.vector\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../../Meshes/mesh\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { <PERSON> } from \"../../Bones/bone\";\r\nimport type { BoundingInfo } from \"../../Culling/boundingInfo\";\r\nimport type { PhysicsEngine as PhysicsEngineV1 } from \"./physicsEngine\";\r\n\r\nimport type { PhysicsJointData } from \"./physicsJoint\";\r\nimport { PhysicsJoint } from \"./physicsJoint\";\r\nimport { Space } from \"../../Maths/math.axis\";\r\n\r\n/**\r\n * The interface for the physics imposter parameters\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport interface PhysicsImpostorParameters {\r\n    /**\r\n     * The mass of the physics imposter\r\n     */\r\n    mass: number;\r\n    /**\r\n     * The friction of the physics imposter\r\n     */\r\n    friction?: number;\r\n    /**\r\n     * The coefficient of restitution of the physics imposter\r\n     */\r\n    restitution?: number;\r\n    /**\r\n     * The native options of the physics imposter\r\n     */\r\n    nativeOptions?: any;\r\n    /**\r\n     * Specifies if the parent should be ignored\r\n     */\r\n    ignoreParent?: boolean;\r\n    /**\r\n     * Specifies if bi-directional transformations should be disabled\r\n     */\r\n    disableBidirectionalTransformation?: boolean;\r\n    /**\r\n     * The pressure inside the physics imposter, soft object only\r\n     */\r\n    pressure?: number;\r\n    /**\r\n     * The stiffness the physics imposter, soft object only\r\n     */\r\n    stiffness?: number;\r\n    /**\r\n     * The number of iterations used in maintaining consistent vertex velocities, soft object only\r\n     */\r\n    velocityIterations?: number;\r\n    /**\r\n     * The number of iterations used in maintaining consistent vertex positions, soft object only\r\n     */\r\n    positionIterations?: number;\r\n    /**\r\n     * The number used to fix points on a cloth (0, 1, 2, 4, 8) or rope (0, 1, 2) only\r\n     * 0 None, 1, back left or top, 2, back right or bottom, 4, front left, 8, front right\r\n     * Add to fix multiple points\r\n     */\r\n    fixedPoints?: number;\r\n    /**\r\n     * The collision margin around a soft object\r\n     */\r\n    margin?: number;\r\n    /**\r\n     * The collision margin around a soft object\r\n     */\r\n    damping?: number;\r\n    /**\r\n     * The path for a rope based on an extrusion\r\n     */\r\n    path?: any;\r\n    /**\r\n     * The shape of an extrusion used for a rope based on an extrusion\r\n     */\r\n    shape?: any;\r\n}\r\n\r\n/**\r\n * Interface for a physics-enabled object\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport interface IPhysicsEnabledObject {\r\n    /**\r\n     * The position of the physics-enabled object\r\n     */\r\n    position: Vector3;\r\n    /**\r\n     * The rotation of the physics-enabled object\r\n     */\r\n    rotationQuaternion: Nullable<Quaternion>;\r\n    /**\r\n     * The scale of the physics-enabled object\r\n     */\r\n    scaling: Vector3;\r\n    /**\r\n     * The rotation of the physics-enabled object\r\n     */\r\n    rotation?: Vector3;\r\n    /**\r\n     * The parent of the physics-enabled object\r\n     */\r\n    parent?: any;\r\n    /**\r\n     * The bounding info of the physics-enabled object\r\n     * @returns The bounding info of the physics-enabled object\r\n     */\r\n    getBoundingInfo(): BoundingInfo;\r\n    /**\r\n     * Computes the world matrix\r\n     * @param force Specifies if the world matrix should be computed by force\r\n     * @returns A world matrix\r\n     */\r\n    computeWorldMatrix(force: boolean): Matrix;\r\n    /**\r\n     * Gets the world matrix\r\n     * @returns A world matrix\r\n     */\r\n    getWorldMatrix?(): Matrix;\r\n    /**\r\n     * Gets the child meshes\r\n     * @param directDescendantsOnly Specifies if only direct-descendants should be obtained\r\n     * @returns An array of abstract meshes\r\n     */\r\n    getChildMeshes?(directDescendantsOnly?: boolean): Array<AbstractMesh>;\r\n    /**\r\n     * Gets the vertex data\r\n     * @param kind The type of vertex data\r\n     * @returns A nullable array of numbers, or a float32 array\r\n     */\r\n    getVerticesData(kind: string): Nullable<Array<number> | Float32Array>;\r\n    /**\r\n     * Gets the indices from the mesh\r\n     * @returns A nullable array of index arrays\r\n     */\r\n    getIndices?(): Nullable<IndicesArray>;\r\n    /**\r\n     * Gets the scene from the mesh\r\n     * @returns the indices array or null\r\n     */\r\n    getScene?(): Scene;\r\n    /**\r\n     * Gets the absolute position from the mesh\r\n     * @returns the absolute position\r\n     */\r\n    getAbsolutePosition(): Vector3;\r\n    /**\r\n     * Gets the absolute pivot point from the mesh\r\n     * @returns the absolute pivot point\r\n     */\r\n    getAbsolutePivotPoint(): Vector3;\r\n    /**\r\n     * Rotates the mesh\r\n     * @param axis The axis of rotation\r\n     * @param amount The amount of rotation\r\n     * @param space The space of the rotation\r\n     * @returns The rotation transform node\r\n     */\r\n    rotate(axis: Vector3, amount: number, space?: Space): TransformNode;\r\n    /**\r\n     * Translates the mesh\r\n     * @param axis The axis of translation\r\n     * @param distance The distance of translation\r\n     * @param space The space of the translation\r\n     * @returns The transform node\r\n     */\r\n    translate(axis: Vector3, distance: number, space?: Space): TransformNode;\r\n    /**\r\n     * Sets the absolute position of the mesh\r\n     * @param absolutePosition The absolute position of the mesh\r\n     * @returns The transform node\r\n     */\r\n    setAbsolutePosition(absolutePosition: Vector3): TransformNode;\r\n    /**\r\n     * Gets the class name of the mesh\r\n     * @returns The class name\r\n     */\r\n    getClassName(): string;\r\n}\r\n\r\nMesh._PhysicsImpostorParser = function (scene: Scene, physicObject: IPhysicsEnabledObject, jsonObject: any): PhysicsImpostor {\r\n    return new PhysicsImpostor(\r\n        physicObject,\r\n        jsonObject.physicsImpostor,\r\n        {\r\n            mass: jsonObject.physicsMass,\r\n            friction: jsonObject.physicsFriction,\r\n            restitution: jsonObject.physicsRestitution,\r\n        },\r\n        scene\r\n    );\r\n};\r\n\r\n/**\r\n * Represents a physics imposter\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport class PhysicsImpostor {\r\n    /**\r\n     * The default object size of the imposter\r\n     */\r\n    public static DEFAULT_OBJECT_SIZE: Vector3 = new Vector3(1, 1, 1);\r\n\r\n    /**\r\n     * The identity quaternion of the imposter\r\n     */\r\n    public static IDENTITY_QUATERNION = Quaternion.Identity();\r\n\r\n    /** @internal */\r\n    public _pluginData: any = {};\r\n\r\n    private _physicsEngine: Nullable<PhysicsEngineV1>;\r\n    //The native cannon/oimo/energy physics body object.\r\n    private _physicsBody: any;\r\n    private _bodyUpdateRequired: boolean = false;\r\n\r\n    private _onBeforePhysicsStepCallbacks = new Array<(impostor: PhysicsImpostor) => void>();\r\n    private _onAfterPhysicsStepCallbacks = new Array<(impostor: PhysicsImpostor) => void>();\r\n    /** @internal */\r\n    public _onPhysicsCollideCallbacks: Array<{\r\n        callback: (collider: PhysicsImpostor, collidedAgainst: PhysicsImpostor, point: Nullable<Vector3>, distance: number, impulse: number, normal: Nullable<Vector3>) => void;\r\n        otherImpostors: Array<PhysicsImpostor>;\r\n    }> = [];\r\n\r\n    private _deltaPosition: Vector3 = Vector3.Zero();\r\n    private _deltaRotation: Quaternion;\r\n    private _deltaRotationConjugated: Quaternion;\r\n\r\n    /** @internal */\r\n    public _isFromLine: boolean;\r\n\r\n    //If set, this is this impostor's parent\r\n    private _parent: Nullable<PhysicsImpostor>;\r\n\r\n    private _isDisposed = false;\r\n\r\n    private static _TmpVecs: Vector3[] = ArrayTools.BuildArray(3, Vector3.Zero);\r\n    private static _TmpQuat: Quaternion = Quaternion.Identity();\r\n\r\n    /**\r\n     * Specifies if the physics imposter is disposed\r\n     */\r\n    get isDisposed(): boolean {\r\n        return this._isDisposed;\r\n    }\r\n\r\n    /**\r\n     * Gets the mass of the physics imposter\r\n     */\r\n    get mass(): number {\r\n        return this._physicsEngine ? this._physicsEngine.getPhysicsPlugin().getBodyMass(this) : 0;\r\n    }\r\n\r\n    set mass(value: number) {\r\n        this.setMass(value);\r\n    }\r\n\r\n    /**\r\n     * Gets the coefficient of friction\r\n     */\r\n    get friction(): number {\r\n        return this._physicsEngine ? this._physicsEngine.getPhysicsPlugin().getBodyFriction(this) : 0;\r\n    }\r\n\r\n    /**\r\n     * Sets the coefficient of friction\r\n     */\r\n    set friction(value: number) {\r\n        if (!this._physicsEngine) {\r\n            return;\r\n        }\r\n        this._physicsEngine.getPhysicsPlugin().setBodyFriction(this, value);\r\n    }\r\n\r\n    /**\r\n     * Gets the coefficient of restitution\r\n     */\r\n    get restitution(): number {\r\n        return this._physicsEngine ? this._physicsEngine.getPhysicsPlugin().getBodyRestitution(this) : 0;\r\n    }\r\n\r\n    /**\r\n     * Sets the coefficient of restitution\r\n     */\r\n    set restitution(value: number) {\r\n        if (!this._physicsEngine) {\r\n            return;\r\n        }\r\n        this._physicsEngine.getPhysicsPlugin().setBodyRestitution(this, value);\r\n    }\r\n\r\n    /**\r\n     * Gets the pressure of a soft body; only supported by the AmmoJSPlugin\r\n     */\r\n    get pressure(): number {\r\n        if (!this._physicsEngine) {\r\n            return 0;\r\n        }\r\n        const plugin = this._physicsEngine.getPhysicsPlugin();\r\n        if (!plugin.setBodyPressure) {\r\n            return 0;\r\n        }\r\n        return plugin.getBodyPressure!(this);\r\n    }\r\n\r\n    /**\r\n     * Sets the pressure of a soft body; only supported by the AmmoJSPlugin\r\n     */\r\n    set pressure(value: number) {\r\n        if (!this._physicsEngine) {\r\n            return;\r\n        }\r\n        const plugin = this._physicsEngine.getPhysicsPlugin();\r\n        if (!plugin.setBodyPressure) {\r\n            return;\r\n        }\r\n        plugin.setBodyPressure!(this, value);\r\n    }\r\n\r\n    /**\r\n     * Gets the stiffness of a soft body; only supported by the AmmoJSPlugin\r\n     */\r\n    get stiffness(): number {\r\n        if (!this._physicsEngine) {\r\n            return 0;\r\n        }\r\n        const plugin = this._physicsEngine.getPhysicsPlugin();\r\n        if (!plugin.getBodyStiffness) {\r\n            return 0;\r\n        }\r\n        return plugin.getBodyStiffness!(this);\r\n    }\r\n\r\n    /**\r\n     * Sets the stiffness of a soft body; only supported by the AmmoJSPlugin\r\n     */\r\n    set stiffness(value: number) {\r\n        if (!this._physicsEngine) {\r\n            return;\r\n        }\r\n        const plugin = this._physicsEngine.getPhysicsPlugin();\r\n        if (!plugin.setBodyStiffness) {\r\n            return;\r\n        }\r\n        plugin.setBodyStiffness!(this, value);\r\n    }\r\n\r\n    /**\r\n     * Gets the velocityIterations of a soft body; only supported by the AmmoJSPlugin\r\n     */\r\n    get velocityIterations(): number {\r\n        if (!this._physicsEngine) {\r\n            return 0;\r\n        }\r\n        const plugin = this._physicsEngine.getPhysicsPlugin();\r\n        if (!plugin.getBodyVelocityIterations) {\r\n            return 0;\r\n        }\r\n        return plugin.getBodyVelocityIterations!(this);\r\n    }\r\n\r\n    /**\r\n     * Sets the velocityIterations of a soft body; only supported by the AmmoJSPlugin\r\n     */\r\n    set velocityIterations(value: number) {\r\n        if (!this._physicsEngine) {\r\n            return;\r\n        }\r\n        const plugin = this._physicsEngine.getPhysicsPlugin();\r\n        if (!plugin.setBodyVelocityIterations) {\r\n            return;\r\n        }\r\n        plugin.setBodyVelocityIterations!(this, value);\r\n    }\r\n\r\n    /**\r\n     * Gets the positionIterations of a soft body; only supported by the AmmoJSPlugin\r\n     */\r\n    get positionIterations(): number {\r\n        if (!this._physicsEngine) {\r\n            return 0;\r\n        }\r\n        const plugin = this._physicsEngine.getPhysicsPlugin();\r\n        if (!plugin.getBodyPositionIterations) {\r\n            return 0;\r\n        }\r\n        return plugin.getBodyPositionIterations!(this);\r\n    }\r\n\r\n    /**\r\n     * Sets the positionIterations of a soft body; only supported by the AmmoJSPlugin\r\n     */\r\n    set positionIterations(value: number) {\r\n        if (!this._physicsEngine) {\r\n            return;\r\n        }\r\n        const plugin = this._physicsEngine.getPhysicsPlugin();\r\n        if (!plugin.setBodyPositionIterations) {\r\n            return;\r\n        }\r\n        plugin.setBodyPositionIterations!(this, value);\r\n    }\r\n\r\n    /**\r\n     * The unique id of the physics imposter\r\n     * set by the physics engine when adding this impostor to the array\r\n     */\r\n    public uniqueId: number;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public soft: boolean = false;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public segments: number = 0;\r\n\r\n    private _joints: Array<{\r\n        joint: PhysicsJoint;\r\n        otherImpostor: PhysicsImpostor;\r\n    }>;\r\n\r\n    /**\r\n     * Initializes the physics imposter\r\n     * @param object The physics-enabled object used as the physics imposter\r\n     * @param type The type of the physics imposter. Types are available as static members of this class.\r\n     * @param _options The options for the physics imposter\r\n     * @param _scene The Babylon scene\r\n     */\r\n    constructor(\r\n        /**\r\n         * The physics-enabled object used as the physics imposter\r\n         */\r\n        public object: IPhysicsEnabledObject,\r\n        /**\r\n         * The type of the physics imposter\r\n         */\r\n        public type: number,\r\n        private _options: PhysicsImpostorParameters = { mass: 0 },\r\n        private _scene?: Scene\r\n    ) {\r\n        //sanity check!\r\n        if (!this.object) {\r\n            Logger.Error(\"No object was provided. A physics object is obligatory\");\r\n            return;\r\n        }\r\n        if (this.object.parent && _options.mass !== 0) {\r\n            Logger.Warn(\"A physics impostor has been created for an object which has a parent. Babylon physics currently works in local space so unexpected issues may occur.\");\r\n        }\r\n\r\n        // Legacy support for old syntax.\r\n        if (!this._scene && object.getScene) {\r\n            this._scene = object.getScene();\r\n        }\r\n\r\n        if (!this._scene) {\r\n            return;\r\n        }\r\n\r\n        if (this.type > 100) {\r\n            this.soft = true;\r\n        }\r\n\r\n        this._physicsEngine = this._scene.getPhysicsEngine() as any;\r\n        if (!this._physicsEngine) {\r\n            Logger.Error(\"Physics not enabled. Please use scene.enablePhysics(...) before creating impostors.\");\r\n        } else {\r\n            //set the object's quaternion, if not set\r\n            if (!this.object.rotationQuaternion) {\r\n                if (this.object.rotation) {\r\n                    this.object.rotationQuaternion = Quaternion.RotationYawPitchRoll(this.object.rotation.y, this.object.rotation.x, this.object.rotation.z);\r\n                } else {\r\n                    this.object.rotationQuaternion = new Quaternion();\r\n                }\r\n            }\r\n            //default options params\r\n            this._options.mass = _options.mass === void 0 ? 0 : _options.mass;\r\n            this._options.friction = _options.friction === void 0 ? 0.2 : _options.friction;\r\n            this._options.restitution = _options.restitution === void 0 ? 0.2 : _options.restitution;\r\n            if (this.soft) {\r\n                //softbody mass must be above 0;\r\n                this._options.mass = this._options.mass > 0 ? this._options.mass : 1;\r\n                this._options.pressure = _options.pressure === void 0 ? 200 : _options.pressure;\r\n                this._options.stiffness = _options.stiffness === void 0 ? 1 : _options.stiffness;\r\n                this._options.velocityIterations = _options.velocityIterations === void 0 ? 20 : _options.velocityIterations;\r\n                this._options.positionIterations = _options.positionIterations === void 0 ? 20 : _options.positionIterations;\r\n                this._options.fixedPoints = _options.fixedPoints === void 0 ? 0 : _options.fixedPoints;\r\n                this._options.margin = _options.margin === void 0 ? 0 : _options.margin;\r\n                this._options.damping = _options.damping === void 0 ? 0 : _options.damping;\r\n                this._options.path = _options.path === void 0 ? null : _options.path;\r\n                this._options.shape = _options.shape === void 0 ? null : _options.shape;\r\n            }\r\n            this._joints = [];\r\n            //If the mesh has a parent, don't initialize the physicsBody. Instead wait for the parent to do that.\r\n            if (!this.object.parent || this._options.ignoreParent) {\r\n                this._init();\r\n            } else if (this.object.parent.physicsImpostor) {\r\n                Logger.Warn(\"You must affect impostors to children before affecting impostor to parent.\");\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * This function will completely initialize this impostor.\r\n     * It will create a new body - but only if this mesh has no parent.\r\n     * If it has, this impostor will not be used other than to define the impostor\r\n     * of the child mesh.\r\n     * @internal\r\n     */\r\n    public _init() {\r\n        if (!this._physicsEngine) {\r\n            return;\r\n        }\r\n\r\n        this._physicsEngine.removeImpostor(this);\r\n        this.physicsBody = null;\r\n        this._parent = this._parent || this._getPhysicsParent();\r\n        if (!this._isDisposed && (!this.parent || this._options.ignoreParent)) {\r\n            this._physicsEngine.addImpostor(this);\r\n        }\r\n    }\r\n\r\n    private _getPhysicsParent(): Nullable<PhysicsImpostor> {\r\n        if (this.object.parent instanceof AbstractMesh) {\r\n            const parentMesh: AbstractMesh = <AbstractMesh>this.object.parent;\r\n            return parentMesh.physicsImpostor;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Should a new body be generated.\r\n     * @returns boolean specifying if body initialization is required\r\n     */\r\n    public isBodyInitRequired(): boolean {\r\n        return this._bodyUpdateRequired || (!this._physicsBody && (!this._parent || !!this._options.ignoreParent));\r\n    }\r\n\r\n    /**\r\n     * Sets the updated scaling\r\n     */\r\n    public setScalingUpdated() {\r\n        this.forceUpdate();\r\n    }\r\n\r\n    /**\r\n     * Force a regeneration of this or the parent's impostor's body.\r\n     * Use with caution - This will remove all previously-instantiated joints.\r\n     */\r\n    public forceUpdate() {\r\n        this._init();\r\n        if (this.parent && !this._options.ignoreParent) {\r\n            this.parent.forceUpdate();\r\n        }\r\n    }\r\n\r\n    /*public get mesh(): AbstractMesh {\r\n        return this._mesh;\r\n    }*/\r\n\r\n    /**\r\n     * Gets the body that holds this impostor. Either its own, or its parent.\r\n     */\r\n    public get physicsBody(): any {\r\n        return this._parent && !this._options.ignoreParent ? this._parent.physicsBody : this._physicsBody;\r\n    }\r\n\r\n    /**\r\n     * Get the parent of the physics imposter\r\n     * @returns Physics imposter or null\r\n     */\r\n    public get parent(): Nullable<PhysicsImpostor> {\r\n        return !this._options.ignoreParent && this._parent ? this._parent : null;\r\n    }\r\n\r\n    /**\r\n     * Sets the parent of the physics imposter\r\n     */\r\n    public set parent(value: Nullable<PhysicsImpostor>) {\r\n        this._parent = value;\r\n    }\r\n\r\n    /**\r\n     * Set the physics body. Used mainly by the physics engine/plugin\r\n     */\r\n    public set physicsBody(physicsBody: any) {\r\n        if (this._physicsBody && this._physicsEngine) {\r\n            this._physicsEngine.getPhysicsPlugin().removePhysicsBody(this);\r\n        }\r\n        this._physicsBody = physicsBody;\r\n        this.resetUpdateFlags();\r\n    }\r\n\r\n    /**\r\n     * Resets the update flags\r\n     */\r\n    public resetUpdateFlags() {\r\n        this._bodyUpdateRequired = false;\r\n    }\r\n\r\n    /**\r\n     * Gets the object extents\r\n     * @returns the object extents\r\n     */\r\n    public getObjectExtents(): Vector3 {\r\n        if (this.object.getBoundingInfo) {\r\n            const q = this.object.rotationQuaternion;\r\n            const scaling = this.object.scaling.clone();\r\n            //reset rotation\r\n            this.object.rotationQuaternion = PhysicsImpostor.IDENTITY_QUATERNION;\r\n            //calculate the world matrix with no rotation\r\n            const worldMatrix = this.object.computeWorldMatrix && this.object.computeWorldMatrix(true);\r\n            if (worldMatrix) {\r\n                worldMatrix.decompose(scaling, undefined, undefined);\r\n            }\r\n            const boundingInfo = this.object.getBoundingInfo();\r\n            // get the global scaling of the object\r\n            const size = boundingInfo.boundingBox.extendSize.scale(2).multiplyInPlace(scaling);\r\n            size.x = Math.abs(size.x);\r\n            size.y = Math.abs(size.y);\r\n            size.z = Math.abs(size.z);\r\n            //bring back the rotation\r\n            this.object.rotationQuaternion = q;\r\n            //calculate the world matrix with the new rotation\r\n            this.object.computeWorldMatrix && this.object.computeWorldMatrix(true);\r\n            return size;\r\n        } else {\r\n            return PhysicsImpostor.DEFAULT_OBJECT_SIZE;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the object center\r\n     * @returns The object center\r\n     */\r\n    public getObjectCenter(): Vector3 {\r\n        if (this.object.getBoundingInfo) {\r\n            const boundingInfo = this.object.getBoundingInfo();\r\n            return boundingInfo.boundingBox.centerWorld;\r\n        } else {\r\n            return this.object.position;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get a specific parameter from the options parameters\r\n     * @param paramName The object parameter name\r\n     * @returns The object parameter\r\n     */\r\n    public getParam(paramName: string): any {\r\n        return (<any>this._options)[paramName];\r\n    }\r\n\r\n    /**\r\n     * Sets a specific parameter in the options given to the physics plugin\r\n     * @param paramName The parameter name\r\n     * @param value The value of the parameter\r\n     */\r\n    public setParam(paramName: string, value: number) {\r\n        (<any>this._options)[paramName] = value;\r\n        this._bodyUpdateRequired = true;\r\n    }\r\n\r\n    /**\r\n     * Specifically change the body's mass. Won't recreate the physics body object\r\n     * @param mass The mass of the physics imposter\r\n     */\r\n    public setMass(mass: number) {\r\n        if (this.getParam(\"mass\") !== mass) {\r\n            this.setParam(\"mass\", mass);\r\n        }\r\n        if (this._physicsEngine) {\r\n            this._physicsEngine.getPhysicsPlugin().setBodyMass(this, mass);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the linear velocity\r\n     * @returns  linear velocity or null\r\n     */\r\n    public getLinearVelocity(): Nullable<Vector3> {\r\n        return this._physicsEngine ? this._physicsEngine.getPhysicsPlugin().getLinearVelocity(this) : Vector3.Zero();\r\n    }\r\n\r\n    /**\r\n     * Sets the linear velocity\r\n     * @param velocity  linear velocity or null\r\n     */\r\n    public setLinearVelocity(velocity: Nullable<Vector3>) {\r\n        if (this._physicsEngine) {\r\n            this._physicsEngine.getPhysicsPlugin().setLinearVelocity(this, velocity);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the angular velocity\r\n     * @returns angular velocity or null\r\n     */\r\n    public getAngularVelocity(): Nullable<Vector3> {\r\n        return this._physicsEngine ? this._physicsEngine.getPhysicsPlugin().getAngularVelocity(this) : Vector3.Zero();\r\n    }\r\n\r\n    /**\r\n     * Sets the angular velocity\r\n     * @param velocity The velocity or null\r\n     */\r\n    public setAngularVelocity(velocity: Nullable<Vector3>) {\r\n        if (this._physicsEngine) {\r\n            this._physicsEngine.getPhysicsPlugin().setAngularVelocity(this, velocity);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Execute a function with the physics plugin native code\r\n     * Provide a function the will have two variables - the world object and the physics body object\r\n     * @param func The function to execute with the physics plugin native code\r\n     */\r\n    public executeNativeFunction(func: (world: any, physicsBody: any) => void) {\r\n        if (this._physicsEngine) {\r\n            func(this._physicsEngine.getPhysicsPlugin().world, this.physicsBody);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Register a function that will be executed before the physics world is stepping forward\r\n     * @param func The function to execute before the physics world is stepped forward\r\n     */\r\n    public registerBeforePhysicsStep(func: (impostor: PhysicsImpostor) => void): void {\r\n        this._onBeforePhysicsStepCallbacks.push(func);\r\n    }\r\n\r\n    /**\r\n     * Unregister a function that will be executed before the physics world is stepping forward\r\n     * @param func The function to execute before the physics world is stepped forward\r\n     */\r\n    public unregisterBeforePhysicsStep(func: (impostor: PhysicsImpostor) => void): void {\r\n        const index = this._onBeforePhysicsStepCallbacks.indexOf(func);\r\n\r\n        if (index > -1) {\r\n            this._onBeforePhysicsStepCallbacks.splice(index, 1);\r\n        } else {\r\n            Logger.Warn(\"Function to remove was not found\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Register a function that will be executed after the physics step\r\n     * @param func The function to execute after physics step\r\n     */\r\n    public registerAfterPhysicsStep(func: (impostor: PhysicsImpostor) => void): void {\r\n        this._onAfterPhysicsStepCallbacks.push(func);\r\n    }\r\n\r\n    /**\r\n     * Unregisters a function that will be executed after the physics step\r\n     * @param func The function to execute after physics step\r\n     */\r\n    public unregisterAfterPhysicsStep(func: (impostor: PhysicsImpostor) => void): void {\r\n        const index = this._onAfterPhysicsStepCallbacks.indexOf(func);\r\n\r\n        if (index > -1) {\r\n            this._onAfterPhysicsStepCallbacks.splice(index, 1);\r\n        } else {\r\n            Logger.Warn(\"Function to remove was not found\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * register a function that will be executed when this impostor collides against a different body\r\n     * @param collideAgainst Physics imposter, or array of physics imposters to collide against\r\n     * @param func Callback that is executed on collision\r\n     */\r\n    public registerOnPhysicsCollide(\r\n        collideAgainst: PhysicsImpostor | Array<PhysicsImpostor>,\r\n        func: (collider: PhysicsImpostor, collidedAgainst: PhysicsImpostor, point: Nullable<Vector3>) => void\r\n    ): void {\r\n        const collidedAgainstList: Array<PhysicsImpostor> = collideAgainst instanceof Array ? <Array<PhysicsImpostor>>collideAgainst : [<PhysicsImpostor>collideAgainst];\r\n        this._onPhysicsCollideCallbacks.push({ callback: func, otherImpostors: collidedAgainstList });\r\n    }\r\n\r\n    /**\r\n     * Unregisters the physics imposter's collision callback\r\n     * @param collideAgainst The physics object to collide against\r\n     * @param func Callback to execute on collision\r\n     */\r\n    public unregisterOnPhysicsCollide(\r\n        collideAgainst: PhysicsImpostor | Array<PhysicsImpostor>,\r\n        func: (collider: PhysicsImpostor, collidedAgainst: PhysicsImpostor | Array<PhysicsImpostor>, point: Nullable<Vector3>) => void\r\n    ): void {\r\n        const collidedAgainstList: Array<PhysicsImpostor> = collideAgainst instanceof Array ? <Array<PhysicsImpostor>>collideAgainst : [<PhysicsImpostor>collideAgainst];\r\n        let index = -1;\r\n        const found = this._onPhysicsCollideCallbacks.some((cbDef, idx) => {\r\n            if (cbDef.callback === func && cbDef.otherImpostors.length === collidedAgainstList.length) {\r\n                // chcek the arrays match\r\n                const sameList = cbDef.otherImpostors.every((impostor) => {\r\n                    return collidedAgainstList.indexOf(impostor) > -1;\r\n                });\r\n                if (sameList) {\r\n                    index = idx;\r\n                }\r\n                return sameList;\r\n            }\r\n            return false;\r\n        });\r\n\r\n        if (found) {\r\n            this._onPhysicsCollideCallbacks.splice(index, 1);\r\n        } else {\r\n            Logger.Warn(\"Function to remove was not found\");\r\n        }\r\n    }\r\n\r\n    //temp variables for parent rotation calculations\r\n    //private _mats: Array<Matrix> = [new Matrix(), new Matrix()];\r\n    private _tmpQuat: Quaternion = new Quaternion();\r\n    private _tmpQuat2: Quaternion = new Quaternion();\r\n\r\n    /**\r\n     * Get the parent rotation\r\n     * @returns The parent rotation\r\n     */\r\n    public getParentsRotation(): Quaternion {\r\n        let parent = this.object.parent;\r\n        this._tmpQuat.copyFromFloats(0, 0, 0, 1);\r\n        while (parent) {\r\n            if (parent.rotationQuaternion) {\r\n                this._tmpQuat2.copyFrom(parent.rotationQuaternion);\r\n            } else {\r\n                Quaternion.RotationYawPitchRollToRef(parent.rotation.y, parent.rotation.x, parent.rotation.z, this._tmpQuat2);\r\n            }\r\n            this._tmpQuat.multiplyToRef(this._tmpQuat2, this._tmpQuat);\r\n            parent = parent.parent;\r\n        }\r\n        return this._tmpQuat;\r\n    }\r\n\r\n    /**\r\n     * this function is executed by the physics engine.\r\n     */\r\n    public beforeStep = () => {\r\n        if (!this._physicsEngine) {\r\n            return;\r\n        }\r\n\r\n        this.object.translate(this._deltaPosition, -1);\r\n        this._deltaRotationConjugated &&\r\n            this.object.rotationQuaternion &&\r\n            this.object.rotationQuaternion.multiplyToRef(this._deltaRotationConjugated, this.object.rotationQuaternion);\r\n        this.object.computeWorldMatrix(false);\r\n        if (this.object.parent && this.object.rotationQuaternion) {\r\n            this.getParentsRotation();\r\n            this._tmpQuat.multiplyToRef(this.object.rotationQuaternion, this._tmpQuat);\r\n        } else {\r\n            this._tmpQuat.copyFrom(this.object.rotationQuaternion || new Quaternion());\r\n        }\r\n        if (!this._options.disableBidirectionalTransformation) {\r\n            this.object.rotationQuaternion &&\r\n                this._physicsEngine.getPhysicsPlugin().setPhysicsBodyTransformation(this, /*bInfo.boundingBox.centerWorld*/ this.object.getAbsolutePosition(), this._tmpQuat);\r\n        }\r\n\r\n        this._onBeforePhysicsStepCallbacks.forEach((func) => {\r\n            func(this);\r\n        });\r\n    };\r\n\r\n    /**\r\n     * this function is executed by the physics engine\r\n     */\r\n    public afterStep = () => {\r\n        if (!this._physicsEngine) {\r\n            return;\r\n        }\r\n\r\n        this._onAfterPhysicsStepCallbacks.forEach((func) => {\r\n            func(this);\r\n        });\r\n\r\n        this._physicsEngine.getPhysicsPlugin().setTransformationFromPhysicsBody(this);\r\n        // object has now its world rotation. needs to be converted to local.\r\n        if (this.object.parent && this.object.rotationQuaternion) {\r\n            this.getParentsRotation();\r\n            this._tmpQuat.conjugateInPlace();\r\n            this._tmpQuat.multiplyToRef(this.object.rotationQuaternion, this.object.rotationQuaternion);\r\n        }\r\n        // take the position set and make it the absolute position of this object.\r\n        this.object.setAbsolutePosition(this.object.position);\r\n        if (this._deltaRotation) {\r\n            this.object.rotationQuaternion && this.object.rotationQuaternion.multiplyToRef(this._deltaRotation, this.object.rotationQuaternion);\r\n            this._deltaPosition.applyRotationQuaternionToRef(this._deltaRotation, PhysicsImpostor._TmpVecs[0]);\r\n            this.object.translate(PhysicsImpostor._TmpVecs[0], 1);\r\n        } else {\r\n            this.object.translate(this._deltaPosition, 1);\r\n        }\r\n        this.object.computeWorldMatrix(true);\r\n    };\r\n\r\n    /**\r\n     * Legacy collision detection event support\r\n     */\r\n    public onCollideEvent: Nullable<(collider: PhysicsImpostor, collidedWith: PhysicsImpostor) => void> = null;\r\n\r\n    /**\r\n     *  define an onCollide function to call when this impostor collides against a different body\r\n     * @param e collide event data\r\n     */\r\n    public onCollide = (e: { body: any; point: Nullable<Vector3>; distance: number; impulse: number; normal: Nullable<Vector3> }) => {\r\n        if (!this._onPhysicsCollideCallbacks.length && !this.onCollideEvent) {\r\n            return;\r\n        }\r\n\r\n        if (!this._physicsEngine) {\r\n            return;\r\n        }\r\n        const otherImpostor = this._physicsEngine.getImpostorWithPhysicsBody(e.body);\r\n        if (otherImpostor) {\r\n            // Legacy collision detection event support\r\n            if (this.onCollideEvent) {\r\n                this.onCollideEvent(this, otherImpostor);\r\n            }\r\n            this._onPhysicsCollideCallbacks\r\n                .filter((obj) => {\r\n                    return obj.otherImpostors.indexOf(<PhysicsImpostor>otherImpostor) !== -1;\r\n                })\r\n                .forEach((obj) => {\r\n                    obj.callback(this, <PhysicsImpostor>otherImpostor, e.point, e.distance, e.impulse, e.normal);\r\n                });\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Apply a force\r\n     * @param force The force to apply\r\n     * @param contactPoint The contact point for the force\r\n     * @returns The physics imposter\r\n     */\r\n    public applyForce(force: Vector3, contactPoint: Vector3): PhysicsImpostor {\r\n        if (this._physicsEngine) {\r\n            this._physicsEngine.getPhysicsPlugin().applyForce(this, force, contactPoint);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Apply an impulse\r\n     * @param force The impulse force\r\n     * @param contactPoint The contact point for the impulse force\r\n     * @returns The physics imposter\r\n     */\r\n    public applyImpulse(force: Vector3, contactPoint: Vector3): PhysicsImpostor {\r\n        if (this._physicsEngine) {\r\n            this._physicsEngine.getPhysicsPlugin().applyImpulse(this, force, contactPoint);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * A help function to create a joint\r\n     * @param otherImpostor A physics imposter used to create a joint\r\n     * @param jointType The type of joint\r\n     * @param jointData The data for the joint\r\n     * @returns The physics imposter\r\n     */\r\n    public createJoint(otherImpostor: PhysicsImpostor, jointType: number, jointData: PhysicsJointData): PhysicsImpostor {\r\n        const joint = new PhysicsJoint(jointType, jointData);\r\n        this.addJoint(otherImpostor, joint);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add a joint to this impostor with a different impostor\r\n     * @param otherImpostor A physics imposter used to add a joint\r\n     * @param joint The joint to add\r\n     * @returns The physics imposter\r\n     */\r\n    public addJoint(otherImpostor: PhysicsImpostor, joint: PhysicsJoint): PhysicsImpostor {\r\n        this._joints.push({\r\n            otherImpostor: otherImpostor,\r\n            joint: joint,\r\n        });\r\n\r\n        if (this._physicsEngine) {\r\n            this._physicsEngine.addJoint(this, otherImpostor, joint);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add an anchor to a cloth impostor\r\n     * @param otherImpostor rigid impostor to anchor to\r\n     * @param width ratio across width from 0 to 1\r\n     * @param height ratio up height from 0 to 1\r\n     * @param influence the elasticity between cloth impostor and anchor from 0, very stretchy to 1, little stretch\r\n     * @param noCollisionBetweenLinkedBodies when true collisions between cloth impostor and anchor are ignored; default false\r\n     * @returns impostor the soft imposter\r\n     */\r\n    public addAnchor(otherImpostor: PhysicsImpostor, width: number, height: number, influence: number, noCollisionBetweenLinkedBodies: boolean): PhysicsImpostor {\r\n        if (!this._physicsEngine) {\r\n            return this;\r\n        }\r\n        const plugin = this._physicsEngine.getPhysicsPlugin();\r\n        if (!plugin.appendAnchor) {\r\n            return this;\r\n        }\r\n        if (this._physicsEngine) {\r\n            plugin.appendAnchor!(this, otherImpostor, width, height, influence, noCollisionBetweenLinkedBodies);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add a hook to a rope impostor\r\n     * @param otherImpostor rigid impostor to anchor to\r\n     * @param length ratio across rope from 0 to 1\r\n     * @param influence the elasticity between rope impostor and anchor from 0, very stretchy to 1, little stretch\r\n     * @param noCollisionBetweenLinkedBodies when true collisions between soft impostor and anchor are ignored; default false\r\n     * @returns impostor the rope imposter\r\n     */\r\n    public addHook(otherImpostor: PhysicsImpostor, length: number, influence: number, noCollisionBetweenLinkedBodies: boolean): PhysicsImpostor {\r\n        if (!this._physicsEngine) {\r\n            return this;\r\n        }\r\n        const plugin = this._physicsEngine.getPhysicsPlugin();\r\n        if (!plugin.appendAnchor) {\r\n            return this;\r\n        }\r\n        if (this._physicsEngine) {\r\n            plugin.appendHook!(this, otherImpostor, length, influence, noCollisionBetweenLinkedBodies);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Will keep this body still, in a sleep mode.\r\n     * @returns the physics imposter\r\n     */\r\n    public sleep(): PhysicsImpostor {\r\n        if (this._physicsEngine) {\r\n            this._physicsEngine.getPhysicsPlugin().sleepBody(this);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Wake the body up.\r\n     * @returns The physics imposter\r\n     */\r\n    public wakeUp(): PhysicsImpostor {\r\n        if (this._physicsEngine) {\r\n            this._physicsEngine.getPhysicsPlugin().wakeUpBody(this);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Clones the physics imposter\r\n     * @param newObject The physics imposter clones to this physics-enabled object\r\n     * @returns A nullable physics imposter\r\n     */\r\n    public clone(newObject: IPhysicsEnabledObject): Nullable<PhysicsImpostor> {\r\n        if (!newObject) {\r\n            return null;\r\n        }\r\n        return new PhysicsImpostor(newObject, this.type, this._options, this._scene);\r\n    }\r\n\r\n    /**\r\n     * Disposes the physics imposter\r\n     */\r\n    public dispose(/*disposeChildren: boolean = true*/) {\r\n        //no dispose if no physics engine is available.\r\n        if (!this._physicsEngine) {\r\n            return;\r\n        }\r\n\r\n        this._joints.forEach((j) => {\r\n            if (this._physicsEngine) {\r\n                this._physicsEngine.removeJoint(this, j.otherImpostor, j.joint);\r\n            }\r\n        });\r\n        //dispose the physics body\r\n        this._physicsEngine.removeImpostor(this);\r\n        if (this.parent) {\r\n            this.parent.forceUpdate();\r\n        } else {\r\n            /*this._object.getChildMeshes().forEach(function(mesh) {\r\n                if (mesh.physicsImpostor) {\r\n                    if (disposeChildren) {\r\n                        mesh.physicsImpostor.dispose();\r\n                        mesh.physicsImpostor = null;\r\n                    }\r\n                }\r\n            })*/\r\n        }\r\n\r\n        this._isDisposed = true;\r\n    }\r\n\r\n    /**\r\n     * Sets the delta position\r\n     * @param position The delta position amount\r\n     */\r\n    public setDeltaPosition(position: Vector3) {\r\n        this._deltaPosition.copyFrom(position);\r\n    }\r\n\r\n    /**\r\n     * Sets the delta rotation\r\n     * @param rotation The delta rotation amount\r\n     */\r\n    public setDeltaRotation(rotation: Quaternion) {\r\n        if (!this._deltaRotation) {\r\n            this._deltaRotation = new Quaternion();\r\n        }\r\n        this._deltaRotation.copyFrom(rotation);\r\n        this._deltaRotationConjugated = this._deltaRotation.conjugate();\r\n    }\r\n\r\n    /**\r\n     * Gets the box size of the physics imposter and stores the result in the input parameter\r\n     * @param result Stores the box size\r\n     * @returns The physics imposter\r\n     */\r\n    public getBoxSizeToRef(result: Vector3): PhysicsImpostor {\r\n        if (this._physicsEngine) {\r\n            this._physicsEngine.getPhysicsPlugin().getBoxSizeToRef(this, result);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets the radius of the physics imposter\r\n     * @returns Radius of the physics imposter\r\n     */\r\n    public getRadius(): number {\r\n        return this._physicsEngine ? this._physicsEngine.getPhysicsPlugin().getRadius(this) : 0;\r\n    }\r\n\r\n    /**\r\n     * Sync a bone with this impostor\r\n     * @param bone The bone to sync to the impostor.\r\n     * @param boneMesh The mesh that the bone is influencing.\r\n     * @param jointPivot The pivot of the joint / bone in local space.\r\n     * @param distToJoint Optional distance from the impostor to the joint.\r\n     * @param adjustRotation Optional quaternion for adjusting the local rotation of the bone.\r\n     */\r\n    public syncBoneWithImpostor(bone: Bone, boneMesh: AbstractMesh, jointPivot: Vector3, distToJoint?: number, adjustRotation?: Quaternion) {\r\n        const tempVec = PhysicsImpostor._TmpVecs[0];\r\n        const mesh = <AbstractMesh>this.object;\r\n\r\n        if (mesh.rotationQuaternion) {\r\n            if (adjustRotation) {\r\n                const tempQuat = PhysicsImpostor._TmpQuat;\r\n                mesh.rotationQuaternion.multiplyToRef(adjustRotation, tempQuat);\r\n                bone.setRotationQuaternion(tempQuat, Space.WORLD, boneMesh);\r\n            } else {\r\n                bone.setRotationQuaternion(mesh.rotationQuaternion, Space.WORLD, boneMesh);\r\n            }\r\n        }\r\n\r\n        tempVec.x = 0;\r\n        tempVec.y = 0;\r\n        tempVec.z = 0;\r\n\r\n        if (jointPivot) {\r\n            tempVec.x = jointPivot.x;\r\n            tempVec.y = jointPivot.y;\r\n            tempVec.z = jointPivot.z;\r\n\r\n            bone.getDirectionToRef(tempVec, boneMesh, tempVec);\r\n\r\n            if (distToJoint === undefined || distToJoint === null) {\r\n                distToJoint = jointPivot.length();\r\n            }\r\n\r\n            tempVec.x *= distToJoint;\r\n            tempVec.y *= distToJoint;\r\n            tempVec.z *= distToJoint;\r\n        }\r\n\r\n        if (bone.getParent()) {\r\n            tempVec.addInPlace(mesh.getAbsolutePosition());\r\n            bone.setAbsolutePosition(tempVec, boneMesh);\r\n        } else {\r\n            boneMesh.setAbsolutePosition(mesh.getAbsolutePosition());\r\n            boneMesh.position.x -= tempVec.x;\r\n            boneMesh.position.y -= tempVec.y;\r\n            boneMesh.position.z -= tempVec.z;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sync impostor to a bone\r\n     * @param bone The bone that the impostor will be synced to.\r\n     * @param boneMesh The mesh that the bone is influencing.\r\n     * @param jointPivot The pivot of the joint / bone in local space.\r\n     * @param distToJoint Optional distance from the impostor to the joint.\r\n     * @param adjustRotation Optional quaternion for adjusting the local rotation of the bone.\r\n     * @param boneAxis Optional vector3 axis the bone is aligned with\r\n     */\r\n    public syncImpostorWithBone(bone: Bone, boneMesh: AbstractMesh, jointPivot: Vector3, distToJoint?: number, adjustRotation?: Quaternion, boneAxis?: Vector3) {\r\n        const mesh = <AbstractMesh>this.object;\r\n\r\n        if (mesh.rotationQuaternion) {\r\n            if (adjustRotation) {\r\n                const tempQuat = PhysicsImpostor._TmpQuat;\r\n                bone.getRotationQuaternionToRef(Space.WORLD, boneMesh, tempQuat);\r\n                tempQuat.multiplyToRef(adjustRotation, mesh.rotationQuaternion);\r\n            } else {\r\n                bone.getRotationQuaternionToRef(Space.WORLD, boneMesh, mesh.rotationQuaternion);\r\n            }\r\n        }\r\n\r\n        const pos = PhysicsImpostor._TmpVecs[0];\r\n        const boneDir = PhysicsImpostor._TmpVecs[1];\r\n\r\n        if (!boneAxis) {\r\n            boneAxis = PhysicsImpostor._TmpVecs[2];\r\n            boneAxis.x = 0;\r\n            boneAxis.y = 1;\r\n            boneAxis.z = 0;\r\n        }\r\n\r\n        bone.getDirectionToRef(boneAxis, boneMesh, boneDir);\r\n        bone.getAbsolutePositionToRef(boneMesh, pos);\r\n\r\n        if ((distToJoint === undefined || distToJoint === null) && jointPivot) {\r\n            distToJoint = jointPivot.length();\r\n        }\r\n\r\n        if (distToJoint !== undefined && distToJoint !== null) {\r\n            pos.x += boneDir.x * distToJoint;\r\n            pos.y += boneDir.y * distToJoint;\r\n            pos.z += boneDir.z * distToJoint;\r\n        }\r\n\r\n        mesh.setAbsolutePosition(pos);\r\n    }\r\n\r\n    //Impostor types\r\n    /**\r\n     * No-Imposter type\r\n     */\r\n    public static NoImpostor = 0;\r\n    /**\r\n     * Sphere-Imposter type\r\n     */\r\n    public static SphereImpostor = 1;\r\n    /**\r\n     * Box-Imposter type\r\n     */\r\n    public static BoxImpostor = 2;\r\n    /**\r\n     * Plane-Imposter type\r\n     */\r\n    public static PlaneImpostor = 3;\r\n    /**\r\n     * Mesh-imposter type (Only available to objects with vertices data)\r\n     */\r\n    public static MeshImpostor = 4;\r\n    /**\r\n     * Capsule-Impostor type (Ammo.js plugin only)\r\n     */\r\n    public static CapsuleImpostor = 6;\r\n    /**\r\n     * Cylinder-Imposter type\r\n     */\r\n    public static CylinderImpostor = 7;\r\n    /**\r\n     * Particle-Imposter type\r\n     */\r\n    public static ParticleImpostor = 8;\r\n    /**\r\n     * Heightmap-Imposter type\r\n     */\r\n    public static HeightmapImpostor = 9;\r\n    /**\r\n     * ConvexHull-Impostor type (Ammo.js plugin only)\r\n     */\r\n    public static ConvexHullImpostor = 10;\r\n    /**\r\n     * Custom-Imposter type (Ammo.js plugin only)\r\n     */\r\n    public static CustomImpostor = 100;\r\n    /**\r\n     * Rope-Imposter type\r\n     */\r\n    public static RopeImpostor = 101;\r\n    /**\r\n     * Cloth-Imposter type\r\n     */\r\n    public static ClothImpostor = 102;\r\n    /**\r\n     * Softbody-Imposter type\r\n     */\r\n    public static SoftbodyImpostor = 103;\r\n}\r\n"]}