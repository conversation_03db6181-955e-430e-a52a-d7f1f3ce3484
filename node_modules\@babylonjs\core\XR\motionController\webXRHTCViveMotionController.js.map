{"version": 3, "file": "webXRHTCViveMotionController.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/motionController/webXRHTCViveMotionController.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAGhF,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,4BAA4B,EAAE,MAAM,gCAAgC,CAAC;AAE9E;;GAEG;AACH,MAAM,OAAO,4BAA6B,SAAQ,6BAA6B;IAc3E;;;;;OAKG;IACH,YAAY,KAAY,EAAE,aAA6C,EAAE,UAAsC;QAC3G,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,UAAU,CAAC,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;QAThE,cAAS,GAAG,UAAU,CAAC;IAU9B,CAAC;IAES,mBAAmB;QACzB,MAAM,QAAQ,GAAG,4BAA4B,CAAC,cAAc,CAAC;QAC7D,MAAM,IAAI,GAAG,4BAA4B,CAAC,cAAc,CAAC;QAEzD,OAAO;YACH,QAAQ;YACR,IAAI;SACP,CAAC;IACN,CAAC;IAES,2BAA2B;QACjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB,CAAC,OAAuB;QACjD,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YAClC,MAAM,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,8BAA8B,CAAC,GAAG,CACnC,CAAC,SAAS,EAAE,EAAE;oBACV,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACzC,OAAO;qBACV;oBAED,QAAQ,EAAE,EAAE;wBACR,KAAK,qBAAqB;4BACP,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,CAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC;4BAC1F,OAAO;wBACX,KAAK,sBAAsB;4BACvB,OAAO;wBACX,KAAK,qBAAqB;4BACtB,OAAO;qBACd;gBACL,CAAC,EACD,SAAS,EACT,IAAI,CACP,CAAC;aACL;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAES,YAAY,CAAC,MAAsB;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAE7E,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACpB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC3C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE;YAClC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;SAChF;IACL,CAAC;IAES,YAAY;QAClB,6CAA6C;IACjD,CAAC;;AA7ED;;GAEG;AACW,2CAAc,GAAW,yCAAyC,AAApD,CAAqD;AACjF;;GAEG;AACW,2CAAc,GAAW,cAAc,AAAzB,CAA0B;AAyE1D,uBAAuB;AACvB,4BAA4B,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC,OAAsB,EAAE,KAAY,EAAE,EAAE;IACjG,OAAO,IAAI,4BAA4B,CAAC,KAAK,EAAO,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;AAC7F,CAAC,CAAC,CAAC;AAEH,iHAAiH;AACjH,wGAAwG;AACxG,MAAM;AAEN,MAAM,aAAa,GAA+B;IAC9C,IAAI,EAAE;QACF,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE;YACR,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;YACD,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;YACD,sBAAsB,EAAE;gBACpB,IAAI,EAAE,UAAU;gBAChB,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;iBACX;gBACD,YAAY,EAAE,sBAAsB;gBACpC,eAAe,EAAE,EAAE;aACtB;YACD,IAAI,EAAE;gBACF,IAAI,EAAE,QAAQ;gBACd,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,MAAM;gBACpB,eAAe,EAAE,EAAE;aACtB;SACJ;QACD,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE,eAAe;QAC7B,SAAS,EAAE,UAAU;KACxB;IACD,KAAK,EAAE;QACH,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE;YACR,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;YACD,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;YACD,sBAAsB,EAAE;gBACpB,IAAI,EAAE,UAAU;gBAChB,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;iBACX;gBACD,YAAY,EAAE,sBAAsB;gBACpC,eAAe,EAAE,EAAE;aACtB;YACD,IAAI,EAAE;gBACF,IAAI,EAAE,QAAQ;gBACd,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,MAAM;gBACpB,eAAe,EAAE,EAAE;aACtB;SACJ;QACD,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE,eAAe;QAC7B,SAAS,EAAE,UAAU;KACxB;IACD,IAAI,EAAE;QACF,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE;YACR,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;YACD,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;YACD,sBAAsB,EAAE;gBACpB,IAAI,EAAE,UAAU;gBAChB,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;iBACX;gBACD,YAAY,EAAE,sBAAsB;gBACpC,eAAe,EAAE,EAAE;aACtB;YACD,IAAI,EAAE;gBACF,IAAI,EAAE,QAAQ;gBACd,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,MAAM;gBACpB,eAAe,EAAE,EAAE;aACtB;SACJ;QACD,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE,eAAe;QAC7B,SAAS,EAAE,UAAU;KACxB;CACJ,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { IMotionControllerLayoutMap, IMinimalMotionControllerObject, MotionControllerHandedness } from \"./webXRAbstractMotionController\";\r\nimport { WebXRAbstractMotionController } from \"./webXRAbstractMotionController\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../../Meshes/mesh\";\r\nimport { Quaternion } from \"../../Maths/math.vector\";\r\nimport { WebXRMotionControllerManager } from \"./webXRMotionControllerManager\";\r\n\r\n/**\r\n * The motion controller class for the standard HTC-Vive controllers\r\n */\r\nexport class WebXRHTCViveMotionController extends WebXRAbstractMotionController {\r\n    private _modelRootNode: AbstractMesh;\r\n\r\n    /**\r\n     * The base url used to load the left and right controller models\r\n     */\r\n    public static MODEL_BASE_URL: string = \"https://controllers.babylonjs.com/vive/\";\r\n    /**\r\n     * File name for the controller model.\r\n     */\r\n    public static MODEL_FILENAME: string = \"wand.babylon\";\r\n\r\n    public profileId = \"htc-vive\";\r\n\r\n    /**\r\n     * Create a new Vive motion controller object\r\n     * @param scene the scene to use to create this controller\r\n     * @param gamepadObject the corresponding gamepad object\r\n     * @param handedness the handedness of the controller\r\n     */\r\n    constructor(scene: Scene, gamepadObject: IMinimalMotionControllerObject, handedness: MotionControllerHandedness) {\r\n        super(scene, HTCViveLayout[handedness], gamepadObject, handedness);\r\n    }\r\n\r\n    protected _getFilenameAndPath(): { filename: string; path: string } {\r\n        const filename = WebXRHTCViveMotionController.MODEL_FILENAME;\r\n        const path = WebXRHTCViveMotionController.MODEL_BASE_URL;\r\n\r\n        return {\r\n            filename,\r\n            path,\r\n        };\r\n    }\r\n\r\n    protected _getModelLoadingConstraints(): boolean {\r\n        return true;\r\n    }\r\n\r\n    protected _processLoadedModel(_meshes: AbstractMesh[]): void {\r\n        this.getComponentIds().forEach((id) => {\r\n            const comp = id && this.getComponent(id);\r\n            if (comp) {\r\n                comp.onButtonStateChangedObservable.add(\r\n                    (component) => {\r\n                        if (!this.rootMesh || this.disableAnimation) {\r\n                            return;\r\n                        }\r\n\r\n                        switch (id) {\r\n                            case \"xr-standard-trigger\":\r\n                                (<AbstractMesh>this._modelRootNode.getChildren()[6]).rotation.x = -component.value * 0.15;\r\n                                return;\r\n                            case \"xr-standard-touchpad\":\r\n                                return;\r\n                            case \"xr-standard-squeeze\":\r\n                                return;\r\n                        }\r\n                    },\r\n                    undefined,\r\n                    true\r\n                );\r\n            }\r\n        });\r\n    }\r\n\r\n    protected _setRootMesh(meshes: AbstractMesh[]): void {\r\n        this.rootMesh = new Mesh(this.profileId + \" \" + this.handedness, this.scene);\r\n\r\n        meshes.forEach((mesh) => {\r\n            mesh.isPickable = false;\r\n        });\r\n        this._modelRootNode = meshes[1];\r\n        this._modelRootNode.parent = this.rootMesh;\r\n        if (!this.scene.useRightHandedSystem) {\r\n            this.rootMesh.rotationQuaternion = Quaternion.FromEulerAngles(0, Math.PI, 0);\r\n        }\r\n    }\r\n\r\n    protected _updateModel(): void {\r\n        // no-op. model is updated using observables.\r\n    }\r\n}\r\n\r\n// register the profile\r\nWebXRMotionControllerManager.RegisterController(\"htc-vive\", (xrInput: XRInputSource, scene: Scene) => {\r\n    return new WebXRHTCViveMotionController(scene, <any>xrInput.gamepad, xrInput.handedness);\r\n});\r\n\r\n// WebXRMotionControllerManager.RegisterController(\"htc-vive-legacy\", (xrInput: XRInputSource, scene: Scene) => {\r\n//     return new WebXRHTCViveMotionController(scene, <any>(xrInput.gamepad), xrInput.handedness, true);\r\n// });\r\n\r\nconst HTCViveLayout: IMotionControllerLayoutMap = {\r\n    left: {\r\n        selectComponentId: \"xr-standard-trigger\",\r\n        components: {\r\n            \"xr-standard-trigger\": {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 0,\r\n                },\r\n                rootNodeName: \"xr_standard_trigger\",\r\n                visualResponses: {},\r\n            },\r\n            \"xr-standard-squeeze\": {\r\n                type: \"squeeze\",\r\n                gamepadIndices: {\r\n                    button: 1,\r\n                },\r\n                rootNodeName: \"xr_standard_squeeze\",\r\n                visualResponses: {},\r\n            },\r\n            \"xr-standard-touchpad\": {\r\n                type: \"touchpad\",\r\n                gamepadIndices: {\r\n                    button: 2,\r\n                    xAxis: 0,\r\n                    yAxis: 1,\r\n                },\r\n                rootNodeName: \"xr_standard_touchpad\",\r\n                visualResponses: {},\r\n            },\r\n            menu: {\r\n                type: \"button\",\r\n                gamepadIndices: {\r\n                    button: 4,\r\n                },\r\n                rootNodeName: \"menu\",\r\n                visualResponses: {},\r\n            },\r\n        },\r\n        gamepadMapping: \"xr-standard\",\r\n        rootNodeName: \"htc_vive_none\",\r\n        assetPath: \"none.glb\",\r\n    },\r\n    right: {\r\n        selectComponentId: \"xr-standard-trigger\",\r\n        components: {\r\n            \"xr-standard-trigger\": {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 0,\r\n                },\r\n                rootNodeName: \"xr_standard_trigger\",\r\n                visualResponses: {},\r\n            },\r\n            \"xr-standard-squeeze\": {\r\n                type: \"squeeze\",\r\n                gamepadIndices: {\r\n                    button: 1,\r\n                },\r\n                rootNodeName: \"xr_standard_squeeze\",\r\n                visualResponses: {},\r\n            },\r\n            \"xr-standard-touchpad\": {\r\n                type: \"touchpad\",\r\n                gamepadIndices: {\r\n                    button: 2,\r\n                    xAxis: 0,\r\n                    yAxis: 1,\r\n                },\r\n                rootNodeName: \"xr_standard_touchpad\",\r\n                visualResponses: {},\r\n            },\r\n            menu: {\r\n                type: \"button\",\r\n                gamepadIndices: {\r\n                    button: 4,\r\n                },\r\n                rootNodeName: \"menu\",\r\n                visualResponses: {},\r\n            },\r\n        },\r\n        gamepadMapping: \"xr-standard\",\r\n        rootNodeName: \"htc_vive_none\",\r\n        assetPath: \"none.glb\",\r\n    },\r\n    none: {\r\n        selectComponentId: \"xr-standard-trigger\",\r\n        components: {\r\n            \"xr-standard-trigger\": {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 0,\r\n                },\r\n                rootNodeName: \"xr_standard_trigger\",\r\n                visualResponses: {},\r\n            },\r\n            \"xr-standard-squeeze\": {\r\n                type: \"squeeze\",\r\n                gamepadIndices: {\r\n                    button: 1,\r\n                },\r\n                rootNodeName: \"xr_standard_squeeze\",\r\n                visualResponses: {},\r\n            },\r\n            \"xr-standard-touchpad\": {\r\n                type: \"touchpad\",\r\n                gamepadIndices: {\r\n                    button: 2,\r\n                    xAxis: 0,\r\n                    yAxis: 1,\r\n                },\r\n                rootNodeName: \"xr_standard_touchpad\",\r\n                visualResponses: {},\r\n            },\r\n            menu: {\r\n                type: \"button\",\r\n                gamepadIndices: {\r\n                    button: 4,\r\n                },\r\n                rootNodeName: \"menu\",\r\n                visualResponses: {},\r\n            },\r\n        },\r\n        gamepadMapping: \"xr-standard\",\r\n        rootNodeName: \"htc-vive-none\",\r\n        assetPath: \"none.glb\",\r\n    },\r\n};\r\n"]}