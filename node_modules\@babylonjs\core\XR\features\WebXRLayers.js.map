{"version": 3, "file": "WebXRLayers.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRLayers.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAEjF,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,OAAO,EAAE,sBAAsB,EAAE,MAAM,oBAAoB,CAAC;AAC5D,OAAO,EAAE,2BAA2B,EAAE,4BAA4B,EAAE,MAAM,+BAA+B,CAAC;AAC1G,OAAO,EAAE,gDAAgD,EAAE,4BAA4B,EAAE,MAAM,gCAAgC,CAAC;AAGhI,OAAO,EAAE,MAAM,EAAE,kCAA8B;AAG/C,MAAM,uBAAuB,GAAqB,EAAE,CAAC;AAkBrD;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,oBAAoB;IAwBjD,YACI,iBAAsC,EACrB,WAAgC,EAAE;QAEnD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFR,aAAQ,GAAR,QAAQ,CAA0B;QAfvD;;WAEG;QACK,oBAAe,GAAwB,EAAE,CAAC;QAI1C,wBAAmB,GAAG,KAAK,CAAC;QAC5B,gCAA2B,GAAG,KAAK,CAAC;QAEpC,oCAA+B,GAA6C,IAAI,OAAO,EAAE,CAAC;QAC1F,+BAA0B,GAAkF,IAAI,OAAO,EAAE,CAAC;QAO9H,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;IACxC,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QACxD,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3F,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAEhC,MAAM,mBAAmB,GAAG,EAAE,GAAG,4BAA4B,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC;QACtG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,SAAS,CAAC;QAC7F,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,8BAA8B,CAAC,CAAC;QAC/E,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACnC,KAAK,CAAC,OAAO,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;QACzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,MAAM,GAAG,uBAAuB;QACtD,MAAM,KAAK,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACxF,OAAO,IAAI,sBAAsB,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC;IAEO,kBAAkB,CAAC,MAA+C,EAAE,SAAS,GAAG,IAAI,CAAC,mBAAmB;QAC5G,6BAA6B;QAC7B,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,6GAA6G,CAAC,CAAC;SAClI;QACD,IAAI,SAAS,IAAI,MAAM,CAAC,WAAW,KAAK,eAAe,EAAE;YACrD,MAAM,IAAI,KAAK,CAAC,4HAA4H,CAAC,CAAC;SACjJ;QAED,iFAAiF;QACjF,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,WAAW,KAAK,eAAe,EAAE;YACtD,MAAM,IAAI,KAAK,CAAC,yGAAyG,CAAC,CAAC;SAC9H;IACL,CAAC;IAEO,kBAAkB,CAAC,MAA+C,EAAE,SAAS,GAAG,IAAI,CAAC,mBAAmB;QAC5G,IAAI,SAAS,EAAE;YACX,MAAM,CAAC,WAAW,GAAG,eAAe,CAAC;SACxC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACI,qBAAqB,CAAC,MAAM,GAAG,4BAA4B,EAAE,SAAS,GAAG,IAAI,CAAC,mBAAmB;QACpG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAC3C,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAE3C,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QACrE,MAAM,KAAK,GAAG,IAAI,2BAA2B,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC1F,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAC9B,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACK,gBAAgB,CAAC,UAAgD,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,cAA4B;QACjH,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,KAA2B,CAAC,YAAY,CAAC;QAChF,MAAM,MAAM,GAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,KAA2B,CAAC,aAAa,CAAC;QAClF,MAAM,eAAe,GAAoB;YACrC,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc;YAC5C,cAAc,EAAE,KAAK;YACrB,eAAe,EAAE,MAAM;YACvB,aAAa,EAAE,IAAI;YACnB,GAAG,OAAO,CAAC,MAAM;SACpB,CAAC;QACF,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;QAExE,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QACrB,mEAAmE;QACnE,MAAM,OAAO,GAAiC,IAAI,4BAA4B,CAC1E,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,EACrB,GAAG,EAAE,CAAC,SAAS,CAAC,MAAM,EACtB,SAAS,EACT,aAAa,EACb,KAAK,EACL,CAAC,cAAc,EAAE,EAAE,CAAC,IAAI,gDAAgD,CAAC,cAAc,EAAE,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,CAC1H,CAAC;QAEF,IAAI,cAAc,EAAE;YAChB,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;SACvE;QACD,MAAM,GAAG,GAAG,OAAO,CAAC,iCAAiC,CAAC,IAAI,CAAC,iBAAiB,CAAqD,CAAC;QAClI,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAChC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;;OAOG;IACI,mCAAmC,CAAC,OAAuB,EAAE,UAA2C,EAAE,mBAAmB,EAAE,GAAG,EAAE;QACvI,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CACjC;YACI,MAAM,EAAE;gBACJ,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,oBAAoB;gBAClD,WAAW,EAAE,SAAS;gBACtB,MAAM,EAAE,MAAM;aACjB;SACJ,EACD,OAAO,CACV,CAAC;QAEF,MAAM,KAAK,GAAG,OAAO,CAAC,KAAoB,CAAC;QAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAC5D,MAAM,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;QACzC,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1C,KAAK,CAAC,SAAS,GAAG,IAAI,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAEpD,MAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC/D,IAAI,CAAC,WAAW,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;SACpE;QACD,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,EAAE;YAC3E,OAAO,YAAY,CAAC,OAAO,KAAK,OAAO,CAAC;QAC5C,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;SACvE;QACD,WAAW,CAAC,sCAAsC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YAC5D,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,KAAK,OAAO,EAAE;gBAClC,OAAO;aACV;YACD,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACjD,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrD,YAAY,CAAC,gCAAgC,GAAG,IAAI,CAAC;YACrD,uEAAuE;YACvE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC3D,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC1B,CAAC,CAAC,CAAC;YACH,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrD,YAAY,CAAC,gCAAgC,GAAG,IAAI,CAAC;YACrD,oCAAoC;YACpC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,EAAE;gBACjD,YAAY,CAAC,oBAAoB,CAAC,MAAM,CAAC,YAAY,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;gBACrG,YAAY,CAAC,gCAAgC,GAAG,KAAK,CAAC;YAC1D,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;;OAOG;IACO,mBAAmB,CAAC,WAA4B;QACtD,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAClC,MAAM,EAAE;gBACJ,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,oBAAoB;gBAClD,WAAW,EAAE,SAAS;gBACtB,MAAM,EAAE,MAAM;aACjB;SACJ,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,OAAO,CAAC,KAAoB,CAAC;QAC3C,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;QAChB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QACjB,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,MAAM,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;QACzC,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1C,KAAK,CAAC,SAAS,GAAG,IAAI,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAEpD,sBAAsB;QACtB,MAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC/D,IAAI,CAAC,WAAW,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;SACpE;QACD,qCAAqC;QACrC,WAAW,CAAC,sCAAsC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YAC5D,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACjD,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,GAAG,EAAE;gBACrC,WAAW,CAAC,MAAM,EAAE,CAAC;YACzB,CAAC,CAAC;YAEF,oCAAoC;YACpC,0EAA0E;YAC1E,6BAA6B;YAC7B,MAAM;QACV,CAAC,CAAC,CAAC;QACH,8CAA8C;QAC9C,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;QAChI,CAAC,CAAC,CAAC;QACH,oCAAoC;QACpC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC7C,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,YAA+B;QACpD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAClD,CAAC;IAED;;;;;;;;OAQG;IACI,kBAAkB,CAAC,gBAA0C,IAAI,CAAC,eAAe;QACpF,wCAAwC;QACxC,MAAM,eAAe,GAAsB,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAC7F,sCAAsC;QACtC,eAAe,CAAC,SAAS,GAAG,SAAS,CAAC;QACtC,eAAe,CAAC,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACjF,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QAC1D,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE;YACnC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACvG;IACL,CAAC;IAEM,YAAY;QACf,oCAAoC;QACpC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,IAAI,OAAO,cAAc,KAAK,WAAW,IAAI,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,qBAAqB,CAAC;IACzI,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAES,UAAU,CAAC,QAAiB;QAClC,yJAAyJ;QACzJ,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;QACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACpC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,KAAK,CAAC,SAAS,KAAK,mBAAmB,EAAE;gBACzC,uBAAuB;gBACvB,MAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,KAAK,CAAC,KAA2B,CAAC,CAAC;gBAC3F,IAAI,CAAC,WAAW,EAAE;oBACd,SAAS;iBACZ;gBAED,IAAI,WAAW,CAAC,YAAY,CAAC,WAAW,EAAE;oBACtC,wCAAwC;oBACxC,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;oBAC3E,IAAI,IAAI,EAAE;wBACN,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;wBACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;4BACnC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;4BACtB,WAAW,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC;yBACnD;qBACJ;iBACJ;qBAAM;oBACH,WAAW,CAAC,6BAA6B,EAAE,CAAC;iBAC/C;aACJ;SACJ;IACL,CAAC;;AAjVD;;GAEG;AACoB,gBAAI,GAAG,gBAAgB,CAAC,MAAM,AAA1B,CAA2B;AACtD;;;;GAIG;AACoB,mBAAO,GAAG,CAAC,AAAJ,CAAK;AA2UvC,qBAAqB;AACrB,oBAAoB,CAAC,eAAe,CAChC,WAAW,CAAC,IAAI,EAChB,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AAC5D,CAAC,EACD,WAAW,CAAC,OAAO,EACnB,KAAK,CACR,CAAC", "sourcesContent": ["import { WebXRFeatureName, WebXRFeaturesManager } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport type { WebXRLayerWrapper } from \"../webXRLayerWrapper\";\r\nimport { WebXRWebGLLayerWrapper } from \"../webXRWebGLLayer\";\r\nimport { WebXRProjectionLayerWrapper, defaultXRProjectionLayerInit } from \"./Layers/WebXRProjectionLayer\";\r\nimport { WebXRCompositionLayerRenderTargetTextureProvider, WebXRCompositionLayerWrapper } from \"./Layers/WebXRCompositionLayer\";\r\nimport type { ThinTexture } from \"core/Materials/Textures/thinTexture\";\r\nimport type { DynamicTexture } from \"core/Materials/Textures/dynamicTexture\";\r\nimport { Color4 } from \"core/Maths/math.color\";\r\nimport type { LensFlareSystem } from \"core/LensFlares/lensFlareSystem\";\r\n\r\nconst defaultXRWebGLLayerInit: XRWebGLLayerInit = {};\r\n\r\n/**\r\n * Configuration options of the layers feature\r\n */\r\nexport interface IWebXRLayersOptions {\r\n    /**\r\n     * Whether to try initializing the base projection layer as a multiview render target, if multiview is supported.\r\n     * Defaults to false.\r\n     */\r\n    preferMultiviewOnInit?: boolean;\r\n\r\n    /**\r\n     * Optional configuration for the base projection layer.\r\n     */\r\n    projectionLayerInit?: Partial<XRProjectionLayerInit>;\r\n}\r\n\r\n/**\r\n * Exposes the WebXR Layers API.\r\n */\r\nexport class WebXRLayers extends WebXRAbstractFeature {\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.LAYERS;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n    /**\r\n     * Already-created layers\r\n     */\r\n    private _existingLayers: WebXRLayerWrapper[] = [];\r\n\r\n    private _glContext: WebGLRenderingContext | WebGL2RenderingContext;\r\n    private _xrWebGLBinding: XRWebGLBinding;\r\n    private _isMultiviewEnabled = false;\r\n    private _projectionLayerInitialized = false;\r\n\r\n    private _compositionLayerTextureMapping: WeakMap<XRCompositionLayer, ThinTexture> = new WeakMap();\r\n    private _layerToRTTProviderMapping: WeakMap<XRCompositionLayer, WebXRCompositionLayerRenderTargetTextureProvider> = new WeakMap();\r\n\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        private readonly _options: IWebXRLayersOptions = {}\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"layers\";\r\n    }\r\n\r\n    /**\r\n     * Attach this feature.\r\n     * Will usually be called by the features manager.\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public attach(): boolean {\r\n        if (!super.attach()) {\r\n            return false;\r\n        }\r\n\r\n        const engine = this._xrSessionManager.scene.getEngine();\r\n        this._glContext = engine._gl;\r\n        this._xrWebGLBinding = new XRWebGLBinding(this._xrSessionManager.session, this._glContext);\r\n        this._existingLayers.length = 0;\r\n\r\n        const projectionLayerInit = { ...defaultXRProjectionLayerInit, ...this._options.projectionLayerInit };\r\n        this._isMultiviewEnabled = this._options.preferMultiviewOnInit && engine.getCaps().multiview;\r\n        this.createProjectionLayer(projectionLayerInit /*, projectionLayerMultiview*/);\r\n        this._projectionLayerInitialized = true;\r\n\r\n        return true;\r\n    }\r\n\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n        this._existingLayers.forEach((layer) => {\r\n            layer.dispose();\r\n        });\r\n        this._existingLayers.length = 0;\r\n        this._projectionLayerInitialized = false;\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Creates a new XRWebGLLayer.\r\n     * @param params an object providing configuration options for the new XRWebGLLayer\r\n     * @returns the XRWebGLLayer\r\n     */\r\n    public createXRWebGLLayer(params = defaultXRWebGLLayerInit): WebXRWebGLLayerWrapper {\r\n        const layer = new XRWebGLLayer(this._xrSessionManager.session, this._glContext, params);\r\n        return new WebXRWebGLLayerWrapper(layer);\r\n    }\r\n\r\n    private _validateLayerInit(params: XRProjectionLayerInit | XRQuadLayerInit, multiview = this._isMultiviewEnabled): void {\r\n        // check if we are in session\r\n        if (!this._xrSessionManager.inXRSession) {\r\n            throw new Error(\"Cannot create a layer outside of a WebXR session. Make sure the session has started before creating layers.\");\r\n        }\r\n        if (multiview && params.textureType !== \"texture-array\") {\r\n            throw new Error(\"Projection layers can only be made multiview if they use texture arrays. Set the textureType parameter to 'texture-array'.\");\r\n        }\r\n\r\n        // TODO (rgerd): Support RTT's that are bound to sub-images in the texture array.\r\n        if (!multiview && params.textureType === \"texture-array\") {\r\n            throw new Error(\"We currently only support multiview rendering when the textureType parameter is set to 'texture-array'.\");\r\n        }\r\n    }\r\n\r\n    private _extendXRLayerInit(params: XRProjectionLayerInit | XRQuadLayerInit, multiview = this._isMultiviewEnabled): XRProjectionLayerInit | XRQuadLayerInit {\r\n        if (multiview) {\r\n            params.textureType = \"texture-array\";\r\n        }\r\n        return params;\r\n    }\r\n\r\n    /**\r\n     * Creates a new XRProjectionLayer.\r\n     * @param params an object providing configuration options for the new XRProjectionLayer.\r\n     * @param multiview whether the projection layer should render with multiview. Will be tru automatically if the extension initialized with multiview.\r\n     * @returns the projection layer\r\n     */\r\n    public createProjectionLayer(params = defaultXRProjectionLayerInit, multiview = this._isMultiviewEnabled): WebXRProjectionLayerWrapper {\r\n        this._extendXRLayerInit(params, multiview);\r\n        this._validateLayerInit(params, multiview);\r\n\r\n        const projLayer = this._xrWebGLBinding.createProjectionLayer(params);\r\n        const layer = new WebXRProjectionLayerWrapper(projLayer, multiview, this._xrWebGLBinding);\r\n        this.addXRSessionLayer(layer);\r\n        return layer;\r\n    }\r\n\r\n    /**\r\n     * Note about making it private - this function will be exposed once I decide on a proper API to support all of the XR layers' options\r\n     * @param options an object providing configuration options for the new XRQuadLayer.\r\n     * @param babylonTexture the texture to display in the layer\r\n     * @returns the quad layer\r\n     */\r\n    private _createQuadLayer(options: { params: Partial<XRQuadLayerInit> } = { params: {} }, babylonTexture?: ThinTexture): WebXRCompositionLayerWrapper {\r\n        this._extendXRLayerInit(options.params, false);\r\n        const width = (this._existingLayers[0].layer as XRProjectionLayer).textureWidth;\r\n        const height = (this._existingLayers[0].layer as XRProjectionLayer).textureHeight;\r\n        const populatedParams: XRQuadLayerInit = {\r\n            space: this._xrSessionManager.referenceSpace,\r\n            viewPixelWidth: width,\r\n            viewPixelHeight: height,\r\n            clearOnAccess: true,\r\n            ...options.params,\r\n        };\r\n        this._validateLayerInit(populatedParams, false);\r\n        const quadLayer = this._xrWebGLBinding.createQuadLayer(populatedParams);\r\n\r\n        quadLayer.width = this._isMultiviewEnabled ? 1 : 2;\r\n        quadLayer.height = 1;\r\n        // this wrapper is not really needed, but it's here for consistency\r\n        const wrapper: WebXRCompositionLayerWrapper = new WebXRCompositionLayerWrapper(\r\n            () => quadLayer.width,\r\n            () => quadLayer.height,\r\n            quadLayer,\r\n            \"XRQuadLayer\",\r\n            false,\r\n            (sessionManager) => new WebXRCompositionLayerRenderTargetTextureProvider(sessionManager, this._xrWebGLBinding, wrapper)\r\n        );\r\n\r\n        if (babylonTexture) {\r\n            this._compositionLayerTextureMapping.set(quadLayer, babylonTexture);\r\n        }\r\n        const rtt = wrapper.createRenderTargetTextureProvider(this._xrSessionManager) as WebXRCompositionLayerRenderTargetTextureProvider;\r\n        this._layerToRTTProviderMapping.set(quadLayer, rtt);\r\n        this.addXRSessionLayer(wrapper);\r\n        return wrapper;\r\n    }\r\n\r\n    /**\r\n     * @experimental\r\n     * This will support full screen ADT when used with WebXR Layers. This API might change in the future.\r\n     * Note that no interaction will be available with the ADT when using this method\r\n     * @param texture the texture to display in the layer\r\n     * @param options optional parameters for the layer\r\n     * @returns a composition layer containing the texture\r\n     */\r\n    public addFullscreenAdvancedDynamicTexture(texture: DynamicTexture, options: { distanceFromHeadset: number } = { distanceFromHeadset: 1.5 }): WebXRCompositionLayerWrapper {\r\n        const wrapper = this._createQuadLayer(\r\n            {\r\n                params: {\r\n                    space: this._xrSessionManager.viewerReferenceSpace,\r\n                    textureType: \"texture\",\r\n                    layout: \"mono\",\r\n                },\r\n            },\r\n            texture\r\n        );\r\n\r\n        const layer = wrapper.layer as XRQuadLayer;\r\n        const distance = Math.max(0.1, options.distanceFromHeadset);\r\n        const pos = { x: 0, y: 0, z: -distance };\r\n        const orient = { x: 0, y: 0, z: 0, w: 1 };\r\n        layer.transform = new XRRigidTransform(pos, orient);\r\n\r\n        const rttProvider = this._layerToRTTProviderMapping.get(layer);\r\n        if (!rttProvider) {\r\n            throw new Error(\"Could not find the RTT provider for the layer\");\r\n        }\r\n        const babylonLayer = this._xrSessionManager.scene.layers.find((babylonLayer) => {\r\n            return babylonLayer.texture === texture;\r\n        });\r\n        if (!babylonLayer) {\r\n            throw new Error(\"Could not find the babylon layer for the texture\");\r\n        }\r\n        rttProvider.onRenderTargetTextureCreatedObservable.add((data) => {\r\n            if (data.eye && data.eye === \"right\") {\r\n                return;\r\n            }\r\n            data.texture.clearColor = new Color4(0, 0, 0, 0);\r\n            babylonLayer.renderTargetTextures.push(data.texture);\r\n            babylonLayer.renderOnlyInRenderTargetTextures = true;\r\n            // for stereo (not for gui) it should be onBeforeCameraRenderObservable\r\n            this._xrSessionManager.scene.onBeforeRenderObservable.add(() => {\r\n                data.texture.render();\r\n            });\r\n            babylonLayer.renderTargetTextures.push(data.texture);\r\n            babylonLayer.renderOnlyInRenderTargetTextures = true;\r\n            // add it back when the session ends\r\n            this._xrSessionManager.onXRSessionEnded.addOnce(() => {\r\n                babylonLayer.renderTargetTextures.splice(babylonLayer.renderTargetTextures.indexOf(data.texture), 1);\r\n                babylonLayer.renderOnlyInRenderTargetTextures = false;\r\n            });\r\n        });\r\n        return wrapper;\r\n    }\r\n\r\n    /**\r\n     * @experimental\r\n     * This functions allows you to add a lens flare system to the XR scene.\r\n     * Note - this will remove the lens flare system from the scene and add it to the XR scene.\r\n     * This feature is experimental and might change in the future.\r\n     * @param flareSystem the flare system to add\r\n     * @returns a composition layer containing the flare system\r\n     */\r\n    protected _addLensFlareSystem(flareSystem: LensFlareSystem): WebXRCompositionLayerWrapper {\r\n        const wrapper = this._createQuadLayer({\r\n            params: {\r\n                space: this._xrSessionManager.viewerReferenceSpace,\r\n                textureType: \"texture\",\r\n                layout: \"mono\",\r\n            },\r\n        });\r\n\r\n        const layer = wrapper.layer as XRQuadLayer;\r\n        layer.width = 2;\r\n        layer.height = 1;\r\n        const distance = 10;\r\n        const pos = { x: 0, y: 0, z: -distance };\r\n        const orient = { x: 0, y: 0, z: 0, w: 1 };\r\n        layer.transform = new XRRigidTransform(pos, orient);\r\n\r\n        // get the rtt wrapper\r\n        const rttProvider = this._layerToRTTProviderMapping.get(layer);\r\n        if (!rttProvider) {\r\n            throw new Error(\"Could not find the RTT provider for the layer\");\r\n        }\r\n        // render the flare system to the rtt\r\n        rttProvider.onRenderTargetTextureCreatedObservable.add((data) => {\r\n            data.texture.clearColor = new Color4(0, 0, 0, 0);\r\n            data.texture.customRenderFunction = () => {\r\n                flareSystem.render();\r\n            };\r\n\r\n            // add to the scene's render targets\r\n            // this._xrSessionManager.scene.onBeforeCameraRenderObservable.add(() => {\r\n            //     data.texture.render();\r\n            // });\r\n        });\r\n        // remove the lens flare system from the scene\r\n        this._xrSessionManager.onXRSessionInit.add(() => {\r\n            this._xrSessionManager.scene.lensFlareSystems.splice(this._xrSessionManager.scene.lensFlareSystems.indexOf(flareSystem), 1);\r\n        });\r\n        // add it back when the session ends\r\n        this._xrSessionManager.onXRSessionEnded.add(() => {\r\n            this._xrSessionManager.scene.lensFlareSystems.push(flareSystem);\r\n        });\r\n\r\n        return wrapper;\r\n    }\r\n\r\n    /**\r\n     * Add a new layer to the already-existing list of layers\r\n     * @param wrappedLayer the new layer to add to the existing ones\r\n     */\r\n    public addXRSessionLayer(wrappedLayer: WebXRLayerWrapper) {\r\n        this._existingLayers.push(wrappedLayer);\r\n        this.setXRSessionLayers(this._existingLayers);\r\n    }\r\n\r\n    /**\r\n     * Sets the layers to be used by the XR session.\r\n     * Note that you must call this function with any layers you wish to render to\r\n     * since it adds them to the XR session's render state\r\n     * (replacing any layers that were added in a previous call to setXRSessionLayers or updateRenderState).\r\n     * This method also sets up the session manager's render target texture provider\r\n     * as the first layer in the array, which feeds the WebXR camera(s) attached to the session.\r\n     * @param wrappedLayers An array of WebXRLayerWrapper, usually returned from the WebXRLayers createLayer functions.\r\n     */\r\n    public setXRSessionLayers(wrappedLayers: Array<WebXRLayerWrapper> = this._existingLayers): void {\r\n        // this._existingLayers = wrappedLayers;\r\n        const renderStateInit: XRRenderStateInit = { ...this._xrSessionManager.session.renderState };\r\n        // Clear out the layer-related fields.\r\n        renderStateInit.baseLayer = undefined;\r\n        renderStateInit.layers = wrappedLayers.map((wrappedLayer) => wrappedLayer.layer);\r\n        this._xrSessionManager.updateRenderState(renderStateInit);\r\n        if (!this._projectionLayerInitialized) {\r\n            this._xrSessionManager._setBaseLayerWrapper(wrappedLayers.length > 0 ? wrappedLayers.at(0)! : null);\r\n        }\r\n    }\r\n\r\n    public isCompatible(): boolean {\r\n        // TODO (rgerd): Add native support.\r\n        return !this._xrSessionManager.isNative && typeof XRWebGLBinding !== \"undefined\" && !!XRWebGLBinding.prototype.createProjectionLayer;\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached.\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n    }\r\n\r\n    protected _onXRFrame(_xrFrame: XRFrame): void {\r\n        // Replace once the mapped internal texture of each available composition layer, apart from the last one, which is the projection layer that needs an RTT\r\n        const layers = this._existingLayers;\r\n        for (let i = 0; i < layers.length; ++i) {\r\n            const layer = layers[i];\r\n            if (layer.layerType !== \"XRProjectionLayer\") {\r\n                // get the rtt provider\r\n                const rttProvider = this._layerToRTTProviderMapping.get(layer.layer as XRCompositionLayer);\r\n                if (!rttProvider) {\r\n                    continue;\r\n                }\r\n\r\n                if (rttProvider.layerWrapper.isMultiview) {\r\n                    // get the views, if we are in multiview\r\n                    const pose = _xrFrame.getViewerPose(this._xrSessionManager.referenceSpace);\r\n                    if (pose) {\r\n                        const views = pose.views;\r\n                        for (let j = 0; j < views.length; ++j) {\r\n                            const view = views[j];\r\n                            rttProvider.getRenderTargetTextureForView(view);\r\n                        }\r\n                    }\r\n                } else {\r\n                    rttProvider.getRenderTargetTextureForView();\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n//register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRLayers.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRLayers(xrSessionManager, options);\r\n    },\r\n    WebXRLayers.Version,\r\n    false\r\n);\r\n"]}