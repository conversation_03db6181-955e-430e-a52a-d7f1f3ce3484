{"version": 3, "file": "thinRenderTargetTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/thinRenderTargetTexture.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAG5C;;;GAGG;AACH,MAAM,OAAO,uBAAwB,SAAQ,WAAW;IAMpD;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,MAAkB,EAAE,IAAiB,EAAE,OAAoC;QACnF,KAAK,CAAC,IAAI,CAAC,CAAC;QAnBR,kBAAa,GAAkC,IAAI,CAAC;QAoBxD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,IAAiB;QAC3B,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAElB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACtG;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAa,CAAC,OAAO,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,yBAAyB,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,uBAAuB,GAAG,KAAK;QAC1C,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,IAAI,CAAC,uBAAuB,EAAE;YAC1B,KAAK,CAAC,OAAO,EAAE,CAAC;SACnB;IACL,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\n\r\nimport type { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { IRenderTargetTexture, RenderTargetWrapper } from \"../../Engines/renderTargetWrapper\";\r\nimport { ThinTexture } from \"./thinTexture\";\r\nimport type { TextureSize, RenderTargetCreationOptions } from \"./textureCreationOptions\";\r\n\r\n/**\r\n * This is a tiny helper class to wrap a RenderTargetWrapper in a texture\r\n * usable as the input of an effect.\r\n */\r\nexport class ThinRenderTargetTexture extends ThinTexture implements IRenderTargetTexture {\r\n    private readonly _renderTargetOptions: RenderTargetCreationOptions;\r\n\r\n    private _renderTarget: Nullable<RenderTargetWrapper> = null;\r\n    private _size: TextureSize;\r\n\r\n    /**\r\n     * Gets the render target wrapper associated with this render target\r\n     */\r\n    public get renderTarget(): Nullable<RenderTargetWrapper> {\r\n        return this._renderTarget;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new ThinRenderTargetTexture.\r\n     * Tiny helper class to wrap a RenderTargetWrapper in a texture.\r\n     * This can be used as an internal texture wrapper in ThinEngine to benefit from the cache and to hold on the associated RTT\r\n     * @param engine Define the internalTexture to wrap\r\n     * @param size Define the size of the RTT to create\r\n     * @param options Define rendertarget options\r\n     */\r\n    constructor(engine: ThinEngine, size: TextureSize, options: RenderTargetCreationOptions) {\r\n        super(null);\r\n        this._engine = engine;\r\n        this._renderTargetOptions = options;\r\n        this.resize(size);\r\n    }\r\n\r\n    /**\r\n     * Resize the texture to a new desired size.\r\n     * Be careful as it will recreate all the data in the new texture.\r\n     * @param size Define the new size. It can be:\r\n     *   - a number for squared texture,\r\n     *   - an object containing { width: number, height: number }\r\n     */\r\n    public resize(size: TextureSize): void {\r\n        this._renderTarget?.dispose();\r\n        this._renderTarget = null;\r\n        this._texture = null;\r\n        this._size = size;\r\n\r\n        if (this._engine) {\r\n            this._renderTarget = this._engine.createRenderTargetTexture(this._size, this._renderTargetOptions);\r\n        }\r\n        this._texture = this.renderTarget!.texture;\r\n    }\r\n\r\n    /**\r\n     * Get the underlying lower level texture from Babylon.\r\n     * @returns the internal texture\r\n     */\r\n    public getInternalTexture(): Nullable<InternalTexture> {\r\n        return this._texture;\r\n    }\r\n\r\n    /**\r\n     * Get the class name of the texture.\r\n     * @returns \"ThinRenderTargetTexture\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"ThinRenderTargetTexture\";\r\n    }\r\n\r\n    /**\r\n     * Dispose the texture and release its associated resources.\r\n     * @param disposeOnlyFramebuffers if set to true it will dispose only the frame buffers (default: false)\r\n     */\r\n    public dispose(disposeOnlyFramebuffers = false): void {\r\n        this._renderTarget?.dispose(true);\r\n        this._renderTarget = null;\r\n\r\n        if (!disposeOnlyFramebuffers) {\r\n            super.dispose();\r\n        }\r\n    }\r\n}\r\n"]}