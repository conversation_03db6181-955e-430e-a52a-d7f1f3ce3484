{"version": 3, "file": "sceneOptimizer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/sceneOptimizer.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AAGtC,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C;;;GAGG;AACH,MAAM,OAAO,iBAAiB;IAC1B;;;OAGG;IACI,cAAc;QACjB,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,KAAY,EAAE,SAAyB;QAChD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH;IACI;;OAEG;IACI,WAAmB,CAAC;QAApB,aAAQ,GAAR,QAAQ,CAAY;IAC5B,CAAC;CACP;AAED;;;GAGG;AACH,MAAM,OAAO,mBAAoB,SAAQ,iBAAiB;IACtD;;;OAGG;IACI,cAAc;QACjB,OAAO,yCAAyC,GAAG,IAAI,CAAC,WAAW,CAAC;IACxE,CAAC;IAED;;;;;OAKG;IACH;IACI;;OAEG;IACI,WAAmB,CAAC;IAC3B;;OAEG;IACI,cAAsB,IAAI;IACjC;;OAEG;IACI,OAAO,GAAG;QAEjB,KAAK,CAAC,QAAQ,CAAC,CAAC;QAVT,aAAQ,GAAR,QAAQ,CAAY;QAIpB,gBAAW,GAAX,WAAW,CAAe;QAI1B,SAAI,GAAJ,IAAI,CAAM;IAGrB,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,KAAY,EAAE,SAAyB;QAChD,IAAI,OAAO,GAAG,IAAI,CAAC;QACnB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACxD,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAEtC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAU,OAAQ,CAAC,UAAU,EAAE;gBAClD,SAAS;aACZ;YAED,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YAErE,IAAI,YAAY,GAAG,IAAI,CAAC,WAAW,EAAE;gBACjC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzB,OAAO,GAAG,KAAK,CAAC;aACnB;SACJ;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,2BAA4B,SAAQ,iBAAiB;IAI9D;;;OAGG;IACI,cAAc;QACjB,OAAO,oCAAoC,GAAG,IAAI,CAAC,aAAa,CAAC;IACrE,CAAC;IAED;;;;;OAKG;IACH;IACI;;OAEG;IACI,WAAmB,CAAC;IAC3B;;OAEG;IACI,eAAuB,CAAC;IAC/B;;OAEG;IACI,OAAe,IAAI;QAE1B,KAAK,CAAC,QAAQ,CAAC,CAAC;QAVT,aAAQ,GAAR,QAAQ,CAAY;QAIpB,iBAAY,GAAZ,YAAY,CAAY;QAIxB,SAAI,GAAJ,IAAI,CAAe;QA7BtB,kBAAa,GAAG,CAAC,CAAC,CAAC;QACnB,qBAAgB,GAAG,CAAC,CAAC;IA+B7B,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,KAAY,EAAE,SAAyB;QAChD,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,CAAC,EAAE;YAC3B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,uBAAuB,EAAE,CAAC;YACjE,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,EAAE;gBACxC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;aAC9B;SACJ;QAED,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC;QAExD,KAAK,CAAC,SAAS,EAAE,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE9D,OAAO,IAAI,CAAC,gBAAgB,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,CAAC;IAC3H,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,mBAAoB,SAAQ,iBAAiB;IACtD;;;OAGG;IACI,cAAc;QACjB,OAAO,wBAAwB,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,KAAY,EAAE,SAAyB;QAChD,KAAK,CAAC,cAAc,GAAG,SAAS,CAAC,mBAAmB,CAAC;QACrD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,yBAA0B,SAAQ,iBAAiB;IAC5D;;;OAGG;IACI,cAAc;QACjB,OAAO,+BAA+B,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,KAAY,EAAE,SAAyB;QAChD,KAAK,CAAC,oBAAoB,GAAG,SAAS,CAAC,mBAAmB,CAAC;QAC3D,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,sBAAuB,SAAQ,iBAAiB;IACzD;;;OAGG;IACI,cAAc;QACjB,OAAO,4BAA4B,CAAC;IACxC,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,KAAY,EAAE,SAAyB;QAChD,KAAK,CAAC,iBAAiB,GAAG,SAAS,CAAC,mBAAmB,CAAC;QACxD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,kBAAmB,SAAQ,iBAAiB;IAWrD;;;OAGG;IACI,cAAc;QACjB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAClC;QAED,OAAO,+BAA+B,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,KAAY,EAAE,SAAyB;QAChD,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;SACzC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,qBAAsB,SAAQ,iBAAiB;IACxD;;;OAGG;IACI,cAAc;QACjB,OAAO,0BAA0B,CAAC;IACtC,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,KAAY,EAAE,SAAyB;QAChD,KAAK,CAAC,gBAAgB,GAAG,SAAS,CAAC,mBAAmB,CAAC;QACvD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,yBAA0B,SAAQ,iBAAiB;IAC5D;;;OAGG;IACI,cAAc;QACjB,OAAO,4BAA4B,CAAC;IACxC,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,KAAY,EAAE,SAAyB;QAChD,KAAK,CAAC,oBAAoB,GAAG,SAAS,CAAC,mBAAmB,CAAC;QAC3D,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,uBAAwB,SAAQ,iBAAiB;IAA9D;;QAyBY,iBAAY,GAAG,CAAC,YAA0B,EAAW,EAAE;YAC3D,IAAI,CAAC,CAAC,YAAY,YAAY,IAAI,CAAC,EAAE;gBACjC,OAAO,KAAK,CAAC;aAChB;YAED,MAAM,IAAI,GAAS,YAAY,CAAC;YAEhC,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACnB,OAAO,KAAK,CAAC;aAChB;YAED,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACtC,OAAO,KAAK,CAAC;aAChB;YAED,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3B,OAAO,KAAK,CAAC;aAChB;YAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE;gBACpC,OAAO,KAAK,CAAC;aAChB;YAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;gBAC/B,OAAO,KAAK,CAAC;aAChB;YAED,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC;IAsEN,CAAC;IAxHG;;OAEG;IACI,MAAM,KAAK,mBAAmB;QACjC,OAAO,uBAAuB,CAAC,oBAAoB,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,mBAAmB,CAAC,KAAc;QAChD,uBAAuB,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACzD,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,iCAAiC,CAAC;IAC7C,CAAC;IAgCD;;;;;;OAMG;IACI,KAAK,CAAC,KAAY,EAAE,SAAyB,EAAE,mBAA6B;QAC/E,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACzC,IAAI,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC;QAErC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,EAAE,KAAK,EAAE,EAAE;YAC/C,MAAM,WAAW,GAAW,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YAElC,SAAS;YACT,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE;gBAC7B,SAAS;aACZ;YAED,WAAW,CAAC,IAAI,CAAO,OAAO,CAAC,CAAC;YAEhC,yBAAyB;YACzB,KAAK,IAAI,QAAQ,GAAG,KAAK,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,EAAE,QAAQ,EAAE,EAAE;gBAChE,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAEvC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;oBAC/B,SAAS;iBACZ;gBAED,IAAI,SAAS,CAAC,QAAQ,KAAK,OAAO,CAAC,QAAQ,EAAE;oBACzC,SAAS;iBACZ;gBAED,IAAI,SAAS,CAAC,eAAe,KAAK,OAAO,CAAC,eAAe,EAAE;oBACvD,SAAS;iBACZ;gBAED,WAAW,CAAC,IAAI,CAAO,SAAS,CAAC,CAAC;gBAClC,YAAY,EAAE,CAAC;gBAEf,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;gBAE/B,QAAQ,EAAE,CAAC;aACd;YAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;gBACxB,SAAS;aACZ;YAED,eAAe;YACf,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;SAClD;QAED,wDAAwD;QACxD,MAAM,UAAU,GAAG,KAAY,CAAC;QAChC,IAAI,UAAU,CAAC,6BAA6B,EAAE;YAC1C,IAAI,mBAAmB,IAAI,SAAS,EAAE;gBAClC,IAAI,mBAAmB,EAAE;oBACrB,UAAU,CAAC,6BAA6B,EAAE,CAAC;iBAC9C;aACJ;iBAAM,IAAI,uBAAuB,CAAC,mBAAmB,EAAE;gBACpD,UAAU,CAAC,6BAA6B,EAAE,CAAC;aAC9C;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;;AAzHc,4CAAoB,GAAG,KAAK,AAAR,CAAS;AA4HhD;;;GAGG;AACH,MAAM,OAAO,qBAAqB;IAM9B;;;;OAIG;IACH;IACI;;OAEG;IACI,kBAA0B,EAAE;IACnC;;OAEG;IACI,kBAA0B,IAAI;QAJ9B,oBAAe,GAAf,eAAe,CAAa;QAI5B,oBAAe,GAAf,eAAe,CAAe;QAlBzC;;WAEG;QACI,kBAAa,GAAwB,EAAE,CAAC;IAgB5C,CAAC;IAEJ;;;;OAIG;IACI,eAAe,CAAC,YAA+B;QAClD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,qBAAqB,CAAC,OAA6D,EAAE,gBAA8B,EAAE,WAAmB,CAAC;QAC5I,MAAM,YAAY,GAAG,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACtD,YAAY,CAAC,OAAO,GAAG,OAAO,CAAC;QAC/B,YAAY,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QAEjD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,qBAAqB,CAAC,eAAwB;QACxD,MAAM,MAAM,GAAG,IAAI,qBAAqB,CAAC,eAAe,CAAC,CAAC;QAE1D,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,MAAM,CAAC,eAAe,CAAC,IAAI,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9D,MAAM,CAAC,eAAe,CAAC,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1D,MAAM,CAAC,eAAe,CAAC,IAAI,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE7D,gBAAgB;QAChB,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,eAAe,CAAC,IAAI,yBAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAChE,MAAM,CAAC,eAAe,CAAC,IAAI,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE5D,gBAAgB;QAChB,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,eAAe,CAAC,IAAI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;QAEhE,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,0BAA0B,CAAC,eAAwB;QAC7D,MAAM,MAAM,GAAG,IAAI,qBAAqB,CAAC,eAAe,CAAC,CAAC;QAE1D,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,MAAM,CAAC,eAAe,CAAC,IAAI,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9D,MAAM,CAAC,eAAe,CAAC,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1D,MAAM,CAAC,eAAe,CAAC,IAAI,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE7D,gBAAgB;QAChB,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,eAAe,CAAC,IAAI,yBAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAChE,MAAM,CAAC,eAAe,CAAC,IAAI,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE5D,gBAAgB;QAChB,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,eAAe,CAAC,IAAI,mBAAmB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;QAE/D,gBAAgB;QAChB,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,eAAe,CAAC,IAAI,yBAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEhE,gBAAgB;QAChB,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,eAAe,CAAC,IAAI,2BAA2B,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;QAErE,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,sBAAsB,CAAC,eAAwB;QACzD,MAAM,MAAM,GAAG,IAAI,qBAAqB,CAAC,eAAe,CAAC,CAAC;QAE1D,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,MAAM,CAAC,eAAe,CAAC,IAAI,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9D,MAAM,CAAC,eAAe,CAAC,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1D,MAAM,CAAC,eAAe,CAAC,IAAI,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE7D,gBAAgB;QAChB,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,eAAe,CAAC,IAAI,yBAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAChE,MAAM,CAAC,eAAe,CAAC,IAAI,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE5D,gBAAgB;QAChB,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,eAAe,CAAC,IAAI,mBAAmB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;QAE/D,gBAAgB;QAChB,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,eAAe,CAAC,IAAI,yBAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEhE,gBAAgB;QAChB,QAAQ,EAAE,CAAC;QACX,MAAM,CAAC,eAAe,CAAC,IAAI,2BAA2B,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;QAErE,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,cAAc;IAwBvB;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAc;QACzC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAW,eAAe,CAAC,KAAa;QACpC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAW,eAAe,CAAC,KAAa;QACpC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;IACvC,CAAC;IAED;;;;;;OAMG;IACH,YAAmB,KAAY,EAAE,OAA+B,EAAE,sBAAsB,GAAG,IAAI,EAAE,eAAe,GAAG,KAAK;QA1FhH,eAAU,GAAG,KAAK,CAAC;QAGnB,0BAAqB,GAAG,CAAC,CAAC;QAC1B,qBAAgB,GAAG,EAAE,CAAC;QACtB,qBAAgB,GAAG,IAAI,CAAC;QACxB,sBAAiB,GAAG,CAAC,CAAC;QAEtB,qBAAgB,GAAG,KAAK,CAAC;QAEjC;;WAEG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAkB,CAAC;QAC9D;;WAEG;QACI,uCAAkC,GAAG,IAAI,UAAU,EAAqB,CAAC;QAChF;;WAEG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAkB,CAAC;QAsE1D,IAAI,CAAC,OAAO,EAAE;YACV,IAAI,CAAC,QAAQ,GAAG,IAAI,qBAAqB,EAAE,CAAC;SAC/C;aAAM;YACH,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;SAC3B;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE;YAC/B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;SACzD;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE;YAC/B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;SACzD;QAED,IAAI,sBAAsB,EAAE;YACxB,IAAI,QAAQ,GAAG,CAAC,CAAC;YACjB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;gBAC7C,KAAK,CAAC,QAAQ,GAAG,QAAQ,EAAE,CAAC;aAC/B;SACJ;QAED,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YAClE,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAClC,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,IAAI;QACP,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO;SACV;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,gEAAgE;QAChE,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE;YAC9B,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC9B,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,kBAAkB;QACtB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO;SACV;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE9B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;QAEhE,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC,EAAE;YAC3J,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC/C,OAAO;SACV;QAED,uCAAuC;QACvC,IAAI,OAAO,GAAG,IAAI,CAAC;QACnB,IAAI,qBAAqB,GAAG,IAAI,CAAC;QACjC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC/D,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAElD,IAAI,YAAY,CAAC,QAAQ,KAAK,IAAI,CAAC,qBAAqB,EAAE;gBACtD,qBAAqB,GAAG,KAAK,CAAC;gBAC9B,OAAO,GAAG,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACrD,IAAI,CAAC,kCAAkC,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;aACzE;SACJ;QAED,uDAAuD;QACvD,IAAI,qBAAqB,EAAE;YACvB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAE/C,OAAO;SACV;QAED,qDAAqD;QACrD,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAChC;QAED,6EAA6E;QAC7E,KAAK,CAAC,gBAAgB,CAAC,GAAG,EAAE;YACxB,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC9B,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,CAAC;QAChD,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SACtE;IACL,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,aAAa,CAAC,KAAY,EAAE,OAA+B,EAAE,SAAsB,EAAE,SAAsB;QACrH,MAAM,SAAS,GAAG,IAAI,cAAc,CAAC,KAAK,EAAE,OAAO,IAAI,qBAAqB,CAAC,0BAA0B,EAAE,EAAE,KAAK,CAAC,CAAC;QAElH,IAAI,SAAS,EAAE;YACX,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACnC,SAAS,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;SACN;QAED,IAAI,SAAS,EAAE;YACX,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACnC,SAAS,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;SACN;QAED,SAAS,CAAC,KAAK,EAAE,CAAC;QAElB,OAAO,SAAS,CAAC;IACrB,CAAC;CACJ", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport type { Scene, IDisposable } from \"../scene\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Observer } from \"./observable\";\r\nimport { Observable } from \"./observable\";\r\n\r\n/**\r\n * Defines the root class used to create scene optimization to use with SceneOptimizer\r\n * @description More details at https://doc.babylonjs.com/features/featuresDeepDive/scene/sceneOptimizer\r\n */\r\nexport class SceneOptimization {\r\n    /**\r\n     * Gets a string describing the action executed by the current optimization\r\n     * @returns description string\r\n     */\r\n    public getDescription(): string {\r\n        return \"\";\r\n    }\r\n\r\n    /**\r\n     * This function will be called by the SceneOptimizer when its priority is reached in order to apply the change required by the current optimization\r\n     * @param scene defines the current scene where to apply this optimization\r\n     * @param optimizer defines the current optimizer\r\n     * @returns true if everything that can be done was applied\r\n     */\r\n    public apply(scene: Scene, optimizer: SceneOptimizer): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Creates the SceneOptimization object\r\n     * @param priority defines the priority of this optimization (0 by default which means first in the list)\r\n     */\r\n    constructor(\r\n        /**\r\n         * Defines the priority of this optimization (0 by default which means first in the list)\r\n         */\r\n        public priority: number = 0\r\n    ) {}\r\n}\r\n\r\n/**\r\n * Defines an optimization used to reduce the size of render target textures\r\n * @description More details at https://doc.babylonjs.com/features/featuresDeepDive/scene/sceneOptimizer\r\n */\r\nexport class TextureOptimization extends SceneOptimization {\r\n    /**\r\n     * Gets a string describing the action executed by the current optimization\r\n     * @returns description string\r\n     */\r\n    public getDescription(): string {\r\n        return \"Reducing render target texture size to \" + this.maximumSize;\r\n    }\r\n\r\n    /**\r\n     * Creates the TextureOptimization object\r\n     * @param priority defines the priority of this optimization (0 by default which means first in the list)\r\n     * @param maximumSize defines the maximum sized allowed for textures (1024 is the default value). If a texture is bigger, it will be scaled down using a factor defined by the step parameter\r\n     * @param step defines the factor (0.5 by default) used to scale down textures bigger than maximum sized allowed.\r\n     */\r\n    constructor(\r\n        /**\r\n         * Defines the priority of this optimization (0 by default which means first in the list)\r\n         */\r\n        public priority: number = 0,\r\n        /**\r\n         * Defines the maximum sized allowed for textures (1024 is the default value). If a texture is bigger, it will be scaled down using a factor defined by the step parameter\r\n         */\r\n        public maximumSize: number = 1024,\r\n        /**\r\n         * Defines the factor (0.5 by default) used to scale down textures bigger than maximum sized allowed.\r\n         */\r\n        public step = 0.5\r\n    ) {\r\n        super(priority);\r\n    }\r\n\r\n    /**\r\n     * This function will be called by the SceneOptimizer when its priority is reached in order to apply the change required by the current optimization\r\n     * @param scene defines the current scene where to apply this optimization\r\n     * @param optimizer defines the current optimizer\r\n     * @returns true if everything that can be done was applied\r\n     */\r\n    public apply(scene: Scene, optimizer: SceneOptimizer): boolean {\r\n        let allDone = true;\r\n        for (let index = 0; index < scene.textures.length; index++) {\r\n            const texture = scene.textures[index];\r\n\r\n            if (!texture.canRescale || (<any>texture).getContext) {\r\n                continue;\r\n            }\r\n\r\n            const currentSize = texture.getSize();\r\n            const maxDimension = Math.max(currentSize.width, currentSize.height);\r\n\r\n            if (maxDimension > this.maximumSize) {\r\n                texture.scale(this.step);\r\n                allDone = false;\r\n            }\r\n        }\r\n\r\n        return allDone;\r\n    }\r\n}\r\n\r\n/**\r\n * Defines an optimization used to increase or decrease the rendering resolution\r\n * @description More details at https://doc.babylonjs.com/features/featuresDeepDive/scene/sceneOptimizer\r\n */\r\nexport class HardwareScalingOptimization extends SceneOptimization {\r\n    private _currentScale = -1;\r\n    private _directionOffset = 1;\r\n\r\n    /**\r\n     * Gets a string describing the action executed by the current optimization\r\n     * @returns description string\r\n     */\r\n    public getDescription(): string {\r\n        return \"Setting hardware scaling level to \" + this._currentScale;\r\n    }\r\n\r\n    /**\r\n     * Creates the HardwareScalingOptimization object\r\n     * @param priority defines the priority of this optimization (0 by default which means first in the list)\r\n     * @param maximumScale defines the maximum scale to use (2 by default)\r\n     * @param step defines the step to use between two passes (0.5 by default)\r\n     */\r\n    constructor(\r\n        /**\r\n         * Defines the priority of this optimization (0 by default which means first in the list)\r\n         */\r\n        public priority: number = 0,\r\n        /**\r\n         * Defines the maximum scale to use (2 by default)\r\n         */\r\n        public maximumScale: number = 2,\r\n        /**\r\n         * Defines the step to use between two passes (0.5 by default)\r\n         */\r\n        public step: number = 0.25\r\n    ) {\r\n        super(priority);\r\n    }\r\n\r\n    /**\r\n     * This function will be called by the SceneOptimizer when its priority is reached in order to apply the change required by the current optimization\r\n     * @param scene defines the current scene where to apply this optimization\r\n     * @param optimizer defines the current optimizer\r\n     * @returns true if everything that can be done was applied\r\n     */\r\n    public apply(scene: Scene, optimizer: SceneOptimizer): boolean {\r\n        if (this._currentScale === -1) {\r\n            this._currentScale = scene.getEngine().getHardwareScalingLevel();\r\n            if (this._currentScale > this.maximumScale) {\r\n                this._directionOffset = -1;\r\n            }\r\n        }\r\n\r\n        this._currentScale += this._directionOffset * this.step;\r\n\r\n        scene.getEngine().setHardwareScalingLevel(this._currentScale);\r\n\r\n        return this._directionOffset === 1 ? this._currentScale >= this.maximumScale : this._currentScale <= this.maximumScale;\r\n    }\r\n}\r\n\r\n/**\r\n * Defines an optimization used to remove shadows\r\n * @description More details at https://doc.babylonjs.com/features/featuresDeepDive/scene/sceneOptimizer\r\n */\r\nexport class ShadowsOptimization extends SceneOptimization {\r\n    /**\r\n     * Gets a string describing the action executed by the current optimization\r\n     * @returns description string\r\n     */\r\n    public getDescription(): string {\r\n        return \"Turning shadows on/off\";\r\n    }\r\n\r\n    /**\r\n     * This function will be called by the SceneOptimizer when its priority is reached in order to apply the change required by the current optimization\r\n     * @param scene defines the current scene where to apply this optimization\r\n     * @param optimizer defines the current optimizer\r\n     * @returns true if everything that can be done was applied\r\n     */\r\n    public apply(scene: Scene, optimizer: SceneOptimizer): boolean {\r\n        scene.shadowsEnabled = optimizer.isInImprovementMode;\r\n        return true;\r\n    }\r\n}\r\n\r\n/**\r\n * Defines an optimization used to turn post-processes off\r\n * @description More details at https://doc.babylonjs.com/features/featuresDeepDive/scene/sceneOptimizer\r\n */\r\nexport class PostProcessesOptimization extends SceneOptimization {\r\n    /**\r\n     * Gets a string describing the action executed by the current optimization\r\n     * @returns description string\r\n     */\r\n    public getDescription(): string {\r\n        return \"Turning post-processes on/off\";\r\n    }\r\n\r\n    /**\r\n     * This function will be called by the SceneOptimizer when its priority is reached in order to apply the change required by the current optimization\r\n     * @param scene defines the current scene where to apply this optimization\r\n     * @param optimizer defines the current optimizer\r\n     * @returns true if everything that can be done was applied\r\n     */\r\n    public apply(scene: Scene, optimizer: SceneOptimizer): boolean {\r\n        scene.postProcessesEnabled = optimizer.isInImprovementMode;\r\n        return true;\r\n    }\r\n}\r\n\r\n/**\r\n * Defines an optimization used to turn lens flares off\r\n * @description More details at https://doc.babylonjs.com/features/featuresDeepDive/scene/sceneOptimizer\r\n */\r\nexport class LensFlaresOptimization extends SceneOptimization {\r\n    /**\r\n     * Gets a string describing the action executed by the current optimization\r\n     * @returns description string\r\n     */\r\n    public getDescription(): string {\r\n        return \"Turning lens flares on/off\";\r\n    }\r\n\r\n    /**\r\n     * This function will be called by the SceneOptimizer when its priority is reached in order to apply the change required by the current optimization\r\n     * @param scene defines the current scene where to apply this optimization\r\n     * @param optimizer defines the current optimizer\r\n     * @returns true if everything that can be done was applied\r\n     */\r\n    public apply(scene: Scene, optimizer: SceneOptimizer): boolean {\r\n        scene.lensFlaresEnabled = optimizer.isInImprovementMode;\r\n        return true;\r\n    }\r\n}\r\n\r\n/**\r\n * Defines an optimization based on user defined callback.\r\n * @description More details at https://doc.babylonjs.com/features/featuresDeepDive/scene/sceneOptimizer\r\n */\r\nexport class CustomOptimization extends SceneOptimization {\r\n    /**\r\n     * Callback called to apply the custom optimization.\r\n     */\r\n    public onApply: (scene: Scene, optimizer: SceneOptimizer) => boolean;\r\n\r\n    /**\r\n     * Callback called to get custom description\r\n     */\r\n    public onGetDescription: () => string;\r\n\r\n    /**\r\n     * Gets a string describing the action executed by the current optimization\r\n     * @returns description string\r\n     */\r\n    public getDescription(): string {\r\n        if (this.onGetDescription) {\r\n            return this.onGetDescription();\r\n        }\r\n\r\n        return \"Running user defined callback\";\r\n    }\r\n\r\n    /**\r\n     * This function will be called by the SceneOptimizer when its priority is reached in order to apply the change required by the current optimization\r\n     * @param scene defines the current scene where to apply this optimization\r\n     * @param optimizer defines the current optimizer\r\n     * @returns true if everything that can be done was applied\r\n     */\r\n    public apply(scene: Scene, optimizer: SceneOptimizer): boolean {\r\n        if (this.onApply) {\r\n            return this.onApply(scene, optimizer);\r\n        }\r\n        return true;\r\n    }\r\n}\r\n\r\n/**\r\n * Defines an optimization used to turn particles off\r\n * @description More details at https://doc.babylonjs.com/features/featuresDeepDive/scene/sceneOptimizer\r\n */\r\nexport class ParticlesOptimization extends SceneOptimization {\r\n    /**\r\n     * Gets a string describing the action executed by the current optimization\r\n     * @returns description string\r\n     */\r\n    public getDescription(): string {\r\n        return \"Turning particles on/off\";\r\n    }\r\n\r\n    /**\r\n     * This function will be called by the SceneOptimizer when its priority is reached in order to apply the change required by the current optimization\r\n     * @param scene defines the current scene where to apply this optimization\r\n     * @param optimizer defines the current optimizer\r\n     * @returns true if everything that can be done was applied\r\n     */\r\n    public apply(scene: Scene, optimizer: SceneOptimizer): boolean {\r\n        scene.particlesEnabled = optimizer.isInImprovementMode;\r\n        return true;\r\n    }\r\n}\r\n\r\n/**\r\n * Defines an optimization used to turn render targets off\r\n * @description More details at https://doc.babylonjs.com/features/featuresDeepDive/scene/sceneOptimizer\r\n */\r\nexport class RenderTargetsOptimization extends SceneOptimization {\r\n    /**\r\n     * Gets a string describing the action executed by the current optimization\r\n     * @returns description string\r\n     */\r\n    public getDescription(): string {\r\n        return \"Turning render targets off\";\r\n    }\r\n\r\n    /**\r\n     * This function will be called by the SceneOptimizer when its priority is reached in order to apply the change required by the current optimization\r\n     * @param scene defines the current scene where to apply this optimization\r\n     * @param optimizer defines the current optimizer\r\n     * @returns true if everything that can be done was applied\r\n     */\r\n    public apply(scene: Scene, optimizer: SceneOptimizer): boolean {\r\n        scene.renderTargetsEnabled = optimizer.isInImprovementMode;\r\n        return true;\r\n    }\r\n}\r\n\r\n/**\r\n * Defines an optimization used to merge meshes with compatible materials\r\n * @description More details at https://doc.babylonjs.com/features/featuresDeepDive/scene/sceneOptimizer\r\n */\r\nexport class MergeMeshesOptimization extends SceneOptimization {\r\n    private static _UpdateSelectionTree = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean which defines if optimization octree has to be updated\r\n     */\r\n    public static get UpdateSelectionTree(): boolean {\r\n        return MergeMeshesOptimization._UpdateSelectionTree;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean which defines if optimization octree has to be updated\r\n     */\r\n    public static set UpdateSelectionTree(value: boolean) {\r\n        MergeMeshesOptimization._UpdateSelectionTree = value;\r\n    }\r\n\r\n    /**\r\n     * Gets a string describing the action executed by the current optimization\r\n     * @returns description string\r\n     */\r\n    public getDescription(): string {\r\n        return \"Merging similar meshes together\";\r\n    }\r\n\r\n    private _canBeMerged = (abstractMesh: AbstractMesh): boolean => {\r\n        if (!(abstractMesh instanceof Mesh)) {\r\n            return false;\r\n        }\r\n\r\n        const mesh = <Mesh>abstractMesh;\r\n\r\n        if (mesh.isDisposed()) {\r\n            return false;\r\n        }\r\n\r\n        if (!mesh.isVisible || !mesh.isEnabled()) {\r\n            return false;\r\n        }\r\n\r\n        if (mesh.instances.length > 0) {\r\n            return false;\r\n        }\r\n\r\n        if (mesh.skeleton || mesh.hasLODLevels) {\r\n            return false;\r\n        }\r\n\r\n        if (mesh.getTotalVertices() === 0) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    };\r\n\r\n    /**\r\n     * This function will be called by the SceneOptimizer when its priority is reached in order to apply the change required by the current optimization\r\n     * @param scene defines the current scene where to apply this optimization\r\n     * @param optimizer defines the current optimizer\r\n     * @param updateSelectionTree defines that the selection octree has to be updated (false by default)\r\n     * @returns true if everything that can be done was applied\r\n     */\r\n    public apply(scene: Scene, optimizer: SceneOptimizer, updateSelectionTree?: boolean): boolean {\r\n        const globalPool = scene.meshes.slice(0);\r\n        let globalLength = globalPool.length;\r\n\r\n        for (let index = 0; index < globalLength; index++) {\r\n            const currentPool: Mesh[] = [];\r\n            const current = globalPool[index];\r\n\r\n            // Checks\r\n            if (!this._canBeMerged(current)) {\r\n                continue;\r\n            }\r\n\r\n            currentPool.push(<Mesh>current);\r\n\r\n            // Find compatible meshes\r\n            for (let subIndex = index + 1; subIndex < globalLength; subIndex++) {\r\n                const otherMesh = globalPool[subIndex];\r\n\r\n                if (!this._canBeMerged(otherMesh)) {\r\n                    continue;\r\n                }\r\n\r\n                if (otherMesh.material !== current.material) {\r\n                    continue;\r\n                }\r\n\r\n                if (otherMesh.checkCollisions !== current.checkCollisions) {\r\n                    continue;\r\n                }\r\n\r\n                currentPool.push(<Mesh>otherMesh);\r\n                globalLength--;\r\n\r\n                globalPool.splice(subIndex, 1);\r\n\r\n                subIndex--;\r\n            }\r\n\r\n            if (currentPool.length < 2) {\r\n                continue;\r\n            }\r\n\r\n            // Merge meshes\r\n            Mesh.MergeMeshes(currentPool, undefined, true);\r\n        }\r\n\r\n        // Call the octree system optimization if it is defined.\r\n        const sceneAsAny = scene as any;\r\n        if (sceneAsAny.createOrUpdateSelectionOctree) {\r\n            if (updateSelectionTree != undefined) {\r\n                if (updateSelectionTree) {\r\n                    sceneAsAny.createOrUpdateSelectionOctree();\r\n                }\r\n            } else if (MergeMeshesOptimization.UpdateSelectionTree) {\r\n                sceneAsAny.createOrUpdateSelectionOctree();\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n}\r\n\r\n/**\r\n * Defines a list of options used by SceneOptimizer\r\n * @description More details at https://doc.babylonjs.com/features/featuresDeepDive/scene/sceneOptimizer\r\n */\r\nexport class SceneOptimizerOptions {\r\n    /**\r\n     * Gets the list of optimizations to apply\r\n     */\r\n    public optimizations: SceneOptimization[] = [];\r\n\r\n    /**\r\n     * Creates a new list of options used by SceneOptimizer\r\n     * @param targetFrameRate defines the target frame rate to reach (60 by default)\r\n     * @param trackerDuration defines the interval between two checks (2000ms by default)\r\n     */\r\n    constructor(\r\n        /**\r\n         * Defines the target frame rate to reach (60 by default)\r\n         */\r\n        public targetFrameRate: number = 60,\r\n        /**\r\n         * Defines the interval between two checks (2000ms by default)\r\n         */\r\n        public trackerDuration: number = 2000\r\n    ) {}\r\n\r\n    /**\r\n     * Add a new optimization\r\n     * @param optimization defines the SceneOptimization to add to the list of active optimizations\r\n     * @returns the current SceneOptimizerOptions\r\n     */\r\n    public addOptimization(optimization: SceneOptimization): SceneOptimizerOptions {\r\n        this.optimizations.push(optimization);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add a new custom optimization\r\n     * @param onApply defines the callback called to apply the custom optimization (true if everything that can be done was applied)\r\n     * @param onGetDescription defines the callback called to get the description attached with the optimization.\r\n     * @param priority defines the priority of this optimization (0 by default which means first in the list)\r\n     * @returns the current SceneOptimizerOptions\r\n     */\r\n    public addCustomOptimization(onApply: (scene: Scene, optimizer: SceneOptimizer) => boolean, onGetDescription: () => string, priority: number = 0): SceneOptimizerOptions {\r\n        const optimization = new CustomOptimization(priority);\r\n        optimization.onApply = onApply;\r\n        optimization.onGetDescription = onGetDescription;\r\n\r\n        this.optimizations.push(optimization);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Creates a list of pre-defined optimizations aimed to reduce the visual impact on the scene\r\n     * @param targetFrameRate defines the target frame rate (60 by default)\r\n     * @returns a SceneOptimizerOptions object\r\n     */\r\n    public static LowDegradationAllowed(targetFrameRate?: number): SceneOptimizerOptions {\r\n        const result = new SceneOptimizerOptions(targetFrameRate);\r\n\r\n        let priority = 0;\r\n        result.addOptimization(new MergeMeshesOptimization(priority));\r\n        result.addOptimization(new ShadowsOptimization(priority));\r\n        result.addOptimization(new LensFlaresOptimization(priority));\r\n\r\n        // Next priority\r\n        priority++;\r\n        result.addOptimization(new PostProcessesOptimization(priority));\r\n        result.addOptimization(new ParticlesOptimization(priority));\r\n\r\n        // Next priority\r\n        priority++;\r\n        result.addOptimization(new TextureOptimization(priority, 1024));\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Creates a list of pre-defined optimizations aimed to have a moderate impact on the scene visual\r\n     * @param targetFrameRate defines the target frame rate (60 by default)\r\n     * @returns a SceneOptimizerOptions object\r\n     */\r\n    public static ModerateDegradationAllowed(targetFrameRate?: number): SceneOptimizerOptions {\r\n        const result = new SceneOptimizerOptions(targetFrameRate);\r\n\r\n        let priority = 0;\r\n        result.addOptimization(new MergeMeshesOptimization(priority));\r\n        result.addOptimization(new ShadowsOptimization(priority));\r\n        result.addOptimization(new LensFlaresOptimization(priority));\r\n\r\n        // Next priority\r\n        priority++;\r\n        result.addOptimization(new PostProcessesOptimization(priority));\r\n        result.addOptimization(new ParticlesOptimization(priority));\r\n\r\n        // Next priority\r\n        priority++;\r\n        result.addOptimization(new TextureOptimization(priority, 512));\r\n\r\n        // Next priority\r\n        priority++;\r\n        result.addOptimization(new RenderTargetsOptimization(priority));\r\n\r\n        // Next priority\r\n        priority++;\r\n        result.addOptimization(new HardwareScalingOptimization(priority, 2));\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Creates a list of pre-defined optimizations aimed to have a big impact on the scene visual\r\n     * @param targetFrameRate defines the target frame rate (60 by default)\r\n     * @returns a SceneOptimizerOptions object\r\n     */\r\n    public static HighDegradationAllowed(targetFrameRate?: number): SceneOptimizerOptions {\r\n        const result = new SceneOptimizerOptions(targetFrameRate);\r\n\r\n        let priority = 0;\r\n        result.addOptimization(new MergeMeshesOptimization(priority));\r\n        result.addOptimization(new ShadowsOptimization(priority));\r\n        result.addOptimization(new LensFlaresOptimization(priority));\r\n\r\n        // Next priority\r\n        priority++;\r\n        result.addOptimization(new PostProcessesOptimization(priority));\r\n        result.addOptimization(new ParticlesOptimization(priority));\r\n\r\n        // Next priority\r\n        priority++;\r\n        result.addOptimization(new TextureOptimization(priority, 256));\r\n\r\n        // Next priority\r\n        priority++;\r\n        result.addOptimization(new RenderTargetsOptimization(priority));\r\n\r\n        // Next priority\r\n        priority++;\r\n        result.addOptimization(new HardwareScalingOptimization(priority, 4));\r\n\r\n        return result;\r\n    }\r\n}\r\n\r\n/**\r\n * Class used to run optimizations in order to reach a target frame rate\r\n * @description More details at https://doc.babylonjs.com/features/featuresDeepDive/scene/sceneOptimizer\r\n */\r\nexport class SceneOptimizer implements IDisposable {\r\n    private _isRunning = false;\r\n    private _options: SceneOptimizerOptions;\r\n    private _scene: Scene;\r\n    private _currentPriorityLevel = 0;\r\n    private _targetFrameRate = 60;\r\n    private _trackerDuration = 2000;\r\n    private _currentFrameRate = 0;\r\n    private _sceneDisposeObserver: Nullable<Observer<Scene>>;\r\n    private _improvementMode = false;\r\n\r\n    /**\r\n     * Defines an observable called when the optimizer reaches the target frame rate\r\n     */\r\n    public onSuccessObservable = new Observable<SceneOptimizer>();\r\n    /**\r\n     * Defines an observable called when the optimizer enables an optimization\r\n     */\r\n    public onNewOptimizationAppliedObservable = new Observable<SceneOptimization>();\r\n    /**\r\n     * Defines an observable called when the optimizer is not able to reach the target frame rate\r\n     */\r\n    public onFailureObservable = new Observable<SceneOptimizer>();\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the optimizer is in improvement mode\r\n     */\r\n    public get isInImprovementMode(): boolean {\r\n        return this._improvementMode;\r\n    }\r\n\r\n    public set isInImprovementMode(value: boolean) {\r\n        this._improvementMode = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the current priority level (0 at start)\r\n     */\r\n    public get currentPriorityLevel(): number {\r\n        return this._currentPriorityLevel;\r\n    }\r\n\r\n    /**\r\n     * Gets the current frame rate checked by the SceneOptimizer\r\n     */\r\n    public get currentFrameRate(): number {\r\n        return this._currentFrameRate;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the current target frame rate (60 by default)\r\n     */\r\n    public get targetFrameRate(): number {\r\n        return this._targetFrameRate;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the current target frame rate (60 by default)\r\n     */\r\n    public set targetFrameRate(value: number) {\r\n        this._targetFrameRate = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the current interval between two checks (every 2000ms by default)\r\n     */\r\n    public get trackerDuration(): number {\r\n        return this._trackerDuration;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the current interval between two checks (every 2000ms by default)\r\n     */\r\n    public set trackerDuration(value: number) {\r\n        this._trackerDuration = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of active optimizations\r\n     */\r\n    public get optimizations(): SceneOptimization[] {\r\n        return this._options.optimizations;\r\n    }\r\n\r\n    /**\r\n     * Creates a new SceneOptimizer\r\n     * @param scene defines the scene to work on\r\n     * @param options defines the options to use with the SceneOptimizer\r\n     * @param autoGeneratePriorities defines if priorities must be generated and not read from SceneOptimization property (true by default)\r\n     * @param improvementMode defines if the scene optimizer must run the maximum optimization while staying over a target frame instead of trying to reach the target framerate (false by default)\r\n     */\r\n    public constructor(scene: Scene, options?: SceneOptimizerOptions, autoGeneratePriorities = true, improvementMode = false) {\r\n        if (!options) {\r\n            this._options = new SceneOptimizerOptions();\r\n        } else {\r\n            this._options = options;\r\n        }\r\n\r\n        if (this._options.targetFrameRate) {\r\n            this._targetFrameRate = this._options.targetFrameRate;\r\n        }\r\n\r\n        if (this._options.trackerDuration) {\r\n            this._trackerDuration = this._options.trackerDuration;\r\n        }\r\n\r\n        if (autoGeneratePriorities) {\r\n            let priority = 0;\r\n            for (const optim of this._options.optimizations) {\r\n                optim.priority = priority++;\r\n            }\r\n        }\r\n\r\n        this._improvementMode = improvementMode;\r\n        this._scene = scene || EngineStore.LastCreatedScene;\r\n        this._sceneDisposeObserver = this._scene.onDisposeObservable.add(() => {\r\n            this._sceneDisposeObserver = null;\r\n            this.dispose();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Stops the current optimizer\r\n     */\r\n    public stop() {\r\n        this._isRunning = false;\r\n    }\r\n\r\n    /**\r\n     * Reset the optimizer to initial step (current priority level = 0)\r\n     */\r\n    public reset() {\r\n        this._currentPriorityLevel = 0;\r\n    }\r\n\r\n    /**\r\n     * Start the optimizer. By default it will try to reach a specific framerate\r\n     * but if the optimizer is set with improvementMode === true then it will run all optimization while frame rate is above the target frame rate\r\n     */\r\n    public start() {\r\n        if (this._isRunning) {\r\n            return;\r\n        }\r\n\r\n        this._isRunning = true;\r\n\r\n        // Let's wait for the scene to be ready before running our check\r\n        this._scene.executeWhenReady(() => {\r\n            setTimeout(() => {\r\n                this._checkCurrentState();\r\n            }, this._trackerDuration);\r\n        });\r\n    }\r\n\r\n    private _checkCurrentState() {\r\n        if (!this._isRunning) {\r\n            return;\r\n        }\r\n\r\n        const scene = this._scene;\r\n        const options = this._options;\r\n\r\n        this._currentFrameRate = Math.round(scene.getEngine().getFps());\r\n\r\n        if ((this._improvementMode && this._currentFrameRate <= this._targetFrameRate) || (!this._improvementMode && this._currentFrameRate >= this._targetFrameRate)) {\r\n            this._isRunning = false;\r\n            this.onSuccessObservable.notifyObservers(this);\r\n            return;\r\n        }\r\n\r\n        // Apply current level of optimizations\r\n        let allDone = true;\r\n        let noOptimizationApplied = true;\r\n        for (let index = 0; index < options.optimizations.length; index++) {\r\n            const optimization = options.optimizations[index];\r\n\r\n            if (optimization.priority === this._currentPriorityLevel) {\r\n                noOptimizationApplied = false;\r\n                allDone = allDone && optimization.apply(scene, this);\r\n                this.onNewOptimizationAppliedObservable.notifyObservers(optimization);\r\n            }\r\n        }\r\n\r\n        // If no optimization was applied, this is a failure :(\r\n        if (noOptimizationApplied) {\r\n            this._isRunning = false;\r\n            this.onFailureObservable.notifyObservers(this);\r\n\r\n            return;\r\n        }\r\n\r\n        // If all optimizations were done, move to next level\r\n        if (allDone) {\r\n            this._currentPriorityLevel++;\r\n        }\r\n\r\n        // Let's the system running for a specific amount of time before checking FPS\r\n        scene.executeWhenReady(() => {\r\n            setTimeout(() => {\r\n                this._checkCurrentState();\r\n            }, this._trackerDuration);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Release all resources\r\n     */\r\n    public dispose(): void {\r\n        this.stop();\r\n        this.onSuccessObservable.clear();\r\n        this.onFailureObservable.clear();\r\n        this.onNewOptimizationAppliedObservable.clear();\r\n        if (this._sceneDisposeObserver) {\r\n            this._scene.onDisposeObservable.remove(this._sceneDisposeObserver);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Helper function to create a SceneOptimizer with one single line of code\r\n     * @param scene defines the scene to work on\r\n     * @param options defines the options to use with the SceneOptimizer\r\n     * @param onSuccess defines a callback to call on success\r\n     * @param onFailure defines a callback to call on failure\r\n     * @returns the new SceneOptimizer object\r\n     */\r\n    public static OptimizeAsync(scene: Scene, options?: SceneOptimizerOptions, onSuccess?: () => void, onFailure?: () => void): SceneOptimizer {\r\n        const optimizer = new SceneOptimizer(scene, options || SceneOptimizerOptions.ModerateDegradationAllowed(), false);\r\n\r\n        if (onSuccess) {\r\n            optimizer.onSuccessObservable.add(() => {\r\n                onSuccess();\r\n            });\r\n        }\r\n\r\n        if (onFailure) {\r\n            optimizer.onFailureObservable.add(() => {\r\n                onFailure();\r\n            });\r\n        }\r\n\r\n        optimizer.start();\r\n\r\n        return optimizer;\r\n    }\r\n}\r\n"]}