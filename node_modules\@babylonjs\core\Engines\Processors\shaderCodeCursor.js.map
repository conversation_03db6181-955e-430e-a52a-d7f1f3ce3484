{"version": 3, "file": "shaderCodeCursor.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Processors/shaderCodeCursor.ts"], "names": [], "mappings": "AAAA,gBAAgB;AAChB,MAAM,OAAO,gBAAgB;IAA7B;QACY,WAAM,GAAa,EAAE,CAAC;IAwElC,CAAC;IArEG,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvC,CAAC;IAED,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IACnD,CAAC;IAED,IAAI,KAAK,CAAC,KAAe;QACrB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAEvB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACtB,mBAAmB;YACnB,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;gBACxB,SAAS;aACZ;YAED,yCAAyC;YACzC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvB,SAAS;aACZ;YAED,oCAAoC;YACpC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAEhC,IAAI,CAAC,WAAW,EAAE;gBACd,SAAS;aACZ;YAED,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvB,SAAS;aACZ;YAED,kCAAkC;YAClC,MAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAEhD,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE;gBACvB,2BAA2B;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aACjC;iBAAM,IAAI,cAAc,KAAK,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClD,0CAA0C;gBAC1C,wFAAwF;gBACxF,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;oBACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBACjC;aACJ;iBAAM;gBACH,sCAAsC;gBACtC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAE9B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBAC/C,IAAI,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;oBAE3B,IAAI,CAAC,OAAO,EAAE;wBACV,SAAS;qBACZ;oBAED,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;oBAEzB,IAAI,CAAC,OAAO,EAAE;wBACV,SAAS;qBACZ;oBAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACvE;aACJ;SACJ;IACL,CAAC;CACJ", "sourcesContent": ["/** @internal */\r\nexport class ShaderCodeCursor {\r\n    private _lines: string[] = [];\r\n    lineIndex: number;\r\n\r\n    get currentLine(): string {\r\n        return this._lines[this.lineIndex];\r\n    }\r\n\r\n    get canRead(): boolean {\r\n        return this.lineIndex < this._lines.length - 1;\r\n    }\r\n\r\n    set lines(value: string[]) {\r\n        this._lines.length = 0;\r\n\r\n        for (const line of value) {\r\n            // Skip empty lines\r\n            if (!line || line === \"\\r\") {\r\n                continue;\r\n            }\r\n\r\n            // Prevent removing line break in macros.\r\n            if (line[0] === \"#\") {\r\n                this._lines.push(line);\r\n                continue;\r\n            }\r\n\r\n            // Do not split single line comments\r\n            const trimmedLine = line.trim();\r\n\r\n            if (!trimmedLine) {\r\n                continue;\r\n            }\r\n\r\n            if (trimmedLine.startsWith(\"//\")) {\r\n                this._lines.push(line);\r\n                continue;\r\n            }\r\n\r\n            // Work with semicolon in the line\r\n            const semicolonIndex = trimmedLine.indexOf(\";\");\r\n\r\n            if (semicolonIndex === -1) {\r\n                // No semicolon in the line\r\n                this._lines.push(trimmedLine);\r\n            } else if (semicolonIndex === trimmedLine.length - 1) {\r\n                // Single semicolon at the end of the line\r\n                // If trimmedLine == \";\", we must not push, to be backward compatible with the old code!\r\n                if (trimmedLine.length > 1) {\r\n                    this._lines.push(trimmedLine);\r\n                }\r\n            } else {\r\n                // Semicolon in the middle of the line\r\n                const split = line.split(\";\");\r\n\r\n                for (let index = 0; index < split.length; index++) {\r\n                    let subLine = split[index];\r\n\r\n                    if (!subLine) {\r\n                        continue;\r\n                    }\r\n\r\n                    subLine = subLine.trim();\r\n\r\n                    if (!subLine) {\r\n                        continue;\r\n                    }\r\n\r\n                    this._lines.push(subLine + (index !== split.length - 1 ? \";\" : \"\"));\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}