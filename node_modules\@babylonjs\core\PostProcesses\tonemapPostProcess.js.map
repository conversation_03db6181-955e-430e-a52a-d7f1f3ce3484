{"version": 3, "file": "tonemapPostProcess.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/tonemapPostProcess.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEjD,OAAO,6BAA6B,CAAC;AAKrC,4CAA4C;AAC5C,MAAM,CAAN,IAAY,mBASX;AATD,WAAY,mBAAmB;IAC3B,YAAY;IACZ,+DAAS,CAAA;IACT,eAAe;IACf,qEAAY,CAAA;IACZ,iBAAiB;IACjB,yEAAc,CAAA;IACd,mBAAmB;IACnB,6EAAgB,CAAA;AACpB,CAAC,EATW,mBAAmB,KAAnB,mBAAmB,QAS9B;AAED;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,WAAW;IAC/C;;;OAGG;IACI,YAAY;QACf,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;;;;;;;;;OAUG;IACH,YACI,IAAY,EACJ,SAA8B;IACtC,+CAA+C;IACxC,kBAA0B,EACjC,MAAwB,EACxB,eAAuB,SAAS,CAAC,6BAA6B,EAC9D,MAAe,EACf,aAAa,GAAG,SAAS,CAAC,wBAAwB,EAClD,QAAkB;QAElB,KAAK,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,qBAAqB,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;QAThH,cAAS,GAAT,SAAS,CAAqB;QAE/B,uBAAkB,GAAlB,kBAAkB,CAAQ;QASjC,IAAI,OAAO,GAAG,UAAU,CAAC;QAEzB,IAAI,IAAI,CAAC,SAAS,KAAK,mBAAmB,CAAC,KAAK,EAAE;YAC9C,OAAO,IAAI,mBAAmB,CAAC;SAClC;aAAM,IAAI,IAAI,CAAC,SAAS,KAAK,mBAAmB,CAAC,QAAQ,EAAE;YACxD,OAAO,IAAI,sBAAsB,CAAC;SACrC;aAAM,IAAI,IAAI,CAAC,SAAS,KAAK,mBAAmB,CAAC,UAAU,EAAE;YAC1D,OAAO,IAAI,kCAAkC,CAAC;SACjD;aAAM,IAAI,IAAI,CAAC,SAAS,KAAK,mBAAmB,CAAC,YAAY,EAAE;YAC5D,OAAO,IAAI,0BAA0B,CAAC;SACzC;QAED,2CAA2C;QAC3C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAE3B,IAAI,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YAC9B,MAAM,CAAC,QAAQ,CAAC,qBAAqB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACpE,CAAC,CAAC;IACN,CAAC;CACJ", "sourcesContent": ["import type { Camera } from \"../Cameras/camera\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { PostProcess } from \"./postProcess\";\r\nimport { Constants } from \"../Engines/constants\";\r\n\r\nimport \"../Shaders/tonemap.fragment\";\r\nimport type { Nullable } from \"../types\";\r\n\r\nimport type { Engine } from \"../Engines/engine\";\r\n\r\n/** Defines operator used for tonemapping */\r\nexport enum TonemappingOperator {\r\n    /** Hable */\r\n    Hable = 0,\r\n    /** Reinhard */\r\n    Reinhard = 1,\r\n    /** Heji<PERSON>awson */\r\n    HejiDawson = 2,\r\n    /** Photographic */\r\n    Photographic = 3,\r\n}\r\n\r\n/**\r\n * Defines a post process to apply tone mapping\r\n */\r\nexport class TonemapPostProcess extends PostProcess {\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"TonemapPostProcess\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"TonemapPostProcess\";\r\n    }\r\n\r\n    /**\r\n     * Creates a new TonemapPostProcess\r\n     * @param name defines the name of the postprocess\r\n     * @param _operator defines the operator to use\r\n     * @param exposureAdjustment defines the required exposure adjustment\r\n     * @param camera defines the camera to use (can be null)\r\n     * @param samplingMode defines the required sampling mode (BABYLON.Texture.BILINEAR_SAMPLINGMODE by default)\r\n     * @param engine defines the hosting engine (can be ignore if camera is set)\r\n     * @param textureFormat defines the texture format to use (BABYLON.Engine.TEXTURETYPE_UNSIGNED_INT by default)\r\n     * @param reusable If the post process can be reused on the same frame. (default: false)\r\n     */\r\n    constructor(\r\n        name: string,\r\n        private _operator: TonemappingOperator,\r\n        /** Defines the required exposure adjustment */\r\n        public exposureAdjustment: number,\r\n        camera: Nullable<Camera>,\r\n        samplingMode: number = Constants.TEXTURE_BILINEAR_SAMPLINGMODE,\r\n        engine?: Engine,\r\n        textureFormat = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        reusable?: boolean\r\n    ) {\r\n        super(name, \"tonemap\", [\"_ExposureAdjustment\"], null, 1.0, camera, samplingMode, engine, reusable, null, textureFormat);\r\n\r\n        let defines = \"#define \";\r\n\r\n        if (this._operator === TonemappingOperator.Hable) {\r\n            defines += \"HABLE_TONEMAPPING\";\r\n        } else if (this._operator === TonemappingOperator.Reinhard) {\r\n            defines += \"REINHARD_TONEMAPPING\";\r\n        } else if (this._operator === TonemappingOperator.HejiDawson) {\r\n            defines += \"OPTIMIZED_HEJIDAWSON_TONEMAPPING\";\r\n        } else if (this._operator === TonemappingOperator.Photographic) {\r\n            defines += \"PHOTOGRAPHIC_TONEMAPPING\";\r\n        }\r\n\r\n        //sadly a second call to create the effect.\r\n        this.updateEffect(defines);\r\n\r\n        this.onApply = (effect: Effect) => {\r\n            effect.setFloat(\"_ExposureAdjustment\", this.exposureAdjustment);\r\n        };\r\n    }\r\n}\r\n"]}