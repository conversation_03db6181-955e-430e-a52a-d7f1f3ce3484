{"version": 3, "file": "tiledPlaneBuilder.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Builders/tiledPlaneBuilder.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,MAAM,UAAU,0BAA0B,CAAC,OAa1C;IACG,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;IACjD,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;IAC7D,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;IAC/D,MAAM,MAAM,GAAG,OAAO,CAAC,eAAe,IAAI,CAAC,CAAC;IAC5C,MAAM,MAAM,GAAG,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC;IAE1C,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;IACjD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC;IAC7C,IAAI,OAAO,GAAG,KAAK,GAAG,MAAM,GAAG,SAAS,CAAC;IAEzC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;IACnD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;IAC/C,IAAI,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,UAAU,CAAC;IAE3C,MAAM,SAAS,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,UAAU,GAAG,CAAC,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;IAE7C,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,IAAI,GAAG,CAAC,CAAC;IAEb,YAAY;IACZ,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,EAAE;QAC5B,MAAM,GAAG,CAAC,SAAS,CAAC;QACpB,MAAM,GAAG,CAAC,UAAU,CAAC;QACrB,IAAI,GAAG,SAAS,CAAC;QACjB,IAAI,GAAG,UAAU,CAAC;QAElB,QAAQ,MAAM,EAAE;YACZ,KAAK,IAAI,CAAC,MAAM;gBACZ,OAAO,IAAI,CAAC,CAAC;gBACb,MAAM,IAAI,OAAO,CAAC;gBAClB,IAAI,IAAI,OAAO,CAAC;gBAChB,MAAM;YACV,KAAK,IAAI,CAAC,IAAI;gBACV,IAAI,IAAI,OAAO,CAAC;gBAChB,OAAO,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC;gBACvB,MAAM;YACV,KAAK,IAAI,CAAC,KAAK;gBACX,MAAM,IAAI,OAAO,CAAC;gBAClB,OAAO,GAAG,OAAO,GAAG,CAAC,CAAC;gBACtB,MAAM;SACb;QAED,QAAQ,MAAM,EAAE;YACZ,KAAK,IAAI,CAAC,MAAM;gBACZ,OAAO,IAAI,CAAC,CAAC;gBACb,MAAM,IAAI,OAAO,CAAC;gBAClB,IAAI,IAAI,OAAO,CAAC;gBAChB,MAAM;YACV,KAAK,IAAI,CAAC,MAAM;gBACZ,IAAI,IAAI,OAAO,CAAC;gBAChB,OAAO,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC;gBACvB,MAAM;YACV,KAAK,IAAI,CAAC,GAAG;gBACT,MAAM,IAAI,OAAO,CAAC;gBAClB,OAAO,GAAG,OAAO,GAAG,CAAC,CAAC;gBACtB,MAAM;SACb;KACJ;IAED,MAAM,SAAS,GAAG,EAAE,CAAC;IACrB,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACrC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACrC,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,EAAE;QAC/D,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACxC;IACD,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;QAC3D,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACxC;IACD,IAAI,QAAQ,KAAK,IAAI,CAAC,kBAAkB,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;QAC7E,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACxC;IACD,IAAI,GAAG,GAAkB,EAAE,CAAC;IAC5B,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7B,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO,EAAE,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YAChG,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,OAAO,EAAE,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YACtG,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,OAAO,EAAE,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YAC5G,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO,EAAE,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YACtG,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAC3E,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,kBAAkB,EAAE;gBACtG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACrD;iBAAM,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;gBAC1G,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aACnC;iBAAM;gBACH,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;aAC/B;YACD,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5D,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACrD,KAAK,IAAI,CAAC,CAAC;SACd;KACJ;IAED,YAAY;IACZ,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,EAAE;QAC5B,MAAM,gBAAgB,GAAY,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC;QACjG,MAAM,aAAa,GAAY,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;QACjG,MAAM,cAAc,GAAY,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC;QACjG,MAAM,eAAe,GAAY,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC;QACjG,IAAI,MAAM,GAAkB,EAAE,CAAC;QAC/B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAS,CAAC;QAEvB,SAAS;QACT,IAAI,gBAAgB,IAAI,cAAc,EAAE;YACpC,oBAAoB;YACpB,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YACtD,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YAC1D,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YACpE,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YAChE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAC3E,KAAK,IAAI,CAAC,CAAC;YACX,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;YAC5B,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,UAAU,CAAC;YAC7B,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;YACN,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAClC,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,EAAE;gBAC9B,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aACrE;YACD,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAC5B,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aACrD;YACD,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;gBACrC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aACrD;YACD,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5D,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACxD;QAED,IAAI,gBAAgB,IAAI,eAAe,EAAE;YACrC,qBAAqB;YACrB,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YACzD,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YACpD,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YAC9D,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YACnE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAC3E,KAAK,IAAI,CAAC,CAAC;YACX,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,UAAU,CAAC;YAC7B,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;YACxB,CAAC,GAAG,CAAC,CAAC;YACN,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAClC,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBACrF,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aACrE;YACD,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBACjF,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aACrD;YACD,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,kBAAkB,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBACnG,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aACrD;YACD,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5D,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACxD;QAED,IAAI,aAAa,IAAI,cAAc,EAAE;YACjC,iBAAiB;YACjB,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,EAAE,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YAC1D,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,OAAO,EAAE,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YAC9D,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,OAAO,EAAE,IAAI,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YACxD,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,EAAE,IAAI,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAC3E,KAAK,IAAI,CAAC,CAAC;YACX,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;YAC5B,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,OAAO,GAAG,UAAU,CAAC;YACzB,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAClC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,UAAU,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC3G,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aACrE;YACD,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBACvG,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aACrD;YACD,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,iBAAiB,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,kBAAkB,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBACzH,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aACrD;YACD,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5D,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACxD;QAED,IAAI,aAAa,IAAI,eAAe,EAAE;YAClC,kBAAkB;YAClB,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YAC7D,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YACxD,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,IAAI,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YAClD,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE,IAAI,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;YACvD,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAC3E,KAAK,IAAI,CAAC,CAAC;YACX,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;YACxB,CAAC,GAAG,OAAO,GAAG,UAAU,CAAC;YACzB,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAClC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,UAAU,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBACtH,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aACrE;YACD,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBAClH,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aACrD;YACD,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,iBAAiB,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,kBAAkB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBACpI,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aACrD;YACD,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5D,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACxD;QAED,WAAW;QACX,IAAI,gBAAgB,EAAE;YAClB,MAAM,QAAQ,GAAG,EAAE,CAAC;YACpB,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,UAAU,CAAC;YAC7B,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;YACN,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,EAAE;gBAC/D,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aAC1E;YACD,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAC3D,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aAC1D;YACD,IAAI,QAAQ,KAAK,IAAI,CAAC,kBAAkB,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;gBAC7E,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aAC1D;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC7B,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC1E,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBAChF,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC1F,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO,EAAE,MAAM,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBACpF,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC3E,KAAK,IAAI,CAAC,CAAC;gBACX,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,kBAAkB,EAAE;oBACtG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBAC3C;qBAAM,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;oBAC1G,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;iBACjC;qBAAM;oBACH,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;iBACjC;gBACD,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5D,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aACxD;SACJ;QAED,IAAI,aAAa,EAAE;YACf,MAAM,QAAQ,GAAG,EAAE,CAAC;YACpB,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,OAAO,GAAG,UAAU,CAAC;YACzB,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,EAAE;gBAC/D,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aAC1E;YACD,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAC3D,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aAC1D;YACD,IAAI,QAAQ,KAAK,IAAI,CAAC,kBAAkB,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;gBAC7E,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aAC1D;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC7B,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO,EAAE,IAAI,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBAClF,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,OAAO,EAAE,IAAI,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBACxF,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,OAAO,EAAE,IAAI,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC9E,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,OAAO,EAAE,IAAI,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBACxE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC3E,KAAK,IAAI,CAAC,CAAC;gBACX,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,kBAAkB,EAAE;oBACtG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBAChD;qBAAM,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;oBAC1G,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;iBAC1C;qBAAM;oBACH,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;iBACjC;gBACD,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5D,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aACxD;SACJ;QAED,IAAI,cAAc,EAAE;YAChB,MAAM,QAAQ,GAAG,EAAE,CAAC;YACpB,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;YAC5B,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;YACN,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,EAAE;gBAC/D,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aAC1E;YACD,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAC3D,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aAC1D;YACD,IAAI,QAAQ,KAAK,IAAI,CAAC,kBAAkB,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;gBAC7E,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aAC1D;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC7B,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,EAAE,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC5E,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBACtF,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC5F,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,EAAE,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBAClF,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC3E,KAAK,IAAI,CAAC,CAAC;gBACX,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,kBAAkB,EAAE;oBACtG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBAC3C;qBAAM,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;oBAC1G,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBACrC;qBAAM;oBACH,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;iBACjC;gBACD,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5D,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aACxD;SACJ;QAED,IAAI,eAAe,EAAE;YACjB,MAAM,QAAQ,GAAG,EAAE,CAAC;YACpB,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,OAAO,GAAG,UAAU,CAAC;YACzB,CAAC,GAAG,CAAC,CAAC;YACN,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,EAAE;gBAC/D,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aAC1E;YACD,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAC3D,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aAC1D;YACD,IAAI,QAAQ,KAAK,IAAI,CAAC,kBAAkB,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;gBAC7E,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;aAC1D;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC7B,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBACpF,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC1E,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBAChF,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,GAAG,OAAO,EAAE,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC1F,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC3E,KAAK,IAAI,CAAC,CAAC;gBACX,IAAI,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,IAAI,QAAQ,KAAK,IAAI,CAAC,kBAAkB,EAAE;oBACtG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBAChD;qBAAM,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE;oBAC1G,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBACrC;qBAAM;oBACH,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;iBACjC;gBACD,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5D,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aACxD;SACJ;KACJ;IAED,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,IAAI,UAAU,CAAC,WAAW,CAAC;IAE9G,QAAQ;IACR,UAAU,CAAC,aAAa,CAAC,eAAe,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IAE/G,SAAS;IACT,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IAEpC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;IACjC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;IAErB,MAAM,WAAW,GAAG,eAAe,KAAK,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAC/F,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC;IAEhC,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgCG;AACH,MAAM,UAAU,gBAAgB,CAC5B,IAAY,EACZ,OAcC,EACD,QAAyB,IAAI;IAE7B,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAEpC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACnF,KAAK,CAAC,+BAA+B,GAAG,OAAO,CAAC,eAAe,CAAC;IAEhE,MAAM,UAAU,GAAG,0BAA0B,CAAC,OAAO,CAAC,CAAC;IAEvD,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAEjD,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG;IAC7B,gEAAgE;IAChE,gBAAgB;CACnB,CAAC;AAEF,UAAU,CAAC,gBAAgB,GAAG,0BAA0B,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Vector4 } from \"../../Maths/math.vector\";\r\nimport { Mesh } from \"../mesh\";\r\nimport { VertexData } from \"../mesh.vertexData\";\r\n\r\n/**\r\n * Creates the VertexData for a tiled plane\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set/tiled_plane\r\n * @param options an object used to set the following optional parameters for the tiled plane, required but can be empty\r\n * * pattern a limited pattern arrangement depending on the number\r\n * * size of the box\r\n * * width of the box, overwrites size\r\n * * height of the box, overwrites size\r\n * * tileSize sets the width, height and depth of the tile to the value of size, optional default 1\r\n * * tileWidth sets the width (x direction) of the tile, overwrites the width set by size, optional, default size\r\n * * tileHeight sets the height (y direction) of the tile, overwrites the height set by size, optional, default size\r\n * * sideOrientation optional and takes the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n * * alignHorizontal places whole tiles aligned to the center, left or right of a row\r\n * * alignVertical places whole tiles aligned to the center, left or right of a column\r\n * * frontUvs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the front side, optional, default vector4 (0, 0, 1, 1)\r\n * @param options.pattern\r\n * @param options.tileSize\r\n * @param options.tileWidth\r\n * @param options.tileHeight\r\n * @param options.size\r\n * @param options.width\r\n * @param options.height\r\n * @param options.alignHorizontal\r\n * @param options.alignVertical\r\n * @param options.sideOrientation\r\n * @param options.frontUVs\r\n * @param options.backUVs\r\n * * backUVs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the back side, optional, default vector4 (0, 0, 1, 1)\r\n * @returns the VertexData of the tiled plane\r\n */\r\nexport function CreateTiledPlaneVertexData(options: {\r\n    pattern?: number;\r\n    tileSize?: number;\r\n    tileWidth?: number;\r\n    tileHeight?: number;\r\n    size?: number;\r\n    width?: number;\r\n    height?: number;\r\n    alignHorizontal?: number;\r\n    alignVertical?: number;\r\n    sideOrientation?: number;\r\n    frontUVs?: Vector4;\r\n    backUVs?: Vector4;\r\n}): VertexData {\r\n    const flipTile = options.pattern || Mesh.NO_FLIP;\r\n    const tileWidth = options.tileWidth || options.tileSize || 1;\r\n    const tileHeight = options.tileHeight || options.tileSize || 1;\r\n    const alignH = options.alignHorizontal || 0;\r\n    const alignV = options.alignVertical || 0;\r\n\r\n    const width = options.width || options.size || 1;\r\n    const tilesX = Math.floor(width / tileWidth);\r\n    let offsetX = width - tilesX * tileWidth;\r\n\r\n    const height = options.height || options.size || 1;\r\n    const tilesY = Math.floor(height / tileHeight);\r\n    let offsetY = height - tilesY * tileHeight;\r\n\r\n    const halfWidth = (tileWidth * tilesX) / 2;\r\n    const halfHeight = (tileHeight * tilesY) / 2;\r\n\r\n    let adjustX = 0;\r\n    let adjustY = 0;\r\n    let startX = 0;\r\n    let startY = 0;\r\n    let endX = 0;\r\n    let endY = 0;\r\n\r\n    //Part Tiles\r\n    if (offsetX > 0 || offsetY > 0) {\r\n        startX = -halfWidth;\r\n        startY = -halfHeight;\r\n        endX = halfWidth;\r\n        endY = halfHeight;\r\n\r\n        switch (alignH) {\r\n            case Mesh.CENTER:\r\n                offsetX /= 2;\r\n                startX -= offsetX;\r\n                endX += offsetX;\r\n                break;\r\n            case Mesh.LEFT:\r\n                endX += offsetX;\r\n                adjustX = -offsetX / 2;\r\n                break;\r\n            case Mesh.RIGHT:\r\n                startX -= offsetX;\r\n                adjustX = offsetX / 2;\r\n                break;\r\n        }\r\n\r\n        switch (alignV) {\r\n            case Mesh.CENTER:\r\n                offsetY /= 2;\r\n                startY -= offsetY;\r\n                endY += offsetY;\r\n                break;\r\n            case Mesh.BOTTOM:\r\n                endY += offsetY;\r\n                adjustY = -offsetY / 2;\r\n                break;\r\n            case Mesh.TOP:\r\n                startY -= offsetY;\r\n                adjustY = offsetY / 2;\r\n                break;\r\n        }\r\n    }\r\n\r\n    const positions = [];\r\n    const normals = [];\r\n    const uvBase = [];\r\n    uvBase[0] = [0, 0, 1, 0, 1, 1, 0, 1];\r\n    uvBase[1] = [0, 0, 1, 0, 1, 1, 0, 1];\r\n    if (flipTile === Mesh.ROTATE_TILE || flipTile === Mesh.ROTATE_ROW) {\r\n        uvBase[1] = [1, 1, 0, 1, 0, 0, 1, 0];\r\n    }\r\n    if (flipTile === Mesh.FLIP_TILE || flipTile === Mesh.FLIP_ROW) {\r\n        uvBase[1] = [1, 0, 0, 0, 0, 1, 1, 1];\r\n    }\r\n    if (flipTile === Mesh.FLIP_N_ROTATE_TILE || flipTile === Mesh.FLIP_N_ROTATE_ROW) {\r\n        uvBase[1] = [0, 1, 1, 1, 1, 0, 0, 0];\r\n    }\r\n    let uvs: Array<number> = [];\r\n    const colors = [];\r\n    const indices = [];\r\n    let index = 0;\r\n    for (let y = 0; y < tilesY; y++) {\r\n        for (let x = 0; x < tilesX; x++) {\r\n            positions.push(-halfWidth + x * tileWidth + adjustX, -halfHeight + y * tileHeight + adjustY, 0);\r\n            positions.push(-halfWidth + (x + 1) * tileWidth + adjustX, -halfHeight + y * tileHeight + adjustY, 0);\r\n            positions.push(-halfWidth + (x + 1) * tileWidth + adjustX, -halfHeight + (y + 1) * tileHeight + adjustY, 0);\r\n            positions.push(-halfWidth + x * tileWidth + adjustX, -halfHeight + (y + 1) * tileHeight + adjustY, 0);\r\n            indices.push(index, index + 1, index + 3, index + 1, index + 2, index + 3);\r\n            if (flipTile === Mesh.FLIP_TILE || flipTile === Mesh.ROTATE_TILE || flipTile === Mesh.FLIP_N_ROTATE_TILE) {\r\n                uvs = uvs.concat(uvBase[((x % 2) + (y % 2)) % 2]);\r\n            } else if (flipTile === Mesh.FLIP_ROW || flipTile === Mesh.ROTATE_ROW || flipTile === Mesh.FLIP_N_ROTATE_ROW) {\r\n                uvs = uvs.concat(uvBase[y % 2]);\r\n            } else {\r\n                uvs = uvs.concat(uvBase[0]);\r\n            }\r\n            colors.push(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1);\r\n            normals.push(0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1);\r\n            index += 4;\r\n        }\r\n    }\r\n\r\n    //Part Tiles\r\n    if (offsetX > 0 || offsetY > 0) {\r\n        const partialBottomRow: boolean = offsetY > 0 && (alignV === Mesh.CENTER || alignV === Mesh.TOP);\r\n        const partialTopRow: boolean = offsetY > 0 && (alignV === Mesh.CENTER || alignV === Mesh.BOTTOM);\r\n        const partialLeftCol: boolean = offsetX > 0 && (alignH === Mesh.CENTER || alignH === Mesh.RIGHT);\r\n        const partialRightCol: boolean = offsetX > 0 && (alignH === Mesh.CENTER || alignH === Mesh.LEFT);\r\n        let uvPart: Array<number> = [];\r\n        let a, b, c, d: number;\r\n\r\n        //corners\r\n        if (partialBottomRow && partialLeftCol) {\r\n            //bottom left corner\r\n            positions.push(startX + adjustX, startY + adjustY, 0);\r\n            positions.push(-halfWidth + adjustX, startY + adjustY, 0);\r\n            positions.push(-halfWidth + adjustX, startY + offsetY + adjustY, 0);\r\n            positions.push(startX + adjustX, startY + offsetY + adjustY, 0);\r\n            indices.push(index, index + 1, index + 3, index + 1, index + 2, index + 3);\r\n            index += 4;\r\n            a = 1 - offsetX / tileWidth;\r\n            b = 1 - offsetY / tileHeight;\r\n            c = 1;\r\n            d = 1;\r\n            uvPart = [a, b, c, b, c, d, a, d];\r\n            if (flipTile === Mesh.ROTATE_ROW) {\r\n                uvPart = [1 - a, 1 - b, 1 - c, 1 - b, 1 - c, 1 - d, 1 - a, 1 - d];\r\n            }\r\n            if (flipTile === Mesh.FLIP_ROW) {\r\n                uvPart = [1 - a, b, 1 - c, b, 1 - c, d, 1 - a, d];\r\n            }\r\n            if (flipTile === Mesh.FLIP_N_ROTATE_ROW) {\r\n                uvPart = [a, 1 - b, c, 1 - b, c, 1 - d, a, 1 - d];\r\n            }\r\n            uvs = uvs.concat(uvPart);\r\n            colors.push(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1);\r\n            normals.push(0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1);\r\n        }\r\n\r\n        if (partialBottomRow && partialRightCol) {\r\n            //bottom right corner\r\n            positions.push(halfWidth + adjustX, startY + adjustY, 0);\r\n            positions.push(endX + adjustX, startY + adjustY, 0);\r\n            positions.push(endX + adjustX, startY + offsetY + adjustY, 0);\r\n            positions.push(halfWidth + adjustX, startY + offsetY + adjustY, 0);\r\n            indices.push(index, index + 1, index + 3, index + 1, index + 2, index + 3);\r\n            index += 4;\r\n            a = 0;\r\n            b = 1 - offsetY / tileHeight;\r\n            c = offsetX / tileWidth;\r\n            d = 1;\r\n            uvPart = [a, b, c, b, c, d, a, d];\r\n            if (flipTile === Mesh.ROTATE_ROW || (flipTile === Mesh.ROTATE_TILE && tilesX % 2 === 0)) {\r\n                uvPart = [1 - a, 1 - b, 1 - c, 1 - b, 1 - c, 1 - d, 1 - a, 1 - d];\r\n            }\r\n            if (flipTile === Mesh.FLIP_ROW || (flipTile === Mesh.FLIP_TILE && tilesX % 2 === 0)) {\r\n                uvPart = [1 - a, b, 1 - c, b, 1 - c, d, 1 - a, d];\r\n            }\r\n            if (flipTile === Mesh.FLIP_N_ROTATE_ROW || (flipTile === Mesh.FLIP_N_ROTATE_TILE && tilesX % 2 === 0)) {\r\n                uvPart = [a, 1 - b, c, 1 - b, c, 1 - d, a, 1 - d];\r\n            }\r\n            uvs = uvs.concat(uvPart);\r\n            colors.push(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1);\r\n            normals.push(0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1);\r\n        }\r\n\r\n        if (partialTopRow && partialLeftCol) {\r\n            //top left corner\r\n            positions.push(startX + adjustX, halfHeight + adjustY, 0);\r\n            positions.push(-halfWidth + adjustX, halfHeight + adjustY, 0);\r\n            positions.push(-halfWidth + adjustX, endY + adjustY, 0);\r\n            positions.push(startX + adjustX, endY + adjustY, 0);\r\n            indices.push(index, index + 1, index + 3, index + 1, index + 2, index + 3);\r\n            index += 4;\r\n            a = 1 - offsetX / tileWidth;\r\n            b = 0;\r\n            c = 1;\r\n            d = offsetY / tileHeight;\r\n            uvPart = [a, b, c, b, c, d, a, d];\r\n            if ((flipTile === Mesh.ROTATE_ROW && tilesY % 2 === 1) || (flipTile === Mesh.ROTATE_TILE && tilesY % 1 === 0)) {\r\n                uvPart = [1 - a, 1 - b, 1 - c, 1 - b, 1 - c, 1 - d, 1 - a, 1 - d];\r\n            }\r\n            if ((flipTile === Mesh.FLIP_ROW && tilesY % 2 === 1) || (flipTile === Mesh.FLIP_TILE && tilesY % 2 === 0)) {\r\n                uvPart = [1 - a, b, 1 - c, b, 1 - c, d, 1 - a, d];\r\n            }\r\n            if ((flipTile === Mesh.FLIP_N_ROTATE_ROW && tilesY % 2 === 1) || (flipTile === Mesh.FLIP_N_ROTATE_TILE && tilesY % 2 === 0)) {\r\n                uvPart = [a, 1 - b, c, 1 - b, c, 1 - d, a, 1 - d];\r\n            }\r\n            uvs = uvs.concat(uvPart);\r\n            colors.push(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1);\r\n            normals.push(0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1);\r\n        }\r\n\r\n        if (partialTopRow && partialRightCol) {\r\n            //top right corner\r\n            positions.push(halfWidth + adjustX, halfHeight + adjustY, 0);\r\n            positions.push(endX + adjustX, halfHeight + adjustY, 0);\r\n            positions.push(endX + adjustX, endY + adjustY, 0);\r\n            positions.push(halfWidth + adjustX, endY + adjustY, 0);\r\n            indices.push(index, index + 1, index + 3, index + 1, index + 2, index + 3);\r\n            index += 4;\r\n            a = 0;\r\n            b = 0;\r\n            c = offsetX / tileWidth;\r\n            d = offsetY / tileHeight;\r\n            uvPart = [a, b, c, b, c, d, a, d];\r\n            if ((flipTile === Mesh.ROTATE_ROW && tilesY % 2 === 1) || (flipTile === Mesh.ROTATE_TILE && (tilesY + tilesX) % 2 === 1)) {\r\n                uvPart = [1 - a, 1 - b, 1 - c, 1 - b, 1 - c, 1 - d, 1 - a, 1 - d];\r\n            }\r\n            if ((flipTile === Mesh.FLIP_ROW && tilesY % 2 === 1) || (flipTile === Mesh.FLIP_TILE && (tilesY + tilesX) % 2 === 1)) {\r\n                uvPart = [1 - a, b, 1 - c, b, 1 - c, d, 1 - a, d];\r\n            }\r\n            if ((flipTile === Mesh.FLIP_N_ROTATE_ROW && tilesY % 2 === 1) || (flipTile === Mesh.FLIP_N_ROTATE_TILE && (tilesY + tilesX) % 2 === 1)) {\r\n                uvPart = [a, 1 - b, c, 1 - b, c, 1 - d, a, 1 - d];\r\n            }\r\n            uvs = uvs.concat(uvPart);\r\n            colors.push(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1);\r\n            normals.push(0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1);\r\n        }\r\n\r\n        //part rows\r\n        if (partialBottomRow) {\r\n            const uvBaseBR = [];\r\n            a = 0;\r\n            b = 1 - offsetY / tileHeight;\r\n            c = 1;\r\n            d = 1;\r\n            uvBaseBR[0] = [a, b, c, b, c, d, a, d];\r\n            uvBaseBR[1] = [a, b, c, b, c, d, a, d];\r\n            if (flipTile === Mesh.ROTATE_TILE || flipTile === Mesh.ROTATE_ROW) {\r\n                uvBaseBR[1] = [1 - a, 1 - b, 1 - c, 1 - b, 1 - c, 1 - d, 1 - a, 1 - d];\r\n            }\r\n            if (flipTile === Mesh.FLIP_TILE || flipTile === Mesh.FLIP_ROW) {\r\n                uvBaseBR[1] = [1 - a, b, 1 - c, b, 1 - c, d, 1 - a, d];\r\n            }\r\n            if (flipTile === Mesh.FLIP_N_ROTATE_TILE || flipTile === Mesh.FLIP_N_ROTATE_ROW) {\r\n                uvBaseBR[1] = [a, 1 - b, c, 1 - b, c, 1 - d, a, 1 - d];\r\n            }\r\n            for (let x = 0; x < tilesX; x++) {\r\n                positions.push(-halfWidth + x * tileWidth + adjustX, startY + adjustY, 0);\r\n                positions.push(-halfWidth + (x + 1) * tileWidth + adjustX, startY + adjustY, 0);\r\n                positions.push(-halfWidth + (x + 1) * tileWidth + adjustX, startY + offsetY + adjustY, 0);\r\n                positions.push(-halfWidth + x * tileWidth + adjustX, startY + offsetY + adjustY, 0);\r\n                indices.push(index, index + 1, index + 3, index + 1, index + 2, index + 3);\r\n                index += 4;\r\n                if (flipTile === Mesh.FLIP_TILE || flipTile === Mesh.ROTATE_TILE || flipTile === Mesh.FLIP_N_ROTATE_TILE) {\r\n                    uvs = uvs.concat(uvBaseBR[(x + 1) % 2]);\r\n                } else if (flipTile === Mesh.FLIP_ROW || flipTile === Mesh.ROTATE_ROW || flipTile === Mesh.FLIP_N_ROTATE_ROW) {\r\n                    uvs = uvs.concat(uvBaseBR[1]);\r\n                } else {\r\n                    uvs = uvs.concat(uvBaseBR[0]);\r\n                }\r\n                colors.push(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1);\r\n                normals.push(0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1);\r\n            }\r\n        }\r\n\r\n        if (partialTopRow) {\r\n            const uvBaseTR = [];\r\n            a = 0;\r\n            b = 0;\r\n            c = 1;\r\n            d = offsetY / tileHeight;\r\n            uvBaseTR[0] = [a, b, c, b, c, d, a, d];\r\n            uvBaseTR[1] = [a, b, c, b, c, d, a, d];\r\n            if (flipTile === Mesh.ROTATE_TILE || flipTile === Mesh.ROTATE_ROW) {\r\n                uvBaseTR[1] = [1 - a, 1 - b, 1 - c, 1 - b, 1 - c, 1 - d, 1 - a, 1 - d];\r\n            }\r\n            if (flipTile === Mesh.FLIP_TILE || flipTile === Mesh.FLIP_ROW) {\r\n                uvBaseTR[1] = [1 - a, b, 1 - c, b, 1 - c, d, 1 - a, d];\r\n            }\r\n            if (flipTile === Mesh.FLIP_N_ROTATE_TILE || flipTile === Mesh.FLIP_N_ROTATE_ROW) {\r\n                uvBaseTR[1] = [a, 1 - b, c, 1 - b, c, 1 - d, a, 1 - d];\r\n            }\r\n            for (let x = 0; x < tilesX; x++) {\r\n                positions.push(-halfWidth + x * tileWidth + adjustX, endY - offsetY + adjustY, 0);\r\n                positions.push(-halfWidth + (x + 1) * tileWidth + adjustX, endY - offsetY + adjustY, 0);\r\n                positions.push(-halfWidth + (x + 1) * tileWidth + adjustX, endY + adjustY, 0);\r\n                positions.push(-halfWidth + x * tileWidth + adjustX, endY + adjustY, 0);\r\n                indices.push(index, index + 1, index + 3, index + 1, index + 2, index + 3);\r\n                index += 4;\r\n                if (flipTile === Mesh.FLIP_TILE || flipTile === Mesh.ROTATE_TILE || flipTile === Mesh.FLIP_N_ROTATE_TILE) {\r\n                    uvs = uvs.concat(uvBaseTR[(x + tilesY) % 2]);\r\n                } else if (flipTile === Mesh.FLIP_ROW || flipTile === Mesh.ROTATE_ROW || flipTile === Mesh.FLIP_N_ROTATE_ROW) {\r\n                    uvs = uvs.concat(uvBaseTR[tilesY % 2]);\r\n                } else {\r\n                    uvs = uvs.concat(uvBaseTR[0]);\r\n                }\r\n                colors.push(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1);\r\n                normals.push(0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1);\r\n            }\r\n        }\r\n\r\n        if (partialLeftCol) {\r\n            const uvBaseLC = [];\r\n            a = 1 - offsetX / tileWidth;\r\n            b = 0;\r\n            c = 1;\r\n            d = 1;\r\n            uvBaseLC[0] = [a, b, c, b, c, d, a, d];\r\n            uvBaseLC[1] = [a, b, c, b, c, d, a, d];\r\n            if (flipTile === Mesh.ROTATE_TILE || flipTile === Mesh.ROTATE_ROW) {\r\n                uvBaseLC[1] = [1 - a, 1 - b, 1 - c, 1 - b, 1 - c, 1 - d, 1 - a, 1 - d];\r\n            }\r\n            if (flipTile === Mesh.FLIP_TILE || flipTile === Mesh.FLIP_ROW) {\r\n                uvBaseLC[1] = [1 - a, b, 1 - c, b, 1 - c, d, 1 - a, d];\r\n            }\r\n            if (flipTile === Mesh.FLIP_N_ROTATE_TILE || flipTile === Mesh.FLIP_N_ROTATE_ROW) {\r\n                uvBaseLC[1] = [a, 1 - b, c, 1 - b, c, 1 - d, a, 1 - d];\r\n            }\r\n            for (let y = 0; y < tilesY; y++) {\r\n                positions.push(startX + adjustX, -halfHeight + y * tileHeight + adjustY, 0);\r\n                positions.push(startX + offsetX + adjustX, -halfHeight + y * tileHeight + adjustY, 0);\r\n                positions.push(startX + offsetX + adjustX, -halfHeight + (y + 1) * tileHeight + adjustY, 0);\r\n                positions.push(startX + adjustX, -halfHeight + (y + 1) * tileHeight + adjustY, 0);\r\n                indices.push(index, index + 1, index + 3, index + 1, index + 2, index + 3);\r\n                index += 4;\r\n                if (flipTile === Mesh.FLIP_TILE || flipTile === Mesh.ROTATE_TILE || flipTile === Mesh.FLIP_N_ROTATE_TILE) {\r\n                    uvs = uvs.concat(uvBaseLC[(y + 1) % 2]);\r\n                } else if (flipTile === Mesh.FLIP_ROW || flipTile === Mesh.ROTATE_ROW || flipTile === Mesh.FLIP_N_ROTATE_ROW) {\r\n                    uvs = uvs.concat(uvBaseLC[y % 2]);\r\n                } else {\r\n                    uvs = uvs.concat(uvBaseLC[0]);\r\n                }\r\n                colors.push(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1);\r\n                normals.push(0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1);\r\n            }\r\n        }\r\n\r\n        if (partialRightCol) {\r\n            const uvBaseRC = [];\r\n            a = 0;\r\n            b = 0;\r\n            c = offsetX / tileHeight;\r\n            d = 1;\r\n            uvBaseRC[0] = [a, b, c, b, c, d, a, d];\r\n            uvBaseRC[1] = [a, b, c, b, c, d, a, d];\r\n            if (flipTile === Mesh.ROTATE_TILE || flipTile === Mesh.ROTATE_ROW) {\r\n                uvBaseRC[1] = [1 - a, 1 - b, 1 - c, 1 - b, 1 - c, 1 - d, 1 - a, 1 - d];\r\n            }\r\n            if (flipTile === Mesh.FLIP_TILE || flipTile === Mesh.FLIP_ROW) {\r\n                uvBaseRC[1] = [1 - a, b, 1 - c, b, 1 - c, d, 1 - a, d];\r\n            }\r\n            if (flipTile === Mesh.FLIP_N_ROTATE_TILE || flipTile === Mesh.FLIP_N_ROTATE_ROW) {\r\n                uvBaseRC[1] = [a, 1 - b, c, 1 - b, c, 1 - d, a, 1 - d];\r\n            }\r\n            for (let y = 0; y < tilesY; y++) {\r\n                positions.push(endX - offsetX + adjustX, -halfHeight + y * tileHeight + adjustY, 0);\r\n                positions.push(endX + adjustX, -halfHeight + y * tileHeight + adjustY, 0);\r\n                positions.push(endX + adjustX, -halfHeight + (y + 1) * tileHeight + adjustY, 0);\r\n                positions.push(endX - offsetX + adjustX, -halfHeight + (y + 1) * tileHeight + adjustY, 0);\r\n                indices.push(index, index + 1, index + 3, index + 1, index + 2, index + 3);\r\n                index += 4;\r\n                if (flipTile === Mesh.FLIP_TILE || flipTile === Mesh.ROTATE_TILE || flipTile === Mesh.FLIP_N_ROTATE_TILE) {\r\n                    uvs = uvs.concat(uvBaseRC[(y + tilesX) % 2]);\r\n                } else if (flipTile === Mesh.FLIP_ROW || flipTile === Mesh.ROTATE_ROW || flipTile === Mesh.FLIP_N_ROTATE_ROW) {\r\n                    uvs = uvs.concat(uvBaseRC[y % 2]);\r\n                } else {\r\n                    uvs = uvs.concat(uvBaseRC[0]);\r\n                }\r\n                colors.push(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1);\r\n                normals.push(0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1);\r\n            }\r\n        }\r\n    }\r\n\r\n    const sideOrientation = options.sideOrientation === 0 ? 0 : options.sideOrientation || VertexData.DEFAULTSIDE;\r\n\r\n    // sides\r\n    VertexData._ComputeSides(sideOrientation, positions, indices, normals, uvs, options.frontUVs, options.backUVs);\r\n\r\n    // Result\r\n    const vertexData = new VertexData();\r\n\r\n    vertexData.indices = indices;\r\n    vertexData.positions = positions;\r\n    vertexData.normals = normals;\r\n    vertexData.uvs = uvs;\r\n\r\n    const totalColors = sideOrientation === VertexData.DOUBLESIDE ? colors.concat(colors) : colors;\r\n    vertexData.colors = totalColors;\r\n\r\n    return vertexData;\r\n}\r\n\r\n/**\r\n * Creates a tiled plane mesh\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set/tiled_plane\r\n * @param name defines the name of the mesh\r\n * @param options an object used to set the following optional parameters for the tiled plane, required but can be empty\r\n * * pattern a limited pattern arrangement depending on the number\r\n * * size of the box\r\n * * width of the box, overwrites size\r\n * * height of the box, overwrites size\r\n * * tileSize sets the width, height and depth of the tile to the value of size, optional default 1\r\n * * tileWidth sets the width (x direction) of the tile, overwrites the width set by size, optional, default size\r\n * * tileHeight sets the height (y direction) of the tile, overwrites the height set by size, optional, default size\r\n * * sideOrientation optional and takes the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n * * alignHorizontal places whole tiles aligned to the center, left or right of a row\r\n * * alignVertical places whole tiles aligned to the center, left or right of a column\r\n * * frontUvs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the front side, optional, default vector4 (0, 0, 1, 1)\r\n * * backUVs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the back side, optional, default vector4 (0, 0, 1, 1)\r\n * @param options.pattern\r\n * @param options.tileSize\r\n * @param options.tileWidth\r\n * @param options.tileHeight\r\n * @param options.size\r\n * @param options.width\r\n * @param options.height\r\n * @param options.alignHorizontal\r\n * @param options.alignVertical\r\n * @param options.sideOrientation\r\n * @param options.frontUVs\r\n * @param options.backUVs\r\n * @param options.updatable\r\n * @param scene defines the hosting scene\r\n * @returns the box mesh\r\n */\r\nexport function CreateTiledPlane(\r\n    name: string,\r\n    options: {\r\n        pattern?: number;\r\n        tileSize?: number;\r\n        tileWidth?: number;\r\n        tileHeight?: number;\r\n        size?: number;\r\n        width?: number;\r\n        height?: number;\r\n        alignHorizontal?: number;\r\n        alignVertical?: number;\r\n        sideOrientation?: number;\r\n        frontUVs?: Vector4;\r\n        backUVs?: Vector4;\r\n        updatable?: boolean;\r\n    },\r\n    scene: Nullable<Scene> = null\r\n): Mesh {\r\n    const plane = new Mesh(name, scene);\r\n\r\n    options.sideOrientation = Mesh._GetDefaultSideOrientation(options.sideOrientation);\r\n    plane._originalBuilderSideOrientation = options.sideOrientation;\r\n\r\n    const vertexData = CreateTiledPlaneVertexData(options);\r\n\r\n    vertexData.applyToMesh(plane, options.updatable);\r\n\r\n    return plane;\r\n}\r\n\r\n/**\r\n * Class containing static functions to help procedurally build meshes\r\n * @deprecated use CreateTiledPlane instead\r\n */\r\nexport const TiledPlaneBuilder = {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    CreateTiledPlane,\r\n};\r\n\r\nVertexData.CreateTiledPlane = CreateTiledPlaneVertexData;\r\n"]}