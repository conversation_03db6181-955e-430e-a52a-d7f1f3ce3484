{"version": 3, "file": "sprites.fragment.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Shaders/sprites.fragment.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,yCAAyC,CAAC;AACjD,OAAO,8BAA8B,CAAC;AACtC,OAAO,+CAA+C,CAAC;AAEvD,MAAM,IAAI,GAAG,oBAAoB,CAAC;AAClC,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;EAqBb,CAAC;AACH,aAAa;AACb,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACxC,gBAAgB;AAChB,MAAM,CAAC,MAAM,kBAAkB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore\";\nimport \"./ShadersInclude/fogFragmentDeclaration\";\nimport \"./ShadersInclude/fogFragment\";\nimport \"./ShadersInclude/imageProcessingCompatibility\";\n\nconst name = \"spritesPixelShader\";\nconst shader = `uniform bool alphaTest;varying vec4 vColor;varying vec2 vUV;uniform sampler2D diffuseSampler;\n#include<fogFragmentDeclaration>\n#define CUSTOM_FRAGMENT_DEFINITIONS\n#ifdef PIXEL_PERFECT\nvec2 uvPixelPerfect(vec2 uv) {vec2 res=vec2(textureSize(diffuseSampler,0));uv=uv*res;vec2 seam=floor(uv+0.5);uv=seam+clamp((uv-seam)/fwidth(uv),-0.5,0.5);return uv/res;}\n#endif\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#ifdef PIXEL_PERFECT\nvec2 uv=uvPixelPerfect(vUV);\n#else\nvec2 uv=vUV;\n#endif\nvec4 color=texture2D(diffuseSampler,uv);float fAlphaTest=float(alphaTest);if (fAlphaTest != 0.)\n{if (color.a<0.95)\ndiscard;}\ncolor*=vColor;\n#include<fogFragment>\ngl_FragColor=color;\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}`;\n// Sideeffect\nShaderStore.ShadersStore[name] = shader;\n/** @internal */\nexport const spritesPixelShader = { name, shader };\n"]}