{"version": 3, "file": "voronoiNoiseBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/voronoiNoiseBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAGvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAExD;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,iBAAiB;IACpD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC1E,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAC1E,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAE3E,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAC3E,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;IAC9E,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACxB,OAAO;SACV;QAED,IAAI,cAAc,GAAG;;;;;SAKpB,CAAC;QAEF,KAAK,CAAC,aAAa,CAAC,eAAe,EAAE,cAAc,EAAE,6BAA6B,CAAC,CAAC;QAEpF,cAAc,GAAG;;;;;;;;;;;;;;;;;;;;;;SAsBhB,CAAC;QAEF,KAAK,CAAC,aAAa,CAAC,SAAS,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;QAE7D,MAAM,UAAU,GAAG,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAC5D,MAAM,SAAS,GAAG,KAAK,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAE1D,KAAK,CAAC,iBAAiB,IAAI,SAAS,UAAU,WAAW,CAAC;QAC1D,KAAK,CAAC,iBAAiB,IAAI,SAAS,SAAS,WAAW,CAAC;QACzD,KAAK,CAAC,iBAAiB,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,MAAM,CAAC,sBAAsB,KAAK,IAAI,CAAC,OAAO,CAAC,sBAAsB,KAAK,UAAU,KAAK,SAAS,MAAM,CAAC;QAEzL,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC1B,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,UAAU,KAAK,CAAC;SAC9F;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;YACzB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,SAAS,KAAK,CAAC;SAC5F;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,2BAA2B,EAAE,iBAAiB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\n\r\n/**\r\n * block used to Generate a Voronoi Noise Pattern\r\n */\r\nexport class VoronoiNoiseBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new VoronoiNoiseBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n        this.registerInput(\"seed\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerInput(\"offset\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerInput(\"density\", NodeMaterialBlockConnectionPointTypes.Float);\r\n\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"cells\", NodeMaterialBlockConnectionPointTypes.Float);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"VoronoiNoiseBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the seed input component\r\n     */\r\n    public get seed(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the offset input component\r\n     */\r\n    public get offset(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the density input component\r\n     */\r\n    public get density(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get cells(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (!this.seed.isConnected) {\r\n            return;\r\n        }\r\n\r\n        let functionString = `vec2 voronoiRandom(vec2 seed, float offset){\r\n            mat2 m = mat2(15.27, 47.63, 99.41, 89.98);\r\n            vec2 uv = fract(sin(m * seed) * 46839.32);\r\n            return vec2(sin(uv.y * offset) * 0.5 + 0.5, cos(uv.x * offset) * 0.5 + 0.5);\r\n        }\r\n        `;\r\n\r\n        state._emitFunction(\"voronoiRandom\", functionString, \"// Voronoi random generator\");\r\n\r\n        functionString = `void voronoi(vec2 seed, float offset, float density, out float outValue, out float cells){\r\n            vec2 g = floor(seed * density);\r\n            vec2 f = fract(seed * density);\r\n            float t = 8.0;\r\n            vec3 res = vec3(8.0, 0.0, 0.0);\r\n\r\n            for(int y=-1; y<=1; y++)\r\n            {\r\n                for(int x=-1; x<=1; x++)\r\n                {\r\n                    vec2 lattice = vec2(x,y);\r\n                    vec2 randomOffset = voronoiRandom(lattice + g, offset);\r\n                    float d = distance(lattice + randomOffset, f);\r\n                    if(d < res.x)\r\n                    {\r\n                        res = vec3(d, randomOffset.x, randomOffset.y);\r\n                        outValue = res.x;\r\n                        cells = res.y;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        `;\r\n\r\n        state._emitFunction(\"voronoi\", functionString, \"// Voronoi\");\r\n\r\n        const tempOutput = state._getFreeVariableName(\"tempOutput\");\r\n        const tempCells = state._getFreeVariableName(\"tempCells\");\r\n\r\n        state.compilationString += `float ${tempOutput} = 0.0;\\n`;\r\n        state.compilationString += `float ${tempCells} = 0.0;\\n`;\r\n        state.compilationString += `voronoi(${this.seed.associatedVariableName}, ${this.offset.associatedVariableName}, ${this.density.associatedVariableName}, ${tempOutput}, ${tempCells});\\n`;\r\n\r\n        if (this.output.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(this.output, state) + ` = ${tempOutput};\\n`;\r\n        }\r\n\r\n        if (this.cells.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(this.cells, state) + ` = ${tempCells};\\n`;\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.VoronoiNoiseBlock\", VoronoiNoiseBlock);\r\n"]}