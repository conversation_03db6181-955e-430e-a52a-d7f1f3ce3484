{"version": 3, "file": "particleHelper.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Particles/particleHelper.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD;;GAEG;AACH,MAAM,OAAO,cAAc;IASvB;;;;;;;OAOG;IACI,MAAM,CAAC,aAAa,CAAC,OAAyC,EAAE,QAAQ,GAAG,GAAG,EAAE,KAAa,EAAE,MAAM,GAAG,KAAK;QAChH,IAAI,MAAuB,CAAC;QAE5B,IAAI,MAAM,EAAE;YACR,MAAM,GAAG,IAAI,iBAAiB,CAAC,gBAAgB,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,KAAM,CAAC,CAAC;SACpF;aAAM;YACH,MAAM,GAAG,IAAI,cAAc,CAAC,gBAAgB,EAAE,QAAQ,EAAE,KAAM,CAAC,CAAC;SACnE;QAED,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;QACzB,MAAM,CAAC,eAAe,GAAG,IAAI,OAAO,CAAC,iDAAiD,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3G,MAAM,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAE3C,iBAAiB;QACjB,MAAM,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC/C,MAAM,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC/C,MAAM,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAElD,gBAAgB;QAChB,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;QACrB,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;QAErB,iBAAiB;QACjB,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;QACxB,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;QAExB,eAAe;QACf,MAAM,CAAC,WAAW,GAAG,CAAC,GAAG,EAAE,CAAC;QAE5B,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;QAErB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,WAAW,CAAC,IAAY,EAAE,KAAsB,EAAE,MAAe,KAAK,EAAE,QAAiB;QACnG,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,WAAW,CAAC,gBAAgB,CAAC;SACxC;QAED,MAAM,KAAK,GAAG,EAAE,CAAC;QAEjB,KAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAE7B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;gBACvC,KAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAChC,OAAO,MAAM,CAAC,4CAA4C,CAAC,CAAC;aAC/D;YAED,KAAK,CAAC,QAAQ,CACV,GAAG,cAAc,CAAC,aAAa,YAAY,IAAI,OAAO,EACtD,CAAC,IAAI,EAAE,EAAE;gBACL,KAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAChC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC5C,OAAO,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,OAAO,EAAE,KAAM,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC5E,CAAC,EACD,SAAS,EACT,SAAS,EACT,SAAS,EACT,GAAG,EAAE;gBACD,KAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAChC,OAAO,MAAM,CAAC,oFAAoF,IAAI,WAAW,CAAC,CAAC;YACvH,CAAC,CACJ,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,SAAS,CAAC,OAA0B;QAC9C,MAAM,GAAG,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAEpC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC1B,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC5B;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAC,kBAAkB,CAAC,IAAsB,EAAE,GAAW,EAAE,KAAY,EAAE,MAAe,KAAK,EAAE,UAAkB,EAAE,EAAE,QAAiB;QAC7I,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE;oBACzB,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE;wBACvB,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBAC7D,IAAI,MAAuB,CAAC;wBAE5B,IAAI,GAAG,EAAE;4BACL,MAAM,GAAG,iBAAiB,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;yBAC1F;6BAAM;4BACH,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;yBACvF;wBAED,IAAI,IAAI,EAAE;4BACN,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;yBACtB;wBAED,OAAO,CAAC,MAAM,CAAC,CAAC;qBACnB;yBAAM;wBACH,MAAM,CAAC,oCAAoC,CAAC,CAAC;qBAChD;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YACzB,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,qBAAqB,CAAC,SAAiB,EAAE,KAAY,EAAE,MAAe,KAAK,EAAE,UAAkB,EAAE,EAAE,QAAiB;QAC9H,IAAI,SAAS,KAAK,QAAQ,EAAE;YACxB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,EAAE,CAAC;YACf,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAClC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE;oBACzB,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE;wBACvB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;wBACzE,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;wBAC/D,IAAI,MAAuB,CAAC;wBAE5B,IAAI,GAAG,EAAE;4BACL,MAAM,GAAG,iBAAiB,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;yBAC1F;6BAAM;4BACH,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;yBACvF;wBACD,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;wBAE7B,OAAO,CAAC,MAAM,CAAC,CAAC;qBACnB;yBAAM;wBACH,MAAM,CAAC,6BAA6B,GAAG,SAAS,CAAC,CAAC;qBACrD;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;YAC1E,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;;AA9LD;;GAEG;AACW,4BAAa,GAAG,iBAAiB,CAAC,aAAa,CAAC;AAE9D,sCAAsC;AACxB,yBAAU,GAAG,SAAS,CAAC,UAAU,CAAC;AA0LhD;;;;;;;;;GASG;AACW,qCAAsB,GAAG,cAAc,CAAC,qBAAqB,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport type { Vector3 } from \"../Maths/math.vector\";\r\nimport { Color4 } from \"../Maths/math.color\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { IParticleSystem } from \"./IParticleSystem\";\r\nimport { GPUParticleSystem } from \"./gpuParticleSystem\";\r\nimport { ParticleSystemSet } from \"./particleSystemSet\";\r\nimport { ParticleSystem } from \"./particleSystem\";\r\nimport { WebRequest } from \"../Misc/webRequest\";\r\nimport { Constants } from \"../Engines/constants\";\r\n/**\r\n * This class is made for on one-liner static method to help creating particle system set.\r\n */\r\nexport class ParticleHelper {\r\n    /**\r\n     * Gets or sets base Assets URL\r\n     */\r\n    public static BaseAssetsUrl = ParticleSystemSet.BaseAssetsUrl;\r\n\r\n    /** Define the Url to load snippets */\r\n    public static SnippetUrl = Constants.SnippetUrl;\r\n\r\n    /**\r\n     * Create a default particle system that you can tweak\r\n     * @param emitter defines the emitter to use\r\n     * @param capacity defines the system capacity (default is 500 particles)\r\n     * @param scene defines the hosting scene\r\n     * @param useGPU defines if a GPUParticleSystem must be created (default is false)\r\n     * @returns the new Particle system\r\n     */\r\n    public static CreateDefault(emitter: Nullable<AbstractMesh | Vector3>, capacity = 500, scene?: Scene, useGPU = false): IParticleSystem {\r\n        let system: IParticleSystem;\r\n\r\n        if (useGPU) {\r\n            system = new GPUParticleSystem(\"default system\", { capacity: capacity }, scene!);\r\n        } else {\r\n            system = new ParticleSystem(\"default system\", capacity, scene!);\r\n        }\r\n\r\n        system.emitter = emitter;\r\n        system.particleTexture = new Texture(\"https://assets.babylonjs.com/textures/flare.png\", system.getScene());\r\n        system.createConeEmitter(0.1, Math.PI / 4);\r\n\r\n        // Particle color\r\n        system.color1 = new Color4(1.0, 1.0, 1.0, 1.0);\r\n        system.color2 = new Color4(1.0, 1.0, 1.0, 1.0);\r\n        system.colorDead = new Color4(1.0, 1.0, 1.0, 0.0);\r\n\r\n        // Particle Size\r\n        system.minSize = 0.1;\r\n        system.maxSize = 0.1;\r\n\r\n        // Emission speed\r\n        system.minEmitPower = 2;\r\n        system.maxEmitPower = 2;\r\n\r\n        // Update speed\r\n        system.updateSpeed = 1 / 60;\r\n\r\n        system.emitRate = 30;\r\n\r\n        return system;\r\n    }\r\n\r\n    /**\r\n     * This is the main static method (one-liner) of this helper to create different particle systems\r\n     * @param type This string represents the type to the particle system to create\r\n     * @param scene The scene where the particle system should live\r\n     * @param gpu If the system will use gpu\r\n     * @param capacity defines the system capacity (if null or undefined the sotred capacity will be used)\r\n     * @returns the ParticleSystemSet created\r\n     */\r\n    public static CreateAsync(type: string, scene: Nullable<Scene>, gpu: boolean = false, capacity?: number): Promise<ParticleSystemSet> {\r\n        if (!scene) {\r\n            scene = EngineStore.LastCreatedScene;\r\n        }\r\n\r\n        const token = {};\r\n\r\n        scene!.addPendingData(token);\r\n\r\n        return new Promise((resolve, reject) => {\r\n            if (gpu && !GPUParticleSystem.IsSupported) {\r\n                scene!.removePendingData(token);\r\n                return reject(\"Particle system with GPU is not supported.\");\r\n            }\r\n\r\n            Tools.LoadFile(\r\n                `${ParticleHelper.BaseAssetsUrl}/systems/${type}.json`,\r\n                (data) => {\r\n                    scene!.removePendingData(token);\r\n                    const newData = JSON.parse(data.toString());\r\n                    return resolve(ParticleSystemSet.Parse(newData, scene!, gpu, capacity));\r\n                },\r\n                undefined,\r\n                undefined,\r\n                undefined,\r\n                () => {\r\n                    scene!.removePendingData(token);\r\n                    return reject(`An error occurred with the creation of your particle system. Check if your type '${type}' exists.`);\r\n                }\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Static function used to export a particle system to a ParticleSystemSet variable.\r\n     * Please note that the emitter shape is not exported\r\n     * @param systems defines the particle systems to export\r\n     * @returns the created particle system set\r\n     */\r\n    public static ExportSet(systems: IParticleSystem[]): ParticleSystemSet {\r\n        const set = new ParticleSystemSet();\r\n\r\n        for (const system of systems) {\r\n            set.systems.push(system);\r\n        }\r\n\r\n        return set;\r\n    }\r\n\r\n    /**\r\n     * Creates a particle system from a snippet saved in a remote file\r\n     * @param name defines the name of the particle system to create (can be null or empty to use the one from the json data)\r\n     * @param url defines the url to load from\r\n     * @param scene defines the hosting scene\r\n     * @param gpu If the system will use gpu\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @param capacity defines the system capacity (if null or undefined the sotred capacity will be used)\r\n     * @returns a promise that will resolve to the new particle system\r\n     */\r\n    public static ParseFromFileAsync(name: Nullable<string>, url: string, scene: Scene, gpu: boolean = false, rootUrl: string = \"\", capacity?: number): Promise<IParticleSystem> {\r\n        return new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        const serializationObject = JSON.parse(request.responseText);\r\n                        let output: IParticleSystem;\r\n\r\n                        if (gpu) {\r\n                            output = GPUParticleSystem.Parse(serializationObject, scene, rootUrl, false, capacity);\r\n                        } else {\r\n                            output = ParticleSystem.Parse(serializationObject, scene, rootUrl, false, capacity);\r\n                        }\r\n\r\n                        if (name) {\r\n                            output.name = name;\r\n                        }\r\n\r\n                        resolve(output);\r\n                    } else {\r\n                        reject(\"Unable to load the particle system\");\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", url);\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a particle system from a snippet saved by the particle system editor\r\n     * @param snippetId defines the snippet to load (can be set to _BLANK to create a default one)\r\n     * @param scene defines the hosting scene\r\n     * @param gpu If the system will use gpu\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @param capacity defines the system capacity (if null or undefined the sotred capacity will be used)\r\n     * @returns a promise that will resolve to the new particle system\r\n     */\r\n    public static ParseFromSnippetAsync(snippetId: string, scene: Scene, gpu: boolean = false, rootUrl: string = \"\", capacity?: number): Promise<IParticleSystem> {\r\n        if (snippetId === \"_BLANK\") {\r\n            const system = this.CreateDefault(null);\r\n            system.start();\r\n            return Promise.resolve(system);\r\n        }\r\n\r\n        return new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        const snippet = JSON.parse(JSON.parse(request.responseText).jsonPayload);\r\n                        const serializationObject = JSON.parse(snippet.particleSystem);\r\n                        let output: IParticleSystem;\r\n\r\n                        if (gpu) {\r\n                            output = GPUParticleSystem.Parse(serializationObject, scene, rootUrl, false, capacity);\r\n                        } else {\r\n                            output = ParticleSystem.Parse(serializationObject, scene, rootUrl, false, capacity);\r\n                        }\r\n                        output.snippetId = snippetId;\r\n\r\n                        resolve(output);\r\n                    } else {\r\n                        reject(\"Unable to load the snippet \" + snippetId);\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", this.SnippetUrl + \"/\" + snippetId.replace(/#/g, \"/\"));\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a particle system from a snippet saved by the particle system editor\r\n     * @deprecated Please use ParseFromSnippetAsync instead\r\n     * @param snippetId defines the snippet to load (can be set to _BLANK to create a default one)\r\n     * @param scene defines the hosting scene\r\n     * @param gpu If the system will use gpu\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @param capacity defines the system capacity (if null or undefined the sotred capacity will be used)\r\n     * @returns a promise that will resolve to the new particle system\r\n     */\r\n    public static CreateFromSnippetAsync = ParticleHelper.ParseFromSnippetAsync;\r\n}\r\n"]}