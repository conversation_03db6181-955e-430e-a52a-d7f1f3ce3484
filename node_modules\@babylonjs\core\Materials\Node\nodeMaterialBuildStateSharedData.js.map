{"version": 3, "file": "nodeMaterialBuildStateSharedData.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Node/nodeMaterialBuildStateSharedData.ts"], "names": [], "mappings": "AAOA;;GAEG;AACH,MAAM,OAAO,gCAAgC;IAwHzC,gCAAgC;IAChC;QAnHA;;WAEG;QACI,UAAK,GAAa,EAAE,CAAC;QAE5B;;WAEG;QACI,aAAQ,GAAa,EAAE,CAAC;QAE/B;;WAEG;QACI,uBAAkB,GAAG,EAAE,CAAC;QAO/B;;WAEG;QACI,gBAAW,GAAiB,EAAE,CAAC;QAEtC;;WAEG;QACI,kBAAa,GAAgC,EAAE,CAAC;QAEvD;;WAEG;QACI,mBAAc,GAAwB,EAAE,CAAC;QAEhD;;WAEG;QACI,yBAAoB,GAAwB,EAAE,CAAC;QAEtD;;WAEG;QACI,wBAAmB,GAAwB,EAAE,CAAC;QAErD;;WAEG;QACI,sBAAiB,GAAwB,EAAE,CAAC;QAEnD;;WAEG;QACI,4BAAuB,GAAwB,EAAE,CAAC;QAEzD;;WAEG;QACI,yBAAoB,GAAwB,EAAE,CAAC;QAEtD;;WAEG;QACI,mBAAc,GAAwB,EAAE,CAAC;QAEhD;;WAEG;QACI,mBAAc,GAAiB,EAAE,CAAC;QAOzC,gCAAgC;QACzB,kBAAa,GAA8B,EAAE,CAAC;QAErD,8BAA8B;QACvB,gBAAW,GAA8B,EAAE,CAAC;QAWnD;;WAEG;QACI,UAAK,GAAG;YACX,mBAAmB,EAAE,KAAK;YAC1B,6BAA6B,EAAE,KAAK;YACpC,iBAAiB,EAAE,KAAK;YACxB,gBAAgB,EAAE,KAAK;SAC1B,CAAC;QAEF;;WAEG;QACI,WAAM,GAAG;YACZ,UAAU,EAAE,KAAK;YACjB,YAAY,EAAE,KAAK;YACnB,6BAA6B,EAAE,IAAI,KAAK,EAA+B;SAC1E,CAAC;QAEF;;WAEG;QACI,4BAAuB,GAAY,KAAK,CAAC;QAI5C,oDAAoD;QACpD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAE/B,yBAAyB;QACzB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAE/B,kBAAkB;QAClB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,UAAU;QACb,IAAI,YAAY,GAAG,EAAE,CAAC;QAEtB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC1D,YAAY,IAAI,mHAAmH,CAAC;SACvI;QACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC3B,YAAY,IAAI,sHAAsH,CAAC;SAC1I;QACD,KAAK,MAAM,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE;YACvE,YAAY,IAAI,SAAS,iBAAiB,CAAC,IAAI,eAC3C,iBAAiB,CAAC,UAAU,CAAC,IACjC,IAAI,iBAAiB,CAAC,UAAU,CAAC,YAAY,EAAE,2CAA2C,CAAC;SAC9F;QAED,IAAI,YAAY,EAAE;YACd,4CAA4C;YAC5C,MAAM,iCAAiC,GAAG,YAAY,CAAC;SAC1D;IACL,CAAC;CACJ", "sourcesContent": ["import type { NodeMaterialConnectionPoint } from \"./nodeMaterialBlockConnectionPoint\";\r\nimport type { NodeMaterialBlock } from \"./nodeMaterialBlock\";\r\nimport type { InputBlock } from \"./Blocks/Input/inputBlock\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Immutable } from \"../../types\";\r\nimport type { NodeMaterial, NodeMaterialTextureBlocks } from \"./nodeMaterial\";\r\n\r\n/**\r\n * Class used to store shared data between 2 NodeMaterialBuildState\r\n */\r\nexport class NodeMaterialBuildStateSharedData {\r\n    /**\r\n     * The node material we are currently building\r\n     */\r\n    public nodeMaterial: NodeMaterial;\r\n\r\n    /**\r\n     * Gets the list of emitted varyings\r\n     */\r\n    public temps: string[] = [];\r\n\r\n    /**\r\n     * Gets the list of emitted varyings\r\n     */\r\n    public varyings: string[] = [];\r\n\r\n    /**\r\n     * Gets the varying declaration string\r\n     */\r\n    public varyingDeclaration = \"\";\r\n\r\n    /**\r\n     * List of the fragment output nodes\r\n     */\r\n    public fragmentOutputNodes: Immutable<Array<NodeMaterialBlock>>;\r\n\r\n    /**\r\n     * Input blocks\r\n     */\r\n    public inputBlocks: InputBlock[] = [];\r\n\r\n    /**\r\n     * Input blocks\r\n     */\r\n    public textureBlocks: NodeMaterialTextureBlocks[] = [];\r\n\r\n    /**\r\n     * Bindable blocks (Blocks that need to set data to the effect)\r\n     */\r\n    public bindableBlocks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * Bindable blocks (Blocks that need to set data to the effect) that will always be called (by bindForSubMesh), contrary to bindableBlocks that won't be called if _mustRebind() returns false\r\n     */\r\n    public forcedBindableBlocks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * List of blocks that can provide a compilation fallback\r\n     */\r\n    public blocksWithFallbacks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * List of blocks that can provide a define update\r\n     */\r\n    public blocksWithDefines: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * List of blocks that can provide a repeatable content\r\n     */\r\n    public repeatableContentBlocks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * List of blocks that can provide a dynamic list of uniforms\r\n     */\r\n    public dynamicUniformBlocks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * List of blocks that can block the isReady function for the material\r\n     */\r\n    public blockingBlocks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * Gets the list of animated inputs\r\n     */\r\n    public animatedInputs: InputBlock[] = [];\r\n\r\n    /**\r\n     * Build Id used to avoid multiple recompilations\r\n     */\r\n    public buildId: number;\r\n\r\n    /** List of emitted variables */\r\n    public variableNames: { [key: string]: number } = {};\r\n\r\n    /** List of emitted defines */\r\n    public defineNames: { [key: string]: number } = {};\r\n\r\n    /** Should emit comments? */\r\n    public emitComments: boolean;\r\n\r\n    /** Emit build activity */\r\n    public verbose: boolean;\r\n\r\n    /** Gets or sets the hosting scene */\r\n    public scene: Scene;\r\n\r\n    /**\r\n     * Gets the compilation hints emitted at compilation time\r\n     */\r\n    public hints = {\r\n        needWorldViewMatrix: false,\r\n        needWorldViewProjectionMatrix: false,\r\n        needAlphaBlending: false,\r\n        needAlphaTesting: false,\r\n    };\r\n\r\n    /**\r\n     * List of compilation checks\r\n     */\r\n    public checks = {\r\n        emitVertex: false,\r\n        emitFragment: false,\r\n        notConnectedNonOptionalInputs: new Array<NodeMaterialConnectionPoint>(),\r\n    };\r\n\r\n    /**\r\n     * Is vertex program allowed to be empty?\r\n     */\r\n    public allowEmptyVertexProgram: boolean = false;\r\n\r\n    /** Creates a new shared data */\r\n    public constructor() {\r\n        // Exclude usual attributes from free variable names\r\n        this.variableNames[\"position\"] = 0;\r\n        this.variableNames[\"normal\"] = 0;\r\n        this.variableNames[\"tangent\"] = 0;\r\n        this.variableNames[\"uv\"] = 0;\r\n        this.variableNames[\"uv2\"] = 0;\r\n        this.variableNames[\"uv3\"] = 0;\r\n        this.variableNames[\"uv4\"] = 0;\r\n        this.variableNames[\"uv5\"] = 0;\r\n        this.variableNames[\"uv6\"] = 0;\r\n        this.variableNames[\"color\"] = 0;\r\n        this.variableNames[\"matricesIndices\"] = 0;\r\n        this.variableNames[\"matricesWeights\"] = 0;\r\n        this.variableNames[\"matricesIndicesExtra\"] = 0;\r\n        this.variableNames[\"matricesWeightsExtra\"] = 0;\r\n        this.variableNames[\"diffuseBase\"] = 0;\r\n        this.variableNames[\"specularBase\"] = 0;\r\n        this.variableNames[\"worldPos\"] = 0;\r\n        this.variableNames[\"shadow\"] = 0;\r\n        this.variableNames[\"view\"] = 0;\r\n\r\n        // Exclude known varyings\r\n        this.variableNames[\"vTBN\"] = 0;\r\n\r\n        // Exclude defines\r\n        this.defineNames[\"MAINUV0\"] = 0;\r\n        this.defineNames[\"MAINUV1\"] = 0;\r\n        this.defineNames[\"MAINUV2\"] = 0;\r\n        this.defineNames[\"MAINUV3\"] = 0;\r\n        this.defineNames[\"MAINUV4\"] = 0;\r\n        this.defineNames[\"MAINUV5\"] = 0;\r\n        this.defineNames[\"MAINUV6\"] = 0;\r\n        this.defineNames[\"MAINUV7\"] = 0;\r\n    }\r\n\r\n    /**\r\n     * Emits console errors and exceptions if there is a failing check\r\n     */\r\n    public emitErrors() {\r\n        let errorMessage = \"\";\r\n\r\n        if (!this.checks.emitVertex && !this.allowEmptyVertexProgram) {\r\n            errorMessage += \"NodeMaterial does not have a vertex output. You need to at least add a block that generates a glPosition value.\\n\";\r\n        }\r\n        if (!this.checks.emitFragment) {\r\n            errorMessage += \"NodeMaterial does not have a fragment output. You need to at least add a block that generates a glFragColor value.\\n\";\r\n        }\r\n        for (const notConnectedInput of this.checks.notConnectedNonOptionalInputs) {\r\n            errorMessage += `input ${notConnectedInput.name} from block ${\r\n                notConnectedInput.ownerBlock.name\r\n            }[${notConnectedInput.ownerBlock.getClassName()}] is not connected and is not optional.\\n`;\r\n        }\r\n\r\n        if (errorMessage) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"Build of NodeMaterial failed:\\n\" + errorMessage;\r\n        }\r\n    }\r\n}\r\n"]}