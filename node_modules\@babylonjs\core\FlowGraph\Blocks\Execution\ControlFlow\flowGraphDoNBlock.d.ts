import type { FlowGraphContext } from "../../../flowGraphContext";
import type { FlowGraphDataConnection } from "../../../flowGraphDataConnection";
import type { FlowGraphSignalConnection } from "../../../flowGraphSignalConnection";
import { FlowGraphExecutionBlockWithOutSignal } from "../../../flowGraphExecutionBlockWithOutSignal";
import type { IFlowGraphBlockConfiguration } from "../../../flowGraphBlock";
import { FlowGraphInteger } from "../../../flowGraphInteger";
/**
 * @experimental
 */
export interface IFlowGraphDoNBlockConfiguration extends IFlowGraphBlockConfiguration {
    /**
     * The start index for the counter.
     */
    startIndex: FlowGraphInteger;
}
/**
 * A block that executes a branch a set number of times.
 * @experimental
 */
export declare class FlowGraphDoNBlock extends FlowGraphExecutionBlockWithOutSignal {
    /**
     * the configuration of the block
     */
    config: IFlowGraphDoNBlockConfiguration;
    /**
     * Input connection: Resets the counter
     */
    readonly reset: FlowGraphSignalConnection;
    /**
     * Input connection: The maximum number of times the block can be executed.
     */
    readonly n: FlowGraphDataConnection<FlowGraphInteger>;
    /**
     * Output connection: The number of times the block has been executed.
     */
    readonly value: FlowGraphDataConnection<FlowGraphInteger>;
    constructor(
    /**
     * the configuration of the block
     */
    config?: IFlowGraphDoNBlockConfiguration);
    _execute(context: FlowGraphContext, callingSignal: FlowGraphSignalConnection): void;
    /**
     * @returns class name of the block.
     */
    getClassName(): string;
    /**
     * the class name of the block.
     */
    static ClassName: string;
}
