{"version": 3, "file": "stringTools.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/stringTools.ts"], "names": [], "mappings": "AAAA,yDAAyD;AAEzD;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAE,MAAc,EAAW,EAAE;IAC7D,OAAO,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,GAAW,EAAE,MAAc,EAAW,EAAE;IAC/D,IAAI,CAAC,GAAG,EAAE;QACN,OAAO,KAAK,CAAC;KAChB;IACD,OAAO,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,MAAgC,EAAU,EAAE;IAC/D,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;QACpC,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KAC3C;IAED,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;QACxC,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5C;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,CAAC,MAAqC,EAAU,EAAE;IACvF,MAAM,MAAM,GAAG,mEAAmE,CAAC;IACnF,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7C,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IAExI,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE;QACrB,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAClB,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;QAClD,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC;QAElD,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;QACjB,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;QACvC,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;QACxC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QAEjB,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;YACb,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;SACpB;aAAM,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;YACpB,IAAI,GAAG,EAAE,CAAC;SACb;QACD,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KACnG;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,UAAkB,EAAU,EAAE;IAC/D,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5B,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,UAAkB,EAAe,EAAE;IACpE,MAAM,aAAa,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAC;IACvD,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC;IAC1C,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;IAEjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;QACnC,UAAU,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;KAC/C;IAED,OAAO,UAAU,CAAC,MAAM,CAAC;AAC7B,CAAC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,GAAW,EAAE,MAAc,EAAU,EAAE;IAC7D,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;IACtB,OAAO,GAAG,CAAC,MAAM,GAAG,MAAM,EAAE;QACxB,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;KACnB;IACD,OAAO,GAAG,CAAC;AACf,CAAC,CAAC;AACF;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG;IACvB,QAAQ;IACR,UAAU;IACV,MAAM;IACN,yBAAyB;IACzB,oBAAoB;IACpB,oBAAoB;IACpB,SAAS;CACZ,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\n\r\n/**\r\n * Checks for a matching suffix at the end of a string (for ES5 and lower)\r\n * @param str Source string\r\n * @param suffix Suffix to search for in the source string\r\n * @returns Boolean indicating whether the suffix was found (true) or not (false)\r\n * @deprecated Please use native string function instead\r\n */\r\nexport const EndsWith = (str: string, suffix: string): boolean => {\r\n    return str.endsWith(suffix);\r\n};\r\n\r\n/**\r\n * Checks for a matching suffix at the beginning of a string (for ES5 and lower)\r\n * @param str Source string\r\n * @param suffix Suffix to search for in the source string\r\n * @returns Boolean indicating whether the suffix was found (true) or not (false)\r\n * @deprecated Please use native string function instead\r\n */\r\nexport const StartsWith = (str: string, suffix: string): boolean => {\r\n    if (!str) {\r\n        return false;\r\n    }\r\n    return str.startsWith(suffix);\r\n};\r\n\r\n/**\r\n * Decodes a buffer into a string\r\n * @param buffer The buffer to decode\r\n * @returns The decoded string\r\n */\r\nexport const Decode = (buffer: Uint8Array | Uint16Array): string => {\r\n    if (typeof TextDecoder !== \"undefined\") {\r\n        return new TextDecoder().decode(buffer);\r\n    }\r\n\r\n    let result = \"\";\r\n    for (let i = 0; i < buffer.byteLength; i++) {\r\n        result += String.fromCharCode(buffer[i]);\r\n    }\r\n\r\n    return result;\r\n};\r\n\r\n/**\r\n * Encode a buffer to a base64 string\r\n * @param buffer defines the buffer to encode\r\n * @returns the encoded string\r\n */\r\nexport const EncodeArrayBufferToBase64 = (buffer: ArrayBuffer | ArrayBufferView): string => {\r\n    const keyStr = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\r\n    let output = \"\";\r\n    let chr1, chr2, chr3, enc1, enc2, enc3, enc4;\r\n    let i = 0;\r\n    const bytes = ArrayBuffer.isView(buffer) ? new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.byteLength) : new Uint8Array(buffer);\r\n\r\n    while (i < bytes.length) {\r\n        chr1 = bytes[i++];\r\n        chr2 = i < bytes.length ? bytes[i++] : Number.NaN;\r\n        chr3 = i < bytes.length ? bytes[i++] : Number.NaN;\r\n\r\n        enc1 = chr1 >> 2;\r\n        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\r\n        enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);\r\n        enc4 = chr3 & 63;\r\n\r\n        if (isNaN(chr2)) {\r\n            enc3 = enc4 = 64;\r\n        } else if (isNaN(chr3)) {\r\n            enc4 = 64;\r\n        }\r\n        output += keyStr.charAt(enc1) + keyStr.charAt(enc2) + keyStr.charAt(enc3) + keyStr.charAt(enc4);\r\n    }\r\n\r\n    return output;\r\n};\r\n\r\n/**\r\n * Converts a given base64 string as an ASCII encoded stream of data\r\n * @param base64Data The base64 encoded string to decode\r\n * @returns Decoded ASCII string\r\n */\r\nexport const DecodeBase64ToString = (base64Data: string): string => {\r\n    return atob(base64Data);\r\n};\r\n\r\n/**\r\n * Converts a given base64 string into an ArrayBuffer of raw byte data\r\n * @param base64Data The base64 encoded string to decode\r\n * @returns ArrayBuffer of byte data\r\n */\r\nexport const DecodeBase64ToBinary = (base64Data: string): ArrayBuffer => {\r\n    const decodedString = DecodeBase64ToString(base64Data);\r\n    const bufferLength = decodedString.length;\r\n    const bufferView = new Uint8Array(new ArrayBuffer(bufferLength));\r\n\r\n    for (let i = 0; i < bufferLength; i++) {\r\n        bufferView[i] = decodedString.charCodeAt(i);\r\n    }\r\n\r\n    return bufferView.buffer;\r\n};\r\n\r\n/**\r\n * Converts a number to string and pads with preceding zeroes until it is of specified length.\r\n * @param num the number to convert and pad\r\n * @param length the expected length of the string\r\n * @returns the padded string\r\n */\r\nexport const PadNumber = (num: number, length: number): string => {\r\n    let str = String(num);\r\n    while (str.length < length) {\r\n        str = \"0\" + str;\r\n    }\r\n    return str;\r\n};\r\n/**\r\n * Helper to manipulate strings\r\n */\r\nexport const StringTools = {\r\n    EndsWith,\r\n    StartsWith,\r\n    Decode,\r\n    EncodeArrayBufferToBase64,\r\n    DecodeBase64ToString,\r\n    DecodeBase64ToBinary,\r\n    PadNumber,\r\n};\r\n"]}