{"version": 3, "file": "vectorSplitterBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/vectorSplitterBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAEvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAE7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAExD;;GAEG;AACH,MAAM,OAAO,mBAAoB,SAAQ,iBAAiB;IACtD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAE9C,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAChF,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAChF,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAE/E,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC1E,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAEtE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,qBAAqB,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,EAAE;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,YAAY,CAAC,IAAY;QAC/B,QAAQ,IAAI,EAAE;YACV,KAAK,KAAK;gBACN,OAAO,MAAM,CAAC;YAClB,KAAK,MAAM;gBACP,OAAO,OAAO,CAAC;YACnB;gBACI,OAAO,IAAI,CAAC;SACnB;IACL,CAAC;IAES,aAAa,CAAC,IAAY;QAChC,QAAQ,IAAI,EAAE;YACV,KAAK,IAAI;gBACL,OAAO,OAAO,CAAC;YACnB,KAAK,KAAK;gBACN,OAAO,QAAQ,CAAC;YACpB;gBACI,OAAO,IAAI,CAAC;SACnB;IACL,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAElG,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEjC,IAAI,SAAS,CAAC,YAAY,EAAE;YACxB,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;gBACrB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,WAAW,KAAK,CAAC,sBAAsB,WAAW,CAAC;aACzH;iBAAM;gBACH,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,sBAAsB,SAAS,CAAC;aAClH;SACJ;QACD,IAAI,QAAQ,CAAC,YAAY,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YAChD,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,sBAAsB,QAAQ,CAAC;SACpH;QACD,IAAI,QAAQ,CAAC,YAAY,EAAE;YACvB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,sBAAsB,QAAQ,CAAC;SAChH;QACD,IAAI,OAAO,CAAC,YAAY,EAAE;YACtB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,sBAAsB,OAAO,CAAC;SAC9G;QACD,IAAI,OAAO,CAAC,YAAY,EAAE;YACtB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,sBAAsB,OAAO,CAAC;SAC9G;QACD,IAAI,OAAO,CAAC,YAAY,EAAE;YACtB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,sBAAsB,OAAO,CAAC;SAC9G;QACD,IAAI,OAAO,CAAC,YAAY,EAAE;YACtB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,sBAAsB,OAAO,CAAC;SAC9G;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\n\r\n/**\r\n * Block used to expand a Vector3/4 into 4 outputs (one for each component)\r\n */\r\nexport class VectorSplitterBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new VectorSplitterBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerInput(\"xyzw\", NodeMaterialBlockConnectionPointTypes.Vector4, true);\r\n        this.registerInput(\"xyz \", NodeMaterialBlockConnectionPointTypes.Vector3, true);\r\n        this.registerInput(\"xy \", NodeMaterialBlockConnectionPointTypes.Vector2, true);\r\n\r\n        this.registerOutput(\"xyz\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n        this.registerOutput(\"xy\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerOutput(\"zw\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerOutput(\"x\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"y\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"z\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"w\", NodeMaterialBlockConnectionPointTypes.Float);\r\n\r\n        this.inputsAreExclusive = true;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"VectorSplitterBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the xyzw component (input)\r\n     */\r\n    public get xyzw(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the xyz component (input)\r\n     */\r\n    public get xyzIn(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the xy component (input)\r\n     */\r\n    public get xyIn(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the xyz component (output)\r\n     */\r\n    public get xyzOut(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the xy component (output)\r\n     */\r\n    public get xyOut(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the zw component (output)\r\n     */\r\n    public get zw(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the x component (output)\r\n     */\r\n    public get x(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the y component (output)\r\n     */\r\n    public get y(): NodeMaterialConnectionPoint {\r\n        return this._outputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the z component (output)\r\n     */\r\n    public get z(): NodeMaterialConnectionPoint {\r\n        return this._outputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the w component (output)\r\n     */\r\n    public get w(): NodeMaterialConnectionPoint {\r\n        return this._outputs[6];\r\n    }\r\n\r\n    protected _inputRename(name: string) {\r\n        switch (name) {\r\n            case \"xy \":\r\n                return \"xyIn\";\r\n            case \"xyz \":\r\n                return \"xyzIn\";\r\n            default:\r\n                return name;\r\n        }\r\n    }\r\n\r\n    protected _outputRename(name: string) {\r\n        switch (name) {\r\n            case \"xy\":\r\n                return \"xyOut\";\r\n            case \"xyz\":\r\n                return \"xyzOut\";\r\n            default:\r\n                return name;\r\n        }\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const input = this.xyzw.isConnected ? this.xyzw : this.xyzIn.isConnected ? this.xyzIn : this.xyIn;\r\n\r\n        const xyzOutput = this._outputs[0];\r\n        const xyOutput = this._outputs[1];\r\n        const zwOutput = this._outputs[2];\r\n        const xOutput = this._outputs[3];\r\n        const yOutput = this._outputs[4];\r\n        const zOutput = this._outputs[5];\r\n        const wOutput = this._outputs[6];\r\n\r\n        if (xyzOutput.hasEndpoints) {\r\n            if (input === this.xyIn) {\r\n                state.compilationString += this._declareOutput(xyzOutput, state) + ` = vec3(${input.associatedVariableName}, 0.0);\\n`;\r\n            } else {\r\n                state.compilationString += this._declareOutput(xyzOutput, state) + ` = ${input.associatedVariableName}.xyz;\\n`;\r\n            }\r\n        }\r\n        if (zwOutput.hasEndpoints && this.xyzw.isConnected) {\r\n            state.compilationString += this._declareOutput(zwOutput, state) + ` = ${this.xyzw.associatedVariableName}.zw;\\n`;\r\n        }\r\n        if (xyOutput.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(xyOutput, state) + ` = ${input.associatedVariableName}.xy;\\n`;\r\n        }\r\n        if (xOutput.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(xOutput, state) + ` = ${input.associatedVariableName}.x;\\n`;\r\n        }\r\n        if (yOutput.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(yOutput, state) + ` = ${input.associatedVariableName}.y;\\n`;\r\n        }\r\n        if (zOutput.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(zOutput, state) + ` = ${input.associatedVariableName}.z;\\n`;\r\n        }\r\n        if (wOutput.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(wOutput, state) + ` = ${input.associatedVariableName}.w;\\n`;\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.VectorSplitterBlock\", VectorSplitterBlock);\r\n"]}