{"version": 3, "file": "shadowMap.fragment.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Shaders/shadowMap.fragment.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,oDAAoD,CAAC;AAC5D,OAAO,+CAA+C,CAAC;AACvD,OAAO,oCAAoC,CAAC;AAC5C,OAAO,oCAAoC,CAAC;AAE5C,MAAM,IAAI,GAAG,sBAAsB,CAAC;AACpC,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;EAwBb,CAAC;AACH,aAAa;AACb,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACxC,gBAAgB;AAChB,MAAM,CAAC,MAAM,oBAAoB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore\";\nimport \"./ShadersInclude/shadowMapFragmentExtraDeclaration\";\nimport \"./ShadersInclude/clipPlaneFragmentDeclaration\";\nimport \"./ShadersInclude/clipPlaneFragment\";\nimport \"./ShadersInclude/shadowMapFragment\";\n\nconst name = \"shadowMapPixelShader\";\nconst shader = `#include<shadowMapFragmentExtraDeclaration>\n#ifdef ALPHATEXTURE\nvarying vec2 vUV;uniform sampler2D diffuseSampler;\n#endif\n#include<clipPlaneFragmentDeclaration>\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void)\n{\n#include<clipPlaneFragment>\n#ifdef ALPHATEXTURE\nfloat alphaFromAlphaTexture=texture2D(diffuseSampler,vUV).a;\n#ifdef ALPHATESTVALUE\nif (alphaFromAlphaTexture<ALPHATESTVALUE)\ndiscard;\n#endif\n#endif\n#if SM_SOFTTRANSPARENTSHADOW==1\n#ifdef ALPHATEXTURE\nif ((bayerDither8(floor(mod(gl_FragCoord.xy,8.0))))/64.0>=softTransparentShadowSM*alphaFromAlphaTexture) discard;\n#else\nif ((bayerDither8(floor(mod(gl_FragCoord.xy,8.0))))/64.0>=softTransparentShadowSM) discard;\n#endif\n#endif\n#include<shadowMapFragment>\n}`;\n// Sideeffect\nShaderStore.ShadersStore[name] = shader;\n/** @internal */\nexport const shadowMapPixelShader = { name, shader };\n"]}