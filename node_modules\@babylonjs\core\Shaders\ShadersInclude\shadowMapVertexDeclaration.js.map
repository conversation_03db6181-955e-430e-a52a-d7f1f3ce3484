{"version": 3, "file": "shadowMapVertexDeclaration.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Shaders/ShadersInclude/shadowMapVertexDeclaration.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,0BAA0B,CAAC;AAClC,OAAO,yBAAyB,CAAC;AAEjC,MAAM,IAAI,GAAG,4BAA4B,CAAC;AAC1C,MAAM,MAAM,GAAG;;CAEd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAChD,gBAAgB;AAChB,MAAM,CAAC,MAAM,0BAA0B,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore\";\nimport \"./sceneVertexDeclaration\";\nimport \"./meshVertexDeclaration\";\n\nconst name = \"shadowMapVertexDeclaration\";\nconst shader = `#include<sceneVertexDeclaration>\n#include<meshVertexDeclaration>\n`;\n// Sideeffect\nShaderStore.IncludesShadersStore[name] = shader;\n/** @internal */\nexport const shadowMapVertexDeclaration = { name, shader };\n"]}