{"version": 3, "file": "refractionTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/refractionTexture.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,wBAAwB,CAAC;AAC/C,OAAO,EAAE,mBAAmB,EAAE,MAAM,8CAA8C,CAAC;AACnF;;;;GAIG;AACH,MAAM,OAAO,iBAAkB,SAAQ,mBAAmB;IAatD;;;;;;;;OAQG;IACH,YAAY,IAAY,EAAE,IAAY,EAAE,KAAa,EAAE,eAAyB;QAC5E,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;QAtBpD;;;;WAIG;QACI,oBAAe,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE/C;;WAEG;QACI,UAAK,GAAG,GAAG,CAAC;QAcf,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACnC,IAAI,CAAC,QAAQ,EAAG,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;YAClC,IAAI,CAAC,QAAQ,EAAG,CAAC,SAAS,GAAG,IAAI,CAAC;QACtC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC;SACf;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,UAAU,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAErG,eAAe;QACf,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAE9B,qBAAqB;QACrB,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC1D,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACpD;QACD,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAE9B,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QAED,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QACjE,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAEvC,OAAO,mBAAmB,CAAC;IAC/B,CAAC;CACJ", "sourcesContent": ["import type { Scene } from \"../../scene\";\r\nimport { Plane } from \"../../Maths/math.plane\";\r\nimport { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\n/**\r\n * Creates a refraction texture used by refraction channel of the standard material.\r\n * It is like a mirror but to see through a material.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/reflectionTexture#refractiontexture\r\n */\r\nexport class RefractionTexture extends RenderTargetTexture {\r\n    /**\r\n     * Define the reflection plane we want to use. The refractionPlane is usually set to the constructed refractor.\r\n     * It is possible to directly set the refractionPlane by directly using a Plane(a, b, c, d) where a, b and c give the plane normal vector (a, b, c) and d is a scalar displacement from the refractionPlane to the origin. However in all but the very simplest of situations it is more straight forward to set it to the refractor as stated in the doc.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/reflectionTexture#refraction\r\n     */\r\n    public refractionPlane = new Plane(0, 1, 0, 1);\r\n\r\n    /**\r\n     * Define how deep under the surface we should see.\r\n     */\r\n    public depth = 2.0;\r\n\r\n    /**\r\n     * Creates a refraction texture used by refraction channel of the standard material.\r\n     * It is like a mirror but to see through a material.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/reflectionTexture#refraction\r\n     * @param name Define the texture name\r\n     * @param size Define the size of the underlying texture\r\n     * @param scene Define the scene the refraction belongs to\r\n     * @param generateMipMaps Define if we need to generate mips level for the refraction\r\n     */\r\n    constructor(name: string, size: number, scene?: Scene, generateMipMaps?: boolean) {\r\n        super(name, size, scene, generateMipMaps, true);\r\n\r\n        this.onBeforeRenderObservable.add(() => {\r\n            this.getScene()!.clipPlane = this.refractionPlane;\r\n        });\r\n\r\n        this.onAfterRenderObservable.add(() => {\r\n            this.getScene()!.clipPlane = null;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Clone the refraction texture.\r\n     * @returns the cloned texture\r\n     */\r\n    public clone(): RefractionTexture {\r\n        const scene = this.getScene();\r\n\r\n        if (!scene) {\r\n            return this;\r\n        }\r\n\r\n        const textureSize = this.getSize();\r\n        const newTexture = new RefractionTexture(this.name, textureSize.width, scene, this._generateMipMaps);\r\n\r\n        // Base texture\r\n        newTexture.hasAlpha = this.hasAlpha;\r\n        newTexture.level = this.level;\r\n\r\n        // Refraction Texture\r\n        newTexture.refractionPlane = this.refractionPlane.clone();\r\n        if (this.renderList) {\r\n            newTexture.renderList = this.renderList.slice(0);\r\n        }\r\n        newTexture.depth = this.depth;\r\n\r\n        return newTexture;\r\n    }\r\n\r\n    /**\r\n     * Serialize the texture to a JSON representation you could use in Parse later on\r\n     * @returns the serialized JSON representation\r\n     */\r\n    public serialize(): any {\r\n        if (!this.name) {\r\n            return null;\r\n        }\r\n\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.mirrorPlane = this.refractionPlane.asArray();\r\n        serializationObject.depth = this.depth;\r\n\r\n        return serializationObject;\r\n    }\r\n}\r\n"]}