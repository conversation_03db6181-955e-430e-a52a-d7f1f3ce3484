{"version": 3, "file": "subSurfaceBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/PBR/subSurfaceBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,oCAAoC,EAAE,MAAM,wCAAwC,CAAC;AAC9F,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AAKxG,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,iBAAiB;IAClD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,MAAM,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,uBAAuB,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAClI,IAAI,CAAC,aAAa,CAAC,2BAA2B,EAAE,qCAAqC,CAAC,MAAM,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACvI,IAAI,CAAC,aAAa,CACd,YAAY,EACZ,qCAAqC,CAAC,MAAM,EAC5C,IAAI,EACJ,wBAAwB,CAAC,QAAQ,EACjC,IAAI,uCAAuC,CAAC,YAAY,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAClJ,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAEvH,IAAI,CAAC,cAAc,CACf,YAAY,EACZ,qCAAqC,CAAC,MAAM,EAC5C,wBAAwB,CAAC,QAAQ,EACjC,IAAI,uCAAuC,CAAC,YAAY,EAAE,IAAI,EAAE,oCAAoC,CAAC,MAAM,EAAE,eAAe,EAAE,iBAAiB,CAAC,CACnJ,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,KAA6B;QAC3C,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAC5C,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAC9C,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QACzC,KAAK,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;QACnD,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa;QAChB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YAC7B,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,sBAAsB,EAAE,wBAAwB,CAAC,QAAQ,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;YAC9I,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC;YACzB,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACnD;IACL,CAAC;IAEM,cAAc,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QAC9F,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;QAElD,MAAM,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,WAAW,IAAI,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC;QAEjH,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,mBAAmB,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACzF,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;QAC/D,OAAO,CAAC,QAAQ,CAAC,6BAA6B,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC7D,OAAO,CAAC,QAAQ,CAAC,gCAAgC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAChE,OAAO,CAAC,QAAQ,CAAC,kCAAkC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAClE,OAAO,CAAC,QAAQ,CAAC,gCAAgC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAChE,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACtD,OAAO,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACzE,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,OAAO,CAAC,KAA6B,EAAE,OAAkC,EAAE,eAA0C,EAAE,eAAuB;QACxJ,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,MAAM,SAAS,GAAG,OAAO,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QACnG,MAAM,SAAS,GAAG,OAAO,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC;QACzG,MAAM,qBAAqB,GAAG,OAAO,EAAE,qBAAqB,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE,qBAAqB,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QACxI,MAAM,6BAA6B,GAAG,OAAO,EAAE,yBAAyB,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE,yBAAyB,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC;QAE9J,MAAM,eAAe,GAA8B,CAAC,OAAO,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAA8B,CAAC;QAE1K,MAAM,wBAAwB,GAAG,eAAe,EAAE,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5I,MAAM,mBAAmB,GAAG,eAAe,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7H,MAAM,cAAc,GAAG,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC;QAE5G,MAAM,UAAU,GAAG,OAAO,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC,KAAK,CAAC;QAExG,IAAI,IAAI,eAAe,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAE9C,IAAI,IAAI;;;8CAG8B,SAAS;qCAClB,SAAS,KAAK,wBAAwB;+CAC5B,mBAAmB,KAAK,qBAAqB;iCAC3D,UAAU;;;;;;;;;;;;sBAYrB,eAAe,EAAE,qBAAqB;;;;;;8BAM9B,eAAe,EAAE,gBAAgB;8BACjC,eAAe,EAAE,6BAA6B;;;;;;;;;;;;kBAY1D,eAAe;;kBAEf,cAAc;kBACd,eAAe,EAAE,qBAAqB,IAAI,EAAE;kBAC5C,eAAe,EAAE,qBAAqB,IAAI,EAAE;kBAC5C,eAAe,EAAE,iCAAiC,IAAI,EAAE;;;;;yBAKjD,eAAe,EAAE,yBAAyB,IAAI,QAAQ;;;yBAGtD,eAAe,EAAE,+BAA+B,IAAI,QAAQ;;;;yBAI5D,eAAe,EAAE,aAAa,IAAI,QAAQ;sBAC7C,eAAe,EAAE,gBAAgB,IAAI,EAAE;;sBAEvC,eAAe,EAAE,cAAc,IAAI,EAAE;;;6BAG9B,eAAe,EAAE,aAAa,IAAI,QAAQ;0BAC7C,eAAe,EAAE,gBAAgB,IAAI,EAAE;0BACvC,eAAe,EAAE,gBAAgB,IAAI,EAAE;;0BAEvC,eAAe,EAAE,cAAc,IAAI,EAAE;0BACrC,eAAe,EAAE,cAAc,IAAI,EAAE;;;;;;;sBAOzC,eAAe,EAAE,6BAA6B,IAAI,EAAE;;;;;;;;;;;kBAWxD,6BAA6B;;;;;;;;;;;;;iBAa9B,CAAC;QAEV,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACpD,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport type { ReflectionBlock } from \"./reflectionBlock\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { RefractionBlock } from \"./refractionBlock\";\r\n\r\n/**\r\n * Block used to implement the sub surface module of the PBR material\r\n */\r\nexport class SubSurfaceBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new SubSurfaceBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"thickness\", NodeMaterialBlockConnectionPointTypes.Float, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"tintColor\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"translucencyIntensity\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"translucencyDiffusionDist\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\r\n            \"refraction\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"refraction\", this, NodeMaterialConnectionPointDirection.Input, RefractionBlock, \"RefractionBlock\")\r\n        );\r\n        this.registerInput(\"dispersion\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\r\n            \"subsurface\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"subsurface\", this, NodeMaterialConnectionPointDirection.Output, SubSurfaceBlock, \"SubSurfaceBlock\")\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"subSurfaceOut\");\r\n        state._excludeVariableName(\"vThicknessParam\");\r\n        state._excludeVariableName(\"vTintColor\");\r\n        state._excludeVariableName(\"vSubSurfaceIntensity\");\r\n        state._excludeVariableName(\"dispersion\");\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"SubSurfaceBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the thickness component\r\n     */\r\n    public get thickness(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the tint color input component\r\n     */\r\n    public get tintColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the translucency intensity input component\r\n     */\r\n    public get translucencyIntensity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the translucency diffusion distance input component\r\n     */\r\n    public get translucencyDiffusionDist(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the refraction object parameters\r\n     */\r\n    public get refraction(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the dispersion input component\r\n     */\r\n    public get dispersion(): NodeMaterialConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the sub surface object output component\r\n     */\r\n    public get subsurface(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public autoConfigure() {\r\n        if (!this.thickness.isConnected) {\r\n            const thicknessInput = new InputBlock(\"SubSurface thickness\", NodeMaterialBlockTargets.Fragment, NodeMaterialBlockConnectionPointTypes.Float);\r\n            thicknessInput.value = 0;\r\n            thicknessInput.output.connectTo(this.thickness);\r\n        }\r\n    }\r\n\r\n    public prepareDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        super.prepareDefines(mesh, nodeMaterial, defines);\r\n\r\n        const translucencyEnabled = this.translucencyDiffusionDist.isConnected || this.translucencyIntensity.isConnected;\r\n\r\n        defines.setValue(\"SUBSURFACE\", translucencyEnabled || this.refraction.isConnected, true);\r\n        defines.setValue(\"SS_TRANSLUCENCY\", translucencyEnabled, true);\r\n        defines.setValue(\"SS_THICKNESSANDMASK_TEXTURE\", false, true);\r\n        defines.setValue(\"SS_REFRACTIONINTENSITY_TEXTURE\", false, true);\r\n        defines.setValue(\"SS_TRANSLUCENCYINTENSITY_TEXTURE\", false, true);\r\n        defines.setValue(\"SS_MASK_FROM_THICKNESS_TEXTURE\", false, true);\r\n        defines.setValue(\"SS_USE_GLTF_TEXTURES\", false, true);\r\n        defines.setValue(\"SS_DISPERSION\", this.dispersion.isConnected, true);\r\n    }\r\n\r\n    /**\r\n     * Gets the main code of the block (fragment side)\r\n     * @param state current state of the node material building\r\n     * @param ssBlock instance of a SubSurfaceBlock or null if the code must be generated without an active sub surface module\r\n     * @param reflectionBlock instance of a ReflectionBlock null if the code must be generated without an active reflection module\r\n     * @param worldPosVarName name of the variable holding the world position\r\n     * @returns the shader code\r\n     */\r\n    public static GetCode(state: NodeMaterialBuildState, ssBlock: Nullable<SubSurfaceBlock>, reflectionBlock: Nullable<ReflectionBlock>, worldPosVarName: string): string {\r\n        let code = \"\";\r\n\r\n        const thickness = ssBlock?.thickness.isConnected ? ssBlock.thickness.associatedVariableName : \"0.\";\r\n        const tintColor = ssBlock?.tintColor.isConnected ? ssBlock.tintColor.associatedVariableName : \"vec3(1.)\";\r\n        const translucencyIntensity = ssBlock?.translucencyIntensity.isConnected ? ssBlock?.translucencyIntensity.associatedVariableName : \"1.\";\r\n        const translucencyDiffusionDistance = ssBlock?.translucencyDiffusionDist.isConnected ? ssBlock?.translucencyDiffusionDist.associatedVariableName : \"vec3(1.)\";\r\n\r\n        const refractionBlock: Nullable<RefractionBlock> = (ssBlock?.refraction.isConnected ? ssBlock?.refraction.connectedPoint?.ownerBlock : null) as Nullable<RefractionBlock>;\r\n\r\n        const refractionTintAtDistance = refractionBlock?.tintAtDistance.isConnected ? refractionBlock.tintAtDistance.associatedVariableName : \"1.\";\r\n        const refractionIntensity = refractionBlock?.intensity.isConnected ? refractionBlock.intensity.associatedVariableName : \"1.\";\r\n        const refractionView = refractionBlock?.view.isConnected ? refractionBlock.view.associatedVariableName : \"\";\r\n\r\n        const dispersion = ssBlock?.dispersion.isConnected ? ssBlock?.dispersion.associatedVariableName : \"0.0\";\r\n\r\n        code += refractionBlock?.getCode(state) ?? \"\";\r\n\r\n        code += `subSurfaceOutParams subSurfaceOut;\r\n\r\n        #ifdef SUBSURFACE\r\n            vec2 vThicknessParam = vec2(0., ${thickness});\r\n            vec4 vTintColor = vec4(${tintColor}, ${refractionTintAtDistance});\r\n            vec3 vSubSurfaceIntensity = vec3(${refractionIntensity}, ${translucencyIntensity}, 0.);\r\n            float dispersion = ${dispersion};\r\n            subSurfaceBlock(\r\n                vSubSurfaceIntensity,\r\n                vThicknessParam,\r\n                vTintColor,\r\n                normalW,\r\n                specularEnvironmentReflectance,\r\n            #ifdef SS_THICKNESSANDMASK_TEXTURE\r\n                vec4(0.),\r\n            #endif\r\n            #ifdef REFLECTION\r\n                #ifdef SS_TRANSLUCENCY\r\n                    ${reflectionBlock?._reflectionMatrixName},\r\n                    #ifdef USESPHERICALFROMREFLECTIONMAP\r\n                        #if !defined(NORMAL) || !defined(USESPHERICALINVERTEX)\r\n                            reflectionOut.irradianceVector,\r\n                        #endif\r\n                        #if defined(REALTIME_FILTERING)\r\n                            ${reflectionBlock?._cubeSamplerName},\r\n                            ${reflectionBlock?._vReflectionFilteringInfoName},\r\n                        #endif\r\n                        #endif\r\n                    #ifdef USEIRRADIANCEMAP\r\n                        irradianceSampler,\r\n                    #endif\r\n                #endif\r\n            #endif\r\n            #if defined(SS_REFRACTION) || defined(SS_TRANSLUCENCY)\r\n                surfaceAlbedo,\r\n            #endif\r\n            #ifdef SS_REFRACTION\r\n                ${worldPosVarName}.xyz,\r\n                viewDirectionW,\r\n                ${refractionView},\r\n                ${refractionBlock?._vRefractionInfosName ?? \"\"},\r\n                ${refractionBlock?._refractionMatrixName ?? \"\"},\r\n                ${refractionBlock?._vRefractionMicrosurfaceInfosName ?? \"\"},\r\n                vLightingIntensity,\r\n                #ifdef SS_LINKREFRACTIONTOTRANSPARENCY\r\n                    alpha,\r\n                #endif\r\n                #ifdef ${refractionBlock?._defineLODRefractionAlpha ?? \"IGNORE\"}\r\n                    NdotVUnclamped,\r\n                #endif\r\n                #ifdef ${refractionBlock?._defineLinearSpecularRefraction ?? \"IGNORE\"}\r\n                    roughness,\r\n                #endif\r\n                alphaG,\r\n                #ifdef ${refractionBlock?._define3DName ?? \"IGNORE\"}\r\n                    ${refractionBlock?._cubeSamplerName ?? \"\"},\r\n                #else\r\n                    ${refractionBlock?._2DSamplerName ?? \"\"},\r\n                #endif\r\n                #ifndef LODBASEDMICROSFURACE\r\n                    #ifdef ${refractionBlock?._define3DName ?? \"IGNORE\"}\r\n                        ${refractionBlock?._cubeSamplerName ?? \"\"},\r\n                        ${refractionBlock?._cubeSamplerName ?? \"\"},\r\n                    #else\r\n                        ${refractionBlock?._2DSamplerName ?? \"\"},\r\n                        ${refractionBlock?._2DSamplerName ?? \"\"},\r\n                    #endif\r\n                #endif\r\n                #ifdef ANISOTROPIC\r\n                    anisotropicOut,\r\n                #endif\r\n                #ifdef REALTIME_FILTERING\r\n                    ${refractionBlock?._vRefractionFilteringInfoName ?? \"\"},\r\n                #endif\r\n                #ifdef SS_USE_LOCAL_REFRACTIONMAP_CUBIC\r\n                    vRefractionPosition,\r\n                    vRefractionSize,\r\n                #endif\r\n                #ifdef SS_DISPERSION\r\n                    dispersion,\r\n                #endif\r\n            #endif\r\n            #ifdef SS_TRANSLUCENCY\r\n                ${translucencyDiffusionDistance},\r\n            #endif\r\n                subSurfaceOut\r\n            );\r\n\r\n            #ifdef SS_REFRACTION\r\n                surfaceAlbedo = subSurfaceOut.surfaceAlbedo;\r\n                #ifdef SS_LINKREFRACTIONTOTRANSPARENCY\r\n                    alpha = subSurfaceOut.alpha;\r\n                #endif\r\n            #endif\r\n        #else\r\n            subSurfaceOut.specularEnvironmentReflectance = specularEnvironmentReflectance;\r\n        #endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.sharedData.blocksWithDefines.push(this);\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SubSurfaceBlock\", SubSurfaceBlock);\r\n"]}