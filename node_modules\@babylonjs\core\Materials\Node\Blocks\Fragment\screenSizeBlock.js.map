{"version": 3, "file": "screenSizeBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Fragment/screenSizeBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAI3D;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,iBAAiB;IAIlD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,qCAAqC,CAAC,OAAO,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC5G,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACzG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;IAC7G,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,EAAE;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,IAAI,CAAC,MAAc;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;IACvF,CAAC;IAED,gEAAgE;IACtD,YAAY,CAAC,KAA6B,EAAE,OAAe;QACjE,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,MAAM,CAAC,YAAY,EAAE;gBACrB,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC;aAClF;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;QAErC,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,EAAE;YAClD,4CAA4C;YAC5C,MAAM,wDAAwD,CAAC;SAClE;QAED,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QACzD,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEpD,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEnE,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { Scene } from \"../../../../scene\";\r\n\r\n/**\r\n * Block used to get the screen sizes\r\n */\r\nexport class ScreenSizeBlock extends NodeMaterialBlock {\r\n    private _varName: string;\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * Creates a new ScreenSizeBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\"xy\", NodeMaterialBlockConnectionPointTypes.Vector2, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"x\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"y\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"ScreenSizeBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the xy component\r\n     */\r\n    public get xy(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the x component\r\n     */\r\n    public get x(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the y component\r\n     */\r\n    public get y(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    public bind(effect: Effect) {\r\n        const engine = this._scene.getEngine();\r\n\r\n        effect.setFloat2(this._varName, engine.getRenderWidth(), engine.getRenderHeight());\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected writeOutputs(state: NodeMaterialBuildState, varName: string): string {\r\n        let code = \"\";\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints) {\r\n                code += `${this._declareOutput(output, state)} = ${varName}.${output.name};\\n`;\r\n            }\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        this._scene = state.sharedData.scene;\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Vertex) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"ScreenSizeBlock must only be used in a fragment shader\";\r\n        }\r\n\r\n        state.sharedData.bindableBlocks.push(this);\r\n\r\n        this._varName = state._getFreeVariableName(\"screenSize\");\r\n        state._emitUniformFromString(this._varName, \"vec2\");\r\n\r\n        state.compilationString += this.writeOutputs(state, this._varName);\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ScreenSizeBlock\", ScreenSizeBlock);\r\n"]}