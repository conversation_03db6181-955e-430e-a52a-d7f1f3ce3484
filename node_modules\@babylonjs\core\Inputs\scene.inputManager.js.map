{"version": 3, "file": "scene.inputManager.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Inputs/scene.inputManager.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAEzF,OAAO,EAAE,qBAAqB,EAAE,MAAM,kCAAkC,CAAC;AACzE,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAEvD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,kBAAkB,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAC7F,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,yCAAyC,CAAC;AAEnF,OAAO,EAAE,mBAAmB,EAAE,MAAM,iDAAiD,CAAC;AACtF,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAIrD,gBAAgB;AAChB,gEAAgE;AAChE,MAAM,UAAU;IAAhB;QACY,iBAAY,GAAG,KAAK,CAAC;QACrB,iBAAY,GAAG,KAAK,CAAC;QACrB,eAAU,GAAG,KAAK,CAAC;QACnB,YAAO,GAAG,KAAK,CAAC;IA2B5B,CAAC;IAzBG,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IACD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IACD,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,WAAW,CAAC,CAAU;QAC7B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;IAC1B,CAAC;IACD,IAAW,WAAW,CAAC,CAAU;QAC7B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;IAC1B,CAAC;IACD,IAAW,SAAS,CAAC,CAAU;QAC3B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;IACxB,CAAC;IACD,IAAW,MAAM,CAAC,CAAU;QACxB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;IACrB,CAAC;CACJ;AASD;;GAEG;AACH,MAAM,OAAO,YAAY;IAqErB;;;OAGG;IACH,YAAY,KAAa;QA3DzB,kLAAkL;QAC1K,qBAAgB,GAAG,KAAK,CAAC;QAgBzB,qBAAgB,GAAG,KAAK,CAAC;QAGzB,uBAAkB,GAA0B,IAAI,CAAC;QACjD,wBAAmB,GAA0B,IAAI,CAAC;QAClD,0BAAqB,GAAG,CAAC,CAAC;QAC1B,wBAAmB,GAAG,KAAK,CAAC;QAC5B,eAAU,GAAY,KAAK,CAAC;QAC5B,wBAAmB,GAAW,CAAC,CAAC,CAAC;QACjC,oBAAe,GAAY,KAAK,CAAC;QACjC,yBAAoB,GAAY,KAAK,CAAC;QAOtC,cAAS,GAAW,CAAC,CAAC;QACtB,cAAS,GAAW,CAAC,CAAC;QAGtB,6BAAwB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,qCAAgC,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,yBAAoB,GAAG,CAAC,CAAC;QACzB,iCAA4B,GAAG,CAAC,CAAC;QACjC,qBAAgB,GAAqC,EAAE,CAAC;QACxD,wBAAmB,GAAoD,EAAE,CAAC;QAC1E,qBAAgB,GAA0B,IAAI,CAAC;QAC/C,yBAAoB,GAAG,CAAC,CAAC;QACzB,mBAAc,GAAkC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAO/E,yBAAoB,GAAkC,IAAI,CAAC;QAO/D,IAAI,CAAC,MAAM,GAAG,KAAK,IAAW,WAAW,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,OAAO;SACV;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,gBAAgB;QACvB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,oGAAoG;YACpG,sFAAsF;YACtF,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;YAC1C,4FAA4F;YAC5F,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACI,8BAA8B,CAAC,SAAiB;QACnD,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;IACvD,CAAC;IAED;;;OAGG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAC/E,CAAC;IAED;;;OAGG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa;QAC7B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa;QAC7B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IAC3B,CAAC;IAEO,sBAAsB,CAAC,GAAkB;QAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,yBAAyB,EAAE,CAAC;QAEvE,IAAI,CAAC,UAAU,EAAE;YACb,OAAO;SACV;QAED,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC;QAE9C,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC;QAC5C,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC;IAChD,CAAC;IAEO,mBAAmB,CAAC,UAAiC,EAAE,GAAkB;QAC7E,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,MAAM,MAAM,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;QAExC,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC;YAExC,kBAAkB;YAClB,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;gBAC3B,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC;aAC7C;SACJ;QAED,IAAI,CAAC,4BAA4B,CAAC,UAAU,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QAE1D,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,iBAAiB,EAAE;YACxC,8FAA8F;YAC9F,8DAA8D;YAC9D,UAAU,GAAG,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC/C,MAAM,YAAY,GAAG,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;YAC3D,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;SACtH;QAED,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,IAAI,YAAY,CAAC,WAAW,IAAI,GAAG,CAAC,UAAU,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,WAAW,CAAC;QAEvK,IAAI,KAAK,CAAC,aAAa,EAAE;YACrB,4EAA4E;YAC5E,UAAU,GAAG,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC/C,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;SAC9C;QAED,IAAI,WAAwB,CAAC;QAC7B,IAAI,UAAU,EAAE;YACZ,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;YACrD,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;SAC9C;aAAM;YACH,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACrD,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;SACvC;QAED,IAAI,KAAK,CAAC,mBAAmB,CAAC,YAAY,EAAE,EAAE;YAC1C,KAAK,CAAC,mBAAmB,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;SAChE;IACL,CAAC;IAED,oBAAoB;IACpB,gBAAgB;IACT,oBAAoB,CAAC,QAA+B,EAAE,KAAkB;QAC3E,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,QAAQ,IAAI,KAAK,CAAC,iBAAiB,EAAE;YACrC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;gBACf,QAAQ,CAAC,GAAG,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;aAC9G;SACJ;IACL,CAAC;IAED,gBAAgB;IACT,yBAAyB,CAAC,QAAiD,EAAE,IAAa;QAC7F,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC/D,CAAC;IAED,gBAAgB;IACT,4BAA4B,CAAC,QAA+B;QAC/D,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC5D,CAAC;IAEO,gBAAgB;QACpB,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACzH,CAAC;IAEO,0BAA0B,CAAC,UAAiC,EAAE,GAAkB,EAAE,IAAY;QAClG,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,EAAE,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACjG,IAAI,UAAU,EAAE;YACZ,EAAE,CAAC,mBAAmB,GAAG,UAAU,CAAC;YACpC,EAAE,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;YACxB,IAAI,GAAG,CAAC,WAAW,KAAK,SAAS,IAAI,UAAU,CAAC,UAAU,EAAE;gBACxD,EAAE,CAAC,0BAA0B,GAAG,UAAU,CAAC;aAC9C;SACJ;QAED,KAAK,CAAC,sBAAsB,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QACvD,IAAI,EAAE,CAAC,uBAAuB,EAAE;YAC5B,OAAO,IAAI,CAAC;SACf;aAAM;YACH,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED,gBAAgB;IACT,SAAS,CAAC,GAAkB;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CACzB,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CAAC,qBAAqB,EAC1B,KAAK,CAAC,oBAAoB,EAC1B,KAAK,CAAC,oBAAoB,EAC1B,KAAK,CAAC,sBAAsB,EAC5B,KAAK,CAAC,4BAA4B,CACrC,CAAC;QAEF,IAAI,CAAC,4BAA4B,CAAC,UAAU,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QAE1D,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,4BAA4B,CAAC,UAAiC,EAAE,GAAkB,EAAE,KAAY;QACpG,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,MAAM,MAAM,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;QAExC,IAAI,UAAU,EAAE,UAAU,EAAE;YACxB,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;YAE/E,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBAC9D,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,EAAE,CAAC;gBAC1E,IAAI,aAAa,IAAI,aAAa,CAAC,kBAAkB,EAAE;oBACnD,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC;iBACxE;aACJ;SACJ;aAAM;YACH,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;SACjE;IACL,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,UAAuB,EAAE,gBAAmC;QACnF,MAAM,GAAG,GAAG,IAAI,YAAY,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;QAC9D,GAAG,CAAC,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC;QAEnC,IAAI,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,GAAG,EAAE,iBAAiB,CAAC,WAAW,CAAC,EAAE;YACjF,OAAO;SACV;QACD,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,UAAuB,EAAE,gBAAmC;QACnF,MAAM,GAAG,GAAG,IAAI,YAAY,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;QAC9D,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;QAEhC,IAAI,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,GAAG,EAAE,iBAAiB,CAAC,WAAW,CAAC,EAAE;YACjF,OAAO;SACV;QAED,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IAC9C,CAAC;IAEO,mBAAmB,CAAC,UAAiC,EAAE,GAAkB;QAC7E,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,UAAU,EAAE,UAAU,EAAE;YACxB,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC;YAC7C,MAAM,aAAa,GAAG,UAAU,CAAC,UAAU,CAAC,2BAA2B,EAAE,CAAC;YAC1E,IAAI,aAAa,EAAE;gBACf,IAAI,aAAa,CAAC,eAAe,EAAE;oBAC/B,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,EAAE,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;oBAChI,QAAQ,GAAG,CAAC,MAAM,EAAE;wBAChB,KAAK,CAAC;4BACF,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,EAAE,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;4BAChI,MAAM;wBACV,KAAK,CAAC;4BACF,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,0BAA0B,EAAE,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;4BAClI,MAAM;wBACV,KAAK,CAAC;4BACF,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,yBAAyB,EAAE,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;4BACjI,MAAM;qBACb;iBACJ;gBAED,IAAI,aAAa,CAAC,kBAAkB,CAAC,SAAS,CAAC,yBAAyB,CAAC,EAAE;oBACvE,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE;wBACnB,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CACzB,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CAAC,qBAAqB,EAC1B,CAAC,IAAkB,EAAW,EAAE,CACnB,CACL,CAAC,IAAI,CAAC,UAAU;4BACZ,IAAI,CAAC,SAAS;4BACd,IAAI,CAAC,OAAO,EAAE;4BACd,IAAI,CAAC,aAAa;4BAClB,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,SAAS,CAAC,yBAAyB,CAAC;4BAC1E,IAAI,KAAK,IAAI,CAAC,eAAe,CAAC,CACrC,EACL,KAAK,EACL,KAAK,CAAC,sBAAsB,CAC/B,CAAC;wBAEF,IAAI,UAAU,EAAE,UAAU,IAAI,aAAa,EAAE;4BACzC,IAAI,IAAI,CAAC,qBAAqB,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE;gCACvI,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;gCAC9B,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,yBAAyB,EAAE,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC;6BACxH;yBACJ;oBACL,CAAC,EAAE,YAAY,CAAC,cAAc,CAAC,CAAC;iBACnC;aACJ;SACJ;aAAM;YACH,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,iBAAiB,EAAE;gBACxC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE,UAAU,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;aAC5G;SACJ;QAED,IAAI,WAAwB,CAAC;QAC7B,MAAM,IAAI,GAAG,iBAAiB,CAAC,WAAW,CAAC;QAE3C,IAAI,UAAU,EAAE;YACZ,IAAI,KAAK,CAAC,aAAa,EAAE;gBACrB,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;aAC9C;YAED,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;YACrD,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;SAC9C;aAAM;YACH,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;SACxD;QAED,IAAI,KAAK,CAAC,mBAAmB,CAAC,YAAY,EAAE,EAAE;YAC1C,KAAK,CAAC,mBAAmB,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;SAChE;IACL,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG;IACI,iBAAiB,CAAC,UAAuB,EAAE,gBAAmC,EAAE,SAAmB;QACtG,MAAM,GAAG,GAAG,IAAI,YAAY,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;QAC5D,GAAG,CAAC,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC;QACnC,MAAM,SAAS,GAAG,IAAI,UAAU,EAAE,CAAC;QAEnC,IAAI,SAAS,EAAE;YACX,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;SAChC;aAAM;YACH,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;SAChC;QAED,IAAI,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,GAAG,EAAE,iBAAiB,CAAC,SAAS,CAAC,EAAE;YAC/E,OAAO;SACV;QAED,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;IACvD,CAAC;IAEO,iBAAiB,CAAC,UAAiC,EAAE,GAAkB,EAAE,SAAqB;QAClG,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,UAAU,EAAE,UAAU,EAAE;YACxB,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,UAAU,CAAC;YAC3C,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,aAAa,EAAE;gBAC7C,IAAI,KAAK,CAAC,aAAa,EAAE;oBACrB,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;iBACxC;gBACD,IAAI,SAAS,CAAC,WAAW,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,EAAE;oBACtH,MAAM,IAAI,GAAG,iBAAiB,CAAC,WAAW,CAAC;oBAC3C,MAAM,EAAE,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;oBAClD,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;oBAC3C,KAAK,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;iBACvD;aACJ;YACD,MAAM,aAAa,GAAG,UAAU,CAAC,UAAU,CAAC,2BAA2B,EAAE,CAAC;YAC1E,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBACpC,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,sBAAsB,EAAE,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;gBAE9H,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,WAAW,EAAE;oBAC/C,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,oBAAoB,EAAE,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;iBAC/H;gBAED,MAAM,wBAAwB,GAAG,UAAU,CAAC,UAAU,CAAC,2BAA2B,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;gBACzH,IAAI,SAAS,CAAC,WAAW,IAAI,wBAAwB,EAAE;oBACnD,wBAAwB,CAAC,cAAc,CAAC,SAAS,CAAC,0BAA0B,EAAE,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;iBAChJ;aACJ;SACJ;aAAM;YACH,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBACnB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,eAAe,EAAE;oBACtC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE,UAAU,EAAE,GAAG,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC;iBAC5H;aACJ;SACJ;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,aAAa,EAAE;YACrE,MAAM,uBAAuB,GAAG,IAAI,CAAC,eAAe,CAAC,2BAA2B,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;YACpH,IAAI,uBAAuB,EAAE;gBACzB,uBAAuB,CAAC,cAAc,CAAC,SAAS,CAAC,uBAAuB,EAAE,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC;aAC/H;SACJ;QAED,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACnB,MAAM,EAAE,GAAG,IAAI,WAAW,CAAC,iBAAiB,CAAC,SAAS,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;YACzE,8FAA8F;YAC9F,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAC3C,KAAK,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAE3E,IAAI,KAAK,CAAC,WAAW,EAAE;gBACnB,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,UAAU,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC;aACnE;YAED,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC7E,IAAI,IAAI,GAAG,CAAC,CAAC;gBACb,IAAI,SAAS,CAAC,WAAW,EAAE;oBACvB,IAAI,GAAG,iBAAiB,CAAC,UAAU,CAAC;iBACvC;qBAAM,IAAI,SAAS,CAAC,WAAW,EAAE;oBAC9B,IAAI,GAAG,iBAAiB,CAAC,gBAAgB,CAAC;iBAC7C;gBAED,IAAI,IAAI,EAAE;oBACN,MAAM,EAAE,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;oBAClD,IAAI,KAAK,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,KAAK,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;wBAC7F,KAAK,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;qBACvD;iBACJ;aACJ;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,SAAS,GAAG,CAAC;QAClC,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;;OAMG;IACI,aAAa,CAAC,QAAQ,GAAG,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,oBAA2C,IAAI;QACvH,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,CAAC,iBAAiB,EAAE;YACpB,iBAAiB,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;SAChD;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;QAED,IAAI,iBAAiB,EAAE;YACnB,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;SAC/C;QACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAE5D,wIAAwI;QACxI,IAAI,CAAC,kBAAkB,GAAG,CAAC,GAAoC,EAAmC,EAAE;YAChG,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBACxB,MAAM,UAAU,GACZ,KAAK,CAAC,oBAAoB,IAAI,CAAC,KAAK,CAAC,kBAAkB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;oBAC5G,CAAC,CAAC,IAAI;oBACN,CAAC,CAAC,KAAK,CAAC,IAAI,CACN,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CAAC,qBAAqB,EAC1B,KAAK,CAAC,kBAAkB,EACxB,KAAK,CAAC,kBAAkB,EACxB,KAAK,CAAC,sBAAsB,EAC5B,KAAK,CAAC,0BAA0B,CACnC,CAAC;gBACZ,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC;gBACrC,IAAI,UAAU,EAAE;oBACZ,GAAG,GAAG,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;iBAC9G;gBACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;aAChC;YACD,OAAO,GAAG,CAAC;QACf,CAAC,CAAC;QAEF,IAAI,CAAC,mBAAmB,GAAG,CAAC,GAAW,EAAE,SAAqB,EAAE,EAAsE,EAAE,EAAE;YACtI,6HAA6H;YAC7H,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,4BAA4B,GAAG,YAAY,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,GAAG,KAAK,IAAI,CAAC,sBAAsB,EAAE;gBACtJ,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;gBACjC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;gBAC7B,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;gBAEzB,+DAA+D;gBAC/D,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;oBAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAE,CAAC,GAAG,CAAC;oBAC1C,MAAM,IAAI,GAAG,iBAAiB,CAAC,UAAU,CAAC;oBAC1C,MAAM,EAAE,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBAC/D,IAAI,KAAK,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,KAAK,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;wBAC7F,KAAK,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;qBACvD;oBAED,0BAA0B;oBAC1B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;iBACnC;aACJ;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,eAAe,GAAG,CACnB,IAAgC,EAChC,IAA6B,EAC7B,GAAkB,EAClB,EAAsE,EAClE,EAAE;YACN,MAAM,SAAS,GAAG,IAAI,UAAU,EAAE,CAAC;YACnC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,GAAG,GAAoC,IAAI,CAAC;YAEhD,IAAI,YAAY,GACZ,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,WAAW,CAAC;gBACnD,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,WAAW,CAAC;gBACnD,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,UAAU,CAAC;gBAClD,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,UAAU,CAAC;gBAClD,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;gBACxD,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAC7D,IAAI,CAAC,YAAY,IAAI,qBAAqB,EAAE;gBACxC,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;gBAC9C,IAAI,GAAG,EAAE;oBACL,YAAY,GAAG,GAAG,CAAC,eAAe,CAAC;iBACtC;aACJ;YAED,IAAI,gBAAgB,GAAG,KAAK,CAAC;YAE7B,IAAI,YAAY,EAAE;gBACd,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;gBACvB,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAE/C,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;oBACtB,IAAI,2BAA2B,GAAG,CAAC,YAAY,CAAC,wBAAwB,CAAC;oBAEzE,IAAI,CAAC,2BAA2B,EAAE;wBAC9B,2BAA2B,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;wBAErJ,IAAI,2BAA2B,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,SAAS,CAAC,0BAA0B,CAAC,EAAE;4BAChH,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;4BAC9C,IAAI,GAAG,EAAE;gCACL,2BAA2B,GAAG,CAAC,GAAG,CAAC,kBAAkB,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;6BAC/F;yBACJ;qBACJ;oBAED,IAAI,2BAA2B,EAAE;wBAC7B,0JAA0J;wBAC1J,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,4BAA4B,GAAG,YAAY,CAAC,gBAAgB,IAAI,GAAG,KAAK,IAAI,CAAC,sBAAsB,EAAE;4BACvH,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;4BAC7B,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;4BACvC,gBAAgB,GAAG,IAAI,CAAC;yBAC3B;qBACJ;oBACD,0FAA0F;yBACrF;wBACD,mEAAmE;wBACnE,2DAA2D;wBAC3D,iEAAiE;wBACjE,gFAAgF;wBAChF,MAAM,YAAY,GAAG;4BACjB,GAAG,EAAE,GAAG;4BACR,SAAS,EAAE,SAAS;4BACpB,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,CAAC,EAAE,YAAY,CAAC,gBAAgB,CAAC;yBACvH,CAAC;wBAEF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;qBAC3C;oBAED,IAAI,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;oBAC5I,IAAI,CAAC,gBAAgB,IAAI,qBAAqB,CAAC,kBAAkB,CAAC,SAAS,CAAC,0BAA0B,CAAC,EAAE;wBACrG,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;wBAC9C,IAAI,GAAG,EAAE;4BACL,gBAAgB,GAAG,GAAG,CAAC,kBAAkB,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;yBACnF;qBACJ;oBACD,IAAI,gBAAgB,EAAE;wBAClB,+GAA+G;wBAC/G,IAAI,GAAG,KAAK,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,4BAA4B,GAAG,YAAY,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;4BACpJ,0DAA0D;4BAC1D,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE;gCACnD,IAAI,CAAC,4BAA4B,GAAG,CAAC,CAAC;gCACtC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gCAChC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;gCAC7B,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;gCACzB,mDAAmD;gCACnD,IAAI,YAAY,CAAC,wBAAwB,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;oCACnE,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC;oCAClD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;iCACnC;gCAED,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;6BAC1C;4BACD,wEAAwE;iCACnE;gCACD,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;gCACjC,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,oBAAoB,CAAC;gCAC9D,IAAI,CAAC,gCAAgC,CAAC,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC;gCAC1E,IAAI,CAAC,gCAAgC,CAAC,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC;gCAC1E,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC;gCAClC,IAAI,YAAY,CAAC,wBAAwB,EAAE;oCACvC,mDAAmD;oCACnD,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;wCAC1B,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC;wCAClD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;qCACnC;oCACD,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;iCAC3C;qCAAM;oCACH,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;iCAC1C;6BACJ;4BACD,gBAAgB,GAAG,IAAI,CAAC;yBAC3B;wBACD,qDAAqD;6BAChD;4BACD,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;4BACjC,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,oBAAoB,CAAC;4BAC9D,IAAI,CAAC,gCAAgC,CAAC,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC;4BAC1E,IAAI,CAAC,gCAAgC,CAAC,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC;4BAC1E,IAAI,CAAC,sBAAsB,GAAG,GAAI,CAAC;yBACtC;qBACJ;iBACJ;aACJ;YAED,qEAAqE;YACrE,yEAAyE;YACzE,IAAI,CAAC,gBAAgB,EAAE;gBACnB,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC1C;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,cAAc,GAAG,CAAC,GAAgB,EAAE,EAAE;YACvC,IAAI,CAAC,sBAAsB,CAAC,GAAoB,CAAC,CAAC;YAElD,yFAAyF;YACzF,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,mBAAmB,KAAK,CAAC,CAAC,EAAE;gBACrD,IAAI,CAAC,UAAU;oBACX,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,YAAY,CAAC,qBAAqB;wBAC/F,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,YAAY,CAAC,qBAAqB,CAAC;aACvG;YAED,gGAAgG;YAChG,wEAAwE;YACxE,IAAI,MAAM,CAAC,aAAa,EAAE;gBACtB,MAAM,CAAC,kBAAkB,EAAE,CAAC;aAC/B;YAED,wBAAwB;YACxB,IACI,IAAI,CAAC,0BAA0B,CAC3B,IAAI,EACJ,GAAoB,EACpB,GAAG,CAAC,UAAU,IAAI,YAAY,CAAC,WAAW,IAAI,GAAG,CAAC,UAAU,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,WAAW,CAC5J,EACH;gBACE,OAAO;aACV;YAED,IAAI,CAAC,KAAK,CAAC,sBAAsB,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;gBACtD,OAAO;aACV;YAED,IAAI,KAAK,CAAC,sBAAsB,EAAE;gBAC9B,IAAI,CAAC,mBAAmB,CAAC,IAAI,WAAW,EAAE,EAAE,GAAoB,CAAC,CAAC;gBAClE,OAAO;aACV;YAED,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBAC7B,KAAK,CAAC,oBAAoB,GAAG,CAAC,IAAkB,EAAW,EAAE,CACzD,IAAI,CAAC,UAAU;oBACf,IAAI,CAAC,SAAS;oBACd,IAAI,CAAC,OAAO,EAAE;oBACd,IAAI,CAAC,SAAS,EAAE;oBAChB,CAAC,IAAI,CAAC,uBAAuB,IAAI,KAAK,CAAC,gCAAgC,IAAI,IAAI,CAAC,2BAA2B,EAAE,KAAK,IAAI,CAAC;oBACvH,CAAC,CAAC,KAAK,CAAC,sBAAsB,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;aAC1G;YAED,MAAM,UAAU,GAAG,KAAK,CAAC,kBAAkB,GAAG,CAAC,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAoB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACxI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,GAAoB,CAAC,CAAC;QAC/D,CAAC,CAAC;QAEF,IAAI,CAAC,cAAc,GAAG,CAAC,GAAkB,EAAE,EAAE;YACzC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAE9B,qFAAqF;YACrF,IAAI,YAAY,CAAC,wBAAwB,EAAE;gBACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACjD,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;wBACxB,2EAA2E;wBAC3E,gEAAgE;wBAChE,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;4BAClB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;yBACnD;6BAAM;4BACH,0CAA0C;4BAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAE,CAAC,SAAS,CAAC;4BACpD,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;4BACjC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;4BAC7B,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;4BAEzB,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAE,CAAC,GAAG,CAAC;4BAC5C,MAAM,IAAI,GAAG,iBAAiB,CAAC,UAAU,CAAC;4BAC1C,MAAM,EAAE,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;4BACnE,IAAI,KAAK,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,KAAK,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;gCAC7F,KAAK,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;6BACvD;4BAED,0BAA0B;4BAC1B,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;yBACjC;qBACJ;iBACJ;aACJ;YAED,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;YAEjC,IAAI,IAAI,CAAC,mBAAmB,KAAK,CAAC,CAAC,EAAE;gBACjC,IAAI,CAAC,mBAAmB,GAAG,GAAG,CAAC,MAAM,CAAC;aACzC;YAED,IAAI,KAAK,CAAC,2BAA2B,IAAI,iBAAiB,EAAE;gBACxD,GAAG,CAAC,cAAc,EAAE,CAAC;gBACrB,iBAAiB,CAAC,KAAK,EAAE,CAAC;aAC7B;YAED,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YACjD,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YACjD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEvC,wBAAwB;YACxB,IAAI,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,GAAG,EAAE,iBAAiB,CAAC,WAAW,CAAC,EAAE;gBAC3E,OAAO;aACV;YAED,IAAI,CAAC,KAAK,CAAC,sBAAsB,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;gBACtD,OAAO;aACV;YAED,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;YAE5C,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBAC7B,KAAK,CAAC,oBAAoB,GAAG,CAAC,IAAkB,EAAW,EAAE;oBACzD,OAAO,CACH,IAAI,CAAC,UAAU;wBACf,IAAI,CAAC,SAAS;wBACd,IAAI,CAAC,OAAO,EAAE;wBACd,IAAI,CAAC,SAAS,EAAE;wBAChB,CAAC,CAAC,KAAK,CAAC,sBAAsB,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CACrG,CAAC;gBACN,CAAC,CAAC;aACL;YAED,SAAS;YACT,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,UAAU,CAAC;YACf,IAAI,KAAK,CAAC,sBAAsB,IAAI,CAAC,KAAK,CAAC,kBAAkB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;gBACtH,UAAU,GAAG,IAAI,WAAW,EAAE,CAAC;aAClC;iBAAM;gBACH,UAAU,GAAG,KAAK,CAAC,IAAI,CACnB,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CAAC,qBAAqB,EAC1B,KAAK,CAAC,oBAAoB,EAC1B,KAAK,CAAC,oBAAoB,EAC1B,KAAK,CAAC,sBAAsB,EAC5B,KAAK,CAAC,4BAA4B,CACrC,CAAC;aACL;YAED,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QAC9C,CAAC,CAAC;QAEF,IAAI,CAAC,YAAY,GAAG,CAAC,GAAkB,EAAE,EAAE;YACvC,IAAI,IAAI,CAAC,qBAAqB,KAAK,CAAC,EAAE;gBAClC,oEAAoE;gBACpE,OAAO,CAAC,6DAA6D;aACxE;YAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAE9B,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;YAEjC,IAAI,KAAK,CAAC,yBAAyB,IAAI,iBAAiB,EAAE;gBACtD,GAAG,CAAC,cAAc,EAAE,CAAC;gBACrB,iBAAiB,CAAC,KAAK,EAAE,CAAC;aAC7B;YAED,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,SAAqB,EAAE,UAAiC,EAAE,EAAE;gBAC5I,wBAAwB;gBACxB,IAAI,KAAK,CAAC,sBAAsB,CAAC,YAAY,EAAE,EAAE;oBAC7C,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;oBAC7B,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;wBACnB,IAAI,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,GAAG,EAAE,iBAAiB,CAAC,SAAS,CAAC,EAAE;4BACzE,2FAA2F;4BAC3F,IAAI,IAAI,CAAC,mBAAmB,KAAK,GAAG,CAAC,MAAM,EAAE;gCACzC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;gCACxB,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;6BACjC;4BAED,6EAA6E;4BAC7E,IAAI,GAAG,CAAC,OAAO,KAAK,CAAC,EAAE;gCACnB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;6BAChD;4BAED,OAAO;yBACV;wBACD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;4BACtB,IAAI,SAAS,CAAC,WAAW,IAAI,KAAK,CAAC,sBAAsB,CAAC,eAAe,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;gCACrG,IAAI,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,GAAG,EAAE,iBAAiB,CAAC,UAAU,CAAC,EAAE;oCAC1E,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;iCAC/B;6BACJ;4BACD,IAAI,SAAS,CAAC,WAAW,IAAI,KAAK,CAAC,sBAAsB,CAAC,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,EAAE;gCAC3G,IAAI,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,GAAG,EAAE,iBAAiB,CAAC,gBAAgB,CAAC,EAAE;oCAChF,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;iCAC/B;6BACJ;yBACJ;qBACJ;iBACJ;gBAED,gGAAgG;gBAChG,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;oBACvC,IAAI,IAAI,CAAC,mBAAmB,KAAK,GAAG,CAAC,MAAM,EAAE;wBACzC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;wBACxB,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;qBACjC;oBACD,OAAO;iBACV;gBAED,mDAAmD;gBACnD,IAAI,GAAG,CAAC,OAAO,KAAK,CAAC,EAAE;oBACnB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;iBAChD;gBACD,IAAI,CAAC,KAAK,CAAC,sBAAsB,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;oBACtD,OAAO;iBACV;gBAED,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;oBAC3B,KAAK,CAAC,kBAAkB,GAAG,CAAC,IAAkB,EAAW,EAAE;wBACvD,OAAO,CACH,IAAI,CAAC,UAAU;4BACf,IAAI,CAAC,SAAS;4BACd,IAAI,CAAC,OAAO,EAAE;4BACd,IAAI,CAAC,SAAS,EAAE;4BAChB,CAAC,CAAC,KAAK,CAAC,sBAAsB,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CACrG,CAAC;oBACN,CAAC,CAAC;iBACL;gBAED,SAAS;gBACT,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,qBAAqB,IAAI,qBAAqB,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE;oBAC1I,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBAC5C;gBACD,IAAI,CAAC,UAAU,EAAE;oBACb,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC;iBACxC;gBAED,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;gBAEnD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAEnD,IAAI,IAAI,CAAC,mBAAmB,KAAK,GAAG,CAAC,MAAM,EAAE;oBACzC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;oBACxB,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;iBACjC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,CAAC,GAAmB,EAAE,EAAE;YACtC,MAAM,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC;YACxC,IAAI,KAAK,CAAC,uBAAuB,CAAC,YAAY,EAAE,EAAE;gBAC9C,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBAC1C,KAAK,CAAC,uBAAuB,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;gBACxD,IAAI,EAAE,CAAC,wBAAwB,EAAE;oBAC7B,OAAO;iBACV;aACJ;YAED,IAAI,KAAK,CAAC,oBAAoB,CAAC,YAAY,EAAE,EAAE;gBAC3C,MAAM,EAAE,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBACvC,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;aACxD;YAED,IAAI,KAAK,CAAC,aAAa,EAAE;gBACrB,KAAK,CAAC,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,uBAAuB,EAAE,WAAW,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;aACrH;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAmB,EAAE,EAAE;YACpC,MAAM,IAAI,GAAG,kBAAkB,CAAC,KAAK,CAAC;YACtC,IAAI,KAAK,CAAC,uBAAuB,CAAC,YAAY,EAAE,EAAE;gBAC9C,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBAC1C,KAAK,CAAC,uBAAuB,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;gBACxD,IAAI,EAAE,CAAC,wBAAwB,EAAE;oBAC7B,OAAO;iBACV;aACJ;YAED,IAAI,KAAK,CAAC,oBAAoB,CAAC,YAAY,EAAE,EAAE;gBAC3C,MAAM,EAAE,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBACvC,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;aACxD;YAED,IAAI,KAAK,CAAC,aAAa,EAAE;gBACrB,KAAK,CAAC,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,qBAAqB,EAAE,WAAW,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;aACnH;QACL,CAAC,CAAC;QAEF,kEAAkE;QAClE,IAAI,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;YACvE,IAAI,YAAY,CAAC,UAAU,KAAK,UAAU,CAAC,KAAK,EAAE;gBAC9C,YAAY,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;oBACpD,IACI,SAAS,CAAC,UAAU,KAAK,YAAY,CAAC,SAAS;wBAC/C,SAAS,CAAC,UAAU,KAAK,YAAY,CAAC,WAAW;wBACjD,SAAS,CAAC,UAAU,KAAK,YAAY,CAAC,UAAU;wBAChD,SAAS,CAAC,UAAU,KAAK,YAAY,CAAC,WAAW;wBACjD,SAAS,CAAC,UAAU,KAAK,YAAY,CAAC,cAAc,EACtD;wBACE,IAAI,UAAU,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;4BACjE,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;yBAClC;6BAAM,IAAI,QAAQ,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;4BACtE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;yBAChC;qBACJ;yBAAM,IAAI,UAAU,EAAE;wBACnB,IAAI,SAAS,CAAC,UAAU,KAAK,YAAY,CAAC,IAAI,EAAE;4BAC5C,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;yBAClC;6BAAM,IACH,SAAS,CAAC,UAAU,KAAK,YAAY,CAAC,WAAW;4BACjD,SAAS,CAAC,UAAU,KAAK,YAAY,CAAC,WAAW;4BACjD,SAAS,CAAC,UAAU,KAAK,YAAY,CAAC,WAAW,EACnD;4BACE,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;yBAClC;qBACJ;gBACL,CAAC,CAAC,CAAC;aACN;iBAAM,IAAI,YAAY,CAAC,UAAU,KAAK,UAAU,CAAC,KAAK,EAAE;gBACrD,YAAY,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;oBACpD,IAAI,SAAS,CAAC,UAAU,KAAK,YAAY,CAAC,SAAS,EAAE;wBACjD,IAAI,UAAU,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;4BACjE,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;4BAC/B,IAAI,IAAI,CAAC,qBAAqB,GAAG,CAAC,EAAE;gCAChC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;6BACpC;yBACJ;6BAAM,IAAI,QAAQ,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;4BACtE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;4BAC7B,IAAI,IAAI,CAAC,qBAAqB,KAAK,CAAC,EAAE;gCAClC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;6BACrC;yBACJ;qBACJ;oBAED,IAAI,UAAU,IAAI,SAAS,CAAC,UAAU,KAAK,YAAY,CAAC,IAAI,EAAE;wBAC1D,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;qBAClC;gBACL,CAAC,CAAC,CAAC;aACN;iBAAM,IAAI,YAAY,CAAC,UAAU,KAAK,UAAU,CAAC,QAAQ,EAAE;gBACxD,YAAY,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;oBACpD,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,EAAE;wBAC9B,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;qBAC9B;yBAAM,IAAI,SAAS,CAAC,IAAI,KAAK,OAAO,EAAE;wBACnC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;qBAC5B;gBACL,CAAC,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,oBAAqB,CAAC,OAAO,EAAE,CAAC;YACrC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YAEjC,SAAS;YACT,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;gBAC5D,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;aACpE;YAED,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAC9B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAClC;IACL,CAAC;IAED;;;;;;OAMG;IACI,kBAAkB,CAAC,IAA4B,EAAE,YAAoB,CAAC,EAAE,UAAkC,EAAE,GAAmB;QAClI,IAAI,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,8BAA8B,CAAC,EAAE;YAC/H,OAAO;SACV;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAE7D,IAAI,aAA8C,CAAC;QACnD,IAAI,gBAAgB,EAAE;YAClB,aAAa,GAAG,gBAAgB,CAAC,2BAA2B,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YACnG,IAAI,aAAa,EAAE;gBACf,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,0BAA0B,EAAE,WAAW,CAAC,SAAS,CAAC,gBAAgB,EAAE,GAAG,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;aACnI;SACJ;QAED,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;YAC3C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAE7B,aAAa,GAAG,IAAI,CAAC,2BAA2B,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;YACxF,IAAI,aAAa,EAAE;gBACf,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,2BAA2B,EAAE,WAAW,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;aACpI;SACJ;aAAM;YACH,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YAC3C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;IACL,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,IAAkB;QACrC,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;YAChC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;QACD,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;YAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC/B;QACD,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B;QACD,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC9C,IAAI,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;gBAC9C,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;aAC9C;SACJ;IACL,CAAC;;AA7lCD,yEAAyE;AAC3D,kCAAqB,GAAG,EAAE,AAAL,CAAM,CAAC,YAAY;AACtD,yFAAyF;AAC3E,2BAAc,GAAG,GAAG,AAAN,CAAO,CAAC,kBAAkB;AACtD,4FAA4F;AAC9E,6BAAgB,GAAG,GAAG,AAAN,CAAO,CAAC,kBAAkB;AACxD;;;;GAIG;AACW,qCAAwB,GAAG,KAAK,AAAR,CAAS", "sourcesContent": ["import type { EventState, Observable, Observer } from \"../Misc/observable\";\r\nimport { PointerInfoPre, PointerInfo, PointerEventTypes } from \"../Events/pointerEvents\";\r\nimport type { Nullable } from \"../types\";\r\nimport { AbstractActionManager } from \"../Actions/abstractActionManager\";\r\nimport { PickingInfo } from \"../Collisions/pickingInfo\";\r\nimport { Vector2, Matrix } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { ActionEvent } from \"../Actions/actionEvent\";\r\nimport { KeyboardEventTypes, KeyboardInfoPre, KeyboardInfo } from \"../Events/keyboardEvents\";\r\nimport { DeviceType, PointerInput } from \"../DeviceInput/InputDevices/deviceEnums\";\r\nimport type { IKeyboardEvent, IMouseEvent, IPointerEvent } from \"../Events/deviceInputEvents\";\r\nimport { DeviceSourceManager } from \"../DeviceInput/InputDevices/deviceSourceManager\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\n\r\nimport type { Scene } from \"../scene\";\r\n\r\n/** @internal */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nclass _ClickInfo {\r\n    private _singleClick = false;\r\n    private _doubleClick = false;\r\n    private _hasSwiped = false;\r\n    private _ignore = false;\r\n\r\n    public get singleClick(): boolean {\r\n        return this._singleClick;\r\n    }\r\n    public get doubleClick(): boolean {\r\n        return this._doubleClick;\r\n    }\r\n    public get hasSwiped(): boolean {\r\n        return this._hasSwiped;\r\n    }\r\n    public get ignore(): boolean {\r\n        return this._ignore;\r\n    }\r\n\r\n    public set singleClick(b: boolean) {\r\n        this._singleClick = b;\r\n    }\r\n    public set doubleClick(b: boolean) {\r\n        this._doubleClick = b;\r\n    }\r\n    public set hasSwiped(b: boolean) {\r\n        this._hasSwiped = b;\r\n    }\r\n    public set ignore(b: boolean) {\r\n        this._ignore = b;\r\n    }\r\n}\r\n\r\n/** @internal */\r\ninterface _IClickEvent {\r\n    clickInfo: _ClickInfo;\r\n    evt: IPointerEvent;\r\n    timeoutId: number;\r\n}\r\n\r\n/**\r\n * Class used to manage all inputs for the scene.\r\n */\r\nexport class InputManager {\r\n    /** The distance in pixel that you have to move to prevent some events */\r\n    public static DragMovementThreshold = 10; // in pixels\r\n    /** Time in milliseconds to wait to raise long press events if button is still pressed */\r\n    public static LongPressDelay = 500; // in milliseconds\r\n    /** Time in milliseconds with two consecutive clicks will be considered as a double click */\r\n    public static DoubleClickDelay = 300; // in milliseconds\r\n    /**\r\n     * This flag will modify the behavior so that, when true, a click will happen if and only if\r\n     * another click DOES NOT happen within the DoubleClickDelay time frame.  If another click does\r\n     * happen within that time frame, the first click will not fire an event and and a double click will occur.\r\n     */\r\n    public static ExclusiveDoubleClickMode = false;\r\n\r\n    /** This is a defensive check to not allow control attachment prior to an already active one. If already attached, previous control is unattached before attaching the new one. */\r\n    private _alreadyAttached = false;\r\n    private _alreadyAttachedTo: Nullable<HTMLElement>;\r\n\r\n    // Pointers\r\n    private _onPointerMove: (evt: IMouseEvent) => void;\r\n    private _onPointerDown: (evt: IPointerEvent) => void;\r\n    private _onPointerUp: (evt: IPointerEvent) => void;\r\n\r\n    private _initClickEvent: (\r\n        obs1: Observable<PointerInfoPre>,\r\n        obs2: Observable<PointerInfo>,\r\n        evt: IPointerEvent,\r\n        cb: (clickInfo: _ClickInfo, pickResult: Nullable<PickingInfo>) => void\r\n    ) => void;\r\n    private _initActionManager: (act: Nullable<AbstractActionManager>, clickInfo: _ClickInfo) => Nullable<AbstractActionManager>;\r\n    private _delayedSimpleClick: (btn: number, clickInfo: _ClickInfo, cb: (clickInfo: _ClickInfo, pickResult: Nullable<PickingInfo>) => void) => void;\r\n    private _meshPickProceed = false;\r\n\r\n    private _previousButtonPressed: number;\r\n    private _currentPickResult: Nullable<PickingInfo> = null;\r\n    private _previousPickResult: Nullable<PickingInfo> = null;\r\n    private _totalPointersPressed = 0;\r\n    private _doubleClickOccured = false;\r\n    private _isSwiping: boolean = false;\r\n    private _swipeButtonPressed: number = -1;\r\n    private _skipPointerTap: boolean = false;\r\n    private _isMultiTouchGesture: boolean = false;\r\n\r\n    private _pointerOverMesh: Nullable<AbstractMesh>;\r\n\r\n    private _pickedDownMesh: Nullable<AbstractMesh>;\r\n    private _pickedUpMesh: Nullable<AbstractMesh>;\r\n\r\n    private _pointerX: number = 0;\r\n    private _pointerY: number = 0;\r\n    private _unTranslatedPointerX: number;\r\n    private _unTranslatedPointerY: number;\r\n    private _startingPointerPosition = new Vector2(0, 0);\r\n    private _previousStartingPointerPosition = new Vector2(0, 0);\r\n    private _startingPointerTime = 0;\r\n    private _previousStartingPointerTime = 0;\r\n    private _pointerCaptures: { [pointerId: number]: boolean } = {};\r\n    private _meshUnderPointerId: { [pointerId: number]: Nullable<AbstractMesh> } = {};\r\n    private _movePointerInfo: Nullable<PointerInfo> = null;\r\n    private _cameraObserverCount = 0;\r\n    private _delayedClicks: Array<Nullable<_IClickEvent>> = [null, null, null, null, null];\r\n\r\n    // Keyboard\r\n    private _onKeyDown: (evt: IKeyboardEvent) => void;\r\n    private _onKeyUp: (evt: IKeyboardEvent) => void;\r\n\r\n    private _scene: Scene;\r\n    private _deviceSourceManager: Nullable<DeviceSourceManager> = null;\r\n\r\n    /**\r\n     * Creates a new InputManager\r\n     * @param scene - defines the hosting scene\r\n     */\r\n    constructor(scene?: Scene) {\r\n        this._scene = scene || <Scene>EngineStore.LastCreatedScene;\r\n        if (!this._scene) {\r\n            return;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the mesh that is currently under the pointer\r\n     * @returns Mesh that the pointer is pointer is hovering over\r\n     */\r\n    public get meshUnderPointer(): Nullable<AbstractMesh> {\r\n        if (this._movePointerInfo) {\r\n            // Because _pointerOverMesh is populated as part of _pickMove, we need to force a pick to update it.\r\n            // Calling _pickMove calls _setCursorAndPointerOverMesh which calls setPointerOverMesh\r\n            this._movePointerInfo._generatePickInfo();\r\n            // Once we have what we need, we can clear _movePointerInfo because we don't need it anymore\r\n            this._movePointerInfo = null;\r\n        }\r\n        return this._pointerOverMesh;\r\n    }\r\n\r\n    /**\r\n     * When using more than one pointer (for example in XR) you can get the mesh under the specific pointer\r\n     * @param pointerId - the pointer id to use\r\n     * @returns The mesh under this pointer id or null if not found\r\n     */\r\n    public getMeshUnderPointerByPointerId(pointerId: number): Nullable<AbstractMesh> {\r\n        return this._meshUnderPointerId[pointerId] || null;\r\n    }\r\n\r\n    /**\r\n     * Gets the pointer coordinates in 2D without any translation (ie. straight out of the pointer event)\r\n     * @returns Vector with X/Y values directly from pointer event\r\n     */\r\n    public get unTranslatedPointer(): Vector2 {\r\n        return new Vector2(this._unTranslatedPointerX, this._unTranslatedPointerY);\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the current on-screen X position of the pointer\r\n     * @returns Translated X with respect to screen\r\n     */\r\n    public get pointerX(): number {\r\n        return this._pointerX;\r\n    }\r\n\r\n    public set pointerX(value: number) {\r\n        this._pointerX = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the current on-screen Y position of the pointer\r\n     * @returns Translated Y with respect to screen\r\n     */\r\n    public get pointerY(): number {\r\n        return this._pointerY;\r\n    }\r\n\r\n    public set pointerY(value: number) {\r\n        this._pointerY = value;\r\n    }\r\n\r\n    private _updatePointerPosition(evt: IPointerEvent): void {\r\n        const canvasRect = this._scene.getEngine().getInputElementClientRect();\r\n\r\n        if (!canvasRect) {\r\n            return;\r\n        }\r\n\r\n        this._pointerX = evt.clientX - canvasRect.left;\r\n        this._pointerY = evt.clientY - canvasRect.top;\r\n\r\n        this._unTranslatedPointerX = this._pointerX;\r\n        this._unTranslatedPointerY = this._pointerY;\r\n    }\r\n\r\n    private _processPointerMove(pickResult: Nullable<PickingInfo>, evt: IPointerEvent) {\r\n        const scene = this._scene;\r\n        const engine = scene.getEngine();\r\n        const canvas = engine.getInputElement();\r\n\r\n        if (canvas) {\r\n            canvas.tabIndex = engine.canvasTabIndex;\r\n\r\n            // Restore pointer\r\n            if (!scene.doNotHandleCursors) {\r\n                canvas.style.cursor = scene.defaultCursor;\r\n            }\r\n        }\r\n\r\n        this._setCursorAndPointerOverMesh(pickResult, evt, scene);\r\n\r\n        for (const step of scene._pointerMoveStage) {\r\n            // If _pointerMoveState is defined, we have an active spriteManager and can't use Lazy Picking\r\n            // Therefore, we need to force a pick to update the pickResult\r\n            pickResult = pickResult || this._pickMove(evt);\r\n            const isMeshPicked = pickResult?.pickedMesh ? true : false;\r\n            pickResult = step.action(this._unTranslatedPointerX, this._unTranslatedPointerY, pickResult, isMeshPicked, canvas);\r\n        }\r\n\r\n        const type = evt.inputIndex >= PointerInput.MouseWheelX && evt.inputIndex <= PointerInput.MouseWheelZ ? PointerEventTypes.POINTERWHEEL : PointerEventTypes.POINTERMOVE;\r\n\r\n        if (scene.onPointerMove) {\r\n            // Because of lazy picking, we need to force a pick to update the pickResult\r\n            pickResult = pickResult || this._pickMove(evt);\r\n            scene.onPointerMove(evt, pickResult, type);\r\n        }\r\n\r\n        let pointerInfo: PointerInfo;\r\n        if (pickResult) {\r\n            pointerInfo = new PointerInfo(type, evt, pickResult);\r\n            this._setRayOnPointerInfo(pickResult, evt);\r\n        } else {\r\n            pointerInfo = new PointerInfo(type, evt, null, this);\r\n            this._movePointerInfo = pointerInfo;\r\n        }\r\n\r\n        if (scene.onPointerObservable.hasObservers()) {\r\n            scene.onPointerObservable.notifyObservers(pointerInfo, type);\r\n        }\r\n    }\r\n\r\n    // Pointers handling\r\n    /** @internal */\r\n    public _setRayOnPointerInfo(pickInfo: Nullable<PickingInfo>, event: IMouseEvent) {\r\n        const scene = this._scene;\r\n        if (pickInfo && scene._pickingAvailable) {\r\n            if (!pickInfo.ray) {\r\n                pickInfo.ray = scene.createPickingRay(event.offsetX, event.offsetY, Matrix.Identity(), scene.activeCamera);\r\n            }\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _addCameraPointerObserver(observer: (p: PointerInfo, s: EventState) => void, mask?: number): Nullable<Observer<PointerInfo>> {\r\n        this._cameraObserverCount++;\r\n        return this._scene.onPointerObservable.add(observer, mask);\r\n    }\r\n\r\n    /** @internal */\r\n    public _removeCameraPointerObserver(observer: Observer<PointerInfo>): boolean {\r\n        this._cameraObserverCount--;\r\n        return this._scene.onPointerObservable.remove(observer);\r\n    }\r\n\r\n    private _checkForPicking(): boolean {\r\n        return !!(this._scene.onPointerObservable.observers.length > this._cameraObserverCount || this._scene.onPointerPick);\r\n    }\r\n\r\n    private _checkPrePointerObservable(pickResult: Nullable<PickingInfo>, evt: IPointerEvent, type: number) {\r\n        const scene = this._scene;\r\n        const pi = new PointerInfoPre(type, evt, this._unTranslatedPointerX, this._unTranslatedPointerY);\r\n        if (pickResult) {\r\n            pi.originalPickingInfo = pickResult;\r\n            pi.ray = pickResult.ray;\r\n            if (evt.pointerType === \"xr-near\" && pickResult.originMesh) {\r\n                pi.nearInteractionPickingInfo = pickResult;\r\n            }\r\n        }\r\n\r\n        scene.onPrePointerObservable.notifyObservers(pi, type);\r\n        if (pi.skipOnPointerObservable) {\r\n            return true;\r\n        } else {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _pickMove(evt: IPointerEvent): PickingInfo {\r\n        const scene = this._scene;\r\n        const pickResult = scene.pick(\r\n            this._unTranslatedPointerX,\r\n            this._unTranslatedPointerY,\r\n            scene.pointerMovePredicate,\r\n            scene.pointerMoveFastCheck,\r\n            scene.cameraToUseForPointers,\r\n            scene.pointerMoveTrianglePredicate\r\n        );\r\n\r\n        this._setCursorAndPointerOverMesh(pickResult, evt, scene);\r\n\r\n        return pickResult;\r\n    }\r\n\r\n    private _setCursorAndPointerOverMesh(pickResult: Nullable<PickingInfo>, evt: IPointerEvent, scene: Scene) {\r\n        const engine = scene.getEngine();\r\n        const canvas = engine.getInputElement();\r\n\r\n        if (pickResult?.pickedMesh) {\r\n            this.setPointerOverMesh(pickResult.pickedMesh, evt.pointerId, pickResult, evt);\r\n\r\n            if (!scene.doNotHandleCursors && canvas && this._pointerOverMesh) {\r\n                const actionManager = this._pointerOverMesh._getActionManagerForTrigger();\r\n                if (actionManager && actionManager.hasPointerTriggers) {\r\n                    canvas.style.cursor = actionManager.hoverCursor || scene.hoverCursor;\r\n                }\r\n            }\r\n        } else {\r\n            this.setPointerOverMesh(null, evt.pointerId, pickResult, evt);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Use this method to simulate a pointer move on a mesh\r\n     * The pickResult parameter can be obtained from a scene.pick or scene.pickWithRay\r\n     * @param pickResult - pickingInfo of the object wished to simulate pointer event on\r\n     * @param pointerEventInit - pointer event state to be used when simulating the pointer event (eg. pointer id for multitouch)\r\n     */\r\n    public simulatePointerMove(pickResult: PickingInfo, pointerEventInit?: PointerEventInit): void {\r\n        const evt = new PointerEvent(\"pointermove\", pointerEventInit);\r\n        evt.inputIndex = PointerInput.Move;\r\n\r\n        if (this._checkPrePointerObservable(pickResult, evt, PointerEventTypes.POINTERMOVE)) {\r\n            return;\r\n        }\r\n        this._processPointerMove(pickResult, evt);\r\n    }\r\n\r\n    /**\r\n     * Use this method to simulate a pointer down on a mesh\r\n     * The pickResult parameter can be obtained from a scene.pick or scene.pickWithRay\r\n     * @param pickResult - pickingInfo of the object wished to simulate pointer event on\r\n     * @param pointerEventInit - pointer event state to be used when simulating the pointer event (eg. pointer id for multitouch)\r\n     */\r\n    public simulatePointerDown(pickResult: PickingInfo, pointerEventInit?: PointerEventInit): void {\r\n        const evt = new PointerEvent(\"pointerdown\", pointerEventInit);\r\n        evt.inputIndex = evt.button + 2;\r\n\r\n        if (this._checkPrePointerObservable(pickResult, evt, PointerEventTypes.POINTERDOWN)) {\r\n            return;\r\n        }\r\n\r\n        this._processPointerDown(pickResult, evt);\r\n    }\r\n\r\n    private _processPointerDown(pickResult: Nullable<PickingInfo>, evt: IPointerEvent): void {\r\n        const scene = this._scene;\r\n        if (pickResult?.pickedMesh) {\r\n            this._pickedDownMesh = pickResult.pickedMesh;\r\n            const actionManager = pickResult.pickedMesh._getActionManagerForTrigger();\r\n            if (actionManager) {\r\n                if (actionManager.hasPickTriggers) {\r\n                    actionManager.processTrigger(Constants.ACTION_OnPickDownTrigger, ActionEvent.CreateNew(pickResult.pickedMesh, evt, pickResult));\r\n                    switch (evt.button) {\r\n                        case 0:\r\n                            actionManager.processTrigger(Constants.ACTION_OnLeftPickTrigger, ActionEvent.CreateNew(pickResult.pickedMesh, evt, pickResult));\r\n                            break;\r\n                        case 1:\r\n                            actionManager.processTrigger(Constants.ACTION_OnCenterPickTrigger, ActionEvent.CreateNew(pickResult.pickedMesh, evt, pickResult));\r\n                            break;\r\n                        case 2:\r\n                            actionManager.processTrigger(Constants.ACTION_OnRightPickTrigger, ActionEvent.CreateNew(pickResult.pickedMesh, evt, pickResult));\r\n                            break;\r\n                    }\r\n                }\r\n\r\n                if (actionManager.hasSpecificTrigger(Constants.ACTION_OnLongPressTrigger)) {\r\n                    window.setTimeout(() => {\r\n                        const pickResult = scene.pick(\r\n                            this._unTranslatedPointerX,\r\n                            this._unTranslatedPointerY,\r\n                            (mesh: AbstractMesh): boolean =>\r\n                                <boolean>(\r\n                                    (mesh.isPickable &&\r\n                                        mesh.isVisible &&\r\n                                        mesh.isReady() &&\r\n                                        mesh.actionManager &&\r\n                                        mesh.actionManager.hasSpecificTrigger(Constants.ACTION_OnLongPressTrigger) &&\r\n                                        mesh === this._pickedDownMesh)\r\n                                ),\r\n                            false,\r\n                            scene.cameraToUseForPointers\r\n                        );\r\n\r\n                        if (pickResult?.pickedMesh && actionManager) {\r\n                            if (this._totalPointersPressed !== 0 && Date.now() - this._startingPointerTime > InputManager.LongPressDelay && !this._isPointerSwiping()) {\r\n                                this._startingPointerTime = 0;\r\n                                actionManager.processTrigger(Constants.ACTION_OnLongPressTrigger, ActionEvent.CreateNew(pickResult.pickedMesh, evt));\r\n                            }\r\n                        }\r\n                    }, InputManager.LongPressDelay);\r\n                }\r\n            }\r\n        } else {\r\n            for (const step of scene._pointerDownStage) {\r\n                pickResult = step.action(this._unTranslatedPointerX, this._unTranslatedPointerY, pickResult, evt, false);\r\n            }\r\n        }\r\n\r\n        let pointerInfo: PointerInfo;\r\n        const type = PointerEventTypes.POINTERDOWN;\r\n\r\n        if (pickResult) {\r\n            if (scene.onPointerDown) {\r\n                scene.onPointerDown(evt, pickResult, type);\r\n            }\r\n\r\n            pointerInfo = new PointerInfo(type, evt, pickResult);\r\n            this._setRayOnPointerInfo(pickResult, evt);\r\n        } else {\r\n            pointerInfo = new PointerInfo(type, evt, null, this);\r\n        }\r\n\r\n        if (scene.onPointerObservable.hasObservers()) {\r\n            scene.onPointerObservable.notifyObservers(pointerInfo, type);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * @internals Boolean if delta for pointer exceeds drag movement threshold\r\n     */\r\n    public _isPointerSwiping(): boolean {\r\n        return this._isSwiping;\r\n    }\r\n\r\n    /**\r\n     * Use this method to simulate a pointer up on a mesh\r\n     * The pickResult parameter can be obtained from a scene.pick or scene.pickWithRay\r\n     * @param pickResult - pickingInfo of the object wished to simulate pointer event on\r\n     * @param pointerEventInit - pointer event state to be used when simulating the pointer event (eg. pointer id for multitouch)\r\n     * @param doubleTap - indicates that the pointer up event should be considered as part of a double click (false by default)\r\n     */\r\n    public simulatePointerUp(pickResult: PickingInfo, pointerEventInit?: PointerEventInit, doubleTap?: boolean): void {\r\n        const evt = new PointerEvent(\"pointerup\", pointerEventInit);\r\n        evt.inputIndex = PointerInput.Move;\r\n        const clickInfo = new _ClickInfo();\r\n\r\n        if (doubleTap) {\r\n            clickInfo.doubleClick = true;\r\n        } else {\r\n            clickInfo.singleClick = true;\r\n        }\r\n\r\n        if (this._checkPrePointerObservable(pickResult, evt, PointerEventTypes.POINTERUP)) {\r\n            return;\r\n        }\r\n\r\n        this._processPointerUp(pickResult, evt, clickInfo);\r\n    }\r\n\r\n    private _processPointerUp(pickResult: Nullable<PickingInfo>, evt: IPointerEvent, clickInfo: _ClickInfo): void {\r\n        const scene = this._scene;\r\n        if (pickResult?.pickedMesh) {\r\n            this._pickedUpMesh = pickResult.pickedMesh;\r\n            if (this._pickedDownMesh === this._pickedUpMesh) {\r\n                if (scene.onPointerPick) {\r\n                    scene.onPointerPick(evt, pickResult);\r\n                }\r\n                if (clickInfo.singleClick && !clickInfo.ignore && scene.onPointerObservable.observers.length > this._cameraObserverCount) {\r\n                    const type = PointerEventTypes.POINTERPICK;\r\n                    const pi = new PointerInfo(type, evt, pickResult);\r\n                    this._setRayOnPointerInfo(pickResult, evt);\r\n                    scene.onPointerObservable.notifyObservers(pi, type);\r\n                }\r\n            }\r\n            const actionManager = pickResult.pickedMesh._getActionManagerForTrigger();\r\n            if (actionManager && !clickInfo.ignore) {\r\n                actionManager.processTrigger(Constants.ACTION_OnPickUpTrigger, ActionEvent.CreateNew(pickResult.pickedMesh, evt, pickResult));\r\n\r\n                if (!clickInfo.hasSwiped && clickInfo.singleClick) {\r\n                    actionManager.processTrigger(Constants.ACTION_OnPickTrigger, ActionEvent.CreateNew(pickResult.pickedMesh, evt, pickResult));\r\n                }\r\n\r\n                const doubleClickActionManager = pickResult.pickedMesh._getActionManagerForTrigger(Constants.ACTION_OnDoublePickTrigger);\r\n                if (clickInfo.doubleClick && doubleClickActionManager) {\r\n                    doubleClickActionManager.processTrigger(Constants.ACTION_OnDoublePickTrigger, ActionEvent.CreateNew(pickResult.pickedMesh, evt, pickResult));\r\n                }\r\n            }\r\n        } else {\r\n            if (!clickInfo.ignore) {\r\n                for (const step of scene._pointerUpStage) {\r\n                    pickResult = step.action(this._unTranslatedPointerX, this._unTranslatedPointerY, pickResult, evt, clickInfo.doubleClick);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._pickedDownMesh && this._pickedDownMesh !== this._pickedUpMesh) {\r\n            const pickedDownActionManager = this._pickedDownMesh._getActionManagerForTrigger(Constants.ACTION_OnPickOutTrigger);\r\n            if (pickedDownActionManager) {\r\n                pickedDownActionManager.processTrigger(Constants.ACTION_OnPickOutTrigger, ActionEvent.CreateNew(this._pickedDownMesh, evt));\r\n            }\r\n        }\r\n\r\n        if (!clickInfo.ignore) {\r\n            const pi = new PointerInfo(PointerEventTypes.POINTERUP, evt, pickResult);\r\n            // Set ray on picking info.  Note that this info will also be reused for the tap notification.\r\n            this._setRayOnPointerInfo(pickResult, evt);\r\n            scene.onPointerObservable.notifyObservers(pi, PointerEventTypes.POINTERUP);\r\n\r\n            if (scene.onPointerUp) {\r\n                scene.onPointerUp(evt, pickResult, PointerEventTypes.POINTERUP);\r\n            }\r\n\r\n            if (!clickInfo.hasSwiped && !this._skipPointerTap && !this._isMultiTouchGesture) {\r\n                let type = 0;\r\n                if (clickInfo.singleClick) {\r\n                    type = PointerEventTypes.POINTERTAP;\r\n                } else if (clickInfo.doubleClick) {\r\n                    type = PointerEventTypes.POINTERDOUBLETAP;\r\n                }\r\n\r\n                if (type) {\r\n                    const pi = new PointerInfo(type, evt, pickResult);\r\n                    if (scene.onPointerObservable.hasObservers() && scene.onPointerObservable.hasSpecificMask(type)) {\r\n                        scene.onPointerObservable.notifyObservers(pi, type);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the current pointer event is captured (meaning that the scene has already handled the pointer down)\r\n     * @param pointerId - defines the pointer id to use in a multi-touch scenario (0 by default)\r\n     * @returns true if the pointer was captured\r\n     */\r\n    public isPointerCaptured(pointerId = 0): boolean {\r\n        return this._pointerCaptures[pointerId];\r\n    }\r\n\r\n    /**\r\n     * Attach events to the canvas (To handle actionManagers triggers and raise onPointerMove, onPointerDown and onPointerUp\r\n     * @param attachUp - defines if you want to attach events to pointerup\r\n     * @param attachDown - defines if you want to attach events to pointerdown\r\n     * @param attachMove - defines if you want to attach events to pointermove\r\n     * @param elementToAttachTo - defines the target DOM element to attach to (will use the canvas by default)\r\n     */\r\n    public attachControl(attachUp = true, attachDown = true, attachMove = true, elementToAttachTo: Nullable<HTMLElement> = null): void {\r\n        const scene = this._scene;\r\n        const engine = scene.getEngine();\r\n\r\n        if (!elementToAttachTo) {\r\n            elementToAttachTo = engine.getInputElement();\r\n        }\r\n\r\n        if (this._alreadyAttached) {\r\n            this.detachControl();\r\n        }\r\n\r\n        if (elementToAttachTo) {\r\n            this._alreadyAttachedTo = elementToAttachTo;\r\n        }\r\n        this._deviceSourceManager = new DeviceSourceManager(engine);\r\n\r\n        // Because this is only called from _initClickEvent, which is called in _onPointerUp, we'll use the pointerUpPredicate for the pick call\r\n        this._initActionManager = (act: Nullable<AbstractActionManager>): Nullable<AbstractActionManager> => {\r\n            if (!this._meshPickProceed) {\r\n                const pickResult =\r\n                    scene.skipPointerUpPicking || (scene._registeredActions === 0 && !this._checkForPicking() && !scene.onPointerUp)\r\n                        ? null\r\n                        : scene.pick(\r\n                              this._unTranslatedPointerX,\r\n                              this._unTranslatedPointerY,\r\n                              scene.pointerUpPredicate,\r\n                              scene.pointerUpFastCheck,\r\n                              scene.cameraToUseForPointers,\r\n                              scene.pointerUpTrianglePredicate\r\n                          );\r\n                this._currentPickResult = pickResult;\r\n                if (pickResult) {\r\n                    act = pickResult.hit && pickResult.pickedMesh ? pickResult.pickedMesh._getActionManagerForTrigger() : null;\r\n                }\r\n                this._meshPickProceed = true;\r\n            }\r\n            return act;\r\n        };\r\n\r\n        this._delayedSimpleClick = (btn: number, clickInfo: _ClickInfo, cb: (clickInfo: _ClickInfo, pickResult: Nullable<PickingInfo>) => void) => {\r\n            // double click delay is over and that no double click has been raised since, or the 2 consecutive keys pressed are different\r\n            if ((Date.now() - this._previousStartingPointerTime > InputManager.DoubleClickDelay && !this._doubleClickOccured) || btn !== this._previousButtonPressed) {\r\n                this._doubleClickOccured = false;\r\n                clickInfo.singleClick = true;\r\n                clickInfo.ignore = false;\r\n\r\n                // If we have a delayed click, we need to resolve the TAP event\r\n                if (this._delayedClicks[btn]) {\r\n                    const evt = this._delayedClicks[btn]!.evt;\r\n                    const type = PointerEventTypes.POINTERTAP;\r\n                    const pi = new PointerInfo(type, evt, this._currentPickResult);\r\n                    if (scene.onPointerObservable.hasObservers() && scene.onPointerObservable.hasSpecificMask(type)) {\r\n                        scene.onPointerObservable.notifyObservers(pi, type);\r\n                    }\r\n\r\n                    // Clear the delayed click\r\n                    this._delayedClicks[btn] = null;\r\n                }\r\n            }\r\n        };\r\n\r\n        this._initClickEvent = (\r\n            obs1: Observable<PointerInfoPre>,\r\n            obs2: Observable<PointerInfo>,\r\n            evt: IPointerEvent,\r\n            cb: (clickInfo: _ClickInfo, pickResult: Nullable<PickingInfo>) => void\r\n        ): void => {\r\n            const clickInfo = new _ClickInfo();\r\n            this._currentPickResult = null;\r\n            let act: Nullable<AbstractActionManager> = null;\r\n\r\n            let checkPicking =\r\n                obs1.hasSpecificMask(PointerEventTypes.POINTERPICK) ||\r\n                obs2.hasSpecificMask(PointerEventTypes.POINTERPICK) ||\r\n                obs1.hasSpecificMask(PointerEventTypes.POINTERTAP) ||\r\n                obs2.hasSpecificMask(PointerEventTypes.POINTERTAP) ||\r\n                obs1.hasSpecificMask(PointerEventTypes.POINTERDOUBLETAP) ||\r\n                obs2.hasSpecificMask(PointerEventTypes.POINTERDOUBLETAP);\r\n            if (!checkPicking && AbstractActionManager) {\r\n                act = this._initActionManager(act, clickInfo);\r\n                if (act) {\r\n                    checkPicking = act.hasPickTriggers;\r\n                }\r\n            }\r\n\r\n            let needToIgnoreNext = false;\r\n\r\n            if (checkPicking) {\r\n                const btn = evt.button;\r\n                clickInfo.hasSwiped = this._isPointerSwiping();\r\n\r\n                if (!clickInfo.hasSwiped) {\r\n                    let checkSingleClickImmediately = !InputManager.ExclusiveDoubleClickMode;\r\n\r\n                    if (!checkSingleClickImmediately) {\r\n                        checkSingleClickImmediately = !obs1.hasSpecificMask(PointerEventTypes.POINTERDOUBLETAP) && !obs2.hasSpecificMask(PointerEventTypes.POINTERDOUBLETAP);\r\n\r\n                        if (checkSingleClickImmediately && !AbstractActionManager.HasSpecificTrigger(Constants.ACTION_OnDoublePickTrigger)) {\r\n                            act = this._initActionManager(act, clickInfo);\r\n                            if (act) {\r\n                                checkSingleClickImmediately = !act.hasSpecificTrigger(Constants.ACTION_OnDoublePickTrigger);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    if (checkSingleClickImmediately) {\r\n                        // single click detected if double click delay is over or two different successive keys pressed without exclusive double click or no double click required\r\n                        if (Date.now() - this._previousStartingPointerTime > InputManager.DoubleClickDelay || btn !== this._previousButtonPressed) {\r\n                            clickInfo.singleClick = true;\r\n                            cb(clickInfo, this._currentPickResult);\r\n                            needToIgnoreNext = true;\r\n                        }\r\n                    }\r\n                    // at least one double click is required to be check and exclusive double click is enabled\r\n                    else {\r\n                        // Queue up a delayed click, just in case this isn't a double click\r\n                        // It should be noted that while this delayed event happens\r\n                        // because of user input, it shouldn't be considered as a direct,\r\n                        // timing-dependent result of that input.  It's meant to just fire the TAP event\r\n                        const delayedClick = {\r\n                            evt: evt,\r\n                            clickInfo: clickInfo,\r\n                            timeoutId: window.setTimeout(this._delayedSimpleClick.bind(this, btn, clickInfo, cb), InputManager.DoubleClickDelay),\r\n                        };\r\n\r\n                        this._delayedClicks[btn] = delayedClick;\r\n                    }\r\n\r\n                    let checkDoubleClick = obs1.hasSpecificMask(PointerEventTypes.POINTERDOUBLETAP) || obs2.hasSpecificMask(PointerEventTypes.POINTERDOUBLETAP);\r\n                    if (!checkDoubleClick && AbstractActionManager.HasSpecificTrigger(Constants.ACTION_OnDoublePickTrigger)) {\r\n                        act = this._initActionManager(act, clickInfo);\r\n                        if (act) {\r\n                            checkDoubleClick = act.hasSpecificTrigger(Constants.ACTION_OnDoublePickTrigger);\r\n                        }\r\n                    }\r\n                    if (checkDoubleClick) {\r\n                        // two successive keys pressed are equal, double click delay is not over and double click has not just occurred\r\n                        if (btn === this._previousButtonPressed && Date.now() - this._previousStartingPointerTime < InputManager.DoubleClickDelay && !this._doubleClickOccured) {\r\n                            // pointer has not moved for 2 clicks, it's a double click\r\n                            if (!clickInfo.hasSwiped && !this._isPointerSwiping()) {\r\n                                this._previousStartingPointerTime = 0;\r\n                                this._doubleClickOccured = true;\r\n                                clickInfo.doubleClick = true;\r\n                                clickInfo.ignore = false;\r\n                                // If we have a pending click, we need to cancel it\r\n                                if (InputManager.ExclusiveDoubleClickMode && this._delayedClicks[btn]) {\r\n                                    clearTimeout(this._delayedClicks[btn]?.timeoutId);\r\n                                    this._delayedClicks[btn] = null;\r\n                                }\r\n\r\n                                cb(clickInfo, this._currentPickResult);\r\n                            }\r\n                            // if the two successive clicks are too far, it's just two simple clicks\r\n                            else {\r\n                                this._doubleClickOccured = false;\r\n                                this._previousStartingPointerTime = this._startingPointerTime;\r\n                                this._previousStartingPointerPosition.x = this._startingPointerPosition.x;\r\n                                this._previousStartingPointerPosition.y = this._startingPointerPosition.y;\r\n                                this._previousButtonPressed = btn;\r\n                                if (InputManager.ExclusiveDoubleClickMode) {\r\n                                    // If we have a delayed click, we need to cancel it\r\n                                    if (this._delayedClicks[btn]) {\r\n                                        clearTimeout(this._delayedClicks[btn]?.timeoutId);\r\n                                        this._delayedClicks[btn] = null;\r\n                                    }\r\n                                    cb(clickInfo, this._previousPickResult);\r\n                                } else {\r\n                                    cb(clickInfo, this._currentPickResult);\r\n                                }\r\n                            }\r\n                            needToIgnoreNext = true;\r\n                        }\r\n                        // just the first click of the double has been raised\r\n                        else {\r\n                            this._doubleClickOccured = false;\r\n                            this._previousStartingPointerTime = this._startingPointerTime;\r\n                            this._previousStartingPointerPosition.x = this._startingPointerPosition.x;\r\n                            this._previousStartingPointerPosition.y = this._startingPointerPosition.y;\r\n                            this._previousButtonPressed = btn!;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Even if ExclusiveDoubleClickMode is true, we need to always handle\r\n            // up events at time of execution, unless we're explicitly ignoring them.\r\n            if (!needToIgnoreNext) {\r\n                cb(clickInfo, this._currentPickResult);\r\n            }\r\n        };\r\n\r\n        this._onPointerMove = (evt: IMouseEvent) => {\r\n            this._updatePointerPosition(evt as IPointerEvent);\r\n\r\n            // Check if pointer leaves DragMovementThreshold range to determine if swipe is occurring\r\n            if (!this._isSwiping && this._swipeButtonPressed !== -1) {\r\n                this._isSwiping =\r\n                    Math.abs(this._startingPointerPosition.x - this._pointerX) > InputManager.DragMovementThreshold ||\r\n                    Math.abs(this._startingPointerPosition.y - this._pointerY) > InputManager.DragMovementThreshold;\r\n            }\r\n\r\n            // Because there's a race condition between pointermove and pointerlockchange events, we need to\r\n            // verify that the pointer is still locked after each pointermove event.\r\n            if (engine.isPointerLock) {\r\n                engine._verifyPointerLock();\r\n            }\r\n\r\n            // PreObservable support\r\n            if (\r\n                this._checkPrePointerObservable(\r\n                    null,\r\n                    evt as IPointerEvent,\r\n                    evt.inputIndex >= PointerInput.MouseWheelX && evt.inputIndex <= PointerInput.MouseWheelZ ? PointerEventTypes.POINTERWHEEL : PointerEventTypes.POINTERMOVE\r\n                )\r\n            ) {\r\n                return;\r\n            }\r\n\r\n            if (!scene.cameraToUseForPointers && !scene.activeCamera) {\r\n                return;\r\n            }\r\n\r\n            if (scene.skipPointerMovePicking) {\r\n                this._processPointerMove(new PickingInfo(), evt as IPointerEvent);\r\n                return;\r\n            }\r\n\r\n            if (!scene.pointerMovePredicate) {\r\n                scene.pointerMovePredicate = (mesh: AbstractMesh): boolean =>\r\n                    mesh.isPickable &&\r\n                    mesh.isVisible &&\r\n                    mesh.isReady() &&\r\n                    mesh.isEnabled() &&\r\n                    (mesh.enablePointerMoveEvents || scene.constantlyUpdateMeshUnderPointer || mesh._getActionManagerForTrigger() !== null) &&\r\n                    (!scene.cameraToUseForPointers || (scene.cameraToUseForPointers.layerMask & mesh.layerMask) !== 0);\r\n            }\r\n\r\n            const pickResult = scene._registeredActions > 0 || scene.constantlyUpdateMeshUnderPointer ? this._pickMove(evt as IPointerEvent) : null;\r\n            this._processPointerMove(pickResult, evt as IPointerEvent);\r\n        };\r\n\r\n        this._onPointerDown = (evt: IPointerEvent) => {\r\n            this._totalPointersPressed++;\r\n            this._pickedDownMesh = null;\r\n            this._meshPickProceed = false;\r\n\r\n            // If ExclusiveDoubleClickMode is true, we need to resolve any pending delayed clicks\r\n            if (InputManager.ExclusiveDoubleClickMode) {\r\n                for (let i = 0; i < this._delayedClicks.length; i++) {\r\n                    if (this._delayedClicks[i]) {\r\n                        // If the button that was pressed is the same as the one that was released,\r\n                        // just clear the timer.  This will be resolved in the up event.\r\n                        if (evt.button === i) {\r\n                            clearTimeout(this._delayedClicks[i]?.timeoutId);\r\n                        } else {\r\n                            // Otherwise, we need to resolve the click\r\n                            const clickInfo = this._delayedClicks[i]!.clickInfo;\r\n                            this._doubleClickOccured = false;\r\n                            clickInfo.singleClick = true;\r\n                            clickInfo.ignore = false;\r\n\r\n                            const prevEvt = this._delayedClicks[i]!.evt;\r\n                            const type = PointerEventTypes.POINTERTAP;\r\n                            const pi = new PointerInfo(type, prevEvt, this._currentPickResult);\r\n                            if (scene.onPointerObservable.hasObservers() && scene.onPointerObservable.hasSpecificMask(type)) {\r\n                                scene.onPointerObservable.notifyObservers(pi, type);\r\n                            }\r\n\r\n                            // Clear the delayed click\r\n                            this._delayedClicks[i] = null;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            this._updatePointerPosition(evt);\r\n\r\n            if (this._swipeButtonPressed === -1) {\r\n                this._swipeButtonPressed = evt.button;\r\n            }\r\n\r\n            if (scene.preventDefaultOnPointerDown && elementToAttachTo) {\r\n                evt.preventDefault();\r\n                elementToAttachTo.focus();\r\n            }\r\n\r\n            this._startingPointerPosition.x = this._pointerX;\r\n            this._startingPointerPosition.y = this._pointerY;\r\n            this._startingPointerTime = Date.now();\r\n\r\n            // PreObservable support\r\n            if (this._checkPrePointerObservable(null, evt, PointerEventTypes.POINTERDOWN)) {\r\n                return;\r\n            }\r\n\r\n            if (!scene.cameraToUseForPointers && !scene.activeCamera) {\r\n                return;\r\n            }\r\n\r\n            this._pointerCaptures[evt.pointerId] = true;\r\n\r\n            if (!scene.pointerDownPredicate) {\r\n                scene.pointerDownPredicate = (mesh: AbstractMesh): boolean => {\r\n                    return (\r\n                        mesh.isPickable &&\r\n                        mesh.isVisible &&\r\n                        mesh.isReady() &&\r\n                        mesh.isEnabled() &&\r\n                        (!scene.cameraToUseForPointers || (scene.cameraToUseForPointers.layerMask & mesh.layerMask) !== 0)\r\n                    );\r\n                };\r\n            }\r\n\r\n            // Meshes\r\n            this._pickedDownMesh = null;\r\n            let pickResult;\r\n            if (scene.skipPointerDownPicking || (scene._registeredActions === 0 && !this._checkForPicking() && !scene.onPointerDown)) {\r\n                pickResult = new PickingInfo();\r\n            } else {\r\n                pickResult = scene.pick(\r\n                    this._unTranslatedPointerX,\r\n                    this._unTranslatedPointerY,\r\n                    scene.pointerDownPredicate,\r\n                    scene.pointerDownFastCheck,\r\n                    scene.cameraToUseForPointers,\r\n                    scene.pointerDownTrianglePredicate\r\n                );\r\n            }\r\n\r\n            this._processPointerDown(pickResult, evt);\r\n        };\r\n\r\n        this._onPointerUp = (evt: IPointerEvent) => {\r\n            if (this._totalPointersPressed === 0) {\r\n                // We are attaching the pointer up to windows because of a bug in FF\r\n                return; // So we need to test it the pointer down was pressed before.\r\n            }\r\n\r\n            this._totalPointersPressed--;\r\n            this._pickedUpMesh = null;\r\n            this._meshPickProceed = false;\r\n\r\n            this._updatePointerPosition(evt);\r\n\r\n            if (scene.preventDefaultOnPointerUp && elementToAttachTo) {\r\n                evt.preventDefault();\r\n                elementToAttachTo.focus();\r\n            }\r\n\r\n            this._initClickEvent(scene.onPrePointerObservable, scene.onPointerObservable, evt, (clickInfo: _ClickInfo, pickResult: Nullable<PickingInfo>) => {\r\n                // PreObservable support\r\n                if (scene.onPrePointerObservable.hasObservers()) {\r\n                    this._skipPointerTap = false;\r\n                    if (!clickInfo.ignore) {\r\n                        if (this._checkPrePointerObservable(null, evt, PointerEventTypes.POINTERUP)) {\r\n                            // If we're skipping the next observable, we need to reset the swipe state before returning\r\n                            if (this._swipeButtonPressed === evt.button) {\r\n                                this._isSwiping = false;\r\n                                this._swipeButtonPressed = -1;\r\n                            }\r\n\r\n                            // If we're going to skip the POINTERUP, we need to reset the pointer capture\r\n                            if (evt.buttons === 0) {\r\n                                this._pointerCaptures[evt.pointerId] = false;\r\n                            }\r\n\r\n                            return;\r\n                        }\r\n                        if (!clickInfo.hasSwiped) {\r\n                            if (clickInfo.singleClick && scene.onPrePointerObservable.hasSpecificMask(PointerEventTypes.POINTERTAP)) {\r\n                                if (this._checkPrePointerObservable(null, evt, PointerEventTypes.POINTERTAP)) {\r\n                                    this._skipPointerTap = true;\r\n                                }\r\n                            }\r\n                            if (clickInfo.doubleClick && scene.onPrePointerObservable.hasSpecificMask(PointerEventTypes.POINTERDOUBLETAP)) {\r\n                                if (this._checkPrePointerObservable(null, evt, PointerEventTypes.POINTERDOUBLETAP)) {\r\n                                    this._skipPointerTap = true;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                // There should be a pointer captured at this point so if there isn't we should reset and return\r\n                if (!this._pointerCaptures[evt.pointerId]) {\r\n                    if (this._swipeButtonPressed === evt.button) {\r\n                        this._isSwiping = false;\r\n                        this._swipeButtonPressed = -1;\r\n                    }\r\n                    return;\r\n                }\r\n\r\n                // Only release capture if all buttons are released\r\n                if (evt.buttons === 0) {\r\n                    this._pointerCaptures[evt.pointerId] = false;\r\n                }\r\n                if (!scene.cameraToUseForPointers && !scene.activeCamera) {\r\n                    return;\r\n                }\r\n\r\n                if (!scene.pointerUpPredicate) {\r\n                    scene.pointerUpPredicate = (mesh: AbstractMesh): boolean => {\r\n                        return (\r\n                            mesh.isPickable &&\r\n                            mesh.isVisible &&\r\n                            mesh.isReady() &&\r\n                            mesh.isEnabled() &&\r\n                            (!scene.cameraToUseForPointers || (scene.cameraToUseForPointers.layerMask & mesh.layerMask) !== 0)\r\n                        );\r\n                    };\r\n                }\r\n\r\n                // Meshes\r\n                if (!this._meshPickProceed && ((AbstractActionManager && AbstractActionManager.HasTriggers) || this._checkForPicking() || scene.onPointerUp)) {\r\n                    this._initActionManager(null, clickInfo);\r\n                }\r\n                if (!pickResult) {\r\n                    pickResult = this._currentPickResult;\r\n                }\r\n\r\n                this._processPointerUp(pickResult, evt, clickInfo);\r\n\r\n                this._previousPickResult = this._currentPickResult;\r\n\r\n                if (this._swipeButtonPressed === evt.button) {\r\n                    this._isSwiping = false;\r\n                    this._swipeButtonPressed = -1;\r\n                }\r\n            });\r\n        };\r\n\r\n        this._onKeyDown = (evt: IKeyboardEvent) => {\r\n            const type = KeyboardEventTypes.KEYDOWN;\r\n            if (scene.onPreKeyboardObservable.hasObservers()) {\r\n                const pi = new KeyboardInfoPre(type, evt);\r\n                scene.onPreKeyboardObservable.notifyObservers(pi, type);\r\n                if (pi.skipOnKeyboardObservable) {\r\n                    return;\r\n                }\r\n            }\r\n\r\n            if (scene.onKeyboardObservable.hasObservers()) {\r\n                const pi = new KeyboardInfo(type, evt);\r\n                scene.onKeyboardObservable.notifyObservers(pi, type);\r\n            }\r\n\r\n            if (scene.actionManager) {\r\n                scene.actionManager.processTrigger(Constants.ACTION_OnKeyDownTrigger, ActionEvent.CreateNewFromScene(scene, evt));\r\n            }\r\n        };\r\n\r\n        this._onKeyUp = (evt: IKeyboardEvent) => {\r\n            const type = KeyboardEventTypes.KEYUP;\r\n            if (scene.onPreKeyboardObservable.hasObservers()) {\r\n                const pi = new KeyboardInfoPre(type, evt);\r\n                scene.onPreKeyboardObservable.notifyObservers(pi, type);\r\n                if (pi.skipOnKeyboardObservable) {\r\n                    return;\r\n                }\r\n            }\r\n\r\n            if (scene.onKeyboardObservable.hasObservers()) {\r\n                const pi = new KeyboardInfo(type, evt);\r\n                scene.onKeyboardObservable.notifyObservers(pi, type);\r\n            }\r\n\r\n            if (scene.actionManager) {\r\n                scene.actionManager.processTrigger(Constants.ACTION_OnKeyUpTrigger, ActionEvent.CreateNewFromScene(scene, evt));\r\n            }\r\n        };\r\n\r\n        // If a device connects that we can handle, wire up the observable\r\n        this._deviceSourceManager.onDeviceConnectedObservable.add((deviceSource) => {\r\n            if (deviceSource.deviceType === DeviceType.Mouse) {\r\n                deviceSource.onInputChangedObservable.add((eventData) => {\r\n                    if (\r\n                        eventData.inputIndex === PointerInput.LeftClick ||\r\n                        eventData.inputIndex === PointerInput.MiddleClick ||\r\n                        eventData.inputIndex === PointerInput.RightClick ||\r\n                        eventData.inputIndex === PointerInput.BrowserBack ||\r\n                        eventData.inputIndex === PointerInput.BrowserForward\r\n                    ) {\r\n                        if (attachDown && deviceSource.getInput(eventData.inputIndex) === 1) {\r\n                            this._onPointerDown(eventData);\r\n                        } else if (attachUp && deviceSource.getInput(eventData.inputIndex) === 0) {\r\n                            this._onPointerUp(eventData);\r\n                        }\r\n                    } else if (attachMove) {\r\n                        if (eventData.inputIndex === PointerInput.Move) {\r\n                            this._onPointerMove(eventData);\r\n                        } else if (\r\n                            eventData.inputIndex === PointerInput.MouseWheelX ||\r\n                            eventData.inputIndex === PointerInput.MouseWheelY ||\r\n                            eventData.inputIndex === PointerInput.MouseWheelZ\r\n                        ) {\r\n                            this._onPointerMove(eventData);\r\n                        }\r\n                    }\r\n                });\r\n            } else if (deviceSource.deviceType === DeviceType.Touch) {\r\n                deviceSource.onInputChangedObservable.add((eventData) => {\r\n                    if (eventData.inputIndex === PointerInput.LeftClick) {\r\n                        if (attachDown && deviceSource.getInput(eventData.inputIndex) === 1) {\r\n                            this._onPointerDown(eventData);\r\n                            if (this._totalPointersPressed > 1) {\r\n                                this._isMultiTouchGesture = true;\r\n                            }\r\n                        } else if (attachUp && deviceSource.getInput(eventData.inputIndex) === 0) {\r\n                            this._onPointerUp(eventData);\r\n                            if (this._totalPointersPressed === 0) {\r\n                                this._isMultiTouchGesture = false;\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    if (attachMove && eventData.inputIndex === PointerInput.Move) {\r\n                        this._onPointerMove(eventData);\r\n                    }\r\n                });\r\n            } else if (deviceSource.deviceType === DeviceType.Keyboard) {\r\n                deviceSource.onInputChangedObservable.add((eventData) => {\r\n                    if (eventData.type === \"keydown\") {\r\n                        this._onKeyDown(eventData);\r\n                    } else if (eventData.type === \"keyup\") {\r\n                        this._onKeyUp(eventData);\r\n                    }\r\n                });\r\n            }\r\n        });\r\n\r\n        this._alreadyAttached = true;\r\n    }\r\n\r\n    /**\r\n     * Detaches all event handlers\r\n     */\r\n    public detachControl() {\r\n        if (this._alreadyAttached) {\r\n            this._deviceSourceManager!.dispose();\r\n            this._deviceSourceManager = null;\r\n\r\n            // Cursor\r\n            if (this._alreadyAttachedTo && !this._scene.doNotHandleCursors) {\r\n                this._alreadyAttachedTo.style.cursor = this._scene.defaultCursor;\r\n            }\r\n\r\n            this._alreadyAttached = false;\r\n            this._alreadyAttachedTo = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Force the value of meshUnderPointer\r\n     * @param mesh - defines the mesh to use\r\n     * @param pointerId - optional pointer id when using more than one pointer. Defaults to 0\r\n     * @param pickResult - optional pickingInfo data used to find mesh\r\n     * @param evt - optional pointer event\r\n     */\r\n    public setPointerOverMesh(mesh: Nullable<AbstractMesh>, pointerId: number = 0, pickResult?: Nullable<PickingInfo>, evt?: IPointerEvent): void {\r\n        if (this._meshUnderPointerId[pointerId] === mesh && (!mesh || !mesh._internalAbstractMeshDataInfo._pointerOverDisableMeshTesting)) {\r\n            return;\r\n        }\r\n\r\n        const underPointerMesh = this._meshUnderPointerId[pointerId];\r\n\r\n        let actionManager: Nullable<AbstractActionManager>;\r\n        if (underPointerMesh) {\r\n            actionManager = underPointerMesh._getActionManagerForTrigger(Constants.ACTION_OnPointerOutTrigger);\r\n            if (actionManager) {\r\n                actionManager.processTrigger(Constants.ACTION_OnPointerOutTrigger, ActionEvent.CreateNew(underPointerMesh, evt, { pointerId }));\r\n            }\r\n        }\r\n\r\n        if (mesh) {\r\n            this._meshUnderPointerId[pointerId] = mesh;\r\n            this._pointerOverMesh = mesh;\r\n\r\n            actionManager = mesh._getActionManagerForTrigger(Constants.ACTION_OnPointerOverTrigger);\r\n            if (actionManager) {\r\n                actionManager.processTrigger(Constants.ACTION_OnPointerOverTrigger, ActionEvent.CreateNew(mesh, evt, { pointerId, pickResult }));\r\n            }\r\n        } else {\r\n            delete this._meshUnderPointerId[pointerId];\r\n            this._pointerOverMesh = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the mesh under the pointer\r\n     * @returns a Mesh or null if no mesh is under the pointer\r\n     */\r\n    public getPointerOverMesh(): Nullable<AbstractMesh> {\r\n        return this.meshUnderPointer;\r\n    }\r\n\r\n    /**\r\n     * @param mesh - Mesh to invalidate\r\n     * @internal\r\n     */\r\n    public _invalidateMesh(mesh: AbstractMesh) {\r\n        if (this._pointerOverMesh === mesh) {\r\n            this._pointerOverMesh = null;\r\n        }\r\n        if (this._pickedDownMesh === mesh) {\r\n            this._pickedDownMesh = null;\r\n        }\r\n        if (this._pickedUpMesh === mesh) {\r\n            this._pickedUpMesh = null;\r\n        }\r\n        for (const pointerId in this._meshUnderPointerId) {\r\n            if (this._meshUnderPointerId[pointerId] === mesh) {\r\n                delete this._meshUnderPointerId[pointerId];\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}