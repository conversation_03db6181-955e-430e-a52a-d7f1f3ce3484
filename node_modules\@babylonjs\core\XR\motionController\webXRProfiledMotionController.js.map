{"version": 3, "file": "webXRProfiledMotionController.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/motionController/webXRProfiledMotionController.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAEhF,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,wBAAwB,EAAE,MAAM,4BAA4B,CAAC;AACtE,OAAO,EAAE,YAAY,EAAE,MAAM,qCAAqC,CAAC;AACnE,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AACpE,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C;;;GAGG;AACH,MAAM,OAAO,6BAA8B,SAAQ,6BAA6B;IAgB5E,YACI,KAAY,EACZ,OAAsB,EACtB,QAAkC,EAC1B,cAAsB;IAC9B,gEAAgE;IACxD,eAIN;QAEF,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC,EAAE,OAAO,CAAC,OAAc,EAAE,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;QAR7H,mBAAc,GAAd,cAAc,CAAQ;QAEtB,oBAAe,GAAf,eAAe,CAIrB;QAzBE,uBAAkB,GAOtB,EAAE,CAAC;QACC,eAAU,GAAuC,EAAE,CAAC;QAoBxD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;IACxC,CAAC;IAEM,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;gBAC/C,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;YACzC,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAES,mBAAmB;QACzB,OAAO;YACH,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;YAC/B,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,aAAa,IAAI,CAAC,SAAS,GAAG;SAC7D,CAAC;IACN,CAAC;IAES,2BAA2B;QACjC,MAAM,SAAS,GAAG,WAAW,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC;QACpE,IAAI,CAAC,SAAS,EAAE;YACZ,MAAM,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAC;SACzF;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAES,mBAAmB,CAAC,OAAuB;QACjD,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACpC,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACvD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG;gBAC5B,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAS,EAAE,iBAAiB,CAAC,YAAY,CAAC;gBAC9E,MAAM,EAAE,EAAE;aACb,CAAC;YACF,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,EAAE;gBACzE,MAAM,WAAW,GAAG,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;gBACzE,IAAI,WAAW,CAAC,iBAAiB,KAAK,WAAW,EAAE;oBAC/C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,GAAG;wBACtD,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAS,EAAE,WAAW,CAAC,aAAc,CAAC;wBAC3E,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAS,EAAE,WAAW,CAAC,WAAY,CAAC;wBACvE,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAS,EAAE,WAAW,CAAC,WAAY,CAAC;qBAC1E,CAAC;iBACL;qBAAM;oBACH,oCAAoC;oBACpC,MAAM,UAAU,GACZ,iBAAiB,CAAC,IAAI,KAAK,wBAAwB,CAAC,aAAa,IAAI,iBAAiB,CAAC,kBAAkB;wBACrG,CAAC,CAAC,iBAAiB,CAAC,kBAAkB;wBACtC,CAAC,CAAC,WAAW,CAAC,aAAc,CAAC;oBACrC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,GAAG;wBACtD,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAS,EAAE,UAAU,CAAC;qBAC9D,CAAC;oBACF,IAAI,iBAAiB,CAAC,IAAI,KAAK,wBAAwB,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE;wBAC1G,MAAM,GAAG,GAAG,YAAY,CACpB,iBAAiB,GAAG,KAAK,EACzB;4BACI,QAAQ,EAAE,MAAM;4BAChB,QAAQ,EAAE,CAAC;yBACd,EACD,IAAI,CAAC,KAAK,CACb,CAAC;wBACF,GAAG,CAAC,QAAQ,GAAG,IAAI,gBAAgB,CAAC,iBAAiB,GAAG,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;wBACxD,GAAG,CAAC,QAAS,CAAC,YAAY,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;wBAC7D,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC;wBACvF,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC;wBACtB,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC;qBAC5C;iBACJ;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAES,YAAY,CAAC,MAAsB;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7E,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC;QACjC,IAAI,QAAQ,CAAC;QACb,wFAAwF;QACxF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAEvB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YAExB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACd,iDAAiD;gBACjD,QAAQ,GAAG,IAAI,CAAC;aACnB;SACJ;QAED,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACrC;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE;YAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;SACtD;IACL,CAAC;IAES,YAAY,CAAC,QAAiB;QACpC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO;SACV;QACD,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YAClC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACxC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;gBACvB,OAAO;aACV;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;YAC3C,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,EAAE;gBACzE,MAAM,WAAW,GAAG,iBAAiB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;gBACzE,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;gBAC5B,IAAI,WAAW,CAAC,iBAAiB,KAAK,OAAO,EAAE;oBAC3C,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;iBAC5B;qBAAM,IAAI,WAAW,CAAC,iBAAiB,KAAK,OAAO,EAAE;oBAClD,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;iBAC5B;gBACD,IAAI,WAAW,CAAC,iBAAiB,KAAK,WAAW,EAAE;oBAC/C,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE,KAAK,EAAE,WAAW,CAAC,iBAAiB,KAAK,QAAQ,CAAC,CAAC;iBAC5G;qBAAM;oBACH,aAAa;oBACb,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,SAAS,CAAC;oBAC7D,IAAI,SAAS,EAAE;wBACX,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC;qBAChE;oBACD,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE;wBACpC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC;qBACzF;iBACJ;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;CACJ", "sourcesContent": ["import type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { IMotionControllerProfile, IMotionControllerMeshMap } from \"./webXRAbstractMotionController\";\r\nimport { WebXRAbstractMotionController } from \"./webXRAbstractMotionController\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { SceneLoader } from \"../../Loading/sceneLoader\";\r\nimport { Mesh } from \"../../Meshes/mesh\";\r\nimport { Axis, Space } from \"../../Maths/math.axis\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport { WebXRControllerComponent } from \"./webXRControllerComponent\";\r\nimport { CreateSphere } from \"../../Meshes/Builders/sphereBuilder\";\r\nimport { StandardMaterial } from \"../../Materials/standardMaterial\";\r\nimport { Logger } from \"../../Misc/logger\";\r\n\r\n/**\r\n * A profiled motion controller has its profile loaded from an online repository.\r\n * The class is responsible of loading the model, mapping the keys and enabling model-animations\r\n */\r\nexport class WebXRProfiledMotionController extends WebXRAbstractMotionController {\r\n    private _buttonMeshMapping: {\r\n        [buttonName: string]: {\r\n            mainMesh?: AbstractMesh;\r\n            states: {\r\n                [state: string]: IMotionControllerMeshMap;\r\n            };\r\n        };\r\n    } = {};\r\n    private _touchDots: { [visKey: string]: AbstractMesh } = {};\r\n\r\n    /**\r\n     * The profile ID of this controller. Will be populated when the controller initializes.\r\n     */\r\n    public profileId: string;\r\n\r\n    constructor(\r\n        scene: Scene,\r\n        xrInput: XRInputSource,\r\n        _profile: IMotionControllerProfile,\r\n        private _repositoryUrl: string,\r\n        // eslint-disable-next-line @typescript-eslint/naming-convention\r\n        private controllerCache?: Array<{\r\n            filename: string;\r\n            path: string;\r\n            meshes: AbstractMesh[];\r\n        }>\r\n    ) {\r\n        super(scene, _profile.layouts[xrInput.handedness || \"none\"], xrInput.gamepad as any, xrInput.handedness, undefined, controllerCache);\r\n        this.profileId = _profile.profileId;\r\n    }\r\n\r\n    public dispose() {\r\n        super.dispose();\r\n        if (!this.controllerCache) {\r\n            Object.keys(this._touchDots).forEach((visResKey) => {\r\n                this._touchDots[visResKey].dispose();\r\n            });\r\n        }\r\n    }\r\n\r\n    protected _getFilenameAndPath(): { filename: string; path: string } {\r\n        return {\r\n            filename: this.layout.assetPath,\r\n            path: `${this._repositoryUrl}/profiles/${this.profileId}/`,\r\n        };\r\n    }\r\n\r\n    protected _getModelLoadingConstraints(): boolean {\r\n        const glbLoaded = SceneLoader.IsPluginForExtensionAvailable(\".glb\");\r\n        if (!glbLoaded) {\r\n            Logger.Warn(\"glTF / glb loader was not registered, using generic controller instead\");\r\n        }\r\n        return glbLoaded;\r\n    }\r\n\r\n    protected _processLoadedModel(_meshes: AbstractMesh[]): void {\r\n        this.getComponentIds().forEach((type) => {\r\n            const componentInLayout = this.layout.components[type];\r\n            this._buttonMeshMapping[type] = {\r\n                mainMesh: this._getChildByName(this.rootMesh!, componentInLayout.rootNodeName),\r\n                states: {},\r\n            };\r\n            Object.keys(componentInLayout.visualResponses).forEach((visualResponseKey) => {\r\n                const visResponse = componentInLayout.visualResponses[visualResponseKey];\r\n                if (visResponse.valueNodeProperty === \"transform\") {\r\n                    this._buttonMeshMapping[type].states[visualResponseKey] = {\r\n                        valueMesh: this._getChildByName(this.rootMesh!, visResponse.valueNodeName!),\r\n                        minMesh: this._getChildByName(this.rootMesh!, visResponse.minNodeName!),\r\n                        maxMesh: this._getChildByName(this.rootMesh!, visResponse.maxNodeName!),\r\n                    };\r\n                } else {\r\n                    // visibility, usually for touchpads\r\n                    const nameOfMesh =\r\n                        componentInLayout.type === WebXRControllerComponent.TOUCHPAD_TYPE && componentInLayout.touchPointNodeName\r\n                            ? componentInLayout.touchPointNodeName\r\n                            : visResponse.valueNodeName!;\r\n                    this._buttonMeshMapping[type].states[visualResponseKey] = {\r\n                        valueMesh: this._getChildByName(this.rootMesh!, nameOfMesh),\r\n                    };\r\n                    if (componentInLayout.type === WebXRControllerComponent.TOUCHPAD_TYPE && !this._touchDots[visualResponseKey]) {\r\n                        const dot = CreateSphere(\r\n                            visualResponseKey + \"dot\",\r\n                            {\r\n                                diameter: 0.0015,\r\n                                segments: 8,\r\n                            },\r\n                            this.scene\r\n                        );\r\n                        dot.material = new StandardMaterial(visualResponseKey + \"mat\", this.scene);\r\n                        (<StandardMaterial>dot.material).diffuseColor = Color3.Red();\r\n                        dot.parent = this._buttonMeshMapping[type].states[visualResponseKey].valueMesh || null;\r\n                        dot.isVisible = false;\r\n                        this._touchDots[visualResponseKey] = dot;\r\n                    }\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    protected _setRootMesh(meshes: AbstractMesh[]): void {\r\n        this.rootMesh = new Mesh(this.profileId + \"-\" + this.handedness, this.scene);\r\n        this.rootMesh.isPickable = false;\r\n        let rootMesh;\r\n        // Find the root node in the loaded glTF scene, and attach it as a child of 'parentMesh'\r\n        for (let i = 0; i < meshes.length; i++) {\r\n            const mesh = meshes[i];\r\n\r\n            mesh.isPickable = false;\r\n\r\n            if (!mesh.parent) {\r\n                // Handle root node, attach to the new parentMesh\r\n                rootMesh = mesh;\r\n            }\r\n        }\r\n\r\n        if (rootMesh) {\r\n            rootMesh.setParent(this.rootMesh);\r\n        }\r\n        if (!this.scene.useRightHandedSystem) {\r\n            this.rootMesh.rotate(Axis.Y, Math.PI, Space.WORLD);\r\n        }\r\n    }\r\n\r\n    protected _updateModel(_xrFrame: XRFrame): void {\r\n        if (this.disableAnimation) {\r\n            return;\r\n        }\r\n        this.getComponentIds().forEach((id) => {\r\n            const component = this.getComponent(id);\r\n            if (!component.hasChanges) {\r\n                return;\r\n            }\r\n            const meshes = this._buttonMeshMapping[id];\r\n            const componentInLayout = this.layout.components[id];\r\n            Object.keys(componentInLayout.visualResponses).forEach((visualResponseKey) => {\r\n                const visResponse = componentInLayout.visualResponses[visualResponseKey];\r\n                let value = component.value;\r\n                if (visResponse.componentProperty === \"xAxis\") {\r\n                    value = component.axes.x;\r\n                } else if (visResponse.componentProperty === \"yAxis\") {\r\n                    value = component.axes.y;\r\n                }\r\n                if (visResponse.valueNodeProperty === \"transform\") {\r\n                    this._lerpTransform(meshes.states[visualResponseKey], value, visResponse.componentProperty !== \"button\");\r\n                } else {\r\n                    // visibility\r\n                    const valueMesh = meshes.states[visualResponseKey].valueMesh;\r\n                    if (valueMesh) {\r\n                        valueMesh.isVisible = component.touched || component.pressed;\r\n                    }\r\n                    if (this._touchDots[visualResponseKey]) {\r\n                        this._touchDots[visualResponseKey].isVisible = component.touched || component.pressed;\r\n                    }\r\n                }\r\n            });\r\n        });\r\n    }\r\n}\r\n"]}