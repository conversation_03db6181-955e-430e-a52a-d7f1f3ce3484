{"version": 3, "file": "sceneLoaderFlags.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Loading/sceneLoaderFlags.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEjD;;GAEG;AACH,MAAM,OAAO,gBAAgB;IAOzB;;OAEG;IACI,MAAM,KAAK,mCAAmC;QACjD,OAAO,gBAAgB,CAAC,oCAAoC,CAAC;IACjE,CAAC;IAEM,MAAM,KAAK,mCAAmC,CAAC,KAAc;QAChE,gBAAgB,CAAC,oCAAoC,GAAG,KAAK,CAAC;IAClE,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,iBAAiB;QAC/B,OAAO,gBAAgB,CAAC,kBAAkB,CAAC;IAC/C,CAAC;IAEM,MAAM,KAAK,iBAAiB,CAAC,KAAc;QAC9C,gBAAgB,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAChD,CAAC;IAED;;;OAGG;IACH,gEAAgE;IACzD,MAAM,KAAK,YAAY;QAC1B,OAAO,gBAAgB,CAAC,aAAa,CAAC;IAC1C,CAAC;IAED,gEAAgE;IACzD,MAAM,KAAK,YAAY,CAAC,KAAa;QACxC,gBAAgB,CAAC,aAAa,GAAG,KAAK,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,sBAAsB;QACpC,OAAO,gBAAgB,CAAC,uBAAuB,CAAC;IACpD,CAAC;IAEM,MAAM,KAAK,sBAAsB,CAAC,KAAc;QACnD,gBAAgB,CAAC,uBAAuB,GAAG,KAAK,CAAC;IACrD,CAAC;;AAnDD,QAAQ;AACO,qDAAoC,GAAG,KAAK,CAAC;AAC7C,mCAAkB,GAAG,IAAI,CAAC;AAC1B,wCAAuB,GAAG,KAAK,CAAC;AAChC,8BAAa,GAAG,SAAS,CAAC,sBAAsB,CAAC", "sourcesContent": ["import { Constants } from \"../Engines/constants\";\r\n\r\n/**\r\n * Class used to represent data loading progression\r\n */\r\nexport class SceneLoaderFlags {\r\n    // Flags\r\n    private static _ForceFullSceneLoadingForIncremental = false;\r\n    private static _ShowLoadingScreen = true;\r\n    private static _CleanBoneMatrixWeights = false;\r\n    private static _LoggingLevel = Constants.SCENELOADER_NO_LOGGING;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if entire scene must be loaded even if scene contains incremental data\r\n     */\r\n    public static get ForceFullSceneLoadingForIncremental() {\r\n        return SceneLoaderFlags._ForceFullSceneLoadingForIncremental;\r\n    }\r\n\r\n    public static set ForceFullSceneLoadingForIncremental(value: boolean) {\r\n        SceneLoaderFlags._ForceFullSceneLoadingForIncremental = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if loading screen must be displayed while loading a scene\r\n     */\r\n    public static get ShowLoadingScreen(): boolean {\r\n        return SceneLoaderFlags._ShowLoadingScreen;\r\n    }\r\n\r\n    public static set ShowLoadingScreen(value: boolean) {\r\n        SceneLoaderFlags._ShowLoadingScreen = value;\r\n    }\r\n\r\n    /**\r\n     * Defines the current logging level (while loading the scene)\r\n     * @ignorenaming\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static get loggingLevel(): number {\r\n        return SceneLoaderFlags._LoggingLevel;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static set loggingLevel(value: number) {\r\n        SceneLoaderFlags._LoggingLevel = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or set a boolean indicating if matrix weights must be cleaned upon loading\r\n     */\r\n    public static get CleanBoneMatrixWeights(): boolean {\r\n        return SceneLoaderFlags._CleanBoneMatrixWeights;\r\n    }\r\n\r\n    public static set CleanBoneMatrixWeights(value: boolean) {\r\n        SceneLoaderFlags._CleanBoneMatrixWeights = value;\r\n    }\r\n}\r\n"]}