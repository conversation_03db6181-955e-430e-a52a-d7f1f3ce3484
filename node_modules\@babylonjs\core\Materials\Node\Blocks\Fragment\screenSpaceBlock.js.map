{"version": 3, "file": "screenSpaceBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Fragment/screenSpaceBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAEjD;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,iBAAiB;IACnD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,qBAAqB,EAAE,qCAAqC,CAAC,MAAM,CAAC,CAAC;QACxF,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAEtE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAA0C,CACrD,qCAAqC,CAAC,MAAM,GAAG,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,CAC/I,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,EAAE,CAAC,IAAI;QACnH,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE;YACvC,IAAI,wBAAwB,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,wBAAwB,CAAC,mBAAmB,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtK,IAAI,CAAC,wBAAwB,EAAE;gBAC3B,wBAAwB,GAAG,IAAI,UAAU,CAAC,qBAAqB,CAAC,CAAC;gBACjE,wBAAwB,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,mBAAmB,CAAC,CAAC;aAC3F;YACD,wBAAwB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACvE;IACL,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAErD,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;YACxB,OAAO;SACV;QAED,MAAM,uBAAuB,GAAG,mBAAmB,CAAC,sBAAsB,CAAC;QAE3E,MAAM,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAEvE,QAAQ,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE;YAChC,KAAK,qCAAqC,CAAC,OAAO;gBAC9C,KAAK,CAAC,iBAAiB,IAAI,QAAQ,gBAAgB,MAAM,uBAAuB,WAAW,MAAM,CAAC,sBAAsB,WAAW,CAAC;gBACpI,MAAM;YACV,KAAK,qCAAqC,CAAC,OAAO;gBAC9C,KAAK,CAAC,iBAAiB,IAAI,QAAQ,gBAAgB,MAAM,uBAAuB,MAAM,MAAM,CAAC,sBAAsB,KAAK,CAAC;gBACzH,MAAM;SACb;QAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,gBAAgB,UAAU,gBAAgB,KAAK,CAAC;QAC9E,KAAK,CAAC,iBAAiB,IAAI,GAAG,gBAAgB,SAAS,gBAAgB,6BAA6B,CAAC;QAErG,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC1B,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,gBAAgB,QAAQ,CAAC;SACvG;QACD,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE;YACrB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,gBAAgB,OAAO,CAAC;SACjG;QACD,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE;YACrB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,gBAAgB,OAAO,CAAC;SACjG;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { NodeMaterial } from \"../../nodeMaterial\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\n\r\n/**\r\n * Block used to transform a vector3 or a vector4 into screen space\r\n */\r\nexport class ScreenSpaceBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new ScreenSpaceBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerInput(\"vector\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerInput(\"worldViewProjection\", NodeMaterialBlockConnectionPointTypes.Matrix);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerOutput(\"x\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"y\", NodeMaterialBlockConnectionPointTypes.Float);\r\n\r\n        this.inputs[0].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"ScreenSpaceBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the vector input\r\n     */\r\n    public get vector(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the worldViewProjection transform input\r\n     */\r\n    public get worldViewProjection(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the x output component\r\n     */\r\n    public get x(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the y output component\r\n     */\r\n    public get y(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    public autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.worldViewProjection.isConnected) {\r\n            let worldViewProjectionInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.WorldViewProjection && additionalFilteringInfo(b));\r\n\r\n            if (!worldViewProjectionInput) {\r\n                worldViewProjectionInput = new InputBlock(\"worldViewProjection\");\r\n                worldViewProjectionInput.setAsSystemValue(NodeMaterialSystemValues.WorldViewProjection);\r\n            }\r\n            worldViewProjectionInput.output.connectTo(this.worldViewProjection);\r\n        }\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const vector = this.vector;\r\n        const worldViewProjection = this.worldViewProjection;\r\n\r\n        if (!vector.connectedPoint) {\r\n            return;\r\n        }\r\n\r\n        const worldViewProjectionName = worldViewProjection.associatedVariableName;\r\n\r\n        const tempVariableName = state._getFreeVariableName(\"screenSpaceTemp\");\r\n\r\n        switch (vector.connectedPoint.type) {\r\n            case NodeMaterialBlockConnectionPointTypes.Vector3:\r\n                state.compilationString += `vec4 ${tempVariableName} = ${worldViewProjectionName} * vec4(${vector.associatedVariableName}, 1.0);\\n`;\r\n                break;\r\n            case NodeMaterialBlockConnectionPointTypes.Vector4:\r\n                state.compilationString += `vec4 ${tempVariableName} = ${worldViewProjectionName} * ${vector.associatedVariableName};\\n`;\r\n                break;\r\n        }\r\n\r\n        state.compilationString += `${tempVariableName}.xy /= ${tempVariableName}.w;`;\r\n        state.compilationString += `${tempVariableName}.xy = ${tempVariableName}.xy * 0.5 + vec2(0.5, 0.5);`;\r\n\r\n        if (this.output.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(this.output, state) + ` = ${tempVariableName}.xy;\\n`;\r\n        }\r\n        if (this.x.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(this.x, state) + ` = ${tempVariableName}.x;\\n`;\r\n        }\r\n        if (this.y.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(this.y, state) + ` = ${tempVariableName}.y;\\n`;\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ScreenSpaceBlock\", ScreenSpaceBlock);\r\n"]}