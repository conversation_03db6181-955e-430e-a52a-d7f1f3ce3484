{"version": 3, "file": "nodeMaterialOptimizer.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Optimizers/nodeMaterialOptimizer.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,OAAO,qBAAqB;IAC9B;;;;OAIG;IACI,QAAQ,CAAC,kBAAuC,EAAE,oBAAyC;QAC9F,wBAAwB;IAC5B,CAAC;CACJ", "sourcesContent": ["import type { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\n\r\n/**\r\n * Root class for all node material optimizers\r\n */\r\nexport class NodeMaterialOptimizer {\r\n    /**\r\n     * Function used to optimize a NodeMaterial graph\r\n     * @param _vertexOutputNodes defines the list of output nodes for the vertex shader\r\n     * @param _fragmentOutputNodes defines the list of output nodes for the fragment shader\r\n     */\r\n    public optimize(_vertexOutputNodes: NodeMaterialBlock[], _fragmentOutputNodes: NodeMaterialBlock[]) {\r\n        // Do nothing by default\r\n    }\r\n}\r\n"]}