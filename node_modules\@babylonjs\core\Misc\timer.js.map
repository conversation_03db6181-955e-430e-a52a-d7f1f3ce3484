{"version": 3, "file": "timer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/timer.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAuEhD;;GAEG;AACH,MAAM,CAAN,IAAY,UAaX;AAbD,WAAY,UAAU;IAClB;;OAEG;IACH,2CAAI,CAAA;IACJ;;OAEG;IACH,iDAAO,CAAA;IACP;;OAEG;IACH,6CAAK,CAAA;AACT,CAAC,EAbW,UAAU,KAAV,UAAU,QAarB;AAED;;;;;GAKG;AACH,MAAM,UAAU,gBAAgB,CAAU,OAAyB;IAC/D,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7B,OAAO,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,IAAI,EAAE,CAAC;IAClE,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAC1C,CAAC,OAAY,EAAE,EAAE;QACb,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,KAAK,GAAG,GAAG,GAAG,SAAS,CAAC;QACxB,MAAM,IAAI,GAAoB;YAC1B,SAAS;YACT,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,KAAK,GAAG,OAAO,CAAC,OAAO;YACrC,OAAO;SACV,CAAC;QACF,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,EAAE,EAAE;YACpD,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC3C,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SAChD;QACD,IAAI,KAAK,IAAI,OAAO,CAAC,OAAO,EAAE;YAC1B,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC3C,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC5C;IACL,CAAC,EACD,OAAO,CAAC,oBAAoB,CAAC,IAAI,EACjC,OAAO,CAAC,oBAAoB,CAAC,WAAW,EACxC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CACrC,CAAC;IACF,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,aAAa;IAgCtB;;;OAGG;IACH,YAAY,OAAyB;QAnCrC;;WAEG;QACI,0BAAqB,GAA8B,IAAI,UAAU,EAAE,CAAC;QAC3E;;WAEG;QACI,6BAAwB,GAA8B,IAAI,UAAU,EAAE,CAAC;QAC9E;;WAEG;QACI,2BAAsB,GAA8B,IAAI,UAAU,EAAE,CAAC;QAC5E;;WAEG;QACI,6BAAwB,GAA2B,IAAI,UAAU,EAAE,CAAC;QAEnE,cAAS,GAA0B,IAAI,CAAC;QAYxC,qBAAgB,GAAY,KAAK,CAAC;QAkFlC,UAAK,GAAG,CAAC,OAAU,EAAE,EAAE;YAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;YACpC,MAAM,IAAI,GAAkB;gBACxB,SAAS,EAAE,IAAI,CAAC,UAAU;gBAC1B,WAAW,EAAE,GAAG;gBAChB,SAAS,EAAE,IAAI,CAAC,MAAM;gBACtB,YAAY,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU;gBAC3C,OAAO;aACV,CAAC;YACF,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACxE,IAAI,WAAW,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE;gBAC/C,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;aACjC;iBAAM;gBACH,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;aACpD;QACL,CAAC,CAAC;QA3FE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACpD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,oBAAoB,IAAI,EAAE,CAAC;QAChE,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,cAAc,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;QAC/D,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC;QAClC,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SACpD;QACD,IAAI,OAAO,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAClD;QACD,IAAI,OAAO,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SACxD;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,cAAc,CAAC,SAA2C;QACjE,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,YAAoB,IAAI,CAAC,UAAU;QAC5C,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,OAAO,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;SAClF;QACD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACpK,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,IAAI;QACP,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,OAAO,EAAE;YACpC,OAAO;SACV;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAClD;QACD,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAEO,SAAS,CAAC,QAAoB;QAClC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IAoBO,KAAK,CAAC,IAAmB,EAAE,UAAmB,KAAK;QACvD,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/C,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SACvD;aAAM;YACH,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SACrD;IACL,CAAC;CACJ", "sourcesContent": ["import type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { IDisposable } from \"../scene\";\r\n\r\n/**\r\n * Construction options for a timer\r\n */\r\nexport interface ITimerOptions<T> {\r\n    /**\r\n     * Time-to-end\r\n     */\r\n    timeout: number;\r\n    /**\r\n     * The context observable is used to calculate time deltas and provides the context of the timer's callbacks. Will usually be OnBeforeRenderObservable.\r\n     * Countdown calculation is done ONLY when the observable is notifying its observers, meaning that if\r\n     * you choose an observable that doesn't trigger too often, the wait time might extend further than the requested max time\r\n     */\r\n    contextObservable: Observable<T>;\r\n    /**\r\n     * Optional parameters when adding an observer to the observable\r\n     */\r\n    observableParameters?: {\r\n        mask?: number;\r\n        insertFirst?: boolean;\r\n        scope?: any;\r\n    };\r\n    /**\r\n     * An optional break condition that will stop the times prematurely. In this case onEnded will not be triggered!\r\n     */\r\n    breakCondition?: (data?: ITimerData<T>) => boolean;\r\n    /**\r\n     * Will be triggered when the time condition has met\r\n     */\r\n    onEnded?: (data: ITimerData<any>) => void;\r\n    /**\r\n     * Will be triggered when the break condition has met (prematurely ended)\r\n     */\r\n    onAborted?: (data: ITimerData<any>) => void;\r\n    /**\r\n     * Optional function to execute on each tick (or count)\r\n     */\r\n    onTick?: (data: ITimerData<any>) => void;\r\n}\r\n\r\n/**\r\n * An interface defining the data sent by the timer\r\n */\r\nexport interface ITimerData<T> {\r\n    /**\r\n     * When did it start\r\n     */\r\n    startTime: number;\r\n    /**\r\n     * Time now\r\n     */\r\n    currentTime: number;\r\n    /**\r\n     * Time passed since started\r\n     */\r\n    deltaTime: number;\r\n    /**\r\n     * How much is completed, in [0.0...1.0].\r\n     * Note that this CAN be higher than 1 due to the fact that we don't actually measure time but delta between observable calls\r\n     */\r\n    completeRate: number;\r\n    /**\r\n     * What the registered observable sent in the last count\r\n     */\r\n    payload: T;\r\n}\r\n\r\n/**\r\n * The current state of the timer\r\n */\r\nexport enum TimerState {\r\n    /**\r\n     * Timer initialized, not yet started\r\n     */\r\n    INIT,\r\n    /**\r\n     * Timer started and counting\r\n     */\r\n    STARTED,\r\n    /**\r\n     * Timer ended (whether aborted or time reached)\r\n     */\r\n    ENDED,\r\n}\r\n\r\n/**\r\n * A simple version of the timer. Will take options and start the timer immediately after calling it\r\n *\r\n * @param options options with which to initialize this timer\r\n * @returns an observer that can be used to stop the timer\r\n */\r\nexport function setAndStartTimer<T = any>(options: ITimerOptions<T>): Nullable<Observer<T>> {\r\n    let timer = 0;\r\n    const startTime = Date.now();\r\n    options.observableParameters = options.observableParameters ?? {};\r\n    const observer = options.contextObservable.add(\r\n        (payload: any) => {\r\n            const now = Date.now();\r\n            timer = now - startTime;\r\n            const data: ITimerData<any> = {\r\n                startTime,\r\n                currentTime: now,\r\n                deltaTime: timer,\r\n                completeRate: timer / options.timeout,\r\n                payload,\r\n            };\r\n            options.onTick && options.onTick(data);\r\n            if (options.breakCondition && options.breakCondition()) {\r\n                options.contextObservable.remove(observer);\r\n                options.onAborted && options.onAborted(data);\r\n            }\r\n            if (timer >= options.timeout) {\r\n                options.contextObservable.remove(observer);\r\n                options.onEnded && options.onEnded(data);\r\n            }\r\n        },\r\n        options.observableParameters.mask,\r\n        options.observableParameters.insertFirst,\r\n        options.observableParameters.scope\r\n    );\r\n    return observer;\r\n}\r\n\r\n/**\r\n * An advanced implementation of a timer class\r\n */\r\nexport class AdvancedTimer<T = any> implements IDisposable {\r\n    /**\r\n     * Will notify each time the timer calculates the remaining time\r\n     */\r\n    public onEachCountObservable: Observable<ITimerData<T>> = new Observable();\r\n    /**\r\n     * Will trigger when the timer was aborted due to the break condition\r\n     */\r\n    public onTimerAbortedObservable: Observable<ITimerData<T>> = new Observable();\r\n    /**\r\n     * Will trigger when the timer ended successfully\r\n     */\r\n    public onTimerEndedObservable: Observable<ITimerData<T>> = new Observable();\r\n    /**\r\n     * Will trigger when the timer state has changed\r\n     */\r\n    public onStateChangedObservable: Observable<TimerState> = new Observable();\r\n\r\n    private _observer: Nullable<Observer<T>> = null;\r\n    private _contextObservable: Observable<T>;\r\n    private _observableParameters: {\r\n        mask?: number;\r\n        insertFirst?: boolean;\r\n        scope?: any;\r\n    };\r\n    private _startTime: number;\r\n    private _timer: number;\r\n    private _state: TimerState;\r\n    private _breakCondition: (data: ITimerData<T>) => boolean;\r\n    private _timeToEnd: number;\r\n    private _breakOnNextTick: boolean = false;\r\n\r\n    /**\r\n     * Will construct a new advanced timer based on the options provided. Timer will not start until start() is called.\r\n     * @param options construction options for this advanced timer\r\n     */\r\n    constructor(options: ITimerOptions<T>) {\r\n        this._setState(TimerState.INIT);\r\n        this._contextObservable = options.contextObservable;\r\n        this._observableParameters = options.observableParameters ?? {};\r\n        this._breakCondition = options.breakCondition ?? (() => false);\r\n        this._timeToEnd = options.timeout;\r\n        if (options.onEnded) {\r\n            this.onTimerEndedObservable.add(options.onEnded);\r\n        }\r\n        if (options.onTick) {\r\n            this.onEachCountObservable.add(options.onTick);\r\n        }\r\n        if (options.onAborted) {\r\n            this.onTimerAbortedObservable.add(options.onAborted);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * set a breaking condition for this timer. Default is to never break during count\r\n     * @param predicate the new break condition. Returns true to break, false otherwise\r\n     */\r\n    public set breakCondition(predicate: (data: ITimerData<T>) => boolean) {\r\n        this._breakCondition = predicate;\r\n    }\r\n\r\n    /**\r\n     * Reset ALL associated observables in this advanced timer\r\n     */\r\n    public clearObservables() {\r\n        this.onEachCountObservable.clear();\r\n        this.onTimerAbortedObservable.clear();\r\n        this.onTimerEndedObservable.clear();\r\n        this.onStateChangedObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Will start a new iteration of this timer. Only one instance of this timer can run at a time.\r\n     *\r\n     * @param timeToEnd how much time to measure until timer ended\r\n     */\r\n    public start(timeToEnd: number = this._timeToEnd) {\r\n        if (this._state === TimerState.STARTED) {\r\n            throw new Error(\"Timer already started. Please stop it before starting again\");\r\n        }\r\n        this._timeToEnd = timeToEnd;\r\n        this._startTime = Date.now();\r\n        this._timer = 0;\r\n        this._observer = this._contextObservable.add(this._tick, this._observableParameters.mask, this._observableParameters.insertFirst, this._observableParameters.scope);\r\n        this._setState(TimerState.STARTED);\r\n    }\r\n\r\n    /**\r\n     * Will force a stop on the next tick.\r\n     */\r\n    public stop() {\r\n        if (this._state !== TimerState.STARTED) {\r\n            return;\r\n        }\r\n        this._breakOnNextTick = true;\r\n    }\r\n\r\n    /**\r\n     * Dispose this timer, clearing all resources\r\n     */\r\n    public dispose() {\r\n        if (this._observer) {\r\n            this._contextObservable.remove(this._observer);\r\n        }\r\n        this.clearObservables();\r\n    }\r\n\r\n    private _setState(newState: TimerState) {\r\n        this._state = newState;\r\n        this.onStateChangedObservable.notifyObservers(this._state);\r\n    }\r\n\r\n    private _tick = (payload: T) => {\r\n        const now = Date.now();\r\n        this._timer = now - this._startTime;\r\n        const data: ITimerData<T> = {\r\n            startTime: this._startTime,\r\n            currentTime: now,\r\n            deltaTime: this._timer,\r\n            completeRate: this._timer / this._timeToEnd,\r\n            payload,\r\n        };\r\n        const shouldBreak = this._breakOnNextTick || this._breakCondition(data);\r\n        if (shouldBreak || this._timer >= this._timeToEnd) {\r\n            this._stop(data, shouldBreak);\r\n        } else {\r\n            this.onEachCountObservable.notifyObservers(data);\r\n        }\r\n    };\r\n\r\n    private _stop(data: ITimerData<T>, aborted: boolean = false) {\r\n        this._contextObservable.remove(this._observer);\r\n        this._setState(TimerState.ENDED);\r\n        if (aborted) {\r\n            this.onTimerAbortedObservable.notifyObservers(data);\r\n        } else {\r\n            this.onTimerEndedObservable.notifyObservers(data);\r\n        }\r\n    }\r\n}\r\n"]}