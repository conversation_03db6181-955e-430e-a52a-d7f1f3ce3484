{"version": 3, "file": "webgpuDepthCullingState.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuDepthCullingState.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,iBAAiB,EAAE,MAAM,gCAAgC,CAAC;AAEnE;;IAEI;AACJ,MAAM,OAAO,uBAAwB,SAAQ,iBAAiB;IAG1D;;;OAGG;IACH,YAAmB,KAAgC;QAC/C,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,KAAa;QAC5B,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;YACzB,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,YAAY,CAAC,KAAa;QACjC,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,EAAE;YAC9B,OAAO;SACV;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAED,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAuB;QACvC,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;YAC1B,OAAO;SACV;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAwB;QACpC,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;YACtB,OAAO;SACV;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,SAAS,CAAC,KAAuB;QACxC,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE;YAC3B,OAAO;SACV;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,SAAS,CAAC,KAAc;QAC/B,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE;YAC3B,OAAO;SACV;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,SAAS,CAAC,KAAc;QAC/B,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE;YAC3B,OAAO;SACV;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,SAAS,CAAC,KAAuB;QACxC,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE;YAC3B,OAAO;SACV;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;IACzC,CAAC;IAEM,KAAK;QACR,KAAK,CAAC,KAAK,EAAE,CAAC;QACd,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;IACzC,CAAC;IAEM,KAAK;QACR,gBAAgB;IACpB,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { WebGPUCacheRenderPipeline } from \"./webgpuCacheRenderPipeline\";\r\nimport { DepthCullingState } from \"../../States/depthCullingState\";\r\n\r\n/**\r\n * @internal\r\n **/\r\nexport class WebGPUDepthCullingState extends DepthCullingState {\r\n    private _cache: WebGPUCacheRenderPipeline;\r\n\r\n    /**\r\n     * Initializes the state.\r\n     * @param cache\r\n     */\r\n    public constructor(cache: WebGPUCacheRenderPipeline) {\r\n        super(false);\r\n        this._cache = cache;\r\n        this.reset();\r\n    }\r\n\r\n    public get zOffset(): number {\r\n        return this._zOffset;\r\n    }\r\n\r\n    public set zOffset(value: number) {\r\n        if (this._zOffset === value) {\r\n            return;\r\n        }\r\n\r\n        this._zOffset = value;\r\n        this._isZOffsetDirty = true;\r\n        this._cache.setDepthBiasSlopeScale(value);\r\n    }\r\n\r\n    public get zOffsetUnits(): number {\r\n        return this._zOffsetUnits;\r\n    }\r\n\r\n    public set zOffsetUnits(value: number) {\r\n        if (this._zOffsetUnits === value) {\r\n            return;\r\n        }\r\n\r\n        this._zOffsetUnits = value;\r\n        this._isZOffsetDirty = true;\r\n        this._cache.setDepthBias(value);\r\n    }\r\n\r\n    public get cullFace(): Nullable<number> {\r\n        return this._cullFace;\r\n    }\r\n\r\n    public set cullFace(value: Nullable<number>) {\r\n        if (this._cullFace === value) {\r\n            return;\r\n        }\r\n\r\n        this._cullFace = value;\r\n        this._isCullFaceDirty = true;\r\n        this._cache.setCullFace(value ?? 1);\r\n    }\r\n\r\n    public get cull(): Nullable<boolean> {\r\n        return this._cull;\r\n    }\r\n\r\n    public set cull(value: Nullable<boolean>) {\r\n        if (this._cull === value) {\r\n            return;\r\n        }\r\n\r\n        this._cull = value;\r\n        this._isCullDirty = true;\r\n        this._cache.setCullEnabled(!!value);\r\n    }\r\n\r\n    public get depthFunc(): Nullable<number> {\r\n        return this._depthFunc;\r\n    }\r\n\r\n    public set depthFunc(value: Nullable<number>) {\r\n        if (this._depthFunc === value) {\r\n            return;\r\n        }\r\n\r\n        this._depthFunc = value;\r\n        this._isDepthFuncDirty = true;\r\n        this._cache.setDepthCompare(value);\r\n    }\r\n\r\n    public get depthMask(): boolean {\r\n        return this._depthMask;\r\n    }\r\n\r\n    public set depthMask(value: boolean) {\r\n        if (this._depthMask === value) {\r\n            return;\r\n        }\r\n\r\n        this._depthMask = value;\r\n        this._isDepthMaskDirty = true;\r\n        this._cache.setDepthWriteEnabled(value);\r\n    }\r\n\r\n    public get depthTest(): boolean {\r\n        return this._depthTest;\r\n    }\r\n\r\n    public set depthTest(value: boolean) {\r\n        if (this._depthTest === value) {\r\n            return;\r\n        }\r\n\r\n        this._depthTest = value;\r\n        this._isDepthTestDirty = true;\r\n        this._cache.setDepthTestEnabled(value);\r\n    }\r\n\r\n    public get frontFace(): Nullable<number> {\r\n        return this._frontFace;\r\n    }\r\n\r\n    public set frontFace(value: Nullable<number>) {\r\n        if (this._frontFace === value) {\r\n            return;\r\n        }\r\n\r\n        this._frontFace = value;\r\n        this._isFrontFaceDirty = true;\r\n        this._cache.setFrontFace(value ?? 2);\r\n    }\r\n\r\n    public reset() {\r\n        super.reset();\r\n        this._cache.resetDepthCullingState();\r\n    }\r\n\r\n    public apply() {\r\n        // nothing to do\r\n    }\r\n}\r\n"]}