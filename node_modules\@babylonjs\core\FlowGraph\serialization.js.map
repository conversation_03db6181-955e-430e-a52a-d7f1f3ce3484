{"version": 3, "file": "serialization.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/FlowGraph/serialization.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAErF,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEtD,SAAS,eAAe,CAAC,SAAiB;IACtC,OAAO,CACH,SAAS,KAAK,MAAM;QACpB,SAAS,KAAK,cAAc;QAC5B,SAAS,KAAK,YAAY;QAC1B,SAAS,KAAK,cAAc;QAC5B,SAAS,KAAK,WAAW;QACzB,SAAS,KAAK,cAAc;QAC5B,SAAS,KAAK,iBAAiB;QAC/B,SAAS,KAAK,WAAW,CAC5B,CAAC;AACN,CAAC;AAED,SAAS,iBAAiB,CAAC,SAAiB;IACxC,OAAO,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,YAAY,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,QAAQ,CAAC;AAC3K,CAAC;AAED,SAAS,WAAW,CAAC,SAAiB,EAAE,KAAoB;IACxD,IAAI,SAAS,KAAK,SAAS,EAAE;QACzB,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KACnC;SAAM,IAAI,SAAS,KAAK,SAAS,EAAE;QAChC,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KACnC;SAAM,IAAI,SAAS,KAAK,SAAS,EAAE;QAChC,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KACnC;SAAM,IAAI,SAAS,KAAK,YAAY,EAAE;QACnC,OAAO,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KACtC;SAAM,IAAI,SAAS,KAAK,QAAQ,EAAE;QAC/B,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KACnD;SAAM,IAAI,SAAS,KAAK,QAAQ,EAAE;QAC/B,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KAC7D;SAAM;QACH,MAAM,IAAI,KAAK,CAAC,6BAA6B,SAAS,EAAE,CAAC,CAAC;KAC7D;AACL,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,iCAAiC,CAAC,GAAW,EAAE,KAAU,EAAE,mBAAwB;IAC/F,MAAM,SAAS,GAAG,KAAK,EAAE,YAAY,EAAE,EAAE,IAAI,EAAE,CAAC;IAChD,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE;QAC5B,mBAAmB,CAAC,GAAG,CAAC,GAAG;YACvB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,SAAS;SACZ,CAAC;KACL;SAAM,IAAI,iBAAiB,CAAC,SAAS,CAAC,EAAE;QACrC,mBAAmB,CAAC,GAAG,CAAC,GAAG;YACvB,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE;YACtB,SAAS;SACZ,CAAC;KACL;SAAM;QACH,mBAAmB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;KACpC;AACL,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,yBAAyB,CAAC,GAAW,EAAE,mBAAwB,EAAE,KAAY;IACzF,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACnD,IAAI,UAAU,CAAC;IACf,MAAM,SAAS,GAAG,iBAAiB,EAAE,SAAS,CAAC;IAC/C,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE;QAC5B,UAAU,GAAG,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KAC5D;SAAM,IAAI,iBAAiB,CAAC,SAAS,CAAC,EAAE;QACrC,UAAU,GAAG,WAAW,CAAC,SAAS,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC;KAChE;SAAM,IAAI,SAAS,KAAK,QAAQ,EAAE;QAC/B,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;KAC1D;SAAM,IAAI,SAAS,KAAK,gBAAgB,CAAC,SAAS,EAAE;QACjD,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;KAC1D;SAAM,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,KAAK,SAAS,EAAE;QACnE,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC;KACxC;SAAM;QACH,UAAU,GAAG,iBAAiB,CAAC;KAClC;IACD,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,kBAAkB,CAAC,SAAiB;IAChD,0FAA0F;IAC1F,yBAAyB;IACzB,OAAO,SAAS,KAAK,oBAAoB,IAAI,SAAS,KAAK,oBAAoB,IAAI,SAAS,KAAK,sBAAsB,IAAI,SAAS,KAAK,sBAAsB,CAAC;AACpK,CAAC", "sourcesContent": ["import { Color3, Color4 } from \"../Maths/math.color\";\r\nimport { Matrix, Quaternion, Vector2, Vector3, Vector4 } from \"../Maths/math.vector\";\r\nimport type { Scene } from \"../scene\";\r\nimport { FlowGraphInteger } from \"./flowGraphInteger\";\r\n\r\nfunction isMeshClassName(className: string) {\r\n    return (\r\n        className === \"Mesh\" ||\r\n        className === \"AbstractMesh\" ||\r\n        className === \"GroundMesh\" ||\r\n        className === \"InstanceMesh\" ||\r\n        className === \"LinesMesh\" ||\r\n        className === \"GoldbergMesh\" ||\r\n        className === \"GreasedLineMesh\" ||\r\n        className === \"TrailMesh\"\r\n    );\r\n}\r\n\r\nfunction isVectorClassName(className: string) {\r\n    return className === \"Vector2\" || className === \"Vector3\" || className === \"Vector4\" || className === \"Quaternion\" || className === \"Color3\" || className === \"Color4\";\r\n}\r\n\r\nfunction parseVector(className: string, value: Array<number>) {\r\n    if (className === \"Vector2\") {\r\n        return Vector2.FromArray(value);\r\n    } else if (className === \"Vector3\") {\r\n        return Vector3.FromArray(value);\r\n    } else if (className === \"Vector4\") {\r\n        return Vector4.FromArray(value);\r\n    } else if (className === \"Quaternion\") {\r\n        return Quaternion.FromArray(value);\r\n    } else if (className === \"Color3\") {\r\n        return new Color3(value[0], value[1], value[2]);\r\n    } else if (className === \"Color4\") {\r\n        return new Color4(value[0], value[1], value[2], value[3]);\r\n    } else {\r\n        throw new Error(`Unknown vector class name ${className}`);\r\n    }\r\n}\r\n\r\n/**\r\n * The default function that serializes values in a context object to a serialization object\r\n * @param key the key where the value should be stored in the serialization object\r\n * @param value the value to store\r\n * @param serializationObject the object where the value will be stored\r\n */\r\nexport function defaultValueSerializationFunction(key: string, value: any, serializationObject: any) {\r\n    const className = value?.getClassName?.() ?? \"\";\r\n    if (isMeshClassName(className)) {\r\n        serializationObject[key] = {\r\n            name: value.name,\r\n            className,\r\n        };\r\n    } else if (isVectorClassName(className)) {\r\n        serializationObject[key] = {\r\n            value: value.asArray(),\r\n            className,\r\n        };\r\n    } else {\r\n        serializationObject[key] = value;\r\n    }\r\n}\r\n\r\n/**\r\n * The default function that parses values stored in a serialization object\r\n * @param key the key to the value that will be parsed\r\n * @param serializationObject the object that will be parsed\r\n * @param scene\r\n * @returns\r\n */\r\nexport function defaultValueParseFunction(key: string, serializationObject: any, scene: Scene) {\r\n    const intermediateValue = serializationObject[key];\r\n    let finalValue;\r\n    const className = intermediateValue?.className;\r\n    if (isMeshClassName(className)) {\r\n        finalValue = scene.getMeshByName(intermediateValue.name);\r\n    } else if (isVectorClassName(className)) {\r\n        finalValue = parseVector(className, intermediateValue.value);\r\n    } else if (className === \"Matrix\") {\r\n        finalValue = Matrix.FromArray(intermediateValue.value);\r\n    } else if (className === FlowGraphInteger.ClassName) {\r\n        finalValue = FlowGraphInteger.Parse(intermediateValue);\r\n    } else if (intermediateValue && intermediateValue.value !== undefined) {\r\n        finalValue = intermediateValue.value;\r\n    } else {\r\n        finalValue = intermediateValue;\r\n    }\r\n    return finalValue;\r\n}\r\n\r\n/**\r\n * Given a name of a flow graph block class, return if this\r\n * class needs to be created with a path converter. Used in\r\n * parsing.\r\n * @param className the name of the flow graph block class\r\n * @returns a boolean indicating if the class needs a path converter\r\n */\r\nexport function needsPathConverter(className: string) {\r\n    // I am not using the ClassName property here because it was causing a circular dependency\r\n    // that jest didn't like!\r\n    return className === \"FGSetPropertyBlock\" || className === \"FGGetPropertyBlock\" || className === \"FGPlayAnimationBlock\" || className === \"FGMeshPickEventBlock\";\r\n}\r\n"]}