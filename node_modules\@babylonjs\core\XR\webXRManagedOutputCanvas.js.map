{"version": 3, "file": "webXRManagedOutputCanvas.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/XR/webXRManagedOutputCanvas.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,OAAO,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAC;AAE3D;;GAEG;AACH,MAAM,OAAO,+BAA+B;IAexC;;;;OAIG;IACI,MAAM,CAAC,WAAW,CAAC,MAAmB;QACzC,MAAM,QAAQ,GAAG,IAAI,+BAA+B,EAAE,CAAC;QACvD,QAAQ,CAAC,aAAa,GAAG;YACrB,SAAS,EAAE,IAAI;YACf,KAAK,EAAE,IAAI;YACX,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI;YAC/C,KAAK,EAAE,IAAI;YACX,sBAAsB,EAAE,CAAC;SAC5B,CAAC;QAEF,QAAQ,CAAC,iBAAiB,GAAG,qGAAqG,CAAC;QAEnI,OAAO,QAAQ,CAAC;IACpB,CAAC;CACJ;AACD;;GAEG;AACH,MAAM,OAAO,wBAAwB;IAyBjC;;;;OAIG;IACH,YACI,iBAAsC,EAC9B,WAA4C,+BAA+B,CAAC,WAAW,EAAE;QAAzF,aAAQ,GAAR,QAAQ,CAAiF;QA/B7F,YAAO,GAAgC,IAAI,CAAC;QAC5C,YAAO,GAAyB,IAAI,CAAC;QAW7C;;WAEG;QACI,YAAO,GAA2B,IAAI,CAAC;QAEtC,oBAAe,GAAgC,IAAI,CAAC;QAE5D;;WAEG;QACI,4BAAuB,GAA6B,IAAI,UAAU,EAAE,CAAC;QAWxE,IAAI,CAAC,OAAO,GAAG,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QACnD,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;YAC1C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;YACzB,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,IAAI,0CAA0C,CAAC;YACrG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;SACxC;aAAM;YACH,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;SACxD;QAED,iBAAiB,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE;YACvC,IAAI,CAAC,UAAU,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,iBAAiB,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE;YACxC,IAAI,CAAC,aAAa,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,sBAAsB,CAAC,SAAoB;QACpD,MAAM,WAAW,GAAG,GAAG,EAAE;YACrB,IAAI,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC5F,IAAI,CAAC,eAAe,GAAG,IAAI,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChE,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3D,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,CAAC,CAAC;QAEF,4CAA4C;QAC5C,IAAI,CAAE,IAAI,CAAC,aAAqB,CAAC,gBAAgB,EAAE;YAC/C,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;SACzC;QAED,OAAQ,IAAI,CAAC,aAAqB;aAC7B,gBAAgB,EAAE;aAClB,IAAI;QACD,qGAAqG;QACrG,GAAG,EAAE,GAAE,CAAC,EACR,GAAG,EAAE;YACD,uCAAuC;YACvC,KAAK,CAAC,IAAI,CAAC,8FAA8F,CAAC,CAAC;QAC/G,CAAC,CACJ;aACA,IAAI,CAAC,GAAG,EAAE;YACP,OAAO,WAAW,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;IACX,CAAC;IAEO,UAAU;QACd,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE;YACpF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC3C;QACD,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SAC7B;aAAM;YACH,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,EAAE;gBACtC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAEO,aAAa;QACjB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE;YAC5H,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC3C;QACD,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAEO,cAAc,CAAC,OAAgB,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC,eAAe;QACvE,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YAChC,OAAO;SACV;QACD,IAAI,IAAI,EAAE;YACN,IAAI,OAAO,EAAE;gBACT,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE;oBACpD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC;oBACrD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC;iBAC1D;qBAAM;oBACH,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;iBACjE;aACJ;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE;oBACpD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC;oBACjE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC;iBACtE;qBAAM;oBACH,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;iBACzF;aACJ;SACJ;IACL,CAAC;IAEO,uBAAuB,CAAC,MAAmC;QAC/D,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,MAAM,EAAE;YACT,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,aAAqB,GAAG,IAAI,CAAC;SACtC;aAAM;YACH,IAAI,CAAC,mBAAmB,GAAG;gBACvB,KAAK,EAAE,MAAM,CAAC,WAAW;gBACzB,MAAM,EAAE,MAAM,CAAC,YAAY;aAC9B,CAAC;YACF,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,aAAa,GAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC5D,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACrB,IAAI,CAAC,aAAa,GAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;aAC9D;SACJ;IACL,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { ThinEngine } from \"../Engines/thinEngine\";\r\nimport type { WebXRRenderTarget } from \"./webXRTypes\";\r\nimport type { WebXRSessionManager } from \"./webXRSessionManager\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport type { WebXRLayerWrapper } from \"./webXRLayerWrapper\";\r\nimport { WebXRWebGLLayerWrapper } from \"./webXRWebGLLayer\";\r\n\r\n/**\r\n * Configuration object for WebXR output canvas\r\n */\r\nexport class WebXRManagedOutputCanvasOptions {\r\n    /**\r\n     * An optional canvas in case you wish to create it yourself and provide it here.\r\n     * If not provided, a new canvas will be created\r\n     */\r\n    public canvasElement?: HTMLCanvasElement;\r\n    /**\r\n     * Options for this XR Layer output\r\n     */\r\n    public canvasOptions?: XRWebGLLayerInit;\r\n    /**\r\n     * CSS styling for a newly created canvas (if not provided)\r\n     */\r\n    public newCanvasCssStyle?: string;\r\n\r\n    /**\r\n     * Get the default values of the configuration object\r\n     * @param engine defines the engine to use (can be null)\r\n     * @returns default values of this configuration object\r\n     */\r\n    public static GetDefaults(engine?: ThinEngine): WebXRManagedOutputCanvasOptions {\r\n        const defaults = new WebXRManagedOutputCanvasOptions();\r\n        defaults.canvasOptions = {\r\n            antialias: true,\r\n            depth: true,\r\n            stencil: engine ? engine.isStencilEnable : true,\r\n            alpha: true,\r\n            framebufferScaleFactor: 1,\r\n        };\r\n\r\n        defaults.newCanvasCssStyle = \"position:absolute; bottom:0px;right:0px;z-index:10;width:90%;height:100%;background-color: #000000;\";\r\n\r\n        return defaults;\r\n    }\r\n}\r\n/**\r\n * Creates a canvas that is added/removed from the webpage when entering/exiting XR\r\n */\r\nexport class WebXRManagedOutputCanvas implements WebXRRenderTarget {\r\n    private _canvas: Nullable<HTMLCanvasElement> = null;\r\n    private _engine: Nullable<ThinEngine> = null;\r\n    private _originalCanvasSize: {\r\n        width: number;\r\n        height: number;\r\n    };\r\n\r\n    /**\r\n     * Rendering context of the canvas which can be used to display/mirror xr content\r\n     */\r\n    public canvasContext: WebGLRenderingContext;\r\n\r\n    /**\r\n     * xr layer for the canvas\r\n     */\r\n    public xrLayer: Nullable<XRWebGLLayer> = null;\r\n\r\n    private _xrLayerWrapper: Nullable<WebXRLayerWrapper> = null;\r\n\r\n    /**\r\n     * Observers registered here will be triggered when the xr layer was initialized\r\n     */\r\n    public onXRLayerInitObservable: Observable<XRWebGLLayer> = new Observable();\r\n\r\n    /**\r\n     * Initializes the canvas to be added/removed upon entering/exiting xr\r\n     * @param _xrSessionManager The XR Session manager\r\n     * @param _options optional configuration for this canvas output. defaults will be used if not provided\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        private _options: WebXRManagedOutputCanvasOptions = WebXRManagedOutputCanvasOptions.GetDefaults()\r\n    ) {\r\n        this._engine = _xrSessionManager.scene.getEngine();\r\n        this._engine.onDisposeObservable.addOnce(() => {\r\n            this._engine = null;\r\n        });\r\n\r\n        if (!_options.canvasElement) {\r\n            const canvas = document.createElement(\"canvas\");\r\n            canvas.style.cssText = this._options.newCanvasCssStyle || \"position:absolute; bottom:0px;right:0px;\";\r\n            this._setManagedOutputCanvas(canvas);\r\n        } else {\r\n            this._setManagedOutputCanvas(_options.canvasElement);\r\n        }\r\n\r\n        _xrSessionManager.onXRSessionInit.add(() => {\r\n            this._addCanvas();\r\n        });\r\n\r\n        _xrSessionManager.onXRSessionEnded.add(() => {\r\n            this._removeCanvas();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Disposes of the object\r\n     */\r\n    public dispose() {\r\n        this._removeCanvas();\r\n        this._setManagedOutputCanvas(null);\r\n    }\r\n\r\n    /**\r\n     * Initializes a XRWebGLLayer to be used as the session's baseLayer.\r\n     * @param xrSession xr session\r\n     * @returns a promise that will resolve once the XR Layer has been created\r\n     */\r\n    public async initializeXRLayerAsync(xrSession: XRSession): Promise<XRWebGLLayer> {\r\n        const createLayer = () => {\r\n            this.xrLayer = new XRWebGLLayer(xrSession, this.canvasContext, this._options.canvasOptions);\r\n            this._xrLayerWrapper = new WebXRWebGLLayerWrapper(this.xrLayer);\r\n            this.onXRLayerInitObservable.notifyObservers(this.xrLayer);\r\n            return this.xrLayer;\r\n        };\r\n\r\n        // support canvases without makeXRCompatible\r\n        if (!(this.canvasContext as any).makeXRCompatible) {\r\n            return Promise.resolve(createLayer());\r\n        }\r\n\r\n        return (this.canvasContext as any)\r\n            .makeXRCompatible()\r\n            .then(\r\n                // catch any error and continue. When using the emulator is throws this error for no apparent reason.\r\n                () => {},\r\n                () => {\r\n                    // log the error, continue nonetheless!\r\n                    Tools.Warn(\"Error executing makeXRCompatible. This does not mean that the session will work incorrectly.\");\r\n                }\r\n            )\r\n            .then(() => {\r\n                return createLayer();\r\n            });\r\n    }\r\n\r\n    private _addCanvas() {\r\n        if (this._canvas && this._engine && this._canvas !== this._engine.getRenderingCanvas()) {\r\n            document.body.appendChild(this._canvas);\r\n        }\r\n        if (this.xrLayer) {\r\n            this._setCanvasSize(true);\r\n        } else {\r\n            this.onXRLayerInitObservable.addOnce(() => {\r\n                this._setCanvasSize(true);\r\n            });\r\n        }\r\n    }\r\n\r\n    private _removeCanvas() {\r\n        if (this._canvas && this._engine && document.body.contains(this._canvas) && this._canvas !== this._engine.getRenderingCanvas()) {\r\n            document.body.removeChild(this._canvas);\r\n        }\r\n        this._setCanvasSize(false);\r\n    }\r\n\r\n    private _setCanvasSize(init: boolean = true, xrLayer = this._xrLayerWrapper) {\r\n        if (!this._canvas || !this._engine) {\r\n            return;\r\n        }\r\n        if (init) {\r\n            if (xrLayer) {\r\n                if (this._canvas !== this._engine.getRenderingCanvas()) {\r\n                    this._canvas.style.width = xrLayer.getWidth() + \"px\";\r\n                    this._canvas.style.height = xrLayer.getHeight() + \"px\";\r\n                } else {\r\n                    this._engine.setSize(xrLayer.getWidth(), xrLayer.getHeight());\r\n                }\r\n            }\r\n        } else {\r\n            if (this._originalCanvasSize) {\r\n                if (this._canvas !== this._engine.getRenderingCanvas()) {\r\n                    this._canvas.style.width = this._originalCanvasSize.width + \"px\";\r\n                    this._canvas.style.height = this._originalCanvasSize.height + \"px\";\r\n                } else {\r\n                    this._engine.setSize(this._originalCanvasSize.width, this._originalCanvasSize.height);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private _setManagedOutputCanvas(canvas: Nullable<HTMLCanvasElement>) {\r\n        this._removeCanvas();\r\n        if (!canvas) {\r\n            this._canvas = null;\r\n            (this.canvasContext as any) = null;\r\n        } else {\r\n            this._originalCanvasSize = {\r\n                width: canvas.offsetWidth,\r\n                height: canvas.offsetHeight,\r\n            };\r\n            this._canvas = canvas;\r\n            this.canvasContext = <any>this._canvas.getContext(\"webgl2\");\r\n            if (!this.canvasContext) {\r\n                this.canvasContext = <any>this._canvas.getContext(\"webgl\");\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}