{"version": 3, "file": "spotLight.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Lights/spotLight.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAGnE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAI/B,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAIxD,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACpD,OAAO,GAAG,EAAE,CAAC,IAAI,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAClF,CAAC,CAAC,CAAC;AAEH;;;;;;GAMG;AACH,MAAM,OAAO,SAAU,SAAQ,WAAW;IAqBtC;;OAEG;IAEH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IACD;;OAEG;IACH,IAAW,KAAK,CAAC,KAAa;QAC1B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC;QACnD,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IAEH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;;;OAIG;IACH,IAAW,UAAU,CAAC,KAAa;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAGD;;OAEG;IAEH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IACD;;OAEG;IACH,IAAW,gBAAgB,CAAC,KAAa;QACrC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACxC,CAAC;IASD;;OAEG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAGD;;OAEG;IAEH,IAAW,0BAA0B;QACjC,OAAO,IAAI,CAAC,2BAA2B,CAAC;IAC5C,CAAC;IACD;;OAEG;IACH,IAAW,0BAA0B,CAAC,KAAa;QAC/C,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;QACzC,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC;IACvD,CAAC;IAGD;;OAEG;IAEH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IACD;;OAEG;IACH,IAAW,yBAAyB,CAAC,KAAa;QAC9C,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC;IACvD,CAAC;IAGD;;OAEG;IAEH,IAAW,4BAA4B;QACnC,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IACD;;OAEG;IACH,IAAW,4BAA4B,CAAC,KAAc;QAClD,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC;IACvD,CAAC;IAKD;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IACD;;OAEG;IACH,IAAW,iBAAiB,CAAC,KAA4B;QACrD,IAAI,IAAI,CAAC,kBAAkB,KAAK,KAAK,EAAE;YACnC,OAAO;SACV;QACD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACpC,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE;YAC/D,IAAI,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE;gBACzD,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE;oBACzD,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBACnC,CAAC,CAAC,CAAC;aACN;iBAAM,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE;gBACtD,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,EAAE;oBAClD,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBACnC,CAAC,CAAC,CAAC;aACN;SACJ;IACL,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,OAAoB;QACpD,OAAQ,OAA6B,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC9E,CAAC;IAEO,MAAM,CAAC,UAAU,CAAC,OAAoB;QAC1C,OAAQ,OAAmB,CAAC,gBAAgB,KAAK,SAAS,CAAC;IAC/D,CAAC;IASD;;OAEG;IACH,IAAW,sCAAsC;QAC7C,OAAO,IAAI,CAAC,uCAAuC,CAAC;IACxD,CAAC;IAED,IAAW,sCAAsC,CAAC,UAAkB;QAChE,IAAI,CAAC,uCAAuC,GAAG,UAAU,CAAC;QAC1D,IAAI,CAAC,sCAAsC,GAAG,KAAK,CAAC;QACpD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAID;;;;;;;;;;OAUG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,SAAkB,EAAE,KAAa,EAAE,QAAgB,EAAE,KAAa;QAC3G,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAlMf,gBAAW,GAAW,CAAC,CAAC;QAiExB,6BAAwB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAQvC,gCAA2B,GAAW,IAAI,CAAC;QAgB3C,+BAA0B,GAAW,MAAM,CAAC;QAgB5C,kCAA6B,GAAY,OAAO,CAAC,EAAE,EAAE,CAAC;QAuDxD,qCAAgC,GAAG,IAAI,CAAC;QACxC,2CAAsC,GAAG,IAAI,CAAC;QAC9C,4BAAuB,GAAG,IAAI,CAAC;QAC/B,uCAAkC,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACpD,sCAAiC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAElD,4CAAuC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAcxD,oCAA+B,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAgBxI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,KAAK,CAAC,qBAAqB,CAAC;IACvC,CAAC;IAED;;;OAGG;IACO,aAAa,CAAC,KAAc;QAClC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;IACjD,CAAC;IAED;;;OAGG;IACO,YAAY,CAAC,KAAc;QACjC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;IACjD,CAAC;IAED;;;;;;OAMG;IACH,6DAA6D;IACnD,iCAAiC,CAAC,MAAc,EAAE,UAAkB,EAAE,UAA+B;QAC3G,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC;QAElD,IAAI,CAAC,YAAY,EAAE;YACf,OAAO;SACV;QAED,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC;QAEnD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;QACjF,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;QAEjF,MAAM,qBAAqB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,qBAAqB,CAAC;QAEhF,MAAM,CAAC,qBAAqB,CACxB,KAAK,EACL,GAAG,EACH,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACnC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACnC,MAAM,EACN,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,EACvC,SAAS,EACT,qBAAqB,CACxB,CAAC;IACN,CAAC;IAES,wCAAwC;QAC9C,IAAI,CAAC,gCAAgC,GAAG,KAAK,CAAC;QAC9C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QAEpC,IAAI,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACxG,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,IAAI,CAAC,kCAAkC,EAAE,IAAI,CAAC,6BAA6B,EAAE,IAAI,CAAC,iCAAiC,CAAC,CAAC;IAC1K,CAAC;IAES,8CAA8C;QACpD,IAAI,CAAC,sCAAsC,GAAG,KAAK,CAAC;QACpD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QAEpC,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,0BAA0B,CAAC;QAElD,MAAM,CAAC,GAAG,QAAQ,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC;QAC5C,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;QACzB,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;QAC5C,MAAM,CAAC,GAAG,GAAG,CAAC;QAEd,MAAM,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,uCAAuC,CAAC,CAAC;IACrJ,CAAC;IAED;;OAEG;IACO,+BAA+B;QACrC,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;QAErC,IAAI,CAAC,iCAAiC,CAAC,aAAa,CAAC,IAAI,CAAC,uCAAuC,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAClI,IAAI,IAAI,CAAC,kBAAkB,YAAY,OAAO,EAAE;YAC5C,MAAM,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,GAAG,CAAC;YAC/C,MAAM,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,GAAG,CAAC;YAC/C,MAAM,CAAC,eAAe,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,+BAA+B,CAAC,CAAC;SAC5I;QACD,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,IAAI,CAAC,+BAA+B,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACrH,CAAC;IAES,mBAAmB;QACzB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACrD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;IACjC,CAAC;IAEO,mBAAmB;QACvB,IAAI,CAAC,gBAAgB,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;QACrG,IAAI,CAAC,iBAAiB,GAAG,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC;IACzE,CAAC;IAED;;;;;OAKG;IACI,wBAAwB,CAAC,MAAc,EAAE,UAAkB;QAC9D,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE;YAC5D,IAAI,IAAI,CAAC,gCAAgC,EAAE;gBACvC,IAAI,CAAC,wCAAwC,EAAE,CAAC;aACnD;YACD,IAAI,IAAI,CAAC,sCAAsC,EAAE;gBAC7C,IAAI,CAAC,8CAA8C,EAAE,CAAC;aACzD;YACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,+BAA+B,EAAE,CAAC;aAC1C;YACD,MAAM,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACxF,MAAM,CAAC,UAAU,CAAC,wBAAwB,GAAG,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACpF;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,MAAc,EAAE,UAAkB;QACtD,IAAI,kBAAkB,CAAC;QAEvB,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE;YACtC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAE9J,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACrE;aAAM;YACH,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAE7H,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC1D;QAED,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QAEtJ,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;QACpJ,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,4BAA4B,CAAC,MAAc,EAAE,oBAA4B;QAC5E,IAAI,kBAAkB,CAAC;QAEvB,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE;YACtC,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACrE;aAAM;YACH,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC1D;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE;YACtC,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;SAC/G;aAAM;YACH,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC;SAC5G;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;SACrC;IACL,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,YAAoB;QACpC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;QAEjF,OAAO,MAAM,CAAC,qBAAqB,IAAI,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9H,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,YAAoB;QACpC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;QAEjF,OAAO,MAAM,CAAC,qBAAqB,IAAI,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7E,CAAC;IAED;;;;OAIG;IACI,2BAA2B,CAAC,OAAY,EAAE,UAAkB;QAC/D,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;QACzC,OAAO,CAAC,uBAAuB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAC9H,CAAC;CACJ;AAzaG;IADC,SAAS,EAAE;sCAGX;AAkBD;IADC,SAAS,EAAE;2CAGX;AAgBD;IADC,SAAS,EAAE;iDAGX;AAaM;IADN,SAAS,EAAE;2CACY;AAexB;IADC,SAAS,EAAE;2DAGX;AAcD;IADC,SAAS,EAAE;0DAGX;AAcD;IADC,SAAS,EAAE;6DAGX;AAUO;IADP,kBAAkB,CAAC,uBAAuB,CAAC;qDACM", "sourcesContent": ["import { serialize, serializeAsTexture } from \"../Misc/decorators\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Matrix, Vector3 } from \"../Maths/math.vector\";\r\nimport { Node } from \"../node\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport { Light } from \"./light\";\r\nimport { ShadowLight } from \"./shadowLight\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport type { ProceduralTexture } from \"../Materials/Textures/Procedurals/proceduralTexture\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\n\r\nNode.AddNodeConstructor(\"Light_Type_2\", (name, scene) => {\r\n    return () => new SpotLight(name, Vector3.Zero(), Vector3.Zero(), 0, 0, scene);\r\n});\r\n\r\n/**\r\n * A spot light is defined by a position, a direction, an angle, and an exponent.\r\n * These values define a cone of light starting from the position, emitting toward the direction.\r\n * The angle, in radians, defines the size (field of illumination) of the spotlight's conical beam,\r\n * and the exponent defines the speed of the decay of the light with distance (reach).\r\n * Documentation: https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n */\r\nexport class SpotLight extends ShadowLight {\r\n    /*\r\n        upVector , rightVector and direction will form the coordinate system for this spot light.\r\n        These three vectors will be used as projection matrix when doing texture projection.\r\n\r\n        Also we have the following rules always holds:\r\n        direction cross up   = right\r\n        right cross direction = up\r\n        up cross right       = forward\r\n\r\n        light_near and light_far will control the range of the texture projection. If a plane is\r\n        out of the range in spot light space, there is no texture projection.\r\n    */\r\n\r\n    private _angle: number;\r\n    private _innerAngle: number = 0;\r\n    private _cosHalfAngle: number;\r\n\r\n    private _lightAngleScale: number;\r\n    private _lightAngleOffset: number;\r\n\r\n    /**\r\n     * Gets the cone angle of the spot light in Radians.\r\n     */\r\n    @serialize()\r\n    public get angle(): number {\r\n        return this._angle;\r\n    }\r\n    /**\r\n     * Sets the cone angle of the spot light in Radians.\r\n     */\r\n    public set angle(value: number) {\r\n        this._angle = value;\r\n        this._cosHalfAngle = Math.cos(value * 0.5);\r\n        this._projectionTextureProjectionLightDirty = true;\r\n        this.forceProjectionMatrixCompute();\r\n        this._computeAngleValues();\r\n    }\r\n\r\n    /**\r\n     * Only used in gltf falloff mode, this defines the angle where\r\n     * the directional falloff will start before cutting at angle which could be seen\r\n     * as outer angle.\r\n     */\r\n    @serialize()\r\n    public get innerAngle(): number {\r\n        return this._innerAngle;\r\n    }\r\n    /**\r\n     * Only used in gltf falloff mode, this defines the angle where\r\n     * the directional falloff will start before cutting at angle which could be seen\r\n     * as outer angle.\r\n     */\r\n    public set innerAngle(value: number) {\r\n        this._innerAngle = value;\r\n        this._computeAngleValues();\r\n    }\r\n\r\n    private _shadowAngleScale: number;\r\n    /**\r\n     * Allows scaling the angle of the light for shadow generation only.\r\n     */\r\n    @serialize()\r\n    public get shadowAngleScale(): number {\r\n        return this._shadowAngleScale;\r\n    }\r\n    /**\r\n     * Allows scaling the angle of the light for shadow generation only.\r\n     */\r\n    public set shadowAngleScale(value: number) {\r\n        this._shadowAngleScale = value;\r\n        this.forceProjectionMatrixCompute();\r\n    }\r\n\r\n    /**\r\n     * The light decay speed with the distance from the emission spot.\r\n     */\r\n    @serialize()\r\n    public exponent: number;\r\n\r\n    private _projectionTextureMatrix = Matrix.Zero();\r\n    /**\r\n     * Allows reading the projection texture\r\n     */\r\n    public get projectionTextureMatrix(): Matrix {\r\n        return this._projectionTextureMatrix;\r\n    }\r\n\r\n    protected _projectionTextureLightNear: number = 1e-6;\r\n    /**\r\n     * Gets the near clip of the Spotlight for texture projection.\r\n     */\r\n    @serialize()\r\n    public get projectionTextureLightNear(): number {\r\n        return this._projectionTextureLightNear;\r\n    }\r\n    /**\r\n     * Sets the near clip of the Spotlight for texture projection.\r\n     */\r\n    public set projectionTextureLightNear(value: number) {\r\n        this._projectionTextureLightNear = value;\r\n        this._projectionTextureProjectionLightDirty = true;\r\n    }\r\n\r\n    protected _projectionTextureLightFar: number = 1000.0;\r\n    /**\r\n     * Gets the far clip of the Spotlight for texture projection.\r\n     */\r\n    @serialize()\r\n    public get projectionTextureLightFar(): number {\r\n        return this._projectionTextureLightFar;\r\n    }\r\n    /**\r\n     * Sets the far clip of the Spotlight for texture projection.\r\n     */\r\n    public set projectionTextureLightFar(value: number) {\r\n        this._projectionTextureLightFar = value;\r\n        this._projectionTextureProjectionLightDirty = true;\r\n    }\r\n\r\n    protected _projectionTextureUpDirection: Vector3 = Vector3.Up();\r\n    /**\r\n     * Gets the Up vector of the Spotlight for texture projection.\r\n     */\r\n    @serialize()\r\n    public get projectionTextureUpDirection(): Vector3 {\r\n        return this._projectionTextureUpDirection;\r\n    }\r\n    /**\r\n     * Sets the Up vector of the Spotlight for texture projection.\r\n     */\r\n    public set projectionTextureUpDirection(value: Vector3) {\r\n        this._projectionTextureUpDirection = value;\r\n        this._projectionTextureProjectionLightDirty = true;\r\n    }\r\n\r\n    @serializeAsTexture(\"projectedLightTexture\")\r\n    private _projectionTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Gets the projection texture of the light.\r\n     */\r\n    public get projectionTexture(): Nullable<BaseTexture> {\r\n        return this._projectionTexture;\r\n    }\r\n    /**\r\n     * Sets the projection texture of the light.\r\n     */\r\n    public set projectionTexture(value: Nullable<BaseTexture>) {\r\n        if (this._projectionTexture === value) {\r\n            return;\r\n        }\r\n        this._projectionTexture = value;\r\n        this._projectionTextureDirty = true;\r\n        if (this._projectionTexture && !this._projectionTexture.isReady()) {\r\n            if (SpotLight._IsProceduralTexture(this._projectionTexture)) {\r\n                this._projectionTexture.getEffect().executeWhenCompiled(() => {\r\n                    this._markMeshesAsLightDirty();\r\n                });\r\n            } else if (SpotLight._IsTexture(this._projectionTexture)) {\r\n                this._projectionTexture.onLoadObservable.addOnce(() => {\r\n                    this._markMeshesAsLightDirty();\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    private static _IsProceduralTexture(texture: BaseTexture): texture is ProceduralTexture {\r\n        return (texture as ProceduralTexture).onGeneratedObservable !== undefined;\r\n    }\r\n\r\n    private static _IsTexture(texture: BaseTexture): texture is Texture {\r\n        return (texture as Texture).onLoadObservable !== undefined;\r\n    }\r\n\r\n    private _projectionTextureViewLightDirty = true;\r\n    private _projectionTextureProjectionLightDirty = true;\r\n    private _projectionTextureDirty = true;\r\n    private _projectionTextureViewTargetVector = Vector3.Zero();\r\n    private _projectionTextureViewLightMatrix = Matrix.Zero();\r\n\r\n    private _projectionTextureProjectionLightMatrix = Matrix.Zero();\r\n    /**\r\n     * Gets or sets the light projection matrix as used by the projection texture\r\n     */\r\n    public get projectionTextureProjectionLightMatrix(): Matrix {\r\n        return this._projectionTextureProjectionLightMatrix;\r\n    }\r\n\r\n    public set projectionTextureProjectionLightMatrix(projection: Matrix) {\r\n        this._projectionTextureProjectionLightMatrix = projection;\r\n        this._projectionTextureProjectionLightDirty = false;\r\n        this._projectionTextureDirty = true;\r\n    }\r\n\r\n    private _projectionTextureScalingMatrix = Matrix.FromValues(0.5, 0.0, 0.0, 0.0, 0.0, 0.5, 0.0, 0.0, 0.0, 0.0, 0.5, 0.0, 0.5, 0.5, 0.5, 1.0);\r\n\r\n    /**\r\n     * Creates a SpotLight object in the scene. A spot light is a simply light oriented cone.\r\n     * It can cast shadows.\r\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n     * @param name The light friendly name\r\n     * @param position The position of the spot light in the scene\r\n     * @param direction The direction of the light in the scene\r\n     * @param angle The cone angle of the light in Radians\r\n     * @param exponent The light decay speed with the distance from the emission spot\r\n     * @param scene The scene the lights belongs to\r\n     */\r\n    constructor(name: string, position: Vector3, direction: Vector3, angle: number, exponent: number, scene?: Scene) {\r\n        super(name, scene);\r\n\r\n        this.position = position;\r\n        this.direction = direction;\r\n        this.angle = angle;\r\n        this.exponent = exponent;\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"SpotLight\".\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"SpotLight\";\r\n    }\r\n\r\n    /**\r\n     * Returns the integer 2.\r\n     * @returns The light Type id as a constant defines in Light.LIGHTTYPEID_x\r\n     */\r\n    public getTypeID(): number {\r\n        return Light.LIGHTTYPEID_SPOTLIGHT;\r\n    }\r\n\r\n    /**\r\n     * Overrides the direction setter to recompute the projection texture view light Matrix.\r\n     * @param value\r\n     */\r\n    protected _setDirection(value: Vector3) {\r\n        super._setDirection(value);\r\n        this._projectionTextureViewLightDirty = true;\r\n    }\r\n\r\n    /**\r\n     * Overrides the position setter to recompute the projection texture view light Matrix.\r\n     * @param value\r\n     */\r\n    protected _setPosition(value: Vector3) {\r\n        super._setPosition(value);\r\n        this._projectionTextureViewLightDirty = true;\r\n    }\r\n\r\n    /**\r\n     * Sets the passed matrix \"matrix\" as perspective projection matrix for the shadows and the passed view matrix with the fov equal to the SpotLight angle and and aspect ratio of 1.0.\r\n     * Returns the SpotLight.\r\n     * @param matrix\r\n     * @param viewMatrix\r\n     * @param renderList\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _setDefaultShadowProjectionMatrix(matrix: Matrix, viewMatrix: Matrix, renderList: Array<AbstractMesh>): void {\r\n        const activeCamera = this.getScene().activeCamera;\r\n\r\n        if (!activeCamera) {\r\n            return;\r\n        }\r\n\r\n        this._shadowAngleScale = this._shadowAngleScale || 1;\r\n        const angle = this._shadowAngleScale * this._angle;\r\n\r\n        const minZ = this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera.minZ;\r\n        const maxZ = this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera.maxZ;\r\n\r\n        const useReverseDepthBuffer = this.getScene().getEngine().useReverseDepthBuffer;\r\n\r\n        Matrix.PerspectiveFovLHToRef(\r\n            angle,\r\n            1.0,\r\n            useReverseDepthBuffer ? maxZ : minZ,\r\n            useReverseDepthBuffer ? minZ : maxZ,\r\n            matrix,\r\n            true,\r\n            this._scene.getEngine().isNDCHalfZRange,\r\n            undefined,\r\n            useReverseDepthBuffer\r\n        );\r\n    }\r\n\r\n    protected _computeProjectionTextureViewLightMatrix(): void {\r\n        this._projectionTextureViewLightDirty = false;\r\n        this._projectionTextureDirty = true;\r\n\r\n        this.getAbsolutePosition().addToRef(this.getShadowDirection(), this._projectionTextureViewTargetVector);\r\n        Matrix.LookAtLHToRef(this.getAbsolutePosition(), this._projectionTextureViewTargetVector, this._projectionTextureUpDirection, this._projectionTextureViewLightMatrix);\r\n    }\r\n\r\n    protected _computeProjectionTextureProjectionLightMatrix(): void {\r\n        this._projectionTextureProjectionLightDirty = false;\r\n        this._projectionTextureDirty = true;\r\n\r\n        const lightFar = this.projectionTextureLightFar;\r\n        const lightNear = this.projectionTextureLightNear;\r\n\r\n        const P = lightFar / (lightFar - lightNear);\r\n        const Q = -P * lightNear;\r\n        const S = 1.0 / Math.tan(this._angle / 2.0);\r\n        const A = 1.0;\r\n\r\n        Matrix.FromValuesToRef(S / A, 0.0, 0.0, 0.0, 0.0, S, 0.0, 0.0, 0.0, 0.0, P, 1.0, 0.0, 0.0, Q, 0.0, this._projectionTextureProjectionLightMatrix);\r\n    }\r\n\r\n    /**\r\n     * Main function for light texture projection matrix computing.\r\n     */\r\n    protected _computeProjectionTextureMatrix(): void {\r\n        this._projectionTextureDirty = false;\r\n\r\n        this._projectionTextureViewLightMatrix.multiplyToRef(this._projectionTextureProjectionLightMatrix, this._projectionTextureMatrix);\r\n        if (this._projectionTexture instanceof Texture) {\r\n            const u = this._projectionTexture.uScale / 2.0;\r\n            const v = this._projectionTexture.vScale / 2.0;\r\n            Matrix.FromValuesToRef(u, 0.0, 0.0, 0.0, 0.0, v, 0.0, 0.0, 0.0, 0.0, 0.5, 0.0, 0.5, 0.5, 0.5, 1.0, this._projectionTextureScalingMatrix);\r\n        }\r\n        this._projectionTextureMatrix.multiplyToRef(this._projectionTextureScalingMatrix, this._projectionTextureMatrix);\r\n    }\r\n\r\n    protected _buildUniformLayout(): void {\r\n        this._uniformBuffer.addUniform(\"vLightData\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightDiffuse\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightSpecular\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightDirection\", 3);\r\n        this._uniformBuffer.addUniform(\"vLightFalloff\", 4);\r\n        this._uniformBuffer.addUniform(\"shadowsInfo\", 3);\r\n        this._uniformBuffer.addUniform(\"depthValues\", 2);\r\n        this._uniformBuffer.create();\r\n    }\r\n\r\n    private _computeAngleValues(): void {\r\n        this._lightAngleScale = 1.0 / Math.max(0.001, Math.cos(this._innerAngle * 0.5) - this._cosHalfAngle);\r\n        this._lightAngleOffset = -this._cosHalfAngle * this._lightAngleScale;\r\n    }\r\n\r\n    /**\r\n     * Sets the passed Effect \"effect\" with the Light textures.\r\n     * @param effect The effect to update\r\n     * @param lightIndex The index of the light in the effect to update\r\n     * @returns The light\r\n     */\r\n    public transferTexturesToEffect(effect: Effect, lightIndex: string): Light {\r\n        if (this.projectionTexture && this.projectionTexture.isReady()) {\r\n            if (this._projectionTextureViewLightDirty) {\r\n                this._computeProjectionTextureViewLightMatrix();\r\n            }\r\n            if (this._projectionTextureProjectionLightDirty) {\r\n                this._computeProjectionTextureProjectionLightMatrix();\r\n            }\r\n            if (this._projectionTextureDirty) {\r\n                this._computeProjectionTextureMatrix();\r\n            }\r\n            effect.setMatrix(\"textureProjectionMatrix\" + lightIndex, this._projectionTextureMatrix);\r\n            effect.setTexture(\"projectionLightSampler\" + lightIndex, this.projectionTexture);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the passed Effect object with the SpotLight transformed position (or position if not parented) and normalized direction.\r\n     * @param effect The effect to update\r\n     * @param lightIndex The index of the light in the effect to update\r\n     * @returns The spot light\r\n     */\r\n    public transferToEffect(effect: Effect, lightIndex: string): SpotLight {\r\n        let normalizeDirection;\r\n\r\n        if (this.computeTransformedInformation()) {\r\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.transformedPosition.x, this.transformedPosition.y, this.transformedPosition.z, this.exponent, lightIndex);\r\n\r\n            normalizeDirection = Vector3.Normalize(this.transformedDirection);\r\n        } else {\r\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.position.x, this.position.y, this.position.z, this.exponent, lightIndex);\r\n\r\n            normalizeDirection = Vector3.Normalize(this.direction);\r\n        }\r\n\r\n        this._uniformBuffer.updateFloat4(\"vLightDirection\", normalizeDirection.x, normalizeDirection.y, normalizeDirection.z, this._cosHalfAngle, lightIndex);\r\n\r\n        this._uniformBuffer.updateFloat4(\"vLightFalloff\", this.range, this._inverseSquaredRange, this._lightAngleScale, this._lightAngleOffset, lightIndex);\r\n        return this;\r\n    }\r\n\r\n    public transferToNodeMaterialEffect(effect: Effect, lightDataUniformName: string) {\r\n        let normalizeDirection;\r\n\r\n        if (this.computeTransformedInformation()) {\r\n            normalizeDirection = Vector3.Normalize(this.transformedDirection);\r\n        } else {\r\n            normalizeDirection = Vector3.Normalize(this.direction);\r\n        }\r\n\r\n        if (this.getScene().useRightHandedSystem) {\r\n            effect.setFloat3(lightDataUniformName, -normalizeDirection.x, -normalizeDirection.y, -normalizeDirection.z);\r\n        } else {\r\n            effect.setFloat3(lightDataUniformName, normalizeDirection.x, normalizeDirection.y, normalizeDirection.z);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Disposes the light and the associated resources.\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n        if (this._projectionTexture) {\r\n            this._projectionTexture.dispose();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the minZ used for shadow according to both the scene and the light.\r\n     * @param activeCamera The camera we are returning the min for\r\n     * @returns the depth min z\r\n     */\r\n    public getDepthMinZ(activeCamera: Camera): number {\r\n        const engine = this._scene.getEngine();\r\n        const minZ = this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera.minZ;\r\n\r\n        return engine.useReverseDepthBuffer && engine.isNDCHalfZRange ? minZ : this._scene.getEngine().isNDCHalfZRange ? 0 : minZ;\r\n    }\r\n\r\n    /**\r\n     * Gets the maxZ used for shadow according to both the scene and the light.\r\n     * @param activeCamera The camera we are returning the max for\r\n     * @returns the depth max z\r\n     */\r\n    public getDepthMaxZ(activeCamera: Camera): number {\r\n        const engine = this._scene.getEngine();\r\n        const maxZ = this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera.maxZ;\r\n\r\n        return engine.useReverseDepthBuffer && engine.isNDCHalfZRange ? 0 : maxZ;\r\n    }\r\n\r\n    /**\r\n     * Prepares the list of defines specific to the light type.\r\n     * @param defines the list of defines\r\n     * @param lightIndex defines the index of the light for the effect\r\n     */\r\n    public prepareLightSpecificDefines(defines: any, lightIndex: number): void {\r\n        defines[\"SPOTLIGHT\" + lightIndex] = true;\r\n        defines[\"PROJECTEDLIGHTTEXTURE\" + lightIndex] = this.projectionTexture && this.projectionTexture.isReady() ? true : false;\r\n    }\r\n}\r\n"]}