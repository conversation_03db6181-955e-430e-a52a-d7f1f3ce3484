{"version": 3, "file": "nodeGeometryBuildState.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Node/nodeGeometryBuildState.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,6BAA6B,EAAE,MAAM,uCAAuC,CAAC;AACtF,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAE5E,OAAO,EAAE,qCAAqC,EAAE,MAAM,0CAA0C,CAAC;AAGjG;;GAEG;AACH,MAAM,OAAO,sBAAsB;IAAnC;QACY,oBAAe,GAAG,IAAI,MAAM,EAAE,CAAC;QAC/B,mBAAc,GAAG,IAAI,MAAM,EAAE,CAAC;QAC9B,oBAAe,GAAG,IAAI,MAAM,EAAE,CAAC;QAC/B,2BAAsB,GAAG,IAAI,MAAM,EAAE,CAAC;QACtC,qBAAgB,GAAG,IAAI,MAAM,EAAE,CAAC;QAChC,iBAAY,GAAG,IAAI,OAAO,EAAE,CAAC;QAErC,8DAA8D;QACvD,kCAA6B,GAAkC,EAAE,CAAC;QACzE,gFAAgF;QACzE,qBAAgB,GAAoC,EAAE,CAAC;QAK9D,mCAAmC;QAC5B,eAAU,GAAyB,IAAI,CAAC;QAEvC,qBAAgB,GAAyB,IAAI,CAAC;QAC9C,sBAAiB,GAA4C,IAAI,CAAC;QAClE,uBAAkB,GAA6C,IAAI,CAAC;QAEpE,0BAAqB,GAAgC,EAAE,CAAC;QACxD,2BAAsB,GAAmD,EAAE,CAAC;QAC5E,4BAAuB,GAAoD,EAAE,CAAC;IA8S1F,CAAC;IA5SG,wCAAwC;IACxC,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,yCAAyC;IACzC,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED,0CAA0C;IAC1C,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,mBAAmB,CAAC,eAA2B;QAClD,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC3D,CAAC;IAED;;;OAGG;IACI,oBAAoB,CAAC,gBAA+C;QACvE,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC7D,CAAC;IAED;;;OAGG;IACI,qBAAqB,CAAC,iBAAiD;QAC1E,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACI,sBAAsB;QACzB,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7I,CAAC;IAED;;OAEG;IACI,uBAAuB;QAC1B,IAAI,CAAC,sBAAsB,CAAC,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACjJ,CAAC;IAED;;OAEG;IACI,wBAAwB;QAC3B,IAAI,CAAC,uBAAuB,CAAC,GAAG,EAAE,CAAC;QACnC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrJ,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,MAAqC,EAAE,WAAW,GAAG,KAAK;QAChF,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,IAAI,CAAC,WAAW,EAAE;gBACd,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACtC;YACD,OAAO,IAAI,CAAC;SACf;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;QAExD,QAAQ,MAAM,EAAE;YACZ,KAAK,6BAA6B,CAAC,SAAS;gBACxC,IAAI,IAAI,CAAC,gBAAgB,CAAC,mCAAmC,EAAE;oBAC3D,OAAO,IAAI,CAAC,gBAAgB,CAAC,mCAAmC,EAAE,CAAC;iBACtE;gBACD,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;oBAC1D,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,SAA8B,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAC7F,KAAK,6BAA6B,CAAC,OAAO;gBACtC,IAAI,IAAI,CAAC,gBAAgB,CAAC,iCAAiC,EAAE;oBACzD,OAAO,IAAI,CAAC,gBAAgB,CAAC,iCAAiC,EAAE,CAAC;iBACpE;gBACD,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE;oBACxD,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,OAA4B,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAC3F,KAAK,6BAA6B,CAAC,MAAM;gBACrC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;oBACvD,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,MAA2B,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAC1F,KAAK,6BAA6B,CAAC,QAAQ;gBACvC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;oBACzD,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,QAA6B,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAC5F,KAAK,6BAA6B,CAAC,EAAE;gBACjC,IAAI,IAAI,CAAC,gBAAgB,CAAC,8BAA8B,EAAE;oBACtD,OAAO,IAAI,CAAC,gBAAgB,CAAC,8BAA8B,EAAE,CAAC;iBACjE;gBACD,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE;oBACpD,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,GAAwB,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YACvF,KAAK,6BAA6B,CAAC,GAAG;gBAClC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;oBACrD,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,IAAyB,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YACxF,KAAK,6BAA6B,CAAC,GAAG;gBAClC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;oBACrD,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,IAAyB,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YACxF,KAAK,6BAA6B,CAAC,GAAG;gBAClC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;oBACrD,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,IAAyB,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YACxF,KAAK,6BAA6B,CAAC,GAAG;gBAClC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;oBACrD,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,IAAyB,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YACxF,KAAK,6BAA6B,CAAC,GAAG;gBAClC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;oBACrD,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;iBACzB;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,IAAyB,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YACxF,KAAK,6BAA6B,CAAC,QAAQ;gBACvC,OAAO,KAAK,CAAC;YACjB,KAAK,6BAA6B,CAAC,MAAM;gBACrC,OAAO,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;YACzD,KAAK,6BAA6B,CAAC,MAAM;gBACrC,OAAO,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;YACzD,KAAK,6BAA6B,CAAC,UAAU;gBACzC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClF,KAAK,6BAA6B,CAAC,UAAU;gBACzC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;YACrE,KAAK,6BAA6B,CAAC,YAAY,CAAC,CAAC;gBAC7C,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;oBACzD,OAAO,CAAC,CAAC;iBACZ;gBACD,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,CAAC;aAC1D;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,MAAmC,EAAE,UAAiD;QACxF,MAAM,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElD,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE;YAC5B,OAAO,KAAK,CAAC;SAChB;QAED,QAAQ,UAAU,EAAE;YAChB,KAAK,qCAAqC,CAAC,OAAO;gBAC9C,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACrC,KAAK,qCAAqC,CAAC,OAAO;gBAC9C,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAC5C,KAAK,qCAAqC,CAAC,OAAO;gBAC9C,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SACtD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACH,UAAU,CAAC,MAAmC,EAAE,UAAiD,EAAE,YAAiB;QAChH,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACrB,OAAO,MAAM,CAAC,KAAK,IAAI,YAAY,CAAC;SACvC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,MAAM,CAAC,eAAe,EAAE,IAAI,KAAK,UAAU,EAAE;YAC7C,OAAO,KAAK,CAAC;SAChB;QAED,QAAQ,UAAU,EAAE;YAChB,KAAK,qCAAqC,CAAC,OAAO;gBAC9C,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACrC,KAAK,qCAAqC,CAAC,OAAO;gBAC9C,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAC5C,KAAK,qCAAqC,CAAC,OAAO;gBAC9C,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SACtD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,UAAU;QACb,IAAI,YAAY,GAAG,EAAE,CAAC;QAEtB,KAAK,MAAM,iBAAiB,IAAI,IAAI,CAAC,6BAA6B,EAAE;YAChE,YAAY,IAAI,SAAS,iBAAiB,CAAC,IAAI,eAC3C,iBAAiB,CAAC,UAAU,CAAC,IACjC,IAAI,iBAAiB,CAAC,UAAU,CAAC,YAAY,EAAE,2CAA2C,CAAC;SAC9F;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACxC,YAAY,IAAI,oBAAoB,6BAA6B,CAAC,MAAM,CAAC,sGAAsG,CAAC;SACnL;QAED,IAAI,YAAY,EAAE;YACd,4CAA4C;YAC5C,MAAM,iCAAiC,GAAG,YAAY,CAAC;SAC1D;IACL,CAAC;IAED,iBAAiB;IACV,YAAY,CAAC,KAAiB,EAAE,eAAwB,EAAE,QAAiB,EAAE,OAAgB,EAAE,oBAAkC;QACpI,YAAY;QACZ,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1E,MAAM,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3F,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAEvG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACrF,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACvF,KAAK,IAAI,kBAAkB,GAAG,CAAC,EAAE,kBAAkB,GAAG,KAAK,CAAC,SAAU,CAAC,MAAM,EAAE,kBAAkB,IAAI,CAAC,EAAE;YACpG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,SAAU,EAAE,kBAAkB,CAAC,CAAC;YAClE,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAC/F,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,SAAU,EAAE,kBAAkB,CAAC,CAAC;YAEhE,IAAI,KAAK,CAAC,OAAO,EAAE;gBACf,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;gBAC/D,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBAChG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;aAChE;SACJ;QAED,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,iBAAiB;IACV,sBAAsB,CAAC,KAAiB,EAAE,SAAiB,EAAE,oBAAkC;QAClG,KAAK,IAAI,kBAAkB,GAAG,CAAC,EAAE,kBAAkB,GAAG,KAAK,CAAC,SAAU,CAAC,MAAM,EAAE,kBAAkB,IAAI,CAAC,EAAE;YACpG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,SAAU,EAAE,kBAAkB,CAAC,CAAC;YAClE,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YACnF,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,SAAU,EAAE,kBAAkB,CAAC,CAAC;YAEhE,IAAI,KAAK,CAAC,OAAO,EAAE;gBACf,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;gBAC/D,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC9E,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;aAChE;SACJ;QAED,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,iBAAiB;IACV,iCAAiC,CAAC,KAAiB,EAAE,eAAwB,EAAE,SAAiB,EAAE,oBAAkC;QACvI,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACvG,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAErE,KAAK,IAAI,kBAAkB,GAAG,CAAC,EAAE,kBAAkB,GAAG,KAAK,CAAC,SAAU,CAAC,MAAM,EAAE,kBAAkB,IAAI,CAAC,EAAE;YACpG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,SAAU,EAAE,kBAAkB,CAAC,CAAC;YAClE,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAC/F,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,SAAU,EAAE,kBAAkB,CAAC,CAAC;YAEhE,IAAI,KAAK,CAAC,OAAO,EAAE;gBACf,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;gBAC/D,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC1F,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;aAChE;SACJ;QAED,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { VertexData } from \"../mesh.vertexData\";\r\nimport type { NodeGeometryConnectionPoint } from \"./nodeGeometryBlockConnectionPoint\";\r\nimport { NodeGeometryContextualSources } from \"./Enums/nodeGeometryContextualSources\";\r\nimport { Matrix, Vector2, Vector3, Vector4 } from \"../../Maths/math.vector\";\r\nimport type { INodeGeometryExecutionContext } from \"./Interfaces/nodeGeometryExecutionContext\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"./Enums/nodeGeometryConnectionPointTypes\";\r\nimport type { INodeGeometryInstancingContext } from \"./Interfaces/nodeGeometryInstancingContext\";\r\n\r\n/**\r\n * Class used to store node based geometry build state\r\n */\r\nexport class NodeGeometryBuildState {\r\n    private _rotationMatrix = new Matrix();\r\n    private _scalingMatrix = new Matrix();\r\n    private _positionMatrix = new Matrix();\r\n    private _scalingRotationMatrix = new Matrix();\r\n    private _transformMatrix = new Matrix();\r\n    private _tempVector3 = new Vector3();\r\n\r\n    /** Gets or sets the list of non connected mandatory inputs */\r\n    public notConnectedNonOptionalInputs: NodeGeometryConnectionPoint[] = [];\r\n    /** Gets or sets the list of non contextual inputs having no contextudal data */\r\n    public noContextualData: NodeGeometryContextualSources[] = [];\r\n    /** Gets or sets the build identifier */\r\n    public buildId: number;\r\n    /** Gets or sets a boolean indicating that verbose mode is on */\r\n    public verbose: boolean;\r\n    /** Gets or sets the vertex data */\r\n    public vertexData: Nullable<VertexData> = null;\r\n\r\n    private _geometryContext: Nullable<VertexData> = null;\r\n    private _executionContext: Nullable<INodeGeometryExecutionContext> = null;\r\n    private _instancingContext: Nullable<INodeGeometryInstancingContext> = null;\r\n\r\n    private _geometryContextStack: Array<Nullable<VertexData>> = [];\r\n    private _executionContextStack: Array<Nullable<INodeGeometryExecutionContext>> = [];\r\n    private _instancingContextStack: Array<Nullable<INodeGeometryInstancingContext>> = [];\r\n\r\n    /** Gets or sets the geometry context */\r\n    public get geometryContext() {\r\n        return this._geometryContext;\r\n    }\r\n\r\n    /** Gets or sets the execution context */\r\n    public get executionContext() {\r\n        return this._executionContext;\r\n    }\r\n\r\n    /** Gets or sets the instancing context */\r\n    public get instancingContext() {\r\n        return this._instancingContext;\r\n    }\r\n\r\n    /**\r\n     * Push the new active geometry context\r\n     * @param geometryContext defines the geometry context\r\n     */\r\n    public pushGeometryContext(geometryContext: VertexData) {\r\n        this._geometryContext = geometryContext;\r\n        this._geometryContextStack.push(this._geometryContext);\r\n    }\r\n\r\n    /**\r\n     * Push the new active execution context\r\n     * @param executionContext defines the execution context\r\n     */\r\n    public pushExecutionContext(executionContext: INodeGeometryExecutionContext) {\r\n        this._executionContext = executionContext;\r\n        this._executionContextStack.push(this._executionContext);\r\n    }\r\n\r\n    /**\r\n     * Push the new active instancing context\r\n     * @param instancingContext defines the instancing context\r\n     */\r\n    public pushInstancingContext(instancingContext: INodeGeometryInstancingContext) {\r\n        this._instancingContext = instancingContext;\r\n        this._instancingContextStack.push(this._instancingContext);\r\n    }\r\n\r\n    /**\r\n     * Remove current geometry context and restore the previous one\r\n     */\r\n    public restoreGeometryContext() {\r\n        this._geometryContextStack.pop();\r\n        this._geometryContext = this._geometryContextStack.length > 0 ? this._geometryContextStack[this._geometryContextStack.length - 1] : null;\r\n    }\r\n\r\n    /**\r\n     * Remove current execution context and restore the previous one\r\n     */\r\n    public restoreExecutionContext() {\r\n        this._executionContextStack.pop();\r\n        this._executionContext = this._executionContextStack.length > 0 ? this._executionContextStack[this._executionContextStack.length - 1] : null;\r\n    }\r\n\r\n    /**\r\n     * Remove current isntancing context and restore the previous one\r\n     */\r\n    public restoreInstancingContext() {\r\n        this._instancingContextStack.pop();\r\n        this._instancingContext = this._instancingContextStack.length > 0 ? this._instancingContextStack[this._instancingContextStack.length - 1] : null;\r\n    }\r\n\r\n    /**\r\n     * Gets the value associated with a contextual source\r\n     * @param source Source of the contextual value\r\n     * @param skipWarning Do not store the warning for reporting if true\r\n     * @returns the value associated with the source\r\n     */\r\n    public getContextualValue(source: NodeGeometryContextualSources, skipWarning = false) {\r\n        if (!this.executionContext) {\r\n            if (!skipWarning) {\r\n                this.noContextualData.push(source);\r\n            }\r\n            return null;\r\n        }\r\n\r\n        const index = this.executionContext.getExecutionIndex();\r\n\r\n        switch (source) {\r\n            case NodeGeometryContextualSources.Positions:\r\n                if (this.executionContext.getOverridePositionsContextualValue) {\r\n                    return this.executionContext.getOverridePositionsContextualValue();\r\n                }\r\n                if (!this.geometryContext || !this.geometryContext.positions) {\r\n                    return Vector3.Zero();\r\n                }\r\n                return Vector3.FromArray(this.geometryContext.positions as ArrayLike<number>, index * 3);\r\n            case NodeGeometryContextualSources.Normals:\r\n                if (this.executionContext.getOverrideNormalsContextualValue) {\r\n                    return this.executionContext.getOverrideNormalsContextualValue();\r\n                }\r\n                if (!this.geometryContext || !this.geometryContext.normals) {\r\n                    return Vector3.Zero();\r\n                }\r\n                return Vector3.FromArray(this.geometryContext.normals as ArrayLike<number>, index * 3);\r\n            case NodeGeometryContextualSources.Colors:\r\n                if (!this.geometryContext || !this.geometryContext.colors) {\r\n                    return Vector4.Zero();\r\n                }\r\n                return Vector4.FromArray(this.geometryContext.colors as ArrayLike<number>, index * 4);\r\n            case NodeGeometryContextualSources.Tangents:\r\n                if (!this.geometryContext || !this.geometryContext.tangents) {\r\n                    return Vector4.Zero();\r\n                }\r\n                return Vector4.FromArray(this.geometryContext.tangents as ArrayLike<number>, index * 4);\r\n            case NodeGeometryContextualSources.UV:\r\n                if (this.executionContext.getOverrideUVs1ContextualValue) {\r\n                    return this.executionContext.getOverrideUVs1ContextualValue();\r\n                }\r\n                if (!this.geometryContext || !this.geometryContext.uvs) {\r\n                    return Vector2.Zero();\r\n                }\r\n                return Vector2.FromArray(this.geometryContext.uvs as ArrayLike<number>, index * 2);\r\n            case NodeGeometryContextualSources.UV2:\r\n                if (!this.geometryContext || !this.geometryContext.uvs2) {\r\n                    return Vector2.Zero();\r\n                }\r\n                return Vector2.FromArray(this.geometryContext.uvs2 as ArrayLike<number>, index * 2);\r\n            case NodeGeometryContextualSources.UV3:\r\n                if (!this.geometryContext || !this.geometryContext.uvs3) {\r\n                    return Vector2.Zero();\r\n                }\r\n                return Vector2.FromArray(this.geometryContext.uvs3 as ArrayLike<number>, index * 2);\r\n            case NodeGeometryContextualSources.UV4:\r\n                if (!this.geometryContext || !this.geometryContext.uvs4) {\r\n                    return Vector2.Zero();\r\n                }\r\n                return Vector2.FromArray(this.geometryContext.uvs4 as ArrayLike<number>, index * 2);\r\n            case NodeGeometryContextualSources.UV5:\r\n                if (!this.geometryContext || !this.geometryContext.uvs5) {\r\n                    return Vector2.Zero();\r\n                }\r\n                return Vector2.FromArray(this.geometryContext.uvs5 as ArrayLike<number>, index * 2);\r\n            case NodeGeometryContextualSources.UV6:\r\n                if (!this.geometryContext || !this.geometryContext.uvs6) {\r\n                    return Vector2.Zero();\r\n                }\r\n                return Vector2.FromArray(this.geometryContext.uvs6 as ArrayLike<number>, index * 2);\r\n            case NodeGeometryContextualSources.VertexID:\r\n                return index;\r\n            case NodeGeometryContextualSources.FaceID:\r\n                return this.executionContext.getExecutionFaceIndex();\r\n            case NodeGeometryContextualSources.LoopID:\r\n                return this.executionContext.getExecutionLoopIndex();\r\n            case NodeGeometryContextualSources.InstanceID:\r\n                return this.instancingContext ? this.instancingContext.getInstanceIndex() : 0;\r\n            case NodeGeometryContextualSources.GeometryID:\r\n                return !this.geometryContext ? 0 : this.geometryContext.uniqueId;\r\n            case NodeGeometryContextualSources.CollectionID: {\r\n                if (!this.geometryContext || !this.geometryContext.metadata) {\r\n                    return 0;\r\n                }\r\n                return this.geometryContext.metadata.collectionId || 0;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Adapt a value to a target type\r\n     * @param source defines the value to adapt\r\n     * @param targetType defines the target type\r\n     * @returns the adapted value\r\n     */\r\n    adapt(source: NodeGeometryConnectionPoint, targetType: NodeGeometryBlockConnectionPointTypes) {\r\n        const value = source.getConnectedValue(this) || 0;\r\n\r\n        if (source.type === targetType) {\r\n            return value;\r\n        }\r\n\r\n        switch (targetType) {\r\n            case NodeGeometryBlockConnectionPointTypes.Vector2:\r\n                return new Vector2(value, value);\r\n            case NodeGeometryBlockConnectionPointTypes.Vector3:\r\n                return new Vector3(value, value, value);\r\n            case NodeGeometryBlockConnectionPointTypes.Vector4:\r\n                return new Vector4(value, value, value, value);\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Adapt an input value to a target type\r\n     * @param source defines the value to adapt\r\n     * @param targetType defines the target type\r\n     * @param defaultValue defines the default value to use if not connected\r\n     * @returns the adapted value\r\n     */\r\n    adaptInput(source: NodeGeometryConnectionPoint, targetType: NodeGeometryBlockConnectionPointTypes, defaultValue: any) {\r\n        if (!source.isConnected) {\r\n            return source.value || defaultValue;\r\n        }\r\n\r\n        const value = source.getConnectedValue(this);\r\n\r\n        if (source._connectedPoint?.type === targetType) {\r\n            return value;\r\n        }\r\n\r\n        switch (targetType) {\r\n            case NodeGeometryBlockConnectionPointTypes.Vector2:\r\n                return new Vector2(value, value);\r\n            case NodeGeometryBlockConnectionPointTypes.Vector3:\r\n                return new Vector3(value, value, value);\r\n            case NodeGeometryBlockConnectionPointTypes.Vector4:\r\n                return new Vector4(value, value, value, value);\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Emits console errors and exceptions if there is a failing check\r\n     */\r\n    public emitErrors() {\r\n        let errorMessage = \"\";\r\n\r\n        for (const notConnectedInput of this.notConnectedNonOptionalInputs) {\r\n            errorMessage += `input ${notConnectedInput.name} from block ${\r\n                notConnectedInput.ownerBlock.name\r\n            }[${notConnectedInput.ownerBlock.getClassName()}] is not connected and is not optional.\\n`;\r\n        }\r\n\r\n        for (const source of this.noContextualData) {\r\n            errorMessage += `Contextual input ${NodeGeometryContextualSources[source]} has no context to pull data from (must be connected to a setXXX block or a instantiateXXX block).\\n`;\r\n        }\r\n\r\n        if (errorMessage) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"Build of NodeGeometry failed:\\n\" + errorMessage;\r\n        }\r\n    }\r\n\r\n    /** @internal  */\r\n    public _instantiate(clone: VertexData, currentPosition: Vector3, rotation: Vector3, scaling: Vector3, additionalVertexData: VertexData[]) {\r\n        // Transform\r\n        Matrix.ScalingToRef(scaling.x, scaling.y, scaling.z, this._scalingMatrix);\r\n        Matrix.RotationYawPitchRollToRef(rotation.y, rotation.x, rotation.z, this._rotationMatrix);\r\n        Matrix.TranslationToRef(currentPosition.x, currentPosition.y, currentPosition.z, this._positionMatrix);\r\n\r\n        this._scalingMatrix.multiplyToRef(this._rotationMatrix, this._scalingRotationMatrix);\r\n        this._scalingRotationMatrix.multiplyToRef(this._positionMatrix, this._transformMatrix);\r\n        for (let clonePositionIndex = 0; clonePositionIndex < clone.positions!.length; clonePositionIndex += 3) {\r\n            this._tempVector3.fromArray(clone.positions!, clonePositionIndex);\r\n            Vector3.TransformCoordinatesToRef(this._tempVector3, this._transformMatrix, this._tempVector3);\r\n            this._tempVector3.toArray(clone.positions!, clonePositionIndex);\r\n\r\n            if (clone.normals) {\r\n                this._tempVector3.fromArray(clone.normals, clonePositionIndex);\r\n                Vector3.TransformNormalToRef(this._tempVector3, this._scalingRotationMatrix, this._tempVector3);\r\n                this._tempVector3.toArray(clone.normals, clonePositionIndex);\r\n            }\r\n        }\r\n\r\n        additionalVertexData.push(clone);\r\n    }\r\n\r\n    /** @internal  */\r\n    public _instantiateWithMatrix(clone: VertexData, transform: Matrix, additionalVertexData: VertexData[]) {\r\n        for (let clonePositionIndex = 0; clonePositionIndex < clone.positions!.length; clonePositionIndex += 3) {\r\n            this._tempVector3.fromArray(clone.positions!, clonePositionIndex);\r\n            Vector3.TransformCoordinatesToRef(this._tempVector3, transform, this._tempVector3);\r\n            this._tempVector3.toArray(clone.positions!, clonePositionIndex);\r\n\r\n            if (clone.normals) {\r\n                this._tempVector3.fromArray(clone.normals, clonePositionIndex);\r\n                Vector3.TransformNormalToRef(this._tempVector3, transform, this._tempVector3);\r\n                this._tempVector3.toArray(clone.normals, clonePositionIndex);\r\n            }\r\n        }\r\n\r\n        additionalVertexData.push(clone);\r\n    }\r\n\r\n    /** @internal  */\r\n    public _instantiateWithPositionAndMatrix(clone: VertexData, currentPosition: Vector3, transform: Matrix, additionalVertexData: VertexData[]) {\r\n        Matrix.TranslationToRef(currentPosition.x, currentPosition.y, currentPosition.z, this._positionMatrix);\r\n        transform.multiplyToRef(this._positionMatrix, this._transformMatrix);\r\n\r\n        for (let clonePositionIndex = 0; clonePositionIndex < clone.positions!.length; clonePositionIndex += 3) {\r\n            this._tempVector3.fromArray(clone.positions!, clonePositionIndex);\r\n            Vector3.TransformCoordinatesToRef(this._tempVector3, this._transformMatrix, this._tempVector3);\r\n            this._tempVector3.toArray(clone.positions!, clonePositionIndex);\r\n\r\n            if (clone.normals) {\r\n                this._tempVector3.fromArray(clone.normals, clonePositionIndex);\r\n                Vector3.TransformNormalToRef(this._tempVector3, this._transformMatrix, this._tempVector3);\r\n                this._tempVector3.toArray(clone.normals, clonePositionIndex);\r\n            }\r\n        }\r\n\r\n        additionalVertexData.push(clone);\r\n    }\r\n}\r\n"]}