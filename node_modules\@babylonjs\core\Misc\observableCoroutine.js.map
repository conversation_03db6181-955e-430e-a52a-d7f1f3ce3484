{"version": 3, "file": "observableCoroutine.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/observableCoroutine.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,EAAE,iBAAiB,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAEjE,SAAS,yBAAyB,CAAI,UAA2B;IAC7D,MAAM,UAAU,GAAG,IAAI,KAAK,EAAqB,CAAC;IAClD,MAAM,OAAO,GAAG,IAAI,KAAK,EAA0C,CAAC;IACpE,MAAM,QAAQ,GAAG,IAAI,KAAK,EAA4B,CAAC;IAEvD,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE;QACjC,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC5B,eAAe,CAAC,UAAU,CAAC,KAAK,EAAG,EAAE,OAAO,CAAC,KAAK,EAAG,EAAE,QAAQ,CAAC,KAAK,EAAG,CAAC,CAAC;SAC7E;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,SAAS,GAAG,CAAC,SAA4B,EAAE,MAA8C,EAAE,OAAiC,EAAE,EAAE;QAClI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC,CAAC;IAEF,OAAO;QACH,SAAS,EAAE,SAAS;QACpB,OAAO,EAAE,GAAG,EAAE;YACV,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAChC,CAAC;KACJ,CAAC;AACN,CAAC;AA6BD,UAAU,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,SAA+B;IAC9E,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;QAC3B,MAAM,mBAAmB,GAAG,yBAAyB,CAAO,IAAI,CAAC,CAAC;QAClE,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC;QACzD,IAAI,CAAC,0BAA0B,GAAG,mBAAmB,CAAC,OAAO,CAAC;KACjE;IAED,OAAO,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAClE,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG;IACvC,IAAI,IAAI,CAAC,0BAA0B,EAAE;QACjC,IAAI,CAAC,0BAA0B,EAAE,CAAC;KACrC;IACD,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;IACrC,IAAI,CAAC,0BAA0B,GAAG,SAAS,CAAC;AAChD,CAAC,CAAC", "sourcesContent": ["import { Observable } from \"./observable\";\r\nimport type { AsyncCoroutine, CoroutineStep, CoroutineScheduler } from \"./coroutine\";\r\nimport { runCoroutineAsync, inlineScheduler } from \"./coroutine\";\r\n\r\nfunction CreateObservableScheduler<T>(observable: Observable<any>): { scheduler: CoroutineScheduler<T>; dispose: () => void } {\r\n    const coroutines = new Array<AsyncCoroutine<T>>();\r\n    const onSteps = new Array<(stepResult: CoroutineStep<T>) => void>();\r\n    const onErrors = new Array<(stepError: any) => void>();\r\n\r\n    const observer = observable.add(() => {\r\n        const count = coroutines.length;\r\n        for (let i = 0; i < count; i++) {\r\n            inlineScheduler(coroutines.shift()!, onSteps.shift()!, onErrors.shift()!);\r\n        }\r\n    });\r\n\r\n    const scheduler = (coroutine: AsyncCoroutine<T>, onStep: (stepResult: CoroutineStep<T>) => void, onError: (stepError: any) => void) => {\r\n        coroutines.push(coroutine);\r\n        onSteps.push(onStep);\r\n        onErrors.push(onError);\r\n    };\r\n\r\n    return {\r\n        scheduler: scheduler,\r\n        dispose: () => {\r\n            observable.remove(observer);\r\n        },\r\n    };\r\n}\r\n\r\ndeclare module \"./observable\" {\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    export interface Observable<T> {\r\n        /**\r\n         * Internal observable-based coroutine scheduler instance.\r\n         */\r\n        _coroutineScheduler?: CoroutineScheduler<void>;\r\n\r\n        /**\r\n         * Internal disposal method for observable-based coroutine scheduler instance.\r\n         */\r\n        _coroutineSchedulerDispose?: () => void;\r\n\r\n        /**\r\n         * Runs a coroutine asynchronously on this observable\r\n         * @param coroutine the iterator resulting from having started the coroutine\r\n         * @returns a promise which will be resolved when the coroutine finishes or rejected if the coroutine is cancelled\r\n         */\r\n        runCoroutineAsync(coroutine: AsyncCoroutine<void>): Promise<void>;\r\n\r\n        /**\r\n         * Cancels all coroutines currently running on this observable\r\n         */\r\n        cancelAllCoroutines(): void;\r\n    }\r\n}\r\n\r\nObservable.prototype.runCoroutineAsync = function (coroutine: AsyncCoroutine<void>) {\r\n    if (!this._coroutineScheduler) {\r\n        const schedulerAndDispose = CreateObservableScheduler<void>(this);\r\n        this._coroutineScheduler = schedulerAndDispose.scheduler;\r\n        this._coroutineSchedulerDispose = schedulerAndDispose.dispose;\r\n    }\r\n\r\n    return runCoroutineAsync(coroutine, this._coroutineScheduler);\r\n};\r\n\r\nObservable.prototype.cancelAllCoroutines = function () {\r\n    if (this._coroutineSchedulerDispose) {\r\n        this._coroutineSchedulerDispose();\r\n    }\r\n    this._coroutineScheduler = undefined;\r\n    this._coroutineSchedulerDispose = undefined;\r\n};\r\n"]}