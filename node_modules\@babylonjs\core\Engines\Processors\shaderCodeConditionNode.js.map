{"version": 3, "file": "shaderCodeConditionNode.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Processors/shaderCodeConditionNode.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAGlD,gBAAgB;AAChB,MAAM,OAAO,uBAAwB,SAAQ,cAAc;IACvD,OAAO,CAAC,aAAwC,EAAE,OAA0B;QACxE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACvD,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAElC,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;gBAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;aAC/C;SACJ;QAED,OAAO,EAAE,CAAC;IACd,CAAC;CACJ", "sourcesContent": ["import { ShaderCodeNode } from \"./shaderCodeNode\";\r\nimport type { ProcessingOptions } from \"./shaderProcessingOptions\";\r\n\r\n/** @internal */\r\nexport class ShaderCodeConditionNode extends ShaderCodeNode {\r\n    process(preprocessors: { [key: string]: string }, options: ProcessingOptions) {\r\n        for (let index = 0; index < this.children.length; index++) {\r\n            const node = this.children[index];\r\n\r\n            if (node.isValid(preprocessors)) {\r\n                return node.process(preprocessors, options);\r\n            }\r\n        }\r\n\r\n        return \"\";\r\n    }\r\n}\r\n"]}