{"version": 3, "file": "targetCamera.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Cameras/targetCamera.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,MAAM,oBAAoB,CAAC;AAE7F,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AACxF,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACpD,OAAO,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,OAAO,YAAa,SAAQ,MAAM;IAgGpC;;;;;;;;OAQG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa,EAAE,4BAA4B,GAAG,IAAI;QAC3F,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,4BAA4B,CAAC,CAAC;QArGvD,iBAAY,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9B,qBAAgB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE1C;;WAEG;QACI,oBAAe,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C;;WAEG;QACI,mBAAc,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1C,4HAA4H;QACrH,wBAAmB,GAAG,KAAK,CAAC;QAEnC;;WAEG;QACI,+BAA0B,GAAG,KAAK,CAAC;QAClC,mBAAc,GAAG,IAAI,UAAU,EAAE,CAAC;QAE1C;;WAEG;QAEI,aAAQ,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAOvC;;WAEG;QAEI,UAAK,GAAG,GAAG,CAAC;QAEnB;;;WAGG;QACI,yBAAoB,GAAG,KAAK,CAAC;QAEpC;;;WAGG;QACI,mBAAc,GAAG,KAAK,CAAC;QAE9B;;WAEG;QACI,yBAAoB,GAAG,GAAG,CAAC;QAElC;;;WAGG;QAEI,iBAAY,GAAQ,IAAI,CAAC;QAEhC,gBAAgB;QACT,mBAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACvC,gBAAgB;QACT,0BAAqB,GAAG,CAAC,CAAC;QACjC,gBAAgB;QACT,gBAAW,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QACnC,gBAAgB;QACT,eAAU,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAClC,gBAAgB;QACT,2BAAsB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAC9C,gBAAgB;QACT,0BAAqB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAE7C,gBAAgB;QACT,oBAAe,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,gBAAgB;QACT,+BAA0B,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEzC,4BAAuB,GAAG,IAAI,OAAO,EAAE,CAAC;QACxC,sCAAiC,GAAG,IAAI,UAAU,EAAE,CAAC;QACrD,4BAAuB,GAAG,IAAI,OAAO,EAAE,CAAC;QACxC,qBAAgB,GAAG,KAAK,CAAC;QACzB,eAAU,GAAY,KAAK,CAAC;QAK9B,eAAU,GAAG,OAAO,CAAC,EAAE,EAAE,CAAC;QA0V1B,qBAAgB,GAAG,CAAC,CAAC;QACrB,+BAA0B,GAAG,CAAC,CAAC;IA9UvC,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,QAAgB;QACpC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3D,SAAS,CAAC,SAAS,EAAE,CAAC;QACtB,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAC9C,CAAC;IAED,gBAAgB;IACT,wBAAwB;QAC3B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;YACpC,MAAM,YAAY,GAAG,IAAI,CAAC,YAA4B,CAAC;YACvD,MAAM,CAAC,GAAG,YAAY,CAAC,kBAAkB,EAAE,CAAC;YAC5C,wGAAwG;YACxG,CAAC,CAAC,mBAAmB,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;SACxD;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,IAAI,IAAI,CAAC,YAAY,CAAC;IACnE,CAAC;IAMD;;;OAGG;IACI,UAAU;QACb,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC7C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC7C,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;SACpE;QAED,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACI,mBAAmB;QACtB,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE;YAC9B,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAE7C,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;SACpE;QAED,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gBAAgB;IACT,UAAU;QACb,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC7F,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACzF,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;IAC5H,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,iBAA2B;QAC3C,IAAI,CAAC,iBAAiB,EAAE;YACpB,KAAK,CAAC,YAAY,EAAE,CAAC;SACxB;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC7D,IAAI,CAAC,oBAAoB,EAAE;YACvB,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;SACnC;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,oBAAoB,CAAC,KAAK,EAAE,CAAC;aAC3D;iBAAM;gBACH,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;aAC3D;SACJ;QAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACpE;IACL,CAAC;IAED,eAAe;IACf,gBAAgB;IACT,yBAAyB;QAC5B,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAE7D,OAAO,CACH,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC;YAC1G,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAC1I,CAAC;IACN,CAAC;IAED,UAAU;IACV,gBAAgB;IACT,wBAAwB;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;IACrF,CAAC;IAED,SAAS;IAET;;;OAGG;IACI,SAAS,CAAC,MAAe;QAC5B,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;QAE1B,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;QAErE,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE;YAC9B,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,OAAO,CAAC;SAC9B;QAED,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAE1E,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9E,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QAEzB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1E,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE5C,IAAI,IAAI,CAAC,CAAC,IAAI,GAAG,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;SACjE;aAAM;YACH,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;SACjE;QAED,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAEpB,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YACxB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;SACvB;QAED,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YACxB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;SACvB;QAED,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YACxB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;SACvB;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACpH;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC5B,CAAC;IACD,IAAW,MAAM,CAAC,KAAc;QAC5B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,gBAAgB;IACT,oBAAoB;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChI,CAAC;IAED,gBAAgB;IACT,eAAe;QAClB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAChG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;aACxD;iBAAM;gBACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;aAChC;YACD,OAAO;SACV;QACD,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC9D,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACxD;aAAM;YACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;IACL,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC;QACnF,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAEpE,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,iCAAiC,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC5E;QAED,OAAO;QACP,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;QAED,SAAS;QACT,IAAI,YAAY,EAAE;YACd,oDAAoD;YACpD,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;aAC5E;YAED,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,mBAAmB,CAAC;YAC9E,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,mBAAmB,CAAC;YAE9E,oBAAoB;YACpB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC5B,MAAM,KAAK,GAAG,QAAQ,CAAC;gBAEvB,IAAI,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,KAAK,EAAE;oBACxC,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,KAAK,CAAC;iBAC1C;gBACD,IAAI,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE;oBACzC,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;iBAC3C;aACJ;YAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;aACxD;iBAAM;gBACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;aAChC;YAED,oDAAoD;YACpD,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,MAAM,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,EAAE,CAAC;gBACzD,IAAI,GAAG,EAAE;oBACL,UAAU,CAAC,yBAAyB,CAChC,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAC9B,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAC9B,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAC9B,IAAI,CAAC,iCAAiC,CACzC,CAAC;oBACF,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;wBAClB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;qBAC5E;yBAAM;wBACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;qBAChC;iBACJ;aACJ;SACJ;QAED,UAAU;QACV,IAAI,UAAU,EAAE;YACZ,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE;gBACzD,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;aAC9B;YAED,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE;gBACzD,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;aAC9B;YAED,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE;gBACzD,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;aAC9B;YAED,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACnD;QACD,IAAI,YAAY,EAAE;YACd,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE;gBACxD,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;aAC7B;YAED,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE;gBACxD,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;aAC7B;YACD,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAClD;QAED,KAAK,CAAC,YAAY,EAAE,CAAC;IACzB,CAAC;IAES,2BAA2B;QACjC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SACxE;aAAM;YACH,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;SACnH;IACL,CAAC;IAED;;;OAGG;IACK,uCAAuC;QAC3C,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzF,OAAO,IAAI,CAAC;IAChB,CAAC;IAID,gBAAgB;IACT,cAAc;QACjB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,wBAAwB,EAAG,CAAC,CAAC;SACpD;QAED,UAAU;QACV,IAAI,CAAC,2BAA2B,EAAE,CAAC;QAEnC,6CAA6C;QAC7C,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE;YACzF,IAAI,CAAC,uCAAuC,EAAE,CAAC;YAC/C,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;SAC/D;aAAM,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE;YAClD,IAAI,CAAC,uCAAuC,EAAE,CAAC;YAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC3C;QAED,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAErH,oCAAoC;QACpC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7E,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,IAAI,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC1E;iBAAM;gBACH,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBACpE,IAAI,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aACtE;SACJ;QACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3E,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAES,kBAAkB,CAAC,QAAiB,EAAE,MAAe,EAAE,EAAW;QACxE,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;gBACvD,OAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBACrF,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACpF,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,iBAAiB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBACvE,IAAI,CAAC,qBAAqB,EAAE,CAAC;aAChC;iBAAM;gBACH,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACxC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACvC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;aAClC;YAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE;gBACtC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;aAC1G;iBAAM;gBACH,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;aAC1G;YACD,OAAO;SACV;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE;YACtC,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;SAChE;aAAM;YACH,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;SAChE;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YACvD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACpE,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3D,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAChC;aAAM;YACH,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;SAC3C;IACL,CAAC;IAED;;OAEG;IACH,6DAA6D;IACtD,eAAe,CAAC,IAAY,EAAE,WAAmB;QACpD,IAAI,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,aAAa,EAAE;YAC7C,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjF,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;YAC7B,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;YAC3B,IAAI,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,WAAW,EAAE;gBAC3C,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;oBAC1B,IAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;iBAC9C;gBACD,SAAS,CAAC,gBAAgB,GAAG,EAAE,CAAC;gBAChC,SAAS,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;aACnD;YAED,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YAC3B,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACrC,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACvC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACnC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YAEzC,OAAO,SAAS,CAAC;SACpB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,MAAM,OAAO,GAAiB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAClD,MAAM,QAAQ,GAAiB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAEnD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,QAAQ,IAAI,CAAC,aAAa,EAAE;YACxB,KAAK,MAAM,CAAC,8BAA8B,CAAC;YAC3C,KAAK,MAAM,CAAC,yCAAyC,CAAC;YACtD,KAAK,MAAM,CAAC,0CAA0C,CAAC;YACvD,KAAK,MAAM,CAAC,+BAA+B,CAAC;YAC5C,KAAK,MAAM,CAAC,gCAAgC,CAAC,CAAC;gBAC1C,4HAA4H;gBAC5H,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,0CAA0C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnG,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,0CAA0C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAC5F,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAC9F,MAAM;aACT;YACD,KAAK,MAAM,CAAC,WAAW;gBACnB,IAAI,OAAO,CAAC,kBAAkB,EAAE;oBAC5B,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBAC7D,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;iBACjE;qBAAM;oBACH,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACzC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBAC7C;gBACD,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACzC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAE1C,MAAM;SACb;QACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAC9B,CAAC;IAEO,2BAA2B,CAAC,SAAiB,EAAE,SAAuB;QAC1E,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,iBAAiB,CAAC,CAAC;QAEpE,YAAY,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACpF,MAAM,cAAc,GAAG,YAAY,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhF,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,EAAE,YAAY,CAAC,sBAAsB,CAAC,CAAC;QACtH,YAAY,CAAC,sBAAsB,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,YAAY,CAAC,sBAAsB,CAAC,CAAC;QAC3I,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,YAAY,CAAC,sBAAsB,CAAC,CAAC;QAEnH,YAAY,CAAC,sBAAsB,CAAC,aAAa,CAAC,YAAY,CAAC,sBAAsB,EAAE,YAAY,CAAC,sBAAsB,CAAC,CAAC;QAE5H,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,sBAAsB,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1G,SAAS,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,cAAc,CAAC;IAC1B,CAAC;;AA5lBc,mCAAsB,GAAG,IAAI,MAAM,EAAE,AAAf,CAAgB;AACtC,mCAAsB,GAAG,IAAI,MAAM,EAAE,AAAf,CAAgB;AACtC,8BAAiB,GAAG,IAAI,OAAO,EAAE,AAAhB,CAAiB;AA2B1C;IADN,kBAAkB,EAAE;8CACkB;AAWhC;IADN,SAAS,EAAE;2CACO;AAwBZ;IADN,wBAAwB,CAAC,gBAAgB,CAAC;kDACX", "sourcesContent": ["import { serialize, serializeAsVector3, serializeAsMeshReference } from \"../Misc/decorators\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Camera } from \"./camera\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Quaternion, Matrix, Vector3, Vector2, TmpVectors } from \"../Maths/math.vector\";\r\nimport { Epsilon } from \"../Maths/math.constants\";\r\nimport { Axis } from \"../Maths/math.axis\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Node } from \"../node\";\r\n\r\nNode.AddNodeConstructor(\"TargetCamera\", (name, scene) => {\r\n    return () => new TargetCamera(name, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * A target camera takes a mesh or position as a target and continues to look at it while it moves.\r\n * This is the base of the follow, arc rotate cameras and Free camera\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras\r\n */\r\nexport class TargetCamera extends Camera {\r\n    private static _RigCamTransformMatrix = new Matrix();\r\n    private static _TargetTransformMatrix = new Matrix();\r\n    private static _TargetFocalPoint = new Vector3();\r\n\r\n    private _tmpUpVector = Vector3.Zero();\r\n    private _tmpTargetVector = Vector3.Zero();\r\n\r\n    /**\r\n     * Define the current direction the camera is moving to\r\n     */\r\n    public cameraDirection = new Vector3(0, 0, 0);\r\n    /**\r\n     * Define the current rotation the camera is rotating to\r\n     */\r\n    public cameraRotation = new Vector2(0, 0);\r\n\r\n    /** Gets or sets a boolean indicating that the scaling of the parent hierarchy will not be taken in account by the camera */\r\n    public ignoreParentScaling = false;\r\n\r\n    /**\r\n     * When set, the up vector of the camera will be updated by the rotation of the camera\r\n     */\r\n    public updateUpVectorFromRotation = false;\r\n    private _tmpQuaternion = new Quaternion();\r\n\r\n    /**\r\n     * Define the current rotation of the camera\r\n     */\r\n    @serializeAsVector3()\r\n    public rotation = new Vector3(0, 0, 0);\r\n\r\n    /**\r\n     * Define the current rotation of the camera as a quaternion to prevent Gimbal lock\r\n     */\r\n    public rotationQuaternion: Quaternion;\r\n\r\n    /**\r\n     * Define the current speed of the camera\r\n     */\r\n    @serialize()\r\n    public speed = 2.0;\r\n\r\n    /**\r\n     * Add constraint to the camera to prevent it to move freely in all directions and\r\n     * around all axis.\r\n     */\r\n    public noRotationConstraint = false;\r\n\r\n    /**\r\n     * Reverses mouselook direction to 'natural' panning as opposed to traditional direct\r\n     * panning\r\n     */\r\n    public invertRotation = false;\r\n\r\n    /**\r\n     * Speed multiplier for inverse camera panning\r\n     */\r\n    public inverseRotationSpeed = 0.2;\r\n\r\n    /**\r\n     * Define the current target of the camera as an object or a position.\r\n     * Please note that locking a target will disable panning.\r\n     */\r\n    @serializeAsMeshReference(\"lockedTargetId\")\r\n    public lockedTarget: any = null;\r\n\r\n    /** @internal */\r\n    public _currentTarget = Vector3.Zero();\r\n    /** @internal */\r\n    public _initialFocalDistance = 1;\r\n    /** @internal */\r\n    public _viewMatrix = Matrix.Zero();\r\n    /** @internal */\r\n    public _camMatrix = Matrix.Zero();\r\n    /** @internal */\r\n    public _cameraTransformMatrix = Matrix.Zero();\r\n    /** @internal */\r\n    public _cameraRotationMatrix = Matrix.Zero();\r\n\r\n    /** @internal */\r\n    public _referencePoint = new Vector3(0, 0, 1);\r\n    /** @internal */\r\n    public _transformedReferencePoint = Vector3.Zero();\r\n\r\n    protected _deferredPositionUpdate = new Vector3();\r\n    protected _deferredRotationQuaternionUpdate = new Quaternion();\r\n    protected _deferredRotationUpdate = new Vector3();\r\n    protected _deferredUpdated = false;\r\n    protected _deferOnly: boolean = false;\r\n\r\n    /** @internal */\r\n    public _reset: () => void;\r\n\r\n    private _defaultUp = Vector3.Up();\r\n\r\n    /**\r\n     * Instantiates a target camera that takes a mesh or position as a target and continues to look at it while it moves.\r\n     * This is the base of the follow, arc rotate cameras and Free camera\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras\r\n     * @param name Defines the name of the camera in the scene\r\n     * @param position Defines the start position of the camera in the scene\r\n     * @param scene Defines the scene the camera belongs to\r\n     * @param setActiveOnSceneIfNoneActive Defines whether the camera should be marked as active if not other active cameras have been defined\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene, setActiveOnSceneIfNoneActive = true) {\r\n        super(name, position, scene, setActiveOnSceneIfNoneActive);\r\n    }\r\n\r\n    /**\r\n     * Gets the position in front of the camera at a given distance.\r\n     * @param distance The distance from the camera we want the position to be\r\n     * @returns the position\r\n     */\r\n    public getFrontPosition(distance: number): Vector3 {\r\n        this.getWorldMatrix();\r\n        const direction = this.getTarget().subtract(this.position);\r\n        direction.normalize();\r\n        direction.scaleInPlace(distance);\r\n        return this.globalPosition.add(direction);\r\n    }\r\n\r\n    /** @internal */\r\n    public _getLockedTargetPosition(): Nullable<Vector3> {\r\n        if (!this.lockedTarget) {\r\n            return null;\r\n        }\r\n\r\n        if (this.lockedTarget.absolutePosition) {\r\n            const lockedTarget = this.lockedTarget as AbstractMesh;\r\n            const m = lockedTarget.computeWorldMatrix();\r\n            // in some cases the absolute position resets externally, but doesn't update since the matrix is cached.\r\n            m.getTranslationToRef(lockedTarget.absolutePosition);\r\n        }\r\n\r\n        return this.lockedTarget.absolutePosition || this.lockedTarget;\r\n    }\r\n\r\n    private _storedPosition: Vector3;\r\n    private _storedRotation: Vector3;\r\n    private _storedRotationQuaternion: Quaternion;\r\n\r\n    /**\r\n     * Store current camera state of the camera (fov, position, rotation, etc..)\r\n     * @returns the camera\r\n     */\r\n    public storeState(): Camera {\r\n        this._storedPosition = this.position.clone();\r\n        this._storedRotation = this.rotation.clone();\r\n        if (this.rotationQuaternion) {\r\n            this._storedRotationQuaternion = this.rotationQuaternion.clone();\r\n        }\r\n\r\n        return super.storeState();\r\n    }\r\n\r\n    /**\r\n     * Restored camera state. You must call storeState() first\r\n     * @returns whether it was successful or not\r\n     * @internal\r\n     */\r\n    public _restoreStateValues(): boolean {\r\n        if (!super._restoreStateValues()) {\r\n            return false;\r\n        }\r\n\r\n        this.position = this._storedPosition.clone();\r\n        this.rotation = this._storedRotation.clone();\r\n\r\n        if (this.rotationQuaternion) {\r\n            this.rotationQuaternion = this._storedRotationQuaternion.clone();\r\n        }\r\n\r\n        this.cameraDirection.copyFromFloats(0, 0, 0);\r\n        this.cameraRotation.copyFromFloats(0, 0);\r\n\r\n        return true;\r\n    }\r\n\r\n    /** @internal */\r\n    public _initCache() {\r\n        super._initCache();\r\n        this._cache.lockedTarget = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        this._cache.rotation = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        this._cache.rotationQuaternion = new Quaternion(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _updateCache(ignoreParentClass?: boolean): void {\r\n        if (!ignoreParentClass) {\r\n            super._updateCache();\r\n        }\r\n\r\n        const lockedTargetPosition = this._getLockedTargetPosition();\r\n        if (!lockedTargetPosition) {\r\n            this._cache.lockedTarget = null;\r\n        } else {\r\n            if (!this._cache.lockedTarget) {\r\n                this._cache.lockedTarget = lockedTargetPosition.clone();\r\n            } else {\r\n                this._cache.lockedTarget.copyFrom(lockedTargetPosition);\r\n            }\r\n        }\r\n\r\n        this._cache.rotation.copyFrom(this.rotation);\r\n        if (this.rotationQuaternion) {\r\n            this._cache.rotationQuaternion.copyFrom(this.rotationQuaternion);\r\n        }\r\n    }\r\n\r\n    // Synchronized\r\n    /** @internal */\r\n    public _isSynchronizedViewMatrix(): boolean {\r\n        if (!super._isSynchronizedViewMatrix()) {\r\n            return false;\r\n        }\r\n\r\n        const lockedTargetPosition = this._getLockedTargetPosition();\r\n\r\n        return (\r\n            (this._cache.lockedTarget ? this._cache.lockedTarget.equals(lockedTargetPosition) : !lockedTargetPosition) &&\r\n            (this.rotationQuaternion ? this.rotationQuaternion.equals(this._cache.rotationQuaternion) : this._cache.rotation.equals(this.rotation))\r\n        );\r\n    }\r\n\r\n    // Methods\r\n    /** @internal */\r\n    public _computeLocalCameraSpeed(): number {\r\n        const engine = this.getEngine();\r\n        return this.speed * Math.sqrt(engine.getDeltaTime() / (engine.getFps() * 100.0));\r\n    }\r\n\r\n    // Target\r\n\r\n    /**\r\n     * Defines the target the camera should look at.\r\n     * @param target Defines the new target as a Vector\r\n     */\r\n    public setTarget(target: Vector3): void {\r\n        this.upVector.normalize();\r\n\r\n        this._initialFocalDistance = target.subtract(this.position).length();\r\n\r\n        if (this.position.z === target.z) {\r\n            this.position.z += Epsilon;\r\n        }\r\n\r\n        this._referencePoint.normalize().scaleInPlace(this._initialFocalDistance);\r\n\r\n        Matrix.LookAtLHToRef(this.position, target, this._defaultUp, this._camMatrix);\r\n        this._camMatrix.invert();\r\n\r\n        this.rotation.x = Math.atan(this._camMatrix.m[6] / this._camMatrix.m[10]);\r\n\r\n        const vDir = target.subtract(this.position);\r\n\r\n        if (vDir.x >= 0.0) {\r\n            this.rotation.y = -Math.atan(vDir.z / vDir.x) + Math.PI / 2.0;\r\n        } else {\r\n            this.rotation.y = -Math.atan(vDir.z / vDir.x) - Math.PI / 2.0;\r\n        }\r\n\r\n        this.rotation.z = 0;\r\n\r\n        if (isNaN(this.rotation.x)) {\r\n            this.rotation.x = 0;\r\n        }\r\n\r\n        if (isNaN(this.rotation.y)) {\r\n            this.rotation.y = 0;\r\n        }\r\n\r\n        if (isNaN(this.rotation.z)) {\r\n            this.rotation.z = 0;\r\n        }\r\n\r\n        if (this.rotationQuaternion) {\r\n            Quaternion.RotationYawPitchRollToRef(this.rotation.y, this.rotation.x, this.rotation.z, this.rotationQuaternion);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Defines the target point of the camera.\r\n     * The camera looks towards it form the radius distance.\r\n     */\r\n    public get target(): Vector3 {\r\n        return this.getTarget();\r\n    }\r\n    public set target(value: Vector3) {\r\n        this.setTarget(value);\r\n    }\r\n\r\n    /**\r\n     * Return the current target position of the camera. This value is expressed in local space.\r\n     * @returns the target position\r\n     */\r\n    public getTarget(): Vector3 {\r\n        return this._currentTarget;\r\n    }\r\n\r\n    /** @internal */\r\n    public _decideIfNeedsToMove(): boolean {\r\n        return Math.abs(this.cameraDirection.x) > 0 || Math.abs(this.cameraDirection.y) > 0 || Math.abs(this.cameraDirection.z) > 0;\r\n    }\r\n\r\n    /** @internal */\r\n    public _updatePosition(): void {\r\n        if (this.parent) {\r\n            this.parent.getWorldMatrix().invertToRef(TmpVectors.Matrix[0]);\r\n            Vector3.TransformNormalToRef(this.cameraDirection, TmpVectors.Matrix[0], TmpVectors.Vector3[0]);\r\n            this._deferredPositionUpdate.addInPlace(TmpVectors.Vector3[0]);\r\n            if (!this._deferOnly) {\r\n                this.position.copyFrom(this._deferredPositionUpdate);\r\n            } else {\r\n                this._deferredUpdated = true;\r\n            }\r\n            return;\r\n        }\r\n        this._deferredPositionUpdate.addInPlace(this.cameraDirection);\r\n        if (!this._deferOnly) {\r\n            this.position.copyFrom(this._deferredPositionUpdate);\r\n        } else {\r\n            this._deferredUpdated = true;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _checkInputs(): void {\r\n        const directionMultiplier = this.invertRotation ? -this.inverseRotationSpeed : 1.0;\r\n        const needToMove = this._decideIfNeedsToMove();\r\n        const needToRotate = this.cameraRotation.x || this.cameraRotation.y;\r\n\r\n        this._deferredUpdated = false;\r\n        this._deferredRotationUpdate.copyFrom(this.rotation);\r\n        this._deferredPositionUpdate.copyFrom(this.position);\r\n        if (this.rotationQuaternion) {\r\n            this._deferredRotationQuaternionUpdate.copyFrom(this.rotationQuaternion);\r\n        }\r\n\r\n        // Move\r\n        if (needToMove) {\r\n            this._updatePosition();\r\n        }\r\n\r\n        // Rotate\r\n        if (needToRotate) {\r\n            //rotate, if quaternion is set and rotation was used\r\n            if (this.rotationQuaternion) {\r\n                this.rotationQuaternion.toEulerAnglesToRef(this._deferredRotationUpdate);\r\n            }\r\n\r\n            this._deferredRotationUpdate.x += this.cameraRotation.x * directionMultiplier;\r\n            this._deferredRotationUpdate.y += this.cameraRotation.y * directionMultiplier;\r\n\r\n            // Apply constraints\r\n            if (!this.noRotationConstraint) {\r\n                const limit = 1.570796;\r\n\r\n                if (this._deferredRotationUpdate.x > limit) {\r\n                    this._deferredRotationUpdate.x = limit;\r\n                }\r\n                if (this._deferredRotationUpdate.x < -limit) {\r\n                    this._deferredRotationUpdate.x = -limit;\r\n                }\r\n            }\r\n\r\n            if (!this._deferOnly) {\r\n                this.rotation.copyFrom(this._deferredRotationUpdate);\r\n            } else {\r\n                this._deferredUpdated = true;\r\n            }\r\n\r\n            //rotate, if quaternion is set and rotation was used\r\n            if (this.rotationQuaternion) {\r\n                const len = this._deferredRotationUpdate.lengthSquared();\r\n                if (len) {\r\n                    Quaternion.RotationYawPitchRollToRef(\r\n                        this._deferredRotationUpdate.y,\r\n                        this._deferredRotationUpdate.x,\r\n                        this._deferredRotationUpdate.z,\r\n                        this._deferredRotationQuaternionUpdate\r\n                    );\r\n                    if (!this._deferOnly) {\r\n                        this.rotationQuaternion.copyFrom(this._deferredRotationQuaternionUpdate);\r\n                    } else {\r\n                        this._deferredUpdated = true;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Inertia\r\n        if (needToMove) {\r\n            if (Math.abs(this.cameraDirection.x) < this.speed * Epsilon) {\r\n                this.cameraDirection.x = 0;\r\n            }\r\n\r\n            if (Math.abs(this.cameraDirection.y) < this.speed * Epsilon) {\r\n                this.cameraDirection.y = 0;\r\n            }\r\n\r\n            if (Math.abs(this.cameraDirection.z) < this.speed * Epsilon) {\r\n                this.cameraDirection.z = 0;\r\n            }\r\n\r\n            this.cameraDirection.scaleInPlace(this.inertia);\r\n        }\r\n        if (needToRotate) {\r\n            if (Math.abs(this.cameraRotation.x) < this.speed * Epsilon) {\r\n                this.cameraRotation.x = 0;\r\n            }\r\n\r\n            if (Math.abs(this.cameraRotation.y) < this.speed * Epsilon) {\r\n                this.cameraRotation.y = 0;\r\n            }\r\n            this.cameraRotation.scaleInPlace(this.inertia);\r\n        }\r\n\r\n        super._checkInputs();\r\n    }\r\n\r\n    protected _updateCameraRotationMatrix() {\r\n        if (this.rotationQuaternion) {\r\n            this.rotationQuaternion.toRotationMatrix(this._cameraRotationMatrix);\r\n        } else {\r\n            Matrix.RotationYawPitchRollToRef(this.rotation.y, this.rotation.x, this.rotation.z, this._cameraRotationMatrix);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the up vector to apply the rotation of the camera (So if you changed the camera rotation.z this will let you update the up vector as well)\r\n     * @returns the current camera\r\n     */\r\n    private _rotateUpVectorWithCameraRotationMatrix(): TargetCamera {\r\n        Vector3.TransformNormalToRef(this._defaultUp, this._cameraRotationMatrix, this.upVector);\r\n        return this;\r\n    }\r\n\r\n    private _cachedRotationZ = 0;\r\n    private _cachedQuaternionRotationZ = 0;\r\n    /** @internal */\r\n    public _getViewMatrix(): Matrix {\r\n        if (this.lockedTarget) {\r\n            this.setTarget(this._getLockedTargetPosition()!);\r\n        }\r\n\r\n        // Compute\r\n        this._updateCameraRotationMatrix();\r\n\r\n        // Apply the changed rotation to the upVector\r\n        if (this.rotationQuaternion && this._cachedQuaternionRotationZ != this.rotationQuaternion.z) {\r\n            this._rotateUpVectorWithCameraRotationMatrix();\r\n            this._cachedQuaternionRotationZ = this.rotationQuaternion.z;\r\n        } else if (this._cachedRotationZ !== this.rotation.z) {\r\n            this._rotateUpVectorWithCameraRotationMatrix();\r\n            this._cachedRotationZ = this.rotation.z;\r\n        }\r\n\r\n        Vector3.TransformCoordinatesToRef(this._referencePoint, this._cameraRotationMatrix, this._transformedReferencePoint);\r\n\r\n        // Computing target and final matrix\r\n        this.position.addToRef(this._transformedReferencePoint, this._currentTarget);\r\n        if (this.updateUpVectorFromRotation) {\r\n            if (this.rotationQuaternion) {\r\n                Axis.Y.rotateByQuaternionToRef(this.rotationQuaternion, this.upVector);\r\n            } else {\r\n                Quaternion.FromEulerVectorToRef(this.rotation, this._tmpQuaternion);\r\n                Axis.Y.rotateByQuaternionToRef(this._tmpQuaternion, this.upVector);\r\n            }\r\n        }\r\n        this._computeViewMatrix(this.position, this._currentTarget, this.upVector);\r\n        return this._viewMatrix;\r\n    }\r\n\r\n    protected _computeViewMatrix(position: Vector3, target: Vector3, up: Vector3): void {\r\n        if (this.ignoreParentScaling) {\r\n            if (this.parent) {\r\n                const parentWorldMatrix = this.parent.getWorldMatrix();\r\n                Vector3.TransformCoordinatesToRef(position, parentWorldMatrix, this._globalPosition);\r\n                Vector3.TransformCoordinatesToRef(target, parentWorldMatrix, this._tmpTargetVector);\r\n                Vector3.TransformNormalToRef(up, parentWorldMatrix, this._tmpUpVector);\r\n                this._markSyncedWithParent();\r\n            } else {\r\n                this._globalPosition.copyFrom(position);\r\n                this._tmpTargetVector.copyFrom(target);\r\n                this._tmpUpVector.copyFrom(up);\r\n            }\r\n\r\n            if (this.getScene().useRightHandedSystem) {\r\n                Matrix.LookAtRHToRef(this._globalPosition, this._tmpTargetVector, this._tmpUpVector, this._viewMatrix);\r\n            } else {\r\n                Matrix.LookAtLHToRef(this._globalPosition, this._tmpTargetVector, this._tmpUpVector, this._viewMatrix);\r\n            }\r\n            return;\r\n        }\r\n\r\n        if (this.getScene().useRightHandedSystem) {\r\n            Matrix.LookAtRHToRef(position, target, up, this._viewMatrix);\r\n        } else {\r\n            Matrix.LookAtLHToRef(position, target, up, this._viewMatrix);\r\n        }\r\n\r\n        if (this.parent) {\r\n            const parentWorldMatrix = this.parent.getWorldMatrix();\r\n            this._viewMatrix.invert();\r\n            this._viewMatrix.multiplyToRef(parentWorldMatrix, this._viewMatrix);\r\n            this._viewMatrix.getTranslationToRef(this._globalPosition);\r\n            this._viewMatrix.invert();\r\n            this._markSyncedWithParent();\r\n        } else {\r\n            this._globalPosition.copyFrom(position);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public createRigCamera(name: string, cameraIndex: number): Nullable<Camera> {\r\n        if (this.cameraRigMode !== Camera.RIG_MODE_NONE) {\r\n            const rigCamera = new TargetCamera(name, this.position.clone(), this.getScene());\r\n            rigCamera.isRigCamera = true;\r\n            rigCamera.rigParent = this;\r\n            if (this.cameraRigMode === Camera.RIG_MODE_VR) {\r\n                if (!this.rotationQuaternion) {\r\n                    this.rotationQuaternion = new Quaternion();\r\n                }\r\n                rigCamera._cameraRigParams = {};\r\n                rigCamera.rotationQuaternion = new Quaternion();\r\n            }\r\n\r\n            rigCamera.mode = this.mode;\r\n            rigCamera.orthoLeft = this.orthoLeft;\r\n            rigCamera.orthoRight = this.orthoRight;\r\n            rigCamera.orthoTop = this.orthoTop;\r\n            rigCamera.orthoBottom = this.orthoBottom;\r\n\r\n            return rigCamera;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _updateRigCameras() {\r\n        const camLeft = <TargetCamera>this._rigCameras[0];\r\n        const camRight = <TargetCamera>this._rigCameras[1];\r\n\r\n        this.computeWorldMatrix();\r\n\r\n        switch (this.cameraRigMode) {\r\n            case Camera.RIG_MODE_STEREOSCOPIC_ANAGLYPH:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_OVERUNDER:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_INTERLACED: {\r\n                //provisionnaly using _cameraRigParams.stereoHalfAngle instead of calculations based on _cameraRigParams.interaxialDistance:\r\n                const leftSign = this.cameraRigMode === Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED ? 1 : -1;\r\n                const rightSign = this.cameraRigMode === Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED ? -1 : 1;\r\n                this._getRigCamPositionAndTarget(this._cameraRigParams.stereoHalfAngle * leftSign, camLeft);\r\n                this._getRigCamPositionAndTarget(this._cameraRigParams.stereoHalfAngle * rightSign, camRight);\r\n                break;\r\n            }\r\n            case Camera.RIG_MODE_VR:\r\n                if (camLeft.rotationQuaternion) {\r\n                    camLeft.rotationQuaternion.copyFrom(this.rotationQuaternion);\r\n                    camRight.rotationQuaternion.copyFrom(this.rotationQuaternion);\r\n                } else {\r\n                    camLeft.rotation.copyFrom(this.rotation);\r\n                    camRight.rotation.copyFrom(this.rotation);\r\n                }\r\n                camLeft.position.copyFrom(this.position);\r\n                camRight.position.copyFrom(this.position);\r\n\r\n                break;\r\n        }\r\n        super._updateRigCameras();\r\n    }\r\n\r\n    private _getRigCamPositionAndTarget(halfSpace: number, rigCamera: TargetCamera) {\r\n        const target = this.getTarget();\r\n        target.subtractToRef(this.position, TargetCamera._TargetFocalPoint);\r\n\r\n        TargetCamera._TargetFocalPoint.normalize().scaleInPlace(this._initialFocalDistance);\r\n        const newFocalTarget = TargetCamera._TargetFocalPoint.addInPlace(this.position);\r\n\r\n        Matrix.TranslationToRef(-newFocalTarget.x, -newFocalTarget.y, -newFocalTarget.z, TargetCamera._TargetTransformMatrix);\r\n        TargetCamera._TargetTransformMatrix.multiplyToRef(Matrix.RotationAxis(rigCamera.upVector, halfSpace), TargetCamera._RigCamTransformMatrix);\r\n        Matrix.TranslationToRef(newFocalTarget.x, newFocalTarget.y, newFocalTarget.z, TargetCamera._TargetTransformMatrix);\r\n\r\n        TargetCamera._RigCamTransformMatrix.multiplyToRef(TargetCamera._TargetTransformMatrix, TargetCamera._RigCamTransformMatrix);\r\n\r\n        Vector3.TransformCoordinatesToRef(this.position, TargetCamera._RigCamTransformMatrix, rigCamera.position);\r\n        rigCamera.setTarget(newFocalTarget);\r\n    }\r\n\r\n    /**\r\n     * Gets the current object class name.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"TargetCamera\";\r\n    }\r\n}\r\n"]}