{"version": 3, "file": "WebXRFeaturePointSystem.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRFeaturePointSystem.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAEjF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAiB9D;;;;GAIG;AACH,MAAM,OAAO,uBAAwB,SAAQ,oBAAoB;IAwB7D;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,YAAY,iBAAsC;QAC9C,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAnCrB,aAAQ,GAAY,KAAK,CAAC;QAC1B,uBAAkB,GAA8B,EAAE,CAAC;QAY3D;;;WAGG;QACa,mCAA8B,GAAyB,IAAI,UAAU,EAAE,CAAC;QACxF;;;WAGG;QACa,qCAAgC,GAAyB,IAAI,UAAU,EAAE,CAAC;QActF,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE;YAChC,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;aAAM;YACH,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,EAAE;gBAChD,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;QAC9C,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;IAChD,CAAC;IAED;;;OAGG;IACO,UAAU,CAAC,KAAc;QAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;YAC5C,OAAO;SACV;QAED,MAAM,mBAAmB,GAAyB,KAAK,CAAC,iBAAiB,CAAC;QAC1E,IAAI,CAAC,mBAAmB,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1D,OAAO;SACV;aAAM;YACH,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;gBACtC,MAAM,IAAI,KAAK,CAAC,oDAAoD,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;aACtG;YAED,MAAM,qBAAqB,GAAW,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;YACrE,MAAM,oBAAoB,GAAG,EAAE,CAAC;YAChC,MAAM,kBAAkB,GAAG,EAAE,CAAC;YAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,qBAAqB,EAAE,CAAC,EAAE,EAAE;gBAC5C,MAAM,QAAQ,GAAW,CAAC,GAAG,CAAC,CAAC;gBAC/B,MAAM,EAAE,GAAG,mBAAmB,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;gBAE7C,+HAA+H;gBAC/H,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAE;oBAC9B,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,OAAO,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE,CAAC;oBAC9E,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBAC/B;qBAAM;oBACH,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBACjC;gBAED,gCAAgC;gBAChC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBACvE,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,mBAAmB,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;gBAC3E,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,mBAAmB,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;gBAC3E,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,eAAe,GAAG,mBAAmB,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;aACnF;YAED,qEAAqE;YACrE,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC/B,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;aAC3E;YAED,uEAAuE;YACvE,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjC,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;aAC/E;SACJ;IACL,CAAC;IAED;;OAEG;IACK,KAAK;QACT,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,8BAA8B,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;YACxI,gBAAgB;YAChB,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;;AAlID;;GAEG;AACoB,4BAAI,GAAG,gBAAgB,CAAC,cAAc,AAAlC,CAAmC;AAC9D;;;;GAIG;AACoB,+BAAO,GAAG,CAAC,AAAJ,CAAK;AA4HvC,sBAAsB;AACtB,oBAAoB,CAAC,eAAe,CAChC,uBAAuB,CAAC,IAAI,EAC5B,CAAC,gBAAgB,EAAE,EAAE;IACjB,OAAO,GAAG,EAAE,CAAC,IAAI,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;AAC/D,CAAC,EACD,uBAAuB,CAAC,OAAO,CAClC,CAAC", "sourcesContent": ["import { WebXRFeaturesManager, WebXRFeatureName } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\n\r\n/**\r\n * A babylon interface for a \"WebXR\" feature point.\r\n * Represents the position and confidence value of a given feature point.\r\n */\r\nexport interface IWebXRFeaturePoint {\r\n    /**\r\n     * Represents the position of the feature point in world space.\r\n     */\r\n    position: Vector3;\r\n    /**\r\n     * Represents the confidence value of the feature point in world space. 0 being least confident, and 1 being most confident.\r\n     */\r\n    confidenceValue: number;\r\n}\r\n\r\n/**\r\n * The feature point system is used to detect feature points from real world geometry.\r\n * This feature is currently experimental and only supported on BabylonNative, and should not be used in the browser.\r\n * The newly introduced API can be seen in webxr.nativeextensions.d.ts and described in FeaturePoints.md.\r\n */\r\nexport class WebXRFeaturePointSystem extends WebXRAbstractFeature {\r\n    private _enabled: boolean = false;\r\n    private _featurePointCloud: Array<IWebXRFeaturePoint> = [];\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.FEATURE_POINTS;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n    /**\r\n     * Observers registered here will be executed whenever new feature points are added (on XRFrame while the session is tracking).\r\n     * Will notify the observers about which feature points have been added.\r\n     */\r\n    public readonly onFeaturePointsAddedObservable: Observable<number[]> = new Observable();\r\n    /**\r\n     * Observers registered here will be executed whenever a feature point has been updated (on XRFrame while the session is tracking).\r\n     * Will notify the observers about which feature points have been updated.\r\n     */\r\n    public readonly onFeaturePointsUpdatedObservable: Observable<number[]> = new Observable();\r\n    /**\r\n     * The current feature point cloud maintained across frames.\r\n     */\r\n    public get featurePointCloud(): Array<IWebXRFeaturePoint> {\r\n        return this._featurePointCloud;\r\n    }\r\n\r\n    /**\r\n     * construct the feature point system\r\n     * @param _xrSessionManager an instance of xr Session manager\r\n     */\r\n    constructor(_xrSessionManager: WebXRSessionManager) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"bjsfeature-points\";\r\n        if (this._xrSessionManager.session) {\r\n            this._init();\r\n        } else {\r\n            this._xrSessionManager.onXRSessionInit.addOnce(() => {\r\n                this._init();\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Detach this feature.\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n\r\n        this.featurePointCloud.length = 0;\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n\r\n        this._featurePointCloud.length = 0;\r\n        this.onFeaturePointsUpdatedObservable.clear();\r\n        this.onFeaturePointsAddedObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * On receiving a new XR frame if this feature is attached notify observers new feature point data is available.\r\n     * @param frame\r\n     */\r\n    protected _onXRFrame(frame: XRFrame) {\r\n        if (!this.attached || !this._enabled || !frame) {\r\n            return;\r\n        }\r\n\r\n        const featurePointRawData: number[] | undefined = frame.featurePointCloud;\r\n        if (!featurePointRawData || featurePointRawData.length === 0) {\r\n            return;\r\n        } else {\r\n            if (featurePointRawData.length % 5 !== 0) {\r\n                throw new Error(\"Received malformed feature point cloud of length: \" + featurePointRawData.length);\r\n            }\r\n\r\n            const numberOfFeaturePoints: number = featurePointRawData.length / 5;\r\n            const updatedFeaturePoints = [];\r\n            const addedFeaturePoints = [];\r\n            for (let i = 0; i < numberOfFeaturePoints; i++) {\r\n                const rawIndex: number = i * 5;\r\n                const id = featurePointRawData[rawIndex + 4];\r\n\r\n                // IDs should be durable across frames and strictly increasing from 0 up, so use them as indexing into the feature point array.\r\n                if (!this._featurePointCloud[id]) {\r\n                    this._featurePointCloud[id] = { position: new Vector3(), confidenceValue: 0 };\r\n                    addedFeaturePoints.push(id);\r\n                } else {\r\n                    updatedFeaturePoints.push(id);\r\n                }\r\n\r\n                // Set the feature point values.\r\n                this._featurePointCloud[id].position.x = featurePointRawData[rawIndex];\r\n                this._featurePointCloud[id].position.y = featurePointRawData[rawIndex + 1];\r\n                this._featurePointCloud[id].position.z = featurePointRawData[rawIndex + 2];\r\n                this._featurePointCloud[id].confidenceValue = featurePointRawData[rawIndex + 3];\r\n            }\r\n\r\n            // Signal observers that feature points have been added if necessary.\r\n            if (addedFeaturePoints.length > 0) {\r\n                this.onFeaturePointsAddedObservable.notifyObservers(addedFeaturePoints);\r\n            }\r\n\r\n            // Signal observers that feature points have been updated if necessary.\r\n            if (updatedFeaturePoints.length > 0) {\r\n                this.onFeaturePointsUpdatedObservable.notifyObservers(updatedFeaturePoints);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Initializes the feature. If the feature point feature is not available for this environment do not mark the feature as enabled.\r\n     */\r\n    private _init() {\r\n        if (!this._xrSessionManager.session.trySetFeaturePointCloudEnabled || !this._xrSessionManager.session.trySetFeaturePointCloudEnabled(true)) {\r\n            // fail silently\r\n            return;\r\n        }\r\n\r\n        this._enabled = true;\r\n    }\r\n}\r\n\r\n// register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRFeaturePointSystem.Name,\r\n    (xrSessionManager) => {\r\n        return () => new WebXRFeaturePointSystem(xrSessionManager);\r\n    },\r\n    WebXRFeaturePointSystem.Version\r\n);\r\n"]}