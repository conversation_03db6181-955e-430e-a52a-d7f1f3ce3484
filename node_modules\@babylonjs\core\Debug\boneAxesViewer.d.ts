import type { Nullable } from "../types";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../Debug/axesViewer";
import { Vector3 } from "../Maths/math.vector";
import type { Mesh } from "../Meshes/mesh";
import type { Bone } from "../Bones/bone";
import type { Scene } from "../scene";
/**
 * The BoneAxesViewer will attach 3 axes to a specific bone of a specific mesh
 * @see demo here: https://www.babylonjs-playground.com/#0DE8F4#8
 */
export declare class BoneAxesViewer extends AxesViewer {
    /**
     * Gets or sets the target mesh where to display the axes viewer
     */
    mesh: Nullable<Mesh>;
    /**
     * Gets or sets the target bone where to display the axes viewer
     */
    bone: Nullable<Bone>;
    /** Gets current position */
    pos: Vector3;
    /** Gets direction of X axis */
    xaxis: Vector3;
    /** Gets direction of Y axis */
    yaxis: Vector3;
    /** Gets direction of Z axis */
    zaxis: Vector3;
    /**
     * Creates a new BoneAxesViewer
     * @param scene defines the hosting scene
     * @param bone defines the target bone
     * @param mesh defines the target mesh
     * @param scaleLines defines a scaling factor for line length (1 by default)
     */
    constructor(scene: Scene, bone: Bone, mesh: Mesh, scaleLines?: number);
    /**
     * Force the viewer to update
     */
    update(): void;
    /** Releases resources */
    dispose(): void;
}
