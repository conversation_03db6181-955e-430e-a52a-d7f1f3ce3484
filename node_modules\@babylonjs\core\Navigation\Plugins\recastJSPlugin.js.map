{"version": 3, "file": "recastJSPlugin.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Navigation/Plugins/recastJSPlugin.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAE,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AAEzC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAG5D,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAKpD;;GAEG;AACH,MAAM,OAAO,cAAc;IAyBvB;;;OAGG;IACH,YAAmB,kBAAuB,MAAM;QA5BhD;;WAEG;QACI,cAAS,GAAQ,EAAE,CAAC;QAE3B;;WAEG;QACI,SAAI,GAAW,gBAAgB,CAAC;QAO/B,yBAAoB,GAAW,EAAE,CAAC;QAClC,cAAS,GAAW,CAAC,GAAG,EAAE,CAAC;QAC3B,gBAAW,GAAW,CAAC,CAAC;QAKxB,YAAO,GAAqB,IAAI,CAAC;QAOrC,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE;YACvC,MAAM,CAAC,KAAK,CAAC,qFAAqF,CAAC,CAAC;SACvG;aAAM;YACH,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;SACpC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACrB,MAAM,CAAC,KAAK,CAAC,uEAAuE,CAAC,CAAC;YACtF,OAAO;SACV;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;IAC/C,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,SAAuB;QACvC,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;YACzB,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC;YACrC,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACH,WAAW,CAAC,cAAsB,CAAC,GAAG,EAAE;QACpC,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,WAAW;QACP,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;;;;OAMG;IACH,sBAAsB,CAAC,eAAuB,EAAE;QAC5C,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACH,sBAAsB;QAClB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,IAAW,UAAU,CAAC,KAAa;QAC/B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;;OAKG;IACH,aAAa,CAAC,MAAmB,EAAE,UAA8B,EAAE,UAA8C;QAC7G,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE;YAC7B,MAAM,CAAC,IAAI,CAAC,yFAAyF,CAAC,CAAC;SAC1G;aAAM,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,UAAU,EAAE;YACpC,MAAM,CAAC,IAAI,CAAC,yFAAyF,CAAC,CAAC;SAC1G;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAE5C,IAAI,KAAa,CAAC;QAClB,IAAI,GAAW,CAAC;QAChB,IAAI,EAAU,CAAC;QAEf,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC5C,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;gBACf,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;gBAE3B,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtC,IAAI,CAAC,WAAW,EAAE;oBACd,SAAS;iBACZ;gBACD,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBACpF,IAAI,CAAC,aAAa,EAAE;oBAChB,SAAS;iBACZ;gBAED,MAAM,aAAa,GAAG,EAAE,CAAC;gBACzB,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAElD,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,MAAM,YAAY,GAAI,IAAa,CAAC,4BAA4B,EAAE,CAAC;oBACnE,KAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,YAAY,CAAC,MAAM,EAAE,aAAa,EAAE,EAAE;wBAC9E,MAAM,SAAS,GAAG,IAAI,MAAM,EAAE,CAAC;wBAC/B,MAAM,UAAU,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC;wBAC/C,UAAU,CAAC,aAAa,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;wBACjD,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;qBACjC;iBACJ;qBAAM;oBACH,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBACnC;gBAED,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;oBACzE,MAAM,EAAE,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;oBACtC,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;wBAC3C,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;qBAC3C;oBAED,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;oBACnC,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;oBAChC,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE;wBAC7C,OAAO,CAAC,cAAc,CAAC,aAAa,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;wBACpD,OAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,EAAE,EAAE,WAAW,CAAC,CAAC;wBAC7D,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;qBAC/D;oBAED,MAAM,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;iBACtC;aACJ;SACJ;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,UAAU,EAAE;YAC5B,gCAAgC;YAChC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;YACnF,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,UAAU,CAAC;gBAChC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACvB,CAAC,CAAC;SACL;aAAM;YACH,iBAAiB;YACjB,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YACzC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;YACtB,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;YACtB,EAAE,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,EAAE,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,EAAE,CAAC,kBAAkB,GAAG,UAAU,CAAC,kBAAkB,CAAC;YACtD,EAAE,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;YAC9C,EAAE,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;YAC5C,EAAE,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;YAC9C,EAAE,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;YACtC,EAAE,CAAC,sBAAsB,GAAG,UAAU,CAAC,sBAAsB,CAAC;YAC9D,EAAE,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;YAC5C,EAAE,CAAC,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;YAChD,EAAE,CAAC,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;YAChD,EAAE,CAAC,gBAAgB,GAAG,UAAU,CAAC,gBAAgB,CAAC;YAClD,EAAE,CAAC,oBAAoB,GAAG,UAAU,CAAC,oBAAoB,CAAC;YAE1D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;SACtE;IACL,CAAC;IAED;;;;OAIG;IACH,kBAAkB,CAAC,KAAY;QAC3B,IAAI,GAAW,CAAC;QAChB,IAAI,EAAU,CAAC;QACf,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QACpD,MAAM,aAAa,GAAG,YAAY,CAAC,gBAAgB,EAAE,CAAC;QAEtD,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,aAAa,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE;YAC1C,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACrB;QACD,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,aAAa,EAAE,GAAG,EAAE,EAAE;YACtC,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE;gBACvB,MAAM,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACzD,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;aAC7C;SACJ;QAED,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QAC7C,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;QAEpC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAC7B,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;QACjC,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACH,eAAe,CAAC,QAAiB;QAC7B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5C,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,oBAAoB,CAAC,QAAiB,EAAE,MAAe;QACnD,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACH,oBAAoB,CAAC,QAAiB,EAAE,SAAiB;QACrD,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACzE,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5C,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,yBAAyB,CAAC,QAAiB,EAAE,SAAiB,EAAE,MAAe;QAC3E,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACzE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACH,SAAS,CAAC,QAAiB,EAAE,WAAoB;QAC7C,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;QACjC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;QACjC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACnE,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5C,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,cAAc,CAAC,QAAiB,EAAE,WAAoB,EAAE,MAAe;QACnE,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;QACjC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;QACjC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACnE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IAEO,qBAAqB,CAAC,OAAY;QACtC,IAAI,EAAU,CAAC;QACf,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,EAAE,EAAE;YAChC,MAAM,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9C;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;OAMG;IACH,WAAW,CAAC,KAAc,EAAE,GAAY;QACpC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;OAMG;IACH,iBAAiB,CAAC,KAAc,EAAE,GAAY;QAC1C,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/E,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IACD;;;;;;OAMG;IACH,WAAW,CAAC,SAAiB,EAAE,cAAsB,EAAE,KAAY;QAC/D,MAAM,KAAK,GAAG,IAAI,aAAa,CAAC,IAAI,EAAE,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;QACxE,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACH,qBAAqB,CAAC,MAAe;QACjC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG;IACH,qBAAqB;QACjB,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC/C,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;IAED;;;OAGG;IACH,oBAAoB,CAAC,IAAgB;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAEnD,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACnF,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEnB,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;QAC7C,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,CAAC;QACtC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC5C,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAEvC,cAAc;QACd,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACH,cAAc;QACV,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;QACxG,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC7C,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACjB,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAC1C,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,0BAA0B,CAAC,MAAe;QACtC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC/C,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,OAAO,KAAI,CAAC;IAEnB;;;;;;OAMG;IACH,mBAAmB,CAAC,QAAiB,EAAE,MAAc,EAAE,MAAc;QACjE,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;;OAMG;IACH,cAAc,CAAC,QAAiB,EAAE,MAAe,EAAE,KAAa;QAC5D,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAC9E,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,QAAmB;QAC9B,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,IAAY;QAC7B,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,aAAa;IA4CtB;;;;;;;OAOG;IACH,YAAmB,MAAsB,EAAE,SAAiB,EAAE,cAAsB,EAAE,KAAY;QA/ClG;;WAEG;QACI,gBAAW,GAAQ,EAAE,CAAC;QAC7B;;WAEG;QACI,eAAU,GAAoB,IAAI,KAAK,EAAiB,CAAC;QAChE;;WAEG;QACI,WAAM,GAAa,IAAI,KAAK,EAAU,CAAC;QAC9C;;WAEG;QACI,eAAU,GAAa,IAAI,KAAK,EAAU,CAAC;QAClD;;WAEG;QACK,2BAAsB,GAAc,IAAI,KAAK,EAAW,CAAC;QACjE;;WAEG;QACK,sBAAiB,GAAc,IAAI,KAAK,EAAW,CAAC;QAM5D;;WAEG;QACK,gCAA2B,GAA8B,IAAI,CAAC;QAEtE;;WAEG;QACI,4BAAuB,GAAG,IAAI,UAAU,EAAgD,CAAC;QAW5F,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,EAAE,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;QAClI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;YAC3E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,GAAG,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,QAAQ,CAAC,GAAY,EAAE,UAA4B,EAAE,SAAwB;QACzE,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;QAC5E,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;QACvC,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;QACvC,WAAW,CAAC,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;QACzD,WAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;QAC3C,WAAW,CAAC,mBAAmB,GAAG,UAAU,CAAC,mBAAmB,CAAC;QACjE,WAAW,CAAC,qBAAqB,GAAG,UAAU,CAAC,qBAAqB,CAAC;QACrE,WAAW,CAAC,gBAAgB,GAAG,UAAU,CAAC,gBAAgB,CAAC;QAC3D,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC;QAC5B,WAAW,CAAC,qBAAqB,GAAG,CAAC,CAAC;QACtC,WAAW,CAAC,eAAe,GAAG,CAAC,CAAC;QAChC,WAAW,CAAC,QAAQ,GAAG,CAAC,CAAC;QAEzB,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QACxH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC1F,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACH,gBAAgB,CAAC,KAAa;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC1D,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACH,qBAAqB,CAAC,KAAa,EAAE,MAAe;QAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC1D,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;;;OAIG;IACH,gBAAgB,CAAC,KAAa;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC1D,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACH,qBAAqB,CAAC,KAAa,EAAE,MAAe;QAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC1D,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;;;OAIG;IACH,sBAAsB,CAAC,KAAa;QAChC,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;QACrE,OAAO,IAAI,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED;;;;OAIG;IACH,2BAA2B,CAAC,KAAa,EAAE,MAAe;QACtD,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;QACrE,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACH,aAAa,CAAC,KAAa;QACvB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IACjD,CAAC;IAED;;;;OAIG;IACH,qBAAqB,CAAC,KAAa;QAC/B,OAAO,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACzD,CAAC;IAED;;;;OAIG;IACH,SAAS,CAAC,KAAa,EAAE,WAAoB;QACzC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAExH,eAAe;QACf,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE;YACX,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;YACzC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;SACjF;IACL,CAAC;IAED;;;;OAIG;IACH,aAAa,CAAC,KAAa,EAAE,WAAoB;QAC7C,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAChI,CAAC;IAED;;;;OAIG;IACH,qBAAqB,CAAC,KAAa,EAAE,UAA4B;QAC7D,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAE/D,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,EAAE;YACjC,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;SAC1C;QACD,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,EAAE;YACjC,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;SAC1C;QACD,IAAI,UAAU,CAAC,eAAe,KAAK,SAAS,EAAE;YAC1C,WAAW,CAAC,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;SAC5D;QACD,IAAI,UAAU,CAAC,QAAQ,KAAK,SAAS,EAAE;YACnC,WAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;SAC9C;QACD,IAAI,UAAU,CAAC,mBAAmB,KAAK,SAAS,EAAE;YAC9C,WAAW,CAAC,mBAAmB,GAAG,UAAU,CAAC,mBAAmB,CAAC;SACpE;QACD,IAAI,UAAU,CAAC,qBAAqB,KAAK,SAAS,EAAE;YAChD,WAAW,CAAC,qBAAqB,GAAG,UAAU,CAAC,qBAAqB,CAAC;SACxE;QACD,IAAI,UAAU,CAAC,gBAAgB,KAAK,SAAS,EAAE;YAC3C,WAAW,CAAC,gBAAgB,GAAG,UAAU,CAAC,gBAAgB,CAAC;SAC9D;QAED,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;IAC5D,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,KAAa;QACrB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEpC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE;YACX,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;SAC1C;IACL,CAAC;IAED;;;OAGG;IACH,SAAS;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,SAAiB;QACpB,mBAAmB;QACnB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QAEtC,IAAI,SAAS,IAAI,OAAO,EAAE;YACtB,OAAO;SACV;QACD,eAAe;QACf,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;QACpD,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC;QACnE,IAAI,QAAQ,IAAI,OAAO,EAAE;YACrB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SACtC;aAAM;YACH,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC;YACtD,IAAI,YAAY,IAAI,cAAc,GAAG,YAAY,EAAE;gBAC/C,cAAc,GAAG,YAAY,CAAC;aACjC;YACD,IAAI,cAAc,GAAG,CAAC,EAAE;gBACpB,cAAc,GAAG,CAAC,CAAC;aACtB;YAED,MAAM,IAAI,GAAG,SAAS,GAAG,cAAc,CAAC;YACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;gBACrC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;aACjC;SACJ;QAED,oBAAoB;QACpB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,4BAA4B;YAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACxD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,aAAa,CAAC;YAChD,gCAAgC;YAChC,IAAI,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE;gBACpC,MAAM,EAAE,GAAG,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC7D,MAAM,EAAE,GAAG,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACtC,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACzE,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBAC1E,MAAM,iBAAiB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;gBAC5C,IAAI,aAAa,CAAC,CAAC,GAAG,OAAO,IAAI,aAAa,CAAC,CAAC,GAAG,QAAQ,IAAI,iBAAiB,GAAG,MAAM,GAAG,MAAM,EAAE;oBAChG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;oBAC3C,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;iBACxH;aACJ;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACH,qBAAqB,CAAC,MAAe;QACjC,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;QAClF,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;IAChD,CAAC;IAED;;;OAGG;IACH,qBAAqB;QACjB,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;IAED;;;OAGG;IACH,0BAA0B,CAAC,MAAe;QACtC,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC;QACnD,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACH,UAAU,CAAC,KAAa;QACpB,IAAI,EAAU,CAAC;QACf,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACnD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,EAAE,EAAE;YAChC,MAAM,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9C;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,OAAO;QACH,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAClF,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;QACxC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;IACzC,CAAC;CACJ", "sourcesContent": ["import type { INavigationEnginePlugin, ICrowd, IAgentParameters, INavMeshParameters, IObstacle } from \"../../Navigation/INavigationEngine\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport { VertexData } from \"../../Meshes/mesh.vertexData\";\r\nimport { Mesh } from \"../../Meshes/mesh\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Epsilon, Vector3, Matrix } from \"../../Maths/math\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { VertexBuffer } from \"../../Buffers/buffer\";\r\n\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\ndeclare let Recast: any;\r\n\r\n/**\r\n * RecastJS navigation plugin\r\n */\r\nexport class RecastJSPlugin implements INavigationEnginePlugin {\r\n    /**\r\n     * Reference to the Recast library\r\n     */\r\n    public bjsRECAST: any = {};\r\n\r\n    /**\r\n     * plugin name\r\n     */\r\n    public name: string = \"RecastJSPlugin\";\r\n\r\n    /**\r\n     * the first navmesh created. We might extend this to support multiple navmeshes\r\n     */\r\n    public navMesh: any;\r\n\r\n    private _maximumSubStepCount: number = 10;\r\n    private _timeStep: number = 1 / 60;\r\n    private _timeFactor: number = 1;\r\n\r\n    private _tempVec1: any;\r\n    private _tempVec2: any;\r\n\r\n    private _worker: Nullable<Worker> = null;\r\n\r\n    /**\r\n     * Initializes the recastJS plugin\r\n     * @param recastInjection can be used to inject your own recast reference\r\n     */\r\n    public constructor(recastInjection: any = Recast) {\r\n        if (typeof recastInjection === \"function\") {\r\n            Logger.Error(\"RecastJS is not ready. Please make sure you await Recast() before using the plugin.\");\r\n        } else {\r\n            this.bjsRECAST = recastInjection;\r\n        }\r\n\r\n        if (!this.isSupported()) {\r\n            Logger.Error(\"RecastJS is not available. Please make sure you included the js file.\");\r\n            return;\r\n        }\r\n        this.setTimeStep();\r\n\r\n        this._tempVec1 = new this.bjsRECAST.Vec3();\r\n        this._tempVec2 = new this.bjsRECAST.Vec3();\r\n    }\r\n\r\n    /**\r\n     * Set worker URL to be used when generating a new navmesh\r\n     * @param workerURL url string\r\n     * @returns boolean indicating if worker is created\r\n     */\r\n    public setWorkerURL(workerURL: string | URL): boolean {\r\n        if (window && window.Worker) {\r\n            this._worker = new Worker(workerURL);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Set the time step of the navigation tick update.\r\n     * Default is 1/60.\r\n     * A value of 0 will disable fixed time update\r\n     * @param newTimeStep the new timestep to apply to this world.\r\n     */\r\n    setTimeStep(newTimeStep: number = 1 / 60): void {\r\n        this._timeStep = newTimeStep;\r\n    }\r\n\r\n    /**\r\n     * Get the time step of the navigation tick update.\r\n     * @returns the current time step\r\n     */\r\n    getTimeStep(): number {\r\n        return this._timeStep;\r\n    }\r\n\r\n    /**\r\n     * If delta time in navigation tick update is greater than the time step\r\n     * a number of sub iterations are done. If more iterations are need to reach deltatime\r\n     * they will be discarded.\r\n     * A value of 0 will set to no maximum and update will use as many substeps as needed\r\n     * @param newStepCount the maximum number of iterations\r\n     */\r\n    setMaximumSubStepCount(newStepCount: number = 10): void {\r\n        this._maximumSubStepCount = newStepCount;\r\n    }\r\n\r\n    /**\r\n     * Get the maximum number of iterations per navigation tick update\r\n     * @returns the maximum number of iterations\r\n     */\r\n    getMaximumSubStepCount(): number {\r\n        return this._maximumSubStepCount;\r\n    }\r\n\r\n    /**\r\n     * Time factor applied when updating crowd agents (default 1). A value of 0 will pause crowd updates.\r\n     * @param value the time factor applied at update\r\n     */\r\n    public set timeFactor(value: number) {\r\n        this._timeFactor = Math.max(value, 0);\r\n    }\r\n\r\n    /**\r\n     * Get the time factor used for crowd agent update\r\n     * @returns the time factor\r\n     */\r\n    public get timeFactor(): number {\r\n        return this._timeFactor;\r\n    }\r\n\r\n    /**\r\n     * Creates a navigation mesh\r\n     * @param meshes array of all the geometry used to compute the navigation mesh\r\n     * @param parameters bunch of parameters used to filter geometry\r\n     * @param completion callback when data is available from the worker. Not used without a worker\r\n     */\r\n    createNavMesh(meshes: Array<Mesh>, parameters: INavMeshParameters, completion?: (navmeshData: Uint8Array) => void): void {\r\n        if (this._worker && !completion) {\r\n            Logger.Warn(\"A worker is avaible but no completion callback. Defaulting to blocking navmesh creation\");\r\n        } else if (!this._worker && completion) {\r\n            Logger.Warn(\"A completion callback is avaible but no worker. Defaulting to blocking navmesh creation\");\r\n        }\r\n\r\n        this.navMesh = new this.bjsRECAST.NavMesh();\r\n\r\n        let index: number;\r\n        let tri: number;\r\n        let pt: number;\r\n\r\n        const indices = [];\r\n        const positions = [];\r\n        let offset = 0;\r\n        for (index = 0; index < meshes.length; index++) {\r\n            if (meshes[index]) {\r\n                const mesh = meshes[index];\r\n\r\n                const meshIndices = mesh.getIndices();\r\n                if (!meshIndices) {\r\n                    continue;\r\n                }\r\n                const meshPositions = mesh.getVerticesData(VertexBuffer.PositionKind, false, false);\r\n                if (!meshPositions) {\r\n                    continue;\r\n                }\r\n\r\n                const worldMatrices = [];\r\n                const worldMatrix = mesh.computeWorldMatrix(true);\r\n\r\n                if (mesh.hasThinInstances) {\r\n                    const thinMatrices = (mesh as Mesh).thinInstanceGetWorldMatrices();\r\n                    for (let instanceIndex = 0; instanceIndex < thinMatrices.length; instanceIndex++) {\r\n                        const tmpMatrix = new Matrix();\r\n                        const thinMatrix = thinMatrices[instanceIndex];\r\n                        thinMatrix.multiplyToRef(worldMatrix, tmpMatrix);\r\n                        worldMatrices.push(tmpMatrix);\r\n                    }\r\n                } else {\r\n                    worldMatrices.push(worldMatrix);\r\n                }\r\n\r\n                for (let matrixIndex = 0; matrixIndex < worldMatrices.length; matrixIndex++) {\r\n                    const wm = worldMatrices[matrixIndex];\r\n                    for (tri = 0; tri < meshIndices.length; tri++) {\r\n                        indices.push(meshIndices[tri] + offset);\r\n                    }\r\n\r\n                    const transformed = Vector3.Zero();\r\n                    const position = Vector3.Zero();\r\n                    for (pt = 0; pt < meshPositions.length; pt += 3) {\r\n                        Vector3.FromArrayToRef(meshPositions, pt, position);\r\n                        Vector3.TransformCoordinatesToRef(position, wm, transformed);\r\n                        positions.push(transformed.x, transformed.y, transformed.z);\r\n                    }\r\n\r\n                    offset += meshPositions.length / 3;\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._worker && completion) {\r\n            // spawn worker and send message\r\n            this._worker.postMessage([positions, offset, indices, indices.length, parameters]);\r\n            this._worker.onmessage = function (e) {\r\n                completion(e.data);\r\n            };\r\n        } else {\r\n            // blocking calls\r\n            const rc = new this.bjsRECAST.rcConfig();\r\n            rc.cs = parameters.cs;\r\n            rc.ch = parameters.ch;\r\n            rc.borderSize = parameters.borderSize ? parameters.borderSize : 0;\r\n            rc.tileSize = parameters.tileSize ? parameters.tileSize : 0;\r\n            rc.walkableSlopeAngle = parameters.walkableSlopeAngle;\r\n            rc.walkableHeight = parameters.walkableHeight;\r\n            rc.walkableClimb = parameters.walkableClimb;\r\n            rc.walkableRadius = parameters.walkableRadius;\r\n            rc.maxEdgeLen = parameters.maxEdgeLen;\r\n            rc.maxSimplificationError = parameters.maxSimplificationError;\r\n            rc.minRegionArea = parameters.minRegionArea;\r\n            rc.mergeRegionArea = parameters.mergeRegionArea;\r\n            rc.maxVertsPerPoly = parameters.maxVertsPerPoly;\r\n            rc.detailSampleDist = parameters.detailSampleDist;\r\n            rc.detailSampleMaxError = parameters.detailSampleMaxError;\r\n\r\n            this.navMesh.build(positions, offset, indices, indices.length, rc);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create a navigation mesh debug mesh\r\n     * @param scene is where the mesh will be added\r\n     * @returns debug display mesh\r\n     */\r\n    createDebugNavMesh(scene: Scene): Mesh {\r\n        let tri: number;\r\n        let pt: number;\r\n        const debugNavMesh = this.navMesh.getDebugNavMesh();\r\n        const triangleCount = debugNavMesh.getTriangleCount();\r\n\r\n        const indices = [];\r\n        const positions = [];\r\n        for (tri = 0; tri < triangleCount * 3; tri++) {\r\n            indices.push(tri);\r\n        }\r\n        for (tri = 0; tri < triangleCount; tri++) {\r\n            for (pt = 0; pt < 3; pt++) {\r\n                const point = debugNavMesh.getTriangle(tri).getPoint(pt);\r\n                positions.push(point.x, point.y, point.z);\r\n            }\r\n        }\r\n\r\n        const mesh = new Mesh(\"NavMeshDebug\", scene);\r\n        const vertexData = new VertexData();\r\n\r\n        vertexData.indices = indices;\r\n        vertexData.positions = positions;\r\n        vertexData.applyToMesh(mesh, false);\r\n        return mesh;\r\n    }\r\n\r\n    /**\r\n     * Get a navigation mesh constrained position, closest to the parameter position\r\n     * @param position world position\r\n     * @returns the closest point to position constrained by the navigation mesh\r\n     */\r\n    getClosestPoint(position: Vector3): Vector3 {\r\n        this._tempVec1.x = position.x;\r\n        this._tempVec1.y = position.y;\r\n        this._tempVec1.z = position.z;\r\n        const ret = this.navMesh.getClosestPoint(this._tempVec1);\r\n        const pr = new Vector3(ret.x, ret.y, ret.z);\r\n        return pr;\r\n    }\r\n\r\n    /**\r\n     * Get a navigation mesh constrained position, closest to the parameter position\r\n     * @param position world position\r\n     * @param result output the closest point to position constrained by the navigation mesh\r\n     */\r\n    getClosestPointToRef(position: Vector3, result: Vector3): void {\r\n        this._tempVec1.x = position.x;\r\n        this._tempVec1.y = position.y;\r\n        this._tempVec1.z = position.z;\r\n        const ret = this.navMesh.getClosestPoint(this._tempVec1);\r\n        result.set(ret.x, ret.y, ret.z);\r\n    }\r\n\r\n    /**\r\n     * Get a navigation mesh constrained position, within a particular radius\r\n     * @param position world position\r\n     * @param maxRadius the maximum distance to the constrained world position\r\n     * @returns the closest point to position constrained by the navigation mesh\r\n     */\r\n    getRandomPointAround(position: Vector3, maxRadius: number): Vector3 {\r\n        this._tempVec1.x = position.x;\r\n        this._tempVec1.y = position.y;\r\n        this._tempVec1.z = position.z;\r\n        const ret = this.navMesh.getRandomPointAround(this._tempVec1, maxRadius);\r\n        const pr = new Vector3(ret.x, ret.y, ret.z);\r\n        return pr;\r\n    }\r\n\r\n    /**\r\n     * Get a navigation mesh constrained position, within a particular radius\r\n     * @param position world position\r\n     * @param maxRadius the maximum distance to the constrained world position\r\n     * @param result output the closest point to position constrained by the navigation mesh\r\n     */\r\n    getRandomPointAroundToRef(position: Vector3, maxRadius: number, result: Vector3): void {\r\n        this._tempVec1.x = position.x;\r\n        this._tempVec1.y = position.y;\r\n        this._tempVec1.z = position.z;\r\n        const ret = this.navMesh.getRandomPointAround(this._tempVec1, maxRadius);\r\n        result.set(ret.x, ret.y, ret.z);\r\n    }\r\n\r\n    /**\r\n     * Compute the final position from a segment made of destination-position\r\n     * @param position world position\r\n     * @param destination world position\r\n     * @returns the resulting point along the navmesh\r\n     */\r\n    moveAlong(position: Vector3, destination: Vector3): Vector3 {\r\n        this._tempVec1.x = position.x;\r\n        this._tempVec1.y = position.y;\r\n        this._tempVec1.z = position.z;\r\n        this._tempVec2.x = destination.x;\r\n        this._tempVec2.y = destination.y;\r\n        this._tempVec2.z = destination.z;\r\n        const ret = this.navMesh.moveAlong(this._tempVec1, this._tempVec2);\r\n        const pr = new Vector3(ret.x, ret.y, ret.z);\r\n        return pr;\r\n    }\r\n\r\n    /**\r\n     * Compute the final position from a segment made of destination-position\r\n     * @param position world position\r\n     * @param destination world position\r\n     * @param result output the resulting point along the navmesh\r\n     */\r\n    moveAlongToRef(position: Vector3, destination: Vector3, result: Vector3): void {\r\n        this._tempVec1.x = position.x;\r\n        this._tempVec1.y = position.y;\r\n        this._tempVec1.z = position.z;\r\n        this._tempVec2.x = destination.x;\r\n        this._tempVec2.y = destination.y;\r\n        this._tempVec2.z = destination.z;\r\n        const ret = this.navMesh.moveAlong(this._tempVec1, this._tempVec2);\r\n        result.set(ret.x, ret.y, ret.z);\r\n    }\r\n\r\n    private _convertNavPathPoints(navPath: any): Vector3[] {\r\n        let pt: number;\r\n        const pointCount = navPath.getPointCount();\r\n        const positions = [];\r\n        for (pt = 0; pt < pointCount; pt++) {\r\n            const p = navPath.getPoint(pt);\r\n            positions.push(new Vector3(p.x, p.y, p.z));\r\n        }\r\n        return positions;\r\n    }\r\n\r\n    /**\r\n     * Compute a navigation path from start to end. Returns an empty array if no path can be computed\r\n     * Path is straight.\r\n     * @param start world position\r\n     * @param end world position\r\n     * @returns array containing world position composing the path\r\n     */\r\n    computePath(start: Vector3, end: Vector3): Vector3[] {\r\n        this._tempVec1.x = start.x;\r\n        this._tempVec1.y = start.y;\r\n        this._tempVec1.z = start.z;\r\n        this._tempVec2.x = end.x;\r\n        this._tempVec2.y = end.y;\r\n        this._tempVec2.z = end.z;\r\n        const navPath = this.navMesh.computePath(this._tempVec1, this._tempVec2);\r\n        return this._convertNavPathPoints(navPath);\r\n    }\r\n\r\n    /**\r\n     * Compute a navigation path from start to end. Returns an empty array if no path can be computed.\r\n     * Path follows navigation mesh geometry.\r\n     * @param start world position\r\n     * @param end world position\r\n     * @returns array containing world position composing the path\r\n     */\r\n    computePathSmooth(start: Vector3, end: Vector3): Vector3[] {\r\n        this._tempVec1.x = start.x;\r\n        this._tempVec1.y = start.y;\r\n        this._tempVec1.z = start.z;\r\n        this._tempVec2.x = end.x;\r\n        this._tempVec2.y = end.y;\r\n        this._tempVec2.z = end.z;\r\n        const navPath = this.navMesh.computePathSmooth(this._tempVec1, this._tempVec2);\r\n        return this._convertNavPathPoints(navPath);\r\n    }\r\n    /**\r\n     * Create a new Crowd so you can add agents\r\n     * @param maxAgents the maximum agent count in the crowd\r\n     * @param maxAgentRadius the maximum radius an agent can have\r\n     * @param scene to attach the crowd to\r\n     * @returns the crowd you can add agents to\r\n     */\r\n    createCrowd(maxAgents: number, maxAgentRadius: number, scene: Scene): ICrowd {\r\n        const crowd = new RecastJSCrowd(this, maxAgents, maxAgentRadius, scene);\r\n        return crowd;\r\n    }\r\n\r\n    /**\r\n     * Set the Bounding box extent for doing spatial queries (getClosestPoint, getRandomPointAround, ...)\r\n     * The queries will try to find a solution within those bounds\r\n     * default is (1,1,1)\r\n     * @param extent x,y,z value that define the extent around the queries point of reference\r\n     */\r\n    setDefaultQueryExtent(extent: Vector3): void {\r\n        this._tempVec1.x = extent.x;\r\n        this._tempVec1.y = extent.y;\r\n        this._tempVec1.z = extent.z;\r\n        this.navMesh.setDefaultQueryExtent(this._tempVec1);\r\n    }\r\n\r\n    /**\r\n     * Get the Bounding box extent specified by setDefaultQueryExtent\r\n     * @returns the box extent values\r\n     */\r\n    getDefaultQueryExtent(): Vector3 {\r\n        const p = this.navMesh.getDefaultQueryExtent();\r\n        return new Vector3(p.x, p.y, p.z);\r\n    }\r\n\r\n    /**\r\n     * build the navmesh from a previously saved state using getNavmeshData\r\n     * @param data the Uint8Array returned by getNavmeshData\r\n     */\r\n    buildFromNavmeshData(data: Uint8Array): void {\r\n        const nDataBytes = data.length * data.BYTES_PER_ELEMENT;\r\n        const dataPtr = this.bjsRECAST._malloc(nDataBytes);\r\n\r\n        const dataHeap = new Uint8Array(this.bjsRECAST.HEAPU8.buffer, dataPtr, nDataBytes);\r\n        dataHeap.set(data);\r\n\r\n        const buf = new this.bjsRECAST.NavmeshData();\r\n        buf.dataPointer = dataHeap.byteOffset;\r\n        buf.size = data.length;\r\n        this.navMesh = new this.bjsRECAST.NavMesh();\r\n        this.navMesh.buildFromNavmeshData(buf);\r\n\r\n        // Free memory\r\n        this.bjsRECAST._free(dataHeap.byteOffset);\r\n    }\r\n\r\n    /**\r\n     * returns the navmesh data that can be used later. The navmesh must be built before retrieving the data\r\n     * @returns data the Uint8Array that can be saved and reused\r\n     */\r\n    getNavmeshData(): Uint8Array {\r\n        const navmeshData = this.navMesh.getNavmeshData();\r\n        const arrView = new Uint8Array(this.bjsRECAST.HEAPU8.buffer, navmeshData.dataPointer, navmeshData.size);\r\n        const ret = new Uint8Array(navmeshData.size);\r\n        ret.set(arrView);\r\n        this.navMesh.freeNavmeshData(navmeshData);\r\n        return ret;\r\n    }\r\n\r\n    /**\r\n     * Get the Bounding box extent result specified by setDefaultQueryExtent\r\n     * @param result output the box extent values\r\n     */\r\n    getDefaultQueryExtentToRef(result: Vector3): void {\r\n        const p = this.navMesh.getDefaultQueryExtent();\r\n        result.set(p.x, p.y, p.z);\r\n    }\r\n\r\n    /**\r\n     * Disposes\r\n     */\r\n    public dispose() {}\r\n\r\n    /**\r\n     * Creates a cylinder obstacle and add it to the navigation\r\n     * @param position world position\r\n     * @param radius cylinder radius\r\n     * @param height cylinder height\r\n     * @returns the obstacle freshly created\r\n     */\r\n    addCylinderObstacle(position: Vector3, radius: number, height: number): IObstacle {\r\n        this._tempVec1.x = position.x;\r\n        this._tempVec1.y = position.y;\r\n        this._tempVec1.z = position.z;\r\n        return this.navMesh.addCylinderObstacle(this._tempVec1, radius, height);\r\n    }\r\n\r\n    /**\r\n     * Creates an oriented box obstacle and add it to the navigation\r\n     * @param position world position\r\n     * @param extent box size\r\n     * @param angle angle in radians of the box orientation on Y axis\r\n     * @returns the obstacle freshly created\r\n     */\r\n    addBoxObstacle(position: Vector3, extent: Vector3, angle: number): IObstacle {\r\n        this._tempVec1.x = position.x;\r\n        this._tempVec1.y = position.y;\r\n        this._tempVec1.z = position.z;\r\n        this._tempVec2.x = extent.x;\r\n        this._tempVec2.y = extent.y;\r\n        this._tempVec2.z = extent.z;\r\n        return this.navMesh.addBoxObstacle(this._tempVec1, this._tempVec2, angle);\r\n    }\r\n\r\n    /**\r\n     * Removes an obstacle created by addCylinderObstacle or addBoxObstacle\r\n     * @param obstacle obstacle to remove from the navigation\r\n     */\r\n    removeObstacle(obstacle: IObstacle): void {\r\n        this.navMesh.removeObstacle(obstacle);\r\n    }\r\n\r\n    /**\r\n     * If this plugin is supported\r\n     * @returns true if plugin is supported\r\n     */\r\n    public isSupported(): boolean {\r\n        return this.bjsRECAST !== undefined;\r\n    }\r\n\r\n    /**\r\n     * Returns the seed used for randomized functions like `getRandomPointAround`\r\n     * @returns seed number\r\n     */\r\n    public getRandomSeed(): number {\r\n        return this.bjsRECAST._getRandomSeed();\r\n    }\r\n\r\n    /**\r\n     * Set the seed used for randomized functions like `getRandomPointAround`\r\n     * @param seed number used as seed for random functions\r\n     */\r\n    public setRandomSeed(seed: number): void {\r\n        this.bjsRECAST._setRandomSeed(seed);\r\n    }\r\n}\r\n\r\n/**\r\n * Recast detour crowd implementation\r\n */\r\nexport class RecastJSCrowd implements ICrowd {\r\n    /**\r\n     * Recast/detour plugin\r\n     */\r\n    public bjsRECASTPlugin: RecastJSPlugin;\r\n    /**\r\n     * Link to the detour crowd\r\n     */\r\n    public recastCrowd: any = {};\r\n    /**\r\n     * One transform per agent\r\n     */\r\n    public transforms: TransformNode[] = new Array<TransformNode>();\r\n    /**\r\n     * All agents created\r\n     */\r\n    public agents: number[] = new Array<number>();\r\n    /**\r\n     * agents reach radius\r\n     */\r\n    public reachRadii: number[] = new Array<number>();\r\n    /**\r\n     * true when a destination is active for an agent and notifier hasn't been notified of reach\r\n     */\r\n    private _agentDestinationArmed: boolean[] = new Array<boolean>();\r\n    /**\r\n     * agent current target\r\n     */\r\n    private _agentDestination: Vector3[] = new Array<Vector3>();\r\n    /**\r\n     * Link to the scene is kept to unregister the crowd from the scene\r\n     */\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * Observer for crowd updates\r\n     */\r\n    private _onBeforeAnimationsObserver: Nullable<Observer<Scene>> = null;\r\n\r\n    /**\r\n     * Fires each time an agent is in reach radius of its destination\r\n     */\r\n    public onReachTargetObservable = new Observable<{ agentIndex: number; destination: Vector3 }>();\r\n\r\n    /**\r\n     * Constructor\r\n     * @param plugin recastJS plugin\r\n     * @param maxAgents the maximum agent count in the crowd\r\n     * @param maxAgentRadius the maximum radius an agent can have\r\n     * @param scene to attach the crowd to\r\n     * @returns the crowd you can add agents to\r\n     */\r\n    public constructor(plugin: RecastJSPlugin, maxAgents: number, maxAgentRadius: number, scene: Scene) {\r\n        this.bjsRECASTPlugin = plugin;\r\n        this.recastCrowd = new this.bjsRECASTPlugin.bjsRECAST.Crowd(maxAgents, maxAgentRadius, this.bjsRECASTPlugin.navMesh.getNavMesh());\r\n        this._scene = scene;\r\n\r\n        this._onBeforeAnimationsObserver = scene.onBeforeAnimationsObservable.add(() => {\r\n            this.update(scene.getEngine().getDeltaTime() * 0.001 * plugin.timeFactor);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Add a new agent to the crowd with the specified parameter a corresponding transformNode.\r\n     * You can attach anything to that node. The node position is updated in the scene update tick.\r\n     * @param pos world position that will be constrained by the navigation mesh\r\n     * @param parameters agent parameters\r\n     * @param transform hooked to the agent that will be update by the scene\r\n     * @returns agent index\r\n     */\r\n    addAgent(pos: Vector3, parameters: IAgentParameters, transform: TransformNode): number {\r\n        const agentParams = new this.bjsRECASTPlugin.bjsRECAST.dtCrowdAgentParams();\r\n        agentParams.radius = parameters.radius;\r\n        agentParams.height = parameters.height;\r\n        agentParams.maxAcceleration = parameters.maxAcceleration;\r\n        agentParams.maxSpeed = parameters.maxSpeed;\r\n        agentParams.collisionQueryRange = parameters.collisionQueryRange;\r\n        agentParams.pathOptimizationRange = parameters.pathOptimizationRange;\r\n        agentParams.separationWeight = parameters.separationWeight;\r\n        agentParams.updateFlags = 7;\r\n        agentParams.obstacleAvoidanceType = 0;\r\n        agentParams.queryFilterType = 0;\r\n        agentParams.userData = 0;\r\n\r\n        const agentIndex = this.recastCrowd.addAgent(new this.bjsRECASTPlugin.bjsRECAST.Vec3(pos.x, pos.y, pos.z), agentParams);\r\n        this.transforms.push(transform);\r\n        this.agents.push(agentIndex);\r\n        this.reachRadii.push(parameters.reachRadius ? parameters.reachRadius : parameters.radius);\r\n        this._agentDestinationArmed.push(false);\r\n        this._agentDestination.push(new Vector3(0, 0, 0));\r\n        return agentIndex;\r\n    }\r\n\r\n    /**\r\n     * Returns the agent position in world space\r\n     * @param index agent index returned by addAgent\r\n     * @returns world space position\r\n     */\r\n    getAgentPosition(index: number): Vector3 {\r\n        const agentPos = this.recastCrowd.getAgentPosition(index);\r\n        return new Vector3(agentPos.x, agentPos.y, agentPos.z);\r\n    }\r\n\r\n    /**\r\n     * Returns the agent position result in world space\r\n     * @param index agent index returned by addAgent\r\n     * @param result output world space position\r\n     */\r\n    getAgentPositionToRef(index: number, result: Vector3): void {\r\n        const agentPos = this.recastCrowd.getAgentPosition(index);\r\n        result.set(agentPos.x, agentPos.y, agentPos.z);\r\n    }\r\n\r\n    /**\r\n     * Returns the agent velocity in world space\r\n     * @param index agent index returned by addAgent\r\n     * @returns world space velocity\r\n     */\r\n    getAgentVelocity(index: number): Vector3 {\r\n        const agentVel = this.recastCrowd.getAgentVelocity(index);\r\n        return new Vector3(agentVel.x, agentVel.y, agentVel.z);\r\n    }\r\n\r\n    /**\r\n     * Returns the agent velocity result in world space\r\n     * @param index agent index returned by addAgent\r\n     * @param result output world space velocity\r\n     */\r\n    getAgentVelocityToRef(index: number, result: Vector3): void {\r\n        const agentVel = this.recastCrowd.getAgentVelocity(index);\r\n        result.set(agentVel.x, agentVel.y, agentVel.z);\r\n    }\r\n\r\n    /**\r\n     * Returns the agent next target point on the path\r\n     * @param index agent index returned by addAgent\r\n     * @returns world space position\r\n     */\r\n    getAgentNextTargetPath(index: number): Vector3 {\r\n        const pathTargetPos = this.recastCrowd.getAgentNextTargetPath(index);\r\n        return new Vector3(pathTargetPos.x, pathTargetPos.y, pathTargetPos.z);\r\n    }\r\n\r\n    /**\r\n     * Returns the agent next target point on the path\r\n     * @param index agent index returned by addAgent\r\n     * @param result output world space position\r\n     */\r\n    getAgentNextTargetPathToRef(index: number, result: Vector3): void {\r\n        const pathTargetPos = this.recastCrowd.getAgentNextTargetPath(index);\r\n        result.set(pathTargetPos.x, pathTargetPos.y, pathTargetPos.z);\r\n    }\r\n\r\n    /**\r\n     * Gets the agent state\r\n     * @param index agent index returned by addAgent\r\n     * @returns agent state\r\n     */\r\n    getAgentState(index: number): number {\r\n        return this.recastCrowd.getAgentState(index);\r\n    }\r\n\r\n    /**\r\n     * returns true if the agent in over an off mesh link connection\r\n     * @param index agent index returned by addAgent\r\n     * @returns true if over an off mesh link connection\r\n     */\r\n    overOffmeshConnection(index: number): boolean {\r\n        return this.recastCrowd.overOffmeshConnection(index);\r\n    }\r\n\r\n    /**\r\n     * Asks a particular agent to go to a destination. That destination is constrained by the navigation mesh\r\n     * @param index agent index returned by addAgent\r\n     * @param destination targeted world position\r\n     */\r\n    agentGoto(index: number, destination: Vector3): void {\r\n        this.recastCrowd.agentGoto(index, new this.bjsRECASTPlugin.bjsRECAST.Vec3(destination.x, destination.y, destination.z));\r\n\r\n        // arm observer\r\n        const item = this.agents.indexOf(index);\r\n        if (item > -1) {\r\n            this._agentDestinationArmed[item] = true;\r\n            this._agentDestination[item].set(destination.x, destination.y, destination.z);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Teleport the agent to a new position\r\n     * @param index agent index returned by addAgent\r\n     * @param destination targeted world position\r\n     */\r\n    agentTeleport(index: number, destination: Vector3): void {\r\n        this.recastCrowd.agentTeleport(index, new this.bjsRECASTPlugin.bjsRECAST.Vec3(destination.x, destination.y, destination.z));\r\n    }\r\n\r\n    /**\r\n     * Update agent parameters\r\n     * @param index agent index returned by addAgent\r\n     * @param parameters agent parameters\r\n     */\r\n    updateAgentParameters(index: number, parameters: IAgentParameters): void {\r\n        const agentParams = this.recastCrowd.getAgentParameters(index);\r\n\r\n        if (parameters.radius !== undefined) {\r\n            agentParams.radius = parameters.radius;\r\n        }\r\n        if (parameters.height !== undefined) {\r\n            agentParams.height = parameters.height;\r\n        }\r\n        if (parameters.maxAcceleration !== undefined) {\r\n            agentParams.maxAcceleration = parameters.maxAcceleration;\r\n        }\r\n        if (parameters.maxSpeed !== undefined) {\r\n            agentParams.maxSpeed = parameters.maxSpeed;\r\n        }\r\n        if (parameters.collisionQueryRange !== undefined) {\r\n            agentParams.collisionQueryRange = parameters.collisionQueryRange;\r\n        }\r\n        if (parameters.pathOptimizationRange !== undefined) {\r\n            agentParams.pathOptimizationRange = parameters.pathOptimizationRange;\r\n        }\r\n        if (parameters.separationWeight !== undefined) {\r\n            agentParams.separationWeight = parameters.separationWeight;\r\n        }\r\n\r\n        this.recastCrowd.setAgentParameters(index, agentParams);\r\n    }\r\n\r\n    /**\r\n     * remove a particular agent previously created\r\n     * @param index agent index returned by addAgent\r\n     */\r\n    removeAgent(index: number): void {\r\n        this.recastCrowd.removeAgent(index);\r\n\r\n        const item = this.agents.indexOf(index);\r\n        if (item > -1) {\r\n            this.agents.splice(item, 1);\r\n            this.transforms.splice(item, 1);\r\n            this.reachRadii.splice(item, 1);\r\n            this._agentDestinationArmed.splice(item, 1);\r\n            this._agentDestination.splice(item, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * get the list of all agents attached to this crowd\r\n     * @returns list of agent indices\r\n     */\r\n    getAgents(): number[] {\r\n        return this.agents;\r\n    }\r\n\r\n    /**\r\n     * Tick update done by the Scene. Agent position/velocity/acceleration is updated by this function\r\n     * @param deltaTime in seconds\r\n     */\r\n    update(deltaTime: number): void {\r\n        // update obstacles\r\n        this.bjsRECASTPlugin.navMesh.update();\r\n\r\n        if (deltaTime <= Epsilon) {\r\n            return;\r\n        }\r\n        // update crowd\r\n        const timeStep = this.bjsRECASTPlugin.getTimeStep();\r\n        const maxStepCount = this.bjsRECASTPlugin.getMaximumSubStepCount();\r\n        if (timeStep <= Epsilon) {\r\n            this.recastCrowd.update(deltaTime);\r\n        } else {\r\n            let iterationCount = Math.floor(deltaTime / timeStep);\r\n            if (maxStepCount && iterationCount > maxStepCount) {\r\n                iterationCount = maxStepCount;\r\n            }\r\n            if (iterationCount < 1) {\r\n                iterationCount = 1;\r\n            }\r\n\r\n            const step = deltaTime / iterationCount;\r\n            for (let i = 0; i < iterationCount; i++) {\r\n                this.recastCrowd.update(step);\r\n            }\r\n        }\r\n\r\n        // update transforms\r\n        for (let index = 0; index < this.agents.length; index++) {\r\n            // update transform position\r\n            const agentIndex = this.agents[index];\r\n            const agentPosition = this.getAgentPosition(agentIndex);\r\n            this.transforms[index].position = agentPosition;\r\n            // check agent reach destination\r\n            if (this._agentDestinationArmed[index]) {\r\n                const dx = agentPosition.x - this._agentDestination[index].x;\r\n                const dz = agentPosition.z - this._agentDestination[index].z;\r\n                const radius = this.reachRadii[index];\r\n                const groundY = this._agentDestination[index].y - this.reachRadii[index];\r\n                const ceilingY = this._agentDestination[index].y + this.reachRadii[index];\r\n                const distanceXZSquared = dx * dx + dz * dz;\r\n                if (agentPosition.y > groundY && agentPosition.y < ceilingY && distanceXZSquared < radius * radius) {\r\n                    this._agentDestinationArmed[index] = false;\r\n                    this.onReachTargetObservable.notifyObservers({ agentIndex: agentIndex, destination: this._agentDestination[index] });\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the Bounding box extent for doing spatial queries (getClosestPoint, getRandomPointAround, ...)\r\n     * The queries will try to find a solution within those bounds\r\n     * default is (1,1,1)\r\n     * @param extent x,y,z value that define the extent around the queries point of reference\r\n     */\r\n    setDefaultQueryExtent(extent: Vector3): void {\r\n        const ext = new this.bjsRECASTPlugin.bjsRECAST.Vec3(extent.x, extent.y, extent.z);\r\n        this.recastCrowd.setDefaultQueryExtent(ext);\r\n    }\r\n\r\n    /**\r\n     * Get the Bounding box extent specified by setDefaultQueryExtent\r\n     * @returns the box extent values\r\n     */\r\n    getDefaultQueryExtent(): Vector3 {\r\n        const p = this.recastCrowd.getDefaultQueryExtent();\r\n        return new Vector3(p.x, p.y, p.z);\r\n    }\r\n\r\n    /**\r\n     * Get the Bounding box extent result specified by setDefaultQueryExtent\r\n     * @param result output the box extent values\r\n     */\r\n    getDefaultQueryExtentToRef(result: Vector3): void {\r\n        const p = this.recastCrowd.getDefaultQueryExtent();\r\n        result.set(p.x, p.y, p.z);\r\n    }\r\n\r\n    /**\r\n     * Get the next corner points composing the path (max 4 points)\r\n     * @param index agent index returned by addAgent\r\n     * @returns array containing world position composing the path\r\n     */\r\n    getCorners(index: number): Vector3[] {\r\n        let pt: number;\r\n        const navPath = this.recastCrowd.getCorners(index);\r\n        const pointCount = navPath.getPointCount();\r\n        const positions = [];\r\n        for (pt = 0; pt < pointCount; pt++) {\r\n            const p = navPath.getPoint(pt);\r\n            positions.push(new Vector3(p.x, p.y, p.z));\r\n        }\r\n        return positions;\r\n    }\r\n\r\n    /**\r\n     * Release all resources\r\n     */\r\n    dispose(): void {\r\n        this.recastCrowd.destroy();\r\n        this._scene.onBeforeAnimationsObservable.remove(this._onBeforeAnimationsObserver);\r\n        this._onBeforeAnimationsObserver = null;\r\n        this.onReachTargetObservable.clear();\r\n    }\r\n}\r\n"]}