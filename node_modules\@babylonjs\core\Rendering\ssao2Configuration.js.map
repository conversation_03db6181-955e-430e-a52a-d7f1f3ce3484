{"version": 3, "file": "ssao2Configuration.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Rendering/ssao2Configuration.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAGjD;;;GAGG;AACH,MAAM,OAAO,kBAAkB;IAA/B;QACI;;WAEG;QACI,YAAO,GAAG,KAAK,CAAC;QAEvB;;WAEG;QACI,SAAI,GAAG,OAAO,CAAC;QAEtB;;WAEG;QACa,qBAAgB,GAAa,CAAC,SAAS,CAAC,2BAA2B,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;IAC/H,CAAC;CAAA", "sourcesContent": ["import { Constants } from \"../Engines/constants\";\r\nimport type { PrePassEffectConfiguration } from \"./prePassEffectConfiguration\";\r\n\r\n/**\r\n * Contains all parameters needed for the prepass to perform\r\n * screen space subsurface scattering\r\n */\r\nexport class SSAO2Configuration implements PrePassEffectConfiguration {\r\n    /**\r\n     * Is subsurface enabled\r\n     */\r\n    public enabled = false;\r\n\r\n    /**\r\n     * Name of the configuration\r\n     */\r\n    public name = \"ssao2\";\r\n\r\n    /**\r\n     * Textures that should be present in the MRT for this effect to work\r\n     */\r\n    public readonly texturesRequired: number[] = [Constants.PREPASS_NORMAL_TEXTURE_TYPE, Constants.PREPASS_DEPTH_TEXTURE_TYPE];\r\n}\r\n"]}