{"version": 3, "file": "stereoscopicInterlacePostProcess.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/stereoscopicInterlacePostProcess.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAG/C,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAG5C,OAAO,2CAA2C,CAAC;AAEnD;;GAEG;AACH,MAAM,OAAO,iCAAkC,SAAQ,WAAW;IAI9D;;;OAGG;IACI,YAAY;QACf,OAAO,mCAAmC,CAAC;IAC/C,CAAC;IAED;;;;;;;;;OASG;IACH,YAAY,IAAY,EAAE,UAAoB,EAAE,mBAA4B,EAAE,wBAAiC,EAAE,YAAqB,EAAE,MAAe,EAAE,QAAkB;QACvK,KAAK,CACD,IAAI,EACJ,uBAAuB,EACvB,CAAC,UAAU,CAAC,EACZ,CAAC,aAAa,CAAC,EACf,CAAC,EACD,UAAU,CAAC,CAAC,CAAC,EACb,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,wBAAwB,CAAC,CAAC,CAAC,sCAAsC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,iCAAiC,CAAC,CAAC,CAAC,SAAS,CAC1I,CAAC;QAEF,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC;QACpD,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAE9D,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;YAClC,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAc,EAAE,EAAE;YAC1C,MAAM,CAAC,yBAAyB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACrE,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AACD;;GAEG;AACH,MAAM,OAAO,gCAAiC,SAAQ,WAAW;IAI7D;;;OAGG;IACI,YAAY;QACf,OAAO,kCAAkC,CAAC;IAC9C,CAAC;IAED;;;;;;;;OAQG;IACH,YAAY,IAAY,EAAE,UAAoB,EAAE,mBAA4B,EAAE,YAAqB,EAAE,MAAe,EAAE,QAAkB;QACpI,KAAK,CACD,IAAI,EACJ,uBAAuB,EACvB,CAAC,UAAU,CAAC,EACZ,CAAC,aAAa,CAAC,EACf,CAAC,EACD,UAAU,CAAC,CAAC,CAAC,EACb,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,mBAAmB,CAAC,CAAC,CAAC,iCAAiC,CAAC,CAAC,CAAC,SAAS,CACtE,CAAC;QAEF,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC;QACpD,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAE9D,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;YAClC,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAc,EAAE,EAAE;YAC1C,MAAM,CAAC,yBAAyB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACrE,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACP,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Vector2 } from \"../Maths/math.vector\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { PostProcess } from \"./postProcess\";\r\nimport type { Engine } from \"../Engines/engine\";\r\n\r\nimport \"../Shaders/stereoscopicInterlace.fragment\";\r\n\r\n/**\r\n * StereoscopicInterlacePostProcessI used to render stereo views from a rigged camera with support for alternate line interlacing\r\n */\r\nexport class StereoscopicInterlacePostProcessI extends PostProcess {\r\n    private _stepSize: Vector2;\r\n    private _passedProcess: Nullable<PostProcess>;\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"StereoscopicInterlacePostProcessI\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"StereoscopicInterlacePostProcessI\";\r\n    }\r\n\r\n    /**\r\n     * Initializes a StereoscopicInterlacePostProcessI\r\n     * @param name The name of the effect.\r\n     * @param rigCameras The rig cameras to be applied to the post process\r\n     * @param isStereoscopicHoriz If the rendered results are horizontal or vertical\r\n     * @param isStereoscopicInterlaced If the rendered results are alternate line interlaced\r\n     * @param samplingMode The sampling mode to be used when computing the pass. (default: 0)\r\n     * @param engine The engine which the post process will be applied. (default: current engine)\r\n     * @param reusable If the post process can be reused on the same frame. (default: false)\r\n     */\r\n    constructor(name: string, rigCameras: Camera[], isStereoscopicHoriz: boolean, isStereoscopicInterlaced: boolean, samplingMode?: number, engine?: Engine, reusable?: boolean) {\r\n        super(\r\n            name,\r\n            \"stereoscopicInterlace\",\r\n            [\"stepSize\"],\r\n            [\"camASampler\"],\r\n            1,\r\n            rigCameras[1],\r\n            samplingMode,\r\n            engine,\r\n            reusable,\r\n            isStereoscopicInterlaced ? \"#define IS_STEREOSCOPIC_INTERLACED 1\" : isStereoscopicHoriz ? \"#define IS_STEREOSCOPIC_HORIZ 1\" : undefined\r\n        );\r\n\r\n        this._passedProcess = rigCameras[0]._rigPostProcess;\r\n        this._stepSize = new Vector2(1 / this.width, 1 / this.height);\r\n\r\n        this.onSizeChangedObservable.add(() => {\r\n            this._stepSize = new Vector2(1 / this.width, 1 / this.height);\r\n        });\r\n        this.onApplyObservable.add((effect: Effect) => {\r\n            effect.setTextureFromPostProcess(\"camASampler\", this._passedProcess);\r\n            effect.setFloat2(\"stepSize\", this._stepSize.x, this._stepSize.y);\r\n        });\r\n    }\r\n}\r\n/**\r\n * StereoscopicInterlacePostProcess used to render stereo views from a rigged camera\r\n */\r\nexport class StereoscopicInterlacePostProcess extends PostProcess {\r\n    private _stepSize: Vector2;\r\n    private _passedProcess: Nullable<PostProcess>;\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"StereoscopicInterlacePostProcess\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"StereoscopicInterlacePostProcess\";\r\n    }\r\n\r\n    /**\r\n     * Initializes a StereoscopicInterlacePostProcess\r\n     * @param name The name of the effect.\r\n     * @param rigCameras The rig cameras to be applied to the post process\r\n     * @param isStereoscopicHoriz If the rendered results are horizontal or vertical\r\n     * @param samplingMode The sampling mode to be used when computing the pass. (default: 0)\r\n     * @param engine The engine which the post process will be applied. (default: current engine)\r\n     * @param reusable If the post process can be reused on the same frame. (default: false)\r\n     */\r\n    constructor(name: string, rigCameras: Camera[], isStereoscopicHoriz: boolean, samplingMode?: number, engine?: Engine, reusable?: boolean) {\r\n        super(\r\n            name,\r\n            \"stereoscopicInterlace\",\r\n            [\"stepSize\"],\r\n            [\"camASampler\"],\r\n            1,\r\n            rigCameras[1],\r\n            samplingMode,\r\n            engine,\r\n            reusable,\r\n            isStereoscopicHoriz ? \"#define IS_STEREOSCOPIC_HORIZ 1\" : undefined\r\n        );\r\n\r\n        this._passedProcess = rigCameras[0]._rigPostProcess;\r\n        this._stepSize = new Vector2(1 / this.width, 1 / this.height);\r\n\r\n        this.onSizeChangedObservable.add(() => {\r\n            this._stepSize = new Vector2(1 / this.width, 1 / this.height);\r\n        });\r\n        this.onApplyObservable.add((effect: Effect) => {\r\n            effect.setTextureFromPostProcess(\"camASampler\", this._passedProcess);\r\n            effect.setFloat2(\"stepSize\", this._stepSize.x, this._stepSize.y);\r\n        });\r\n    }\r\n}\r\n"]}