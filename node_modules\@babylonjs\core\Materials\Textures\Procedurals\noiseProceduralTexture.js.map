{"version": 3, "file": "noiseProceduralTexture.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Textures/Procedurals/noiseProceduralTexture.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAE3D,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAExD,OAAO,iCAAiC,CAAC;AAEzC;;GAEG;AACH,MAAM,OAAO,sBAAuB,SAAQ,iBAAiB;IAgBzD;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,OAAe,GAAG,EAAE,QAAyB,WAAW,CAAC,gBAAgB,EAAE,eAAyB,EAAE,eAAyB;QACrJ,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;QAxBxE,iDAAiD;QAC1C,SAAI,GAAW,GAAG,CAAC;QAE1B,6GAA6G;QACtG,eAAU,GAAG,GAAG,CAAC;QAExB,+CAA+C;QACxC,YAAO,GAAG,CAAC,CAAC;QAEnB,wDAAwD;QACjD,gBAAW,GAAG,GAAG,CAAC;QAEzB,yDAAyD;QAClD,yBAAoB,GAAG,CAAC,CAAC;QAY5B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACjC,CAAC;IAEO,qBAAqB;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAE1E,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/C,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAES,WAAW;QACjB,OAAO,kBAAkB,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,oBAA8B;QACxC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,KAAK,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QACpC,mBAAmB,CAAC,UAAU,GAAG,gCAAgC,CAAC;QAElE,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACnD,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;QAChD,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC5D,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAErC,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,UAAU,GAAG,IAAI,sBAAsB,CACzC,IAAI,CAAC,IAAI,EACT,WAAW,CAAC,KAAK,EACjB,IAAI,CAAC,QAAQ,EAAE,EACf,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,EACzD,IAAI,CAAC,gBAAgB,CACxB,CAAC;QAEF,eAAe;QACf,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAE9B,uBAAuB;QACvB,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAElD,kBAAkB;QAClB,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACxC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAClC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QAC1C,UAAU,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC5D,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAE5B,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,aAAkB,EAAE,KAAY;QAChD,MAAM,OAAO,GAAG,IAAI,sBAAsB,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,CAAC,eAAe,CAAC,CAAC;QAEpI,OAAO,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;QAC9C,OAAO,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;QACxC,OAAO,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;QAChD,OAAO,CAAC,oBAAoB,GAAG,aAAa,CAAC,oBAAoB,CAAC;QAClE,OAAO,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,IAAI,CAAC,CAAC;QAEvC,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ;AAED,aAAa,CAAC,gCAAgC,EAAE,sBAAsB,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../../types\";\r\nimport type { Scene } from \"../../../scene\";\r\nimport { EngineStore } from \"../../../Engines/engineStore\";\r\nimport type { Texture } from \"../../../Materials/Textures/texture\";\r\nimport { ProceduralTexture } from \"./proceduralTexture\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\n\r\nimport \"../../../Shaders/noise.fragment\";\r\n\r\n/**\r\n * Class used to generate noise procedural textures\r\n */\r\nexport class NoiseProceduralTexture extends ProceduralTexture {\r\n    /** Gets or sets the start time (default is 0) */\r\n    public time: number = 0.0;\r\n\r\n    /** Gets or sets a value between 0 and 1 indicating the overall brightness of the texture (default is 0.2) */\r\n    public brightness = 0.2;\r\n\r\n    /** Defines the number of octaves to process */\r\n    public octaves = 3;\r\n\r\n    /** Defines the level of persistence (0.8 by default) */\r\n    public persistence = 0.8;\r\n\r\n    /** Gets or sets animation speed factor (default is 1) */\r\n    public animationSpeedFactor = 1;\r\n\r\n    /**\r\n     * Creates a new NoiseProceduralTexture\r\n     * @param name defines the name fo the texture\r\n     * @param size defines the size of the texture (default is 256)\r\n     * @param scene defines the hosting scene\r\n     * @param fallbackTexture defines the texture to use if the NoiseProceduralTexture can't be created\r\n     * @param generateMipMaps defines if mipmaps must be generated (true by default)\r\n     */\r\n    constructor(name: string, size: number = 256, scene: Nullable<Scene> = EngineStore.LastCreatedScene, fallbackTexture?: Texture, generateMipMaps?: boolean) {\r\n        super(name, size, \"noise\", scene, fallbackTexture, generateMipMaps);\r\n        this.autoClear = false;\r\n        this._updateShaderUniforms();\r\n    }\r\n\r\n    private _updateShaderUniforms() {\r\n        const scene = this.getScene();\r\n\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        this.time += scene.getAnimationRatio() * this.animationSpeedFactor * 0.01;\r\n\r\n        this.setFloat(\"brightness\", this.brightness);\r\n        this.setFloat(\"persistence\", this.persistence);\r\n        this.setFloat(\"timeScale\", this.time);\r\n    }\r\n\r\n    protected _getDefines(): string {\r\n        return \"#define OCTAVES \" + (this.octaves | 0);\r\n    }\r\n\r\n    /**\r\n     * Generate the current state of the procedural texture\r\n     * @param useCameraPostProcess Define if camera post process should be applied to the texture\r\n     */\r\n    public render(useCameraPostProcess?: boolean) {\r\n        this._updateShaderUniforms();\r\n        super.render(useCameraPostProcess);\r\n    }\r\n\r\n    /**\r\n     * Serializes this noise procedural texture\r\n     * @returns a serialized noise procedural texture object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n        serializationObject.customType = \"BABYLON.NoiseProceduralTexture\";\r\n\r\n        serializationObject.brightness = this.brightness;\r\n        serializationObject.octaves = this.octaves;\r\n        serializationObject.persistence = this.persistence;\r\n        serializationObject.animationSpeedFactor = this.animationSpeedFactor;\r\n        serializationObject.size = this.getSize().width;\r\n        serializationObject.generateMipMaps = this._generateMipMaps;\r\n        serializationObject.time = this.time;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Clone the texture.\r\n     * @returns the cloned texture\r\n     */\r\n    public clone(): NoiseProceduralTexture {\r\n        const textureSize = this.getSize();\r\n        const newTexture = new NoiseProceduralTexture(\r\n            this.name,\r\n            textureSize.width,\r\n            this.getScene(),\r\n            this._fallbackTexture ? this._fallbackTexture : undefined,\r\n            this._generateMipMaps\r\n        );\r\n\r\n        // Base texture\r\n        newTexture.hasAlpha = this.hasAlpha;\r\n        newTexture.level = this.level;\r\n\r\n        // RenderTarget Texture\r\n        newTexture.coordinatesMode = this.coordinatesMode;\r\n\r\n        // Noise Specifics\r\n        newTexture.brightness = this.brightness;\r\n        newTexture.octaves = this.octaves;\r\n        newTexture.persistence = this.persistence;\r\n        newTexture.animationSpeedFactor = this.animationSpeedFactor;\r\n        newTexture.time = this.time;\r\n\r\n        return newTexture;\r\n    }\r\n\r\n    /**\r\n     * Creates a NoiseProceduralTexture from parsed noise procedural texture data\r\n     * @param parsedTexture defines parsed texture data\r\n     * @param scene defines the current scene\r\n     * @returns a parsed NoiseProceduralTexture\r\n     */\r\n    public static Parse(parsedTexture: any, scene: Scene): NoiseProceduralTexture {\r\n        const texture = new NoiseProceduralTexture(parsedTexture.name, parsedTexture.size, scene, undefined, parsedTexture.generateMipMaps);\r\n\r\n        texture.brightness = parsedTexture.brightness;\r\n        texture.octaves = parsedTexture.octaves;\r\n        texture.persistence = parsedTexture.persistence;\r\n        texture.animationSpeedFactor = parsedTexture.animationSpeedFactor;\r\n        texture.time = parsedTexture.time ?? 0;\r\n\r\n        return texture;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.NoiseProceduralTexture\", NoiseProceduralTexture);\r\n"]}