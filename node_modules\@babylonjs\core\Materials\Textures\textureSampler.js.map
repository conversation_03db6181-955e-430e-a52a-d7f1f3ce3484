{"version": 3, "file": "textureSampler.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/textureSampler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAGpD;;GAEG;AACH,MAAM,OAAO,cAAc;IAMvB;;;;;;OAMG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,KAAK,CAAC,KAAuB;QACpC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC9B,CAAC;IAED;;;;;;OAMG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,KAAK,CAAC,KAAuB;QACpC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC9B,CAAC;IAED;;;;;;OAMG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,KAAK,CAAC,KAAuB;QACpC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,gCAAgC,CAAC;IACjD,CAAC;IAED,IAAW,yBAAyB,CAAC,KAAuB;QACxD,IAAI,CAAC,gCAAgC,GAAG,KAAK,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAa;QACvC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;IACrC,CAAC;IAGD;;;OAGG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAc;QAChC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAsBD;;OAEG;IACH;QA9GA;;WAEG;QACI,iBAAY,GAAW,CAAC,CAAC,CAAC;QAuEzB,gBAAW,GAAG,IAAI,CAAC;QAa3B,gBAAgB;QACT,iBAAY,GAAqB,IAAI,CAAC;QAE7C,gBAAgB;QACT,iBAAY,GAAqB,IAAI,CAAC;QAE7C,gBAAgB;QACT,iBAAY,GAAqB,IAAI,CAAC;QAE7C,gBAAgB;QACT,qCAAgC,GAAqB,IAAI,CAAC;QAEjE,gBAAgB;QACT,wBAAmB,GAAW,CAAC,CAAC;IAUxB,CAAC;IAEhB;;;;;;;;;OASG;IACI,aAAa,CAChB,KAAK,GAAG,SAAS,CAAC,wBAAwB,EAC1C,KAAK,GAAG,SAAS,CAAC,wBAAwB,EAC1C,KAAK,GAAG,SAAS,CAAC,wBAAwB,EAC1C,yBAAyB,GAAG,CAAC,EAC7B,YAAY,GAAG,SAAS,CAAC,6BAA6B,EACtD,kBAAkB,GAAG,CAAC;QAEtB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,gCAAgC,GAAG,yBAAyB,CAAC;QAClE,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAE9C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,KAAqB;QACvC,OAAO,CACH,IAAI,CAAC,YAAY,KAAK,KAAK,CAAC,YAAY;YACxC,IAAI,CAAC,YAAY,KAAK,KAAK,CAAC,YAAY;YACxC,IAAI,CAAC,YAAY,KAAK,KAAK,CAAC,YAAY;YACxC,IAAI,CAAC,gCAAgC,KAAK,KAAK,CAAC,gCAAgC;YAChF,IAAI,CAAC,YAAY,KAAK,KAAK,CAAC,YAAY;YACxC,IAAI,CAAC,mBAAmB,KAAK,KAAK,CAAC,mBAAmB;YACtD,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC,WAAW,CACzC,CAAC;IACN,CAAC;CACJ", "sourcesContent": ["import { Constants } from \"../../Engines/constants\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * Class used to store a texture sampler data\r\n */\r\nexport class TextureSampler {\r\n    /**\r\n     * Gets the sampling mode of the texture\r\n     */\r\n    public samplingMode: number = -1;\r\n\r\n    /**\r\n     * | Value | Type               | Description |\r\n     * | ----- | ------------------ | ----------- |\r\n     * | 0     | CLAMP_ADDRESSMODE  |             |\r\n     * | 1     | WRAP_ADDRESSMODE   |             |\r\n     * | 2     | MIRROR_ADDRESSMODE |             |\r\n     */\r\n    public get wrapU() {\r\n        return this._cachedWrapU;\r\n    }\r\n\r\n    public set wrapU(value: Nullable<number>) {\r\n        this._cachedWrapU = value;\r\n    }\r\n\r\n    /**\r\n     * | Value | Type               | Description |\r\n     * | ----- | ------------------ | ----------- |\r\n     * | 0     | CLAMP_ADDRESSMODE  |             |\r\n     * | 1     | WRAP_ADDRESSMODE   |             |\r\n     * | 2     | MIRROR_ADDRESSMODE |             |\r\n     */\r\n    public get wrapV() {\r\n        return this._cachedWrapV;\r\n    }\r\n\r\n    public set wrapV(value: Nullable<number>) {\r\n        this._cachedWrapV = value;\r\n    }\r\n\r\n    /**\r\n     * | Value | Type               | Description |\r\n     * | ----- | ------------------ | ----------- |\r\n     * | 0     | CLAMP_ADDRESSMODE  |             |\r\n     * | 1     | WRAP_ADDRESSMODE   |             |\r\n     * | 2     | MIRROR_ADDRESSMODE |             |\r\n     */\r\n    public get wrapR() {\r\n        return this._cachedWrapR;\r\n    }\r\n\r\n    public set wrapR(value: Nullable<number>) {\r\n        this._cachedWrapR = value;\r\n    }\r\n\r\n    /**\r\n     * With compliant hardware and browser (supporting anisotropic filtering)\r\n     * this defines the level of anisotropic filtering in the texture.\r\n     * The higher the better but the slower.\r\n     */\r\n    public get anisotropicFilteringLevel() {\r\n        return this._cachedAnisotropicFilteringLevel;\r\n    }\r\n\r\n    public set anisotropicFilteringLevel(value: Nullable<number>) {\r\n        this._cachedAnisotropicFilteringLevel = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the comparison function (Constants.LESS, Constants.EQUAL, etc). Set 0 to not use a comparison function\r\n     */\r\n    public get comparisonFunction() {\r\n        return this._comparisonFunction;\r\n    }\r\n\r\n    public set comparisonFunction(value: number) {\r\n        this._comparisonFunction = value;\r\n    }\r\n\r\n    private _useMipMaps = true;\r\n    /**\r\n     * Indicates to use the mip maps (if available on the texture).\r\n     * Thanks to this flag, you can instruct the sampler to not sample the mipmaps even if they exist (and if the sampling mode is set to a value that normally samples the mipmaps!)\r\n     */\r\n    public get useMipMaps() {\r\n        return this._useMipMaps;\r\n    }\r\n\r\n    public set useMipMaps(value: boolean) {\r\n        this._useMipMaps = value;\r\n    }\r\n\r\n    /** @internal */\r\n    public _cachedWrapU: Nullable<number> = null;\r\n\r\n    /** @internal */\r\n    public _cachedWrapV: Nullable<number> = null;\r\n\r\n    /** @internal */\r\n    public _cachedWrapR: Nullable<number> = null;\r\n\r\n    /** @internal */\r\n    public _cachedAnisotropicFilteringLevel: Nullable<number> = null;\r\n\r\n    /** @internal */\r\n    public _comparisonFunction: number = 0;\r\n\r\n    /**\r\n     * Used for debugging purpose only\r\n     */\r\n    public label?: string;\r\n\r\n    /**\r\n     * Creates a Sampler instance\r\n     */\r\n    constructor() {}\r\n\r\n    /**\r\n     * Sets all the parameters of the sampler\r\n     * @param wrapU u address mode (default: TEXTURE_WRAP_ADDRESSMODE)\r\n     * @param wrapV v address mode (default: TEXTURE_WRAP_ADDRESSMODE)\r\n     * @param wrapR r address mode (default: TEXTURE_WRAP_ADDRESSMODE)\r\n     * @param anisotropicFilteringLevel anisotropic level (default: 1)\r\n     * @param samplingMode sampling mode (default: Constants.TEXTURE_BILINEAR_SAMPLINGMODE)\r\n     * @param comparisonFunction comparison function (default: 0 - no comparison function)\r\n     * @returns the current sampler instance\r\n     */\r\n    public setParameters(\r\n        wrapU = Constants.TEXTURE_WRAP_ADDRESSMODE,\r\n        wrapV = Constants.TEXTURE_WRAP_ADDRESSMODE,\r\n        wrapR = Constants.TEXTURE_WRAP_ADDRESSMODE,\r\n        anisotropicFilteringLevel = 1,\r\n        samplingMode = Constants.TEXTURE_BILINEAR_SAMPLINGMODE,\r\n        comparisonFunction = 0\r\n    ): TextureSampler {\r\n        this._cachedWrapU = wrapU;\r\n        this._cachedWrapV = wrapV;\r\n        this._cachedWrapR = wrapR;\r\n        this._cachedAnisotropicFilteringLevel = anisotropicFilteringLevel;\r\n        this.samplingMode = samplingMode;\r\n        this._comparisonFunction = comparisonFunction;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Compares this sampler with another one\r\n     * @param other sampler to compare with\r\n     * @returns true if the samplers have the same parametres, else false\r\n     */\r\n    public compareSampler(other: TextureSampler): boolean {\r\n        return (\r\n            this._cachedWrapU === other._cachedWrapU &&\r\n            this._cachedWrapV === other._cachedWrapV &&\r\n            this._cachedWrapR === other._cachedWrapR &&\r\n            this._cachedAnisotropicFilteringLevel === other._cachedAnisotropicFilteringLevel &&\r\n            this.samplingMode === other.samplingMode &&\r\n            this._comparisonFunction === other._comparisonFunction &&\r\n            this._useMipMaps === other._useMipMaps\r\n        );\r\n    }\r\n}\r\n"]}