{"version": 3, "file": "nodeMaterialBlockConnectionPointTypes.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,CAAN,IAAY,qCAyBX;AAzBD,WAAY,qCAAqC;IAC7C,YAAY;IACZ,mGAAc,CAAA;IACd,UAAU;IACV,+FAAY,CAAA;IACZ,cAAc;IACd,uGAAgB,CAAA;IAChB,cAAc;IACd,uGAAgB,CAAA;IAChB,cAAc;IACd,wGAAgB,CAAA;IAChB,aAAa;IACb,sGAAe,CAAA;IACf,aAAa;IACb,sGAAe,CAAA;IACf,aAAa;IACb,uGAAe,CAAA;IACf,oBAAoB;IACpB,uGAAe,CAAA;IACf,sCAAsC;IACtC,gHAAmB,CAAA;IACnB,qDAAqD;IACrD,oHAAqB,CAAA;IACrB,2BAA2B;IAC3B,kGAAY,CAAA;AAChB,CAAC,EAzBW,qCAAqC,KAArC,qCAAqC,QAyBhD", "sourcesContent": ["/**\r\n * Defines the kind of connection point for node based material\r\n */\r\nexport enum NodeMaterialBlockConnectionPointTypes {\r\n    /** Float */\r\n    Float = 0x0001,\r\n    /** Int */\r\n    Int = 0x0002,\r\n    /** Vector2 */\r\n    Vector2 = 0x0004,\r\n    /** Vector3 */\r\n    Vector3 = 0x0008,\r\n    /** Vector4 */\r\n    Vector4 = 0x0010,\r\n    /** Color3 */\r\n    Color3 = 0x0020,\r\n    /** Color4 */\r\n    Color4 = 0x0040,\r\n    /** Matrix */\r\n    Matrix = 0x0080,\r\n    /** Custom object */\r\n    Object = 0x0100,\r\n    /** Detect type based on connection */\r\n    AutoDetect = 0x0400,\r\n    /** Output type that will be defined by input type */\r\n    BasedOnInput = 0x0800,\r\n    /** Bitmask of all types */\r\n    All = 0x0fff,\r\n}\r\n"]}