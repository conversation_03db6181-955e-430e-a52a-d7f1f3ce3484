{"version": 3, "file": "webRequest.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/webRequest.ts"], "names": [], "mappings": "AAOA,gBAAgB;AAChB,gEAAgE;AAChE,SAAS,oBAAoB;IACzB,4GAA4G;IAC5G,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,cAAc,EAAE;QAC1D,OAAO,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;KACvC;SAAM;QACH,OAAO,IAAI,cAAc,EAAE,CAAC;KAC/B;AACL,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,UAAU;IAAvB;QACqB,SAAI,GAAG,oBAAoB,EAAE,CAAC;QA0BvC,gBAAW,GAAW,EAAE,CAAC;IA2KrC,CAAC;IAnLG;;;OAGG;IACI,MAAM,KAAK,wBAAwB;QACtC,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;IACnH,CAAC;IAIO,2BAA2B;QAC/B,IAAI,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YACxD,OAAO;SACV;QACD,KAAK,MAAM,GAAG,IAAI,UAAU,CAAC,oBAAoB,EAAE;YAC/C,MAAM,GAAG,GAAG,UAAU,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YACjD,IAAI,GAAG,EAAE;gBACL,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;aACxC;SACJ;IACL,CAAC;IAEO,+BAA+B,CAAC,GAAW;QAC/C,OAAO,UAAU,CAAC,oCAAoC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,uBAAuB,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC;IAC3I,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;IAChC,CAAC;IAED,IAAW,UAAU,CAAC,KAAgE;QAClF,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;IAClC,CAAC;IAED,IAAW,YAAY,CAAC,KAAiC;QACrD,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;IAC7B,CAAC;IAED,IAAW,OAAO,CAAC,KAAa;QAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IAC9B,CAAC;IAQM,gBAAgB,CAAC,IAAY,EAAE,QAA4C,EAAE,OAA2C;QAC3H,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;IAQM,mBAAmB,CAAC,IAAY,EAAE,QAA4C,EAAE,OAAwC;QAC3H,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,IAAI,CAAC,IAA+C;QACvD,IAAI,UAAU,CAAC,oBAAoB,EAAE;YACjC,IAAI,CAAC,2BAA2B,EAAE,CAAC;SACtC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACI,IAAI,CAAC,MAAc,EAAE,GAAW;QACnC,KAAK,MAAM,MAAM,IAAI,UAAU,CAAC,sBAAsB,EAAE;YACpD,IAAI,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,EAAE;gBAC3C,OAAO;aACV;YACD,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;SAC1B;QAED,YAAY;QACZ,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QACzC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAE3C,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACH,gBAAgB,CAAC,IAAY,EAAE,KAAa;QACxC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACH,iBAAiB,CAAC,IAAY;QAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;;AAlMD;;;GAGG;AACW,+BAAoB,GAA8B,EAAE,AAAhC,CAAiC;AAEnE;;GAEG;AACW,iCAAsB,GAAG,IAAI,KAAK,EAAkD,AAA9D,CAA+D;AAEnG;;GAEG;AACW,+CAAoC,GAAG,IAAI,AAAP,CAAQ", "sourcesContent": ["import type { IWebRequest } from \"./interfaces/iWebRequest\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { INative } from \"../Engines/Native/nativeInterfaces\";\r\n\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\ndeclare const _native: INative;\r\n\r\n/** @internal */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nfunction createXMLHttpRequest(): XMLHttpRequest {\r\n    // If running in Babylon Native, then defer to the native XMLHttpRequest, which has the same public contract\r\n    if (typeof _native !== \"undefined\" && _native.XMLHttpRequest) {\r\n        return new _native.XMLHttpRequest();\r\n    } else {\r\n        return new XMLHttpRequest();\r\n    }\r\n}\r\n\r\n/**\r\n * Extended version of XMLHttpRequest with support for customizations (headers, ...)\r\n */\r\nexport class WebRequest implements IWebRequest {\r\n    private readonly _xhr = createXMLHttpRequest();\r\n\r\n    /**\r\n     * Custom HTTP Request Headers to be sent with XMLHttpRequests\r\n     * i.e. when loading files, where the server/service expects an Authorization header\r\n     */\r\n    public static CustomRequestHeaders: { [key: string]: string } = {};\r\n\r\n    /**\r\n     * Add callback functions in this array to update all the requests before they get sent to the network\r\n     */\r\n    public static CustomRequestModifiers = new Array<(request: XMLHttpRequest, url: string) => void>();\r\n\r\n    /**\r\n     * If set to true, requests to Babylon.js CDN requests will not be modified\r\n     */\r\n    public static SkipRequestModificationForBabylonCDN = true;\r\n\r\n    /**\r\n     * This function can be called to check if there are request modifiers for network requests\r\n     * @returns true if there are any custom requests available\r\n     */\r\n    public static get IsCustomRequestAvailable(): boolean {\r\n        return Object.keys(WebRequest.CustomRequestHeaders).length > 0 || WebRequest.CustomRequestModifiers.length > 0;\r\n    }\r\n\r\n    private _requestURL: string = \"\";\r\n\r\n    private _injectCustomRequestHeaders(): void {\r\n        if (this._shouldSkipRequestModifications(this._requestURL)) {\r\n            return;\r\n        }\r\n        for (const key in WebRequest.CustomRequestHeaders) {\r\n            const val = WebRequest.CustomRequestHeaders[key];\r\n            if (val) {\r\n                this._xhr.setRequestHeader(key, val);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _shouldSkipRequestModifications(url: string): boolean {\r\n        return WebRequest.SkipRequestModificationForBabylonCDN && (url.includes(\"preview.babylonjs.com\") || url.includes(\"cdn.babylonjs.com\"));\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a function to be called when loading progress changes\r\n     */\r\n    public get onprogress(): ((this: XMLHttpRequest, ev: ProgressEvent) => any) | null {\r\n        return this._xhr.onprogress;\r\n    }\r\n\r\n    public set onprogress(value: ((this: XMLHttpRequest, ev: ProgressEvent) => any) | null) {\r\n        this._xhr.onprogress = value;\r\n    }\r\n\r\n    /**\r\n     * Returns client's state\r\n     */\r\n    public get readyState(): number {\r\n        return this._xhr.readyState;\r\n    }\r\n\r\n    /**\r\n     * Returns client's status\r\n     */\r\n    public get status(): number {\r\n        return this._xhr.status;\r\n    }\r\n\r\n    /**\r\n     * Returns client's status as a text\r\n     */\r\n    public get statusText(): string {\r\n        return this._xhr.statusText;\r\n    }\r\n\r\n    /**\r\n     * Returns client's response\r\n     */\r\n    public get response(): any {\r\n        return this._xhr.response;\r\n    }\r\n\r\n    /**\r\n     * Returns client's response url\r\n     */\r\n    public get responseURL(): string {\r\n        return this._xhr.responseURL;\r\n    }\r\n\r\n    /**\r\n     * Returns client's response as text\r\n     */\r\n    public get responseText(): string {\r\n        return this._xhr.responseText;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the expected response type\r\n     */\r\n    public get responseType(): XMLHttpRequestResponseType {\r\n        return this._xhr.responseType;\r\n    }\r\n\r\n    public set responseType(value: XMLHttpRequestResponseType) {\r\n        this._xhr.responseType = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the timeout value in milliseconds\r\n     */\r\n    public get timeout(): number {\r\n        return this._xhr.timeout;\r\n    }\r\n\r\n    public set timeout(value: number) {\r\n        this._xhr.timeout = value;\r\n    }\r\n\r\n    /** @internal */\r\n    public addEventListener<K extends keyof XMLHttpRequestEventMap>(\r\n        type: K,\r\n        listener: (this: XMLHttpRequest, ev: XMLHttpRequestEventMap[K]) => any,\r\n        options?: boolean | AddEventListenerOptions\r\n    ): void;\r\n    public addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void {\r\n        this._xhr.addEventListener(type, listener, options);\r\n    }\r\n\r\n    /** @internal */\r\n    public removeEventListener<K extends keyof XMLHttpRequestEventMap>(\r\n        type: K,\r\n        listener: (this: XMLHttpRequest, ev: XMLHttpRequestEventMap[K]) => any,\r\n        options?: boolean | EventListenerOptions\r\n    ): void;\r\n    public removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void {\r\n        this._xhr.removeEventListener(type, listener, options);\r\n    }\r\n\r\n    /**\r\n     * Cancels any network activity\r\n     */\r\n    public abort() {\r\n        this._xhr.abort();\r\n    }\r\n\r\n    /**\r\n     * Initiates the request. The optional argument provides the request body. The argument is ignored if request method is GET or HEAD\r\n     * @param body defines an optional request body\r\n     */\r\n    public send(body?: Document | XMLHttpRequestBodyInit | null): void {\r\n        if (WebRequest.CustomRequestHeaders) {\r\n            this._injectCustomRequestHeaders();\r\n        }\r\n\r\n        this._xhr.send(body);\r\n    }\r\n\r\n    /**\r\n     * Sets the request method, request URL\r\n     * @param method defines the method to use (GET, POST, etc..)\r\n     * @param url defines the url to connect with\r\n     */\r\n    public open(method: string, url: string): void {\r\n        for (const update of WebRequest.CustomRequestModifiers) {\r\n            if (this._shouldSkipRequestModifications(url)) {\r\n                return;\r\n            }\r\n            update(this._xhr, url);\r\n        }\r\n\r\n        // Clean url\r\n        url = url.replace(\"file:http:\", \"http:\");\r\n        url = url.replace(\"file:https:\", \"https:\");\r\n\r\n        this._requestURL = url;\r\n\r\n        this._xhr.open(method, url, true);\r\n    }\r\n\r\n    /**\r\n     * Sets the value of a request header.\r\n     * @param name The name of the header whose value is to be set\r\n     * @param value The value to set as the body of the header\r\n     */\r\n    setRequestHeader(name: string, value: string): void {\r\n        this._xhr.setRequestHeader(name, value);\r\n    }\r\n\r\n    /**\r\n     * Get the string containing the text of a particular header's value.\r\n     * @param name The name of the header\r\n     * @returns The string containing the text of the given header name\r\n     */\r\n    getResponseHeader(name: string): Nullable<string> {\r\n        return this._xhr.getResponseHeader(name);\r\n    }\r\n}\r\n"]}