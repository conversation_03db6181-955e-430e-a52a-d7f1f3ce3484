{"version": 3, "file": "trailMesh.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Meshes/trailMesh.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AACtD,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AAItC,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AAGvD,IAAI,CAAC,gBAAgB,GAAG,CAAC,UAAe,EAAE,KAAY,EAAE,EAAE;IACtD,OAAO,SAAS,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAC9C,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,SAAU,SAAQ,IAAI;IAe/B;;;;;;;;OAQG;IACH,YAAY,IAAY,EAAE,SAAwB,EAAE,KAAa,EAAE,WAAmB,CAAC,EAAE,SAAiB,EAAE,EAAE,YAAqB,IAAI;QACnI,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAff,+BAA0B,GAAW,CAAC,CAAC;QAiB3C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;QAChC,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC,EAAE,EAAE;YAC/D,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YACzC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;SAClD;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,WAAW;QACf,MAAM,IAAI,GAAe,IAAI,UAAU,EAAE,CAAC;QAC1C,MAAM,SAAS,GAAkB,EAAE,CAAC;QACpC,MAAM,OAAO,GAAkB,EAAE,CAAC;QAClC,MAAM,OAAO,GAAkB,EAAE,CAAC;QAClC,MAAM,GAAG,GAAkB,EAAE,CAAC;QAC9B,IAAI,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAChC,IAAI,IAAI,CAAC,UAAU,YAAY,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE;YAC5E,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC;SAC1E;aAAM;YACH,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;SACjD;QACD,MAAM,KAAK,GAAW,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;QACtE,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC,EAAE,EAAE;YAC/D,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7H,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;SACpD;QACD,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE;YAC5C,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC,EAAE,EAAE;gBAC/D,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC7H,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;aACnE;YACD,MAAM,CAAC,GAAW,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAAC;YACnF,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC,EAAE,EAAE;gBAC/D,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAAC;gBAC1G,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,0BAA0B,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;aAC/E;SACJ;QACD,UAAU,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7B,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;IACL,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC3E,IAAI,CAAC,MAAM,EAAE,CAAC;YAClB,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACI,IAAI;QACP,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC7C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,QAAQ,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SAC/E;IACL,CAAC;IAED;;OAEG;IACI,MAAM;QACT,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAClE,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAC9D,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;QAC5C,IAAI,SAAS,IAAI,OAAO,EAAE;YACtB,KAAK,IAAI,CAAC,GAAW,CAAC,GAAG,CAAC,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACvF,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;aACzH;YACD,KAAK,IAAI,CAAC,GAAW,CAAC,GAAG,CAAC,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrF,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;aACvE;YACD,MAAM,CAAC,GAAW,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAAC;YAC/E,MAAM,KAAK,GAAW,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;YACtE,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC,EAAE,EAAE;gBAC/D,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;gBAC5G,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;gBAClF,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxF,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;aAClG;YACD,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC,EAAE,EAAE;gBAC/D,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjD,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrD,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrD,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrD,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzD,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC5D;YACD,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAC3E,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;SAC1E;IACL,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,OAAe,EAAE,EAAE,YAA2B;QACvD,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,YAAY,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAC/H,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,mBAAwB;QACrC,KAAK,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QAErC,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;IACzD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,UAAe,EAAE,KAAY;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,eAAe,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,wBAAwB,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAE1H,IAAI,CAAC,SAAS,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,yCAAyC,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;SACvF;QAED,OAAO,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;IACpJ,CAAC;CACJ", "sourcesContent": ["import { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport { VertexData } from \"../Meshes/mesh.vertexData\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\n\r\nMesh._TrailMeshParser = (parsedMesh: any, scene: Scene) => {\r\n    return TrailMesh.Parse(parsedMesh, scene);\r\n};\r\n\r\n/**\r\n * Class used to create a trail following a mesh\r\n */\r\nexport class TrailMesh extends Mesh {\r\n    /**\r\n     * The diameter of the trail, i.e. the width of the ribbon.\r\n     */\r\n    public diameter: number;\r\n\r\n    private _generator: TransformNode;\r\n    private _autoStart: boolean;\r\n    private _running: boolean;\r\n    private _length: number;\r\n    private _sectionPolygonPointsCount: number = 4;\r\n    private _sectionVectors: Array<Vector3>;\r\n    private _sectionNormalVectors: Array<Vector3>;\r\n    private _beforeRenderObserver: Nullable<Observer<Scene>>;\r\n\r\n    /**\r\n     * Creates a new TrailMesh.\r\n     * @param name The value used by scene.getMeshByName() to do a lookup.\r\n     * @param generator The mesh or transform node to generate a trail.\r\n     * @param scene The scene to add this mesh to.\r\n     * @param diameter Diameter of trailing mesh. Default is 1.\r\n     * @param length Length of trailing mesh. Default is 60.\r\n     * @param autoStart Automatically start trailing mesh. Default true.\r\n     */\r\n    constructor(name: string, generator: TransformNode, scene?: Scene, diameter: number = 1, length: number = 60, autoStart: boolean = true) {\r\n        super(name, scene);\r\n\r\n        this._running = false;\r\n        this._autoStart = autoStart;\r\n        this._generator = generator;\r\n        this.diameter = diameter;\r\n        this._length = length;\r\n        this._sectionVectors = [];\r\n        this._sectionNormalVectors = [];\r\n        for (let i: number = 0; i <= this._sectionPolygonPointsCount; i++) {\r\n            this._sectionVectors[i] = Vector3.Zero();\r\n            this._sectionNormalVectors[i] = Vector3.Zero();\r\n        }\r\n        this._createMesh();\r\n    }\r\n\r\n    /**\r\n     * \"TrailMesh\"\r\n     * @returns \"TrailMesh\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"TrailMesh\";\r\n    }\r\n\r\n    private _createMesh(): void {\r\n        const data: VertexData = new VertexData();\r\n        const positions: Array<number> = [];\r\n        const normals: Array<number> = [];\r\n        const indices: Array<number> = [];\r\n        const uvs: Array<number> = [];\r\n        let meshCenter = Vector3.Zero();\r\n        if (this._generator instanceof AbstractMesh && this._generator.hasBoundingInfo) {\r\n            meshCenter = this._generator.getBoundingInfo().boundingBox.centerWorld;\r\n        } else {\r\n            meshCenter = this._generator.absolutePosition;\r\n        }\r\n        const alpha: number = (2 * Math.PI) / this._sectionPolygonPointsCount;\r\n        for (let i: number = 0; i <= this._sectionPolygonPointsCount; i++) {\r\n            const angle = i !== this._sectionPolygonPointsCount ? i * alpha : 0;\r\n            positions.push(meshCenter.x + Math.cos(angle) * this.diameter, meshCenter.y + Math.sin(angle) * this.diameter, meshCenter.z);\r\n            uvs.push(i / this._sectionPolygonPointsCount, 0);\r\n        }\r\n        for (let i: number = 1; i <= this._length; i++) {\r\n            for (let j: number = 0; j <= this._sectionPolygonPointsCount; j++) {\r\n                const angle = j !== this._sectionPolygonPointsCount ? j * alpha : 0;\r\n                positions.push(meshCenter.x + Math.cos(angle) * this.diameter, meshCenter.y + Math.sin(angle) * this.diameter, meshCenter.z);\r\n                uvs.push(j / this._sectionPolygonPointsCount, i / this._length);\r\n            }\r\n            const l: number = positions.length / 3 - 2 * (this._sectionPolygonPointsCount + 1);\r\n            for (let j: number = 0; j <= this._sectionPolygonPointsCount; j++) {\r\n                indices.push(l + j, l + j + this._sectionPolygonPointsCount, l + j + this._sectionPolygonPointsCount + 1);\r\n                indices.push(l + j, l + j + this._sectionPolygonPointsCount + 1, l + j + 1);\r\n            }\r\n        }\r\n        VertexData.ComputeNormals(positions, indices, normals);\r\n        data.positions = positions;\r\n        data.normals = normals;\r\n        data.indices = indices;\r\n        data.uvs = uvs;\r\n        data.applyToMesh(this, true);\r\n        if (this._autoStart) {\r\n            this.start();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Start trailing mesh.\r\n     */\r\n    public start(): void {\r\n        if (!this._running) {\r\n            this._running = true;\r\n            this._beforeRenderObserver = this.getScene().onBeforeRenderObservable.add(() => {\r\n                this.update();\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Stop trailing mesh.\r\n     */\r\n    public stop(): void {\r\n        if (this._beforeRenderObserver && this._running) {\r\n            this._running = false;\r\n            this.getScene().onBeforeRenderObservable.remove(this._beforeRenderObserver);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update trailing mesh geometry.\r\n     */\r\n    public update(): void {\r\n        const positions = this.getVerticesData(VertexBuffer.PositionKind);\r\n        const normals = this.getVerticesData(VertexBuffer.NormalKind);\r\n        const wm = this._generator.getWorldMatrix();\r\n        if (positions && normals) {\r\n            for (let i: number = 3 * (this._sectionPolygonPointsCount + 1); i < positions.length; i++) {\r\n                positions[i - 3 * (this._sectionPolygonPointsCount + 1)] = positions[i] - (normals[i] / this._length) * this.diameter;\r\n            }\r\n            for (let i: number = 3 * (this._sectionPolygonPointsCount + 1); i < normals.length; i++) {\r\n                normals[i - 3 * (this._sectionPolygonPointsCount + 1)] = normals[i];\r\n            }\r\n            const l: number = positions.length - 3 * (this._sectionPolygonPointsCount + 1);\r\n            const alpha: number = (2 * Math.PI) / this._sectionPolygonPointsCount;\r\n            for (let i: number = 0; i <= this._sectionPolygonPointsCount; i++) {\r\n                const angle = i !== this._sectionPolygonPointsCount ? i * alpha : 0;\r\n                this._sectionVectors[i].copyFromFloats(Math.cos(angle) * this.diameter, Math.sin(angle) * this.diameter, 0);\r\n                this._sectionNormalVectors[i].copyFromFloats(Math.cos(angle), Math.sin(angle), 0);\r\n                Vector3.TransformCoordinatesToRef(this._sectionVectors[i], wm, this._sectionVectors[i]);\r\n                Vector3.TransformNormalToRef(this._sectionNormalVectors[i], wm, this._sectionNormalVectors[i]);\r\n            }\r\n            for (let i: number = 0; i <= this._sectionPolygonPointsCount; i++) {\r\n                positions[l + 3 * i] = this._sectionVectors[i].x;\r\n                positions[l + 3 * i + 1] = this._sectionVectors[i].y;\r\n                positions[l + 3 * i + 2] = this._sectionVectors[i].z;\r\n                normals[l + 3 * i] = this._sectionNormalVectors[i].x;\r\n                normals[l + 3 * i + 1] = this._sectionNormalVectors[i].y;\r\n                normals[l + 3 * i + 2] = this._sectionNormalVectors[i].z;\r\n            }\r\n            this.updateVerticesData(VertexBuffer.PositionKind, positions, true, false);\r\n            this.updateVerticesData(VertexBuffer.NormalKind, normals, true, false);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns a new TrailMesh object.\r\n     * @param name is a string, the name given to the new mesh\r\n     * @param newGenerator use new generator object for cloned trail mesh\r\n     * @returns a new mesh\r\n     */\r\n    public clone(name: string = \"\", newGenerator: TransformNode): TrailMesh {\r\n        return new TrailMesh(name, newGenerator ?? this._generator, this.getScene(), this.diameter, this._length, this._autoStart);\r\n    }\r\n\r\n    /**\r\n     * Serializes this trail mesh\r\n     * @param serializationObject object to write serialization to\r\n     */\r\n    public serialize(serializationObject: any): void {\r\n        super.serialize(serializationObject);\r\n\r\n        serializationObject.generatorId = this._generator.id;\r\n    }\r\n\r\n    /**\r\n     * Parses a serialized trail mesh\r\n     * @param parsedMesh the serialized mesh\r\n     * @param scene the scene to create the trail mesh in\r\n     * @returns the created trail mesh\r\n     */\r\n    public static Parse(parsedMesh: any, scene: Scene): TrailMesh {\r\n        const generator = scene.getLastMeshById(parsedMesh.generatorId) ?? scene.getLastTransformNodeById(parsedMesh.generatorId);\r\n\r\n        if (!generator) {\r\n            throw new Error(\"TrailMesh: generator not found with ID \" + parsedMesh.generatorId);\r\n        }\r\n\r\n        return new TrailMesh(parsedMesh.name, generator, scene, parsedMesh.diameter ?? parsedMesh._diameter, parsedMesh._length, parsedMesh._autoStart);\r\n    }\r\n}\r\n"]}