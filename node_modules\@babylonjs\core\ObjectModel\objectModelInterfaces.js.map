{"version": 3, "file": "objectModelInterfaces.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/ObjectModel/objectModelInterfaces.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\r\n * A container with an original object and information about that object.\r\n * on some other object.\r\n */\r\nexport interface IObjectInfo<T> {\r\n    /**\r\n     * The original object.\r\n     */\r\n    object: any;\r\n    /**\r\n     * Information about the object.\r\n     */\r\n    info: T;\r\n}\r\n\r\n/**\r\n * Interface for a converter that takes a string path and transforms\r\n * it into an ObjectAccessorContainer.\r\n */\r\nexport interface IPathToObjectConverter<T> {\r\n    /**\r\n     * Convert a path to an object that can be used to access properties of a base object\r\n     * @param path the path to convert\r\n     * @returns an object that can be used to access properties of a base object\r\n     */\r\n    convert(path: string): IObjectInfo<T>;\r\n}\r\n"]}