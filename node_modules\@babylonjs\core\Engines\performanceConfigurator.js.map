{"version": 3, "file": "performanceConfigurator.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Engines/performanceConfigurator.ts"], "names": [], "mappings": "AAAA,gBAAgB;AAChB,MAAM,OAAO,uBAAuB;IAUhC;;OAEG;IACI,MAAM,CAAC,kBAAkB,CAAC,SAAkB;QAC/C,uBAAuB,CAAC,0BAA0B,GAAG,KAAK,CAAC;QAE3D,IAAI,SAAS,IAAI,CAAC,uBAAuB,CAAC,eAAe,EAAE;YACvD,IAAI,uBAAuB,CAAC,qBAAqB,EAAE;gBAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,uBAAuB,CAAC,qBAAqB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;oBAC3E,MAAM,MAAM,GAAG,uBAAuB,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;oBAChE,MAAM,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC;oBAEzB,MAAM,CAAC,EAAE,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;oBAE1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE;wBACzB,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;qBAC5B;iBACJ;aACJ;SACJ;QAED,uBAAuB,CAAC,eAAe,GAAG,SAAS,CAAC;QACpD,uBAAuB,CAAC,iBAAiB,GAAG,uBAAuB,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC;QAC3G,uBAAuB,CAAC,qBAAqB,GAAG,IAAI,CAAC,CAAC,iEAAiE;IAC3H,CAAC;;AAjCD,gBAAgB;AACF,uCAAe,GAAG,KAAK,CAAC;AACtC,gBAAgB;AACF,kDAA0B,GAAG,IAAI,CAAC;AAChD,gBAAgB;AACF,yCAAiB,GAAQ,YAAY,CAAC;AACpD,gBAAgB;AACF,6CAAqB,GAAsB,EAAE,CAAC", "sourcesContent": ["/** @internal */\r\nexport class PerformanceConfigurator {\r\n    /** @internal */\r\n    public static MatrixUse64Bits = false;\r\n    /** @internal */\r\n    public static MatrixTrackPrecisionChange = true;\r\n    /** @internal */\r\n    public static MatrixCurrentType: any = Float32Array;\r\n    /** @internal */\r\n    public static MatrixTrackedMatrices: Array<any> | null = [];\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static SetMatrixPrecision(use64bits: boolean) {\r\n        PerformanceConfigurator.MatrixTrackPrecisionChange = false;\r\n\r\n        if (use64bits && !PerformanceConfigurator.MatrixUse64Bits) {\r\n            if (PerformanceConfigurator.MatrixTrackedMatrices) {\r\n                for (let m = 0; m < PerformanceConfigurator.MatrixTrackedMatrices.length; ++m) {\r\n                    const matrix = PerformanceConfigurator.MatrixTrackedMatrices[m];\r\n                    const values = matrix._m;\r\n\r\n                    matrix._m = new Array(16);\r\n\r\n                    for (let i = 0; i < 16; ++i) {\r\n                        matrix._m[i] = values[i];\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        PerformanceConfigurator.MatrixUse64Bits = use64bits;\r\n        PerformanceConfigurator.MatrixCurrentType = PerformanceConfigurator.MatrixUse64Bits ? Array : Float32Array;\r\n        PerformanceConfigurator.MatrixTrackedMatrices = null; // reclaim some memory, as we don't need _TrackedMatrices anymore\r\n    }\r\n}\r\n"]}