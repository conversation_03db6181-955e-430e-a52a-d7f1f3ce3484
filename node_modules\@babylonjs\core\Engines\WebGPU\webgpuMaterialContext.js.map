{"version": 3, "file": "webgpuMaterialContext.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuMaterialContext.ts"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,wCAAwC;AACxC,OAAO,EAAE,eAAe,EAAE,MAAM,0CAA0C,CAAC;AAI3E,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAe1D,gBAAgB;AAChB,MAAM,OAAO,qBAAqB;IAiB9B,IAAW,sBAAsB;QAC7B,gGAAgG;QAChG,iGAAiG;QACjG,OAAO,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;IACzC,CAAC;IAED,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC;IAC7C,CAAC;IAKD;QACI,IAAI,CAAC,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,EAAE,CAAC;QACjD,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;IAClC,CAAC;IAEM,UAAU,CAAC,IAAY,EAAE,OAAiC;QAC7D,IAAI,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,eAAe,GAAG,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,YAAY,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,YAAY,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;SACjE;aAAM;YACH,eAAe,GAAG,YAAY,CAAC,QAAQ,CAAC;SAC3C;QAED,YAAY,CAAC,OAAO,GAAG,OAAO,CAAC;QAC/B,YAAY,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAErF,MAAM,OAAO,GAAG,eAAe,KAAK,YAAY,CAAC,QAAQ,CAAC;QAC1D,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB;QAED,IAAI,CAAC,OAAO,KAAZ,IAAI,CAAC,OAAO,GAAK,OAAO,EAAC;IAC7B,CAAC;IAEM,UAAU,CAAC,IAAY,EAAE,OAAoD;QAChF,IAAI,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,YAAY,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,YAAY,GAAG,EAAE,OAAO,EAAE,qBAAqB,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC;SAC5G;aAAM;YACH,gBAAgB,GAAG,YAAY,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAC,CAAC;SAC3D;QAED,IAAI,YAAY,CAAC,iBAAiB,EAAE;YAChC,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC/B;QACD,IAAI,YAAY,CAAC,qBAAqB,EAAE;YACpC,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACnC;QAED,IAAI,OAAO,EAAE;YACT,YAAY,CAAC,qBAAqB;gBAC9B,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,iBAAiB;oBAC5C,CAAC,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,8BAA8B,IAAI,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC;YACpI,YAAY,CAAC,iBAAiB,GAAG,eAAe,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAC5E,IAAI,YAAY,CAAC,qBAAqB,EAAE;gBACpC,IAAI,CAAC,wBAAwB,EAAE,CAAC;aACnC;YACD,IAAI,YAAY,CAAC,iBAAiB,EAAE;gBAChC,IAAI,CAAC,oBAAoB,EAAE,CAAC;aAC/B;SACJ;aAAM;YACH,YAAY,CAAC,qBAAqB,GAAG,KAAK,CAAC;YAC3C,YAAY,CAAC,iBAAiB,GAAG,KAAK,CAAC;SAC1C;QAED,YAAY,CAAC,OAAO,GAAG,OAAO,CAAC;QAE/B,MAAM,OAAO,GAAG,gBAAgB,KAAK,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB;QAED,IAAI,CAAC,OAAO,KAAZ,IAAI,CAAC,OAAO,GAAK,OAAO,EAAC;IAC7B,CAAC;;AAxGc,8BAAQ,GAAG,CAAC,CAAC", "sourcesContent": ["/* eslint-disable baby<PERSON>js/available */\r\n/* eslint-disable jsdoc/require-jsdoc */\r\nimport { ExternalTexture } from \"../../Materials/Textures/externalTexture\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport type { TextureSampler } from \"../../Materials/Textures/textureSampler\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Constants } from \"../constants\";\r\nimport type { IMaterialContext } from \"../IMaterialContext\";\r\nimport { WebGPUCacheSampler } from \"./webgpuCacheSampler\";\r\n\r\n/** @internal */\r\ninterface IWebGPUMaterialContextSamplerCache {\r\n    sampler: Nullable<TextureSampler>;\r\n    hashCode: number;\r\n}\r\n\r\n/** @internal */\r\ninterface IWebGPUMaterialContextTextureCache {\r\n    texture: Nullable<InternalTexture | ExternalTexture>;\r\n    isFloatOrDepthTexture: boolean;\r\n    isExternalTexture: boolean;\r\n}\r\n\r\n/** @internal */\r\nexport class WebGPUMaterialContext implements IMaterialContext {\r\n    private static _Counter = 0;\r\n\r\n    public uniqueId: number;\r\n    public updateId: number;\r\n    public isDirty: boolean;\r\n    public samplers: { [name: string]: Nullable<IWebGPUMaterialContextSamplerCache> };\r\n    public textures: { [name: string]: Nullable<IWebGPUMaterialContextTextureCache> };\r\n\r\n    // The texture state is a bitfield where each bit is set if the texture is a float32 texture (calculated in @WebGPUEngine._draw).\r\n    // Float32 textures must be handled differently because float filtering may not be supported by the underlying browser implementation.\r\n    // In this case, we must configure the sampler as \"non filtering\", as well as set the texture sample type to \"unfilterable-float\" when creating the bind group layout.\r\n    // When that happens, we end up with different bind group layouts (depending on which type of textures have been set in the material), that we must all store\r\n    // in the WebGPUPipelineContext (see @WebGPUPipelineContext.bindGroupLayouts) for later retrieval in the bind group cache implementation (see @WebGPUCacheBindGroups.getBindGroups), thanks to this property.\r\n    // There's the same problem with depth textures, where \"float\" filtering is not supported either.\r\n    public textureState: number;\r\n\r\n    public get forceBindGroupCreation() {\r\n        // If there is at least one external texture to bind, we must recreate the bind groups each time\r\n        // because we need to retrieve a new texture each frame (by calling device.importExternalTexture)\r\n        return this._numExternalTextures > 0;\r\n    }\r\n\r\n    public get hasFloatOrDepthTextures() {\r\n        return this._numFloatOrDepthTextures > 0;\r\n    }\r\n\r\n    protected _numFloatOrDepthTextures: number;\r\n    protected _numExternalTextures: number;\r\n\r\n    constructor() {\r\n        this.uniqueId = WebGPUMaterialContext._Counter++;\r\n        this.updateId = 0;\r\n        this.textureState = 0;\r\n        this.reset();\r\n    }\r\n\r\n    public reset(): void {\r\n        this.samplers = {};\r\n        this.textures = {};\r\n        this.isDirty = true;\r\n        this._numFloatOrDepthTextures = 0;\r\n        this._numExternalTextures = 0;\r\n    }\r\n\r\n    public setSampler(name: string, sampler: Nullable<TextureSampler>): void {\r\n        let samplerCache = this.samplers[name];\r\n        let currentHashCode = -1;\r\n        if (!samplerCache) {\r\n            this.samplers[name] = samplerCache = { sampler, hashCode: 0 };\r\n        } else {\r\n            currentHashCode = samplerCache.hashCode;\r\n        }\r\n\r\n        samplerCache.sampler = sampler;\r\n        samplerCache.hashCode = sampler ? WebGPUCacheSampler.GetSamplerHashCode(sampler) : 0;\r\n\r\n        const isDirty = currentHashCode !== samplerCache.hashCode;\r\n        if (isDirty) {\r\n            this.updateId++;\r\n        }\r\n\r\n        this.isDirty ||= isDirty;\r\n    }\r\n\r\n    public setTexture(name: string, texture: Nullable<InternalTexture | ExternalTexture>): void {\r\n        let textureCache = this.textures[name];\r\n        let currentTextureId = -1;\r\n        if (!textureCache) {\r\n            this.textures[name] = textureCache = { texture, isFloatOrDepthTexture: false, isExternalTexture: false };\r\n        } else {\r\n            currentTextureId = textureCache.texture?.uniqueId ?? -1;\r\n        }\r\n\r\n        if (textureCache.isExternalTexture) {\r\n            this._numExternalTextures--;\r\n        }\r\n        if (textureCache.isFloatOrDepthTexture) {\r\n            this._numFloatOrDepthTextures--;\r\n        }\r\n\r\n        if (texture) {\r\n            textureCache.isFloatOrDepthTexture =\r\n                texture.type === Constants.TEXTURETYPE_FLOAT ||\r\n                (texture.format >= Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 && texture.format <= Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8);\r\n            textureCache.isExternalTexture = ExternalTexture.IsExternalTexture(texture);\r\n            if (textureCache.isFloatOrDepthTexture) {\r\n                this._numFloatOrDepthTextures++;\r\n            }\r\n            if (textureCache.isExternalTexture) {\r\n                this._numExternalTextures++;\r\n            }\r\n        } else {\r\n            textureCache.isFloatOrDepthTexture = false;\r\n            textureCache.isExternalTexture = false;\r\n        }\r\n\r\n        textureCache.texture = texture;\r\n\r\n        const isDirty = currentTextureId !== (texture?.uniqueId ?? -1);\r\n        if (isDirty) {\r\n            this.updateId++;\r\n        }\r\n\r\n        this.isDirty ||= isDirty;\r\n    }\r\n}\r\n"]}