import { Engine, Scene, ArcRotateCamera, HemisphericLight, Vector3, Color3, MeshBuilder, StandardMaterial, PointerEventTypes } from '@babylonjs/core';
import '@babylonjs/core/Cameras/Inputs';
import { TridomGame } from './game/TridomGame.js';
import { GameUI } from './ui/GameUI.js';

class TridomApp {
    constructor() {
        this.canvas = null;
        this.engine = null;
        this.scene = null;
        this.camera = null;
        this.game = null;
        this.ui = null;

        this.init();
    }

    async init() {
        // Get canvas element
        this.canvas = document.getElementById('renderCanvas');

        // Create Babylon.js engine
        this.engine = new Engine(this.canvas, true);

        // Create scene
        this.scene = new Scene(this.engine);
        this.scene.clearColor = new Color3(0.2, 0.3, 0.5);

        // Setup camera
        this.setupCamera();

        // Setup lighting
        this.setupLighting();

        // Create game board environment
        this.createGameEnvironment();

        // Initialize game
        this.game = new TridomGame(this.scene);

        // Initialize UI
        this.ui = new GameUI(this.game);
        this.ui.initialize();

        // Setup scene interactions
        this.setupSceneInteractions();

        // Start render loop
        this.engine.runRenderLoop(() => {
            this.scene.render();
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.engine.resize();
        });

        console.log('Tridom game initialized successfully!');
    }

    setupCamera() {
        // Create arc rotate camera
        this.camera = new ArcRotateCamera(
            'camera',
            -Math.PI / 2,  // alpha (horizontal rotation)
            Math.PI / 3,   // beta (vertical rotation)
            15,            // radius
            Vector3.Zero(), // target
            this.scene
        );

        // Set camera limits
        this.camera.setTarget(Vector3.Zero());
        this.camera.lowerBetaLimit = 0.1;
        this.camera.upperBetaLimit = Math.PI / 2;
        this.camera.lowerRadiusLimit = 5;
        this.camera.upperRadiusLimit = 25;

        // Attach camera controls to canvas
        this.camera.attachToCanvas(this.canvas, true);
    }

    setupLighting() {
        // Create hemisphere light
        const light = new HemisphericLight('light', new Vector3(0, 1, 0), this.scene);
        light.intensity = 0.8;
        light.diffuse = new Color3(1, 1, 1);
        light.specular = new Color3(1, 1, 1);
        light.groundColor = new Color3(0.3, 0.3, 0.3);
    }

    createGameEnvironment() {
        // Create game table/surface
        const table = MeshBuilder.CreateBox('table', {
            width: 20,
            height: 0.2,
            depth: 20
        }, this.scene);

        table.position.y = -0.5;

        const tableMaterial = new StandardMaterial('tableMaterial', this.scene);
        tableMaterial.diffuseColor = new Color3(0.4, 0.2, 0.1); // Brown wood color
        tableMaterial.specularColor = new Color3(0.1, 0.1, 0.1);
        table.material = tableMaterial;

        // Create a subtle grid pattern on the table
        const gridLines = [];
        const gridSize = 20;
        const gridSpacing = 1;

        for (let i = -gridSize / 2; i <= gridSize / 2; i += gridSpacing) {
            // Vertical lines
            const vLine = MeshBuilder.CreateBox(`vLine_${i}`, {
                width: 0.02,
                height: 0.01,
                depth: gridSize
            }, this.scene);
            vLine.position = new Vector3(i, -0.4, 0);

            // Horizontal lines
            const hLine = MeshBuilder.CreateBox(`hLine_${i}`, {
                width: gridSize,
                height: 0.01,
                depth: 0.02
            }, this.scene);
            hLine.position = new Vector3(0, -0.4, i);

            // Grid material
            const gridMaterial = new StandardMaterial(`gridMaterial_${i}`, this.scene);
            gridMaterial.diffuseColor = new Color3(0.3, 0.3, 0.3);
            gridMaterial.alpha = 0.3;

            vLine.material = gridMaterial;
            hLine.material = gridMaterial;
        }

        // Create center marker
        const centerMarker = MeshBuilder.CreateSphere('centerMarker', {
            diameter: 0.2
        }, this.scene);
        centerMarker.position = new Vector3(0, -0.3, 0);

        const centerMaterial = new StandardMaterial('centerMaterial', this.scene);
        centerMaterial.diffuseColor = new Color3(1, 0.8, 0.2); // Gold
        centerMaterial.emissiveColor = new Color3(0.2, 0.16, 0.04);
        centerMarker.material = centerMaterial;
    }

    setupSceneInteractions() {
        // Handle mouse/touch interactions
        this.scene.onPointerObservable.add((pointerInfo) => {
            switch (pointerInfo.type) {
                case PointerEventTypes.POINTERDOWN:
                    this.onPointerDown(pointerInfo);
                    break;
                case PointerEventTypes.POINTERUP:
                    this.onPointerUp(pointerInfo);
                    break;
                case PointerEventTypes.POINTERMOVE:
                    this.onPointerMove(pointerInfo);
                    break;
            }
        });
    }

    onPointerDown(pointerInfo) {
        const pickInfo = pointerInfo.pickInfo;

        if (pickInfo.hit && pickInfo.pickedMesh) {
            const mesh = pickInfo.pickedMesh;

            // Check if clicked mesh is a tile
            if (mesh.tridomTile) {
                this.ui.onTileClicked(mesh.tridomTile);
            } else if (mesh.name === 'table') {
                // Clicked on table - try to place selected tile
                const worldPosition = pickInfo.pickedPoint;
                const boardPosition = new Vector3(worldPosition.x, 0, worldPosition.z);
                this.ui.onBoardClicked(boardPosition);
            }
        }
    }

    onPointerUp(pointerInfo) {
        // Handle pointer up events if needed
    }

    onPointerMove(pointerInfo) {
        // Handle hover effects if needed
        const pickInfo = pointerInfo.pickInfo;

        if (pickInfo.hit && pickInfo.pickedMesh) {
            const mesh = pickInfo.pickedMesh;

            // Add hover effects for tiles
            if (mesh.tridomTile && !mesh.tridomTile.isSelected) {
                // Subtle highlight on hover
                mesh.tridomTile.material.emissiveColor = new Color3(0.1, 0.1, 0.1);
            }
        } else {
            // Remove hover effects
            this.game.board.placedTiles.forEach(tile => {
                if (!tile.isSelected) {
                    tile.material.emissiveColor = new Color3(0, 0, 0);
                }
            });
        }
    }

    // Utility methods
    dispose() {
        if (this.ui) {
            this.ui.dispose();
        }
        if (this.game) {
            this.game.dispose();
        }
        if (this.engine) {
            this.engine.dispose();
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const app = new TridomApp();

    // Make app globally accessible for debugging
    window.tridomApp = app;
});

// Handle page unload
window.addEventListener('beforeunload', () => {
    if (window.tridomApp) {
        window.tridomApp.dispose();
    }
});
