{"version": 3, "file": "webXROculusTouchMotionController.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/motionController/webXROculusTouchMotionController.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAChF,OAAO,EAAE,4BAA4B,EAAE,MAAM,gCAAgC,CAAC;AAG9E,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAErD;;;GAGG;AACH,MAAM,OAAO,gCAAiC,SAAQ,6BAA6B;IAsB/E,YACI,KAAY,EACZ,aAA6C,EAC7C,UAAsC,EACtC,iBAA0B,KAAK,EACvB,0BAAmC,KAAK;QAEhD,KAAK,CAAC,KAAK,EAAE,kBAAkB,CAAC,UAAU,CAAC,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;QAFhE,4BAAuB,GAAvB,uBAAuB,CAAiB;QAP7C,cAAS,GAAG,cAAc,CAAC;IAUlC,CAAC;IAES,mBAAmB;QACzB,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE;YAC5B,QAAQ,GAAG,gCAAgC,CAAC,mBAAmB,CAAC;SACnE;aAAM;YACH,+CAA+C;YAC/C,QAAQ,GAAG,gCAAgC,CAAC,oBAAoB,CAAC;SACpE;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,gCAAgC,CAAC,oBAAoB,CAAC,CAAC,CAAC,gCAAgC,CAAC,cAAc,CAAC;QACvI,OAAO;YACH,QAAQ;YACR,IAAI;SACP,CAAC;IACN,CAAC;IAES,2BAA2B;QACjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB,CAAC,OAAuB;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChC,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9D,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YAClC,MAAM,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACzC,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,8BAA8B,CAAC,GAAG,CACnC,CAAC,SAAS,EAAE,EAAE;oBACV,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACzC,OAAO;qBACV;oBAED,QAAQ,EAAE,EAAE;wBACR,KAAK,qBAAqB,EAAE,gBAAgB;4BACxC,IAAI,CAAC,OAAO,EAAE;gCACK,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,CAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC;gCAC1E,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,CAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC;gCAC5E,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,CAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC;6BAC9F;4BACD,OAAO;wBACX,KAAK,qBAAqB,EAAE,oBAAoB;4BAC5C,IAAI,CAAC,OAAO,EAAE;gCACK,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,CAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,gBAAgB,GAAG,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC;6BACjH;4BACD,OAAO;wBACX,KAAK,wBAAwB,EAAE,aAAa;4BACxC,OAAO;wBACX,KAAK,UAAU,CAAC;wBAChB,KAAK,UAAU;4BACX,IAAI,CAAC,OAAO,EAAE;gCACV,IAAI,SAAS,CAAC,OAAO,EAAE;oCACJ,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,CAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;iCAC5E;qCAAM;oCACY,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,CAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;iCACvE;6BACJ;4BACD,OAAO;wBACX,KAAK,UAAU,CAAC;wBAChB,KAAK,UAAU;4BACX,IAAI,CAAC,OAAO,EAAE;gCACV,IAAI,SAAS,CAAC,OAAO,EAAE;oCACJ,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,CAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;iCAC5E;qCAAM;oCACY,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,CAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;iCACvE;6BACJ;4BACD,OAAO;qBACd;gBACL,CAAC,EACD,SAAS,EACT,IAAI,CACP,CAAC;aACL;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAES,YAAY,CAAC,MAAsB;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7E,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE;YAClC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;SAChF;QAED,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACpB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YACjB,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SACnC;aAAM;YACH,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;YACjC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;SACpC;QACD,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC/C,CAAC;IAES,YAAY;QAClB,6CAA6C;IACjD,CAAC;IAED;;;;OAIG;IACK,QAAQ;QACZ,0FAA0F;QAC1F,OAAO,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC;IACnF,CAAC;;AAzID;;GAEG;AACW,+CAAc,GAAW,2CAA2C,AAAtD,CAAuD;AACnF;;GAEG;AACW,oDAAmB,GAAW,cAAc,AAAzB,CAA0B;AAC3D;;GAEG;AACW,qDAAoB,GAAW,eAAe,AAA1B,CAA2B;AAC7D;;GAEG;AACW,qDAAoB,GAAW,gDAAgD,AAA3D,CAA4D;AA6HlG,uBAAuB;AACvB,4BAA4B,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,OAAsB,EAAE,KAAY,EAAE,EAAE;IACrG,OAAO,IAAI,gCAAgC,CAAC,KAAK,EAAO,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;AACjG,CAAC,CAAC,CAAC;AAEH,4BAA4B,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,CAAC,OAAsB,EAAE,KAAY,EAAE,EAAE;IAC5G,OAAO,IAAI,gCAAgC,CAAC,KAAK,EAAO,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AACvG,CAAC,CAAC,CAAC;AAEH,MAAM,kBAAkB,GAA+B;IACnD,IAAI,EAAE;QACF,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE;YACR,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;YACD,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;YACD,wBAAwB,EAAE;gBACtB,IAAI,EAAE,YAAY;gBAClB,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;iBACX;gBACD,YAAY,EAAE,wBAAwB;gBACtC,eAAe,EAAE,EAAE;aACtB;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,UAAU;gBACxB,eAAe,EAAE,EAAE;aACtB;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,UAAU;gBACxB,eAAe,EAAE,EAAE;aACtB;YACD,SAAS,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,WAAW;gBACzB,eAAe,EAAE,EAAE;aACtB;SACJ;QACD,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE,sBAAsB;QACpC,SAAS,EAAE,UAAU;KACxB;IACD,KAAK,EAAE;QACH,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE;YACR,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;YACD,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE,EAAE;aACtB;YACD,wBAAwB,EAAE;gBACtB,IAAI,EAAE,YAAY;gBAClB,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;iBACX;gBACD,YAAY,EAAE,wBAAwB;gBACtC,eAAe,EAAE,EAAE;aACtB;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,UAAU;gBACxB,eAAe,EAAE,EAAE;aACtB;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,QAAQ;gBACd,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,UAAU;gBACxB,eAAe,EAAE,EAAE;aACtB;YACD,SAAS,EAAE;gBACP,IAAI,EAAE,QAAQ;gBACd,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,WAAW;gBACzB,eAAe,EAAE,EAAE;aACtB;SACJ;QACD,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE,uBAAuB;QACrC,SAAS,EAAE,WAAW;KACzB;CACJ,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { IMinimalMotionControllerObject, MotionControllerHandedness, IMotionControllerLayoutMap } from \"./webXRAbstractMotionController\";\r\nimport { WebXRAbstractMotionController } from \"./webXRAbstractMotionController\";\r\nimport { WebXRMotionControllerManager } from \"./webXRMotionControllerManager\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Mesh } from \"../../Meshes/mesh\";\r\nimport { Quaternion } from \"../../Maths/math.vector\";\r\n\r\n/**\r\n * The motion controller class for oculus touch (quest, rift).\r\n * This class supports legacy mapping as well the standard xr mapping\r\n */\r\nexport class WebXROculusTouchMotionController extends WebXRAbstractMotionController {\r\n    private _modelRootNode: AbstractMesh;\r\n\r\n    /**\r\n     * The base url used to load the left and right controller models\r\n     */\r\n    public static MODEL_BASE_URL: string = \"https://controllers.babylonjs.com/oculus/\";\r\n    /**\r\n     * The name of the left controller model file\r\n     */\r\n    public static MODEL_LEFT_FILENAME: string = \"left.babylon\";\r\n    /**\r\n     * The name of the right controller model file\r\n     */\r\n    public static MODEL_RIGHT_FILENAME: string = \"right.babylon\";\r\n    /**\r\n     * Base Url for the Quest controller model.\r\n     */\r\n    public static QUEST_MODEL_BASE_URL: string = \"https://controllers.babylonjs.com/oculusQuest/\";\r\n\r\n    public profileId = \"oculus-touch\";\r\n\r\n    constructor(\r\n        scene: Scene,\r\n        gamepadObject: IMinimalMotionControllerObject,\r\n        handedness: MotionControllerHandedness,\r\n        _legacyMapping: boolean = false,\r\n        private _forceLegacyControllers: boolean = false\r\n    ) {\r\n        super(scene, OculusTouchLayouts[handedness], gamepadObject, handedness);\r\n    }\r\n\r\n    protected _getFilenameAndPath(): { filename: string; path: string } {\r\n        let filename = \"\";\r\n        if (this.handedness === \"left\") {\r\n            filename = WebXROculusTouchMotionController.MODEL_LEFT_FILENAME;\r\n        } else {\r\n            // Right is the default if no hand is specified\r\n            filename = WebXROculusTouchMotionController.MODEL_RIGHT_FILENAME;\r\n        }\r\n\r\n        const path = this._isQuest() ? WebXROculusTouchMotionController.QUEST_MODEL_BASE_URL : WebXROculusTouchMotionController.MODEL_BASE_URL;\r\n        return {\r\n            filename,\r\n            path,\r\n        };\r\n    }\r\n\r\n    protected _getModelLoadingConstraints(): boolean {\r\n        return true;\r\n    }\r\n\r\n    protected _processLoadedModel(_meshes: AbstractMesh[]): void {\r\n        const isQuest = this._isQuest();\r\n        const triggerDirection = this.handedness === \"right\" ? -1 : 1;\r\n\r\n        this.getComponentIds().forEach((id) => {\r\n            const comp = id && this.getComponent(id);\r\n            if (comp) {\r\n                comp.onButtonStateChangedObservable.add(\r\n                    (component) => {\r\n                        if (!this.rootMesh || this.disableAnimation) {\r\n                            return;\r\n                        }\r\n\r\n                        switch (id) {\r\n                            case \"xr-standard-trigger\": // index trigger\r\n                                if (!isQuest) {\r\n                                    (<AbstractMesh>this._modelRootNode.getChildren()[3]).rotation.x = -component.value * 0.2;\r\n                                    (<AbstractMesh>this._modelRootNode.getChildren()[3]).position.y = -component.value * 0.005;\r\n                                    (<AbstractMesh>this._modelRootNode.getChildren()[3]).position.z = -component.value * 0.005;\r\n                                }\r\n                                return;\r\n                            case \"xr-standard-squeeze\": // secondary trigger\r\n                                if (!isQuest) {\r\n                                    (<AbstractMesh>this._modelRootNode.getChildren()[4]).position.x = triggerDirection * component.value * 0.0035;\r\n                                }\r\n                                return;\r\n                            case \"xr-standard-thumbstick\": // thumbstick\r\n                                return;\r\n                            case \"a-button\":\r\n                            case \"x-button\":\r\n                                if (!isQuest) {\r\n                                    if (component.pressed) {\r\n                                        (<AbstractMesh>this._modelRootNode.getChildren()[1]).position.y = -0.001;\r\n                                    } else {\r\n                                        (<AbstractMesh>this._modelRootNode.getChildren()[1]).position.y = 0;\r\n                                    }\r\n                                }\r\n                                return;\r\n                            case \"b-button\":\r\n                            case \"y-button\":\r\n                                if (!isQuest) {\r\n                                    if (component.pressed) {\r\n                                        (<AbstractMesh>this._modelRootNode.getChildren()[2]).position.y = -0.001;\r\n                                    } else {\r\n                                        (<AbstractMesh>this._modelRootNode.getChildren()[2]).position.y = 0;\r\n                                    }\r\n                                }\r\n                                return;\r\n                        }\r\n                    },\r\n                    undefined,\r\n                    true\r\n                );\r\n            }\r\n        });\r\n    }\r\n\r\n    protected _setRootMesh(meshes: AbstractMesh[]): void {\r\n        this.rootMesh = new Mesh(this.profileId + \" \" + this.handedness, this.scene);\r\n        if (!this.scene.useRightHandedSystem) {\r\n            this.rootMesh.rotationQuaternion = Quaternion.FromEulerAngles(0, Math.PI, 0);\r\n        }\r\n\r\n        meshes.forEach((mesh) => {\r\n            mesh.isPickable = false;\r\n        });\r\n        if (this._isQuest()) {\r\n            this._modelRootNode = meshes[0];\r\n        } else {\r\n            this._modelRootNode = meshes[1];\r\n            this.rootMesh.position.y = 0.034;\r\n            this.rootMesh.position.z = 0.052;\r\n        }\r\n        this._modelRootNode.parent = this.rootMesh;\r\n    }\r\n\r\n    protected _updateModel(): void {\r\n        // no-op. model is updated using observables.\r\n    }\r\n\r\n    /**\r\n     * Is this the new type of oculus touch. At the moment both have the same profile and it is impossible to differentiate\r\n     * between the touch and touch 2.\r\n     * @returns true if this is the new type of oculus touch controllers.\r\n     */\r\n    private _isQuest() {\r\n        // this is SADLY the only way to currently check. Until proper profiles will be available.\r\n        return !!navigator.userAgent.match(/Quest/gi) && !this._forceLegacyControllers;\r\n    }\r\n}\r\n\r\n// register the profile\r\nWebXRMotionControllerManager.RegisterController(\"oculus-touch\", (xrInput: XRInputSource, scene: Scene) => {\r\n    return new WebXROculusTouchMotionController(scene, <any>xrInput.gamepad, xrInput.handedness);\r\n});\r\n\r\nWebXRMotionControllerManager.RegisterController(\"oculus-touch-legacy\", (xrInput: XRInputSource, scene: Scene) => {\r\n    return new WebXROculusTouchMotionController(scene, <any>xrInput.gamepad, xrInput.handedness, true);\r\n});\r\n\r\nconst OculusTouchLayouts: IMotionControllerLayoutMap = {\r\n    left: {\r\n        selectComponentId: \"xr-standard-trigger\",\r\n        components: {\r\n            \"xr-standard-trigger\": {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 0,\r\n                },\r\n                rootNodeName: \"xr_standard_trigger\",\r\n                visualResponses: {},\r\n            },\r\n            \"xr-standard-squeeze\": {\r\n                type: \"squeeze\",\r\n                gamepadIndices: {\r\n                    button: 1,\r\n                },\r\n                rootNodeName: \"xr_standard_squeeze\",\r\n                visualResponses: {},\r\n            },\r\n            \"xr-standard-thumbstick\": {\r\n                type: \"thumbstick\",\r\n                gamepadIndices: {\r\n                    button: 3,\r\n                    xAxis: 2,\r\n                    yAxis: 3,\r\n                },\r\n                rootNodeName: \"xr_standard_thumbstick\",\r\n                visualResponses: {},\r\n            },\r\n            \"x-button\": {\r\n                type: \"button\",\r\n                gamepadIndices: {\r\n                    button: 4,\r\n                },\r\n                rootNodeName: \"x_button\",\r\n                visualResponses: {},\r\n            },\r\n            \"y-button\": {\r\n                type: \"button\",\r\n                gamepadIndices: {\r\n                    button: 5,\r\n                },\r\n                rootNodeName: \"y_button\",\r\n                visualResponses: {},\r\n            },\r\n            thumbrest: {\r\n                type: \"button\",\r\n                gamepadIndices: {\r\n                    button: 6,\r\n                },\r\n                rootNodeName: \"thumbrest\",\r\n                visualResponses: {},\r\n            },\r\n        },\r\n        gamepadMapping: \"xr-standard\",\r\n        rootNodeName: \"oculus-touch-v2-left\",\r\n        assetPath: \"left.glb\",\r\n    },\r\n    right: {\r\n        selectComponentId: \"xr-standard-trigger\",\r\n        components: {\r\n            \"xr-standard-trigger\": {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 0,\r\n                },\r\n                rootNodeName: \"xr_standard_trigger\",\r\n                visualResponses: {},\r\n            },\r\n            \"xr-standard-squeeze\": {\r\n                type: \"squeeze\",\r\n                gamepadIndices: {\r\n                    button: 1,\r\n                },\r\n                rootNodeName: \"xr_standard_squeeze\",\r\n                visualResponses: {},\r\n            },\r\n            \"xr-standard-thumbstick\": {\r\n                type: \"thumbstick\",\r\n                gamepadIndices: {\r\n                    button: 3,\r\n                    xAxis: 2,\r\n                    yAxis: 3,\r\n                },\r\n                rootNodeName: \"xr_standard_thumbstick\",\r\n                visualResponses: {},\r\n            },\r\n            \"a-button\": {\r\n                type: \"button\",\r\n                gamepadIndices: {\r\n                    button: 4,\r\n                },\r\n                rootNodeName: \"a_button\",\r\n                visualResponses: {},\r\n            },\r\n            \"b-button\": {\r\n                type: \"button\",\r\n                gamepadIndices: {\r\n                    button: 5,\r\n                },\r\n                rootNodeName: \"b_button\",\r\n                visualResponses: {},\r\n            },\r\n            thumbrest: {\r\n                type: \"button\",\r\n                gamepadIndices: {\r\n                    button: 6,\r\n                },\r\n                rootNodeName: \"thumbrest\",\r\n                visualResponses: {},\r\n            },\r\n        },\r\n        gamepadMapping: \"xr-standard\",\r\n        rootNodeName: \"oculus-touch-v2-right\",\r\n        assetPath: \"right.glb\",\r\n    },\r\n};\r\n"]}