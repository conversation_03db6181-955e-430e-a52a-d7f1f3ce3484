{"version": 3, "file": "physicsShapeProximityCastQuery.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Physics/physicsShapeProximityCastQuery.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Quaternion, Vector3 } from \"../Maths/math.vector\";\r\nimport type { PhysicsShape } from \"./v2/physicsShape\";\r\nimport type { PhysicsBody } from \"./v2/physicsBody\";\r\n\r\n/**\r\n * Query for shape proximity.\r\n */\r\nexport interface IPhysicsShapeProximityCastQuery {\r\n    /**\r\n     * The shape to test proximity against\r\n     */\r\n    shape: PhysicsShape;\r\n    /**\r\n     * The position of shape\r\n     */\r\n    position: Vector3;\r\n    /**\r\n     * The rotation of shape\r\n     */\r\n    rotation: Quaternion;\r\n    /**\r\n     * Maximum distance to check for collisions. Can be set to 0 to check for overlaps.\r\n     */\r\n    maxDistance: number;\r\n    /**\r\n     * Should trigger collisions be considered in the query?\r\n     */\r\n    shouldHitTriggers: boolean;\r\n    /**\r\n     * Ignores the body passed if it is in the query\r\n     */\r\n    ignoreBody?: PhysicsBody;\r\n}\r\n"]}