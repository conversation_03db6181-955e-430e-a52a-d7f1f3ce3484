{"version": 3, "file": "physicsPointProximityQuery.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Physics/physicsPointProximityQuery.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Vector3 } from \"../Maths/math.vector\";\r\nimport type { IRaycastQuery } from \"./physicsRaycastResult\";\r\nimport type { PhysicsBody } from \"./v2/physicsBody\";\r\n\r\n/**\r\n * Interface for point proximity query.\r\n */\r\nexport interface IPhysicsPointProximityQuery {\r\n    /**\r\n     * The position of the query\r\n     */\r\n    position: Vector3;\r\n    /**\r\n     * Maximum distance to check for collisions. Can be set to 0 to check for overlaps.\r\n     */\r\n    maxDistance: number;\r\n    /**\r\n     * Collision filter for the query.\r\n     */\r\n    collisionFilter: IRaycastQuery;\r\n    /**\r\n     * Should trigger collisions be considered in the query?\r\n     */\r\n    shouldHitTriggers: boolean;\r\n    /**\r\n     * Should the query ignore the body that is passed in?\r\n     */\r\n    ignoreBody?: PhysicsBody;\r\n}\r\n"]}