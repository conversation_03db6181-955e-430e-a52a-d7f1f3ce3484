{"version": 3, "file": "WebXRDepthSensing.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRDepthSensing.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,qCAAqC,CAAC;AACjE,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAEjF,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AACzC,OAAO,EAAE,OAAO,EAAE,MAAM,kCAAkC,CAAC;AAC3D,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,oBAAoB,EAAE,MAAM,0CAA0C,CAAC;AAChF,OAAO,EAAE,eAAe,EAAE,qBAAqB,EAAE,MAAM,0CAA0C,CAAC;AAqBlG;;;GAGG;AACH,MAAM,OAAO,iBAAkB,SAAQ,oBAAoB;IASvD;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,2BAA2B;QAClC,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,QAAQ,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAU,EAAE;YAC/C,KAAK,eAAe;gBAChB,OAAO,KAAK,CAAC;YACjB,KAAK,eAAe;gBAChB,OAAO,KAAK,CAAC;SACpB;IACL,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,QAAQ,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe,EAAE;YACpD,KAAK,iBAAiB;gBAClB,OAAO,QAAQ,CAAC;YACpB,KAAK,SAAS;gBACV,OAAO,OAAO,CAAC;SACtB;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,qBAAqB;QAC5B,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QACxD,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,MAAM,EAAE,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACnF,eAAe,CAAC,MAAM,GAAG,KAAK,CAAC;QAC/B,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC;QAChC,eAAe,CAAC,cAAc,GAAG,KAAK,CAAC;QACvC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC;QACpI,eAAe,CAAC,eAAe,GAAG,KAAK,CAAC;QACxC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC;QAC9H,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC,sBAAsB,CAAC;QAChE,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;QACxC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;QAC1C,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC,wBAAwB,CAAC;QAClE,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC,wBAAwB,CAAC;QAClE,eAAe,CAAC,gBAAgB,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QAElG,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,eAAe,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACpI,CAAC;IAQD;;OAEG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAmBD;;;;OAIG;IACH,YACI,iBAAsC,EACtB,OAAkC;QAElD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFT,YAAO,GAAP,OAAO,CAA2B;QAvI9C,WAAM,GAAqB,IAAI,CAAC;QAChC,YAAO,GAAqB,IAAI,CAAC;QACjC,sBAAiB,GAAqB,IAAI,CAAC;QAC3C,iCAA4B,GAA+B,IAAI,CAAC;QAChE,uBAAkB,GAA0B,IAAI,CAAC;QACjD,wBAAmB,GAA2B,IAAI,CAAC;QACnD,6BAAwB,GAAyB,IAAI,CAAC;QA4F9D;;;WAGG;QACI,gCAA2B,GAAqC,IAAI,UAAU,EAAwB,CAAC;QAoC1G,IAAI,CAAC,mBAAmB,GAAG,eAAe,CAAC;QAE3C,iDAAiD;QACjD,KAAK,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;IACzE,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAA2B;QACrC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACtB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,6BAA6B,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe,IAAI,IAAI,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC;QAClJ,IAAI,6BAA6B,EAAE;YAC/B,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC;QAEnH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE,CAAC;IAC7C,CAAC;IAES,UAAU,CAAC,QAAiB;QAClC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;QAE7D,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QACpD,IAAI,IAAI,IAAI,IAAI,EAAE;YACd,OAAO;SACV;QAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;YAC3B,QAAQ,IAAI,CAAC,UAAU,EAAE;gBACrB,KAAK,KAAK;oBACN,IAAI,CAAC,8CAA8C,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBAC1F,MAAM;gBAEV,KAAK,KAAK;oBACN,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;wBAClB,MAAM;qBACT;oBAED,IAAI,CAAC,gDAAgD,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBACnG,MAAM;gBAEV;oBACI,KAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;oBACnC,IAAI,CAAC,MAAM,EAAE,CAAC;oBACd,MAAM;aACb;SACJ;IACL,CAAC;IAEO,8CAA8C,CAAC,KAAc,EAAE,IAAY,EAAE,UAAgC;QACjH,MAAM,SAAS,GAAG,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,SAAS,KAAK,IAAI,EAAE;YACpB,OAAO;SACV;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,SAAkC,CAAC;QAEvG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAE/B,iDAAiD;QACjD,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAEnF,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;YAChC,IAAI,CAAC,wBAAwB,GAAG,UAAU,CAAC,cAAc,CACrD,IAAI,EACJ,KAAK,EACL,MAAM,EACN,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAC5B,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,oBAAoB,EAC5B,MAAM,CAAC,iBAAiB,CAC3B,CAAC;SACL;QAED,QAAQ,UAAU,EAAE;YAChB,KAAK,QAAQ;gBACT,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,gBAAgB,CAAC,CAAC,CAAC;gBACxH,MAAM;YAEV,KAAK,OAAO;gBACR,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,gBAAgB,CAAC,CAAC,CAAC;gBACtG,MAAM;YAEV;gBACI,MAAM;SACb;IACL,CAAC;IAEO,gDAAgD,CAAC,YAA4B,EAAE,IAAY,EAAE,UAAgC;QACjI,MAAM,SAAS,GAAG,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,SAAS,KAAK,IAAI,EAAE;YACpB,OAAO;SACV;QAED,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,SAAoC,CAAC;QAExE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC;QAEnC,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;QAC3C,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,MAAM,eAAe,GAAG,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAEzD,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;YAChC,IAAI,CAAC,wBAAwB,GAAG,UAAU,CAAC,cAAc,CACrD,IAAI,EACJ,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,oBAAoB,EAC5B,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAAC,MAAM,CAAC,iBAAiB,CACxF,CAAC;SACL;QAED,IAAI,CAAC,wBAAwB,CAAC,QAAQ,GAAG,eAAe,CAAC;IAC7D,CAAC;IAED;;;OAGG;IACI,yBAAyB;QAC5B,MAAM,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,CAAC;QAC/G,MAAM,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,CAAC;QAEzH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,oBAAoB,IAAI,oBAAoB,EAAE;gBAC9C,MAAM,MAAM,GAAmB,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;oBACtE,QAAQ,KAAK,EAAE;wBACX,KAAK,KAAK;4BACN,OAAO,eAAe,CAAC;wBAC3B,KAAK,KAAK;4BACN,OAAO,eAAe,CAAC;qBAC9B;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM,WAAW,GAAwB,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;oBACtF,QAAQ,MAAM,EAAE;wBACZ,KAAK,QAAQ;4BACT,OAAO,iBAAiB,CAAC;wBAC7B,KAAK,OAAO;4BACR,OAAO,SAAS,CAAC;qBACxB;gBACL,CAAC,CAAC,CAAC;gBAEH,OAAO,CAAC;oBACJ,YAAY,EAAE;wBACV,eAAe,EAAE,MAAM;wBACvB,oBAAoB,EAAE,WAAW;qBACpC;iBACJ,CAAC,CAAC;aACN;iBAAM;gBACH,OAAO,CAAC,EAAE,CAAC,CAAC;aACf;QACL,CAAC,CAAC,CAAC;IACP,CAAC;;AAtMD;;GAEG;AACoB,sBAAI,GAAG,gBAAgB,CAAC,aAAa,AAAjC,CAAkC;AAE7D;;;;GAIG;AACoB,yBAAO,GAAG,CAAC,AAAJ,CAAK;AA+LvC,oBAAoB,CAAC,eAAe,CAChC,iBAAiB,CAAC,IAAI,EACtB,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,iBAAiB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AAClE,CAAC,EACD,iBAAiB,CAAC,OAAO,EACzB,KAAK,CACR,CAAC", "sourcesContent": ["import { RawTexture } from \"../../Materials/Textures/rawTexture\";\r\nimport { WebXRFeatureName, WebXRFeaturesManager } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport { Texture } from \"../../Materials/Textures/texture\";\r\nimport { Engine } from \"../../Engines/engine\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { WebGLHardwareTexture } from \"../../Engines/WebGL/webGLHardwareTexture\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\n\r\nexport type WebXRDepthUsage = \"cpu\" | \"gpu\";\r\nexport type WebXRDepthDataFormat = \"ushort\" | \"float\";\r\n\r\n/**\r\n * Options for Depth Sensing feature\r\n */\r\nexport interface IWebXRDepthSensingOptions {\r\n    /**\r\n     *  The desired depth sensing usage for the session\r\n     */\r\n    usagePreference: WebXRDepthUsage[];\r\n    /**\r\n     * The desired depth sensing data format for the session\r\n     */\r\n    dataFormatPreference: WebXRDepthDataFormat[];\r\n}\r\n\r\ntype GetDepthInMetersType = (x: number, y: number) => number;\r\n\r\n/**\r\n * WebXR Feature for WebXR Depth Sensing Module\r\n * @since 5.49.1\r\n */\r\nexport class WebXRDepthSensing extends WebXRAbstractFeature {\r\n    private _width: Nullable<number> = null;\r\n    private _height: Nullable<number> = null;\r\n    private _rawValueToMeters: Nullable<number> = null;\r\n    private _normDepthBufferFromNormView: Nullable<XRRigidTransform> = null;\r\n    private _cachedDepthBuffer: Nullable<ArrayBuffer> = null;\r\n    private _cachedWebGLTexture: Nullable<WebGLTexture> = null;\r\n    private _cachedDepthImageTexture: Nullable<RawTexture> = null;\r\n\r\n    /**\r\n     * Width of depth data. If depth data is not exist, returns null.\r\n     */\r\n    public get width(): Nullable<number> {\r\n        return this._width;\r\n    }\r\n\r\n    /**\r\n     * Height of depth data. If depth data is not exist, returns null.\r\n     */\r\n    public get height(): Nullable<number> {\r\n        return this._height;\r\n    }\r\n\r\n    /**\r\n     * Scale factor by which the raw depth values must be multiplied in order to get the depths in meters.\r\n     */\r\n    public get rawValueToMeters(): Nullable<number> {\r\n        return this._rawValueToMeters;\r\n    }\r\n\r\n    /**\r\n     * An XRRigidTransform that needs to be applied when indexing into the depth buffer.\r\n     */\r\n    public get normDepthBufferFromNormView(): Nullable<XRRigidTransform> {\r\n        return this._normDepthBufferFromNormView;\r\n    }\r\n\r\n    /**\r\n     * Describes which depth-sensing usage (\"cpu\" or \"gpu\") is used.\r\n     */\r\n    public get depthUsage(): WebXRDepthUsage {\r\n        switch (this._xrSessionManager.session.depthUsage) {\r\n            case \"cpu-optimized\":\r\n                return \"cpu\";\r\n            case \"gpu-optimized\":\r\n                return \"gpu\";\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Describes which depth sensing data format (\"ushort\" or \"float\") is used.\r\n     */\r\n    public get depthDataFormat(): WebXRDepthDataFormat {\r\n        switch (this._xrSessionManager.session.depthDataFormat) {\r\n            case \"luminance-alpha\":\r\n                return \"ushort\";\r\n            case \"float32\":\r\n                return \"float\";\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Latest cached InternalTexture which containing depth buffer information.\r\n     * This can be used when the depth usage is \"gpu\".\r\n     */\r\n    public get latestInternalTexture(): Nullable<InternalTexture> {\r\n        if (!this._cachedWebGLTexture) {\r\n            return null;\r\n        }\r\n\r\n        const engine = this._xrSessionManager.scene.getEngine();\r\n        const internalTexture = new InternalTexture(engine, InternalTextureSource.Unknown);\r\n        internalTexture.isCube = false;\r\n        internalTexture.invertY = false;\r\n        internalTexture._useSRGBBuffer = false;\r\n        internalTexture.format = this.depthDataFormat === \"ushort\" ? Constants.TEXTUREFORMAT_LUMINANCE_ALPHA : Constants.TEXTUREFORMAT_RGBA;\r\n        internalTexture.generateMipMaps = false;\r\n        internalTexture.type = this.depthDataFormat === \"ushort\" ? Constants.TEXTURETYPE_UNSIGNED_SHORT : Constants.TEXTURETYPE_FLOAT;\r\n        internalTexture.samplingMode = Constants.TEXTURE_NEAREST_LINEAR;\r\n        internalTexture.width = this.width ?? 0;\r\n        internalTexture.height = this.height ?? 0;\r\n        internalTexture._cachedWrapU = Constants.TEXTURE_WRAP_ADDRESSMODE;\r\n        internalTexture._cachedWrapV = Constants.TEXTURE_WRAP_ADDRESSMODE;\r\n        internalTexture._hardwareTexture = new WebGLHardwareTexture(this._cachedWebGLTexture, engine._gl);\r\n\r\n        return internalTexture;\r\n    }\r\n\r\n    /**\r\n     * cached depth buffer\r\n     */\r\n    public get latestDepthBuffer(): Nullable<ArrayBufferView> {\r\n        if (!this._cachedDepthBuffer) {\r\n            return null;\r\n        }\r\n\r\n        return this.depthDataFormat === \"ushort\" ? new Uint16Array(this._cachedDepthBuffer) : new Float32Array(this._cachedDepthBuffer);\r\n    }\r\n\r\n    /**\r\n     * Event that notify when `DepthInformation.getDepthInMeters` is available.\r\n     * `getDepthInMeters` method needs active XRFrame (not available for cached XRFrame)\r\n     */\r\n    public onGetDepthInMetersAvailable: Observable<GetDepthInMetersType> = new Observable<GetDepthInMetersType>();\r\n\r\n    /**\r\n     * Latest cached Texture of depth image which is made from the depth buffer data.\r\n     */\r\n    public get latestDepthImageTexture(): Nullable<RawTexture> {\r\n        return this._cachedDepthImageTexture;\r\n    }\r\n\r\n    /**\r\n     * XRWebGLBinding which is used for acquiring WebGLDepthInformation\r\n     */\r\n    private _glBinding?: XRWebGLBinding;\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.DEPTH_SENSING;\r\n\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * Creates a new instance of the depth sensing feature\r\n     * @param _xrSessionManager the WebXRSessionManager\r\n     * @param options options for WebXR Depth Sensing Feature\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        public readonly options: IWebXRDepthSensingOptions\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"depth-sensing\";\r\n\r\n        // https://immersive-web.github.io/depth-sensing/\r\n        Tools.Warn(\"depth-sensing is an experimental and unstable feature.\");\r\n    }\r\n\r\n    /**\r\n     * attach this feature\r\n     * Will usually be called by the features manager\r\n     * @param force should attachment be forced (even when already attached)\r\n     * @returns true if successful.\r\n     */\r\n    public attach(force?: boolean | undefined): boolean {\r\n        if (!super.attach(force)) {\r\n            return false;\r\n        }\r\n\r\n        const isBothDepthUsageAndFormatNull = this._xrSessionManager.session.depthDataFormat == null || this._xrSessionManager.session.depthUsage == null;\r\n        if (isBothDepthUsageAndFormatNull) {\r\n            return false;\r\n        }\r\n\r\n        this._glBinding = new XRWebGLBinding(this._xrSessionManager.session, this._xrSessionManager.scene.getEngine()._gl);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached\r\n     */\r\n    public dispose(): void {\r\n        this._cachedDepthImageTexture?.dispose();\r\n    }\r\n\r\n    protected _onXRFrame(_xrFrame: XRFrame): void {\r\n        const referenceSPace = this._xrSessionManager.referenceSpace;\r\n\r\n        const pose = _xrFrame.getViewerPose(referenceSPace);\r\n        if (pose == null) {\r\n            return;\r\n        }\r\n\r\n        for (const view of pose.views) {\r\n            switch (this.depthUsage) {\r\n                case \"cpu\":\r\n                    this._updateDepthInformationAndTextureCPUDepthUsage(_xrFrame, view, this.depthDataFormat);\r\n                    break;\r\n\r\n                case \"gpu\":\r\n                    if (!this._glBinding) {\r\n                        break;\r\n                    }\r\n\r\n                    this._updateDepthInformationAndTextureWebGLDepthUsage(this._glBinding, view, this.depthDataFormat);\r\n                    break;\r\n\r\n                default:\r\n                    Tools.Error(\"Unknown depth usage\");\r\n                    this.detach();\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    private _updateDepthInformationAndTextureCPUDepthUsage(frame: XRFrame, view: XRView, dataFormat: WebXRDepthDataFormat): void {\r\n        const depthInfo = frame.getDepthInformation(view);\r\n        if (depthInfo === null) {\r\n            return;\r\n        }\r\n\r\n        const { data, width, height, rawValueToMeters, getDepthInMeters } = depthInfo as XRCPUDepthInformation;\r\n\r\n        this._width = width;\r\n        this._height = height;\r\n        this._rawValueToMeters = rawValueToMeters;\r\n        this._cachedDepthBuffer = data;\r\n\r\n        // to avoid Illegal Invocation error, bind `this`\r\n        this.onGetDepthInMetersAvailable.notifyObservers(getDepthInMeters.bind(depthInfo));\r\n\r\n        if (!this._cachedDepthImageTexture) {\r\n            this._cachedDepthImageTexture = RawTexture.CreateRTexture(\r\n                null,\r\n                width,\r\n                height,\r\n                this._xrSessionManager.scene,\r\n                false,\r\n                true,\r\n                Texture.NEAREST_SAMPLINGMODE,\r\n                Engine.TEXTURETYPE_FLOAT\r\n            );\r\n        }\r\n\r\n        switch (dataFormat) {\r\n            case \"ushort\":\r\n                this._cachedDepthImageTexture.update(Float32Array.from(new Uint16Array(data)).map((value) => value * rawValueToMeters));\r\n                break;\r\n\r\n            case \"float\":\r\n                this._cachedDepthImageTexture.update(new Float32Array(data).map((value) => value * rawValueToMeters));\r\n                break;\r\n\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n\r\n    private _updateDepthInformationAndTextureWebGLDepthUsage(webglBinding: XRWebGLBinding, view: XRView, dataFormat: WebXRDepthDataFormat): void {\r\n        const depthInfo = webglBinding.getDepthInformation(view);\r\n        if (depthInfo === null) {\r\n            return;\r\n        }\r\n\r\n        const { texture, width, height } = depthInfo as XRWebGLDepthInformation;\r\n\r\n        this._width = width;\r\n        this._height = height;\r\n        this._cachedWebGLTexture = texture;\r\n\r\n        const scene = this._xrSessionManager.scene;\r\n        const engine = scene.getEngine();\r\n        const internalTexture = engine.wrapWebGLTexture(texture);\r\n\r\n        if (!this._cachedDepthImageTexture) {\r\n            this._cachedDepthImageTexture = RawTexture.CreateRTexture(\r\n                null,\r\n                width,\r\n                height,\r\n                scene,\r\n                false,\r\n                true,\r\n                Texture.NEAREST_SAMPLINGMODE,\r\n                dataFormat === \"ushort\" ? Engine.TEXTURETYPE_UNSIGNED_BYTE : Engine.TEXTURETYPE_FLOAT\r\n            );\r\n        }\r\n\r\n        this._cachedDepthImageTexture._texture = internalTexture;\r\n    }\r\n\r\n    /**\r\n     * Extends the session init object if needed\r\n     * @returns augmentation object for the xr session init object.\r\n     */\r\n    public getXRSessionInitExtension(): Promise<Partial<XRSessionInit>> {\r\n        const isDepthUsageDeclared = this.options.usagePreference != null && this.options.usagePreference.length !== 0;\r\n        const isDataFormatDeclared = this.options.dataFormatPreference != null && this.options.dataFormatPreference.length !== 0;\r\n\r\n        return new Promise((resolve) => {\r\n            if (isDepthUsageDeclared && isDataFormatDeclared) {\r\n                const usages: XRDepthUsage[] = this.options.usagePreference.map((usage) => {\r\n                    switch (usage) {\r\n                        case \"cpu\":\r\n                            return \"cpu-optimized\";\r\n                        case \"gpu\":\r\n                            return \"gpu-optimized\";\r\n                    }\r\n                });\r\n                const dataFormats: XRDepthDataFormat[] = this.options.dataFormatPreference.map((format) => {\r\n                    switch (format) {\r\n                        case \"ushort\":\r\n                            return \"luminance-alpha\";\r\n                        case \"float\":\r\n                            return \"float32\";\r\n                    }\r\n                });\r\n\r\n                resolve({\r\n                    depthSensing: {\r\n                        usagePreference: usages,\r\n                        dataFormatPreference: dataFormats,\r\n                    },\r\n                });\r\n            } else {\r\n                resolve({});\r\n            }\r\n        });\r\n    }\r\n}\r\n\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRDepthSensing.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRDepthSensing(xrSessionManager, options);\r\n    },\r\n    WebXRDepthSensing.Version,\r\n    false\r\n);\r\n"]}