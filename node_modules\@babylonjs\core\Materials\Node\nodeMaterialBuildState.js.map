{"version": 3, "file": "nodeMaterialBuildState.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Node/nodeMaterialBuildState.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,+CAA+C,CAAC;AACtG,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAE5E,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAEnC;;GAEG;AACH,MAAM,OAAO,sBAAsB;IAAnC;QACI,sFAAsF;QAC/E,0BAAqB,GAAG,KAAK,CAAC;QACrC;;WAEG;QACI,eAAU,GAAa,EAAE,CAAC;QACjC;;WAEG;QACI,aAAQ,GAAa,EAAE,CAAC;QAC/B;;WAEG;QACI,cAAS,GAAa,EAAE,CAAC;QAChC;;WAEG;QACI,aAAQ,GAAa,EAAE,CAAC;QAC/B;;WAEG;QACI,cAAS,GAA8B,EAAE,CAAC;QACjD;;WAEG;QACI,eAAU,GAA8B,EAAE,CAAC;QAClD;;WAEG;QACI,kBAAa,GAA8B,EAAE,CAAC;QAMrD;;WAEG;QACI,aAAQ,GAA8B,EAAE,CAAC;QAUhD,gBAAgB;QACT,0BAAqB,GAAG,EAAE,CAAC;QAClC,gBAAgB;QACT,wBAAmB,GAAG,EAAE,CAAC;QAChC,gBAAgB;QACT,yBAAoB,GAAG,EAAE,CAAC;QACjC,gBAAgB;QACT,wBAAmB,GAAG,EAAE,CAAC;QAChC,gBAAgB;QACT,qBAAgB,GAAG,EAAE,CAAC;QAC7B,gBAAgB;QACT,iBAAY,GAAG,EAAE,CAAC;QAEjB,kCAA6B,GAAG,CAAC,CAAC;QAC1C,gBAAgB;QACT,4BAAuB,GAAG,EAAE,CAAC;QAEpC;;WAEG;QACI,sBAAiB,GAAG,EAAE,CAAC;IAwWlC,CAAC;IAtWG;;;OAGG;IACI,QAAQ,CAAC,KAA6B;QACzC,MAAM,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC;QACnD,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,CAAC;QAEzE,IAAI,CAAC,iBAAiB,GAAG,KAAK,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,sBAAsB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAElH,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,GAAG,KAAK,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,oBAAoB,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC9H;QAED,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,SAAS,EAAE;YACvC,YAAY,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;SACvD;QACD,IAAI,CAAC,iBAAiB,GAAG,KAAK,YAAY,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAExE,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC1C,IAAI,CAAC,iBAAiB,GAAG,GAAG,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAClF;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,iBAAiB,GAAG,GAAG,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,YAAY,EAAE,CAAC;SAC9E;QAED,IAAI,CAAC,iBAAiB,GAAG,GAAG,IAAI,CAAC,iBAAiB,KAAK,CAAC;QAExD,IAAI,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE;YACpC,IAAI,CAAC,iBAAiB,GAAG,KAAK,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACtI;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,iBAAiB,GAAG,KAAK,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5H;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,iBAAiB,GAAG,KAAK,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5H;QAED,IAAI,IAAI,CAAC,qBAAqB,IAAI,CAAC,cAAc,EAAE;YAC/C,IAAI,CAAC,iBAAiB,GAAG,KAAK,YAAY,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,qBAAqB,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAChI;QAED,IAAI,CAAC,iBAAiB,GAAG,0BAA0B,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC7E,IAAI,CAAC,iBAAiB,GAAG,mFAAmF,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAEtI,IAAI,cAAc,EAAE;YAChB,IAAI,CAAC,iBAAiB;gBAClB,gLAAgL;oBAChL,IAAI,CAAC,iBAAiB,CAAC;SAC9B;QAED,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,UAAU,EAAE;YACzC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YACjD,IAAI,CAAC,iBAAiB,GAAG,KAAK,SAAS,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACxE;QAED,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC;IAC1D,CAAC;IAED,gBAAgB;IAChB,IAAW,wBAAwB;QAC/B,OAAO,eAAe,IAAI,CAAC,6BAA6B,EAAE,QAAQ,CAAC;IACvE,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,MAAc;QACtC,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QAE5C,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,SAAS,EAAE;YACrD,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAE1C,uBAAuB;YACvB,IAAI,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,SAAS,EAAE;gBAC7C,OAAO,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;aACzD;YAED,OAAO,MAAM,CAAC;SACjB;aAAM;YACH,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;SAC3C;QAED,OAAO,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,MAAc;QACpC,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,SAAS,EAAE;YACnD,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SAC3C;aAAM;YACH,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC;SACzC;QAED,OAAO,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,IAAY;QACpC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,IAAY;QAC9B,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACjC,IAAI,CAAC,mBAAmB,IAAI,qBAAqB,IAAI,KAAK,CAAC;YAC3D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC5B;IACL,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,IAAY;QACnC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACjC,IAAI,CAAC,mBAAmB,IAAI,0BAA0B,IAAI,KAAK,CAAC;YAChE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC5B;IACL,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,IAA2C;QACzD,QAAQ,IAAI,EAAE;YACV,KAAK,qCAAqC,CAAC,KAAK;gBAC5C,OAAO,OAAO,CAAC;YACnB,KAAK,qCAAqC,CAAC,GAAG;gBAC1C,OAAO,KAAK,CAAC;YACjB,KAAK,qCAAqC,CAAC,OAAO;gBAC9C,OAAO,MAAM,CAAC;YAClB,KAAK,qCAAqC,CAAC,MAAM,CAAC;YAClD,KAAK,qCAAqC,CAAC,OAAO;gBAC9C,OAAO,MAAM,CAAC;YAClB,KAAK,qCAAqC,CAAC,MAAM,CAAC;YAClD,KAAK,qCAAqC,CAAC,OAAO;gBAC9C,OAAO,MAAM,CAAC;YAClB,KAAK,qCAAqC,CAAC,MAAM;gBAC7C,OAAO,MAAM,CAAC;SACrB;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,IAAY,EAAE,SAAiB,EAAE,SAAiB,EAAE;QACtE,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACvB,OAAO;SACV;QAED,IAAI,MAAM,EAAE;YACR,SAAS,GAAG,OAAO,MAAM,KAAK,SAAS,UAAU,CAAC;SACrD;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,IAAY,EAAE,IAAY,EAAE,QAAgB;QAC7D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;YACtB,OAAO;SACV;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;YAC9B,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;SACjC;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,oBAAoB,CACvB,WAAmB,EACnB,QAAgB,EAChB,OAIC;QAED,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,EAAE;YAC9B,OAAO,YAAY,WAAW,IAAI,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC,SAAS,KAAK,CAAC;SACvI;QAED,IAAI,IAAI,GAAG,MAAM,CAAC,oBAAoB,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QAE3D,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;YAC9B,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;SACjC;QAED,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAChE,MAAM,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACpD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;aACpE;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,wBAAwB,CAC3B,WAAmB,EACnB,QAAgB,EAChB,OAQC,EACD,WAAmB,EAAE;QAErB,MAAM,GAAG,GAAG,WAAW,GAAG,QAAQ,CAAC;QACnC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACrB,OAAO;SACV;QAED,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,gBAAgB,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;YAClJ,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,EAAE;gBAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,YAAY,WAAW,IAAI,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC,SAAS,KAAK,CAAC;aACtJ;iBAAM;gBACH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,YAAY,WAAW,IAAI,OAAO,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,EAAE,gBAAgB,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;aAC/H;YAED,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;gBAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;aAC/D;YAED,OAAO;SACV;QAED,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAE/D,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;YAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;SAC/D;QAED,IAAI,OAAO,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;YAC1E,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;YAC1E,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;YACzE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;SAC5E;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;SAClF;QAED,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;SAChF;QAED,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;SACrF;QAED,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAChE,MAAM,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACpD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;aAClG;SACJ;IACL,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,IAAY;QACrC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5C,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,IAAY,EAAE,IAAY,EAAE,SAAiB,EAAE,EAAE,SAAS,GAAG,KAAK;QAC5F,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/C,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEpC,IAAI,MAAM,EAAE;YACR,IAAI,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;gBAC/B,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,OAAO,MAAM,IAAI,CAAC;aAC3D;iBAAM;gBACH,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,IAAI,MAAM,IAAI,CAAC;aAC3F;SACJ;QACD,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,WAAW,IAAI,IAAI,IAAI,KAAK,CAAC;QACnE,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,UAAU,CAAC;SACpD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,IAAY,EAAE,IAAY,EAAE,SAAiB,EAAE,EAAE,SAAS,GAAG,KAAK;QAC5F,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACpC,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzB,IAAI,MAAM,EAAE;YACR,IAAI,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;gBAC/B,IAAI,CAAC,mBAAmB,IAAI,OAAO,MAAM,IAAI,CAAC;aACjD;iBAAM;gBACH,IAAI,CAAC,mBAAmB,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,IAAI,MAAM,IAAI,CAAC;aACjF;SACJ;QACD,IAAI,CAAC,mBAAmB,IAAI,WAAW,IAAI,IAAI,IAAI,KAAK,CAAC;QACzD,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,mBAAmB,IAAI,UAAU,CAAC;SAC1C;IACL,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,KAAa;QAC3B,IAAI,KAAK,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACvC,OAAO,GAAG,KAAK,IAAI,CAAC;SACvB;QAED,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC5B,CAAC;CACJ", "sourcesContent": ["import { NodeMaterialBlockConnectionPointTypes } from \"./Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport { NodeMaterialBlockTargets } from \"./Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialBuildStateSharedData } from \"./nodeMaterialBuildStateSharedData\";\r\nimport { Effect } from \"../effect\";\r\n\r\n/**\r\n * Class used to store node based material build state\r\n */\r\nexport class NodeMaterialBuildState {\r\n    /** Gets or sets a boolean indicating if the current state can emit uniform buffers */\r\n    public supportUniformBuffers = false;\r\n    /**\r\n     * Gets the list of emitted attributes\r\n     */\r\n    public attributes: string[] = [];\r\n    /**\r\n     * Gets the list of emitted uniforms\r\n     */\r\n    public uniforms: string[] = [];\r\n    /**\r\n     * Gets the list of emitted constants\r\n     */\r\n    public constants: string[] = [];\r\n    /**\r\n     * Gets the list of emitted samplers\r\n     */\r\n    public samplers: string[] = [];\r\n    /**\r\n     * Gets the list of emitted functions\r\n     */\r\n    public functions: { [key: string]: string } = {};\r\n    /**\r\n     * Gets the list of emitted extensions\r\n     */\r\n    public extensions: { [key: string]: string } = {};\r\n    /**\r\n     * Gets the list of emitted prePass outputs - if using the prepass\r\n     */\r\n    public prePassOutput: { [key: string]: string } = {};\r\n\r\n    /**\r\n     * Gets the target of the compilation state\r\n     */\r\n    public target: NodeMaterialBlockTargets;\r\n    /**\r\n     * Gets the list of emitted counters\r\n     */\r\n    public counters: { [key: string]: number } = {};\r\n\r\n    /**\r\n     * Shared data between multiple NodeMaterialBuildState instances\r\n     */\r\n    public sharedData: NodeMaterialBuildStateSharedData;\r\n\r\n    /** @internal */\r\n    public _vertexState: NodeMaterialBuildState;\r\n\r\n    /** @internal */\r\n    public _attributeDeclaration = \"\";\r\n    /** @internal */\r\n    public _uniformDeclaration = \"\";\r\n    /** @internal */\r\n    public _constantDeclaration = \"\";\r\n    /** @internal */\r\n    public _samplerDeclaration = \"\";\r\n    /** @internal */\r\n    public _varyingTransfer = \"\";\r\n    /** @internal */\r\n    public _injectAtEnd = \"\";\r\n\r\n    private _repeatableContentAnchorIndex = 0;\r\n    /** @internal */\r\n    public _builtCompilationString = \"\";\r\n\r\n    /**\r\n     * Gets the emitted compilation strings\r\n     */\r\n    public compilationString = \"\";\r\n\r\n    /**\r\n     * Finalize the compilation strings\r\n     * @param state defines the current compilation state\r\n     */\r\n    public finalize(state: NodeMaterialBuildState) {\r\n        const emitComments = state.sharedData.emitComments;\r\n        const isFragmentMode = this.target === NodeMaterialBlockTargets.Fragment;\r\n\r\n        this.compilationString = `\\n${emitComments ? \"//Entry point\\n\" : \"\"}void main(void) {\\n${this.compilationString}`;\r\n\r\n        if (this._constantDeclaration) {\r\n            this.compilationString = `\\n${emitComments ? \"//Constants\\n\" : \"\"}${this._constantDeclaration}\\n${this.compilationString}`;\r\n        }\r\n\r\n        let functionCode = \"\";\r\n        for (const functionName in this.functions) {\r\n            functionCode += this.functions[functionName] + `\\n`;\r\n        }\r\n        this.compilationString = `\\n${functionCode}\\n${this.compilationString}`;\r\n\r\n        if (!isFragmentMode && this._varyingTransfer) {\r\n            this.compilationString = `${this.compilationString}\\n${this._varyingTransfer}`;\r\n        }\r\n\r\n        if (this._injectAtEnd) {\r\n            this.compilationString = `${this.compilationString}\\n${this._injectAtEnd}`;\r\n        }\r\n\r\n        this.compilationString = `${this.compilationString}\\n}`;\r\n\r\n        if (this.sharedData.varyingDeclaration) {\r\n            this.compilationString = `\\n${emitComments ? \"//Varyings\\n\" : \"\"}${this.sharedData.varyingDeclaration}\\n${this.compilationString}`;\r\n        }\r\n\r\n        if (this._samplerDeclaration) {\r\n            this.compilationString = `\\n${emitComments ? \"//Samplers\\n\" : \"\"}${this._samplerDeclaration}\\n${this.compilationString}`;\r\n        }\r\n\r\n        if (this._uniformDeclaration) {\r\n            this.compilationString = `\\n${emitComments ? \"//Uniforms\\n\" : \"\"}${this._uniformDeclaration}\\n${this.compilationString}`;\r\n        }\r\n\r\n        if (this._attributeDeclaration && !isFragmentMode) {\r\n            this.compilationString = `\\n${emitComments ? \"//Attributes\\n\" : \"\"}${this._attributeDeclaration}\\n${this.compilationString}`;\r\n        }\r\n\r\n        this.compilationString = \"precision highp float;\\n\" + this.compilationString;\r\n        this.compilationString = \"#if defined(WEBGL2) || defines(WEBGPU)\\nprecision highp sampler2DArray;\\n#endif\\n\" + this.compilationString;\r\n\r\n        if (isFragmentMode) {\r\n            this.compilationString =\r\n                \"#if defined(PREPASS)\\r\\n#extension GL_EXT_draw_buffers : require\\r\\nlayout(location = 0) out highp vec4 glFragData[SCENE_MRT_COUNT];\\r\\nhighp vec4 gl_FragColor;\\r\\n#endif\\r\\n\" +\r\n                this.compilationString;\r\n        }\r\n\r\n        for (const extensionName in this.extensions) {\r\n            const extension = this.extensions[extensionName];\r\n            this.compilationString = `\\n${extension}\\n${this.compilationString}`;\r\n        }\r\n\r\n        this._builtCompilationString = this.compilationString;\r\n    }\r\n\r\n    /** @internal */\r\n    public get _repeatableContentAnchor(): string {\r\n        return `###___ANCHOR${this._repeatableContentAnchorIndex++}___###`;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getFreeVariableName(prefix: string): string {\r\n        prefix = prefix.replace(/[^a-zA-Z_]+/g, \"\");\r\n\r\n        if (this.sharedData.variableNames[prefix] === undefined) {\r\n            this.sharedData.variableNames[prefix] = 0;\r\n\r\n            // Check reserved words\r\n            if (prefix === \"output\" || prefix === \"texture\") {\r\n                return prefix + this.sharedData.variableNames[prefix];\r\n            }\r\n\r\n            return prefix;\r\n        } else {\r\n            this.sharedData.variableNames[prefix]++;\r\n        }\r\n\r\n        return prefix + this.sharedData.variableNames[prefix];\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getFreeDefineName(prefix: string): string {\r\n        if (this.sharedData.defineNames[prefix] === undefined) {\r\n            this.sharedData.defineNames[prefix] = 0;\r\n        } else {\r\n            this.sharedData.defineNames[prefix]++;\r\n        }\r\n\r\n        return prefix + this.sharedData.defineNames[prefix];\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _excludeVariableName(name: string) {\r\n        this.sharedData.variableNames[name] = 0;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emit2DSampler(name: string) {\r\n        if (this.samplers.indexOf(name) < 0) {\r\n            this._samplerDeclaration += `uniform sampler2D ${name};\\n`;\r\n            this.samplers.push(name);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emit2DArraySampler(name: string) {\r\n        if (this.samplers.indexOf(name) < 0) {\r\n            this._samplerDeclaration += `uniform sampler2DArray ${name};\\n`;\r\n            this.samplers.push(name);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getGLType(type: NodeMaterialBlockConnectionPointTypes): string {\r\n        switch (type) {\r\n            case NodeMaterialBlockConnectionPointTypes.Float:\r\n                return \"float\";\r\n            case NodeMaterialBlockConnectionPointTypes.Int:\r\n                return \"int\";\r\n            case NodeMaterialBlockConnectionPointTypes.Vector2:\r\n                return \"vec2\";\r\n            case NodeMaterialBlockConnectionPointTypes.Color3:\r\n            case NodeMaterialBlockConnectionPointTypes.Vector3:\r\n                return \"vec3\";\r\n            case NodeMaterialBlockConnectionPointTypes.Color4:\r\n            case NodeMaterialBlockConnectionPointTypes.Vector4:\r\n                return \"vec4\";\r\n            case NodeMaterialBlockConnectionPointTypes.Matrix:\r\n                return \"mat4\";\r\n        }\r\n\r\n        return \"\";\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitExtension(name: string, extension: string, define: string = \"\") {\r\n        if (this.extensions[name]) {\r\n            return;\r\n        }\r\n\r\n        if (define) {\r\n            extension = `#if ${define}\\n${extension}\\n#endif`;\r\n        }\r\n        this.extensions[name] = extension;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitFunction(name: string, code: string, comments: string) {\r\n        if (this.functions[name]) {\r\n            return;\r\n        }\r\n\r\n        if (this.sharedData.emitComments) {\r\n            code = comments + `\\n` + code;\r\n        }\r\n\r\n        this.functions[name] = code;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitCodeFromInclude(\r\n        includeName: string,\r\n        comments: string,\r\n        options?: {\r\n            replaceStrings?: { search: RegExp; replace: string }[];\r\n            repeatKey?: string;\r\n            substitutionVars?: string;\r\n        }\r\n    ) {\r\n        if (options && options.repeatKey) {\r\n            return `#include<${includeName}>${options.substitutionVars ? \"(\" + options.substitutionVars + \")\" : \"\"}[0..${options.repeatKey}]\\n`;\r\n        }\r\n\r\n        let code = Effect.IncludesShadersStore[includeName] + \"\\n\";\r\n\r\n        if (this.sharedData.emitComments) {\r\n            code = comments + `\\n` + code;\r\n        }\r\n\r\n        if (!options) {\r\n            return code;\r\n        }\r\n\r\n        if (options.replaceStrings) {\r\n            for (let index = 0; index < options.replaceStrings.length; index++) {\r\n                const replaceString = options.replaceStrings[index];\r\n                code = code.replace(replaceString.search, replaceString.replace);\r\n            }\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitFunctionFromInclude(\r\n        includeName: string,\r\n        comments: string,\r\n        options?: {\r\n            repeatKey?: string;\r\n            substitutionVars?: string;\r\n            removeAttributes?: boolean;\r\n            removeUniforms?: boolean;\r\n            removeVaryings?: boolean;\r\n            removeIfDef?: boolean;\r\n            replaceStrings?: { search: RegExp; replace: string }[];\r\n        },\r\n        storeKey: string = \"\"\r\n    ) {\r\n        const key = includeName + storeKey;\r\n        if (this.functions[key]) {\r\n            return;\r\n        }\r\n\r\n        if (!options || (!options.removeAttributes && !options.removeUniforms && !options.removeVaryings && !options.removeIfDef && !options.replaceStrings)) {\r\n            if (options && options.repeatKey) {\r\n                this.functions[key] = `#include<${includeName}>${options.substitutionVars ? \"(\" + options.substitutionVars + \")\" : \"\"}[0..${options.repeatKey}]\\n`;\r\n            } else {\r\n                this.functions[key] = `#include<${includeName}>${options?.substitutionVars ? \"(\" + options?.substitutionVars + \")\" : \"\"}\\n`;\r\n            }\r\n\r\n            if (this.sharedData.emitComments) {\r\n                this.functions[key] = comments + `\\n` + this.functions[key];\r\n            }\r\n\r\n            return;\r\n        }\r\n\r\n        this.functions[key] = Effect.IncludesShadersStore[includeName];\r\n\r\n        if (this.sharedData.emitComments) {\r\n            this.functions[key] = comments + `\\n` + this.functions[key];\r\n        }\r\n\r\n        if (options.removeIfDef) {\r\n            this.functions[key] = this.functions[key].replace(/^\\s*?#ifdef.+$/gm, \"\");\r\n            this.functions[key] = this.functions[key].replace(/^\\s*?#endif.*$/gm, \"\");\r\n            this.functions[key] = this.functions[key].replace(/^\\s*?#else.*$/gm, \"\");\r\n            this.functions[key] = this.functions[key].replace(/^\\s*?#elif.*$/gm, \"\");\r\n        }\r\n\r\n        if (options.removeAttributes) {\r\n            this.functions[key] = this.functions[key].replace(/\\s*?attribute .+?;/g, \"\\n\");\r\n        }\r\n\r\n        if (options.removeUniforms) {\r\n            this.functions[key] = this.functions[key].replace(/\\s*?uniform .*?;/g, \"\\n\");\r\n        }\r\n\r\n        if (options.removeVaryings) {\r\n            this.functions[key] = this.functions[key].replace(/\\s*?(varying|in) .+?;/g, \"\\n\");\r\n        }\r\n\r\n        if (options.replaceStrings) {\r\n            for (let index = 0; index < options.replaceStrings.length; index++) {\r\n                const replaceString = options.replaceStrings[index];\r\n                this.functions[key] = this.functions[key].replace(replaceString.search, replaceString.replace);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _registerTempVariable(name: string) {\r\n        if (this.sharedData.temps.indexOf(name) !== -1) {\r\n            return false;\r\n        }\r\n\r\n        this.sharedData.temps.push(name);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitVaryingFromString(name: string, type: string, define: string = \"\", notDefine = false) {\r\n        if (this.sharedData.varyings.indexOf(name) !== -1) {\r\n            return false;\r\n        }\r\n\r\n        this.sharedData.varyings.push(name);\r\n\r\n        if (define) {\r\n            if (define.startsWith(\"defined(\")) {\r\n                this.sharedData.varyingDeclaration += `#if ${define}\\n`;\r\n            } else {\r\n                this.sharedData.varyingDeclaration += `${notDefine ? \"#ifndef\" : \"#ifdef\"} ${define}\\n`;\r\n            }\r\n        }\r\n        this.sharedData.varyingDeclaration += `varying ${type} ${name};\\n`;\r\n        if (define) {\r\n            this.sharedData.varyingDeclaration += `#endif\\n`;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitUniformFromString(name: string, type: string, define: string = \"\", notDefine = false) {\r\n        if (this.uniforms.indexOf(name) !== -1) {\r\n            return;\r\n        }\r\n\r\n        this.uniforms.push(name);\r\n\r\n        if (define) {\r\n            if (define.startsWith(\"defined(\")) {\r\n                this._uniformDeclaration += `#if ${define}\\n`;\r\n            } else {\r\n                this._uniformDeclaration += `${notDefine ? \"#ifndef\" : \"#ifdef\"} ${define}\\n`;\r\n            }\r\n        }\r\n        this._uniformDeclaration += `uniform ${type} ${name};\\n`;\r\n        if (define) {\r\n            this._uniformDeclaration += `#endif\\n`;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _emitFloat(value: number) {\r\n        if (value.toString() === value.toFixed(0)) {\r\n            return `${value}.0`;\r\n        }\r\n\r\n        return value.toString();\r\n    }\r\n}\r\n"]}