import { Observable } from "../Misc/observable";
import type { Nullable } from "../types";
import type { Scene } from "../scene";
import { Vector3 } from "../Maths/math.vector";
import { Color3 } from "../Maths/math.color";
import type { AbstractMesh } from "../Meshes/abstractMesh";
import { Mesh } from "../Meshes/mesh";
import { BaseTexture } from "../Materials/Textures/baseTexture";
import { MirrorTexture } from "../Materials/Textures/mirrorTexture";
import { BackgroundMaterial } from "../Materials/Background/backgroundMaterial";
/**
 * Represents the different options available during the creation of
 * a Environment helper.
 *
 * This can control the default ground, skybox and image processing setup of your scene.
 */
export interface IEnvironmentHelperOptions {
    /**
     * Specifies whether or not to create a ground.
     * True by default.
     */
    createGround: boolean;
    /**
     * Specifies the ground size.
     * 15 by default.
     */
    groundSize: number;
    /**
     * The texture used on the ground for the main color.
     * Comes from the BabylonJS CDN by default.
     *
     * Remarks: Can be either a texture or a url.
     */
    groundTexture: string | BaseTexture;
    /**
     * The color mixed in the ground texture by default.
     * BabylonJS clearColor by default.
     */
    groundColor: Color3;
    /**
     * Specifies the ground opacity.
     * 1 by default.
     */
    groundOpacity: number;
    /**
     * Enables the ground to receive shadows.
     * True by default.
     */
    enableGroundShadow: boolean;
    /**
     * Helps preventing the shadow to be fully black on the ground.
     * 0.5 by default.
     */
    groundShadowLevel: number;
    /**
     * Creates a mirror texture attach to the ground.
     * false by default.
     */
    enableGroundMirror: boolean;
    /**
     * Specifies the ground mirror size ratio.
     * 0.3 by default as the default kernel is 64.
     */
    groundMirrorSizeRatio: number;
    /**
     * Specifies the ground mirror blur kernel size.
     * 64 by default.
     */
    groundMirrorBlurKernel: number;
    /**
     * Specifies the ground mirror visibility amount.
     * 1 by default
     */
    groundMirrorAmount: number;
    /**
     * Specifies the ground mirror reflectance weight.
     * This uses the standard weight of the background material to setup the fresnel effect
     * of the mirror.
     * 1 by default.
     */
    groundMirrorFresnelWeight: number;
    /**
     * Specifies the ground mirror Falloff distance.
     * This can helps reducing the size of the reflection.
     * 0 by Default.
     */
    groundMirrorFallOffDistance: number;
    /**
     * Specifies the ground mirror texture type.
     * Unsigned Int by Default.
     */
    groundMirrorTextureType: number;
    /**
     * Specifies a bias applied to the ground vertical position to prevent z-fighting with
     * the shown objects.
     */
    groundYBias: number;
    /**
     * Specifies whether or not to create a skybox.
     * True by default.
     */
    createSkybox: boolean;
    /**
     * Specifies the skybox size.
     * 20 by default.
     */
    skyboxSize: number;
    /**
     * The texture used on the skybox for the main color.
     * Comes from the BabylonJS CDN by default.
     *
     * Remarks: Can be either a texture or a url.
     */
    skyboxTexture: string | BaseTexture;
    /**
     * The color mixed in the skybox texture by default.
     * BabylonJS clearColor by default.
     */
    skyboxColor: Color3;
    /**
     * The background rotation around the Y axis of the scene.
     * This helps aligning the key lights of your scene with the background.
     * 0 by default.
     */
    backgroundYRotation: number;
    /**
     * Compute automatically the size of the elements to best fit with the scene.
     */
    sizeAuto: boolean;
    /**
     * Default position of the rootMesh if autoSize is not true.
     */
    rootPosition: Vector3;
    /**
     * Sets up the image processing in the scene.
     * true by default.
     */
    setupImageProcessing: boolean;
    /**
     * The texture used as your environment texture in the scene.
     * Comes from the BabylonJS CDN by default and in use if setupImageProcessing is true.
     *
     * Remarks: Can be either a texture or a url.
     */
    environmentTexture: string | BaseTexture;
    /**
     * The value of the exposure to apply to the scene.
     * 0.6 by default if setupImageProcessing is true.
     */
    cameraExposure: number;
    /**
     * The value of the contrast to apply to the scene.
     * 1.6 by default if setupImageProcessing is true.
     */
    cameraContrast: number;
    /**
     * Specifies whether or not tonemapping should be enabled in the scene.
     * true by default if setupImageProcessing is true.
     */
    toneMappingEnabled: boolean;
}
/**
 * The EnvironmentHelper class can be used to add a fully featured non-expensive background to your scene.
 * It includes by default a skybox and a ground relying on the BackgroundMaterial.
 * It also helps with the default setup of your ImageProcessingConfiguration.
 */
export declare class EnvironmentHelper {
    /**
     * Default ground texture URL.
     */
    private static _GroundTextureCDNUrl;
    /**
     * Default skybox texture URL.
     */
    private static _SkyboxTextureCDNUrl;
    /**
     * Default environment texture URL.
     */
    private static _EnvironmentTextureCDNUrl;
    /**
     * Creates the default options for the helper.
     * @param scene The scene the environment helper belongs to.
     * @returns default options for the helper.
     */
    private static _GetDefaultOptions;
    private _rootMesh;
    /**
     * Gets the root mesh created by the helper.
     */
    get rootMesh(): Mesh;
    private _skybox;
    /**
     * Gets the skybox created by the helper.
     */
    get skybox(): Nullable<Mesh>;
    private _skyboxTexture;
    /**
     * Gets the skybox texture created by the helper.
     */
    get skyboxTexture(): Nullable<BaseTexture>;
    private _skyboxMaterial;
    /**
     * Gets the skybox material created by the helper.
     */
    get skyboxMaterial(): Nullable<BackgroundMaterial>;
    private _ground;
    /**
     * Gets the ground mesh created by the helper.
     */
    get ground(): Nullable<Mesh>;
    private _groundTexture;
    /**
     * Gets the ground texture created by the helper.
     */
    get groundTexture(): Nullable<BaseTexture>;
    private _groundMirror;
    /**
     * Gets the ground mirror created by the helper.
     */
    get groundMirror(): Nullable<MirrorTexture>;
    /**
     * Gets the ground mirror render list to helps pushing the meshes
     * you wish in the ground reflection.
     */
    get groundMirrorRenderList(): Nullable<AbstractMesh[]>;
    private _groundMaterial;
    /**
     * Gets the ground material created by the helper.
     */
    get groundMaterial(): Nullable<BackgroundMaterial>;
    /**
     * Stores the creation options.
     */
    private readonly _scene;
    private _options;
    /**
     * This observable will be notified with any error during the creation of the environment,
     * mainly texture creation errors.
     */
    onErrorObservable: Observable<{
        message?: string;
        exception?: any;
    }>;
    /**
     * constructor
     * @param options Defines the options we want to customize the helper
     * @param scene The scene to add the material to
     */
    constructor(options: Partial<IEnvironmentHelperOptions>, scene: Scene);
    /**
     * Updates the environment according to the new options
     * @param options options to configure the helper (IEnvironmentHelperOptions)
     */
    updateOptions(options: Partial<IEnvironmentHelperOptions>): void;
    /**
     * Sets the primary color of all the available elements.
     * @param color the main color to affect to the ground and the background
     */
    setMainColor(color: Color3): void;
    /**
     * Setup the image processing according to the specified options.
     */
    private _setupImageProcessing;
    /**
     * Setup the environment texture according to the specified options.
     */
    private _setupEnvironmentTexture;
    /**
     * Setup the background according to the specified options.
     */
    private _setupBackground;
    /**
     * Get the scene sizes according to the setup.
     * @returns the different ground and skybox sizes.
     */
    private _getSceneSize;
    /**
     * Setup the ground according to the specified options.
     * @param sceneSize
     */
    private _setupGround;
    /**
     * Setup the ground material according to the specified options.
     */
    private _setupGroundMaterial;
    /**
     * Setup the ground diffuse texture according to the specified options.
     */
    private _setupGroundDiffuseTexture;
    /**
     * Setup the ground mirror texture according to the specified options.
     * @param sceneSize
     */
    private _setupGroundMirrorTexture;
    /**
     * Setup the ground to receive the mirror texture.
     */
    private _setupMirrorInGroundMaterial;
    /**
     * Setup the skybox according to the specified options.
     * @param sceneSize
     */
    private _setupSkybox;
    /**
     * Setup the skybox material according to the specified options.
     */
    private _setupSkyboxMaterial;
    /**
     * Setup the skybox reflection texture according to the specified options.
     */
    private _setupSkyboxReflectionTexture;
    private _errorHandler;
    /**
     * Dispose all the elements created by the Helper.
     */
    dispose(): void;
}
