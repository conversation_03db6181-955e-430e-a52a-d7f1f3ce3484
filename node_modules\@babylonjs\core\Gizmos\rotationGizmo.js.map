{"version": 3, "file": "rotationGizmo.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Gizmos/rotationGizmo.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE/C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAI7C,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAkFzE;;GAEG;AACH,MAAM,OAAO,aAAc,SAAQ,KAAK;IA6BpC,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAW,YAAY,CAAC,IAA4B;QAChD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACtD,IAAI,KAAK,CAAC,SAAS,EAAE;gBACjB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;aAC7B;iBAAM;gBACH,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAW,YAAY,CAAC,IAAoB;QACxC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACtD,IAAI,KAAK,CAAC,SAAS,EAAE;gBACjB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;aAC7B;iBAAM;gBACH,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAES,wBAAwB;QAC9B,IAAI,IAAI,CAAC,aAAa,IAAoB,IAAI,CAAC,aAAc,CAAC,aAAa,EAAE;YACzE,MAAM,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;SACjF;IACL,CAAC;IAED;;OAEG;IACH,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACtD,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;IACvH,CAAC;IAED,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED,IAAW,uBAAuB,CAAC,aAAwC;QACvE,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACtD,KAAK,CAAC,uBAAuB,GAAG,aAAa,CAAC;QAClD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;OAQG;IACH,YACI,aAAmC,oBAAoB,CAAC,mBAAmB,EAC3E,YAAY,GAAG,EAAE,EACjB,gBAAgB,GAAG,KAAK,EACxB,YAAoB,CAAC,EACrB,YAA2B,EAC3B,OAA8B;QAE9B,KAAK,CAAC,UAAU,CAAC,CAAC;QA7GtB,6DAA6D;QACtD,0BAAqB,GAAG,IAAI,UAAU,EAAE,CAAC;QAChD,mEAAmE;QAC5D,qBAAgB,GAAG,IAAI,UAAU,EAAE,CAAC;QAC3C,4EAA4E;QACrE,wBAAmB,GAAG,IAAI,UAAU,EAAE,CAAC;QAIpC,iBAAY,GAA4B,EAAE,CAAC;QAC3C,iBAAY,GAAW,CAAC,CAAC;QAEnC,oCAAoC;QAC1B,oBAAe,GAA8B,IAAI,GAAG,EAAE,CAAC;QAiG7D,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxH,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC1H,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzH,IAAI,CAAC,MAAM,GAAG,IAAI,kBAAkB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAChI,IAAI,CAAC,MAAM,GAAG,IAAI,kBAAkB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAChI,IAAI,CAAC,MAAM,GAAG,IAAI,kBAAkB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAEhI,IAAI,CAAC,uBAAuB,GAAG,OAAO,EAAE,uBAAuB,CAAC;QAEhE,yCAAyC;QACzC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACtD,wHAAwH;YACxH,kJAAkJ;YAClJ,IAAI,OAAO,IAAI,OAAO,CAAC,WAAW,IAAI,SAAS,EAAE;gBAC7C,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;aAC3C;YACD,KAAK,CAAC,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC9C,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YACH,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACzC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YACH,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC5C,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,YAAY,EAAE;YACd,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACrD;aAAM;YACH,uDAAuD;YACvD,KAAK,CAAC,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;SACpE;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,sCAAsC,CAAC,KAAc;QAC5D,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,sCAAsC,GAAG,KAAK,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,sCAAsC,GAAG,KAAK,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,sCAAsC,GAAG,KAAK,CAAC;SAC9D;IACL,CAAC;IACD,IAAW,sCAAsC;QAC7C,OAAO,IAAI,CAAC,MAAM,CAAC,sCAAsC,CAAC;IAC9D,CAAC;IAED,IAAW,sCAAsC,CAAC,KAAc;QAC5D,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,sCAAsC,GAAG,KAAK,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,sCAAsC,GAAG,KAAK,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,sCAAsC,GAAG,KAAK,CAAC;SAC9D;IACL,CAAC;IACD,IAAW,sCAAsC;QAC7C,OAAO,IAAI,CAAC,MAAM,CAAC,sCAAsC,CAAC;IAC9D,CAAC;IAED,IAAW,WAAW,CAAC,KAAuB;QAC1C,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACtD,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;QAC9B,CAAC,CAAC,CAAC;IACP,CAAC;IACD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,IAAW,eAAe,CAAC,eAAqC;QAC5D,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACtD,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;QAC5C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,IAAW,WAAW,CAAC,KAAc;QACjC,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;SACnC;IACL,CAAC;IACD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACnC,CAAC;IACD;;OAEG;IACH,IAAW,YAAY,CAAC,KAAa;QACjC,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC;SACpC;IACL,CAAC;IACD,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,UAAU,CAAC,KAAa;QAC/B,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;SAClC;IACL,CAAC;IACD,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IAED,IAAW,wBAAwB,CAAC,wBAA8C;QAC9E,IAAI,CAAC,yBAAyB,GAAG,wBAAwB,CAAC;QAC1D,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACtD,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;aAC7D;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,IAAU,EAAE,KAAqB;QACnD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,WAAW;QACd,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACtB,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YAC9B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,MAAM,CAAC,KAAK,CACR,gKAAgK,CACnK,CAAC;IACN,CAAC;CACJ", "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Quaternion } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { GizmoAnchorPoint, GizmoCoordinatesMode, GizmoAxisCache, IGizmo } from \"./gizmo\";\r\nimport { Gizmo } from \"./gizmo\";\r\nimport type { IPlaneRotationGizmo } from \"./planeRotationGizmo\";\r\nimport { PlaneRotationGizmo } from \"./planeRotationGizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport type { Node } from \"../node\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { GizmoManager } from \"./gizmoManager\";\r\n\r\n/**\r\n * Interface for rotation gizmo\r\n */\r\nexport interface IRotationGizmo extends IGizmo {\r\n    /** True when the mouse pointer is dragging a gizmo mesh */\r\n    readonly isDragging: boolean;\r\n    /** Internal gizmo used for interactions on the x axis */\r\n    xGizmo: IPlaneRotationGizmo;\r\n    /** Internal gizmo used for interactions on the y axis */\r\n    yGizmo: IPlaneRotationGizmo;\r\n    /** Internal gizmo used for interactions on the z axis */\r\n    zGizmo: IPlaneRotationGizmo;\r\n    /** Fires an event when any of it's sub gizmos are dragged */\r\n    onDragStartObservable: Observable<unknown>;\r\n    /** Fires an event when any of it's sub gizmos are being dragged */\r\n    onDragObservable: Observable<unknown>;\r\n    /** Fires an event when any of it's sub gizmos are released from dragging */\r\n    onDragEndObservable: Observable<unknown>;\r\n    /** Drag distance in babylon units that the gizmo will snap to when dragged */\r\n    snapDistance: number;\r\n    /** Custom sensitivity value for the drag strength */\r\n    sensitivity: number;\r\n    /**\r\n     * Builds Gizmo Axis Cache to enable features such as hover state preservation and graying out other axis during manipulation\r\n     * @param mesh Axis gizmo mesh\r\n     * @param cache Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    addToAxisCache(mesh: Mesh, cache: GizmoAxisCache): void;\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    releaseDrag(): void;\r\n}\r\n\r\n/**\r\n * Options for each individual plane rotation gizmo contained within RotationGizmo\r\n * @since 5.0.0\r\n */\r\nexport interface PlaneRotationGizmoOptions {\r\n    /**\r\n     * Color to use for the plane rotation gizmo\r\n     */\r\n    color?: Color3;\r\n}\r\n\r\n/**\r\n * Additional options for each rotation gizmo\r\n */\r\nexport interface RotationGizmoOptions {\r\n    /**\r\n     * When set, the gizmo will always appear the same size no matter where the camera is (default: true)\r\n     */\r\n    updateScale?: boolean;\r\n\r\n    /**\r\n     * Specific options for xGizmo\r\n     */\r\n    xOptions?: PlaneRotationGizmoOptions;\r\n\r\n    /**\r\n     * Specific options for yGizmo\r\n     */\r\n    yOptions?: PlaneRotationGizmoOptions;\r\n\r\n    /**\r\n     * Specific options for zGizmo\r\n     */\r\n    zOptions?: PlaneRotationGizmoOptions;\r\n\r\n    /**\r\n     * Additional transform applied to the gizmo.\r\n     * @See Gizmo.additionalTransformNode for more detail\r\n     */\r\n    additionalTransformNode?: TransformNode;\r\n}\r\n\r\n/**\r\n * Gizmo that enables rotating a mesh along 3 axis\r\n */\r\nexport class RotationGizmo extends Gizmo implements IRotationGizmo {\r\n    /**\r\n     * Internal gizmo used for interactions on the x axis\r\n     */\r\n    public xGizmo: IPlaneRotationGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the y axis\r\n     */\r\n    public yGizmo: IPlaneRotationGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the z axis\r\n     */\r\n    public zGizmo: IPlaneRotationGizmo;\r\n\r\n    /** Fires an event when any of it's sub gizmos are dragged */\r\n    public onDragStartObservable = new Observable();\r\n    /** Fires an event when any of it's sub gizmos are being dragged */\r\n    public onDragObservable = new Observable();\r\n    /** Fires an event when any of it's sub gizmos are released from dragging */\r\n    public onDragEndObservable = new Observable();\r\n\r\n    protected _meshAttached: Nullable<AbstractMesh>;\r\n    protected _nodeAttached: Nullable<Node>;\r\n    protected _observables: Observer<PointerInfo>[] = [];\r\n    protected _sensitivity: number = 1;\r\n\r\n    /** Node Caching for quick lookup */\r\n    protected _gizmoAxisCache: Map<Mesh, GizmoAxisCache> = new Map();\r\n\r\n    public get attachedMesh() {\r\n        return this._meshAttached;\r\n    }\r\n    public set attachedMesh(mesh: Nullable<AbstractMesh>) {\r\n        this._meshAttached = mesh;\r\n        this._nodeAttached = mesh;\r\n        this._checkBillboardTransform();\r\n        [this.xGizmo, this.yGizmo, this.zGizmo].forEach((gizmo) => {\r\n            if (gizmo.isEnabled) {\r\n                gizmo.attachedMesh = mesh;\r\n            } else {\r\n                gizmo.attachedMesh = null;\r\n            }\r\n        });\r\n    }\r\n\r\n    public get attachedNode() {\r\n        return this._nodeAttached;\r\n    }\r\n    public set attachedNode(node: Nullable<Node>) {\r\n        this._meshAttached = null;\r\n        this._nodeAttached = node;\r\n        this._checkBillboardTransform();\r\n        [this.xGizmo, this.yGizmo, this.zGizmo].forEach((gizmo) => {\r\n            if (gizmo.isEnabled) {\r\n                gizmo.attachedNode = node;\r\n            } else {\r\n                gizmo.attachedNode = null;\r\n            }\r\n        });\r\n    }\r\n\r\n    protected _checkBillboardTransform() {\r\n        if (this._nodeAttached && (<TransformNode>this._nodeAttached).billboardMode) {\r\n            Logger.Log(\"Rotation Gizmo will not work with transforms in billboard mode.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sensitivity factor for dragging (Default: 1)\r\n     */\r\n    public set sensitivity(value: number) {\r\n        this._sensitivity = value;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.sensitivity = value;\r\n            }\r\n        });\r\n    }\r\n    public get sensitivity() {\r\n        return this._sensitivity;\r\n    }\r\n\r\n    /**\r\n     * True when the mouse pointer is hovering a gizmo mesh\r\n     */\r\n    public get isHovered() {\r\n        return this.xGizmo.isHovered || this.yGizmo.isHovered || this.zGizmo.isHovered;\r\n    }\r\n\r\n    /**\r\n     * True when the mouse pointer is dragging a gizmo mesh\r\n     */\r\n    public get isDragging() {\r\n        return this.xGizmo.dragBehavior.dragging || this.yGizmo.dragBehavior.dragging || this.zGizmo.dragBehavior.dragging;\r\n    }\r\n\r\n    public get additionalTransformNode() {\r\n        return this._additionalTransformNode;\r\n    }\r\n\r\n    public set additionalTransformNode(transformNode: TransformNode | undefined) {\r\n        [this.xGizmo, this.yGizmo, this.zGizmo].forEach((gizmo) => {\r\n            gizmo.additionalTransformNode = transformNode;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a RotationGizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param tessellation Amount of tessellation to be used when creating rotation circles\r\n     * @param useEulerRotation Use and update Euler angle instead of quaternion\r\n     * @param thickness display gizmo axis thickness\r\n     * @param gizmoManager Gizmo manager\r\n     * @param options More options\r\n     */\r\n    constructor(\r\n        gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer,\r\n        tessellation = 32,\r\n        useEulerRotation = false,\r\n        thickness: number = 1,\r\n        gizmoManager?: GizmoManager,\r\n        options?: RotationGizmoOptions\r\n    ) {\r\n        super(gizmoLayer);\r\n        const xColor = options && options.xOptions && options.xOptions.color ? options.xOptions.color : Color3.Red().scale(0.5);\r\n        const yColor = options && options.yOptions && options.yOptions.color ? options.yOptions.color : Color3.Green().scale(0.5);\r\n        const zColor = options && options.zOptions && options.zOptions.color ? options.zOptions.color : Color3.Blue().scale(0.5);\r\n        this.xGizmo = new PlaneRotationGizmo(new Vector3(1, 0, 0), xColor, gizmoLayer, tessellation, this, useEulerRotation, thickness);\r\n        this.yGizmo = new PlaneRotationGizmo(new Vector3(0, 1, 0), yColor, gizmoLayer, tessellation, this, useEulerRotation, thickness);\r\n        this.zGizmo = new PlaneRotationGizmo(new Vector3(0, 0, 1), zColor, gizmoLayer, tessellation, this, useEulerRotation, thickness);\r\n\r\n        this.additionalTransformNode = options?.additionalTransformNode;\r\n\r\n        // Relay drag events and set update scale\r\n        [this.xGizmo, this.yGizmo, this.zGizmo].forEach((gizmo) => {\r\n            //must set updateScale on each gizmo, as setting it on root RotationGizmo doesnt prevent individual gizmos from updating\r\n            //currently updateScale is a property with no getter/setter, so no good way to override behavior at runtime, so we will at least set it on startup\r\n            if (options && options.updateScale != undefined) {\r\n                gizmo.updateScale = options.updateScale;\r\n            }\r\n            gizmo.dragBehavior.onDragStartObservable.add(() => {\r\n                this.onDragStartObservable.notifyObservers({});\r\n            });\r\n            gizmo.dragBehavior.onDragObservable.add(() => {\r\n                this.onDragObservable.notifyObservers({});\r\n            });\r\n            gizmo.dragBehavior.onDragEndObservable.add(() => {\r\n                this.onDragEndObservable.notifyObservers({});\r\n            });\r\n        });\r\n\r\n        this.attachedMesh = null;\r\n        this.attachedNode = null;\r\n\r\n        if (gizmoManager) {\r\n            gizmoManager.addToAxisCache(this._gizmoAxisCache);\r\n        } else {\r\n            // Only subscribe to pointer event if gizmoManager isnt\r\n            Gizmo.GizmoAxisPointerObserver(gizmoLayer, this._gizmoAxisCache);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If set the gizmo's rotation will be updated to match the attached mesh each frame (Default: true)\r\n     * NOTE: This is only possible for meshes with uniform scaling, as otherwise it's not possible to decompose the rotation\r\n     */\r\n    public set updateGizmoRotationToMatchAttachedMesh(value: boolean) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.updateGizmoRotationToMatchAttachedMesh = value;\r\n            this.yGizmo.updateGizmoRotationToMatchAttachedMesh = value;\r\n            this.zGizmo.updateGizmoRotationToMatchAttachedMesh = value;\r\n        }\r\n    }\r\n    public get updateGizmoRotationToMatchAttachedMesh() {\r\n        return this.xGizmo.updateGizmoRotationToMatchAttachedMesh;\r\n    }\r\n\r\n    public set updateGizmoPositionToMatchAttachedMesh(value: boolean) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.updateGizmoPositionToMatchAttachedMesh = value;\r\n            this.yGizmo.updateGizmoPositionToMatchAttachedMesh = value;\r\n            this.zGizmo.updateGizmoPositionToMatchAttachedMesh = value;\r\n        }\r\n    }\r\n    public get updateGizmoPositionToMatchAttachedMesh() {\r\n        return this.xGizmo.updateGizmoPositionToMatchAttachedMesh;\r\n    }\r\n\r\n    public set anchorPoint(value: GizmoAnchorPoint) {\r\n        this._anchorPoint = value;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo].forEach((gizmo) => {\r\n            gizmo.anchorPoint = value;\r\n        });\r\n    }\r\n    public get anchorPoint() {\r\n        return this._anchorPoint;\r\n    }\r\n\r\n    /**\r\n     * Set the coordinate system to use. By default it's local.\r\n     * But it's possible for a user to tweak so its local for translation and world for rotation.\r\n     * In that case, setting the coordinate system will change `updateGizmoRotationToMatchAttachedMesh` and `updateGizmoPositionToMatchAttachedMesh`\r\n     */\r\n    public set coordinatesMode(coordinatesMode: GizmoCoordinatesMode) {\r\n        [this.xGizmo, this.yGizmo, this.zGizmo].forEach((gizmo) => {\r\n            gizmo.coordinatesMode = coordinatesMode;\r\n        });\r\n    }\r\n\r\n    public set updateScale(value: boolean) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.updateScale = value;\r\n            this.yGizmo.updateScale = value;\r\n            this.zGizmo.updateScale = value;\r\n        }\r\n    }\r\n    public get updateScale() {\r\n        return this.xGizmo.updateScale;\r\n    }\r\n    /**\r\n     * Drag distance in babylon units that the gizmo will snap to when dragged (Default: 0)\r\n     */\r\n    public set snapDistance(value: number) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.snapDistance = value;\r\n            this.yGizmo.snapDistance = value;\r\n            this.zGizmo.snapDistance = value;\r\n        }\r\n    }\r\n    public get snapDistance() {\r\n        return this.xGizmo.snapDistance;\r\n    }\r\n\r\n    /**\r\n     * Ratio for the scale of the gizmo (Default: 1)\r\n     */\r\n    public set scaleRatio(value: number) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.scaleRatio = value;\r\n            this.yGizmo.scaleRatio = value;\r\n            this.zGizmo.scaleRatio = value;\r\n        }\r\n    }\r\n    public get scaleRatio() {\r\n        return this.xGizmo.scaleRatio;\r\n    }\r\n\r\n    /**\r\n     * posture that the gizmo will be display\r\n     * When set null, default value will be used (Quaternion(0, 0, 0, 1))\r\n     */\r\n    public get customRotationQuaternion(): Nullable<Quaternion> {\r\n        return this._customRotationQuaternion;\r\n    }\r\n\r\n    public set customRotationQuaternion(customRotationQuaternion: Nullable<Quaternion>) {\r\n        this._customRotationQuaternion = customRotationQuaternion;\r\n        [this.xGizmo, this.yGizmo, this.zGizmo].forEach((gizmo) => {\r\n            if (gizmo) {\r\n                gizmo.customRotationQuaternion = customRotationQuaternion;\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Builds Gizmo Axis Cache to enable features such as hover state preservation and graying out other axis during manipulation\r\n     * @param mesh Axis gizmo mesh\r\n     * @param cache Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    public addToAxisCache(mesh: Mesh, cache: GizmoAxisCache) {\r\n        this._gizmoAxisCache.set(mesh, cache);\r\n    }\r\n\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    public releaseDrag() {\r\n        this.xGizmo.dragBehavior.releaseDrag();\r\n        this.yGizmo.dragBehavior.releaseDrag();\r\n        this.zGizmo.dragBehavior.releaseDrag();\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public dispose() {\r\n        this.xGizmo.dispose();\r\n        this.yGizmo.dispose();\r\n        this.zGizmo.dispose();\r\n        this.onDragStartObservable.clear();\r\n        this.onDragObservable.clear();\r\n        this.onDragEndObservable.clear();\r\n        this._observables.forEach((obs) => {\r\n            this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(obs);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * CustomMeshes are not supported by this gizmo\r\n     */\r\n    public setCustomMesh() {\r\n        Logger.Error(\r\n            \"Custom meshes are not supported on this gizmo, please set the custom meshes on the gizmos contained within this one (gizmo.xGizmo, gizmo.yGizmo, gizmo.zGizmo)\"\r\n        );\r\n    }\r\n}\r\n"]}