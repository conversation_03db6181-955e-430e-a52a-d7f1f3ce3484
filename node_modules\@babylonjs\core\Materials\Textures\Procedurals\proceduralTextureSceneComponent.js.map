{"version": 3, "file": "proceduralTextureSceneComponent.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Textures/Procedurals/proceduralTextureSceneComponent.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AAG5C,OAAO,EAAE,uBAAuB,EAAE,MAAM,yBAAyB,CAAC;AAalE;;;GAGG;AACH,MAAM,OAAO,+BAA+B;IAWxC;;;OAGG;IACH,YAAY,KAAY;QAdxB;;WAEG;QACa,SAAI,GAAG,uBAAuB,CAAC,sBAAsB,CAAC;QAYlE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,EAAyB,CAAC;IAC9D,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,uBAAuB,CAAC,kCAAkC,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IACnI,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,sBAAsB;IAC1B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,sBAAsB;IAC1B,CAAC;IAEO,YAAY;QAChB,IAAI,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE;YACtC,KAAK,CAAC,uBAAuB,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC/F,KAAK,IAAI,eAAe,GAAG,CAAC,EAAE,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,EAAE,eAAe,EAAE,EAAE;gBACrG,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;gBACzE,IAAI,iBAAiB,CAAC,aAAa,EAAE,EAAE;oBACnC,iBAAiB,CAAC,MAAM,EAAE,CAAC;iBAC9B;aACJ;YACD,KAAK,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SAChG;IACL,CAAC;CACJ", "sourcesContent": ["import { Tools } from \"../../../Misc/tools\";\r\nimport type { Scene } from \"../../../scene\";\r\nimport type { ISceneComponent } from \"../../../sceneComponent\";\r\nimport { SceneComponentConstants } from \"../../../sceneComponent\";\r\n\r\nimport type { ProceduralTexture } from \"./proceduralTexture\";\r\n\r\ndeclare module \"../../../abstractScene\" {\r\n    export interface AbstractScene {\r\n        /**\r\n         * The list of procedural textures added to the scene\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/proceduralTextures\r\n         */\r\n        proceduralTextures: Array<ProceduralTexture>;\r\n    }\r\n}\r\n/**\r\n * Defines the Procedural Texture scene component responsible to manage any Procedural Texture\r\n * in a given scene.\r\n */\r\nexport class ProceduralTextureSceneComponent implements ISceneComponent {\r\n    /**\r\n     * The component name helpful to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_PROCEDURALTEXTURE;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene: Scene) {\r\n        this.scene = scene;\r\n        this.scene.proceduralTextures = [] as ProceduralTexture[];\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {\r\n        this.scene._beforeClearStage.registerStep(SceneComponentConstants.STEP_BEFORECLEAR_PROCEDURALTEXTURE, this, this._beforeClear);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        // Nothing to do here.\r\n    }\r\n\r\n    /**\r\n     * Disposes the component and the associated resources.\r\n     */\r\n    public dispose(): void {\r\n        // Nothing to do here.\r\n    }\r\n\r\n    private _beforeClear(): void {\r\n        if (this.scene.proceduralTexturesEnabled) {\r\n            Tools.StartPerformanceCounter(\"Procedural textures\", this.scene.proceduralTextures.length > 0);\r\n            for (let proceduralIndex = 0; proceduralIndex < this.scene.proceduralTextures.length; proceduralIndex++) {\r\n                const proceduralTexture = this.scene.proceduralTextures[proceduralIndex];\r\n                if (proceduralTexture._shouldRender()) {\r\n                    proceduralTexture.render();\r\n                }\r\n            }\r\n            Tools.EndPerformanceCounter(\"Procedural textures\", this.scene.proceduralTextures.length > 0);\r\n        }\r\n    }\r\n}\r\n"]}