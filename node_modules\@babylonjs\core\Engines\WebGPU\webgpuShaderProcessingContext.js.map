{"version": 3, "file": "webgpuShaderProcessingContext.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuShaderProcessingContext.ts"], "names": [], "mappings": "AAMA,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,oBAAoB,GAAG,CAAC,IAAI,EAAE,CAAC;AAErC,yDAAyD;AACzD,MAAM,mBAAmB,GAA8B;IACnD,aAAa;IACb,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;IAEP,aAAa;IACb,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;CACZ,CAAC;AAmCF;;GAEG;AACH,MAAM,OAAO,6BAA6B;IAqF/B,MAAM,KAAK,SAAS;QACvB,OAAO,6BAA6B,CAAC,wBAAwB,CAAC,CAAC,CAAC,6BAA6B,CAAC,oBAAoB,CAAC,CAAC,CAAC,6BAA6B,CAAC,UAAU,CAAC;IAClK,CAAC;IA6BD,YAAY,cAA8B;QACtC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QAErC,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE1B,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAE5B,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,wBAAwB,GAAG,EAAE,CAAC;QACnC,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QAEvB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAE3B,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAEO,yBAAyB;QAC7B,MAAM,SAAS,GAAG,6BAA6B,CAAC,SAAS,CAAC;QAE1D,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;YAC1B,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;YACxC,IAAI,OAAO,CAAC,UAAU,KAAK,CAAC,CAAC,EAAE;gBAC3B,SAAS;aACZ;YACD,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;gBAC1C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,YAAY,CAAC;aACrD;iBAAM;gBACH,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;aAC3F;SACJ;QAED,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QACxC,IAAI,IAAI,CAAC,cAAc,KAAK,CAAC,EAAE;YAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;SAC7B;aAAM;YACH,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;SACzD;IACL,CAAC;IAEM,wBAAwB,CAAC,QAAgB,EAAE,cAAsB,CAAC;QACrE,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAE1C,IAAI,CAAC,sBAAsB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;QAEzF,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,sBAAsB,CAAC,QAAgB,EAAE,cAAsB,CAAC;QACnE,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAExC,IAAI,CAAC,oBAAoB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;QAEvF,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,qBAAqB;QACxB,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;IAEO,mBAAmB,CAAC,YAAoB;QAC5C,IAAI,IAAI,CAAC,gBAAgB,GAAG,oBAAoB,GAAG,YAAY,EAAE;YAC7D,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;SAC7B;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,UAAU,EAAE;YACpC,4CAA4C;YAC5C,MAAM,iFAAiF,CAAC;SAC3F;QAED,MAAM,WAAW,GAAG;YAChB,UAAU,EAAE,IAAI,CAAC,cAAc;YAC/B,YAAY,EAAE,IAAI,CAAC,gBAAgB;SACtC,CAAC;QAEF,IAAI,CAAC,gBAAgB,IAAI,YAAY,CAAC;QAEtC,OAAO,WAAW,CAAC;IACvB,CAAC;;AA9MD,gBAAgB;AACF,sDAAwB,GAAG,IAAI,CAAC,CAAC,kIAAkI;AACjL,8DAA8D;AAE7C,kDAAoB,GAA+C;IAChF,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IACtD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IACzD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IACzD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IACzD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IACzD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IACzD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IACzD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IACzD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IACzD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IACzD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC1D,QAAQ,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IAC3D,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;IACvD,SAAS,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE;CAC/D,CAAC;AAEe,wCAAU,GAA+C;IACtE,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IAEtD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IACvD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IACvD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IACvD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IACvD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IACvD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IACvD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IACvD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IACvD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IACvD,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IACvD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IACzD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE;IAEzD,QAAQ,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IACzD,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;IACrD,SAAS,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE;CAC7D,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\n/* eslint-disable babylonjs/available */\r\n/* eslint-disable jsdoc/require-jsdoc */\r\nimport type { ShaderLanguage } from \"../../Materials/shaderLanguage\";\r\nimport type { ShaderProcessingContext } from \"../Processors/shaderProcessingOptions\";\r\n\r\nconst _maxGroups = 4;\r\nconst _maxBindingsPerGroup = 1 << 16;\r\n\r\n// all types not listed are assumed to consume 1 location\r\nconst _typeToLocationSize: { [key: string]: number } = {\r\n    // GLSL types\r\n    mat2: 2,\r\n    mat3: 3,\r\n    mat4: 4,\r\n\r\n    // WGSL types\r\n    mat2x2: 2,\r\n    mat3x3: 3,\r\n    mat4x4: 4,\r\n};\r\n\r\n/** @internal */\r\nexport interface WebGPUBindingInfo {\r\n    groupIndex: number;\r\n    bindingIndex: number;\r\n}\r\n\r\n/** @internal */\r\nexport interface WebGPUTextureDescription {\r\n    autoBindSampler?: boolean;\r\n    isTextureArray: boolean;\r\n    isStorageTexture: boolean;\r\n    textures: Array<WebGPUBindingInfo>;\r\n    sampleType?: GPUTextureSampleType; // not used if the texture is a storage texture\r\n}\r\n\r\n/** @internal */\r\nexport interface WebGPUSamplerDescription {\r\n    binding: WebGPUBindingInfo;\r\n    type: GPUSamplerBindingType;\r\n}\r\n\r\n/** @internal */\r\nexport interface WebGPUBufferDescription {\r\n    binding: WebGPUBindingInfo;\r\n}\r\n\r\n/** @internal */\r\nexport interface WebGPUBindGroupLayoutEntryInfo {\r\n    name: string;\r\n    index: number; // index of the entry (GPUBindGroupLayoutEntry) in the bindGroupLayoutEntries[group] array\r\n    nameInArrayOfTexture?: string; // something like texture0, texture1, ... if texture is an array, else same thing as \"name\"\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class WebGPUShaderProcessingContext implements ShaderProcessingContext {\r\n    /** @internal */\r\n    public static _SimplifiedKnownBindings = true; // if true, use only group=0,binding=0 as a known group/binding for the Scene ubo and use group=1,binding=X for all other bindings\r\n    // if false, see _KnownUBOs for the known groups/bindings used\r\n\r\n    protected static _SimplifiedKnownUBOs: { [key: string]: WebGPUBufferDescription } = {\r\n        Scene: { binding: { groupIndex: 0, bindingIndex: 0 } },\r\n        Light0: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light1: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light2: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light3: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light4: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light5: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light6: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light7: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light8: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light9: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light10: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light11: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light12: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light13: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light14: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light15: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light16: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light17: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light18: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light19: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light20: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light21: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light22: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light23: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light24: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light25: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light26: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light27: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light28: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light29: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light30: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Light31: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Material: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Mesh: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n        Internals: { binding: { groupIndex: -1, bindingIndex: -1 } },\r\n    };\r\n\r\n    protected static _KnownUBOs: { [key: string]: WebGPUBufferDescription } = {\r\n        Scene: { binding: { groupIndex: 0, bindingIndex: 0 } },\r\n\r\n        Light0: { binding: { groupIndex: 1, bindingIndex: 0 } },\r\n        Light1: { binding: { groupIndex: 1, bindingIndex: 1 } },\r\n        Light2: { binding: { groupIndex: 1, bindingIndex: 2 } },\r\n        Light3: { binding: { groupIndex: 1, bindingIndex: 3 } },\r\n        Light4: { binding: { groupIndex: 1, bindingIndex: 4 } },\r\n        Light5: { binding: { groupIndex: 1, bindingIndex: 5 } },\r\n        Light6: { binding: { groupIndex: 1, bindingIndex: 6 } },\r\n        Light7: { binding: { groupIndex: 1, bindingIndex: 7 } },\r\n        Light8: { binding: { groupIndex: 1, bindingIndex: 8 } },\r\n        Light9: { binding: { groupIndex: 1, bindingIndex: 9 } },\r\n        Light10: { binding: { groupIndex: 1, bindingIndex: 10 } },\r\n        Light11: { binding: { groupIndex: 1, bindingIndex: 11 } },\r\n        Light12: { binding: { groupIndex: 1, bindingIndex: 12 } },\r\n        Light13: { binding: { groupIndex: 1, bindingIndex: 13 } },\r\n        Light14: { binding: { groupIndex: 1, bindingIndex: 14 } },\r\n        Light15: { binding: { groupIndex: 1, bindingIndex: 15 } },\r\n        Light16: { binding: { groupIndex: 1, bindingIndex: 16 } },\r\n        Light17: { binding: { groupIndex: 1, bindingIndex: 17 } },\r\n        Light18: { binding: { groupIndex: 1, bindingIndex: 18 } },\r\n        Light19: { binding: { groupIndex: 1, bindingIndex: 19 } },\r\n        Light20: { binding: { groupIndex: 1, bindingIndex: 20 } },\r\n        Light21: { binding: { groupIndex: 1, bindingIndex: 21 } },\r\n        Light22: { binding: { groupIndex: 1, bindingIndex: 22 } },\r\n        Light23: { binding: { groupIndex: 1, bindingIndex: 23 } },\r\n        Light24: { binding: { groupIndex: 1, bindingIndex: 24 } },\r\n        Light25: { binding: { groupIndex: 1, bindingIndex: 25 } },\r\n        Light26: { binding: { groupIndex: 1, bindingIndex: 26 } },\r\n        Light27: { binding: { groupIndex: 1, bindingIndex: 27 } },\r\n        Light28: { binding: { groupIndex: 1, bindingIndex: 28 } },\r\n        Light29: { binding: { groupIndex: 1, bindingIndex: 29 } },\r\n        Light30: { binding: { groupIndex: 1, bindingIndex: 30 } },\r\n        Light31: { binding: { groupIndex: 1, bindingIndex: 31 } },\r\n\r\n        Material: { binding: { groupIndex: 2, bindingIndex: 0 } },\r\n        Mesh: { binding: { groupIndex: 2, bindingIndex: 1 } },\r\n        Internals: { binding: { groupIndex: 2, bindingIndex: 2 } },\r\n    };\r\n\r\n    public static get KnownUBOs() {\r\n        return WebGPUShaderProcessingContext._SimplifiedKnownBindings ? WebGPUShaderProcessingContext._SimplifiedKnownUBOs : WebGPUShaderProcessingContext._KnownUBOs;\r\n    }\r\n\r\n    public shaderLanguage: ShaderLanguage;\r\n\r\n    public uboNextBindingIndex: number;\r\n    public freeGroupIndex: number;\r\n    public freeBindingIndex: number;\r\n\r\n    public availableVaryings: { [key: string]: number };\r\n    public availableAttributes: { [key: string]: number };\r\n    public availableBuffers: { [key: string]: WebGPUBufferDescription };\r\n    public availableTextures: { [key: string]: WebGPUTextureDescription };\r\n    public availableSamplers: { [key: string]: WebGPUSamplerDescription };\r\n\r\n    public leftOverUniforms: { name: string; type: string; length: number }[];\r\n\r\n    public orderedAttributes: string[];\r\n    public bindGroupLayoutEntries: GPUBindGroupLayoutEntry[][];\r\n    public bindGroupLayoutEntryInfo: WebGPUBindGroupLayoutEntryInfo[][];\r\n    public bindGroupEntries: GPUBindGroupEntry[][];\r\n    public bufferNames: string[]; // list of all uniform/storage buffer names used in the shader\r\n    public textureNames: string[]; // list of all texture names used in the shader\r\n    public samplerNames: string[]; // list of all sampler names used in the shader\r\n    public attributeNamesFromEffect: string[];\r\n    public attributeLocationsFromEffect: number[];\r\n\r\n    private _attributeNextLocation: number;\r\n    private _varyingNextLocation: number;\r\n\r\n    constructor(shaderLanguage: ShaderLanguage) {\r\n        this.shaderLanguage = shaderLanguage;\r\n\r\n        this._attributeNextLocation = 0;\r\n        this._varyingNextLocation = 0;\r\n        this.freeGroupIndex = 0;\r\n        this.freeBindingIndex = 0;\r\n\r\n        this.availableVaryings = {};\r\n        this.availableAttributes = {};\r\n        this.availableBuffers = {};\r\n        this.availableTextures = {};\r\n        this.availableSamplers = {};\r\n\r\n        this.orderedAttributes = [];\r\n        this.bindGroupLayoutEntries = [];\r\n        this.bindGroupLayoutEntryInfo = [];\r\n        this.bindGroupEntries = [];\r\n        this.bufferNames = [];\r\n        this.textureNames = [];\r\n        this.samplerNames = [];\r\n\r\n        this.leftOverUniforms = [];\r\n\r\n        this._findStartingGroupBinding();\r\n    }\r\n\r\n    private _findStartingGroupBinding(): void {\r\n        const knownUBOs = WebGPUShaderProcessingContext.KnownUBOs;\r\n\r\n        const groups: number[] = [];\r\n        for (const name in knownUBOs) {\r\n            const binding = knownUBOs[name].binding;\r\n            if (binding.groupIndex === -1) {\r\n                continue;\r\n            }\r\n            if (groups[binding.groupIndex] === undefined) {\r\n                groups[binding.groupIndex] = binding.bindingIndex;\r\n            } else {\r\n                groups[binding.groupIndex] = Math.max(groups[binding.groupIndex], binding.bindingIndex);\r\n            }\r\n        }\r\n\r\n        this.freeGroupIndex = groups.length - 1;\r\n        if (this.freeGroupIndex === 0) {\r\n            this.freeGroupIndex++;\r\n            this.freeBindingIndex = 0;\r\n        } else {\r\n            this.freeBindingIndex = groups[groups.length - 1] + 1;\r\n        }\r\n    }\r\n\r\n    public getAttributeNextLocation(dataType: string, arrayLength: number = 0): number {\r\n        const index = this._attributeNextLocation;\r\n\r\n        this._attributeNextLocation += (_typeToLocationSize[dataType] ?? 1) * (arrayLength || 1);\r\n\r\n        return index;\r\n    }\r\n\r\n    public getVaryingNextLocation(dataType: string, arrayLength: number = 0): number {\r\n        const index = this._varyingNextLocation;\r\n\r\n        this._varyingNextLocation += (_typeToLocationSize[dataType] ?? 1) * (arrayLength || 1);\r\n\r\n        return index;\r\n    }\r\n\r\n    public getNextFreeUBOBinding() {\r\n        return this._getNextFreeBinding(1);\r\n    }\r\n\r\n    private _getNextFreeBinding(bindingCount: number) {\r\n        if (this.freeBindingIndex > _maxBindingsPerGroup - bindingCount) {\r\n            this.freeGroupIndex++;\r\n            this.freeBindingIndex = 0;\r\n        }\r\n\r\n        if (this.freeGroupIndex === _maxGroups) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"Too many textures or UBOs have been declared and it is not supported in WebGPU.\";\r\n        }\r\n\r\n        const returnValue = {\r\n            groupIndex: this.freeGroupIndex,\r\n            bindingIndex: this.freeBindingIndex,\r\n        };\r\n\r\n        this.freeBindingIndex += bindingCount;\r\n\r\n        return returnValue;\r\n    }\r\n}\r\n"]}