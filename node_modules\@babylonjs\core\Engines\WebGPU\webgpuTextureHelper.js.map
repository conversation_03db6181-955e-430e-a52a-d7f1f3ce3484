{"version": 3, "file": "webgpuTextureHelper.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuTextureHelper.ts"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,gEAAgE;AAChE,OAAO,KAAK,eAAe,MAAM,mBAAmB,CAAC;AACrD,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAIzC,gBAAgB;AAChB,MAAM,OAAO,mBAAmB;IACrB,MAAM,CAAC,sBAAsB,CAAC,KAAa,EAAE,MAAc;QAC9D,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACrD,CAAC;IAEM,MAAM,CAAC,wBAAwB,CAAC,MAAwB;QAC3D,QAAQ,MAAM,EAAE;YACZ,yBAAyB;YACzB,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,MAAM,CAAC;YAC1C,KAAK,eAAe,CAAC,aAAa,CAAC,MAAM,CAAC;YAC1C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,4CAA4C;YAC5F,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,4CAA4C;YAC7F,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,4CAA4C;YAC7F,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,4CAA4C;YAC9F,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,eAAe,CAAC;YACnD,KAAK,eAAe,CAAC,aAAa,CAAC,mBAAmB,CAAC;YACvD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB,CAAC;YACtD,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB,CAAC;YACtD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB,CAAC;YACtD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB,CAAC;YACtD,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ;gBACvC,OAAO,SAAS,CAAC,yBAAyB,CAAC;YAE/C,0BAA0B;YAC1B,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY;gBAC3C,OAAO,SAAS,CAAC,0BAA0B,CAAC;YAEhD,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW;gBAC1C,OAAO,SAAS,CAAC,sBAAsB,CAAC;YAE5C,0BAA0B;YAC1B,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU;gBACzC,OAAO,SAAS,CAAC,4BAA4B,CAAC;YAElD,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACxD,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,mBAAmB;gBAClD,OAAO,SAAS,CAAC,iBAAiB,CAAC;SAC1C;QAED,OAAO,SAAS,CAAC,yBAAyB,CAAC;IAC/C,CAAC;IAEM,MAAM,CAAC,6BAA6B,CAAC,MAAwB;QAChE,QAAQ,MAAM,EAAE;YACZ,iBAAiB;YACjB,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,MAAM,CAAC;YAC1C,KAAK,eAAe,CAAC,aAAa,CAAC,MAAM;gBACrC,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,kBAAkB;YAClB,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO;gBACtC,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,kBAAkB;YAClB,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa;gBAC5C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,kBAAkB;YAClB,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW;gBAC1C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,mBAAmB;YACnB,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW;gBAC1C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAE/C,4BAA4B;YAC5B,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ;gBACvC,4CAA4C;gBAC5C,MAAM,oCAAoC,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY;gBAC3C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW;gBAC1C,4CAA4C;gBAC5C,MAAM,uCAAuC,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,mBAAmB;gBAClD,4CAA4C;gBAC5C,MAAM,+CAA+C,CAAC;YAC1D,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY;gBAC3C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,oBAAoB;gBACnD,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,mEAAmE;YACnE,mEAAmE;YACnE,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB;gBAC/C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAE/C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB;gBAC/C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,uEAAuE;YACvE,mEAAmE;YACnE,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,eAAe,CAAC;YACnD,KAAK,eAAe,CAAC,aAAa,CAAC,mBAAmB,CAAC;YACvD,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW;gBAC1C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB,CAAC;YACtD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY;gBAC3C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAE/C,uEAAuE;YACvE,mEAAmE;YACnE,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB;gBAC/C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB;gBAC/C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB;gBAC/C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB;gBAC/C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB;gBAC/C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB;gBAC/C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB;gBAC/C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB;gBAC/C,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB;gBAChD,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB;gBAChD,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB;gBAChD,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB;gBACjD,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB;gBACjD,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB;gBACjD,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;SACpD;QAED,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;IAC9C,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,OAA4C;QACxE,OAAO,CAAC,CAAE,OAAkC,CAAC,OAAO,CAAC;IACzD,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,OAAqC;QACjE,OAAO,CAAC,CAAE,OAA2B,CAAC,OAAO,CAAC;IAClD,CAAC;IAEM,MAAM,CAAC,aAAa,CAAC,WAA4D;QACpF,OAAQ,WAA2B,CAAC,KAAK,KAAK,SAAS,CAAC;IAC5D,CAAC;IAEM,MAAM,CAAC,kBAAkB,CAAC,WAA8D;QAC3F,OAAO,KAAK,CAAC,OAAO,CAAC,WAA4B,CAAC,IAAK,WAA6B,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,SAAS,CAAC;IAChH,CAAC;IAEM,MAAM,CAAC,kBAAkB,CAAC,MAAwB;QACrD,QAAQ,MAAM,EAAE;YACZ,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,eAAe,CAAC;YACnD,KAAK,eAAe,CAAC,aAAa,CAAC,mBAAmB,CAAC;YACvD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB,CAAC;YACtD,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB,CAAC;YACtD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB,CAAC;YACtD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB;gBACjD,OAAO,IAAI,CAAC;SACnB;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,MAAM,CAAC,sBAAsB,CAAC,IAAY,EAAE,MAAc,EAAE,aAAa,GAAG,KAAK;QACpF,QAAQ,MAAM,EAAE;YACZ,KAAK,SAAS,CAAC,qBAAqB;gBAChC,OAAO,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACtD,KAAK,SAAS,CAAC,qBAAqB;gBAChC,OAAO,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YACrD,KAAK,SAAS,CAAC,8BAA8B;gBACzC,OAAO,eAAe,CAAC,aAAa,CAAC,mBAAmB,CAAC;YAC7D,KAAK,SAAS,CAAC,2BAA2B;gBACtC,OAAO,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACtD,KAAK,SAAS,CAAC,mCAAmC;gBAC9C,OAAO,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAC9D,KAAK,SAAS,CAAC,sBAAsB;gBACjC,OAAO,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAElD,KAAK,SAAS,CAAC,wCAAwC;gBACnD,OAAO,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACvH,KAAK,SAAS,CAAC,gDAAgD;gBAC3D,OAAO,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACvD,KAAK,SAAS,CAAC,8CAA8C;gBACzD,OAAO,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACtD,KAAK,SAAS,CAAC,uCAAuC;gBAClD,OAAO,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACvH,KAAK,SAAS,CAAC,uCAAuC;gBAClD,OAAO,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACvH,KAAK,SAAS,CAAC,uCAAuC,CAAC;YACvD,KAAK,SAAS,CAAC,sCAAsC;gBACjD,OAAO,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACvH,KAAK,SAAS,CAAC,sCAAsC;gBACjD,OAAO,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACvH,KAAK,SAAS,CAAC,uCAAuC,CAAC;YACvD,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,OAAO,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACzH,KAAK,SAAS,CAAC,uCAAuC;gBAClD,OAAO,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;SAC9H;QAED,QAAQ,IAAI,EAAE;YACV,KAAK,SAAS,CAAC,gBAAgB;gBAC3B,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;oBACjD,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,OAAO,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;oBAClD,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,4CAA4C;wBAC5C,MAAM,oCAAoC,CAAC;oBAC/C,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,eAAe,CAAC,aAAa,CAAC,MAAM,CAAC;oBAChD,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;oBACjD,KAAK,SAAS,CAAC,yBAAyB;wBACpC,4CAA4C;wBAC5C,MAAM,4CAA4C,CAAC;oBACvD,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;oBACnD;wBACI,OAAO,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;iBACvD;YACL,KAAK,SAAS,CAAC,yBAAyB;gBACpC,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;oBACjD,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,OAAO,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;oBAClD,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,4CAA4C;wBAC5C,MAAM,kDAAkD,CAAC;oBAC7D,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;oBACnH,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;oBACnH,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,eAAe,CAAC,aAAa,CAAC,MAAM,CAAC;oBAChD,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;oBACjD,KAAK,SAAS,CAAC,yBAAyB;wBACpC,4CAA4C;wBAC5C,MAAM,4CAA4C,CAAC;oBACvD,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;oBACnD,KAAK,SAAS,CAAC,mBAAmB;wBAC9B,4CAA4C;wBAC5C,MAAM,oDAAoD,CAAC;oBAC/D,KAAK,SAAS,CAAC,uBAAuB;wBAClC,4CAA4C;wBAC5C,MAAM,wDAAwD,CAAC;oBACnE,KAAK,SAAS,CAAC,6BAA6B;wBACxC,4CAA4C;wBAC5C,MAAM,8DAA8D,CAAC;oBACzE;wBACI,OAAO,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;iBACvD;YACL,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;oBACjD,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;oBAClD,KAAK,SAAS,CAAC,yBAAyB;wBACpC,4CAA4C;wBAC5C,MAAM,0DAA0D,CAAC;oBACrE,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;oBACpD;wBACI,OAAO,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;iBACvD;YACL,KAAK,SAAS,CAAC,0BAA0B;gBACrC,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;oBACjD,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;oBAClD,KAAK,SAAS,CAAC,yBAAyB;wBACpC,4CAA4C;wBAC5C,MAAM,0DAA0D,CAAC;oBACrE,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;oBACpD;wBACI,OAAO,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;iBACvD;YACL,KAAK,SAAS,CAAC,eAAe;gBAC1B,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;oBACjD,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;oBAClD,KAAK,SAAS,CAAC,yBAAyB;wBACpC,4CAA4C;wBAC5C,MAAM,0DAA0D,CAAC;oBACrE,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;oBACpD;wBACI,OAAO,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;iBACvD;YACL,KAAK,SAAS,CAAC,4BAA4B,EAAE,yBAAyB;gBAClE,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;oBACjD,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;oBAClD,KAAK,SAAS,CAAC,yBAAyB;wBACpC,4CAA4C;wBAC5C,MAAM,0DAA0D,CAAC;oBACrE,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;oBACpD;wBACI,OAAO,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;iBACvD;YACL,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,6CAA6C;oBAChG,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,OAAO,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,8CAA8C;oBAClG,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,4CAA4C;wBAC5C,MAAM,kDAAkD,CAAC;oBAC7D,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,gDAAgD;oBACtG;wBACI,OAAO,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;iBACxD;YACL,KAAK,SAAS,CAAC,sBAAsB;gBACjC,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;oBAClD,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,OAAO,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;oBACnD,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,4CAA4C;wBAC5C,MAAM,kDAAkD,CAAC;oBAC7D,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;oBACrD;wBACI,OAAO,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;iBACxD;YACL,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,4CAA4C;gBAC5C,MAAM,iEAAiE,CAAC;YAC5E,KAAK,SAAS,CAAC,wCAAwC;gBACnD,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;oBACvD,KAAK,SAAS,CAAC,0BAA0B;wBACrC,4CAA4C;wBAC5C,MAAM,iHAAiH,CAAC;oBAC5H;wBACI,OAAO,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;iBAC1D;YACL,KAAK,SAAS,CAAC,oCAAoC;gBAC/C,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;oBACtD,KAAK,SAAS,CAAC,0BAA0B;wBACrC,4CAA4C;wBAC5C,MAAM,6GAA6G,CAAC;oBACxH;wBACI,OAAO,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;iBACzD;YACL,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,4CAA4C;gBAC5C,MAAM,mEAAmE,CAAC;YAC9E,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,4CAA4C;gBAC5C,MAAM,mEAAmE,CAAC;YAC9E,KAAK,SAAS,CAAC,uCAAuC;gBAClD,QAAQ,MAAM,EAAE;oBACZ,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;oBACtD,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;oBACrD;wBACI,OAAO,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;iBACzD;SACR;QAED,OAAO,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;IACnH,CAAC;IAEM,MAAM,CAAC,qCAAqC,CAAC,MAAwB;QACxE,QAAQ,MAAM,EAAE;YACZ,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,MAAM,CAAC;YAC1C,KAAK,eAAe,CAAC,aAAa,CAAC,MAAM,CAAC;YAC1C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW;gBAC1C,OAAO,CAAC,CAAC;YAEb,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,KAAK,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACxD,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,mBAAmB,CAAC;YACvD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY;gBAC3C,OAAO,CAAC,CAAC;YAEb,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB;gBAChD,OAAO,CAAC,CAAC;YAEb,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC;YAC7C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC;YAC9C,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YAC/C,KAAK,eAAe,CAAC,aAAa,CAAC,eAAe,CAAC;YACnD,KAAK,eAAe,CAAC,aAAa,CAAC,mBAAmB,CAAC;YACvD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB,CAAC;YACtD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChD,KAAK,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC;YACpD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,KAAK,eAAe,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB,CAAC;YACtD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB,CAAC;YACtD,KAAK,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC;YAClD,KAAK,eAAe,CAAC,aAAa,CAAC,kBAAkB;gBACjD,OAAO,CAAC,CAAC;SAChB;QAED,4CAA4C;QAC5C,MAAM,kBAAkB,MAAM,GAAG,CAAC;IACtC,CAAC;IAEM,MAAM,CAAC,gBAAgB,CAAC,MAAwB;QACnD,QAAQ,MAAM,EAAE;YACZ,KAAK,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC5C,KAAK,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACxD,KAAK,eAAe,CAAC,aAAa,CAAC,mBAAmB;gBAClD,OAAO,IAAI,CAAC;SACnB;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,MAAM,CAAC,yBAAyB,CAAC,MAAwB;QAC5D,QAAQ,MAAM,EAAE;YACZ,KAAK,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACxD,KAAK,eAAe,CAAC,aAAa,CAAC,mBAAmB;gBAClD,OAAO,IAAI,CAAC;SACnB;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,MAAM,CAAC,kBAAkB,CAAC,MAAwB;QACrD,QAAQ,MAAM,EAAE;YACZ,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY;gBAC3C,OAAO,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACtD,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW;gBAC1C,OAAO,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,mBAAmB;gBAClD,OAAO,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YACrD,KAAK,eAAe,CAAC,aAAa,CAAC,YAAY;gBAC3C,OAAO,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACtD,KAAK,eAAe,CAAC,aAAa,CAAC,oBAAoB;gBACnD,OAAO,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;SACzD;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEM,MAAM,CAAC,SAAS,CAAC,WAAmB;QACvC,8BAA8B;QAC9B,OAAO,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;CACJ", "sourcesContent": ["/* eslint-disable baby<PERSON>js/available */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport { Scalar } from \"../../Maths/math.scalar\";\r\nimport { Constants } from \"../constants\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport type { HardwareTextureWrapper } from \"../../Materials/Textures/hardwareTextureWrapper\";\r\n\r\n/** @internal */\r\nexport class WebGPUTextureHelper {\r\n    public static ComputeNumMipmapLevels(width: number, height: number) {\r\n        return Scalar.ILog2(Math.max(width, height)) + 1;\r\n    }\r\n\r\n    public static GetTextureTypeFromFormat(format: GPUTextureFormat): number {\r\n        switch (format) {\r\n            // One Component = 8 bits\r\n            case WebGPUConstants.TextureFormat.R8Unorm:\r\n            case WebGPUConstants.TextureFormat.R8Snorm:\r\n            case WebGPUConstants.TextureFormat.R8Uint:\r\n            case WebGPUConstants.TextureFormat.R8Sint:\r\n            case WebGPUConstants.TextureFormat.RG8Unorm:\r\n            case WebGPUConstants.TextureFormat.RG8Snorm:\r\n            case WebGPUConstants.TextureFormat.RG8Uint:\r\n            case WebGPUConstants.TextureFormat.RG8Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGBA8Snorm:\r\n            case WebGPUConstants.TextureFormat.RGBA8Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA8Sint:\r\n            case WebGPUConstants.TextureFormat.BGRA8Unorm:\r\n            case WebGPUConstants.TextureFormat.BGRA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGB10A2UINT: // composite format - let's say it's byte...\r\n            case WebGPUConstants.TextureFormat.RGB10A2Unorm: // composite format - let's say it's byte...\r\n            case WebGPUConstants.TextureFormat.RGB9E5UFloat: // composite format - let's say it's byte...\r\n            case WebGPUConstants.TextureFormat.RG11B10UFloat: // composite format - let's say it's byte...\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBUFloat:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBFloat:\r\n            case WebGPUConstants.TextureFormat.BC5RGUnorm:\r\n            case WebGPUConstants.TextureFormat.BC5RGSnorm:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC4RUnorm:\r\n            case WebGPUConstants.TextureFormat.BC4RSnorm:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.EACR11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACR11Snorm:\r\n            case WebGPUConstants.TextureFormat.EACRG11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACRG11Snorm:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.Stencil8:\r\n                return Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n\r\n            // One component = 16 bits\r\n            case WebGPUConstants.TextureFormat.R16Uint:\r\n            case WebGPUConstants.TextureFormat.R16Sint:\r\n            case WebGPUConstants.TextureFormat.RG16Uint:\r\n            case WebGPUConstants.TextureFormat.RG16Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA16Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA16Sint:\r\n            case WebGPUConstants.TextureFormat.Depth16Unorm:\r\n                return Constants.TEXTURETYPE_UNSIGNED_SHORT;\r\n\r\n            case WebGPUConstants.TextureFormat.R16Float:\r\n            case WebGPUConstants.TextureFormat.RG16Float:\r\n            case WebGPUConstants.TextureFormat.RGBA16Float:\r\n                return Constants.TEXTURETYPE_HALF_FLOAT;\r\n\r\n            // One component = 32 bits\r\n            case WebGPUConstants.TextureFormat.R32Uint:\r\n            case WebGPUConstants.TextureFormat.R32Sint:\r\n            case WebGPUConstants.TextureFormat.RG32Uint:\r\n            case WebGPUConstants.TextureFormat.RG32Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA32Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA32Sint:\r\n                return Constants.TEXTURETYPE_UNSIGNED_INTEGER;\r\n\r\n            case WebGPUConstants.TextureFormat.R32Float:\r\n            case WebGPUConstants.TextureFormat.RG32Float:\r\n            case WebGPUConstants.TextureFormat.RGBA32Float:\r\n            case WebGPUConstants.TextureFormat.Depth32Float:\r\n            case WebGPUConstants.TextureFormat.Depth32FloatStencil8:\r\n            case WebGPUConstants.TextureFormat.Depth24Plus:\r\n            case WebGPUConstants.TextureFormat.Depth24PlusStencil8:\r\n                return Constants.TEXTURETYPE_FLOAT;\r\n        }\r\n\r\n        return Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n    }\r\n\r\n    public static GetBlockInformationFromFormat(format: GPUTextureFormat): { width: number; height: number; length: number } {\r\n        switch (format) {\r\n            // 8 bits formats\r\n            case WebGPUConstants.TextureFormat.R8Unorm:\r\n            case WebGPUConstants.TextureFormat.R8Snorm:\r\n            case WebGPUConstants.TextureFormat.R8Uint:\r\n            case WebGPUConstants.TextureFormat.R8Sint:\r\n                return { width: 1, height: 1, length: 1 };\r\n\r\n            // 16 bits formats\r\n            case WebGPUConstants.TextureFormat.R16Uint:\r\n            case WebGPUConstants.TextureFormat.R16Sint:\r\n            case WebGPUConstants.TextureFormat.R16Float:\r\n            case WebGPUConstants.TextureFormat.RG8Unorm:\r\n            case WebGPUConstants.TextureFormat.RG8Snorm:\r\n            case WebGPUConstants.TextureFormat.RG8Uint:\r\n            case WebGPUConstants.TextureFormat.RG8Sint:\r\n                return { width: 1, height: 1, length: 2 };\r\n\r\n            // 32 bits formats\r\n            case WebGPUConstants.TextureFormat.R32Uint:\r\n            case WebGPUConstants.TextureFormat.R32Sint:\r\n            case WebGPUConstants.TextureFormat.R32Float:\r\n            case WebGPUConstants.TextureFormat.RG16Uint:\r\n            case WebGPUConstants.TextureFormat.RG16Sint:\r\n            case WebGPUConstants.TextureFormat.RG16Float:\r\n            case WebGPUConstants.TextureFormat.RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGBA8Snorm:\r\n            case WebGPUConstants.TextureFormat.RGBA8Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA8Sint:\r\n            case WebGPUConstants.TextureFormat.BGRA8Unorm:\r\n            case WebGPUConstants.TextureFormat.BGRA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGB9E5UFloat:\r\n            case WebGPUConstants.TextureFormat.RGB10A2UINT:\r\n            case WebGPUConstants.TextureFormat.RGB10A2Unorm:\r\n            case WebGPUConstants.TextureFormat.RG11B10UFloat:\r\n                return { width: 1, height: 1, length: 4 };\r\n\r\n            // 64 bits formats\r\n            case WebGPUConstants.TextureFormat.RG32Uint:\r\n            case WebGPUConstants.TextureFormat.RG32Sint:\r\n            case WebGPUConstants.TextureFormat.RG32Float:\r\n            case WebGPUConstants.TextureFormat.RGBA16Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA16Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA16Float:\r\n                return { width: 1, height: 1, length: 8 };\r\n\r\n            // 128 bits formats\r\n            case WebGPUConstants.TextureFormat.RGBA32Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA32Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA32Float:\r\n                return { width: 1, height: 1, length: 16 };\r\n\r\n            // Depth and stencil formats\r\n            case WebGPUConstants.TextureFormat.Stencil8:\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"No fixed size for Stencil8 format!\";\r\n            case WebGPUConstants.TextureFormat.Depth16Unorm:\r\n                return { width: 1, height: 1, length: 2 };\r\n            case WebGPUConstants.TextureFormat.Depth24Plus:\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"No fixed size for Depth24Plus format!\";\r\n            case WebGPUConstants.TextureFormat.Depth24PlusStencil8:\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"No fixed size for Depth24PlusStencil8 format!\";\r\n            case WebGPUConstants.TextureFormat.Depth32Float:\r\n                return { width: 1, height: 1, length: 4 };\r\n            case WebGPUConstants.TextureFormat.Depth32FloatStencil8:\r\n                return { width: 1, height: 1, length: 5 };\r\n\r\n            // BC compressed formats usable if \"texture-compression-bc\" is both\r\n            // supported by the device/user agent and enabled in requestDevice.\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBUFloat:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBFloat:\r\n            case WebGPUConstants.TextureFormat.BC5RGUnorm:\r\n            case WebGPUConstants.TextureFormat.BC5RGSnorm:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnormSRGB:\r\n                return { width: 4, height: 4, length: 16 };\r\n\r\n            case WebGPUConstants.TextureFormat.BC4RUnorm:\r\n            case WebGPUConstants.TextureFormat.BC4RSnorm:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnormSRGB:\r\n                return { width: 4, height: 4, length: 8 };\r\n\r\n            // ETC2 compressed formats usable if \"texture-compression-etc2\" is both\r\n            // supported by the device/user agent and enabled in requestDevice.\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.EACR11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACR11Snorm:\r\n                return { width: 4, height: 4, length: 8 };\r\n\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.EACRG11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACRG11Snorm:\r\n                return { width: 4, height: 4, length: 16 };\r\n\r\n            // ASTC compressed formats usable if \"texture-compression-astc\" is both\r\n            // supported by the device/user agent and enabled in requestDevice.\r\n            case WebGPUConstants.TextureFormat.ASTC4x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4UnormSRGB:\r\n                return { width: 4, height: 4, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC5x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4UnormSRGB:\r\n                return { width: 5, height: 4, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC5x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5UnormSRGB:\r\n                return { width: 5, height: 5, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC6x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5UnormSRGB:\r\n                return { width: 6, height: 5, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC6x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6UnormSRGB:\r\n                return { width: 6, height: 6, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC8x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5UnormSRGB:\r\n                return { width: 8, height: 5, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC8x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6UnormSRGB:\r\n                return { width: 8, height: 6, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC8x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8UnormSRGB:\r\n                return { width: 8, height: 8, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC10x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5UnormSRGB:\r\n                return { width: 10, height: 5, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC10x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6UnormSRGB:\r\n                return { width: 10, height: 6, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC10x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8UnormSRGB:\r\n                return { width: 10, height: 8, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC10x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10UnormSRGB:\r\n                return { width: 10, height: 10, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC12x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10UnormSRGB:\r\n                return { width: 12, height: 10, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC12x12Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12UnormSRGB:\r\n                return { width: 12, height: 12, length: 16 };\r\n        }\r\n\r\n        return { width: 1, height: 1, length: 4 };\r\n    }\r\n\r\n    public static IsHardwareTexture(texture: HardwareTextureWrapper | GPUTexture): texture is HardwareTextureWrapper {\r\n        return !!(texture as HardwareTextureWrapper).release;\r\n    }\r\n\r\n    public static IsInternalTexture(texture: InternalTexture | GPUTexture): texture is InternalTexture {\r\n        return !!(texture as InternalTexture).dispose;\r\n    }\r\n\r\n    public static IsImageBitmap(imageBitmap: ImageBitmap | { width: number; height: number }): imageBitmap is ImageBitmap {\r\n        return (imageBitmap as ImageBitmap).close !== undefined;\r\n    }\r\n\r\n    public static IsImageBitmapArray(imageBitmap: ImageBitmap[] | { width: number; height: number }): imageBitmap is ImageBitmap[] {\r\n        return Array.isArray(imageBitmap as ImageBitmap[]) && (imageBitmap as ImageBitmap[])[0].close !== undefined;\r\n    }\r\n\r\n    public static IsCompressedFormat(format: GPUTextureFormat): boolean {\r\n        switch (format) {\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBFloat:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBUFloat:\r\n            case WebGPUConstants.TextureFormat.BC5RGSnorm:\r\n            case WebGPUConstants.TextureFormat.BC5RGUnorm:\r\n            case WebGPUConstants.TextureFormat.BC4RSnorm:\r\n            case WebGPUConstants.TextureFormat.BC4RUnorm:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.EACR11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACR11Snorm:\r\n            case WebGPUConstants.TextureFormat.EACRG11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACRG11Snorm:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12UnormSRGB:\r\n                return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public static GetWebGPUTextureFormat(type: number, format: number, useSRGBBuffer = false): GPUTextureFormat {\r\n        switch (format) {\r\n            case Constants.TEXTUREFORMAT_DEPTH16:\r\n                return WebGPUConstants.TextureFormat.Depth16Unorm;\r\n            case Constants.TEXTUREFORMAT_DEPTH24:\r\n                return WebGPUConstants.TextureFormat.Depth24Plus;\r\n            case Constants.TEXTUREFORMAT_DEPTH24_STENCIL8:\r\n                return WebGPUConstants.TextureFormat.Depth24PlusStencil8;\r\n            case Constants.TEXTUREFORMAT_DEPTH32_FLOAT:\r\n                return WebGPUConstants.TextureFormat.Depth32Float;\r\n            case Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8:\r\n                return WebGPUConstants.TextureFormat.Depth32FloatStencil8;\r\n            case Constants.TEXTUREFORMAT_STENCIL8:\r\n                return WebGPUConstants.TextureFormat.Stencil8;\r\n\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_BPTC_UNORM:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.BC7RGBAUnormSRGB : WebGPUConstants.TextureFormat.BC7RGBAUnorm;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT:\r\n                return WebGPUConstants.TextureFormat.BC6HRGBUFloat;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGB_BPTC_SIGNED_FLOAT:\r\n                return WebGPUConstants.TextureFormat.BC6HRGBFloat;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT5:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.BC3RGBAUnormSRGB : WebGPUConstants.TextureFormat.BC3RGBAUnorm;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT3:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.BC2RGBAUnormSRGB : WebGPUConstants.TextureFormat.BC2RGBAUnorm;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT1:\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGB_S3TC_DXT1:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.BC1RGBAUnormSRGB : WebGPUConstants.TextureFormat.BC1RGBAUnorm;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_ASTC_4x4:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.ASTC4x4UnormSRGB : WebGPUConstants.TextureFormat.ASTC4x4Unorm;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGB_ETC1_WEBGL:\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGB8_ETC2:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.ETC2RGB8UnormSRGB : WebGPUConstants.TextureFormat.ETC2RGB8Unorm;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGBA8_ETC2_EAC:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.ETC2RGBA8UnormSRGB : WebGPUConstants.TextureFormat.ETC2RGBA8Unorm;\r\n        }\r\n\r\n        switch (type) {\r\n            case Constants.TEXTURETYPE_BYTE:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return WebGPUConstants.TextureFormat.R8Snorm;\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return WebGPUConstants.TextureFormat.RG8Snorm;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"RGB format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.R8Sint;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RG8Sint;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"RGB_INTEGER format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGBA8Sint;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA8Snorm;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_BYTE:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return WebGPUConstants.TextureFormat.R8Unorm;\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return WebGPUConstants.TextureFormat.RG8Unorm;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return useSRGBBuffer ? WebGPUConstants.TextureFormat.RGBA8UnormSRGB : WebGPUConstants.TextureFormat.RGBA8Unorm;\r\n                    case Constants.TEXTUREFORMAT_BGRA:\r\n                        return useSRGBBuffer ? WebGPUConstants.TextureFormat.BGRA8UnormSRGB : WebGPUConstants.TextureFormat.BGRA8Unorm;\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.R8Uint;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RG8Uint;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"RGB_INTEGER format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGBA8Uint;\r\n                    case Constants.TEXTUREFORMAT_ALPHA:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_ALPHA format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_LUMINANCE:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_LUMINANCE format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_LUMINANCE_ALPHA:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_LUMINANCE_ALPHA format not supported in WebGPU\";\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA8Unorm;\r\n                }\r\n            case Constants.TEXTURETYPE_SHORT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.R16Sint;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RG16Sint;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB_INTEGER format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGBA16Sint;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA16Sint;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.R16Uint;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RG16Uint;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB_INTEGER format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGBA16Uint;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA16Uint;\r\n                }\r\n            case Constants.TEXTURETYPE_INT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.R32Sint;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RG32Sint;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB_INTEGER format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGBA32Sint;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA32Sint;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_INTEGER: // Refers to UNSIGNED_INT\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.R32Uint;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RG32Uint;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB_INTEGER format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGBA32Uint;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA32Uint;\r\n                }\r\n            case Constants.TEXTURETYPE_FLOAT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return WebGPUConstants.TextureFormat.R32Float; // By default. Other possibility is R16Float.\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return WebGPUConstants.TextureFormat.RG32Float; // By default. Other possibility is RG16Float.\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return WebGPUConstants.TextureFormat.RGBA32Float; // By default. Other possibility is RGBA16Float.\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA32Float;\r\n                }\r\n            case Constants.TEXTURETYPE_HALF_FLOAT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return WebGPUConstants.TextureFormat.R16Float;\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return WebGPUConstants.TextureFormat.RG16Float;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return WebGPUConstants.TextureFormat.RGBA16Float;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA16Float;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_6_5:\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"TEXTURETYPE_UNSIGNED_SHORT_5_6_5 format not supported in WebGPU\";\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_10F_11F_11F_REV:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return WebGPUConstants.TextureFormat.RG11B10UFloat;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGBA_INTEGER format not supported in WebGPU when type is TEXTURETYPE_UNSIGNED_INT_10F_11F_11F_REV\";\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RG11B10UFloat;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_5_9_9_9_REV:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return WebGPUConstants.TextureFormat.RGB9E5UFloat;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGBA_INTEGER format not supported in WebGPU when type is TEXTURETYPE_UNSIGNED_INT_5_9_9_9_REV\";\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGB9E5UFloat;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4:\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4 format not supported in WebGPU\";\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1:\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1 format not supported in WebGPU\";\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_2_10_10_10_REV:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return WebGPUConstants.TextureFormat.RGB10A2Unorm;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGB10A2UINT;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGB10A2Unorm;\r\n                }\r\n        }\r\n\r\n        return useSRGBBuffer ? WebGPUConstants.TextureFormat.RGBA8UnormSRGB : WebGPUConstants.TextureFormat.RGBA8Unorm;\r\n    }\r\n\r\n    public static GetNumChannelsFromWebGPUTextureFormat(format: GPUTextureFormat): number {\r\n        switch (format) {\r\n            case WebGPUConstants.TextureFormat.R8Unorm:\r\n            case WebGPUConstants.TextureFormat.R8Snorm:\r\n            case WebGPUConstants.TextureFormat.R8Uint:\r\n            case WebGPUConstants.TextureFormat.R8Sint:\r\n            case WebGPUConstants.TextureFormat.BC4RUnorm:\r\n            case WebGPUConstants.TextureFormat.BC4RSnorm:\r\n            case WebGPUConstants.TextureFormat.R16Uint:\r\n            case WebGPUConstants.TextureFormat.R16Sint:\r\n            case WebGPUConstants.TextureFormat.Depth16Unorm:\r\n            case WebGPUConstants.TextureFormat.R16Float:\r\n            case WebGPUConstants.TextureFormat.R32Uint:\r\n            case WebGPUConstants.TextureFormat.R32Sint:\r\n            case WebGPUConstants.TextureFormat.R32Float:\r\n            case WebGPUConstants.TextureFormat.Depth32Float:\r\n            case WebGPUConstants.TextureFormat.Stencil8:\r\n            case WebGPUConstants.TextureFormat.Depth24Plus:\r\n            case WebGPUConstants.TextureFormat.EACR11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACR11Snorm:\r\n                return 1;\r\n\r\n            case WebGPUConstants.TextureFormat.RG8Unorm:\r\n            case WebGPUConstants.TextureFormat.RG8Snorm:\r\n            case WebGPUConstants.TextureFormat.RG8Uint:\r\n            case WebGPUConstants.TextureFormat.RG8Sint:\r\n            case WebGPUConstants.TextureFormat.Depth32FloatStencil8:\r\n            case WebGPUConstants.TextureFormat.BC5RGUnorm:\r\n            case WebGPUConstants.TextureFormat.BC5RGSnorm:\r\n            case WebGPUConstants.TextureFormat.RG16Uint:\r\n            case WebGPUConstants.TextureFormat.RG16Sint:\r\n            case WebGPUConstants.TextureFormat.RG16Float:\r\n            case WebGPUConstants.TextureFormat.RG32Uint:\r\n            case WebGPUConstants.TextureFormat.RG32Sint:\r\n            case WebGPUConstants.TextureFormat.RG32Float:\r\n            case WebGPUConstants.TextureFormat.Depth24PlusStencil8:\r\n            case WebGPUConstants.TextureFormat.EACRG11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACRG11Snorm:\r\n                return 2;\r\n\r\n            case WebGPUConstants.TextureFormat.RGB9E5UFloat:\r\n            case WebGPUConstants.TextureFormat.RG11B10UFloat:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBUFloat:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBFloat:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8UnormSRGB:\r\n                return 3;\r\n\r\n            case WebGPUConstants.TextureFormat.RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGBA8Snorm:\r\n            case WebGPUConstants.TextureFormat.RGBA8Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA8Sint:\r\n            case WebGPUConstants.TextureFormat.BGRA8Unorm:\r\n            case WebGPUConstants.TextureFormat.BGRA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGB10A2UINT:\r\n            case WebGPUConstants.TextureFormat.RGB10A2Unorm:\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGBA16Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA16Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA16Float:\r\n            case WebGPUConstants.TextureFormat.RGBA32Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA32Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA32Float:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12UnormSRGB:\r\n                return 4;\r\n        }\r\n\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw `Unknown format ${format}!`;\r\n    }\r\n\r\n    public static HasStencilAspect(format: GPUTextureFormat): boolean {\r\n        switch (format) {\r\n            case WebGPUConstants.TextureFormat.Stencil8:\r\n            case WebGPUConstants.TextureFormat.Depth32FloatStencil8:\r\n            case WebGPUConstants.TextureFormat.Depth24PlusStencil8:\r\n                return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public static HasDepthAndStencilAspects(format: GPUTextureFormat): boolean {\r\n        switch (format) {\r\n            case WebGPUConstants.TextureFormat.Depth32FloatStencil8:\r\n            case WebGPUConstants.TextureFormat.Depth24PlusStencil8:\r\n                return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public static GetDepthFormatOnly(format: GPUTextureFormat): GPUTextureFormat {\r\n        switch (format) {\r\n            case WebGPUConstants.TextureFormat.Depth16Unorm:\r\n                return WebGPUConstants.TextureFormat.Depth16Unorm;\r\n            case WebGPUConstants.TextureFormat.Depth24Plus:\r\n                return WebGPUConstants.TextureFormat.Depth24Plus;\r\n            case WebGPUConstants.TextureFormat.Depth24PlusStencil8:\r\n                return WebGPUConstants.TextureFormat.Depth24Plus;\r\n            case WebGPUConstants.TextureFormat.Depth32Float:\r\n                return WebGPUConstants.TextureFormat.Depth32Float;\r\n            case WebGPUConstants.TextureFormat.Depth32FloatStencil8:\r\n                return WebGPUConstants.TextureFormat.Depth32Float;\r\n        }\r\n\r\n        return format;\r\n    }\r\n\r\n    public static GetSample(sampleCount: number) {\r\n        // WebGPU only supports 1 or 4\r\n        return sampleCount > 1 ? 4 : 1;\r\n    }\r\n}\r\n"]}