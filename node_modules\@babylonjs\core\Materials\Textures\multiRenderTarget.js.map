{"version": 3, "file": "multiRenderTarget.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/multiRenderTarget.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,OAAO,EAAE,MAAM,kCAAkC,CAAC;AAC3D,OAAO,EAAE,mBAAmB,EAAE,MAAM,8CAA8C,CAAC;AACnF,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAEpD,OAAO,6CAA6C,CAAC;AA0FrD;;;;;GAKG;AACH,MAAM,OAAO,iBAAkB,SAAQ,mBAAmB;IAOtD;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,oBAAoB,IAAI,KAAK,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACH,IAAW,KAAK,CAAC,IAAY;QACzB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC;aAClC;SACJ;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,KAAK,CAAC,IAAY;QACzB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC;aAClC;SACJ;IACL,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,YAAY,IAAY,EAAE,IAAS,EAAE,KAAa,EAAE,KAAa,EAAE,OAAmC,EAAE,YAAuB;QAC3H,MAAM,eAAe,GAAG,OAAO,IAAI,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC;QAC7F,MAAM,oBAAoB,GAAG,OAAO,IAAI,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC;QAC5G,MAAM,kBAAkB,GAAG,OAAO,IAAI,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC;QAChI,MAAM,sBAAsB,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC;QAChI,MAAM,kCAAkC,GAAG,OAAO,IAAI,OAAO,CAAC,kCAAkC,CAAC,CAAC,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC,CAAC,KAAK,CAAC;QACtJ,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,eAAe,EAAE,sBAAsB,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAErJ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO;SACV;QAED,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,MAAM,aAAa,GAAa,EAAE,CAAC;QACnC,MAAM,cAAc,GAAc,EAAE,CAAC;QACrC,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,cAAc,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAEhI,MAAM,mBAAmB,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC;QACvH,MAAM,qBAAqB,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC;QAE9H,IAAI,CAAC,yBAAyB,GAAG;YAC7B,aAAa,EAAE,aAAa;YAC5B,eAAe,EAAE,eAAe;YAChC,mBAAmB,EAAE,mBAAmB;YACxC,qBAAqB,EAAE,qBAAqB;YAC5C,oBAAoB,EAAE,oBAAoB;YAC1C,kBAAkB,EAAE,kBAAkB;YACtC,KAAK,EAAE,KAAK;YACZ,YAAY,EAAE,KAAK;YACnB,cAAc,EAAE,cAAc;YAC9B,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,WAAW;YACxB,SAAS,EAAE,SAAS;YACpB,UAAU,EAAE,UAAU;YACtB,WAAW,EAAE,WAAW;YACxB,MAAM,EAAE,YAAY;YACpB,KAAK,EAAE,IAAI;SACd,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,mCAAmC,GAAG,kCAAkC,CAAC;QAE9E,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;SACtC;IACL,CAAC;IAEO,UAAU,CACd,KAAa,EACb,KAAe,EACf,aAAuB,EACvB,cAAyB,EACzB,OAAiB,EACjB,OAAiB,EACjB,SAAmB,EACnB,UAAoB,EACpB,WAAqB,EACrB,OAAmC;QAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC5B,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC5D,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aAChC;iBAAM;gBACH,KAAK,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;aACzG;YAED,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC5E,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;aAChD;iBAAM;gBACH,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;aACrD;YAED,IAAI,OAAO,IAAI,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC9E,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;aAClD;iBAAM;gBACH,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC9B;YAED,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAChE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aACpC;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;aAC9C;YAED,IAAI,OAAO,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBACxE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;aACxC;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;aACtC;YAED,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBACpE,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;aACxC;iBAAM;gBACH,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACrB;YAED,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBACtE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aAC1C;iBAAM;gBACH,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACtB;YAED,IAAI,OAAO,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBACxE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;aAC5C;iBAAM;gBACH,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACvB;SACJ;IACL,CAAC;IAEO,iCAAiC;QACrC,MAAM,4BAA4B,GAA8B,EAAE,CAAC;QACnE,MAAM,4BAA4B,GAAa,EAAE,CAAC;QAElD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,4BAA4B,CAAC;SACvC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAc,CAAC,QAAS,CAAC;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,MAAM,OAAO,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,CAAC,OAAO,EAAE;gBACV,SAAS;aACZ;YACD,MAAM,SAAS,GAAG,4BAA4B,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACjE,IAAI,SAAS,KAAK,SAAS,EAAE;gBACzB,4BAA4B,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;aAC/C;iBAAM;gBACH,4BAA4B,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;aACtD;SACJ;QAED,OAAO,4BAA4B,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,eAAe,GAAG,KAAK,EAAE,mBAA4B,KAAK,EAAE,YAAuB;QAC/F,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,EAAE;YACpC,OAAO;SACV;QAED,MAAM,4BAA4B,GAAG,IAAI,CAAC,iCAAiC,EAAE,CAAC;QAE9E,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,IAAI,gBAAgB,EAAE;YAClB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;SACtC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAc,CAAC,QAAS,CAAC;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAClC,IAAI,4BAA4B,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC/C,IAAI,CAAC,aAAc,CAAC,UAAU,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACxF;YACD,OAAO,CAAC,QAAQ,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACvC,IAAI,OAAO,CAAC,QAAQ,EAAE;gBAClB,OAAO,CAAC,SAAS,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACjD,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC;aAC5D;SACJ;QAED,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE;YACpB,IAAI,CAAC,aAAc,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,mCAAmC,EAAE,IAAI,CAAC,CAAC;SACjG;IACL,CAAC;IAEO,uBAAuB;QAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,EAAG,CAAC,0BAA0B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,yBAAyB,EAAE,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAC1J,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;IAC/C,CAAC;IAEO,gBAAgB;QACpB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,wEAAwE;gBAC3G,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;aAC/B;SACJ;IACL,CAAC;IAEO,eAAe,CAAC,YAAuB;QAC3C,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAc,CAAC,QAAS,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnD,IAAI,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE;gBACnB,OAAO,CAAC,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;aAClC;YACD,OAAO,CAAC,QAAQ,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACvC,IAAI,OAAO,CAAC,QAAQ,EAAE;gBAClB,OAAO,CAAC,SAAS,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACjD,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC;aAC5D;YACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAChC;IACL,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,OAAwB,EAAE,KAAa,EAAE,kBAA2B,IAAI;QAC9F,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO;SACV;QAED,IAAI,KAAK,KAAK,CAAC,EAAE;YACb,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;SAC3B;QAED,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;QAE9D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACvB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;SACxF;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC;QACrD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;QAE7D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjF,IAAI,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE;YACtC,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;SAC9D;QACD,IAAI,IAAI,CAAC,yBAAyB,CAAC,aAAa,EAAE;YAC9C,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,YAAY,CAAC;SAC9E;QACD,IAAI,IAAI,CAAC,yBAAyB,CAAC,cAAc,EAAE;YAC/C,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC;SACjF;QACD,IAAI,IAAI,CAAC,yBAAyB,CAAC,WAAW,IAAI,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;YACxG,IAAI,MAAM,GAAW,CAAC,CAAC;YACvB,IAAI,OAAO,CAAC,SAAS,EAAE;gBACnB,MAAM,GAAG,SAAS,CAAC,gBAAgB,CAAC;aACvC;iBAAM,IAAI,OAAO,CAAC,MAAM,EAAE;gBACvB,MAAM,GAAG,SAAS,CAAC,gBAAgB,CAAC;aACvC,CAAC;;eAEC;iBAAM,IAAI,OAAO,CAAC,IAAI,EAAE;gBACvB,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;aACjC;iBAAM;gBACH,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;aACjC;YACD,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;SAC9D;IACL,CAAC;IAED;;;;;OAKG;IACI,oBAAoB,CAAC,KAAa,EAAE,aAAqB,CAAC,CAAC,EAAE,YAAoB,CAAC,CAAC;QACtF,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YAC7C,OAAO;SACV;QAED,IAAI,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE;YAC3C,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC;SACjE;QACD,IAAI,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE;YAC1C,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;SAC/D;QAED,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,KAAK,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IACzE,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,YAAsB,EAAE,WAAqB;QACvE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO;SACV;QAED,IAAI,CAAC,yBAAyB,CAAC,UAAU,GAAG,YAAY,CAAC;QACzD,IAAI,CAAC,yBAAyB,CAAC,SAAS,GAAG,WAAW,CAAC;QAEvD,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,KAAa;QAC5B,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SACxD;aAAM;YACH,0FAA0F;YAC1F,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACzB;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,IAAS;QACnB,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;OAMG;IACI,WAAW,CAAC,KAAa,EAAE,OAAmC,EAAE,YAAuB;QAC1F,IAAI,CAAC,yBAAyB,CAAC,YAAY,GAAG,KAAK,CAAC;QACpD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,MAAM,aAAa,GAAa,EAAE,CAAC;QACnC,MAAM,cAAc,GAAc,EAAE,CAAC;QACrC,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,cAAc,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAChI,IAAI,CAAC,yBAAyB,CAAC,KAAK,GAAG,KAAK,CAAC;QAC7C,IAAI,CAAC,yBAAyB,CAAC,aAAa,GAAG,aAAa,CAAC;QAC7D,IAAI,CAAC,yBAAyB,CAAC,cAAc,GAAG,cAAc,CAAC;QAC/D,IAAI,CAAC,yBAAyB,CAAC,OAAO,GAAG,OAAO,CAAC;QACjD,IAAI,CAAC,yBAAyB,CAAC,WAAW,GAAG,WAAW,CAAC;QACzD,IAAI,CAAC,yBAAyB,CAAC,SAAS,GAAG,SAAS,CAAC;QACrD,IAAI,CAAC,yBAAyB,CAAC,UAAU,GAAG,UAAU,CAAC;QACvD,IAAI,CAAC,yBAAyB,CAAC,WAAW,GAAG,WAAW,CAAC;QACzD,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,YAAY,CAAC;QAErD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;IAC7C,CAAC;IAES,kBAAkB,CAAC,MAAc,EAAE,SAAiB;QAC1D,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,MAAM,CAAC,qCAAqC,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE;gBAC/E,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,4BAA4B,GAAG,KAAK;QAC/C,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,4BAA4B,EAAE;YAC/B,IAAI,CAAC,uBAAuB,EAAE,CAAC;SAClC;aAAM;YACH,oDAAoD;YACpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACxB;QACD,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,uBAAuB;QAC1B,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC;QAEtD,IAAI,CAAC,gBAAgB,EAAE;YACnB,OAAO;SACV;QAED,KAAK,IAAI,CAAC,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACnD,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;SACrC;QAED,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC9B,CAAC;CACJ", "sourcesContent": ["import type { Scene } from \"../../scene\";\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport { Texture } from \"../../Materials/Textures/texture\";\r\nimport { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\nimport { Constants } from \"../../Engines/constants\";\r\n\r\nimport \"../../Engines/Extensions/engine.multiRender\";\r\nimport type { InternalTexture } from \"./internalTexture\";\r\n\r\n/**\r\n * Creation options of the multi render target texture.\r\n */\r\nexport interface IMultiRenderTargetOptions {\r\n    /**\r\n     * Define if the texture needs to create mip maps after render.\r\n     */\r\n    generateMipMaps?: boolean;\r\n    /**\r\n     * Define the types of all the draw buffers (render textures) we want to create\r\n     */\r\n    types?: number[];\r\n    /**\r\n     * Define the sampling modes of all the draw buffers (render textures) we want to create\r\n     */\r\n    samplingModes?: number[];\r\n    /**\r\n     * Define if sRGB format should be used for each of the draw buffers (render textures) we want to create\r\n     */\r\n    useSRGBBuffers?: boolean[];\r\n    /**\r\n     * Define if a depth buffer is required\r\n     */\r\n    generateDepthBuffer?: boolean;\r\n    /**\r\n     * Define if a stencil buffer is required\r\n     */\r\n    generateStencilBuffer?: boolean;\r\n    /**\r\n     * Define if a depth texture is required instead of a depth buffer\r\n     */\r\n    generateDepthTexture?: boolean;\r\n    /**\r\n     * Define the internal format of the buffer in the RTT (RED, RG, RGB, RGBA (default), ALPHA...) of all the draw buffers (render textures) we want to create\r\n     */\r\n    formats?: number[];\r\n    /**\r\n     * Define depth texture format to use\r\n     */\r\n    depthTextureFormat?: number;\r\n    /**\r\n     * Define the number of desired draw buffers (render textures)\r\n     */\r\n    textureCount?: number;\r\n    /**\r\n     * Define if aspect ratio should be adapted to the texture or stay the scene one\r\n     */\r\n    doNotChangeAspectRatio?: boolean;\r\n    /**\r\n     * Define the default type of the buffers we are creating\r\n     */\r\n    defaultType?: number;\r\n    /**\r\n     * Define the default type of the buffers we are creating\r\n     */\r\n    drawOnlyOnFirstAttachmentByDefault?: boolean;\r\n    /**\r\n     * Define the type of texture at each attahment index (of Constants.TEXTURE_2D, .TEXTURE_2D_ARRAY, .TEXTURE_CUBE_MAP, .TEXTURE_CUBE_MAP_ARRAY, .TEXTURE_3D).\r\n     * You can also use the -1 value to indicate that no texture should be created but that you will assign a texture to that attachment index later.\r\n     * Can be useful when you want to attach several layers of the same 2DArrayTexture / 3DTexture or several faces of the same CubeMapTexture: Use the setInternalTexture\r\n     * method for that purpose, after the MultiRenderTarget has been created.\r\n     */\r\n    targetTypes?: number[];\r\n    /**\r\n     * Define the face index of each texture in the textures array (if applicable, given the corresponding targetType) at creation time (for Constants.TEXTURE_CUBE_MAP and .TEXTURE_CUBE_MAP_ARRAY).\r\n     * Can be changed at any time by calling setLayerAndFaceIndices or setLayerAndFaceIndex\r\n     */\r\n    faceIndex?: number[];\r\n    /**\r\n     * Define the layer index of each texture in the textures array (if applicable, given the corresponding targetType) at creation time (for Constants.TEXTURE_3D, .TEXTURE_2D_ARRAY, and .TEXTURE_CUBE_MAP_ARRAY).\r\n     * Can be changed at any time by calling setLayerAndFaceIndices or setLayerAndFaceIndex\r\n     */\r\n    layerIndex?: number[];\r\n    /**\r\n     * Define the number of layer of each texture in the textures array (if applicable, given the corresponding targetType) (for Constants.TEXTURE_3D, .TEXTURE_2D_ARRAY, and .TEXTURE_CUBE_MAP_ARRAY)\r\n     */\r\n    layerCounts?: number[];\r\n    /**\r\n     * Define the names of the textures (used for debugging purpose)\r\n     */\r\n    labels?: string[];\r\n    /**\r\n     * Label of the RenderTargetWrapper (used for debugging only)\r\n     */\r\n    label?: string;\r\n}\r\n\r\n/**\r\n * A multi render target, like a render target provides the ability to render to a texture.\r\n * Unlike the render target, it can render to several draw buffers (render textures) in one draw.\r\n * This is specially interesting in deferred rendering or for any effects requiring more than\r\n * just one color from a single pass.\r\n */\r\nexport class MultiRenderTarget extends RenderTargetTexture {\r\n    private _textures: Texture[];\r\n    private _multiRenderTargetOptions: IMultiRenderTargetOptions;\r\n    private _count: number;\r\n    private _drawOnlyOnFirstAttachmentByDefault: boolean;\r\n    private _textureNames?: string[];\r\n\r\n    /**\r\n     * Get if draw buffers (render textures) are currently supported by the used hardware and browser.\r\n     */\r\n    public get isSupported(): boolean {\r\n        return this._engine?.getCaps().drawBuffersExtension ?? false;\r\n    }\r\n\r\n    /**\r\n     * Get the list of textures generated by the multi render target.\r\n     */\r\n    public get textures(): Texture[] {\r\n        return this._textures;\r\n    }\r\n\r\n    /**\r\n     * Gets the number of textures in this MRT. This number can be different from `_textures.length` in case a depth texture is generated.\r\n     */\r\n    public get count(): number {\r\n        return this._count;\r\n    }\r\n\r\n    /**\r\n     * Get the depth texture generated by the multi render target if options.generateDepthTexture has been set\r\n     */\r\n    public get depthTexture(): Texture {\r\n        return this._textures[this._textures.length - 1];\r\n    }\r\n\r\n    /**\r\n     * Set the wrapping mode on U of all the textures we are rendering to.\r\n     * Can be any of the Texture. (CLAMP_ADDRESSMODE, MIRROR_ADDRESSMODE or WRAP_ADDRESSMODE)\r\n     */\r\n    public set wrapU(wrap: number) {\r\n        if (this._textures) {\r\n            for (let i = 0; i < this._textures.length; i++) {\r\n                this._textures[i].wrapU = wrap;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the wrapping mode on V of all the textures we are rendering to.\r\n     * Can be any of the Texture. (CLAMP_ADDRESSMODE, MIRROR_ADDRESSMODE or WRAP_ADDRESSMODE)\r\n     */\r\n    public set wrapV(wrap: number) {\r\n        if (this._textures) {\r\n            for (let i = 0; i < this._textures.length; i++) {\r\n                this._textures[i].wrapV = wrap;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Instantiate a new multi render target texture.\r\n     * A multi render target, like a render target provides the ability to render to a texture.\r\n     * Unlike the render target, it can render to several draw buffers (render textures) in one draw.\r\n     * This is specially interesting in deferred rendering or for any effects requiring more than\r\n     * just one color from a single pass.\r\n     * @param name Define the name of the texture\r\n     * @param size Define the size of the buffers to render to\r\n     * @param count Define the number of target we are rendering into\r\n     * @param scene Define the scene the texture belongs to\r\n     * @param options Define the options used to create the multi render target\r\n     * @param textureNames Define the names to set to the textures (if count \\> 0 - optional)\r\n     */\r\n    constructor(name: string, size: any, count: number, scene?: Scene, options?: IMultiRenderTargetOptions, textureNames?: string[]) {\r\n        const generateMipMaps = options && options.generateMipMaps ? options.generateMipMaps : false;\r\n        const generateDepthTexture = options && options.generateDepthTexture ? options.generateDepthTexture : false;\r\n        const depthTextureFormat = options && options.depthTextureFormat ? options.depthTextureFormat : Constants.TEXTUREFORMAT_DEPTH16;\r\n        const doNotChangeAspectRatio = !options || options.doNotChangeAspectRatio === undefined ? true : options.doNotChangeAspectRatio;\r\n        const drawOnlyOnFirstAttachmentByDefault = options && options.drawOnlyOnFirstAttachmentByDefault ? options.drawOnlyOnFirstAttachmentByDefault : false;\r\n        super(name, size, scene, generateMipMaps, doNotChangeAspectRatio, undefined, undefined, undefined, undefined, undefined, undefined, undefined, true);\r\n\r\n        if (!this.isSupported) {\r\n            this.dispose();\r\n            return;\r\n        }\r\n\r\n        this._textureNames = textureNames;\r\n\r\n        const types: number[] = [];\r\n        const samplingModes: number[] = [];\r\n        const useSRGBBuffers: boolean[] = [];\r\n        const formats: number[] = [];\r\n        const targetTypes: number[] = [];\r\n        const faceIndex: number[] = [];\r\n        const layerIndex: number[] = [];\r\n        const layerCounts: number[] = [];\r\n        this._initTypes(count, types, samplingModes, useSRGBBuffers, formats, targetTypes, faceIndex, layerIndex, layerCounts, options);\r\n\r\n        const generateDepthBuffer = !options || options.generateDepthBuffer === undefined ? true : options.generateDepthBuffer;\r\n        const generateStencilBuffer = !options || options.generateStencilBuffer === undefined ? false : options.generateStencilBuffer;\r\n\r\n        this._multiRenderTargetOptions = {\r\n            samplingModes: samplingModes,\r\n            generateMipMaps: generateMipMaps,\r\n            generateDepthBuffer: generateDepthBuffer,\r\n            generateStencilBuffer: generateStencilBuffer,\r\n            generateDepthTexture: generateDepthTexture,\r\n            depthTextureFormat: depthTextureFormat,\r\n            types: types,\r\n            textureCount: count,\r\n            useSRGBBuffers: useSRGBBuffers,\r\n            formats: formats,\r\n            targetTypes: targetTypes,\r\n            faceIndex: faceIndex,\r\n            layerIndex: layerIndex,\r\n            layerCounts: layerCounts,\r\n            labels: textureNames,\r\n            label: name,\r\n        };\r\n\r\n        this._count = count;\r\n        this._drawOnlyOnFirstAttachmentByDefault = drawOnlyOnFirstAttachmentByDefault;\r\n\r\n        if (count > 0) {\r\n            this._createInternalTextures();\r\n            this._createTextures(textureNames);\r\n        }\r\n    }\r\n\r\n    private _initTypes(\r\n        count: number,\r\n        types: number[],\r\n        samplingModes: number[],\r\n        useSRGBBuffers: boolean[],\r\n        formats: number[],\r\n        targets: number[],\r\n        faceIndex: number[],\r\n        layerIndex: number[],\r\n        layerCounts: number[],\r\n        options?: IMultiRenderTargetOptions\r\n    ) {\r\n        for (let i = 0; i < count; i++) {\r\n            if (options && options.types && options.types[i] !== undefined) {\r\n                types.push(options.types[i]);\r\n            } else {\r\n                types.push(options && options.defaultType ? options.defaultType : Constants.TEXTURETYPE_UNSIGNED_INT);\r\n            }\r\n\r\n            if (options && options.samplingModes && options.samplingModes[i] !== undefined) {\r\n                samplingModes.push(options.samplingModes[i]);\r\n            } else {\r\n                samplingModes.push(Texture.BILINEAR_SAMPLINGMODE);\r\n            }\r\n\r\n            if (options && options.useSRGBBuffers && options.useSRGBBuffers[i] !== undefined) {\r\n                useSRGBBuffers.push(options.useSRGBBuffers[i]);\r\n            } else {\r\n                useSRGBBuffers.push(false);\r\n            }\r\n\r\n            if (options && options.formats && options.formats[i] !== undefined) {\r\n                formats.push(options.formats[i]);\r\n            } else {\r\n                formats.push(Constants.TEXTUREFORMAT_RGBA);\r\n            }\r\n\r\n            if (options && options.targetTypes && options.targetTypes[i] !== undefined) {\r\n                targets.push(options.targetTypes[i]);\r\n            } else {\r\n                targets.push(Constants.TEXTURE_2D);\r\n            }\r\n\r\n            if (options && options.faceIndex && options.faceIndex[i] !== undefined) {\r\n                faceIndex.push(options.faceIndex[i]);\r\n            } else {\r\n                faceIndex.push(0);\r\n            }\r\n\r\n            if (options && options.layerIndex && options.layerIndex[i] !== undefined) {\r\n                layerIndex.push(options.layerIndex[i]);\r\n            } else {\r\n                layerIndex.push(0);\r\n            }\r\n\r\n            if (options && options.layerCounts && options.layerCounts[i] !== undefined) {\r\n                layerCounts.push(options.layerCounts[i]);\r\n            } else {\r\n                layerCounts.push(1);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _createInternaTextureIndexMapping() {\r\n        const mapMainInternalTexture2Index: { [key: number]: number } = {};\r\n        const mapInternalTexture2MainIndex: number[] = [];\r\n\r\n        if (!this._renderTarget) {\r\n            return mapInternalTexture2MainIndex;\r\n        }\r\n\r\n        const internalTextures = this._renderTarget!.textures!;\r\n        for (let i = 0; i < internalTextures.length; i++) {\r\n            const texture = internalTextures[i];\r\n            if (!texture) {\r\n                continue;\r\n            }\r\n            const mainIndex = mapMainInternalTexture2Index[texture.uniqueId];\r\n            if (mainIndex !== undefined) {\r\n                mapInternalTexture2MainIndex[i] = mainIndex;\r\n            } else {\r\n                mapMainInternalTexture2Index[texture.uniqueId] = i;\r\n            }\r\n        }\r\n\r\n        return mapInternalTexture2MainIndex;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _rebuild(fromContextLost = false, forceFullRebuild: boolean = false, textureNames?: string[]): void {\r\n        if (this._count < 1 || fromContextLost) {\r\n            return;\r\n        }\r\n\r\n        const mapInternalTexture2MainIndex = this._createInternaTextureIndexMapping();\r\n\r\n        this.releaseInternalTextures();\r\n        this._createInternalTextures();\r\n\r\n        if (forceFullRebuild) {\r\n            this._releaseTextures();\r\n            this._createTextures(textureNames);\r\n        }\r\n\r\n        const internalTextures = this._renderTarget!.textures!;\r\n        for (let i = 0; i < internalTextures.length; i++) {\r\n            const texture = this._textures[i];\r\n            if (mapInternalTexture2MainIndex[i] !== undefined) {\r\n                this._renderTarget!.setTexture(internalTextures[mapInternalTexture2MainIndex[i]], i);\r\n            }\r\n            texture._texture = internalTextures[i];\r\n            if (texture._texture) {\r\n                texture._noMipmap = !texture._texture.useMipMaps;\r\n                texture._useSRGBBuffer = texture._texture._useSRGBBuffer;\r\n            }\r\n        }\r\n\r\n        if (this.samples !== 1) {\r\n            this._renderTarget!.setSamples(this.samples, !this._drawOnlyOnFirstAttachmentByDefault, true);\r\n        }\r\n    }\r\n\r\n    private _createInternalTextures(): void {\r\n        this._renderTarget = this._getEngine()!.createMultipleRenderTarget(this._size, this._multiRenderTargetOptions, !this._drawOnlyOnFirstAttachmentByDefault);\r\n        this._texture = this._renderTarget.texture;\r\n    }\r\n\r\n    private _releaseTextures(): void {\r\n        if (this._textures) {\r\n            for (let i = 0; i < this._textures.length; i++) {\r\n                this._textures[i]._texture = null; // internal textures are released by a call to releaseInternalTextures()\r\n                this._textures[i].dispose();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _createTextures(textureNames?: string[]): void {\r\n        const internalTextures = this._renderTarget!.textures!;\r\n        this._textures = [];\r\n        for (let i = 0; i < internalTextures.length; i++) {\r\n            const texture = new Texture(null, this.getScene());\r\n            if (textureNames?.[i]) {\r\n                texture.name = textureNames[i];\r\n            }\r\n            texture._texture = internalTextures[i];\r\n            if (texture._texture) {\r\n                texture._noMipmap = !texture._texture.useMipMaps;\r\n                texture._useSRGBBuffer = texture._texture._useSRGBBuffer;\r\n            }\r\n            this._textures.push(texture);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Replaces an internal texture within the MRT. Useful to share textures between MultiRenderTarget.\r\n     * @param texture The new texture to set in the MRT\r\n     * @param index The index of the texture to replace\r\n     * @param disposePrevious Set to true if the previous internal texture should be disposed\r\n     */\r\n    public setInternalTexture(texture: InternalTexture, index: number, disposePrevious: boolean = true) {\r\n        if (!this.renderTarget) {\r\n            return;\r\n        }\r\n\r\n        if (index === 0) {\r\n            this._texture = texture;\r\n        }\r\n\r\n        this.renderTarget.setTexture(texture, index, disposePrevious);\r\n\r\n        if (!this.textures[index]) {\r\n            this.textures[index] = new Texture(null, this.getScene());\r\n            this.textures[index].name = this._textureNames?.[index] ?? this.textures[index].name;\r\n        }\r\n        this.textures[index]._texture = texture;\r\n        this.textures[index]._noMipmap = !texture.useMipMaps;\r\n        this.textures[index]._useSRGBBuffer = texture._useSRGBBuffer;\r\n\r\n        this._count = this.renderTarget.textures ? this.renderTarget.textures.length : 0;\r\n\r\n        if (this._multiRenderTargetOptions.types) {\r\n            this._multiRenderTargetOptions.types[index] = texture.type;\r\n        }\r\n        if (this._multiRenderTargetOptions.samplingModes) {\r\n            this._multiRenderTargetOptions.samplingModes[index] = texture.samplingMode;\r\n        }\r\n        if (this._multiRenderTargetOptions.useSRGBBuffers) {\r\n            this._multiRenderTargetOptions.useSRGBBuffers[index] = texture._useSRGBBuffer;\r\n        }\r\n        if (this._multiRenderTargetOptions.targetTypes && this._multiRenderTargetOptions.targetTypes[index] !== -1) {\r\n            let target: number = 0;\r\n            if (texture.is2DArray) {\r\n                target = Constants.TEXTURE_2D_ARRAY;\r\n            } else if (texture.isCube) {\r\n                target = Constants.TEXTURE_CUBE_MAP;\r\n            } /*else if (texture.isCubeArray) {\r\n                target = Constants.TEXTURE_CUBE_MAP_ARRAY;\r\n            }*/ else if (texture.is3D) {\r\n                target = Constants.TEXTURE_3D;\r\n            } else {\r\n                target = Constants.TEXTURE_2D;\r\n            }\r\n            this._multiRenderTargetOptions.targetTypes[index] = target;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Changes an attached texture's face index or layer.\r\n     * @param index The index of the texture to modify the attachment of\r\n     * @param layerIndex The layer index of the texture to be attached to the framebuffer\r\n     * @param faceIndex The face index of the texture to be attached to the framebuffer\r\n     */\r\n    public setLayerAndFaceIndex(index: number, layerIndex: number = -1, faceIndex: number = -1) {\r\n        if (!this.textures[index] || !this.renderTarget) {\r\n            return;\r\n        }\r\n\r\n        if (this._multiRenderTargetOptions.layerIndex) {\r\n            this._multiRenderTargetOptions.layerIndex[index] = layerIndex;\r\n        }\r\n        if (this._multiRenderTargetOptions.faceIndex) {\r\n            this._multiRenderTargetOptions.faceIndex[index] = faceIndex;\r\n        }\r\n\r\n        this.renderTarget.setLayerAndFaceIndex(index, layerIndex, faceIndex);\r\n    }\r\n\r\n    /**\r\n     * Changes every attached texture's face index or layer.\r\n     * @param layerIndices The layer indices of the texture to be attached to the framebuffer\r\n     * @param faceIndices The face indices of the texture to be attached to the framebuffer\r\n     */\r\n    public setLayerAndFaceIndices(layerIndices: number[], faceIndices: number[]) {\r\n        if (!this.renderTarget) {\r\n            return;\r\n        }\r\n\r\n        this._multiRenderTargetOptions.layerIndex = layerIndices;\r\n        this._multiRenderTargetOptions.faceIndex = faceIndices;\r\n\r\n        this.renderTarget.setLayerAndFaceIndices(layerIndices, faceIndices);\r\n    }\r\n\r\n    /**\r\n     * Define the number of samples used if MSAA is enabled.\r\n     */\r\n    public get samples(): number {\r\n        return this._samples;\r\n    }\r\n\r\n    public set samples(value: number) {\r\n        if (this._renderTarget) {\r\n            this._samples = this._renderTarget.setSamples(value);\r\n        } else {\r\n            // In case samples are set with 0 textures created, we must save the desired samples value\r\n            this._samples = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Resize all the textures in the multi render target.\r\n     * Be careful as it will recreate all the data in the new texture.\r\n     * @param size Define the new size\r\n     */\r\n    public resize(size: any) {\r\n        this._processSizeParameter(size, false);\r\n        this._rebuild(false, undefined, this._textureNames);\r\n    }\r\n\r\n    /**\r\n     * Changes the number of render targets in this MRT\r\n     * Be careful as it will recreate all the data in the new texture.\r\n     * @param count new texture count\r\n     * @param options Specifies texture types and sampling modes for new textures\r\n     * @param textureNames Specifies the names of the textures (optional)\r\n     */\r\n    public updateCount(count: number, options?: IMultiRenderTargetOptions, textureNames?: string[]) {\r\n        this._multiRenderTargetOptions.textureCount = count;\r\n        this._count = count;\r\n\r\n        const types: number[] = [];\r\n        const samplingModes: number[] = [];\r\n        const useSRGBBuffers: boolean[] = [];\r\n        const formats: number[] = [];\r\n        const targetTypes: number[] = [];\r\n        const faceIndex: number[] = [];\r\n        const layerIndex: number[] = [];\r\n        const layerCounts: number[] = [];\r\n\r\n        this._textureNames = textureNames;\r\n\r\n        this._initTypes(count, types, samplingModes, useSRGBBuffers, formats, targetTypes, faceIndex, layerIndex, layerCounts, options);\r\n        this._multiRenderTargetOptions.types = types;\r\n        this._multiRenderTargetOptions.samplingModes = samplingModes;\r\n        this._multiRenderTargetOptions.useSRGBBuffers = useSRGBBuffers;\r\n        this._multiRenderTargetOptions.formats = formats;\r\n        this._multiRenderTargetOptions.targetTypes = targetTypes;\r\n        this._multiRenderTargetOptions.faceIndex = faceIndex;\r\n        this._multiRenderTargetOptions.layerIndex = layerIndex;\r\n        this._multiRenderTargetOptions.layerCounts = layerCounts;\r\n        this._multiRenderTargetOptions.labels = textureNames;\r\n\r\n        this._rebuild(false, true, textureNames);\r\n    }\r\n\r\n    protected _unbindFrameBuffer(engine: Engine, faceIndex: number): void {\r\n        if (this._renderTarget) {\r\n            engine.unBindMultiColorAttachmentFramebuffer(this._renderTarget, this.isCube, () => {\r\n                this.onAfterRenderObservable.notifyObservers(faceIndex);\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Dispose the render targets and their associated resources\r\n     * @param doNotDisposeInternalTextures if set to true, internal textures won't be disposed (default: false).\r\n     */\r\n    public dispose(doNotDisposeInternalTextures = false): void {\r\n        this._releaseTextures();\r\n        if (!doNotDisposeInternalTextures) {\r\n            this.releaseInternalTextures();\r\n        } else {\r\n            // Prevent internal texture dispose in super.dispose\r\n            this._texture = null;\r\n        }\r\n        super.dispose();\r\n    }\r\n\r\n    /**\r\n     * Release all the underlying texture used as draw buffers (render textures).\r\n     */\r\n    public releaseInternalTextures(): void {\r\n        const internalTextures = this._renderTarget?.textures;\r\n\r\n        if (!internalTextures) {\r\n            return;\r\n        }\r\n\r\n        for (let i = internalTextures.length - 1; i >= 0; i--) {\r\n            this._textures[i]._texture = null;\r\n        }\r\n\r\n        this._renderTarget?.dispose();\r\n        this._renderTarget = null;\r\n    }\r\n}\r\n"]}