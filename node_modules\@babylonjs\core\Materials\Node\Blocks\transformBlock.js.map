{"version": 3, "file": "transformBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/transformBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAEvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAE7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAMxD;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,iBAAiB;IAWjD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAflD;;WAEG;QACI,gBAAW,GAAG,CAAC,CAAC;QAEvB;;WAEG;QACI,gBAAW,GAAG,CAAC,CAAC;QASnB,IAAI,CAAC,MAAM,GAAG,wBAAwB,CAAC,MAAM,CAAC;QAE9C,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,MAAM,CAAC,CAAC;QAC9E,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAE1E,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACjD,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE;gBAC1B,MAAM,YAAY,GAAG,KAAK,CAAC,UAAwB,CAAC;gBAEpD,IAAI,YAAY,CAAC,IAAI,KAAK,QAAQ,IAAI,YAAY,CAAC,IAAI,KAAK,SAAS,EAAE;oBACnE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;iBACxB;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,IAAI,MAAM,CAAC,cAAc,EAAE;YACvB,6BAA6B;YAC7B,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;gBACxB,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClC,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;gBAC5D,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE9C,MAAM,aAAa,GAAG,KAAK,CAAC,oBAAoB,CAAC,GAAG,SAAS,CAAC,sBAAsB,MAAM,CAAC,CAAC;gBAC5F,KAAK,CAAC,iBAAiB,IAAI,QAAQ,aAAa,WAAW,SAAS,CAAC,sBAAsB,MAAM,CAAC;gBAClG,KAAK,CAAC,iBAAiB,IAAI,4BAA4B,CAAC;gBACxD,KAAK,CAAC,iBAAiB,IAAI,GAAG,aAAa,gCAAgC,aAAa,OAAO,CAAC;gBAChG,KAAK,CAAC,iBAAiB,IAAI,UAAU,CAAC;gBACtC,QAAQ,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE;oBAChC,KAAK,qCAAqC,CAAC,OAAO;wBAC9C,KAAK,CAAC,iBAAiB;4BACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;gCACvC,WAAW,aAAa,WAAW,MAAM,CAAC,sBAAsB,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;wBAC1J,MAAM;oBACV,KAAK,qCAAqC,CAAC,OAAO,CAAC;oBACnD,KAAK,qCAAqC,CAAC,MAAM;wBAC7C,KAAK,CAAC,iBAAiB;4BACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,WAAW,aAAa,MAAM,MAAM,CAAC,sBAAsB,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;wBACvJ,MAAM;oBACV;wBACI,KAAK,CAAC,iBAAiB;4BACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,WAAW,aAAa,MAAM,MAAM,CAAC,sBAAsB,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;wBAC3J,MAAM;iBACb;aACJ;iBAAM;gBACH,MAAM,aAAa,GAAG,SAAS,CAAC,sBAAsB,CAAC;gBACvD,QAAQ,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE;oBAChC,KAAK,qCAAqC,CAAC,OAAO;wBAC9C,KAAK,CAAC,iBAAiB;4BACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;gCACvC,MAAM,aAAa,WAAW,MAAM,CAAC,sBAAsB,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;wBACpJ,MAAM;oBACV,KAAK,qCAAqC,CAAC,OAAO,CAAC;oBACnD,KAAK,qCAAqC,CAAC,MAAM;wBAC7C,KAAK,CAAC,iBAAiB;4BACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,aAAa,WAAW,MAAM,CAAC,sBAAsB,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;wBACvJ,MAAM;oBACV;wBACI,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,aAAa,MAAM,MAAM,CAAC,sBAAsB,KAAK,CAAC;wBACjI,MAAM;iBACb;aACJ;YAED,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;gBACvB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB,SAAS,CAAC;aACvH;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QAC9F,aAAa;QACb,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;SAC/C;IACL,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACnD,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QAEnD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,WAAW,GAAG,mBAAmB,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;QACzG,IAAI,CAAC,WAAW,GAAG,mBAAmB,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7G,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,GAAG,GAAG,IAAI,CAAC,iBAAiB,kBAAkB,IAAI,CAAC,WAAW,KAAK,CAAC;QAEhH,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,kBAAkB,IAAI,CAAC,WAAW,KAAK,CAAC;QAE/E,OAAO,UAAU,CAAC;IACtB,CAAC;CACJ;AAED,aAAa,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport type { Scene } from \"../../../scene\";\r\nimport type { InputBlock } from \"./Input/inputBlock\";\r\nimport type { AbstractMesh } from \"../../../Meshes/abstractMesh\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../nodeMaterial\";\r\n\r\n/**\r\n * Block used to transform a vector (2, 3 or 4) with a matrix. It will generate a Vector4\r\n */\r\nexport class TransformBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Defines the value to use to complement W value to transform it to a Vector4\r\n     */\r\n    public complementW = 1;\r\n\r\n    /**\r\n     * Defines the value to use to complement z value to transform it to a Vector4\r\n     */\r\n    public complementZ = 0;\r\n\r\n    /**\r\n     * Creates a new TransformBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.target = NodeMaterialBlockTargets.Vertex;\r\n\r\n        this.registerInput(\"vector\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerInput(\"transform\", NodeMaterialBlockConnectionPointTypes.Matrix);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        this.registerOutput(\"xyz\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n\r\n        this._inputs[0].onConnectionObservable.add((other) => {\r\n            if (other.ownerBlock.isInput) {\r\n                const otherAsInput = other.ownerBlock as InputBlock;\r\n\r\n                if (otherAsInput.name === \"normal\" || otherAsInput.name === \"tangent\") {\r\n                    this.complementW = 0;\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"TransformBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the vector input\r\n     */\r\n    public get vector(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the xyz output component\r\n     */\r\n    public get xyz(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the matrix transform input\r\n     */\r\n    public get transform(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const vector = this.vector;\r\n        const transform = this.transform;\r\n\r\n        if (vector.connectedPoint) {\r\n            // None uniform scaling case.\r\n            if (this.complementW === 0) {\r\n                const comments = `//${this.name}`;\r\n                state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n                state.sharedData.blocksWithDefines.push(this);\r\n\r\n                const transformName = state._getFreeVariableName(`${transform.associatedVariableName}_NUS`);\r\n                state.compilationString += `mat3 ${transformName} = mat3(${transform.associatedVariableName});\\n`;\r\n                state.compilationString += `#ifdef NONUNIFORMSCALING\\n`;\r\n                state.compilationString += `${transformName} = transposeMat3(inverseMat3(${transformName}));\\n`;\r\n                state.compilationString += `#endif\\n`;\r\n                switch (vector.connectedPoint.type) {\r\n                    case NodeMaterialBlockConnectionPointTypes.Vector2:\r\n                        state.compilationString +=\r\n                            this._declareOutput(this.output, state) +\r\n                            ` = vec4(${transformName} * vec3(${vector.associatedVariableName}, ${this._writeFloat(this.complementZ)}), ${this._writeFloat(this.complementW)});\\n`;\r\n                        break;\r\n                    case NodeMaterialBlockConnectionPointTypes.Vector3:\r\n                    case NodeMaterialBlockConnectionPointTypes.Color3:\r\n                        state.compilationString +=\r\n                            this._declareOutput(this.output, state) + ` = vec4(${transformName} * ${vector.associatedVariableName}, ${this._writeFloat(this.complementW)});\\n`;\r\n                        break;\r\n                    default:\r\n                        state.compilationString +=\r\n                            this._declareOutput(this.output, state) + ` = vec4(${transformName} * ${vector.associatedVariableName}.xyz, ${this._writeFloat(this.complementW)});\\n`;\r\n                        break;\r\n                }\r\n            } else {\r\n                const transformName = transform.associatedVariableName;\r\n                switch (vector.connectedPoint.type) {\r\n                    case NodeMaterialBlockConnectionPointTypes.Vector2:\r\n                        state.compilationString +=\r\n                            this._declareOutput(this.output, state) +\r\n                            ` = ${transformName} * vec4(${vector.associatedVariableName}, ${this._writeFloat(this.complementZ)}, ${this._writeFloat(this.complementW)});\\n`;\r\n                        break;\r\n                    case NodeMaterialBlockConnectionPointTypes.Vector3:\r\n                    case NodeMaterialBlockConnectionPointTypes.Color3:\r\n                        state.compilationString +=\r\n                            this._declareOutput(this.output, state) + ` = ${transformName} * vec4(${vector.associatedVariableName}, ${this._writeFloat(this.complementW)});\\n`;\r\n                        break;\r\n                    default:\r\n                        state.compilationString += this._declareOutput(this.output, state) + ` = ${transformName} * ${vector.associatedVariableName};\\n`;\r\n                        break;\r\n                }\r\n            }\r\n\r\n            if (this.xyz.hasEndpoints) {\r\n                state.compilationString += this._declareOutput(this.xyz, state) + ` = ${this.output.associatedVariableName}.xyz;\\n`;\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Update defines for shader compilation\r\n     * @param mesh defines the mesh to be rendered\r\n     * @param nodeMaterial defines the node material requesting the update\r\n     * @param defines defines the material defines to update\r\n     */\r\n    public prepareDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        // Do nothing\r\n        if (mesh.nonUniformScaling) {\r\n            defines.setValue(\"NONUNIFORMSCALING\", true);\r\n        }\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.complementZ = this.complementZ;\r\n        serializationObject.complementW = this.complementW;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.complementZ = serializationObject.complementZ !== undefined ? serializationObject.complementZ : 0.0;\r\n        this.complementW = serializationObject.complementW !== undefined ? serializationObject.complementW : 1.0;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode() + `${this._codeVariableName}.complementZ = ${this.complementZ};\\n`;\r\n\r\n        codeString += `${this._codeVariableName}.complementW = ${this.complementW};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.TransformBlock\", TransformBlock);\r\n"]}