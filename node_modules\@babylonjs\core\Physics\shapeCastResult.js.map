{"version": 3, "file": "shapeCastResult.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Physics/shapeCastResult.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,aAAa;IAAlD;;QACY,iBAAY,GAAW,CAAC,CAAC;IAgBrC,CAAC;IAdG;;OAEG;IACH,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,QAAgB;QAClC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;IACjC,CAAC;CACJ", "sourcesContent": ["import { CastingResult } from \"./castingResult\";\r\n\r\n/**\r\n * Class representing a contact point produced in a shape cast\r\n */\r\nexport class ShapeCastResult extends CastingResult {\r\n    private _hitFraction: number = 0;\r\n\r\n    /**\r\n     * Gets the hit fraction along the casting ray\r\n     */\r\n    get hitFraction(): number {\r\n        return this._hitFraction;\r\n    }\r\n\r\n    /**\r\n     * Sets the hit fraction along the casting ray\r\n     * @param fraction\r\n     */\r\n    public setHitFraction(fraction: number) {\r\n        this._hitFraction = fraction;\r\n    }\r\n}\r\n"]}