{"version": 3, "file": "webXRInputSource.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/XR/webXRInputSource.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AACtD,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAI3D,OAAO,EAAE,4BAA4B,EAAE,MAAM,iDAAiD,CAAC;AAC/F,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAItC,IAAI,OAAO,GAAG,CAAC,CAAC;AA2BhB;;GAEG;AACH,MAAM,OAAO,gBAAgB;IA0CzB;;;;;;OAMG;IACH,YACY,MAAa;IACrB,sDAAsD;IAC/C,WAA0B,EACzB,WAAoC,EAAE;QAHtC,WAAM,GAAN,MAAM,CAAO;QAEd,gBAAW,GAAX,WAAW,CAAe;QACzB,aAAQ,GAAR,QAAQ,CAA8B;QApD1C,eAAU,GAAG,IAAI,OAAO,EAAE,CAAC;QAE3B,cAAS,GAAG,KAAK,CAAC;QAY1B;;;;WAIG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAoB,CAAC;QAChE;;;;WAIG;QACI,2BAAsB,GAAG,IAAI,UAAU,EAAgB,CAAC;QAC/D;;WAEG;QACI,qCAAgC,GAAG,IAAI,UAAU,EAAiC,CAAC;QAyBtF,IAAI,CAAC,SAAS,GAAG,cAAc,OAAO,EAAE,IAAI,WAAW,CAAC,aAAa,IAAI,WAAW,CAAC,UAAU,EAAE,CAAC;QAElG,IAAI,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC,GAAG,IAAI,CAAC,SAAS,UAAU,EAAE,MAAM,CAAC,CAAC;QACrE,IAAI,CAAC,OAAO,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;QAEnD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,IAAI,GAAG,IAAI,YAAY,CAAC,GAAG,IAAI,CAAC,SAAS,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACpE,IAAI,CAAC,IAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;SACnD;QAED,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAEzE,mEAAmE;QACnE,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,KAAK,iBAAiB,EAAE;YAClF,4BAA4B,CAAC,8BAA8B,CAAC,WAAW,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC,IAAI,CACvH,CAAC,gBAAgB,EAAE,EAAE;gBACjB,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;gBACzC,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;gBACxE,8BAA8B;gBAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,EAAE;oBAC3F,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;wBAC/C,IAAI,OAAO,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;4BACpE,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE;gCAChC,yBAAyB;gCACzB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;gCACjF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAiB,CAAC,CAAC,CAAC;6BACrI;4BACD,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;4BAC5E,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC;4BAClE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,gCAAgC,CAAC;yBAC7F;wBACD,6DAA6D;wBAC7D,IAAI,IAAI,CAAC,SAAS,EAAE;4BAChB,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,CAAC;yBACpC;oBACL,CAAC,CAAC,CAAC;iBACN;YACL,CAAC,EACD,GAAG,EAAE;gBACD,KAAK,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;YAC9F,CAAC,CACJ,CAAC;SACL;IACL,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC3B;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;SACnC;QACD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;QAC9C,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,MAAW,EAAE,kBAA2B,KAAK;QACxE,MAAM,MAAM,GAAG,eAAe,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;QACvE,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACzF,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC7B,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAChD,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;;;;;OAMG;IACI,iBAAiB,CAAC,OAAgB,EAAE,cAAgC,EAAE,QAAqB,EAAE,gBAAqC;QACrI,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;QAC9E,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,0BAA0B;QAC1B,IAAI,IAAI,EAAE;YACN,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;YACpC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;YACjG,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;YAC/C,IAAI,CAAC,OAAO,CAAC,kBAAmB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACjG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBACnC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC9B,IAAI,CAAC,OAAO,CAAC,kBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACzC,IAAI,CAAC,OAAO,CAAC,kBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;aAC5C;YACD,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;YACtC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;SACpE;QAED,oCAAoC;QACpC,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,EAAE;YACzC,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YACzE,IAAI,IAAI,EAAE;gBACN,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;gBACpC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;gBAC/C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;gBAC9F,IAAI,CAAC,IAAI,CAAC,kBAAmB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;gBAC9F,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;oBACnC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC3B,IAAI,CAAC,IAAI,CAAC,kBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBACtC,IAAI,CAAC,IAAI,CAAC,kBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;iBACzC;aACJ;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;SACjE;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,kEAAkE;YAClE,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;SACpD;IACL,CAAC;CACJ", "sourcesContent": ["import { Observable } from \"../Misc/observable\";\r\nimport { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Quaternion, Vector3 } from \"../Maths/math.vector\";\r\nimport type { Ray } from \"../Culling/ray\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { WebXRAbstractMotionController } from \"./motionController/webXRAbstractMotionController\";\r\nimport { WebXRMotionControllerManager } from \"./motionController/webXRMotionControllerManager\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport type { WebXRCamera } from \"./webXRCamera\";\r\nimport type { WebXRSessionManager } from \"./webXRSessionManager\";\r\n\r\nlet idCount = 0;\r\n\r\n/**\r\n * Configuration options for the WebXR controller creation\r\n */\r\nexport interface IWebXRControllerOptions {\r\n    /**\r\n     * Should the controller mesh be animated when a user interacts with it\r\n     * The pressed buttons / thumbstick and touchpad animations will be disabled\r\n     */\r\n    disableMotionControllerAnimation?: boolean;\r\n    /**\r\n     * Do not load the controller mesh, in case a different mesh needs to be loaded.\r\n     */\r\n    doNotLoadControllerMesh?: boolean;\r\n    /**\r\n     * Force a specific controller type for this controller.\r\n     * This can be used when creating your own profile or when testing different controllers\r\n     */\r\n    forceControllerProfile?: string;\r\n    /**\r\n     * Defines a rendering group ID for meshes that will be loaded.\r\n     * This is for the default controllers only.\r\n     */\r\n    renderingGroupId?: number;\r\n}\r\n\r\n/**\r\n * Represents an XR controller\r\n */\r\nexport class WebXRInputSource {\r\n    private _tmpVector = new Vector3();\r\n    private _uniqueId: string;\r\n    private _disposed = false;\r\n\r\n    /**\r\n     * Represents the part of the controller that is held. This may not exist if the controller is the head mounted display itself, if that's the case only the pointer from the head will be available\r\n     */\r\n    public grip?: AbstractMesh;\r\n    /**\r\n     * If available, this is the gamepad object related to this controller.\r\n     * Using this object it is possible to get click events and trackpad changes of the\r\n     * webxr controller that is currently being used.\r\n     */\r\n    public motionController?: WebXRAbstractMotionController;\r\n    /**\r\n     * Event that fires when the controller is removed/disposed.\r\n     * The object provided as event data is this controller, after associated assets were disposed.\r\n     * uniqueId is still available.\r\n     */\r\n    public onDisposeObservable = new Observable<WebXRInputSource>();\r\n    /**\r\n     * Will be triggered when the mesh associated with the motion controller is done loading.\r\n     * It is also possible that this will never trigger (!) if no mesh was loaded, or if the developer decides to load a different mesh\r\n     * A shortened version of controller -> motion controller -> on mesh loaded.\r\n     */\r\n    public onMeshLoadedObservable = new Observable<AbstractMesh>();\r\n    /**\r\n     * Observers registered here will trigger when a motion controller profile was assigned to this xr controller\r\n     */\r\n    public onMotionControllerInitObservable = new Observable<WebXRAbstractMotionController>();\r\n    /**\r\n     * Pointer which can be used to select objects or attach a visible laser to\r\n     */\r\n    public pointer: AbstractMesh;\r\n\r\n    /**\r\n     * The last XRPose the was calculated on the current XRFrame\r\n     * @internal\r\n     */\r\n    public _lastXRPose?: XRPose;\r\n\r\n    /**\r\n     * Creates the input source object\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/webXR/webXRInputControllerSupport\r\n     * @param _scene the scene which the controller should be associated to\r\n     * @param inputSource the underlying input source for the controller\r\n     * @param _options options for this controller creation\r\n     */\r\n    constructor(\r\n        private _scene: Scene,\r\n        /** The underlying input source for the controller  */\r\n        public inputSource: XRInputSource,\r\n        private _options: IWebXRControllerOptions = {}\r\n    ) {\r\n        this._uniqueId = `controller-${idCount++}-${inputSource.targetRayMode}-${inputSource.handedness}`;\r\n\r\n        this.pointer = new AbstractMesh(`${this._uniqueId}-pointer`, _scene);\r\n        this.pointer.rotationQuaternion = new Quaternion();\r\n\r\n        if (this.inputSource.gripSpace) {\r\n            this.grip = new AbstractMesh(`${this._uniqueId}-grip`, this._scene);\r\n            this.grip.rotationQuaternion = new Quaternion();\r\n        }\r\n\r\n        this._tmpVector.set(0, 0, this._scene.useRightHandedSystem ? -1.0 : 1.0);\r\n\r\n        // for now only load motion controllers if gamepad object available\r\n        if (this.inputSource.gamepad && this.inputSource.targetRayMode === \"tracked-pointer\") {\r\n            WebXRMotionControllerManager.GetMotionControllerWithXRInput(inputSource, _scene, this._options.forceControllerProfile).then(\r\n                (motionController) => {\r\n                    this.motionController = motionController;\r\n                    this.onMotionControllerInitObservable.notifyObservers(motionController);\r\n                    // should the model be loaded?\r\n                    if (!this._options.doNotLoadControllerMesh && !this.motionController._doNotLoadControllerMesh) {\r\n                        this.motionController.loadModel().then((success) => {\r\n                            if (success && this.motionController && this.motionController.rootMesh) {\r\n                                if (this._options.renderingGroupId) {\r\n                                    // anything other than 0?\r\n                                    this.motionController.rootMesh.renderingGroupId = this._options.renderingGroupId;\r\n                                    this.motionController.rootMesh.getChildMeshes(false).forEach((mesh) => (mesh.renderingGroupId = this._options.renderingGroupId!));\r\n                                }\r\n                                this.onMeshLoadedObservable.notifyObservers(this.motionController.rootMesh);\r\n                                this.motionController.rootMesh.parent = this.grip || this.pointer;\r\n                                this.motionController.disableAnimation = !!this._options.disableMotionControllerAnimation;\r\n                            }\r\n                            // make sure to dispose is the controller is already disposed\r\n                            if (this._disposed) {\r\n                                this.motionController?.dispose();\r\n                            }\r\n                        });\r\n                    }\r\n                },\r\n                () => {\r\n                    Tools.Warn(`Could not find a matching motion controller for the registered input source`);\r\n                }\r\n            );\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get this controllers unique id\r\n     */\r\n    public get uniqueId() {\r\n        return this._uniqueId;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the object\r\n     */\r\n    public dispose() {\r\n        if (this.grip) {\r\n            this.grip.dispose(true);\r\n        }\r\n        if (this.motionController) {\r\n            this.motionController.dispose();\r\n        }\r\n        this.pointer.dispose(true);\r\n        this.onMotionControllerInitObservable.clear();\r\n        this.onMeshLoadedObservable.clear();\r\n        this.onDisposeObservable.notifyObservers(this);\r\n        this.onDisposeObservable.clear();\r\n        this._disposed = true;\r\n    }\r\n\r\n    /**\r\n     * Gets a world space ray coming from the pointer or grip\r\n     * @param result the resulting ray\r\n     * @param gripIfAvailable use the grip mesh instead of the pointer, if available\r\n     */\r\n    public getWorldPointerRayToRef(result: Ray, gripIfAvailable: boolean = false) {\r\n        const object = gripIfAvailable && this.grip ? this.grip : this.pointer;\r\n        Vector3.TransformNormalToRef(this._tmpVector, object.getWorldMatrix(), result.direction);\r\n        result.direction.normalize();\r\n        result.origin.copyFrom(object.absolutePosition);\r\n        result.length = 1000;\r\n    }\r\n\r\n    /**\r\n     * Updates the controller pose based on the given XRFrame\r\n     * @param xrFrame xr frame to update the pose with\r\n     * @param referenceSpace reference space to use\r\n     * @param xrCamera the xr camera, used for parenting\r\n     * @param xrSessionManager the session manager used to get the world reference system\r\n     */\r\n    public updateFromXRFrame(xrFrame: XRFrame, referenceSpace: XRReferenceSpace, xrCamera: WebXRCamera, xrSessionManager: WebXRSessionManager) {\r\n        const pose = xrFrame.getPose(this.inputSource.targetRaySpace, referenceSpace);\r\n        this._lastXRPose = pose;\r\n\r\n        // Update the pointer mesh\r\n        if (pose) {\r\n            const pos = pose.transform.position;\r\n            this.pointer.position.set(pos.x, pos.y, pos.z).scaleInPlace(xrSessionManager.worldScalingFactor);\r\n            const orientation = pose.transform.orientation;\r\n            this.pointer.rotationQuaternion!.set(orientation.x, orientation.y, orientation.z, orientation.w);\r\n            if (!this._scene.useRightHandedSystem) {\r\n                this.pointer.position.z *= -1;\r\n                this.pointer.rotationQuaternion!.z *= -1;\r\n                this.pointer.rotationQuaternion!.w *= -1;\r\n            }\r\n            this.pointer.parent = xrCamera.parent;\r\n            this.pointer.scaling.setAll(xrSessionManager.worldScalingFactor);\r\n        }\r\n\r\n        // Update the grip mesh if it exists\r\n        if (this.inputSource.gripSpace && this.grip) {\r\n            const pose = xrFrame.getPose(this.inputSource.gripSpace, referenceSpace);\r\n            if (pose) {\r\n                const pos = pose.transform.position;\r\n                const orientation = pose.transform.orientation;\r\n                this.grip.position.set(pos.x, pos.y, pos.z).scaleInPlace(xrSessionManager.worldScalingFactor);\r\n                this.grip.rotationQuaternion!.set(orientation.x, orientation.y, orientation.z, orientation.w);\r\n                if (!this._scene.useRightHandedSystem) {\r\n                    this.grip.position.z *= -1;\r\n                    this.grip.rotationQuaternion!.z *= -1;\r\n                    this.grip.rotationQuaternion!.w *= -1;\r\n                }\r\n            }\r\n            this.grip.parent = xrCamera.parent;\r\n            this.grip.scaling.setAll(xrSessionManager.worldScalingFactor);\r\n        }\r\n        if (this.motionController) {\r\n            // either update buttons only or also position, if in gamepad mode\r\n            this.motionController.updateFromXRFrame(xrFrame);\r\n        }\r\n    }\r\n}\r\n"]}