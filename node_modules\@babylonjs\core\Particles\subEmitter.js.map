{"version": 3, "file": "subEmitter.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Particles/subEmitter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAE/C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAM7C;;GAEG;AACH,MAAM,CAAN,IAAY,cASX;AATD,WAAY,cAAc;IACtB;;OAEG;IACH,2DAAQ,CAAA;IACR;;OAEG;IACH,iDAAG,CAAA;AACP,CAAC,EATW,cAAc,KAAd,cAAc,QASzB;AAED;;GAEG;AACH,MAAM,OAAO,UAAU;IAenB;;;OAGG;IACH;IACI;;OAEG;IACI,cAA8B;QAA9B,mBAAc,GAAd,cAAc,CAAgB;QAtBzC;;WAEG;QACI,SAAI,GAAG,cAAc,CAAC,GAAG,CAAC;QACjC;;;WAGG;QACI,qBAAgB,GAAG,KAAK,CAAC;QAChC;;WAEG;QACI,4BAAuB,GAAG,CAAC,CAAC;QAY/B,6CAA6C;QAC7C,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,CAAgB,cAAc,CAAC,OAAQ,CAAC,OAAO,EAAE;YAC5E,MAAM,aAAa,GAAG,QAAQ,CAAC,sBAAsB,CAAC,CAAC;YACvD,cAAc,CAAC,OAAO,GAAG,IAAI,aAAa,CAAC,yBAAyB,EAAE,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjG,cAAc,CAAC,wBAAwB,GAAG,IAAI,CAAC;SAClD;IACL,CAAC;IACD;;;OAGG;IACI,KAAK;QACR,wBAAwB;QACxB,IAAI,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;QAC1C,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;SAC3B;aAAM,IAAI,OAAO,YAAY,OAAO,EAAE;YACnC,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;SAC7B;aAAM,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;YACtD,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC;YAC/C,OAAO,GAAG,IAAI,aAAa,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnD,OAAgB,CAAC,SAAS,GAAG,KAAK,CAAC;SACvC;QACD,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;QAE3F,mBAAmB;QACnB,KAAK,CAAC,cAAc,CAAC,IAAI,IAAI,OAAO,CAAC;QACrC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC/C,KAAK,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAE7D,KAAK,CAAC,cAAc,CAAC,wBAAwB,GAAG,IAAI,CAAC;QACrD,KAAK,CAAC,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1C,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,mBAA4B,KAAK;QAC9C,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC7D,mBAAmB,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAC3E,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAErF,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,6DAA6D;IACtD,MAAM,CAAC,oBAAoB,CAAC,MAAW,EAAE,aAAiC,EAAE,OAAe,EAAE,UAAU,GAAG,KAAK;QAClH,MAAM,WAAW,CAAC,eAAe,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,mBAAwB,EAAE,aAAiC,EAAE,OAAe;QAC5F,MAAM,MAAM,GAAG,mBAAmB,CAAC,cAAc,CAAC;QAClD,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,oBAAoB,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QACzG,UAAU,CAAC,IAAI,GAAG,mBAAmB,CAAC,IAAI,CAAC;QAC3C,UAAU,CAAC,gBAAgB,GAAG,mBAAmB,CAAC,gBAAgB,CAAC;QACnE,UAAU,CAAC,uBAAuB,GAAG,mBAAmB,CAAC,uBAAuB,CAAC;QACjF,UAAU,CAAC,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC;QAE/C,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,mCAAmC;IAC5B,OAAO;QACV,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;IAClC,CAAC;CACJ", "sourcesContent": ["import { Vector3 } from \"../Maths/math.vector\";\r\nimport { _WarnImport } from \"../Misc/devTools\";\r\nimport type { ThinEngine } from \"../Engines/thinEngine\";\r\nimport { GetClass } from \"../Misc/typeStore\";\r\n\r\nimport type { Scene } from \"../scene\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { ParticleSystem } from \"../Particles/particleSystem\";\r\n\r\n/**\r\n * Type of sub emitter\r\n */\r\nexport enum SubEmitterType {\r\n    /**\r\n     * Attached to the particle over it's lifetime\r\n     */\r\n    ATTACHED,\r\n    /**\r\n     * Created when the particle dies\r\n     */\r\n    END,\r\n}\r\n\r\n/**\r\n * Sub emitter class used to emit particles from an existing particle\r\n */\r\nexport class SubEmitter {\r\n    /**\r\n     * Type of the submitter (Default: END)\r\n     */\r\n    public type = SubEmitterType.END;\r\n    /**\r\n     * If the particle should inherit the direction from the particle it's attached to. (+Y will face the direction the particle is moving) (Default: false)\r\n     * Note: This only is supported when using an emitter of type Mesh\r\n     */\r\n    public inheritDirection = false;\r\n    /**\r\n     * How much of the attached particles speed should be added to the sub emitted particle (default: 0)\r\n     */\r\n    public inheritedVelocityAmount = 0;\r\n\r\n    /**\r\n     * Creates a sub emitter\r\n     * @param particleSystem the particle system to be used by the sub emitter\r\n     */\r\n    constructor(\r\n        /**\r\n         * the particle system to be used by the sub emitter\r\n         */\r\n        public particleSystem: ParticleSystem\r\n    ) {\r\n        // Create mesh as emitter to support rotation\r\n        if (!particleSystem.emitter || !(<AbstractMesh>particleSystem.emitter).dispose) {\r\n            const internalClass = GetClass(\"BABYLON.AbstractMesh\");\r\n            particleSystem.emitter = new internalClass(\"SubemitterSystemEmitter\", particleSystem.getScene());\r\n            particleSystem._disposeEmitterOnDispose = true;\r\n        }\r\n    }\r\n    /**\r\n     * Clones the sub emitter\r\n     * @returns the cloned sub emitter\r\n     */\r\n    public clone(): SubEmitter {\r\n        // Clone particle system\r\n        let emitter = this.particleSystem.emitter;\r\n        if (!emitter) {\r\n            emitter = new Vector3();\r\n        } else if (emitter instanceof Vector3) {\r\n            emitter = emitter.clone();\r\n        } else if (emitter.getClassName().indexOf(\"Mesh\") !== -1) {\r\n            const internalClass = GetClass(\"BABYLON.Mesh\");\r\n            emitter = new internalClass(\"\", emitter.getScene());\r\n            (emitter! as any).isVisible = false;\r\n        }\r\n        const clone = new SubEmitter(this.particleSystem.clone(this.particleSystem.name, emitter));\r\n\r\n        // Clone properties\r\n        clone.particleSystem.name += \"Clone\";\r\n        clone.type = this.type;\r\n        clone.inheritDirection = this.inheritDirection;\r\n        clone.inheritedVelocityAmount = this.inheritedVelocityAmount;\r\n\r\n        clone.particleSystem._disposeEmitterOnDispose = true;\r\n        clone.particleSystem.disposeOnStop = true;\r\n        return clone;\r\n    }\r\n\r\n    /**\r\n     * Serialize current object to a JSON object\r\n     * @param serializeTexture defines if the texture must be serialized as well\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(serializeTexture: boolean = false): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.type = this.type;\r\n        serializationObject.inheritDirection = this.inheritDirection;\r\n        serializationObject.inheritedVelocityAmount = this.inheritedVelocityAmount;\r\n        serializationObject.particleSystem = this.particleSystem.serialize(serializeTexture);\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static _ParseParticleSystem(system: any, sceneOrEngine: Scene | ThinEngine, rootUrl: string, doNotStart = false): ParticleSystem {\r\n        throw _WarnImport(\"ParseParticle\");\r\n    }\r\n\r\n    /**\r\n     * Creates a new SubEmitter from a serialized JSON version\r\n     * @param serializationObject defines the JSON object to read from\r\n     * @param sceneOrEngine defines the hosting scene or the hosting engine\r\n     * @param rootUrl defines the rootUrl for data loading\r\n     * @returns a new SubEmitter\r\n     */\r\n    public static Parse(serializationObject: any, sceneOrEngine: Scene | ThinEngine, rootUrl: string): SubEmitter {\r\n        const system = serializationObject.particleSystem;\r\n        const subEmitter = new SubEmitter(SubEmitter._ParseParticleSystem(system, sceneOrEngine, rootUrl, true));\r\n        subEmitter.type = serializationObject.type;\r\n        subEmitter.inheritDirection = serializationObject.inheritDirection;\r\n        subEmitter.inheritedVelocityAmount = serializationObject.inheritedVelocityAmount;\r\n        subEmitter.particleSystem._isSubEmitter = true;\r\n\r\n        return subEmitter;\r\n    }\r\n\r\n    /** Release associated resources */\r\n    public dispose() {\r\n        this.particleSystem.dispose();\r\n    }\r\n}\r\n"]}