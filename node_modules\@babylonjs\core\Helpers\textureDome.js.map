{"version": 3, "file": "textureDome.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Helpers/textureDome.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,kBAAkB,EAAE,MAAM,4CAA4C,CAAC;AAChF,OAAO,EAAE,YAAY,EAAE,MAAM,kCAAkC,CAAC;AAGhE,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAIrC;;;;;GAKG;AACH,MAAM,OAAgB,WAA+B,SAAQ,aAAa;IAwBtE;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,OAAO,CAAC,UAAa;QAC5B,IAAI,IAAI,CAAC,QAAQ,KAAK,UAAU,EAAE;YAC9B,OAAO;SACV;QACD,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;QAC3B,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;YAChD,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;YAChD,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;SACjD;aAAM;YACH,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,OAAO,CAAC,mCAAmC,CAAC,CAAC,sBAAsB;YACnG,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;YAChD,IAAI,CAAC,SAAS,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;SACpD;QACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAWD;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAOD;;;OAGG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;IACxC,CAAC;IACD,IAAW,aAAa,CAAC,KAAa;QAClC,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,KAAK,CAAC;IACzC,CAAC;IAGD;;;;;OAKG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IACD;;;;;OAKG;IACH,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE;YAC7B,OAAO;SACV;QAED,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ,CAAC,OAAgB;QAChC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;QACzB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ,CAAC,OAAgB;QAChC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;QACzB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAeD;;;;;;;;;;;;;;;;;;;OAmBG;IACH,YACI,IAAY,EACZ,mBAAyD,EACzD,OAaC,EACD,KAAY;IACZ,gEAAgE;IACtD,UAAiE,IAAI;QAE/E,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAFT,YAAO,GAAP,OAAO,CAA8D;QAvL3E,cAAS,GAAY,KAAK,CAAC;QAC3B,cAAS,GAAY,KAAK,CAAC;QAEzB,sBAAiB,GAAG,KAAK,CAAC;QAkE1B,iBAAY,GAAG,WAAW,CAAC,eAAe,CAAC;QA8DrD;;WAEG;QACK,kCAA6B,GAA+B,IAAI,CAAC;QACzE;;WAEG;QACI,0BAAqB,GAAG,IAAI,UAAU,EAAU,CAAC;QACxD;;WAEG;QACI,qBAAgB,GAAG,IAAI,UAAU,EAAQ,CAAC;QA6C7C,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAExB,iCAAiC;QACjC,IAAI,GAAG,IAAI,IAAI,aAAa,CAAC;QAC7B,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACnE,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACnD,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACrF,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACzE,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAW,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAE7G,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,EAAE;YACxC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACjC;aAAM;YACH,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,gBAAgB,CAAC;SACrD;QAED,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;YACnC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;SAC9B;QAED,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACf,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,GAAG,OAAO,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;SAChK;aAAM;YACH,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;SAC7B;QACD,qBAAqB;QACrB,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,kBAAkB,CAAC,IAAI,GAAG,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;QACtF,QAAQ,CAAC,qBAAqB,GAAG,IAAI,CAAC;QACtC,QAAQ,CAAC,aAAa,GAAG,GAAG,CAAC;QAC7B,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC;QAEhC,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACvE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,iBAAiB;QACjB,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;QAEzB,mFAAmF;QACnF,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE,eAAe,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QAC9J,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAChD,kHAAkH;QAClH,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;QACvC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;QACxC,8CAA8C;QAC9C,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;QAExC,SAAS;QACT,IAAI,CAAC,QAAQ,CAAC,yBAAyB,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,EAAE;YACxC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,mBAAmB;QACnB,IAAI,OAAO,CAAC,WAAW,IAAI,KAAK,CAAC,YAAY,EAAE;YAC3C,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC;YAElC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;YAClC,MAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;YAC3E,SAAS,CAAC,SAAS,EAAE,CAAC;YAEtB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;SAChE;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAIS,kBAAkB,CAAC,KAAa;QACtC,IAAI,CAAC,MAAM,CAAC,8BAA8B,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACtF,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;QAEvB,QAAQ,KAAK,EAAE;YACX,KAAK,WAAW,CAAC,eAAe;gBAC5B,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;oBACzB,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;iBAC9B;gBACD,MAAM;YACV,KAAK,WAAW,CAAC,eAAe,CAAC,CAAC;gBAC9B,+DAA+D;gBAC/D,qDAAqD;gBACrD,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;gBACtD,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBAC/C,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,MAAM,CAAC,8BAA8B,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;oBAC3F,IAAI,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;oBACzC,IAAI,IAAI,CAAC,SAAS,EAAE;wBAChB,aAAa,GAAG,CAAC,aAAa,CAAC;qBAClC;oBACD,IAAI,aAAa,EAAE;wBACf,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,WAAW,CAAC;qBACvC;yBAAM;wBACH,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,UAAU,CAAC;qBACtC;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM;aACT;YACD,KAAK,WAAW,CAAC,cAAc;gBAC3B,+DAA+D;gBAC/D,qDAAqD;gBACrD,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;gBACtD,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,MAAM,CAAC,8BAA8B,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;oBAC3F,IAAI,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;oBACzC,iEAAiE;oBACjE,IAAI,IAAI,CAAC,SAAS,EAAE;wBAChB,aAAa,GAAG,CAAC,aAAa,CAAC;qBAClC;oBACD,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;gBACtD,CAAC,CAAC,CAAC;gBACH,MAAM;SACb;IACL,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,YAAsB,EAAE,0BAA0B,GAAG,KAAK;QACrE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACxB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACrB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAEzB,IAAI,CAAC,MAAM,CAAC,8BAA8B,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAE9B,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,0BAA0B,CAAC,CAAC;IAC5D,CAAC;;AAnVD;;GAEG;AACoB,2BAAe,GAAG,CAAC,AAAJ,CAAK;AAC3C;;GAEG;AACoB,0BAAc,GAAG,CAAC,AAAJ,CAAK;AAC1C;;GAEG;AACoB,2BAAe,GAAG,CAAC,AAAJ,CAAK", "sourcesContent": ["import type { Scene } from \"../scene\";\r\nimport { TransformNode } from \"../Meshes/transformNode\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { BackgroundMaterial } from \"../Materials/Background/backgroundMaterial\";\r\nimport { CreateSphere } from \"../Meshes/Builders/sphereBuilder\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport { Axis } from \"../Maths/math\";\r\n\r\nimport type { Camera } from \"../Cameras/camera\";\r\n\r\n/**\r\n * Display a 360/180 degree texture on an approximately spherical surface, useful for VR applications or skyboxes.\r\n * As a subclass of TransformNode, this allow parenting to the camera or multiple textures with different locations in the scene.\r\n * This class achieves its effect with a Texture and a correctly configured BackgroundMaterial on an inverted sphere.\r\n * Potential additions to this helper include zoom and and non-infinite distance rendering effects.\r\n */\r\nexport abstract class TextureDome<T extends Texture> extends TransformNode {\r\n    /**\r\n     * Define the source as a Monoscopic panoramic 360/180.\r\n     */\r\n    public static readonly MODE_MONOSCOPIC = 0;\r\n    /**\r\n     * Define the source as a Stereoscopic TopBottom/OverUnder panoramic 360/180.\r\n     */\r\n    public static readonly MODE_TOPBOTTOM = 1;\r\n    /**\r\n     * Define the source as a Stereoscopic Side by Side panoramic 360/180.\r\n     */\r\n    public static readonly MODE_SIDEBYSIDE = 2;\r\n\r\n    private _halfDome: boolean = false;\r\n    private _crossEye: boolean = false;\r\n\r\n    protected _useDirectMapping = false;\r\n\r\n    /**\r\n     * The texture being displayed on the sphere\r\n     */\r\n    protected _texture: T;\r\n\r\n    /**\r\n     * Gets the texture being displayed on the sphere\r\n     */\r\n    public get texture(): T {\r\n        return this._texture;\r\n    }\r\n\r\n    /**\r\n     * Sets the texture being displayed on the sphere\r\n     */\r\n    public set texture(newTexture: T) {\r\n        if (this._texture === newTexture) {\r\n            return;\r\n        }\r\n        this._texture = newTexture;\r\n        if (this._useDirectMapping) {\r\n            this._texture.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n            this._texture.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n            this._material.diffuseTexture = this._texture;\r\n        } else {\r\n            this._texture.coordinatesMode = Texture.FIXED_EQUIRECTANGULAR_MIRRORED_MODE; // matches orientation\r\n            this._texture.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n            this._material.reflectionTexture = this._texture;\r\n        }\r\n        this._changeTextureMode(this._textureMode);\r\n    }\r\n\r\n    /**\r\n     * The skybox material\r\n     */\r\n    protected _material: BackgroundMaterial;\r\n\r\n    /**\r\n     * The surface used for the dome\r\n     */\r\n    protected _mesh: Mesh;\r\n    /**\r\n     * Gets the mesh used for the dome.\r\n     */\r\n    public get mesh(): Mesh {\r\n        return this._mesh;\r\n    }\r\n\r\n    /**\r\n     * A mesh that will be used to mask the back of the dome in case it is a 180 degree movie.\r\n     */\r\n    private _halfDomeMask: Mesh;\r\n\r\n    /**\r\n     * The current fov(field of view) multiplier, 0.0 - 2.0. Defaults to 1.0. Lower values \"zoom in\" and higher values \"zoom out\".\r\n     * Also see the options.resolution property.\r\n     */\r\n    public get fovMultiplier(): number {\r\n        return this._material.fovMultiplier;\r\n    }\r\n    public set fovMultiplier(value: number) {\r\n        this._material.fovMultiplier = value;\r\n    }\r\n\r\n    protected _textureMode = TextureDome.MODE_MONOSCOPIC;\r\n    /**\r\n     * Gets or set the current texture mode for the texture. It can be:\r\n     * * TextureDome.MODE_MONOSCOPIC : Define the texture source as a Monoscopic panoramic 360.\r\n     * * TextureDome.MODE_TOPBOTTOM  : Define the texture source as a Stereoscopic TopBottom/OverUnder panoramic 360.\r\n     * * TextureDome.MODE_SIDEBYSIDE : Define the texture source as a Stereoscopic Side by Side panoramic 360.\r\n     */\r\n    public get textureMode(): number {\r\n        return this._textureMode;\r\n    }\r\n    /**\r\n     * Sets the current texture mode for the texture. It can be:\r\n     * * TextureDome.MODE_MONOSCOPIC : Define the texture source as a Monoscopic panoramic 360.\r\n     * * TextureDome.MODE_TOPBOTTOM  : Define the texture source as a Stereoscopic TopBottom/OverUnder panoramic 360.\r\n     * * TextureDome.MODE_SIDEBYSIDE : Define the texture source as a Stereoscopic Side by Side panoramic 360.\r\n     */\r\n    public set textureMode(value: number) {\r\n        if (this._textureMode === value) {\r\n            return;\r\n        }\r\n\r\n        this._changeTextureMode(value);\r\n    }\r\n\r\n    /**\r\n     * Is it a 180 degrees dome (half dome) or 360 texture (full dome)\r\n     */\r\n    public get halfDome(): boolean {\r\n        return this._halfDome;\r\n    }\r\n\r\n    /**\r\n     * Set the halfDome mode. If set, only the front (180 degrees) will be displayed and the back will be blacked out.\r\n     */\r\n    public set halfDome(enabled: boolean) {\r\n        this._halfDome = enabled;\r\n        this._halfDomeMask.setEnabled(enabled);\r\n        this._changeTextureMode(this._textureMode);\r\n    }\r\n\r\n    /**\r\n     * Set the cross-eye mode. If set, images that can be seen when crossing eyes will render correctly\r\n     */\r\n    public set crossEye(enabled: boolean) {\r\n        this._crossEye = enabled;\r\n        this._changeTextureMode(this._textureMode);\r\n    }\r\n\r\n    /**\r\n     * Is it a cross-eye texture?\r\n     */\r\n    public get crossEye(): boolean {\r\n        return this._crossEye;\r\n    }\r\n\r\n    /**\r\n     * The background material of this dome.\r\n     */\r\n    public get material(): BackgroundMaterial {\r\n        return this._material;\r\n    }\r\n\r\n    /**\r\n     * Oberserver used in Stereoscopic VR Mode.\r\n     */\r\n    private _onBeforeCameraRenderObserver: Nullable<Observer<Camera>> = null;\r\n    /**\r\n     * Observable raised when an error occurred while loading the texture\r\n     */\r\n    public onLoadErrorObservable = new Observable<string>();\r\n    /**\r\n     * Observable raised when the texture finished loading\r\n     */\r\n    public onLoadObservable = new Observable<void>();\r\n\r\n    /**\r\n     * Create an instance of this class and pass through the parameters to the relevant classes- Texture, StandardMaterial, and Mesh.\r\n     * @param name Element's name, child elements will append suffixes for their own names.\r\n     * @param textureUrlOrElement defines the url(s) or the (video) HTML element to use\r\n     * @param options An object containing optional or exposed sub element properties\r\n     * @param options.resolution\r\n     * @param options.clickToPlay\r\n     * @param options.autoPlay\r\n     * @param options.loop\r\n     * @param options.size\r\n     * @param options.poster\r\n     * @param options.faceForward\r\n     * @param options.useDirectMapping\r\n     * @param options.halfDomeMode\r\n     * @param options.crossEyeMode\r\n     * @param options.generateMipMaps\r\n     * @param options.mesh\r\n     * @param scene\r\n     * @param onError\r\n     */\r\n    constructor(\r\n        name: string,\r\n        textureUrlOrElement: string | string[] | HTMLVideoElement,\r\n        options: {\r\n            resolution?: number;\r\n            clickToPlay?: boolean;\r\n            autoPlay?: boolean;\r\n            loop?: boolean;\r\n            size?: number;\r\n            poster?: string;\r\n            faceForward?: boolean;\r\n            useDirectMapping?: boolean;\r\n            halfDomeMode?: boolean;\r\n            crossEyeMode?: boolean;\r\n            generateMipMaps?: boolean;\r\n            mesh?: Mesh;\r\n        },\r\n        scene: Scene,\r\n        // eslint-disable-next-line @typescript-eslint/naming-convention\r\n        protected onError: Nullable<(message?: string, exception?: any) => void> = null\r\n    ) {\r\n        super(name, scene);\r\n\r\n        scene = this.getScene();\r\n\r\n        // set defaults and manage values\r\n        name = name || \"textureDome\";\r\n        options.resolution = Math.abs(options.resolution as any) | 0 || 32;\r\n        options.clickToPlay = Boolean(options.clickToPlay);\r\n        options.autoPlay = options.autoPlay === undefined ? true : Boolean(options.autoPlay);\r\n        options.loop = options.loop === undefined ? true : Boolean(options.loop);\r\n        options.size = Math.abs(options.size as any) || (scene.activeCamera ? scene.activeCamera.maxZ * 0.48 : 1000);\r\n\r\n        if (options.useDirectMapping === undefined) {\r\n            this._useDirectMapping = true;\r\n        } else {\r\n            this._useDirectMapping = options.useDirectMapping;\r\n        }\r\n\r\n        if (options.faceForward === undefined) {\r\n            options.faceForward = true;\r\n        }\r\n\r\n        this._setReady(false);\r\n        if (!options.mesh) {\r\n            this._mesh = CreateSphere(name + \"_mesh\", { segments: options.resolution, diameter: options.size, updatable: false, sideOrientation: Mesh.BACKSIDE }, scene);\r\n        } else {\r\n            this._mesh = options.mesh;\r\n        }\r\n        // configure material\r\n        const material = (this._material = new BackgroundMaterial(name + \"_material\", scene));\r\n        material.useEquirectangularFOV = true;\r\n        material.fovMultiplier = 1.0;\r\n        material.opacityFresnel = false;\r\n\r\n        const texture = this._initTexture(textureUrlOrElement, scene, options);\r\n        this.texture = texture;\r\n\r\n        // configure mesh\r\n        this._mesh.material = material;\r\n        this._mesh.parent = this;\r\n\r\n        // create a (disabled until needed) mask to cover unneeded segments of 180 texture.\r\n        this._halfDomeMask = CreateSphere(\"\", { slice: 0.5, diameter: options.size * 0.98, segments: options.resolution * 2, sideOrientation: Mesh.BACKSIDE }, scene);\r\n        this._halfDomeMask.rotate(Axis.X, -Math.PI / 2);\r\n        // set the parent, so it will always be positioned correctly AND will be disposed when the main sphere is disposed\r\n        this._halfDomeMask.parent = this._mesh;\r\n        this._halfDome = !!options.halfDomeMode;\r\n        // enable or disable according to the settings\r\n        this._halfDomeMask.setEnabled(this._halfDome);\r\n        this._crossEye = !!options.crossEyeMode;\r\n\r\n        // create\r\n        this._texture.anisotropicFilteringLevel = 1;\r\n        this._texture.onLoadObservable.addOnce(() => {\r\n            this._setReady(true);\r\n        });\r\n\r\n        // Initial rotation\r\n        if (options.faceForward && scene.activeCamera) {\r\n            const camera = scene.activeCamera;\r\n\r\n            const forward = Vector3.Forward();\r\n            const direction = Vector3.TransformNormal(forward, camera.getViewMatrix());\r\n            direction.normalize();\r\n\r\n            this.rotation.y = Math.acos(Vector3.Dot(forward, direction));\r\n        }\r\n\r\n        this._changeTextureMode(this._textureMode);\r\n    }\r\n\r\n    protected abstract _initTexture(urlsOrElement: string | string[] | HTMLElement, scene: Scene, options: any): T;\r\n\r\n    protected _changeTextureMode(value: number): void {\r\n        this._scene.onBeforeCameraRenderObservable.remove(this._onBeforeCameraRenderObserver);\r\n        this._textureMode = value;\r\n\r\n        // Default Setup and Reset.\r\n        this._texture.uScale = 1;\r\n        this._texture.vScale = 1;\r\n        this._texture.uOffset = 0;\r\n        this._texture.vOffset = 0;\r\n        this._texture.vAng = 0;\r\n\r\n        switch (value) {\r\n            case TextureDome.MODE_MONOSCOPIC:\r\n                if (this._halfDome) {\r\n                    this._texture.uScale = 2;\r\n                    this._texture.uOffset = -1;\r\n                }\r\n                break;\r\n            case TextureDome.MODE_SIDEBYSIDE: {\r\n                // in half-dome mode the uScale should be double of 360 texture\r\n                // Use 0.99999 to boost perf by not switching program\r\n                this._texture.uScale = this._halfDome ? 0.99999 : 0.5;\r\n                const rightOffset = this._halfDome ? 0.0 : 0.5;\r\n                const leftOffset = this._halfDome ? -0.5 : 0.0;\r\n                this._onBeforeCameraRenderObserver = this._scene.onBeforeCameraRenderObservable.add((camera) => {\r\n                    let isRightCamera = camera.isRightCamera;\r\n                    if (this._crossEye) {\r\n                        isRightCamera = !isRightCamera;\r\n                    }\r\n                    if (isRightCamera) {\r\n                        this._texture.uOffset = rightOffset;\r\n                    } else {\r\n                        this._texture.uOffset = leftOffset;\r\n                    }\r\n                });\r\n                break;\r\n            }\r\n            case TextureDome.MODE_TOPBOTTOM:\r\n                // in half-dome mode the vScale should be double of 360 texture\r\n                // Use 0.99999 to boost perf by not switching program\r\n                this._texture.vScale = this._halfDome ? 0.99999 : 0.5;\r\n                this._onBeforeCameraRenderObserver = this._scene.onBeforeCameraRenderObservable.add((camera) => {\r\n                    let isRightCamera = camera.isRightCamera;\r\n                    // allow \"cross-eye\" if left and right were switched in this mode\r\n                    if (this._crossEye) {\r\n                        isRightCamera = !isRightCamera;\r\n                    }\r\n                    this._texture.vOffset = isRightCamera ? 0.5 : 0.0;\r\n                });\r\n                break;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Releases resources associated with this node.\r\n     * @param doNotRecurse Set to true to not recurse into each children (recurse into each children by default)\r\n     * @param disposeMaterialAndTextures Set to true to also dispose referenced materials and textures (false by default)\r\n     */\r\n    public dispose(doNotRecurse?: boolean, disposeMaterialAndTextures = false): void {\r\n        this._texture.dispose();\r\n        this._mesh.dispose();\r\n        this._material.dispose();\r\n\r\n        this._scene.onBeforeCameraRenderObservable.remove(this._onBeforeCameraRenderObserver);\r\n        this.onLoadErrorObservable.clear();\r\n        this.onLoadObservable.clear();\r\n\r\n        super.dispose(doNotRecurse, disposeMaterialAndTextures);\r\n    }\r\n}\r\n"]}