{"version": 3, "file": "outlineRenderer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Rendering/outlineRenderer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAGjD,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AAEjC,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEjD,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAEvD,OAAO,6BAA6B,CAAC;AACrC,OAAO,2BAA2B,CAAC;AACnC,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,iCAAiC,EAAE,gDAA+C;AAChI,OAAO,EAAE,yBAAyB,EAAE,2CAA2C,EAAE,0BAA0B,EAAE,MAAM,uCAAuC,CAAC;AAe3J;;;GAGG;AACH,KAAK,CAAC,SAAS,CAAC,kBAAkB,GAAG;IACjC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QACxB,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;KACrD;IACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;AACjC,CAAC,CAAC;AAsBF,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,EAAE;IACnD,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IACD,GAAG,EAAE,UAAsB,KAAc;QACrC,IAAI,KAAK,EAAE;YACP,2BAA2B;YAC3B,IAAI,CAAC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,CAAC;SACxC;QACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAChC,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,EAAE;IACnD,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IACD,GAAG,EAAE,UAAsB,KAAc;QACrC,IAAI,KAAK,EAAE;YACP,2BAA2B;YAC3B,IAAI,CAAC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,CAAC;SACxC;QACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAChC,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH;;;GAGG;AACH,MAAM,OAAO,eAAe;IA6BxB;;;OAGG;IACH,YAAY,KAAY;QA5BxB;;WAEG;QACI,SAAI,GAAG,uBAAuB,CAAC,oBAAoB,CAAC;QAO3D;;WAEG;QACI,YAAO,GAAG,CAAC,CAAC;QAEnB;;WAEG;QACI,iBAAY,GAAG,CAAC,CAAC,CAAC,+CAA+C;QAWpE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YACxB,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;SAC9F;IACL,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,YAAY,CAAC,uBAAuB,CAAC,gCAAgC,EAAE,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC7I,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,YAAY,CAAC,uBAAuB,CAAC,+BAA+B,EAAE,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAC9I,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,sBAAsB;IAC1B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACxD,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;SACnE;IACL,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,OAAgB,EAAE,KAAsB,EAAE,aAAsB,KAAK,EAAE,YAAqB;QACtG,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAE7D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,MAAM,0BAA0B,GAC5B,MAAM,CAAC,OAAO,EAAE,CAAC,eAAe;YAChC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,CAAC,CAAC;QAEzJ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,0BAA0B,EAAE,YAAY,CAAC,EAAE;YAClE,OAAO;SACV;QAED,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QACpC,MAAM,eAAe,GAAG,SAAS,CAAC,6BAA6B,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;QACrG,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACjD,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC;QACxE,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAEvC,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;YAClC,OAAO;SACV;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,YAAY,CAAE,CAAC;QAC3D,MAAM,MAAM,GAAG,WAAW,CAAC,SAAS,CAAC,WAAW,CAAE,CAAC;QAEnD,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAEjC,oBAAoB;QACpB,IAAU,QAAS,CAAC,mBAAmB,EAAE;YACrC,MAAM,CAAC,QAAQ,CAAC,0BAA0B,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;SAC3G;QAED,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACvE,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC1J,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC/D,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC;QAE1D,QAAQ;QACR,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,wBAAwB,IAAI,aAAa,CAAC,QAAQ,EAAE;YAC5F,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,aAAa,CAAC,QAAQ,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC;SAC5F;QAED,IAAI,aAAa,CAAC,kBAAkB,IAAI,aAAa,CAAC,kBAAkB,CAAC,wBAAwB,EAAE;YAC/F,aAAa,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SAClD;QAED,gBAAgB;QAChB,yBAAyB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QAEjD,IAAI,CAAC,0BAA0B,EAAE;YAC7B,aAAa,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;SAC3D;QAED,aAAa;QACb,IAAI,QAAQ,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE;YACzC,MAAM,YAAY,GAAG,QAAQ,CAAC,mBAAmB,EAAE,CAAC;YACpD,IAAI,YAAY,EAAE;gBACd,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,YAAY,CAAC,gBAAgB,EAAE,CAAC,CAAC;aACtE;SACJ;QAED,aAAa;QACb,aAAa,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAEvC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACjC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE3C,aAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;YACxI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IAED;;;;;;;OAOG;IACI,OAAO,CAAC,OAAgB,EAAE,YAAqB,EAAE,YAAqB;QACzE,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAE7D,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;QAErE,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QAC/B,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAEvC,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,aAAa;QACb,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE;YAC7B,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClC,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;gBACjD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBAClC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aAC/B;YACD,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE;gBAClD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;gBACnC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aAC/B;SACJ;QACD,mBAAmB;QACnB,IAAU,QAAS,CAAC,mBAAmB,EAAE;YACrC,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;SAC5C;QACD,cAAc;QACd,iCAAiC,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAE5D,QAAQ;QACR,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAChD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;YAC/C,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;gBAC7B,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;gBACpD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;aACvD;YACD,OAAO,CAAC,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACxE,OAAO,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAChG;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;SAClD;QAED,gBAAgB;QAChB,MAAM,kBAAkB,GAAI,IAAa,CAAC,kBAAkB,CAAC;QAC7D,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAC5B,IAAI,kBAAkB,EAAE;YACpB,mBAAmB,GAAG,kBAAkB,CAAC,iBAAiB,IAAI,kBAAkB,CAAC,cAAc,CAAC;YAChG,IAAI,mBAAmB,GAAG,CAAC,EAAE;gBACzB,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACrC,OAAO,CAAC,IAAI,CAAC,gCAAgC,GAAG,mBAAmB,CAAC,CAAC;gBAErE,IAAI,kBAAkB,CAAC,wBAAwB,EAAE;oBAC7C,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;iBAChD;gBAED,2CAA2C,CAAC,OAAO,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;aACnF;SACJ;QAED,YAAY;QACZ,IAAI,YAAY,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YACpC,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE;gBAC7C,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;aAC1C;SACJ;QAED,qBAAqB;QACrB,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAE,CAAC;QACjE,MAAM,aAAa,GAAG,WAAW,CAAC,OAAO,CAAC;QAC1C,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,aAAa,KAAK,IAAI,EAAE;YACxB,MAAM,QAAQ,GAAG;gBACb,OAAO;gBACP,QAAQ;gBACR,gBAAgB;gBAChB,eAAe;gBACf,QAAQ;gBACR,OAAO;gBACP,0BAA0B;gBAC1B,uBAAuB;gBACvB,kBAAkB;gBAClB,wBAAwB;gBACxB,2BAA2B;aAC9B,CAAC;YACF,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAE/B,WAAW,CAAC,SAAS,CACjB,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE;gBACzI,2BAA2B,EAAE,mBAAmB;aACnD,CAAC,EACF,IAAI,CACP,CAAC;SACL;QAED,OAAO,WAAW,CAAC,MAAO,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;IAEO,oBAAoB,CAAC,IAAU,EAAE,OAAgB,EAAE,KAAsB;QAC7E,mBAAmB;QACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QACrD,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YACvC,IAAI,QAAQ,IAAI,QAAQ,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE;gBACrD,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBACjC,oDAAoD;gBACpD,uHAAuH;gBACvH,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAClC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAClC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBACpC,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACxD,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAClD,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;gBAC/D,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;gBAC5E,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,oBAAoB,GAAG,IAAI,CAAC;gBAC9D,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE7F,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBACjC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;aACvD;YAED,sFAAsF;YACtF,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAElD,IAAI,QAAQ,IAAI,QAAQ,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE;gBACrD,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,oBAAoB,GAAG,KAAK,CAAC;gBAC/D,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;aACtC;SACJ;IACL,CAAC;IAEO,mBAAmB,CAAC,IAAU,EAAE,OAAgB,EAAE,KAAsB;QAC5E,UAAU;QACV,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAChD,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC;YAC3D,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;YACjE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACvC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAClD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,GAAG,eAAe,CAAC;SACxD;QAED,mBAAmB;QACnB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC7C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACjC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SACpC;IACL,CAAC;;AAhUD;;GAEG;AACY,iCAAiB,GAAG,IAAI,AAAP,CAAQ", "sourcesContent": ["import { VertexBuffer } from \"../Buffers/buffer\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { _InstancesBatch } from \"../Meshes/mesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { Scene } from \"../scene\";\r\nimport type { Engine } from \"../Engines/engine\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { ISceneComponent } from \"../sceneComponent\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport { DrawWrapper } from \"../Materials/drawWrapper\";\r\n\r\nimport \"../Shaders/outline.fragment\";\r\nimport \"../Shaders/outline.vertex\";\r\nimport { addClipPlaneUniforms, bindClipPlane, prepareStringDefinesForClipPlanes } from \"core/Materials/clipPlaneMaterialHelper\";\r\nimport { BindMorphTargetParameters, PrepareAttributesForMorphTargetsInfluencers, PushAttributesForInstances } from \"../Materials/materialHelper.functions\";\r\n\r\ndeclare module \"../scene\" {\r\n    export interface Scene {\r\n        /** @internal */\r\n        _outlineRenderer: OutlineRenderer;\r\n\r\n        /**\r\n         * Gets the outline renderer associated with the scene\r\n         * @returns a OutlineRenderer\r\n         */\r\n        getOutlineRenderer(): OutlineRenderer;\r\n    }\r\n}\r\n\r\n/**\r\n * Gets the outline renderer associated with the scene\r\n * @returns a OutlineRenderer\r\n */\r\nScene.prototype.getOutlineRenderer = function (): OutlineRenderer {\r\n    if (!this._outlineRenderer) {\r\n        this._outlineRenderer = new OutlineRenderer(this);\r\n    }\r\n    return this._outlineRenderer;\r\n};\r\n\r\ndeclare module \"../Meshes/abstractMesh\" {\r\n    export interface AbstractMesh {\r\n        /** @internal (Backing field) */\r\n        _renderOutline: boolean;\r\n        /**\r\n         * Gets or sets a boolean indicating if the outline must be rendered as well\r\n         * @see https://www.babylonjs-playground.com/#10WJ5S#3\r\n         */\r\n        renderOutline: boolean;\r\n\r\n        /** @internal (Backing field) */\r\n        _renderOverlay: boolean;\r\n        /**\r\n         * Gets or sets a boolean indicating if the overlay must be rendered as well\r\n         * @see https://www.babylonjs-playground.com/#10WJ5S#2\r\n         */\r\n        renderOverlay: boolean;\r\n    }\r\n}\r\n\r\nObject.defineProperty(Mesh.prototype, \"renderOutline\", {\r\n    get: function (this: Mesh) {\r\n        return this._renderOutline;\r\n    },\r\n    set: function (this: Mesh, value: boolean) {\r\n        if (value) {\r\n            // Lazy Load the component.\r\n            this.getScene().getOutlineRenderer();\r\n        }\r\n        this._renderOutline = value;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(Mesh.prototype, \"renderOverlay\", {\r\n    get: function (this: Mesh) {\r\n        return this._renderOverlay;\r\n    },\r\n    set: function (this: Mesh, value: boolean) {\r\n        if (value) {\r\n            // Lazy Load the component.\r\n            this.getScene().getOutlineRenderer();\r\n        }\r\n        this._renderOverlay = value;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\n/**\r\n * This class is responsible to draw the outline/overlay of meshes.\r\n * It should not be used directly but through the available method on mesh.\r\n */\r\nexport class OutlineRenderer implements ISceneComponent {\r\n    /**\r\n     * Stencil value used to avoid outline being seen within the mesh when the mesh is transparent\r\n     */\r\n    private static _StencilReference = 0x04;\r\n    /**\r\n     * The name of the component. Each component must have a unique name.\r\n     */\r\n    public name = SceneComponentConstants.NAME_OUTLINERENDERER;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    /**\r\n     * Defines a zOffset default Factor to prevent zFighting between the overlay and the mesh.\r\n     */\r\n    public zOffset = 1;\r\n\r\n    /**\r\n     * Defines a zOffset default Unit to prevent zFighting between the overlay and the mesh.\r\n     */\r\n    public zOffsetUnits = 4; // 4 to account for projection a bit by default\r\n\r\n    private _engine: Engine;\r\n    private _savedDepthWrite: boolean;\r\n    private _passIdForDrawWrapper: number[];\r\n\r\n    /**\r\n     * Instantiates a new outline renderer. (There could be only one per scene).\r\n     * @param scene Defines the scene it belongs to\r\n     */\r\n    constructor(scene: Scene) {\r\n        this.scene = scene;\r\n        this._engine = scene.getEngine();\r\n        this.scene._addComponent(this);\r\n        this._passIdForDrawWrapper = [];\r\n        for (let i = 0; i < 4; ++i) {\r\n            this._passIdForDrawWrapper[i] = this._engine.createRenderPassId(`Outline Renderer (${i})`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Register the component to one instance of a scene.\r\n     */\r\n    public register(): void {\r\n        this.scene._beforeRenderingMeshStage.registerStep(SceneComponentConstants.STEP_BEFORERENDERINGMESH_OUTLINE, this, this._beforeRenderingMesh);\r\n        this.scene._afterRenderingMeshStage.registerStep(SceneComponentConstants.STEP_AFTERRENDERINGMESH_OUTLINE, this, this._afterRenderingMesh);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        // Nothing to do here.\r\n    }\r\n\r\n    /**\r\n     * Disposes the component and the associated resources.\r\n     */\r\n    public dispose(): void {\r\n        for (let i = 0; i < this._passIdForDrawWrapper.length; ++i) {\r\n            this._engine.releaseRenderPassId(this._passIdForDrawWrapper[i]);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Renders the outline in the canvas.\r\n     * @param subMesh Defines the sumesh to render\r\n     * @param batch Defines the batch of meshes in case of instances\r\n     * @param useOverlay Defines if the rendering is for the overlay or the outline\r\n     * @param renderPassId Render pass id to use to render the mesh\r\n     */\r\n    public render(subMesh: SubMesh, batch: _InstancesBatch, useOverlay: boolean = false, renderPassId?: number): void {\r\n        renderPassId = renderPassId ?? this._passIdForDrawWrapper[0];\r\n\r\n        const scene = this.scene;\r\n        const engine = scene.getEngine();\r\n\r\n        const hardwareInstancedRendering =\r\n            engine.getCaps().instancedArrays &&\r\n            ((batch.visibleInstances[subMesh._id] !== null && batch.visibleInstances[subMesh._id] !== undefined) || subMesh.getRenderingMesh().hasThinInstances);\r\n\r\n        if (!this.isReady(subMesh, hardwareInstancedRendering, renderPassId)) {\r\n            return;\r\n        }\r\n\r\n        const ownerMesh = subMesh.getMesh();\r\n        const replacementMesh = ownerMesh._internalAbstractMeshDataInfo._actAsRegularMesh ? ownerMesh : null;\r\n        const renderingMesh = subMesh.getRenderingMesh();\r\n        const effectiveMesh = replacementMesh ? replacementMesh : renderingMesh;\r\n        const material = subMesh.getMaterial();\r\n\r\n        if (!material || !scene.activeCamera) {\r\n            return;\r\n        }\r\n\r\n        const drawWrapper = subMesh._getDrawWrapper(renderPassId)!;\r\n        const effect = DrawWrapper.GetEffect(drawWrapper)!;\r\n\r\n        engine.enableEffect(drawWrapper);\r\n\r\n        // Logarithmic depth\r\n        if ((<any>material).useLogarithmicDepth) {\r\n            effect.setFloat(\"logarithmicDepthConstant\", 2.0 / (Math.log(scene.activeCamera.maxZ + 1.0) / Math.LN2));\r\n        }\r\n\r\n        effect.setFloat(\"offset\", useOverlay ? 0 : renderingMesh.outlineWidth);\r\n        effect.setColor4(\"color\", useOverlay ? renderingMesh.overlayColor : renderingMesh.outlineColor, useOverlay ? renderingMesh.overlayAlpha : material.alpha);\r\n        effect.setMatrix(\"viewProjection\", scene.getTransformMatrix());\r\n        effect.setMatrix(\"world\", effectiveMesh.getWorldMatrix());\r\n\r\n        // Bones\r\n        if (renderingMesh.useBones && renderingMesh.computeBonesUsingShaders && renderingMesh.skeleton) {\r\n            effect.setMatrices(\"mBones\", renderingMesh.skeleton.getTransformMatrices(renderingMesh));\r\n        }\r\n\r\n        if (renderingMesh.morphTargetManager && renderingMesh.morphTargetManager.isUsingTextureForTargets) {\r\n            renderingMesh.morphTargetManager._bind(effect);\r\n        }\r\n\r\n        // Morph targets\r\n        BindMorphTargetParameters(renderingMesh, effect);\r\n\r\n        if (!hardwareInstancedRendering) {\r\n            renderingMesh._bind(subMesh, effect, material.fillMode);\r\n        }\r\n\r\n        // Alpha test\r\n        if (material && material.needAlphaTesting()) {\r\n            const alphaTexture = material.getAlphaTestTexture();\r\n            if (alphaTexture) {\r\n                effect.setTexture(\"diffuseSampler\", alphaTexture);\r\n                effect.setMatrix(\"diffuseMatrix\", alphaTexture.getTextureMatrix());\r\n            }\r\n        }\r\n\r\n        // Clip plane\r\n        bindClipPlane(effect, material, scene);\r\n\r\n        engine.setZOffset(-this.zOffset);\r\n        engine.setZOffsetUnits(-this.zOffsetUnits);\r\n\r\n        renderingMesh._processRendering(effectiveMesh, subMesh, effect, material.fillMode, batch, hardwareInstancedRendering, (isInstance, world) => {\r\n            effect.setMatrix(\"world\", world);\r\n        });\r\n\r\n        engine.setZOffset(0);\r\n        engine.setZOffsetUnits(0);\r\n    }\r\n\r\n    /**\r\n     * Returns whether or not the outline renderer is ready for a given submesh.\r\n     * All the dependencies e.g. submeshes, texture, effect... mus be ready\r\n     * @param subMesh Defines the submesh to check readiness for\r\n     * @param useInstances Defines whether wee are trying to render instances or not\r\n     * @param renderPassId Render pass id to use to render the mesh\r\n     * @returns true if ready otherwise false\r\n     */\r\n    public isReady(subMesh: SubMesh, useInstances: boolean, renderPassId?: number): boolean {\r\n        renderPassId = renderPassId ?? this._passIdForDrawWrapper[0];\r\n\r\n        const defines = [];\r\n        const attribs = [VertexBuffer.PositionKind, VertexBuffer.NormalKind];\r\n\r\n        const mesh = subMesh.getMesh();\r\n        const material = subMesh.getMaterial();\r\n\r\n        if (!material) {\r\n            return false;\r\n        }\r\n\r\n        const scene = mesh.getScene();\r\n\r\n        // Alpha test\r\n        if (material.needAlphaTesting()) {\r\n            defines.push(\"#define ALPHATEST\");\r\n            if (mesh.isVerticesDataPresent(VertexBuffer.UVKind)) {\r\n                attribs.push(VertexBuffer.UVKind);\r\n                defines.push(\"#define UV1\");\r\n            }\r\n            if (mesh.isVerticesDataPresent(VertexBuffer.UV2Kind)) {\r\n                attribs.push(VertexBuffer.UV2Kind);\r\n                defines.push(\"#define UV2\");\r\n            }\r\n        }\r\n        //Logarithmic depth\r\n        if ((<any>material).useLogarithmicDepth) {\r\n            defines.push(\"#define LOGARITHMICDEPTH\");\r\n        }\r\n        // Clip planes\r\n        prepareStringDefinesForClipPlanes(material, scene, defines);\r\n\r\n        // Bones\r\n        if (mesh.useBones && mesh.computeBonesUsingShaders) {\r\n            attribs.push(VertexBuffer.MatricesIndicesKind);\r\n            attribs.push(VertexBuffer.MatricesWeightsKind);\r\n            if (mesh.numBoneInfluencers > 4) {\r\n                attribs.push(VertexBuffer.MatricesIndicesExtraKind);\r\n                attribs.push(VertexBuffer.MatricesWeightsExtraKind);\r\n            }\r\n            defines.push(\"#define NUM_BONE_INFLUENCERS \" + mesh.numBoneInfluencers);\r\n            defines.push(\"#define BonesPerMesh \" + (mesh.skeleton ? mesh.skeleton.bones.length + 1 : 0));\r\n        } else {\r\n            defines.push(\"#define NUM_BONE_INFLUENCERS 0\");\r\n        }\r\n\r\n        // Morph targets\r\n        const morphTargetManager = (mesh as Mesh).morphTargetManager;\r\n        let numMorphInfluencers = 0;\r\n        if (morphTargetManager) {\r\n            numMorphInfluencers = morphTargetManager.numMaxInfluencers || morphTargetManager.numInfluencers;\r\n            if (numMorphInfluencers > 0) {\r\n                defines.push(\"#define MORPHTARGETS\");\r\n                defines.push(\"#define NUM_MORPH_INFLUENCERS \" + numMorphInfluencers);\r\n\r\n                if (morphTargetManager.isUsingTextureForTargets) {\r\n                    defines.push(\"#define MORPHTARGETS_TEXTURE\");\r\n                }\r\n\r\n                PrepareAttributesForMorphTargetsInfluencers(attribs, mesh, numMorphInfluencers);\r\n            }\r\n        }\r\n\r\n        // Instances\r\n        if (useInstances) {\r\n            defines.push(\"#define INSTANCES\");\r\n            PushAttributesForInstances(attribs);\r\n            if (subMesh.getRenderingMesh().hasThinInstances) {\r\n                defines.push(\"#define THIN_INSTANCES\");\r\n            }\r\n        }\r\n\r\n        // Get correct effect\r\n        const drawWrapper = subMesh._getDrawWrapper(renderPassId, true)!;\r\n        const cachedDefines = drawWrapper.defines;\r\n        const join = defines.join(\"\\n\");\r\n\r\n        if (cachedDefines !== join) {\r\n            const uniforms = [\r\n                \"world\",\r\n                \"mBones\",\r\n                \"viewProjection\",\r\n                \"diffuseMatrix\",\r\n                \"offset\",\r\n                \"color\",\r\n                \"logarithmicDepthConstant\",\r\n                \"morphTargetInfluences\",\r\n                \"morphTargetCount\",\r\n                \"morphTargetTextureInfo\",\r\n                \"morphTargetTextureIndices\",\r\n            ];\r\n            addClipPlaneUniforms(uniforms);\r\n\r\n            drawWrapper.setEffect(\r\n                this.scene.getEngine().createEffect(\"outline\", attribs, uniforms, [\"diffuseSampler\", \"morphTargets\"], join, undefined, undefined, undefined, {\r\n                    maxSimultaneousMorphTargets: numMorphInfluencers,\r\n                }),\r\n                join\r\n            );\r\n        }\r\n\r\n        return drawWrapper.effect!.isReady();\r\n    }\r\n\r\n    private _beforeRenderingMesh(mesh: Mesh, subMesh: SubMesh, batch: _InstancesBatch): void {\r\n        // Outline - step 1\r\n        this._savedDepthWrite = this._engine.getDepthWrite();\r\n        if (mesh.renderOutline) {\r\n            const material = subMesh.getMaterial();\r\n            if (material && material.needAlphaBlendingForMesh(mesh)) {\r\n                this._engine.cacheStencilState();\r\n                // Draw only to stencil buffer for the original mesh\r\n                // The resulting stencil buffer will be used so the outline is not visible inside the mesh when the mesh is transparent\r\n                this._engine.setDepthWrite(false);\r\n                this._engine.setColorWrite(false);\r\n                this._engine.setStencilBuffer(true);\r\n                this._engine.setStencilOperationPass(Constants.REPLACE);\r\n                this._engine.setStencilFunction(Constants.ALWAYS);\r\n                this._engine.setStencilMask(OutlineRenderer._StencilReference);\r\n                this._engine.setStencilFunctionReference(OutlineRenderer._StencilReference);\r\n                this._engine.stencilStateComposer.useStencilGlobalOnly = true;\r\n                this.render(subMesh, batch, /* This sets offset to 0 */ true, this._passIdForDrawWrapper[1]);\r\n\r\n                this._engine.setColorWrite(true);\r\n                this._engine.setStencilFunction(Constants.NOTEQUAL);\r\n            }\r\n\r\n            // Draw the outline using the above stencil if needed to avoid drawing within the mesh\r\n            this._engine.setDepthWrite(false);\r\n            this.render(subMesh, batch, false, this._passIdForDrawWrapper[0]);\r\n            this._engine.setDepthWrite(this._savedDepthWrite);\r\n\r\n            if (material && material.needAlphaBlendingForMesh(mesh)) {\r\n                this._engine.stencilStateComposer.useStencilGlobalOnly = false;\r\n                this._engine.restoreStencilState();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _afterRenderingMesh(mesh: Mesh, subMesh: SubMesh, batch: _InstancesBatch): void {\r\n        // Overlay\r\n        if (mesh.renderOverlay) {\r\n            const currentMode = this._engine.getAlphaMode();\r\n            const alphaBlendState = this._engine.alphaState.alphaBlend;\r\n            this._engine.setAlphaMode(Constants.ALPHA_COMBINE);\r\n            this.render(subMesh, batch, true, this._passIdForDrawWrapper[3]);\r\n            this._engine.setAlphaMode(currentMode);\r\n            this._engine.setDepthWrite(this._savedDepthWrite);\r\n            this._engine.alphaState.alphaBlend = alphaBlendState;\r\n        }\r\n\r\n        // Outline - step 2\r\n        if (mesh.renderOutline && this._savedDepthWrite) {\r\n            this._engine.setDepthWrite(true);\r\n            this._engine.setColorWrite(false);\r\n            this.render(subMesh, batch, false, this._passIdForDrawWrapper[2]);\r\n            this._engine.setColorWrite(true);\r\n        }\r\n    }\r\n}\r\n"]}