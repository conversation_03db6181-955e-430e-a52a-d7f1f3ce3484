{"version": 3, "file": "shadowMapBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Fragment/shadowMapBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,iBAAiB;IACjD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,qCAAqC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC1F,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,qCAAqC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC1F,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,qCAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC1F,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAE5E,IAAI,CAAC,WAAW,CAAC,0CAA0C,CACvD,qCAAqC,CAAC,MAAM,GAAG,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,CAC/I,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,KAA6B;QAC3C,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAC7C,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAC5C,KAAK,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACtC,KAAK,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACvC,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAElC,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;QACvD,KAAK,CAAC,sBAAsB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QACpD,KAAK,CAAC,sBAAsB,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QAEtD,KAAK,CAAC,wBAAwB,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QAE7D,KAAK,CAAC,iBAAiB,IAAI,mBAAmB,IAAI,CAAC,aAAa,CAAC,sBAAsB,KAAK,CAAC;QAC7F,KAAK,CAAC,iBAAiB,IAAI,sBAAsB,CAAC;QAClD,KAAK,CAAC,iBAAiB,IAAI,+BAA+B,CAAC;QAC3D,KAAK,CAAC,iBAAiB,IAAI,cAAc,CAAC;QAE1C,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;YAC9B,KAAK,CAAC,iBAAiB,IAAI,mBAAmB,IAAI,CAAC,WAAW,CAAC,sBAAsB,SAAS,CAAC;YAC/F,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,2BAA2B,EAAE,QAAQ,CAAC,CAAC;SAChG;QAED,KAAK,CAAC,iBAAiB,IAAI,kBAAkB,IAAI,CAAC,cAAc,CAAC,sBAAsB,gBAAgB,CAAC;QAExG,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,uBAAuB,EAAE,QAAQ,EAAE;YACrF,cAAc,EAAE;gBACZ;oBACI,MAAM,EAAE,cAAc;oBACtB,OAAO,EAAE,SAAS;iBACrB;aACJ;SACJ,CAAC,CAAC;QAEH,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,QAAQ,EAAE;YACjF,cAAc,EAAE;gBACZ;oBACI,MAAM,EAAE,UAAU;oBAClB,OAAO,EAAE,EAAE;iBACd;aACJ;SACJ,CAAC,CAAC;QAEH,KAAK,CAAC,iBAAiB,IAAI;;;;;;;;SAQ1B,CAAC;QAEF,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,6BAA6B,CAAC;QAElG,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\n\r\n/**\r\n * Block used to output the depth to a shadow map\r\n */\r\nexport class ShadowMapBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new ShadowMapBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.Vector4, false);\r\n        this.registerInput(\"viewProjection\", NodeMaterialBlockConnectionPointTypes.Matrix, false);\r\n        this.registerInput(\"worldNormal\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n        this.registerOutput(\"depth\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n\r\n        this.worldNormal.addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"ShadowMapBlock\";\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"vPositionWSM\");\r\n        state._excludeVariableName(\"lightDataSM\");\r\n        state._excludeVariableName(\"biasAndScaleSM\");\r\n        state._excludeVariableName(\"depthValuesSM\");\r\n        state._excludeVariableName(\"clipPos\");\r\n        state._excludeVariableName(\"worldPos\");\r\n        state._excludeVariableName(\"zSM\");\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the view x projection input component\r\n     */\r\n    public get viewProjection(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the depth output component\r\n     */\r\n    public get depth(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const comments = `//${this.name}`;\r\n\r\n        state._emitUniformFromString(\"biasAndScaleSM\", \"vec3\");\r\n        state._emitUniformFromString(\"lightDataSM\", \"vec3\");\r\n        state._emitUniformFromString(\"depthValuesSM\", \"vec2\");\r\n\r\n        state._emitFunctionFromInclude(\"packingFunctions\", comments);\r\n\r\n        state.compilationString += `vec4 worldPos = ${this.worldPosition.associatedVariableName};\\n`;\r\n        state.compilationString += `vec3 vPositionWSM;\\n`;\r\n        state.compilationString += `float vDepthMetricSM = 0.0;\\n`;\r\n        state.compilationString += `float zSM;\\n`;\r\n\r\n        if (this.worldNormal.isConnected) {\r\n            state.compilationString += `vec3 vNormalW = ${this.worldNormal.associatedVariableName}.xyz;\\n`;\r\n            state.compilationString += state._emitCodeFromInclude(\"shadowMapVertexNormalBias\", comments);\r\n        }\r\n\r\n        state.compilationString += `vec4 clipPos = ${this.viewProjection.associatedVariableName} * worldPos;\\n`;\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"shadowMapVertexMetric\", comments, {\r\n            replaceStrings: [\r\n                {\r\n                    search: /gl_Position/g,\r\n                    replace: \"clipPos\",\r\n                },\r\n            ],\r\n        });\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"shadowMapFragment\", comments, {\r\n            replaceStrings: [\r\n                {\r\n                    search: /return;/g,\r\n                    replace: \"\",\r\n                },\r\n            ],\r\n        });\r\n\r\n        state.compilationString += `\r\n            #if SM_DEPTHTEXTURE == 1\r\n                #ifdef IS_NDC_HALF_ZRANGE\r\n                    gl_FragDepth = (clipPos.z / clipPos.w);\r\n                #else\r\n                    gl_FragDepth = (clipPos.z / clipPos.w) * 0.5 + 0.5;\r\n                #endif\r\n            #endif\r\n        `;\r\n\r\n        state.compilationString += `${this._declareOutput(this.depth, state)} = vec3(depthSM, 1., 1.);\\n`;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ShadowMapBlock\", ShadowMapBlock);\r\n"]}