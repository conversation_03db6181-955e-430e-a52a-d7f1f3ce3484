{"version": 3, "file": "node.js", "sourceRoot": "", "sources": ["../../../dev/core/src/node.ts"], "names": [], "mappings": ";AAGA,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;AAGtD,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAC/C,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAU9C,OAAO,EAAE,mBAAmB,EAAE,MAAM,iCAAiC,CAAC;AAOtE,gBAAgB;AAChB,MAAM,qBAAqB;IAA3B;QACW,oBAAe,GAAG,KAAK,CAAC;QACxB,gBAAW,GAAG,KAAK,CAAC;QACpB,yBAAoB,GAAG,CAAC,CAAC,CAAC;QAC1B,eAAU,GAAG,IAAI,CAAC;QAClB,qBAAgB,GAAG,IAAI,CAAC;QACxB,aAAQ,GAAG,IAAI,CAAC;QAChB,qCAAgC,GAAG,IAAI,UAAU,EAAW,CAAC;QAC7D,wBAAmB,GAAG,IAAI,UAAU,EAAQ,CAAC;IACxD,CAAC;CAAA;AAED;;GAEG;AACH,MAAM,OAAO,IAAI;IAYb;;;;OAIG;IACI,MAAM,CAAC,kBAAkB,CAAC,IAAY,EAAE,eAAgC;QAC3E,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC;IACnD,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,SAAS,CAAC,IAAY,EAAE,IAAY,EAAE,KAAY,EAAE,OAAa;QAC3E,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO,IAAI,CAAC;SACf;QAED,OAAO,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;IAgDD;;OAEG;IACH,IAAW,gBAAgB,CAAC,KAAkC;QAC1D,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,mCAAmC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IACpE,CAAC;IAED,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IASD;;OAEG;IACH,IAAW,cAAc;QACrB,IAAI,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE;YACvC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;SAC1C;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAW,cAAc,CAAC,KAAc;QACpC,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,KAAK,CAAC;IAClD,CAAC;IA6CD;;;OAGG;IACI,UAAU;QACb,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACH,IAAW,MAAM,CAAC,MAAsB;QACpC,IAAI,IAAI,CAAC,WAAW,KAAK,MAAM,EAAE;YAC7B,OAAO;SACV;QAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC;QAE5C,8CAA8C;QAC9C,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,KAAK,IAAI,EAAE;YACrG,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACvD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBACd,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAC/C;YAED,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE;gBAC/C,IAAI,CAAC,oBAAoB,EAAE,CAAC;aAC/B;SACJ;QAED,mBAAmB;QACnB,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;QAE1B,6BAA6B;QAC7B,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,KAAK,IAAI,EAAE;gBACjF,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,KAAK,EAAQ,CAAC;aAClD;YACD,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtC,IAAI,CAAC,kBAAkB,EAAE;gBACrB,IAAI,CAAC,yBAAyB,EAAE,CAAC;aACpC;SACJ;QAED,gBAAgB;QAChB,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACnC,CAAC;IAED,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,mBAAwB;QAC9C,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IACjD,CAAC;IAED,gBAAgB;IACT,oBAAoB;QACvB,IAAI,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,KAAK,CAAC,CAAC,EAAE;YACnD,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YAC1E,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACpC;IACL,CAAC;IAED,gBAAgB;IACT,yBAAyB;QAC5B,IAAI,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,KAAK,CAAC,CAAC,EAAE;YACnD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YACxC,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YACrC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;YAC3E,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAC,gBAAgB,CAAC,oBAAoB,GAAG,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,CAAC;YACzI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;YAC5B,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;SACnD;IACL,CAAC;IAID;;OAEG;IACH,IAAW,2BAA2B;QAClC,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACpC,OAAO,IAAI,CAAC,MAAM,CAAC,2BAA2B,CAAC;SAClD;QACD,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED,IAAW,2BAA2B,CAAC,KAA4C;QAC/E,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,MAAM,CAAC;IAClB,CAAC;IAWD;;OAEG;IACH,IAAW,SAAS,CAAC,QAAoB;QACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC5D;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,IAAW,+BAA+B;QACtC,OAAO,IAAI,CAAC,gBAAgB,CAAC,gCAAgC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;IACrD,CAAC;IAED;;;;OAIG;IACH,YAAY,IAAY,EAAE,QAAyB,IAAI;QApT7C,aAAQ,GAAG,KAAK,CAAC;QAsCnB,qBAAgB,GAAG,IAAI,qBAAqB,EAAE,CAAC;QAoBvD;;WAEG;QAEI,UAAK,GAAG,EAAE,CAAC;QAElB;;WAEG;QAEI,aAAQ,GAAQ,IAAI,CAAC;QAK5B;;WAEG;QACI,sBAAiB,GAAQ,IAAI,CAAC;QAoB3B,sBAAiB,GAAgC,IAAI,CAAC;QAEhE;;WAEG;QACI,wCAAmC,GAAG,IAAI,UAAU,EAA+B,CAAC;QAqB3F,gBAAgB;QACT,qBAAgB,GAA4B,IAAI,CAAC;QAExD;;WAEG;QACI,eAAU,GAAgB,EAAE,CAAC;QAC1B,YAAO,GAAiD,EAAE,CAAC;QAErE;;WAEG;QACI,YAAO,GAAmC,IAAI,CAAC;QAEtD,gBAAgB;QACT,qBAAgB,GAAG,CAAC,CAAC,CAAC;QACrB,oBAAe,GAAG,CAAC,CAAC,CAAC;QAC7B,gBAAgB;QACT,mBAAc,GAAG,CAAC,CAAC,CAAC;QAE3B,gBAAgB;QACT,qBAAgB,GAAqB,IAAI,CAAC;QACjD,gBAAgB;QACT,gCAA2B,GAAqB,IAAI,CAAC;QAC5D,gBAAgB;QACT,2BAAsB,GAAqB,IAAI,CAAC;QAGvD,gBAAgB;QACT,WAAM,GAAQ,EAAE,CAAC;QAEd,gBAAW,GAAmB,IAAI,CAAC;QAE7C,gBAAgB;QACN,cAAS,GAAqB,IAAI,CAAC;QAE7C,gBAAgB;QACT,iBAAY,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QACxC,gBAAgB;QACT,4BAAuB,GAAG,CAAC,CAAC;QACnC,gBAAgB;QACT,mCAA8B,GAAG,IAAI,CAAC;QAmFrC,iCAA4B,GAA0C,IAAI,CAAC;QAwBnF,gBAAgB;QACA,YAAO,GAAG,IAAI,CAAC;QAE/B;;WAEG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAQ,CAAC;QAE5C,uBAAkB,GAA6B,IAAI,CAAC;QAsD5D,YAAY;QACJ,eAAU,GAAG,IAAI,KAAK,EAAkB,CAAC;QAxB7C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;QACf,IAAI,CAAC,MAAM,GAAU,CAAC,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC,CAAC;QAC7D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;IACnC,CAAC;IAKD;;;;;;OAMG;IACI,WAAW,CAAC,QAAwB,EAAE,iBAAiB,GAAG,KAAK;QAClE,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,OAAO,IAAI,CAAC;SACf;QAED,QAAQ,CAAC,IAAI,EAAE,CAAC;QAChB,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,iBAAiB,EAAE;YAC7C,oDAAoD;YACpD,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,GAAG,EAAE;gBAC5C,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC;SACN;aAAM;YACH,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACzB;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,QAAwB;QAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAEjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,IAAY;QACjC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,IAAI,QAAQ,CAAC,IAAI,KAAK,IAAI,EAAE;gBACxB,OAAO,QAAQ,CAAC;aACnB;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE;YACrD,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,gBAAgB;IACT,0BAA0B;QAC7B,IAAI,IAAI,CAAC,8BAA8B,EAAE;YACrC,IAAI,CAAC,8BAA8B,GAAG,KAAK,CAAC;YAC5C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;SAClE;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;OAGG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,qEAAqE;IACrE,mCAAmC;IACnC,gBAAgB;IACT,UAAU;QACb,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,KAAe;QAC9B,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;YACjC,OAAO;SACV;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAEjC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,OAAgB,EAAE,YAAY,GAAG,IAAI;QACpE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,2BAA2B,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED,qEAAqE;IACrE,yDAAyD;IACzD;;OAEG;IACI,YAAY,CAAC,kBAA4B,IAAS,CAAC;IAE1D,qEAAqE;IACrE,gBAAgB;IACT,eAAe;QAClB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gBAAgB;IACT,qBAAqB;QACxB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;SAC1D;IACL,CAAC;IAED,gBAAgB;IACT,wBAAwB;QAC3B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;YACvF,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;IAC7C,CAAC;IAED,gBAAgB;IACT,cAAc;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,WAAW,EAAE;YACzC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;YACtC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE;YACtD,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,cAAc,GAAG,KAAK;QACjC,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,SAAkB;QACjC,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,iBAA0B,IAAI;QAC3C,IAAI,cAAc,KAAK,KAAK,EAAE;YAC1B,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;SAC3C;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE;YACnC,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;IAClD,CAAC;IAED,gBAAgB;IACN,uBAAuB;QAC7B,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAEhG,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBACzB,CAAC,CAAC,uBAAuB,EAAE,CAAC,CAAC,uCAAuC;YACxE,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,KAAc;QAC5B,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,KAAK,KAAK,EAAE;YAC5C,OAAO;SACV;QACD,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG,KAAK,CAAC;QACzC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAClF,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,QAAc;QAChC,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;gBAC1B,OAAO,IAAI,CAAC;aACf;YAED,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;SAC/C;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,OAAe,EAAE,wBAAiC,KAAK,EAAE,SAAmC;QAC/G,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO;SACV;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACxD,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAEnC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;gBAC/B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACtB;YAED,IAAI,CAAC,qBAAqB,EAAE;gBACxB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;aACnD;SACJ;IACL,CAAC;IAkBD;;;;;OAKG;IACI,cAAc,CAAC,qBAA+B,EAAE,SAAmC;QACtF,MAAM,OAAO,GAAW,EAAE,CAAC;QAE3B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,qBAAqB,EAAE,SAAS,CAAC,CAAC;QAEhE,OAAO,OAAO,CAAC;IACnB,CAAC;IAkBD;;;;;OAKG;IACI,cAAc,CAAC,qBAA+B,EAAE,SAAmC;QACtF,MAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,qBAAqB,EAAE,CAAC,IAAU,EAAE,EAAE;YAChE,OAAO,CAAC,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,IAAmB,IAAK,CAAC,eAAe,KAAK,SAAS,CAAC;QACjG,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAkBD;;;;;OAKG;IACI,WAAW,CAAC,SAAmC,EAAE,qBAAqB,GAAG,IAAI;QAChF,OAAO,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,KAAc;QAC3B,IAAI,KAAK,KAAK,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;YAC1C,OAAO;SACV;QAED,IAAI,CAAC,KAAK,EAAE;YACR,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,KAAK,CAAC;YACvC,OAAO;SACV;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtB;QACD,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,IAAY;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAErC,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,EAAE;gBACzB,OAAO,SAAS,CAAC;aACpB;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,oBAAoB,CAAC,IAAY,EAAE,IAAY,EAAE,EAAU;QAC9D,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;gBACxE,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;oBACpB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;iBAClD;aACJ;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,IAAY,EAAE,YAAY,GAAG,IAAI;QACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;YACxE,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;gBACpB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;aACtD;SACJ;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,mDAAmD;IAClF,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,IAAY;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IACtC,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,IAAY,EAAE,SAAyB,EAAE,kBAA4B;QAC9E,MAAM,MAAM,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAEtF,IAAI,SAAS,EAAE;YACX,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;SAC7B;QAED,IAAI,CAAC,kBAAkB,EAAE;YACrB,WAAW;YACX,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC3D,MAAM,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAEvC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;aAChD;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,MAAM,eAAe,GAA+B,EAAE,CAAC;QACvD,IAAI,IAAY,CAAC;QACjB,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;YACvB,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;SAC5C;QACD,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;;;OAOG;IACI,cAAc,CAAC,IAAY,EAAE,IAAc,EAAE,UAAmB,EAAE,cAA2B;QAChG,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IACpG,CAAC;IAED;;;OAGG;IACI,wBAAwB;QAC3B,MAAM,mBAAmB,GAAG,EAAE,CAAC;QAC/B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;YAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,UAAU,EAAE;gBACb,SAAS;aACZ;YACD,MAAM,KAAK,GAAQ,EAAE,CAAC;YACtB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;YAClB,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;YAC7B,KAAK,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;YACzB,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnC;QACD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,MAAgB;QACtC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;SACzC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,YAAsB,EAAE,0BAA0B,GAAG,KAAK;QACrE,IAAI,CAAC,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC;QAEzC,IAAI,CAAC,YAAY,EAAE;YACf,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACxC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACtB,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,0BAA0B,CAAC,CAAC;aAC1D;SACJ;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,IAAI,CAAC,yBAAyB,EAAE,CAAC;SACpC;aAAM;YACH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACtB;QAED,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QAEjC,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,CAAC;QAC7C,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAEhC,YAAY;QACZ,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,QAAQ,CAAC,MAAM,EAAE,CAAC;SACrB;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QAE3B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,oBAAoB,CAAC,IAAU,EAAE,UAAe,EAAE,MAAa;QACzE,IAAI,UAAU,CAAC,MAAM,EAAE;YACnB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC3D,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACtC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;aAC5D;SACJ;IACL,CAAC;IACD;;;;;OAKG;IACI,2BAA2B,CAAC,kBAAkB,GAAG,IAAI,EAAE,YAA+D,IAAI;QAC7H,oDAAoD;QACpD,IAAI,CAAC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,CAAC;QAEpC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAE9B,IAAI,GAAY,CAAC;QACjB,IAAI,GAAY,CAAC;QAEjB,MAAM,gBAAgB,GAAG,IAA4B,CAAC;QACtD,IAAI,gBAAgB,CAAC,eAAe,IAAI,gBAAgB,CAAC,SAAS,EAAE;YAChE,oDAAoD;YACpD,MAAM,YAAY,GAAG,gBAAgB,CAAC,eAAe,EAAE,CAAC;YACxD,GAAG,GAAG,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;YACpD,GAAG,GAAG,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;SACvD;aAAM;YACH,GAAG,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;YACxE,GAAG,GAAG,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SAC9E;QAED,IAAI,kBAAkB,EAAE;YACpB,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAE/C,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;gBAClC,MAAM,SAAS,GAAiB,UAAU,CAAC;gBAC3C,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAEnC,qDAAqD;gBACrD,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;oBACpC,SAAS;iBACZ;gBAED,wDAAwD;gBACxD,IAAI,CAAC,SAAS,CAAC,eAAe,IAAI,SAAS,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;oBAClE,SAAS;iBACZ;gBAED,MAAM,iBAAiB,GAAG,SAAS,CAAC,eAAe,EAAE,CAAC;gBACtD,MAAM,WAAW,GAAG,iBAAiB,CAAC,WAAW,CAAC;gBAElD,MAAM,MAAM,GAAG,WAAW,CAAC,YAAY,CAAC;gBACxC,MAAM,MAAM,GAAG,WAAW,CAAC,YAAY,CAAC;gBAExC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACvC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;aAC1C;SACJ;QAED,OAAO;YACH,GAAG,EAAE,GAAG;YACR,GAAG,EAAE,GAAG;SACX,CAAC;IACN,CAAC;;AA98BD;;GAEG;AACW,2BAAsB,GAAG,CAAC,KAAa,EAAE,KAAa,EAAE,GAAW,EAAkB,EAAE;IACjG,MAAM,WAAW,CAAC,gBAAgB,CAAC,CAAC;AACxC,CAAC,AAFmC,CAElC;AAEa,sBAAiB,GAA2B,EAAE,AAA7B,CAA8B;AAmCvD;IADN,SAAS,EAAE;kCACQ;AAMb;IADN,SAAS,EAAE;gCACM;AAMX;IADN,SAAS,EAAE;sCACY;AAMjB;IADN,SAAS,EAAE;mCACM;AAMX;IADN,SAAS,EAAE;sCACgB", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport type { Scene } from \"./scene\";\r\nimport type { Nullable } from \"./types\";\r\nimport { Matrix, Vector3 } from \"./Maths/math.vector\";\r\nimport type { Engine } from \"./Engines/engine\";\r\nimport type { IBehaviorAware, Behavior } from \"./Behaviors/behavior\";\r\nimport { serialize } from \"./Misc/decorators\";\r\nimport type { Observer } from \"./Misc/observable\";\r\nimport { Observable } from \"./Misc/observable\";\r\nimport { EngineStore } from \"./Engines/engineStore\";\r\nimport { _WarnImport } from \"./Misc/devTools\";\r\nimport type { AbstractActionManager } from \"./Actions/abstractActionManager\";\r\nimport type { IInspectable } from \"./Misc/iInspectable\";\r\nimport type { AbstractScene } from \"./abstractScene\";\r\nimport type { IAccessibilityTag } from \"./IAccessibilityTag\";\r\nimport type { AnimationRange } from \"./Animations/animationRange\";\r\nimport type { AnimationPropertiesOverride } from \"./Animations/animationPropertiesOverride\";\r\nimport type { AbstractMesh } from \"./Meshes/abstractMesh\";\r\nimport type { Animation } from \"./Animations/animation\";\r\nimport type { Animatable } from \"./Animations/animatable\";\r\nimport { SerializationHelper } from \"./Misc/decorators.serialization\";\r\n\r\n/**\r\n * Defines how a node can be built from a string name.\r\n */\r\nexport type NodeConstructor = (name: string, scene: Scene, options?: any) => () => Node;\r\n\r\n/** @internal */\r\nclass _InternalNodeDataInfo {\r\n    public _doNotSerialize = false;\r\n    public _isDisposed = false;\r\n    public _sceneRootNodesIndex = -1;\r\n    public _isEnabled = true;\r\n    public _isParentEnabled = true;\r\n    public _isReady = true;\r\n    public _onEnabledStateChangedObservable = new Observable<boolean>();\r\n    public _onClonedObservable = new Observable<Node>();\r\n}\r\n\r\n/**\r\n * Node is the basic class for all scene objects (Mesh, Light, Camera.)\r\n */\r\nexport class Node implements IBehaviorAware<Node> {\r\n    protected _isDirty = false;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _AnimationRangeFactory = (_name: string, _from: number, _to: number): AnimationRange => {\r\n        throw _WarnImport(\"AnimationRange\");\r\n    };\r\n\r\n    private static _NodeConstructors: { [key: string]: any } = {};\r\n\r\n    /**\r\n     * Add a new node constructor\r\n     * @param type defines the type name of the node to construct\r\n     * @param constructorFunc defines the constructor function\r\n     */\r\n    public static AddNodeConstructor(type: string, constructorFunc: NodeConstructor) {\r\n        this._NodeConstructors[type] = constructorFunc;\r\n    }\r\n\r\n    /**\r\n     * Returns a node constructor based on type name\r\n     * @param type defines the type name\r\n     * @param name defines the new node name\r\n     * @param scene defines the hosting scene\r\n     * @param options defines optional options to transmit to constructors\r\n     * @returns the new constructor or null\r\n     */\r\n    public static Construct(type: string, name: string, scene: Scene, options?: any): Nullable<() => Node> {\r\n        const constructorFunc = this._NodeConstructors[type];\r\n\r\n        if (!constructorFunc) {\r\n            return null;\r\n        }\r\n\r\n        return constructorFunc(name, scene, options);\r\n    }\r\n\r\n    private _nodeDataStorage = new _InternalNodeDataInfo();\r\n\r\n    /**\r\n     * Gets or sets the name of the node\r\n     */\r\n    @serialize()\r\n    public name: string;\r\n\r\n    /**\r\n     * Gets or sets the id of the node\r\n     */\r\n    @serialize()\r\n    public id: string;\r\n\r\n    /**\r\n     * Gets or sets the unique id of the node\r\n     */\r\n    @serialize()\r\n    public uniqueId: number;\r\n\r\n    /**\r\n     * Gets or sets a string used to store user defined state for the node\r\n     */\r\n    @serialize()\r\n    public state = \"\";\r\n\r\n    /**\r\n     * Gets or sets an object used to store user defined information for the node\r\n     */\r\n    @serialize()\r\n    public metadata: any = null;\r\n\r\n    /** @internal */\r\n    public _internalMetadata: any;\r\n\r\n    /**\r\n     * For internal use only. Please do not use.\r\n     */\r\n    public reservedDataStore: any = null;\r\n\r\n    /**\r\n     * List of inspectable custom properties (used by the Inspector)\r\n     * @see https://doc.babylonjs.com/toolsAndResources/inspector#extensibility\r\n     */\r\n    public inspectableCustomProperties: IInspectable[];\r\n\r\n    /**\r\n     * Gets or sets the accessibility tag to describe the node for accessibility purpose.\r\n     */\r\n    public set accessibilityTag(value: Nullable<IAccessibilityTag>) {\r\n        this._accessibilityTag = value;\r\n        this.onAccessibilityTagChangedObservable.notifyObservers(value);\r\n    }\r\n\r\n    public get accessibilityTag() {\r\n        return this._accessibilityTag;\r\n    }\r\n\r\n    protected _accessibilityTag: Nullable<IAccessibilityTag> = null;\r\n\r\n    /**\r\n     * Observable fired when an accessibility tag is changed\r\n     */\r\n    public onAccessibilityTagChangedObservable = new Observable<Nullable<IAccessibilityTag>>();\r\n\r\n    /**\r\n     * Gets or sets a boolean used to define if the node must be serialized\r\n     */\r\n    public get doNotSerialize() {\r\n        if (this._nodeDataStorage._doNotSerialize) {\r\n            return true;\r\n        }\r\n\r\n        if (this._parentNode) {\r\n            return this._parentNode.doNotSerialize;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public set doNotSerialize(value: boolean) {\r\n        this._nodeDataStorage._doNotSerialize = value;\r\n    }\r\n\r\n    /** @internal */\r\n    public _parentContainer: Nullable<AbstractScene> = null;\r\n\r\n    /**\r\n     * Gets a list of Animations associated with the node\r\n     */\r\n    public animations: Animation[] = [];\r\n    protected _ranges: { [name: string]: Nullable<AnimationRange> } = {};\r\n\r\n    /**\r\n     * Callback raised when the node is ready to be used\r\n     */\r\n    public onReady: Nullable<(node: Node) => void> = null;\r\n\r\n    /** @internal */\r\n    public _currentRenderId = -1;\r\n    private _parentUpdateId = -1;\r\n    /** @internal */\r\n    public _childUpdateId = -1;\r\n\r\n    /** @internal */\r\n    public _waitingParentId: Nullable<string> = null;\r\n    /** @internal */\r\n    public _waitingParentInstanceIndex: Nullable<string> = null;\r\n    /** @internal */\r\n    public _waitingParsedUniqueId: Nullable<number> = null;\r\n    /** @internal */\r\n    public _scene: Scene;\r\n    /** @internal */\r\n    public _cache: any = {};\r\n\r\n    protected _parentNode: Nullable<Node> = null;\r\n\r\n    /** @internal */\r\n    protected _children: Nullable<Node[]> = null;\r\n\r\n    /** @internal */\r\n    public _worldMatrix = Matrix.Identity();\r\n    /** @internal */\r\n    public _worldMatrixDeterminant = 0;\r\n    /** @internal */\r\n    public _worldMatrixDeterminantIsDirty = true;\r\n\r\n    /**\r\n     * Gets a boolean indicating if the node has been disposed\r\n     * @returns true if the node was disposed\r\n     */\r\n    public isDisposed(): boolean {\r\n        return this._nodeDataStorage._isDisposed;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the parent of the node (without keeping the current position in the scene)\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/transforms/parent_pivot/parent\r\n     */\r\n    public set parent(parent: Nullable<Node>) {\r\n        if (this._parentNode === parent) {\r\n            return;\r\n        }\r\n\r\n        const previousParentNode = this._parentNode;\r\n\r\n        // Remove self from list of children of parent\r\n        if (this._parentNode && this._parentNode._children !== undefined && this._parentNode._children !== null) {\r\n            const index = this._parentNode._children.indexOf(this);\r\n            if (index !== -1) {\r\n                this._parentNode._children.splice(index, 1);\r\n            }\r\n\r\n            if (!parent && !this._nodeDataStorage._isDisposed) {\r\n                this._addToSceneRootNodes();\r\n            }\r\n        }\r\n\r\n        // Store new parent\r\n        this._parentNode = parent;\r\n\r\n        // Add as child to new parent\r\n        if (this._parentNode) {\r\n            if (this._parentNode._children === undefined || this._parentNode._children === null) {\r\n                this._parentNode._children = new Array<Node>();\r\n            }\r\n            this._parentNode._children.push(this);\r\n\r\n            if (!previousParentNode) {\r\n                this._removeFromSceneRootNodes();\r\n            }\r\n        }\r\n\r\n        // Enabled state\r\n        this._syncParentEnabledState();\r\n    }\r\n\r\n    public get parent(): Nullable<Node> {\r\n        return this._parentNode;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _serializeAsParent(serializationObject: any): void {\r\n        serializationObject.parentId = this.uniqueId;\r\n    }\r\n\r\n    /** @internal */\r\n    public _addToSceneRootNodes() {\r\n        if (this._nodeDataStorage._sceneRootNodesIndex === -1) {\r\n            this._nodeDataStorage._sceneRootNodesIndex = this._scene.rootNodes.length;\r\n            this._scene.rootNodes.push(this);\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _removeFromSceneRootNodes() {\r\n        if (this._nodeDataStorage._sceneRootNodesIndex !== -1) {\r\n            const rootNodes = this._scene.rootNodes;\r\n            const lastIdx = rootNodes.length - 1;\r\n            rootNodes[this._nodeDataStorage._sceneRootNodesIndex] = rootNodes[lastIdx];\r\n            rootNodes[this._nodeDataStorage._sceneRootNodesIndex]._nodeDataStorage._sceneRootNodesIndex = this._nodeDataStorage._sceneRootNodesIndex;\r\n            this._scene.rootNodes.pop();\r\n            this._nodeDataStorage._sceneRootNodesIndex = -1;\r\n        }\r\n    }\r\n\r\n    private _animationPropertiesOverride: Nullable<AnimationPropertiesOverride> = null;\r\n\r\n    /**\r\n     * Gets or sets the animation properties override\r\n     */\r\n    public get animationPropertiesOverride(): Nullable<AnimationPropertiesOverride> {\r\n        if (!this._animationPropertiesOverride) {\r\n            return this._scene.animationPropertiesOverride;\r\n        }\r\n        return this._animationPropertiesOverride;\r\n    }\r\n\r\n    public set animationPropertiesOverride(value: Nullable<AnimationPropertiesOverride>) {\r\n        this._animationPropertiesOverride = value;\r\n    }\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"Node\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"Node\";\r\n    }\r\n\r\n    /** @internal */\r\n    public readonly _isNode = true;\r\n\r\n    /**\r\n     * An event triggered when the mesh is disposed\r\n     */\r\n    public onDisposeObservable = new Observable<Node>();\r\n\r\n    private _onDisposeObserver: Nullable<Observer<Node>> = null;\r\n    /**\r\n     * Sets a callback that will be raised when the node will be disposed\r\n     */\r\n    public set onDispose(callback: () => void) {\r\n        if (this._onDisposeObserver) {\r\n            this.onDisposeObservable.remove(this._onDisposeObserver);\r\n        }\r\n        this._onDisposeObserver = this.onDisposeObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered when the enabled state of the node changes\r\n     */\r\n    public get onEnabledStateChangedObservable(): Observable<boolean> {\r\n        return this._nodeDataStorage._onEnabledStateChangedObservable;\r\n    }\r\n\r\n    /**\r\n     * An event triggered when the node is cloned\r\n     */\r\n    public get onClonedObservable(): Observable<Node> {\r\n        return this._nodeDataStorage._onClonedObservable;\r\n    }\r\n\r\n    /**\r\n     * Creates a new Node\r\n     * @param name the name and id to be given to this node\r\n     * @param scene the scene this node will be added to\r\n     */\r\n    constructor(name: string, scene: Nullable<Scene> = null) {\r\n        this.name = name;\r\n        this.id = name;\r\n        this._scene = <Scene>(scene || EngineStore.LastCreatedScene);\r\n        this.uniqueId = this._scene.getUniqueId();\r\n        this._initCache();\r\n    }\r\n\r\n    /**\r\n     * Gets the scene of the node\r\n     * @returns a scene\r\n     */\r\n    public getScene(): Scene {\r\n        return this._scene;\r\n    }\r\n\r\n    /**\r\n     * Gets the engine of the node\r\n     * @returns a Engine\r\n     */\r\n    public getEngine(): Engine {\r\n        return this._scene.getEngine();\r\n    }\r\n\r\n    // Behaviors\r\n    private _behaviors = new Array<Behavior<Node>>();\r\n\r\n    /**\r\n     * Attach a behavior to the node\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors\r\n     * @param behavior defines the behavior to attach\r\n     * @param attachImmediately defines that the behavior must be attached even if the scene is still loading\r\n     * @returns the current Node\r\n     */\r\n    public addBehavior(behavior: Behavior<Node>, attachImmediately = false): Node {\r\n        const index = this._behaviors.indexOf(behavior);\r\n\r\n        if (index !== -1) {\r\n            return this;\r\n        }\r\n\r\n        behavior.init();\r\n        if (this._scene.isLoading && !attachImmediately) {\r\n            // We defer the attach when the scene will be loaded\r\n            this._scene.onDataLoadedObservable.addOnce(() => {\r\n                behavior.attach(this);\r\n            });\r\n        } else {\r\n            behavior.attach(this);\r\n        }\r\n        this._behaviors.push(behavior);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove an attached behavior\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors\r\n     * @param behavior defines the behavior to attach\r\n     * @returns the current Node\r\n     */\r\n    public removeBehavior(behavior: Behavior<Node>): Node {\r\n        const index = this._behaviors.indexOf(behavior);\r\n\r\n        if (index === -1) {\r\n            return this;\r\n        }\r\n\r\n        this._behaviors[index].detach();\r\n        this._behaviors.splice(index, 1);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of attached behaviors\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors\r\n     */\r\n    public get behaviors(): Behavior<Node>[] {\r\n        return this._behaviors;\r\n    }\r\n\r\n    /**\r\n     * Gets an attached behavior by name\r\n     * @param name defines the name of the behavior to look for\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors\r\n     * @returns null if behavior was not found else the requested behavior\r\n     */\r\n    public getBehaviorByName(name: string): Nullable<Behavior<Node>> {\r\n        for (const behavior of this._behaviors) {\r\n            if (behavior.name === name) {\r\n                return behavior;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Returns the latest update of the World matrix\r\n     * @returns a Matrix\r\n     */\r\n    public getWorldMatrix(): Matrix {\r\n        if (this._currentRenderId !== this._scene.getRenderId()) {\r\n            this.computeWorldMatrix();\r\n        }\r\n        return this._worldMatrix;\r\n    }\r\n\r\n    /** @internal */\r\n    public _getWorldMatrixDeterminant(): number {\r\n        if (this._worldMatrixDeterminantIsDirty) {\r\n            this._worldMatrixDeterminantIsDirty = false;\r\n            this._worldMatrixDeterminant = this._worldMatrix.determinant();\r\n        }\r\n        return this._worldMatrixDeterminant;\r\n    }\r\n\r\n    /**\r\n     * Returns directly the latest state of the mesh World matrix.\r\n     * A Matrix is returned.\r\n     */\r\n    public get worldMatrixFromCache(): Matrix {\r\n        return this._worldMatrix;\r\n    }\r\n\r\n    // override it in derived class if you add new variables to the cache\r\n    // and call the parent class method\r\n    /** @internal */\r\n    public _initCache() {\r\n        this._cache = {};\r\n        this._cache.parent = undefined;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public updateCache(force?: boolean): void {\r\n        if (!force && this.isSynchronized()) {\r\n            return;\r\n        }\r\n\r\n        this._cache.parent = this.parent;\r\n\r\n        this._updateCache();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getActionManagerForTrigger(trigger?: number, _initialCall = true): Nullable<AbstractActionManager> {\r\n        if (!this.parent) {\r\n            return null;\r\n        }\r\n\r\n        return this.parent._getActionManagerForTrigger(trigger, false);\r\n    }\r\n\r\n    // override it in derived class if you add new variables to the cache\r\n    // and call the parent class method if !ignoreParentClass\r\n    /**\r\n     * @internal\r\n     */\r\n    public _updateCache(_ignoreParentClass?: boolean): void {}\r\n\r\n    // override it in derived class if you add new variables to the cache\r\n    /** @internal */\r\n    public _isSynchronized(): boolean {\r\n        return true;\r\n    }\r\n\r\n    /** @internal */\r\n    public _markSyncedWithParent() {\r\n        if (this._parentNode) {\r\n            this._parentUpdateId = this._parentNode._childUpdateId;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public isSynchronizedWithParent(): boolean {\r\n        if (!this._parentNode) {\r\n            return true;\r\n        }\r\n\r\n        if (this._parentNode._isDirty || this._parentUpdateId !== this._parentNode._childUpdateId) {\r\n            return false;\r\n        }\r\n\r\n        return this._parentNode.isSynchronized();\r\n    }\r\n\r\n    /** @internal */\r\n    public isSynchronized(): boolean {\r\n        if (this._cache.parent !== this._parentNode) {\r\n            this._cache.parent = this._parentNode;\r\n            return false;\r\n        }\r\n\r\n        if (this._parentNode && !this.isSynchronizedWithParent()) {\r\n            return false;\r\n        }\r\n\r\n        return this._isSynchronized();\r\n    }\r\n\r\n    /**\r\n     * Is this node ready to be used/rendered\r\n     * @param _completeCheck defines if a complete check (including materials and lights) has to be done (false by default)\r\n     * @returns true if the node is ready\r\n     */\r\n    public isReady(_completeCheck = false): boolean {\r\n        return this._nodeDataStorage._isReady;\r\n    }\r\n\r\n    /**\r\n     * Flag the  node as dirty (Forcing it to update everything)\r\n     * @param _property helps children apply precise \"dirtyfication\"\r\n     * @returns this node\r\n     */\r\n    public markAsDirty(_property?: string): Node {\r\n        this._currentRenderId = Number.MAX_VALUE;\r\n        this._isDirty = true;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Is this node enabled?\r\n     * If the node has a parent, all ancestors will be checked and false will be returned if any are false (not enabled), otherwise will return true\r\n     * @param checkAncestors indicates if this method should check the ancestors. The default is to check the ancestors. If set to false, the method will return the value of this node without checking ancestors\r\n     * @returns whether this node (and its parent) is enabled\r\n     */\r\n    public isEnabled(checkAncestors: boolean = true): boolean {\r\n        if (checkAncestors === false) {\r\n            return this._nodeDataStorage._isEnabled;\r\n        }\r\n\r\n        if (!this._nodeDataStorage._isEnabled) {\r\n            return false;\r\n        }\r\n\r\n        return this._nodeDataStorage._isParentEnabled;\r\n    }\r\n\r\n    /** @internal */\r\n    protected _syncParentEnabledState() {\r\n        this._nodeDataStorage._isParentEnabled = this._parentNode ? this._parentNode.isEnabled() : true;\r\n\r\n        if (this._children) {\r\n            this._children.forEach((c) => {\r\n                c._syncParentEnabledState(); // Force children to update accordingly\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the enabled state of this node\r\n     * @param value defines the new enabled state\r\n     */\r\n    public setEnabled(value: boolean): void {\r\n        if (this._nodeDataStorage._isEnabled === value) {\r\n            return;\r\n        }\r\n        this._nodeDataStorage._isEnabled = value;\r\n        this._syncParentEnabledState();\r\n        this._nodeDataStorage._onEnabledStateChangedObservable.notifyObservers(value);\r\n    }\r\n\r\n    /**\r\n     * Is this node a descendant of the given node?\r\n     * The function will iterate up the hierarchy until the ancestor was found or no more parents defined\r\n     * @param ancestor defines the parent node to inspect\r\n     * @returns a boolean indicating if this node is a descendant of the given node\r\n     */\r\n    public isDescendantOf(ancestor: Node): boolean {\r\n        if (this.parent) {\r\n            if (this.parent === ancestor) {\r\n                return true;\r\n            }\r\n\r\n            return this.parent.isDescendantOf(ancestor);\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getDescendants(results: Node[], directDescendantsOnly: boolean = false, predicate?: (node: Node) => boolean): void {\r\n        if (!this._children) {\r\n            return;\r\n        }\r\n\r\n        for (let index = 0; index < this._children.length; index++) {\r\n            const item = this._children[index];\r\n\r\n            if (!predicate || predicate(item)) {\r\n                results.push(item);\r\n            }\r\n\r\n            if (!directDescendantsOnly) {\r\n                item._getDescendants(results, false, predicate);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Will return all nodes that have this node as ascendant\r\n     * @param directDescendantsOnly defines if true only direct descendants of 'this' will be considered, if false direct and also indirect (children of children, an so on in a recursive manner) descendants of 'this' will be considered\r\n     * @param predicate defines an optional predicate that will be called on every evaluated child, the predicate must return true for a given child to be part of the result, otherwise it will be ignored\r\n     * @returns all children nodes of all types\r\n     */\r\n    public getDescendants<T extends Node>(directDescendantsOnly?: boolean, predicate?: (node: Node) => node is T): T[];\r\n\r\n    /**\r\n     * Will return all nodes that have this node as ascendant\r\n     * @param directDescendantsOnly defines if true only direct descendants of 'this' will be considered, if false direct and also indirect (children of children, an so on in a recursive manner) descendants of 'this' will be considered\r\n     * @param predicate defines an optional predicate that will be called on every evaluated child, the predicate must return true for a given child to be part of the result, otherwise it will be ignored\r\n     * @returns all children nodes of all types\r\n     */\r\n    public getDescendants(directDescendantsOnly?: boolean, predicate?: (node: Node) => boolean): Node[];\r\n\r\n    /**\r\n     * Will return all nodes that have this node as ascendant\r\n     * @param directDescendantsOnly defines if true only direct descendants of 'this' will be considered, if false direct and also indirect (children of children, an so on in a recursive manner) descendants of 'this' will be considered\r\n     * @param predicate defines an optional predicate that will be called on every evaluated child, the predicate must return true for a given child to be part of the result, otherwise it will be ignored\r\n     * @returns all children nodes of all types\r\n     */\r\n    public getDescendants(directDescendantsOnly?: boolean, predicate?: (node: Node) => boolean): Node[] {\r\n        const results: Node[] = [];\r\n\r\n        this._getDescendants(results, directDescendantsOnly, predicate);\r\n\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Get all child-meshes of this node\r\n     * @param directDescendantsOnly defines if true only direct descendants of 'this' will be considered, if false direct and also indirect (children of children, an so on in a recursive manner) descendants of 'this' will be considered (Default: false)\r\n     * @param predicate defines an optional predicate that will be called on every evaluated child, the predicate must return true for a given child to be part of the result, otherwise it will be ignored\r\n     * @returns an array of AbstractMesh\r\n     */\r\n    public getChildMeshes<T extends AbstractMesh>(directDescendantsOnly?: boolean, predicate?: (node: Node) => node is T): T[];\r\n\r\n    /**\r\n     * Get all child-meshes of this node\r\n     * @param directDescendantsOnly defines if true only direct descendants of 'this' will be considered, if false direct and also indirect (children of children, an so on in a recursive manner) descendants of 'this' will be considered (Default: false)\r\n     * @param predicate defines an optional predicate that will be called on every evaluated child, the predicate must return true for a given child to be part of the result, otherwise it will be ignored\r\n     * @returns an array of AbstractMesh\r\n     */\r\n    public getChildMeshes(directDescendantsOnly?: boolean, predicate?: (node: Node) => boolean): AbstractMesh[];\r\n\r\n    /**\r\n     * Get all child-meshes of this node\r\n     * @param directDescendantsOnly defines if true only direct descendants of 'this' will be considered, if false direct and also indirect (children of children, an so on in a recursive manner) descendants of 'this' will be considered (Default: false)\r\n     * @param predicate defines an optional predicate that will be called on every evaluated child, the predicate must return true for a given child to be part of the result, otherwise it will be ignored\r\n     * @returns an array of AbstractMesh\r\n     */\r\n    public getChildMeshes(directDescendantsOnly?: boolean, predicate?: (node: Node) => boolean): AbstractMesh[] {\r\n        const results: Array<AbstractMesh> = [];\r\n        this._getDescendants(results, directDescendantsOnly, (node: Node) => {\r\n            return (!predicate || predicate(node)) && (<AbstractMesh>node).cullingStrategy !== undefined;\r\n        });\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Get all direct children of this node\r\n     * @param predicate defines an optional predicate that will be called on every evaluated child, the predicate must return true for a given child to be part of the result, otherwise it will be ignored\r\n     * @param directDescendantsOnly defines if true only direct descendants of 'this' will be considered, if false direct and also indirect (children of children, an so on in a recursive manner) descendants of 'this' will be considered (Default: true)\r\n     * @returns an array of Node\r\n     */\r\n    public getChildren<T extends Node>(predicate?: (node: Node) => node is T, directDescendantsOnly?: boolean): T[];\r\n\r\n    /**\r\n     * Get all direct children of this node\r\n     * @param predicate defines an optional predicate that will be called on every evaluated child, the predicate must return true for a given child to be part of the result, otherwise it will be ignored\r\n     * @param directDescendantsOnly defines if true only direct descendants of 'this' will be considered, if false direct and also indirect (children of children, an so on in a recursive manner) descendants of 'this' will be considered (Default: true)\r\n     * @returns an array of Node\r\n     */\r\n    public getChildren(predicate?: (node: Node) => boolean, directDescendantsOnly?: boolean): Node[];\r\n\r\n    /**\r\n     * Get all direct children of this node\r\n     * @param predicate defines an optional predicate that will be called on every evaluated child, the predicate must return true for a given child to be part of the result, otherwise it will be ignored\r\n     * @param directDescendantsOnly defines if true only direct descendants of 'this' will be considered, if false direct and also indirect (children of children, an so on in a recursive manner) descendants of 'this' will be considered (Default: true)\r\n     * @returns an array of Node\r\n     */\r\n    public getChildren(predicate?: (node: Node) => boolean, directDescendantsOnly = true): Node[] {\r\n        return this.getDescendants(directDescendantsOnly, predicate);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setReady(state: boolean): void {\r\n        if (state === this._nodeDataStorage._isReady) {\r\n            return;\r\n        }\r\n\r\n        if (!state) {\r\n            this._nodeDataStorage._isReady = false;\r\n            return;\r\n        }\r\n\r\n        if (this.onReady) {\r\n            this.onReady(this);\r\n        }\r\n        this._nodeDataStorage._isReady = true;\r\n    }\r\n\r\n    /**\r\n     * Get an animation by name\r\n     * @param name defines the name of the animation to look for\r\n     * @returns null if not found else the requested animation\r\n     */\r\n    public getAnimationByName(name: string): Nullable<Animation> {\r\n        for (let i = 0; i < this.animations.length; i++) {\r\n            const animation = this.animations[i];\r\n\r\n            if (animation.name === name) {\r\n                return animation;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Creates an animation range for this node\r\n     * @param name defines the name of the range\r\n     * @param from defines the starting key\r\n     * @param to defines the end key\r\n     */\r\n    public createAnimationRange(name: string, from: number, to: number): void {\r\n        // check name not already in use\r\n        if (!this._ranges[name]) {\r\n            this._ranges[name] = Node._AnimationRangeFactory(name, from, to);\r\n            for (let i = 0, nAnimations = this.animations.length; i < nAnimations; i++) {\r\n                if (this.animations[i]) {\r\n                    this.animations[i].createRange(name, from, to);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Delete a specific animation range\r\n     * @param name defines the name of the range to delete\r\n     * @param deleteFrames defines if animation frames from the range must be deleted as well\r\n     */\r\n    public deleteAnimationRange(name: string, deleteFrames = true): void {\r\n        for (let i = 0, nAnimations = this.animations.length; i < nAnimations; i++) {\r\n            if (this.animations[i]) {\r\n                this.animations[i].deleteRange(name, deleteFrames);\r\n            }\r\n        }\r\n        this._ranges[name] = null; // said much faster than 'delete this._range[name]'\r\n    }\r\n\r\n    /**\r\n     * Get an animation range by name\r\n     * @param name defines the name of the animation range to look for\r\n     * @returns null if not found else the requested animation range\r\n     */\r\n    public getAnimationRange(name: string): Nullable<AnimationRange> {\r\n        return this._ranges[name] || null;\r\n    }\r\n\r\n    /**\r\n     * Clone the current node\r\n     * @param name Name of the new clone\r\n     * @param newParent New parent for the clone\r\n     * @param doNotCloneChildren Do not clone children hierarchy\r\n     * @returns the new transform node\r\n     */\r\n    public clone(name: string, newParent: Nullable<Node>, doNotCloneChildren?: boolean): Nullable<Node> {\r\n        const result = SerializationHelper.Clone(() => new Node(name, this.getScene()), this);\r\n\r\n        if (newParent) {\r\n            result.parent = newParent;\r\n        }\r\n\r\n        if (!doNotCloneChildren) {\r\n            // Children\r\n            const directDescendants = this.getDescendants(true);\r\n            for (let index = 0; index < directDescendants.length; index++) {\r\n                const child = directDescendants[index];\r\n\r\n                child.clone(name + \".\" + child.name, result);\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of all animation ranges defined on this node\r\n     * @returns an array\r\n     */\r\n    public getAnimationRanges(): Nullable<AnimationRange>[] {\r\n        const animationRanges: Nullable<AnimationRange>[] = [];\r\n        let name: string;\r\n        for (name in this._ranges) {\r\n            animationRanges.push(this._ranges[name]);\r\n        }\r\n        return animationRanges;\r\n    }\r\n\r\n    /**\r\n     * Will start the animation sequence\r\n     * @param name defines the range frames for animation sequence\r\n     * @param loop defines if the animation should loop (false by default)\r\n     * @param speedRatio defines the speed factor in which to run the animation (1 by default)\r\n     * @param onAnimationEnd defines a function to be executed when the animation ended (undefined by default)\r\n     * @returns the object created for this animation. If range does not exist, it will return null\r\n     */\r\n    public beginAnimation(name: string, loop?: boolean, speedRatio?: number, onAnimationEnd?: () => void): Nullable<Animatable> {\r\n        const range = this.getAnimationRange(name);\r\n\r\n        if (!range) {\r\n            return null;\r\n        }\r\n\r\n        return this._scene.beginAnimation(this, range.from, range.to, loop, speedRatio, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Serialize animation ranges into a JSON compatible object\r\n     * @returns serialization object\r\n     */\r\n    public serializeAnimationRanges(): any {\r\n        const serializationRanges = [];\r\n        for (const name in this._ranges) {\r\n            const localRange = this._ranges[name];\r\n            if (!localRange) {\r\n                continue;\r\n            }\r\n            const range: any = {};\r\n            range.name = name;\r\n            range.from = localRange.from;\r\n            range.to = localRange.to;\r\n            serializationRanges.push(range);\r\n        }\r\n        return serializationRanges;\r\n    }\r\n\r\n    /**\r\n     * Computes the world matrix of the node\r\n     * @param _force defines if the cache version should be invalidated forcing the world matrix to be created from scratch\r\n     * @returns the world matrix\r\n     */\r\n    public computeWorldMatrix(_force?: boolean): Matrix {\r\n        if (!this._worldMatrix) {\r\n            this._worldMatrix = Matrix.Identity();\r\n        }\r\n        return this._worldMatrix;\r\n    }\r\n\r\n    /**\r\n     * Releases resources associated with this node.\r\n     * @param doNotRecurse Set to true to not recurse into each children (recurse into each children by default)\r\n     * @param disposeMaterialAndTextures Set to true to also dispose referenced materials and textures (false by default)\r\n     */\r\n    public dispose(doNotRecurse?: boolean, disposeMaterialAndTextures = false): void {\r\n        this._nodeDataStorage._isDisposed = true;\r\n\r\n        if (!doNotRecurse) {\r\n            const nodes = this.getDescendants(true);\r\n            for (const node of nodes) {\r\n                node.dispose(doNotRecurse, disposeMaterialAndTextures);\r\n            }\r\n        }\r\n\r\n        if (!this.parent) {\r\n            this._removeFromSceneRootNodes();\r\n        } else {\r\n            this.parent = null;\r\n        }\r\n\r\n        // Callback\r\n        this.onDisposeObservable.notifyObservers(this);\r\n        this.onDisposeObservable.clear();\r\n\r\n        this.onEnabledStateChangedObservable.clear();\r\n        this.onClonedObservable.clear();\r\n\r\n        // Behaviors\r\n        for (const behavior of this._behaviors) {\r\n            behavior.detach();\r\n        }\r\n\r\n        this._behaviors.length = 0;\r\n\r\n        this.metadata = null;\r\n    }\r\n\r\n    /**\r\n     * Parse animation range data from a serialization object and store them into a given node\r\n     * @param node defines where to store the animation ranges\r\n     * @param parsedNode defines the serialization object to read data from\r\n     * @param _scene defines the hosting scene\r\n     */\r\n    public static ParseAnimationRanges(node: Node, parsedNode: any, _scene: Scene): void {\r\n        if (parsedNode.ranges) {\r\n            for (let index = 0; index < parsedNode.ranges.length; index++) {\r\n                const data = parsedNode.ranges[index];\r\n                node.createAnimationRange(data.name, data.from, data.to);\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * Return the minimum and maximum world vectors of the entire hierarchy under current node\r\n     * @param includeDescendants Include bounding info from descendants as well (true by default)\r\n     * @param predicate defines a callback function that can be customize to filter what meshes should be included in the list used to compute the bounding vectors\r\n     * @returns the new bounding vectors\r\n     */\r\n    public getHierarchyBoundingVectors(includeDescendants = true, predicate: Nullable<(abstractMesh: AbstractMesh) => boolean> = null): { min: Vector3; max: Vector3 } {\r\n        // Ensures that all world matrix will be recomputed.\r\n        this.getScene().incrementRenderId();\r\n\r\n        this.computeWorldMatrix(true);\r\n\r\n        let min: Vector3;\r\n        let max: Vector3;\r\n\r\n        const thisAbstractMesh = this as Node as AbstractMesh;\r\n        if (thisAbstractMesh.getBoundingInfo && thisAbstractMesh.subMeshes) {\r\n            // If this is an abstract mesh get its bounding info\r\n            const boundingInfo = thisAbstractMesh.getBoundingInfo();\r\n            min = boundingInfo.boundingBox.minimumWorld.clone();\r\n            max = boundingInfo.boundingBox.maximumWorld.clone();\r\n        } else {\r\n            min = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n            max = new Vector3(-Number.MAX_VALUE, -Number.MAX_VALUE, -Number.MAX_VALUE);\r\n        }\r\n\r\n        if (includeDescendants) {\r\n            const descendants = this.getDescendants(false);\r\n\r\n            for (const descendant of descendants) {\r\n                const childMesh = <AbstractMesh>descendant;\r\n                childMesh.computeWorldMatrix(true);\r\n\r\n                // Filters meshes based on custom predicate function.\r\n                if (predicate && !predicate(childMesh)) {\r\n                    continue;\r\n                }\r\n\r\n                //make sure we have the needed params to get mix and max\r\n                if (!childMesh.getBoundingInfo || childMesh.getTotalVertices() === 0) {\r\n                    continue;\r\n                }\r\n\r\n                const childBoundingInfo = childMesh.getBoundingInfo();\r\n                const boundingBox = childBoundingInfo.boundingBox;\r\n\r\n                const minBox = boundingBox.minimumWorld;\r\n                const maxBox = boundingBox.maximumWorld;\r\n\r\n                Vector3.CheckExtends(minBox, min, max);\r\n                Vector3.CheckExtends(maxBox, min, max);\r\n            }\r\n        }\r\n\r\n        return {\r\n            min: min,\r\n            max: max,\r\n        };\r\n    }\r\n}\r\n"]}