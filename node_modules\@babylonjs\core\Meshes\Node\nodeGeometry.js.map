{"version": 3, "file": "nodeGeometry.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Node/nodeGeometry.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAG/B,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AAEnE,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAClE,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAClD,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AAC1E,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAErD,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AAGzD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAoB9C;;;GAGG;AACH,MAAM,OAAO,YAAY;IAgBrB,mDAAmD;IAC3C,4BAA4B;QAChC,0DAA0D;QAC1D,IAAI,OAAO,kBAAkB,KAAK,WAAW,EAAE;YAC3C,OAAO,kBAAkB,CAAC;SAC7B;QAED,gFAAgF;QAChF,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,OAAO,CAAC,kBAAkB,KAAK,WAAW,EAAE;YACrF,OAAO,OAAO,CAAC;SAClB;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAsCD;;;OAGG;IACH,YAAmB,IAAY;QA5EvB,aAAQ,GAAW,YAAY,CAAC,iBAAiB,EAAE,CAAC;QACpD,wBAAmB,GAAG,KAAK,CAAC;QAC5B,gBAAW,GAAyB,IAAI,CAAC;QACzC,wBAAmB,GAAW,CAAC,CAAC;QAQxC,gEAAgE;QACxD,0BAAqB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAwBpE;;;WAGG;QACI,eAAU,GAAQ,IAAI,CAAC;QAE9B;;WAEG;QACI,mBAAc,GAAwB,EAAE,CAAC;QAEhD;;WAEG;QACI,sBAAiB,GAAG,IAAI,UAAU,EAAgB,CAAC;QAE1D,kFAAkF;QAC3E,gBAAW,GAAkC,IAAI,CAAC;QAwBrD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACrB,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,IAAY;QAC9B,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE;gBACrB,IAAI,CAAC,MAAM,EAAE;oBACT,MAAM,GAAG,KAAK,CAAC;iBAClB;qBAAM;oBACH,KAAK,CAAC,IAAI,CAAC,+CAA+C,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;oBACzE,OAAO,MAAM,CAAC;iBACjB;aACJ;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,SAAgD;QACvE,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;gBAClB,OAAO,KAAK,CAAC;aAChB;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,MAAM,MAAM,GAAyB,EAAE,CAAC;QACxC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,IAAI,KAAK,CAAC,OAAO,EAAE;gBACf,MAAM,CAAC,IAAI,CAAC,KAA2B,CAAC,CAAC;aAC5C;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,IAAI,CAAC,MAAmC;QAC3C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAC/F,IAAI,OAAO,IAAI,CAAC,qBAAqB,IAAI,WAAW,EAAE;gBAClD,MAAM,SAAS,GAAG,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC;gBAEzF,oCAAoC;gBACpC,KAAK,CAAC,iBAAiB,CAAC,SAAS,EAAE,GAAG,EAAE;oBACpC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;oBAC/F,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;oBACzD,OAAO,EAAE,CAAC;gBACd,CAAC,CAAC,CAAC;aACN;iBAAM;gBACH,+BAA+B;gBAC/B,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;gBACzD,OAAO,EAAE,CAAC;aACb;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACK,iBAAiB,CAAC,gBAAsB;QAC5C,MAAM,gBAAgB,GAAQ;YAC1B,YAAY,EAAE,IAAI;YAClB,GAAG,gBAAgB;SACtB,CAAC;QACF,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACzE,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,UAAmB,KAAK,EAAE,aAAa,GAAG,IAAI,EAAE,aAAa,GAAG,KAAK;QAC9E,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QAEjC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,4CAA4C;YAC5C,MAAM,uEAAuE,CAAC;SACjF;QACD,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;QAC9B,oBAAoB;QACpB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;QAEvD,QAAQ;QACR,MAAM,KAAK,GAAG,IAAI,sBAAsB,EAAE,CAAC;QAE3C,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAE9B,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,iBAAiB,EAAE,CAAC;SACpD;QAED,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC,GAAG,GAAG,GAAG,CAAC;QAEnD,SAAS;QACT,KAAK,CAAC,UAAU,EAAE,CAAC;QAEnB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC;QACpC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;;;;OAKG;IACI,UAAU,CAAC,IAAY,EAAE,QAAyB,IAAI;QACzD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAEnC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,EAAE,CAAC;QACtD,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC;QAE3C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,IAAU;QACxB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAEnC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,EAAE,CAAC;QACtD,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC;QAE3C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,gBAAgB,CAAC,IAAuB,EAAE,aAAa,GAAG,IAAI;QAClE,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEpC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC1C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAClC;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC7B,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC;YAC5C,IAAI,cAAc,EAAE;gBAChB,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC;gBACxC,IAAI,KAAK,KAAK,IAAI,EAAE;oBAChB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;iBAC/C;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,KAAwB;QACvC,MAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9D,IAAI,kBAAkB,GAAG,CAAC,CAAC,EAAE;YACzB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;SACrD;QAED,IAAI,KAAK,KAAK,IAAI,CAAC,WAAW,EAAE;YAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SAC3B;IACL,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,MAAW,EAAE,KAAK,GAAG,KAAK;QACnD,IAAI,CAAC,KAAK,EAAE;YACR,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;QAED,MAAM,GAAG,GAAyC,EAAE,CAAC;QAErD,gBAAgB;QAChB,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE;YACrC,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACnD,IAAI,SAAS,EAAE;gBACX,MAAM,KAAK,GAAsB,IAAI,SAAS,EAAE,CAAC;gBACjD,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBAChC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;gBAE5B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACnC;SACJ;QAED,0BAA0B;QAC1B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,IAAI,KAAK,CAAC,aAAa,EAAE;gBACrB,MAAM,WAAW,GAAG,KAAyB,CAAC;gBAC9C,MAAM,EAAE,GAAG,WAAW,CAAC,uBAAuB,CAAC;gBAC/C,IAAI,EAAE,EAAE;oBACJ,MAAM,MAAM,GAAG,GAAG,CAAC,EAAE,CAAoB,CAAC;oBAC1C,IAAI,MAAM,EAAE;wBACR,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;qBACxC;iBACJ;aACJ;SACJ;QAED,mGAAmG;QACnG,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;YACtE,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAElC,IAAI,CAAC,KAAK,EAAE;gBACR,SAAS;aACZ;YAED,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,EAAE;gBAC9F,SAAS;aACZ;YACD,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;SAChD;QAED,UAAU;QACV,IAAI,MAAM,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,YAAY,CAAwB,CAAC;SACtE;QAED,kBAAkB;QAClB,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;YACxE,MAAM,SAAS,GAIT,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC;YAEtD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;gBAC9B,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;oBACvB,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC;iBACrD;aACJ;YAED,IAAI,KAAK,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;gBACvD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;aAC/C;YAED,IAAI,MAAM,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,UAAU,GAAG;oBACd,SAAS,EAAE,SAAS;iBACvB,CAAC;aACL;iBAAM;gBACH,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;gBACpC,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;aACzC;YAED,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;gBACnB,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;aACrC;YAED,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,QAAQ,CAAC;SAClC;QAED,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;IAClC,CAAC;IAEO,mBAAmB,CAAC,KAAwB,EAAE,MAAW,EAAE,GAAyC;QACxG,KAAK,MAAM,WAAW,IAAI,KAAK,CAAC,OAAO,EAAE;YACrC,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,MAAM,EAAE;gBACnC,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBAEjC,IAAI,CAAC,MAAM,EAAE;oBACT,SAAS;iBACZ;gBAED,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE;oBAClC,IAAI,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,KAAK,IAAI,KAAK,CAAC,oBAAoB,KAAK,WAAW,CAAC,IAAI,EAAE;wBACvF,MAAM,UAAU,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;wBAC1D,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,WAAW,EAAE;4BACvC,SAAS;yBACZ;wBAED,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;wBACxC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;wBAC9C,SAAS;qBACZ;iBACJ;aACJ;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,IAAI,aAAa,GAAwB,EAAE,CAAC;QAC5C,MAAM,MAAM,GAAwB,EAAE,CAAC;QACvC,MAAM,WAAW,GAAa,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACtD,qBAAqB;QACrB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;SAChD;QAED,WAAW;QACX,IAAI,UAAU,GAAG,gDAAgD,IAAI,CAAC,IAAI,IAAI,eAAe,OAAO,CAAC;QACrG,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE;YACvB,IAAI,IAAI,CAAC,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACpD,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;aAC5D;SACJ;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,cAAc;YACd,aAAa,GAAG,EAAE,CAAC;YACnB,UAAU,IAAI,kBAAkB,CAAC;YACjC,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;YAE5E,eAAe;YACf,UAAU,IAAI,mBAAmB,CAAC;YAClC,UAAU,IAAI,8BAA8B,IAAI,CAAC,WAAW,CAAC,iBAAiB,KAAK,CAAC;YACpF,UAAU,IAAI,yBAAyB,CAAC;SAC3C;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,aAAa,CAAC,QAA2B,EAAE,IAAyB;QACxE,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/B,OAAO;SACV;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEpB,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,EAAE;YACjC,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC;YAC5C,IAAI,cAAc,EAAE;gBAChB,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC;gBACxC,IAAI,KAAK,KAAK,QAAQ,EAAE;oBACpB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;iBACnC;aACJ;SACJ;QAED,gBAAgB;QAChB,IAAI,QAAQ,CAAC,aAAa,EAAE;YACxB,MAAM,KAAK,GAAG,QAA4B,CAAC;YAC3C,IAAI,KAAK,CAAC,UAAU,EAAE;gBAClB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aAC9C;SACJ;IACL,CAAC;IAED;;OAEG;IACI,YAAY;QACf,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,SAAS;QACT,MAAM,SAAS,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;QACtC,SAAS,CAAC,aAAa,EAAE,CAAC;QAE1B,eAAe;QACf,MAAM,MAAM,GAAG,IAAI,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;QAC1D,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE9C,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,IAAY;QACrB,MAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE7C,MAAM,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;QAC5E,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAElB,KAAK,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;QACjD,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAEnB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,cAAoC;QACjD,MAAM,mBAAmB,GAAG,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACtF,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO;QAErF,IAAI,MAAM,GAAwB,EAAE,CAAC;QAErC,IAAI,cAAc,EAAE;YAChB,MAAM,GAAG,cAAc,CAAC;SAC3B;aAAM;YACH,mBAAmB,CAAC,UAAU,GAAG,sBAAsB,CAAC;YACxD,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;aAChE;SACJ;QAED,SAAS;QACT,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAEhC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YACxB,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;SACtD;QAED,IAAI,CAAC,cAAc,EAAE;YACjB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC9B,SAAS;iBACZ;gBACD,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;aACtD;SACJ;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,KAAK,CAAC,OAAO,EAAE,CAAC;SACnB;QAED,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;IACnC,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,aAAa,CAAC,IAAY;QACpC,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QAE5C,YAAY,CAAC,YAAY,EAAE,CAAC;QAC5B,YAAY,CAAC,KAAK,EAAE,CAAC;QAErB,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,KAAK,CAAC,MAAW;QAC3B,MAAM,YAAY,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAElG,YAAY,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAC3C,YAAY,CAAC,KAAK,EAAE,CAAC;QAErB,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,qBAAqB,CAAC,SAAiB,EAAE,YAA2B,EAAE,YAAqB,KAAK;QAC1G,IAAI,SAAS,KAAK,QAAQ,EAAE;YACxB,OAAO,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;SAC/D;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE;oBACzB,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE;wBACvB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;wBACzE,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBAE7D,IAAI,CAAC,YAAY,EAAE;4BACf,YAAY,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,SAAS,CAAC,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;yBAC1G;wBAED,YAAY,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;wBACxD,YAAY,CAAC,SAAS,GAAG,SAAS,CAAC;wBAEnC,IAAI;4BACA,IAAI,CAAC,SAAS,EAAE;gCACZ,YAAY,CAAC,KAAK,EAAE,CAAC;6BACxB;4BACD,OAAO,CAAC,YAAY,CAAC,CAAC;yBACzB;wBAAC,OAAO,GAAG,EAAE;4BACV,MAAM,CAAC,GAAG,CAAC,CAAC;yBACf;qBACJ;yBAAM;wBACH,MAAM,CAAC,6BAA6B,GAAG,SAAS,CAAC,CAAC;qBACrD;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;YAC1E,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;;AA1oBc,8BAAiB,GAAW,CAAC,AAAZ,CAAa;AAM7C,gDAAgD;AAClC,sBAAS,GAAG,GAAG,KAAK,CAAC,cAAc,KAAK,MAAM,CAAC,OAAO,mDAAmD,AAAhG,CAAiG;AAExH,sCAAsC;AACxB,uBAAU,GAAG,SAAS,CAAC,UAAU,AAAvB,CAAwB;AAuDzC;IADN,SAAS,EAAE;0CACQ;AAMb;IADN,SAAS,CAAC,SAAS,CAAC;6CACE", "sourcesContent": ["import { Observable } from \"../../Misc/observable\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Mesh } from \"../mesh\";\r\nimport type { VertexData } from \"../mesh.vertexData\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { GeometryOutputBlock } from \"./Blocks/geometryOutputBlock\";\r\nimport type { NodeGeometryBlock } from \"./nodeGeometryBlock\";\r\nimport { NodeGeometryBuildState } from \"./nodeGeometryBuildState\";\r\nimport { GetClass } from \"../../Misc/typeStore\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport { SerializationHelper } from \"../../Misc/decorators.serialization\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { WebRequest } from \"../../Misc/webRequest\";\r\nimport { BoxBlock } from \"./Blocks/Sources/boxBlock\";\r\nimport type { GeometryInputBlock } from \"./Blocks/geometryInputBlock\";\r\nimport { PrecisionDate } from \"../../Misc/precisionDate\";\r\nimport type { TeleportOutBlock } from \"./Blocks/Teleport/teleportOutBlock\";\r\nimport type { TeleportInBlock } from \"./Blocks/Teleport/teleportInBlock\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport type { Color4 } from \"../../Maths/math.color\";\r\nimport { Engine } from \"../../Engines/engine\";\r\n\r\n// declare NODEGEOMETRYEDITOR namespace for compilation issue\r\ndeclare let NODEGEOMETRYEDITOR: any;\r\ndeclare let BABYLON: any;\r\n\r\n/**\r\n * Interface used to configure the node geometry editor\r\n */\r\nexport interface INodeGeometryEditorOptions {\r\n    /** Define the URL to load node editor script from */\r\n    editorURL?: string;\r\n    /** Additional configuration for the NGE */\r\n    nodeGeometryEditorConfig?: {\r\n        backgroundColor?: Color4;\r\n        hostScene?: Scene;\r\n        hostMesh?: Mesh;\r\n    };\r\n}\r\n\r\n/**\r\n * Defines a node based geometry\r\n * @see demo at https://playground.babylonjs.com#PYY6XE#69\r\n */\r\nexport class NodeGeometry {\r\n    private static _BuildIdGenerator: number = 0;\r\n    private _buildId: number = NodeGeometry._BuildIdGenerator++;\r\n    private _buildWasSuccessful = false;\r\n    private _vertexData: Nullable<VertexData> = null;\r\n    private _buildExecutionTime: number = 0;\r\n\r\n    /** Define the Url to load node editor script */\r\n    public static EditorURL = `${Tools._DefaultCdnUrl}/v${Engine.Version}/nodeGeometryEditor/babylon.nodeGeometryEditor.js`;\r\n\r\n    /** Define the Url to load snippets */\r\n    public static SnippetUrl = Constants.SnippetUrl;\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private BJSNODEGEOMETRYEDITOR = this._getGlobalNodeGeometryEditor();\r\n\r\n    /** @returns the inspector from bundle or global */\r\n    private _getGlobalNodeGeometryEditor(): any {\r\n        // UMD Global name detection from Webpack Bundle UMD Name.\r\n        if (typeof NODEGEOMETRYEDITOR !== \"undefined\") {\r\n            return NODEGEOMETRYEDITOR;\r\n        }\r\n\r\n        // In case of module let's check the global emitted from the editor entry point.\r\n        if (typeof BABYLON !== \"undefined\" && typeof BABYLON.NodeGeometryEditor !== \"undefined\") {\r\n            return BABYLON;\r\n        }\r\n\r\n        return undefined;\r\n    }\r\n\r\n    /**\r\n     * Gets the time spent to build this block (in ms)\r\n     */\r\n    public get buildExecutionTime() {\r\n        return this._buildExecutionTime;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets data used by visual editor\r\n     * @see https://nge.babylonjs.com\r\n     */\r\n    public editorData: any = null;\r\n\r\n    /**\r\n     * Gets an array of blocks that needs to be serialized even if they are not yet connected\r\n     */\r\n    public attachedBlocks: NodeGeometryBlock[] = [];\r\n\r\n    /**\r\n     * Observable raised when the geometry is built\r\n     */\r\n    public onBuildObservable = new Observable<NodeGeometry>();\r\n\r\n    /** Gets or sets the GeometryOutputBlock used to gather the final geometry data */\r\n    public outputBlock: Nullable<GeometryOutputBlock> = null;\r\n\r\n    /**\r\n     * Snippet ID if the material was created from the snippet server\r\n     */\r\n    public snippetId: string;\r\n\r\n    /**\r\n     * The name of the geometry\r\n     */\r\n    @serialize()\r\n    public name: string;\r\n\r\n    /**\r\n     * A free comment about the geometry\r\n     */\r\n    @serialize(\"comment\")\r\n    public comment: string;\r\n\r\n    /**\r\n     * Creates a new geometry\r\n     * @param name defines the name of the geometry\r\n     */\r\n    public constructor(name: string) {\r\n        this.name = name;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name of the geometry e.g. \"NodeGeometry\"\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"NodeGeometry\";\r\n    }\r\n\r\n    /**\r\n     * Get a block by its name\r\n     * @param name defines the name of the block to retrieve\r\n     * @returns the required block or null if not found\r\n     */\r\n    public getBlockByName(name: string) {\r\n        let result = null;\r\n        for (const block of this.attachedBlocks) {\r\n            if (block.name === name) {\r\n                if (!result) {\r\n                    result = block;\r\n                } else {\r\n                    Tools.Warn(\"More than one block was found with the name `\" + name + \"`\");\r\n                    return result;\r\n                }\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Get a block using a predicate\r\n     * @param predicate defines the predicate used to find the good candidate\r\n     * @returns the required block or null if not found\r\n     */\r\n    public getBlockByPredicate(predicate: (block: NodeGeometryBlock) => boolean) {\r\n        for (const block of this.attachedBlocks) {\r\n            if (predicate(block)) {\r\n                return block;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of input blocks attached to this material\r\n     * @returns an array of InputBlocks\r\n     */\r\n    public getInputBlocks() {\r\n        const blocks: GeometryInputBlock[] = [];\r\n        for (const block of this.attachedBlocks) {\r\n            if (block.isInput) {\r\n                blocks.push(block as GeometryInputBlock);\r\n            }\r\n        }\r\n\r\n        return blocks;\r\n    }\r\n\r\n    /**\r\n     * Launch the node geometry editor\r\n     * @param config Define the configuration of the editor\r\n     * @returns a promise fulfilled when the node editor is visible\r\n     */\r\n    public edit(config?: INodeGeometryEditorOptions): Promise<void> {\r\n        return new Promise((resolve) => {\r\n            this.BJSNODEGEOMETRYEDITOR = this.BJSNODEGEOMETRYEDITOR || this._getGlobalNodeGeometryEditor();\r\n            if (typeof this.BJSNODEGEOMETRYEDITOR == \"undefined\") {\r\n                const editorUrl = config && config.editorURL ? config.editorURL : NodeGeometry.EditorURL;\r\n\r\n                // Load editor and add it to the DOM\r\n                Tools.LoadBabylonScript(editorUrl, () => {\r\n                    this.BJSNODEGEOMETRYEDITOR = this.BJSNODEGEOMETRYEDITOR || this._getGlobalNodeGeometryEditor();\r\n                    this._createNodeEditor(config?.nodeGeometryEditorConfig);\r\n                    resolve();\r\n                });\r\n            } else {\r\n                // Otherwise creates the editor\r\n                this._createNodeEditor(config?.nodeGeometryEditorConfig);\r\n                resolve();\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates the node editor window.\r\n     * @param additionalConfig Additional configuration for the NGE\r\n     */\r\n    private _createNodeEditor(additionalConfig?: any) {\r\n        const nodeEditorConfig: any = {\r\n            nodeGeometry: this,\r\n            ...additionalConfig,\r\n        };\r\n        this.BJSNODEGEOMETRYEDITOR.NodeGeometryEditor.Show(nodeEditorConfig);\r\n    }\r\n\r\n    /**\r\n     * Build the final geometry\r\n     * @param verbose defines if the build should log activity\r\n     * @param updateBuildId defines if the internal build Id should be updated (default is true)\r\n     * @param autoConfigure defines if the autoConfigure method should be called when initializing blocks (default is false)\r\n     */\r\n    public build(verbose: boolean = false, updateBuildId = true, autoConfigure = false) {\r\n        this._buildWasSuccessful = false;\r\n\r\n        if (!this.outputBlock) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"You must define the outputBlock property before building the geometry\";\r\n        }\r\n        const now = PrecisionDate.Now;\r\n        // Initialize blocks\r\n        this._initializeBlock(this.outputBlock, autoConfigure);\r\n\r\n        // Build\r\n        const state = new NodeGeometryBuildState();\r\n\r\n        state.buildId = this._buildId;\r\n        state.verbose = verbose;\r\n\r\n        this.outputBlock.build(state);\r\n\r\n        if (updateBuildId) {\r\n            this._buildId = NodeGeometry._BuildIdGenerator++;\r\n        }\r\n\r\n        this._buildExecutionTime = PrecisionDate.Now - now;\r\n\r\n        // Errors\r\n        state.emitErrors();\r\n\r\n        this._buildWasSuccessful = true;\r\n        this._vertexData = state.vertexData;\r\n        this.onBuildObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Creates a mesh from the geometry blocks\r\n     * @param name defines the name of the mesh\r\n     * @param scene The scene the mesh is scoped to\r\n     * @returns The new mesh\r\n     */\r\n    public createMesh(name: string, scene: Nullable<Scene> = null): Nullable<Mesh> {\r\n        if (!this._buildWasSuccessful) {\r\n            this.build();\r\n        }\r\n\r\n        if (!this._vertexData) {\r\n            return null;\r\n        }\r\n\r\n        const mesh = new Mesh(name, scene);\r\n        this._vertexData.applyToMesh(mesh);\r\n\r\n        mesh._internalMetadata = mesh._internalMetadata || {};\r\n        mesh._internalMetadata.nodeGeometry = this;\r\n\r\n        return mesh;\r\n    }\r\n\r\n    /**\r\n     * Creates a mesh from the geometry blocks\r\n     * @param mesh the mesh to update\r\n     * @returns True if successfully updated\r\n     */\r\n    public updateMesh(mesh: Mesh) {\r\n        if (!this._buildWasSuccessful) {\r\n            this.build();\r\n        }\r\n\r\n        if (!this._vertexData) {\r\n            return false;\r\n        }\r\n\r\n        this._vertexData.applyToMesh(mesh);\r\n\r\n        mesh._internalMetadata = mesh._internalMetadata || {};\r\n        mesh._internalMetadata.nodeGeometry = this;\r\n\r\n        return mesh;\r\n    }\r\n\r\n    private _initializeBlock(node: NodeGeometryBlock, autoConfigure = true) {\r\n        node.initialize();\r\n        if (autoConfigure) {\r\n            node.autoConfigure();\r\n        }\r\n        node._preparationId = this._buildId;\r\n\r\n        if (this.attachedBlocks.indexOf(node) === -1) {\r\n            this.attachedBlocks.push(node);\r\n        }\r\n\r\n        for (const input of node.inputs) {\r\n            const connectedPoint = input.connectedPoint;\r\n            if (connectedPoint) {\r\n                const block = connectedPoint.ownerBlock;\r\n                if (block !== node) {\r\n                    this._initializeBlock(block, autoConfigure);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clear the current geometry\r\n     */\r\n    public clear() {\r\n        this.outputBlock = null;\r\n        this.attachedBlocks.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Remove a block from the current geometry\r\n     * @param block defines the block to remove\r\n     */\r\n    public removeBlock(block: NodeGeometryBlock) {\r\n        const attachedBlockIndex = this.attachedBlocks.indexOf(block);\r\n        if (attachedBlockIndex > -1) {\r\n            this.attachedBlocks.splice(attachedBlockIndex, 1);\r\n        }\r\n\r\n        if (block === this.outputBlock) {\r\n            this.outputBlock = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clear the current graph and load a new one from a serialization object\r\n     * @param source defines the JSON representation of the geometry\r\n     * @param merge defines whether or not the source must be merged or replace the current content\r\n     */\r\n    public parseSerializedObject(source: any, merge = false) {\r\n        if (!merge) {\r\n            this.clear();\r\n        }\r\n\r\n        const map: { [key: number]: NodeGeometryBlock } = {};\r\n\r\n        // Create blocks\r\n        for (const parsedBlock of source.blocks) {\r\n            const blockType = GetClass(parsedBlock.customType);\r\n            if (blockType) {\r\n                const block: NodeGeometryBlock = new blockType();\r\n                block._deserialize(parsedBlock);\r\n                map[parsedBlock.id] = block;\r\n\r\n                this.attachedBlocks.push(block);\r\n            }\r\n        }\r\n\r\n        // Reconnect teleportation\r\n        for (const block of this.attachedBlocks) {\r\n            if (block.isTeleportOut) {\r\n                const teleportOut = block as TeleportOutBlock;\r\n                const id = teleportOut._tempEntryPointUniqueId;\r\n                if (id) {\r\n                    const source = map[id] as TeleportInBlock;\r\n                    if (source) {\r\n                        source.attachToEndpoint(teleportOut);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Connections - Starts with input blocks only (except if in \"merge\" mode where we scan all blocks)\r\n        for (let blockIndex = 0; blockIndex < source.blocks.length; blockIndex++) {\r\n            const parsedBlock = source.blocks[blockIndex];\r\n            const block = map[parsedBlock.id];\r\n\r\n            if (!block) {\r\n                continue;\r\n            }\r\n\r\n            if (block.inputs.length && parsedBlock.inputs.some((i: any) => i.targetConnectionName) && !merge) {\r\n                continue;\r\n            }\r\n            this._restoreConnections(block, source, map);\r\n        }\r\n\r\n        // Outputs\r\n        if (source.outputNodeId) {\r\n            this.outputBlock = map[source.outputNodeId] as GeometryOutputBlock;\r\n        }\r\n\r\n        // UI related info\r\n        if (source.locations || (source.editorData && source.editorData.locations)) {\r\n            const locations: {\r\n                blockId: number;\r\n                x: number;\r\n                y: number;\r\n            }[] = source.locations || source.editorData.locations;\r\n\r\n            for (const location of locations) {\r\n                if (map[location.blockId]) {\r\n                    location.blockId = map[location.blockId].uniqueId;\r\n                }\r\n            }\r\n\r\n            if (merge && this.editorData && this.editorData.locations) {\r\n                locations.concat(this.editorData.locations);\r\n            }\r\n\r\n            if (source.locations) {\r\n                this.editorData = {\r\n                    locations: locations,\r\n                };\r\n            } else {\r\n                this.editorData = source.editorData;\r\n                this.editorData.locations = locations;\r\n            }\r\n\r\n            const blockMap: number[] = [];\r\n\r\n            for (const key in map) {\r\n                blockMap[key] = map[key].uniqueId;\r\n            }\r\n\r\n            this.editorData.map = blockMap;\r\n        }\r\n\r\n        this.comment = source.comment;\r\n    }\r\n\r\n    private _restoreConnections(block: NodeGeometryBlock, source: any, map: { [key: number]: NodeGeometryBlock }) {\r\n        for (const outputPoint of block.outputs) {\r\n            for (const candidate of source.blocks) {\r\n                const target = map[candidate.id];\r\n\r\n                if (!target) {\r\n                    continue;\r\n                }\r\n\r\n                for (const input of candidate.inputs) {\r\n                    if (map[input.targetBlockId] === block && input.targetConnectionName === outputPoint.name) {\r\n                        const inputPoint = target.getInputByName(input.inputName);\r\n                        if (!inputPoint || inputPoint.isConnected) {\r\n                            continue;\r\n                        }\r\n\r\n                        outputPoint.connectTo(inputPoint, true);\r\n                        this._restoreConnections(target, source, map);\r\n                        continue;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Generate a string containing the code declaration required to create an equivalent of this geometry\r\n     * @returns a string\r\n     */\r\n    public generateCode() {\r\n        let alreadyDumped: NodeGeometryBlock[] = [];\r\n        const blocks: NodeGeometryBlock[] = [];\r\n        const uniqueNames: string[] = [\"const\", \"var\", \"let\"];\r\n        // Gets active blocks\r\n        if (this.outputBlock) {\r\n            this._gatherBlocks(this.outputBlock, blocks);\r\n        }\r\n\r\n        // Generate\r\n        let codeString = `let nodeGeometry = new BABYLON.NodeGeometry(\"${this.name || \"node geometry\"}\");\\n`;\r\n        for (const node of blocks) {\r\n            if (node.isInput && alreadyDumped.indexOf(node) === -1) {\r\n                codeString += node._dumpCode(uniqueNames, alreadyDumped);\r\n            }\r\n        }\r\n\r\n        if (this.outputBlock) {\r\n            // Connections\r\n            alreadyDumped = [];\r\n            codeString += \"// Connections\\n\";\r\n            codeString += this.outputBlock._dumpCodeForOutputConnections(alreadyDumped);\r\n\r\n            // Output nodes\r\n            codeString += \"// Output nodes\\n\";\r\n            codeString += `nodeGeometry.outputBlock = ${this.outputBlock._codeVariableName};\\n`;\r\n            codeString += `nodeGeometry.build();\\n`;\r\n        }\r\n\r\n        return codeString;\r\n    }\r\n\r\n    private _gatherBlocks(rootNode: NodeGeometryBlock, list: NodeGeometryBlock[]) {\r\n        if (list.indexOf(rootNode) !== -1) {\r\n            return;\r\n        }\r\n        list.push(rootNode);\r\n\r\n        for (const input of rootNode.inputs) {\r\n            const connectedPoint = input.connectedPoint;\r\n            if (connectedPoint) {\r\n                const block = connectedPoint.ownerBlock;\r\n                if (block !== rootNode) {\r\n                    this._gatherBlocks(block, list);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Teleportation\r\n        if (rootNode.isTeleportOut) {\r\n            const block = rootNode as TeleportOutBlock;\r\n            if (block.entryPoint) {\r\n                this._gatherBlocks(block.entryPoint, list);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clear the current geometry and set it to a default state\r\n     */\r\n    public setToDefault() {\r\n        this.clear();\r\n\r\n        this.editorData = null;\r\n\r\n        // Source\r\n        const dataBlock = new BoxBlock(\"Box\");\r\n        dataBlock.autoConfigure();\r\n\r\n        // Final output\r\n        const output = new GeometryOutputBlock(\"Geometry Output\");\r\n        dataBlock.geometry.connectTo(output.geometry);\r\n\r\n        this.outputBlock = output;\r\n    }\r\n\r\n    /**\r\n     * Makes a duplicate of the current geometry.\r\n     * @param name defines the name to use for the new geometry\r\n     * @returns the new geometry\r\n     */\r\n    public clone(name: string): NodeGeometry {\r\n        const serializationObject = this.serialize();\r\n\r\n        const clone = SerializationHelper.Clone(() => new NodeGeometry(name), this);\r\n        clone.name = name;\r\n\r\n        clone.parseSerializedObject(serializationObject);\r\n        clone._buildId = this._buildId;\r\n        clone.build(false);\r\n\r\n        return clone;\r\n    }\r\n\r\n    /**\r\n     * Serializes this geometry in a JSON representation\r\n     * @param selectedBlocks defines the list of blocks to save (if null the whole geometry will be saved)\r\n     * @returns the serialized geometry object\r\n     */\r\n    public serialize(selectedBlocks?: NodeGeometryBlock[]): any {\r\n        const serializationObject = selectedBlocks ? {} : SerializationHelper.Serialize(this);\r\n        serializationObject.editorData = JSON.parse(JSON.stringify(this.editorData)); // Copy\r\n\r\n        let blocks: NodeGeometryBlock[] = [];\r\n\r\n        if (selectedBlocks) {\r\n            blocks = selectedBlocks;\r\n        } else {\r\n            serializationObject.customType = \"BABYLON.NodeGeometry\";\r\n            if (this.outputBlock) {\r\n                serializationObject.outputNodeId = this.outputBlock.uniqueId;\r\n            }\r\n        }\r\n\r\n        // Blocks\r\n        serializationObject.blocks = [];\r\n\r\n        for (const block of blocks) {\r\n            serializationObject.blocks.push(block.serialize());\r\n        }\r\n\r\n        if (!selectedBlocks) {\r\n            for (const block of this.attachedBlocks) {\r\n                if (blocks.indexOf(block) !== -1) {\r\n                    continue;\r\n                }\r\n                serializationObject.blocks.push(block.serialize());\r\n            }\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Disposes the ressources\r\n     */\r\n    public dispose(): void {\r\n        for (const block of this.attachedBlocks) {\r\n            block.dispose();\r\n        }\r\n\r\n        this.attachedBlocks.length = 0;\r\n        this.onBuildObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Creates a new node geometry set to default basic configuration\r\n     * @param name defines the name of the geometry\r\n     * @returns a new NodeGeometry\r\n     */\r\n    public static CreateDefault(name: string) {\r\n        const nodeGeometry = new NodeGeometry(name);\r\n\r\n        nodeGeometry.setToDefault();\r\n        nodeGeometry.build();\r\n\r\n        return nodeGeometry;\r\n    }\r\n\r\n    /**\r\n     * Creates a node geometry from parsed geometry data\r\n     * @param source defines the JSON representation of the geometry\r\n     * @returns a new node geometry\r\n     */\r\n    public static Parse(source: any): NodeGeometry {\r\n        const nodeGeometry = SerializationHelper.Parse(() => new NodeGeometry(source.name), source, null);\r\n\r\n        nodeGeometry.parseSerializedObject(source);\r\n        nodeGeometry.build();\r\n\r\n        return nodeGeometry;\r\n    }\r\n\r\n    /**\r\n     * Creates a node geometry from a snippet saved by the node geometry editor\r\n     * @param snippetId defines the snippet to load\r\n     * @param nodeGeometry defines a node geometry to update (instead of creating a new one)\r\n     * @param skipBuild defines whether to build the node geometry\r\n     * @returns a promise that will resolve to the new node geometry\r\n     */\r\n    public static ParseFromSnippetAsync(snippetId: string, nodeGeometry?: NodeGeometry, skipBuild: boolean = false): Promise<NodeGeometry> {\r\n        if (snippetId === \"_BLANK\") {\r\n            return Promise.resolve(NodeGeometry.CreateDefault(\"blank\"));\r\n        }\r\n\r\n        return new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        const snippet = JSON.parse(JSON.parse(request.responseText).jsonPayload);\r\n                        const serializationObject = JSON.parse(snippet.nodeGeometry);\r\n\r\n                        if (!nodeGeometry) {\r\n                            nodeGeometry = SerializationHelper.Parse(() => new NodeGeometry(snippetId), serializationObject, null);\r\n                        }\r\n\r\n                        nodeGeometry.parseSerializedObject(serializationObject);\r\n                        nodeGeometry.snippetId = snippetId;\r\n\r\n                        try {\r\n                            if (!skipBuild) {\r\n                                nodeGeometry.build();\r\n                            }\r\n                            resolve(nodeGeometry);\r\n                        } catch (err) {\r\n                            reject(err);\r\n                        }\r\n                    } else {\r\n                        reject(\"Unable to load the snippet \" + snippetId);\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", this.SnippetUrl + \"/\" + snippetId.replace(/#/g, \"/\"));\r\n            request.send();\r\n        });\r\n    }\r\n}\r\n"]}