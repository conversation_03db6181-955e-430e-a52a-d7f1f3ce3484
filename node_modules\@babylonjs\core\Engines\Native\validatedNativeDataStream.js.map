{"version": 3, "file": "validatedNativeDataStream.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Native/validatedNativeDataStream.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAE/C,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAKtD,YAAY,CAAC,uBAAuB,GAAG;IACnC,IAAI,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE;QAC7C,OAAO,IAAI,yBAAyB,EAAE,CAAC;KAC1C;SAAM;QACH,OAAO,IAAI,gBAAgB,EAAE,CAAC;KACjC;AACL,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,yBAA0B,SAAQ,gBAAgB;IAC3D;QACI,KAAK,EAAE,CAAC;IACZ,CAAC;IAEe,WAAW,CAAC,KAAa;QACrC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QAC/D,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAEe,UAAU,CAAC,KAAa;QACpC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;QAC9D,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAEe,YAAY,CAAC,KAAa;QACtC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;QAChE,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAEe,gBAAgB,CAAC,MAAmB;QAChD,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;QACrE,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAEe,eAAe,CAAC,MAAkB;QAC9C,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;QACpE,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAEe,iBAAiB,CAAC,MAAoB;QAClD,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;QACtE,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAEe,eAAe,CAAC,MAAkB;QAC9C,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;QACnE,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAEe,YAAY,CAAC,KAAc;QACvC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QAC/D,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;CACJ", "sourcesContent": ["import { NativeEngine } from \"../nativeEngine\";\r\nimport type { NativeData } from \"./nativeDataStream\";\r\nimport { NativeDataStream } from \"./nativeDataStream\";\r\nimport type { INative } from \"./nativeInterfaces\";\r\n\r\ndeclare const _native: INative;\r\n\r\nNativeEngine._createNativeDataStream = function () {\r\n    if (_native.NativeDataStream.VALIDATION_ENABLED) {\r\n        return new ValidatedNativeDataStream();\r\n    } else {\r\n        return new NativeDataStream();\r\n    }\r\n};\r\n\r\n/**\r\n * Validated Native Data Stream\r\n */\r\nexport class ValidatedNativeDataStream extends NativeDataStream {\r\n    constructor() {\r\n        super();\r\n    }\r\n\r\n    public override writeUint32(value: number): void {\r\n        super.writeUint32(_native.NativeDataStream.VALIDATION_UINT_32);\r\n        super.writeUint32(value);\r\n    }\r\n\r\n    public override writeInt32(value: number): void {\r\n        super.writeUint32(_native.NativeDataStream.VALIDATION_INT_32);\r\n        super.writeInt32(value);\r\n    }\r\n\r\n    public override writeFloat32(value: number): void {\r\n        super.writeUint32(_native.NativeDataStream.VALIDATION_FLOAT_32);\r\n        super.writeFloat32(value);\r\n    }\r\n\r\n    public override writeUint32Array(values: Uint32Array): void {\r\n        super.writeUint32(_native.NativeDataStream.VALIDATION_UINT_32_ARRAY);\r\n        super.writeUint32Array(values);\r\n    }\r\n\r\n    public override writeInt32Array(values: Int32Array): void {\r\n        super.writeUint32(_native.NativeDataStream.VALIDATION_INT_32_ARRAY);\r\n        super.writeInt32Array(values);\r\n    }\r\n\r\n    public override writeFloat32Array(values: Float32Array): void {\r\n        super.writeUint32(_native.NativeDataStream.VALIDATION_FLOAT_32_ARRAY);\r\n        super.writeFloat32Array(values);\r\n    }\r\n\r\n    public override writeNativeData(handle: NativeData) {\r\n        super.writeUint32(_native.NativeDataStream.VALIDATION_NATIVE_DATA);\r\n        super.writeNativeData(handle);\r\n    }\r\n\r\n    public override writeBoolean(value: boolean) {\r\n        super.writeUint32(_native.NativeDataStream.VALIDATION_BOOLEAN);\r\n        super.writeBoolean(value);\r\n    }\r\n}\r\n"]}