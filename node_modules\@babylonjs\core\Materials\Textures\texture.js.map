{"version": 3, "file": "texture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/texture.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AACtE,OAAO,EAAE,WAAW,EAAE,MAAM,sCAAsC,CAAC;AACnE,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAC/D,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAGlD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,kBAAkB,EAAE,MAAM,+BAA+B,CAAC;AACnE,OAAO,EAAE,KAAK,EAAE,MAAM,wBAAwB,CAAC;AAC/C,OAAO,EAAE,yBAAyB,EAAE,MAAM,wBAAwB,CAAC;AACnE,OAAO,EAAE,+BAA+B,EAAE,oCAAoC,EAAE,MAAM,sBAAsB,CAAC;AAC7G,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AASzE,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AAiD1E;;;GAGG;AACH,MAAM,OAAO,OAAQ,SAAQ,WAAW;IA0CpC;;OAEG;IACI,MAAM,CAAC,mBAAmB,CAC7B,IAAsB,EACtB,GAAyC,EACzC,KAAsB,EACtB,eAAe,GAAG,KAAK,EACvB,OAAO,GAAG,KAAK,EACf,eAAuB,OAAO,CAAC,sBAAsB,EACrD,WAA0C,EAAE,EAC5C,OAA+D,EAC/D,SAAiB,SAAS,CAAC,kBAAkB;QAE7C,MAAM,WAAW,CAAC,cAAc,CAAC,CAAC;IACtC,CAAC;IAuJD;;OAEG;IACH,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAoDD,mFAAmF;IACnF,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAQD;;;OAGG;IACH,IAAW,UAAU,CAAC,KAAc;QAChC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAED,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,YACI,GAAqB,EACrB,aAA4C,EAC5C,iBAAqD,EACrD,OAAiB,EACjB,eAAuB,OAAO,CAAC,sBAAsB,EACrD,SAA+B,IAAI,EACnC,UAAiE,IAAI,EACrE,SAAmG,IAAI,EACvG,eAAwB,KAAK,EAC7B,MAAe,EACf,QAAiB,EACjB,aAAmB,EACnB,aAAsB,EACtB,eAAwB;QAExB,KAAK,CAAC,aAAa,CAAC,CAAC;QA5MzB;;WAEG;QAEI,QAAG,GAAqB,IAAI,CAAC;QAEpC;;;WAGG;QAEI,YAAO,GAAG,CAAC,CAAC;QAEnB;;;WAGG;QAEI,YAAO,GAAG,CAAC,CAAC;QAEnB;;;WAGG;QAEI,WAAM,GAAG,GAAG,CAAC;QAEpB;;;WAGG;QAEI,WAAM,GAAG,GAAG,CAAC;QAEpB;;;;WAIG;QAEI,SAAI,GAAG,CAAC,CAAC;QAEhB;;;;WAIG;QAEI,SAAI,GAAG,CAAC,CAAC;QAEhB;;;;WAIG;QAEI,SAAI,GAAG,CAAC,CAAC;QAEhB;;WAEG;QAEI,oBAAe,GAAG,GAAG,CAAC;QAE7B;;WAEG;QAEI,oBAAe,GAAG,GAAG,CAAC;QAE7B;;WAEG;QAEI,oBAAe,GAAG,GAAG,CAAC;QAE7B;;WAEG;QAEI,qCAAgC,GAAG,KAAK,CAAC;QAShD;;;WAGG;QACI,gCAA2B,GAA6B,IAAI,CAAC;QAEpE,gBAAgB;QACT,cAAS,GAAY,KAAK,CAAC;QAClC,gBAAgB;QACT,aAAQ,GAAY,KAAK,CAAC;QACzB,yBAAoB,GAAqB,IAAI,CAAC;QAC9C,yBAAoB,GAAqB,IAAI,CAAC;QAC9C,0BAAqB,GAAqB,IAAI,CAAC;QAC/C,QAAG,GAAsB,IAAI,CAAC;QAC9B,QAAG,GAAsB,IAAI,CAAC;QAC9B,QAAG,GAAsB,IAAI,CAAC;QAE9B,mBAAc,GAAW,CAAC,CAAC,CAAC;QAC5B,mBAAc,GAAW,CAAC,CAAC,CAAC;QAC5B,kBAAa,GAAW,CAAC,CAAC;QAC1B,kBAAa,GAAW,CAAC,CAAC;QAC1B,gBAAW,GAAW,CAAC,CAAC,CAAC;QACzB,gBAAW,GAAW,CAAC,CAAC,CAAC;QACzB,gBAAW,GAAW,CAAC,CAAC,CAAC;QACzB,wCAAmC,GAAW,CAAC,CAAC,CAAC;QACjD,2BAAsB,GAAW,CAAC,CAAC,CAAC;QACpC,2BAAsB,GAAW,CAAC,CAAC,CAAC;QACpC,2BAAsB,GAAW,CAAC,CAAC,CAAC;QACpC,4CAAuC,GAAY,KAAK,CAAC;QAEzD,mCAA8B,GAAqB,IAAI,CAAC;QACxD,6BAAwB,GAAG,CAAC,CAAC,CAAC;QAC9B,6BAAwB,GAAG,CAAC,CAAC,CAAC;QAC9B,4BAAuB,GAAG,CAAC,CAAC;QAC5B,4BAAuB,GAAG,CAAC,CAAC;QAC5B,qCAAgC,GAAG,CAAC,CAAC,CAAC;QAE9C,gBAAgB;QACT,YAAO,GAA6F,IAAI,CAAC;QACxG,kBAAa,GAAY,KAAK,CAAC;QAC7B,YAAO,GAAqB,IAAI,CAAC;QACnC,mBAAc,GAAyB,IAAI,CAAC;QAC5C,oBAAe,GAAyB,IAAI,CAAC;QAarD;;WAEG;QACI,qBAAgB,GAAwB,IAAI,UAAU,EAAW,CAAC;QAE/D,gBAAW,GAAY,IAAI,CAAC;QAyDlC,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAEf,IAAI,QAAiB,CAAC;QACtB,IAAI,aAAa,GAAY,KAAK,CAAC;QACnC,IAAI,eAAe,GAA8B,IAAI,CAAC;QACtD,IAAI,UAAU,GAAG,IAAI,CAAC;QAEtB,IAAI,OAAO,iBAAiB,KAAK,QAAQ,IAAI,iBAAiB,KAAK,IAAI,EAAE;YACrE,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,IAAI,KAAK,CAAC;YAC/C,OAAO,GAAG,iBAAiB,CAAC,OAAO,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACvG,YAAY,GAAG,iBAAiB,CAAC,YAAY,IAAI,OAAO,CAAC,sBAAsB,CAAC;YAChF,MAAM,GAAG,iBAAiB,CAAC,MAAM,IAAI,IAAI,CAAC;YAC1C,OAAO,GAAG,iBAAiB,CAAC,OAAO,IAAI,IAAI,CAAC;YAC5C,MAAM,GAAG,iBAAiB,CAAC,MAAM,IAAI,IAAI,CAAC;YAC1C,YAAY,GAAG,iBAAiB,CAAC,YAAY,IAAI,KAAK,CAAC;YACvD,MAAM,GAAG,iBAAiB,CAAC,MAAM,CAAC;YAClC,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC;YACtC,aAAa,GAAG,iBAAiB,CAAC,aAAa,CAAC;YAChD,aAAa,GAAG,iBAAiB,CAAC,aAAa,CAAC;YAChD,aAAa,GAAG,iBAAiB,CAAC,aAAa,IAAI,KAAK,CAAC;YACzD,eAAe,GAAG,iBAAiB,CAAC,eAAe,IAAI,IAAI,CAAC;YAC5D,UAAU,GAAG,iBAAiB,CAAC,UAAU,IAAI,UAAU,CAAC;SAC3D;aAAM;YACH,QAAQ,GAAG,CAAC,CAAC,iBAAiB,CAAC;SAClC;QAED,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAClH,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC;QACzC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;SACzB;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QAED,MAAM,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE3D,MAAM,IAAI,GAAG,GAAG,EAAE;YACd,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;oBAC7B,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;oBAClB,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;iBACrB;gBAED,sDAAsD;gBACtD,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,KAAK,IAAI,EAAE;oBACrC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;oBACxC,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;iBACrC;gBACD,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,KAAK,IAAI,EAAE;oBACrC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;oBACxC,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;iBACrC;gBACD,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,KAAK,IAAI,EAAE;oBACrC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;oBACxC,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;iBACrC;aACJ;YAED,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,EAAE;gBACtC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;aAC/C;YACD,IAAI,MAAM,EAAE;gBACR,MAAM,EAAE,CAAC;aACZ;YAED,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,KAAK,EAAE;gBAC3B,KAAK,CAAC,mBAAmB,EAAE,CAAC;aAC/B;QACL,CAAC,CAAC;QAEF,MAAM,YAAY,GAAG,CAAC,OAAgB,EAAE,SAAe,EAAE,EAAE;YACvD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,YAAY,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;YAC3C,IAAI,OAAO,EAAE;gBACT,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;aAC/B;YACD,OAAO,CAAC,4BAA4B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/D,CAAC,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE;YAC/B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,eAAe,GAAG,YAAY,CAAC;YACpC,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,GAAG,eAAe,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEnI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBAC3C,IAAI;oBACA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,aAAa,CAChC,IAAI,CAAC,GAAG,EACR,QAAQ,EACR,IAAI,CAAC,QAAQ,EACb,KAAK,EACL,YAAY,EACZ,IAAI,EACJ,YAAY,EACZ,IAAI,CAAC,OAAO,EACZ,SAAS,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,gBAAgB,EACrB,QAAQ,EACR,aAAa,EACb,aAAa,EACb,aAAa,CAChB,CAAC;iBACL;gBAAC,OAAO,CAAC,EAAE;oBACR,YAAY,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;oBACjC,MAAM,CAAC,CAAC;iBACX;gBACD,IAAI,YAAY,EAAE;oBACd,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;iBACvB;aACJ;iBAAM;gBACH,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,wBAAwB,CAAC;gBAEzD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,IAAI,CAAC,eAAe,GAAG,YAAY,CAAC;aACvC;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACvB,WAAW,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;aAC1C;iBAAM;gBACH,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAChE,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;oBACtC,YAAY,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC;oBACrC,IAAI,CAAC,QAAQ,EAAE,kBAAkB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBAC3D,CAAC,CAAC,CAAC;aACN;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CACZ,GAAW,EACX,SAAmG,IAAI,EACvG,MAAmB,EACnB,eAAwB;QAExB,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,QAAQ,EAAG,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBAClF,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC7C,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;SACnB;QACD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,wBAAwB,CAAC;QAEzD,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;SAChC;QACD,IAAI,CAAC,SAAS,EAAE,CAAC;IACrB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,CAAC,wBAAwB,EAAE;YAC5D,OAAO;SACV;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,qBAAqB,CAAC;QACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEjI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,QAAQ,GAAG,KAAK;iBAChB,SAAS,EAAE;iBACX,aAAa,CACV,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,QAAQ,EACb,KAAK,EACL,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,OAAO,EACZ,IAAI,EACJ,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,CACtB,CAAC;YACN,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;aACvB;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;oBACvB,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;iBACjD;qBAAM;oBACH,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;iBAC7D;aACJ;SACJ;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IAEO,+BAA+B,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAU;QAC/E,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC;QACxB,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC;QAExB,CAAC,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;QAC/C,CAAC,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;QAC/C,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC;QAE1B,OAAO,CAAC,mCAAmC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,oBAAqB,EAAE,CAAC,CAAC,CAAC;QAEpF,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QACvE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QACvE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACI,2BAA2B,CAAC,OAA0B;QACzD,OAAO,CACH,OAAO,KAAK,IAAI;YAChB,IAAI,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO;YAChC,IAAI,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO;YAChC,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;YAC9B,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;YAC9B,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;YAC1B,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;YAC1B,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAC7B,CAAC;IACN,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,KAAK,GAAG,CAAC;QAC7B,IACI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,cAAc;YACpC,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,cAAc;YACpC,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,IAAI,CAAC,aAAa;YAC1C,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa;YAClC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW;YAC9B,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW;YAC9B,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW;YAC9B,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,sBAAsB;YACpD,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,sBAAsB;YACpD,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,sBAAsB;YACpD,IAAI,CAAC,gCAAgC,KAAK,IAAI,CAAC,uCAAuC,EACxF;YACE,OAAO,IAAI,CAAC,oBAAqB,CAAC;SACrC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,eAAe,CAAC;QACnD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,eAAe,CAAC;QACnD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,eAAe,CAAC;QACnD,IAAI,CAAC,uCAAuC,GAAG,IAAI,CAAC,gCAAgC,CAAC;QAErF,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC1D,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;YAC1C,IAAI,CAAC,oBAAoB,GAAG,IAAI,MAAM,EAAE,CAAC;YACzC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;SAC7B;QAED,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,oBAAqB,CAAC,CAAC;QAE9F,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACvC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,IAAI,CAAC,sBAAsB,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACxI,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACrI,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACrF,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAE3F,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAqB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC1F,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACzF,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACzF,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAEzF,yJAAyJ;YACzJ,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACvJ;aAAM;YACH,IAAI,CAAC,+BAA+B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAI,CAAC,CAAC;YACzD,IAAI,CAAC,+BAA+B,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAI,CAAC,CAAC;YAC3D,IAAI,CAAC,+BAA+B,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,GAAI,CAAC,CAAC;YAE3D,IAAI,CAAC,GAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAI,CAAC,CAAC;YACrC,IAAI,CAAC,GAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAI,CAAC,CAAC;YAErC,MAAM,CAAC,eAAe,CAClB,IAAI,CAAC,GAAI,CAAC,CAAC,EACX,IAAI,CAAC,GAAI,CAAC,CAAC,EACX,IAAI,CAAC,GAAI,CAAC,CAAC,EACX,GAAG,EACH,IAAI,CAAC,GAAI,CAAC,CAAC,EACX,IAAI,CAAC,GAAI,CAAC,CAAC,EACX,IAAI,CAAC,GAAI,CAAC,CAAC,EACX,GAAG,EACH,IAAI,CAAC,GAAI,CAAC,CAAC,EACX,IAAI,CAAC,GAAI,CAAC,CAAC,EACX,IAAI,CAAC,GAAI,CAAC,CAAC,EACX,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,IAAI,CAAC,oBAAoB,CAC5B,CAAC;SACL;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC,oBAAoB,CAAC;SACpC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,0JAA0J;YAC1J,iHAAiH;YACjH,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;SACN;QAED,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,0BAA0B;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC,8BAA+B,CAAC;SAC/C;QAED,IACI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,wBAAwB;YAC9C,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,wBAAwB;YAC9C,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,uBAAuB;YAC5C,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,uBAAuB;YAC5C,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,gCAAgC,EAChE;YACE,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,CAAC,eAAe,EAAE;gBAClD,IAAI,IAAI,CAAC,mCAAmC,KAAK,KAAK,CAAC,mBAAmB,EAAE,CAAC,UAAU,EAAE;oBACrF,OAAO,IAAI,CAAC,8BAA+B,CAAC;iBAC/C;aACJ;iBAAM;gBACH,OAAO,IAAI,CAAC,8BAA+B,CAAC;aAC/C;SACJ;QAED,IAAI,CAAC,IAAI,CAAC,8BAA8B,EAAE;YACtC,IAAI,CAAC,8BAA8B,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;SACvD;QAED,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;SAC9C;QAED,MAAM,2BAA2B,GAAG,IAAI,CAAC,gCAAgC,KAAK,IAAI,CAAC,eAAe,CAAC;QAEnG,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7C,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3C,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC,eAAe,CAAC;QAE7D,QAAQ,IAAI,CAAC,eAAe,EAAE;YAC1B,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;gBACtB,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBACpD,IAAI,CAAC,8BAA+B,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;gBACtD,IAAI,CAAC,8BAA+B,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;gBACtD,IAAI,CAAC,8BAA+B,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;gBACxD,IAAI,CAAC,8BAA+B,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;gBAC9D,MAAM;aACT;YACD,KAAK,OAAO,CAAC,eAAe,CAAC,CAAC;gBAC1B,MAAM,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAEpI,MAAM,gBAAgB,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;gBACrD,IAAI,CAAC,mCAAmC,GAAG,gBAAgB,CAAC,UAAU,CAAC;gBACvE,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAChG,MAAM;aACT;YACD;gBACI,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAC1D,MAAM;SACb;QAED,IAAI,2BAA2B,EAAE;YAC7B,2GAA2G;YAC3G,uNAAuN;YACvN,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;SACN;QAED,OAAO,IAAI,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,OAAO,GAA4B;YACrC,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACzD,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,MAAM,EAAE,IAAI,CAAC,aAAa;YAC1B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,aAAa,EAAE,IAAI,CAAC,cAAc;SACrC,CAAC;QAEF,OAAO,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE;YAClC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;QAC3F,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC;QAE5B,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YAC3B,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;gBAC/B,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;aAClB;SACJ;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE;YACzD,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;SACjB;QAED,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QAEvF,IAAI,CAAC,mBAAmB,EAAE;YACtB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,qBAAqB,EAAE;YAC3D,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAK,IAAI,CAAC,OAAkB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,EAAE;gBACvF,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC;gBAChD,mBAAmB,CAAC,IAAI,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;aAC5E;iBAAM,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,YAAY,UAAU,EAAE;gBACvF,mBAAmB,CAAC,YAAY,GAAG,wBAAwB,GAAG,yBAAyB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACzG;iBAAM,IAAI,OAAO,CAAC,qBAAqB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE;gBAC5G,mBAAmB,CAAC,YAAY;oBAC5B,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,+BAA+B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,oCAAoC,CAAC,IAAI,CAAC,CAAC;aAC3J;SACJ;QAED,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5C,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACrD,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzD,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzD,IAAI,OAAO,CAAC,iCAAiC,EAAE;YAC3C,mBAAmB,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,EAAE,QAAQ,IAAI,SAAS,CAAC;SACtF;QACD,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAE9C,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;QAEtB,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAE9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,aAAkB,EAAE,KAAY,EAAE,OAAe;QACjE,IAAI,aAAa,CAAC,UAAU,EAAE;YAC1B,MAAM,aAAa,GAAG,kBAAkB,CAAC,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAC/E,uBAAuB;YACvB,MAAM,mBAAmB,GAAQ,aAAa,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YACpF,IAAI,aAAa,CAAC,YAAY,IAAI,mBAAmB,CAAC,kBAAkB,IAAI,mBAAmB,CAAC,aAAa,EAAE;gBAC3G,IAAI,mBAAmB,CAAC,aAAa,KAAK,aAAa,CAAC,YAAY,EAAE;oBAClE,mBAAmB,CAAC,kBAAkB,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;iBACtE;aACJ;YACD,OAAO,mBAAmB,CAAC;SAC9B;QAED,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE;YACvD,OAAO,OAAO,CAAC,kBAAkB,CAAC,aAAa,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACpE;QAED,MAAM,0BAA0B,GAAG,aAAa,CAAC,uBAAuB,KAAK,SAAS,CAAC;QAEvF,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,cAAc,IAAI,CAAC,0BAA0B,EAAE;YACrF,OAAO,IAAI,CAAC;SACf;QAED,IAAI,eAA4C,CAAC;QAEjD,IAAI,0BAA0B,EAAE;YAC5B,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,sBAAsB,EAAE,CAAC;YACzD,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE;gBACzB,IAAI,OAAO,CAAC,QAAQ,KAAK,aAAa,CAAC,uBAAuB,EAAE;oBAC5D,eAAe,GAAG,OAAO,CAAC;oBAC1B,MAAM;iBACT;aACJ;SACJ;QAED,MAAM,QAAQ,GAAG,CAAC,OAAuB,EAAE,EAAE;YACzC,cAAc;YACd,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE;gBAC7B,OAAO,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;gBACrC,OAAO,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;gBACrC,OAAO,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;aACxC;YAED,uBAAuB;YACvB,IAAI,aAAa,CAAC,YAAY,EAAE;gBAC5B,MAAM,QAAQ,GAAW,aAAa,CAAC,YAAY,CAAC;gBACpD,IAAI,OAAO,IAAI,OAAO,CAAC,YAAY,KAAK,QAAQ,EAAE;oBAC9C,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;iBACxC;aACJ;YACD,aAAa;YACb,IAAI,OAAO,IAAI,aAAa,CAAC,UAAU,EAAE;gBACrC,KAAK,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,aAAa,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,EAAE,EAAE;oBAC7F,MAAM,eAAe,GAAG,aAAa,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;oBACjE,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAAmB,CAAC,CAAC;oBACpD,IAAI,aAAa,EAAE;wBACf,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;qBACjE;iBACJ;aACJ;YAED,IAAI,0BAA0B,IAAI,CAAC,eAAe,EAAE;gBAChD,OAAO,EAAE,QAAQ,EAAE,YAAY,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;aAC1E;QACL,CAAC,CAAC;QAEF,MAAM,OAAO,GAAG,mBAAmB,CAAC,KAAK,CACrC,GAAG,EAAE;YACD,IAAI,eAAe,GAAY,IAAI,CAAC;YACpC,IAAI,aAAa,CAAC,QAAQ,EAAE;gBACxB,eAAe,GAAG,KAAK,CAAC;aAC3B;YACD,IAAI,aAAa,CAAC,WAAW,EAAE;gBAC3B,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,gBAAgB,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;gBACxH,aAAa,CAAC,kBAAkB,GAAG,aAAa,CAAC,UAAU,CAAC;gBAC5D,aAAa,CAAC,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;gBACvE,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACxB,OAAO,aAAa,CAAC;aACxB;iBAAM,IAAI,aAAa,CAAC,cAAc,EAAE;gBACrC,IAAI,mBAAmB,GAAkC,IAAI,CAAC;gBAC9D,IAAI,aAAa,CAAC,MAAM,EAAE;oBACtB,wFAAwF;oBACxF,IAAI,KAAK,CAAC,gBAAgB,EAAE;wBACxB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;4BAChE,MAAM,KAAK,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;4BAC5C,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,EAAE;gCACnC,OAAO,KAAK,CAAC,WAAW,CAAC;6BAC5B;yBACJ;qBACJ;iBACJ;qBAAM;oBACH,mBAAmB,GAAG,OAAO,CAAC,0BAA0B,CACpD,aAAa,CAAC,IAAI,EAClB,aAAa,CAAC,gBAAgB,EAC9B,KAAK,EACL,eAAe,EACf,aAAa,CAAC,cAAc,IAAI,CAAC,CACpC,CAAC;oBACF,mBAAmB,CAAC,kBAAkB,GAAG,aAAa,CAAC,UAAU,CAAC;iBACrE;gBACD,QAAQ,CAAC,mBAAmB,CAAC,CAAC;gBAC9B,OAAO,mBAAmB,CAAC;aAC9B;iBAAM,IAAI,aAAa,CAAC,OAAO,EAAE;gBAC9B,MAAM,OAAO,GAAG,OAAO,CAAC,mBAAmB,CACvC,OAAO,GAAG,CAAC,aAAa,CAAC,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,EACnD,OAAO,GAAG,CAAC,aAAa,CAAC,GAAG,IAAI,aAAa,CAAC,GAAG,CAAC,EAClD,KAAK,EACL,eAAe,EACf,aAAa,CAAC,OAAO,EACrB,aAAa,CAAC,YAAY,EAC1B,aAAa,CAAC,QAAQ,IAAI,EAAE,CAC/B,CAAC;gBACF,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAClB,OAAO,OAAO,CAAC;aAClB;iBAAM;gBACH,IAAI,OAAgB,CAAC;gBAErB,IAAI,aAAa,CAAC,YAAY,IAAI,CAAC,eAAe,EAAE;oBAChD,oFAAoF;oBACpF,OAAO,GAAG,OAAO,CAAC,sBAAsB,CACpC,aAAa,CAAC,YAAY,EAC1B,aAAa,CAAC,YAAY,EAC1B,KAAK,EACL,CAAC,eAAe,EAChB,aAAa,CAAC,OAAO,EACrB,aAAa,CAAC,YAAY,EAC1B,GAAG,EAAE;wBACD,QAAQ,CAAC,OAAO,CAAC,CAAC;oBACtB,CAAC,EACD,aAAa,CAAC,cAAc,IAAI,CAAC,EACjC,aAAa,CAAC,cAAc,IAAI,KAAK,CACxC,CAAC;oBAEF,4CAA4C;oBAC5C,OAAO,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;iBACrC;qBAAM;oBACH,IAAI,GAAW,CAAC;oBAChB,IAAI,aAAa,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE;wBACzG,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC;qBAC5B;yBAAM;wBACH,GAAG,GAAG,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC;qBACtC;oBAED,IAAI,aAAa,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,qBAAqB,CAAC,EAAE;wBAC/F,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;qBAC3B;oBAED,MAAM,OAAO,GAA4B;wBACrC,QAAQ,EAAE,CAAC,eAAe;wBAC1B,OAAO,EAAE,aAAa,CAAC,OAAO;wBAC9B,YAAY,EAAE,aAAa,CAAC,YAAY;wBACxC,MAAM,EAAE,GAAG,EAAE;4BACT,QAAQ,CAAC,OAAO,CAAC,CAAC;wBACtB,CAAC;wBACD,eAAe;qBAClB,CAAC;oBAEF,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;iBAC9C;gBAED,OAAO,OAAO,CAAC;aAClB;QACL,CAAC,EACD,aAAa,EACb,KAAK,CACR,CAAC;QAEF,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,MAAM,CAAC,sBAAsB,CAChC,IAAY,EACZ,IAAY,EACZ,KAAY,EACZ,iBAAqD,EACrD,OAAiB,EACjB,eAAuB,OAAO,CAAC,sBAAsB,EACrD,SAA+B,IAAI,EACnC,UAAgC,IAAI,EACpC,SAAiB,SAAS,CAAC,kBAAkB,EAC7C,aAAsB;QAEtB,OAAO,IAAI,OAAO,CAAC,OAAO,GAAG,IAAI,EAAE,KAAK,EAAE,iBAAiB,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IACnK,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACI,MAAM,CAAC,kBAAkB,CAC5B,IAAY,EACZ,MAAW,EACX,KAAY,EACZ,eAAwB,KAAK,EAC7B,iBAAqD,EACrD,UAAmB,IAAI,EACvB,eAAuB,OAAO,CAAC,sBAAsB,EACrD,SAA+B,IAAI,EACnC,UAAiE,IAAI,EACrE,SAAiB,SAAS,CAAC,kBAAkB,EAC7C,aAAsB;QAEtB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,EAAE;YAC/B,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC;SACzB;QAED,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,iBAAiB,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IAClK,CAAC;;AAxlCD;;GAEG;AACW,wBAAgB,GAAG,IAAI,AAAP,CAAQ;AAEtC;;;GAGG;AACW,6BAAqB,GAAG,KAAK,AAAR,CAAS;AAE5C;;GAEG;AACW,oCAA4B,GAAG,IAAI,UAAU,EAAe,AAAhC,CAAiC;AAE3E,gBAAgB;AACF,yCAAiC,GAAG,KAAK,AAAR,CAAS;AAExD;;GAEG;AACH,6DAA6D;AAC/C,0BAAkB,GAAG,CAAC,WAAgB,EAAE,KAAY,EAAE,OAAe,EAAe,EAAE;IAChG,MAAM,WAAW,CAAC,aAAa,CAAC,CAAC;AACrC,CAAC,AAF+B,CAE9B;AACF;;GAEG;AACH,6DAA6D;AAC/C,qBAAa,GAAG,CAAC,IAAY,EAAE,gBAAwB,EAAE,KAAY,EAAE,eAAwB,EAAiB,EAAE;IAC5H,MAAM,WAAW,CAAC,eAAe,CAAC,CAAC;AACvC,CAAC,AAF0B,CAEzB;AACF;;GAEG;AACH,6DAA6D;AAC/C,kCAA0B,GAAG,CAAC,IAAY,EAAE,gBAAwB,EAAE,KAAY,EAAE,eAAwB,EAAE,aAAsB,EAAuB,EAAE;IACvK,MAAM,WAAW,CAAC,qBAAqB,CAAC,CAAC;AAC7C,CAAC,AAFuC,CAEtC;AAmBF,4DAA4D;AACrC,4BAAoB,GAAG,SAAS,CAAC,4BAA4B,AAAzC,CAA0C;AACrF,kEAAkE;AAC3C,iCAAyB,GAAG,SAAS,CAAC,iCAAiC,AAA9C,CAA+C,CAAC,8DAA8D;AAE9J,2DAA2D;AACpC,6BAAqB,GAAG,SAAS,CAAC,6BAA6B,AAA1C,CAA2C;AACvF,kEAAkE;AAC3C,gCAAwB,GAAG,SAAS,CAAC,gCAAgC,AAA7C,CAA8C,CAAC,8DAA8D;AAE5J,kEAAkE;AAC3C,8BAAsB,GAAG,SAAS,CAAC,8BAA8B,AAA3C,CAA4C;AACzF,kEAAkE;AAC3C,+BAAuB,GAAG,SAAS,CAAC,+BAA+B,AAA5C,CAA6C,CAAC,8DAA8D;AAE1J,wDAAwD;AACjC,kCAA0B,GAAG,SAAS,CAAC,kCAAkC,AAA/C,CAAgD;AACjG,uDAAuD;AAChC,iCAAyB,GAAG,SAAS,CAAC,iCAAiC,AAA9C,CAA+C;AAC/F,sDAAsD;AAC/B,gCAAwB,GAAG,SAAS,CAAC,gCAAgC,AAA7C,CAA8C;AAC7F,oDAAoD;AAC7B,sBAAc,GAAG,SAAS,CAAC,sBAAsB,AAAnC,CAAoC;AACzE,qDAAqD;AAC9B,uBAAe,GAAG,SAAS,CAAC,uBAAuB,AAApC,CAAqC;AAC3E,uDAAuD;AAChC,iCAAyB,GAAG,SAAS,CAAC,iCAAiC,AAA9C,CAA+C;AAC/F,sDAAsD;AAC/B,gCAAwB,GAAG,SAAS,CAAC,gCAAgC,AAA7C,CAA8C;AAC7F,mDAAmD;AAC5B,qBAAa,GAAG,SAAS,CAAC,qBAAqB,AAAlC,CAAmC;AACvE,oDAAoD;AAC7B,sBAAc,GAAG,SAAS,CAAC,sBAAsB,AAAnC,CAAoC;AAEzE,gCAAgC;AACT,qBAAa,GAAG,SAAS,CAAC,qBAAqB,AAAlC,CAAmC;AACvE,iCAAiC;AACV,sBAAc,GAAG,SAAS,CAAC,sBAAsB,AAAnC,CAAoC;AACzE,8BAA8B;AACP,mBAAW,GAAG,SAAS,CAAC,mBAAmB,AAAhC,CAAiC;AACnE,6BAA6B;AACN,kBAAU,GAAG,SAAS,CAAC,kBAAkB,AAA/B,CAAgC;AACjE,kCAAkC;AACX,uBAAe,GAAG,SAAS,CAAC,uBAAuB,AAApC,CAAqC;AAC3E,qCAAqC;AACd,mBAAW,GAAG,SAAS,CAAC,mBAAmB,AAAhC,CAAiC;AACnE,qCAAqC;AACd,qBAAa,GAAG,SAAS,CAAC,qBAAqB,AAAlC,CAAmC;AACvE,uCAAuC;AAChB,4BAAoB,GAAG,SAAS,CAAC,4BAA4B,AAAzC,CAA0C;AACrF,6CAA6C;AACtB,kCAA0B,GAAG,SAAS,CAAC,kCAAkC,AAA/C,CAAgD;AACjG,sDAAsD;AAC/B,2CAAmC,GAAG,SAAS,CAAC,2CAA2C,AAAxD,CAAyD;AAEnH,mDAAmD;AAC5B,yBAAiB,GAAG,SAAS,CAAC,yBAAyB,AAAtC,CAAuC;AAC/E,+CAA+C;AACxB,wBAAgB,GAAG,SAAS,CAAC,wBAAwB,AAArC,CAAsC;AAC7E,wCAAwC;AACjB,0BAAkB,GAAG,SAAS,CAAC,0BAA0B,AAAvC,CAAwC;AAEjF;;GAEG;AACW,6BAAqB,GAAG,KAAK,AAAR,CAAS;AAMrC;IADN,SAAS,EAAE;oCACwB;AAO7B;IADN,SAAS,EAAE;wCACO;AAOZ;IADN,SAAS,EAAE;wCACO;AAOZ;IADN,SAAS,EAAE;uCACQ;AAOb;IADN,SAAS,EAAE;uCACQ;AAQb;IADN,SAAS,EAAE;qCACI;AAQT;IADN,SAAS,EAAE;qCACI;AAQT;IADN,SAAS,EAAE;qCACI;AAMT;IADN,SAAS,EAAE;gDACiB;AAMtB;IADN,SAAS,EAAE;gDACiB;AAMtB;IADN,SAAS,EAAE;gDACiB;AAMtB;IADN,SAAS,EAAE;iEACoC;AA8EhD;IADC,SAAS,EAAE;yCAGX;AA8zBL,+BAA+B;AAC/B,aAAa,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;AAC1C,mBAAmB,CAAC,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC", "sourcesContent": ["import { serialize } from \"../../Misc/decorators\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Matrix, TmpVectors, Vector3 } from \"../../Maths/math.vector\";\r\nimport { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { GetClass, RegisterClass } from \"../../Misc/typeStore\";\r\nimport { _WarnImport } from \"../../Misc/devTools\";\r\nimport type { IInspectable } from \"../../Misc/iInspectable\";\r\nimport type { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport { TimingTools } from \"../../Misc/timingTools\";\r\nimport { InstantiationTools } from \"../../Misc/instantiationTools\";\r\nimport { Plane } from \"../../Maths/math.plane\";\r\nimport { EncodeArrayBufferToBase64 } from \"../../Misc/stringTools\";\r\nimport { GenerateBase64StringFromTexture, GenerateBase64StringFromTextureAsync } from \"../../Misc/copyTools\";\r\nimport { CompatibilityOptions } from \"../../Compat/compatibilityOptions\";\r\nimport type { InternalTexture } from \"./internalTexture\";\r\n\r\nimport type { CubeTexture } from \"../../Materials/Textures/cubeTexture\";\r\nimport type { MirrorTexture } from \"../../Materials/Textures/mirrorTexture\";\r\nimport type { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { VideoTexture, VideoTextureSettings } from \"./videoTexture\";\r\n\r\nimport { SerializationHelper } from \"../../Misc/decorators.serialization\";\r\n\r\n/**\r\n * Defines the available options when creating a texture\r\n */\r\nexport interface ITextureCreationOptions {\r\n    /** Defines if the texture will require mip maps or not (default: false) */\r\n    noMipmap?: boolean;\r\n\r\n    /** Defines if the texture needs to be inverted on the y axis during loading (default: true) */\r\n    invertY?: boolean;\r\n\r\n    /** Defines the sampling mode we want for the texture while fetching from it (Texture.NEAREST_SAMPLINGMODE...) (default: Texture.TRILINEAR_SAMPLINGMODE) */\r\n    samplingMode?: number;\r\n\r\n    /** Defines a callback triggered when the texture has been loaded (default: null) */\r\n    onLoad?: Nullable<() => void>;\r\n\r\n    /** Defines a callback triggered when an error occurred during the loading session (default: null) */\r\n    onError?: Nullable<(message?: string, exception?: any) => void>;\r\n\r\n    /** Defines the buffer to load the texture from in case the texture is loaded from a buffer representation (default: null) */\r\n    buffer?: Nullable<string | ArrayBuffer | ArrayBufferView | HTMLImageElement | Blob | ImageBitmap>;\r\n\r\n    /** Defines if the buffer we are loading the texture from should be deleted after load (default: false) */\r\n    deleteBuffer?: boolean;\r\n\r\n    /** Defines the format of the texture we are trying to load (Engine.TEXTUREFORMAT_RGBA...) (default: ) */\r\n    format?: number;\r\n\r\n    /** Defines an optional mime type information (default: undefined) */\r\n    mimeType?: string;\r\n\r\n    /** Options to be passed to the loader (default: undefined) */\r\n    loaderOptions?: any;\r\n\r\n    /** Specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg) (default: undefined) */\r\n    creationFlags?: number;\r\n\r\n    /** Defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU) (default: false) */\r\n    useSRGBBuffer?: boolean;\r\n\r\n    /** Defines the underlying texture from an already existing one */\r\n    internalTexture?: InternalTexture;\r\n\r\n    /** Defines the underlying texture texture space */\r\n    gammaSpace?: boolean;\r\n}\r\n\r\n/**\r\n * This represents a texture in babylon. It can be easily loaded from a network, base64 or html input.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction#texture\r\n */\r\nexport class Texture extends BaseTexture {\r\n    /**\r\n     * Gets or sets a general boolean used to indicate that textures containing direct data (buffers) must be saved as part of the serialization process\r\n     */\r\n    public static SerializeBuffers = true;\r\n\r\n    /**\r\n     * Gets or sets a general boolean used to indicate that texture buffers must be saved as part of the serialization process.\r\n     * If no buffer exists, one will be created as base64 string from the internal webgl data.\r\n     */\r\n    public static ForceSerializeBuffers = false;\r\n\r\n    /**\r\n     * This observable will notify when any texture had a loading error\r\n     */\r\n    public static OnTextureLoadErrorObservable = new Observable<BaseTexture>();\r\n\r\n    /** @internal */\r\n    public static _SerializeInternalTextureUniqueId = false;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static _CubeTextureParser = (jsonTexture: any, scene: Scene, rootUrl: string): CubeTexture => {\r\n        throw _WarnImport(\"CubeTexture\");\r\n    };\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static _CreateMirror = (name: string, renderTargetSize: number, scene: Scene, generateMipMaps: boolean): MirrorTexture => {\r\n        throw _WarnImport(\"MirrorTexture\");\r\n    };\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static _CreateRenderTargetTexture = (name: string, renderTargetSize: number, scene: Scene, generateMipMaps: boolean, creationFlags?: number): RenderTargetTexture => {\r\n        throw _WarnImport(\"RenderTargetTexture\");\r\n    };\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _CreateVideoTexture(\r\n        name: Nullable<string>,\r\n        src: string | string[] | HTMLVideoElement,\r\n        scene: Nullable<Scene>,\r\n        generateMipMaps = false,\r\n        invertY = false,\r\n        samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE,\r\n        settings: Partial<VideoTextureSettings> = {},\r\n        onError?: Nullable<(message?: string, exception?: any) => void>,\r\n        format: number = Constants.TEXTUREFORMAT_RGBA\r\n    ): VideoTexture {\r\n        throw _WarnImport(\"VideoTexture\");\r\n    }\r\n\r\n    /** nearest is mag = nearest and min = nearest and no mip */\r\n    public static readonly NEAREST_SAMPLINGMODE = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n    /** nearest is mag = nearest and min = nearest and mip = linear */\r\n    public static readonly NEAREST_NEAREST_MIPLINEAR = Constants.TEXTURE_NEAREST_NEAREST_MIPLINEAR; // nearest is mag = nearest and min = nearest and mip = linear\r\n\r\n    /** Bilinear is mag = linear and min = linear and no mip */\r\n    public static readonly BILINEAR_SAMPLINGMODE = Constants.TEXTURE_BILINEAR_SAMPLINGMODE;\r\n    /** Bilinear is mag = linear and min = linear and mip = nearest */\r\n    public static readonly LINEAR_LINEAR_MIPNEAREST = Constants.TEXTURE_LINEAR_LINEAR_MIPNEAREST; // Bilinear is mag = linear and min = linear and mip = nearest\r\n\r\n    /** Trilinear is mag = linear and min = linear and mip = linear */\r\n    public static readonly TRILINEAR_SAMPLINGMODE = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE;\r\n    /** Trilinear is mag = linear and min = linear and mip = linear */\r\n    public static readonly LINEAR_LINEAR_MIPLINEAR = Constants.TEXTURE_LINEAR_LINEAR_MIPLINEAR; // Trilinear is mag = linear and min = linear and mip = linear\r\n\r\n    /** mag = nearest and min = nearest and mip = nearest */\r\n    public static readonly NEAREST_NEAREST_MIPNEAREST = Constants.TEXTURE_NEAREST_NEAREST_MIPNEAREST;\r\n    /** mag = nearest and min = linear and mip = nearest */\r\n    public static readonly NEAREST_LINEAR_MIPNEAREST = Constants.TEXTURE_NEAREST_LINEAR_MIPNEAREST;\r\n    /** mag = nearest and min = linear and mip = linear */\r\n    public static readonly NEAREST_LINEAR_MIPLINEAR = Constants.TEXTURE_NEAREST_LINEAR_MIPLINEAR;\r\n    /** mag = nearest and min = linear and mip = none */\r\n    public static readonly NEAREST_LINEAR = Constants.TEXTURE_NEAREST_LINEAR;\r\n    /** mag = nearest and min = nearest and mip = none */\r\n    public static readonly NEAREST_NEAREST = Constants.TEXTURE_NEAREST_NEAREST;\r\n    /** mag = linear and min = nearest and mip = nearest */\r\n    public static readonly LINEAR_NEAREST_MIPNEAREST = Constants.TEXTURE_LINEAR_NEAREST_MIPNEAREST;\r\n    /** mag = linear and min = nearest and mip = linear */\r\n    public static readonly LINEAR_NEAREST_MIPLINEAR = Constants.TEXTURE_LINEAR_NEAREST_MIPLINEAR;\r\n    /** mag = linear and min = linear and mip = none */\r\n    public static readonly LINEAR_LINEAR = Constants.TEXTURE_LINEAR_LINEAR;\r\n    /** mag = linear and min = nearest and mip = none */\r\n    public static readonly LINEAR_NEAREST = Constants.TEXTURE_LINEAR_NEAREST;\r\n\r\n    /** Explicit coordinates mode */\r\n    public static readonly EXPLICIT_MODE = Constants.TEXTURE_EXPLICIT_MODE;\r\n    /** Spherical coordinates mode */\r\n    public static readonly SPHERICAL_MODE = Constants.TEXTURE_SPHERICAL_MODE;\r\n    /** Planar coordinates mode */\r\n    public static readonly PLANAR_MODE = Constants.TEXTURE_PLANAR_MODE;\r\n    /** Cubic coordinates mode */\r\n    public static readonly CUBIC_MODE = Constants.TEXTURE_CUBIC_MODE;\r\n    /** Projection coordinates mode */\r\n    public static readonly PROJECTION_MODE = Constants.TEXTURE_PROJECTION_MODE;\r\n    /** Inverse Cubic coordinates mode */\r\n    public static readonly SKYBOX_MODE = Constants.TEXTURE_SKYBOX_MODE;\r\n    /** Inverse Cubic coordinates mode */\r\n    public static readonly INVCUBIC_MODE = Constants.TEXTURE_INVCUBIC_MODE;\r\n    /** Equirectangular coordinates mode */\r\n    public static readonly EQUIRECTANGULAR_MODE = Constants.TEXTURE_EQUIRECTANGULAR_MODE;\r\n    /** Equirectangular Fixed coordinates mode */\r\n    public static readonly FIXED_EQUIRECTANGULAR_MODE = Constants.TEXTURE_FIXED_EQUIRECTANGULAR_MODE;\r\n    /** Equirectangular Fixed Mirrored coordinates mode */\r\n    public static readonly FIXED_EQUIRECTANGULAR_MIRRORED_MODE = Constants.TEXTURE_FIXED_EQUIRECTANGULAR_MIRRORED_MODE;\r\n\r\n    /** Texture is not repeating outside of 0..1 UVs */\r\n    public static readonly CLAMP_ADDRESSMODE = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n    /** Texture is repeating outside of 0..1 UVs */\r\n    public static readonly WRAP_ADDRESSMODE = Constants.TEXTURE_WRAP_ADDRESSMODE;\r\n    /** Texture is repeating and mirrored */\r\n    public static readonly MIRROR_ADDRESSMODE = Constants.TEXTURE_MIRROR_ADDRESSMODE;\r\n\r\n    /**\r\n     * Gets or sets a boolean which defines if the texture url must be build from the serialized URL instead of just using the name and loading them side by side with the scene file\r\n     */\r\n    public static UseSerializedUrlIfAny = false;\r\n\r\n    /**\r\n     * Define the url of the texture.\r\n     */\r\n    @serialize()\r\n    public url: Nullable<string> = null;\r\n\r\n    /**\r\n     * Define an offset on the texture to offset the u coordinates of the UVs\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/moreMaterials#offsetting\r\n     */\r\n    @serialize()\r\n    public uOffset = 0;\r\n\r\n    /**\r\n     * Define an offset on the texture to offset the v coordinates of the UVs\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/moreMaterials#offsetting\r\n     */\r\n    @serialize()\r\n    public vOffset = 0;\r\n\r\n    /**\r\n     * Define an offset on the texture to scale the u coordinates of the UVs\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/moreMaterials#tiling\r\n     */\r\n    @serialize()\r\n    public uScale = 1.0;\r\n\r\n    /**\r\n     * Define an offset on the texture to scale the v coordinates of the UVs\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/moreMaterials#tiling\r\n     */\r\n    @serialize()\r\n    public vScale = 1.0;\r\n\r\n    /**\r\n     * Define an offset on the texture to rotate around the u coordinates of the UVs\r\n     * The angle is defined in radians.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/moreMaterials\r\n     */\r\n    @serialize()\r\n    public uAng = 0;\r\n\r\n    /**\r\n     * Define an offset on the texture to rotate around the v coordinates of the UVs\r\n     * The angle is defined in radians.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/moreMaterials\r\n     */\r\n    @serialize()\r\n    public vAng = 0;\r\n\r\n    /**\r\n     * Define an offset on the texture to rotate around the w coordinates of the UVs (in case of 3d texture)\r\n     * The angle is defined in radians.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/moreMaterials\r\n     */\r\n    @serialize()\r\n    public wAng = 0;\r\n\r\n    /**\r\n     * Defines the center of rotation (U)\r\n     */\r\n    @serialize()\r\n    public uRotationCenter = 0.5;\r\n\r\n    /**\r\n     * Defines the center of rotation (V)\r\n     */\r\n    @serialize()\r\n    public vRotationCenter = 0.5;\r\n\r\n    /**\r\n     * Defines the center of rotation (W)\r\n     */\r\n    @serialize()\r\n    public wRotationCenter = 0.5;\r\n\r\n    /**\r\n     * Sets this property to true to avoid deformations when rotating the texture with non-uniform scaling\r\n     */\r\n    @serialize()\r\n    public homogeneousRotationInUVTransform = false;\r\n\r\n    /**\r\n     * Are mip maps generated for this texture or not.\r\n     */\r\n    get noMipmap(): boolean {\r\n        return this._noMipmap;\r\n    }\r\n\r\n    /**\r\n     * List of inspectable custom properties (used by the Inspector)\r\n     * @see https://doc.babylonjs.com/toolsAndResources/inspector#extensibility\r\n     */\r\n    public inspectableCustomProperties: Nullable<IInspectable[]> = null;\r\n\r\n    /** @internal */\r\n    public _noMipmap: boolean = false;\r\n    /** @internal */\r\n    public _invertY: boolean = false;\r\n    private _rowGenerationMatrix: Nullable<Matrix> = null;\r\n    private _cachedTextureMatrix: Nullable<Matrix> = null;\r\n    private _projectionModeMatrix: Nullable<Matrix> = null;\r\n    private _t0: Nullable<Vector3> = null;\r\n    private _t1: Nullable<Vector3> = null;\r\n    private _t2: Nullable<Vector3> = null;\r\n\r\n    private _cachedUOffset: number = -1;\r\n    private _cachedVOffset: number = -1;\r\n    private _cachedUScale: number = 0;\r\n    private _cachedVScale: number = 0;\r\n    private _cachedUAng: number = -1;\r\n    private _cachedVAng: number = -1;\r\n    private _cachedWAng: number = -1;\r\n    private _cachedReflectionProjectionMatrixId: number = -1;\r\n    private _cachedURotationCenter: number = -1;\r\n    private _cachedVRotationCenter: number = -1;\r\n    private _cachedWRotationCenter: number = -1;\r\n    private _cachedHomogeneousRotationInUVTransform: boolean = false;\r\n\r\n    private _cachedReflectionTextureMatrix: Nullable<Matrix> = null;\r\n    private _cachedReflectionUOffset = -1;\r\n    private _cachedReflectionVOffset = -1;\r\n    private _cachedReflectionUScale = 0;\r\n    private _cachedReflectionVScale = 0;\r\n    private _cachedReflectionCoordinatesMode = -1;\r\n\r\n    /** @internal */\r\n    public _buffer: Nullable<string | ArrayBuffer | ArrayBufferView | HTMLImageElement | Blob | ImageBitmap> = null;\r\n    private _deleteBuffer: boolean = false;\r\n    protected _format: Nullable<number> = null;\r\n    private _delayedOnLoad: Nullable<() => void> = null;\r\n    private _delayedOnError: Nullable<() => void> = null;\r\n    private _mimeType?: string;\r\n    private _loaderOptions?: any;\r\n    private _creationFlags?: number;\r\n    /** @internal */\r\n    public _useSRGBBuffer?: boolean;\r\n    private _forcedExtension?: string;\r\n\r\n    /** Returns the texture mime type if it was defined by a loader (undefined else) */\r\n    public get mimeType() {\r\n        return this._mimeType;\r\n    }\r\n\r\n    /**\r\n     * Observable triggered once the texture has been loaded.\r\n     */\r\n    public onLoadObservable: Observable<Texture> = new Observable<Texture>();\r\n\r\n    protected _isBlocking: boolean = true;\r\n    /**\r\n     * Is the texture preventing material to render while loading.\r\n     * If false, a default texture will be used instead of the loading one during the preparation step.\r\n     */\r\n    public set isBlocking(value: boolean) {\r\n        this._isBlocking = value;\r\n    }\r\n    @serialize()\r\n    public get isBlocking(): boolean {\r\n        return this._isBlocking;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the texture needs to be inverted on the y axis during loading\r\n     */\r\n    public get invertY(): boolean {\r\n        return this._invertY;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new texture.\r\n     * This represents a texture in babylon. It can be easily loaded from a network, base64 or html input.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction#texture\r\n     * @param url defines the url of the picture to load as a texture\r\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\r\n     * @param noMipmapOrOptions defines if the texture will require mip maps or not or set of all options to create the texture\r\n     * @param invertY defines if the texture needs to be inverted on the y axis during loading\r\n     * @param samplingMode defines the sampling mode we want for the texture while fetching from it (Texture.NEAREST_SAMPLINGMODE...)\r\n     * @param onLoad defines a callback triggered when the texture has been loaded\r\n     * @param onError defines a callback triggered when an error occurred during the loading session\r\n     * @param buffer defines the buffer to load the texture from in case the texture is loaded from a buffer representation\r\n     * @param deleteBuffer defines if the buffer we are loading the texture from should be deleted after load\r\n     * @param format defines the format of the texture we are trying to load (Engine.TEXTUREFORMAT_RGBA...)\r\n     * @param mimeType defines an optional mime type information\r\n     * @param loaderOptions options to be passed to the loader\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     * @param forcedExtension defines the extension to use to pick the right loader\r\n     */\r\n    constructor(\r\n        url: Nullable<string>,\r\n        sceneOrEngine?: Nullable<Scene | ThinEngine>,\r\n        noMipmapOrOptions?: boolean | ITextureCreationOptions,\r\n        invertY?: boolean,\r\n        samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE,\r\n        onLoad: Nullable<() => void> = null,\r\n        onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n        buffer: Nullable<string | ArrayBuffer | ArrayBufferView | HTMLImageElement | Blob | ImageBitmap> = null,\r\n        deleteBuffer: boolean = false,\r\n        format?: number,\r\n        mimeType?: string,\r\n        loaderOptions?: any,\r\n        creationFlags?: number,\r\n        forcedExtension?: string\r\n    ) {\r\n        super(sceneOrEngine);\r\n\r\n        this.name = url || \"\";\r\n        this.url = url;\r\n\r\n        let noMipmap: boolean;\r\n        let useSRGBBuffer: boolean = false;\r\n        let internalTexture: Nullable<InternalTexture> = null;\r\n        let gammaSpace = true;\r\n\r\n        if (typeof noMipmapOrOptions === \"object\" && noMipmapOrOptions !== null) {\r\n            noMipmap = noMipmapOrOptions.noMipmap ?? false;\r\n            invertY = noMipmapOrOptions.invertY ?? (CompatibilityOptions.UseOpenGLOrientationForUV ? false : true);\r\n            samplingMode = noMipmapOrOptions.samplingMode ?? Texture.TRILINEAR_SAMPLINGMODE;\r\n            onLoad = noMipmapOrOptions.onLoad ?? null;\r\n            onError = noMipmapOrOptions.onError ?? null;\r\n            buffer = noMipmapOrOptions.buffer ?? null;\r\n            deleteBuffer = noMipmapOrOptions.deleteBuffer ?? false;\r\n            format = noMipmapOrOptions.format;\r\n            mimeType = noMipmapOrOptions.mimeType;\r\n            loaderOptions = noMipmapOrOptions.loaderOptions;\r\n            creationFlags = noMipmapOrOptions.creationFlags;\r\n            useSRGBBuffer = noMipmapOrOptions.useSRGBBuffer ?? false;\r\n            internalTexture = noMipmapOrOptions.internalTexture ?? null;\r\n            gammaSpace = noMipmapOrOptions.gammaSpace ?? gammaSpace;\r\n        } else {\r\n            noMipmap = !!noMipmapOrOptions;\r\n        }\r\n\r\n        this._gammaSpace = gammaSpace;\r\n        this._noMipmap = noMipmap;\r\n        this._invertY = invertY === undefined ? (CompatibilityOptions.UseOpenGLOrientationForUV ? false : true) : invertY;\r\n        this._initialSamplingMode = samplingMode;\r\n        this._buffer = buffer;\r\n        this._deleteBuffer = deleteBuffer;\r\n        this._mimeType = mimeType;\r\n        this._loaderOptions = loaderOptions;\r\n        this._creationFlags = creationFlags;\r\n        this._useSRGBBuffer = useSRGBBuffer;\r\n        this._forcedExtension = forcedExtension;\r\n        if (format) {\r\n            this._format = format;\r\n        }\r\n\r\n        const scene = this.getScene();\r\n        const engine = this._getEngine();\r\n        if (!engine) {\r\n            return;\r\n        }\r\n\r\n        engine.onBeforeTextureInitObservable.notifyObservers(this);\r\n\r\n        const load = () => {\r\n            if (this._texture) {\r\n                if (this._texture._invertVScale) {\r\n                    this.vScale *= -1;\r\n                    this.vOffset += 1;\r\n                }\r\n\r\n                // Update texture to match internal texture's wrapping\r\n                if (this._texture._cachedWrapU !== null) {\r\n                    this.wrapU = this._texture._cachedWrapU;\r\n                    this._texture._cachedWrapU = null;\r\n                }\r\n                if (this._texture._cachedWrapV !== null) {\r\n                    this.wrapV = this._texture._cachedWrapV;\r\n                    this._texture._cachedWrapV = null;\r\n                }\r\n                if (this._texture._cachedWrapR !== null) {\r\n                    this.wrapR = this._texture._cachedWrapR;\r\n                    this._texture._cachedWrapR = null;\r\n                }\r\n            }\r\n\r\n            if (this.onLoadObservable.hasObservers()) {\r\n                this.onLoadObservable.notifyObservers(this);\r\n            }\r\n            if (onLoad) {\r\n                onLoad();\r\n            }\r\n\r\n            if (!this.isBlocking && scene) {\r\n                scene.resetCachedMaterial();\r\n            }\r\n        };\r\n\r\n        const errorHandler = (message?: string, exception?: any) => {\r\n            this._loadingError = true;\r\n            this._errorObject = { message, exception };\r\n            if (onError) {\r\n                onError(message, exception);\r\n            }\r\n            Texture.OnTextureLoadErrorObservable.notifyObservers(this);\r\n        };\r\n\r\n        if (!this.url && !internalTexture) {\r\n            this._delayedOnLoad = load;\r\n            this._delayedOnError = errorHandler;\r\n            return;\r\n        }\r\n\r\n        this._texture = internalTexture ?? this._getFromCache(this.url, noMipmap, samplingMode, this._invertY, useSRGBBuffer, this.isCube);\r\n\r\n        if (!this._texture) {\r\n            if (!scene || !scene.useDelayedTextureLoading) {\r\n                try {\r\n                    this._texture = engine.createTexture(\r\n                        this.url,\r\n                        noMipmap,\r\n                        this._invertY,\r\n                        scene,\r\n                        samplingMode,\r\n                        load,\r\n                        errorHandler,\r\n                        this._buffer,\r\n                        undefined,\r\n                        this._format,\r\n                        this._forcedExtension,\r\n                        mimeType,\r\n                        loaderOptions,\r\n                        creationFlags,\r\n                        useSRGBBuffer\r\n                    );\r\n                } catch (e) {\r\n                    errorHandler(\"error loading\", e);\r\n                    throw e;\r\n                }\r\n                if (deleteBuffer) {\r\n                    this._buffer = null;\r\n                }\r\n            } else {\r\n                this.delayLoadState = Constants.DELAYLOADSTATE_NOTLOADED;\r\n\r\n                this._delayedOnLoad = load;\r\n                this._delayedOnError = errorHandler;\r\n            }\r\n        } else {\r\n            if (this._texture.isReady) {\r\n                TimingTools.SetImmediate(() => load());\r\n            } else {\r\n                const loadObserver = this._texture.onLoadedObservable.add(load);\r\n                this._texture.onErrorObservable.add((e) => {\r\n                    errorHandler(e.message, e.exception);\r\n                    this._texture?.onLoadedObservable.remove(loadObserver);\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the url (and optional buffer) of this texture if url was null during construction.\r\n     * @param url the url of the texture\r\n     * @param buffer the buffer of the texture (defaults to null)\r\n     * @param onLoad callback called when the texture is loaded  (defaults to null)\r\n     * @param forcedExtension defines the extension to use to pick the right loader\r\n     */\r\n    public updateURL(\r\n        url: string,\r\n        buffer: Nullable<string | ArrayBuffer | ArrayBufferView | HTMLImageElement | Blob | ImageBitmap> = null,\r\n        onLoad?: () => void,\r\n        forcedExtension?: string\r\n    ): void {\r\n        if (this.url) {\r\n            this.releaseInternalTexture();\r\n            this.getScene()!.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this);\r\n            });\r\n        }\r\n\r\n        if (!this.name || this.name.startsWith(\"data:\")) {\r\n            this.name = url;\r\n        }\r\n        this.url = url;\r\n        this._buffer = buffer;\r\n        this._forcedExtension = forcedExtension;\r\n        this.delayLoadState = Constants.DELAYLOADSTATE_NOTLOADED;\r\n\r\n        if (onLoad) {\r\n            this._delayedOnLoad = onLoad;\r\n        }\r\n        this.delayLoad();\r\n    }\r\n\r\n    /**\r\n     * Finish the loading sequence of a texture flagged as delayed load.\r\n     * @internal\r\n     */\r\n    public delayLoad(): void {\r\n        if (this.delayLoadState !== Constants.DELAYLOADSTATE_NOTLOADED) {\r\n            return;\r\n        }\r\n\r\n        const scene = this.getScene();\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        this.delayLoadState = Constants.DELAYLOADSTATE_LOADED;\r\n        this._texture = this._getFromCache(this.url, this._noMipmap, this.samplingMode, this._invertY, this._useSRGBBuffer, this.isCube);\r\n\r\n        if (!this._texture) {\r\n            this._texture = scene\r\n                .getEngine()\r\n                .createTexture(\r\n                    this.url,\r\n                    this._noMipmap,\r\n                    this._invertY,\r\n                    scene,\r\n                    this.samplingMode,\r\n                    this._delayedOnLoad,\r\n                    this._delayedOnError,\r\n                    this._buffer,\r\n                    null,\r\n                    this._format,\r\n                    this._forcedExtension,\r\n                    this._mimeType,\r\n                    this._loaderOptions,\r\n                    this._creationFlags,\r\n                    this._useSRGBBuffer\r\n                );\r\n            if (this._deleteBuffer) {\r\n                this._buffer = null;\r\n            }\r\n        } else {\r\n            if (this._delayedOnLoad) {\r\n                if (this._texture.isReady) {\r\n                    TimingTools.SetImmediate(this._delayedOnLoad);\r\n                } else {\r\n                    this._texture.onLoadedObservable.add(this._delayedOnLoad);\r\n                }\r\n            }\r\n        }\r\n\r\n        this._delayedOnLoad = null;\r\n        this._delayedOnError = null;\r\n    }\r\n\r\n    private _prepareRowForTextureGeneration(x: number, y: number, z: number, t: Vector3): void {\r\n        x *= this._cachedUScale;\r\n        y *= this._cachedVScale;\r\n\r\n        x -= this.uRotationCenter * this._cachedUScale;\r\n        y -= this.vRotationCenter * this._cachedVScale;\r\n        z -= this.wRotationCenter;\r\n\r\n        Vector3.TransformCoordinatesFromFloatsToRef(x, y, z, this._rowGenerationMatrix!, t);\r\n\r\n        t.x += this.uRotationCenter * this._cachedUScale + this._cachedUOffset;\r\n        t.y += this.vRotationCenter * this._cachedVScale + this._cachedVOffset;\r\n        t.z += this.wRotationCenter;\r\n    }\r\n\r\n    /**\r\n     * Checks if the texture has the same transform matrix than another texture\r\n     * @param texture texture to check against\r\n     * @returns true if the transforms are the same, else false\r\n     */\r\n    public checkTransformsAreIdentical(texture: Nullable<Texture>): boolean {\r\n        return (\r\n            texture !== null &&\r\n            this.uOffset === texture.uOffset &&\r\n            this.vOffset === texture.vOffset &&\r\n            this.uScale === texture.uScale &&\r\n            this.vScale === texture.vScale &&\r\n            this.uAng === texture.uAng &&\r\n            this.vAng === texture.vAng &&\r\n            this.wAng === texture.wAng\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Get the current texture matrix which includes the requested offsetting, tiling and rotation components.\r\n     * @param uBase The horizontal base offset multiplier (1 by default)\r\n     * @returns the transform matrix of the texture.\r\n     */\r\n    public getTextureMatrix(uBase = 1): Matrix {\r\n        if (\r\n            this.uOffset === this._cachedUOffset &&\r\n            this.vOffset === this._cachedVOffset &&\r\n            this.uScale * uBase === this._cachedUScale &&\r\n            this.vScale === this._cachedVScale &&\r\n            this.uAng === this._cachedUAng &&\r\n            this.vAng === this._cachedVAng &&\r\n            this.wAng === this._cachedWAng &&\r\n            this.uRotationCenter === this._cachedURotationCenter &&\r\n            this.vRotationCenter === this._cachedVRotationCenter &&\r\n            this.wRotationCenter === this._cachedWRotationCenter &&\r\n            this.homogeneousRotationInUVTransform === this._cachedHomogeneousRotationInUVTransform\r\n        ) {\r\n            return this._cachedTextureMatrix!;\r\n        }\r\n\r\n        this._cachedUOffset = this.uOffset;\r\n        this._cachedVOffset = this.vOffset;\r\n        this._cachedUScale = this.uScale * uBase;\r\n        this._cachedVScale = this.vScale;\r\n        this._cachedUAng = this.uAng;\r\n        this._cachedVAng = this.vAng;\r\n        this._cachedWAng = this.wAng;\r\n        this._cachedURotationCenter = this.uRotationCenter;\r\n        this._cachedVRotationCenter = this.vRotationCenter;\r\n        this._cachedWRotationCenter = this.wRotationCenter;\r\n        this._cachedHomogeneousRotationInUVTransform = this.homogeneousRotationInUVTransform;\r\n\r\n        if (!this._cachedTextureMatrix || !this._rowGenerationMatrix) {\r\n            this._cachedTextureMatrix = Matrix.Zero();\r\n            this._rowGenerationMatrix = new Matrix();\r\n            this._t0 = Vector3.Zero();\r\n            this._t1 = Vector3.Zero();\r\n            this._t2 = Vector3.Zero();\r\n        }\r\n\r\n        Matrix.RotationYawPitchRollToRef(this.vAng, this.uAng, this.wAng, this._rowGenerationMatrix!);\r\n\r\n        if (this.homogeneousRotationInUVTransform) {\r\n            Matrix.TranslationToRef(-this._cachedURotationCenter, -this._cachedVRotationCenter, -this._cachedWRotationCenter, TmpVectors.Matrix[0]);\r\n            Matrix.TranslationToRef(this._cachedURotationCenter, this._cachedVRotationCenter, this._cachedWRotationCenter, TmpVectors.Matrix[1]);\r\n            Matrix.ScalingToRef(this._cachedUScale, this._cachedVScale, 0, TmpVectors.Matrix[2]);\r\n            Matrix.TranslationToRef(this._cachedUOffset, this._cachedVOffset, 0, TmpVectors.Matrix[3]);\r\n\r\n            TmpVectors.Matrix[0].multiplyToRef(this._rowGenerationMatrix!, this._cachedTextureMatrix);\r\n            this._cachedTextureMatrix.multiplyToRef(TmpVectors.Matrix[1], this._cachedTextureMatrix);\r\n            this._cachedTextureMatrix.multiplyToRef(TmpVectors.Matrix[2], this._cachedTextureMatrix);\r\n            this._cachedTextureMatrix.multiplyToRef(TmpVectors.Matrix[3], this._cachedTextureMatrix);\r\n\r\n            // copy the translation row to the 3rd row of the matrix so that we don't need to update the shaders (which expects the translation to be on the 3rd row)\r\n            this._cachedTextureMatrix.setRowFromFloats(2, this._cachedTextureMatrix.m[12], this._cachedTextureMatrix.m[13], this._cachedTextureMatrix.m[14], 1);\r\n        } else {\r\n            this._prepareRowForTextureGeneration(0, 0, 0, this._t0!);\r\n            this._prepareRowForTextureGeneration(1.0, 0, 0, this._t1!);\r\n            this._prepareRowForTextureGeneration(0, 1.0, 0, this._t2!);\r\n\r\n            this._t1!.subtractInPlace(this._t0!);\r\n            this._t2!.subtractInPlace(this._t0!);\r\n\r\n            Matrix.FromValuesToRef(\r\n                this._t1!.x,\r\n                this._t1!.y,\r\n                this._t1!.z,\r\n                0.0,\r\n                this._t2!.x,\r\n                this._t2!.y,\r\n                this._t2!.z,\r\n                0.0,\r\n                this._t0!.x,\r\n                this._t0!.y,\r\n                this._t0!.z,\r\n                0.0,\r\n                0.0,\r\n                0.0,\r\n                0.0,\r\n                1.0,\r\n                this._cachedTextureMatrix\r\n            );\r\n        }\r\n\r\n        const scene = this.getScene();\r\n\r\n        if (!scene) {\r\n            return this._cachedTextureMatrix;\r\n        }\r\n\r\n        if (this.optimizeUVAllocation) {\r\n            // We flag the materials that are using this texture as \"texture dirty\" because depending on the fact that the matrix is the identity or not, some defines\r\n            // will get different values (see PrepareDefinesForMergedUV), meaning we should regenerate the effect accordingly\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this);\r\n            });\r\n        }\r\n\r\n        return this._cachedTextureMatrix;\r\n    }\r\n\r\n    /**\r\n     * Get the current matrix used to apply reflection. This is useful to rotate an environment texture for instance.\r\n     * @returns The reflection texture transform\r\n     */\r\n    public getReflectionTextureMatrix(): Matrix {\r\n        const scene = this.getScene();\r\n\r\n        if (!scene) {\r\n            return this._cachedReflectionTextureMatrix!;\r\n        }\r\n\r\n        if (\r\n            this.uOffset === this._cachedReflectionUOffset &&\r\n            this.vOffset === this._cachedReflectionVOffset &&\r\n            this.uScale === this._cachedReflectionUScale &&\r\n            this.vScale === this._cachedReflectionVScale &&\r\n            this.coordinatesMode === this._cachedReflectionCoordinatesMode\r\n        ) {\r\n            if (this.coordinatesMode === Texture.PROJECTION_MODE) {\r\n                if (this._cachedReflectionProjectionMatrixId === scene.getProjectionMatrix().updateFlag) {\r\n                    return this._cachedReflectionTextureMatrix!;\r\n                }\r\n            } else {\r\n                return this._cachedReflectionTextureMatrix!;\r\n            }\r\n        }\r\n\r\n        if (!this._cachedReflectionTextureMatrix) {\r\n            this._cachedReflectionTextureMatrix = Matrix.Zero();\r\n        }\r\n\r\n        if (!this._projectionModeMatrix) {\r\n            this._projectionModeMatrix = Matrix.Zero();\r\n        }\r\n\r\n        const flagMaterialsAsTextureDirty = this._cachedReflectionCoordinatesMode !== this.coordinatesMode;\r\n\r\n        this._cachedReflectionUOffset = this.uOffset;\r\n        this._cachedReflectionVOffset = this.vOffset;\r\n        this._cachedReflectionUScale = this.uScale;\r\n        this._cachedReflectionVScale = this.vScale;\r\n        this._cachedReflectionCoordinatesMode = this.coordinatesMode;\r\n\r\n        switch (this.coordinatesMode) {\r\n            case Texture.PLANAR_MODE: {\r\n                Matrix.IdentityToRef(this._cachedReflectionTextureMatrix);\r\n                (<any>this._cachedReflectionTextureMatrix)[0] = this.uScale;\r\n                (<any>this._cachedReflectionTextureMatrix)[5] = this.vScale;\r\n                (<any>this._cachedReflectionTextureMatrix)[12] = this.uOffset;\r\n                (<any>this._cachedReflectionTextureMatrix)[13] = this.vOffset;\r\n                break;\r\n            }\r\n            case Texture.PROJECTION_MODE: {\r\n                Matrix.FromValuesToRef(0.5, 0.0, 0.0, 0.0, 0.0, -0.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 1.0, 1.0, this._projectionModeMatrix);\r\n\r\n                const projectionMatrix = scene.getProjectionMatrix();\r\n                this._cachedReflectionProjectionMatrixId = projectionMatrix.updateFlag;\r\n                projectionMatrix.multiplyToRef(this._projectionModeMatrix, this._cachedReflectionTextureMatrix);\r\n                break;\r\n            }\r\n            default:\r\n                Matrix.IdentityToRef(this._cachedReflectionTextureMatrix);\r\n                break;\r\n        }\r\n\r\n        if (flagMaterialsAsTextureDirty) {\r\n            // We flag the materials that are using this texture as \"texture dirty\" if the coordinatesMode has changed.\r\n            // Indeed, this property is used to set the value of some defines used to generate the effect (in material.isReadyForSubMesh), so we must make sure this code will be re-executed and the effect recreated if necessary\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this);\r\n            });\r\n        }\r\n\r\n        return this._cachedReflectionTextureMatrix;\r\n    }\r\n\r\n    /**\r\n     * Clones the texture.\r\n     * @returns the cloned texture\r\n     */\r\n    public clone(): Texture {\r\n        const options: ITextureCreationOptions = {\r\n            noMipmap: this._noMipmap,\r\n            invertY: this._invertY,\r\n            samplingMode: this.samplingMode,\r\n            onLoad: undefined,\r\n            onError: undefined,\r\n            buffer: this._texture ? this._texture._buffer : undefined,\r\n            deleteBuffer: this._deleteBuffer,\r\n            format: this.textureFormat,\r\n            mimeType: this.mimeType,\r\n            loaderOptions: this._loaderOptions,\r\n            creationFlags: this._creationFlags,\r\n            useSRGBBuffer: this._useSRGBBuffer,\r\n        };\r\n\r\n        return SerializationHelper.Clone(() => {\r\n            return new Texture(this._texture ? this._texture.url : null, this.getScene(), options);\r\n        }, this);\r\n    }\r\n\r\n    /**\r\n     * Serialize the texture to a JSON representation we can easily use in the respective Parse function.\r\n     * @returns The JSON representation of the texture\r\n     */\r\n    public serialize(): any {\r\n        const savedName = this.name;\r\n\r\n        if (!Texture.SerializeBuffers) {\r\n            if (this.name.startsWith(\"data:\")) {\r\n                this.name = \"\";\r\n            }\r\n        }\r\n\r\n        if (this.name.startsWith(\"data:\") && this.url === this.name) {\r\n            this.url = \"\";\r\n        }\r\n\r\n        const serializationObject = super.serialize(Texture._SerializeInternalTextureUniqueId);\r\n\r\n        if (!serializationObject) {\r\n            return null;\r\n        }\r\n\r\n        if (Texture.SerializeBuffers || Texture.ForceSerializeBuffers) {\r\n            if (typeof this._buffer === \"string\" && (this._buffer as string).substr(0, 5) === \"data:\") {\r\n                serializationObject.base64String = this._buffer;\r\n                serializationObject.name = serializationObject.name.replace(\"data:\", \"\");\r\n            } else if (this.url && this.url.startsWith(\"data:\") && this._buffer instanceof Uint8Array) {\r\n                serializationObject.base64String = \"data:image/png;base64,\" + EncodeArrayBufferToBase64(this._buffer);\r\n            } else if (Texture.ForceSerializeBuffers || (this.url && this.url.startsWith(\"blob:\")) || this._forceSerialize) {\r\n                serializationObject.base64String =\r\n                    !this._engine || this._engine._features.supportSyncTextureRead ? GenerateBase64StringFromTexture(this) : GenerateBase64StringFromTextureAsync(this);\r\n            }\r\n        }\r\n\r\n        serializationObject.invertY = this._invertY;\r\n        serializationObject.samplingMode = this.samplingMode;\r\n        serializationObject._creationFlags = this._creationFlags;\r\n        serializationObject._useSRGBBuffer = this._useSRGBBuffer;\r\n        if (Texture._SerializeInternalTextureUniqueId) {\r\n            serializationObject.internalTextureUniqueId = this._texture?.uniqueId ?? undefined;\r\n        }\r\n        serializationObject.noMipmap = this._noMipmap;\r\n\r\n        this.name = savedName;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Get the current class name of the texture useful for serialization or dynamic coding.\r\n     * @returns \"Texture\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"Texture\";\r\n    }\r\n\r\n    /**\r\n     * Dispose the texture and release its associated resources.\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n\r\n        this.onLoadObservable.clear();\r\n\r\n        this._delayedOnLoad = null;\r\n        this._delayedOnError = null;\r\n        this._buffer = null;\r\n    }\r\n\r\n    /**\r\n     * Parse the JSON representation of a texture in order to recreate the texture in the given scene.\r\n     * @param parsedTexture Define the JSON representation of the texture\r\n     * @param scene Define the scene the parsed texture should be instantiated in\r\n     * @param rootUrl Define the root url of the parsing sequence in the case of relative dependencies\r\n     * @returns The parsed texture if successful\r\n     */\r\n    public static Parse(parsedTexture: any, scene: Scene, rootUrl: string): Nullable<BaseTexture> {\r\n        if (parsedTexture.customType) {\r\n            const customTexture = InstantiationTools.Instantiate(parsedTexture.customType);\r\n            // Update Sampling Mode\r\n            const parsedCustomTexture: any = customTexture.Parse(parsedTexture, scene, rootUrl);\r\n            if (parsedTexture.samplingMode && parsedCustomTexture.updateSamplingMode && parsedCustomTexture._samplingMode) {\r\n                if (parsedCustomTexture._samplingMode !== parsedTexture.samplingMode) {\r\n                    parsedCustomTexture.updateSamplingMode(parsedTexture.samplingMode);\r\n                }\r\n            }\r\n            return parsedCustomTexture;\r\n        }\r\n\r\n        if (parsedTexture.isCube && !parsedTexture.isRenderTarget) {\r\n            return Texture._CubeTextureParser(parsedTexture, scene, rootUrl);\r\n        }\r\n\r\n        const hasInternalTextureUniqueId = parsedTexture.internalTextureUniqueId !== undefined;\r\n\r\n        if (!parsedTexture.name && !parsedTexture.isRenderTarget && !hasInternalTextureUniqueId) {\r\n            return null;\r\n        }\r\n\r\n        let internalTexture: InternalTexture | undefined;\r\n\r\n        if (hasInternalTextureUniqueId) {\r\n            const cache = scene.getEngine().getLoadedTexturesCache();\r\n            for (const texture of cache) {\r\n                if (texture.uniqueId === parsedTexture.internalTextureUniqueId) {\r\n                    internalTexture = texture;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n\r\n        const onLoaded = (texture: Texture | null) => {\r\n            // Clear cache\r\n            if (texture && texture._texture) {\r\n                texture._texture._cachedWrapU = null;\r\n                texture._texture._cachedWrapV = null;\r\n                texture._texture._cachedWrapR = null;\r\n            }\r\n\r\n            // Update Sampling Mode\r\n            if (parsedTexture.samplingMode) {\r\n                const sampling: number = parsedTexture.samplingMode;\r\n                if (texture && texture.samplingMode !== sampling) {\r\n                    texture.updateSamplingMode(sampling);\r\n                }\r\n            }\r\n            // Animations\r\n            if (texture && parsedTexture.animations) {\r\n                for (let animationIndex = 0; animationIndex < parsedTexture.animations.length; animationIndex++) {\r\n                    const parsedAnimation = parsedTexture.animations[animationIndex];\r\n                    const internalClass = GetClass(\"BABYLON.Animation\");\r\n                    if (internalClass) {\r\n                        texture.animations.push(internalClass.Parse(parsedAnimation));\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (hasInternalTextureUniqueId && !internalTexture) {\r\n                texture?._texture?._setUniqueId(parsedTexture.internalTextureUniqueId);\r\n            }\r\n        };\r\n\r\n        const texture = SerializationHelper.Parse(\r\n            () => {\r\n                let generateMipMaps: boolean = true;\r\n                if (parsedTexture.noMipmap) {\r\n                    generateMipMaps = false;\r\n                }\r\n                if (parsedTexture.mirrorPlane) {\r\n                    const mirrorTexture = Texture._CreateMirror(parsedTexture.name, parsedTexture.renderTargetSize, scene, generateMipMaps);\r\n                    mirrorTexture._waitingRenderList = parsedTexture.renderList;\r\n                    mirrorTexture.mirrorPlane = Plane.FromArray(parsedTexture.mirrorPlane);\r\n                    onLoaded(mirrorTexture);\r\n                    return mirrorTexture;\r\n                } else if (parsedTexture.isRenderTarget) {\r\n                    let renderTargetTexture: Nullable<RenderTargetTexture> = null;\r\n                    if (parsedTexture.isCube) {\r\n                        // Search for an existing reflection probe (which contains a cube render target texture)\r\n                        if (scene.reflectionProbes) {\r\n                            for (let index = 0; index < scene.reflectionProbes.length; index++) {\r\n                                const probe = scene.reflectionProbes[index];\r\n                                if (probe.name === parsedTexture.name) {\r\n                                    return probe.cubeTexture;\r\n                                }\r\n                            }\r\n                        }\r\n                    } else {\r\n                        renderTargetTexture = Texture._CreateRenderTargetTexture(\r\n                            parsedTexture.name,\r\n                            parsedTexture.renderTargetSize,\r\n                            scene,\r\n                            generateMipMaps,\r\n                            parsedTexture._creationFlags ?? 0\r\n                        );\r\n                        renderTargetTexture._waitingRenderList = parsedTexture.renderList;\r\n                    }\r\n                    onLoaded(renderTargetTexture);\r\n                    return renderTargetTexture;\r\n                } else if (parsedTexture.isVideo) {\r\n                    const texture = Texture._CreateVideoTexture(\r\n                        rootUrl + (parsedTexture.url || parsedTexture.name),\r\n                        rootUrl + (parsedTexture.src || parsedTexture.url),\r\n                        scene,\r\n                        generateMipMaps,\r\n                        parsedTexture.invertY,\r\n                        parsedTexture.samplingMode,\r\n                        parsedTexture.settings || {}\r\n                    );\r\n                    onLoaded(texture);\r\n                    return texture;\r\n                } else {\r\n                    let texture: Texture;\r\n\r\n                    if (parsedTexture.base64String && !internalTexture) {\r\n                        // name and url are the same to ensure caching happens from the actual base64 string\r\n                        texture = Texture.CreateFromBase64String(\r\n                            parsedTexture.base64String,\r\n                            parsedTexture.base64String,\r\n                            scene,\r\n                            !generateMipMaps,\r\n                            parsedTexture.invertY,\r\n                            parsedTexture.samplingMode,\r\n                            () => {\r\n                                onLoaded(texture);\r\n                            },\r\n                            parsedTexture._creationFlags ?? 0,\r\n                            parsedTexture._useSRGBBuffer ?? false\r\n                        );\r\n\r\n                        // prettier name to fit with the loaded data\r\n                        texture.name = parsedTexture.name;\r\n                    } else {\r\n                        let url: string;\r\n                        if (parsedTexture.name && (parsedTexture.name.indexOf(\"://\") > 0 || parsedTexture.name.startsWith(\"data:\"))) {\r\n                            url = parsedTexture.name;\r\n                        } else {\r\n                            url = rootUrl + parsedTexture.name;\r\n                        }\r\n\r\n                        if (parsedTexture.url && (parsedTexture.url.startsWith(\"data:\") || Texture.UseSerializedUrlIfAny)) {\r\n                            url = parsedTexture.url;\r\n                        }\r\n\r\n                        const options: ITextureCreationOptions = {\r\n                            noMipmap: !generateMipMaps,\r\n                            invertY: parsedTexture.invertY,\r\n                            samplingMode: parsedTexture.samplingMode,\r\n                            onLoad: () => {\r\n                                onLoaded(texture);\r\n                            },\r\n                            internalTexture,\r\n                        };\r\n\r\n                        texture = new Texture(url, scene, options);\r\n                    }\r\n\r\n                    return texture;\r\n                }\r\n            },\r\n            parsedTexture,\r\n            scene\r\n        );\r\n\r\n        return texture;\r\n    }\r\n\r\n    /**\r\n     * Creates a texture from its base 64 representation.\r\n     * @param data Define the base64 payload without the data: prefix\r\n     * @param name Define the name of the texture in the scene useful fo caching purpose for instance\r\n     * @param scene Define the scene the texture should belong to\r\n     * @param noMipmapOrOptions defines if the texture will require mip maps or not or set of all options to create the texture\r\n     * @param invertY define if the texture needs to be inverted on the y axis during loading\r\n     * @param samplingMode define the sampling mode we want for the texture while fetching from it (Texture.NEAREST_SAMPLINGMODE...)\r\n     * @param onLoad define a callback triggered when the texture has been loaded\r\n     * @param onError define a callback triggered when an error occurred during the loading session\r\n     * @param format define the format of the texture we are trying to load (Engine.TEXTUREFORMAT_RGBA...)\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     * @returns the created texture\r\n     */\r\n    public static CreateFromBase64String(\r\n        data: string,\r\n        name: string,\r\n        scene: Scene,\r\n        noMipmapOrOptions?: boolean | ITextureCreationOptions,\r\n        invertY?: boolean,\r\n        samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE,\r\n        onLoad: Nullable<() => void> = null,\r\n        onError: Nullable<() => void> = null,\r\n        format: number = Constants.TEXTUREFORMAT_RGBA,\r\n        creationFlags?: number\r\n    ): Texture {\r\n        return new Texture(\"data:\" + name, scene, noMipmapOrOptions, invertY, samplingMode, onLoad, onError, data, false, format, undefined, undefined, creationFlags);\r\n    }\r\n\r\n    /**\r\n     * Creates a texture from its data: representation. (data: will be added in case only the payload has been passed in)\r\n     * @param name Define the name of the texture in the scene useful fo caching purpose for instance\r\n     * @param buffer define the buffer to load the texture from in case the texture is loaded from a buffer representation\r\n     * @param scene Define the scene the texture should belong to\r\n     * @param deleteBuffer define if the buffer we are loading the texture from should be deleted after load\r\n     * @param noMipmapOrOptions defines if the texture will require mip maps or not or set of all options to create the texture\r\n     * @param invertY define if the texture needs to be inverted on the y axis during loading\r\n     * @param samplingMode define the sampling mode we want for the texture while fetching from it (Texture.NEAREST_SAMPLINGMODE...)\r\n     * @param onLoad define a callback triggered when the texture has been loaded\r\n     * @param onError define a callback triggered when an error occurred during the loading session\r\n     * @param format define the format of the texture we are trying to load (Engine.TEXTUREFORMAT_RGBA...)\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     * @returns the created texture\r\n     */\r\n    public static LoadFromDataString(\r\n        name: string,\r\n        buffer: any,\r\n        scene: Scene,\r\n        deleteBuffer: boolean = false,\r\n        noMipmapOrOptions?: boolean | ITextureCreationOptions,\r\n        invertY: boolean = true,\r\n        samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE,\r\n        onLoad: Nullable<() => void> = null,\r\n        onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n        format: number = Constants.TEXTUREFORMAT_RGBA,\r\n        creationFlags?: number\r\n    ): Texture {\r\n        if (name.substr(0, 5) !== \"data:\") {\r\n            name = \"data:\" + name;\r\n        }\r\n\r\n        return new Texture(name, scene, noMipmapOrOptions, invertY, samplingMode, onLoad, onError, buffer, deleteBuffer, format, undefined, undefined, creationFlags);\r\n    }\r\n}\r\n\r\n// References the dependencies.\r\nRegisterClass(\"BABYLON.Texture\", Texture);\r\nSerializationHelper._TextureParser = Texture.Parse;\r\n"]}