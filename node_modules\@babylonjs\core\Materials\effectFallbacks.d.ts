import type { IEffectFallbacks } from "./iEffectFallbacks";
import type { Effect } from "./effect";
import type { AbstractMesh } from "../Meshes/abstractMesh";
/**
 * EffectFallbacks can be used to add fallbacks (properties to disable) to certain properties when desired to improve performance.
 * (Eg. Start at high quality with reflection and fog, if fps is low, remove reflection, if still low remove fog)
 */
export declare class EffectFallbacks implements IEffectFallbacks {
    private _defines;
    private _currentRank;
    private _maxRank;
    private _mesh;
    /**
     * Removes the fallback from the bound mesh.
     */
    unBindMesh(): void;
    /**
     * Adds a fallback on the specified property.
     * @param rank The rank of the fallback (Lower ranks will be fallbacked to first)
     * @param define The name of the define in the shader
     */
    addFallback(rank: number, define: string): void;
    /**
     * Sets the mesh to use CPU skinning when needing to fallback.
     * @param rank The rank of the fallback (Lower ranks will be fallbacked to first)
     * @param mesh The mesh to use the fallbacks.
     */
    addCPUSkinningFallback(rank: number, mesh: AbstractMesh): void;
    /**
     * Checks to see if more fallbacks are still available.
     */
    get hasMoreFallbacks(): boolean;
    /**
     * Removes the defines that should be removed when falling back.
     * @param currentDefines defines the current define statements for the shader.
     * @param effect defines the current effect we try to compile
     * @returns The resulting defines with defines of the current rank removed.
     */
    reduce(currentDefines: string, effect: Effect): string;
}
