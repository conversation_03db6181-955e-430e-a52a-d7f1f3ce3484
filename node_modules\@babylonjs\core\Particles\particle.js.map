{"version": 3, "file": "particle.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Particles/particle.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC7E,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAM7C,OAAO,EAAE,KAAK,EAAE,MAAM,gCAAgC,CAAC;AAEvD;;;GAGG;AACH,MAAM,OAAO,QAAQ;IAoIjB;;;OAGG;IACH;IACI;;OAEG;IACI,cAAkC;QAAlC,mBAAc,GAAd,cAAc,CAAoB;QAtI7C;;WAEG;QACI,aAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEjC;;WAEG;QACI,cAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAElC;;WAEG;QACI,UAAK,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEtC;;WAEG;QACI,cAAS,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1C;;WAEG;QACI,aAAQ,GAAG,GAAG,CAAC;QAEtB;;WAEG;QACI,QAAG,GAAG,CAAC,CAAC;QAEf;;WAEG;QACI,SAAI,GAAG,CAAC,CAAC;QAEhB;;WAEG;QACI,UAAK,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjC;;WAEG;QACI,UAAK,GAAG,CAAC,CAAC;QAEjB;;WAEG;QACI,iBAAY,GAAG,CAAC,CAAC;QAExB;;WAEG;QACI,cAAS,GAAW,CAAC,CAAC;QAa7B,gBAAgB;QACT,yBAAoB,GAAgC,IAAI,CAAC;QAWhE,gBAAgB;QACT,mBAAc,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/C,gBAAgB;QACT,mBAAc,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAI/C,gBAAgB;QACT,kBAAa,GAAG,CAAC,CAAC;QACzB,gBAAgB;QACT,kBAAa,GAAG,CAAC,CAAC;QAIzB,gBAAgB;QACT,0BAAqB,GAAG,CAAC,CAAC;QACjC,gBAAgB;QACT,0BAAqB,GAAG,CAAC,CAAC;QAIjC,gBAAgB;QACT,sBAAiB,GAAG,CAAC,CAAC;QAC7B,gBAAgB;QACT,sBAAiB,GAAG,CAAC,CAAC;QAI7B,gBAAgB;QACT,2BAAsB,GAAG,CAAC,CAAC;QAClC,gBAAgB;QACT,2BAAsB,GAAG,CAAC,CAAC;QAIlC,gBAAgB;QACT,kBAAa,GAAG,CAAC,CAAC;QACzB,gBAAgB;QACT,kBAAa,GAAG,CAAC,CAAC;QAoBrB,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE;YAC9C,OAAO;SACV;QAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAEO,yBAAyB;QAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;IAC3D,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC;QACzB,IAAI,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC;QAE5D,IAAI,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE;YAC3C,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE;gBACtC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;aAC1D;YAED,IAAI,WAAW,KAAK,CAAC,EAAE;gBACnB,sEAAsE;gBACtE,WAAW,GAAG,CAAC,CAAC;gBAChB,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC;aACtC;iBAAM;gBACH,SAAS,IAAI,IAAI,CAAC,iBAAiB,CAAC;aACvC;SACJ;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,yBAAyB,CAAC;QAC3E,IAAI,KAAa,CAAC;QAClB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,SAAS,GAAG,WAAW,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC9E;aAAM;YACH,KAAK,GAAG,KAAK,CAAC,CAAC,SAAS,GAAG,WAAW,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC5D;QACD,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,yBAAyB,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACI,gCAAgC,CAAC,UAAsB;QAC1D,IAAmB,UAAU,CAAC,cAAc,CAAC,OAAQ,CAAC,QAAQ,EAAE;YAC5D,MAAM,WAAW,GAAiB,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC;YACpE,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7C,IAAI,UAAU,CAAC,gBAAgB,EAAE;gBAC7B,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACnC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBACpC,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;aAClD;SACJ;aAAM;YACH,MAAM,eAAe,GAAY,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC;YACnE,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC3C;QACD,wEAAwE;QACxE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,uBAAuB,GAAG,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACzF,UAAU,CAAC,cAAc,CAAC,wBAAwB,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACvF,CAAC;IAED,gBAAgB;IACT,iCAAiC;QACpC,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;YACnE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC7C,IAAI,CAAC,gCAAgC,CAAC,UAAU,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED,gBAAgB;IACT,MAAM;QACT,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;QAC5B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;QACzC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;QAC1C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;QACvD,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,KAAe;QACzB,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,KAAK,CAAC,iBAAiB,EAAE;gBACzB,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;aAC5D;iBAAM;gBACH,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;aAC5D;SACJ;aAAM;YACH,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC;SAClC;QACD,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,KAAK,CAAC,cAAc,EAAE;gBACtB,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;aACtD;iBAAM;gBACH,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;aACtD;SACJ;QACD,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACjD,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC3C,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACvD,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,KAAK,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YACzD,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnD,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACtD;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACvD,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;YACzC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SAC5C;QACD,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,KAAK,CAAC,4BAA4B,GAAG,IAAI,CAAC,4BAA4B,CAAC;YACvE,KAAK,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YACzD,KAAK,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;SAC5D;QACD,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,KAAK,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC;YAC/D,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;YACjD,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;SACpD;QACD,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACpC,KAAK,CAAC,6BAA6B,GAAG,IAAI,CAAC,6BAA6B,CAAC;YACzE,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAC3D,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;SAC9D;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACvD,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;YACzC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SAC5C;QACD,IAAI,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE;YAC7C,KAAK,CAAC,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAC;YACjE,KAAK,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;YAC7D,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;SAC9D;QACD,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE;YACtC,IAAI,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;gBACnC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC5C;iBAAM;gBACH,KAAK,CAAC,SAAS,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAC7C;SACJ;QACD,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,KAAK,CAAC,wBAAwB,EAAE;gBAChC,KAAK,CAAC,wBAAwB,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBACvE,KAAK,CAAC,wBAAwB,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;aAC1E;iBAAM;gBACH,KAAK,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;gBACvE,KAAK,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;aAC1E;SACJ;IACL,CAAC;;AA3Tc,eAAM,GAAG,CAAC,AAAJ,CAAK", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Vector2, Vector3, TmpVectors, Vector4 } from \"../Maths/math.vector\";\r\nimport { Color4 } from \"../Maths/math.color\";\r\nimport type { SubEmitter } from \"./subEmitter\";\r\nimport type { ColorGradient, FactorGradient } from \"../Misc/gradients\";\r\n\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { ThinParticleSystem } from \"./thinParticleSystem\";\r\nimport { Clamp } from \"../Maths/math.scalar.functions\";\r\n\r\n/**\r\n * A particle represents one of the element emitted by a particle system.\r\n * This is mainly define by its coordinates, direction, velocity and age.\r\n */\r\nexport class Particle {\r\n    private static _Count = 0;\r\n    /**\r\n     * Unique ID of the particle\r\n     */\r\n    public id: number;\r\n    /**\r\n     * The world position of the particle in the scene.\r\n     */\r\n    public position = Vector3.Zero();\r\n\r\n    /**\r\n     * The world direction of the particle in the scene.\r\n     */\r\n    public direction = Vector3.Zero();\r\n\r\n    /**\r\n     * The color of the particle.\r\n     */\r\n    public color = new Color4(0, 0, 0, 0);\r\n\r\n    /**\r\n     * The color change of the particle per step.\r\n     */\r\n    public colorStep = new Color4(0, 0, 0, 0);\r\n\r\n    /**\r\n     * Defines how long will the life of the particle be.\r\n     */\r\n    public lifeTime = 1.0;\r\n\r\n    /**\r\n     * The current age of the particle.\r\n     */\r\n    public age = 0;\r\n\r\n    /**\r\n     * The current size of the particle.\r\n     */\r\n    public size = 0;\r\n\r\n    /**\r\n     * The current scale of the particle.\r\n     */\r\n    public scale = new Vector2(1, 1);\r\n\r\n    /**\r\n     * The current angle of the particle.\r\n     */\r\n    public angle = 0;\r\n\r\n    /**\r\n     * Defines how fast is the angle changing.\r\n     */\r\n    public angularSpeed = 0;\r\n\r\n    /**\r\n     * Defines the cell index used by the particle to be rendered from a sprite.\r\n     */\r\n    public cellIndex: number = 0;\r\n\r\n    /**\r\n     * The information required to support color remapping\r\n     */\r\n    public remapData: Vector4;\r\n\r\n    /** @internal */\r\n    public _randomCellOffset?: number;\r\n\r\n    /** @internal */\r\n    public _initialDirection: Nullable<Vector3>;\r\n\r\n    /** @internal */\r\n    public _attachedSubEmitters: Nullable<Array<SubEmitter>> = null;\r\n\r\n    /** @internal */\r\n    public _initialStartSpriteCellID: number;\r\n    /** @internal */\r\n    public _initialEndSpriteCellID: number;\r\n    /** @internal */\r\n    public _initialSpriteCellLoop: boolean;\r\n\r\n    /** @internal */\r\n    public _currentColorGradient: Nullable<ColorGradient>;\r\n    /** @internal */\r\n    public _currentColor1 = new Color4(0, 0, 0, 0);\r\n    /** @internal */\r\n    public _currentColor2 = new Color4(0, 0, 0, 0);\r\n\r\n    /** @internal */\r\n    public _currentSizeGradient: Nullable<FactorGradient>;\r\n    /** @internal */\r\n    public _currentSize1 = 0;\r\n    /** @internal */\r\n    public _currentSize2 = 0;\r\n\r\n    /** @internal */\r\n    public _currentAngularSpeedGradient: Nullable<FactorGradient>;\r\n    /** @internal */\r\n    public _currentAngularSpeed1 = 0;\r\n    /** @internal */\r\n    public _currentAngularSpeed2 = 0;\r\n\r\n    /** @internal */\r\n    public _currentVelocityGradient: Nullable<FactorGradient>;\r\n    /** @internal */\r\n    public _currentVelocity1 = 0;\r\n    /** @internal */\r\n    public _currentVelocity2 = 0;\r\n\r\n    /** @internal */\r\n    public _currentLimitVelocityGradient: Nullable<FactorGradient>;\r\n    /** @internal */\r\n    public _currentLimitVelocity1 = 0;\r\n    /** @internal */\r\n    public _currentLimitVelocity2 = 0;\r\n\r\n    /** @internal */\r\n    public _currentDragGradient: Nullable<FactorGradient>;\r\n    /** @internal */\r\n    public _currentDrag1 = 0;\r\n    /** @internal */\r\n    public _currentDrag2 = 0;\r\n\r\n    /** @internal */\r\n    public _randomNoiseCoordinates1: Vector3;\r\n    /** @internal */\r\n    public _randomNoiseCoordinates2: Vector3;\r\n\r\n    /** @internal */\r\n    public _localPosition?: Vector3;\r\n\r\n    /**\r\n     * Creates a new instance Particle\r\n     * @param particleSystem the particle system the particle belongs to\r\n     */\r\n    constructor(\r\n        /**\r\n         * The particle system the particle belongs to.\r\n         */\r\n        public particleSystem: ThinParticleSystem\r\n    ) {\r\n        this.id = Particle._Count++;\r\n        if (!this.particleSystem.isAnimationSheetEnabled) {\r\n            return;\r\n        }\r\n\r\n        this._updateCellInfoFromSystem();\r\n    }\r\n\r\n    private _updateCellInfoFromSystem(): void {\r\n        this.cellIndex = this.particleSystem.startSpriteCellID;\r\n    }\r\n\r\n    /**\r\n     * Defines how the sprite cell index is updated for the particle\r\n     */\r\n    public updateCellIndex(): void {\r\n        let offsetAge = this.age;\r\n        let changeSpeed = this.particleSystem.spriteCellChangeSpeed;\r\n\r\n        if (this.particleSystem.spriteRandomStartCell) {\r\n            if (this._randomCellOffset === undefined) {\r\n                this._randomCellOffset = Math.random() * this.lifeTime;\r\n            }\r\n\r\n            if (changeSpeed === 0) {\r\n                // Special case when speed = 0 meaning we want to stay on initial cell\r\n                changeSpeed = 1;\r\n                offsetAge = this._randomCellOffset;\r\n            } else {\r\n                offsetAge += this._randomCellOffset;\r\n            }\r\n        }\r\n\r\n        const dist = this._initialEndSpriteCellID - this._initialStartSpriteCellID;\r\n        let ratio: number;\r\n        if (this._initialSpriteCellLoop) {\r\n            ratio = Clamp(((offsetAge * changeSpeed) % this.lifeTime) / this.lifeTime);\r\n        } else {\r\n            ratio = Clamp((offsetAge * changeSpeed) / this.lifeTime);\r\n        }\r\n        this.cellIndex = (this._initialStartSpriteCellID + ratio * dist) | 0;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _inheritParticleInfoToSubEmitter(subEmitter: SubEmitter) {\r\n        if ((<AbstractMesh>subEmitter.particleSystem.emitter).position) {\r\n            const emitterMesh = <AbstractMesh>subEmitter.particleSystem.emitter;\r\n            emitterMesh.position.copyFrom(this.position);\r\n            if (subEmitter.inheritDirection) {\r\n                const temp = TmpVectors.Vector3[0];\r\n                this.direction.normalizeToRef(temp);\r\n                emitterMesh.setDirection(temp, 0, Math.PI / 2);\r\n            }\r\n        } else {\r\n            const emitterPosition = <Vector3>subEmitter.particleSystem.emitter;\r\n            emitterPosition.copyFrom(this.position);\r\n        }\r\n        // Set inheritedVelocityOffset to be used when new particles are created\r\n        this.direction.scaleToRef(subEmitter.inheritedVelocityAmount / 2, TmpVectors.Vector3[0]);\r\n        subEmitter.particleSystem._inheritedVelocityOffset.copyFrom(TmpVectors.Vector3[0]);\r\n    }\r\n\r\n    /** @internal */\r\n    public _inheritParticleInfoToSubEmitters() {\r\n        if (this._attachedSubEmitters && this._attachedSubEmitters.length > 0) {\r\n            this._attachedSubEmitters.forEach((subEmitter) => {\r\n                this._inheritParticleInfoToSubEmitter(subEmitter);\r\n            });\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _reset() {\r\n        this.age = 0;\r\n        this.id = Particle._Count++;\r\n        this._currentColorGradient = null;\r\n        this._currentSizeGradient = null;\r\n        this._currentAngularSpeedGradient = null;\r\n        this._currentVelocityGradient = null;\r\n        this._currentLimitVelocityGradient = null;\r\n        this._currentDragGradient = null;\r\n        this.cellIndex = this.particleSystem.startSpriteCellID;\r\n        this._randomCellOffset = undefined;\r\n    }\r\n\r\n    /**\r\n     * Copy the properties of particle to another one.\r\n     * @param other the particle to copy the information to.\r\n     */\r\n    public copyTo(other: Particle) {\r\n        other.position.copyFrom(this.position);\r\n        if (this._initialDirection) {\r\n            if (other._initialDirection) {\r\n                other._initialDirection.copyFrom(this._initialDirection);\r\n            } else {\r\n                other._initialDirection = this._initialDirection.clone();\r\n            }\r\n        } else {\r\n            other._initialDirection = null;\r\n        }\r\n        other.direction.copyFrom(this.direction);\r\n        if (this._localPosition) {\r\n            if (other._localPosition) {\r\n                other._localPosition.copyFrom(this._localPosition);\r\n            } else {\r\n                other._localPosition = this._localPosition.clone();\r\n            }\r\n        }\r\n        other.color.copyFrom(this.color);\r\n        other.colorStep.copyFrom(this.colorStep);\r\n        other.lifeTime = this.lifeTime;\r\n        other.age = this.age;\r\n        other._randomCellOffset = this._randomCellOffset;\r\n        other.size = this.size;\r\n        other.scale.copyFrom(this.scale);\r\n        other.angle = this.angle;\r\n        other.angularSpeed = this.angularSpeed;\r\n        other.particleSystem = this.particleSystem;\r\n        other.cellIndex = this.cellIndex;\r\n        other.id = this.id;\r\n        other._attachedSubEmitters = this._attachedSubEmitters;\r\n        if (this._currentColorGradient) {\r\n            other._currentColorGradient = this._currentColorGradient;\r\n            other._currentColor1.copyFrom(this._currentColor1);\r\n            other._currentColor2.copyFrom(this._currentColor2);\r\n        }\r\n        if (this._currentSizeGradient) {\r\n            other._currentSizeGradient = this._currentSizeGradient;\r\n            other._currentSize1 = this._currentSize1;\r\n            other._currentSize2 = this._currentSize2;\r\n        }\r\n        if (this._currentAngularSpeedGradient) {\r\n            other._currentAngularSpeedGradient = this._currentAngularSpeedGradient;\r\n            other._currentAngularSpeed1 = this._currentAngularSpeed1;\r\n            other._currentAngularSpeed2 = this._currentAngularSpeed2;\r\n        }\r\n        if (this._currentVelocityGradient) {\r\n            other._currentVelocityGradient = this._currentVelocityGradient;\r\n            other._currentVelocity1 = this._currentVelocity1;\r\n            other._currentVelocity2 = this._currentVelocity2;\r\n        }\r\n        if (this._currentLimitVelocityGradient) {\r\n            other._currentLimitVelocityGradient = this._currentLimitVelocityGradient;\r\n            other._currentLimitVelocity1 = this._currentLimitVelocity1;\r\n            other._currentLimitVelocity2 = this._currentLimitVelocity2;\r\n        }\r\n        if (this._currentDragGradient) {\r\n            other._currentDragGradient = this._currentDragGradient;\r\n            other._currentDrag1 = this._currentDrag1;\r\n            other._currentDrag2 = this._currentDrag2;\r\n        }\r\n        if (this.particleSystem.isAnimationSheetEnabled) {\r\n            other._initialStartSpriteCellID = this._initialStartSpriteCellID;\r\n            other._initialEndSpriteCellID = this._initialEndSpriteCellID;\r\n            other._initialSpriteCellLoop = this._initialSpriteCellLoop;\r\n        }\r\n        if (this.particleSystem.useRampGradients) {\r\n            if (other.remapData && this.remapData) {\r\n                other.remapData.copyFrom(this.remapData);\r\n            } else {\r\n                other.remapData = new Vector4(0, 0, 0, 0);\r\n            }\r\n        }\r\n        if (this._randomNoiseCoordinates1) {\r\n            if (other._randomNoiseCoordinates1) {\r\n                other._randomNoiseCoordinates1.copyFrom(this._randomNoiseCoordinates1);\r\n                other._randomNoiseCoordinates2.copyFrom(this._randomNoiseCoordinates2);\r\n            } else {\r\n                other._randomNoiseCoordinates1 = this._randomNoiseCoordinates1.clone();\r\n                other._randomNoiseCoordinates2 = this._randomNoiseCoordinates2.clone();\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}