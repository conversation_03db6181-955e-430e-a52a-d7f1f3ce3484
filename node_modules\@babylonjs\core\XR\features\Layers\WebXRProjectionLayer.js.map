{"version": 3, "file": "WebXRProjectionLayer.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/XR/features/Layers/WebXRProjectionLayer.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,gDAAgD,EAAE,4BAA4B,EAAE,MAAM,yBAAyB,CAAC;AAKzH;;;GAGG;AACH,MAAM,OAAO,2BAA4B,SAAQ,4BAA4B;IACzE,YACoB,KAAwB,EACxC,WAAoB,EACpB,WAA2B;QAE3B,KAAK,CACD,GAAG,EAAE,CAAC,KAAK,CAAC,YAAY,EACxB,GAAG,EAAE,CAAC,KAAK,CAAC,aAAa,EACzB,KAAK,EACL,mBAAmB,EACnB,WAAW,EACX,CAAC,cAAc,EAAE,EAAE,CAAC,IAAI,+CAA+C,CAAC,cAAc,EAAE,WAAW,EAAE,IAAI,CAAC,CAC7G,CAAC;QAXc,UAAK,GAAL,KAAK,CAAmB;IAY5C,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,+CAAgD,SAAQ,gDAAgD;IAG1G,YACI,iBAAsC,EACtC,eAA+B,EACf,YAAyC;QAEzD,KAAK,CAAC,iBAAiB,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;QAFxC,iBAAY,GAAZ,YAAY,CAA6B;QAGzD,IAAI,CAAC,gBAAgB,GAAG,YAAY,CAAC,KAAK,CAAC;IAC/C,CAAC;IAEO,mBAAmB,CAAC,IAAY;QACpC,OAAO,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC7E,CAAC;IAEM,6BAA6B,CAAC,IAAY;QAC7C,OAAO,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACtF,CAAC;IAEM,4BAA4B,CAAC,GAAU;QAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClD,IAAI,YAAY,EAAE;YACd,OAAO,IAAI,CAAC,2BAA2B,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;SAC9D;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,qBAAqB,CAAC,QAAkB,EAAE,IAAY;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QACrF,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ;AAED,MAAM,CAAC,MAAM,4BAA4B,GAA0B;IAC/D,WAAW,EAAE,SAAS;IACtB,WAAW,EAAE,MAAM,CAAC,gCAAgC;IACpD,WAAW,EAAE,MAAM,CAAC,4CAA4C;IAChE,WAAW,EAAE,GAAG;IAChB,aAAa,EAAE,KAAK;CACvB,CAAC", "sourcesContent": ["import type { WebXRSessionManager } from \"core/XR/webXRSessionManager\";\r\nimport { WebXRCompositionLayerRenderTargetTextureProvider, WebXRCompositionLayerWrapper } from \"./WebXRCompositionLayer\";\r\nimport type { Nullable } from \"core/types\";\r\nimport type { RenderTargetTexture } from \"core/Materials/Textures/renderTargetTexture\";\r\nimport type { Viewport } from \"core/Maths/math.viewport\";\r\n\r\n/**\r\n * Wraps xr projection layers.\r\n * @internal\r\n */\r\nexport class WebXRProjectionLayerWrapper extends WebXRCompositionLayerWrapper {\r\n    constructor(\r\n        public readonly layer: XRProjectionLayer,\r\n        isMultiview: boolean,\r\n        xrGLBinding: XRWebGLBinding\r\n    ) {\r\n        super(\r\n            () => layer.textureWidth,\r\n            () => layer.textureHeight,\r\n            layer,\r\n            \"XRProjectionLayer\",\r\n            isMultiview,\r\n            (sessionManager) => new WebXRProjectionLayerRenderTargetTextureProvider(sessionManager, xrGLBinding, this)\r\n        );\r\n    }\r\n}\r\n\r\n/**\r\n * Provides render target textures and other important rendering information for a given XRProjectionLayer.\r\n * @internal\r\n */\r\nclass WebXRProjectionLayerRenderTargetTextureProvider extends WebXRCompositionLayerRenderTargetTextureProvider {\r\n    private readonly _projectionLayer: XRProjectionLayer;\r\n\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        _xrWebGLBinding: XRWebGLBinding,\r\n        public readonly layerWrapper: WebXRProjectionLayerWrapper\r\n    ) {\r\n        super(_xrSessionManager, _xrWebGLBinding, layerWrapper);\r\n        this._projectionLayer = layerWrapper.layer;\r\n    }\r\n\r\n    private _getSubImageForView(view: XRView): XRWebGLSubImage {\r\n        return this._xrWebGLBinding.getViewSubImage(this._projectionLayer, view);\r\n    }\r\n\r\n    public getRenderTargetTextureForView(view: XRView): Nullable<RenderTargetTexture> {\r\n        return this._getRenderTargetForSubImage(this._getSubImageForView(view), view.eye);\r\n    }\r\n\r\n    public getRenderTargetTextureForEye(eye: XREye): Nullable<RenderTargetTexture> {\r\n        const lastSubImage = this._lastSubImages.get(eye);\r\n        if (lastSubImage) {\r\n            return this._getRenderTargetForSubImage(lastSubImage, eye);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    public trySetViewportForView(viewport: Viewport, view: XRView): boolean {\r\n        const subImage = this._lastSubImages.get(view.eye) || this._getSubImageForView(view);\r\n        if (subImage) {\r\n            this._setViewportForSubImage(viewport, subImage);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n}\r\n\r\nexport const defaultXRProjectionLayerInit: XRProjectionLayerInit = {\r\n    textureType: \"texture\",\r\n    colorFormat: 0x1908 /* WebGLRenderingContext.RGBA */,\r\n    depthFormat: 0x88f0 /* WebGLRenderingContext.DEPTH24_STENCIL8 */,\r\n    scaleFactor: 1.0,\r\n    clearOnAccess: false,\r\n};\r\n"]}