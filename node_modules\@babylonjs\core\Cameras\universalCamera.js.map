{"version": 3, "file": "universalCamera.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Cameras/universalCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAG/B,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAElC,OAAO,mCAAmC,CAAC;AAE3C,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IAClD,sCAAsC;IACtC,OAAO,GAAG,EAAE,CAAC,IAAI,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAClE,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,OAAO,eAAgB,SAAQ,WAAW;IAC5C;;;OAGG;IACH,IAAW,yBAAyB;QAChC,MAAM,OAAO,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACxE,IAAI,OAAO,EAAE;YACT,OAAO,OAAO,CAAC,yBAAyB,CAAC;SAC5C;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,yBAAyB,CAAC,KAAa;QAC9C,MAAM,OAAO,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACxE,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,yBAAyB,GAAG,KAAK,CAAC;SAC7C;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,sBAAsB;QAC7B,MAAM,OAAO,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACxE,IAAI,OAAO,EAAE;YACT,OAAO,OAAO,CAAC,sBAAsB,CAAC;SACzC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,sBAAsB,CAAC,KAAa;QAC3C,MAAM,OAAO,GAA2B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACxE,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,sBAAsB,GAAG,KAAK,CAAC;SAC1C;IACL,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa;QACtD,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,iBAAiB,CAAC;IAC7B,CAAC;CACJ;AAED,MAAM,CAAC,0BAA0B,GAAG,CAAC,IAAY,EAAE,KAAY,EAAE,EAAE;IAC/D,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAC5D,CAAC,CAAC", "sourcesContent": ["import { TouchCamera } from \"./touchCamera\";\r\nimport { Node } from \"../node\";\r\nimport type { FreeCameraGamepadInput } from \"../Cameras/Inputs/freeCameraGamepadInput\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport { Camera } from \"./camera\";\r\n\r\nimport \"../Gamepads/gamepadSceneComponent\";\r\n\r\nNode.AddNodeConstructor(\"FreeCamera\", (name, scene) => {\r\n    // Forcing to use the Universal camera\r\n    return () => new UniversalCamera(name, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * The Universal Camera is the one to choose for first person shooter type games, and works with all the keyboard, mouse, touch and gamepads. This replaces the earlier Free Camera,\r\n * which still works and will still be found in many Playgrounds.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#universal-camera\r\n */\r\nexport class UniversalCamera extends TouchCamera {\r\n    /**\r\n     * Defines the gamepad rotation sensibility.\r\n     * This is the threshold from when rotation starts to be accounted for to prevent jittering.\r\n     */\r\n    public get gamepadAngularSensibility(): number {\r\n        const gamepad = <FreeCameraGamepadInput>this.inputs.attached[\"gamepad\"];\r\n        if (gamepad) {\r\n            return gamepad.gamepadAngularSensibility;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set gamepadAngularSensibility(value: number) {\r\n        const gamepad = <FreeCameraGamepadInput>this.inputs.attached[\"gamepad\"];\r\n        if (gamepad) {\r\n            gamepad.gamepadAngularSensibility = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Defines the gamepad move sensibility.\r\n     * This is the threshold from when moving starts to be accounted for to prevent jittering.\r\n     */\r\n    public get gamepadMoveSensibility(): number {\r\n        const gamepad = <FreeCameraGamepadInput>this.inputs.attached[\"gamepad\"];\r\n        if (gamepad) {\r\n            return gamepad.gamepadMoveSensibility;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set gamepadMoveSensibility(value: number) {\r\n        const gamepad = <FreeCameraGamepadInput>this.inputs.attached[\"gamepad\"];\r\n        if (gamepad) {\r\n            gamepad.gamepadMoveSensibility = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * The Universal Camera is the one to choose for first person shooter type games, and works with all the keyboard, mouse, touch and gamepads. This replaces the earlier Free Camera,\r\n     * which still works and will still be found in many Playgrounds.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#universal-camera\r\n     * @param name Define the name of the camera in the scene\r\n     * @param position Define the start position of the camera in the scene\r\n     * @param scene Define the scene the camera belongs to\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene) {\r\n        super(name, position, scene);\r\n        this.inputs.addGamepad();\r\n    }\r\n\r\n    /**\r\n     * Gets the current object class name.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"UniversalCamera\";\r\n    }\r\n}\r\n\r\nCamera._CreateDefaultParsedCamera = (name: string, scene: Scene) => {\r\n    return new UniversalCamera(name, Vector3.Zero(), scene);\r\n};\r\n"]}