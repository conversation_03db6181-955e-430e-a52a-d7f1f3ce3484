{"version": 3, "file": "webXRMotionControllerManager.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/motionController/webXRMotionControllerManager.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,mCAAmC,EAAE,MAAM,gCAAgC,CAAC;AAErF,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AACzC,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAShF;;;;;;;GAOG;AAEH,MAAM,eAAe,GAIhB,EAAE,CAAC;AAER;;;GAGG;AACH,MAAM,OAAO,4BAA4B;IA0BrC;;OAEG;IACI,MAAM,CAAC,kBAAkB;QAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,gBAAgB;QAC1B,IAAI,CAAC,6BAA6B,CAAC,iBAAiB,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,6BAA6B,CAAC,gBAAgB,EAAE,CAAC,0BAA0B,CAAC,CAAC,CAAC;QACnF,IAAI,CAAC,6BAA6B,CAAC,UAAU,EAAE,CAAC,kCAAkC,CAAC,CAAC,CAAC;QACrF,IAAI,CAAC,6BAA6B,CAAC,eAAe,EAAE,CAAC,kCAAkC,CAAC,CAAC,CAAC;QAC1F,IAAI,CAAC,6BAA6B,CAAC,uBAAuB,EAAE,CAAC,6CAA6C,CAAC,CAAC,CAAC;QAC7G,IAAI,CAAC,6BAA6B,CAAC,yBAAyB,EAAE,CAAC,uBAAuB,EAAE,6CAA6C,CAAC,CAAC,CAAC;QACxI,IAAI,CAAC,6BAA6B,CAAC,WAAW,EAAE,CAAC,0BAA0B,CAAC,CAAC,CAAC;QAC9E,IAAI,CAAC,6BAA6B,CAAC,iBAAiB,EAAE,CAAC,cAAc,EAAE,oCAAoC,CAAC,CAAC,CAAC;QAC9G,IAAI,CAAC,6BAA6B,CAAC,cAAc,EAAE,CAAC,oCAAoC,CAAC,CAAC,CAAC;QAC3F,IAAI,CAAC,6BAA6B,CAAC,gBAAgB,EAAE,CAAC,uBAAuB,EAAE,6CAA6C,CAAC,CAAC,CAAC;QAC/H,IAAI,CAAC,6BAA6B,CAAC,iBAAiB,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,6BAA6B,CAAC,aAAa,EAAE,CAAC,6CAA6C,CAAC,CAAC,CAAC;QACnG,IAAI,CAAC,6BAA6B,CAAC,qBAAqB,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC;IACnF,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,yBAAyB,CAAC,SAAiB;QACrD,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAErD,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC/B,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,MAAM,CAAC,8BAA8B,CAAC,OAAsB,EAAE,KAAY,EAAE,YAAqB;QACpG,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,IAAI,YAAY,EAAE;YACd,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACnC;QACD,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC;QAE/C,mBAAmB;QACnB,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;YACzC,2DAA2D;YAC3D,YAAY,CAAC,GAAG,EAAE,CAAC;SACtB;QAED,4CAA4C;QAC5C,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE;YACvC,QAAQ,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE;gBACxB,KAAK,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;oBAC5E,uBAAuB;oBACvB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBACrC,MAAM;aACb;SACJ;QAED,4DAA4D;QAC5D,MAAM,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QACnE,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE;YACrB,YAAY,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,EAAE,yBAAyB,CAAC,CAAC;SACnE;QAED,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YACtB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACxC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,MAAM,aAAa,GAAG,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC;YACrI,MAAM,cAAc,GAAG,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC;YAEtI,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;gBACrE,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;SACN;aAAM;YACH,+BAA+B;YAC/B,OAAO,IAAI,CAAC,qCAAqC,CAAC,YAAY,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;SACnF;IACL,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,kBAAkB,CAAC,IAAY,EAAE,iBAA8C;QACzF,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC;IACzD,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,6BAA6B,CAAC,SAAiB,EAAE,SAAmB;QAC9E,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;YAC5B,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;SACjD;aAAM;YACH,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;SAC1C;IACL,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,kBAAkB;QAC5B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,GAAG,6BAA6B,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YAClH,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,oBAAoB;QAC9B,eAAe,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;YAClC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC9B,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QACH,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,CAAC;IAEO,MAAM,CAAC,0BAA0B,CAAC,YAAsB,EAAE,OAAsB,EAAE,KAAY;QAClG,OAAO,OAAO,CAAC,OAAO,EAAE;aACnB,IAAI,CAAC,GAAG,EAAE;YACP,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACrB,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;aACpC;iBAAM;gBACH,OAAO,IAAI,CAAC,aAAa,CAAC;aAC7B;QACL,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,YAA2C,EAAE,EAAE;YAClD,yBAAyB;YACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBAC1C,YAAY;gBACZ,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;oBAClB,SAAS;iBACZ;gBACD,IAAI,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC/B,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC;iBAC1B;aACJ;YAED,MAAM,IAAI,KAAK,CAAC,sBAAsB,YAAY,CAAC,CAAC,CAAC,kDAAkD,CAAC,CAAC;QAC7G,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,aAAqB,EAAE,EAAE;YAC5B,mBAAmB;YACnB,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,EAAE;gBAC9C,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,iBAAiB,aAAa,aAAa,eAAe,EAAE,KAAK,CAAC,CAAC,IAAI,CAC7I,CAAC,IAAI,EAAE,EAAE,CAA2B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CACvD,CAAC;aACL;YACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;QACvD,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,OAAiC,EAAE,EAAE;YACxC,OAAO,IAAI,6BAA6B,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;QACzJ,CAAC,CAAC,CAAC;IACX,CAAC;IAEO,MAAM,CAAC,qCAAqC,CAAC,YAAsB,EAAE,OAAsB,EAAE,KAAY;QAC7G,kBAAkB;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAC1C,YAAY;YACZ,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;gBAClB,SAAS;aACZ;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACvC,MAAM,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtE,IAAI,oBAAoB,EAAE;oBACtB,OAAO,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;iBAChE;aACJ;SACJ;QAED,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;IAC3F,CAAC;;AAnOc,kDAAqB,GAAoD,EAAE,CAAC;AAC5E,uCAAU,GAAsC,EAAE,CAAC;AAClE,oBAAoB;AACL,oDAAuB,GAAiE,EAAE,CAAC;AAG1G;;GAEG;AACW,8CAAiB,GAAG,2EAA2E,CAAC;AAC9G;;GAEG;AACW,uDAA0B,GAAY,IAAI,CAAC;AACzD;;GAEG;AACW,gDAAmB,GAAY,IAAI,CAAC;AAElD;;;GAGG;AACW,mDAAsB,GAAY,IAAI,CAAC;AA+MzD,qEAAqE;AACrE,4BAA4B,CAAC,kBAAkB,CAAC,mCAAmC,CAAC,SAAS,EAAE,CAAC,OAAsB,EAAE,KAAY,EAAE,EAAE;IACpI,OAAO,IAAI,mCAAmC,CAAC,KAAK,EAAO,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;AACpG,CAAC,CAAC,CAAC;AAEH,qBAAqB;AACrB,4BAA4B,CAAC,gBAAgB,EAAE,CAAC", "sourcesContent": ["import type { WebXRAbstractMotionController, IMotionControllerProfile } from \"./webXRAbstractMotionController\";\r\nimport { WebXRGenericTriggerMotionController } from \"./webXRGenericMotionController\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport { WebXRProfiledMotionController } from \"./webXRProfiledMotionController\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\n\r\n/**\r\n * A construction function type to create a new controller based on an xrInput object\r\n */\r\nexport type MotionControllerConstructor = (xrInput: XRInputSource, scene: Scene) => WebXRAbstractMotionController;\r\n\r\n/**\r\n * The MotionController Manager manages all registered motion controllers and loads the right one when needed.\r\n *\r\n * When this repository is complete: https://github.com/immersive-web/webxr-input-profiles/tree/master/packages/assets\r\n * it should be replaced with auto-loaded controllers.\r\n *\r\n * When using a model try to stay as generic as possible. Eventually there will be no need in any of the controller classes\r\n */\r\n\r\nconst controllerCache: Array<{\r\n    filename: string;\r\n    path: string;\r\n    meshes: AbstractMesh[];\r\n}> = [];\r\n\r\n/**\r\n * Motion controller manager is managing the different webxr profiles and makes sure the right\r\n * controller is being loaded.\r\n */\r\nexport class WebXRMotionControllerManager {\r\n    private static _AvailableControllers: { [type: string]: MotionControllerConstructor } = {};\r\n    private static _Fallbacks: { [profileId: string]: string[] } = {};\r\n    // cache for loading\r\n    private static _ProfileLoadingPromises: { [profileName: string]: Promise<IMotionControllerProfile> } = {};\r\n    private static _ProfilesList: Nullable<Promise<{ [profile: string]: string }>>;\r\n\r\n    /**\r\n     * The base URL of the online controller repository. Can be changed at any time.\r\n     */\r\n    public static BaseRepositoryUrl = \"https://immersive-web.github.io/webxr-input-profiles/packages/viewer/dist\";\r\n    /**\r\n     * Which repository gets priority - local or online\r\n     */\r\n    public static PrioritizeOnlineRepository: boolean = true;\r\n    /**\r\n     * Use the online repository, or use only locally-defined controllers\r\n     */\r\n    public static UseOnlineRepository: boolean = true;\r\n\r\n    /**\r\n     * Disable the controller cache and load the models each time a new WebXRProfileMotionController is loaded.\r\n     * Defaults to true.\r\n     */\r\n    public static DisableControllerCache: boolean = true;\r\n\r\n    /**\r\n     * Clear the cache used for profile loading and reload when requested again\r\n     */\r\n    public static ClearProfilesCache() {\r\n        this._ProfilesList = null;\r\n        this._ProfileLoadingPromises = {};\r\n    }\r\n\r\n    /**\r\n     * Register the default fallbacks.\r\n     * This function is called automatically when this file is imported.\r\n     */\r\n    public static DefaultFallbacks() {\r\n        this.RegisterFallbacksForProfileId(\"google-daydream\", [\"generic-touchpad\"]);\r\n        this.RegisterFallbacksForProfileId(\"htc-vive-focus\", [\"generic-trigger-touchpad\"]);\r\n        this.RegisterFallbacksForProfileId(\"htc-vive\", [\"generic-trigger-squeeze-touchpad\"]);\r\n        this.RegisterFallbacksForProfileId(\"magicleap-one\", [\"generic-trigger-squeeze-touchpad\"]);\r\n        this.RegisterFallbacksForProfileId(\"windows-mixed-reality\", [\"generic-trigger-squeeze-touchpad-thumbstick\"]);\r\n        this.RegisterFallbacksForProfileId(\"microsoft-mixed-reality\", [\"windows-mixed-reality\", \"generic-trigger-squeeze-touchpad-thumbstick\"]);\r\n        this.RegisterFallbacksForProfileId(\"oculus-go\", [\"generic-trigger-touchpad\"]);\r\n        this.RegisterFallbacksForProfileId(\"oculus-touch-v2\", [\"oculus-touch\", \"generic-trigger-squeeze-thumbstick\"]);\r\n        this.RegisterFallbacksForProfileId(\"oculus-touch\", [\"generic-trigger-squeeze-thumbstick\"]);\r\n        this.RegisterFallbacksForProfileId(\"samsung-gearvr\", [\"windows-mixed-reality\", \"generic-trigger-squeeze-touchpad-thumbstick\"]);\r\n        this.RegisterFallbacksForProfileId(\"samsung-odyssey\", [\"generic-touchpad\"]);\r\n        this.RegisterFallbacksForProfileId(\"valve-index\", [\"generic-trigger-squeeze-touchpad-thumbstick\"]);\r\n        this.RegisterFallbacksForProfileId(\"generic-hand-select\", [\"generic-trigger\"]);\r\n    }\r\n\r\n    /**\r\n     * Find a fallback profile if the profile was not found. There are a few predefined generic profiles.\r\n     * @param profileId the profile to which a fallback needs to be found\r\n     * @returns an array with corresponding fallback profiles\r\n     */\r\n    public static FindFallbackWithProfileId(profileId: string): string[] {\r\n        const returnArray = this._Fallbacks[profileId] || [];\r\n\r\n        returnArray.unshift(profileId);\r\n        return returnArray;\r\n    }\r\n\r\n    /**\r\n     * When acquiring a new xrInput object (usually by the WebXRInput class), match it with the correct profile.\r\n     * The order of search:\r\n     *\r\n     * 1) Iterate the profiles array of the xr input and try finding a corresponding motion controller\r\n     * 2) (If not found) search in the gamepad id and try using it (legacy versions only)\r\n     * 3) search for registered fallbacks (should be redundant, nonetheless it makes sense to check)\r\n     * 4) return the generic trigger controller if none were found\r\n     *\r\n     * @param xrInput the xrInput to which a new controller is initialized\r\n     * @param scene the scene to which the model will be added\r\n     * @param forceProfile force a certain profile for this controller\r\n     * @returns A promise that fulfils with the motion controller class for this profile id or the generic standard class if none was found\r\n     */\r\n    public static GetMotionControllerWithXRInput(xrInput: XRInputSource, scene: Scene, forceProfile?: string): Promise<WebXRAbstractMotionController> {\r\n        const profileArray: string[] = [];\r\n        if (forceProfile) {\r\n            profileArray.push(forceProfile);\r\n        }\r\n        profileArray.push(...(xrInput.profiles || []));\r\n\r\n        // emulator support\r\n        if (profileArray.length && !profileArray[0]) {\r\n            // remove the first \"undefined\" that the emulator is adding\r\n            profileArray.pop();\r\n        }\r\n\r\n        // legacy support - try using the gamepad id\r\n        if (xrInput.gamepad && xrInput.gamepad.id) {\r\n            switch (xrInput.gamepad.id) {\r\n                case xrInput.gamepad.id.match(/oculus touch/gi) ? xrInput.gamepad.id : undefined:\r\n                    // oculus in gamepad id\r\n                    profileArray.push(\"oculus-touch-v2\");\r\n                    break;\r\n            }\r\n        }\r\n\r\n        // make sure microsoft/windows mixed reality works correctly\r\n        const windowsMRIdx = profileArray.indexOf(\"windows-mixed-reality\");\r\n        if (windowsMRIdx !== -1) {\r\n            profileArray.splice(windowsMRIdx, 0, \"microsoft-mixed-reality\");\r\n        }\r\n\r\n        if (!profileArray.length) {\r\n            profileArray.push(\"generic-trigger\");\r\n        }\r\n\r\n        if (this.UseOnlineRepository) {\r\n            const firstFunction = this.PrioritizeOnlineRepository ? this._LoadProfileFromRepository : this._LoadProfilesFromAvailableControllers;\r\n            const secondFunction = this.PrioritizeOnlineRepository ? this._LoadProfilesFromAvailableControllers : this._LoadProfileFromRepository;\r\n\r\n            return firstFunction.call(this, profileArray, xrInput, scene).catch(() => {\r\n                return secondFunction.call(this, profileArray, xrInput, scene);\r\n            });\r\n        } else {\r\n            // use only available functions\r\n            return this._LoadProfilesFromAvailableControllers(profileArray, xrInput, scene);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Register a new controller based on its profile. This function will be called by the controller classes themselves.\r\n     *\r\n     * If you are missing a profile, make sure it is imported in your source, otherwise it will not register.\r\n     *\r\n     * @param type the profile type to register\r\n     * @param constructFunction the function to be called when loading this profile\r\n     */\r\n    public static RegisterController(type: string, constructFunction: MotionControllerConstructor) {\r\n        this._AvailableControllers[type] = constructFunction;\r\n    }\r\n\r\n    /**\r\n     * Register a fallback to a specific profile.\r\n     * @param profileId the profileId that will receive the fallbacks\r\n     * @param fallbacks A list of fallback profiles\r\n     */\r\n    public static RegisterFallbacksForProfileId(profileId: string, fallbacks: string[]): void {\r\n        if (this._Fallbacks[profileId]) {\r\n            this._Fallbacks[profileId].push(...fallbacks);\r\n        } else {\r\n            this._Fallbacks[profileId] = fallbacks;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Will update the list of profiles available in the repository\r\n     * @returns a promise that resolves to a map of profiles available online\r\n     */\r\n    public static UpdateProfilesList() {\r\n        this._ProfilesList = Tools.LoadFileAsync(this.BaseRepositoryUrl + \"/profiles/profilesList.json\", false).then((data) => {\r\n            return JSON.parse(data);\r\n        });\r\n        return this._ProfilesList;\r\n    }\r\n\r\n    /**\r\n     * Clear the controller's cache (usually happens at the end of a session)\r\n     */\r\n    public static ClearControllerCache() {\r\n        controllerCache.forEach((cacheItem) => {\r\n            cacheItem.meshes.forEach((mesh) => {\r\n                mesh.dispose(false, true);\r\n            });\r\n        });\r\n        controllerCache.length = 0;\r\n    }\r\n\r\n    private static _LoadProfileFromRepository(profileArray: string[], xrInput: XRInputSource, scene: Scene): Promise<WebXRAbstractMotionController> {\r\n        return Promise.resolve()\r\n            .then(() => {\r\n                if (!this._ProfilesList) {\r\n                    return this.UpdateProfilesList();\r\n                } else {\r\n                    return this._ProfilesList;\r\n                }\r\n            })\r\n            .then((profilesList: { [profile: string]: string }) => {\r\n                // load the right profile\r\n                for (let i = 0; i < profileArray.length; ++i) {\r\n                    // defensive\r\n                    if (!profileArray[i]) {\r\n                        continue;\r\n                    }\r\n                    if (profilesList[profileArray[i]]) {\r\n                        return profileArray[i];\r\n                    }\r\n                }\r\n\r\n                throw new Error(`neither controller ${profileArray[0]} nor all fallbacks were found in the repository,`);\r\n            })\r\n            .then((profileToLoad: string) => {\r\n                // load the profile\r\n                if (!this._ProfileLoadingPromises[profileToLoad]) {\r\n                    this._ProfileLoadingPromises[profileToLoad] = Tools.LoadFileAsync(`${this.BaseRepositoryUrl}/profiles/${profileToLoad}/profile.json`, false).then(\r\n                        (data) => <IMotionControllerProfile>JSON.parse(data)\r\n                    );\r\n                }\r\n                return this._ProfileLoadingPromises[profileToLoad];\r\n            })\r\n            .then((profile: IMotionControllerProfile) => {\r\n                return new WebXRProfiledMotionController(scene, xrInput, profile, this.BaseRepositoryUrl, this.DisableControllerCache ? undefined : controllerCache);\r\n            });\r\n    }\r\n\r\n    private static _LoadProfilesFromAvailableControllers(profileArray: string[], xrInput: XRInputSource, scene: Scene) {\r\n        // check fallbacks\r\n        for (let i = 0; i < profileArray.length; ++i) {\r\n            // defensive\r\n            if (!profileArray[i]) {\r\n                continue;\r\n            }\r\n            const fallbacks = this.FindFallbackWithProfileId(profileArray[i]);\r\n            for (let j = 0; j < fallbacks.length; ++j) {\r\n                const constructionFunction = this._AvailableControllers[fallbacks[j]];\r\n                if (constructionFunction) {\r\n                    return Promise.resolve(constructionFunction(xrInput, scene));\r\n                }\r\n            }\r\n        }\r\n\r\n        throw new Error(`no controller requested was found in the available controllers list`);\r\n    }\r\n}\r\n\r\n// register the generic profile(s) here so we will at least have them\r\nWebXRMotionControllerManager.RegisterController(WebXRGenericTriggerMotionController.ProfileId, (xrInput: XRInputSource, scene: Scene) => {\r\n    return new WebXRGenericTriggerMotionController(scene, <any>xrInput.gamepad, xrInput.handedness);\r\n});\r\n\r\n// register fallbacks\r\nWebXRMotionControllerManager.DefaultFallbacks();\r\n"]}