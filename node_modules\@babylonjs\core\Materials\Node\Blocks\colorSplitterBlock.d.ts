import { NodeMaterialBlock } from "../nodeMaterialBlock";
import type { NodeMaterialBuildState } from "../nodeMaterialBuildState";
import type { NodeMaterialConnectionPoint } from "../nodeMaterialBlockConnectionPoint";
/**
 * Block used to expand a Color3/4 into 4 outputs (one for each component)
 */
export declare class ColorSplitterBlock extends NodeMaterialBlock {
    /**
     * Create a new ColorSplitterBlock
     * @param name defines the block name
     */
    constructor(name: string);
    /**
     * Gets the current class name
     * @returns the class name
     */
    getClassName(): string;
    /**
     * Gets the rgba component (input)
     */
    get rgba(): NodeMaterialConnectionPoint;
    /**
     * Gets the rgb component (input)
     */
    get rgbIn(): NodeMaterialConnectionPoint;
    /**
     * Gets the rgb component (output)
     */
    get rgbOut(): NodeMaterialConnectionPoint;
    /**
     * Gets the r component (output)
     */
    get r(): NodeMaterialConnectionPoint;
    /**
     * Gets the g component (output)
     */
    get g(): NodeMaterialConnectionPoint;
    /**
     * Gets the b component (output)
     */
    get b(): NodeMaterialConnectionPoint;
    /**
     * Gets the a component (output)
     */
    get a(): NodeMaterialConnectionPoint;
    protected _inputRename(name: string): string;
    protected _outputRename(name: string): string;
    protected _buildBlock(state: NodeMaterialBuildState): this | undefined;
}
