{"version": 3, "file": "reflectionProbe.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Probes/reflectionProbe.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,wBAAwB,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAClF,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AACvE,OAAO,EAAE,mBAAmB,EAAE,MAAM,2CAA2C,CAAC;AAChF,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAGvD,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAEjD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AA0BjD,aAAa,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAAU,QAAyB;IAC/E,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QACxB,OAAO,CAAC,CAAC,CAAC;KACb;IAED,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACtD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;QACd,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;KAC1C;IAED,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,aAAa,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,kBAAmC;IACtF,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QACxB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;KAC9B;IAED,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACnD,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,OAAO,eAAe;IA0BxB;;;;;;;;OAQG;IACH;IACI,oCAAoC;IAC7B,IAAY,EACnB,IAAY,EACZ,KAAY,EACZ,eAAe,GAAG,IAAI,EACtB,QAAQ,GAAG,KAAK,EAChB,WAAW,GAAG,KAAK;QALZ,SAAI,GAAJ,IAAI,CAAQ;QAjCf,gBAAW,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAChC,YAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACzB,SAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAItB,iBAAY,GAAG,KAAK,CAAC;QAI7B,2DAA2D;QAEpD,aAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEjC;;WAEG;QACI,aAAQ,GAAQ,IAAI,CAAC;QAE5B,gBAAgB;QACT,qBAAgB,GAA4B,IAAI,CAAC;QAoBpD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,sBAAsB,EAAE;YAC1C,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;YACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;gBACxB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,qCAAqC,IAAI,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;aAClH;SACJ;QAED,uCAAuC;QACvC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG,EAAuB,CAAC;SAC1D;QACD,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAExC,IAAI,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC;QACtD,IAAI,QAAQ,EAAE;YACV,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC;YAC/C,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,WAAW,GAAG,SAAS,CAAC,sBAAsB,CAAC;aAClD;iBAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAChC,WAAW,GAAG,SAAS,CAAC,iBAAiB,CAAC;aAC7C;SACJ;QACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QACjH,IAAI,CAAC,oBAAoB,CAAC,UAAU,GAAG,CAAC,WAAW,CAAC;QACpD,IAAI,CAAC,oBAAoB,CAAC,OAAO,GAAG,KAAK,CAAC,oBAAoB,CAAC;QAE/D,MAAM,qBAAqB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,qBAAqB,CAAC;QAEtE,IAAI,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,SAAiB,EAAE,EAAE;YACzE,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;gBACxD,KAAK,CAAC,qBAAqB,EAAE,CAAC,YAAY,EAAE,CAAC;aAChD;YACD,QAAQ,SAAS,EAAE;gBACf,KAAK,CAAC;oBACF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAClC,MAAM;gBACV,KAAK,CAAC;oBACF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACnC,MAAM;gBACV,KAAK,CAAC;oBACF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC3D,MAAM;gBACV,KAAK,CAAC;oBACF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC3D,MAAM;gBACV,KAAK,CAAC;oBACF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpE,MAAM;gBACV,KAAK,CAAC;oBACF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpE,MAAM;aACb;YAED,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC,CAAC;aACpE;YAED,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAEhD,MAAM,cAAc,GAAG,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;YAChG,MAAM,mBAAmB,GAAG,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAE3G,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAE5E,IAAI,KAAK,CAAC,YAAY,EAAE;gBACpB,IAAI,CAAC,iBAAiB,GAAG,mBAAmB,CACxC,IAAI,CAAC,EAAE,GAAG,CAAC,EACX,CAAC,EACD,qBAAqB,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EACzE,qBAAqB,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EACzE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,CAC1C,CAAC;gBACF,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBACnE,IAAI,KAAK,CAAC,YAAY,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE;oBAC3E,IAAI,CAAC,oBAAoB,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC,SAAS,IAAI,IAAI,CAAC;iBACjF;aACJ;YACD,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,IAAI,yBAAkC,CAAC;QAEvC,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,EAAE;YACtD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC;YACtD,KAAK,CAAC,SAAS,EAAE,CAAC,eAAe,EAAE,CAAC,mCAAmC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;YAClF,yBAAyB,GAAG,IAAI,CAAC,MAAM,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;YACxF,IAAI,WAAW,EAAE;gBACb,KAAK,CAAC,4BAA4B,CAAC,kBAAkB,GAAG,IAAI,CAAC;aAChE;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;YACvD,KAAK,CAAC,4BAA4B,CAAC,kBAAkB,GAAG,yBAAyB,CAAC;YAClF,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC;YACjC,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aACtD;YACD,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAClC,KAAK,CAAC,SAAS,EAAE,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,mGAAmG;IACnG,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;IAC7C,CAAC;IAED,IAAW,OAAO,CAAC,KAAa;QAC5B,IAAI,CAAC,oBAAoB,CAAC,OAAO,GAAG,KAAK,CAAC;IAC9C,CAAC;IAED,uEAAuE;IACvE,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC;IACjD,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,oBAAoB,CAAC,WAAW,GAAG,KAAK,CAAC;IAClD,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,sDAAsD;IACtD,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED,gDAAgD;IAChD,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;IAChD,CAAC;IAED,IAAW,UAAU,CAAC,KAA+B;QACjD,IAAI,CAAC,oBAAoB,CAAC,UAAU,GAAG,KAAK,CAAC;IACjD,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,IAA4B;QAC5C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACI,iCAAiC,CAAC,gBAAwB,EAAE,qBAA8B;QAC7F,IAAI,CAAC,oBAAoB,CAAC,iCAAiC,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAAC;IACzG,CAAC;IAED;;OAEG;IACI,OAAO;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEzD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,iCAAiC;YACjC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACjD;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACZ,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAC3D;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;YAC9B,IAAI,CAAC,oBAAqB,GAAG,IAAI,CAAC;SAC3C;QAED,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;gBAC/B,GAAG,CAAC,OAAO,EAAE,CAAC;aACjB;YACD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACxB;IACL,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,WAAqB;QACjC,IAAI,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAE/B,IAAI,WAAW,EAAE;YACb,GAAG,IAAI,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAEjD,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,GAAG,IAAI,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;aACxD;SACJ;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,CAAC,CAAC;QACvG,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC7C,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE7C,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,qBAA0B,EAAE,KAAY,EAAE,OAAe;QACzE,IAAI,eAAe,GAA8B,IAAI,CAAC;QACtD,IAAI,KAAK,CAAC,gBAAgB,EAAE;YACxB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAChE,MAAM,EAAE,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBACzC,IAAI,EAAE,CAAC,IAAI,KAAK,qBAAqB,CAAC,IAAI,EAAE;oBACxC,eAAe,GAAG,EAAE,CAAC;oBACrB,MAAM;iBACT;aACJ;SACJ;QAED,eAAe,GAAG,mBAAmB,CAAC,KAAK,CACvC,GAAG,EAAE,CAAC,eAAe,IAAI,IAAI,eAAe,CAAC,qBAAqB,CAAC,IAAI,EAAE,qBAAqB,CAAC,gBAAgB,EAAE,KAAK,EAAE,qBAAqB,CAAC,gBAAgB,CAAC,EAC/J,qBAAqB,EACrB,KAAK,EACL,OAAO,CACV,CAAC;QACF,eAAe,CAAC,WAAW,CAAC,kBAAkB,GAAG,qBAAqB,CAAC,UAAU,CAAC;QAElF,IAAI,qBAAqB,CAAC,aAAa,EAAE;YACrC,eAAe,CAAC,YAAY,CAAC,KAAK,CAAC,WAAW,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC,CAAC;SACxF;QAED,IAAI,qBAAqB,CAAC,QAAQ,EAAE;YAChC,eAAe,CAAC,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC;SAC7D;QAED,OAAO,eAAe,CAAC;IAC3B,CAAC;CACJ;AAlTW;IADP,wBAAwB,EAAE;sDACmB;AAQvC;IADN,kBAAkB,EAAE;iDACY", "sourcesContent": ["import { serializeAsMeshReference, serializeAsVector3 } from \"../Misc/decorators\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\nimport { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport { Matrix, Vector3 } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Nullable } from \"../types\";\r\nimport { AbstractScene } from \"../abstractScene\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { UniformBuffer } from \"../Materials/uniformBuffer\";\r\n\r\ndeclare module \"../abstractScene\" {\r\n    export interface AbstractScene {\r\n        /**\r\n         * The list of reflection probes added to the scene\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/reflectionProbes\r\n         */\r\n        reflectionProbes: Array<ReflectionProbe>;\r\n\r\n        /**\r\n         * Removes the given reflection probe from this scene.\r\n         * @param toRemove The reflection probe to remove\r\n         * @returns The index of the removed reflection probe\r\n         */\r\n        removeReflectionProbe(toRemove: ReflectionProbe): number;\r\n\r\n        /**\r\n         * Adds the given reflection probe to this scene.\r\n         * @param newReflectionProbe The reflection probe to add\r\n         */\r\n        addReflectionProbe(newReflectionProbe: ReflectionProbe): void;\r\n    }\r\n}\r\n\r\nAbstractScene.prototype.removeReflectionProbe = function (toRemove: ReflectionProbe): number {\r\n    if (!this.reflectionProbes) {\r\n        return -1;\r\n    }\r\n\r\n    const index = this.reflectionProbes.indexOf(toRemove);\r\n    if (index !== -1) {\r\n        this.reflectionProbes.splice(index, 1);\r\n    }\r\n\r\n    return index;\r\n};\r\n\r\nAbstractScene.prototype.addReflectionProbe = function (newReflectionProbe: ReflectionProbe): void {\r\n    if (!this.reflectionProbes) {\r\n        this.reflectionProbes = [];\r\n    }\r\n\r\n    this.reflectionProbes.push(newReflectionProbe);\r\n};\r\n\r\n/**\r\n * Class used to generate realtime reflection / refraction cube textures\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/reflectionProbes\r\n */\r\nexport class ReflectionProbe {\r\n    private _scene: Scene;\r\n    private _renderTargetTexture: RenderTargetTexture;\r\n    private _projectionMatrix: Matrix;\r\n    private _viewMatrix = Matrix.Identity();\r\n    private _target = Vector3.Zero();\r\n    private _add = Vector3.Zero();\r\n    @serializeAsMeshReference()\r\n    private _attachedMesh: Nullable<AbstractMesh>;\r\n\r\n    private _invertYAxis = false;\r\n    private _sceneUBOs: UniformBuffer[];\r\n    private _currentSceneUBO: UniformBuffer;\r\n\r\n    /** Gets or sets probe position (center of the cube map) */\r\n    @serializeAsVector3()\r\n    public position = Vector3.Zero();\r\n\r\n    /**\r\n     * Gets or sets an object used to store user defined information for the reflection probe.\r\n     */\r\n    public metadata: any = null;\r\n\r\n    /** @internal */\r\n    public _parentContainer: Nullable<AbstractScene> = null;\r\n\r\n    /**\r\n     * Creates a new reflection probe\r\n     * @param name defines the name of the probe\r\n     * @param size defines the texture resolution (for each face)\r\n     * @param scene defines the hosting scene\r\n     * @param generateMipMaps defines if mip maps should be generated automatically (true by default)\r\n     * @param useFloat defines if HDR data (float data) should be used to store colors (false by default)\r\n     * @param linearSpace defines if the probe should be generated in linear space or not (false by default)\r\n     */\r\n    constructor(\r\n        /** defines the name of the probe */\r\n        public name: string,\r\n        size: number,\r\n        scene: Scene,\r\n        generateMipMaps = true,\r\n        useFloat = false,\r\n        linearSpace = false\r\n    ) {\r\n        this._scene = scene;\r\n\r\n        if (scene.getEngine().supportsUniformBuffers) {\r\n            this._sceneUBOs = [];\r\n            for (let i = 0; i < 6; ++i) {\r\n                this._sceneUBOs.push(scene.createSceneUniformBuffer(`Scene for Reflection Probe (name \"${name}\") face #${i}`));\r\n            }\r\n        }\r\n\r\n        // Create the scene field if not exist.\r\n        if (!this._scene.reflectionProbes) {\r\n            this._scene.reflectionProbes = [] as ReflectionProbe[];\r\n        }\r\n        this._scene.reflectionProbes.push(this);\r\n\r\n        let textureType = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n        if (useFloat) {\r\n            const caps = this._scene.getEngine().getCaps();\r\n            if (caps.textureHalfFloatRender) {\r\n                textureType = Constants.TEXTURETYPE_HALF_FLOAT;\r\n            } else if (caps.textureFloatRender) {\r\n                textureType = Constants.TEXTURETYPE_FLOAT;\r\n            }\r\n        }\r\n        this._renderTargetTexture = new RenderTargetTexture(name, size, scene, generateMipMaps, true, textureType, true);\r\n        this._renderTargetTexture.gammaSpace = !linearSpace;\r\n        this._renderTargetTexture.invertZ = scene.useRightHandedSystem;\r\n\r\n        const useReverseDepthBuffer = scene.getEngine().useReverseDepthBuffer;\r\n\r\n        this._renderTargetTexture.onBeforeRenderObservable.add((faceIndex: number) => {\r\n            if (this._sceneUBOs) {\r\n                scene.setSceneUniformBuffer(this._sceneUBOs[faceIndex]);\r\n                scene.getSceneUniformBuffer().unbindEffect();\r\n            }\r\n            switch (faceIndex) {\r\n                case 0:\r\n                    this._add.copyFromFloats(1, 0, 0);\r\n                    break;\r\n                case 1:\r\n                    this._add.copyFromFloats(-1, 0, 0);\r\n                    break;\r\n                case 2:\r\n                    this._add.copyFromFloats(0, this._invertYAxis ? 1 : -1, 0);\r\n                    break;\r\n                case 3:\r\n                    this._add.copyFromFloats(0, this._invertYAxis ? -1 : 1, 0);\r\n                    break;\r\n                case 4:\r\n                    this._add.copyFromFloats(0, 0, scene.useRightHandedSystem ? -1 : 1);\r\n                    break;\r\n                case 5:\r\n                    this._add.copyFromFloats(0, 0, scene.useRightHandedSystem ? 1 : -1);\r\n                    break;\r\n            }\r\n\r\n            if (this._attachedMesh) {\r\n                this.position.copyFrom(this._attachedMesh.getAbsolutePosition());\r\n            }\r\n\r\n            this.position.addToRef(this._add, this._target);\r\n\r\n            const lookAtFunction = scene.useRightHandedSystem ? Matrix.LookAtRHToRef : Matrix.LookAtLHToRef;\r\n            const perspectiveFunction = scene.useRightHandedSystem ? Matrix.PerspectiveFovRH : Matrix.PerspectiveFovLH;\r\n\r\n            lookAtFunction(this.position, this._target, Vector3.Up(), this._viewMatrix);\r\n\r\n            if (scene.activeCamera) {\r\n                this._projectionMatrix = perspectiveFunction(\r\n                    Math.PI / 2,\r\n                    1,\r\n                    useReverseDepthBuffer ? scene.activeCamera.maxZ : scene.activeCamera.minZ,\r\n                    useReverseDepthBuffer ? scene.activeCamera.minZ : scene.activeCamera.maxZ,\r\n                    this._scene.getEngine().isNDCHalfZRange\r\n                );\r\n                scene.setTransformMatrix(this._viewMatrix, this._projectionMatrix);\r\n                if (scene.activeCamera.isRigCamera && !this._renderTargetTexture.activeCamera) {\r\n                    this._renderTargetTexture.activeCamera = scene.activeCamera.rigParent || null;\r\n                }\r\n            }\r\n            scene._forcedViewPosition = this.position;\r\n        });\r\n\r\n        let currentApplyByPostProcess: boolean;\r\n\r\n        this._renderTargetTexture.onBeforeBindObservable.add(() => {\r\n            this._currentSceneUBO = scene.getSceneUniformBuffer();\r\n            scene.getEngine()._debugPushGroup?.(`reflection probe generation for ${name}`, 1);\r\n            currentApplyByPostProcess = this._scene.imageProcessingConfiguration.applyByPostProcess;\r\n            if (linearSpace) {\r\n                scene.imageProcessingConfiguration.applyByPostProcess = true;\r\n            }\r\n        });\r\n\r\n        this._renderTargetTexture.onAfterUnbindObservable.add(() => {\r\n            scene.imageProcessingConfiguration.applyByPostProcess = currentApplyByPostProcess;\r\n            scene._forcedViewPosition = null;\r\n            if (this._sceneUBOs) {\r\n                scene.setSceneUniformBuffer(this._currentSceneUBO);\r\n            }\r\n            scene.updateTransformMatrix(true);\r\n            scene.getEngine()._debugPopGroup?.(1);\r\n        });\r\n    }\r\n\r\n    /** Gets or sets the number of samples to use for multi-sampling (0 by default). Required WebGL2 */\r\n    public get samples(): number {\r\n        return this._renderTargetTexture.samples;\r\n    }\r\n\r\n    public set samples(value: number) {\r\n        this._renderTargetTexture.samples = value;\r\n    }\r\n\r\n    /** Gets or sets the refresh rate to use (on every frame by default) */\r\n    public get refreshRate(): number {\r\n        return this._renderTargetTexture.refreshRate;\r\n    }\r\n\r\n    public set refreshRate(value: number) {\r\n        this._renderTargetTexture.refreshRate = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the hosting scene\r\n     * @returns a Scene\r\n     */\r\n    public getScene(): Scene {\r\n        return this._scene;\r\n    }\r\n\r\n    /** Gets the internal CubeTexture used to render to */\r\n    public get cubeTexture(): RenderTargetTexture {\r\n        return this._renderTargetTexture;\r\n    }\r\n\r\n    /** Gets or sets the list of meshes to render */\r\n    public get renderList(): Nullable<AbstractMesh[]> {\r\n        return this._renderTargetTexture.renderList;\r\n    }\r\n\r\n    public set renderList(value: Nullable<AbstractMesh[]>) {\r\n        this._renderTargetTexture.renderList = value;\r\n    }\r\n\r\n    /**\r\n     * Attach the probe to a specific mesh (Rendering will be done from attached mesh's position)\r\n     * @param mesh defines the mesh to attach to\r\n     */\r\n    public attachToMesh(mesh: Nullable<AbstractMesh>): void {\r\n        this._attachedMesh = mesh;\r\n    }\r\n\r\n    /**\r\n     * Specifies whether or not the stencil and depth buffer are cleared between two rendering groups\r\n     * @param renderingGroupId The rendering group id corresponding to its index\r\n     * @param autoClearDepthStencil Automatically clears depth and stencil between groups if true.\r\n     */\r\n    public setRenderingAutoClearDepthStencil(renderingGroupId: number, autoClearDepthStencil: boolean): void {\r\n        this._renderTargetTexture.setRenderingAutoClearDepthStencil(renderingGroupId, autoClearDepthStencil);\r\n    }\r\n\r\n    /**\r\n     * Clean all associated resources\r\n     */\r\n    public dispose() {\r\n        const index = this._scene.reflectionProbes.indexOf(this);\r\n\r\n        if (index !== -1) {\r\n            // Remove from the scene if found\r\n            this._scene.reflectionProbes.splice(index, 1);\r\n        }\r\n\r\n        if (this._parentContainer) {\r\n            const index = this._parentContainer.reflectionProbes.indexOf(this);\r\n            if (index > -1) {\r\n                this._parentContainer.reflectionProbes.splice(index, 1);\r\n            }\r\n            this._parentContainer = null;\r\n        }\r\n\r\n        if (this._renderTargetTexture) {\r\n            this._renderTargetTexture.dispose();\r\n            (<any>this._renderTargetTexture) = null;\r\n        }\r\n\r\n        if (this._sceneUBOs) {\r\n            for (const ubo of this._sceneUBOs) {\r\n                ubo.dispose();\r\n            }\r\n            this._sceneUBOs = [];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Converts the reflection probe information to a readable string for debug purpose.\r\n     * @param fullDetails Supports for multiple levels of logging within scene loading\r\n     * @returns the human readable reflection probe info\r\n     */\r\n    public toString(fullDetails?: boolean): string {\r\n        let ret = \"Name: \" + this.name;\r\n\r\n        if (fullDetails) {\r\n            ret += \", position: \" + this.position.toString();\r\n\r\n            if (this._attachedMesh) {\r\n                ret += \", attached mesh: \" + this._attachedMesh.name;\r\n            }\r\n        }\r\n\r\n        return ret;\r\n    }\r\n\r\n    /**\r\n     * Get the class name of the refection probe.\r\n     * @returns \"ReflectionProbe\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"ReflectionProbe\";\r\n    }\r\n\r\n    /**\r\n     * Serialize the reflection probe to a JSON representation we can easily use in the respective Parse function.\r\n     * @returns The JSON representation of the texture\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this, this._renderTargetTexture.serialize());\r\n        serializationObject.isReflectionProbe = true;\r\n        serializationObject.metadata = this.metadata;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parse the JSON representation of a reflection probe in order to recreate the reflection probe in the given scene.\r\n     * @param parsedReflectionProbe Define the JSON representation of the reflection probe\r\n     * @param scene Define the scene the parsed reflection probe should be instantiated in\r\n     * @param rootUrl Define the root url of the parsing sequence in the case of relative dependencies\r\n     * @returns The parsed reflection probe if successful\r\n     */\r\n    public static Parse(parsedReflectionProbe: any, scene: Scene, rootUrl: string): Nullable<ReflectionProbe> {\r\n        let reflectionProbe: Nullable<ReflectionProbe> = null;\r\n        if (scene.reflectionProbes) {\r\n            for (let index = 0; index < scene.reflectionProbes.length; index++) {\r\n                const rp = scene.reflectionProbes[index];\r\n                if (rp.name === parsedReflectionProbe.name) {\r\n                    reflectionProbe = rp;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n\r\n        reflectionProbe = SerializationHelper.Parse(\r\n            () => reflectionProbe || new ReflectionProbe(parsedReflectionProbe.name, parsedReflectionProbe.renderTargetSize, scene, parsedReflectionProbe._generateMipMaps),\r\n            parsedReflectionProbe,\r\n            scene,\r\n            rootUrl\r\n        );\r\n        reflectionProbe.cubeTexture._waitingRenderList = parsedReflectionProbe.renderList;\r\n\r\n        if (parsedReflectionProbe._attachedMesh) {\r\n            reflectionProbe.attachToMesh(scene.getMeshById(parsedReflectionProbe._attachedMesh));\r\n        }\r\n\r\n        if (parsedReflectionProbe.metadata) {\r\n            reflectionProbe.metadata = parsedReflectionProbe.metadata;\r\n        }\r\n\r\n        return reflectionProbe;\r\n    }\r\n}\r\n"]}