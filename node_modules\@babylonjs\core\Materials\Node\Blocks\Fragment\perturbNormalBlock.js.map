{"version": 3, "file": "perturbNormalBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Fragment/perturbNormalBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,oCAAoC,EAAE,MAAM,wCAAwC,CAAC;AAC9F,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAI3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAGjD,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAEtG,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AACxG,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAEtC,OAAO,8DAA8D,CAAC;AACtE,OAAO,0DAA0D,CAAC;AAClE,OAAO,iDAAiD,CAAC;AAEzD;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,iBAAiB;IAkBrD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAtB3C,+BAA0B,GAAG,EAAE,CAAC;QAChC,iCAA4B,GAAG,EAAE,CAAC;QAClC,qBAAgB,GAAG,EAAE,CAAC;QAE9B,iFAAiF;QAE1E,YAAO,GAAG,KAAK,CAAC;QACvB,iFAAiF;QAE1E,YAAO,GAAG,KAAK,CAAC;QACvB,kFAAkF;QAE3E,yBAAoB,GAAG,KAAK,CAAC;QACpC,8EAA8E;QAEvE,4BAAuB,GAAG,KAAK,CAAC;QASnC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,SAAS;QACT,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,qCAAqC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC1F,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,qCAAqC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACxF,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACxF,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,qCAAqC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,qCAAqC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC1F,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,qCAAqC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACnF,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACzF,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACvF,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACxF,IAAI,CAAC,aAAa,CACd,KAAK,EACL,qCAAqC,CAAC,MAAM,EAC5C,IAAI,EACJ,wBAAwB,CAAC,iBAAiB,EAC1C,IAAI,uCAAuC,CAAC,KAAK,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,EAAE,QAAQ,EAAE,UAAU,CAAC,CAC7H,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEhF,WAAW;QACX,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;IACnF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,EAAE;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,gEAAgE;IAChE,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,cAAc,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QAC9F,MAAM,iBAAiB,GAAI,IAAI,CAAC,cAAc,CAAC,cAAe,CAAC,WAA4B,CAAC,WAAW,CAAC;QACxG,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC;QAE5K,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC/B,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QAChD,OAAO,CAAC,QAAQ,CAAC,cAAc,EAAE,YAAY,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QACrF,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QACvE,OAAO,CAAC,QAAQ,CAAC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;IAClF,CAAC;IAEM,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW;QAC/D,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC,uBAAuB,EAAE;YACjD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SAC3G;aAAM;YACH,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SAC3G;QAED,IAAI,IAAI,EAAE;YACN,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4BAA4B,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAErG,IAAI,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;gBACzD,yCAAyC;gBACzC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;aAClE;SACJ;IACL,CAAC;IAEM,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,EAAE,CAAC,IAAI;QACnH,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE;YACtB,IAAI,OAAO,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAEvH,IAAI,CAAC,OAAO,EAAE;gBACV,OAAO,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC/B,OAAO,CAAC,cAAc,EAAE,CAAC;aAC5B;YACD,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC5B,MAAM,aAAa,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;YACjD,aAAa,CAAC,KAAK,GAAG,GAAG,CAAC;YAC1B,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACjD;IACL,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QAEvC,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC,kBAAkB,CAAC,uBAAuB,CAAC,CAAC;QAEpF,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAC;QAEtE,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;QAExF,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;QAEzE,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;QAE7E,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;QAE5D,IAAI,iBAAiB,GAAG,IAAI,CAAC;QAC7B,IAAI,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE;YACpC,iBAAiB,GAAI,IAAI,CAAC,cAAc,CAAC,cAAe,CAAC,WAA4B,CAAC,WAAW,CAAC;SACrG;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC;QAE5K,MAAM,uBAAuB,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,uBAAuB;YACvE,CAAC,CAAC,MAAM;YACR,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAkB,CAAC,UAAU;gBAChD,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAkB,CAAC,KAAK,CAAC;gBAC/D,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC;QAElD,MAAM,mBAAmB,GACrB,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAkB,CAAC,UAAU;YAChF,CAAC,CAAC,gDAAgD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAkB,CAAC,KAAK,CAAC,EAAE;YAC5G,CAAC,CAAC,gDAAgD,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE,CAAC;QAEjG,KAAK,CAAC,cAAc,CAAC,aAAa,EAAE,iDAAiD,CAAC,CAAC;QAEvF,MAAM,oBAAoB,GAAG,EAAE,MAAM,EAAE,qBAAqB,EAAE,OAAO,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC3I,MAAM,UAAU,GAAG,EAAE,MAAM,EAAE,qBAAqB,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;QAClE,MAAM,yBAAyB,GAAG,EAAE,MAAM,EAAE,6BAA6B,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;QAEzF,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,IAAI,GAAG,CAAC,WAAW,EAAE;YACjB,KAAK,CAAC,iBAAiB,IAAI;;0BAEb,GAAG,CAAC,sBAAsB;;aAEvC,CAAC;SACL;aAAM,IAAI,YAAY,CAAC,WAAW,EAAE;YACjC,KAAK,CAAC,iBAAiB,IAAI,8BAA8B,WAAW,CAAC,sBAAsB,UAAU,CAAC;YACtG,KAAK,CAAC,iBAAiB,IAAI,+BAA+B,YAAY,CAAC,sBAAsB,UAAU,CAAC;YACxG,KAAK,CAAC,iBAAiB,IAAI,sDAAsD,IAAI,CAAC,4BAA4B,KAAK,CAAC;YACxH,KAAK,CAAC,iBAAiB,IAAI,0DAA0D,CAAC;SACzF;QAED,KAAK,CAAC,wBAAwB,CAAC,2BAA2B,EAAE,QAAQ,EAAE;YAClE,cAAc,EAAE,CAAC,oBAAoB,EAAE,UAAU,EAAE,yBAAyB,CAAC;SAChF,CAAC,CAAC;QAEH,KAAK,CAAC,wBAAwB,CAAC,uBAAuB,EAAE,QAAQ,EAAE;YAC9D,cAAc,EAAE;gBACZ,EAAE,MAAM,EAAE,kGAAkG,EAAE,OAAO,EAAE,EAAE,EAAE;gBAC3H,EAAE,MAAM,EAAE,iCAAiC,EAAE,OAAO,EAAE,EAAE,EAAE;gBAC1D;oBACI,MAAM,EAAE,+FAA+F;oBACvG,OAAO,EAAE,sIAAsI;iBAClJ;gBACD,EAAE,MAAM,EAAE,wDAAwD,EAAE,OAAO,EAAE,qEAAqE,EAAE;gBACpJ,EAAE,MAAM,EAAE,sCAAsC,EAAE,OAAO,EAAE,SAAS,EAAE;aACzE;SACJ,CAAC,CAAC;QAEH,MAAM,kBAAkB,GACpB,CAAC,WAAW,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC,CAAC,aAAa,iBAAiB,KAAK,EAAE,CAAC,sBAAsB,kBAAkB,CAAC;QAErK,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,gBAAgB,CAAC;QACtF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,cAAc,EAAE,QAAQ,EAAE;YAC5E,cAAc,EAAE;gBACZ,EAAE,MAAM,EAAE,mCAAmC,EAAE,OAAO,EAAE,GAAG,kBAAkB,EAAE,EAAE;gBACjF;oBACI,MAAM,EAAE,wCAAwC;oBAChD,OAAO,EAAE,sCAAsC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,IAAI;iBACxI;gBACD,EAAE,MAAM,EAAE,mFAAmF,EAAE,OAAO,EAAE,sBAAsB,kBAAkB,iBAAiB,EAAE;gBACnK;oBACI,MAAM,EAAE,oFAAoF;oBAC5F,OAAO,EAAE,4FACL,WAAW,IAAI,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,aACnE,GAAG;iBACN;gBACD;oBACI,MAAM,EAAE,yDAAyD;oBACjE,OAAO,EAAE,yDAAyD,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,GAAG;iBACvI;gBACD,EAAE,MAAM,EAAE,sBAAsB,EAAE,OAAO,EAAE,IAAI,CAAC,0BAA0B,EAAE;gBAC5E,EAAE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,mBAAmB,EAAE;gBACzD,EAAE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,uBAAuB,EAAE;gBAC7D,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC,sBAAsB,EAAE;gBAC1D,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,aAAa,CAAC,sBAAsB,GAAG,MAAM,EAAE;gBACjF,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,sBAAsB,GAAG,SAAS,EAAE;gBAChF,EAAE,MAAM,EAAE,gCAAgC,EAAE,OAAO,EAAE,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,GAAG,MAAM,EAAE;gBAC5H,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC,sBAAsB,GAAG,MAAM,EAAE;gBAC5E,EAAE,MAAM,EAAE,iBAAiB,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,EAAE;gBAC5G,oBAAoB;aACvB;SACJ,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,GAAG,GAAG,IAAI,CAAC,iBAAiB,cAAc,IAAI,CAAC,OAAO,KAAK,CAAC;QAExG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,cAAc,IAAI,CAAC,OAAO,KAAK,CAAC;QACvE,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,2BAA2B,IAAI,CAAC,oBAAoB,KAAK,CAAC;QACjG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,8BAA8B,IAAI,CAAC,uBAAuB,KAAK,CAAC;QAEvG,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAE3E,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC;QAC3C,IAAI,CAAC,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC;QAC3C,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;QACvE,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC,mBAAmB,CAAC,uBAAuB,CAAC;IACjF,CAAC;CACJ;AA/UU;IADN,sBAAsB,CAAC,eAAe,EAAE,sBAAsB,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;mDACjG;AAGhB;IADN,sBAAsB,CAAC,eAAe,EAAE,sBAAsB,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;mDACjG;AAGhB;IADN,sBAAsB,CAAC,wBAAwB,EAAE,sBAAsB,CAAC,OAAO,CAAC;gEAC7C;AAG7B;IADN,sBAAsB,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;mEACrF;AAwU3C,aAAa,CAAC,4BAA4B,EAAE,kBAAkB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport type { TextureBlock } from \"../Dual/textureBlock\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport { TBNBlock } from \"./TBNBlock\";\r\n\r\nimport \"../../../../Shaders/ShadersInclude/bumpFragmentMainFunctions\";\r\nimport \"../../../../Shaders/ShadersInclude/bumpFragmentFunctions\";\r\nimport \"../../../../Shaders/ShadersInclude/bumpFragment\";\r\n\r\n/**\r\n * Block used to perturb normals based on a normal map\r\n */\r\nexport class PerturbNormalBlock extends NodeMaterialBlock {\r\n    private _tangentSpaceParameterName = \"\";\r\n    private _tangentCorrectionFactorName = \"\";\r\n    private _worldMatrixName = \"\";\r\n\r\n    /** Gets or sets a boolean indicating that normal should be inverted on X axis */\r\n    @editableInPropertyPage(\"Invert X axis\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { notifiers: { update: false } })\r\n    public invertX = false;\r\n    /** Gets or sets a boolean indicating that normal should be inverted on Y axis */\r\n    @editableInPropertyPage(\"Invert Y axis\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { notifiers: { update: false } })\r\n    public invertY = false;\r\n    /** Gets or sets a boolean indicating that parallax occlusion should be enabled */\r\n    @editableInPropertyPage(\"Use parallax occlusion\", PropertyTypeForEdition.Boolean)\r\n    public useParallaxOcclusion = false;\r\n    /** Gets or sets a boolean indicating that sampling mode is in Object space */\r\n    @editableInPropertyPage(\"Object Space Mode\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { notifiers: { update: false } })\r\n    public useObjectSpaceNormalMap = false;\r\n\r\n    /**\r\n     * Create a new PerturbNormalBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        // Vertex\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.Vector4, false);\r\n        this.registerInput(\"worldNormal\", NodeMaterialBlockConnectionPointTypes.Vector4, false);\r\n        this.registerInput(\"worldTangent\", NodeMaterialBlockConnectionPointTypes.Vector4, true);\r\n        this.registerInput(\"uv\", NodeMaterialBlockConnectionPointTypes.Vector2, false);\r\n        this.registerInput(\"normalMapColor\", NodeMaterialBlockConnectionPointTypes.Color3, false);\r\n        this.registerInput(\"strength\", NodeMaterialBlockConnectionPointTypes.Float, false);\r\n        this.registerInput(\"viewDirection\", NodeMaterialBlockConnectionPointTypes.Vector3, true);\r\n        this.registerInput(\"parallaxScale\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"parallaxHeight\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\r\n            \"TBN\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.VertexAndFragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"TBN\", this, NodeMaterialConnectionPointDirection.Input, TBNBlock, \"TBNBlock\")\r\n        );\r\n        this.registerInput(\"world\", NodeMaterialBlockConnectionPointTypes.Matrix, true);\r\n\r\n        // Fragment\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        this.registerOutput(\"uvOffset\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"PerturbNormalBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the world tangent input component\r\n     */\r\n    public get worldTangent(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the uv input component\r\n     */\r\n    public get uv(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the normal map color input component\r\n     */\r\n    public get normalMapColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the strength input component\r\n     */\r\n    public get strength(): NodeMaterialConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the view direction input component\r\n     */\r\n    public get viewDirection(): NodeMaterialConnectionPoint {\r\n        return this._inputs[6];\r\n    }\r\n\r\n    /**\r\n     * Gets the parallax scale input component\r\n     */\r\n    public get parallaxScale(): NodeMaterialConnectionPoint {\r\n        return this._inputs[7];\r\n    }\r\n\r\n    /**\r\n     * Gets the parallax height input component\r\n     */\r\n    public get parallaxHeight(): NodeMaterialConnectionPoint {\r\n        return this._inputs[8];\r\n    }\r\n\r\n    /**\r\n     * Gets the TBN input component\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get TBN(): NodeMaterialConnectionPoint {\r\n        return this._inputs[9];\r\n    }\r\n\r\n    /**\r\n     * Gets the World input component\r\n     */\r\n    public get world(): NodeMaterialConnectionPoint {\r\n        return this._inputs[10];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the uv offset output component\r\n     */\r\n    public get uvOffset(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    public prepareDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        const normalSamplerName = (this.normalMapColor.connectedPoint!._ownerBlock as TextureBlock).samplerName;\r\n        const useParallax = this.viewDirection.isConnected && ((this.useParallaxOcclusion && normalSamplerName) || (!this.useParallaxOcclusion && this.parallaxHeight.isConnected));\r\n\r\n        defines.setValue(\"BUMP\", true);\r\n        defines.setValue(\"PARALLAX\", useParallax, true);\r\n        defines.setValue(\"PARALLAX_RHS\", nodeMaterial.getScene().useRightHandedSystem, true);\r\n        defines.setValue(\"PARALLAXOCCLUSION\", this.useParallaxOcclusion, true);\r\n        defines.setValue(\"OBJECTSPACE_NORMALMAP\", this.useObjectSpaceNormalMap, true);\r\n    }\r\n\r\n    public bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh) {\r\n        if (nodeMaterial.getScene()._mirroredCameraPosition) {\r\n            effect.setFloat2(this._tangentSpaceParameterName, this.invertX ? 1.0 : -1.0, this.invertY ? 1.0 : -1.0);\r\n        } else {\r\n            effect.setFloat2(this._tangentSpaceParameterName, this.invertX ? -1.0 : 1.0, this.invertY ? -1.0 : 1.0);\r\n        }\r\n\r\n        if (mesh) {\r\n            effect.setFloat(this._tangentCorrectionFactorName, mesh.getWorldMatrix().determinant() < 0 ? -1 : 1);\r\n\r\n            if (this.useObjectSpaceNormalMap && !this.world.isConnected) {\r\n                // World default to the mesh world matrix\r\n                effect.setMatrix(this._worldMatrixName, mesh.getWorldMatrix());\r\n            }\r\n        }\r\n    }\r\n\r\n    public autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.uv.isConnected) {\r\n            let uvInput = material.getInputBlockByPredicate((b) => b.isAttribute && b.name === \"uv\" && additionalFilteringInfo(b));\r\n\r\n            if (!uvInput) {\r\n                uvInput = new InputBlock(\"uv\");\r\n                uvInput.setAsAttribute();\r\n            }\r\n            uvInput.output.connectTo(this.uv);\r\n        }\r\n\r\n        if (!this.strength.isConnected) {\r\n            const strengthInput = new InputBlock(\"strength\");\r\n            strengthInput.value = 1.0;\r\n            strengthInput.output.connectTo(this.strength);\r\n        }\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const comments = `//${this.name}`;\r\n        const uv = this.uv;\r\n        const worldPosition = this.worldPosition;\r\n        const worldNormal = this.worldNormal;\r\n        const worldTangent = this.worldTangent;\r\n\r\n        state.sharedData.blocksWithDefines.push(this);\r\n        state.sharedData.bindableBlocks.push(this);\r\n\r\n        this._tangentSpaceParameterName = state._getFreeDefineName(\"tangentSpaceParameter\");\r\n\r\n        state._emitUniformFromString(this._tangentSpaceParameterName, \"vec2\");\r\n\r\n        this._tangentCorrectionFactorName = state._getFreeDefineName(\"tangentCorrectionFactor\");\r\n\r\n        state._emitUniformFromString(this._tangentCorrectionFactorName, \"float\");\r\n\r\n        this._worldMatrixName = state._getFreeDefineName(\"perturbNormalWorldMatrix\");\r\n\r\n        state._emitUniformFromString(this._worldMatrixName, \"mat4\");\r\n\r\n        let normalSamplerName = null;\r\n        if (this.normalMapColor.connectedPoint) {\r\n            normalSamplerName = (this.normalMapColor.connectedPoint!._ownerBlock as TextureBlock).samplerName;\r\n        }\r\n        const useParallax = this.viewDirection.isConnected && ((this.useParallaxOcclusion && normalSamplerName) || (!this.useParallaxOcclusion && this.parallaxHeight.isConnected));\r\n\r\n        const replaceForParallaxInfos = !this.parallaxScale.isConnectedToInputBlock\r\n            ? \"0.05\"\r\n            : this.parallaxScale.connectInputBlock!.isConstant\r\n              ? state._emitFloat(this.parallaxScale.connectInputBlock!.value)\r\n              : this.parallaxScale.associatedVariableName;\r\n\r\n        const replaceForBumpInfos =\r\n            this.strength.isConnectedToInputBlock && this.strength.connectInputBlock!.isConstant\r\n                ? `\\n#if !defined(NORMALXYSCALE)\\n1.0/\\n#endif\\n${state._emitFloat(this.strength.connectInputBlock!.value)}`\r\n                : `\\n#if !defined(NORMALXYSCALE)\\n1.0/\\n#endif\\n${this.strength.associatedVariableName}`;\r\n\r\n        state._emitExtension(\"derivatives\", \"#extension GL_OES_standard_derivatives : enable\");\r\n\r\n        const tangentReplaceString = { search: /defined\\(TANGENT\\)/g, replace: worldTangent.isConnected ? \"defined(TANGENT)\" : \"defined(IGNORE)\" };\r\n        const tbnVarying = { search: /varying mat3 vTBN;/g, replace: \"\" };\r\n        const normalMatrixReplaceString = { search: /uniform mat4 normalMatrix;/g, replace: \"\" };\r\n\r\n        const TBN = this.TBN;\r\n        if (TBN.isConnected) {\r\n            state.compilationString += `\r\n            #ifdef TBNBLOCK\r\n            mat3 vTBN = ${TBN.associatedVariableName};\r\n            #endif\r\n            `;\r\n        } else if (worldTangent.isConnected) {\r\n            state.compilationString += `vec3 tbnNormal = normalize(${worldNormal.associatedVariableName}.xyz);\\n`;\r\n            state.compilationString += `vec3 tbnTangent = normalize(${worldTangent.associatedVariableName}.xyz);\\n`;\r\n            state.compilationString += `vec3 tbnBitangent = cross(tbnNormal, tbnTangent) * ${this._tangentCorrectionFactorName};\\n`;\r\n            state.compilationString += `mat3 vTBN = mat3(tbnTangent, tbnBitangent, tbnNormal);\\n`;\r\n        }\r\n\r\n        state._emitFunctionFromInclude(\"bumpFragmentMainFunctions\", comments, {\r\n            replaceStrings: [tangentReplaceString, tbnVarying, normalMatrixReplaceString],\r\n        });\r\n\r\n        state._emitFunctionFromInclude(\"bumpFragmentFunctions\", comments, {\r\n            replaceStrings: [\r\n                { search: /#include<samplerFragmentDeclaration>\\(_DEFINENAME_,BUMP,_VARYINGNAME_,Bump,_SAMPLERNAME_,bump\\)/g, replace: \"\" },\r\n                { search: /uniform sampler2D bumpSampler;/g, replace: \"\" },\r\n                {\r\n                    search: /vec2 parallaxOcclusion\\(vec3 vViewDirCoT,vec3 vNormalCoT,vec2 texCoord,float parallaxScale\\)/g,\r\n                    replace: \"#define inline\\nvec2 parallaxOcclusion(vec3 vViewDirCoT, vec3 vNormalCoT, vec2 texCoord, float parallaxScale, sampler2D bumpSampler)\",\r\n                },\r\n                { search: /vec2 parallaxOffset\\(vec3 viewDir,float heightScale\\)/g, replace: \"vec2 parallaxOffset(vec3 viewDir, float heightScale, float height_)\" },\r\n                { search: /texture2D\\(bumpSampler,vBumpUV\\)\\.w/g, replace: \"height_\" },\r\n            ],\r\n        });\r\n\r\n        const uvForPerturbNormal =\r\n            !useParallax || !normalSamplerName ? this.normalMapColor.associatedVariableName : `texture2D(${normalSamplerName}, ${uv.associatedVariableName} + uvOffset).xyz`;\r\n\r\n        state.compilationString += this._declareOutput(this.output, state) + \" = vec4(0.);\\n\";\r\n        state.compilationString += state._emitCodeFromInclude(\"bumpFragment\", comments, {\r\n            replaceStrings: [\r\n                { search: /texture2D\\(bumpSampler,vBumpUV\\)/g, replace: `${uvForPerturbNormal}` },\r\n                {\r\n                    search: /#define CUSTOM_FRAGMENT_BUMP_FRAGMENT/g,\r\n                    replace: `mat4 normalMatrix = toNormalMatrix(${this.world.isConnected ? this.world.associatedVariableName : this._worldMatrixName});`,\r\n                },\r\n                { search: /perturbNormal\\(TBN,texture2D\\(bumpSampler,vBumpUV\\+uvOffset\\).xyz,vBumpInfos.y\\)/g, replace: `perturbNormal(TBN, ${uvForPerturbNormal}, vBumpInfos.y)` },\r\n                {\r\n                    search: /parallaxOcclusion\\(invTBN\\*-viewDirectionW,invTBN\\*normalW,vBumpUV,vBumpInfos.z\\)/g,\r\n                    replace: `parallaxOcclusion((invTBN * -viewDirectionW), (invTBN * normalW), vBumpUV, vBumpInfos.z, ${\r\n                        useParallax && this.useParallaxOcclusion ? normalSamplerName : \"bumpSampler\"\r\n                    })`,\r\n                },\r\n                {\r\n                    search: /parallaxOffset\\(invTBN\\*viewDirectionW,vBumpInfos\\.z\\)/g,\r\n                    replace: `parallaxOffset(invTBN * viewDirectionW, vBumpInfos.z, ${useParallax ? this.parallaxHeight.associatedVariableName : \"0.\"})`,\r\n                },\r\n                { search: /vTangentSpaceParams/g, replace: this._tangentSpaceParameterName },\r\n                { search: /vBumpInfos.y/g, replace: replaceForBumpInfos },\r\n                { search: /vBumpInfos.z/g, replace: replaceForParallaxInfos },\r\n                { search: /vBumpUV/g, replace: uv.associatedVariableName },\r\n                { search: /vPositionW/g, replace: worldPosition.associatedVariableName + \".xyz\" },\r\n                { search: /normalW=/g, replace: this.output.associatedVariableName + \".xyz = \" },\r\n                { search: /mat3\\(normalMatrix\\)\\*normalW/g, replace: \"mat3(normalMatrix) * \" + this.output.associatedVariableName + \".xyz\" },\r\n                { search: /normalW/g, replace: worldNormal.associatedVariableName + \".xyz\" },\r\n                { search: /viewDirectionW/g, replace: useParallax ? this.viewDirection.associatedVariableName : \"vec3(0.)\" },\r\n                tangentReplaceString,\r\n            ],\r\n        });\r\n\r\n        return this;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode() + `${this._codeVariableName}.invertX = ${this.invertX};\\n`;\r\n\r\n        codeString += `${this._codeVariableName}.invertY = ${this.invertY};\\n`;\r\n        codeString += `${this._codeVariableName}.useParallaxOcclusion = ${this.useParallaxOcclusion};\\n`;\r\n        codeString += `${this._codeVariableName}.useObjectSpaceNormalMap = ${this.useObjectSpaceNormalMap};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.invertX = this.invertX;\r\n        serializationObject.invertY = this.invertY;\r\n        serializationObject.useParallaxOcclusion = this.useParallaxOcclusion;\r\n        serializationObject.useObjectSpaceNormalMap = this.useObjectSpaceNormalMap;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.invertX = serializationObject.invertX;\r\n        this.invertY = serializationObject.invertY;\r\n        this.useParallaxOcclusion = !!serializationObject.useParallaxOcclusion;\r\n        this.useObjectSpaceNormalMap = !!serializationObject.useObjectSpaceNormalMap;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PerturbNormalBlock\", PerturbNormalBlock);\r\n"]}