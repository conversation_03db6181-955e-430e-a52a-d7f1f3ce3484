{"version": 3, "file": "physicsEngineComponent.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Physics/v2/physicsEngineComponent.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAI3D,OAAO,iCAAiC,CAAC;AAiCzC,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,aAAa,EAAE;IAC1D,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IACD,GAAG,EAAE,UAA+B,KAA4B;QAC5D,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE;YAC7B,OAAO;SACV;QACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACjE;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC7D,UAAU;gBACV,IAAI,IAAI,CAAC,WAAW,EAAE;oBAClB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAC,iBAAiB,CAAC,CAAC;oBAC5C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;iBAC3B;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH;;;GAGG;AACH,aAAa,CAAC,SAAS,CAAC,cAAc,GAAG;IACrC,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,aAAa,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,KAAc,EAAE,YAAqB;IAClF,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;KACxD;IACD,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;IACnD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Vector3 } from \"../../Maths/math.vector\";\r\nimport { TransformNode } from \"../../Meshes/transformNode\";\r\nimport type { Node } from \"../../node\";\r\nimport type { PhysicsBody } from \"./physicsBody\";\r\n\r\nimport \"../joinedPhysicsEngineComponent\";\r\n\r\ndeclare module \"../../Meshes/transformNode\" {\r\n    /**\r\n     *\r\n     */\r\n    /** @internal */\r\n    export interface TransformNode {\r\n        /** @internal */\r\n        _physicsBody: Nullable<PhysicsBody>;\r\n\r\n        /**\r\n         * @see\r\n         */\r\n        physicsBody: Nullable<PhysicsBody>;\r\n\r\n        /**\r\n         *\r\n         */\r\n        getPhysicsBody(): Nullable<PhysicsBody>;\r\n\r\n        /** Apply a physic impulse to the mesh\r\n         * @param force defines the force to apply\r\n         * @param contactPoint defines where to apply the force\r\n         * @returns the current mesh\r\n         */\r\n        applyImpulse(force: Vector3, contactPoint: Vector3): TransformNode;\r\n\r\n        /** @internal */\r\n        _disposePhysicsObserver: Nullable<Observer<Node>>;\r\n    }\r\n}\r\n\r\nObject.defineProperty(TransformNode.prototype, \"physicsBody\", {\r\n    get: function (this: TransformNode) {\r\n        return this._physicsBody;\r\n    },\r\n    set: function (this: TransformNode, value: Nullable<PhysicsBody>) {\r\n        if (this._physicsBody === value) {\r\n            return;\r\n        }\r\n        if (this._disposePhysicsObserver) {\r\n            this.onDisposeObservable.remove(this._disposePhysicsObserver);\r\n        }\r\n\r\n        this._physicsBody = value;\r\n\r\n        if (value) {\r\n            this._disposePhysicsObserver = this.onDisposeObservable.add(() => {\r\n                // Physics\r\n                if (this.physicsBody) {\r\n                    this.physicsBody.dispose(/*!doNotRecurse*/);\r\n                    this.physicsBody = null;\r\n                }\r\n            });\r\n        }\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\n/**\r\n * Gets the current physics body\r\n * @returns a physics body or null\r\n */\r\nTransformNode.prototype.getPhysicsBody = function (): Nullable<PhysicsBody> {\r\n    return this.physicsBody;\r\n};\r\n\r\n/**\r\n * Apply a physic impulse to the mesh\r\n * @param force defines the force to apply\r\n * @param contactPoint defines where to apply the force\r\n * @returns the current mesh\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nTransformNode.prototype.applyImpulse = function (force: Vector3, contactPoint: Vector3): TransformNode {\r\n    if (!this.physicsBody) {\r\n        throw new Error(\"No Physics Body for TransformNode\");\r\n    }\r\n    this.physicsBody.applyImpulse(force, contactPoint);\r\n    return this;\r\n};\r\n"]}