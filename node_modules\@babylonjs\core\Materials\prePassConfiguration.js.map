{"version": 3, "file": "prePassConfiguration.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Materials/prePassConfiguration.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEjD;;GAEG;AACH,MAAM,OAAO,oBAAoB;IAAjC;QACI;;;WAGG;QACI,0BAAqB,GAAgC,EAAE,CAAC;QAW/D;;;WAGG;QACI,kBAAa,GAAsC,EAAE,CAAC;IA8DjE,CAAC;IA1DG;;;OAGG;IACI,MAAM,CAAC,WAAW,CAAC,QAAkB;QACxC,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,wBAAwB,EAAE,gBAAgB,CAAC,CAAC;IAC/E,CAAC;IAED;;;OAGG;IACH,6DAA6D;IACtD,MAAM,CAAC,WAAW,CAAC,QAAkB;QACxC,OAAO;IACX,CAAC;IAED;;;;;;;OAOG;IACH,6DAA6D;IACtD,cAAc,CAAC,MAAc,EAAE,KAAY,EAAE,IAAU,EAAE,KAAa,EAAE,QAAiB;QAC5F,IAAI,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,eAAe,CAAC,OAAO,IAAI,KAAK,CAAC,eAAe,CAAC,kBAAkB,EAAE;YACpG,IAAI,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC,EAAE;gBAChF,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;oBAC5C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;iBAC7D;gBAED,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;oBAC9B,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC,KAAK,EAAE,CAAC;oBACjE,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC,KAAK,EAAE,CAAC;iBACnE;gBAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBAEjC,IAAI,IAAI,CAAC,qBAAqB,CAAC,UAAU,KAAK,KAAK,CAAC,kBAAkB,EAAE,CAAC,UAAU,EAAE;oBACjF,oEAAoE;oBACpE,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,OAAO,CAAC;oBACzC,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;oBACjE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;iBACnE;qBAAM,IAAI,IAAI,CAAC,kBAAkB,KAAK,MAAM,CAAC,OAAO,EAAE;oBACnD,2IAA2I;oBAC3I,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,OAAO,CAAC;oBACzC,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;iBACpE;gBAED,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC7E,MAAM,CAAC,SAAS,CAAC,wBAAwB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAExE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;aAC7D;SACJ;IACL,CAAC;CACJ", "sourcesContent": ["import type { Matrix } from \"../Maths/math.vector\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Constants } from \"../Engines/constants\";\r\n\r\n/**\r\n * Configuration needed for prepass-capable materials\r\n */\r\nexport class PrePassConfiguration {\r\n    /**\r\n     * Previous world matrices of meshes carrying this material\r\n     * Used for computing velocity\r\n     */\r\n    public previousWorldMatrices: { [index: number]: Matrix } = {};\r\n    /**\r\n     * Previous view project matrix\r\n     * Used for computing velocity\r\n     */\r\n    public previousViewProjection: Matrix;\r\n    /**\r\n     * Current view projection matrix\r\n     * Used for computing velocity\r\n     */\r\n    public currentViewProjection: Matrix;\r\n    /**\r\n     * Previous bones of meshes carrying this material\r\n     * Used for computing velocity\r\n     */\r\n    public previousBones: { [index: number]: Float32Array } = {};\r\n\r\n    private _lastUpdateFrameId: number;\r\n\r\n    /**\r\n     * Add the required uniforms to the current list.\r\n     * @param uniforms defines the current uniform list.\r\n     */\r\n    public static AddUniforms(uniforms: string[]): void {\r\n        uniforms.push(\"previousWorld\", \"previousViewProjection\", \"mPreviousBones\");\r\n    }\r\n\r\n    /**\r\n     * Add the required samplers to the current list.\r\n     * @param samplers defines the current sampler list.\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static AddSamplers(samplers: string[]): void {\r\n        // pass\r\n    }\r\n\r\n    /**\r\n     * Binds the material data.\r\n     * @param effect defines the effect to update\r\n     * @param scene defines the scene the material belongs to.\r\n     * @param mesh The mesh\r\n     * @param world World matrix of this mesh\r\n     * @param isFrozen Is the material frozen\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public bindForSubMesh(effect: Effect, scene: Scene, mesh: Mesh, world: Matrix, isFrozen: boolean): void {\r\n        if (scene.prePassRenderer && scene.prePassRenderer.enabled && scene.prePassRenderer.currentRTisSceneRT) {\r\n            if (scene.prePassRenderer.getIndex(Constants.PREPASS_VELOCITY_TEXTURE_TYPE) !== -1) {\r\n                if (!this.previousWorldMatrices[mesh.uniqueId]) {\r\n                    this.previousWorldMatrices[mesh.uniqueId] = world.clone();\r\n                }\r\n\r\n                if (!this.previousViewProjection) {\r\n                    this.previousViewProjection = scene.getTransformMatrix().clone();\r\n                    this.currentViewProjection = scene.getTransformMatrix().clone();\r\n                }\r\n\r\n                const engine = scene.getEngine();\r\n\r\n                if (this.currentViewProjection.updateFlag !== scene.getTransformMatrix().updateFlag) {\r\n                    // First update of the prepass configuration for this rendering pass\r\n                    this._lastUpdateFrameId = engine.frameId;\r\n                    this.previousViewProjection.copyFrom(this.currentViewProjection);\r\n                    this.currentViewProjection.copyFrom(scene.getTransformMatrix());\r\n                } else if (this._lastUpdateFrameId !== engine.frameId) {\r\n                    // The scene transformation did not change from the previous frame (so no camera motion), we must update previousViewProjection accordingly\r\n                    this._lastUpdateFrameId = engine.frameId;\r\n                    this.previousViewProjection.copyFrom(this.currentViewProjection);\r\n                }\r\n\r\n                effect.setMatrix(\"previousWorld\", this.previousWorldMatrices[mesh.uniqueId]);\r\n                effect.setMatrix(\"previousViewProjection\", this.previousViewProjection);\r\n\r\n                this.previousWorldMatrices[mesh.uniqueId] = world.clone();\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}