{"version": 3, "file": "prePassEffectConfiguration.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Rendering/prePassEffectConfiguration.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { PostProcess } from \"../PostProcesses/postProcess\";\r\n\r\n/**\r\n * Interface for defining prepass effects in the prepass post-process pipeline\r\n */\r\nexport interface PrePassEffectConfiguration {\r\n    /**\r\n     * Name of the effect\r\n     */\r\n    name: string;\r\n    /**\r\n     * Post process to attach for this effect\r\n     */\r\n    postProcess?: PostProcess;\r\n    /**\r\n     * Textures required in the MRT\r\n     */\r\n    texturesRequired: number[];\r\n    /**\r\n     * Is the effect enabled\r\n     */\r\n    enabled: boolean;\r\n    /**\r\n     * Does the output of this prepass need to go through imageprocessing\r\n     */\r\n    needsImageProcessing?: boolean;\r\n    /**\r\n     * Disposes the effect configuration\r\n     */\r\n    dispose?: () => void;\r\n    /**\r\n     * Creates the associated post process\r\n     */\r\n    createPostProcess?: () => PostProcess;\r\n}\r\n"]}