{"version": 3, "file": "pointsCloudSystem.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Particles/pointsCloudSystem.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AACrF,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AACvD,OAAO,EAAE,GAAG,EAAE,MAAM,gBAAgB,CAAC;AAErC,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,WAAW,EAAE,MAAM,qCAAqC,CAAC;AAClE,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAG9C,kCAAkC;AAClC,MAAM,CAAN,IAAY,UASX;AATD,WAAY,UAAU;IAClB,kBAAkB;IAClB,6CAAS,CAAA;IACT,eAAe;IACf,uCAAM,CAAA;IACN,mBAAmB;IACnB,+CAAU,CAAA;IACV,mBAAmB;IACnB,+CAAU,CAAA;AACd,CAAC,EATW,UAAU,KAAV,UAAU,QASrB;AAED;;;;;;;;GAQG;AACH,MAAM,OAAO,iBAAiB;IAsD1B;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;;;OAQG;IACH,YAAY,IAAY,EAAE,SAAiB,EAAE,KAAY,EAAE,OAAiC;QAnF5F;;;WAGG;QACI,cAAS,GAAiB,IAAI,KAAK,EAAc,CAAC;QACzD;;WAEG;QACI,gBAAW,GAAW,CAAC,CAAC;QAC/B;;WAEG;QACI,YAAO,GAAW,CAAC,CAAC;QAS3B;;;WAGG;QACI,SAAI,GAAQ,EAAE,CAAC;QAOd,cAAS,GAAwB,EAAE,CAAC;QACpC,eAAU,GAAa,IAAI,KAAK,EAAU,CAAC;QAC3C,aAAQ,GAAa,IAAI,KAAK,EAAU,CAAC;QACzC,aAAQ,GAAa,IAAI,KAAK,EAAU,CAAC;QACzC,YAAO,GAAa,IAAI,KAAK,EAAU,CAAC;QACxC,SAAI,GAAa,IAAI,KAAK,EAAU,CAAC;QAKrC,eAAU,GAAY,IAAI,CAAC;QAC3B,2BAAsB,GAAG,KAAK,CAAC;QAC/B,mBAAc,GAAY,KAAK,CAAC;QAChC,YAAO,GAAa,IAAI,KAAK,EAAU,CAAC,CAAC,2CAA2C;QACpF,kBAAa,GAAW,CAAC,CAAC;QAC1B,0BAAqB,GAAY,IAAI,CAAC;QACtC,4BAAuB,GAAY,IAAI,CAAC;QACxC,6BAAwB,GAAY,IAAI,CAAC;QACzC,wBAAmB,GAAY,KAAK,CAAC;QACrC,aAAQ,GAAY,KAAK,CAAC;QAiC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QACpD,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;YAC5C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC;SACvC;aAAM;YACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SAC1B;IACL,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,QAAmB;QACrC,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,QAAmB;QAClC,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;YACxB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SACrB;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtD,IAAI,CAAC,MAAM,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEhD,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;QACpC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC;QAE7D,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;SACpD;QACD,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,6CAA6C;QACzD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,EAAE,GAAG,CAAC,CAAC;YACP,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;SAC1D;QACD,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9C,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,cAAc;QACR,IAAI,CAAC,UAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,IAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,OAAQ,GAAG,IAAI,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;SAC7B;QAED,IAAI,GAAG,GAAG,QAAQ,CAAC;QAEnB,IAAI,CAAC,GAAG,EAAE;YACN,GAAG,GAAG,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7C,GAAI,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAC5C,GAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,GAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,GAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;SAClD;QACD,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;QAEpB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,oDAAoD;IAC5C,YAAY,CAAC,GAAW,EAAE,KAAkB,EAAE,OAAe,EAAE,UAAkB;QACrF,MAAM,EAAE,GAAG,IAAI,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACjE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxB,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,iBAAiB,CAAC,QAAoB;QAC1C,QAAQ,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QAC7E,QAAQ,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5C,CAAC;IAEO,wBAAwB,CAAC,WAAwB,EAAE,CAAS,EAAE,CAAS,EAAE,KAAa;QAC1F,MAAM,SAAS,GAAe,WAAW,CAAC,eAAe,CAAC;QAC1D,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACtC,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;QAC9D,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QACnC,MAAM,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,aAAa,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC;QAC5C,MAAM,YAAY,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;QAC1C,MAAM,aAAa,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC;QAC5C,OAAO,IAAI,MAAM,CAAC,WAAW,GAAG,GAAG,EAAE,aAAa,GAAG,GAAG,EAAE,YAAY,GAAG,GAAG,EAAE,aAAa,CAAC,CAAC;IACjG,CAAC;IAEO,mBAAmB,CACvB,IAAU,EACV,WAAwB,EACxB,QAAiB,EACjB,gBAA0B,EAC1B,UAAoB,EACpB,KAAc,EACd,KAAc,EACd,UAAmB;QAEnB,UAAU,GAAG,UAAU,IAAI,CAAC,CAAC;QAE7B,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACzC,MAAM,QAAQ,GAAG,CAAC,GAAG,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC;QAErD,IAAI,OAAO,GAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAC1E,MAAM,OAAO,GAAiB,IAAI,CAAC,UAAU,EAAE,CAAC;QAChD,MAAM,MAAM,GAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1G,MAAM,OAAO,GAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAEzE,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,MAAM,UAAU,GAAW,IAAI,CAAC,cAAc,EAAE,CAAC;QACjD,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE;YAC1B,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACzC,OAAO,CAAC,mCAAmC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;gBACvH,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBACzB,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBAC7B,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;aAChC;SACJ;QAED,IAAI,SAAS,GAAW,CAAC,CAAC;QAE1B,IAAI,GAAG,GAAW,CAAC,CAAC;QACpB,IAAI,GAAG,GAAW,CAAC,CAAC;QACpB,IAAI,GAAG,GAAW,CAAC,CAAC;QACpB,IAAI,GAAG,GAAW,CAAC,CAAC;QACpB,IAAI,GAAG,GAAW,CAAC,CAAC;QACpB,IAAI,GAAG,GAAW,CAAC,CAAC;QACpB,IAAI,GAAG,GAAW,CAAC,CAAC;QACpB,IAAI,GAAG,GAAW,CAAC,CAAC;QACpB,IAAI,GAAG,GAAW,CAAC,CAAC;QACpB,IAAI,GAAG,GAAW,CAAC,CAAC;QACpB,IAAI,GAAG,GAAW,CAAC,CAAC;QACpB,IAAI,GAAG,GAAW,CAAC,CAAC;QACpB,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC5B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE5B,IAAI,IAAI,GAAW,CAAC,CAAC;QACrB,IAAI,IAAI,GAAW,CAAC,CAAC;QACrB,IAAI,IAAI,GAAW,CAAC,CAAC;QACrB,IAAI,IAAI,GAAW,CAAC,CAAC;QACrB,IAAI,IAAI,GAAW,CAAC,CAAC;QACrB,IAAI,IAAI,GAAW,CAAC,CAAC;QACrB,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC3B,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC3B,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC3B,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC7B,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE7B,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC5B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC5B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC5B,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE/B,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAI,EAAE,GAAW,CAAC,CAAC;QACnB,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1B,IAAI,UAAmB,CAAC;QACxB,IAAI,OAAgB,CAAC;QACrB,IAAI,QAAQ,GAAY,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEhD,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC1B,IAAI,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC5B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,aAAa,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEnC,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAI,QAAqB,CAAC;QAC1B,IAAI,SAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE/B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE;YACrD,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;YACzB,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YACvB,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC3B,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC3B,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YACvB,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC3B,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC3B,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YACvB,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC3B,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAC3B,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACrC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAErC,IAAI,MAAM,EAAE;gBACR,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBACvB,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC3B,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBACvB,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC3B,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBACvB,IAAI,GAAG,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC3B,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACpB,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACpB,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACpB,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC9B,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;aACjC;YAED,IAAI,OAAO,IAAI,gBAAgB,EAAE;gBAC7B,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBACzB,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC7B,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC7B,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC7B,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBACzB,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC7B,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC7B,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC7B,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBACzB,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC7B,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC7B,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC7B,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBACrC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBACrC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBACrC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAClC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;aACrC;YAED,IAAI,KAAa,CAAC;YAClB,IAAI,MAAc,CAAC;YACnB,IAAI,MAAc,CAAC;YACnB,IAAI,MAAc,CAAC;YACnB,IAAI,CAAS,CAAC;YACd,IAAI,CAAS,CAAC;YACd,IAAI,CAAS,CAAC;YACd,IAAI,MAAc,CAAC;YACnB,MAAM,WAAW,GAAW,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAChD,MAAM,SAAS,GAAW,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9C,IAAI,WAAmB,CAAC;YACxB,IAAI,QAAoB,CAAC;YAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE;gBACvD,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;gBAClC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;gBACzE,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBACrC,2CAA2C;gBAC3C,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC5C,EAAE,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC9B,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC;gBACxE,IAAI,QAAQ,EAAE;oBACV,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxD,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,CAAC;oBAChC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBACnC,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;oBAC3C,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC/E,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;oBAC7C,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAElF,GAAG,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;oBACtD,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;oBAC1B,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC;oBACtB,QAAQ,GAAG,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;oBACpC,IAAI,QAAQ,CAAC,GAAG,EAAE;wBACd,QAAQ,GAAG,QAAQ,CAAC,WAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,CAAC;wBAC/D,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC;wBAC1C,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;qBAC/C;iBACJ;gBACD,QAAQ,CAAC,QAAQ,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;gBACvC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACpF,IAAI,gBAAgB,KAAK,SAAS,EAAE;oBAChC,IAAI,MAAM,EAAE;wBACR,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC;wBACnE,IAAI,gBAAgB,EAAE;4BAClB,qCAAqC;4BACrC,IAAI,UAAU,IAAI,WAAW,CAAC,eAAe,KAAK,IAAI,EAAE;gCACpD,KAAK,GAAG,WAAW,CAAC,cAAc,CAAC;gCACnC,MAAM,GAAG,WAAW,CAAC,eAAe,CAAC;gCACrC,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;gCAC/H,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC;gCAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;6BACjF;iCAAM;gCACH,IAAI,OAAO,EAAE;oCACT,yCAAyC;oCACzC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC;oCACzE,QAAQ,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;oCAC5E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;iCACrE;qCAAM;oCACH,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;oCACpE,QAAQ,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;oCAC5E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;iCACrE;6BACJ;yBACJ;6BAAM;4BACH,oCAAoC;4BACpC,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;4BAC9B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;yBAChD;qBACJ;iBACJ;qBAAM;oBACH,IAAI,KAAK,EAAE;wBACP,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;wBAC3C,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;wBAC3C,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;wBAC3C,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;wBAC7B,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;wBACb,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC;wBACtB,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC;wBACtB,IAAI,CAAC,GAAG,CAAC,EAAE;4BACP,CAAC,GAAG,CAAC,CAAC;yBACT;wBACD,IAAI,CAAC,GAAG,CAAC,EAAE;4BACP,CAAC,GAAG,CAAC,CAAC;yBACT;wBACD,IAAI,CAAC,GAAG,CAAC,EAAE;4BACP,CAAC,GAAG,CAAC,CAAC;yBACT;wBACD,IAAI,CAAC,GAAG,CAAC,EAAE;4BACP,CAAC,GAAG,CAAC,CAAC;yBACT;wBACD,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;wBACzC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;qBAC1D;yBAAM;wBACH,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;qBACvE;oBACD,QAAQ,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAC5E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;iBACrE;aACJ;SACJ;IACL,CAAC;IAED,mEAAmE;IACnE,mDAAmD;IAC3C,iBAAiB,CAAC,IAAU,EAAE,WAAwB,EAAE,QAAiB;QAC7E,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YACxB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC,CAAC;YAC5C,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO;SACV;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC1B,MAAM,WAAW,GAAkB,GAAG,CAAC,iBAAiB,EAAE,CAAC;QAC3D,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,wBAAwB,CAAC,CAAC;YAClD,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO;SACV;QAED,MAAM,KAAK,GAAS,IAAI,CAAC,KAAK,EAAE,CAAC;QACjC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CACf,IAAI,OAAO,CAAC,CAAC,OAA0B,EAAE,EAAE;YACvC,WAAW,CAAC,YAAY,CAAC,WAAW,EAAE,GAAG,EAAE;gBACvC,IAAI,CAAC,GAAG,WAAW,CAAC,UAAU,CAAC;gBAC/B,IAAI,CAAC,GAAG,CAAC,EAAE;oBACP,CAAC,GAAG,CAAC,CAAC;iBACT;gBACD,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC5B,CAAC,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;iBAC9B;gBACD,MAAM,QAAQ,GAAG,GAAG,EAAE;oBAClB,WAAW,CAAC,cAAc,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;oBAC5D,WAAW,CAAC,eAAe,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;oBAC9D,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC;oBAC1H,KAAK,CAAC,OAAO,EAAE,CAAC;oBAChB,OAAO,EAAE,CAAC;gBACd,CAAC,CAAC;gBACF,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC;gBACnC,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;gBAChD,IAAI,CAAC,WAAW,EAAE;oBACd,QAAQ,EAAE,CAAC;iBACd;qBAAM;oBACH,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;wBACtB,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC;wBACnC,QAAQ,EAAE,CAAC;oBACf,CAAC,CAAC,CAAC;iBACN;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CACL,CAAC;IACN,CAAC;IAED,sEAAsE;IAC9D,iBAAiB,CAAC,QAAgB,EAAE,SAAqB,EAAE,OAAqB;QACpF,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC5B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE9B,IAAI,IAAY,CAAC;QACjB,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,IAAI,WAAW,GAAW,CAAC,CAAC;QAE5B,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QAEpC,cAAc;QACd,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,EAAE,KAAK,EAAE,EAAE;YAC3C,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;YACzB,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YACzB,GAAG,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YACzB,GAAG,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YACzB,GAAG,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAC3B,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACrC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACrC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;YACvC,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC7B,WAAW,IAAI,IAAI,CAAC;YACpB,eAAe,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC;SACxC;QAED,MAAM,OAAO,GAAa,IAAI,KAAK,CAAS,QAAQ,CAAC,CAAC;QACtD,IAAI,eAAe,GAAG,QAAQ,CAAC;QAC/B,KAAK,IAAI,KAAK,GAAG,QAAQ,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE;YAC/C,MAAM,cAAc,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;YAC9C,IAAI,cAAc,KAAK,CAAC,EAAE;gBACtB,mDAAmD;gBACnD,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACtB;iBAAM;gBACH,MAAM,IAAI,GAAG,cAAc,GAAG,eAAe,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACzD,MAAM,uBAAuB,GAAG,CAAC,IAAI,GAAG,cAAc,CAAC,GAAG,eAAe,CAAC;gBAC1E,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;gBACpD,MAAM,QAAQ,GAAG,uBAAuB,GAAG,OAAO,CAAC;gBACnD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC;gBACpD,MAAM,WAAW,GAAG,OAAO,GAAG,UAAU,CAAC;gBACzC,OAAO,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC;gBAC7B,eAAe,IAAI,WAAW,CAAC;aAClC;SACJ;QACD,OAAO,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;QAE7B,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,EAAU,EAAE,gBAAqB,IAAI,CAAC,iBAAiB;QACpE,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;QACvE,IAAI,EAAc,CAAC;QAEnB,YAAY;QACZ,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;YACzB,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;YAChE,IAAI,WAAW,IAAI,WAAW,CAAC,iBAAiB,EAAE;gBAC9C,WAAW,CAAC,iBAAiB,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;aAC7C;YACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAClE,IAAI,EAAE,CAAC,KAAK,EAAE;gBACV,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACrE;YACD,IAAI,EAAE,CAAC,EAAE,EAAE;gBACP,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aACpC;YACD,GAAG,EAAE,CAAC;SACT;QACD,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;;;;;;OAQG;IACI,gBAAgB,CAAC,IAAU,EAAE,EAAU,EAAE,SAAkB,EAAE,KAAuB,EAAE,KAAc;QACvG,IAAI,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;QACxD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,EAAE;YAC9C,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC;SAC/B;QAED,MAAM,OAAO,GAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAC5E,MAAM,OAAO,GAAiB,IAAI,CAAC,UAAU,EAAE,CAAC;QAEhD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACtC,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAE9D,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QACzE,IAAI,OAAO,KAAK,UAAU,CAAC,KAAK,EAAE;YAC9B,WAAW,CAAC,UAAU,GAAW,KAAK,CAAC,CAAC,CAAS,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9D;aAAM;YACH,KAAK,GAAW,KAAK,CAAC,CAAC,CAAS,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAClE;QACD,QAAQ,OAAO,EAAE;YACb,KAAK,UAAU,CAAC,KAAK;gBACjB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;gBACjD,MAAM;YACV,KAAK,UAAU,CAAC,EAAE;gBACd,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBACjE,MAAM;YACV,KAAK,UAAU,CAAC,MAAM;gBAClB,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;gBACnD,MAAM;YACV,KAAK,UAAU,CAAC,MAAM;gBAClB,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAU,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC/F,MAAM;SACb;QACD,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,OAAO,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;;;;;;;OAQG;IACI,eAAe,CAAC,IAAU,EAAE,EAAU,EAAE,SAAkB,EAAE,KAAuB,EAAE,KAAc;QACtG,IAAI,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;QACxD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,EAAE;YAC9C,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC;SAC/B;QAED,MAAM,OAAO,GAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAC5E,MAAM,OAAO,GAAiB,IAAI,CAAC,UAAU,EAAE,CAAC;QAEhD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACtC,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAE9D,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QACzE,IAAI,OAAO,KAAK,UAAU,CAAC,KAAK,EAAE;YAC9B,WAAW,CAAC,UAAU,GAAW,KAAK,CAAC,CAAC,CAAS,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9D;aAAM;YACH,KAAK,GAAW,KAAK,CAAC,CAAC,CAAS,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAClE;QACD,QAAQ,OAAO,EAAE;YACb,KAAK,UAAU,CAAC,KAAK;gBACjB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;gBAChD,MAAM;YACV,KAAK,UAAU,CAAC,EAAE;gBACd,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBAChE,MAAM;YACV,KAAK,UAAU,CAAC,MAAM;gBAClB,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;gBAClD,MAAM;YACV,KAAK,UAAU,CAAC,MAAM;gBAClB,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAU,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC9F,MAAM;SACb;QACD,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,OAAO,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;;;;;;;OAQG;IACI,YAAY,CAAC,QAAgB,CAAC,EAAE,MAAc,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,SAAkB,IAAI;QAC7F,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACpC,OAAO,IAAI,CAAC;SACf;QAED,sBAAsB;QACtB,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;QAE/C,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC;QACvC,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC9D,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC9D,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC9D,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEzD,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAChC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,gCAAgC;QAE7C,IAAI,IAAI,CAAC,IAAI,EAAE,kBAAkB,EAAE;YAC/B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACnC;QAED,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAC3D,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;gBAC3C,0GAA0G;gBAC1G,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC;gBAClD,IAAI,YAAY,EAAE;oBACd,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;oBACvC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;iBAC1C;aACJ;SACJ;QAED,GAAG,GAAG,CAAC,CAAC,CAAC,iBAAiB;QAC1B,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,0BAA0B;QAC1C,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,sBAAsB;QACtC,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,mBAAmB;QAEnC,gBAAgB;QAChB,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE;YAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACnC,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;YACnB,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;YACjB,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;YACjB,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;YAEjB,iEAAiE;YACjE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAE9B,MAAM,sBAAsB,GAAG,QAAQ,CAAC,eAAe,CAAC;YACxD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,QAAQ,CAAC;YAC3C,MAAM,sBAAsB,GAAG,QAAQ,CAAC,eAAe,CAAC;YAExD,IAAI,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;aACzC;YAED,MAAM,iBAAiB,GAAG,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC;YACrD,IAAI,iBAAiB,EAAE;gBACnB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAS,CAAC,CAAC;gBAClD,MAAM,oBAAoB,GAAG,MAAM,CAAC,eAAe,CAAC;gBACpD,MAAM,oBAAoB,GAAG,MAAM,CAAC,eAAe,CAAC;gBAEpD,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBAC5J,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBAC5J,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBAE5J,sBAAsB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,GAAG,QAAQ,CAAC;gBAC7D,sBAAsB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,GAAG,QAAQ,CAAC;gBAC7D,sBAAsB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,GAAG,QAAQ,CAAC;gBAE7D,IAAI,IAAI,CAAC,wBAAwB,EAAE;oBAC/B,MAAM,eAAe,GAAG,SAAS,CAAC,CAAC,CAAC;oBACpC,sBAAsB,CAAC,CAAC,CAAC;wBACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;oBAC/I,sBAAsB,CAAC,CAAC,CAAC;wBACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;oBAC/I,sBAAsB,CAAC,CAAC,CAAC;wBACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;oBAC/I,sBAAsB,CAAC,CAAC,CAAC;wBACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;oBAC/I,sBAAsB,CAAC,CAAC,CAAC;wBACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;oBAC/I,sBAAsB,CAAC,CAAC,CAAC;wBACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;oBAC/I,sBAAsB,CAAC,CAAC,CAAC;wBACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;oBAChJ,sBAAsB,CAAC,CAAC,CAAC;wBACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;oBAChJ,sBAAsB,CAAC,CAAC,CAAC;wBACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;iBACnJ;aACJ;iBAAM;gBACH,sBAAsB,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC7B,sBAAsB,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC7B,sBAAsB,CAAC,CAAC,GAAG,CAAC,CAAC;gBAE7B,IAAI,IAAI,CAAC,wBAAwB,EAAE;oBAC/B,MAAM,eAAe,GAAG,SAAS,CAAC,CAAC,CAAC;oBACpC,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;oBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;oBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;oBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;oBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;oBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;oBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;oBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;oBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,EAAE,CAAC,CAAC;iBACnD;aACJ;YAED,MAAM,oBAAoB,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;YAC7C,IAAI,QAAQ,CAAC,kBAAkB,EAAE;gBAC7B,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aACpC;iBAAM;gBACH,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aACjD;YAED,YAAY;YACZ,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACjC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACtC,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAC/C,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAC/C,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAE/C,IAAI,QAAQ,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC/H,IAAI,QAAQ,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC/H,IAAI,QAAQ,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAE/H,QAAQ,IAAI,oBAAoB,CAAC,CAAC,CAAC;YACnC,QAAQ,IAAI,oBAAoB,CAAC,CAAC,CAAC;YACnC,QAAQ,IAAI,oBAAoB,CAAC,CAAC,CAAC;YAEnC,MAAM,EAAE,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,sBAAsB,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;YACpI,MAAM,EAAE,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,sBAAsB,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;YACxI,MAAM,EAAE,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,sBAAsB,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;YAExI,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,OAAO,CAAC,yBAAyB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC9C,OAAO,CAAC,yBAAyB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;aACjD;YAED,IAAI,IAAI,CAAC,qBAAqB,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAC9C,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;gBAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;gBAChC,QAAQ,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBAC3B,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBAC/B,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;gBAC/B,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;aAClC;YACD,IAAI,IAAI,CAAC,uBAAuB,IAAI,QAAQ,CAAC,EAAE,EAAE;gBAC7C,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC;gBACvB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC1B,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBACrB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;aAC5B;SACJ;QAED,6BAA6B;QAC7B,IAAI,IAAI,EAAE;YACN,IAAI,MAAM,EAAE;gBACR,IAAI,IAAI,CAAC,qBAAqB,EAAE;oBAC5B,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;iBAC3E;gBACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;oBAC9B,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;iBACrE;gBACD,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,YAAY,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACjF;YAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,IAAI,CAAC,eAAe,EAAE;oBACtB,IAAI,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;iBAC3E;qBAAM;oBACH,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;iBAC/D;aACJ;SACJ;QACD,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,oDAAoD;QAC9C,IAAI,CAAC,UAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,QAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,QAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,IAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,OAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,UAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,YAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,MAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,SAAU,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACI,kBAAkB;QACrB,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,IAAI,CAAC,IAAI,EAAE,mBAAmB,EAAE,CAAC;SACpC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,IAAY;QAChC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,OAAO;SACV;QAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3F,CAAC;IAED;;;OAGG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,IAAW,eAAe,CAAC,GAAY;QACnC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,OAAO;SACV;QAED,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,wBAAwB,GAAG,GAAG,CAAC;IAC7C,CAAC;IAED;;;;;OAKG;IACH,IAAW,uBAAuB,CAAC,GAAY;QAC3C,IAAI,CAAC,wBAAwB,GAAG,GAAG,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,oBAAoB,CAAC,GAAY;QACxC,IAAI,CAAC,qBAAqB,GAAG,GAAG,CAAC;IACrC,CAAC;IAED,IAAW,sBAAsB,CAAC,GAAY;QAC1C,IAAI,CAAC,uBAAuB,GAAG,GAAG,CAAC;IACvC,CAAC;IACD;;;;OAIG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IACD;;;;OAIG;IACH,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IACD;;OAEG;IACH,IAAW,kBAAkB,CAAC,GAAY;QACtC,IAAI,CAAC,mBAAmB,GAAG,GAAG,CAAC;IACnC,CAAC;IACD;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED,0EAA0E;IAC1E,0BAA0B;IAC1B,yEAAyE;IAEzE;;;;OAIG;IACI,aAAa,KAAU,CAAC;IAE/B;;;;;;OAMG;IACI,eAAe,CAAC,QAAoB;QACvC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;;;;;OAOG;IACI,cAAc,CAAC,QAAoB;QACtC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;;;;OAMG;IACH,6DAA6D;IACtD,qBAAqB,CAAC,KAAc,EAAE,IAAa,EAAE,MAAgB,IAAS,CAAC;IACtF;;;;;;;OAOG;IACH,6DAA6D;IACtD,oBAAoB,CAAC,KAAc,EAAE,IAAa,EAAE,MAAgB,IAAS,CAAC;CACxF", "sourcesContent": ["import type { IndicesArray, FloatArray } from \"../types\";\r\nimport { Color4, Color3 } from \"../Maths/math\";\r\nimport { Vector2, Vector3, Vector4, TmpVectors, Matrix } from \"../Maths/math.vector\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport { VertexData } from \"../Meshes/mesh.vertexData\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { Scene, IDisposable } from \"../scene\";\r\nimport { CloudPoint, PointsGroup } from \"./cloudPoint\";\r\nimport { Ray } from \"../Culling/ray\";\r\nimport type { PickingInfo } from \"../Collisions/pickingInfo\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport { BaseTexture } from \"./../Materials/Textures/baseTexture\";\r\nimport { Scalar } from \"../Maths/math.scalar\";\r\nimport type { Material } from \"../Materials/material\";\r\n\r\n/** Defines the 4 color options */\r\nexport enum PointColor {\r\n    /** color value */\r\n    Color = 2,\r\n    /** uv value */\r\n    UV = 1,\r\n    /** random value */\r\n    Random = 0,\r\n    /** stated value */\r\n    Stated = 3,\r\n}\r\n\r\n/**\r\n * The PointCloudSystem (PCS) is a single updatable mesh. The points corresponding to the vertices of this big mesh.\r\n * As it is just a mesh, the PointCloudSystem has all the same properties as any other BJS mesh : not more, not less. It can be scaled, rotated, translated, enlighted, textured, moved, etc.\r\n\r\n * The PointCloudSystem is also a particle system, with each point being a particle. It provides some methods to manage the particles.\r\n * However it is behavior agnostic. This means it has no emitter, no particle physics, no particle recycler. You have to implement your own behavior.\r\n *\r\n * Full documentation here : TO BE ENTERED\r\n */\r\nexport class PointsCloudSystem implements IDisposable {\r\n    /**\r\n     *  The PCS array of cloud point objects. Just access each particle as with any classic array.\r\n     *  Example : var p = SPS.particles[i];\r\n     */\r\n    public particles: CloudPoint[] = new Array<CloudPoint>();\r\n    /**\r\n     * The PCS total number of particles. Read only. Use PCS.counter instead if you need to set your own value.\r\n     */\r\n    public nbParticles: number = 0;\r\n    /**\r\n     * This a counter for your own usage. It's not set by any SPS functions.\r\n     */\r\n    public counter: number = 0;\r\n    /**\r\n     * The PCS name. This name is also given to the underlying mesh.\r\n     */\r\n    public name: string;\r\n    /**\r\n     * The PCS mesh. It's a standard BJS Mesh, so all the methods from the Mesh class are available.\r\n     */\r\n    public mesh?: Mesh;\r\n    /**\r\n     * This empty object is intended to store some PCS specific or temporary values in order to lower the Garbage Collector activity.\r\n     * Please read :\r\n     */\r\n    public vars: any = {};\r\n    /**\r\n     * @internal\r\n     */\r\n    public _size: number; //size of each point particle\r\n\r\n    private _scene: Scene;\r\n    private _promises: Array<Promise<any>> = [];\r\n    private _positions: number[] = new Array<number>();\r\n    private _indices: number[] = new Array<number>();\r\n    private _normals: number[] = new Array<number>();\r\n    private _colors: number[] = new Array<number>();\r\n    private _uvs: number[] = new Array<number>();\r\n    private _indices32: IndicesArray; // used as depth sorted array if depth sort enabled, else used as typed indices\r\n    private _positions32: Float32Array; // updated positions for the VBO\r\n    private _colors32: Float32Array;\r\n    private _uvs32: Float32Array;\r\n    private _updatable: boolean = true;\r\n    private _isVisibilityBoxLocked = false;\r\n    private _alwaysVisible: boolean = false;\r\n    private _groups: number[] = new Array<number>(); //start indices for each group of particles\r\n    private _groupCounter: number = 0;\r\n    private _computeParticleColor: boolean = true;\r\n    private _computeParticleTexture: boolean = true;\r\n    private _computeParticleRotation: boolean = true;\r\n    private _computeBoundingBox: boolean = false;\r\n    private _isReady: boolean = false;\r\n\r\n    /**\r\n     * Gets the particle positions computed by the Point Cloud System\r\n     */\r\n    public get positions() {\r\n        return this._positions32;\r\n    }\r\n\r\n    /**\r\n     * Gets the particle colors computed by the Point Cloud System\r\n     */\r\n    public get colors() {\r\n        return this._colors32;\r\n    }\r\n\r\n    /**\r\n     * Gets the particle uvs computed by the Point Cloud System\r\n     */\r\n    public get uvs() {\r\n        return this._uvs32;\r\n    }\r\n\r\n    /**\r\n     * Creates a PCS (Points Cloud System) object\r\n     * @param name (String) is the PCS name, this will be the underlying mesh name\r\n     * @param pointSize (number) is the size for each point. Has no effect on a WebGPU engine.\r\n     * @param scene (Scene) is the scene in which the PCS is added\r\n     * @param options defines the options of the PCS e.g.\r\n     * * updatable (optional boolean, default true) : if the PCS must be updatable or immutable\r\n     * @param options.updatable\r\n     */\r\n    constructor(name: string, pointSize: number, scene: Scene, options?: { updatable?: boolean }) {\r\n        this.name = name;\r\n        this._size = pointSize;\r\n        this._scene = scene || EngineStore.LastCreatedScene;\r\n        if (options && options.updatable !== undefined) {\r\n            this._updatable = options.updatable;\r\n        } else {\r\n            this._updatable = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Builds the PCS underlying mesh. Returns a standard Mesh.\r\n     * If no points were added to the PCS, the returned mesh is just a single point.\r\n     * @param material The material to use to render the mesh. If not provided, will create a default one\r\n     * @returns a promise for the created mesh\r\n     */\r\n    public buildMeshAsync(material?: Material): Promise<Mesh> {\r\n        return Promise.all(this._promises).then(() => {\r\n            this._isReady = true;\r\n            return this._buildMesh(material);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    private _buildMesh(material?: Material): Promise<Mesh> {\r\n        if (this.nbParticles === 0) {\r\n            this.addPoints(1);\r\n        }\r\n\r\n        this._positions32 = new Float32Array(this._positions);\r\n        this._uvs32 = new Float32Array(this._uvs);\r\n        this._colors32 = new Float32Array(this._colors);\r\n\r\n        const vertexData = new VertexData();\r\n        vertexData.set(this._positions32, VertexBuffer.PositionKind);\r\n\r\n        if (this._uvs32.length > 0) {\r\n            vertexData.set(this._uvs32, VertexBuffer.UVKind);\r\n        }\r\n        let ec = 0; //emissive color value 0 for UVs, 1 for color\r\n        if (this._colors32.length > 0) {\r\n            ec = 1;\r\n            vertexData.set(this._colors32, VertexBuffer.ColorKind);\r\n        }\r\n        const mesh = new Mesh(this.name, this._scene);\r\n        vertexData.applyToMesh(mesh, this._updatable);\r\n        this.mesh = mesh;\r\n\r\n        // free memory\r\n        (<any>this._positions) = null;\r\n        (<any>this._uvs) = null;\r\n        (<any>this._colors) = null;\r\n\r\n        if (!this._updatable) {\r\n            this.particles.length = 0;\r\n        }\r\n\r\n        let mat = material;\r\n\r\n        if (!mat) {\r\n            mat = new StandardMaterial(\"point cloud material\", this._scene);\r\n            (<StandardMaterial>mat).emissiveColor = new Color3(ec, ec, ec);\r\n            (<StandardMaterial>mat).disableLighting = true;\r\n            (<StandardMaterial>mat).pointsCloud = true;\r\n            (<StandardMaterial>mat).pointSize = this._size;\r\n        }\r\n        mesh.material = mat;\r\n\r\n        return new Promise((resolve) => resolve(mesh));\r\n    }\r\n\r\n    // adds a new particle object in the particles array\r\n    private _addParticle(idx: number, group: PointsGroup, groupId: number, idxInGroup: number): CloudPoint {\r\n        const cp = new CloudPoint(idx, group, groupId, idxInGroup, this);\r\n        this.particles.push(cp);\r\n        return cp;\r\n    }\r\n\r\n    private _randomUnitVector(particle: CloudPoint): void {\r\n        particle.position = new Vector3(Math.random(), Math.random(), Math.random());\r\n        particle.color = new Color4(1, 1, 1, 1);\r\n    }\r\n\r\n    private _getColorIndicesForCoord(pointsGroup: PointsGroup, x: number, y: number, width: number): Color4 {\r\n        const imageData = <Uint8Array>pointsGroup._groupImageData;\r\n        const color = y * (width * 4) + x * 4;\r\n        const colorIndices = [color, color + 1, color + 2, color + 3];\r\n        const redIndex = colorIndices[0];\r\n        const greenIndex = colorIndices[1];\r\n        const blueIndex = colorIndices[2];\r\n        const alphaIndex = colorIndices[3];\r\n        const redForCoord = imageData[redIndex];\r\n        const greenForCoord = imageData[greenIndex];\r\n        const blueForCoord = imageData[blueIndex];\r\n        const alphaForCoord = imageData[alphaIndex];\r\n        return new Color4(redForCoord / 255, greenForCoord / 255, blueForCoord / 255, alphaForCoord);\r\n    }\r\n\r\n    private _setPointsColorOrUV(\r\n        mesh: Mesh,\r\n        pointsGroup: PointsGroup,\r\n        isVolume: boolean,\r\n        colorFromTexture?: boolean,\r\n        hasTexture?: boolean,\r\n        color?: Color4,\r\n        range?: number,\r\n        uvSetIndex?: number\r\n    ): void {\r\n        uvSetIndex = uvSetIndex ?? 0;\r\n\r\n        if (isVolume) {\r\n            mesh.updateFacetData();\r\n        }\r\n\r\n        const boundInfo = mesh.getBoundingInfo();\r\n        const diameter = 2 * boundInfo.boundingSphere.radius;\r\n\r\n        let meshPos = <FloatArray>mesh.getVerticesData(VertexBuffer.PositionKind);\r\n        const meshInd = <IndicesArray>mesh.getIndices();\r\n        const meshUV = <FloatArray>mesh.getVerticesData(VertexBuffer.UVKind + (uvSetIndex ? uvSetIndex + 1 : \"\"));\r\n        const meshCol = <FloatArray>mesh.getVerticesData(VertexBuffer.ColorKind);\r\n\r\n        const place = Vector3.Zero();\r\n        mesh.computeWorldMatrix();\r\n        const meshMatrix: Matrix = mesh.getWorldMatrix();\r\n        if (!meshMatrix.isIdentity()) {\r\n            meshPos = meshPos.slice(0);\r\n            for (let p = 0; p < meshPos.length / 3; p++) {\r\n                Vector3.TransformCoordinatesFromFloatsToRef(meshPos[3 * p], meshPos[3 * p + 1], meshPos[3 * p + 2], meshMatrix, place);\r\n                meshPos[3 * p] = place.x;\r\n                meshPos[3 * p + 1] = place.y;\r\n                meshPos[3 * p + 2] = place.z;\r\n            }\r\n        }\r\n\r\n        let idxPoints: number = 0;\r\n\r\n        let id0: number = 0;\r\n        let id1: number = 0;\r\n        let id2: number = 0;\r\n        let v0X: number = 0;\r\n        let v0Y: number = 0;\r\n        let v0Z: number = 0;\r\n        let v1X: number = 0;\r\n        let v1Y: number = 0;\r\n        let v1Z: number = 0;\r\n        let v2X: number = 0;\r\n        let v2Y: number = 0;\r\n        let v2Z: number = 0;\r\n        const vertex0 = Vector3.Zero();\r\n        const vertex1 = Vector3.Zero();\r\n        const vertex2 = Vector3.Zero();\r\n        const vec0 = Vector3.Zero();\r\n        const vec1 = Vector3.Zero();\r\n\r\n        let uv0X: number = 0;\r\n        let uv0Y: number = 0;\r\n        let uv1X: number = 0;\r\n        let uv1Y: number = 0;\r\n        let uv2X: number = 0;\r\n        let uv2Y: number = 0;\r\n        const uv0 = Vector2.Zero();\r\n        const uv1 = Vector2.Zero();\r\n        const uv2 = Vector2.Zero();\r\n        const uvec0 = Vector2.Zero();\r\n        const uvec1 = Vector2.Zero();\r\n\r\n        let col0X: number = 0;\r\n        let col0Y: number = 0;\r\n        let col0Z: number = 0;\r\n        let col0A: number = 0;\r\n        let col1X: number = 0;\r\n        let col1Y: number = 0;\r\n        let col1Z: number = 0;\r\n        let col1A: number = 0;\r\n        let col2X: number = 0;\r\n        let col2Y: number = 0;\r\n        let col2Z: number = 0;\r\n        let col2A: number = 0;\r\n        const col0 = Vector4.Zero();\r\n        const col1 = Vector4.Zero();\r\n        const col2 = Vector4.Zero();\r\n        const colvec0 = Vector4.Zero();\r\n        const colvec1 = Vector4.Zero();\r\n\r\n        let lamda: number = 0;\r\n        let mu: number = 0;\r\n        range = range ? range : 0;\r\n\r\n        let facetPoint: Vector3;\r\n        let uvPoint: Vector2;\r\n        let colPoint: Vector4 = new Vector4(0, 0, 0, 0);\r\n\r\n        let norm = Vector3.Zero();\r\n        let tang = Vector3.Zero();\r\n        let biNorm = Vector3.Zero();\r\n        let angle = 0;\r\n        let facetPlaneVec = Vector3.Zero();\r\n\r\n        let gap = 0;\r\n        let distance = 0;\r\n        const ray = new Ray(Vector3.Zero(), new Vector3(1, 0, 0));\r\n        let pickInfo: PickingInfo;\r\n        let direction = Vector3.Zero();\r\n\r\n        for (let index = 0; index < meshInd.length / 3; index++) {\r\n            id0 = meshInd[3 * index];\r\n            id1 = meshInd[3 * index + 1];\r\n            id2 = meshInd[3 * index + 2];\r\n            v0X = meshPos[3 * id0];\r\n            v0Y = meshPos[3 * id0 + 1];\r\n            v0Z = meshPos[3 * id0 + 2];\r\n            v1X = meshPos[3 * id1];\r\n            v1Y = meshPos[3 * id1 + 1];\r\n            v1Z = meshPos[3 * id1 + 2];\r\n            v2X = meshPos[3 * id2];\r\n            v2Y = meshPos[3 * id2 + 1];\r\n            v2Z = meshPos[3 * id2 + 2];\r\n            vertex0.set(v0X, v0Y, v0Z);\r\n            vertex1.set(v1X, v1Y, v1Z);\r\n            vertex2.set(v2X, v2Y, v2Z);\r\n            vertex1.subtractToRef(vertex0, vec0);\r\n            vertex2.subtractToRef(vertex1, vec1);\r\n\r\n            if (meshUV) {\r\n                uv0X = meshUV[2 * id0];\r\n                uv0Y = meshUV[2 * id0 + 1];\r\n                uv1X = meshUV[2 * id1];\r\n                uv1Y = meshUV[2 * id1 + 1];\r\n                uv2X = meshUV[2 * id2];\r\n                uv2Y = meshUV[2 * id2 + 1];\r\n                uv0.set(uv0X, uv0Y);\r\n                uv1.set(uv1X, uv1Y);\r\n                uv2.set(uv2X, uv2Y);\r\n                uv1.subtractToRef(uv0, uvec0);\r\n                uv2.subtractToRef(uv1, uvec1);\r\n            }\r\n\r\n            if (meshCol && colorFromTexture) {\r\n                col0X = meshCol[4 * id0];\r\n                col0Y = meshCol[4 * id0 + 1];\r\n                col0Z = meshCol[4 * id0 + 2];\r\n                col0A = meshCol[4 * id0 + 3];\r\n                col1X = meshCol[4 * id1];\r\n                col1Y = meshCol[4 * id1 + 1];\r\n                col1Z = meshCol[4 * id1 + 2];\r\n                col1A = meshCol[4 * id1 + 3];\r\n                col2X = meshCol[4 * id2];\r\n                col2Y = meshCol[4 * id2 + 1];\r\n                col2Z = meshCol[4 * id2 + 2];\r\n                col2A = meshCol[4 * id2 + 3];\r\n                col0.set(col0X, col0Y, col0Z, col0A);\r\n                col1.set(col1X, col1Y, col1Z, col1A);\r\n                col2.set(col2X, col2Y, col2Z, col2A);\r\n                col1.subtractToRef(col0, colvec0);\r\n                col2.subtractToRef(col1, colvec1);\r\n            }\r\n\r\n            let width: number;\r\n            let height: number;\r\n            let deltaS: number;\r\n            let deltaV: number;\r\n            let h: number;\r\n            let s: number;\r\n            let v: number;\r\n            let hsvCol: Color3;\r\n            const statedColor: Color3 = new Color3(0, 0, 0);\r\n            const colPoint3: Color3 = new Color3(0, 0, 0);\r\n            let pointColors: Color4;\r\n            let particle: CloudPoint;\r\n\r\n            for (let i = 0; i < pointsGroup._groupDensity[index]; i++) {\r\n                idxPoints = this.particles.length;\r\n                this._addParticle(idxPoints, pointsGroup, this._groupCounter, index + i);\r\n                particle = this.particles[idxPoints];\r\n                //form a point inside the facet v0, v1, v2;\r\n                lamda = Math.sqrt(Scalar.RandomRange(0, 1));\r\n                mu = Scalar.RandomRange(0, 1);\r\n                facetPoint = vertex0.add(vec0.scale(lamda)).add(vec1.scale(lamda * mu));\r\n                if (isVolume) {\r\n                    norm = mesh.getFacetNormal(index).normalize().scale(-1);\r\n                    tang = vec0.clone().normalize();\r\n                    biNorm = Vector3.Cross(norm, tang);\r\n                    angle = Scalar.RandomRange(0, 2 * Math.PI);\r\n                    facetPlaneVec = tang.scale(Math.cos(angle)).add(biNorm.scale(Math.sin(angle)));\r\n                    angle = Scalar.RandomRange(0.1, Math.PI / 2);\r\n                    direction = facetPlaneVec.scale(Math.cos(angle)).add(norm.scale(Math.sin(angle)));\r\n\r\n                    ray.origin = facetPoint.add(direction.scale(0.00001));\r\n                    ray.direction = direction;\r\n                    ray.length = diameter;\r\n                    pickInfo = ray.intersectsMesh(mesh);\r\n                    if (pickInfo.hit) {\r\n                        distance = pickInfo.pickedPoint!.subtract(facetPoint).length();\r\n                        gap = Scalar.RandomRange(0, 1) * distance;\r\n                        facetPoint.addInPlace(direction.scale(gap));\r\n                    }\r\n                }\r\n                particle.position = facetPoint.clone();\r\n                this._positions.push(particle.position.x, particle.position.y, particle.position.z);\r\n                if (colorFromTexture !== undefined) {\r\n                    if (meshUV) {\r\n                        uvPoint = uv0.add(uvec0.scale(lamda)).add(uvec1.scale(lamda * mu));\r\n                        if (colorFromTexture) {\r\n                            //Set particle color to texture color\r\n                            if (hasTexture && pointsGroup._groupImageData !== null) {\r\n                                width = pointsGroup._groupImgWidth;\r\n                                height = pointsGroup._groupImgHeight;\r\n                                pointColors = this._getColorIndicesForCoord(pointsGroup, Math.round(uvPoint.x * width), Math.round(uvPoint.y * height), width);\r\n                                particle.color = pointColors;\r\n                                this._colors.push(pointColors.r, pointColors.g, pointColors.b, pointColors.a);\r\n                            } else {\r\n                                if (meshCol) {\r\n                                    //failure in texture and colors available\r\n                                    colPoint = col0.add(colvec0.scale(lamda)).add(colvec1.scale(lamda * mu));\r\n                                    particle.color = new Color4(colPoint.x, colPoint.y, colPoint.z, colPoint.w);\r\n                                    this._colors.push(colPoint.x, colPoint.y, colPoint.z, colPoint.w);\r\n                                } else {\r\n                                    colPoint = col0.set(Math.random(), Math.random(), Math.random(), 1);\r\n                                    particle.color = new Color4(colPoint.x, colPoint.y, colPoint.z, colPoint.w);\r\n                                    this._colors.push(colPoint.x, colPoint.y, colPoint.z, colPoint.w);\r\n                                }\r\n                            }\r\n                        } else {\r\n                            //Set particle uv based on a mesh uv\r\n                            particle.uv = uvPoint.clone();\r\n                            this._uvs.push(particle.uv.x, particle.uv.y);\r\n                        }\r\n                    }\r\n                } else {\r\n                    if (color) {\r\n                        statedColor.set(color.r, color.g, color.b);\r\n                        deltaS = Scalar.RandomRange(-range, range);\r\n                        deltaV = Scalar.RandomRange(-range, range);\r\n                        hsvCol = statedColor.toHSV();\r\n                        h = hsvCol.r;\r\n                        s = hsvCol.g + deltaS;\r\n                        v = hsvCol.b + deltaV;\r\n                        if (s < 0) {\r\n                            s = 0;\r\n                        }\r\n                        if (s > 1) {\r\n                            s = 1;\r\n                        }\r\n                        if (v < 0) {\r\n                            v = 0;\r\n                        }\r\n                        if (v > 1) {\r\n                            v = 1;\r\n                        }\r\n                        Color3.HSVtoRGBToRef(h, s, v, colPoint3);\r\n                        colPoint.set(colPoint3.r, colPoint3.g, colPoint3.b, 1);\r\n                    } else {\r\n                        colPoint = col0.set(Math.random(), Math.random(), Math.random(), 1);\r\n                    }\r\n                    particle.color = new Color4(colPoint.x, colPoint.y, colPoint.z, colPoint.w);\r\n                    this._colors.push(colPoint.x, colPoint.y, colPoint.z, colPoint.w);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // stores mesh texture in dynamic texture for color pixel retrieval\r\n    // when pointColor type is color for surface points\r\n    private _colorFromTexture(mesh: Mesh, pointsGroup: PointsGroup, isVolume: boolean): void {\r\n        if (mesh.material === null) {\r\n            Logger.Warn(mesh.name + \"has no material.\");\r\n            pointsGroup._groupImageData = null;\r\n            this._setPointsColorOrUV(mesh, pointsGroup, isVolume, true, false);\r\n            return;\r\n        }\r\n\r\n        const mat = mesh.material;\r\n        const textureList: BaseTexture[] = mat.getActiveTextures();\r\n        if (textureList.length === 0) {\r\n            Logger.Warn(mesh.name + \"has no usable texture.\");\r\n            pointsGroup._groupImageData = null;\r\n            this._setPointsColorOrUV(mesh, pointsGroup, isVolume, true, false);\r\n            return;\r\n        }\r\n\r\n        const clone = <Mesh>mesh.clone();\r\n        clone.setEnabled(false);\r\n        this._promises.push(\r\n            new Promise((resolve: (_: void) => void) => {\r\n                BaseTexture.WhenAllReady(textureList, () => {\r\n                    let n = pointsGroup._textureNb;\r\n                    if (n < 0) {\r\n                        n = 0;\r\n                    }\r\n                    if (n > textureList.length - 1) {\r\n                        n = textureList.length - 1;\r\n                    }\r\n                    const finalize = () => {\r\n                        pointsGroup._groupImgWidth = textureList[n].getSize().width;\r\n                        pointsGroup._groupImgHeight = textureList[n].getSize().height;\r\n                        this._setPointsColorOrUV(clone, pointsGroup, isVolume, true, true, undefined, undefined, textureList[n].coordinatesIndex);\r\n                        clone.dispose();\r\n                        resolve();\r\n                    };\r\n                    pointsGroup._groupImageData = null;\r\n                    const dataPromise = textureList[n].readPixels();\r\n                    if (!dataPromise) {\r\n                        finalize();\r\n                    } else {\r\n                        dataPromise.then((data) => {\r\n                            pointsGroup._groupImageData = data;\r\n                            finalize();\r\n                        });\r\n                    }\r\n                });\r\n            })\r\n        );\r\n    }\r\n\r\n    // calculates the point density per facet of a mesh for surface points\r\n    private _calculateDensity(nbPoints: number, positions: FloatArray, indices: IndicesArray): number[] {\r\n        let id0: number;\r\n        let id1: number;\r\n        let id2: number;\r\n        let v0X: number;\r\n        let v0Y: number;\r\n        let v0Z: number;\r\n        let v1X: number;\r\n        let v1Y: number;\r\n        let v1Z: number;\r\n        let v2X: number;\r\n        let v2Y: number;\r\n        let v2Z: number;\r\n        const vertex0 = Vector3.Zero();\r\n        const vertex1 = Vector3.Zero();\r\n        const vertex2 = Vector3.Zero();\r\n        const vec0 = Vector3.Zero();\r\n        const vec1 = Vector3.Zero();\r\n        const normal = Vector3.Zero();\r\n\r\n        let area: number;\r\n        const cumulativeAreas: number[] = [];\r\n        let surfaceArea: number = 0;\r\n\r\n        const nbFacets = indices.length / 3;\r\n\r\n        //surface area\r\n        for (let index = 0; index < nbFacets; index++) {\r\n            id0 = indices[3 * index];\r\n            id1 = indices[3 * index + 1];\r\n            id2 = indices[3 * index + 2];\r\n            v0X = positions[3 * id0];\r\n            v0Y = positions[3 * id0 + 1];\r\n            v0Z = positions[3 * id0 + 2];\r\n            v1X = positions[3 * id1];\r\n            v1Y = positions[3 * id1 + 1];\r\n            v1Z = positions[3 * id1 + 2];\r\n            v2X = positions[3 * id2];\r\n            v2Y = positions[3 * id2 + 1];\r\n            v2Z = positions[3 * id2 + 2];\r\n            vertex0.set(v0X, v0Y, v0Z);\r\n            vertex1.set(v1X, v1Y, v1Z);\r\n            vertex2.set(v2X, v2Y, v2Z);\r\n            vertex1.subtractToRef(vertex0, vec0);\r\n            vertex2.subtractToRef(vertex1, vec1);\r\n            Vector3.CrossToRef(vec0, vec1, normal);\r\n            area = 0.5 * normal.length();\r\n            surfaceArea += area;\r\n            cumulativeAreas[index] = surfaceArea;\r\n        }\r\n\r\n        const density: number[] = new Array<number>(nbFacets);\r\n        let remainingPoints = nbPoints;\r\n        for (let index = nbFacets - 1; index > 0; index--) {\r\n            const cumulativeArea = cumulativeAreas[index];\r\n            if (cumulativeArea === 0) {\r\n                // avoiding division by 0 upon degenerate triangles\r\n                density[index] = 0;\r\n            } else {\r\n                const area = cumulativeArea - cumulativeAreas[index - 1];\r\n                const facetPointsWithFraction = (area / cumulativeArea) * remainingPoints;\r\n                const floored = Math.floor(facetPointsWithFraction);\r\n                const fraction = facetPointsWithFraction - floored;\r\n                const extraPoint = Number(Math.random() < fraction);\r\n                const facetPoints = floored + extraPoint;\r\n                density[index] = facetPoints;\r\n                remainingPoints -= facetPoints;\r\n            }\r\n        }\r\n        density[0] = remainingPoints;\r\n\r\n        return density;\r\n    }\r\n\r\n    /**\r\n     * Adds points to the PCS in random positions within a unit sphere\r\n     * @param nb (positive integer) the number of particles to be created from this model\r\n     * @param pointFunction is an optional javascript function to be called for each particle on PCS creation\r\n     * @returns the number of groups in the system\r\n     */\r\n    public addPoints(nb: number, pointFunction: any = this._randomUnitVector): number {\r\n        const pointsGroup = new PointsGroup(this._groupCounter, pointFunction);\r\n        let cp: CloudPoint;\r\n\r\n        // particles\r\n        let idx = this.nbParticles;\r\n        for (let i = 0; i < nb; i++) {\r\n            cp = this._addParticle(idx, pointsGroup, this._groupCounter, i);\r\n            if (pointsGroup && pointsGroup._positionFunction) {\r\n                pointsGroup._positionFunction(cp, idx, i);\r\n            }\r\n            this._positions.push(cp.position.x, cp.position.y, cp.position.z);\r\n            if (cp.color) {\r\n                this._colors.push(cp.color.r, cp.color.g, cp.color.b, cp.color.a);\r\n            }\r\n            if (cp.uv) {\r\n                this._uvs.push(cp.uv.x, cp.uv.y);\r\n            }\r\n            idx++;\r\n        }\r\n        this.nbParticles += nb;\r\n        this._groupCounter++;\r\n        return this._groupCounter;\r\n    }\r\n\r\n    /**\r\n     * Adds points to the PCS from the surface of the model shape\r\n     * @param mesh is any Mesh object that will be used as a surface model for the points\r\n     * @param nb (positive integer) the number of particles to be created from this model\r\n     * @param colorWith determines whether a point is colored using color (default), uv, random, stated or none (invisible)\r\n     * @param color (color4) to be used when colorWith is stated or color (number) when used to specify texture position\r\n     * @param range (number from 0 to 1) to determine the variation in shape and tone for a stated color\r\n     * @returns the number of groups in the system\r\n     */\r\n    public addSurfacePoints(mesh: Mesh, nb: number, colorWith?: number, color?: Color4 | number, range?: number): number {\r\n        let colored = colorWith ? colorWith : PointColor.Random;\r\n        if (isNaN(colored) || colored < 0 || colored > 3) {\r\n            colored = PointColor.Random;\r\n        }\r\n\r\n        const meshPos = <FloatArray>mesh.getVerticesData(VertexBuffer.PositionKind);\r\n        const meshInd = <IndicesArray>mesh.getIndices();\r\n\r\n        this._groups.push(this._groupCounter);\r\n        const pointsGroup = new PointsGroup(this._groupCounter, null);\r\n\r\n        pointsGroup._groupDensity = this._calculateDensity(nb, meshPos, meshInd);\r\n        if (colored === PointColor.Color) {\r\n            pointsGroup._textureNb = <number>color ? <number>color : 0;\r\n        } else {\r\n            color = <Color4>color ? <Color4>color : new Color4(1, 1, 1, 1);\r\n        }\r\n        switch (colored) {\r\n            case PointColor.Color:\r\n                this._colorFromTexture(mesh, pointsGroup, false);\r\n                break;\r\n            case PointColor.UV:\r\n                this._setPointsColorOrUV(mesh, pointsGroup, false, false, false);\r\n                break;\r\n            case PointColor.Random:\r\n                this._setPointsColorOrUV(mesh, pointsGroup, false);\r\n                break;\r\n            case PointColor.Stated:\r\n                this._setPointsColorOrUV(mesh, pointsGroup, false, undefined, undefined, <Color4>color, range);\r\n                break;\r\n        }\r\n        this.nbParticles += nb;\r\n        this._groupCounter++;\r\n        return this._groupCounter - 1;\r\n    }\r\n\r\n    /**\r\n     * Adds points to the PCS inside the model shape\r\n     * @param mesh is any Mesh object that will be used as a surface model for the points\r\n     * @param nb (positive integer) the number of particles to be created from this model\r\n     * @param colorWith determines whether a point is colored using color (default), uv, random, stated or none (invisible)\r\n     * @param color (color4) to be used when colorWith is stated or color (number) when used to specify texture position\r\n     * @param range (number from 0 to 1) to determine the variation in shape and tone for a stated color\r\n     * @returns the number of groups in the system\r\n     */\r\n    public addVolumePoints(mesh: Mesh, nb: number, colorWith?: number, color?: Color4 | number, range?: number): number {\r\n        let colored = colorWith ? colorWith : PointColor.Random;\r\n        if (isNaN(colored) || colored < 0 || colored > 3) {\r\n            colored = PointColor.Random;\r\n        }\r\n\r\n        const meshPos = <FloatArray>mesh.getVerticesData(VertexBuffer.PositionKind);\r\n        const meshInd = <IndicesArray>mesh.getIndices();\r\n\r\n        this._groups.push(this._groupCounter);\r\n        const pointsGroup = new PointsGroup(this._groupCounter, null);\r\n\r\n        pointsGroup._groupDensity = this._calculateDensity(nb, meshPos, meshInd);\r\n        if (colored === PointColor.Color) {\r\n            pointsGroup._textureNb = <number>color ? <number>color : 0;\r\n        } else {\r\n            color = <Color4>color ? <Color4>color : new Color4(1, 1, 1, 1);\r\n        }\r\n        switch (colored) {\r\n            case PointColor.Color:\r\n                this._colorFromTexture(mesh, pointsGroup, true);\r\n                break;\r\n            case PointColor.UV:\r\n                this._setPointsColorOrUV(mesh, pointsGroup, true, false, false);\r\n                break;\r\n            case PointColor.Random:\r\n                this._setPointsColorOrUV(mesh, pointsGroup, true);\r\n                break;\r\n            case PointColor.Stated:\r\n                this._setPointsColorOrUV(mesh, pointsGroup, true, undefined, undefined, <Color4>color, range);\r\n                break;\r\n        }\r\n        this.nbParticles += nb;\r\n        this._groupCounter++;\r\n        return this._groupCounter - 1;\r\n    }\r\n\r\n    /**\r\n     *  Sets all the particles : this method actually really updates the mesh according to the particle positions, rotations, colors, textures, etc.\r\n     *  This method calls `updateParticle()` for each particle of the SPS.\r\n     *  For an animated SPS, it is usually called within the render loop.\r\n     * @param start The particle index in the particle array where to start to compute the particle property values _(default 0)_\r\n     * @param end The particle index in the particle array where to stop to compute the particle property values _(default nbParticle - 1)_\r\n     * @param update If the mesh must be finally updated on this call after all the particle computations _(default true)_\r\n     * @returns the PCS.\r\n     */\r\n    public setParticles(start: number = 0, end: number = this.nbParticles - 1, update: boolean = true): PointsCloudSystem {\r\n        if (!this._updatable || !this._isReady) {\r\n            return this;\r\n        }\r\n\r\n        // custom beforeUpdate\r\n        this.beforeUpdateParticles(start, end, update);\r\n\r\n        const rotMatrix = TmpVectors.Matrix[0];\r\n        const mesh = this.mesh;\r\n        const colors32 = this._colors32;\r\n        const positions32 = this._positions32;\r\n        const uvs32 = this._uvs32;\r\n\r\n        const tempVectors = TmpVectors.Vector3;\r\n        const camAxisX = tempVectors[5].copyFromFloats(1.0, 0.0, 0.0);\r\n        const camAxisY = tempVectors[6].copyFromFloats(0.0, 1.0, 0.0);\r\n        const camAxisZ = tempVectors[7].copyFromFloats(0.0, 0.0, 1.0);\r\n        const minimum = tempVectors[8].setAll(Number.MAX_VALUE);\r\n        const maximum = tempVectors[9].setAll(-Number.MAX_VALUE);\r\n\r\n        Matrix.IdentityToRef(rotMatrix);\r\n        let idx = 0; // current index of the particle\r\n\r\n        if (this.mesh?.isFacetDataEnabled) {\r\n            this._computeBoundingBox = true;\r\n        }\r\n\r\n        end = end >= this.nbParticles ? this.nbParticles - 1 : end;\r\n        if (this._computeBoundingBox) {\r\n            if (start != 0 || end != this.nbParticles - 1) {\r\n                // only some particles are updated, then use the current existing BBox basis. Note : it can only increase.\r\n                const boundingInfo = this.mesh?.getBoundingInfo();\r\n                if (boundingInfo) {\r\n                    minimum.copyFrom(boundingInfo.minimum);\r\n                    maximum.copyFrom(boundingInfo.maximum);\r\n                }\r\n            }\r\n        }\r\n\r\n        idx = 0; // particle index\r\n        let pindex = 0; //index in positions array\r\n        let cindex = 0; //index in color array\r\n        let uindex = 0; //index in uv array\r\n\r\n        // particle loop\r\n        for (let p = start; p <= end; p++) {\r\n            const particle = this.particles[p];\r\n            idx = particle.idx;\r\n            pindex = 3 * idx;\r\n            cindex = 4 * idx;\r\n            uindex = 2 * idx;\r\n\r\n            // call to custom user function to update the particle properties\r\n            this.updateParticle(particle);\r\n\r\n            const particleRotationMatrix = particle._rotationMatrix;\r\n            const particlePosition = particle.position;\r\n            const particleGlobalPosition = particle._globalPosition;\r\n\r\n            if (this._computeParticleRotation) {\r\n                particle.getRotationMatrix(rotMatrix);\r\n            }\r\n\r\n            const particleHasParent = particle.parentId !== null;\r\n            if (particleHasParent) {\r\n                const parent = this.particles[particle.parentId!];\r\n                const parentRotationMatrix = parent._rotationMatrix;\r\n                const parentGlobalPosition = parent._globalPosition;\r\n\r\n                const rotatedY = particlePosition.x * parentRotationMatrix[1] + particlePosition.y * parentRotationMatrix[4] + particlePosition.z * parentRotationMatrix[7];\r\n                const rotatedX = particlePosition.x * parentRotationMatrix[0] + particlePosition.y * parentRotationMatrix[3] + particlePosition.z * parentRotationMatrix[6];\r\n                const rotatedZ = particlePosition.x * parentRotationMatrix[2] + particlePosition.y * parentRotationMatrix[5] + particlePosition.z * parentRotationMatrix[8];\r\n\r\n                particleGlobalPosition.x = parentGlobalPosition.x + rotatedX;\r\n                particleGlobalPosition.y = parentGlobalPosition.y + rotatedY;\r\n                particleGlobalPosition.z = parentGlobalPosition.z + rotatedZ;\r\n\r\n                if (this._computeParticleRotation) {\r\n                    const rotMatrixValues = rotMatrix.m;\r\n                    particleRotationMatrix[0] =\r\n                        rotMatrixValues[0] * parentRotationMatrix[0] + rotMatrixValues[1] * parentRotationMatrix[3] + rotMatrixValues[2] * parentRotationMatrix[6];\r\n                    particleRotationMatrix[1] =\r\n                        rotMatrixValues[0] * parentRotationMatrix[1] + rotMatrixValues[1] * parentRotationMatrix[4] + rotMatrixValues[2] * parentRotationMatrix[7];\r\n                    particleRotationMatrix[2] =\r\n                        rotMatrixValues[0] * parentRotationMatrix[2] + rotMatrixValues[1] * parentRotationMatrix[5] + rotMatrixValues[2] * parentRotationMatrix[8];\r\n                    particleRotationMatrix[3] =\r\n                        rotMatrixValues[4] * parentRotationMatrix[0] + rotMatrixValues[5] * parentRotationMatrix[3] + rotMatrixValues[6] * parentRotationMatrix[6];\r\n                    particleRotationMatrix[4] =\r\n                        rotMatrixValues[4] * parentRotationMatrix[1] + rotMatrixValues[5] * parentRotationMatrix[4] + rotMatrixValues[6] * parentRotationMatrix[7];\r\n                    particleRotationMatrix[5] =\r\n                        rotMatrixValues[4] * parentRotationMatrix[2] + rotMatrixValues[5] * parentRotationMatrix[5] + rotMatrixValues[6] * parentRotationMatrix[8];\r\n                    particleRotationMatrix[6] =\r\n                        rotMatrixValues[8] * parentRotationMatrix[0] + rotMatrixValues[9] * parentRotationMatrix[3] + rotMatrixValues[10] * parentRotationMatrix[6];\r\n                    particleRotationMatrix[7] =\r\n                        rotMatrixValues[8] * parentRotationMatrix[1] + rotMatrixValues[9] * parentRotationMatrix[4] + rotMatrixValues[10] * parentRotationMatrix[7];\r\n                    particleRotationMatrix[8] =\r\n                        rotMatrixValues[8] * parentRotationMatrix[2] + rotMatrixValues[9] * parentRotationMatrix[5] + rotMatrixValues[10] * parentRotationMatrix[8];\r\n                }\r\n            } else {\r\n                particleGlobalPosition.x = 0;\r\n                particleGlobalPosition.y = 0;\r\n                particleGlobalPosition.z = 0;\r\n\r\n                if (this._computeParticleRotation) {\r\n                    const rotMatrixValues = rotMatrix.m;\r\n                    particleRotationMatrix[0] = rotMatrixValues[0];\r\n                    particleRotationMatrix[1] = rotMatrixValues[1];\r\n                    particleRotationMatrix[2] = rotMatrixValues[2];\r\n                    particleRotationMatrix[3] = rotMatrixValues[4];\r\n                    particleRotationMatrix[4] = rotMatrixValues[5];\r\n                    particleRotationMatrix[5] = rotMatrixValues[6];\r\n                    particleRotationMatrix[6] = rotMatrixValues[8];\r\n                    particleRotationMatrix[7] = rotMatrixValues[9];\r\n                    particleRotationMatrix[8] = rotMatrixValues[10];\r\n                }\r\n            }\r\n\r\n            const pivotBackTranslation = tempVectors[11];\r\n            if (particle.translateFromPivot) {\r\n                pivotBackTranslation.setAll(0.0);\r\n            } else {\r\n                pivotBackTranslation.copyFrom(particle.pivot);\r\n            }\r\n\r\n            // positions\r\n            const tmpVertex = tempVectors[0];\r\n            tmpVertex.copyFrom(particle.position);\r\n            const vertexX = tmpVertex.x - particle.pivot.x;\r\n            const vertexY = tmpVertex.y - particle.pivot.y;\r\n            const vertexZ = tmpVertex.z - particle.pivot.z;\r\n\r\n            let rotatedX = vertexX * particleRotationMatrix[0] + vertexY * particleRotationMatrix[3] + vertexZ * particleRotationMatrix[6];\r\n            let rotatedY = vertexX * particleRotationMatrix[1] + vertexY * particleRotationMatrix[4] + vertexZ * particleRotationMatrix[7];\r\n            let rotatedZ = vertexX * particleRotationMatrix[2] + vertexY * particleRotationMatrix[5] + vertexZ * particleRotationMatrix[8];\r\n\r\n            rotatedX += pivotBackTranslation.x;\r\n            rotatedY += pivotBackTranslation.y;\r\n            rotatedZ += pivotBackTranslation.z;\r\n\r\n            const px = (positions32[pindex] = particleGlobalPosition.x + camAxisX.x * rotatedX + camAxisY.x * rotatedY + camAxisZ.x * rotatedZ);\r\n            const py = (positions32[pindex + 1] = particleGlobalPosition.y + camAxisX.y * rotatedX + camAxisY.y * rotatedY + camAxisZ.y * rotatedZ);\r\n            const pz = (positions32[pindex + 2] = particleGlobalPosition.z + camAxisX.z * rotatedX + camAxisY.z * rotatedY + camAxisZ.z * rotatedZ);\r\n\r\n            if (this._computeBoundingBox) {\r\n                minimum.minimizeInPlaceFromFloats(px, py, pz);\r\n                maximum.maximizeInPlaceFromFloats(px, py, pz);\r\n            }\r\n\r\n            if (this._computeParticleColor && particle.color) {\r\n                const color = particle.color;\r\n                const colors32 = this._colors32;\r\n                colors32[cindex] = color.r;\r\n                colors32[cindex + 1] = color.g;\r\n                colors32[cindex + 2] = color.b;\r\n                colors32[cindex + 3] = color.a;\r\n            }\r\n            if (this._computeParticleTexture && particle.uv) {\r\n                const uv = particle.uv;\r\n                const uvs32 = this._uvs32;\r\n                uvs32[uindex] = uv.x;\r\n                uvs32[uindex + 1] = uv.y;\r\n            }\r\n        }\r\n\r\n        // if the VBO must be updated\r\n        if (mesh) {\r\n            if (update) {\r\n                if (this._computeParticleColor) {\r\n                    mesh.updateVerticesData(VertexBuffer.ColorKind, colors32, false, false);\r\n                }\r\n                if (this._computeParticleTexture) {\r\n                    mesh.updateVerticesData(VertexBuffer.UVKind, uvs32, false, false);\r\n                }\r\n                mesh.updateVerticesData(VertexBuffer.PositionKind, positions32, false, false);\r\n            }\r\n\r\n            if (this._computeBoundingBox) {\r\n                if (mesh.hasBoundingInfo) {\r\n                    mesh.getBoundingInfo().reConstruct(minimum, maximum, mesh._worldMatrix);\r\n                } else {\r\n                    mesh.buildBoundingInfo(minimum, maximum, mesh._worldMatrix);\r\n                }\r\n            }\r\n        }\r\n        this.afterUpdateParticles(start, end, update);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Disposes the PCS.\r\n     */\r\n    public dispose(): void {\r\n        this.mesh?.dispose();\r\n        this.vars = null;\r\n        // drop references to internal big arrays for the GC\r\n        (<any>this._positions) = null;\r\n        (<any>this._indices) = null;\r\n        (<any>this._normals) = null;\r\n        (<any>this._uvs) = null;\r\n        (<any>this._colors) = null;\r\n        (<any>this._indices32) = null;\r\n        (<any>this._positions32) = null;\r\n        (<any>this._uvs32) = null;\r\n        (<any>this._colors32) = null;\r\n    }\r\n\r\n    /**\r\n     * Visibility helper : Recomputes the visible size according to the mesh bounding box\r\n     * doc :\r\n     * @returns the PCS.\r\n     */\r\n    public refreshVisibleSize(): PointsCloudSystem {\r\n        if (!this._isVisibilityBoxLocked) {\r\n            this.mesh?.refreshBoundingInfo();\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Visibility helper : Sets the size of a visibility box, this sets the underlying mesh bounding box.\r\n     * @param size the size (float) of the visibility box\r\n     * note : this doesn't lock the PCS mesh bounding box.\r\n     * doc :\r\n     */\r\n    public setVisibilityBox(size: number): void {\r\n        if (!this.mesh) {\r\n            return;\r\n        }\r\n\r\n        const vis = size / 2;\r\n        this.mesh.buildBoundingInfo(new Vector3(-vis, -vis, -vis), new Vector3(vis, vis, vis));\r\n    }\r\n\r\n    /**\r\n     * Gets whether the PCS is always visible or not\r\n     * doc :\r\n     */\r\n    public get isAlwaysVisible(): boolean {\r\n        return this._alwaysVisible;\r\n    }\r\n\r\n    /**\r\n     * Sets the PCS as always visible or not\r\n     * doc :\r\n     */\r\n    public set isAlwaysVisible(val: boolean) {\r\n        if (!this.mesh) {\r\n            return;\r\n        }\r\n\r\n        this._alwaysVisible = val;\r\n        this.mesh.alwaysSelectAsActiveMesh = val;\r\n    }\r\n\r\n    /**\r\n     * Tells to `setParticles()` to compute the particle rotations or not\r\n     * Default value : false. The PCS is faster when it's set to false\r\n     * Note : particle rotations are only applied to parent particles\r\n     * Note : the particle rotations aren't stored values, so setting `computeParticleRotation` to false will prevents the particle to rotate\r\n     */\r\n    public set computeParticleRotation(val: boolean) {\r\n        this._computeParticleRotation = val;\r\n    }\r\n\r\n    /**\r\n     * Tells to `setParticles()` to compute the particle colors or not.\r\n     * Default value : true. The PCS is faster when it's set to false.\r\n     * Note : the particle colors are stored values, so setting `computeParticleColor` to false will keep yet the last colors set.\r\n     */\r\n    public set computeParticleColor(val: boolean) {\r\n        this._computeParticleColor = val;\r\n    }\r\n\r\n    public set computeParticleTexture(val: boolean) {\r\n        this._computeParticleTexture = val;\r\n    }\r\n    /**\r\n     * Gets if `setParticles()` computes the particle colors or not.\r\n     * Default value : false. The PCS is faster when it's set to false.\r\n     * Note : the particle colors are stored values, so setting `computeParticleColor` to false will keep yet the last colors set.\r\n     */\r\n    public get computeParticleColor(): boolean {\r\n        return this._computeParticleColor;\r\n    }\r\n    /**\r\n     * Gets if `setParticles()` computes the particle textures or not.\r\n     * Default value : false. The PCS is faster when it's set to false.\r\n     * Note : the particle textures are stored values, so setting `computeParticleTexture` to false will keep yet the last colors set.\r\n     */\r\n    public get computeParticleTexture(): boolean {\r\n        return this._computeParticleTexture;\r\n    }\r\n    /**\r\n     * Tells to `setParticles()` to compute or not the mesh bounding box when computing the particle positions.\r\n     */\r\n    public set computeBoundingBox(val: boolean) {\r\n        this._computeBoundingBox = val;\r\n    }\r\n    /**\r\n     * Gets if `setParticles()` computes or not the mesh bounding box when computing the particle positions.\r\n     */\r\n    public get computeBoundingBox(): boolean {\r\n        return this._computeBoundingBox;\r\n    }\r\n\r\n    // =======================================================================\r\n    // Particle behavior logic\r\n    // these following methods may be overwritten by users to fit their needs\r\n\r\n    /**\r\n     * This function does nothing. It may be overwritten to set all the particle first values.\r\n     * The PCS doesn't call this function, you may have to call it by your own.\r\n     * doc :\r\n     */\r\n    public initParticles(): void {}\r\n\r\n    /**\r\n     * This function does nothing. It may be overwritten to recycle a particle\r\n     * The PCS doesn't call this function, you can to call it\r\n     * doc :\r\n     * @param particle The particle to recycle\r\n     * @returns the recycled particle\r\n     */\r\n    public recycleParticle(particle: CloudPoint): CloudPoint {\r\n        return particle;\r\n    }\r\n\r\n    /**\r\n     * Updates a particle : this function should  be overwritten by the user.\r\n     * It is called on each particle by `setParticles()`. This is the place to code each particle behavior.\r\n     * doc :\r\n     * @example : just set a particle position or velocity and recycle conditions\r\n     * @param particle The particle to update\r\n     * @returns the updated particle\r\n     */\r\n    public updateParticle(particle: CloudPoint): CloudPoint {\r\n        return particle;\r\n    }\r\n\r\n    /**\r\n     * This will be called before any other treatment by `setParticles()` and will be passed three parameters.\r\n     * This does nothing and may be overwritten by the user.\r\n     * @param start the particle index in the particle array where to start to iterate, same than the value passed to setParticle()\r\n     * @param stop the particle index in the particle array where to stop to iterate, same than the value passed to setParticle()\r\n     * @param update the boolean update value actually passed to setParticles()\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public beforeUpdateParticles(start?: number, stop?: number, update?: boolean): void {}\r\n    /**\r\n     * This will be called  by `setParticles()` after all the other treatments and just before the actual mesh update.\r\n     * This will be passed three parameters.\r\n     * This does nothing and may be overwritten by the user.\r\n     * @param start the particle index in the particle array where to start to iterate, same than the value passed to setParticle()\r\n     * @param stop the particle index in the particle array where to stop to iterate, same than the value passed to setParticle()\r\n     * @param update the boolean update value actually passed to setParticles()\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public afterUpdateParticles(start?: number, stop?: number, update?: boolean): void {}\r\n}\r\n"]}