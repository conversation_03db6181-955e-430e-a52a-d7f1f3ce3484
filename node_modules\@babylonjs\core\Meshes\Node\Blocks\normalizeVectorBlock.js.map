{"version": 3, "file": "normalizeVectorBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Meshes/Node/Blocks/normalizeVectorBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,qCAAqC,EAAE,MAAM,2CAA2C,CAAC;AAClG,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AAGzD;;GAEG;AACH,MAAM,OAAO,oBAAqB,SAAQ,iBAAiB;IACvD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAEZ,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAC9E,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,YAAY,CAAC,CAAC;QAElF,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAC/F,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,MAAM,CAAC,CAAC;QAChG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,QAAQ,CAAC,CAAC;QAClG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,CAAC;QACjG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;QAEnC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;YAChC,OAAO;SACV;QAED,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,CAAC;IAC7F,CAAC;CACJ;AAED,aAAa,CAAC,8BAA8B,EAAE,oBAAoB,CAAC,CAAC", "sourcesContent": ["import { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"../Enums/nodeGeometryConnectionPointTypes\";\r\nimport { NodeGeometryBlock } from \"../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../nodeGeometryBlockConnectionPoint\";\r\nimport type { NodeGeometryBuildState } from \"../nodeGeometryBuildState\";\r\n/**\r\n * Block used to normalize vectors\r\n */\r\nexport class NormalizeVectorBlock extends NodeGeometryBlock {\r\n    /**\r\n     * Creates a new NormalizeVectorBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"input\", NodeGeometryBlockConnectionPointTypes.AutoDetect);\r\n        this.registerOutput(\"output\", NodeGeometryBlockConnectionPointTypes.BasedOnInput);\r\n\r\n        this._inputs[0].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Float);\r\n        this._inputs[0].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Matrix);\r\n        this._inputs[0].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Geometry);\r\n        this._inputs[0].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Texture);\r\n        this._outputs[0]._typeConnectionSource = this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"NormalizeVectorBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the input component\r\n     */\r\n    public get input(): NodeGeometryConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeGeometryBuildState) {\r\n        super._buildBlock(state);\r\n        this.output._storedFunction = null;\r\n\r\n        if (!this.input.isConnected) {\r\n            this.output._storedValue = null;\r\n            return;\r\n        }\r\n\r\n        this.output._storedFunction = (state) => this.input.getConnectedValue(state).normalize();\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.NormalizeVectorBlock\", NormalizeVectorBlock);\r\n"]}