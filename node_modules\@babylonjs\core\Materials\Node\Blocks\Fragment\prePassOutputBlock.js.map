{"version": 3, "file": "prePassOutputBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Fragment/prePassOutputBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,iBAAiB;IACrD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACnF,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,qCAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5F,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,qCAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACzF,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,qCAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAE3F,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAA0C,CAAC,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,CAAC,CAAC;QACzJ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAA0C,CAAC,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,CAAC,CAAC;QACzJ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAA0C,CACrD,qCAAqC,CAAC,OAAO;YACzC,qCAAqC,CAAC,OAAO;YAC7C,qCAAqC,CAAC,MAAM;YAC5C,qCAAqC,CAAC,MAAM,CACnD,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QAEvC,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE9C,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAE5D,KAAK,CAAC,iBAAiB,IAAI,0BAA0B,CAAC;QACtD,KAAK,CAAC,iBAAiB,IAAI,0BAA0B,CAAC;QACtD,IAAI,SAAS,CAAC,cAAc,EAAE;YAC1B,KAAK,CAAC,iBAAiB,IAAI,4CAA4C,SAAS,CAAC,sBAAsB,uBAAuB,CAAC;SAClI;aAAM;YACH,iFAAiF;YACjF,KAAK,CAAC,iBAAiB,IAAI,mEAAmE,CAAC;SAClG;QACD,KAAK,CAAC,iBAAiB,IAAI,YAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,6BAA6B,CAAC;QACzD,IAAI,aAAa,CAAC,cAAc,EAAE;YAC9B,KAAK,CAAC,iBAAiB,IAAI,+CAA+C,aAAa,CAAC,sBAAsB,SAC1G,aAAa,CAAC,cAAc,CAAC,IAAI,KAAK,qCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAC,CAAC,KACxI,QAAQ,CAAC;SACZ;aAAM;YACH,gFAAgF;YAChF,KAAK,CAAC,iBAAiB,IAAI,sEAAsE,CAAC;SACrG;QACD,KAAK,CAAC,iBAAiB,IAAI,YAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,2BAA2B,CAAC;QACvD,IAAI,UAAU,CAAC,cAAc,EAAE;YAC3B,KAAK,CAAC,iBAAiB,IAAI,6CAA6C,UAAU,CAAC,sBAAsB,SACrG,UAAU,CAAC,cAAc,CAAC,IAAI,KAAK,qCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAC,CAAC,KAClI,QAAQ,CAAC;SACZ;aAAM;YACH,8EAA8E;YAC9E,KAAK,CAAC,iBAAiB,IAAI,oEAAoE,CAAC;SACnG;QACD,KAAK,CAAC,iBAAiB,IAAI,YAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,iCAAiC,CAAC;QAC7D,IAAI,YAAY,CAAC,cAAc,EAAE;YAC7B,KAAK,CAAC,iBAAiB,IAAI,mDAAmD,YAAY,CAAC,sBAAsB,SAC7G,YAAY,CAAC,cAAc,CAAC,IAAI,KAAK,qCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAC,CAAC,KACtI,QAAQ,CAAC;SACZ;aAAM;YACH,oFAAoF;YACpF,KAAK,CAAC,iBAAiB,IAAI,0EAA0E,CAAC;SACzG;QACD,KAAK,CAAC,iBAAiB,IAAI,YAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,YAAY,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,4BAA4B,EAAE,kBAAkB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\n\r\n/**\r\n * Block used to output values on the prepass textures\r\n */\r\nexport class PrePassOutputBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new PrePassOutputBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment, true);\r\n\r\n        this.registerInput(\"viewDepth\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n        this.registerInput(\"viewNormal\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n        this.registerInput(\"reflectivity\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n\r\n        this.inputs[1].addExcludedConnectionPointFromAllowedTypes(NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        this.inputs[2].addExcludedConnectionPointFromAllowedTypes(NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        this.inputs[3].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Vector3 |\r\n                NodeMaterialBlockConnectionPointTypes.Vector4 |\r\n                NodeMaterialBlockConnectionPointTypes.Color3 |\r\n                NodeMaterialBlockConnectionPointTypes.Color4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"PrePassOutputBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the view depth component\r\n     */\r\n    public get viewDepth(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the world position component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the view normal component\r\n     */\r\n    public get viewNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the reflectivity component\r\n     */\r\n    public get reflectivity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const worldPosition = this.worldPosition;\r\n        const viewNormal = this.viewNormal;\r\n        const viewDepth = this.viewDepth;\r\n        const reflectivity = this.reflectivity;\r\n\r\n        state.sharedData.blocksWithDefines.push(this);\r\n\r\n        const comments = `//${this.name}`;\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n\r\n        state.compilationString += `#if defined(PREPASS)\\r\\n`;\r\n        state.compilationString += `#ifdef PREPASS_DEPTH\\r\\n`;\r\n        if (viewDepth.connectedPoint) {\r\n            state.compilationString += ` gl_FragData[PREPASS_DEPTH_INDEX] = vec4(${viewDepth.associatedVariableName}, 0.0, 0.0, 1.0);\\r\\n`;\r\n        } else {\r\n            // We have to write something on the viewDepth output or it will raise a gl error\r\n            state.compilationString += ` gl_FragData[PREPASS_DEPTH_INDEX] = vec4(0.0, 0.0, 0.0, 0.0);\\r\\n`;\r\n        }\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#ifdef PREPASS_POSITION\\r\\n`;\r\n        if (worldPosition.connectedPoint) {\r\n            state.compilationString += ` gl_FragData[PREPASS_POSITION_INDEX] = vec4(${worldPosition.associatedVariableName}.rgb, ${\r\n                worldPosition.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Vector4 ? worldPosition.associatedVariableName + \".a\" : \"1.0\"\r\n            });\\r\\n`;\r\n        } else {\r\n            // We have to write something on the position output or it will raise a gl error\r\n            state.compilationString += ` gl_FragData[PREPASS_POSITION_INDEX] = vec4(0.0, 0.0, 0.0, 0.0);\\r\\n`;\r\n        }\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#ifdef PREPASS_NORMAL\\r\\n`;\r\n        if (viewNormal.connectedPoint) {\r\n            state.compilationString += ` gl_FragData[PREPASS_NORMAL_INDEX] = vec4(${viewNormal.associatedVariableName}.rgb, ${\r\n                viewNormal.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Vector4 ? viewNormal.associatedVariableName + \".a\" : \"1.0\"\r\n            });\\r\\n`;\r\n        } else {\r\n            // We have to write something on the normal output or it will raise a gl error\r\n            state.compilationString += ` gl_FragData[PREPASS_NORMAL_INDEX] = vec4(0.0, 0.0, 0.0, 0.0);\\r\\n`;\r\n        }\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#ifdef PREPASS_REFLECTIVITY\\r\\n`;\r\n        if (reflectivity.connectedPoint) {\r\n            state.compilationString += ` gl_FragData[PREPASS_REFLECTIVITY_INDEX] = vec4(${reflectivity.associatedVariableName}.rgb, ${\r\n                reflectivity.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Vector4 ? reflectivity.associatedVariableName + \".a\" : \"1.0\"\r\n            });\\r\\n`;\r\n        } else {\r\n            // We have to write something on the reflectivity output or it will raise a gl error\r\n            state.compilationString += ` gl_FragData[PREPASS_REFLECTIVITY_INDEX] = vec4(0.0, 0.0, 0.0, 1.0);\\r\\n`;\r\n        }\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#endif\\r\\n`;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PrePassOutputBlock\", PrePassOutputBlock);\r\n"]}