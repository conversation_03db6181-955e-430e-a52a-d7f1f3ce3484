{"version": 3, "file": "ssaoRenderingPipeline.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/PostProcesses/RenderPipeline/Pipelines/ssaoRenderingPipeline.ts"], "names": [], "mappings": ";AAAA,yDAAyD;AACzD,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,4BAA4B,CAAC;AAGjE,OAAO,EAAE,OAAO,EAAE,MAAM,qCAAqC,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,oCAAoC,CAAC;AACjE,OAAO,EAAE,yBAAyB,EAAE,MAAM,iEAAiE,CAAC;AAC5G,OAAO,EAAE,uBAAuB,EAAE,MAAM,+DAA+D,CAAC;AACxG,OAAO,EAAE,eAAe,EAAE,MAAM,wCAAwC,CAAC;AACzE,OAAO,EAAE,eAAe,EAAE,MAAM,wCAAwC,CAAC;AACzE,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AACvD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AAErD,OAAO,EAAE,UAAU,EAAE,MAAM,wCAAwC,CAAC;AACpE,OAAO,EAAE,MAAM,EAAE,MAAM,4BAA4B,CAAC;AAEpD,OAAO,sFAAsF,CAAC;AAE9F,OAAO,gCAAgC,CAAC;AACxC,OAAO,uCAAuC,CAAC;AAE/C;;GAEG;AACH,MAAM,OAAO,qBAAsB,SAAQ,yBAAyB;IA2EhE;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;OAMG;IACH,YAAY,IAAY,EAAE,KAAY,EAAE,KAAU,EAAE,OAAkB;QAClE,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;QAzFnC,UAAU;QAEV;;;WAGG;QACI,iCAA4B,GAAW,8BAA8B,CAAC;QAC7E;;;WAGG;QACI,qBAAgB,GAAW,kBAAkB,CAAC;QACrD;;;WAGG;QACI,0BAAqB,GAAW,uBAAuB,CAAC;QAC/D;;;WAGG;QACI,0BAAqB,GAAW,uBAAuB,CAAC;QAC/D;;;WAGG;QACI,4BAAuB,GAAW,yBAAyB,CAAC;QAEnE;;WAEG;QAEI,kBAAa,GAAW,GAAG,CAAC;QAEnC;;WAEG;QAEI,WAAM,GAAW,MAAM,CAAC;QAE/B;;;;WAIG;QAEI,SAAI,GAAW,MAAM,CAAC;QAE7B;;;;WAIG;QAEI,YAAO,GAAW,QAAQ,CAAC;QAElC;;;WAGG;QAEI,SAAI,GAAW,GAAG,CAAC;QAWlB,iBAAY,GAAY,IAAI,CAAC;QAmBjC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,gBAAgB;QAChB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC;QAC3C,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC;QAEjD,IAAI,CAAC,yBAAyB,GAAG,IAAI,eAAe,CAAC,wBAAwB,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,qBAAqB,EAAE,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;QAC5J,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QACvC,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QACvC,IAAI,CAAC,6BAA6B,CAAC,YAAY,CAAC,CAAC;QAEjD,kBAAkB;QAClB,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,IAAI,CAAC,4BAA4B,EACjC,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,yBAAyB,CAAC;QAC1C,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,IAAI,CAAC,gBAAgB,EACrB,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;QACjC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,IAAI,CAAC,qBAAqB,EAC1B,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAClC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,IAAI,CAAC,qBAAqB,EAC1B,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAClC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QAEF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,IAAI,CAAC,uBAAuB,EAC5B,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACxC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QAEF,SAAS;QACT,KAAK,CAAC,gCAAgC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,OAAO,EAAE;YACT,KAAK,CAAC,gCAAgC,CAAC,6BAA6B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SACvF;IACL,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,OAAY,EAAE,MAAe;QAC/C,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAEtC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,4BAA4B;SACtF;IACL,CAAC;IAED,iBAAiB;IAEjB;;;OAGG;IACI,YAAY;QACf,OAAO,uBAAuB,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,qBAA8B,KAAK;QAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACjD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAEtC,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC/C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACtC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvC,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAChD;QAED,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAE9B,IAAI,kBAAkB,EAAE;YACpB,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;SACtC;QAED,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAE9G,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED,kBAAkB;IACV,sBAAsB,CAAC,KAAa;QACxC,MAAM,IAAI,GAAG,EAAE,CAAC;QAEhB,IAAI,CAAC,iBAAiB,GAAG,IAAI,eAAe,CACxC,OAAO,EACP,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EACjB,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EACvB,KAAK,EACL,SAAS,CAAC,wBAAwB,CACrC,CAAC;QACF,IAAI,CAAC,iBAAiB,GAAG,IAAI,eAAe,CACxC,OAAO,EACP,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EACjB,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EACvB,KAAK,EACL,SAAS,CAAC,wBAAwB,CACrC,CAAC;QAEF,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,EAAE;YACjD,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,cAAc,EAAE,CAAC;YACnF,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,EAAE;YACjD,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,EAAE,CAAC;YACrF,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;QAC9C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,KAAK,CAAC,QAAQ,EAAE,CAAC;IACrB,CAAC;IAEO,sBAAsB,CAAC,KAAa;QACxC,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,MAAM,YAAY,GAAG;YACjB,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM;YACrK,MAAM,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM;YACvK,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM;SACpE,CAAC;QACF,MAAM,aAAa,GAAG,GAAG,GAAG,UAAU,CAAC;QAEvC,IAAI,CAAC,gBAAgB,GAAG,IAAI,WAAW,CACnC,MAAM,EACN,MAAM,EACN,CAAC,cAAc,EAAE,eAAe,EAAE,kBAAkB,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,EAChI,CAAC,eAAe,CAAC,EACjB,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EACvB,KAAK,EACL,kBAAkB,GAAG,UAAU,GAAG,gBAAgB,CACrD,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,6BAA6B,GAAG,IAAI,CAAC;QAC3D,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YAC/C,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;gBAC/C,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;gBAChD,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;aAC5C;YAED,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAEnC,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;YAC7G,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5D,CAAC,CAAC;IACN,CAAC;IAEO,6BAA6B,CAAC,KAAa;QAC/C,IAAI,CAAC,uBAAuB,GAAG,IAAI,WAAW,CAC1C,aAAa,EACb,aAAa,EACb,EAAE,EACF,CAAC,eAAe,EAAE,UAAU,CAAC,EAC7B,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EACvB,KAAK,CACR,CAAC;QAEF,IAAI,CAAC,uBAAuB,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YACtD,MAAM,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YACpF,MAAM,CAAC,yBAAyB,CAAC,eAAe,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACtF,CAAC,CAAC;IACN,CAAC;IAEO,oBAAoB;QACxB,MAAM,IAAI,GAAG,GAAG,CAAC;QAEjB,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC;QAC7C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,GAAI;YACvC,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YAC/E,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YAC/E,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YAC/E,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC;SACvB;QAED,MAAM,OAAO,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,6BAA6B,CAAC,CAAC;QACnI,OAAO,CAAC,IAAI,GAAG,mBAAmB,CAAC;QACnC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACzC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACzC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;IAClC,CAAC;CACJ;AA5SU;IADN,SAAS,EAAE;4DACuB;AAM5B;IADN,SAAS,EAAE;qDACmB;AAQxB;IADN,SAAS,EAAE;mDACiB;AAQtB;IADN,SAAS,EAAE;sDACsB;AAO3B;IADN,SAAS,EAAE;mDACc", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { Vector2, TmpVectors } from \"../../../Maths/math.vector\";\r\nimport type { Camera } from \"../../../Cameras/camera\";\r\nimport type { Effect } from \"../../../Materials/effect\";\r\nimport { Texture } from \"../../../Materials/Textures/texture\";\r\nimport { PostProcess } from \"../../../PostProcesses/postProcess\";\r\nimport { PostProcessRenderPipeline } from \"../../../PostProcesses/RenderPipeline/postProcessRenderPipeline\";\r\nimport { PostProcessRenderEffect } from \"../../../PostProcesses/RenderPipeline/postProcessRenderEffect\";\r\nimport { PassPostProcess } from \"../../../PostProcesses/passPostProcess\";\r\nimport { BlurPostProcess } from \"../../../PostProcesses/blurPostProcess\";\r\nimport { Constants } from \"../../../Engines/constants\";\r\nimport { serialize } from \"../../../Misc/decorators\";\r\nimport type { Scene } from \"../../../scene\";\r\nimport { RawTexture } from \"../../../Materials/Textures/rawTexture\";\r\nimport { Scalar } from \"../../../Maths/math.scalar\";\r\n\r\nimport \"../../../PostProcesses/RenderPipeline/postProcessRenderPipelineManagerSceneComponent\";\r\n\r\nimport \"../../../Shaders/ssao.fragment\";\r\nimport \"../../../Shaders/ssaoCombine.fragment\";\r\n\r\n/**\r\n * Render pipeline to produce ssao effect\r\n */\r\nexport class SSAORenderingPipeline extends PostProcessRenderPipeline {\r\n    // Members\r\n\r\n    /**\r\n     * @ignore\r\n     * The PassPostProcess id in the pipeline that contains the original scene color\r\n     */\r\n    public SSAOOriginalSceneColorEffect: string = \"SSAOOriginalSceneColorEffect\";\r\n    /**\r\n     * @ignore\r\n     * The SSAO PostProcess id in the pipeline\r\n     */\r\n    public SSAORenderEffect: string = \"SSAORenderEffect\";\r\n    /**\r\n     * @ignore\r\n     * The horizontal blur PostProcess id in the pipeline\r\n     */\r\n    public SSAOBlurHRenderEffect: string = \"SSAOBlurHRenderEffect\";\r\n    /**\r\n     * @ignore\r\n     * The vertical blur PostProcess id in the pipeline\r\n     */\r\n    public SSAOBlurVRenderEffect: string = \"SSAOBlurVRenderEffect\";\r\n    /**\r\n     * @ignore\r\n     * The PostProcess id in the pipeline that combines the SSAO-Blur output with the original scene color (SSAOOriginalSceneColorEffect)\r\n     */\r\n    public SSAOCombineRenderEffect: string = \"SSAOCombineRenderEffect\";\r\n\r\n    /**\r\n     * The output strength of the SSAO post-process. Default value is 1.0.\r\n     */\r\n    @serialize()\r\n    public totalStrength: number = 1.0;\r\n\r\n    /**\r\n     * The radius around the analyzed pixel used by the SSAO post-process. Default value is 0.0006\r\n     */\r\n    @serialize()\r\n    public radius: number = 0.0001;\r\n\r\n    /**\r\n     * Related to fallOff, used to interpolate SSAO samples (first interpolate function input) based on the occlusion difference of each pixel\r\n     * Must not be equal to fallOff and superior to fallOff.\r\n     * Default value is 0.0075\r\n     */\r\n    @serialize()\r\n    public area: number = 0.0075;\r\n\r\n    /**\r\n     * Related to area, used to interpolate SSAO samples (second interpolate function input) based on the occlusion difference of each pixel\r\n     * Must not be equal to area and inferior to area.\r\n     * Default value is 0.000001\r\n     */\r\n    @serialize()\r\n    public fallOff: number = 0.000001;\r\n\r\n    /**\r\n     * The base color of the SSAO post-process\r\n     * The final result is \"base + ssao\" between [0, 1]\r\n     */\r\n    @serialize()\r\n    public base: number = 0.5;\r\n\r\n    private _scene: Scene;\r\n    private _randomTexture: Texture;\r\n\r\n    private _originalColorPostProcess: PassPostProcess;\r\n    private _ssaoPostProcess: PostProcess;\r\n    private _blurHPostProcess: BlurPostProcess;\r\n    private _blurVPostProcess: BlurPostProcess;\r\n    private _ssaoCombinePostProcess: PostProcess;\r\n\r\n    private _firstUpdate: boolean = true;\r\n\r\n    /**\r\n     * Gets active scene\r\n     */\r\n    public get scene(): Scene {\r\n        return this._scene;\r\n    }\r\n\r\n    /**\r\n     * @constructor\r\n     * @param name - The rendering pipeline name\r\n     * @param scene - The scene linked to this pipeline\r\n     * @param ratio - The size of the postprocesses. Can be a number shared between passes or an object for more precision: { ssaoRatio: 0.5, combineRatio: 1.0 }\r\n     * @param cameras - The array of cameras that the rendering pipeline will be attached to\r\n     */\r\n    constructor(name: string, scene: Scene, ratio: any, cameras?: Camera[]) {\r\n        super(scene.getEngine(), name);\r\n\r\n        this._scene = scene;\r\n\r\n        // Set up assets\r\n        this._createRandomTexture();\r\n\r\n        const ssaoRatio = ratio.ssaoRatio || ratio;\r\n        const combineRatio = ratio.combineRatio || ratio;\r\n\r\n        this._originalColorPostProcess = new PassPostProcess(\"SSAOOriginalSceneColor\", combineRatio, null, Texture.BILINEAR_SAMPLINGMODE, scene.getEngine(), false);\r\n        this._createSSAOPostProcess(ssaoRatio);\r\n        this._createBlurPostProcess(ssaoRatio);\r\n        this._createSSAOCombinePostProcess(combineRatio);\r\n\r\n        // Set up pipeline\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                this.SSAOOriginalSceneColorEffect,\r\n                () => {\r\n                    return this._originalColorPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                this.SSAORenderEffect,\r\n                () => {\r\n                    return this._ssaoPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                this.SSAOBlurHRenderEffect,\r\n                () => {\r\n                    return this._blurHPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                this.SSAOBlurVRenderEffect,\r\n                () => {\r\n                    return this._blurVPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                this.SSAOCombineRenderEffect,\r\n                () => {\r\n                    return this._ssaoCombinePostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n\r\n        // Finish\r\n        scene.postProcessRenderPipelineManager.addPipeline(this);\r\n        if (cameras) {\r\n            scene.postProcessRenderPipelineManager.attachCamerasToRenderPipeline(name, cameras);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _attachCameras(cameras: any, unique: boolean): void {\r\n        super._attachCameras(cameras, unique);\r\n\r\n        for (const camera of this._cameras) {\r\n            this._scene.enableDepthRenderer(camera).getDepthMap(); // Force depth renderer \"on\"\r\n        }\r\n    }\r\n\r\n    // Public Methods\r\n\r\n    /**\r\n     * Get the class name\r\n     * @returns \"SSAORenderingPipeline\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"SSAORenderingPipeline\";\r\n    }\r\n\r\n    /**\r\n     * Removes the internal pipeline assets and detaches the pipeline from the scene cameras\r\n     * @param disableDepthRender - If the depth renderer should be disabled on the scene\r\n     */\r\n    public dispose(disableDepthRender: boolean = false): void {\r\n        for (let i = 0; i < this._scene.cameras.length; i++) {\r\n            const camera = this._scene.cameras[i];\r\n\r\n            this._originalColorPostProcess.dispose(camera);\r\n            this._ssaoPostProcess.dispose(camera);\r\n            this._blurHPostProcess.dispose(camera);\r\n            this._blurVPostProcess.dispose(camera);\r\n            this._ssaoCombinePostProcess.dispose(camera);\r\n        }\r\n\r\n        this._randomTexture.dispose();\r\n\r\n        if (disableDepthRender) {\r\n            this._scene.disableDepthRenderer();\r\n        }\r\n\r\n        this._scene.postProcessRenderPipelineManager.detachCamerasFromRenderPipeline(this._name, this._scene.cameras);\r\n\r\n        super.dispose();\r\n    }\r\n\r\n    // Private Methods\r\n    private _createBlurPostProcess(ratio: number): void {\r\n        const size = 16;\r\n\r\n        this._blurHPostProcess = new BlurPostProcess(\r\n            \"BlurH\",\r\n            new Vector2(1, 0),\r\n            size,\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            this._scene.getEngine(),\r\n            false,\r\n            Constants.TEXTURETYPE_UNSIGNED_INT\r\n        );\r\n        this._blurVPostProcess = new BlurPostProcess(\r\n            \"BlurV\",\r\n            new Vector2(0, 1),\r\n            size,\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            this._scene.getEngine(),\r\n            false,\r\n            Constants.TEXTURETYPE_UNSIGNED_INT\r\n        );\r\n\r\n        this._blurHPostProcess.onActivateObservable.add(() => {\r\n            const dw = this._blurHPostProcess.width / this._scene.getEngine().getRenderWidth();\r\n            this._blurHPostProcess.kernel = size * dw;\r\n        });\r\n\r\n        this._blurVPostProcess.onActivateObservable.add(() => {\r\n            const dw = this._blurVPostProcess.height / this._scene.getEngine().getRenderHeight();\r\n            this._blurVPostProcess.kernel = size * dw;\r\n        });\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuild() {\r\n        this._firstUpdate = true;\r\n        super._rebuild();\r\n    }\r\n\r\n    private _createSSAOPostProcess(ratio: number): void {\r\n        const numSamples = 16;\r\n        const sampleSphere = [\r\n            0.5381, 0.1856, -0.4319, 0.1379, 0.2486, 0.443, 0.3371, 0.5679, -0.0057, -0.6999, -0.0451, -0.0019, 0.0689, -0.1598, -0.8547, 0.056, 0.0069, -0.1843, -0.0146, 0.1402,\r\n            0.0762, 0.01, -0.1924, -0.0344, -0.3577, -0.5301, -0.4358, -0.3169, 0.1063, 0.0158, 0.0103, -0.5869, 0.0046, -0.0897, -0.494, 0.3287, 0.7119, -0.0154, -0.0918, -0.0533,\r\n            0.0596, -0.5411, 0.0352, -0.0631, 0.546, -0.4776, 0.2847, -0.0271,\r\n        ];\r\n        const samplesFactor = 1.0 / numSamples;\r\n\r\n        this._ssaoPostProcess = new PostProcess(\r\n            \"ssao\",\r\n            \"ssao\",\r\n            [\"sampleSphere\", \"samplesFactor\", \"randTextureTiles\", \"totalStrength\", \"radius\", \"area\", \"fallOff\", \"base\", \"range\", \"viewport\"],\r\n            [\"randomSampler\"],\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            this._scene.getEngine(),\r\n            false,\r\n            \"#define SAMPLES \" + numSamples + \"\\n#define SSAO\"\r\n        );\r\n\r\n        this._ssaoPostProcess.externalTextureSamplerBinding = true;\r\n        this._ssaoPostProcess.onApply = (effect: Effect) => {\r\n            if (this._firstUpdate) {\r\n                effect.setArray3(\"sampleSphere\", sampleSphere);\r\n                effect.setFloat(\"samplesFactor\", samplesFactor);\r\n                effect.setFloat(\"randTextureTiles\", 4.0);\r\n            }\r\n\r\n            effect.setFloat(\"totalStrength\", this.totalStrength);\r\n            effect.setFloat(\"radius\", this.radius);\r\n            effect.setFloat(\"area\", this.area);\r\n            effect.setFloat(\"fallOff\", this.fallOff);\r\n            effect.setFloat(\"base\", this.base);\r\n\r\n            effect.setTexture(\"textureSampler\", this._scene.enableDepthRenderer(this._scene.activeCamera).getDepthMap());\r\n            effect.setTexture(\"randomSampler\", this._randomTexture);\r\n        };\r\n    }\r\n\r\n    private _createSSAOCombinePostProcess(ratio: number): void {\r\n        this._ssaoCombinePostProcess = new PostProcess(\r\n            \"ssaoCombine\",\r\n            \"ssaoCombine\",\r\n            [],\r\n            [\"originalColor\", \"viewport\"],\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            this._scene.getEngine(),\r\n            false\r\n        );\r\n\r\n        this._ssaoCombinePostProcess.onApply = (effect: Effect) => {\r\n            effect.setVector4(\"viewport\", TmpVectors.Vector4[0].copyFromFloats(0, 0, 1.0, 1.0));\r\n            effect.setTextureFromPostProcess(\"originalColor\", this._originalColorPostProcess);\r\n        };\r\n    }\r\n\r\n    private _createRandomTexture(): void {\r\n        const size = 512;\r\n\r\n        const data = new Uint8Array(size * size * 4);\r\n        for (let index = 0; index < data.length; ) {\r\n            data[index++] = Math.floor(Math.max(0.0, Scalar.RandomRange(-1.0, 1.0)) * 255);\r\n            data[index++] = Math.floor(Math.max(0.0, Scalar.RandomRange(-1.0, 1.0)) * 255);\r\n            data[index++] = Math.floor(Math.max(0.0, Scalar.RandomRange(-1.0, 1.0)) * 255);\r\n            data[index++] = 255;\r\n        }\r\n\r\n        const texture = RawTexture.CreateRGBATexture(data, size, size, this._scene, false, false, Constants.TEXTURE_BILINEAR_SAMPLINGMODE);\r\n        texture.name = \"SSAORandomTexture\";\r\n        texture.wrapU = Texture.WRAP_ADDRESSMODE;\r\n        texture.wrapV = Texture.WRAP_ADDRESSMODE;\r\n        this._randomTexture = texture;\r\n    }\r\n}\r\n"]}