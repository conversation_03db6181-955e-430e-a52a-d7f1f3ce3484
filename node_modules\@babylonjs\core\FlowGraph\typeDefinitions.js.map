{"version": 3, "file": "typeDefinitions.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/FlowGraph/typeDefinitions.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { FlowGraphConnectionType } from \"./flowGraphConnection\";\r\n\r\n/**\r\n * An accessor that allows modifying properties on some other object.\r\n */\r\nexport interface IObjectAccessor {\r\n    /**\r\n     * The type of the object that is converted\r\n     */\r\n    type: string;\r\n    /**\r\n     * Get a property value from the object.\r\n     * @param args any necessary arguments to get the property value\r\n     */\r\n    get(...args: any[]): any;\r\n    /**\r\n     * Set a property value on the object.\r\n     * @param value the value to set\r\n     * @param args any necessary arguments to set the property value\r\n     */\r\n    set(value: any, ...args: any[]): void;\r\n    /**\r\n     * Get the original object\r\n     * @param args any necessary arguments to get the original object\r\n     */\r\n    getObject(...args: any[]): any;\r\n}\r\n\r\n/**\r\n * A Serialized Flow Graph Context\r\n */\r\nexport interface ISerializedFlowGraphContext {\r\n    /**\r\n     * The unique id of the context\r\n     */\r\n    uniqueId: string;\r\n    /**\r\n     * User variables\r\n     */\r\n    _userVariables: { [key: string]: any };\r\n    /**\r\n     * Values of the connection points\r\n     */\r\n    _connectionValues: { [key: string]: any };\r\n}\r\n\r\n/**\r\n * A Serialized Flow Graph Connection\r\n */\r\nexport interface ISerializedFlowGraphConnection {\r\n    /**\r\n     * The unique id of the connection\r\n     */\r\n    uniqueId: string;\r\n    /**\r\n     * The name of the connection\r\n     */\r\n    name: string;\r\n    /**\r\n     * The type of the connection\r\n     */\r\n    _connectionType: FlowGraphConnectionType;\r\n    /**\r\n     * The id of the connection that this is connected to\r\n     */\r\n    connectedPointIds: string[];\r\n}\r\n\r\n/**\r\n * A Serialized Flow Graph Block\r\n */\r\nexport interface ISerializedFlowGraphBlock {\r\n    /**\r\n     * The class name of the block\r\n     */\r\n    className: string;\r\n    /**\r\n     * Configuration parameters for the block\r\n     */\r\n    config: any;\r\n    /**\r\n     * The unique id of the block\r\n     */\r\n    uniqueId: string;\r\n    /**\r\n     * Input connection data\r\n     */\r\n    dataInputs: ISerializedFlowGraphConnection[];\r\n    /**\r\n     * Output connection data\r\n     */\r\n    dataOutputs: ISerializedFlowGraphConnection[];\r\n    /**\r\n     * Metadata for the block\r\n     */\r\n    metadata: any;\r\n    /**\r\n     * Input connection signal\r\n     */\r\n    signalInputs: ISerializedFlowGraphConnection[];\r\n    /**\r\n     * Output connection signal\r\n     */\r\n    signalOutputs: ISerializedFlowGraphConnection[];\r\n}\r\n\r\n/**\r\n * A Serialized Flow Graph\r\n */\r\nexport interface ISerializedFlowGraph {\r\n    /**\r\n     * Contexts belonging to the flow graph\r\n     */\r\n    executionContexts: ISerializedFlowGraphContext[];\r\n    /**\r\n     * Blocks belonging to the flow graph\r\n     */\r\n    allBlocks: ISerializedFlowGraphBlock[];\r\n}\r\n"]}