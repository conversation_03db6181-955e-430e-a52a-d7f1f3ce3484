{"version": 3, "file": "WebXRWalkingLocomotion.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRWalkingLocomotion.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAEvE,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAGnD,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAEjF,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,MAAM,YAAY;IAId,YAAY,UAAkB,EAAE,WAA2B;QAHnD,aAAQ,GAAmB,EAAE,CAAC;QAC9B,SAAI,GAAW,CAAC,CAAC;QAGrB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,UAAU,EAAE,EAAE,GAAG,EAAE;YACvC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;SACpE;IACL,CAAC;IAED,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IAChC,CAAC;IAEM,IAAI,CAAC,CAAS,EAAE,CAAS;QAC5B,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC1E,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpC,CAAC;IAEM,EAAE,CAAC,GAAW;QACjB,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;SAC1C;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACnE,CAAC;CACJ;AASD,MAAM,iBAAiB;IAAvB;QACY,aAAQ,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC,CAAC;QAChC,aAAQ,GAAG,CAAC,CAAC;QAEd,wBAAmB,GAA8B,IAAI,UAAU,EAAiB,CAAC;IAoH5F,CAAC;IAlHU,MAAM,CAAC,IAAY,EAAE,IAAY,EAAE,QAAgB,EAAE,QAAgB;QACxE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAEnC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB,CAAC;QAC1C,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,EAAE;YACxC,OAAO;SACV;QAED,IAAI,YAAY,CAAC;QACjB,KAAK,YAAY,GAAG,IAAI,CAAC,uBAAuB,EAAE,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,YAAY,EAAE;YACnG,IAAI,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,kCAAkC,EAAE;gBAC3G,MAAM;aACT;SACJ;QAED,IAAI,YAAY,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACvC,OAAO;SACV;QAED,IAAI,eAAe,GAAG,CAAC,CAAC,CAAC;QACzB,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,KAAK,IAAI,WAAW,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,YAAY,EAAE,EAAE,GAAG,EAAE;YACtD,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACrE,IAAI,WAAW,GAAG,eAAe,EAAE;gBAC/B,OAAO,GAAG,GAAG,CAAC;gBACd,eAAe,GAAG,WAAW,CAAC;aACjC;SACJ;QAED,IAAI,eAAe,GAAG,IAAI,CAAC,6BAA6B,EAAE;YACtD,OAAO;SACV;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACnC,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,GAAG,CAAC;QACR,IAAI,MAAM,CAAC;QACX,IAAI,6BAA6B,GAAG,CAAC,CAAC;QACtC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,YAAY,EAAE,EAAE,GAAG,EAAE;YACzC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YAC/B,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YAClC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAC7B,6BAA6B,IAAI,GAAG,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;SACpE;QAED,IAAI,6BAA6B,GAAG,YAAY,GAAG,IAAI,CAAC,mCAAmC,EAAE;YACzF,OAAO;SACV;QAED,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;QACtC,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5D,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAChC,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACjC,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;YAC9D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC;SAClE;aAAM;YACH,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;YAC9D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC;SAClE;QACD,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC;YACrC,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,SAAS;YACpB,eAAe,EAAE,MAAM;YACvB,oBAAoB,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;SACtD,CAAC,CAAC;IACP,CAAC;IAEM,KAAK;QACR,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE;YACjD,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAC9C;IACL,CAAC;IAED,IAAY,uBAAuB;QAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,IAAY,kCAAkC;QAC1C,OAAO,IAAI,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,IAAY,6BAA6B;QACrC,OAAO,IAAI,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,IAAY,mCAAmC;QAC3C,OAAO,IAAI,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,IAAY,uBAAuB;QAC/B,OAAO,GAAG,CAAC;IACf,CAAC;IAED,IAAY,uBAAuB;QAC/B,OAAO,CAAC,GAAG,CAAC;IAChB,CAAC;IAED,IAAY,mBAAmB;QAC3B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAY,iBAAiB;QACzB,OAAO,GAAG,CAAC;IACf,CAAC;CACJ;AAED,MAAM,cAAc;IAgBhB,YAAY,QAAiB,EAAE,SAAkB,EAAE,eAAwB,EAAE,oBAAsC;QAf3G,cAAS,GAAG,IAAI,OAAO,EAAE,CAAC;QAC1B,eAAU,GAAG,IAAI,OAAO,EAAE,CAAC;QAC3B,qBAAgB,GAAG,IAAI,OAAO,EAAE,CAAC;QACjC,UAAK,GAAG,IAAI,OAAO,EAAE,CAAC;QACtB,gBAAW,GAAG,CAAC,CAAC,CAAC;QACjB,aAAQ,GAAG,IAAI,OAAO,EAAE,CAAC;QACzB,kBAAa,GAAG,KAAK,CAAC;QACtB,OAAE,GAAG,CAAC,CAAC,CAAC;QACR,UAAK,GAAG,CAAC,CAAC,CAAC;QACX,kBAAa,GAAG,IAAI,OAAO,EAAE,CAAC;QAC9B,cAAS,GAAG,CAAC,CAAC;QAEf,eAAU,GAAG,IAAI,UAAU,EAAsB,CAAC;QAClD,eAAU,GAAG,IAAI,UAAU,EAA8B,CAAC;QAG7D,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,EAAE,eAAe,EAAE,oBAAoB,KAAK,MAAM,CAAC,CAAC;IACvF,CAAC;IAEO,MAAM,CAAC,QAAiB,EAAE,SAAkB,EAAE,eAAwB,EAAE,YAAqB;QACjG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACpC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SAC7D;aAAM;YACH,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SAC7D;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QACvC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;QAEjD,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC;QACrB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QAE7C,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;IACvB,CAAC;IAEO,mBAAmB,CAAC,CAAS,EAAE,CAAS;QAC5C,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3C,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAC1D;aAAM;YACH,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACzD;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;QACvB,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;QACtD,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;QAEpH,6BAA6B;QAC7B,IAAI,CAAC,SAAS,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,MAAM,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;IACzG,CAAC;IAEM,MAAM,CAAC,CAAS,EAAE,CAAS;QAC9B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,EAAE;YAC1C,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;QACvB,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE/B,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE;YACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAC3C;QAED,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,EAAE;YAC1C,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;YAClB,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC;YAE9D,IAAI,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,EAAE,IAAI,GAAG,EAAE;gBAChC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;aACpF;SACJ;QAED,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE;YAC7B,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aAC/C;iBAAM;gBACH,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aAChD;YACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAC5F;QAED,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,EAAE;YACzB,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAY,kBAAkB;QAC1B,OAAO,GAAG,CAAC;IACf,CAAC;IAED,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;CACJ;AAED,MAAM,MAAM;IAOA,MAAM,KAAK,sBAAsB;QACrC,SAAS;QACT,OAAO,IAAI,GAAG,EAAE,CAAC;IACrB,CAAC;IAID,YAAY,MAAc;QAZlB,cAAS,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACpC,YAAO,GAA6B,IAAI,CAAC;QACzC,cAAS,GAAG,IAAI,OAAO,EAAE,CAAC;QAC1B,iCAA4B,GAAW,MAAM,CAAC,sBAAsB,CAAC;QAOtE,sBAAiB,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAG/C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,oBAAoB,CAAC,CAAC;gBACtH,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE;oBAC7B,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;oBAClC,IAAI,CAAC,OAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBACjF,CAAC,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,MAAM,CAAC,QAAiB,EAAE,OAAgB;QAC7C,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,OAAO,CAAC,SAAS,EAAE,CAAC;QAEpB,4BAA4B;QAC5B,IAAI,CAAC,4BAA4B,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;QACjE,IAAI,IAAI,CAAC,4BAA4B,IAAI,MAAM,CAAC,sBAAsB,EAAE;YACpE,IAAI,CAAC,4BAA4B,IAAI,MAAM,CAAC,sBAAsB,CAAC;YACnE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;YACpE,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC5D,IAAI,CAAC,OAAO,EAAE;oBACV,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;iBACvB;aACJ;YACD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACtE,CAAC;CACJ;AAeD;;GAEG;AACH,MAAM,OAAO,sBAAuB,SAAQ,oBAAoB;IAC5D;;OAEG;IACI,MAAM,KAAK,IAAI;QAClB,OAAO,gBAAgB,CAAC,kBAAkB,CAAC;IAC/C,CAAC;IAED;;;;OAIG;IACI,MAAM,KAAK,OAAO;QACrB,OAAO,CAAC,CAAC;IACb,CAAC;IAYD;;;;;;;OAOG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;;;;;OAOG;IACH,IAAW,gBAAgB,CAAC,gBAA6C;QACrE,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,KAAK,aAAa,CAAC;IAClG,CAAC;IAED;;;;OAIG;IACH,YAAmB,cAAmC,EAAE,OAAuC;QAC3F,KAAK,CAAC,cAAc,CAAC,CAAC;QAxClB,QAAG,GAAY,IAAI,OAAO,EAAE,CAAC;QAC7B,aAAQ,GAAY,IAAI,OAAO,EAAE,CAAC;QAClC,cAAS,GAAY,IAAI,OAAO,EAAE,CAAC;QACnC,cAAS,GAAY,IAAI,OAAO,EAAE,CAAC;QAsCvC,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACjD,IAAI,IAAI,CAAC,8BAA8B,EAAE;YACrC,MAAM,CAAC,IAAI,CACP,8JAA8J,CACjK,CAAC;SACL;IACL,CAAC;IAED;;;;OAIG;IACI,YAAY;QACf,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,KAAK,cAAc,CAAC;IACjH,CAAC;IAED;;;;OAIG;IACI,MAAM;QACT,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACvC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,UAAU,CAAC,KAAc;QAC/B,MAAM,IAAI,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;QAC1E,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAExF,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAChC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,gBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,gBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEtE,4BAA4B;QAC5B,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,OAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAQ,CAAC,iBAAiB,CAAC,CAAC;QACzD,IAAI,CAAC,IAAI,CAAC,8BAA8B,EAAE;YACtC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SACxG;QACD,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9D,CAAC;CACJ;AAED,qBAAqB;AACrB,oBAAoB,CAAC,eAAe,CAChC,sBAAsB,CAAC,IAAI,EAC3B,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,sBAAsB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AACvE,CAAC,EACD,sBAAsB,CAAC,OAAO,EAC9B,KAAK,CACR,CAAC", "sourcesContent": ["import type { Engine } from \"../../Engines/engine\";\r\nimport { TmpVectors, Vector2, Vector3 } from \"../../Maths/math.vector\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { WebXRCamera } from \"../webXRCamera\";\r\nimport { WebXRFeatureName, WebXRFeaturesManager } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\n\r\nclass CircleBuffer {\r\n    private _samples: Array<Vector2> = [];\r\n    private _idx: number = 0;\r\n\r\n    constructor(numSamples: number, initializer?: () => Vector2) {\r\n        for (let idx = 0; idx < numSamples; ++idx) {\r\n            this._samples.push(initializer ? initializer() : Vector2.Zero());\r\n        }\r\n    }\r\n\r\n    public get length() {\r\n        return this._samples.length;\r\n    }\r\n\r\n    public push(x: number, y: number) {\r\n        this._idx = (this._idx + this._samples.length - 1) % this._samples.length;\r\n        this.at(0).copyFromFloats(x, y);\r\n    }\r\n\r\n    public at(idx: number) {\r\n        if (idx >= this._samples.length) {\r\n            throw new Error(\"Index out of bounds\");\r\n        }\r\n        return this._samples[(this._idx + idx) % this._samples.length];\r\n    }\r\n}\r\n\r\ninterface IDetectedStep {\r\n    leftApex: Vector2;\r\n    rightApex: Vector2;\r\n    currentPosition: Vector2;\r\n    currentStepDirection: \"left\" | \"right\";\r\n}\r\n\r\nclass FirstStepDetector {\r\n    private _samples = new CircleBuffer(20);\r\n    private _entropy = 0;\r\n\r\n    public onFirstStepDetected: Observable<IDetectedStep> = new Observable<IDetectedStep>();\r\n\r\n    public update(posX: number, posY: number, forwardX: number, forwardY: number) {\r\n        this._samples.push(posX, posY);\r\n        const origin = this._samples.at(0);\r\n\r\n        this._entropy *= this._entropyDecayFactor;\r\n        this._entropy += Vector2.Distance(origin, this._samples.at(1));\r\n        if (this._entropy > this._entropyThreshold) {\r\n            return;\r\n        }\r\n\r\n        let samePointIdx;\r\n        for (samePointIdx = this._samePointCheckStartIdx; samePointIdx < this._samples.length; ++samePointIdx) {\r\n            if (Vector2.DistanceSquared(origin, this._samples.at(samePointIdx)) < this._samePointSquaredDistanceThreshold) {\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (samePointIdx === this._samples.length) {\r\n            return;\r\n        }\r\n\r\n        let apexDistSquared = -1;\r\n        let apexIdx = 0;\r\n        for (let distSquared, idx = 1; idx < samePointIdx; ++idx) {\r\n            distSquared = Vector2.DistanceSquared(origin, this._samples.at(idx));\r\n            if (distSquared > apexDistSquared) {\r\n                apexIdx = idx;\r\n                apexDistSquared = distSquared;\r\n            }\r\n        }\r\n\r\n        if (apexDistSquared < this._apexSquaredDistanceThreshold) {\r\n            return;\r\n        }\r\n\r\n        const apex = this._samples.at(apexIdx);\r\n        const axis = apex.subtract(origin);\r\n        axis.normalize();\r\n\r\n        const vec = TmpVectors.Vector2[0];\r\n        let dot;\r\n        let sample;\r\n        let sumSquaredProjectionDistances = 0;\r\n        for (let idx = 1; idx < samePointIdx; ++idx) {\r\n            sample = this._samples.at(idx);\r\n            sample.subtractToRef(origin, vec);\r\n            dot = Vector2.Dot(axis, vec);\r\n            sumSquaredProjectionDistances += vec.lengthSquared() - dot * dot;\r\n        }\r\n\r\n        if (sumSquaredProjectionDistances > samePointIdx * this._squaredProjectionDistanceThreshold) {\r\n            return;\r\n        }\r\n\r\n        const forwardVec = TmpVectors.Vector3[0];\r\n        forwardVec.set(forwardX, forwardY, 0);\r\n        const axisVec = TmpVectors.Vector3[1];\r\n        axisVec.set(axis.x, axis.y, 0);\r\n        const isApexLeft = Vector3.Cross(forwardVec, axisVec).z > 0;\r\n        const leftApex = origin.clone();\r\n        const rightApex = origin.clone();\r\n        apex.subtractToRef(origin, axis);\r\n        if (isApexLeft) {\r\n            axis.scaleAndAddToRef(this._axisToApexShrinkFactor, leftApex);\r\n            axis.scaleAndAddToRef(this._axisToApexExtendFactor, rightApex);\r\n        } else {\r\n            axis.scaleAndAddToRef(this._axisToApexExtendFactor, leftApex);\r\n            axis.scaleAndAddToRef(this._axisToApexShrinkFactor, rightApex);\r\n        }\r\n        this.onFirstStepDetected.notifyObservers({\r\n            leftApex: leftApex,\r\n            rightApex: rightApex,\r\n            currentPosition: origin,\r\n            currentStepDirection: isApexLeft ? \"right\" : \"left\",\r\n        });\r\n    }\r\n\r\n    public reset() {\r\n        for (let idx = 0; idx < this._samples.length; ++idx) {\r\n            this._samples.at(idx).copyFromFloats(0, 0);\r\n        }\r\n    }\r\n\r\n    private get _samePointCheckStartIdx() {\r\n        return Math.floor(this._samples.length / 3);\r\n    }\r\n\r\n    private get _samePointSquaredDistanceThreshold() {\r\n        return 0.03 * 0.03;\r\n    }\r\n\r\n    private get _apexSquaredDistanceThreshold() {\r\n        return 0.09 * 0.09;\r\n    }\r\n\r\n    private get _squaredProjectionDistanceThreshold() {\r\n        return 0.03 * 0.03;\r\n    }\r\n\r\n    private get _axisToApexShrinkFactor() {\r\n        return 0.8;\r\n    }\r\n\r\n    private get _axisToApexExtendFactor() {\r\n        return -1.6;\r\n    }\r\n\r\n    private get _entropyDecayFactor() {\r\n        return 0.93;\r\n    }\r\n\r\n    private get _entropyThreshold() {\r\n        return 0.4;\r\n    }\r\n}\r\n\r\nclass WalkingTracker {\r\n    private _leftApex = new Vector2();\r\n    private _rightApex = new Vector2();\r\n    private _currentPosition = new Vector2();\r\n    private _axis = new Vector2();\r\n    private _axisLength = -1;\r\n    private _forward = new Vector2();\r\n    private _steppingLeft = false;\r\n    private _t = -1;\r\n    private _maxT = -1;\r\n    private _maxTPosition = new Vector2();\r\n    private _vitality = 0;\r\n\r\n    public onMovement = new Observable<{ deltaT: number }>();\r\n    public onFootfall = new Observable<{ foot: \"left\" | \"right\" }>();\r\n\r\n    constructor(leftApex: Vector2, rightApex: Vector2, currentPosition: Vector2, currentStepDirection: \"left\" | \"right\") {\r\n        this._reset(leftApex, rightApex, currentPosition, currentStepDirection === \"left\");\r\n    }\r\n\r\n    private _reset(leftApex: Vector2, rightApex: Vector2, currentPosition: Vector2, steppingLeft: boolean) {\r\n        this._leftApex.copyFrom(leftApex);\r\n        this._rightApex.copyFrom(rightApex);\r\n        this._steppingLeft = steppingLeft;\r\n\r\n        if (this._steppingLeft) {\r\n            this._leftApex.subtractToRef(this._rightApex, this._axis);\r\n            this._forward.copyFromFloats(-this._axis.y, this._axis.x);\r\n        } else {\r\n            this._rightApex.subtractToRef(this._leftApex, this._axis);\r\n            this._forward.copyFromFloats(this._axis.y, -this._axis.x);\r\n        }\r\n        this._axisLength = this._axis.length();\r\n        this._forward.scaleInPlace(1 / this._axisLength);\r\n\r\n        this._updateTAndVitality(currentPosition.x, currentPosition.y);\r\n        this._maxT = this._t;\r\n        this._maxTPosition.copyFrom(currentPosition);\r\n\r\n        this._vitality = 1;\r\n    }\r\n\r\n    private _updateTAndVitality(x: number, y: number) {\r\n        this._currentPosition.copyFromFloats(x, y);\r\n\r\n        if (this._steppingLeft) {\r\n            this._currentPosition.subtractInPlace(this._rightApex);\r\n        } else {\r\n            this._currentPosition.subtractInPlace(this._leftApex);\r\n        }\r\n        const priorT = this._t;\r\n        const dot = Vector2.Dot(this._currentPosition, this._axis);\r\n        this._t = dot / (this._axisLength * this._axisLength);\r\n        const projDistSquared = this._currentPosition.lengthSquared() - (dot / this._axisLength) * (dot / this._axisLength);\r\n\r\n        // TODO: Extricate the magic.\r\n        this._vitality *= 0.92 - 100 * Math.max(projDistSquared - 0.0016, 0) + Math.max(this._t - priorT, 0);\r\n    }\r\n\r\n    public update(x: number, y: number) {\r\n        if (this._vitality < this._vitalityThreshold) {\r\n            return false;\r\n        }\r\n\r\n        const priorT = this._t;\r\n        this._updateTAndVitality(x, y);\r\n\r\n        if (this._t > this._maxT) {\r\n            this._maxT = this._t;\r\n            this._maxTPosition.copyFromFloats(x, y);\r\n        }\r\n\r\n        if (this._vitality < this._vitalityThreshold) {\r\n            return false;\r\n        }\r\n\r\n        if (this._t > priorT) {\r\n            this.onMovement.notifyObservers({ deltaT: this._t - priorT });\r\n\r\n            if (priorT < 0.5 && this._t >= 0.5) {\r\n                this.onFootfall.notifyObservers({ foot: this._steppingLeft ? \"left\" : \"right\" });\r\n            }\r\n        }\r\n\r\n        if (this._t < 0.95 * this._maxT) {\r\n            this._currentPosition.copyFromFloats(x, y);\r\n            if (this._steppingLeft) {\r\n                this._leftApex.copyFrom(this._maxTPosition);\r\n            } else {\r\n                this._rightApex.copyFrom(this._maxTPosition);\r\n            }\r\n            this._reset(this._leftApex, this._rightApex, this._currentPosition, !this._steppingLeft);\r\n        }\r\n\r\n        if (this._axisLength < 0.03) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    private get _vitalityThreshold() {\r\n        return 0.1;\r\n    }\r\n\r\n    get forward() {\r\n        return this._forward;\r\n    }\r\n}\r\n\r\nclass Walker {\r\n    private _engine: Engine;\r\n    private _detector = new FirstStepDetector();\r\n    private _walker: Nullable<WalkingTracker> = null;\r\n    private _movement = new Vector2();\r\n    private _millisecondsSinceLastUpdate: number = Walker._MillisecondsPerUpdate;\r\n\r\n    private static get _MillisecondsPerUpdate(): number {\r\n        // 15 FPS\r\n        return 1000 / 15;\r\n    }\r\n\r\n    public movementThisFrame: Vector3 = Vector3.Zero();\r\n\r\n    constructor(engine: Engine) {\r\n        this._engine = engine;\r\n        this._detector.onFirstStepDetected.add((event) => {\r\n            if (!this._walker) {\r\n                this._walker = new WalkingTracker(event.leftApex, event.rightApex, event.currentPosition, event.currentStepDirection);\r\n                this._walker.onFootfall.add(() => {\r\n                    Logger.Log(\"Footfall!\");\r\n                });\r\n                this._walker.onMovement.add((event) => {\r\n                    this._walker!.forward.scaleAndAddToRef(0.024 * event.deltaT, this._movement);\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    public update(position: Vector3, forward: Vector3) {\r\n        forward.y = 0;\r\n        forward.normalize();\r\n\r\n        // Enforce reduced framerate\r\n        this._millisecondsSinceLastUpdate += this._engine.getDeltaTime();\r\n        if (this._millisecondsSinceLastUpdate >= Walker._MillisecondsPerUpdate) {\r\n            this._millisecondsSinceLastUpdate -= Walker._MillisecondsPerUpdate;\r\n            this._detector.update(position.x, position.z, forward.x, forward.z);\r\n            if (this._walker) {\r\n                const updated = this._walker.update(position.x, position.z);\r\n                if (!updated) {\r\n                    this._walker = null;\r\n                }\r\n            }\r\n            this._movement.scaleInPlace(0.85);\r\n        }\r\n\r\n        this.movementThisFrame.set(this._movement.x, 0, this._movement.y);\r\n    }\r\n}\r\n\r\n/**\r\n * Options for the walking locomotion feature.\r\n */\r\nexport interface IWebXRWalkingLocomotionOptions {\r\n    /**\r\n     * The target to be moved by walking locomotion. This should be the transform node\r\n     * which is the root of the XR space (i.e., the WebXRCamera's parent node). However,\r\n     * for simple cases and legacy purposes, articulating the WebXRCamera itself is also\r\n     * supported as a deprecated feature.\r\n     */\r\n    locomotionTarget: WebXRCamera | TransformNode;\r\n}\r\n\r\n/**\r\n * A module that will enable VR locomotion by detecting when the user walks in place.\r\n */\r\nexport class WebXRWalkingLocomotion extends WebXRAbstractFeature {\r\n    /**\r\n     * The module's name.\r\n     */\r\n    public static get Name(): string {\r\n        return WebXRFeatureName.WALKING_LOCOMOTION;\r\n    }\r\n\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number has no external basis.\r\n     */\r\n    public static get Version(): number {\r\n        return 1;\r\n    }\r\n\r\n    private _sessionManager: WebXRSessionManager;\r\n    private _up: Vector3 = new Vector3();\r\n    private _forward: Vector3 = new Vector3();\r\n    private _position: Vector3 = new Vector3();\r\n    private _movement: Vector3 = new Vector3();\r\n    private _walker: Nullable<Walker>;\r\n\r\n    private _locomotionTarget: WebXRCamera | TransformNode;\r\n    private _isLocomotionTargetWebXRCamera: boolean;\r\n\r\n    /**\r\n     * The target to be articulated by walking locomotion.\r\n     * When the walking locomotion feature detects walking in place, this element's\r\n     * X and Z coordinates will be modified to reflect locomotion. This target should\r\n     * be either the XR space's origin (i.e., the parent node of the WebXRCamera) or\r\n     * the WebXRCamera itself. Note that the WebXRCamera path will modify the position\r\n     * of the WebXRCamera directly and is thus discouraged.\r\n     */\r\n    public get locomotionTarget(): WebXRCamera | TransformNode {\r\n        return this._locomotionTarget;\r\n    }\r\n\r\n    /**\r\n     * The target to be articulated by walking locomotion.\r\n     * When the walking locomotion feature detects walking in place, this element's\r\n     * X and Z coordinates will be modified to reflect locomotion. This target should\r\n     * be either the XR space's origin (i.e., the parent node of the WebXRCamera) or\r\n     * the WebXRCamera itself. Note that the WebXRCamera path will modify the position\r\n     * of the WebXRCamera directly and is thus discouraged.\r\n     */\r\n    public set locomotionTarget(locomotionTarget: WebXRCamera | TransformNode) {\r\n        this._locomotionTarget = locomotionTarget;\r\n        this._isLocomotionTargetWebXRCamera = this._locomotionTarget.getClassName() === \"WebXRCamera\";\r\n    }\r\n\r\n    /**\r\n     * Construct a new Walking Locomotion feature.\r\n     * @param sessionManager manager for the current XR session\r\n     * @param options creation options, prominently including the vector target for locomotion\r\n     */\r\n    public constructor(sessionManager: WebXRSessionManager, options: IWebXRWalkingLocomotionOptions) {\r\n        super(sessionManager);\r\n        this._sessionManager = sessionManager;\r\n        this.locomotionTarget = options.locomotionTarget;\r\n        if (this._isLocomotionTargetWebXRCamera) {\r\n            Logger.Warn(\r\n                \"Using walking locomotion directly on a WebXRCamera may have unintended interactions with other XR techniques. Using an XR space parent is highly recommended\"\r\n            );\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Checks whether this feature is compatible with the current WebXR session.\r\n     * Walking locomotion is only compatible with \"immersive-vr\" sessions.\r\n     * @returns true if compatible, false otherwise\r\n     */\r\n    public isCompatible(): boolean {\r\n        return this._sessionManager.sessionMode === undefined || this._sessionManager.sessionMode === \"immersive-vr\";\r\n    }\r\n\r\n    /**\r\n     * Attaches the feature.\r\n     * Typically called automatically by the features manager.\r\n     * @returns true if attach succeeded, false otherwise\r\n     */\r\n    public attach(): boolean {\r\n        if (!this.isCompatible || !super.attach()) {\r\n            return false;\r\n        }\r\n\r\n        this._walker = new Walker(this._sessionManager.scene.getEngine());\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Detaches the feature.\r\n     * Typically called automatically by the features manager.\r\n     * @returns true if detach succeeded, false otherwise\r\n     */\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n\r\n        this._walker = null;\r\n        return true;\r\n    }\r\n\r\n    protected _onXRFrame(frame: XRFrame): void {\r\n        const pose = frame.getViewerPose(this._sessionManager.baseReferenceSpace);\r\n        if (!pose) {\r\n            return;\r\n        }\r\n\r\n        const handednessScalar = this.locomotionTarget.getScene().useRightHandedSystem ? 1 : -1;\r\n\r\n        const m = pose.transform.matrix;\r\n        this._up.copyFromFloats(m[4], m[5], handednessScalar * m[6]);\r\n        this._forward.copyFromFloats(m[8], m[9], handednessScalar * m[10]);\r\n        this._position.copyFromFloats(m[12], m[13], handednessScalar * m[14]);\r\n\r\n        // Compute the nape position\r\n        this._forward.scaleAndAddToRef(0.05, this._position);\r\n        this._up.scaleAndAddToRef(-0.05, this._position);\r\n        this._walker!.update(this._position, this._forward);\r\n        this._movement.copyFrom(this._walker!.movementThisFrame);\r\n        if (!this._isLocomotionTargetWebXRCamera) {\r\n            Vector3.TransformNormalToRef(this._movement, this.locomotionTarget.getWorldMatrix(), this._movement);\r\n        }\r\n        this.locomotionTarget.position.addInPlace(this._movement);\r\n    }\r\n}\r\n\r\n//register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRWalkingLocomotion.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRWalkingLocomotion(xrSessionManager, options);\r\n    },\r\n    WebXRWalkingLocomotion.Version,\r\n    false\r\n);\r\n"]}