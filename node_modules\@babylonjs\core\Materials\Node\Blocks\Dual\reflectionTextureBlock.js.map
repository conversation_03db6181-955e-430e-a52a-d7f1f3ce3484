{"version": 3, "file": "reflectionTextureBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Dual/reflectionTextureBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAGhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,0BAA0B,EAAE,MAAM,8BAA8B,CAAC;AAE1E,OAAO,EAAE,MAAM,EAAE,mCAAyB;AAE1C;;GAEG;AACH,MAAM,OAAO,sBAAuB,SAAQ,0BAA0B;IACxD,kCAAkC;QACxC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC3B,IAAI,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC/D,MAAM,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;YAC/E,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;YAChC,IAAI,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC/D,MAAM,CAAC,KAAK,CAAC,qEAAqE,CAAC,CAAC;YACpF,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,UAAU;QAChB,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAE,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB,CAAC,MAAM,CAAC;QAC9I,IAAI,CAAC,cAAc,CAAC,eAAe,CAAE,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB,CAAC,MAAM,CAAC;IACvJ,CAAC;IAED;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAEZ,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,qCAAqC,CAAC,UAAU,EAAE,KAAK,EAAE,wBAAwB,CAAC,MAAM,CAAC,CAAC;QACzH,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,qCAAqC,CAAC,OAAO,EAAE,KAAK,EAAE,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAC3H,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,qCAAqC,CAAC,OAAO,EAAE,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,qEAAqE;QACjM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,MAAM,EAAE,KAAK,EAAE,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAElH,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,qCAAqC,CAAC,OAAO,EAAE,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9H,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,qCAAqC,CAAC,MAAM,EAAE,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAEnH,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC5G,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC7G,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACzG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACzG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACzG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAEzG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,CACtD,qCAAqC,CAAC,MAAM,GAAG,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,CAC/I,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,wBAAwB,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,EAAE,CAAC,IAAI;QACnH,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE;YAClC,IAAI,mBAAmB,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,wBAAwB,CAAC,cAAc,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5J,IAAI,CAAC,mBAAmB,EAAE;gBACtB,mBAAmB,GAAG,IAAI,UAAU,CAAC,gBAAgB,CAAC,CAAC;gBACvD,mBAAmB,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;aACjF;YACD,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC7D;IACL,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YAChE,OAAO,IAAI,CAAC;SACf;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACpD,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;SAC3D;QAED,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QAEpC,MAAM,WAAW,GAAG,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAE9D,KAAK,CAAC,iBAAiB,IAAI,QAAQ,WAAW,gBAAgB,IAAI,CAAC,WAAW,CAAC,sBAAsB,MAAM,CAAC;QAE5G,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,sCAAsC,CAAC,WAAW,CAAC,CAAC;QAEpF,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,qCAAqC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAErF,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAE/E,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,gCAAgC,EAAE,sBAAsB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { NodeMaterial } from \"../../nodeMaterial\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { ReflectionTextureBaseBlock } from \"./reflectionTextureBaseBlock\";\r\nimport type { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * Block used to read a reflection texture from a sampler\r\n */\r\nexport class ReflectionTextureBlock extends ReflectionTextureBaseBlock {\r\n    protected _onGenerateOnlyFragmentCodeChanged(): boolean {\r\n        if (this.position.isConnected) {\r\n            this.generateOnlyFragmentCode = !this.generateOnlyFragmentCode;\r\n            Logger.Error(\"The position input must not be connected to be able to switch!\");\r\n            return false;\r\n        }\r\n\r\n        if (this.worldPosition.isConnected) {\r\n            this.generateOnlyFragmentCode = !this.generateOnlyFragmentCode;\r\n            Logger.Error(\"The worldPosition input must not be connected to be able to switch!\");\r\n            return false;\r\n        }\r\n\r\n        this._setTarget();\r\n\r\n        return true;\r\n    }\r\n\r\n    protected _setTarget(): void {\r\n        super._setTarget();\r\n        this.getInputByName(\"position\")!.target = this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.Vertex;\r\n        this.getInputByName(\"worldPosition\")!.target = this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.Vertex;\r\n    }\r\n\r\n    /**\r\n     * Create a new ReflectionTextureBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"position\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false, NodeMaterialBlockTargets.Vertex);\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.Vector4, false, NodeMaterialBlockTargets.Vertex);\r\n        this.registerInput(\"worldNormal\", NodeMaterialBlockConnectionPointTypes.Vector4, false, NodeMaterialBlockTargets.Fragment); // Flagging as fragment as the normal can be changed by fragment code\r\n        this.registerInput(\"world\", NodeMaterialBlockConnectionPointTypes.Matrix, false, NodeMaterialBlockTargets.Vertex);\r\n\r\n        this.registerInput(\"cameraPosition\", NodeMaterialBlockConnectionPointTypes.Vector3, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"view\", NodeMaterialBlockConnectionPointTypes.Matrix, false, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\"rgb\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"rgba\", NodeMaterialBlockConnectionPointTypes.Color4, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"r\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"g\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"b\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"a\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._inputs[0].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"ReflectionTextureBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get position(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the world input component\r\n     */\r\n    public get world(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the camera (or eye) position component\r\n     */\r\n    public get cameraPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the view input component\r\n     */\r\n    public get view(): NodeMaterialConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgb output component\r\n     */\r\n    public get rgb(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgba output component\r\n     */\r\n    public get rgba(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the r output component\r\n     */\r\n    public get r(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the g output component\r\n     */\r\n    public get g(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the b output component\r\n     */\r\n    public get b(): NodeMaterialConnectionPoint {\r\n        return this._outputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the a output component\r\n     */\r\n    public get a(): NodeMaterialConnectionPoint {\r\n        return this._outputs[5];\r\n    }\r\n\r\n    public autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        super.autoConfigure(material);\r\n\r\n        if (!this.cameraPosition.isConnected) {\r\n            let cameraPositionInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.CameraPosition && additionalFilteringInfo(b));\r\n\r\n            if (!cameraPositionInput) {\r\n                cameraPositionInput = new InputBlock(\"cameraPosition\");\r\n                cameraPositionInput.setAsSystemValue(NodeMaterialSystemValues.CameraPosition);\r\n            }\r\n            cameraPositionInput.output.connectTo(this.cameraPosition);\r\n        }\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (!this.texture) {\r\n            state.compilationString += this.writeOutputs(state, \"vec4(0.)\");\r\n            return this;\r\n        }\r\n\r\n        if (state.target !== NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += this.handleVertexSide(state);\r\n            return this;\r\n        }\r\n\r\n        if (this.generateOnlyFragmentCode) {\r\n            state.compilationString += this.handleVertexSide(state);\r\n        }\r\n\r\n        this.handleFragmentSideInits(state);\r\n\r\n        const normalWUnit = state._getFreeVariableName(\"normalWUnit\");\r\n\r\n        state.compilationString += `vec4 ${normalWUnit} = normalize(${this.worldNormal.associatedVariableName});\\n`;\r\n\r\n        state.compilationString += this.handleFragmentSideCodeReflectionCoords(normalWUnit);\r\n\r\n        state.compilationString += this.handleFragmentSideCodeReflectionColor(undefined, \"\");\r\n\r\n        state.compilationString += this.writeOutputs(state, this._reflectionColorName);\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ReflectionTextureBlock\", ReflectionTextureBlock);\r\n"]}