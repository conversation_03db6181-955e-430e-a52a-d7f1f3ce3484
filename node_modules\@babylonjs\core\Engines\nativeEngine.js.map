{"version": 3, "file": "nativeEngine.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Engines/nativeEngine.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,eAAe,EAAE,qBAAqB,EAAE,MAAM,uCAAuC,CAAC;AAE/F,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAKxD,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,+BAA+B,EAAE,UAAU,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AAKlH,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,iBAAiB,EAAE,MAAM,gCAAgC,CAAC;AACnE,OAAO,EAAE,qBAAqB,EAAE,MAAM,yCAAyC,CAAC;AAOhF,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAE7D,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AACvE,OAAO,EAAE,yBAAyB,EAAE,MAAM,oCAAoC,CAAC;AAC/E,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AAEvE,OAAO,EACH,kBAAkB,EAClB,mBAAmB,EACnB,qBAAqB,EACrB,sBAAsB,EACtB,yBAAyB,EACzB,yBAAyB,EACzB,oBAAoB,EACpB,sBAAsB,EACtB,oBAAoB,GACvB,MAAM,wBAAwB,CAAC;AAIhC,MAAM,yBAAyB,GAAG,IAAI,UAAU,EAAW,CAAC;AAC5D,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE;IACvF,IAAI,QAAiB,CAAC;IACtB,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE;QACnC,GAAG,EAAE,GAAG,EAAE,CAAC,QAAQ;QACnB,GAAG,EAAE,CAAC,KAAc,EAAE,EAAE;YACpB,QAAQ,GAAG,KAAK,CAAC;YACjB,IAAI,QAAQ,EAAE;gBACV,yBAAyB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;aACvD;QACL,CAAC;KACJ,CAAC,CAAC;CACN;AAED;;;GAGG;AACH,MAAM,UAAU,wBAAwB;IACpC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC3B,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;YAChC,yBAAyB,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;SAC9E;aAAM;YACH,OAAO,CAAC,OAAO,CAAC,CAAC;SACpB;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAAO,QAAgB,EAAE,WAAiB;IAClF,CAAC,MAAM,wBAAwB,EAAE,CAAS,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC;AACxE,CAAC;AAED;;GAEG;AACH,MAAM,gBAAiB,SAAQ,UAAU;CAUxC;AAYD,gBAAgB;AAChB,MAAM,oBAAoB;IAKtB,YAAoC,OAAsB;QAAtB,YAAO,GAAP,OAAO,CAAe;QAHzC,aAAQ,GAAG,IAAI,KAAK,EAAc,CAAC;QAC5C,gCAA2B,GAAG,KAAK,CAAC;QAGxC,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC,uBAAuB,EAAE,CAAC;QAC7D,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC3D,CAAC;IAEM,iBAAiB;QACpB,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;IAC5C,CAAC;IAEM,eAAe;QAClB,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACnD;QAED,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;QACzC,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAEM,oBAAoB,CAAC,OAAmB;QAC3C,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;IAEM,wBAAwB,CAAC,UAAkB;QAC9C,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAChD,CAAC;IAEM,yBAAyB,CAAC,UAAuB;QACpD,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;IACrD,CAAC;IAEM,uBAAuB,CAAC,UAAkB;QAC7C,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IAC/C,CAAC;IAEM,wBAAwB,CAAC,UAAsB;QAClD,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;IACpD,CAAC;IAEM,yBAAyB,CAAC,UAAkB;QAC/C,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IAEM,0BAA0B,CAAC,UAAqC;QACnE,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IACtD,CAAC;IAEM,4BAA4B,CAAC,UAAsB;QACtD,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACnC,CAAC;IAEM,qBAAqB;QACxB,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE;YACnC,IAAI,CAAC,OAAO,EAAE,CAAC;SAClB;IACL,CAAC;IAEO,OAAO;QACX,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAC9B,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;IAC7B,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,OAAO,YAAa,SAAQ,MAAM;IAuB7B,uBAAuB,CAAC,KAAa;QACxC,KAAK,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,YAAmB,UAA+B,EAAE;QAChD,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAzB7C,YAAO,GAAkB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QAC9C,YAAO,GAA4B,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAEhF,0BAAqB,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAExE,6BAAwB,GAAQ,IAAI,CAAC;QACrC,sBAAiB,GAAW,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC;QAC7D,iBAAY,GAAG,KAAK,CAAC;QACrB,iBAAY,GAAW,GAAG,CAAC;QAC3B,iBAAY,GAAW,SAAS,CAAC,MAAM,CAAC;QACxC,oBAAe,GAAW,CAAC,CAAC;QAC5B,qBAAgB,GAAW,GAAG,CAAC;QAC/B,0BAAqB,GAAW,SAAS,CAAC,IAAI,CAAC;QAC/C,wBAAmB,GAAW,SAAS,CAAC,IAAI,CAAC;QAC7C,+BAA0B,GAAW,SAAS,CAAC,OAAO,CAAC;QACvD,aAAQ,GAAW,CAAC,CAAC;QACrB,kBAAa,GAAW,CAAC,CAAC;QAC1B,gBAAW,GAAY,IAAI,CAAC;QAUhC,IAAI,OAAO,CAAC,MAAM,CAAC,gBAAgB,KAAK,YAAY,CAAC,gBAAgB,EAAE;YACnE,MAAM,IAAI,KAAK,CAAC,8BAA8B,OAAO,CAAC,MAAM,CAAC,gBAAgB,iBAAiB,YAAY,CAAC,gBAAgB,OAAO,CAAC,CAAC;SACvI;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;YACpC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,GAAG,EAAE;gBACpC,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACnD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,IAAI,CAAC,8BAA8B,EAAE,CAAC;YAC1C,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;QAEpC,2EAA2E;QAC3E,YAAY;QAEZ,IAAI,CAAC,KAAK,GAAG;YACT,qBAAqB,EAAE,EAAE;YACzB,0BAA0B,EAAE,EAAE;YAC9B,6BAA6B,EAAE,EAAE;YACjC,cAAc,EAAE,OAAO,CAAC,MAAM,CAAC,4BAA4B;YAC3D,qBAAqB,EAAE,GAAG;YAC1B,oBAAoB,EAAE,GAAG;YACzB,gBAAgB,EAAE,EAAE;YACpB,iBAAiB,EAAE,EAAE;YACrB,yBAAyB,EAAE,EAAE;YAC7B,uBAAuB,EAAE,EAAE;YAC3B,mBAAmB,EAAE,IAAI;YACzB,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,aAAa,EAAE,EAAE;YACjB,WAAW,EAAE,IAAI;YACjB,sBAAsB,EAAE,KAAK;YAC7B,4BAA4B,EAAE,IAAI;YAClC,gBAAgB,EAAE,KAAK;YACvB,2BAA2B,EAAE,KAAK;YAClC,wBAAwB,EAAE,KAAK;YAC/B,YAAY,EAAE,IAAI;YAClB,2BAA2B,EAAE,KAAK;YAClC,kBAAkB,EAAE,IAAI;YACxB,gBAAgB,EAAE,IAAI;YACtB,+BAA+B,EAAE,KAAK;YACtC,sBAAsB,EAAE,IAAI;YAC5B,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,KAAK;YACjB,oBAAoB,EAAE,KAAK;YAC3B,qBAAqB,EAAE,KAAK;YAC5B,iBAAiB,EAAE,IAAI;YACvB,eAAe,EAAE,IAAI;YACrB,qBAAqB,EAAE,KAAK;YAC5B,4BAA4B,EAAE,KAAK;YACnC,WAAW,EAAE,KAAK;YAClB,cAAc,EAAE,EAAE;YAClB,kBAAkB,EAAE,IAAI;YACxB,gBAAgB,EAAE,IAAI;YACtB,qBAAqB,EAAE,KAAK;YAC5B,kBAAkB,EAAE,IAAI;YACxB,yBAAyB,EAAE,KAAK;YAChC,eAAe,EAAE,KAAK;YACtB,2BAA2B,EAAE,OAAO,CAAC,MAAM,CAAC,8BAA8B;YAC1E,yBAAyB,EAAE,KAAK;YAChC,qBAAqB,EAAE,EAAE,qBAAqB,EAAE,CAAC,EAAE;SACtD,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG;YACb,+BAA+B,EAAE,IAAI;YACrC,yCAAyC,EAAE,KAAK;YAChD,0BAA0B,EAAE,KAAK;YACjC,qBAAqB,EAAE,KAAK;YAC5B,4BAA4B,EAAE,KAAK;YACnC,wBAAwB,EAAE,KAAK;YAC/B,gBAAgB,EAAE,KAAK;YACvB,4BAA4B,EAAE,KAAK;YACnC,UAAU,EAAE,KAAK;YACjB,aAAa,EAAE,KAAK;YACpB,iBAAiB,EAAE,KAAK;YACxB,+BAA+B,EAAE,KAAK;YACtC,WAAW,EAAE,IAAI;YACjB,YAAY,EAAE,KAAK;YACnB,6BAA6B,EAAE,KAAK;YACpC,yBAAyB,EAAE,KAAK;YAChC,sBAAsB,EAAE,KAAK;YAC7B,oBAAoB,EAAE,IAAI;YAC1B,kBAAkB,EAAE,IAAI;YACxB,sBAAsB,EAAE,IAAI;YAC5B,8BAA8B,EAAE,KAAK;YACrC,mBAAmB,EAAE,IAAI;YACzB,uBAAuB,EAAE,KAAK;YAC9B,8CAA8C,EAAE,KAAK;YACrD,0BAA0B,EAAE,KAAK;SACpC,CAAC;QAEF,KAAK,CAAC,GAAG,CAAC,mBAAmB,GAAG,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC,CAAC;QAE/D,KAAK,CAAC,UAAU,GAAG,UAAU,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ;YAChE,KAAK,CAAC,QAAQ,CACV,SAAS,EACT,CAAC,IAAI,EAAE,EAAE;gBACL,QAAQ,CAAC,IAAc,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACrC,IAAI,SAAS,EAAE;oBACX,SAAS,EAAE,CAAC;iBACf;YACL,CAAC,EACD,SAAS,EACT,SAAS,EACT,KAAK,EACL,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;gBACnB,IAAI,OAAO,EAAE;oBACT,OAAO,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;iBAC1C;YACL,CAAC,CACJ,CAAC;QACN,CAAC,CAAC;QAEF,WAAW;QACX,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;YAC3B,MAAM,CAAC,GAAW,GAAG;gBAClB,eAAe,EAAE,cAAa,CAAC;gBAC/B,eAAe,EAAE,cAAa,CAAC;aAClC,CAAC;SACL;QAED,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;YAC5B,MAAM,CAAC,IAAY,GAAG,UAAU,CAAM;gBACnC,OAAO,CAAC,CAAC;YACb,CAAC,CAAC;SACL;QAED,sBAAsB;QACtB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE;YACvB,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,EAAE;gBAC3C,YAAY,EAAE,IAAI;gBAClB,KAAK,EAAE,SAAS,IAAI;oBAChB,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;oBAE7D,OAAO,KAAK;wBACR,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CACvB,IAAI,EACJ,UAAU,GAAQ,EAAE,GAAQ;4BACxB,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gCACpB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;6BAClD;iCAAM;gCACH,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;6BACjB;4BACD,OAAO,GAAG,CAAC;wBACf,CAAC,EACD,EAAE,CACL;wBACH,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3C,CAAC;gBACD,QAAQ,EAAE,IAAI;aACjB,CAAC,CAAC;SACN;QAED,sFAAsF;QACtF,sDAAsD;QACtD,MAAM,gBAAgB,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACvE,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG,GAAG,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC;QACvF,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACjE,IAAI,CAAC,qBAAqB,GAAG,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,MAAM,oBAAoB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACrD,IAAI,oBAAoB,EAAE;YACtB,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;SAC/C;QAED,mBAAmB;QACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,qBAAqB,EAAE,CAAC;QAEpD,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACzC,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC;YACpC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,IAAuC,EAAE,EAAE;gBAC1D,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,EAAE,CAAC;gBAC/C,cAAc,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAClC,IAAI,CAAC,qBAAqB,CAAC,eAAe,EAAE,CAAC;YACjD,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;SAC1D;QACD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,gBAAgB;IACT,MAAM,CAAC,uBAAuB;QACjC,OAAO,IAAI,gBAAgB,EAAE,CAAC;IAClC,CAAC;IAED;;;OAGG;IACO,cAAc,CAAC,oBAAyB,EAAE,SAAe;QAC/D,oKAAoK;QACpK,IAAI,SAAS,CAAC,qBAAqB,IAAI,SAAS,KAAK,MAAM,EAAE;YACzD,SAAS,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,CAAC;SACzD;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,CAAC;SAC5D;QACD,OAAO,CAAC,CAAC;IACb,CAAC;IAES,8BAA8B;QACpC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,kFAAkF;QACvI,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC;QACpD,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC;QACpD,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;QAEnD,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC;QAC9C,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC;QAC9C,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC;QAC9C,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC;QAE7C,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,WAAuC;QAClE,IAAI,IAAI,CAAC,mBAAmB,KAAK,WAAW,EAAE;YAC1C,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;gBAC1F,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,IAAI,CAAC,mBAAwC,CAAC,CAAC;gBACvG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;aACtD;YAED,IAAI,WAAW,EAAE;gBACb,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;gBACxF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,WAAgC,CAAC,CAAC;gBAC1F,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;aACtD;YAED,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC;SAC1C;IACL,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,KAA4B,EAAE,UAAmB,EAAE,KAAc,EAAE,UAAmB,KAAK;QACpG,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;SACxE;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAC9E,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,UAAU,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjF,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;QACvD,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAEM,iBAAiB,CAAC,OAAqB,EAAE,UAAoB,EAAE,MAAe;QACjF,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC/C,MAAM,MAAM,GAAG,IAAI,gBAAgB,EAAE,CAAC;QACtC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC;QACtB,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC;QAC/C,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,QAAQ,EAAE,UAAU,IAAI,KAAK,CAAC,CAAC;SAClJ;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEM,kBAAkB,CAAC,QAAmB,EAAE,UAAoB,EAAE,MAAe;QAChF,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;QAClF,MAAM,MAAM,GAAG,IAAI,gBAAgB,EAAE,CAAC;QACtC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,IAAI,KAAK,CAAC,CAAC;SACnI;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAES,wBAAwB,CAC9B,WAAgB,EAChB,aAA8C,EAC9C,WAAuC,EACvC,MAAc,EACd,qBAAkE;QAElE,IAAI,WAAW,EAAE;YACb,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,WAAW,EAAE,WAAW,CAAC,iBAAkB,CAAC,CAAC;SAC/E;QAED,MAAM,UAAU,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAC;QAC/C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACpD,MAAM,QAAQ,GAAG,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YACpD,IAAI,QAAQ,IAAI,CAAC,EAAE;gBACf,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;gBAC/B,IAAI,YAAY,GAA2B,IAAI,CAAC;gBAEhD,IAAI,qBAAqB,EAAE;oBACvB,YAAY,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;iBAC9C;gBACD,IAAI,CAAC,YAAY,EAAE;oBACf,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;iBACtC;gBAED,IAAI,YAAY,EAAE;oBACd,MAAM,MAAM,GAAG,YAAY,CAAC,SAAS,EAAgC,CAAC;oBACtE,IAAI,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE;wBACrC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAC3B,WAAW,EACX,MAAM,CAAC,kBAAmB,EAC1B,QAAQ,EACR,YAAY,CAAC,UAAU,EACvB,YAAY,CAAC,UAAU,EACvB,YAAY,CAAC,OAAO,EAAE,EACtB,mBAAmB,CAAC,YAAY,CAAC,IAAI,CAAC,EACtC,YAAY,CAAC,UAAU,EACvB,YAAY,CAAC,kBAAkB,EAAE,CACpC,CAAC;qBACL;iBACJ;aACJ;SACJ;IACL,CAAC;IAEM,WAAW,CAAC,aAA8C,EAAE,WAAuC,EAAE,MAAc;QACtH,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;SAC1D;QACD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;QACjE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,wBAAwB,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;QACjG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC;IAEM,uBAAuB,CAC1B,aAA8C,EAC9C,WAAuC,EACvC,MAAc,EACd,qBAAkE;QAElE,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;QACrD,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;QACtG,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,kBAAkB,CAAC,WAAoC;QAC3D,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;QAC1F,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,WAAW,CAAC,CAAC;QACrE,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAEM,qBAAqB,CAAC,WAAmC;QAC5D,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;QACxF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,WAAsC,CAAC,CAAC;QAChG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAEM,wBAAwB,CAAC,WAAmC;QAC/D,IAAI,CAAC,kBAAkB,CAAC,WAAsC,CAAC,CAAC;IACpE,CAAC;IAEM,aAAa,CAAC,eAAiC,EAAE,eAAyB;QAC7E,MAAM,qBAAqB,GAAG,eAAwC,CAAC;QACvE,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IACtF,CAAC;IAED;;;;;;OAMG;IACI,gBAAgB,CAAC,QAAgB,EAAE,UAAkB,EAAE,UAAkB,EAAE,cAAuB;QACrG,eAAe;QACf,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEnC,IAAI,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC,4BAA4B,EAAE;YAC/D,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;YAC7F,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YAC9D,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;YAChE,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;YAChE,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;SACvE;aAAM;YACH,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;YACpF,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YAC9D,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;YAChE,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;SACnE;QAED,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI;IACR,CAAC;IAED;;;;;;OAMG;IACI,cAAc,CAAC,QAAgB,EAAE,aAAqB,EAAE,aAAqB,EAAE,cAAuB;QACzG,eAAe;QACf,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEnC,IAAI,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC,qBAAqB,EAAE;YACxD,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;YACtF,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YAC9D,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;YACnE,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;YACnE,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;SACvE;aAAM;YACH,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC7E,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YAC9D,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;YACnE,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;SACtE;QAED,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,IAAI;IACR,CAAC;IAEM,qBAAqB;QACxB,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACxF,OAAO,IAAI,qBAAqB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAEM,qBAAqB;QACxB,OAAO,SAAS,CAAC;IACrB,CAAC;IAEM,iBAAiB;QACpB,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,uBAAuB,CAC1B,eAAiC,EACjC,gBAAwB,EACxB,kBAA0B,EAC1B,WAAoB,EACpB,oBAA4B,EAC5B,sBAA8B,EAC9B,cAAmB,EACnB,OAAyB;QAEzB,IAAI,WAAW,EAAE;YACb,IAAI,CAAC,sBAAsB,EAAE,CAAC;SACjC;aAAM;YACH,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;SAC5F;IACL,CAAC;IAED;;OAEG;IACI,oCAAoC,CAAC,eAAiC,EAAE,MAAkB;QAC7F,MAAM,qBAAqB,GAAG,eAAwC,CAAC;QACvE,IAAI,qBAAqB,CAAC,OAAO,EAAE;YAC/B,IAAI,qBAAqB,CAAC,UAAU,EAAE;gBAClC,MAAM,UAAU,GAAG,qBAAqB,CAAC,UAAU,CAAC;gBACpD,qBAAqB,CAAC,UAAU,GAAG,GAAG,EAAE;oBACpC,UAAU,EAAE,CAAC;oBACb,MAAM,EAAE,CAAC;gBACb,CAAC,CAAC;aACL;iBAAM;gBACH,qBAAqB,CAAC,UAAU,GAAG,MAAM,CAAC;aAC7C;SACJ;aAAM;YACH,MAAM,EAAE,CAAC;SACZ;IACL,CAAC;IAEM,sBAAsB;QACzB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACrC,CAAC;IAEM,mBAAmB,CAAC,eAAiC,EAAE,UAAkB,EAAE,YAAoB,EAAE,OAAyB;QAC7H,MAAM,qBAAqB,GAAG,eAAwC,CAAC;QAEvE,IAAI,CAAC,mCAAmC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE/D,MAAM,aAAa,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;QACxD,aAAa,CAAC,WAAW,EAAE,CAAC;QAC5B,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC;QAEhC,MAAM,eAAe,GAAG,IAAI,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAC5D,eAAe,CAAC,WAAW,EAAE,CAAC;QAC9B,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC;QAEpC,UAAU,GAAG,UAAU,CAAC,kBAAkB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAChE,YAAY,GAAG,UAAU,CAAC,kBAAkB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAEpE,MAAM,SAAS,GAAG,GAAG,EAAE;YACnB,qBAAqB,CAAC,UAAU,GAAG,IAAI,CAAC;YACxC,qBAAqB,CAAC,UAAU,EAAE,EAAE,CAAC;YACrC,IAAI,CAAC,kCAAkC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAClE,CAAC,CAAC;QAEF,IAAI,eAAe,CAAC,OAAO,EAAE;YACzB,qBAAqB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,CAAC,KAAY,EAAE,EAAE;gBAClH,qBAAqB,CAAC,gBAAgB,GAAG,KAAK,CAAC;YACnD,CAAC,CAAC,CAAC;SACN;aAAM;YACH,IAAI;gBACA,qBAAqB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;gBACrF,SAAS,EAAE,CAAC;aACf;YAAC,OAAO,CAAC,EAAE;gBACR,MAAM,OAAO,GAAG,CAAC,EAAE,OAAO,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,cAAc,GAAG,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACzF;SACJ;QAED,OAAO,qBAAqB,CAAC,OAAuB,CAAC;IACzD,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,IAAY;QAChC,MAAM,GAAG,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACxC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;QAClB,GAAG,CAAC,WAAW,EAAE,CAAC;QAClB,OAAO,GAAG,CAAC,IAAI,CAAC;IACpB,CAAC;IAES,WAAW,CAAC,OAAqB;QACvC,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE;YAClC,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;YACnF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAAwB,CAAC,CAAC;YAClF,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;YACnD,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;SAClC;IACL,CAAC;IAEM,sBAAsB,CAAC,eAAiC;QAC3D,MAAM,qBAAqB,GAAG,eAAwC,CAAC;QACvE,IAAI,qBAAqB,IAAI,qBAAqB,CAAC,OAAO,EAAE;YACxD,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;YACtF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YACvF,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;SACtD;IACL,CAAC;IAEM,WAAW,CAAC,eAAiC,EAAE,aAAuB;QACzE,MAAM,qBAAqB,GAAG,eAAwC,CAAC;QACvE,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,qBAAqB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IAClF,CAAC;IAEM,gBAAgB,CAAC,eAAiC,EAAE,SAAiB,EAAE,KAAa;QACvF,OAAO;QACP,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACvC,CAAC;IAEM,YAAY,CAAC,MAAc;QAC9B,MAAM,qBAAqB,GAAG,MAAM,CAAC,kBAAkB,EAA2B,CAAC;QACnF,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,OAAuB,CAAC,CAAC;QAEhE,gCAAgC;QAChC,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACtC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAClD,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAEnD,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;aACxC;SACJ;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAEM,cAAc,CAAC,SAAS,GAAG,KAAK;QACnC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;SAC1C;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;IACzC,CAAC;IAEM,eAAe,CAAC,SAAS,GAAG,KAAK;QACpC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;SAC3C;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;IAC1C,CAAC;IAEM,WAAW,CAAC,QAAuB,EAAE,aAAsB,EAAE,cAAuB;QACvF,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAChC,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;QACpF,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrE,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACtE,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAEM,aAAa,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc;QACpE,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACnF,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;QAC5D,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QAC7D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAEM,cAAc;QACjB,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACnF,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAEM,QAAQ,CAAC,OAAgB,EAAE,UAAkB,CAAC,EAAE,KAAe,EAAE,WAAW,GAAG,KAAK,EAAE,aAAuB,EAAE,OAAuB,EAAE,eAAuB,CAAC;QACnK,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE;YACrB,KAAK,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;SAC5D;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACjF,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QAC9D,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC;QACnE,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,IAAI,CAAC,aAAa,IAAI,aAAa,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzG,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,yBAAyB;QAC5B,MAAM,IAAI,GAAG;YACT,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE;YAC9B,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE;YAC9B,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE;YAC5B,GAAG,EAAE,CAAC;YACN,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE;YAC5B,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,MAAM,EAAE,GAAG,EAAE,GAAE,CAAC;SACnB,CAAC;QACF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,KAAa;QAC3B,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,EAAE;YACzB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;YACnF,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAClG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;SACtD;IACL,CAAC;IAED;;;OAGG;IACI,UAAU;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,KAAa;QAChC,IAAI,KAAK,KAAK,IAAI,CAAC,aAAa,EAAE;YAC9B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;YACxF,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAClG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;SACtD;IACL,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,MAAe;QACjC,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACrF,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACxH,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAEM,gBAAgB;QACnB,QAAQ,IAAI,CAAC,iBAAiB,EAAE;YAC5B,KAAK,OAAO,CAAC,MAAM,CAAC,gBAAgB;gBAChC,OAAO,SAAS,CAAC,KAAK,CAAC;YAC3B,KAAK,OAAO,CAAC,MAAM,CAAC,iBAAiB;gBACjC,OAAO,SAAS,CAAC,MAAM,CAAC;YAC5B,KAAK,OAAO,CAAC,MAAM,CAAC,kBAAkB;gBAClC,OAAO,SAAS,CAAC,OAAO,CAAC;YAC7B,KAAK,OAAO,CAAC,MAAM,CAAC,iBAAiB;gBACjC,OAAO,SAAS,CAAC,MAAM,CAAC;YAC5B,KAAK,OAAO,CAAC,MAAM,CAAC,mBAAmB;gBACnC,OAAO,SAAS,CAAC,QAAQ,CAAC;YAC9B,KAAK,OAAO,CAAC,MAAM,CAAC,gBAAgB;gBAChC,OAAO,SAAS,CAAC,KAAK,CAAC;YAC3B,KAAK,OAAO,CAAC,MAAM,CAAC,eAAe;gBAC/B,OAAO,SAAS,CAAC,IAAI,CAAC;YAC1B,KAAK,OAAO,CAAC,MAAM,CAAC,iBAAiB;gBACjC,OAAO,SAAS,CAAC,MAAM,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,gBAAgB,CAAC,SAAiB;QACrC,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,QAAQ,SAAS,EAAE;YACf,KAAK,SAAS,CAAC,KAAK;gBAChB,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBAClD,MAAM;YACV,KAAK,SAAS,CAAC,MAAM;gBACjB,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC;gBACnD,MAAM;YACV,KAAK,SAAS,CAAC,OAAO;gBAClB,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;gBACpD,MAAM;YACV,KAAK,SAAS,CAAC,MAAM;gBACjB,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC;gBACnD,MAAM;YACV,KAAK,SAAS,CAAC,QAAQ;gBACnB,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;gBACrD,MAAM;YACV,KAAK,SAAS,CAAC,KAAK;gBAChB,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBAClD,MAAM;YACV,KAAK,SAAS,CAAC,IAAI;gBACf,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC;gBACjD,MAAM;YACV,KAAK,SAAS,CAAC,MAAM;gBACjB,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC;gBACnD,MAAM;SACb;QAED,IAAI,CAAC,iBAAiB,GAAG,eAAe,CAAC;QACzC,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACrF,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC5E,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,MAAe;QAChC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;QAC1B,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QACpE,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,MAAe;QAChC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;QAC1B,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QACpE,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAEO,YAAY;QAChB,IAAI,CAAC,WAAW,CACZ,IAAI,CAAC,YAAY,EACjB,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAClD,yBAAyB,CAAC,IAAI,CAAC,mBAAmB,CAAC,EACnD,yBAAyB,CAAC,IAAI,CAAC,0BAA0B,CAAC,EAC1D,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,EACvC,IAAI,CAAC,eAAe,CACvB,CAAC;IACN,CAAC;IAEO,WAAW,CAAC,IAAY,EAAE,aAAqB,EAAE,WAAmB,EAAE,WAAmB,EAAE,IAAY,EAAE,GAAW;QACxH,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACnF,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;QACnE,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QACjE,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QACjE,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;QACzD,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,MAAe;QACnC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,YAAY,EAAE,CAAC;SACvB;aAAM;YACH,IAAI,CAAC,WAAW,CACZ,GAAG,EACH,OAAO,CAAC,MAAM,CAAC,sBAAsB,EACrC,OAAO,CAAC,MAAM,CAAC,sBAAsB,EACrC,OAAO,CAAC,MAAM,CAAC,sBAAsB,EACrC,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAClC,CAAC,CACJ,CAAC;SACL;IACL,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,uBAAuB;QAC1B,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,SAAiB;QAC5C,IAAI,CAAC,0BAA0B,GAAG,SAAS,CAAC;QAC5C,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,IAAY;QAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,kBAAkB,CAAC,WAAmB;QACzC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,2BAA2B,CAAC,SAAiB;QAChD,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QACjC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,sBAAsB,CAAC,IAAY;QACtC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,SAAiB;QAC5C,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;QACvC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,4BAA4B,CAAC,SAAiB;QACjD,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;QACrC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,2BAA2B;QAC9B,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,sBAAsB;QACzB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,uBAAuB;QAC1B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,4BAA4B;QAC/B,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;;;;;OAMG;IACI,iBAAiB,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC/D,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;IAC/E,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,IAAY,EAAE,qBAA8B,KAAK;QACjE,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;YAC1B,OAAO;SACV;QAED,MAAM,UAAU,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAE5C,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACrF,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;QAChE,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QAEnD,IAAI,CAAC,kBAAkB,EAAE;YACrB,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,SAAS,CAAC,aAAa,CAAC,CAAC;SACxD;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACI,YAAY;QACf,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAEM,MAAM,CAAC,OAA6B,EAAE,GAAW;QACpD,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAC/E,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,WAAW,CAAC,OAA6B,EAAE,KAAiB;QAC/D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;QACpF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,YAAY,CAAC,OAA6B,EAAE,KAAiB;QAChE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACrF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,YAAY,CAAC,OAA6B,EAAE,KAAiB;QAChE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACrF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,YAAY,CAAC,OAA6B,EAAE,KAAiB;QAChE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACrF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAC3D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,aAAa,CAAC,OAA6B,EAAE,KAAmB;QACnE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;QAC7D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,OAA6B,EAAE,KAAmB;QACpE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;QACvF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;QAC7D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,OAA6B,EAAE,KAAmB;QACpE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;QACvF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;QAC7D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,OAA6B,EAAE,KAAmB;QACpE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;QACvF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;QAC7D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,QAAQ,CAAC,OAA6B,EAAE,KAAe;QAC1D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;IAChE,CAAC;IAEM,SAAS,CAAC,OAA6B,EAAE,KAAe;QAC3D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;IACjE,CAAC;IAEM,SAAS,CAAC,OAA6B,EAAE,KAAe;QAC3D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;IACjE,CAAC;IAEM,SAAS,CAAC,OAA6B,EAAE,KAAe;QAC3D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;IACjE,CAAC;IAEM,WAAW,CAAC,OAA6B,EAAE,QAAmC;QACjF,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;QACpF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;QAChE,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QAEnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,YAAY,CAAC,OAA6B,EAAE,MAAoB;QACnE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACrF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;QAC9D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,YAAY,CAAC,OAA6B,EAAE,MAAoB;QACnE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACrF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;QAC9D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,QAAQ,CAAC,OAA6B,EAAE,KAAa;QACxD,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACjF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;QAC5D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,SAAS,CAAC,OAA6B,EAAE,CAAS,EAAE,CAAS;QAChE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QAClF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,SAAS,CAAC,OAA6B,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC3E,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QAClF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,SAAS,CAAC,OAA6B,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACtF,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QAClF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAA4B,CAAC,CAAC;QACtF,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,SAAS,CAAC,OAA6B,EAAE,MAAmB;QAC/D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;QACtD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,SAAS,CAAC,OAA6B,EAAE,MAAmB,EAAE,KAAa;QAC9E,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,UAAU,CAAC,UAAoB;QAClC,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACpC,OAAO;SACV;QACD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAE5B,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;YACnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;SAC5B;QAED,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;IAC9C,CAAC;IAES,cAAc;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;IACxC,CAAC;IAES,cAAc,CAAC,OAA+B;QACpD,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAwB,CAAC,CAAC;SACxD;IACL,CAAC;IAED;;;;;;;OAOG;IACI,oBAAoB,CAAC,OAAkC,EAAE,MAAW,EAAE,OAAgB,EAAE,cAAuB,KAAK,EAAE,MAAe;QACxI,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE;YACxB,WAAW,GAAG,KAAK,CAAC;SACvB;QAED,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE;YACzC,MAAM,MAAM,GAAG,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;YAChE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC9C,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;SAC1B;IACL,CAAC;IAEM,oBAAoB,CAAC,KAAa,EAAE,MAAc,EAAE,eAAwB,EAAE,YAAoB;QACrG,mHAAmH;QACnH,wCAAwC;QACxC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3B,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,kBAAkB,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;IAC9I,CAAC;IAEM,kBAAkB,CAAC,WAAkC;QACxD,oFAAoF;QACpF,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;SAChD;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,kBAAkB,CAAC,OAAkC,EAAE,KAAuB,EAAE,OAAgB;QACnG,IAAI,OAAO,IAAI,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,OAAO,EAAE;YACrD,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;YACjE,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,YAAY,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACjE;IACL,CAAC;IAEM,gBAAgB,CACnB,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,MAAc,EACd,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI,EACpC,OAAe,SAAS,CAAC,wBAAwB,EACjD,gBAAwB,CAAC,EACzB,gBAAyB,KAAK;QAE9B,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC,GAAG,CAAC,CAAC;QAErE,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;QAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC;QAClC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;QACpC,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;QACnC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC,eAAe,CAAC,CAAC;QAEjF,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;QAEjG,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1B,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;YACjE,MAAM,MAAM,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;YACnD,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,uBAAuB,CAC1B,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,KAAa,EACb,MAAc,EACd,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI,EACpC,WAAW,GAAG,SAAS,CAAC,wBAAwB;QAEhD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC,UAAU,CAAC,CAAC;QAE5E,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;QAC3B,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;QAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;QAEzB,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1B,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;YAClE,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,sBAAsB,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;YAErJ,MAAM,MAAM,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;YACnD,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;SACnD;QAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QAEvB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,gBAAgB,CACnB,OAAkC,EAClC,UAAqC,EACrC,MAAc,EACd,OAAgB,EAChB,cAAgC,IAAI,EACpC,OAAe,SAAS,CAAC,wBAAwB,EACjD,gBAAyB,KAAK;QAE9B,IAAI,CAAC,OAAO,EAAE;YACV,OAAO;SACV;QAED,IAAI,UAAU,IAAI,OAAO,CAAC,gBAAgB,EAAE;YACxC,MAAM,kBAAkB,GAAG,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;YACvE,IAAI,CAAC,OAAO,CAAC,cAAc,CACvB,kBAAkB,EAClB,UAAU,EACV,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,MAAM,EACd,sBAAsB,CAAC,MAAM,EAAE,IAAI,CAAC,EACpC,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,OAAO,CAClB,CAAC;SACL;QAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED,qEAAqE;IACrE;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACI,aAAa,CAChB,GAAqB,EACrB,QAAiB,EACjB,OAAgB,EAChB,KAA2B,EAC3B,eAAuB,SAAS,CAAC,8BAA8B,EAC/D,SAAuD,IAAI,EAC3D,UAA+D,IAAI,EACnE,SAAmG,IAAI,EACvG,WAAsC,IAAI,EAC1C,SAA2B,IAAI,EAC/B,kBAAoC,IAAI,EACxC,QAAiB,EACjB,aAAmB,EACnB,aAAsB,EACtB,aAAa,GAAG,KAAK;QAErB,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAChB,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,CAAC;QAC9C,gDAAgD;QAChD,MAAM,QAAQ,GAAG,QAAQ,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;QAE5D,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC,GAAG,CAAC,CAAC;QAE3F,MAAM,WAAW,GAAG,GAAG,CAAC;QACxB,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE;YAChE,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;SACxC;QAED,4CAA4C;QAC5C,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACrC,MAAM,SAAS,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAE/G,IAAI,MAAM,GAAqC,IAAI,CAAC;QACpD,KAAK,MAAM,eAAe,IAAI,MAAM,CAAC,eAAe,EAAE;YAClD,IAAI,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACpC,MAAM,GAAG,eAAe,CAAC;gBACzB,MAAM;aACT;SACJ;QAED,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;SACjC;QACD,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;QAClB,OAAO,CAAC,eAAe,GAAG,CAAC,QAAQ,CAAC;QACpC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAEzE,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,mEAAmE;YACnE,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC;SAC5B;QAED,IAAI,cAAc,GAAwC,IAAI,CAAC;QAC/D,IAAI,MAAM,IAAI,CAAC,QAAQ,EAAE;YACrB,cAAc,GAAG,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SAC3D;QAED,IAAI,CAAC,QAAQ,EAAE;YACX,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC7C;QAED,MAAM,eAAe,GAAG,CAAC,OAAgB,EAAE,SAAe,EAAE,EAAE;YAC1D,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;aACpC;YAED,IAAI,GAAG,KAAK,WAAW,EAAE;gBACrB,IAAI,cAAc,EAAE;oBAChB,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;iBACrD;gBAED,IAAI,WAAW,CAAC,kBAAkB,EAAE;oBAChC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,eAAe,EAAE,QAAQ,EAAE,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;iBACnI;gBAED,IAAI,OAAO,EAAE;oBACT,OAAO,CAAC,CAAC,OAAO,IAAI,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;iBAC7H;aACJ;iBAAM;gBACH,qEAAqE;gBACrE,MAAM,CAAC,IAAI,CAAC,kBAAkB,GAAG,qBAAqB,WAAW,EAAE,CAAC,CAAC;gBACrE,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;aACvK;QACL,CAAC,CAAC;QAEF,mCAAmC;QACnC,IAAI,MAAM,EAAE;YACR,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;SACxF;aAAM;YACH,MAAM,MAAM,GAAG,CAAC,IAAqB,EAAE,EAAE;gBACrC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;oBAC3B,IAAI,KAAK,EAAE;wBACP,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;qBACpC;oBAED,OAAO;iBACV;gBAED,MAAM,kBAAkB,GAAG,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;gBAEvE,IAAI,CAAC,OAAO,CAAC,WAAW,CACpB,kBAAkB,EAClB,IAAI,EACJ,CAAC,QAAQ,EACT,OAAO,EACP,OAAO,CAAC,cAAc,EACtB,GAAG,EAAE;oBACD,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;oBACrE,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;oBACvE,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC;oBAClC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;oBACpC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;oBAEvB,MAAM,MAAM,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;oBACnD,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;oBAErD,IAAI,KAAK,EAAE;wBACP,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;qBACpC;oBAED,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;oBACpD,OAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;gBACvC,CAAC,EACD,GAAG,EAAE;oBACD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;gBACxD,CAAC,CACJ,CAAC;YACN,CAAC,CAAC;YAEF,IAAI,QAAQ,IAAI,MAAM,EAAE;gBACpB,IAAI,MAAM,YAAY,WAAW,EAAE;oBAC/B,MAAM,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;iBAClC;qBAAM,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;oBACnC,MAAM,CAAC,MAAM,CAAC,CAAC;iBAClB;qBAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;oBACnC,MAAM,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;iBACtD;qBAAM;oBACH,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;iBAC9C;aACJ;iBAAM;gBACH,IAAI,QAAQ,EAAE;oBACV,MAAM,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBACnD;qBAAM;oBACH,IAAI,CAAC,SAAS,CACV,GAAG,EACH,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,IAAmB,CAAC,CAAC,EACrD,SAAS,EACT,SAAS,EACT,IAAI,EACJ,CAAC,OAAqB,EAAE,SAAe,EAAE,EAAE;wBACvC,eAAe,CAAC,iBAAiB,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC;oBAC1F,CAAC,CACJ,CAAC;iBACL;aACJ;SACJ;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;OAMG;IACI,iBAAiB,CAAC,OAAsB,EAAE,aAAsB,KAAK,EAAE,eAAuB,SAAS,CAAC,8BAA8B;QACzI,MAAM,eAAe,GAAG,IAAI,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACzE,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACvF,eAAe,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACnD,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAClE,eAAe,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACpE,eAAe,CAAC,KAAK,GAAG,eAAe,CAAC,SAAS,CAAC;QAClD,eAAe,CAAC,MAAM,GAAG,eAAe,CAAC,UAAU,CAAC;QACpD,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC;QAC/B,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;QACxC,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;QAC9D,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED,uDAAuD;IACvD;;;OAGG;IACI,gBAAgB;QACnB,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;IACzF,CAAC;IAEM,0BAA0B,CAAC,IAAiB,EAAE,OAAoC,EAAE,SAA8B;QACrH,8BAA8B;QAC9B,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,KAAK,CAAC;QACzD,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC;QAErC,MAAM,eAAe,GAAG,SAAsC,CAAC;QAC/D,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC,YAAY,CAAC,CAAC;QAE9E,MAAM,KAAK,GAAwD,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;QAC/F,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAY,IAAI,CAAC;QAEjG,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,gBAAiB,CAAC,kBAAkB,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAChJ,eAAe,CAAC,wBAAwB,GAAG,WAAW,CAAC;QACvD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,0BAA0B,CAAC,WAAwC;QACtE,IAAI,WAAW,EAAE;YACb,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;YAC1F,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,WAAyB,CAAC,CAAC;YACnF,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;SACtD;IACL,CAAC;IAED;;;;;OAKG;IACI,4BAA4B,CAAC,WAAmB,EAAE,OAA4B;QACjF,MAAM,OAAO,GAAG,IAAI,OAAO,CAAc,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzD,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvC,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE;gBAChB,IAAI;oBACA,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;oBAC1D,OAAO,CAAC,WAAW,CAAC,CAAC;iBACxB;gBAAC,OAAO,KAAK,EAAE;oBACZ,MAAM,CAAC,uBAAuB,KAAK,CAAC,GAAG,oBAAoB,KAAK,EAAE,CAAC,CAAC;iBACvE;YACL,CAAC,CAAC;YACF,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;gBACtB,MAAM,CAAC,uBAAuB,KAAK,CAAC,GAAG,oBAAoB,KAAK,EAAE,CAAC,CAAC;YACxE,CAAC,CAAC;YAEF,KAAK,CAAC,GAAG,GAAG,WAAW,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,KAAwB,EAAE,OAA4B;QAC3E,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACtB,MAAM,GAAG,GAA2B,KAAK,CAAC;gBAC1C,IAAI,GAAG,CAAC,MAAM,EAAE;oBACZ,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrD,IAAI,KAAK,EAAE;wBACP,OAAO,CAAC,KAAK,CAAC,CAAC;wBACf,OAAO;qBACV;iBACJ;aACJ;YACD,MAAM,CAAC,yCAAyC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACI,iBAAiB,CAAC,KAAkB,EAAE,WAAmB,EAAE,YAAoB;QAClF,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACI,iBAAiB,CACpB,OAAe,EACf,KAAsB,EACtB,KAAyB,EACzB,QAAkB,EAClB,SAAyC,IAAI,EAC7C,UAAiE,IAAI,EACrE,MAAe,EACf,kBAAuB,IAAI,EAC3B,iBAAiB,GAAG,KAAK,EACzB,WAAmB,CAAC,EACpB,YAAoB,CAAC,EACrB,WAAsC,IAAI,EAC1C,aAAmB,EACnB,aAAa,GAAG,KAAK;QAErB,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC5F,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;QACtB,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC;QACtB,OAAO,CAAC,eAAe,GAAG,CAAC,QAAQ,CAAC;QACpC,OAAO,CAAC,mBAAmB,GAAG,QAAQ,CAAC;QACvC,OAAO,CAAC,oBAAoB,GAAG,SAAS,CAAC;QACzC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;QAE3E,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,OAAO,CAAC,UAAU,GAAG,eAAe,CAAC;YACrC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;SAC1B;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACzC,MAAM,SAAS,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAEnH,8CAA8C;QAC9C,IAAI,SAAS,KAAK,MAAM,EAAE;YACtB,MAAM,UAAU,GAAG,CAAC,IAAqB,EAAE,EAAE;gBACzC,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAE,CAAC;gBAC/B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;gBAC3B,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;gBAE5B,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAElC,MAAM,YAAY,GAAG,IAAI,CAAC,QAA4C,CAAC;gBACvE,IAAI,CAAC,YAAY,EAAE;oBACf,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;iBACjD;gBAED,OAAO,CAAC,mBAAmB,GAAG,YAAY,CAAC,kBAAkB,CAAC;gBAC9D,MAAM,SAAS,GAAG,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAE9D,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,kBAAkB,CAAC;gBAC9C,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC,wBAAwB,CAAC;gBAClD,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC/B,OAAO,CAAC,SAAS,EAAE,CAAC,yBAAyB,CAAC,OAAO,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;gBACvF,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;gBACvB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;gBAEvB,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAChC,OAAO,CAAC,gBAAiB,CAAC,kBAAkB,EAC5C,SAAS,EACT,KAAK,EACL,OAAO,CAAC,cAAc,EACtB,GAAG,EAAE;oBACD,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;oBACvB,IAAI,MAAM,EAAE;wBACR,MAAM,EAAE,CAAC;qBACZ;gBACL,CAAC,EACD,GAAG,EAAE;oBACD,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;gBAC7D,CAAC,CACJ,CAAC;YACN,CAAC,CAAC;YAEF,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7B,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;aACnE;iBAAM;gBACH,MAAM,eAAe,GAAG,CAAC,OAAqB,EAAE,SAAe,EAAE,EAAE;oBAC/D,IAAI,OAAO,IAAI,OAAO,EAAE;wBACpB,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;qBACjE;gBACL,CAAC,CAAC;gBAEF,IAAI,CAAC,SAAS,CACV,OAAO,EACP,CAAC,IAAI,EAAE,EAAE;oBACL,UAAU,CAAC,IAAI,UAAU,CAAC,IAAmB,EAAE,CAAC,EAAG,IAAoB,CAAC,UAAU,CAAC,CAAC,CAAC;gBACzF,CAAC,EACD,SAAS,EACT,SAAS,EACT,IAAI,EACJ,eAAe,CAClB,CAAC;aACL;SACJ;aAAM;YACH,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC9B,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;aAC3E;YAED,qEAAqE;YACrE,MAAM,cAAc,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACpF,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;iBACzI,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBACX,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACzC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,gBAAiB,CAAC,kBAAkB,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,cAAc,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;gBAC/I,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;iBACD,IAAI,CACD,GAAG,EAAE;gBACD,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;gBACvB,IAAI,MAAM,EAAE;oBACR,MAAM,EAAE,CAAC;iBACZ;YACL,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;gBACN,IAAI,OAAO,EAAE;oBACT,OAAO,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;iBAC9D;YACL,CAAC,CACJ,CAAC;SACT;QAED,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1C,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,gBAAgB;IACT,sBAAsB;QACzB,OAAO,IAAI,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3F,CAAC;IAED,gBAAgB;IACT,kCAAkC,CAAC,OAAgB,EAAE,MAAe,EAAE,IAAiB;QAC1F,MAAM,SAAS,GAAG,IAAI,yBAAyB,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7E,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/C,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,gBAAgB;IACT,sBAAsB,CACzB,IAAiB,EACjB,OAAiD,EACjD,wBAAwB,GAAG,IAAI,EAC/B,MAAM,GAAG,qBAAqB,CAAC,OAAO;QAEtC,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,IAAI,GAAG,SAAS,CAAC,wBAAwB,CAAC;QAC9C,IAAI,YAAY,GAAG,SAAS,CAAC,8BAA8B,CAAC;QAC5D,IAAI,MAAM,GAAG,SAAS,CAAC,kBAAkB,CAAC;QAC1C,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,KAAyB,CAAC;QAC9B,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YACtD,eAAe,GAAG,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;YAC5C,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;YACtF,YAAY,GAAG,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;YACpH,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;YACtF,aAAa,GAAG,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;YACpF,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC;YAC/B,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;SACzB;aAAM;YACH,eAAe,GAAG,CAAC,CAAC,OAAO,CAAC;SAC/B;QAED,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC,eAAe,CAAC,CAAC;QAExE,IAAI,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE;YACjF,yEAAyE;YACzE,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;SACzD;aAAM,IAAI,IAAI,KAAK,SAAS,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE;YACjG,2EAA2E;YAC3E,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;SACzD;QACD,IAAI,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;YAClE,IAAI,GAAG,SAAS,CAAC,wBAAwB,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;SAC7F;QAED,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClD,MAAM,KAAK,GAAwD,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;QAC/F,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAY,IAAI,CAAC;QAEjG,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAI,CAAC,CAAC;QACtF,IAAI,MAAM,KAAK,CAAC,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;SACzE;QAED,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAiB,CAAC,kBAAkB,CAAC;QACnE,MAAM,mBAAmB,GAAG,sBAAsB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACjE,4HAA4H;QAC5H,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,mBAAmB,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QACjI,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,qBAAqB,CAAC,YAAY,CAAC,CAAC,CAAC;QAE7E,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC;QACvC,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC;QACvB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;QAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QAEtB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1C,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,yBAAyB,CAAC,IAAgD,EAAE,OAA8C;QAC7H,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAA8B,CAAC;QAE3G,IAAI,mBAAmB,GAAG,IAAI,CAAC;QAC/B,IAAI,qBAAqB,GAAG,KAAK,CAAC;QAClC,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAC9B,IAAI,eAAe,GAAgC,SAAS,CAAC;QAC7D,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YACtD,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,IAAI,IAAI,CAAC;YAC1D,qBAAqB,GAAG,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC;YACxD,iBAAiB,GAAG,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC;YAChD,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;YAC1C,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC;SAClC;QAED,MAAM,OAAO,GAAG,eAAe,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,qBAAqB,CAAC,YAAY,CAAC,CAAC,CAAC;QACrJ,MAAM,KAAK,GAAwD,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;QAC/F,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAY,IAAI,CAAC;QAEjG,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAC9C,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAiB,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,EAC7D,KAAK,EACL,MAAM,EACN,qBAAqB,EACrB,mBAAmB,EACnB,OAAO,CACV,CAAC;QAEF,SAAS,CAAC,YAAY,GAAG,WAAW,CAAC;QACrC,SAAS,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QACrD,SAAS,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;QACzD,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;QAE7B,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAE/B,OAAO,SAAS,CAAC;IACrB,CAAC;IAEM,oCAAoC,CAAC,SAA8B,EAAE,OAAe;QACvF,MAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;QAC9E,OAAO,SAAS,CAAC,OAAO,CAAC;IAC7B,CAAC;IAEM,yBAAyB,CAAC,YAAoB,EAAE,OAAwB;QAC3E,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1B,MAAM,MAAM,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;YACnD,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;SACjF;QAED,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACxC,CAAC;IAEM,eAAe,CAAC,OAA4B,EAAE,SAAkB,EAAE,aAAsB,EAAE,cAAuB,EAAE,uBAAiC;QACvJ,MAAM,eAAe,GAAG,OAAoC,CAAC;QAE7D,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACrD;QAED,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QAEpC,IAAI,SAAS,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;SAClF;QAED,IAAI,aAAa,IAAI,cAAc,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;SACjG;QAED,IAAI,uBAAuB,EAAE;YACzB,4CAA4C;SAC/C;QAED,IAAI,eAAe,CAAC,wBAAwB,EAAE;YAC1C,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAC;SAC1E;aAAM;YACH,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;SAC9D;IACL,CAAC;IAEM,iBAAiB,CAAC,OAA4B,EAAE,sBAAsB,GAAG,KAAK,EAAE,cAA2B;QAC9G,0EAA0E;QAE1E,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,IAAI,cAAc,EAAE;YAChB,cAAc,EAAE,CAAC;SACpB;QAED,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAEM,yBAAyB,CAAC,IAAe;QAC5C,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC/C,CAAC;IAEM,wBAAwB,CAAC,WAAuB,EAAE,OAAqB,EAAE,SAAiB,CAAC;QAC9F,MAAM,MAAM,GAAG,WAA+B,CAAC;QAC/C,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC/C,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,MAAM,CAAC,iBAAkB,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAC5H,CAAC;IAEM,yBAAyB,CAAC,YAAwB,EAAE,IAAe,EAAE,UAAU,GAAG,CAAC,EAAE,UAAmB;QAC3G,MAAM,MAAM,GAAG,YAAgC,CAAC;QAChD,MAAM,QAAQ,GAAG,IAAI,YAAY,KAAK,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,WAAW,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5H,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,EAAE,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC;QACzG,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,kBAAmB,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAC9I,CAAC;IAED,sEAAsE;IAC5D,WAAW,CAAC,OAAe,EAAE,OAA8B,EAAE,oBAAoB,GAAG,KAAK,EAAE,mBAAmB,GAAG,KAAK;QAC5H,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAA6B,CAAC;QACzE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,KAAK,CAAC;SAChB;QAED,aAAa;QACb,IAAI,CAAC,OAAO,EAAE;YACV,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE;gBAC3C,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;gBAC9B,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;aAC5C;YACD,OAAO,KAAK,CAAC;SAChB;QAED,QAAQ;QACR,IAAmB,OAAQ,CAAC,KAAK,EAAE;YAC/B,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;YACf,OAAQ,CAAC,MAAM,EAAE,CAAC;SACpC;aAAM,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,CAAC,wBAAwB,EAAE;YACtE,gBAAgB;YAChB,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,eAAgC,CAAC;QACrC,IAAI,mBAAmB,EAAE;YACrB,eAAe,GAAyB,OAAQ,CAAC,mBAAoB,CAAC;SACzE;aAAM,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE;YAC1B,eAAe,GAAoB,OAAO,CAAC,kBAAkB,EAAE,CAAC;SACnE;aAAM,IAAI,OAAO,CAAC,MAAM,EAAE;YACvB,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;SAC3C;aAAM,IAAI,OAAO,CAAC,IAAI,EAAE;YACrB,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC;SACzC;aAAM,IAAI,OAAO,CAAC,SAAS,EAAE;YAC1B,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC;SAC9C;aAAM;YACH,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC;SACvC;QAED,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAE9B,IAAI,CAAC,eAAe,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE;YACvD,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,mBAAmB,CACpB,eAAe,CAAC,gBAAgB,CAAC,kBAAkB,EACnD,oBAAoB,CAAC,OAAO,CAAC,KAAK,CAAC,EACnC,oBAAoB,CAAC,OAAO,CAAC,KAAK,CAAC,EACnC,oBAAoB,CAAC,OAAO,CAAC,KAAK,CAAC,CACtC,CAAC;QACF,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAEtC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,eAAe,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QAEnF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,uCAAuC;IAC/B,mBAAmB,CAAC,OAAsB,EAAE,MAAc;QAC9D,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;QAC3F,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAAqB,CAAC,CAAC;QAC/E,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAC5D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAED,kDAAkD;IAC1C,mBAAmB,CAAC,OAAsB,EAAE,YAAoB,EAAE,YAAoB,EAAE,YAAoB;QAChH,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;QAC3F,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAAqB,CAAC,CAAC;QAC/E,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAClE,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAClE,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAClE,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAEO,eAAe,CAAC,OAAsB,EAAE,OAAsB;QAClE,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACnF,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC;QACjE,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC;QACjE,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;IACvD,CAAC;IAED,+DAA+D;IAC/D,oGAAoG;IAC5F,uBAAuB,CAAC,OAAoB;QAChD,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;QACrD,MAAM,KAAK,GAAG,OAAO,CAAC,yBAAyB,CAAC;QAEhD,IAAI,CAAC,eAAe,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE;YACvD,OAAO;SACV;QAED,IAAI,eAAe,CAAC,gCAAgC,KAAK,KAAK,EAAE;YAC5D,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC;YACnG,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,eAAe,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;YAC7G,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;YAC3D,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;YACnD,eAAe,CAAC,gCAAgC,GAAG,KAAK,CAAC;SAC5D;IACL,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,OAAe,EAAE,OAAwB;QACzD,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAA6B,CAAC;QACzE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO;SACV;QACD,IAAI,OAAO,IAAI,OAAO,CAAC,gBAAgB,EAAE;YACrC,MAAM,kBAAkB,GAAG,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;YACvE,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;SACrD;IACL,CAAC;IAES,aAAa,CAAC,MAAwB;QAC5C,IAAI,MAAM,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;YAC1F,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAClF,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;YACnD,OAAO,MAAM,CAAC,iBAAiB,CAAC;SACnC;QAED,IAAI,MAAM,CAAC,kBAAkB,EAAE;YAC3B,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;YAC3F,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;YACnF,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC;YACnD,OAAO,MAAM,CAAC,kBAAkB,CAAC;SACpC;IACL,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,KAAa,EAAE,MAAc;QAC7C,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;SAC1D;QACD,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACpC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;QACrB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;SAC1D;QACD,MAAM,KAAK,GAAG,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;QAClC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;OAWG;IACI,iBAAiB,CACpB,OAAwB,EACxB,SAA0B,EAC1B,OAAe,EACf,OAAe,EACf,KAAa,EACb,MAAc,EACd,YAAoB,CAAC,EACrB,MAAc,CAAC,EACf,eAAe,GAAG,KAAK;QAEvB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,sCAAsC,CACzC,OAAwB,EACxB,cAAsB,EACtB,KAAa,EACb,MAAc,EACd,IAAqB,EACrB,YAAoB,CAAC,EACrB,MAAc,CAAC;QAEf,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACI,4BAA4B,CAAC,OAAwB,EAAE,SAA0B,EAAE,YAAoB,CAAC,EAAE,MAAc,CAAC;QAC5H,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACI,+BAA+B,CAAC,OAAwB,EAAE,SAA0B,EAAE,YAAoB,CAAC,EAAE,MAAc,CAAC;QAC/H,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,OAAwB,EAAE,KAAuB,EAAE,YAAoB,CAAC,EAAE,MAAc,CAAC;QAClH,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;IACxE,CAAC;IAEM,aAAa,CAAC,IAAY;QAC7B,OAAO;QACP,MAAM,MAAM,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;QACpD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,gBAAgB,KAAU,CAAC;IAE3B,kBAAkB,CACrB,OAAwB,EACxB,KAAa,EACb,MAAc,EACd,SAAkB,EAClB,KAAc,EACd,MAAkC,EAClC,cAAwB,EACxB,iBAA2B,EAC3B,CAAU,EACV,CAAU;QAEV,IAAI,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;YAC7C,MAAM,IAAI,KAAK,CAAC,4DAA4D,SAAS,GAAG,CAAC,CAAC;SAC7F;QAED,OAAO,IAAI,CAAC,OAAO;aACd,WAAW,CACR,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,EAC5C,KAAK,IAAI,CAAC,EACV,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,KAAK,EACL,MAAM,EACN,MAAM,EAAE,MAAM,IAAI,IAAI,EACtB,MAAM,EAAE,UAAU,IAAI,CAAC,EACvB,MAAM,EAAE,UAAU,IAAI,CAAC,CAC1B;aACA,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;YAChB,IAAI,CAAC,MAAM,EAAE;gBACT,MAAM,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;aACtC;YAED,OAAO,MAAM,CAAC;QAClB,CAAC,CAAC,CAAC;IACX,CAAC;;AAx2ED,2DAA2D;AACnC,6BAAgB,GAAG,CAAC,AAAJ,CAAK", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable, IndicesArray, DataArray, FloatArray, DeepImmutable } from \"../types\";\r\nimport { Engine } from \"../Engines/engine\";\r\nimport type { VertexBuffer } from \"../Buffers/buffer\";\r\nimport { InternalTexture, InternalTextureSource } from \"../Materials/Textures/internalTexture\";\r\nimport type { IInternalTextureLoader } from \"../Materials/Textures/internalTextureLoader\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport type { VideoTexture } from \"../Materials/Textures/videoTexture\";\r\nimport type { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { EnvironmentTextureSpecularInfoV1 } from \"../Misc/environmentTextureTools\";\r\nimport { CreateImageDataArrayBufferViews, GetEnvInfo, UploadEnvSpherical } from \"../Misc/environmentTextureTools\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { RenderTargetCreationOptions, TextureSize, DepthTextureCreationOptions, InternalTextureCreationOptions } from \"../Materials/Textures/textureCreationOptions\";\r\nimport type { IPipelineContext } from \"./IPipelineContext\";\r\nimport type { IColor3Like, IColor4Like, IViewportLike } from \"../Maths/math.like\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { Constants } from \"./constants\";\r\nimport type { ISceneLike } from \"./thinEngine\";\r\nimport { ThinEngine } from \"./thinEngine\";\r\nimport type { IWebRequest } from \"../Misc/interfaces/iWebRequest\";\r\nimport { EngineStore } from \"./engineStore\";\r\nimport { ShaderCodeInliner } from \"./Processors/shaderCodeInliner\";\r\nimport { WebGL2ShaderProcessor } from \"../Engines/WebGL/webGL2ShaderProcessors\";\r\nimport type { IMaterialContext } from \"./IMaterialContext\";\r\nimport type { IDrawContext } from \"./IDrawContext\";\r\nimport type { ICanvas, IImage } from \"./ICanvas\";\r\nimport type { IStencilState } from \"../States/IStencilState\";\r\nimport type { RenderTargetWrapper } from \"./renderTargetWrapper\";\r\nimport type { NativeData } from \"./Native/nativeDataStream\";\r\nimport { NativeDataStream } from \"./Native/nativeDataStream\";\r\nimport type { INative, INativeCamera, INativeEngine, NativeFramebuffer, NativeProgram, NativeTexture, NativeUniform, NativeVertexArrayObject } from \"./Native/nativeInterfaces\";\r\nimport { NativePipelineContext } from \"./Native/nativePipelineContext\";\r\nimport { NativeRenderTargetWrapper } from \"./Native/nativeRenderTargetWrapper\";\r\nimport { NativeHardwareTexture } from \"./Native/nativeHardwareTexture\";\r\nimport type { HardwareTextureWrapper } from \"../Materials/Textures/hardwareTextureWrapper\";\r\nimport {\r\n    getNativeAlphaMode,\r\n    getNativeAttribType,\r\n    getNativeSamplingMode,\r\n    getNativeTextureFormat,\r\n    getNativeStencilDepthFail,\r\n    getNativeStencilDepthPass,\r\n    getNativeStencilFunc,\r\n    getNativeStencilOpFail,\r\n    getNativeAddressMode,\r\n} from \"./Native/nativeHelpers\";\r\n\r\ndeclare const _native: INative;\r\n\r\nconst onNativeObjectInitialized = new Observable<INative>();\r\nif (typeof self !== \"undefined\" && !Object.prototype.hasOwnProperty.call(self, \"_native\")) {\r\n    let __native: INative;\r\n    Object.defineProperty(self, \"_native\", {\r\n        get: () => __native,\r\n        set: (value: INative) => {\r\n            __native = value;\r\n            if (__native) {\r\n                onNativeObjectInitialized.notifyObservers(__native);\r\n            }\r\n        },\r\n    });\r\n}\r\n\r\n/**\r\n * Returns _native only after it has been defined by BabylonNative.\r\n * @internal\r\n */\r\nexport function AcquireNativeObjectAsync(): Promise<INative> {\r\n    return new Promise((resolve) => {\r\n        if (typeof _native === \"undefined\") {\r\n            onNativeObjectInitialized.addOnce((nativeObject) => resolve(nativeObject));\r\n        } else {\r\n            resolve(_native);\r\n        }\r\n    });\r\n}\r\n\r\n/**\r\n * Registers a constructor on the _native object. See NativeXRFrame for an example.\r\n * @internal\r\n */\r\nexport async function RegisterNativeTypeAsync<Type>(typeName: string, constructor: Type) {\r\n    ((await AcquireNativeObjectAsync()) as any)[typeName] = constructor;\r\n}\r\n\r\n/**\r\n * Container for accessors for natively-stored mesh data buffers.\r\n */\r\nclass NativeDataBuffer extends DataBuffer {\r\n    /**\r\n     * Accessor value used to identify/retrieve a natively-stored index buffer.\r\n     */\r\n    public nativeIndexBuffer?: NativeData;\r\n\r\n    /**\r\n     * Accessor value used to identify/retrieve a natively-stored vertex buffer.\r\n     */\r\n    public nativeVertexBuffer?: NativeData;\r\n}\r\n\r\n/**\r\n * Options to create the Native engine\r\n */\r\nexport interface NativeEngineOptions {\r\n    /**\r\n     * defines whether to adapt to the device's viewport characteristics (default: false)\r\n     */\r\n    adaptToDeviceRatio?: boolean;\r\n}\r\n\r\n/** @internal */\r\nclass CommandBufferEncoder {\r\n    private readonly _commandStream: NativeDataStream;\r\n    private readonly _pending = new Array<NativeData>();\r\n    private _isCommandBufferScopeActive = false;\r\n\r\n    public constructor(private readonly _engine: INativeEngine) {\r\n        this._commandStream = NativeEngine._createNativeDataStream();\r\n        this._engine.setCommandDataStream(this._commandStream);\r\n    }\r\n\r\n    public beginCommandScope() {\r\n        if (this._isCommandBufferScopeActive) {\r\n            throw new Error(\"Command scope already active.\");\r\n        }\r\n\r\n        this._isCommandBufferScopeActive = true;\r\n    }\r\n\r\n    public endCommandScope() {\r\n        if (!this._isCommandBufferScopeActive) {\r\n            throw new Error(\"Command scope is not active.\");\r\n        }\r\n\r\n        this._isCommandBufferScopeActive = false;\r\n        this._submit();\r\n    }\r\n\r\n    public startEncodingCommand(command: NativeData) {\r\n        this._commandStream.writeNativeData(command);\r\n    }\r\n\r\n    public encodeCommandArgAsUInt32(commandArg: number) {\r\n        this._commandStream.writeUint32(commandArg);\r\n    }\r\n\r\n    public encodeCommandArgAsUInt32s(commandArg: Uint32Array) {\r\n        this._commandStream.writeUint32Array(commandArg);\r\n    }\r\n\r\n    public encodeCommandArgAsInt32(commandArg: number) {\r\n        this._commandStream.writeInt32(commandArg);\r\n    }\r\n\r\n    public encodeCommandArgAsInt32s(commandArg: Int32Array) {\r\n        this._commandStream.writeInt32Array(commandArg);\r\n    }\r\n\r\n    public encodeCommandArgAsFloat32(commandArg: number) {\r\n        this._commandStream.writeFloat32(commandArg);\r\n    }\r\n\r\n    public encodeCommandArgAsFloat32s(commandArg: DeepImmutable<FloatArray>) {\r\n        this._commandStream.writeFloat32Array(commandArg);\r\n    }\r\n\r\n    public encodeCommandArgAsNativeData(commandArg: NativeData) {\r\n        this._commandStream.writeNativeData(commandArg);\r\n        this._pending.push(commandArg);\r\n    }\r\n\r\n    public finishEncodingCommand() {\r\n        if (!this._isCommandBufferScopeActive) {\r\n            this._submit();\r\n        }\r\n    }\r\n\r\n    private _submit() {\r\n        this._engine.submitCommands();\r\n        this._pending.length = 0;\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport class NativeEngine extends Engine {\r\n    // This must match the protocol version in NativeEngine.cpp\r\n    private static readonly PROTOCOL_VERSION = 8;\r\n\r\n    private readonly _engine: INativeEngine = new _native.Engine();\r\n    private readonly _camera: Nullable<INativeCamera> = _native.Camera ? new _native.Camera() : null;\r\n\r\n    private readonly _commandBufferEncoder = new CommandBufferEncoder(this._engine);\r\n\r\n    private _boundBuffersVertexArray: any = null;\r\n    private _currentDepthTest: number = _native.Engine.DEPTH_TEST_LEQUAL;\r\n    private _stencilTest = false;\r\n    private _stencilMask: number = 255;\r\n    private _stencilFunc: number = Constants.ALWAYS;\r\n    private _stencilFuncRef: number = 0;\r\n    private _stencilFuncMask: number = 255;\r\n    private _stencilOpStencilFail: number = Constants.KEEP;\r\n    private _stencilOpDepthFail: number = Constants.KEEP;\r\n    private _stencilOpStencilDepthPass: number = Constants.REPLACE;\r\n    private _zOffset: number = 0;\r\n    private _zOffsetUnits: number = 0;\r\n    private _depthWrite: boolean = true;\r\n\r\n    public setHardwareScalingLevel(level: number): void {\r\n        super.setHardwareScalingLevel(level);\r\n        this._engine.setHardwareScalingLevel(level);\r\n    }\r\n\r\n    public constructor(options: NativeEngineOptions = {}) {\r\n        super(null, false, undefined, options.adaptToDeviceRatio);\r\n\r\n        if (_native.Engine.PROTOCOL_VERSION !== NativeEngine.PROTOCOL_VERSION) {\r\n            throw new Error(`Protocol version mismatch: ${_native.Engine.PROTOCOL_VERSION} (Native) !== ${NativeEngine.PROTOCOL_VERSION} (JS)`);\r\n        }\r\n\r\n        if (this._engine.setDeviceLostCallback) {\r\n            this._engine.setDeviceLostCallback(() => {\r\n                this.onContextLostObservable.notifyObservers(this);\r\n                this._contextWasLost = true;\r\n                this._restoreEngineAfterContextLost();\r\n            });\r\n        }\r\n\r\n        this._webGLVersion = 2;\r\n        this.disableUniformBuffers = true;\r\n        this._shaderPlatformName = \"NATIVE\";\r\n\r\n        // TODO: Initialize this more correctly based on the hardware capabilities.\r\n        // Init caps\r\n\r\n        this._caps = {\r\n            maxTexturesImageUnits: 16,\r\n            maxVertexTextureImageUnits: 16,\r\n            maxCombinedTexturesImageUnits: 32,\r\n            maxTextureSize: _native.Engine.CAPS_LIMITS_MAX_TEXTURE_SIZE,\r\n            maxCubemapTextureSize: 512,\r\n            maxRenderTextureSize: 512,\r\n            maxVertexAttribs: 16,\r\n            maxVaryingVectors: 16,\r\n            maxFragmentUniformVectors: 16,\r\n            maxVertexUniformVectors: 16,\r\n            standardDerivatives: true,\r\n            astc: null,\r\n            pvrtc: null,\r\n            etc1: null,\r\n            etc2: null,\r\n            bptc: null,\r\n            maxAnisotropy: 16, // TODO: Retrieve this smartly. Currently set to D3D11 maximum allowable value.\r\n            uintIndices: true,\r\n            fragmentDepthSupported: false,\r\n            highPrecisionShaderSupported: true,\r\n            colorBufferFloat: false,\r\n            supportFloatTexturesResolve: false,\r\n            rg11b10ufColorRenderable: false,\r\n            textureFloat: true,\r\n            textureFloatLinearFiltering: false,\r\n            textureFloatRender: true,\r\n            textureHalfFloat: true,\r\n            textureHalfFloatLinearFiltering: false,\r\n            textureHalfFloatRender: true,\r\n            textureLOD: true,\r\n            texelFetch: false,\r\n            drawBuffersExtension: false,\r\n            depthTextureExtension: false,\r\n            vertexArrayObject: true,\r\n            instancedArrays: true,\r\n            supportOcclusionQuery: false,\r\n            canUseTimestampForTimerQuery: false,\r\n            blendMinMax: false,\r\n            maxMSAASamples: 16,\r\n            canUseGLInstanceID: true,\r\n            canUseGLVertexID: true,\r\n            supportComputeShaders: false,\r\n            supportSRGBBuffers: true,\r\n            supportTransformFeedbacks: false,\r\n            textureMaxLevel: false,\r\n            texture2DArrayMaxLayerCount: _native.Engine.CAPS_LIMITS_MAX_TEXTURE_LAYERS,\r\n            disableMorphTargetTexture: false,\r\n            parallelShaderCompile: { COMPLETION_STATUS_KHR: 0 },\r\n        };\r\n\r\n        this._features = {\r\n            forceBitmapOverHTMLImageElement: true,\r\n            supportRenderAndCopyToLodForFloatTextures: false,\r\n            supportDepthStencilTexture: false,\r\n            supportShadowSamplers: false,\r\n            uniformBufferHardCheckMatrix: false,\r\n            allowTexturePrefiltering: false,\r\n            trackUbosInFrame: false,\r\n            checkUbosContentBeforeUpload: false,\r\n            supportCSM: false,\r\n            basisNeedsPOT: false,\r\n            support3DTextures: false,\r\n            needTypeSuffixInShaderConstants: false,\r\n            supportMSAA: true,\r\n            supportSSAO2: false,\r\n            supportExtendedTextureFormats: false,\r\n            supportSwitchCaseInShader: false,\r\n            supportSyncTextureRead: false,\r\n            needsInvertingBitmap: true,\r\n            useUBOBindingCache: true,\r\n            needShaderCodeInlining: true,\r\n            needToAlwaysBindUniformBuffers: false,\r\n            supportRenderPasses: true,\r\n            supportSpriteInstancing: false,\r\n            forceVertexBufferStrideAndOffsetMultiple4Bytes: false,\r\n            _collectUbosUpdatedInFrame: false,\r\n        };\r\n\r\n        Tools.Log(\"Babylon Native (v\" + Engine.Version + \") launched\");\r\n\r\n        Tools.LoadScript = function (scriptUrl, onSuccess, onError, scriptId) {\r\n            Tools.LoadFile(\r\n                scriptUrl,\r\n                (data) => {\r\n                    Function(data as string).apply(null);\r\n                    if (onSuccess) {\r\n                        onSuccess();\r\n                    }\r\n                },\r\n                undefined,\r\n                undefined,\r\n                false,\r\n                (request, exception) => {\r\n                    if (onError) {\r\n                        onError(\"LoadScript Error\", exception);\r\n                    }\r\n                }\r\n            );\r\n        };\r\n\r\n        // Wrappers\r\n        if (typeof URL === \"undefined\") {\r\n            (window.URL as any) = {\r\n                createObjectURL: function () {},\r\n                revokeObjectURL: function () {},\r\n            };\r\n        }\r\n\r\n        if (typeof Blob === \"undefined\") {\r\n            (window.Blob as any) = function (v: any) {\r\n                return v;\r\n            };\r\n        }\r\n\r\n        // polyfill for Chakra\r\n        if (!Array.prototype.flat) {\r\n            Object.defineProperty(Array.prototype, \"flat\", {\r\n                configurable: true,\r\n                value: function flat() {\r\n                    const depth = isNaN(arguments[0]) ? 1 : Number(arguments[0]);\r\n\r\n                    return depth\r\n                        ? Array.prototype.reduce.call(\r\n                              this,\r\n                              function (acc: any, cur: any) {\r\n                                  if (Array.isArray(cur)) {\r\n                                      acc.push.apply(acc, flat.call(cur, depth - 1));\r\n                                  } else {\r\n                                      acc.push(cur);\r\n                                  }\r\n                                  return acc;\r\n                              },\r\n                              []\r\n                          )\r\n                        : Array.prototype.slice.call(this);\r\n                },\r\n                writable: true,\r\n            });\r\n        }\r\n\r\n        // Currently we do not fully configure the ThinEngine on construction of NativeEngine.\r\n        // Setup resolution scaling based on display settings.\r\n        const devicePixelRatio = window ? window.devicePixelRatio || 1.0 : 1.0;\r\n        this._hardwareScalingLevel = options.adaptToDeviceRatio ? 1.0 / devicePixelRatio : 1.0;\r\n        this._engine.setHardwareScalingLevel(this._hardwareScalingLevel);\r\n        this._lastDevicePixelRatio = devicePixelRatio;\r\n        this.resize();\r\n\r\n        const currentDepthFunction = this.getDepthFunction();\r\n        if (currentDepthFunction) {\r\n            this.setDepthFunction(currentDepthFunction);\r\n        }\r\n\r\n        // Shader processor\r\n        this._shaderProcessor = new WebGL2ShaderProcessor();\r\n\r\n        this.onNewSceneAddedObservable.add((scene) => {\r\n            const originalRender = scene.render;\r\n            scene.render = (...args: Parameters<typeof originalRender>) => {\r\n                this._commandBufferEncoder.beginCommandScope();\r\n                originalRender.apply(scene, args);\r\n                this._commandBufferEncoder.endCommandScope();\r\n            };\r\n        });\r\n    }\r\n\r\n    public dispose(): void {\r\n        super.dispose();\r\n        if (this._boundBuffersVertexArray) {\r\n            this._deleteVertexArray(this._boundBuffersVertexArray);\r\n        }\r\n        this._engine.dispose();\r\n    }\r\n\r\n    /** @internal */\r\n    public static _createNativeDataStream(): NativeDataStream {\r\n        return new NativeDataStream();\r\n    }\r\n\r\n    /**\r\n     * Can be used to override the current requestAnimationFrame requester.\r\n     * @internal\r\n     */\r\n    protected _queueNewFrame(bindedRenderFunction: any, requester?: any): number {\r\n        // Use the provided requestAnimationFrame, unless the requester is the window. In that case, we will default to the Babylon Native version of requestAnimationFrame.\r\n        if (requester.requestAnimationFrame && requester !== window) {\r\n            requester.requestAnimationFrame(bindedRenderFunction);\r\n        } else {\r\n            this._engine.requestAnimationFrame(bindedRenderFunction);\r\n        }\r\n        return 0;\r\n    }\r\n\r\n    protected _restoreEngineAfterContextLost(): void {\r\n        this._clearEmptyResources();\r\n\r\n        const depthTest = this._depthCullingState.depthTest; // backup those values because the call to initEngine / wipeCaches will reset them\r\n        const depthFunc = this._depthCullingState.depthFunc;\r\n        const depthMask = this._depthCullingState.depthMask;\r\n        const stencilTest = this._stencilState.stencilTest;\r\n\r\n        this._rebuildGraphicsResources();\r\n\r\n        this._depthCullingState.depthTest = depthTest;\r\n        this._depthCullingState.depthFunc = depthFunc;\r\n        this._depthCullingState.depthMask = depthMask;\r\n        this._stencilState.stencilTest = stencilTest;\r\n\r\n        this._flagContextRestored();\r\n    }\r\n\r\n    /**\r\n     * Override default engine behavior.\r\n     * @param framebuffer\r\n     */\r\n    public _bindUnboundFramebuffer(framebuffer: Nullable<WebGLFramebuffer>) {\r\n        if (this._currentFramebuffer !== framebuffer) {\r\n            if (this._currentFramebuffer) {\r\n                this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_UNBINDFRAMEBUFFER);\r\n                this._commandBufferEncoder.encodeCommandArgAsNativeData(this._currentFramebuffer as NativeFramebuffer);\r\n                this._commandBufferEncoder.finishEncodingCommand();\r\n            }\r\n\r\n            if (framebuffer) {\r\n                this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_BINDFRAMEBUFFER);\r\n                this._commandBufferEncoder.encodeCommandArgAsNativeData(framebuffer as NativeFramebuffer);\r\n                this._commandBufferEncoder.finishEncodingCommand();\r\n            }\r\n\r\n            this._currentFramebuffer = framebuffer;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets host document\r\n     * @returns the host document object\r\n     */\r\n    public getHostDocument(): Nullable<Document> {\r\n        return null;\r\n    }\r\n\r\n    public clear(color: Nullable<IColor4Like>, backBuffer: boolean, depth: boolean, stencil: boolean = false): void {\r\n        if (this.useReverseDepthBuffer) {\r\n            throw new Error(\"reverse depth buffer is not currently implemented\");\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_CLEAR);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(backBuffer && color ? 1 : 0);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(color ? color.r : 0);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(color ? color.g : 0);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(color ? color.b : 0);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(color ? color.a : 1);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(depth ? 1 : 0);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(1);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(stencil ? 1 : 0);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(0);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    public createIndexBuffer(indices: IndicesArray, updateable?: boolean, _label?: string): NativeDataBuffer {\r\n        const data = this._normalizeIndexData(indices);\r\n        const buffer = new NativeDataBuffer();\r\n        buffer.references = 1;\r\n        buffer.is32Bits = data.BYTES_PER_ELEMENT === 4;\r\n        if (data.byteLength) {\r\n            buffer.nativeIndexBuffer = this._engine.createIndexBuffer(data.buffer, data.byteOffset, data.byteLength, buffer.is32Bits, updateable ?? false);\r\n        }\r\n        return buffer;\r\n    }\r\n\r\n    public createVertexBuffer(vertices: DataArray, updateable?: boolean, _label?: string): NativeDataBuffer {\r\n        const data = ArrayBuffer.isView(vertices) ? vertices : new Float32Array(vertices);\r\n        const buffer = new NativeDataBuffer();\r\n        buffer.references = 1;\r\n        if (data.byteLength) {\r\n            buffer.nativeVertexBuffer = this._engine.createVertexBuffer(data.buffer, data.byteOffset, data.byteLength, updateable ?? false);\r\n        }\r\n        return buffer;\r\n    }\r\n\r\n    protected _recordVertexArrayObject(\r\n        vertexArray: any,\r\n        vertexBuffers: { [key: string]: VertexBuffer },\r\n        indexBuffer: Nullable<NativeDataBuffer>,\r\n        effect: Effect,\r\n        overrideVertexBuffers?: { [kind: string]: Nullable<VertexBuffer> }\r\n    ): void {\r\n        if (indexBuffer) {\r\n            this._engine.recordIndexBuffer(vertexArray, indexBuffer.nativeIndexBuffer!);\r\n        }\r\n\r\n        const attributes = effect.getAttributesNames();\r\n        for (let index = 0; index < attributes.length; index++) {\r\n            const location = effect.getAttributeLocation(index);\r\n            if (location >= 0) {\r\n                const kind = attributes[index];\r\n                let vertexBuffer: Nullable<VertexBuffer> = null;\r\n\r\n                if (overrideVertexBuffers) {\r\n                    vertexBuffer = overrideVertexBuffers[kind];\r\n                }\r\n                if (!vertexBuffer) {\r\n                    vertexBuffer = vertexBuffers[kind];\r\n                }\r\n\r\n                if (vertexBuffer) {\r\n                    const buffer = vertexBuffer.getBuffer() as Nullable<NativeDataBuffer>;\r\n                    if (buffer && buffer.nativeVertexBuffer) {\r\n                        this._engine.recordVertexBuffer(\r\n                            vertexArray,\r\n                            buffer.nativeVertexBuffer!,\r\n                            location,\r\n                            vertexBuffer.byteOffset,\r\n                            vertexBuffer.byteStride,\r\n                            vertexBuffer.getSize(),\r\n                            getNativeAttribType(vertexBuffer.type),\r\n                            vertexBuffer.normalized,\r\n                            vertexBuffer.getInstanceDivisor()\r\n                        );\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    public bindBuffers(vertexBuffers: { [key: string]: VertexBuffer }, indexBuffer: Nullable<NativeDataBuffer>, effect: Effect): void {\r\n        if (this._boundBuffersVertexArray) {\r\n            this._deleteVertexArray(this._boundBuffersVertexArray);\r\n        }\r\n        this._boundBuffersVertexArray = this._engine.createVertexArray();\r\n        this._recordVertexArrayObject(this._boundBuffersVertexArray, vertexBuffers, indexBuffer, effect);\r\n        this.bindVertexArrayObject(this._boundBuffersVertexArray);\r\n    }\r\n\r\n    public recordVertexArrayObject(\r\n        vertexBuffers: { [key: string]: VertexBuffer },\r\n        indexBuffer: Nullable<NativeDataBuffer>,\r\n        effect: Effect,\r\n        overrideVertexBuffers?: { [kind: string]: Nullable<VertexBuffer> }\r\n    ): WebGLVertexArrayObject {\r\n        const vertexArray = this._engine.createVertexArray();\r\n        this._recordVertexArrayObject(vertexArray, vertexBuffers, indexBuffer, effect, overrideVertexBuffers);\r\n        return vertexArray;\r\n    }\r\n\r\n    private _deleteVertexArray(vertexArray: NativeVertexArrayObject) {\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_DELETEVERTEXARRAY);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(vertexArray);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    public bindVertexArrayObject(vertexArray: WebGLVertexArrayObject): void {\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_BINDVERTEXARRAY);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(vertexArray as NativeVertexArrayObject);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    public releaseVertexArrayObject(vertexArray: WebGLVertexArrayObject) {\r\n        this._deleteVertexArray(vertexArray as NativeVertexArrayObject);\r\n    }\r\n\r\n    public getAttributes(pipelineContext: IPipelineContext, attributesNames: string[]): number[] {\r\n        const nativePipelineContext = pipelineContext as NativePipelineContext;\r\n        return this._engine.getAttributes(nativePipelineContext.program, attributesNames);\r\n    }\r\n\r\n    /**\r\n     * Draw a list of indexed primitives\r\n     * @param fillMode defines the primitive to use\r\n     * @param indexStart defines the starting index\r\n     * @param indexCount defines the number of index to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawElementsType(fillMode: number, indexStart: number, indexCount: number, instancesCount?: number): void {\r\n        // Apply states\r\n        this._drawCalls.addCount(1, false);\r\n\r\n        if (instancesCount && _native.Engine.COMMAND_DRAWINDEXEDINSTANCED) {\r\n            this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_DRAWINDEXEDINSTANCED);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(fillMode);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(indexStart);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(indexCount);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(instancesCount);\r\n        } else {\r\n            this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_DRAWINDEXED);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(fillMode);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(indexStart);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(indexCount);\r\n        }\r\n\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * Draw a list of unindexed primitives\r\n     * @param fillMode defines the primitive to use\r\n     * @param verticesStart defines the index of first vertex to draw\r\n     * @param verticesCount defines the count of vertices to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawArraysType(fillMode: number, verticesStart: number, verticesCount: number, instancesCount?: number): void {\r\n        // Apply states\r\n        this._drawCalls.addCount(1, false);\r\n\r\n        if (instancesCount && _native.Engine.COMMAND_DRAWINSTANCED) {\r\n            this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_DRAWINSTANCED);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(fillMode);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(verticesStart);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(verticesCount);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(instancesCount);\r\n        } else {\r\n            this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_DRAW);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(fillMode);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(verticesStart);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(verticesCount);\r\n        }\r\n\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        // }\r\n    }\r\n\r\n    public createPipelineContext(): IPipelineContext {\r\n        const isAsync = !!(this._caps.parallelShaderCompile && this._engine.createProgramAsync);\r\n        return new NativePipelineContext(this, isAsync);\r\n    }\r\n\r\n    public createMaterialContext(): IMaterialContext | undefined {\r\n        return undefined;\r\n    }\r\n\r\n    public createDrawContext(): IDrawContext | undefined {\r\n        return undefined;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _preparePipelineContext(\r\n        pipelineContext: IPipelineContext,\r\n        vertexSourceCode: string,\r\n        fragmentSourceCode: string,\r\n        createAsRaw: boolean,\r\n        _rawVertexSourceCode: string,\r\n        _rawFragmentSourceCode: string,\r\n        _rebuildRebind: any,\r\n        defines: Nullable<string>\r\n    ) {\r\n        if (createAsRaw) {\r\n            this.createRawShaderProgram();\r\n        } else {\r\n            this.createShaderProgram(pipelineContext, vertexSourceCode, fragmentSourceCode, defines);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _executeWhenRenderingStateIsCompiled(pipelineContext: IPipelineContext, action: () => void) {\r\n        const nativePipelineContext = pipelineContext as NativePipelineContext;\r\n        if (nativePipelineContext.isAsync) {\r\n            if (nativePipelineContext.onCompiled) {\r\n                const oldHandler = nativePipelineContext.onCompiled;\r\n                nativePipelineContext.onCompiled = () => {\r\n                    oldHandler();\r\n                    action();\r\n                };\r\n            } else {\r\n                nativePipelineContext.onCompiled = action;\r\n            }\r\n        } else {\r\n            action();\r\n        }\r\n    }\r\n\r\n    public createRawShaderProgram(): WebGLProgram {\r\n        throw new Error(\"Not Supported\");\r\n    }\r\n\r\n    public createShaderProgram(pipelineContext: IPipelineContext, vertexCode: string, fragmentCode: string, defines: Nullable<string>): WebGLProgram {\r\n        const nativePipelineContext = pipelineContext as NativePipelineContext;\r\n\r\n        this.onBeforeShaderCompilationObservable.notifyObservers(this);\r\n\r\n        const vertexInliner = new ShaderCodeInliner(vertexCode);\r\n        vertexInliner.processCode();\r\n        vertexCode = vertexInliner.code;\r\n\r\n        const fragmentInliner = new ShaderCodeInliner(fragmentCode);\r\n        fragmentInliner.processCode();\r\n        fragmentCode = fragmentInliner.code;\r\n\r\n        vertexCode = ThinEngine._ConcatenateShader(vertexCode, defines);\r\n        fragmentCode = ThinEngine._ConcatenateShader(fragmentCode, defines);\r\n\r\n        const onSuccess = () => {\r\n            nativePipelineContext.isCompiled = true;\r\n            nativePipelineContext.onCompiled?.();\r\n            this.onAfterShaderCompilationObservable.notifyObservers(this);\r\n        };\r\n\r\n        if (pipelineContext.isAsync) {\r\n            nativePipelineContext.program = this._engine.createProgramAsync(vertexCode, fragmentCode, onSuccess, (error: Error) => {\r\n                nativePipelineContext.compilationError = error;\r\n            });\r\n        } else {\r\n            try {\r\n                nativePipelineContext.program = this._engine.createProgram(vertexCode, fragmentCode);\r\n                onSuccess();\r\n            } catch (e) {\r\n                const message = e?.message;\r\n                throw new Error(\"SHADER ERROR\" + (typeof message === \"string\" ? \"\\n\" + message : \"\"));\r\n            }\r\n        }\r\n\r\n        return nativePipelineContext.program as WebGLProgram;\r\n    }\r\n\r\n    /**\r\n     * Inline functions in shader code that are marked to be inlined\r\n     * @param code code to inline\r\n     * @returns inlined code\r\n     */\r\n    public inlineShaderCode(code: string): string {\r\n        const sci = new ShaderCodeInliner(code);\r\n        sci.debug = false;\r\n        sci.processCode();\r\n        return sci.code;\r\n    }\r\n\r\n    protected _setProgram(program: WebGLProgram): void {\r\n        if (this._currentProgram !== program) {\r\n            this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETPROGRAM);\r\n            this._commandBufferEncoder.encodeCommandArgAsNativeData(program as NativeProgram);\r\n            this._commandBufferEncoder.finishEncodingCommand();\r\n            this._currentProgram = program;\r\n        }\r\n    }\r\n\r\n    public _deletePipelineContext(pipelineContext: IPipelineContext): void {\r\n        const nativePipelineContext = pipelineContext as NativePipelineContext;\r\n        if (nativePipelineContext && nativePipelineContext.program) {\r\n            this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_DELETEPROGRAM);\r\n            this._commandBufferEncoder.encodeCommandArgAsNativeData(nativePipelineContext.program);\r\n            this._commandBufferEncoder.finishEncodingCommand();\r\n        }\r\n    }\r\n\r\n    public getUniforms(pipelineContext: IPipelineContext, uniformsNames: string[]): WebGLUniformLocation[] {\r\n        const nativePipelineContext = pipelineContext as NativePipelineContext;\r\n        return this._engine.getUniforms(nativePipelineContext.program, uniformsNames);\r\n    }\r\n\r\n    public bindUniformBlock(pipelineContext: IPipelineContext, blockName: string, index: number): void {\r\n        // TODO\r\n        throw new Error(\"Not Implemented\");\r\n    }\r\n\r\n    public bindSamplers(effect: Effect): void {\r\n        const nativePipelineContext = effect.getPipelineContext() as NativePipelineContext;\r\n        this._setProgram(nativePipelineContext.program as WebGLProgram);\r\n\r\n        // TODO: share this with engine?\r\n        const samplers = effect.getSamplers();\r\n        for (let index = 0; index < samplers.length; index++) {\r\n            const uniform = effect.getUniform(samplers[index]);\r\n\r\n            if (uniform) {\r\n                this._boundUniforms[index] = uniform;\r\n            }\r\n        }\r\n        this._currentEffect = null;\r\n    }\r\n\r\n    public getRenderWidth(useScreen = false): number {\r\n        if (!useScreen && this._currentRenderTarget) {\r\n            return this._currentRenderTarget.width;\r\n        }\r\n\r\n        return this._engine.getRenderWidth();\r\n    }\r\n\r\n    public getRenderHeight(useScreen = false): number {\r\n        if (!useScreen && this._currentRenderTarget) {\r\n            return this._currentRenderTarget.height;\r\n        }\r\n\r\n        return this._engine.getRenderHeight();\r\n    }\r\n\r\n    public setViewport(viewport: IViewportLike, requiredWidth?: number, requiredHeight?: number): void {\r\n        this._cachedViewport = viewport;\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETVIEWPORT);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(viewport.x);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(viewport.y);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(viewport.width);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(viewport.height);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    public enableScissor(x: number, y: number, width: number, height: number): void {\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETSCISSOR);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(x);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(y);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(width);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(height);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    public disableScissor() {\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETSCISSOR);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(0);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(0);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(0);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(0);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    public setState(culling: boolean, zOffset: number = 0, force?: boolean, reverseSide = false, cullBackFaces?: boolean, stencil?: IStencilState, zOffsetUnits: number = 0): void {\r\n        this._zOffset = zOffset;\r\n        this._zOffsetUnits = zOffsetUnits;\r\n        if (this._zOffset !== 0) {\r\n            Tools.Warn(\"zOffset is not supported in Native engine.\");\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETSTATE);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(culling ? 1 : 0);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(zOffset);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(zOffsetUnits);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(this.cullBackFaces ?? cullBackFaces ?? true ? 1 : 0);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(reverseSide ? 1 : 0);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    /**\r\n     * Gets the client rect of native canvas.  Needed for InputManager.\r\n     * @returns a client rectangle\r\n     */\r\n    public getInputElementClientRect(): Nullable<DOMRect> {\r\n        const rect = {\r\n            bottom: this.getRenderHeight(),\r\n            height: this.getRenderHeight(),\r\n            left: 0,\r\n            right: this.getRenderWidth(),\r\n            top: 0,\r\n            width: this.getRenderWidth(),\r\n            x: 0,\r\n            y: 0,\r\n            toJSON: () => {},\r\n        };\r\n        return rect;\r\n    }\r\n\r\n    /**\r\n     * Set the z offset Factor to apply to current rendering\r\n     * @param value defines the offset to apply\r\n     */\r\n    public setZOffset(value: number): void {\r\n        if (value !== this._zOffset) {\r\n            this._zOffset = value;\r\n            this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETZOFFSET);\r\n            this._commandBufferEncoder.encodeCommandArgAsFloat32(this.useReverseDepthBuffer ? -value : value);\r\n            this._commandBufferEncoder.finishEncodingCommand();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the current value of the zOffset Factor\r\n     * @returns the current zOffset Factor state\r\n     */\r\n    public getZOffset(): number {\r\n        return this._zOffset;\r\n    }\r\n\r\n    /**\r\n     * Set the z offset Units to apply to current rendering\r\n     * @param value defines the offset to apply\r\n     */\r\n    public setZOffsetUnits(value: number): void {\r\n        if (value !== this._zOffsetUnits) {\r\n            this._zOffsetUnits = value;\r\n            this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETZOFFSETUNITS);\r\n            this._commandBufferEncoder.encodeCommandArgAsFloat32(this.useReverseDepthBuffer ? -value : value);\r\n            this._commandBufferEncoder.finishEncodingCommand();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the current value of the zOffset Units\r\n     * @returns the current zOffset Units state\r\n     */\r\n    public getZOffsetUnits(): number {\r\n        return this._zOffsetUnits;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable depth buffering\r\n     * @param enable defines the state to set\r\n     */\r\n    public setDepthBuffer(enable: boolean): void {\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETDEPTHTEST);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(enable ? this._currentDepthTest : _native.Engine.DEPTH_TEST_ALWAYS);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if depth writing is enabled\r\n     * @returns the current depth writing state\r\n     */\r\n    public getDepthWrite(): boolean {\r\n        return this._depthWrite;\r\n    }\r\n\r\n    public getDepthFunction(): Nullable<number> {\r\n        switch (this._currentDepthTest) {\r\n            case _native.Engine.DEPTH_TEST_NEVER:\r\n                return Constants.NEVER;\r\n            case _native.Engine.DEPTH_TEST_ALWAYS:\r\n                return Constants.ALWAYS;\r\n            case _native.Engine.DEPTH_TEST_GREATER:\r\n                return Constants.GREATER;\r\n            case _native.Engine.DEPTH_TEST_GEQUAL:\r\n                return Constants.GEQUAL;\r\n            case _native.Engine.DEPTH_TEST_NOTEQUAL:\r\n                return Constants.NOTEQUAL;\r\n            case _native.Engine.DEPTH_TEST_EQUAL:\r\n                return Constants.EQUAL;\r\n            case _native.Engine.DEPTH_TEST_LESS:\r\n                return Constants.LESS;\r\n            case _native.Engine.DEPTH_TEST_LEQUAL:\r\n                return Constants.LEQUAL;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    public setDepthFunction(depthFunc: number) {\r\n        let nativeDepthFunc = 0;\r\n        switch (depthFunc) {\r\n            case Constants.NEVER:\r\n                nativeDepthFunc = _native.Engine.DEPTH_TEST_NEVER;\r\n                break;\r\n            case Constants.ALWAYS:\r\n                nativeDepthFunc = _native.Engine.DEPTH_TEST_ALWAYS;\r\n                break;\r\n            case Constants.GREATER:\r\n                nativeDepthFunc = _native.Engine.DEPTH_TEST_GREATER;\r\n                break;\r\n            case Constants.GEQUAL:\r\n                nativeDepthFunc = _native.Engine.DEPTH_TEST_GEQUAL;\r\n                break;\r\n            case Constants.NOTEQUAL:\r\n                nativeDepthFunc = _native.Engine.DEPTH_TEST_NOTEQUAL;\r\n                break;\r\n            case Constants.EQUAL:\r\n                nativeDepthFunc = _native.Engine.DEPTH_TEST_EQUAL;\r\n                break;\r\n            case Constants.LESS:\r\n                nativeDepthFunc = _native.Engine.DEPTH_TEST_LESS;\r\n                break;\r\n            case Constants.LEQUAL:\r\n                nativeDepthFunc = _native.Engine.DEPTH_TEST_LEQUAL;\r\n                break;\r\n        }\r\n\r\n        this._currentDepthTest = nativeDepthFunc;\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETDEPTHTEST);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(this._currentDepthTest);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    /**\r\n     * Enable or disable depth writing\r\n     * @param enable defines the state to set\r\n     */\r\n    public setDepthWrite(enable: boolean): void {\r\n        this._depthWrite = enable;\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETDEPTHWRITE);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(Number(enable));\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    /**\r\n     * Enable or disable color writing\r\n     * @param enable defines the state to set\r\n     */\r\n    public setColorWrite(enable: boolean): void {\r\n        this._colorWrite = enable;\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETCOLORWRITE);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(Number(enable));\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if color writing is enabled\r\n     * @returns the current color writing state\r\n     */\r\n    public getColorWrite(): boolean {\r\n        return this._colorWrite;\r\n    }\r\n\r\n    private applyStencil(): void {\r\n        this._setStencil(\r\n            this._stencilMask,\r\n            getNativeStencilOpFail(this._stencilOpStencilFail),\r\n            getNativeStencilDepthFail(this._stencilOpDepthFail),\r\n            getNativeStencilDepthPass(this._stencilOpStencilDepthPass),\r\n            getNativeStencilFunc(this._stencilFunc),\r\n            this._stencilFuncRef\r\n        );\r\n    }\r\n\r\n    private _setStencil(mask: number, stencilOpFail: number, depthOpFail: number, depthOpPass: number, func: number, ref: number) {\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETSTENCIL);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(mask);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(stencilOpFail);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(depthOpFail);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(depthOpPass);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(func);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(ref);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    /**\r\n     * Enable or disable the stencil buffer\r\n     * @param enable defines if the stencil buffer must be enabled or disabled\r\n     */\r\n    public setStencilBuffer(enable: boolean): void {\r\n        this._stencilTest = enable;\r\n        if (enable) {\r\n            this.applyStencil();\r\n        } else {\r\n            this._setStencil(\r\n                255,\r\n                _native.Engine.STENCIL_OP_FAIL_S_KEEP,\r\n                _native.Engine.STENCIL_OP_FAIL_Z_KEEP,\r\n                _native.Engine.STENCIL_OP_PASS_Z_KEEP,\r\n                _native.Engine.STENCIL_TEST_ALWAYS,\r\n                0\r\n            );\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if stencil buffer is enabled\r\n     * @returns the current stencil buffer state\r\n     */\r\n    public getStencilBuffer(): boolean {\r\n        return this._stencilTest;\r\n    }\r\n\r\n    /**\r\n     * Gets the current stencil operation when stencil passes\r\n     * @returns a number defining stencil operation to use when stencil passes\r\n     */\r\n    public getStencilOperationPass(): number {\r\n        return this._stencilOpStencilDepthPass;\r\n    }\r\n\r\n    /**\r\n     * Sets the stencil operation to use when stencil passes\r\n     * @param operation defines the stencil operation to use when stencil passes\r\n     */\r\n    public setStencilOperationPass(operation: number): void {\r\n        this._stencilOpStencilDepthPass = operation;\r\n        this.applyStencil();\r\n    }\r\n\r\n    /**\r\n     * Sets the current stencil mask\r\n     * @param mask defines the new stencil mask to use\r\n     */\r\n    public setStencilMask(mask: number): void {\r\n        this._stencilMask = mask;\r\n        this.applyStencil();\r\n    }\r\n\r\n    /**\r\n     * Sets the current stencil function\r\n     * @param stencilFunc defines the new stencil function to use\r\n     */\r\n    public setStencilFunction(stencilFunc: number) {\r\n        this._stencilFunc = stencilFunc;\r\n        this.applyStencil();\r\n    }\r\n\r\n    /**\r\n     * Sets the current stencil reference\r\n     * @param reference defines the new stencil reference to use\r\n     */\r\n    public setStencilFunctionReference(reference: number) {\r\n        this._stencilFuncRef = reference;\r\n        this.applyStencil();\r\n    }\r\n\r\n    /**\r\n     * Sets the current stencil mask\r\n     * @param mask defines the new stencil mask to use\r\n     */\r\n    public setStencilFunctionMask(mask: number) {\r\n        this._stencilFuncMask = mask;\r\n    }\r\n\r\n    /**\r\n     * Sets the stencil operation to use when stencil fails\r\n     * @param operation defines the stencil operation to use when stencil fails\r\n     */\r\n    public setStencilOperationFail(operation: number): void {\r\n        this._stencilOpStencilFail = operation;\r\n        this.applyStencil();\r\n    }\r\n\r\n    /**\r\n     * Sets the stencil operation to use when depth fails\r\n     * @param operation defines the stencil operation to use when depth fails\r\n     */\r\n    public setStencilOperationDepthFail(operation: number): void {\r\n        this._stencilOpDepthFail = operation;\r\n        this.applyStencil();\r\n    }\r\n\r\n    /**\r\n     * Gets the current stencil mask\r\n     * @returns a number defining the new stencil mask to use\r\n     */\r\n    public getStencilMask(): number {\r\n        return this._stencilMask;\r\n    }\r\n\r\n    /**\r\n     * Gets the current stencil function\r\n     * @returns a number defining the stencil function to use\r\n     */\r\n    public getStencilFunction(): number {\r\n        return this._stencilFunc;\r\n    }\r\n\r\n    /**\r\n     * Gets the current stencil reference value\r\n     * @returns a number defining the stencil reference value to use\r\n     */\r\n    public getStencilFunctionReference(): number {\r\n        return this._stencilFuncRef;\r\n    }\r\n\r\n    /**\r\n     * Gets the current stencil mask\r\n     * @returns a number defining the stencil mask to use\r\n     */\r\n    public getStencilFunctionMask(): number {\r\n        return this._stencilFuncMask;\r\n    }\r\n\r\n    /**\r\n     * Gets the current stencil operation when stencil fails\r\n     * @returns a number defining stencil operation to use when stencil fails\r\n     */\r\n    public getStencilOperationFail(): number {\r\n        return this._stencilOpStencilFail;\r\n    }\r\n\r\n    /**\r\n     * Gets the current stencil operation when depth fails\r\n     * @returns a number defining stencil operation to use when depth fails\r\n     */\r\n    public getStencilOperationDepthFail(): number {\r\n        return this._stencilOpDepthFail;\r\n    }\r\n\r\n    /**\r\n     * Sets alpha constants used by some alpha blending modes\r\n     * @param r defines the red component\r\n     * @param g defines the green component\r\n     * @param b defines the blue component\r\n     * @param a defines the alpha component\r\n     */\r\n    public setAlphaConstants(r: number, g: number, b: number, a: number) {\r\n        throw new Error(\"Setting alpha blend constant color not yet implemented.\");\r\n    }\r\n\r\n    /**\r\n     * Sets the current alpha mode\r\n     * @param mode defines the mode to use (one of the BABYLON.Constants.ALPHA_XXX)\r\n     * @param noDepthWriteChange defines if depth writing state should remains unchanged (false by default)\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/advanced/transparent_rendering\r\n     */\r\n    public setAlphaMode(mode: number, noDepthWriteChange: boolean = false): void {\r\n        if (this._alphaMode === mode) {\r\n            return;\r\n        }\r\n\r\n        const nativeMode = getNativeAlphaMode(mode);\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETBLENDMODE);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(nativeMode);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n\r\n        if (!noDepthWriteChange) {\r\n            this.setDepthWrite(mode === Constants.ALPHA_DISABLE);\r\n        }\r\n\r\n        this._alphaMode = mode;\r\n    }\r\n\r\n    /**\r\n     * Gets the current alpha mode\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/advanced/transparent_rendering\r\n     * @returns the current alpha mode\r\n     */\r\n    public getAlphaMode(): number {\r\n        return this._alphaMode;\r\n    }\r\n\r\n    public setInt(uniform: WebGLUniformLocation, int: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETINT);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsInt32(int);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setIntArray(uniform: WebGLUniformLocation, array: Int32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETINTARRAY);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsInt32s(array);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setIntArray2(uniform: WebGLUniformLocation, array: Int32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETINTARRAY2);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsInt32s(array);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setIntArray3(uniform: WebGLUniformLocation, array: Int32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETINTARRAY3);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsInt32s(array);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setIntArray4(uniform: WebGLUniformLocation, array: Int32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETINTARRAY4);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsInt32s(array);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setFloatArray(uniform: WebGLUniformLocation, array: Float32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETFLOATARRAY);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32s(array);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setFloatArray2(uniform: WebGLUniformLocation, array: Float32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETFLOATARRAY2);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32s(array);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setFloatArray3(uniform: WebGLUniformLocation, array: Float32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETFLOATARRAY3);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32s(array);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setFloatArray4(uniform: WebGLUniformLocation, array: Float32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETFLOATARRAY4);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32s(array);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setArray(uniform: WebGLUniformLocation, array: number[]): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        return this.setFloatArray(uniform, new Float32Array(array));\r\n    }\r\n\r\n    public setArray2(uniform: WebGLUniformLocation, array: number[]): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        return this.setFloatArray2(uniform, new Float32Array(array));\r\n    }\r\n\r\n    public setArray3(uniform: WebGLUniformLocation, array: number[]): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        return this.setFloatArray3(uniform, new Float32Array(array));\r\n    }\r\n\r\n    public setArray4(uniform: WebGLUniformLocation, array: number[]): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        return this.setFloatArray4(uniform, new Float32Array(array));\r\n    }\r\n\r\n    public setMatrices(uniform: WebGLUniformLocation, matrices: DeepImmutable<FloatArray>): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETMATRICES);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32s(matrices);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n\r\n        return true;\r\n    }\r\n\r\n    public setMatrix3x3(uniform: WebGLUniformLocation, matrix: Float32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETMATRIX3X3);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32s(matrix);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setMatrix2x2(uniform: WebGLUniformLocation, matrix: Float32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETMATRIX2X2);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32s(matrix);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setFloat(uniform: WebGLUniformLocation, value: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETFLOAT);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(value);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setFloat2(uniform: WebGLUniformLocation, x: number, y: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETFLOAT2);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(x);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(y);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setFloat3(uniform: WebGLUniformLocation, x: number, y: number, z: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETFLOAT3);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(x);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(y);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(z);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setFloat4(uniform: WebGLUniformLocation, x: number, y: number, z: number, w: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETFLOAT4);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform as any as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(x);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(y);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(z);\r\n        this._commandBufferEncoder.encodeCommandArgAsFloat32(w);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n        return true;\r\n    }\r\n\r\n    public setColor3(uniform: WebGLUniformLocation, color3: IColor3Like): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this.setFloat3(uniform, color3.r, color3.g, color3.b);\r\n        return true;\r\n    }\r\n\r\n    public setColor4(uniform: WebGLUniformLocation, color3: IColor3Like, alpha: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this.setFloat4(uniform, color3.r, color3.g, color3.b, alpha);\r\n        return true;\r\n    }\r\n\r\n    public wipeCaches(bruteForce?: boolean): void {\r\n        if (this.preventCacheWipeBetweenFrames) {\r\n            return;\r\n        }\r\n        this.resetTextureCache();\r\n        this._currentEffect = null;\r\n\r\n        if (bruteForce) {\r\n            this._currentProgram = null;\r\n\r\n            this._stencilStateComposer.reset();\r\n            this._depthCullingState.reset();\r\n            this._alphaState.reset();\r\n        }\r\n\r\n        this._cachedVertexBuffers = null;\r\n        this._cachedIndexBuffer = null;\r\n        this._cachedEffectForVertexBuffers = null;\r\n    }\r\n\r\n    protected _createTexture(): WebGLTexture {\r\n        return this._engine.createTexture();\r\n    }\r\n\r\n    protected _deleteTexture(texture: Nullable<WebGLTexture>): void {\r\n        if (texture) {\r\n            this._engine.deleteTexture(texture as NativeTexture);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the content of a dynamic texture\r\n     * @param texture defines the texture to update\r\n     * @param canvas defines the canvas containing the source\r\n     * @param invertY defines if data must be stored with Y axis inverted\r\n     * @param premulAlpha defines if alpha is stored as premultiplied\r\n     * @param format defines the format of the data\r\n     */\r\n    public updateDynamicTexture(texture: Nullable<InternalTexture>, canvas: any, invertY: boolean, premulAlpha: boolean = false, format?: number): void {\r\n        if (premulAlpha === void 0) {\r\n            premulAlpha = false;\r\n        }\r\n\r\n        if (!!texture && !!texture._hardwareTexture) {\r\n            const source = canvas.getCanvasTexture();\r\n            const destination = texture._hardwareTexture.underlyingResource;\r\n            this._engine.copyTexture(destination, source);\r\n            texture.isReady = true;\r\n        }\r\n    }\r\n\r\n    public createDynamicTexture(width: number, height: number, generateMipMaps: boolean, samplingMode: number): InternalTexture {\r\n        // it's not possible to create 0x0 texture sized. Many bgfx methods assume texture size is at least 1x1(best case).\r\n        // Worst case is getting a crash/assert.\r\n        width = Math.max(width, 1);\r\n        height = Math.max(height, 1);\r\n        return this.createRawTexture(new Uint8Array(width * height * 4), width, height, Constants.TEXTUREFORMAT_RGBA, false, false, samplingMode);\r\n    }\r\n\r\n    public createVideoElement(constraints: MediaTrackConstraints): any {\r\n        // create native object depending on stream. Only NativeCamera is supported for now.\r\n        if (this._camera) {\r\n            return this._camera.createVideo(constraints);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    public updateVideoTexture(texture: Nullable<InternalTexture>, video: HTMLVideoElement, invertY: boolean): void {\r\n        if (texture && texture._hardwareTexture && this._camera) {\r\n            const webGLTexture = texture._hardwareTexture.underlyingResource;\r\n            this._camera.updateVideoTexture(webGLTexture, video, invertY);\r\n        }\r\n    }\r\n\r\n    public createRawTexture(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        format: number,\r\n        generateMipMaps: boolean,\r\n        invertY: boolean,\r\n        samplingMode: number,\r\n        compression: Nullable<string> = null,\r\n        type: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        creationFlags: number = 0,\r\n        useSRGBBuffer: boolean = false\r\n    ): InternalTexture {\r\n        const texture = new InternalTexture(this, InternalTextureSource.Raw);\r\n\r\n        texture.format = format;\r\n        texture.generateMipMaps = generateMipMaps;\r\n        texture.samplingMode = samplingMode;\r\n        texture.invertY = invertY;\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.width = texture.baseWidth;\r\n        texture.height = texture.baseHeight;\r\n        texture._compression = compression;\r\n        texture.type = type;\r\n        texture._useSRGBBuffer = this._getUseSRGBBuffer(useSRGBBuffer, !generateMipMaps);\r\n\r\n        this.updateRawTexture(texture, data, format, invertY, compression, type, texture._useSRGBBuffer);\r\n\r\n        if (texture._hardwareTexture) {\r\n            const webGLTexture = texture._hardwareTexture.underlyingResource;\r\n            const filter = getNativeSamplingMode(samplingMode);\r\n            this._setTextureSampling(webGLTexture, filter);\r\n        }\r\n\r\n        this._internalTexturesCache.push(texture);\r\n        return texture;\r\n    }\r\n\r\n    public createRawTexture2DArray(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        depth: number,\r\n        format: number,\r\n        generateMipMaps: boolean,\r\n        invertY: boolean,\r\n        samplingMode: number,\r\n        compression: Nullable<string> = null,\r\n        textureType = Constants.TEXTURETYPE_UNSIGNED_INT\r\n    ): InternalTexture {\r\n        const texture = new InternalTexture(this, InternalTextureSource.Raw2DArray);\r\n\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.baseDepth = depth;\r\n        texture.width = width;\r\n        texture.height = height;\r\n        texture.depth = depth;\r\n        texture.format = format;\r\n        texture.type = textureType;\r\n        texture.generateMipMaps = generateMipMaps;\r\n        texture.samplingMode = samplingMode;\r\n        texture.is2DArray = true;\r\n\r\n        if (texture._hardwareTexture) {\r\n            const nativeTexture = texture._hardwareTexture.underlyingResource;\r\n            this._engine.loadRawTexture2DArray(nativeTexture, data, width, height, depth, getNativeTextureFormat(format, textureType), generateMipMaps, invertY);\r\n\r\n            const filter = getNativeSamplingMode(samplingMode);\r\n            this._setTextureSampling(nativeTexture, filter);\r\n        }\r\n\r\n        texture.isReady = true;\r\n\r\n        this._internalTexturesCache.push(texture);\r\n        return texture;\r\n    }\r\n\r\n    public updateRawTexture(\r\n        texture: Nullable<InternalTexture>,\r\n        bufferView: Nullable<ArrayBufferView>,\r\n        format: number,\r\n        invertY: boolean,\r\n        compression: Nullable<string> = null,\r\n        type: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        useSRGBBuffer: boolean = false\r\n    ): void {\r\n        if (!texture) {\r\n            return;\r\n        }\r\n\r\n        if (bufferView && texture._hardwareTexture) {\r\n            const underlyingResource = texture._hardwareTexture.underlyingResource;\r\n            this._engine.loadRawTexture(\r\n                underlyingResource,\r\n                bufferView,\r\n                texture.width,\r\n                texture.height,\r\n                getNativeTextureFormat(format, type),\r\n                texture.generateMipMaps,\r\n                texture.invertY\r\n            );\r\n        }\r\n\r\n        texture.isReady = true;\r\n    }\r\n\r\n    // TODO: Refactor to share more logic with babylon.engine.ts version.\r\n    /**\r\n     * Usually called from Texture.ts.\r\n     * Passed information to create a NativeTexture\r\n     * @param url defines a value which contains one of the following:\r\n     * * A conventional http URL, e.g. 'http://...' or 'file://...'\r\n     * * A base64 string of in-line texture data, e.g. 'data:image/jpg;base64,/...'\r\n     * * An indicator that data being passed using the buffer parameter, e.g. 'data:mytexture.jpg'\r\n     * @param noMipmap defines a boolean indicating that no mipmaps shall be generated.  Ignored for compressed textures.  They must be in the file\r\n     * @param invertY when true, image is flipped when loaded.  You probably want true. Certain compressed textures may invert this if their default is inverted (eg. ktx)\r\n     * @param scene needed for loading to the correct scene\r\n     * @param samplingMode mode with should be used sample / access the texture (Default: Texture.TRILINEAR_SAMPLINGMODE)\r\n     * @param onLoad optional callback to be called upon successful completion\r\n     * @param onError optional callback to be called upon failure\r\n     * @param buffer a source of a file previously fetched as either a base64 string, an ArrayBuffer (compressed or image format), HTMLImageElement (image format), or a Blob\r\n     * @param fallback an internal argument in case the function must be called again, due to etc1 not having alpha capabilities\r\n     * @param format internal format.  Default: RGB when extension is '.jpg' else RGBA.  Ignored for compressed textures\r\n     * @param forcedExtension defines the extension to use to pick the right loader\r\n     * @param mimeType defines an optional mime type\r\n     * @param loaderOptions options to be passed to the loader\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n     * @returns a InternalTexture for assignment back into BABYLON.Texture\r\n     */\r\n    public createTexture(\r\n        url: Nullable<string>,\r\n        noMipmap: boolean,\r\n        invertY: boolean,\r\n        scene: Nullable<ISceneLike>,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        onLoad: Nullable<(texture: InternalTexture) => void> = null,\r\n        onError: Nullable<(message: string, exception: any) => void> = null,\r\n        buffer: Nullable<string | ArrayBuffer | ArrayBufferView | HTMLImageElement | Blob | ImageBitmap> = null,\r\n        fallback: Nullable<InternalTexture> = null,\r\n        format: Nullable<number> = null,\r\n        forcedExtension: Nullable<string> = null,\r\n        mimeType?: string,\r\n        loaderOptions?: any,\r\n        creationFlags?: number,\r\n        useSRGBBuffer = false\r\n    ): InternalTexture {\r\n        url = url || \"\";\r\n        const fromData = url.substr(0, 5) === \"data:\";\r\n        //const fromBlob = url.substr(0, 5) === \"blob:\";\r\n        const isBase64 = fromData && url.indexOf(\";base64,\") !== -1;\r\n\r\n        const texture = fallback ? fallback : new InternalTexture(this, InternalTextureSource.Url);\r\n\r\n        const originalUrl = url;\r\n        if (this._transformTextureUrl && !isBase64 && !fallback && !buffer) {\r\n            url = this._transformTextureUrl(url);\r\n        }\r\n\r\n        // establish the file extension, if possible\r\n        const lastDot = url.lastIndexOf(\".\");\r\n        const extension = forcedExtension ? forcedExtension : lastDot > -1 ? url.substring(lastDot).toLowerCase() : \"\";\r\n\r\n        let loader: Nullable<IInternalTextureLoader> = null;\r\n        for (const availableLoader of Engine._TextureLoaders) {\r\n            if (availableLoader.canLoad(extension)) {\r\n                loader = availableLoader;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (scene) {\r\n            scene.addPendingData(texture);\r\n        }\r\n        texture.url = url;\r\n        texture.generateMipMaps = !noMipmap;\r\n        texture.samplingMode = samplingMode;\r\n        texture.invertY = invertY;\r\n        texture._useSRGBBuffer = this._getUseSRGBBuffer(useSRGBBuffer, noMipmap);\r\n\r\n        if (!this.doNotHandleContextLost) {\r\n            // Keep a link to the buffer only if we plan to handle context lost\r\n            texture._buffer = buffer;\r\n        }\r\n\r\n        let onLoadObserver: Nullable<Observer<InternalTexture>> = null;\r\n        if (onLoad && !fallback) {\r\n            onLoadObserver = texture.onLoadedObservable.add(onLoad);\r\n        }\r\n\r\n        if (!fallback) {\r\n            this._internalTexturesCache.push(texture);\r\n        }\r\n\r\n        const onInternalError = (message?: string, exception?: any) => {\r\n            if (scene) {\r\n                scene.removePendingData(texture);\r\n            }\r\n\r\n            if (url === originalUrl) {\r\n                if (onLoadObserver) {\r\n                    texture.onLoadedObservable.remove(onLoadObserver);\r\n                }\r\n\r\n                if (EngineStore.UseFallbackTexture) {\r\n                    this.createTexture(EngineStore.FallbackTexture, noMipmap, texture.invertY, scene, samplingMode, null, onError, buffer, texture);\r\n                }\r\n\r\n                if (onError) {\r\n                    onError((message || \"Unknown error\") + (EngineStore.UseFallbackTexture ? \" - Fallback texture was used\" : \"\"), exception);\r\n                }\r\n            } else {\r\n                // fall back to the original url if the transformed url fails to load\r\n                Logger.Warn(`Failed to load ${url}, falling back to ${originalUrl}`);\r\n                this.createTexture(originalUrl, noMipmap, texture.invertY, scene, samplingMode, onLoad, onError, buffer, texture, format, forcedExtension, mimeType, loaderOptions);\r\n            }\r\n        };\r\n\r\n        // processing for non-image formats\r\n        if (loader) {\r\n            throw new Error(\"Loading textures from IInternalTextureLoader not yet implemented.\");\r\n        } else {\r\n            const onload = (data: ArrayBufferView) => {\r\n                if (!texture._hardwareTexture) {\r\n                    if (scene) {\r\n                        scene.removePendingData(texture);\r\n                    }\r\n\r\n                    return;\r\n                }\r\n\r\n                const underlyingResource = texture._hardwareTexture.underlyingResource;\r\n\r\n                this._engine.loadTexture(\r\n                    underlyingResource,\r\n                    data,\r\n                    !noMipmap,\r\n                    invertY,\r\n                    texture._useSRGBBuffer,\r\n                    () => {\r\n                        texture.baseWidth = this._engine.getTextureWidth(underlyingResource);\r\n                        texture.baseHeight = this._engine.getTextureHeight(underlyingResource);\r\n                        texture.width = texture.baseWidth;\r\n                        texture.height = texture.baseHeight;\r\n                        texture.isReady = true;\r\n\r\n                        const filter = getNativeSamplingMode(samplingMode);\r\n                        this._setTextureSampling(underlyingResource, filter);\r\n\r\n                        if (scene) {\r\n                            scene.removePendingData(texture);\r\n                        }\r\n\r\n                        texture.onLoadedObservable.notifyObservers(texture);\r\n                        texture.onLoadedObservable.clear();\r\n                    },\r\n                    () => {\r\n                        throw new Error(\"Could not load a native texture.\");\r\n                    }\r\n                );\r\n            };\r\n\r\n            if (fromData && buffer) {\r\n                if (buffer instanceof ArrayBuffer) {\r\n                    onload(new Uint8Array(buffer));\r\n                } else if (ArrayBuffer.isView(buffer)) {\r\n                    onload(buffer);\r\n                } else if (typeof buffer === \"string\") {\r\n                    onload(new Uint8Array(Tools.DecodeBase64(buffer)));\r\n                } else {\r\n                    throw new Error(\"Unsupported buffer type\");\r\n                }\r\n            } else {\r\n                if (isBase64) {\r\n                    onload(new Uint8Array(Tools.DecodeBase64(url)));\r\n                } else {\r\n                    this._loadFile(\r\n                        url,\r\n                        (data) => onload(new Uint8Array(data as ArrayBuffer)),\r\n                        undefined,\r\n                        undefined,\r\n                        true,\r\n                        (request?: IWebRequest, exception?: any) => {\r\n                            onInternalError(\"Unable to load \" + (request ? request.responseURL : url, exception));\r\n                        }\r\n                    );\r\n                }\r\n            }\r\n        }\r\n\r\n        return texture;\r\n    }\r\n\r\n    /**\r\n     * Wraps an external native texture in a Babylon texture.\r\n     * @param texture defines the external texture\r\n     * @param hasMipMaps defines whether the external texture has mip maps\r\n     * @param samplingMode defines the sampling mode for the external texture (default: Constants.TEXTURE_TRILINEAR_SAMPLINGMODE)\r\n     * @returns the babylon internal texture\r\n     */\r\n    public wrapNativeTexture(texture: NativeTexture, hasMipMaps: boolean = false, samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE): InternalTexture {\r\n        const hardwareTexture = new NativeHardwareTexture(texture, this._engine);\r\n        const internalTexture = new InternalTexture(this, InternalTextureSource.Unknown, true);\r\n        internalTexture._hardwareTexture = hardwareTexture;\r\n        internalTexture.baseWidth = this._engine.getTextureWidth(texture);\r\n        internalTexture.baseHeight = this._engine.getTextureHeight(texture);\r\n        internalTexture.width = internalTexture.baseWidth;\r\n        internalTexture.height = internalTexture.baseHeight;\r\n        internalTexture.isReady = true;\r\n        internalTexture.useMipMaps = hasMipMaps;\r\n        this.updateTextureSamplingMode(samplingMode, internalTexture);\r\n        return internalTexture;\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Wraps an external web gl texture in a Babylon texture.\r\n     * @returns the babylon internal texture\r\n     */\r\n    public wrapWebGLTexture(): InternalTexture {\r\n        throw new Error(\"wrapWebGLTexture is not supported, use wrapNativeTexture instead.\");\r\n    }\r\n\r\n    public _createDepthStencilTexture(size: TextureSize, options: DepthTextureCreationOptions, rtWrapper: RenderTargetWrapper): InternalTexture {\r\n        // TODO: handle other options?\r\n        const generateStencil = options.generateStencil || false;\r\n        const samples = options.samples || 1;\r\n\r\n        const nativeRTWrapper = rtWrapper as NativeRenderTargetWrapper;\r\n        const texture = new InternalTexture(this, InternalTextureSource.DepthStencil);\r\n\r\n        const width = (<{ width: number; height: number; layers?: number }>size).width ?? <number>size;\r\n        const height = (<{ width: number; height: number; layers?: number }>size).height ?? <number>size;\r\n\r\n        const framebuffer = this._engine.createFrameBuffer(texture._hardwareTexture!.underlyingResource, width, height, generateStencil, true, samples);\r\n        nativeRTWrapper._framebufferDepthStencil = framebuffer;\r\n        return texture;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseFramebufferObjects(framebuffer: Nullable<NativeFramebuffer>): void {\r\n        if (framebuffer) {\r\n            this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_DELETEFRAMEBUFFER);\r\n            this._commandBufferEncoder.encodeCommandArgAsNativeData(framebuffer as NativeData);\r\n            this._commandBufferEncoder.finishEncodingCommand();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal Engine abstraction for loading and creating an image bitmap from a given source string.\r\n     * @param imageSource source to load the image from.\r\n     * @param options An object that sets options for the image's extraction.\r\n     * @returns ImageBitmap\r\n     */\r\n    public _createImageBitmapFromSource(imageSource: string, options?: ImageBitmapOptions): Promise<ImageBitmap> {\r\n        const promise = new Promise<ImageBitmap>((resolve, reject) => {\r\n            const image = this.createCanvasImage();\r\n            image.onload = () => {\r\n                try {\r\n                    const imageBitmap = this._engine.createImageBitmap(image);\r\n                    resolve(imageBitmap);\r\n                } catch (error) {\r\n                    reject(`Error loading image ${image.src} with exception: ${error}`);\r\n                }\r\n            };\r\n            image.onerror = (error) => {\r\n                reject(`Error loading image ${image.src} with exception: ${error}`);\r\n            };\r\n\r\n            image.src = imageSource;\r\n        });\r\n\r\n        return promise;\r\n    }\r\n\r\n    /**\r\n     * Engine abstraction for createImageBitmap\r\n     * @param image source for image\r\n     * @param options An object that sets options for the image's extraction.\r\n     * @returns ImageBitmap\r\n     */\r\n    public createImageBitmap(image: ImageBitmapSource, options?: ImageBitmapOptions): Promise<ImageBitmap> {\r\n        return new Promise((resolve, reject) => {\r\n            if (Array.isArray(image)) {\r\n                const arr = <Array<ArrayBufferView>>image;\r\n                if (arr.length) {\r\n                    const image = this._engine.createImageBitmap(arr[0]);\r\n                    if (image) {\r\n                        resolve(image);\r\n                        return;\r\n                    }\r\n                }\r\n            }\r\n            reject(`Unsupported data for createImageBitmap.`);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Resize an image and returns the image data as an uint8array\r\n     * @param image image to resize\r\n     * @param bufferWidth destination buffer width\r\n     * @param bufferHeight destination buffer height\r\n     * @returns an uint8array containing RGBA values of bufferWidth * bufferHeight size\r\n     */\r\n    public resizeImageBitmap(image: ImageBitmap, bufferWidth: number, bufferHeight: number): Uint8Array {\r\n        return this._engine.resizeImageBitmap(image, bufferWidth, bufferHeight);\r\n    }\r\n\r\n    /**\r\n     * Creates a cube texture\r\n     * @param rootUrl defines the url where the files to load is located\r\n     * @param scene defines the current scene\r\n     * @param files defines the list of files to load (1 per face)\r\n     * @param noMipmap defines a boolean indicating that no mipmaps shall be generated (false by default)\r\n     * @param onLoad defines an optional callback raised when the texture is loaded\r\n     * @param onError defines an optional callback raised if there is an issue to load the texture\r\n     * @param format defines the format of the data\r\n     * @param forcedExtension defines the extension to use to pick the right loader\r\n     * @param createPolynomials if a polynomial sphere should be created for the cube texture\r\n     * @param lodScale defines the scale applied to environment texture. This manages the range of LOD level used for IBL according to the roughness\r\n     * @param lodOffset defines the offset applied to environment texture. This manages first LOD level used for IBL according to the roughness\r\n     * @param fallback defines texture to use while falling back when (compressed) texture file not found.\r\n     * @param loaderOptions options to be passed to the loader\r\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n     * @returns the cube texture as an InternalTexture\r\n     */\r\n    public createCubeTexture(\r\n        rootUrl: string,\r\n        scene: Nullable<Scene>,\r\n        files: Nullable<string[]>,\r\n        noMipmap?: boolean,\r\n        onLoad: Nullable<(data?: any) => void> = null,\r\n        onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n        format?: number,\r\n        forcedExtension: any = null,\r\n        createPolynomials = false,\r\n        lodScale: number = 0,\r\n        lodOffset: number = 0,\r\n        fallback: Nullable<InternalTexture> = null,\r\n        loaderOptions?: any,\r\n        useSRGBBuffer = false\r\n    ): InternalTexture {\r\n        const texture = fallback ? fallback : new InternalTexture(this, InternalTextureSource.Cube);\r\n        texture.isCube = true;\r\n        texture.url = rootUrl;\r\n        texture.generateMipMaps = !noMipmap;\r\n        texture._lodGenerationScale = lodScale;\r\n        texture._lodGenerationOffset = lodOffset;\r\n        texture._useSRGBBuffer = this._getUseSRGBBuffer(useSRGBBuffer, !!noMipmap);\r\n\r\n        if (!this._doNotHandleContextLost) {\r\n            texture._extension = forcedExtension;\r\n            texture._files = files;\r\n        }\r\n\r\n        const lastDot = rootUrl.lastIndexOf(\".\");\r\n        const extension = forcedExtension ? forcedExtension : lastDot > -1 ? rootUrl.substring(lastDot).toLowerCase() : \"\";\r\n\r\n        // TODO: use texture loader to load env files?\r\n        if (extension === \".env\") {\r\n            const onloaddata = (data: ArrayBufferView) => {\r\n                const info = GetEnvInfo(data)!;\r\n                texture.width = info.width;\r\n                texture.height = info.width;\r\n\r\n                UploadEnvSpherical(texture, info);\r\n\r\n                const specularInfo = info.specular as EnvironmentTextureSpecularInfoV1;\r\n                if (!specularInfo) {\r\n                    throw new Error(`Nothing else parsed so far`);\r\n                }\r\n\r\n                texture._lodGenerationScale = specularInfo.lodGenerationScale;\r\n                const imageData = CreateImageDataArrayBufferViews(data, info);\r\n\r\n                texture.format = Constants.TEXTUREFORMAT_RGBA;\r\n                texture.type = Constants.TEXTURETYPE_UNSIGNED_INT;\r\n                texture.generateMipMaps = true;\r\n                texture.getEngine().updateTextureSamplingMode(Texture.TRILINEAR_SAMPLINGMODE, texture);\r\n                texture._isRGBD = true;\r\n                texture.invertY = true;\r\n\r\n                this._engine.loadCubeTextureWithMips(\r\n                    texture._hardwareTexture!.underlyingResource,\r\n                    imageData,\r\n                    false,\r\n                    texture._useSRGBBuffer,\r\n                    () => {\r\n                        texture.isReady = true;\r\n                        if (onLoad) {\r\n                            onLoad();\r\n                        }\r\n                    },\r\n                    () => {\r\n                        throw new Error(\"Could not load a native cube texture.\");\r\n                    }\r\n                );\r\n            };\r\n\r\n            if (files && files.length === 6) {\r\n                throw new Error(`Multi-file loading not allowed on env files.`);\r\n            } else {\r\n                const onInternalError = (request?: IWebRequest, exception?: any) => {\r\n                    if (onError && request) {\r\n                        onError(request.status + \" \" + request.statusText, exception);\r\n                    }\r\n                };\r\n\r\n                this._loadFile(\r\n                    rootUrl,\r\n                    (data) => {\r\n                        onloaddata(new Uint8Array(data as ArrayBuffer, 0, (data as ArrayBuffer).byteLength));\r\n                    },\r\n                    undefined,\r\n                    undefined,\r\n                    true,\r\n                    onInternalError\r\n                );\r\n            }\r\n        } else {\r\n            if (!files || files.length !== 6) {\r\n                throw new Error(\"Cannot load cubemap because 6 files were not defined\");\r\n            }\r\n\r\n            // Reorder from [+X, +Y, +Z, -X, -Y, -Z] to [+X, -X, +Y, -Y, +Z, -Z].\r\n            const reorderedFiles = [files[0], files[3], files[1], files[4], files[2], files[5]];\r\n            Promise.all(reorderedFiles.map((file) => this._loadFileAsync(file, undefined, true).then((data) => new Uint8Array(data, 0, data.byteLength))))\r\n                .then((data) => {\r\n                    return new Promise<void>((resolve, reject) => {\r\n                        this._engine.loadCubeTexture(texture._hardwareTexture!.underlyingResource, data, !noMipmap, true, texture._useSRGBBuffer, resolve, reject);\r\n                    });\r\n                })\r\n                .then(\r\n                    () => {\r\n                        texture.isReady = true;\r\n                        if (onLoad) {\r\n                            onLoad();\r\n                        }\r\n                    },\r\n                    (error) => {\r\n                        if (onError) {\r\n                            onError(`Failed to load cubemap: ${error.message}`, error);\r\n                        }\r\n                    }\r\n                );\r\n        }\r\n\r\n        this._internalTexturesCache.push(texture);\r\n\r\n        return texture;\r\n    }\r\n\r\n    /** @internal */\r\n    public _createHardwareTexture(): HardwareTextureWrapper {\r\n        return new NativeHardwareTexture(this._createTexture() as NativeTexture, this._engine);\r\n    }\r\n\r\n    /** @internal */\r\n    public _createHardwareRenderTargetWrapper(isMulti: boolean, isCube: boolean, size: TextureSize): RenderTargetWrapper {\r\n        const rtWrapper = new NativeRenderTargetWrapper(isMulti, isCube, size, this);\r\n        this._renderTargetWrapperCache.push(rtWrapper);\r\n        return rtWrapper;\r\n    }\r\n\r\n    /** @internal */\r\n    public _createInternalTexture(\r\n        size: TextureSize,\r\n        options: boolean | InternalTextureCreationOptions,\r\n        _delayGPUTextureCreation = true,\r\n        source = InternalTextureSource.Unknown\r\n    ): InternalTexture {\r\n        let generateMipMaps = false;\r\n        let type = Constants.TEXTURETYPE_UNSIGNED_INT;\r\n        let samplingMode = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE;\r\n        let format = Constants.TEXTUREFORMAT_RGBA;\r\n        let useSRGBBuffer = false;\r\n        let samples = 1;\r\n        let label: string | undefined;\r\n        if (options !== undefined && typeof options === \"object\") {\r\n            generateMipMaps = !!options.generateMipMaps;\r\n            type = options.type === undefined ? Constants.TEXTURETYPE_UNSIGNED_INT : options.type;\r\n            samplingMode = options.samplingMode === undefined ? Constants.TEXTURE_TRILINEAR_SAMPLINGMODE : options.samplingMode;\r\n            format = options.format === undefined ? Constants.TEXTUREFORMAT_RGBA : options.format;\r\n            useSRGBBuffer = options.useSRGBBuffer === undefined ? false : options.useSRGBBuffer;\r\n            samples = options.samples ?? 1;\r\n            label = options.label;\r\n        } else {\r\n            generateMipMaps = !!options;\r\n        }\r\n\r\n        useSRGBBuffer = this._getUseSRGBBuffer(useSRGBBuffer, !generateMipMaps);\r\n\r\n        if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n            // if floating point linear (gl.FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        } else if (type === Constants.TEXTURETYPE_HALF_FLOAT && !this._caps.textureHalfFloatLinearFiltering) {\r\n            // if floating point linear (HALF_FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        }\r\n        if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloat) {\r\n            type = Constants.TEXTURETYPE_UNSIGNED_INT;\r\n            Logger.Warn(\"Float textures are not supported. Type forced to TEXTURETYPE_UNSIGNED_BYTE\");\r\n        }\r\n\r\n        const texture = new InternalTexture(this, source);\r\n        const width = (<{ width: number; height: number; layers?: number }>size).width ?? <number>size;\r\n        const height = (<{ width: number; height: number; layers?: number }>size).height ?? <number>size;\r\n\r\n        const layers = (<{ width: number; height: number; layers?: number }>size).layers || 0;\r\n        if (layers !== 0) {\r\n            throw new Error(\"Texture layers are not supported in Babylon Native\");\r\n        }\r\n\r\n        const nativeTexture = texture._hardwareTexture!.underlyingResource;\r\n        const nativeTextureFormat = getNativeTextureFormat(format, type);\r\n        // REVIEW: We are always setting the renderTarget flag as we don't know whether the texture will be used as a render target.\r\n        this._engine.initializeTexture(nativeTexture, width, height, generateMipMaps, nativeTextureFormat, true, useSRGBBuffer, samples);\r\n        this._setTextureSampling(nativeTexture, getNativeSamplingMode(samplingMode));\r\n\r\n        texture._useSRGBBuffer = useSRGBBuffer;\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.width = width;\r\n        texture.height = height;\r\n        texture.depth = layers;\r\n        texture.isReady = true;\r\n        texture.samples = samples;\r\n        texture.generateMipMaps = generateMipMaps;\r\n        texture.samplingMode = samplingMode;\r\n        texture.type = type;\r\n        texture.format = format;\r\n        texture.label = label;\r\n\r\n        this._internalTexturesCache.push(texture);\r\n\r\n        return texture;\r\n    }\r\n\r\n    public createRenderTargetTexture(size: number | { width: number; height: number }, options: boolean | RenderTargetCreationOptions): RenderTargetWrapper {\r\n        const rtWrapper = this._createHardwareRenderTargetWrapper(false, false, size) as NativeRenderTargetWrapper;\r\n\r\n        let generateDepthBuffer = true;\r\n        let generateStencilBuffer = false;\r\n        let noColorAttachment = false;\r\n        let colorAttachment: InternalTexture | undefined = undefined;\r\n        let samples = 1;\r\n        if (options !== undefined && typeof options === \"object\") {\r\n            generateDepthBuffer = options.generateDepthBuffer ?? true;\r\n            generateStencilBuffer = !!options.generateStencilBuffer;\r\n            noColorAttachment = !!options.noColorAttachment;\r\n            colorAttachment = options.colorAttachment;\r\n            samples = options.samples ?? 1;\r\n        }\r\n\r\n        const texture = colorAttachment || (noColorAttachment ? null : this._createInternalTexture(size, options, true, InternalTextureSource.RenderTarget));\r\n        const width = (<{ width: number; height: number; layers?: number }>size).width ?? <number>size;\r\n        const height = (<{ width: number; height: number; layers?: number }>size).height ?? <number>size;\r\n\r\n        const framebuffer = this._engine.createFrameBuffer(\r\n            texture ? texture._hardwareTexture!.underlyingResource : null,\r\n            width,\r\n            height,\r\n            generateStencilBuffer,\r\n            generateDepthBuffer,\r\n            samples\r\n        );\r\n\r\n        rtWrapper._framebuffer = framebuffer;\r\n        rtWrapper._generateDepthBuffer = generateDepthBuffer;\r\n        rtWrapper._generateStencilBuffer = generateStencilBuffer;\r\n        rtWrapper._samples = samples;\r\n\r\n        rtWrapper.setTextures(texture);\r\n\r\n        return rtWrapper;\r\n    }\r\n\r\n    public updateRenderTargetTextureSampleCount(rtWrapper: RenderTargetWrapper, samples: number): number {\r\n        Logger.Warn(\"Updating render target sample count is not currently supported\");\r\n        return rtWrapper.samples;\r\n    }\r\n\r\n    public updateTextureSamplingMode(samplingMode: number, texture: InternalTexture): void {\r\n        if (texture._hardwareTexture) {\r\n            const filter = getNativeSamplingMode(samplingMode);\r\n            this._setTextureSampling(texture._hardwareTexture.underlyingResource, filter);\r\n        }\r\n\r\n        texture.samplingMode = samplingMode;\r\n    }\r\n\r\n    public bindFramebuffer(texture: RenderTargetWrapper, faceIndex?: number, requiredWidth?: number, requiredHeight?: number, forceFullscreenViewport?: boolean): void {\r\n        const nativeRTWrapper = texture as NativeRenderTargetWrapper;\r\n\r\n        if (this._currentRenderTarget) {\r\n            this.unBindFramebuffer(this._currentRenderTarget);\r\n        }\r\n\r\n        this._currentRenderTarget = texture;\r\n\r\n        if (faceIndex) {\r\n            throw new Error(\"Cuboid frame buffers are not yet supported in NativeEngine.\");\r\n        }\r\n\r\n        if (requiredWidth || requiredHeight) {\r\n            throw new Error(\"Required width/height for frame buffers not yet supported in NativeEngine.\");\r\n        }\r\n\r\n        if (forceFullscreenViewport) {\r\n            //Not supported yet but don't stop rendering\r\n        }\r\n\r\n        if (nativeRTWrapper._framebufferDepthStencil) {\r\n            this._bindUnboundFramebuffer(nativeRTWrapper._framebufferDepthStencil);\r\n        } else {\r\n            this._bindUnboundFramebuffer(nativeRTWrapper._framebuffer);\r\n        }\r\n    }\r\n\r\n    public unBindFramebuffer(texture: RenderTargetWrapper, disableGenerateMipMaps = false, onBeforeUnbind?: () => void): void {\r\n        // NOTE: Disabling mipmap generation is not yet supported in NativeEngine.\r\n\r\n        this._currentRenderTarget = null;\r\n\r\n        if (onBeforeUnbind) {\r\n            onBeforeUnbind();\r\n        }\r\n\r\n        this._bindUnboundFramebuffer(null);\r\n    }\r\n\r\n    public createDynamicVertexBuffer(data: DataArray): DataBuffer {\r\n        return this.createVertexBuffer(data, true);\r\n    }\r\n\r\n    public updateDynamicIndexBuffer(indexBuffer: DataBuffer, indices: IndicesArray, offset: number = 0): void {\r\n        const buffer = indexBuffer as NativeDataBuffer;\r\n        const data = this._normalizeIndexData(indices);\r\n        buffer.is32Bits = data.BYTES_PER_ELEMENT === 4;\r\n        this._engine.updateDynamicIndexBuffer(buffer.nativeIndexBuffer!, data.buffer, data.byteOffset, data.byteLength, offset);\r\n    }\r\n\r\n    public updateDynamicVertexBuffer(vertexBuffer: DataBuffer, data: DataArray, byteOffset = 0, byteLength?: number): void {\r\n        const buffer = vertexBuffer as NativeDataBuffer;\r\n        const dataView = data instanceof Array ? new Float32Array(data) : data instanceof ArrayBuffer ? new Uint8Array(data) : data;\r\n        const byteView = new Uint8Array(dataView.buffer, dataView.byteOffset, byteLength ?? dataView.byteLength);\r\n        this._engine.updateDynamicVertexBuffer(buffer.nativeVertexBuffer!, byteView.buffer, byteView.byteOffset, byteView.byteLength, byteOffset);\r\n    }\r\n\r\n    // TODO: Refactor to share more logic with base Engine implementation.\r\n    protected _setTexture(channel: number, texture: Nullable<BaseTexture>, isPartOfTextureArray = false, depthStencilTexture = false): boolean {\r\n        const uniform = this._boundUniforms[channel] as unknown as NativeUniform;\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        // Not ready?\r\n        if (!texture) {\r\n            if (this._boundTexturesCache[channel] != null) {\r\n                this._activeChannel = channel;\r\n                this._boundTexturesCache[channel] = null;\r\n            }\r\n            return false;\r\n        }\r\n\r\n        // Video\r\n        if ((<VideoTexture>texture).video) {\r\n            this._activeChannel = channel;\r\n            (<VideoTexture>texture).update();\r\n        } else if (texture.delayLoadState === Constants.DELAYLOADSTATE_NOTLOADED) {\r\n            // Delay loading\r\n            texture.delayLoad();\r\n            return false;\r\n        }\r\n\r\n        let internalTexture: InternalTexture;\r\n        if (depthStencilTexture) {\r\n            internalTexture = (<RenderTargetTexture>texture).depthStencilTexture!;\r\n        } else if (texture.isReady()) {\r\n            internalTexture = <InternalTexture>texture.getInternalTexture();\r\n        } else if (texture.isCube) {\r\n            internalTexture = this.emptyCubeTexture;\r\n        } else if (texture.is3D) {\r\n            internalTexture = this.emptyTexture3D;\r\n        } else if (texture.is2DArray) {\r\n            internalTexture = this.emptyTexture2DArray;\r\n        } else {\r\n            internalTexture = this.emptyTexture;\r\n        }\r\n\r\n        this._activeChannel = channel;\r\n\r\n        if (!internalTexture || !internalTexture._hardwareTexture) {\r\n            return false;\r\n        }\r\n\r\n        this._setTextureWrapMode(\r\n            internalTexture._hardwareTexture.underlyingResource,\r\n            getNativeAddressMode(texture.wrapU),\r\n            getNativeAddressMode(texture.wrapV),\r\n            getNativeAddressMode(texture.wrapR)\r\n        );\r\n        this._updateAnisotropicLevel(texture);\r\n\r\n        this._setTextureCore(uniform, internalTexture._hardwareTexture.underlyingResource);\r\n\r\n        return true;\r\n    }\r\n\r\n    // filter is a NativeFilter.XXXX value.\r\n    private _setTextureSampling(texture: NativeTexture, filter: number) {\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETTEXTURESAMPLING);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(texture as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(filter);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    // addressModes are NativeAddressMode.XXXX values.\r\n    private _setTextureWrapMode(texture: NativeTexture, addressModeU: number, addressModeV: number, addressModeW: number) {\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETTEXTUREWRAPMODE);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(texture as NativeData);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(addressModeU);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(addressModeV);\r\n        this._commandBufferEncoder.encodeCommandArgAsUInt32(addressModeW);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    private _setTextureCore(uniform: NativeUniform, texture: NativeTexture) {\r\n        this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETTEXTURE);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(uniform);\r\n        this._commandBufferEncoder.encodeCommandArgAsNativeData(texture);\r\n        this._commandBufferEncoder.finishEncodingCommand();\r\n    }\r\n\r\n    // TODO: Share more of this logic with the base implementation.\r\n    // TODO: Rename to match naming in base implementation once refactoring allows different parameters.\r\n    private _updateAnisotropicLevel(texture: BaseTexture) {\r\n        const internalTexture = texture.getInternalTexture();\r\n        const value = texture.anisotropicFilteringLevel;\r\n\r\n        if (!internalTexture || !internalTexture._hardwareTexture) {\r\n            return;\r\n        }\r\n\r\n        if (internalTexture._cachedAnisotropicFilteringLevel !== value) {\r\n            this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_SETTEXTUREANISOTROPICLEVEL);\r\n            this._commandBufferEncoder.encodeCommandArgAsNativeData(internalTexture._hardwareTexture.underlyingResource);\r\n            this._commandBufferEncoder.encodeCommandArgAsUInt32(value);\r\n            this._commandBufferEncoder.finishEncodingCommand();\r\n            internalTexture._cachedAnisotropicFilteringLevel = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindTexture(channel: number, texture: InternalTexture): void {\r\n        const uniform = this._boundUniforms[channel] as unknown as NativeUniform;\r\n        if (!uniform) {\r\n            return;\r\n        }\r\n        if (texture && texture._hardwareTexture) {\r\n            const underlyingResource = texture._hardwareTexture.underlyingResource;\r\n            this._setTextureCore(uniform, underlyingResource);\r\n        }\r\n    }\r\n\r\n    protected _deleteBuffer(buffer: NativeDataBuffer): void {\r\n        if (buffer.nativeIndexBuffer) {\r\n            this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_DELETEINDEXBUFFER);\r\n            this._commandBufferEncoder.encodeCommandArgAsNativeData(buffer.nativeIndexBuffer);\r\n            this._commandBufferEncoder.finishEncodingCommand();\r\n            delete buffer.nativeIndexBuffer;\r\n        }\r\n\r\n        if (buffer.nativeVertexBuffer) {\r\n            this._commandBufferEncoder.startEncodingCommand(_native.Engine.COMMAND_DELETEVERTEXBUFFER);\r\n            this._commandBufferEncoder.encodeCommandArgAsNativeData(buffer.nativeVertexBuffer);\r\n            this._commandBufferEncoder.finishEncodingCommand();\r\n            delete buffer.nativeVertexBuffer;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create a canvas\r\n     * @param width width\r\n     * @param height height\r\n     * @returns ICanvas interface\r\n     */\r\n    public createCanvas(width: number, height: number): ICanvas {\r\n        if (!_native.Canvas) {\r\n            throw new Error(\"Native Canvas plugin not available.\");\r\n        }\r\n        const canvas = new _native.Canvas();\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n        return canvas;\r\n    }\r\n\r\n    /**\r\n     * Create an image to use with canvas\r\n     * @returns IImage interface\r\n     */\r\n    public createCanvasImage(): IImage {\r\n        if (!_native.Canvas) {\r\n            throw new Error(\"Native Canvas plugin not available.\");\r\n        }\r\n        const image = new _native.Image();\r\n        return image;\r\n    }\r\n\r\n    /**\r\n     * Update a portion of an internal texture\r\n     * @param texture defines the texture to update\r\n     * @param imageData defines the data to store into the texture\r\n     * @param xOffset defines the x coordinates of the update rectangle\r\n     * @param yOffset defines the y coordinates of the update rectangle\r\n     * @param width defines the width of the update rectangle\r\n     * @param height defines the height of the update rectangle\r\n     * @param faceIndex defines the face index if texture is a cube (0 by default)\r\n     * @param lod defines the lod level to update (0 by default)\r\n     * @param generateMipMaps defines whether to generate mipmaps or not\r\n     */\r\n    public updateTextureData(\r\n        texture: InternalTexture,\r\n        imageData: ArrayBufferView,\r\n        xOffset: number,\r\n        yOffset: number,\r\n        width: number,\r\n        height: number,\r\n        faceIndex: number = 0,\r\n        lod: number = 0,\r\n        generateMipMaps = false\r\n    ): void {\r\n        throw new Error(\"updateTextureData not implemented.\");\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadCompressedDataToTextureDirectly(\r\n        texture: InternalTexture,\r\n        internalFormat: number,\r\n        width: number,\r\n        height: number,\r\n        data: ArrayBufferView,\r\n        faceIndex: number = 0,\r\n        lod: number = 0\r\n    ) {\r\n        throw new Error(\"_uploadCompressedDataToTextureDirectly not implemented.\");\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadDataToTextureDirectly(texture: InternalTexture, imageData: ArrayBufferView, faceIndex: number = 0, lod: number = 0): void {\r\n        throw new Error(\"_uploadDataToTextureDirectly not implemented.\");\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadArrayBufferViewToTexture(texture: InternalTexture, imageData: ArrayBufferView, faceIndex: number = 0, lod: number = 0): void {\r\n        throw new Error(\"_uploadArrayBufferViewToTexture not implemented.\");\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadImageToTexture(texture: InternalTexture, image: HTMLImageElement, faceIndex: number = 0, lod: number = 0) {\r\n        throw new Error(\"_uploadArrayBufferViewToTexture not implemented.\");\r\n    }\r\n\r\n    public getFontOffset(font: string): { ascent: number; height: number; descent: number } {\r\n        // TODO\r\n        const result = { ascent: 0, height: 0, descent: 0 };\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * No equivalent for native. Do nothing.\r\n     */\r\n    public flushFramebuffer(): void {}\r\n\r\n    public _readTexturePixels(\r\n        texture: InternalTexture,\r\n        width: number,\r\n        height: number,\r\n        faceIndex?: number,\r\n        level?: number,\r\n        buffer?: Nullable<ArrayBufferView>,\r\n        _flushRenderer?: boolean,\r\n        _noDataConversion?: boolean,\r\n        x?: number,\r\n        y?: number\r\n    ): Promise<ArrayBufferView> {\r\n        if (faceIndex !== undefined && faceIndex !== -1) {\r\n            throw new Error(`Reading cubemap faces is not supported, but faceIndex is ${faceIndex}.`);\r\n        }\r\n\r\n        return this._engine\r\n            .readTexture(\r\n                texture._hardwareTexture?.underlyingResource,\r\n                level ?? 0,\r\n                x ?? 0,\r\n                y ?? 0,\r\n                width,\r\n                height,\r\n                buffer?.buffer ?? null,\r\n                buffer?.byteOffset ?? 0,\r\n                buffer?.byteLength ?? 0\r\n            )\r\n            .then((rawBuffer) => {\r\n                if (!buffer) {\r\n                    buffer = new Uint8Array(rawBuffer);\r\n                }\r\n\r\n                return buffer;\r\n            });\r\n    }\r\n}\r\n"]}