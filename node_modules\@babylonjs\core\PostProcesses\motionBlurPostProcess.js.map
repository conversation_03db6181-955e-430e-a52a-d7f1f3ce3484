{"version": 3, "file": "motionBlurPostProcess.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/motionBlurPostProcess.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAInE,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,sBAAsB,EAAE,MAAM,qCAAqC,CAAC;AAE7E,OAAO,EAAE,uBAAuB,EAAE,MAAM,sCAAsC,CAAC;AAG/E,OAAO,0BAA0B,CAAC;AAClC,OAAO,mDAAmD,CAAC;AAC3D,OAAO,gCAAgC,CAAC;AACxC,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AACvE,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAKlD;;;;;;;;;;;GAWG;AACH,MAAM,OAAO,qBAAsB,SAAQ,WAAW;IAOlD;;OAEG;IAEH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB,CAAC,OAAe;QACxC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAID;;OAEG;IAEH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,aAAa,CAAC,KAAc;QACnC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAKD,IAAY,uBAAuB;QAC/B,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;IAC9C,CAAC;IAED,IAAY,gBAAgB;QACxB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;IACvC,CAAC;IAKD;;;OAGG;IACI,YAAY;QACf,OAAO,uBAAuB,CAAC;IACnC,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,YACI,IAAY,EACZ,KAAY,EACZ,OAAoC,EACpC,MAAwB,EACxB,YAAqB,EACrB,MAAe,EACf,QAAkB,EAClB,cAAsB,SAAS,CAAC,wBAAwB,EACxD,gBAAgB,GAAG,KAAK,EACxB,mBAAmB,GAAG,KAAK;QAE3B,KAAK,CACD,IAAI,EACJ,YAAY,EACZ,CAAC,gBAAgB,EAAE,aAAa,EAAE,YAAY,EAAE,uBAAuB,EAAE,oBAAoB,EAAE,YAAY,CAAC,EAC5G,CAAC,iBAAiB,EAAE,cAAc,CAAC,EACnC,OAAO,EACP,MAAM,EACN,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,wEAAwE,EACxE,WAAW,EACX,SAAS,EACT,IAAI,EACJ,gBAAgB,CACnB,CAAC;QAlHN;;WAEG;QAEI,mBAAc,GAAW,CAAC,CAAC;QAkB1B,uBAAkB,GAAW,EAAE,CAAC;QAsBhC,mBAAc,GAAY,IAAI,CAAC;QAE/B,yBAAoB,GAAY,KAAK,CAAC;QAiBtC,uBAAkB,GAAqB,IAAI,CAAC;QAC5C,4BAAuB,GAAqB,IAAI,CAAC;QAoDrD,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QAEhD,gBAAgB;QAChB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,KAAK,CAAC,4BAA4B,EAAE,CAAC;YAErC,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,uBAAuB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;aACrE;SACJ;aAAM;YACH,KAAK,CAAC,qBAAqB,EAAE,CAAC;YAE9B,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;gBACpC,IAAI,CAAC,2BAA2B,GAAG,IAAI,uBAAuB,EAAE,CAAC;aACpE;SACJ;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,WAAyB;QAC/C,IAAI,WAAW,CAAC,QAAQ,EAAE;YACtB,IAAI,IAAI,CAAC;YACT,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,iCAAiC,CAAC;aACzE;iBAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBAC9B,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;aACpD;iBAAM;gBACH,OAAO;aACV;YACD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC1B;IACL,CAAC;IAED;;;;OAIG;IACI,yBAAyB,CAAC,WAAyB;QACtD,IAAI,WAAW,CAAC,QAAQ,EAAE;YACtB,IAAI,IAAI,CAAC;YACT,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,iCAAiC,CAAC;aACzE;iBAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBAC9B,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;aACpD;iBAAM;gBACH,OAAO;aACV;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACxC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBACd,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACzB;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,MAAe;QAC1B,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,uFAAuF;YACvF,IAAI,CAAC,uBAAuB,CAAC,+BAA+B,GAAG,EAAE,CAAC;YAClE,IAAI,CAAC,uBAAuB,CAAC,oCAAoC,GAAG,EAAE,CAAC;YACvE,IAAI,CAAC,uBAAuB,CAAC,iCAAiC,GAAG,EAAE,CAAC;SACvE;QAED,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACK,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACzD,uEAAuE;YACvE,MAAM,CAAC,IAAI,CAAC,2EAA2E,CAAC,CAAC;YACzF,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;SAC9B;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;SACrE;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QAEpC,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBAC3D,IAAI,CAAC,2BAA2B,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,6BAA6B,CAAC;aAClG;YAED,IAAI,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;SACvE;aAAM;YACH,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC5C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,KAAK,EAAE,CAAC;YAExE,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBAC3D,IAAI,CAAC,2BAA2B,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,0BAA0B,CAAC;aAC/F;YAED,IAAI,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;SACvE;IACL,CAAC;IAED;;;OAGG;IACK,mBAAmB,CAAC,MAAc;QACtC,MAAM,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAEtE,MAAM,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAChE,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvD,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;YACjH,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;SAC3G;aAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;YAC9F,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;SACzG;IACL,CAAC;IAED;;;OAGG;IACK,mBAAmB,CAAC,MAAc;QACtC,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC5C,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAE1D,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAmB,CAAC,CAAC;QACrD,MAAM,CAAC,SAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC,kBAAmB,CAAC,CAAC;QAEpE,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,uBAAwB,CAAC,CAAC;QACtE,IAAI,CAAC,uBAAwB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QAEvD,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAElE,MAAM,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAEtE,MAAM,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAChE,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvD,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;YAC3G,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;SACrG;aAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;YACxF,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;SACnG;IACL,CAAC;IAED;;OAEG;IACK,aAAa;QACjB,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvD,MAAM,OAAO,GAAa;gBACtB,4BAA4B;gBAC5B,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;gBACvD,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,sBAAsB;aACxE,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACzC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,iBAAsB,EAAE,YAAoB,EAAE,KAAY,EAAE,OAAe;QAC5F,OAAO,mBAAmB,CAAC,KAAK,CAC5B,GAAG,EAAE;YACD,OAAO,IAAI,qBAAqB,CAC5B,iBAAiB,CAAC,IAAI,EACtB,KAAK,EACL,iBAAiB,CAAC,OAAO,EACzB,YAAY,EACZ,iBAAiB,CAAC,wBAAwB,EAC1C,KAAK,CAAC,SAAS,EAAE,EACjB,iBAAiB,CAAC,QAAQ,EAC1B,iBAAiB,CAAC,WAAW,EAC7B,KAAK,CACR,CAAC;QACN,CAAC,EACD,iBAAiB,EACjB,KAAK,EACL,OAAO,CACV,CAAC;IACN,CAAC;CACJ;AA3TU;IADN,SAAS,EAAE;6DACsB;AAMlC;IADC,SAAS,EAAE;8DAGX;AAgBD;IADC,SAAS,EAAE;0DAGX;AAmSL,aAAa,CAAC,+BAA+B,EAAE,qBAAqB,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { Matrix, TmpVectors, Vector2 } from \"../Maths/math.vector\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport type { PostProcessOptions } from \"./postProcess\";\r\nimport { PostProcess } from \"./postProcess\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { GeometryBufferRenderer } from \"../Rendering/geometryBufferRenderer\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { MotionBlurConfiguration } from \"../Rendering/motionBlurConfiguration\";\r\nimport type { PrePassRenderer } from \"../Rendering/prePassRenderer\";\r\n\r\nimport \"../Animations/animatable\";\r\nimport \"../Rendering/geometryBufferRendererSceneComponent\";\r\nimport \"../Shaders/motionBlur.fragment\";\r\nimport { serialize } from \"../Misc/decorators\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\n\r\nimport type { Engine } from \"../Engines/engine\";\r\nimport type { Scene } from \"../scene\";\r\n\r\n/**\r\n * The Motion Blur Post Process which blurs an image based on the objects velocity in scene.\r\n * Velocity can be affected by each object's rotation, position and scale depending on the transformation speed.\r\n * As an example, all you have to do is to create the post-process:\r\n *  var mb = new BABYLON.MotionBlurPostProcess(\r\n *      'mb', // The name of the effect.\r\n *      scene, // The scene containing the objects to blur according to their velocity.\r\n *      1.0, // The required width/height ratio to downsize to before computing the render pass.\r\n *      camera // The camera to apply the render pass to.\r\n * );\r\n * Then, all objects moving, rotating and/or scaling will be blurred depending on the transformation speed.\r\n */\r\nexport class MotionBlurPostProcess extends PostProcess {\r\n    /**\r\n     * Defines how much the image is blurred by the movement. Default value is equal to 1\r\n     */\r\n    @serialize()\r\n    public motionStrength: number = 1;\r\n\r\n    /**\r\n     * Gets the number of iterations are used for motion blur quality. Default value is equal to 32\r\n     */\r\n    @serialize()\r\n    public get motionBlurSamples(): number {\r\n        return this._motionBlurSamples;\r\n    }\r\n\r\n    /**\r\n     * Sets the number of iterations to be used for motion blur quality\r\n     */\r\n    public set motionBlurSamples(samples: number) {\r\n        this._motionBlurSamples = samples;\r\n        this._updateEffect();\r\n    }\r\n\r\n    private _motionBlurSamples: number = 32;\r\n\r\n    /**\r\n     * Gets whether or not the motion blur post-process is in object based mode.\r\n     */\r\n    @serialize()\r\n    public get isObjectBased(): boolean {\r\n        return this._isObjectBased;\r\n    }\r\n\r\n    /**\r\n     * Sets whether or not the motion blur post-process is in object based mode.\r\n     */\r\n    public set isObjectBased(value: boolean) {\r\n        if (this._isObjectBased === value) {\r\n            return;\r\n        }\r\n\r\n        this._isObjectBased = value;\r\n        this._applyMode();\r\n    }\r\n\r\n    private _isObjectBased: boolean = true;\r\n\r\n    private _forceGeometryBuffer: boolean = false;\r\n    private get _geometryBufferRenderer(): Nullable<GeometryBufferRenderer> {\r\n        if (!this._forceGeometryBuffer) {\r\n            return null;\r\n        }\r\n\r\n        return this._scene.geometryBufferRenderer;\r\n    }\r\n\r\n    private get _prePassRenderer(): Nullable<PrePassRenderer> {\r\n        if (this._forceGeometryBuffer) {\r\n            return null;\r\n        }\r\n\r\n        return this._scene.prePassRenderer;\r\n    }\r\n\r\n    private _invViewProjection: Nullable<Matrix> = null;\r\n    private _previousViewProjection: Nullable<Matrix> = null;\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"MotionBlurPostProcess\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"MotionBlurPostProcess\";\r\n    }\r\n\r\n    /**\r\n     * Creates a new instance MotionBlurPostProcess\r\n     * @param name The name of the effect.\r\n     * @param scene The scene containing the objects to blur according to their velocity.\r\n     * @param options The required width/height ratio to downsize to before computing the render pass.\r\n     * @param camera The camera to apply the render pass to.\r\n     * @param samplingMode The sampling mode to be used when computing the pass. (default: 0)\r\n     * @param engine The engine which the post process will be applied. (default: current engine)\r\n     * @param reusable If the post process can be reused on the same frame. (default: false)\r\n     * @param textureType Type of textures used when performing the post process. (default: 0)\r\n     * @param blockCompilation If compilation of the shader should not be done in the constructor. The updateEffect method can be used to compile the shader at a later time. (default: true)\r\n     * @param forceGeometryBuffer If this post process should use geometry buffer instead of prepass (default: false)\r\n     */\r\n    constructor(\r\n        name: string,\r\n        scene: Scene,\r\n        options: number | PostProcessOptions,\r\n        camera: Nullable<Camera>,\r\n        samplingMode?: number,\r\n        engine?: Engine,\r\n        reusable?: boolean,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        blockCompilation = false,\r\n        forceGeometryBuffer = false\r\n    ) {\r\n        super(\r\n            name,\r\n            \"motionBlur\",\r\n            [\"motionStrength\", \"motionScale\", \"screenSize\", \"inverseViewProjection\", \"prevViewProjection\", \"projection\"],\r\n            [\"velocitySampler\", \"depthSampler\"],\r\n            options,\r\n            camera,\r\n            samplingMode,\r\n            engine,\r\n            reusable,\r\n            \"#define GEOMETRY_SUPPORTED\\n#define SAMPLES 64.0\\n#define OBJECT_BASED\",\r\n            textureType,\r\n            undefined,\r\n            null,\r\n            blockCompilation\r\n        );\r\n\r\n        this._forceGeometryBuffer = forceGeometryBuffer;\r\n\r\n        // Set up assets\r\n        if (this._forceGeometryBuffer) {\r\n            scene.enableGeometryBufferRenderer();\r\n\r\n            if (this._geometryBufferRenderer) {\r\n                this._geometryBufferRenderer.enableVelocity = this._isObjectBased;\r\n            }\r\n        } else {\r\n            scene.enablePrePassRenderer();\r\n\r\n            if (this._prePassRenderer) {\r\n                this._prePassRenderer.markAsDirty();\r\n                this._prePassEffectConfiguration = new MotionBlurConfiguration();\r\n            }\r\n        }\r\n\r\n        this._applyMode();\r\n    }\r\n\r\n    /**\r\n     * Excludes the given skinned mesh from computing bones velocities.\r\n     * Computing bones velocities can have a cost and that cost. The cost can be saved by calling this function and by passing the skinned mesh reference to ignore.\r\n     * @param skinnedMesh The mesh containing the skeleton to ignore when computing the velocity map.\r\n     */\r\n    public excludeSkinnedMesh(skinnedMesh: AbstractMesh): void {\r\n        if (skinnedMesh.skeleton) {\r\n            let list;\r\n            if (this._geometryBufferRenderer) {\r\n                list = this._geometryBufferRenderer.excludedSkinnedMeshesFromVelocity;\r\n            } else if (this._prePassRenderer) {\r\n                list = this._prePassRenderer.excludedSkinnedMesh;\r\n            } else {\r\n                return;\r\n            }\r\n            list.push(skinnedMesh);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Removes the given skinned mesh from the excluded meshes to integrate bones velocities while rendering the velocity map.\r\n     * @param skinnedMesh The mesh containing the skeleton that has been ignored previously.\r\n     * @see excludeSkinnedMesh to exclude a skinned mesh from bones velocity computation.\r\n     */\r\n    public removeExcludedSkinnedMesh(skinnedMesh: AbstractMesh): void {\r\n        if (skinnedMesh.skeleton) {\r\n            let list;\r\n            if (this._geometryBufferRenderer) {\r\n                list = this._geometryBufferRenderer.excludedSkinnedMeshesFromVelocity;\r\n            } else if (this._prePassRenderer) {\r\n                list = this._prePassRenderer.excludedSkinnedMesh;\r\n            } else {\r\n                return;\r\n            }\r\n\r\n            const index = list.indexOf(skinnedMesh);\r\n            if (index !== -1) {\r\n                list.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disposes the post process.\r\n     * @param camera The camera to dispose the post process on.\r\n     */\r\n    public dispose(camera?: Camera): void {\r\n        if (this._geometryBufferRenderer) {\r\n            // Clear previous transformation matrices dictionary used to compute objects velocities\r\n            this._geometryBufferRenderer._previousTransformationMatrices = {};\r\n            this._geometryBufferRenderer._previousBonesTransformationMatrices = {};\r\n            this._geometryBufferRenderer.excludedSkinnedMeshesFromVelocity = [];\r\n        }\r\n\r\n        super.dispose(camera);\r\n    }\r\n\r\n    /**\r\n     * Called on the mode changed (object based or screen based).\r\n     * @returns void\r\n     */\r\n    private _applyMode() {\r\n        if (!this._geometryBufferRenderer && !this._prePassRenderer) {\r\n            // We can't get a velocity or depth texture. So, work as a passthrough.\r\n            Logger.Warn(\"Multiple Render Target support needed to compute object based motion blur\");\r\n            return this.updateEffect();\r\n        }\r\n\r\n        if (this._geometryBufferRenderer) {\r\n            this._geometryBufferRenderer.enableVelocity = this._isObjectBased;\r\n        }\r\n\r\n        this._updateEffect();\r\n\r\n        this._invViewProjection = null;\r\n        this._previousViewProjection = null;\r\n\r\n        if (this.isObjectBased) {\r\n            if (this._prePassRenderer && this._prePassEffectConfiguration) {\r\n                this._prePassEffectConfiguration.texturesRequired[0] = Constants.PREPASS_VELOCITY_TEXTURE_TYPE;\r\n            }\r\n\r\n            this.onApply = (effect: Effect) => this._onApplyObjectBased(effect);\r\n        } else {\r\n            this._invViewProjection = Matrix.Identity();\r\n            this._previousViewProjection = this._scene.getTransformMatrix().clone();\r\n\r\n            if (this._prePassRenderer && this._prePassEffectConfiguration) {\r\n                this._prePassEffectConfiguration.texturesRequired[0] = Constants.PREPASS_DEPTH_TEXTURE_TYPE;\r\n            }\r\n\r\n            this.onApply = (effect: Effect) => this._onApplyScreenBased(effect);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Called on the effect is applied when the motion blur post-process is in object based mode.\r\n     * @param effect\r\n     */\r\n    private _onApplyObjectBased(effect: Effect): void {\r\n        effect.setVector2(\"screenSize\", new Vector2(this.width, this.height));\r\n\r\n        effect.setFloat(\"motionScale\", this._scene.getAnimationRatio());\r\n        effect.setFloat(\"motionStrength\", this.motionStrength);\r\n\r\n        if (this._geometryBufferRenderer) {\r\n            const velocityIndex = this._geometryBufferRenderer.getTextureIndex(GeometryBufferRenderer.VELOCITY_TEXTURE_TYPE);\r\n            effect.setTexture(\"velocitySampler\", this._geometryBufferRenderer.getGBuffer().textures[velocityIndex]);\r\n        } else if (this._prePassRenderer) {\r\n            const velocityIndex = this._prePassRenderer.getIndex(Constants.PREPASS_VELOCITY_TEXTURE_TYPE);\r\n            effect.setTexture(\"velocitySampler\", this._prePassRenderer.getRenderTarget().textures[velocityIndex]);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Called on the effect is applied when the motion blur post-process is in screen based mode.\r\n     * @param effect\r\n     */\r\n    private _onApplyScreenBased(effect: Effect): void {\r\n        const viewProjection = TmpVectors.Matrix[0];\r\n        viewProjection.copyFrom(this._scene.getTransformMatrix());\r\n\r\n        viewProjection.invertToRef(this._invViewProjection!);\r\n        effect.setMatrix(\"inverseViewProjection\", this._invViewProjection!);\r\n\r\n        effect.setMatrix(\"prevViewProjection\", this._previousViewProjection!);\r\n        this._previousViewProjection!.copyFrom(viewProjection);\r\n\r\n        effect.setMatrix(\"projection\", this._scene.getProjectionMatrix());\r\n\r\n        effect.setVector2(\"screenSize\", new Vector2(this.width, this.height));\r\n\r\n        effect.setFloat(\"motionScale\", this._scene.getAnimationRatio());\r\n        effect.setFloat(\"motionStrength\", this.motionStrength);\r\n\r\n        if (this._geometryBufferRenderer) {\r\n            const depthIndex = this._geometryBufferRenderer.getTextureIndex(GeometryBufferRenderer.DEPTH_TEXTURE_TYPE);\r\n            effect.setTexture(\"depthSampler\", this._geometryBufferRenderer.getGBuffer().textures[depthIndex]);\r\n        } else if (this._prePassRenderer) {\r\n            const depthIndex = this._prePassRenderer.getIndex(Constants.PREPASS_DEPTH_TEXTURE_TYPE);\r\n            effect.setTexture(\"depthSampler\", this._prePassRenderer.getRenderTarget().textures[depthIndex]);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Called on the effect must be updated (changed mode, samples count, etc.).\r\n     */\r\n    private _updateEffect(): void {\r\n        if (this._geometryBufferRenderer || this._prePassRenderer) {\r\n            const defines: string[] = [\r\n                \"#define GEOMETRY_SUPPORTED\",\r\n                \"#define SAMPLES \" + this._motionBlurSamples.toFixed(1),\r\n                this._isObjectBased ? \"#define OBJECT_BASED\" : \"#define SCREEN_BASED\",\r\n            ];\r\n\r\n            this.updateEffect(defines.join(\"\\n\"));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _Parse(parsedPostProcess: any, targetCamera: Camera, scene: Scene, rootUrl: string): Nullable<MotionBlurPostProcess> {\r\n        return SerializationHelper.Parse(\r\n            () => {\r\n                return new MotionBlurPostProcess(\r\n                    parsedPostProcess.name,\r\n                    scene,\r\n                    parsedPostProcess.options,\r\n                    targetCamera,\r\n                    parsedPostProcess.renderTargetSamplingMode,\r\n                    scene.getEngine(),\r\n                    parsedPostProcess.reusable,\r\n                    parsedPostProcess.textureType,\r\n                    false\r\n                );\r\n            },\r\n            parsedPostProcess,\r\n            scene,\r\n            rootUrl\r\n        );\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.MotionBlurPostProcess\", MotionBlurPostProcess);\r\n"]}