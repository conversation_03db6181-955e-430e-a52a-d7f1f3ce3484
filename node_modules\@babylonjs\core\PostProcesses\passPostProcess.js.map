{"version": 3, "file": "passPostProcess.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/passPostProcess.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAGjD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,0BAA0B,CAAC;AAClC,OAAO,8BAA8B,CAAC;AACtC,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AAIvE;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,WAAW;IAC5C;;;OAGG;IACI,YAAY;QACf,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;;;;;;;;;OAUG;IACH,YACI,IAAY,EACZ,OAAoC,EACpC,SAA2B,IAAI,EAC/B,YAAqB,EACrB,MAAe,EACf,QAAkB,EAClB,cAAsB,SAAS,CAAC,wBAAwB,EACxD,gBAAgB,GAAG,KAAK;QAExB,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;IAChJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,iBAAsB,EAAE,YAAoB,EAAE,KAAY,EAAE,OAAe;QAC5F,OAAO,mBAAmB,CAAC,KAAK,CAC5B,GAAG,EAAE;YACD,OAAO,IAAI,eAAe,CACtB,iBAAiB,CAAC,IAAI,EACtB,iBAAiB,CAAC,OAAO,EACzB,YAAY,EACZ,iBAAiB,CAAC,wBAAwB,EAC1C,iBAAiB,CAAC,OAAO,EACzB,iBAAiB,CAAC,QAAQ,CAC7B,CAAC;QACN,CAAC,EACD,iBAAiB,EACjB,KAAK,EACL,OAAO,CACV,CAAC;IACN,CAAC;CACJ;AAED,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC;AAE1D;;GAEG;AACH,MAAM,OAAO,mBAAoB,SAAQ,WAAW;IAGhD;;;;;;;;OAQG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAa;QACzB,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;YACxB,OAAO;SACV;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,QAAQ,IAAI,CAAC,KAAK,EAAE;YAChB,KAAK,CAAC;gBACF,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;gBACvC,MAAM;YACV,KAAK,CAAC;gBACF,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;gBACvC,MAAM;YACV,KAAK,CAAC;gBACF,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;gBACvC,MAAM;YACV,KAAK,CAAC;gBACF,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;gBACvC,MAAM;YACV,KAAK,CAAC;gBACF,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;gBACvC,MAAM;YACV,KAAK,CAAC;gBACF,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;gBACvC,MAAM;SACb;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,qBAAqB,CAAC;IACjC,CAAC;IAED;;;;;;;;;;OAUG;IACH,YACI,IAAY,EACZ,OAAoC,EACpC,SAA2B,IAAI,EAC/B,YAAqB,EACrB,MAAe,EACf,QAAkB,EAClB,cAAsB,SAAS,CAAC,wBAAwB,EACxD,gBAAgB,GAAG,KAAK;QAExB,KAAK,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,mBAAmB,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAxEtJ,UAAK,GAAG,CAAC,CAAC;IAyElB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,iBAAsB,EAAE,YAAoB,EAAE,KAAY,EAAE,OAAe;QAC5F,OAAO,mBAAmB,CAAC,KAAK,CAC5B,GAAG,EAAE;YACD,OAAO,IAAI,mBAAmB,CAC1B,iBAAiB,CAAC,IAAI,EACtB,iBAAiB,CAAC,OAAO,EACzB,YAAY,EACZ,iBAAiB,CAAC,wBAAwB,EAC1C,iBAAiB,CAAC,OAAO,EACzB,iBAAiB,CAAC,QAAQ,CAC7B,CAAC;QACN,CAAC,EACD,iBAAiB,EACjB,KAAK,EACL,OAAO,CACV,CAAC;IACN,CAAC;CACJ;AAED,MAAM,CAAC,0BAA0B,GAAG,CAAC,MAAc,EAAE,EAAE;IACnD,OAAO,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,6BAA6B,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,wBAAwB,CAAC,CAAC;AAC/I,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { PostProcessOptions } from \"./postProcess\";\r\nimport { PostProcess } from \"./postProcess\";\r\nimport { Engine } from \"../Engines/engine\";\r\n\r\nimport \"../Shaders/pass.fragment\";\r\nimport \"../Shaders/passCube.fragment\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\n\r\nimport type { Scene } from \"../scene\";\r\n\r\n/**\r\n * PassPostProcess which produces an output the same as it's input\r\n */\r\nexport class PassPostProcess extends PostProcess {\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"PassPostProcess\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"PassPostProcess\";\r\n    }\r\n\r\n    /**\r\n     * Creates the PassPostProcess\r\n     * @param name The name of the effect.\r\n     * @param options The required width/height ratio to downsize to before computing the render pass.\r\n     * @param camera The camera to apply the render pass to.\r\n     * @param samplingMode The sampling mode to be used when computing the pass. (default: 0)\r\n     * @param engine The engine which the post process will be applied. (default: current engine)\r\n     * @param reusable If the post process can be reused on the same frame. (default: false)\r\n     * @param textureType The type of texture to be used when performing the post processing.\r\n     * @param blockCompilation If compilation of the shader should not be done in the constructor. The updateEffect method can be used to compile the shader at a later time. (default: false)\r\n     */\r\n    constructor(\r\n        name: string,\r\n        options: number | PostProcessOptions,\r\n        camera: Nullable<Camera> = null,\r\n        samplingMode?: number,\r\n        engine?: Engine,\r\n        reusable?: boolean,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        blockCompilation = false\r\n    ) {\r\n        super(name, \"pass\", null, null, options, camera, samplingMode, engine, reusable, undefined, textureType, undefined, null, blockCompilation);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _Parse(parsedPostProcess: any, targetCamera: Camera, scene: Scene, rootUrl: string) {\r\n        return SerializationHelper.Parse(\r\n            () => {\r\n                return new PassPostProcess(\r\n                    parsedPostProcess.name,\r\n                    parsedPostProcess.options,\r\n                    targetCamera,\r\n                    parsedPostProcess.renderTargetSamplingMode,\r\n                    parsedPostProcess._engine,\r\n                    parsedPostProcess.reusable\r\n                );\r\n            },\r\n            parsedPostProcess,\r\n            scene,\r\n            rootUrl\r\n        );\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PassPostProcess\", PassPostProcess);\r\n\r\n/**\r\n * PassCubePostProcess which produces an output the same as it's input (which must be a cube texture)\r\n */\r\nexport class PassCubePostProcess extends PostProcess {\r\n    private _face = 0;\r\n\r\n    /**\r\n     * Gets or sets the cube face to display.\r\n     *  * 0 is +X\r\n     *  * 1 is -X\r\n     *  * 2 is +Y\r\n     *  * 3 is -Y\r\n     *  * 4 is +Z\r\n     *  * 5 is -Z\r\n     */\r\n    public get face(): number {\r\n        return this._face;\r\n    }\r\n\r\n    public set face(value: number) {\r\n        if (value < 0 || value > 5) {\r\n            return;\r\n        }\r\n\r\n        this._face = value;\r\n        switch (this._face) {\r\n            case 0:\r\n                this.updateEffect(\"#define POSITIVEX\");\r\n                break;\r\n            case 1:\r\n                this.updateEffect(\"#define NEGATIVEX\");\r\n                break;\r\n            case 2:\r\n                this.updateEffect(\"#define POSITIVEY\");\r\n                break;\r\n            case 3:\r\n                this.updateEffect(\"#define NEGATIVEY\");\r\n                break;\r\n            case 4:\r\n                this.updateEffect(\"#define POSITIVEZ\");\r\n                break;\r\n            case 5:\r\n                this.updateEffect(\"#define NEGATIVEZ\");\r\n                break;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"PassCubePostProcess\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"PassCubePostProcess\";\r\n    }\r\n\r\n    /**\r\n     * Creates the PassCubePostProcess\r\n     * @param name The name of the effect.\r\n     * @param options The required width/height ratio to downsize to before computing the render pass.\r\n     * @param camera The camera to apply the render pass to.\r\n     * @param samplingMode The sampling mode to be used when computing the pass. (default: 0)\r\n     * @param engine The engine which the post process will be applied. (default: current engine)\r\n     * @param reusable If the post process can be reused on the same frame. (default: false)\r\n     * @param textureType The type of texture to be used when performing the post processing.\r\n     * @param blockCompilation If compilation of the shader should not be done in the constructor. The updateEffect method can be used to compile the shader at a later time. (default: false)\r\n     */\r\n    constructor(\r\n        name: string,\r\n        options: number | PostProcessOptions,\r\n        camera: Nullable<Camera> = null,\r\n        samplingMode?: number,\r\n        engine?: Engine,\r\n        reusable?: boolean,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        blockCompilation = false\r\n    ) {\r\n        super(name, \"passCube\", null, null, options, camera, samplingMode, engine, reusable, \"#define POSITIVEX\", textureType, undefined, null, blockCompilation);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _Parse(parsedPostProcess: any, targetCamera: Camera, scene: Scene, rootUrl: string) {\r\n        return SerializationHelper.Parse(\r\n            () => {\r\n                return new PassCubePostProcess(\r\n                    parsedPostProcess.name,\r\n                    parsedPostProcess.options,\r\n                    targetCamera,\r\n                    parsedPostProcess.renderTargetSamplingMode,\r\n                    parsedPostProcess._engine,\r\n                    parsedPostProcess.reusable\r\n                );\r\n            },\r\n            parsedPostProcess,\r\n            scene,\r\n            rootUrl\r\n        );\r\n    }\r\n}\r\n\r\nEngine._RescalePostProcessFactory = (engine: Engine) => {\r\n    return new PassPostProcess(\"rescale\", 1, null, Constants.TEXTURE_BILINEAR_SAMPLINGMODE, engine, false, Constants.TEXTURETYPE_UNSIGNED_INT);\r\n};\r\n"]}