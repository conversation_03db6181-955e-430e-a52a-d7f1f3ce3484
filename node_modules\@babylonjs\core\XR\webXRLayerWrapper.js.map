{"version": 3, "file": "webXRLayerWrapper.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/XR/webXRLayerWrapper.ts"], "names": [], "mappings": "AAYA;;;GAGG;AACH,MAAM,OAAO,iBAAiB;IAE1B;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,SAAS,IAAI,cAAc,IAAI,OAAQ,IAAI,CAAC,KAAsB,CAAC,cAAc,IAAI,QAAQ,CAAC;IAC9G,CAAC;IAED;;;OAGG;IACH,IAAW,cAAc;QACrB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,OAAQ,IAAI,CAAC,KAAsB,CAAC,cAAe,CAAC;SACvD;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,IAAW,cAAc,CAAC,KAAuB;QAC7C,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,KAAsB,CAAC,cAAc,GAAG,GAAG,CAAC;SACrD;IACL,CAAC;IAED;;;;OAIG;IACI,iCAAiC,CAAC,gBAAqC;QAC1E,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,kCAAkC,CAAC,gBAAgB,CAAC,CAAC;QAC7E,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SAC3B;IACL,CAAC;IAED;IACI,4CAA4C;IACrC,QAAsB;IAC7B,6CAA6C;IACtC,SAAuB;IAC9B,sDAAsD;IACtC,KAAc;IAC9B,kDAAkD;IAClC,SAAyB;IACzC,6DAA6D;IACrD,kCAAoH;QARrH,aAAQ,GAAR,QAAQ,CAAc;QAEtB,cAAS,GAAT,SAAS,CAAc;QAEd,UAAK,GAAL,KAAK,CAAS;QAEd,cAAS,GAAT,SAAS,CAAgB;QAEjC,uCAAkC,GAAlC,kCAAkC,CAAkF;QAzDxH,gBAAW,GAAoD,IAAI,CAAC;IA0DzE,CAAC;CACP", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { WebXRLayerRenderTargetTextureProvider } from \"./webXRRenderTargetTextureProvider\";\r\nimport type { WebXRSessionManager } from \"./webXRSessionManager\";\r\n\r\n/** Covers all supported subclasses of WebXR's XRCompositionLayer */\r\n// TODO (rgerd): Extend for all other subclasses of XRCompositionLayer.\r\nexport type WebXRCompositionLayerType = \"XRProjectionLayer\";\r\nexport type WebXRQuadLayerType = \"XRQuadLayer\";\r\n\r\n/** Covers all supported subclasses of WebXR's XRLayer */\r\nexport type WebXRLayerType = \"XRWebGLLayer\" | WebXRCompositionLayerType | WebXRQuadLayerType;\r\n\r\n/**\r\n * Wrapper over subclasses of XRLayer.\r\n * @internal\r\n */\r\nexport class WebXRLayerWrapper {\r\n    private _rttWrapper: Nullable<WebXRLayerRenderTargetTextureProvider> = null;\r\n    /**\r\n     * Check if fixed foveation is supported on this device\r\n     */\r\n    public get isFixedFoveationSupported(): boolean {\r\n        return this.layerType == \"XRWebGLLayer\" && typeof (this.layer as XRWebGLLayer).fixedFoveation == \"number\";\r\n    }\r\n\r\n    /**\r\n     * Get the fixed foveation currently set, as specified by the webxr specs\r\n     * If this returns null, then fixed foveation is not supported\r\n     */\r\n    public get fixedFoveation(): Nullable<number> {\r\n        if (this.isFixedFoveationSupported) {\r\n            return (this.layer as XRWebGLLayer).fixedFoveation!;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Set the fixed foveation to the specified value, as specified by the webxr specs\r\n     * This value will be normalized to be between 0 and 1, 1 being max foveation, 0 being no foveation\r\n     */\r\n    public set fixedFoveation(value: Nullable<number>) {\r\n        if (this.isFixedFoveationSupported) {\r\n            const val = Math.max(0, Math.min(1, value || 0));\r\n            (this.layer as XRWebGLLayer).fixedFoveation = val;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create a render target provider for the wrapped layer.\r\n     * @param xrSessionManager The XR Session Manager\r\n     * @returns A new render target texture provider for the wrapped layer.\r\n     */\r\n    public createRenderTargetTextureProvider(xrSessionManager: WebXRSessionManager): WebXRLayerRenderTargetTextureProvider {\r\n        this._rttWrapper = this._createRenderTargetTextureProvider(xrSessionManager);\r\n        return this._rttWrapper;\r\n    }\r\n\r\n    public dispose(): void {\r\n        if (this._rttWrapper) {\r\n            this._rttWrapper.dispose();\r\n            this._rttWrapper = null;\r\n        }\r\n    }\r\n\r\n    protected constructor(\r\n        /** The width of the layer's framebuffer. */\r\n        public getWidth: () => number,\r\n        /** The height of the layer's framebuffer. */\r\n        public getHeight: () => number,\r\n        /** The XR layer that this WebXRLayerWrapper wraps. */\r\n        public readonly layer: XRLayer,\r\n        /** The type of XR layer that is being wrapped. */\r\n        public readonly layerType: WebXRLayerType,\r\n        /** Create a render target provider for the wrapped layer. */\r\n        private _createRenderTargetTextureProvider: (xrSessionManager: WebXRSessionManager) => WebXRLayerRenderTargetTextureProvider\r\n    ) {}\r\n}\r\n"]}