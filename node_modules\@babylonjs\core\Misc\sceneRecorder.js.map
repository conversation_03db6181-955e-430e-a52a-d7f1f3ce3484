{"version": 3, "file": "sceneRecorder.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/sceneRecorder.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,KAAK,EAAE,MAAM,iBAAiB,CAAC;AACxC,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAC7D,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AACjE,OAAO,EAAE,eAAe,EAAE,MAAM,mCAAmC,CAAC;AACpE,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAC;AAEjE;;GAEG;AACH,MAAM,OAAO,aAAa;IAA1B;QACY,kBAAa,GAAoB,IAAI,CAAC;IA0SlD,CAAC;IAvSG;;;OAGG;IACI,KAAK,CAAC,KAAY;QACrB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAChD,IAAI,CAAC,UAAU,GAAG,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACnD,mBAAmB,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACrD,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,4BAA4B,GAAG,OAAO,CAAC,qBAAqB,CAAC;QACnE,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC;QAEtC,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAChD,MAAM,OAAO,GAAG,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9D,mBAAmB,CAAC,oBAAoB,GAAG,KAAK,CAAC;QACjD,MAAM,SAAS,GAAQ,EAAE,CAAC;QAE1B,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;YACxB,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC;SACnF;QAED,OAAO,CAAC,qBAAqB,GAAG,4BAA4B,CAAC;QAE7D,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,aAAa,CAAC,GAAW,EAAE,QAAe,EAAE,OAAc,EAAE,SAAc;QAC9E,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/C,OAAO,IAAI,CAAC;SACf;QAED,WAAW;QACX,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACpF,IAAI,QAAQ,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE;gBACpC,OAAO,KAAK,CAAC;aAChB;YAED,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBACvB,OAAO,IAAI,CAAC;aACf;YACD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAClD,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,KAAK,CAAC,EAAE;oBACpC,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;oBACzB,OAAO,KAAK,CAAC;iBAChB;aACJ;YACD,OAAO,IAAI,CAAC;SACf;QAED,6CAA6C;QAC7C,MAAM,iBAAiB,GAAa,EAAE,CAAC;QACvC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAClD,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;YACvC,MAAM,gBAAgB,GAAG,cAAc,CAAC,QAAQ,CAAC;YAEjD,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACzC,wCAAwC;YACxC,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,gBAAgB,CAAC,CAAC;YAC9E,IAAI,cAAc,CAAC,MAAM,EAAE;gBACvB,sBAAsB;gBACtB,MAAM,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBAExC,MAAM,SAAS,GAAQ,EAAE,CAAC;gBAC1B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,aAAa,EAAE,SAAS,CAAC,EAAE;oBACjE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;wBACjB,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;qBACvB;oBACD,SAAS,CAAC,OAAO,GAAG;wBAChB,EAAE,EAAE,aAAa,CAAC,EAAE,IAAI,aAAa,CAAC,IAAI;qBAC7C,CAAC;oBACF,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBAClC;aACJ;iBAAM;gBACH,oBAAoB;gBACpB,MAAM,SAAS,GAAQ;oBACnB,OAAO,EAAE;wBACL,QAAQ,EAAE,cAAc,CAAC,EAAE,IAAI,cAAc,CAAC,IAAI;qBACrD;iBACJ,CAAC;gBACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;oBACjB,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;iBACvB;gBACD,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAClC;SACJ;QAED,2BAA2B;QAC3B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACjD,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,eAAe,GAAG,aAAa,CAAC,QAAQ,CAAC;YAE/C,mBAAmB;YACnB,IAAI,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;gBACnD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;oBACjB,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;iBACvB;gBAED,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aACtC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,eAAe,CAAC,aAAkB,EAAE,aAAkB,EAAE,SAAc;QAC1E,IAAI,mBAAmB,GAAG,KAAK,CAAC;QAEhC,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE;YAC9B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE;gBAC5D,SAAS;aACZ;YACD,MAAM,aAAa,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;YACzC,IAAI,SAAS,GAAG,KAAK,CAAC;YAEtB,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;gBAC9B,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;aAC9E;iBAAM,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,iBAAiB,EAAE;gBACpG,SAAS,GAAG,aAAa,KAAK,YAAY,CAAC;aAC9C;iBAAM,IAAI,OAAO,aAAa,KAAK,QAAQ,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;gBAC9E,MAAM,SAAS,GAAG,EAAE,CAAC;gBACrB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,YAAY,EAAE,SAAS,CAAC,EAAE;oBAC/D,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;oBAC5B,mBAAmB,GAAG,IAAI,CAAC;iBAC9B;aACJ;YAED,IAAI,SAAS,EAAE;gBACX,mBAAmB,GAAG,IAAI,CAAC;gBAC3B,SAAS,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;aAClC;SACJ;QAED,OAAO,CAAC,mBAAmB,CAAC;IAChC,CAAC;IAEO,mBAAmB,CAAC,GAAW,EAAE,QAAe,EAAE,OAAc,EAAE,SAAc;QACpF,SAAS;QACT,IAAI,QAAQ,KAAK,OAAO,EAAE;YACtB,OAAO;SACV;QAED,IAAI,QAAQ,IAAI,OAAO,EAAE;YACrB,SAAS;YACT,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACnD,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE;oBACvD,OAAO;iBACV;aACJ;iBAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;gBACpE,SAAS;gBACT,MAAM,SAAS,GAAG,EAAE,CAAC;gBACrB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE;oBACrD,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;iBAC9B;gBACD,OAAO;aACV;SACJ;IACL,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,KAAY,EAAE,EAAU;QAC1D,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAEvE,KAAK,MAAM,UAAU,IAAI,aAAa,EAAE;YACpC,IAAI,UAAU,EAAE;gBACZ,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;gBACrC,KAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE;oBACtE,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC;oBAC5B,IAAI,SAAS,IAAI,SAAS,CAAC,EAAE,KAAK,EAAE,EAAE;wBAClC,OAAO,SAAS,CAAC;qBACpB;iBACJ;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,UAAU,CAAC,SAAuB,EAAE,KAAY;QAC1D,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YAC/B,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;SACrC;QAED,QAAQ;QACR,MAAM,QAAQ,GAAG,KAAY,CAAC;QAC9B,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;YAC1B,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEhC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,kBAAkB,EAAE;gBACxD,gBAAgB;gBAChB,QAAQ,IAAI,EAAE;oBACV,KAAK,SAAS;wBACV,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;wBAC/G,MAAM;oBACV,KAAK,QAAQ;wBACT,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;wBAC7G,MAAM;oBACV,KAAK,kBAAkB;wBACnB,IAAI,CAAC,oBAAoB,CACrB,MAAM,EACN,KAAK,EACL,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,EAAE,CAAC,EAC9C,CAAC,IAAI,EAAE,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAC/C,CAAC;wBACF,MAAM;oBACV,KAAK,QAAQ;wBACT,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;wBAC/G,MAAM;oBACV,KAAK,WAAW;wBACZ,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;wBACnH,MAAM;oBACV,KAAK,WAAW;wBACZ,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;wBACvH,MAAM;oBACV,KAAK,gBAAgB;wBACjB,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;wBAC5H,MAAM;oBACV,KAAK,gBAAgB;wBACjB,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;wBACjI,MAAM;oBACV,KAAK,iBAAiB;wBAClB,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;wBACnI,MAAM;oBACV,KAAK,qBAAqB;wBACtB,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;wBAChI,MAAM;oBACV,KAAK,eAAe;wBAChB,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;wBAC/H,MAAM;iBACb;aACJ;iBAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;gBACzB,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;aAC3B;iBAAM,IAAI,QAAQ,CAAC,SAAS,EAAE;gBAC3B,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;aAC9B;SACJ;IACL,CAAC;IAEO,MAAM,CAAC,wBAAwB,CAAC,SAAc,EAAE,MAAW;QAC/D,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;YAC1B,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;YAE9B,IAAI,QAAQ,KAAK,SAAS,EAAE;gBACxB,SAAS;aACZ;YAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC7C,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;aACzB;iBAAM,IAAI,QAAQ,CAAC,SAAS,EAAE;gBAC3B,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;aAC9B;iBAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC1D,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;aACnD;SACJ;IACL,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,OAAc,EAAE,KAAY,EAAE,MAA2B,EAAE,MAA2B;QACtH,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC1B,SAAS;YACT,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE,KAAK,SAAS,EAAE;gBACnD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAE/C,IAAI,YAAY,EAAE;oBACd,2EAA2E;oBAC3E,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;oBACpD,sDAAsD;oBACtD,mBAAmB,CAAC,eAAe,CAAC,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;iBAC1E;aACJ;iBAAM,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAChE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAC/C,MAAM,EAAE,OAAO,EAAE,CAAC;aACrB;iBAAM;gBACH,MAAM;gBACN,MAAM,CAAC,MAAM,CAAC,CAAC;aAClB;SACJ;IACL,CAAC;CACJ", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Scene } from \"../scene\";\r\nimport type { Nullable } from \"../types\";\r\nimport { SceneSerializer } from \"./sceneSerializer\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { Light } from \"../Lights/light\";\r\nimport { Camera } from \"../Cameras/camera\";\r\nimport { Skeleton } from \"../Bones/skeleton\";\r\nimport { Material } from \"../Materials/material\";\r\nimport { MultiMaterial } from \"../Materials/multiMaterial\";\r\nimport { TransformNode } from \"../Meshes/transformNode\";\r\nimport { ParticleSystem } from \"../Particles/particleSystem\";\r\nimport { MorphTargetManager } from \"../Morph/morphTargetManager\";\r\nimport { ShadowGenerator } from \"../Lights/Shadows/shadowGenerator\";\r\nimport { PostProcess } from \"../PostProcesses/postProcess\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { SerializationHelper } from \"./decorators.serialization\";\r\n\r\n/**\r\n * Class used to record delta files between 2 scene states\r\n */\r\nexport class SceneRecorder {\r\n    private _trackedScene: Nullable<Scene> = null;\r\n    private _savedJSON: any;\r\n\r\n    /**\r\n     * Track a given scene. This means the current scene state will be considered the original state\r\n     * @param scene defines the scene to track\r\n     */\r\n    public track(scene: Scene) {\r\n        this._trackedScene = scene;\r\n\r\n        SerializationHelper.AllowLoadingUniqueId = true;\r\n        this._savedJSON = SceneSerializer.Serialize(scene);\r\n        SerializationHelper.AllowLoadingUniqueId = false;\r\n    }\r\n\r\n    /**\r\n     * Get the delta between current state and original state\r\n     * @returns a any containing the delta\r\n     */\r\n    public getDelta(): any {\r\n        if (!this._trackedScene) {\r\n            return null;\r\n        }\r\n\r\n        const currentForceSerializeBuffers = Texture.ForceSerializeBuffers;\r\n        Texture.ForceSerializeBuffers = false;\r\n\r\n        SerializationHelper.AllowLoadingUniqueId = true;\r\n        const newJSON = SceneSerializer.Serialize(this._trackedScene);\r\n        SerializationHelper.AllowLoadingUniqueId = false;\r\n        const deltaJSON: any = {};\r\n\r\n        for (const node in newJSON) {\r\n            this._compareCollections(node, this._savedJSON[node], newJSON[node], deltaJSON);\r\n        }\r\n\r\n        Texture.ForceSerializeBuffers = currentForceSerializeBuffers;\r\n\r\n        return deltaJSON;\r\n    }\r\n\r\n    private _compareArray(key: string, original: any[], current: any[], deltaJSON: any) {\r\n        if (original.length === 0 && current.length === 0) {\r\n            return true;\r\n        }\r\n\r\n        // Numbers?\r\n        if ((original.length && !isNaN(original[0])) || (current.length && !isNaN(current[0]))) {\r\n            if (original.length !== current.length) {\r\n                return false;\r\n            }\r\n\r\n            if (original.length === 0) {\r\n                return true;\r\n            }\r\n            for (let index = 0; index < original.length; index++) {\r\n                if (original[index] !== current[index]) {\r\n                    deltaJSON[key] = current;\r\n                    return false;\r\n                }\r\n            }\r\n            return true;\r\n        }\r\n\r\n        // let's use uniqueId to find similar objects\r\n        const originalUniqueIds: number[] = [];\r\n        for (let index = 0; index < original.length; index++) {\r\n            const originalObject = original[index];\r\n            const originalUniqueId = originalObject.uniqueId;\r\n\r\n            originalUniqueIds.push(originalUniqueId);\r\n            // Look for that object in current state\r\n            const currentObjects = current.filter((c) => c.uniqueId === originalUniqueId);\r\n            if (currentObjects.length) {\r\n                // We have a candidate\r\n                const currentObject = currentObjects[0];\r\n\r\n                const newObject: any = {};\r\n                if (!this._compareObjects(originalObject, currentObject, newObject)) {\r\n                    if (!deltaJSON[key]) {\r\n                        deltaJSON[key] = [];\r\n                    }\r\n                    newObject.__state = {\r\n                        id: currentObject.id || currentObject.name,\r\n                    };\r\n                    deltaJSON[key].push(newObject);\r\n                }\r\n            } else {\r\n                // We need to delete\r\n                const newObject: any = {\r\n                    __state: {\r\n                        deleteId: originalObject.id || originalObject.name,\r\n                    },\r\n                };\r\n                if (!deltaJSON[key]) {\r\n                    deltaJSON[key] = [];\r\n                }\r\n                deltaJSON[key].push(newObject);\r\n            }\r\n        }\r\n\r\n        // Checking for new objects\r\n        for (let index = 0; index < current.length; index++) {\r\n            const currentObject = current[index];\r\n            const currentUniqueId = currentObject.uniqueId;\r\n\r\n            // Object was added\r\n            if (originalUniqueIds.indexOf(currentUniqueId) === -1) {\r\n                if (!deltaJSON[key]) {\r\n                    deltaJSON[key] = [];\r\n                }\r\n\r\n                deltaJSON[key].push(currentObject);\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    private _compareObjects(originalObjet: any, currentObject: any, deltaJSON: any) {\r\n        let aDifferenceWasFound = false;\r\n\r\n        for (const prop in originalObjet) {\r\n            if (!Object.prototype.hasOwnProperty.call(originalObjet, prop)) {\r\n                continue;\r\n            }\r\n            const originalValue = originalObjet[prop];\r\n            const currentValue = currentObject[prop];\r\n            let diffFound = false;\r\n\r\n            if (Array.isArray(originalValue)) {\r\n                diffFound = JSON.stringify(originalValue) !== JSON.stringify(currentValue);\r\n            } else if (!isNaN(originalValue) || Object.prototype.toString.call(originalValue) == \"[object String]\") {\r\n                diffFound = originalValue !== currentValue;\r\n            } else if (typeof originalValue === \"object\" && typeof currentValue === \"object\") {\r\n                const newObject = {};\r\n                if (!this._compareObjects(originalValue, currentValue, newObject)) {\r\n                    deltaJSON[prop] = newObject;\r\n                    aDifferenceWasFound = true;\r\n                }\r\n            }\r\n\r\n            if (diffFound) {\r\n                aDifferenceWasFound = true;\r\n                deltaJSON[prop] = currentValue;\r\n            }\r\n        }\r\n\r\n        return !aDifferenceWasFound;\r\n    }\r\n\r\n    private _compareCollections(key: string, original: any[], current: any[], deltaJSON: any) {\r\n        // Same ?\r\n        if (original === current) {\r\n            return;\r\n        }\r\n\r\n        if (original && current) {\r\n            // Array?\r\n            if (Array.isArray(original) && Array.isArray(current)) {\r\n                if (this._compareArray(key, original, current, deltaJSON)) {\r\n                    return;\r\n                }\r\n            } else if (typeof original === \"object\" && typeof current === \"object\") {\r\n                // Object\r\n                const newObject = {};\r\n                if (!this._compareObjects(original, current, newObject)) {\r\n                    deltaJSON[key] = newObject;\r\n                }\r\n                return;\r\n            }\r\n        }\r\n    }\r\n\r\n    private static GetShadowGeneratorById(scene: Scene, id: string) {\r\n        const allGenerators = scene.lights.map((l) => l.getShadowGenerators());\r\n\r\n        for (const generators of allGenerators) {\r\n            if (generators) {\r\n                const iterator = generators.values();\r\n                for (let key = iterator.next(); key.done !== true; key = iterator.next()) {\r\n                    const generator = key.value;\r\n                    if (generator && generator.id === id) {\r\n                        return generator;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Apply a given delta to a given scene\r\n     * @param deltaJSON defines the JSON containing the delta\r\n     * @param scene defines the scene to apply the delta to\r\n     */\r\n    public static ApplyDelta(deltaJSON: any | string, scene: Scene) {\r\n        if (typeof deltaJSON === \"string\") {\r\n            deltaJSON = JSON.parse(deltaJSON);\r\n        }\r\n\r\n        // Scene\r\n        const anyScene = scene as any;\r\n        for (const prop in deltaJSON) {\r\n            const source = deltaJSON[prop];\r\n            const property = anyScene[prop];\r\n\r\n            if (Array.isArray(property) || prop === \"shadowGenerators\") {\r\n                // Restore array\r\n                switch (prop) {\r\n                    case \"cameras\":\r\n                        this._ApplyDeltaForEntity(source, scene, scene.getCameraById.bind(scene), (data) => Camera.Parse(data, scene));\r\n                        break;\r\n                    case \"lights\":\r\n                        this._ApplyDeltaForEntity(source, scene, scene.getLightById.bind(scene), (data) => Light.Parse(data, scene));\r\n                        break;\r\n                    case \"shadowGenerators\":\r\n                        this._ApplyDeltaForEntity(\r\n                            source,\r\n                            scene,\r\n                            (id) => this.GetShadowGeneratorById(scene, id),\r\n                            (data) => ShadowGenerator.Parse(data, scene)\r\n                        );\r\n                        break;\r\n                    case \"meshes\":\r\n                        this._ApplyDeltaForEntity(source, scene, scene.getMeshById.bind(scene), (data) => Mesh.Parse(data, scene, \"\"));\r\n                        break;\r\n                    case \"skeletons\":\r\n                        this._ApplyDeltaForEntity(source, scene, scene.getSkeletonById.bind(scene), (data) => Skeleton.Parse(data, scene));\r\n                        break;\r\n                    case \"materials\":\r\n                        this._ApplyDeltaForEntity(source, scene, scene.getMaterialById.bind(scene), (data) => Material.Parse(data, scene, \"\"));\r\n                        break;\r\n                    case \"multiMaterials\":\r\n                        this._ApplyDeltaForEntity(source, scene, scene.getMaterialById.bind(scene), (data) => MultiMaterial.Parse(data, scene, \"\"));\r\n                        break;\r\n                    case \"transformNodes\":\r\n                        this._ApplyDeltaForEntity(source, scene, scene.getTransformNodeById.bind(scene), (data) => TransformNode.Parse(data, scene, \"\"));\r\n                        break;\r\n                    case \"particleSystems\":\r\n                        this._ApplyDeltaForEntity(source, scene, scene.getParticleSystemById.bind(scene), (data) => ParticleSystem.Parse(data, scene, \"\"));\r\n                        break;\r\n                    case \"morphTargetManagers\":\r\n                        this._ApplyDeltaForEntity(source, scene, scene.getMorphTargetById.bind(scene), (data) => MorphTargetManager.Parse(data, scene));\r\n                        break;\r\n                    case \"postProcesses\":\r\n                        this._ApplyDeltaForEntity(source, scene, scene.getPostProcessByName.bind(scene), (data) => PostProcess.Parse(data, scene, \"\"));\r\n                        break;\r\n                }\r\n            } else if (!isNaN(property)) {\r\n                anyScene[prop] = source;\r\n            } else if (property.fromArray) {\r\n                property.fromArray(source);\r\n            }\r\n        }\r\n    }\r\n\r\n    private static _ApplyPropertiesToEntity(deltaJSON: any, entity: any) {\r\n        for (const prop in deltaJSON) {\r\n            const source = deltaJSON[prop];\r\n            const property = entity[prop];\r\n\r\n            if (property === undefined) {\r\n                continue;\r\n            }\r\n\r\n            if (!isNaN(property) || Array.isArray(property)) {\r\n                entity[prop] = source;\r\n            } else if (property.fromArray) {\r\n                property.fromArray(source);\r\n            } else if (typeof property === \"object\" && property !== null) {\r\n                this._ApplyPropertiesToEntity(source, property);\r\n            }\r\n        }\r\n    }\r\n\r\n    private static _ApplyDeltaForEntity(sources: any[], scene: Scene, finder: (id: string) => any, addNew: (data: any) => void) {\r\n        for (const source of sources) {\r\n            // Update\r\n            if (source.__state && source.__state.id !== undefined) {\r\n                const targetEntity = finder(source.__state.id);\r\n\r\n                if (targetEntity) {\r\n                    // This first pass applies properties that aren't on the serialization list\r\n                    this._ApplyPropertiesToEntity(source, targetEntity);\r\n                    // The second pass applies the serializable properties\r\n                    SerializationHelper.ParseProperties(source, targetEntity, scene, null);\r\n                }\r\n            } else if (source.__state && source.__state.deleteId !== undefined) {\r\n                const target = finder(source.__state.deleteId);\r\n                target?.dispose();\r\n            } else {\r\n                // New\r\n                addNew(source);\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}