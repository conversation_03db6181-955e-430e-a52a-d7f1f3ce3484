import type { Nullable } from "../../../types";
import { Observable } from "../../../Misc/observable";
import type { IAnimatable } from "../../../Animations/animatable.interface";
import type { Camera } from "../../../Cameras/camera";
import type { IDisposable, Scene } from "../../../scene";
import { GlowLayer } from "../../../Layers/glowLayer";
import { SharpenPostProcess } from "../../../PostProcesses/sharpenPostProcess";
import { ImageProcessingPostProcess } from "../../../PostProcesses/imageProcessingPostProcess";
import { ChromaticAberrationPostProcess } from "../../../PostProcesses/chromaticAberrationPostProcess";
import { GrainPostProcess } from "../../../PostProcesses/grainPostProcess";
import { FxaaPostProcess } from "../../../PostProcesses/fxaaPostProcess";
import { PostProcessRenderPipeline } from "../../../PostProcesses/RenderPipeline/postProcessRenderPipeline";
import { DepthOfFieldEffect, DepthOfFieldEffectBlurLevel } from "../../../PostProcesses/depthOfFieldEffect";
import "../../../PostProcesses/RenderPipeline/postProcessRenderPipelineManagerSceneComponent";
import type { Animation } from "../../../Animations/animation";
/**
 * The default rendering pipeline can be added to a scene to apply common post processing effects such as anti-aliasing or depth of field.
 * See https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/defaultRenderingPipeline
 */
export declare class DefaultRenderingPipeline extends PostProcessRenderPipeline implements IDisposable, IAnimatable {
    private _scene;
    private _camerasToBeAttached;
    /**
     * ID of the sharpen post process,
     */
    private readonly SharpenPostProcessId;
    /**
     * @ignore
     * ID of the image processing post process;
     */
    readonly ImageProcessingPostProcessId = "ImageProcessingPostProcessEffect";
    /**
     * @ignore
     * ID of the Fast Approximate Anti-Aliasing post process;
     */
    readonly FxaaPostProcessId = "FxaaPostProcessEffect";
    /**
     * ID of the chromatic aberration post process,
     */
    private readonly ChromaticAberrationPostProcessId;
    /**
     * ID of the grain post process
     */
    private readonly GrainPostProcessId;
    /**
     * Sharpen post process which will apply a sharpen convolution to enhance edges
     */
    sharpen: SharpenPostProcess;
    private _sharpenEffect;
    private bloom;
    /**
     * Depth of field effect, applies a blur based on how far away objects are from the focus distance.
     */
    depthOfField: DepthOfFieldEffect;
    /**
     * The Fast Approximate Anti-Aliasing post process which attempts to remove aliasing from an image.
     */
    fxaa: FxaaPostProcess;
    /**
     * Image post processing pass used to perform operations such as tone mapping or color grading.
     */
    imageProcessing: ImageProcessingPostProcess;
    /**
     * Chromatic aberration post process which will shift rgb colors in the image
     */
    chromaticAberration: ChromaticAberrationPostProcess;
    private _chromaticAberrationEffect;
    /**
     * Grain post process which add noise to the image
     */
    grain: GrainPostProcess;
    private _grainEffect;
    /**
     * Glow post process which adds a glow to emissive areas of the image
     */
    private _glowLayer;
    /**
     * Animations which can be used to tweak settings over a period of time
     */
    animations: Animation[];
    private _imageProcessingConfigurationObserver;
    private _sharpenEnabled;
    private _bloomEnabled;
    private _depthOfFieldEnabled;
    private _depthOfFieldBlurLevel;
    private _fxaaEnabled;
    private _imageProcessingEnabled;
    private _defaultPipelineTextureType;
    private _bloomScale;
    private _chromaticAberrationEnabled;
    private _grainEnabled;
    private _buildAllowed;
    /**
     * Enable or disable automatic building of the pipeline when effects are enabled and disabled.
     * If false, you will have to manually call prepare() to update the pipeline.
     */
    get automaticBuild(): boolean;
    set automaticBuild(value: boolean);
    /**
     * This is triggered each time the pipeline has been built.
     */
    onBuildObservable: Observable<DefaultRenderingPipeline>;
    /**
     * Gets active scene
     */
    get scene(): Scene;
    /**
     * Enable or disable the sharpen process from the pipeline
     */
    set sharpenEnabled(enabled: boolean);
    get sharpenEnabled(): boolean;
    private _resizeObserver;
    private _hardwareScaleLevel;
    private _bloomKernel;
    /**
     * Specifies the size of the bloom blur kernel, relative to the final output size
     */
    get bloomKernel(): number;
    set bloomKernel(value: number);
    /**
     * Specifies the weight of the bloom in the final rendering
     */
    private _bloomWeight;
    /**
     * Specifies the luma threshold for the area that will be blurred by the bloom
     */
    private _bloomThreshold;
    private _hdr;
    /**
     * The strength of the bloom.
     */
    set bloomWeight(value: number);
    get bloomWeight(): number;
    /**
     * The luminance threshold to find bright areas of the image to bloom.
     */
    set bloomThreshold(value: number);
    get bloomThreshold(): number;
    /**
     * The scale of the bloom, lower value will provide better performance.
     */
    set bloomScale(value: number);
    get bloomScale(): number;
    /**
     * Enable or disable the bloom from the pipeline
     */
    set bloomEnabled(enabled: boolean);
    get bloomEnabled(): boolean;
    private _rebuildBloom;
    /**
     * If the depth of field is enabled.
     */
    get depthOfFieldEnabled(): boolean;
    set depthOfFieldEnabled(enabled: boolean);
    /**
     * Blur level of the depth of field effect. (Higher blur will effect performance)
     */
    get depthOfFieldBlurLevel(): DepthOfFieldEffectBlurLevel;
    set depthOfFieldBlurLevel(value: DepthOfFieldEffectBlurLevel);
    /**
     * If the anti aliasing is enabled.
     */
    set fxaaEnabled(enabled: boolean);
    get fxaaEnabled(): boolean;
    private _samples;
    /**
     * MSAA sample count, setting this to 4 will provide 4x anti aliasing. (default: 1)
     */
    set samples(sampleCount: number);
    get samples(): number;
    /**
     * If image processing is enabled.
     */
    set imageProcessingEnabled(enabled: boolean);
    get imageProcessingEnabled(): boolean;
    /**
     * If glow layer is enabled. (Adds a glow effect to emmissive materials)
     */
    set glowLayerEnabled(enabled: boolean);
    get glowLayerEnabled(): boolean;
    /**
     * Gets the glow layer (or null if not defined)
     */
    get glowLayer(): Nullable<GlowLayer>;
    /**
     * Enable or disable the chromaticAberration process from the pipeline
     */
    set chromaticAberrationEnabled(enabled: boolean);
    get chromaticAberrationEnabled(): boolean;
    /**
     * Enable or disable the grain process from the pipeline
     */
    set grainEnabled(enabled: boolean);
    get grainEnabled(): boolean;
    /**
     * Instantiates a DefaultRenderingPipeline.
     * @param name The rendering pipeline name (default: "")
     * @param hdr If high dynamic range textures should be used (default: true)
     * @param scene The scene linked to this pipeline (default: the last created scene)
     * @param cameras The array of cameras that the rendering pipeline will be attached to (default: scene.cameras)
     * @param automaticBuild If false, you will have to manually call prepare() to update the pipeline (default: true)
     */
    constructor(name?: string, hdr?: boolean, scene?: Scene, cameras?: Camera[], automaticBuild?: boolean);
    /**
     * Get the class name
     * @returns "DefaultRenderingPipeline"
     */
    getClassName(): string;
    /**
     * Force the compilation of the entire pipeline.
     */
    prepare(): void;
    private _hasCleared;
    private _prevPostProcess;
    private _prevPrevPostProcess;
    private _setAutoClearAndTextureSharing;
    private _depthOfFieldSceneObserver;
    private _activeCameraChangedObserver;
    private _activeCamerasChangedObserver;
    private _buildPipeline;
    private _disposePostProcesses;
    /**
     * Adds a camera to the pipeline
     * @param camera the camera to be added
     */
    addCamera(camera: Camera): void;
    /**
     * Removes a camera from the pipeline
     * @param camera the camera to remove
     */
    removeCamera(camera: Camera): void;
    /**
     * Dispose of the pipeline and stop all post processes
     */
    dispose(): void;
    /**
     * Serialize the rendering pipeline (Used when exporting)
     * @returns the serialized object
     */
    serialize(): any;
    /**
     * Parse the serialized pipeline
     * @param source Source pipeline.
     * @param scene The scene to load the pipeline to.
     * @param rootUrl The URL of the serialized pipeline.
     * @returns An instantiated pipeline from the serialized object.
     */
    static Parse(source: any, scene: Scene, rootUrl: string): DefaultRenderingPipeline;
}
