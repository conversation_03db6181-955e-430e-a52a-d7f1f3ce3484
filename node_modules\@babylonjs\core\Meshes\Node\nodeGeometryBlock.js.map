{"version": 3, "file": "nodeGeometryBlock.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Node/nodeGeometryBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAClD,OAAO,EAAE,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;AAEjE,OAAO,EAAE,2BAA2B,EAAE,oCAAoC,EAAE,MAAM,oCAAoC,CAAC;AAEvH,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AAGzD,OAAO,EAAE,MAAM,EAAE,6BAAyB;AAE1C;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAwB1B;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,qCAAqC;IACrC,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAOD;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAa;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAWD;;;OAGG;IACI,YAAY;QACf,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAES,YAAY,CAAC,IAAY;QAC/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,aAAa,CAAC,IAAY;QAChC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,KAAwB;QAC1C,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACtB,SAAS;aACZ;YAED,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE;gBACrC,IAAI,QAAQ,CAAC,UAAU,KAAK,KAAK,EAAE;oBAC/B,OAAO,IAAI,CAAC;iBACf;gBACD,IAAI,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC3C,OAAO,IAAI,CAAC;iBACf;aACJ;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,IAAY;QAClC,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;YAC9B,OAAO,IAAI,CAAC;SACf;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACtB,SAAS;aACZ;YAED,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE;gBACrC,IAAI,QAAQ,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;oBAC9C,OAAO,IAAI,CAAC;iBACf;aACJ;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAAC,SAAgD;QAC5E,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;YACjB,OAAO,IAAI,CAAC;SACf;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACtB,SAAS;aACZ;YAED,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE;gBACrC,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;gBAE3E,IAAI,UAAU,EAAE;oBACZ,OAAO,UAAU,CAAC;iBACrB;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,YAAmB,IAAY;QAtMvB,UAAK,GAAG,EAAE,CAAC;QAET,aAAQ,GAAG,KAAK,CAAC;QACjB,mBAAc,GAAG,KAAK,CAAC;QACvB,kBAAa,GAAG,KAAK,CAAC;QACtB,aAAQ,GAAG,KAAK,CAAC;QACjB,cAAS,GAAG,KAAK,CAAC;QACpB,wBAAmB,GAAW,CAAC,CAAC;QAExC;;WAEG;QACI,sBAAiB,GAAG,IAAI,UAAU,EAAqB,CAAC;QAE/D,gBAAgB;QACT,YAAO,GAAG,IAAI,KAAK,EAA+B,CAAC;QAC1D,gBAAgB;QACT,aAAQ,GAAG,IAAI,KAAK,EAA+B,CAAC;QAG3D,gBAAgB;QACT,sBAAiB,GAAG,EAAE,CAAC;QA8E9B,6FAA6F;QACtF,mBAAc,GAAG,KAAK,CAAC;QAmG1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC;IAC/C,CAAC;IAED;;;;;;;;;OASG;IACI,aAAa,CAAC,IAAY,EAAE,IAA2C,EAAE,aAAsB,KAAK,EAAE,KAAW,EAAE,QAAc,EAAE,QAAc;QACpJ,MAAM,KAAK,GAAG,IAAI,2BAA2B,CAAC,IAAI,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,CAAC,CAAC;QACtG,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;QAC9B,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;QAC3B,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QACpB,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC1B,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAE1B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,cAAc,CAAC,IAAY,EAAE,IAA2C,EAAE,KAAmC;QAChH,KAAK,GAAG,KAAK,IAAI,IAAI,2BAA2B,CAAC,IAAI,EAAE,IAAI,EAAE,oCAAoC,CAAC,MAAM,CAAC,CAAC;QAC1G,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE1B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,6DAA6D;IACnD,WAAW,CAAC,KAA6B;QAC/C,wCAAwC;IAC5C,CAAC;IAED,6DAA6D;IACnD,gBAAgB,CAAC,KAA6B;QACpD,kCAAkC;IACtC,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,KAA6B;QACtC,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,OAAO,EAAE;YACjC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBAC7D,OAAO,KAAK,CAAC;aAChB;YAED,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC;SACnD;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;QAE9B,wCAAwC;QACxC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;YAC9B,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;oBACnB,iBAAiB;oBACjB,KAAK,CAAC,6BAA6B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACnD;gBACD,SAAS;aACZ;YAED,MAAM,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC;YAC9C,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,EAAE;gBACzB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;aACtB;SACJ;QAED,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE7B,OAAO;QACP,IAAI,KAAK,CAAC,OAAO,EAAE;YACf,MAAM,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;SAChE;QAED,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;QAC9B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACxB,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC,GAAG,GAAG,GAAG,CAAC;QAEnD,2BAA2B;QAC3B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE;gBACrC,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC;gBAElC,IAAI,KAAK,EAAE;oBACP,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;iBACtB;aACJ;SACJ;QAED,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE7C,OAAO,KAAK,CAAC;IACjB,CAAC;IAES,oBAAoB,CAAC,WAAmB,EAAE,WAAmB,EAAE,aAAa,GAAG,KAAK;QAC1F,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,4BAA4B,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SACtF;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SACjF;QACD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IAClF,CAAC;IAED;;OAEG;IACI,UAAU;QACb,aAAa;IACjB,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,aAAa;IACjB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,IAAY;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;QAE3D,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;SACpB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,IAAY;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;QAE5D,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;SACpB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QACpC,mBAAmB,CAAC,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAClE,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;QACvC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAErC,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAChC,mBAAmB,CAAC,OAAO,GAAG,EAAE,CAAC;QAEjC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC7B,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;SACtD;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;SAC7D;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,mBAAwB;QACxC,IAAI,CAAC,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC;QACtC,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC;QAC7C,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,mBAAmB,CAAC,cAAc,CAAC;QAC3D,IAAI,CAAC,6CAA6C,CAAC,mBAAmB,CAAC,CAAC;IAC5E,CAAC;IAEO,6CAA6C,CAAC,mBAAwB;QAC1E,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,MAAM,CAAC;QACpD,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,OAAO,CAAC;QACtD,IAAI,gBAAgB,EAAE;YAClB,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;gBACnC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE5D,IAAI,CAAC,KAAK,EAAE;oBACR,OAAO;iBACV;gBAED,IAAI,IAAI,CAAC,WAAW,EAAE;oBAClB,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;iBACxC;gBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;oBAC/C,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;iBACxD;gBACD,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;oBACjD,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE;wBAC7B,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;qBAC5B;yBAAM;wBACH,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAE3C,IAAI,SAAS,EAAE;4BACX,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;yBACjD;qBACJ;iBACJ;YACL,CAAC,CAAC,CAAC;SACN;QACD,IAAI,iBAAiB,EAAE;YACnB,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,CAAS,EAAE,EAAE;gBAC/C,IAAI,IAAI,CAAC,WAAW,EAAE;oBAClB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;iBAClD;gBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;oBACzD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;iBAClE;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAES,mBAAmB;QACzB,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC5C,OAAO,GAAG,YAAY,qBAAqB,IAAI,CAAC,cAAc,KAAK,CAAC;IACxE,CAAC;IAED;;OAEG;IACI,6BAA6B,CAAC,aAAkC;QACnE,IAAI,UAAU,GAAG,EAAE,CAAC;QAEpB,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACpC,OAAO,UAAU,CAAC;SACrB;QAED,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;gBACpB,SAAS;aACZ;YAED,MAAM,eAAe,GAAG,KAAK,CAAC,cAAe,CAAC;YAC9C,MAAM,cAAc,GAAG,eAAe,CAAC,UAAU,CAAC;YAElD,UAAU,IAAI,cAAc,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;YAC1E,UAAU,IAAI,GAAG,cAAc,CAAC,iBAAiB,IAAI,cAAc,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAC5J,KAAK,CAAC,IAAI,CACb,MAAM,CAAC;SACX;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,WAAqB,EAAE,aAAkC;QACtE,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzB,kBAAkB;QAClB,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QACjE,IAAI,CAAC,iBAAiB,GAAG,kBAAkB,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEzF,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE;YACpD,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,GAAG;gBACC,KAAK,EAAE,CAAC;gBACR,IAAI,CAAC,iBAAiB,GAAG,kBAAkB,GAAG,KAAK,CAAC;aACvD,QAAQ,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE;SAChE;QAED,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEzC,cAAc;QACd,IAAI,UAAU,GAAG,QAAQ,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC;QACjD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,UAAU,IAAI,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC;SACzC;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,IAAI,SAAS,KAAK,oBAAoB,EAAE;YACpC,MAAM,KAAK,GAAG,IAAqC,CAAC;YACpD,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;YAC7B,UAAU,IAAI,OAAO,IAAI,CAAC,iBAAiB,sCAAsC,IAAI,CAAC,IAAI,MAAM,SAAS,MAAM,CAAC;SACnH;aAAM;YACH,UAAU,IAAI,OAAO,IAAI,CAAC,iBAAiB,kBAAkB,SAAS,KAAK,IAAI,CAAC,IAAI,OAAO,CAAC;SAC/F;QAED,aAAa;QACb,UAAU,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEzC,SAAS;QACT,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;gBACpB,SAAS;aACZ;YAED,MAAM,eAAe,GAAG,KAAK,CAAC,cAAe,CAAC;YAC9C,MAAM,cAAc,GAAG,eAAe,CAAC,UAAU,CAAC;YAElD,IAAI,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC9C,UAAU,IAAI,cAAc,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;aACtE;SACJ;QAED,UAAU;QACV,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACtB,SAAS;aACZ;YAED,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE;gBACrC,MAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC;gBAC3C,IAAI,cAAc,IAAI,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE;oBAChE,UAAU,IAAI,cAAc,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;iBACtE;aACJ;SACJ;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE7C,MAAM,SAAS,GAAG,QAAQ,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAC3D,IAAI,SAAS,EAAE;YACX,MAAM,KAAK,GAAsB,IAAI,SAAS,EAAE,CAAC;YACjD,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;YAExC,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC7B,KAAK,CAAC,OAAO,EAAE,CAAC;SACnB;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,MAAM,CAAC,OAAO,EAAE,CAAC;SACpB;QAED,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;IACnC,CAAC;CACJ;AApeU;IADN,SAAS,CAAC,SAAS,CAAC;mDACG", "sourcesContent": ["import { GetClass } from \"../../Misc/typeStore\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport { UniqueIdGenerator } from \"../../Misc/uniqueIdGenerator\";\r\nimport type { NodeGeometryBlockConnectionPointTypes } from \"./Enums/nodeGeometryConnectionPointTypes\";\r\nimport { NodeGeometryConnectionPoint, NodeGeometryConnectionPointDirection } from \"./nodeGeometryBlockConnectionPoint\";\r\nimport type { NodeGeometryBuildState } from \"./nodeGeometryBuildState\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { PrecisionDate } from \"../../Misc/precisionDate\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { GeometryInputBlock } from \"./Blocks/geometryInputBlock\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * Defines a block that can be used inside a node based geometry\r\n */\r\nexport class NodeGeometryBlock {\r\n    private _name = \"\";\r\n    private _buildId: number;\r\n    protected _isInput = false;\r\n    protected _isTeleportOut = false;\r\n    protected _isTeleportIn = false;\r\n    protected _isDebug = false;\r\n    protected _isUnique = false;\r\n    private _buildExecutionTime: number = 0;\r\n\r\n    /**\r\n     * Gets an observable raised when the block is built\r\n     */\r\n    public onBuildObservable = new Observable<NodeGeometryBlock>();\r\n\r\n    /** @internal */\r\n    public _inputs = new Array<NodeGeometryConnectionPoint>();\r\n    /** @internal */\r\n    public _outputs = new Array<NodeGeometryConnectionPoint>();\r\n    /** @internal */\r\n    public _preparationId: number;\r\n    /** @internal */\r\n    public _codeVariableName = \"\";\r\n\r\n    /**\r\n     * Gets the time spent to build this block (in ms)\r\n     */\r\n    public get buildExecutionTime() {\r\n        return this._buildExecutionTime;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of input points\r\n     */\r\n    public get inputs(): NodeGeometryConnectionPoint[] {\r\n        return this._inputs;\r\n    }\r\n\r\n    /** Gets the list of output points */\r\n    public get outputs(): NodeGeometryConnectionPoint[] {\r\n        return this._outputs;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the unique id of the node\r\n     */\r\n    public uniqueId: number;\r\n\r\n    /**\r\n     * Gets or set the name of the block\r\n     */\r\n    public get name(): string {\r\n        return this._name;\r\n    }\r\n\r\n    public set name(value: string) {\r\n        this._name = value;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if this block is an input\r\n     */\r\n    public get isInput(): boolean {\r\n        return this._isInput;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if this block is a teleport out\r\n     */\r\n    public get isTeleportOut(): boolean {\r\n        return this._isTeleportOut;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if this block is a teleport in\r\n     */\r\n    public get isTeleportIn(): boolean {\r\n        return this._isTeleportIn;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if this block is a debug block\r\n     */\r\n    public get isDebug(): boolean {\r\n        return this._isDebug;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that this block can only be used once per NodeGeometry\r\n     */\r\n    public get isUnique() {\r\n        return this._isUnique;\r\n    }\r\n\r\n    /**\r\n     * A free comment about the block\r\n     */\r\n    @serialize(\"comment\")\r\n    public comments: string;\r\n\r\n    /** Gets or sets a boolean indicating that this input can be edited from a collapsed frame */\r\n    public visibleOnFrame = false;\r\n\r\n    /**\r\n     * Gets the current class name e.g. \"NodeGeometryBlock\"\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"NodeGeometryBlock\";\r\n    }\r\n\r\n    protected _inputRename(name: string) {\r\n        return name;\r\n    }\r\n\r\n    protected _outputRename(name: string) {\r\n        return name;\r\n    }\r\n\r\n    /**\r\n     * Checks if the current block is an ancestor of a given block\r\n     * @param block defines the potential descendant block to check\r\n     * @returns true if block is a descendant\r\n     */\r\n    public isAnAncestorOf(block: NodeGeometryBlock): boolean {\r\n        for (const output of this._outputs) {\r\n            if (!output.hasEndpoints) {\r\n                continue;\r\n            }\r\n\r\n            for (const endpoint of output.endpoints) {\r\n                if (endpoint.ownerBlock === block) {\r\n                    return true;\r\n                }\r\n                if (endpoint.ownerBlock.isAnAncestorOf(block)) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Checks if the current block is an ancestor of a given type\r\n     * @param type defines the potential type to check\r\n     * @returns true if block is a descendant\r\n     */\r\n    public isAnAncestorOfType(type: string): boolean {\r\n        if (this.getClassName() === type) {\r\n            return true;\r\n        }\r\n\r\n        for (const output of this._outputs) {\r\n            if (!output.hasEndpoints) {\r\n                continue;\r\n            }\r\n\r\n            for (const endpoint of output.endpoints) {\r\n                if (endpoint.ownerBlock.isAnAncestorOfType(type)) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Get the first descendant using a predicate\r\n     * @param predicate defines the predicate to check\r\n     * @returns descendant or null if none found\r\n     */\r\n    public getDescendantOfPredicate(predicate: (block: NodeGeometryBlock) => boolean): Nullable<NodeGeometryBlock> {\r\n        if (predicate(this)) {\r\n            return this;\r\n        }\r\n\r\n        for (const output of this._outputs) {\r\n            if (!output.hasEndpoints) {\r\n                continue;\r\n            }\r\n\r\n            for (const endpoint of output.endpoints) {\r\n                const descendant = endpoint.ownerBlock.getDescendantOfPredicate(predicate);\r\n\r\n                if (descendant) {\r\n                    return descendant;\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Creates a new NodeGeometryBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        this._name = name;\r\n        this.uniqueId = UniqueIdGenerator.UniqueId;\r\n    }\r\n\r\n    /**\r\n     * Register a new input. Must be called inside a block constructor\r\n     * @param name defines the connection point name\r\n     * @param type defines the connection point type\r\n     * @param isOptional defines a boolean indicating that this input can be omitted\r\n     * @param value value to return if there is no connection\r\n     * @param valueMin min value accepted for value\r\n     * @param valueMax max value accepted for value\r\n     * @returns the current block\r\n     */\r\n    public registerInput(name: string, type: NodeGeometryBlockConnectionPointTypes, isOptional: boolean = false, value?: any, valueMin?: any, valueMax?: any) {\r\n        const point = new NodeGeometryConnectionPoint(name, this, NodeGeometryConnectionPointDirection.Input);\r\n        point.type = type;\r\n        point.isOptional = isOptional;\r\n        point.defaultValue = value;\r\n        point.value = value;\r\n        point.valueMin = valueMin;\r\n        point.valueMax = valueMax;\r\n\r\n        this._inputs.push(point);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Register a new output. Must be called inside a block constructor\r\n     * @param name defines the connection point name\r\n     * @param type defines the connection point type\r\n     * @param point an already created connection point. If not provided, create a new one\r\n     * @returns the current block\r\n     */\r\n    public registerOutput(name: string, type: NodeGeometryBlockConnectionPointTypes, point?: NodeGeometryConnectionPoint) {\r\n        point = point ?? new NodeGeometryConnectionPoint(name, this, NodeGeometryConnectionPointDirection.Output);\r\n        point.type = type;\r\n\r\n        this._outputs.push(point);\r\n\r\n        return this;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _buildBlock(state: NodeGeometryBuildState) {\r\n        // Empty. Must be defined by child nodes\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _customBuildStep(state: NodeGeometryBuildState): void {\r\n        // Must be implemented by children\r\n    }\r\n\r\n    /**\r\n     * Build the current node and generate the vertex data\r\n     * @param state defines the current generation state\r\n     * @returns true if already built\r\n     */\r\n    public build(state: NodeGeometryBuildState): boolean {\r\n        if (this._buildId === state.buildId) {\r\n            return true;\r\n        }\r\n\r\n        if (this._outputs.length > 0) {\r\n            if (!this._outputs.some((o) => o.hasEndpoints) && !this.isDebug) {\r\n                return false;\r\n            }\r\n\r\n            this.outputs.forEach((o) => o._resetCounters());\r\n        }\r\n\r\n        this._buildId = state.buildId;\r\n\r\n        // Check if \"parent\" blocks are compiled\r\n        for (const input of this._inputs) {\r\n            if (!input.connectedPoint) {\r\n                if (!input.isOptional) {\r\n                    // Emit a warning\r\n                    state.notConnectedNonOptionalInputs.push(input);\r\n                }\r\n                continue;\r\n            }\r\n\r\n            const block = input.connectedPoint.ownerBlock;\r\n            if (block && block !== this) {\r\n                block.build(state);\r\n            }\r\n        }\r\n\r\n        this._customBuildStep(state);\r\n\r\n        // Logs\r\n        if (state.verbose) {\r\n            Logger.Log(`Building ${this.name} [${this.getClassName()}]`);\r\n        }\r\n\r\n        const now = PrecisionDate.Now;\r\n        this._buildBlock(state);\r\n        this._buildExecutionTime = PrecisionDate.Now - now;\r\n\r\n        // Compile connected blocks\r\n        for (const output of this._outputs) {\r\n            for (const endpoint of output.endpoints) {\r\n                const block = endpoint.ownerBlock;\r\n\r\n                if (block) {\r\n                    block.build(state);\r\n                }\r\n            }\r\n        }\r\n\r\n        this.onBuildObservable.notifyObservers(this);\r\n\r\n        return false;\r\n    }\r\n\r\n    protected _linkConnectionTypes(inputIndex0: number, inputIndex1: number, looseCoupling = false) {\r\n        if (looseCoupling) {\r\n            this._inputs[inputIndex1]._acceptedConnectionPointType = this._inputs[inputIndex0];\r\n        } else {\r\n            this._inputs[inputIndex0]._linkedConnectionSource = this._inputs[inputIndex1];\r\n        }\r\n        this._inputs[inputIndex1]._linkedConnectionSource = this._inputs[inputIndex0];\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     */\r\n    public initialize() {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Lets the block try to connect some inputs automatically\r\n     */\r\n    public autoConfigure() {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Find an input by its name\r\n     * @param name defines the name of the input to look for\r\n     * @returns the input or null if not found\r\n     */\r\n    public getInputByName(name: string) {\r\n        const filter = this._inputs.filter((e) => e.name === name);\r\n\r\n        if (filter.length) {\r\n            return filter[0];\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Find an output by its name\r\n     * @param name defines the name of the output to look for\r\n     * @returns the output or null if not found\r\n     */\r\n    public getOutputByName(name: string) {\r\n        const filter = this._outputs.filter((e) => e.name === name);\r\n\r\n        if (filter.length) {\r\n            return filter[0];\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Serializes this block in a JSON representation\r\n     * @returns the serialized block object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n        serializationObject.customType = \"BABYLON.\" + this.getClassName();\r\n        serializationObject.id = this.uniqueId;\r\n        serializationObject.name = this.name;\r\n\r\n        serializationObject.inputs = [];\r\n        serializationObject.outputs = [];\r\n\r\n        for (const input of this.inputs) {\r\n            serializationObject.inputs.push(input.serialize());\r\n        }\r\n\r\n        for (const output of this.outputs) {\r\n            serializationObject.outputs.push(output.serialize(false));\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _deserialize(serializationObject: any) {\r\n        this._name = serializationObject.name;\r\n        this.comments = serializationObject.comments;\r\n        this.visibleOnFrame = !!serializationObject.visibleOnFrame;\r\n        this._deserializePortDisplayNamesAndExposedOnFrame(serializationObject);\r\n    }\r\n\r\n    private _deserializePortDisplayNamesAndExposedOnFrame(serializationObject: any) {\r\n        const serializedInputs = serializationObject.inputs;\r\n        const serializedOutputs = serializationObject.outputs;\r\n        if (serializedInputs) {\r\n            serializedInputs.forEach((port: any) => {\r\n                const input = this.inputs.find((i) => i.name === port.name);\r\n\r\n                if (!input) {\r\n                    return;\r\n                }\r\n\r\n                if (port.displayName) {\r\n                    input.displayName = port.displayName;\r\n                }\r\n                if (port.isExposedOnFrame) {\r\n                    input.isExposedOnFrame = port.isExposedOnFrame;\r\n                    input.exposedPortPosition = port.exposedPortPosition;\r\n                }\r\n                if (port.value !== undefined && port.value !== null) {\r\n                    if (port.valueType === \"number\") {\r\n                        input.value = port.value;\r\n                    } else {\r\n                        const valueType = GetClass(port.valueType);\r\n\r\n                        if (valueType) {\r\n                            input.value = valueType.FromArray(port.value);\r\n                        }\r\n                    }\r\n                }\r\n            });\r\n        }\r\n        if (serializedOutputs) {\r\n            serializedOutputs.forEach((port: any, i: number) => {\r\n                if (port.displayName) {\r\n                    this.outputs[i].displayName = port.displayName;\r\n                }\r\n                if (port.isExposedOnFrame) {\r\n                    this.outputs[i].isExposedOnFrame = port.isExposedOnFrame;\r\n                    this.outputs[i].exposedPortPosition = port.exposedPortPosition;\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        const variableName = this._codeVariableName;\r\n        return `${variableName}.visibleOnFrame = ${this.visibleOnFrame};\\n`;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _dumpCodeForOutputConnections(alreadyDumped: NodeGeometryBlock[]) {\r\n        let codeString = \"\";\r\n\r\n        if (alreadyDumped.indexOf(this) !== -1) {\r\n            return codeString;\r\n        }\r\n\r\n        alreadyDumped.push(this);\r\n\r\n        for (const input of this.inputs) {\r\n            if (!input.isConnected) {\r\n                continue;\r\n            }\r\n\r\n            const connectedOutput = input.connectedPoint!;\r\n            const connectedBlock = connectedOutput.ownerBlock;\r\n\r\n            codeString += connectedBlock._dumpCodeForOutputConnections(alreadyDumped);\r\n            codeString += `${connectedBlock._codeVariableName}.${connectedBlock._outputRename(connectedOutput.name)}.connectTo(${this._codeVariableName}.${this._inputRename(\r\n                input.name\r\n            )});\\n`;\r\n        }\r\n\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _dumpCode(uniqueNames: string[], alreadyDumped: NodeGeometryBlock[]) {\r\n        alreadyDumped.push(this);\r\n\r\n        // Get unique name\r\n        const nameAsVariableName = this.name.replace(/[^A-Za-z_]+/g, \"\");\r\n        this._codeVariableName = nameAsVariableName || `${this.getClassName()}_${this.uniqueId}`;\r\n\r\n        if (uniqueNames.indexOf(this._codeVariableName) !== -1) {\r\n            let index = 0;\r\n            do {\r\n                index++;\r\n                this._codeVariableName = nameAsVariableName + index;\r\n            } while (uniqueNames.indexOf(this._codeVariableName) !== -1);\r\n        }\r\n\r\n        uniqueNames.push(this._codeVariableName);\r\n\r\n        // Declaration\r\n        let codeString = `\\n// ${this.getClassName()}\\n`;\r\n        if (this.comments) {\r\n            codeString += `// ${this.comments}\\n`;\r\n        }\r\n        const className = this.getClassName();\r\n        if (className === \"GeometryInputBlock\") {\r\n            const block = this as unknown as GeometryInputBlock;\r\n            const blockType = block.type;\r\n            codeString += `var ${this._codeVariableName} = new BABYLON.GeometryInputBlock(\"${this.name}\", ${blockType});\\n`;\r\n        } else {\r\n            codeString += `var ${this._codeVariableName} = new BABYLON.${className}(\"${this.name}\");\\n`;\r\n        }\r\n\r\n        // Properties\r\n        codeString += this._dumpPropertiesCode();\r\n\r\n        // Inputs\r\n        for (const input of this.inputs) {\r\n            if (!input.isConnected) {\r\n                continue;\r\n            }\r\n\r\n            const connectedOutput = input.connectedPoint!;\r\n            const connectedBlock = connectedOutput.ownerBlock;\r\n\r\n            if (alreadyDumped.indexOf(connectedBlock) === -1) {\r\n                codeString += connectedBlock._dumpCode(uniqueNames, alreadyDumped);\r\n            }\r\n        }\r\n\r\n        // Outputs\r\n        for (const output of this.outputs) {\r\n            if (!output.hasEndpoints) {\r\n                continue;\r\n            }\r\n\r\n            for (const endpoint of output.endpoints) {\r\n                const connectedBlock = endpoint.ownerBlock;\r\n                if (connectedBlock && alreadyDumped.indexOf(connectedBlock) === -1) {\r\n                    codeString += connectedBlock._dumpCode(uniqueNames, alreadyDumped);\r\n                }\r\n            }\r\n        }\r\n\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Clone the current block to a new identical block\r\n     * @returns a copy of the current block\r\n     */\r\n    public clone() {\r\n        const serializationObject = this.serialize();\r\n\r\n        const blockType = GetClass(serializationObject.customType);\r\n        if (blockType) {\r\n            const block: NodeGeometryBlock = new blockType();\r\n            block._deserialize(serializationObject);\r\n\r\n            return block;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Release resources\r\n     */\r\n    public dispose() {\r\n        for (const input of this.inputs) {\r\n            input.dispose();\r\n        }\r\n\r\n        for (const output of this.outputs) {\r\n            output.dispose();\r\n        }\r\n\r\n        this.onBuildObservable.clear();\r\n    }\r\n}\r\n"]}