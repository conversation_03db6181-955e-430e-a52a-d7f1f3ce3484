{"version": 3, "file": "webgpuPerfCounter.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuPerfCounter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAA9B;QACY,sBAAiB,GAAG,CAAC,CAAC,CAAC;QAE/B;;WAEG;QACI,YAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAkBvC,CAAC;IAhBG;;OAEG;IACI,YAAY,CAAC,cAAsB,EAAE,QAAgB;QACxD,IAAI,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE;YACzC,OAAO;SACV;QACD,IAAI,IAAI,CAAC,iBAAiB,KAAK,cAAc,EAAE;YAC3C,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAC5B,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YACvC,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC;SAC3C;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SAC1C;IACL,CAAC;CACJ", "sourcesContent": ["import { PerfCounter } from \"../../Misc/perfCounter\";\r\n\r\n/**\r\n * Class used to define a WebGPU performance counter\r\n */\r\nexport class WebGPUPerfCounter {\r\n    private _gpuTimeInFrameId = -1;\r\n\r\n    /**\r\n     * The GPU time in nanoseconds spent in the last frame\r\n     */\r\n    public counter = new PerfCounter();\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _addDuration(currentFrameId: number, duration: number) {\r\n        if (currentFrameId < this._gpuTimeInFrameId) {\r\n            return;\r\n        }\r\n        if (this._gpuTimeInFrameId !== currentFrameId) {\r\n            this.counter._fetchResult();\r\n            this.counter.fetchNewFrame();\r\n            this.counter.addCount(duration, false);\r\n            this._gpuTimeInFrameId = currentFrameId;\r\n        } else {\r\n            this.counter.addCount(duration, false);\r\n        }\r\n    }\r\n}\r\n"]}