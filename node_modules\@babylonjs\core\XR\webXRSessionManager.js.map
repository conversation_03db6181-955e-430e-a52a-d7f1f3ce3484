{"version": 3, "file": "webXRSessionManager.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/XR/webXRSessionManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAKhD,OAAO,EAAE,wBAAwB,EAAE,+BAA+B,EAAE,MAAM,4BAA4B,CAAC;AAKvG,OAAO,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,MAAM,+BAA+B,CAAC;AAC3F,OAAO,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAC;AAG3D;;;GAGG;AACH,MAAM,OAAO,mBAAmB;IA+E5B;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAa;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAC1C,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,mCAAmC,CAAC,eAAe,CAAC;YACrD,mBAAmB,EAAE,QAAQ;YAC7B,cAAc,EAAE,KAAK;SACxB,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACH;IACI,wDAAwD;IACjD,KAAY;QAAZ,UAAK,GAAL,KAAK,CAAO;QAnFvB,0CAA0C;QACnC,qBAAgB,GAAW,CAAC,CAAC,CAAC;QACrC;;;WAGG;QACI,8BAAyB,GAAG,GAAG,CAAC;QACvC;;WAEG;QACI,wBAAmB,GAAwB,IAAI,UAAU,EAAW,CAAC;QAC5E;;WAEG;QACI,8BAAyB,GAAiC,IAAI,UAAU,EAAE,CAAC;QAClF;;WAEG;QACI,qBAAgB,GAAoB,IAAI,UAAU,EAAO,CAAC;QACjE;;WAEG;QACI,oBAAe,GAA0B,IAAI,UAAU,EAAa,CAAC;QAE5E;;WAEG;QACI,kCAA6B,GAAiC,IAAI,UAAU,EAAoB,CAAC;QAExG;;WAEG;QACI,cAAS,GAAoC,IAAI,UAAU,EAAuB,CAAC;QAU1F;;WAEG;QACI,kBAAa,GAAY,KAAK,CAAC;QACtC;;WAEG;QACI,gBAAW,GAAY,KAAK,CAAC;QAE5B,wBAAmB,GAAW,CAAC,CAAC;QAExC;;WAEG;QACI,wCAAmC,GAGrC,IAAI,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QA0BjC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;YAC3E,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACxB,CAAC,CAAC,CAAC;QACH,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;YACnC,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,cAAc,CAAC,iBAAmC;QACzD,IAAI,CAAC,eAAe,GAAG,iBAAiB,CAAC;QACzC,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,8CAA8C;QAC9C,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;QACD,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,mCAAmC,CAAC,KAAK,EAAE,CAAC;QACjD,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACzE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,WAAW;QACpB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,WAAW,EAAE;YAClC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAI;gBACA,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;aACnC;YAAC,MAAM;gBACJ,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;aAC5C;SACJ;QACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;;OAMG;IACI,qBAAqB,CAAC,QAAkB,EAAE,IAAY;QACzD,OAAO,IAAI,CAAC,qBAAqB,EAAE,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,KAAK,CAAC;IACtF,CAAC;IAED;;;;OAIG;IACI,4BAA4B,CAAC,GAAU;QAC1C,OAAO,IAAI,CAAC,qBAAqB,EAAE,4BAA4B,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IACjF,CAAC;IAED;;;;OAIG;IACI,6BAA6B,CAAC,IAAY;QAC7C,OAAO,IAAI,CAAC,qBAAqB,EAAE,6BAA6B,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IACnF,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,OAAyC;QACjE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QACtC,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,EAAE;YAC7B,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;SACzC;aAAM;YACH,OAAO,GAAG,OAAO,IAAI,+BAA+B,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACzE,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,MAAM,CAAC,kBAAkB,EAAE,IAAI,SAAS,CAAC;YAC1F,OAAO,IAAI,wBAAwB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SACtD;IACL,CAAC;IAED;;;;OAIG;IACI,eAAe;QAClB,sCAAsC;QACtC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE;YACvB,OAAO,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;SAChD;QACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACI,sBAAsB,CAAC,gBAA+B,cAAc,EAAE,gBAA+B,EAAE;QAC1G,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,cAAc,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,OAAkB,EAAE,EAAE;YACjG,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC;YAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAE9C,6HAA6H;YAC7H,IAAI,CAAC,OAAO,CAAC,gBAAgB,CACzB,KAAK,EACL,GAAG,EAAE;gBACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBAEzB,yBAAyB;gBACzB,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAE5C,IAAI,IAAI,CAAC,OAAO,EAAE;oBACd,0CAA0C;oBAC1C,IAAI,CAAC,OAAO,CAAC,2BAA2B,GAAG,IAAI,CAAC;oBAEhD,0EAA0E;oBAC1E,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC;oBAEzC,oHAAoH;oBACpH,IAAI,CAAC,OAAO,CAAC,6BAA6B,GAAG,IAAI,CAAC;oBAClD,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;iBAC9B;gBAED,kCAAkC;gBAClC,8EAA8E;gBAC9E,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACf,IAAI,CAAC,qBAAqB,EAAE,OAAO,EAAE,CAAC;iBACzC;gBACD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;gBAClC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAClC,CAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACjB,CAAC;YAEF,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,WAA0B;QACrD,OAAO,mBAAmB,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACpC,OAAO;SACV;QAED,8GAA8G;QAC9G,IAAI,CAAC,OAAO,CAAC,6BAA6B,GAAG;YACzC,qBAAqB,EAAE,CAAC,QAA8B,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,QAAQ,CAAC;YACvG,cAAc,EAAE,CAAC,SAAiB,EAAE,OAA0B,EAAE,EAAE;gBAC9D,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;oBACpC,OAAO;iBACV;gBACD,0DAA0D;gBAC1D,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;gBAC5B,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;gBAClC,IAAI,OAAO,EAAE;oBACT,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;oBAC1B,MAAM,2BAA2B,GAAG,IAAI,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,IAAI,IAAI,CAAC;oBACnG,yDAAyD;oBACzD,IAAI,IAAI,CAAC,OAAO,CAAC,2BAA2B,KAAK,2BAA2B,EAAE;wBAC1E,IAAI,CAAC,OAAO,CAAC,2BAA2B,GAAG,2BAA2B,CAAC;qBAC1E;oBACD,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;oBAClD,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;oBAC3B,IAAI,CAAC,OAAO,CAAC,2BAA2B,GAAG,IAAI,CAAC;oBAChD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;iBAC9B;YACL,CAAC;SACJ,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,2BAA2B,GAAG,IAAI,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,IAAI,IAAI,CAAC;QAC1G,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;YAClC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,qEAAqE;QACrE,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,oBAAoB,EAAE;YAC9D,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SAC3D;QACD,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACI,0BAA0B,CAAC,qBAA2C,aAAa;QACtF,OAAO,IAAI,CAAC,OAAO;aACd,qBAAqB,CAAC,kBAAkB,CAAC;aACzC,IAAI,CACD,CAAC,cAAc,EAAE,EAAE;YACf,OAAO,cAAkC,CAAC;QAC9C,CAAC,EACD,CAAC,eAAe,EAAE,EAAE;YAChB,MAAM,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAC3E,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YAC9B,MAAM,CAAC,GAAG,CAAC,oEAAoE,CAAC,CAAC;YAEjF,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,IAAI,CACpD,CAAC,cAAc,EAAE,EAAE;gBACf,MAAM,kBAAkB,GAAG,IAAI,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACpG,OAAQ,cAAmC,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;YAC5F,CAAC,EACD,CAAC,eAAe,EAAE,EAAE;gBAChB,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;gBAC9B,4CAA4C;gBAC5C,MAAM,iFAAiF,CAAC;YAC5F,CAAC,CACJ,CAAC;QACN,CAAC,CACJ;aACA,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE;YACrB,yEAAyE;YACzE,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,oBAAoB,EAAE,EAAE;gBAC9E,IAAI,CAAC,oBAAoB,GAAG,oBAAwC,CAAC;gBACrE,OAAO,cAAc,CAAC;YAC1B,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE;YACrB,sDAAsD;YACtD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC;YAC/D,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B,CAAC,CAAC,CAAC;IACX,CAAC;IAED;;;;;;OAMG;IACI,sBAAsB,CAAC,KAAoB;QAC9C,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,gBAA6C;QACrE,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,qBAAqB,EAAE,OAAO,EAAE,CAAC;SACzC;QACD,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB,EAAE,iCAAiC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IACzG,CAAC;IAED;;OAEG;IACI,oBAAoB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,KAAwB;QAC7C,IAAI,KAAK,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,oBAAoB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;SACtI;QAED,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,uBAAuB,CAAC,WAA0B;QAC5D,IAAI,CAAE,SAAiB,CAAC,EAAE,EAAE;YACxB,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACjC;QACD,oDAAoD;QACpD,MAAM,aAAa,GAAI,SAAiB,CAAC,EAAE,CAAC,kBAAkB,IAAK,SAAiB,CAAC,EAAE,CAAC,eAAe,CAAC;QACxG,IAAI,CAAC,aAAa,EAAE;YAChB,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACjC;aAAM;YACH,OAAO,aAAa;iBACf,IAAI,CAAE,SAAiB,CAAC,EAAE,EAAE,WAAW,CAAC;iBACxC,IAAI,CAAC,CAAC,MAAe,EAAE,EAAE;gBACtB,MAAM,WAAW,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;gBAClE,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACxC,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;gBACd,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACf,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;SACV;IACL,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,IAAI,KAAK,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,IAAY;QACrC,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,QAAoB,EAAE,oBAAoB,GAAG,IAAI;QACjE,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,QAAQ,EAAE,CAAC;SACd;aAAM,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,oBAAoB,EAAE;YAClD,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC9C;IACL,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,iBAAiB,EAAE,yBAAyB,IAAI,KAAK,CAAC;IACtE,CAAC;IAED;;;OAGG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,iBAAiB,EAAE,cAAc,IAAI,IAAI,CAAC;IAC1D,CAAC;IAED;;;OAGG;IACH,IAAW,cAAc,CAAC,KAAuB;QAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,cAAc,GAAG,GAAG,CAAC;SAC/C;IACL,CAAC;IAED;;;;OAIG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,OAAO,EAAE,eAAe,IAAI,IAAI,CAAC;IACjD,CAAC;CACJ", "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { IDisposable, Scene } from \"../scene\";\r\nimport type { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport type { WebXRRenderTarget } from \"./webXRTypes\";\r\nimport { WebXRManagedOutputCanvas, WebXRManagedOutputCanvasOptions } from \"./webXRManagedOutputCanvas\";\r\nimport type { Engine } from \"../Engines/engine\";\r\nimport type { IWebXRRenderTargetTextureProvider, WebXRLayerRenderTargetTextureProvider } from \"./webXRRenderTargetTextureProvider\";\r\nimport type { Viewport } from \"../Maths/math.viewport\";\r\nimport type { WebXRLayerWrapper } from \"./webXRLayerWrapper\";\r\nimport { NativeXRLayerWrapper, NativeXRRenderTarget } from \"./native/nativeXRRenderTarget\";\r\nimport { WebXRWebGLLayerWrapper } from \"./webXRWebGLLayer\";\r\nimport type { ThinEngine } from \"../Engines/thinEngine\";\r\n\r\n/**\r\n * Manages an XRSession to work with Babylon's engine\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/webXR/webXRSessionManagers\r\n */\r\nexport class WebXRSessionManager implements IDisposable, IWebXRRenderTargetTextureProvider {\r\n    private _engine: Nullable<Engine>;\r\n    private _referenceSpace: XRReferenceSpace;\r\n    private _baseLayerWrapper: Nullable<WebXRLayerWrapper>;\r\n    private _baseLayerRTTProvider: Nullable<WebXRLayerRenderTargetTextureProvider>;\r\n    private _xrNavigator: any;\r\n    private _sessionMode: XRSessionMode;\r\n    private _onEngineDisposedObserver: Nullable<Observer<ThinEngine>>;\r\n\r\n    /**\r\n     * The base reference space from which the session started. good if you want to reset your\r\n     * reference space\r\n     */\r\n    public baseReferenceSpace: XRReferenceSpace;\r\n    /**\r\n     * Current XR frame\r\n     */\r\n    public currentFrame: Nullable<XRFrame>;\r\n    /** WebXR timestamp updated every frame */\r\n    public currentTimestamp: number = -1;\r\n    /**\r\n     * Used just in case of a failure to initialize an immersive session.\r\n     * The viewer reference space is compensated using this height, creating a kind of \"viewer-floor\" reference space\r\n     */\r\n    public defaultHeightCompensation = 1.7;\r\n    /**\r\n     * Fires every time a new xrFrame arrives which can be used to update the camera\r\n     */\r\n    public onXRFrameObservable: Observable<XRFrame> = new Observable<XRFrame>();\r\n    /**\r\n     * Fires when the reference space changed\r\n     */\r\n    public onXRReferenceSpaceChanged: Observable<XRReferenceSpace> = new Observable();\r\n    /**\r\n     * Fires when the xr session is ended either by the device or manually done\r\n     */\r\n    public onXRSessionEnded: Observable<any> = new Observable<any>();\r\n    /**\r\n     * Fires when the xr session is initialized: right after requestSession was called and returned with a successful result\r\n     */\r\n    public onXRSessionInit: Observable<XRSession> = new Observable<XRSession>();\r\n\r\n    /**\r\n     * Fires when the xr reference space has been initialized\r\n     */\r\n    public onXRReferenceSpaceInitialized: Observable<XRReferenceSpace> = new Observable<XRReferenceSpace>();\r\n\r\n    /**\r\n     * Fires when the session manager is rendering the first frame\r\n     */\r\n    public onXRReady: Observable<WebXRSessionManager> = new Observable<WebXRSessionManager>();\r\n    /**\r\n     * Underlying xr session\r\n     */\r\n    public session: XRSession;\r\n    /**\r\n     * The viewer (head position) reference space. This can be used to get the XR world coordinates\r\n     * or get the offset the player is currently at.\r\n     */\r\n    public viewerReferenceSpace: XRReferenceSpace;\r\n    /**\r\n     * Are we currently in the XR loop?\r\n     */\r\n    public inXRFrameLoop: boolean = false;\r\n    /**\r\n     * Are we in an XR session?\r\n     */\r\n    public inXRSession: boolean = false;\r\n\r\n    private _worldScalingFactor: number = 1;\r\n\r\n    /**\r\n     * Observable raised when the world scale has changed\r\n     */\r\n    public onWorldScaleFactorChangedObservable: Observable<{\r\n        previousScaleFactor: number;\r\n        newScaleFactor: number;\r\n    }> = new Observable(undefined, true);\r\n\r\n    /**\r\n     * Scale factor to apply to all XR-related elements (camera, controllers)\r\n     */\r\n    public get worldScalingFactor(): number {\r\n        return this._worldScalingFactor;\r\n    }\r\n\r\n    public set worldScalingFactor(value: number) {\r\n        const oldValue = this._worldScalingFactor;\r\n        this._worldScalingFactor = value;\r\n        this.onWorldScaleFactorChangedObservable.notifyObservers({\r\n            previousScaleFactor: oldValue,\r\n            newScaleFactor: value,\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Constructs a WebXRSessionManager, this must be initialized within a user action before usage\r\n     * @param scene The scene which the session should be created for\r\n     */\r\n    constructor(\r\n        /** The scene which the session should be created for */\r\n        public scene: Scene\r\n    ) {\r\n        this._engine = scene.getEngine();\r\n        this._onEngineDisposedObserver = this._engine.onDisposeObservable.addOnce(() => {\r\n            this._engine = null;\r\n        });\r\n        scene.onDisposeObservable.addOnce(() => {\r\n            this.dispose();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * The current reference space used in this session. This reference space can constantly change!\r\n     * It is mainly used to offset the camera's position.\r\n     */\r\n    public get referenceSpace(): XRReferenceSpace {\r\n        return this._referenceSpace;\r\n    }\r\n\r\n    /**\r\n     * Set a new reference space and triggers the observable\r\n     */\r\n    public set referenceSpace(newReferenceSpace: XRReferenceSpace) {\r\n        this._referenceSpace = newReferenceSpace;\r\n        this.onXRReferenceSpaceChanged.notifyObservers(this._referenceSpace);\r\n    }\r\n\r\n    /**\r\n     * The mode for the managed XR session\r\n     */\r\n    public get sessionMode(): XRSessionMode {\r\n        return this._sessionMode;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the session manager\r\n     * This should be called explicitly by the dev, if required.\r\n     */\r\n    public dispose() {\r\n        // disposing without leaving XR? Exit XR first\r\n        if (this.inXRSession) {\r\n            this.exitXRAsync();\r\n        }\r\n        this.onXRFrameObservable.clear();\r\n        this.onXRSessionEnded.clear();\r\n        this.onXRReferenceSpaceChanged.clear();\r\n        this.onXRSessionInit.clear();\r\n        this.onWorldScaleFactorChangedObservable.clear();\r\n        this._engine?.onDisposeObservable.remove(this._onEngineDisposedObserver);\r\n        this._engine = null;\r\n    }\r\n\r\n    /**\r\n     * Stops the xrSession and restores the render loop\r\n     * @returns Promise which resolves after it exits XR\r\n     */\r\n    public async exitXRAsync() {\r\n        if (this.session && this.inXRSession) {\r\n            this.inXRSession = false;\r\n            try {\r\n                return await this.session.end();\r\n            } catch {\r\n                Logger.Warn(\"Could not end XR session.\");\r\n            }\r\n        }\r\n        return Promise.resolve();\r\n    }\r\n\r\n    /**\r\n     * Attempts to set the framebuffer-size-normalized viewport to be rendered this frame for this view.\r\n     * In the event of a failure, the supplied viewport is not updated.\r\n     * @param viewport the viewport to which the view will be rendered\r\n     * @param view the view for which to set the viewport\r\n     * @returns whether the operation was successful\r\n     */\r\n    public trySetViewportForView(viewport: Viewport, view: XRView): boolean {\r\n        return this._baseLayerRTTProvider?.trySetViewportForView(viewport, view) || false;\r\n    }\r\n\r\n    /**\r\n     * Gets the correct render target texture to be rendered this frame for this eye\r\n     * @param eye the eye for which to get the render target\r\n     * @returns the render target for the specified eye or null if not available\r\n     */\r\n    public getRenderTargetTextureForEye(eye: XREye): Nullable<RenderTargetTexture> {\r\n        return this._baseLayerRTTProvider?.getRenderTargetTextureForEye(eye) || null;\r\n    }\r\n\r\n    /**\r\n     * Gets the correct render target texture to be rendered this frame for this view\r\n     * @param view the view for which to get the render target\r\n     * @returns the render target for the specified view or null if not available\r\n     */\r\n    public getRenderTargetTextureForView(view: XRView): Nullable<RenderTargetTexture> {\r\n        return this._baseLayerRTTProvider?.getRenderTargetTextureForView(view) || null;\r\n    }\r\n\r\n    /**\r\n     * Creates a WebXRRenderTarget object for the XR session\r\n     * @param options optional options to provide when creating a new render target\r\n     * @returns a WebXR render target to which the session can render\r\n     */\r\n    public getWebXRRenderTarget(options?: WebXRManagedOutputCanvasOptions): WebXRRenderTarget {\r\n        const engine = this.scene.getEngine();\r\n        if (this._xrNavigator.xr.native) {\r\n            return new NativeXRRenderTarget(this);\r\n        } else {\r\n            options = options || WebXRManagedOutputCanvasOptions.GetDefaults(engine);\r\n            options.canvasElement = options.canvasElement || engine.getRenderingCanvas() || undefined;\r\n            return new WebXRManagedOutputCanvas(this, options);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Initializes the manager\r\n     * After initialization enterXR can be called to start an XR session\r\n     * @returns Promise which resolves after it is initialized\r\n     */\r\n    public initializeAsync(): Promise<void> {\r\n        // Check if the browser supports webXR\r\n        this._xrNavigator = navigator;\r\n        if (!this._xrNavigator.xr) {\r\n            return Promise.reject(\"WebXR not available\");\r\n        }\r\n        return Promise.resolve();\r\n    }\r\n\r\n    /**\r\n     * Initializes an xr session\r\n     * @param xrSessionMode mode to initialize\r\n     * @param xrSessionInit defines optional and required values to pass to the session builder\r\n     * @returns a promise which will resolve once the session has been initialized\r\n     */\r\n    public initializeSessionAsync(xrSessionMode: XRSessionMode = \"immersive-vr\", xrSessionInit: XRSessionInit = {}): Promise<XRSession> {\r\n        return this._xrNavigator.xr.requestSession(xrSessionMode, xrSessionInit).then((session: XRSession) => {\r\n            this.session = session;\r\n            this._sessionMode = xrSessionMode;\r\n            this.inXRSession = true;\r\n            this.onXRSessionInit.notifyObservers(session);\r\n\r\n            // handle when the session is ended (By calling session.end or device ends its own session eg. pressing home button on phone)\r\n            this.session.addEventListener(\r\n                \"end\",\r\n                () => {\r\n                    this.inXRSession = false;\r\n\r\n                    // Notify frame observers\r\n                    this.onXRSessionEnded.notifyObservers(null);\r\n\r\n                    if (this._engine) {\r\n                        // make sure dimensions object is restored\r\n                        this._engine.framebufferDimensionsObject = null;\r\n\r\n                        // Restore frame buffer to avoid clear on xr framebuffer after session end\r\n                        this._engine.restoreDefaultFramebuffer();\r\n\r\n                        // Need to restart render loop as after the session is ended the last request for new frame will never call callback\r\n                        this._engine.customAnimationFrameRequester = null;\r\n                        this._engine._renderLoop();\r\n                    }\r\n\r\n                    // Dispose render target textures.\r\n                    // Only dispose on native because we can't destroy opaque textures on browser.\r\n                    if (this.isNative) {\r\n                        this._baseLayerRTTProvider?.dispose();\r\n                    }\r\n                    this._baseLayerRTTProvider = null;\r\n                    this._baseLayerWrapper = null;\r\n                },\r\n                { once: true }\r\n            );\r\n\r\n            return this.session;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Checks if a session would be supported for the creation options specified\r\n     * @param sessionMode session mode to check if supported eg. immersive-vr\r\n     * @returns A Promise that resolves to true if supported and false if not\r\n     */\r\n    public isSessionSupportedAsync(sessionMode: XRSessionMode): Promise<boolean> {\r\n        return WebXRSessionManager.IsSessionSupportedAsync(sessionMode);\r\n    }\r\n\r\n    /**\r\n     * Resets the reference space to the one started the session\r\n     */\r\n    public resetReferenceSpace() {\r\n        this.referenceSpace = this.baseReferenceSpace;\r\n    }\r\n\r\n    /**\r\n     * Starts rendering to the xr layer\r\n     */\r\n    public runXRRenderLoop() {\r\n        if (!this.inXRSession || !this._engine) {\r\n            return;\r\n        }\r\n\r\n        // Tell the engine's render loop to be driven by the xr session's refresh rate and provide xr pose information\r\n        this._engine.customAnimationFrameRequester = {\r\n            requestAnimationFrame: (callback: FrameRequestCallback) => this.session.requestAnimationFrame(callback),\r\n            renderFunction: (timestamp: number, xrFrame: Nullable<XRFrame>) => {\r\n                if (!this.inXRSession || !this._engine) {\r\n                    return;\r\n                }\r\n                // Store the XR frame and timestamp in the session manager\r\n                this.currentFrame = xrFrame;\r\n                this.currentTimestamp = timestamp;\r\n                if (xrFrame) {\r\n                    this.inXRFrameLoop = true;\r\n                    const framebufferDimensionsObject = this._baseLayerRTTProvider?.getFramebufferDimensions() || null;\r\n                    // equality can be tested as it should be the same object\r\n                    if (this._engine.framebufferDimensionsObject !== framebufferDimensionsObject) {\r\n                        this._engine.framebufferDimensionsObject = framebufferDimensionsObject;\r\n                    }\r\n                    this.onXRFrameObservable.notifyObservers(xrFrame);\r\n                    this._engine._renderLoop();\r\n                    this._engine.framebufferDimensionsObject = null;\r\n                    this.inXRFrameLoop = false;\r\n                }\r\n            },\r\n        };\r\n\r\n        this._engine.framebufferDimensionsObject = this._baseLayerRTTProvider?.getFramebufferDimensions() || null;\r\n        this.onXRFrameObservable.addOnce(() => {\r\n            this.onXRReady.notifyObservers(this);\r\n        });\r\n\r\n        // Stop window's animation frame and trigger sessions animation frame\r\n        if (typeof window !== \"undefined\" && window.cancelAnimationFrame) {\r\n            window.cancelAnimationFrame(this._engine._frameHandler);\r\n        }\r\n        this._engine._renderLoop();\r\n    }\r\n\r\n    /**\r\n     * Sets the reference space on the xr session\r\n     * @param referenceSpaceType space to set\r\n     * @returns a promise that will resolve once the reference space has been set\r\n     */\r\n    public setReferenceSpaceTypeAsync(referenceSpaceType: XRReferenceSpaceType = \"local-floor\"): Promise<XRReferenceSpace> {\r\n        return this.session\r\n            .requestReferenceSpace(referenceSpaceType)\r\n            .then(\r\n                (referenceSpace) => {\r\n                    return referenceSpace as XRReferenceSpace;\r\n                },\r\n                (rejectionReason) => {\r\n                    Logger.Error(\"XR.requestReferenceSpace failed for the following reason: \");\r\n                    Logger.Error(rejectionReason);\r\n                    Logger.Log('Defaulting to universally-supported \"viewer\" reference space type.');\r\n\r\n                    return this.session.requestReferenceSpace(\"viewer\").then(\r\n                        (referenceSpace) => {\r\n                            const heightCompensation = new XRRigidTransform({ x: 0, y: -this.defaultHeightCompensation, z: 0 });\r\n                            return (referenceSpace as XRReferenceSpace).getOffsetReferenceSpace(heightCompensation);\r\n                        },\r\n                        (rejectionReason) => {\r\n                            Logger.Error(rejectionReason);\r\n                            // eslint-disable-next-line no-throw-literal\r\n                            throw 'XR initialization failed: required \"viewer\" reference space type not supported.';\r\n                        }\r\n                    );\r\n                }\r\n            )\r\n            .then((referenceSpace) => {\r\n                // create viewer reference space before setting the first reference space\r\n                return this.session.requestReferenceSpace(\"viewer\").then((viewerReferenceSpace) => {\r\n                    this.viewerReferenceSpace = viewerReferenceSpace as XRReferenceSpace;\r\n                    return referenceSpace;\r\n                });\r\n            })\r\n            .then((referenceSpace) => {\r\n                // initialize the base and offset (currently the same)\r\n                this.referenceSpace = this.baseReferenceSpace = referenceSpace;\r\n                this.onXRReferenceSpaceInitialized.notifyObservers(referenceSpace);\r\n                return this.referenceSpace;\r\n            });\r\n    }\r\n\r\n    /**\r\n     * Updates the render state of the session.\r\n     * Note that this is deprecated in favor of WebXRSessionManager.updateRenderState().\r\n     * @param state state to set\r\n     * @returns a promise that resolves once the render state has been updated\r\n     * @deprecated Use updateRenderState() instead.\r\n     */\r\n    public updateRenderStateAsync(state: XRRenderState): Promise<void> {\r\n        return Promise.resolve(this.session.updateRenderState(state));\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setBaseLayerWrapper(baseLayerWrapper: Nullable<WebXRLayerWrapper>): void {\r\n        if (this.isNative) {\r\n            this._baseLayerRTTProvider?.dispose();\r\n        }\r\n        this._baseLayerWrapper = baseLayerWrapper;\r\n        this._baseLayerRTTProvider = this._baseLayerWrapper?.createRenderTargetTextureProvider(this) || null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getBaseLayerWrapper(): Nullable<WebXRLayerWrapper> {\r\n        return this._baseLayerWrapper;\r\n    }\r\n\r\n    /**\r\n     * Updates the render state of the session\r\n     * @param state state to set\r\n     */\r\n    public updateRenderState(state: XRRenderStateInit): void {\r\n        if (state.baseLayer) {\r\n            this._setBaseLayerWrapper(this.isNative ? new NativeXRLayerWrapper(state.baseLayer) : new WebXRWebGLLayerWrapper(state.baseLayer));\r\n        }\r\n\r\n        this.session.updateRenderState(state);\r\n    }\r\n\r\n    /**\r\n     * Returns a promise that resolves with a boolean indicating if the provided session mode is supported by this browser\r\n     * @param sessionMode defines the session to test\r\n     * @returns a promise with boolean as final value\r\n     */\r\n    public static IsSessionSupportedAsync(sessionMode: XRSessionMode): Promise<boolean> {\r\n        if (!(navigator as any).xr) {\r\n            return Promise.resolve(false);\r\n        }\r\n        // When the specs are final, remove supportsSession!\r\n        const functionToUse = (navigator as any).xr.isSessionSupported || (navigator as any).xr.supportsSession;\r\n        if (!functionToUse) {\r\n            return Promise.resolve(false);\r\n        } else {\r\n            return functionToUse\r\n                .call((navigator as any).xr, sessionMode)\r\n                .then((result: boolean) => {\r\n                    const returnValue = typeof result === \"undefined\" ? true : result;\r\n                    return Promise.resolve(returnValue);\r\n                })\r\n                .catch((e: any) => {\r\n                    Logger.Warn(e);\r\n                    return Promise.resolve(false);\r\n                });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns true if Babylon.js is using the BabylonNative backend, otherwise false\r\n     */\r\n    public get isNative(): boolean {\r\n        return this._xrNavigator.xr.native ?? false;\r\n    }\r\n\r\n    /**\r\n     * The current frame rate as reported by the device\r\n     */\r\n    public get currentFrameRate(): number | undefined {\r\n        return this.session?.frameRate;\r\n    }\r\n\r\n    /**\r\n     * A list of supported frame rates (only available in-session!\r\n     */\r\n    public get supportedFrameRates(): Float32Array | undefined {\r\n        return this.session?.supportedFrameRates;\r\n    }\r\n\r\n    /**\r\n     * Set the framerate of the session.\r\n     * @param rate the new framerate. This value needs to be in the supportedFrameRates array\r\n     * @returns a promise that resolves once the framerate has been set\r\n     */\r\n    public updateTargetFrameRate(rate: number): Promise<void> {\r\n        return this.session.updateTargetFrameRate(rate);\r\n    }\r\n\r\n    /**\r\n     * Run a callback in the xr render loop\r\n     * @param callback the callback to call when in XR Frame\r\n     * @param ignoreIfNotInSession if no session is currently running, run it first thing on the next session\r\n     */\r\n    public runInXRFrame(callback: () => void, ignoreIfNotInSession = true): void {\r\n        if (this.inXRFrameLoop) {\r\n            callback();\r\n        } else if (this.inXRSession || !ignoreIfNotInSession) {\r\n            this.onXRFrameObservable.addOnce(callback);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Check if fixed foveation is supported on this device\r\n     */\r\n    public get isFixedFoveationSupported(): boolean {\r\n        return this._baseLayerWrapper?.isFixedFoveationSupported || false;\r\n    }\r\n\r\n    /**\r\n     * Get the fixed foveation currently set, as specified by the webxr specs\r\n     * If this returns null, then fixed foveation is not supported\r\n     */\r\n    public get fixedFoveation(): Nullable<number> {\r\n        return this._baseLayerWrapper?.fixedFoveation || null;\r\n    }\r\n\r\n    /**\r\n     * Set the fixed foveation to the specified value, as specified by the webxr specs\r\n     * This value will be normalized to be between 0 and 1, 1 being max foveation, 0 being no foveation\r\n     */\r\n    public set fixedFoveation(value: Nullable<number>) {\r\n        const val = Math.max(0, Math.min(1, value || 0));\r\n        if (this._baseLayerWrapper) {\r\n            this._baseLayerWrapper.fixedFoveation = val;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the features enabled on the current session\r\n     * This is only available in-session!\r\n     * @see https://www.w3.org/TR/webxr/#dom-xrsession-enabledfeatures\r\n     */\r\n    public get enabledFeatures(): Nullable<string[]> {\r\n        return this.session?.enabledFeatures ?? null;\r\n    }\r\n}\r\n"]}