{"version": 3, "file": "rgbdTextureTools.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/rgbdTextureTools.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAC3D,OAAO,gCAAgC,CAAC;AAGxC,OAAO,2CAA2C,CAAC;AACnD,OAAO,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAMlD;;GAEG;AACH,MAAM,OAAO,gBAAgB;IACzB;;;OAGG;IACI,MAAM,CAAC,iBAAiB,CAAC,OAAgB;QAC5C,MAAM,eAAe,GAAG,OAAO,CAAC,QAAQ,CAAC;QACzC,IAAI,CAAC,eAAe,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACrC,OAAO;SACV;QAED,yBAAyB;QACzB,MAAM,MAAM,GAAG,eAAe,CAAC,SAAS,EAAY,CAAC;QACrD,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QAC9B,MAAM,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC;QACxC,IAAI,aAAa,GAAG,KAAK,CAAC;QAE1B,wDAAwD;QACxD,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,+BAA+B,EAAE;YACrE,aAAa,GAAG,IAAI,CAAC;YACrB,eAAe,CAAC,IAAI,GAAG,SAAS,CAAC,sBAAsB,CAAC;SAC3D;QACD,wDAAwD;aACnD,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClE,aAAa,GAAG,IAAI,CAAC;YACrB,eAAe,CAAC,IAAI,GAAG,SAAS,CAAC,iBAAiB,CAAC;SACtD;QAED,IAAI,aAAa,EAAE;YACf,4BAA4B;YAC5B,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC;YAChC,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC;YAChC,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC;SACnC;QAED,MAAM,iBAAiB,GAAG,GAAG,EAAE;YAC3B,iCAAiC;YACjC,oCAAoC;YACpC,MAAM,eAAe,GAAG,IAAI,WAAW,CACnC,YAAY,EACZ,YAAY,EACZ,IAAI,EACJ,IAAI,EACJ,CAAC,EACD,IAAI,EACJ,SAAS,CAAC,8BAA8B,EACxC,MAAM,EACN,KAAK,EACL,SAAS,EACT,eAAe,CAAC,IAAI,EACpB,SAAS,EACT,IAAI,EACJ,KAAK,CACR,CAAC;YACF,eAAe,CAAC,6BAA6B,GAAG,IAAI,CAAC;YAErD,mCAAmC;YACnC,MAAM,eAAe,GAAG,MAAM,CAAC,yBAAyB,CAAC,eAAe,CAAC,KAAK,EAAE;gBAC5E,mBAAmB,EAAE,KAAK;gBAC1B,eAAe,EAAE,KAAK;gBACtB,qBAAqB,EAAE,KAAK;gBAC5B,YAAY,EAAE,eAAe,CAAC,YAAY;gBAC1C,IAAI,EAAE,eAAe,CAAC,IAAI;gBAC1B,MAAM,EAAE,SAAS,CAAC,kBAAkB;aACvC,CAAC,CAAC;YAEH,eAAe,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE;gBACjD,iBAAiB;gBACjB,eAAe,CAAC,OAAO,GAAG,CAAC,MAAM,EAAE,EAAE;oBACjC,MAAM,CAAC,YAAY,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;oBACvD,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACpC,CAAC,CAAC;gBACF,OAAO,CAAC,QAAQ,EAAG,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,eAAgB,CAAC,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;gBAE/F,UAAU;gBACV,MAAM,CAAC,yBAAyB,EAAE,CAAC;gBACnC,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;gBACxC,IAAI,eAAe,EAAE;oBACjB,eAAe,CAAC,OAAO,EAAE,CAAC;iBAC7B;gBAED,gBAAgB;gBAChB,eAAe,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;gBAE7C,8BAA8B;gBAC9B,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC;YACnC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QAEF,IAAI,aAAa,EAAE;YACf,IAAI,OAAO,EAAE;gBACT,iBAAiB,EAAE,CAAC;aACvB;iBAAM;gBACH,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;aACvD;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,mBAAmB,CAAC,eAAgC,EAAE,KAAY,EAAE,iBAAiB,GAAG,SAAS,CAAC,yBAAyB;QACrI,OAAO,gBAAgB,CAAC,YAAY,EAAE,eAAe,EAAE,KAAK,EAAE,iBAAiB,EAAE,SAAS,CAAC,4BAA4B,EAAE,SAAS,CAAC,kBAAkB,CAAC,CAAC;IAC3J,CAAC;CACJ", "sourcesContent": ["import { Constants } from \"../Engines/constants\";\r\nimport { PostProcess } from \"../PostProcesses/postProcess\";\r\nimport \"../Shaders/rgbdDecode.fragment\";\r\nimport type { Engine } from \"../Engines/engine\";\r\n\r\nimport \"../Engines/Extensions/engine.renderTarget\";\r\nimport { ApplyPostProcess } from \"./textureTools\";\r\n\r\nimport type { Texture } from \"../Materials/Textures/texture\";\r\nimport type { InternalTexture } from \"../Materials/Textures/internalTexture\";\r\nimport type { Scene } from \"../scene\";\r\n\r\n/**\r\n * Class used to host RGBD texture specific utilities\r\n */\r\nexport class RGBDTextureTools {\r\n    /**\r\n     * Expand the RGBD Texture from RGBD to Half Float if possible.\r\n     * @param texture the texture to expand.\r\n     */\r\n    public static ExpandRGBDTexture(texture: Texture) {\r\n        const internalTexture = texture._texture;\r\n        if (!internalTexture || !texture.isRGBD) {\r\n            return;\r\n        }\r\n\r\n        // Gets everything ready.\r\n        const engine = internalTexture.getEngine() as Engine;\r\n        const caps = engine.getCaps();\r\n        const isReady = internalTexture.isReady;\r\n        let expandTexture = false;\r\n\r\n        // If half float available we can uncompress the texture\r\n        if (caps.textureHalfFloatRender && caps.textureHalfFloatLinearFiltering) {\r\n            expandTexture = true;\r\n            internalTexture.type = Constants.TEXTURETYPE_HALF_FLOAT;\r\n        }\r\n        // If full float available we can uncompress the texture\r\n        else if (caps.textureFloatRender && caps.textureFloatLinearFiltering) {\r\n            expandTexture = true;\r\n            internalTexture.type = Constants.TEXTURETYPE_FLOAT;\r\n        }\r\n\r\n        if (expandTexture) {\r\n            // Do not use during decode.\r\n            internalTexture.isReady = false;\r\n            internalTexture._isRGBD = false;\r\n            internalTexture.invertY = false;\r\n        }\r\n\r\n        const expandRGBDTexture = () => {\r\n            // Expand the texture if possible\r\n            // Simply run through the decode PP.\r\n            const rgbdPostProcess = new PostProcess(\r\n                \"rgbdDecode\",\r\n                \"rgbdDecode\",\r\n                null,\r\n                null,\r\n                1,\r\n                null,\r\n                Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n                engine,\r\n                false,\r\n                undefined,\r\n                internalTexture.type,\r\n                undefined,\r\n                null,\r\n                false\r\n            );\r\n            rgbdPostProcess.externalTextureSamplerBinding = true;\r\n\r\n            // Hold the output of the decoding.\r\n            const expandedTexture = engine.createRenderTargetTexture(internalTexture.width, {\r\n                generateDepthBuffer: false,\r\n                generateMipMaps: false,\r\n                generateStencilBuffer: false,\r\n                samplingMode: internalTexture.samplingMode,\r\n                type: internalTexture.type,\r\n                format: Constants.TEXTUREFORMAT_RGBA,\r\n            });\r\n\r\n            rgbdPostProcess.getEffect().executeWhenCompiled(() => {\r\n                // PP Render Pass\r\n                rgbdPostProcess.onApply = (effect) => {\r\n                    effect._bindTexture(\"textureSampler\", internalTexture);\r\n                    effect.setFloat2(\"scale\", 1, 1);\r\n                };\r\n                texture.getScene()!.postProcessManager.directRender([rgbdPostProcess!], expandedTexture, true);\r\n\r\n                // Cleanup\r\n                engine.restoreDefaultFramebuffer();\r\n                engine._releaseTexture(internalTexture);\r\n                if (rgbdPostProcess) {\r\n                    rgbdPostProcess.dispose();\r\n                }\r\n\r\n                // Internal Swap\r\n                expandedTexture._swapAndDie(internalTexture);\r\n\r\n                // Ready to get rolling again.\r\n                internalTexture.isReady = true;\r\n            });\r\n        };\r\n\r\n        if (expandTexture) {\r\n            if (isReady) {\r\n                expandRGBDTexture();\r\n            } else {\r\n                texture.onLoadObservable.addOnce(expandRGBDTexture);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Encode the texture to RGBD if possible.\r\n     * @param internalTexture the texture to encode\r\n     * @param scene the scene hosting the texture\r\n     * @param outputTextureType type of the texture in which the encoding is performed\r\n     * @returns a promise with the internalTexture having its texture replaced by the result of the processing\r\n     */\r\n    public static EncodeTextureToRGBD(internalTexture: InternalTexture, scene: Scene, outputTextureType = Constants.TEXTURETYPE_UNSIGNED_BYTE): Promise<InternalTexture> {\r\n        return ApplyPostProcess(\"rgbdEncode\", internalTexture, scene, outputTextureType, Constants.TEXTURE_NEAREST_SAMPLINGMODE, Constants.TEXTUREFORMAT_RGBA);\r\n    }\r\n}\r\n"]}