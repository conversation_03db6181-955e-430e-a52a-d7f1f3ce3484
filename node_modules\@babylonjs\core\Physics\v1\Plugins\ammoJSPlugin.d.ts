import { Quaternion, Vector3 } from "../../../Maths/math.vector";
import type { IPhysicsEnginePlugin, PhysicsImpostorJoint } from "../IPhysicsEnginePlugin";
import { PhysicsImpostor } from "../physicsImpostor";
import type { IMotorEnabledJoint } from "../physicsJoint";
import type { Nullable } from "../../../types";
import type { AbstractMesh } from "../../../Meshes/abstractMesh";
import { PhysicsRaycastResult } from "../../physicsRaycastResult";
/**
 * AmmoJS Physics plugin
 * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine
 * @see https://github.com/kripken/ammo.js/
 */
export declare class AmmoJSPlugin implements IPhysicsEnginePlugin {
    private _useDeltaForWorldStep;
    /**
     * Reference to the Ammo library
     */
    bjsAMMO: any;
    /**
     * Created ammoJS world which physics bodies are added to
     */
    world: any;
    /**
     * Name of the plugin
     */
    name: string;
    private _timeStep;
    private _fixedTimeStep;
    private _maxSteps;
    private _tmpQuaternion;
    private _tmpAmmoTransform;
    private _tmpAmmoQuaternion;
    private _tmpAmmoConcreteContactResultCallback;
    private _collisionConfiguration;
    private _dispatcher;
    private _overlappingPairCache;
    private _solver;
    private _softBodySolver;
    private _tmpAmmoVectorA;
    private _tmpAmmoVectorB;
    private _tmpAmmoVectorC;
    private _tmpAmmoVectorD;
    private _tmpContactCallbackResult;
    private _tmpAmmoVectorRCA;
    private _tmpAmmoVectorRCB;
    private _raycastResult;
    private _tmpContactPoint;
    private _tmpContactNormal;
    private _tmpContactDistance;
    private _tmpContactImpulse;
    private _tmpVec3;
    private static readonly _DISABLE_COLLISION_FLAG;
    private static readonly _KINEMATIC_FLAG;
    private static readonly _DISABLE_DEACTIVATION_FLAG;
    /**
     * Initializes the ammoJS plugin
     * @param _useDeltaForWorldStep if the time between frames should be used when calculating physics steps (Default: true)
     * @param ammoInjection can be used to inject your own ammo reference
     * @param overlappingPairCache can be used to specify your own overlapping pair cache
     */
    constructor(_useDeltaForWorldStep?: boolean, ammoInjection?: any, overlappingPairCache?: any);
    /**
     *
     * @returns plugin version
     */
    getPluginVersion(): number;
    /**
     * Sets the gravity of the physics world (m/(s^2))
     * @param gravity Gravity to set
     */
    setGravity(gravity: Vector3): void;
    /**
     * Amount of time to step forward on each frame (only used if useDeltaForWorldStep is false in the constructor)
     * @param timeStep timestep to use in seconds
     */
    setTimeStep(timeStep: number): void;
    /**
     * Increment to step forward in the physics engine (If timeStep is set to 1/60 and fixedTimeStep is set to 1/120 the physics engine should run 2 steps per frame) (Default: 1/60)
     * @param fixedTimeStep fixedTimeStep to use in seconds
     */
    setFixedTimeStep(fixedTimeStep: number): void;
    /**
     * Sets the maximum number of steps by the physics engine per frame (Default: 5)
     * @param maxSteps the maximum number of steps by the physics engine per frame
     */
    setMaxSteps(maxSteps: number): void;
    /**
     * Gets the current timestep (only used if useDeltaForWorldStep is false in the constructor)
     * @returns the current timestep in seconds
     */
    getTimeStep(): number;
    /**
     * The create custom shape handler function to be called when using BABYLON.PhysicsImposter.CustomImpostor
     */
    onCreateCustomShape: (impostor: PhysicsImpostor) => any;
    /**
     * The create custom mesh impostor handler function to support building custom mesh impostor vertex data
     */
    onCreateCustomMeshImpostor: (impostor: PhysicsImpostor) => any;
    /**
     * The create custom convex hull impostor handler function to support building custom convex hull impostor vertex data
     */
    onCreateCustomConvexHullImpostor: (impostor: PhysicsImpostor) => any;
    private _isImpostorInContact;
    private _isImpostorPairInContact;
    private _stepSimulation;
    /**
     * Moves the physics simulation forward delta seconds and updates the given physics imposters
     * Prior to the step the imposters physics location is set to the position of the babylon meshes
     * After the step the babylon meshes are set to the position of the physics imposters
     * @param delta amount of time to step forward
     * @param impostors array of imposters to update before/after the step
     */
    executeStep(delta: number, impostors: Array<PhysicsImpostor>): void;
    /**
     * Update babylon mesh to match physics world object
     * @param impostor imposter to match
     */
    private _afterSoftStep;
    /**
     * Update babylon mesh vertices vertices to match physics world softbody or cloth
     * @param impostor imposter to match
     */
    private _ropeStep;
    /**
     * Update babylon mesh vertices vertices to match physics world softbody or cloth
     * @param impostor imposter to match
     */
    private _softbodyOrClothStep;
    private _tmpMatrix;
    /**
     * Applies an impulse on the imposter
     * @param impostor imposter to apply impulse to
     * @param force amount of force to be applied to the imposter
     * @param contactPoint the location to apply the impulse on the imposter
     */
    applyImpulse(impostor: PhysicsImpostor, force: Vector3, contactPoint: Vector3): void;
    /**
     * Applies a force on the imposter
     * @param impostor imposter to apply force
     * @param force amount of force to be applied to the imposter
     * @param contactPoint the location to apply the force on the imposter
     */
    applyForce(impostor: PhysicsImpostor, force: Vector3, contactPoint: Vector3): void;
    /**
     * Creates a physics body using the plugin
     * @param impostor the imposter to create the physics body on
     */
    generatePhysicsBody(impostor: PhysicsImpostor): void;
    /**
     * Removes the physics body from the imposter and disposes of the body's memory
     * @param impostor imposter to remove the physics body from
     */
    removePhysicsBody(impostor: PhysicsImpostor): void;
    /**
     * Generates a joint
     * @param impostorJoint the imposter joint to create the joint with
     */
    generateJoint(impostorJoint: PhysicsImpostorJoint): void;
    /**
     * Removes a joint
     * @param impostorJoint the imposter joint to remove the joint from
     */
    removeJoint(impostorJoint: PhysicsImpostorJoint): void;
    private _addMeshVerts;
    /**
     * Initialise the soft body vertices to match its object's (mesh) vertices
     * Softbody vertices (nodes) are in world space and to match this
     * The object's position and rotation is set to zero and so its vertices are also then set in world space
     * @param impostor to create the softbody for
     * @returns the number of vertices added to the softbody
     */
    private _softVertexData;
    /**
     * Create an impostor's soft body
     * @param impostor to create the softbody for
     * @returns the softbody
     */
    private _createSoftbody;
    /**
     * Create cloth for an impostor
     * @param impostor to create the softbody for
     * @returns the cloth
     */
    private _createCloth;
    /**
     * Create rope for an impostor
     * @param impostor to create the softbody for
     * @returns the rope
     */
    private _createRope;
    /**
     * Create a custom physics impostor shape using the plugin's onCreateCustomShape handler
     * @param impostor to create the custom physics shape for
     * @returns the custom physics shape
     */
    private _createCustom;
    private _addHullVerts;
    private _createShape;
    /**
     * Sets the mesh body position/rotation from the babylon impostor
     * @param impostor imposter containing the physics body and babylon object
     */
    setTransformationFromPhysicsBody(impostor: PhysicsImpostor): void;
    /**
     * Sets the babylon object's position/rotation from the physics body's position/rotation
     * @param impostor imposter containing the physics body and babylon object
     * @param newPosition new position
     * @param newRotation new rotation
     */
    setPhysicsBodyTransformation(impostor: PhysicsImpostor, newPosition: Vector3, newRotation: Quaternion): void;
    /**
     * If this plugin is supported
     * @returns true if its supported
     */
    isSupported(): boolean;
    /**
     * Sets the linear velocity of the physics body
     * @param impostor imposter to set the velocity on
     * @param velocity velocity to set
     */
    setLinearVelocity(impostor: PhysicsImpostor, velocity: Vector3): void;
    /**
     * Sets the angular velocity of the physics body
     * @param impostor imposter to set the velocity on
     * @param velocity velocity to set
     */
    setAngularVelocity(impostor: PhysicsImpostor, velocity: Vector3): void;
    /**
     * gets the linear velocity
     * @param impostor imposter to get linear velocity from
     * @returns linear velocity
     */
    getLinearVelocity(impostor: PhysicsImpostor): Nullable<Vector3>;
    /**
     * gets the angular velocity
     * @param impostor imposter to get angular velocity from
     * @returns angular velocity
     */
    getAngularVelocity(impostor: PhysicsImpostor): Nullable<Vector3>;
    /**
     * Sets the mass of physics body
     * @param impostor imposter to set the mass on
     * @param mass mass to set
     */
    setBodyMass(impostor: PhysicsImpostor, mass: number): void;
    /**
     * Gets the mass of the physics body
     * @param impostor imposter to get the mass from
     * @returns mass
     */
    getBodyMass(impostor: PhysicsImpostor): number;
    /**
     * Gets friction of the impostor
     * @param impostor impostor to get friction from
     * @returns friction value
     */
    getBodyFriction(impostor: PhysicsImpostor): number;
    /**
     * Sets friction of the impostor
     * @param impostor impostor to set friction on
     * @param friction friction value
     */
    setBodyFriction(impostor: PhysicsImpostor, friction: number): void;
    /**
     * Gets restitution of the impostor
     * @param impostor impostor to get restitution from
     * @returns restitution value
     */
    getBodyRestitution(impostor: PhysicsImpostor): number;
    /**
     * Sets restitution of the impostor
     * @param impostor impostor to set resitution on
     * @param restitution resitution value
     */
    setBodyRestitution(impostor: PhysicsImpostor, restitution: number): void;
    /**
     * Gets pressure inside the impostor
     * @param impostor impostor to get pressure from
     * @returns pressure value
     */
    getBodyPressure(impostor: PhysicsImpostor): number;
    /**
     * Sets pressure inside a soft body impostor
     * Cloth and rope must remain 0 pressure
     * @param impostor impostor to set pressure on
     * @param pressure pressure value
     */
    setBodyPressure(impostor: PhysicsImpostor, pressure: number): void;
    /**
     * Gets stiffness of the impostor
     * @param impostor impostor to get stiffness from
     * @returns pressure value
     */
    getBodyStiffness(impostor: PhysicsImpostor): number;
    /**
     * Sets stiffness of the impostor
     * @param impostor impostor to set stiffness on
     * @param stiffness stiffness value from 0 to 1
     */
    setBodyStiffness(impostor: PhysicsImpostor, stiffness: number): void;
    /**
     * Gets velocityIterations of the impostor
     * @param impostor impostor to get velocity iterations from
     * @returns velocityIterations value
     */
    getBodyVelocityIterations(impostor: PhysicsImpostor): number;
    /**
     * Sets velocityIterations of the impostor
     * @param impostor impostor to set velocity iterations on
     * @param velocityIterations velocityIterations value
     */
    setBodyVelocityIterations(impostor: PhysicsImpostor, velocityIterations: number): void;
    /**
     * Gets positionIterations of the impostor
     * @param impostor impostor to get position iterations from
     * @returns positionIterations value
     */
    getBodyPositionIterations(impostor: PhysicsImpostor): number;
    /**
     * Sets positionIterations of the impostor
     * @param impostor impostor to set position on
     * @param positionIterations positionIterations value
     */
    setBodyPositionIterations(impostor: PhysicsImpostor, positionIterations: number): void;
    /**
     * Append an anchor to a cloth object
     * @param impostor is the cloth impostor to add anchor to
     * @param otherImpostor is the rigid impostor to anchor to
     * @param width ratio across width from 0 to 1
     * @param height ratio up height from 0 to 1
     * @param influence the elasticity between cloth impostor and anchor from 0, very stretchy to 1, little stretch
     * @param noCollisionBetweenLinkedBodies when true collisions between soft impostor and anchor are ignored; default false
     */
    appendAnchor(impostor: PhysicsImpostor, otherImpostor: PhysicsImpostor, width: number, height: number, influence?: number, noCollisionBetweenLinkedBodies?: boolean): void;
    /**
     * Append an hook to a rope object
     * @param impostor is the rope impostor to add hook to
     * @param otherImpostor is the rigid impostor to hook to
     * @param length ratio along the rope from 0 to 1
     * @param influence the elasticity between soft impostor and anchor from 0, very stretchy to 1, little stretch
     * @param noCollisionBetweenLinkedBodies when true collisions between soft impostor and anchor are ignored; default false
     */
    appendHook(impostor: PhysicsImpostor, otherImpostor: PhysicsImpostor, length: number, influence?: number, noCollisionBetweenLinkedBodies?: boolean): void;
    /**
     * Sleeps the physics body and stops it from being active
     * @param impostor impostor to sleep
     */
    sleepBody(impostor: PhysicsImpostor): void;
    /**
     * Activates the physics body
     * @param impostor impostor to activate
     */
    wakeUpBody(impostor: PhysicsImpostor): void;
    /**
     * Updates the distance parameters of the joint
     */
    updateDistanceJoint(): void;
    /**
     * Sets a motor on the joint
     * @param joint joint to set motor on
     * @param speed speed of the motor
     * @param maxForce maximum force of the motor
     */
    setMotor(joint: IMotorEnabledJoint, speed?: number, maxForce?: number): void;
    /**
     * Sets the motors limit
     */
    setLimit(): void;
    /**
     * Syncs the position and rotation of a mesh with the impostor
     * @param mesh mesh to sync
     * @param impostor impostor to update the mesh with
     */
    syncMeshWithImpostor(mesh: AbstractMesh, impostor: PhysicsImpostor): void;
    /**
     * Gets the radius of the impostor
     * @param impostor impostor to get radius from
     * @returns the radius
     */
    getRadius(impostor: PhysicsImpostor): number;
    /**
     * Gets the box size of the impostor
     * @param impostor impostor to get box size from
     * @param result the resulting box size
     */
    getBoxSizeToRef(impostor: PhysicsImpostor, result: Vector3): void;
    /**
     * Disposes of the impostor
     */
    dispose(): void;
    /**
     * Does a raycast in the physics world
     * @param from where should the ray start?
     * @param to where should the ray end?
     * @returns PhysicsRaycastResult
     */
    raycast(from: Vector3, to: Vector3): PhysicsRaycastResult;
    /**
     * Does a raycast in the physics world
     * @param from when should the ray start?
     * @param to when should the ray end?
     * @param result resulting PhysicsRaycastResult
     */
    raycastToRef(from: Vector3, to: Vector3, result: PhysicsRaycastResult): void;
}
