{"version": 3, "file": "pbrMetallicRoughnessMaterial.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/PBR/pbrMetallicRoughnessMaterial.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3G,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AAI1E,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAGrD;;;;;GAKG;AACH,MAAM,OAAO,4BAA6B,SAAQ,qBAAqB;IA2CnE;;;;;OAKG;IACH,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACnB,IAAI,CAAC,qCAAqC,GAAG,KAAK,CAAC;QACnD,IAAI,CAAC,qCAAqC,GAAG,IAAI,CAAC;QAClD,IAAI,CAAC,qCAAqC,GAAG,IAAI,CAAC;QAClD,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO,8BAA8B,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,IAAY;QACrB,MAAM,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAE7G,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC;QAChB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEzC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,mBAAmB,CAAC,UAAU,GAAG,sCAAsC,CAAC;QAExE,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC3D,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QAC7D,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;QACjD,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QACnD,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QAC7D,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;QAE/D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,MAAM,QAAQ,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,4BAA4B,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC/H,IAAI,MAAM,CAAC,SAAS,EAAE;YAClB,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAC9D;QACD,IAAI,MAAM,CAAC,UAAU,EAAE;YACnB,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAChE;QACD,IAAI,MAAM,CAAC,IAAI,EAAE;YACb,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACpD;QACD,IAAI,MAAM,CAAC,KAAK,EAAE;YACd,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACtD;QACD,IAAI,MAAM,CAAC,UAAU,EAAE;YACnB,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAChE;QACD,IAAI,MAAM,CAAC,WAAW,EAAE;YACpB,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAClE;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;CACJ;AA3HU;IAFN,iBAAiB,EAAE;IACnB,gBAAgB,CAAC,kCAAkC,EAAE,cAAc,CAAC;+DAC5C;AAQlB;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,EAAE,gBAAgB,CAAC;iEAC7B;AAQnC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;8DAC7B;AAQjB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;+DAC5B;AAQlB;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,EAAE,kBAAkB,CAAC;8EAClB;AA6F3D,aAAa,CAAC,sCAAsC,EAAE,4BAA4B,CAAC,CAAC", "sourcesContent": ["import { serialize, serializeAsColor3, expandToProperty, serializeAsTexture } from \"../../Misc/decorators\";\r\nimport { SerializationHelper } from \"../../Misc/decorators.serialization\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Color3 } from \"../../Maths/math.color\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { PBRBaseSimpleMaterial } from \"./pbrBaseSimpleMaterial\";\r\nimport { RegisterClass } from \"../../Misc/typeStore\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * The PBR material of BJS following the metal roughness convention.\r\n *\r\n * This fits to the PBR convention in the GLTF definition:\r\n * https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Archived/KHR_materials_pbrSpecularGlossiness/README.md\r\n */\r\nexport class PBRMetallicRoughnessMaterial extends PBRBaseSimpleMaterial {\r\n    /**\r\n     * The base color has two different interpretations depending on the value of metalness.\r\n     * When the material is a metal, the base color is the specific measured reflectance value\r\n     * at normal incidence (F0). For a non-metal the base color represents the reflected diffuse color\r\n     * of the material.\r\n     */\r\n    @serializeAsColor3()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_albedoColor\")\r\n    public baseColor: Color3;\r\n\r\n    /**\r\n     * Base texture of the metallic workflow. It contains both the baseColor information in RGB as\r\n     * well as opacity information in the alpha channel.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_albedoTexture\")\r\n    public baseTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Specifies the metallic scalar value of the material.\r\n     * Can also be used to scale the metalness values of the metallic texture.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public metallic: number;\r\n\r\n    /**\r\n     * Specifies the roughness scalar value of the material.\r\n     * Can also be used to scale the roughness values of the metallic texture.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public roughness: number;\r\n\r\n    /**\r\n     * Texture containing both the metallic value in the B channel and the\r\n     * roughness value in the G channel to keep better precision.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_metallicTexture\")\r\n    public metallicRoughnessTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Instantiates a new PBRMetalRoughnessMaterial instance.\r\n     *\r\n     * @param name The material name\r\n     * @param scene The scene the material will be use in.\r\n     */\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n        this._useRoughnessFromMetallicTextureAlpha = false;\r\n        this._useRoughnessFromMetallicTextureGreen = true;\r\n        this._useMetallnessFromMetallicTextureBlue = true;\r\n        this.metallic = 1.0;\r\n        this.roughness = 1.0;\r\n    }\r\n\r\n    /**\r\n     * @returns the current class name of the material.\r\n     */\r\n    public getClassName(): string {\r\n        return \"PBRMetallicRoughnessMaterial\";\r\n    }\r\n\r\n    /**\r\n     * Makes a duplicate of the current material.\r\n     * @param name - name to use for the new material.\r\n     * @returns cloned material instance\r\n     */\r\n    public clone(name: string): PBRMetallicRoughnessMaterial {\r\n        const clone = SerializationHelper.Clone(() => new PBRMetallicRoughnessMaterial(name, this.getScene()), this);\r\n\r\n        clone.id = name;\r\n        clone.name = name;\r\n\r\n        this.clearCoat.copyTo(clone.clearCoat);\r\n        this.anisotropy.copyTo(clone.anisotropy);\r\n        this.brdf.copyTo(clone.brdf);\r\n        this.sheen.copyTo(clone.sheen);\r\n        this.subSurface.copyTo(clone.subSurface);\r\n\r\n        return clone;\r\n    }\r\n\r\n    /**\r\n     * Serialize the material to a parsable JSON object.\r\n     * @returns the JSON object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n        serializationObject.customType = \"BABYLON.PBRMetallicRoughnessMaterial\";\r\n\r\n        serializationObject.clearCoat = this.clearCoat.serialize();\r\n        serializationObject.anisotropy = this.anisotropy.serialize();\r\n        serializationObject.brdf = this.brdf.serialize();\r\n        serializationObject.sheen = this.sheen.serialize();\r\n        serializationObject.subSurface = this.subSurface.serialize();\r\n        serializationObject.iridescence = this.iridescence.serialize();\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parses a JSON object corresponding to the serialize function.\r\n     * @param source - JSON source object.\r\n     * @param scene - Defines the scene we are parsing for\r\n     * @param rootUrl - Defines the rootUrl of this parsed object\r\n     * @returns a new PBRMetalRoughnessMaterial\r\n     */\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): PBRMetallicRoughnessMaterial {\r\n        const material = SerializationHelper.Parse(() => new PBRMetallicRoughnessMaterial(source.name, scene), source, scene, rootUrl);\r\n        if (source.clearCoat) {\r\n            material.clearCoat.parse(source.clearCoat, scene, rootUrl);\r\n        }\r\n        if (source.anisotropy) {\r\n            material.anisotropy.parse(source.anisotropy, scene, rootUrl);\r\n        }\r\n        if (source.brdf) {\r\n            material.brdf.parse(source.brdf, scene, rootUrl);\r\n        }\r\n        if (source.sheen) {\r\n            material.sheen.parse(source.sheen, scene, rootUrl);\r\n        }\r\n        if (source.subSurface) {\r\n            material.subSurface.parse(source.subSurface, scene, rootUrl);\r\n        }\r\n        if (source.iridescence) {\r\n            material.iridescence.parse(source.iridescence, scene, rootUrl);\r\n        }\r\n        return material;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PBRMetallicRoughnessMaterial\", PBRMetallicRoughnessMaterial);\r\n"]}