{"version": 3, "file": "ssrRenderingPipeline.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/PostProcesses/RenderPipeline/Pipelines/ssrRenderingPipeline.ts"], "names": [], "mappings": ";AAAA,yDAAyD;AACzD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,mBAAmB,EAAE,MAAM,wCAAwC,CAAC;AAC7E,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,4BAA4B,CAAC;AAGrF,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,yBAAyB,EAAE,MAAM,8BAA8B,CAAC;AACzE,OAAO,EAAE,uBAAuB,EAAE,MAAM,4BAA4B,CAAC;AAErE,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,oCAAoC,EAAE,MAAM,yDAAyD,CAAC;AAE/G,OAAO,EAAE,sBAAsB,EAAE,MAAM,2CAA2C,CAAC;AACnF,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AAGvD,OAAO,EAAE,aAAa,EAAE,MAAM,kCAAkC,CAAC;AAGjE,OAAO,mDAAmD,CAAC;AAE3D,OAAO,kDAAkD,CAAC;AAC1D,OAAO,sDAAsD,CAAC;AAC9D,OAAO,8DAA8D,CAAC;AAEtE,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAC1G,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAE5G;;;;;;;;;;;;GAYG;AACH,MAAM,OAAO,oBAAqB,SAAQ,yBAAyB;IAe/D;;OAEG;IACH,IAAW,OAAO,CAAC,WAAmB;QAClC,IAAI,IAAI,CAAC,QAAQ,KAAK,WAAW,EAAE;YAC/B,OAAO;SACV;QACD,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;QAE5B,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAGD,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAsDD;;;OAGG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED,IAAW,qBAAqB,CAAC,SAAiB;QAC9C,IAAI,SAAS,KAAK,IAAI,CAAC,sBAAsB,EAAE;YAC3C,OAAO;SACV;QAED,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,CAAC,sBAAsB,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,CAAC,sBAAsB,KAAK,CAAC,CAAC,EAAE;YAClH,IAAI,CAAC,sBAAsB,GAAG,SAAS,CAAC;YACxC,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;aAAM;YACH,IAAI,CAAC,sBAAsB,GAAG,SAAS,CAAC;SAC3C;IACL,CAAC;IAKD;;;;OAIG;IAEH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,UAAkB;QACvC,IAAI,UAAU,KAAK,IAAI,CAAC,cAAc,EAAE;YACpC,OAAO;SACV;QAED,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;QACjC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAKD;;;;OAIG;IACH,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED,IAAW,sBAAsB,CAAC,QAAgB;QAC9C,IAAI,QAAQ,KAAK,IAAI,CAAC,uBAAuB,EAAE;YAC3C,OAAO;SACV;QAED,MAAM,OAAO,GAAG,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC,uBAAuB,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC,uBAAuB,KAAK,CAAC,CAAC,CAAC;QAEjI,IAAI,CAAC,uBAAuB,GAAG,QAAQ,CAAC;QAExC,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;IACL,CAAC;IAEO,QAAQ;QACZ,OAAO,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC;IAC5C,CAAC;IAKD;;;OAGG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,IAAW,cAAc,CAAC,UAAkB;QACxC,IAAI,UAAU,KAAK,IAAI,CAAC,eAAe,EAAE;YACrC,OAAO;SACV;QAED,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAKD;;;;OAIG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED,IAAW,uBAAuB,CAAC,OAAgB;QAC/C,IAAI,OAAO,KAAK,IAAI,CAAC,wBAAwB,EAAE;YAC3C,OAAO;SACV;QAED,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC;QACxC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAKD;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED,IAAW,kBAAkB,CAAC,OAA8B;QACxD,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC;QACnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAKD;;;OAGG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IAED,IAAW,yBAAyB,CAAC,OAAgB;QACjD,IAAI,CAAC,0BAA0B,GAAG,OAAO,CAAC;QAC1C,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAKD;;OAEG;IACH,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED,IAAW,sBAAsB,CAAC,SAAkB;QAChD,IAAI,IAAI,CAAC,uBAAuB,KAAK,SAAS,EAAE;YAC5C,OAAO;SACV;QACD,IAAI,CAAC,uBAAuB,GAAG,SAAS,CAAC;QACzC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAKD;;OAEG;IACH,IAAW,6BAA6B;QACpC,OAAO,IAAI,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IAED,IAAW,6BAA6B,CAAC,SAAkB;QACvD,IAAI,IAAI,CAAC,8BAA8B,KAAK,SAAS,EAAE;YACnD,OAAO;SACV;QACD,IAAI,CAAC,8BAA8B,GAAG,SAAS,CAAC;QAChD,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAKD;;OAEG;IACH,IAAW,+BAA+B;QACtC,OAAO,IAAI,CAAC,gCAAgC,CAAC;IACjD,CAAC;IAED,IAAW,+BAA+B,CAAC,SAAkB;QACzD,IAAI,IAAI,CAAC,gCAAgC,KAAK,SAAS,EAAE;YACrD,OAAO;SACV;QACD,IAAI,CAAC,gCAAgC,GAAG,SAAS,CAAC;QAClD,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAKD;;OAEG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED,IAAW,qBAAqB,CAAC,SAAkB;QAC/C,IAAI,IAAI,CAAC,sBAAsB,KAAK,SAAS,EAAE;YAC3C,OAAO;SACV;QACD,IAAI,CAAC,sBAAsB,GAAG,SAAS,CAAC;QACxC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAKD;;OAEG;IACH,IAAW,2BAA2B;QAClC,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED,IAAW,2BAA2B,CAAC,SAAkB;QACrD,IAAI,IAAI,CAAC,4BAA4B,KAAK,SAAS,EAAE;YACjD,OAAO;SACV;QACD,IAAI,CAAC,4BAA4B,GAAG,SAAS,CAAC;QAC9C,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAKD;;;OAGG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,IAAa;QAClC,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;YAC9B,OAAO;SACV;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAKD;;;OAGG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,OAAgB;QAClC,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,EAAE;YAC9B,OAAO;SACV;QACD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAKD;;;;;;OAMG;IACH,IAAW,mCAAmC;QAC1C,OAAO,IAAI,CAAC,oCAAoC,CAAC;IACrD,CAAC;IAED,IAAW,mCAAmC,CAAC,SAAkB;QAC7D,IAAI,IAAI,CAAC,oCAAoC,KAAK,SAAS,EAAE;YACzD,OAAO;SACV;QAED,IAAI,CAAC,oCAAoC,GAAG,SAAS,CAAC;QAEtD,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAKD;;;;OAIG;IACH,IAAW,8BAA8B;QACrC,OAAO,IAAI,CAAC,+BAA+B,CAAC;IAChD,CAAC;IAED,IAAW,8BAA8B,CAAC,MAAc;QACpD,IAAI,IAAI,CAAC,+BAA+B,KAAK,MAAM,EAAE;YACjD,OAAO;SACV;QAED,IAAI,CAAC,+BAA+B,GAAG,MAAM,CAAC;QAC9C,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAKD;;OAEG;IACH,IAAW,wCAAwC;QAC/C,OAAO,IAAI,CAAC,yCAAyC,CAAC;IAC1D,CAAC;IAED,IAAW,wCAAwC,CAAC,KAAc;QAC9D,IAAI,IAAI,CAAC,yCAAyC,KAAK,KAAK,EAAE;YAC1D,OAAO;SACV;QAED,IAAI,CAAC,yCAAyC,GAAG,KAAK,CAAC;QAEvD,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,gCAAgC,GAAG,KAAK,CAAC;SAChE;IACL,CAAC;IAKD;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,SAAS,CAAC,KAAc;QAC/B,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE;YAC3B,OAAO;SACV;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,IAAI,CAAC,KAAK,EAAE;YACR,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;gBACxB,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACxG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;aACrD;SACJ;aAAM,IAAI,KAAK,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChB,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;oBACxB,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,6BAA6B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACzG;aACJ;iBAAM;gBACH,IAAI,CAAC,cAAc,EAAE,CAAC;aACzB;SACJ;IACL,CAAC;IAKD;;;OAGG;IACH,IAAW,+BAA+B;QACtC,OAAO,IAAI,CAAC,gCAAgC,CAAC;IACjD,CAAC;IAED,IAAW,+BAA+B,CAAC,UAAmB;QAC1D,IAAI,IAAI,CAAC,gCAAgC,KAAK,UAAU,EAAE;YACtD,OAAO;SACV;QAED,IAAI,CAAC,gCAAgC,GAAG,UAAU,CAAC;QAEnD,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAKD;;;OAGG;IACH,IAAW,0BAA0B;QACjC,OAAO,IAAI,CAAC,2BAA2B,CAAC;IAC5C,CAAC;IAED,IAAW,0BAA0B,CAAC,UAAmB;QACrD,IAAI,IAAI,CAAC,2BAA2B,KAAK,UAAU,EAAE;YACjD,OAAO;SACV;QAED,IAAI,CAAC,2BAA2B,GAAG,UAAU,CAAC;QAE9C,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAKD;;;;;;;;;OASG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,IAAW,KAAK,CAAC,KAAc;QAC3B,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;YACvB,OAAO;SACV;QAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAGD,IAAY,uBAAuB;QAC/B,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;IAC9C,CAAC;IAED,IAAY,gBAAgB;QACxB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;IACvC,CAAC;IAaD;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC;QAE/C,OAAO,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,UAAU,CAAC;IACxD,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,KAAY,EAAE,OAAkB,EAAE,mBAAmB,GAAG,KAAK,EAAE,WAAW,GAAG,SAAS,CAAC,yBAAyB;QACtI,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;QAjlBnC;;WAEG;QACI,oBAAe,GAAW,iBAAiB,CAAC;QACnD;;WAEG;QACI,wBAAmB,GAAW,qBAAqB,CAAC;QAC3D;;WAEG;QACI,2BAAsB,GAAW,wBAAwB,CAAC;QAEzD,aAAQ,GAAG,CAAC,CAAC;QAkBrB;;;WAGG;QAEI,gBAAW,GAAG,MAAM,CAAC;QAC5B;;;WAGG;QAEI,SAAI,GAAG,GAAG,CAAC;QAClB;;;;WAIG;QAEI,cAAS,GAAG,GAAG,CAAC;QACvB;;WAEG;QAEI,aAAQ,GAAG,CAAC,CAAC;QACpB;;WAEG;QAEI,sCAAiC,GAAG,CAAC,CAAC;QAC7C;;;WAGG;QAEI,aAAQ,GAAG,MAAM,CAAC;QACzB;;;;WAIG;QAEI,oBAAe,GAAG,GAAG,CAAC;QAC7B;;;WAGG;QAEI,yBAAoB,GAAG,CAAC,CAAC;QAGxB,2BAAsB,GAAG,IAAI,CAAC;QAwB9B,mBAAc,GAAG,CAAC,CAAC;QAsBnB,4BAAuB,GAAG,IAAI,CAAC;QA8B/B,oBAAe,GAAG,CAAC,CAAC;QAoBpB,6BAAwB,GAAG,KAAK,CAAC;QAoCjC,+BAA0B,GAAG,KAAK,CAAC;QAgBnC,4BAAuB,GAAG,IAAI,CAAC;QAkB/B,mCAA8B,GAAG,IAAI,CAAC;QAkBtC,qCAAgC,GAAG,IAAI,CAAC;QAkBxC,2BAAsB,GAAG,KAAK,CAAC;QAkB/B,iCAA4B,GAAG,KAAK,CAAC;QAkBrC,mBAAc,GAAG,IAAI,CAAC;QAmBtB,gBAAW,GAAG,KAAK,CAAC;QAmBpB,yCAAoC,GAAG,KAAK,CAAC;QA+B7C,oCAA+B,GAAG,CAAC,CAAC;QAqBpC,8CAAyC,GAAG,IAAI,CAAC;QAsBjD,eAAU,GAAG,IAAI,CAAC;QAiClB,qCAAgC,GAAG,IAAI,CAAC;QAqBxC,gCAA2B,GAAG,IAAI,CAAC;QAqBnC,WAAM,GAAG,KAAK,CAAC;QAkCf,yBAAoB,GAAG,KAAK,CAAC;QAkB7B,aAAQ,GAAG,KAAK,CAAC;QACjB,yBAAoB,GAAkB,EAAE,CAAC;QAoC7C,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAElD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QAEhD,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,KAAK,CAAC,gCAAgC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAEzD,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,MAAM,sBAAsB,GAAG,KAAK,CAAC,4BAA4B,EAAE,CAAC;gBACpE,IAAI,sBAAsB,EAAE;oBACxB,sBAAsB,CAAC,kBAAkB,GAAG,IAAI,CAAC;oBACjD,sBAAsB,CAAC,+BAA+B,GAAG,IAAI,CAAC;iBACjE;aACJ;iBAAM;gBACH,MAAM,eAAe,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC;gBACtD,IAAI,eAAe,EAAE;oBACjB,eAAe,CAAC,+BAA+B,GAAG,IAAI,CAAC;oBACvD,eAAe,CAAC,WAAW,EAAE,CAAC;iBACjC;aACJ;YAED,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,MAAc;QAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,MAAc;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3C,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,gCAAyC,KAAK;QACzD,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,6BAA6B,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE,CAAC;SAC/C;QAED,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAExG,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAEO,eAAe;QACnB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE9C,IAAI,WAAW,GAAU,EAAE,KAAK,EAAE,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,eAAe,EAAE,EAAE,CAAC;QAE9F,IAAI,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,oBAAoB,EAAE,KAAK,IAAI,CAAC,eAAe,EAAE;YAC9F,MAAM,YAAY,GAAG,eAAe,CAAC,eAAe,EAAE,CAAC;YAEvD,IAAI,YAAY,IAAI,YAAY,CAAC,QAAQ,EAAE;gBACvC,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;aACjH;SACJ;aAAM,IAAI,IAAI,CAAC,eAAe,EAAE,YAAY,EAAE;YAC3C,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,KAAK,CAAC;YAC5D,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,MAAM,CAAC;SACjE;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,oBAAoB;QACxB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvD,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACzC;QACD,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;SACxD;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;SACzD;QACD,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YACjD,IAAI,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE;gBAC1C,OAAO,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;aAC7D;YACD,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE;gBACrC,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;aAC9D;SACJ;QACD,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAC9C;QACD,IAAI,IAAI,CAAC,oCAAoC,EAAE;YAC3C,OAAO,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;SAC3D;QACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;SACxD;QACD,IAAI,IAAI,CAAC,8BAA8B,EAAE;YACrC,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;SAC/D;QACD,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACvC,OAAO,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;SACpE;QACD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;SACvD;QACD,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,OAAO,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;SAC7D;QACD,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;SACtD;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YACjB,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;SACxC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;SAC5C;QACD,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACvC,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;SACpD;QACD,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;SACrD;QACD,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;SAClD;QACD,IAAI,IAAI,CAAC,sBAAsB,KAAK,CAAC,EAAE;YACnC,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;SACzD;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,2BAA2B,IAAI,IAAI,CAAC,gBAAgB,EAAE,2BAA2B,EAAE;YACjH,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;SACvD;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,kBAAkB,EAAE;YAClD,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,eAAe,EAAE,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,cAAc;QAClB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YACxB,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxG,mDAAmD;YACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;SACrD;QACD,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,IAAI,IAAI,CAAC,oCAAoC,EAAE;YAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;YAElC,IAAI,MAAM,EAAE;gBACR,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC;gBACnC,IAAI,CAAC,cAAc,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,4BAA4B,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;gBACpJ,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,wEAAwE;gBAChH,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,IAAI,CAAC,CAAC,uCAAuC;gBAClF,IAAI,CAAC,cAAc,CAAC,gCAAgC,GAAG,IAAI,CAAC,yCAAyC,CAAC;gBAEtG,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAE5B,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC;aACtE;SACJ;QAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,MAAM,EACN,IAAI,CAAC,eAAe,EACpB,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,eAAe,CAAC;QAChC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YACjB,IAAI,CAAC,mCAAmC,EAAE,CAAC;YAC3C,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,MAAM,EACN,IAAI,CAAC,mBAAmB,EACxB,GAAG,EAAE;gBACD,OAAO,CAAC,IAAI,CAAC,iBAAkB,EAAE,IAAI,CAAC,iBAAkB,CAAC,CAAC;YAC9D,CAAC,EACD,IAAI,CACP,CACJ,CAAC;YACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,MAAM,EACN,IAAI,CAAC,sBAAsB,EAC3B,GAAG,EAAE;gBACD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACzC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;SACL;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YACxB,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,6BAA6B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SACzG;IACL,CAAC;IAEO,oBAAoB;QACxB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,CAAC;QAEtE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,+BAA+B,GAAG,CAAC,CAAC,CAAC,CAAC;QACzF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,+BAA+B,GAAG,CAAC,CAAC,CAAC,CAAC;QAE3F,IAAI,iBAAiB,CAAC,KAAK,KAAK,KAAK,IAAI,iBAAiB,CAAC,MAAM,KAAK,MAAM,EAAE;YAC1E,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;SAC/D;IACL,CAAC;IAEO,qBAAqB;QACzB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC3G,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;oBACZ,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;iBAChE;aACJ;YACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACjC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,CAAC;SAC/C;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAEO,qBAAqB;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEhC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YACtC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YACxC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YACxC,IAAI,CAAC,wBAAwB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;IACzC,CAAC;IAEO,qBAAqB;QACzB,IAAI,CAAC,eAAe,GAAG,IAAI,WAAW,CAClC,KAAK,EACL,wBAAwB,EACxB;YACI,YAAY;YACZ,qBAAqB;YACrB,MAAM;YACN,SAAS;YACT,WAAW;YACX,mCAAmC;YACnC,UAAU;YACV,UAAU;YACV,UAAU;YACV,iBAAiB;YACjB,iBAAiB;YACjB,YAAY;YACZ,aAAa;YACb,sBAAsB;YACtB,qBAAqB;YACrB,iBAAiB;YACjB,gBAAgB;YAChB,uBAAuB;SAC1B,EACD,CAAC,gBAAgB,EAAE,eAAe,EAAE,qBAAqB,EAAE,cAAc,EAAE,gBAAgB,EAAE,kBAAkB,CAAC,EAChH,GAAG,EACH,IAAI,EACJ,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EACvB,KAAK,EACL,EAAE,EACF,IAAI,CAAC,YAAY,CACpB,CAAC;QAEF,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YAC9C,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,MAAM,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC;YAC5D,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAE9C,IAAI,CAAC,eAAe,IAAI,CAAC,sBAAsB,EAAE;gBAC7C,OAAO;aACV;YAED,IAAI,sBAAsB,EAAE;gBACxB,MAAM,cAAc,GAAG,sBAAsB,CAAC,eAAe,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;gBAEhH,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,sBAAsB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpF,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,sBAAsB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;gBACvG,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,sBAAsB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;aACtF;iBAAM,IAAI,eAAe,EAAE;gBACxB,MAAM,UAAU,GAAG,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;gBAClF,MAAM,cAAc,GAAG,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;gBAC7F,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;gBAEpF,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,eAAe,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC5F,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,eAAe,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC1F,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,eAAe,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;aACxG;YAED,IAAI,IAAI,CAAC,oCAAoC,IAAI,IAAI,CAAC,cAAc,EAAE;gBAClE,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC;gBACzE,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,+BAA+B,GAAG,CAAC,CAAC,CAAC;aAC/E;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YACxC,IAAI,CAAC,MAAM,EAAE;gBACT,OAAO;aACV;YAED,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;YAC1C,MAAM,gBAAgB,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC;YAEtD,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAE7C,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACrC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,mCAAmC,EAAE,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC7F,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,CAAC,QAAQ,CAAC,sBAAsB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACnE,MAAM,CAAC,QAAQ,CAAC,uBAAuB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAEtE,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAE3C,MAAM,CAAC,YAAY,CAAC,WAAY,CAAC,KAAK,EAAE,WAAY,CAAC,MAAM,EAAE,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtF,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAEzG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAE/E,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAE1D,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAE9D,IAAI,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE;oBAC1C,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;oBACvF,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;iBAClF;aACJ;QACL,CAAC,CAAC;QACF,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5C,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,IAAI,CAAC,eAAe,CAAC,2BAA2B,GAAG,IAAI,oCAAoC,EAAE,CAAC;SACjG;IACL,CAAC;IAEO,mCAAmC;QACvC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,CAAC,iBAAiB,GAAG,IAAI,WAAW,CACpC,UAAU,EACV,4BAA4B,EAC5B,CAAC,kBAAkB,CAAC,EACpB,CAAC,gBAAgB,CAAC,EAClB,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACnD,IAAI,EACJ,SAAS,CAAC,6BAA6B,EACvC,MAAM,EACN,KAAK,EACL,EAAE,EACF,IAAI,CAAC,YAAY,CACpB,CAAC;QACF,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,KAAK,CAAC;QAEzC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACpD,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,YAAY,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,cAAc,EAAE,CAAC;YAErG,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,IAAI,CAAC,uBAAuB,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,GAAG,IAAI,WAAW,CACpC,UAAU,EACV,4BAA4B,EAC5B,CAAC,kBAAkB,CAAC,EACpB,CAAC,gBAAgB,CAAC,EAClB,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACpD,IAAI,EACJ,SAAS,CAAC,6BAA6B,EACvC,MAAM,EACN,KAAK,EACL,EAAE,EACF,IAAI,CAAC,YAAY,CACpB,CAAC;QACF,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,KAAK,CAAC;QAEzC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACpD,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,YAAY,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,EAAE,CAAC;YAExG,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC,EAAE,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,CAAC,UAAU,EAAE,mCAAmC,EAAE,uBAAuB,CAAC,CAAC;QAChG,MAAM,YAAY,GAAG,CAAC,gBAAgB,EAAE,aAAa,EAAE,qBAAqB,CAAC,CAAC;QAE9E,IAAI,OAAO,GAAG,EAAE,CAAC;QAEjB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO,IAAI,4BAA4B,CAAC;SAC3C;QACD,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACvC,OAAO,IAAI,oCAAoC,CAAC;SACnD;QACD,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClC,OAAO,IAAI,qCAAqC,CAAC;SACpD;QACD,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,IAAI,kCAAkC,CAAC;YAE9C,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,qBAAqB,CAAC,CAAC;YACvD,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;SACtD;QACD,IAAI,IAAI,CAAC,sBAAsB,KAAK,CAAC,EAAE;YACnC,OAAO,IAAI,uCAAuC,CAAC;SACtD;QAED,IAAI,CAAC,wBAAwB,GAAG,IAAI,WAAW,CAC3C,iBAAiB,EACjB,oCAAoC,EACpC,YAAY,EACZ,YAAY,EACZ,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACpD,IAAI,EACJ,SAAS,CAAC,4BAA4B,EACtC,MAAM,EACN,KAAK,EACL,OAAO,EACP,IAAI,CAAC,YAAY,CACpB,CAAC;QACF,IAAI,CAAC,wBAAwB,CAAC,SAAS,GAAG,KAAK,CAAC;QAEhD,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YAC3D,MAAM,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC;YAC5D,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAE9C,IAAI,CAAC,eAAe,IAAI,CAAC,sBAAsB,EAAE;gBAC7C,OAAO;aACV;YAED,IAAI,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,oBAAoB,EAAE,KAAK,IAAI,CAAC,eAAe,EAAE;gBAC9F,MAAM,YAAY,GAAG,eAAe,CAAC,eAAe,EAAE,CAAC;gBAEvD,IAAI,YAAY,IAAI,YAAY,CAAC,QAAQ,EAAE;oBACvC,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC;iBAC3H;aACJ;iBAAM;gBACH,MAAM,CAAC,yBAAyB,CAAC,aAAa,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;aACzE;YAED,IAAI,sBAAsB,EAAE;gBACxB,MAAM,cAAc,GAAG,sBAAsB,CAAC,eAAe,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;gBAChH,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,sBAAsB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;gBACvG,IAAI,IAAI,CAAC,UAAU,EAAE;oBACjB,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,sBAAsB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpF,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,sBAAsB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;iBACtF;aACJ;iBAAM,IAAI,eAAe,EAAE;gBACxB,MAAM,cAAc,GAAG,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;gBAC7F,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,eAAe,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;gBACrG,IAAI,IAAI,CAAC,UAAU,EAAE;oBACjB,MAAM,UAAU,GAAG,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;oBAClF,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;oBAEpF,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,eAAe,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;oBAC5F,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,eAAe,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;iBAC7F;aACJ;YAED,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,mCAAmC,EAAE,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC7F,MAAM,CAAC,QAAQ,CAAC,uBAAuB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAEtE,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBACxC,IAAI,MAAM,EAAE;oBACR,MAAM,gBAAgB,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC;oBAEtD,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBAEnD,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;oBACjD,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;iBACjE;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,mBAAmB,CAAC,UAAU,GAAG,sBAAsB,CAAC;QAExD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,OAAO,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,oBAAoB,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACjI,CAAC;CACJ;AArnCG;IADC,SAAS,EAAE;mDAGX;AAOM;IADN,SAAS,EAAE;yDACgB;AAMrB;IADN,SAAS,EAAE;kDACM;AAOX;IADN,SAAS,EAAE;uDACW;AAKhB;IADN,SAAS,EAAE;sDACQ;AAKb;IADN,SAAS,EAAE;+EACiC;AAMtC;IADN,SAAS,EAAE;sDACa;AAOlB;IADN,SAAS,EAAE;6DACiB;AAMtB;IADN,SAAS,EAAE;kEACoB;AAGxB;IADP,SAAS,EAAE;oEAC0B;AAwB9B;IADP,SAAS,CAAC,gBAAgB,CAAC;4DACD;AAQ3B;IADC,SAAS,EAAE;yDAGX;AAYO;IADP,SAAS,CAAC,wBAAwB,CAAC;qEACG;AA8B/B;IADP,SAAS,CAAC,gBAAgB,CAAC;6DACA;AAoBpB;IADP,SAAS,CAAC,yBAAyB,CAAC;sEACI;AAqBjC;IADP,SAAS,CAAC,oBAAoB,CAAC;iEACmB;AAe3C;IADP,SAAS,CAAC,2BAA2B,CAAC;wEACI;AAgBnC;IADP,SAAS,CAAC,wBAAwB,CAAC;qEACG;AAkB/B;IADP,SAAS,CAAC,+BAA+B,CAAC;4EACG;AAkBtC;IADP,SAAS,CAAC,iCAAiC,CAAC;8EACG;AAkBxC;IADP,SAAS,CAAC,uBAAuB,CAAC;oEACI;AAkB/B;IADP,SAAS,CAAC,6BAA6B,CAAC;0EACI;AAkBrC;IADP,SAAS,CAAC,eAAe,CAAC;4DACG;AAmBtB;IADP,SAAS,CAAC,YAAY,CAAC;yDACI;AAmBpB;IADP,SAAS,CAAC,qCAAqC,CAAC;kFACI;AA+B7C;IADP,SAAS,CAAC,gCAAgC,CAAC;6EACA;AAqBpC;IADP,SAAS,CAAC,0CAA0C,CAAC;uFACG;AAsBjD;IADP,SAAS,CAAC,WAAW,CAAC;wDACG;AAiClB;IADP,SAAS,CAAC,iCAAiC,CAAC;8EACG;AAqBxC;IADP,SAAS,CAAC,4BAA4B,CAAC;yEACG;AAqBnC;IADP,SAAS,CAAC,OAAO,CAAC;oDACI;AAwpB3B,aAAa,CAAC,8BAA8B,EAAE,oBAAoB,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { serialize } from \"../../../Misc/decorators\";\r\nimport { SerializationHelper } from \"../../../Misc/decorators.serialization\";\r\nimport { Vector3, Matrix, Quaternion, TmpVectors } from \"../../../Maths/math.vector\";\r\nimport type { Camera } from \"../../../Cameras/camera\";\r\nimport type { Effect } from \"../../../Materials/effect\";\r\nimport { PostProcess } from \"../../postProcess\";\r\nimport { PostProcessRenderPipeline } from \"../postProcessRenderPipeline\";\r\nimport { PostProcessRenderEffect } from \"../postProcessRenderEffect\";\r\nimport type { Scene } from \"../../../scene\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport { ScreenSpaceReflections2Configuration } from \"../../../Rendering/screenSpaceReflections2Configuration\";\r\nimport type { PrePassRenderer } from \"../../../Rendering/prePassRenderer\";\r\nimport { GeometryBufferRenderer } from \"../../../Rendering/geometryBufferRenderer\";\r\nimport { Constants } from \"../../../Engines/constants\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport type { CubeTexture } from \"../../../Materials/Textures/cubeTexture\";\r\nimport { DepthRenderer } from \"../../../Rendering/depthRenderer\";\r\nimport type { ISize } from \"../../../Maths/math.size\";\r\n\r\nimport \"../postProcessRenderPipelineManagerSceneComponent\";\r\n\r\nimport \"../../../Shaders/screenSpaceReflection2.fragment\";\r\nimport \"../../../Shaders/screenSpaceReflection2Blur.fragment\";\r\nimport \"../../../Shaders/screenSpaceReflection2BlurCombiner.fragment\";\r\n\r\nconst trs = Matrix.Compose(new Vector3(0.5, 0.5, 0.5), Quaternion.Identity(), new Vector3(0.5, 0.5, 0.5));\r\nconst trsWebGPU = Matrix.Compose(new Vector3(0.5, 0.5, 1), Quaternion.Identity(), new Vector3(0.5, 0.5, 0));\r\n\r\n/**\r\n * Render pipeline to produce Screen Space Reflections (SSR) effect\r\n *\r\n * References:\r\n *   Screen Space Ray Tracing:\r\n *     - http://casual-effects.blogspot.com/2014/08/screen-space-ray-tracing.html\r\n *     - https://sourceforge.net/p/g3d/code/HEAD/tree/G3D10/data-files/shader/screenSpaceRayTrace.glsl\r\n *     - https://github.com/kode80/kode80SSR\r\n *   SSR:\r\n *     - general tips: https://sakibsaikia.github.io/graphics/2016/12/26/Screen-Space-Reflection-in-Killing-Floor-2.html\r\n *     - computation of blur radius from roughness and distance: https://github.com/godotengine/godot/blob/master/servers/rendering/renderer_rd/shaders/effects/screen_space_reflection.glsl\r\n *     - blur and usage of back depth buffer: https://github.com/kode80/kode80SSR\r\n */\r\nexport class SSRRenderingPipeline extends PostProcessRenderPipeline {\r\n    /**\r\n     * The SSR PostProcess effect id in the pipeline\r\n     */\r\n    public SSRRenderEffect: string = \"SSRRenderEffect\";\r\n    /**\r\n     * The blur PostProcess effect id in the pipeline\r\n     */\r\n    public SSRBlurRenderEffect: string = \"SSRBlurRenderEffect\";\r\n    /**\r\n     * The PostProcess effect id in the pipeline that combines the SSR-Blur output with the original scene color\r\n     */\r\n    public SSRCombineRenderEffect: string = \"SSRCombineRenderEffect\";\r\n\r\n    private _samples = 1;\r\n    /**\r\n     * MSAA sample count, setting this to 4 will provide 4x anti aliasing. (default: 1)\r\n     */\r\n    public set samples(sampleCount: number) {\r\n        if (this._samples === sampleCount) {\r\n            return;\r\n        }\r\n        this._samples = sampleCount;\r\n\r\n        this._buildPipeline();\r\n    }\r\n\r\n    @serialize()\r\n    public get samples(): number {\r\n        return this._samples;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the maxDistance used to define how far we look for reflection during the ray-marching on the reflected ray (default: 1000).\r\n     * Note that this value is a view (camera) space distance (not pixels!).\r\n     */\r\n    @serialize()\r\n    public maxDistance = 1000.0;\r\n    /**\r\n     * Gets or sets the step size used to iterate until the effect finds the color of the reflection's pixel. Should be an integer \\>= 1 as it is the number of pixels we advance at each step (default: 1).\r\n     * Use higher values to improve performances (but at the expense of quality).\r\n     */\r\n    @serialize()\r\n    public step = 1.0;\r\n    /**\r\n     * Gets or sets the thickness value used as tolerance when computing the intersection between the reflected ray and the scene (default: 0.5).\r\n     * If setting \"enableAutomaticThicknessComputation\" to true, you can use lower values for \"thickness\" (even 0), as the geometry thickness\r\n     * is automatically computed thank to the regular depth buffer + the backface depth buffer\r\n     */\r\n    @serialize()\r\n    public thickness = 0.5;\r\n    /**\r\n     * Gets or sets the current reflection strength. 1.0 is an ideal value but can be increased/decreased for particular results (default: 1).\r\n     */\r\n    @serialize()\r\n    public strength = 1;\r\n    /**\r\n     * Gets or sets the falloff exponent used to compute the reflection strength. Higher values lead to fainter reflections (default: 1).\r\n     */\r\n    @serialize()\r\n    public reflectionSpecularFalloffExponent = 1;\r\n    /**\r\n     * Maximum number of steps during the ray marching process after which we consider an intersection could not be found (default: 1000).\r\n     * Should be an integer value.\r\n     */\r\n    @serialize()\r\n    public maxSteps = 1000.0;\r\n    /**\r\n     * Gets or sets the factor applied when computing roughness. Default value is 0.2.\r\n     * When blurring based on roughness is enabled (meaning blurDispersionStrength \\> 0), roughnessFactor is used as a global roughness factor applied on all objects.\r\n     * If you want to disable this global roughness set it to 0.\r\n     */\r\n    @serialize()\r\n    public roughnessFactor = 0.2;\r\n    /**\r\n     * Number of steps to skip at start when marching the ray to avoid self collisions (default: 1)\r\n     * 1 should normally be a good value, depending on the scene you may need to use a higher value (2 or 3)\r\n     */\r\n    @serialize()\r\n    public selfCollisionNumSkip = 1;\r\n\r\n    @serialize()\r\n    private _reflectivityThreshold = 0.04;\r\n\r\n    /**\r\n     * Gets or sets the minimum value for one of the reflectivity component of the material to consider it for SSR (default: 0.04).\r\n     * If all r/g/b components of the reflectivity is below or equal this value, the pixel will not be considered reflective and SSR won't be applied.\r\n     */\r\n    public get reflectivityThreshold() {\r\n        return this._reflectivityThreshold;\r\n    }\r\n\r\n    public set reflectivityThreshold(threshold: number) {\r\n        if (threshold === this._reflectivityThreshold) {\r\n            return;\r\n        }\r\n\r\n        if ((threshold === 0 && this._reflectivityThreshold !== 0) || (threshold !== 0 && this._reflectivityThreshold === 0)) {\r\n            this._reflectivityThreshold = threshold;\r\n            this._buildPipeline();\r\n        } else {\r\n            this._reflectivityThreshold = threshold;\r\n        }\r\n    }\r\n\r\n    @serialize(\"_ssrDownsample\")\r\n    private _ssrDownsample = 0;\r\n\r\n    /**\r\n     * Gets or sets the downsample factor used to reduce the size of the texture used to compute the SSR contribution (default: 0).\r\n     * Use 0 to render the SSR contribution at full resolution, 1 to render at half resolution, 2 to render at 1/3 resolution, etc.\r\n     * Note that it is used only when blurring is enabled (blurDispersionStrength \\> 0), because in that mode the SSR contribution is generated in a separate texture.\r\n     */\r\n    @serialize()\r\n    public get ssrDownsample() {\r\n        return this._ssrDownsample;\r\n    }\r\n\r\n    public set ssrDownsample(downsample: number) {\r\n        if (downsample === this._ssrDownsample) {\r\n            return;\r\n        }\r\n\r\n        this._ssrDownsample = downsample;\r\n        this._buildPipeline();\r\n    }\r\n\r\n    @serialize(\"blurDispersionStrength\")\r\n    private _blurDispersionStrength = 0.03;\r\n\r\n    /**\r\n     * Gets or sets the blur dispersion strength. Set this value to 0 to disable blurring (default: 0.05)\r\n     * The reflections are blurred based on the roughness of the surface and the distance between the pixel shaded and the reflected pixel: the higher the distance the more blurry the reflection is.\r\n     * blurDispersionStrength allows to increase or decrease this effect.\r\n     */\r\n    public get blurDispersionStrength() {\r\n        return this._blurDispersionStrength;\r\n    }\r\n\r\n    public set blurDispersionStrength(strength: number) {\r\n        if (strength === this._blurDispersionStrength) {\r\n            return;\r\n        }\r\n\r\n        const rebuild = (strength === 0 && this._blurDispersionStrength !== 0) || (strength !== 0 && this._blurDispersionStrength === 0);\r\n\r\n        this._blurDispersionStrength = strength;\r\n\r\n        if (rebuild) {\r\n            this._buildPipeline();\r\n        }\r\n    }\r\n\r\n    private _useBlur() {\r\n        return this._blurDispersionStrength > 0;\r\n    }\r\n\r\n    @serialize(\"blurDownsample\")\r\n    private _blurDownsample = 0;\r\n\r\n    /**\r\n     * Gets or sets the downsample factor used to reduce the size of the textures used to blur the reflection effect (default: 0).\r\n     * Use 0 to blur at full resolution, 1 to render at half resolution, 2 to render at 1/3 resolution, etc.\r\n     */\r\n    public get blurDownsample() {\r\n        return this._blurDownsample;\r\n    }\r\n\r\n    public set blurDownsample(downsample: number) {\r\n        if (downsample === this._blurDownsample) {\r\n            return;\r\n        }\r\n\r\n        this._blurDownsample = downsample;\r\n        this._buildPipeline();\r\n    }\r\n\r\n    @serialize(\"enableSmoothReflections\")\r\n    private _enableSmoothReflections = false;\r\n\r\n    /**\r\n     * Gets or sets whether or not smoothing reflections is enabled (default: false)\r\n     * Enabling smoothing will require more GPU power.\r\n     * Note that this setting has no effect if step = 1: it's only used if step \\> 1.\r\n     */\r\n    public get enableSmoothReflections(): boolean {\r\n        return this._enableSmoothReflections;\r\n    }\r\n\r\n    public set enableSmoothReflections(enabled: boolean) {\r\n        if (enabled === this._enableSmoothReflections) {\r\n            return;\r\n        }\r\n\r\n        this._enableSmoothReflections = enabled;\r\n        this._updateEffectDefines();\r\n    }\r\n\r\n    @serialize(\"environmentTexture\")\r\n    private _environmentTexture: Nullable<CubeTexture>;\r\n\r\n    /**\r\n     * Gets or sets the environment cube texture used to define the reflection when the reflected rays of SSR leave the view space or when the maxDistance/maxSteps is reached.\r\n     */\r\n    public get environmentTexture() {\r\n        return this._environmentTexture;\r\n    }\r\n\r\n    public set environmentTexture(texture: Nullable<CubeTexture>) {\r\n        this._environmentTexture = texture;\r\n        this._updateEffectDefines();\r\n    }\r\n\r\n    @serialize(\"environmentTextureIsProbe\")\r\n    private _environmentTextureIsProbe = false;\r\n\r\n    /**\r\n     * Gets or sets the boolean defining if the environment texture is a standard cubemap (false) or a probe (true). Default value is false.\r\n     * Note: a probe cube texture is treated differently than an ordinary cube texture because the Y axis is reversed.\r\n     */\r\n    public get environmentTextureIsProbe(): boolean {\r\n        return this._environmentTextureIsProbe;\r\n    }\r\n\r\n    public set environmentTextureIsProbe(isProbe: boolean) {\r\n        this._environmentTextureIsProbe = isProbe;\r\n        this._updateEffectDefines();\r\n    }\r\n\r\n    @serialize(\"attenuateScreenBorders\")\r\n    private _attenuateScreenBorders = true;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the reflections should be attenuated at the screen borders (default: true).\r\n     */\r\n    public get attenuateScreenBorders() {\r\n        return this._attenuateScreenBorders;\r\n    }\r\n\r\n    public set attenuateScreenBorders(attenuate: boolean) {\r\n        if (this._attenuateScreenBorders === attenuate) {\r\n            return;\r\n        }\r\n        this._attenuateScreenBorders = attenuate;\r\n        this._updateEffectDefines();\r\n    }\r\n\r\n    @serialize(\"attenuateIntersectionDistance\")\r\n    private _attenuateIntersectionDistance = true;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the reflections should be attenuated according to the distance of the intersection (default: true).\r\n     */\r\n    public get attenuateIntersectionDistance() {\r\n        return this._attenuateIntersectionDistance;\r\n    }\r\n\r\n    public set attenuateIntersectionDistance(attenuate: boolean) {\r\n        if (this._attenuateIntersectionDistance === attenuate) {\r\n            return;\r\n        }\r\n        this._attenuateIntersectionDistance = attenuate;\r\n        this._updateEffectDefines();\r\n    }\r\n\r\n    @serialize(\"attenuateIntersectionIterations\")\r\n    private _attenuateIntersectionIterations = true;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the reflections should be attenuated according to the number of iterations performed to find the intersection (default: true).\r\n     */\r\n    public get attenuateIntersectionIterations() {\r\n        return this._attenuateIntersectionIterations;\r\n    }\r\n\r\n    public set attenuateIntersectionIterations(attenuate: boolean) {\r\n        if (this._attenuateIntersectionIterations === attenuate) {\r\n            return;\r\n        }\r\n        this._attenuateIntersectionIterations = attenuate;\r\n        this._updateEffectDefines();\r\n    }\r\n\r\n    @serialize(\"attenuateFacingCamera\")\r\n    private _attenuateFacingCamera = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the reflections should be attenuated when the reflection ray is facing the camera (the view direction) (default: false).\r\n     */\r\n    public get attenuateFacingCamera() {\r\n        return this._attenuateFacingCamera;\r\n    }\r\n\r\n    public set attenuateFacingCamera(attenuate: boolean) {\r\n        if (this._attenuateFacingCamera === attenuate) {\r\n            return;\r\n        }\r\n        this._attenuateFacingCamera = attenuate;\r\n        this._updateEffectDefines();\r\n    }\r\n\r\n    @serialize(\"attenuateBackfaceReflection\")\r\n    private _attenuateBackfaceReflection = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the backface reflections should be attenuated (default: false).\r\n     */\r\n    public get attenuateBackfaceReflection() {\r\n        return this._attenuateBackfaceReflection;\r\n    }\r\n\r\n    public set attenuateBackfaceReflection(attenuate: boolean) {\r\n        if (this._attenuateBackfaceReflection === attenuate) {\r\n            return;\r\n        }\r\n        this._attenuateBackfaceReflection = attenuate;\r\n        this._updateEffectDefines();\r\n    }\r\n\r\n    @serialize(\"clipToFrustum\")\r\n    private _clipToFrustum = true;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the ray should be clipped to the frustum (default: true).\r\n     * You can try to set this parameter to false to save some performances: it may produce some artefacts in some cases, but generally they won't really be visible\r\n     */\r\n    public get clipToFrustum() {\r\n        return this._clipToFrustum;\r\n    }\r\n\r\n    public set clipToFrustum(clip: boolean) {\r\n        if (this._clipToFrustum === clip) {\r\n            return;\r\n        }\r\n        this._clipToFrustum = clip;\r\n        this._updateEffectDefines();\r\n    }\r\n\r\n    @serialize(\"useFresnel\")\r\n    private _useFresnel = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating whether the blending between the current color pixel and the reflection color should be done with a Fresnel coefficient (default: false).\r\n     * It is more physically accurate to use the Fresnel coefficient (otherwise it uses the reflectivity of the material for blending), but it is also more expensive when you use blur (when blurDispersionStrength \\> 0).\r\n     */\r\n    public get useFresnel() {\r\n        return this._useFresnel;\r\n    }\r\n\r\n    public set useFresnel(fresnel: boolean) {\r\n        if (this._useFresnel === fresnel) {\r\n            return;\r\n        }\r\n        this._useFresnel = fresnel;\r\n        this._buildPipeline();\r\n    }\r\n\r\n    @serialize(\"enableAutomaticThicknessComputation\")\r\n    private _enableAutomaticThicknessComputation = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean defining if geometry thickness should be computed automatically (default: false).\r\n     * When enabled, a depth renderer is created which will render the back faces of the scene to a depth texture (meaning additional work for the GPU).\r\n     * In that mode, the \"thickness\" property is still used as an offset to compute the ray intersection, but you can typically use a much lower\r\n     * value than when enableAutomaticThicknessComputation is false (it's even possible to use a value of 0 when using low values for \"step\")\r\n     * Note that for performance reasons, this option will only apply to the first camera to which the rendering pipeline is attached!\r\n     */\r\n    public get enableAutomaticThicknessComputation(): boolean {\r\n        return this._enableAutomaticThicknessComputation;\r\n    }\r\n\r\n    public set enableAutomaticThicknessComputation(automatic: boolean) {\r\n        if (this._enableAutomaticThicknessComputation === automatic) {\r\n            return;\r\n        }\r\n\r\n        this._enableAutomaticThicknessComputation = automatic;\r\n\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * Gets the depth renderer used to render the back faces of the scene to a depth texture.\r\n     */\r\n    public get backfaceDepthRenderer(): Nullable<DepthRenderer> {\r\n        return this._depthRenderer;\r\n    }\r\n\r\n    @serialize(\"backfaceDepthTextureDownsample\")\r\n    private _backfaceDepthTextureDownsample = 0;\r\n\r\n    /**\r\n     * Gets or sets the downsample factor (default: 0) used to create the backface depth texture - used only if enableAutomaticThicknessComputation = true.\r\n     * Use 0 to render the depth at full resolution, 1 to render at half resolution, 2 to render at 1/4 resolution, etc.\r\n     * Note that you will get rendering artefacts when using a value different from 0: it's a tradeoff between image quality and performances.\r\n     */\r\n    public get backfaceDepthTextureDownsample() {\r\n        return this._backfaceDepthTextureDownsample;\r\n    }\r\n\r\n    public set backfaceDepthTextureDownsample(factor: number) {\r\n        if (this._backfaceDepthTextureDownsample === factor) {\r\n            return;\r\n        }\r\n\r\n        this._backfaceDepthTextureDownsample = factor;\r\n        this._resizeDepthRenderer();\r\n    }\r\n\r\n    @serialize(\"backfaceForceDepthWriteTransparentMeshes\")\r\n    private _backfaceForceDepthWriteTransparentMeshes = true;\r\n\r\n    /**\r\n     * Gets or sets a boolean (default: true) indicating if the depth of transparent meshes should be written to the backface depth texture (when automatic thickness computation is enabled).\r\n     */\r\n    public get backfaceForceDepthWriteTransparentMeshes() {\r\n        return this._backfaceForceDepthWriteTransparentMeshes;\r\n    }\r\n\r\n    public set backfaceForceDepthWriteTransparentMeshes(force: boolean) {\r\n        if (this._backfaceForceDepthWriteTransparentMeshes === force) {\r\n            return;\r\n        }\r\n\r\n        this._backfaceForceDepthWriteTransparentMeshes = force;\r\n\r\n        if (this._depthRenderer) {\r\n            this._depthRenderer.forceDepthWriteTransparentMeshes = force;\r\n        }\r\n    }\r\n\r\n    @serialize(\"isEnabled\")\r\n    private _isEnabled = true;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the effect is enabled (default: true).\r\n     */\r\n    public get isEnabled(): boolean {\r\n        return this._isEnabled;\r\n    }\r\n\r\n    public set isEnabled(value: boolean) {\r\n        if (this._isEnabled === value) {\r\n            return;\r\n        }\r\n\r\n        this._isEnabled = value;\r\n\r\n        if (!value) {\r\n            if (this._cameras !== null) {\r\n                this._scene.postProcessRenderPipelineManager.detachCamerasFromRenderPipeline(this._name, this._cameras);\r\n                this._cameras = this._camerasToBeAttached.slice();\r\n            }\r\n        } else if (value) {\r\n            if (!this._isDirty) {\r\n                if (this._cameras !== null) {\r\n                    this._scene.postProcessRenderPipelineManager.attachCamerasToRenderPipeline(this._name, this._cameras);\r\n                }\r\n            } else {\r\n                this._buildPipeline();\r\n            }\r\n        }\r\n    }\r\n\r\n    @serialize(\"inputTextureColorIsInGammaSpace\")\r\n    private _inputTextureColorIsInGammaSpace = true;\r\n\r\n    /**\r\n     * Gets or sets a boolean defining if the input color texture is in gamma space (default: true)\r\n     * The SSR effect works in linear space, so if the input texture is in gamma space, we must convert the texture to linear space before applying the effect\r\n     */\r\n    public get inputTextureColorIsInGammaSpace(): boolean {\r\n        return this._inputTextureColorIsInGammaSpace;\r\n    }\r\n\r\n    public set inputTextureColorIsInGammaSpace(gammaSpace: boolean) {\r\n        if (this._inputTextureColorIsInGammaSpace === gammaSpace) {\r\n            return;\r\n        }\r\n\r\n        this._inputTextureColorIsInGammaSpace = gammaSpace;\r\n\r\n        this._buildPipeline();\r\n    }\r\n\r\n    @serialize(\"generateOutputInGammaSpace\")\r\n    private _generateOutputInGammaSpace = true;\r\n\r\n    /**\r\n     * Gets or sets a boolean defining if the output color texture generated by the SSR pipeline should be in gamma space (default: true)\r\n     * If you have a post-process that comes after the SSR and that post-process needs the input to be in a linear space, you must disable generateOutputInGammaSpace\r\n     */\r\n    public get generateOutputInGammaSpace(): boolean {\r\n        return this._generateOutputInGammaSpace;\r\n    }\r\n\r\n    public set generateOutputInGammaSpace(gammaSpace: boolean) {\r\n        if (this._generateOutputInGammaSpace === gammaSpace) {\r\n            return;\r\n        }\r\n\r\n        this._generateOutputInGammaSpace = gammaSpace;\r\n\r\n        this._buildPipeline();\r\n    }\r\n\r\n    @serialize(\"debug\")\r\n    private _debug = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the effect should be rendered in debug mode (default: false).\r\n     * In this mode, colors have this meaning:\r\n     *   - blue: the ray hit the max distance (we reached maxDistance)\r\n     *   - red: the ray ran out of steps (we reached maxSteps)\r\n     *   - yellow: the ray went off screen\r\n     *   - green: the ray hit a surface. The brightness of the green color is proportional to the distance between the ray origin and the intersection point: A brighter green means more computation than a darker green.\r\n     * In the first 3 cases, the final color is calculated by mixing the skybox color with the pixel color (if environmentTexture is defined), otherwise the pixel color is not modified\r\n     * You should try to get as few blue/red/yellow pixels as possible, as this means that the ray has gone further than if it had hit a surface.\r\n     */\r\n    public get debug(): boolean {\r\n        return this._debug;\r\n    }\r\n\r\n    public set debug(value: boolean) {\r\n        if (this._debug === value) {\r\n            return;\r\n        }\r\n\r\n        this._debug = value;\r\n\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * Gets the scene the effect belongs to.\r\n     * @returns the scene the effect belongs to.\r\n     */\r\n    public getScene() {\r\n        return this._scene;\r\n    }\r\n\r\n    private _forceGeometryBuffer = false;\r\n    private get _geometryBufferRenderer(): Nullable<GeometryBufferRenderer> {\r\n        if (!this._forceGeometryBuffer) {\r\n            return null;\r\n        }\r\n\r\n        return this._scene.geometryBufferRenderer;\r\n    }\r\n\r\n    private get _prePassRenderer(): Nullable<PrePassRenderer> {\r\n        if (this._forceGeometryBuffer) {\r\n            return null;\r\n        }\r\n\r\n        return this._scene.prePassRenderer;\r\n    }\r\n\r\n    private _scene: Scene;\r\n    private _isDirty = false;\r\n    private _camerasToBeAttached: Array<Camera> = [];\r\n    private _textureType: number;\r\n    private _ssrPostProcess: Nullable<PostProcess>;\r\n    private _blurPostProcessX: Nullable<PostProcess>;\r\n    private _blurPostProcessY: Nullable<PostProcess>;\r\n    private _blurCombinerPostProcess: Nullable<PostProcess>;\r\n    private _depthRenderer: Nullable<DepthRenderer>;\r\n    private _depthRendererCamera: Nullable<Camera>;\r\n\r\n    /**\r\n     * Gets active scene\r\n     */\r\n    public get scene(): Scene {\r\n        return this._scene;\r\n    }\r\n\r\n    /**\r\n     * Returns true if SSR is supported by the running hardware\r\n     */\r\n    public get isSupported(): boolean {\r\n        const caps = this._scene.getEngine().getCaps();\r\n\r\n        return caps.drawBuffersExtension && caps.texelFetch;\r\n    }\r\n\r\n    /**\r\n     * Constructor of the SSR rendering pipeline\r\n     * @param name The rendering pipeline name\r\n     * @param scene The scene linked to this pipeline\r\n     * @param cameras The array of cameras that the rendering pipeline will be attached to (default: scene.cameras)\r\n     * @param forceGeometryBuffer Set to true if you want to use the legacy geometry buffer renderer (default: false)\r\n     * @param textureType The texture type used by the different post processes created by SSR (default: Constants.TEXTURETYPE_UNSIGNED_BYTE)\r\n     */\r\n    constructor(name: string, scene: Scene, cameras?: Camera[], forceGeometryBuffer = false, textureType = Constants.TEXTURETYPE_UNSIGNED_BYTE) {\r\n        super(scene.getEngine(), name);\r\n\r\n        this._cameras = cameras || scene.cameras;\r\n        this._cameras = this._cameras.slice();\r\n        this._camerasToBeAttached = this._cameras.slice();\r\n\r\n        this._scene = scene;\r\n        this._textureType = textureType;\r\n        this._forceGeometryBuffer = forceGeometryBuffer;\r\n\r\n        if (this.isSupported) {\r\n            scene.postProcessRenderPipelineManager.addPipeline(this);\r\n\r\n            if (this._forceGeometryBuffer) {\r\n                const geometryBufferRenderer = scene.enableGeometryBufferRenderer();\r\n                if (geometryBufferRenderer) {\r\n                    geometryBufferRenderer.enableReflectivity = true;\r\n                    geometryBufferRenderer.useSpecificClearForDepthTexture = true;\r\n                }\r\n            } else {\r\n                const prePassRenderer = scene.enablePrePassRenderer();\r\n                if (prePassRenderer) {\r\n                    prePassRenderer.useSpecificClearForDepthTexture = true;\r\n                    prePassRenderer.markAsDirty();\r\n                }\r\n            }\r\n\r\n            this._buildPipeline();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the class name\r\n     * @returns \"SSRRenderingPipeline\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"SSRRenderingPipeline\";\r\n    }\r\n\r\n    /**\r\n     * Adds a camera to the pipeline\r\n     * @param camera the camera to be added\r\n     */\r\n    public addCamera(camera: Camera): void {\r\n        this._camerasToBeAttached.push(camera);\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * Removes a camera from the pipeline\r\n     * @param camera the camera to remove\r\n     */\r\n    public removeCamera(camera: Camera): void {\r\n        const index = this._camerasToBeAttached.indexOf(camera);\r\n        this._camerasToBeAttached.splice(index, 1);\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * Removes the internal pipeline assets and detaches the pipeline from the scene cameras\r\n     * @param disableGeometryBufferRenderer if the geometry buffer renderer should be disabled\r\n     */\r\n    public dispose(disableGeometryBufferRenderer: boolean = false): void {\r\n        this._disposeDepthRenderer();\r\n        this._disposePostProcesses();\r\n\r\n        if (disableGeometryBufferRenderer) {\r\n            this._scene.disableGeometryBufferRenderer();\r\n        }\r\n\r\n        this._scene.postProcessRenderPipelineManager.detachCamerasFromRenderPipeline(this._name, this._cameras);\r\n\r\n        super.dispose();\r\n    }\r\n\r\n    private _getTextureSize() {\r\n        const engine = this._scene.getEngine();\r\n        const prePassRenderer = this._prePassRenderer;\r\n\r\n        let textureSize: ISize = { width: engine.getRenderWidth(), height: engine.getRenderHeight() };\r\n\r\n        if (prePassRenderer && this._scene.activeCamera?._getFirstPostProcess() === this._ssrPostProcess) {\r\n            const renderTarget = prePassRenderer.getRenderTarget();\r\n\r\n            if (renderTarget && renderTarget.textures) {\r\n                textureSize = renderTarget.textures[prePassRenderer.getIndex(Constants.PREPASS_COLOR_TEXTURE_TYPE)].getSize();\r\n            }\r\n        } else if (this._ssrPostProcess?.inputTexture) {\r\n            textureSize.width = this._ssrPostProcess.inputTexture.width;\r\n            textureSize.height = this._ssrPostProcess.inputTexture.height;\r\n        }\r\n\r\n        return textureSize;\r\n    }\r\n\r\n    private _updateEffectDefines(): void {\r\n        const defines: string[] = [];\r\n\r\n        if (this._geometryBufferRenderer || this._prePassRenderer) {\r\n            defines.push(\"#define SSR_SUPPORTED\");\r\n        }\r\n        if (this._enableSmoothReflections) {\r\n            defines.push(\"#define SSRAYTRACE_ENABLE_REFINEMENT\");\r\n        }\r\n        if (this._scene.useRightHandedSystem) {\r\n            defines.push(\"#define SSRAYTRACE_RIGHT_HANDED_SCENE\");\r\n        }\r\n        if (this._environmentTexture) {\r\n            defines.push(\"#define SSR_USE_ENVIRONMENT_CUBE\");\r\n            if (this._environmentTexture.boundingBoxSize) {\r\n                defines.push(\"#define SSR_USE_LOCAL_REFLECTIONMAP_CUBIC\");\r\n            }\r\n            if (this._environmentTexture.gammaSpace) {\r\n                defines.push(\"#define SSR_ENVIRONMENT_CUBE_IS_GAMMASPACE\");\r\n            }\r\n        }\r\n        if (this._environmentTextureIsProbe) {\r\n            defines.push(\"#define SSR_INVERTCUBICMAP\");\r\n        }\r\n        if (this._enableAutomaticThicknessComputation) {\r\n            defines.push(\"#define SSRAYTRACE_USE_BACK_DEPTHBUFFER\");\r\n        }\r\n        if (this._attenuateScreenBorders) {\r\n            defines.push(\"#define SSR_ATTENUATE_SCREEN_BORDERS\");\r\n        }\r\n        if (this._attenuateIntersectionDistance) {\r\n            defines.push(\"#define SSR_ATTENUATE_INTERSECTION_DISTANCE\");\r\n        }\r\n        if (this._attenuateIntersectionIterations) {\r\n            defines.push(\"#define SSR_ATTENUATE_INTERSECTION_NUMITERATIONS\");\r\n        }\r\n        if (this._attenuateFacingCamera) {\r\n            defines.push(\"#define SSR_ATTENUATE_FACING_CAMERA\");\r\n        }\r\n        if (this._attenuateBackfaceReflection) {\r\n            defines.push(\"#define SSR_ATTENUATE_BACKFACE_REFLECTION\");\r\n        }\r\n        if (this._clipToFrustum) {\r\n            defines.push(\"#define SSRAYTRACE_CLIP_TO_FRUSTUM\");\r\n        }\r\n        if (this._useBlur()) {\r\n            defines.push(\"#define SSR_USE_BLUR\");\r\n        }\r\n        if (this._debug) {\r\n            defines.push(\"#define SSRAYTRACE_DEBUG\");\r\n        }\r\n        if (this._inputTextureColorIsInGammaSpace) {\r\n            defines.push(\"#define SSR_INPUT_IS_GAMMA_SPACE\");\r\n        }\r\n        if (this._generateOutputInGammaSpace) {\r\n            defines.push(\"#define SSR_OUTPUT_IS_GAMMA_SPACE\");\r\n        }\r\n        if (this._useFresnel) {\r\n            defines.push(\"#define SSR_BLEND_WITH_FRESNEL\");\r\n        }\r\n        if (this._reflectivityThreshold === 0) {\r\n            defines.push(\"#define SSR_DISABLE_REFLECTIVITY_TEST\");\r\n        }\r\n\r\n        if (this._geometryBufferRenderer?.generateNormalsInWorldSpace ?? this._prePassRenderer?.generateNormalsInWorldSpace) {\r\n            defines.push(\"#define SSR_NORMAL_IS_IN_WORLDSPACE\");\r\n        }\r\n\r\n        if (this._geometryBufferRenderer?.normalsAreUnsigned) {\r\n            defines.push(\"#define SSR_DECODE_NORMAL\");\r\n        }\r\n\r\n        this._ssrPostProcess?.updateEffect(defines.join(\"\\n\"));\r\n    }\r\n\r\n    private _buildPipeline() {\r\n        if (!this.isSupported) {\r\n            return;\r\n        }\r\n\r\n        if (!this._isEnabled) {\r\n            this._isDirty = true;\r\n            return;\r\n        }\r\n\r\n        this._isDirty = false;\r\n\r\n        const engine = this._scene.getEngine();\r\n\r\n        this._disposeDepthRenderer();\r\n        this._disposePostProcesses();\r\n        if (this._cameras !== null) {\r\n            this._scene.postProcessRenderPipelineManager.detachCamerasFromRenderPipeline(this._name, this._cameras);\r\n            // get back cameras to be used to reattach pipeline\r\n            this._cameras = this._camerasToBeAttached.slice();\r\n        }\r\n        this._reset();\r\n\r\n        if (this._enableAutomaticThicknessComputation) {\r\n            const camera = this._cameras?.[0];\r\n\r\n            if (camera) {\r\n                this._depthRendererCamera = camera;\r\n                this._depthRenderer = new DepthRenderer(this._scene, undefined, undefined, undefined, Constants.TEXTURE_NEAREST_SAMPLINGMODE, true, \"SSRBackDepth\");\r\n                this._depthRenderer.clearColor.r = 1e8; // \"infinity\": put a big value because we use the storeCameraSpaceZ mode\r\n                this._depthRenderer.reverseCulling = true; // we generate depth for the back faces\r\n                this._depthRenderer.forceDepthWriteTransparentMeshes = this._backfaceForceDepthWriteTransparentMeshes;\r\n\r\n                this._resizeDepthRenderer();\r\n\r\n                camera.customRenderTargets.push(this._depthRenderer.getDepthMap());\r\n            }\r\n        }\r\n\r\n        this._createSSRPostProcess();\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                engine,\r\n                this.SSRRenderEffect,\r\n                () => {\r\n                    return this._ssrPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n\r\n        if (this._useBlur()) {\r\n            this._createBlurAndCombinerPostProcesses();\r\n            this.addEffect(\r\n                new PostProcessRenderEffect(\r\n                    engine,\r\n                    this.SSRBlurRenderEffect,\r\n                    () => {\r\n                        return [this._blurPostProcessX!, this._blurPostProcessY!];\r\n                    },\r\n                    true\r\n                )\r\n            );\r\n            this.addEffect(\r\n                new PostProcessRenderEffect(\r\n                    engine,\r\n                    this.SSRCombineRenderEffect,\r\n                    () => {\r\n                        return this._blurCombinerPostProcess;\r\n                    },\r\n                    true\r\n                )\r\n            );\r\n        }\r\n\r\n        if (this._cameras !== null) {\r\n            this._scene.postProcessRenderPipelineManager.attachCamerasToRenderPipeline(this._name, this._cameras);\r\n        }\r\n    }\r\n\r\n    private _resizeDepthRenderer() {\r\n        if (!this._depthRenderer) {\r\n            return;\r\n        }\r\n\r\n        const textureSize = this._getTextureSize();\r\n        const depthRendererSize = this._depthRenderer.getDepthMap().getSize();\r\n\r\n        const width = Math.floor(textureSize.width / (this._backfaceDepthTextureDownsample + 1));\r\n        const height = Math.floor(textureSize.height / (this._backfaceDepthTextureDownsample + 1));\r\n\r\n        if (depthRendererSize.width !== width || depthRendererSize.height !== height) {\r\n            this._depthRenderer.getDepthMap().resize({ width, height });\r\n        }\r\n    }\r\n\r\n    private _disposeDepthRenderer() {\r\n        if (this._depthRenderer) {\r\n            if (this._depthRendererCamera) {\r\n                const idx = this._depthRendererCamera.customRenderTargets.indexOf(this._depthRenderer.getDepthMap()) ?? -1;\r\n                if (idx !== -1) {\r\n                    this._depthRendererCamera.customRenderTargets.splice(idx, 1);\r\n                }\r\n            }\r\n            this._depthRendererCamera = null;\r\n            this._depthRenderer.getDepthMap().dispose();\r\n        }\r\n        this._depthRenderer = null;\r\n    }\r\n\r\n    private _disposePostProcesses(): void {\r\n        for (let i = 0; i < this._cameras.length; i++) {\r\n            const camera = this._cameras[i];\r\n\r\n            this._ssrPostProcess?.dispose(camera);\r\n            this._blurPostProcessX?.dispose(camera);\r\n            this._blurPostProcessY?.dispose(camera);\r\n            this._blurCombinerPostProcess?.dispose(camera);\r\n        }\r\n\r\n        this._ssrPostProcess = null;\r\n        this._blurPostProcessX = null;\r\n        this._blurPostProcessY = null;\r\n        this._blurCombinerPostProcess = null;\r\n    }\r\n\r\n    private _createSSRPostProcess(): void {\r\n        this._ssrPostProcess = new PostProcess(\r\n            \"ssr\",\r\n            \"screenSpaceReflection2\",\r\n            [\r\n                \"projection\",\r\n                \"invProjectionMatrix\",\r\n                \"view\",\r\n                \"invView\",\r\n                \"thickness\",\r\n                \"reflectionSpecularFalloffExponent\",\r\n                \"strength\",\r\n                \"stepSize\",\r\n                \"maxSteps\",\r\n                \"roughnessFactor\",\r\n                \"projectionPixel\",\r\n                \"nearPlaneZ\",\r\n                \"maxDistance\",\r\n                \"selfCollisionNumSkip\",\r\n                \"vReflectionPosition\",\r\n                \"vReflectionSize\",\r\n                \"backSizeFactor\",\r\n                \"reflectivityThreshold\",\r\n            ],\r\n            [\"textureSampler\", \"normalSampler\", \"reflectivitySampler\", \"depthSampler\", \"envCubeSampler\", \"backDepthSampler\"],\r\n            1.0,\r\n            null,\r\n            this._textureType,\r\n            this._scene.getEngine(),\r\n            false,\r\n            \"\",\r\n            this._textureType\r\n        );\r\n\r\n        this._updateEffectDefines();\r\n\r\n        this._ssrPostProcess.onApply = (effect: Effect) => {\r\n            this._resizeDepthRenderer();\r\n\r\n            const geometryBufferRenderer = this._geometryBufferRenderer;\r\n            const prePassRenderer = this._prePassRenderer;\r\n\r\n            if (!prePassRenderer && !geometryBufferRenderer) {\r\n                return;\r\n            }\r\n\r\n            if (geometryBufferRenderer) {\r\n                const roughnessIndex = geometryBufferRenderer.getTextureIndex(GeometryBufferRenderer.REFLECTIVITY_TEXTURE_TYPE);\r\n\r\n                effect.setTexture(\"normalSampler\", geometryBufferRenderer.getGBuffer().textures[1]);\r\n                effect.setTexture(\"reflectivitySampler\", geometryBufferRenderer.getGBuffer().textures[roughnessIndex]);\r\n                effect.setTexture(\"depthSampler\", geometryBufferRenderer.getGBuffer().textures[0]);\r\n            } else if (prePassRenderer) {\r\n                const depthIndex = prePassRenderer.getIndex(Constants.PREPASS_DEPTH_TEXTURE_TYPE);\r\n                const roughnessIndex = prePassRenderer.getIndex(Constants.PREPASS_REFLECTIVITY_TEXTURE_TYPE);\r\n                const normalIndex = prePassRenderer.getIndex(Constants.PREPASS_NORMAL_TEXTURE_TYPE);\r\n\r\n                effect.setTexture(\"normalSampler\", prePassRenderer.getRenderTarget().textures[normalIndex]);\r\n                effect.setTexture(\"depthSampler\", prePassRenderer.getRenderTarget().textures[depthIndex]);\r\n                effect.setTexture(\"reflectivitySampler\", prePassRenderer.getRenderTarget().textures[roughnessIndex]);\r\n            }\r\n\r\n            if (this._enableAutomaticThicknessComputation && this._depthRenderer) {\r\n                effect.setTexture(\"backDepthSampler\", this._depthRenderer.getDepthMap());\r\n                effect.setFloat(\"backSizeFactor\", this._backfaceDepthTextureDownsample + 1);\r\n            }\r\n\r\n            const camera = this._scene.activeCamera;\r\n            if (!camera) {\r\n                return;\r\n            }\r\n\r\n            const viewMatrix = camera.getViewMatrix();\r\n            const projectionMatrix = camera.getProjectionMatrix();\r\n\r\n            projectionMatrix.invertToRef(TmpVectors.Matrix[0]);\r\n            viewMatrix.invertToRef(TmpVectors.Matrix[1]);\r\n\r\n            effect.setMatrix(\"projection\", projectionMatrix);\r\n            effect.setMatrix(\"view\", viewMatrix);\r\n            effect.setMatrix(\"invView\", TmpVectors.Matrix[1]);\r\n            effect.setMatrix(\"invProjectionMatrix\", TmpVectors.Matrix[0]);\r\n            effect.setFloat(\"thickness\", this.thickness);\r\n            effect.setFloat(\"reflectionSpecularFalloffExponent\", this.reflectionSpecularFalloffExponent);\r\n            effect.setFloat(\"strength\", this.strength);\r\n            effect.setFloat(\"stepSize\", this.step);\r\n            effect.setFloat(\"maxSteps\", this.maxSteps);\r\n            effect.setFloat(\"roughnessFactor\", this.roughnessFactor);\r\n            effect.setFloat(\"nearPlaneZ\", camera.minZ);\r\n            effect.setFloat(\"maxDistance\", this.maxDistance);\r\n            effect.setFloat(\"selfCollisionNumSkip\", this.selfCollisionNumSkip);\r\n            effect.setFloat(\"reflectivityThreshold\", this._reflectivityThreshold);\r\n\r\n            const textureSize = this._getTextureSize();\r\n\r\n            Matrix.ScalingToRef(textureSize!.width, textureSize!.height, 1, TmpVectors.Matrix[2]);\r\n\r\n            projectionMatrix.multiplyToRef(this._scene.getEngine().isWebGPU ? trsWebGPU : trs, TmpVectors.Matrix[3]);\r\n\r\n            TmpVectors.Matrix[3].multiplyToRef(TmpVectors.Matrix[2], TmpVectors.Matrix[4]);\r\n\r\n            effect.setMatrix(\"projectionPixel\", TmpVectors.Matrix[4]);\r\n\r\n            if (this._environmentTexture) {\r\n                effect.setTexture(\"envCubeSampler\", this._environmentTexture);\r\n\r\n                if (this._environmentTexture.boundingBoxSize) {\r\n                    effect.setVector3(\"vReflectionPosition\", this._environmentTexture.boundingBoxPosition);\r\n                    effect.setVector3(\"vReflectionSize\", this._environmentTexture.boundingBoxSize);\r\n                }\r\n            }\r\n        };\r\n        this._ssrPostProcess.samples = this.samples;\r\n\r\n        if (!this._forceGeometryBuffer) {\r\n            this._ssrPostProcess._prePassEffectConfiguration = new ScreenSpaceReflections2Configuration();\r\n        }\r\n    }\r\n\r\n    private _createBlurAndCombinerPostProcesses() {\r\n        const engine = this._scene.getEngine();\r\n\r\n        this._blurPostProcessX = new PostProcess(\r\n            \"SSRblurX\",\r\n            \"screenSpaceReflection2Blur\",\r\n            [\"texelOffsetScale\"],\r\n            [\"textureSampler\"],\r\n            this._useBlur() ? 1 / (this._ssrDownsample + 1) : 1,\r\n            null,\r\n            Constants.TEXTURE_BILINEAR_SAMPLINGMODE,\r\n            engine,\r\n            false,\r\n            \"\",\r\n            this._textureType\r\n        );\r\n        this._blurPostProcessX.autoClear = false;\r\n\r\n        this._blurPostProcessX.onApplyObservable.add((effect) => {\r\n            const width = this._blurPostProcessX?.inputTexture.width ?? this._scene.getEngine().getRenderWidth();\r\n\r\n            effect.setFloat2(\"texelOffsetScale\", this._blurDispersionStrength / width, 0);\r\n        });\r\n\r\n        this._blurPostProcessY = new PostProcess(\r\n            \"SSRblurY\",\r\n            \"screenSpaceReflection2Blur\",\r\n            [\"texelOffsetScale\"],\r\n            [\"textureSampler\"],\r\n            this._useBlur() ? 1 / (this._blurDownsample + 1) : 1,\r\n            null,\r\n            Constants.TEXTURE_BILINEAR_SAMPLINGMODE,\r\n            engine,\r\n            false,\r\n            \"\",\r\n            this._textureType\r\n        );\r\n        this._blurPostProcessY.autoClear = false;\r\n\r\n        this._blurPostProcessY.onApplyObservable.add((effect) => {\r\n            const height = this._blurPostProcessY?.inputTexture.height ?? this._scene.getEngine().getRenderHeight();\r\n\r\n            effect.setFloat2(\"texelOffsetScale\", 0, this._blurDispersionStrength / height);\r\n        });\r\n\r\n        const uniformNames = [\"strength\", \"reflectionSpecularFalloffExponent\", \"reflectivityThreshold\"];\r\n        const samplerNames = [\"textureSampler\", \"mainSampler\", \"reflectivitySampler\"];\r\n\r\n        let defines = \"\";\r\n\r\n        if (this._debug) {\r\n            defines += \"#define SSRAYTRACE_DEBUG\\n\";\r\n        }\r\n        if (this._inputTextureColorIsInGammaSpace) {\r\n            defines += \"#define SSR_INPUT_IS_GAMMA_SPACE\\n\";\r\n        }\r\n        if (this._generateOutputInGammaSpace) {\r\n            defines += \"#define SSR_OUTPUT_IS_GAMMA_SPACE\\n\";\r\n        }\r\n        if (this.useFresnel) {\r\n            defines += \"#define SSR_BLEND_WITH_FRESNEL\\n\";\r\n\r\n            uniformNames.push(\"projection\", \"invProjectionMatrix\");\r\n            samplerNames.push(\"depthSampler\", \"normalSampler\");\r\n        }\r\n        if (this._reflectivityThreshold === 0) {\r\n            defines += \"#define SSR_DISABLE_REFLECTIVITY_TEST\";\r\n        }\r\n\r\n        this._blurCombinerPostProcess = new PostProcess(\r\n            \"SSRblurCombiner\",\r\n            \"screenSpaceReflection2BlurCombiner\",\r\n            uniformNames,\r\n            samplerNames,\r\n            this._useBlur() ? 1 / (this._blurDownsample + 1) : 1,\r\n            null,\r\n            Constants.TEXTURE_NEAREST_SAMPLINGMODE,\r\n            engine,\r\n            false,\r\n            defines,\r\n            this._textureType\r\n        );\r\n        this._blurCombinerPostProcess.autoClear = false;\r\n\r\n        this._blurCombinerPostProcess.onApplyObservable.add((effect) => {\r\n            const geometryBufferRenderer = this._geometryBufferRenderer;\r\n            const prePassRenderer = this._prePassRenderer;\r\n\r\n            if (!prePassRenderer && !geometryBufferRenderer) {\r\n                return;\r\n            }\r\n\r\n            if (prePassRenderer && this._scene.activeCamera?._getFirstPostProcess() === this._ssrPostProcess) {\r\n                const renderTarget = prePassRenderer.getRenderTarget();\r\n\r\n                if (renderTarget && renderTarget.textures) {\r\n                    effect.setTexture(\"mainSampler\", renderTarget.textures[prePassRenderer.getIndex(Constants.PREPASS_COLOR_TEXTURE_TYPE)]);\r\n                }\r\n            } else {\r\n                effect.setTextureFromPostProcess(\"mainSampler\", this._ssrPostProcess);\r\n            }\r\n\r\n            if (geometryBufferRenderer) {\r\n                const roughnessIndex = geometryBufferRenderer.getTextureIndex(GeometryBufferRenderer.REFLECTIVITY_TEXTURE_TYPE);\r\n                effect.setTexture(\"reflectivitySampler\", geometryBufferRenderer.getGBuffer().textures[roughnessIndex]);\r\n                if (this.useFresnel) {\r\n                    effect.setTexture(\"normalSampler\", geometryBufferRenderer.getGBuffer().textures[1]);\r\n                    effect.setTexture(\"depthSampler\", geometryBufferRenderer.getGBuffer().textures[0]);\r\n                }\r\n            } else if (prePassRenderer) {\r\n                const roughnessIndex = prePassRenderer.getIndex(Constants.PREPASS_REFLECTIVITY_TEXTURE_TYPE);\r\n                effect.setTexture(\"reflectivitySampler\", prePassRenderer.getRenderTarget().textures[roughnessIndex]);\r\n                if (this.useFresnel) {\r\n                    const depthIndex = prePassRenderer.getIndex(Constants.PREPASS_DEPTH_TEXTURE_TYPE);\r\n                    const normalIndex = prePassRenderer.getIndex(Constants.PREPASS_NORMAL_TEXTURE_TYPE);\r\n\r\n                    effect.setTexture(\"normalSampler\", prePassRenderer.getRenderTarget().textures[normalIndex]);\r\n                    effect.setTexture(\"depthSampler\", prePassRenderer.getRenderTarget().textures[depthIndex]);\r\n                }\r\n            }\r\n\r\n            effect.setFloat(\"strength\", this.strength);\r\n            effect.setFloat(\"reflectionSpecularFalloffExponent\", this.reflectionSpecularFalloffExponent);\r\n            effect.setFloat(\"reflectivityThreshold\", this._reflectivityThreshold);\r\n\r\n            if (this.useFresnel) {\r\n                const camera = this._scene.activeCamera;\r\n                if (camera) {\r\n                    const projectionMatrix = camera.getProjectionMatrix();\r\n\r\n                    projectionMatrix.invertToRef(TmpVectors.Matrix[0]);\r\n\r\n                    effect.setMatrix(\"projection\", projectionMatrix);\r\n                    effect.setMatrix(\"invProjectionMatrix\", TmpVectors.Matrix[0]);\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Serializes the rendering pipeline (Used when exporting)\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n        serializationObject.customType = \"SSRRenderingPipeline\";\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parse the serialized pipeline\r\n     * @param source Source pipeline.\r\n     * @param scene The scene to load the pipeline to.\r\n     * @param rootUrl The URL of the serialized pipeline.\r\n     * @returns An instantiated pipeline from the serialized object.\r\n     */\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): SSRRenderingPipeline {\r\n        return SerializationHelper.Parse(() => new SSRRenderingPipeline(source._name, scene, source._ratio), source, scene, rootUrl);\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SSRRenderingPipeline\", SSRRenderingPipeline);\r\n"]}