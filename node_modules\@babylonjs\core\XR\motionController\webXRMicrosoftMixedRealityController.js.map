{"version": 3, "file": "webXRMicrosoftMixedRealityController.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/motionController/webXRMicrosoftMixedRealityController.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAChF,OAAO,EAAE,4BAA4B,EAAE,MAAM,gCAAgC,CAAC;AAG9E,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C;;GAEG;AACH,MAAM,OAAO,oCAAqC,SAAQ,6BAA6B;IAsEnF,YAAY,KAAY,EAAE,aAA6C,EAAE,UAAsC;QAC3G,KAAK,CAAC,KAAK,EAAE,mBAAmB,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;QAtE/E,gIAAgI;QAC7G,aAAQ,GAAG;YAC1B,aAAa,EAAE;gBACX,aAAa,EAAE,OAAO;gBACtB,iBAAiB,EAAE,WAAW;gBAC9B,eAAe,EAAE,SAAS;aAC7B;YACD,WAAW,EAAE;gBACT,aAAa,EAAE,OAAO;gBACtB,WAAW,EAAE,KAAK;gBAClB,WAAW,EAAE,KAAK;aACrB;YACD,OAAO,EAAE;gBACL,qBAAqB,EAAE;oBACnB,YAAY,EAAE,QAAQ;oBACtB,iBAAiB,EAAE,QAAQ;oBAC3B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;iBAC5C;gBACD,qBAAqB,EAAE;oBACnB,YAAY,EAAE,OAAO;oBACrB,iBAAiB,EAAE,OAAO;oBAC1B,MAAM,EAAE,CAAC,SAAS,CAAC;iBACtB;gBACD,sBAAsB,EAAE;oBACpB,YAAY,EAAE,gBAAgB;oBAC9B,mBAAmB,EAAE,eAAe;oBACpC,kBAAkB,EAAE,OAAO,EAAE,sCAAsC;iBACtE;gBACD,wBAAwB,EAAE;oBACtB,YAAY,EAAE,kBAAkB;oBAChC,iBAAiB,EAAE,OAAO;oBAC1B,MAAM,EAAE,CAAC,SAAS,CAAC;iBACtB;aACJ;YACD,IAAI,EAAE;gBACF,sBAAsB,EAAE;oBACpB,QAAQ,EAAE;wBACN,YAAY,EAAE,kBAAkB;qBACnC;oBACD,QAAQ,EAAE;wBACN,YAAY,EAAE,kBAAkB;qBACnC;iBACJ;gBACD,wBAAwB,EAAE;oBACtB,QAAQ,EAAE;wBACN,YAAY,EAAE,cAAc;qBAC/B;oBACD,QAAQ,EAAE;wBACN,YAAY,EAAE,cAAc;qBAC/B;iBACJ;aACJ;SACJ,CAAC;QAeK,cAAS,GAAG,yBAAyB,CAAC;IAI7C,CAAC;IAES,mBAAmB;QACzB,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE;YAC5B,QAAQ,GAAG,oCAAoC,CAAC,mBAAmB,CAAC;SACvE;aAAM;YACH,+CAA+C;YAC/C,QAAQ,GAAG,oCAAoC,CAAC,oBAAoB,CAAC;SACxE;QAED,MAAM,MAAM,GAAG,SAAS,CAAC;QACzB,MAAM,IAAI,GAAG,oCAAoC,CAAC,cAAc,GAAG,MAAM,GAAG,GAAG,CAAC;QAChF,OAAO;YACH,QAAQ;YACR,IAAI;SACP,CAAC;IACN,CAAC;IAES,2BAA2B;QACjC,MAAM,SAAS,GAAG,WAAW,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC;QACpE,IAAI,CAAC,SAAS,EAAE;YACZ,MAAM,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAC;SACzF;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAES,mBAAmB,CAAC,OAAuB;QACjD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QAED,gBAAgB;QAChB,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,OAAO;aACV;YACD,IAAI,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACrB,MAAM,SAAS,GAAS,IAAI,CAAC,QAAQ,CAAC,OAAQ,CAAC,EAAE,CAAC,CAAC;gBACnD,MAAM,cAAc,GAAG,SAAS,CAAC,YAAY,CAAC;gBAC9C,IAAI,CAAC,cAAc,EAAE;oBACjB,MAAM,CAAC,GAAG,CAAC,oCAAoC,GAAG,CAAC,GAAG,qBAAqB,GAAG,EAAE,CAAC,CAAC;oBAClF,OAAO;iBACV;gBAED,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;gBACvE,IAAI,CAAC,UAAU,EAAE;oBACb,MAAM,CAAC,IAAI,CAAC,iCAAiC,GAAG,cAAc,CAAC,CAAC;oBAChE,OAAO;iBACV;gBAED,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;gBAC3G,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;gBAC/G,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;gBAEnH,IAAI,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,aAAa,EAAE;oBACzE,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;oBACnC,IAAI,IAAI,EAAE;wBACN,IAAI,CAAC,8BAA8B,CAAC,GAAG,CACnC,CAAC,SAAS,EAAE,EAAE;4BACV,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;wBACpD,CAAC,EACD,SAAS,EACT,IAAI,CACP,CAAC;qBACL;iBACJ;qBAAM;oBACH,wHAAwH;oBACxH,MAAM,CAAC,IAAI,CAAC,+CAA+C,GAAG,cAAc,CAAC,CAAC;iBACjF;aACJ;QACL,CAAC,CAAC,CAAC;QAEH,cAAc;QACd,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;gBAChB,OAAO;aACV;YAED,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAChB,OAAO;iBACV;gBACD,MAAM,OAAO,GAAS,IAAI,CAAC,QAAQ,CAAC,IAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;gBAEpD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;gBAC3E,IAAI,CAAC,QAAQ,EAAE;oBACX,MAAM,CAAC,IAAI,CAAC,+BAA+B,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;oBACpE,OAAO;iBACV;gBAED,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;gBACrG,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;gBACjG,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;gBAEjG,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;oBACzD,IAAI,IAAI,EAAE;wBACN,IAAI,CAAC,4BAA4B,CAAC,GAAG,CACjC,CAAC,UAAU,EAAE,EAAE;4BACX,MAAM,KAAK,GAAG,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;4BAC9D,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;wBAC9C,CAAC,EACD,SAAS,EACT,IAAI,CACP,CAAC;qBACL;iBACJ;qBAAM;oBACH,wHAAwH;oBACxH,MAAM,CAAC,IAAI,CAAC,6CAA6C,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;iBACrF;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAES,YAAY,CAAC,MAAsB;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7E,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC;QACjC,IAAI,QAAQ,CAAC;QACb,wFAAwF;QACxF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAEvB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YAExB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACd,iDAAiD;gBACjD,QAAQ,GAAG,IAAI,CAAC;aACnB;SACJ;QAED,IAAI,QAAQ,EAAE;YACV,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE;YAClC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;SAChF;IACL,CAAC;IAES,YAAY;QAClB,6CAA6C;IACjD,CAAC;;AA/JD;;GAEG;AACW,mDAAc,GAAW,8CAA8C,AAAzD,CAA0D;AACtF;;GAEG;AACW,wDAAmB,GAAW,UAAU,AAArB,CAAsB;AACvD;;GAEG;AACW,yDAAoB,GAAW,WAAW,AAAtB,CAAuB;AAuJ7D,uBAAuB;AACvB,4BAA4B,CAAC,kBAAkB,CAAC,uBAAuB,EAAE,CAAC,OAAsB,EAAE,KAAY,EAAE,EAAE;IAC9G,OAAO,IAAI,oCAAoC,CAAC,KAAK,EAAO,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;AACrG,CAAC,CAAC,CAAC;AAEH,sIAAsI;AACtI,MAAM,mBAAmB,GAA+B;IACpD,IAAI,EAAE;QACF,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE;YACR,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE;oBACb,2BAA2B,EAAE;wBACzB,iBAAiB,EAAE,QAAQ;wBAC3B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,mCAAmC;wBAClD,WAAW,EAAE,iCAAiC;wBAC9C,WAAW,EAAE,iCAAiC;qBACjD;iBACJ;aACJ;YACD,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE;oBACb,2BAA2B,EAAE;wBACzB,iBAAiB,EAAE,QAAQ;wBAC3B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,mCAAmC;wBAClD,WAAW,EAAE,iCAAiC;wBAC9C,WAAW,EAAE,iCAAiC;qBACjD;iBACJ;aACJ;YACD,sBAAsB,EAAE;gBACpB,IAAI,EAAE,UAAU;gBAChB,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;iBACX;gBACD,YAAY,EAAE,sBAAsB;gBACpC,eAAe,EAAE;oBACb,4BAA4B,EAAE;wBAC1B,iBAAiB,EAAE,QAAQ;wBAC3B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,oCAAoC;wBACnD,WAAW,EAAE,kCAAkC;wBAC/C,WAAW,EAAE,kCAAkC;qBAClD;oBACD,kCAAkC,EAAE;wBAChC,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,0CAA0C;wBACzD,WAAW,EAAE,wCAAwC;wBACrD,WAAW,EAAE,wCAAwC;qBACxD;oBACD,kCAAkC,EAAE;wBAChC,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,0CAA0C;wBACzD,WAAW,EAAE,wCAAwC;wBACrD,WAAW,EAAE,wCAAwC;qBACxD;oBACD,kCAAkC,EAAE;wBAChC,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,0CAA0C;wBACzD,WAAW,EAAE,wCAAwC;wBACrD,WAAW,EAAE,wCAAwC;qBACxD;oBACD,kCAAkC,EAAE;wBAChC,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,0CAA0C;wBACzD,WAAW,EAAE,wCAAwC;wBACrD,WAAW,EAAE,wCAAwC;qBACxD;oBACD,iCAAiC,EAAE;wBAC/B,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;wBAC9B,iBAAiB,EAAE,YAAY;wBAC/B,aAAa,EAAE,yCAAyC;qBAC3D;iBACJ;gBACD,kBAAkB,EAAE,yCAAyC;aAChE;YACD,wBAAwB,EAAE;gBACtB,IAAI,EAAE,YAAY;gBAClB,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;iBACX;gBACD,YAAY,EAAE,wBAAwB;gBACtC,eAAe,EAAE;oBACb,8BAA8B,EAAE;wBAC5B,iBAAiB,EAAE,QAAQ;wBAC3B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,sCAAsC;wBACrD,WAAW,EAAE,oCAAoC;wBACjD,WAAW,EAAE,oCAAoC;qBACpD;oBACD,oCAAoC,EAAE;wBAClC,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,4CAA4C;wBAC3D,WAAW,EAAE,0CAA0C;wBACvD,WAAW,EAAE,0CAA0C;qBAC1D;oBACD,oCAAoC,EAAE;wBAClC,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,4CAA4C;wBAC3D,WAAW,EAAE,0CAA0C;wBACvD,WAAW,EAAE,0CAA0C;qBAC1D;iBACJ;aACJ;SACJ;QACD,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE,8BAA8B;QAC5C,SAAS,EAAE,UAAU;KACxB;IACD,KAAK,EAAE;QACH,iBAAiB,EAAE,qBAAqB;QACxC,UAAU,EAAE;YACR,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE;oBACb,2BAA2B,EAAE;wBACzB,iBAAiB,EAAE,QAAQ;wBAC3B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,mCAAmC;wBAClD,WAAW,EAAE,iCAAiC;wBAC9C,WAAW,EAAE,iCAAiC;qBACjD;iBACJ;aACJ;YACD,qBAAqB,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;iBACZ;gBACD,YAAY,EAAE,qBAAqB;gBACnC,eAAe,EAAE;oBACb,2BAA2B,EAAE;wBACzB,iBAAiB,EAAE,QAAQ;wBAC3B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,mCAAmC;wBAClD,WAAW,EAAE,iCAAiC;wBAC9C,WAAW,EAAE,iCAAiC;qBACjD;iBACJ;aACJ;YACD,sBAAsB,EAAE;gBACpB,IAAI,EAAE,UAAU;gBAChB,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;iBACX;gBACD,YAAY,EAAE,sBAAsB;gBACpC,eAAe,EAAE;oBACb,4BAA4B,EAAE;wBAC1B,iBAAiB,EAAE,QAAQ;wBAC3B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,oCAAoC;wBACnD,WAAW,EAAE,kCAAkC;wBAC/C,WAAW,EAAE,kCAAkC;qBAClD;oBACD,kCAAkC,EAAE;wBAChC,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,0CAA0C;wBACzD,WAAW,EAAE,wCAAwC;wBACrD,WAAW,EAAE,wCAAwC;qBACxD;oBACD,kCAAkC,EAAE;wBAChC,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,0CAA0C;wBACzD,WAAW,EAAE,wCAAwC;wBACrD,WAAW,EAAE,wCAAwC;qBACxD;oBACD,kCAAkC,EAAE;wBAChC,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,0CAA0C;wBACzD,WAAW,EAAE,wCAAwC;wBACrD,WAAW,EAAE,wCAAwC;qBACxD;oBACD,kCAAkC,EAAE;wBAChC,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,0CAA0C;wBACzD,WAAW,EAAE,wCAAwC;wBACrD,WAAW,EAAE,wCAAwC;qBACxD;oBACD,iCAAiC,EAAE;wBAC/B,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;wBAC9B,iBAAiB,EAAE,YAAY;wBAC/B,aAAa,EAAE,yCAAyC;qBAC3D;iBACJ;gBACD,kBAAkB,EAAE,yCAAyC;aAChE;YACD,wBAAwB,EAAE;gBACtB,IAAI,EAAE,YAAY;gBAClB,cAAc,EAAE;oBACZ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;iBACX;gBACD,YAAY,EAAE,wBAAwB;gBACtC,eAAe,EAAE;oBACb,8BAA8B,EAAE;wBAC5B,iBAAiB,EAAE,QAAQ;wBAC3B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,sCAAsC;wBACrD,WAAW,EAAE,oCAAoC;wBACjD,WAAW,EAAE,oCAAoC;qBACpD;oBACD,oCAAoC,EAAE;wBAClC,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,4CAA4C;wBAC3D,WAAW,EAAE,0CAA0C;wBACvD,WAAW,EAAE,0CAA0C;qBAC1D;oBACD,oCAAoC,EAAE;wBAClC,iBAAiB,EAAE,OAAO;wBAC1B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBACzC,iBAAiB,EAAE,WAAW;wBAC9B,aAAa,EAAE,4CAA4C;wBAC3D,WAAW,EAAE,0CAA0C;wBACvD,WAAW,EAAE,0CAA0C;qBAC1D;iBACJ;aACJ;SACJ;QACD,cAAc,EAAE,aAAa;QAC7B,YAAY,EAAE,+BAA+B;QAC7C,SAAS,EAAE,WAAW;KACzB;CACJ,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { IMinimalMotionControllerObject, MotionControllerHandedness, IMotionControllerLayoutMap } from \"./webXRAbstractMotionController\";\r\nimport { WebXRAbstractMotionController } from \"./webXRAbstractMotionController\";\r\nimport { WebXRMotionControllerManager } from \"./webXRMotionControllerManager\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Mesh } from \"../../Meshes/mesh\";\r\nimport { Quaternion } from \"../../Maths/math.vector\";\r\nimport { SceneLoader } from \"../../Loading/sceneLoader\";\r\nimport { Logger } from \"../../Misc/logger\";\r\n\r\n/**\r\n * The motion controller class for all microsoft mixed reality controllers\r\n */\r\nexport class WebXRMicrosoftMixedRealityController extends WebXRAbstractMotionController {\r\n    // use this in the future - https://github.com/immersive-web/webxr-input-profiles/tree/master/packages/assets/profiles/microsoft\r\n    protected readonly _mapping = {\r\n        defaultButton: {\r\n            valueNodeName: \"VALUE\",\r\n            unpressedNodeName: \"UNPRESSED\",\r\n            pressedNodeName: \"PRESSED\",\r\n        },\r\n        defaultAxis: {\r\n            valueNodeName: \"VALUE\",\r\n            minNodeName: \"MIN\",\r\n            maxNodeName: \"MAX\",\r\n        },\r\n        buttons: {\r\n            \"xr-standard-trigger\": {\r\n                rootNodeName: \"SELECT\",\r\n                componentProperty: \"button\",\r\n                states: [\"default\", \"touched\", \"pressed\"],\r\n            },\r\n            \"xr-standard-squeeze\": {\r\n                rootNodeName: \"GRASP\",\r\n                componentProperty: \"state\",\r\n                states: [\"pressed\"],\r\n            },\r\n            \"xr-standard-touchpad\": {\r\n                rootNodeName: \"TOUCHPAD_PRESS\",\r\n                labelAnchorNodeName: \"squeeze-label\",\r\n                touchPointNodeName: \"TOUCH\", // TODO - use this for visual feedback\r\n            },\r\n            \"xr-standard-thumbstick\": {\r\n                rootNodeName: \"THUMBSTICK_PRESS\",\r\n                componentProperty: \"state\",\r\n                states: [\"pressed\"],\r\n            },\r\n        },\r\n        axes: {\r\n            \"xr-standard-touchpad\": {\r\n                \"x-axis\": {\r\n                    rootNodeName: \"TOUCHPAD_TOUCH_X\",\r\n                },\r\n                \"y-axis\": {\r\n                    rootNodeName: \"TOUCHPAD_TOUCH_Y\",\r\n                },\r\n            },\r\n            \"xr-standard-thumbstick\": {\r\n                \"x-axis\": {\r\n                    rootNodeName: \"THUMBSTICK_X\",\r\n                },\r\n                \"y-axis\": {\r\n                    rootNodeName: \"THUMBSTICK_Y\",\r\n                },\r\n            },\r\n        },\r\n    };\r\n\r\n    /**\r\n     * The base url used to load the left and right controller models\r\n     */\r\n    public static MODEL_BASE_URL: string = \"https://controllers.babylonjs.com/microsoft/\";\r\n    /**\r\n     * The name of the left controller model file\r\n     */\r\n    public static MODEL_LEFT_FILENAME: string = \"left.glb\";\r\n    /**\r\n     * The name of the right controller model file\r\n     */\r\n    public static MODEL_RIGHT_FILENAME: string = \"right.glb\";\r\n\r\n    public profileId = \"microsoft-mixed-reality\";\r\n\r\n    constructor(scene: Scene, gamepadObject: IMinimalMotionControllerObject, handedness: MotionControllerHandedness) {\r\n        super(scene, MixedRealityProfile[\"left-right\"], gamepadObject, handedness);\r\n    }\r\n\r\n    protected _getFilenameAndPath(): { filename: string; path: string } {\r\n        let filename = \"\";\r\n        if (this.handedness === \"left\") {\r\n            filename = WebXRMicrosoftMixedRealityController.MODEL_LEFT_FILENAME;\r\n        } else {\r\n            // Right is the default if no hand is specified\r\n            filename = WebXRMicrosoftMixedRealityController.MODEL_RIGHT_FILENAME;\r\n        }\r\n\r\n        const device = \"default\";\r\n        const path = WebXRMicrosoftMixedRealityController.MODEL_BASE_URL + device + \"/\";\r\n        return {\r\n            filename,\r\n            path,\r\n        };\r\n    }\r\n\r\n    protected _getModelLoadingConstraints(): boolean {\r\n        const glbLoaded = SceneLoader.IsPluginForExtensionAvailable(\".glb\");\r\n        if (!glbLoaded) {\r\n            Logger.Warn(\"glTF / glb loaded was not registered, using generic controller instead\");\r\n        }\r\n        return glbLoaded;\r\n    }\r\n\r\n    protected _processLoadedModel(_meshes: AbstractMesh[]): void {\r\n        if (!this.rootMesh) {\r\n            return;\r\n        }\r\n\r\n        // Button Meshes\r\n        this.getComponentIds().forEach((id, i) => {\r\n            if (this.disableAnimation) {\r\n                return;\r\n            }\r\n            if (id && this.rootMesh) {\r\n                const buttonMap = (<any>this._mapping.buttons)[id];\r\n                const buttonMeshName = buttonMap.rootNodeName;\r\n                if (!buttonMeshName) {\r\n                    Logger.Log(\"Skipping unknown button at index: \" + i + \" with mapped name: \" + id);\r\n                    return;\r\n                }\r\n\r\n                const buttonMesh = this._getChildByName(this.rootMesh, buttonMeshName);\r\n                if (!buttonMesh) {\r\n                    Logger.Warn(\"Missing button mesh with name: \" + buttonMeshName);\r\n                    return;\r\n                }\r\n\r\n                buttonMap.valueMesh = this._getImmediateChildByName(buttonMesh, this._mapping.defaultButton.valueNodeName);\r\n                buttonMap.pressedMesh = this._getImmediateChildByName(buttonMesh, this._mapping.defaultButton.pressedNodeName);\r\n                buttonMap.unpressedMesh = this._getImmediateChildByName(buttonMesh, this._mapping.defaultButton.unpressedNodeName);\r\n\r\n                if (buttonMap.valueMesh && buttonMap.pressedMesh && buttonMap.unpressedMesh) {\r\n                    const comp = this.getComponent(id);\r\n                    if (comp) {\r\n                        comp.onButtonStateChangedObservable.add(\r\n                            (component) => {\r\n                                this._lerpTransform(buttonMap, component.value);\r\n                            },\r\n                            undefined,\r\n                            true\r\n                        );\r\n                    }\r\n                } else {\r\n                    // If we didn't find the mesh, it simply means this button won't have transforms applied as mapped button value changes.\r\n                    Logger.Warn(\"Missing button submesh under mesh with name: \" + buttonMeshName);\r\n                }\r\n            }\r\n        });\r\n\r\n        // Axis Meshes\r\n        this.getComponentIds().forEach((id) => {\r\n            const comp = this.getComponent(id);\r\n            if (!comp.isAxes()) {\r\n                return;\r\n            }\r\n\r\n            [\"x-axis\", \"y-axis\"].forEach((axis) => {\r\n                if (!this.rootMesh) {\r\n                    return;\r\n                }\r\n                const axisMap = (<any>this._mapping.axes)[id][axis];\r\n\r\n                const axisMesh = this._getChildByName(this.rootMesh, axisMap.rootNodeName);\r\n                if (!axisMesh) {\r\n                    Logger.Warn(\"Missing axis mesh with name: \" + axisMap.rootNodeName);\r\n                    return;\r\n                }\r\n\r\n                axisMap.valueMesh = this._getImmediateChildByName(axisMesh, this._mapping.defaultAxis.valueNodeName);\r\n                axisMap.minMesh = this._getImmediateChildByName(axisMesh, this._mapping.defaultAxis.minNodeName);\r\n                axisMap.maxMesh = this._getImmediateChildByName(axisMesh, this._mapping.defaultAxis.maxNodeName);\r\n\r\n                if (axisMap.valueMesh && axisMap.minMesh && axisMap.maxMesh) {\r\n                    if (comp) {\r\n                        comp.onAxisValueChangedObservable.add(\r\n                            (axisValues) => {\r\n                                const value = axis === \"x-axis\" ? axisValues.x : axisValues.y;\r\n                                this._lerpTransform(axisMap, value, true);\r\n                            },\r\n                            undefined,\r\n                            true\r\n                        );\r\n                    }\r\n                } else {\r\n                    // If we didn't find the mesh, it simply means this button won't have transforms applied as mapped button value changes.\r\n                    Logger.Warn(\"Missing axis submesh under mesh with name: \" + axisMap.rootNodeName);\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    protected _setRootMesh(meshes: AbstractMesh[]): void {\r\n        this.rootMesh = new Mesh(this.profileId + \" \" + this.handedness, this.scene);\r\n        this.rootMesh.isPickable = false;\r\n        let rootMesh;\r\n        // Find the root node in the loaded glTF scene, and attach it as a child of 'parentMesh'\r\n        for (let i = 0; i < meshes.length; i++) {\r\n            const mesh = meshes[i];\r\n\r\n            mesh.isPickable = false;\r\n\r\n            if (!mesh.parent) {\r\n                // Handle root node, attach to the new parentMesh\r\n                rootMesh = mesh;\r\n            }\r\n        }\r\n\r\n        if (rootMesh) {\r\n            rootMesh.setParent(this.rootMesh);\r\n        }\r\n\r\n        if (!this.scene.useRightHandedSystem) {\r\n            this.rootMesh.rotationQuaternion = Quaternion.FromEulerAngles(0, Math.PI, 0);\r\n        }\r\n    }\r\n\r\n    protected _updateModel(): void {\r\n        // no-op. model is updated using observables.\r\n    }\r\n}\r\n\r\n// register the profile\r\nWebXRMotionControllerManager.RegisterController(\"windows-mixed-reality\", (xrInput: XRInputSource, scene: Scene) => {\r\n    return new WebXRMicrosoftMixedRealityController(scene, <any>xrInput.gamepad, xrInput.handedness);\r\n});\r\n\r\n// https://github.com/immersive-web/webxr-input-profiles/blob/master/packages/registry/profiles/microsoft/microsoft-mixed-reality.json\r\nconst MixedRealityProfile: IMotionControllerLayoutMap = {\r\n    left: {\r\n        selectComponentId: \"xr-standard-trigger\",\r\n        components: {\r\n            \"xr-standard-trigger\": {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 0,\r\n                },\r\n                rootNodeName: \"xr_standard_trigger\",\r\n                visualResponses: {\r\n                    xr_standard_trigger_pressed: {\r\n                        componentProperty: \"button\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_trigger_pressed_value\",\r\n                        minNodeName: \"xr_standard_trigger_pressed_min\",\r\n                        maxNodeName: \"xr_standard_trigger_pressed_max\",\r\n                    },\r\n                },\r\n            },\r\n            \"xr-standard-squeeze\": {\r\n                type: \"squeeze\",\r\n                gamepadIndices: {\r\n                    button: 1,\r\n                },\r\n                rootNodeName: \"xr_standard_squeeze\",\r\n                visualResponses: {\r\n                    xr_standard_squeeze_pressed: {\r\n                        componentProperty: \"button\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_squeeze_pressed_value\",\r\n                        minNodeName: \"xr_standard_squeeze_pressed_min\",\r\n                        maxNodeName: \"xr_standard_squeeze_pressed_max\",\r\n                    },\r\n                },\r\n            },\r\n            \"xr-standard-touchpad\": {\r\n                type: \"touchpad\",\r\n                gamepadIndices: {\r\n                    button: 2,\r\n                    xAxis: 0,\r\n                    yAxis: 1,\r\n                },\r\n                rootNodeName: \"xr_standard_touchpad\",\r\n                visualResponses: {\r\n                    xr_standard_touchpad_pressed: {\r\n                        componentProperty: \"button\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_touchpad_pressed_value\",\r\n                        minNodeName: \"xr_standard_touchpad_pressed_min\",\r\n                        maxNodeName: \"xr_standard_touchpad_pressed_max\",\r\n                    },\r\n                    xr_standard_touchpad_xaxis_pressed: {\r\n                        componentProperty: \"xAxis\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_touchpad_xaxis_pressed_value\",\r\n                        minNodeName: \"xr_standard_touchpad_xaxis_pressed_min\",\r\n                        maxNodeName: \"xr_standard_touchpad_xaxis_pressed_max\",\r\n                    },\r\n                    xr_standard_touchpad_yaxis_pressed: {\r\n                        componentProperty: \"yAxis\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_touchpad_yaxis_pressed_value\",\r\n                        minNodeName: \"xr_standard_touchpad_yaxis_pressed_min\",\r\n                        maxNodeName: \"xr_standard_touchpad_yaxis_pressed_max\",\r\n                    },\r\n                    xr_standard_touchpad_xaxis_touched: {\r\n                        componentProperty: \"xAxis\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_touchpad_xaxis_touched_value\",\r\n                        minNodeName: \"xr_standard_touchpad_xaxis_touched_min\",\r\n                        maxNodeName: \"xr_standard_touchpad_xaxis_touched_max\",\r\n                    },\r\n                    xr_standard_touchpad_yaxis_touched: {\r\n                        componentProperty: \"yAxis\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_touchpad_yaxis_touched_value\",\r\n                        minNodeName: \"xr_standard_touchpad_yaxis_touched_min\",\r\n                        maxNodeName: \"xr_standard_touchpad_yaxis_touched_max\",\r\n                    },\r\n                    xr_standard_touchpad_axes_touched: {\r\n                        componentProperty: \"state\",\r\n                        states: [\"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"visibility\",\r\n                        valueNodeName: \"xr_standard_touchpad_axes_touched_value\",\r\n                    },\r\n                },\r\n                touchPointNodeName: \"xr_standard_touchpad_axes_touched_value\",\r\n            },\r\n            \"xr-standard-thumbstick\": {\r\n                type: \"thumbstick\",\r\n                gamepadIndices: {\r\n                    button: 3,\r\n                    xAxis: 2,\r\n                    yAxis: 3,\r\n                },\r\n                rootNodeName: \"xr_standard_thumbstick\",\r\n                visualResponses: {\r\n                    xr_standard_thumbstick_pressed: {\r\n                        componentProperty: \"button\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_thumbstick_pressed_value\",\r\n                        minNodeName: \"xr_standard_thumbstick_pressed_min\",\r\n                        maxNodeName: \"xr_standard_thumbstick_pressed_max\",\r\n                    },\r\n                    xr_standard_thumbstick_xaxis_pressed: {\r\n                        componentProperty: \"xAxis\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_thumbstick_xaxis_pressed_value\",\r\n                        minNodeName: \"xr_standard_thumbstick_xaxis_pressed_min\",\r\n                        maxNodeName: \"xr_standard_thumbstick_xaxis_pressed_max\",\r\n                    },\r\n                    xr_standard_thumbstick_yaxis_pressed: {\r\n                        componentProperty: \"yAxis\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_thumbstick_yaxis_pressed_value\",\r\n                        minNodeName: \"xr_standard_thumbstick_yaxis_pressed_min\",\r\n                        maxNodeName: \"xr_standard_thumbstick_yaxis_pressed_max\",\r\n                    },\r\n                },\r\n            },\r\n        },\r\n        gamepadMapping: \"xr-standard\",\r\n        rootNodeName: \"microsoft-mixed-reality-left\",\r\n        assetPath: \"left.glb\",\r\n    },\r\n    right: {\r\n        selectComponentId: \"xr-standard-trigger\",\r\n        components: {\r\n            \"xr-standard-trigger\": {\r\n                type: \"trigger\",\r\n                gamepadIndices: {\r\n                    button: 0,\r\n                },\r\n                rootNodeName: \"xr_standard_trigger\",\r\n                visualResponses: {\r\n                    xr_standard_trigger_pressed: {\r\n                        componentProperty: \"button\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_trigger_pressed_value\",\r\n                        minNodeName: \"xr_standard_trigger_pressed_min\",\r\n                        maxNodeName: \"xr_standard_trigger_pressed_max\",\r\n                    },\r\n                },\r\n            },\r\n            \"xr-standard-squeeze\": {\r\n                type: \"squeeze\",\r\n                gamepadIndices: {\r\n                    button: 1,\r\n                },\r\n                rootNodeName: \"xr_standard_squeeze\",\r\n                visualResponses: {\r\n                    xr_standard_squeeze_pressed: {\r\n                        componentProperty: \"button\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_squeeze_pressed_value\",\r\n                        minNodeName: \"xr_standard_squeeze_pressed_min\",\r\n                        maxNodeName: \"xr_standard_squeeze_pressed_max\",\r\n                    },\r\n                },\r\n            },\r\n            \"xr-standard-touchpad\": {\r\n                type: \"touchpad\",\r\n                gamepadIndices: {\r\n                    button: 2,\r\n                    xAxis: 0,\r\n                    yAxis: 1,\r\n                },\r\n                rootNodeName: \"xr_standard_touchpad\",\r\n                visualResponses: {\r\n                    xr_standard_touchpad_pressed: {\r\n                        componentProperty: \"button\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_touchpad_pressed_value\",\r\n                        minNodeName: \"xr_standard_touchpad_pressed_min\",\r\n                        maxNodeName: \"xr_standard_touchpad_pressed_max\",\r\n                    },\r\n                    xr_standard_touchpad_xaxis_pressed: {\r\n                        componentProperty: \"xAxis\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_touchpad_xaxis_pressed_value\",\r\n                        minNodeName: \"xr_standard_touchpad_xaxis_pressed_min\",\r\n                        maxNodeName: \"xr_standard_touchpad_xaxis_pressed_max\",\r\n                    },\r\n                    xr_standard_touchpad_yaxis_pressed: {\r\n                        componentProperty: \"yAxis\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_touchpad_yaxis_pressed_value\",\r\n                        minNodeName: \"xr_standard_touchpad_yaxis_pressed_min\",\r\n                        maxNodeName: \"xr_standard_touchpad_yaxis_pressed_max\",\r\n                    },\r\n                    xr_standard_touchpad_xaxis_touched: {\r\n                        componentProperty: \"xAxis\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_touchpad_xaxis_touched_value\",\r\n                        minNodeName: \"xr_standard_touchpad_xaxis_touched_min\",\r\n                        maxNodeName: \"xr_standard_touchpad_xaxis_touched_max\",\r\n                    },\r\n                    xr_standard_touchpad_yaxis_touched: {\r\n                        componentProperty: \"yAxis\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_touchpad_yaxis_touched_value\",\r\n                        minNodeName: \"xr_standard_touchpad_yaxis_touched_min\",\r\n                        maxNodeName: \"xr_standard_touchpad_yaxis_touched_max\",\r\n                    },\r\n                    xr_standard_touchpad_axes_touched: {\r\n                        componentProperty: \"state\",\r\n                        states: [\"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"visibility\",\r\n                        valueNodeName: \"xr_standard_touchpad_axes_touched_value\",\r\n                    },\r\n                },\r\n                touchPointNodeName: \"xr_standard_touchpad_axes_touched_value\",\r\n            },\r\n            \"xr-standard-thumbstick\": {\r\n                type: \"thumbstick\",\r\n                gamepadIndices: {\r\n                    button: 3,\r\n                    xAxis: 2,\r\n                    yAxis: 3,\r\n                },\r\n                rootNodeName: \"xr_standard_thumbstick\",\r\n                visualResponses: {\r\n                    xr_standard_thumbstick_pressed: {\r\n                        componentProperty: \"button\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_thumbstick_pressed_value\",\r\n                        minNodeName: \"xr_standard_thumbstick_pressed_min\",\r\n                        maxNodeName: \"xr_standard_thumbstick_pressed_max\",\r\n                    },\r\n                    xr_standard_thumbstick_xaxis_pressed: {\r\n                        componentProperty: \"xAxis\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_thumbstick_xaxis_pressed_value\",\r\n                        minNodeName: \"xr_standard_thumbstick_xaxis_pressed_min\",\r\n                        maxNodeName: \"xr_standard_thumbstick_xaxis_pressed_max\",\r\n                    },\r\n                    xr_standard_thumbstick_yaxis_pressed: {\r\n                        componentProperty: \"yAxis\",\r\n                        states: [\"default\", \"touched\", \"pressed\"],\r\n                        valueNodeProperty: \"transform\",\r\n                        valueNodeName: \"xr_standard_thumbstick_yaxis_pressed_value\",\r\n                        minNodeName: \"xr_standard_thumbstick_yaxis_pressed_min\",\r\n                        maxNodeName: \"xr_standard_thumbstick_yaxis_pressed_max\",\r\n                    },\r\n                },\r\n            },\r\n        },\r\n        gamepadMapping: \"xr-standard\",\r\n        rootNodeName: \"microsoft-mixed-reality-right\",\r\n        assetPath: \"right.glb\",\r\n    },\r\n};\r\n"]}