{"version": 3, "file": "tubeBuilder.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Builders/tubeBuilder.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AACtE,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAE/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAM,UAAU,UAAU,CACtB,IAAY,EACZ,OAaC,EACD,QAAyB,IAAI;IAE7B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAC1B,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IAChC,IAAI,MAAM,GAAG,GAAG,CAAC;IAEjB,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;QAC9B,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;KAC3B;SAAM,IAAI,QAAQ,EAAE;QACjB,MAAM,GAAG,QAAQ,CAAC,oBAAqB,CAAC,MAAM,CAAC;KAClD;IAED,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,EAAE,GAAG,CAAC,CAAC;IACpD,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC;IACtD,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC;IACrC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC;IAC3C,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;IACpC,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACjF,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC;IAElG,gBAAgB;IAChB,MAAM,aAAa,GAAG,CAClB,IAAe,EACf,MAAc,EACd,WAAwB,EACxB,MAAc,EACd,YAAoB,EACpB,cAAmE,EACnE,GAAW,EACX,GAAW,EACb,EAAE;QACA,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACtC,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QACpC,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QACxC,MAAM,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACxB,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,GAAG,CAAC;QACxC,MAAM,YAAY,GAA8C,GAAG,EAAE,CAAC,MAAM,CAAC;QAC7E,MAAM,mBAAmB,GAA8C,cAAc,IAAI,YAAY,CAAC;QAEtG,IAAI,UAAqB,CAAC;QAC1B,IAAI,GAAW,CAAC;QAChB,IAAI,MAAe,CAAC;QACpB,IAAI,OAAgB,CAAC;QACrB,MAAM,cAAc,GAAW,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACpD,IAAI,KAAK,GAAG,GAAG,KAAK,IAAI,CAAC,MAAM,IAAI,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,GAAG,GAAG,mBAAmB,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB;YAC7D,UAAU,GAAG,KAAK,EAAW,CAAC,CAAC,uBAAuB;YACtD,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;gBACnC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,cAAc,CAAC,CAAC;gBAChE,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gBACzD,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;gBACnE,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,UAAU,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;aAC3B;YACD,WAAW,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC;YAChC,KAAK,EAAE,CAAC;SACX;QACD,MAAM;QACN,MAAM,OAAO,GAAG,CAAC,QAAgB,EAAE,SAAiB,EAAkB,EAAE;YACpE,MAAM,QAAQ,GAAG,KAAK,EAAW,CAAC;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;gBAC/B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;aAClC;YACD,OAAO,QAAQ,CAAC;QACpB,CAAC,CAAC;QACF,QAAQ,GAAG,EAAE;YACT,KAAK,IAAI,CAAC,MAAM;gBACZ,MAAM;YACV,KAAK,IAAI,CAAC,SAAS;gBACf,WAAW,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;gBAC1C,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACzC,MAAM;YACV,KAAK,IAAI,CAAC,OAAO;gBACb,WAAW,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACrD,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChE,MAAM;YACV,KAAK,IAAI,CAAC,OAAO;gBACb,WAAW,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;gBAC1C,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACzC,WAAW,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACrD,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChE,MAAM;YACV;gBACI,MAAM;SACb;QACD,OAAO,WAAW,CAAC;IACvB,CAAC,CAAC;IAEF,IAAI,MAAM,CAAC;IACX,IAAI,SAAS,CAAC;IACd,IAAI,QAAQ,EAAE;QACV,cAAc;QACd,MAAM,OAAO,GAAG,QAAQ,CAAC,oBAAqB,CAAC;QAC/C,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC;QACvC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACrC,SAAS,GAAG,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,YAAY,EAAE,cAAc,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC3H,QAAQ,GAAG,YAAY,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC1E,gDAAgD;QAChD,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;QAC9B,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;QAClB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QAExB,OAAO,QAAQ,CAAC;KACnB;IAED,gBAAgB;IAChB,MAAM,GAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC;IAC/B,MAAM,YAAY,GAAG,IAAI,KAAK,EAAkB,CAAC;IACjD,GAAG,GAAG,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IACnC,SAAS,GAAG,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC9G,MAAM,IAAI,GAAG,YAAY,CACrB,IAAI,EACJ;QACI,SAAS,EAAE,SAAS;QACpB,SAAS,EAAE,IAAI;QACf,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,SAAS;QACpB,eAAe,EAAE,eAAe;QAChC,QAAQ,EAAE,QAAQ;QAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;KAC3B,EACD,KAAK,CACR,CAAC;IACF,IAAI,CAAC,oBAAqB,CAAC,SAAS,GAAG,SAAS,CAAC;IACjD,IAAI,CAAC,oBAAqB,CAAC,MAAM,GAAG,MAAM,CAAC;IAC3C,IAAI,CAAC,oBAAqB,CAAC,YAAY,GAAG,YAAY,CAAC;IACvD,IAAI,CAAC,oBAAqB,CAAC,GAAG,GAAG,GAAG,CAAC;IACrC,IAAI,CAAC,oBAAqB,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAI,CAAC,oBAAqB,CAAC,MAAM,GAAG,MAAM,CAAC;IAE3C,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG;IACvB,gEAAgE;IAChE,UAAU;CACb,CAAC;AAEF,IAAI,CAAC,UAAU,GAAG,CACd,IAAY,EACZ,IAAe,EACf,MAAc,EACd,YAAoB,EACpB,cAAyD,EACzD,GAAW,EACX,KAAY,EACZ,SAAmB,EACnB,eAAwB,EACxB,QAAe,EACX,EAAE;IACN,MAAM,OAAO,GAAG;QACZ,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,MAAM;QACd,YAAY,EAAE,YAAY;QAC1B,cAAc,EAAE,cAAc;QAC9B,GAAG,EAAE,CAAC;QACN,GAAG,EAAE,GAAG;QACR,SAAS,EAAE,SAAS;QACpB,eAAe,EAAE,eAAe;QAChC,QAAQ,EAAE,QAAQ;KACrB,CAAC;IACF,OAAO,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC5C,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Vector4 } from \"../../Maths/math.vector\";\r\nimport { Vector3, TmpVectors, Matrix } from \"../../Maths/math.vector\";\r\nimport { Mesh } from \"../mesh\";\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON> } from \"./ribbonBuilder\";\r\nimport { Path3D } from \"../../Maths/math.path\";\r\n\r\n/**\r\n * Creates a tube mesh.\r\n * The tube is a parametric shape. It has no predefined shape. Its final shape will depend on the input parameters\r\n * * The parameter `path` is a required array of successive Vector3. It is the curve used as the axis of the tube\r\n * * The parameter `radius` (positive float, default 1) sets the tube radius size\r\n * * The parameter `tessellation` (positive float, default 64) is the number of sides on the tubular surface\r\n * * The parameter `radiusFunction` (javascript function, default null) is a vanilla javascript function. If it is not null, it overrides the parameter `radius`\r\n * * This function is called on each point of the tube path and is passed the index `i` of the i-th point and the distance of this point from the first point of the path. It must return a radius value (positive float)\r\n * * The parameter `arc` (positive float, maximum 1, default 1) is the ratio to apply to the tube circumference : 2 x PI x arc\r\n * * The parameter `cap` sets the way the extruded shape is capped. Possible values : BABYLON.Mesh.NO_CAP (default), BABYLON.Mesh.CAP_START, BABYLON.Mesh.CAP_END, BABYLON.Mesh.CAP_ALL\r\n * * The optional parameter `instance` is an instance of an existing Tube object to be updated with the passed `pathArray` parameter. The `path`Array HAS to have the SAME number of points as the previous one: https://doc.babylonjs.com/features/featuresDeepDive/mesh/dynamicMeshMorph#tube\r\n * * You can also set the mesh side orientation with the values : BABYLON.Mesh.FRONTSIDE (default), BABYLON.Mesh.BACKSIDE or BABYLON.Mesh.DOUBLESIDE\r\n * * If you create a double-sided mesh, you can choose what parts of the texture image to crop and stick respectively on the front and the back sides with the parameters `frontUVs` and `backUVs` (Vector4). Detail here : https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation\r\n * * The optional parameter `invertUV` (boolean, default false) swaps in the geometry the U and V coordinates to apply a texture\r\n * * The mesh can be set to updatable with the boolean parameter `updatable` (default false) if its internal geometry is supposed to change once created. The NUMBER of points CAN'T CHANGE, only their positions.\r\n * @param name defines the name of the mesh\r\n * @param options defines the options used to create the mesh\r\n * @param options.path\r\n * @param options.radius\r\n * @param options.tessellation\r\n * @param options.radiusFunction\r\n * @param options.cap\r\n * @param options.arc\r\n * @param options.updatable\r\n * @param options.sideOrientation\r\n * @param options.frontUVs\r\n * @param options.backUVs\r\n * @param options.instance\r\n * @param options.invertUV\r\n * @param scene defines the hosting scene\r\n * @returns the tube mesh\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#tube\r\n */\r\nexport function CreateTube(\r\n    name: string,\r\n    options: {\r\n        path: Vector3[];\r\n        radius?: number;\r\n        tessellation?: number;\r\n        radiusFunction?: { (i: number, distance: number): number };\r\n        cap?: number;\r\n        arc?: number;\r\n        updatable?: boolean;\r\n        sideOrientation?: number;\r\n        frontUVs?: Vector4;\r\n        backUVs?: Vector4;\r\n        instance?: Mesh;\r\n        invertUV?: boolean;\r\n    },\r\n    scene: Nullable<Scene> = null\r\n): Mesh {\r\n    const path = options.path;\r\n    let instance = options.instance;\r\n    let radius = 1.0;\r\n\r\n    if (options.radius !== undefined) {\r\n        radius = options.radius;\r\n    } else if (instance) {\r\n        radius = instance._creationDataStorage!.radius;\r\n    }\r\n\r\n    const tessellation = options.tessellation || 64 | 0;\r\n    const radiusFunction = options.radiusFunction || null;\r\n    let cap = options.cap || Mesh.NO_CAP;\r\n    const invertUV = options.invertUV || false;\r\n    const updatable = options.updatable;\r\n    const sideOrientation = Mesh._GetDefaultSideOrientation(options.sideOrientation);\r\n    options.arc = options.arc && (options.arc <= 0.0 || options.arc > 1.0) ? 1.0 : options.arc || 1.0;\r\n\r\n    // tube geometry\r\n    const tubePathArray = (\r\n        path: Vector3[],\r\n        path3D: Path3D,\r\n        circlePaths: Vector3[][],\r\n        radius: number,\r\n        tessellation: number,\r\n        radiusFunction: Nullable<{ (i: number, distance: number): number }>,\r\n        cap: number,\r\n        arc: number\r\n    ) => {\r\n        const tangents = path3D.getTangents();\r\n        const normals = path3D.getNormals();\r\n        const distances = path3D.getDistances();\r\n        const pi2 = Math.PI * 2;\r\n        const step = (pi2 / tessellation) * arc;\r\n        const returnRadius: { (i: number, distance: number): number } = () => radius;\r\n        const radiusFunctionFinal: { (i: number, distance: number): number } = radiusFunction || returnRadius;\r\n\r\n        let circlePath: Vector3[];\r\n        let rad: number;\r\n        let normal: Vector3;\r\n        let rotated: Vector3;\r\n        const rotationMatrix: Matrix = TmpVectors.Matrix[0];\r\n        let index = cap === Mesh.NO_CAP || cap === Mesh.CAP_END ? 0 : 2;\r\n        for (let i = 0; i < path.length; i++) {\r\n            rad = radiusFunctionFinal(i, distances[i]); // current radius\r\n            circlePath = Array<Vector3>(); // current circle array\r\n            normal = normals[i]; // current normal\r\n            for (let t = 0; t < tessellation; t++) {\r\n                Matrix.RotationAxisToRef(tangents[i], step * t, rotationMatrix);\r\n                rotated = circlePath[t] ? circlePath[t] : Vector3.Zero();\r\n                Vector3.TransformCoordinatesToRef(normal, rotationMatrix, rotated);\r\n                rotated.scaleInPlace(rad).addInPlace(path[i]);\r\n                circlePath[t] = rotated;\r\n            }\r\n            circlePaths[index] = circlePath;\r\n            index++;\r\n        }\r\n        // cap\r\n        const capPath = (nbPoints: number, pathIndex: number): Array<Vector3> => {\r\n            const pointCap = Array<Vector3>();\r\n            for (let i = 0; i < nbPoints; i++) {\r\n                pointCap.push(path[pathIndex]);\r\n            }\r\n            return pointCap;\r\n        };\r\n        switch (cap) {\r\n            case Mesh.NO_CAP:\r\n                break;\r\n            case Mesh.CAP_START:\r\n                circlePaths[0] = capPath(tessellation, 0);\r\n                circlePaths[1] = circlePaths[2].slice(0);\r\n                break;\r\n            case Mesh.CAP_END:\r\n                circlePaths[index] = circlePaths[index - 1].slice(0);\r\n                circlePaths[index + 1] = capPath(tessellation, path.length - 1);\r\n                break;\r\n            case Mesh.CAP_ALL:\r\n                circlePaths[0] = capPath(tessellation, 0);\r\n                circlePaths[1] = circlePaths[2].slice(0);\r\n                circlePaths[index] = circlePaths[index - 1].slice(0);\r\n                circlePaths[index + 1] = capPath(tessellation, path.length - 1);\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n        return circlePaths;\r\n    };\r\n\r\n    let path3D;\r\n    let pathArray;\r\n    if (instance) {\r\n        // tube update\r\n        const storage = instance._creationDataStorage!;\r\n        const arc = options.arc || storage.arc;\r\n        path3D = storage.path3D.update(path);\r\n        pathArray = tubePathArray(path, path3D, storage.pathArray, radius, storage.tessellation, radiusFunction, storage.cap, arc);\r\n        instance = CreateRibbon(\"\", { pathArray: pathArray, instance: instance });\r\n        // Update mode, no need to recreate the storage.\r\n        storage.path3D = path3D;\r\n        storage.pathArray = pathArray;\r\n        storage.arc = arc;\r\n        storage.radius = radius;\r\n\r\n        return instance;\r\n    }\r\n\r\n    // tube creation\r\n    path3D = <any>new Path3D(path);\r\n    const newPathArray = new Array<Array<Vector3>>();\r\n    cap = cap < 0 || cap > 3 ? 0 : cap;\r\n    pathArray = tubePathArray(path, path3D, newPathArray, radius, tessellation, radiusFunction, cap, options.arc);\r\n    const tube = CreateRibbon(\r\n        name,\r\n        {\r\n            pathArray: pathArray,\r\n            closePath: true,\r\n            closeArray: false,\r\n            updatable: updatable,\r\n            sideOrientation: sideOrientation,\r\n            invertUV: invertUV,\r\n            frontUVs: options.frontUVs,\r\n            backUVs: options.backUVs,\r\n        },\r\n        scene\r\n    );\r\n    tube._creationDataStorage!.pathArray = pathArray;\r\n    tube._creationDataStorage!.path3D = path3D;\r\n    tube._creationDataStorage!.tessellation = tessellation;\r\n    tube._creationDataStorage!.cap = cap;\r\n    tube._creationDataStorage!.arc = options.arc;\r\n    tube._creationDataStorage!.radius = radius;\r\n\r\n    return tube;\r\n}\r\n\r\n/**\r\n * Class containing static functions to help procedurally build meshes\r\n * @deprecated use CreateTube directly\r\n */\r\nexport const TubeBuilder = {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    CreateTube,\r\n};\r\n\r\nMesh.CreateTube = (\r\n    name: string,\r\n    path: Vector3[],\r\n    radius: number,\r\n    tessellation: number,\r\n    radiusFunction: { (i: number, distance: number): number },\r\n    cap: number,\r\n    scene: Scene,\r\n    updatable?: boolean,\r\n    sideOrientation?: number,\r\n    instance?: Mesh\r\n): Mesh => {\r\n    const options = {\r\n        path: path,\r\n        radius: radius,\r\n        tessellation: tessellation,\r\n        radiusFunction: radiusFunction,\r\n        arc: 1,\r\n        cap: cap,\r\n        updatable: updatable,\r\n        sideOrientation: sideOrientation,\r\n        instance: instance,\r\n    };\r\n    return CreateTube(name, options, scene);\r\n};\r\n"]}