{"version": 3, "file": "shadowMapUboDeclaration.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Shaders/ShadersInclude/shadowMapUboDeclaration.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,uBAAuB,CAAC;AAC/B,OAAO,sBAAsB,CAAC;AAE9B,MAAM,IAAI,GAAG,yBAAyB,CAAC;AACvC,MAAM,MAAM,GAAG;;;CAGd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAChD,gBAAgB;AAChB,MAAM,CAAC,MAAM,uBAAuB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore\";\nimport \"./sceneUboDeclaration\";\nimport \"./meshUboDeclaration\";\n\nconst name = \"shadowMapUboDeclaration\";\nconst shader = `layout(std140,column_major) uniform;\n#include<sceneUboDeclaration>\n#include<meshUboDeclaration>\n`;\n// Sideeffect\nShaderStore.IncludesShadersStore[name] = shader;\n/** @internal */\nexport const shadowMapUboDeclaration = { name, shader };\n"]}