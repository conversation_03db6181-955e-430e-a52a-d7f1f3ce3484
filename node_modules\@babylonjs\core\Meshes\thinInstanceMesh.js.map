{"version": 3, "file": "thinInstanceMesh.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Meshes/thinInstanceMesh.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AACzD,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AACnE,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,YAAY,EAAE,mCAAkC;AAyHzD,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,MAAwE,EAAE,UAAmB,IAAI;IACxI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,eAAe,EAAE;QACxD,MAAM,CAAC,KAAK,CAAC,4FAA4F,CAAC,CAAC;QAC3G,OAAO,CAAC,CAAC,CAAC;KACb;IAED,IAAI,CAAC,6BAA6B,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAExF,MAAM,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC;IAE3D,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACpC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,wBAAwB,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC;SAC/H;KACJ;SAAM;QACH,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,wBAAwB,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;KACjG;IAED,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,UAAmB,IAAI;IAClE,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AAClE,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,6BAA6B,GAAG,UAAU,IAAY,EAAE,MAAc;IACjF,kCAAkC;IAClC,IAAI,IAAI,KAAK,YAAY,CAAC,SAAS,EAAE;QACjC,IAAI,GAAG,YAAY,CAAC,iBAAiB,CAAC;KACzC;IAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAE9B,IAAI,CAAC,kCAAkC,EAAE,CAAC;IAE1C,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;IAC5D,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe;IACvI,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IACrH,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAEhL,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAE,CAAC,CAAC;AACtF,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAAU,KAAa,EAAE,MAAmC,EAAE,UAAmB,IAAI;IAC1H,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,UAAU,IAAI,KAAK,IAAI,IAAI,CAAC,wBAAwB,CAAC,cAAc,EAAE;QACpG,OAAO,KAAK,CAAC;KAChB;IAED,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC;IAE5D,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC;IAE3C,IAAI,IAAI,CAAC,wBAAwB,CAAC,aAAa,EAAE;QAC7C,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,MAAgB,CAAC;KACzE;IAED,IAAI,OAAO,EAAE;QACT,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QAEzC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAC;SAC/C;KACJ;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,0BAA0B,GAAG,UAAU,IAAY,EAAE,KAAa,EAAE,KAAoB,EAAE,UAAmB,IAAI;IAC5H,kCAAkC;IAClC,IAAI,IAAI,KAAK,YAAY,CAAC,SAAS,EAAE;QACjC,IAAI,GAAG,YAAY,CAAC,iBAAiB,CAAC;KACzC;IAED,IAAI,CAAC,IAAI,CAAC,+BAA+B,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,wBAAwB,CAAC,cAAc,EAAE;QACpJ,OAAO,KAAK,CAAC;KAChB;IAED,IAAI,CAAC,6BAA6B,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,2DAA2D;IAExG,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAEvH,IAAI,OAAO,EAAE;QACT,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;KACxC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,mBAAmB,EAAE;IACvD,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC;IACxD,CAAC;IACD,GAAG,EAAE,UAAsB,KAAa;QACpC,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,EAAE,wBAAwB,CAAC,UAAU,CAAC;QAChH,MAAM,eAAe,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhE,IAAI,KAAK,IAAI,eAAe,EAAE;YAC1B,IAAI,CAAC,wBAAwB,CAAC,cAAc,GAAG,KAAK,CAAC;SACxD;IACL,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,IAAI,CAAC,SAAS,CAAC,+BAA+B,GAAG,UAAU,IAAY,EAAE,MAAoB,EAAE,eAAwB,IAAI;IACvH,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC,YAAY,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAE1F,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACxB,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC/E;IAED,OAAO,YAAY,CAAC;AACxB,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAAU,IAAY,EAAE,MAA8B,EAAE,SAAiB,CAAC,EAAE,eAAwB,IAAI;IAC3I,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;IAEtB,IAAI,IAAI,KAAK,QAAQ,EAAE;QACnB,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC;QACtD,IAAI,CAAC,wBAAwB,CAAC,YAAY,GAAG,IAAI,CAAC;QAClD,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC;QACtF,IAAI,CAAC,wBAAwB,CAAC,UAAU,GAAG,MAAM,CAAC;QAClD,IAAI,CAAC,wBAAwB,CAAC,aAAa,GAAG,IAAI,CAAC;QAEnD,IAAI,MAAM,KAAK,IAAI,EAAE;YACjB,IAAI,CAAC,wBAAwB,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;YACtE,IAAI,CAAC,wBAAwB,CAAC,YAAY,GAAG,IAAI,CAAC,+BAA+B,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;YAEjH,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC7B,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAC;aAC/C;SACJ;aAAM;YACH,IAAI,CAAC,wBAAwB,CAAC,cAAc,GAAG,CAAC,CAAC;YACjD,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC7B,kIAAkI;gBAClI,IAAI,CAAC,mBAAmB,EAAE,CAAC;aAC9B;SACJ;KACJ;SAAM,IAAI,IAAI,KAAK,gBAAgB,EAAE;QAClC,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;QAC9D,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAC1D,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,GAAG,MAAM,CAAC;QAC1D,IAAI,MAAM,KAAK,IAAI,EAAE;YACjB,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,GAAG,IAAI,CAAC,+BAA+B,CAAC,eAAe,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;SACpI;KACJ;SAAM;QACH,yHAAyH;QACzH,6DAA6D;QAC7D,IAAI,IAAI,KAAK,YAAY,CAAC,SAAS,EAAE;YACjC,IAAI,GAAG,YAAY,CAAC,iBAAiB,CAAC;SACzC;QAED,IAAI,MAAM,KAAK,IAAI,EAAE;YACjB,IAAI,IAAI,CAAC,+BAA+B,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;gBAClD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC9B,OAAO,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvD,OAAO,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC1D,OAAO,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACxD,OAAO,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;aACnE;SACJ;aAAM;YACH,IAAI,CAAC,kCAAkC,EAAE,CAAC;YAE1C,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YACzD,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YAC5D,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;YACjE,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YAEhJ,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAE,CAAC,CAAC;SACrF;KACJ;AACL,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,IAAY;IAC7D,IAAI,IAAI,KAAK,QAAQ,EAAE;QACnB,IAAI,IAAI,CAAC,gDAAgD,IAAI,IAAI,CAAC,wBAAwB,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE;YAClK,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;SAC1C;QACD,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,cAAc,CAAC,IAAI,CAAC,wBAAwB,CAAC,UAAW,EAAE,CAAC,EAAE,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;KAC1J;SAAM,IAAI,IAAI,KAAK,gBAAgB,EAAE;QAClC,IACI,IAAI,CAAC,gDAAgD;YACrD,IAAI,CAAC,wBAAwB,CAAC,oBAAoB;YAClD,CAAC,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,CAAC,WAAW,EAAE,EACnE;YACE,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;SAC1C;QACD,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,cAAc,CAAC,IAAI,CAAC,wBAAwB,CAAC,kBAAmB,EAAE,CAAC,EAAE,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;KAC1K;SAAM;QACH,kCAAkC;QAClC,IAAI,IAAI,KAAK,YAAY,CAAC,SAAS,EAAE;YACjC,IAAI,GAAG,YAAY,CAAC,iBAAiB,CAAC;SACzC;QAED,IAAI,IAAI,CAAC,+BAA+B,EAAE,aAAa,CAAC,IAAI,CAAC,EAAE;YAC3D,IAAI,IAAI,CAAC,gDAAgD,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAE,CAAC,WAAW,EAAE,EAAE;gBACnI,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;aAC1C;YACD,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAE,CAAC,cAAc,CAAC,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;SAChI;KACJ;AACL,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,+BAA+B,GAAG,UAAU,IAAY,EAAE,IAAkB,EAAE,MAAc;IACvG,IAAI,IAAI,KAAK,QAAQ,EAAE;QACnB,IAAI,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE;YAC5C,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SAC3E;KACJ;SAAM;QACH,kCAAkC;QAClC,IAAI,IAAI,KAAK,YAAY,CAAC,SAAS,EAAE;YACjC,IAAI,GAAG,YAAY,CAAC,iBAAiB,CAAC;SACzC;QAED,IAAI,IAAI,CAAC,+BAA+B,EAAE,aAAa,CAAC,IAAI,CAAC,EAAE;YAC3D,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAE,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SAC1F;KACJ;AACL,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,4BAA4B,GAAG;IAC1C,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE;QAC1F,OAAO,EAAE,CAAC;KACb;IACD,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC;IAE5D,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,aAAa,EAAE;QAC9C,IAAI,CAAC,wBAAwB,CAAC,aAAa,GAAG,EAAc,CAAC;QAE7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,cAAc,EAAE,EAAE,CAAC,EAAE;YACnE,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;SACzF;KACJ;IAED,OAAO,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC;AACvD,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,+BAA+B,GAAG,UAAU,yBAAkC,KAAK,EAAE,gBAAyB,KAAK,EAAE,aAAsB,KAAK;IAC3J,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE;QAC1F,OAAO;KACV;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC;IAE9D,IAAI,sBAAsB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;QACjD,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QACpD,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,IAAI,CAAC,eAAe,GAAG,IAAI,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;KACvF;IAED,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC;IAE5D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAC9D,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;SAC7D;KACJ;IAED,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAM;IAC9D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAM;IAE9D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,cAAc,EAAE,EAAE,CAAC,EAAE;QACnE,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACrC,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3F,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SAChE;KACJ;IAED,YAAY,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAEvE,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC/B,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,2BAA2B,GAAG,UAAU,IAAY,EAAE,eAAwB,IAAI;IAC7F,IAAI,IAAI,KAAK,QAAQ,EAAE;QACnB,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC;QACtD,IAAI,CAAC,wBAAwB,CAAC,YAAY,GAAG,IAAI,CAAC,+BAA+B,CAAC,OAAO,EAAE,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;KACtJ;SAAM,IAAI,IAAI,KAAK,gBAAgB,EAAE;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,0BAA0B,EAAE;YACxC,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;YAC9D,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,GAAG,IAAI,CAAC,+BAA+B,CACrF,eAAe,EACf,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,IAAI,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAC5F,YAAY,CACf,CAAC;SACL;KACJ;SAAM;QACH,IAAI,IAAI,KAAK,YAAY,CAAC,SAAS,EAAE;YACjC,IAAI,GAAG,YAAY,CAAC,iBAAiB,CAAC;SACzC;QAED,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC;QACpE,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,YAAY,CACvE,IAAI,CAAC,SAAS,EAAE,EAChB,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC/C,IAAI,EACJ,CAAC,YAAY,EACb,KAAK,EACL,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,IAAI,CAAC,EAClD,IAAI,CACP,CAAC;QACF,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAE,CAAC,CAAC;KACrF;AACL,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,6BAA6B,GAAG,UAAU,IAAY,EAAE,eAAuB,CAAC;IAC3F,kCAAkC;IAClC,IAAI,IAAI,KAAK,YAAY,CAAC,SAAS,EAAE;QACjC,IAAI,GAAG,YAAY,CAAC,iBAAiB,CAAC;KACzC;IAED,MAAM,YAAY,GAAG,IAAI,KAAK,QAAQ,CAAC;IAEvC,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,+BAA+B,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE;QACjH,OAAO;KACV;IAED,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACtF,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACrI,IAAI,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAErH,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,cAAc,GAAG,YAAY,CAAC,GAAG,MAAM,CAAC;IAE1F,IAAI,OAAO,GAAG,WAAW,CAAC;IAE1B,OAAO,OAAO,GAAG,UAAU,EAAE;QACzB,OAAO,IAAI,CAAC,CAAC;KAChB;IAED,IAAI,CAAC,IAAI,IAAI,WAAW,IAAI,OAAO,EAAE;QACjC,IAAI,CAAC,IAAI,EAAE;YACP,IAAI,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;SACpC;aAAM;YACH,MAAM,OAAO,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACrB,IAAI,GAAG,OAAO,CAAC;SAClB;QAED,IAAI,YAAY,EAAE;YACd,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC;YACtD,IAAI,CAAC,wBAAwB,CAAC,YAAY,GAAG,IAAI,CAAC,+BAA+B,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YACxG,IAAI,CAAC,wBAAwB,CAAC,UAAU,GAAG,IAAI,CAAC;YAChD,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,GAAG,OAAO,CAAC;YACzD,IAAI,IAAI,CAAC,MAAM,CAAC,0BAA0B,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,EAAE;gBAC7F,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;gBAC9D,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,GAAG,IAAI,CAAC,+BAA+B,CAAC,eAAe,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;aAC3H;SACJ;aAAM;YACH,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC;YAEpE,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;YACvD,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;YAC3D,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YAErI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAE,CAAC,CAAC;SACrF;KACJ;AACL,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,kCAAkC,GAAG;IAChD,IAAI,CAAC,IAAI,CAAC,+BAA+B,EAAE;QACvC,IAAI,CAAC,+BAA+B,GAAG;YACnC,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,EAAE;YACT,aAAa,EAAE,EAAE;YACjB,OAAO,EAAE,EAAE;SACd,CAAC;KACL;AACL,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,CAAC,gCAAgC,GAAG;IAC9C,IAAI,IAAI,CAAC,wBAAwB,EAAE,YAAY,EAAE;QAC7C,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QACrD,IAAI,CAAC,wBAAwB,CAAC,YAAY,GAAG,IAAI,CAAC;KACrD;AACL,CAAC,CAAC", "sourcesContent": ["import type { Nullable, DeepImmutableObject } from \"../types\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { Vertex<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from \"../Buffers/buffer\";\r\nimport { Matrix, Vector3, TmpVectors } from \"../Maths/math.vector\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { BoundingInfo } from \"core/Culling/boundingInfo\";\r\n\r\ndeclare module \"./mesh\" {\r\n    export interface Mesh {\r\n        /**\r\n         * Gets or sets a boolean defining if we want picking to pick thin instances as well\r\n         */\r\n        thinInstanceEnablePicking: boolean;\r\n\r\n        /**\r\n         * Indicates that a buffer created as static should be recreated if the buffer is updated (by calling thinInstanceSetMatrixAt or thinInstanceSetAttributeAt, for eg.)\r\n         * If this flag is false (the default behavior), a buffer created as \"static\" won't show any update done to it, and will stay the same as it was created.\r\n         * Note however that recreating a buffer each time there's a change will have some performance cost, that's why it is set to false by default.\r\n         * You should set this flag to true only if your static buffers should change infrequently. If they change frequently, you should create your buffers as \"dynamic\" instead.\r\n         */\r\n        thinInstanceAllowAutomaticStaticBufferRecreation: boolean;\r\n\r\n        /**\r\n         * Creates a new thin instance\r\n         * @param matrix the matrix or array of matrices (position, rotation, scale) of the thin instance(s) to create\r\n         * @param refresh true to refresh the underlying gpu buffer (default: true). If you do multiple calls to this method in a row, set refresh to true only for the last call to save performance\r\n         * @returns the thin instance index number. If you pass an array of matrices, other instance indexes are index+1, index+2, etc\r\n         */\r\n        thinInstanceAdd(matrix: DeepImmutableObject<Matrix> | Array<DeepImmutableObject<Matrix>>, refresh?: boolean): number;\r\n\r\n        /**\r\n         * Adds the transformation (matrix) of the current mesh as a thin instance\r\n         * @param refresh true to refresh the underlying gpu buffer (default: true). If you do multiple calls to this method in a row, set refresh to true only for the last call to save performance\r\n         * @returns the thin instance index number\r\n         */\r\n        thinInstanceAddSelf(refresh?: boolean): number;\r\n\r\n        /**\r\n         * Registers a custom attribute to be used with thin instances\r\n         * @param kind name of the attribute\r\n         * @param stride size in floats of the attribute\r\n         */\r\n        thinInstanceRegisterAttribute(kind: string, stride: number): void;\r\n\r\n        /**\r\n         * Sets the matrix of a thin instance\r\n         * @param index index of the thin instance\r\n         * @param matrix matrix to set\r\n         * @param refresh true to refresh the underlying gpu buffer (default: true). If you do multiple calls to this method in a row, set refresh to true only for the last call to save performance\r\n         */\r\n        thinInstanceSetMatrixAt(index: number, matrix: DeepImmutableObject<Matrix>, refresh?: boolean): void;\r\n\r\n        /**\r\n         * Sets the value of a custom attribute for a thin instance\r\n         * @param kind name of the attribute\r\n         * @param index index of the thin instance\r\n         * @param value value to set\r\n         * @param refresh true to refresh the underlying gpu buffer (default: true). If you do multiple calls to this method in a row, set refresh to true only for the last call to save performance\r\n         */\r\n        thinInstanceSetAttributeAt(kind: string, index: number, value: Array<number>, refresh?: boolean): void;\r\n\r\n        /**\r\n         * Gets / sets the number of thin instances to display. Note that you can't set a number higher than what the underlying buffer can handle.\r\n         */\r\n        thinInstanceCount: number;\r\n\r\n        /**\r\n         * Sets a buffer to be used with thin instances. This method is a faster way to setup multiple instances than calling thinInstanceAdd repeatedly\r\n         * @param kind name of the attribute. Use \"matrix\" to setup the buffer of matrices\r\n         * @param buffer buffer to set\r\n         * @param stride size in floats of each value of the buffer\r\n         * @param staticBuffer indicates that the buffer is static, so that you won't change it after it is set (better performances - true by default)\r\n         */\r\n        thinInstanceSetBuffer(kind: string, buffer: Nullable<Float32Array>, stride?: number, staticBuffer?: boolean): void;\r\n\r\n        /**\r\n         * Gets the list of world matrices\r\n         * @returns an array containing all the world matrices from the thin instances\r\n         */\r\n        thinInstanceGetWorldMatrices(): Matrix[];\r\n\r\n        /**\r\n         * Synchronize the gpu buffers with a thin instance buffer. Call this method if you update later on the buffers passed to thinInstanceSetBuffer\r\n         * @param kind name of the attribute to update. Use \"matrix\" to update the buffer of matrices\r\n         */\r\n        thinInstanceBufferUpdated(kind: string): void;\r\n\r\n        /**\r\n         * Applies a partial update to a buffer directly on the GPU\r\n         * Note that the buffer located on the CPU is NOT updated! It's up to you to update it (or not) with the same data you pass to this method\r\n         * @param kind name of the attribute to update. Use \"matrix\" to update the buffer of matrices\r\n         * @param data the data to set in the GPU buffer\r\n         * @param offset the offset in the GPU buffer where to update the data\r\n         */\r\n        thinInstancePartialBufferUpdate(kind: string, data: Float32Array, offset: number): void;\r\n\r\n        /**\r\n         * Refreshes the bounding info, taking into account all the thin instances defined\r\n         * @param forceRefreshParentInfo true to force recomputing the mesh bounding info and use it to compute the aggregated bounding info\r\n         * @param applySkeleton defines whether to apply the skeleton before computing the bounding info\r\n         * @param applyMorph  defines whether to apply the morph target before computing the bounding info\r\n         */\r\n        thinInstanceRefreshBoundingInfo(forceRefreshParentInfo?: boolean, applySkeleton?: boolean, applyMorph?: boolean): void;\r\n\r\n        /** @internal */\r\n        _thinInstanceInitializeUserStorage(): void;\r\n\r\n        /** @internal */\r\n        _thinInstanceUpdateBufferSize(kind: string, numInstances?: number): void;\r\n\r\n        /** @internal */\r\n        _thinInstanceCreateMatrixBuffer(kind: string, buffer: Nullable<Float32Array>, staticBuffer: boolean): Buffer;\r\n\r\n        /** @internal */\r\n        _thinInstanceRecreateBuffer(kind: string, staticBuffer?: boolean): void;\r\n\r\n        /** @internal */\r\n        _userThinInstanceBuffersStorage: {\r\n            data: { [key: string]: Float32Array };\r\n            sizes: { [key: string]: number };\r\n            vertexBuffers: { [key: string]: Nullable<VertexBuffer> };\r\n            strides: { [key: string]: number };\r\n        };\r\n    }\r\n}\r\n\r\nMesh.prototype.thinInstanceAdd = function (matrix: DeepImmutableObject<Matrix> | Array<DeepImmutableObject<Matrix>>, refresh: boolean = true): number {\r\n    if (!this.getScene().getEngine().getCaps().instancedArrays) {\r\n        Logger.Error(\"Thin Instances are not supported on this device as Instanced Array extension not supported\");\r\n        return -1;\r\n    }\r\n\r\n    this._thinInstanceUpdateBufferSize(\"matrix\", Array.isArray(matrix) ? matrix.length : 1);\r\n\r\n    const index = this._thinInstanceDataStorage.instancesCount;\r\n\r\n    if (Array.isArray(matrix)) {\r\n        for (let i = 0; i < matrix.length; ++i) {\r\n            this.thinInstanceSetMatrixAt(this._thinInstanceDataStorage.instancesCount++, matrix[i], i === matrix.length - 1 && refresh);\r\n        }\r\n    } else {\r\n        this.thinInstanceSetMatrixAt(this._thinInstanceDataStorage.instancesCount++, matrix, refresh);\r\n    }\r\n\r\n    return index;\r\n};\r\n\r\nMesh.prototype.thinInstanceAddSelf = function (refresh: boolean = true): number {\r\n    return this.thinInstanceAdd(Matrix.IdentityReadOnly, refresh);\r\n};\r\n\r\nMesh.prototype.thinInstanceRegisterAttribute = function (kind: string, stride: number): void {\r\n    // preserve backward compatibility\r\n    if (kind === VertexBuffer.ColorKind) {\r\n        kind = VertexBuffer.ColorInstanceKind;\r\n    }\r\n\r\n    this.removeVerticesData(kind);\r\n\r\n    this._thinInstanceInitializeUserStorage();\r\n\r\n    this._userThinInstanceBuffersStorage.strides[kind] = stride;\r\n    this._userThinInstanceBuffersStorage.sizes[kind] = stride * Math.max(32, this._thinInstanceDataStorage.instancesCount); // Initial size\r\n    this._userThinInstanceBuffersStorage.data[kind] = new Float32Array(this._userThinInstanceBuffersStorage.sizes[kind]);\r\n    this._userThinInstanceBuffersStorage.vertexBuffers[kind] = new VertexBuffer(this.getEngine(), this._userThinInstanceBuffersStorage.data[kind], kind, true, false, stride, true);\r\n\r\n    this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[kind]!);\r\n};\r\n\r\nMesh.prototype.thinInstanceSetMatrixAt = function (index: number, matrix: DeepImmutableObject<Matrix>, refresh: boolean = true): boolean {\r\n    if (!this._thinInstanceDataStorage.matrixData || index >= this._thinInstanceDataStorage.instancesCount) {\r\n        return false;\r\n    }\r\n\r\n    const matrixData = this._thinInstanceDataStorage.matrixData;\r\n\r\n    matrix.copyToArray(matrixData, index * 16);\r\n\r\n    if (this._thinInstanceDataStorage.worldMatrices) {\r\n        this._thinInstanceDataStorage.worldMatrices[index] = matrix as Matrix;\r\n    }\r\n\r\n    if (refresh) {\r\n        this.thinInstanceBufferUpdated(\"matrix\");\r\n\r\n        if (!this.doNotSyncBoundingInfo) {\r\n            this.thinInstanceRefreshBoundingInfo(false);\r\n        }\r\n    }\r\n\r\n    return true;\r\n};\r\n\r\nMesh.prototype.thinInstanceSetAttributeAt = function (kind: string, index: number, value: Array<number>, refresh: boolean = true): boolean {\r\n    // preserve backward compatibility\r\n    if (kind === VertexBuffer.ColorKind) {\r\n        kind = VertexBuffer.ColorInstanceKind;\r\n    }\r\n\r\n    if (!this._userThinInstanceBuffersStorage || !this._userThinInstanceBuffersStorage.data[kind] || index >= this._thinInstanceDataStorage.instancesCount) {\r\n        return false;\r\n    }\r\n\r\n    this._thinInstanceUpdateBufferSize(kind, 0); // make sur the buffer for the kind attribute is big enough\r\n\r\n    this._userThinInstanceBuffersStorage.data[kind].set(value, index * this._userThinInstanceBuffersStorage.strides[kind]);\r\n\r\n    if (refresh) {\r\n        this.thinInstanceBufferUpdated(kind);\r\n    }\r\n\r\n    return true;\r\n};\r\n\r\nObject.defineProperty(Mesh.prototype, \"thinInstanceCount\", {\r\n    get: function (this: Mesh) {\r\n        return this._thinInstanceDataStorage.instancesCount;\r\n    },\r\n    set: function (this: Mesh, value: number) {\r\n        const matrixData = this._thinInstanceDataStorage.matrixData ?? this.source?._thinInstanceDataStorage.matrixData;\r\n        const numMaxInstances = matrixData ? matrixData.length / 16 : 0;\r\n\r\n        if (value <= numMaxInstances) {\r\n            this._thinInstanceDataStorage.instancesCount = value;\r\n        }\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nMesh.prototype._thinInstanceCreateMatrixBuffer = function (kind: string, buffer: Float32Array, staticBuffer: boolean = true): Buffer {\r\n    const matrixBuffer = new Buffer(this.getEngine(), buffer, !staticBuffer, 16, false, true);\r\n\r\n    for (let i = 0; i < 4; i++) {\r\n        this.setVerticesBuffer(matrixBuffer.createVertexBuffer(kind + i, i * 4, 4));\r\n    }\r\n\r\n    return matrixBuffer;\r\n};\r\n\r\nMesh.prototype.thinInstanceSetBuffer = function (kind: string, buffer: Nullable<Float32Array>, stride: number = 0, staticBuffer: boolean = true): void {\r\n    stride = stride || 16;\r\n\r\n    if (kind === \"matrix\") {\r\n        this._thinInstanceDataStorage.matrixBuffer?.dispose();\r\n        this._thinInstanceDataStorage.matrixBuffer = null;\r\n        this._thinInstanceDataStorage.matrixBufferSize = buffer ? buffer.length : 32 * stride;\r\n        this._thinInstanceDataStorage.matrixData = buffer;\r\n        this._thinInstanceDataStorage.worldMatrices = null;\r\n\r\n        if (buffer !== null) {\r\n            this._thinInstanceDataStorage.instancesCount = buffer.length / stride;\r\n            this._thinInstanceDataStorage.matrixBuffer = this._thinInstanceCreateMatrixBuffer(\"world\", buffer, staticBuffer);\r\n\r\n            if (!this.doNotSyncBoundingInfo) {\r\n                this.thinInstanceRefreshBoundingInfo(false);\r\n            }\r\n        } else {\r\n            this._thinInstanceDataStorage.instancesCount = 0;\r\n            if (!this.doNotSyncBoundingInfo) {\r\n                // mesh has no more thin instances, so need to recompute the bounding box because it's the regular mesh that will now be displayed\r\n                this.refreshBoundingInfo();\r\n            }\r\n        }\r\n    } else if (kind === \"previousMatrix\") {\r\n        this._thinInstanceDataStorage.previousMatrixBuffer?.dispose();\r\n        this._thinInstanceDataStorage.previousMatrixBuffer = null;\r\n        this._thinInstanceDataStorage.previousMatrixData = buffer;\r\n        if (buffer !== null) {\r\n            this._thinInstanceDataStorage.previousMatrixBuffer = this._thinInstanceCreateMatrixBuffer(\"previousWorld\", buffer, staticBuffer);\r\n        }\r\n    } else {\r\n        // color for instanced mesh is ColorInstanceKind and not ColorKind because of native that needs to do the differenciation\r\n        // hot switching kind here to preserve backward compatibility\r\n        if (kind === VertexBuffer.ColorKind) {\r\n            kind = VertexBuffer.ColorInstanceKind;\r\n        }\r\n\r\n        if (buffer === null) {\r\n            if (this._userThinInstanceBuffersStorage?.data[kind]) {\r\n                this.removeVerticesData(kind);\r\n                delete this._userThinInstanceBuffersStorage.data[kind];\r\n                delete this._userThinInstanceBuffersStorage.strides[kind];\r\n                delete this._userThinInstanceBuffersStorage.sizes[kind];\r\n                delete this._userThinInstanceBuffersStorage.vertexBuffers[kind];\r\n            }\r\n        } else {\r\n            this._thinInstanceInitializeUserStorage();\r\n\r\n            this._userThinInstanceBuffersStorage.data[kind] = buffer;\r\n            this._userThinInstanceBuffersStorage.strides[kind] = stride;\r\n            this._userThinInstanceBuffersStorage.sizes[kind] = buffer.length;\r\n            this._userThinInstanceBuffersStorage.vertexBuffers[kind] = new VertexBuffer(this.getEngine(), buffer, kind, !staticBuffer, false, stride, true);\r\n\r\n            this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[kind]!);\r\n        }\r\n    }\r\n};\r\n\r\nMesh.prototype.thinInstanceBufferUpdated = function (kind: string): void {\r\n    if (kind === \"matrix\") {\r\n        if (this.thinInstanceAllowAutomaticStaticBufferRecreation && this._thinInstanceDataStorage.matrixBuffer && !this._thinInstanceDataStorage.matrixBuffer.isUpdatable()) {\r\n            this._thinInstanceRecreateBuffer(kind);\r\n        }\r\n        this._thinInstanceDataStorage.matrixBuffer?.updateDirectly(this._thinInstanceDataStorage.matrixData!, 0, this._thinInstanceDataStorage.instancesCount);\r\n    } else if (kind === \"previousMatrix\") {\r\n        if (\r\n            this.thinInstanceAllowAutomaticStaticBufferRecreation &&\r\n            this._thinInstanceDataStorage.previousMatrixBuffer &&\r\n            !this._thinInstanceDataStorage.previousMatrixBuffer.isUpdatable()\r\n        ) {\r\n            this._thinInstanceRecreateBuffer(kind);\r\n        }\r\n        this._thinInstanceDataStorage.previousMatrixBuffer?.updateDirectly(this._thinInstanceDataStorage.previousMatrixData!, 0, this._thinInstanceDataStorage.instancesCount);\r\n    } else {\r\n        // preserve backward compatibility\r\n        if (kind === VertexBuffer.ColorKind) {\r\n            kind = VertexBuffer.ColorInstanceKind;\r\n        }\r\n\r\n        if (this._userThinInstanceBuffersStorage?.vertexBuffers[kind]) {\r\n            if (this.thinInstanceAllowAutomaticStaticBufferRecreation && !this._userThinInstanceBuffersStorage.vertexBuffers[kind]!.isUpdatable()) {\r\n                this._thinInstanceRecreateBuffer(kind);\r\n            }\r\n            this._userThinInstanceBuffersStorage.vertexBuffers[kind]!.updateDirectly(this._userThinInstanceBuffersStorage.data[kind], 0);\r\n        }\r\n    }\r\n};\r\n\r\nMesh.prototype.thinInstancePartialBufferUpdate = function (kind: string, data: Float32Array, offset: number): void {\r\n    if (kind === \"matrix\") {\r\n        if (this._thinInstanceDataStorage.matrixBuffer) {\r\n            this._thinInstanceDataStorage.matrixBuffer.updateDirectly(data, offset);\r\n        }\r\n    } else {\r\n        // preserve backward compatibility\r\n        if (kind === VertexBuffer.ColorKind) {\r\n            kind = VertexBuffer.ColorInstanceKind;\r\n        }\r\n\r\n        if (this._userThinInstanceBuffersStorage?.vertexBuffers[kind]) {\r\n            this._userThinInstanceBuffersStorage.vertexBuffers[kind]!.updateDirectly(data, offset);\r\n        }\r\n    }\r\n};\r\n\r\nMesh.prototype.thinInstanceGetWorldMatrices = function (): Matrix[] {\r\n    if (!this._thinInstanceDataStorage.matrixData || !this._thinInstanceDataStorage.matrixBuffer) {\r\n        return [];\r\n    }\r\n    const matrixData = this._thinInstanceDataStorage.matrixData;\r\n\r\n    if (!this._thinInstanceDataStorage.worldMatrices) {\r\n        this._thinInstanceDataStorage.worldMatrices = [] as Matrix[];\r\n\r\n        for (let i = 0; i < this._thinInstanceDataStorage.instancesCount; ++i) {\r\n            this._thinInstanceDataStorage.worldMatrices[i] = Matrix.FromArray(matrixData, i * 16);\r\n        }\r\n    }\r\n\r\n    return this._thinInstanceDataStorage.worldMatrices;\r\n};\r\n\r\nMesh.prototype.thinInstanceRefreshBoundingInfo = function (forceRefreshParentInfo: boolean = false, applySkeleton: boolean = false, applyMorph: boolean = false) {\r\n    if (!this._thinInstanceDataStorage.matrixData || !this._thinInstanceDataStorage.matrixBuffer) {\r\n        return;\r\n    }\r\n\r\n    const vectors = this._thinInstanceDataStorage.boundingVectors;\r\n\r\n    if (forceRefreshParentInfo || !this.rawBoundingInfo) {\r\n        vectors.length = 0;\r\n        this.refreshBoundingInfo(applySkeleton, applyMorph);\r\n        const boundingInfo = this.getBoundingInfo();\r\n        this.rawBoundingInfo = new BoundingInfo(boundingInfo.minimum, boundingInfo.maximum);\r\n    }\r\n\r\n    const boundingInfo = this.getBoundingInfo();\r\n    const matrixData = this._thinInstanceDataStorage.matrixData;\r\n\r\n    if (vectors.length === 0) {\r\n        for (let v = 0; v < boundingInfo.boundingBox.vectors.length; ++v) {\r\n            vectors.push(boundingInfo.boundingBox.vectors[v].clone());\r\n        }\r\n    }\r\n\r\n    TmpVectors.Vector3[0].setAll(Number.POSITIVE_INFINITY); // min\r\n    TmpVectors.Vector3[1].setAll(Number.NEGATIVE_INFINITY); // max\r\n\r\n    for (let i = 0; i < this._thinInstanceDataStorage.instancesCount; ++i) {\r\n        Matrix.FromArrayToRef(matrixData, i * 16, TmpVectors.Matrix[0]);\r\n\r\n        for (let v = 0; v < vectors.length; ++v) {\r\n            Vector3.TransformCoordinatesToRef(vectors[v], TmpVectors.Matrix[0], TmpVectors.Vector3[2]);\r\n            TmpVectors.Vector3[0].minimizeInPlace(TmpVectors.Vector3[2]);\r\n            TmpVectors.Vector3[1].maximizeInPlace(TmpVectors.Vector3[2]);\r\n        }\r\n    }\r\n\r\n    boundingInfo.reConstruct(TmpVectors.Vector3[0], TmpVectors.Vector3[1]);\r\n\r\n    this._updateBoundingInfo();\r\n};\r\n\r\nMesh.prototype._thinInstanceRecreateBuffer = function (kind: string, staticBuffer: boolean = true) {\r\n    if (kind === \"matrix\") {\r\n        this._thinInstanceDataStorage.matrixBuffer?.dispose();\r\n        this._thinInstanceDataStorage.matrixBuffer = this._thinInstanceCreateMatrixBuffer(\"world\", this._thinInstanceDataStorage.matrixData, staticBuffer);\r\n    } else if (kind === \"previousMatrix\") {\r\n        if (this._scene.needsPreviousWorldMatrices) {\r\n            this._thinInstanceDataStorage.previousMatrixBuffer?.dispose();\r\n            this._thinInstanceDataStorage.previousMatrixBuffer = this._thinInstanceCreateMatrixBuffer(\r\n                \"previousWorld\",\r\n                this._thinInstanceDataStorage.previousMatrixData ?? this._thinInstanceDataStorage.matrixData,\r\n                staticBuffer\r\n            );\r\n        }\r\n    } else {\r\n        if (kind === VertexBuffer.ColorKind) {\r\n            kind = VertexBuffer.ColorInstanceKind;\r\n        }\r\n\r\n        this._userThinInstanceBuffersStorage.vertexBuffers[kind]?.dispose();\r\n        this._userThinInstanceBuffersStorage.vertexBuffers[kind] = new VertexBuffer(\r\n            this.getEngine(),\r\n            this._userThinInstanceBuffersStorage.data[kind],\r\n            kind,\r\n            !staticBuffer,\r\n            false,\r\n            this._userThinInstanceBuffersStorage.strides[kind],\r\n            true\r\n        );\r\n        this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[kind]!);\r\n    }\r\n};\r\n\r\nMesh.prototype._thinInstanceUpdateBufferSize = function (kind: string, numInstances: number = 1) {\r\n    // preserve backward compatibility\r\n    if (kind === VertexBuffer.ColorKind) {\r\n        kind = VertexBuffer.ColorInstanceKind;\r\n    }\r\n\r\n    const kindIsMatrix = kind === \"matrix\";\r\n\r\n    if (!kindIsMatrix && (!this._userThinInstanceBuffersStorage || !this._userThinInstanceBuffersStorage.strides[kind])) {\r\n        return;\r\n    }\r\n\r\n    const stride = kindIsMatrix ? 16 : this._userThinInstanceBuffersStorage.strides[kind];\r\n    const currentSize = kindIsMatrix ? this._thinInstanceDataStorage.matrixBufferSize : this._userThinInstanceBuffersStorage.sizes[kind];\r\n    let data = kindIsMatrix ? this._thinInstanceDataStorage.matrixData : this._userThinInstanceBuffersStorage.data[kind];\r\n\r\n    const bufferSize = (this._thinInstanceDataStorage.instancesCount + numInstances) * stride;\r\n\r\n    let newSize = currentSize;\r\n\r\n    while (newSize < bufferSize) {\r\n        newSize *= 2;\r\n    }\r\n\r\n    if (!data || currentSize != newSize) {\r\n        if (!data) {\r\n            data = new Float32Array(newSize);\r\n        } else {\r\n            const newData = new Float32Array(newSize);\r\n            newData.set(data, 0);\r\n            data = newData;\r\n        }\r\n\r\n        if (kindIsMatrix) {\r\n            this._thinInstanceDataStorage.matrixBuffer?.dispose();\r\n            this._thinInstanceDataStorage.matrixBuffer = this._thinInstanceCreateMatrixBuffer(\"world\", data, false);\r\n            this._thinInstanceDataStorage.matrixData = data;\r\n            this._thinInstanceDataStorage.matrixBufferSize = newSize;\r\n            if (this._scene.needsPreviousWorldMatrices && !this._thinInstanceDataStorage.previousMatrixData) {\r\n                this._thinInstanceDataStorage.previousMatrixBuffer?.dispose();\r\n                this._thinInstanceDataStorage.previousMatrixBuffer = this._thinInstanceCreateMatrixBuffer(\"previousWorld\", data, false);\r\n            }\r\n        } else {\r\n            this._userThinInstanceBuffersStorage.vertexBuffers[kind]?.dispose();\r\n\r\n            this._userThinInstanceBuffersStorage.data[kind] = data;\r\n            this._userThinInstanceBuffersStorage.sizes[kind] = newSize;\r\n            this._userThinInstanceBuffersStorage.vertexBuffers[kind] = new VertexBuffer(this.getEngine(), data, kind, true, false, stride, true);\r\n\r\n            this.setVerticesBuffer(this._userThinInstanceBuffersStorage.vertexBuffers[kind]!);\r\n        }\r\n    }\r\n};\r\n\r\nMesh.prototype._thinInstanceInitializeUserStorage = function () {\r\n    if (!this._userThinInstanceBuffersStorage) {\r\n        this._userThinInstanceBuffersStorage = {\r\n            data: {},\r\n            sizes: {},\r\n            vertexBuffers: {},\r\n            strides: {},\r\n        };\r\n    }\r\n};\r\n\r\nMesh.prototype._disposeThinInstanceSpecificData = function () {\r\n    if (this._thinInstanceDataStorage?.matrixBuffer) {\r\n        this._thinInstanceDataStorage.matrixBuffer.dispose();\r\n        this._thinInstanceDataStorage.matrixBuffer = null;\r\n    }\r\n};\r\n"]}