{"version": 3, "file": "WebXRHandTracking.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRHandTracking.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAKjF,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAE7D,OAAO,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AAInE,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAGnD,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,YAAY,EAAE,MAAM,mCAAmC,CAAC;AAEjE,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACpD,OAAO,EAAE,eAAe,EAAE,MAAM,wCAAwC,CAAC;AACzE,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,IAAI,EAAE,MAAM,uBAAuB,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAoGpD;;GAEG;AACH,MAAM,CAAN,IAAY,QAyBX;AAzBD,WAAY,QAAQ;IAChB;;OAEG;IACH,2BAAe,CAAA;IACf;;OAEG;IACH,2BAAe,CAAA;IACf;;OAEG;IACH,2BAAe,CAAA;IACf;;OAEG;IACH,6BAAiB,CAAA;IACjB;;OAEG;IACH,yBAAa,CAAA;IACb;;OAEG;IACH,6BAAiB,CAAA;AACrB,CAAC,EAzBW,QAAQ,KAAR,QAAQ,QAyBnB;AAED;;;GAGG;AACH,MAAM,CAAN,IAAY,cAwDX;AAxDD,WAAY,cAAc;IACtB,YAAY;IACZ,iCAAe,CAAA;IAEf,uBAAuB;IACvB,uDAAqC,CAAA;IACrC,0BAA0B;IAC1B,mEAAiD,CAAA;IACjD,2BAA2B;IAC3B,+DAA6C,CAAA;IAC7C,gBAAgB;IAChB,yCAAuB,CAAA;IAEvB,8BAA8B;IAC9B,qEAAmD,CAAA;IACnD,iCAAiC;IACjC,iFAA+D,CAAA;IAC/D,kCAAkC;IAClC,yFAAuE,CAAA;IACvE,iCAAiC;IACjC,6EAA2D,CAAA;IAC3D,uBAAuB;IACvB,uDAAqC,CAAA;IAErC,+BAA+B;IAC/B,uEAAqD,CAAA;IACrD,kCAAkC;IAClC,mFAAiE,CAAA;IACjE,mCAAmC;IACnC,2FAAyE,CAAA;IACzE,kCAAkC;IAClC,+EAA6D,CAAA;IAC7D,wBAAwB;IACxB,yDAAuC,CAAA;IAEvC,6BAA6B;IAC7B,mEAAiD,CAAA;IACjD,gCAAgC;IAChC,+EAA6D,CAAA;IAC7D,iCAAiC;IACjC,uFAAqE,CAAA;IACrE,gCAAgC;IAChC,2EAAyD,CAAA;IACzD,sBAAsB;IACtB,qDAAmC,CAAA;IAEnC,8BAA8B;IAC9B,qEAAmD,CAAA;IACnD,iCAAiC;IACjC,iFAA+D,CAAA;IAC/D,kCAAkC;IAClC,yFAAuE,CAAA;IACvE,iCAAiC;IACjC,6EAA2D,CAAA;IAC3D,uBAAuB;IACvB,uDAAqC,CAAA;AACzC,CAAC,EAxDW,cAAc,KAAd,cAAc,QAwDzB;AAKD,MAAM,uBAAuB,GAAqB;IAC9C,cAAc,CAAC,KAAK;IACpB,cAAc,CAAC,gBAAgB;IAC/B,cAAc,CAAC,sBAAsB;IACrC,cAAc,CAAC,oBAAoB;IACnC,cAAc,CAAC,SAAS;IACxB,cAAc,CAAC,uBAAuB;IACtC,cAAc,CAAC,6BAA6B;IAC5C,cAAc,CAAC,iCAAiC;IAChD,cAAc,CAAC,2BAA2B;IAC1C,cAAc,CAAC,gBAAgB;IAC/B,cAAc,CAAC,wBAAwB;IACvC,cAAc,CAAC,8BAA8B;IAC7C,cAAc,CAAC,kCAAkC;IACjD,cAAc,CAAC,4BAA4B;IAC3C,cAAc,CAAC,iBAAiB;IAChC,cAAc,CAAC,sBAAsB;IACrC,cAAc,CAAC,4BAA4B;IAC3C,cAAc,CAAC,gCAAgC;IAC/C,cAAc,CAAC,0BAA0B;IACzC,cAAc,CAAC,eAAe;IAC9B,cAAc,CAAC,uBAAuB;IACtC,cAAc,CAAC,6BAA6B;IAC5C,cAAc,CAAC,iCAAiC;IAChD,cAAc,CAAC,2BAA2B;IAC1C,cAAc,CAAC,gBAAgB;CAClC,CAAC;AAEF,MAAM,mBAAmB,GAA4C;IACjE,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC;IACxC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,gBAAgB,EAAE,cAAc,CAAC,sBAAsB,EAAE,cAAc,CAAC,oBAAoB,EAAE,cAAc,CAAC,SAAS,CAAC;IACzJ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACd,cAAc,CAAC,uBAAuB;QACtC,cAAc,CAAC,6BAA6B;QAC5C,cAAc,CAAC,iCAAiC;QAChD,cAAc,CAAC,2BAA2B;QAC1C,cAAc,CAAC,gBAAgB;KAClC;IACD,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QACf,cAAc,CAAC,wBAAwB;QACvC,cAAc,CAAC,8BAA8B;QAC7C,cAAc,CAAC,kCAAkC;QACjD,cAAc,CAAC,4BAA4B;QAC3C,cAAc,CAAC,iBAAiB;KACnC;IACD,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACb,cAAc,CAAC,sBAAsB;QACrC,cAAc,CAAC,4BAA4B;QAC3C,cAAc,CAAC,gCAAgC;QAC/C,cAAc,CAAC,0BAA0B;QACzC,cAAc,CAAC,eAAe;KACjC;IACD,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QACf,cAAc,CAAC,uBAAuB;QACtC,cAAc,CAAC,6BAA6B;QAC5C,cAAc,CAAC,iCAAiC;QAChD,cAAc,CAAC,2BAA2B;QAC1C,cAAc,CAAC,gBAAgB;KAClC;CACJ,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,SAAS;IAoBlB;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,IAAc;QACnC,OAAO,mBAAmB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC;IAC9G,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,SAAyB;QACzC,OAAO,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,OAAO,CAAC,SAAS,CAAE,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;;;;;;;;OAYG;IACH;IACI,mDAAmD;IACnC,YAA8B,EAC7B,YAA4B,EACrC,SAAiC;IACzC;sGACkG;IACzF,UAA0C,EAClC,oBAA6B,KAAK,EAClC,mBAA4B,KAAK,EACjC,oBAA4B,CAAC;QAR9B,iBAAY,GAAZ,YAAY,CAAkB;QAC7B,iBAAY,GAAZ,YAAY,CAAgB;QACrC,cAAS,GAAT,SAAS,CAAwB;QAGhC,eAAU,GAAV,UAAU,CAAgC;QAClC,sBAAiB,GAAjB,iBAAiB,CAAiB;QAClC,qBAAgB,GAAhB,gBAAgB,CAAiB;QACjC,sBAAiB,GAAjB,iBAAiB,CAAY;QAjElD;;WAEG;QACK,qBAAgB,GAAG,IAAI,KAAK,CAAgB,uBAAuB,CAAC,MAAM,CAAC,CAAC;QAEpF;;WAEG;QACK,4BAAuB,GAAG,IAAI,YAAY,CAAC,uBAAuB,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;QAEhF,qBAAgB,GAAG,IAAI,MAAM,EAAE,CAAC;QAExC;;WAEG;QACK,gBAAW,GAAG,IAAI,YAAY,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QAoDnE,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QAEzC,mFAAmF;QACnF,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;YACxE,MAAM,cAAc,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,IAAI,aAAa,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAC7H,cAAc,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;YAErD,mEAAmE;YACnE,YAAY,CAAC,QAAQ,CAAC,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;SAChE;QAED,IAAI,SAAS,EAAE;YACX,+FAA+F;YAC/F,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;SAC3C;QAED,kDAAkD;QAClD,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;YACpC,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,QAAQ,EAAE;gBAC7C,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;aACpE;SACJ;QAED,IAAI,CAAC,YAAY,CAAC,gCAAgC,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,EAAE;YACxE,gBAAgB,CAAC,wBAAwB,GAAG,IAAI,CAAC;QACrD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,QAAsB,EAAE,UAA0C,EAAE,iBAAuC;QAC1H,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE1B,gGAAgG;QAChG,QAAQ,CAAC,wBAAwB,GAAG,IAAI,CAAC;QACzC,QAAQ,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACvC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,yGAAyG;QACzG,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;YACzB,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;YACjD,uBAAuB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;gBACpD,MAAM,YAAY,GAAG,gBAAgB,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;gBACzG,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE;oBACrB,gBAAgB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;iBAC3F;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,OAAgB,EAAE,cAAgC;QACvE,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAChD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QAED,oFAAoF;QACpF,MAAM,OAAO,GAAQ,IAAI,CAAC;QAC1B,MAAM,WAAW,GAAmB,uBAAuB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;QAC1H,IAAI,kBAAkB,GAAG,KAAK,CAAC;QAE/B,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,cAAc,EAAE;YAC7C,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,cAAc,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;SAC9J;aAAM,IAAI,OAAO,CAAC,YAAY,EAAE;YAC7B,kBAAkB,GAAG,IAAI,CAAC;YAC1B,sEAAsE;YACtE,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;gBAC9D,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,cAAc,CAAC,CAAC;gBAC9E,IAAI,SAAS,EAAE;oBACX,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC;oBAC5E,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,MAAM,IAAI,KAAK,CAAC;iBAC1D;qBAAM;oBACH,kBAAkB,GAAG,KAAK,CAAC;oBAC3B,MAAM;iBACT;aACJ;SACJ;QAED,IAAI,CAAC,kBAAkB,EAAE;YACrB,OAAO;SACV;QAED,uBAAuB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;YACrD,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACvD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,uBAAuB,EAAE,QAAQ,GAAG,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC1F,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,EAAE,cAAc,CAAC,kBAAmB,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC;YAExG,4GAA4G;YAC5G,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;YAE9E,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC9C,SAAS,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC;YAChE,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACrD,SAAS,CAAC,kBAAmB,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAmB,CAAC,CAAC;YAC3E,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAE5C,iFAAiF;YACjF,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBACnC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC3B,SAAS,CAAC,kBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACtC,SAAS,CAAC,kBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAEtC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,SAAS,EAAE;oBAC1C,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBAChC,cAAc,CAAC,kBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC3C,cAAc,CAAC,kBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;iBAC9C;aACJ;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;SACnC;IACL,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,aAAa,GAAG,KAAK;QAChC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,aAAa,EAAE;gBACf,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;gBACnC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;aACvC;iBAAM;gBACH,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,KAAK,CAAC;aACpC;SACJ;IACL,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,oBAAoB;IA2B/C,MAAM,CAAC,2BAA2B,CAAC,cAAyC;QAChF,MAAM,MAAM,GAA6C,EAAE,CAAC;QAC5D,CAAC,MAAsB,EAAE,OAAuB,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YACjE,MAAM,aAAa,GAAG,EAAE,CAAC;YACzB,MAAM,YAAY,GAAG,cAAc,CAAC,WAAW,EAAE,UAAU,IAAI,eAAe,CAAC,aAAa,EAAE,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YACnI,YAAY,CAAC,SAAS,GAAG,CAAC,CAAC,cAAc,CAAC,WAAW,EAAE,mBAAmB,CAAC;YAC3E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,uBAAuB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACrD,IAAI,WAAW,GAAiB,YAAY,CAAC,cAAc,CAAC,GAAG,UAAU,cAAc,CAAC,EAAE,CAAC,CAAC;gBAC5F,IAAI,cAAc,CAAC,WAAW,EAAE,wBAAwB,EAAE;oBACtD,MAAM,YAAY,GAAG,cAAc,CAAC,WAAW,CAAC,wBAAwB,CAAC,WAA4B,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;oBACtH,IAAI,YAAY,EAAE;wBACd,IAAI,YAAY,KAAK,WAAW,EAAE;4BAC9B,WAAW,CAAC,OAAO,EAAE,CAAC;4BACtB,WAAW,GAAG,YAAY,CAAC;yBAC9B;qBACJ;iBACJ;gBACD,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC;gBAC/B,IAAI,cAAc,CAAC,WAAW,EAAE,aAAa,EAAE;oBAC3C,MAAM,KAAK,GAAG,cAAc,CAAC,WAAW,EAAE,YAAY,IAAI,EAAE,CAAC;oBAC7D,wEAAwE;oBACxE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBACjC,MAAM,IAAI,GAAG,KAAK,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC;oBACpG,WAAW,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC;iBAC/F;gBACD,WAAW,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;gBAClD,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC;gBAC9B,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aACnC;YAED,MAAM,CAAC,UAAU,CAAC,GAAG,aAAa,CAAC;QACvC,CAAC,CAAC,CAAC;QACH,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;IACtD,CAAC;IAEO,MAAM,CAAC,+BAA+B,CAC1C,KAAY,EACZ,gBAAqC,EACrC,OAAmC;QAEnC,qDAAqD;QACrD,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACjC,MAAM,YAAY,GAA2C,EAAE,CAAC;YAChE,6BAA6B;YAC7B,IAAI,iBAAiB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE;gBAC1D,iBAAiB,CAAC,aAAa,GAAG,IAAI,CAAC;aAC1C;YACD,IAAI,iBAAiB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE;gBACzD,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC;aACzC;YAED,MAAM,YAAY,GAAG,CAAC,CAAC,CAAC,iBAAiB,CAAC,aAAa,IAAI,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC3F,wBAAwB;YACxB,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC/B,iBAAiB,CAAC,aAAa;oBAC3B,WAAW,CAAC,eAAe,CAAC,EAAE,EAAE,iBAAiB,CAAC,2BAA2B,EAAE,iBAAiB,CAAC,iCAAiC,EAAE,KAAK,CAAC;gBAC9I,iBAAiB,CAAC,YAAY;oBAC1B,WAAW,CAAC,eAAe,CAAC,EAAE,EAAE,iBAAiB,CAAC,2BAA2B,EAAE,iBAAiB,CAAC,gCAAgC,EAAE,KAAK,CAAC;aAChJ,CAAC,CAAC;YACH,iBAAiB,CAAC,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC9C,iBAAiB,CAAC,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE7C,MAAM,UAAU,GAAG,MAAM,YAAY,CAAC,kBAAkB,CAAC,YAAY,EAAE,iBAAiB,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YAE/H,+BAA+B;YAC/B,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC;YACnC,UAAU,CAAC,gBAAgB,GAAG,QAAQ,CAAC,mBAAmB,CAAC;YAC3D,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC,aAAa,CAAC;YAE/C,uBAAuB;YACvB,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAExB,SAAS;YACT,MAAM,UAAU,GAAG;gBACf,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;gBACnC,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;gBACvC,WAAW,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;gBAC3C,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;gBAC1C,GAAG,OAAO,EAAE,UAAU,EAAE,YAAY;aACvC,CAAC;YAEF,MAAM,SAAS,GAAG;gBACd,IAAI,EAAE,UAAU,CAAC,cAAc,CAAC,WAAW,CAAe;gBAC1D,OAAO,EAAE,UAAU,CAAC,cAAc,CAAC,cAAc,CAAe;gBAChE,WAAW,EAAE,UAAU,CAAC,cAAc,CAAC,aAAa,CAAe;gBACnE,UAAU,EAAE,UAAU,CAAC,cAAc,CAAC,iBAAiB,CAAe;aACzE,CAAC;YAEF,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC;YACvC,SAAS,CAAC,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC;YAC7C,SAAS,CAAC,WAAW,CAAC,KAAK,GAAG,UAAU,CAAC,WAAW,CAAC;YACrD,SAAS,CAAC,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,UAAU,CAAC;YACnD,MAAM,WAAW,GAAI,gBAAgB,CAAC,oBAAoB,EAAmC,EAAE,WAAW,CAAC;YAC3G,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACrC,MAAM,OAAO,GAAG,UAAU,IAAI,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,CAAC,aAAa,CAAC;gBACxG,IAAI,CAAC,OAAO,EAAE;oBACV,4BAA4B;oBAC5B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;iBAChD;gBACD,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACnC,QAAQ,CAAC,6BAA6B,CAAC,yBAAyB,GAAG,IAAI,CAAC;gBACxE,0CAA0C;gBAC1C,IAAI,CAAC,WAAW,EAAE;oBACd,QAAQ,CAAC,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,UAAU,iBAAiB,EAAE,IAAI,CAAC,CAAC;iBAC9E;gBACD,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;gBAE3B,YAAY,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;gBAEpC,wCAAwC;gBACxC,IAAI,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE;oBAC9C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;iBAC7C;YACL,CAAC,CAAC,CAAC;YAEH,UAAU,CAAC,OAAO,EAAE,CAAC;YACrB,OAAO,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACK,MAAM,CAAC,kCAAkC,CAAC,UAAwB;QACtE,MAAM,CAAC,GAAG,UAAU,IAAI,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC5C,OAAO;YACH,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC,EAAE;YACpC,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE,oBAAoB,CAAC,EAAE;YAC1D,CAAC,cAAc,CAAC,sBAAsB,CAAC,EAAE,qBAAqB,CAAC,EAAE;YACjE,CAAC,cAAc,CAAC,oBAAoB,CAAC,EAAE,qBAAqB,CAAC,EAAE;YAC/D,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,aAAa,CAAC,EAAE;YAC5C,CAAC,cAAc,CAAC,uBAAuB,CAAC,EAAE,oBAAoB,CAAC,EAAE;YACjE,CAAC,cAAc,CAAC,6BAA6B,CAAC,EAAE,qBAAqB,CAAC,EAAE;YACxE,CAAC,cAAc,CAAC,iCAAiC,CAAC,EAAE,oBAAoB,CAAC,EAAE;YAC3E,CAAC,cAAc,CAAC,2BAA2B,CAAC,EAAE,qBAAqB,CAAC,EAAE;YACtE,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE,aAAa,CAAC,EAAE;YACnD,CAAC,cAAc,CAAC,wBAAwB,CAAC,EAAE,qBAAqB,CAAC,EAAE;YACnE,CAAC,cAAc,CAAC,8BAA8B,CAAC,EAAE,sBAAsB,CAAC,EAAE;YAC1E,CAAC,cAAc,CAAC,kCAAkC,CAAC,EAAE,qBAAqB,CAAC,EAAE;YAC7E,CAAC,cAAc,CAAC,4BAA4B,CAAC,EAAE,sBAAsB,CAAC,EAAE;YACxE,CAAC,cAAc,CAAC,iBAAiB,CAAC,EAAE,cAAc,CAAC,EAAE;YACrD,CAAC,cAAc,CAAC,sBAAsB,CAAC,EAAE,mBAAmB,CAAC,EAAE;YAC/D,CAAC,cAAc,CAAC,4BAA4B,CAAC,EAAE,oBAAoB,CAAC,EAAE;YACtE,CAAC,cAAc,CAAC,gCAAgC,CAAC,EAAE,mBAAmB,CAAC,EAAE;YACzE,CAAC,cAAc,CAAC,0BAA0B,CAAC,EAAE,oBAAoB,CAAC,EAAE;YACpE,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,YAAY,CAAC,EAAE;YACjD,CAAC,cAAc,CAAC,uBAAuB,CAAC,EAAE,qBAAqB,CAAC,EAAE;YAClE,CAAC,cAAc,CAAC,6BAA6B,CAAC,EAAE,sBAAsB,CAAC,EAAE;YACzE,CAAC,cAAc,CAAC,iCAAiC,CAAC,EAAE,qBAAqB,CAAC,EAAE;YAC5E,CAAC,cAAc,CAAC,2BAA2B,CAAC,EAAE,sBAAsB,CAAC,EAAE;YACvE,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE,cAAc,CAAC,EAAE;SACvD,CAAC;IACN,CAAC;IA4BD;;;;OAIG;IACI,YAAY;QACf,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,YAAoB;QAC7C,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,UAAwB;QAC/C,IAAI,UAAU,IAAI,MAAM,EAAE;YACtB,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACH,YACI,iBAAsC;IACtC,qDAAqD;IACrC,OAAkC;QAElD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFT,YAAO,GAAP,OAAO,CAA2B;QAhE9C,mBAAc,GAElB,EAAE,CAAC;QAEC,mBAAc,GAGlB,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;QAExB,mBAAc,GAIlB,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;QAEvD,wBAAmB,GAAiF,IAAI,CAAC;QAEjH;;WAEG;QACI,0BAAqB,GAA0B,IAAI,UAAU,EAAE,CAAC;QACvE;;WAEG;QACI,4BAAuB,GAA0B,IAAI,UAAU,EAAE,CAAC;QAuIjE,gBAAW,GAAG,CAAC,YAA8B,EAAE,EAAE;YACrD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,IAAI,YAAY,CAAC,WAAW,CAAC,UAAU,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE;gBACrH,OAAO;aACV;YAED,MAAM,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC,UAAU,CAAC;YACvD,MAAM,SAAS,GAAG,IAAI,SAAS,CAC3B,YAAY,EACZ,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,UAAU,CAAC,EAC3C,IAAI,CAAC,cAAc,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,CAAC,EAC5E,IAAI,CAAC,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,UAAU,CAAC,EAC9E,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,8BAA8B,EACvD,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,EACnC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CACxC,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;YACvD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;YAE5C,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAC1D,CAAC,CAAC;QAeM,gBAAW,GAAG,CAAC,YAA8B,EAAE,EAAE;YACrD,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC;QAjIE,IAAI,CAAC,mBAAmB,GAAG,eAAe,CAAC;QAE3C,sFAAsF;QACtF,MAAM,UAAU,GAAG,OAAc,CAAC;QAClC,MAAM,mBAAmB,GAAG,UAAU,CAAC,WAAW,CAAC;QACnD,IAAI,mBAAmB,EAAE;YACrB,IAAI,OAAO,mBAAmB,CAAC,sBAAsB,KAAK,WAAW,EAAE;gBACnE,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;gBAC9C,OAAO,CAAC,UAAU,CAAC,oBAAoB,GAAG,mBAAmB,CAAC,sBAAsB,CAAC;aACxF;YACD,IAAI,OAAO,mBAAmB,CAAC,UAAU,KAAK,WAAW,EAAE;gBACvD,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;gBAC9C,OAAO,CAAC,UAAU,CAAC,YAAY,GAAG,mBAAmB,CAAC,UAAU,CAAC;aACpE;YACD,IAAI,OAAO,mBAAmB,CAAC,sBAAsB,KAAK,WAAW,EAAE;gBACnE,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;gBAC9C,OAAO,CAAC,UAAU,CAAC,8BAA8B,GAAG,mBAAmB,CAAC,sBAAsB,CAAC;aAClG;YACD,IAAI,OAAO,mBAAmB,CAAC,UAAU,KAAK,WAAW,EAAE;gBACvD,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;gBAC9C,MAAM,cAAc,GAAG,EAAE,CAAC;gBAC1B,MAAM,eAAe,GAAG,EAAE,CAAC;gBAC3B;oBACI,CAAC,mBAAmB,CAAC,UAAU,CAAC,IAAI,EAAE,cAAc,CAAC;oBACrD,CAAC,mBAAmB,CAAC,UAAU,CAAC,KAAK,EAAE,eAAe,CAAC;iBAC1D,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,EAAE;oBAC1B,MAAM,gBAAgB,GAAG,eAAe,CAAC,CAAC,CAAa,CAAC;oBACxD,MAAM,UAAU,GAAG,eAAe,CAAC,CAAC,CAAyB,CAAC;oBAC9D,gBAAgB,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,KAAK,EAAE,EAAE;wBAC/C,UAAU,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,GAAG,cAAc,CAAC;oBAChE,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;gBACH,OAAO,CAAC,UAAU,CAAC,iBAAiB,GAAG;oBACnC,IAAI,EAAE,cAAsC;oBAC5C,KAAK,EAAE,eAAuC;iBACjD,CAAC;aACL;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,cAAc,GAAG;YAClB,WAAW,EAAE,iBAAiB,CAAC,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC;YACxE,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,IAAI,IAAI;YACzD,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,iBAAiB,IAAI,IAAI;SAClE,CAAC;QAEF,kFAAkF;QAClF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,oBAAoB,EAAE;YAC1F,iBAAiB,CAAC,+BAA+B,CAAC,WAAW,CAAC,gBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,EAAE;gBAC9I,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,iBAAiB,CAAC;gBACnD,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG;oBAC9B,IAAI,EAAE,iBAAiB,CAAC,kCAAkC,CAAC,MAAM,CAAC;oBAClE,KAAK,EAAE,iBAAiB,CAAC,kCAAkC,CAAC,OAAO,CAAC;iBACvE,CAAC;gBAEF,sDAAsD;gBACtD,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBACzI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAC5I,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;gBAC9F,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YACnG,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,mCAAmC,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE;gBACzG,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE;oBAChC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,GAAG,cAAc,CAAC,mBAAmB,CAAC,CAAC;oBAC7H,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,GAAG,cAAc,CAAC,mBAAmB,CAAC,CAAC;iBACjI;YACL,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3D,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,2BAA2B,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/F,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,6BAA6B,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAEjG,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,UAAU,CAAC,QAAiB;QAClC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAC7F,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAClG,CAAC;IAwBO,eAAe,CAAC,YAAoB,EAAE,WAAqB;QAC/D,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;QACtD,IAAI,IAAI,EAAE;YACN,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,UAAU,IAAI,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;YACzF,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,YAAY,CAAC,QAAQ,KAAK,YAAY,EAAE;gBACzE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;aAC1C;YACD,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACnD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC1B,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;SAC5C;IACL,CAAC;IAMD;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC,CAAC;QACrI,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,mBAAmB,EAAE;YAC9C,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE;gBACjC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;gBACrF,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;aACzF;SACJ;QAED,8BAA8B;QAC9B,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,iBAAiB,CAAC,mCAAmC,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC/F;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QAErC,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,EAAE;YAC1E,sCAAsC;YACtC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAC9C,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YAC/C,2BAA2B;YAC3B,iBAAiB,CAAC,aAAa,GAAG,IAAI,CAAC;YACvC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC;SACzC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE;YACjC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;YACrF,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;SACzF;IACL,CAAC;;AA5aD;;GAEG;AACoB,sBAAI,GAAG,gBAAgB,CAAC,aAAa,AAAjC,CAAkC;AAC7D;;;;GAIG;AACoB,yBAAO,GAAG,CAAC,AAAJ,CAAK;AAEnC,+CAA+C;AACjC,6CAA2B,GAAG,iDAAiD,AAApD,CAAqD;AAC9F,4DAA4D;AAC9C,mDAAiC,GAAG,gBAAgB,AAAnB,CAAoB;AACnE,2DAA2D;AAC7C,kDAAgC,GAAG,gBAAgB,AAAnB,CAAoB;AAClE,sEAAsE;AACxD,+CAA6B,GAAG,iEAAiE,AAApE,CAAqE;AAEhH,gHAAgH;AACxF,mCAAiB,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,EAAE,AAAhD,CAAiD;AAE3E,+BAAa,GAAsC,IAAI,AAA1C,CAA2C;AACxD,8BAAY,GAAsC,IAAI,AAA1C,CAA2C;AAuZ1E,qBAAqB;AACrB,oBAAoB,CAAC,eAAe,CAChC,iBAAiB,CAAC,IAAI,EACtB,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,iBAAiB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AAClE,CAAC,EACD,iBAAiB,CAAC,OAAO,EACzB,KAAK,CACR,CAAC", "sourcesContent": ["import { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { WebXRFeatureName, WebXRFeaturesManager } from \"../webXRFeaturesManager\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { WebXRInput } from \"../webXRInput\";\r\nimport type { WebXRInputSource } from \"../webXRInputSource\";\r\nimport { Matrix, Quaternion } from \"../../Maths/math.vector\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { PhysicsImpostor } from \"../../Physics/v1/physicsImpostor\";\r\n\r\nimport type { IDisposable, Scene } from \"../../scene\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { InstancedMesh } from \"../../Meshes/instancedMesh\";\r\nimport type { ISceneLoaderAsyncResult } from \"../../Loading/sceneLoader\";\r\nimport { SceneLoader } from \"../../Loading/sceneLoader\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport { NodeMaterial } from \"../../Materials/Node/nodeMaterial\";\r\nimport type { InputBlock } from \"../../Materials/Node/Blocks/Input/inputBlock\";\r\nimport { Material } from \"../../Materials/material\";\r\nimport { CreateIcoSphere } from \"../../Meshes/Builders/icoSphereBuilder\";\r\nimport { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { Axis } from \"../../Maths/math.axis\";\r\nimport { EngineStore } from \"../../Engines/engineStore\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport type { WebXRCompositionLayerWrapper } from \"./Layers/WebXRCompositionLayer\";\r\n\r\ndeclare const XRHand: XRHand;\r\n\r\n/**\r\n * Configuration interface for the hand tracking feature\r\n */\r\nexport interface IWebXRHandTrackingOptions {\r\n    /**\r\n     * The xrInput that will be used as source for new hands\r\n     */\r\n    xrInput: WebXRInput;\r\n\r\n    /**\r\n     * Configuration object for the joint meshes.\r\n     */\r\n    jointMeshes?: {\r\n        /**\r\n         * Should the meshes created be invisible (defaults to false).\r\n         */\r\n        invisible?: boolean;\r\n        /**\r\n         * A source mesh to be used to create instances. Defaults to an icosphere with two subdivisions and smooth lighting.\r\n         * This mesh will be the source for all other (25) meshes.\r\n         * It should have the general size of a single unit, as the instances will be scaled according to the provided radius.\r\n         */\r\n        sourceMesh?: Mesh;\r\n        /**\r\n         * This function will be called after a mesh was created for a specific joint.\r\n         * Using this function you can either manipulate the instance or return a new mesh.\r\n         * When returning a new mesh the instance created before will be disposed.\r\n         * @param meshInstance An instance of the original joint mesh being used for the joint.\r\n         * @param jointId The joint's index, see https://immersive-web.github.io/webxr-hand-input/#skeleton-joints-section for more info.\r\n         * @param hand Which hand (\"left\", \"right\") the joint will be on.\r\n         */\r\n        onHandJointMeshGenerated?: (meshInstance: InstancedMesh, jointId: number, hand: XRHandedness) => AbstractMesh | undefined;\r\n        /**\r\n         * Should the source mesh stay visible (defaults to false).\r\n         */\r\n        keepOriginalVisible?: boolean;\r\n        /**\r\n         * Should each instance have its own physics impostor\r\n         */\r\n        enablePhysics?: boolean;\r\n        /**\r\n         * If enabled, override default physics properties\r\n         */\r\n        physicsProps?: { friction?: number; restitution?: number; impostorType?: number };\r\n        /**\r\n         * Scale factor for all joint meshes (defaults to 1)\r\n         */\r\n        scaleFactor?: number;\r\n    };\r\n\r\n    /**\r\n     * Configuration object for the hand meshes.\r\n     */\r\n    handMeshes?: {\r\n        /**\r\n         * Should the default hand mesh be disabled. In this case, the spheres will be visible (unless set invisible).\r\n         */\r\n        disableDefaultMeshes?: boolean;\r\n        /**\r\n         * Rigged hand meshes that will be tracked to the user's hands. This will override the default hand mesh.\r\n         */\r\n        customMeshes?: {\r\n            right: AbstractMesh;\r\n            left: AbstractMesh;\r\n        };\r\n        /**\r\n         * Are the meshes prepared for a left-handed system. Default hand meshes are right-handed.\r\n         */\r\n        meshesUseLeftHandedCoordinates?: boolean;\r\n        /**\r\n         * If a hand mesh was provided, this array will define what axis will update which node. This will override the default hand mesh\r\n         */\r\n        customRigMappings?: {\r\n            right: XRHandMeshRigMapping;\r\n            left: XRHandMeshRigMapping;\r\n        };\r\n\r\n        /**\r\n         * Override the colors of the hand meshes.\r\n         */\r\n        customColors?: {\r\n            base?: Color3;\r\n            fresnel?: Color3;\r\n            fingerColor?: Color3;\r\n            tipFresnel?: Color3;\r\n        };\r\n\r\n        /**\r\n         * Define whether or not the hand meshes should be disposed on just invisible when the session ends.\r\n         * Not setting, or setting to false, will maintain the hand meshes in the scene after the session ends, which will allow q quicker re-entry into XR.\r\n         */\r\n        disposeOnSessionEnd?: boolean;\r\n    };\r\n}\r\n\r\n/**\r\n * Parts of the hands divided to writs and finger names\r\n */\r\nexport enum HandPart {\r\n    /**\r\n     * HandPart - Wrist\r\n     */\r\n    WRIST = \"wrist\",\r\n    /**\r\n     * HandPart - The thumb\r\n     */\r\n    THUMB = \"thumb\",\r\n    /**\r\n     * HandPart - Index finger\r\n     */\r\n    INDEX = \"index\",\r\n    /**\r\n     * HandPart - Middle finger\r\n     */\r\n    MIDDLE = \"middle\",\r\n    /**\r\n     * HandPart - Ring finger\r\n     */\r\n    RING = \"ring\",\r\n    /**\r\n     * HandPart - Little finger\r\n     */\r\n    LITTLE = \"little\",\r\n}\r\n\r\n/**\r\n * Joints of the hand as defined by the WebXR specification.\r\n * https://immersive-web.github.io/webxr-hand-input/#skeleton-joints-section\r\n */\r\nexport enum WebXRHandJoint {\r\n    /** Wrist */\r\n    WRIST = \"wrist\",\r\n\r\n    /** Thumb near wrist */\r\n    THUMB_METACARPAL = \"thumb-metacarpal\",\r\n    /** Thumb first knuckle */\r\n    THUMB_PHALANX_PROXIMAL = \"thumb-phalanx-proximal\",\r\n    /** Thumb second knuckle */\r\n    THUMB_PHALANX_DISTAL = \"thumb-phalanx-distal\",\r\n    /** Thumb tip */\r\n    THUMB_TIP = \"thumb-tip\",\r\n\r\n    /** Index finger near wrist */\r\n    INDEX_FINGER_METACARPAL = \"index-finger-metacarpal\",\r\n    /** Index finger first knuckle */\r\n    INDEX_FINGER_PHALANX_PROXIMAL = \"index-finger-phalanx-proximal\",\r\n    /** Index finger second knuckle */\r\n    INDEX_FINGER_PHALANX_INTERMEDIATE = \"index-finger-phalanx-intermediate\",\r\n    /** Index finger third knuckle */\r\n    INDEX_FINGER_PHALANX_DISTAL = \"index-finger-phalanx-distal\",\r\n    /** Index finger tip */\r\n    INDEX_FINGER_TIP = \"index-finger-tip\",\r\n\r\n    /** Middle finger near wrist */\r\n    MIDDLE_FINGER_METACARPAL = \"middle-finger-metacarpal\",\r\n    /** Middle finger first knuckle */\r\n    MIDDLE_FINGER_PHALANX_PROXIMAL = \"middle-finger-phalanx-proximal\",\r\n    /** Middle finger second knuckle */\r\n    MIDDLE_FINGER_PHALANX_INTERMEDIATE = \"middle-finger-phalanx-intermediate\",\r\n    /** Middle finger third knuckle */\r\n    MIDDLE_FINGER_PHALANX_DISTAL = \"middle-finger-phalanx-distal\",\r\n    /** Middle finger tip */\r\n    MIDDLE_FINGER_TIP = \"middle-finger-tip\",\r\n\r\n    /** Ring finger near wrist */\r\n    RING_FINGER_METACARPAL = \"ring-finger-metacarpal\",\r\n    /** Ring finger first knuckle */\r\n    RING_FINGER_PHALANX_PROXIMAL = \"ring-finger-phalanx-proximal\",\r\n    /** Ring finger second knuckle */\r\n    RING_FINGER_PHALANX_INTERMEDIATE = \"ring-finger-phalanx-intermediate\",\r\n    /** Ring finger third knuckle */\r\n    RING_FINGER_PHALANX_DISTAL = \"ring-finger-phalanx-distal\",\r\n    /** Ring finger tip */\r\n    RING_FINGER_TIP = \"ring-finger-tip\",\r\n\r\n    /** Pinky finger near wrist */\r\n    PINKY_FINGER_METACARPAL = \"pinky-finger-metacarpal\",\r\n    /** Pinky finger first knuckle */\r\n    PINKY_FINGER_PHALANX_PROXIMAL = \"pinky-finger-phalanx-proximal\",\r\n    /** Pinky finger second knuckle */\r\n    PINKY_FINGER_PHALANX_INTERMEDIATE = \"pinky-finger-phalanx-intermediate\",\r\n    /** Pinky finger third knuckle */\r\n    PINKY_FINGER_PHALANX_DISTAL = \"pinky-finger-phalanx-distal\",\r\n    /** Pinky finger tip */\r\n    PINKY_FINGER_TIP = \"pinky-finger-tip\",\r\n}\r\n\r\n/** A type encapsulating a dictionary mapping WebXR joints to bone names in a rigged hand mesh.  */\r\nexport type XRHandMeshRigMapping = { [webXRJointName in WebXRHandJoint]: string };\r\n\r\nconst handJointReferenceArray: WebXRHandJoint[] = [\r\n    WebXRHandJoint.WRIST,\r\n    WebXRHandJoint.THUMB_METACARPAL,\r\n    WebXRHandJoint.THUMB_PHALANX_PROXIMAL,\r\n    WebXRHandJoint.THUMB_PHALANX_DISTAL,\r\n    WebXRHandJoint.THUMB_TIP,\r\n    WebXRHandJoint.INDEX_FINGER_METACARPAL,\r\n    WebXRHandJoint.INDEX_FINGER_PHALANX_PROXIMAL,\r\n    WebXRHandJoint.INDEX_FINGER_PHALANX_INTERMEDIATE,\r\n    WebXRHandJoint.INDEX_FINGER_PHALANX_DISTAL,\r\n    WebXRHandJoint.INDEX_FINGER_TIP,\r\n    WebXRHandJoint.MIDDLE_FINGER_METACARPAL,\r\n    WebXRHandJoint.MIDDLE_FINGER_PHALANX_PROXIMAL,\r\n    WebXRHandJoint.MIDDLE_FINGER_PHALANX_INTERMEDIATE,\r\n    WebXRHandJoint.MIDDLE_FINGER_PHALANX_DISTAL,\r\n    WebXRHandJoint.MIDDLE_FINGER_TIP,\r\n    WebXRHandJoint.RING_FINGER_METACARPAL,\r\n    WebXRHandJoint.RING_FINGER_PHALANX_PROXIMAL,\r\n    WebXRHandJoint.RING_FINGER_PHALANX_INTERMEDIATE,\r\n    WebXRHandJoint.RING_FINGER_PHALANX_DISTAL,\r\n    WebXRHandJoint.RING_FINGER_TIP,\r\n    WebXRHandJoint.PINKY_FINGER_METACARPAL,\r\n    WebXRHandJoint.PINKY_FINGER_PHALANX_PROXIMAL,\r\n    WebXRHandJoint.PINKY_FINGER_PHALANX_INTERMEDIATE,\r\n    WebXRHandJoint.PINKY_FINGER_PHALANX_DISTAL,\r\n    WebXRHandJoint.PINKY_FINGER_TIP,\r\n];\r\n\r\nconst handPartsDefinition: { [key in HandPart]: WebXRHandJoint[] } = {\r\n    [HandPart.WRIST]: [WebXRHandJoint.WRIST],\r\n    [HandPart.THUMB]: [WebXRHandJoint.THUMB_METACARPAL, WebXRHandJoint.THUMB_PHALANX_PROXIMAL, WebXRHandJoint.THUMB_PHALANX_DISTAL, WebXRHandJoint.THUMB_TIP],\r\n    [HandPart.INDEX]: [\r\n        WebXRHandJoint.INDEX_FINGER_METACARPAL,\r\n        WebXRHandJoint.INDEX_FINGER_PHALANX_PROXIMAL,\r\n        WebXRHandJoint.INDEX_FINGER_PHALANX_INTERMEDIATE,\r\n        WebXRHandJoint.INDEX_FINGER_PHALANX_DISTAL,\r\n        WebXRHandJoint.INDEX_FINGER_TIP,\r\n    ],\r\n    [HandPart.MIDDLE]: [\r\n        WebXRHandJoint.MIDDLE_FINGER_METACARPAL,\r\n        WebXRHandJoint.MIDDLE_FINGER_PHALANX_PROXIMAL,\r\n        WebXRHandJoint.MIDDLE_FINGER_PHALANX_INTERMEDIATE,\r\n        WebXRHandJoint.MIDDLE_FINGER_PHALANX_DISTAL,\r\n        WebXRHandJoint.MIDDLE_FINGER_TIP,\r\n    ],\r\n    [HandPart.RING]: [\r\n        WebXRHandJoint.RING_FINGER_METACARPAL,\r\n        WebXRHandJoint.RING_FINGER_PHALANX_PROXIMAL,\r\n        WebXRHandJoint.RING_FINGER_PHALANX_INTERMEDIATE,\r\n        WebXRHandJoint.RING_FINGER_PHALANX_DISTAL,\r\n        WebXRHandJoint.RING_FINGER_TIP,\r\n    ],\r\n    [HandPart.LITTLE]: [\r\n        WebXRHandJoint.PINKY_FINGER_METACARPAL,\r\n        WebXRHandJoint.PINKY_FINGER_PHALANX_PROXIMAL,\r\n        WebXRHandJoint.PINKY_FINGER_PHALANX_INTERMEDIATE,\r\n        WebXRHandJoint.PINKY_FINGER_PHALANX_DISTAL,\r\n        WebXRHandJoint.PINKY_FINGER_TIP,\r\n    ],\r\n};\r\n\r\n/**\r\n * Representing a single hand (with its corresponding native XRHand object)\r\n */\r\nexport class WebXRHand implements IDisposable {\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * Transform nodes that will directly receive the transforms from the WebXR matrix data.\r\n     */\r\n    private _jointTransforms = new Array<TransformNode>(handJointReferenceArray.length);\r\n\r\n    /**\r\n     * The float array that will directly receive the transform matrix data from WebXR.\r\n     */\r\n    private _jointTransformMatrices = new Float32Array(handJointReferenceArray.length * 16);\r\n\r\n    private _tempJointMatrix = new Matrix();\r\n\r\n    /**\r\n     * The float array that will directly receive the joint radii from WebXR.\r\n     */\r\n    private _jointRadii = new Float32Array(handJointReferenceArray.length);\r\n\r\n    /**\r\n     * Get the hand mesh.\r\n     */\r\n    public get handMesh(): Nullable<AbstractMesh> {\r\n        return this._handMesh;\r\n    }\r\n\r\n    /**\r\n     * Get meshes of part of the hand.\r\n     * @param part The part of hand to get.\r\n     * @returns An array of meshes that correlate to the hand part requested.\r\n     */\r\n    public getHandPartMeshes(part: HandPart): AbstractMesh[] {\r\n        return handPartsDefinition[part].map((name) => this._jointMeshes[handJointReferenceArray.indexOf(name)]!);\r\n    }\r\n\r\n    /**\r\n     * Retrieves a mesh linked to a named joint in the hand.\r\n     * @param jointName The name of the joint.\r\n     * @returns An AbstractMesh whose position corresponds with the joint position.\r\n     */\r\n    public getJointMesh(jointName: WebXRHandJoint): AbstractMesh {\r\n        return this._jointMeshes[handJointReferenceArray.indexOf(jointName)!];\r\n    }\r\n\r\n    /**\r\n     * Construct a new hand object\r\n     * @param xrController The controller to which the hand correlates.\r\n     * @param _jointMeshes The meshes to be used to track the hand joints.\r\n     * @param _handMesh An optional hand mesh.\r\n     * @param rigMapping An optional rig mapping for the hand mesh.\r\n     *                   If not provided (but a hand mesh is provided),\r\n     *                   it will be assumed that the hand mesh's bones are named\r\n     *                   directly after the WebXR bone names.\r\n     * @param _leftHandedMeshes Are the hand meshes left-handed-system meshes\r\n     * @param _jointsInvisible Are the tracked joint meshes visible\r\n     * @param _jointScaleFactor Scale factor for all joint meshes\r\n     */\r\n    constructor(\r\n        /** The controller to which the hand correlates. */\r\n        public readonly xrController: WebXRInputSource,\r\n        private readonly _jointMeshes: AbstractMesh[],\r\n        private _handMesh: Nullable<AbstractMesh>,\r\n        /** An optional rig mapping for the hand mesh. If not provided (but a hand mesh is provided),\r\n         * it will be assumed that the hand mesh's bones are named directly after the WebXR bone names. */\r\n        readonly rigMapping: Nullable<XRHandMeshRigMapping>,\r\n        private readonly _leftHandedMeshes: boolean = false,\r\n        private readonly _jointsInvisible: boolean = false,\r\n        private readonly _jointScaleFactor: number = 1\r\n    ) {\r\n        this._scene = _jointMeshes[0].getScene();\r\n\r\n        // Initialize the joint transform quaternions and link the transforms to the bones.\r\n        for (let jointIdx = 0; jointIdx < this._jointTransforms.length; jointIdx++) {\r\n            const jointTransform = (this._jointTransforms[jointIdx] = new TransformNode(handJointReferenceArray[jointIdx], this._scene));\r\n            jointTransform.rotationQuaternion = new Quaternion();\r\n\r\n            // Set the rotation quaternion so we can use it later for tracking.\r\n            _jointMeshes[jointIdx].rotationQuaternion = new Quaternion();\r\n        }\r\n\r\n        if (_handMesh) {\r\n            // Note that this logic needs to happen after we initialize the joint tracking transform nodes.\r\n            this.setHandMesh(_handMesh, rigMapping);\r\n        }\r\n\r\n        // hide the motion controller, if available/loaded\r\n        if (this.xrController.motionController) {\r\n            if (this.xrController.motionController.rootMesh) {\r\n                this.xrController.motionController.rootMesh.dispose(false, true);\r\n            }\r\n        }\r\n\r\n        this.xrController.onMotionControllerInitObservable.add((motionController) => {\r\n            motionController._doNotLoadControllerMesh = true;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Sets the current hand mesh to render for the WebXRHand.\r\n     * @param handMesh The rigged hand mesh that will be tracked to the user's hand.\r\n     * @param rigMapping The mapping from XRHandJoint to bone names to use with the mesh.\r\n     * @param _xrSessionManager The XRSessionManager used to initialize the hand mesh.\r\n     */\r\n    public setHandMesh(handMesh: AbstractMesh, rigMapping: Nullable<XRHandMeshRigMapping>, _xrSessionManager?: WebXRSessionManager) {\r\n        this._handMesh = handMesh;\r\n\r\n        // Avoid any strange frustum culling. We will manually control visibility via attach and detach.\r\n        handMesh.alwaysSelectAsActiveMesh = true;\r\n        handMesh.getChildMeshes().forEach((mesh) => {\r\n            mesh.alwaysSelectAsActiveMesh = true;\r\n        });\r\n\r\n        // Link the bones in the hand mesh to the transform nodes that will be bound to the WebXR tracked joints.\r\n        if (this._handMesh.skeleton) {\r\n            const handMeshSkeleton = this._handMesh.skeleton;\r\n            handJointReferenceArray.forEach((jointName, jointIdx) => {\r\n                const jointBoneIdx = handMeshSkeleton.getBoneIndexByName(rigMapping ? rigMapping[jointName] : jointName);\r\n                if (jointBoneIdx !== -1) {\r\n                    handMeshSkeleton.bones[jointBoneIdx].linkTransformNode(this._jointTransforms[jointIdx]);\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update this hand from the latest xr frame.\r\n     * @param xrFrame The latest frame received from WebXR.\r\n     * @param referenceSpace The current viewer reference space.\r\n     */\r\n    public updateFromXRFrame(xrFrame: XRFrame, referenceSpace: XRReferenceSpace) {\r\n        const hand = this.xrController.inputSource.hand;\r\n        if (!hand) {\r\n            return;\r\n        }\r\n\r\n        // TODO: Modify webxr.d.ts to better match WebXR IDL so we don't need this any cast.\r\n        const anyHand: any = hand;\r\n        const jointSpaces: XRJointSpace[] = handJointReferenceArray.map((jointName) => anyHand[jointName] || hand.get(jointName));\r\n        let trackingSuccessful = false;\r\n\r\n        if (xrFrame.fillPoses && xrFrame.fillJointRadii) {\r\n            trackingSuccessful = xrFrame.fillPoses(jointSpaces, referenceSpace, this._jointTransformMatrices) && xrFrame.fillJointRadii(jointSpaces, this._jointRadii);\r\n        } else if (xrFrame.getJointPose) {\r\n            trackingSuccessful = true;\r\n            // Warning: This codepath is slow by comparison, only here for compat.\r\n            for (let jointIdx = 0; jointIdx < jointSpaces.length; jointIdx++) {\r\n                const jointPose = xrFrame.getJointPose(jointSpaces[jointIdx], referenceSpace);\r\n                if (jointPose) {\r\n                    this._jointTransformMatrices.set(jointPose.transform.matrix, jointIdx * 16);\r\n                    this._jointRadii[jointIdx] = jointPose.radius || 0.008;\r\n                } else {\r\n                    trackingSuccessful = false;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n\r\n        if (!trackingSuccessful) {\r\n            return;\r\n        }\r\n\r\n        handJointReferenceArray.forEach((_jointName, jointIdx) => {\r\n            const jointTransform = this._jointTransforms[jointIdx];\r\n            Matrix.FromArrayToRef(this._jointTransformMatrices, jointIdx * 16, this._tempJointMatrix);\r\n            this._tempJointMatrix.decompose(undefined, jointTransform.rotationQuaternion!, jointTransform.position);\r\n\r\n            // The radius we need to make the joint in order for it to roughly cover the joints of the user's real hand.\r\n            const scaledJointRadius = this._jointRadii[jointIdx] * this._jointScaleFactor;\r\n\r\n            const jointMesh = this._jointMeshes[jointIdx];\r\n            jointMesh.isVisible = !this._handMesh && !this._jointsInvisible;\r\n            jointMesh.position.copyFrom(jointTransform.position);\r\n            jointMesh.rotationQuaternion!.copyFrom(jointTransform.rotationQuaternion!);\r\n            jointMesh.scaling.setAll(scaledJointRadius);\r\n\r\n            // The WebXR data comes as right-handed, so we might need to do some conversions.\r\n            if (!this._scene.useRightHandedSystem) {\r\n                jointMesh.position.z *= -1;\r\n                jointMesh.rotationQuaternion!.z *= -1;\r\n                jointMesh.rotationQuaternion!.w *= -1;\r\n\r\n                if (this._leftHandedMeshes && this._handMesh) {\r\n                    jointTransform.position.z *= -1;\r\n                    jointTransform.rotationQuaternion!.z *= -1;\r\n                    jointTransform.rotationQuaternion!.w *= -1;\r\n                }\r\n            }\r\n        });\r\n\r\n        if (this._handMesh) {\r\n            this._handMesh.isVisible = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Dispose this Hand object\r\n     * @param disposeMeshes Should the meshes be disposed as well\r\n     */\r\n    public dispose(disposeMeshes = false) {\r\n        if (this._handMesh) {\r\n            if (disposeMeshes) {\r\n                this._handMesh.skeleton?.dispose();\r\n                this._handMesh.dispose(false, true);\r\n            } else {\r\n                this._handMesh.isVisible = false;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * WebXR Hand Joint tracking feature, available for selected browsers and devices\r\n */\r\nexport class WebXRHandTracking extends WebXRAbstractFeature {\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.HAND_TRACKING;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /** The base URL for the default hand model. */\r\n    public static DEFAULT_HAND_MODEL_BASE_URL = \"https://assets.babylonjs.com/meshes/HandMeshes/\";\r\n    /** The filename to use for the default right hand model. */\r\n    public static DEFAULT_HAND_MODEL_RIGHT_FILENAME = \"r_hand_rhs.glb\";\r\n    /** The filename to use for the default left hand model. */\r\n    public static DEFAULT_HAND_MODEL_LEFT_FILENAME = \"l_hand_rhs.glb\";\r\n    /** The URL pointing to the default hand model NodeMaterial shader. */\r\n    public static DEFAULT_HAND_MODEL_SHADER_URL = \"https://assets.babylonjs.com/meshes/HandMeshes/handsShader.json\";\r\n\r\n    // We want to use lightweight models, diameter will initially be 1 but scaled to the values returned from WebXR.\r\n    private static readonly _ICOSPHERE_PARAMS = { radius: 0.5, flat: false, subdivisions: 2 };\r\n\r\n    private static _RightHandGLB: Nullable<ISceneLoaderAsyncResult> = null;\r\n    private static _LeftHandGLB: Nullable<ISceneLoaderAsyncResult> = null;\r\n\r\n    private static _GenerateTrackedJointMeshes(featureOptions: IWebXRHandTrackingOptions): { left: AbstractMesh[]; right: AbstractMesh[] } {\r\n        const meshes: { [handedness: string]: AbstractMesh[] } = {};\r\n        [\"left\" as XRHandedness, \"right\" as XRHandedness].map((handedness) => {\r\n            const trackedMeshes = [];\r\n            const originalMesh = featureOptions.jointMeshes?.sourceMesh || CreateIcoSphere(\"jointParent\", WebXRHandTracking._ICOSPHERE_PARAMS);\r\n            originalMesh.isVisible = !!featureOptions.jointMeshes?.keepOriginalVisible;\r\n            for (let i = 0; i < handJointReferenceArray.length; ++i) {\r\n                let newInstance: AbstractMesh = originalMesh.createInstance(`${handedness}-handJoint-${i}`);\r\n                if (featureOptions.jointMeshes?.onHandJointMeshGenerated) {\r\n                    const returnedMesh = featureOptions.jointMeshes.onHandJointMeshGenerated(newInstance as InstancedMesh, i, handedness);\r\n                    if (returnedMesh) {\r\n                        if (returnedMesh !== newInstance) {\r\n                            newInstance.dispose();\r\n                            newInstance = returnedMesh;\r\n                        }\r\n                    }\r\n                }\r\n                newInstance.isPickable = false;\r\n                if (featureOptions.jointMeshes?.enablePhysics) {\r\n                    const props = featureOptions.jointMeshes?.physicsProps || {};\r\n                    // downscale the instances so that physics will be initialized correctly\r\n                    newInstance.scaling.setAll(0.02);\r\n                    const type = props.impostorType !== undefined ? props.impostorType : PhysicsImpostor.SphereImpostor;\r\n                    newInstance.physicsImpostor = new PhysicsImpostor(newInstance, type, { mass: 0, ...props });\r\n                }\r\n                newInstance.rotationQuaternion = new Quaternion();\r\n                newInstance.isVisible = false;\r\n                trackedMeshes.push(newInstance);\r\n            }\r\n\r\n            meshes[handedness] = trackedMeshes;\r\n        });\r\n        return { left: meshes.left, right: meshes.right };\r\n    }\r\n\r\n    private static _GenerateDefaultHandMeshesAsync(\r\n        scene: Scene,\r\n        xrSessionManager: WebXRSessionManager,\r\n        options?: IWebXRHandTrackingOptions\r\n    ): Promise<{ left: AbstractMesh; right: AbstractMesh }> {\r\n        // eslint-disable-next-line no-async-promise-executor\r\n        return new Promise(async (resolve) => {\r\n            const riggedMeshes: { [handedness: string]: AbstractMesh } = {};\r\n            // check the cache, defensive\r\n            if (WebXRHandTracking._RightHandGLB?.meshes[1]?.isDisposed()) {\r\n                WebXRHandTracking._RightHandGLB = null;\r\n            }\r\n            if (WebXRHandTracking._LeftHandGLB?.meshes[1]?.isDisposed()) {\r\n                WebXRHandTracking._LeftHandGLB = null;\r\n            }\r\n\r\n            const handsDefined = !!(WebXRHandTracking._RightHandGLB && WebXRHandTracking._LeftHandGLB);\r\n            // load them in parallel\r\n            const handGLBs = await Promise.all([\r\n                WebXRHandTracking._RightHandGLB ||\r\n                    SceneLoader.ImportMeshAsync(\"\", WebXRHandTracking.DEFAULT_HAND_MODEL_BASE_URL, WebXRHandTracking.DEFAULT_HAND_MODEL_RIGHT_FILENAME, scene),\r\n                WebXRHandTracking._LeftHandGLB ||\r\n                    SceneLoader.ImportMeshAsync(\"\", WebXRHandTracking.DEFAULT_HAND_MODEL_BASE_URL, WebXRHandTracking.DEFAULT_HAND_MODEL_LEFT_FILENAME, scene),\r\n            ]);\r\n            WebXRHandTracking._RightHandGLB = handGLBs[0];\r\n            WebXRHandTracking._LeftHandGLB = handGLBs[1];\r\n\r\n            const handShader = await NodeMaterial.ParseFromFileAsync(\"handShader\", WebXRHandTracking.DEFAULT_HAND_MODEL_SHADER_URL, scene);\r\n\r\n            // depth prepass and alpha mode\r\n            handShader.needDepthPrePass = true;\r\n            handShader.transparencyMode = Material.MATERIAL_ALPHABLEND;\r\n            handShader.alphaMode = Constants.ALPHA_COMBINE;\r\n\r\n            // build node materials\r\n            handShader.build(false);\r\n\r\n            // shader\r\n            const handColors = {\r\n                base: Color3.FromInts(116, 63, 203),\r\n                fresnel: Color3.FromInts(149, 102, 229),\r\n                fingerColor: Color3.FromInts(177, 130, 255),\r\n                tipFresnel: Color3.FromInts(220, 200, 255),\r\n                ...options?.handMeshes?.customColors,\r\n            };\r\n\r\n            const handNodes = {\r\n                base: handShader.getBlockByName(\"baseColor\") as InputBlock,\r\n                fresnel: handShader.getBlockByName(\"fresnelColor\") as InputBlock,\r\n                fingerColor: handShader.getBlockByName(\"fingerColor\") as InputBlock,\r\n                tipFresnel: handShader.getBlockByName(\"tipFresnelColor\") as InputBlock,\r\n            };\r\n\r\n            handNodes.base.value = handColors.base;\r\n            handNodes.fresnel.value = handColors.fresnel;\r\n            handNodes.fingerColor.value = handColors.fingerColor;\r\n            handNodes.tipFresnel.value = handColors.tipFresnel;\r\n            const isMultiview = (xrSessionManager._getBaseLayerWrapper() as WebXRCompositionLayerWrapper)?.isMultiview;\r\n            [\"left\", \"right\"].forEach((handedness) => {\r\n                const handGLB = handedness == \"left\" ? WebXRHandTracking._LeftHandGLB : WebXRHandTracking._RightHandGLB;\r\n                if (!handGLB) {\r\n                    // this should never happen!\r\n                    throw new Error(\"Could not load hand model\");\r\n                }\r\n                const handMesh = handGLB.meshes[1];\r\n                handMesh._internalAbstractMeshDataInfo._computeBonesUsingShaders = true;\r\n                // if in multiview do not use the material\r\n                if (!isMultiview) {\r\n                    handMesh.material = handShader.clone(`${handedness}HandShaderClone`, true);\r\n                }\r\n                handMesh.isVisible = false;\r\n\r\n                riggedMeshes[handedness] = handMesh;\r\n\r\n                // single change for left handed systems\r\n                if (!handsDefined && !scene.useRightHandedSystem) {\r\n                    handGLB.meshes[1].rotate(Axis.Y, Math.PI);\r\n                }\r\n            });\r\n\r\n            handShader.dispose();\r\n            resolve({ left: riggedMeshes.left, right: riggedMeshes.right });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Generates a mapping from XRHandJoint to bone name for the default hand mesh.\r\n     * @param handedness The handedness being mapped for.\r\n     * @returns A mapping from XRHandJoint to bone name.\r\n     */\r\n    private static _GenerateDefaultHandMeshRigMapping(handedness: XRHandedness): XRHandMeshRigMapping {\r\n        const H = handedness == \"right\" ? \"R\" : \"L\";\r\n        return {\r\n            [WebXRHandJoint.WRIST]: `wrist_${H}`,\r\n            [WebXRHandJoint.THUMB_METACARPAL]: `thumb_metacarpal_${H}`,\r\n            [WebXRHandJoint.THUMB_PHALANX_PROXIMAL]: `thumb_proxPhalanx_${H}`,\r\n            [WebXRHandJoint.THUMB_PHALANX_DISTAL]: `thumb_distPhalanx_${H}`,\r\n            [WebXRHandJoint.THUMB_TIP]: `thumb_tip_${H}`,\r\n            [WebXRHandJoint.INDEX_FINGER_METACARPAL]: `index_metacarpal_${H}`,\r\n            [WebXRHandJoint.INDEX_FINGER_PHALANX_PROXIMAL]: `index_proxPhalanx_${H}`,\r\n            [WebXRHandJoint.INDEX_FINGER_PHALANX_INTERMEDIATE]: `index_intPhalanx_${H}`,\r\n            [WebXRHandJoint.INDEX_FINGER_PHALANX_DISTAL]: `index_distPhalanx_${H}`,\r\n            [WebXRHandJoint.INDEX_FINGER_TIP]: `index_tip_${H}`,\r\n            [WebXRHandJoint.MIDDLE_FINGER_METACARPAL]: `middle_metacarpal_${H}`,\r\n            [WebXRHandJoint.MIDDLE_FINGER_PHALANX_PROXIMAL]: `middle_proxPhalanx_${H}`,\r\n            [WebXRHandJoint.MIDDLE_FINGER_PHALANX_INTERMEDIATE]: `middle_intPhalanx_${H}`,\r\n            [WebXRHandJoint.MIDDLE_FINGER_PHALANX_DISTAL]: `middle_distPhalanx_${H}`,\r\n            [WebXRHandJoint.MIDDLE_FINGER_TIP]: `middle_tip_${H}`,\r\n            [WebXRHandJoint.RING_FINGER_METACARPAL]: `ring_metacarpal_${H}`,\r\n            [WebXRHandJoint.RING_FINGER_PHALANX_PROXIMAL]: `ring_proxPhalanx_${H}`,\r\n            [WebXRHandJoint.RING_FINGER_PHALANX_INTERMEDIATE]: `ring_intPhalanx_${H}`,\r\n            [WebXRHandJoint.RING_FINGER_PHALANX_DISTAL]: `ring_distPhalanx_${H}`,\r\n            [WebXRHandJoint.RING_FINGER_TIP]: `ring_tip_${H}`,\r\n            [WebXRHandJoint.PINKY_FINGER_METACARPAL]: `little_metacarpal_${H}`,\r\n            [WebXRHandJoint.PINKY_FINGER_PHALANX_PROXIMAL]: `little_proxPhalanx_${H}`,\r\n            [WebXRHandJoint.PINKY_FINGER_PHALANX_INTERMEDIATE]: `little_intPhalanx_${H}`,\r\n            [WebXRHandJoint.PINKY_FINGER_PHALANX_DISTAL]: `little_distPhalanx_${H}`,\r\n            [WebXRHandJoint.PINKY_FINGER_TIP]: `little_tip_${H}`,\r\n        };\r\n    }\r\n\r\n    private _attachedHands: {\r\n        [uniqueId: string]: WebXRHand;\r\n    } = {};\r\n\r\n    private _trackingHands: {\r\n        left: Nullable<WebXRHand>;\r\n        right: Nullable<WebXRHand>;\r\n    } = { left: null, right: null };\r\n\r\n    private _handResources: {\r\n        jointMeshes: Nullable<{ left: AbstractMesh[]; right: AbstractMesh[] }>;\r\n        handMeshes: Nullable<{ left: AbstractMesh; right: AbstractMesh }>;\r\n        rigMappings: Nullable<{ left: XRHandMeshRigMapping; right: XRHandMeshRigMapping }>;\r\n    } = { jointMeshes: null, handMeshes: null, rigMappings: null };\r\n\r\n    private _worldScaleObserver?: Nullable<Observer<{ previousScaleFactor: number; newScaleFactor: number }>> = null;\r\n\r\n    /**\r\n     * This observable will notify registered observers when a new hand object was added and initialized\r\n     */\r\n    public onHandAddedObservable: Observable<WebXRHand> = new Observable();\r\n    /**\r\n     * This observable will notify its observers right before the hand object is disposed\r\n     */\r\n    public onHandRemovedObservable: Observable<WebXRHand> = new Observable();\r\n\r\n    /**\r\n     * Check if the needed objects are defined.\r\n     * This does not mean that the feature is enabled, but that the objects needed are well defined.\r\n     * @returns true if the needed objects for this feature are defined\r\n     */\r\n    public isCompatible(): boolean {\r\n        return typeof XRHand !== \"undefined\";\r\n    }\r\n\r\n    /**\r\n     * Get the hand object according to the controller id\r\n     * @param controllerId the controller id to which we want to get the hand\r\n     * @returns null if not found or the WebXRHand object if found\r\n     */\r\n    public getHandByControllerId(controllerId: string): Nullable<WebXRHand> {\r\n        return this._attachedHands[controllerId];\r\n    }\r\n\r\n    /**\r\n     * Get a hand object according to the requested handedness\r\n     * @param handedness the handedness to request\r\n     * @returns null if not found or the WebXRHand object if found\r\n     */\r\n    public getHandByHandedness(handedness: XRHandedness): Nullable<WebXRHand> {\r\n        if (handedness == \"none\") {\r\n            return null;\r\n        }\r\n        return this._trackingHands[handedness];\r\n    }\r\n\r\n    /**\r\n     * Creates a new instance of the XR hand tracking feature.\r\n     * @param _xrSessionManager An instance of WebXRSessionManager.\r\n     * @param options Options to use when constructing this feature.\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        /** Options to use when constructing this feature. */\r\n        public readonly options: IWebXRHandTrackingOptions\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"hand-tracking\";\r\n\r\n        // Support legacy versions of the options object by copying over joint mesh properties\r\n        const anyOptions = options as any;\r\n        const anyJointMeshOptions = anyOptions.jointMeshes;\r\n        if (anyJointMeshOptions) {\r\n            if (typeof anyJointMeshOptions.disableDefaultHandMesh !== \"undefined\") {\r\n                options.handMeshes = options.handMeshes || {};\r\n                options.handMeshes.disableDefaultMeshes = anyJointMeshOptions.disableDefaultHandMesh;\r\n            }\r\n            if (typeof anyJointMeshOptions.handMeshes !== \"undefined\") {\r\n                options.handMeshes = options.handMeshes || {};\r\n                options.handMeshes.customMeshes = anyJointMeshOptions.handMeshes;\r\n            }\r\n            if (typeof anyJointMeshOptions.leftHandedSystemMeshes !== \"undefined\") {\r\n                options.handMeshes = options.handMeshes || {};\r\n                options.handMeshes.meshesUseLeftHandedCoordinates = anyJointMeshOptions.leftHandedSystemMeshes;\r\n            }\r\n            if (typeof anyJointMeshOptions.rigMapping !== \"undefined\") {\r\n                options.handMeshes = options.handMeshes || {};\r\n                const leftRigMapping = {};\r\n                const rightRigMapping = {};\r\n                [\r\n                    [anyJointMeshOptions.rigMapping.left, leftRigMapping],\r\n                    [anyJointMeshOptions.rigMapping.right, rightRigMapping],\r\n                ].forEach((rigMappingTuple) => {\r\n                    const legacyRigMapping = rigMappingTuple[0] as string[];\r\n                    const rigMapping = rigMappingTuple[1] as XRHandMeshRigMapping;\r\n                    legacyRigMapping.forEach((modelJointName, index) => {\r\n                        rigMapping[handJointReferenceArray[index]] = modelJointName;\r\n                    });\r\n                });\r\n                options.handMeshes.customRigMappings = {\r\n                    left: leftRigMapping as XRHandMeshRigMapping,\r\n                    right: rightRigMapping as XRHandMeshRigMapping,\r\n                };\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Attach this feature.\r\n     * Will usually be called by the features manager.\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public attach(): boolean {\r\n        if (!super.attach()) {\r\n            return false;\r\n        }\r\n\r\n        this._handResources = {\r\n            jointMeshes: WebXRHandTracking._GenerateTrackedJointMeshes(this.options),\r\n            handMeshes: this.options.handMeshes?.customMeshes || null,\r\n            rigMappings: this.options.handMeshes?.customRigMappings || null,\r\n        };\r\n\r\n        // If they didn't supply custom meshes and are not disabling the default meshes...\r\n        if (!this.options.handMeshes?.customMeshes && !this.options.handMeshes?.disableDefaultMeshes) {\r\n            WebXRHandTracking._GenerateDefaultHandMeshesAsync(EngineStore.LastCreatedScene!, this._xrSessionManager, this.options).then((defaultHandMeshes) => {\r\n                this._handResources.handMeshes = defaultHandMeshes;\r\n                this._handResources.rigMappings = {\r\n                    left: WebXRHandTracking._GenerateDefaultHandMeshRigMapping(\"left\"),\r\n                    right: WebXRHandTracking._GenerateDefaultHandMeshRigMapping(\"right\"),\r\n                };\r\n\r\n                // Apply meshes to existing hands if already tracking.\r\n                this._trackingHands.left?.setHandMesh(this._handResources.handMeshes.left, this._handResources.rigMappings.left, this._xrSessionManager);\r\n                this._trackingHands.right?.setHandMesh(this._handResources.handMeshes.right, this._handResources.rigMappings.right, this._xrSessionManager);\r\n                this._handResources.handMeshes.left.scaling.setAll(this._xrSessionManager.worldScalingFactor);\r\n                this._handResources.handMeshes.right.scaling.setAll(this._xrSessionManager.worldScalingFactor);\r\n            });\r\n            this._worldScaleObserver = this._xrSessionManager.onWorldScaleFactorChangedObservable.add((scalingFactors) => {\r\n                if (this._handResources.handMeshes) {\r\n                    this._handResources.handMeshes.left.scaling.scaleInPlace(scalingFactors.newScaleFactor / scalingFactors.previousScaleFactor);\r\n                    this._handResources.handMeshes.right.scaling.scaleInPlace(scalingFactors.newScaleFactor / scalingFactors.previousScaleFactor);\r\n                }\r\n            });\r\n        }\r\n\r\n        this.options.xrInput.controllers.forEach(this._attachHand);\r\n        this._addNewAttachObserver(this.options.xrInput.onControllerAddedObservable, this._attachHand);\r\n        this._addNewAttachObserver(this.options.xrInput.onControllerRemovedObservable, this._detachHand);\r\n\r\n        return true;\r\n    }\r\n\r\n    protected _onXRFrame(_xrFrame: XRFrame): void {\r\n        this._trackingHands.left?.updateFromXRFrame(_xrFrame, this._xrSessionManager.referenceSpace);\r\n        this._trackingHands.right?.updateFromXRFrame(_xrFrame, this._xrSessionManager.referenceSpace);\r\n    }\r\n\r\n    private _attachHand = (xrController: WebXRInputSource) => {\r\n        if (!xrController.inputSource.hand || xrController.inputSource.handedness == \"none\" || !this._handResources.jointMeshes) {\r\n            return;\r\n        }\r\n\r\n        const handedness = xrController.inputSource.handedness;\r\n        const webxrHand = new WebXRHand(\r\n            xrController,\r\n            this._handResources.jointMeshes[handedness],\r\n            this._handResources.handMeshes && this._handResources.handMeshes[handedness],\r\n            this._handResources.rigMappings && this._handResources.rigMappings[handedness],\r\n            this.options.handMeshes?.meshesUseLeftHandedCoordinates,\r\n            this.options.jointMeshes?.invisible,\r\n            this.options.jointMeshes?.scaleFactor\r\n        );\r\n\r\n        this._attachedHands[xrController.uniqueId] = webxrHand;\r\n        this._trackingHands[handedness] = webxrHand;\r\n\r\n        this.onHandAddedObservable.notifyObservers(webxrHand);\r\n    };\r\n\r\n    private _detachHandById(controllerId: string, disposeMesh?: boolean) {\r\n        const hand = this.getHandByControllerId(controllerId);\r\n        if (hand) {\r\n            const handedness = hand.xrController.inputSource.handedness == \"left\" ? \"left\" : \"right\";\r\n            if (this._trackingHands[handedness]?.xrController.uniqueId === controllerId) {\r\n                this._trackingHands[handedness] = null;\r\n            }\r\n            this.onHandRemovedObservable.notifyObservers(hand);\r\n            hand.dispose(disposeMesh);\r\n            delete this._attachedHands[controllerId];\r\n        }\r\n    }\r\n\r\n    private _detachHand = (xrController: WebXRInputSource) => {\r\n        this._detachHandById(xrController.uniqueId);\r\n    };\r\n\r\n    /**\r\n     * Detach this feature.\r\n     * Will usually be called by the features manager.\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n\r\n        Object.keys(this._attachedHands).forEach((uniqueId) => this._detachHandById(uniqueId, this.options.handMeshes?.disposeOnSessionEnd));\r\n        if (this.options.handMeshes?.disposeOnSessionEnd) {\r\n            if (this._handResources.jointMeshes) {\r\n                this._handResources.jointMeshes.left.forEach((trackedMesh) => trackedMesh.dispose());\r\n                this._handResources.jointMeshes.right.forEach((trackedMesh) => trackedMesh.dispose());\r\n            }\r\n        }\r\n\r\n        // remove world scale observer\r\n        if (this._worldScaleObserver) {\r\n            this._xrSessionManager.onWorldScaleFactorChangedObservable.remove(this._worldScaleObserver);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached.\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n        this.onHandAddedObservable.clear();\r\n        this.onHandRemovedObservable.clear();\r\n\r\n        if (this._handResources.handMeshes && !this.options.handMeshes?.customMeshes) {\r\n            // this will dispose the cached meshes\r\n            this._handResources.handMeshes.left.dispose();\r\n            this._handResources.handMeshes.right.dispose();\r\n            // remove the cached meshes\r\n            WebXRHandTracking._RightHandGLB = null;\r\n            WebXRHandTracking._LeftHandGLB = null;\r\n        }\r\n\r\n        if (this._handResources.jointMeshes) {\r\n            this._handResources.jointMeshes.left.forEach((trackedMesh) => trackedMesh.dispose());\r\n            this._handResources.jointMeshes.right.forEach((trackedMesh) => trackedMesh.dispose());\r\n        }\r\n    }\r\n}\r\n\r\n//register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRHandTracking.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRHandTracking(xrSessionManager, options);\r\n    },\r\n    WebXRHandTracking.Version,\r\n    false\r\n);\r\n"]}