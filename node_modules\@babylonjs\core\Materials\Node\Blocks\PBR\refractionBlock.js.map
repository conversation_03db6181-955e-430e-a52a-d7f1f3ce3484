{"version": 3, "file": "refractionBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/PBR/refractionBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,oCAAoC,EAAE,MAAM,wCAAwC,CAAC;AAC9F,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AAMxG,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAEtG,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAC;AACpD,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,MAAM,EAAE,MAAM,+BAA+B,CAAC;AAEvD;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,iBAAiB;IA4DlD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QA7BnD;;;WAGG;QAEI,mCAA8B,GAAY,KAAK,CAAC;QAEvD;;WAEG;QAEI,sBAAiB,GAAY,KAAK,CAAC;QAE1C;;WAEG;QAEI,wBAAmB,GAAY,KAAK,CAAC;QAcxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC3H,IAAI,CAAC,aAAa,CAAC,yBAAyB,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAEpI,IAAI,CAAC,cAAc,CACf,YAAY,EACZ,qCAAqC,CAAC,MAAM,EAC5C,wBAAwB,CAAC,QAAQ,EACjC,IAAI,uCAAuC,CAAC,YAAY,EAAE,IAAI,EAAE,oCAAoC,CAAC,MAAM,EAAE,eAAe,EAAE,iBAAiB,CAAC,CACnJ,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,KAA6B;QAC3C,KAAK,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;QAClD,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC;IAES,WAAW;QACjB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,OAAO,IAAI,CAAC,OAAO,CAAC;SACvB;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;IAC1C,CAAC;IAEM,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,EAAE,CAAC,IAAI;QACnH,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YAC7B,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,sBAAsB,EAAE,wBAAwB,CAAC,QAAQ,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;YAC9I,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC;YACzB,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACnD;QAED,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrC,IAAI,SAAS,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,wBAAwB,CAAC,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAExI,IAAI,CAAC,SAAS,EAAE;gBACZ,SAAS,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;gBACnC,SAAS,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;aAC7D;YACD,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzC;IACL,CAAC;IAEM,cAAc,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QAC9F,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;QAElD,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7C,MAAM,UAAU,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,gBAAgB,CAAC;QAE3E,OAAO,CAAC,QAAQ,CAAC,eAAe,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAEpD,IAAI,CAAC,UAAU,EAAE;YACb,OAAO;SACV;QAED,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,iBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACtE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,EAAE,iBAAkB,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QAC3F,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,EAAE,iBAAkB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QACnG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,iBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAEvK,OAAO,CAAC,QAAQ,CAAC,iCAAiC,EAAE,IAAI,CAAC,8BAA8B,EAAE,IAAI,CAAC,CAAC;QAC/F,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,iBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5E,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,iBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACvE,OAAO,CAAC,QAAQ,CAAC,kCAAkC,EAAQ,iBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACpH,OAAO,CAAC,QAAQ,CAAC,2BAA2B,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;IAClF,CAAC;IAEM,OAAO;QACV,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE;YAC5C,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW;QAC/D,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;QAEvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAE7C,IAAI,CAAC,iBAAiB,EAAE;YACpB,OAAO;SACV;QAED,IAAI,iBAAiB,CAAC,MAAM,EAAE;YAC1B,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;SAC/D;aAAM;YACH,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;SAC7D;QAED,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,EAAE,iBAAiB,CAAC,0BAA0B,EAAE,CAAC,CAAC;QAE7F,IAAI,KAAK,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;YAC3B,IAAU,iBAAkB,CAAC,KAAK,EAAE;gBAChC,KAAK,GAAS,iBAAkB,CAAC,KAAK,CAAC;aAC1C;SACJ;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,EAAE,KAAK,IAAI,IAAI,CAAC,gCAAgC,CAAC,iBAAiB,EAAE,KAAK,IAAI,GAAG,CAAC;QAEzJ,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,EAAE,iBAAiB,CAAC,KAAK,EAAE,CAAC,GAAG,iBAAiB,EAAE,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAErI,MAAM,CAAC,SAAS,CACZ,IAAI,CAAC,iCAAiC,EACtC,iBAAiB,CAAC,OAAO,EAAE,CAAC,KAAK,EACjC,iBAAiB,CAAC,kBAAkB,EACpC,iBAAiB,CAAC,mBAAmB,EACrC,CAAC,GAAG,iBAAiB,CACxB,CAAC;QAEF,MAAM,KAAK,GAAG,iBAAiB,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;QAEhD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAEhF,IAAU,iBAAkB,CAAC,eAAe,EAAE;YAC1C,MAAM,WAAW,GAAgB,iBAAiB,CAAC;YACnD,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,WAAW,CAAC,mBAAmB,CAAC,CAAC;YAC1E,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;SACrE;IACL,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,KAA6B;QACxC,MAAM,IAAI,GAAG,EAAE,CAAC;QAEhB,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE1C,WAAW;QACX,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC;QAC9E,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE3C,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC;QAC1E,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEzC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAC;QAErE,KAAK,CAAC,mBAAmB,IAAI,UAAU,IAAI,CAAC,aAAa,IAAI,CAAC;QAC9D,KAAK,CAAC,mBAAmB,IAAI,uBAAuB,IAAI,CAAC,gBAAgB,KAAK,CAAC;QAC/E,KAAK,CAAC,mBAAmB,IAAI,SAAS,CAAC;QACvC,KAAK,CAAC,mBAAmB,IAAI,qBAAqB,IAAI,CAAC,cAAc,KAAK,CAAC;QAC3E,KAAK,CAAC,mBAAmB,IAAI,UAAU,CAAC;QAExC,WAAW;QACX,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;QACrF,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC,kBAAkB,CAAC,6BAA6B,CAAC,CAAC;QAC/F,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,4BAA4B,CAAC,CAAC;QAE/E,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAE5E,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;QAEjE,KAAK,CAAC,aAAa,CACf,kBAAkB,EAClB;qBACS,IAAI,CAAC,aAAa;;;;qBAIlB,EACT,KAAK,IAAI,CAAC,IAAI,EAAE,CACnB,CAAC;QAEF,KAAK,CAAC,aAAa,CACf,qBAAqB,EACrB;qBACS,IAAI,CAAC,aAAa;;;;qBAIlB,EACT,KAAK,IAAI,CAAC,IAAI,EAAE,CACnB,CAAC;QAEF,IAAI,CAAC,iCAAiC,GAAG,KAAK,CAAC,oBAAoB,CAAC,8BAA8B,CAAC,CAAC;QAEpG,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,iCAAiC,EAAE,MAAM,CAAC,CAAC;QAE7E,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAE5E,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;QAEjE,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC,oBAAoB,CAAC,0BAA0B,CAAC,CAAC;QAE5F,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;QAEzE,KAAK,CAAC,sBAAsB,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;QAC5D,KAAK,CAAC,sBAAsB,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;QAExD,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;QAErC,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACrB,UAAU,GAAG,GAAG,IAAI,CAAC,iBAAiB,uCAAuC,IAAI,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC;aACzG;iBAAM;gBACH,UAAU,GAAG,GAAG,IAAI,CAAC,iBAAiB,mCAAmC,IAAI,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC;aACrG;YACD,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,8BAA8B,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,CAAC;SAC1G;QAED,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,qCAAqC,IAAI,CAAC,8BAA8B,KAAK,CAAC;QACrH,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,wBAAwB,IAAI,CAAC,iBAAiB,KAAK,CAAC;QAC3F,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,0BAA0B,IAAI,CAAC,mBAAmB,KAAK,CAAC;QAE/F,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC9C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;SAC1D;QAED,mBAAmB,CAAC,8BAA8B,GAAG,IAAI,CAAC,8BAA8B,CAAC;QACzF,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC/D,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAEnE,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,mBAAmB,CAAC,OAAO,EAAE;YAC7B,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YAChF,IAAI,mBAAmB,CAAC,OAAO,CAAC,MAAM,EAAE;gBACpC,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;aACjF;iBAAM;gBACH,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;aAC7E;SACJ;QAED,IAAI,CAAC,8BAA8B,GAAG,mBAAmB,CAAC,8BAA8B,CAAC;QACzF,IAAI,CAAC,iBAAiB,GAAG,mBAAmB,CAAC,iBAAiB,CAAC;QAC/D,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,mBAAmB,CAAC,mBAAmB,CAAC;IACzE,CAAC;CACJ;AAnVU;IADN,sBAAsB,CAAC,iCAAiC,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;uEAChF;AAMhD;IADN,sBAAsB,CAAC,qBAAqB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;0DACjF;AAMnC;IADN,sBAAsB,CAAC,wBAAwB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;4DAClF;AAyUhD,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport type { BaseTexture } from \"../../../Textures/baseTexture\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { CubeTexture } from \"../../../Textures/cubeTexture\";\r\nimport { Texture } from \"../../../Textures/texture\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { Scalar } from \"../../../../Maths/math.scalar\";\r\n\r\n/**\r\n * Block used to implement the refraction part of the sub surface module of the PBR material\r\n */\r\nexport class RefractionBlock extends NodeMaterialBlock {\r\n    /** @internal */\r\n    public _define3DName: string;\r\n    /** @internal */\r\n    public _refractionMatrixName: string;\r\n    /** @internal */\r\n    public _defineLODRefractionAlpha: string;\r\n    /** @internal */\r\n    public _defineLinearSpecularRefraction: string;\r\n    /** @internal */\r\n    public _defineOppositeZ: string;\r\n    /** @internal */\r\n    public _cubeSamplerName: string;\r\n    /** @internal */\r\n    public _2DSamplerName: string;\r\n    /** @internal */\r\n    public _vRefractionMicrosurfaceInfosName: string;\r\n    /** @internal */\r\n    public _vRefractionInfosName: string;\r\n    /** @internal */\r\n    public _vRefractionFilteringInfoName: string;\r\n\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * The properties below are set by the main PBR block prior to calling methods of this class.\r\n     * This is to avoid having to add them as inputs here whereas they are already inputs of the main block, so already known.\r\n     * It's less burden on the user side in the editor part.\r\n     */\r\n\r\n    /** @internal */\r\n    public viewConnectionPoint: NodeMaterialConnectionPoint;\r\n\r\n    /** @internal */\r\n    public indexOfRefractionConnectionPoint: NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * This parameters will make the material used its opacity to control how much it is refracting against not.\r\n     * Materials half opaque for instance using refraction could benefit from this control.\r\n     */\r\n    @editableInPropertyPage(\"Link refraction to transparency\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public linkRefractionWithTransparency: boolean = false;\r\n\r\n    /**\r\n     * Controls if refraction needs to be inverted on Y. This could be useful for procedural texture.\r\n     */\r\n    @editableInPropertyPage(\"Invert refraction Y\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public invertRefractionY: boolean = false;\r\n\r\n    /**\r\n     * Controls if refraction needs to be inverted on Y. This could be useful for procedural texture.\r\n     */\r\n    @editableInPropertyPage(\"Use thickness as depth\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public useThicknessAsDepth: boolean = false;\r\n\r\n    /**\r\n     * Gets or sets the texture associated with the node\r\n     */\r\n    public texture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Create a new RefractionBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"intensity\", NodeMaterialBlockConnectionPointTypes.Float, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"tintAtDistance\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"volumeIndexOfRefraction\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\r\n            \"refraction\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"refraction\", this, NodeMaterialConnectionPointDirection.Output, RefractionBlock, \"RefractionBlock\")\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"vRefractionPosition\");\r\n        state._excludeVariableName(\"vRefractionSize\");\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"RefractionBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the intensity input component\r\n     */\r\n    public get intensity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the tint at distance input component\r\n     */\r\n    public get tintAtDistance(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the volume index of refraction input component\r\n     */\r\n    public get volumeIndexOfRefraction(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the view input component\r\n     */\r\n    public get view(): NodeMaterialConnectionPoint {\r\n        return this.viewConnectionPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the refraction object output component\r\n     */\r\n    public get refraction(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Returns true if the block has a texture\r\n     */\r\n    public get hasTexture(): boolean {\r\n        return !!this._getTexture();\r\n    }\r\n\r\n    protected _getTexture(): Nullable<BaseTexture> {\r\n        if (this.texture) {\r\n            return this.texture;\r\n        }\r\n\r\n        return this._scene.environmentTexture;\r\n    }\r\n\r\n    public autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.intensity.isConnected) {\r\n            const intensityInput = new InputBlock(\"Refraction intensity\", NodeMaterialBlockTargets.Fragment, NodeMaterialBlockConnectionPointTypes.Float);\r\n            intensityInput.value = 1;\r\n            intensityInput.output.connectTo(this.intensity);\r\n        }\r\n\r\n        if (this.view && !this.view.isConnected) {\r\n            let viewInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.View && additionalFilteringInfo(b));\r\n\r\n            if (!viewInput) {\r\n                viewInput = new InputBlock(\"view\");\r\n                viewInput.setAsSystemValue(NodeMaterialSystemValues.View);\r\n            }\r\n            viewInput.output.connectTo(this.view);\r\n        }\r\n    }\r\n\r\n    public prepareDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        super.prepareDefines(mesh, nodeMaterial, defines);\r\n\r\n        const refractionTexture = this._getTexture();\r\n        const refraction = refractionTexture && refractionTexture.getTextureMatrix;\r\n\r\n        defines.setValue(\"SS_REFRACTION\", refraction, true);\r\n\r\n        if (!refraction) {\r\n            return;\r\n        }\r\n\r\n        defines.setValue(this._define3DName, refractionTexture!.isCube, true);\r\n        defines.setValue(this._defineLODRefractionAlpha, refractionTexture!.lodLevelInAlpha, true);\r\n        defines.setValue(this._defineLinearSpecularRefraction, refractionTexture!.linearSpecularLOD, true);\r\n        defines.setValue(this._defineOppositeZ, this._scene.useRightHandedSystem && refractionTexture.isCube ? !refractionTexture!.invertZ : refractionTexture!.invertZ, true);\r\n\r\n        defines.setValue(\"SS_LINKREFRACTIONTOTRANSPARENCY\", this.linkRefractionWithTransparency, true);\r\n        defines.setValue(\"SS_GAMMAREFRACTION\", refractionTexture!.gammaSpace, true);\r\n        defines.setValue(\"SS_RGBDREFRACTION\", refractionTexture!.isRGBD, true);\r\n        defines.setValue(\"SS_USE_LOCAL_REFRACTIONMAP_CUBIC\", (<any>refractionTexture).boundingBoxSize ? true : false, true);\r\n        defines.setValue(\"SS_USE_THICKNESS_AS_DEPTH\", this.useThicknessAsDepth, true);\r\n    }\r\n\r\n    public isReady() {\r\n        const texture = this._getTexture();\r\n\r\n        if (texture && !texture.isReadyOrNotBlocking()) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh) {\r\n        super.bind(effect, nodeMaterial, mesh);\r\n\r\n        const refractionTexture = this._getTexture();\r\n\r\n        if (!refractionTexture) {\r\n            return;\r\n        }\r\n\r\n        if (refractionTexture.isCube) {\r\n            effect.setTexture(this._cubeSamplerName, refractionTexture);\r\n        } else {\r\n            effect.setTexture(this._2DSamplerName, refractionTexture);\r\n        }\r\n\r\n        effect.setMatrix(this._refractionMatrixName, refractionTexture.getRefractionTextureMatrix());\r\n\r\n        let depth = 1.0;\r\n        if (!refractionTexture.isCube) {\r\n            if ((<any>refractionTexture).depth) {\r\n                depth = (<any>refractionTexture).depth;\r\n            }\r\n        }\r\n\r\n        const indexOfRefraction = this.volumeIndexOfRefraction.connectInputBlock?.value ?? this.indexOfRefractionConnectionPoint.connectInputBlock?.value ?? 1.5;\r\n\r\n        effect.setFloat4(this._vRefractionInfosName, refractionTexture.level, 1 / indexOfRefraction, depth, this.invertRefractionY ? -1 : 1);\r\n\r\n        effect.setFloat4(\r\n            this._vRefractionMicrosurfaceInfosName,\r\n            refractionTexture.getSize().width,\r\n            refractionTexture.lodGenerationScale,\r\n            refractionTexture.lodGenerationOffset,\r\n            1 / indexOfRefraction\r\n        );\r\n\r\n        const width = refractionTexture.getSize().width;\r\n\r\n        effect.setFloat2(this._vRefractionFilteringInfoName, width, Scalar.Log2(width));\r\n\r\n        if ((<any>refractionTexture).boundingBoxSize) {\r\n            const cubeTexture = <CubeTexture>refractionTexture;\r\n            effect.setVector3(\"vRefractionPosition\", cubeTexture.boundingBoxPosition);\r\n            effect.setVector3(\"vRefractionSize\", cubeTexture.boundingBoxSize);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the main code of the block (fragment side)\r\n     * @param state current state of the node material building\r\n     * @returns the shader code\r\n     */\r\n    public getCode(state: NodeMaterialBuildState): string {\r\n        const code = \"\";\r\n\r\n        state.sharedData.blockingBlocks.push(this);\r\n        state.sharedData.textureBlocks.push(this);\r\n\r\n        // Samplers\r\n        this._cubeSamplerName = state._getFreeVariableName(this.name + \"CubeSampler\");\r\n        state.samplers.push(this._cubeSamplerName);\r\n\r\n        this._2DSamplerName = state._getFreeVariableName(this.name + \"2DSampler\");\r\n        state.samplers.push(this._2DSamplerName);\r\n\r\n        this._define3DName = state._getFreeDefineName(\"SS_REFRACTIONMAP_3D\");\r\n\r\n        state._samplerDeclaration += `#ifdef ${this._define3DName}\\n`;\r\n        state._samplerDeclaration += `uniform samplerCube ${this._cubeSamplerName};\\n`;\r\n        state._samplerDeclaration += `#else\\n`;\r\n        state._samplerDeclaration += `uniform sampler2D ${this._2DSamplerName};\\n`;\r\n        state._samplerDeclaration += `#endif\\n`;\r\n\r\n        // Fragment\r\n        state.sharedData.blocksWithDefines.push(this);\r\n        state.sharedData.bindableBlocks.push(this);\r\n\r\n        this._defineLODRefractionAlpha = state._getFreeDefineName(\"SS_LODINREFRACTIONALPHA\");\r\n        this._defineLinearSpecularRefraction = state._getFreeDefineName(\"SS_LINEARSPECULARREFRACTION\");\r\n        this._defineOppositeZ = state._getFreeDefineName(\"SS_REFRACTIONMAP_OPPOSITEZ\");\r\n\r\n        this._refractionMatrixName = state._getFreeVariableName(\"refractionMatrix\");\r\n\r\n        state._emitUniformFromString(this._refractionMatrixName, \"mat4\");\r\n\r\n        state._emitFunction(\r\n            \"sampleRefraction\",\r\n            `\r\n            #ifdef ${this._define3DName}\r\n                #define sampleRefraction(s, c) textureCube(s, c)\r\n            #else\r\n                #define sampleRefraction(s, c) texture2D(s, c)\r\n            #endif\\n`,\r\n            `//${this.name}`\r\n        );\r\n\r\n        state._emitFunction(\r\n            \"sampleRefractionLod\",\r\n            `\r\n            #ifdef ${this._define3DName}\r\n                #define sampleRefractionLod(s, c, l) textureCubeLodEXT(s, c, l)\r\n            #else\r\n                #define sampleRefractionLod(s, c, l) texture2DLodEXT(s, c, l)\r\n            #endif\\n`,\r\n            `//${this.name}`\r\n        );\r\n\r\n        this._vRefractionMicrosurfaceInfosName = state._getFreeVariableName(\"vRefractionMicrosurfaceInfos\");\r\n\r\n        state._emitUniformFromString(this._vRefractionMicrosurfaceInfosName, \"vec4\");\r\n\r\n        this._vRefractionInfosName = state._getFreeVariableName(\"vRefractionInfos\");\r\n\r\n        state._emitUniformFromString(this._vRefractionInfosName, \"vec4\");\r\n\r\n        this._vRefractionFilteringInfoName = state._getFreeVariableName(\"vRefractionFilteringInfo\");\r\n\r\n        state._emitUniformFromString(this._vRefractionFilteringInfoName, \"vec2\");\r\n\r\n        state._emitUniformFromString(\"vRefractionPosition\", \"vec3\");\r\n        state._emitUniformFromString(\"vRefractionSize\", \"vec3\");\r\n\r\n        return code;\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        this._scene = state.sharedData.scene;\r\n\r\n        return this;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        if (this.texture) {\r\n            if (this.texture.isCube) {\r\n                codeString = `${this._codeVariableName}.texture = new BABYLON.CubeTexture(\"${this.texture.name}\");\\n`;\r\n            } else {\r\n                codeString = `${this._codeVariableName}.texture = new BABYLON.Texture(\"${this.texture.name}\");\\n`;\r\n            }\r\n            codeString += `${this._codeVariableName}.texture.coordinatesMode = ${this.texture.coordinatesMode};\\n`;\r\n        }\r\n\r\n        codeString += `${this._codeVariableName}.linkRefractionWithTransparency = ${this.linkRefractionWithTransparency};\\n`;\r\n        codeString += `${this._codeVariableName}.invertRefractionY = ${this.invertRefractionY};\\n`;\r\n        codeString += `${this._codeVariableName}.useThicknessAsDepth = ${this.useThicknessAsDepth};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        if (this.texture && !this.texture.isRenderTarget) {\r\n            serializationObject.texture = this.texture.serialize();\r\n        }\r\n\r\n        serializationObject.linkRefractionWithTransparency = this.linkRefractionWithTransparency;\r\n        serializationObject.invertRefractionY = this.invertRefractionY;\r\n        serializationObject.useThicknessAsDepth = this.useThicknessAsDepth;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        if (serializationObject.texture) {\r\n            rootUrl = serializationObject.texture.url.indexOf(\"data:\") === 0 ? \"\" : rootUrl;\r\n            if (serializationObject.texture.isCube) {\r\n                this.texture = CubeTexture.Parse(serializationObject.texture, scene, rootUrl);\r\n            } else {\r\n                this.texture = Texture.Parse(serializationObject.texture, scene, rootUrl);\r\n            }\r\n        }\r\n\r\n        this.linkRefractionWithTransparency = serializationObject.linkRefractionWithTransparency;\r\n        this.invertRefractionY = serializationObject.invertRefractionY;\r\n        this.useThicknessAsDepth = !!serializationObject.useThicknessAsDepth;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.RefractionBlock\", RefractionBlock);\r\n"]}