import type { DeepImmutable, Nullable } from "../types";
import { Matrix, Vector3 } from "../Maths/math.vector";
import type { BoundingSphere } from "../Culling/boundingSphere";
import type { ICullable } from "./boundingInfo";
import type { Plane } from "../Maths/math.plane";
import type { DrawWrapper } from "../Materials/drawWrapper";
/**
 * Class used to store bounding box information
 */
export declare class BoundingBox implements ICullable {
    /**
     * Gets the 8 vectors representing the bounding box in local space
     */
    readonly vectors: Vector3[];
    /**
     * Gets the center of the bounding box in local space
     */
    readonly center: Vector3;
    /**
     * Gets the center of the bounding box in world space
     */
    readonly centerWorld: Vector3;
    /**
     * Gets the extend size in local space
     */
    readonly extendSize: Vector3;
    /**
     * Gets the extend size in world space
     */
    readonly extendSizeWorld: Vector3;
    /**
     * Gets the OBB (object bounding box) directions
     */
    readonly directions: Vector3[];
    /**
     * Gets the 8 vectors representing the bounding box in world space
     */
    readonly vectorsWorld: Vector3[];
    /**
     * Gets the minimum vector in world space
     */
    readonly minimumWorld: Vector3;
    /**
     * Gets the maximum vector in world space
     */
    readonly maximumWorld: Vector3;
    /**
     * Gets the minimum vector in local space
     */
    readonly minimum: Vector3;
    /**
     * Gets the maximum vector in local space
     */
    readonly maximum: Vector3;
    private _worldMatrix;
    private static readonly _TmpVector3;
    /**
     * @internal
     */
    _tag: number;
    /** @internal */
    _drawWrapperFront: Nullable<DrawWrapper>;
    /** @internal */
    _drawWrapperBack: Nullable<DrawWrapper>;
    /**
     * Creates a new bounding box
     * @param min defines the minimum vector (in local space)
     * @param max defines the maximum vector (in local space)
     * @param worldMatrix defines the new world matrix
     */
    constructor(min: DeepImmutable<Vector3>, max: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>);
    /**
     * Recreates the entire bounding box from scratch as if we call the constructor in place
     * @param min defines the new minimum vector (in local space)
     * @param max defines the new maximum vector (in local space)
     * @param worldMatrix defines the new world matrix
     */
    reConstruct(min: DeepImmutable<Vector3>, max: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>): void;
    /**
     * Scale the current bounding box by applying a scale factor
     * @param factor defines the scale factor to apply
     * @returns the current bounding box
     */
    scale(factor: number): BoundingBox;
    /**
     * Gets the world matrix of the bounding box
     * @returns a matrix
     */
    getWorldMatrix(): DeepImmutable<Matrix>;
    /**
     * @internal
     */
    _update(world: DeepImmutable<Matrix>): void;
    /**
     * Tests if the bounding box is intersecting the frustum planes
     * @param frustumPlanes defines the frustum planes to test
     * @returns true if there is an intersection
     */
    isInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>): boolean;
    /**
     * Tests if the bounding box is entirely inside the frustum planes
     * @param frustumPlanes defines the frustum planes to test
     * @returns true if there is an inclusion
     */
    isCompletelyInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>): boolean;
    /**
     * Tests if a point is inside the bounding box
     * @param point defines the point to test
     * @returns true if the point is inside the bounding box
     */
    intersectsPoint(point: DeepImmutable<Vector3>): boolean;
    /**
     * Tests if the bounding box intersects with a bounding sphere
     * @param sphere defines the sphere to test
     * @returns true if there is an intersection
     */
    intersectsSphere(sphere: DeepImmutable<BoundingSphere>): boolean;
    /**
     * Tests if the bounding box intersects with a box defined by a min and max vectors
     * @param min defines the min vector to use
     * @param max defines the max vector to use
     * @returns true if there is an intersection
     */
    intersectsMinMax(min: DeepImmutable<Vector3>, max: DeepImmutable<Vector3>): boolean;
    /**
     * Disposes the resources of the class
     */
    dispose(): void;
    /**
     * Tests if two bounding boxes are intersections
     * @param box0 defines the first box to test
     * @param box1 defines the second box to test
     * @returns true if there is an intersection
     */
    static Intersects(box0: DeepImmutable<BoundingBox>, box1: DeepImmutable<BoundingBox>): boolean;
    /**
     * Tests if a bounding box defines by a min/max vectors intersects a sphere
     * @param minPoint defines the minimum vector of the bounding box
     * @param maxPoint defines the maximum vector of the bounding box
     * @param sphereCenter defines the sphere center
     * @param sphereRadius defines the sphere radius
     * @returns true if there is an intersection
     */
    static IntersectsSphere(minPoint: DeepImmutable<Vector3>, maxPoint: DeepImmutable<Vector3>, sphereCenter: DeepImmutable<Vector3>, sphereRadius: number): boolean;
    /**
     * Tests if a bounding box defined with 8 vectors is entirely inside frustum planes
     * @param boundingVectors defines an array of 8 vectors representing a bounding box
     * @param frustumPlanes defines the frustum planes to test
     * @returns true if there is an inclusion
     */
    static IsCompletelyInFrustum(boundingVectors: Array<DeepImmutable<Vector3>>, frustumPlanes: Array<DeepImmutable<Plane>>): boolean;
    /**
     * Tests if a bounding box defined with 8 vectors intersects frustum planes
     * @param boundingVectors defines an array of 8 vectors representing a bounding box
     * @param frustumPlanes defines the frustum planes to test
     * @returns true if there is an intersection
     */
    static IsInFrustum(boundingVectors: Array<DeepImmutable<Vector3>>, frustumPlanes: Array<DeepImmutable<Plane>>): boolean;
}
