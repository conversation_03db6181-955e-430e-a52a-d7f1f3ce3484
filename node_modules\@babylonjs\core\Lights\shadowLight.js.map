{"version": 3, "file": "shadowLight.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Lights/shadowLight.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAGnE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAEnE,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAiH1C;;;GAGG;AACH,MAAM,OAAgB,WAAY,SAAQ,KAAK;IAA/C;;QA2FY,iCAA4B,GAAY,IAAI,CAAC;QA0L3C,gBAAW,GAAW,MAAM,CAAC,QAAQ,EAAE,CAAC;QACxC,sBAAiB,GAAW,MAAM,CAAC,QAAQ,EAAE,CAAC;IAwC5D,CAAC;IA1Ta,YAAY,CAAC,KAAc;QACjC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IAC3B,CAAC;IACD;;;OAGG;IAEH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IACD;;;OAGG;IACH,IAAW,QAAQ,CAAC,KAAc;QAC9B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAGS,aAAa,CAAC,KAAc;QAClC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IACD;;;OAGG;IAEH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD;;;OAGG;IACH,IAAW,SAAS,CAAC,KAAc;QAC/B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAGD;;OAEG;IAEH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;OAEG;IACH,IAAW,UAAU,CAAC,KAAa;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACxC,CAAC;IAGD;;OAEG;IAEH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;OAEG;IACH,IAAW,UAAU,CAAC,KAAa;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACxC,CAAC;IAoBD;;;OAGG;IACI,6BAA6B;QAChC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;YAC3C,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3B,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;aAC7C;YACD,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEzG,oCAAoC;YACpC,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;oBAC5B,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;iBAC9C;gBACD,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;aACzG;YACD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACH,6DAA6D;IACtD,kBAAkB,CAAC,SAAkB;QACxC,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;IAClF,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACtB,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;IAC/E,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,MAAe;QACvC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC3B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QACpD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACnD,OAAO,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAClE,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,2BAA2B;QAC9B,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,4BAA4B;QAC/B,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;IAC7C,CAAC;IAED,gBAAgB;IACT,UAAU;QACb,KAAK,CAAC,UAAU,EAAE,CAAC;QAEnB,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAC1C,CAAC;IAED,gBAAgB;IACT,eAAe;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC7C,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,KAAe;QACrC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;YACjC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC;YACtD,OAAO,IAAI,CAAC,YAAY,CAAC;SAC5B;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE7C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;SACzC;QAED,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAE9F,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;YAC3C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAEjF,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAChC;QAED,wBAAwB;QACxB,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC;QAE3C,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,YAAoB;QACpC,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;IAC/E,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,YAAoB;QACpC,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;IAC/E,CAAC;IAED;;;;;;OAMG;IACI,yBAAyB,CAAC,MAAc,EAAE,UAAkB,EAAE,UAA+B;QAChG,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACpC,IAAI,CAAC,6BAA6B,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;SACtE;aAAM;YACH,IAAI,CAAC,iCAAiC,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;SAC1E;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gBAAgB;IACN,uBAAuB;QAC7B,KAAK,CAAC,uBAAuB,EAAE,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;YAC5C,IAAI,CAAC,mBAA2B,GAAG,IAAI,CAAC;YACxC,IAAI,CAAC,oBAA4B,GAAG,IAAI,CAAC;SAC7C;IACL,CAAC;IAKD;;;;OAIG;IACI,aAAa,CAAC,SAAkB;QACnC,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE7C,IAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;QAClC,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE;YACtC,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC;SAC5C;QAED,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,cAAc,CAAC,CAAC;QAC3E,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE;YAC7D,cAAc,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,kDAAkD;SACzF;QAED,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1C,aAAa,CAAC,QAAQ,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAEpD,MAAM,CAAC,aAAa,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAEjF,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;;;OAMG;IACI,mBAAmB,CAAC,UAAmB,EAAE,UAAgC;QAC5E,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,EAAE,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,UAAU,IAAI,EAAE,CAAC,CAAC;QAEzG,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;CACJ;AAlTG;IADC,kBAAkB,EAAE;2CAGpB;AAkBD;IADC,kBAAkB,EAAE;4CAGpB;AAcD;IADC,SAAS,EAAE;6CAGX;AAcD;IADC,SAAS,EAAE;6CAGX", "sourcesContent": ["import { serialize, serializeAsVector3 } from \"../Misc/decorators\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Matrix, TmpVectors, Vector3 } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Light } from \"./light\";\r\nimport { Axis } from \"../Maths/math.axis\";\r\nimport type { Nullable } from \"core/types\";\r\n/**\r\n * Interface describing all the common properties and methods a shadow light needs to implement.\r\n * This helps both the shadow generator and materials to generate the corresponding shadow maps\r\n * as well as binding the different shadow properties to the effects.\r\n */\r\nexport interface IShadowLight extends Light {\r\n    /**\r\n     * The light id in the scene (used in scene.getLightById for instance)\r\n     */\r\n    id: string;\r\n    /**\r\n     * The position the shadow will be casted from.\r\n     */\r\n    position: Vector3;\r\n    /**\r\n     * In 2d mode (needCube being false), the direction used to cast the shadow.\r\n     */\r\n    direction: Vector3;\r\n    /**\r\n     * The transformed position. Position of the light in world space taking parenting in account.\r\n     */\r\n    transformedPosition: Vector3;\r\n    /**\r\n     * The transformed direction. Direction of the light in world space taking parenting in account.\r\n     */\r\n    transformedDirection: Vector3;\r\n    /**\r\n     * The friendly name of the light in the scene.\r\n     */\r\n    name: string;\r\n    /**\r\n     * Defines the shadow projection clipping minimum z value.\r\n     */\r\n    shadowMinZ: number;\r\n    /**\r\n     * Defines the shadow projection clipping maximum z value.\r\n     */\r\n    shadowMaxZ: number;\r\n\r\n    /**\r\n     * Computes the transformed information (transformedPosition and transformedDirection in World space) of the current light\r\n     * @returns true if the information has been computed, false if it does not need to (no parenting)\r\n     */\r\n    computeTransformedInformation(): boolean;\r\n\r\n    /**\r\n     * Gets the scene the light belongs to.\r\n     * @returns The scene\r\n     */\r\n    getScene(): Scene;\r\n\r\n    /**\r\n     * Callback defining a custom Projection Matrix Builder.\r\n     * This can be used to override the default projection matrix computation.\r\n     */\r\n    customProjectionMatrixBuilder: (viewMatrix: Matrix, renderList: Array<AbstractMesh>, result: Matrix) => void;\r\n\r\n    /**\r\n     * Sets the shadow projection matrix in parameter to the generated projection matrix.\r\n     * @param matrix The matrix to update with the projection information\r\n     * @param viewMatrix The transform matrix of the light\r\n     * @param renderList The list of mesh to render in the map\r\n     * @returns The current light\r\n     */\r\n    setShadowProjectionMatrix(matrix: Matrix, viewMatrix: Matrix, renderList: Array<AbstractMesh>): IShadowLight;\r\n\r\n    /**\r\n     * Gets the current depth scale used in ESM.\r\n     * @returns The scale\r\n     */\r\n    getDepthScale(): number;\r\n\r\n    /**\r\n     * Returns whether or not the shadow generation require a cube texture or a 2d texture.\r\n     * @returns true if a cube texture needs to be use\r\n     */\r\n    needCube(): boolean;\r\n\r\n    /**\r\n     * Detects if the projection matrix requires to be recomputed this frame.\r\n     * @returns true if it requires to be recomputed otherwise, false.\r\n     */\r\n    needProjectionMatrixCompute(): boolean;\r\n\r\n    /**\r\n     * Forces the shadow generator to recompute the projection matrix even if position and direction did not changed.\r\n     */\r\n    forceProjectionMatrixCompute(): void;\r\n\r\n    /**\r\n     * Get the direction to use to render the shadow map. In case of cube texture, the face index can be passed.\r\n     * @param faceIndex The index of the face we are computed the direction to generate shadow\r\n     * @returns The set direction in 2d mode otherwise the direction to the cubemap face if needCube() is true\r\n     */\r\n    getShadowDirection(faceIndex?: number): Vector3;\r\n\r\n    /**\r\n     * Gets the minZ used for shadow according to both the scene and the light.\r\n     * @param activeCamera The camera we are returning the min for\r\n     * @returns the depth min z\r\n     */\r\n    getDepthMinZ(activeCamera: Camera): number;\r\n\r\n    /**\r\n     * Gets the maxZ used for shadow according to both the scene and the light.\r\n     * @param activeCamera The camera we are returning the max for\r\n     * @returns the depth max z\r\n     */\r\n    getDepthMaxZ(activeCamera: Camera): number;\r\n}\r\n\r\n/**\r\n * Base implementation IShadowLight\r\n * It groups all the common behaviour in order to reduce duplication and better follow the DRY pattern.\r\n */\r\nexport abstract class ShadowLight extends Light implements IShadowLight {\r\n    protected abstract _setDefaultShadowProjectionMatrix(matrix: Matrix, viewMatrix: Matrix, renderList: Array<AbstractMesh>): void;\r\n\r\n    protected _position: Vector3;\r\n    protected _setPosition(value: Vector3) {\r\n        this._position = value;\r\n    }\r\n    /**\r\n     * Sets the position the shadow will be casted from. Also use as the light position for both\r\n     * point and spot lights.\r\n     */\r\n    @serializeAsVector3()\r\n    public get position(): Vector3 {\r\n        return this._position;\r\n    }\r\n    /**\r\n     * Sets the position the shadow will be casted from. Also use as the light position for both\r\n     * point and spot lights.\r\n     */\r\n    public set position(value: Vector3) {\r\n        this._setPosition(value);\r\n    }\r\n\r\n    protected _direction: Vector3;\r\n    protected _setDirection(value: Vector3) {\r\n        this._direction = value;\r\n    }\r\n    /**\r\n     * In 2d mode (needCube being false), gets the direction used to cast the shadow.\r\n     * Also use as the light direction on spot and directional lights.\r\n     */\r\n    @serializeAsVector3()\r\n    public get direction(): Vector3 {\r\n        return this._direction;\r\n    }\r\n    /**\r\n     * In 2d mode (needCube being false), sets the direction used to cast the shadow.\r\n     * Also use as the light direction on spot and directional lights.\r\n     */\r\n    public set direction(value: Vector3) {\r\n        this._setDirection(value);\r\n    }\r\n\r\n    protected _shadowMinZ: number;\r\n    /**\r\n     * Gets the shadow projection clipping minimum z value.\r\n     */\r\n    @serialize()\r\n    public get shadowMinZ(): number {\r\n        return this._shadowMinZ;\r\n    }\r\n    /**\r\n     * Sets the shadow projection clipping minimum z value.\r\n     */\r\n    public set shadowMinZ(value: number) {\r\n        this._shadowMinZ = value;\r\n        this.forceProjectionMatrixCompute();\r\n    }\r\n\r\n    protected _shadowMaxZ: number;\r\n    /**\r\n     * Sets the shadow projection clipping maximum z value.\r\n     */\r\n    @serialize()\r\n    public get shadowMaxZ(): number {\r\n        return this._shadowMaxZ;\r\n    }\r\n    /**\r\n     * Gets the shadow projection clipping maximum z value.\r\n     */\r\n    public set shadowMaxZ(value: number) {\r\n        this._shadowMaxZ = value;\r\n        this.forceProjectionMatrixCompute();\r\n    }\r\n\r\n    /**\r\n     * Callback defining a custom Projection Matrix Builder.\r\n     * This can be used to override the default projection matrix computation.\r\n     */\r\n    public customProjectionMatrixBuilder: (viewMatrix: Matrix, renderList: Array<AbstractMesh>, result: Matrix) => void;\r\n\r\n    /**\r\n     * The transformed position. Position of the light in world space taking parenting in account. Needs to be computed by calling computeTransformedInformation.\r\n     */\r\n    public transformedPosition: Vector3;\r\n\r\n    /**\r\n     * The transformed direction. Direction of the light in world space taking parenting in account.\r\n     */\r\n    public transformedDirection: Vector3;\r\n\r\n    private _needProjectionMatrixCompute: boolean = true;\r\n\r\n    /**\r\n     * Computes the transformed information (transformedPosition and transformedDirection in World space) of the current light\r\n     * @returns true if the information has been computed, false if it does not need to (no parenting)\r\n     */\r\n    public computeTransformedInformation(): boolean {\r\n        if (this.parent && this.parent.getWorldMatrix) {\r\n            if (!this.transformedPosition) {\r\n                this.transformedPosition = Vector3.Zero();\r\n            }\r\n            Vector3.TransformCoordinatesToRef(this.position, this.parent.getWorldMatrix(), this.transformedPosition);\r\n\r\n            // In case the direction is present.\r\n            if (this.direction) {\r\n                if (!this.transformedDirection) {\r\n                    this.transformedDirection = Vector3.Zero();\r\n                }\r\n                Vector3.TransformNormalToRef(this.direction, this.parent.getWorldMatrix(), this.transformedDirection);\r\n            }\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Return the depth scale used for the shadow map.\r\n     * @returns the depth scale.\r\n     */\r\n    public getDepthScale(): number {\r\n        return 50.0;\r\n    }\r\n\r\n    /**\r\n     * Get the direction to use to render the shadow map. In case of cube texture, the face index can be passed.\r\n     * @param faceIndex The index of the face we are computed the direction to generate shadow\r\n     * @returns The set direction in 2d mode otherwise the direction to the cubemap face if needCube() is true\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public getShadowDirection(faceIndex?: number): Vector3 {\r\n        return this.transformedDirection ? this.transformedDirection : this.direction;\r\n    }\r\n\r\n    /**\r\n     * If computeTransformedInformation has been called, returns the ShadowLight absolute position in the world. Otherwise, returns the local position.\r\n     * @returns the position vector in world space\r\n     */\r\n    public getAbsolutePosition(): Vector3 {\r\n        return this.transformedPosition ? this.transformedPosition : this.position;\r\n    }\r\n\r\n    /**\r\n     * Sets the ShadowLight direction toward the passed target.\r\n     * @param target The point to target in local space\r\n     * @returns the updated ShadowLight direction\r\n     */\r\n    public setDirectionToTarget(target: Vector3): Vector3 {\r\n        this.direction = Vector3.Normalize(target.subtract(this.position));\r\n        return this.direction;\r\n    }\r\n\r\n    /**\r\n     * Returns the light rotation in euler definition.\r\n     * @returns the x y z rotation in local space.\r\n     */\r\n    public getRotation(): Vector3 {\r\n        this.direction.normalize();\r\n        const xaxis = Vector3.Cross(this.direction, Axis.Y);\r\n        const yaxis = Vector3.Cross(xaxis, this.direction);\r\n        return Vector3.RotationFromAxis(xaxis, yaxis, this.direction);\r\n    }\r\n\r\n    /**\r\n     * Returns whether or not the shadow generation require a cube texture or a 2d texture.\r\n     * @returns true if a cube texture needs to be use\r\n     */\r\n    public needCube(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Detects if the projection matrix requires to be recomputed this frame.\r\n     * @returns true if it requires to be recomputed otherwise, false.\r\n     */\r\n    public needProjectionMatrixCompute(): boolean {\r\n        return this._needProjectionMatrixCompute;\r\n    }\r\n\r\n    /**\r\n     * Forces the shadow generator to recompute the projection matrix even if position and direction did not changed.\r\n     */\r\n    public forceProjectionMatrixCompute(): void {\r\n        this._needProjectionMatrixCompute = true;\r\n    }\r\n\r\n    /** @internal */\r\n    public _initCache() {\r\n        super._initCache();\r\n\r\n        this._cache.position = Vector3.Zero();\r\n    }\r\n\r\n    /** @internal */\r\n    public _isSynchronized(): boolean {\r\n        if (!this._cache.position.equals(this.position)) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Computes the world matrix of the node\r\n     * @param force defines if the cache version should be invalidated forcing the world matrix to be created from scratch\r\n     * @returns the world matrix\r\n     */\r\n    public computeWorldMatrix(force?: boolean): Matrix {\r\n        if (!force && this.isSynchronized()) {\r\n            this._currentRenderId = this.getScene().getRenderId();\r\n            return this._worldMatrix;\r\n        }\r\n\r\n        this._updateCache();\r\n        this._cache.position.copyFrom(this.position);\r\n\r\n        if (!this._worldMatrix) {\r\n            this._worldMatrix = Matrix.Identity();\r\n        }\r\n\r\n        Matrix.TranslationToRef(this.position.x, this.position.y, this.position.z, this._worldMatrix);\r\n\r\n        if (this.parent && this.parent.getWorldMatrix) {\r\n            this._worldMatrix.multiplyToRef(this.parent.getWorldMatrix(), this._worldMatrix);\r\n\r\n            this._markSyncedWithParent();\r\n        }\r\n\r\n        // Cache the determinant\r\n        this._worldMatrixDeterminantIsDirty = true;\r\n\r\n        return this._worldMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the minZ used for shadow according to both the scene and the light.\r\n     * @param activeCamera The camera we are returning the min for\r\n     * @returns the depth min z\r\n     */\r\n    public getDepthMinZ(activeCamera: Camera): number {\r\n        return this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera.minZ;\r\n    }\r\n\r\n    /**\r\n     * Gets the maxZ used for shadow according to both the scene and the light.\r\n     * @param activeCamera The camera we are returning the max for\r\n     * @returns the depth max z\r\n     */\r\n    public getDepthMaxZ(activeCamera: Camera): number {\r\n        return this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera.maxZ;\r\n    }\r\n\r\n    /**\r\n     * Sets the shadow projection matrix in parameter to the generated projection matrix.\r\n     * @param matrix The matrix to updated with the projection information\r\n     * @param viewMatrix The transform matrix of the light\r\n     * @param renderList The list of mesh to render in the map\r\n     * @returns The current light\r\n     */\r\n    public setShadowProjectionMatrix(matrix: Matrix, viewMatrix: Matrix, renderList: Array<AbstractMesh>): IShadowLight {\r\n        if (this.customProjectionMatrixBuilder) {\r\n            this.customProjectionMatrixBuilder(viewMatrix, renderList, matrix);\r\n        } else {\r\n            this._setDefaultShadowProjectionMatrix(matrix, viewMatrix, renderList);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /** @internal */\r\n    protected _syncParentEnabledState() {\r\n        super._syncParentEnabledState();\r\n        if (!this.parent || !this.parent.getWorldMatrix) {\r\n            (this.transformedPosition as any) = null;\r\n            (this.transformedDirection as any) = null;\r\n        }\r\n    }\r\n\r\n    protected _viewMatrix: Matrix = Matrix.Identity();\r\n    protected _projectionMatrix: Matrix = Matrix.Identity();\r\n\r\n    /**\r\n     * Returns the view matrix.\r\n     * @param faceIndex The index of the face for which we want to extract the view matrix. Only used for point light types.\r\n     * @returns The view matrix. Can be null, if a view matrix cannot be defined for the type of light considered (as for a hemispherical light, for example).\r\n     */\r\n    public getViewMatrix(faceIndex?: number): Nullable<Matrix> {\r\n        const lightDirection = TmpVectors.Vector3[0];\r\n\r\n        let lightPosition = this.position;\r\n        if (this.computeTransformedInformation()) {\r\n            lightPosition = this.transformedPosition;\r\n        }\r\n\r\n        Vector3.NormalizeToRef(this.getShadowDirection(faceIndex), lightDirection);\r\n        if (Math.abs(Vector3.Dot(lightDirection, Vector3.Up())) === 1.0) {\r\n            lightDirection.z = 0.0000000000001; // Required to avoid perfectly perpendicular light\r\n        }\r\n\r\n        const lightTarget = TmpVectors.Vector3[1];\r\n        lightPosition.addToRef(lightDirection, lightTarget);\r\n\r\n        Matrix.LookAtLHToRef(lightPosition, lightTarget, Vector3.Up(), this._viewMatrix);\r\n\r\n        return this._viewMatrix;\r\n    }\r\n\r\n    /**\r\n     * Returns the projection matrix.\r\n     * Note that viewMatrix and renderList are optional and are only used by lights that calculate the projection matrix from a list of meshes (e.g. directional lights with automatic extents calculation).\r\n     * @param viewMatrix The view transform matrix of the light (optional).\r\n     * @param renderList The list of meshes to take into account when calculating the projection matrix (optional).\r\n     * @returns The projection matrix. Can be null, if a projection matrix cannot be defined for the type of light considered (as for a hemispherical light, for example).\r\n     */\r\n    public getProjectionMatrix(viewMatrix?: Matrix, renderList?: Array<AbstractMesh>): Nullable<Matrix> {\r\n        this.setShadowProjectionMatrix(this._projectionMatrix, viewMatrix ?? this._viewMatrix, renderList ?? []);\r\n\r\n        return this._projectionMatrix;\r\n    }\r\n}\r\n"]}