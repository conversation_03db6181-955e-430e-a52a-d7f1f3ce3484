import type { Camera } from "../../Cameras/camera.js";
import type { Engine } from "../../Engines/engine.js";
import type { RenderTargetWrapper } from "../../Engines/renderTargetWrapper.js";
import { Texture } from "../../Materials/Textures/texture.js";
import type { ThinTexture } from "../../Materials/Textures/thinTexture.js";
import { PostProcess } from "../../PostProcesses/postProcess.js";
import type { Scene } from "../../scene.js";
import type { Nullable } from "../../types.js";
import { Observable } from "../../Misc/observable.js";
/** @internal */
export declare class FluidRenderingTextures {
    protected _name: string;
    protected _scene: Scene;
    protected _camera: Nullable<Camera>;
    protected _engine: Engine;
    protected _width: number;
    protected _height: number;
    protected _blurTextureSizeX: number;
    protected _blurTextureSizeY: number;
    protected _textureType: number;
    protected _textureFormat: number;
    protected _blurTextureType: number;
    protected _blurTextureFormat: number;
    protected _useStandardBlur: boolean;
    protected _generateDepthBuffer: boolean;
    protected _samples: number;
    protected _postProcessRunningIndex: number;
    protected _rt: Nullable<RenderTargetWrapper>;
    protected _texture: Nullable<Texture>;
    protected _rtBlur: Nullable<RenderTargetWrapper>;
    protected _textureBlurred: Nullable<Texture>;
    protected _blurPostProcesses: Nullable<PostProcess[]>;
    enableBlur: boolean;
    blurSizeDivisor: number;
    blurFilterSize: number;
    private _blurNumIterations;
    get blurNumIterations(): number;
    set blurNumIterations(numIterations: number);
    blurMaxFilterSize: number;
    blurDepthScale: number;
    particleSize: number;
    onDisposeObservable: Observable<FluidRenderingTextures>;
    get renderTarget(): Nullable<RenderTargetWrapper>;
    get renderTargetBlur(): Nullable<RenderTargetWrapper>;
    get texture(): Nullable<Texture>;
    get textureBlur(): Nullable<Texture>;
    constructor(name: string, scene: Scene, width: number, height: number, blurTextureSizeX: number, blurTextureSizeY: number, textureType?: number, textureFormat?: number, blurTextureType?: number, blurTextureFormat?: number, useStandardBlur?: boolean, camera?: Nullable<Camera>, generateDepthBuffer?: boolean, samples?: number);
    initialize(): void;
    applyBlurPostProcesses(): void;
    protected _createRenderTarget(): void;
    protected _createBlurPostProcesses(textureBlurSource: ThinTexture, textureType: number, textureFormat: number, blurSizeDivisor: number, debugName: string, useStandardBlur?: boolean): [RenderTargetWrapper, Texture, PostProcess[]];
    private _fixReusablePostProcess;
    private _getProjectedParticleConstant;
    private _getDepthThreshold;
    dispose(): void;
}
