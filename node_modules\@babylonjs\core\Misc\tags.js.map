{"version": 3, "file": "tags.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/tags.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD;;GAEG;AACH,MAAM,OAAO,IAAI;IACb;;;OAGG;IACI,MAAM,CAAC,SAAS,CAAC,GAAQ;QAC5B,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;QAE5B,GAAG,CAAC,OAAO,GAAG,GAAG,EAAE;YACf,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC;QAEF,GAAG,CAAC,OAAO,GAAG,CAAC,UAAkB,EAAE,EAAE;YACjC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;QAC3C,CAAC,CAAC;QAEF,GAAG,CAAC,UAAU,GAAG,CAAC,UAAkB,EAAE,EAAE;YACpC,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;QAChD,CAAC,CAAC;QAEF,GAAG,CAAC,gBAAgB,GAAG,CAAC,SAAiB,EAAE,EAAE;YACzC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAC7C,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,UAAU,CAAC,GAAQ;QAC7B,OAAO,GAAG,CAAC,KAAK,CAAC;QACjB,OAAO,GAAG,CAAC,OAAO,CAAC;QACnB,OAAO,GAAG,CAAC,OAAO,CAAC;QACnB,OAAO,GAAG,CAAC,UAAU,CAAC;QACtB,OAAO,GAAG,CAAC,gBAAgB,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,OAAO,CAAC,GAAQ;QAC1B,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;YACZ,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC;QACvB,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;YAClB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;gBAC/C,OAAO,IAAI,CAAC;aACf;SACJ;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,OAAO,CAAC,GAAQ,EAAE,WAAoB,IAAI;QACpD,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QACD,IAAI,QAAQ,EAAE;YACV,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,KAAK,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,EAAE;gBACzB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;oBACjF,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACvB;aACJ;YACD,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC9B;aAAM;YACH,OAAO,GAAG,CAAC,KAAK,CAAC;SACpB;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,SAAS,CAAC,GAAQ,EAAE,UAAkB;QAChD,IAAI,CAAC,UAAU,EAAE;YACb,OAAO;SACV;QAED,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;YAChC,OAAO;SACV;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG;YACtB,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,SAAS,CAAC,GAAQ,EAAE,GAAW;QACzC,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QAEjB,IAAI,GAAG,KAAK,EAAE,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,OAAO,EAAE;YACjD,OAAO;SACV;QAED,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE;YACvD,OAAO;SACV;QAED,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACpB,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,cAAc,CAAC,GAAQ,EAAE,UAAkB;QACrD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACpB,OAAO;SACV;QACD,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnC,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;YAClB,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SACrC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,cAAc,CAAC,GAAQ,EAAE,GAAW;QAC9C,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,YAAY,CAAC,GAAQ,EAAE,SAAiB;QAClD,IAAI,SAAS,KAAK,SAAS,EAAE;YACzB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,SAAS,KAAK,EAAE,EAAE;YAClB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SAC5B;QAED,OAAO,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACvF,CAAC;CACJ", "sourcesContent": ["import { AndOrNotEvaluator } from \"./andOrNotEvaluator\";\r\n\r\n/**\r\n * Class used to store custom tags\r\n */\r\nexport class Tags {\r\n    /**\r\n     * Adds support for tags on the given object\r\n     * @param obj defines the object to use\r\n     */\r\n    public static EnableFor(obj: any): void {\r\n        obj._tags = obj._tags || {};\r\n\r\n        obj.hasTags = () => {\r\n            return Tags.HasTags(obj);\r\n        };\r\n\r\n        obj.addTags = (tagsString: string) => {\r\n            return Tags.AddTagsTo(obj, tagsString);\r\n        };\r\n\r\n        obj.removeTags = (tagsString: string) => {\r\n            return Tags.RemoveTagsFrom(obj, tagsString);\r\n        };\r\n\r\n        obj.matchesTagsQuery = (tagsQuery: string) => {\r\n            return Tags.MatchesQuery(obj, tagsQuery);\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Removes tags support\r\n     * @param obj defines the object to use\r\n     */\r\n    public static DisableFor(obj: any): void {\r\n        delete obj._tags;\r\n        delete obj.hasTags;\r\n        delete obj.addTags;\r\n        delete obj.removeTags;\r\n        delete obj.matchesTagsQuery;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the given object has tags\r\n     * @param obj defines the object to use\r\n     * @returns a boolean\r\n     */\r\n    public static HasTags(obj: any): boolean {\r\n        if (!obj._tags) {\r\n            return false;\r\n        }\r\n\r\n        const tags = obj._tags;\r\n        for (const i in tags) {\r\n            if (Object.prototype.hasOwnProperty.call(tags, i)) {\r\n                return true;\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Gets the tags available on a given object\r\n     * @param obj defines the object to use\r\n     * @param asString defines if the tags must be returned as a string instead of an array of strings\r\n     * @returns the tags\r\n     */\r\n    public static GetTags(obj: any, asString: boolean = true): any {\r\n        if (!obj._tags) {\r\n            return null;\r\n        }\r\n        if (asString) {\r\n            const tagsArray = [];\r\n            for (const tag in obj._tags) {\r\n                if (Object.prototype.hasOwnProperty.call(obj._tags, tag) && obj._tags[tag] === true) {\r\n                    tagsArray.push(tag);\r\n                }\r\n            }\r\n            return tagsArray.join(\" \");\r\n        } else {\r\n            return obj._tags;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds tags to an object\r\n     * @param obj defines the object to use\r\n     * @param tagsString defines the tag string. The tags 'true' and 'false' are reserved and cannot be used as tags.\r\n     * A tag cannot start with '||', '&&', and '!'. It cannot contain whitespaces\r\n     */\r\n    public static AddTagsTo(obj: any, tagsString: string): void {\r\n        if (!tagsString) {\r\n            return;\r\n        }\r\n\r\n        if (typeof tagsString !== \"string\") {\r\n            return;\r\n        }\r\n\r\n        const tags = tagsString.split(\" \");\r\n        tags.forEach(function (tag) {\r\n            Tags._AddTagTo(obj, tag);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _AddTagTo(obj: any, tag: string): void {\r\n        tag = tag.trim();\r\n\r\n        if (tag === \"\" || tag === \"true\" || tag === \"false\") {\r\n            return;\r\n        }\r\n\r\n        if (tag.match(/[\\s]/) || tag.match(/^([!]|([|]|[&]){2})/)) {\r\n            return;\r\n        }\r\n\r\n        Tags.EnableFor(obj);\r\n        obj._tags[tag] = true;\r\n    }\r\n\r\n    /**\r\n     * Removes specific tags from a specific object\r\n     * @param obj defines the object to use\r\n     * @param tagsString defines the tags to remove\r\n     */\r\n    public static RemoveTagsFrom(obj: any, tagsString: string) {\r\n        if (!Tags.HasTags(obj)) {\r\n            return;\r\n        }\r\n        const tags = tagsString.split(\" \");\r\n        for (const t in tags) {\r\n            Tags._RemoveTagFrom(obj, tags[t]);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _RemoveTagFrom(obj: any, tag: string): void {\r\n        delete obj._tags[tag];\r\n    }\r\n\r\n    /**\r\n     * Defines if tags hosted on an object match a given query\r\n     * @param obj defines the object to use\r\n     * @param tagsQuery defines the tag query\r\n     * @returns a boolean\r\n     */\r\n    public static MatchesQuery(obj: any, tagsQuery: string): boolean {\r\n        if (tagsQuery === undefined) {\r\n            return true;\r\n        }\r\n\r\n        if (tagsQuery === \"\") {\r\n            return Tags.HasTags(obj);\r\n        }\r\n\r\n        return AndOrNotEvaluator.Eval(tagsQuery, (r) => Tags.HasTags(obj) && obj._tags[r]);\r\n    }\r\n}\r\n"]}