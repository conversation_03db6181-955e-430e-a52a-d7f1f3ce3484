{"version": 3, "file": "photoDome.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Helpers/photoDome.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C;;;;;GAKG;AACH,MAAM,OAAO,SAAU,SAAQ,WAAoB;IAa/C;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,YAAY,CAAC,KAAc;QAClC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;;;;OAKG;IACH,IAAW,SAAS,CAAC,KAAa;QAC9B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAES,YAAY,CAAC,aAAqB,EAAE,KAAY,EAAE,OAAY;QACpE,OAAO,IAAI,OAAO,CACd,aAAa,EACb,KAAK,EACL,CAAC,OAAO,CAAC,eAAe,EACxB,CAAC,IAAI,CAAC,iBAAiB,EACvB,SAAS,EACT,GAAG,EAAE;YACD,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC;QAC5C,CAAC,EACD,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;YACnB,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,OAAO,IAAI,uBAAuB,CAAC,CAAC;YAE/E,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;aACpC;QACL,CAAC,CACJ,CAAC;IACN,CAAC;;AA/DD;;GAEG;AACoB,yBAAe,GAAG,WAAW,CAAC,eAAe,CAAC;AACrE;;GAEG;AACoB,wBAAc,GAAG,WAAW,CAAC,cAAc,CAAC;AACnE;;GAEG;AACoB,yBAAe,GAAG,WAAW,CAAC,eAAe,CAAC", "sourcesContent": ["import type { Scene } from \"../scene\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { TextureDome } from \"./textureDome\";\r\n\r\n/**\r\n * Display a 360 degree photo on an approximately spherical surface, useful for VR applications or skyboxes.\r\n * As a subclass of TransformNode, this allow parenting to the camera with different locations in the scene.\r\n * This class achieves its effect with a Texture and a correctly configured BackgroundMaterial on an inverted sphere.\r\n * Potential additions to this helper include zoom and and non-infinite distance rendering effects.\r\n */\r\nexport class PhotoDome extends TextureDome<Texture> {\r\n    /**\r\n     * Define the image as a Monoscopic panoramic 360 image.\r\n     */\r\n    public static readonly MODE_MONOSCOPIC = TextureDome.MODE_MONOSCOPIC;\r\n    /**\r\n     * Define the image as a Stereoscopic TopBottom/OverUnder panoramic 360 image.\r\n     */\r\n    public static readonly MODE_TOPBOTTOM = TextureDome.MODE_TOPBOTTOM;\r\n    /**\r\n     * Define the image as a Stereoscopic Side by Side panoramic 360 image.\r\n     */\r\n    public static readonly MODE_SIDEBYSIDE = TextureDome.MODE_SIDEBYSIDE;\r\n    /**\r\n     * Gets or sets the texture being displayed on the sphere\r\n     */\r\n    public get photoTexture(): Texture {\r\n        return this.texture;\r\n    }\r\n\r\n    /**\r\n     * sets the texture being displayed on the sphere\r\n     */\r\n    public set photoTexture(value: Texture) {\r\n        this.texture = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the current video mode for the video. It can be:\r\n     * * TextureDome.MODE_MONOSCOPIC : Define the texture source as a Monoscopic panoramic 360.\r\n     * * TextureDome.MODE_TOPBOTTOM  : Define the texture source as a Stereoscopic TopBottom/OverUnder panoramic 360.\r\n     * * TextureDome.MODE_SIDEBYSIDE : Define the texture source as a Stereoscopic Side by Side panoramic 360.\r\n     */\r\n    public get imageMode(): number {\r\n        return this.textureMode;\r\n    }\r\n    /**\r\n     * Sets the current video mode for the video. It can be:\r\n     * * TextureDome.MODE_MONOSCOPIC : Define the texture source as a Monoscopic panoramic 360.\r\n     * * TextureDome.MODE_TOPBOTTOM  : Define the texture source as a Stereoscopic TopBottom/OverUnder panoramic 360.\r\n     * * TextureDome.MODE_SIDEBYSIDE : Define the texture source as a Stereoscopic Side by Side panoramic 360.\r\n     */\r\n    public set imageMode(value: number) {\r\n        this.textureMode = value;\r\n    }\r\n\r\n    protected _initTexture(urlsOrElement: string, scene: Scene, options: any): Texture {\r\n        return new Texture(\r\n            urlsOrElement,\r\n            scene,\r\n            !options.generateMipMaps,\r\n            !this._useDirectMapping,\r\n            undefined,\r\n            () => {\r\n                this.onLoadObservable.notifyObservers();\r\n            },\r\n            (message, exception) => {\r\n                this.onLoadErrorObservable.notifyObservers(message || \"Unknown error occured\");\r\n\r\n                if (this.onError) {\r\n                    this.onError(message, exception);\r\n                }\r\n            }\r\n        );\r\n    }\r\n}\r\n"]}