{"version": 3, "file": "renderTargetWrapper.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Engines/renderTargetWrapper.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,qBAAqB,EAAE,MAAM,uCAAuC,CAAC;AAG9E,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAcxC;;GAEG;AACH,MAAM,OAAO,mBAAmB;IA6B5B;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAW,8BAA8B;QACrC,OAAO,IAAI,CAAC,+BAA+B,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAA2C,IAAI,CAAC,KAAM,CAAC,KAAK,IAAY,IAAI,CAAC,KAAK,CAAC;IACvF,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAA2C,IAAI,CAAC,KAAM,CAAC,MAAM,IAAY,IAAI,CAAC,KAAK,CAAC;IACxF,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAA4D,IAAI,CAAC,KAAM,CAAC,MAAM,IAAI,CAAC,CAAC;IACxF,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;;;;OAMG;IACI,UAAU,CAAC,KAAa,EAAE,iBAAiB,GAAG,IAAI,EAAE,KAAK,GAAG,KAAK;QACpE,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;YAClC,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ;YACxB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,4CAA4C,CAAC,IAAI,EAAE,KAAK,EAAE,iBAAiB,CAAC;YAC3F,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,oCAAoC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACrE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,OAAgB,EAAE,MAAe,EAAE,IAAiB,EAAE,MAAkB,EAAE,KAAc;QArJ5F,cAAS,GAAgC,IAAI,CAAC;QAC9C,iBAAY,GAAuB,IAAI,CAAC;QACxC,kBAAa,GAAuB,IAAI,CAAC;QAEjD,gBAAgB;QACT,aAAQ,GAAG,CAAC,CAAC;QAEpB,gBAAgB;QACT,iBAAY,GAAuB,IAAI,CAAC;QAC/C,gBAAgB;QACT,2BAAsB,GAAY,KAAK,CAAC;QAC/C,gBAAgB;QACT,yBAAoB,GAAY,KAAK,CAAC;QAI7C,gBAAgB;QACT,oCAA+B,GAAY,KAAK,CAAC;QAqIpD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,QAAiE;QAChF,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACzB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;SAC7B;aAAM,IAAI,QAAQ,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC,CAAC;SAC/B;aAAM;YACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACzB;IACL,CAAC;IAED;;;;;OAKG;IACI,UAAU,CAAC,OAAwB,EAAE,QAAgB,CAAC,EAAE,kBAA2B,IAAI;QAC1F,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;SACvB;QACD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,OAAO,EAAE;YACnC,OAAO;SACV;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,eAAe,EAAE;YAC1C,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;SACnC;QAED,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,MAAgB,EAAE,KAAe;QAC3D,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC9B,CAAC;IAED;;;;;OAKG;IACI,oBAAoB,CAAC,QAAgB,CAAC,EAAE,KAAc,EAAE,IAAa;QACxE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;SAC3B;QACD,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;SAC1B;QAED,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,IAAI,CAAC,EAAE;YACnC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;SACrC;QACD,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,IAAI,CAAC,EAAE;YACjC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;SACnC;IACL,CAAC;IAED;;;;;;;;;OASG;IACI,yBAAyB,CAC5B,qBAA6B,CAAC,EAC9B,oBAA6B,IAAI,EACjC,kBAA2B,KAAK,EAChC,UAAkB,CAAC,EACnB,SAAiB,SAAS,CAAC,2BAA2B,EACtD,KAAc;QAEd,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;QAErC,IAAI,CAAC,+BAA+B,GAAG,eAAe,CAAC;QACvD,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;QACvC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAC9D,IAAI,CAAC,KAAK,EACV;YACI,iBAAiB;YACjB,kBAAkB;YAClB,eAAe;YACf,MAAM,EAAE,IAAI,CAAC,OAAO;YACpB,OAAO;YACP,kBAAkB,EAAE,MAAM;YAC1B,KAAK;SACR,EACD,IAAI,CACP,CAAC;QAEF,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,YAAiC;QAChD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,YAAY,CAAC,oBAAoB,EAAE;gBACnC,YAAY,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;aAC/C;YAED,YAAY,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAC9D,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,CAAC;SACnD;IACL,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,MAAuB;QACtC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SACpC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAES,yBAAyB;QAC/B,IAAI,GAAG,GAAkC,IAAI,CAAC;QAE9C,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;YACnC,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzC,IAAI,oBAAoB,GAAG,KAAK,CAAC;gBACjC,IAAI,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC;gBACvC,IAAI,kBAAkB,GAAG,CAAC,CAAC,CAAC;gBAE5B,MAAM,iBAAiB,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC;gBACxE,IAAI,iBAAiB,KAAK,qBAAqB,CAAC,KAAK,IAAI,iBAAiB,KAAK,qBAAqB,CAAC,YAAY,EAAE;oBAC/G,oBAAoB,GAAG,IAAI,CAAC;oBAC5B,kBAAkB,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;oBAClE,YAAY,EAAE,CAAC;iBAClB;gBAED,MAAM,aAAa,GAAa,EAAE,CAAC;gBACnC,MAAM,KAAK,GAAa,EAAE,CAAC;gBAC3B,MAAM,OAAO,GAAa,EAAE,CAAC;gBAC7B,MAAM,WAAW,GAAa,EAAE,CAAC;gBACjC,MAAM,SAAS,GAAa,EAAE,CAAC;gBAC/B,MAAM,UAAU,GAAa,EAAE,CAAC;gBAChC,MAAM,WAAW,GAAa,EAAE,CAAC;gBACjC,MAAM,qBAAqB,GAA6B,EAAE,CAAC;gBAE3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,EAAE,CAAC,EAAE;oBACnC,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;oBAEhC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;oBACzC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACzB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBAE7B,MAAM,KAAK,GAAG,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACtD,IAAI,KAAK,KAAK,SAAS,EAAE;wBACrB,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrB,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;qBACvB;yBAAM;wBACH,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;wBAC5C,IAAI,OAAO,CAAC,SAAS,EAAE;4BACnB,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;4BAC7C,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;yBACnC;6BAAM,IAAI,OAAO,CAAC,MAAM,EAAE;4BACvB,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;4BAC7C,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;yBACvB,CAAC;;;2BAGC;6BAAM,IAAI,OAAO,CAAC,IAAI,EAAE;4BACvB,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;4BACvC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;yBACnC;6BAAM;4BACH,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;4BACvC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;yBACvB;qBACJ;oBAED,IAAI,IAAI,CAAC,YAAY,EAAE;wBACnB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;qBAC7C;oBACD,IAAI,IAAI,CAAC,aAAa,EAAE;wBACpB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;qBAC/C;iBACJ;gBAED,MAAM,UAAU,GAA8B;oBAC1C,aAAa;oBACb,eAAe,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,eAAe;oBAChD,mBAAmB,EAAE,IAAI,CAAC,oBAAoB;oBAC9C,qBAAqB,EAAE,IAAI,CAAC,sBAAsB;oBAClD,oBAAoB;oBACpB,kBAAkB;oBAClB,KAAK;oBACL,OAAO;oBACP,YAAY;oBACZ,WAAW;oBACX,SAAS;oBACT,UAAU;oBACV,WAAW;oBACX,KAAK,EAAE,IAAI,CAAC,KAAK;iBACpB,CAAC;gBACF,MAAM,IAAI,GAAG;oBACT,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;iBACtB,CAAC;gBAEF,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;gBAEhE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,EAAE,CAAC,EAAE;oBACnC,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;wBACvB,SAAS;qBACZ;oBACD,MAAM,KAAK,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;oBAC9D,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,QAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC3C;aACJ;SACJ;aAAM;YACH,MAAM,OAAO,GAAgC,EAAE,CAAC;YAEhD,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACxD,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,EAAE,eAAe,IAAI,KAAK,CAAC;YACjE,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAC5D,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC;YAClD,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;YAClC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC;YACtC,OAAO,CAAC,iBAAiB,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;YAC5C,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YAE3B,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;aACzE;iBAAM;gBACH,MAAM,IAAI,GAAG;oBACT,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,SAAS;iBAC3D,CAAC;gBAEF,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;aAC/D;YACD,IAAI,GAAG,CAAC,OAAO,EAAE;gBACb,GAAG,CAAC,OAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;aAC/B;SACJ;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAES,wBAAwB,CAAC,MAA2B;QAC1D,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,EAAE;YACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBAC5C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC1D,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;aACtC;SACJ;QACD,IAAI,IAAI,CAAC,oBAAoB,IAAI,MAAM,CAAC,oBAAoB,EAAE;YAC1D,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;YACnE,MAAM,CAAC,oBAAoB,CAAC,OAAO,GAAG,IAAI,CAAC;SAC9C;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IACrC,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC7C,IAAI,CAAC,GAAG,EAAE;YACN,OAAO;SACV;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC;YAC5D,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;YAChD,MAAM,QAAQ,GACV,YAAY,KAAK,SAAS,CAAC,6BAA6B;gBACxD,YAAY,KAAK,SAAS,CAAC,8BAA8B;gBACzD,YAAY,KAAK,SAAS,CAAC,gCAAgC,CAAC;YAEhE,GAAG,CAAC,yBAAyB,CACzB,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAC7C,QAAQ,EACR,IAAI,CAAC,+BAA+B,EACpC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EACjC,MAAM,EACN,IAAI,CAAC,yBAAyB,CACjC,CAAC;SACL;QAED,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE;YAClB,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAChC;QAED,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QACnC,GAAG,CAAC,OAAO,EAAE,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;gBAClD,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;aAC/B;SACJ;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,uBAAuB,GAAG,KAAK;QAC1C,IAAI,CAAC,uBAAuB,EAAE;YAC1B,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;YACrC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACjC,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;QAED,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;CACJ", "sourcesContent": ["import type { InternalTexture } from \"../Materials/Textures/internalTexture\";\r\nimport { InternalTextureSource } from \"../Materials/Textures/internalTexture\";\r\nimport type { RenderTargetCreationOptions, TextureSize } from \"../Materials/Textures/textureCreationOptions\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Constants } from \"./constants\";\r\nimport type { ThinEngine } from \"./thinEngine\";\r\nimport type { IMultiRenderTargetOptions } from \"../Materials/Textures/multiRenderTarget\";\r\n\r\n/**\r\n * An interface enforcing the renderTarget accessor to used by render target textures.\r\n */\r\nexport interface IRenderTargetTexture {\r\n    /**\r\n     * Entry point to access the wrapper on a texture.\r\n     */\r\n    renderTarget: Nullable<RenderTargetWrapper>;\r\n}\r\n\r\n/**\r\n * Wrapper around a render target (either single or multi textures)\r\n */\r\nexport class RenderTargetWrapper {\r\n    protected _engine: ThinEngine;\r\n    private _size: TextureSize;\r\n    private _isCube: boolean;\r\n    private _isMulti: boolean;\r\n    private _textures: Nullable<InternalTexture[]> = null;\r\n    private _faceIndices: Nullable<number[]> = null;\r\n    private _layerIndices: Nullable<number[]> = null;\r\n    private _depthStencilTextureLabel?: string;\r\n    /** @internal */\r\n    public _samples = 1;\r\n\r\n    /** @internal */\r\n    public _attachments: Nullable<number[]> = null;\r\n    /** @internal */\r\n    public _generateStencilBuffer: boolean = false;\r\n    /** @internal */\r\n    public _generateDepthBuffer: boolean = false;\r\n\r\n    /** @internal */\r\n    public _depthStencilTexture: Nullable<InternalTexture>;\r\n    /** @internal */\r\n    public _depthStencilTextureWithStencil: boolean = false;\r\n\r\n    /**\r\n     * Gets or sets the label of the render target wrapper (optional, for debugging purpose)\r\n     */\r\n    public label?: string;\r\n\r\n    /**\r\n     * Gets the depth/stencil texture (if created by a createDepthStencilTexture() call)\r\n     */\r\n    public get depthStencilTexture() {\r\n        return this._depthStencilTexture;\r\n    }\r\n\r\n    /**\r\n     * Indicates if the depth/stencil texture has a stencil aspect\r\n     */\r\n    public get depthStencilTextureWithStencil() {\r\n        return this._depthStencilTextureWithStencil;\r\n    }\r\n\r\n    /**\r\n     * Defines if the render target wrapper is for a cube texture or if false a 2d texture\r\n     */\r\n    public get isCube(): boolean {\r\n        return this._isCube;\r\n    }\r\n\r\n    /**\r\n     * Defines if the render target wrapper is for a single or multi target render wrapper\r\n     */\r\n    public get isMulti(): boolean {\r\n        return this._isMulti;\r\n    }\r\n\r\n    /**\r\n     * Defines if the render target wrapper is for a single or an array of textures\r\n     */\r\n    public get is2DArray(): boolean {\r\n        return this.layers > 0;\r\n    }\r\n\r\n    /**\r\n     * Gets the size of the render target wrapper (used for cubes, as width=height in this case)\r\n     */\r\n    public get size(): number {\r\n        return this.width;\r\n    }\r\n\r\n    /**\r\n     * Gets the width of the render target wrapper\r\n     */\r\n    public get width(): number {\r\n        return (<{ width: number; height: number }>this._size).width || <number>this._size;\r\n    }\r\n\r\n    /**\r\n     * Gets the height of the render target wrapper\r\n     */\r\n    public get height(): number {\r\n        return (<{ width: number; height: number }>this._size).height || <number>this._size;\r\n    }\r\n\r\n    /**\r\n     * Gets the number of layers of the render target wrapper (only used if is2DArray is true and wrapper is not a multi render target)\r\n     */\r\n    public get layers(): number {\r\n        return (<{ width: number; height: number; layers?: number }>this._size).layers || 0;\r\n    }\r\n\r\n    /**\r\n     * Gets the render texture. If this is a multi render target, gets the first texture\r\n     */\r\n    public get texture(): Nullable<InternalTexture> {\r\n        return this._textures?.[0] ?? null;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of render textures. If we are not in a multi render target, the list will be null (use the texture getter instead)\r\n     */\r\n    public get textures(): Nullable<InternalTexture[]> {\r\n        return this._textures;\r\n    }\r\n\r\n    /**\r\n     * Gets the face indices that correspond to the list of render textures. If we are not in a multi render target, the list will be null\r\n     */\r\n    public get faceIndices(): Nullable<number[]> {\r\n        return this._faceIndices;\r\n    }\r\n\r\n    /**\r\n     * Gets the layer indices that correspond to the list of render textures. If we are not in a multi render target, the list will be null\r\n     */\r\n    public get layerIndices(): Nullable<number[]> {\r\n        return this._layerIndices;\r\n    }\r\n\r\n    /**\r\n     * Gets the sample count of the render target\r\n     */\r\n    public get samples(): number {\r\n        return this._samples;\r\n    }\r\n\r\n    /**\r\n     * Sets the sample count of the render target\r\n     * @param value sample count\r\n     * @param initializeBuffers If set to true, the engine will make an initializing call to drawBuffers (only used when isMulti=true).\r\n     * @param force true to force calling the update sample count engine function even if the current sample count is equal to value\r\n     * @returns the sample count that has been set\r\n     */\r\n    public setSamples(value: number, initializeBuffers = true, force = false): number {\r\n        if (this.samples === value && !force) {\r\n            return value;\r\n        }\r\n\r\n        const result = this._isMulti\r\n            ? this._engine.updateMultipleRenderTargetTextureSampleCount(this, value, initializeBuffers)\r\n            : this._engine.updateRenderTargetTextureSampleCount(this, value);\r\n        this._samples = value;\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Initializes the render target wrapper\r\n     * @param isMulti true if the wrapper is a multi render target\r\n     * @param isCube true if the wrapper should render to a cube texture\r\n     * @param size size of the render target (width/height/layers)\r\n     * @param engine engine used to create the render target\r\n     * @param label defines the label to use for the wrapper (for debugging purpose only)\r\n     */\r\n    constructor(isMulti: boolean, isCube: boolean, size: TextureSize, engine: ThinEngine, label?: string) {\r\n        this._isMulti = isMulti;\r\n        this._isCube = isCube;\r\n        this._size = size;\r\n        this._engine = engine;\r\n        this._depthStencilTexture = null;\r\n        this.label = label;\r\n    }\r\n\r\n    /**\r\n     * Sets the render target texture(s)\r\n     * @param textures texture(s) to set\r\n     */\r\n    public setTextures(textures: Nullable<InternalTexture> | Nullable<InternalTexture[]>): void {\r\n        if (Array.isArray(textures)) {\r\n            this._textures = textures;\r\n        } else if (textures) {\r\n            this._textures = [textures];\r\n        } else {\r\n            this._textures = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set a texture in the textures array\r\n     * @param texture The texture to set\r\n     * @param index The index in the textures array to set\r\n     * @param disposePrevious If this function should dispose the previous texture\r\n     */\r\n    public setTexture(texture: InternalTexture, index: number = 0, disposePrevious: boolean = true): void {\r\n        if (!this._textures) {\r\n            this._textures = [];\r\n        }\r\n        if (this._textures[index] === texture) {\r\n            return;\r\n        }\r\n\r\n        if (this._textures[index] && disposePrevious) {\r\n            this._textures[index].dispose();\r\n        }\r\n\r\n        this._textures[index] = texture;\r\n    }\r\n\r\n    /**\r\n     * Sets the layer and face indices of every render target texture bound to each color attachment\r\n     * @param layers The layers of each texture to be set\r\n     * @param faces The faces of each texture to be set\r\n     */\r\n    public setLayerAndFaceIndices(layers: number[], faces: number[]) {\r\n        this._layerIndices = layers;\r\n        this._faceIndices = faces;\r\n    }\r\n\r\n    /**\r\n     * Sets the layer and face indices of a texture in the textures array that should be bound to each color attachment\r\n     * @param index The index of the texture in the textures array to modify\r\n     * @param layer The layer of the texture to be set\r\n     * @param face The face of the texture to be set\r\n     */\r\n    public setLayerAndFaceIndex(index: number = 0, layer?: number, face?: number): void {\r\n        if (!this._layerIndices) {\r\n            this._layerIndices = [];\r\n        }\r\n        if (!this._faceIndices) {\r\n            this._faceIndices = [];\r\n        }\r\n\r\n        if (layer !== undefined && layer >= 0) {\r\n            this._layerIndices[index] = layer;\r\n        }\r\n        if (face !== undefined && face >= 0) {\r\n            this._faceIndices[index] = face;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Creates the depth/stencil texture\r\n     * @param comparisonFunction Comparison function to use for the texture\r\n     * @param bilinearFiltering true if bilinear filtering should be used when sampling the texture\r\n     * @param generateStencil true if the stencil aspect should also be created\r\n     * @param samples sample count to use when creating the texture\r\n     * @param format format of the depth texture\r\n     * @param label defines the label to use for the texture (for debugging purpose only)\r\n     * @returns the depth/stencil created texture\r\n     */\r\n    public createDepthStencilTexture(\r\n        comparisonFunction: number = 0,\r\n        bilinearFiltering: boolean = true,\r\n        generateStencil: boolean = false,\r\n        samples: number = 1,\r\n        format: number = Constants.TEXTUREFORMAT_DEPTH32_FLOAT,\r\n        label?: string\r\n    ): InternalTexture {\r\n        this._depthStencilTexture?.dispose();\r\n\r\n        this._depthStencilTextureWithStencil = generateStencil;\r\n        this._depthStencilTextureLabel = label;\r\n        this._depthStencilTexture = this._engine.createDepthStencilTexture(\r\n            this._size,\r\n            {\r\n                bilinearFiltering,\r\n                comparisonFunction,\r\n                generateStencil,\r\n                isCube: this._isCube,\r\n                samples,\r\n                depthTextureFormat: format,\r\n                label,\r\n            },\r\n            this\r\n        );\r\n\r\n        return this._depthStencilTexture;\r\n    }\r\n\r\n    /**\r\n     * Shares the depth buffer of this render target with another render target.\r\n     * @internal\r\n     * @param renderTarget Destination renderTarget\r\n     */\r\n    public _shareDepth(renderTarget: RenderTargetWrapper): void {\r\n        if (this._depthStencilTexture) {\r\n            if (renderTarget._depthStencilTexture) {\r\n                renderTarget._depthStencilTexture.dispose();\r\n            }\r\n\r\n            renderTarget._depthStencilTexture = this._depthStencilTexture;\r\n            this._depthStencilTexture.incrementReferences();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _swapAndDie(target: InternalTexture): void {\r\n        if (this.texture) {\r\n            this.texture._swapAndDie(target);\r\n        }\r\n        this._textures = null;\r\n        this.dispose(true);\r\n    }\r\n\r\n    protected _cloneRenderTargetWrapper(): Nullable<RenderTargetWrapper> {\r\n        let rtw: Nullable<RenderTargetWrapper> = null;\r\n\r\n        if (this._isMulti) {\r\n            const textureArray = this.textures;\r\n            if (textureArray && textureArray.length > 0) {\r\n                let generateDepthTexture = false;\r\n                let textureCount = textureArray.length;\r\n                let depthTextureFormat = -1;\r\n\r\n                const lastTextureSource = textureArray[textureArray.length - 1]._source;\r\n                if (lastTextureSource === InternalTextureSource.Depth || lastTextureSource === InternalTextureSource.DepthStencil) {\r\n                    generateDepthTexture = true;\r\n                    depthTextureFormat = textureArray[textureArray.length - 1].format;\r\n                    textureCount--;\r\n                }\r\n\r\n                const samplingModes: number[] = [];\r\n                const types: number[] = [];\r\n                const formats: number[] = [];\r\n                const targetTypes: number[] = [];\r\n                const faceIndex: number[] = [];\r\n                const layerIndex: number[] = [];\r\n                const layerCounts: number[] = [];\r\n                const internalTexture2Index: { [id: number]: number } = {};\r\n\r\n                for (let i = 0; i < textureCount; ++i) {\r\n                    const texture = textureArray[i];\r\n\r\n                    samplingModes.push(texture.samplingMode);\r\n                    types.push(texture.type);\r\n                    formats.push(texture.format);\r\n\r\n                    const index = internalTexture2Index[texture.uniqueId];\r\n                    if (index !== undefined) {\r\n                        targetTypes.push(-1);\r\n                        layerCounts.push(0);\r\n                    } else {\r\n                        internalTexture2Index[texture.uniqueId] = i;\r\n                        if (texture.is2DArray) {\r\n                            targetTypes.push(Constants.TEXTURE_2D_ARRAY);\r\n                            layerCounts.push(texture.depth);\r\n                        } else if (texture.isCube) {\r\n                            targetTypes.push(Constants.TEXTURE_CUBE_MAP);\r\n                            layerCounts.push(0);\r\n                        } /*else if (texture.isCubeArray) {\r\n                            targetTypes.push(Constants.TEXTURE_CUBE_MAP_ARRAY);\r\n                            layerCounts.push(texture.depth);\r\n                        }*/ else if (texture.is3D) {\r\n                            targetTypes.push(Constants.TEXTURE_3D);\r\n                            layerCounts.push(texture.depth);\r\n                        } else {\r\n                            targetTypes.push(Constants.TEXTURE_2D);\r\n                            layerCounts.push(0);\r\n                        }\r\n                    }\r\n\r\n                    if (this._faceIndices) {\r\n                        faceIndex.push(this._faceIndices[i] ?? 0);\r\n                    }\r\n                    if (this._layerIndices) {\r\n                        layerIndex.push(this._layerIndices[i] ?? 0);\r\n                    }\r\n                }\r\n\r\n                const optionsMRT: IMultiRenderTargetOptions = {\r\n                    samplingModes,\r\n                    generateMipMaps: textureArray[0].generateMipMaps,\r\n                    generateDepthBuffer: this._generateDepthBuffer,\r\n                    generateStencilBuffer: this._generateStencilBuffer,\r\n                    generateDepthTexture,\r\n                    depthTextureFormat,\r\n                    types,\r\n                    formats,\r\n                    textureCount,\r\n                    targetTypes,\r\n                    faceIndex,\r\n                    layerIndex,\r\n                    layerCounts,\r\n                    label: this.label,\r\n                };\r\n                const size = {\r\n                    width: this.width,\r\n                    height: this.height,\r\n                };\r\n\r\n                rtw = this._engine.createMultipleRenderTarget(size, optionsMRT);\r\n\r\n                for (let i = 0; i < textureCount; ++i) {\r\n                    if (targetTypes[i] !== -1) {\r\n                        continue;\r\n                    }\r\n                    const index = internalTexture2Index[textureArray[i].uniqueId];\r\n                    rtw.setTexture(rtw.textures![index], i);\r\n                }\r\n            }\r\n        } else {\r\n            const options: RenderTargetCreationOptions = {};\r\n\r\n            options.generateDepthBuffer = this._generateDepthBuffer;\r\n            options.generateMipMaps = this.texture?.generateMipMaps ?? false;\r\n            options.generateStencilBuffer = this._generateStencilBuffer;\r\n            options.samplingMode = this.texture?.samplingMode;\r\n            options.type = this.texture?.type;\r\n            options.format = this.texture?.format;\r\n            options.noColorAttachment = !this._textures;\r\n            options.label = this.label;\r\n\r\n            if (this.isCube) {\r\n                rtw = this._engine.createRenderTargetCubeTexture(this.width, options);\r\n            } else {\r\n                const size = {\r\n                    width: this.width,\r\n                    height: this.height,\r\n                    layers: this.is2DArray ? this.texture?.depth : undefined,\r\n                };\r\n\r\n                rtw = this._engine.createRenderTargetTexture(size, options);\r\n            }\r\n            if (rtw.texture) {\r\n                rtw.texture!.isReady = true;\r\n            }\r\n        }\r\n\r\n        return rtw;\r\n    }\r\n\r\n    protected _swapRenderTargetWrapper(target: RenderTargetWrapper): void {\r\n        if (this._textures && target._textures) {\r\n            for (let i = 0; i < this._textures.length; ++i) {\r\n                this._textures[i]._swapAndDie(target._textures[i], false);\r\n                target._textures[i].isReady = true;\r\n            }\r\n        }\r\n        if (this._depthStencilTexture && target._depthStencilTexture) {\r\n            this._depthStencilTexture._swapAndDie(target._depthStencilTexture);\r\n            target._depthStencilTexture.isReady = true;\r\n        }\r\n\r\n        this._textures = null;\r\n        this._depthStencilTexture = null;\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuild(): void {\r\n        const rtw = this._cloneRenderTargetWrapper();\r\n        if (!rtw) {\r\n            return;\r\n        }\r\n\r\n        if (this._depthStencilTexture) {\r\n            const samplingMode = this._depthStencilTexture.samplingMode;\r\n            const format = this._depthStencilTexture.format;\r\n            const bilinear =\r\n                samplingMode === Constants.TEXTURE_BILINEAR_SAMPLINGMODE ||\r\n                samplingMode === Constants.TEXTURE_TRILINEAR_SAMPLINGMODE ||\r\n                samplingMode === Constants.TEXTURE_LINEAR_LINEAR_MIPNEAREST;\r\n\r\n            rtw.createDepthStencilTexture(\r\n                this._depthStencilTexture._comparisonFunction,\r\n                bilinear,\r\n                this._depthStencilTextureWithStencil,\r\n                this._depthStencilTexture.samples,\r\n                format,\r\n                this._depthStencilTextureLabel\r\n            );\r\n        }\r\n\r\n        if (this.samples > 1) {\r\n            rtw.setSamples(this.samples);\r\n        }\r\n\r\n        rtw._swapRenderTargetWrapper(this);\r\n        rtw.dispose();\r\n    }\r\n\r\n    /**\r\n     * Releases the internal render textures\r\n     */\r\n    public releaseTextures(): void {\r\n        if (this._textures) {\r\n            for (let i = 0; i < this._textures?.length ?? 0; ++i) {\r\n                this._textures[i].dispose();\r\n            }\r\n        }\r\n        this._textures = null;\r\n    }\r\n\r\n    /**\r\n     * Disposes the whole render target wrapper\r\n     * @param disposeOnlyFramebuffers true if only the frame buffers should be released (used for the WebGL engine). If false, all the textures will also be released\r\n     */\r\n    public dispose(disposeOnlyFramebuffers = false): void {\r\n        if (!disposeOnlyFramebuffers) {\r\n            this._depthStencilTexture?.dispose();\r\n            this._depthStencilTexture = null;\r\n            this.releaseTextures();\r\n        }\r\n\r\n        this._engine._releaseRenderTargetWrapper(this);\r\n    }\r\n}\r\n"]}