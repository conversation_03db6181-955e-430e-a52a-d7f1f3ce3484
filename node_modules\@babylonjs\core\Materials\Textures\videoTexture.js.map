{"version": 3, "file": "videoTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/videoTexture.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AACzC,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C,OAAO,EAAE,OAAO,EAAE,MAAM,kCAAkC,CAAC;AAC3D,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAGpD,OAAO,8CAA8C,CAAC;AACtD,OAAO,gDAAgD,CAAC;AACxD,OAAO,EAAE,SAAS,EAAE,iCAA6B;AACjD,OAAO,EAAE,aAAa,EAAE,gCAA4B;AAEpD,SAAS,YAAY,CAAC,KAAuB;IACzC,qCAAqC;IACrC,OAAO,KAAK,CAAC,UAAU,EAAE;QACrB,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;KACvC;IAED,mBAAmB;IACnB,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;IAEvB,sHAAsH;IACtH,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;IAEf,8FAA8F;IAC9F,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC;AA2CD;;;;GAIG;AACH,MAAM,OAAO,YAAa,SAAQ,OAAO;IAcrC;;;OAGG;IACH,IAAW,+BAA+B;QACtC,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE;YACxC,IAAI,CAAC,gCAAgC,GAAG,IAAI,UAAU,EAAW,CAAC;SACrE;QACD,OAAO,IAAI,CAAC,gCAAgC,CAAC;IACjD,CAAC;IAoBO,aAAa,CAAC,MAAW;QAC7B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SAClC;aAAM;YACH,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SACjC;IACL,CAAC;IAEO,WAAW;QACf,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;YAC/B,IAAI,MAAM,EAAE,IAAI,KAAK,iBAAiB,EAAE;gBACpC,IAAI,IAAI,CAAC,gCAAgC,IAAI,IAAI,CAAC,gCAAgC,CAAC,YAAY,EAAE,EAAE;oBAC/F,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oBAC5D,OAAO;iBACV;qBAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;oBAC1B,MAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;oBAC1F,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;oBACxB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;oBACzB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,EAAE;wBACpC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;oBACpC,CAAC,CAAC,CAAC;oBACH,OAAO;iBACV;aACJ;YAED,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,YACI,IAAsB,EACtB,GAAyC,EACzC,KAAsB,EACtB,eAAe,GAAG,KAAK,EACvB,OAAO,GAAG,KAAK,EACf,eAAuB,OAAO,CAAC,sBAAsB,EACrD,WAA0C,EAAE,EAC5C,OAA+D,EAC/D,SAAiB,SAAS,CAAC,kBAAkB;QAE7C,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;QAzF1C,qBAAgB,GAA8B,IAAI,CAAC;QACnD,qCAAgC,GAAkC,IAAI,CAAC;QAcvE,wBAAmB,GAAG,KAAK,CAAC;QAC5B,6BAAwB,GAAG,KAAK,CAAC;QAIjC,aAAQ,GAAG,CAAC,CAAC,CAAC;QAEd,gBAAW,GAAmD,IAAI,CAAC;QAEnE,gBAAW,GAAG,KAAK,CAAC;QAE5B;;WAEG;QAEa,YAAO,GAAG,IAAI,CAAC;QAuKvB,2BAAsB,GAAG,GAAS,EAAE;YACxC,8CAA8C;YAC9C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACvB,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;aAC3B;YAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAG,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE;gBACxI,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC;gBACtC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC;aACzC;iBAAM;gBACH,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;gBACvC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;gBACvC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;aACjC;YAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YACjJ,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,IAAI,SAAS,CAAC,kBAAkB,CAAC;YAEpE,8FAA8F;YAC9F,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;YACnB,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAClC,CAAC,CAAC;QAEM,2BAAsB,GAAG,GAAS,EAAE;YACxC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACvB,IAAI,IAAI,CAAC,wBAAwB,EAAE;oBAC/B,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;iBACzC;qBAAM;oBACH,OAAO;iBACV;aACJ;YAED,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACnE,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAE9B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,EAAE;gBAC1F,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;gBACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;gBAClC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,EAAE;oBACxB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC;oBAC5B,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC;oBAClC,IAAI,CAAC,sBAAsB,EAAE,CAAC;oBAC9B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;wBACnB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;qBACtB;oBACD,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,EAAE;wBACtC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;qBAC/C;gBACL,CAAC,CAAC;gBACF,IAAI,CAAC,WAAW,EAAE,CAAC;aACtB;iBAAM;gBACH,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC9B,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,EAAE;oBACtC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;iBAC/C;aACJ;QACL,CAAC,CAAC;QAEM,WAAM,GAAG,GAAS,EAAE;YACxB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACvB,OAAO;aACV;YAED,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAChC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;aACxB;QACL,CAAC,CAAC;QAoCQ,2BAAsB,GAAG,GAAS,EAAE;YAC1C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACvB,OAAO;aACV;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE;gBACtD,OAAO;aACV;YACD,IAAI,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,OAAO;aACV;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAG,CAAC,UAAU,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;gBAC3B,OAAO;aACV;YAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;YAExB,IAAI,CAAC,UAAU,EAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpI,CAAC,CAAC;QArOE,IAAI,CAAC,SAAS,GAAG;YACb,QAAQ,EAAE,IAAI;YACd,IAAI,EAAE,IAAI;YACV,iBAAiB,EAAE,IAAI;YACvB,GAAG,QAAQ;SACd,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC;QACzC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;QAE1D,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,IAAI,CAAC,OAAO,EAAE,qBAAqB,EAAE;YACrC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1E;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,EAAE;YACxC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBACvB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;aAC7C;YACD,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,SAAS,EAAE;gBACvC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;aACjD;YACD,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,SAAS,EAAE;gBACnC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;aACzC;YACD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK,SAAS,EAAE;gBACpC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;aAC3C;YAED,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;YAC3C,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACnE,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACnE,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACvE,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAEpD,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;gBACzB,IAAI,CAAC,WAAW,EAAE,CAAC;aACtB;SACJ;QAED,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;QAC5G,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,6BAA6B,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC7F,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;QACjF,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,kBAAkB,CAAC,EAAE;YAC5E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAG,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,MAAO,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACtG,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;SACxC;aAAM,IAAI,kBAAkB,EAAE;YAC3B,IAAI,CAAC,sBAAsB,EAAE,CAAC;SACjC;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEO,QAAQ,CAAC,GAAyC;QACtD,IAAI,GAAG,YAAY,gBAAgB,EAAE;YACjC,OAAO,GAAG,CAAC,UAAU,CAAC;SACzB;QAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YACzB,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;SACzB;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAEO,SAAS,CAAC,GAAyC;QACvD,IAAU,GAAI,CAAC,QAAQ,EAAE;YACrB,OAAyB,GAAG,CAAC;SAChC;QACD,IAAI,GAAG,YAAY,gBAAgB,EAAE;YACjC,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAC3C,OAAO,GAAG,CAAC;SACd;QACD,MAAM,KAAK,GAAqB,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAChE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YACzB,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAClC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;SACnB;aAAM;YACH,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YACrC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBAChB,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAChD,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;gBACjB,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;YAClC,YAAY,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACjB,CAAC;IAwED;;OAEG;IACI,QAAQ;QACX,IAAI,CAAC,MAAM,EAAE,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,kDAAkD;YAClD,OAAO;SACV;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,SAAkB;QACnC,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO;SACV;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC/C,OAAO;SACV;QACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAuBD;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,GAAW;QACxB,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACnJ,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACvC,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;YAC9C,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;SAChD;QAED,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,6BAA6B,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAChG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,EAAE;YACxC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACtE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACtE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1E,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvD,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACtE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;SACtB;QAED,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,CAAC;IACrC,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,qBAAqB,CAAC,KAAY,EAAE,MAAmB,EAAE,WAAgB,EAAE,OAAO,GAAG,IAAI;QACnG,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAEhE,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE;YAC1B,gDAAgD;YAChD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACjC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,uBAAuB,CAAC;YAChD,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;YAC1B,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;YAC/B,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;YAC3B,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;SAC7B;QAED,KAAK,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QACnC,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACpC,KAAK,CAAC,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;QACtC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;QAEnB,IAAI,KAAK,CAAC,QAAQ,EAAE;YAChB,gDAAgD;SACnD;aAAM,IAAI,KAAK,CAAC,YAAY,KAAK,SAAS,EAAE;YACzC,wBAAwB;YACxB,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC;SAC/B;aAAM;YACH,IAAI,OAAO,KAAK,CAAC,SAAS,IAAI,QAAQ,EAAE;gBACpC,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC;aAC5B;iBAAM;gBACH,0HAA0H;gBAC1H,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,MAAa,CAAC,CAAC;aACvE;SACJ;QAED,OAAO,IAAI,OAAO,CAAe,CAAC,OAAO,EAAE,EAAE;YACzC,MAAM,SAAS,GAAG,GAAG,EAAE;gBACnB,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,iBAAiB,CAAC,CAAC;gBAC1I,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE;oBAC1B,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;wBAC1C,KAAK,CAAC,MAAM,EAAE,CAAC;oBACnB,CAAC,CAAC,CAAC;iBACN;gBACD,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;oBAC1C,YAAY,CAAC,KAAK,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC;gBAEH,OAAO,CAAC,YAAY,CAAC,CAAC;gBACtB,KAAK,CAAC,mBAAmB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YACpD,CAAC,CAAC;YAEF,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAC7C,KAAK,CAAC,IAAI,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,KAAK,CAAC,qBAAqB,CACrC,KAAY,EACZ,WAMyB,EACzB,kBAAmD,KAAK,EACxD,OAAO,GAAG,IAAI;QAEd,IAAI,SAAS,CAAC,YAAY,EAAE;YACxB,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC;gBACrD,KAAK,EAAE,WAAW;gBAClB,KAAK,EAAE,eAAe;aACzB,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YAC3F,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;gBAC1C,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACjC,KAAK,CAAC,IAAI,EAAE,CAAC;gBACjB,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC;SACvB;QAED,OAAO,OAAO,CAAC,MAAM,CAAC,yCAAyC,CAAC,CAAC;IACrE,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,gBAAgB,CAC1B,KAAY,EACZ,OAA6C,EAC7C,WAMyB,EACzB,kBAAmD,KAAK,EACxD,OAAO,GAAG,IAAI;QAEd,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,OAAO,CAAC;aACnE,IAAI,CAAC,UAAU,YAAY;YACxB,IAAI,OAAO,EAAE;gBACT,OAAO,CAAC,YAAY,CAAC,CAAC;aACzB;QACL,CAAC,CAAC;aACD,KAAK,CAAC,UAAU,GAAG;YAChB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;IACX,CAAC;CACJ;AAreW;IADP,SAAS,CAAC,UAAU,CAAC;+CACkB;AAIhC;IADP,SAAS,CAAC,KAAK,CAAC;iDAC0D;AAQ3D;IADf,SAAS,EAAE;6CACmB;AA2dnC,OAAO,CAAC,mBAAmB,GAAG,CAC1B,IAAsB,EACtB,GAAyC,EACzC,KAAsB,EACtB,eAAe,GAAG,KAAK,EACvB,OAAO,GAAG,KAAK,EACf,eAAuB,OAAO,CAAC,sBAAsB,EACrD,WAA0C,EAAE,EAC5C,OAA+D,EAC/D,SAAiB,SAAS,CAAC,kBAAkB,EAC/C,EAAE;IACA,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AACjH,CAAC,CAAC;AACF,6CAA6C;AAC7C,aAAa,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC", "sourcesContent": ["import { Observable } from \"../../Misc/observable\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Texture } from \"../../Materials/Textures/texture\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport type { ExternalTexture } from \"./externalTexture\";\r\n\r\nimport \"../../Engines/Extensions/engine.videoTexture\";\r\nimport \"../../Engines/Extensions/engine.dynamicTexture\";\r\nimport { serialize } from \"core/Misc/decorators\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\n\r\nfunction removeSource(video: HTMLVideoElement): void {\r\n    // Remove any <source> elements, etc.\r\n    while (video.firstChild) {\r\n        video.removeChild(video.firstChild);\r\n    }\r\n\r\n    // detach srcObject\r\n    video.srcObject = null;\r\n\r\n    // Set a blank src (https://html.spec.whatwg.org/multipage/media.html#best-practices-for-authors-using-media-elements)\r\n    video.src = \"\";\r\n\r\n    // Prevent non-important errors maybe (https://twitter.com/beraliv/status/1205214277956775936)\r\n    video.removeAttribute(\"src\");\r\n}\r\n\r\n/**\r\n * Settings for finer control over video usage\r\n */\r\nexport interface VideoTextureSettings {\r\n    /**\r\n     * Applies `autoplay` to video, if specified\r\n     */\r\n    autoPlay?: boolean;\r\n\r\n    /**\r\n     * Applies `muted` to video, if specified\r\n     */\r\n    muted?: boolean;\r\n\r\n    /**\r\n     * Applies `loop` to video, if specified\r\n     */\r\n    loop?: boolean;\r\n\r\n    /**\r\n     * Automatically updates internal texture from video at every frame in the render loop\r\n     */\r\n    autoUpdateTexture: boolean;\r\n\r\n    /**\r\n     * Image src displayed during the video loading or until the user interacts with the video.\r\n     */\r\n    poster?: string;\r\n\r\n    /**\r\n     * Defines the associated texture format.\r\n     */\r\n    format?: number;\r\n\r\n    /**\r\n     * Notify babylon to not modify any video settings and not control the video's playback.\r\n     * Set this to true if you are controlling the way the video is being played, stopped and paused.\r\n     */\r\n    independentVideoSource?: boolean;\r\n}\r\n\r\n/**\r\n * If you want to display a video in your scene, this is the special texture for that.\r\n * This special texture works similar to other textures, with the exception of a few parameters.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/videoTexture\r\n */\r\nexport class VideoTexture extends Texture {\r\n    /**\r\n     * Tells whether textures will be updated automatically or user is required to call `updateTexture` manually\r\n     */\r\n    public readonly autoUpdateTexture: boolean;\r\n\r\n    /**\r\n     * The video instance used by the texture internally\r\n     */\r\n    public readonly video: HTMLVideoElement;\r\n\r\n    private _externalTexture: Nullable<ExternalTexture> = null;\r\n    private _onUserActionRequestedObservable: Nullable<Observable<Texture>> = null;\r\n\r\n    /**\r\n     * Event triggered when a dom action is required by the user to play the video.\r\n     * This happens due to recent changes in browser policies preventing video to auto start.\r\n     */\r\n    public get onUserActionRequestedObservable(): Observable<Texture> {\r\n        if (!this._onUserActionRequestedObservable) {\r\n            this._onUserActionRequestedObservable = new Observable<Texture>();\r\n        }\r\n        return this._onUserActionRequestedObservable;\r\n    }\r\n\r\n    private _generateMipMaps: boolean;\r\n    private _stillImageCaptured = false;\r\n    private _displayingPosterTexture = false;\r\n    @serialize(\"settings\")\r\n    private _settings: VideoTextureSettings;\r\n    private _createInternalTextureOnEvent: string;\r\n    private _frameId = -1;\r\n    @serialize(\"src\")\r\n    private _currentSrc: Nullable<string | string[] | HTMLVideoElement> = null;\r\n    private _onError?: Nullable<(message?: string, exception?: any) => void>;\r\n    private _errorFound = false;\r\n\r\n    /**\r\n     * Serialize the flag to define this texture as a video texture\r\n     */\r\n    @serialize()\r\n    public readonly isVideo = true;\r\n\r\n    private _processError(reason: any) {\r\n        this._errorFound = true;\r\n        if (this._onError) {\r\n            this._onError(reason?.message);\r\n        } else {\r\n            Logger.Error(reason?.message);\r\n        }\r\n    }\r\n\r\n    private _handlePlay() {\r\n        this._errorFound = false;\r\n        this.video.play().catch((reason) => {\r\n            if (reason?.name === \"NotAllowedError\") {\r\n                if (this._onUserActionRequestedObservable && this._onUserActionRequestedObservable.hasObservers()) {\r\n                    this._onUserActionRequestedObservable.notifyObservers(this);\r\n                    return;\r\n                } else if (!this.video.muted) {\r\n                    Logger.Warn(\"Unable to autoplay a video with sound. Trying again with muted turned true\");\r\n                    this.video.muted = true;\r\n                    this._errorFound = false;\r\n                    this.video.play().catch((otherReason) => {\r\n                        this._processError(otherReason);\r\n                    });\r\n                    return;\r\n                }\r\n            }\r\n\r\n            this._processError(reason);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a video texture.\r\n     * If you want to display a video in your scene, this is the special texture for that.\r\n     * This special texture works similar to other textures, with the exception of a few parameters.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/videoTexture\r\n     * @param name optional name, will detect from video source, if not defined\r\n     * @param src can be used to provide an url, array of urls or an already setup HTML video element.\r\n     * @param scene is obviously the current scene.\r\n     * @param generateMipMaps can be used to turn on mipmaps (Can be expensive for videoTextures because they are often updated).\r\n     * @param invertY is false by default but can be used to invert video on Y axis\r\n     * @param samplingMode controls the sampling method and is set to TRILINEAR_SAMPLINGMODE by default\r\n     * @param settings allows finer control over video usage\r\n     * @param onError defines a callback triggered when an error occurred during the loading session\r\n     * @param format defines the texture format to use (Engine.TEXTUREFORMAT_RGBA by default)\r\n     */\r\n    constructor(\r\n        name: Nullable<string>,\r\n        src: string | string[] | HTMLVideoElement,\r\n        scene: Nullable<Scene>,\r\n        generateMipMaps = false,\r\n        invertY = false,\r\n        samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE,\r\n        settings: Partial<VideoTextureSettings> = {},\r\n        onError?: Nullable<(message?: string, exception?: any) => void>,\r\n        format: number = Constants.TEXTUREFORMAT_RGBA\r\n    ) {\r\n        super(null, scene, !generateMipMaps, invertY);\r\n\r\n        this._settings = {\r\n            autoPlay: true,\r\n            loop: true,\r\n            autoUpdateTexture: true,\r\n            ...settings,\r\n        };\r\n\r\n        this._onError = onError;\r\n\r\n        this._generateMipMaps = generateMipMaps;\r\n        this._initialSamplingMode = samplingMode;\r\n        this.autoUpdateTexture = this._settings.autoUpdateTexture;\r\n\r\n        this._currentSrc = src;\r\n        this.name = name || this._getName(src);\r\n        this.video = this._getVideo(src);\r\n        if (this._engine?.createExternalTexture) {\r\n            this._externalTexture = this._engine.createExternalTexture(this.video);\r\n        }\r\n\r\n        if (!this._settings.independentVideoSource) {\r\n            if (this._settings.poster) {\r\n                this.video.poster = this._settings.poster;\r\n            }\r\n            if (this._settings.autoPlay !== undefined) {\r\n                this.video.autoplay = this._settings.autoPlay;\r\n            }\r\n            if (this._settings.loop !== undefined) {\r\n                this.video.loop = this._settings.loop;\r\n            }\r\n            if (this._settings.muted !== undefined) {\r\n                this.video.muted = this._settings.muted;\r\n            }\r\n\r\n            this.video.setAttribute(\"playsinline\", \"\");\r\n            this.video.addEventListener(\"paused\", this._updateInternalTexture);\r\n            this.video.addEventListener(\"seeked\", this._updateInternalTexture);\r\n            this.video.addEventListener(\"loadeddata\", this._updateInternalTexture);\r\n            this.video.addEventListener(\"emptied\", this._reset);\r\n\r\n            if (this._settings.autoPlay) {\r\n                this._handlePlay();\r\n            }\r\n        }\r\n\r\n        this._createInternalTextureOnEvent = this._settings.poster && !this._settings.autoPlay ? \"play\" : \"canplay\";\r\n        this.video.addEventListener(this._createInternalTextureOnEvent, this._createInternalTexture);\r\n        this._format = format;\r\n\r\n        const videoHasEnoughData = this.video.readyState >= this.video.HAVE_CURRENT_DATA;\r\n        if (this._settings.poster && (!this._settings.autoPlay || !videoHasEnoughData)) {\r\n            this._texture = this._getEngine()!.createTexture(this._settings.poster!, false, !this.invertY, scene);\r\n            this._displayingPosterTexture = true;\r\n        } else if (videoHasEnoughData) {\r\n            this._createInternalTexture();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the current class name of the video texture useful for serialization or dynamic coding.\r\n     * @returns \"VideoTexture\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"VideoTexture\";\r\n    }\r\n\r\n    private _getName(src: string | string[] | HTMLVideoElement): string {\r\n        if (src instanceof HTMLVideoElement) {\r\n            return src.currentSrc;\r\n        }\r\n\r\n        if (typeof src === \"object\") {\r\n            return src.toString();\r\n        }\r\n\r\n        return src;\r\n    }\r\n\r\n    private _getVideo(src: string | string[] | HTMLVideoElement): HTMLVideoElement {\r\n        if ((<any>src).isNative) {\r\n            return <HTMLVideoElement>src;\r\n        }\r\n        if (src instanceof HTMLVideoElement) {\r\n            Tools.SetCorsBehavior(src.currentSrc, src);\r\n            return src;\r\n        }\r\n        const video: HTMLVideoElement = document.createElement(\"video\");\r\n        if (typeof src === \"string\") {\r\n            Tools.SetCorsBehavior(src, video);\r\n            video.src = src;\r\n        } else {\r\n            Tools.SetCorsBehavior(src[0], video);\r\n            src.forEach((url) => {\r\n                const source = document.createElement(\"source\");\r\n                source.src = url;\r\n                video.appendChild(source);\r\n            });\r\n        }\r\n\r\n        this.onDisposeObservable.addOnce(() => {\r\n            removeSource(video);\r\n        });\r\n\r\n        return video;\r\n    }\r\n\r\n    private _resizeInternalTexture = (): void => {\r\n        // Cleanup the old texture before replacing it\r\n        if (this._texture != null) {\r\n            this._texture.dispose();\r\n        }\r\n\r\n        if (!this._getEngine()!.needPOTTextures || (Tools.IsExponentOfTwo(this.video.videoWidth) && Tools.IsExponentOfTwo(this.video.videoHeight))) {\r\n            this.wrapU = Texture.WRAP_ADDRESSMODE;\r\n            this.wrapV = Texture.WRAP_ADDRESSMODE;\r\n        } else {\r\n            this.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n            this.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n            this._generateMipMaps = false;\r\n        }\r\n\r\n        this._texture = this._getEngine()!.createDynamicTexture(this.video.videoWidth, this.video.videoHeight, this._generateMipMaps, this.samplingMode);\r\n        this._texture.format = this._format ?? Constants.TEXTUREFORMAT_RGBA;\r\n\r\n        // Reset the frame ID and update the new texture to ensure it pulls in the current video frame\r\n        this._frameId = -1;\r\n        this._updateInternalTexture();\r\n    };\r\n\r\n    private _createInternalTexture = (): void => {\r\n        if (this._texture != null) {\r\n            if (this._displayingPosterTexture) {\r\n                this._displayingPosterTexture = false;\r\n            } else {\r\n                return;\r\n            }\r\n        }\r\n\r\n        this.video.addEventListener(\"resize\", this._resizeInternalTexture);\r\n        this._resizeInternalTexture();\r\n\r\n        if (!this.video.autoplay && !this._settings.poster && !this._settings.independentVideoSource) {\r\n            const oldHandler = this.video.onplaying;\r\n            const oldMuted = this.video.muted;\r\n            this.video.muted = true;\r\n            this.video.onplaying = () => {\r\n                this.video.muted = oldMuted;\r\n                this.video.onplaying = oldHandler;\r\n                this._updateInternalTexture();\r\n                if (!this._errorFound) {\r\n                    this.video.pause();\r\n                }\r\n                if (this.onLoadObservable.hasObservers()) {\r\n                    this.onLoadObservable.notifyObservers(this);\r\n                }\r\n            };\r\n            this._handlePlay();\r\n        } else {\r\n            this._updateInternalTexture();\r\n            if (this.onLoadObservable.hasObservers()) {\r\n                this.onLoadObservable.notifyObservers(this);\r\n            }\r\n        }\r\n    };\r\n\r\n    private _reset = (): void => {\r\n        if (this._texture == null) {\r\n            return;\r\n        }\r\n\r\n        if (!this._displayingPosterTexture) {\r\n            this._texture.dispose();\r\n            this._texture = null;\r\n        }\r\n    };\r\n\r\n    /**\r\n     * @internal Internal method to initiate `update`.\r\n     */\r\n    public _rebuild(): void {\r\n        this.update();\r\n    }\r\n\r\n    /**\r\n     * Update Texture in the `auto` mode. Does not do anything if `settings.autoUpdateTexture` is false.\r\n     */\r\n    public update(): void {\r\n        if (!this.autoUpdateTexture) {\r\n            // Expecting user to call `updateTexture` manually\r\n            return;\r\n        }\r\n\r\n        this.updateTexture(true);\r\n    }\r\n\r\n    /**\r\n     * Update Texture in `manual` mode. Does not do anything if not visible or paused.\r\n     * @param isVisible Visibility state, detected by user using `scene.getActiveMeshes()` or otherwise.\r\n     */\r\n    public updateTexture(isVisible: boolean): void {\r\n        if (!isVisible) {\r\n            return;\r\n        }\r\n        if (this.video.paused && this._stillImageCaptured) {\r\n            return;\r\n        }\r\n        this._stillImageCaptured = true;\r\n        this._updateInternalTexture();\r\n    }\r\n\r\n    protected _updateInternalTexture = (): void => {\r\n        if (this._texture == null) {\r\n            return;\r\n        }\r\n        if (this.video.readyState < this.video.HAVE_CURRENT_DATA) {\r\n            return;\r\n        }\r\n        if (this._displayingPosterTexture) {\r\n            return;\r\n        }\r\n\r\n        const frameId = this.getScene()!.getFrameId();\r\n        if (this._frameId === frameId) {\r\n            return;\r\n        }\r\n\r\n        this._frameId = frameId;\r\n\r\n        this._getEngine()!.updateVideoTexture(this._texture, this._externalTexture ? this._externalTexture : this.video, this._invertY);\r\n    };\r\n\r\n    /**\r\n     * Get the underlying external texture (if supported by the current engine, else null)\r\n     */\r\n    public get externalTexture(): Nullable<ExternalTexture> {\r\n        return this._externalTexture;\r\n    }\r\n\r\n    /**\r\n     * Change video content. Changing video instance or setting multiple urls (as in constructor) is not supported.\r\n     * @param url New url.\r\n     */\r\n    public updateURL(url: string): void {\r\n        this.video.src = url;\r\n        this._currentSrc = url;\r\n    }\r\n\r\n    /**\r\n     * Clones the texture.\r\n     * @returns the cloned texture\r\n     */\r\n    public clone(): VideoTexture {\r\n        return new VideoTexture(this.name, this._currentSrc!, this.getScene(), this._generateMipMaps, this.invertY, this.samplingMode, this._settings);\r\n    }\r\n\r\n    /**\r\n     * Dispose the texture and release its associated resources.\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n\r\n        this._currentSrc = null;\r\n\r\n        if (this._onUserActionRequestedObservable) {\r\n            this._onUserActionRequestedObservable.clear();\r\n            this._onUserActionRequestedObservable = null;\r\n        }\r\n\r\n        this.video.removeEventListener(this._createInternalTextureOnEvent, this._createInternalTexture);\r\n        if (!this._settings.independentVideoSource) {\r\n            this.video.removeEventListener(\"paused\", this._updateInternalTexture);\r\n            this.video.removeEventListener(\"seeked\", this._updateInternalTexture);\r\n            this.video.removeEventListener(\"loadeddata\", this._updateInternalTexture);\r\n            this.video.removeEventListener(\"emptied\", this._reset);\r\n            this.video.removeEventListener(\"resize\", this._resizeInternalTexture);\r\n            this.video.pause();\r\n        }\r\n\r\n        this._externalTexture?.dispose();\r\n    }\r\n\r\n    /**\r\n     * Creates a video texture straight from a stream.\r\n     * @param scene Define the scene the texture should be created in\r\n     * @param stream Define the stream the texture should be created from\r\n     * @param constraints video constraints\r\n     * @param invertY Defines if the video should be stored with invert Y set to true (true by default)\r\n     * @returns The created video texture as a promise\r\n     */\r\n    public static CreateFromStreamAsync(scene: Scene, stream: MediaStream, constraints: any, invertY = true): Promise<VideoTexture> {\r\n        const video = scene.getEngine().createVideoElement(constraints);\r\n\r\n        if (scene.getEngine()._badOS) {\r\n            // Yes... I know and I hope to remove it soon...\r\n            document.body.appendChild(video);\r\n            video.style.transform = \"scale(0.0001, 0.0001)\";\r\n            video.style.opacity = \"0\";\r\n            video.style.position = \"fixed\";\r\n            video.style.bottom = \"0px\";\r\n            video.style.right = \"0px\";\r\n        }\r\n\r\n        video.setAttribute(\"autoplay\", \"\");\r\n        video.setAttribute(\"muted\", \"true\");\r\n        video.setAttribute(\"playsinline\", \"\");\r\n        video.muted = true;\r\n\r\n        if (video.isNative) {\r\n            // No additional configuration needed for native\r\n        } else if (video.mozSrcObject !== undefined) {\r\n            // hack for Firefox < 19\r\n            video.mozSrcObject = stream;\r\n        } else {\r\n            if (typeof video.srcObject == \"object\") {\r\n                video.srcObject = stream;\r\n            } else {\r\n                // older API. See https://developer.mozilla.org/en-US/docs/Web/API/URL/createObjectURL#using_object_urls_for_media_streams\r\n                video.src = window.URL && window.URL.createObjectURL(stream as any);\r\n            }\r\n        }\r\n\r\n        return new Promise<VideoTexture>((resolve) => {\r\n            const onPlaying = () => {\r\n                const videoTexture = new VideoTexture(\"video\", video, scene, true, invertY, undefined, undefined, undefined, Constants.TEXTUREFORMAT_RGB);\r\n                if (scene.getEngine()._badOS) {\r\n                    videoTexture.onDisposeObservable.addOnce(() => {\r\n                        video.remove();\r\n                    });\r\n                }\r\n                videoTexture.onDisposeObservable.addOnce(() => {\r\n                    removeSource(video);\r\n                });\r\n\r\n                resolve(videoTexture);\r\n                video.removeEventListener(\"playing\", onPlaying);\r\n            };\r\n\r\n            video.addEventListener(\"playing\", onPlaying);\r\n            video.play();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a video texture straight from your WebCam video feed.\r\n     * @param scene Define the scene the texture should be created in\r\n     * @param constraints Define the constraints to use to create the web cam feed from WebRTC\r\n     * @param audioConstaints Define the audio constraints to use to create the web cam feed from WebRTC\r\n     * @param invertY Defines if the video should be stored with invert Y set to true (true by default)\r\n     * @returns The created video texture as a promise\r\n     */\r\n    public static async CreateFromWebCamAsync(\r\n        scene: Scene,\r\n        constraints: {\r\n            minWidth: number;\r\n            maxWidth: number;\r\n            minHeight: number;\r\n            maxHeight: number;\r\n            deviceId: string;\r\n        } & MediaTrackConstraints,\r\n        audioConstaints: boolean | MediaTrackConstraints = false,\r\n        invertY = true\r\n    ): Promise<VideoTexture> {\r\n        if (navigator.mediaDevices) {\r\n            const stream = await navigator.mediaDevices.getUserMedia({\r\n                video: constraints,\r\n                audio: audioConstaints,\r\n            });\r\n\r\n            const videoTexture = await this.CreateFromStreamAsync(scene, stream, constraints, invertY);\r\n            videoTexture.onDisposeObservable.addOnce(() => {\r\n                stream.getTracks().forEach((track) => {\r\n                    track.stop();\r\n                });\r\n            });\r\n\r\n            return videoTexture;\r\n        }\r\n\r\n        return Promise.reject(\"No support for userMedia on this device\");\r\n    }\r\n\r\n    /**\r\n     * Creates a video texture straight from your WebCam video feed.\r\n     * @param scene Defines the scene the texture should be created in\r\n     * @param onReady Defines a callback to triggered once the texture will be ready\r\n     * @param constraints Defines the constraints to use to create the web cam feed from WebRTC\r\n     * @param audioConstaints Defines the audio constraints to use to create the web cam feed from WebRTC\r\n     * @param invertY Defines if the video should be stored with invert Y set to true (true by default)\r\n     */\r\n    public static CreateFromWebCam(\r\n        scene: Scene,\r\n        onReady: (videoTexture: VideoTexture) => void,\r\n        constraints: {\r\n            minWidth: number;\r\n            maxWidth: number;\r\n            minHeight: number;\r\n            maxHeight: number;\r\n            deviceId: string;\r\n        } & MediaTrackConstraints,\r\n        audioConstaints: boolean | MediaTrackConstraints = false,\r\n        invertY = true\r\n    ): void {\r\n        this.CreateFromWebCamAsync(scene, constraints, audioConstaints, invertY)\r\n            .then(function (videoTexture) {\r\n                if (onReady) {\r\n                    onReady(videoTexture);\r\n                }\r\n            })\r\n            .catch(function (err) {\r\n                Logger.Error(err.name);\r\n            });\r\n    }\r\n}\r\n\r\nTexture._CreateVideoTexture = (\r\n    name: Nullable<string>,\r\n    src: string | string[] | HTMLVideoElement,\r\n    scene: Nullable<Scene>,\r\n    generateMipMaps = false,\r\n    invertY = false,\r\n    samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE,\r\n    settings: Partial<VideoTextureSettings> = {},\r\n    onError?: Nullable<(message?: string, exception?: any) => void>,\r\n    format: number = Constants.TEXTUREFORMAT_RGBA\r\n) => {\r\n    return new VideoTexture(name, src, scene, generateMipMaps, invertY, samplingMode, settings, onError, format);\r\n};\r\n// Some exporters relies on Tools.Instantiate\r\nRegisterClass(\"BABYLON.VideoTexture\", VideoTexture);\r\n"]}