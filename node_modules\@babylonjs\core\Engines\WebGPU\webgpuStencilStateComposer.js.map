{"version": 3, "file": "webgpuStencilStateComposer.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuStencilStateComposer.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAEzE;;IAEI;AACJ,MAAM,OAAO,0BAA2B,SAAQ,oBAAoB;IAGhE,YAAmB,KAAgC;QAC/C,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAa;QACzB,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;YACtB,OAAO;SACV;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAED,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa;QAC7B,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;YAC1B,OAAO;SACV;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,KAAa;QAClC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE;YAC7B,OAAO;SACV;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAa;QACvC,IAAI,IAAI,CAAC,mBAAmB,KAAK,KAAK,EAAE;YACpC,OAAO;SACV;QAED,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAa;QACzB,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;YACtB,OAAO;SACV;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,KAAc;QAC7B,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;YACzB,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAEM,KAAK;QACR,KAAK,CAAC,KAAK,EAAE,CAAC;QACd,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;IACpC,CAAC;IAEM,KAAK;QACR,MAAM,sBAAsB,GAAG,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC;QAE7D,IAAI,CAAC,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QACnG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QAED,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QAC1F,IAAI,CAAC,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QACnG,IAAI,CAAC,QAAQ,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;QACtG,IAAI,CAAC,aAAa,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;QACrH,IAAI,CAAC,WAAW,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;QAC/G,IAAI,CAAC,kBAAkB,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;QACpI,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;IAC9F,CAAC;CACJ", "sourcesContent": ["import type { WebGPUCacheRenderPipeline } from \"./webgpuCacheRenderPipeline\";\r\nimport { StencilStateComposer } from \"../../States/stencilStateComposer\";\r\n\r\n/**\r\n * @internal\r\n **/\r\nexport class WebGPUStencilStateComposer extends StencilStateComposer {\r\n    private _cache: WebGPUCacheRenderPipeline;\r\n\r\n    public constructor(cache: WebGPUCacheRenderPipeline) {\r\n        super(false);\r\n        this._cache = cache;\r\n        this.reset();\r\n    }\r\n\r\n    public get func(): number {\r\n        return this._func;\r\n    }\r\n\r\n    public set func(value: number) {\r\n        if (this._func === value) {\r\n            return;\r\n        }\r\n\r\n        this._func = value;\r\n        this._cache.setStencilCompare(value);\r\n    }\r\n\r\n    public get funcMask(): number {\r\n        return this._funcMask;\r\n    }\r\n\r\n    public set funcMask(value: number) {\r\n        if (this._funcMask === value) {\r\n            return;\r\n        }\r\n\r\n        this._funcMask = value;\r\n        this._cache.setStencilReadMask(value);\r\n    }\r\n\r\n    public get opStencilFail(): number {\r\n        return this._opStencilFail;\r\n    }\r\n\r\n    public set opStencilFail(value: number) {\r\n        if (this._opStencilFail === value) {\r\n            return;\r\n        }\r\n\r\n        this._opStencilFail = value;\r\n        this._cache.setStencilFailOp(value);\r\n    }\r\n\r\n    public get opDepthFail(): number {\r\n        return this._opDepthFail;\r\n    }\r\n\r\n    public set opDepthFail(value: number) {\r\n        if (this._opDepthFail === value) {\r\n            return;\r\n        }\r\n\r\n        this._opDepthFail = value;\r\n        this._cache.setStencilDepthFailOp(value);\r\n    }\r\n\r\n    public get opStencilDepthPass(): number {\r\n        return this._opStencilDepthPass;\r\n    }\r\n\r\n    public set opStencilDepthPass(value: number) {\r\n        if (this._opStencilDepthPass === value) {\r\n            return;\r\n        }\r\n\r\n        this._opStencilDepthPass = value;\r\n        this._cache.setStencilPassOp(value);\r\n    }\r\n\r\n    public get mask(): number {\r\n        return this._mask;\r\n    }\r\n\r\n    public set mask(value: number) {\r\n        if (this._mask === value) {\r\n            return;\r\n        }\r\n\r\n        this._mask = value;\r\n        this._cache.setStencilWriteMask(value);\r\n    }\r\n\r\n    public get enabled(): boolean {\r\n        return this._enabled;\r\n    }\r\n\r\n    public set enabled(value: boolean) {\r\n        if (this._enabled === value) {\r\n            return;\r\n        }\r\n\r\n        this._enabled = value;\r\n        this._cache.setStencilEnabled(value);\r\n    }\r\n\r\n    public reset() {\r\n        super.reset();\r\n        this._cache.resetStencilState();\r\n    }\r\n\r\n    public apply() {\r\n        const stencilMaterialEnabled = this.stencilMaterial?.enabled;\r\n\r\n        this.enabled = stencilMaterialEnabled ? this.stencilMaterial!.enabled : this.stencilGlobal.enabled;\r\n        if (!this.enabled) {\r\n            return;\r\n        }\r\n\r\n        this.func = stencilMaterialEnabled ? this.stencilMaterial!.func : this.stencilGlobal.func;\r\n        this.funcRef = stencilMaterialEnabled ? this.stencilMaterial!.funcRef : this.stencilGlobal.funcRef;\r\n        this.funcMask = stencilMaterialEnabled ? this.stencilMaterial!.funcMask : this.stencilGlobal.funcMask;\r\n        this.opStencilFail = stencilMaterialEnabled ? this.stencilMaterial!.opStencilFail : this.stencilGlobal.opStencilFail;\r\n        this.opDepthFail = stencilMaterialEnabled ? this.stencilMaterial!.opDepthFail : this.stencilGlobal.opDepthFail;\r\n        this.opStencilDepthPass = stencilMaterialEnabled ? this.stencilMaterial!.opStencilDepthPass : this.stencilGlobal.opStencilDepthPass;\r\n        this.mask = stencilMaterialEnabled ? this.stencilMaterial!.mask : this.stencilGlobal.mask;\r\n    }\r\n}\r\n"]}