{"version": 3, "file": "sound.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Audio/sound.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAG/C,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAE/C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAIrD;;;;GAIG;AACH,MAAM,OAAO,KAAK;IAWd;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAc;QAC1B,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;YACtB,OAAO;SACV;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;IACxC,CAAC;IAuDD;;OAEG;IACH,IAAW,WAAW;QAClB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;SAC7C;QAED,IAAI,MAAM,CAAC,WAAW,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;YACvE,wGAAwG;YACxG,kCAAkC;YAClC,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;YAC7G,OAAO,IAAI,CAAC,YAAY,GAAG,kBAAkB,CAAC;SACjD;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;OAGG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACH,IAAW,YAAY,CAAC,QAAiB;QACrC,IAAI,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE;YAChC,OAAO;SACV;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;YAC9B,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACnC;aAAM;YACH,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC/B;QAED,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,IAAI,EAAE,CAAC;SACf;IACL,CAAC;IA6CD;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,gBAAqB,EAAE,KAAuB,EAAE,sBAA4C,IAAI,EAAE,OAAuB;QAhLnJ;;WAEG;QACI,aAAQ,GAAY,KAAK,CAAC;QAEzB,UAAK,GAAG,KAAK,CAAC;QAiBtB;;;;WAIG;QACI,yBAAoB,GAAY,KAAK,CAAC;QAK7C;;WAEG;QACI,cAAS,GAAY,KAAK,CAAC;QAClC;;WAEG;QACI,aAAQ,GAAY,KAAK,CAAC;QACjC;;;WAGG;QACI,gBAAW,GAAW,CAAC,CAAC;QAC/B;;;WAGG;QACI,kBAAa,GAAW,CAAC,CAAC;QACjC;;;WAGG;QACI,gBAAW,GAAW,GAAG,CAAC;QACjC;;;WAGG;QACI,kBAAa,GAAW,QAAQ,CAAC;QAMxC;;WAEG;QACI,aAAQ,GAAQ,IAAI,CAAC;QAE5B;;WAEG;QACI,sBAAiB,GAAG,IAAI,UAAU,EAAS,CAAC;QAoD3C,kBAAa,GAAY,KAAK,CAAC;QAC/B,kBAAa,GAAW,YAAY,CAAC;QACrC,kBAAa,GAAW,CAAC,CAAC;QAC1B,eAAU,GAAY,KAAK,CAAC;QAC5B,eAAU,GAAW,CAAC,CAAC;QACvB,iBAAY,GAAW,CAAC,CAAC;QACzB,cAAS,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QACpC,oBAAe,GAAY,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAChD,YAAO,GAAW,CAAC,CAAC;QACpB,mBAAc,GAAY,KAAK,CAAC;QAChC,mBAAc,GAAY,KAAK,CAAC;QASxC,oDAAoD;QACpD,gDAAgD;QACxC,oBAAe,GAAW,GAAG,CAAC;QAC9B,oBAAe,GAAW,GAAG,CAAC;QAC9B,mBAAc,GAAW,CAAC,CAAC;QAK3B,uBAAkB,GAAG,KAAK,CAAC;QAE3B,aAAQ,GAAoG,SAAS,CAAC;QAsB1H,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,KAAK,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,KAAK,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;QAE3C,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QAChD,8DAA8D;QAC9D,6DAA6D;QAC7D,IAAI,CAAC,0BAA0B,GAAG,CAAC,aAAqB,EAAE,eAAuB,EAAE,WAAmB,EAAE,WAAmB,EAAE,aAAqB,EAAE,EAAE;YAClJ,IAAI,eAAe,GAAG,WAAW,EAAE;gBAC/B,OAAO,aAAa,GAAG,CAAC,CAAC,GAAG,eAAe,GAAG,WAAW,CAAC,CAAC;aAC9D;iBAAM;gBACH,OAAO,CAAC,CAAC;aACZ;QACL,CAAC,CAAC;QACF,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC;YAC1C,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,IAAI,KAAK,CAAC;YACnC,4DAA4D;YAC5D,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;gBAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;aACjC;YACD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,KAAK,CAAC;YACnD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,GAAG,CAAC;YAC9C,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,IAAI,KAAK,CAAC;YAClE,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC;YAChD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC;YAC5C,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC;YACvD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;YAC/C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC;YAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;YAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;SACjC;QAED,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,IAAI,MAAM,CAAC,WAAW,CAAC,YAAY,EAAE;YACvE,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;YAC/D,IAAI,CAAC,UAAW,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;YAC3C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC;YACvC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC;YACxC,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,wBAAwB,EAAE,CAAC;aACnC;YACD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,cAAc,GAAG,IAAI,CAAC;YAE1B,2FAA2F;YAC3F,IAAI,gBAAgB,EAAE;gBAClB,IAAI;oBACA,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE;wBACtC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;wBACzB,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;qBAChC;yBAAM,IAAI,gBAAgB,YAAY,WAAW,EAAE;wBAChD,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;qBACjC;yBAAM,IAAI,gBAAgB,YAAY,gBAAgB,EAAE;wBACrD,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;qBAClC;yBAAM,IAAI,gBAAgB,YAAY,WAAW,EAAE;wBAChD,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;qBACjC;yBAAM,IAAI,gBAAgB,YAAY,WAAW,EAAE;wBAChD,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;qBACjC;yBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;wBACxC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;qBAC3B;oBAED,IAAI,IAAI,GAAa,EAAE,CAAC;oBACxB,IAAI,mBAAmB,GAAG,KAAK,CAAC;oBAEhC,QAAQ,IAAI,CAAC,QAAQ,EAAE;wBACnB,KAAK,cAAc;4BACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;4BACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;4BAC3B,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;4BAEnG,IAAI,IAAI,CAAC,QAAQ,EAAE;gCACf,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;6BAC5C;4BAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;gCAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;6BAC/B;4BACD,MAAM;wBACV,KAAK,aAAa;4BACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;4BACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;4BAC3B,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;4BAElG,IAAI,IAAI,CAAC,QAAQ,EAAE;gCACf,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;6BAC5C;4BAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;gCAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;6BAC/B;4BACD,MAAM;wBACV,KAAK,aAAa;4BACd,IAAkB,gBAAiB,CAAC,UAAU,GAAG,CAAC,EAAE;gCAChD,mBAAmB,GAAG,IAAI,CAAC;gCAC3B,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;6BACvC;4BACD,MAAM;wBACV,KAAK,aAAa;4BACd,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;4BAC1C,MAAM;wBACV,KAAK,QAAQ;4BACT,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBAChC,0CAA0C;wBAC1C,KAAK,OAAO;4BACR,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gCACnB,IAAI,GAAG,gBAAgB,CAAC;6BAC3B;4BACD,2EAA2E;4BAC3E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gCAClC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gCACpB,mBAAmB;oCACf,CAAC,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC;wCACnC,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC;wCACjF,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC;wCACjF,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;wCAC1C,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;wCAC1C,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;wCAC1C,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gCAChC,IAAI,mBAAmB,EAAE;oCACrB,gBAAgB;oCAChB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;wCAClB,IAAI,CAAC,MAAM,CAAC,SAAS,CACjB,GAAG,EACH,CAAC,IAAI,EAAE,EAAE;4CACL,IAAI,CAAC,YAAY,CAAC,IAAmB,CAAC,CAAC;wCAC3C,CAAC,EACD,SAAS,EACT,IAAI,EACJ,IAAI,EACJ,CAAC,SAAS,EAAE,EAAE;4CACV,IAAI,SAAS,EAAE;gDACX,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,aAAa,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;6CACvE;4CACD,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;4CACxC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;wCACjD,CAAC,CACJ,CAAC;qCACL;oCACD,wCAAwC;yCACnC;wCACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;wCACxC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,GAAG,KAAK,CAAC;wCACxC,IAAI,CAAC,iBAAiB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;wCACxC,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;wCACnD,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,MAAM,CAAC;wCACxC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,GAAG,EAAE;4CAC3D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;4CAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE;gDACf,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;6CAC5C;4CACD,IAAI,IAAI,CAAC,oBAAoB,EAAE;gDAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;6CAC/B;wCACL,CAAC,CAAC,CAAC;wCACH,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;wCAClD,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;qCACjC;oCACD,MAAM;iCACT;6BACJ;4BACD,MAAM;wBACV;4BACI,cAAc,GAAG,KAAK,CAAC;4BACvB,MAAM;qBACb;oBAED,IAAI,CAAC,cAAc,EAAE;wBACjB,MAAM,CAAC,KAAK,CAAC,sGAAsG,CAAC,CAAC;qBACxH;yBAAM;wBACH,IAAI,CAAC,mBAAmB,EAAE;4BACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;4BAC3B,+DAA+D;4BAC/D,IAAI,IAAI,CAAC,oBAAoB,EAAE;gCAC3B,UAAU,CAAC,GAAG,EAAE;oCACZ,IAAI,IAAI,CAAC,oBAAoB,EAAE;wCAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;qCAC/B;gCACL,CAAC,EAAE,IAAI,CAAC,CAAC;6BACZ;yBACJ;qBACJ;iBACJ;gBAAC,OAAO,EAAE,EAAE;oBACT,MAAM,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;oBAC1D,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;iBAChD;aACJ;SACJ;aAAM;YACH,iFAAiF;YACjF,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,yBAAyB,EAAE;gBACrE,MAAM,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;gBAC5D,MAAM,CAAC,WAAW,CAAC,yBAAyB,GAAG,IAAI,CAAC;aACvD;YACD,qFAAqF;YACrF,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,UAAU,CAAC,GAAG,EAAE;oBACZ,IAAI,IAAI,CAAC,oBAAoB,EAAE;wBAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;qBAC/B;gBACL,CAAC,EAAE,IAAI,CAAC,CAAC;aACZ;SACJ;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,EAAE;YACpC,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,IAAI,EAAE,CAAC;aACf;YACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC,EAAE;gBAC1B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;aAChD;iBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;gBAChC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;aAChE;YACD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;aAC1B;YACD,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;aAC5B;YACD,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;aAC5B;YACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAEzB,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;gBAC/B,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,EAAE,CAAC;gBAChC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;aACrD;YAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;aACtC;YAED,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpD,IAAI,CAAC,uBAAuB,CAAC,gCAAgC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAClF,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;aACvC;YAED,IAAI,CAAC,0BAA0B,EAAE,CAAC;SACrC;IACL,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,kBAAkB,CAAC,MAAmB;QAC1C,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,YAAY,EAAE;YACnC,OAAO;SACV;QACD,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAC5C;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC/B;IACL,CAAC;IAEO,YAAY,CAAC,SAAsB;QACvC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,YAAY,EAAE;YACnC,OAAO;SACV;QACD,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,eAAe,CAC3C,SAAS,EACT,CAAC,MAAM,EAAE,EAAE;YACP,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC,EACD,CAAC,GAAQ,EAAE,EAAE;YACT,MAAM,CAAC,KAAK,CAAC,uCAAuC,GAAG,IAAI,CAAC,IAAI,GAAG,YAAY,GAAG,GAAG,CAAC,CAAC;QAC3F,CAAC,CACJ,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,WAAwB;QAC1C,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,EAAE;YACpC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;YAChC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC9B;IACL,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,OAAsB;QACvC,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;YACtC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC;YAC3D,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC;YACtF,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC;YACjE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC;YAC3D,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC;YACjE,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC;YAChE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC;YAC3C,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC;YAC/D,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC;YAC7C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/C,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBAC3C,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;oBACzD,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;wBAC3C,IAAI,CAAC,iBAAiB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;qBAC3C;iBACJ;qBAAM;oBACH,IAAI,IAAI,CAAC,YAAY,EAAE;wBACnB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;wBAC1D,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;4BACtC,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;yBACtC;wBACD,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,IAAI,CAAC,OAAO,EAAE;4BAC5E,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;yBAC9C;wBACD,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;4BAC1E,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAQ,CAAC;yBACnE;qBACJ;iBACJ;aACJ;SACJ;IACL,CAAC;IAEO,wBAAwB;QAC5B,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,IAAI,MAAM,CAAC,WAAW,CAAC,YAAY,EAAE;YACvE,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;gBACvB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;aAC/B;YACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YACxF,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBAC5C,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAChC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACjD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC;aAC5C;SACJ;IACL,CAAC;IAEO,oBAAoB;QACxB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO;SACV;QACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC;QACvC,IAAI,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC/B,CAAC;IAEO,wBAAwB;QAC5B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO;SACV;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,4DAA4D;gBAC5D,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,QAAQ,CAAC;gBAC3C,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC;gBACjD,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC;gBAClC,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,CAAC,CAAC;gBACpC,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,aAAoB,CAAC;aAC9D;iBAAM;gBACH,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,aAAoB,CAAC;gBAC5D,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;gBACjD,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;gBACjD,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;gBACrD,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,aAAoB,CAAC;aAC9D;SACJ;aAAM;YACH,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACnC;IACL,CAAC;IAED;;;;OAIG;IACI,wBAAwB;QAC3B,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACI,8BAA8B;QACjC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAEO,mBAAmB;QACvB,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE;YAC/E,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,aAAoB,CAAC;SAC9D;IACL,CAAC;IAED;;;OAGG;IACI,4BAA4B,CAAC,mBAA8B;QAC9D,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC7D,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;aACtC;YACD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YACnD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAClC;IACL,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,cAAsB,EAAE,cAAsB,EAAE,aAAqB;QAC3F,IAAI,cAAc,GAAG,cAAc,EAAE;YACjC,MAAM,CAAC,KAAK,CAAC,6FAA6F,CAAC,CAAC;YAC5G,OAAO;SACV;QACD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,EAAE;YAC7B,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAC5C;IACL,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB,CAAC,KAAa;QAC9C,IAAI,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE;YAC/B,IAAI,IAAI,CAAC,eAAe,GAAG,KAAK,EAAE;gBAC9B,MAAM,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;gBACjH,OAAO;aACV;YAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE;gBAC/E,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;aAC3D;SACJ;IACL,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB,CAAC,KAAa;QAC9C,IAAI,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE;YAC/B,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE;gBAC9B,MAAM,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;gBACjH,OAAO;aACV;YAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE;gBAC/E,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;aAC3D;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,WAAoB;QACnC,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACpC,OAAO;SACV;QACD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAErC,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;YACnK,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;SACxD;IACL,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,iBAA0B;QACrD,IAAI,CAAC,eAAe,GAAG,iBAAiB,CAAC;QAEzC,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,SAAS,EAAE;YACtF,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;IACL,CAAC;IAEO,gBAAgB;QACpB,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACrD,OAAO;SACV;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC;QAC1D,MAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;QACrE,SAAS,CAAC,SAAS,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,gBAAgB;IACT,0BAA0B;QAC7B,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAChJ,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,6BAA6B;gBACtD,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE,CAAC,CAAC,MAAM,EAAE;gBACtG,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACjF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;SAChJ;IACL,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,QAAqI;QAC/J,IAAI,CAAC,0BAA0B,GAAG,QAAQ,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACI,IAAI,CAAC,IAAa,EAAE,MAAe,EAAE,MAAe;QACvD,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,WAAW,EAAE,YAAY,EAAE;YACrF,IAAI;gBACA,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAElC,IAAI,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,WAAW,CAAC;gBAC1H,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;oBAC9C,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE;wBACzC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;4BAClF,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;4BACrD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;4BACrD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;yBACxD;wBACD,IAAI,IAAI,CAAC,cAAc,EAAE;4BACrB,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;4BACxD,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;4BACxD,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;4BACtD,IAAI,IAAI,CAAC,uBAAuB,EAAE;gCAC9B,IAAI,CAAC,gBAAgB,EAAE,CAAC;6BAC3B;iCAAM;gCACH,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;6BAC5G;yBACJ;qBACJ;iBACJ;gBACD,IAAI,IAAI,CAAC,UAAU,EAAE;oBACjB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;wBACxB,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;wBACzG,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,GAAG,EAAE;4BAClC,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACpB,CAAC,CAAC;wBACF,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;qBAC5D;oBACD,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;oBACnC,IAAI,IAAI,CAAC,eAAe,EAAE;wBACtB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;qBACvD;oBACD,IAAI,IAAI,CAAC,iBAAiB,EAAE;wBACxB,wEAAwE;wBACxE,uEAAuE;wBACvE,0EAA0E;wBAC1E,wBAAwB;wBACxB,MAAM,SAAS,GAAG,GAAG,EAAE;4BACnB,IAAI,MAAM,CAAC,WAAW,EAAE,QAAQ,EAAE;gCAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;gCAElD,yDAAyD;gCACzD,gCAAgC;gCAChC,IAAI,WAAW,KAAK,SAAS,EAAE;oCAC3B,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE;wCACnB,6BAA6B;wCAC7B,sEAAsE;wCACtE,MAAM,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;wCAC3B,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;4CAC5B,IAAI,CAAC,sBAAsB,GAAG,MAAM,CAAC,WAAW,EAAE,yBAAyB,CAAC,OAAO,CAAC,GAAG,EAAE;gDACrF,SAAS,EAAE,CAAC;4CAChB,CAAC,CAAC,CAAC;yCACN;oCACL,CAAC,CAAC,CAAC;iCACN;6BACJ;iCAAM;gCACH,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;oCAC5B,IAAI,CAAC,sBAAsB,GAAG,MAAM,CAAC,WAAW,EAAE,yBAAyB,CAAC,OAAO,CAAC,GAAG,EAAE;wCACrF,SAAS,EAAE,CAAC;oCAChB,CAAC,CAAC,CAAC;iCACN;6BACJ;wBACL,CAAC,CAAC;wBACF,SAAS,EAAE,CAAC;qBACf;iBACJ;qBAAM;oBACH,MAAM,SAAS,GAAG,GAAG,EAAE;wBACnB,IAAI,MAAM,CAAC,WAAW,EAAE,YAAY,EAAE;4BAClC,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;4BAEhC,IAAI,MAAM,KAAK,SAAS,EAAE;gCACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;6BAC3B;4BAED,IAAI,IAAI,CAAC,YAAY,EAAE;gCACnB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC;gCACpC,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE;oCACrB,SAAS,CAAC,UAAU,EAAE,CAAC;gCAC3B,CAAC,CAAC;6BACL;4BACD,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,kBAAkB,EAAE,CAAC;4BAC1E,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,eAAe,EAAE;gCAC3C,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;gCAC7C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gCAChD,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gCACnC,IAAI,MAAM,KAAK,SAAS,EAAE;oCACtB,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,MAAM,CAAC;iCACxC;gCACD,IAAI,MAAM,KAAK,SAAS,EAAE;oCACtB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,MAAO,GAAG,CAAC,CAAC,GAAG,MAAO,CAAC;iCACvD;gCACD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;gCAC1D,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,EAAE;oCAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;gCACpB,CAAC,CAAC;gCACF,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,YAAa,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,YAAa,CAAC,WAAW,CAAC;gCACvH,MAAM,YAAY,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAa,CAAC,MAAO,CAAC,QAAQ,CAAC;gCAC1H,IAAI,CAAC,YAAa,CAAC,KAAK,CAAC,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;6BACrF;yBACJ;oBACL,CAAC,CAAC;oBAEF,IAAI,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,KAAK,KAAK,WAAW,EAAE;wBACxD,uDAAuD;wBACvD,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,GAAG,EAAE;4BACrC,IAAI,MAAM,CAAC,WAAW,EAAE,YAAa,CAAC,KAAK,KAAK,WAAW,EAAE;gCACzD,6BAA6B;gCAC7B,sEAAsE;gCACtE,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gCAC1B,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;oCAC5B,IAAI,CAAC,sBAAsB,GAAG,MAAM,CAAC,WAAW,CAAC,yBAAyB,CAAC,OAAO,CAAC,GAAG,EAAE;wCACpF,SAAS,EAAE,CAAC;oCAChB,CAAC,CAAC,CAAC;iCACN;6BACJ;iCAAM;gCACH,SAAS,EAAE,CAAC;6BACf;wBACL,CAAC,EAAE,GAAG,CAAC,CAAC;qBACX;yBAAM;wBACH,SAAS,EAAE,CAAC;qBACf;iBACJ;gBACD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;aACzB;YAAC,OAAO,EAAE,EAAE;gBACT,MAAM,CAAC,KAAK,CAAC,oCAAoC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC;aACtF;SACJ;IACL,CAAC;IAEO,QAAQ;QACZ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,EAAE,CAAC;SAClB;QACD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG;IACI,IAAI,CAAC,IAAa;QACrB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBACxB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;oBAC/B,qEAAqE;oBACrE,IAAI,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,CAAC,EAAE;wBACxC,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,CAAC,CAAC;qBAC1C;iBACJ;qBAAM;oBACH,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;iBACtC;gBACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;aAC1B;iBAAM,IAAI,MAAM,CAAC,WAAW,EAAE,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE;gBAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;gBACvF,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,EAAE;oBAC7B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;oBACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;oBACpB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;oBACtB,IAAI,IAAI,CAAC,YAAY,EAAE;wBACnB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;qBAC5C;oBACD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACpB,CAAC,CAAC;gBACF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACpC;iBAAM;gBACH,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;aAC1B;SACJ;aAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YACtB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;SACzB;IACL,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBACxB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;iBAClC;qBAAM;oBACH,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;iBACtC;gBACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;aACxB;iBAAM,IAAI,MAAM,CAAC,WAAW,EAAE,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE;gBAC9D,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;gBACzC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;aACtF;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,SAAiB,EAAE,IAAa;QAC7C,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,UAAU,EAAE;YACvD,IAAI,IAAI,IAAI,MAAM,CAAC,WAAW,CAAC,YAAY,EAAE;gBACzC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBACxF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBAC7G,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC;aAC/G;iBAAM;gBACH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;aAC1C;SACJ;QACD,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,eAAuB;QAC1C,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC;QACrC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC3C,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;aAC5D;iBAAM,IAAI,IAAI,CAAC,YAAY,EAAE;gBAC1B,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;aAC7D;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,aAA4B;QAC5C,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpD,IAAI,CAAC,uBAAuB,CAAC,gCAAgC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B;QACD,IAAI,CAAC,uBAAuB,GAAG,aAAa,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,EAAE;gBAC7B,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aAC5C;SACJ;QACD,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACrE,IAAI,CAAC,aAAa,GAAG,CAAC,aAA4B,EAAE,EAAE,CAAC,IAAI,CAAC,iCAAiC,CAAC,aAAa,CAAC,CAAC;QAC7G,IAAI,CAAC,uBAAuB,CAAC,8BAA8B,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACpF,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpD,IAAI,CAAC,uBAAuB,CAAC,gCAAgC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;SACvC;IACL,CAAC;IAEO,iCAAiC,CAAC,IAAmB;QACzD,IAAI,CAAO,IAAK,CAAC,eAAe,EAAE;YAC9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC3C;aAAM;YACH,MAAM,IAAI,GAAG,IAAoB,CAAC;YAClC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5C,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;SAC7D;QACD,IAAI,MAAM,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,SAAS,EAAE;YAC7E,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;IACL,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,MAAM,eAAe,GAAG,GAAG,EAAE;gBACzB,IAAI,IAAI,CAAC,cAAc,EAAE;oBACrB,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;oBACjD,WAAW,CAAC,cAAc,GAAG,IAAI,CAAC;oBAClC,IAAI,WAAW,CAAC,QAAQ,EAAE;wBACtB,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;qBACnD;iBACJ;qBAAM;oBACH,UAAU,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;iBACpC;YACL,CAAC,CAAC;YAEF,MAAM,cAAc,GAAG;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM,EAAE,IAAI,CAAC,OAAO;gBACpB,YAAY,EAAE,IAAI,CAAC,aAAa;gBAChC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;gBAC/C,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;aACpC,CAAC;YAEF,MAAM,WAAW,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;YAC5G,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,WAAW,CAAC,sBAAsB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;aACvE;YACD,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,eAAe,EAAE,CAAC;YAElB,OAAO,WAAW,CAAC;SACtB;QACD,gCAAgC;aAC3B;YACD,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,GAAG,EAAE,IAAI,CAAC,IAAI;YACd,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,OAAO;YACpB,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;SAC1B,CAAC;QAEF,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC;aACzE;YAED,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACxD,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YACnD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;YAEvD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;YACxD,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YAC1E,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;YAC1D,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;YAC1D,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;SAC3D;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,KAAK,CAAC,WAAgB,EAAE,KAAY,EAAE,OAAe,EAAE,WAAmB;QACpF,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC;QACnC,IAAI,QAAQ,CAAC;QAEb,IAAI,WAAW,CAAC,GAAG,EAAE;YACjB,QAAQ,GAAG,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC;SACxC;aAAM;YACH,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;SAClC;QAED,MAAM,OAAO,GAAG;YACZ,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,YAAY,EAAE,WAAW,CAAC,YAAY;YACtC,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,aAAa,EAAE,WAAW,CAAC,aAAa;YACxC,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,aAAa,EAAE,WAAW,CAAC,aAAa;YACxC,YAAY,EAAE,WAAW,CAAC,YAAY;SACzC,CAAC;QAEF,IAAI,QAAe,CAAC;QAEpB,IAAI,CAAC,WAAW,EAAE;YACd,QAAQ,GAAG,IAAI,KAAK,CAChB,SAAS,EACT,QAAQ,EACR,KAAK,EACL,GAAG,EAAE;gBACD,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACtC,CAAC,EACD,OAAO,CACV,CAAC;YACF,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;SAClC;aAAM;YACH,MAAM,eAAe,GAAG,GAAG,EAAE;gBACzB,IAAI,WAAW,CAAC,cAAc,EAAE;oBAC5B,QAAQ,CAAC,YAAY,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;oBACrD,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC;oBAC/B,IAAI,QAAQ,CAAC,QAAQ,EAAE;wBACnB,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;qBACxD;iBACJ;qBAAM;oBACH,UAAU,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;iBACpC;YACL,CAAC,CAAC;YAEF,QAAQ,GAAG,IAAI,KAAK,CAAC,SAAS,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAC1E,eAAe,EAAE,CAAC;SACrB;QAED,IAAI,WAAW,CAAC,QAAQ,EAAE;YACtB,MAAM,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC9D,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;SACvC;QACD,IAAI,WAAW,CAAC,aAAa,EAAE;YAC3B,QAAQ,CAAC,kBAAkB,CAAC,WAAW,CAAC,cAAc,IAAI,GAAG,EAAE,WAAW,CAAC,cAAc,IAAI,GAAG,EAAE,WAAW,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC;YAClI,IAAI,WAAW,CAAC,oBAAoB,EAAE;gBAClC,MAAM,oBAAoB,GAAG,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;gBACjF,QAAQ,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,CAAC;aAC1D;SACJ;QACD,IAAI,WAAW,CAAC,eAAe,EAAE;YAC7B,MAAM,aAAa,GAAG,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;YACrE,IAAI,aAAa,EAAE;gBACf,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;aACxC;SACJ;QAED,IAAI,WAAW,CAAC,QAAQ,EAAE;YACtB,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;SAC5C;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,UAAU,CAAC,KAAc;QAC7B,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;YACxB,OAAO;SACV;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACzB;QACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAEO,0BAA0B;QAC9B,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACjC;QACD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,MAAM,CAAC,WAAW,EAAE,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAClF,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;SACtC;IACL,CAAC;;AAhmCD;;GAEG;AACW,mCAA6B,GAA2B,CAAC,CAAC,EAAE,EAAE;IACxE,MAAM,WAAW,CAAC,qBAAqB,CAAC,CAAC;AAC7C,CAAC,AAF0C,CAEzC", "sourcesContent": ["import { Tools } from \"../Misc/tools\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Engine } from \"../Engines/engine\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { _WarnImport } from \"../Misc/devTools\";\r\nimport type { ISoundOptions } from \"./Interfaces/ISoundOptions\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { IAudioEngine } from \"./Interfaces/IAudioEngine\";\r\nimport type { Observer } from \"../Misc/observable\";\r\n\r\n/**\r\n * Defines a sound that can be played in the application.\r\n * The sound can either be an ambient track or a simple sound played in reaction to a user action.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n */\r\nexport class Sound {\r\n    /**\r\n     * The name of the sound in the scene.\r\n     */\r\n    public name: string;\r\n    /**\r\n     * Does the sound autoplay once loaded.\r\n     */\r\n    public autoplay: boolean = false;\r\n\r\n    private _loop = false;\r\n    /**\r\n     * Does the sound loop after it finishes playing once.\r\n     */\r\n    public get loop(): boolean {\r\n        return this._loop;\r\n    }\r\n\r\n    public set loop(value: boolean) {\r\n        if (value === this._loop) {\r\n            return;\r\n        }\r\n\r\n        this._loop = value;\r\n        this.updateOptions({ loop: value });\r\n    }\r\n\r\n    /**\r\n     * Does the sound use a custom attenuation curve to simulate the falloff\r\n     * happening when the source gets further away from the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-your-own-custom-attenuation-function\r\n     */\r\n    public useCustomAttenuation: boolean = false;\r\n    /**\r\n     * The sound track id this sound belongs to.\r\n     */\r\n    public soundTrackId: number;\r\n    /**\r\n     * Is this sound currently played.\r\n     */\r\n    public isPlaying: boolean = false;\r\n    /**\r\n     * Is this sound currently paused.\r\n     */\r\n    public isPaused: boolean = false;\r\n    /**\r\n     * Define the reference distance the sound should be heard perfectly.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public refDistance: number = 1;\r\n    /**\r\n     * Define the roll off factor of spatial sounds.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public rolloffFactor: number = 1;\r\n    /**\r\n     * Define the max distance the sound should be heard (intensity just became 0 at this point).\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public maxDistance: number = 100;\r\n    /**\r\n     * Define the distance attenuation model the sound will follow.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public distanceModel: string = \"linear\";\r\n    /**\r\n     * @internal\r\n     * Back Compat\r\n     **/\r\n    public onended: () => any;\r\n    /**\r\n     * Gets or sets an object used to store user defined information for the sound.\r\n     */\r\n    public metadata: any = null;\r\n\r\n    /**\r\n     * Observable event when the current playing sound finishes.\r\n     */\r\n    public onEndedObservable = new Observable<Sound>();\r\n\r\n    /**\r\n     * Gets the current time for the sound.\r\n     */\r\n    public get currentTime(): number {\r\n        if (this._htmlAudioElement) {\r\n            return this._htmlAudioElement.currentTime;\r\n        }\r\n\r\n        if (Engine.audioEngine?.audioContext && (this.isPlaying || this.isPaused)) {\r\n            // The `_currentTime` member is only updated when the sound is paused. Add the time since the last start\r\n            // to get the actual current time.\r\n            const timeSinceLastStart = this.isPaused ? 0 : Engine.audioEngine.audioContext.currentTime - this._startTime;\r\n            return this._currentTime + timeSinceLastStart;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Does this sound enables spatial sound.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public get spatialSound(): boolean {\r\n        return this._spatialSound;\r\n    }\r\n\r\n    /**\r\n     * Does this sound enables spatial sound.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public set spatialSound(newValue: boolean) {\r\n        if (newValue == this._spatialSound) {\r\n            return;\r\n        }\r\n\r\n        const wasPlaying = this.isPlaying;\r\n        this.pause();\r\n\r\n        if (newValue) {\r\n            this._spatialSound = newValue;\r\n            this._updateSpatialParameters();\r\n        } else {\r\n            this._disableSpatialSound();\r\n        }\r\n\r\n        if (wasPlaying) {\r\n            this.play();\r\n        }\r\n    }\r\n\r\n    private _spatialSound: boolean = false;\r\n    private _panningModel: string = \"equalpower\";\r\n    private _playbackRate: number = 1;\r\n    private _streaming: boolean = false;\r\n    private _startTime: number = 0;\r\n    private _currentTime: number = 0;\r\n    private _position: Vector3 = Vector3.Zero();\r\n    private _localDirection: Vector3 = new Vector3(1, 0, 0);\r\n    private _volume: number = 1;\r\n    private _isReadyToPlay: boolean = false;\r\n    private _isDirectional: boolean = false;\r\n    private _readyToPlayCallback: Nullable<() => any>;\r\n    private _audioBuffer: Nullable<AudioBuffer>;\r\n    private _soundSource: Nullable<AudioBufferSourceNode>;\r\n    private _streamingSource: AudioNode;\r\n    private _soundPanner: Nullable<PannerNode>;\r\n    private _soundGain: Nullable<GainNode>;\r\n    private _inputAudioNode: Nullable<AudioNode>;\r\n    private _outputAudioNode: Nullable<AudioNode>;\r\n    // Used if you'd like to create a directional sound.\r\n    // If not set, the sound will be omnidirectional\r\n    private _coneInnerAngle: number = 360;\r\n    private _coneOuterAngle: number = 360;\r\n    private _coneOuterGain: number = 0;\r\n    private _scene: Scene;\r\n    private _connectedTransformNode: Nullable<TransformNode>;\r\n    private _customAttenuationFunction: (currentVolume: number, currentDistance: number, maxDistance: number, refDistance: number, rolloffFactor: number) => number;\r\n    private _registerFunc: Nullable<(connectedMesh: TransformNode) => void>;\r\n    private _isOutputConnected = false;\r\n    private _htmlAudioElement: HTMLAudioElement;\r\n    private _urlType: \"Unknown\" | \"String\" | \"Array\" | \"ArrayBuffer\" | \"MediaStream\" | \"AudioBuffer\" | \"MediaElement\" = \"Unknown\";\r\n    private _length?: number;\r\n    private _offset?: number;\r\n    private _tryToPlayTimeout: Nullable<NodeJS.Timeout>;\r\n    private _audioUnlockedObserver?: Nullable<Observer<IAudioEngine>>;\r\n    private _url?: Nullable<string>;\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _SceneComponentInitialization: (scene: Scene) => void = (_) => {\r\n        throw _WarnImport(\"AudioSceneComponent\");\r\n    };\r\n\r\n    /**\r\n     * Create a sound and attach it to a scene\r\n     * @param name Name of your sound\r\n     * @param urlOrArrayBuffer Url to the sound to load async or ArrayBuffer, it also works with MediaStreams and AudioBuffers\r\n     * @param scene defines the scene the sound belongs to\r\n     * @param readyToPlayCallback Provide a callback function if you'd like to load your code once the sound is ready to be played\r\n     * @param options Objects to provide with the current available options: autoplay, loop, volume, spatialSound, maxDistance, rolloffFactor, refDistance, distanceModel, panningModel, streaming\r\n     */\r\n    constructor(name: string, urlOrArrayBuffer: any, scene?: Nullable<Scene>, readyToPlayCallback: Nullable<() => void> = null, options?: ISoundOptions) {\r\n        this.name = name;\r\n        scene = scene || EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        this._scene = scene;\r\n        Sound._SceneComponentInitialization(scene);\r\n\r\n        this._readyToPlayCallback = readyToPlayCallback;\r\n        // Default custom attenuation function is a linear attenuation\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        this._customAttenuationFunction = (currentVolume: number, currentDistance: number, maxDistance: number, refDistance: number, rolloffFactor: number) => {\r\n            if (currentDistance < maxDistance) {\r\n                return currentVolume * (1 - currentDistance / maxDistance);\r\n            } else {\r\n                return 0;\r\n            }\r\n        };\r\n        if (options) {\r\n            this.autoplay = options.autoplay || false;\r\n            this._loop = options.loop || false;\r\n            // if volume === 0, we need another way to check this option\r\n            if (options.volume !== undefined) {\r\n                this._volume = options.volume;\r\n            }\r\n            this._spatialSound = options.spatialSound ?? false;\r\n            this.maxDistance = options.maxDistance ?? 100;\r\n            this.useCustomAttenuation = options.useCustomAttenuation ?? false;\r\n            this.rolloffFactor = options.rolloffFactor || 1;\r\n            this.refDistance = options.refDistance || 1;\r\n            this.distanceModel = options.distanceModel || \"linear\";\r\n            this._playbackRate = options.playbackRate || 1;\r\n            this._streaming = options.streaming ?? false;\r\n            this._length = options.length;\r\n            this._offset = options.offset;\r\n        }\r\n\r\n        if (Engine.audioEngine?.canUseWebAudio && Engine.audioEngine.audioContext) {\r\n            this._soundGain = Engine.audioEngine.audioContext.createGain();\r\n            this._soundGain!.gain.value = this._volume;\r\n            this._inputAudioNode = this._soundGain;\r\n            this._outputAudioNode = this._soundGain;\r\n            if (this._spatialSound) {\r\n                this._createSpatialParameters();\r\n            }\r\n            this._scene.mainSoundTrack.addSound(this);\r\n            let validParameter = true;\r\n\r\n            // if no parameter is passed, you need to call setAudioBuffer yourself to prepare the sound\r\n            if (urlOrArrayBuffer) {\r\n                try {\r\n                    if (typeof urlOrArrayBuffer === \"string\") {\r\n                        this._urlType = \"String\";\r\n                        this._url = urlOrArrayBuffer;\r\n                    } else if (urlOrArrayBuffer instanceof ArrayBuffer) {\r\n                        this._urlType = \"ArrayBuffer\";\r\n                    } else if (urlOrArrayBuffer instanceof HTMLMediaElement) {\r\n                        this._urlType = \"MediaElement\";\r\n                    } else if (urlOrArrayBuffer instanceof MediaStream) {\r\n                        this._urlType = \"MediaStream\";\r\n                    } else if (urlOrArrayBuffer instanceof AudioBuffer) {\r\n                        this._urlType = \"AudioBuffer\";\r\n                    } else if (Array.isArray(urlOrArrayBuffer)) {\r\n                        this._urlType = \"Array\";\r\n                    }\r\n\r\n                    let urls: string[] = [];\r\n                    let codecSupportedFound = false;\r\n\r\n                    switch (this._urlType) {\r\n                        case \"MediaElement\":\r\n                            this._streaming = true;\r\n                            this._isReadyToPlay = true;\r\n                            this._streamingSource = Engine.audioEngine.audioContext.createMediaElementSource(urlOrArrayBuffer);\r\n\r\n                            if (this.autoplay) {\r\n                                this.play(0, this._offset, this._length);\r\n                            }\r\n\r\n                            if (this._readyToPlayCallback) {\r\n                                this._readyToPlayCallback();\r\n                            }\r\n                            break;\r\n                        case \"MediaStream\":\r\n                            this._streaming = true;\r\n                            this._isReadyToPlay = true;\r\n                            this._streamingSource = Engine.audioEngine.audioContext.createMediaStreamSource(urlOrArrayBuffer);\r\n\r\n                            if (this.autoplay) {\r\n                                this.play(0, this._offset, this._length);\r\n                            }\r\n\r\n                            if (this._readyToPlayCallback) {\r\n                                this._readyToPlayCallback();\r\n                            }\r\n                            break;\r\n                        case \"ArrayBuffer\":\r\n                            if ((<ArrayBuffer>urlOrArrayBuffer).byteLength > 0) {\r\n                                codecSupportedFound = true;\r\n                                this._soundLoaded(urlOrArrayBuffer);\r\n                            }\r\n                            break;\r\n                        case \"AudioBuffer\":\r\n                            this._audioBufferLoaded(urlOrArrayBuffer);\r\n                            break;\r\n                        case \"String\":\r\n                            urls.push(urlOrArrayBuffer);\r\n                        // eslint-disable-next-line no-fallthrough\r\n                        case \"Array\":\r\n                            if (urls.length === 0) {\r\n                                urls = urlOrArrayBuffer;\r\n                            }\r\n                            // If we found a supported format, we load it immediately and stop the loop\r\n                            for (let i = 0; i < urls.length; i++) {\r\n                                const url = urls[i];\r\n                                codecSupportedFound =\r\n                                    (options && options.skipCodecCheck) ||\r\n                                    (url.indexOf(\".mp3\", url.length - 4) !== -1 && Engine.audioEngine.isMP3supported) ||\r\n                                    (url.indexOf(\".ogg\", url.length - 4) !== -1 && Engine.audioEngine.isOGGsupported) ||\r\n                                    url.indexOf(\".wav\", url.length - 4) !== -1 ||\r\n                                    url.indexOf(\".m4a\", url.length - 4) !== -1 ||\r\n                                    url.indexOf(\".mp4\", url.length - 4) !== -1 ||\r\n                                    url.indexOf(\"blob:\") !== -1;\r\n                                if (codecSupportedFound) {\r\n                                    // Loading sound\r\n                                    if (!this._streaming) {\r\n                                        this._scene._loadFile(\r\n                                            url,\r\n                                            (data) => {\r\n                                                this._soundLoaded(data as ArrayBuffer);\r\n                                            },\r\n                                            undefined,\r\n                                            true,\r\n                                            true,\r\n                                            (exception) => {\r\n                                                if (exception) {\r\n                                                    Logger.Error(\"XHR \" + exception.status + \" error on: \" + url + \".\");\r\n                                                }\r\n                                                Logger.Error(\"Sound creation aborted.\");\r\n                                                this._scene.mainSoundTrack.removeSound(this);\r\n                                            }\r\n                                        );\r\n                                    }\r\n                                    // Streaming sound using HTML5 Audio tag\r\n                                    else {\r\n                                        this._htmlAudioElement = new Audio(url);\r\n                                        this._htmlAudioElement.controls = false;\r\n                                        this._htmlAudioElement.loop = this.loop;\r\n                                        Tools.SetCorsBehavior(url, this._htmlAudioElement);\r\n                                        this._htmlAudioElement.preload = \"auto\";\r\n                                        this._htmlAudioElement.addEventListener(\"canplaythrough\", () => {\r\n                                            this._isReadyToPlay = true;\r\n                                            if (this.autoplay) {\r\n                                                this.play(0, this._offset, this._length);\r\n                                            }\r\n                                            if (this._readyToPlayCallback) {\r\n                                                this._readyToPlayCallback();\r\n                                            }\r\n                                        });\r\n                                        document.body.appendChild(this._htmlAudioElement);\r\n                                        this._htmlAudioElement.load();\r\n                                    }\r\n                                    break;\r\n                                }\r\n                            }\r\n                            break;\r\n                        default:\r\n                            validParameter = false;\r\n                            break;\r\n                    }\r\n\r\n                    if (!validParameter) {\r\n                        Logger.Error(\"Parameter must be a URL to the sound, an Array of URLs (.mp3 & .ogg) or an ArrayBuffer of the sound.\");\r\n                    } else {\r\n                        if (!codecSupportedFound) {\r\n                            this._isReadyToPlay = true;\r\n                            // Simulating a ready to play event to avoid breaking code path\r\n                            if (this._readyToPlayCallback) {\r\n                                setTimeout(() => {\r\n                                    if (this._readyToPlayCallback) {\r\n                                        this._readyToPlayCallback();\r\n                                    }\r\n                                }, 1000);\r\n                            }\r\n                        }\r\n                    }\r\n                } catch (ex) {\r\n                    Logger.Error(\"Unexpected error. Sound creation aborted.\");\r\n                    this._scene.mainSoundTrack.removeSound(this);\r\n                }\r\n            }\r\n        } else {\r\n            // Adding an empty sound to avoid breaking audio calls for non Web Audio browsers\r\n            this._scene.mainSoundTrack.addSound(this);\r\n            if (Engine.audioEngine && !Engine.audioEngine.WarnedWebAudioUnsupported) {\r\n                Logger.Error(\"Web Audio is not supported by your browser.\");\r\n                Engine.audioEngine.WarnedWebAudioUnsupported = true;\r\n            }\r\n            // Simulating a ready to play event to avoid breaking code for non web audio browsers\r\n            if (this._readyToPlayCallback) {\r\n                setTimeout(() => {\r\n                    if (this._readyToPlayCallback) {\r\n                        this._readyToPlayCallback();\r\n                    }\r\n                }, 1000);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Release the sound and its associated resources\r\n     */\r\n    public dispose() {\r\n        if (Engine.audioEngine?.canUseWebAudio) {\r\n            if (this.isPlaying) {\r\n                this.stop();\r\n            }\r\n            this._isReadyToPlay = false;\r\n            if (this.soundTrackId === -1) {\r\n                this._scene.mainSoundTrack.removeSound(this);\r\n            } else if (this._scene.soundTracks) {\r\n                this._scene.soundTracks[this.soundTrackId].removeSound(this);\r\n            }\r\n            if (this._soundGain) {\r\n                this._soundGain.disconnect();\r\n                this._soundGain = null;\r\n            }\r\n            if (this._soundPanner) {\r\n                this._soundPanner.disconnect();\r\n                this._soundPanner = null;\r\n            }\r\n            if (this._soundSource) {\r\n                this._soundSource.disconnect();\r\n                this._soundSource = null;\r\n            }\r\n            this._audioBuffer = null;\r\n\r\n            if (this._htmlAudioElement) {\r\n                this._htmlAudioElement.pause();\r\n                this._htmlAudioElement.src = \"\";\r\n                document.body.removeChild(this._htmlAudioElement);\r\n            }\r\n\r\n            if (this._streamingSource) {\r\n                this._streamingSource.disconnect();\r\n            }\r\n\r\n            if (this._connectedTransformNode && this._registerFunc) {\r\n                this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc);\r\n                this._connectedTransformNode = null;\r\n            }\r\n\r\n            this._clearTimeoutsAndObservers();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets if the sounds is ready to be played or not.\r\n     * @returns true if ready, otherwise false\r\n     */\r\n    public isReady(): boolean {\r\n        return this._isReadyToPlay;\r\n    }\r\n\r\n    /**\r\n     * Get the current class name.\r\n     * @returns current class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"Sound\";\r\n    }\r\n\r\n    private _audioBufferLoaded(buffer: AudioBuffer) {\r\n        if (!Engine.audioEngine?.audioContext) {\r\n            return;\r\n        }\r\n        this._audioBuffer = buffer;\r\n        this._isReadyToPlay = true;\r\n        if (this.autoplay) {\r\n            this.play(0, this._offset, this._length);\r\n        }\r\n        if (this._readyToPlayCallback) {\r\n            this._readyToPlayCallback();\r\n        }\r\n    }\r\n\r\n    private _soundLoaded(audioData: ArrayBuffer) {\r\n        if (!Engine.audioEngine?.audioContext) {\r\n            return;\r\n        }\r\n        Engine.audioEngine.audioContext.decodeAudioData(\r\n            audioData,\r\n            (buffer) => {\r\n                this._audioBufferLoaded(buffer);\r\n            },\r\n            (err: any) => {\r\n                Logger.Error(\"Error while decoding audio data for: \" + this.name + \" / Error: \" + err);\r\n            }\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Sets the data of the sound from an audiobuffer\r\n     * @param audioBuffer The audioBuffer containing the data\r\n     */\r\n    public setAudioBuffer(audioBuffer: AudioBuffer): void {\r\n        if (Engine.audioEngine?.canUseWebAudio) {\r\n            this._audioBuffer = audioBuffer;\r\n            this._isReadyToPlay = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the current sounds options such as maxdistance, loop...\r\n     * @param options A JSON object containing values named as the object properties\r\n     */\r\n    public updateOptions(options: ISoundOptions): void {\r\n        if (options) {\r\n            this.loop = options.loop ?? this.loop;\r\n            this.maxDistance = options.maxDistance ?? this.maxDistance;\r\n            this.useCustomAttenuation = options.useCustomAttenuation ?? this.useCustomAttenuation;\r\n            this.rolloffFactor = options.rolloffFactor ?? this.rolloffFactor;\r\n            this.refDistance = options.refDistance ?? this.refDistance;\r\n            this.distanceModel = options.distanceModel ?? this.distanceModel;\r\n            this._playbackRate = options.playbackRate ?? this._playbackRate;\r\n            this._length = options.length ?? undefined;\r\n            this.spatialSound = options.spatialSound ?? this._spatialSound;\r\n            this._setOffset(options.offset ?? undefined);\r\n            this.setVolume(options.volume ?? this._volume);\r\n            this._updateSpatialParameters();\r\n            if (this.isPlaying) {\r\n                if (this._streaming && this._htmlAudioElement) {\r\n                    this._htmlAudioElement.playbackRate = this._playbackRate;\r\n                    if (this._htmlAudioElement.loop !== this.loop) {\r\n                        this._htmlAudioElement.loop = this.loop;\r\n                    }\r\n                } else {\r\n                    if (this._soundSource) {\r\n                        this._soundSource.playbackRate.value = this._playbackRate;\r\n                        if (this._soundSource.loop !== this.loop) {\r\n                            this._soundSource.loop = this.loop;\r\n                        }\r\n                        if (this._offset !== undefined && this._soundSource.loopStart !== this._offset) {\r\n                            this._soundSource.loopStart = this._offset;\r\n                        }\r\n                        if (this._length !== undefined && this._length !== this._soundSource.loopEnd) {\r\n                            this._soundSource.loopEnd = (this._offset! | 0) + this._length!;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private _createSpatialParameters() {\r\n        if (Engine.audioEngine?.canUseWebAudio && Engine.audioEngine.audioContext) {\r\n            if (this._scene.headphone) {\r\n                this._panningModel = \"HRTF\";\r\n            }\r\n            this._soundPanner = this._soundPanner ?? Engine.audioEngine.audioContext.createPanner();\r\n            if (this._soundPanner && this._outputAudioNode) {\r\n                this._updateSpatialParameters();\r\n                this._soundPanner.connect(this._outputAudioNode);\r\n                this._inputAudioNode = this._soundPanner;\r\n            }\r\n        }\r\n    }\r\n\r\n    private _disableSpatialSound() {\r\n        if (!this._spatialSound) {\r\n            return;\r\n        }\r\n        this._inputAudioNode = this._soundGain;\r\n        this._soundPanner?.disconnect();\r\n        this._soundPanner = null;\r\n        this._spatialSound = false;\r\n    }\r\n\r\n    private _updateSpatialParameters() {\r\n        if (!this._spatialSound) {\r\n            return;\r\n        }\r\n        if (this._soundPanner) {\r\n            if (this.useCustomAttenuation) {\r\n                // Tricks to disable in a way embedded Web Audio attenuation\r\n                this._soundPanner.distanceModel = \"linear\";\r\n                this._soundPanner.maxDistance = Number.MAX_VALUE;\r\n                this._soundPanner.refDistance = 1;\r\n                this._soundPanner.rolloffFactor = 1;\r\n                this._soundPanner.panningModel = this._panningModel as any;\r\n            } else {\r\n                this._soundPanner.distanceModel = this.distanceModel as any;\r\n                this._soundPanner.maxDistance = this.maxDistance;\r\n                this._soundPanner.refDistance = this.refDistance;\r\n                this._soundPanner.rolloffFactor = this.rolloffFactor;\r\n                this._soundPanner.panningModel = this._panningModel as any;\r\n            }\r\n        } else {\r\n            this._createSpatialParameters();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Switch the panning model to HRTF:\r\n     * Renders a stereo output of higher quality than equalpower — it uses a convolution with measured impulse responses from human subjects.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public switchPanningModelToHRTF() {\r\n        this._panningModel = \"HRTF\";\r\n        this._switchPanningModel();\r\n    }\r\n\r\n    /**\r\n     * Switch the panning model to Equal Power:\r\n     * Represents the equal-power panning algorithm, generally regarded as simple and efficient. equalpower is the default value.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public switchPanningModelToEqualPower() {\r\n        this._panningModel = \"equalpower\";\r\n        this._switchPanningModel();\r\n    }\r\n\r\n    private _switchPanningModel() {\r\n        if (Engine.audioEngine?.canUseWebAudio && this._spatialSound && this._soundPanner) {\r\n            this._soundPanner.panningModel = this._panningModel as any;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Connect this sound to a sound track audio node like gain...\r\n     * @param soundTrackAudioNode the sound track audio node to connect to\r\n     */\r\n    public connectToSoundTrackAudioNode(soundTrackAudioNode: AudioNode): void {\r\n        if (Engine.audioEngine?.canUseWebAudio && this._outputAudioNode) {\r\n            if (this._isOutputConnected) {\r\n                this._outputAudioNode.disconnect();\r\n            }\r\n            this._outputAudioNode.connect(soundTrackAudioNode);\r\n            this._isOutputConnected = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Transform this sound into a directional source\r\n     * @param coneInnerAngle Size of the inner cone in degree\r\n     * @param coneOuterAngle Size of the outer cone in degree\r\n     * @param coneOuterGain Volume of the sound outside the outer cone (between 0.0 and 1.0)\r\n     */\r\n    public setDirectionalCone(coneInnerAngle: number, coneOuterAngle: number, coneOuterGain: number): void {\r\n        if (coneOuterAngle < coneInnerAngle) {\r\n            Logger.Error(\"setDirectionalCone(): outer angle of the cone must be superior or equal to the inner angle.\");\r\n            return;\r\n        }\r\n        this._coneInnerAngle = coneInnerAngle;\r\n        this._coneOuterAngle = coneOuterAngle;\r\n        this._coneOuterGain = coneOuterGain;\r\n        this._isDirectional = true;\r\n\r\n        if (this.isPlaying && this.loop) {\r\n            this.stop();\r\n            this.play(0, this._offset, this._length);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the inner angle for the directional cone.\r\n     */\r\n    public get directionalConeInnerAngle(): number {\r\n        return this._coneInnerAngle;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the inner angle for the directional cone.\r\n     */\r\n    public set directionalConeInnerAngle(value: number) {\r\n        if (value != this._coneInnerAngle) {\r\n            if (this._coneOuterAngle < value) {\r\n                Logger.Error(\"directionalConeInnerAngle: outer angle of the cone must be superior or equal to the inner angle.\");\r\n                return;\r\n            }\r\n\r\n            this._coneInnerAngle = value;\r\n            if (Engine.audioEngine?.canUseWebAudio && this._spatialSound && this._soundPanner) {\r\n                this._soundPanner.coneInnerAngle = this._coneInnerAngle;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the outer angle for the directional cone.\r\n     */\r\n    public get directionalConeOuterAngle(): number {\r\n        return this._coneOuterAngle;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the outer angle for the directional cone.\r\n     */\r\n    public set directionalConeOuterAngle(value: number) {\r\n        if (value != this._coneOuterAngle) {\r\n            if (value < this._coneInnerAngle) {\r\n                Logger.Error(\"directionalConeOuterAngle: outer angle of the cone must be superior or equal to the inner angle.\");\r\n                return;\r\n            }\r\n\r\n            this._coneOuterAngle = value;\r\n            if (Engine.audioEngine?.canUseWebAudio && this._spatialSound && this._soundPanner) {\r\n                this._soundPanner.coneOuterAngle = this._coneOuterAngle;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the position of the emitter if spatial sound is enabled\r\n     * @param newPosition Defines the new position\r\n     */\r\n    public setPosition(newPosition: Vector3): void {\r\n        if (newPosition.equals(this._position)) {\r\n            return;\r\n        }\r\n        this._position.copyFrom(newPosition);\r\n\r\n        if (Engine.audioEngine?.canUseWebAudio && this._spatialSound && this._soundPanner && !isNaN(this._position.x) && !isNaN(this._position.y) && !isNaN(this._position.z)) {\r\n            this._soundPanner.positionX.value = this._position.x;\r\n            this._soundPanner.positionY.value = this._position.y;\r\n            this._soundPanner.positionZ.value = this._position.z;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the local direction of the emitter if spatial sound is enabled\r\n     * @param newLocalDirection Defines the new local direction\r\n     */\r\n    public setLocalDirectionToMesh(newLocalDirection: Vector3): void {\r\n        this._localDirection = newLocalDirection;\r\n\r\n        if (Engine.audioEngine?.canUseWebAudio && this._connectedTransformNode && this.isPlaying) {\r\n            this._updateDirection();\r\n        }\r\n    }\r\n\r\n    private _updateDirection() {\r\n        if (!this._connectedTransformNode || !this._soundPanner) {\r\n            return;\r\n        }\r\n\r\n        const mat = this._connectedTransformNode.getWorldMatrix();\r\n        const direction = Vector3.TransformNormal(this._localDirection, mat);\r\n        direction.normalize();\r\n        this._soundPanner.orientationX.value = direction.x;\r\n        this._soundPanner.orientationY.value = direction.y;\r\n        this._soundPanner.orientationZ.value = direction.z;\r\n    }\r\n\r\n    /** @internal */\r\n    public updateDistanceFromListener() {\r\n        if (Engine.audioEngine?.canUseWebAudio && this._connectedTransformNode && this.useCustomAttenuation && this._soundGain && this._scene.activeCamera) {\r\n            const distance = this._scene.audioListenerPositionProvider\r\n                ? this._connectedTransformNode.position.subtract(this._scene.audioListenerPositionProvider()).length()\r\n                : this._connectedTransformNode.getDistanceToCamera(this._scene.activeCamera);\r\n            this._soundGain.gain.value = this._customAttenuationFunction(this._volume, distance, this.maxDistance, this.refDistance, this.rolloffFactor);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a new custom attenuation function for the sound.\r\n     * @param callback Defines the function used for the attenuation\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-your-own-custom-attenuation-function\r\n     */\r\n    public setAttenuationFunction(callback: (currentVolume: number, currentDistance: number, maxDistance: number, refDistance: number, rolloffFactor: number) => number): void {\r\n        this._customAttenuationFunction = callback;\r\n    }\r\n\r\n    /**\r\n     * Play the sound\r\n     * @param time (optional) Start the sound after X seconds. Start immediately (0) by default.\r\n     * @param offset (optional) Start the sound at a specific time in seconds\r\n     * @param length (optional) Sound duration (in seconds)\r\n     */\r\n    public play(time?: number, offset?: number, length?: number): void {\r\n        if (this._isReadyToPlay && this._scene.audioEnabled && Engine.audioEngine?.audioContext) {\r\n            try {\r\n                this._clearTimeoutsAndObservers();\r\n\r\n                let startTime = time ? Engine.audioEngine?.audioContext.currentTime + time : Engine.audioEngine?.audioContext.currentTime;\r\n                if (!this._soundSource || !this._streamingSource) {\r\n                    if (this._spatialSound && this._soundPanner) {\r\n                        if (!isNaN(this._position.x) && !isNaN(this._position.y) && !isNaN(this._position.z)) {\r\n                            this._soundPanner.positionX.value = this._position.x;\r\n                            this._soundPanner.positionY.value = this._position.y;\r\n                            this._soundPanner.positionZ.value = this._position.z;\r\n                        }\r\n                        if (this._isDirectional) {\r\n                            this._soundPanner.coneInnerAngle = this._coneInnerAngle;\r\n                            this._soundPanner.coneOuterAngle = this._coneOuterAngle;\r\n                            this._soundPanner.coneOuterGain = this._coneOuterGain;\r\n                            if (this._connectedTransformNode) {\r\n                                this._updateDirection();\r\n                            } else {\r\n                                this._soundPanner.setOrientation(this._localDirection.x, this._localDirection.y, this._localDirection.z);\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                if (this._streaming) {\r\n                    if (!this._streamingSource) {\r\n                        this._streamingSource = Engine.audioEngine.audioContext.createMediaElementSource(this._htmlAudioElement);\r\n                        this._htmlAudioElement.onended = () => {\r\n                            this._onended();\r\n                        };\r\n                        this._htmlAudioElement.playbackRate = this._playbackRate;\r\n                    }\r\n                    this._streamingSource.disconnect();\r\n                    if (this._inputAudioNode) {\r\n                        this._streamingSource.connect(this._inputAudioNode);\r\n                    }\r\n                    if (this._htmlAudioElement) {\r\n                        // required to manage properly the new suspended default state of Chrome\r\n                        // When the option 'streaming: true' is used, we need first to wait for\r\n                        // the audio engine to be unlocked by a user gesture before trying to play\r\n                        // an HTML Audio element\r\n                        const tryToPlay = () => {\r\n                            if (Engine.audioEngine?.unlocked) {\r\n                                const playPromise = this._htmlAudioElement.play();\r\n\r\n                                // In browsers that don’t yet support this functionality,\r\n                                // playPromise won’t be defined.\r\n                                if (playPromise !== undefined) {\r\n                                    playPromise.catch(() => {\r\n                                        // Automatic playback failed.\r\n                                        // Waiting for the audio engine to be unlocked by user click on unmute\r\n                                        Engine.audioEngine?.lock();\r\n                                        if (this.loop || this.autoplay) {\r\n                                            this._audioUnlockedObserver = Engine.audioEngine?.onAudioUnlockedObservable.addOnce(() => {\r\n                                                tryToPlay();\r\n                                            });\r\n                                        }\r\n                                    });\r\n                                }\r\n                            } else {\r\n                                if (this.loop || this.autoplay) {\r\n                                    this._audioUnlockedObserver = Engine.audioEngine?.onAudioUnlockedObservable.addOnce(() => {\r\n                                        tryToPlay();\r\n                                    });\r\n                                }\r\n                            }\r\n                        };\r\n                        tryToPlay();\r\n                    }\r\n                } else {\r\n                    const tryToPlay = () => {\r\n                        if (Engine.audioEngine?.audioContext) {\r\n                            length = length || this._length;\r\n\r\n                            if (offset !== undefined) {\r\n                                this._setOffset(offset);\r\n                            }\r\n\r\n                            if (this._soundSource) {\r\n                                const oldSource = this._soundSource;\r\n                                oldSource.onended = () => {\r\n                                    oldSource.disconnect();\r\n                                };\r\n                            }\r\n                            this._soundSource = Engine.audioEngine?.audioContext.createBufferSource();\r\n                            if (this._soundSource && this._inputAudioNode) {\r\n                                this._soundSource.buffer = this._audioBuffer;\r\n                                this._soundSource.connect(this._inputAudioNode);\r\n                                this._soundSource.loop = this.loop;\r\n                                if (offset !== undefined) {\r\n                                    this._soundSource.loopStart = offset;\r\n                                }\r\n                                if (length !== undefined) {\r\n                                    this._soundSource.loopEnd = (offset! | 0) + length!;\r\n                                }\r\n                                this._soundSource.playbackRate.value = this._playbackRate;\r\n                                this._soundSource.onended = () => {\r\n                                    this._onended();\r\n                                };\r\n                                startTime = time ? Engine.audioEngine?.audioContext!.currentTime + time : Engine.audioEngine.audioContext!.currentTime;\r\n                                const actualOffset = ((this.isPaused ? this.currentTime : 0) + (this._offset ?? 0)) % this._soundSource!.buffer!.duration;\r\n                                this._soundSource!.start(startTime, actualOffset, this.loop ? undefined : length);\r\n                            }\r\n                        }\r\n                    };\r\n\r\n                    if (Engine.audioEngine?.audioContext.state === \"suspended\") {\r\n                        // Wait a bit for FF as context seems late to be ready.\r\n                        this._tryToPlayTimeout = setTimeout(() => {\r\n                            if (Engine.audioEngine?.audioContext!.state === \"suspended\") {\r\n                                // Automatic playback failed.\r\n                                // Waiting for the audio engine to be unlocked by user click on unmute\r\n                                Engine.audioEngine.lock();\r\n                                if (this.loop || this.autoplay) {\r\n                                    this._audioUnlockedObserver = Engine.audioEngine.onAudioUnlockedObservable.addOnce(() => {\r\n                                        tryToPlay();\r\n                                    });\r\n                                }\r\n                            } else {\r\n                                tryToPlay();\r\n                            }\r\n                        }, 500);\r\n                    } else {\r\n                        tryToPlay();\r\n                    }\r\n                }\r\n                this._startTime = startTime;\r\n                this.isPlaying = true;\r\n                this.isPaused = false;\r\n            } catch (ex) {\r\n                Logger.Error(\"Error while trying to play audio: \" + this.name + \", \" + ex.message);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _onended() {\r\n        this.isPlaying = false;\r\n        this._startTime = 0;\r\n        this._currentTime = 0;\r\n        if (this.onended) {\r\n            this.onended();\r\n        }\r\n        this.onEndedObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Stop the sound\r\n     * @param time (optional) Stop the sound after X seconds. Stop immediately (0) by default.\r\n     */\r\n    public stop(time?: number): void {\r\n        if (this.isPlaying) {\r\n            this._clearTimeoutsAndObservers();\r\n            if (this._streaming) {\r\n                if (this._htmlAudioElement) {\r\n                    this._htmlAudioElement.pause();\r\n                    // Test needed for Firefox or it will generate an Invalid State Error\r\n                    if (this._htmlAudioElement.currentTime > 0) {\r\n                        this._htmlAudioElement.currentTime = 0;\r\n                    }\r\n                } else {\r\n                    this._streamingSource.disconnect();\r\n                }\r\n                this.isPlaying = false;\r\n            } else if (Engine.audioEngine?.audioContext && this._soundSource) {\r\n                const stopTime = time ? Engine.audioEngine.audioContext.currentTime + time : undefined;\r\n                this._soundSource.onended = () => {\r\n                    this.isPlaying = false;\r\n                    this.isPaused = false;\r\n                    this._startTime = 0;\r\n                    this._currentTime = 0;\r\n                    if (this._soundSource) {\r\n                        this._soundSource.onended = () => void 0;\r\n                    }\r\n                    this._onended();\r\n                };\r\n                this._soundSource.stop(stopTime);\r\n            } else {\r\n                this.isPlaying = false;\r\n            }\r\n        } else if (this.isPaused) {\r\n            this.isPaused = false;\r\n            this._startTime = 0;\r\n            this._currentTime = 0;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Put the sound in pause\r\n     */\r\n    public pause(): void {\r\n        if (this.isPlaying) {\r\n            this._clearTimeoutsAndObservers();\r\n            if (this._streaming) {\r\n                if (this._htmlAudioElement) {\r\n                    this._htmlAudioElement.pause();\r\n                } else {\r\n                    this._streamingSource.disconnect();\r\n                }\r\n                this.isPlaying = false;\r\n                this.isPaused = true;\r\n            } else if (Engine.audioEngine?.audioContext && this._soundSource) {\r\n                this._soundSource.onended = () => void 0;\r\n                this._soundSource.stop();\r\n                this.isPlaying = false;\r\n                this.isPaused = true;\r\n                this._currentTime += Engine.audioEngine.audioContext.currentTime - this._startTime;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a dedicated volume for this sounds\r\n     * @param newVolume Define the new volume of the sound\r\n     * @param time Define time for gradual change to new volume\r\n     */\r\n    public setVolume(newVolume: number, time?: number): void {\r\n        if (Engine.audioEngine?.canUseWebAudio && this._soundGain) {\r\n            if (time && Engine.audioEngine.audioContext) {\r\n                this._soundGain.gain.cancelScheduledValues(Engine.audioEngine.audioContext.currentTime);\r\n                this._soundGain.gain.setValueAtTime(this._soundGain.gain.value, Engine.audioEngine.audioContext.currentTime);\r\n                this._soundGain.gain.linearRampToValueAtTime(newVolume, Engine.audioEngine.audioContext.currentTime + time);\r\n            } else {\r\n                this._soundGain.gain.value = newVolume;\r\n            }\r\n        }\r\n        this._volume = newVolume;\r\n    }\r\n\r\n    /**\r\n     * Set the sound play back rate\r\n     * @param newPlaybackRate Define the playback rate the sound should be played at\r\n     */\r\n    public setPlaybackRate(newPlaybackRate: number): void {\r\n        this._playbackRate = newPlaybackRate;\r\n        if (this.isPlaying) {\r\n            if (this._streaming && this._htmlAudioElement) {\r\n                this._htmlAudioElement.playbackRate = this._playbackRate;\r\n            } else if (this._soundSource) {\r\n                this._soundSource.playbackRate.value = this._playbackRate;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the sound play back rate.\r\n     * @returns the  play back rate of the sound\r\n     */\r\n    public getPlaybackRate(): number {\r\n        return this._playbackRate;\r\n    }\r\n\r\n    /**\r\n     * Gets the volume of the sound.\r\n     * @returns the volume of the sound\r\n     */\r\n    public getVolume(): number {\r\n        return this._volume;\r\n    }\r\n\r\n    /**\r\n     * Attach the sound to a dedicated mesh\r\n     * @param transformNode The transform node to connect the sound with\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#attaching-a-sound-to-a-mesh\r\n     */\r\n    public attachToMesh(transformNode: TransformNode): void {\r\n        if (this._connectedTransformNode && this._registerFunc) {\r\n            this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc);\r\n            this._registerFunc = null;\r\n        }\r\n        this._connectedTransformNode = transformNode;\r\n        if (!this._spatialSound) {\r\n            this._spatialSound = true;\r\n            this._createSpatialParameters();\r\n            if (this.isPlaying && this.loop) {\r\n                this.stop();\r\n                this.play(0, this._offset, this._length);\r\n            }\r\n        }\r\n        this._onRegisterAfterWorldMatrixUpdate(this._connectedTransformNode);\r\n        this._registerFunc = (transformNode: TransformNode) => this._onRegisterAfterWorldMatrixUpdate(transformNode);\r\n        this._connectedTransformNode.registerAfterWorldMatrixUpdate(this._registerFunc);\r\n    }\r\n\r\n    /**\r\n     * Detach the sound from the previously attached mesh\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#attaching-a-sound-to-a-mesh\r\n     */\r\n    public detachFromMesh() {\r\n        if (this._connectedTransformNode && this._registerFunc) {\r\n            this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc);\r\n            this._registerFunc = null;\r\n            this._connectedTransformNode = null;\r\n        }\r\n    }\r\n\r\n    private _onRegisterAfterWorldMatrixUpdate(node: TransformNode): void {\r\n        if (!(<any>node).getBoundingInfo) {\r\n            this.setPosition(node.absolutePosition);\r\n        } else {\r\n            const mesh = node as AbstractMesh;\r\n            const boundingInfo = mesh.getBoundingInfo();\r\n            this.setPosition(boundingInfo.boundingSphere.centerWorld);\r\n        }\r\n        if (Engine.audioEngine?.canUseWebAudio && this._isDirectional && this.isPlaying) {\r\n            this._updateDirection();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clone the current sound in the scene.\r\n     * @returns the new sound clone\r\n     */\r\n    public clone(): Nullable<Sound> {\r\n        if (!this._streaming) {\r\n            const setBufferAndRun = () => {\r\n                if (this._isReadyToPlay) {\r\n                    clonedSound._audioBuffer = this.getAudioBuffer();\r\n                    clonedSound._isReadyToPlay = true;\r\n                    if (clonedSound.autoplay) {\r\n                        clonedSound.play(0, this._offset, this._length);\r\n                    }\r\n                } else {\r\n                    setTimeout(setBufferAndRun, 300);\r\n                }\r\n            };\r\n\r\n            const currentOptions = {\r\n                autoplay: this.autoplay,\r\n                loop: this.loop,\r\n                volume: this._volume,\r\n                spatialSound: this._spatialSound,\r\n                maxDistance: this.maxDistance,\r\n                useCustomAttenuation: this.useCustomAttenuation,\r\n                rolloffFactor: this.rolloffFactor,\r\n                refDistance: this.refDistance,\r\n                distanceModel: this.distanceModel,\r\n            };\r\n\r\n            const clonedSound = new Sound(this.name + \"_cloned\", new ArrayBuffer(0), this._scene, null, currentOptions);\r\n            if (this.useCustomAttenuation) {\r\n                clonedSound.setAttenuationFunction(this._customAttenuationFunction);\r\n            }\r\n            clonedSound.setPosition(this._position);\r\n            clonedSound.setPlaybackRate(this._playbackRate);\r\n            setBufferAndRun();\r\n\r\n            return clonedSound;\r\n        }\r\n        // Can't clone a streaming sound\r\n        else {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the current underlying audio buffer containing the data\r\n     * @returns the audio buffer\r\n     */\r\n    public getAudioBuffer(): Nullable<AudioBuffer> {\r\n        return this._audioBuffer;\r\n    }\r\n\r\n    /**\r\n     * Gets the WebAudio AudioBufferSourceNode, lets you keep track of and stop instances of this Sound.\r\n     * @returns the source node\r\n     */\r\n    public getSoundSource(): Nullable<AudioBufferSourceNode> {\r\n        return this._soundSource;\r\n    }\r\n\r\n    /**\r\n     * Gets the WebAudio GainNode, gives you precise control over the gain of instances of this Sound.\r\n     * @returns the gain node\r\n     */\r\n    public getSoundGain(): Nullable<GainNode> {\r\n        return this._soundGain;\r\n    }\r\n\r\n    /**\r\n     * Serializes the Sound in a JSON representation\r\n     * @returns the JSON representation of the sound\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {\r\n            name: this.name,\r\n            url: this._url,\r\n            autoplay: this.autoplay,\r\n            loop: this.loop,\r\n            volume: this._volume,\r\n            spatialSound: this._spatialSound,\r\n            maxDistance: this.maxDistance,\r\n            rolloffFactor: this.rolloffFactor,\r\n            refDistance: this.refDistance,\r\n            distanceModel: this.distanceModel,\r\n            playbackRate: this._playbackRate,\r\n            panningModel: this._panningModel,\r\n            soundTrackId: this.soundTrackId,\r\n            metadata: this.metadata,\r\n        };\r\n\r\n        if (this._spatialSound) {\r\n            if (this._connectedTransformNode) {\r\n                serializationObject.connectedMeshId = this._connectedTransformNode.id;\r\n            }\r\n\r\n            serializationObject.position = this._position.asArray();\r\n            serializationObject.refDistance = this.refDistance;\r\n            serializationObject.distanceModel = this.distanceModel;\r\n\r\n            serializationObject.isDirectional = this._isDirectional;\r\n            serializationObject.localDirectionToMesh = this._localDirection.asArray();\r\n            serializationObject.coneInnerAngle = this._coneInnerAngle;\r\n            serializationObject.coneOuterAngle = this._coneOuterAngle;\r\n            serializationObject.coneOuterGain = this._coneOuterGain;\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parse a JSON representation of a sound to instantiate in a given scene\r\n     * @param parsedSound Define the JSON representation of the sound (usually coming from the serialize method)\r\n     * @param scene Define the scene the new parsed sound should be created in\r\n     * @param rootUrl Define the rooturl of the load in case we need to fetch relative dependencies\r\n     * @param sourceSound Define a sound place holder if do not need to instantiate a new one\r\n     * @returns the newly parsed sound\r\n     */\r\n    public static Parse(parsedSound: any, scene: Scene, rootUrl: string, sourceSound?: Sound): Sound {\r\n        const soundName = parsedSound.name;\r\n        let soundUrl;\r\n\r\n        if (parsedSound.url) {\r\n            soundUrl = rootUrl + parsedSound.url;\r\n        } else {\r\n            soundUrl = rootUrl + soundName;\r\n        }\r\n\r\n        const options = {\r\n            autoplay: parsedSound.autoplay,\r\n            loop: parsedSound.loop,\r\n            volume: parsedSound.volume,\r\n            spatialSound: parsedSound.spatialSound,\r\n            maxDistance: parsedSound.maxDistance,\r\n            rolloffFactor: parsedSound.rolloffFactor,\r\n            refDistance: parsedSound.refDistance,\r\n            distanceModel: parsedSound.distanceModel,\r\n            playbackRate: parsedSound.playbackRate,\r\n        };\r\n\r\n        let newSound: Sound;\r\n\r\n        if (!sourceSound) {\r\n            newSound = new Sound(\r\n                soundName,\r\n                soundUrl,\r\n                scene,\r\n                () => {\r\n                    scene.removePendingData(newSound);\r\n                },\r\n                options\r\n            );\r\n            scene.addPendingData(newSound);\r\n        } else {\r\n            const setBufferAndRun = () => {\r\n                if (sourceSound._isReadyToPlay) {\r\n                    newSound._audioBuffer = sourceSound.getAudioBuffer();\r\n                    newSound._isReadyToPlay = true;\r\n                    if (newSound.autoplay) {\r\n                        newSound.play(0, newSound._offset, newSound._length);\r\n                    }\r\n                } else {\r\n                    setTimeout(setBufferAndRun, 300);\r\n                }\r\n            };\r\n\r\n            newSound = new Sound(soundName, new ArrayBuffer(0), scene, null, options);\r\n            setBufferAndRun();\r\n        }\r\n\r\n        if (parsedSound.position) {\r\n            const soundPosition = Vector3.FromArray(parsedSound.position);\r\n            newSound.setPosition(soundPosition);\r\n        }\r\n        if (parsedSound.isDirectional) {\r\n            newSound.setDirectionalCone(parsedSound.coneInnerAngle || 360, parsedSound.coneOuterAngle || 360, parsedSound.coneOuterGain || 0);\r\n            if (parsedSound.localDirectionToMesh) {\r\n                const localDirectionToMesh = Vector3.FromArray(parsedSound.localDirectionToMesh);\r\n                newSound.setLocalDirectionToMesh(localDirectionToMesh);\r\n            }\r\n        }\r\n        if (parsedSound.connectedMeshId) {\r\n            const connectedMesh = scene.getMeshById(parsedSound.connectedMeshId);\r\n            if (connectedMesh) {\r\n                newSound.attachToMesh(connectedMesh);\r\n            }\r\n        }\r\n\r\n        if (parsedSound.metadata) {\r\n            newSound.metadata = parsedSound.metadata;\r\n        }\r\n\r\n        return newSound;\r\n    }\r\n\r\n    private _setOffset(value?: number) {\r\n        if (this._offset === value) {\r\n            return;\r\n        }\r\n        if (this.isPaused) {\r\n            this.stop();\r\n            this.isPaused = false;\r\n        }\r\n        this._offset = value;\r\n    }\r\n\r\n    private _clearTimeoutsAndObservers() {\r\n        if (this._tryToPlayTimeout) {\r\n            clearTimeout(this._tryToPlayTimeout);\r\n            this._tryToPlayTimeout = null;\r\n        }\r\n        if (this._audioUnlockedObserver) {\r\n            Engine.audioEngine?.onAudioUnlockedObservable.remove(this._audioUnlockedObserver);\r\n            this._audioUnlockedObserver = null;\r\n        }\r\n    }\r\n}\r\n"]}