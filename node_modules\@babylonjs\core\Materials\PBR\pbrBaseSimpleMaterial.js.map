{"version": 3, "file": "pbrBaseSimpleMaterial.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/PBR/pbrBaseSimpleMaterial.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAE3G,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAIpD;;;;;GAKG;AACH,MAAM,OAAgB,qBAAsB,SAAQ,eAAe;IA8E/D;;OAEG;IAEH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IACD;;OAEG;IACH,IAAW,WAAW,CAAC,KAAc;QACjC,IAAI,IAAI,CAAC,iBAAiB,KAAK,KAAK,EAAE;YAClC,OAAO;SACV;QACD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,CAAC,KAAK,CAAC;QAC9B,IAAI,CAAC,gCAAgC,EAAE,CAAC;IAC5C,CAAC;IAgBD;;;;;OAKG;IACH,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QArHvB;;WAEG;QAGI,0BAAqB,GAAG,CAAC,CAAC;QAEjC;;WAEG;QAGI,oBAAe,GAAG,KAAK,CAAC;QAS/B;;WAEG;QAGI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG;QAGI,qBAAgB,GAAG,KAAK,CAAC;QAShC;;WAEG;QAGI,kBAAa,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAS3C;;WAEG;QAGI,sBAAiB,GAAW,GAAG,CAAC;QA0CvC;;WAEG;QAGI,2BAAsB,GAAG,KAAK,CAAC;QAWlC,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;IACvC,CAAC;IAEM,YAAY;QACf,OAAO,uBAAuB,CAAC;IACnC,CAAC;CACJ;AAzHU;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,gCAAgC,CAAC;oEAClB;AAO1B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,gCAAgC,CAAC;8DACpB;AAOxB;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,EAAE,oBAAoB,CAAC;iEAC1B;AAO1C;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;+DACrB;AAOzB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;+DACrB;AAOzB;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,EAAE,cAAc,CAAC;4DACzB;AAOrC;IAFN,iBAAiB,CAAC,UAAU,CAAC;IAC7B,gBAAgB,CAAC,kCAAkC,CAAC;4DACV;AAOpC;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;8DACP;AAOvC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,EAAE,yBAAyB,CAAC;gEACzC;AAOhC;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,EAAE,iBAAiB,CAAC;+DACzB;AAOxC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,EAAE,cAAc,CAAC;0DAC1C;AAM3B;IADC,SAAS,EAAE;wDAGX;AAkBM;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,EAAE,IAAI,CAAC;8DACb;AAOvC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;qEACf", "sourcesContent": ["import { serialize, serializeAsColor3, expandToProperty, serializeAsTexture } from \"../../Misc/decorators\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * The Physically based simple base material of BJS.\r\n *\r\n * This enables better naming and convention enforcements on top of the pbrMaterial.\r\n * It is used as the base class for both the specGloss and metalRough conventions.\r\n */\r\nexport abstract class PBRBaseSimpleMaterial extends PBRBaseMaterial {\r\n    /**\r\n     * Number of Simultaneous lights allowed on the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public maxSimultaneousLights = 4;\r\n\r\n    /**\r\n     * If sets to true, disables all the lights affecting the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public disableLighting = false;\r\n\r\n    /**\r\n     * Environment Texture used in the material (this is use for both reflection and environment lighting).\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_reflectionTexture\")\r\n    public environmentTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * If sets to true, x component of normal map value will invert (x = 1.0 - x).\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public invertNormalMapX = false;\r\n\r\n    /**\r\n     * If sets to true, y component of normal map value will invert (y = 1.0 - y).\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public invertNormalMapY = false;\r\n\r\n    /**\r\n     * Normal map used in the model.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_bumpTexture\")\r\n    public normalTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Emissivie color used to self-illuminate the model.\r\n     */\r\n    @serializeAsColor3(\"emissive\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public emissiveColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * Emissivie texture used to self-illuminate the model.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public emissiveTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Occlusion Channel Strength.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_ambientTextureStrength\")\r\n    public occlusionStrength: number = 1.0;\r\n\r\n    /**\r\n     * Occlusion Texture of the material (adding extra occlusion effects).\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_ambientTexture\")\r\n    public occlusionTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Defines the alpha limits in alpha test mode.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_alphaCutOff\")\r\n    public alphaCutOff: number;\r\n\r\n    /**\r\n     * Gets the current double sided mode.\r\n     */\r\n    @serialize()\r\n    public get doubleSided(): boolean {\r\n        return this._twoSidedLighting;\r\n    }\r\n    /**\r\n     * If sets to true and backfaceCulling is false, normals will be flipped on the backside.\r\n     */\r\n    public set doubleSided(value: boolean) {\r\n        if (this._twoSidedLighting === value) {\r\n            return;\r\n        }\r\n        this._twoSidedLighting = value;\r\n        this.backFaceCulling = !value;\r\n        this._markAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    /**\r\n     * Stores the pre-calculated light information of a mesh in a texture.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", null)\r\n    public lightmapTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * If true, the light map contains occlusion information instead of lighting info.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useLightmapAsShadowmap = false;\r\n\r\n    /**\r\n     * Instantiates a new PBRMaterial instance.\r\n     *\r\n     * @param name The material name\r\n     * @param scene The scene the material will be use in.\r\n     */\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n\r\n        this._useAlphaFromAlbedoTexture = true;\r\n        this._useAmbientInGrayScale = true;\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"PBRBaseSimpleMaterial\";\r\n    }\r\n}\r\n"]}