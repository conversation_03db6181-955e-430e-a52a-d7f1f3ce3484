{"version": 3, "file": "pointerPickingConfiguration.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Inputs/pointerPickingConfiguration.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,OAAO,2BAA2B;IAAxC;QAeI;;WAEG;QACI,yBAAoB,GAAG,KAAK,CAAC;QACpC;;WAEG;QACI,uBAAkB,GAAG,KAAK,CAAC;QAElC;;WAEG;QACI,yBAAoB,GAAG,KAAK,CAAC;QAEpC;;WAEG;QACI,2BAAsB,GAAG,KAAK,CAAC;QAEtC;;WAEG;QACI,2BAAsB,GAAG,KAAK,CAAC;QAEtC;;WAEG;QACI,yBAAoB,GAAG,KAAK,CAAC;IACxC,CAAC;CAAA", "sourcesContent": ["import type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\n\r\n/**\r\n * Class used to store configuration data associated with pointer picking\r\n */\r\nexport class PointerPickingConfiguration {\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer down event\r\n     */\r\n    public pointerDownPredicate: (Mesh: AbstractMesh) => boolean;\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer up event\r\n     */\r\n    public pointerUpPredicate: (Mesh: AbstractMesh) => boolean;\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer move event\r\n     */\r\n    public pointerMovePredicate: (Mesh: AbstractMesh) => boolean;\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer down event\r\n     */\r\n    public pointerDownFastCheck = false;\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer up event\r\n     */\r\n    public pointerUpFastCheck = false;\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer move event\r\n     */\r\n    public pointerMoveFastCheck = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the user want to entirely skip the picking phase when a pointer move event occurs.\r\n     */\r\n    public skipPointerMovePicking = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the user want to entirely skip the picking phase when a pointer down event occurs.\r\n     */\r\n    public skipPointerDownPicking = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the user want to entirely skip the picking phase when a pointer up event occurs.  Off by default.\r\n     */\r\n    public skipPointerUpPicking = false;\r\n}\r\n"]}