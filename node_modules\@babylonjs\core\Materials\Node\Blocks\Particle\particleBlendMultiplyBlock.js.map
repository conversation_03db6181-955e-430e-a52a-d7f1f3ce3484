{"version": 3, "file": "particleBlendMultiplyBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Particle/particleBlendMultiplyBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D;;GAEG;AACH,MAAM,OAAO,0BAA2B,SAAQ,iBAAiB;IAC7D;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,MAAM,EAAE,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACpH,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,qCAAqC,CAAC,KAAK,EAAE,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC1H,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,qCAAqC,CAAC,KAAK,EAAE,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAExH,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;IACvH,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,4BAA4B,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,KAA6B;QAC3C,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,EAAE;YAClD,OAAO;SACV;QAED,KAAK,CAAC,iBAAiB,IAAI;;kBAEjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;sCACvB,IAAI,CAAC,UAAU,CAAC,sBAAsB,MAAM,IAAI,CAAC,YAAY,CAAC,sBAAsB;kBACxG,IAAI,CAAC,UAAU,CAAC,sBAAsB,UAAU,IAAI,CAAC,KAAK,CAAC,sBAAsB;kBACjF,IAAI,CAAC,UAAU,CAAC,sBAAsB,QAAQ,IAAI,CAAC,KAAK,CAAC,sBAAsB;;kBAE/E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,sBAAsB;;SAE3F,CAAC;QAEF,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,oCAAoC,EAAE,0BAA0B,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\n\r\n/**\r\n * Block used for the particle blend multiply section\r\n */\r\nexport class ParticleBlendMultiplyBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new ParticleBlendMultiplyBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"color\", NodeMaterialBlockConnectionPointTypes.Color4, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"alphaTexture\", NodeMaterialBlockConnectionPointTypes.Float, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"alphaColor\", NodeMaterialBlockConnectionPointTypes.Float, false, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\"blendColor\", NodeMaterialBlockConnectionPointTypes.Color4, NodeMaterialBlockTargets.Fragment);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"ParticleBlendMultiplyBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the color input component\r\n     */\r\n    public get color(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the alphaTexture input component\r\n     */\r\n    public get alphaTexture(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the alphaColor input component\r\n     */\r\n    public get alphaColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the blendColor output component\r\n     */\r\n    public get blendColor(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"sourceAlpha\");\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Vertex) {\r\n            return;\r\n        }\r\n\r\n        state.compilationString += `\r\n            #ifdef BLENDMULTIPLYMODE\r\n                ${this._declareOutput(this.blendColor, state)};\r\n                float sourceAlpha = ${this.alphaColor.associatedVariableName} * ${this.alphaTexture.associatedVariableName};\r\n                ${this.blendColor.associatedVariableName}.rgb = ${this.color.associatedVariableName}.rgb * sourceAlpha + vec3(1.0) * (1.0 - sourceAlpha);\r\n                ${this.blendColor.associatedVariableName}.a = ${this.color.associatedVariableName}.a;\r\n            #else\r\n                ${this._declareOutput(this.blendColor, state)} = ${this.color.associatedVariableName};\r\n            #endif\r\n        `;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ParticleBlendMultiplyBlock\", ParticleBlendMultiplyBlock);\r\n"]}