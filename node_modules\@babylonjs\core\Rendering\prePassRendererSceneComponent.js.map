{"version": 3, "file": "prePassRendererSceneComponent.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Rendering/prePassRendererSceneComponent.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AAEjC,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AA2CxC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,iBAAiB,EAAE;IACtD,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IACD,GAAG,EAAE,UAAuB,KAAgC;QACxD,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE;YAC5B,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;SACjC;IACL,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,KAAK,CAAC,SAAS,CAAC,qBAAqB,GAAG;IACpC,IAAI,IAAI,CAAC,gBAAgB,EAAE;QACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAChC;IAED,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;IAElD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE;QACpC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,MAAM,CAAC,KAAK,CAAC,0CAA0C,GAAG,iFAAiF,GAAG,0BAA0B,CAAC,CAAC;KAC7K;IAED,OAAO,IAAI,CAAC,gBAAgB,CAAC;AACjC,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,CAAC,sBAAsB,GAAG;IACrC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QACxB,OAAO;KACV;IAED,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;IAChC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACjC,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,OAAO,6BAA6B;IAWtC;;;OAGG;IACH,YAAY,KAAY;QAdxB;;WAEG;QACa,SAAI,GAAG,uBAAuB,CAAC,oBAAoB,CAAC;QAYhE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,YAAY,CAAC,uBAAuB,CAAC,6BAA6B,EAAE,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACpI,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,YAAY,CAAC,uBAAuB,CAAC,4BAA4B,EAAE,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACjI,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,YAAY,CAAC,uBAAuB,CAAC,mCAAmC,EAAE,IAAI,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACtJ,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,YAAY,CAAC,uBAAuB,CAAC,4BAA4B,EAAE,IAAI,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAE7I,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,uBAAuB,CAAC,wBAAwB,EAAE,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC1H,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,YAAY,CAAC,uBAAuB,CAAC,oCAAoC,EAAE,IAAI,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAE9J,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,YAAY,CAAC,uBAAuB,CAAC,gCAAgC,EAAE,IAAI,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAClJ,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,YAAY,CAAC,uBAAuB,CAAC,+BAA+B,EAAE,IAAI,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACnJ,CAAC;IAEO,uBAAuB,CAAC,YAAiC,EAAE,SAAkB,EAAE,KAAc;QACjG,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE;YAC/D,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,gBAAgB,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;YAC/E,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;SACvE;IACL,CAAC;IAEO,sBAAsB,CAAC,YAAiC,EAAE,SAAkB,EAAE,KAAc;QAChG,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE;YAC/D,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,UAAU,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SAC3D;IACL,CAAC;IAEO,6BAA6B,CAAC,YAAiC;QACnE,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE;YAC/D,IAAI,CAAC,YAAY,CAAC,oBAAoB,EAAE;gBACpC,YAAY,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,mBAAmB,CAAC,YAAY,CAAC,IAAI,GAAG,aAAa,EAAE,YAAY,CAAC,CAAC;aACvI;YACD,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,gBAAgB,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;YAC/E,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;SACvC;IACL,CAAC;IAEO,iBAAiB,CAAC,MAAc;QACpC,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAClD,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SAClD;IACL,CAAC;IAEO,gBAAgB;QACpB,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;SAC3C;IACL,CAAC;IAEO,iBAAiB;QACrB,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAClD,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;SACvC;IACL,CAAC;IAEO,yBAAyB,CAAC,IAAkB,EAAE,OAAgB,EAAE,KAAsB,EAAE,MAAwB;QACpH,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QAED,gBAAgB;QAChB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,KAAK,CAAC,eAAe,EAAE;YACvB,KAAK,CAAC,eAAe,CAAC,wBAAwB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SACnE;IACL,CAAC;IAEO,wBAAwB,CAAC,IAAkB;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,KAAK,CAAC,eAAe,EAAE;YACvB,KAAK,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC;SAC9C;IACL,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,mCAAmC;IACvC,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;IACxC,CAAC;CACJ;AAED,eAAe,CAAC,6BAA6B,GAAG,CAAC,KAAY,EAAE,EAAE;IAC7D,gDAAgD;IAChD,IAAI,SAAS,GAAG,KAAK,CAAC,aAAa,CAAC,uBAAuB,CAAC,oBAAoB,CAAkC,CAAC;IACnH,IAAI,CAAC,SAAS,EAAE;QACZ,SAAS,GAAG,IAAI,6BAA6B,CAAC,KAAK,CAAC,CAAC;QACrD,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;KAClC;AACL,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Scene } from \"../scene\";\r\nimport type { ISceneComponent } from \"../sceneComponent\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport { PrePassRenderer } from \"./prePassRenderer\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { _InstancesBatch } from \"../Meshes/mesh\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport type { PrePassRenderTarget } from \"../Materials/Textures/prePassRenderTarget\";\r\n\r\ndeclare module \"../abstractScene\" {\r\n    export interface AbstractScene {\r\n        /** @internal (Backing field) */\r\n        _prePassRenderer: Nullable<PrePassRenderer>;\r\n\r\n        /**\r\n         * Gets or Sets the current prepass renderer associated to the scene.\r\n         */\r\n        prePassRenderer: Nullable<PrePassRenderer>;\r\n\r\n        /**\r\n         * Enables the prepass and associates it with the scene\r\n         * @returns the PrePassRenderer\r\n         */\r\n        enablePrePassRenderer(): Nullable<PrePassRenderer>;\r\n\r\n        /**\r\n         * Disables the prepass associated with the scene\r\n         */\r\n        disablePrePassRenderer(): void;\r\n    }\r\n}\r\n\r\ndeclare module \"../Materials/Textures/renderTargetTexture\" {\r\n    export interface RenderTargetTexture {\r\n        /**\r\n         * Gets or sets a boolean indicating that the prepass renderer should not be used with this render target\r\n         */\r\n        noPrePassRenderer: boolean;\r\n        /** @internal */\r\n        _prePassRenderTarget: Nullable<PrePassRenderTarget>;\r\n    }\r\n}\r\n\r\nObject.defineProperty(Scene.prototype, \"prePassRenderer\", {\r\n    get: function (this: Scene) {\r\n        return this._prePassRenderer;\r\n    },\r\n    set: function (this: Scene, value: Nullable<PrePassRenderer>) {\r\n        if (value && value.isSupported) {\r\n            this._prePassRenderer = value;\r\n        }\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nScene.prototype.enablePrePassRenderer = function (): Nullable<PrePassRenderer> {\r\n    if (this._prePassRenderer) {\r\n        return this._prePassRenderer;\r\n    }\r\n\r\n    this._prePassRenderer = new PrePassRenderer(this);\r\n\r\n    if (!this._prePassRenderer.isSupported) {\r\n        this._prePassRenderer = null;\r\n        Logger.Error(\"PrePassRenderer needs WebGL 2 support.\\n\" + \"Maybe you tried to use the following features that need the PrePassRenderer :\\n\" + \" + Subsurface Scattering\");\r\n    }\r\n\r\n    return this._prePassRenderer;\r\n};\r\n\r\nScene.prototype.disablePrePassRenderer = function (): void {\r\n    if (!this._prePassRenderer) {\r\n        return;\r\n    }\r\n\r\n    this._prePassRenderer.dispose();\r\n    this._prePassRenderer = null;\r\n};\r\n\r\n/**\r\n * Defines the Geometry Buffer scene component responsible to manage a G-Buffer useful\r\n * in several rendering techniques.\r\n */\r\nexport class PrePassRendererSceneComponent implements ISceneComponent {\r\n    /**\r\n     * The component name helpful to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_PREPASSRENDERER;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene: Scene) {\r\n        this.scene = scene;\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {\r\n        this.scene._beforeCameraDrawStage.registerStep(SceneComponentConstants.STEP_BEFORECAMERADRAW_PREPASS, this, this._beforeCameraDraw);\r\n        this.scene._afterCameraDrawStage.registerStep(SceneComponentConstants.STEP_AFTERCAMERADRAW_PREPASS, this, this._afterCameraDraw);\r\n        this.scene._beforeRenderTargetDrawStage.registerStep(SceneComponentConstants.STEP_BEFORERENDERTARGETDRAW_PREPASS, this, this._beforeRenderTargetDraw);\r\n        this.scene._afterRenderTargetDrawStage.registerStep(SceneComponentConstants.STEP_AFTERCAMERADRAW_PREPASS, this, this._afterRenderTargetDraw);\r\n\r\n        this.scene._beforeClearStage.registerStep(SceneComponentConstants.STEP_BEFORECLEAR_PREPASS, this, this._beforeClearStage);\r\n        this.scene._beforeRenderTargetClearStage.registerStep(SceneComponentConstants.STEP_BEFORERENDERTARGETCLEAR_PREPASS, this, this._beforeRenderTargetClearStage);\r\n\r\n        this.scene._beforeRenderingMeshStage.registerStep(SceneComponentConstants.STEP_BEFORERENDERINGMESH_PREPASS, this, this._beforeRenderingMeshStage);\r\n        this.scene._afterRenderingMeshStage.registerStep(SceneComponentConstants.STEP_AFTERRENDERINGMESH_PREPASS, this, this._afterRenderingMeshStage);\r\n    }\r\n\r\n    private _beforeRenderTargetDraw(renderTarget: RenderTargetTexture, faceIndex?: number, layer?: number) {\r\n        if (this.scene.prePassRenderer && !renderTarget.noPrePassRenderer) {\r\n            this.scene.prePassRenderer._setRenderTarget(renderTarget._prePassRenderTarget);\r\n            this.scene.prePassRenderer._beforeDraw(undefined, faceIndex, layer);\r\n        }\r\n    }\r\n\r\n    private _afterRenderTargetDraw(renderTarget: RenderTargetTexture, faceIndex?: number, layer?: number) {\r\n        if (this.scene.prePassRenderer && !renderTarget.noPrePassRenderer) {\r\n            this.scene.prePassRenderer._afterDraw(faceIndex, layer);\r\n        }\r\n    }\r\n\r\n    private _beforeRenderTargetClearStage(renderTarget: RenderTargetTexture) {\r\n        if (this.scene.prePassRenderer && !renderTarget.noPrePassRenderer) {\r\n            if (!renderTarget._prePassRenderTarget) {\r\n                renderTarget._prePassRenderTarget = this.scene.prePassRenderer._createRenderTarget(renderTarget.name + \"_prePassRTT\", renderTarget);\r\n            }\r\n            this.scene.prePassRenderer._setRenderTarget(renderTarget._prePassRenderTarget);\r\n            this.scene.prePassRenderer._clear();\r\n        }\r\n    }\r\n\r\n    private _beforeCameraDraw(camera: Camera) {\r\n        if (this.scene.prePassRenderer) {\r\n            this.scene.prePassRenderer._setRenderTarget(null);\r\n            this.scene.prePassRenderer._beforeDraw(camera);\r\n        }\r\n    }\r\n\r\n    private _afterCameraDraw() {\r\n        if (this.scene.prePassRenderer) {\r\n            this.scene.prePassRenderer._afterDraw();\r\n        }\r\n    }\r\n\r\n    private _beforeClearStage() {\r\n        if (this.scene.prePassRenderer) {\r\n            this.scene.prePassRenderer._setRenderTarget(null);\r\n            this.scene.prePassRenderer._clear();\r\n        }\r\n    }\r\n\r\n    private _beforeRenderingMeshStage(mesh: AbstractMesh, subMesh: SubMesh, batch: _InstancesBatch, effect: Nullable<Effect>) {\r\n        if (!effect) {\r\n            return;\r\n        }\r\n\r\n        // Render to MRT\r\n        const scene = mesh.getScene();\r\n        if (scene.prePassRenderer) {\r\n            scene.prePassRenderer.bindAttachmentsForEffect(effect, subMesh);\r\n        }\r\n    }\r\n\r\n    private _afterRenderingMeshStage(mesh: AbstractMesh) {\r\n        const scene = mesh.getScene();\r\n\r\n        if (scene.prePassRenderer) {\r\n            scene.prePassRenderer.restoreAttachments();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        // Nothing to do for this component\r\n    }\r\n\r\n    /**\r\n     * Disposes the component and the associated resources\r\n     */\r\n    public dispose(): void {\r\n        this.scene.disablePrePassRenderer();\r\n    }\r\n}\r\n\r\nPrePassRenderer._SceneComponentInitialization = (scene: Scene) => {\r\n    // Register the G Buffer component to the scene.\r\n    let component = scene._getComponent(SceneComponentConstants.NAME_PREPASSRENDERER) as PrePassRendererSceneComponent;\r\n    if (!component) {\r\n        component = new PrePassRendererSceneComponent(scene);\r\n        scene._addComponent(component);\r\n    }\r\n};\r\n"]}