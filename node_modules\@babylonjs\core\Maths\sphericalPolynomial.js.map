{"version": 3, "file": "sphericalPolynomial.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Maths/sphericalPolynomial.ts"], "names": [], "mappings": "AAAA,yDAAyD;AACzD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAG/C,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AAEpC,+EAA+E;AAC/E,gFAAgF;AAChF,sDAAsD;AACtD,yDAAyD;AACzD,kDAAkD;AAClD,oDAAoD;AACpD,8EAA8E;AAC9E,qCAAqC;AACrC,2EAA2E;AAE3E,uBAAuB;AACvB,yBAAyB;AACzB,kDAAkD;AAClD,yBAAyB;AACzB,EAAE;AACF,2BAA2B;AAC3B,kDAAkD;AAClD,yBAAyB;AACzB,EAAE;AACF,eAAe;AACf,8BAA8B;AAC9B,eAAe;AACf,EAAE;AACF,8BAA8B;AAC9B,+BAA+B;AAC/B,6BAA6B;AAC7B,EAAE;AACF,sBAAsB;AACtB,EAAE;AACF,aAAa;AACb,aAAa;AACb,yCAAyC;AACzC,mDAAmD;AACnD,mDAAmD;AACnD,gCAAgC;AAChC,6EAA6E;AAC7E,MAAM,oBAAoB,GAAG;IACzB,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;IAE5B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7B,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;IAE7B,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9B,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9B,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM;CACzC,CAAC;AAEF,oBAAoB;AACpB,oBAAoB;AACpB,iEAAiE;AACjE,0CAA0C;AAC1C,MAAM,6BAA6B,GAAG;IAClC,GAAG,EAAE,CAAC,CAAC;IAEP,CAAC,SAAkB,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC,SAAkB,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC,SAAkB,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;IAEnC,CAAC,SAAkB,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;IACjD,CAAC,SAAkB,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;IACjD,CAAC,SAAkB,EAAE,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC;IACzD,CAAC,SAAkB,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;IACjD,CAAC,SAAkB,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,MAAM;CACxF,CAAC;AAEF,wBAAwB;AACxB,MAAM,QAAQ,GAAG,CAAC,EAAU,EAAE,SAAkB,EAAE,EAAE;IAChD,OAAO,oBAAoB,CAAC,EAAE,CAAC,GAAG,6BAA6B,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC;AACnF,CAAC,CAAC;AAEF,kEAAkE;AAClE,gHAAgH;AAChH,MAAM,sBAAsB,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAEnK;;GAEG;AACH,MAAM,OAAO,kBAAkB;IAA/B;QACI;;WAEG;QACI,cAAS,GAAG,KAAK,CAAC;QAEzB;;WAEG;QACI,QAAG,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAErC;;WAEG;QACI,SAAI,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAEtC;;WAEG;QACI,QAAG,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAErC;;WAEG;QACI,QAAG,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAErC;;WAEG;QACI,SAAI,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAEtC;;WAEG;QACI,SAAI,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAEtC;;WAEG;QACI,QAAG,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAErC;;WAEG;QACI,QAAG,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAErC;;WAEG;QACI,QAAG,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;IAiMzC,CAAC;IA/LG;;;;;OAKG;IACI,QAAQ,CAAC,SAAkB,EAAE,KAAa,EAAE,eAAuB;QACtE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QACrD,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChC,WAAW,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QAE3C,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3C,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3C,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,KAAa;QAC7B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;;;;;;;;;OAUG;IACI,mCAAmC;QACtC,oBAAoB;QACpB,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjD,kBAAkB;QAClB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjD,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;;;OAQG;IACI,qCAAqC;QACxC,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;QAEjC,wIAAwI;QACxI,mEAAmE;IACvE,CAAC;IAED;;;;;;OAMG;IACI,oBAAoB;QACvB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/C,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/C,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,IAAkC;QACrD,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,IAAuB;QAChD,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7D,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9D,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7D,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/D,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAChE,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAChE,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,SAAS,CAAC,IAAkC;QACtD,MAAM,EAAE,GAAG,IAAI,kBAAkB,EAAE,CAAC;QACpC,OAAO,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,uBAAuB;IACvB;;;;OAIG;IACI,MAAM,CAAC,cAAc,CAAC,UAA+B;QACxD,MAAM,MAAM,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAExC,MAAM,CAAC,GAAG,GAAG,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;QACjH,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC3C,MAAM,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC1C,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC3C,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC3C,MAAM,CAAC,GAAG,GAAG,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1H,MAAM,CAAC,GAAG,GAAG,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,GAAG,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAEjF,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5B,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAE7B,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,mBAAmB;IAAhC;QAgBI;;WAEG;QACI,MAAC,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAEnC;;WAEG;QACI,MAAC,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAEnC;;WAEG;QACI,MAAC,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAEnC;;WAEG;QACI,OAAE,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpC;;WAEG;QACI,OAAE,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpC;;WAEG;QACI,OAAE,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpC;;WAEG;QACI,OAAE,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpC;;WAEG;QACI,OAAE,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpC;;WAEG;QACI,OAAE,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;IA+FxC,CAAC;IAvJG;;OAEG;IACH,IAAW,kBAAkB;QACzB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SAC7D;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;SAC1C;QACD,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IA+CD;;;OAGG;IACI,UAAU,CAAC,KAAa;QAC3B,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QAChE,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAChC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAChC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,KAAa;QAC7B,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,SAA6B;QACpD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAE5B,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAE7B,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACrE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACrE,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACxG,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7G,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACrE,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjE,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE/B,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;QAEjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,aAAa,CAAC,SAA6B;QACrD,MAAM,MAAM,GAAG,IAAI,mBAAmB,EAAE,CAAC;QACzC,OAAO,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;IACjD,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,SAAS,CAAC,IAAkC;QACtD,MAAM,EAAE,GAAG,IAAI,mBAAmB,EAAE,CAAC;QACrC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACzC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACzC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACzC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1C,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1C,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1C,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1C,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1C,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1C,OAAO,EAAE,CAAC;IACd,CAAC;CACJ", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Color3 } from \"../Maths/math.color\";\r\nimport { TmpVectors } from \"./math\";\r\n\r\n// https://dickyjim.wordpress.com/2013/09/04/spherical-harmonics-for-beginners/\r\n// http://silviojemma.com/public/papers/lighting/spherical-harmonic-lighting.pdf\r\n// https://www.ppsloan.org/publications/StupidSH36.pdf\r\n// http://cseweb.ucsd.edu/~ravir/papers/envmap/envmap.pdf\r\n// https://www.ppsloan.org/publications/SHJCGT.pdf\r\n// https://www.ppsloan.org/publications/shdering.pdf\r\n// https://google.github.io/filament/Filament.md.html#annex/sphericalharmonics\r\n// https://patapom.com/blog/SHPortal/\r\n// https://imdoingitwrong.wordpress.com/2011/04/14/spherical-harmonics-wtf/\r\n\r\n// Using real SH basis:\r\n//  m>0             m   m\r\n// y   = sqrt(2) * K * P * cos(m*phi) * cos(theta)\r\n//  l               l   l\r\n//\r\n//  m<0             m   |m|\r\n// y   = sqrt(2) * K * P * sin(m*phi) * cos(theta)\r\n//  l               l   l\r\n//\r\n//  m=0   0   0\r\n// y   = K * P * trigono terms\r\n//  l     l   l\r\n//\r\n//  m       (2l + 1)(l - |m|)!\r\n// K = sqrt(------------------)\r\n//  l           4pi(l + |m|)!\r\n//\r\n// and P by recursion:\r\n//\r\n// P00(x) = 1\r\n// P01(x) = x\r\n// Pll(x) = (-1^l)(2l - 1)!!(1-x*x)^(1/2)\r\n//          ((2l - 1)x[Pl-1/m]-(l + m - 1)[Pl-2/m])\r\n// Plm(x) = ---------------------------------------\r\n//                         l - m\r\n// Leaving the trigonometric terms aside we can precompute the constants to :\r\nconst SH3ylmBasisConstants = [\r\n    Math.sqrt(1 / (4 * Math.PI)), // l00\r\n\r\n    -Math.sqrt(3 / (4 * Math.PI)), // l1_1\r\n    Math.sqrt(3 / (4 * Math.PI)), // l10\r\n    -Math.sqrt(3 / (4 * Math.PI)), // l11\r\n\r\n    Math.sqrt(15 / (4 * Math.PI)), // l2_2\r\n    -Math.sqrt(15 / (4 * Math.PI)), // l2_1\r\n    Math.sqrt(5 / (16 * Math.PI)), // l20\r\n    -Math.sqrt(15 / (4 * Math.PI)), // l21\r\n    Math.sqrt(15 / (16 * Math.PI)), // l22\r\n];\r\n\r\n// cm = cos(m * phi)\r\n// sm = sin(m * phi)\r\n// {x,y,z} = {cos(phi)sin(theta), sin(phi)sin(theta), cos(theta)}\r\n// By recursion on using trigo identities:\r\nconst SH3ylmBasisTrigonometricTerms = [\r\n    () => 1, // l00\r\n\r\n    (direction: Vector3) => direction.y, // l1_1\r\n    (direction: Vector3) => direction.z, // l10\r\n    (direction: Vector3) => direction.x, // l11\r\n\r\n    (direction: Vector3) => direction.x * direction.y, // l2_2\r\n    (direction: Vector3) => direction.y * direction.z, // l2_1\r\n    (direction: Vector3) => 3 * direction.z * direction.z - 1, // l20\r\n    (direction: Vector3) => direction.x * direction.z, // l21\r\n    (direction: Vector3) => direction.x * direction.x - direction.y * direction.y, // l22\r\n];\r\n\r\n// Wrap the full compute\r\nconst applySH3 = (lm: number, direction: Vector3) => {\r\n    return SH3ylmBasisConstants[lm] * SH3ylmBasisTrigonometricTerms[lm](direction);\r\n};\r\n\r\n// Derived from the integration of the a kernel convolution to SH.\r\n// Great explanation here: https://patapom.com/blog/SHPortal/#about-distant-radiance-and-irradiance-environments\r\nconst SHCosKernelConvolution = [Math.PI, (2 * Math.PI) / 3, (2 * Math.PI) / 3, (2 * Math.PI) / 3, Math.PI / 4, Math.PI / 4, Math.PI / 4, Math.PI / 4, Math.PI / 4];\r\n\r\n/**\r\n * Class representing spherical harmonics coefficients to the 3rd degree\r\n */\r\nexport class SphericalHarmonics {\r\n    /**\r\n     * Defines whether or not the harmonics have been prescaled for rendering.\r\n     */\r\n    public preScaled = false;\r\n\r\n    /**\r\n     * The l0,0 coefficients of the spherical harmonics\r\n     */\r\n    public l00: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The l1,-1 coefficients of the spherical harmonics\r\n     */\r\n    public l1_1: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The l1,0 coefficients of the spherical harmonics\r\n     */\r\n    public l10: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The l1,1 coefficients of the spherical harmonics\r\n     */\r\n    public l11: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The l2,-2 coefficients of the spherical harmonics\r\n     */\r\n    public l2_2: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The l2,-1 coefficients of the spherical harmonics\r\n     */\r\n    public l2_1: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The l2,0 coefficients of the spherical harmonics\r\n     */\r\n    public l20: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The l2,1 coefficients of the spherical harmonics\r\n     */\r\n    public l21: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The l2,2 coefficients of the spherical harmonics\r\n     */\r\n    public l22: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * Adds a light to the spherical harmonics\r\n     * @param direction the direction of the light\r\n     * @param color the color of the light\r\n     * @param deltaSolidAngle the delta solid angle of the light\r\n     */\r\n    public addLight(direction: Vector3, color: Color3, deltaSolidAngle: number): void {\r\n        TmpVectors.Vector3[0].set(color.r, color.g, color.b);\r\n        const colorVector = TmpVectors.Vector3[0];\r\n        const c = TmpVectors.Vector3[1];\r\n        colorVector.scaleToRef(deltaSolidAngle, c);\r\n\r\n        c.scaleToRef(applySH3(0, direction), TmpVectors.Vector3[2]);\r\n        this.l00.addInPlace(TmpVectors.Vector3[2]);\r\n\r\n        c.scaleToRef(applySH3(1, direction), TmpVectors.Vector3[2]);\r\n        this.l1_1.addInPlace(TmpVectors.Vector3[2]);\r\n        c.scaleToRef(applySH3(2, direction), TmpVectors.Vector3[2]);\r\n        this.l10.addInPlace(TmpVectors.Vector3[2]);\r\n        c.scaleToRef(applySH3(3, direction), TmpVectors.Vector3[2]);\r\n        this.l11.addInPlace(TmpVectors.Vector3[2]);\r\n\r\n        c.scaleToRef(applySH3(4, direction), TmpVectors.Vector3[2]);\r\n        this.l2_2.addInPlace(TmpVectors.Vector3[2]);\r\n        c.scaleToRef(applySH3(5, direction), TmpVectors.Vector3[2]);\r\n        this.l2_1.addInPlace(TmpVectors.Vector3[2]);\r\n        c.scaleToRef(applySH3(6, direction), TmpVectors.Vector3[2]);\r\n        this.l20.addInPlace(TmpVectors.Vector3[2]);\r\n        c.scaleToRef(applySH3(7, direction), TmpVectors.Vector3[2]);\r\n        this.l21.addInPlace(TmpVectors.Vector3[2]);\r\n        c.scaleToRef(applySH3(8, direction), TmpVectors.Vector3[2]);\r\n        this.l22.addInPlace(TmpVectors.Vector3[2]);\r\n    }\r\n\r\n    /**\r\n     * Scales the spherical harmonics by the given amount\r\n     * @param scale the amount to scale\r\n     */\r\n    public scaleInPlace(scale: number): void {\r\n        this.l00.scaleInPlace(scale);\r\n        this.l1_1.scaleInPlace(scale);\r\n        this.l10.scaleInPlace(scale);\r\n        this.l11.scaleInPlace(scale);\r\n        this.l2_2.scaleInPlace(scale);\r\n        this.l2_1.scaleInPlace(scale);\r\n        this.l20.scaleInPlace(scale);\r\n        this.l21.scaleInPlace(scale);\r\n        this.l22.scaleInPlace(scale);\r\n    }\r\n\r\n    /**\r\n     * Convert from incident radiance (Li) to irradiance (E) by applying convolution with the cosine-weighted hemisphere.\r\n     *\r\n     * ```\r\n     * E_lm = A_l * L_lm\r\n     * ```\r\n     *\r\n     * In spherical harmonics this convolution amounts to scaling factors for each frequency band.\r\n     * This corresponds to equation 5 in \"An Efficient Representation for Irradiance Environment Maps\", where\r\n     * the scaling factors are given in equation 9.\r\n     */\r\n    public convertIncidentRadianceToIrradiance(): void {\r\n        // Constant (Band 0)\r\n        this.l00.scaleInPlace(SHCosKernelConvolution[0]);\r\n\r\n        // Linear (Band 1)\r\n        this.l1_1.scaleInPlace(SHCosKernelConvolution[1]);\r\n        this.l10.scaleInPlace(SHCosKernelConvolution[2]);\r\n        this.l11.scaleInPlace(SHCosKernelConvolution[3]);\r\n\r\n        // Quadratic (Band 2)\r\n        this.l2_2.scaleInPlace(SHCosKernelConvolution[4]);\r\n        this.l2_1.scaleInPlace(SHCosKernelConvolution[5]);\r\n        this.l20.scaleInPlace(SHCosKernelConvolution[6]);\r\n        this.l21.scaleInPlace(SHCosKernelConvolution[7]);\r\n        this.l22.scaleInPlace(SHCosKernelConvolution[8]);\r\n    }\r\n\r\n    /**\r\n     * Convert from irradiance to outgoing radiance for Lambertian BDRF, suitable for efficient shader evaluation.\r\n     *\r\n     * ```\r\n     * L = (1/pi) * E * rho\r\n     * ```\r\n     *\r\n     * This is done by an additional scale by 1/pi, so is a fairly trivial operation but important conceptually.\r\n     */\r\n    public convertIrradianceToLambertianRadiance(): void {\r\n        this.scaleInPlace(1.0 / Math.PI);\r\n\r\n        // The resultant SH now represents outgoing radiance, so includes the Lambert 1/pi normalisation factor but without albedo (rho) applied\r\n        // (The pixel shader must apply albedo after texture fetches, etc).\r\n    }\r\n\r\n    /**\r\n     * Integrates the reconstruction coefficients directly in to the SH preventing further\r\n     * required operations at run time.\r\n     *\r\n     * This is simply done by scaling back the SH with Ylm constants parameter.\r\n     * The trigonometric part being applied by the shader at run time.\r\n     */\r\n    public preScaleForRendering(): void {\r\n        this.preScaled = true;\r\n\r\n        this.l00.scaleInPlace(SH3ylmBasisConstants[0]);\r\n\r\n        this.l1_1.scaleInPlace(SH3ylmBasisConstants[1]);\r\n        this.l10.scaleInPlace(SH3ylmBasisConstants[2]);\r\n        this.l11.scaleInPlace(SH3ylmBasisConstants[3]);\r\n\r\n        this.l2_2.scaleInPlace(SH3ylmBasisConstants[4]);\r\n        this.l2_1.scaleInPlace(SH3ylmBasisConstants[5]);\r\n        this.l20.scaleInPlace(SH3ylmBasisConstants[6]);\r\n        this.l21.scaleInPlace(SH3ylmBasisConstants[7]);\r\n        this.l22.scaleInPlace(SH3ylmBasisConstants[8]);\r\n    }\r\n\r\n    /**\r\n     * update the spherical harmonics coefficients from the given array\r\n     * @param data defines the 9x3 coefficients (l00, l1-1, l10, l11, l2-2, l2-1, l20, l21, l22)\r\n     * @returns the spherical harmonics (this)\r\n     */\r\n    public updateFromArray(data: ArrayLike<ArrayLike<number>>): SphericalHarmonics {\r\n        Vector3.FromArrayToRef(data[0], 0, this.l00);\r\n        Vector3.FromArrayToRef(data[1], 0, this.l1_1);\r\n        Vector3.FromArrayToRef(data[2], 0, this.l10);\r\n        Vector3.FromArrayToRef(data[3], 0, this.l11);\r\n        Vector3.FromArrayToRef(data[4], 0, this.l2_2);\r\n        Vector3.FromArrayToRef(data[5], 0, this.l2_1);\r\n        Vector3.FromArrayToRef(data[6], 0, this.l20);\r\n        Vector3.FromArrayToRef(data[7], 0, this.l21);\r\n        Vector3.FromArrayToRef(data[8], 0, this.l22);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * update the spherical harmonics coefficients from the given floats array\r\n     * @param data defines the 9x3 coefficients (l00, l1-1, l10, l11, l2-2, l2-1, l20, l21, l22)\r\n     * @returns the spherical harmonics (this)\r\n     */\r\n    public updateFromFloatsArray(data: ArrayLike<number>): SphericalHarmonics {\r\n        Vector3.FromFloatsToRef(data[0], data[1], data[2], this.l00);\r\n        Vector3.FromFloatsToRef(data[3], data[4], data[5], this.l1_1);\r\n        Vector3.FromFloatsToRef(data[6], data[7], data[8], this.l10);\r\n        Vector3.FromFloatsToRef(data[9], data[10], data[11], this.l11);\r\n        Vector3.FromFloatsToRef(data[12], data[13], data[14], this.l2_2);\r\n        Vector3.FromFloatsToRef(data[15], data[16], data[17], this.l2_1);\r\n        Vector3.FromFloatsToRef(data[18], data[19], data[20], this.l20);\r\n        Vector3.FromFloatsToRef(data[21], data[22], data[23], this.l21);\r\n        Vector3.FromFloatsToRef(data[24], data[25], data[26], this.l22);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Constructs a spherical harmonics from an array.\r\n     * @param data defines the 9x3 coefficients (l00, l1-1, l10, l11, l2-2, l2-1, l20, l21, l22)\r\n     * @returns the spherical harmonics\r\n     */\r\n    public static FromArray(data: ArrayLike<ArrayLike<number>>): SphericalHarmonics {\r\n        const sh = new SphericalHarmonics();\r\n        return sh.updateFromArray(data);\r\n    }\r\n\r\n    // Keep for references.\r\n    /**\r\n     * Gets the spherical harmonics from polynomial\r\n     * @param polynomial the spherical polynomial\r\n     * @returns the spherical harmonics\r\n     */\r\n    public static FromPolynomial(polynomial: SphericalPolynomial): SphericalHarmonics {\r\n        const result = new SphericalHarmonics();\r\n\r\n        result.l00 = polynomial.xx.scale(0.376127).add(polynomial.yy.scale(0.376127)).add(polynomial.zz.scale(0.376126));\r\n        result.l1_1 = polynomial.y.scale(0.977204);\r\n        result.l10 = polynomial.z.scale(0.977204);\r\n        result.l11 = polynomial.x.scale(0.977204);\r\n        result.l2_2 = polynomial.xy.scale(1.16538);\r\n        result.l2_1 = polynomial.yz.scale(1.16538);\r\n        result.l20 = polynomial.zz.scale(1.34567).subtract(polynomial.xx.scale(0.672834)).subtract(polynomial.yy.scale(0.672834));\r\n        result.l21 = polynomial.zx.scale(1.16538);\r\n        result.l22 = polynomial.xx.scale(1.16538).subtract(polynomial.yy.scale(1.16538));\r\n\r\n        result.l1_1.scaleInPlace(-1);\r\n        result.l11.scaleInPlace(-1);\r\n        result.l2_1.scaleInPlace(-1);\r\n        result.l21.scaleInPlace(-1);\r\n\r\n        result.scaleInPlace(Math.PI);\r\n\r\n        return result;\r\n    }\r\n}\r\n\r\n/**\r\n * Class representing spherical polynomial coefficients to the 3rd degree\r\n */\r\nexport class SphericalPolynomial {\r\n    private _harmonics: Nullable<SphericalHarmonics>;\r\n\r\n    /**\r\n     * The spherical harmonics used to create the polynomials.\r\n     */\r\n    public get preScaledHarmonics(): SphericalHarmonics {\r\n        if (!this._harmonics) {\r\n            this._harmonics = SphericalHarmonics.FromPolynomial(this);\r\n        }\r\n        if (!this._harmonics.preScaled) {\r\n            this._harmonics.preScaleForRendering();\r\n        }\r\n        return this._harmonics;\r\n    }\r\n\r\n    /**\r\n     * The x coefficients of the spherical polynomial\r\n     */\r\n    public x: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The y coefficients of the spherical polynomial\r\n     */\r\n    public y: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The z coefficients of the spherical polynomial\r\n     */\r\n    public z: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The xx coefficients of the spherical polynomial\r\n     */\r\n    public xx: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The yy coefficients of the spherical polynomial\r\n     */\r\n    public yy: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The zz coefficients of the spherical polynomial\r\n     */\r\n    public zz: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The xy coefficients of the spherical polynomial\r\n     */\r\n    public xy: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The yz coefficients of the spherical polynomial\r\n     */\r\n    public yz: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * The zx coefficients of the spherical polynomial\r\n     */\r\n    public zx: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * Adds an ambient color to the spherical polynomial\r\n     * @param color the color to add\r\n     */\r\n    public addAmbient(color: Color3): void {\r\n        TmpVectors.Vector3[0].copyFromFloats(color.r, color.g, color.b);\r\n        const colorVector = TmpVectors.Vector3[0];\r\n        this.xx.addInPlace(colorVector);\r\n        this.yy.addInPlace(colorVector);\r\n        this.zz.addInPlace(colorVector);\r\n    }\r\n\r\n    /**\r\n     * Scales the spherical polynomial by the given amount\r\n     * @param scale the amount to scale\r\n     */\r\n    public scaleInPlace(scale: number) {\r\n        this.x.scaleInPlace(scale);\r\n        this.y.scaleInPlace(scale);\r\n        this.z.scaleInPlace(scale);\r\n        this.xx.scaleInPlace(scale);\r\n        this.yy.scaleInPlace(scale);\r\n        this.zz.scaleInPlace(scale);\r\n        this.yz.scaleInPlace(scale);\r\n        this.zx.scaleInPlace(scale);\r\n        this.xy.scaleInPlace(scale);\r\n    }\r\n\r\n    /**\r\n     * Updates the spherical polynomial from harmonics\r\n     * @param harmonics the spherical harmonics\r\n     * @returns the spherical polynomial\r\n     */\r\n    public updateFromHarmonics(harmonics: SphericalHarmonics): SphericalPolynomial {\r\n        this._harmonics = harmonics;\r\n\r\n        this.x.copyFrom(harmonics.l11);\r\n        this.x.scaleInPlace(1.02333).scaleInPlace(-1);\r\n        this.y.copyFrom(harmonics.l1_1);\r\n        this.y.scaleInPlace(1.02333).scaleInPlace(-1);\r\n        this.z.copyFrom(harmonics.l10);\r\n        this.z.scaleInPlace(1.02333);\r\n\r\n        this.xx.copyFrom(harmonics.l00);\r\n        TmpVectors.Vector3[0].copyFrom(harmonics.l20).scaleInPlace(0.247708);\r\n        TmpVectors.Vector3[1].copyFrom(harmonics.l22).scaleInPlace(0.429043);\r\n        this.xx.scaleInPlace(0.886277).subtractInPlace(TmpVectors.Vector3[0]).addInPlace(TmpVectors.Vector3[1]);\r\n        this.yy.copyFrom(harmonics.l00);\r\n        this.yy.scaleInPlace(0.886277).subtractInPlace(TmpVectors.Vector3[0]).subtractInPlace(TmpVectors.Vector3[1]);\r\n        this.zz.copyFrom(harmonics.l00);\r\n        TmpVectors.Vector3[0].copyFrom(harmonics.l20).scaleInPlace(0.495417);\r\n        this.zz.scaleInPlace(0.886277).addInPlace(TmpVectors.Vector3[0]);\r\n\r\n        this.yz.copyFrom(harmonics.l2_1);\r\n        this.yz.scaleInPlace(0.858086).scaleInPlace(-1);\r\n        this.zx.copyFrom(harmonics.l21);\r\n        this.zx.scaleInPlace(0.858086).scaleInPlace(-1);\r\n        this.xy.copyFrom(harmonics.l2_2);\r\n        this.xy.scaleInPlace(0.858086);\r\n\r\n        this.scaleInPlace(1.0 / Math.PI);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets the spherical polynomial from harmonics\r\n     * @param harmonics the spherical harmonics\r\n     * @returns the spherical polynomial\r\n     */\r\n    public static FromHarmonics(harmonics: SphericalHarmonics): SphericalPolynomial {\r\n        const result = new SphericalPolynomial();\r\n        return result.updateFromHarmonics(harmonics);\r\n    }\r\n\r\n    /**\r\n     * Constructs a spherical polynomial from an array.\r\n     * @param data defines the 9x3 coefficients (x, y, z, xx, yy, zz, yz, zx, xy)\r\n     * @returns the spherical polynomial\r\n     */\r\n    public static FromArray(data: ArrayLike<ArrayLike<number>>): SphericalPolynomial {\r\n        const sp = new SphericalPolynomial();\r\n        Vector3.FromArrayToRef(data[0], 0, sp.x);\r\n        Vector3.FromArrayToRef(data[1], 0, sp.y);\r\n        Vector3.FromArrayToRef(data[2], 0, sp.z);\r\n        Vector3.FromArrayToRef(data[3], 0, sp.xx);\r\n        Vector3.FromArrayToRef(data[4], 0, sp.yy);\r\n        Vector3.FromArrayToRef(data[5], 0, sp.zz);\r\n        Vector3.FromArrayToRef(data[6], 0, sp.yz);\r\n        Vector3.FromArrayToRef(data[7], 0, sp.zx);\r\n        Vector3.FromArrayToRef(data[8], 0, sp.xy);\r\n        return sp;\r\n    }\r\n}\r\n"]}