{"version": 3, "file": "vrDeviceOrientationFreeCamera.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/VR/vrDeviceOrientationFreeCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AAChF,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AAErD,IAAI,CAAC,kBAAkB,CAAC,+BAA+B,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACrE,OAAO,GAAG,EAAE,CAAC,IAAI,6BAA6B,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAChF,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,MAAM,OAAO,6BAA8B,SAAQ,uBAAuB;IACtE;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa,EAAE,oBAAoB,GAAG,IAAI,EAAE,kBAAmC,eAAe,CAAC,UAAU,EAAE;QACpJ,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAcvB,gBAAW,GAAG,CAAC,SAAc,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAZtE,eAAe,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QAC5D,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,eAAe,EAAE,eAAe,EAAE,CAAC,CAAC;IACpF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,+BAA+B,CAAC;IAC3C,CAAC;CAGJ", "sourcesContent": ["import { Camera } from \"../../Cameras/camera\";\r\nimport { DeviceOrientationCamera } from \"../../Cameras/deviceOrientationCamera\";\r\nimport { VRCameraMetrics } from \"./vrCameraMetrics\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { Node } from \"../../node\";\r\nimport { setVRRigMode } from \"../RigModes/vrRigMode\";\r\n\r\nNode.AddNodeConstructor(\"VRDeviceOrientationFreeCamera\", (name, scene) => {\r\n    return () => new VRDeviceOrientationFreeCamera(name, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * Camera used to simulate VR rendering (based on FreeCamera)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#vr-device-orientation-cameras\r\n */\r\nexport class VRDeviceOrientationFreeCamera extends DeviceOrientationCamera {\r\n    /**\r\n     * Creates a new VRDeviceOrientationFreeCamera\r\n     * @param name defines camera name\r\n     * @param position defines the start position of the camera\r\n     * @param scene defines the scene the camera belongs to\r\n     * @param compensateDistortion defines if the camera needs to compensate the lens distortion\r\n     * @param vrCameraMetrics defines the vr metrics associated to the camera\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene, compensateDistortion = true, vrCameraMetrics: VRCameraMetrics = VRCameraMetrics.GetDefault()) {\r\n        super(name, position, scene);\r\n\r\n        vrCameraMetrics.compensateDistortion = compensateDistortion;\r\n        this.setCameraRigMode(Camera.RIG_MODE_VR, { vrCameraMetrics: vrCameraMetrics });\r\n    }\r\n\r\n    /**\r\n     * Gets camera class name\r\n     * @returns VRDeviceOrientationFreeCamera\r\n     */\r\n    public getClassName(): string {\r\n        return \"VRDeviceOrientationFreeCamera\";\r\n    }\r\n\r\n    protected _setRigMode = (rigParams: any) => setVRRigMode(this, rigParams);\r\n}\r\n"]}