import { Mesh<PERSON><PERSON>er, StandardMaterial, Color3, Vector3, DynamicTexture, VertexData, Mesh } from '@babylonjs/core';

export class TridomTile {
    constructor(scene, tileData, position = Vector3.Zero()) {
        this.scene = scene;
        this.data = tileData;
        this.position = position;
        this.mesh = null;
        this.material = null;
        this.isSelected = false;
        this.isPlaced = false;
        this.rotation = 0; // 0, 1, or 2 (60-degree increments)

        this.createMesh();
        this.createMaterial();
        this.createNumberTexture();
    }

    createMesh() {
        // Create triangular prism mesh manually
        const size = 1;
        const depth = 0.1;

        // Define triangle vertices (equilateral triangle)
        const positions = [
            // Top face
            -size * 0.5, depth * 0.5, -size * 0.289,  // Bottom left
            size * 0.5, depth * 0.5, -size * 0.289,   // Bottom right
            0, depth * 0.5, size * 0.577,             // Top

            // Bottom face
            -size * 0.5, -depth * 0.5, -size * 0.289, // Bottom left
            size * 0.5, -depth * 0.5, -size * 0.289,  // Bottom right
            0, -depth * 0.5, size * 0.577,            // Top
        ];

        // Define triangle indices
        const indices = [
            // Top face
            0, 1, 2,
            // Bottom face
            3, 5, 4,
            // Side faces
            0, 3, 4, 0, 4, 1,  // Side 1
            1, 4, 5, 1, 5, 2,  // Side 2
            2, 5, 3, 2, 3, 0   // Side 3
        ];

        // Create normals
        const normals = [];
        VertexData.ComputeNormals(positions, indices, normals);

        // Create UVs for texture mapping
        const uvs = [
            // Top face
            0.2, 0.8,  // Bottom left
            0.8, 0.8,  // Bottom right
            0.5, 0.2,  // Top

            // Bottom face
            0.2, 0.8,  // Bottom left
            0.8, 0.8,  // Bottom right
            0.5, 0.2,  // Top
        ];

        // Create vertex data
        const vertexData = new VertexData();
        vertexData.positions = positions;
        vertexData.indices = indices;
        vertexData.normals = normals;
        vertexData.uvs = uvs;

        // Create mesh
        this.mesh = new Mesh(`tile_${this.data.id}`, this.scene);
        vertexData.applyToMesh(this.mesh);

        this.mesh.position = this.position.clone();
        this.mesh.tridomTile = this; // Reference back to this tile

        // Make mesh pickable
        this.mesh.isPickable = true;
    }

    createMaterial() {
        this.material = new StandardMaterial(`tileMat_${this.data.id}`, this.scene);
        this.material.diffuseColor = new Color3(0.9, 0.9, 0.8);
        this.material.specularColor = new Color3(0.1, 0.1, 0.1);

        if (this.data.isTriple) {
            this.material.diffuseColor = new Color3(1, 0.8, 0.2); // Gold for triples
        }

        this.mesh.material = this.material;
    }

    createNumberTexture() {
        // Create texture with numbers on the triangle
        const textureSize = 512;
        const texture = new DynamicTexture(`tileTexture_${this.data.id}`, textureSize, this.scene);

        // Clear texture
        texture.drawText('', null, null, 'bold 60px Arial', 'black', 'transparent', true);

        // Draw numbers at triangle corners
        const numbers = this.data.numbers;
        const ctx = texture.getContext();

        // Set font and style
        ctx.font = 'bold 48px Arial';
        ctx.fillStyle = 'black';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Position numbers at triangle corners
        // Bottom left corner
        ctx.fillText(numbers[0].toString(), textureSize * 0.2, textureSize * 0.8);

        // Bottom right corner
        ctx.fillText(numbers[1].toString(), textureSize * 0.8, textureSize * 0.8);

        // Top corner
        ctx.fillText(numbers[2].toString(), textureSize * 0.5, textureSize * 0.2);

        texture.update();
        this.material.diffuseTexture = texture;
    }

    setSelected(selected) {
        this.isSelected = selected;
        if (selected) {
            this.material.emissiveColor = new Color3(0.2, 0.8, 0.2);
        } else {
            this.material.emissiveColor = new Color3(0, 0, 0);
        }
    }

    setPosition(position) {
        this.position = position;
        if (this.mesh) {
            this.mesh.position = position;
        }
    }

    rotateTile(steps = 1) {
        this.rotation = (this.rotation + steps) % 3;
        if (this.mesh) {
            this.mesh.rotation.y = (this.rotation * Math.PI * 2) / 3;
        }

        // Update numbers array to reflect rotation
        const originalNumbers = [...this.data.numbers];
        for (let i = 0; i < steps; i++) {
            const temp = originalNumbers[0];
            originalNumbers[0] = originalNumbers[2];
            originalNumbers[2] = originalNumbers[1];
            originalNumbers[1] = temp;
        }
        this.data.numbers = originalNumbers;

        // Recreate texture with new number positions
        this.createNumberTexture();
    }

    moveTo(position, duration = 1000) {
        // Animate tile movement
        return new Promise((resolve) => {
            const startPos = this.mesh.position.clone();
            const endPos = position;
            const startTime = Date.now();

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Easing function
                const easeProgress = 1 - Math.pow(1 - progress, 3);

                this.mesh.position = Vector3.Lerp(startPos, endPos, easeProgress);

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    this.position = endPos;
                    resolve();
                }
            };

            animate();
        });
    }

    highlight(color = new Color3(1, 1, 0)) {
        this.material.emissiveColor = color;
    }

    removeHighlight() {
        this.material.emissiveColor = new Color3(0, 0, 0);
    }

    dispose() {
        if (this.mesh) {
            this.mesh.dispose();
        }
        if (this.material) {
            this.material.dispose();
        }
    }

    clone() {
        return new TridomTile(this.scene, { ...this.data }, this.position.clone());
    }

    // Get the numbers on a specific side (0, 1, or 2)
    getSideNumbers(side) {
        return [
            this.data.numbers[side],
            this.data.numbers[(side + 1) % 3]
        ];
    }

    // Check if this tile can match with another tile
    canMatchWith(otherTile) {
        const thisNumbers = this.data.numbers;
        const otherNumbers = otherTile.data.numbers;

        let matches = 0;
        for (let num1 of thisNumbers) {
            if (otherNumbers.includes(num1)) {
                matches++;
            }
        }

        return matches >= 2;
    }
}
