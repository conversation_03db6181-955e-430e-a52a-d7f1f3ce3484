{"version": 3, "file": "postProcessRenderPipeline.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/PostProcesses/RenderPipeline/postProcessRenderPipeline.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AACzC,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAQlD;;;GAGG;AACH,MAAM,OAAO,yBAAyB;IAmBlC;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,wCAAwC;IACxC,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACH,YACY,OAAe,EACvB,IAAY;QADJ,YAAO,GAAP,OAAO,CAAQ;QAGvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,6BAA6B,GAAG,IAAI,KAAK,EAA2B,CAAC;QAE1E,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,2BAA2B,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,cAAc,EAAE;YAChD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,gBAAgB,CAAC,EAAE;gBAC7E,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE;oBACpD,OAAO,KAAK,CAAC;iBAChB;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,YAAqC;QAC5C,IAAI,CAAC,cAAe,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC;IAClE,CAAC;IAED,UAAU;IAEV,gBAAgB;IACT,QAAQ,KAAI,CAAC;IAMpB;;OAEG;IACI,aAAa,CAAC,gBAAwB,EAAE,OAAY;QACvD,MAAM,aAAa,GAAkC,IAAI,CAAC,cAAe,CAAC,gBAAgB,CAAC,CAAC;QAE5F,IAAI,CAAC,aAAa,EAAE;YAChB,OAAO;SACV;QAED,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACrE,CAAC;IAMD;;OAEG;IACI,cAAc,CAAC,gBAAwB,EAAE,OAA2B;QACvE,MAAM,aAAa,GAAkC,IAAI,CAAC,cAAe,CAAC,gBAAgB,CAAC,CAAC;QAE5F,IAAI,CAAC,aAAa,EAAE;YAChB,OAAO;SACV;QAED,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACtE,CAAC;IAMD;;OAEG;IACI,cAAc,CAAC,OAAY,EAAE,MAAe;QAC/C,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEvD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QAED,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAS,CAAC;QACd,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACvB,IAAI,CAAC,MAAM,EAAE;gBACT,SAAS;aACZ;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBACtC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC9B;iBAAM,IAAI,MAAM,EAAE;gBACf,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aAC3B;SACJ;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACtC;QAED,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,cAAc,EAAE;YAChD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,gBAAgB,CAAC,EAAE;gBAC7E,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;aAC9D;SACJ;IACL,CAAC;IAMD;;OAEG;IACI,cAAc,CAAC,OAAY;QAC9B,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEvD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QAED,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,cAAc,EAAE;YAChD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,gBAAgB,CAAC,EAAE;gBAC7E,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;aAC9D;SACJ;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAC3D;IACL,CAAC;IAED,gBAAgB;IACT,OAAO;QACV,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,cAAc,EAAE;YAChD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,gBAAgB,CAAC,EAAE;gBAC7E,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,OAAO,EAAE,CAAC;aACnD;SACJ;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;gBACnB,SAAS;aACZ;YACD,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACzC,IAAU,IAAI,CAAC,6BAA8B,CAAC,UAAU,CAAC,EAAE;gBACjD,IAAI,CAAC,6BAA8B,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,CAAC;aACnE;SACJ;IACL,CAAC;IAED,gBAAgB;IACT,MAAM;QACT,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,6BAA6B,GAAG,IAAI,KAAK,EAA2B,CAAC;IAC9E,CAAC;IAES,6BAA6B,CAAC,WAAmB;QACvD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE;YACrC,OAAO,KAAK,CAAC;SAChB;QAED,+KAA+K;QAC/K,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpD,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC5E,IAAI,aAAa,EAAE;gBACf,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,WAAW,CAAC;aAC1C;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACO,6BAA6B;QACnC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;YAChC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACxE,IAAI,aAAa,EAAE;gBACf,KAAK,MAAM,WAAW,IAAI,aAAa,EAAE;oBACrC,WAAW,CAAC,2BAA2B,GAAG,IAAI,CAAC;iBAClD;aACJ;SACJ;IACL,CAAC;IAED;;;;OAIG;IACH,6DAA6D;IACtD,kBAAkB,CAAC,eAAgC;QACtD,wBAAwB;QACxB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,kCAAkC;IACtC,CAAC;CACJ;AAhPU;IADN,SAAS,EAAE;wDACS", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport type { PostProcessRenderEffect } from \"./postProcessRenderEffect\";\r\nimport type { IInspectable } from \"../../Misc/iInspectable\";\r\n\r\nimport type { PrePassRenderer } from \"../../Rendering/prePassRenderer\";\r\n\r\n/**\r\n * PostProcessRenderPipeline\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/postProcessRenderPipeline\r\n */\r\nexport class PostProcessRenderPipeline {\r\n    protected _renderEffects: { [key: string]: PostProcessRenderEffect };\r\n    protected _renderEffectsForIsolatedPass: PostProcessRenderEffect[];\r\n\r\n    /**\r\n     * List of inspectable custom properties (used by the Inspector)\r\n     * @see https://doc.babylonjs.com/toolsAndResources/inspector#extensibility\r\n     */\r\n    public inspectableCustomProperties: IInspectable[];\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    protected _cameras: Camera[];\r\n\r\n    /** @internal */\r\n    @serialize()\r\n    public _name: string;\r\n\r\n    /**\r\n     * Gets pipeline name\r\n     */\r\n    public get name(): string {\r\n        return this._name;\r\n    }\r\n\r\n    /** Gets the list of attached cameras */\r\n    public get cameras() {\r\n        return this._cameras;\r\n    }\r\n\r\n    /**\r\n     * Initializes a PostProcessRenderPipeline\r\n     * @param _engine engine to add the pipeline to\r\n     * @param name name of the pipeline\r\n     */\r\n    constructor(\r\n        private _engine: Engine,\r\n        name: string\r\n    ) {\r\n        this._name = name;\r\n\r\n        this._renderEffects = {};\r\n        this._renderEffectsForIsolatedPass = new Array<PostProcessRenderEffect>();\r\n\r\n        this._cameras = [];\r\n    }\r\n\r\n    /**\r\n     * Gets the class name\r\n     * @returns \"PostProcessRenderPipeline\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"PostProcessRenderPipeline\";\r\n    }\r\n\r\n    /**\r\n     * If all the render effects in the pipeline are supported\r\n     */\r\n    public get isSupported(): boolean {\r\n        for (const renderEffectName in this._renderEffects) {\r\n            if (Object.prototype.hasOwnProperty.call(this._renderEffects, renderEffectName)) {\r\n                if (!this._renderEffects[renderEffectName].isSupported) {\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Adds an effect to the pipeline\r\n     * @param renderEffect the effect to add\r\n     */\r\n    public addEffect(renderEffect: PostProcessRenderEffect): void {\r\n        (<any>this._renderEffects)[renderEffect._name] = renderEffect;\r\n    }\r\n\r\n    // private\r\n\r\n    /** @internal */\r\n    public _rebuild() {}\r\n\r\n    /** @internal */\r\n    public _enableEffect(renderEffectName: string, cameras: Camera): void;\r\n    /** @internal */\r\n    public _enableEffect(renderEffectName: string, cameras: Camera[]): void;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _enableEffect(renderEffectName: string, cameras: any): void {\r\n        const renderEffects: PostProcessRenderEffect = (<any>this._renderEffects)[renderEffectName];\r\n\r\n        if (!renderEffects) {\r\n            return;\r\n        }\r\n\r\n        renderEffects._enable(Tools.MakeArray(cameras || this._cameras));\r\n    }\r\n\r\n    /** @internal */\r\n    public _disableEffect(renderEffectName: string, cameras: Nullable<Camera[]>): void;\r\n    /** @internal */\r\n    public _disableEffect(renderEffectName: string, cameras: Nullable<Camera[]>): void;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _disableEffect(renderEffectName: string, cameras: Nullable<Camera[]>): void {\r\n        const renderEffects: PostProcessRenderEffect = (<any>this._renderEffects)[renderEffectName];\r\n\r\n        if (!renderEffects) {\r\n            return;\r\n        }\r\n\r\n        renderEffects._disable(Tools.MakeArray(cameras || this._cameras));\r\n    }\r\n\r\n    /** @internal */\r\n    public _attachCameras(cameras: Camera, unique: boolean): void;\r\n    /** @internal */\r\n    public _attachCameras(cameras: Camera[], unique: boolean): void;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _attachCameras(cameras: any, unique: boolean): void {\r\n        const cams = Tools.MakeArray(cameras || this._cameras);\r\n\r\n        if (!cams) {\r\n            return;\r\n        }\r\n\r\n        const indicesToDelete = [];\r\n        let i: number;\r\n        for (i = 0; i < cams.length; i++) {\r\n            const camera = cams[i];\r\n            if (!camera) {\r\n                continue;\r\n            }\r\n\r\n            if (this._cameras.indexOf(camera) === -1) {\r\n                this._cameras.push(camera);\r\n            } else if (unique) {\r\n                indicesToDelete.push(i);\r\n            }\r\n        }\r\n\r\n        for (i = 0; i < indicesToDelete.length; i++) {\r\n            cams.splice(indicesToDelete[i], 1);\r\n        }\r\n\r\n        for (const renderEffectName in this._renderEffects) {\r\n            if (Object.prototype.hasOwnProperty.call(this._renderEffects, renderEffectName)) {\r\n                this._renderEffects[renderEffectName]._attachCameras(cams);\r\n            }\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _detachCameras(cameras: Camera): void;\r\n    /** @internal */\r\n    public _detachCameras(cameras: Nullable<Camera[]>): void;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _detachCameras(cameras: any): void {\r\n        const cams = Tools.MakeArray(cameras || this._cameras);\r\n\r\n        if (!cams) {\r\n            return;\r\n        }\r\n\r\n        for (const renderEffectName in this._renderEffects) {\r\n            if (Object.prototype.hasOwnProperty.call(this._renderEffects, renderEffectName)) {\r\n                this._renderEffects[renderEffectName]._detachCameras(cams);\r\n            }\r\n        }\r\n\r\n        for (let i = 0; i < cams.length; i++) {\r\n            this._cameras.splice(this._cameras.indexOf(cams[i]), 1);\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _update(): void {\r\n        for (const renderEffectName in this._renderEffects) {\r\n            if (Object.prototype.hasOwnProperty.call(this._renderEffects, renderEffectName)) {\r\n                this._renderEffects[renderEffectName]._update();\r\n            }\r\n        }\r\n\r\n        for (let i = 0; i < this._cameras.length; i++) {\r\n            if (!this._cameras[i]) {\r\n                continue;\r\n            }\r\n            const cameraName = this._cameras[i].name;\r\n            if ((<any>this._renderEffectsForIsolatedPass)[cameraName]) {\r\n                (<any>this._renderEffectsForIsolatedPass)[cameraName]._update();\r\n            }\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _reset(): void {\r\n        this._renderEffects = {};\r\n        this._renderEffectsForIsolatedPass = new Array<PostProcessRenderEffect>();\r\n    }\r\n\r\n    protected _enableMSAAOnFirstPostProcess(sampleCount: number): boolean {\r\n        if (!this._engine._features.supportMSAA) {\r\n            return false;\r\n        }\r\n\r\n        // Set samples of the very first post process to 4 to enable native anti-aliasing in browsers that support webGL 2.0 (See: https://github.com/BabylonJS/Babylon.js/issues/3754)\r\n        const effectKeys = Object.keys(this._renderEffects);\r\n        if (effectKeys.length > 0) {\r\n            const postProcesses = this._renderEffects[effectKeys[0]].getPostProcesses();\r\n            if (postProcesses) {\r\n                postProcesses[0].samples = sampleCount;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Ensures that all post processes in the pipeline are the correct size according to the\r\n     * the viewport's required size\r\n     */\r\n    protected _adaptPostProcessesToViewPort(): void {\r\n        const effectKeys = Object.keys(this._renderEffects);\r\n        for (const effectKey of effectKeys) {\r\n            const postProcesses = this._renderEffects[effectKey].getPostProcesses();\r\n            if (postProcesses) {\r\n                for (const postProcess of postProcesses) {\r\n                    postProcess.adaptScaleToCurrentViewport = true;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the required values to the prepass renderer.\r\n     * @param prePassRenderer defines the prepass renderer to setup.\r\n     * @returns true if the pre pass is needed.\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public setPrePassRenderer(prePassRenderer: PrePassRenderer): boolean {\r\n        // Do Nothing by default\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the pipeline\r\n     */\r\n    public dispose() {\r\n        // Must be implemented by children\r\n    }\r\n}\r\n"]}