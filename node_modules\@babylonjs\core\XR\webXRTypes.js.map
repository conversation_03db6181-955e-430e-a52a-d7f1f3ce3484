{"version": 3, "file": "webXRTypes.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/XR/webXRTypes.ts"], "names": [], "mappings": "AAGA;;GAEG;AACH,MAAM,CAAN,IAAY,UAiBX;AAjBD,WAAY,UAAU;IAClB;;OAEG;IACH,yDAAW,CAAA;IACX;;OAEG;IACH,uDAAU,CAAA;IACV;;OAEG;IACH,6CAAK,CAAA;IACL;;OAEG;IACH,qDAAS,CAAA;AACb,CAAC,EAjBW,UAAU,KAAV,UAAU,QAiBrB;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,kBAaX;AAbD,WAAY,kBAAkB;IAC1B;;OAEG;IACH,2EAAY,CAAA;IACZ;;OAEG;IACH,6EAAa,CAAA;IACb;;OAEG;IACH,mEAAQ,CAAA;AACZ,CAAC,EAbW,kBAAkB,KAAlB,kBAAkB,QAa7B", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { IDisposable } from \"../scene\";\r\n\r\n/**\r\n * States of the webXR experience\r\n */\r\nexport enum WebXRState {\r\n    /**\r\n     * Transitioning to being in XR mode\r\n     */\r\n    ENTERING_XR,\r\n    /**\r\n     * Transitioning to non XR mode\r\n     */\r\n    EXITING_XR,\r\n    /**\r\n     * In XR mode and presenting\r\n     */\r\n    IN_XR,\r\n    /**\r\n     * Not entered XR mode\r\n     */\r\n    NOT_IN_XR,\r\n}\r\n\r\n/**\r\n * The state of the XR camera's tracking\r\n */\r\nexport enum WebXRTrackingState {\r\n    /**\r\n     * No transformation received, device is not being tracked\r\n     */\r\n    NOT_TRACKING,\r\n    /**\r\n     * Tracking lost - using emulated position\r\n     */\r\n    TRACKING_LOST,\r\n    /**\r\n     * Transformation tracking works normally\r\n     */\r\n    TRACKING,\r\n}\r\n\r\n/**\r\n * Abstraction of the XR render target\r\n */\r\nexport interface WebXRRenderTarget extends IDisposable {\r\n    /**\r\n     * xrpresent context of the canvas which can be used to display/mirror xr content\r\n     */\r\n    canvasContext: WebGLRenderingContext;\r\n\r\n    /**\r\n     * xr layer for the canvas\r\n     */\r\n    xrLayer: Nullable<XRWebGLLayer>;\r\n\r\n    /**\r\n     * Initializes a XRWebGLLayer to be used as the session's baseLayer.\r\n     * @param xrSession xr session\r\n     * @returns a promise that will resolve once the XR Layer has been created\r\n     */\r\n    initializeXRLayerAsync(xrSession: XRSession): Promise<XRWebGLLayer>;\r\n}\r\n"]}