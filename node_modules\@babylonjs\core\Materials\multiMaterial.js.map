{"version": 3, "file": "multiMaterial.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Materials/multiMaterial.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AACpC,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAElD;;;;GAIG;AACH,MAAM,OAAO,aAAc,SAAQ,QAAQ;IAKvC;;;OAGG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,YAAY,CAAC,KAA2B;QAC/C,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAjC7B,gBAAgB;QACT,kCAA6B,GAAa,EAAE,CAAC;QAkChD,IAAI,CAAC,QAAQ,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,YAAY,GAAG,EAAgB,CAAC;QAErC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,CAAC,mDAAmD;IAC5F,CAAC;IAEO,UAAU,CAAC,KAA2B;QAC1C,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;QAC3B,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,KAA2B,EAAE,EAAE;YAC5C,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAE3C,IAAI,CAAC,gCAAgC,EAAE,CAAC;YAExC,OAAO,MAAM,CAAC;QAClB,CAAC,CAAC;QAEF,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,KAAK,CAAC,MAAM,GAAG,CAAC,KAAa,EAAE,WAAoB,EAAE,EAAE;YACnD,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;YAE7D,IAAI,CAAC,gCAAgC,EAAE,CAAC;YAExC,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC;IACN,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,KAAa;QAC/B,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YAChD,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC;SAC1C;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,OAAO,KAAK,CAAC,iBAAiB,EAAE,CAAC,MAAM,CACnC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACrC,IAAI,WAAW,EAAE;gBACb,OAAO,WAAW,CAAC,iBAAiB,EAAE,CAAC;aAC1C;iBAAM;gBACH,OAAO,EAAE,CAAC;aACb;QACL,CAAC,CAAC,CACL,CAAC;IACN,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,OAAoB;QAClC,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/C,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,EAAE;gBAC3C,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,YAAY;QACf,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG;IACI,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAAE,YAAsB;QACjF,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC7C,IAAI,WAAW,EAAE;gBACb,IAAI,WAAW,CAAC,uBAAuB,EAAE;oBACrC,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE;wBAC7D,OAAO,KAAK,CAAC;qBAChB;oBACD,SAAS;iBACZ;gBAED,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBAC5B,OAAO,KAAK,CAAC;iBAChB;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,IAAY,EAAE,aAAuB;QAC9C,MAAM,gBAAgB,GAAG,IAAI,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAElE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,IAAI,WAAW,GAAuB,IAAI,CAAC;YAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YACzC,IAAI,aAAa,IAAI,OAAO,EAAE;gBAC1B,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;aAC1D;iBAAM;gBACH,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;aAC1C;YACD,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACnD;QAED,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACjC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7C,IAAI,IAAI,EAAE;YACN,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACjD;QACD,mBAAmB,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC5C,mBAAmB,CAAC,SAAS,GAAG,EAAE,CAAC;QAEnC,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;YACpE,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAE3C,IAAI,MAAM,EAAE;gBACR,mBAAmB,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC7D,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;aACjD;iBAAM;gBACH,mBAAmB,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClD,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC5C;SACJ;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,kBAA4B,EAAE,oBAA8B,EAAE,oBAA8B;QACvG,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,IAAI,oBAAoB,EAAE;YACtB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAC7C,IAAI,WAAW,EAAE;oBACb,WAAW,CAAC,OAAO,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;iBACjE;aACJ;SACJ;QAED,MAAM,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,KAAK,IAAI,CAAC,EAAE;YACZ,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACzC;QAED,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,kBAAkB,CAAC,mBAAwB,EAAE,KAAY;QACnE,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAEzE,aAAa,CAAC,EAAE,GAAG,mBAAmB,CAAC,EAAE,CAAC;QAC1C,aAAa,CAAC,eAAe,GAAG,mBAAmB,CAAC,QAAQ,CAAC;QAE7D,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,mBAAmB,CAAC,IAAI,CAAC,CAAC;SAC3D;QAED,IAAI,mBAAmB,CAAC,kBAAkB,EAAE;YACxC,aAAa,CAAC,6BAA6B,GAAG,mBAAmB,CAAC,kBAAkB,CAAC;SACxF;aAAM;YACH,mBAAmB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAgB,EAAE,EAAE,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SACrI;QAED,OAAO,aAAa,CAAC;IACzB,CAAC;CACJ;AAED,aAAa,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport { Material } from \"../Materials/material\";\r\nimport { Tags } from \"../Misc/tags\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\n\r\n/**\r\n * A multi-material is used to apply different materials to different parts of the same object without the need of\r\n * separate meshes. This can be use to improve performances.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/multiMaterials\r\n */\r\nexport class MultiMaterial extends Material {\r\n    private _subMaterials: Nullable<Material>[];\r\n    /** @internal */\r\n    public _waitingSubMaterialsUniqueIds: string[] = [];\r\n\r\n    /**\r\n     * Gets or Sets the list of Materials used within the multi material.\r\n     * They need to be ordered according to the submeshes order in the associated mesh\r\n     */\r\n    public get subMaterials(): Nullable<Material>[] {\r\n        return this._subMaterials;\r\n    }\r\n\r\n    public set subMaterials(value: Nullable<Material>[]) {\r\n        this._subMaterials = value;\r\n        this._hookArray(value);\r\n    }\r\n\r\n    /**\r\n     * Function used to align with Node.getChildren()\r\n     * @returns the list of Materials used within the multi material\r\n     */\r\n    public getChildren(): Nullable<Material>[] {\r\n        return this.subMaterials;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new Multi Material\r\n     * A multi-material is used to apply different materials to different parts of the same object without the need of\r\n     * separate meshes. This can be use to improve performances.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/multiMaterials\r\n     * @param name Define the name in the scene\r\n     * @param scene Define the scene the material belongs to\r\n     */\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene, true);\r\n\r\n        this.getScene().addMultiMaterial(this);\r\n\r\n        this.subMaterials = [] as Material[];\r\n\r\n        this._storeEffectOnSubMeshes = true; // multimaterial is considered like a push material\r\n    }\r\n\r\n    private _hookArray(array: Nullable<Material>[]): void {\r\n        const oldPush = array.push;\r\n        array.push = (...items: Nullable<Material>[]) => {\r\n            const result = oldPush.apply(array, items);\r\n\r\n            this._markAllSubMeshesAsTexturesDirty();\r\n\r\n            return result;\r\n        };\r\n\r\n        const oldSplice = array.splice;\r\n        array.splice = (index: number, deleteCount?: number) => {\r\n            const deleted = oldSplice.apply(array, [index, deleteCount]);\r\n\r\n            this._markAllSubMeshesAsTexturesDirty();\r\n\r\n            return deleted;\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get one of the submaterial by its index in the submaterials array\r\n     * @param index The index to look the sub material at\r\n     * @returns The Material if the index has been defined\r\n     */\r\n    public getSubMaterial(index: number): Nullable<Material> {\r\n        if (index < 0 || index >= this.subMaterials.length) {\r\n            return this.getScene().defaultMaterial;\r\n        }\r\n\r\n        return this.subMaterials[index];\r\n    }\r\n\r\n    /**\r\n     * Get the list of active textures for the whole sub materials list.\r\n     * @returns All the textures that will be used during the rendering\r\n     */\r\n    public getActiveTextures(): BaseTexture[] {\r\n        return super.getActiveTextures().concat(\r\n            ...this.subMaterials.map((subMaterial) => {\r\n                if (subMaterial) {\r\n                    return subMaterial.getActiveTextures();\r\n                } else {\r\n                    return [];\r\n                }\r\n            })\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Specifies if any sub-materials of this multi-material use a given texture.\r\n     * @param texture Defines the texture to check against this multi-material's sub-materials.\r\n     * @returns A boolean specifying if any sub-material of this multi-material uses the texture.\r\n     */\r\n    public hasTexture(texture: BaseTexture): boolean {\r\n        if (super.hasTexture(texture)) {\r\n            return true;\r\n        }\r\n\r\n        for (let i = 0; i < this.subMaterials.length; i++) {\r\n            if (this.subMaterials[i]?.hasTexture(texture)) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name of the material e.g. \"MultiMaterial\"\r\n     * Mainly use in serialization.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"MultiMaterial\";\r\n    }\r\n\r\n    /**\r\n     * Checks if the material is ready to render the requested sub mesh\r\n     * @param mesh Define the mesh the submesh belongs to\r\n     * @param subMesh Define the sub mesh to look readiness for\r\n     * @param useInstances Define whether or not the material is used with instances\r\n     * @returns true if ready, otherwise false\r\n     */\r\n    public isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances?: boolean): boolean {\r\n        for (let index = 0; index < this.subMaterials.length; index++) {\r\n            const subMaterial = this.subMaterials[index];\r\n            if (subMaterial) {\r\n                if (subMaterial._storeEffectOnSubMeshes) {\r\n                    if (!subMaterial.isReadyForSubMesh(mesh, subMesh, useInstances)) {\r\n                        return false;\r\n                    }\r\n                    continue;\r\n                }\r\n\r\n                if (!subMaterial.isReady(mesh)) {\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Clones the current material and its related sub materials\r\n     * @param name Define the name of the newly cloned material\r\n     * @param cloneChildren Define if submaterial will be cloned or shared with the parent instance\r\n     * @returns the cloned material\r\n     */\r\n    public clone(name: string, cloneChildren?: boolean): MultiMaterial {\r\n        const newMultiMaterial = new MultiMaterial(name, this.getScene());\r\n\r\n        for (let index = 0; index < this.subMaterials.length; index++) {\r\n            let subMaterial: Nullable<Material> = null;\r\n            const current = this.subMaterials[index];\r\n            if (cloneChildren && current) {\r\n                subMaterial = current.clone(name + \"-\" + current.name);\r\n            } else {\r\n                subMaterial = this.subMaterials[index];\r\n            }\r\n            newMultiMaterial.subMaterials.push(subMaterial);\r\n        }\r\n\r\n        return newMultiMaterial;\r\n    }\r\n\r\n    /**\r\n     * Serializes the materials into a JSON representation.\r\n     * @returns the JSON representation\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.id = this.id;\r\n        serializationObject.uniqueId = this.uniqueId;\r\n        if (Tags) {\r\n            serializationObject.tags = Tags.GetTags(this);\r\n        }\r\n        serializationObject.materialsUniqueIds = [];\r\n        serializationObject.materials = [];\r\n\r\n        for (let matIndex = 0; matIndex < this.subMaterials.length; matIndex++) {\r\n            const subMat = this.subMaterials[matIndex];\r\n\r\n            if (subMat) {\r\n                serializationObject.materialsUniqueIds.push(subMat.uniqueId);\r\n                serializationObject.materials.push(subMat.id);\r\n            } else {\r\n                serializationObject.materialsUniqueIds.push(null);\r\n                serializationObject.materials.push(null);\r\n            }\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Dispose the material and release its associated resources\r\n     * @param forceDisposeEffect Define if we want to force disposing the associated effect (if false the shader is not released and could be reuse later on)\r\n     * @param forceDisposeTextures Define if we want to force disposing the associated textures (if false, they will not be disposed and can still be use elsewhere in the app)\r\n     * @param forceDisposeChildren Define if we want to force disposing the associated submaterials (if false, they will not be disposed and can still be use elsewhere in the app)\r\n     */\r\n    public dispose(forceDisposeEffect?: boolean, forceDisposeTextures?: boolean, forceDisposeChildren?: boolean): void {\r\n        const scene = this.getScene();\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        if (forceDisposeChildren) {\r\n            for (let index = 0; index < this.subMaterials.length; index++) {\r\n                const subMaterial = this.subMaterials[index];\r\n                if (subMaterial) {\r\n                    subMaterial.dispose(forceDisposeEffect, forceDisposeTextures);\r\n                }\r\n            }\r\n        }\r\n\r\n        const index = scene.multiMaterials.indexOf(this);\r\n        if (index >= 0) {\r\n            scene.multiMaterials.splice(index, 1);\r\n        }\r\n\r\n        super.dispose(forceDisposeEffect, forceDisposeTextures);\r\n    }\r\n\r\n    /**\r\n     * Creates a MultiMaterial from parsed MultiMaterial data.\r\n     * @param parsedMultiMaterial defines parsed MultiMaterial data.\r\n     * @param scene defines the hosting scene\r\n     * @returns a new MultiMaterial\r\n     */\r\n    public static ParseMultiMaterial(parsedMultiMaterial: any, scene: Scene): MultiMaterial {\r\n        const multiMaterial = new MultiMaterial(parsedMultiMaterial.name, scene);\r\n\r\n        multiMaterial.id = parsedMultiMaterial.id;\r\n        multiMaterial._loadedUniqueId = parsedMultiMaterial.uniqueId;\r\n\r\n        if (Tags) {\r\n            Tags.AddTagsTo(multiMaterial, parsedMultiMaterial.tags);\r\n        }\r\n\r\n        if (parsedMultiMaterial.materialsUniqueIds) {\r\n            multiMaterial._waitingSubMaterialsUniqueIds = parsedMultiMaterial.materialsUniqueIds;\r\n        } else {\r\n            parsedMultiMaterial.materials.forEach((subMatId: string) => multiMaterial.subMaterials.push(scene.getLastMaterialById(subMatId)));\r\n        }\r\n\r\n        return multiMaterial;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.MultiMaterial\", MultiMaterial);\r\n"]}