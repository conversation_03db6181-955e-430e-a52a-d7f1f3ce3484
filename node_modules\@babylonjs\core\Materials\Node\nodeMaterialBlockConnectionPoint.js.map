{"version": 3, "file": "nodeMaterialBlockConnectionPoint.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Node/nodeMaterialBlockConnectionPoint.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,+CAA+C,CAAC;AACtG,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAG5E,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAGnD;;GAEG;AACH,MAAM,CAAN,IAAY,8CASX;AATD,WAAY,8CAA8C;IACtD,6BAA6B;IAC7B,+HAAU,CAAA;IACV,qDAAqD;IACrD,2IAAgB,CAAA;IAChB,4EAA4E;IAC5E,+IAAkB,CAAA;IAClB,sEAAsE;IACtE,uIAAc,CAAA;AAClB,CAAC,EATW,8CAA8C,KAA9C,8CAA8C,QASzD;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,oCAKX;AALD,WAAY,oCAAoC;IAC5C,YAAY;IACZ,iGAAK,CAAA;IACL,aAAa;IACb,mGAAM,CAAA;AACV,CAAC,EALW,oCAAoC,KAApC,oCAAoC,QAK/C;AAED;;GAEG;AACH,MAAM,OAAO,2BAA2B;IACpC;;;;;OAKG;IACI,MAAM,CAAC,kBAAkB,CAAC,KAAa,EAAE,KAAa;QACzD,QAAQ,KAAK,EAAE;YACX,KAAK,qCAAqC,CAAC,OAAO,CAAC,CAAC;gBAChD,IAAI,KAAK,KAAK,qCAAqC,CAAC,MAAM,EAAE;oBACxD,OAAO,IAAI,CAAC;iBACf;gBACD,MAAM;aACT;YACD,KAAK,qCAAqC,CAAC,OAAO,CAAC,CAAC;gBAChD,IAAI,KAAK,KAAK,qCAAqC,CAAC,MAAM,EAAE;oBACxD,OAAO,IAAI,CAAC;iBACf;gBACD,MAAM;aACT;YACD,KAAK,qCAAqC,CAAC,MAAM,CAAC,CAAC;gBAC/C,IAAI,KAAK,KAAK,qCAAqC,CAAC,OAAO,EAAE;oBACzD,OAAO,IAAI,CAAC;iBACf;gBACD,MAAM;aACT;YACD,KAAK,qCAAqC,CAAC,MAAM,CAAC,CAAC;gBAC/C,IAAI,KAAK,KAAK,qCAAqC,CAAC,OAAO,EAAE;oBACzD,OAAO,IAAI,CAAC;iBACf;gBACD,MAAM;aACT;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IA4BD,sCAAsC;IACtC,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAoBD;;OAEG;IACH,IAAW,sBAAsB;QAC7B,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;YAC1B,OAAQ,IAAI,CAAC,WAA0B,CAAC,sBAAsB,CAAC;SAClE;QAED,IAAI,CAAC,CAAC,IAAI,CAAC,8BAA8B,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE;YACjG,OAAO,IAAI,CAAC,eAAe,CAAC,sBAAsB,CAAC;SACtD;QAED,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED,IAAW,sBAAsB,CAAC,KAAa;QAC3C,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;IACzC,CAAC;IAED,kFAAkF;IAClF,IAAW,SAAS;QAChB,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE;YAC1E,OAAO,IAAI,CAAC,IAAI,CAAC;SACpB;QACD,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,IAAI,IAAI,CAAC,KAAK,KAAK,qCAAqC,CAAC,UAAU,EAAE;YACjE,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;gBAC1B,OAAQ,IAAI,CAAC,WAA0B,CAAC,IAAI,CAAC;aAChD;YAED,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;aACpC;YAED,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE;gBAC1E,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;aAC5C;SACJ;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,qCAAqC,CAAC,YAAY,EAAE;YACnE,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,IAAI,IAAI,CAAC,2BAA2B,EAAE;oBAC7E,OAAO,IAAI,CAAC,2BAA2B,CAAC;iBAC3C;gBACD,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;aAC1C;iBAAM,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBACzC,OAAO,IAAI,CAAC,2BAA2B,CAAC;aAC3C;SACJ;QAED,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAA4C;QACxD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAqCD,uDAAuD;IACvD,IAAW,MAAM;QACb,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YAC9C,OAAO,IAAI,CAAC,OAAO,CAAC;SACvB;QAED,IAAI,IAAI,CAAC,OAAO,KAAK,wBAAwB,CAAC,iBAAiB,EAAE;YAC7D,OAAO,IAAI,CAAC,OAAO,CAAC;SACvB;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YAC/D,OAAO,wBAAwB,CAAC,QAAQ,CAAC;SAC5C;QAED,OAAO,wBAAwB,CAAC,MAAM,CAAC;IAC3C,CAAC;IAED,IAAW,MAAM,CAAC,KAA+B;QAC7C,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC;IAClF,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,cAAe,CAAC,UAAwB,CAAC;IACzD,CAAC;IAED,oDAAoD;IACpD,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,oDAAoD;IACpD,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,4EAA4E;IAC5E,IAAW,WAAW;QAClB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;IAC3C,CAAC;IAED,2EAA2E;IAC3E,IAAW,eAAe;QACtB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO,EAAE,CAAC;SACb;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;IACpD,CAAC;IAED,2CAA2C;IAC3C,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,wFAAwF;IACxF,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IACzD,CAAC;IAED,oFAAoF;IACpF,IAAW,iCAAiC;QACxC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,KAAK,CAAC;SAChB;QAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,EAAE;gBAChE,OAAO,IAAI,CAAC;aACf;YAED,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,iBAAiB,EAAE;gBAC9I,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,iCAAiC,CAAC,EAAE;oBAC9E,OAAO,IAAI,CAAC;iBACf;aACJ;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,uFAAuF;IACvF,IAAW,yBAAyB;QAChC,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,EAAE;YACjD,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,KAAK,CAAC;SAChB;QAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,EAAE;gBAChE,OAAO,IAAI,CAAC;aACf;YAED,IAAI,QAAQ,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,EAAE;gBACrD,OAAO,IAAI,CAAC;aACf;YAED,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,iBAAiB,EAAE;gBAC9I,IAAI,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,yBAAyB,CAAC,EAAE;oBACtE,OAAO,IAAI,CAAC;iBACf;aACJ;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,yFAAyF;IACzF,IAAW,2BAA2B;QAClC,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACnD,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,KAAK,CAAC;SAChB;QAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;gBAClE,OAAO,IAAI,CAAC;aACf;YAED,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,iBAAiB,EAAE;gBAC9I,IAAI,QAAQ,CAAC,UAAU,CAAC,2BAA2B,EAAE,EAAE;oBACnD,OAAO,IAAI,CAAC;iBACf;aACJ;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,sBAAsB;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACH,YAAmB,IAAY,EAAE,UAA6B,EAAE,SAA+C;QA/T/G,gBAAgB;QACT,oBAAe,GAA0C,IAAI,CAAC;QAE7D,eAAU,GAAG,IAAI,KAAK,EAA+B,CAAC;QAI9D,gBAAgB;QACT,0BAAqB,GAA0C,IAAI,CAAC;QAE3E,gBAAgB;QACT,gCAA2B,GAAoD,IAAI,CAAC;QAE3F,gBAAgB;QACT,4BAAuB,GAA0C,IAAI,CAAC;QAE7E,gBAAgB;QACT,iCAA4B,GAA0C,IAAI,CAAC;QAE1E,UAAK,GAAG,qCAAqC,CAAC,KAAK,CAAC;QAE5D,gBAAgB;QACT,mCAA8B,GAAG,KAAK,CAAC;QAO9C,yGAAyG;QAClG,gCAA2B,GAAY,KAAK,CAAC;QAEpD;;WAEG;QACI,iCAA4B,GAA4C,EAAE,CAAC;QAElF;;WAEG;QACI,iCAA4B,GAA4C,EAAE,CAAC;QAElF;;WAEG;QACI,2BAAsB,GAAG,IAAI,UAAU,EAA+B,CAAC;QAgF9E;;WAEG;QACI,qBAAgB,GAAY,KAAK,CAAC;QAEzC;;WAEG;QACI,wBAAmB,GAAW,CAAC,CAAC,CAAC;QAOxC,gBAAgB;QACT,sBAAiB,GAAG,KAAK,CAAC;QAEzB,YAAO,GAA6B,wBAAwB,CAAC,iBAAiB,CAAC;QAiLnF,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,6BAA6B,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,eAA4C;QAC5D,OAAO,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,KAAK,8CAA8C,CAAC,UAAU,CAAC;IACvH,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,eAA4C;QACvE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QACpC,MAAM,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC;QAE9C,IAAI,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACzD,uCAAuC;YAEvC,IAAI,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,EAAE;gBACvD,OAAO,8CAA8C,CAAC,kBAAkB,CAAC;aAC5E;YAED,KAAK,MAAM,MAAM,IAAI,UAAU,CAAC,OAAO,EAAE;gBACrC,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,IAAI,wBAAwB,CAAC,OAAO,IAAI,MAAM,CAAC,yBAAyB,EAAE;oBAClG,OAAO,8CAA8C,CAAC,kBAAkB,CAAC;iBAC5E;aACJ;SACJ;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,IAAI,eAAe,CAAC,SAAS,KAAK,qCAAqC,CAAC,UAAU,EAAE;YACtH,cAAc;YACd,IAAI,2BAA2B,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,EAAE;gBACjF,OAAO,8CAA8C,CAAC,UAAU,CAAC;aACpE;YAED,iBAAiB;YACjB,IACI,CAAC,eAAe,CAAC,4BAA4B,IAAI,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBACxH,CAAC,eAAe,CAAC,4BAA4B,IAAI,2BAA2B,CAAC,kBAAkB,CAAC,eAAe,CAAC,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,EAChK;gBACE,OAAO,8CAA8C,CAAC,UAAU,CAAC;aACpE;iBAAM;gBACH,OAAO,8CAA8C,CAAC,gBAAgB,CAAC;aAC1E;SACJ;QAED,WAAW;QACX,IAAI,eAAe,CAAC,4BAA4B,IAAI,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACxH,OAAO,8CAA8C,CAAC,gBAAgB,CAAC;SAC1E;QAED,kBAAkB;QAClB,IAAI,WAAW,GAAG,UAAU,CAAC;QAC7B,IAAI,WAAW,GAAG,UAAU,CAAC;QAC7B,IAAI,IAAI,CAAC,SAAS,KAAK,oCAAoC,CAAC,KAAK,EAAE;YAC/D,WAAW,GAAG,UAAU,CAAC;YACzB,WAAW,GAAG,UAAU,CAAC;SAC5B;QAED,IAAI,WAAW,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;YACzC,OAAO,8CAA8C,CAAC,cAAc,CAAC;SACxE;QAED,OAAO,8CAA8C,CAAC,UAAU,CAAC;IACrE,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,eAA4C,EAAE,iBAAiB,GAAG,KAAK;QACpF,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE;YAC3D,4CAA4C;YAC5C,MAAM,sCAAsC,CAAC;SAChD;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtC,eAAe,CAAC,eAAe,GAAG,IAAI,CAAC;QAEvC,IAAI,CAAC,8BAA8B,GAAG,KAAK,CAAC;QAE5C,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;QAC7D,eAAe,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,QAAqC;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACjC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,8BAA8B,GAAG,KAAK,CAAC;QAC5C,QAAQ,CAAC,8BAA8B,GAAG,KAAK,CAAC;QAChD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,0CAA0C,CAAC,IAAY;QAC1D,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,OAAO,OAAO,GAAG,qCAAqC,CAAC,GAAG,EAAE;YACxD,IAAI,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,EAAE;gBACnB,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACnD;YACD,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC;SAC1B;IACL,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,OAAO,GAAG,IAAI;QAC3B,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QAEnD,IAAI,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE;YAChC,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC;YAC1C,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC;YAC5E,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACpE,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC5C,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;SACtE;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,EAAE;YACxD,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC5C,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;SACtE;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;IACxC,CAAC;CACJ", "sourcesContent": ["import { NodeMaterialBlockConnectionPointTypes } from \"./Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport { NodeMaterialBlockTargets } from \"./Enums/nodeMaterialBlockTargets\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { InputBlock } from \"./Blocks/Input/inputBlock\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { NodeMaterialBlock } from \"./nodeMaterialBlock\";\r\n\r\n/**\r\n * Enum used to define the compatibility state between two connection points\r\n */\r\nexport enum NodeMaterialConnectionPointCompatibilityStates {\r\n    /** Points are compatibles */\r\n    Compatible,\r\n    /** Points are incompatible because of their types */\r\n    TypeIncompatible,\r\n    /** Points are incompatible because of their targets (vertex vs fragment) */\r\n    TargetIncompatible,\r\n    /** Points are incompatible because they are in the same hierarchy **/\r\n    HierarchyIssue,\r\n}\r\n\r\n/**\r\n * Defines the direction of a connection point\r\n */\r\nexport enum NodeMaterialConnectionPointDirection {\r\n    /** Input */\r\n    Input,\r\n    /** Output */\r\n    Output,\r\n}\r\n\r\n/**\r\n * Defines a connection point for a block\r\n */\r\nexport class NodeMaterialConnectionPoint {\r\n    /**\r\n     * Checks if two types are equivalent\r\n     * @param type1 type 1 to check\r\n     * @param type2 type 2 to check\r\n     * @returns true if both types are equivalent, else false\r\n     */\r\n    public static AreEquivalentTypes(type1: number, type2: number): boolean {\r\n        switch (type1) {\r\n            case NodeMaterialBlockConnectionPointTypes.Vector3: {\r\n                if (type2 === NodeMaterialBlockConnectionPointTypes.Color3) {\r\n                    return true;\r\n                }\r\n                break;\r\n            }\r\n            case NodeMaterialBlockConnectionPointTypes.Vector4: {\r\n                if (type2 === NodeMaterialBlockConnectionPointTypes.Color4) {\r\n                    return true;\r\n                }\r\n                break;\r\n            }\r\n            case NodeMaterialBlockConnectionPointTypes.Color3: {\r\n                if (type2 === NodeMaterialBlockConnectionPointTypes.Vector3) {\r\n                    return true;\r\n                }\r\n                break;\r\n            }\r\n            case NodeMaterialBlockConnectionPointTypes.Color4: {\r\n                if (type2 === NodeMaterialBlockConnectionPointTypes.Vector4) {\r\n                    return true;\r\n                }\r\n                break;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /** @internal */\r\n    public _ownerBlock: NodeMaterialBlock;\r\n    /** @internal */\r\n    public _connectedPoint: Nullable<NodeMaterialConnectionPoint> = null;\r\n\r\n    private _endpoints = new Array<NodeMaterialConnectionPoint>();\r\n    private _associatedVariableName: string;\r\n    private _direction: NodeMaterialConnectionPointDirection;\r\n\r\n    /** @internal */\r\n    public _typeConnectionSource: Nullable<NodeMaterialConnectionPoint> = null;\r\n\r\n    /** @internal */\r\n    public _defaultConnectionPointType: Nullable<NodeMaterialBlockConnectionPointTypes> = null;\r\n\r\n    /** @internal */\r\n    public _linkedConnectionSource: Nullable<NodeMaterialConnectionPoint> = null;\r\n\r\n    /** @internal */\r\n    public _acceptedConnectionPointType: Nullable<NodeMaterialConnectionPoint> = null;\r\n\r\n    private _type = NodeMaterialBlockConnectionPointTypes.Float;\r\n\r\n    /** @internal */\r\n    public _enforceAssociatedVariableName = false;\r\n\r\n    /** Gets the direction of the point */\r\n    public get direction() {\r\n        return this._direction;\r\n    }\r\n\r\n    /** Indicates that this connection point needs dual validation before being connected to another point */\r\n    public needDualDirectionValidation: boolean = false;\r\n\r\n    /**\r\n     * Gets or sets the additional types supported by this connection point\r\n     */\r\n    public acceptedConnectionPointTypes: NodeMaterialBlockConnectionPointTypes[] = [];\r\n\r\n    /**\r\n     * Gets or sets the additional types excluded by this connection point\r\n     */\r\n    public excludedConnectionPointTypes: NodeMaterialBlockConnectionPointTypes[] = [];\r\n\r\n    /**\r\n     * Observable triggered when this point is connected\r\n     */\r\n    public onConnectionObservable = new Observable<NodeMaterialConnectionPoint>();\r\n\r\n    /**\r\n     * Gets or sets the associated variable name in the shader\r\n     */\r\n    public get associatedVariableName(): string {\r\n        if (this._ownerBlock.isInput) {\r\n            return (this._ownerBlock as InputBlock).associatedVariableName;\r\n        }\r\n\r\n        if ((!this._enforceAssociatedVariableName || !this._associatedVariableName) && this._connectedPoint) {\r\n            return this._connectedPoint.associatedVariableName;\r\n        }\r\n\r\n        return this._associatedVariableName;\r\n    }\r\n\r\n    public set associatedVariableName(value: string) {\r\n        this._associatedVariableName = value;\r\n    }\r\n\r\n    /** Get the inner type (ie AutoDetect for instance instead of the inferred one) */\r\n    public get innerType() {\r\n        if (this._linkedConnectionSource && this._linkedConnectionSource.isConnected) {\r\n            return this.type;\r\n        }\r\n        return this._type;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the connection point type (default is float)\r\n     */\r\n    public get type(): NodeMaterialBlockConnectionPointTypes {\r\n        if (this._type === NodeMaterialBlockConnectionPointTypes.AutoDetect) {\r\n            if (this._ownerBlock.isInput) {\r\n                return (this._ownerBlock as InputBlock).type;\r\n            }\r\n\r\n            if (this._connectedPoint) {\r\n                return this._connectedPoint.type;\r\n            }\r\n\r\n            if (this._linkedConnectionSource && this._linkedConnectionSource.isConnected) {\r\n                return this._linkedConnectionSource.type;\r\n            }\r\n        }\r\n\r\n        if (this._type === NodeMaterialBlockConnectionPointTypes.BasedOnInput) {\r\n            if (this._typeConnectionSource) {\r\n                if (!this._typeConnectionSource.isConnected && this._defaultConnectionPointType) {\r\n                    return this._defaultConnectionPointType;\r\n                }\r\n                return this._typeConnectionSource.type;\r\n            } else if (this._defaultConnectionPointType) {\r\n                return this._defaultConnectionPointType;\r\n            }\r\n        }\r\n\r\n        return this._type;\r\n    }\r\n\r\n    public set type(value: NodeMaterialBlockConnectionPointTypes) {\r\n        this._type = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the connection point name\r\n     */\r\n    public name: string;\r\n\r\n    /**\r\n     * Gets or sets the connection point name\r\n     */\r\n    public displayName: string;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that this connection point can be omitted\r\n     */\r\n    public isOptional: boolean;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that this connection point is exposed on a frame\r\n     */\r\n    public isExposedOnFrame: boolean = false;\r\n\r\n    /**\r\n     * Gets or sets number indicating the position that the port is exposed to on a frame\r\n     */\r\n    public exposedPortPosition: number = -1;\r\n\r\n    /**\r\n     * Gets or sets a string indicating that this uniform must be defined under a #ifdef\r\n     */\r\n    public define: string;\r\n\r\n    /** @internal */\r\n    public _prioritizeVertex = false;\r\n\r\n    private _target: NodeMaterialBlockTargets = NodeMaterialBlockTargets.VertexAndFragment;\r\n\r\n    /** Gets or sets the target of that connection point */\r\n    public get target(): NodeMaterialBlockTargets {\r\n        if (!this._prioritizeVertex || !this._ownerBlock) {\r\n            return this._target;\r\n        }\r\n\r\n        if (this._target !== NodeMaterialBlockTargets.VertexAndFragment) {\r\n            return this._target;\r\n        }\r\n\r\n        if (this._ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            return NodeMaterialBlockTargets.Fragment;\r\n        }\r\n\r\n        return NodeMaterialBlockTargets.Vertex;\r\n    }\r\n\r\n    public set target(value: NodeMaterialBlockTargets) {\r\n        this._target = value;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the current point is connected to another NodeMaterialBlock\r\n     */\r\n    public get isConnected(): boolean {\r\n        return this.connectedPoint !== null || this.hasEndpoints;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the current point is connected to an input block\r\n     */\r\n    public get isConnectedToInputBlock(): boolean {\r\n        return this.connectedPoint !== null && this.connectedPoint.ownerBlock.isInput;\r\n    }\r\n\r\n    /**\r\n     * Gets a the connected input block (if any)\r\n     */\r\n    public get connectInputBlock(): Nullable<InputBlock> {\r\n        if (!this.isConnectedToInputBlock) {\r\n            return null;\r\n        }\r\n\r\n        return this.connectedPoint!.ownerBlock as InputBlock;\r\n    }\r\n\r\n    /** Get the other side of the connection (if any) */\r\n    public get connectedPoint(): Nullable<NodeMaterialConnectionPoint> {\r\n        return this._connectedPoint;\r\n    }\r\n\r\n    /** Get the block that owns this connection point */\r\n    public get ownerBlock(): NodeMaterialBlock {\r\n        return this._ownerBlock;\r\n    }\r\n\r\n    /** Get the block connected on the other side of this connection (if any) */\r\n    public get sourceBlock(): Nullable<NodeMaterialBlock> {\r\n        if (!this._connectedPoint) {\r\n            return null;\r\n        }\r\n\r\n        return this._connectedPoint.ownerBlock;\r\n    }\r\n\r\n    /** Get the block connected on the endpoints of this connection (if any) */\r\n    public get connectedBlocks(): Array<NodeMaterialBlock> {\r\n        if (this._endpoints.length === 0) {\r\n            return [];\r\n        }\r\n\r\n        return this._endpoints.map((e) => e.ownerBlock);\r\n    }\r\n\r\n    /** Gets the list of connected endpoints */\r\n    public get endpoints() {\r\n        return this._endpoints;\r\n    }\r\n\r\n    /** Gets a boolean indicating if that output point is connected to at least one input */\r\n    public get hasEndpoints(): boolean {\r\n        return this._endpoints && this._endpoints.length > 0;\r\n    }\r\n\r\n    /** Gets a boolean indicating that this connection has a path to the vertex output*/\r\n    public get isDirectlyConnectedToVertexOutput(): boolean {\r\n        if (!this.hasEndpoints) {\r\n            return false;\r\n        }\r\n\r\n        for (const endpoint of this._endpoints) {\r\n            if (endpoint.ownerBlock.target === NodeMaterialBlockTargets.Vertex) {\r\n                return true;\r\n            }\r\n\r\n            if (endpoint.ownerBlock.target === NodeMaterialBlockTargets.Neutral || endpoint.ownerBlock.target === NodeMaterialBlockTargets.VertexAndFragment) {\r\n                if (endpoint.ownerBlock.outputs.some((o) => o.isDirectlyConnectedToVertexOutput)) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /** Gets a boolean indicating that this connection will be used in the vertex shader */\r\n    public get isConnectedInVertexShader(): boolean {\r\n        if (this.target === NodeMaterialBlockTargets.Vertex) {\r\n            return true;\r\n        }\r\n\r\n        if (!this.hasEndpoints) {\r\n            return false;\r\n        }\r\n\r\n        for (const endpoint of this._endpoints) {\r\n            if (endpoint.ownerBlock.target === NodeMaterialBlockTargets.Vertex) {\r\n                return true;\r\n            }\r\n\r\n            if (endpoint.target === NodeMaterialBlockTargets.Vertex) {\r\n                return true;\r\n            }\r\n\r\n            if (endpoint.ownerBlock.target === NodeMaterialBlockTargets.Neutral || endpoint.ownerBlock.target === NodeMaterialBlockTargets.VertexAndFragment) {\r\n                if (endpoint.ownerBlock.outputs.some((o) => o.isConnectedInVertexShader)) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /** Gets a boolean indicating that this connection will be used in the fragment shader */\r\n    public get isConnectedInFragmentShader(): boolean {\r\n        if (this.target === NodeMaterialBlockTargets.Fragment) {\r\n            return true;\r\n        }\r\n\r\n        if (!this.hasEndpoints) {\r\n            return false;\r\n        }\r\n\r\n        for (const endpoint of this._endpoints) {\r\n            if (endpoint.ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n                return true;\r\n            }\r\n\r\n            if (endpoint.ownerBlock.target === NodeMaterialBlockTargets.Neutral || endpoint.ownerBlock.target === NodeMaterialBlockTargets.VertexAndFragment) {\r\n                if (endpoint.ownerBlock.isConnectedInFragmentShader()) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Creates a block suitable to be used as an input for this input point.\r\n     * If null is returned, a block based on the point type will be created.\r\n     * @returns The returned string parameter is the name of the output point of NodeMaterialBlock (first parameter of the returned array) that can be connected to the input\r\n     */\r\n    public createCustomInputBlock(): Nullable<[NodeMaterialBlock, string]> {\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Creates a new connection point\r\n     * @param name defines the connection point name\r\n     * @param ownerBlock defines the block hosting this connection point\r\n     * @param direction defines the direction of the connection point\r\n     */\r\n    public constructor(name: string, ownerBlock: NodeMaterialBlock, direction: NodeMaterialConnectionPointDirection) {\r\n        this._ownerBlock = ownerBlock;\r\n        this.name = name;\r\n        this._direction = direction;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name e.g. \"NodeMaterialConnectionPoint\"\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"NodeMaterialConnectionPoint\";\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the current point can be connected to another point\r\n     * @param connectionPoint defines the other connection point\r\n     * @returns a boolean\r\n     */\r\n    public canConnectTo(connectionPoint: NodeMaterialConnectionPoint) {\r\n        return this.checkCompatibilityState(connectionPoint) === NodeMaterialConnectionPointCompatibilityStates.Compatible;\r\n    }\r\n\r\n    /**\r\n     * Gets a number indicating if the current point can be connected to another point\r\n     * @param connectionPoint defines the other connection point\r\n     * @returns a number defining the compatibility state\r\n     */\r\n    public checkCompatibilityState(connectionPoint: NodeMaterialConnectionPoint): NodeMaterialConnectionPointCompatibilityStates {\r\n        const ownerBlock = this._ownerBlock;\r\n        const otherBlock = connectionPoint.ownerBlock;\r\n\r\n        if (ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            // Let's check we are not going reverse\r\n\r\n            if (otherBlock.target === NodeMaterialBlockTargets.Vertex) {\r\n                return NodeMaterialConnectionPointCompatibilityStates.TargetIncompatible;\r\n            }\r\n\r\n            for (const output of otherBlock.outputs) {\r\n                if (output.ownerBlock.target != NodeMaterialBlockTargets.Neutral && output.isConnectedInVertexShader) {\r\n                    return NodeMaterialConnectionPointCompatibilityStates.TargetIncompatible;\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this.type !== connectionPoint.type && connectionPoint.innerType !== NodeMaterialBlockConnectionPointTypes.AutoDetect) {\r\n            // Equivalents\r\n            if (NodeMaterialConnectionPoint.AreEquivalentTypes(this.type, connectionPoint.type)) {\r\n                return NodeMaterialConnectionPointCompatibilityStates.Compatible;\r\n            }\r\n\r\n            // Accepted types\r\n            if (\r\n                (connectionPoint.acceptedConnectionPointTypes && connectionPoint.acceptedConnectionPointTypes.indexOf(this.type) !== -1) ||\r\n                (connectionPoint._acceptedConnectionPointType && NodeMaterialConnectionPoint.AreEquivalentTypes(connectionPoint._acceptedConnectionPointType.type, this.type))\r\n            ) {\r\n                return NodeMaterialConnectionPointCompatibilityStates.Compatible;\r\n            } else {\r\n                return NodeMaterialConnectionPointCompatibilityStates.TypeIncompatible;\r\n            }\r\n        }\r\n\r\n        // Excluded\r\n        if (connectionPoint.excludedConnectionPointTypes && connectionPoint.excludedConnectionPointTypes.indexOf(this.type) !== -1) {\r\n            return NodeMaterialConnectionPointCompatibilityStates.TypeIncompatible;\r\n        }\r\n\r\n        // Check hierarchy\r\n        let targetBlock = otherBlock;\r\n        let sourceBlock = ownerBlock;\r\n        if (this.direction === NodeMaterialConnectionPointDirection.Input) {\r\n            targetBlock = ownerBlock;\r\n            sourceBlock = otherBlock;\r\n        }\r\n\r\n        if (targetBlock.isAnAncestorOf(sourceBlock)) {\r\n            return NodeMaterialConnectionPointCompatibilityStates.HierarchyIssue;\r\n        }\r\n\r\n        return NodeMaterialConnectionPointCompatibilityStates.Compatible;\r\n    }\r\n\r\n    /**\r\n     * Connect this point to another connection point\r\n     * @param connectionPoint defines the other connection point\r\n     * @param ignoreConstraints defines if the system will ignore connection type constraints (default is false)\r\n     * @returns the current connection point\r\n     */\r\n    public connectTo(connectionPoint: NodeMaterialConnectionPoint, ignoreConstraints = false): NodeMaterialConnectionPoint {\r\n        if (!ignoreConstraints && !this.canConnectTo(connectionPoint)) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"Cannot connect these two connectors.\";\r\n        }\r\n\r\n        this._endpoints.push(connectionPoint);\r\n        connectionPoint._connectedPoint = this;\r\n\r\n        this._enforceAssociatedVariableName = false;\r\n\r\n        this.onConnectionObservable.notifyObservers(connectionPoint);\r\n        connectionPoint.onConnectionObservable.notifyObservers(this);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Disconnect this point from one of his endpoint\r\n     * @param endpoint defines the other connection point\r\n     * @returns the current connection point\r\n     */\r\n    public disconnectFrom(endpoint: NodeMaterialConnectionPoint): NodeMaterialConnectionPoint {\r\n        const index = this._endpoints.indexOf(endpoint);\r\n\r\n        if (index === -1) {\r\n            return this;\r\n        }\r\n\r\n        this._endpoints.splice(index, 1);\r\n        endpoint._connectedPoint = null;\r\n        this._enforceAssociatedVariableName = false;\r\n        endpoint._enforceAssociatedVariableName = false;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Fill the list of excluded connection point types with all types other than those passed in the parameter\r\n     * @param mask Types (ORed values of NodeMaterialBlockConnectionPointTypes) that are allowed, and thus will not be pushed to the excluded list\r\n     */\r\n    public addExcludedConnectionPointFromAllowedTypes(mask: number): void {\r\n        let bitmask = 1;\r\n        while (bitmask < NodeMaterialBlockConnectionPointTypes.All) {\r\n            if (!(mask & bitmask)) {\r\n                this.excludedConnectionPointTypes.push(bitmask);\r\n            }\r\n            bitmask = bitmask << 1;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serializes this point in a JSON representation\r\n     * @param isInput defines if the connection point is an input (default is true)\r\n     * @returns the serialized point object\r\n     */\r\n    public serialize(isInput = true): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.displayName = this.displayName;\r\n\r\n        if (isInput && this.connectedPoint) {\r\n            serializationObject.inputName = this.name;\r\n            serializationObject.targetBlockId = this.connectedPoint.ownerBlock.uniqueId;\r\n            serializationObject.targetConnectionName = this.connectedPoint.name;\r\n            serializationObject.isExposedOnFrame = true;\r\n            serializationObject.exposedPortPosition = this.exposedPortPosition;\r\n        }\r\n\r\n        if (this.isExposedOnFrame || this.exposedPortPosition >= 0) {\r\n            serializationObject.isExposedOnFrame = true;\r\n            serializationObject.exposedPortPosition = this.exposedPortPosition;\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Release resources\r\n     */\r\n    public dispose() {\r\n        this.onConnectionObservable.clear();\r\n    }\r\n}\r\n"]}