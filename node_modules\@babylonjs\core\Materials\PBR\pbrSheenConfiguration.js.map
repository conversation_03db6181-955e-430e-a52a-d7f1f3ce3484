{"version": 3, "file": "pbrSheenConfiguration.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/PBR/pbrSheenConfiguration.ts"], "names": [], "mappings": ";AAAA,yDAAyD;AACzD,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAE3G,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAM9D,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAKrD,OAAO,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAE3F;;GAEG;AACH,MAAM,OAAO,oBAAqB,SAAQ,eAAe;IAAzD;;QACW,UAAK,GAAG,KAAK,CAAC;QACd,kBAAa,GAAG,KAAK,CAAC;QACtB,uBAAkB,GAAG,KAAK,CAAC;QAC3B,4BAAuB,GAAG,KAAK,CAAC;QAChC,0BAAqB,GAAG,CAAC,CAAC;QAC1B,oCAA+B,GAAG,CAAC,CAAC;QACpC,yBAAoB,GAAG,KAAK,CAAC;QAC7B,oBAAe,GAAG,KAAK,CAAC;QACxB,wBAAmB,GAAG,KAAK,CAAC;QAC5B,yCAAoC,GAAG,KAAK,CAAC;QAC7C,sCAAiC,GAAG,KAAK,CAAC;IACrD,CAAC;CAAA;AAED;;GAEG;AACH,MAAM,OAAO,qBAAsB,SAAQ,kBAAkB;IAiFzD,gBAAgB;IACT,gCAAgC;QACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,IAAI,CAAC,wCAAwC,EAAE,CAAC;IACpD,CAAC;IAED,YAAY,QAAyB,EAAE,eAAe,GAAG,IAAI;QACzD,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,oBAAoB,EAAE,EAAE,eAAe,CAAC,CAAC;QAvFvE,eAAU,GAAG,KAAK,CAAC;QAC3B;;WAEG;QAGI,cAAS,GAAG,KAAK,CAAC;QAEjB,yBAAoB,GAAG,KAAK,CAAC;QACrC;;WAEG;QAGI,wBAAmB,GAAG,KAAK,CAAC;QAEnC;;WAEG;QAEI,cAAS,GAAG,CAAC,CAAC;QAErB;;WAEG;QAEI,UAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAEtB,aAAQ,GAA0B,IAAI,CAAC;QAC/C;;;;;WAKG;QAGI,YAAO,GAA0B,IAAI,CAAC;QAErC,iCAA4B,GAAG,IAAI,CAAC;QAC5C;;;WAGG;QAGI,gCAA2B,GAAG,IAAI,CAAC;QAElC,eAAU,GAAqB,IAAI,CAAC;QAC5C;;;;WAIG;QAGI,cAAS,GAAqB,IAAI,CAAC;QAElC,sBAAiB,GAA0B,IAAI,CAAC;QACxD;;;WAGG;QAGI,qBAAgB,GAA0B,IAAI,CAAC;QAE9C,mBAAc,GAAG,KAAK,CAAC;QAC/B;;;;WAIG;QAGI,kBAAa,GAAG,KAAK,CAAC;QAczB,IAAI,CAAC,wCAAwC,GAAG,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IAClH,CAAC;IAEM,iBAAiB,CAAC,OAA6B,EAAE,KAAY;QAChE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,IAAI,KAAK,CAAC,eAAe,EAAE;gBACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,mBAAmB,EAAE;oBACpD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE;wBACvC,OAAO,KAAK,CAAC;qBAChB;iBACJ;gBAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,aAAa,CAAC,mBAAmB,EAAE;oBAC7D,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE;wBAChD,OAAO,KAAK,CAAC;qBAChB;iBACJ;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,8BAA8B,CAAC,OAA6B,EAAE,KAAY;QAC7E,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;YACrB,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACzD,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC;YACnD,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC;YAClD,OAAO,CAAC,oCAAoC,GAAG,IAAI,CAAC,4BAA4B,CAAC;YACjF,OAAO,CAAC,iCAAiC;gBACrC,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,2BAA2B,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAE/J,IAAI,OAAO,CAAC,iBAAiB,EAAE;gBAC3B,IAAI,KAAK,CAAC,eAAe,EAAE;oBACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,mBAAmB,EAAE;wBACpD,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;wBACnE,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;qBACzD;yBAAM;wBACH,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;qBACjC;oBAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,aAAa,CAAC,mBAAmB,EAAE;wBAC7D,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,yBAAyB,CAAC,CAAC;qBACzF;yBAAM;wBACH,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;qBAC3C;iBACJ;aACJ;SACJ;aAAM;YACH,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;YACtB,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;YAC9B,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;YACxC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;YACrC,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;YAChC,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACpC,OAAO,CAAC,oCAAoC,GAAG,KAAK,CAAC;YACrD,OAAO,CAAC,iCAAiC,GAAG,KAAK,CAAC;YAClD,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;YACnC,OAAO,CAAC,qBAAqB,GAAG,CAAC,CAAC;YAClC,OAAO,CAAC,+BAA+B,GAAG,CAAC,CAAC;SAC/C;IACL,CAAC;IAEM,cAAc,CAAC,aAA4B,EAAE,KAAY,EAAE,MAAc,EAAE,OAAgB;QAC9F,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO;SACV;QAED,MAAM,OAAO,GAAG,OAAQ,CAAC,eAAkD,CAAC;QAE5E,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAEzC,MAAM,iBAAiB,GAAG,OAAO,CAAC,iCAAiC,CAAC;QAEpE,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YAC7D,IAAI,iBAAiB,IAAI,aAAa,CAAC,mBAAmB,EAAE;gBACxD,aAAa,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,QAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,QAAS,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACzG,iBAAiB,CAAC,IAAI,CAAC,QAAS,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;aAC7D;iBAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,aAAa,CAAC,mBAAmB,EAAE;gBACvF,aAAa,CAAC,YAAY,CACtB,aAAa,EACb,IAAI,CAAC,QAAQ,EAAE,gBAAgB,IAAI,CAAC,EACpC,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,EACzB,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,IAAI,CAAC,EAC7C,IAAI,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,CACrC,CAAC;gBACF,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACf,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;iBAC5D;gBACD,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC,oCAAoC,EAAE;oBAC/F,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;iBAC9E;aACJ;YAED,QAAQ;YACR,aAAa,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAEpG,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;gBAC1B,aAAa,CAAC,WAAW,CAAC,iBAAiB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;aACjE;SACJ;QAED,WAAW;QACX,IAAI,KAAK,CAAC,eAAe,EAAE;YACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,mBAAmB,EAAE;gBACpD,aAAa,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC3D;YAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC,oCAAoC,IAAI,aAAa,CAAC,mBAAmB,EAAE;gBACpI,aAAa,CAAC,UAAU,CAAC,uBAAuB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;aAC7E;SACJ;IACL,CAAC;IAEM,UAAU,CAAC,OAAoB;QAClC,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,OAAO,EAAE;YACpC,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,iBAAiB,CAAC,cAA6B;QAClD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACtC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC/C;IACL,CAAC;IAEM,cAAc,CAAC,WAA0B;QAC5C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAClF,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7G,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC5C;IACL,CAAC;IAEM,OAAO,CAAC,oBAA8B;QACzC,IAAI,oBAAoB,EAAE;YACtB,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;SACrC;IACL,CAAC;IAEM,YAAY;QACf,OAAO,uBAAuB,CAAC;IACnC,CAAC;IAEM,YAAY,CAAC,OAA6B,EAAE,SAA0B,EAAE,WAAmB;QAC9F,IAAI,OAAO,CAAC,KAAK,EAAE;YACf,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,OAAO,CAAC,CAAC;SACjD;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAEM,WAAW,CAAC,QAAkB;QACjC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,uBAAuB,CAAC,CAAC;IAC3D,CAAC;IAEM,WAAW;QACd,OAAO;YACH,GAAG,EAAE;gBACD,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC9C,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;gBACnD,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC9C,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC/C,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;aAC3D;SACJ,CAAC;IACN,CAAC;CACJ;AA3QU;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;wDAC5B;AAQlB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;kEAClB;AAM5B;IADN,SAAS,EAAE;wDACS;AAMd;IADN,iBAAiB,EAAE;oDACU;AAWvB;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;sDACR;AAStC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;0EACX;AAUnC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;wDACX;AASnC;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;+DACC;AAU/C;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;4DACxB", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { serialize, expandToProperty, serializeAsColor3, serializeAsTexture } from \"../../Misc/decorators\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport { MaterialFlags } from \"../../Materials/materialFlags\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { IAnimatable } from \"../../Animations/animatable.interface\";\r\nimport type { EffectFallbacks } from \"../effectFallbacks\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { MaterialPluginBase } from \"../materialPluginBase\";\r\nimport { MaterialDefines } from \"../materialDefines\";\r\n\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport { BindTextureMatrix, PrepareDefinesForMergedUV } from \"../materialHelper.functions\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class MaterialSheenDefines extends MaterialDefines {\r\n    public SHEEN = false;\r\n    public SHEEN_TEXTURE = false;\r\n    public SHEEN_GAMMATEXTURE = false;\r\n    public SHEEN_TEXTURE_ROUGHNESS = false;\r\n    public SHEEN_TEXTUREDIRECTUV = 0;\r\n    public SHEEN_TEXTURE_ROUGHNESSDIRECTUV = 0;\r\n    public SHEEN_LINKWITHALBEDO = false;\r\n    public SHEEN_ROUGHNESS = false;\r\n    public SHEEN_ALBEDOSCALING = false;\r\n    public SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE = false;\r\n    public SHEEN_TEXTURE_ROUGHNESS_IDENTICAL = false;\r\n}\r\n\r\n/**\r\n * Plugin that implements the sheen component of the PBR material.\r\n */\r\nexport class PBRSheenConfiguration extends MaterialPluginBase {\r\n    private _isEnabled = false;\r\n    /**\r\n     * Defines if the material uses sheen.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isEnabled = false;\r\n\r\n    private _linkSheenWithAlbedo = false;\r\n    /**\r\n     * Defines if the sheen is linked to the sheen color.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public linkSheenWithAlbedo = false;\r\n\r\n    /**\r\n     * Defines the sheen intensity.\r\n     */\r\n    @serialize()\r\n    public intensity = 1;\r\n\r\n    /**\r\n     * Defines the sheen color.\r\n     */\r\n    @serializeAsColor3()\r\n    public color = Color3.White();\r\n\r\n    private _texture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the sheen tint values in a texture.\r\n     * rgb is tint\r\n     * a is a intensity or roughness if the roughness property has been defined and useRoughnessFromTexture is true (in that case, textureRoughness won't be used)\r\n     * If the roughness property has been defined and useRoughnessFromTexture is false then the alpha channel is not used to modulate roughness\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public texture: Nullable<BaseTexture> = null;\r\n\r\n    private _useRoughnessFromMainTexture = true;\r\n    /**\r\n     * Indicates that the alpha channel of the texture property will be used for roughness.\r\n     * Has no effect if the roughness (and texture!) property is not defined\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useRoughnessFromMainTexture = true;\r\n\r\n    private _roughness: Nullable<number> = null;\r\n    /**\r\n     * Defines the sheen roughness.\r\n     * It is not taken into account if linkSheenWithAlbedo is true.\r\n     * To stay backward compatible, material roughness is used instead if sheen roughness = null\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public roughness: Nullable<number> = null;\r\n\r\n    private _textureRoughness: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the sheen roughness in a texture.\r\n     * alpha channel is the roughness. This texture won't be used if the texture property is not empty and useRoughnessFromTexture is true\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public textureRoughness: Nullable<BaseTexture> = null;\r\n\r\n    private _albedoScaling = false;\r\n    /**\r\n     * If true, the sheen effect is layered above the base BRDF with the albedo-scaling technique.\r\n     * It allows the strength of the sheen effect to not depend on the base color of the material,\r\n     * making it easier to setup and tweak the effect\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public albedoScaling = false;\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsTexturesDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsTexturesDirty(): void {\r\n        this._enable(this._isEnabled);\r\n        this._internalMarkAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    constructor(material: PBRBaseMaterial, addToPluginList = true) {\r\n        super(material, \"Sheen\", 120, new MaterialSheenDefines(), addToPluginList);\r\n\r\n        this._internalMarkAllSubMeshesAsTexturesDirty = material._dirtyCallbacks[Constants.MATERIAL_TextureDirtyFlag];\r\n    }\r\n\r\n    public isReadyForSubMesh(defines: MaterialSheenDefines, scene: Scene): boolean {\r\n        if (!this._isEnabled) {\r\n            return true;\r\n        }\r\n\r\n        if (defines._areTexturesDirty) {\r\n            if (scene.texturesEnabled) {\r\n                if (this._texture && MaterialFlags.SheenTextureEnabled) {\r\n                    if (!this._texture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._textureRoughness && MaterialFlags.SheenTextureEnabled) {\r\n                    if (!this._textureRoughness.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public prepareDefinesBeforeAttributes(defines: MaterialSheenDefines, scene: Scene): void {\r\n        if (this._isEnabled) {\r\n            defines.SHEEN = true;\r\n            defines.SHEEN_LINKWITHALBEDO = this._linkSheenWithAlbedo;\r\n            defines.SHEEN_ROUGHNESS = this._roughness !== null;\r\n            defines.SHEEN_ALBEDOSCALING = this._albedoScaling;\r\n            defines.SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE = this._useRoughnessFromMainTexture;\r\n            defines.SHEEN_TEXTURE_ROUGHNESS_IDENTICAL =\r\n                this._texture !== null && this._texture._texture === this._textureRoughness?._texture && this._texture.checkTransformsAreIdentical(this._textureRoughness);\r\n\r\n            if (defines._areTexturesDirty) {\r\n                if (scene.texturesEnabled) {\r\n                    if (this._texture && MaterialFlags.SheenTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._texture, defines, \"SHEEN_TEXTURE\");\r\n                        defines.SHEEN_GAMMATEXTURE = this._texture.gammaSpace;\r\n                    } else {\r\n                        defines.SHEEN_TEXTURE = false;\r\n                    }\r\n\r\n                    if (this._textureRoughness && MaterialFlags.SheenTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._textureRoughness, defines, \"SHEEN_TEXTURE_ROUGHNESS\");\r\n                    } else {\r\n                        defines.SHEEN_TEXTURE_ROUGHNESS = false;\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            defines.SHEEN = false;\r\n            defines.SHEEN_TEXTURE = false;\r\n            defines.SHEEN_TEXTURE_ROUGHNESS = false;\r\n            defines.SHEEN_LINKWITHALBEDO = false;\r\n            defines.SHEEN_ROUGHNESS = false;\r\n            defines.SHEEN_ALBEDOSCALING = false;\r\n            defines.SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE = false;\r\n            defines.SHEEN_TEXTURE_ROUGHNESS_IDENTICAL = false;\r\n            defines.SHEEN_GAMMATEXTURE = false;\r\n            defines.SHEEN_TEXTUREDIRECTUV = 0;\r\n            defines.SHEEN_TEXTURE_ROUGHNESSDIRECTUV = 0;\r\n        }\r\n    }\r\n\r\n    public bindForSubMesh(uniformBuffer: UniformBuffer, scene: Scene, engine: Engine, subMesh: SubMesh): void {\r\n        if (!this._isEnabled) {\r\n            return;\r\n        }\r\n\r\n        const defines = subMesh!.materialDefines as unknown as MaterialSheenDefines;\r\n\r\n        const isFrozen = this._material.isFrozen;\r\n\r\n        const identicalTextures = defines.SHEEN_TEXTURE_ROUGHNESS_IDENTICAL;\r\n\r\n        if (!uniformBuffer.useUbo || !isFrozen || !uniformBuffer.isSync) {\r\n            if (identicalTextures && MaterialFlags.SheenTextureEnabled) {\r\n                uniformBuffer.updateFloat4(\"vSheenInfos\", this._texture!.coordinatesIndex, this._texture!.level, -1, -1);\r\n                BindTextureMatrix(this._texture!, uniformBuffer, \"sheen\");\r\n            } else if ((this._texture || this._textureRoughness) && MaterialFlags.SheenTextureEnabled) {\r\n                uniformBuffer.updateFloat4(\r\n                    \"vSheenInfos\",\r\n                    this._texture?.coordinatesIndex ?? 0,\r\n                    this._texture?.level ?? 0,\r\n                    this._textureRoughness?.coordinatesIndex ?? 0,\r\n                    this._textureRoughness?.level ?? 0\r\n                );\r\n                if (this._texture) {\r\n                    BindTextureMatrix(this._texture, uniformBuffer, \"sheen\");\r\n                }\r\n                if (this._textureRoughness && !identicalTextures && !defines.SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE) {\r\n                    BindTextureMatrix(this._textureRoughness, uniformBuffer, \"sheenRoughness\");\r\n                }\r\n            }\r\n\r\n            // Sheen\r\n            uniformBuffer.updateFloat4(\"vSheenColor\", this.color.r, this.color.g, this.color.b, this.intensity);\r\n\r\n            if (this._roughness !== null) {\r\n                uniformBuffer.updateFloat(\"vSheenRoughness\", this._roughness);\r\n            }\r\n        }\r\n\r\n        // Textures\r\n        if (scene.texturesEnabled) {\r\n            if (this._texture && MaterialFlags.SheenTextureEnabled) {\r\n                uniformBuffer.setTexture(\"sheenSampler\", this._texture);\r\n            }\r\n\r\n            if (this._textureRoughness && !identicalTextures && !defines.SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE && MaterialFlags.SheenTextureEnabled) {\r\n                uniformBuffer.setTexture(\"sheenRoughnessSampler\", this._textureRoughness);\r\n            }\r\n        }\r\n    }\r\n\r\n    public hasTexture(texture: BaseTexture): boolean {\r\n        if (this._texture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._textureRoughness === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public getActiveTextures(activeTextures: BaseTexture[]): void {\r\n        if (this._texture) {\r\n            activeTextures.push(this._texture);\r\n        }\r\n\r\n        if (this._textureRoughness) {\r\n            activeTextures.push(this._textureRoughness);\r\n        }\r\n    }\r\n\r\n    public getAnimatables(animatables: IAnimatable[]): void {\r\n        if (this._texture && this._texture.animations && this._texture.animations.length > 0) {\r\n            animatables.push(this._texture);\r\n        }\r\n\r\n        if (this._textureRoughness && this._textureRoughness.animations && this._textureRoughness.animations.length > 0) {\r\n            animatables.push(this._textureRoughness);\r\n        }\r\n    }\r\n\r\n    public dispose(forceDisposeTextures?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            this._texture?.dispose();\r\n            this._textureRoughness?.dispose();\r\n        }\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"PBRSheenConfiguration\";\r\n    }\r\n\r\n    public addFallbacks(defines: MaterialSheenDefines, fallbacks: EffectFallbacks, currentRank: number): number {\r\n        if (defines.SHEEN) {\r\n            fallbacks.addFallback(currentRank++, \"SHEEN\");\r\n        }\r\n        return currentRank;\r\n    }\r\n\r\n    public getSamplers(samplers: string[]): void {\r\n        samplers.push(\"sheenSampler\", \"sheenRoughnessSampler\");\r\n    }\r\n\r\n    public getUniforms(): { ubo?: Array<{ name: string; size: number; type: string }>; vertex?: string; fragment?: string } {\r\n        return {\r\n            ubo: [\r\n                { name: \"vSheenColor\", size: 4, type: \"vec4\" },\r\n                { name: \"vSheenRoughness\", size: 1, type: \"float\" },\r\n                { name: \"vSheenInfos\", size: 4, type: \"vec4\" },\r\n                { name: \"sheenMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"sheenRoughnessMatrix\", size: 16, type: \"mat4\" },\r\n            ],\r\n        };\r\n    }\r\n}\r\n"]}