{"version": 3, "file": "TBNBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Fragment/TBNBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,oCAAoC,EAAE,MAAM,wCAAwC,CAAC;AAC9F,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AAExG,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAGjD;;GAEG;AACH,MAAM,OAAO,QAAS,SAAQ,iBAAiB;IAC3C;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,qCAAqC,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACtF,IAAI,CAAC,MAAM,CAAC,0CAA0C,CAClD,qCAAqC,CAAC,MAAM,GAAG,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,CAC/I,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,qCAAqC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpF,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAEjF,IAAI,CAAC,cAAc,CACf,KAAK,EACL,qCAAqC,CAAC,MAAM,EAC5C,wBAAwB,CAAC,QAAQ,EACjC,IAAI,uCAAuC,CAAC,KAAK,EAAE,IAAI,EAAE,oCAAoC,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,CAC9H,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,qCAAqC,CAAC,OAAO,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9G,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,qCAAqC,CAAC,OAAO,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9G,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,qCAAqC,CAAC,OAAO,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;IAClH,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,KAA6B;QAC3C,KAAK,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACxC,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QACzC,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,gEAAgE;IAChE,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED,IAAW,MAAM;QACb,OAAO,wBAAwB,CAAC,QAAQ,CAAC;IAC7C,CAAC;IAED,IAAW,MAAM,CAAC,KAA+B,IAAG,CAAC;IAE9C,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,EAAE,CAAC,IAAI;QACnH,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YACzB,IAAI,UAAU,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,WAAW,KAAK,wBAAwB,CAAC,KAAK,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE7J,IAAI,CAAC,UAAU,EAAE;gBACb,UAAU,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;gBACrC,UAAU,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;aAC/D;YACD,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC1B,IAAI,WAAW,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE/H,IAAI,CAAC,WAAW,EAAE;gBACd,WAAW,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACvC,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;aACxC;YACD,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC3B,IAAI,YAAY,GAAG,QAAQ,CAAC,wBAAwB,CAChD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,CAAC,IAAI,KAAK,qCAAqC,CAAC,OAAO,IAAI,uBAAuB,CAAC,CAAC,CAAC,CACzI,CAAC;YAEF,IAAI,CAAC,YAAY,EAAE;gBACf,YAAY,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;gBACzC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;aAC1C;YACD,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC/C;IACL,CAAC;IAEM,cAAc,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QAC9F,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAE7B,IAAI,eAAe,GAAG,MAAM,CAAC,WAAW,CAAC;QACzC,IAAI,MAAM,CAAC,iBAAiB,EAAE,WAAW,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,EAAE;YACtG,eAAe,GAAG,KAAK,CAAC;SAC3B;QAED,IAAI,gBAAgB,GAAG,OAAO,CAAC,WAAW,CAAC;QAC3C,IAAI,OAAO,CAAC,iBAAiB,EAAE,WAAW,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,EAAE;YACxG,gBAAgB,GAAG,KAAK,CAAC;SAC5B;QAED,MAAM,WAAW,GAAG,eAAe,IAAI,gBAAgB,CAAC;QAExD,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAEvB,WAAW;QACX,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACpD,KAAK,CAAC,iBAAiB,IAAI;qBAClB,IAAI,CAAC,IAAI;6CACe,MAAM,CAAC,sBAAsB;8CAC5B,OAAO,CAAC,sBAAsB;qEACP,OAAO,CAAC,sBAAsB;uBAC5E,GAAG,CAAC,sBAAsB,WAAW,KAAK,CAAC,sBAAsB;aAC3E,CAAC;YAEF,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,KAAK,CAAC,iBAAiB;oBACnB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,WAAW,GAAG,CAAC,sBAAsB,WAAW,GAAG,CAAC,sBAAsB,WAAW,GAAG,CAAC,sBAAsB,YAAY,CAAC;aACtK;YACD,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,KAAK,CAAC,iBAAiB;oBACnB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,WAAW,GAAG,CAAC,sBAAsB,UAAU,GAAG,CAAC,sBAAsB,WAAW,GAAG,CAAC,sBAAsB,YAAY,CAAC;aACrK;YACD,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,KAAK,CAAC,iBAAiB;oBACnB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,WAAW,GAAG,CAAC,sBAAsB,WAAW,GAAG,CAAC,sBAAsB,WAAW,GAAG,CAAC,sBAAsB,YAAY,CAAC;aACtK;YAED,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\n\r\n/**\r\n * Block used to implement TBN matrix\r\n */\r\nexport class TBNBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new TBNBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment, true);\r\n\r\n        this.registerInput(\"normal\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false);\r\n        this.normal.addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color4 | NodeMaterialBlockConnectionPointTypes.Vector4 | NodeMaterialBlockConnectionPointTypes.Vector3\r\n        );\r\n        this.registerInput(\"tangent\", NodeMaterialBlockConnectionPointTypes.Vector4, false);\r\n        this.registerInput(\"world\", NodeMaterialBlockConnectionPointTypes.Matrix, false);\r\n\r\n        this.registerOutput(\r\n            \"TBN\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"TBN\", this, NodeMaterialConnectionPointDirection.Output, TBNBlock, \"TBNBlock\")\r\n        );\r\n\r\n        this.registerOutput(\"row0\", NodeMaterialBlockConnectionPointTypes.Vector3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"row1\", NodeMaterialBlockConnectionPointTypes.Vector3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"row2\", NodeMaterialBlockConnectionPointTypes.Vector3, NodeMaterialBlockTargets.Fragment);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"TBNBlock\";\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"tbnNormal\");\r\n        state._excludeVariableName(\"tbnTangent\");\r\n        state._excludeVariableName(\"tbnBitangent\");\r\n        state._excludeVariableName(\"TBN\");\r\n    }\r\n\r\n    /**\r\n     * Gets the normal input component\r\n     */\r\n    public get normal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the tangent input component\r\n     */\r\n    public get tangent(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the world matrix input component\r\n     */\r\n    public get world(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the TBN output component\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get TBN(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the row0 of the output matrix\r\n     */\r\n    public get row0(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the row1 of the output matrix\r\n     */\r\n    public get row1(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the row2 of the output matrix\r\n     */\r\n    public get row2(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    public get target() {\r\n        return NodeMaterialBlockTargets.Fragment;\r\n    }\r\n\r\n    public set target(value: NodeMaterialBlockTargets) {}\r\n\r\n    public autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.world.isConnected) {\r\n            let worldInput = material.getInputBlockByPredicate((b) => b.isSystemValue && b.systemValue === NodeMaterialSystemValues.World && additionalFilteringInfo(b));\r\n\r\n            if (!worldInput) {\r\n                worldInput = new InputBlock(\"world\");\r\n                worldInput.setAsSystemValue(NodeMaterialSystemValues.World);\r\n            }\r\n            worldInput.output.connectTo(this.world);\r\n        }\r\n\r\n        if (!this.normal.isConnected) {\r\n            let normalInput = material.getInputBlockByPredicate((b) => b.isAttribute && b.name === \"normal\" && additionalFilteringInfo(b));\r\n\r\n            if (!normalInput) {\r\n                normalInput = new InputBlock(\"normal\");\r\n                normalInput.setAsAttribute(\"normal\");\r\n            }\r\n            normalInput.output.connectTo(this.normal);\r\n        }\r\n\r\n        if (!this.tangent.isConnected) {\r\n            let tangentInput = material.getInputBlockByPredicate(\r\n                (b) => b.isAttribute && b.name === \"tangent\" && b.type === NodeMaterialBlockConnectionPointTypes.Vector4 && additionalFilteringInfo(b)\r\n            );\r\n\r\n            if (!tangentInput) {\r\n                tangentInput = new InputBlock(\"tangent\");\r\n                tangentInput.setAsAttribute(\"tangent\");\r\n            }\r\n            tangentInput.output.connectTo(this.tangent);\r\n        }\r\n    }\r\n\r\n    public prepareDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        const normal = this.normal;\r\n        const tangent = this.tangent;\r\n\r\n        let normalAvailable = normal.isConnected;\r\n        if (normal.connectInputBlock?.isAttribute && !mesh.isVerticesDataPresent(normal.connectInputBlock?.name)) {\r\n            normalAvailable = false;\r\n        }\r\n\r\n        let tangentAvailable = tangent.isConnected;\r\n        if (tangent.connectInputBlock?.isAttribute && !mesh.isVerticesDataPresent(tangent.connectInputBlock?.name)) {\r\n            tangentAvailable = false;\r\n        }\r\n\r\n        const useTBNBlock = normalAvailable && tangentAvailable;\r\n\r\n        defines.setValue(\"TBNBLOCK\", useTBNBlock, true);\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const normal = this.normal;\r\n        const tangent = this.tangent;\r\n        const world = this.world;\r\n        const TBN = this.TBN;\r\n        const row0 = this.row0;\r\n        const row1 = this.row1;\r\n        const row2 = this.row2;\r\n\r\n        // Fragment\r\n        if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += `\r\n                // ${this.name}\r\n                vec3 tbnNormal = normalize(${normal.associatedVariableName}).xyz;\r\n                vec3 tbnTangent = normalize(${tangent.associatedVariableName}.xyz);\r\n                vec3 tbnBitangent = cross(tbnNormal, tbnTangent) * ${tangent.associatedVariableName}.w;\r\n                mat3 ${TBN.associatedVariableName} = mat3(${world.associatedVariableName}) * mat3(tbnTangent, tbnBitangent, tbnNormal);\r\n            `;\r\n\r\n            if (row0.hasEndpoints) {\r\n                state.compilationString +=\r\n                    this._declareOutput(row0, state) + ` = vec3(${TBN.associatedVariableName}[0][0], ${TBN.associatedVariableName}[0][1], ${TBN.associatedVariableName}[0][2]);\\n`;\r\n            }\r\n            if (row1.hasEndpoints) {\r\n                state.compilationString +=\r\n                    this._declareOutput(row1, state) + ` = vec3(${TBN.associatedVariableName}[1[0], ${TBN.associatedVariableName}[1][1], ${TBN.associatedVariableName}[1][2]);\\n`;\r\n            }\r\n            if (row2.hasEndpoints) {\r\n                state.compilationString +=\r\n                    this._declareOutput(row2, state) + ` = vec3(${TBN.associatedVariableName}[2][0], ${TBN.associatedVariableName}[2][1], ${TBN.associatedVariableName}[2][2]);\\n`;\r\n            }\r\n\r\n            state.sharedData.blocksWithDefines.push(this);\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.TBNBlock\", TBNBlock);\r\n"]}