{"version": 3, "file": "shaderCodeInliner.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Processors/shaderCodeInliner.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,6BAAyB;AAC1C,OAAO,EAAE,YAAY,EAAE,qBAAqB,EAAE,YAAY,EAAE,gBAAgB,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,mCAAmC,CAAC;AAUzJ;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAa1B,+CAA+C;IAC/C,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,YAAY,UAAkB,EAAE,gBAAgB,GAAG,EAAE;QAbrD,kCAAkC;QAC3B,UAAK,GAAY,KAAK,CAAC;QAa1B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,gBAAgB,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,WAAW;QACd,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,MAAM,CAAC,GAAG,CAAC,qCAAqC,IAAI,CAAC,WAAW,CAAC,MAAM,MAAM,CAAC,CAAC;SAClF;QACD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC9C,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,MAAM,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;SAC1C;IACL,CAAC;IAEO,iBAAiB;QACrB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,OAAO,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACzC,qDAAqD;YACrD,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAChF,IAAI,gBAAgB,GAAG,CAAC,EAAE;gBACtB,MAAM;aACT;YAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACvG,IAAI,oBAAoB,GAAG,CAAC,EAAE;gBAC1B,IAAI,IAAI,CAAC,KAAK,EAAE;oBACZ,MAAM,CAAC,IAAI,CAAC,sEAAsE,UAAU,EAAE,CAAC,CAAC;iBACnG;gBACD,UAAU,GAAG,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACxD,SAAS;aACZ;YAED,MAAM,aAAa,GAAG,iBAAiB,CAAC,8BAA8B,CAAC,IAAI,CACvE,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAC/F,CAAC;YACF,IAAI,CAAC,aAAa,EAAE;gBAChB,IAAI,IAAI,CAAC,KAAK,EAAE;oBACZ,MAAM,CAAC,IAAI,CACP,yDAAyD,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAAE,CAC1J,CAAC;iBACL;gBACD,UAAU,GAAG,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACxD,SAAS;aACZ;YACD,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;YAElE,wGAAwG;YACxG,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC;YACnG,IAAI,kBAAkB,GAAG,CAAC,EAAE;gBACxB,IAAI,IAAI,CAAC,KAAK,EAAE;oBACZ,MAAM,CAAC,IAAI,CAAC,kDAAkD,QAAQ,WAAW,QAAQ,2BAA2B,oBAAoB,EAAE,CAAC,CAAC;iBAC/I;gBACD,UAAU,GAAG,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACxD,SAAS;aACZ;YACD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,oBAAoB,GAAG,CAAC,EAAE,kBAAkB,CAAC,CAAC;YAE5F,6DAA6D;YAC7D,MAAM,kBAAkB,GAAG,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,kBAAkB,GAAG,CAAC,CAAC,CAAC;YACrF,IAAI,kBAAkB,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;gBAChD,IAAI,IAAI,CAAC,KAAK,EAAE;oBACZ,MAAM,CAAC,IAAI,CAAC,+CAA+C,QAAQ,WAAW,QAAQ,yBAAyB,kBAAkB,EAAE,CAAC,CAAC;iBACxI;gBACD,UAAU,GAAG,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACxD,SAAS;aACZ;YAED,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;YAC/F,IAAI,gBAAgB,GAAG,CAAC,EAAE;gBACtB,IAAI,IAAI,CAAC,KAAK,EAAE;oBACZ,MAAM,CAAC,IAAI,CAAC,+CAA+C,QAAQ,WAAW,QAAQ,yBAAyB,kBAAkB,EAAE,CAAC,CAAC;iBACxI;gBACD,UAAU,GAAG,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACxD,SAAS;aACZ;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,kBAAkB,EAAE,gBAAgB,GAAG,CAAC,CAAC,CAAC;YAEtF,6CAA6C;YAC7C,MAAM,MAAM,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACrD,MAAM,UAAU,GAAG,EAAE,CAAC;YAEtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACpC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAC/B,MAAM,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAEnC,IAAI,GAAG,IAAI,CAAC,EAAE;oBACV,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;iBAC7C;aACJ;YAED,IAAI,QAAQ,KAAK,MAAM,EAAE;gBACrB,6HAA6H;gBAC7H,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC7B;YAED,uBAAuB;YACvB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBACrB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,UAAU;gBACtB,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,CAAC;aACf,CAAC,CAAC;YAEH,UAAU,GAAG,gBAAgB,GAAG,CAAC,CAAC;YAElC,2CAA2C;YAC3C,MAAM,UAAU,GAAG,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/F,MAAM,SAAS,GAAG,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAE7H,IAAI,CAAC,WAAW,GAAG,UAAU,GAAG,SAAS,CAAC;YAE1C,UAAU,IAAI,gBAAgB,GAAG,CAAC,GAAG,gBAAgB,CAAC;SACzD;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,MAAM,CAAC,GAAG,CAAC,sBAAsB,IAAI,CAAC,cAAc,CAAC,MAAM,mCAAmC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;SACxH;IACL,CAAC;IAEO,gBAAgB,CAAC,mBAA2B,EAAE;QAClD,OAAO,gBAAgB,EAAE,IAAI,CAAC,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE;gBACrC,MAAM;aACT;SACJ;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,MAAM,CAAC,GAAG,CAAC,uBAAuB,gBAAgB,yBAAyB,CAAC,CAAC;SAChF;QAED,OAAO,gBAAgB,IAAI,CAAC,CAAC;IACjC,CAAC;IAEO,2BAA2B;QAC/B,IAAI,OAAO,GAAG,KAAK,CAAC;QAEpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YACpC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;YAE9C,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,OAAO,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;gBACzC,gDAAgD;gBAChD,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;gBAErE,IAAI,iBAAiB,GAAG,CAAC,EAAE;oBACvB,MAAM;iBACT;gBAED,kDAAkD;gBAClD,IAAI,iBAAiB,KAAK,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,EAAE;oBAC7F,UAAU,GAAG,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC;oBAC7C,SAAS;iBACZ;gBAED,+BAA+B;gBAC/B,MAAM,oBAAoB,GAAG,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;gBAChG,IAAI,oBAAoB,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,GAAG,EAAE;oBAC3G,UAAU,GAAG,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC;oBAC7C,SAAS;iBACZ;gBAED,6GAA6G;gBAC7G,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAC;gBACnG,IAAI,kBAAkB,GAAG,CAAC,EAAE;oBACxB,IAAI,IAAI,CAAC,KAAK,EAAE;wBACZ,MAAM,CAAC,IAAI,CAAC,oEAAoE,IAAI,WAAW,IAAI,2BAA2B,oBAAoB,EAAE,CAAC,CAAC;qBACzJ;oBACD,UAAU,GAAG,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC;oBAC7C,SAAS;iBACZ;gBACD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,oBAAoB,GAAG,CAAC,EAAE,kBAAkB,CAAC,CAAC;gBAE5F,iDAAiD;gBAEjD,sIAAsI;gBACtI,mCAAmC;gBACnC,MAAM,kBAAkB,GAAG,CAAC,CAAS,EAAE,EAAE;oBACrC,MAAM,UAAU,GAAG,EAAE,CAAC;oBACtB,IAAI,MAAM,GAAG,CAAC,EACV,aAAa,GAAG,CAAC,CAAC;oBACtB,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM,EAAE;wBACtB,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE;4BAC1B,MAAM,IAAI,GAAG,qBAAqB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;4BACxD,IAAI,IAAI,GAAG,CAAC,EAAE;gCACV,OAAO,IAAI,CAAC;6BACf;4BACD,MAAM,GAAG,IAAI,CAAC;yBACjB;6BAAM,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE;4BACjC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;4BACpD,aAAa,GAAG,MAAM,GAAG,CAAC,CAAC;yBAC9B;wBACD,MAAM,EAAE,CAAC;qBACZ;oBACD,IAAI,aAAa,GAAG,MAAM,EAAE;wBACxB,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;qBACvD;oBACD,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC;gBAEF,MAAM,MAAM,GAAG,kBAAkB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC;gBAE9D,IAAI,MAAM,KAAK,IAAI,EAAE;oBACjB,IAAI,IAAI,CAAC,KAAK,EAAE;wBACZ,MAAM,CAAC,IAAI,CACP,uFAAuF,IAAI,WAAW,IAAI,2BAA2B,oBAAoB,eAAe;4BACpK,UAAU,CACjB,CAAC;qBACL;oBACD,UAAU,GAAG,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC;oBAC7C,SAAS;iBACZ;gBAED,MAAM,UAAU,GAAG,EAAE,CAAC;gBAEtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;oBACpC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;oBAC/B,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAC1B;gBAED,MAAM,YAAY,GAAG,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;gBAE5E,IAAI,YAAY,EAAE;oBACd,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC;iBACxC;gBAED,IAAI,UAAU,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE;oBACzC,IAAI,IAAI,CAAC,KAAK,EAAE;wBACZ,MAAM,CAAC,IAAI,CACP,6HAA6H,IAAI,WAAW,IAAI,0BAA0B,UAAU,qBAAqB,UAAU,EAAE,CACxN,CAAC;qBACL;oBACD,UAAU,GAAG,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC;oBAC7C,SAAS;iBACZ;gBAED,UAAU,GAAG,kBAAkB,GAAG,CAAC,CAAC;gBAEpC,iDAAiD;gBACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;gBAElE,IAAI,UAAU,GAAG,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC/F,MAAM,SAAS,GAAG,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAEjI,IAAI,YAAY,EAAE;oBACd,wDAAwD;oBACxD,yBAAyB;oBACzB,kBAAkB;oBAClB,gDAAgD;oBAChD,MAAM,sBAAsB,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,iBAAiB,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;oBAEhG,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,sBAAsB,GAAG,CAAC,CAAC,CAAC;oBACvE,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,sBAAsB,GAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC;oBAE9F,IAAI,CAAC,WAAW,GAAG,UAAU,GAAG,IAAI,GAAG,GAAG,GAAG,YAAY,GAAG,KAAK,GAAG,QAAQ,GAAG,IAAI,GAAG,WAAW,GAAG,YAAY,GAAG,SAAS,CAAC;oBAE7H,IAAI,IAAI,CAAC,KAAK,EAAE;wBACZ,MAAM,CAAC,GAAG,CACN,4CAA4C,IAAI,WAAW,IAAI,6BAA6B,sBAAsB,qBAAqB,UAAU,EAAE,CACtJ,CAAC;qBACL;iBACJ;qBAAM;oBACH,+DAA+D;oBAC/D,IAAI,CAAC,WAAW,GAAG,UAAU,GAAG,QAAQ,GAAG,SAAS,CAAC;oBAErD,UAAU,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,kBAAkB,GAAG,CAAC,GAAG,iBAAiB,CAAC,CAAC;oBAE7E,IAAI,IAAI,CAAC,KAAK,EAAE;wBACZ,MAAM,CAAC,GAAG,CAAC,4CAA4C,IAAI,WAAW,IAAI,wBAAwB,iBAAiB,qBAAqB,UAAU,EAAE,CAAC,CAAC;qBACzJ;iBACJ;gBAED,OAAO,GAAG,IAAI,CAAC;aAClB;SACJ;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,aAAa,CAAC,IAAY,EAAE,OAAiB,EAAE,YAAsB;QACzE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EACpD,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAC7B,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YAElC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,IAAI,EAAE,EAAE;gBAC3C,MAAM,MAAM,GAAW,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC/B,sHAAsH;gBACtH,IAAI,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,EAAE;oBAChG,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;iBACrB;gBACD,OAAO,WAAW,CAAC;YACvB,CAAC,CAAC,CAAC;SACN;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;;AAvUuB,gDAA8B,GAAG,4BAA4B,AAA/B,CAAgC", "sourcesContent": ["import { Logger } from \"core/Misc/logger\";\r\nimport { EscapeRegExp, ExtractBetweenMarkers, FindBackward, IsIdentifierChar, RemoveComments, SkipWhitespaces } from \"../../Misc/codeStringParsingTools\";\r\n\r\ninterface IInlineFunctionDescr {\r\n    name: string;\r\n    type: string;\r\n    parameters: string[];\r\n    body: string;\r\n    callIndex: number;\r\n}\r\n\r\n/**\r\n * Class used to inline functions in shader code\r\n */\r\nexport class ShaderCodeInliner {\r\n    private static readonly _RegexpFindFunctionNameAndType = /((\\s+?)(\\w+)\\s+(\\w+)\\s*?)$/;\r\n\r\n    private _sourceCode: string;\r\n    private _functionDescr: IInlineFunctionDescr[];\r\n    private _numMaxIterations: number;\r\n\r\n    /** Gets or sets the token used to mark the functions to inline */\r\n    public inlineToken: string;\r\n\r\n    /** Gets or sets the debug mode */\r\n    public debug: boolean = false;\r\n\r\n    /** Gets the code after the inlining process */\r\n    public get code(): string {\r\n        return this._sourceCode;\r\n    }\r\n\r\n    /**\r\n     * Initializes the inliner\r\n     * @param sourceCode shader code source to inline\r\n     * @param numMaxIterations maximum number of iterations (used to detect recursive calls)\r\n     */\r\n    constructor(sourceCode: string, numMaxIterations = 20) {\r\n        this._sourceCode = sourceCode;\r\n        this._numMaxIterations = numMaxIterations;\r\n        this._functionDescr = [];\r\n        this.inlineToken = \"#define inline\";\r\n    }\r\n\r\n    /**\r\n     * Start the processing of the shader code\r\n     */\r\n    public processCode() {\r\n        if (this.debug) {\r\n            Logger.Log(`Start inlining process (code size=${this._sourceCode.length})...`);\r\n        }\r\n        this._collectFunctions();\r\n        this._processInlining(this._numMaxIterations);\r\n        if (this.debug) {\r\n            Logger.Log(\"End of inlining process.\");\r\n        }\r\n    }\r\n\r\n    private _collectFunctions() {\r\n        let startIndex = 0;\r\n\r\n        while (startIndex < this._sourceCode.length) {\r\n            // locate the function to inline and extract its name\r\n            const inlineTokenIndex = this._sourceCode.indexOf(this.inlineToken, startIndex);\r\n            if (inlineTokenIndex < 0) {\r\n                break;\r\n            }\r\n\r\n            const funcParamsStartIndex = this._sourceCode.indexOf(\"(\", inlineTokenIndex + this.inlineToken.length);\r\n            if (funcParamsStartIndex < 0) {\r\n                if (this.debug) {\r\n                    Logger.Warn(`Could not find the opening parenthesis after the token. startIndex=${startIndex}`);\r\n                }\r\n                startIndex = inlineTokenIndex + this.inlineToken.length;\r\n                continue;\r\n            }\r\n\r\n            const funcNameMatch = ShaderCodeInliner._RegexpFindFunctionNameAndType.exec(\r\n                this._sourceCode.substring(inlineTokenIndex + this.inlineToken.length, funcParamsStartIndex)\r\n            );\r\n            if (!funcNameMatch) {\r\n                if (this.debug) {\r\n                    Logger.Warn(\r\n                        `Could not extract the name/type of the function from: ${this._sourceCode.substring(inlineTokenIndex + this.inlineToken.length, funcParamsStartIndex)}`\r\n                    );\r\n                }\r\n                startIndex = inlineTokenIndex + this.inlineToken.length;\r\n                continue;\r\n            }\r\n            const [funcType, funcName] = [funcNameMatch[3], funcNameMatch[4]];\r\n\r\n            // extract the parameters of the function as a whole string (without the leading / trailing parenthesis)\r\n            const funcParamsEndIndex = ExtractBetweenMarkers(\"(\", \")\", this._sourceCode, funcParamsStartIndex);\r\n            if (funcParamsEndIndex < 0) {\r\n                if (this.debug) {\r\n                    Logger.Warn(`Could not extract the parameters the function '${funcName}' (type=${funcType}). funcParamsStartIndex=${funcParamsStartIndex}`);\r\n                }\r\n                startIndex = inlineTokenIndex + this.inlineToken.length;\r\n                continue;\r\n            }\r\n            const funcParams = this._sourceCode.substring(funcParamsStartIndex + 1, funcParamsEndIndex);\r\n\r\n            // extract the body of the function (with the curly brackets)\r\n            const funcBodyStartIndex = SkipWhitespaces(this._sourceCode, funcParamsEndIndex + 1);\r\n            if (funcBodyStartIndex === this._sourceCode.length) {\r\n                if (this.debug) {\r\n                    Logger.Warn(`Could not extract the body of the function '${funcName}' (type=${funcType}). funcParamsEndIndex=${funcParamsEndIndex}`);\r\n                }\r\n                startIndex = inlineTokenIndex + this.inlineToken.length;\r\n                continue;\r\n            }\r\n\r\n            const funcBodyEndIndex = ExtractBetweenMarkers(\"{\", \"}\", this._sourceCode, funcBodyStartIndex);\r\n            if (funcBodyEndIndex < 0) {\r\n                if (this.debug) {\r\n                    Logger.Warn(`Could not extract the body of the function '${funcName}' (type=${funcType}). funcBodyStartIndex=${funcBodyStartIndex}`);\r\n                }\r\n                startIndex = inlineTokenIndex + this.inlineToken.length;\r\n                continue;\r\n            }\r\n            const funcBody = this._sourceCode.substring(funcBodyStartIndex, funcBodyEndIndex + 1);\r\n\r\n            // process the parameters: extract each names\r\n            const params = RemoveComments(funcParams).split(\",\");\r\n            const paramNames = [];\r\n\r\n            for (let p = 0; p < params.length; ++p) {\r\n                const param = params[p].trim();\r\n                const idx = param.lastIndexOf(\" \");\r\n\r\n                if (idx >= 0) {\r\n                    paramNames.push(param.substring(idx + 1));\r\n                }\r\n            }\r\n\r\n            if (funcType !== \"void\") {\r\n                // for functions that return a value, we will replace \"return\" by \"tempvarname = \", tempvarname being a unique generated name\r\n                paramNames.push(\"return\");\r\n            }\r\n\r\n            // collect the function\r\n            this._functionDescr.push({\r\n                name: funcName,\r\n                type: funcType,\r\n                parameters: paramNames,\r\n                body: funcBody,\r\n                callIndex: 0,\r\n            });\r\n\r\n            startIndex = funcBodyEndIndex + 1;\r\n\r\n            // remove the function from the source code\r\n            const partBefore = inlineTokenIndex > 0 ? this._sourceCode.substring(0, inlineTokenIndex) : \"\";\r\n            const partAfter = funcBodyEndIndex + 1 < this._sourceCode.length - 1 ? this._sourceCode.substring(funcBodyEndIndex + 1) : \"\";\r\n\r\n            this._sourceCode = partBefore + partAfter;\r\n\r\n            startIndex -= funcBodyEndIndex + 1 - inlineTokenIndex;\r\n        }\r\n\r\n        if (this.debug) {\r\n            Logger.Log(`Collect functions: ${this._functionDescr.length} functions found. functionDescr=${this._functionDescr}`);\r\n        }\r\n    }\r\n\r\n    private _processInlining(numMaxIterations: number = 20): boolean {\r\n        while (numMaxIterations-- >= 0) {\r\n            if (!this._replaceFunctionCallsByCode()) {\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (this.debug) {\r\n            Logger.Log(`numMaxIterations is ${numMaxIterations} after inlining process`);\r\n        }\r\n\r\n        return numMaxIterations >= 0;\r\n    }\r\n\r\n    private _replaceFunctionCallsByCode(): boolean {\r\n        let doAgain = false;\r\n\r\n        for (const func of this._functionDescr) {\r\n            const { name, type, parameters, body } = func;\r\n\r\n            let startIndex = 0;\r\n\r\n            while (startIndex < this._sourceCode.length) {\r\n                // Look for the function name in the source code\r\n                const functionCallIndex = this._sourceCode.indexOf(name, startIndex);\r\n\r\n                if (functionCallIndex < 0) {\r\n                    break;\r\n                }\r\n\r\n                // Make sure \"name\" is not part of a bigger string\r\n                if (functionCallIndex === 0 || IsIdentifierChar(this._sourceCode.charAt(functionCallIndex - 1))) {\r\n                    startIndex = functionCallIndex + name.length;\r\n                    continue;\r\n                }\r\n\r\n                // Find the opening parenthesis\r\n                const callParamsStartIndex = SkipWhitespaces(this._sourceCode, functionCallIndex + name.length);\r\n                if (callParamsStartIndex === this._sourceCode.length || this._sourceCode.charAt(callParamsStartIndex) !== \"(\") {\r\n                    startIndex = functionCallIndex + name.length;\r\n                    continue;\r\n                }\r\n\r\n                // extract the parameters of the function call as a whole string (without the leading / trailing parenthesis)\r\n                const callParamsEndIndex = ExtractBetweenMarkers(\"(\", \")\", this._sourceCode, callParamsStartIndex);\r\n                if (callParamsEndIndex < 0) {\r\n                    if (this.debug) {\r\n                        Logger.Warn(`Could not extract the parameters of the function call. Function '${name}' (type=${type}). callParamsStartIndex=${callParamsStartIndex}`);\r\n                    }\r\n                    startIndex = functionCallIndex + name.length;\r\n                    continue;\r\n                }\r\n                const callParams = this._sourceCode.substring(callParamsStartIndex + 1, callParamsEndIndex);\r\n\r\n                // process the parameter call: extract each names\r\n\r\n                // this function split the parameter list used in the function call at ',' boundaries by taking care of potential parenthesis like in:\r\n                //      myfunc(a, vec2(1., 0.), 4.)\r\n                const splitParameterCall = (s: string) => {\r\n                    const parameters = [];\r\n                    let curIdx = 0,\r\n                        startParamIdx = 0;\r\n                    while (curIdx < s.length) {\r\n                        if (s.charAt(curIdx) === \"(\") {\r\n                            const idx2 = ExtractBetweenMarkers(\"(\", \")\", s, curIdx);\r\n                            if (idx2 < 0) {\r\n                                return null;\r\n                            }\r\n                            curIdx = idx2;\r\n                        } else if (s.charAt(curIdx) === \",\") {\r\n                            parameters.push(s.substring(startParamIdx, curIdx));\r\n                            startParamIdx = curIdx + 1;\r\n                        }\r\n                        curIdx++;\r\n                    }\r\n                    if (startParamIdx < curIdx) {\r\n                        parameters.push(s.substring(startParamIdx, curIdx));\r\n                    }\r\n                    return parameters;\r\n                };\r\n\r\n                const params = splitParameterCall(RemoveComments(callParams));\r\n\r\n                if (params === null) {\r\n                    if (this.debug) {\r\n                        Logger.Warn(\r\n                            `Invalid function call: can't extract the parameters of the function call. Function '${name}' (type=${type}). callParamsStartIndex=${callParamsStartIndex}, callParams=` +\r\n                                callParams\r\n                        );\r\n                    }\r\n                    startIndex = functionCallIndex + name.length;\r\n                    continue;\r\n                }\r\n\r\n                const paramNames = [];\r\n\r\n                for (let p = 0; p < params.length; ++p) {\r\n                    const param = params[p].trim();\r\n                    paramNames.push(param);\r\n                }\r\n\r\n                const retParamName = type !== \"void\" ? name + \"_\" + func.callIndex++ : null;\r\n\r\n                if (retParamName) {\r\n                    paramNames.push(retParamName + \" =\");\r\n                }\r\n\r\n                if (paramNames.length !== parameters.length) {\r\n                    if (this.debug) {\r\n                        Logger.Warn(\r\n                            `Invalid function call: not the same number of parameters for the call than the number expected by the function. Function '${name}' (type=${type}). function parameters=${parameters}, call parameters=${paramNames}`\r\n                        );\r\n                    }\r\n                    startIndex = functionCallIndex + name.length;\r\n                    continue;\r\n                }\r\n\r\n                startIndex = callParamsEndIndex + 1;\r\n\r\n                // replace the function call by the body function\r\n                const funcBody = this._replaceNames(body, parameters, paramNames);\r\n\r\n                let partBefore = functionCallIndex > 0 ? this._sourceCode.substring(0, functionCallIndex) : \"\";\r\n                const partAfter = callParamsEndIndex + 1 < this._sourceCode.length - 1 ? this._sourceCode.substring(callParamsEndIndex + 1) : \"\";\r\n\r\n                if (retParamName) {\r\n                    // case where the function returns a value. We generate:\r\n                    // FUNCTYPE retParamName;\r\n                    // {function body}\r\n                    // and replace the function call by retParamName\r\n                    const injectDeclarationIndex = FindBackward(this._sourceCode, functionCallIndex - 1, \"\\n\", \"{\");\r\n\r\n                    partBefore = this._sourceCode.substring(0, injectDeclarationIndex + 1);\r\n                    const partBetween = this._sourceCode.substring(injectDeclarationIndex + 1, functionCallIndex);\r\n\r\n                    this._sourceCode = partBefore + type + \" \" + retParamName + \";\\n\" + funcBody + \"\\n\" + partBetween + retParamName + partAfter;\r\n\r\n                    if (this.debug) {\r\n                        Logger.Log(\r\n                            `Replace function call by code. Function '${name}' (type=${type}). injectDeclarationIndex=${injectDeclarationIndex}, call parameters=${paramNames}`\r\n                        );\r\n                    }\r\n                } else {\r\n                    // simple case where the return value of the function is \"void\"\r\n                    this._sourceCode = partBefore + funcBody + partAfter;\r\n\r\n                    startIndex += funcBody.length - (callParamsEndIndex + 1 - functionCallIndex);\r\n\r\n                    if (this.debug) {\r\n                        Logger.Log(`Replace function call by code. Function '${name}' (type=${type}). functionCallIndex=${functionCallIndex}, call parameters=${paramNames}`);\r\n                    }\r\n                }\r\n\r\n                doAgain = true;\r\n            }\r\n        }\r\n\r\n        return doAgain;\r\n    }\r\n\r\n    private _replaceNames(code: string, sources: string[], destinations: string[]): string {\r\n        for (let i = 0; i < sources.length; ++i) {\r\n            const source = new RegExp(EscapeRegExp(sources[i]), \"g\"),\r\n                sourceLen = sources[i].length,\r\n                destination = destinations[i];\r\n\r\n            code = code.replace(source, (match, ...args) => {\r\n                const offset: number = args[0];\r\n                // Make sure \"source\" is not part of a bigger identifier (for eg, if source=view and we matched it with viewDirection)\r\n                if (IsIdentifierChar(code.charAt(offset - 1)) || IsIdentifierChar(code.charAt(offset + sourceLen))) {\r\n                    return sources[i];\r\n                }\r\n                return destination;\r\n            });\r\n        }\r\n\r\n        return code;\r\n    }\r\n}\r\n"]}