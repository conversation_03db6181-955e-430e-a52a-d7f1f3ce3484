{"version": 3, "file": "sphereParticleEmitter.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Particles/EmitterTypes/sphereParticleEmitter.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAGjD,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAGnD;;;GAGG;AACH,MAAM,OAAO,qBAAqB;IAC9B;;;;;OAKG;IACH;IACI;;OAEG;IACI,SAAS,CAAC;IACjB;;OAEG;IACI,cAAc,CAAC;IACtB;;OAEG;IACI,sBAAsB,CAAC;QARvB,WAAM,GAAN,MAAM,CAAI;QAIV,gBAAW,GAAX,WAAW,CAAI;QAIf,wBAAmB,GAAnB,mBAAmB,CAAI;IAC/B,CAAC;IAEJ;;;;;;OAMG;IACI,sBAAsB,CAAC,WAAmB,EAAE,iBAA0B,EAAE,QAAkB,EAAE,OAAgB;QAC/G,MAAM,SAAS,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC;QACvF,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC9D,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC9D,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC9D,SAAS,CAAC,CAAC,IAAI,KAAK,CAAC;QACrB,SAAS,CAAC,CAAC,IAAI,KAAK,CAAC;QACrB,SAAS,CAAC,CAAC,IAAI,KAAK,CAAC;QACrB,SAAS,CAAC,SAAS,EAAE,CAAC;QAEtB,IAAI,OAAO,EAAE;YACT,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACtC,OAAO;SACV;QAED,OAAO,CAAC,8BAA8B,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;IAClH,CAAC;IAED;;;;;;OAMG;IACI,qBAAqB,CAAC,WAAmB,EAAE,gBAAyB,EAAE,QAAkB,EAAE,OAAgB;QAC7G,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;QACvF,MAAM,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACnC,MAAM,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC3D,MAAM,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC3C,MAAM,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAE3D,IAAI,OAAO,EAAE;YACT,gBAAgB,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO;SACV;QAED,OAAO,CAAC,mCAAmC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC;IACpG,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,MAAM,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAEhF,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAElC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,WAA8C;QAC/D,WAAW,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5C,WAAW,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACtD,WAAW,CAAC,QAAQ,CAAC,qBAAqB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAC1E,CAAC;IAED;;;OAGG;IACI,kBAAkB,CAAC,GAAkB;QACxC,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC5B,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,uBAAuB,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,uBAAuB,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QACpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAC/C,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzC,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACnD,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAEnE,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,mBAAwB;QACjC,IAAI,CAAC,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,WAAW,GAAG,mBAAmB,CAAC,WAAW,CAAC;QACnD,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,mBAAmB,CAAC;IACvE,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,6BAA8B,SAAQ,qBAAqB;IACpE;;;;;OAKG;IACH,YACI,MAAM,GAAG,CAAC;IACV;;OAEG;IACI,aAAa,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxC;;OAEG;IACI,aAAa,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAExC,KAAK,CAAC,MAAM,CAAC,CAAC;QANP,eAAU,GAAV,UAAU,CAAuB;QAIjC,eAAU,GAAV,UAAU,CAAuB;IAG5C,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,WAAmB,EAAE,iBAA0B;QACzE,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACvE,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACvE,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACvE,OAAO,CAAC,8BAA8B,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;IAChG,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,MAAM,GAAG,IAAI,6BAA6B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAEhG,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAElC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,WAA8C;QAC/D,WAAW,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5C,WAAW,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACtD,WAAW,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACtD,WAAW,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED;;;OAGG;IACI,kBAAkB,CAAC,GAAkB;QACxC,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC5B,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAChC,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,sDAAsD,CAAC;IAClE,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,+BAA+B,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC3D,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAE3D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,mBAAwB;QACjC,KAAK,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACjC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QACzD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;IAC7D,CAAC;CACJ", "sourcesContent": ["import type { Matrix } from \"../../Maths/math.vector\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { <PERSON><PERSON><PERSON> } from \"../../Maths/math.scalar\";\r\nimport type { Particle } from \"../../Particles/particle\";\r\nimport type { IParticleEmitterType } from \"./IParticleEmitterType\";\r\nimport { DeepCopier } from \"../../Misc/deepCopier\";\r\nimport type { UniformBufferEffectCommonAccessor } from \"../../Materials/uniformBufferEffectCommonAccessor\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\n/**\r\n * Particle emitter emitting particles from the inside of a sphere.\r\n * It emits the particles alongside the sphere radius. The emission direction might be randomized.\r\n */\r\nexport class SphereParticleEmitter implements IParticleEmitterType {\r\n    /**\r\n     * Creates a new instance SphereParticleEmitter\r\n     * @param radius the radius of the emission sphere (1 by default)\r\n     * @param radiusRange the range of the emission sphere [0-1] 0 Surface only, 1 Entire Radius (1 by default)\r\n     * @param directionRandomizer defines how much to randomize the particle direction [0-1]\r\n     */\r\n    constructor(\r\n        /**\r\n         * The radius of the emission sphere.\r\n         */\r\n        public radius = 1,\r\n        /**\r\n         * The range of emission [0-1] 0 Surface only, 1 Entire Radius.\r\n         */\r\n        public radiusRange = 1,\r\n        /**\r\n         * How much to randomize the particle direction [0-1].\r\n         */\r\n        public directionRandomizer = 0\r\n    ) {}\r\n\r\n    /**\r\n     * Called by the particle System when the direction is computed for the created particle.\r\n     * @param worldMatrix is the world matrix of the particle system\r\n     * @param directionToUpdate is the direction vector to update with the result\r\n     * @param particle is the particle we are computed the direction for\r\n     * @param isLocal defines if the direction should be set in local space\r\n     */\r\n    public startDirectionFunction(worldMatrix: Matrix, directionToUpdate: Vector3, particle: Particle, isLocal: boolean): void {\r\n        const direction = particle.position.subtract(worldMatrix.getTranslation()).normalize();\r\n        const randX = Scalar.RandomRange(0, this.directionRandomizer);\r\n        const randY = Scalar.RandomRange(0, this.directionRandomizer);\r\n        const randZ = Scalar.RandomRange(0, this.directionRandomizer);\r\n        direction.x += randX;\r\n        direction.y += randY;\r\n        direction.z += randZ;\r\n        direction.normalize();\r\n\r\n        if (isLocal) {\r\n            directionToUpdate.copyFrom(direction);\r\n            return;\r\n        }\r\n\r\n        Vector3.TransformNormalFromFloatsToRef(direction.x, direction.y, direction.z, worldMatrix, directionToUpdate);\r\n    }\r\n\r\n    /**\r\n     * Called by the particle System when the position is computed for the created particle.\r\n     * @param worldMatrix is the world matrix of the particle system\r\n     * @param positionToUpdate is the position vector to update with the result\r\n     * @param particle is the particle we are computed the position for\r\n     * @param isLocal defines if the position should be set in local space\r\n     */\r\n    public startPositionFunction(worldMatrix: Matrix, positionToUpdate: Vector3, particle: Particle, isLocal: boolean): void {\r\n        const randRadius = this.radius - Scalar.RandomRange(0, this.radius * this.radiusRange);\r\n        const v = Scalar.RandomRange(0, 1.0);\r\n        const phi = Scalar.RandomRange(0, 2 * Math.PI);\r\n        const theta = Math.acos(2 * v - 1);\r\n        const randX = randRadius * Math.cos(phi) * Math.sin(theta);\r\n        const randY = randRadius * Math.cos(theta);\r\n        const randZ = randRadius * Math.sin(phi) * Math.sin(theta);\r\n\r\n        if (isLocal) {\r\n            positionToUpdate.copyFromFloats(randX, randY, randZ);\r\n            return;\r\n        }\r\n\r\n        Vector3.TransformCoordinatesFromFloatsToRef(randX, randY, randZ, worldMatrix, positionToUpdate);\r\n    }\r\n\r\n    /**\r\n     * Clones the current emitter and returns a copy of it\r\n     * @returns the new emitter\r\n     */\r\n    public clone(): SphereParticleEmitter {\r\n        const newOne = new SphereParticleEmitter(this.radius, this.directionRandomizer);\r\n\r\n        DeepCopier.DeepCopy(this, newOne);\r\n\r\n        return newOne;\r\n    }\r\n\r\n    /**\r\n     * Called by the GPUParticleSystem to setup the update shader\r\n     * @param uboOrEffect defines the update shader\r\n     */\r\n    public applyToShader(uboOrEffect: UniformBufferEffectCommonAccessor): void {\r\n        uboOrEffect.setFloat(\"radius\", this.radius);\r\n        uboOrEffect.setFloat(\"radiusRange\", this.radiusRange);\r\n        uboOrEffect.setFloat(\"directionRandomizer\", this.directionRandomizer);\r\n    }\r\n\r\n    /**\r\n     * Creates the structure of the ubo for this particle emitter\r\n     * @param ubo ubo to create the structure for\r\n     */\r\n    public buildUniformLayout(ubo: UniformBuffer): void {\r\n        ubo.addUniform(\"radius\", 1);\r\n        ubo.addUniform(\"radiusRange\", 1);\r\n        ubo.addUniform(\"directionRandomizer\", 1);\r\n    }\r\n\r\n    /**\r\n     * Returns a string to use to update the GPU particles update shader\r\n     * @returns a string containing the defines string\r\n     */\r\n    public getEffectDefines(): string {\r\n        return \"#define SPHEREEMITTER\";\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"SphereParticleEmitter\"\r\n     * @returns a string containing the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"SphereParticleEmitter\";\r\n    }\r\n\r\n    /**\r\n     * Serializes the particle system to a JSON object.\r\n     * @returns the JSON object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n        serializationObject.type = this.getClassName();\r\n        serializationObject.radius = this.radius;\r\n        serializationObject.radiusRange = this.radiusRange;\r\n        serializationObject.directionRandomizer = this.directionRandomizer;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parse properties from a JSON object\r\n     * @param serializationObject defines the JSON object\r\n     */\r\n    public parse(serializationObject: any): void {\r\n        this.radius = serializationObject.radius;\r\n        this.radiusRange = serializationObject.radiusRange;\r\n        this.directionRandomizer = serializationObject.directionRandomizer;\r\n    }\r\n}\r\n\r\n/**\r\n * Particle emitter emitting particles from the inside of a sphere.\r\n * It emits the particles randomly between two vectors.\r\n */\r\nexport class SphereDirectedParticleEmitter extends SphereParticleEmitter {\r\n    /**\r\n     * Creates a new instance SphereDirectedParticleEmitter\r\n     * @param radius the radius of the emission sphere (1 by default)\r\n     * @param direction1 the min limit of the emission direction (up vector by default)\r\n     * @param direction2 the max limit of the emission direction (up vector by default)\r\n     */\r\n    constructor(\r\n        radius = 1,\r\n        /**\r\n         * The min limit of the emission direction.\r\n         */\r\n        public direction1 = new Vector3(0, 1, 0),\r\n        /**\r\n         * The max limit of the emission direction.\r\n         */\r\n        public direction2 = new Vector3(0, 1, 0)\r\n    ) {\r\n        super(radius);\r\n    }\r\n\r\n    /**\r\n     * Called by the particle System when the direction is computed for the created particle.\r\n     * @param worldMatrix is the world matrix of the particle system\r\n     * @param directionToUpdate is the direction vector to update with the result\r\n     */\r\n    public startDirectionFunction(worldMatrix: Matrix, directionToUpdate: Vector3): void {\r\n        const randX = Scalar.RandomRange(this.direction1.x, this.direction2.x);\r\n        const randY = Scalar.RandomRange(this.direction1.y, this.direction2.y);\r\n        const randZ = Scalar.RandomRange(this.direction1.z, this.direction2.z);\r\n        Vector3.TransformNormalFromFloatsToRef(randX, randY, randZ, worldMatrix, directionToUpdate);\r\n    }\r\n\r\n    /**\r\n     * Clones the current emitter and returns a copy of it\r\n     * @returns the new emitter\r\n     */\r\n    public clone(): SphereDirectedParticleEmitter {\r\n        const newOne = new SphereDirectedParticleEmitter(this.radius, this.direction1, this.direction2);\r\n\r\n        DeepCopier.DeepCopy(this, newOne);\r\n\r\n        return newOne;\r\n    }\r\n\r\n    /**\r\n     * Called by the GPUParticleSystem to setup the update shader\r\n     * @param uboOrEffect defines the update shader\r\n     */\r\n    public applyToShader(uboOrEffect: UniformBufferEffectCommonAccessor): void {\r\n        uboOrEffect.setFloat(\"radius\", this.radius);\r\n        uboOrEffect.setFloat(\"radiusRange\", this.radiusRange);\r\n        uboOrEffect.setVector3(\"direction1\", this.direction1);\r\n        uboOrEffect.setVector3(\"direction2\", this.direction2);\r\n    }\r\n\r\n    /**\r\n     * Creates the structure of the ubo for this particle emitter\r\n     * @param ubo ubo to create the structure for\r\n     */\r\n    public buildUniformLayout(ubo: UniformBuffer): void {\r\n        ubo.addUniform(\"radius\", 1);\r\n        ubo.addUniform(\"radiusRange\", 1);\r\n        ubo.addUniform(\"direction1\", 3);\r\n        ubo.addUniform(\"direction2\", 3);\r\n    }\r\n\r\n    /**\r\n     * Returns a string to use to update the GPU particles update shader\r\n     * @returns a string containing the defines string\r\n     */\r\n    public getEffectDefines(): string {\r\n        return \"#define SPHEREEMITTER\\n#define DIRECTEDSPHEREEMITTER\";\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"SphereDirectedParticleEmitter\"\r\n     * @returns a string containing the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"SphereDirectedParticleEmitter\";\r\n    }\r\n\r\n    /**\r\n     * Serializes the particle system to a JSON object.\r\n     * @returns the JSON object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.direction1 = this.direction1.asArray();\r\n        serializationObject.direction2 = this.direction2.asArray();\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parse properties from a JSON object\r\n     * @param serializationObject defines the JSON object\r\n     */\r\n    public parse(serializationObject: any): void {\r\n        super.parse(serializationObject);\r\n        this.direction1.copyFrom(serializationObject.direction1);\r\n        this.direction2.copyFrom(serializationObject.direction2);\r\n    }\r\n}\r\n"]}