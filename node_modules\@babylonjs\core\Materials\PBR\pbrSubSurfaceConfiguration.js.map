{"version": 3, "file": "pbrSubSurfaceConfiguration.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/PBR/pbrSubSurfaceConfiguration.ts"], "names": [], "mappings": ";AAGA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AAC3G,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAIhD,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAGjD,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAEjD,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAErD,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAKrD,OAAO,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAE3F;;GAEG;AACH,MAAM,OAAO,yBAA0B,SAAQ,eAAe;IAA9D;;QACW,eAAU,GAAG,KAAK,CAAC;QAEnB,kBAAa,GAAG,KAAK,CAAC;QACtB,6CAAwC,GAAG,KAAK,CAAC;QACjD,oBAAe,GAAG,KAAK,CAAC;QACxB,+CAA0C,GAAG,KAAK,CAAC;QACnD,kBAAa,GAAG,KAAK,CAAC;QACtB,kBAAa,GAAG,KAAK,CAAC;QAEtB,gCAA2B,GAAG,KAAK,CAAC;QACpC,wCAAmC,GAAG,CAAC,CAAC;QACxC,qBAAgB,GAAG,KAAK,CAAC;QACzB,mCAA8B,GAAG,KAAK,CAAC;QACvC,2CAAsC,GAAG,CAAC,CAAC;QAC3C,qCAAgC,GAAG,KAAK,CAAC;QACzC,6CAAwC,GAAG,CAAC,CAAC;QAE7C,wBAAmB,GAAG,KAAK,CAAC;QAC5B,+BAA0B,GAAG,KAAK,CAAC;QACnC,4BAAuB,GAAG,KAAK,CAAC;QAChC,uBAAkB,GAAG,KAAK,CAAC;QAC3B,sBAAiB,GAAG,KAAK,CAAC;QAC1B,gCAA2B,GAAG,KAAK,CAAC;QACpC,oCAA+B,GAAG,KAAK,CAAC;QACxC,+BAA0B,GAAG,KAAK,CAAC;QACnC,iCAA4B,GAAG,KAAK,CAAC;QACrC,qCAAgC,GAAG,KAAK,CAAC;QACzC,8BAAyB,GAAG,KAAK,CAAC;QAElC,mCAA8B,GAAG,KAAK,CAAC;QACvC,yBAAoB,GAAG,KAAK,CAAC;IACxC,CAAC;CAAA;AAED;;GAEG;AACH,MAAM,OAAO,0BAA2B,SAAQ,kBAAkB;IAsC9D;;;OAGG;IACH,IAAW,0BAA0B;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE;YACtC,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,wBAAwB,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;IAC/G,CAAC;IAED,IAAW,0BAA0B,CAAC,CAAmB;QACrD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,0BAA0B,EAAE,EAAE;YAC3C,gBAAgB;YAChB,OAAO;SACV;QAED,uDAAuD;QACvD,IAAI,CAAC,EAAE;YACH,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAwB,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;SACvG;IACL,CAAC;IAmED;;;;;;OAMG;IAEH,IAAW,uBAAuB;QAC9B,IAAI,IAAI,CAAC,wBAAwB,IAAI,GAAG,EAAE;YACtC,OAAO,IAAI,CAAC,wBAAwB,CAAC;SACxC;QACD,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IACD,IAAW,uBAAuB,CAAC,KAAa;QAC5C,IAAI,KAAK,IAAI,GAAG,EAAE;YACd,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;SACzC;aAAM;YACH,IAAI,CAAC,wBAAwB,GAAG,CAAC,GAAG,CAAC;SACxC;IACL,CAAC;IA+GD,gBAAgB;IACT,gCAAgC;QACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACpG,IAAI,CAAC,wCAAwC,EAAE,CAAC;IACpD,CAAC;IACD,gBAAgB;IACT,sBAAsB;QACzB,IAAI,CAAC,wCAAwC,EAAE,CAAC;QAChD,IAAI,CAAC,8BAA8B,EAAE,CAAC;IAC1C,CAAC;IAED,YAAY,QAAyB,EAAE,eAAe,GAAG,IAAI;QACzD,KAAK,CAAC,QAAQ,EAAE,eAAe,EAAE,GAAG,EAAE,IAAI,yBAAyB,EAAE,EAAE,eAAe,CAAC,CAAC;QA3QpF,yBAAoB,GAAG,KAAK,CAAC;QACrC;;WAEG;QAGI,wBAAmB,GAAG,KAAK,CAAC;QAE3B,2BAAsB,GAAG,KAAK,CAAC;QACvC;;WAEG;QAGI,0BAAqB,GAAG,KAAK,CAAC;QAE7B,yBAAoB,GAAG,KAAK,CAAC;QACrC;;WAEG;QAGI,wBAAmB,GAAG,KAAK,CAAC;QAE3B,yBAAoB,GAAG,KAAK,CAAC;QACrC;;WAEG;QAGI,wBAAmB,GAAG,KAAK,CAAC;QAG3B,qCAAgC,GAAG,CAAC,CAAC;QA0B7C;;;;WAIG;QAEI,wBAAmB,GAAW,CAAC,CAAC;QAEvC;;;;WAIG;QAEI,0BAAqB,GAAW,CAAC,CAAC;QAEzC;;WAEG;QAEI,8BAAyB,GAAY,KAAK,CAAC;QAElD;;WAEG;QAEI,gCAA2B,GAAY,KAAK,CAAC;QAE5C,sBAAiB,GAA0B,IAAI,CAAC;QACxD;;;;;;WAMG;QAGI,qBAAgB,GAA0B,IAAI,CAAC;QAE9C,uBAAkB,GAA0B,IAAI,CAAC;QACzD;;WAEG;QAGI,sBAAiB,GAA0B,IAAI,CAAC;QAEvD,gBAAgB;QACT,uBAAkB,GAAG,GAAG,CAAC;QAChC;;;;;;;WAOG;QAGI,sBAAiB,GAAG,GAAG,CAAC;QAGvB,6BAAwB,GAAG,CAAC,GAAG,CAAC;QAwBhC,uBAAkB,GAAG,KAAK,CAAC;QACnC;;WAEG;QAGI,sBAAiB,GAAG,KAAK,CAAC;QAEjC,gBAAgB;QACT,oCAA+B,GAAG,KAAK,CAAC;QAC/C;;;WAGG;QAGI,mCAA8B,GAAG,KAAK,CAAC;QAE9C;;;WAGG;QAEI,qBAAgB,GAAW,CAAC,CAAC;QAEpC;;WAEG;QAEI,qBAAgB,GAAW,CAAC,CAAC;QAEpC;;WAEG;QAEI,wBAAmB,GAAG,KAAK,CAAC;QAEnC;;;WAGG;QAEI,cAAS,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAElC;;;WAGG;QAEI,wBAAmB,GAAG,CAAC,CAAC;QAE/B;;WAEG;QAEI,eAAU,GAAG,CAAC,CAAC;QAEtB;;;WAGG;QAEI,sBAAiB,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAElC,iCAA4B,GAAG,KAAK,CAAC;QAC7C;;;;;WAKG;QAGI,gCAA2B,GAAY,KAAK,CAAC;QAE5C,gCAA2B,GAA0B,IAAI,CAAC;QAClE;;;WAGG;QAGI,+BAA0B,GAA0B,IAAI,CAAC;QAExD,kCAA6B,GAA0B,IAAI,CAAC;QACpE;;;WAGG;QAGI,iCAA4B,GAA0B,IAAI,CAAC;QAG1D,0BAAqB,GAAG,KAAK,CAAC;QACtC;;;;;WAKG;QAGI,yBAAoB,GAAY,KAAK,CAAC;QAoBzC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;QAClC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QAEnC,IAAI,CAAC,wCAAwC,GAAG,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QAC9G,IAAI,CAAC,8BAA8B,GAAG,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IACxG,CAAC;IAEM,iBAAiB,CAAC,OAAkC,EAAE,KAAY;QACrE,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC1F,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,IAAI,KAAK,CAAC,eAAe,EAAE;gBACvB,IAAI,IAAI,CAAC,iBAAiB,IAAI,aAAa,CAAC,uBAAuB,EAAE;oBACjE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE;wBAChD,OAAO,KAAK,CAAC;qBAChB;iBACJ;gBAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;gBAC5D,IAAI,iBAAiB,IAAI,aAAa,CAAC,wBAAwB,EAAE;oBAC7D,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE;wBAC3C,OAAO,KAAK,CAAC;qBAChB;iBACJ;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,8BAA8B,CAAC,OAAkC,EAAE,KAAY;QAClF,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC1F,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;YAC3B,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;YAC9B,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;YAChC,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;YAC9B,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;YAC9B,OAAO,CAAC,wCAAwC,GAAG,KAAK,CAAC;YACzD,OAAO,CAAC,0CAA0C,GAAG,KAAK,CAAC;YAC3D,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;YAC5C,OAAO,CAAC,mCAAmC,GAAG,CAAC,CAAC;YAChD,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;YACjC,OAAO,CAAC,8BAA8B,GAAG,KAAK,CAAC;YAC/C,OAAO,CAAC,sCAAsC,GAAG,CAAC,CAAC;YACnD,OAAO,CAAC,gCAAgC,GAAG,KAAK,CAAC;YACjD,OAAO,CAAC,wCAAwC,GAAG,CAAC,CAAC;YACrD,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACpC,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC;YAC3C,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;YACxC,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;YACnC,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAClC,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;YAC5C,OAAO,CAAC,+BAA+B,GAAG,KAAK,CAAC;YAChD,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC;YAC3C,OAAO,CAAC,4BAA4B,GAAG,KAAK,CAAC;YAC7C,OAAO,CAAC,gCAAgC,GAAG,KAAK,CAAC;YACjD,OAAO,CAAC,yBAAyB,GAAG,KAAK,CAAC;YAC1C,OAAO,CAAC,8BAA8B,GAAG,KAAK,CAAC;YAC/C,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;YACrC,OAAO;SACV;QAED,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;YAE1B,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAClD,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC;YACtD,OAAO,CAAC,0CAA0C,GAAG,KAAK,CAAC;YAC3D,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAClD,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;YAC5C,OAAO,CAAC,8BAA8B,GAAG,KAAK,CAAC;YAC/C,OAAO,CAAC,gCAAgC,GAAG,KAAK,CAAC;YACjD,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;YACjC,OAAO,CAAC,8BAA8B,GAAG,KAAK,CAAC;YAC/C,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;YACrC,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;YAC9B,OAAO,CAAC,wCAAwC,GAAG,KAAK,CAAC;YACzD,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACpC,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;YACnC,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAClC,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;YAC5C,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC;YAC3C,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;YACxC,OAAO,CAAC,+BAA+B,GAAG,KAAK,CAAC;YAChD,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC;YAC3C,OAAO,CAAC,4BAA4B,GAAG,KAAK,CAAC;YAC7C,OAAO,CAAC,gCAAgC,GAAG,KAAK,CAAC;YACjD,OAAO,CAAC,yBAAyB,GAAG,KAAK,CAAC;YAE1C,MAAM,4CAA4C,GAC9C,CAAC,CAAC,IAAI,CAAC,iBAAiB;gBACxB,CAAC,CAAC,IAAI,CAAC,2BAA2B;gBAClC,IAAI,CAAC,2BAA2B,CAAC,2BAA2B,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACpF,IAAI,CAAC,2BAA2B,CAAC,QAAQ,KAAK,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YAElF,MAAM,8CAA8C,GAChD,CAAC,CAAC,IAAI,CAAC,iBAAiB;gBACxB,CAAC,CAAC,IAAI,CAAC,6BAA6B;gBACpC,IAAI,CAAC,6BAA6B,CAAC,2BAA2B,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBACtF,IAAI,CAAC,6BAA6B,CAAC,QAAQ,KAAK,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YAEpF,sKAAsK;YACtK,MAAM,uBAAuB,GACzB,CAAC,4CAA4C,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC;gBACnF,CAAC,8CAA8C,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAE5F,IAAI,OAAO,CAAC,iBAAiB,EAAE;gBAC3B,IAAI,KAAK,CAAC,eAAe,EAAE;oBACvB,IAAI,IAAI,CAAC,iBAAiB,IAAI,aAAa,CAAC,uBAAuB,EAAE;wBACjE,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,6BAA6B,CAAC,CAAC;qBAC7F;oBAED,IAAI,IAAI,CAAC,2BAA2B,IAAI,aAAa,CAAC,iCAAiC,IAAI,CAAC,uBAAuB,EAAE;wBACjH,yBAAyB,CAAC,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,gCAAgC,CAAC,CAAC;qBAC1G;oBAED,IAAI,IAAI,CAAC,6BAA6B,IAAI,aAAa,CAAC,mCAAmC,IAAI,CAAC,uBAAuB,EAAE;wBACrH,yBAAyB,CAAC,IAAI,CAAC,6BAA6B,EAAE,OAAO,EAAE,kCAAkC,CAAC,CAAC;qBAC9G;iBACJ;aACJ;YAED,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,KAAK,GAAG,CAAC;YACjF,OAAO,CAAC,8BAA8B;gBAClC,CAAC,IAAI,CAAC,4BAA4B,IAAI,CAAC,CAAC,IAAI,CAAC,2BAA2B,IAAI,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,IAAI,uBAAuB,CAAC;YACjJ,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC1D,OAAO,CAAC,wCAAwC,GAAG,CAAC,IAAI,CAAC,4BAA4B,IAAI,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,IAAI,uBAAuB,CAAC;YACxJ,OAAO,CAAC,0CAA0C,GAAG,CAAC,IAAI,CAAC,4BAA4B,IAAI,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,IAAI,uBAAuB,CAAC;YAE5J,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,IAAI,KAAK,CAAC,eAAe,EAAE;oBACvB,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;oBAC5D,IAAI,iBAAiB,IAAI,aAAa,CAAC,wBAAwB,EAAE;wBAC7D,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;wBAC7B,OAAO,CAAC,mBAAmB,GAAG,iBAAiB,CAAC,MAAM,CAAC;wBACvD,OAAO,CAAC,kBAAkB,GAAG,iBAAiB,CAAC,UAAU,CAAC;wBAC1D,OAAO,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAAC;wBACrD,OAAO,CAAC,2BAA2B,GAAG,iBAAiB,CAAC,iBAAiB,CAAC;wBAC1E,OAAO,CAAC,0BAA0B,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC;wBAC3J,OAAO,CAAC,uBAAuB,GAAG,iBAAiB,CAAC,eAAe,CAAC;wBACpE,OAAO,CAAC,+BAA+B,GAAG,IAAI,CAAC,+BAA+B,CAAC;wBAC/E,OAAO,CAAC,0BAA0B,GAAG,IAAI,CAAC,yBAAyB,CAAC;wBACpE,OAAO,CAAC,gCAAgC,GAAG,iBAAiB,CAAC,MAAM,IAAU,iBAAkB,CAAC,eAAe,CAAC;wBAChH,OAAO,CAAC,yBAAyB,GAAG,IAAI,CAAC,mBAAmB,CAAC;qBAChE;iBACJ;aACJ;YAED,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,OAAO,CAAC,4BAA4B,GAAG,IAAI,CAAC,2BAA2B,CAAC;aAC3E;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,kBAAkB,CAAC,aAA4B,EAAE,KAAY,EAAE,MAAc,EAAE,OAAgB;QAClG,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC1F,OAAO;SACV;QAED,OAAO,CAAC,gBAAgB,EAAE,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7E,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzI,aAAa,CAAC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,GAAG,cAAc,EAAE,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,cAAc,CAAC,CAAC;IAC5J,CAAC;IAEM,cAAc,CAAC,aAA4B,EAAE,KAAY,EAAE,MAAc,EAAE,OAAgB;QAC9F,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC1F,OAAO;SACV;QAED,MAAM,OAAO,GAAG,OAAQ,CAAC,eAAuD,CAAC;QAEjF,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QACzC,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;QAC3D,MAAM,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;QAE1D,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAE5D,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YAC7D,IAAI,IAAI,CAAC,iBAAiB,IAAI,aAAa,CAAC,uBAAuB,EAAE;gBACjE,aAAa,CAAC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBACrH,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;aACzE;YAED,IAAI,IAAI,CAAC,2BAA2B,IAAI,aAAa,CAAC,iCAAiC,IAAI,OAAO,CAAC,8BAA8B,EAAE;gBAC/H,aAAa,CAAC,YAAY,CAAC,2BAA2B,EAAE,IAAI,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;gBACnJ,iBAAiB,CAAC,IAAI,CAAC,2BAA2B,EAAE,aAAa,EAAE,qBAAqB,CAAC,CAAC;aAC7F;YAED,IAAI,IAAI,CAAC,6BAA6B,IAAI,aAAa,CAAC,mCAAmC,IAAI,OAAO,CAAC,gCAAgC,EAAE;gBACrI,aAAa,CAAC,YAAY,CAAC,6BAA6B,EAAE,IAAI,CAAC,6BAA6B,CAAC,gBAAgB,EAAE,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;gBACzJ,iBAAiB,CAAC,IAAI,CAAC,6BAA6B,EAAE,aAAa,EAAE,uBAAuB,CAAC,CAAC;aACjG;YAED,IAAI,iBAAiB,IAAI,aAAa,CAAC,wBAAwB,EAAE;gBAC7D,aAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,0BAA0B,EAAE,CAAC,CAAC;gBAE/F,IAAI,KAAK,GAAG,GAAG,CAAC;gBAChB,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;oBAC3B,IAAU,iBAAkB,CAAC,KAAK,EAAE;wBAChC,KAAK,GAAS,iBAAkB,CAAC,KAAK,CAAC;qBAC1C;iBACJ;gBAED,MAAM,KAAK,GAAG,iBAAiB,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;gBAChD,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC;gBACnD,aAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,KAAK,EAAE,CAAC,GAAG,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpI,aAAa,CAAC,YAAY,CACtB,8BAA8B,EAC9B,KAAK,EACL,iBAAiB,CAAC,kBAAkB,EACpC,iBAAiB,CAAC,mBAAmB,EACrC,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAC/B,CAAC;gBAEF,IAAI,iBAAiB,EAAE;oBACnB,aAAa,CAAC,YAAY,CAAC,0BAA0B,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;iBACrF;gBAED,IAAU,iBAAkB,CAAC,eAAe,EAAE;oBAC1C,MAAM,WAAW,GAAgB,iBAAiB,CAAC;oBAEnD,aAAa,CAAC,aAAa,CAAC,qBAAqB,EAAE,WAAW,CAAC,mBAAmB,CAAC,CAAC;oBACpF,aAAa,CAAC,aAAa,CAAC,iBAAiB,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;iBAC/E;aACJ;YAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,aAAa,CAAC,WAAW,CAAC,4BAA4B,EAAE,IAAI,CAAC,gCAAgC,CAAC,CAAC;aAClG;YACD,aAAa,CAAC,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAEzE,aAAa,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;YAE5I,aAAa,CAAC,YAAY,CAAC,sBAAsB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;YAE5G,aAAa,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;SAC5D;QAED,WAAW;QACX,IAAI,KAAK,CAAC,eAAe,EAAE;YACvB,IAAI,IAAI,CAAC,iBAAiB,IAAI,aAAa,CAAC,uBAAuB,EAAE;gBACjE,aAAa,CAAC,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;aACxE;YAED,IAAI,IAAI,CAAC,2BAA2B,IAAI,aAAa,CAAC,iCAAiC,IAAI,OAAO,CAAC,8BAA8B,EAAE;gBAC/H,aAAa,CAAC,UAAU,CAAC,4BAA4B,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;aAC5F;YAED,IAAI,IAAI,CAAC,6BAA6B,IAAI,aAAa,CAAC,mCAAmC,IAAI,OAAO,CAAC,gCAAgC,EAAE;gBACrI,aAAa,CAAC,UAAU,CAAC,8BAA8B,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAC;aAChG;YAED,IAAI,iBAAiB,IAAI,aAAa,CAAC,wBAAwB,EAAE;gBAC7D,IAAI,oBAAoB,EAAE;oBACtB,aAAa,CAAC,UAAU,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;iBACpE;qBAAM;oBACH,aAAa,CAAC,UAAU,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,cAAc,IAAI,iBAAiB,CAAC,CAAC;oBACrG,aAAa,CAAC,UAAU,CAAC,sBAAsB,EAAE,iBAAiB,CAAC,cAAc,IAAI,iBAAiB,CAAC,CAAC;oBACxG,aAAa,CAAC,UAAU,CAAC,uBAAuB,EAAE,iBAAiB,CAAC,eAAe,IAAI,iBAAiB,CAAC,CAAC;iBAC7G;aACJ;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACK,qBAAqB,CAAC,KAAY;QACtC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,OAAO,IAAI,CAAC,kBAAkB,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,OAAO,KAAK,CAAC,kBAAkB,CAAC;SACnC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,+BAA+B,CAAC;IAC7E,CAAC;IAED;;;OAGG;IACI,wBAAwB,CAAC,aAA8C;QAC1E,IAAI,aAAa,CAAC,wBAAwB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE;YAC7G,aAAa,CAAC,IAAI,CAAsB,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACpE;IACL,CAAC;IAEM,UAAU,CAAC,OAAoB;QAClC,IAAI,IAAI,CAAC,iBAAiB,KAAK,OAAO,EAAE;YACpC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,EAAE;YACrC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,2BAA2B,KAAK,OAAO,EAAE;YAC9C,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,6BAA6B,KAAK,OAAO,EAAE;YAChD,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,uBAAuB;QAC1B,IAAI,aAAa,CAAC,wBAAwB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE;YAC7G,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,iBAAiB,CAAC,cAA6B;QAClD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC/C;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAChD;IACL,CAAC;IAEM,cAAc,CAAC,WAA0B;QAC5C,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7G,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC5C;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAChH,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC7C;IACL,CAAC;IAEM,OAAO,CAAC,oBAA8B;QACzC,IAAI,oBAAoB,EAAE;YACtB,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;aACpC;YAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;aACrC;SACJ;IACL,CAAC;IAEM,YAAY;QACf,OAAO,4BAA4B,CAAC;IACxC,CAAC;IAEM,YAAY,CAAC,OAAkC,EAAE,SAA0B,EAAE,WAAmB;QACnG,IAAI,OAAO,CAAC,aAAa,EAAE;YACvB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,eAAe,CAAC,CAAC;SACzD;QACD,IAAI,OAAO,CAAC,eAAe,EAAE;YACzB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,iBAAiB,CAAC,CAAC;SAC3D;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAEM,WAAW,CAAC,QAAkB;QACjC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,4BAA4B,EAAE,8BAA8B,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,uBAAuB,CAAC,CAAC;IAC1K,CAAC;IAEM,WAAW;QACd,OAAO;YACH,GAAG,EAAE;gBACD,EAAE,IAAI,EAAE,8BAA8B,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC/D,EAAE,IAAI,EAAE,0BAA0B,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC3D,EAAE,IAAI,EAAE,6BAA6B,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC9D,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBACnD,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBACpD,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAClD,EAAE,IAAI,EAAE,2BAA2B,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC5D,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBACnD,EAAE,IAAI,EAAE,2BAA2B,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC7D,EAAE,IAAI,EAAE,6BAA6B,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC/D,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAClD,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBACrD,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC7C,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBACvD,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBACtD,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAClD,EAAE,IAAI,EAAE,4BAA4B,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;gBAC9D,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;aACjD;SACJ,CAAC;IACN,CAAC;CACJ;AAnqBU;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;uEAClB;AAQ5B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;yEAChB;AAQ9B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;uEAClB;AAQ5B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,wBAAwB,CAAC;uEACR;AAG3B;IADP,SAAS,EAAE;oFACiC;AAgCtC;IADN,SAAS,EAAE;uEAC2B;AAQhC;IADN,SAAS,EAAE;yEAC6B;AAMlC;IADN,SAAS,EAAE;6EACsC;AAM3C;IADN,SAAS,EAAE;+EACwC;AAY7C;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;oEACC;AAQ/C;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;qEACE;AAchD;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;qEACtB;AAGvB;IADP,SAAS,EAAE;4EAC4B;AAUxC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;yEAMpD;AAeM;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;qEACpB;AAU1B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;kFACP;AAOvC;IADN,SAAS,EAAE;oEACwB;AAM7B;IADN,SAAS,EAAE;oEACwB;AAM7B;IADN,SAAS,EAAE;uEACuB;AAO5B;IADN,iBAAiB,EAAE;6DACc;AAO3B;IADN,SAAS,EAAE;uEACmB;AAMxB;IADN,SAAS,EAAE;8DACU;AAOf;IADN,iBAAiB,EAAE;qEACsB;AAWnC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;+EACD;AAS7C;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;8EACW;AASzD;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;gFACa;AAY3D;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;wEACR", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"../../types\";\r\nimport type { IAnimatable } from \"../../Animations/animatable.interface\";\r\nimport { serialize, serializeAsTexture, expandToProperty, serializeAsColor3 } from \"../../Misc/decorators\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport type { SmartArray } from \"../../Misc/smartArray\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport type { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\nimport { MaterialFlags } from \"../materialFlags\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport type { EffectFallbacks } from \"../effectFallbacks\";\r\nimport { Scalar } from \"../../Maths/math.scalar\";\r\nimport type { CubeTexture } from \"../Textures/cubeTexture\";\r\nimport { TmpVectors } from \"../../Maths/math.vector\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport { MaterialPluginBase } from \"../materialPluginBase\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { MaterialDefines } from \"../materialDefines\";\r\n\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport { BindTextureMatrix, PrepareDefinesForMergedUV } from \"../materialHelper.functions\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class MaterialSubSurfaceDefines extends MaterialDefines {\r\n    public SUBSURFACE = false;\r\n\r\n    public SS_REFRACTION = false;\r\n    public SS_REFRACTION_USE_INTENSITY_FROM_TEXTURE = false;\r\n    public SS_TRANSLUCENCY = false;\r\n    public SS_TRANSLUCENCY_USE_INTENSITY_FROM_TEXTURE = false;\r\n    public SS_SCATTERING = false;\r\n    public SS_DISPERSION = false;\r\n\r\n    public SS_THICKNESSANDMASK_TEXTURE = false;\r\n    public SS_THICKNESSANDMASK_TEXTUREDIRECTUV = 0;\r\n    public SS_HAS_THICKNESS = false;\r\n    public SS_REFRACTIONINTENSITY_TEXTURE = false;\r\n    public SS_REFRACTIONINTENSITY_TEXTUREDIRECTUV = 0;\r\n    public SS_TRANSLUCENCYINTENSITY_TEXTURE = false;\r\n    public SS_TRANSLUCENCYINTENSITY_TEXTUREDIRECTUV = 0;\r\n\r\n    public SS_REFRACTIONMAP_3D = false;\r\n    public SS_REFRACTIONMAP_OPPOSITEZ = false;\r\n    public SS_LODINREFRACTIONALPHA = false;\r\n    public SS_GAMMAREFRACTION = false;\r\n    public SS_RGBDREFRACTION = false;\r\n    public SS_LINEARSPECULARREFRACTION = false;\r\n    public SS_LINKREFRACTIONTOTRANSPARENCY = false;\r\n    public SS_ALBEDOFORREFRACTIONTINT = false;\r\n    public SS_ALBEDOFORTRANSLUCENCYTINT = false;\r\n    public SS_USE_LOCAL_REFRACTIONMAP_CUBIC = false;\r\n    public SS_USE_THICKNESS_AS_DEPTH = false;\r\n\r\n    public SS_MASK_FROM_THICKNESS_TEXTURE = false;\r\n    public SS_USE_GLTF_TEXTURES = false;\r\n}\r\n\r\n/**\r\n * Plugin that implements the sub surface component of the PBR material\r\n */\r\nexport class PBRSubSurfaceConfiguration extends MaterialPluginBase {\r\n    protected _material: PBRBaseMaterial;\r\n\r\n    private _isRefractionEnabled = false;\r\n    /**\r\n     * Defines if the refraction is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isRefractionEnabled = false;\r\n\r\n    private _isTranslucencyEnabled = false;\r\n    /**\r\n     * Defines if the translucency is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isTranslucencyEnabled = false;\r\n\r\n    private _isDispersionEnabled = false;\r\n    /**\r\n     * Defines if dispersion is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isDispersionEnabled = false;\r\n\r\n    private _isScatteringEnabled = false;\r\n    /**\r\n     * Defines if the sub surface scattering is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markScenePrePassDirty\")\r\n    public isScatteringEnabled = false;\r\n\r\n    @serialize()\r\n    private _scatteringDiffusionProfileIndex = 0;\r\n\r\n    /**\r\n     * Diffusion profile for subsurface scattering.\r\n     * Useful for better scattering in the skins or foliages.\r\n     */\r\n    public get scatteringDiffusionProfile(): Nullable<Color3> {\r\n        if (!this._scene.subSurfaceConfiguration) {\r\n            return null;\r\n        }\r\n\r\n        return this._scene.subSurfaceConfiguration.ssDiffusionProfileColors[this._scatteringDiffusionProfileIndex];\r\n    }\r\n\r\n    public set scatteringDiffusionProfile(c: Nullable<Color3>) {\r\n        if (!this._scene.enableSubSurfaceForPrePass()) {\r\n            // Not supported\r\n            return;\r\n        }\r\n\r\n        // addDiffusionProfile automatically checks for doubles\r\n        if (c) {\r\n            this._scatteringDiffusionProfileIndex = this._scene.subSurfaceConfiguration!.addDiffusionProfile(c);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Defines the refraction intensity of the material.\r\n     * The refraction when enabled replaces the Diffuse part of the material.\r\n     * The intensity helps transitioning between diffuse and refraction.\r\n     */\r\n    @serialize()\r\n    public refractionIntensity: number = 1;\r\n\r\n    /**\r\n     * Defines the translucency intensity of the material.\r\n     * When translucency has been enabled, this defines how much of the \"translucency\"\r\n     * is added to the diffuse part of the material.\r\n     */\r\n    @serialize()\r\n    public translucencyIntensity: number = 1;\r\n\r\n    /**\r\n     * When enabled, transparent surfaces will be tinted with the albedo colour (independent of thickness)\r\n     */\r\n    @serialize()\r\n    public useAlbedoToTintRefraction: boolean = false;\r\n\r\n    /**\r\n     * When enabled, translucent surfaces will be tinted with the albedo colour (independent of thickness)\r\n     */\r\n    @serialize()\r\n    public useAlbedoToTintTranslucency: boolean = false;\r\n\r\n    private _thicknessTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the average thickness of a mesh in a texture (The texture is holding the values linearly).\r\n     * The red (or green if useGltfStyleTextures=true) channel of the texture should contain the thickness remapped between 0 and 1.\r\n     * 0 would mean minimumThickness\r\n     * 1 would mean maximumThickness\r\n     * The other channels might be use as a mask to vary the different effects intensity.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public thicknessTexture: Nullable<BaseTexture> = null;\r\n\r\n    private _refractionTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Defines the texture to use for refraction.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public refractionTexture: Nullable<BaseTexture> = null;\r\n\r\n    /** @internal */\r\n    public _indexOfRefraction = 1.5;\r\n    /**\r\n     * Index of refraction of the material base layer.\r\n     * https://en.wikipedia.org/wiki/List_of_refractive_indices\r\n     *\r\n     * This does not only impact refraction but also the Base F0 of Dielectric Materials.\r\n     *\r\n     * From dielectric fresnel rules: F0 = square((iorT - iorI) / (iorT + iorI))\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public indexOfRefraction = 1.5;\r\n\r\n    @serialize()\r\n    private _volumeIndexOfRefraction = -1.0;\r\n\r\n    /**\r\n     * Index of refraction of the material's volume.\r\n     * https://en.wikipedia.org/wiki/List_of_refractive_indices\r\n     *\r\n     * This ONLY impacts refraction. If not provided or given a non-valid value,\r\n     * the volume will use the same IOR as the surface.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public get volumeIndexOfRefraction(): number {\r\n        if (this._volumeIndexOfRefraction >= 1.0) {\r\n            return this._volumeIndexOfRefraction;\r\n        }\r\n        return this._indexOfRefraction;\r\n    }\r\n    public set volumeIndexOfRefraction(value: number) {\r\n        if (value >= 1.0) {\r\n            this._volumeIndexOfRefraction = value;\r\n        } else {\r\n            this._volumeIndexOfRefraction = -1.0;\r\n        }\r\n    }\r\n\r\n    private _invertRefractionY = false;\r\n    /**\r\n     * Controls if refraction needs to be inverted on Y. This could be useful for procedural texture.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public invertRefractionY = false;\r\n\r\n    /** @internal */\r\n    public _linkRefractionWithTransparency = false;\r\n    /**\r\n     * This parameters will make the material used its opacity to control how much it is refracting against not.\r\n     * Materials half opaque for instance using refraction could benefit from this control.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public linkRefractionWithTransparency = false;\r\n\r\n    /**\r\n     * Defines the minimum thickness stored in the thickness map.\r\n     * If no thickness map is defined, this value will be used to simulate thickness.\r\n     */\r\n    @serialize()\r\n    public minimumThickness: number = 0;\r\n\r\n    /**\r\n     * Defines the maximum thickness stored in the thickness map.\r\n     */\r\n    @serialize()\r\n    public maximumThickness: number = 1;\r\n\r\n    /**\r\n     * Defines that the thickness should be used as a measure of the depth volume.\r\n     */\r\n    @serialize()\r\n    public useThicknessAsDepth = false;\r\n\r\n    /**\r\n     * Defines the volume tint of the material.\r\n     * This is used for both translucency and scattering.\r\n     */\r\n    @serializeAsColor3()\r\n    public tintColor = Color3.White();\r\n\r\n    /**\r\n     * Defines the distance at which the tint color should be found in the media.\r\n     * This is used for refraction only.\r\n     */\r\n    @serialize()\r\n    public tintColorAtDistance = 1;\r\n\r\n    /**\r\n     * Defines the Abbe number for the volume.\r\n     */\r\n    @serialize()\r\n    public dispersion = 0;\r\n\r\n    /**\r\n     * Defines how far each channel transmit through the media.\r\n     * It is defined as a color to simplify it selection.\r\n     */\r\n    @serializeAsColor3()\r\n    public diffusionDistance = Color3.White();\r\n\r\n    private _useMaskFromThicknessTexture = false;\r\n    /**\r\n     * Stores the intensity of the different subsurface effects in the thickness texture.\r\n     * Note that if refractionIntensityTexture and/or translucencyIntensityTexture is provided it takes precedence over thicknessTexture + useMaskFromThicknessTexture\r\n     * * the green (red if useGltfStyleTextures = true) channel is the refraction intensity.\r\n     * * the blue channel is the translucency intensity.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useMaskFromThicknessTexture: boolean = false;\r\n\r\n    private _refractionIntensityTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the intensity of the refraction. If provided, it takes precedence over thicknessTexture + useMaskFromThicknessTexture\r\n     * * the green (red if useGltfStyleTextures = true) channel is the refraction intensity.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public refractionIntensityTexture: Nullable<BaseTexture> = null;\r\n\r\n    private _translucencyIntensityTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the intensity of the translucency. If provided, it takes precedence over thicknessTexture + useMaskFromThicknessTexture\r\n     * * the blue channel is the translucency intensity.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public translucencyIntensityTexture: Nullable<BaseTexture> = null;\r\n\r\n    private _scene: Scene;\r\n    private _useGltfStyleTextures = false;\r\n    /**\r\n     * Use channels layout used by glTF:\r\n     * * thicknessTexture: the green (instead of red) channel is the thickness\r\n     * * thicknessTexture/refractionIntensityTexture: the red (instead of green) channel is the refraction intensity\r\n     * * thicknessTexture/translucencyIntensityTexture: no change, use the blue channel for the translucency intensity\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useGltfStyleTextures: boolean = false;\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsTexturesDirty: () => void;\r\n    private _internalMarkScenePrePassDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsTexturesDirty(): void {\r\n        this._enable(this._isRefractionEnabled || this._isTranslucencyEnabled || this._isScatteringEnabled);\r\n        this._internalMarkAllSubMeshesAsTexturesDirty();\r\n    }\r\n    /** @internal */\r\n    public _markScenePrePassDirty(): void {\r\n        this._internalMarkAllSubMeshesAsTexturesDirty();\r\n        this._internalMarkScenePrePassDirty();\r\n    }\r\n\r\n    constructor(material: PBRBaseMaterial, addToPluginList = true) {\r\n        super(material, \"PBRSubSurface\", 130, new MaterialSubSurfaceDefines(), addToPluginList);\r\n\r\n        this._scene = material.getScene();\r\n        this.registerForExtraEvents = true;\r\n\r\n        this._internalMarkAllSubMeshesAsTexturesDirty = material._dirtyCallbacks[Constants.MATERIAL_TextureDirtyFlag];\r\n        this._internalMarkScenePrePassDirty = material._dirtyCallbacks[Constants.MATERIAL_PrePassDirtyFlag];\r\n    }\r\n\r\n    public isReadyForSubMesh(defines: MaterialSubSurfaceDefines, scene: Scene): boolean {\r\n        if (!this._isRefractionEnabled && !this._isTranslucencyEnabled && !this._isScatteringEnabled) {\r\n            return true;\r\n        }\r\n\r\n        if (defines._areTexturesDirty) {\r\n            if (scene.texturesEnabled) {\r\n                if (this._thicknessTexture && MaterialFlags.ThicknessTextureEnabled) {\r\n                    if (!this._thicknessTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                const refractionTexture = this._getRefractionTexture(scene);\r\n                if (refractionTexture && MaterialFlags.RefractionTextureEnabled) {\r\n                    if (!refractionTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public prepareDefinesBeforeAttributes(defines: MaterialSubSurfaceDefines, scene: Scene): void {\r\n        if (!this._isRefractionEnabled && !this._isTranslucencyEnabled && !this._isScatteringEnabled) {\r\n            defines.SUBSURFACE = false;\r\n            defines.SS_DISPERSION = false;\r\n            defines.SS_TRANSLUCENCY = false;\r\n            defines.SS_SCATTERING = false;\r\n            defines.SS_REFRACTION = false;\r\n            defines.SS_REFRACTION_USE_INTENSITY_FROM_TEXTURE = false;\r\n            defines.SS_TRANSLUCENCY_USE_INTENSITY_FROM_TEXTURE = false;\r\n            defines.SS_THICKNESSANDMASK_TEXTURE = false;\r\n            defines.SS_THICKNESSANDMASK_TEXTUREDIRECTUV = 0;\r\n            defines.SS_HAS_THICKNESS = false;\r\n            defines.SS_REFRACTIONINTENSITY_TEXTURE = false;\r\n            defines.SS_REFRACTIONINTENSITY_TEXTUREDIRECTUV = 0;\r\n            defines.SS_TRANSLUCENCYINTENSITY_TEXTURE = false;\r\n            defines.SS_TRANSLUCENCYINTENSITY_TEXTUREDIRECTUV = 0;\r\n            defines.SS_REFRACTIONMAP_3D = false;\r\n            defines.SS_REFRACTIONMAP_OPPOSITEZ = false;\r\n            defines.SS_LODINREFRACTIONALPHA = false;\r\n            defines.SS_GAMMAREFRACTION = false;\r\n            defines.SS_RGBDREFRACTION = false;\r\n            defines.SS_LINEARSPECULARREFRACTION = false;\r\n            defines.SS_LINKREFRACTIONTOTRANSPARENCY = false;\r\n            defines.SS_ALBEDOFORREFRACTIONTINT = false;\r\n            defines.SS_ALBEDOFORTRANSLUCENCYTINT = false;\r\n            defines.SS_USE_LOCAL_REFRACTIONMAP_CUBIC = false;\r\n            defines.SS_USE_THICKNESS_AS_DEPTH = false;\r\n            defines.SS_MASK_FROM_THICKNESS_TEXTURE = false;\r\n            defines.SS_USE_GLTF_TEXTURES = false;\r\n            return;\r\n        }\r\n\r\n        if (defines._areTexturesDirty) {\r\n            defines.SUBSURFACE = true;\r\n\r\n            defines.SS_DISPERSION = this._isDispersionEnabled;\r\n            defines.SS_TRANSLUCENCY = this._isTranslucencyEnabled;\r\n            defines.SS_TRANSLUCENCY_USE_INTENSITY_FROM_TEXTURE = false;\r\n            defines.SS_SCATTERING = this._isScatteringEnabled;\r\n            defines.SS_THICKNESSANDMASK_TEXTURE = false;\r\n            defines.SS_REFRACTIONINTENSITY_TEXTURE = false;\r\n            defines.SS_TRANSLUCENCYINTENSITY_TEXTURE = false;\r\n            defines.SS_HAS_THICKNESS = false;\r\n            defines.SS_MASK_FROM_THICKNESS_TEXTURE = false;\r\n            defines.SS_USE_GLTF_TEXTURES = false;\r\n            defines.SS_REFRACTION = false;\r\n            defines.SS_REFRACTION_USE_INTENSITY_FROM_TEXTURE = false;\r\n            defines.SS_REFRACTIONMAP_3D = false;\r\n            defines.SS_GAMMAREFRACTION = false;\r\n            defines.SS_RGBDREFRACTION = false;\r\n            defines.SS_LINEARSPECULARREFRACTION = false;\r\n            defines.SS_REFRACTIONMAP_OPPOSITEZ = false;\r\n            defines.SS_LODINREFRACTIONALPHA = false;\r\n            defines.SS_LINKREFRACTIONTOTRANSPARENCY = false;\r\n            defines.SS_ALBEDOFORREFRACTIONTINT = false;\r\n            defines.SS_ALBEDOFORTRANSLUCENCYTINT = false;\r\n            defines.SS_USE_LOCAL_REFRACTIONMAP_CUBIC = false;\r\n            defines.SS_USE_THICKNESS_AS_DEPTH = false;\r\n\r\n            const refractionIntensityTextureIsThicknessTexture =\r\n                !!this._thicknessTexture &&\r\n                !!this._refractionIntensityTexture &&\r\n                this._refractionIntensityTexture.checkTransformsAreIdentical(this._thicknessTexture) &&\r\n                this._refractionIntensityTexture._texture === this._thicknessTexture._texture;\r\n\r\n            const translucencyIntensityTextureIsThicknessTexture =\r\n                !!this._thicknessTexture &&\r\n                !!this._translucencyIntensityTexture &&\r\n                this._translucencyIntensityTexture.checkTransformsAreIdentical(this._thicknessTexture) &&\r\n                this._translucencyIntensityTexture._texture === this._thicknessTexture._texture;\r\n\r\n            // if true, it means the refraction/translucency textures are the same than the thickness texture so there's no need to pass them to the shader, only thicknessTexture\r\n            const useOnlyThicknessTexture =\r\n                (refractionIntensityTextureIsThicknessTexture || !this._refractionIntensityTexture) &&\r\n                (translucencyIntensityTextureIsThicknessTexture || !this._translucencyIntensityTexture);\r\n\r\n            if (defines._areTexturesDirty) {\r\n                if (scene.texturesEnabled) {\r\n                    if (this._thicknessTexture && MaterialFlags.ThicknessTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._thicknessTexture, defines, \"SS_THICKNESSANDMASK_TEXTURE\");\r\n                    }\r\n\r\n                    if (this._refractionIntensityTexture && MaterialFlags.RefractionIntensityTextureEnabled && !useOnlyThicknessTexture) {\r\n                        PrepareDefinesForMergedUV(this._refractionIntensityTexture, defines, \"SS_REFRACTIONINTENSITY_TEXTURE\");\r\n                    }\r\n\r\n                    if (this._translucencyIntensityTexture && MaterialFlags.TranslucencyIntensityTextureEnabled && !useOnlyThicknessTexture) {\r\n                        PrepareDefinesForMergedUV(this._translucencyIntensityTexture, defines, \"SS_TRANSLUCENCYINTENSITY_TEXTURE\");\r\n                    }\r\n                }\r\n            }\r\n\r\n            defines.SS_HAS_THICKNESS = this.maximumThickness - this.minimumThickness !== 0.0;\r\n            defines.SS_MASK_FROM_THICKNESS_TEXTURE =\r\n                (this._useMaskFromThicknessTexture || !!this._refractionIntensityTexture || !!this._translucencyIntensityTexture) && useOnlyThicknessTexture;\r\n            defines.SS_USE_GLTF_TEXTURES = this._useGltfStyleTextures;\r\n            defines.SS_REFRACTION_USE_INTENSITY_FROM_TEXTURE = (this._useMaskFromThicknessTexture || !!this._refractionIntensityTexture) && useOnlyThicknessTexture;\r\n            defines.SS_TRANSLUCENCY_USE_INTENSITY_FROM_TEXTURE = (this._useMaskFromThicknessTexture || !!this._translucencyIntensityTexture) && useOnlyThicknessTexture;\r\n\r\n            if (this._isRefractionEnabled) {\r\n                if (scene.texturesEnabled) {\r\n                    const refractionTexture = this._getRefractionTexture(scene);\r\n                    if (refractionTexture && MaterialFlags.RefractionTextureEnabled) {\r\n                        defines.SS_REFRACTION = true;\r\n                        defines.SS_REFRACTIONMAP_3D = refractionTexture.isCube;\r\n                        defines.SS_GAMMAREFRACTION = refractionTexture.gammaSpace;\r\n                        defines.SS_RGBDREFRACTION = refractionTexture.isRGBD;\r\n                        defines.SS_LINEARSPECULARREFRACTION = refractionTexture.linearSpecularLOD;\r\n                        defines.SS_REFRACTIONMAP_OPPOSITEZ = this._scene.useRightHandedSystem && refractionTexture.isCube ? !refractionTexture.invertZ : refractionTexture.invertZ;\r\n                        defines.SS_LODINREFRACTIONALPHA = refractionTexture.lodLevelInAlpha;\r\n                        defines.SS_LINKREFRACTIONTOTRANSPARENCY = this._linkRefractionWithTransparency;\r\n                        defines.SS_ALBEDOFORREFRACTIONTINT = this.useAlbedoToTintRefraction;\r\n                        defines.SS_USE_LOCAL_REFRACTIONMAP_CUBIC = refractionTexture.isCube && (<any>refractionTexture).boundingBoxSize;\r\n                        defines.SS_USE_THICKNESS_AS_DEPTH = this.useThicknessAsDepth;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (this._isTranslucencyEnabled) {\r\n                defines.SS_ALBEDOFORTRANSLUCENCYTINT = this.useAlbedoToTintTranslucency;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Binds the material data (this function is called even if mustRebind() returns false)\r\n     * @param uniformBuffer defines the Uniform buffer to fill in.\r\n     * @param scene defines the scene the material belongs to.\r\n     * @param engine defines the engine the material belongs to.\r\n     * @param subMesh the submesh to bind data for\r\n     */\r\n    public hardBindForSubMesh(uniformBuffer: UniformBuffer, scene: Scene, engine: Engine, subMesh: SubMesh): void {\r\n        if (!this._isRefractionEnabled && !this._isTranslucencyEnabled && !this._isScatteringEnabled) {\r\n            return;\r\n        }\r\n\r\n        subMesh.getRenderingMesh().getWorldMatrix().decompose(TmpVectors.Vector3[0]);\r\n\r\n        const thicknessScale = Math.max(Math.abs(TmpVectors.Vector3[0].x), Math.abs(TmpVectors.Vector3[0].y), Math.abs(TmpVectors.Vector3[0].z));\r\n\r\n        uniformBuffer.updateFloat2(\"vThicknessParam\", this.minimumThickness * thicknessScale, (this.maximumThickness - this.minimumThickness) * thicknessScale);\r\n    }\r\n\r\n    public bindForSubMesh(uniformBuffer: UniformBuffer, scene: Scene, engine: Engine, subMesh: SubMesh): void {\r\n        if (!this._isRefractionEnabled && !this._isTranslucencyEnabled && !this._isScatteringEnabled) {\r\n            return;\r\n        }\r\n\r\n        const defines = subMesh!.materialDefines as unknown as MaterialSubSurfaceDefines;\r\n\r\n        const isFrozen = this._material.isFrozen;\r\n        const realTimeFiltering = this._material.realTimeFiltering;\r\n        const lodBasedMicrosurface = defines.LODBASEDMICROSFURACE;\r\n\r\n        const refractionTexture = this._getRefractionTexture(scene);\r\n\r\n        if (!uniformBuffer.useUbo || !isFrozen || !uniformBuffer.isSync) {\r\n            if (this._thicknessTexture && MaterialFlags.ThicknessTextureEnabled) {\r\n                uniformBuffer.updateFloat2(\"vThicknessInfos\", this._thicknessTexture.coordinatesIndex, this._thicknessTexture.level);\r\n                BindTextureMatrix(this._thicknessTexture, uniformBuffer, \"thickness\");\r\n            }\r\n\r\n            if (this._refractionIntensityTexture && MaterialFlags.RefractionIntensityTextureEnabled && defines.SS_REFRACTIONINTENSITY_TEXTURE) {\r\n                uniformBuffer.updateFloat2(\"vRefractionIntensityInfos\", this._refractionIntensityTexture.coordinatesIndex, this._refractionIntensityTexture.level);\r\n                BindTextureMatrix(this._refractionIntensityTexture, uniformBuffer, \"refractionIntensity\");\r\n            }\r\n\r\n            if (this._translucencyIntensityTexture && MaterialFlags.TranslucencyIntensityTextureEnabled && defines.SS_TRANSLUCENCYINTENSITY_TEXTURE) {\r\n                uniformBuffer.updateFloat2(\"vTranslucencyIntensityInfos\", this._translucencyIntensityTexture.coordinatesIndex, this._translucencyIntensityTexture.level);\r\n                BindTextureMatrix(this._translucencyIntensityTexture, uniformBuffer, \"translucencyIntensity\");\r\n            }\r\n\r\n            if (refractionTexture && MaterialFlags.RefractionTextureEnabled) {\r\n                uniformBuffer.updateMatrix(\"refractionMatrix\", refractionTexture.getRefractionTextureMatrix());\r\n\r\n                let depth = 1.0;\r\n                if (!refractionTexture.isCube) {\r\n                    if ((<any>refractionTexture).depth) {\r\n                        depth = (<any>refractionTexture).depth;\r\n                    }\r\n                }\r\n\r\n                const width = refractionTexture.getSize().width;\r\n                const refractionIor = this.volumeIndexOfRefraction;\r\n                uniformBuffer.updateFloat4(\"vRefractionInfos\", refractionTexture.level, 1 / refractionIor, depth, this._invertRefractionY ? -1 : 1);\r\n                uniformBuffer.updateFloat4(\r\n                    \"vRefractionMicrosurfaceInfos\",\r\n                    width,\r\n                    refractionTexture.lodGenerationScale,\r\n                    refractionTexture.lodGenerationOffset,\r\n                    1.0 / this.indexOfRefraction\r\n                );\r\n\r\n                if (realTimeFiltering) {\r\n                    uniformBuffer.updateFloat2(\"vRefractionFilteringInfo\", width, Scalar.Log2(width));\r\n                }\r\n\r\n                if ((<any>refractionTexture).boundingBoxSize) {\r\n                    const cubeTexture = <CubeTexture>refractionTexture;\r\n\r\n                    uniformBuffer.updateVector3(\"vRefractionPosition\", cubeTexture.boundingBoxPosition);\r\n                    uniformBuffer.updateVector3(\"vRefractionSize\", cubeTexture.boundingBoxSize);\r\n                }\r\n            }\r\n\r\n            if (this._isScatteringEnabled) {\r\n                uniformBuffer.updateFloat(\"scatteringDiffusionProfile\", this._scatteringDiffusionProfileIndex);\r\n            }\r\n            uniformBuffer.updateColor3(\"vDiffusionDistance\", this.diffusionDistance);\r\n\r\n            uniformBuffer.updateFloat4(\"vTintColor\", this.tintColor.r, this.tintColor.g, this.tintColor.b, Math.max(0.00001, this.tintColorAtDistance));\r\n\r\n            uniformBuffer.updateFloat3(\"vSubSurfaceIntensity\", this.refractionIntensity, this.translucencyIntensity, 0);\r\n\r\n            uniformBuffer.updateFloat(\"dispersion\", this.dispersion);\r\n        }\r\n\r\n        // Textures\r\n        if (scene.texturesEnabled) {\r\n            if (this._thicknessTexture && MaterialFlags.ThicknessTextureEnabled) {\r\n                uniformBuffer.setTexture(\"thicknessSampler\", this._thicknessTexture);\r\n            }\r\n\r\n            if (this._refractionIntensityTexture && MaterialFlags.RefractionIntensityTextureEnabled && defines.SS_REFRACTIONINTENSITY_TEXTURE) {\r\n                uniformBuffer.setTexture(\"refractionIntensitySampler\", this._refractionIntensityTexture);\r\n            }\r\n\r\n            if (this._translucencyIntensityTexture && MaterialFlags.TranslucencyIntensityTextureEnabled && defines.SS_TRANSLUCENCYINTENSITY_TEXTURE) {\r\n                uniformBuffer.setTexture(\"translucencyIntensitySampler\", this._translucencyIntensityTexture);\r\n            }\r\n\r\n            if (refractionTexture && MaterialFlags.RefractionTextureEnabled) {\r\n                if (lodBasedMicrosurface) {\r\n                    uniformBuffer.setTexture(\"refractionSampler\", refractionTexture);\r\n                } else {\r\n                    uniformBuffer.setTexture(\"refractionSampler\", refractionTexture._lodTextureMid || refractionTexture);\r\n                    uniformBuffer.setTexture(\"refractionSamplerLow\", refractionTexture._lodTextureLow || refractionTexture);\r\n                    uniformBuffer.setTexture(\"refractionSamplerHigh\", refractionTexture._lodTextureHigh || refractionTexture);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the texture used for refraction or null if none is used.\r\n     * @param scene defines the scene the material belongs to.\r\n     * @returns - Refraction texture if present.  If no refraction texture and refraction\r\n     * is linked with transparency, returns environment texture.  Otherwise, returns null.\r\n     */\r\n    private _getRefractionTexture(scene: Scene): Nullable<BaseTexture> {\r\n        if (this._refractionTexture) {\r\n            return this._refractionTexture;\r\n        }\r\n\r\n        if (this._isRefractionEnabled) {\r\n            return scene.environmentTexture;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Returns true if alpha blending should be disabled.\r\n     */\r\n    public get disableAlphaBlending(): boolean {\r\n        return this._isRefractionEnabled && this._linkRefractionWithTransparency;\r\n    }\r\n\r\n    /**\r\n     * Fills the list of render target textures.\r\n     * @param renderTargets the list of render targets to update\r\n     */\r\n    public fillRenderTargetTextures(renderTargets: SmartArray<RenderTargetTexture>): void {\r\n        if (MaterialFlags.RefractionTextureEnabled && this._refractionTexture && this._refractionTexture.isRenderTarget) {\r\n            renderTargets.push(<RenderTargetTexture>this._refractionTexture);\r\n        }\r\n    }\r\n\r\n    public hasTexture(texture: BaseTexture): boolean {\r\n        if (this._thicknessTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._refractionTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._refractionIntensityTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._translucencyIntensityTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public hasRenderTargetTextures(): boolean {\r\n        if (MaterialFlags.RefractionTextureEnabled && this._refractionTexture && this._refractionTexture.isRenderTarget) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public getActiveTextures(activeTextures: BaseTexture[]): void {\r\n        if (this._thicknessTexture) {\r\n            activeTextures.push(this._thicknessTexture);\r\n        }\r\n\r\n        if (this._refractionTexture) {\r\n            activeTextures.push(this._refractionTexture);\r\n        }\r\n    }\r\n\r\n    public getAnimatables(animatables: IAnimatable[]): void {\r\n        if (this._thicknessTexture && this._thicknessTexture.animations && this._thicknessTexture.animations.length > 0) {\r\n            animatables.push(this._thicknessTexture);\r\n        }\r\n\r\n        if (this._refractionTexture && this._refractionTexture.animations && this._refractionTexture.animations.length > 0) {\r\n            animatables.push(this._refractionTexture);\r\n        }\r\n    }\r\n\r\n    public dispose(forceDisposeTextures?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            if (this._thicknessTexture) {\r\n                this._thicknessTexture.dispose();\r\n            }\r\n\r\n            if (this._refractionTexture) {\r\n                this._refractionTexture.dispose();\r\n            }\r\n        }\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"PBRSubSurfaceConfiguration\";\r\n    }\r\n\r\n    public addFallbacks(defines: MaterialSubSurfaceDefines, fallbacks: EffectFallbacks, currentRank: number): number {\r\n        if (defines.SS_SCATTERING) {\r\n            fallbacks.addFallback(currentRank++, \"SS_SCATTERING\");\r\n        }\r\n        if (defines.SS_TRANSLUCENCY) {\r\n            fallbacks.addFallback(currentRank++, \"SS_TRANSLUCENCY\");\r\n        }\r\n        return currentRank;\r\n    }\r\n\r\n    public getSamplers(samplers: string[]): void {\r\n        samplers.push(\"thicknessSampler\", \"refractionIntensitySampler\", \"translucencyIntensitySampler\", \"refractionSampler\", \"refractionSamplerLow\", \"refractionSamplerHigh\");\r\n    }\r\n\r\n    public getUniforms(): { ubo?: Array<{ name: string; size: number; type: string }>; vertex?: string; fragment?: string } {\r\n        return {\r\n            ubo: [\r\n                { name: \"vRefractionMicrosurfaceInfos\", size: 4, type: \"vec4\" },\r\n                { name: \"vRefractionFilteringInfo\", size: 2, type: \"vec2\" },\r\n                { name: \"vTranslucencyIntensityInfos\", size: 2, type: \"vec2\" },\r\n                { name: \"vRefractionInfos\", size: 4, type: \"vec4\" },\r\n                { name: \"refractionMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"vThicknessInfos\", size: 2, type: \"vec2\" },\r\n                { name: \"vRefractionIntensityInfos\", size: 2, type: \"vec2\" },\r\n                { name: \"thicknessMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"refractionIntensityMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"translucencyIntensityMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"vThicknessParam\", size: 2, type: \"vec2\" },\r\n                { name: \"vDiffusionDistance\", size: 3, type: \"vec3\" },\r\n                { name: \"vTintColor\", size: 4, type: \"vec4\" },\r\n                { name: \"vSubSurfaceIntensity\", size: 3, type: \"vec3\" },\r\n                { name: \"vRefractionPosition\", size: 3, type: \"vec3\" },\r\n                { name: \"vRefractionSize\", size: 3, type: \"vec3\" },\r\n                { name: \"scatteringDiffusionProfile\", size: 1, type: \"float\" },\r\n                { name: \"dispersion\", size: 1, type: \"float\" },\r\n            ],\r\n        };\r\n    }\r\n}\r\n"]}