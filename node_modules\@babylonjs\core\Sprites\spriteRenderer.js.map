{"version": 3, "file": "spriteRenderer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Sprites/spriteRenderer.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAIjD,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACzD,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAOvD,OAAO,oCAAoC,CAAC;AAC5C,OAAO,4CAA4C,CAAC;AAEpD,OAAO,6BAA6B,CAAC;AACrC,OAAO,2BAA2B,CAAC;AAEnC;;;;GAIG;AACH,MAAM,OAAO,cAAc;IAyCvB;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAID;;;OAGG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,YAAY,CAAC,KAAc;QAClC,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,EAAE;YAC9B,OAAO;SACV;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAsBD;;;;;;OAMG;IACH,YAAY,MAAkB,EAAE,QAAgB,EAAE,UAAkB,IAAI,EAAE,QAAyB,IAAI;QA9EvG;;;;WAIG;QACI,cAAS,GAAG,SAAS,CAAC,aAAa,CAAC;QAE3C;;;WAGG;QACI,mBAAc,GAAG,IAAI,CAAC;QAE7B;;;;WAIG;QACI,sBAAiB,GAAY,KAAK,CAAC;QAE1C;;WAEG;QACI,eAAU,GAAG,IAAI,CAAC;QASjB,kBAAa,GAAG,KAAK,CAAC;QAoBb,YAAO,GAAY,KAAK,CAAC;QACzB,mBAAc,GAAY,KAAK,CAAC;QASzC,mBAAc,GAAoC,EAAE,CAAC;QAiBzD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,eAAe,IAAI,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC;QACnG,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,iBAAiB,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC;QACvF,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;QAED,MAAM;QACN,wKAAwK;QACxK,0BAA0B;QAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACvD,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvG,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAElF,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAChI,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAE9G,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,OAAqB,CAAC;QAE1B,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC9D,IAAI,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YAC9D,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACpE;aAAM;YACH,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAC7G,MAAM,IAAI,CAAC,CAAC;SACf;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACnH,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,UAAU,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACzH,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAEnI,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC;QAC3D,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;QACzC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;QACzC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;QACzC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;QAC3C,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;QAErD,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAEO,cAAc;QAClB,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;QAClC,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;QAErC,IAAI,CAAC,gBAAgB,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,eAAe,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrD,IAAI,CAAC,iBAAiB,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC9D,IAAI,CAAC,oBAAoB,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEjE,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE;YACnC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;SACzE;QACD,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE;YAClC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;SACxE;QACD,IAAI,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;YACpC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;SAC1E;QACD,IAAI,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE;YACvC,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;SAC7E;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,EAAE,CAAC;QAEpE,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CACpD,SAAS,EACT,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,CAAC,SAAS,CAAC,EAChG,CAAC,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,CAAC,EACnD,CAAC,gBAAgB,CAAC,EAClB,OAAO,CACV,CAAC;QAEF,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;QAC7D,IAAI,CAAC,iBAAiB,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;QAE/E,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;iBACpC,SAAS,EAAE;iBACX,YAAY,CACT,SAAS,EACT,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,CAAC,SAAS,CAAC,EAChG,CAAC,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,EAC7E,CAAC,gBAAgB,CAAC,EAClB,OAAO,GAAG,aAAa,CAC1B,CAAC;YACN,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YAC/D,IAAI,CAAC,oBAAoB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC;SACpF;IACL,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CACT,OAAqB,EACrB,SAAiB,EACjB,UAAuB,EACvB,gBAA6B,EAC7B,qBAA8E,IAAI;QAElF,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YAC7D,OAAO;SACV;QAED,IAAI,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACxC,IAAI,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC9C,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,CAAC,EAAE;YACvF,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;YACnC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAC7C,eAAe,GAAG,IAAI,CAAC;SAC1B;QAED,MAAM,MAAM,GAAG,WAAW,CAAC,MAAO,CAAC;QAEnC,QAAQ;QACR,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;YACnB,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,MAAM,oBAAoB,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACjF,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAE5C,UAAU;QACV,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAErD,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,GAAG,EAAE,KAAK,EAAE,EAAE;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAC9B,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;gBAC9B,SAAS;aACZ;YAED,QAAQ,GAAG,KAAK,CAAC;YACjB,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAE3B,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;YACrG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;gBACrG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;gBACrG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;aACxG;SACJ;QAED,IAAI,QAAQ,EAAE;YACV,OAAO;SACV;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEtC,MAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC;QAChD,MAAM,OAAO,GAAG,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC;QACjD,MAAM,YAAY,GAAG,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC;QAE3D,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QAEpF,SAAS;QACT,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAEjC,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QACrC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;QAEjD,aAAa;QACb,IAAI,eAAe,EAAE;YACjB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAO,CAAC;YAE3B,MAAM;YACN,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;YAC7F,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;SACjD;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC1B,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;aAC5G;YACD,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;SAC5E;aAAM;YACH,OAAO;YACP,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;SACtE;QAED,aAAa;QACb,MAAM,CAAC,iBAAiB,CAAC,SAAS,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC;QACxG,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC5B,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;YACtC,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,8BAA8B,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;aACjF;iBAAM;gBACH,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aACrF;YACD,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACjC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC3B,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;SACtC;QAED,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpC,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,8BAA8B,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;SACjF;aAAM;YACH,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SACrF;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;SAChD;QAED,uBAAuB;QACvB,IAAI,oBAAoB,EAAE;YACtB,IAAI,CAAC,MAAO,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;SACxG;QAED,MAAM,CAAC,wBAAwB,EAAE,CAAC;IACtC,CAAC;IAEO,mBAAmB,CACvB,KAAa,EACb,MAAkB,EAClB,OAAe,EACf,OAAe,EACf,QAAe,EACf,oBAA6B,EAC7B,kBAA2E;QAE3E,IAAI,WAAW,GAAG,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAEjD,IAAI,OAAO,KAAK,CAAC,EAAE;YACf,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;SAC3B;aAAM,IAAI,OAAO,KAAK,CAAC,EAAE;YACtB,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;SAC/B;QAED,IAAI,OAAO,KAAK,CAAC,EAAE;YACf,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;SAC3B;aAAM,IAAI,OAAO,KAAK,CAAC,EAAE;YACtB,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;SAC/B;QAED,IAAI,kBAAkB,EAAE;YACpB,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACxC;aAAM;YACH,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;gBACnB,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;aACxB;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;YAChD,MAAM,MAAM,GAAG,CAAC,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC;YAC5F,MAAM,CAAC,QAAQ,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC/D,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;YAC/B,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;SACnC;QAED,YAAY;QACZ,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;QACjD,UAAU;QACV,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;QACjD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAElD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;YAC5C,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;SAC/C;aAAM;YACH,WAAW,IAAI,CAAC,CAAC;SACpB;QAED,oCAAoC;QACpC,IAAI,oBAAoB,EAAE;YACtB,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9D;aAAM;YACH,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9D;QAED,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3D,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;QACrD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;QACrD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC;QACpE,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAErE,QAAQ;QACR,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACxD,CAAC;IAEO,iBAAiB;QACrB,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;YACjD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxB,KAAK,IAAI,CAAC,CAAC;SACd;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,kBAAkB,GAAG,SAAgB,CAAC;SAC9C;QAED,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QAExB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,cAAc,EAAE;YACnC,MAAM,YAAY,GAAiB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAC5D,YAAY,CAAC,QAAQ,EAAE,CAAC;SAC3B;QAED,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAQ,GAAG,IAAI,CAAC;SAC9B;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,aAAc,GAAG,IAAI,CAAC;SACpC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACzC,IAAI,CAAC,YAAa,GAAG,IAAI,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACzD,IAAI,CAAC,kBAAmB,GAAG,IAAI,CAAC;SACzC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAQ,GAAG,IAAI,CAAC;SAC9B;QACD,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QAC/B,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { IMatrixLike } from \"../Maths/math.like\";\r\nimport type { ThinEngine } from \"../Engines/thinEngine\";\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport { Buffer, VertexBuffer } from \"../Buffers/buffer\";\r\nimport { DrawWrapper } from \"../Materials/drawWrapper\";\r\nimport type { ThinSprite } from \"./thinSprite\";\r\nimport type { ISize } from \"../Maths/math.size\";\r\n\r\nimport type { ThinTexture } from \"../Materials/Textures/thinTexture\";\r\nimport type { Scene } from \"../scene\";\r\n\r\nimport \"../Engines/Extensions/engine.alpha\";\r\nimport \"../Engines/Extensions/engine.dynamicBuffer\";\r\n\r\nimport \"../Shaders/sprites.fragment\";\r\nimport \"../Shaders/sprites.vertex\";\r\n\r\n/**\r\n * Class used to render sprites.\r\n *\r\n * It can be used either to render Sprites or ThinSprites with ThinEngine only.\r\n */\r\nexport class SpriteRenderer {\r\n    /**\r\n     * Defines the texture of the spritesheet\r\n     */\r\n    public texture: Nullable<ThinTexture>;\r\n\r\n    /**\r\n     * Defines the default width of a cell in the spritesheet\r\n     */\r\n    public cellWidth: number;\r\n\r\n    /**\r\n     * Defines the default height of a cell in the spritesheet\r\n     */\r\n    public cellHeight: number;\r\n\r\n    /**\r\n     * Blend mode use to render the particle, it can be any of\r\n     * the static Constants.ALPHA_x properties provided in this class.\r\n     * Default value is Constants.ALPHA_COMBINE\r\n     */\r\n    public blendMode = Constants.ALPHA_COMBINE;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if alpha mode is automatically\r\n     * reset.\r\n     */\r\n    public autoResetAlpha = true;\r\n\r\n    /**\r\n     * Disables writing to the depth buffer when rendering the sprites.\r\n     * It can be handy to disable depth writing when using textures without alpha channel\r\n     * and setting some specific blend modes.\r\n     */\r\n    public disableDepthWrite: boolean = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the manager must consider scene fog when rendering\r\n     */\r\n    public fogEnabled = true;\r\n\r\n    /**\r\n     * Gets the capacity of the manager\r\n     */\r\n    public get capacity() {\r\n        return this._capacity;\r\n    }\r\n\r\n    private _pixelPerfect = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the renderer must render sprites with pixel perfect rendering\r\n     * Note that pixel perfect mode is not supported in WebGL 1\r\n     */\r\n    public get pixelPerfect() {\r\n        return this._pixelPerfect;\r\n    }\r\n\r\n    public set pixelPerfect(value: boolean) {\r\n        if (this._pixelPerfect === value) {\r\n            return;\r\n        }\r\n\r\n        this._pixelPerfect = value;\r\n        this._createEffects();\r\n    }\r\n\r\n    private readonly _engine: ThinEngine;\r\n    private readonly _useVAO: boolean = false;\r\n    private readonly _useInstancing: boolean = false;\r\n    private readonly _scene: Nullable<Scene>;\r\n\r\n    private readonly _capacity: number;\r\n    private readonly _epsilon: number;\r\n\r\n    private _vertexBufferSize: number;\r\n    private _vertexData: Float32Array;\r\n    private _buffer: Buffer;\r\n    private _vertexBuffers: { [key: string]: VertexBuffer } = {};\r\n    private _spriteBuffer: Nullable<Buffer>;\r\n    private _indexBuffer: DataBuffer;\r\n    private _drawWrapperBase: DrawWrapper;\r\n    private _drawWrapperFog: DrawWrapper;\r\n    private _drawWrapperDepth: DrawWrapper;\r\n    private _drawWrapperFogDepth: DrawWrapper;\r\n    private _vertexArrayObject: WebGLVertexArrayObject;\r\n\r\n    /**\r\n     * Creates a new sprite Renderer\r\n     * @param engine defines the engine the renderer works with\r\n     * @param capacity defines the maximum allowed number of sprites\r\n     * @param epsilon defines the epsilon value to align texture (0.01 by default)\r\n     * @param scene defines the hosting scene\r\n     */\r\n    constructor(engine: ThinEngine, capacity: number, epsilon: number = 0.01, scene: Nullable<Scene> = null) {\r\n        this._capacity = capacity;\r\n        this._epsilon = epsilon;\r\n\r\n        this._engine = engine;\r\n        this._useInstancing = engine.getCaps().instancedArrays && engine._features.supportSpriteInstancing;\r\n        this._useVAO = engine.getCaps().vertexArrayObject && !engine.disableVertexArrayObjects;\r\n        this._scene = scene;\r\n\r\n        if (!this._useInstancing) {\r\n            this._buildIndexBuffer();\r\n        }\r\n\r\n        // VBO\r\n        // 18 floats per sprite (x, y, z, angle, sizeX, sizeY, offsetX, offsetY, invertU, invertV, cellLeft, cellTop, cellWidth, cellHeight, color r, color g, color b, color a)\r\n        // 16 when using instances\r\n        this._vertexBufferSize = this._useInstancing ? 16 : 18;\r\n        this._vertexData = new Float32Array(capacity * this._vertexBufferSize * (this._useInstancing ? 1 : 4));\r\n        this._buffer = new Buffer(engine, this._vertexData, true, this._vertexBufferSize);\r\n\r\n        const positions = this._buffer.createVertexBuffer(VertexBuffer.PositionKind, 0, 4, this._vertexBufferSize, this._useInstancing);\r\n        const options = this._buffer.createVertexBuffer(\"options\", 4, 2, this._vertexBufferSize, this._useInstancing);\r\n\r\n        let offset = 6;\r\n        let offsets: VertexBuffer;\r\n\r\n        if (this._useInstancing) {\r\n            const spriteData = new Float32Array([0, 0, 1, 0, 0, 1, 1, 1]);\r\n            this._spriteBuffer = new Buffer(engine, spriteData, false, 2);\r\n            offsets = this._spriteBuffer.createVertexBuffer(\"offsets\", 0, 2);\r\n        } else {\r\n            offsets = this._buffer.createVertexBuffer(\"offsets\", offset, 2, this._vertexBufferSize, this._useInstancing);\r\n            offset += 2;\r\n        }\r\n\r\n        const inverts = this._buffer.createVertexBuffer(\"inverts\", offset, 2, this._vertexBufferSize, this._useInstancing);\r\n        const cellInfo = this._buffer.createVertexBuffer(\"cellInfo\", offset + 2, 4, this._vertexBufferSize, this._useInstancing);\r\n        const colors = this._buffer.createVertexBuffer(VertexBuffer.ColorKind, offset + 6, 4, this._vertexBufferSize, this._useInstancing);\r\n\r\n        this._vertexBuffers[VertexBuffer.PositionKind] = positions;\r\n        this._vertexBuffers[\"options\"] = options;\r\n        this._vertexBuffers[\"offsets\"] = offsets;\r\n        this._vertexBuffers[\"inverts\"] = inverts;\r\n        this._vertexBuffers[\"cellInfo\"] = cellInfo;\r\n        this._vertexBuffers[VertexBuffer.ColorKind] = colors;\r\n\r\n        this._createEffects();\r\n    }\r\n\r\n    private _createEffects() {\r\n        this._drawWrapperBase?.dispose();\r\n        this._drawWrapperFog?.dispose();\r\n        this._drawWrapperDepth?.dispose();\r\n        this._drawWrapperFogDepth?.dispose();\r\n\r\n        this._drawWrapperBase = new DrawWrapper(this._engine);\r\n        this._drawWrapperFog = new DrawWrapper(this._engine);\r\n        this._drawWrapperDepth = new DrawWrapper(this._engine, false);\r\n        this._drawWrapperFogDepth = new DrawWrapper(this._engine, false);\r\n\r\n        if (this._drawWrapperBase.drawContext) {\r\n            this._drawWrapperBase.drawContext.useInstancing = this._useInstancing;\r\n        }\r\n        if (this._drawWrapperFog.drawContext) {\r\n            this._drawWrapperFog.drawContext.useInstancing = this._useInstancing;\r\n        }\r\n        if (this._drawWrapperDepth.drawContext) {\r\n            this._drawWrapperDepth.drawContext.useInstancing = this._useInstancing;\r\n        }\r\n        if (this._drawWrapperFogDepth.drawContext) {\r\n            this._drawWrapperFogDepth.drawContext.useInstancing = this._useInstancing;\r\n        }\r\n\r\n        const defines = this._pixelPerfect ? \"#define PIXEL_PERFECT\\n\" : \"\";\r\n\r\n        this._drawWrapperBase.effect = this._engine.createEffect(\r\n            \"sprites\",\r\n            [VertexBuffer.PositionKind, \"options\", \"offsets\", \"inverts\", \"cellInfo\", VertexBuffer.ColorKind],\r\n            [\"view\", \"projection\", \"textureInfos\", \"alphaTest\"],\r\n            [\"diffuseSampler\"],\r\n            defines\r\n        );\r\n\r\n        this._drawWrapperDepth.effect = this._drawWrapperBase.effect;\r\n        this._drawWrapperDepth.materialContext = this._drawWrapperBase.materialContext;\r\n\r\n        if (this._scene) {\r\n            this._drawWrapperFog.effect = this._scene\r\n                .getEngine()\r\n                .createEffect(\r\n                    \"sprites\",\r\n                    [VertexBuffer.PositionKind, \"options\", \"offsets\", \"inverts\", \"cellInfo\", VertexBuffer.ColorKind],\r\n                    [\"view\", \"projection\", \"textureInfos\", \"alphaTest\", \"vFogInfos\", \"vFogColor\"],\r\n                    [\"diffuseSampler\"],\r\n                    defines + \"#define FOG\"\r\n                );\r\n            this._drawWrapperFogDepth.effect = this._drawWrapperFog.effect;\r\n            this._drawWrapperFogDepth.materialContext = this._drawWrapperFog.materialContext;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Render all child sprites\r\n     * @param sprites defines the list of sprites to render\r\n     * @param deltaTime defines the time since last frame\r\n     * @param viewMatrix defines the viewMatrix to use to render the sprites\r\n     * @param projectionMatrix defines the projectionMatrix to use to render the sprites\r\n     * @param customSpriteUpdate defines a custom function to update the sprites data before they render\r\n     */\r\n    public render(\r\n        sprites: ThinSprite[],\r\n        deltaTime: number,\r\n        viewMatrix: IMatrixLike,\r\n        projectionMatrix: IMatrixLike,\r\n        customSpriteUpdate: Nullable<(sprite: ThinSprite, baseSize: ISize) => void> = null\r\n    ): void {\r\n        if (!this.texture || !this.texture.isReady() || !sprites.length) {\r\n            return;\r\n        }\r\n\r\n        let drawWrapper = this._drawWrapperBase;\r\n        let drawWrapperDepth = this._drawWrapperDepth;\r\n        let shouldRenderFog = false;\r\n        if (this.fogEnabled && this._scene && this._scene.fogEnabled && this._scene.fogMode !== 0) {\r\n            drawWrapper = this._drawWrapperFog;\r\n            drawWrapperDepth = this._drawWrapperFogDepth;\r\n            shouldRenderFog = true;\r\n        }\r\n\r\n        const effect = drawWrapper.effect!;\r\n\r\n        // Check\r\n        if (!effect.isReady()) {\r\n            return;\r\n        }\r\n\r\n        const engine = this._engine;\r\n        const useRightHandedSystem = !!(this._scene && this._scene.useRightHandedSystem);\r\n        const baseSize = this.texture.getBaseSize();\r\n\r\n        // Sprites\r\n        const max = Math.min(this._capacity, sprites.length);\r\n\r\n        let offset = 0;\r\n        let noSprite = true;\r\n        for (let index = 0; index < max; index++) {\r\n            const sprite = sprites[index];\r\n            if (!sprite || !sprite.isVisible) {\r\n                continue;\r\n            }\r\n\r\n            noSprite = false;\r\n            sprite._animate(deltaTime);\r\n\r\n            this._appendSpriteVertex(offset++, sprite, 0, 0, baseSize, useRightHandedSystem, customSpriteUpdate);\r\n            if (!this._useInstancing) {\r\n                this._appendSpriteVertex(offset++, sprite, 1, 0, baseSize, useRightHandedSystem, customSpriteUpdate);\r\n                this._appendSpriteVertex(offset++, sprite, 1, 1, baseSize, useRightHandedSystem, customSpriteUpdate);\r\n                this._appendSpriteVertex(offset++, sprite, 0, 1, baseSize, useRightHandedSystem, customSpriteUpdate);\r\n            }\r\n        }\r\n\r\n        if (noSprite) {\r\n            return;\r\n        }\r\n\r\n        this._buffer.update(this._vertexData);\r\n\r\n        const culling = !!engine.depthCullingState.cull;\r\n        const zOffset = engine.depthCullingState.zOffset;\r\n        const zOffsetUnits = engine.depthCullingState.zOffsetUnits;\r\n\r\n        engine.setState(culling, zOffset, false, false, undefined, undefined, zOffsetUnits);\r\n\r\n        // Render\r\n        engine.enableEffect(drawWrapper);\r\n\r\n        effect.setTexture(\"diffuseSampler\", this.texture);\r\n        effect.setMatrix(\"view\", viewMatrix);\r\n        effect.setMatrix(\"projection\", projectionMatrix);\r\n\r\n        // Scene Info\r\n        if (shouldRenderFog) {\r\n            const scene = this._scene!;\r\n\r\n            // Fog\r\n            effect.setFloat4(\"vFogInfos\", scene.fogMode, scene.fogStart, scene.fogEnd, scene.fogDensity);\r\n            effect.setColor3(\"vFogColor\", scene.fogColor);\r\n        }\r\n\r\n        if (this._useVAO) {\r\n            if (!this._vertexArrayObject) {\r\n                this._vertexArrayObject = engine.recordVertexArrayObject(this._vertexBuffers, this._indexBuffer, effect);\r\n            }\r\n            engine.bindVertexArrayObject(this._vertexArrayObject, this._indexBuffer);\r\n        } else {\r\n            // VBOs\r\n            engine.bindBuffers(this._vertexBuffers, this._indexBuffer, effect);\r\n        }\r\n\r\n        // Draw order\r\n        engine.depthCullingState.depthFunc = engine.useReverseDepthBuffer ? Constants.GEQUAL : Constants.LEQUAL;\r\n        if (!this.disableDepthWrite) {\r\n            effect.setBool(\"alphaTest\", true);\r\n            engine.setColorWrite(false);\r\n            engine.enableEffect(drawWrapperDepth);\r\n            if (this._useInstancing) {\r\n                engine.drawArraysType(Constants.MATERIAL_TriangleStripDrawMode, 0, 4, offset);\r\n            } else {\r\n                engine.drawElementsType(Constants.MATERIAL_TriangleFillMode, 0, (offset / 4) * 6);\r\n            }\r\n            engine.enableEffect(drawWrapper);\r\n            engine.setColorWrite(true);\r\n            effect.setBool(\"alphaTest\", false);\r\n        }\r\n\r\n        engine.setAlphaMode(this.blendMode);\r\n        if (this._useInstancing) {\r\n            engine.drawArraysType(Constants.MATERIAL_TriangleStripDrawMode, 0, 4, offset);\r\n        } else {\r\n            engine.drawElementsType(Constants.MATERIAL_TriangleFillMode, 0, (offset / 4) * 6);\r\n        }\r\n\r\n        if (this.autoResetAlpha) {\r\n            engine.setAlphaMode(Constants.ALPHA_DISABLE);\r\n        }\r\n\r\n        // Restore Right Handed\r\n        if (useRightHandedSystem) {\r\n            this._scene!.getEngine().setState(culling, zOffset, false, true, undefined, undefined, zOffsetUnits);\r\n        }\r\n\r\n        engine.unbindInstanceAttributes();\r\n    }\r\n\r\n    private _appendSpriteVertex(\r\n        index: number,\r\n        sprite: ThinSprite,\r\n        offsetX: number,\r\n        offsetY: number,\r\n        baseSize: ISize,\r\n        useRightHandedSystem: boolean,\r\n        customSpriteUpdate: Nullable<(sprite: ThinSprite, baseSize: ISize) => void>\r\n    ): void {\r\n        let arrayOffset = index * this._vertexBufferSize;\r\n\r\n        if (offsetX === 0) {\r\n            offsetX = this._epsilon;\r\n        } else if (offsetX === 1) {\r\n            offsetX = 1 - this._epsilon;\r\n        }\r\n\r\n        if (offsetY === 0) {\r\n            offsetY = this._epsilon;\r\n        } else if (offsetY === 1) {\r\n            offsetY = 1 - this._epsilon;\r\n        }\r\n\r\n        if (customSpriteUpdate) {\r\n            customSpriteUpdate(sprite, baseSize);\r\n        } else {\r\n            if (!sprite.cellIndex) {\r\n                sprite.cellIndex = 0;\r\n            }\r\n\r\n            const rowSize = baseSize.width / this.cellWidth;\r\n            const offset = (sprite.cellIndex / rowSize) >> 0;\r\n            sprite._xOffset = ((sprite.cellIndex - offset * rowSize) * this.cellWidth) / baseSize.width;\r\n            sprite._yOffset = (offset * this.cellHeight) / baseSize.height;\r\n            sprite._xSize = this.cellWidth;\r\n            sprite._ySize = this.cellHeight;\r\n        }\r\n\r\n        // Positions\r\n        this._vertexData[arrayOffset] = sprite.position.x;\r\n        this._vertexData[arrayOffset + 1] = sprite.position.y;\r\n        this._vertexData[arrayOffset + 2] = sprite.position.z;\r\n        this._vertexData[arrayOffset + 3] = sprite.angle;\r\n        // Options\r\n        this._vertexData[arrayOffset + 4] = sprite.width;\r\n        this._vertexData[arrayOffset + 5] = sprite.height;\r\n\r\n        if (!this._useInstancing) {\r\n            this._vertexData[arrayOffset + 6] = offsetX;\r\n            this._vertexData[arrayOffset + 7] = offsetY;\r\n        } else {\r\n            arrayOffset -= 2;\r\n        }\r\n\r\n        // Inverts according to Right Handed\r\n        if (useRightHandedSystem) {\r\n            this._vertexData[arrayOffset + 8] = sprite.invertU ? 0 : 1;\r\n        } else {\r\n            this._vertexData[arrayOffset + 8] = sprite.invertU ? 1 : 0;\r\n        }\r\n\r\n        this._vertexData[arrayOffset + 9] = sprite.invertV ? 1 : 0;\r\n\r\n        this._vertexData[arrayOffset + 10] = sprite._xOffset;\r\n        this._vertexData[arrayOffset + 11] = sprite._yOffset;\r\n        this._vertexData[arrayOffset + 12] = sprite._xSize / baseSize.width;\r\n        this._vertexData[arrayOffset + 13] = sprite._ySize / baseSize.height;\r\n\r\n        // Color\r\n        this._vertexData[arrayOffset + 14] = sprite.color.r;\r\n        this._vertexData[arrayOffset + 15] = sprite.color.g;\r\n        this._vertexData[arrayOffset + 16] = sprite.color.b;\r\n        this._vertexData[arrayOffset + 17] = sprite.color.a;\r\n    }\r\n\r\n    private _buildIndexBuffer(): void {\r\n        const indices = [];\r\n        let index = 0;\r\n        for (let count = 0; count < this._capacity; count++) {\r\n            indices.push(index);\r\n            indices.push(index + 1);\r\n            indices.push(index + 2);\r\n            indices.push(index);\r\n            indices.push(index + 2);\r\n            indices.push(index + 3);\r\n            index += 4;\r\n        }\r\n\r\n        this._indexBuffer = this._engine.createIndexBuffer(indices);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the renderer (after a context lost, for eg)\r\n     */\r\n    public rebuild(): void {\r\n        if (this._indexBuffer) {\r\n            this._buildIndexBuffer();\r\n        }\r\n\r\n        if (this._useVAO) {\r\n            this._vertexArrayObject = undefined as any;\r\n        }\r\n\r\n        this._buffer._rebuild();\r\n\r\n        for (const key in this._vertexBuffers) {\r\n            const vertexBuffer = <VertexBuffer>this._vertexBuffers[key];\r\n            vertexBuffer._rebuild();\r\n        }\r\n\r\n        this._spriteBuffer?._rebuild();\r\n    }\r\n\r\n    /**\r\n     * Release associated resources\r\n     */\r\n    public dispose(): void {\r\n        if (this._buffer) {\r\n            this._buffer.dispose();\r\n            (<any>this._buffer) = null;\r\n        }\r\n\r\n        if (this._spriteBuffer) {\r\n            this._spriteBuffer.dispose();\r\n            (<any>this._spriteBuffer) = null;\r\n        }\r\n\r\n        if (this._indexBuffer) {\r\n            this._engine._releaseBuffer(this._indexBuffer);\r\n            (<any>this._indexBuffer) = null;\r\n        }\r\n\r\n        if (this._vertexArrayObject) {\r\n            this._engine.releaseVertexArrayObject(this._vertexArrayObject);\r\n            (<any>this._vertexArrayObject) = null;\r\n        }\r\n\r\n        if (this.texture) {\r\n            this.texture.dispose();\r\n            (<any>this.texture) = null;\r\n        }\r\n        this._drawWrapperBase.dispose();\r\n        this._drawWrapperFog.dispose();\r\n        this._drawWrapperDepth.dispose();\r\n        this._drawWrapperFogDepth.dispose();\r\n    }\r\n}\r\n"]}