import type { Matrix } from "../../Maths/math.vector";
import { Vector3 } from "../../Maths/math.vector";
import type { Particle } from "../../Particles/particle";
import type { IParticleEmitterType } from "./IParticleEmitterType";
import type { UniformBufferEffectCommonAccessor } from "../../Materials/uniformBufferEffectCommonAccessor";
import type { UniformBuffer } from "../../Materials/uniformBuffer";
/**
 * Particle emitter emitting particles from the inside of a cone.
 * It emits the particles alongside the cone volume from the base to the particle.
 * The emission direction might be randomized.
 */
export declare class ConeParticleEmitter implements IParticleEmitterType {
    /** defines how much to randomize the particle direction [0-1] (default is 0) */
    directionRandomizer: number;
    private _radius;
    private _angle;
    private _height;
    /**
     * Gets or sets a value indicating where on the radius the start position should be picked (1 = everywhere, 0 = only surface)
     */
    radiusRange: number;
    /**
     * Gets or sets a value indicating where on the height the start position should be picked (1 = everywhere, 0 = only surface)
     */
    heightRange: number;
    /**
     * Gets or sets a value indicating if all the particles should be emitted from the spawn point only (the base of the cone)
     */
    emitFromSpawnPointOnly: boolean;
    /**
     * Gets or sets the radius of the emission cone
     */
    get radius(): number;
    set radius(value: number);
    /**
     * Gets or sets the angle of the emission cone
     */
    get angle(): number;
    set angle(value: number);
    private _buildHeight;
    /**
     * Creates a new instance ConeParticleEmitter
     * @param radius the radius of the emission cone (1 by default)
     * @param angle the cone base angle (PI by default)
     * @param directionRandomizer defines how much to randomize the particle direction [0-1] (default is 0)
     */
    constructor(radius?: number, angle?: number, 
    /** defines how much to randomize the particle direction [0-1] (default is 0) */
    directionRandomizer?: number);
    /**
     * Called by the particle System when the direction is computed for the created particle.
     * @param worldMatrix is the world matrix of the particle system
     * @param directionToUpdate is the direction vector to update with the result
     * @param particle is the particle we are computed the direction for
     * @param isLocal defines if the direction should be set in local space
     */
    startDirectionFunction(worldMatrix: Matrix, directionToUpdate: Vector3, particle: Particle, isLocal: boolean): void;
    /**
     * Called by the particle System when the position is computed for the created particle.
     * @param worldMatrix is the world matrix of the particle system
     * @param positionToUpdate is the position vector to update with the result
     * @param particle is the particle we are computed the position for
     * @param isLocal defines if the position should be set in local space
     */
    startPositionFunction(worldMatrix: Matrix, positionToUpdate: Vector3, particle: Particle, isLocal: boolean): void;
    /**
     * Clones the current emitter and returns a copy of it
     * @returns the new emitter
     */
    clone(): ConeParticleEmitter;
    /**
     * Called by the GPUParticleSystem to setup the update shader
     * @param uboOrEffect defines the update shader
     */
    applyToShader(uboOrEffect: UniformBufferEffectCommonAccessor): void;
    /**
     * Creates the structure of the ubo for this particle emitter
     * @param ubo ubo to create the structure for
     */
    buildUniformLayout(ubo: UniformBuffer): void;
    /**
     * Returns a string to use to update the GPU particles update shader
     * @returns a string containing the defines string
     */
    getEffectDefines(): string;
    /**
     * Returns the string "ConeParticleEmitter"
     * @returns a string containing the class name
     */
    getClassName(): string;
    /**
     * Serializes the particle system to a JSON object.
     * @returns the JSON object
     */
    serialize(): any;
    /**
     * Parse properties from a JSON object
     * @param serializationObject defines the JSON object
     */
    parse(serializationObject: any): void;
}
