{"version": 3, "file": "typeStore.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/typeStore.ts"], "names": [], "mappings": "AAAA,gBAAgB;AAChB,gEAAgE;AAChE,MAAM,gBAAgB,GAA8B,EAAE,CAAC;AAEvD;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,SAAiB,EAAE,IAAY;IACzD,gBAAgB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;AACvC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,QAAQ,CAAC,IAAY;IACjC,OAAO,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAClC,CAAC", "sourcesContent": ["/** @internal */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nconst _RegisteredTypes: { [key: string]: Object } = {};\r\n\r\n/**\r\n * @internal\r\n */\r\nexport function RegisterClass(className: string, type: Object) {\r\n    _RegisteredTypes[className] = type;\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nexport function GetClass(fqdn: string): any {\r\n    return _RegisteredTypes[fqdn];\r\n}\r\n"]}