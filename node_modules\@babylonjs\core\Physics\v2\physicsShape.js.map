{"version": 3, "file": "physicsShape.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Physics/v2/physicsShape.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAC;AAG1D,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAuBlF;;;;GAIG;AACH,MAAM,OAAO,YAAY;IAkBrB;;;;;;;;;;;;OAYG;IACH,YAAY,OAA2B,EAAE,KAAY;QA9BrD;;WAEG;QACI,gBAAW,GAAQ,SAAS,CAAC;QAU5B,eAAU,GAAY,KAAK,CAAC;QAE5B,gBAAW,GAAG,KAAK,CAAC;QAgBxB,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QACD,MAAM,aAAa,GAAG,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC/C,IAAI,CAAC,aAAa,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACnD;QACD,IAAI,aAAa,CAAC,gBAAgB,EAAE,IAAI,CAAC,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;SACvE;QACD,MAAM,aAAa,GAAG,aAAa,CAAC,gBAAgB,EAAE,CAAC;QACvD,IAAI,CAAC,aAAa,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACnD;QACD,IAAI,CAAC,cAAc,GAAG,aAAuC,CAAC;QAE9D,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,IAAI,OAAO,CAAC,UAAU,KAAK,IAAI,EAAE;YACjE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC;YACtC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;SACvD;aAAM,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,EAAE;YAC5D,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;YAC1B,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;YAC5C,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;SACjE;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;;;;;;OAOG;IACH,IAAW,oBAAoB,CAAC,cAAsB;QAClD,IAAI,CAAC,cAAc,CAAC,4BAA4B,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IAC3E,CAAC;IAED;;;OAGG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,cAAc,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,IAAW,iBAAiB,CAAC,WAAmB;QAC5C,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACrE,CAAC;IAED;;;OAGG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC;IACD;;;OAGG;IACH,IAAW,QAAQ,CAAC,QAAyB;QACzC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAChD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACH,IAAW,QAAQ;QACf,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SAC1D;QACD,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,IAAW,OAAO,CAAC,OAAe;QAC9B,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;;;OAQG;IACI,kBAAkB,CAAC,eAA8B,EAAE,QAAsB,EAAE,cAA6B;QAC3G,MAAM,YAAY,GAAG,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC7D,MAAM,aAAa,GAAG,eAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC/D,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC3C,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,aAAa,CAAC,CAAC;QACxE,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpC,aAAa,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QACtD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC/E,CAAC;IAED;;;;;;OAMG;IACI,QAAQ,CAAC,QAAsB,EAAE,WAAqB,EAAE,QAAqB,EAAE,KAAe;QACjG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC/E,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,UAAkB;QACjC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IACtD,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;IAED,IAAW,SAAS,CAAC,SAAkB;QACnC,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;YAC/B,OAAO;SACV;QACD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,OAAO;SACV;QACD,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,YAAY;IAChD;;;;;OAKG;IACH,YAAY,MAAe,EAAE,MAAc,EAAE,KAAY;QACrD,KAAK,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;IACpG,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAkB;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACtC,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;QACjD,MAAM,EAAE,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1C,OAAO,IAAI,kBAAkB,CAAC,WAAW,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IACxE,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,mBAAoB,SAAQ,YAAY;IACjD;;;;;;OAMG;IACH,YAAY,MAAe,EAAE,MAAe,EAAE,MAAc,EAAE,KAAY;QACtE,KAAK,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;IACrH,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAkB;QAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,MAAM,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC;QACpD,MAAM,eAAe,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;QACzF,MAAM,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACnE,MAAM,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QACxE,OAAO,IAAI,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC5E,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,oBAAqB,SAAQ,YAAY;IAClD;;;;;;OAMG;IACH,YAAY,MAAe,EAAE,MAAe,EAAE,MAAc,EAAE,KAAY;QACtE,KAAK,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;IACtH,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAkB;QAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,MAAM,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC;QACpD,MAAM,eAAe,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChF,MAAM,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACnE,MAAM,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QACxE,OAAO,IAAI,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC7E,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,YAAY;IAC7C;;;;;;OAMG;IACH,YAAY,MAAe,EAAE,QAAoB,EAAE,OAAgB,EAAE,KAAY;QAC7E,KAAK,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;IACvH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAkB;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACtC,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;QAC9C,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,wDAAwD;QAClH,OAAO,IAAI,eAAe,CAAC,WAAW,EAAE,UAAU,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC7F,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,sBAAuB,SAAQ,YAAY;IACpD;;;;OAIG;IACH,YAAY,IAAU,EAAE,KAAY;QAChC,KAAK,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;IACrF,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,YAAY;IAC9C;;;;OAIG;IACH,YAAY,IAAU,EAAE,KAAY;QAChC,KAAK,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;IAC9E,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,qBAAsB,SAAQ,YAAY;IACnD;;;OAGG;IACH,YAAY,KAAY;QACpB,KAAK,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,SAAS,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;IACvE,CAAC;CACJ", "sourcesContent": ["import type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { BoundingBox } from \"../../Culling/boundingBox\";\r\nimport { PhysicsShapeType } from \"./IPhysicsEnginePlugin\";\r\nimport type { IPhysicsEnginePluginV2, PhysicsShapeParameters } from \"./IPhysicsEnginePlugin\";\r\nimport type { PhysicsMaterial } from \"./physicsMaterial\";\r\nimport { Matrix, Vector3, Quaternion, TmpVectors } from \"../../Maths/math.vector\";\r\n\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { Scene } from \"../../scene\";\r\n\r\n/**\r\n * Options for creating a physics shape\r\n */\r\nexport interface PhysicShapeOptions {\r\n    /**\r\n     * The type of the shape. This can be one of the following: SPHERE, BOX, CAPSULE, CYLINDER, CONVEX_HULL, MESH, HEIGHTFIELD, CONTAINER\r\n     */\r\n    type?: PhysicsShapeType;\r\n    /**\r\n     * The parameters of the shape. Varies depending of the shape type.\r\n     */\r\n    parameters?: PhysicsShapeParameters;\r\n    /**\r\n     * Reference to an already existing physics shape in the plugin.\r\n     */\r\n    pluginData?: any;\r\n}\r\n\r\n/**\r\n * PhysicsShape class.\r\n * This class is useful for creating a physics shape that can be used in a physics engine.\r\n * A Physic Shape determine how collision are computed. It must be attached to a body.\r\n */\r\nexport class PhysicsShape {\r\n    /**\r\n     * V2 Physics plugin private data for single shape\r\n     */\r\n    public _pluginData: any = undefined;\r\n    /**\r\n     * The V2 plugin used to create and manage this Physics Body\r\n     */\r\n    private _physicsPlugin: IPhysicsEnginePluginV2;\r\n\r\n    private _type: PhysicsShapeType;\r\n\r\n    private _material: PhysicsMaterial;\r\n\r\n    private _isTrigger: boolean = false;\r\n\r\n    private _isDisposed = false;\r\n\r\n    /**\r\n     * Constructs a new physics shape.\r\n     * @param options The options for the physics shape. These are:\r\n     *  * type: The type of the shape. This can be one of the following: SPHERE, BOX, CAPSULE, CYLINDER, CONVEX_HULL, MESH, HEIGHTFIELD, CONTAINER\r\n     *  * parameters: The parameters of the shape.\r\n     *  * pluginData: The plugin data of the shape. This is used if you already have a reference to the object on the plugin side.\r\n     * You need to specify either type or pluginData.\r\n     * @param scene The scene the shape belongs to.\r\n     *\r\n     * This code is useful for creating a new physics shape with the given type, options, and scene.\r\n     * It also checks that the physics engine and plugin version are correct.\r\n     * If not, it throws an error. This ensures that the shape is created with the correct parameters and is compatible with the physics engine.\r\n     */\r\n    constructor(options: PhysicShapeOptions, scene: Scene) {\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        const physicsEngine = scene.getPhysicsEngine();\r\n        if (!physicsEngine) {\r\n            throw new Error(\"No Physics Engine available.\");\r\n        }\r\n        if (physicsEngine.getPluginVersion() != 2) {\r\n            throw new Error(\"Plugin version is incorrect. Expected version 2.\");\r\n        }\r\n        const physicsPlugin = physicsEngine.getPhysicsPlugin();\r\n        if (!physicsPlugin) {\r\n            throw new Error(\"No Physics Plugin available.\");\r\n        }\r\n        this._physicsPlugin = physicsPlugin as IPhysicsEnginePluginV2;\r\n\r\n        if (options.pluginData !== undefined && options.pluginData !== null) {\r\n            this._pluginData = options.pluginData;\r\n            this._type = this._physicsPlugin.getShapeType(this);\r\n        } else if (options.type !== undefined && options.type !== null) {\r\n            this._type = options.type;\r\n            const parameters = options.parameters ?? {};\r\n            this._physicsPlugin.initShape(this, options.type, parameters);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"PhysicsShape\".\r\n     * @returns \"PhysicsShape\"\r\n     */\r\n    public getClassName() {\r\n        return \"PhysicsShape\";\r\n    }\r\n\r\n    /**\r\n     * Returns the type of the physics shape.\r\n     * @returns The type of the physics shape.\r\n     */\r\n    public get type(): PhysicsShapeType {\r\n        return this._type;\r\n    }\r\n\r\n    /**\r\n     * Set the membership mask of a shape. This is a bitfield of arbitrary\r\n     * \"categories\" to which the shape is a member. This is used in combination\r\n     * with the collide mask to determine if this shape should collide with\r\n     * another.\r\n     *\r\n     * @param membershipMask Bitfield of categories of this shape.\r\n     */\r\n    public set filterMembershipMask(membershipMask: number) {\r\n        this._physicsPlugin.setShapeFilterMembershipMask(this, membershipMask);\r\n    }\r\n\r\n    /**\r\n     * Get the membership mask of a shape.\r\n     * @returns Bitmask of categories which this shape is a member of.\r\n     */\r\n    public get filterMembershipMask(): number {\r\n        return this._physicsPlugin.getShapeFilterMembershipMask(this);\r\n    }\r\n\r\n    /**\r\n     * Sets the collide mask of a shape. This is a bitfield of arbitrary\r\n     * \"categories\" to which this shape collides with. Given two shapes,\r\n     * the engine will check if the collide mask and membership overlap:\r\n     * shapeA.filterMembershipMask & shapeB.filterCollideMask\r\n     *\r\n     * If this value is zero (i.e. shapeB only collides with categories\r\n     * which shapeA is _not_ a member of) then the shapes will not collide.\r\n     *\r\n     * Note, the engine will also perform the same test with shapeA and\r\n     * shapeB swapped; the shapes will not collide if either shape has\r\n     * a collideMask which prevents collision with the other shape.\r\n     *\r\n     * @param collideMask Bitmask of categories this shape should collide with\r\n     */\r\n    public set filterCollideMask(collideMask: number) {\r\n        this._physicsPlugin.setShapeFilterCollideMask(this, collideMask);\r\n    }\r\n\r\n    /**\r\n     *\r\n     * @returns Bitmask of categories that this shape should collide with\r\n     */\r\n    public get filterCollideMask(): number {\r\n        return this._physicsPlugin.getShapeFilterCollideMask(this);\r\n    }\r\n    /**\r\n     *\r\n     * @param material\r\n     */\r\n    public set material(material: PhysicsMaterial) {\r\n        this._physicsPlugin.setMaterial(this, material);\r\n        this._material = material;\r\n    }\r\n\r\n    /**\r\n     * Returns the material of the physics shape.\r\n     * @returns The material of the physics shape.\r\n     */\r\n    public get material(): PhysicsMaterial {\r\n        if (!this._material) {\r\n            this._material = this._physicsPlugin.getMaterial(this);\r\n        }\r\n        return this._material;\r\n    }\r\n\r\n    /**\r\n     * Sets the density of the physics shape.\r\n     * @param density The density of the physics shape.\r\n     */\r\n    public set density(density: number) {\r\n        this._physicsPlugin.setDensity(this, density);\r\n    }\r\n\r\n    /**\r\n     * Returns the density of the physics shape.\r\n     * @returns The density of the physics shape.\r\n     */\r\n    public get density(): number {\r\n        return this._physicsPlugin.getDensity(this);\r\n    }\r\n\r\n    /**\r\n     * Utility to add a child shape to this container,\r\n     * automatically computing the relative transform between\r\n     * the container shape and the child instance.\r\n     *\r\n     * @param parentTransform The transform node associated with this shape\r\n     * @param newChild The new PhysicsShape to add\r\n     * @param childTransform The transform node associated with the child shape\r\n     */\r\n    public addChildFromParent(parentTransform: TransformNode, newChild: PhysicsShape, childTransform: TransformNode): void {\r\n        const childToWorld = childTransform.computeWorldMatrix(true);\r\n        const parentToWorld = parentTransform.computeWorldMatrix(true);\r\n        const childToParent = TmpVectors.Matrix[0];\r\n        childToWorld.multiplyToRef(Matrix.Invert(parentToWorld), childToParent);\r\n        const translation = TmpVectors.Vector3[0];\r\n        const rotation = TmpVectors.Quaternion[0];\r\n        const scale = TmpVectors.Vector3[1];\r\n        childToParent.decompose(scale, rotation, translation);\r\n        this._physicsPlugin.addChild(this, newChild, translation, rotation, scale);\r\n    }\r\n\r\n    /**\r\n     * Adds a child shape to a container with an optional transform\r\n     * @param newChild The new PhysicsShape to add\r\n     * @param translation Optional position of the child shape relative to this shape\r\n     * @param rotation Optional rotation of the child shape relative to this shape\r\n     * @param scale Optional scale of the child shape relative to this shape\r\n     */\r\n    public addChild(newChild: PhysicsShape, translation?: Vector3, rotation?: Quaternion, scale?: Vector3): void {\r\n        this._physicsPlugin.addChild(this, newChild, translation, rotation, scale);\r\n    }\r\n\r\n    /**\r\n     * Removes a child shape from this shape.\r\n     * @param childIndex The index of the child shape to remove\r\n     */\r\n    public removeChild(childIndex: number): void {\r\n        this._physicsPlugin.removeChild(this, childIndex);\r\n    }\r\n\r\n    /**\r\n     * Returns the number of children of a physics shape.\r\n     * @returns The number of children of a physics shape.\r\n     */\r\n    public getNumChildren(): number {\r\n        return this._physicsPlugin.getNumChildren(this);\r\n    }\r\n\r\n    /**\r\n     * Returns the bounding box of the physics shape.\r\n     * @returns The bounding box of the physics shape.\r\n     */\r\n    public getBoundingBox(): BoundingBox {\r\n        return this._physicsPlugin.getBoundingBox(this);\r\n    }\r\n\r\n    public set isTrigger(isTrigger: boolean) {\r\n        if (this._isTrigger === isTrigger) {\r\n            return;\r\n        }\r\n        this._isTrigger = isTrigger;\r\n        this._physicsPlugin.setTrigger(this, isTrigger);\r\n    }\r\n\r\n    public get isTrigger(): boolean {\r\n        return this._isTrigger;\r\n    }\r\n\r\n    /**\r\n     * Dispose the shape and release its associated resources.\r\n     */\r\n    public dispose() {\r\n        if (this._isDisposed) {\r\n            return;\r\n        }\r\n        this._physicsPlugin.disposeShape(this);\r\n        this._isDisposed = true;\r\n    }\r\n}\r\n\r\n/**\r\n * Helper object to create a sphere shape\r\n */\r\nexport class PhysicsShapeSphere extends PhysicsShape {\r\n    /**\r\n     * Constructor for the Sphere Shape\r\n     * @param center local center of the sphere\r\n     * @param radius radius\r\n     * @param scene scene to attach to\r\n     */\r\n    constructor(center: Vector3, radius: number, scene: Scene) {\r\n        super({ type: PhysicsShapeType.SPHERE, parameters: { center: center, radius: radius } }, scene);\r\n    }\r\n\r\n    /**\r\n     * Derive an approximate sphere from the mesh.\r\n     * @param mesh node from which to derive the sphere shape\r\n     * @returns PhysicsShapeSphere\r\n     */\r\n    static FromMesh(mesh: AbstractMesh) {\r\n        const bounds = mesh.getBoundingInfo();\r\n        const centerLocal = bounds.boundingSphere.center;\r\n        const he = bounds.boundingBox.extendSize;\r\n        const radius = Math.max(he.x, he.y, he.z);\r\n        return new PhysicsShapeSphere(centerLocal, radius, mesh.getScene());\r\n    }\r\n}\r\n\r\n/**\r\n * Helper object to create a capsule shape\r\n */\r\nexport class PhysicsShapeCapsule extends PhysicsShape {\r\n    /**\r\n     *\r\n     * @param pointA Starting point that defines the capsule segment\r\n     * @param pointB ending point of that same segment\r\n     * @param radius radius\r\n     * @param scene scene to attach to\r\n     */\r\n    constructor(pointA: Vector3, pointB: Vector3, radius: number, scene: Scene) {\r\n        super({ type: PhysicsShapeType.CAPSULE, parameters: { pointA: pointA, pointB: pointB, radius: radius } }, scene);\r\n    }\r\n\r\n    /**\r\n     * Derive an approximate capsule from the mesh. Note, this is\r\n     * not the optimal bounding capsule.\r\n     * @param mesh Node from which to derive a cylinder shape\r\n     * @returns Physics Shape Capsule\r\n     */\r\n    static FromMesh(mesh: AbstractMesh): PhysicsShapeCapsule {\r\n        const boundsLocal = mesh.getBoundingInfo();\r\n        const radius = boundsLocal.boundingBox.extendSize.x;\r\n        const pointFromCenter = new Vector3(0, boundsLocal.boundingBox.extendSize.y - radius, 0);\r\n        const pointA = boundsLocal.boundingBox.center.add(pointFromCenter);\r\n        const pointB = boundsLocal.boundingBox.center.subtract(pointFromCenter);\r\n        return new PhysicsShapeCapsule(pointA, pointB, radius, mesh.getScene());\r\n    }\r\n}\r\n\r\n/**\r\n * Helper object to create a cylinder shape\r\n */\r\nexport class PhysicsShapeCylinder extends PhysicsShape {\r\n    /**\r\n     *\r\n     * @param pointA Starting point that defines the cylinder segment\r\n     * @param pointB ending point of that same segment\r\n     * @param radius radius\r\n     * @param scene scene to attach to\r\n     */\r\n    constructor(pointA: Vector3, pointB: Vector3, radius: number, scene: Scene) {\r\n        super({ type: PhysicsShapeType.CYLINDER, parameters: { pointA: pointA, pointB: pointB, radius: radius } }, scene);\r\n    }\r\n\r\n    /**\r\n     * Derive an approximate cylinder from the mesh. Note, this is\r\n     * not the optimal bounding cylinder.\r\n     * @param mesh Node from which to derive a cylinder shape\r\n     * @returns Physics Shape Cylinder\r\n     */\r\n    static FromMesh(mesh: AbstractMesh): PhysicsShapeCylinder {\r\n        const boundsLocal = mesh.getBoundingInfo();\r\n        const radius = boundsLocal.boundingBox.extendSize.x;\r\n        const pointFromCenter = new Vector3(0, boundsLocal.boundingBox.extendSize.y, 0);\r\n        const pointA = boundsLocal.boundingBox.center.add(pointFromCenter);\r\n        const pointB = boundsLocal.boundingBox.center.subtract(pointFromCenter);\r\n        return new PhysicsShapeCylinder(pointA, pointB, radius, mesh.getScene());\r\n    }\r\n}\r\n\r\n/**\r\n * Helper object to create a box shape\r\n */\r\nexport class PhysicsShapeBox extends PhysicsShape {\r\n    /**\r\n     *\r\n     * @param center local center of the box\r\n     * @param rotation local orientation\r\n     * @param extents size of the box in each direction\r\n     * @param scene scene to attach to\r\n     */\r\n    constructor(center: Vector3, rotation: Quaternion, extents: Vector3, scene: Scene) {\r\n        super({ type: PhysicsShapeType.BOX, parameters: { center: center, rotation: rotation, extents: extents } }, scene);\r\n    }\r\n\r\n    /**\r\n     *\r\n     * @param mesh\r\n     * @returns PhysicsShapeBox\r\n     */\r\n    static FromMesh(mesh: AbstractMesh): PhysicsShapeBox {\r\n        const bounds = mesh.getBoundingInfo();\r\n        const centerLocal = bounds.boundingBox.center;\r\n        const extents = bounds.boundingBox.extendSize.scale(2.0); //<todo.eoin extendSize seems to really be half-extents?\r\n        return new PhysicsShapeBox(centerLocal, Quaternion.Identity(), extents, mesh.getScene());\r\n    }\r\n}\r\n\r\n/**\r\n * Helper object to create a convex hull shape\r\n */\r\nexport class PhysicsShapeConvexHull extends PhysicsShape {\r\n    /**\r\n     *\r\n     * @param mesh the mesh to be used as topology infos for the convex hull\r\n     * @param scene scene to attach to\r\n     */\r\n    constructor(mesh: Mesh, scene: Scene) {\r\n        super({ type: PhysicsShapeType.CONVEX_HULL, parameters: { mesh: mesh } }, scene);\r\n    }\r\n}\r\n\r\n/**\r\n * Helper object to create a mesh shape\r\n */\r\nexport class PhysicsShapeMesh extends PhysicsShape {\r\n    /**\r\n     *\r\n     * @param mesh the mesh topology that will be used to create the shape\r\n     * @param scene scene to attach to\r\n     */\r\n    constructor(mesh: Mesh, scene: Scene) {\r\n        super({ type: PhysicsShapeType.MESH, parameters: { mesh: mesh } }, scene);\r\n    }\r\n}\r\n\r\n/**\r\n * A shape container holds a variable number of shapes. Use AddChild to append to newly created parent container.\r\n */\r\nexport class PhysicsShapeContainer extends PhysicsShape {\r\n    /**\r\n     * Constructor of the Shape container\r\n     * @param scene scene to attach to\r\n     */\r\n    constructor(scene: Scene) {\r\n        super({ type: PhysicsShapeType.CONTAINER, parameters: {} }, scene);\r\n    }\r\n}\r\n"]}