{"version": 3, "file": "pbrSpecularGlossinessMaterial.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/PBR/pbrSpecularGlossinessMaterial.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAI3G,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAErD,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AAE1E;;;;;GAKG;AACH,MAAM,OAAO,6BAA8B,SAAQ,qBAAqB;IAqCpE;;OAEG;IACH,IAAW,uCAAuC;QAC9C,OAAO,IAAI,CAAC,wCAAwC,CAAC;IACzD,CAAC;IAED;;;;;OAKG;IACH,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACnB,IAAI,CAAC,wCAAwC,GAAG,IAAI,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO,+BAA+B,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,IAAY;QACrB,MAAM,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,6BAA6B,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAE9G,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC;QAChB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEzC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,mBAAmB,CAAC,UAAU,GAAG,uCAAuC,CAAC;QAEzE,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC3D,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QAC7D,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;QACjD,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QACnD,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QAC7D,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;QAE/D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,MAAM,QAAQ,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,6BAA6B,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAChI,IAAI,MAAM,CAAC,SAAS,EAAE;YAClB,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAC9D;QACD,IAAI,MAAM,CAAC,UAAU,EAAE;YACnB,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAChE;QACD,IAAI,MAAM,CAAC,IAAI,EAAE;YACb,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACpD;QACD,IAAI,MAAM,CAAC,KAAK,EAAE;YACd,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACtD;QACD,IAAI,MAAM,CAAC,UAAU,EAAE;YACnB,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAChE;QACD,IAAI,MAAM,CAAC,WAAW,EAAE;YACpB,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAClE;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;CACJ;AA3HU;IAFN,iBAAiB,CAAC,SAAS,CAAC;IAC5B,gBAAgB,CAAC,kCAAkC,EAAE,cAAc,CAAC;mEACzC;AAQrB;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,EAAE,gBAAgB,CAAC;qEAC1B;AAOtC;IAFN,iBAAiB,CAAC,UAAU,CAAC;IAC7B,gBAAgB,CAAC,kCAAkC,EAAE,oBAAoB,CAAC;oEAC9C;AAOtB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,EAAE,eAAe,CAAC;iEAC5C;AAOnB;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,EAAE,sBAAsB,CAAC;gFACrB;AAgG5D,aAAa,CAAC,uCAAuC,EAAE,6BAA6B,CAAC,CAAC", "sourcesContent": ["import { serialize, serializeAsColor3, expandToProperty, serializeAsTexture } from \"../../Misc/decorators\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Color3 } from \"../../Maths/math.color\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { PBRBaseSimpleMaterial } from \"./pbrBaseSimpleMaterial\";\r\nimport { RegisterClass } from \"../../Misc/typeStore\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { SerializationHelper } from \"../../Misc/decorators.serialization\";\r\n\r\n/**\r\n * The PBR material of BJS following the specular glossiness convention.\r\n *\r\n * This fits to the PBR convention in the GLTF definition:\r\n * https://github.com/KhronosGroup/glTF/tree/2.0/extensions/Khronos/KHR_materials_pbrSpecularGlossiness\r\n */\r\nexport class PBRSpecularGlossinessMaterial extends PBRBaseSimpleMaterial {\r\n    /**\r\n     * Specifies the diffuse color of the material.\r\n     */\r\n    @serializeAsColor3(\"diffuse\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_albedoColor\")\r\n    public diffuseColor: Color3;\r\n\r\n    /**\r\n     * Specifies the diffuse texture of the material. This can also contains the opacity value in its alpha\r\n     * channel.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_albedoTexture\")\r\n    public diffuseTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Specifies the specular color of the material. This indicates how reflective is the material (none to mirror).\r\n     */\r\n    @serializeAsColor3(\"specular\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_reflectivityColor\")\r\n    public specularColor: Color3;\r\n\r\n    /**\r\n     * Specifies the glossiness of the material. This indicates \"how sharp is the reflection\".\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_microSurface\")\r\n    public glossiness: number;\r\n\r\n    /**\r\n     * Specifies both the specular color RGB and the glossiness A of the material per pixels.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_reflectivityTexture\")\r\n    public specularGlossinessTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Specifies if the reflectivity texture contains the glossiness information in its alpha channel.\r\n     */\r\n    public get useMicroSurfaceFromReflectivityMapAlpha() {\r\n        return this._useMicroSurfaceFromReflectivityMapAlpha;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new PBRSpecularGlossinessMaterial instance.\r\n     *\r\n     * @param name The material name\r\n     * @param scene The scene the material will be use in.\r\n     */\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n        this._useMicroSurfaceFromReflectivityMapAlpha = true;\r\n    }\r\n\r\n    /**\r\n     * @returns the current class name of the material.\r\n     */\r\n    public getClassName(): string {\r\n        return \"PBRSpecularGlossinessMaterial\";\r\n    }\r\n\r\n    /**\r\n     * Makes a duplicate of the current material.\r\n     * @param name - name to use for the new material.\r\n     * @returns cloned material instance\r\n     */\r\n    public clone(name: string): PBRSpecularGlossinessMaterial {\r\n        const clone = SerializationHelper.Clone(() => new PBRSpecularGlossinessMaterial(name, this.getScene()), this);\r\n\r\n        clone.id = name;\r\n        clone.name = name;\r\n\r\n        this.clearCoat.copyTo(clone.clearCoat);\r\n        this.anisotropy.copyTo(clone.anisotropy);\r\n        this.brdf.copyTo(clone.brdf);\r\n        this.sheen.copyTo(clone.sheen);\r\n        this.subSurface.copyTo(clone.subSurface);\r\n\r\n        return clone;\r\n    }\r\n\r\n    /**\r\n     * Serialize the material to a parsable JSON object.\r\n     * @returns the JSON object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n        serializationObject.customType = \"BABYLON.PBRSpecularGlossinessMaterial\";\r\n\r\n        serializationObject.clearCoat = this.clearCoat.serialize();\r\n        serializationObject.anisotropy = this.anisotropy.serialize();\r\n        serializationObject.brdf = this.brdf.serialize();\r\n        serializationObject.sheen = this.sheen.serialize();\r\n        serializationObject.subSurface = this.subSurface.serialize();\r\n        serializationObject.iridescence = this.iridescence.serialize();\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parses a JSON object corresponding to the serialize function.\r\n     * @param source - JSON source object.\r\n     * @param scene - the scene to parse to.\r\n     * @param rootUrl - root url of the assets.\r\n     * @returns a new PBRSpecularGlossinessMaterial.\r\n     */\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): PBRSpecularGlossinessMaterial {\r\n        const material = SerializationHelper.Parse(() => new PBRSpecularGlossinessMaterial(source.name, scene), source, scene, rootUrl);\r\n        if (source.clearCoat) {\r\n            material.clearCoat.parse(source.clearCoat, scene, rootUrl);\r\n        }\r\n        if (source.anisotropy) {\r\n            material.anisotropy.parse(source.anisotropy, scene, rootUrl);\r\n        }\r\n        if (source.brdf) {\r\n            material.brdf.parse(source.brdf, scene, rootUrl);\r\n        }\r\n        if (source.sheen) {\r\n            material.sheen.parse(source.sheen, scene, rootUrl);\r\n        }\r\n        if (source.subSurface) {\r\n            material.subSurface.parse(source.subSurface, scene, rootUrl);\r\n        }\r\n        if (source.iridescence) {\r\n            material.iridescence.parse(source.iridescence, scene, rootUrl);\r\n        }\r\n        return material;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PBRSpecularGlossinessMaterial\", PBRSpecularGlossinessMaterial);\r\n"]}