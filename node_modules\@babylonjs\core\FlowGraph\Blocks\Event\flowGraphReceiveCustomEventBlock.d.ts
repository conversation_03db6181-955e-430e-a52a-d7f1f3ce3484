import type { FlowGraphContext } from "../../flowGraphContext";
import { FlowGraphEventBlock } from "../../flowGraphEventBlock";
import type { IFlowGraphBlockConfiguration } from "../../flowGraphBlock";
/**
 * @experimental
 * Parameters used to create a FlowGraphReceiveCustomEventBlock.
 */
export interface IFlowGraphReceiveCustomEventBlockConfiguration extends IFlowGraphBlockConfiguration {
    /**
     * The id of the event to receive.
     */
    eventId: string;
    /**
     * The names of the data outputs for that event. Should be in the same order as the event data in
     * SendCustomEvent
     */
    eventData: string[];
}
/**
 * @experimental
 * A block that receives a custom event. It saves the data sent in the eventData output.
 */
export declare class FlowGraphReceiveCustomEventBlock extends FlowGraphEventBlock {
    /**
     * the configuration of the block
     */
    config: IFlowGraphReceiveCustomEventBlockConfiguration;
    private _eventObserver;
    constructor(
    /**
     * the configuration of the block
     */
    config: IFlowGraphReceiveCustomEventBlockConfiguration);
    _preparePendingTasks(context: FlowGraphContext): void;
    _cancelPendingTasks(context: FlowGraphContext): void;
    /**
     * @returns class name of the block.
     */
    getClassName(): string;
    /**
     * the class name of the block.
     */
    static ClassName: string;
    /**
     * Serializes this block
     * @param serializationObject the object to serialize to
     */
    serialize(serializationObject?: any): void;
}
