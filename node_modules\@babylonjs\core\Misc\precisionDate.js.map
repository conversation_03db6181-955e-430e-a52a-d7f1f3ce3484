{"version": 3, "file": "precisionDate.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/precisionDate.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAC;AAEtD;;GAEG;AACH,MAAM,OAAO,aAAa;IACtB;;OAEG;IACI,MAAM,KAAK,GAAG;QACjB,IAAI,mBAAmB,EAAE,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE;YACvE,OAAO,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;SACnC;QAED,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC;CACJ", "sourcesContent": ["import { IsWindowObjectExist } from \"./domManagement\";\r\n\r\n/**\r\n * Class containing a set of static utilities functions for precision date\r\n */\r\nexport class PrecisionDate {\r\n    /**\r\n     * Gets either window.performance.now() if supported or Date.now() else\r\n     */\r\n    public static get Now(): number {\r\n        if (IsWindowObjectExist() && window.performance && window.performance.now) {\r\n            return window.performance.now();\r\n        }\r\n\r\n        return Date.now();\r\n    }\r\n}\r\n"]}