{"version": 3, "file": "sphereBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Meshes/Node/Blocks/Sources/sphereBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,8CAA8C,CAAC;AACrG,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAG5D,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,sBAAsB,EAAE,2CAA2C;AAC5E,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAEtG;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,iBAAiB;IAQ9C;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAZhB;;;WAGG;QAEI,oBAAe,GAAG,KAAK,CAAC;QAS3B,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,qCAAqC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QACpF,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACrF,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACtF,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACtF,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACtF,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAChF,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAElF,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,qCAAqC,CAAC,QAAQ,CAAC,CAAC;IACpF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC5B,MAAM,aAAa,GAAG,IAAI,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACzD,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC;YACxB,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACjD;IACL,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,MAAM,OAAO,GAYT,EAAE,CAAC;QACP,MAAM,IAAI,GAAG,CAAC,KAA6B,EAAE,EAAE;YAC3C,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC1D,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC1D,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC5D,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC5D,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAChD,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAEpD,4CAA4C;YAC5C,OAAO,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;SACxC;aAAM;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1B,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,GAAG,EAAE;gBACjC,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,CAAC,CAAC;gBAClC,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;YACzB,CAAC,CAAC;SACL;IACL,CAAC;IAES,mBAAmB;QACzB,MAAM,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,GAAG,GAAG,IAAI,CAAC,iBAAiB,sBAAsB,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC;QAC7I,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAE3D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB;QACxC,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAExC,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC,eAAe,CAAC;IAC/D,CAAC;CACJ;AAxJU;IADN,sBAAsB,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC;oDAC1F;AA0JnC,aAAa,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC", "sourcesContent": ["import { NodeGeometryBlockConnectionPointTypes } from \"../../Enums/nodeGeometryConnectionPointTypes\";\r\nimport { NodeGeometryBlock } from \"../../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../../nodeGeometryBlockConnectionPoint\";\r\nimport type { NodeGeometryBuildState } from \"../../nodeGeometryBuildState\";\r\nimport { GeometryInputBlock } from \"../geometryInputBlock\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Vector4 } from \"../../../../Maths/math.vector\";\r\nimport { CreateSphereVertexData } from \"core/Meshes/Builders/sphereBuilder\";\r\nimport { PropertyTypeForEdition, editableInPropertyPage } from \"../../../../Decorators/nodeDecorator\";\r\n\r\n/**\r\n * Defines a block used to generate sphere geometry data\r\n */\r\nexport class SphereBlock extends NodeGeometryBlock {\r\n    /**\r\n     * Gets or sets a boolean indicating that this block can evaluate context\r\n     * Build performance is improved when this value is set to false as the system will cache values instead of reevaluating everything per context change\r\n     */\r\n    @editableInPropertyPage(\"Evaluate context\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { rebuild: true } })\r\n    public evaluateContext = false;\r\n\r\n    /**\r\n     * Create a new SphereBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"segments\", NodeGeometryBlockConnectionPointTypes.Int, true, 32);\r\n        this.registerInput(\"diameter\", NodeGeometryBlockConnectionPointTypes.Float, true, 1);\r\n        this.registerInput(\"diameterX\", NodeGeometryBlockConnectionPointTypes.Float, true, 0);\r\n        this.registerInput(\"diameterY\", NodeGeometryBlockConnectionPointTypes.Float, true, 0);\r\n        this.registerInput(\"diameterZ\", NodeGeometryBlockConnectionPointTypes.Float, true, 0);\r\n        this.registerInput(\"arc\", NodeGeometryBlockConnectionPointTypes.Float, true, 1);\r\n        this.registerInput(\"slice\", NodeGeometryBlockConnectionPointTypes.Float, true, 1);\r\n\r\n        this.registerOutput(\"geometry\", NodeGeometryBlockConnectionPointTypes.Geometry);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"SphereBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the segments input component\r\n     */\r\n    public get segments(): NodeGeometryConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the diameter input component\r\n     */\r\n    public get diameter(): NodeGeometryConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the diameterX input component\r\n     */\r\n    public get diameterX(): NodeGeometryConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the diameterY input component\r\n     */\r\n    public get diameterY(): NodeGeometryConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the diameterZ input component\r\n     */\r\n    public get diameterZ(): NodeGeometryConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the arc input component\r\n     */\r\n    public get arc(): NodeGeometryConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the slice input component\r\n     */\r\n    public get slice(): NodeGeometryConnectionPoint {\r\n        return this._inputs[6];\r\n    }\r\n\r\n    /**\r\n     * Gets the geometry output component\r\n     */\r\n    public get geometry(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public autoConfigure() {\r\n        if (!this.diameter.isConnected) {\r\n            const diameterInput = new GeometryInputBlock(\"Diameter\");\r\n            diameterInput.value = 1;\r\n            diameterInput.output.connectTo(this.diameter);\r\n        }\r\n    }\r\n\r\n    protected _buildBlock(state: NodeGeometryBuildState) {\r\n        const options: {\r\n            segments?: number;\r\n            diameter?: number;\r\n            diameterX?: number;\r\n            diameterY?: number;\r\n            diameterZ?: number;\r\n            arc?: number;\r\n            slice?: number;\r\n            sideOrientation?: number;\r\n            frontUVs?: Vector4;\r\n            backUVs?: Vector4;\r\n            dedupTopBottomIndices?: boolean;\r\n        } = {};\r\n        const func = (state: NodeGeometryBuildState) => {\r\n            options.segments = this.segments.getConnectedValue(state);\r\n            options.diameter = this.diameter.getConnectedValue(state);\r\n            options.diameterX = this.diameterX.getConnectedValue(state);\r\n            options.diameterY = this.diameterY.getConnectedValue(state);\r\n            options.diameterZ = this.diameterZ.getConnectedValue(state);\r\n            options.arc = this.arc.getConnectedValue(state);\r\n            options.slice = this.slice.getConnectedValue(state);\r\n\r\n            // Append vertex data from the plane builder\r\n            return CreateSphereVertexData(options);\r\n        };\r\n\r\n        if (this.evaluateContext) {\r\n            this.geometry._storedFunction = func;\r\n        } else {\r\n            const value = func(state);\r\n            this.geometry._storedFunction = () => {\r\n                this.geometry._executionCount = 1;\r\n                return value.clone();\r\n            };\r\n        }\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        const codeString = super._dumpPropertiesCode() + `${this._codeVariableName}.evaluateContext = ${this.evaluateContext ? \"true\" : \"false\"};\\n`;\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Serializes this block in a JSON representation\r\n     * @returns the serialized block object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.evaluateContext = this.evaluateContext;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any) {\r\n        super._deserialize(serializationObject);\r\n\r\n        this.evaluateContext = serializationObject.evaluateContext;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SphereBlock\", SphereBlock);\r\n"]}