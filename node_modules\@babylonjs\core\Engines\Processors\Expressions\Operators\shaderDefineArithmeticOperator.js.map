{"version": 3, "file": "shaderDefineArithmeticOperator.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Engines/Processors/Expressions/Operators/shaderDefineArithmeticOperator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,2BAA2B,CAAC;AAEnE,gBAAgB;AAChB,MAAM,OAAO,8BAA+B,SAAQ,sBAAsB;IACtE,YACW,MAAc,EACd,OAAe,EACf,SAAiB;QAExB,KAAK,EAAE,CAAC;QAJD,WAAM,GAAN,MAAM,CAAQ;QACd,YAAO,GAAP,OAAO,CAAQ;QACf,cAAS,GAAT,SAAS,CAAQ;IAG5B,CAAC;IAEM,MAAM,CAAC,aAAwC;QAClD,IAAI,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEvC,IAAI,KAAK,KAAK,SAAS,EAAE;YACrB,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;SACvB;QAED,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC7B,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEvC,QAAQ,IAAI,CAAC,OAAO,EAAE;YAClB,KAAK,GAAG;gBACJ,SAAS,GAAG,IAAI,GAAG,KAAK,CAAC;gBACzB,MAAM;YACV,KAAK,GAAG;gBACJ,SAAS,GAAG,IAAI,GAAG,KAAK,CAAC;gBACzB,MAAM;YACV,KAAK,IAAI;gBACL,SAAS,GAAG,IAAI,IAAI,KAAK,CAAC;gBAC1B,MAAM;YACV,KAAK,IAAI;gBACL,SAAS,GAAG,IAAI,IAAI,KAAK,CAAC;gBAC1B,MAAM;YACV,KAAK,IAAI;gBACL,SAAS,GAAG,IAAI,KAAK,KAAK,CAAC;gBAC3B,MAAM;YACV,KAAK,IAAI;gBACL,SAAS,GAAG,IAAI,KAAK,KAAK,CAAC;gBAC3B,MAAM;SACb;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;CACJ", "sourcesContent": ["import { ShaderDefineExpression } from \"../shaderDefineExpression\";\r\n\r\n/** @internal */\r\nexport class ShaderDefineArithmeticOperator extends ShaderDefineExpression {\r\n    public constructor(\r\n        public define: string,\r\n        public operand: string,\r\n        public testValue: string\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    public isTrue(preprocessors: { [key: string]: string }) {\r\n        let value = preprocessors[this.define];\r\n\r\n        if (value === undefined) {\r\n            value = this.define;\r\n        }\r\n\r\n        let condition = false;\r\n        const left = parseInt(value);\r\n        const right = parseInt(this.testValue);\r\n\r\n        switch (this.operand) {\r\n            case \">\":\r\n                condition = left > right;\r\n                break;\r\n            case \"<\":\r\n                condition = left < right;\r\n                break;\r\n            case \"<=\":\r\n                condition = left <= right;\r\n                break;\r\n            case \">=\":\r\n                condition = left >= right;\r\n                break;\r\n            case \"==\":\r\n                condition = left === right;\r\n                break;\r\n            case \"!=\":\r\n                condition = left !== right;\r\n                break;\r\n        }\r\n\r\n        return condition;\r\n    }\r\n}\r\n"]}