{"version": 3, "file": "reflector.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/reflector.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD;;;GAGG;AACH,MAAM,OAAO,SAAS;IAMlB;;;;;OAKG;IACH,YAAmB,KAAY,EAAE,QAAgB,EAAE,IAAY;QAC3D,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,MAAM,CAAC,GAAG,CAAC,kCAAkC,QAAQ,IAAI,IAAI,EAAE,CAAC,CAAC;QACjE,IAAI,CAAC,UAAU,GAAG,IAAI,SAAS,CAAC,QAAQ,QAAQ,IAAI,IAAI,EAAE,CAAC,CAAC;QAE5D,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE;YAClC,MAAM,OAAO,GAAW,KAAK,CAAC,IAAI,CAAC;YACnC,IAAI,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;gBAC9C,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACtE,MAAM,CAAC,GAAG,CAAC,wCAAwC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;gBAClF,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;gBACzC,OAAO;aACV;iBAAM;gBACH,MAAM,CAAC,GAAG,CAAC,wCAAwC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC5E,IAAI,CAAC,oBAAoB,EAAE,CAAC;aAC/B;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;YAChC,MAAM,CAAC,GAAG,CAAC,4BAA4B,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QACzE,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;IAEO,oBAAoB,CAAC,OAAe;QACxC,QAAQ,OAAO,EAAE;YACb,KAAK,WAAW,CAAC,CAAC;gBACd,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;oBAC5D,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;gBAC/D,CAAC,CAAC,CAAC;gBACH,MAAM;aACT;SACJ;IACL,CAAC;IAEO,oBAAoB;QACxB,aAAa;IACjB,CAAC;;AAvDuB,wBAAc,GAAG,IAAI,CAAC", "sourcesContent": ["import type { Scene } from \"../scene\";\r\nimport { Logger } from \"./logger\";\r\nimport { SceneSerializer } from \"./sceneSerializer\";\r\n\r\n/**\r\n * Class used to connect with the reflector zone of the sandbox via the reflector bridge\r\n * @since 5.0.0\r\n */\r\nexport class Reflector {\r\n    private static readonly _SERVER_PREFIX = \"$$\";\r\n\r\n    private _scene: Scene;\r\n    private _webSocket: WebSocket;\r\n\r\n    /**\r\n     * Constructs a reflector object.\r\n     * @param scene The scene to use\r\n     * @param hostname The hostname of the reflector bridge\r\n     * @param port The port of the reflector bridge\r\n     */\r\n    public constructor(scene: Scene, hostname: string, port: number) {\r\n        this._scene = scene;\r\n\r\n        Logger.Log(`[Reflector] Connecting to ws://${hostname}:${port}`);\r\n        this._webSocket = new WebSocket(`ws://${hostname}:${port}`);\r\n\r\n        this._webSocket.onmessage = (event) => {\r\n            const message: string = event.data;\r\n            if (message.startsWith(Reflector._SERVER_PREFIX)) {\r\n                const serverMessage = message.substr(Reflector._SERVER_PREFIX.length);\r\n                Logger.Log(`[Reflector] Received server message: ${serverMessage.substr(0, 64)}`);\r\n                this._handleServerMessage(serverMessage);\r\n                return;\r\n            } else {\r\n                Logger.Log(`[Reflector] Received client message: ${message.substr(0, 64)}`);\r\n                this._handleClientMessage();\r\n            }\r\n        };\r\n\r\n        this._webSocket.onclose = (event) => {\r\n            Logger.Log(`[Reflector] Disconnected ${event.code} ${event.reason}`);\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Closes the reflector connection\r\n     */\r\n    public close(): void {\r\n        this._webSocket.close();\r\n    }\r\n\r\n    private _handleServerMessage(message: string): void {\r\n        switch (message) {\r\n            case \"connected\": {\r\n                SceneSerializer.SerializeAsync(this._scene).then((serialized) => {\r\n                    this._webSocket.send(`load|${JSON.stringify(serialized)}`);\r\n                });\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    private _handleClientMessage(): void {\r\n        // do nothing\r\n    }\r\n}\r\n"]}