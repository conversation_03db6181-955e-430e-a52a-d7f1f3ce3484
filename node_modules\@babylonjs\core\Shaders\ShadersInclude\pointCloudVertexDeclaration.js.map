{"version": 3, "file": "pointCloudVertexDeclaration.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Shaders/ShadersInclude/pointCloudVertexDeclaration.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAExD,MAAM,IAAI,GAAG,6BAA6B,CAAC;AAC3C,MAAM,MAAM,GAAG;;;CAGd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAChD,gBAAgB;AAChB,MAAM,CAAC,MAAM,2BAA2B,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore\";\n\nconst name = \"pointCloudVertexDeclaration\";\nconst shader = `#ifdef POINTSIZE\nuniform float pointSize;\n#endif\n`;\n// Sideeffect\nShaderStore.IncludesShadersStore[name] = shader;\n/** @internal */\nexport const pointCloudVertexDeclaration = { name, shader };\n"]}