{"version": 3, "file": "reflectionBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/PBR/reflectionBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,oCAAoC,EAAE,MAAM,wCAAwC,CAAC;AAC9F,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AACxG,OAAO,EAAE,0BAA0B,EAAE,MAAM,oCAAoC,CAAC;AAGhF,OAAO,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAC;AAKpD,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAEtG,OAAO,EAAE,MAAM,EAAE,MAAM,+BAA+B,CAAC;AACvD,OAAO,EAAE,MAAM,EAAE,mCAAyB;AAE1C;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,0BAA0B;IA0CjD,kCAAkC;QACxC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC3B,IAAI,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC/D,MAAM,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;YAC/E,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,UAAU;QAChB,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAE,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB,CAAC,MAAM,CAAC;QAC9I,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;SACzC;IACL,CAAC;IAED;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAtChB;;;WAGG;QAEI,0BAAqB,GAAY,IAAI,CAAC;QAE7C;;WAEG;QAEI,8BAAyB,GAAY,KAAK,CAAC;QA6B9C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,qCAAqC,CAAC,UAAU,EAAE,KAAK,EAAE,wBAAwB,CAAC,MAAM,CAAC,CAAC;QACzH,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,MAAM,EAAE,KAAK,EAAE,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAClH,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,MAAM,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAEnH,IAAI,CAAC,cAAc,CACf,YAAY,EACZ,qCAAqC,CAAC,MAAM,EAC5C,wBAAwB,CAAC,QAAQ,EACjC,IAAI,uCAAuC,CAAC,YAAY,EAAE,IAAI,EAAE,oCAAoC,CAAC,MAAM,EAAE,eAAe,EAAE,iBAAiB,CAAC,CACnJ,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,0CAA0C,CACpD,qCAAqC,CAAC,MAAM,GAAG,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,CAC/I,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,kBAAkB,CAAC;IAC3F,CAAC;IAES,WAAW;QACjB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,OAAO,IAAI,CAAC,OAAO,CAAC;SACvB;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;IAC1C,CAAC;IAEM,cAAc,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QAC9F,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;QAElD,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7C,MAAM,UAAU,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,gBAAgB,CAAC;QAE3E,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAEjD,IAAI,CAAC,UAAU,EAAE;YACb,OAAO;SACV;QAED,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,EAAE,iBAAkB,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QAC3F,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,EAAE,iBAAkB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QACnG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,iBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAE3I,OAAO,CAAC,QAAQ,CAAC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAC1E,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,iBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACzE,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,iBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEpE,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,eAAe,KAAK,OAAO,CAAC,WAAW,EAAE;YAChF,IAAI,iBAAiB,CAAC,MAAM,EAAE;gBAC1B,OAAO,CAAC,QAAQ,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAAC;gBACxD,OAAO,CAAC,QAAQ,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;gBAC5C,IAAI,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,iBAAiB,IAAI,CAAC,EAAE;oBAC5F,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;iBACnD;qBAAM;oBACH,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;iBAClD;aACJ;SACJ;IACL,CAAC;IAEM,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAE,OAAiB;QAClF,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;QAEvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAE7C,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,EAAE;YAChC,OAAO;SACV;QAED,IAAI,iBAAiB,CAAC,MAAM,EAAE;YAC1B,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;SAC/D;aAAM;YACH,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;SAC7D;QAED,MAAM,KAAK,GAAG,iBAAiB,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;QAEhD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,iCAAiC,EAAE,KAAK,EAAE,iBAAiB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;QAC7I,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAEhF,MAAM,OAAO,GAAG,OAAO,CAAC,eAAsC,CAAC;QAE/D,MAAM,WAAW,GAAG,iBAAiB,CAAC,mBAAmB,CAAC;QAC1D,IAAI,OAAO,CAAC,6BAA6B,IAAI,WAAW,EAAE;YACtD,IAAI,OAAO,CAAC,mBAAmB,EAAE;gBAC7B,MAAM,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,CAAC;gBAC1D,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC3D,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC7D,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC3D,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC3D,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC7D,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC7D,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC3D,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAC3D,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;aAC9D;iBAAM;gBACH,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnF,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnF,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnF,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACnJ,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACnJ,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACvF,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACvF,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACvF,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aAC1F;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,KAA6B;QACjD,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAEzC,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,EAAE;YACnE,cAAc,EAAE;gBACZ,EAAE,MAAM,EAAE,iEAAiE,EAAE,OAAO,EAAE,EAAE,EAAE;gBAC1F,EAAE,MAAM,EAAE,8DAA8D,EAAE,OAAO,EAAE,EAAE,EAAE;aAC1F;SACJ,CAAC,CAAC;QAEH,MAAM,oBAAoB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAE5E,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,CAAC;QAExF,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,2BAA2B,EAAE,MAAM,EAAE,yEAAyE,CAAC,CAAC;QAElJ,KAAK,CAAC,sBAAsB,CAAC,eAAe,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAC7E,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAC9E,KAAK,CAAC,sBAAsB,CAAC,eAAe,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAC7E,KAAK,CAAC,sBAAsB,CAAC,eAAe,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAC7E,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAC9E,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAC9E,KAAK,CAAC,sBAAsB,CAAC,eAAe,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAC7E,KAAK,CAAC,sBAAsB,CAAC,eAAe,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAC7E,KAAK,CAAC,sBAAsB,CAAC,eAAe,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAE7E,KAAK,CAAC,sBAAsB,CAAC,aAAa,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACjF,KAAK,CAAC,sBAAsB,CAAC,aAAa,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACjF,KAAK,CAAC,sBAAsB,CAAC,aAAa,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACjF,KAAK,CAAC,sBAAsB,CAAC,iBAAiB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACrF,KAAK,CAAC,sBAAsB,CAAC,iBAAiB,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACrF,KAAK,CAAC,sBAAsB,CAAC,cAAc,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAClF,KAAK,CAAC,sBAAsB,CAAC,cAAc,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAClF,KAAK,CAAC,sBAAsB,CAAC,cAAc,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAClF,KAAK,CAAC,sBAAsB,CAAC,cAAc,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAElF,IAAI,IAAI;uBACO,oBAAoB,WAAW,IAAI,CAAC,qBAAqB,qBAAqB,IAAI,CAAC,WAAW,CAAC,sBAAsB;yBACnH,IAAI,CAAC,gBAAgB;sBACxB,oBAAoB;;kBAExB,IAAI,CAAC,2BAA2B,mCAAmC,oBAAoB;qBACpF,CAAC;QAEd,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,KAA6B,EAAE,aAAqB;QAC/D,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QAEpC,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,EAAE;YACnE,cAAc,EAAE;gBACZ,EAAE,MAAM,EAAE,iEAAiE,EAAE,OAAO,EAAE,EAAE,EAAE;gBAC1F,EAAE,MAAM,EAAE,8DAA8D,EAAE,OAAO,EAAE,EAAE,EAAE;aAC1F;SACJ,CAAC,CAAC;QAEH,KAAK,CAAC,aAAa,CACf,kBAAkB,EAClB;qBACS,IAAI,CAAC,aAAa;;;;qBAIlB,EACT,KAAK,IAAI,CAAC,IAAI,EAAE,CACnB,CAAC;QAEF,KAAK,CAAC,aAAa,CACf,qBAAqB,EACrB;qBACS,IAAI,CAAC,aAAa;;;;qBAIlB,EACT,KAAK,IAAI,CAAC,IAAI,EAAE,CACnB,CAAC;QAEF,MAAM,2BAA2B,GAAG;;kBAE1B,IAAI,CAAC,sCAAsC,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC;yBAC3E,IAAI,CAAC,qBAAqB;gBACnC,CAAC;QAET,KAAK,CAAC,aAAa,CAAC,4BAA4B,EAAE,2BAA2B,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAEjG,IAAI,CAAC,iCAAiC,GAAG,KAAK,CAAC,oBAAoB,CAAC,8BAA8B,CAAC,CAAC;QAEpG,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,iCAAiC,EAAE,MAAM,CAAC,CAAC;QAE7E,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAE5E,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC,oBAAoB,CAAC,0BAA0B,CAAC,CAAC;QAE5F,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;QAEzE,IAAI,IAAI;mBACG,IAAI,CAAC,qBAAqB;;;;;kBAK3B,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB;kBAC5H,aAAa;;kBAEb,IAAI,CAAC,iCAAiC;kBACtC,IAAI,CAAC,qBAAqB;kBAC1B,IAAI,CAAC,eAAe;;;;0BAIZ,IAAI,CAAC,yBAAyB,iBAAiB,IAAI,CAAC,iBAAiB;;;qBAG1E,IAAI,CAAC,+BAA+B;;;qBAGpC,IAAI,CAAC,aAAa;kBACrB,IAAI,CAAC,gBAAgB;;kBAErB,IAAI,CAAC,cAAc;;;kBAGnB,IAAI,CAAC,2BAA2B;;;;sBAI5B,IAAI,CAAC,qBAAqB;;;;;;;yBAOvB,IAAI,CAAC,aAAa;sBACrB,IAAI,CAAC,gBAAgB;sBACrB,IAAI,CAAC,gBAAgB;;sBAErB,IAAI,CAAC,cAAc;sBACnB,IAAI,CAAC,cAAc;;;;kBAIvB,IAAI,CAAC,6BAA6B;;;;iBAInC,CAAC;QAEV,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;QAErC,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACpD,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;YAClF,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;SAC/F;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,yBAAyB,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC;SAChG;QACD,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,4BAA4B,IAAI,CAAC,qBAAqB,KAAK,CAAC;QACnG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,gCAAgC,IAAI,CAAC,yBAAyB,KAAK,CAAC;QAE3G,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACvE,mBAAmB,CAAC,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAC;QAC/E,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,UAAU,IAAI,IAAI,CAAC;QAElE,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,qBAAqB,GAAG,mBAAmB,CAAC,qBAAqB,CAAC;QACvE,IAAI,CAAC,yBAAyB,GAAG,mBAAmB,CAAC,yBAAyB,CAAC;QAC/E,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,mBAAmB,CAAC,UAAU,CAAC;SAC5D;IACL,CAAC;CACJ;AA7aU;IADN,sBAAsB,CAAC,qBAAqB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;8DAC9E;AAMtC;IADN,sBAAsB,CAAC,8BAA8B,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;kEAClF;AAyatD,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport { ReflectionTextureBaseBlock } from \"../Dual/reflectionTextureBaseBlock\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { Texture } from \"../../../Textures/texture\";\r\nimport type { BaseTexture } from \"../../../Textures/baseTexture\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport type { SubMesh } from \"../../../../Meshes/subMesh\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { Scalar } from \"../../../../Maths/math.scalar\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * Block used to implement the reflection module of the PBR material\r\n */\r\nexport class ReflectionBlock extends ReflectionTextureBaseBlock {\r\n    /** @internal */\r\n    public _defineLODReflectionAlpha: string;\r\n    /** @internal */\r\n    public _defineLinearSpecularReflection: string;\r\n    private _vEnvironmentIrradianceName: string;\r\n    /** @internal */\r\n    public _vReflectionMicrosurfaceInfosName: string;\r\n    /** @internal */\r\n    public _vReflectionInfosName: string;\r\n    /** @internal */\r\n    public _vReflectionFilteringInfoName: string;\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * The properties below are set by the main PBR block prior to calling methods of this class.\r\n     * This is to avoid having to add them as inputs here whereas they are already inputs of the main block, so already known.\r\n     * It's less burden on the user side in the editor part.\r\n     */\r\n\r\n    /** @internal */\r\n    public worldPositionConnectionPoint: NodeMaterialConnectionPoint;\r\n    /** @internal */\r\n    public worldNormalConnectionPoint: NodeMaterialConnectionPoint;\r\n    /** @internal */\r\n    public cameraPositionConnectionPoint: NodeMaterialConnectionPoint;\r\n    /** @internal */\r\n    public viewConnectionPoint: NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * Defines if the material uses spherical harmonics vs spherical polynomials for the\r\n     * diffuse part of the IBL.\r\n     */\r\n    @editableInPropertyPage(\"Spherical Harmonics\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public useSphericalHarmonics: boolean = true;\r\n\r\n    /**\r\n     * Force the shader to compute irradiance in the fragment shader in order to take bump in account.\r\n     */\r\n    @editableInPropertyPage(\"Force irradiance in fragment\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public forceIrradianceInFragment: boolean = false;\r\n\r\n    protected _onGenerateOnlyFragmentCodeChanged(): boolean {\r\n        if (this.position.isConnected) {\r\n            this.generateOnlyFragmentCode = !this.generateOnlyFragmentCode;\r\n            Logger.Error(\"The position input must not be connected to be able to switch!\");\r\n            return false;\r\n        }\r\n\r\n        this._setTarget();\r\n\r\n        return true;\r\n    }\r\n\r\n    protected _setTarget(): void {\r\n        super._setTarget();\r\n        this.getInputByName(\"position\")!.target = this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.Vertex;\r\n        if (this.generateOnlyFragmentCode) {\r\n            this.forceIrradianceInFragment = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create a new ReflectionBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"position\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false, NodeMaterialBlockTargets.Vertex);\r\n        this.registerInput(\"world\", NodeMaterialBlockConnectionPointTypes.Matrix, false, NodeMaterialBlockTargets.Vertex);\r\n        this.registerInput(\"color\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\r\n            \"reflection\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"reflection\", this, NodeMaterialConnectionPointDirection.Output, ReflectionBlock, \"ReflectionBlock\")\r\n        );\r\n\r\n        this.position.addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"ReflectionBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the position input component\r\n     */\r\n    public get position(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this.worldPositionConnectionPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this.worldNormalConnectionPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the world input component\r\n     */\r\n    public get world(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the camera (or eye) position component\r\n     */\r\n    public get cameraPosition(): NodeMaterialConnectionPoint {\r\n        return this.cameraPositionConnectionPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the view input component\r\n     */\r\n    public get view(): NodeMaterialConnectionPoint {\r\n        return this.viewConnectionPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the color input component\r\n     */\r\n    public get color(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the reflection object output component\r\n     */\r\n    public get reflection(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Returns true if the block has a texture (either its own texture or the environment texture from the scene, if set)\r\n     */\r\n    public get hasTexture(): boolean {\r\n        return !!this._getTexture();\r\n    }\r\n\r\n    /**\r\n     * Gets the reflection color (either the name of the variable if the color input is connected, else a default value)\r\n     */\r\n    public get reflectionColor(): string {\r\n        return this.color.isConnected ? this.color.associatedVariableName : \"vec3(1., 1., 1.)\";\r\n    }\r\n\r\n    protected _getTexture(): Nullable<BaseTexture> {\r\n        if (this.texture) {\r\n            return this.texture;\r\n        }\r\n\r\n        return this._scene.environmentTexture;\r\n    }\r\n\r\n    public prepareDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        super.prepareDefines(mesh, nodeMaterial, defines);\r\n\r\n        const reflectionTexture = this._getTexture();\r\n        const reflection = reflectionTexture && reflectionTexture.getTextureMatrix;\r\n\r\n        defines.setValue(\"REFLECTION\", reflection, true);\r\n\r\n        if (!reflection) {\r\n            return;\r\n        }\r\n\r\n        defines.setValue(this._defineLODReflectionAlpha, reflectionTexture!.lodLevelInAlpha, true);\r\n        defines.setValue(this._defineLinearSpecularReflection, reflectionTexture!.linearSpecularLOD, true);\r\n        defines.setValue(this._defineOppositeZ, this._scene.useRightHandedSystem ? !reflectionTexture!.invertZ : reflectionTexture!.invertZ, true);\r\n\r\n        defines.setValue(\"SPHERICAL_HARMONICS\", this.useSphericalHarmonics, true);\r\n        defines.setValue(\"GAMMAREFLECTION\", reflectionTexture!.gammaSpace, true);\r\n        defines.setValue(\"RGBDREFLECTION\", reflectionTexture!.isRGBD, true);\r\n\r\n        if (reflectionTexture && reflectionTexture.coordinatesMode !== Texture.SKYBOX_MODE) {\r\n            if (reflectionTexture.isCube) {\r\n                defines.setValue(\"USESPHERICALFROMREFLECTIONMAP\", true);\r\n                defines.setValue(\"USEIRRADIANCEMAP\", false);\r\n                if (this.forceIrradianceInFragment || this._scene.getEngine().getCaps().maxVaryingVectors <= 8) {\r\n                    defines.setValue(\"USESPHERICALINVERTEX\", false);\r\n                } else {\r\n                    defines.setValue(\"USESPHERICALINVERTEX\", true);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    public bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh, subMesh?: SubMesh) {\r\n        super.bind(effect, nodeMaterial, mesh);\r\n\r\n        const reflectionTexture = this._getTexture();\r\n\r\n        if (!reflectionTexture || !subMesh) {\r\n            return;\r\n        }\r\n\r\n        if (reflectionTexture.isCube) {\r\n            effect.setTexture(this._cubeSamplerName, reflectionTexture);\r\n        } else {\r\n            effect.setTexture(this._2DSamplerName, reflectionTexture);\r\n        }\r\n\r\n        const width = reflectionTexture.getSize().width;\r\n\r\n        effect.setFloat3(this._vReflectionMicrosurfaceInfosName, width, reflectionTexture.lodGenerationScale, reflectionTexture.lodGenerationOffset);\r\n        effect.setFloat2(this._vReflectionFilteringInfoName, width, Scalar.Log2(width));\r\n\r\n        const defines = subMesh.materialDefines as NodeMaterialDefines;\r\n\r\n        const polynomials = reflectionTexture.sphericalPolynomial;\r\n        if (defines.USESPHERICALFROMREFLECTIONMAP && polynomials) {\r\n            if (defines.SPHERICAL_HARMONICS) {\r\n                const preScaledHarmonics = polynomials.preScaledHarmonics;\r\n                effect.setVector3(\"vSphericalL00\", preScaledHarmonics.l00);\r\n                effect.setVector3(\"vSphericalL1_1\", preScaledHarmonics.l1_1);\r\n                effect.setVector3(\"vSphericalL10\", preScaledHarmonics.l10);\r\n                effect.setVector3(\"vSphericalL11\", preScaledHarmonics.l11);\r\n                effect.setVector3(\"vSphericalL2_2\", preScaledHarmonics.l2_2);\r\n                effect.setVector3(\"vSphericalL2_1\", preScaledHarmonics.l2_1);\r\n                effect.setVector3(\"vSphericalL20\", preScaledHarmonics.l20);\r\n                effect.setVector3(\"vSphericalL21\", preScaledHarmonics.l21);\r\n                effect.setVector3(\"vSphericalL22\", preScaledHarmonics.l22);\r\n            } else {\r\n                effect.setFloat3(\"vSphericalX\", polynomials.x.x, polynomials.x.y, polynomials.x.z);\r\n                effect.setFloat3(\"vSphericalY\", polynomials.y.x, polynomials.y.y, polynomials.y.z);\r\n                effect.setFloat3(\"vSphericalZ\", polynomials.z.x, polynomials.z.y, polynomials.z.z);\r\n                effect.setFloat3(\"vSphericalXX_ZZ\", polynomials.xx.x - polynomials.zz.x, polynomials.xx.y - polynomials.zz.y, polynomials.xx.z - polynomials.zz.z);\r\n                effect.setFloat3(\"vSphericalYY_ZZ\", polynomials.yy.x - polynomials.zz.x, polynomials.yy.y - polynomials.zz.y, polynomials.yy.z - polynomials.zz.z);\r\n                effect.setFloat3(\"vSphericalZZ\", polynomials.zz.x, polynomials.zz.y, polynomials.zz.z);\r\n                effect.setFloat3(\"vSphericalXY\", polynomials.xy.x, polynomials.xy.y, polynomials.xy.z);\r\n                effect.setFloat3(\"vSphericalYZ\", polynomials.yz.x, polynomials.yz.y, polynomials.yz.z);\r\n                effect.setFloat3(\"vSphericalZX\", polynomials.zx.x, polynomials.zx.y, polynomials.zx.z);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the code to inject in the vertex shader\r\n     * @param state current state of the node material building\r\n     * @returns the shader code\r\n     */\r\n    public handleVertexSide(state: NodeMaterialBuildState): string {\r\n        let code = super.handleVertexSide(state);\r\n\r\n        state._emitFunctionFromInclude(\"harmonicsFunctions\", `//${this.name}`, {\r\n            replaceStrings: [\r\n                { search: /uniform vec3 vSphericalL00;[\\s\\S]*?uniform vec3 vSphericalL22;/g, replace: \"\" },\r\n                { search: /uniform vec3 vSphericalX;[\\s\\S]*?uniform vec3 vSphericalZX;/g, replace: \"\" },\r\n            ],\r\n        });\r\n\r\n        const reflectionVectorName = state._getFreeVariableName(\"reflectionVector\");\r\n\r\n        this._vEnvironmentIrradianceName = state._getFreeVariableName(\"vEnvironmentIrradiance\");\r\n\r\n        state._emitVaryingFromString(this._vEnvironmentIrradianceName, \"vec3\", \"defined(USESPHERICALFROMREFLECTIONMAP) && defined(USESPHERICALINVERTEX)\");\r\n\r\n        state._emitUniformFromString(\"vSphericalL00\", \"vec3\", \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL1_1\", \"vec3\", \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL10\", \"vec3\", \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL11\", \"vec3\", \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL2_2\", \"vec3\", \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL2_1\", \"vec3\", \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL20\", \"vec3\", \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL21\", \"vec3\", \"SPHERICAL_HARMONICS\");\r\n        state._emitUniformFromString(\"vSphericalL22\", \"vec3\", \"SPHERICAL_HARMONICS\");\r\n\r\n        state._emitUniformFromString(\"vSphericalX\", \"vec3\", \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalY\", \"vec3\", \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalZ\", \"vec3\", \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalXX_ZZ\", \"vec3\", \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalYY_ZZ\", \"vec3\", \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalZZ\", \"vec3\", \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalXY\", \"vec3\", \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalYZ\", \"vec3\", \"SPHERICAL_HARMONICS\", true);\r\n        state._emitUniformFromString(\"vSphericalZX\", \"vec3\", \"SPHERICAL_HARMONICS\", true);\r\n\r\n        code += `#if defined(USESPHERICALFROMREFLECTIONMAP) && defined(USESPHERICALINVERTEX)\r\n                vec3 ${reflectionVectorName} = vec3(${this._reflectionMatrixName} * vec4(normalize(${this.worldNormal.associatedVariableName}).xyz, 0)).xyz;\r\n                #ifdef ${this._defineOppositeZ}\r\n                    ${reflectionVectorName}.z *= -1.0;\r\n                #endif\r\n                ${this._vEnvironmentIrradianceName} = computeEnvironmentIrradiance(${reflectionVectorName});\r\n            #endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * Gets the main code of the block (fragment side)\r\n     * @param state current state of the node material building\r\n     * @param normalVarName name of the existing variable corresponding to the normal\r\n     * @returns the shader code\r\n     */\r\n    public getCode(state: NodeMaterialBuildState, normalVarName: string): string {\r\n        let code = \"\";\r\n\r\n        this.handleFragmentSideInits(state);\r\n\r\n        state._emitFunctionFromInclude(\"harmonicsFunctions\", `//${this.name}`, {\r\n            replaceStrings: [\r\n                { search: /uniform vec3 vSphericalL00;[\\s\\S]*?uniform vec3 vSphericalL22;/g, replace: \"\" },\r\n                { search: /uniform vec3 vSphericalX;[\\s\\S]*?uniform vec3 vSphericalZX;/g, replace: \"\" },\r\n            ],\r\n        });\r\n\r\n        state._emitFunction(\r\n            \"sampleReflection\",\r\n            `\r\n            #ifdef ${this._define3DName}\r\n                #define sampleReflection(s, c) textureCube(s, c)\r\n            #else\r\n                #define sampleReflection(s, c) texture2D(s, c)\r\n            #endif\\n`,\r\n            `//${this.name}`\r\n        );\r\n\r\n        state._emitFunction(\r\n            \"sampleReflectionLod\",\r\n            `\r\n            #ifdef ${this._define3DName}\r\n                #define sampleReflectionLod(s, c, l) textureCubeLodEXT(s, c, l)\r\n            #else\r\n                #define sampleReflectionLod(s, c, l) texture2DLodEXT(s, c, l)\r\n            #endif\\n`,\r\n            `//${this.name}`\r\n        );\r\n\r\n        const computeReflectionCoordsFunc = `\r\n            vec3 computeReflectionCoordsPBR(vec4 worldPos, vec3 worldNormal) {\r\n                ${this.handleFragmentSideCodeReflectionCoords(\"worldNormal\", \"worldPos\", true, true)}\r\n                return ${this._reflectionVectorName};\r\n            }\\n`;\r\n\r\n        state._emitFunction(\"computeReflectionCoordsPBR\", computeReflectionCoordsFunc, `//${this.name}`);\r\n\r\n        this._vReflectionMicrosurfaceInfosName = state._getFreeVariableName(\"vReflectionMicrosurfaceInfos\");\r\n\r\n        state._emitUniformFromString(this._vReflectionMicrosurfaceInfosName, \"vec3\");\r\n\r\n        this._vReflectionInfosName = state._getFreeVariableName(\"vReflectionInfos\");\r\n\r\n        this._vReflectionFilteringInfoName = state._getFreeVariableName(\"vReflectionFilteringInfo\");\r\n\r\n        state._emitUniformFromString(this._vReflectionFilteringInfoName, \"vec2\");\r\n\r\n        code += `#ifdef REFLECTION\r\n            vec2 ${this._vReflectionInfosName} = vec2(1., 0.);\r\n\r\n            reflectionOutParams reflectionOut;\r\n\r\n            reflectionBlock(\r\n                ${this.generateOnlyFragmentCode ? this._worldPositionNameInFragmentOnlyMode : \"v_\" + this.worldPosition.associatedVariableName}.xyz,\r\n                ${normalVarName},\r\n                alphaG,\r\n                ${this._vReflectionMicrosurfaceInfosName},\r\n                ${this._vReflectionInfosName},\r\n                ${this.reflectionColor},\r\n            #ifdef ANISOTROPIC\r\n                anisotropicOut,\r\n            #endif\r\n            #if defined(${this._defineLODReflectionAlpha}) && !defined(${this._defineSkyboxName})\r\n                NdotVUnclamped,\r\n            #endif\r\n            #ifdef ${this._defineLinearSpecularReflection}\r\n                roughness,\r\n            #endif\r\n            #ifdef ${this._define3DName}\r\n                ${this._cubeSamplerName},\r\n            #else\r\n                ${this._2DSamplerName},\r\n            #endif\r\n            #if defined(NORMAL) && defined(USESPHERICALINVERTEX)\r\n                ${this._vEnvironmentIrradianceName},\r\n            #endif\r\n            #ifdef USESPHERICALFROMREFLECTIONMAP\r\n                #if !defined(NORMAL) || !defined(USESPHERICALINVERTEX)\r\n                    ${this._reflectionMatrixName},\r\n                #endif\r\n            #endif\r\n            #ifdef USEIRRADIANCEMAP\r\n                irradianceSampler, // ** not handled **\r\n            #endif\r\n            #ifndef LODBASEDMICROSFURACE\r\n                #ifdef ${this._define3DName}\r\n                    ${this._cubeSamplerName},\r\n                    ${this._cubeSamplerName},\r\n                #else\r\n                    ${this._2DSamplerName},\r\n                    ${this._2DSamplerName},\r\n                #endif\r\n            #endif\r\n            #ifdef REALTIME_FILTERING\r\n                ${this._vReflectionFilteringInfoName},\r\n            #endif\r\n                reflectionOut\r\n            );\r\n        #endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        this._scene = state.sharedData.scene;\r\n\r\n        if (state.target !== NodeMaterialBlockTargets.Fragment) {\r\n            this._defineLODReflectionAlpha = state._getFreeDefineName(\"LODINREFLECTIONALPHA\");\r\n            this._defineLinearSpecularReflection = state._getFreeDefineName(\"LINEARSPECULARREFLECTION\");\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        if (this.texture) {\r\n            codeString += `${this._codeVariableName}.texture.gammaSpace = ${this.texture.gammaSpace};\\n`;\r\n        }\r\n        codeString += `${this._codeVariableName}.useSphericalHarmonics = ${this.useSphericalHarmonics};\\n`;\r\n        codeString += `${this._codeVariableName}.forceIrradianceInFragment = ${this.forceIrradianceInFragment};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.useSphericalHarmonics = this.useSphericalHarmonics;\r\n        serializationObject.forceIrradianceInFragment = this.forceIrradianceInFragment;\r\n        serializationObject.gammaSpace = this.texture?.gammaSpace ?? true;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.useSphericalHarmonics = serializationObject.useSphericalHarmonics;\r\n        this.forceIrradianceInFragment = serializationObject.forceIrradianceInFragment;\r\n        if (this.texture) {\r\n            this.texture.gammaSpace = serializationObject.gammaSpace;\r\n        }\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ReflectionBlock\", ReflectionBlock);\r\n"]}