{"version": 3, "file": "solidParticleSystem.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Particles/solidParticleSystem.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAC/E,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,gCAAgC,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD,OAAO,EAAE,mBAAmB,EAAE,aAAa,EAAE,UAAU,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAC;AAEtG,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AACvD,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AAE5C,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAI3D;;;;;;;;GAQG;AACH,MAAM,OAAO,mBAAmB;IA8H5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACH,YACI,IAAY,EACZ,KAAY,EACZ,OAYC;QAzKL;;;WAGG;QACI,cAAS,GAAoB,IAAI,KAAK,EAAiB,CAAC;QAC/D;;WAEG;QACI,gBAAW,GAAW,CAAC,CAAC;QAC/B;;WAEG;QACI,cAAS,GAAY,KAAK,CAAC;QAClC;;WAEG;QACI,qBAAgB,GAAY,KAAK,CAAC;QACzC;;WAEG;QACI,YAAO,GAAW,CAAC,CAAC;QAS3B;;;WAGG;QACI,SAAI,GAAQ,EAAE,CAAC;QA+BtB;;;WAGG;QACI,iBAAY,GAAY,KAAK,CAAC;QACrC;;;WAGG;QACI,yBAAoB,GAAW,GAAG,CAAC;QAGhC,eAAU,GAAa,IAAI,KAAK,EAAU,CAAC;QAC3C,aAAQ,GAAa,IAAI,KAAK,EAAU,CAAC;QACzC,aAAQ,GAAa,IAAI,KAAK,EAAU,CAAC;QACzC,YAAO,GAAa,IAAI,KAAK,EAAU,CAAC;QACxC,SAAI,GAAa,IAAI,KAAK,EAAU,CAAC;QAOrC,WAAM,GAAW,CAAC,CAAC,CAAC,gBAAgB;QACpC,eAAU,GAAY,IAAI,CAAC;QAC3B,cAAS,GAAY,KAAK,CAAC;QAC3B,2BAAsB,GAAG,KAAK,CAAC;QAC/B,mBAAc,GAAY,KAAK,CAAC;QAChC,eAAU,GAAY,KAAK,CAAC;QAC5B,gBAAW,GAAY,KAAK,CAAC;QAC7B,kBAAa,GAAW,CAAC,CAAC;QAC1B,UAAK,GAAkB,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QACvE,WAAM,GAAW,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxC,0BAAqB,GAAY,IAAI,CAAC;QACtC,4BAAuB,GAAY,IAAI,CAAC;QACxC,6BAAwB,GAAY,IAAI,CAAC;QACzC,2BAAsB,GAAY,KAAK,CAAC;QACxC,wBAAmB,GAAY,KAAK,CAAC;QACrC,4BAAuB,GAAY,KAAK,CAAC;QACzC,wBAAmB,GAAY,IAAI,CAAC;QAEpC,8BAAyB,GAAG,KAAK,CAAC;QAClC,wBAAmB,GAAY,KAAK,CAAC;QACrC,iBAAY,GAAY,KAAK,CAAC;QAC9B,gBAAW,GAAY,IAAI,CAAC;QAC5B,oBAAe,GAAW,CAAC,CAAC;QAC5B,aAAQ,GAAa,EAAE,CAAC,CAAC,mDAAmD;QAC5E,0BAAqB,GAAY,KAAK,CAAC;QACvC,sBAAiB,GAAY,KAAK,CAAC;QAGnC,uBAAkB,GAAG,CAAC,EAAuB,EAAE,EAAuB,EAAE,EAAE,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,CAAC,UAAU,CAAC;QACzG,0BAAqB,GAAG,CAAC,EAAuB,EAAE,EAAuB,EAAE,EAAE,CAAC,EAAE,CAAC,aAAa,GAAG,EAAE,CAAC,aAAa,CAAC;QAKlH,yBAAoB,GAAY,KAAK,CAAC;QAEtC,yBAAoB,GAAY,KAAK,CAAC;QAgD5C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,OAAO,GAAiB,KAAK,CAAC,YAAY,CAAC;QAChD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC,CAAU,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC;QAC/D,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,CAAC,CAAU,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC;QACrE,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,CAAC,CAAU,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,KAAK,CAAC;QACpF,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,CAAC,CAAU,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC;QAC7E,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC;QACxF,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC,CAAU,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC;QACjE,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,CAAC,CAAU,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC;QACnF,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC,CAAU,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,CAAC;QAC1E,IAAI,CAAC,oBAAoB,GAAG,OAAO,IAAI,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC;QACvG,IAAI,CAAC,mBAAmB,GAAG,OAAO,EAAE,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,CAAC;QAC5F,IAAI,CAAC,uBAAuB,GAAG,OAAO,EAAE,sBAAsB,CAAC,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC,KAAK,CAAC;QACxG,IAAI,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;YAC5C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC;SACvC;aAAM;YACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SAC1B;QACD,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC,CAAC;YAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;SAClD;QACD,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC/C,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;SAClC;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,GAAG,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAClF,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;YACrB,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;SAClC;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,mBAAmB,EAAE,CAAC;IAChD,CAAC;IAED;;;;OAIG;IACI,SAAS;QACZ,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,EAAE;YAChC,OAAO,IAAI,CAAC,IAAI,CAAC;SACpB;QACD,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACtC,MAAM,QAAQ,GAAG,UAAU,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7E,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAC3B,QAAQ,CAAC,OAAO,EAAE,CAAC;SACtB;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtG,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtD,IAAI,CAAC,MAAM,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEhD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,gCAAgC;YAChC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SACpB;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAChD,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAC,iCAAiC;SACrE;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SAChF;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,cAAc,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtD,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,qFAAqF;YACrF,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAChC;QACD,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;QACpC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;QACvE,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC;QAC7D,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;QAEzD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;SACpD;QACD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;SAC1D;QAED,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACnD,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;QAEtC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;gBACvC,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;gBACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;oBAC3B,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAChB,IAAI,CAAC,IAAI,CAAC,EAAE;wBACR,MAAM,UAAU,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;wBACrD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC;wBAC1C,MAAM,EAAE,CAAC;qBACZ;iBACJ;aACJ;SACJ;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,cAAc;YACd,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAC5E,IAAI,CAAC,QAAS,GAAG,IAAI,CAAC;aAC/B;YACK,IAAI,CAAC,UAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,QAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,IAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,OAAQ,GAAG,IAAI,CAAC;YAE3B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;aAC7B;SACJ;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAEO,UAAU,CAAC,IAAU,EAAE,MAAc;QACzC,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE;YACf,IAAK,IAAI,CAAC,QAA6B,EAAE,cAAc,EAAE;gBACrD,MAAM,GAAI,IAAI,CAAC,QAA6B,CAAC,cAAe,CAAC,gBAAgB,CAAC;aACjF;iBAAM,IAAK,IAAI,CAAC,QAAwB,EAAE,aAAa,EAAE;gBACtD,MAAM,GAAI,IAAI,CAAC,QAAwB,CAAC,aAAc,CAAC,gBAAgB,CAAC;aAC3E;SACJ;QAED,OAAO,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACI,MAAM,CAAC,IAAU,EAAE,OAA8F;QACpH,IAAI,IAAI,GAAW,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,MAAM,GAAW,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,KAAK,GAAW,CAAC,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,OAAO,GAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAC5E,MAAM,OAAO,GAAiB,IAAI,CAAC,UAAU,EAAE,CAAC;QAChD,MAAM,MAAM,GAAe,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7F,MAAM,OAAO,GAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACzE,MAAM,OAAO,GAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAC1E,MAAM,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;QAEpE,IAAI,CAAC,GAAW,CAAC,CAAC,CAAC,gBAAgB;QACnC,MAAM,WAAW,GAAW,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,sCAAsC;QACtF,2BAA2B;QAC3B,IAAI,MAAM,EAAE;YACR,MAAM,GAAG,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;YACrD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC;YACxC,KAAK,GAAG,CAAC,CAAC;SACb;aAAM;YACH,IAAI,GAAG,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;SAClD;QAED,MAAM,QAAQ,GAAa,EAAE,CAAC,CAAC,oBAAoB;QACnD,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,QAAQ,GAAa,EAAE,CAAC,CAAC,kBAAkB;QACjD,MAAM,OAAO,GAAa,EAAE,CAAC,CAAC,aAAa;QAC3C,MAAM,QAAQ,GAAa,EAAE,CAAC,CAAC,iBAAiB;QAChD,MAAM,UAAU,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAC3C,MAAM,KAAK,GAAW,IAAI,CAAC;QAE3B,OAAO,CAAC,GAAG,WAAW,EAAE;YACpB,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YACvD,IAAI,CAAC,GAAG,WAAW,GAAG,IAAI,EAAE;gBACxB,IAAI,GAAG,WAAW,GAAG,CAAC,CAAC;aAC1B;YACD,oBAAoB;YACpB,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YACpB,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YACpB,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YACpB,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;YACnB,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YAEpB,6BAA6B;YAC7B,IAAI,EAAE,GAAW,CAAC,CAAC;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACzC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAClB,MAAM,CAAC,GAAW,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM,EAAE,GAAW,CAAC,GAAG,CAAC,CAAC;gBACzB,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC7D,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC7D,IAAI,MAAM,EAAE;oBACR,MAAM,EAAE,GAAW,CAAC,GAAG,CAAC,CAAC;oBACzB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;iBAC5C;gBACD,IAAI,OAAO,EAAE;oBACT,MAAM,EAAE,GAAW,CAAC,GAAG,CAAC,CAAC;oBACzB,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;iBACjF;gBACD,EAAE,EAAE,CAAC;aACR;YAED,gDAAgD;YAChD,IAAI,GAAG,GAAW,IAAI,CAAC,WAAW,CAAC;YACnC,MAAM,KAAK,GAAc,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,OAAO,GAAa,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACtD,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;YAClC,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;YAClC,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;YAElC,sCAAsC;YACtC,UAAU,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACnC,IAAI,CAAS,CAAC;YACd,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/B,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aACnC;YACD,UAAU,CAAC,YAAY,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YAE1C,oDAAoD;YACpD,kDAAkD;YAClD,MAAM,OAAO,GAAY,IAAI,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACnE,MAAM,OAAO,GAAY,IAAI,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;YACtE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/B,KAAK,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;gBACrC,OAAO,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtE,OAAO,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACzE;YACD,IAAI,KAAK,CAAC;YACV,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,KAAK,GAAG,IAAI,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aAC9C;YACD,IAAI,QAAQ,GAAG,IAAI,CAAC;YACpB,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxB,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;aACzE;YACD,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YAE1H,8BAA8B;YAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACxC,IAAI,CAAC,YAAY,CACb,IAAI,CAAC,MAAM,EACX,UAAU,EACV,KAAK,EACL,IAAI,CAAC,UAAU,EACf,QAAQ,EACR,IAAI,CAAC,QAAQ,EACb,OAAO,EACP,IAAI,CAAC,IAAI,EACT,QAAQ,EACR,IAAI,CAAC,OAAO,EACZ,QAAQ,EACR,IAAI,CAAC,QAAQ,EACb,GAAG,EACH,CAAC,EACD,IAAI,EACJ,UAAU,CACb,CAAC;YACF,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YACxH,mCAAmC;YACnC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAEjE,IAAI,CAAC,OAAO,EAAE;gBACV,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;gBAC5B,GAAG,EAAE,CAAC;gBACN,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,IAAI,CAAC,eAAe,EAAE,CAAC;aAC1B;YACD,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,CAAC,IAAI,IAAI,CAAC;SACb;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,yDAAyD;QAClF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACO,qBAAqB;QAC3B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACxC,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC5C,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;YAErC,mEAAmE;YACnE,mGAAmG;YACnG,IAAI,QAAQ,CAAC,kBAAkB,EAAE;gBAC7B,QAAQ,CAAC,kBAAkB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;aAC1D;iBAAM;gBACH,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;gBACnC,UAAU,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;gBACrF,UAAU,CAAC,gBAAgB,EAAE,CAAC;aACjC;YACD,UAAU,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;YAE/C,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;gBACtC,GAAG,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,CAAC;gBACrB,OAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,iBAAiB,EAAE,SAAS,CAAC,CAAC;gBAC/I,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;aAC/C;YACD,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;SACnB;IACL,CAAC;IAED;;;OAGG;IACO,UAAU;QAChB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC9B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACO,YAAY,CAClB,CAAS,EACT,GAAW,EACX,KAAgB,EAChB,SAAmB,EACnB,OAAqB,EACrB,OAAiB,EACjB,MAA+B,EAC/B,GAAa,EACb,OAAgC,EAChC,MAAgB,EAChB,OAAgC,EAChC,OAAiB,EACjB,GAAW,EACX,UAAkB,EAClB,OAAY,EACZ,KAAiB;QAEjB,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,CAAC,CAAC;QAEV,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACxB,MAAM,UAAU,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAC7D,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC7B,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,MAAM,UAAU,GAAG,KAAK,CAAC,SAAU,CAAC,QAAQ,CAAC;YAC7C,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACtD,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE;gBACxE,mBAAmB,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBACzD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,SAAU,CAAC,CAAC;aAC1C;YACD,MAAM,MAAM,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAC/C,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;SAC/B;QAED,IAAI,OAAO,IAAI,OAAO,CAAC,gBAAgB,EAAE;YACrC,kCAAkC;YAClC,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;YAChD,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;SACzC;QAED,8EAA8E;QAC9E,IAAI,UAAU,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC;QACrC,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC;QACjC,MAAM,KAAK,GAAG,SAAS,CAAC,EAAE,CAAC;QAC3B,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,oBAAoB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAChC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAEpD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACpC;aAAM;YACH,oBAAoB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;SAC9C;QAED,MAAM,kBAAkB,GAAG,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC;QAC7D,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/B,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,IAAI,CAAC,KAAK,EAAE;gBACZ,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACjC;YACD,IAAI,MAAM,EAAE;gBACR,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAClD;YACD,IAAI,kBAAkB,EAAE;gBACpB,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;aAC9C;YAED,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YACrE,OAAO,CAAC,yBAAyB,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;YACpE,UAAU,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;YAEzD,IAAI,MAAM,EAAE;gBACR,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC;gBACzB,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBACvG,CAAC,IAAI,CAAC,CAAC;aACV;YAED,IAAI,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;aAClC;iBAAM;gBACH,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC1B,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;oBACrC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBACrB,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACzB,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACzB,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;iBAC5B;qBAAM;oBACH,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;oBACd,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;oBACd,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;oBACd,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;iBACjB;aACJ;YACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACxE,CAAC,IAAI,CAAC,CAAC;YAEP,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,OAAO,EAAE;gBACnC,OAAO,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;gBACzG,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;gBACpD,CAAC,IAAI,CAAC,CAAC;aACV;SACJ;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACjC,MAAM,WAAW,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1B,IAAI,WAAW,GAAG,KAAK,EAAE;gBACrB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;aAC5B;SACJ;QAED,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;SAC/F;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACO,WAAW,CAAC,SAAkC;QACpD,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YAC1C,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;SAC/C;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACO,aAAa,CAAC,GAA4B;QAChD,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,IAAI,GAAG,EAAE;YACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACjC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aACxB;SACJ;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;;;;;;;OAYG;IACO,YAAY,CAClB,GAAW,EACX,EAAU,EACV,MAAc,EACd,MAAc,EACd,KAAiB,EACjB,OAAe,EACf,UAAkB,EAClB,QAAgC,IAAI,EACpC,UAAwB,IAAI;QAE5B,MAAM,EAAE,GAAG,IAAI,aAAa,CAAC,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC/F,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;QAClD,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChB,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,QAAQ,CAAC,IAAU,EAAE,EAAU,EAAE,OAAwE;QAC5G,MAAM,OAAO,GAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAC5E,MAAM,OAAO,GAAiB,IAAI,CAAC,UAAU,EAAE,CAAC;QAChD,MAAM,MAAM,GAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACrE,MAAM,OAAO,GAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACzE,MAAM,OAAO,GAAe,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAC/C,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,MAAM,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACxD,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvD,MAAM,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;QACpE,IAAI,MAAM,GAA2B,IAAI,CAAC;QAC1C,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;SACnC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAE3C,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC;QAC1D,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC;QACxD,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;SACzE;QACD,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAEtI,YAAY;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SAChI;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,8DAA8D;QACvF,OAAO,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;;OAGG;IACO,gBAAgB,CAAC,QAAuB,EAAE,QAAiB,KAAK;QACtE,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACxB,IAAI,QAAQ,CAAC,MAAM,CAAC,iBAAiB,EAAE;YACnC,2CAA2C;YAC3C,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;SAC9E;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACxC,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,oBAAoB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE1C,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAElC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAE5D,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,oBAAoB,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;SACtD;aAAM;YACH,oBAAoB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;SAC9C;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;QAErC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;YACtC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9B,IAAI,QAAQ,CAAC,MAAM,CAAC,eAAe,EAAE;gBACjC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,kCAAkC;aAC3F;YAED,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YACrE,OAAO,CAAC,yBAAyB,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;YACpE,UAAU;iBACL,UAAU,CAAC,oBAAoB,CAAC;iBAChC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;iBACzB,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;SAC3D;QACD,IAAI,KAAK,EAAE;YACP,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC9B,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC9B,QAAQ,CAAC,kBAAkB,GAAG,IAAI,CAAC;YACnC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC7B,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACzB,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC3B,QAAQ,CAAC,kBAAkB,GAAG,KAAK,CAAC;YACpC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;SAC5B;IACL,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,QAAiB,KAAK;QACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;SACnD;QACD,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACzF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG;IACI,eAAe,CAAC,KAAa,EAAE,GAAW;QAC7C,MAAM,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAC5E,OAAO,EAAE,CAAC;SACb;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC;QACnC,IAAI,GAAG,GAAG,SAAS,GAAG,CAAC,EAAE;YACrB,gHAAgH;YAChH,MAAM,cAAc,GAAG,GAAG,GAAG,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;YACxE,MAAM,OAAO,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;YACvE,KAAK,IAAI,CAAC,GAAG,cAAc,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;gBAC7C,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC;gBACtB,IAAI,CAAC,IAAI,IAAI,OAAO,CAAC;aACxB;SACJ;QACD,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAC5C,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACzB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC/C,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;SAClC;QACD,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC9B,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;YAC3B,MAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC;YACpC,MAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC;YACpC,MAAM,WAAW,GAAG,KAAK,CAAC,YAAY,CAAC;YACvC,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;YAChC,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;YACjB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YAC/B,IAAI,CAAC,YAAY,CACb,IAAI,CAAC,MAAM,EACX,GAAG,EACH,KAAK,EACL,IAAI,CAAC,UAAU,EACf,YAAY,EACZ,IAAI,CAAC,QAAQ,EACb,QAAQ,EACR,IAAI,CAAC,IAAI,EACT,WAAW,EACX,IAAI,CAAC,OAAO,EACZ,YAAY,EACZ,IAAI,CAAC,QAAQ,EACb,QAAQ,CAAC,GAAG,EACZ,QAAQ,CAAC,UAAU,EACnB,IAAI,EACJ,KAAK,CACR,CAAC;YACF,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;YAC5B,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC;SAC9B;QACD,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,8DAA8D;QACvF,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAAC,kBAAmC;QAC/D,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO,IAAI,CAAC;SACf;QACD,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,cAAc,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QACnD,MAAM,EAAE,GAAG,kBAAkB,CAAC,MAAM,CAAC;QACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;YACzB,MAAM,EAAE,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC;YACxB,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;YAC3B,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC;YAC/B,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC;YAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,YAAY,CAAC;YACnC,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC;YAC/B,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;YACrC,IAAI,CAAC,gBAAgB,GAAG,KAAK,IAAI,IAAI,CAAC,gBAAgB,CAAC;YACvD,MAAM,MAAM,GAAG,EAAE,CAAC,eAAe,EAAE,CAAC;YACpC,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC3I,EAAE,CAAC,SAAS,CAAC,OAAQ,CAAC,CAAC;YACvB,UAAU,EAAE,CAAC;YACb,IAAI,cAAc,IAAI,EAAE,CAAC,OAAO,EAAE;gBAC9B,cAAc,GAAG,EAAE,CAAC,OAAO,CAAC;gBAC5B,UAAU,GAAG,CAAC,CAAC;aAClB;SACJ;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,8DAA8D;QACvF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACO,kBAAkB,CACxB,GAAW,EACX,CAAS,EACT,UAAsB,EACtB,KAAgB,EAChB,OAAqB,EACrB,MAA+B,EAC/B,OAAgC,EAChC,OAAgC,EAChC,MAA8B,EAC9B,OAAqB,EACrB,OAAY;QAEZ,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACxC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CACjC,IAAI,CAAC,MAAM,EACX,UAAU,EACV,KAAK,EACL,IAAI,CAAC,UAAU,EACf,OAAO,EACP,IAAI,CAAC,QAAQ,EACb,MAAM,EACN,IAAI,CAAC,IAAI,EACT,OAAO,EACP,IAAI,CAAC,OAAO,EACZ,OAAO,EACP,IAAI,CAAC,QAAQ,EACb,GAAG,EACH,CAAC,EACD,OAAO,EACP,UAAU,CACb,CAAC;QACF,IAAI,EAAE,GAA4B,IAAI,CAAC;QACvC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YAC3I,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC3C,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC3C,IAAI,WAAW,CAAC,kBAAkB,EAAE;gBAChC,IAAI,EAAE,CAAC,kBAAkB,EAAE;oBACvB,EAAE,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;iBAClE;qBAAM;oBACH,EAAE,CAAC,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;iBAClE;aACJ;YACD,IAAI,WAAW,CAAC,KAAK,EAAE;gBACnB,IAAI,EAAE,CAAC,KAAK,EAAE;oBACV,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;iBACxC;qBAAM;oBACH,EAAE,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;iBACxC;aACJ;YACD,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACzC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACjC,IAAI,WAAW,CAAC,aAAa,KAAK,IAAI,EAAE;gBACpC,EAAE,CAAC,aAAa,GAAG,WAAW,CAAC,aAAa,CAAC;aAChD;YACD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;aACjC;SACJ;QACD,IAAI,CAAC,OAAO,EAAE;YACV,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;YAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;QACD,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;;;;;;;;OASG;IACI,YAAY,CAAC,QAAgB,CAAC,EAAE,MAAc,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,SAAkB,IAAI;QAC7F,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE;YACtC,OAAO,IAAI,CAAC;SACf;QAED,sBAAsB;QACtB,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;QAE/C,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;QACtC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAC1C,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,mBAAmB,CAAC;QAEvE,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC;QACvC,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC9D,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC9D,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC9D,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACzD,MAAM,mBAAmB,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAEtD,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC;QACrC,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC;QACjC,MAAM,KAAK,GAAG,SAAS,CAAC,EAAE,CAAC;QAE3B,sDAAsD;QACtD,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;SACtD;QACD,+CAA+C;QAC/C,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,4EAA4E;YAC5E,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YACnD,OAAO,CAAC,oBAAoB,CAAC,UAAU,EAAE,cAAc,EAAE,QAAQ,CAAC,CAAC;YACnE,QAAQ,CAAC,SAAS,EAAE,CAAC;YACrB,+DAA+D;YAC/D,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC9C,OAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,cAAc,EAAE,QAAQ,CAAC,CAAC;YAClG,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACjD,QAAQ,CAAC,SAAS,EAAE,CAAC;YACrB,QAAQ,CAAC,SAAS,EAAE,CAAC;SACxB;QAED,4EAA4E;QAC5E,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,EAAE,mBAAmB,CAAC,CAAC,CAAC,4BAA4B;SACpI;QAED,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAChC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,yDAAyD;QACtE,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,+EAA+E;QAC9F,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,mDAAmD;QACnE,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC,yEAAyE;QAC7F,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,6CAA6C;QAC5D,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,mEAAmE;QACpF,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,4CAA4C;QAExD,IAAI,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC9B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACnC;QAED,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAC3D,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;gBAC3C,0GAA0G;gBAC1G,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACjD,IAAI,YAAY,EAAE;oBACd,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;oBACvC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;iBAC1C;aACJ;SACJ;QAED,gBAAgB;QAChB,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;QACnC,MAAM,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7B,UAAU,GAAG,IAAI,GAAG,CAAC,CAAC;QACtB,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC;QAEnB,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE;YAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAEnC,iEAAiE;YACjE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAE9B,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;YACrC,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC;YACzC,MAAM,sBAAsB,GAAG,QAAQ,CAAC,eAAe,CAAC;YACxD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,QAAQ,CAAC;YAC3C,MAAM,gBAAgB,GAAG,QAAQ,CAAC,QAAQ,CAAC;YAC3C,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC;YACzC,MAAM,sBAAsB,GAAG,QAAQ,CAAC,eAAe,CAAC;YAExD,6CAA6C;YAC7C,IAAI,kBAAkB,EAAE;gBACpB,MAAM,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBACzC,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;gBACvB,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC;gBACxB,GAAG,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;gBACnD,GAAG,CAAC,UAAU,GAAG,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC;aACpF;YAED,oEAAoE;YACpE,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE;gBACpG,0CAA0C;gBAC1C,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC;gBAClB,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC;gBAChB,UAAU,IAAI,EAAE,GAAG,CAAC,CAAC;gBACrB,OAAO,IAAI,EAAE,GAAG,CAAC,CAAC;gBAClB,SAAS;aACZ;YAED,IAAI,QAAQ,CAAC,SAAS,EAAE;gBACpB,QAAQ,CAAC,eAAe,GAAG,KAAK,CAAC,CAAC,iCAAiC;gBAEnE,MAAM,WAAW,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;gBACpC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;gBAE3D,2BAA2B;gBAC3B,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,gBAAgB,CAAC,CAAC,GAAG,GAAG,CAAC;oBACzB,gBAAgB,CAAC,CAAC,GAAG,GAAG,CAAC;iBAC5B;gBACD,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,SAAS,EAAE;oBACjD,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;iBACzC;gBAED,MAAM,iBAAiB,GAAG,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC;gBACrD,IAAI,iBAAiB,EAAE;oBACnB,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAS,CAAC,CAAC;oBACxD,IAAI,MAAM,EAAE;wBACR,MAAM,oBAAoB,GAAG,MAAM,CAAC,eAAe,CAAC;wBACpD,MAAM,oBAAoB,GAAG,MAAM,CAAC,eAAe,CAAC;wBAEpD,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;wBAC5J,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;wBAC5J,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;wBAE5J,sBAAsB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,GAAG,QAAQ,CAAC;wBAC7D,sBAAsB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,GAAG,QAAQ,CAAC;wBAC7D,sBAAsB,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,GAAG,QAAQ,CAAC;wBAE7D,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,SAAS,EAAE;4BACjD,MAAM,eAAe,GAAG,SAAS,CAAC,CAAC,CAAC;4BACpC,sBAAsB,CAAC,CAAC,CAAC;gCACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;4BAC/I,sBAAsB,CAAC,CAAC,CAAC;gCACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;4BAC/I,sBAAsB,CAAC,CAAC,CAAC;gCACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;4BAC/I,sBAAsB,CAAC,CAAC,CAAC;gCACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;4BAC/I,sBAAsB,CAAC,CAAC,CAAC;gCACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;4BAC/I,sBAAsB,CAAC,CAAC,CAAC;gCACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;4BAC/I,sBAAsB,CAAC,CAAC,CAAC;gCACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;4BAChJ,sBAAsB,CAAC,CAAC,CAAC;gCACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;4BAChJ,sBAAsB,CAAC,CAAC,CAAC;gCACrB,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;yBACnJ;qBACJ;yBAAM;wBACH,iDAAiD;wBACjD,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;qBAC5B;iBACJ;qBAAM;oBACH,sBAAsB,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC;oBAC9C,sBAAsB,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC;oBAC9C,sBAAsB,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC;oBAE9C,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,SAAS,EAAE;wBACjD,MAAM,eAAe,GAAG,SAAS,CAAC,CAAC,CAAC;wBACpC,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC/C,sBAAsB,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,EAAE,CAAC,CAAC;qBACnD;iBACJ;gBAED,MAAM,oBAAoB,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;gBAC7C,IAAI,QAAQ,CAAC,kBAAkB,EAAE;oBAC7B,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;iBACpC;qBAAM;oBACH,oBAAoB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;iBAC9C;gBAED,uBAAuB;gBACvB,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;oBAClC,GAAG,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,CAAC;oBACrB,MAAM,GAAG,UAAU,GAAG,EAAE,GAAG,CAAC,CAAC;oBAC7B,KAAK,GAAG,OAAO,GAAG,EAAE,GAAG,CAAC,CAAC;oBACzB,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;oBAClB,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;oBAElB,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC9B,IAAI,IAAI,CAAC,qBAAqB,IAAI,QAAQ,CAAC,KAAK,EAAE;wBAC9C,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;qBACrC;oBACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;wBAC9B,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;qBAClD;oBACD,IAAI,IAAI,CAAC,sBAAsB,EAAE;wBAC7B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;qBACtD;oBAED,YAAY;oBACZ,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;oBAChE,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;oBAChE,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;oBAEhE,IAAI,QAAQ,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;oBAC/H,IAAI,QAAQ,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;oBAC/H,IAAI,QAAQ,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;oBAE/H,QAAQ,IAAI,oBAAoB,CAAC,CAAC,CAAC;oBACnC,QAAQ,IAAI,oBAAoB,CAAC,CAAC,CAAC;oBACnC,QAAQ,IAAI,oBAAoB,CAAC,CAAC,CAAC;oBAEnC,MAAM,EAAE,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,sBAAsB,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;oBACjI,MAAM,EAAE,GAAG,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,sBAAsB,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;oBACrI,MAAM,EAAE,GAAG,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,sBAAsB,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;oBAErI,IAAI,IAAI,CAAC,mBAAmB,EAAE;wBAC1B,OAAO,CAAC,yBAAyB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;wBAC9C,OAAO,CAAC,yBAAyB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;qBACjD;oBAED,2HAA2H;oBAC3H,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;wBAC9B,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;wBACnC,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;wBACvC,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;wBAEvC,MAAM,QAAQ,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBACjI,MAAM,QAAQ,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBACjI,MAAM,QAAQ,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBAEjI,SAAS,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC;wBACvF,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC;wBAC3F,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC;qBAC9F;oBAED,IAAI,IAAI,CAAC,qBAAqB,IAAI,QAAQ,CAAC,KAAK,EAAE;wBAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;wBAChC,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;wBAC9B,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;wBAClC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;wBAClC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;qBACrC;oBAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;wBAC9B,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;wBACzB,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;wBACjD,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;qBACxD;iBACJ;aACJ;YACD,4EAA4E;iBACvE;gBACD,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,CAAC,iCAAiC;gBAClE,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;oBAClC,GAAG,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,CAAC;oBACrB,MAAM,GAAG,UAAU,GAAG,EAAE,GAAG,CAAC,CAAC;oBAC7B,KAAK,GAAG,OAAO,GAAG,EAAE,GAAG,CAAC,CAAC;oBAEzB,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACnE,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBAC7D,IAAI,IAAI,CAAC,qBAAqB,IAAI,QAAQ,CAAC,KAAK,EAAE;wBAC9C,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;wBAC7B,QAAQ,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;wBAC3B,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;wBAC/B,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;wBAC/B,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;qBAClC;oBACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;wBAC9B,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;wBACzB,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;wBACzD,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;qBACpE;iBACJ;aACJ;YAED,qEAAqE;YACrE,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,MAAM,KAAK,GAAG,QAAQ,CAAC,eAAe,EAAE,CAAC;gBACzC,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC;gBAC/B,MAAM,OAAO,GAAG,KAAK,CAAC,cAAc,CAAC;gBACrC,MAAM,iBAAiB,GAAG,QAAQ,CAAC,kBAAkB,CAAC;gBACtD,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;oBACpB,wFAAwF;oBACxF,MAAM,wBAAwB,GAAG,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC;oBAEvE,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;oBAC/B,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;oBAC/B,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBACjC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;wBACxB,MAAM,OAAO,GAAG,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;wBAClE,MAAM,OAAO,GAAG,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;wBAClE,MAAM,OAAO,GAAG,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;wBAClE,MAAM,QAAQ,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBACjI,MAAM,QAAQ,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBACjI,MAAM,QAAQ,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBACjI,MAAM,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC;wBACrG,MAAM,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC;wBACrG,MAAM,CAAC,GAAG,gBAAgB,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC;wBACrG,OAAO,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;wBAC3C,OAAO,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;qBAC9C;oBAED,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;iBACzD;gBAED,sFAAsF;gBACtF,MAAM,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzF,MAAM,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEzF,MAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC;gBACrH,MAAM,QAAQ,GAAG,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC9G,MAAM,cAAc,GAAG,aAAa,CAAC,aAAa,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7E,MAAM,cAAc,GAAG,aAAa,CAAC,QAAQ,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxE,OAAO,CAAC,WAAW,CAAC,cAAc,EAAE,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;aAC1E;YAED,0CAA0C;YAC1C,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;YAChB,UAAU,GAAG,MAAM,GAAG,CAAC,CAAC;YACxB,OAAO,GAAG,KAAK,GAAG,CAAC,CAAC;SACvB;QAED,6BAA6B;QAC7B,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxD,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBACxB,EAAE,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;iBAClC;qBAAM;oBACH,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;iBAC3E;aACJ;YACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBACrD,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBACxB,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAC/B;qBAAM;oBACH,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;iBACrE;aACJ;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAC5D,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACzB,GAAG,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;aACtC;iBAAM;gBACH,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,YAAY,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACjF;YACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACnD,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBACxD,2HAA2H;oBAC3H,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;oBAC9E,UAAU,CAAC,cAAc,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;oBACrE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACvC,aAAa,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;qBACnC;iBACJ;gBACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;oBACxB,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;oBACzD,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;wBACxB,EAAE,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;qBACnC;yBAAM;wBACH,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;qBAC7E;iBACJ;aACJ;YACD,IAAI,kBAAkB,EAAE;gBACpB,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;gBACvD,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACnD,MAAM,IAAI,GAAG,oBAAoB,CAAC,MAAM,CAAC;gBACzC,IAAI,GAAG,GAAG,CAAC,CAAC;gBACZ,IAAI,MAAM,GAAG,CAAC,CAAC;gBACf,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,EAAE,MAAM,EAAE,EAAE;oBAC1C,MAAM,cAAc,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC;oBACpD,MAAM,IAAI,GAAG,cAAc,CAAC,aAAa,CAAC;oBAC1C,MAAM,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC;oBAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;wBAC3B,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;wBACnC,GAAG,EAAE,CAAC;wBACN,IAAI,IAAI,CAAC,SAAS,EAAE;4BAChB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;4BAChB,IAAI,CAAC,IAAI,CAAC,EAAE;gCACR,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gCAChD,UAAU,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC;gCACpC,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;gCAC3B,MAAM,EAAE,CAAC;6BACZ;yBACJ;qBACJ;iBACJ;aACJ;YACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,WAAW,GAAG,CAAC,CAAC;gBAEpB,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;oBAC1E,MAAM,QAAQ,GAAG,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;oBAC/H,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;oBAE7E,IAAI,SAAS,EAAE;wBACX,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,IAAI,CAAC,EAAE;4BAC1E,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;4BAC7C,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC;4BACxE,SAAS,CAAC,WAAW,GAAG,OAAO,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;yBAC9C;qBACJ;oBAED,WAAW,IAAI,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;iBACjD;aACJ;YAED,IAAI,kBAAkB,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBACpD,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;aACjC;SACJ;QACD,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;aAC3E;iBAAM;gBACH,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;aAC/D;SACJ;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;QACD,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,oDAAoD;QAC9C,IAAI,CAAC,UAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,QAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,QAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,IAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,OAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,UAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,YAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,UAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,cAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,MAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,SAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,eAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,eAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,UAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,gBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,kBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,QAAS,GAAG,IAAI,CAAC;IAChC,CAAC;IACD;;;;;;OAMG;IACI,cAAc,CAAC,WAAwB;QAC1C,IAAI,WAAW,CAAC,GAAG,EAAE;YACjB,MAAM,OAAO,GAAG,WAAW,CAAC,SAAS,CAAC;YACtC,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC;YAChF,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;YACpC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,EAAE;gBAC5C,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC;aAClC;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,EAAU;QAC7B,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;YACjB,OAAO,CAAC,CAAC;SACZ;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC9B,IAAI,GAAG,KAAK,SAAS,EAAE;YACnB,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;SACzB;QACD,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC;QAC5B,OAAO,CAAC,GAAG,EAAE,EAAE;YACX,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,QAAQ,CAAC,EAAE,IAAI,EAAE,EAAE;gBACnB,OAAO,QAAQ,CAAC;aACnB;YACD,CAAC,EAAE,CAAC;SACP;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,OAAe;QACxC,MAAM,GAAG,GAAoB,EAAE,CAAC;QAChC,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAC9C,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACI,0BAA0B,CAAC,OAAe,EAAE,GAAoB;QACnE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;YACvC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,CAAC,OAAO,IAAI,OAAO,EAAE;gBACtB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACf;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IACD;;;;OAIG;IACI,gBAAgB;QACnB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC3C,OAAO,IAAI,CAAC;SACf;QACD,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACvD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC/B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;oBACrB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;iBAC1B;gBACD,MAAM,UAAU,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBAC3C,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;gBAC9C,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;gBAC3B,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;gBACtD,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;aAC7B;SACJ;QACD,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAClD,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC9C,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,MAAM,KAAK,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,KAAK,GAAG,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAC/C,MAAM,QAAQ,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SACxD;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IACD;;;;;;;OAOG;IACO,wBAAwB;QAC9B,MAAM,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACvD,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACtD,MAAM,MAAM,GAAG,oBAAoB,CAAC,MAAM,CAAC;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE9B,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,YAAY,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;QACzD,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACnC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC,CAAC;YAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;SAClD;QACD,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,MAAM,EAAE,EAAE;YAC5C,MAAM,UAAU,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAChD,MAAM,IAAI,GAAG,UAAU,CAAC,aAAa,CAAC;YACtC,MAAM,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC;YAC5B,IAAI,UAAU,CAAC,aAAa,KAAK,YAAY,EAAE;gBAC3C,YAAY,GAAG,UAAU,CAAC,aAAa,CAAC;gBACxC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC5B,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACnC,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,YAAY,EAAE,CAAC;oBACf,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;oBACxC,aAAa,GAAG,CAAC,CAAC;iBACrB;aACJ;YACD,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC3B,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;gBACnC,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAChB,IAAI,CAAC,IAAI,CAAC,EAAE;wBACR,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,aAAa,CAAC,CAAC;wBACrE,IAAI,UAAU,EAAE;4BACZ,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC;4BAChC,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;yBAC9B;6BAAM;4BACH,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;yBAC/F;wBACD,aAAa,EAAE,CAAC;wBAChB,MAAM,EAAE,CAAC;qBACZ;iBACJ;gBACD,GAAG,EAAE,CAAC;aACT;SACJ;QAED,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,oFAAoF;QAC9H,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;SACtC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IACD;;;OAGG;IACO,uBAAuB;QAC7B,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YACvC,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;SACrC;IACL,CAAC;IACD;;;;OAIG;IACO,uBAAuB,CAAC,KAAiB;QAC/C,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,KAAK,EAAE,KAAK,EAAE,IAAI;YACtD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;QACzC,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC;IACpB,CAAC;IACD;;;OAGG;IACO,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,GAAG,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SAC5F;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IACD;;;;OAIG;IACI,kBAAkB;QACrB,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;SACnC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,IAAY;QAChC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3F,CAAC;IAED;;;OAGG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,IAAW,eAAe,CAAC,GAAY;QACnC,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,wBAAwB,GAAG,GAAG,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACH,IAAW,qBAAqB,CAAC,GAAY;QACzC,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC;QAElC,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;QAEjD,YAAY,CAAC,QAAQ,GAAG,GAAG,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACH,IAAW,uBAAuB,CAAC,GAAY;QAC3C,IAAI,CAAC,wBAAwB,GAAG,GAAG,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,oBAAoB,CAAC,GAAY;QACxC,IAAI,CAAC,qBAAqB,GAAG,GAAG,CAAC;IACrC,CAAC;IAED,IAAW,sBAAsB,CAAC,GAAY;QAC1C,IAAI,CAAC,uBAAuB,GAAG,GAAG,CAAC;IACvC,CAAC;IACD;;;;OAIG;IACH,IAAW,qBAAqB,CAAC,GAAY;QACzC,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC;IACtC,CAAC;IACD;;OAEG;IACH,IAAW,kBAAkB,CAAC,GAAY;QACtC,IAAI,CAAC,mBAAmB,GAAG,GAAG,CAAC;IACnC,CAAC;IACD;;;;OAIG;IACH,IAAW,kBAAkB,CAAC,GAAY;QACtC,IAAI,CAAC,mBAAmB,GAAG,GAAG,CAAC;IACnC,CAAC;IAED;;;;OAIG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACH,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;;OAGG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IACD;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IACD;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD;;;;OAIG;IACI,gBAAgB,CAAC,SAAqB;QACzC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;QAC1D,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;SACjC;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,GAAG,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAClF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7D;QACD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC;IAC7C,CAAC;IACD;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IACD,IAAW,aAAa,CAAC,EAAE;QACvB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IAC7B,CAAC;IACD;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IACD,IAAW,mBAAmB,CAAC,GAAY;QACvC,IAAI,CAAC,oBAAoB,GAAG,GAAG,CAAC;IACpC,CAAC;IACD,0EAA0E;IAC1E,0BAA0B;IAC1B,0EAA0E;IAE1E;;;;OAIG;IACI,aAAa,KAAU,CAAC;IAE/B;;;;;;OAMG;IACI,eAAe,CAAC,QAAuB;QAC1C,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;;;;;OAOG;IACI,cAAc,CAAC,QAAuB;QACzC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;;;;;;;OASG;IACH,6DAA6D;IACtD,oBAAoB,CAAC,QAAuB,EAAE,MAA2B,EAAE,EAAU;QACxF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACH,6DAA6D;IACtD,qBAAqB,CAAC,KAAc,EAAE,IAAa,EAAE,MAAgB,IAAS,CAAC;IACtF;;;;;;;OAOG;IACH,6DAA6D;IACtD,oBAAoB,CAAC,KAAc,EAAE,IAAa,EAAE,MAAgB,IAAS,CAAC;CACxF", "sourcesContent": ["import type { Nullable, IndicesArray, FloatArray } from \"../types\";\r\nimport { Vector3, Matrix, TmpVectors, Quaternion } from \"../Maths/math.vector\";\r\nimport { Color4 } from \"../Maths/math.color\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport { VertexData } from \"../Meshes/mesh.vertexData\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { CreateDisc } from \"../Meshes/Builders/discBuilder\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { Scene, IDisposable } from \"../scene\";\r\nimport { DepthSortedParticle, SolidParticle, ModelShape, SolidParticleVertex } from \"./solidParticle\";\r\nimport type { TargetCamera } from \"../Cameras/targetCamera\";\r\nimport { BoundingInfo } from \"../Culling/boundingInfo\";\r\nimport { Axis } from \"../Maths/math.axis\";\r\nimport { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { Material } from \"../Materials/material\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport { MultiMaterial } from \"../Materials/multiMaterial\";\r\nimport type { PickingInfo } from \"../Collisions/pickingInfo\";\r\nimport type { PBRMaterial } from \"../Materials/PBR/pbrMaterial\";\r\n\r\n/**\r\n * The SPS is a single updatable mesh. The solid particles are simply separate parts or faces of this big mesh.\r\n *As it is just a mesh, the SPS has all the same properties than any other BJS mesh : not more, not less. It can be scaled, rotated, translated, enlighted, textured, moved, etc.\r\n\r\n * The SPS is also a particle system. It provides some methods to manage the particles.\r\n * However it is behavior agnostic. This means it has no emitter, no particle physics, no particle recycler. You have to implement your own behavior.\r\n *\r\n * Full documentation here : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/sps_intro\r\n */\r\nexport class SolidParticleSystem implements IDisposable {\r\n    /**\r\n     *  The SPS array of Solid Particle objects. Just access each particle as with any classic array.\r\n     *  Example : var p = SPS.particles[i];\r\n     */\r\n    public particles: SolidParticle[] = new Array<SolidParticle>();\r\n    /**\r\n     * The SPS total number of particles. Read only. Use SPS.counter instead if you need to set your own value.\r\n     */\r\n    public nbParticles: number = 0;\r\n    /**\r\n     * If the particles must ever face the camera (default false). Useful for planar particles.\r\n     */\r\n    public billboard: boolean = false;\r\n    /**\r\n     * Recompute normals when adding a shape\r\n     */\r\n    public recomputeNormals: boolean = false;\r\n    /**\r\n     * This a counter ofr your own usage. It's not set by any SPS functions.\r\n     */\r\n    public counter: number = 0;\r\n    /**\r\n     * The SPS name. This name is also given to the underlying mesh.\r\n     */\r\n    public name: string;\r\n    /**\r\n     * The SPS mesh. It's a standard BJS Mesh, so all the methods from the Mesh class are available.\r\n     */\r\n    public mesh: Mesh;\r\n    /**\r\n     * This empty object is intended to store some SPS specific or temporary values in order to lower the Garbage Collector activity.\r\n     * Please read : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/optimize_sps#limit-garbage-collection\r\n     */\r\n    public vars: any = {};\r\n    /**\r\n     * This array is populated when the SPS is set as 'pickable'.\r\n     * Each key of this array is a `faceId` value that you can get from a pickResult object.\r\n     * Each element of this array is an object `{idx: int, faceId: int}`.\r\n     * `idx` is the picked particle index in the `SPS.particles` array\r\n     * `faceId` is the picked face index counted within this particle.\r\n     * This array is the first element of the pickedBySubMesh array : sps.pickBySubMesh[0].\r\n     * It's not pertinent to use it when using a SPS with the support for MultiMaterial enabled.\r\n     * Use the method SPS.pickedParticle(pickingInfo) instead.\r\n     * Please read : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/picking_sps\r\n     */\r\n    public pickedParticles: { idx: number; faceId: number }[];\r\n    /**\r\n     * This array is populated when the SPS is set as 'pickable'\r\n     * Each key of this array is a submesh index.\r\n     * Each element of this array is a second array defined like this :\r\n     * Each key of this second array is a `faceId` value that you can get from a pickResult object.\r\n     * Each element of this second array is an object `{idx: int, faceId: int}`.\r\n     * `idx` is the picked particle index in the `SPS.particles` array\r\n     * `faceId` is the picked face index counted within this particle.\r\n     * It's better to use the method SPS.pickedParticle(pickingInfo) rather than using directly this array.\r\n     * Please read : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/picking_sps\r\n     */\r\n    public pickedBySubMesh: { idx: number; faceId: number }[][];\r\n    /**\r\n     * This array is populated when `enableDepthSort` is set to true.\r\n     * Each element of this array is an instance of the class DepthSortedParticle.\r\n     */\r\n    public depthSortedParticles: DepthSortedParticle[];\r\n\r\n    /**\r\n     * If the particle intersection must be computed only with the bounding sphere (no bounding box computation, so faster). (Internal use only)\r\n     * @internal\r\n     */\r\n    public _bSphereOnly: boolean = false;\r\n    /**\r\n     * A number to multiply the bounding sphere radius by in order to reduce it for instance. (Internal use only)\r\n     * @internal\r\n     */\r\n    public _bSphereRadiusFactor: number = 1.0;\r\n\r\n    protected _scene: Scene;\r\n    protected _positions: number[] = new Array<number>();\r\n    protected _indices: number[] = new Array<number>();\r\n    protected _normals: number[] = new Array<number>();\r\n    protected _colors: number[] = new Array<number>();\r\n    protected _uvs: number[] = new Array<number>();\r\n    protected _indices32: IndicesArray; // used as depth sorted array if depth sort enabled, else used as typed indices\r\n    protected _positions32: Float32Array; // updated positions for the VBO\r\n    protected _normals32: Float32Array; // updated normals for the VBO\r\n    protected _fixedNormal32: Float32Array; // initial normal references\r\n    protected _colors32: Float32Array;\r\n    protected _uvs32: Float32Array;\r\n    protected _index: number = 0; // indices index\r\n    protected _updatable: boolean = true;\r\n    protected _pickable: boolean = false;\r\n    protected _isVisibilityBoxLocked = false;\r\n    protected _alwaysVisible: boolean = false;\r\n    protected _depthSort: boolean = false;\r\n    protected _expandable: boolean = false;\r\n    protected _shapeCounter: number = 0;\r\n    protected _copy: SolidParticle = new SolidParticle(0, 0, 0, 0, null, 0, 0, this);\r\n    protected _color: Color4 = new Color4(0, 0, 0, 0);\r\n    protected _computeParticleColor: boolean = true;\r\n    protected _computeParticleTexture: boolean = true;\r\n    protected _computeParticleRotation: boolean = true;\r\n    protected _computeParticleVertex: boolean = false;\r\n    protected _computeBoundingBox: boolean = false;\r\n    protected _autoFixFaceOrientation: boolean = false;\r\n    protected _depthSortParticles: boolean = true;\r\n    protected _camera: TargetCamera;\r\n    protected _mustUnrotateFixedNormals = false;\r\n    protected _particlesIntersect: boolean = false;\r\n    protected _needs32Bits: boolean = false;\r\n    protected _isNotBuilt: boolean = true;\r\n    protected _lastParticleId: number = 0;\r\n    protected _idxOfId: number[] = []; // array : key = particle.id / value = particle.idx\r\n    protected _multimaterialEnabled: boolean = false;\r\n    protected _useModelMaterial: boolean = false;\r\n    protected _indicesByMaterial: number[];\r\n    protected _materialIndexes: number[];\r\n    protected _depthSortFunction = (p1: DepthSortedParticle, p2: DepthSortedParticle) => p2.sqDistance - p1.sqDistance;\r\n    protected _materialSortFunction = (p1: DepthSortedParticle, p2: DepthSortedParticle) => p1.materialIndex - p2.materialIndex;\r\n    protected _materials: Material[];\r\n    protected _multimaterial: MultiMaterial;\r\n    protected _materialIndexesById: any;\r\n    protected _defaultMaterial: Material;\r\n    protected _autoUpdateSubMeshes: boolean = false;\r\n    protected _tmpVertex: SolidParticleVertex;\r\n    protected _recomputeInvisibles: boolean = false;\r\n\r\n    /**\r\n     * Creates a SPS (Solid Particle System) object.\r\n     * @param name (String) is the SPS name, this will be the underlying mesh name.\r\n     * @param scene (Scene) is the scene in which the SPS is added.\r\n     * @param options defines the options of the sps e.g.\r\n     * * updatable (optional boolean, default true) : if the SPS must be updatable or immutable.\r\n     * * isPickable (optional boolean, default false) : if the solid particles must be pickable.\r\n     * * enableDepthSort (optional boolean, default false) : if the solid particles must be sorted in the geometry according to their distance to the camera.\r\n     * * useModelMaterial (optional boolean, default false) : if the model materials must be used to create the SPS multimaterial. This enables the multimaterial supports of the SPS.\r\n     * * enableMultiMaterial (optional boolean, default false) : if the solid particles can be given different materials.\r\n     * * expandable (optional boolean, default false) : if particles can still be added after the initial SPS mesh creation.\r\n     * * particleIntersection (optional boolean, default false) : if the solid particle intersections must be computed.\r\n     * * boundingSphereOnly (optional boolean, default false) : if the particle intersection must be computed only with the bounding sphere (no bounding box computation, so faster).\r\n     * * bSphereRadiusFactor (optional float, default 1.0) : a number to multiply the bounding sphere radius by in order to reduce it for instance.\r\n     * * computeBoundingBox (optional boolean, default false): if the bounding box of the entire SPS will be computed (for occlusion detection, for example). If it is false, the bounding box will be the bounding box of the first particle.\r\n     * * autoFixFaceOrientation (optional boolean, default false): if the particle face orientations will be flipped for transformations that change orientation (scale (-1, 1, 1), for example)\r\n     * @param options.updatable\r\n     * @param options.isPickable\r\n     * @param options.enableDepthSort\r\n     * @param options.particleIntersection\r\n     * @param options.boundingSphereOnly\r\n     * @param options.bSphereRadiusFactor\r\n     * @param options.expandable\r\n     * @param options.useModelMaterial\r\n     * @param options.enableMultiMaterial\r\n     * @param options.computeBoundingBox\r\n     * @param options.autoFixFaceOrientation\r\n     * @example bSphereRadiusFactor = 1.0 / Math.sqrt(3.0) => the bounding sphere exactly matches a spherical mesh.\r\n     */\r\n    constructor(\r\n        name: string,\r\n        scene: Scene,\r\n        options?: {\r\n            updatable?: boolean;\r\n            isPickable?: boolean;\r\n            enableDepthSort?: boolean;\r\n            particleIntersection?: boolean;\r\n            boundingSphereOnly?: boolean;\r\n            bSphereRadiusFactor?: number;\r\n            expandable?: boolean;\r\n            useModelMaterial?: boolean;\r\n            enableMultiMaterial?: boolean;\r\n            computeBoundingBox?: boolean;\r\n            autoFixFaceOrientation?: boolean;\r\n        }\r\n    ) {\r\n        this.name = name;\r\n        this._scene = scene || EngineStore.LastCreatedScene;\r\n        this._camera = <TargetCamera>scene.activeCamera;\r\n        this._pickable = options ? <boolean>options.isPickable : false;\r\n        this._depthSort = options ? <boolean>options.enableDepthSort : false;\r\n        this._multimaterialEnabled = options ? <boolean>options.enableMultiMaterial : false;\r\n        this._useModelMaterial = options ? <boolean>options.useModelMaterial : false;\r\n        this._multimaterialEnabled = this._useModelMaterial ? true : this._multimaterialEnabled;\r\n        this._expandable = options ? <boolean>options.expandable : false;\r\n        this._particlesIntersect = options ? <boolean>options.particleIntersection : false;\r\n        this._bSphereOnly = options ? <boolean>options.boundingSphereOnly : false;\r\n        this._bSphereRadiusFactor = options && options.bSphereRadiusFactor ? options.bSphereRadiusFactor : 1.0;\r\n        this._computeBoundingBox = options?.computeBoundingBox ? options.computeBoundingBox : false;\r\n        this._autoFixFaceOrientation = options?.autoFixFaceOrientation ? options.autoFixFaceOrientation : false;\r\n        if (options && options.updatable !== undefined) {\r\n            this._updatable = options.updatable;\r\n        } else {\r\n            this._updatable = true;\r\n        }\r\n        if (this._pickable) {\r\n            this.pickedBySubMesh = [[]];\r\n            this.pickedParticles = this.pickedBySubMesh[0];\r\n        }\r\n        if (this._depthSort || this._multimaterialEnabled) {\r\n            this.depthSortedParticles = [];\r\n        }\r\n        if (this._multimaterialEnabled) {\r\n            this._multimaterial = new MultiMaterial(this.name + \"MultiMaterial\", this._scene);\r\n            this._materials = [];\r\n            this._materialIndexesById = {};\r\n        }\r\n        this._tmpVertex = new SolidParticleVertex();\r\n    }\r\n\r\n    /**\r\n     * Builds the SPS underlying mesh. Returns a standard Mesh.\r\n     * If no model shape was added to the SPS, the returned mesh is just a single triangular plane.\r\n     * @returns the created mesh\r\n     */\r\n    public buildMesh(): Mesh {\r\n        if (!this._isNotBuilt && this.mesh) {\r\n            return this.mesh;\r\n        }\r\n        if (this.nbParticles === 0 && !this.mesh) {\r\n            const triangle = CreateDisc(\"\", { radius: 1, tessellation: 3 }, this._scene);\r\n            this.addShape(triangle, 1);\r\n            triangle.dispose();\r\n        }\r\n        this._indices32 = this._needs32Bits ? new Uint32Array(this._indices) : new Uint16Array(this._indices);\r\n        this._positions32 = new Float32Array(this._positions);\r\n        this._uvs32 = new Float32Array(this._uvs);\r\n        this._colors32 = new Float32Array(this._colors);\r\n\r\n        if (!this.mesh) {\r\n            // in case it's already expanded\r\n            const mesh = new Mesh(this.name, this._scene);\r\n            this.mesh = mesh;\r\n        }\r\n        if (!this._updatable && this._multimaterialEnabled) {\r\n            this._sortParticlesByMaterial(); // this may reorder the indices32\r\n        }\r\n        if (this.recomputeNormals) {\r\n            VertexData.ComputeNormals(this._positions32, this._indices32, this._normals);\r\n        }\r\n\r\n        this._normals32 = new Float32Array(this._normals);\r\n        this._fixedNormal32 = new Float32Array(this._normals);\r\n        if (this._mustUnrotateFixedNormals) {\r\n            // the particles could be created already rotated in the mesh with a positionFunction\r\n            this._unrotateFixedNormals();\r\n        }\r\n        const vertexData = new VertexData();\r\n        vertexData.indices = this._depthSort ? this._indices : this._indices32;\r\n        vertexData.set(this._positions32, VertexBuffer.PositionKind);\r\n        vertexData.set(this._normals32, VertexBuffer.NormalKind);\r\n\r\n        if (this._uvs32.length > 0) {\r\n            vertexData.set(this._uvs32, VertexBuffer.UVKind);\r\n        }\r\n        if (this._colors32.length > 0) {\r\n            vertexData.set(this._colors32, VertexBuffer.ColorKind);\r\n        }\r\n\r\n        vertexData.applyToMesh(this.mesh, this._updatable);\r\n        this.mesh.isPickable = this._pickable;\r\n\r\n        if (this._pickable) {\r\n            let faceId = 0;\r\n            for (let p = 0; p < this.nbParticles; p++) {\r\n                const part = this.particles[p];\r\n                const lind = part._model._indicesLength;\r\n                for (let i = 0; i < lind; i++) {\r\n                    const f = i % 3;\r\n                    if (f == 0) {\r\n                        const pickedData = { idx: part.idx, faceId: faceId };\r\n                        this.pickedParticles[faceId] = pickedData;\r\n                        faceId++;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._multimaterialEnabled) {\r\n            this.setMultiMaterial(this._materials);\r\n        }\r\n\r\n        if (!this._expandable) {\r\n            // free memory\r\n            if (!this._depthSort && !this._multimaterialEnabled && !this._autoFixFaceOrientation) {\r\n                (<any>this._indices) = null;\r\n            }\r\n            (<any>this._positions) = null;\r\n            (<any>this._normals) = null;\r\n            (<any>this._uvs) = null;\r\n            (<any>this._colors) = null;\r\n\r\n            if (!this._updatable) {\r\n                this.particles.length = 0;\r\n            }\r\n        }\r\n        this._isNotBuilt = false;\r\n        this.recomputeNormals = false;\r\n        this._recomputeInvisibles = true;\r\n        return this.mesh;\r\n    }\r\n\r\n    private _getUVKind(mesh: Mesh, uvKind: number) {\r\n        if (uvKind === -1) {\r\n            if ((mesh.material as StandardMaterial)?.diffuseTexture) {\r\n                uvKind = (mesh.material as StandardMaterial).diffuseTexture!.coordinatesIndex;\r\n            } else if ((mesh.material as PBRMaterial)?.albedoTexture) {\r\n                uvKind = (mesh.material as PBRMaterial).albedoTexture!.coordinatesIndex;\r\n            }\r\n        }\r\n\r\n        return \"uv\" + (uvKind ? uvKind + 1 : \"\");\r\n    }\r\n\r\n    /**\r\n     * Digests the mesh and generates as many solid particles in the system as wanted. Returns the SPS.\r\n     * These particles will have the same geometry than the mesh parts and will be positioned at the same localisation than the mesh original places.\r\n     * Thus the particles generated from `digest()` have their property `position` set yet.\r\n     * @param mesh ( Mesh ) is the mesh to be digested\r\n     * @param options {facetNb} (optional integer, default 1) is the number of mesh facets per particle, this parameter is overridden by the parameter `number` if any\r\n     * {delta} (optional integer, default 0) is the random extra number of facets per particle , each particle will have between `facetNb` and `facetNb + delta` facets\r\n     * {number} (optional positive integer) is the wanted number of particles : each particle is built with `mesh_total_facets / number` facets\r\n     * {storage} (optional existing array) is an array where the particles will be stored for a further use instead of being inserted in the SPS.\r\n     * {uvKind} (optional positive integer, default 0) is the kind of UV to read from. Use -1 to deduce it from the diffuse/albedo texture (if any) of the mesh material\r\n     * @param options.facetNb\r\n     * @param options.number\r\n     * @param options.delta\r\n     * @param options.storage\r\n     * @param options.uvKind\r\n     * @returns the current SPS\r\n     */\r\n    public digest(mesh: Mesh, options?: { facetNb?: number; number?: number; delta?: number; storage?: []; uvKind?: number }): SolidParticleSystem {\r\n        let size: number = (options && options.facetNb) || 1;\r\n        let number: number = (options && options.number) || 0;\r\n        let delta: number = (options && options.delta) || 0;\r\n        const meshPos = <FloatArray>mesh.getVerticesData(VertexBuffer.PositionKind);\r\n        const meshInd = <IndicesArray>mesh.getIndices();\r\n        const meshUV = <FloatArray>mesh.getVerticesData(this._getUVKind(mesh, options?.uvKind ?? 0));\r\n        const meshCol = <FloatArray>mesh.getVerticesData(VertexBuffer.ColorKind);\r\n        const meshNor = <FloatArray>mesh.getVerticesData(VertexBuffer.NormalKind);\r\n        const storage = options && options.storage ? options.storage : null;\r\n\r\n        let f: number = 0; // facet counter\r\n        const totalFacets: number = meshInd.length / 3; // a facet is a triangle, so 3 indices\r\n        // compute size from number\r\n        if (number) {\r\n            number = number > totalFacets ? totalFacets : number;\r\n            size = Math.round(totalFacets / number);\r\n            delta = 0;\r\n        } else {\r\n            size = size > totalFacets ? totalFacets : size;\r\n        }\r\n\r\n        const facetPos: number[] = []; // submesh positions\r\n        const facetNor: number[] = [];\r\n        const facetInd: number[] = []; // submesh indices\r\n        const facetUV: number[] = []; // submesh UV\r\n        const facetCol: number[] = []; // submesh colors\r\n        const barycenter: Vector3 = Vector3.Zero();\r\n        const sizeO: number = size;\r\n\r\n        while (f < totalFacets) {\r\n            size = sizeO + Math.floor((1 + delta) * Math.random());\r\n            if (f > totalFacets - size) {\r\n                size = totalFacets - f;\r\n            }\r\n            // reset temp arrays\r\n            facetPos.length = 0;\r\n            facetNor.length = 0;\r\n            facetInd.length = 0;\r\n            facetUV.length = 0;\r\n            facetCol.length = 0;\r\n\r\n            // iterate over \"size\" facets\r\n            let fi: number = 0;\r\n            for (let j = f * 3; j < (f + size) * 3; j++) {\r\n                facetInd.push(fi);\r\n                const i: number = meshInd[j];\r\n                const i3: number = i * 3;\r\n                facetPos.push(meshPos[i3], meshPos[i3 + 1], meshPos[i3 + 2]);\r\n                facetNor.push(meshNor[i3], meshNor[i3 + 1], meshNor[i3 + 2]);\r\n                if (meshUV) {\r\n                    const i2: number = i * 2;\r\n                    facetUV.push(meshUV[i2], meshUV[i2 + 1]);\r\n                }\r\n                if (meshCol) {\r\n                    const i4: number = i * 4;\r\n                    facetCol.push(meshCol[i4], meshCol[i4 + 1], meshCol[i4 + 2], meshCol[i4 + 3]);\r\n                }\r\n                fi++;\r\n            }\r\n\r\n            // create a model shape for each single particle\r\n            let idx: number = this.nbParticles;\r\n            const shape: Vector3[] = this._posToShape(facetPos);\r\n            const shapeUV: number[] = this._uvsToShapeUV(facetUV);\r\n            const shapeInd = facetInd.slice();\r\n            const shapeCol = facetCol.slice();\r\n            const shapeNor = facetNor.slice();\r\n\r\n            // compute the barycenter of the shape\r\n            barycenter.copyFromFloats(0, 0, 0);\r\n            let v: number;\r\n            for (v = 0; v < shape.length; v++) {\r\n                barycenter.addInPlace(shape[v]);\r\n            }\r\n            barycenter.scaleInPlace(1 / shape.length);\r\n\r\n            // shift the shape from its barycenter to the origin\r\n            // and compute the BBox required for intersection.\r\n            const minimum: Vector3 = new Vector3(Infinity, Infinity, Infinity);\r\n            const maximum: Vector3 = new Vector3(-Infinity, -Infinity, -Infinity);\r\n            for (v = 0; v < shape.length; v++) {\r\n                shape[v].subtractInPlace(barycenter);\r\n                minimum.minimizeInPlaceFromFloats(shape[v].x, shape[v].y, shape[v].z);\r\n                maximum.maximizeInPlaceFromFloats(shape[v].x, shape[v].y, shape[v].z);\r\n            }\r\n            let bInfo;\r\n            if (this._particlesIntersect) {\r\n                bInfo = new BoundingInfo(minimum, maximum);\r\n            }\r\n            let material = null;\r\n            if (this._useModelMaterial) {\r\n                material = mesh.material ? mesh.material : this._setDefaultMaterial();\r\n            }\r\n            const modelShape = new ModelShape(this._shapeCounter, shape, shapeInd, shapeNor, shapeCol, shapeUV, null, null, material);\r\n\r\n            // add the particle in the SPS\r\n            const currentPos = this._positions.length;\r\n            const currentInd = this._indices.length;\r\n            this._meshBuilder(\r\n                this._index,\r\n                currentInd,\r\n                shape,\r\n                this._positions,\r\n                shapeInd,\r\n                this._indices,\r\n                facetUV,\r\n                this._uvs,\r\n                shapeCol,\r\n                this._colors,\r\n                shapeNor,\r\n                this._normals,\r\n                idx,\r\n                0,\r\n                null,\r\n                modelShape\r\n            );\r\n            this._addParticle(idx, this._lastParticleId, currentPos, currentInd, modelShape, this._shapeCounter, 0, bInfo, storage);\r\n            // initialize the particle position\r\n            this.particles[this.nbParticles].position.addInPlace(barycenter);\r\n\r\n            if (!storage) {\r\n                this._index += shape.length;\r\n                idx++;\r\n                this.nbParticles++;\r\n                this._lastParticleId++;\r\n            }\r\n            this._shapeCounter++;\r\n            f += size;\r\n        }\r\n        this._isNotBuilt = true; // buildMesh() is now expected for setParticles() to work\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Unrotate the fixed normals in case the mesh was built with pre-rotated particles, ex : use of positionFunction in addShape()\r\n     * @internal\r\n     */\r\n    protected _unrotateFixedNormals() {\r\n        let index = 0;\r\n        let idx = 0;\r\n        const tmpNormal = TmpVectors.Vector3[0];\r\n        const quaternion = TmpVectors.Quaternion[0];\r\n        const invertedRotMatrix = TmpVectors.Matrix[0];\r\n        for (let p = 0; p < this.particles.length; p++) {\r\n            const particle = this.particles[p];\r\n            const shape = particle._model._shape;\r\n\r\n            // computing the inverse of the rotation matrix from the quaternion\r\n            // is equivalent to computing the matrix of the inverse quaternion, i.e of the conjugate quaternion\r\n            if (particle.rotationQuaternion) {\r\n                particle.rotationQuaternion.conjugateToRef(quaternion);\r\n            } else {\r\n                const rotation = particle.rotation;\r\n                Quaternion.RotationYawPitchRollToRef(rotation.y, rotation.x, rotation.z, quaternion);\r\n                quaternion.conjugateInPlace();\r\n            }\r\n            quaternion.toRotationMatrix(invertedRotMatrix);\r\n\r\n            for (let pt = 0; pt < shape.length; pt++) {\r\n                idx = index + pt * 3;\r\n                Vector3.TransformNormalFromFloatsToRef(this._normals32[idx], this._normals32[idx + 1], this._normals32[idx + 2], invertedRotMatrix, tmpNormal);\r\n                tmpNormal.toArray(this._fixedNormal32, idx);\r\n            }\r\n            index = idx + 3;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Resets the temporary working copy particle\r\n     * @internal\r\n     */\r\n    protected _resetCopy() {\r\n        const copy = this._copy;\r\n        copy.position.setAll(0);\r\n        copy.rotation.setAll(0);\r\n        copy.rotationQuaternion = null;\r\n        copy.scaling.setAll(1);\r\n        copy.uvs.copyFromFloats(0.0, 0.0, 1.0, 1.0);\r\n        copy.color = null;\r\n        copy.translateFromPivot = false;\r\n        copy.shapeId = 0;\r\n        copy.materialIndex = null;\r\n    }\r\n\r\n    /**\r\n     * Inserts the shape model geometry in the global SPS mesh by updating the positions, indices, normals, colors, uvs arrays\r\n     * @param p the current index in the positions array to be updated\r\n     * @param ind the current index in the indices array\r\n     * @param shape a Vector3 array, the shape geometry\r\n     * @param positions the positions array to be updated\r\n     * @param meshInd the shape indices array\r\n     * @param indices the indices array to be updated\r\n     * @param meshUV the shape uv array\r\n     * @param uvs the uv array to be updated\r\n     * @param meshCol the shape color array\r\n     * @param colors the color array to be updated\r\n     * @param meshNor the shape normals array\r\n     * @param normals the normals array to be updated\r\n     * @param idx the particle index\r\n     * @param idxInShape the particle index in its shape\r\n     * @param options the addShape() method  passed options\r\n     * @param model\r\n     * @model the particle model\r\n     * @internal\r\n     */\r\n    protected _meshBuilder(\r\n        p: number,\r\n        ind: number,\r\n        shape: Vector3[],\r\n        positions: number[],\r\n        meshInd: IndicesArray,\r\n        indices: number[],\r\n        meshUV: number[] | Float32Array,\r\n        uvs: number[],\r\n        meshCol: number[] | Float32Array,\r\n        colors: number[],\r\n        meshNor: number[] | Float32Array,\r\n        normals: number[],\r\n        idx: number,\r\n        idxInShape: number,\r\n        options: any,\r\n        model: ModelShape\r\n    ): SolidParticle {\r\n        let i;\r\n        let u = 0;\r\n        let c = 0;\r\n        let n = 0;\r\n\r\n        this._resetCopy();\r\n        const copy = this._copy;\r\n        const storeApart = options && options.storage ? true : false;\r\n        copy.idx = idx;\r\n        copy.idxInShape = idxInShape;\r\n        copy.shapeId = model.shapeId;\r\n        if (this._useModelMaterial) {\r\n            const materialId = model._material!.uniqueId;\r\n            const materialIndexesById = this._materialIndexesById;\r\n            if (!Object.prototype.hasOwnProperty.call(materialIndexesById, materialId)) {\r\n                materialIndexesById[materialId] = this._materials.length;\r\n                this._materials.push(model._material!);\r\n            }\r\n            const matIdx = materialIndexesById[materialId];\r\n            copy.materialIndex = matIdx;\r\n        }\r\n\r\n        if (options && options.positionFunction) {\r\n            // call to custom positionFunction\r\n            options.positionFunction(copy, idx, idxInShape);\r\n            this._mustUnrotateFixedNormals = true;\r\n        }\r\n\r\n        // in case the particle geometry must NOT be inserted in the SPS mesh geometry\r\n        if (storeApart) {\r\n            return copy;\r\n        }\r\n\r\n        const rotMatrix = TmpVectors.Matrix[0];\r\n        const tmpVertex = this._tmpVertex;\r\n        const tmpVector = tmpVertex.position;\r\n        const tmpColor = tmpVertex.color;\r\n        const tmpUV = tmpVertex.uv;\r\n        const tmpRotated = TmpVectors.Vector3[1];\r\n        const pivotBackTranslation = TmpVectors.Vector3[2];\r\n        const scaledPivot = TmpVectors.Vector3[3];\r\n        Matrix.IdentityToRef(rotMatrix);\r\n        copy.getRotationMatrix(rotMatrix);\r\n\r\n        copy.pivot.multiplyToRef(copy.scaling, scaledPivot);\r\n\r\n        if (copy.translateFromPivot) {\r\n            pivotBackTranslation.setAll(0.0);\r\n        } else {\r\n            pivotBackTranslation.copyFrom(scaledPivot);\r\n        }\r\n\r\n        const someVertexFunction = options && options.vertexFunction;\r\n        for (i = 0; i < shape.length; i++) {\r\n            tmpVector.copyFrom(shape[i]);\r\n            if (copy.color) {\r\n                tmpColor.copyFrom(copy.color);\r\n            }\r\n            if (meshUV) {\r\n                tmpUV.copyFromFloats(meshUV[u], meshUV[u + 1]);\r\n            }\r\n            if (someVertexFunction) {\r\n                options.vertexFunction(copy, tmpVertex, i);\r\n            }\r\n\r\n            tmpVector.multiplyInPlace(copy.scaling).subtractInPlace(scaledPivot);\r\n            Vector3.TransformCoordinatesToRef(tmpVector, rotMatrix, tmpRotated);\r\n            tmpRotated.addInPlace(pivotBackTranslation).addInPlace(copy.position);\r\n            positions.push(tmpRotated.x, tmpRotated.y, tmpRotated.z);\r\n\r\n            if (meshUV) {\r\n                const copyUvs = copy.uvs;\r\n                uvs.push((copyUvs.z - copyUvs.x) * tmpUV.x + copyUvs.x, (copyUvs.w - copyUvs.y) * tmpUV.y + copyUvs.y);\r\n                u += 2;\r\n            }\r\n\r\n            if (copy.color) {\r\n                this._color.copyFrom(tmpColor);\r\n            } else {\r\n                const color = this._color;\r\n                if (meshCol && meshCol[c] !== undefined) {\r\n                    color.r = meshCol[c];\r\n                    color.g = meshCol[c + 1];\r\n                    color.b = meshCol[c + 2];\r\n                    color.a = meshCol[c + 3];\r\n                } else {\r\n                    color.r = 1.0;\r\n                    color.g = 1.0;\r\n                    color.b = 1.0;\r\n                    color.a = 1.0;\r\n                }\r\n            }\r\n            colors.push(this._color.r, this._color.g, this._color.b, this._color.a);\r\n            c += 4;\r\n\r\n            if (!this.recomputeNormals && meshNor) {\r\n                Vector3.TransformNormalFromFloatsToRef(meshNor[n], meshNor[n + 1], meshNor[n + 2], rotMatrix, tmpVector);\r\n                normals.push(tmpVector.x, tmpVector.y, tmpVector.z);\r\n                n += 3;\r\n            }\r\n        }\r\n\r\n        for (i = 0; i < meshInd.length; i++) {\r\n            const current_ind = p + meshInd[i];\r\n            indices.push(current_ind);\r\n            if (current_ind > 65535) {\r\n                this._needs32Bits = true;\r\n            }\r\n        }\r\n\r\n        if (this._depthSort || this._multimaterialEnabled) {\r\n            const matIndex = copy.materialIndex !== null ? copy.materialIndex : 0;\r\n            this.depthSortedParticles.push(new DepthSortedParticle(idx, ind, meshInd.length, matIndex));\r\n        }\r\n\r\n        return copy;\r\n    }\r\n\r\n    /**\r\n     * Returns a shape Vector3 array from positions float array\r\n     * @param positions float array\r\n     * @returns a vector3 array\r\n     * @internal\r\n     */\r\n    protected _posToShape(positions: number[] | Float32Array): Vector3[] {\r\n        const shape = [];\r\n        for (let i = 0; i < positions.length; i += 3) {\r\n            shape.push(Vector3.FromArray(positions, i));\r\n        }\r\n        return shape;\r\n    }\r\n\r\n    /**\r\n     * Returns a shapeUV array from a float uvs (array deep copy)\r\n     * @param uvs as a float array\r\n     * @returns a shapeUV array\r\n     * @internal\r\n     */\r\n    protected _uvsToShapeUV(uvs: number[] | Float32Array): number[] {\r\n        const shapeUV = [];\r\n        if (uvs) {\r\n            for (let i = 0; i < uvs.length; i++) {\r\n                shapeUV.push(uvs[i]);\r\n            }\r\n        }\r\n        return shapeUV;\r\n    }\r\n\r\n    /**\r\n     * Adds a new particle object in the particles array\r\n     * @param idx particle index in particles array\r\n     * @param id particle id\r\n     * @param idxpos positionIndex : the starting index of the particle vertices in the SPS \"positions\" array\r\n     * @param idxind indiceIndex : he starting index of the particle indices in the SPS \"indices\" array\r\n     * @param model particle ModelShape object\r\n     * @param shapeId model shape identifier\r\n     * @param idxInShape index of the particle in the current model\r\n     * @param bInfo model bounding info object\r\n     * @param storage target storage array, if any\r\n     * @internal\r\n     */\r\n    protected _addParticle(\r\n        idx: number,\r\n        id: number,\r\n        idxpos: number,\r\n        idxind: number,\r\n        model: ModelShape,\r\n        shapeId: number,\r\n        idxInShape: number,\r\n        bInfo: Nullable<BoundingInfo> = null,\r\n        storage: Nullable<[]> = null\r\n    ): SolidParticle {\r\n        const sp = new SolidParticle(idx, id, idxpos, idxind, model, shapeId, idxInShape, this, bInfo);\r\n        const target = storage ? storage : this.particles;\r\n        target.push(sp);\r\n        return sp;\r\n    }\r\n\r\n    /**\r\n     * Adds some particles to the SPS from the model shape. Returns the shape id.\r\n     * Please read the doc : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/immutable_sps\r\n     * @param mesh is any Mesh object that will be used as a model for the solid particles. If the mesh does not have vertex normals, it will turn on the recomputeNormals attribute.\r\n     * @param nb (positive integer) the number of particles to be created from this model\r\n     * @param options {positionFunction} is an optional javascript function to called for each particle on SPS creation.\r\n     * {vertexFunction} is an optional javascript function to called for each vertex of each particle on SPS creation\r\n     * {storage} (optional existing array) is an array where the particles will be stored for a further use instead of being inserted in the SPS.\r\n     * @param options.positionFunction\r\n     * @param options.vertexFunction\r\n     * @param options.storage\r\n     * @returns the number of shapes in the system\r\n     */\r\n    public addShape(mesh: Mesh, nb: number, options?: { positionFunction?: any; vertexFunction?: any; storage?: [] }): number {\r\n        const meshPos = <FloatArray>mesh.getVerticesData(VertexBuffer.PositionKind);\r\n        const meshInd = <IndicesArray>mesh.getIndices();\r\n        const meshUV = <FloatArray>mesh.getVerticesData(VertexBuffer.UVKind);\r\n        const meshCol = <FloatArray>mesh.getVerticesData(VertexBuffer.ColorKind);\r\n        const meshNor = <FloatArray>mesh.getVerticesData(VertexBuffer.NormalKind);\r\n        this.recomputeNormals = meshNor ? false : true;\r\n        const indices = Array.from(meshInd);\r\n        const shapeNormals = meshNor ? Array.from(meshNor) : [];\r\n        const shapeColors = meshCol ? Array.from(meshCol) : [];\r\n        const storage = options && options.storage ? options.storage : null;\r\n        let bbInfo: Nullable<BoundingInfo> = null;\r\n        if (this._particlesIntersect) {\r\n            bbInfo = mesh.getBoundingInfo();\r\n        }\r\n\r\n        const shape = this._posToShape(meshPos);\r\n        const shapeUV = this._uvsToShapeUV(meshUV);\r\n\r\n        const posfunc = options ? options.positionFunction : null;\r\n        const vtxfunc = options ? options.vertexFunction : null;\r\n        let material = null;\r\n        if (this._useModelMaterial) {\r\n            material = mesh.material ? mesh.material : this._setDefaultMaterial();\r\n        }\r\n        const modelShape = new ModelShape(this._shapeCounter, shape, indices, shapeNormals, shapeColors, shapeUV, posfunc, vtxfunc, material);\r\n\r\n        // particles\r\n        for (let i = 0; i < nb; i++) {\r\n            this._insertNewParticle(this.nbParticles, i, modelShape, shape, meshInd, meshUV, meshCol, meshNor, bbInfo, storage, options);\r\n        }\r\n        this._shapeCounter++;\r\n        this._isNotBuilt = true; // buildMesh() call is now expected for setParticles() to work\r\n        return this._shapeCounter - 1;\r\n    }\r\n\r\n    /**\r\n     * Rebuilds a particle back to its just built status : if needed, recomputes the custom positions and vertices\r\n     * @internal\r\n     */\r\n    protected _rebuildParticle(particle: SolidParticle, reset: boolean = false): void {\r\n        this._resetCopy();\r\n        const copy = this._copy;\r\n        if (particle._model._positionFunction) {\r\n            // recall to stored custom positionFunction\r\n            particle._model._positionFunction(copy, particle.idx, particle.idxInShape);\r\n        }\r\n\r\n        const rotMatrix = TmpVectors.Matrix[0];\r\n        const tmpVertex = TmpVectors.Vector3[0];\r\n        const tmpRotated = TmpVectors.Vector3[1];\r\n        const pivotBackTranslation = TmpVectors.Vector3[2];\r\n        const scaledPivot = TmpVectors.Vector3[3];\r\n\r\n        copy.getRotationMatrix(rotMatrix);\r\n\r\n        particle.pivot.multiplyToRef(particle.scaling, scaledPivot);\r\n\r\n        if (copy.translateFromPivot) {\r\n            pivotBackTranslation.copyFromFloats(0.0, 0.0, 0.0);\r\n        } else {\r\n            pivotBackTranslation.copyFrom(scaledPivot);\r\n        }\r\n\r\n        const shape = particle._model._shape;\r\n\r\n        for (let pt = 0; pt < shape.length; pt++) {\r\n            tmpVertex.copyFrom(shape[pt]);\r\n            if (particle._model._vertexFunction) {\r\n                particle._model._vertexFunction(copy, tmpVertex, pt); // recall to stored vertexFunction\r\n            }\r\n\r\n            tmpVertex.multiplyInPlace(copy.scaling).subtractInPlace(scaledPivot);\r\n            Vector3.TransformCoordinatesToRef(tmpVertex, rotMatrix, tmpRotated);\r\n            tmpRotated\r\n                .addInPlace(pivotBackTranslation)\r\n                .addInPlace(copy.position)\r\n                .toArray(this._positions32, particle._pos + pt * 3);\r\n        }\r\n        if (reset) {\r\n            particle.position.setAll(0.0);\r\n            particle.rotation.setAll(0.0);\r\n            particle.rotationQuaternion = null;\r\n            particle.scaling.setAll(1.0);\r\n            particle.uvs.setAll(0.0);\r\n            particle.pivot.setAll(0.0);\r\n            particle.translateFromPivot = false;\r\n            particle.parentId = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the whole mesh and updates the VBO : custom positions and vertices are recomputed if needed.\r\n     * @param reset boolean, default false : if the particles must be reset at position and rotation zero, scaling 1, color white, initial UVs and not parented.\r\n     * @returns the SPS.\r\n     */\r\n    public rebuildMesh(reset: boolean = false): SolidParticleSystem {\r\n        for (let p = 0; p < this.particles.length; p++) {\r\n            this._rebuildParticle(this.particles[p], reset);\r\n        }\r\n        this.mesh.updateVerticesData(VertexBuffer.PositionKind, this._positions32, false, false);\r\n        return this;\r\n    }\r\n\r\n    /** Removes the particles from the start-th to the end-th included from an expandable SPS (required).\r\n     *  Returns an array with the removed particles.\r\n     *  If the number of particles to remove is lower than zero or greater than the global remaining particle number, then an empty array is returned.\r\n     *  The SPS can't be empty so at least one particle needs to remain in place.\r\n     *  Under the hood, the VertexData array, so the VBO buffer, is recreated each call.\r\n     * @param start index of the first particle to remove\r\n     * @param end index of the last particle to remove (included)\r\n     * @returns an array populated with the removed particles\r\n     */\r\n    public removeParticles(start: number, end: number): SolidParticle[] {\r\n        const nb = end - start + 1;\r\n        if (!this._expandable || nb <= 0 || nb >= this.nbParticles || !this._updatable) {\r\n            return [];\r\n        }\r\n        const particles = this.particles;\r\n        const currentNb = this.nbParticles;\r\n        if (end < currentNb - 1) {\r\n            // update the particle indexes in the positions array in case they're remaining particles after the last removed\r\n            const firstRemaining = end + 1;\r\n            const shiftPos = particles[firstRemaining]._pos - particles[start]._pos;\r\n            const shifInd = particles[firstRemaining]._ind - particles[start]._ind;\r\n            for (let i = firstRemaining; i < currentNb; i++) {\r\n                const part = particles[i];\r\n                part._pos -= shiftPos;\r\n                part._ind -= shifInd;\r\n            }\r\n        }\r\n        const removed = particles.splice(start, nb);\r\n        this._positions.length = 0;\r\n        this._indices.length = 0;\r\n        this._colors.length = 0;\r\n        this._uvs.length = 0;\r\n        this._normals.length = 0;\r\n        this._index = 0;\r\n        this._idxOfId.length = 0;\r\n        if (this._depthSort || this._multimaterialEnabled) {\r\n            this.depthSortedParticles = [];\r\n        }\r\n        let ind = 0;\r\n        const particlesLength = particles.length;\r\n        for (let p = 0; p < particlesLength; p++) {\r\n            const particle = particles[p];\r\n            const model = particle._model;\r\n            const shape = model._shape;\r\n            const modelIndices = model._indices;\r\n            const modelNormals = model._normals;\r\n            const modelColors = model._shapeColors;\r\n            const modelUVs = model._shapeUV;\r\n            particle.idx = p;\r\n            this._idxOfId[particle.id] = p;\r\n            this._meshBuilder(\r\n                this._index,\r\n                ind,\r\n                shape,\r\n                this._positions,\r\n                modelIndices,\r\n                this._indices,\r\n                modelUVs,\r\n                this._uvs,\r\n                modelColors,\r\n                this._colors,\r\n                modelNormals,\r\n                this._normals,\r\n                particle.idx,\r\n                particle.idxInShape,\r\n                null,\r\n                model\r\n            );\r\n            this._index += shape.length;\r\n            ind += modelIndices.length;\r\n        }\r\n        this.nbParticles -= nb;\r\n        this._isNotBuilt = true; // buildMesh() call is now expected for setParticles() to work\r\n        return removed;\r\n    }\r\n\r\n    /**\r\n     * Inserts some pre-created particles in the solid particle system so that they can be managed by setParticles().\r\n     * @param solidParticleArray an array populated with Solid Particles objects\r\n     * @returns the SPS\r\n     */\r\n    public insertParticlesFromArray(solidParticleArray: SolidParticle[]): SolidParticleSystem {\r\n        if (!this._expandable) {\r\n            return this;\r\n        }\r\n        let idxInShape = 0;\r\n        let currentShapeId = solidParticleArray[0].shapeId;\r\n        const nb = solidParticleArray.length;\r\n        for (let i = 0; i < nb; i++) {\r\n            const sp = solidParticleArray[i];\r\n            const model = sp._model;\r\n            const shape = model._shape;\r\n            const meshInd = model._indices;\r\n            const meshUV = model._shapeUV;\r\n            const meshCol = model._shapeColors;\r\n            const meshNor = model._normals;\r\n            const noNor = meshNor ? false : true;\r\n            this.recomputeNormals = noNor || this.recomputeNormals;\r\n            const bbInfo = sp.getBoundingInfo();\r\n            const newPart = this._insertNewParticle(this.nbParticles, idxInShape, model, shape, meshInd, meshUV, meshCol, meshNor, bbInfo, null, null);\r\n            sp.copyToRef(newPart!);\r\n            idxInShape++;\r\n            if (currentShapeId != sp.shapeId) {\r\n                currentShapeId = sp.shapeId;\r\n                idxInShape = 0;\r\n            }\r\n        }\r\n        this._isNotBuilt = true; // buildMesh() call is now expected for setParticles() to work\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Creates a new particle and modifies the SPS mesh geometry :\r\n     * - calls _meshBuilder() to increase the SPS mesh geometry step by step\r\n     * - calls _addParticle() to populate the particle array\r\n     * factorized code from addShape() and insertParticlesFromArray()\r\n     * @param idx particle index in the particles array\r\n     * @param i particle index in its shape\r\n     * @param modelShape particle ModelShape object\r\n     * @param shape shape vertex array\r\n     * @param meshInd shape indices array\r\n     * @param meshUV shape uv array\r\n     * @param meshCol shape color array\r\n     * @param meshNor shape normals array\r\n     * @param bbInfo shape bounding info\r\n     * @param storage target particle storage\r\n     * @param options\r\n     * @options addShape() passed options\r\n     * @internal\r\n     */\r\n    protected _insertNewParticle(\r\n        idx: number,\r\n        i: number,\r\n        modelShape: ModelShape,\r\n        shape: Vector3[],\r\n        meshInd: IndicesArray,\r\n        meshUV: number[] | Float32Array,\r\n        meshCol: number[] | Float32Array,\r\n        meshNor: number[] | Float32Array,\r\n        bbInfo: Nullable<BoundingInfo>,\r\n        storage: Nullable<[]>,\r\n        options: any\r\n    ): Nullable<SolidParticle> {\r\n        const currentPos = this._positions.length;\r\n        const currentInd = this._indices.length;\r\n        const currentCopy = this._meshBuilder(\r\n            this._index,\r\n            currentInd,\r\n            shape,\r\n            this._positions,\r\n            meshInd,\r\n            this._indices,\r\n            meshUV,\r\n            this._uvs,\r\n            meshCol,\r\n            this._colors,\r\n            meshNor,\r\n            this._normals,\r\n            idx,\r\n            i,\r\n            options,\r\n            modelShape\r\n        );\r\n        let sp: Nullable<SolidParticle> = null;\r\n        if (this._updatable) {\r\n            sp = this._addParticle(this.nbParticles, this._lastParticleId, currentPos, currentInd, modelShape, this._shapeCounter, i, bbInfo, storage);\r\n            sp.position.copyFrom(currentCopy.position);\r\n            sp.rotation.copyFrom(currentCopy.rotation);\r\n            if (currentCopy.rotationQuaternion) {\r\n                if (sp.rotationQuaternion) {\r\n                    sp.rotationQuaternion.copyFrom(currentCopy.rotationQuaternion);\r\n                } else {\r\n                    sp.rotationQuaternion = currentCopy.rotationQuaternion.clone();\r\n                }\r\n            }\r\n            if (currentCopy.color) {\r\n                if (sp.color) {\r\n                    sp.color.copyFrom(currentCopy.color);\r\n                } else {\r\n                    sp.color = currentCopy.color.clone();\r\n                }\r\n            }\r\n            sp.scaling.copyFrom(currentCopy.scaling);\r\n            sp.uvs.copyFrom(currentCopy.uvs);\r\n            if (currentCopy.materialIndex !== null) {\r\n                sp.materialIndex = currentCopy.materialIndex;\r\n            }\r\n            if (this.expandable) {\r\n                this._idxOfId[sp.id] = sp.idx;\r\n            }\r\n        }\r\n        if (!storage) {\r\n            this._index += shape.length;\r\n            this.nbParticles++;\r\n            this._lastParticleId++;\r\n        }\r\n        return sp;\r\n    }\r\n\r\n    /**\r\n     *  Sets all the particles : this method actually really updates the mesh according to the particle positions, rotations, colors, textures, etc.\r\n     *  This method calls `updateParticle()` for each particle of the SPS.\r\n     *  For an animated SPS, it is usually called within the render loop.\r\n     * This methods does nothing if called on a non updatable or not yet built SPS. Example : buildMesh() not called after having added or removed particles from an expandable SPS.\r\n     * @param start The particle index in the particle array where to start to compute the particle property values _(default 0)_\r\n     * @param end The particle index in the particle array where to stop to compute the particle property values _(default nbParticle - 1)_\r\n     * @param update If the mesh must be finally updated on this call after all the particle computations _(default true)_\r\n     * @returns the SPS.\r\n     */\r\n    public setParticles(start: number = 0, end: number = this.nbParticles - 1, update: boolean = true): SolidParticleSystem {\r\n        if (!this._updatable || this._isNotBuilt) {\r\n            return this;\r\n        }\r\n\r\n        // custom beforeUpdate\r\n        this.beforeUpdateParticles(start, end, update);\r\n\r\n        const rotMatrix = TmpVectors.Matrix[0];\r\n        const invertedMatrix = TmpVectors.Matrix[1];\r\n        const mesh = this.mesh;\r\n        const colors32 = this._colors32;\r\n        const positions32 = this._positions32;\r\n        const normals32 = this._normals32;\r\n        const uvs32 = this._uvs32;\r\n        const indices32 = this._indices32;\r\n        const indices = this._indices;\r\n        const fixedNormal32 = this._fixedNormal32;\r\n        const depthSortParticles = this._depthSort && this._depthSortParticles;\r\n\r\n        const tempVectors = TmpVectors.Vector3;\r\n        const camAxisX = tempVectors[5].copyFromFloats(1.0, 0.0, 0.0);\r\n        const camAxisY = tempVectors[6].copyFromFloats(0.0, 1.0, 0.0);\r\n        const camAxisZ = tempVectors[7].copyFromFloats(0.0, 0.0, 1.0);\r\n        const minimum = tempVectors[8].setAll(Number.MAX_VALUE);\r\n        const maximum = tempVectors[9].setAll(-Number.MAX_VALUE);\r\n        const camInvertedPosition = tempVectors[10].setAll(0);\r\n\r\n        const tmpVertex = this._tmpVertex;\r\n        const tmpVector = tmpVertex.position;\r\n        const tmpColor = tmpVertex.color;\r\n        const tmpUV = tmpVertex.uv;\r\n\r\n        // cases when the World Matrix is to be computed first\r\n        if (this.billboard || this._depthSort) {\r\n            this.mesh.computeWorldMatrix(true);\r\n            this.mesh._worldMatrix.invertToRef(invertedMatrix);\r\n        }\r\n        // if the particles will always face the camera\r\n        if (this.billboard) {\r\n            // compute the camera position and un-rotate it by the current mesh rotation\r\n            const tmpVector0 = tempVectors[0];\r\n            this._camera.getDirectionToRef(Axis.Z, tmpVector0);\r\n            Vector3.TransformNormalToRef(tmpVector0, invertedMatrix, camAxisZ);\r\n            camAxisZ.normalize();\r\n            // same for camera up vector extracted from the cam view matrix\r\n            const view = this._camera.getViewMatrix(true);\r\n            Vector3.TransformNormalFromFloatsToRef(view.m[1], view.m[5], view.m[9], invertedMatrix, camAxisY);\r\n            Vector3.CrossToRef(camAxisY, camAxisZ, camAxisX);\r\n            camAxisY.normalize();\r\n            camAxisX.normalize();\r\n        }\r\n\r\n        // if depthSort, compute the camera global position in the mesh local system\r\n        if (this._depthSort) {\r\n            Vector3.TransformCoordinatesToRef(this._camera.globalPosition, invertedMatrix, camInvertedPosition); // then un-rotate the camera\r\n        }\r\n\r\n        Matrix.IdentityToRef(rotMatrix);\r\n        let idx = 0; // current position index in the global array positions32\r\n        let index = 0; // position start index in the global array positions32 of the current particle\r\n        let colidx = 0; // current color index in the global array colors32\r\n        let colorIndex = 0; // color start index in the global array colors32 of the current particle\r\n        let uvidx = 0; // current uv index in the global array uvs32\r\n        let uvIndex = 0; // uv start index in the global array uvs32 of the current particle\r\n        let pt = 0; // current index in the particle model shape\r\n\r\n        if (this.mesh.isFacetDataEnabled) {\r\n            this._computeBoundingBox = true;\r\n        }\r\n\r\n        end = end >= this.nbParticles ? this.nbParticles - 1 : end;\r\n        if (this._computeBoundingBox) {\r\n            if (start != 0 || end != this.nbParticles - 1) {\r\n                // only some particles are updated, then use the current existing BBox basis. Note : it can only increase.\r\n                const boundingInfo = this.mesh.getBoundingInfo();\r\n                if (boundingInfo) {\r\n                    minimum.copyFrom(boundingInfo.minimum);\r\n                    maximum.copyFrom(boundingInfo.maximum);\r\n                }\r\n            }\r\n        }\r\n\r\n        // particle loop\r\n        index = this.particles[start]._pos;\r\n        const vpos = (index / 3) | 0;\r\n        colorIndex = vpos * 4;\r\n        uvIndex = vpos * 2;\r\n\r\n        for (let p = start; p <= end; p++) {\r\n            const particle = this.particles[p];\r\n\r\n            // call to custom user function to update the particle properties\r\n            this.updateParticle(particle);\r\n\r\n            const shape = particle._model._shape;\r\n            const shapeUV = particle._model._shapeUV;\r\n            const particleRotationMatrix = particle._rotationMatrix;\r\n            const particlePosition = particle.position;\r\n            const particleRotation = particle.rotation;\r\n            const particleScaling = particle.scaling;\r\n            const particleGlobalPosition = particle._globalPosition;\r\n\r\n            // camera-particle distance for depth sorting\r\n            if (depthSortParticles) {\r\n                const dsp = this.depthSortedParticles[p];\r\n                dsp.idx = particle.idx;\r\n                dsp.ind = particle._ind;\r\n                dsp.indicesLength = particle._model._indicesLength;\r\n                dsp.sqDistance = Vector3.DistanceSquared(particle.position, camInvertedPosition);\r\n            }\r\n\r\n            // skip the computations for inactive or already invisible particles\r\n            if (!particle.alive || (particle._stillInvisible && !particle.isVisible && !this._recomputeInvisibles)) {\r\n                // increment indexes for the next particle\r\n                pt = shape.length;\r\n                index += pt * 3;\r\n                colorIndex += pt * 4;\r\n                uvIndex += pt * 2;\r\n                continue;\r\n            }\r\n\r\n            if (particle.isVisible) {\r\n                particle._stillInvisible = false; // un-mark permanent invisibility\r\n\r\n                const scaledPivot = tempVectors[12];\r\n                particle.pivot.multiplyToRef(particleScaling, scaledPivot);\r\n\r\n                // particle rotation matrix\r\n                if (this.billboard) {\r\n                    particleRotation.x = 0.0;\r\n                    particleRotation.y = 0.0;\r\n                }\r\n                if (this._computeParticleRotation || this.billboard) {\r\n                    particle.getRotationMatrix(rotMatrix);\r\n                }\r\n\r\n                const particleHasParent = particle.parentId !== null;\r\n                if (particleHasParent) {\r\n                    const parent = this.getParticleById(particle.parentId!);\r\n                    if (parent) {\r\n                        const parentRotationMatrix = parent._rotationMatrix;\r\n                        const parentGlobalPosition = parent._globalPosition;\r\n\r\n                        const rotatedY = particlePosition.x * parentRotationMatrix[1] + particlePosition.y * parentRotationMatrix[4] + particlePosition.z * parentRotationMatrix[7];\r\n                        const rotatedX = particlePosition.x * parentRotationMatrix[0] + particlePosition.y * parentRotationMatrix[3] + particlePosition.z * parentRotationMatrix[6];\r\n                        const rotatedZ = particlePosition.x * parentRotationMatrix[2] + particlePosition.y * parentRotationMatrix[5] + particlePosition.z * parentRotationMatrix[8];\r\n\r\n                        particleGlobalPosition.x = parentGlobalPosition.x + rotatedX;\r\n                        particleGlobalPosition.y = parentGlobalPosition.y + rotatedY;\r\n                        particleGlobalPosition.z = parentGlobalPosition.z + rotatedZ;\r\n\r\n                        if (this._computeParticleRotation || this.billboard) {\r\n                            const rotMatrixValues = rotMatrix.m;\r\n                            particleRotationMatrix[0] =\r\n                                rotMatrixValues[0] * parentRotationMatrix[0] + rotMatrixValues[1] * parentRotationMatrix[3] + rotMatrixValues[2] * parentRotationMatrix[6];\r\n                            particleRotationMatrix[1] =\r\n                                rotMatrixValues[0] * parentRotationMatrix[1] + rotMatrixValues[1] * parentRotationMatrix[4] + rotMatrixValues[2] * parentRotationMatrix[7];\r\n                            particleRotationMatrix[2] =\r\n                                rotMatrixValues[0] * parentRotationMatrix[2] + rotMatrixValues[1] * parentRotationMatrix[5] + rotMatrixValues[2] * parentRotationMatrix[8];\r\n                            particleRotationMatrix[3] =\r\n                                rotMatrixValues[4] * parentRotationMatrix[0] + rotMatrixValues[5] * parentRotationMatrix[3] + rotMatrixValues[6] * parentRotationMatrix[6];\r\n                            particleRotationMatrix[4] =\r\n                                rotMatrixValues[4] * parentRotationMatrix[1] + rotMatrixValues[5] * parentRotationMatrix[4] + rotMatrixValues[6] * parentRotationMatrix[7];\r\n                            particleRotationMatrix[5] =\r\n                                rotMatrixValues[4] * parentRotationMatrix[2] + rotMatrixValues[5] * parentRotationMatrix[5] + rotMatrixValues[6] * parentRotationMatrix[8];\r\n                            particleRotationMatrix[6] =\r\n                                rotMatrixValues[8] * parentRotationMatrix[0] + rotMatrixValues[9] * parentRotationMatrix[3] + rotMatrixValues[10] * parentRotationMatrix[6];\r\n                            particleRotationMatrix[7] =\r\n                                rotMatrixValues[8] * parentRotationMatrix[1] + rotMatrixValues[9] * parentRotationMatrix[4] + rotMatrixValues[10] * parentRotationMatrix[7];\r\n                            particleRotationMatrix[8] =\r\n                                rotMatrixValues[8] * parentRotationMatrix[2] + rotMatrixValues[9] * parentRotationMatrix[5] + rotMatrixValues[10] * parentRotationMatrix[8];\r\n                        }\r\n                    } else {\r\n                        // in case the parent were removed at some moment\r\n                        particle.parentId = null;\r\n                    }\r\n                } else {\r\n                    particleGlobalPosition.x = particlePosition.x;\r\n                    particleGlobalPosition.y = particlePosition.y;\r\n                    particleGlobalPosition.z = particlePosition.z;\r\n\r\n                    if (this._computeParticleRotation || this.billboard) {\r\n                        const rotMatrixValues = rotMatrix.m;\r\n                        particleRotationMatrix[0] = rotMatrixValues[0];\r\n                        particleRotationMatrix[1] = rotMatrixValues[1];\r\n                        particleRotationMatrix[2] = rotMatrixValues[2];\r\n                        particleRotationMatrix[3] = rotMatrixValues[4];\r\n                        particleRotationMatrix[4] = rotMatrixValues[5];\r\n                        particleRotationMatrix[5] = rotMatrixValues[6];\r\n                        particleRotationMatrix[6] = rotMatrixValues[8];\r\n                        particleRotationMatrix[7] = rotMatrixValues[9];\r\n                        particleRotationMatrix[8] = rotMatrixValues[10];\r\n                    }\r\n                }\r\n\r\n                const pivotBackTranslation = tempVectors[11];\r\n                if (particle.translateFromPivot) {\r\n                    pivotBackTranslation.setAll(0.0);\r\n                } else {\r\n                    pivotBackTranslation.copyFrom(scaledPivot);\r\n                }\r\n\r\n                // particle vertex loop\r\n                for (pt = 0; pt < shape.length; pt++) {\r\n                    idx = index + pt * 3;\r\n                    colidx = colorIndex + pt * 4;\r\n                    uvidx = uvIndex + pt * 2;\r\n                    const iu = 2 * pt;\r\n                    const iv = iu + 1;\r\n\r\n                    tmpVector.copyFrom(shape[pt]);\r\n                    if (this._computeParticleColor && particle.color) {\r\n                        tmpColor.copyFrom(particle.color);\r\n                    }\r\n                    if (this._computeParticleTexture) {\r\n                        tmpUV.copyFromFloats(shapeUV[iu], shapeUV[iv]);\r\n                    }\r\n                    if (this._computeParticleVertex) {\r\n                        this.updateParticleVertex(particle, tmpVertex, pt);\r\n                    }\r\n\r\n                    // positions\r\n                    const vertexX = tmpVector.x * particleScaling.x - scaledPivot.x;\r\n                    const vertexY = tmpVector.y * particleScaling.y - scaledPivot.y;\r\n                    const vertexZ = tmpVector.z * particleScaling.z - scaledPivot.z;\r\n\r\n                    let rotatedX = vertexX * particleRotationMatrix[0] + vertexY * particleRotationMatrix[3] + vertexZ * particleRotationMatrix[6];\r\n                    let rotatedY = vertexX * particleRotationMatrix[1] + vertexY * particleRotationMatrix[4] + vertexZ * particleRotationMatrix[7];\r\n                    let rotatedZ = vertexX * particleRotationMatrix[2] + vertexY * particleRotationMatrix[5] + vertexZ * particleRotationMatrix[8];\r\n\r\n                    rotatedX += pivotBackTranslation.x;\r\n                    rotatedY += pivotBackTranslation.y;\r\n                    rotatedZ += pivotBackTranslation.z;\r\n\r\n                    const px = (positions32[idx] = particleGlobalPosition.x + camAxisX.x * rotatedX + camAxisY.x * rotatedY + camAxisZ.x * rotatedZ);\r\n                    const py = (positions32[idx + 1] = particleGlobalPosition.y + camAxisX.y * rotatedX + camAxisY.y * rotatedY + camAxisZ.y * rotatedZ);\r\n                    const pz = (positions32[idx + 2] = particleGlobalPosition.z + camAxisX.z * rotatedX + camAxisY.z * rotatedY + camAxisZ.z * rotatedZ);\r\n\r\n                    if (this._computeBoundingBox) {\r\n                        minimum.minimizeInPlaceFromFloats(px, py, pz);\r\n                        maximum.maximizeInPlaceFromFloats(px, py, pz);\r\n                    }\r\n\r\n                    // normals : if the particles can't be morphed then just rotate the normals, what is much more faster than ComputeNormals()\r\n                    if (!this._computeParticleVertex) {\r\n                        const normalx = fixedNormal32[idx];\r\n                        const normaly = fixedNormal32[idx + 1];\r\n                        const normalz = fixedNormal32[idx + 2];\r\n\r\n                        const rotatedx = normalx * particleRotationMatrix[0] + normaly * particleRotationMatrix[3] + normalz * particleRotationMatrix[6];\r\n                        const rotatedy = normalx * particleRotationMatrix[1] + normaly * particleRotationMatrix[4] + normalz * particleRotationMatrix[7];\r\n                        const rotatedz = normalx * particleRotationMatrix[2] + normaly * particleRotationMatrix[5] + normalz * particleRotationMatrix[8];\r\n\r\n                        normals32[idx] = camAxisX.x * rotatedx + camAxisY.x * rotatedy + camAxisZ.x * rotatedz;\r\n                        normals32[idx + 1] = camAxisX.y * rotatedx + camAxisY.y * rotatedy + camAxisZ.y * rotatedz;\r\n                        normals32[idx + 2] = camAxisX.z * rotatedx + camAxisY.z * rotatedy + camAxisZ.z * rotatedz;\r\n                    }\r\n\r\n                    if (this._computeParticleColor && particle.color) {\r\n                        const colors32 = this._colors32;\r\n                        colors32[colidx] = tmpColor.r;\r\n                        colors32[colidx + 1] = tmpColor.g;\r\n                        colors32[colidx + 2] = tmpColor.b;\r\n                        colors32[colidx + 3] = tmpColor.a;\r\n                    }\r\n\r\n                    if (this._computeParticleTexture) {\r\n                        const uvs = particle.uvs;\r\n                        uvs32[uvidx] = tmpUV.x * (uvs.z - uvs.x) + uvs.x;\r\n                        uvs32[uvidx + 1] = tmpUV.y * (uvs.w - uvs.y) + uvs.y;\r\n                    }\r\n                }\r\n            }\r\n            // particle just set invisible : scaled to zero and positioned at the origin\r\n            else {\r\n                particle._stillInvisible = true; // mark the particle as invisible\r\n                for (pt = 0; pt < shape.length; pt++) {\r\n                    idx = index + pt * 3;\r\n                    colidx = colorIndex + pt * 4;\r\n                    uvidx = uvIndex + pt * 2;\r\n\r\n                    positions32[idx] = positions32[idx + 1] = positions32[idx + 2] = 0;\r\n                    normals32[idx] = normals32[idx + 1] = normals32[idx + 2] = 0;\r\n                    if (this._computeParticleColor && particle.color) {\r\n                        const color = particle.color;\r\n                        colors32[colidx] = color.r;\r\n                        colors32[colidx + 1] = color.g;\r\n                        colors32[colidx + 2] = color.b;\r\n                        colors32[colidx + 3] = color.a;\r\n                    }\r\n                    if (this._computeParticleTexture) {\r\n                        const uvs = particle.uvs;\r\n                        uvs32[uvidx] = shapeUV[pt * 2] * (uvs.z - uvs.x) + uvs.x;\r\n                        uvs32[uvidx + 1] = shapeUV[pt * 2 + 1] * (uvs.w - uvs.y) + uvs.y;\r\n                    }\r\n                }\r\n            }\r\n\r\n            // if the particle intersections must be computed : update the bbInfo\r\n            if (this._particlesIntersect) {\r\n                const bInfo = particle.getBoundingInfo();\r\n                const bBox = bInfo.boundingBox;\r\n                const bSphere = bInfo.boundingSphere;\r\n                const modelBoundingInfo = particle._modelBoundingInfo;\r\n                if (!this._bSphereOnly) {\r\n                    // place, scale and rotate the particle bbox within the SPS local system, then update it\r\n                    const modelBoundingInfoVectors = modelBoundingInfo.boundingBox.vectors;\r\n\r\n                    const tempMin = tempVectors[1];\r\n                    const tempMax = tempVectors[2];\r\n                    tempMin.setAll(Number.MAX_VALUE);\r\n                    tempMax.setAll(-Number.MAX_VALUE);\r\n                    for (let b = 0; b < 8; b++) {\r\n                        const scaledX = modelBoundingInfoVectors[b].x * particleScaling.x;\r\n                        const scaledY = modelBoundingInfoVectors[b].y * particleScaling.y;\r\n                        const scaledZ = modelBoundingInfoVectors[b].z * particleScaling.z;\r\n                        const rotatedX = scaledX * particleRotationMatrix[0] + scaledY * particleRotationMatrix[3] + scaledZ * particleRotationMatrix[6];\r\n                        const rotatedY = scaledX * particleRotationMatrix[1] + scaledY * particleRotationMatrix[4] + scaledZ * particleRotationMatrix[7];\r\n                        const rotatedZ = scaledX * particleRotationMatrix[2] + scaledY * particleRotationMatrix[5] + scaledZ * particleRotationMatrix[8];\r\n                        const x = particlePosition.x + camAxisX.x * rotatedX + camAxisY.x * rotatedY + camAxisZ.x * rotatedZ;\r\n                        const y = particlePosition.y + camAxisX.y * rotatedX + camAxisY.y * rotatedY + camAxisZ.y * rotatedZ;\r\n                        const z = particlePosition.z + camAxisX.z * rotatedX + camAxisY.z * rotatedY + camAxisZ.z * rotatedZ;\r\n                        tempMin.minimizeInPlaceFromFloats(x, y, z);\r\n                        tempMax.maximizeInPlaceFromFloats(x, y, z);\r\n                    }\r\n\r\n                    bBox.reConstruct(tempMin, tempMax, mesh._worldMatrix);\r\n                }\r\n\r\n                // place and scale the particle bouding sphere in the SPS local system, then update it\r\n                const minBbox = modelBoundingInfo.minimum.multiplyToRef(particleScaling, tempVectors[1]);\r\n                const maxBbox = modelBoundingInfo.maximum.multiplyToRef(particleScaling, tempVectors[2]);\r\n\r\n                const bSphereCenter = maxBbox.addToRef(minBbox, tempVectors[3]).scaleInPlace(0.5).addInPlace(particleGlobalPosition);\r\n                const halfDiag = maxBbox.subtractToRef(minBbox, tempVectors[4]).scaleInPlace(0.5 * this._bSphereRadiusFactor);\r\n                const bSphereMinBbox = bSphereCenter.subtractToRef(halfDiag, tempVectors[1]);\r\n                const bSphereMaxBbox = bSphereCenter.addToRef(halfDiag, tempVectors[2]);\r\n                bSphere.reConstruct(bSphereMinBbox, bSphereMaxBbox, mesh._worldMatrix);\r\n            }\r\n\r\n            // increment indexes for the next particle\r\n            index = idx + 3;\r\n            colorIndex = colidx + 4;\r\n            uvIndex = uvidx + 2;\r\n        }\r\n\r\n        // if the VBO must be updated\r\n        if (update) {\r\n            if (this._computeParticleColor) {\r\n                const vb = mesh.getVertexBuffer(VertexBuffer.ColorKind);\r\n                if (vb && !mesh.isPickable) {\r\n                    vb.updateDirectly(colors32, 0);\r\n                } else {\r\n                    mesh.updateVerticesData(VertexBuffer.ColorKind, colors32, false, false);\r\n                }\r\n            }\r\n            if (this._computeParticleTexture) {\r\n                const vb = mesh.getVertexBuffer(VertexBuffer.UVKind);\r\n                if (vb && !mesh.isPickable) {\r\n                    vb.updateDirectly(uvs32, 0);\r\n                } else {\r\n                    mesh.updateVerticesData(VertexBuffer.UVKind, uvs32, false, false);\r\n                }\r\n            }\r\n            const vbp = mesh.getVertexBuffer(VertexBuffer.PositionKind);\r\n            if (vbp && !mesh.isPickable) {\r\n                vbp.updateDirectly(positions32, 0);\r\n            } else {\r\n                mesh.updateVerticesData(VertexBuffer.PositionKind, positions32, false, false);\r\n            }\r\n            if (!mesh.areNormalsFrozen || mesh.isFacetDataEnabled) {\r\n                if (this._computeParticleVertex || mesh.isFacetDataEnabled) {\r\n                    // recompute the normals only if the particles can be morphed, update then also the normal reference array _fixedNormal32[]\r\n                    const params = mesh.isFacetDataEnabled ? mesh.getFacetDataParameters() : null;\r\n                    VertexData.ComputeNormals(positions32, indices32, normals32, params);\r\n                    for (let i = 0; i < normals32.length; i++) {\r\n                        fixedNormal32[i] = normals32[i];\r\n                    }\r\n                }\r\n                if (!mesh.areNormalsFrozen) {\r\n                    const vb = mesh.getVertexBuffer(VertexBuffer.NormalKind);\r\n                    if (vb && !mesh.isPickable) {\r\n                        vb.updateDirectly(normals32, 0);\r\n                    } else {\r\n                        mesh.updateVerticesData(VertexBuffer.NormalKind, normals32, false, false);\r\n                    }\r\n                }\r\n            }\r\n            if (depthSortParticles) {\r\n                const depthSortedParticles = this.depthSortedParticles;\r\n                depthSortedParticles.sort(this._depthSortFunction);\r\n                const dspl = depthSortedParticles.length;\r\n                let sid = 0;\r\n                let faceId = 0;\r\n                for (let sorted = 0; sorted < dspl; sorted++) {\r\n                    const sortedParticle = depthSortedParticles[sorted];\r\n                    const lind = sortedParticle.indicesLength;\r\n                    const sind = sortedParticle.ind;\r\n                    for (let i = 0; i < lind; i++) {\r\n                        indices32[sid] = indices[sind + i];\r\n                        sid++;\r\n                        if (this._pickable) {\r\n                            const f = i % 3;\r\n                            if (f == 0) {\r\n                                const pickedData = this.pickedParticles[faceId];\r\n                                pickedData.idx = sortedParticle.idx;\r\n                                pickedData.faceId = faceId;\r\n                                faceId++;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            if (this._autoFixFaceOrientation) {\r\n                let particleInd = 0;\r\n\r\n                for (let particleIdx = 0; particleIdx < this.particles.length; particleIdx++) {\r\n                    const particle = depthSortParticles ? this.particles[this.depthSortedParticles[particleIdx].idx] : this.particles[particleIdx];\r\n                    const flipFaces = particle.scale.x * particle.scale.y * particle.scale.z < 0;\r\n\r\n                    if (flipFaces) {\r\n                        for (let faceInd = 0; faceInd < particle._model._indicesLength; faceInd += 3) {\r\n                            const tmp = indices[particle._ind + faceInd];\r\n                            indices32[particleInd + faceInd] = indices[particle._ind + faceInd + 1];\r\n                            indices32[particleInd + faceInd + 1] = tmp;\r\n                        }\r\n                    }\r\n\r\n                    particleInd += particle._model._indicesLength;\r\n                }\r\n            }\r\n\r\n            if (depthSortParticles || this._autoFixFaceOrientation) {\r\n                mesh.updateIndices(indices32);\r\n            }\r\n        }\r\n        if (this._computeBoundingBox) {\r\n            if (mesh.hasBoundingInfo) {\r\n                mesh.getBoundingInfo().reConstruct(minimum, maximum, mesh._worldMatrix);\r\n            } else {\r\n                mesh.buildBoundingInfo(minimum, maximum, mesh._worldMatrix);\r\n            }\r\n        }\r\n        if (this._autoUpdateSubMeshes) {\r\n            this.computeSubMeshes();\r\n        }\r\n        this._recomputeInvisibles = false;\r\n        this.afterUpdateParticles(start, end, update);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Disposes the SPS.\r\n     */\r\n    public dispose(): void {\r\n        this.mesh.dispose();\r\n        this.vars = null;\r\n        // drop references to internal big arrays for the GC\r\n        (<any>this._positions) = null;\r\n        (<any>this._indices) = null;\r\n        (<any>this._normals) = null;\r\n        (<any>this._uvs) = null;\r\n        (<any>this._colors) = null;\r\n        (<any>this._indices32) = null;\r\n        (<any>this._positions32) = null;\r\n        (<any>this._normals32) = null;\r\n        (<any>this._fixedNormal32) = null;\r\n        (<any>this._uvs32) = null;\r\n        (<any>this._colors32) = null;\r\n        (<any>this.pickedParticles) = null;\r\n        (<any>this.pickedBySubMesh) = null;\r\n        (<any>this._materials) = null;\r\n        (<any>this._materialIndexes) = null;\r\n        (<any>this._indicesByMaterial) = null;\r\n        (<any>this._idxOfId) = null;\r\n    }\r\n    /** Returns an object {idx: number faceId: number} for the picked particle from the passed pickingInfo object.\r\n     * idx is the particle index in the SPS\r\n     * faceId is the picked face index counted within this particle.\r\n     * Returns null if the pickInfo can't identify a picked particle.\r\n     * @param pickingInfo (PickingInfo object)\r\n     * @returns {idx: number, faceId: number} or null\r\n     */\r\n    public pickedParticle(pickingInfo: PickingInfo): Nullable<{ idx: number; faceId: number }> {\r\n        if (pickingInfo.hit) {\r\n            const subMesh = pickingInfo.subMeshId;\r\n            const faceId = pickingInfo.faceId - this.mesh.subMeshes[subMesh].indexStart / 3;\r\n            const picked = this.pickedBySubMesh;\r\n            if (picked[subMesh] && picked[subMesh][faceId]) {\r\n                return picked[subMesh][faceId];\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Returns a SolidParticle object from its identifier : particle.id\r\n     * @param id (integer) the particle Id\r\n     * @returns the searched particle or null if not found in the SPS.\r\n     */\r\n    public getParticleById(id: number): Nullable<SolidParticle> {\r\n        const p = this.particles[id];\r\n        if (p && p.id == id) {\r\n            return p;\r\n        }\r\n        const particles = this.particles;\r\n        const idx = this._idxOfId[id];\r\n        if (idx !== undefined) {\r\n            return particles[idx];\r\n        }\r\n        let i = 0;\r\n        const nb = this.nbParticles;\r\n        while (i < nb) {\r\n            const particle = particles[i];\r\n            if (particle.id == id) {\r\n                return particle;\r\n            }\r\n            i++;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Returns a new array populated with the particles having the passed shapeId.\r\n     * @param shapeId (integer) the shape identifier\r\n     * @returns a new solid particle array\r\n     */\r\n    public getParticlesByShapeId(shapeId: number): SolidParticle[] {\r\n        const ref: SolidParticle[] = [];\r\n        this.getParticlesByShapeIdToRef(shapeId, ref);\r\n        return ref;\r\n    }\r\n\r\n    /**\r\n     * Populates the passed array \"ref\" with the particles having the passed shapeId.\r\n     * @param shapeId the shape identifier\r\n     * @param ref array to populate\r\n     * @returns the SPS\r\n     */\r\n    public getParticlesByShapeIdToRef(shapeId: number, ref: SolidParticle[]): SolidParticleSystem {\r\n        ref.length = 0;\r\n        for (let i = 0; i < this.nbParticles; i++) {\r\n            const p = this.particles[i];\r\n            if (p.shapeId == shapeId) {\r\n                ref.push(p);\r\n            }\r\n        }\r\n        return this;\r\n    }\r\n    /**\r\n     * Computes the required SubMeshes according the materials assigned to the particles.\r\n     * @returns the solid particle system.\r\n     * Does nothing if called before the SPS mesh is built.\r\n     */\r\n    public computeSubMeshes(): SolidParticleSystem {\r\n        if (!this.mesh || !this._multimaterialEnabled) {\r\n            return this;\r\n        }\r\n        const depthSortedParticles = this.depthSortedParticles;\r\n        if (this.particles.length > 0) {\r\n            for (let p = 0; p < this.particles.length; p++) {\r\n                const part = this.particles[p];\r\n                if (!part.materialIndex) {\r\n                    part.materialIndex = 0;\r\n                }\r\n                const sortedPart = depthSortedParticles[p];\r\n                sortedPart.materialIndex = part.materialIndex;\r\n                sortedPart.ind = part._ind;\r\n                sortedPart.indicesLength = part._model._indicesLength;\r\n                sortedPart.idx = part.idx;\r\n            }\r\n        }\r\n        this._sortParticlesByMaterial();\r\n        const indicesByMaterial = this._indicesByMaterial;\r\n        const materialIndexes = this._materialIndexes;\r\n        const mesh = this.mesh;\r\n        mesh.subMeshes = [];\r\n        const vcount = mesh.getTotalVertices();\r\n        for (let m = 0; m < materialIndexes.length; m++) {\r\n            const start = indicesByMaterial[m];\r\n            const count = indicesByMaterial[m + 1] - start;\r\n            const matIndex = materialIndexes[m];\r\n            new SubMesh(matIndex, 0, vcount, start, count, mesh);\r\n        }\r\n        return this;\r\n    }\r\n    /**\r\n     * Sorts the solid particles by material when MultiMaterial is enabled.\r\n     * Updates the indices32 array.\r\n     * Updates the indicesByMaterial array.\r\n     * Updates the mesh indices array.\r\n     * @returns the SPS\r\n     * @internal\r\n     */\r\n    protected _sortParticlesByMaterial(): SolidParticleSystem {\r\n        const indicesByMaterial = [0];\r\n        this._indicesByMaterial = indicesByMaterial;\r\n        const materialIndexes: number[] = [];\r\n        this._materialIndexes = materialIndexes;\r\n        const depthSortedParticles = this.depthSortedParticles;\r\n        depthSortedParticles.sort(this._materialSortFunction);\r\n        const length = depthSortedParticles.length;\r\n        const indices32 = this._indices32;\r\n        const indices = this._indices;\r\n\r\n        let subMeshIndex = 0;\r\n        let subMeshFaceId = 0;\r\n        let sid = 0;\r\n        let lastMatIndex = depthSortedParticles[0].materialIndex;\r\n        materialIndexes.push(lastMatIndex);\r\n        if (this._pickable) {\r\n            this.pickedBySubMesh = [[]];\r\n            this.pickedParticles = this.pickedBySubMesh[0];\r\n        }\r\n        for (let sorted = 0; sorted < length; sorted++) {\r\n            const sortedPart = depthSortedParticles[sorted];\r\n            const lind = sortedPart.indicesLength;\r\n            const sind = sortedPart.ind;\r\n            if (sortedPart.materialIndex !== lastMatIndex) {\r\n                lastMatIndex = sortedPart.materialIndex;\r\n                indicesByMaterial.push(sid);\r\n                materialIndexes.push(lastMatIndex);\r\n                if (this._pickable) {\r\n                    subMeshIndex++;\r\n                    this.pickedBySubMesh[subMeshIndex] = [];\r\n                    subMeshFaceId = 0;\r\n                }\r\n            }\r\n            let faceId = 0;\r\n            for (let i = 0; i < lind; i++) {\r\n                indices32[sid] = indices[sind + i];\r\n                if (this._pickable) {\r\n                    const f = i % 3;\r\n                    if (f == 0) {\r\n                        const pickedData = this.pickedBySubMesh[subMeshIndex][subMeshFaceId];\r\n                        if (pickedData) {\r\n                            pickedData.idx = sortedPart.idx;\r\n                            pickedData.faceId = faceId;\r\n                        } else {\r\n                            this.pickedBySubMesh[subMeshIndex][subMeshFaceId] = { idx: sortedPart.idx, faceId: faceId };\r\n                        }\r\n                        subMeshFaceId++;\r\n                        faceId++;\r\n                    }\r\n                }\r\n                sid++;\r\n            }\r\n        }\r\n\r\n        indicesByMaterial.push(indices32.length); // add the last number to ease the indices start/count values for subMeshes creation\r\n        if (this._updatable) {\r\n            this.mesh.updateIndices(indices32);\r\n        }\r\n        return this;\r\n    }\r\n    /**\r\n     * Sets the material indexes by id materialIndexesById[id] = materialIndex\r\n     * @internal\r\n     */\r\n    protected _setMaterialIndexesById() {\r\n        this._materialIndexesById = {};\r\n        for (let i = 0; i < this._materials.length; i++) {\r\n            const id = this._materials[i].uniqueId;\r\n            this._materialIndexesById[id] = i;\r\n        }\r\n    }\r\n    /**\r\n     * Returns an array with unique values of Materials from the passed array\r\n     * @param array the material array to be checked and filtered\r\n     * @internal\r\n     */\r\n    protected _filterUniqueMaterialId(array: Material[]): Material[] {\r\n        const filtered = array.filter(function (value, index, self) {\r\n            return self.indexOf(value) === index;\r\n        });\r\n        return filtered;\r\n    }\r\n    /**\r\n     * Sets a new Standard Material as _defaultMaterial if not already set.\r\n     * @internal\r\n     */\r\n    protected _setDefaultMaterial(): Material {\r\n        if (!this._defaultMaterial) {\r\n            this._defaultMaterial = new StandardMaterial(this.name + \"DefaultMaterial\", this._scene);\r\n        }\r\n        return this._defaultMaterial;\r\n    }\r\n    /**\r\n     * Visibility helper : Recomputes the visible size according to the mesh bounding box\r\n     * doc : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/sps_visibility\r\n     * @returns the SPS.\r\n     */\r\n    public refreshVisibleSize(): SolidParticleSystem {\r\n        if (!this._isVisibilityBoxLocked) {\r\n            this.mesh.refreshBoundingInfo();\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Visibility helper : Sets the size of a visibility box, this sets the underlying mesh bounding box.\r\n     * @param size the size (float) of the visibility box\r\n     * note : this doesn't lock the SPS mesh bounding box.\r\n     * doc : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/sps_visibility\r\n     */\r\n    public setVisibilityBox(size: number): void {\r\n        const vis = size / 2;\r\n        this.mesh.buildBoundingInfo(new Vector3(-vis, -vis, -vis), new Vector3(vis, vis, vis));\r\n    }\r\n\r\n    /**\r\n     * Gets whether the SPS as always visible or not\r\n     * doc : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/sps_visibility\r\n     */\r\n    public get isAlwaysVisible(): boolean {\r\n        return this._alwaysVisible;\r\n    }\r\n\r\n    /**\r\n     * Sets the SPS as always visible or not\r\n     * doc : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/sps_visibility\r\n     */\r\n    public set isAlwaysVisible(val: boolean) {\r\n        this._alwaysVisible = val;\r\n        this.mesh.alwaysSelectAsActiveMesh = val;\r\n    }\r\n\r\n    /**\r\n     * Sets the SPS visibility box as locked or not. This enables/disables the underlying mesh bounding box updates.\r\n     * doc : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/sps_visibility\r\n     */\r\n    public set isVisibilityBoxLocked(val: boolean) {\r\n        this._isVisibilityBoxLocked = val;\r\n\r\n        const boundingInfo = this.mesh.getBoundingInfo();\r\n\r\n        boundingInfo.isLocked = val;\r\n    }\r\n\r\n    /**\r\n     * Gets if the SPS visibility box as locked or not. This enables/disables the underlying mesh bounding box updates.\r\n     * doc : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/sps_visibility\r\n     */\r\n    public get isVisibilityBoxLocked(): boolean {\r\n        return this._isVisibilityBoxLocked;\r\n    }\r\n\r\n    /**\r\n     * Tells to `setParticles()` to compute the particle rotations or not.\r\n     * Default value : true. The SPS is faster when it's set to false.\r\n     * Note : the particle rotations aren't stored values, so setting `computeParticleRotation` to false will prevents the particle to rotate.\r\n     */\r\n    public set computeParticleRotation(val: boolean) {\r\n        this._computeParticleRotation = val;\r\n    }\r\n\r\n    /**\r\n     * Tells to `setParticles()` to compute the particle colors or not.\r\n     * Default value : true. The SPS is faster when it's set to false.\r\n     * Note : the particle colors are stored values, so setting `computeParticleColor` to false will keep yet the last colors set.\r\n     */\r\n    public set computeParticleColor(val: boolean) {\r\n        this._computeParticleColor = val;\r\n    }\r\n\r\n    public set computeParticleTexture(val: boolean) {\r\n        this._computeParticleTexture = val;\r\n    }\r\n    /**\r\n     * Tells to `setParticles()` to call the vertex function for each vertex of each particle, or not.\r\n     * Default value : false. The SPS is faster when it's set to false.\r\n     * Note : the particle custom vertex positions aren't stored values.\r\n     */\r\n    public set computeParticleVertex(val: boolean) {\r\n        this._computeParticleVertex = val;\r\n    }\r\n    /**\r\n     * Tells to `setParticles()` to compute or not the mesh bounding box when computing the particle positions.\r\n     */\r\n    public set computeBoundingBox(val: boolean) {\r\n        this._computeBoundingBox = val;\r\n    }\r\n    /**\r\n     * Tells to `setParticles()` to sort or not the distance between each particle and the camera.\r\n     * Skipped when `enableDepthSort` is set to `false` (default) at construction time.\r\n     * Default : `true`\r\n     */\r\n    public set depthSortParticles(val: boolean) {\r\n        this._depthSortParticles = val;\r\n    }\r\n\r\n    /**\r\n     * Gets if `setParticles()` computes the particle rotations or not.\r\n     * Default value : true. The SPS is faster when it's set to false.\r\n     * Note : the particle rotations aren't stored values, so setting `computeParticleRotation` to false will prevents the particle to rotate.\r\n     */\r\n    public get computeParticleRotation(): boolean {\r\n        return this._computeParticleRotation;\r\n    }\r\n\r\n    /**\r\n     * Gets if `setParticles()` computes the particle colors or not.\r\n     * Default value : true. The SPS is faster when it's set to false.\r\n     * Note : the particle colors are stored values, so setting `computeParticleColor` to false will keep yet the last colors set.\r\n     */\r\n    public get computeParticleColor(): boolean {\r\n        return this._computeParticleColor;\r\n    }\r\n\r\n    /**\r\n     * Gets if `setParticles()` computes the particle textures or not.\r\n     * Default value : true. The SPS is faster when it's set to false.\r\n     * Note : the particle textures are stored values, so setting `computeParticleTexture` to false will keep yet the last colors set.\r\n     */\r\n    public get computeParticleTexture(): boolean {\r\n        return this._computeParticleTexture;\r\n    }\r\n\r\n    /**\r\n     * Gets if `setParticles()` calls the vertex function for each vertex of each particle, or not.\r\n     * Default value : false. The SPS is faster when it's set to false.\r\n     * Note : the particle custom vertex positions aren't stored values.\r\n     */\r\n    public get computeParticleVertex(): boolean {\r\n        return this._computeParticleVertex;\r\n    }\r\n\r\n    /**\r\n     * Gets if `setParticles()` computes or not the mesh bounding box when computing the particle positions.\r\n     */\r\n    public get computeBoundingBox(): boolean {\r\n        return this._computeBoundingBox;\r\n    }\r\n\r\n    /**\r\n     * Gets if `setParticles()` sorts or not the distance between each particle and the camera.\r\n     * Skipped when `enableDepthSort` is set to `false` (default) at construction time.\r\n     * Default : `true`\r\n     */\r\n    public get depthSortParticles(): boolean {\r\n        return this._depthSortParticles;\r\n    }\r\n\r\n    /**\r\n     * Gets if the SPS is created as expandable at construction time.\r\n     * Default : `false`\r\n     */\r\n    public get expandable(): boolean {\r\n        return this._expandable;\r\n    }\r\n    /**\r\n     * Gets if the SPS supports the Multi Materials\r\n     */\r\n    public get multimaterialEnabled(): boolean {\r\n        return this._multimaterialEnabled;\r\n    }\r\n    /**\r\n     * Gets if the SPS uses the model materials for its own multimaterial.\r\n     */\r\n    public get useModelMaterial(): boolean {\r\n        return this._useModelMaterial;\r\n    }\r\n    /**\r\n     * The SPS used material array.\r\n     */\r\n    public get materials(): Material[] {\r\n        return this._materials;\r\n    }\r\n    /**\r\n     * Sets the SPS MultiMaterial from the passed materials.\r\n     * Note : the passed array is internally copied and not used then by reference.\r\n     * @param materials an array of material objects. This array indexes are the materialIndex values of the particles.\r\n     */\r\n    public setMultiMaterial(materials: Material[]) {\r\n        this._materials = this._filterUniqueMaterialId(materials);\r\n        this._setMaterialIndexesById();\r\n        if (this._multimaterial) {\r\n            this._multimaterial.dispose();\r\n        }\r\n        this._multimaterial = new MultiMaterial(this.name + \"MultiMaterial\", this._scene);\r\n        for (let m = 0; m < this._materials.length; m++) {\r\n            this._multimaterial.subMaterials.push(this._materials[m]);\r\n        }\r\n        this.computeSubMeshes();\r\n        this.mesh.material = this._multimaterial;\r\n    }\r\n    /**\r\n     * The SPS computed multimaterial object\r\n     */\r\n    public get multimaterial(): MultiMaterial {\r\n        return this._multimaterial;\r\n    }\r\n    public set multimaterial(mm) {\r\n        this._multimaterial = mm;\r\n    }\r\n    /**\r\n     * If the subMeshes must be updated on the next call to setParticles()\r\n     */\r\n    public get autoUpdateSubMeshes(): boolean {\r\n        return this._autoUpdateSubMeshes;\r\n    }\r\n    public set autoUpdateSubMeshes(val: boolean) {\r\n        this._autoUpdateSubMeshes = val;\r\n    }\r\n    // =======================================================================\r\n    // Particle behavior logic\r\n    // these following methods may be overwritten by the user to fit his needs\r\n\r\n    /**\r\n     * This function does nothing. It may be overwritten to set all the particle first values.\r\n     * The SPS doesn't call this function, you may have to call it by your own.\r\n     * doc : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/manage_sps_particles\r\n     */\r\n    public initParticles(): void {}\r\n\r\n    /**\r\n     * This function does nothing. It may be overwritten to recycle a particle.\r\n     * The SPS doesn't call this function, you may have to call it by your own.\r\n     * doc : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/manage_sps_particles\r\n     * @param particle The particle to recycle\r\n     * @returns the recycled particle\r\n     */\r\n    public recycleParticle(particle: SolidParticle): SolidParticle {\r\n        return particle;\r\n    }\r\n\r\n    /**\r\n     * Updates a particle : this function should  be overwritten by the user.\r\n     * It is called on each particle by `setParticles()`. This is the place to code each particle behavior.\r\n     * doc : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/manage_sps_particles\r\n     * @example : just set a particle position or velocity and recycle conditions\r\n     * @param particle The particle to update\r\n     * @returns the updated particle\r\n     */\r\n    public updateParticle(particle: SolidParticle): SolidParticle {\r\n        return particle;\r\n    }\r\n\r\n    /**\r\n     * Updates a vertex of a particle : it can be overwritten by the user.\r\n     * This will be called on each vertex particle by `setParticles()` if `computeParticleVertex` is set to true only.\r\n     * @param particle the current particle\r\n     * @param vertex the current vertex of the current particle : a SolidParticleVertex object\r\n     * @param pt the index of the current vertex in the particle shape\r\n     * doc : https://doc.babylonjs.com/features/featuresDeepDive/particles/solid_particle_system/sps_vertices\r\n     * @example : just set a vertex particle position or color\r\n     * @returns the sps\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public updateParticleVertex(particle: SolidParticle, vertex: SolidParticleVertex, pt: number): SolidParticleSystem {\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * This will be called before any other treatment by `setParticles()` and will be passed three parameters.\r\n     * This does nothing and may be overwritten by the user.\r\n     * @param start the particle index in the particle array where to stop to iterate, same than the value passed to setParticle()\r\n     * @param stop the particle index in the particle array where to stop to iterate, same than the value passed to setParticle()\r\n     * @param update the boolean update value actually passed to setParticles()\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public beforeUpdateParticles(start?: number, stop?: number, update?: boolean): void {}\r\n    /**\r\n     * This will be called  by `setParticles()` after all the other treatments and just before the actual mesh update.\r\n     * This will be passed three parameters.\r\n     * This does nothing and may be overwritten by the user.\r\n     * @param start the particle index in the particle array where to stop to iterate, same than the value passed to setParticle()\r\n     * @param stop the particle index in the particle array where to stop to iterate, same than the value passed to setParticle()\r\n     * @param update the boolean update value actually passed to setParticles()\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public afterUpdateParticles(start?: number, stop?: number, update?: boolean): void {}\r\n}\r\n"]}