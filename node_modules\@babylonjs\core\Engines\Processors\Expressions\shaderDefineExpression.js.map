{"version": 3, "file": "shaderDefineExpression.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Engines/Processors/Expressions/shaderDefineExpression.ts"], "names": [], "mappings": "AAAA,yDAAyD;AACzD,gBAAgB;AAChB,MAAM,OAAO,sBAAsB;IAwB/B,6DAA6D;IACtD,MAAM,CAAC,aAAwC;QAClD,OAAO,IAAI,CAAC;IAChB,CAAC;IAWM,MAAM,CAAC,cAAc,CAAC,OAAiB;QAC1C,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE;YACrB,IAAI,sBAAsB,CAAC,iBAAiB,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC3D,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACjB;iBAAM;gBACH,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAC9B,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAEjC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC;gBAClB,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;aAClC;SACJ;QAED,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACnC,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACI,MAAM,CAAC,cAAc,CAAC,KAAa;QACtC,4BAA4B;QAC5B,MAAM,SAAS,GAAG,sBAAsB,CAAC,oBAAoB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACzE,IAAI,SAAS,EAAE;YACX,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAClC,OAAO,SAAS,CAAC,MAAM,CAAC;SAC3B;QAED,gCAAgC;QAChC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YAChG,OAAO,CAAC,KAAK,CAAC,CAAC;SAClB;QAED,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC;QAElB,MAAM,WAAW,GAAG,GAAG,EAAE;YACrB,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YACzB,IAAI,OAAO,KAAK,EAAE,EAAE;gBAChB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACrB,OAAO,GAAG,EAAE,CAAC;aAChB;QACL,CAAC,CAAC;QAEF,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,EAAE;YACvB,IAAI,QAAQ,GAAG,sBAAsB,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrD,sBAAsB,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;aACjD;QACL,CAAC,CAAC;QAEF,MAAM,IAAI,GAAG,GAAG,EAAE,CAAC,sBAAsB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE3D,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAE3G,IAAI,GAAG,GAAG,CAAC,EACP,OAAO,GAAG,EAAE,CAAC;QAEjB,OAAO,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE;YACvB,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EACvB,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAE/D,IAAI,CAAC,KAAK,GAAG,EAAE;gBACX,OAAO,GAAG,EAAE,CAAC;gBACb,IAAI,CAAC,CAAC,CAAC,CAAC;aACX;iBAAM,IAAI,CAAC,KAAK,GAAG,EAAE;gBAClB,WAAW,EAAE,CAAC;gBACd,OAAO,QAAQ,KAAK,CAAC,CAAC,IAAI,IAAI,EAAE,KAAK,GAAG,EAAE;oBACtC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;iBACtB;gBACD,GAAG,EAAE,CAAC;aACT;iBAAM,IAAI,sBAAsB,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gBAC5D,WAAW,EAAE,CAAC;gBACd,OAAO,QAAQ,KAAK,CAAC,CAAC,IAAI,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,IAAI,sBAAsB,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;oBAC3H,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;iBACtB;gBACD,IAAI,CAAC,KAAK,CAAC,CAAC;gBACZ,GAAG,EAAE,CAAC;aACT;iBAAM;gBACH,OAAO,IAAI,CAAC,CAAC;aAChB;YACD,GAAG,EAAE,CAAC;SACT;QAED,WAAW,EAAE,CAAC;QAEd,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE;YACpB,IAAI,IAAI,EAAE,KAAK,GAAG,EAAE;gBAChB,GAAG,EAAE,CAAC;aACT;iBAAM;gBACH,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;aACtB;SACJ;QAED,iEAAiE;QACjE,IAAI,sBAAsB,CAAC,oBAAoB,CAAC,IAAI,IAAI,sBAAsB,CAAC,4BAA4B,EAAE;YACzG,sBAAsB,CAAC,UAAU,EAAE,CAAC;SACvC;QAED,oFAAoF;QACpF,sBAAsB,CAAC,oBAAoB,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAE3F,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,MAAM,CAAC,UAAU;QACrB,6DAA6D;QAC7D,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QAExI,uDAAuD;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,sBAAsB,CAAC,8BAA8B,EAAE,CAAC,EAAE,EAAE;YAC5E,sBAAsB,CAAC,oBAAoB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACzE;IACL,CAAC;;AAvKD;;;;GAIG;AACI,mDAA4B,GAAG,KAAK,CAAC;AAE5C;;;;;GAKG;AACI,qDAA8B,GAAG,KAAK,CAAC;AAE7B,2CAAoB,GAMjC,IAAI,GAAG,EAAE,CAAC;AAOC,wCAAiB,GAA+B;IAC3D,GAAG,EAAE,CAAC;IACN,GAAG,EAAE,CAAC;IACN,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;CACV,CAAC;AAEa,6BAAM,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\n/** @internal */\r\nexport class ShaderDefineExpression {\r\n    /**\r\n     * Cache items count limit for the InfixToPostfix cache.\r\n     * It uses to improve the performance of the shader compilation.\r\n     * For details see PR: https://github.com/BabylonJS/Babylon.js/pull/13936\r\n     */\r\n    static InfixToPostfixCacheLimitSize = 50000;\r\n\r\n    /**\r\n     * When the cache size is exceeded, a cache cleanup will be triggered\r\n     * and the cache will be reduced by the size specified\r\n     * in the InfixToPostfixCacheCleanupSize variable, removing entries\r\n     * that have not been accessed the longest.\r\n     */\r\n    static InfixToPostfixCacheCleanupSize = 25000;\r\n\r\n    protected static _InfixToPostfixCache: Map<\r\n        string,\r\n        {\r\n            accessTime: number;\r\n            result: string[];\r\n        }\r\n    > = new Map();\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public isTrue(preprocessors: { [key: string]: string }): boolean {\r\n        return true;\r\n    }\r\n\r\n    private static _OperatorPriority: { [name: string]: number } = {\r\n        \")\": 0,\r\n        \"(\": 1,\r\n        \"||\": 2,\r\n        \"&&\": 3,\r\n    };\r\n\r\n    private static _Stack = [\"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"];\r\n\r\n    public static postfixToInfix(postfix: string[]): string {\r\n        const stack: string[] = [];\r\n\r\n        for (const c of postfix) {\r\n            if (ShaderDefineExpression._OperatorPriority[c] === undefined) {\r\n                stack.push(c);\r\n            } else {\r\n                const v1 = stack[stack.length - 1],\r\n                    v2 = stack[stack.length - 2];\r\n\r\n                stack.length -= 2;\r\n                stack.push(`(${v2}${c}${v1})`);\r\n            }\r\n        }\r\n\r\n        return stack[stack.length - 1];\r\n    }\r\n\r\n    /**\r\n     * Converts an infix expression to a postfix expression.\r\n     *\r\n     * This method is used to transform infix expressions, which are more human-readable,\r\n     * into postfix expressions, also known as Reverse Polish Notation (RPN), that can be\r\n     * evaluated more efficiently by a computer. The conversion is based on the operator\r\n     * priority defined in _OperatorPriority.\r\n     *\r\n     * The function employs a stack-based algorithm for the conversion and caches the result\r\n     * to improve performance. The cache keeps track of each converted expression's access time\r\n     * to manage the cache size and optimize memory usage. When the cache size exceeds a specified\r\n     * limit, the least recently accessed items in the cache are deleted.\r\n     *\r\n     * The cache mechanism is particularly helpful for shader compilation, where the same infix\r\n     * expressions might be encountered repeatedly, hence the caching can speed up the process.\r\n     *\r\n     * @param infix - The infix expression to be converted.\r\n     * @returns The postfix expression as an array of strings.\r\n     */\r\n    public static infixToPostfix(infix: string): string[] {\r\n        // Is infix already in cache\r\n        const cacheItem = ShaderDefineExpression._InfixToPostfixCache.get(infix);\r\n        if (cacheItem) {\r\n            cacheItem.accessTime = Date.now();\r\n            return cacheItem.result;\r\n        }\r\n\r\n        // Is infix contain any operator\r\n        if (!infix.includes(\"&&\") && !infix.includes(\"||\") && !infix.includes(\")\") && !infix.includes(\"(\")) {\r\n            return [infix];\r\n        }\r\n\r\n        const result: string[] = [];\r\n\r\n        let stackIdx = -1;\r\n\r\n        const pushOperand = () => {\r\n            operand = operand.trim();\r\n            if (operand !== \"\") {\r\n                result.push(operand);\r\n                operand = \"\";\r\n            }\r\n        };\r\n\r\n        const push = (s: string) => {\r\n            if (stackIdx < ShaderDefineExpression._Stack.length - 1) {\r\n                ShaderDefineExpression._Stack[++stackIdx] = s;\r\n            }\r\n        };\r\n\r\n        const peek = () => ShaderDefineExpression._Stack[stackIdx];\r\n\r\n        const pop = () => (stackIdx === -1 ? \"!!INVALID EXPRESSION!!\" : ShaderDefineExpression._Stack[stackIdx--]);\r\n\r\n        let idx = 0,\r\n            operand = \"\";\r\n\r\n        while (idx < infix.length) {\r\n            const c = infix.charAt(idx),\r\n                token = idx < infix.length - 1 ? infix.substr(idx, 2) : \"\";\r\n\r\n            if (c === \"(\") {\r\n                operand = \"\";\r\n                push(c);\r\n            } else if (c === \")\") {\r\n                pushOperand();\r\n                while (stackIdx !== -1 && peek() !== \"(\") {\r\n                    result.push(pop());\r\n                }\r\n                pop();\r\n            } else if (ShaderDefineExpression._OperatorPriority[token] > 1) {\r\n                pushOperand();\r\n                while (stackIdx !== -1 && ShaderDefineExpression._OperatorPriority[peek()] >= ShaderDefineExpression._OperatorPriority[token]) {\r\n                    result.push(pop());\r\n                }\r\n                push(token);\r\n                idx++;\r\n            } else {\r\n                operand += c;\r\n            }\r\n            idx++;\r\n        }\r\n\r\n        pushOperand();\r\n\r\n        while (stackIdx !== -1) {\r\n            if (peek() === \"(\") {\r\n                pop();\r\n            } else {\r\n                result.push(pop());\r\n            }\r\n        }\r\n\r\n        // If the cache is at capacity, clear it before adding a new item\r\n        if (ShaderDefineExpression._InfixToPostfixCache.size >= ShaderDefineExpression.InfixToPostfixCacheLimitSize) {\r\n            ShaderDefineExpression.ClearCache();\r\n        }\r\n\r\n        // Add the new item to the cache, including the current time as the last access time\r\n        ShaderDefineExpression._InfixToPostfixCache.set(infix, { result, accessTime: Date.now() });\r\n\r\n        return result;\r\n    }\r\n\r\n    private static ClearCache(): void {\r\n        // Convert the cache to an array and sort by last access time\r\n        const sortedCache = Array.from(ShaderDefineExpression._InfixToPostfixCache.entries()).sort((a, b) => a[1].accessTime - b[1].accessTime);\r\n\r\n        // Remove the least recently accessed half of the cache\r\n        for (let i = 0; i < ShaderDefineExpression.InfixToPostfixCacheCleanupSize; i++) {\r\n            ShaderDefineExpression._InfixToPostfixCache.delete(sortedCache[i][0]);\r\n        }\r\n    }\r\n}\r\n"]}