{"version": 3, "file": "teleportInBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Teleport/teleportInBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAC1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAI5D;;GAEG;AACH,MAAM,OAAO,2BAA4B,SAAQ,iBAAiB;IAG9D,0CAA0C;IAC1C,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,KAAK,CAAC,WAAW,EAAE;YACnB,MAAM,KAAK,GAAG,KAAK,CAAC,cAAe,CAAC,UAAU,CAAC;YAC/C,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,iBAAiB,EAAE;gBAC7D,OAAO,KAAK,CAAC,MAAM,CAAC;aACvB;YAED,IAAI,KAAK,CAAC,cAAe,CAAC,MAAM,KAAK,wBAAwB,CAAC,iBAAiB,EAAE;gBAC7E,OAAO,KAAK,CAAC,cAAe,CAAC,MAAM,CAAC;aACvC;SACJ;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,KAA+B;QAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE;YAC9B,OAAO;SACV;QACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAtC1C,eAAU,GAAmC,EAAE,CAAC;QAwCpD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;IAClF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,6BAA6B,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,2BAA2B;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC;IAC5E,CAAC;IAEM,SAAS,CAAC,WAAqB,EAAE,aAAkC;QACtE,IAAI,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;QAE7D,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;gBACxC,UAAU,IAAI,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;aAChE;SACJ;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,KAAwB;QAC1C,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI,QAAQ,KAAK,KAAK,EAAE;gBACpB,OAAO,IAAI,CAAC;aACf;YAED,IAAI,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBAChC,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,QAAsC;QAC1D,QAAQ,CAAC,MAAM,EAAE,CAAC;QAElB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/B,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;QAC5B,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7D,QAAQ,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACxC,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,kBAAkB,CAAC,QAAsC;QAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACjC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAClD,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;SAC/B;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACzB,CAAC;CACJ;AAED,aAAa,CAAC,qCAAqC,EAAE,2BAA2B,CAAC,CAAC", "sourcesContent": ["import { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { NodeMaterialTeleportOutBlock } from \"./teleportOutBlock\";\r\n\r\n/**\r\n * Defines a block used to teleport a value to an endpoint\r\n */\r\nexport class NodeMaterialTeleportInBlock extends NodeMaterialBlock {\r\n    private _endpoints: NodeMaterialTeleportOutBlock[] = [];\r\n\r\n    /** Gets the list of attached endpoints */\r\n    public get endpoints() {\r\n        return this._endpoints;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the target of the block\r\n     */\r\n    public get target() {\r\n        const input = this._inputs[0];\r\n        if (input.isConnected) {\r\n            const block = input.connectedPoint!.ownerBlock;\r\n            if (block.target !== NodeMaterialBlockTargets.VertexAndFragment) {\r\n                return block.target;\r\n            }\r\n\r\n            if (input.connectedPoint!.target !== NodeMaterialBlockTargets.VertexAndFragment) {\r\n                return input.connectedPoint!.target;\r\n            }\r\n        }\r\n\r\n        return this._target;\r\n    }\r\n\r\n    public set target(value: NodeMaterialBlockTargets) {\r\n        if ((this._target & value) !== 0) {\r\n            return;\r\n        }\r\n        this._target = value;\r\n    }\r\n\r\n    /**\r\n     * Create a new NodeMaterialTeleportInBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerInput(\"input\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"NodeMaterialTeleportInBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the input component\r\n     */\r\n    public get input(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * @returns a boolean indicating that this connection will be used in the fragment shader\r\n     */\r\n    public isConnectedInFragmentShader() {\r\n        return this.endpoints.some((e) => e.output.isConnectedInFragmentShader);\r\n    }\r\n\r\n    public _dumpCode(uniqueNames: string[], alreadyDumped: NodeMaterialBlock[]) {\r\n        let codeString = super._dumpCode(uniqueNames, alreadyDumped);\r\n\r\n        for (const endpoint of this.endpoints) {\r\n            if (alreadyDumped.indexOf(endpoint) === -1) {\r\n                codeString += endpoint._dumpCode(uniqueNames, alreadyDumped);\r\n            }\r\n        }\r\n\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Checks if the current block is an ancestor of a given block\r\n     * @param block defines the potential descendant block to check\r\n     * @returns true if block is a descendant\r\n     */\r\n    public isAnAncestorOf(block: NodeMaterialBlock): boolean {\r\n        for (const endpoint of this.endpoints) {\r\n            if (endpoint === block) {\r\n                return true;\r\n            }\r\n\r\n            if (endpoint.isAnAncestorOf(block)) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Add an enpoint to this block\r\n     * @param endpoint define the endpoint to attach to\r\n     */\r\n    public attachToEndpoint(endpoint: NodeMaterialTeleportOutBlock) {\r\n        endpoint.detach();\r\n\r\n        this._endpoints.push(endpoint);\r\n        endpoint._entryPoint = this;\r\n        endpoint._outputs[0]._typeConnectionSource = this._inputs[0];\r\n        endpoint._tempEntryPointUniqueId = null;\r\n        endpoint.name = \"> \" + this.name;\r\n    }\r\n\r\n    /**\r\n     * Remove enpoint from this block\r\n     * @param endpoint define the endpoint to remove\r\n     */\r\n    public detachFromEndpoint(endpoint: NodeMaterialTeleportOutBlock) {\r\n        const index = this._endpoints.indexOf(endpoint);\r\n\r\n        if (index !== -1) {\r\n            this._endpoints.splice(index, 1);\r\n            endpoint._outputs[0]._typeConnectionSource = null;\r\n            endpoint._entryPoint = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Release resources\r\n     */\r\n    public dispose() {\r\n        super.dispose();\r\n\r\n        for (const endpoint of this._endpoints) {\r\n            this.detachFromEndpoint(endpoint);\r\n        }\r\n\r\n        this._endpoints = [];\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.NodeMaterialTeleportInBlock\", NodeMaterialTeleportInBlock);\r\n"]}