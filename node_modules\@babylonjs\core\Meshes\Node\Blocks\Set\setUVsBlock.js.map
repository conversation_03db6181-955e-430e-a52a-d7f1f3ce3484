{"version": 3, "file": "setUVsBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Meshes/Node/Blocks/Set/setUVsBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,qCAAqC,EAAE,MAAM,8CAA8C,CAAC;AAKrG,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAEtG;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,iBAAiB;IA2B9C;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QA5BhB;;;WAGG;QAEI,oBAAe,GAAG,IAAI,CAAC;QAE9B;;WAEG;QAYI,2BAAsB,GAAG,CAAC,CAAC;QAS9B,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,qCAAqC,CAAC,QAAQ,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAEzE,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,QAAQ,CAAC,CAAC;IAClF,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,qBAAqB;QACxB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,qBAAqB;QACxB,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,MAAM,IAAI,GAAG,CAAC,KAA6B,EAAE,EAAE;YAC3C,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAEjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,uBAAuB;aACvE;YAED,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE5C,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;gBAClD,KAAK,CAAC,sBAAsB,EAAE,CAAC;gBAC/B,KAAK,CAAC,uBAAuB,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;gBAChC,OAAO;aACV;YAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE;gBACvB,KAAK,CAAC,sBAAsB,EAAE,CAAC;gBAC/B,KAAK,CAAC,uBAAuB,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC;gBAC5C,OAAO;aACV;YAED,MAAM,GAAG,GAAa,EAAE,CAAC;YAEzB,aAAa;YACb,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YAC1D,KAAK,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,IAAI,CAAC,aAAa,GAAG,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE;gBACjF,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAY,CAAC;gBACjE,IAAI,WAAW,EAAE;oBACb,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;iBACpD;aACJ;YAED,QAAQ,IAAI,CAAC,sBAAsB,EAAE;gBACjC,KAAK,CAAC;oBACF,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC;oBAC3B,MAAM;gBACV,KAAK,CAAC;oBACF,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,GAAG,CAAC;oBAC5B,MAAM;gBACV,KAAK,CAAC;oBACF,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,GAAG,CAAC;oBAC5B,MAAM;gBACV,KAAK,CAAC;oBACF,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,GAAG,CAAC;oBAC5B,MAAM;gBACV,KAAK,CAAC;oBACF,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,GAAG,CAAC;oBAC5B,MAAM;gBACV,KAAK,CAAC;oBACF,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,GAAG,CAAC;oBAC5B,MAAM;aACb;YAED,UAAU;YACV,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/B,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,WAAW,CAAC;QAC5B,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;SACtC;aAAM;YACH,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1C;IACL,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,GAAG,GAAG,IAAI,CAAC,iBAAiB,6BAA6B,CAAC;QACtG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,sBAAsB,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC;QAC1G,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC3D,mBAAmB,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAEzE,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB;QACxC,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAExC,IAAI,CAAC,sBAAsB,GAAG,mBAAmB,CAAC,sBAAsB,CAAC;QAEzE,IAAI,mBAAmB,CAAC,eAAe,KAAK,SAAS,EAAE;YACnD,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC,eAAe,CAAC;SAC9D;IACL,CAAC;CACJ;AAxLU;IADN,sBAAsB,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC;oDAC3F;AAgBvB;IAXN,sBAAsB,CAAC,2BAA2B,EAAE,sBAAsB,CAAC,IAAI,EAAE,UAAU,EAAE;QAC1F,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;QAC3B,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;YAC1B,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;YAC1B,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;YAC1B,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;YAC1B,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;YAC1B,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;SAC7B;KACJ,CAAC;2DACgC;AA0KtC,aAAa,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC", "sourcesContent": ["import { NodeGeometry<PERSON>lock } from \"../../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../../nodeGeometryBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"../../Enums/nodeGeometryConnectionPointTypes\";\r\nimport type { NodeGeometryBuildState } from \"../../nodeGeometryBuildState\";\r\nimport type { INodeGeometryExecutionContext } from \"../../Interfaces/nodeGeometryExecutionContext\";\r\nimport type { VertexData } from \"../../../mesh.vertexData\";\r\nimport type { Vector2 } from \"../../../../Maths/math.vector\";\r\nimport { PropertyTypeForEdition, editableInPropertyPage } from \"../../../../Decorators/nodeDecorator\";\r\n\r\n/**\r\n * Block used to set texture coordinates for a geometry\r\n */\r\nexport class SetUVsBlock extends NodeGeometryBlock implements INodeGeometryExecutionContext {\r\n    private _vertexData: VertexData;\r\n    private _currentIndex: number;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that this block can evaluate context\r\n     * Build performance is improved when this value is set to false as the system will cache values instead of reevaluating everything per context change\r\n     */\r\n    @editableInPropertyPage(\"Evaluate context\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { rebuild: true } })\r\n    public evaluateContext = true;\r\n\r\n    /**\r\n     * Gets or sets a value indicating which UV to set\r\n     */\r\n    @editableInPropertyPage(\"Texture coordinates index\", PropertyTypeForEdition.List, \"ADVANCED\", {\r\n        notifiers: { update: true },\r\n        options: [\r\n            { label: \"UV1\", value: 0 },\r\n            { label: \"UV2\", value: 1 },\r\n            { label: \"UV3\", value: 2 },\r\n            { label: \"UV4\", value: 3 },\r\n            { label: \"UV5\", value: 4 },\r\n            { label: \"UV6\", value: 5 },\r\n        ],\r\n    })\r\n    public textureCoordinateIndex = 0;\r\n\r\n    /**\r\n     * Create a new SetUVsBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"geometry\", NodeGeometryBlockConnectionPointTypes.Geometry);\r\n        this.registerInput(\"uvs\", NodeGeometryBlockConnectionPointTypes.Vector2);\r\n\r\n        this.registerOutput(\"output\", NodeGeometryBlockConnectionPointTypes.Geometry);\r\n    }\r\n\r\n    /**\r\n     * Gets the current index in the current flow\r\n     * @returns the current index\r\n     */\r\n    public getExecutionIndex(): number {\r\n        return this._currentIndex;\r\n    }\r\n\r\n    /**\r\n     * Gets the current loop index in the current flow\r\n     * @returns the current loop index\r\n     */\r\n    public getExecutionLoopIndex(): number {\r\n        return this._currentIndex;\r\n    }\r\n\r\n    /**\r\n     * Gets the current face index in the current flow\r\n     * @returns the current face index\r\n     */\r\n    public getExecutionFaceIndex(): number {\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"SetUVsBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the geometry input component\r\n     */\r\n    public get geometry(): NodeGeometryConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the uvs input component\r\n     */\r\n    public get uvs(): NodeGeometryConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the geometry output component\r\n     */\r\n    public get output(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeGeometryBuildState) {\r\n        const func = (state: NodeGeometryBuildState) => {\r\n            state.pushExecutionContext(this);\r\n\r\n            this._vertexData = this.geometry.getConnectedValue(state);\r\n\r\n            if (this._vertexData) {\r\n                this._vertexData = this._vertexData.clone(); // Preserve source data\r\n            }\r\n\r\n            state.pushGeometryContext(this._vertexData);\r\n\r\n            if (!this._vertexData || !this._vertexData.positions) {\r\n                state.restoreGeometryContext();\r\n                state.restoreExecutionContext();\r\n                this.output._storedValue = null;\r\n                return;\r\n            }\r\n\r\n            if (!this.uvs.isConnected) {\r\n                state.restoreGeometryContext();\r\n                state.restoreExecutionContext();\r\n                this.output._storedValue = this._vertexData;\r\n                return;\r\n            }\r\n\r\n            const uvs: number[] = [];\r\n\r\n            // Processing\r\n            const vertexCount = this._vertexData.positions.length / 3;\r\n            for (this._currentIndex = 0; this._currentIndex < vertexCount; this._currentIndex++) {\r\n                const tempVector2 = this.uvs.getConnectedValue(state) as Vector2;\r\n                if (tempVector2) {\r\n                    tempVector2.toArray(uvs, this._currentIndex * 2);\r\n                }\r\n            }\r\n\r\n            switch (this.textureCoordinateIndex) {\r\n                case 0:\r\n                    this._vertexData.uvs = uvs;\r\n                    break;\r\n                case 1:\r\n                    this._vertexData.uvs2 = uvs;\r\n                    break;\r\n                case 2:\r\n                    this._vertexData.uvs3 = uvs;\r\n                    break;\r\n                case 3:\r\n                    this._vertexData.uvs4 = uvs;\r\n                    break;\r\n                case 4:\r\n                    this._vertexData.uvs5 = uvs;\r\n                    break;\r\n                case 5:\r\n                    this._vertexData.uvs6 = uvs;\r\n                    break;\r\n            }\r\n\r\n            // Storage\r\n            state.restoreGeometryContext();\r\n            state.restoreExecutionContext();\r\n            return this._vertexData;\r\n        };\r\n\r\n        if (this.evaluateContext) {\r\n            this.output._storedFunction = func;\r\n        } else {\r\n            this.output._storedFunction = null;\r\n            this.output._storedValue = func(state);\r\n        }\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode() + `${this._codeVariableName}.textureCoordinateIndex};\\n`;\r\n        codeString += `${this._codeVariableName}.evaluateContext = ${this.evaluateContext ? \"true\" : \"false\"};\\n`;\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Serializes this block in a JSON representation\r\n     * @returns the serialized block object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.evaluateContext = this.evaluateContext;\r\n        serializationObject.textureCoordinateIndex = this.textureCoordinateIndex;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any) {\r\n        super._deserialize(serializationObject);\r\n\r\n        this.textureCoordinateIndex = serializationObject.textureCoordinateIndex;\r\n\r\n        if (serializationObject.evaluateContext !== undefined) {\r\n            this.evaluateContext = serializationObject.evaluateContext;\r\n        }\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SetUVsBlock\", SetUVsBlock);\r\n"]}