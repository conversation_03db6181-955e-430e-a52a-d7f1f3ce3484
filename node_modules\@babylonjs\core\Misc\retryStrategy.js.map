{"version": 3, "file": "retryStrategy.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/retryStrategy.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,OAAO,aAAa;IACtB;;;;;OAKG;IACI,MAAM,CAAC,kBAAkB,CAAC,UAAU,GAAG,CAAC,EAAE,YAAY,GAAG,GAAG;QAC/D,OAAO,CAAC,GAAW,EAAE,OAAmB,EAAE,UAAkB,EAAU,EAAE;YACpE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,IAAI,UAAU,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;gBACjF,OAAO,CAAC,CAAC,CAAC;aACb;YAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,YAAY,CAAC;QAClD,CAAC,CAAC;IACN,CAAC;CACJ", "sourcesContent": ["import type { WebRequest } from \"./webRequest\";\r\n\r\n/**\r\n * Class used to define a retry strategy when error happens while loading assets\r\n */\r\nexport class RetryStrategy {\r\n    /**\r\n     * Function used to defines an exponential back off strategy\r\n     * @param maxRetries defines the maximum number of retries (3 by default)\r\n     * @param baseInterval defines the interval between retries\r\n     * @returns the strategy function to use\r\n     */\r\n    public static ExponentialBackoff(maxRetries = 3, baseInterval = 500) {\r\n        return (url: string, request: WebRequest, retryIndex: number): number => {\r\n            if (request.status !== 0 || retryIndex >= maxRetries || url.indexOf(\"file:\") !== -1) {\r\n                return -1;\r\n            }\r\n\r\n            return Math.pow(2, retryIndex) * baseInterval;\r\n        };\r\n    }\r\n}\r\n"]}