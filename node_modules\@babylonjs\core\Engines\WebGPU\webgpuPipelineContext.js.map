{"version": 3, "file": "webgpuPipelineContext.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuPipelineContext.ts"], "names": [], "mappings": "AAOA,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAE9D,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAQhE,gBAAgB;AAChB,MAAM,OAAO,qBAAqB;IA8B9B,IAAW,OAAO;QACd,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAW,OAAO;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAKD,YAAY,uBAAsD,EAAE,MAAoB;QA1BxF,sGAAsG;QAC/F,qBAAgB,GAAqD,EAAE,CAAC;QA0B3E,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,uBAAuB,GAAG,uBAAuB,CAAC;QACvD,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;IACrC,CAAC;IAEM,8BAA8B;QACjC,iCAAiC;IACrC,CAAC;IAEM,sBAAsB,CACzB,MAAc,EACd,mBAA8C,EAC9C,aAAuB,EACvB,QAA2D,EAC3D,WAAqB,EACrB,QAAmC,EACnC,eAAyB,EACzB,UAAoB;QAEpB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,IAAI,MAAM,CAAC,uBAAuB,EAAE;YAChC,MAAM,CAAC,mBAAmB,GAAG,EAAE,CAAC;YAChC,MAAM,CAAC,iBAAiB,GAAG,EAAE,CAAC;SACjC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC;QACrE,IAAI,KAAa,CAAC;QAClB,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACjD,MAAM,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;YAChC,MAAM,OAAO,GAAG,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;YAElD,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,IAAI,SAAS,EAAE;gBACzC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC7B,KAAK,EAAE,CAAC;aACX;iBAAM;gBACH,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;aAC1B;SACJ;QAED,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,eAAe,CAAC,EAAE;YAC5D,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzB;QAED,uDAAuD;QACvD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,MAAM,wBAAwB,GAAa,EAAE,CAAC;QAC9C,MAAM,4BAA4B,GAAa,EAAE,CAAC;QAClD,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,QAAQ,IAAI,CAAC,EAAE;gBACf,wBAAwB,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtD,4BAA4B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC/C;SACJ;QACD,IAAI,CAAC,uBAAuB,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;QACjF,IAAI,CAAC,uBAAuB,CAAC,4BAA4B,GAAG,4BAA4B,CAAC;IAC7F,CAAC;IAED,gBAAgB;IAChB;;OAEG;IACI,kBAAkB;QACrB,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,MAAM,EAAE;YACvD,OAAO;SACV;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAEpG,KAAK,MAAM,eAAe,IAAI,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE;YACzE,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;YAClE,MAAM,IAAI,GAAG,qBAAqB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YACtD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;YAClF,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC;SAC7E;QAED,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;IAChC,CAAC;IAED;;QAEI;IACG,OAAO;QACV,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;SAChC;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,WAAmB,EAAE,KAAa;QAC5C,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS;QACpD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;OAMG;IACI,OAAO,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC/D,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;;OAOG;IACI,OAAO,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC1E,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,WAAmB,EAAE,KAAiB;QACrD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAC1D,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAmB,EAAE,KAAiB;QACtD,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAmB,EAAE,KAAiB;QACtD,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAmB,EAAE,KAAiB;QACtD,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,WAAmB,EAAE,KAAa;QAC7C,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACtD,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS;QACrD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;;;;;OAMG;IACI,QAAQ,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAChE,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;;;;;;OAOG;IACI,QAAQ,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC3E,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAmB,EAAE,KAAkB;QACvD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,WAAmB,EAAE,KAAkB;QACxD,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,WAAmB,EAAE,KAAkB;QACxD,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,WAAmB,EAAE,KAAkB;QACxD,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,WAAmB,EAAE,KAAe;QAChD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACvD,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,WAAmB,EAAE,KAAe;QACjD,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,WAAmB,EAAE,KAAe;QACjD,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,WAAmB,EAAE,KAAe;QACjD,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,WAAmB,EAAE,QAAsB;QAC1D,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,WAAmB,EAAE,MAAmB;QACrD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IACzD,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAmB,EAAE,MAAoB;QACzD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAmB,EAAE,MAAoB;QACzD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,WAAmB,EAAE,KAAa;QAC9C,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACvD,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,WAAmB,EAAE,OAAqB;QACxD,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS;QACtD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACvD,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,WAAmB,EAAE,OAAqB;QACxD,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACjE,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,WAAmB,EAAE,OAAqB;QACxD,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,WAAmB,EAAE,UAA2B;QACjE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC;IAED;;;;;;;OAOG;IACI,SAAS,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC5E,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,WAAmB,EAAE,MAAmB;QACrD,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,WAAmB,EAAE,MAAmB,EAAE,KAAa;QACpE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACrE,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,WAAmB,EAAE,MAAmB;QAC3D,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC;IAEM,oBAAoB;QACvB,OAAO,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC;IAChC,CAAC;IAEM,sBAAsB;QACzB,OAAO,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC;IAClC,CAAC;CACJ", "sourcesContent": ["/* eslint-disable baby<PERSON>js/available */\r\n/* eslint-disable jsdoc/require-jsdoc */\r\nimport type { IPipelineContext } from \"../IPipelineContext\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { WebGPUEngine } from \"../webgpuEngine\";\r\nimport type { Effect } from \"../../Materials/effect\";\r\nimport type { WebGPUShaderProcessingContext } from \"./webgpuShaderProcessingContext\";\r\nimport { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport type { IMatrixLike, IVector2Like, IVector3Like, IVector4Like, IColor3Like, IColor4Like, IQuaternionLike } from \"../../Maths/math.like\";\r\nimport { WebGPUShaderProcessor } from \"./webgpuShaderProcessor\";\r\n\r\n/** @internal */\r\nexport interface IWebGPURenderPipelineStageDescriptor {\r\n    vertexStage: GPUProgrammableStage;\r\n    fragmentStage?: GPUProgrammableStage;\r\n}\r\n\r\n/** @internal */\r\nexport class WebGPUPipelineContext implements IPipelineContext {\r\n    public engine: WebGPUEngine;\r\n\r\n    public shaderProcessingContext: WebGPUShaderProcessingContext;\r\n\r\n    protected _leftOverUniformsByName: { [name: string]: string };\r\n\r\n    // Property used to handle vertex buffers with int values when the shader code expect float values.\r\n    public vertexBufferKindToType: { [kind: string]: number };\r\n\r\n    public sources: {\r\n        vertex: string;\r\n        fragment: string;\r\n        rawVertex: string;\r\n        rawFragment: string;\r\n    };\r\n\r\n    public stages: Nullable<IWebGPURenderPipelineStageDescriptor>;\r\n\r\n    // The field is indexed by textureState. See @WebGPUMaterialContext.textureState for more information.\r\n    public bindGroupLayouts: { [textureState: number]: GPUBindGroupLayout[] } = {};\r\n\r\n    /**\r\n     * Stores the left-over uniform buffer\r\n     */\r\n    public uniformBuffer: Nullable<UniformBuffer>;\r\n\r\n    // Default implementation.\r\n    public onCompiled?: () => void;\r\n\r\n    public get isAsync() {\r\n        return false;\r\n    }\r\n\r\n    public get isReady(): boolean {\r\n        if (this.stages) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /** @internal */\r\n    public _name: string;\r\n\r\n    constructor(shaderProcessingContext: WebGPUShaderProcessingContext, engine: WebGPUEngine) {\r\n        this._name = \"unnamed\";\r\n        this.shaderProcessingContext = shaderProcessingContext;\r\n        this._leftOverUniformsByName = {};\r\n        this.engine = engine;\r\n        this.vertexBufferKindToType = {};\r\n    }\r\n\r\n    public _handlesSpectorRebuildCallback(): void {\r\n        // Nothing to do yet for spector.\r\n    }\r\n\r\n    public _fillEffectInformation(\r\n        effect: Effect,\r\n        uniformBuffersNames: { [key: string]: number },\r\n        uniformsNames: string[],\r\n        uniforms: { [key: string]: Nullable<WebGLUniformLocation> },\r\n        samplerList: string[],\r\n        samplers: { [key: string]: number },\r\n        attributesNames: string[],\r\n        attributes: number[]\r\n    ) {\r\n        const engine = this.engine;\r\n\r\n        if (engine._doNotHandleContextLost) {\r\n            effect._fragmentSourceCode = \"\";\r\n            effect._vertexSourceCode = \"\";\r\n        }\r\n\r\n        const foundSamplers = this.shaderProcessingContext.availableTextures;\r\n        let index: number;\r\n        for (index = 0; index < samplerList.length; index++) {\r\n            const name = samplerList[index];\r\n            const sampler = foundSamplers[samplerList[index]];\r\n\r\n            if (sampler == null || sampler == undefined) {\r\n                samplerList.splice(index, 1);\r\n                index--;\r\n            } else {\r\n                samplers[name] = index;\r\n            }\r\n        }\r\n\r\n        for (const attr of engine.getAttributes(this, attributesNames)) {\r\n            attributes.push(attr);\r\n        }\r\n\r\n        // Build the uniform layout for the left over uniforms.\r\n        this.buildUniformLayout();\r\n\r\n        const attributeNamesFromEffect: string[] = [];\r\n        const attributeLocationsFromEffect: number[] = [];\r\n        for (index = 0; index < attributesNames.length; index++) {\r\n            const location = attributes[index];\r\n            if (location >= 0) {\r\n                attributeNamesFromEffect.push(attributesNames[index]);\r\n                attributeLocationsFromEffect.push(location);\r\n            }\r\n        }\r\n        this.shaderProcessingContext.attributeNamesFromEffect = attributeNamesFromEffect;\r\n        this.shaderProcessingContext.attributeLocationsFromEffect = attributeLocationsFromEffect;\r\n    }\r\n\r\n    /** @internal */\r\n    /**\r\n     * Build the uniform buffer used in the material.\r\n     */\r\n    public buildUniformLayout(): void {\r\n        if (!this.shaderProcessingContext.leftOverUniforms.length) {\r\n            return;\r\n        }\r\n\r\n        this.uniformBuffer = new UniformBuffer(this.engine, undefined, undefined, \"leftOver-\" + this._name);\r\n\r\n        for (const leftOverUniform of this.shaderProcessingContext.leftOverUniforms) {\r\n            const type = leftOverUniform.type.replace(/^(.*?)(<.*>)?$/, \"$1\");\r\n            const size = WebGPUShaderProcessor.UniformSizes[type];\r\n            this.uniformBuffer.addUniform(leftOverUniform.name, size, leftOverUniform.length);\r\n            this._leftOverUniformsByName[leftOverUniform.name] = leftOverUniform.type;\r\n        }\r\n\r\n        this.uniformBuffer.create();\r\n    }\r\n\r\n    /**\r\n     * Release all associated resources.\r\n     **/\r\n    public dispose() {\r\n        if (this.uniformBuffer) {\r\n            this.uniformBuffer.dispose();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets an integer value on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param value Value to be set.\r\n     */\r\n    public setInt(uniformName: string, value: number): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateInt(uniformName, value);\r\n    }\r\n\r\n    /**\r\n     * Sets an int2 value on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First int in int2.\r\n     * @param y Second int in int2.\r\n     */\r\n    public setInt2(uniformName: string, x: number, y: number): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateInt2(uniformName, x, y);\r\n    }\r\n\r\n    /**\r\n     * Sets an int3 value on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First int in int3.\r\n     * @param y Second int in int3.\r\n     * @param z Third int in int3.\r\n     */\r\n    public setInt3(uniformName: string, x: number, y: number, z: number): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateInt3(uniformName, x, y, z);\r\n    }\r\n\r\n    /**\r\n     * Sets an int4 value on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First int in int4.\r\n     * @param y Second int in int4.\r\n     * @param z Third int in int4.\r\n     * @param w Fourth int in int4.\r\n     */\r\n    public setInt4(uniformName: string, x: number, y: number, z: number, w: number): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateInt4(uniformName, x, y, z, w);\r\n    }\r\n\r\n    /**\r\n     * Sets an int array on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setIntArray(uniformName: string, array: Int32Array): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateIntArray(uniformName, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an int array 2 on a uniform variable. (Array is specified as single array eg. [1,2,3,4] will result in [[1,2],[3,4]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setIntArray2(uniformName: string, array: Int32Array): void {\r\n        this.setIntArray(uniformName, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an int array 3 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6] will result in [[1,2,3],[4,5,6]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setIntArray3(uniformName: string, array: Int32Array): void {\r\n        this.setIntArray(uniformName, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an int array 4 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6,7,8] will result in [[1,2,3,4],[5,6,7,8]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setIntArray4(uniformName: string, array: Int32Array): void {\r\n        this.setIntArray(uniformName, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an unsigned integer value on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param value Value to be set.\r\n     */\r\n    public setUInt(uniformName: string, value: number): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateUInt(uniformName, value);\r\n    }\r\n\r\n    /**\r\n     * Sets an unsigned int2 value on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First unsigned int in uint2.\r\n     * @param y Second unsigned int in uint2.\r\n     */\r\n    public setUInt2(uniformName: string, x: number, y: number): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateUInt2(uniformName, x, y);\r\n    }\r\n\r\n    /**\r\n     * Sets an unsigned int3 value on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First unsigned int in uint3.\r\n     * @param y Second unsigned int in uint3.\r\n     * @param z Third unsigned int in uint3.\r\n     */\r\n    public setUInt3(uniformName: string, x: number, y: number, z: number): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateUInt3(uniformName, x, y, z);\r\n    }\r\n\r\n    /**\r\n     * Sets an unsigned int4 value on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First unsigned int in uint4.\r\n     * @param y Second unsigned int in uint4.\r\n     * @param z Third unsigned int in uint4.\r\n     * @param w Fourth unsigned int in uint4.\r\n     */\r\n    public setUInt4(uniformName: string, x: number, y: number, z: number, w: number): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateUInt4(uniformName, x, y, z, w);\r\n    }\r\n\r\n    /**\r\n     * Sets an unsigned int array on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setUIntArray(uniformName: string, array: Uint32Array): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateUIntArray(uniformName, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an unsigned int array 2 on a uniform variable. (Array is specified as single array eg. [1,2,3,4] will result in [[1,2],[3,4]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setUIntArray2(uniformName: string, array: Uint32Array): void {\r\n        this.setUIntArray(uniformName, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an unsigned int array 3 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6] will result in [[1,2,3],[4,5,6]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setUIntArray3(uniformName: string, array: Uint32Array): void {\r\n        this.setUIntArray(uniformName, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an unsigned int array 4 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6,7,8] will result in [[1,2,3,4],[5,6,7,8]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setUIntArray4(uniformName: string, array: Uint32Array): void {\r\n        this.setUIntArray(uniformName, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an array on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setArray(uniformName: string, array: number[]): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateArray(uniformName, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an array 2 on a uniform variable. (Array is specified as single array eg. [1,2,3,4] will result in [[1,2],[3,4]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setArray2(uniformName: string, array: number[]): void {\r\n        this.setArray(uniformName, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an array 3 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6] will result in [[1,2,3],[4,5,6]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setArray3(uniformName: string, array: number[]): void {\r\n        this.setArray(uniformName, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an array 4 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6,7,8] will result in [[1,2,3,4],[5,6,7,8]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setArray4(uniformName: string, array: number[]): void {\r\n        this.setArray(uniformName, array);\r\n    }\r\n\r\n    /**\r\n     * Sets matrices on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param matrices matrices to be set.\r\n     */\r\n    public setMatrices(uniformName: string, matrices: Float32Array): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateMatrices(uniformName, matrices);\r\n    }\r\n\r\n    /**\r\n     * Sets matrix on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param matrix matrix to be set.\r\n     */\r\n    public setMatrix(uniformName: string, matrix: IMatrixLike): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateMatrix(uniformName, matrix);\r\n    }\r\n\r\n    /**\r\n     * Sets a 3x3 matrix on a uniform variable. (Specified as [1,2,3,4,5,6,7,8,9] will result in [1,2,3][4,5,6][7,8,9] matrix)\r\n     * @param uniformName Name of the variable.\r\n     * @param matrix matrix to be set.\r\n     */\r\n    public setMatrix3x3(uniformName: string, matrix: Float32Array): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateMatrix3x3(uniformName, matrix);\r\n    }\r\n\r\n    /**\r\n     * Sets a 2x2 matrix on a uniform variable. (Specified as [1,2,3,4] will result in [1,2][3,4] matrix)\r\n     * @param uniformName Name of the variable.\r\n     * @param matrix matrix to be set.\r\n     */\r\n    public setMatrix2x2(uniformName: string, matrix: Float32Array): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateMatrix2x2(uniformName, matrix);\r\n    }\r\n\r\n    /**\r\n     * Sets a float on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param value value to be set.\r\n     */\r\n    public setFloat(uniformName: string, value: number): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateFloat(uniformName, value);\r\n    }\r\n\r\n    /**\r\n     * Sets a Vector2 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param vector2 vector2 to be set.\r\n     */\r\n    public setVector2(uniformName: string, vector2: IVector2Like): void {\r\n        this.setFloat2(uniformName, vector2.x, vector2.y);\r\n    }\r\n\r\n    /**\r\n     * Sets a float2 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First float in float2.\r\n     * @param y Second float in float2.\r\n     */\r\n    public setFloat2(uniformName: string, x: number, y: number): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateFloat2(uniformName, x, y);\r\n    }\r\n\r\n    /**\r\n     * Sets a Vector3 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param vector3 Value to be set.\r\n     */\r\n    public setVector3(uniformName: string, vector3: IVector3Like): void {\r\n        this.setFloat3(uniformName, vector3.x, vector3.y, vector3.z);\r\n    }\r\n\r\n    /**\r\n     * Sets a float3 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First float in float3.\r\n     * @param y Second float in float3.\r\n     * @param z Third float in float3.\r\n     */\r\n    public setFloat3(uniformName: string, x: number, y: number, z: number): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateFloat3(uniformName, x, y, z);\r\n    }\r\n\r\n    /**\r\n     * Sets a Vector4 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param vector4 Value to be set.\r\n     */\r\n    public setVector4(uniformName: string, vector4: IVector4Like): void {\r\n        this.setFloat4(uniformName, vector4.x, vector4.y, vector4.z, vector4.w);\r\n    }\r\n\r\n    /**\r\n     * Sets a Quaternion on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param quaternion Value to be set.\r\n     */\r\n    public setQuaternion(uniformName: string, quaternion: IQuaternionLike): void {\r\n        this.setFloat4(uniformName, quaternion.x, quaternion.y, quaternion.z, quaternion.w);\r\n    }\r\n\r\n    /**\r\n     * Sets a float4 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First float in float4.\r\n     * @param y Second float in float4.\r\n     * @param z Third float in float4.\r\n     * @param w Fourth float in float4.\r\n     */\r\n    public setFloat4(uniformName: string, x: number, y: number, z: number, w: number): void {\r\n        if (!this.uniformBuffer || !this._leftOverUniformsByName[uniformName]) {\r\n            return;\r\n        }\r\n        this.uniformBuffer.updateFloat4(uniformName, x, y, z, w);\r\n    }\r\n\r\n    /**\r\n     * Sets a Color3 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param color3 Value to be set.\r\n     */\r\n    public setColor3(uniformName: string, color3: IColor3Like): void {\r\n        this.setFloat3(uniformName, color3.r, color3.g, color3.b);\r\n    }\r\n\r\n    /**\r\n     * Sets a Color4 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param color3 Value to be set.\r\n     * @param alpha Alpha value to be set.\r\n     */\r\n    public setColor4(uniformName: string, color3: IColor3Like, alpha: number): void {\r\n        this.setFloat4(uniformName, color3.r, color3.g, color3.b, alpha);\r\n    }\r\n\r\n    /**\r\n     * Sets a Color4 on a uniform variable\r\n     * @param uniformName defines the name of the variable\r\n     * @param color4 defines the value to be set\r\n     */\r\n    public setDirectColor4(uniformName: string, color4: IColor4Like): void {\r\n        this.setFloat4(uniformName, color4.r, color4.g, color4.b, color4.a);\r\n    }\r\n\r\n    public _getVertexShaderCode(): string | null {\r\n        return this.sources?.vertex;\r\n    }\r\n\r\n    public _getFragmentShaderCode(): string | null {\r\n        return this.sources?.fragment;\r\n    }\r\n}\r\n"]}