{"version": 3, "file": "webgpuClearQuad.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuClearQuad.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAGzC,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAEhF,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAChF,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,8BAA8B,EAAE,MAAM,wBAAwB,CAAC;AAExE,OAAO,gCAAgC,CAAC;AACxC,OAAO,kCAAkC,CAAC;AAE1C,gBAAgB;AAChB,MAAM,OAAO,eAAe;IAUjB,qBAAqB,CAAC,MAAoC;QAC7D,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC;QAClC,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IAEM,cAAc,CAAC,MAA+B;QACjD,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAEM,iBAAiB,CAAC,WAAqB,EAAE,YAA+B,EAAE,YAAoB;QACjG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QAC7D,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;IAC7D,CAAC;IAED,YAAY,MAAiB,EAAE,MAAoB,EAAE,iBAA+B;QAnB5E,gBAAW,GAAqC,EAAE,CAAC;QAEnD,iBAAY,GAAuC,EAAE,CAAC;QACtD,aAAQ,GAAa,EAAE,CAAC;QAiB5B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,CAAC,oBAAoB,GAAG,IAAI,6BAA6B,CAAC,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QAC/F,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAEnD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE,EAAE,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;IACjF,CAAC;IAEM,KAAK,CACR,UAA0C,EAC1C,UAAkC,EAClC,UAAoB,EACpB,YAAsB,EACtB,WAAW,GAAG,CAAC;QAEf,IAAI,WAA0D,CAAC;QAC/D,IAAI,MAAM,GAA8B,IAAI,CAAC;QAC7C,IAAI,SAAiB,CAAC;QAEtB,MAAM,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;QAEtD,IAAI,UAAU,EAAE;YACZ,WAAW,GAAG,UAAU,CAAC;SAC5B;aAAM;YACH,IAAI,GAAG,GAAG,CAAC,CAAC;YACZ,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACpE,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,8BAA8B,CAAC,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;aAC1G;YAED,MAAM,uBAAuB,GAAG,8BAA8B,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC,CAAC;YAE9F,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACd,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChH,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1B,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5B,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClD,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzB,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/B,uBAAuB,GAAG,CAAC,IAAI,EAAE,CAAC;YAEtC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAEtC,IAAI,MAAM,EAAE;gBACR,OAAO,MAAM,CAAC;aACjB;YAED,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC;gBACjD,YAAY,EAAE,IAAI,CAAC,oBAAoB,CAAC,YAAY;gBACpD,kBAAkB,EAAE,IAAI,CAAC,mBAAmB;gBAC5C,WAAW,EAAE,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC;aAC1D,CAAC,CAAC;SACN;QAED,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QAC7D,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,IAAI,mBAAmB,CAAC,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAC5J,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvE,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC/F,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9F,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7D,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,SAAS,CAAC,8BAA8B,EAAE,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAElI,MAAM,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAyC,CAAC;QAErF,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;SACrD;QAED,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAE/I,qBAAqB,CAAC,aAAc,CAAC,MAAM,EAAE,CAAC;QAE9C,MAAM,eAAe,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QAC1F,MAAM,cAAc,GAAG,qBAAqB,CAAC,aAAc,CAAC,SAAS,EAAsB,CAAC;QAE5F,MAAM,GAAG,GAAG,cAAc,CAAC,QAAQ,GAAG,GAAG,GAAG,eAAe,CAAC,QAAQ,CAAC;QAErE,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAEvC,IAAI,CAAC,UAAU,EAAE;YACb,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACnE,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YACxC,UAAU,CAAC,IAAI,CACX,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;gBACzB,MAAM,EAAE,gBAAgB,CAAC,CAAC,CAAC;gBAC3B,OAAO,EAAE,EAAE;aACd,CAAC,CACL,CAAC;YACF,IAAI,CAAC,6BAA6B,CAAC,wBAAwB,EAAE;gBACzD,UAAU,CAAC,IAAI,CACX,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;oBACzB,MAAM,EAAE,gBAAgB,CAAC,CAAC,CAAC;oBAC3B,OAAO,EAAE,EAAE;iBACd,CAAC,CACL,CAAC;aACL;YACD,UAAU,CAAC,IAAI,CACX,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;gBACzB,MAAM,EAAE,gBAAgB,CAAC,6BAA6B,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxF,OAAO,EAAE;oBACL;wBACI,OAAO,EAAE,CAAC;wBACV,QAAQ,EAAE;4BACN,MAAM,EAAE,eAAe,CAAC,kBAAkB;4BAC1C,IAAI,EAAE,eAAe,CAAC,QAAQ;yBACjC;qBACJ;oBACD;wBACI,OAAO,EAAE,CAAC;wBACV,QAAQ,EAAE;4BACN,MAAM,EAAE,cAAc,CAAC,kBAAkB;4BACzC,IAAI,EAAE,cAAc,CAAC,QAAQ;yBAChC;qBACJ;iBACJ;aACJ,CAAC,CACL,CAAC;SACL;QAED,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACxC,WAAW,CAAC,YAAY,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9C;QACD,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7B,IAAI,CAAC,UAAU,EAAE;YACb,MAAM,GAAI,WAAsC,CAAC,MAAM,EAAE,CAAC;YAC1D,IAAI,CAAC,YAAY,CAAC,SAAU,CAAC,GAAG,MAAM,CAAC;SAC1C;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ", "sourcesContent": ["import type { Effect } from \"../../Materials/effect\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport type { IColor4Like } from \"../../Maths/math.like\";\r\nimport type { VertexBuffer } from \"../../Buffers/buffer\";\r\nimport type { WebGPUDataBuffer } from \"../../Meshes/WebGPU/webgpuDataBuffer\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Constants } from \"../constants\";\r\nimport type { WebGPUEngine } from \"../webgpuEngine\";\r\nimport type { WebGPUCacheRenderPipeline } from \"./webgpuCacheRenderPipeline\";\r\nimport { WebGPUCacheRenderPipelineTree } from \"./webgpuCacheRenderPipelineTree\";\r\nimport type { WebGPUPipelineContext } from \"./webgpuPipelineContext\";\r\nimport { WebGPUShaderProcessingContext } from \"./webgpuShaderProcessingContext\";\r\nimport { WebGPUTextureHelper } from \"./webgpuTextureHelper\";\r\nimport { renderableTextureFormatToIndex } from \"./webgpuTextureManager\";\r\n\r\nimport \"../../Shaders/clearQuad.vertex\";\r\nimport \"../../Shaders/clearQuad.fragment\";\r\n\r\n/** @internal */\r\nexport class WebGPUClearQuad {\r\n    private _device: GPUDevice;\r\n    private _engine: WebGPUEngine;\r\n    private _cacheRenderPipeline: WebGPUCacheRenderPipeline;\r\n    private _effect: Effect;\r\n    private _bindGroups: { [id: string]: GPUBindGroup[] } = {};\r\n    private _depthTextureFormat: GPUTextureFormat | undefined;\r\n    private _bundleCache: { [key: string]: GPURenderBundle } = {};\r\n    private _keyTemp: number[] = [];\r\n\r\n    public setDepthStencilFormat(format: GPUTextureFormat | undefined): void {\r\n        this._depthTextureFormat = format;\r\n        this._cacheRenderPipeline.setDepthStencilFormat(format);\r\n    }\r\n\r\n    public setColorFormat(format: GPUTextureFormat | null): void {\r\n        this._cacheRenderPipeline.setColorFormat(format);\r\n    }\r\n\r\n    public setMRTAttachments(attachments: number[], textureArray: InternalTexture[], textureCount: number): void {\r\n        this._cacheRenderPipeline.setMRT(textureArray, textureCount);\r\n        this._cacheRenderPipeline.setMRTAttachments(attachments);\r\n    }\r\n\r\n    constructor(device: GPUDevice, engine: WebGPUEngine, emptyVertexBuffer: VertexBuffer) {\r\n        this._device = device;\r\n        this._engine = engine;\r\n\r\n        this._cacheRenderPipeline = new WebGPUCacheRenderPipelineTree(this._device, emptyVertexBuffer);\r\n        this._cacheRenderPipeline.setDepthTestEnabled(false);\r\n        this._cacheRenderPipeline.setStencilReadMask(0xff);\r\n\r\n        this._effect = engine.createEffect(\"clearQuad\", [], [\"color\", \"depthValue\"]);\r\n    }\r\n\r\n    public clear(\r\n        renderPass: Nullable<GPURenderPassEncoder>,\r\n        clearColor?: Nullable<IColor4Like>,\r\n        clearDepth?: boolean,\r\n        clearStencil?: boolean,\r\n        sampleCount = 1\r\n    ): Nullable<GPURenderBundle> {\r\n        let renderPass2: GPURenderPassEncoder | GPURenderBundleEncoder;\r\n        let bundle: Nullable<GPURenderBundle> = null;\r\n        let bundleKey: string;\r\n\r\n        const isRTTPass = !!this._engine._currentRenderTarget;\r\n\r\n        if (renderPass) {\r\n            renderPass2 = renderPass;\r\n        } else {\r\n            let idx = 0;\r\n            this._keyTemp.length = 0;\r\n            for (let i = 0; i < this._cacheRenderPipeline.colorFormats.length; ++i) {\r\n                this._keyTemp[idx++] = renderableTextureFormatToIndex[this._cacheRenderPipeline.colorFormats[i] ?? \"\"];\r\n            }\r\n\r\n            const depthStencilFormatIndex = renderableTextureFormatToIndex[this._depthTextureFormat ?? 0];\r\n\r\n            this._keyTemp[idx] =\r\n                (clearColor ? clearColor.r + clearColor.g * 256 + clearColor.b * 256 * 256 + clearColor.a * 256 * 256 * 256 : 0) +\r\n                (clearDepth ? 2 ** 32 : 0) +\r\n                (clearStencil ? 2 ** 33 : 0) +\r\n                (this._engine.useReverseDepthBuffer ? 2 ** 34 : 0) +\r\n                (isRTTPass ? 2 ** 35 : 0) +\r\n                (sampleCount > 1 ? 2 ** 36 : 0) +\r\n                depthStencilFormatIndex * 2 ** 37;\r\n\r\n            bundleKey = this._keyTemp.join(\"_\");\r\n            bundle = this._bundleCache[bundleKey];\r\n\r\n            if (bundle) {\r\n                return bundle;\r\n            }\r\n\r\n            renderPass2 = this._device.createRenderBundleEncoder({\r\n                colorFormats: this._cacheRenderPipeline.colorFormats,\r\n                depthStencilFormat: this._depthTextureFormat,\r\n                sampleCount: WebGPUTextureHelper.GetSample(sampleCount),\r\n            });\r\n        }\r\n\r\n        this._cacheRenderPipeline.setDepthWriteEnabled(!!clearDepth);\r\n        this._cacheRenderPipeline.setStencilEnabled(!!clearStencil && !!this._depthTextureFormat && WebGPUTextureHelper.HasStencilAspect(this._depthTextureFormat));\r\n        this._cacheRenderPipeline.setStencilWriteMask(clearStencil ? 0xff : 0);\r\n        this._cacheRenderPipeline.setStencilCompare(clearStencil ? Constants.ALWAYS : Constants.NEVER);\r\n        this._cacheRenderPipeline.setStencilPassOp(clearStencil ? Constants.REPLACE : Constants.KEEP);\r\n        this._cacheRenderPipeline.setWriteMask(clearColor ? 0xf : 0);\r\n\r\n        const pipeline = this._cacheRenderPipeline.getRenderPipeline(Constants.MATERIAL_TriangleStripDrawMode, this._effect, sampleCount);\r\n\r\n        const webgpuPipelineContext = this._effect._pipelineContext as WebGPUPipelineContext;\r\n\r\n        if (clearColor) {\r\n            this._effect.setDirectColor4(\"color\", clearColor);\r\n        }\r\n\r\n        this._effect.setFloat(\"depthValue\", this._engine.useReverseDepthBuffer ? this._engine._clearReverseDepthValue : this._engine._clearDepthValue);\r\n\r\n        webgpuPipelineContext.uniformBuffer!.update();\r\n\r\n        const bufferInternals = isRTTPass ? this._engine._ubInvertY : this._engine._ubDontInvertY;\r\n        const bufferLeftOver = webgpuPipelineContext.uniformBuffer!.getBuffer() as WebGPUDataBuffer;\r\n\r\n        const key = bufferLeftOver.uniqueId + \"-\" + bufferInternals.uniqueId;\r\n\r\n        let bindGroups = this._bindGroups[key];\r\n\r\n        if (!bindGroups) {\r\n            const bindGroupLayouts = webgpuPipelineContext.bindGroupLayouts[0];\r\n            bindGroups = this._bindGroups[key] = [];\r\n            bindGroups.push(\r\n                this._device.createBindGroup({\r\n                    layout: bindGroupLayouts[0],\r\n                    entries: [],\r\n                })\r\n            );\r\n            if (!WebGPUShaderProcessingContext._SimplifiedKnownBindings) {\r\n                bindGroups.push(\r\n                    this._device.createBindGroup({\r\n                        layout: bindGroupLayouts[1],\r\n                        entries: [],\r\n                    })\r\n                );\r\n            }\r\n            bindGroups.push(\r\n                this._device.createBindGroup({\r\n                    layout: bindGroupLayouts[WebGPUShaderProcessingContext._SimplifiedKnownBindings ? 1 : 2],\r\n                    entries: [\r\n                        {\r\n                            binding: 0,\r\n                            resource: {\r\n                                buffer: bufferInternals.underlyingResource,\r\n                                size: bufferInternals.capacity,\r\n                            },\r\n                        },\r\n                        {\r\n                            binding: 1,\r\n                            resource: {\r\n                                buffer: bufferLeftOver.underlyingResource,\r\n                                size: bufferLeftOver.capacity,\r\n                            },\r\n                        },\r\n                    ],\r\n                })\r\n            );\r\n        }\r\n\r\n        renderPass2.setPipeline(pipeline);\r\n        for (let i = 0; i < bindGroups.length; ++i) {\r\n            renderPass2.setBindGroup(i, bindGroups[i]);\r\n        }\r\n        renderPass2.draw(4, 1, 0, 0);\r\n\r\n        if (!renderPass) {\r\n            bundle = (renderPass2 as GPURenderBundleEncoder).finish();\r\n            this._bundleCache[bundleKey!] = bundle;\r\n        }\r\n\r\n        return bundle;\r\n    }\r\n}\r\n"]}