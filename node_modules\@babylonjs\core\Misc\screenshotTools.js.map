{"version": 3, "file": "screenshotTools.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/screenshotTools.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,mBAAmB,EAAE,MAAM,2CAA2C,CAAC;AAChF,OAAO,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AACnE,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,OAAO,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAIlD,IAAI,gBAAgB,GAAgC,IAAI,CAAC;AAEzD;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,UAAU,gBAAgB,CAC5B,MAAc,EACd,MAAc,EACd,IAA8B,EAC9B,eAAwC,EACxC,QAAQ,GAAG,WAAW,EACtB,aAAa,GAAG,KAAK,EACrB,OAAgB;IAEhB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAEnE,IAAI,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,EAAE;QACpB,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAC3C,OAAO;KACV;IAED,IAAI,CAAC,gBAAgB,EAAE;QACnB,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;KACvD;IAED,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC;IAC/B,gBAAgB,CAAC,MAAM,GAAG,MAAM,CAAC;IAEjC,MAAM,aAAa,GAAG,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAExD,MAAM,KAAK,GAAG,MAAM,CAAC,cAAc,EAAE,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;IACjE,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,SAAS,GAAG,QAAQ,GAAG,KAAK,CAAC;IACjC,IAAI,SAAS,GAAG,MAAM,EAAE;QACpB,SAAS,GAAG,MAAM,CAAC;QACnB,QAAQ,GAAG,SAAS,GAAG,KAAK,CAAC;KAChC;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;IAClD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IAEpD,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;IAChC,IAAI,KAAK,CAAC,YAAY,KAAK,MAAM,EAAE;QAC/B,iCAAiC,CAC7B,MAAM,EACN,MAAM,EACN,IAAI,EACJ,CAAC,IAAI,EAAE,EAAE;YACL,IAAI,aAAa,EAAE;gBACf,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC9B,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBACzB,IAAI,eAAe,EAAE;oBACjB,eAAe,CAAC,EAAE,CAAC,CAAC;iBACvB;aACJ;iBAAM,IAAI,eAAe,EAAE;gBACxB,eAAe,CAAC,IAAI,CAAC,CAAC;aACzB;QACL,CAAC,EACD,QAAQ,EACR,GAAG,EACH,MAAM,CAAC,kBAAkB,EAAE,CAAC,SAAS,EACrC,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,OAAO,CACV,CAAC;KACL;SAAM;QACH,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,GAAG,EAAE;YACrC,MAAM,eAAe,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAC;YACpD,IAAI,aAAa,IAAI,eAAe,EAAE;gBAClC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;aACnF;YAED,IAAI,gBAAgB,EAAE;gBAClB,IAAI,aAAa,EAAE;oBACf,KAAK,CAAC,0BAA0B,CAAC,gBAAgB,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;oBAC5F,IAAI,eAAe,EAAE;wBACjB,eAAe,CAAC,EAAE,CAAC,CAAC;qBACvB;iBACJ;qBAAM;oBACH,KAAK,CAAC,0BAA0B,CAAC,gBAAgB,EAAE,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;iBACrG;aACJ;QACL,CAAC,CAAC,CAAC;KACN;AACL,CAAC;AAED;;;;;;;;;;;;;;;GAeG;AACH,MAAM,UAAU,qBAAqB,CAAC,MAAc,EAAE,MAAc,EAAE,IAA8B,EAAE,QAAQ,GAAG,WAAW,EAAE,OAAgB;IAC1I,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACnC,gBAAgB,CACZ,MAAM,EACN,MAAM,EACN,IAAI,EACJ,CAAC,IAAI,EAAE,EAAE;YACL,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;gBAC7B,OAAO,CAAC,IAAI,CAAC,CAAC;aACjB;iBAAM;gBACH,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;aAC1C;QACL,CAAC,EACD,QAAQ,EACR,SAAS,EACT,OAAO,CACV,CAAC;IACN,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,+BAA+B,CAAC,MAAc,EAAE,MAAc,EAAE,KAAa,EAAE,MAAc,EAAE,QAAQ,GAAG,WAAW,EAAE,OAAgB;IACnJ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC3B,gBAAgB,CACZ,MAAM,EACN,MAAM,EACN,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAChC,GAAG,EAAE;YACD,OAAO,EAAE,CAAC;QACd,CAAC,EACD,QAAQ,EACR,IAAI,EACJ,OAAO,CACV,CAAC;IACN,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,MAAM,UAAU,iCAAiC,CAC7C,MAAc,EACd,MAAc,EACd,IAA8B,EAC9B,eAAwC,EACxC,QAAQ,GAAG,WAAW,EACtB,OAAO,GAAG,CAAC,EACX,YAAY,GAAG,KAAK,EACpB,QAAiB,EACjB,aAAa,GAAG,KAAK,EACrB,mBAAmB,GAAG,KAAK,EAC3B,YAAY,GAAG,IAAI,EACnB,OAAgB,EAChB,gBAAyD;IAEzD,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAC5F,MAAM,iBAAiB,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;IAE5C,IAAI,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,EAAE;QACpB,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAC3C,OAAO;KACV;IAED,MAAM,YAAY,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,eAAe,EAAE,EAAE,CAAC;IAC1F,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,6MAA6M;IAE5O,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;IAEhC,oHAAoH;IACpH,MAAM,OAAO,GAAG,IAAI,mBAAmB,CACnC,YAAY,EACZ,iBAAiB,EACjB,KAAK,EACL,KAAK,EACL,KAAK,EACL,SAAS,CAAC,wBAAwB,EAClC,KAAK,EACL,OAAO,CAAC,qBAAqB,EAC7B,SAAS,EACT,mBAAmB,EACnB,SAAS,EACT,SAAS,EACT,SAAS,EACT,OAAO,CACV,CAAC;IACF,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IAC1C,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IAC1B,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;IACtC,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC;IAC9B,OAAO,CAAC,mBAAmB,GAAG,YAAY,CAAC;IAC3C,gBAAgB,EAAE,CAAC,OAAO,CAAC,CAAC;IAE5B,MAAM,eAAe,GAAG,GAAG,EAAE;QACzB,IAAI,OAAO,CAAC,mBAAmB,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvD,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,GAAG,EAAE;gBACrC,IAAI,UAAU,KAAK,KAAK,IAAI,WAAW,KAAK,MAAM,EAAE;oBAChD,OAAO,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;wBACtE,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,eAAuD,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;wBAC/I,OAAO,CAAC,OAAO,EAAE,CAAC;oBACtB,CAAC,CAAC,CAAC;iBACN;qBAAM;oBACH,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,kBAAkB,EAAG,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;wBACtI,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;4BACtG,SAAS,CAAC,QAAQ,CACd,UAAU,EACV,WAAW,EACX,IAAI,EACJ,eAAuD,EACvD,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,SAAS,EACT,OAAO,CACV,CAAC;4BACF,OAAO,CAAC,OAAO,EAAE,CAAC;wBACtB,CAAC,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC;iBACN;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAErB,iHAAiH;YACjH,0IAA0I;YAC1I,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC1B,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAC5B,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB;YACzD,KAAK,CAAC,MAAM,EAAE,CAAC;SAClB;aAAM;YACH,UAAU,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;SACnC;IACL,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,GAAG,EAAE;QACzB,iBAAiB;QACjB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE5B,eAAe,EAAE,CAAC;IACtB,CAAC,CAAC;IAEF,IAAI,YAAY,EAAE;QACd,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,cAAc,EAAE,GAAG,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;QACrF,OAAO,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QACxC,8EAA8E;QAC9E,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE;YACxC,eAAe,CAAC,SAAS,EAAE,CAAC,UAAU,GAAG,GAAG,EAAE;gBAC1C,eAAe,EAAE,CAAC;YACtB,CAAC,CAAC;SACL;QACD,oCAAoC;aAC/B;YACD,eAAe,EAAE,CAAC;SACrB;KACJ;SAAM;QACH,kDAAkD;QAClD,eAAe,EAAE,CAAC;KACrB;AACL,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,UAAU,sCAAsC,CAClD,MAAc,EACd,MAAc,EACd,IAA8B,EAC9B,QAAQ,GAAG,WAAW,EACtB,OAAO,GAAG,CAAC,EACX,YAAY,GAAG,KAAK,EACpB,QAAiB,EACjB,aAAa,GAAG,KAAK,EACrB,mBAAmB,GAAG,KAAK,EAC3B,YAAY,GAAG,IAAI,EACnB,OAAgB;IAEhB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACnC,iCAAiC,CAC7B,MAAM,EACN,MAAM,EACN,IAAI,EACJ,CAAC,IAAI,EAAE,EAAE;YACL,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;gBAC7B,OAAO,CAAC,IAAI,CAAC,CAAC;aACjB;iBAAM;gBACH,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;aAC1C;QACL,CAAC,EACD,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,mBAAmB,EACnB,YAAY,EACZ,OAAO,CACV,CAAC;IACN,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;;;;;GAMG;AACH,SAAS,kBAAkB,CAAC,MAAc,EAAE,MAAc,EAAE,IAA8B;IACtF,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,WAAW,GAAG,CAAC,CAAC;IAEpB,mCAAmC;IACnC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;YAC5B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,+DAA+D;YAC1F,CAAC,CAAC,CAAC,CAAC;QAER,2CAA2C;QAC3C,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC3B,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;YACjC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;SAClC;QACD,uEAAuE;aAClE,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACjC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YAC/B,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;SAC9D;QACD,uEAAuE;aAClE,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACjC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;YACjC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;SAC9D;aAAM;YACH,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,GAAG,SAAS,CAAC,CAAC;YACxD,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;SAC9D;QAED,qDAAqD;QACrD,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE;YACrC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YAC/B,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;SAChC;QACD,iFAAiF;aAC5E,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YAC3C,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YAC7B,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;SACxE;QACD,iFAAiF;aAC5E,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAC3C,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YAC/B,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;SACxE;aAAM;YACH,UAAU,GAAG,KAAK,CAAC;YACnB,WAAW,GAAG,MAAM,CAAC;SACxB;KACJ;IACD,iDAAiD;SAC5C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACnB,MAAM,GAAG,IAAI,CAAC;QACd,KAAK,GAAG,IAAI,CAAC;QACb,UAAU,GAAG,IAAI,CAAC;QAClB,WAAW,GAAG,IAAI,CAAC;KACtB;IAED,iIAAiI;IACjI,8JAA8J;IAC9J,0IAA0I;IAC1I,uEAAuE;IACvE,IAAI,KAAK,EAAE;QACP,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KAC7B;IACD,IAAI,MAAM,EAAE;QACR,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;KAC/B;IACD,IAAI,UAAU,EAAE;QACZ,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;KACvC;IACD,IAAI,WAAW,EAAE;QACb,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;KACzC;IAED,OAAO,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,UAAU,EAAE,UAAU,GAAG,CAAC,EAAE,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AAC9G,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG;IAC3B;;;;;;;;;;;;;;;;;OAiBG;IACH,gBAAgB;IAEhB;;;;;;;;;;;;;;;OAeG;IACH,qBAAqB;IAErB;;;;;;;;;;;;OAYG;IACH,+BAA+B;IAE/B;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,iCAAiC;IAEjC;;;;;;;;;;;;;;;;;;;OAmBG;IACH,sCAAsC;CACzC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,eAAe,GAAG,GAAG,EAAE;IACzB,+BAA+B;IAC/B,KAAK,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAC1C,KAAK,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;IACpD,KAAK,CAAC,iCAAiC,GAAG,iCAAiC,CAAC;IAC5E,KAAK,CAAC,sCAAsC,GAAG,sCAAsC,CAAC;AAC1F,CAAC,CAAC;AAEF,eAAe,EAAE,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport { FxaaPostProcess } from \"../PostProcesses/fxaaPostProcess\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { Logger } from \"./logger\";\r\nimport { Tools } from \"./tools\";\r\nimport type { IScreenshotSize } from \"./interfaces/screenshotSize\";\r\nimport { DumpTools } from \"./dumpTools\";\r\nimport type { Nullable } from \"../types\";\r\nimport { ApplyPostProcess } from \"./textureTools\";\r\n\r\nimport type { Engine } from \"../Engines/engine\";\r\n\r\nlet screenshotCanvas: Nullable<HTMLCanvasElement> = null;\r\n\r\n/**\r\n * Captures a screenshot of the current rendering\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n * @param engine defines the rendering engine\r\n * @param camera defines the source camera\r\n * @param size This parameter can be set to a single number or to an object with the\r\n * following (optional) properties: precision, width, height. If a single number is passed,\r\n * it will be used for both width and height. If an object is passed, the screenshot size\r\n * will be derived from the parameters. The precision property is a multiplier allowing\r\n * rendering at a higher or lower resolution\r\n * @param successCallback defines the callback receives a single parameter which contains the\r\n * screenshot as a string of base64-encoded characters. This string can be assigned to the\r\n * src parameter of an <img> to display it\r\n * @param mimeType defines the MIME type of the screenshot image (default: image/png).\r\n * Check your browser for supported MIME types\r\n * @param forceDownload force the system to download the image even if a successCallback is provided\r\n * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n */\r\nexport function CreateScreenshot(\r\n    engine: Engine,\r\n    camera: Camera,\r\n    size: IScreenshotSize | number,\r\n    successCallback?: (data: string) => void,\r\n    mimeType = \"image/png\",\r\n    forceDownload = false,\r\n    quality?: number\r\n): void {\r\n    const { height, width } = _GetScreenshotSize(engine, camera, size);\r\n\r\n    if (!(height && width)) {\r\n        Logger.Error(\"Invalid 'size' parameter !\");\r\n        return;\r\n    }\r\n\r\n    if (!screenshotCanvas) {\r\n        screenshotCanvas = document.createElement(\"canvas\");\r\n    }\r\n\r\n    screenshotCanvas.width = width;\r\n    screenshotCanvas.height = height;\r\n\r\n    const renderContext = screenshotCanvas.getContext(\"2d\");\r\n\r\n    const ratio = engine.getRenderWidth() / engine.getRenderHeight();\r\n    let newWidth = width;\r\n    let newHeight = newWidth / ratio;\r\n    if (newHeight > height) {\r\n        newHeight = height;\r\n        newWidth = newHeight * ratio;\r\n    }\r\n\r\n    const offsetX = Math.max(0, width - newWidth) / 2;\r\n    const offsetY = Math.max(0, height - newHeight) / 2;\r\n\r\n    const scene = camera.getScene();\r\n    if (scene.activeCamera !== camera) {\r\n        CreateScreenshotUsingRenderTarget(\r\n            engine,\r\n            camera,\r\n            size,\r\n            (data) => {\r\n                if (forceDownload) {\r\n                    const blob = new Blob([data]);\r\n                    Tools.DownloadBlob(blob);\r\n                    if (successCallback) {\r\n                        successCallback(\"\");\r\n                    }\r\n                } else if (successCallback) {\r\n                    successCallback(data);\r\n                }\r\n            },\r\n            mimeType,\r\n            1.0,\r\n            engine.getCreationOptions().antialias,\r\n            undefined,\r\n            undefined,\r\n            undefined,\r\n            undefined,\r\n            quality\r\n        );\r\n    } else {\r\n        engine.onEndFrameObservable.addOnce(() => {\r\n            const renderingCanvas = engine.getRenderingCanvas();\r\n            if (renderContext && renderingCanvas) {\r\n                renderContext.drawImage(renderingCanvas, offsetX, offsetY, newWidth, newHeight);\r\n            }\r\n\r\n            if (screenshotCanvas) {\r\n                if (forceDownload) {\r\n                    Tools.EncodeScreenshotCanvasData(screenshotCanvas, undefined, mimeType, undefined, quality);\r\n                    if (successCallback) {\r\n                        successCallback(\"\");\r\n                    }\r\n                } else {\r\n                    Tools.EncodeScreenshotCanvasData(screenshotCanvas, successCallback, mimeType, undefined, quality);\r\n                }\r\n            }\r\n        });\r\n    }\r\n}\r\n\r\n/**\r\n * Captures a screenshot of the current rendering\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n * @param engine defines the rendering engine\r\n * @param camera defines the source camera\r\n * @param size This parameter can be set to a single number or to an object with the\r\n * following (optional) properties: precision, width, height. If a single number is passed,\r\n * it will be used for both width and height. If an object is passed, the screenshot size\r\n * will be derived from the parameters. The precision property is a multiplier allowing\r\n * rendering at a higher or lower resolution\r\n * @param mimeType defines the MIME type of the screenshot image (default: image/png).\r\n * Check your browser for supported MIME types\r\n * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n * @returns screenshot as a string of base64-encoded characters. This string can be assigned\r\n * to the src parameter of an <img> to display it\r\n */\r\nexport function CreateScreenshotAsync(engine: Engine, camera: Camera, size: IScreenshotSize | number, mimeType = \"image/png\", quality?: number): Promise<string> {\r\n    return new Promise((resolve, reject) => {\r\n        CreateScreenshot(\r\n            engine,\r\n            camera,\r\n            size,\r\n            (data) => {\r\n                if (typeof data !== \"undefined\") {\r\n                    resolve(data);\r\n                } else {\r\n                    reject(new Error(\"Data is undefined\"));\r\n                }\r\n            },\r\n            mimeType,\r\n            undefined,\r\n            quality\r\n        );\r\n    });\r\n}\r\n\r\n/**\r\n * Captures a screenshot of the current rendering for a specific size. This will render the entire canvas but will generate a blink (due to canvas resize)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n * @param engine defines the rendering engine\r\n * @param camera defines the source camera\r\n * @param width defines the expected width\r\n * @param height defines the expected height\r\n * @param mimeType defines the MIME type of the screenshot image (default: image/png).\r\n * Check your browser for supported MIME types\r\n * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n * @returns screenshot as a string of base64-encoded characters. This string can be assigned\r\n * to the src parameter of an <img> to display it\r\n */\r\nexport function CreateScreenshotWithResizeAsync(engine: Engine, camera: Camera, width: number, height: number, mimeType = \"image/png\", quality?: number): Promise<void> {\r\n    return new Promise((resolve) => {\r\n        CreateScreenshot(\r\n            engine,\r\n            camera,\r\n            { width: width, height: height },\r\n            () => {\r\n                resolve();\r\n            },\r\n            mimeType,\r\n            true,\r\n            quality\r\n        );\r\n    });\r\n}\r\n\r\n/**\r\n * Generates an image screenshot from the specified camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n * @param engine The engine to use for rendering\r\n * @param camera The camera to use for rendering\r\n * @param size This parameter can be set to a single number or to an object with the\r\n * following (optional) properties: precision, width, height, finalWidth, finalHeight. If a single number is passed,\r\n * it will be used for both width and height, as well as finalWidth, finalHeight. If an object is passed, the screenshot size\r\n * will be derived from the parameters. The precision property is a multiplier allowing\r\n * rendering at a higher or lower resolution\r\n * @param successCallback The callback receives a single parameter which contains the\r\n * screenshot as a string of base64-encoded characters. This string can be assigned to the\r\n * src parameter of an <img> to display it\r\n * @param mimeType The MIME type of the screenshot image (default: image/png).\r\n * Check your browser for supported MIME types\r\n * @param samples Texture samples (default: 1)\r\n * @param antialiasing Whether antialiasing should be turned on or not (default: false)\r\n * @param fileName A name for for the downloaded file.\r\n * @param renderSprites Whether the sprites should be rendered or not (default: false)\r\n * @param enableStencilBuffer Whether the stencil buffer should be enabled or not (default: false)\r\n * @param useLayerMask if the camera's layer mask should be used to filter what should be rendered (default: true)\r\n * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n * @param customizeTexture An optional callback that can be used to modify the render target texture before taking the screenshot. This can be used, for instance, to enable camera post-processes before taking the screenshot.\r\n */\r\nexport function CreateScreenshotUsingRenderTarget(\r\n    engine: Engine,\r\n    camera: Camera,\r\n    size: IScreenshotSize | number,\r\n    successCallback?: (data: string) => void,\r\n    mimeType = \"image/png\",\r\n    samples = 1,\r\n    antialiasing = false,\r\n    fileName?: string,\r\n    renderSprites = false,\r\n    enableStencilBuffer = false,\r\n    useLayerMask = true,\r\n    quality?: number,\r\n    customizeTexture?: (texture: RenderTargetTexture) => void\r\n): void {\r\n    const { height, width, finalWidth, finalHeight } = _GetScreenshotSize(engine, camera, size);\r\n    const targetTextureSize = { width, height };\r\n\r\n    if (!(height && width)) {\r\n        Logger.Error(\"Invalid 'size' parameter !\");\r\n        return;\r\n    }\r\n\r\n    const originalSize = { width: engine.getRenderWidth(), height: engine.getRenderHeight() };\r\n    engine.setSize(width, height); // we need this call to trigger onResizeObservable with the screenshot width/height on all the subsystems that are observing this event and that needs to (re)create some resources with the right dimensions\r\n\r\n    const scene = camera.getScene();\r\n\r\n    // At this point size can be a number, or an object (according to engine.prototype.createRenderTargetTexture method)\r\n    const texture = new RenderTargetTexture(\r\n        \"screenShot\",\r\n        targetTextureSize,\r\n        scene,\r\n        false,\r\n        false,\r\n        Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        false,\r\n        Texture.BILINEAR_SAMPLINGMODE,\r\n        undefined,\r\n        enableStencilBuffer,\r\n        undefined,\r\n        undefined,\r\n        undefined,\r\n        samples\r\n    );\r\n    texture.renderList = scene.meshes.slice();\r\n    texture.samples = samples;\r\n    texture.renderSprites = renderSprites;\r\n    texture.activeCamera = camera;\r\n    texture.forceLayerMaskCheck = useLayerMask;\r\n    customizeTexture?.(texture);\r\n\r\n    const renderWhenReady = () => {\r\n        if (texture.isReadyForRendering() && camera.isReady(true)) {\r\n            engine.onEndFrameObservable.addOnce(() => {\r\n                if (finalWidth === width && finalHeight === height) {\r\n                    texture.readPixels(undefined, undefined, undefined, false)!.then((data) => {\r\n                        DumpTools.DumpData(width, height, data, successCallback as (data: string | ArrayBuffer) => void, mimeType, fileName, true, undefined, quality);\r\n                        texture.dispose();\r\n                    });\r\n                } else {\r\n                    ApplyPostProcess(\"pass\", texture.getInternalTexture()!, scene, undefined, undefined, undefined, finalWidth, finalHeight).then((texture) => {\r\n                        engine._readTexturePixels(texture, finalWidth, finalHeight, -1, 0, null, true, false, 0, 0).then((data) => {\r\n                            DumpTools.DumpData(\r\n                                finalWidth,\r\n                                finalHeight,\r\n                                data,\r\n                                successCallback as (data: string | ArrayBuffer) => void,\r\n                                mimeType,\r\n                                fileName,\r\n                                true,\r\n                                undefined,\r\n                                quality\r\n                            );\r\n                            texture.dispose();\r\n                        });\r\n                    });\r\n                }\r\n            });\r\n\r\n            texture.render(true);\r\n\r\n            // re-render the scene after the camera has been reset to the original camera to avoid a flicker that could occur\r\n            // if the camera used for the RTT rendering stays in effect for the next frame (and if that camera was different from the original camera)\r\n            scene.incrementRenderId();\r\n            scene.resetCachedMaterial();\r\n            engine.setSize(originalSize.width, originalSize.height);\r\n            camera.getProjectionMatrix(true); // Force cache refresh;\r\n            scene.render();\r\n        } else {\r\n            setTimeout(renderWhenReady, 16);\r\n        }\r\n    };\r\n\r\n    const renderToTexture = () => {\r\n        // render the RTT\r\n        scene.incrementRenderId();\r\n        scene.resetCachedMaterial();\r\n\r\n        renderWhenReady();\r\n    };\r\n\r\n    if (antialiasing) {\r\n        const fxaaPostProcess = new FxaaPostProcess(\"antialiasing\", 1.0, scene.activeCamera);\r\n        texture.addPostProcess(fxaaPostProcess);\r\n        // Async Shader Compilation can lead to none ready effects in synchronous code\r\n        if (!fxaaPostProcess.getEffect().isReady()) {\r\n            fxaaPostProcess.getEffect().onCompiled = () => {\r\n                renderToTexture();\r\n            };\r\n        }\r\n        // The effect is ready we can render\r\n        else {\r\n            renderToTexture();\r\n        }\r\n    } else {\r\n        // No need to wait for extra resources to be ready\r\n        renderToTexture();\r\n    }\r\n}\r\n\r\n/**\r\n * Generates an image screenshot from the specified camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n * @param engine The engine to use for rendering\r\n * @param camera The camera to use for rendering\r\n * @param size This parameter can be set to a single number or to an object with the\r\n * following (optional) properties: precision, width, height. If a single number is passed,\r\n * it will be used for both width and height. If an object is passed, the screenshot size\r\n * will be derived from the parameters. The precision property is a multiplier allowing\r\n * rendering at a higher or lower resolution\r\n * @param mimeType The MIME type of the screenshot image (default: image/png).\r\n * Check your browser for supported MIME types\r\n * @param samples Texture samples (default: 1)\r\n * @param antialiasing Whether antialiasing should be turned on or not (default: false)\r\n * @param fileName A name for for the downloaded file.\r\n * @param renderSprites Whether the sprites should be rendered or not (default: false)\r\n * @param enableStencilBuffer Whether the stencil buffer should be enabled or not (default: false)\r\n * @param useLayerMask if the camera's layer mask should be used to filter what should be rendered (default: true)\r\n * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n * @returns screenshot as a string of base64-encoded characters. This string can be assigned\r\n * to the src parameter of an <img> to display it\r\n */\r\nexport function CreateScreenshotUsingRenderTargetAsync(\r\n    engine: Engine,\r\n    camera: Camera,\r\n    size: IScreenshotSize | number,\r\n    mimeType = \"image/png\",\r\n    samples = 1,\r\n    antialiasing = false,\r\n    fileName?: string,\r\n    renderSprites = false,\r\n    enableStencilBuffer = false,\r\n    useLayerMask = true,\r\n    quality?: number\r\n): Promise<string> {\r\n    return new Promise((resolve, reject) => {\r\n        CreateScreenshotUsingRenderTarget(\r\n            engine,\r\n            camera,\r\n            size,\r\n            (data) => {\r\n                if (typeof data !== \"undefined\") {\r\n                    resolve(data);\r\n                } else {\r\n                    reject(new Error(\"Data is undefined\"));\r\n                }\r\n            },\r\n            mimeType,\r\n            samples,\r\n            antialiasing,\r\n            fileName,\r\n            renderSprites,\r\n            enableStencilBuffer,\r\n            useLayerMask,\r\n            quality\r\n        );\r\n    });\r\n}\r\n\r\n/**\r\n * Gets height and width for screenshot size\r\n * @param engine\r\n * @param camera\r\n * @param size\r\n * @private\r\n */\r\nfunction _GetScreenshotSize(engine: Engine, camera: Camera, size: IScreenshotSize | number): { height: number; width: number; finalWidth: number; finalHeight: number } {\r\n    let height = 0;\r\n    let width = 0;\r\n    let finalWidth = 0;\r\n    let finalHeight = 0;\r\n\r\n    //If a size value defined as object\r\n    if (typeof size === \"object\") {\r\n        const precision = size.precision\r\n            ? Math.abs(size.precision) // prevent GL_INVALID_VALUE : glViewport: negative width/height\r\n            : 1;\r\n\r\n        //If a width and height values is specified\r\n        if (size.width && size.height) {\r\n            height = size.height * precision;\r\n            width = size.width * precision;\r\n        }\r\n        //If passing only width, computing height to keep display canvas ratio.\r\n        else if (size.width && !size.height) {\r\n            width = size.width * precision;\r\n            height = Math.round(width / engine.getAspectRatio(camera));\r\n        }\r\n        //If passing only height, computing width to keep display canvas ratio.\r\n        else if (size.height && !size.width) {\r\n            height = size.height * precision;\r\n            width = Math.round(height * engine.getAspectRatio(camera));\r\n        } else {\r\n            width = Math.round(engine.getRenderWidth() * precision);\r\n            height = Math.round(width / engine.getAspectRatio(camera));\r\n        }\r\n\r\n        //If a finalWidth and finalHeight values is specified\r\n        if (size.finalWidth && size.finalHeight) {\r\n            finalHeight = size.finalHeight;\r\n            finalWidth = size.finalWidth;\r\n        }\r\n        //If passing only finalWidth, computing finalHeight to keep display canvas ratio.\r\n        else if (size.finalWidth && !size.finalHeight) {\r\n            finalWidth = size.finalWidth;\r\n            finalHeight = Math.round(finalWidth / engine.getAspectRatio(camera));\r\n        }\r\n        //If passing only finalHeight, computing finalWidth to keep display canvas ratio.\r\n        else if (size.finalHeight && !size.finalWidth) {\r\n            finalHeight = size.finalHeight;\r\n            finalWidth = Math.round(finalHeight * engine.getAspectRatio(camera));\r\n        } else {\r\n            finalWidth = width;\r\n            finalHeight = height;\r\n        }\r\n    }\r\n    //Assuming here that \"size\" parameter is a number\r\n    else if (!isNaN(size)) {\r\n        height = size;\r\n        width = size;\r\n        finalWidth = size;\r\n        finalHeight = size;\r\n    }\r\n\r\n    // When creating the image data from the CanvasRenderingContext2D, the width and height is clamped to the size of the _gl context\r\n    // On certain GPUs, it seems as if the _gl context truncates to an integer automatically. Therefore, if a user tries to pass the width of their canvas element\r\n    // and it happens to be a float (1000.5 x 600.5 px), the engine.readPixels will return a different size array than context.createImageData\r\n    // to resolve this, we truncate the floats here to ensure the same size\r\n    if (width) {\r\n        width = Math.floor(width);\r\n    }\r\n    if (height) {\r\n        height = Math.floor(height);\r\n    }\r\n    if (finalWidth) {\r\n        finalWidth = Math.floor(finalWidth);\r\n    }\r\n    if (finalHeight) {\r\n        finalHeight = Math.floor(finalHeight);\r\n    }\r\n\r\n    return { height: height | 0, width: width | 0, finalWidth: finalWidth | 0, finalHeight: finalHeight | 0 };\r\n}\r\n\r\n/**\r\n * Class containing a set of static utilities functions for screenshots\r\n */\r\nexport const ScreenshotTools = {\r\n    /**\r\n     * Captures a screenshot of the current rendering\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n     * @param engine defines the rendering engine\r\n     * @param camera defines the source camera\r\n     * @param size This parameter can be set to a single number or to an object with the\r\n     * following (optional) properties: precision, width, height. If a single number is passed,\r\n     * it will be used for both width and height. If an object is passed, the screenshot size\r\n     * will be derived from the parameters. The precision property is a multiplier allowing\r\n     * rendering at a higher or lower resolution\r\n     * @param successCallback defines the callback receives a single parameter which contains the\r\n     * screenshot as a string of base64-encoded characters. This string can be assigned to the\r\n     * src parameter of an <img> to display it\r\n     * @param mimeType defines the MIME type of the screenshot image (default: image/png).\r\n     * Check your browser for supported MIME types\r\n     * @param forceDownload force the system to download the image even if a successCallback is provided\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     */\r\n    CreateScreenshot,\r\n\r\n    /**\r\n     * Captures a screenshot of the current rendering\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n     * @param engine defines the rendering engine\r\n     * @param camera defines the source camera\r\n     * @param size This parameter can be set to a single number or to an object with the\r\n     * following (optional) properties: precision, width, height. If a single number is passed,\r\n     * it will be used for both width and height. If an object is passed, the screenshot size\r\n     * will be derived from the parameters. The precision property is a multiplier allowing\r\n     * rendering at a higher or lower resolution\r\n     * @param mimeType defines the MIME type of the screenshot image (default: image/png).\r\n     * Check your browser for supported MIME types\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     * @returns screenshot as a string of base64-encoded characters. This string can be assigned\r\n     * to the src parameter of an <img> to display it\r\n     */\r\n    CreateScreenshotAsync,\r\n\r\n    /**\r\n     * Captures a screenshot of the current rendering for a specific size. This will render the entire canvas but will generate a blink (due to canvas resize)\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n     * @param engine defines the rendering engine\r\n     * @param camera defines the source camera\r\n     * @param width defines the expected width\r\n     * @param height defines the expected height\r\n     * @param mimeType defines the MIME type of the screenshot image (default: image/png).\r\n     * Check your browser for supported MIME types\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     * @returns screenshot as a string of base64-encoded characters. This string can be assigned\r\n     * to the src parameter of an <img> to display it\r\n     */\r\n    CreateScreenshotWithResizeAsync,\r\n\r\n    /**\r\n     * Generates an image screenshot from the specified camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n     * @param engine The engine to use for rendering\r\n     * @param camera The camera to use for rendering\r\n     * @param size This parameter can be set to a single number or to an object with the\r\n     * following (optional) properties: precision, width, height. If a single number is passed,\r\n     * it will be used for both width and height. If an object is passed, the screenshot size\r\n     * will be derived from the parameters. The precision property is a multiplier allowing\r\n     * rendering at a higher or lower resolution\r\n     * @param successCallback The callback receives a single parameter which contains the\r\n     * screenshot as a string of base64-encoded characters. This string can be assigned to the\r\n     * src parameter of an <img> to display it\r\n     * @param mimeType The MIME type of the screenshot image (default: image/png).\r\n     * Check your browser for supported MIME types\r\n     * @param samples Texture samples (default: 1)\r\n     * @param antialiasing Whether antialiasing should be turned on or not (default: false)\r\n     * @param fileName A name for for the downloaded file.\r\n     * @param renderSprites Whether the sprites should be rendered or not (default: false)\r\n     * @param enableStencilBuffer Whether the stencil buffer should be enabled or not (default: false)\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     */\r\n    CreateScreenshotUsingRenderTarget,\r\n\r\n    /**\r\n     * Generates an image screenshot from the specified camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n     * @param engine The engine to use for rendering\r\n     * @param camera The camera to use for rendering\r\n     * @param size This parameter can be set to a single number or to an object with the\r\n     * following (optional) properties: precision, width, height. If a single number is passed,\r\n     * it will be used for both width and height. If an object is passed, the screenshot size\r\n     * will be derived from the parameters. The precision property is a multiplier allowing\r\n     * rendering at a higher or lower resolution\r\n     * @param mimeType The MIME type of the screenshot image (default: image/png).\r\n     * Check your browser for supported MIME types\r\n     * @param samples Texture samples (default: 1)\r\n     * @param antialiasing Whether antialiasing should be turned on or not (default: false)\r\n     * @param fileName A name for for the downloaded file.\r\n     * @param renderSprites Whether the sprites should be rendered or not (default: false)\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     * @returns screenshot as a string of base64-encoded characters. This string can be assigned\r\n     * to the src parameter of an <img> to display it\r\n     */\r\n    CreateScreenshotUsingRenderTargetAsync,\r\n};\r\n\r\n/**\r\n * This will be executed automatically for UMD and es5.\r\n * If esm dev wants the side effects to execute they will have to run it manually\r\n * Once we build native modules those need to be exported.\r\n * @internal\r\n */\r\nconst initSideEffects = () => {\r\n    // References the dependencies.\r\n    Tools.CreateScreenshot = CreateScreenshot;\r\n    Tools.CreateScreenshotAsync = CreateScreenshotAsync;\r\n    Tools.CreateScreenshotUsingRenderTarget = CreateScreenshotUsingRenderTarget;\r\n    Tools.CreateScreenshotUsingRenderTargetAsync = CreateScreenshotUsingRenderTargetAsync;\r\n};\r\n\r\ninitSideEffects();\r\n"]}