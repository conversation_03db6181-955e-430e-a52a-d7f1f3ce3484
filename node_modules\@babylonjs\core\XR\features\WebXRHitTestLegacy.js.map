{"version": 3, "file": "WebXRHitTestLegacy.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRHitTestLegacy.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAEjF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAE1D,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AA0CzC;;;;GAIG;AACH,MAAM,OAAO,kBAAmB,SAAQ,oBAAoB;IA2BxD;;;;OAIG;IACH,YACI,iBAAsC;IACtC;;OAEG;IACa,UAAsC,EAAE;QAExD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFT,YAAO,GAAP,OAAO,CAAiC;QApC5D,oCAAoC;QAC5B,eAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnC,SAAI,GAAG,IAAI,MAAM,EAAE,CAAC;QACpB,qBAAgB,GAAG,KAAK,CAAC;QACzB,YAAO,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAavC;;WAEG;QACI,2BAAsB,GAAkB,EAAE,CAAC;QAClD;;WAEG;QACI,8BAAyB,GAAwC,IAAI,UAAU,EAAE,CAAC;QAiHjF,sBAAiB,GAAG,CAAC,SAAwB,EAAE,EAAE;YACrD,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBAClC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC/C,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,EAAE;oBACpD,GAAG,CAAC,4BAA4B,EAAE,CAAC;iBACtC;gBACD,uDAAuD;gBACvD,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;oBAC9B,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,CAAC;iBACzE;gBACD,OAAO;oBACH,WAAW,EAAE,MAAM;oBACnB,oBAAoB,EAAE,GAAG;iBAC5B,CAAC;YACN,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,sBAAsB,GAAG,SAAS,CAAC;YACxC,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC;QAEF,yEAAyE;QACjE,cAAS,GAAG,CAAC,KAAyB,EAAE,EAAE;YAC9C,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBACxB,OAAO;aACV;YACD,kBAAkB,CAAC,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAC9F,CAAC,CAAC;QA5HE,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC;QACtC,KAAK,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,gBAAgB,CAAC,SAAoB,EAAE,KAAY,EAAE,cAAgC,EAAE,MAAyC;QAC1I,OAAO,SAAS,CAAC,cAAe,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;YACrE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAClE,OAAO,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,wBAAwB,CAAC,KAAyB,EAAE,cAAgC;QAC9F,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;QAC5F,IAAI,CAAC,aAAa,EAAE;YAChB,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC9B;QACD,MAAM,SAAS,GAAG,IAAI,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAErD,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;IACjF,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;YACpC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SACpF;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QACD,iBAAiB;QACjB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;IAC3C,CAAC;IAES,UAAU,CAAC,KAAc;QAC/B,kDAAkD;QAClD,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;YACtD,OAAO;SACV;QACD,MAAM,IAAI,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QACxE,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QACD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,OAAO,CAAC,mCAAmC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9E,OAAO,CAAC,mCAAmC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAClF,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QAC5B,MAAM,GAAG,GAAG,IAAI,KAAK,CACC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EACjE,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAC/F,CAAC;QACF,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACjJ,CAAC;;AAjID;;GAEG;AACoB,uBAAI,GAAG,gBAAgB,CAAC,QAAQ,AAA5B,CAA6B;AACxD;;;;GAIG;AACoB,0BAAO,GAAG,CAAC,AAAJ,CAAK;AAuJvC,8BAA8B;AAC9B,oBAAoB,CAAC,eAAe,CAChC,kBAAkB,CAAC,IAAI,EACvB,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,kBAAkB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AACnE,CAAC,EACD,kBAAkB,CAAC,OAAO,EAC1B,KAAK,CACR,CAAC", "sourcesContent": ["import type { IWebXRFeature } from \"../webXRFeaturesManager\";\r\nimport { WebXRFeaturesManager, WebXRFeatureName } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { Vector3, Matrix } from \"../../Maths/math.vector\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n\r\n// the plugin is registered at the end of the file\r\n\r\n/**\r\n * An interface for all Hit test features\r\n */\r\nexport interface IWebXRHitTestFeature<T extends IWebXRLegacyHitResult> extends IWebXRFeature {\r\n    /**\r\n     * Triggered when new babylon (transformed) hit test results are available\r\n     */\r\n    onHitTestResultObservable: Observable<T[]>;\r\n}\r\n\r\n/**\r\n * Options used for hit testing\r\n */\r\nexport interface IWebXRLegacyHitTestOptions {\r\n    /**\r\n     * Only test when user interacted with the scene. Default - hit test every frame\r\n     */\r\n    testOnPointerDownOnly?: boolean;\r\n    /**\r\n     * The node to use to transform the local results to world coordinates\r\n     */\r\n    worldParentNode?: TransformNode;\r\n}\r\n\r\n/**\r\n * Interface defining the babylon result of raycasting/hit-test\r\n */\r\nexport interface IWebXRLegacyHitResult {\r\n    /**\r\n     * Transformation matrix that can be applied to a node that will put it in the hit point location\r\n     */\r\n    transformationMatrix: Matrix;\r\n    /**\r\n     * The native hit test result\r\n     */\r\n    xrHitResult: XRHitResult | XRHitTestResult;\r\n}\r\n\r\n/**\r\n * The currently-working hit-test module.\r\n * Hit test (or Ray-casting) is used to interact with the real world.\r\n * For further information read here - https://github.com/immersive-web/hit-test\r\n */\r\nexport class WebXRHitTestLegacy extends WebXRAbstractFeature implements IWebXRHitTestFeature<IWebXRLegacyHitResult> {\r\n    // in XR space z-forward is negative\r\n    private _direction = new Vector3(0, 0, -1);\r\n    private _mat = new Matrix();\r\n    private _onSelectEnabled = false;\r\n    private _origin = new Vector3(0, 0, 0);\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.HIT_TEST;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * Populated with the last native XR Hit Results\r\n     */\r\n    public lastNativeXRHitResults: XRHitResult[] = [];\r\n    /**\r\n     * Triggered when new babylon (transformed) hit test results are available\r\n     */\r\n    public onHitTestResultObservable: Observable<IWebXRLegacyHitResult[]> = new Observable();\r\n\r\n    /**\r\n     * Creates a new instance of the (legacy version) hit test feature\r\n     * @param _xrSessionManager an instance of WebXRSessionManager\r\n     * @param options options to use when constructing this feature\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        /**\r\n         * options to use when constructing this feature\r\n         */\r\n        public readonly options: IWebXRLegacyHitTestOptions = {}\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"hit-test\";\r\n        Tools.Warn(\"A newer version of this plugin is available\");\r\n    }\r\n\r\n    /**\r\n     * execute a hit test with an XR Ray\r\n     *\r\n     * @param xrSession a native xrSession that will execute this hit test\r\n     * @param xrRay the ray (position and direction) to use for ray-casting\r\n     * @param referenceSpace native XR reference space to use for the hit-test\r\n     * @param filter filter function that will filter the results\r\n     * @returns a promise that resolves with an array of native XR hit result in xr coordinates system\r\n     */\r\n    public static XRHitTestWithRay(xrSession: XRSession, xrRay: XRRay, referenceSpace: XRReferenceSpace, filter?: (result: XRHitResult) => boolean): Promise<XRHitResult[]> {\r\n        return xrSession.requestHitTest!(xrRay, referenceSpace).then((results) => {\r\n            const filterFunction = filter || ((result) => !!result.hitMatrix);\r\n            return results.filter(filterFunction);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Execute a hit test on the current running session using a select event returned from a transient input (such as touch)\r\n     * @param event the (select) event to use to select with\r\n     * @param referenceSpace the reference space to use for this hit test\r\n     * @returns a promise that resolves with an array of native XR hit result in xr coordinates system\r\n     */\r\n    public static XRHitTestWithSelectEvent(event: XRInputSourceEvent, referenceSpace: XRReferenceSpace): Promise<XRHitResult[]> {\r\n        const targetRayPose = event.frame.getPose(event.inputSource.targetRaySpace, referenceSpace);\r\n        if (!targetRayPose) {\r\n            return Promise.resolve([]);\r\n        }\r\n        const targetRay = new XRRay(targetRayPose.transform);\r\n\r\n        return this.XRHitTestWithRay(event.frame.session, targetRay, referenceSpace);\r\n    }\r\n\r\n    /**\r\n     * attach this feature\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public attach(): boolean {\r\n        if (!super.attach()) {\r\n            return false;\r\n        }\r\n        if (this.options.testOnPointerDownOnly) {\r\n            this._xrSessionManager.session.addEventListener(\"select\", this._onSelect, false);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * detach this feature.\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n        // disable select\r\n        this._onSelectEnabled = false;\r\n        this._xrSessionManager.session.removeEventListener(\"select\", this._onSelect);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n        this.onHitTestResultObservable.clear();\r\n    }\r\n\r\n    protected _onXRFrame(frame: XRFrame) {\r\n        // make sure we do nothing if (async) not attached\r\n        if (!this.attached || this.options.testOnPointerDownOnly) {\r\n            return;\r\n        }\r\n        const pose = frame.getViewerPose(this._xrSessionManager.referenceSpace);\r\n        if (!pose) {\r\n            return;\r\n        }\r\n        Matrix.FromArrayToRef(pose.transform.matrix, 0, this._mat);\r\n        Vector3.TransformCoordinatesFromFloatsToRef(0, 0, 0, this._mat, this._origin);\r\n        Vector3.TransformCoordinatesFromFloatsToRef(0, 0, -1, this._mat, this._direction);\r\n        this._direction.subtractInPlace(this._origin);\r\n        this._direction.normalize();\r\n        const ray = new XRRay(\r\n            <DOMPointReadOnly>{ x: this._origin.x, y: this._origin.y, z: this._origin.z, w: 0 },\r\n            <DOMPointReadOnly>{ x: this._direction.x, y: this._direction.y, z: this._direction.z, w: 0 }\r\n        );\r\n        WebXRHitTestLegacy.XRHitTestWithRay(this._xrSessionManager.session, ray, this._xrSessionManager.referenceSpace).then(this._onHitTestResults);\r\n    }\r\n\r\n    private _onHitTestResults = (xrResults: XRHitResult[]) => {\r\n        const mats = xrResults.map((result) => {\r\n            const mat = Matrix.FromArray(result.hitMatrix);\r\n            if (!this._xrSessionManager.scene.useRightHandedSystem) {\r\n                mat.toggleModelMatrixHandInPlace();\r\n            }\r\n            // if (this.options.coordinatesSpace === Space.WORLD) {\r\n            if (this.options.worldParentNode) {\r\n                mat.multiplyToRef(this.options.worldParentNode.getWorldMatrix(), mat);\r\n            }\r\n            return {\r\n                xrHitResult: result,\r\n                transformationMatrix: mat,\r\n            };\r\n        });\r\n\r\n        this.lastNativeXRHitResults = xrResults;\r\n        this.onHitTestResultObservable.notifyObservers(mats);\r\n    };\r\n\r\n    // can be done using pointerdown event, and xrSessionManager.currentFrame\r\n    private _onSelect = (event: XRInputSourceEvent) => {\r\n        if (!this._onSelectEnabled) {\r\n            return;\r\n        }\r\n        WebXRHitTestLegacy.XRHitTestWithSelectEvent(event, this._xrSessionManager.referenceSpace);\r\n    };\r\n}\r\n\r\n//register the plugin versions\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRHitTestLegacy.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRHitTestLegacy(xrSessionManager, options);\r\n    },\r\n    WebXRHitTestLegacy.Version,\r\n    false\r\n);\r\n"]}