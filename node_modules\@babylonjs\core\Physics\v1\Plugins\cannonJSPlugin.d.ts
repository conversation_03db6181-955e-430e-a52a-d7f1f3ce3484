import type { Nullable } from "../../../types";
import { Vector3, Quaternion } from "../../../Maths/math.vector";
import type { AbstractMesh } from "../../../Meshes/abstractMesh";
import type { IPhysicsEnginePlugin, PhysicsImpostorJoint } from "../IPhysicsEnginePlugin";
import { PhysicsImpostor } from "../physicsImpostor";
import type { IMotorEnabledJoint } from "../physicsJoint";
import { PhysicsJoint } from "../physicsJoint";
import { PhysicsRaycastResult } from "../../physicsRaycastResult";
/** @internal */
export declare class CannonJSPlugin implements IPhysicsEnginePlugin {
    private _useDeltaForWorldStep;
    world: any;
    name: string;
    private _physicsMaterials;
    private _fixedTimeStep;
    private _cannonRaycastResult;
    private _raycastResult;
    private _physicsBodiesToRemoveAfterStep;
    private _firstFrame;
    private _tmpQuaternion;
    BJSCANNON: any;
    constructor(_useDeltaForWorldStep?: boolean, iterations?: number, cannonInjection?: any);
    /**
     *
     * @returns plugin version
     */
    getPluginVersion(): number;
    setGravity(gravity: Vector3): void;
    setTimeStep(timeStep: number): void;
    getTimeStep(): number;
    executeStep(delta: number, impostors: Array<PhysicsImpostor>): void;
    private _removeMarkedPhysicsBodiesFromWorld;
    applyImpulse(impostor: PhysicsImpostor, force: Vector3, contactPoint: Vector3): void;
    applyForce(impostor: PhysicsImpostor, force: Vector3, contactPoint: Vector3): void;
    generatePhysicsBody(impostor: PhysicsImpostor): void;
    private _processChildMeshes;
    removePhysicsBody(impostor: PhysicsImpostor): void;
    generateJoint(impostorJoint: PhysicsImpostorJoint): void;
    removeJoint(impostorJoint: PhysicsImpostorJoint): void;
    private _addMaterial;
    private _checkWithEpsilon;
    private _createShape;
    private _createHeightmap;
    private _minus90X;
    private _plus90X;
    private _tmpPosition;
    private _tmpDeltaPosition;
    private _tmpUnityRotation;
    private _updatePhysicsBodyTransformation;
    setTransformationFromPhysicsBody(impostor: PhysicsImpostor): void;
    setPhysicsBodyTransformation(impostor: PhysicsImpostor, newPosition: Vector3, newRotation: Quaternion): void;
    isSupported(): boolean;
    setLinearVelocity(impostor: PhysicsImpostor, velocity: Vector3): void;
    setAngularVelocity(impostor: PhysicsImpostor, velocity: Vector3): void;
    getLinearVelocity(impostor: PhysicsImpostor): Nullable<Vector3>;
    getAngularVelocity(impostor: PhysicsImpostor): Nullable<Vector3>;
    setBodyMass(impostor: PhysicsImpostor, mass: number): void;
    getBodyMass(impostor: PhysicsImpostor): number;
    getBodyFriction(impostor: PhysicsImpostor): number;
    setBodyFriction(impostor: PhysicsImpostor, friction: number): void;
    getBodyRestitution(impostor: PhysicsImpostor): number;
    setBodyRestitution(impostor: PhysicsImpostor, restitution: number): void;
    sleepBody(impostor: PhysicsImpostor): void;
    wakeUpBody(impostor: PhysicsImpostor): void;
    updateDistanceJoint(joint: PhysicsJoint, maxDistance: number): void;
    setMotor(joint: IMotorEnabledJoint, speed?: number, maxForce?: number, motorIndex?: number): void;
    setLimit(joint: IMotorEnabledJoint, minForce: number, maxForce?: number): void;
    syncMeshWithImpostor(mesh: AbstractMesh, impostor: PhysicsImpostor): void;
    getRadius(impostor: PhysicsImpostor): number;
    getBoxSizeToRef(impostor: PhysicsImpostor, result: Vector3): void;
    dispose(): void;
    private _extendNamespace;
    /**
     * Does a raycast in the physics world
     * @param from when should the ray start?
     * @param to when should the ray end?
     * @returns PhysicsRaycastResult
     */
    raycast(from: Vector3, to: Vector3): PhysicsRaycastResult;
    /**
     * Does a raycast in the physics world
     * @param from when should the ray start?
     * @param to when should the ray end?
     * @param result resulting PhysicsRaycastResult
     */
    raycastToRef(from: Vector3, to: Vector3, result: PhysicsRaycastResult): void;
}
