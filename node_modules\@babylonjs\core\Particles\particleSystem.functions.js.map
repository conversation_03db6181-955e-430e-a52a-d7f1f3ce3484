{"version": 3, "file": "particleSystem.functions.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Particles/particleSystem.functions.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,gCAA+B;AACjD,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAC3E,OAAO,EAAE,0BAA0B,EAAE,MAAM,2CAA2C,CAAC;AACvF,OAAO,EAAE,6BAA6B,EAAE,qBAAqB,EAAE,MAAM,sCAAsC,CAAC;AAC5G,OAAO,EAAE,+BAA+B,EAAE,uBAAuB,EAAE,MAAM,wCAAwC,CAAC;AAClH,OAAO,EAAE,mBAAmB,EAAE,MAAM,oCAAoC,CAAC;AAEzE;;;;;GAKG;AACH,MAAM,UAAU,kBAAkB,CAAC,UAAmB,EAAE,UAAmB;IACvE,MAAM,eAAe,GAAG,IAAI,oBAAoB,EAAE,CAAC;IACnD,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;IACxC,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;IACxC,OAAO,eAAe,CAAC;AAC3B,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,wBAAwB,CAAC,MAAM,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC;IAChE,OAAO,IAAI,0BAA0B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC/D,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC;IAC3D,OAAO,IAAI,qBAAqB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,2BAA2B,CAAC,MAAM,GAAG,CAAC,EAAE,UAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,UAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5H,OAAO,IAAI,6BAA6B,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;AAC7E,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,EAAE,mBAAmB,GAAG,CAAC;IAClG,OAAO,IAAI,uBAAuB,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,mBAAmB,CAAC,CAAC;AACzF,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,6BAA6B,CACzC,MAAM,GAAG,CAAC,EACV,MAAM,GAAG,CAAC,EACV,WAAW,GAAG,CAAC,EACf,UAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EACnC,UAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAEnC,OAAO,IAAI,+BAA+B,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;AACpG,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC;IAC7D,OAAO,IAAI,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAClD,CAAC", "sourcesContent": ["import { Vector3 } from \"core/Maths/math.vector\";\r\nimport { PointParticleEmitter } from \"./EmitterTypes/pointParticleEmitter\";\r\nimport { HemisphericParticleEmitter } from \"./EmitterTypes/hemisphericParticleEmitter\";\r\nimport { SphereDirectedParticleEmitter, SphereParticleEmitter } from \"./EmitterTypes/sphereParticleEmitter\";\r\nimport { CylinderDirectedParticleEmitter, CylinderParticleEmitter } from \"./EmitterTypes/cylinderParticleEmitter\";\r\nimport { ConeParticleEmitter } from \"./EmitterTypes/coneParticleEmitter\";\r\n\r\n/**\r\n * Creates a Point Emitter for the particle system (emits directly from the emitter position)\r\n * @param direction1 Particles are emitted between the direction1 and direction2 from within the box\r\n * @param direction2 Particles are emitted between the direction1 and direction2 from within the box\r\n * @returns the emitter\r\n */\r\nexport function CreatePointEmitter(direction1: Vector3, direction2: Vector3): PointParticleEmitter {\r\n    const particleEmitter = new PointParticleEmitter();\r\n    particleEmitter.direction1 = direction1;\r\n    particleEmitter.direction2 = direction2;\r\n    return particleEmitter;\r\n}\r\n\r\n/**\r\n * Creates a Hemisphere Emitter for the particle system (emits along the hemisphere radius)\r\n * @param radius The radius of the hemisphere to emit from\r\n * @param radiusRange The range of the hemisphere to emit from [0-1] 0 Surface Only, 1 Entire Radius\r\n * @returns the emitter\r\n */\r\nexport function CreateHemisphericEmitter(radius = 1, radiusRange = 1): HemisphericParticleEmitter {\r\n    return new HemisphericParticleEmitter(radius, radiusRange);\r\n}\r\n\r\n/**\r\n * Creates a Sphere Emitter for the particle system (emits along the sphere radius)\r\n * @param radius The radius of the sphere to emit from\r\n * @param radiusRange The range of the sphere to emit from [0-1] 0 Surface Only, 1 Entire Radius\r\n * @returns the emitter\r\n */\r\nexport function CreateSphereEmitter(radius = 1, radiusRange = 1): SphereParticleEmitter {\r\n    return new SphereParticleEmitter(radius, radiusRange);\r\n}\r\n\r\n/**\r\n * Creates a Directed Sphere Emitter for the particle system (emits between direction1 and direction2)\r\n * @param radius The radius of the sphere to emit from\r\n * @param direction1 Particles are emitted between the direction1 and direction2 from within the sphere\r\n * @param direction2 Particles are emitted between the direction1 and direction2 from within the sphere\r\n * @returns the emitter\r\n */\r\nexport function CreateDirectedSphereEmitter(radius = 1, direction1 = new Vector3(0, 1.0, 0), direction2 = new Vector3(0, 1.0, 0)): SphereDirectedParticleEmitter {\r\n    return new SphereDirectedParticleEmitter(radius, direction1, direction2);\r\n}\r\n\r\n/**\r\n * Creates a Cylinder Emitter for the particle system (emits from the cylinder to the particle position)\r\n * @param radius The radius of the emission cylinder\r\n * @param height The height of the emission cylinder\r\n * @param radiusRange The range of emission [0-1] 0 Surface only, 1 Entire Radius\r\n * @param directionRandomizer How much to randomize the particle direction [0-1]\r\n * @returns the emitter\r\n */\r\nexport function CreateCylinderEmitter(radius = 1, height = 1, radiusRange = 1, directionRandomizer = 0): CylinderParticleEmitter {\r\n    return new CylinderParticleEmitter(radius, height, radiusRange, directionRandomizer);\r\n}\r\n\r\n/**\r\n * Creates a Directed Cylinder Emitter for the particle system (emits between direction1 and direction2)\r\n * @param radius The radius of the cylinder to emit from\r\n * @param height The height of the emission cylinder\r\n * @param radiusRange the range of the emission cylinder [0-1] 0 Surface only, 1 Entire Radius (1 by default)\r\n * @param direction1 Particles are emitted between the direction1 and direction2 from within the cylinder\r\n * @param direction2 Particles are emitted between the direction1 and direction2 from within the cylinder\r\n * @returns the emitter\r\n */\r\nexport function CreateDirectedCylinderEmitter(\r\n    radius = 1,\r\n    height = 1,\r\n    radiusRange = 1,\r\n    direction1 = new Vector3(0, 1.0, 0),\r\n    direction2 = new Vector3(0, 1.0, 0)\r\n): CylinderDirectedParticleEmitter {\r\n    return new CylinderDirectedParticleEmitter(radius, height, radiusRange, direction1, direction2);\r\n}\r\n\r\n/**\r\n * Creates a Cone Emitter for the particle system (emits from the cone to the particle position)\r\n * @param radius The radius of the cone to emit from\r\n * @param angle The base angle of the cone\r\n * @returns the emitter\r\n */\r\nexport function CreateConeEmitter(radius = 1, angle = Math.PI / 4): ConeParticleEmitter {\r\n    return new ConeParticleEmitter(radius, angle);\r\n}\r\n"]}