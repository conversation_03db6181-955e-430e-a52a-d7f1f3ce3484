{"version": 3, "file": "rawTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/rawTexture.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,4CAA4C,CAAC;AAMpD;;;;GAIG;AACH,MAAM,OAAO,UAAW,SAAQ,OAAO;IACnC;;;;;;;;;;;;;;;;OAgBG;IACH,YACI,IAA+B,EAC/B,KAAa,EACb,MAAc;IACd;;OAEG;IACI,MAAc,EACrB,aAA2C,EAC3C,kBAA2B,IAAI,EAC/B,UAAmB,KAAK,EACxB,eAAuB,SAAS,CAAC,8BAA8B,EAC/D,OAAe,SAAS,CAAC,wBAAwB,EACjD,aAAsB,EACtB,aAAuB;QAEvB,KAAK,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,eAAe,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;QATtJ,WAAM,GAAN,MAAM,CAAQ;QAWrB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,2BAA2B,IAAI,IAAI,KAAK,SAAS,CAAC,iBAAiB,EAAE;YACzF,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;SACzD;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,+BAA+B,IAAI,IAAI,KAAK,SAAS,CAAC,sBAAsB,EAAE;YAClG,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;SACzD;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,IAAI,CAAC,EAAE,aAAa,IAAI,KAAK,CAAC,CAAC;QAE3K,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACvC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,IAAqB;QAC/B,IAAI,CAAC,UAAU,EAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,QAAS,CAAC,MAAM,EAAE,IAAI,CAAC,QAAS,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,QAAS,CAAC,IAAI,EAAE,IAAI,CAAC,QAAS,CAAC,cAAc,CAAC,CAAC;IACtK,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAC,sBAAsB,CAChC,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,aAA2C,EAC3C,kBAA2B,IAAI,EAC/B,UAAmB,KAAK,EACxB,eAAuB,SAAS,CAAC,8BAA8B;QAE/D,OAAO,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,uBAAuB,EAAE,aAAa,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;IACzI,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAC,2BAA2B,CACrC,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,aAA2C,EAC3C,kBAA2B,IAAI,EAC/B,UAAmB,KAAK,EACxB,eAAuB,SAAS,CAAC,8BAA8B;QAE/D,OAAO,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,6BAA6B,EAAE,aAAa,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;IAC/I,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAC,kBAAkB,CAC5B,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,aAA2C,EAC3C,kBAA2B,IAAI,EAC/B,UAAmB,KAAK,EACxB,eAAuB,SAAS,CAAC,8BAA8B;QAE/D,OAAO,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,mBAAmB,EAAE,aAAa,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;IACrI,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,MAAM,CAAC,gBAAgB,CAC1B,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,aAA2C,EAC3C,kBAA2B,IAAI,EAC/B,UAAmB,KAAK,EACxB,eAAuB,SAAS,CAAC,8BAA8B,EAC/D,OAAe,SAAS,CAAC,wBAAwB,EACjD,gBAAwB,CAAC,EACzB,gBAAyB,KAAK;QAE9B,OAAO,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,iBAAiB,EAAE,aAAa,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;IACvK,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,MAAM,CAAC,iBAAiB,CAC3B,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,aAA2C,EAC3C,kBAA2B,IAAI,EAC/B,UAAmB,KAAK,EACxB,eAAuB,SAAS,CAAC,8BAA8B,EAC/D,OAAe,SAAS,CAAC,wBAAwB,EACjD,gBAAwB,CAAC,EACzB,gBAAyB,KAAK;QAE9B,OAAO,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,kBAAkB,EAAE,aAAa,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;IACxK,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,MAAM,CAAC,wBAAwB,CAClC,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,aAA2C,EAC3C,kBAA2B,IAAI,EAC/B,UAAmB,KAAK,EACxB,eAAuB,SAAS,CAAC,8BAA8B,EAC/D,OAAe,SAAS,CAAC,wBAAwB,EACjD,gBAAyB,KAAK;QAE9B,OAAO,IAAI,UAAU,CACjB,IAAI,EACJ,KAAK,EACL,MAAM,EACN,SAAS,CAAC,kBAAkB,EAC5B,aAAa,EACb,eAAe,EACf,OAAO,EACP,YAAY,EACZ,IAAI,EACJ,SAAS,CAAC,4BAA4B,EACtC,aAAa,CAChB,CAAC;IACN,CAAC;IAED;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,cAAc,CACxB,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,aAA2C,EAC3C,kBAA2B,IAAI,EAC/B,UAAmB,KAAK,EACxB,eAAuB,OAAO,CAAC,sBAAsB,EACrD,OAAe,SAAS,CAAC,iBAAiB;QAE1C,OAAO,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,eAAe,EAAE,aAAa,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;IACvI,CAAC;IAED;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,qBAAqB,CAC/B,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,aAA2C,EAC3C,kBAA2B,IAAI,EAC/B,UAAmB,KAAK,EACxB,eAAuB,OAAO,CAAC,sBAAsB,EACrD,OAAe,SAAS,CAAC,iBAAiB;QAE1C,OAAO,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,eAAe,EAAE,aAAa,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,CAAC,4BAA4B,CAAC,CAAC;IAC/K,CAAC;CACJ", "sourcesContent": ["import { Texture } from \"./texture\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport \"../../Engines/Extensions/engine.rawTexture\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { ThinEngine } from \"../../Engines/thinEngine\";\r\n\r\nimport type { Scene } from \"../../scene\";\r\n\r\n/**\r\n * Raw texture can help creating a texture directly from an array of data.\r\n * This can be super useful if you either get the data from an uncompressed source or\r\n * if you wish to create your texture pixel by pixel.\r\n */\r\nexport class RawTexture extends Texture {\r\n    /**\r\n     * Instantiates a new RawTexture.\r\n     * Raw texture can help creating a texture directly from an array of data.\r\n     * This can be super useful if you either get the data from an uncompressed source or\r\n     * if you wish to create your texture pixel by pixel.\r\n     * @param data define the array of data to use to create the texture (null to create an empty texture)\r\n     * @param width define the width of the texture\r\n     * @param height define the height of the texture\r\n     * @param format define the format of the data (RGB, RGBA... Engine.TEXTUREFORMAT_xxx)\r\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\r\n     * @param generateMipMaps define whether mip maps should be generated or not\r\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\r\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\r\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n     */\r\n    constructor(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        /**\r\n         * Define the format of the data (RGB, RGBA... Engine.TEXTUREFORMAT_xxx)\r\n         */\r\n        public format: number,\r\n        sceneOrEngine: Nullable<Scene | ThinEngine>,\r\n        generateMipMaps: boolean = true,\r\n        invertY: boolean = false,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        type: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        creationFlags?: number,\r\n        useSRGBBuffer?: boolean\r\n    ) {\r\n        super(null, sceneOrEngine, !generateMipMaps, invertY, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, creationFlags);\r\n\r\n        if (!this._engine) {\r\n            return;\r\n        }\r\n\r\n        if (!this._engine._caps.textureFloatLinearFiltering && type === Constants.TEXTURETYPE_FLOAT) {\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        }\r\n        if (!this._engine._caps.textureHalfFloatLinearFiltering && type === Constants.TEXTURETYPE_HALF_FLOAT) {\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        }\r\n\r\n        this._texture = this._engine.createRawTexture(data, width, height, format, generateMipMaps, invertY, samplingMode, null, type, creationFlags ?? 0, useSRGBBuffer ?? false);\r\n\r\n        this.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n        this.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n    }\r\n\r\n    /**\r\n     * Updates the texture underlying data.\r\n     * @param data Define the new data of the texture\r\n     */\r\n    public update(data: ArrayBufferView): void {\r\n        this._getEngine()!.updateRawTexture(this._texture, data, this._texture!.format, this._texture!.invertY, null, this._texture!.type, this._texture!._useSRGBBuffer);\r\n    }\r\n\r\n    /**\r\n     * Creates a luminance texture from some data.\r\n     * @param data Define the texture data\r\n     * @param width Define the width of the texture\r\n     * @param height Define the height of the texture\r\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\r\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\r\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\r\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\r\n     * @returns the luminance texture\r\n     */\r\n    public static CreateLuminanceTexture(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        sceneOrEngine: Nullable<Scene | ThinEngine>,\r\n        generateMipMaps: boolean = true,\r\n        invertY: boolean = false,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE\r\n    ): RawTexture {\r\n        return new RawTexture(data, width, height, Constants.TEXTUREFORMAT_LUMINANCE, sceneOrEngine, generateMipMaps, invertY, samplingMode);\r\n    }\r\n\r\n    /**\r\n     * Creates a luminance alpha texture from some data.\r\n     * @param data Define the texture data\r\n     * @param width Define the width of the texture\r\n     * @param height Define the height of the texture\r\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\r\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\r\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\r\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\r\n     * @returns the luminance alpha texture\r\n     */\r\n    public static CreateLuminanceAlphaTexture(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        sceneOrEngine: Nullable<Scene | ThinEngine>,\r\n        generateMipMaps: boolean = true,\r\n        invertY: boolean = false,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE\r\n    ): RawTexture {\r\n        return new RawTexture(data, width, height, Constants.TEXTUREFORMAT_LUMINANCE_ALPHA, sceneOrEngine, generateMipMaps, invertY, samplingMode);\r\n    }\r\n\r\n    /**\r\n     * Creates an alpha texture from some data.\r\n     * @param data Define the texture data\r\n     * @param width Define the width of the texture\r\n     * @param height Define the height of the texture\r\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\r\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\r\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\r\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\r\n     * @returns the alpha texture\r\n     */\r\n    public static CreateAlphaTexture(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        sceneOrEngine: Nullable<Scene | ThinEngine>,\r\n        generateMipMaps: boolean = true,\r\n        invertY: boolean = false,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE\r\n    ): RawTexture {\r\n        return new RawTexture(data, width, height, Constants.TEXTUREFORMAT_ALPHA, sceneOrEngine, generateMipMaps, invertY, samplingMode);\r\n    }\r\n\r\n    /**\r\n     * Creates a RGB texture from some data.\r\n     * @param data Define the texture data\r\n     * @param width Define the width of the texture\r\n     * @param height Define the height of the texture\r\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\r\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\r\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\r\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\r\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n     * @returns the RGB alpha texture\r\n     */\r\n    public static CreateRGBTexture(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        sceneOrEngine: Nullable<Scene | ThinEngine>,\r\n        generateMipMaps: boolean = true,\r\n        invertY: boolean = false,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        type: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        creationFlags: number = 0,\r\n        useSRGBBuffer: boolean = false\r\n    ): RawTexture {\r\n        return new RawTexture(data, width, height, Constants.TEXTUREFORMAT_RGB, sceneOrEngine, generateMipMaps, invertY, samplingMode, type, creationFlags, useSRGBBuffer);\r\n    }\r\n\r\n    /**\r\n     * Creates a RGBA texture from some data.\r\n     * @param data Define the texture data\r\n     * @param width Define the width of the texture\r\n     * @param height Define the height of the texture\r\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\r\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\r\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\r\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\r\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n     * @returns the RGBA texture\r\n     */\r\n    public static CreateRGBATexture(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        sceneOrEngine: Nullable<Scene | ThinEngine>,\r\n        generateMipMaps: boolean = true,\r\n        invertY: boolean = false,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        type: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        creationFlags: number = 0,\r\n        useSRGBBuffer: boolean = false\r\n    ): RawTexture {\r\n        return new RawTexture(data, width, height, Constants.TEXTUREFORMAT_RGBA, sceneOrEngine, generateMipMaps, invertY, samplingMode, type, creationFlags, useSRGBBuffer);\r\n    }\r\n\r\n    /**\r\n     * Creates a RGBA storage texture from some data.\r\n     * @param data Define the texture data\r\n     * @param width Define the width of the texture\r\n     * @param height Define the height of the texture\r\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\r\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\r\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\r\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\r\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\r\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n     * @returns the RGBA texture\r\n     */\r\n    public static CreateRGBAStorageTexture(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        sceneOrEngine: Nullable<Scene | ThinEngine>,\r\n        generateMipMaps: boolean = true,\r\n        invertY: boolean = false,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        type: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        useSRGBBuffer: boolean = false\r\n    ): RawTexture {\r\n        return new RawTexture(\r\n            data,\r\n            width,\r\n            height,\r\n            Constants.TEXTUREFORMAT_RGBA,\r\n            sceneOrEngine,\r\n            generateMipMaps,\r\n            invertY,\r\n            samplingMode,\r\n            type,\r\n            Constants.TEXTURE_CREATIONFLAG_STORAGE,\r\n            useSRGBBuffer\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Creates a R texture from some data.\r\n     * @param data Define the texture data\r\n     * @param width Define the width of the texture\r\n     * @param height Define the height of the texture\r\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\r\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\r\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\r\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\r\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\r\n     * @returns the R texture\r\n     */\r\n    public static CreateRTexture(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        sceneOrEngine: Nullable<Scene | ThinEngine>,\r\n        generateMipMaps: boolean = true,\r\n        invertY: boolean = false,\r\n        samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE,\r\n        type: number = Constants.TEXTURETYPE_FLOAT\r\n    ): RawTexture {\r\n        return new RawTexture(data, width, height, Constants.TEXTUREFORMAT_R, sceneOrEngine, generateMipMaps, invertY, samplingMode, type);\r\n    }\r\n\r\n    /**\r\n     * Creates a R storage texture from some data.\r\n     * @param data Define the texture data\r\n     * @param width Define the width of the texture\r\n     * @param height Define the height of the texture\r\n     * @param sceneOrEngine defines the scene or engine the texture will belong to\r\n     * @param generateMipMaps Define whether or not to create mip maps for the texture\r\n     * @param invertY define if the data should be flipped on Y when uploaded to the GPU\r\n     * @param samplingMode define the texture sampling mode (Texture.xxx_SAMPLINGMODE)\r\n     * @param type define the format of the data (int, float... Engine.TEXTURETYPE_xxx)\r\n     * @returns the R texture\r\n     */\r\n    public static CreateRStorageTexture(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        sceneOrEngine: Nullable<Scene | ThinEngine>,\r\n        generateMipMaps: boolean = true,\r\n        invertY: boolean = false,\r\n        samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE,\r\n        type: number = Constants.TEXTURETYPE_FLOAT\r\n    ): RawTexture {\r\n        return new RawTexture(data, width, height, Constants.TEXTUREFORMAT_R, sceneOrEngine, generateMipMaps, invertY, samplingMode, type, Constants.TEXTURE_CREATIONFLAG_STORAGE);\r\n    }\r\n}\r\n"]}