{"version": 3, "file": "webXRDefaultExperience.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/XR/webXRDefaultExperience.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAGhE,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,EAAE,+BAA+B,EAAE,MAAM,4CAA4C,CAAC;AAE7F,OAAO,EAAE,oBAAoB,EAAE,MAAM,iCAAiC,CAAC;AAGvE,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAItD,OAAO,EAAE,iBAAiB,EAAkC,MAAM,8BAA8B,CAAC;AACjG,OAAO,EAAE,kCAAkC,EAAE,MAAM,yCAAyC,CAAC;AAC7F,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC;;GAEG;AACH,MAAM,OAAO,6BAA6B;CA8EzC;AAED;;GAEG;AACH,MAAM,OAAO,sBAAsB;IA+B/B,gBAAuB,CAAC;IAExB;;;;;OAKG;IACI,MAAM,CAAC,WAAW,CAAC,KAAY,EAAE,UAAyC,EAAE;QAC/E,MAAM,MAAM,GAAG,IAAI,sBAAsB,EAAE,CAAC;QAC5C,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;YACnC,MAAM,CAAC,OAAO,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC;QACH,uCAAuC;QACvC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YAC3B,MAAM,SAAS,GAA4B;gBACvC,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,GAAG,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;aAC/B,CAAC;YACF,IAAI,OAAO,CAAC,gBAAgB,EAAE;gBAC1B,IAAI,OAAO,OAAO,CAAC,gBAAgB,KAAK,SAAS,EAAE;oBAC/C,SAAS,CAAC,gBAAgB,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC;iBAC5F;qBAAM;oBACH,SAAS,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;iBACzD;aACJ;YACD,MAAM,CAAC,WAAW,GAAG,IAAI,gBAAgB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;SAC/D;QAED,yBAAyB;QACzB,OAAO,qBAAqB,CAAC,WAAW,CAAC,KAAK,CAAC;aAC1C,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;YACf,MAAM,CAAC,cAAc,GAAG,QAAQ,CAAC;YAEjC,IAAI,OAAO,CAAC,gCAAgC,EAAE;gBAC1C,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,sBAAsB,GAAG,KAAK,CAAC;aAC/D;YAED,yBAAyB;YACzB,MAAM,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,cAAc,EAAE,QAAQ,CAAC,MAAM,EAAE;gBACpE,iBAAiB,EAAE;oBACf,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;iBAC7C;gBACD,GAAG,CAAC,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC;aAClC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;gBAClC,gCAAgC;gBAChC,MAAM,uBAAuB,GAAG;oBAC5B,GAAG,OAAO,CAAC,uBAAuB;oBAClC,OAAO,EAAE,MAAM,CAAC,KAAK;oBACrB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;iBAC7C,CAAC;gBAEF,MAAM,CAAC,gBAAgB,GAAoC,CACvD,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,CAC/C,+BAA+B,CAAC,IAAI,EACpC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EACL,uBAAuB,CACnE,CACJ,CAAC;gBAEF,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;oBAC/B,gDAAgD;oBAChD,MAAM,CAAC,aAAa,GAAuC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,CAC1G,kCAAkC,CAAC,IAAI,EACvC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAClB;wBACxB,WAAW,EAAE,OAAO,CAAC,WAAW;wBAChC,OAAO,EAAE,MAAM,CAAC,KAAK;wBACrB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;wBAC1C,GAAG,OAAO,CAAC,oBAAoB;qBAClC,CACJ,CAAC;oBACF,MAAM,CAAC,aAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;iBACrE;aACJ;YAED,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;gBACjC,gCAAgC;gBAChC,MAAM,CAAC,eAAe,GAAyB,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,CAC9F,oBAAoB,CAAC,IAAI,EACzB,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAChB;oBAC1B,OAAO,EAAE,MAAM,CAAC,KAAK;oBACrB,qBAAqB,EAAE,MAAM,CAAC,gBAAgB;oBAC9C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;oBAC1C,eAAe,EAAE,IAAI;oBACrB,qCAAqC,EAAE,IAAI;oBAC3C,GAAG,OAAO,CAAC,sBAAsB;iBACpC,CACJ,CAAC;aACL;YAED,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;gBAC9B,4BAA4B;gBAC5B,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,CAC/C,iBAAiB,CAAC,IAAI,EACtB,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EACnB;oBACvB,OAAO,EAAE,MAAM,CAAC,KAAK;oBACrB,GAAG,OAAO,CAAC,kBAAkB;iBAChC,EACD,SAAS,EACT,KAAK,CACR,CAAC;aACL;YAED,iCAAiC;YACjC,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAE7G,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;gBAC3B,oCAAoC;gBACpC,OAAO,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;aACxF;iBAAM;gBACH,OAAO;aACV;QACL,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACP,OAAO,MAAM,CAAC;QAClB,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YACb,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACpB,OAAO,MAAM,CAAC;QAClB,CAAC,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;SACjC;QACD,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;SACxB;QACD,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;SAC9B;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;SAC/B;IACL,CAAC;CACJ", "sourcesContent": ["import { WebXRExperienceHelper } from \"./webXRExperienceHelper\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { IWebXRInputOptions } from \"./webXRInput\";\r\nimport { WebXRInput } from \"./webXRInput\";\r\nimport type { IWebXRControllerPointerSelectionOptions } from \"./features/WebXRControllerPointerSelection\";\r\nimport { WebXRControllerPointerSelection } from \"./features/WebXRControllerPointerSelection\";\r\nimport type { IWebXRNearInteractionOptions } from \"./features/WebXRNearInteraction\";\r\nimport { WebXRNearInteraction } from \"./features/WebXRNearInteraction\";\r\nimport type { WebXRRenderTarget } from \"./webXRTypes\";\r\nimport type { WebXREnterExitUIOptions } from \"./webXREnterExitUI\";\r\nimport { WebXREnterExitUI } from \"./webXREnterExitUI\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { WebXRManagedOutputCanvasOptions } from \"./webXRManagedOutputCanvas\";\r\nimport type { IWebXRTeleportationOptions } from \"./features/WebXRControllerTeleportation\";\r\nimport { WebXRHandTracking, type IWebXRHandTrackingOptions } from \"./features/WebXRHandTracking\";\r\nimport { WebXRMotionControllerTeleportation } from \"./features/WebXRControllerTeleportation\";\r\nimport { Logger } from \"../Misc/logger\";\r\n\r\n/**\r\n * Options for the default xr helper\r\n */\r\nexport class WebXRDefaultExperienceOptions {\r\n    /**\r\n     * Enable or disable default UI to enter XR\r\n     */\r\n    public disableDefaultUI?: boolean;\r\n    /**\r\n     * Should pointer selection not initialize.\r\n     * Note that disabling pointer selection also disables teleportation.\r\n     * Defaults to false.\r\n     */\r\n    public disablePointerSelection?: boolean;\r\n    /**\r\n     * Should teleportation not initialize. Defaults to false.\r\n     */\r\n    public disableTeleportation?: boolean;\r\n    /**\r\n     * Should nearInteraction not initialize. Defaults to false.\r\n     */\r\n    public disableNearInteraction?: boolean;\r\n\r\n    /**\r\n     * Should hand tracking be disabled. Defaults to false.\r\n     */\r\n    public disableHandTracking?: boolean;\r\n    /**\r\n     * Floor meshes that will be used for teleport\r\n     */\r\n    public floorMeshes?: Array<AbstractMesh>;\r\n    /**\r\n     * If set to true, the first frame will not be used to reset position\r\n     * The first frame is mainly used when copying transformation from the old camera\r\n     * Mainly used in AR\r\n     */\r\n    public ignoreNativeCameraTransformation?: boolean;\r\n    /**\r\n     * Optional configuration for the XR input object\r\n     */\r\n    public inputOptions?: Partial<IWebXRInputOptions>;\r\n    /**\r\n     * optional configuration for pointer selection\r\n     */\r\n    public pointerSelectionOptions?: Partial<IWebXRControllerPointerSelectionOptions>;\r\n    /**\r\n     * optional configuration for near interaction\r\n     */\r\n    public nearInteractionOptions?: Partial<IWebXRNearInteractionOptions>;\r\n\r\n    /**\r\n     * optional configuration for hand tracking\r\n     */\r\n    public handSupportOptions?: Partial<IWebXRHandTrackingOptions>;\r\n    /**\r\n     * optional configuration for teleportation\r\n     */\r\n    public teleportationOptions?: Partial<IWebXRTeleportationOptions>;\r\n    /**\r\n     * optional configuration for the output canvas\r\n     */\r\n    public outputCanvasOptions?: WebXRManagedOutputCanvasOptions;\r\n    /**\r\n     * optional UI options. This can be used among other to change session mode and reference space type\r\n     */\r\n    public uiOptions?: Partial<WebXREnterExitUIOptions>;\r\n    /**\r\n     * When loading teleportation and pointer select, use stable versions instead of latest.\r\n     */\r\n    public useStablePlugins?: boolean;\r\n\r\n    /**\r\n     * An optional rendering group id that will be set globally for teleportation, pointer selection and default controller meshes\r\n     */\r\n    public renderingGroupId?: number;\r\n\r\n    /**\r\n     * A list of optional features to init the session with\r\n     * If set to true, all features we support will be added\r\n     */\r\n    public optionalFeatures?: boolean | string[];\r\n}\r\n\r\n/**\r\n * Default experience for webxr\r\n */\r\nexport class WebXRDefaultExperience {\r\n    /**\r\n     * Base experience\r\n     */\r\n    public baseExperience: WebXRExperienceHelper;\r\n    /**\r\n     * Enables ui for entering/exiting xr\r\n     */\r\n    public enterExitUI: WebXREnterExitUI;\r\n    /**\r\n     * Input experience extension\r\n     */\r\n    public input: WebXRInput;\r\n    /**\r\n     * Enables laser pointer and selection\r\n     */\r\n    public pointerSelection: WebXRControllerPointerSelection;\r\n    /**\r\n     * Default target xr should render to\r\n     */\r\n    public renderTarget: WebXRRenderTarget;\r\n    /**\r\n     * Enables teleportation\r\n     */\r\n    public teleportation: WebXRMotionControllerTeleportation;\r\n\r\n    /**\r\n     * Enables near interaction for hands/controllers\r\n     */\r\n    public nearInteraction: WebXRNearInteraction;\r\n\r\n    private constructor() {}\r\n\r\n    /**\r\n     * Creates the default xr experience\r\n     * @param scene scene\r\n     * @param options options for basic configuration\r\n     * @returns resulting WebXRDefaultExperience\r\n     */\r\n    public static CreateAsync(scene: Scene, options: WebXRDefaultExperienceOptions = {}) {\r\n        const result = new WebXRDefaultExperience();\r\n        scene.onDisposeObservable.addOnce(() => {\r\n            result.dispose();\r\n        });\r\n        // init the UI right after construction\r\n        if (!options.disableDefaultUI) {\r\n            const uiOptions: WebXREnterExitUIOptions = {\r\n                renderTarget: result.renderTarget,\r\n                ...(options.uiOptions || {}),\r\n            };\r\n            if (options.optionalFeatures) {\r\n                if (typeof options.optionalFeatures === \"boolean\") {\r\n                    uiOptions.optionalFeatures = [\"hit-test\", \"anchors\", \"plane-detection\", \"hand-tracking\"];\r\n                } else {\r\n                    uiOptions.optionalFeatures = options.optionalFeatures;\r\n                }\r\n            }\r\n            result.enterExitUI = new WebXREnterExitUI(scene, uiOptions);\r\n        }\r\n\r\n        // Create base experience\r\n        return WebXRExperienceHelper.CreateAsync(scene)\r\n            .then((xrHelper) => {\r\n                result.baseExperience = xrHelper;\r\n\r\n                if (options.ignoreNativeCameraTransformation) {\r\n                    result.baseExperience.camera.compensateOnFirstFrame = false;\r\n                }\r\n\r\n                // Add controller support\r\n                result.input = new WebXRInput(xrHelper.sessionManager, xrHelper.camera, {\r\n                    controllerOptions: {\r\n                        renderingGroupId: options.renderingGroupId,\r\n                    },\r\n                    ...(options.inputOptions || {}),\r\n                });\r\n\r\n                if (!options.disablePointerSelection) {\r\n                    // Add default pointer selection\r\n                    const pointerSelectionOptions = {\r\n                        ...options.pointerSelectionOptions,\r\n                        xrInput: result.input,\r\n                        renderingGroupId: options.renderingGroupId,\r\n                    };\r\n\r\n                    result.pointerSelection = <WebXRControllerPointerSelection>(\r\n                        result.baseExperience.featuresManager.enableFeature(\r\n                            WebXRControllerPointerSelection.Name,\r\n                            options.useStablePlugins ? \"stable\" : \"latest\",\r\n                            <IWebXRControllerPointerSelectionOptions>pointerSelectionOptions\r\n                        )\r\n                    );\r\n\r\n                    if (!options.disableTeleportation) {\r\n                        // Add default teleportation, including rotation\r\n                        result.teleportation = <WebXRMotionControllerTeleportation>result.baseExperience.featuresManager.enableFeature(\r\n                            WebXRMotionControllerTeleportation.Name,\r\n                            options.useStablePlugins ? \"stable\" : \"latest\",\r\n                            <IWebXRTeleportationOptions>{\r\n                                floorMeshes: options.floorMeshes,\r\n                                xrInput: result.input,\r\n                                renderingGroupId: options.renderingGroupId,\r\n                                ...options.teleportationOptions,\r\n                            }\r\n                        );\r\n                        result.teleportation.setSelectionFeature(result.pointerSelection);\r\n                    }\r\n                }\r\n\r\n                if (!options.disableNearInteraction) {\r\n                    // Add default pointer selection\r\n                    result.nearInteraction = <WebXRNearInteraction>result.baseExperience.featuresManager.enableFeature(\r\n                        WebXRNearInteraction.Name,\r\n                        options.useStablePlugins ? \"stable\" : \"latest\",\r\n                        <IWebXRNearInteractionOptions>{\r\n                            xrInput: result.input,\r\n                            farInteractionFeature: result.pointerSelection,\r\n                            renderingGroupId: options.renderingGroupId,\r\n                            useUtilityLayer: true,\r\n                            enableNearInteractionOnAllControllers: true,\r\n                            ...options.nearInteractionOptions,\r\n                        }\r\n                    );\r\n                }\r\n\r\n                if (!options.disableHandTracking) {\r\n                    // Add default hand tracking\r\n                    result.baseExperience.featuresManager.enableFeature(\r\n                        WebXRHandTracking.Name,\r\n                        options.useStablePlugins ? \"stable\" : \"latest\",\r\n                        <IWebXRHandTrackingOptions>{\r\n                            xrInput: result.input,\r\n                            ...options.handSupportOptions,\r\n                        },\r\n                        undefined,\r\n                        false\r\n                    );\r\n                }\r\n\r\n                // Create the WebXR output target\r\n                result.renderTarget = result.baseExperience.sessionManager.getWebXRRenderTarget(options.outputCanvasOptions);\r\n\r\n                if (!options.disableDefaultUI) {\r\n                    // Create ui for entering/exiting xr\r\n                    return result.enterExitUI.setHelperAsync(result.baseExperience, result.renderTarget);\r\n                } else {\r\n                    return;\r\n                }\r\n            })\r\n            .then(() => {\r\n                return result;\r\n            })\r\n            .catch((error) => {\r\n                Logger.Error(\"Error initializing XR\");\r\n                Logger.Error(error);\r\n                return result;\r\n            });\r\n    }\r\n\r\n    /**\r\n     * Disposes of the experience helper\r\n     */\r\n    public dispose() {\r\n        if (this.baseExperience) {\r\n            this.baseExperience.dispose();\r\n        }\r\n        if (this.input) {\r\n            this.input.dispose();\r\n        }\r\n        if (this.enterExitUI) {\r\n            this.enterExitUI.dispose();\r\n        }\r\n        if (this.renderTarget) {\r\n            this.renderTarget.dispose();\r\n        }\r\n    }\r\n}\r\n"]}