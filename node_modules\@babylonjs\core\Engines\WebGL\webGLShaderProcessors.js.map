{"version": 3, "file": "webGLShaderProcessors.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGL/webGLShaderProcessors.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAOhE,gBAAgB;AAChB,MAAM,OAAO,oBAAoB;IAAjC;QACW,mBAAc,GAAG,cAAc,CAAC,IAAI,CAAC;IAYhD,CAAC;IAVU,aAAa,CAAC,IAAY,EAAE,OAAiB,EAAE,UAAmB,EAAE,iBAAoD,EAAE,MAAkB;QAC/I,oBAAoB;QACpB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,oBAAoB,EAAE;YACxC,iHAAiH;YACjH,MAAM,KAAK,GAAG,oDAAoD,CAAC;YACnE,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;SAClC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ", "sourcesContent": ["import { ShaderLanguage } from \"../../Materials/shaderLanguage\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { IShaderProcessor } from \"../Processors/iShaderProcessor\";\r\nimport type { ShaderProcessingContext } from \"../Processors/shaderProcessingOptions\";\r\n\r\nimport type { ThinEngine } from \"../thinEngine\";\r\n\r\n/** @internal */\r\nexport class WebGLShaderProcessor implements IShaderProcessor {\r\n    public shaderLanguage = ShaderLanguage.GLSL;\r\n\r\n    public postProcessor(code: string, defines: string[], isFragment: boolean, processingContext: Nullable<ShaderProcessingContext>, engine: ThinEngine) {\r\n        // Remove extensions\r\n        if (!engine.getCaps().drawBuffersExtension) {\r\n            // even if enclosed in #if/#endif, IE11 does parse the #extension declaration, so we need to remove it altogether\r\n            const regex = /#extension.+GL_EXT_draw_buffers.+(enable|require)/g;\r\n            code = code.replace(regex, \"\");\r\n        }\r\n\r\n        return code;\r\n    }\r\n}\r\n"]}