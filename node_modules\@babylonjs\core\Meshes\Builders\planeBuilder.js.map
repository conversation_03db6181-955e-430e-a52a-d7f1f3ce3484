{"version": 3, "file": "planeBuilder.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Builders/planeBuilder.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAEzE;;;;;;;;;;GAUG;AACH,MAAM,UAAU,qBAAqB,CAAC,OAA4H;IAC9J,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,MAAM,SAAS,GAAG,EAAE,CAAC;IACrB,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,MAAM,GAAG,GAAG,EAAE,CAAC;IAEf,MAAM,KAAK,GAAW,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;IACzD,MAAM,MAAM,GAAW,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;IAC3D,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,IAAI,UAAU,CAAC,WAAW,CAAC;IAE9G,WAAW;IACX,MAAM,SAAS,GAAG,KAAK,GAAG,GAAG,CAAC;IAC9B,MAAM,UAAU,GAAG,MAAM,GAAG,GAAG,CAAC;IAEhC,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IAC3C,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACzB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1E,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IAC1C,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACzB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1E,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IACzC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACzB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1E,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IAC1C,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACzB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1E,UAAU;IACV,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAEhB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAEhB,QAAQ;IACR,UAAU,CAAC,aAAa,CAAC,eAAe,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IAE/G,SAAS;IACT,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IAEpC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;IACjC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;IAErB,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,WAAW,CACvB,IAAY,EACZ,UAAyK,EAAE,EAC3K,QAAyB,IAAI;IAE7B,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAEpC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACnF,KAAK,CAAC,+BAA+B,GAAG,OAAO,CAAC,eAAe,CAAC;IAEhE,MAAM,UAAU,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAElD,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAEjD,IAAI,OAAO,CAAC,WAAW,EAAE;QACrB,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACpE,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC5D;IAED,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG;IACxB,gEAAgE;IAChE,WAAW;CACd,CAAC;AAEF,UAAU,CAAC,WAAW,GAAG,qBAAqB,CAAC;AAC/C,IAAI,CAAC,WAAW,GAAG,CAAC,IAAY,EAAE,IAAY,EAAE,KAAY,EAAE,SAAmB,EAAE,eAAwB,EAAQ,EAAE;IACjH,MAAM,OAAO,GAAG;QACZ,IAAI;QACJ,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,IAAI;QACZ,eAAe;QACf,SAAS;KACZ,CAAC;IAEF,OAAO,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC7C,CAAC,CAAC", "sourcesContent": ["import type { Scene } from \"../../scene\";\r\nimport type { Vector4 } from \"../../Maths/math.vector\";\r\nimport { Mesh } from \"../mesh\";\r\nimport { VertexData } from \"../mesh.vertexData\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Plane } from \"../../Maths/math.plane\";\r\nimport { CompatibilityOptions } from \"../../Compat/compatibilityOptions\";\r\n\r\n/**\r\n * Creates the VertexData for a Plane\r\n * @param options an object used to set the following optional parameters for the plane, required but can be empty\r\n * * size sets the width and height of the plane to the value of size, optional default 1\r\n * * width sets the width (x direction) of the plane, overwrites the width set by size, optional, default size\r\n * * height sets the height (y direction) of the plane, overwrites the height set by size, optional, default size\r\n * * sideOrientation optional and takes the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n * * frontUvs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the front side, optional, default vector4 (0, 0, 1, 1)\r\n * * backUVs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the back side, optional, default vector4 (0, 0, 1, 1)\r\n * @returns the VertexData of the box\r\n */\r\nexport function CreatePlaneVertexData(options: { size?: number; width?: number; height?: number; sideOrientation?: number; frontUVs?: Vector4; backUVs?: Vector4 }): VertexData {\r\n    const indices = [];\r\n    const positions = [];\r\n    const normals = [];\r\n    const uvs = [];\r\n\r\n    const width: number = options.width || options.size || 1;\r\n    const height: number = options.height || options.size || 1;\r\n    const sideOrientation = options.sideOrientation === 0 ? 0 : options.sideOrientation || VertexData.DEFAULTSIDE;\r\n\r\n    // Vertices\r\n    const halfWidth = width / 2.0;\r\n    const halfHeight = height / 2.0;\r\n\r\n    positions.push(-halfWidth, -halfHeight, 0);\r\n    normals.push(0, 0, -1.0);\r\n    uvs.push(0.0, CompatibilityOptions.UseOpenGLOrientationForUV ? 1.0 : 0.0);\r\n\r\n    positions.push(halfWidth, -halfHeight, 0);\r\n    normals.push(0, 0, -1.0);\r\n    uvs.push(1.0, CompatibilityOptions.UseOpenGLOrientationForUV ? 1.0 : 0.0);\r\n\r\n    positions.push(halfWidth, halfHeight, 0);\r\n    normals.push(0, 0, -1.0);\r\n    uvs.push(1.0, CompatibilityOptions.UseOpenGLOrientationForUV ? 0.0 : 1.0);\r\n\r\n    positions.push(-halfWidth, halfHeight, 0);\r\n    normals.push(0, 0, -1.0);\r\n    uvs.push(0.0, CompatibilityOptions.UseOpenGLOrientationForUV ? 0.0 : 1.0);\r\n\r\n    // Indices\r\n    indices.push(0);\r\n    indices.push(1);\r\n    indices.push(2);\r\n\r\n    indices.push(0);\r\n    indices.push(2);\r\n    indices.push(3);\r\n\r\n    // Sides\r\n    VertexData._ComputeSides(sideOrientation, positions, indices, normals, uvs, options.frontUVs, options.backUVs);\r\n\r\n    // Result\r\n    const vertexData = new VertexData();\r\n\r\n    vertexData.indices = indices;\r\n    vertexData.positions = positions;\r\n    vertexData.normals = normals;\r\n    vertexData.uvs = uvs;\r\n\r\n    return vertexData;\r\n}\r\n\r\n/**\r\n * Creates a plane mesh\r\n * * The parameter `size` sets the size (float) of both sides of the plane at once (default 1)\r\n * * You can set some different plane dimensions by using the parameters `width` and `height` (both by default have the same value of `size`)\r\n * * The parameter `sourcePlane` is a Plane instance. It builds a mesh plane from a Math plane\r\n * * You can also set the mesh side orientation with the values : BABYLON.Mesh.FRONTSIDE (default), BABYLON.Mesh.BACKSIDE or BABYLON.Mesh.DOUBLESIDE\r\n * * If you create a double-sided mesh, you can choose what parts of the texture image to crop and stick respectively on the front and the back sides with the parameters `frontUVs` and `backUVs` (Vector4). Detail here : https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation\r\n * * The mesh can be set to updatable with the boolean parameter `updatable` (default false) if its internal geometry is supposed to change once created\r\n * @param name defines the name of the mesh\r\n * @param options defines the options used to create the mesh\r\n * @param scene defines the hosting scene\r\n * @returns the plane mesh\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#plane\r\n */\r\nexport function CreatePlane(\r\n    name: string,\r\n    options: { size?: number; width?: number; height?: number; sideOrientation?: number; frontUVs?: Vector4; backUVs?: Vector4; updatable?: boolean; sourcePlane?: Plane } = {},\r\n    scene: Nullable<Scene> = null\r\n): Mesh {\r\n    const plane = new Mesh(name, scene);\r\n\r\n    options.sideOrientation = Mesh._GetDefaultSideOrientation(options.sideOrientation);\r\n    plane._originalBuilderSideOrientation = options.sideOrientation;\r\n\r\n    const vertexData = CreatePlaneVertexData(options);\r\n\r\n    vertexData.applyToMesh(plane, options.updatable);\r\n\r\n    if (options.sourcePlane) {\r\n        plane.translate(options.sourcePlane.normal, -options.sourcePlane.d);\r\n        plane.setDirection(options.sourcePlane.normal.scale(-1));\r\n    }\r\n\r\n    return plane;\r\n}\r\n\r\n/**\r\n * Class containing static functions to help procedurally build meshes\r\n * @deprecated use the function directly from the module\r\n */\r\nexport const PlaneBuilder = {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    CreatePlane,\r\n};\r\n\r\nVertexData.CreatePlane = CreatePlaneVertexData;\r\nMesh.CreatePlane = (name: string, size: number, scene: Scene, updatable?: boolean, sideOrientation?: number): Mesh => {\r\n    const options = {\r\n        size,\r\n        width: size,\r\n        height: size,\r\n        sideOrientation,\r\n        updatable,\r\n    };\r\n\r\n    return CreatePlane(name, options, scene);\r\n};\r\n"]}