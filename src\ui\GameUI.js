export class GameUI {
    constructor(game) {
        this.game = game;
        this.elements = {};
        this.selectedHandTile = null;

        this.initializeElements();
        this.setupEventListeners();
        this.setupGameCallbacks();
    }

    initializeElements() {
        // Get all UI elements
        this.elements = {
            currentPlayer: document.getElementById('currentPlayer'),
            player1Score: document.getElementById('player1Score'),
            player2Score: document.getElementById('player2Score'),
            drawTileBtn: document.getElementById('drawTileBtn'),
            passBtn: document.getElementById('passBtn'),
            newGameBtn: document.getElementById('newGameBtn'),
            handTiles: document.getElementById('handTiles'),
            gameMessages: document.getElementById('gameMessages'),
            messageText: document.getElementById('messageText')
        };
    }

    setupEventListeners() {
        // Draw tile button
        this.elements.drawTileBtn.addEventListener('click', () => {
            this.game.drawTile();
            this.updatePlayerHand();
        });

        // Pass button
        this.elements.passBtn.addEventListener('click', () => {
            this.game.passTurn();
        });

        // New game button
        this.elements.newGameBtn.addEventListener('click', () => {
            this.game.newGame();
            this.updatePlayerHand();
        });
    }

    setupGameCallbacks() {
        // Set up game event callbacks
        this.game.onPlayerChange = (player) => {
            this.updateCurrentPlayer(player);
            this.updatePlayerHand();
        };

        this.game.onScoreUpdate = (scores) => {
            this.updateScores(scores);
        };

        this.game.onMessage = (message) => {
            this.showMessage(message);
        };

        this.game.onGameStateChange = (state) => {
            this.updateGameState(state);
        };
    }

    updateCurrentPlayer(player) {
        this.elements.currentPlayer.textContent = `${player.name} ist dran`;

        // Update button states
        const isCurrentPlayerTurn = player.id === 1; // Assuming player 1 is human
        this.elements.drawTileBtn.disabled = !isCurrentPlayerTurn;
        this.elements.passBtn.disabled = !isCurrentPlayerTurn;
    }

    updateScores(scores) {
        scores.forEach(score => {
            if (score.id === 1) {
                this.elements.player1Score.textContent = score.score;
            } else if (score.id === 2) {
                this.elements.player2Score.textContent = score.score;
            }
        });
    }

    updatePlayerHand() {
        const currentPlayer = this.game.getCurrentPlayer();
        const handTiles = this.elements.handTiles;

        // Clear existing tiles
        handTiles.innerHTML = '';

        // Only show hand for player 1 (human player)
        if (currentPlayer.id === 1) {
            currentPlayer.hand.forEach(tile => {
                const tileElement = this.createHandTileElement(tile);
                handTiles.appendChild(tileElement);
            });
        } else {
            // Show tile count for AI player
            const tileCount = document.createElement('div');
            tileCount.className = 'ai-hand-info';
            tileCount.textContent = `${currentPlayer.name} hat ${currentPlayer.hand.length} Steine`;
            handTiles.appendChild(tileCount);
        }
    }

    createHandTileElement(tile) {
        const tileElement = document.createElement('div');
        tileElement.className = 'hand-tile';
        tileElement.innerHTML = `
            <div class="tile-numbers">${tile.data.numbers.join('-')}</div>
            <div class="tile-value">${tile.data.value} Punkte</div>
        `;

        // Store tile reference
        tileElement.tile = tile;

        // Add click handler
        tileElement.addEventListener('click', () => {
            this.selectHandTile(tile, tileElement);
        });

        // Add double-click handler to play tile
        tileElement.addEventListener('dblclick', () => {
            this.playSelectedTile(tile);
        });

        return tileElement;
    }

    selectHandTile(tile, element) {
        // Deselect previous tile
        if (this.selectedHandTile) {
            this.selectedHandTile.element.classList.remove('selected');
        }

        // Select new tile
        this.selectedHandTile = { tile, element };
        element.classList.add('selected');

        // Also select in game
        this.game.selectTile(tile);

        // Highlight valid positions on board
        this.highlightValidPositions(tile);
    }

    playSelectedTile(tile) {
        if (this.game.canPlayTile(tile)) {
            const success = this.game.playTile(tile);
            if (success) {
                this.selectedHandTile = null;
                this.updatePlayerHand();
                this.clearHighlights();
            }
        } else {
            this.showMessage('Dieser Stein kann nicht gespielt werden!');
        }
    }

    highlightValidPositions(tile) {
        // This would highlight valid positions on the 3D board
        // For now, we'll just clear any existing highlights
        this.clearHighlights();

        // In a full implementation, you would:
        // 1. Get valid positions from game.board.getValidPlacementPositions(tile)
        // 2. Create visual indicators on the 3D scene
        // 3. Make them clickable to place the tile
    }

    clearHighlights() {
        // Clear any position highlights on the 3D board
        // This would remove visual indicators from the scene
    }

    showMessage(message, duration = 3000) {
        this.elements.messageText.textContent = message;
        this.elements.gameMessages.classList.add('show');

        // Auto-hide after duration
        setTimeout(() => {
            this.elements.gameMessages.classList.remove('show');
        }, duration);
    }

    updateGameState(state) {
        switch (state) {
            case 'waiting':
                this.showMessage('Spiel wird vorbereitet...', 1000);
                break;
            case 'playing':
                this.elements.gameMessages.classList.remove('show');
                break;
            case 'finished':
                const winner = this.game.getWinner();
                if (winner) {
                    this.showMessage(`${winner.name} hat gewonnen!`, 5000);
                }
                break;
        }
    }

    // Handle 3D scene interactions
    onTileClicked(tile) {
        if (tile.isPlaced) {
            // Clicked on a board tile - could show info or allow rotation
            this.showMessage(`Stein: ${tile.data.numbers.join('-')} (${tile.data.value} Punkte)`);
        } else {
            // Clicked on a hand tile
            const handTileElement = this.findHandTileElement(tile);
            if (handTileElement) {
                this.selectHandTile(tile, handTileElement);
            }
        }
    }

    onBoardClicked(position) {
        if (this.selectedHandTile) {
            const tile = this.selectedHandTile.tile;
            if (this.game.canPlayTile(tile, position)) {
                const success = this.game.playTile(tile, position);
                if (success) {
                    this.selectedHandTile = null;
                    this.updatePlayerHand();
                    this.clearHighlights();
                }
            } else {
                this.showMessage('Stein kann hier nicht platziert werden!');
            }
        }
    }

    findHandTileElement(tile) {
        const handTiles = this.elements.handTiles.children;
        for (let element of handTiles) {
            if (element.tile === tile) {
                return element;
            }
        }
        return null;
    }

    // Initialize the UI with current game state
    initialize() {
        this.updateCurrentPlayer(this.game.getCurrentPlayer());
        this.updateScores(this.game.players.map(p => ({ id: p.id, name: p.name, score: p.score })));
        this.updatePlayerHand();
        this.updateGameState(this.game.gameState);
    }

    // Clean up UI
    dispose() {
        // Remove event listeners and clean up
        this.clearHighlights();
        this.selectedHandTile = null;
    }
}
