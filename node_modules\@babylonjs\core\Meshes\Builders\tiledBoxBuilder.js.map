{"version": 3, "file": "tiledBoxBuilder.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Builders/tiledBoxBuilder.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AACnE,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,0BAA0B,EAAE,MAAM,qBAAqB,CAAC;AACjE,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAEzE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,MAAM,UAAU,wBAAwB,CAAC,OAcxC;IACG,MAAM,OAAO,GAAG,CAAC,CAAC;IAElB,MAAM,MAAM,GAAc,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,CAAU,CAAC,CAAC,CAAC;IAClE,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IAEtC,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;IAEjD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;IACjD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;IACnD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;IACjD,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;IAC7D,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;IAC/D,MAAM,MAAM,GAAG,OAAO,CAAC,eAAe,IAAI,CAAC,CAAC;IAC5C,MAAM,MAAM,GAAG,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC;IAE1C,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,IAAI,UAAU,CAAC,WAAW,CAAC;IAE9G,0CAA0C;IAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;QAC9B,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YACzB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACvC;QACD,IAAI,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YAC3C,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC1C;KACJ;IAED,MAAM,SAAS,GAAG,KAAK,GAAG,CAAC,CAAC;IAC5B,MAAM,UAAU,GAAG,MAAM,GAAG,CAAC,CAAC;IAC9B,MAAM,SAAS,GAAG,KAAK,GAAG,CAAC,CAAC;IAE5B,MAAM,cAAc,GAAsB,EAAE,CAAC;IAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACxB,gBAAgB;QAChB,cAAc,CAAC,CAAC,CAAC,GAAG,0BAA0B,CAAC;YAC3C,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,SAAS;YACpB,UAAU,EAAE,UAAU;YACtB,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,MAAM;YACd,aAAa,EAAE,MAAM;YACrB,eAAe,EAAE,MAAM;YACvB,eAAe,EAAE,eAAe;SACnC,CAAC,CAAC;KACN;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACxB,OAAO;QACP,cAAc,CAAC,CAAC,CAAC,GAAG,0BAA0B,CAAC;YAC3C,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,SAAS;YACpB,UAAU,EAAE,UAAU;YACtB,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,MAAM;YACd,aAAa,EAAE,MAAM;YACrB,eAAe,EAAE,MAAM;YACvB,eAAe,EAAE,eAAe;SACnC,CAAC,CAAC;KACN;IAED,IAAI,UAAU,GAAG,MAAM,CAAC;IACxB,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;QACxB,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC;KACzB;SAAM,IAAI,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE;QAC5B,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;KAC5B;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACxB,gBAAgB;QAChB,cAAc,CAAC,CAAC,CAAC,GAAG,0BAA0B,CAAC;YAC3C,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,SAAS;YACpB,UAAU,EAAE,UAAU;YACtB,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,KAAK;YACb,aAAa,EAAE,UAAU;YACzB,eAAe,EAAE,MAAM;YACvB,eAAe,EAAE,eAAe;SACnC,CAAC,CAAC;KACN;IAED,IAAI,SAAS,GAAkB,EAAE,CAAC;IAClC,IAAI,OAAO,GAAkB,EAAE,CAAC;IAChC,IAAI,GAAG,GAAkB,EAAE,CAAC;IAC5B,IAAI,OAAO,GAAkB,EAAE,CAAC;IAChC,MAAM,MAAM,GAAkB,EAAE,CAAC;IACjC,MAAM,aAAa,GAA0B,EAAE,CAAC;IAChD,MAAM,WAAW,GAA0B,EAAE,CAAC;IAE9C,MAAM,SAAS,GAAyB,EAAE,CAAC;IAC3C,IAAI,EAAE,GAAW,CAAC,CAAC;IAEnB,IAAI,EAAE,GAAW,CAAC,CAAC;IAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;QAC9B,MAAM,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,SAAU,CAAC,MAAM,CAAC;QAChD,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACtB,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9B,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,SAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,SAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1J,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,OAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,OAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SACrJ;QACD,MAAM;QACN,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,MAAM,CAAC;QACnC,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE;YAC5B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,CAAC,CAAC,CAAC;YACxF,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAEhG,IAAI,oBAAoB,CAAC,yBAAyB,EAAE;gBAChD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aACnD;SACJ;QACD,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAgB,cAAc,CAAC,CAAC,CAAC,CAAC,OAAQ,CAAC,GAAG,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAC/F,EAAE,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC9B,IAAI,UAAU,EAAE;YACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACxB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACnF;SACJ;KACJ;IAED,MAAM,IAAI,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;IAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACxC,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC;SACvB,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SAC/D,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SAC3C,MAAM,CAAC,CAAC,WAA0B,EAAE,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC;IAChG,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC;SACnB,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACrD,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SAC3C,MAAM,CAAC,CAAC,WAA0B,EAAE,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC;IAChG,SAAS,GAAG,SAAS,CAAC,MAAM,CACxB,aAAa,CAAC,CAAC,CAAC;SACX,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACpC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SAC3C,MAAM,CAAC,CAAC,WAA0B,EAAE,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAClG,CAAC;IACF,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,WAA0B,EAAE,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAEhL,MAAM,IAAI,GAAG,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC7C,SAAS,GAAG,SAAS,CAAC,MAAM,CACxB,aAAa,CAAC,CAAC,CAAC;SACX,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SAC/D,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SAC3C,MAAM,CAAC,CAAC,WAA0B,EAAE,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAClG,CAAC;IACF,OAAO,GAAG,OAAO,CAAC,MAAM,CACpB,WAAW,CAAC,CAAC,CAAC;SACT,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACrD,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SAC3C,MAAM,CAAC,CAAC,WAA0B,EAAE,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAClG,CAAC;IACF,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5C,SAAS,GAAG,SAAS,CAAC,MAAM,CACxB,aAAa,CAAC,CAAC,CAAC;SACX,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACpE,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SAC3C,MAAM,CAAC,CAAC,WAA0B,EAAE,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAClG,CAAC;IACF,OAAO,GAAG,OAAO,CAAC,MAAM,CACpB,WAAW,CAAC,CAAC,CAAC;SACT,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACrD,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SAC3C,MAAM,CAAC,CAAC,WAA0B,EAAE,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAClG,CAAC;IAEF,MAAM,IAAI,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IAC3C,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5C,SAAS,GAAG,SAAS,CAAC,MAAM,CACxB,aAAa,CAAC,CAAC,CAAC;SACX,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SAC/D,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SAC3C,MAAM,CAAC,CAAC,WAA0B,EAAE,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAClG,CAAC;IACF,OAAO,GAAG,OAAO,CAAC,MAAM,CACpB,WAAW,CAAC,CAAC,CAAC;SACT,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACrD,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SAC3C,MAAM,CAAC,CAAC,WAA0B,EAAE,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAClG,CAAC;IACF,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC7C,SAAS,GAAG,SAAS,CAAC,MAAM,CACxB,aAAa,CAAC,CAAC,CAAC;SACX,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACpE,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SAC3C,MAAM,CAAC,CAAC,WAA0B,EAAE,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAClG,CAAC;IACF,OAAO,GAAG,OAAO,CAAC,MAAM,CACpB,WAAW,CAAC,CAAC,CAAC;SACT,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACrD,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SAC3C,MAAM,CAAC,CAAC,WAA0B,EAAE,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAClG,CAAC;IAEF,QAAQ;IACR,UAAU,CAAC,aAAa,CAAC,eAAe,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE5E,SAAS;IACT,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IAEpC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;IACjC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;IAErB,IAAI,UAAU,EAAE;QACZ,MAAM,WAAW,GAAG,eAAe,KAAK,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC/F,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC;KACnC;IAED,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAM,UAAU,cAAc,CAC1B,IAAY,EACZ,OAcC,EACD,QAAyB,IAAI;IAE7B,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAElC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACnF,GAAG,CAAC,+BAA+B,GAAG,OAAO,CAAC,eAAe,CAAC;IAE9D,MAAM,UAAU,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;IAErD,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAE/C,OAAO,GAAG,CAAC;AACf,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG;IAC3B,gEAAgE;IAChE,cAAc;CACjB,CAAC;AAEF,UAAU,CAAC,cAAc,GAAG,wBAAwB,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Matrix, Vector3, Vector4 } from \"../../Maths/math.vector\";\r\nimport { Color4 } from \"../../Maths/math.color\";\r\nimport { Mesh } from \"../mesh\";\r\nimport { VertexData } from \"../mesh.vertexData\";\r\nimport { CreateTiledPlaneVertexData } from \"./tiledPlaneBuilder\";\r\nimport { CompatibilityOptions } from \"../../Compat/compatibilityOptions\";\r\n\r\n/**\r\n * Creates the VertexData for a tiled box\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set/tiled_box\r\n * @param options an object used to set the following optional parameters for the tiled box, required but can be empty\r\n * * pattern sets the rotation or reflection pattern for the tiles,\r\n * * size of the box\r\n * * width of the box, overwrites size\r\n * * height of the box, overwrites size\r\n * * depth of the box, overwrites size\r\n * * tileSize sets the size of a tile\r\n * * tileWidth sets the tile width and overwrites tileSize\r\n * * tileHeight sets the tile width and overwrites tileSize\r\n * * faceUV an array of 6 Vector4 elements used to set different images to each box side\r\n * * faceColors an array of 6 Color3 elements used to set different colors to each box side\r\n * * alignHorizontal places whole tiles aligned to the center, left or right of a row\r\n * * alignVertical places whole tiles aligned to the center, left or right of a column\r\n * @param options.pattern\r\n * @param options.size\r\n * @param options.width\r\n * @param options.height\r\n * @param options.depth\r\n * @param options.tileSize\r\n * @param options.tileWidth\r\n * @param options.tileHeight\r\n * @param options.faceUV\r\n * @param options.faceColors\r\n * @param options.alignHorizontal\r\n * @param options.alignVertical\r\n * @param options.sideOrientation\r\n * * sideOrientation optional and takes the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n * @returns the VertexData of the TiledBox\r\n */\r\nexport function CreateTiledBoxVertexData(options: {\r\n    pattern?: number;\r\n    size?: number;\r\n    width?: number;\r\n    height?: number;\r\n    depth?: number;\r\n    tileSize?: number;\r\n    tileWidth?: number;\r\n    tileHeight?: number;\r\n    faceUV?: Vector4[];\r\n    faceColors?: Color4[];\r\n    alignHorizontal?: number;\r\n    alignVertical?: number;\r\n    sideOrientation?: number;\r\n}): VertexData {\r\n    const nbFaces = 6;\r\n\r\n    const faceUV: Vector4[] = options.faceUV || new Array<Vector4>(6);\r\n    const faceColors = options.faceColors;\r\n\r\n    const flipTile = options.pattern || Mesh.NO_FLIP;\r\n\r\n    const width = options.width || options.size || 1;\r\n    const height = options.height || options.size || 1;\r\n    const depth = options.depth || options.size || 1;\r\n    const tileWidth = options.tileWidth || options.tileSize || 1;\r\n    const tileHeight = options.tileHeight || options.tileSize || 1;\r\n    const alignH = options.alignHorizontal || 0;\r\n    const alignV = options.alignVertical || 0;\r\n\r\n    const sideOrientation = options.sideOrientation === 0 ? 0 : options.sideOrientation || VertexData.DEFAULTSIDE;\r\n\r\n    // default face colors and UV if undefined\r\n    for (let f = 0; f < nbFaces; f++) {\r\n        if (faceUV[f] === undefined) {\r\n            faceUV[f] = new Vector4(0, 0, 1, 1);\r\n        }\r\n        if (faceColors && faceColors[f] === undefined) {\r\n            faceColors[f] = new Color4(1, 1, 1, 1);\r\n        }\r\n    }\r\n\r\n    const halfWidth = width / 2;\r\n    const halfHeight = height / 2;\r\n    const halfDepth = depth / 2;\r\n\r\n    const faceVertexData: Array<VertexData> = [];\r\n\r\n    for (let f = 0; f < 2; f++) {\r\n        //front and back\r\n        faceVertexData[f] = CreateTiledPlaneVertexData({\r\n            pattern: flipTile,\r\n            tileWidth: tileWidth,\r\n            tileHeight: tileHeight,\r\n            width: width,\r\n            height: height,\r\n            alignVertical: alignV,\r\n            alignHorizontal: alignH,\r\n            sideOrientation: sideOrientation,\r\n        });\r\n    }\r\n\r\n    for (let f = 2; f < 4; f++) {\r\n        //sides\r\n        faceVertexData[f] = CreateTiledPlaneVertexData({\r\n            pattern: flipTile,\r\n            tileWidth: tileWidth,\r\n            tileHeight: tileHeight,\r\n            width: depth,\r\n            height: height,\r\n            alignVertical: alignV,\r\n            alignHorizontal: alignH,\r\n            sideOrientation: sideOrientation,\r\n        });\r\n    }\r\n\r\n    let baseAlignV = alignV;\r\n    if (alignV === Mesh.BOTTOM) {\r\n        baseAlignV = Mesh.TOP;\r\n    } else if (alignV === Mesh.TOP) {\r\n        baseAlignV = Mesh.BOTTOM;\r\n    }\r\n\r\n    for (let f = 4; f < 6; f++) {\r\n        //top and bottom\r\n        faceVertexData[f] = CreateTiledPlaneVertexData({\r\n            pattern: flipTile,\r\n            tileWidth: tileWidth,\r\n            tileHeight: tileHeight,\r\n            width: width,\r\n            height: depth,\r\n            alignVertical: baseAlignV,\r\n            alignHorizontal: alignH,\r\n            sideOrientation: sideOrientation,\r\n        });\r\n    }\r\n\r\n    let positions: Array<number> = [];\r\n    let normals: Array<number> = [];\r\n    let uvs: Array<number> = [];\r\n    let indices: Array<number> = [];\r\n    const colors: Array<number> = [];\r\n    const facePositions: Array<Array<Vector3>> = [];\r\n    const faceNormals: Array<Array<Vector3>> = [];\r\n\r\n    const newFaceUV: Array<Array<number>> = [];\r\n    let lu: number = 0;\r\n\r\n    let li: number = 0;\r\n\r\n    for (let f = 0; f < nbFaces; f++) {\r\n        const len = faceVertexData[f].positions!.length;\r\n        facePositions[f] = [];\r\n        faceNormals[f] = [];\r\n        for (let p = 0; p < len / 3; p++) {\r\n            facePositions[f].push(new Vector3(faceVertexData[f].positions![3 * p], faceVertexData[f].positions![3 * p + 1], faceVertexData[f].positions![3 * p + 2]));\r\n            faceNormals[f].push(new Vector3(faceVertexData[f].normals![3 * p], faceVertexData[f].normals![3 * p + 1], faceVertexData[f].normals![3 * p + 2]));\r\n        }\r\n        // uvs\r\n        lu = faceVertexData[f].uvs!.length;\r\n        newFaceUV[f] = [];\r\n        for (let i = 0; i < lu; i += 2) {\r\n            newFaceUV[f][i] = faceUV[f].x + (faceUV[f].z - faceUV[f].x) * faceVertexData[f].uvs![i];\r\n            newFaceUV[f][i + 1] = faceUV[f].y + (faceUV[f].w - faceUV[f].y) * faceVertexData[f].uvs![i + 1];\r\n\r\n            if (CompatibilityOptions.UseOpenGLOrientationForUV) {\r\n                newFaceUV[f][i + 1] = 1.0 - newFaceUV[f][i + 1];\r\n            }\r\n        }\r\n        uvs = uvs.concat(newFaceUV[f]);\r\n\r\n        indices = indices.concat(<Array<number>>faceVertexData[f].indices!.map((x: number) => x + li));\r\n        li += facePositions[f].length;\r\n        if (faceColors) {\r\n            for (let c = 0; c < 4; c++) {\r\n                colors.push(faceColors[f].r, faceColors[f].g, faceColors[f].b, faceColors[f].a);\r\n            }\r\n        }\r\n    }\r\n\r\n    const vec0 = new Vector3(0, 0, halfDepth);\r\n    const mtrx0 = Matrix.RotationY(Math.PI);\r\n    positions = facePositions[0]\r\n        .map((entry) => Vector3.TransformNormal(entry, mtrx0).add(vec0))\r\n        .map((entry) => [entry.x, entry.y, entry.z])\r\n        .reduce((accumulator: Array<number>, currentValue) => accumulator.concat(currentValue), []);\r\n    normals = faceNormals[0]\r\n        .map((entry) => Vector3.TransformNormal(entry, mtrx0))\r\n        .map((entry) => [entry.x, entry.y, entry.z])\r\n        .reduce((accumulator: Array<number>, currentValue) => accumulator.concat(currentValue), []);\r\n    positions = positions.concat(\r\n        facePositions[1]\r\n            .map((entry) => entry.subtract(vec0))\r\n            .map((entry) => [entry.x, entry.y, entry.z])\r\n            .reduce((accumulator: Array<number>, currentValue) => accumulator.concat(currentValue), [])\r\n    );\r\n    normals = normals.concat(faceNormals[1].map((entry) => [entry.x, entry.y, entry.z]).reduce((accumulator: Array<number>, currentValue) => accumulator.concat(currentValue), []));\r\n\r\n    const vec2 = new Vector3(halfWidth, 0, 0);\r\n    const mtrx2 = Matrix.RotationY(-Math.PI / 2);\r\n    positions = positions.concat(\r\n        facePositions[2]\r\n            .map((entry) => Vector3.TransformNormal(entry, mtrx2).add(vec2))\r\n            .map((entry) => [entry.x, entry.y, entry.z])\r\n            .reduce((accumulator: Array<number>, currentValue) => accumulator.concat(currentValue), [])\r\n    );\r\n    normals = normals.concat(\r\n        faceNormals[2]\r\n            .map((entry) => Vector3.TransformNormal(entry, mtrx2))\r\n            .map((entry) => [entry.x, entry.y, entry.z])\r\n            .reduce((accumulator: Array<number>, currentValue) => accumulator.concat(currentValue), [])\r\n    );\r\n    const mtrx3 = Matrix.RotationY(Math.PI / 2);\r\n    positions = positions.concat(\r\n        facePositions[3]\r\n            .map((entry) => Vector3.TransformNormal(entry, mtrx3).subtract(vec2))\r\n            .map((entry) => [entry.x, entry.y, entry.z])\r\n            .reduce((accumulator: Array<number>, currentValue) => accumulator.concat(currentValue), [])\r\n    );\r\n    normals = normals.concat(\r\n        faceNormals[3]\r\n            .map((entry) => Vector3.TransformNormal(entry, mtrx3))\r\n            .map((entry) => [entry.x, entry.y, entry.z])\r\n            .reduce((accumulator: Array<number>, currentValue) => accumulator.concat(currentValue), [])\r\n    );\r\n\r\n    const vec4 = new Vector3(0, halfHeight, 0);\r\n    const mtrx4 = Matrix.RotationX(Math.PI / 2);\r\n    positions = positions.concat(\r\n        facePositions[4]\r\n            .map((entry) => Vector3.TransformNormal(entry, mtrx4).add(vec4))\r\n            .map((entry) => [entry.x, entry.y, entry.z])\r\n            .reduce((accumulator: Array<number>, currentValue) => accumulator.concat(currentValue), [])\r\n    );\r\n    normals = normals.concat(\r\n        faceNormals[4]\r\n            .map((entry) => Vector3.TransformNormal(entry, mtrx4))\r\n            .map((entry) => [entry.x, entry.y, entry.z])\r\n            .reduce((accumulator: Array<number>, currentValue) => accumulator.concat(currentValue), [])\r\n    );\r\n    const mtrx5 = Matrix.RotationX(-Math.PI / 2);\r\n    positions = positions.concat(\r\n        facePositions[5]\r\n            .map((entry) => Vector3.TransformNormal(entry, mtrx5).subtract(vec4))\r\n            .map((entry) => [entry.x, entry.y, entry.z])\r\n            .reduce((accumulator: Array<number>, currentValue) => accumulator.concat(currentValue), [])\r\n    );\r\n    normals = normals.concat(\r\n        faceNormals[5]\r\n            .map((entry) => Vector3.TransformNormal(entry, mtrx5))\r\n            .map((entry) => [entry.x, entry.y, entry.z])\r\n            .reduce((accumulator: Array<number>, currentValue) => accumulator.concat(currentValue), [])\r\n    );\r\n\r\n    // sides\r\n    VertexData._ComputeSides(sideOrientation, positions, indices, normals, uvs);\r\n\r\n    // Result\r\n    const vertexData = new VertexData();\r\n\r\n    vertexData.indices = indices;\r\n    vertexData.positions = positions;\r\n    vertexData.normals = normals;\r\n    vertexData.uvs = uvs;\r\n\r\n    if (faceColors) {\r\n        const totalColors = sideOrientation === VertexData.DOUBLESIDE ? colors.concat(colors) : colors;\r\n        vertexData.colors = totalColors;\r\n    }\r\n\r\n    return vertexData;\r\n}\r\n\r\n/**\r\n * Creates a tiled box mesh\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set/tiled_box\r\n * @param name defines the name of the mesh\r\n * @param options an object used to set the following optional parameters for the tiled box, required but can be empty\r\n * * pattern sets the rotation or reflection pattern for the tiles,\r\n * * size of the box\r\n * * width of the box, overwrites size\r\n * * height of the box, overwrites size\r\n * * depth of the box, overwrites size\r\n * * tileSize sets the size of a tile\r\n * * tileWidth sets the tile width and overwrites tileSize\r\n * * tileHeight sets the tile width and overwrites tileSize\r\n * * faceUV an array of 6 Vector4 elements used to set different images to each box side\r\n * * faceColors an array of 6 Color3 elements used to set different colors to each box side\r\n * * alignHorizontal places whole tiles aligned to the center, left or right of a row\r\n * * alignVertical places whole tiles aligned to the center, left or right of a column\r\n * * sideOrientation optional and takes the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n * @param options.pattern\r\n * @param options.width\r\n * @param options.height\r\n * @param options.depth\r\n * @param options.tileSize\r\n * @param options.tileWidth\r\n * @param options.tileHeight\r\n * @param options.alignHorizontal\r\n * @param options.alignVertical\r\n * @param options.faceUV\r\n * @param options.faceColors\r\n * @param options.sideOrientation\r\n * @param options.updatable\r\n * @param scene defines the hosting scene\r\n * @returns the box mesh\r\n */\r\nexport function CreateTiledBox(\r\n    name: string,\r\n    options: {\r\n        pattern?: number;\r\n        width?: number;\r\n        height?: number;\r\n        depth?: number;\r\n        tileSize?: number;\r\n        tileWidth?: number;\r\n        tileHeight?: number;\r\n        alignHorizontal?: number;\r\n        alignVertical?: number;\r\n        faceUV?: Vector4[];\r\n        faceColors?: Color4[];\r\n        sideOrientation?: number;\r\n        updatable?: boolean;\r\n    },\r\n    scene: Nullable<Scene> = null\r\n): Mesh {\r\n    const box = new Mesh(name, scene);\r\n\r\n    options.sideOrientation = Mesh._GetDefaultSideOrientation(options.sideOrientation);\r\n    box._originalBuilderSideOrientation = options.sideOrientation;\r\n\r\n    const vertexData = CreateTiledBoxVertexData(options);\r\n\r\n    vertexData.applyToMesh(box, options.updatable);\r\n\r\n    return box;\r\n}\r\n\r\n/**\r\n * Class containing static functions to help procedurally build meshes\r\n * @deprecated use CreateTiledBox instead\r\n */\r\nexport const TiledBoxBuilder = {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    CreateTiledBox,\r\n};\r\n\r\nVertexData.CreateTiledBox = CreateTiledBoxVertexData;\r\n"]}