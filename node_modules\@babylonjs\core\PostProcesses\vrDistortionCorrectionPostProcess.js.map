{"version": 3, "file": "vrDistortionCorrectionPostProcess.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/vrDistortionCorrectionPostProcess.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAI/C,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,OAAO,4CAA4C,CAAC;AAGpD;;GAEG;AACH,MAAM,OAAO,iCAAkC,SAAQ,WAAW;IAS9D;;;OAGG;IACI,YAAY;QACf,OAAO,mCAAmC,CAAC;IAC/C,CAAC;IAED;;;;;;OAMG;IACH,YAAY,IAAY,EAAE,MAAwB,EAAE,UAAmB,EAAE,SAA0B;QAC/F,KAAK,CAAC,IAAI,EAAE,wBAAwB,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,cAAc,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,sBAAsB,EAAE,MAAM,EAAE,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAEzK,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC,WAAW,CAAC;QAChD,IAAI,CAAC,uBAAuB,GAAG,SAAS,CAAC,sBAAsB,CAAC;QAChE,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;QAExC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;YAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,GAAG,IAAI,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;YACvI,IAAI,CAAC,WAAW,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,iBAAiB,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;QACpI,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAc,EAAE,EAAE;YAC1C,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACvE,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpE,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;QACrJ,CAAC,CAAC,CAAC;IACP,CAAC;CACJ", "sourcesContent": ["import { Vector2 } from \"../Maths/math.vector\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { VRCameraMetrics } from \"../Cameras/VR/vrCameraMetrics\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { PostProcess } from \"./postProcess\";\r\n\r\nimport \"../Shaders/vrDistortionCorrection.fragment\";\r\nimport type { Nullable } from \"../types\";\r\n\r\n/**\r\n * VRDistortionCorrectionPostProcess used for mobile VR\r\n */\r\nexport class VRDistortionCorrectionPostProcess extends PostProcess {\r\n    private _isRightEye: boolean;\r\n    private _distortionFactors: number[];\r\n    private _postProcessScaleFactor: number;\r\n    private _lensCenterOffset: number;\r\n    private _scaleIn: Vector2;\r\n    private _scaleFactor: Vector2;\r\n    private _lensCenter: Vector2;\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"VRDistortionCorrectionPostProcess\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"VRDistortionCorrectionPostProcess\";\r\n    }\r\n\r\n    /**\r\n     * Initializes the VRDistortionCorrectionPostProcess\r\n     * @param name The name of the effect.\r\n     * @param camera The camera to apply the render pass to.\r\n     * @param isRightEye If this is for the right eye distortion\r\n     * @param vrMetrics All the required metrics for the VR camera\r\n     */\r\n    constructor(name: string, camera: Nullable<Camera>, isRightEye: boolean, vrMetrics: VRCameraMetrics) {\r\n        super(name, \"vrDistortionCorrection\", [\"LensCenter\", \"Scale\", \"ScaleIn\", \"HmdWarpParam\"], null, vrMetrics.postProcessScaleFactor, camera, Texture.BILINEAR_SAMPLINGMODE);\r\n\r\n        this._isRightEye = isRightEye;\r\n        this._distortionFactors = vrMetrics.distortionK;\r\n        this._postProcessScaleFactor = vrMetrics.postProcessScaleFactor;\r\n        this._lensCenterOffset = vrMetrics.lensCenterOffset;\r\n        this.adaptScaleToCurrentViewport = true;\r\n\r\n        this.onSizeChangedObservable.add(() => {\r\n            this._scaleIn = new Vector2(2, 2 / this.aspectRatio);\r\n            this._scaleFactor = new Vector2(0.5 * (1 / this._postProcessScaleFactor), 0.5 * (1 / this._postProcessScaleFactor) * this.aspectRatio);\r\n            this._lensCenter = new Vector2(this._isRightEye ? 0.5 - this._lensCenterOffset * 0.5 : 0.5 + this._lensCenterOffset * 0.5, 0.5);\r\n        });\r\n        this.onApplyObservable.add((effect: Effect) => {\r\n            effect.setFloat2(\"LensCenter\", this._lensCenter.x, this._lensCenter.y);\r\n            effect.setFloat2(\"Scale\", this._scaleFactor.x, this._scaleFactor.y);\r\n            effect.setFloat2(\"ScaleIn\", this._scaleIn.x, this._scaleIn.y);\r\n            effect.setFloat4(\"HmdWarpParam\", this._distortionFactors[0], this._distortionFactors[1], this._distortionFactors[2], this._distortionFactors[3]);\r\n        });\r\n    }\r\n}\r\n"]}