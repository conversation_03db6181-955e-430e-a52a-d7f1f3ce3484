{"version": 3, "file": "smoothStepBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/smoothStepBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAGvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,iBAAiB;IAClD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAE9C,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAC9E,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QACzE,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QACzE,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,YAAY,CAAC,CAAC;QAElF,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEhC,KAAK,CAAC,iBAAiB;YACnB,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC;gBAClC,iBAAiB,IAAI,CAAC,KAAK,CAAC,sBAAsB,KAAK,IAAI,CAAC,KAAK,CAAC,sBAAsB,KAAK,IAAI,CAAC,KAAK,CAAC,sBAAsB,MAAM,CAAC;QAEzI,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\n/**\r\n * Block used to smooth step a value\r\n */\r\nexport class SmoothStepBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new SmoothStepBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerInput(\"value\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerInput(\"edge0\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerInput(\"edge1\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.BasedOnInput);\r\n\r\n        this._outputs[0]._typeConnectionSource = this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"SmoothStepBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the value operand input component\r\n     */\r\n    public get value(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the first edge operand input component\r\n     */\r\n    public get edge0(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the second edge operand input component\r\n     */\r\n    public get edge1(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const output = this._outputs[0];\r\n\r\n        state.compilationString +=\r\n            this._declareOutput(output, state) +\r\n            ` = smoothstep(${this.edge0.associatedVariableName}, ${this.edge1.associatedVariableName}, ${this.value.associatedVariableName});\\n`;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SmoothStepBlock\", SmoothStepBlock);\r\n"]}