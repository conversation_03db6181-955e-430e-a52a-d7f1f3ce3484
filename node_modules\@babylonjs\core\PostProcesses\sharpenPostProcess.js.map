{"version": 3, "file": "sharpenPostProcess.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/sharpenPostProcess.ts"], "names": [], "mappings": ";AAIA,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEjD,OAAO,6BAA6B,CAAC;AACrC,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AAKvE;;;GAGG;AACH,MAAM,OAAO,kBAAmB,SAAQ,WAAW;IAY/C;;;OAGG;IACI,YAAY;QACf,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;;;;;;;;;OAUG;IACH,YACI,IAAY,EACZ,OAAoC,EACpC,MAAwB,EACxB,YAAqB,EACrB,MAAe,EACf,QAAkB,EAClB,cAAsB,SAAS,CAAC,wBAAwB,EACxD,gBAAgB,GAAG,KAAK;QAExB,KAAK,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,kBAAkB,EAAE,YAAY,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAxC5K;;WAEG;QAEI,gBAAW,GAAW,GAAG,CAAC;QACjC;;WAEG;QAEI,eAAU,GAAW,GAAG,CAAC;QAiC5B,IAAI,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YAC9B,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5E,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,iBAAsB,EAAE,YAAoB,EAAE,KAAY,EAAE,OAAe;QAC5F,OAAO,mBAAmB,CAAC,KAAK,CAC5B,GAAG,EAAE;YACD,OAAO,IAAI,kBAAkB,CACzB,iBAAiB,CAAC,IAAI,EACtB,iBAAiB,CAAC,OAAO,EACzB,YAAY,EACZ,iBAAiB,CAAC,wBAAwB,EAC1C,KAAK,CAAC,SAAS,EAAE,EACjB,iBAAiB,CAAC,WAAW,EAC7B,iBAAiB,CAAC,QAAQ,CAC7B,CAAC;QACN,CAAC,EACD,iBAAiB,EACjB,KAAK,EACL,OAAO,CACV,CAAC;IACN,CAAC;CACJ;AAjEU;IADN,SAAS,EAAE;uDACqB;AAK1B;IADN,SAAS,EAAE;sDACoB;AA8DpC,aAAa,CAAC,4BAA4B,EAAE,kBAAkB,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport type { PostProcessOptions } from \"./postProcess\";\r\nimport { PostProcess } from \"./postProcess\";\r\nimport { Constants } from \"../Engines/constants\";\r\n\r\nimport \"../Shaders/sharpen.fragment\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport { serialize } from \"../Misc/decorators\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\n\r\nimport type { Engine } from \"../Engines/engine\";\r\nimport type { Scene } from \"../scene\";\r\n\r\n/**\r\n * The SharpenPostProcess applies a sharpen kernel to every pixel\r\n * See http://en.wikipedia.org/wiki/Kernel_(image_processing)\r\n */\r\nexport class SharpenPostProcess extends PostProcess {\r\n    /**\r\n     * How much of the original color should be applied. Setting this to 0 will display edge detection. (default: 1)\r\n     */\r\n    @serialize()\r\n    public colorAmount: number = 1.0;\r\n    /**\r\n     * How much sharpness should be applied (default: 0.3)\r\n     */\r\n    @serialize()\r\n    public edgeAmount: number = 0.3;\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"SharpenPostProcess\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"SharpenPostProcess\";\r\n    }\r\n\r\n    /**\r\n     * Creates a new instance ConvolutionPostProcess\r\n     * @param name The name of the effect.\r\n     * @param options The required width/height ratio to downsize to before computing the render pass.\r\n     * @param camera The camera to apply the render pass to.\r\n     * @param samplingMode The sampling mode to be used when computing the pass. (default: 0)\r\n     * @param engine The engine which the post process will be applied. (default: current engine)\r\n     * @param reusable If the post process can be reused on the same frame. (default: false)\r\n     * @param textureType Type of textures used when performing the post process. (default: 0)\r\n     * @param blockCompilation If compilation of the shader should not be done in the constructor. The updateEffect method can be used to compile the shader at a later time. (default: false)\r\n     */\r\n    constructor(\r\n        name: string,\r\n        options: number | PostProcessOptions,\r\n        camera: Nullable<Camera>,\r\n        samplingMode?: number,\r\n        engine?: Engine,\r\n        reusable?: boolean,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        blockCompilation = false\r\n    ) {\r\n        super(name, \"sharpen\", [\"sharpnessAmounts\", \"screenSize\"], null, options, camera, samplingMode, engine, reusable, null, textureType, undefined, null, blockCompilation);\r\n\r\n        this.onApply = (effect: Effect) => {\r\n            effect.setFloat2(\"screenSize\", this.width, this.height);\r\n            effect.setFloat2(\"sharpnessAmounts\", this.edgeAmount, this.colorAmount);\r\n        };\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _Parse(parsedPostProcess: any, targetCamera: Camera, scene: Scene, rootUrl: string) {\r\n        return SerializationHelper.Parse(\r\n            () => {\r\n                return new SharpenPostProcess(\r\n                    parsedPostProcess.name,\r\n                    parsedPostProcess.options,\r\n                    targetCamera,\r\n                    parsedPostProcess.renderTargetSamplingMode,\r\n                    scene.getEngine(),\r\n                    parsedPostProcess.textureType,\r\n                    parsedPostProcess.reusable\r\n                );\r\n            },\r\n            parsedPostProcess,\r\n            scene,\r\n            rootUrl\r\n        );\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SharpenPostProcess\", SharpenPostProcess);\r\n"]}