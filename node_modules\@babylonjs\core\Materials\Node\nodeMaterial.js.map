{"version": 3, "file": "nodeMaterial.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Node/nodeMaterial.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAE/C,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAExD,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAElE,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAGnC,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAC5E,OAAO,EAAE,gCAAgC,EAAE,MAAM,oCAAoC,CAAC;AAEtF,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAIlE,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACpD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AACzC,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,iBAAiB,EAAE,MAAM,mCAAmC,CAAC;AACtE,OAAO,EAAE,mBAAmB,EAAE,MAAM,uCAAuC,CAAC;AAC5E,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAC/D,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAClD,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AAI1E,OAAO,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AACtE,OAAO,EAAE,oBAAoB,EAAE,MAAM,wCAAwC,CAAC;AAC9E,OAAO,EAAE,yBAAyB,EAAE,MAAM,6CAA6C,CAAC;AACxF,OAAO,EAAE,0BAA0B,EAAE,MAAM,8CAA8C,CAAC;AAC1F,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAEpD,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;AAE9C,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAC;AACxE,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AACjE,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,iBAAiB,EAAE,MAAM,2CAA2C,CAAC;AAC9E,OAAO,EAAE,uBAAuB,EAAE,MAAM,wCAAwC,CAAC;AACjF,OAAO,EAAE,iBAAiB,EAAE,2BAA2B,EAAE,MAAM,4BAA4B,CAAC;AAC5F,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAE5E,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AASxD,OAAO,EAAE,MAAM,EAAE,6BAAyB;AAC1C,OAAO,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,MAAM,6BAA6B,CAAC;AAGhG,MAAM,yBAAyB,GAAG,EAAE,MAAM,EAAE,IAAyB,EAAE,OAAO,EAAE,IAAoC,EAAE,CAAC;AAkBvH,gBAAgB;AAChB,MAAM,OAAO,mBAAoB,SAAQ,eAAe;IAiGpD;;OAEG;IACH;QACI,KAAK,EAAE,CAAC;QApGZ,aAAa;QACN,WAAM,GAAG,KAAK,CAAC;QACtB,cAAc;QACP,YAAO,GAAG,KAAK,CAAC;QACvB,mBAAmB;QACZ,oBAAe,GAAG,KAAK,CAAC;QAC/B,YAAY;QACL,QAAG,GAAG,KAAK,CAAC;QACnB,WAAW;QACJ,QAAG,GAAG,KAAK,CAAC;QACnB,WAAW;QACJ,QAAG,GAAG,KAAK,CAAC;QACnB,WAAW;QACJ,QAAG,GAAG,KAAK,CAAC;QACnB,WAAW;QACJ,QAAG,GAAG,KAAK,CAAC;QACnB,WAAW;QACJ,QAAG,GAAG,KAAK,CAAC;QAEnB,eAAe;QACR,YAAO,GAAG,KAAK,CAAC;QACvB,qBAAqB;QACd,mBAAc,GAAG,KAAK,CAAC;QAC9B,2BAA2B;QACpB,yBAAoB,GAAG,CAAC,CAAC,CAAC;QACjC,uBAAuB;QAChB,qBAAgB,GAAG,KAAK,CAAC;QAChC,6BAA6B;QACtB,2BAAsB,GAAG,CAAC,CAAC,CAAC;QACnC,oBAAoB;QACb,kBAAa,GAAG,KAAK,CAAC;QAC7B,0BAA0B;QACnB,wBAAmB,GAAG,CAAC,CAAC,CAAC;QAChC,sBAAsB;QACf,oBAAe,GAAG,CAAC,CAAC;QAE3B,YAAY;QACL,yBAAoB,GAAG,CAAC,CAAC;QAChC,qBAAqB;QACd,iBAAY,GAAG,CAAC,CAAC;QACxB,qCAAqC;QAC9B,gBAAW,GAAG,KAAK,CAAC;QAE3B,oBAAoB;QACb,iBAAY,GAAG,KAAK,CAAC;QAC5B,0BAA0B;QACnB,wBAAmB,GAAG,KAAK,CAAC;QACnC,2BAA2B;QACpB,yBAAoB,GAAG,KAAK,CAAC;QACpC,sBAAsB;QACf,oBAAe,GAAG,KAAK,CAAC;QAC/B,kCAAkC;QAC3B,0BAAqB,GAAG,CAAC,CAAC;QACjC,iDAAiD;QAC1C,yBAAoB,GAAG,KAAK,CAAC;QAEpC,uBAAuB;QAChB,oBAAe,GAAG,KAAK,CAAC;QAC/B,eAAe;QACR,aAAQ,GAAG,KAAK,CAAC;QACxB,uCAAuC;QAChC,8BAAyB,GAAG,KAAK,CAAC;QACzC,qCAAqC;QAC9B,4BAAuB,GAAG,KAAK,CAAC;QACvC,mBAAmB;QACZ,gBAAW,GAAG,KAAK,CAAC;QAC3B,6BAA6B;QACtB,qBAAgB,GAAG,KAAK,CAAC;QAChC,eAAe;QACR,aAAQ,GAAG,KAAK,CAAC;QACxB,eAAe;QACR,aAAQ,GAAG,KAAK,CAAC;QACxB,mBAAmB;QACZ,gBAAW,GAAG,KAAK,CAAC;QAC3B,oBAAoB;QACb,iBAAY,GAAG,KAAK,CAAC;QAC5B,uBAAuB;QAChB,mBAAc,GAAG,KAAK,CAAC;QAC9B,0BAA0B;QACnB,wBAAmB,GAAG,KAAK,CAAC;QACnC,0BAA0B;QACnB,oBAAe,GAAG,KAAK,CAAC;QAC/B,gBAAgB;QACT,WAAM,GAAG,KAAK,CAAC;QACtB,8CAA8C;QACvC,+BAA0B,GAAG,KAAK,CAAC;QAC1C,uBAAuB;QAChB,wBAAmB,GAAG,KAAK,CAAC;QAEnC,YAAY;QACL,iBAAY,GAAG,CAAC,CAAC;QACxB,6BAA6B;QACtB,wBAAmB,GAAG,KAAK,CAAC;QACnC,4BAA4B;QACrB,uBAAkB,GAAG,KAAK,CAAC;QAO9B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,IAAY,EAAE,KAAU,EAAE,wBAAwB,GAAG,KAAK;QACtE,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;YAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzB;QAED,IAAI,wBAAwB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;YAClD,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;IACvB,CAAC;CACJ;AA0BD;;GAEG;AACH,MAAM,OAAO,YAAa,SAAQ,YAAY;IAsB1C;;;;OAIG;IACI,MAAM,CAAC,oBAAoB,CAAC,KAAwB;QACvD,OAAO,CACH,KAAK,CAAC,YAAY,EAAE,KAAK,cAAc;YACvC,KAAK,CAAC,YAAY,EAAE,KAAK,4BAA4B;YACrD,KAAK,CAAC,YAAY,EAAE,KAAK,wBAAwB;YACjD,KAAK,CAAC,YAAY,EAAE,KAAK,iBAAiB;YAC1C,KAAK,CAAC,YAAY,EAAE,KAAK,iBAAiB;YAC1C,KAAK,CAAC,YAAY,EAAE,KAAK,oBAAoB;YAC7C,KAAK,CAAC,YAAY,EAAE,KAAK,sBAAsB;YAC/C,KAAK,CAAC,YAAY,EAAE,KAAK,kBAAkB;YAC3C,KAAK,CAAC,YAAY,EAAE,KAAK,gBAAgB;YACzC,KAAK,CAAC,YAAY,EAAE,KAAK,eAAe;YACxC,KAAK,CAAC,YAAY,EAAE,KAAK,qBAAqB,CACjD,CAAC;IACN,CAAC;IAID;;OAEG;IACK,4BAA4B;QAChC,0DAA0D;QAC1D,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;YACnC,OAAO,UAAU,CAAC;SACrB;QAED,gFAAgF;QAChF,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,WAAW,EAAE;YAC7E,OAAO,OAAO,CAAC;SAClB;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAwCD,yEAAyE;IACzE,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,OAA6B;QAC5C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAOD;;OAEG;IACH,IAAW,4BAA4B;QACnC,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;;;OAIG;IACH,IAAW,4BAA4B,CAAC,KAAmC;QACvE,IAAI,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC;QAEhD,qCAAqC;QACrC,IAAI,CAAC,gCAAgC,EAAE,CAAC;IAC5C,CAAC;IAcD;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAwB;QACpC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED,oGAAoG;IACpG,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,KAAa;QAC5B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAQD;;;;;OAKG;IACH,YAAY,IAAY,EAAE,KAAa,EAAE,UAAyC,EAAE;QAChF,KAAK,CAAC,IAAI,EAAE,KAAK,IAAI,WAAW,CAAC,gBAAiB,CAAC,CAAC;QA5KhD,aAAQ,GAAW,YAAY,CAAC,iBAAiB,EAAE,CAAC;QACpD,wBAAmB,GAAG,KAAK,CAAC;QAC5B,2BAAsB,GAAG,IAAI,MAAM,EAAE,CAAC;QACtC,qCAAgC,GAAG,IAAI,MAAM,EAAE,CAAC;QAChD,gBAAW,GAAG,IAAI,KAAK,EAAyB,CAAC;QACjD,oBAAe,GAAG,CAAC,CAAC,CAAC;QAgCrB,0BAAqB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAwBpE;;;WAGG;QACI,eAAU,GAAQ,IAAI,CAAC;QAE9B;;WAEG;QAEI,gBAAW,GAAG,KAAK,CAAC;QAE3B;;WAEG;QAEI,0BAAqB,GAAG,CAAC,CAAC;QAEjC;;WAEG;QACI,sBAAiB,GAAG,IAAI,UAAU,EAAgB,CAAC;QAE1D;;WAEG;QACI,uBAAkB,GAAG,IAAI,KAAK,EAAqB,CAAC;QAE3D;;WAEG;QACI,yBAAoB,GAAG,IAAI,KAAK,EAAqB,CAAC;QAmC7D;;WAEG;QACI,mBAAc,GAAwB,EAAE,CAAC;QAEhD;;;WAGG;QAEI,UAAK,GAAsB,iBAAiB,CAAC,QAAQ,CAAC;QAmR7D;;WAEG;QAEI,uBAAkB,GAAG,KAAK,CAAC;QAlP9B,IAAI,CAAC,QAAQ,GAAG;YACZ,YAAY,EAAE,KAAK;YACnB,GAAG,OAAO;SACb,CAAC;QAEF,2DAA2D;QAC3D,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,cAAc,CAAC;IAC1B,CAAC;IAOD;;;OAGG;IACO,mCAAmC,CAAC,aAAqD;QAC/F,IAAI,aAAa,KAAK,IAAI,CAAC,6BAA6B,EAAE;YACtD,OAAO;SACV;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,6BAA6B,IAAI,IAAI,CAAC,wBAAwB,EAAE;YACrE,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;SAC/F;QAED,0CAA0C;QAC1C,IAAI,CAAC,aAAa,EAAE;YAChB,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,4BAA4B,CAAC;SACrF;aAAM;YACH,IAAI,CAAC,6BAA6B,GAAG,aAAa,CAAC;SACtD;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACpC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC3F,IAAI,CAAC,uCAAuC,EAAE,CAAC;YACnD,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,IAAY;QAC9B,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE;gBACrB,IAAI,CAAC,MAAM,EAAE;oBACT,MAAM,GAAG,KAAK,CAAC;iBAClB;qBAAM;oBACH,KAAK,CAAC,IAAI,CAAC,+CAA+C,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;oBACzE,OAAO,MAAM,CAAC;iBACjB;aACJ;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,SAAgD;QACvE,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;gBAClB,OAAO,KAAK,CAAC;aAChB;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAAC,SAAyC;QACrE,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,IAAI,KAAK,CAAC,OAAO,IAAI,SAAS,CAAC,KAAmB,CAAC,EAAE;gBACjD,OAAO,KAAmB,CAAC;aAC9B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,MAAM,MAAM,GAAiB,EAAE,CAAC;QAChC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,IAAI,KAAK,CAAC,OAAO,EAAE;gBACf,MAAM,CAAC,IAAI,CAAC,KAAmB,CAAC,CAAC;aACpC;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,SAAgC;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAElD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,OAAO;SACV;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,SAAgC;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAElD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,OAAO;SACV;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,IAAuB;QACxC,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;YACtB,4CAA4C;YAC5C,MAAM,+FAA+F,CAAC;SACzG;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,wBAAwB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACvD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;SACnC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,wBAAwB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACzD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;SACrC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,IAAuB;QAC3C,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;YACtB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,wBAAwB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACvD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;SACtC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,wBAAwB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACzD,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;SACxC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB,CAAC,IAAuB;QAChD,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC9C,OAAO;SACV;QAED,IAAI,CAAC,MAAM,GAAG,wBAAwB,CAAC,MAAM,CAAC;QAC9C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEnC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,uBAAuB,CAAC,IAAuB;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,OAAO;SACV;QAED,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAEzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,sBAAsB,CAAC,IAAuB;QAClD,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAChD,OAAO;SACV;QAED,IAAI,CAAC,MAAM,GAAG,wBAAwB,CAAC,QAAQ,CAAC;QAChD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,yBAAyB,CAAC,IAAuB;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,OAAO;SACV;QAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAE3C,OAAO,IAAI,CAAC;IAChB,CAAC;IAQD;;;OAGG;IACI,iBAAiB;QACpB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACzH,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACvE,CAAC;IAEO,wBAAwB,CAAC,KAAwB,EAAE,KAA6B,EAAE,gCAAqD,EAAE,aAAa,GAAG,IAAI;QACjK,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,iBAAiB,EAAE;YAC7D,gCAAgC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAChD;aAAM,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,IAAI,KAAK,CAAC,cAAc,KAAK,IAAI,CAAC,QAAQ,EAAE;YACzJ,gCAAgC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAChD;QACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,gCAAgC,EAAE,aAAa,CAAC,CAAC;IACzF,CAAC;IAEO,gBAAgB,CAAC,IAAuB,EAAE,KAA6B,EAAE,gCAAqD,EAAE,aAAa,GAAG,IAAI;QACxJ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACvB,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAC5B;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEpC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC1C,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBAEtC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;oBACrC,IAAI,KAAK,CAAC,YAAY,EAAE,KAAK,SAAS,EAAE;wBACpC,4CAA4C;wBAC5C,MAAM,uCAAuC,SAAS,2BAA2B,CAAC;qBACrF;iBACJ;aACJ;YACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAClC;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC7B,KAAK,CAAC,sBAAsB,GAAG,EAAE,CAAC;YAElC,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC;YAC5C,IAAI,cAAc,EAAE;gBAChB,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC;gBACxC,IAAI,KAAK,KAAK,IAAI,EAAE;oBAChB,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,gCAAgC,EAAE,aAAa,CAAC,CAAC;iBAChG;aACJ;SACJ;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,MAAM,QAAQ,GAAG,IAAoC,CAAC;YACtD,IAAI,QAAQ,CAAC,UAAU,EAAE;gBACrB,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,UAAU,EAAE,KAAK,EAAE,gCAAgC,EAAE,aAAa,CAAC,CAAC;aAC9G;SACJ;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,MAAM,CAAC,sBAAsB,GAAG,EAAE,CAAC;SACtC;IACL,CAAC;IAEO,gBAAgB,CAAC,IAAuB,EAAE,EAAU;QACxD,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAwB,CAAC,iBAAiB,EAAE;YAC5D,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;SACrB;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE;YAC9B,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;YAC7C,IAAI,cAAc,EAAE;gBAChB,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC;gBACxC,IAAI,KAAK,KAAK,IAAI,EAAE;oBAChB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;iBACpC;aACJ;SACJ;QAED,kEAAkE;QAClE,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,MAAM,WAAW,GAAG,IAAoC,CAAC;YACzD,IAAI,WAAW,CAAC,UAAU,EAAE;gBACxB,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;aACrD;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,KAAwB;QACvC,MAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9D,IAAI,kBAAkB,GAAG,CAAC,CAAC,EAAE;YACzB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;SACrD;QAED,IAAI,KAAK,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;SAChC;IACL,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,UAAmB,KAAK,EAAE,aAAa,GAAG,IAAI,EAAE,aAAa,GAAG,KAAK;QAC9E,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,aAAa,EAAE;YACjD,aAAa,GAAG,IAAI,CAAC;SACxB;QAED,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC;QAE3C,MAAM,uBAAuB,GAAG,IAAI,CAAC,KAAK,KAAK,iBAAiB,CAAC,QAAQ,CAAC;QAE1E,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAClE,4CAA4C;YAC5C,MAAM,+CAA+C,CAAC;SACzD;QAED,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE;YACxC,4CAA4C;YAC5C,MAAM,iDAAiD,CAAC;SAC3D;QAED,oBAAoB;QACpB,IAAI,CAAC,uBAAuB,GAAG,IAAI,sBAAsB,EAAE,CAAC;QAC5D,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,GAAG,MAAM,CAAC,sBAAsB,CAAC;QACnF,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,wBAAwB,CAAC,MAAM,CAAC;QACtE,IAAI,CAAC,yBAAyB,GAAG,IAAI,sBAAsB,EAAE,CAAC;QAC9D,IAAI,CAAC,yBAAyB,CAAC,qBAAqB,GAAG,MAAM,CAAC,sBAAsB,CAAC;QACrF,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,wBAAwB,CAAC,QAAQ,CAAC;QAE1E,cAAc;QACd,IAAI,CAAC,WAAW,GAAG,IAAI,gCAAgC,EAAE,CAAC;QAC1D,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACjE,IAAI,CAAC,uBAAuB,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAC3D,IAAI,CAAC,yBAAyB,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAC7D,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QACzC,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;QAC3D,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzC,IAAI,CAAC,WAAW,CAAC,uBAAuB,GAAG,uBAAuB,CAAC;QAEnE,oBAAoB;QACpB,MAAM,WAAW,GAAwB,EAAE,CAAC;QAC5C,MAAM,aAAa,GAAwB,EAAE,CAAC;QAE9C,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACpD,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACnC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;SACvG;QAED,KAAK,MAAM,kBAAkB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACxD,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,yBAAyB,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;SACzG;QAED,WAAW;QACX,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,SAAS;QACT,KAAK,MAAM,gBAAgB,IAAI,WAAW,EAAE;YACxC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC;SACrE;QAED,WAAW;QACX,IAAI,CAAC,yBAAyB,CAAC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACzF,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC;QACtG,IAAI,CAAC,yBAAyB,CAAC,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC;QACxG,IAAI,CAAC,yBAAyB,CAAC,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAE3E,KAAK,MAAM,kBAAkB,IAAI,aAAa,EAAE;YAC5C,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;SAChE;QAED,KAAK,MAAM,kBAAkB,IAAI,aAAa,EAAE;YAC5C,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,yBAAyB,EAAE,aAAa,CAAC,CAAC;SAC3E;QAED,WAAW;QACX,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACpE,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAExE,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,iBAAiB,EAAE,CAAC;SACpD;QAED,SAAS;QACT,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;QAE9B,IAAI,OAAO,EAAE;YACT,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC7B,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;YAC3D,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAC/B,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,CAAC;SAChE;QAED,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE7C,eAAe;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC;QACtC,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACjB,SAAS;aACZ;YACD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClC,IAAI,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;oBAChC,SAAS;iBACZ;gBAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;oBAC1B,SAAS;iBACZ;gBAED,MAAM,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC;gBACxC,OAAO,CAAC,cAAc,EAAE,CAAC;gBACzB,OAAO,CAAC,KAAK,EAAE,CAAC;aACnB;SACJ;QAED,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE;YAClC,IAAI,CAAC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,CAAC;SAC3C;QACD,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC;QACxD,IAAI,eAAe,EAAE;YACjB,eAAe,CAAC,WAAW,EAAE,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACtC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAC1E;IACL,CAAC;IAEO,4BAA4B,CAAC,IAAkB,EAAE,OAA4B;QACjF,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpC,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QACtC,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAE5C,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QACxE,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAE1E,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACnG,OAAO,CAAC,iBAAiB,CAAC,GAAG,eAAe,CAAC;QAE7C,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAAE;YACvD,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAChC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACxE,SAAS,GAAG,SAAS,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC;SACxD;QAED,UAAU;QACV,MAAM,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,+BAA+B,CAAC;QACnG,wBAAwB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;QAEzD,IAAI,SAAS,KAAK,OAAO,CAAC,QAAQ,CAAC,IAAI,UAAU,KAAK,OAAO,CAAC,SAAS,CAAC,IAAI,QAAQ,KAAK,OAAO,CAAC,iBAAiB,CAAC,IAAI,SAAS,EAAE;YAC9H,OAAO,CAAC,qBAAqB,EAAE,CAAC;SACnC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,IAAW,qBAAqB;QAC5B,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,oBAAoB,CAAuB,CAAC;QACpI,MAAM,MAAM,GAAG,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACtD,IAAI,CAAC,kBAAkB,EAAE;YACrB,OAAO,MAAM,CAAC;SACjB;QACD,yDAAyD;QACzD,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE;YAClC,OAAO,MAAM,CAAC;SACjB;QAED,IAAI,kBAAkB,CAAC,SAAS,CAAC,WAAW,EAAE;YAC1C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;SACrD;QAED,IAAI,kBAAkB,CAAC,UAAU,CAAC,WAAW,EAAE;YAC3C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;SACtD;QAED,IAAI,kBAAkB,CAAC,aAAa,CAAC,WAAW,EAAE;YAC9C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;SACxD;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,MAAM,oBAAoB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,qBAAqB,CAA0B,CAAC;QACnJ,MAAM,MAAM,GAAG,EAAc,CAAC;QAE9B,KAAK,MAAM,KAAK,IAAI,oBAAoB,EAAE;YACtC,IAAI,KAAK,CAAC,QAAQ,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,6BAA6B,CAAC,EAAE;gBACzF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;aACxD;YACD,IAAI,KAAK,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,0BAA0B,CAAC,EAAE;gBACnF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;aACrD;YACD,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,2BAA2B,CAAC,EAAE;gBACrF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;aACtD;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,eAAgC;QACtD,MAAM,uBAAuB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAE7F,IAAI,eAAe,IAAI,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE;YACvD,IAAI,GAAG,GAAG,eAAe,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;YACjE,IAAI,CAAC,GAAG,EAAE;gBACN,GAAG,GAAG,eAAe,CAAC,sBAAsB,CAAC;oBACzC,OAAO,EAAE,IAAI;oBACb,oBAAoB,EAAE,KAAK;oBAC3B,IAAI,EAAE,cAAc;oBACpB,gBAAgB,EAAE,EAAE;iBACvB,CAAC,CAAC;aACN;YACD,KAAK,MAAM,cAAc,IAAI,uBAAuB,EAAE;gBAClD,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;oBAChD,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;iBAC7C;aACJ;YACD,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;SACtB;QAED,iEAAiE;QACjE,wDAAwD;QACxD,OAAO,uBAAuB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;OAUG;IACI,iBAAiB,CACpB,MAAwB,EACxB,UAAuC,CAAC,EACxC,eAAuB,SAAS,CAAC,4BAA4B,EAC7D,MAAe,EACf,QAAkB,EAClB,cAAsB,SAAS,CAAC,wBAAwB,EACxD,aAAa,GAAG,SAAS,CAAC,kBAAkB;QAE5C,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,EAAE;YAC7C,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;IAC/H,CAAC;IAED;;;OAGG;IACI,0BAA0B,CAAC,WAAwB;QACtD,IAAI,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;IAEO,2BAA2B,CAC/B,WAAkC,EAClC,MAAyB,EACzB,UAAuC,CAAC,EACxC,eAAuB,SAAS,CAAC,4BAA4B,EAC7D,MAAe,EACf,QAAkB,EAClB,cAAsB,SAAS,CAAC,wBAAwB,EACxD,aAAa,GAAG,SAAS,CAAC,kBAAkB;QAE5C,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEzC,MAAM,OAAO,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAE1C,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,QAAQ,GAAG,aAAa,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE9E,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE5B,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEzC,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,CAAC;QAE9I,IAAI,CAAC,WAAW,EAAE;YACd,WAAW,GAAG,IAAI,WAAW,CACzB,IAAI,CAAC,IAAI,GAAG,aAAa,EACzB,QAAQ,EACR,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,OAAO,EACP,MAAO,EACP,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,OAAO,CAAC,QAAQ,EAAE,EAClB,WAAW,EACX,QAAQ,EACR,EAAE,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE,EACrD,KAAK,EACL,aAAa,CAChB,CAAC;SACL;aAAM;YACH,WAAW,CAAC,YAAY,CACpB,OAAO,CAAC,QAAQ,EAAE,EAClB,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,EAAE,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE,EACrD,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,CACX,CAAC;SACL;QAED,WAAW,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAEtC,WAAW,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACzC,IAAI,OAAO,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAC3B,OAAO,MAAM,CAAC,YAAY,CAAC,QAAQ,GAAG,cAAc,CAAC,CAAC;gBACtD,OAAO,MAAM,CAAC,YAAY,CAAC,QAAQ,GAAG,aAAa,CAAC,CAAC;gBAErD,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAErC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAEzB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;aAC3B;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAExD,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,CAAC;gBAE9I,WAAW,CAAC,YAAY,CAAC,GAAG,EAAE,CAC1B,WAAY,CAAC,YAAY,CACrB,OAAO,CAAC,QAAQ,EAAE,EAClB,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,EAAE,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE,EACrD,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,CACX,CACJ,CAAC;aACL;YAED,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACI,uBAAuB,CAAC,IAAiE,EAAE,KAAY;QAC1G,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAiB,CAAC,iBAAiB,EAAE;YACnD,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEzC,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAE7E,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,QAAQ,GAAG,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7E,SAAS,CAAC,iBAAiB,GAAG;YAC1B,MAAM,EAAE,IAAI;SACf,CAAC;QAEF,MAAM,OAAO,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACxD,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,CAAC;QAE9I,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,YAAY,CACjD;YACI,aAAa,EAAE,QAAQ;YACvB,eAAe,EAAE,QAAQ;SAC5B,EACD,CAAC,YAAY,CAAC,YAAY,CAAC,EAC3B,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,OAAO,CAAC,QAAQ,EAAE,EAClB,MAAM,EAAE,SAAS,EACjB,SAAS,CACZ,CAAC;QAEF,iBAAiB,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC5C,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAErC,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5B,iBAAiB,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;YACpD,IAAI,OAAO,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAC3B,OAAO,MAAM,CAAC,YAAY,CAAC,QAAQ,GAAG,cAAc,CAAC,CAAC;gBACtD,OAAO,MAAM,CAAC,YAAY,CAAC,QAAQ,GAAG,aAAa,CAAC,CAAC;gBAErD,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAErC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAEzB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;aAC3B;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAExD,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,CAAC;gBAE9I,WAAW,CAAC,YAAY,CAAC,GAAG,EAAE;oBAC1B,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,YAAY,CAC7C;wBACI,aAAa,EAAE,QAAQ;wBACvB,eAAe,EAAE,QAAQ;qBAC5B,EACD,CAAC,YAAY,CAAC,YAAY,CAAC,EAC3B,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,OAAO,CAAC,QAAQ,EAAE,EAClB,MAAM,EAAE,SAAS,EACjB,SAAS,CACZ,CAAC;oBAEF,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBACzC,CAAC,CAAC,CAAC;aACN;YAED,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAEO,yBAAyB,CAC7B,cAA+B,EAC/B,SAAiB,EACjB,UAAqC,EACrC,OAAkD,EAClD,MAAe,EACf,OAA6B,EAC7B,SAAkC,EAClC,2BAA2B,GAAG,EAAE;QAEhC,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,GAAG,GAAG,SAAS,CAAC;QAE3D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,GAAG,IAAI,mBAAmB,EAAE,CAAC;SACvC;QAED,IAAI,CAAC,SAAS,EAAE;YACZ,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,CAAC;YAClE,IAAI,CAAC,SAAS,EAAE;gBACZ,SAAS,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACtE,SAAS,CAAC,iBAAiB,GAAG;oBAC1B,MAAM,EAAE,IAAI;iBACf,CAAC;aACL;SACJ;QAED,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE5B,MAAM,qBAAqB,GAAkB,EAAE,CAAC;QAChD,IAAI,IAAI,GAAG,2BAA2B,CAAC;QAEvC,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAExD,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;YAExF,cAAc,CAAC,WAAW,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;YAE7D,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAExC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;iBACnB,SAAS,EAAE;iBACX,wBAAwB,CACrB,QAAQ,EACR,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,OAAO,CAAC,QAAQ,EAAE,GAAG,IAAI,GAAG,IAAI,EAChC,MAAM,EAAE,SAAS,EACjB,UAAU,EACV,OAAO,EACP,cAAc,CACjB,CAAC;YAEN,cAAc,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;SACrD;QAED,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACnC,IAAI,OAAO,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAC3B,OAAO,MAAM,CAAC,YAAY,CAAC,QAAQ,GAAG,aAAa,CAAC,CAAC;gBAErD,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,GAAG,GAAG,GAAG,SAAS,CAAC;gBAEvD,OAAQ,CAAC,cAAc,EAAE,CAAC;gBAE1B,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;aAC3B;YAED,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;YAEjC,cAAc,CAAC,WAAW,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;YAE7D,MAAM,kCAAkC,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE5E,IAAI,kCAAkC,KAAK,IAAI,EAAE;gBAC7C,OAAQ,CAAC,cAAc,EAAE,CAAC;gBAC1B,IAAI,GAAG,kCAAkC,CAAC;aAC7C;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,SAAU,EAAE,OAAQ,CAAC,CAAC;YAE1D,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,CAAC;gBAExF,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;qBACnB,SAAS,EAAE;qBACX,wBAAwB,CACrB,QAAQ,EACR,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EACvC,OAAQ,CAAC,QAAQ,EAAE,GAAG,IAAI,GAAG,IAAI,EACjC,MAAM,EAAE,SAAS,EACjB,UAAU,EACV,OAAO,EACP,cAAc,CACjB,CAAC;gBACN,cAAc,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gBAClD,IAAI,CAAC,yBAAyB,CAAC,cAAc,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,2BAA2B,CAAC,CAAC,CAAC,2CAA2C;gBACpL,OAAO;aACV;YAED,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,eAAe,CAAC,MAAc;QAClC,kBAAkB;QAClB,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;YACjC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAE9B,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YAEnC,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE;gBAClC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;oBACjD,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;iBACxB;gBAED,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;aAClC;SACJ;QAED,kBAAkB;QAClB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;YACjD,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SAC5B;QAED,oBAAoB;QACpB,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;YACnD,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;SACvD;IACL,CAAC;IAED;;;;;OAKG;IACI,wBAAwB,CAAC,cAA+B,EAAE,UAAqC,EAAE,OAAkD;QACtJ,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAiB,CAAC,QAAQ,EAAE;YAC1C,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YACzC,OAAO;SACV;QAED,IAAI,CAAC,yBAAyB,CAAC,cAAc,EAAE,kBAAkB,CAAC,gBAAgB,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QACzG,IAAI,CAAC,yBAAyB,CAAC,cAAc,EAAE,kBAAkB,CAAC,kBAAkB,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC/G,CAAC;IAED;;;OAGG;IACI,0BAA0B,CAAC,cAAwB;QACtD,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAiB,CAAC,QAAQ,EAAE;YAC1C,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YACzC,OAAO;SACV;QAED,cAAc,CAAC,kBAAkB,GAAG,IAAI,OAAO,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC9F,CAAC;IAEO,eAAe,CACnB,IAAkB,EAClB,OAA4B,EAC5B,YAAY,GAAG,KAAK,EACpB,OAAiB;QAQjB,IAAI,MAAM,GAAG,IAAI,CAAC;QAElB,iBAAiB;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,uBAAuB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;YACzC,OAAO,CAAC,eAAe,EAAE,CAAC;SAC7B;QAED,iBAAiB;QACjB,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAC7C,CAAC,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAC7C,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,MAAM,aAAa,GAAG,OAAO,CAAC,kBAAkB,CAAC;YACjD,OAAO,CAAC,eAAe,EAAE,CAAC;YAE1B,gCAAgC;YAChC,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,CAAC;YACtG,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CAAC,uBAAuB,CAAC;YAE1G,IAAI,CAAC,WAAW,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBACnD,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,yBAAyB,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAC5G,CAAC,CAAC,CAAC;YAEH,WAAW;YACX,MAAM,cAAc,GAAa,EAAE,CAAC;YACpC,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBAChD,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;YAC5F,CAAC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC;YAE7D,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBAClD,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAExC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;oBACd,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAC1B;YACL,CAAC,CAAC,CAAC;YAEH,WAAW;YACX,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC;YAE7D,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBAClD,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAExC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;oBACd,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAC1B;YACL,CAAC,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;YAExC,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBAC/C,CAAC,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,MAAM,GAAG;gBACL,aAAa;gBACb,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd,SAAS;aACZ,CAAC;SACL;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;;OAOG;IACI,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAAE,eAAwB,KAAK;QACxF,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;YACjC,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YAEnC,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE;gBAClC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;oBACjD,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;iBACxB;gBAED,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;aAClC;SACJ;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;QAEzC,IAAI,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YACrC,IAAI,WAAW,CAAC,mBAAmB,IAAI,WAAW,CAAC,4BAA4B,KAAK,YAAY,EAAE;gBAC9F,OAAO,IAAI,CAAC;aACf;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC1B,OAAO,CAAC,eAAe,GAAG,IAAI,mBAAmB,EAAE,CAAC;SACvD;QAED,MAAM,OAAO,GAAwB,OAAO,CAAC,eAAe,CAAC;QAC7D,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,CAAC,4BAA4B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEjD,4BAA4B;QAC5B,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,EAAE;YAC5F,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;QAE1E,IAAI,MAAM,EAAE;YACR,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;YACtC,cAAc;YACd,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YAChC,IAAI,MAAM,GAAG,MAAM,CAAC,YAAY,CAC5B;gBACI,MAAM,EAAE,cAAc,GAAG,IAAI,CAAC,QAAQ;gBACtC,QAAQ,EAAE,cAAc,GAAG,IAAI,CAAC,QAAQ;gBACxC,YAAY,EAAE,IAAI,CAAC,uBAAuB,CAAC,iBAAiB;gBAC5D,cAAc,EAAE,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;aACnE,EACuB;gBACpB,UAAU,EAAE,IAAI,CAAC,uBAAuB,CAAC,UAAU;gBACnD,aAAa,EAAE,MAAM,CAAC,cAAc;gBACpC,mBAAmB,EAAE,MAAM,CAAC,cAAc;gBAC1C,QAAQ,EAAE,MAAM,CAAC,cAAc;gBAC/B,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,WAAW,EAAE,OAAO,CAAC,OAAO;gBAC5B,eAAe,EAAE,EAAE,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE,2BAA2B,EAAE,OAAO,CAAC,qBAAqB,EAAE;aACrI,EACD,MAAM,CACT,CAAC;YAEF,IAAI,MAAM,EAAE;gBACR,IAAI,IAAI,CAAC,0BAA0B,EAAE;oBACjC,yBAAyB,CAAC,MAAM,GAAG,MAAM,CAAC;oBAC1C,yBAAyB,CAAC,OAAO,GAAG,OAAO,CAAC;oBAC5C,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC;iBAC9E;gBAED,iDAAiD;gBACjD,IAAI,IAAI,CAAC,sBAAsB,IAAI,cAAc,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;oBACpE,MAAM,GAAG,cAAc,CAAC;oBACxB,OAAO,CAAC,iBAAiB,EAAE,CAAC;oBAE5B,IAAI,MAAM,CAAC,aAAa,EAAE;wBACtB,oDAAoD;wBACpD,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC;wBAClC,OAAO,KAAK,CAAC;qBAChB;iBACJ;qBAAM;oBACH,KAAK,CAAC,mBAAmB,EAAE,CAAC;oBAC5B,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;iBAC7D;aACJ;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;YAC9C,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,WAAW,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACvC,WAAW,CAAC,4BAA4B,GAAG,YAAY,CAAC;QAExD,IAAI,CAAC,8BAA8B,EAAE,CAAC;QAEtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,qBAAqB,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,2BAA2B,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,EAAE,CAAC;IAC5J,CAAC;IAED;;;OAGG;IACI,mBAAmB,CAAC,KAAa;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO;SACV;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;QAErC,IAAI,KAAK,CAAC,mBAAmB,EAAE;YAC3B,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;SAC3E;QAED,IAAI,KAAK,CAAC,6BAA6B,EAAE;YACrC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,gCAAgC,CAAC,CAAC;SAC1F;QAED,oBAAoB;QACpB,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;YACnD,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,gCAAgC,CAAC,CAAC;SAC5H;IACL,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,KAAa,EAAE,IAAU,EAAE,OAAgB;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAEhC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7E,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAEpC,IAAI,UAAU,EAAE;YACZ,kBAAkB;YAClB,KAAK,MAAM,KAAK,IAAI,UAAU,CAAC,cAAc,EAAE;gBAC3C,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;aAC3C;YAED,KAAK,MAAM,KAAK,IAAI,UAAU,CAAC,oBAAoB,EAAE;gBACjD,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;aAC3C;YAED,oBAAoB;YACpB,KAAK,MAAM,UAAU,IAAI,UAAU,CAAC,WAAW,EAAE;gBAC7C,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC7C;SACJ;aAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACvB,KAAK,MAAM,KAAK,IAAI,UAAU,CAAC,oBAAoB,EAAE;gBACjD,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;aAC3C;SACJ;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,MAAM,cAAc,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAEjD,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAQ,CAAC,CAAC,CAAC;SAC9G;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACI,gBAAgB;QACnB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO,EAAE,CAAC;SACb;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,mBAAmB;QACtB,MAAM,aAAa,GAAgC,EAAE,CAAC;QAEtD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,IAAI,YAAY,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;gBAC1C,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC7B;SACJ;QAED,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,OAAoB;QAClC,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO,KAAK,CAAC;SAChB;QAED,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;YAC5C,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,EAAE;gBACvB,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,kBAA4B,EAAE,oBAA8B,EAAE,cAAwB;QACjG,IAAI,oBAAoB,EAAE;YACtB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,gBAAgB,EAAE;iBACxC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC;iBAC1B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAQ,CAAC,EAAE;gBAC3B,OAAO,CAAC,OAAO,EAAE,CAAC;aACrB;SACJ;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,KAAK,CAAC,OAAO,EAAE,CAAC;SACnB;QAED,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,WAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,uBAA+B,GAAG,IAAI,CAAC;QAC5C,IAAI,CAAC,yBAAiC,GAAG,IAAI,CAAC;QAE/C,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAE/B,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC5F,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;SACxC;QAED,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,gBAAsB;QAC5C,MAAM,gBAAgB,GAAQ;YAC1B,YAAY,EAAE,IAAI;YAClB,GAAG,gBAAgB;SACtB,CAAC;QACF,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACjE,CAAC;IAED;;;;OAIG;IACI,IAAI,CAAC,MAAmC;QAC3C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAC/F,IAAI,OAAO,IAAI,CAAC,qBAAqB,IAAI,WAAW,EAAE;gBAClD,MAAM,SAAS,GAAG,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC;gBAEzF,oCAAoC;gBACpC,KAAK,CAAC,iBAAiB,CAAC,SAAS,EAAE,GAAG,EAAE;oBACpC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;oBAC/F,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;oBACjD,OAAO,EAAE,CAAC;gBACd,CAAC,CAAC,CAAC;aACN;iBAAM;gBACH,+BAA+B;gBAC/B,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;gBACjD,OAAO,EAAE,CAAC;aACb;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,YAAY;QACf,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,MAAM,aAAa,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;QACjD,aAAa,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAEzC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;QAC3C,UAAU,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAE5D,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC;QAChD,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAClC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE/B,MAAM,mBAAmB,GAAG,IAAI,UAAU,CAAC,gBAAgB,CAAC,CAAC;QAC7D,mBAAmB,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;QAE9E,MAAM,mCAAmC,GAAG,IAAI,cAAc,CAAC,oCAAoC,CAAC,CAAC;QACrG,QAAQ,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;QACxD,mBAAmB,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC;QAEnE,MAAM,YAAY,GAAG,IAAI,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAC3D,mCAAmC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAE5D,QAAQ;QACR,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;QAC3C,UAAU,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAEhD,MAAM,cAAc,GAAG,IAAI,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QACjE,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAErC,eAAe;QACf,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAEnC,IAAI,CAAC,KAAK,GAAG,iBAAiB,CAAC,QAAQ,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,uBAAuB;QAC1B,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;QAC5C,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAEtC,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;QAC3C,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;QAEjB,MAAM,OAAO,GAAG,IAAI,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAEpD,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC5B,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;QAE1C,MAAM,YAAY,GAAG,IAAI,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAC3D,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAEhC,QAAQ;QACR,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;QACtC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAChC,KAAK,CAAC,KAAK,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEhC,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;QAClC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAExB,MAAM,EAAE,GAAG,IAAI,aAAa,CAAC,UAAU,CAAC,CAAC;QACzC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAClB,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAEpB,MAAM,aAAa,GAAG,IAAI,kBAAkB,CAAC,eAAe,CAAC,CAAC;QAC9D,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAE5B,aAAa,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,+DAA+D,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEtH,MAAM,cAAc,GAAG,IAAI,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QACjE,aAAa,CAAC,SAAS,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;QAE5D,eAAe;QACf,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAEnC,IAAI,CAAC,KAAK,GAAG,iBAAiB,CAAC,WAAW,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,6BAA6B;QAChC,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;QAC5C,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAEtC,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;QAC3C,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;QAEjB,MAAM,OAAO,GAAG,IAAI,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAEpD,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC5B,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;QAE1C,MAAM,YAAY,GAAG,IAAI,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAC3D,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAEhC,QAAQ;QACR,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,uBAAuB,CAAC,IAAI,CAAC;QAClD,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;QACvC,KAAK,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;QACzB,MAAM,cAAc,GAAG,IAAI,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAEjE,MAAM,YAAY,GAAG,IAAI,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAC3D,YAAY,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAExC,MAAM,GAAG,GAAG,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC;QACzC,GAAG,CAAC,SAAS,GAAG,2BAA2B,CAAC,GAAG,CAAC;QAEhD,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACjC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAElD,eAAe;QACf,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAEnC,IAAI,CAAC,KAAK,GAAG,iBAAiB,CAAC,iBAAiB,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,oBAAoB;QACvB,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,QAAQ;QACR,MAAM,EAAE,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;QAChC,EAAE,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAEjC,MAAM,OAAO,GAAG,IAAI,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAC5D,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAEtB,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;QACtC,KAAK,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QAEvC,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,iBAAiB,CAAC,CAAC;QACtD,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC5B,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE1B,MAAM,YAAY,GAAG,IAAI,yBAAyB,CAAC,sBAAsB,CAAC,CAAC;QAC3E,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAEjC,MAAM,SAAS,GAAG,IAAI,kBAAkB,CAAC,eAAe,CAAC,CAAC;QAC1D,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAE3B,MAAM,aAAa,GAAG,IAAI,0BAA0B,CAAC,uBAAuB,CAAC,CAAC;QAC9E,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACtC,OAAO,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;QAClD,SAAS,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;QAEpD,MAAM,cAAc,GAAG,IAAI,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QACjE,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAExC,eAAe;QACf,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAEnC,IAAI,CAAC,KAAK,GAAG,iBAAiB,CAAC,QAAQ,CAAC;IAC5C,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,SAAS,CAAC,GAAW,EAAE,UAAkB,EAAE;QACpD,OAAO,YAAY,CAAC,kBAAkB,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1F,CAAC;IAEO,aAAa,CAAC,QAA2B,EAAE,IAAyB;QACxE,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/B,OAAO;SACV;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEpB,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,EAAE;YACjC,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC;YAC5C,IAAI,cAAc,EAAE;gBAChB,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC;gBACxC,IAAI,KAAK,KAAK,QAAQ,EAAE;oBACpB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;iBACnC;aACJ;SACJ;QAED,gBAAgB;QAChB,IAAI,QAAQ,CAAC,aAAa,EAAE;YACxB,MAAM,KAAK,GAAG,QAAwC,CAAC;YACvD,IAAI,KAAK,CAAC,UAAU,EAAE;gBAClB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aAC9C;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,IAAI,aAAa,GAAwB,EAAE,CAAC;QAC5C,MAAM,YAAY,GAAwB,EAAE,CAAC;QAC7C,MAAM,WAAW,GAAa,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACtD,qBAAqB;QACrB,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC9C,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;SAChD;QAED,MAAM,cAAc,GAAwB,EAAE,CAAC;QAC/C,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAChD,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;SAClD;QAED,yBAAyB;QACzB,IAAI,UAAU,GAAG,gDAAgD,IAAI,CAAC,IAAI,IAAI,eAAe,OAAO,CAAC;QACrG,UAAU,IAAI,iDAAiD,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;QACjG,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE;YAC7B,IAAI,IAAI,CAAC,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACpD,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;aAC5D;SACJ;QAED,2BAA2B;QAC3B,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE;YAC/B,IAAI,IAAI,CAAC,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACpD,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;aAC5D;SACJ;QAED,cAAc;QACd,aAAa,GAAG,EAAE,CAAC;QACnB,UAAU,IAAI,oBAAoB,CAAC;QACnC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACxC,UAAU,IAAI,IAAI,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;SACnE;QACD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC1C,UAAU,IAAI,IAAI,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;SACnE;QAED,eAAe;QACf,UAAU,IAAI,qBAAqB,CAAC;QACpC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACxC,UAAU,IAAI,8BAA8B,IAAI,CAAC,iBAAiB,MAAM,CAAC;SAC5E;QAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC1C,UAAU,IAAI,8BAA8B,IAAI,CAAC,iBAAiB,MAAM,CAAC;SAC5E;QAED,UAAU,IAAI,yBAAyB,CAAC;QAExC,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,cAAoC;QACjD,MAAM,mBAAmB,GAAG,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACtF,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO;QAErF,IAAI,MAAM,GAAwB,EAAE,CAAC;QAErC,IAAI,cAAc,EAAE;YAChB,MAAM,GAAG,cAAc,CAAC;SAC3B;aAAM;YACH,mBAAmB,CAAC,UAAU,GAAG,sBAAsB,CAAC;YACxD,mBAAmB,CAAC,WAAW,GAAG,EAAE,CAAC;YAErC,UAAU;YACV,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC9C,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBACvC,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;aAC7D;YAED,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAChD,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAEvC,IAAI,mBAAmB,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;oBACrE,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;iBAC7D;aACJ;SACJ;QAED,SAAS;QACT,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAEhC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YACxB,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;SACtD;QAED,IAAI,CAAC,cAAc,EAAE;YACjB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC9B,SAAS;iBACZ;gBACD,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;aACtD;SACJ;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEO,mBAAmB,CAAC,KAAwB,EAAE,MAAW,EAAE,GAAyC;QACxG,KAAK,MAAM,WAAW,IAAI,KAAK,CAAC,OAAO,EAAE;YACrC,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,MAAM,EAAE;gBACnC,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBAEjC,IAAI,CAAC,MAAM,EAAE;oBACT,SAAS;iBACZ;gBAED,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE;oBAClC,IAAI,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,KAAK,IAAI,KAAK,CAAC,oBAAoB,KAAK,WAAW,CAAC,IAAI,EAAE;wBACvF,MAAM,UAAU,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;wBAC1D,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,WAAW,EAAE;4BACvC,SAAS;yBACZ;wBAED,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;wBACxC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;wBAC9C,SAAS;qBACZ;iBACJ;aACJ;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,qBAAqB,CAAC,MAAW,EAAE,UAAkB,EAAE,EAAE,KAAK,GAAG,KAAK;QACzE,IAAI,CAAC,KAAK,EAAE;YACR,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;QAED,MAAM,GAAG,GAAyC,EAAE,CAAC;QAErD,gBAAgB;QAChB,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE;YACrC,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACnD,IAAI,SAAS,EAAE;gBACX,MAAM,KAAK,GAAsB,IAAI,SAAS,EAAE,CAAC;gBACjD,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;gBAC1D,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;gBAE5B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACnC;SACJ;QAED,0BAA0B;QAC1B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;YACrC,IAAI,KAAK,CAAC,aAAa,EAAE;gBACrB,MAAM,WAAW,GAAG,KAAqC,CAAC;gBAC1D,MAAM,EAAE,GAAG,WAAW,CAAC,uBAAuB,CAAC;gBAC/C,IAAI,EAAE,EAAE;oBACJ,MAAM,MAAM,GAAG,GAAG,CAAC,EAAE,CAAgC,CAAC;oBACtD,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;iBACxC;aACJ;SACJ;QAED,mGAAmG;QACnG,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;YACtE,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAElC,IAAI,CAAC,KAAK,EAAE;gBACR,SAAS;aACZ;YAED,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE;gBAC/B,SAAS;aACZ;YACD,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;SAChD;QAED,UAAU;QACV,IAAI,MAAM,CAAC,WAAW,EAAE;YACpB,KAAK,MAAM,YAAY,IAAI,MAAM,CAAC,WAAW,EAAE;gBAC3C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;aACzC;SACJ;QAED,kBAAkB;QAClB,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;YACxE,MAAM,SAAS,GAIT,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC;YAEtD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;gBAC9B,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;oBACvB,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC;iBACrD;aACJ;YAED,IAAI,KAAK,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;gBACvD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;aAC/C;YAED,IAAI,MAAM,CAAC,SAAS,EAAE;gBAClB,IAAI,CAAC,UAAU,GAAG;oBACd,SAAS,EAAE,SAAS;iBACvB,CAAC;aACL;iBAAM;gBACH,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;gBACpC,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;aACzC;YAED,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;gBACnB,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;aACrC;YAED,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,QAAQ,CAAC;SAClC;QAED,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAE9B,IAAI,MAAM,CAAC,kBAAkB,KAAK,SAAS,EAAE;YACzC,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;SACvD;QAED,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE;YAChC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;SACrC;QAED,IAAI,CAAC,KAAK,EAAE;YACR,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,IAAI,iBAAiB,CAAC,QAAQ,CAAC;SAC1D;IACL,CAAC;IAED;;;;;;OAMG;IACI,qBAAqB,CAAC,MAAW,EAAE,UAAkB,EAAE,EAAE,KAAK,GAAG,KAAK;QACzE,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACvD,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,IAAY,EAAE,cAAuB,KAAK;QACnD,MAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE7C,MAAM,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;QAC3G,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC;QAChB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAElB,KAAK,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;QACjD,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;QAEjC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,sBAAsB;QACzB,4CAA4C;QAC5C,MAAM,oBAAoB,GAAoB,EAAE,CAAC;QACjD,IAAI,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACzC,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;YACrD,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE;gBAC7C,oBAAoB,CAAC,IAAI,CACrB,IAAI,OAAO,CAAC,CAAC,cAAc,EAAE,aAAa,EAAE,EAAE;oBAC1C,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,EAAE;wBAC5C,cAAc,EAAE,CAAC;oBACrB,CAAC,CAAC,CAAC;oBACH,eAAe,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;wBAC5C,aAAa,CAAC,CAAC,CAAC,CAAC;oBACrB,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CACL,CAAC;aACL;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,UAAkB,EAAE;QAC/D,MAAM,YAAY,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAEnH,YAAY,CAAC,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACpD,YAAY,CAAC,KAAK,EAAE,CAAC;QAErB,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAClC,IAAY,EACZ,GAAW,EACX,KAAY,EACZ,UAAkB,EAAE,EACpB,YAAqB,KAAK,EAC1B,cAA6B;QAE7B,MAAM,QAAQ,GAAG,cAAc,IAAI,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAEjE,MAAM,IAAI,GAAG,MAAM,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAC7C,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7C,QAAQ,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;QAC7D,IAAI,CAAC,SAAS,EAAE;YACZ,QAAQ,CAAC,KAAK,EAAE,CAAC;SACpB;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAC,qBAAqB,CAC/B,SAAiB,EACjB,QAAe,WAAW,CAAC,gBAAiB,EAC5C,UAAkB,EAAE,EACpB,YAA2B,EAC3B,YAAqB,KAAK,EAC1B,0BAAmC,KAAK;QAExC,IAAI,SAAS,KAAK,QAAQ,EAAE;YACxB,OAAO,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;SACtE;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE;oBACzB,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE;wBACvB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;wBACzE,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBAE7D,IAAI,CAAC,YAAY,EAAE;4BACf,YAAY,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;4BACxH,YAAY,CAAC,QAAQ,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;yBAC/C;wBAED,YAAY,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,CAAC;wBACxD,YAAY,CAAC,SAAS,GAAG,SAAS,CAAC;wBAEnC,IAAI;4BACA,IAAI,CAAC,SAAS,EAAE;gCACZ,YAAY,CAAC,KAAK,EAAE,CAAC;6BACxB;yBACJ;wBAAC,OAAO,GAAG,EAAE;4BACV,MAAM,CAAC,GAAG,CAAC,CAAC;yBACf;wBAED,IAAI,uBAAuB,EAAE;4BACzB,YAAY;iCACP,sBAAsB,EAAE;iCACxB,IAAI,CAAC,GAAG,EAAE;gCACP,OAAO,CAAC,YAAa,CAAC,CAAC;4BAC3B,CAAC,CAAC;iCACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;gCACX,MAAM,CAAC,GAAG,CAAC,CAAC;4BAChB,CAAC,CAAC,CAAC;yBACV;6BAAM;4BACH,OAAO,CAAC,YAAY,CAAC,CAAC;yBACzB;qBACJ;yBAAM;wBACH,MAAM,CAAC,6BAA6B,GAAG,SAAS,CAAC,CAAC;qBACrD;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;YAC1E,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,aAAa,CAAC,IAAY,EAAE,KAAa;QACnD,MAAM,WAAW,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAElD,WAAW,CAAC,YAAY,EAAE,CAAC;QAC3B,WAAW,CAAC,KAAK,EAAE,CAAC;QAEpB,OAAO,WAAW,CAAC;IACvB,CAAC;;AAvsEc,8BAAiB,GAAW,CAAC,AAAZ,CAAa;AAY7C,gDAAgD;AAClC,sBAAS,GAAG,GAAG,KAAK,CAAC,cAAc,KAAK,MAAM,CAAC,OAAO,mCAAmC,AAAhF,CAAiF;AAExG,sCAAsC;AACxB,uBAAU,GAAG,SAAS,CAAC,UAAU,AAAvB,CAAwB;AAEhD,wHAAwH;AAC1G,qCAAwB,GAAG,KAAK,AAAR,CAAS;AAyDxC;IADN,SAAS,EAAE;iDACe;AAMpB;IADN,SAAS,EAAE;2DACqB;AA4D1B;IADN,SAAS,CAAC,MAAM,CAAC;2CAC2C;AA0BtD;IADN,SAAS,CAAC,SAAS,CAAC;6CACE;AA6PhB;IADN,SAAS,EAAE;wDACsB;AAqyDtC,aAAa,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { NodeMaterialBlock } from \"./nodeMaterialBlock\";\r\nimport { PushMaterial } from \"../pushMaterial\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Matrix, Vector2 } from \"../../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../../Maths/math.color\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport { Engine } from \"../../Engines/engine\";\r\nimport { NodeMaterialBuildState } from \"./nodeMaterialBuildState\";\r\nimport type { IEffectCreationOptions } from \"../effect\";\r\nimport { Effect } from \"../effect\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { NodeMaterialBlockTargets } from \"./Enums/nodeMaterialBlockTargets\";\r\nimport { NodeMaterialBuildStateSharedData } from \"./nodeMaterialBuildStateSharedData\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport { MaterialDefines } from \"../../Materials/materialDefines\";\r\nimport type { NodeMaterialOptimizer } from \"./Optimizers/nodeMaterialOptimizer\";\r\nimport type { ImageProcessingConfiguration } from \"../imageProcessingConfiguration\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { VertexBuffer } from \"../../Buffers/buffer\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport { TransformBlock } from \"./Blocks/transformBlock\";\r\nimport { VertexOutputBlock } from \"./Blocks/Vertex/vertexOutputBlock\";\r\nimport { FragmentOutputBlock } from \"./Blocks/Fragment/fragmentOutputBlock\";\r\nimport { InputBlock } from \"./Blocks/Input/inputBlock\";\r\nimport { GetClass, RegisterClass } from \"../../Misc/typeStore\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport { SerializationHelper } from \"../../Misc/decorators.serialization\";\r\nimport type { TextureBlock } from \"./Blocks/Dual/textureBlock\";\r\nimport type { ReflectionTextureBaseBlock } from \"./Blocks/Dual/reflectionTextureBaseBlock\";\r\nimport type { RefractionBlock } from \"./Blocks/PBR/refractionBlock\";\r\nimport { CurrentScreenBlock } from \"./Blocks/Dual/currentScreenBlock\";\r\nimport { ParticleTextureBlock } from \"./Blocks/Particle/particleTextureBlock\";\r\nimport { ParticleRampGradientBlock } from \"./Blocks/Particle/particleRampGradientBlock\";\r\nimport { ParticleBlendMultiplyBlock } from \"./Blocks/Particle/particleBlendMultiplyBlock\";\r\nimport { EffectFallbacks } from \"../effectFallbacks\";\r\nimport { WebRequest } from \"../../Misc/webRequest\";\r\nimport type { PostProcessOptions } from \"../../PostProcesses/postProcess\";\r\nimport { PostProcess } from \"../../PostProcesses/postProcess\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport { VectorMergerBlock } from \"./Blocks/vectorMergerBlock\";\r\nimport { RemapBlock } from \"./Blocks/remapBlock\";\r\nimport { MultiplyBlock } from \"./Blocks/multiplyBlock\";\r\nimport { NodeMaterialModes } from \"./Enums/nodeMaterialModes\";\r\nimport { Texture } from \"../Textures/texture\";\r\nimport type { IParticleSystem } from \"../../Particles/IParticleSystem\";\r\nimport { BaseParticleSystem } from \"../../Particles/baseParticleSystem\";\r\nimport { ColorSplitterBlock } from \"./Blocks/colorSplitterBlock\";\r\nimport { TimingTools } from \"../../Misc/timingTools\";\r\nimport { ProceduralTexture } from \"../Textures/Procedurals/proceduralTexture\";\r\nimport { AnimatedInputBlockTypes } from \"./Blocks/Input/animatedInputBlockTypes\";\r\nimport { TrigonometryBlock, TrigonometryBlockOperations } from \"./Blocks/trigonometryBlock\";\r\nimport { NodeMaterialSystemValues } from \"./Enums/nodeMaterialSystemValues\";\r\nimport type { ImageSourceBlock } from \"./Blocks/Dual/imageSourceBlock\";\r\nimport { EngineStore } from \"../../Engines/engineStore\";\r\nimport type { Material } from \"../material\";\r\nimport type { TriPlanarBlock } from \"./Blocks/triPlanarBlock\";\r\nimport type { BiPlanarBlock } from \"./Blocks/biPlanarBlock\";\r\nimport type { PrePassRenderer } from \"../../Rendering/prePassRenderer\";\r\nimport type { PrePassTextureBlock } from \"./Blocks/Input/prePassTextureBlock\";\r\nimport type { PrePassOutputBlock } from \"./Blocks/Fragment/prePassOutputBlock\";\r\nimport type { NodeMaterialTeleportOutBlock } from \"./Blocks/Teleport/teleportOutBlock\";\r\nimport type { NodeMaterialTeleportInBlock } from \"./Blocks/Teleport/teleportInBlock\";\r\nimport { Logger } from \"core/Misc/logger\";\r\nimport { PrepareDefinesForCamera, PrepareDefinesForPrePass } from \"../materialHelper.functions\";\r\nimport type { IImageProcessingConfigurationDefines } from \"../imageProcessingConfiguration.defines\";\r\n\r\nconst onCreatedEffectParameters = { effect: null as unknown as Effect, subMesh: null as unknown as Nullable<SubMesh> };\r\n\r\n// declare NODEEDITOR namespace for compilation issue\r\ndeclare let NODEEDITOR: any;\r\ndeclare let BABYLON: any;\r\n\r\n/**\r\n * Interface used to configure the node material editor\r\n */\r\nexport interface INodeMaterialEditorOptions {\r\n    /** Define the URL to load node editor script from */\r\n    editorURL?: string;\r\n    /** Additional configuration for the NME */\r\n    nodeEditorConfig?: {\r\n        backgroundColor?: Color4;\r\n    };\r\n}\r\n\r\n/** @internal */\r\nexport class NodeMaterialDefines extends MaterialDefines implements IImageProcessingConfigurationDefines {\r\n    /** Normal */\r\n    public NORMAL = false;\r\n    /** Tangent */\r\n    public TANGENT = false;\r\n    /** Vertex color */\r\n    public VERTEXCOLOR_NME = false;\r\n    /**  Uv1 **/\r\n    public UV1 = false;\r\n    /** Uv2 **/\r\n    public UV2 = false;\r\n    /** Uv3 **/\r\n    public UV3 = false;\r\n    /** Uv4 **/\r\n    public UV4 = false;\r\n    /** Uv5 **/\r\n    public UV5 = false;\r\n    /** Uv6 **/\r\n    public UV6 = false;\r\n\r\n    /** Prepass **/\r\n    public PREPASS = false;\r\n    /** Prepass normal */\r\n    public PREPASS_NORMAL = false;\r\n    /** Prepass normal index */\r\n    public PREPASS_NORMAL_INDEX = -1;\r\n    /** Prepass position */\r\n    public PREPASS_POSITION = false;\r\n    /** Prepass position index */\r\n    public PREPASS_POSITION_INDEX = -1;\r\n    /** Prepass depth */\r\n    public PREPASS_DEPTH = false;\r\n    /** Prepass depth index */\r\n    public PREPASS_DEPTH_INDEX = -1;\r\n    /** Scene MRT count */\r\n    public SCENE_MRT_COUNT = 0;\r\n\r\n    /** BONES */\r\n    public NUM_BONE_INFLUENCERS = 0;\r\n    /** Bones per mesh */\r\n    public BonesPerMesh = 0;\r\n    /** Using texture for bone storage */\r\n    public BONETEXTURE = false;\r\n\r\n    /** MORPH TARGETS */\r\n    public MORPHTARGETS = false;\r\n    /** Morph target normal */\r\n    public MORPHTARGETS_NORMAL = false;\r\n    /** Morph target tangent */\r\n    public MORPHTARGETS_TANGENT = false;\r\n    /** Morph target uv */\r\n    public MORPHTARGETS_UV = false;\r\n    /** Number of morph influencers */\r\n    public NUM_MORPH_INFLUENCERS = 0;\r\n    /** Using a texture to store morph target data */\r\n    public MORPHTARGETS_TEXTURE = false;\r\n\r\n    /** IMAGE PROCESSING */\r\n    public IMAGEPROCESSING = false;\r\n    /** Vignette */\r\n    public VIGNETTE = false;\r\n    /** Multiply blend mode for vignette */\r\n    public VIGNETTEBLENDMODEMULTIPLY = false;\r\n    /** Opaque blend mode for vignette */\r\n    public VIGNETTEBLENDMODEOPAQUE = false;\r\n    /** Tone mapping */\r\n    public TONEMAPPING = false;\r\n    /** ACES tone mapping mode */\r\n    public TONEMAPPING_ACES = false;\r\n    /** Contrast */\r\n    public CONTRAST = false;\r\n    /** Exposure */\r\n    public EXPOSURE = false;\r\n    /** Color curves */\r\n    public COLORCURVES = false;\r\n    /** Color grading */\r\n    public COLORGRADING = false;\r\n    /** 3D color grading */\r\n    public COLORGRADING3D = false;\r\n    /** Sampler green depth */\r\n    public SAMPLER3DGREENDEPTH = false;\r\n    /** Sampler for BGR map */\r\n    public SAMPLER3DBGRMAP = false;\r\n    /** Dithering */\r\n    public DITHER = false;\r\n    /** Using post process for image processing */\r\n    public IMAGEPROCESSINGPOSTPROCESS = false;\r\n    /** Skip color clamp */\r\n    public SKIPFINALCOLORCLAMP = false;\r\n\r\n    /** MISC. */\r\n    public BUMPDIRECTUV = 0;\r\n    /** Camera is orthographic */\r\n    public CAMERA_ORTHOGRAPHIC = false;\r\n    /** Camera is perspective */\r\n    public CAMERA_PERSPECTIVE = false;\r\n\r\n    /**\r\n     * Creates a new NodeMaterialDefines\r\n     */\r\n    constructor() {\r\n        super();\r\n        this.rebuild();\r\n    }\r\n\r\n    /**\r\n     * Set the value of a specific key\r\n     * @param name defines the name of the key to set\r\n     * @param value defines the value to set\r\n     * @param markAsUnprocessedIfDirty Flag to indicate to the cache that this value needs processing\r\n     */\r\n    public setValue(name: string, value: any, markAsUnprocessedIfDirty = false) {\r\n        if (this[name] === undefined) {\r\n            this._keys.push(name);\r\n        }\r\n\r\n        if (markAsUnprocessedIfDirty && this[name] !== value) {\r\n            this.markAsUnprocessed();\r\n        }\r\n\r\n        this[name] = value;\r\n    }\r\n}\r\n\r\n/**\r\n * Class used to configure NodeMaterial\r\n */\r\nexport interface INodeMaterialOptions {\r\n    /**\r\n     * Defines if blocks should emit comments\r\n     */\r\n    emitComments: boolean;\r\n}\r\n\r\n/**\r\n * Blocks that manage a texture\r\n */\r\nexport type NodeMaterialTextureBlocks =\r\n    | TextureBlock\r\n    | ReflectionTextureBaseBlock\r\n    | RefractionBlock\r\n    | CurrentScreenBlock\r\n    | ParticleTextureBlock\r\n    | ImageSourceBlock\r\n    | TriPlanarBlock\r\n    | BiPlanarBlock\r\n    | PrePassTextureBlock;\r\n\r\n/**\r\n * Class used to create a node based material built by assembling shader blocks\r\n */\r\nexport class NodeMaterial extends PushMaterial {\r\n    private static _BuildIdGenerator: number = 0;\r\n    private _options: INodeMaterialOptions;\r\n    private _vertexCompilationState: NodeMaterialBuildState;\r\n    private _fragmentCompilationState: NodeMaterialBuildState;\r\n    private _sharedData: NodeMaterialBuildStateSharedData;\r\n    private _buildId: number = NodeMaterial._BuildIdGenerator++;\r\n    private _buildWasSuccessful = false;\r\n    private _cachedWorldViewMatrix = new Matrix();\r\n    private _cachedWorldViewProjectionMatrix = new Matrix();\r\n    private _optimizers = new Array<NodeMaterialOptimizer>();\r\n    private _animationFrame = -1;\r\n\r\n    /** Define the Url to load node editor script */\r\n    public static EditorURL = `${Tools._DefaultCdnUrl}/v${Engine.Version}/nodeEditor/babylon.nodeEditor.js`;\r\n\r\n    /** Define the Url to load snippets */\r\n    public static SnippetUrl = Constants.SnippetUrl;\r\n\r\n    /** Gets or sets a boolean indicating that node materials should not deserialize textures from json / snippet content */\r\n    public static IgnoreTexturesAtLoadTime = false;\r\n\r\n    /**\r\n     * Checks if a block is a texture block\r\n     * @param block The block to check\r\n     * @returns True if the block is a texture block\r\n     */\r\n    public static _BlockIsTextureBlock(block: NodeMaterialBlock): block is NodeMaterialTextureBlocks {\r\n        return (\r\n            block.getClassName() === \"TextureBlock\" ||\r\n            block.getClassName() === \"ReflectionTextureBaseBlock\" ||\r\n            block.getClassName() === \"ReflectionTextureBlock\" ||\r\n            block.getClassName() === \"ReflectionBlock\" ||\r\n            block.getClassName() === \"RefractionBlock\" ||\r\n            block.getClassName() === \"CurrentScreenBlock\" ||\r\n            block.getClassName() === \"ParticleTextureBlock\" ||\r\n            block.getClassName() === \"ImageSourceBlock\" ||\r\n            block.getClassName() === \"TriPlanarBlock\" ||\r\n            block.getClassName() === \"BiPlanarBlock\" ||\r\n            block.getClassName() === \"PrePassTextureBlock\"\r\n        );\r\n    }\r\n\r\n    private BJSNODEMATERIALEDITOR = this._getGlobalNodeMaterialEditor();\r\n\r\n    /** Get the inspector from bundle or global\r\n     * @returns the global NME\r\n     */\r\n    private _getGlobalNodeMaterialEditor(): any {\r\n        // UMD Global name detection from Webpack Bundle UMD Name.\r\n        if (typeof NODEEDITOR !== \"undefined\") {\r\n            return NODEEDITOR;\r\n        }\r\n\r\n        // In case of module let's check the global emitted from the editor entry point.\r\n        if (typeof BABYLON !== \"undefined\" && typeof BABYLON.NodeEditor !== \"undefined\") {\r\n            return BABYLON;\r\n        }\r\n\r\n        return undefined;\r\n    }\r\n\r\n    /**\r\n     * Snippet ID if the material was created from the snippet server\r\n     */\r\n    public snippetId: string;\r\n\r\n    /**\r\n     * Gets or sets data used by visual editor\r\n     * @see https://nme.babylonjs.com\r\n     */\r\n    public editorData: any = null;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that alpha value must be ignored (This will turn alpha blending off even if an alpha value is produced by the material)\r\n     */\r\n    @serialize()\r\n    public ignoreAlpha = false;\r\n\r\n    /**\r\n     * Defines the maximum number of lights that can be used in the material\r\n     */\r\n    @serialize()\r\n    public maxSimultaneousLights = 4;\r\n\r\n    /**\r\n     * Observable raised when the material is built\r\n     */\r\n    public onBuildObservable = new Observable<NodeMaterial>();\r\n\r\n    /**\r\n     * Gets or sets the root nodes of the material vertex shader\r\n     */\r\n    public _vertexOutputNodes = new Array<NodeMaterialBlock>();\r\n\r\n    /**\r\n     * Gets or sets the root nodes of the material fragment (pixel) shader\r\n     */\r\n    public _fragmentOutputNodes = new Array<NodeMaterialBlock>();\r\n\r\n    /** Gets or sets options to control the node material overall behavior */\r\n    public get options() {\r\n        return this._options;\r\n    }\r\n\r\n    public set options(options: INodeMaterialOptions) {\r\n        this._options = options;\r\n    }\r\n\r\n    /**\r\n     * Default configuration related to image processing available in the standard Material.\r\n     */\r\n    protected _imageProcessingConfiguration: ImageProcessingConfiguration;\r\n\r\n    /**\r\n     * Gets the image processing configuration used either in this material.\r\n     */\r\n    public get imageProcessingConfiguration(): ImageProcessingConfiguration {\r\n        return this._imageProcessingConfiguration;\r\n    }\r\n\r\n    /**\r\n     * Sets the Default image processing configuration used either in the this material.\r\n     *\r\n     * If sets to null, the scene one is in use.\r\n     */\r\n    public set imageProcessingConfiguration(value: ImageProcessingConfiguration) {\r\n        this._attachImageProcessingConfiguration(value);\r\n\r\n        // Ensure the effect will be rebuilt.\r\n        this._markAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets an array of blocks that needs to be serialized even if they are not yet connected\r\n     */\r\n    public attachedBlocks: NodeMaterialBlock[] = [];\r\n\r\n    /**\r\n     * Specifies the mode of the node material\r\n     * @internal\r\n     */\r\n    @serialize(\"mode\")\r\n    public _mode: NodeMaterialModes = NodeMaterialModes.Material;\r\n\r\n    /**\r\n     * Gets or sets the mode property\r\n     */\r\n    public get mode(): NodeMaterialModes {\r\n        return this._mode;\r\n    }\r\n\r\n    public set mode(value: NodeMaterialModes) {\r\n        this._mode = value;\r\n    }\r\n\r\n    /** Gets or sets the unique identifier used to identified the effect associated with the material */\r\n    public get buildId() {\r\n        return this._buildId;\r\n    }\r\n\r\n    public set buildId(value: number) {\r\n        this._buildId = value;\r\n    }\r\n\r\n    /**\r\n     * A free comment about the material\r\n     */\r\n    @serialize(\"comment\")\r\n    public comment: string;\r\n\r\n    /**\r\n     * Create a new node based material\r\n     * @param name defines the material name\r\n     * @param scene defines the hosting scene\r\n     * @param options defines creation option\r\n     */\r\n    constructor(name: string, scene?: Scene, options: Partial<INodeMaterialOptions> = {}) {\r\n        super(name, scene || EngineStore.LastCreatedScene!);\r\n\r\n        this._options = {\r\n            emitComments: false,\r\n            ...options,\r\n        };\r\n\r\n        // Setup the default processing configuration to the scene.\r\n        this._attachImageProcessingConfiguration(null);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name of the material e.g. \"NodeMaterial\"\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"NodeMaterial\";\r\n    }\r\n\r\n    /**\r\n     * Keep track of the image processing observer to allow dispose and replace.\r\n     */\r\n    private _imageProcessingObserver: Nullable<Observer<ImageProcessingConfiguration>>;\r\n\r\n    /**\r\n     * Attaches a new image processing configuration to the Standard Material.\r\n     * @param configuration\r\n     */\r\n    protected _attachImageProcessingConfiguration(configuration: Nullable<ImageProcessingConfiguration>): void {\r\n        if (configuration === this._imageProcessingConfiguration) {\r\n            return;\r\n        }\r\n\r\n        // Detaches observer.\r\n        if (this._imageProcessingConfiguration && this._imageProcessingObserver) {\r\n            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);\r\n        }\r\n\r\n        // Pick the scene configuration if needed.\r\n        if (!configuration) {\r\n            this._imageProcessingConfiguration = this.getScene().imageProcessingConfiguration;\r\n        } else {\r\n            this._imageProcessingConfiguration = configuration;\r\n        }\r\n\r\n        // Attaches observer.\r\n        if (this._imageProcessingConfiguration) {\r\n            this._imageProcessingObserver = this._imageProcessingConfiguration.onUpdateParameters.add(() => {\r\n                this._markAllSubMeshesAsImageProcessingDirty();\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get a block by its name\r\n     * @param name defines the name of the block to retrieve\r\n     * @returns the required block or null if not found\r\n     */\r\n    public getBlockByName(name: string) {\r\n        let result = null;\r\n        for (const block of this.attachedBlocks) {\r\n            if (block.name === name) {\r\n                if (!result) {\r\n                    result = block;\r\n                } else {\r\n                    Tools.Warn(\"More than one block was found with the name `\" + name + \"`\");\r\n                    return result;\r\n                }\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Get a block using a predicate\r\n     * @param predicate defines the predicate used to find the good candidate\r\n     * @returns the required block or null if not found\r\n     */\r\n    public getBlockByPredicate(predicate: (block: NodeMaterialBlock) => boolean) {\r\n        for (const block of this.attachedBlocks) {\r\n            if (predicate(block)) {\r\n                return block;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Get an input block using a predicate\r\n     * @param predicate defines the predicate used to find the good candidate\r\n     * @returns the required input block or null if not found\r\n     */\r\n    public getInputBlockByPredicate(predicate: (block: InputBlock) => boolean): Nullable<InputBlock> {\r\n        for (const block of this.attachedBlocks) {\r\n            if (block.isInput && predicate(block as InputBlock)) {\r\n                return block as InputBlock;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of input blocks attached to this material\r\n     * @returns an array of InputBlocks\r\n     */\r\n    public getInputBlocks() {\r\n        const blocks: InputBlock[] = [];\r\n        for (const block of this.attachedBlocks) {\r\n            if (block.isInput) {\r\n                blocks.push(block as InputBlock);\r\n            }\r\n        }\r\n\r\n        return blocks;\r\n    }\r\n\r\n    /**\r\n     * Adds a new optimizer to the list of optimizers\r\n     * @param optimizer defines the optimizers to add\r\n     * @returns the current material\r\n     */\r\n    public registerOptimizer(optimizer: NodeMaterialOptimizer) {\r\n        const index = this._optimizers.indexOf(optimizer);\r\n\r\n        if (index > -1) {\r\n            return;\r\n        }\r\n\r\n        this._optimizers.push(optimizer);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove an optimizer from the list of optimizers\r\n     * @param optimizer defines the optimizers to remove\r\n     * @returns the current material\r\n     */\r\n    public unregisterOptimizer(optimizer: NodeMaterialOptimizer) {\r\n        const index = this._optimizers.indexOf(optimizer);\r\n\r\n        if (index === -1) {\r\n            return;\r\n        }\r\n\r\n        this._optimizers.splice(index, 1);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Add a new block to the list of output nodes\r\n     * @param node defines the node to add\r\n     * @returns the current material\r\n     */\r\n    public addOutputNode(node: NodeMaterialBlock) {\r\n        if (node.target === null) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"This node is not meant to be an output node. You may want to explicitly set its target value.\";\r\n        }\r\n\r\n        if ((node.target & NodeMaterialBlockTargets.Vertex) !== 0) {\r\n            this._addVertexOutputNode(node);\r\n        }\r\n\r\n        if ((node.target & NodeMaterialBlockTargets.Fragment) !== 0) {\r\n            this._addFragmentOutputNode(node);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a block from the list of root nodes\r\n     * @param node defines the node to remove\r\n     * @returns the current material\r\n     */\r\n    public removeOutputNode(node: NodeMaterialBlock) {\r\n        if (node.target === null) {\r\n            return this;\r\n        }\r\n\r\n        if ((node.target & NodeMaterialBlockTargets.Vertex) !== 0) {\r\n            this._removeVertexOutputNode(node);\r\n        }\r\n\r\n        if ((node.target & NodeMaterialBlockTargets.Fragment) !== 0) {\r\n            this._removeFragmentOutputNode(node);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    private _addVertexOutputNode(node: NodeMaterialBlock) {\r\n        if (this._vertexOutputNodes.indexOf(node) !== -1) {\r\n            return;\r\n        }\r\n\r\n        node.target = NodeMaterialBlockTargets.Vertex;\r\n        this._vertexOutputNodes.push(node);\r\n\r\n        return this;\r\n    }\r\n\r\n    private _removeVertexOutputNode(node: NodeMaterialBlock) {\r\n        const index = this._vertexOutputNodes.indexOf(node);\r\n        if (index === -1) {\r\n            return;\r\n        }\r\n\r\n        this._vertexOutputNodes.splice(index, 1);\r\n\r\n        return this;\r\n    }\r\n\r\n    private _addFragmentOutputNode(node: NodeMaterialBlock) {\r\n        if (this._fragmentOutputNodes.indexOf(node) !== -1) {\r\n            return;\r\n        }\r\n\r\n        node.target = NodeMaterialBlockTargets.Fragment;\r\n        this._fragmentOutputNodes.push(node);\r\n\r\n        return this;\r\n    }\r\n\r\n    private _removeFragmentOutputNode(node: NodeMaterialBlock) {\r\n        const index = this._fragmentOutputNodes.indexOf(node);\r\n        if (index === -1) {\r\n            return;\r\n        }\r\n\r\n        this._fragmentOutputNodes.splice(index, 1);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that alpha blending must be enabled no matter what alpha value or alpha channel of the FragmentBlock are\r\n     */\r\n    @serialize()\r\n    public forceAlphaBlending = false;\r\n\r\n    /**\r\n     * Specifies if the material will require alpha blending\r\n     * @returns a boolean specifying if alpha blending is needed\r\n     */\r\n    public needAlphaBlending(): boolean {\r\n        if (this.ignoreAlpha) {\r\n            return false;\r\n        }\r\n        return this.forceAlphaBlending || this.alpha < 1.0 || (this._sharedData && this._sharedData.hints.needAlphaBlending);\r\n    }\r\n\r\n    /**\r\n     * Specifies if this material should be rendered in alpha test mode\r\n     * @returns a boolean specifying if an alpha test is needed.\r\n     */\r\n    public needAlphaTesting(): boolean {\r\n        return this._sharedData && this._sharedData.hints.needAlphaTesting;\r\n    }\r\n\r\n    private _processInitializeOnLink(block: NodeMaterialBlock, state: NodeMaterialBuildState, nodesToProcessForOtherBuildState: NodeMaterialBlock[], autoConfigure = true) {\r\n        if (block.target === NodeMaterialBlockTargets.VertexAndFragment) {\r\n            nodesToProcessForOtherBuildState.push(block);\r\n        } else if (state.target === NodeMaterialBlockTargets.Fragment && block.target === NodeMaterialBlockTargets.Vertex && block._preparationId !== this._buildId) {\r\n            nodesToProcessForOtherBuildState.push(block);\r\n        }\r\n        this._initializeBlock(block, state, nodesToProcessForOtherBuildState, autoConfigure);\r\n    }\r\n\r\n    private _initializeBlock(node: NodeMaterialBlock, state: NodeMaterialBuildState, nodesToProcessForOtherBuildState: NodeMaterialBlock[], autoConfigure = true) {\r\n        node.initialize(state);\r\n        if (autoConfigure) {\r\n            node.autoConfigure(this);\r\n        }\r\n        node._preparationId = this._buildId;\r\n\r\n        if (this.attachedBlocks.indexOf(node) === -1) {\r\n            if (node.isUnique) {\r\n                const className = node.getClassName();\r\n\r\n                for (const other of this.attachedBlocks) {\r\n                    if (other.getClassName() === className) {\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw `Cannot have multiple blocks of type ${className} in the same NodeMaterial`;\r\n                    }\r\n                }\r\n            }\r\n            this.attachedBlocks.push(node);\r\n        }\r\n\r\n        for (const input of node.inputs) {\r\n            input.associatedVariableName = \"\";\r\n\r\n            const connectedPoint = input.connectedPoint;\r\n            if (connectedPoint) {\r\n                const block = connectedPoint.ownerBlock;\r\n                if (block !== node) {\r\n                    this._processInitializeOnLink(block, state, nodesToProcessForOtherBuildState, autoConfigure);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Teleportation\r\n        if (node.isTeleportOut) {\r\n            const teleport = node as NodeMaterialTeleportOutBlock;\r\n            if (teleport.entryPoint) {\r\n                this._processInitializeOnLink(teleport.entryPoint, state, nodesToProcessForOtherBuildState, autoConfigure);\r\n            }\r\n        }\r\n\r\n        for (const output of node.outputs) {\r\n            output.associatedVariableName = \"\";\r\n        }\r\n    }\r\n\r\n    private _resetDualBlocks(node: NodeMaterialBlock, id: number) {\r\n        if (node.target === NodeMaterialBlockTargets.VertexAndFragment) {\r\n            node.buildId = id;\r\n        }\r\n\r\n        for (const inputs of node.inputs) {\r\n            const connectedPoint = inputs.connectedPoint;\r\n            if (connectedPoint) {\r\n                const block = connectedPoint.ownerBlock;\r\n                if (block !== node) {\r\n                    this._resetDualBlocks(block, id);\r\n                }\r\n            }\r\n        }\r\n\r\n        // If this is a teleport out, we need to reset the connected block\r\n        if (node.isTeleportOut) {\r\n            const teleportOut = node as NodeMaterialTeleportOutBlock;\r\n            if (teleportOut.entryPoint) {\r\n                this._resetDualBlocks(teleportOut.entryPoint, id);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove a block from the current node material\r\n     * @param block defines the block to remove\r\n     */\r\n    public removeBlock(block: NodeMaterialBlock) {\r\n        const attachedBlockIndex = this.attachedBlocks.indexOf(block);\r\n        if (attachedBlockIndex > -1) {\r\n            this.attachedBlocks.splice(attachedBlockIndex, 1);\r\n        }\r\n\r\n        if (block.isFinalMerger) {\r\n            this.removeOutputNode(block);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Build the material and generates the inner effect\r\n     * @param verbose defines if the build should log activity\r\n     * @param updateBuildId defines if the internal build Id should be updated (default is true)\r\n     * @param autoConfigure defines if the autoConfigure method should be called when initializing blocks (default is false)\r\n     */\r\n    public build(verbose: boolean = false, updateBuildId = true, autoConfigure = false) {\r\n        // First time?\r\n        if (!this._vertexCompilationState && !autoConfigure) {\r\n            autoConfigure = true;\r\n        }\r\n\r\n        this._buildWasSuccessful = false;\r\n        const engine = this.getScene().getEngine();\r\n\r\n        const allowEmptyVertexProgram = this._mode === NodeMaterialModes.Particle;\r\n\r\n        if (this._vertexOutputNodes.length === 0 && !allowEmptyVertexProgram) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"You must define at least one vertexOutputNode\";\r\n        }\r\n\r\n        if (this._fragmentOutputNodes.length === 0) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"You must define at least one fragmentOutputNode\";\r\n        }\r\n\r\n        // Compilation state\r\n        this._vertexCompilationState = new NodeMaterialBuildState();\r\n        this._vertexCompilationState.supportUniformBuffers = engine.supportsUniformBuffers;\r\n        this._vertexCompilationState.target = NodeMaterialBlockTargets.Vertex;\r\n        this._fragmentCompilationState = new NodeMaterialBuildState();\r\n        this._fragmentCompilationState.supportUniformBuffers = engine.supportsUniformBuffers;\r\n        this._fragmentCompilationState.target = NodeMaterialBlockTargets.Fragment;\r\n\r\n        // Shared data\r\n        this._sharedData = new NodeMaterialBuildStateSharedData();\r\n        this._sharedData.nodeMaterial = this;\r\n        this._sharedData.fragmentOutputNodes = this._fragmentOutputNodes;\r\n        this._vertexCompilationState.sharedData = this._sharedData;\r\n        this._fragmentCompilationState.sharedData = this._sharedData;\r\n        this._sharedData.buildId = this._buildId;\r\n        this._sharedData.emitComments = this._options.emitComments;\r\n        this._sharedData.verbose = verbose;\r\n        this._sharedData.scene = this.getScene();\r\n        this._sharedData.allowEmptyVertexProgram = allowEmptyVertexProgram;\r\n\r\n        // Initialize blocks\r\n        const vertexNodes: NodeMaterialBlock[] = [];\r\n        const fragmentNodes: NodeMaterialBlock[] = [];\r\n\r\n        for (const vertexOutputNode of this._vertexOutputNodes) {\r\n            vertexNodes.push(vertexOutputNode);\r\n            this._initializeBlock(vertexOutputNode, this._vertexCompilationState, fragmentNodes, autoConfigure);\r\n        }\r\n\r\n        for (const fragmentOutputNode of this._fragmentOutputNodes) {\r\n            fragmentNodes.push(fragmentOutputNode);\r\n            this._initializeBlock(fragmentOutputNode, this._fragmentCompilationState, vertexNodes, autoConfigure);\r\n        }\r\n\r\n        // Optimize\r\n        this.optimize();\r\n\r\n        // Vertex\r\n        for (const vertexOutputNode of vertexNodes) {\r\n            vertexOutputNode.build(this._vertexCompilationState, vertexNodes);\r\n        }\r\n\r\n        // Fragment\r\n        this._fragmentCompilationState.uniforms = this._vertexCompilationState.uniforms.slice(0);\r\n        this._fragmentCompilationState._uniformDeclaration = this._vertexCompilationState._uniformDeclaration;\r\n        this._fragmentCompilationState._constantDeclaration = this._vertexCompilationState._constantDeclaration;\r\n        this._fragmentCompilationState._vertexState = this._vertexCompilationState;\r\n\r\n        for (const fragmentOutputNode of fragmentNodes) {\r\n            this._resetDualBlocks(fragmentOutputNode, this._buildId - 1);\r\n        }\r\n\r\n        for (const fragmentOutputNode of fragmentNodes) {\r\n            fragmentOutputNode.build(this._fragmentCompilationState, fragmentNodes);\r\n        }\r\n\r\n        // Finalize\r\n        this._vertexCompilationState.finalize(this._vertexCompilationState);\r\n        this._fragmentCompilationState.finalize(this._fragmentCompilationState);\r\n\r\n        if (updateBuildId) {\r\n            this._buildId = NodeMaterial._BuildIdGenerator++;\r\n        }\r\n\r\n        // Errors\r\n        this._sharedData.emitErrors();\r\n\r\n        if (verbose) {\r\n            Logger.Log(\"Vertex shader:\");\r\n            Logger.Log(this._vertexCompilationState.compilationString);\r\n            Logger.Log(\"Fragment shader:\");\r\n            Logger.Log(this._fragmentCompilationState.compilationString);\r\n        }\r\n\r\n        this._buildWasSuccessful = true;\r\n        this.onBuildObservable.notifyObservers(this);\r\n\r\n        // Wipe defines\r\n        const meshes = this.getScene().meshes;\r\n        for (const mesh of meshes) {\r\n            if (!mesh.subMeshes) {\r\n                continue;\r\n            }\r\n            for (const subMesh of mesh.subMeshes) {\r\n                if (subMesh.getMaterial() !== this) {\r\n                    continue;\r\n                }\r\n\r\n                if (!subMesh.materialDefines) {\r\n                    continue;\r\n                }\r\n\r\n                const defines = subMesh.materialDefines;\r\n                defines.markAllAsDirty();\r\n                defines.reset();\r\n            }\r\n        }\r\n\r\n        if (this.prePassTextureInputs.length) {\r\n            this.getScene().enablePrePassRenderer();\r\n        }\r\n        const prePassRenderer = this.getScene().prePassRenderer;\r\n        if (prePassRenderer) {\r\n            prePassRenderer.markAsDirty();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Runs an otpimization phase to try to improve the shader code\r\n     */\r\n    public optimize() {\r\n        for (const optimizer of this._optimizers) {\r\n            optimizer.optimize(this._vertexOutputNodes, this._fragmentOutputNodes);\r\n        }\r\n    }\r\n\r\n    private _prepareDefinesForAttributes(mesh: AbstractMesh, defines: NodeMaterialDefines) {\r\n        const oldNormal = defines[\"NORMAL\"];\r\n        const oldTangent = defines[\"TANGENT\"];\r\n        const oldColor = defines[\"VERTEXCOLOR_NME\"];\r\n\r\n        defines[\"NORMAL\"] = mesh.isVerticesDataPresent(VertexBuffer.NormalKind);\r\n        defines[\"TANGENT\"] = mesh.isVerticesDataPresent(VertexBuffer.TangentKind);\r\n\r\n        const hasVertexColors = mesh.useVertexColors && mesh.isVerticesDataPresent(VertexBuffer.ColorKind);\r\n        defines[\"VERTEXCOLOR_NME\"] = hasVertexColors;\r\n\r\n        let uvChanged = false;\r\n        for (let i = 1; i <= Constants.MAX_SUPPORTED_UV_SETS; ++i) {\r\n            const oldUV = defines[\"UV\" + i];\r\n            defines[\"UV\" + i] = mesh.isVerticesDataPresent(`uv${i === 1 ? \"\" : i}`);\r\n            uvChanged = uvChanged || defines[\"UV\" + i] !== oldUV;\r\n        }\r\n\r\n        // PrePass\r\n        const oit = this.needAlphaBlendingForMesh(mesh) && this.getScene().useOrderIndependentTransparency;\r\n        PrepareDefinesForPrePass(this.getScene(), defines, !oit);\r\n\r\n        if (oldNormal !== defines[\"NORMAL\"] || oldTangent !== defines[\"TANGENT\"] || oldColor !== defines[\"VERTEXCOLOR_NME\"] || uvChanged) {\r\n            defines.markAsAttributesDirty();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Can this material render to prepass\r\n     */\r\n    public get isPrePassCapable(): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Outputs written to the prepass\r\n     */\r\n    public get prePassTextureOutputs(): number[] {\r\n        const prePassOutputBlock = this.getBlockByPredicate((block) => block.getClassName() === \"PrePassOutputBlock\") as PrePassOutputBlock;\r\n        const result = [Constants.PREPASS_COLOR_TEXTURE_TYPE];\r\n        if (!prePassOutputBlock) {\r\n            return result;\r\n        }\r\n        // Cannot write to prepass if we alread read from prepass\r\n        if (this.prePassTextureInputs.length) {\r\n            return result;\r\n        }\r\n\r\n        if (prePassOutputBlock.viewDepth.isConnected) {\r\n            result.push(Constants.PREPASS_DEPTH_TEXTURE_TYPE);\r\n        }\r\n\r\n        if (prePassOutputBlock.viewNormal.isConnected) {\r\n            result.push(Constants.PREPASS_NORMAL_TEXTURE_TYPE);\r\n        }\r\n\r\n        if (prePassOutputBlock.worldPosition.isConnected) {\r\n            result.push(Constants.PREPASS_POSITION_TEXTURE_TYPE);\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of prepass texture required\r\n     */\r\n    public get prePassTextureInputs(): number[] {\r\n        const prePassTextureBlocks = this.getAllTextureBlocks().filter((block) => block.getClassName() === \"PrePassTextureBlock\") as PrePassTextureBlock[];\r\n        const result = [] as number[];\r\n\r\n        for (const block of prePassTextureBlocks) {\r\n            if (block.position.isConnected && !result.includes(Constants.PREPASS_POSITION_TEXTURE_TYPE)) {\r\n                result.push(Constants.PREPASS_POSITION_TEXTURE_TYPE);\r\n            }\r\n            if (block.depth.isConnected && !result.includes(Constants.PREPASS_DEPTH_TEXTURE_TYPE)) {\r\n                result.push(Constants.PREPASS_DEPTH_TEXTURE_TYPE);\r\n            }\r\n            if (block.normal.isConnected && !result.includes(Constants.PREPASS_NORMAL_TEXTURE_TYPE)) {\r\n                result.push(Constants.PREPASS_NORMAL_TEXTURE_TYPE);\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Sets the required values to the prepass renderer.\r\n     * @param prePassRenderer defines the prepass renderer to set\r\n     * @returns true if the pre pass is needed\r\n     */\r\n    public setPrePassRenderer(prePassRenderer: PrePassRenderer): boolean {\r\n        const prePassTexturesRequired = this.prePassTextureInputs.concat(this.prePassTextureOutputs);\r\n\r\n        if (prePassRenderer && prePassTexturesRequired.length > 1) {\r\n            let cfg = prePassRenderer.getEffectConfiguration(\"nodeMaterial\");\r\n            if (!cfg) {\r\n                cfg = prePassRenderer.addEffectConfiguration({\r\n                    enabled: true,\r\n                    needsImageProcessing: false,\r\n                    name: \"nodeMaterial\",\r\n                    texturesRequired: [],\r\n                });\r\n            }\r\n            for (const prePassTexture of prePassTexturesRequired) {\r\n                if (!cfg.texturesRequired.includes(prePassTexture)) {\r\n                    cfg.texturesRequired.push(prePassTexture);\r\n                }\r\n            }\r\n            cfg.enabled = true;\r\n        }\r\n\r\n        // COLOR_TEXTURE is always required for prepass, length > 1 means\r\n        // we actually need to write to special prepass textures\r\n        return prePassTexturesRequired.length > 1;\r\n    }\r\n\r\n    /**\r\n     * Create a post process from the material\r\n     * @param camera The camera to apply the render pass to.\r\n     * @param options The required width/height ratio to downsize to before computing the render pass. (Use 1.0 for full size)\r\n     * @param samplingMode The sampling mode to be used when computing the pass. (default: 0)\r\n     * @param engine The engine which the post process will be applied. (default: current engine)\r\n     * @param reusable If the post process can be reused on the same frame. (default: false)\r\n     * @param textureType Type of textures used when performing the post process. (default: 0)\r\n     * @param textureFormat Format of textures used when performing the post process. (default: TEXTUREFORMAT_RGBA)\r\n     * @returns the post process created\r\n     */\r\n    public createPostProcess(\r\n        camera: Nullable<Camera>,\r\n        options: number | PostProcessOptions = 1,\r\n        samplingMode: number = Constants.TEXTURE_NEAREST_SAMPLINGMODE,\r\n        engine?: Engine,\r\n        reusable?: boolean,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        textureFormat = Constants.TEXTUREFORMAT_RGBA\r\n    ): Nullable<PostProcess> {\r\n        if (this.mode !== NodeMaterialModes.PostProcess) {\r\n            Logger.Log(\"Incompatible material mode\");\r\n            return null;\r\n        }\r\n        return this._createEffectForPostProcess(null, camera, options, samplingMode, engine, reusable, textureType, textureFormat);\r\n    }\r\n\r\n    /**\r\n     * Create the post process effect from the material\r\n     * @param postProcess The post process to create the effect for\r\n     */\r\n    public createEffectForPostProcess(postProcess: PostProcess) {\r\n        this._createEffectForPostProcess(postProcess);\r\n    }\r\n\r\n    private _createEffectForPostProcess(\r\n        postProcess: Nullable<PostProcess>,\r\n        camera?: Nullable<Camera>,\r\n        options: number | PostProcessOptions = 1,\r\n        samplingMode: number = Constants.TEXTURE_NEAREST_SAMPLINGMODE,\r\n        engine?: Engine,\r\n        reusable?: boolean,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        textureFormat = Constants.TEXTUREFORMAT_RGBA\r\n    ): PostProcess {\r\n        let tempName = this.name + this._buildId;\r\n\r\n        const defines = new NodeMaterialDefines();\r\n\r\n        const dummyMesh = new AbstractMesh(tempName + \"PostProcess\", this.getScene());\r\n\r\n        let buildId = this._buildId;\r\n\r\n        this._processDefines(dummyMesh, defines);\r\n\r\n        Effect.RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, this._vertexCompilationState._builtCompilationString);\r\n\r\n        if (!postProcess) {\r\n            postProcess = new PostProcess(\r\n                this.name + \"PostProcess\",\r\n                tempName,\r\n                this._fragmentCompilationState.uniforms,\r\n                this._fragmentCompilationState.samplers,\r\n                options,\r\n                camera!,\r\n                samplingMode,\r\n                engine,\r\n                reusable,\r\n                defines.toString(),\r\n                textureType,\r\n                tempName,\r\n                { maxSimultaneousLights: this.maxSimultaneousLights },\r\n                false,\r\n                textureFormat\r\n            );\r\n        } else {\r\n            postProcess.updateEffect(\r\n                defines.toString(),\r\n                this._fragmentCompilationState.uniforms,\r\n                this._fragmentCompilationState.samplers,\r\n                { maxSimultaneousLights: this.maxSimultaneousLights },\r\n                undefined,\r\n                undefined,\r\n                tempName,\r\n                tempName\r\n            );\r\n        }\r\n\r\n        postProcess.nodeMaterialSource = this;\r\n\r\n        postProcess.onApplyObservable.add((effect) => {\r\n            if (buildId !== this._buildId) {\r\n                delete Effect.ShadersStore[tempName + \"VertexShader\"];\r\n                delete Effect.ShadersStore[tempName + \"PixelShader\"];\r\n\r\n                tempName = this.name + this._buildId;\r\n\r\n                defines.markAllAsDirty();\r\n\r\n                buildId = this._buildId;\r\n            }\r\n\r\n            const result = this._processDefines(dummyMesh, defines);\r\n\r\n            if (result) {\r\n                Effect.RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, this._vertexCompilationState._builtCompilationString);\r\n\r\n                TimingTools.SetImmediate(() =>\r\n                    postProcess!.updateEffect(\r\n                        defines.toString(),\r\n                        this._fragmentCompilationState.uniforms,\r\n                        this._fragmentCompilationState.samplers,\r\n                        { maxSimultaneousLights: this.maxSimultaneousLights },\r\n                        undefined,\r\n                        undefined,\r\n                        tempName,\r\n                        tempName\r\n                    )\r\n                );\r\n            }\r\n\r\n            this._checkInternals(effect);\r\n        });\r\n\r\n        return postProcess;\r\n    }\r\n\r\n    /**\r\n     * Create a new procedural texture based on this node material\r\n     * @param size defines the size of the texture\r\n     * @param scene defines the hosting scene\r\n     * @returns the new procedural texture attached to this node material\r\n     */\r\n    public createProceduralTexture(size: number | { width: number; height: number; layers?: number }, scene: Scene): Nullable<ProceduralTexture> {\r\n        if (this.mode !== NodeMaterialModes.ProceduralTexture) {\r\n            Logger.Log(\"Incompatible material mode\");\r\n            return null;\r\n        }\r\n\r\n        let tempName = this.name + this._buildId;\r\n\r\n        const proceduralTexture = new ProceduralTexture(tempName, size, null, scene);\r\n\r\n        const dummyMesh = new AbstractMesh(tempName + \"Procedural\", this.getScene());\r\n        dummyMesh.reservedDataStore = {\r\n            hidden: true,\r\n        };\r\n\r\n        const defines = new NodeMaterialDefines();\r\n        const result = this._processDefines(dummyMesh, defines);\r\n        Effect.RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, this._vertexCompilationState._builtCompilationString);\r\n\r\n        let effect = this.getScene().getEngine().createEffect(\r\n            {\r\n                vertexElement: tempName,\r\n                fragmentElement: tempName,\r\n            },\r\n            [VertexBuffer.PositionKind],\r\n            this._fragmentCompilationState.uniforms,\r\n            this._fragmentCompilationState.samplers,\r\n            defines.toString(),\r\n            result?.fallbacks,\r\n            undefined\r\n        );\r\n\r\n        proceduralTexture.nodeMaterialSource = this;\r\n        proceduralTexture._setEffect(effect);\r\n\r\n        let buildId = this._buildId;\r\n        proceduralTexture.onBeforeGenerationObservable.add(() => {\r\n            if (buildId !== this._buildId) {\r\n                delete Effect.ShadersStore[tempName + \"VertexShader\"];\r\n                delete Effect.ShadersStore[tempName + \"PixelShader\"];\r\n\r\n                tempName = this.name + this._buildId;\r\n\r\n                defines.markAllAsDirty();\r\n\r\n                buildId = this._buildId;\r\n            }\r\n\r\n            const result = this._processDefines(dummyMesh, defines);\r\n\r\n            if (result) {\r\n                Effect.RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, this._vertexCompilationState._builtCompilationString);\r\n\r\n                TimingTools.SetImmediate(() => {\r\n                    effect = this.getScene().getEngine().createEffect(\r\n                        {\r\n                            vertexElement: tempName,\r\n                            fragmentElement: tempName,\r\n                        },\r\n                        [VertexBuffer.PositionKind],\r\n                        this._fragmentCompilationState.uniforms,\r\n                        this._fragmentCompilationState.samplers,\r\n                        defines.toString(),\r\n                        result?.fallbacks,\r\n                        undefined\r\n                    );\r\n\r\n                    proceduralTexture._setEffect(effect);\r\n                });\r\n            }\r\n\r\n            this._checkInternals(effect);\r\n        });\r\n\r\n        return proceduralTexture;\r\n    }\r\n\r\n    private _createEffectForParticles(\r\n        particleSystem: IParticleSystem,\r\n        blendMode: number,\r\n        onCompiled?: (effect: Effect) => void,\r\n        onError?: (effect: Effect, errors: string) => void,\r\n        effect?: Effect,\r\n        defines?: NodeMaterialDefines,\r\n        dummyMesh?: Nullable<AbstractMesh>,\r\n        particleSystemDefinesJoined = \"\"\r\n    ) {\r\n        let tempName = this.name + this._buildId + \"_\" + blendMode;\r\n\r\n        if (!defines) {\r\n            defines = new NodeMaterialDefines();\r\n        }\r\n\r\n        if (!dummyMesh) {\r\n            dummyMesh = this.getScene().getMeshByName(this.name + \"Particle\");\r\n            if (!dummyMesh) {\r\n                dummyMesh = new AbstractMesh(this.name + \"Particle\", this.getScene());\r\n                dummyMesh.reservedDataStore = {\r\n                    hidden: true,\r\n                };\r\n            }\r\n        }\r\n\r\n        let buildId = this._buildId;\r\n\r\n        const particleSystemDefines: Array<string> = [];\r\n        let join = particleSystemDefinesJoined;\r\n\r\n        if (!effect) {\r\n            const result = this._processDefines(dummyMesh, defines);\r\n\r\n            Effect.RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString);\r\n\r\n            particleSystem.fillDefines(particleSystemDefines, blendMode);\r\n\r\n            join = particleSystemDefines.join(\"\\n\");\r\n\r\n            effect = this.getScene()\r\n                .getEngine()\r\n                .createEffectForParticles(\r\n                    tempName,\r\n                    this._fragmentCompilationState.uniforms,\r\n                    this._fragmentCompilationState.samplers,\r\n                    defines.toString() + \"\\n\" + join,\r\n                    result?.fallbacks,\r\n                    onCompiled,\r\n                    onError,\r\n                    particleSystem\r\n                );\r\n\r\n            particleSystem.setCustomEffect(effect, blendMode);\r\n        }\r\n\r\n        effect.onBindObservable.add((effect) => {\r\n            if (buildId !== this._buildId) {\r\n                delete Effect.ShadersStore[tempName + \"PixelShader\"];\r\n\r\n                tempName = this.name + this._buildId + \"_\" + blendMode;\r\n\r\n                defines!.markAllAsDirty();\r\n\r\n                buildId = this._buildId;\r\n            }\r\n\r\n            particleSystemDefines.length = 0;\r\n\r\n            particleSystem.fillDefines(particleSystemDefines, blendMode);\r\n\r\n            const particleSystemDefinesJoinedCurrent = particleSystemDefines.join(\"\\n\");\r\n\r\n            if (particleSystemDefinesJoinedCurrent !== join) {\r\n                defines!.markAllAsDirty();\r\n                join = particleSystemDefinesJoinedCurrent;\r\n            }\r\n\r\n            const result = this._processDefines(dummyMesh!, defines!);\r\n\r\n            if (result) {\r\n                Effect.RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString);\r\n\r\n                effect = this.getScene()\r\n                    .getEngine()\r\n                    .createEffectForParticles(\r\n                        tempName,\r\n                        this._fragmentCompilationState.uniforms,\r\n                        this._fragmentCompilationState.samplers,\r\n                        defines!.toString() + \"\\n\" + join,\r\n                        result?.fallbacks,\r\n                        onCompiled,\r\n                        onError,\r\n                        particleSystem\r\n                    );\r\n                particleSystem.setCustomEffect(effect, blendMode);\r\n                this._createEffectForParticles(particleSystem, blendMode, onCompiled, onError, effect, defines, dummyMesh, particleSystemDefinesJoined); // add the effect.onBindObservable observer\r\n                return;\r\n            }\r\n\r\n            this._checkInternals(effect);\r\n        });\r\n    }\r\n\r\n    private _checkInternals(effect: Effect) {\r\n        // Animated blocks\r\n        if (this._sharedData.animatedInputs) {\r\n            const scene = this.getScene();\r\n\r\n            const frameId = scene.getFrameId();\r\n\r\n            if (this._animationFrame !== frameId) {\r\n                for (const input of this._sharedData.animatedInputs) {\r\n                    input.animate(scene);\r\n                }\r\n\r\n                this._animationFrame = frameId;\r\n            }\r\n        }\r\n\r\n        // Bindable blocks\r\n        for (const block of this._sharedData.bindableBlocks) {\r\n            block.bind(effect, this);\r\n        }\r\n\r\n        // Connection points\r\n        for (const inputBlock of this._sharedData.inputBlocks) {\r\n            inputBlock._transmit(effect, this.getScene(), this);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create the effect to be used as the custom effect for a particle system\r\n     * @param particleSystem Particle system to create the effect for\r\n     * @param onCompiled defines a function to call when the effect creation is successful\r\n     * @param onError defines a function to call when the effect creation has failed\r\n     */\r\n    public createEffectForParticles(particleSystem: IParticleSystem, onCompiled?: (effect: Effect) => void, onError?: (effect: Effect, errors: string) => void) {\r\n        if (this.mode !== NodeMaterialModes.Particle) {\r\n            Logger.Log(\"Incompatible material mode\");\r\n            return;\r\n        }\r\n\r\n        this._createEffectForParticles(particleSystem, BaseParticleSystem.BLENDMODE_ONEONE, onCompiled, onError);\r\n        this._createEffectForParticles(particleSystem, BaseParticleSystem.BLENDMODE_MULTIPLY, onCompiled, onError);\r\n    }\r\n\r\n    /**\r\n     * Use this material as the shadow depth wrapper of a target material\r\n     * @param targetMaterial defines the target material\r\n     */\r\n    public createAsShadowDepthWrapper(targetMaterial: Material) {\r\n        if (this.mode !== NodeMaterialModes.Material) {\r\n            Logger.Log(\"Incompatible material mode\");\r\n            return;\r\n        }\r\n\r\n        targetMaterial.shadowDepthWrapper = new BABYLON.ShadowDepthWrapper(this, this.getScene());\r\n    }\r\n\r\n    private _processDefines(\r\n        mesh: AbstractMesh,\r\n        defines: NodeMaterialDefines,\r\n        useInstances = false,\r\n        subMesh?: SubMesh\r\n    ): Nullable<{\r\n        lightDisposed: boolean;\r\n        uniformBuffers: string[];\r\n        mergedUniforms: string[];\r\n        mergedSamplers: string[];\r\n        fallbacks: EffectFallbacks;\r\n    }> {\r\n        let result = null;\r\n\r\n        // Global defines\r\n        const scene = this.getScene();\r\n        if (PrepareDefinesForCamera(scene, defines)) {\r\n            defines.markAsMiscDirty();\r\n        }\r\n\r\n        // Shared defines\r\n        this._sharedData.blocksWithDefines.forEach((b) => {\r\n            b.initializeDefines(mesh, this, defines, useInstances);\r\n        });\r\n\r\n        this._sharedData.blocksWithDefines.forEach((b) => {\r\n            b.prepareDefines(mesh, this, defines, useInstances, subMesh);\r\n        });\r\n\r\n        // Need to recompile?\r\n        if (defines.isDirty) {\r\n            const lightDisposed = defines._areLightsDisposed;\r\n            defines.markAsProcessed();\r\n\r\n            // Repeatable content generators\r\n            this._vertexCompilationState.compilationString = this._vertexCompilationState._builtCompilationString;\r\n            this._fragmentCompilationState.compilationString = this._fragmentCompilationState._builtCompilationString;\r\n\r\n            this._sharedData.repeatableContentBlocks.forEach((b) => {\r\n                b.replaceRepeatableContent(this._vertexCompilationState, this._fragmentCompilationState, mesh, defines);\r\n            });\r\n\r\n            // Uniforms\r\n            const uniformBuffers: string[] = [];\r\n            this._sharedData.dynamicUniformBlocks.forEach((b) => {\r\n                b.updateUniformsAndSamples(this._vertexCompilationState, this, defines, uniformBuffers);\r\n            });\r\n\r\n            const mergedUniforms = this._vertexCompilationState.uniforms;\r\n\r\n            this._fragmentCompilationState.uniforms.forEach((u) => {\r\n                const index = mergedUniforms.indexOf(u);\r\n\r\n                if (index === -1) {\r\n                    mergedUniforms.push(u);\r\n                }\r\n            });\r\n\r\n            // Samplers\r\n            const mergedSamplers = this._vertexCompilationState.samplers;\r\n\r\n            this._fragmentCompilationState.samplers.forEach((s) => {\r\n                const index = mergedSamplers.indexOf(s);\r\n\r\n                if (index === -1) {\r\n                    mergedSamplers.push(s);\r\n                }\r\n            });\r\n\r\n            const fallbacks = new EffectFallbacks();\r\n\r\n            this._sharedData.blocksWithFallbacks.forEach((b) => {\r\n                b.provideFallbacks(mesh, fallbacks);\r\n            });\r\n\r\n            result = {\r\n                lightDisposed,\r\n                uniformBuffers,\r\n                mergedUniforms,\r\n                mergedSamplers,\r\n                fallbacks,\r\n            };\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Get if the submesh is ready to be used and all its information available.\r\n     * Child classes can use it to update shaders\r\n     * @param mesh defines the mesh to check\r\n     * @param subMesh defines which submesh to check\r\n     * @param useInstances specifies that instances should be used\r\n     * @returns a boolean indicating that the submesh is ready or not\r\n     */\r\n    public isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances: boolean = false): boolean {\r\n        if (!this._buildWasSuccessful) {\r\n            return false;\r\n        }\r\n\r\n        const scene = this.getScene();\r\n        if (this._sharedData.animatedInputs) {\r\n            const frameId = scene.getFrameId();\r\n\r\n            if (this._animationFrame !== frameId) {\r\n                for (const input of this._sharedData.animatedInputs) {\r\n                    input.animate(scene);\r\n                }\r\n\r\n                this._animationFrame = frameId;\r\n            }\r\n        }\r\n\r\n        const drawWrapper = subMesh._drawWrapper;\r\n\r\n        if (drawWrapper.effect && this.isFrozen) {\r\n            if (drawWrapper._wasPreviouslyReady && drawWrapper._wasPreviouslyUsingInstances === useInstances) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!subMesh.materialDefines) {\r\n            subMesh.materialDefines = new NodeMaterialDefines();\r\n        }\r\n\r\n        const defines = <NodeMaterialDefines>subMesh.materialDefines;\r\n        if (this._isReadyForSubMesh(subMesh)) {\r\n            return true;\r\n        }\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        this._prepareDefinesForAttributes(mesh, defines);\r\n\r\n        // Check if blocks are ready\r\n        if (this._sharedData.blockingBlocks.some((b) => !b.isReady(mesh, this, defines, useInstances))) {\r\n            return false;\r\n        }\r\n\r\n        const result = this._processDefines(mesh, defines, useInstances, subMesh);\r\n\r\n        if (result) {\r\n            const previousEffect = subMesh.effect;\r\n            // Compilation\r\n            const join = defines.toString();\r\n            let effect = engine.createEffect(\r\n                {\r\n                    vertex: \"nodeMaterial\" + this._buildId,\r\n                    fragment: \"nodeMaterial\" + this._buildId,\r\n                    vertexSource: this._vertexCompilationState.compilationString,\r\n                    fragmentSource: this._fragmentCompilationState.compilationString,\r\n                },\r\n                <IEffectCreationOptions>{\r\n                    attributes: this._vertexCompilationState.attributes,\r\n                    uniformsNames: result.mergedUniforms,\r\n                    uniformBuffersNames: result.uniformBuffers,\r\n                    samplers: result.mergedSamplers,\r\n                    defines: join,\r\n                    fallbacks: result.fallbacks,\r\n                    onCompiled: this.onCompiled,\r\n                    onError: this.onError,\r\n                    multiTarget: defines.PREPASS,\r\n                    indexParameters: { maxSimultaneousLights: this.maxSimultaneousLights, maxSimultaneousMorphTargets: defines.NUM_MORPH_INFLUENCERS },\r\n                },\r\n                engine\r\n            );\r\n\r\n            if (effect) {\r\n                if (this._onEffectCreatedObservable) {\r\n                    onCreatedEffectParameters.effect = effect;\r\n                    onCreatedEffectParameters.subMesh = subMesh;\r\n                    this._onEffectCreatedObservable.notifyObservers(onCreatedEffectParameters);\r\n                }\r\n\r\n                // Use previous effect while new one is compiling\r\n                if (this.allowShaderHotSwapping && previousEffect && !effect.isReady()) {\r\n                    effect = previousEffect;\r\n                    defines.markAsUnprocessed();\r\n\r\n                    if (result.lightDisposed) {\r\n                        // re register in case it takes more than one frame.\r\n                        defines._areLightsDisposed = true;\r\n                        return false;\r\n                    }\r\n                } else {\r\n                    scene.resetCachedMaterial();\r\n                    subMesh.setEffect(effect, defines, this._materialContext);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        defines._renderId = scene.getRenderId();\r\n        drawWrapper._wasPreviouslyReady = true;\r\n        drawWrapper._wasPreviouslyUsingInstances = useInstances;\r\n\r\n        this._checkScenePerformancePriority();\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Get a string representing the shaders built by the current node graph\r\n     */\r\n    public get compiledShaders() {\r\n        return `// Vertex shader\\n${this._vertexCompilationState.compilationString}\\n\\n// Fragment shader\\n${this._fragmentCompilationState.compilationString}`;\r\n    }\r\n\r\n    /**\r\n     * Binds the world matrix to the material\r\n     * @param world defines the world transformation matrix\r\n     */\r\n    public bindOnlyWorldMatrix(world: Matrix): void {\r\n        const scene = this.getScene();\r\n\r\n        if (!this._activeEffect) {\r\n            return;\r\n        }\r\n\r\n        const hints = this._sharedData.hints;\r\n\r\n        if (hints.needWorldViewMatrix) {\r\n            world.multiplyToRef(scene.getViewMatrix(), this._cachedWorldViewMatrix);\r\n        }\r\n\r\n        if (hints.needWorldViewProjectionMatrix) {\r\n            world.multiplyToRef(scene.getTransformMatrix(), this._cachedWorldViewProjectionMatrix);\r\n        }\r\n\r\n        // Connection points\r\n        for (const inputBlock of this._sharedData.inputBlocks) {\r\n            inputBlock._transmitWorld(this._activeEffect, world, this._cachedWorldViewMatrix, this._cachedWorldViewProjectionMatrix);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Binds the submesh to this material by preparing the effect and shader to draw\r\n     * @param world defines the world transformation matrix\r\n     * @param mesh defines the mesh containing the submesh\r\n     * @param subMesh defines the submesh to bind the material to\r\n     */\r\n    public bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void {\r\n        const scene = this.getScene();\r\n        const effect = subMesh.effect;\r\n        if (!effect) {\r\n            return;\r\n        }\r\n        this._activeEffect = effect;\r\n\r\n        // Matrices\r\n        this.bindOnlyWorldMatrix(world);\r\n\r\n        const mustRebind = this._mustRebind(scene, effect, subMesh, mesh.visibility);\r\n        const sharedData = this._sharedData;\r\n\r\n        if (mustRebind) {\r\n            // Bindable blocks\r\n            for (const block of sharedData.bindableBlocks) {\r\n                block.bind(effect, this, mesh, subMesh);\r\n            }\r\n\r\n            for (const block of sharedData.forcedBindableBlocks) {\r\n                block.bind(effect, this, mesh, subMesh);\r\n            }\r\n\r\n            // Connection points\r\n            for (const inputBlock of sharedData.inputBlocks) {\r\n                inputBlock._transmit(effect, scene, this);\r\n            }\r\n        } else if (!this.isFrozen) {\r\n            for (const block of sharedData.forcedBindableBlocks) {\r\n                block.bind(effect, this, mesh, subMesh);\r\n            }\r\n        }\r\n\r\n        this._afterBind(mesh, this._activeEffect, subMesh);\r\n    }\r\n\r\n    /**\r\n     * Gets the active textures from the material\r\n     * @returns an array of textures\r\n     */\r\n    public getActiveTextures(): BaseTexture[] {\r\n        const activeTextures = super.getActiveTextures();\r\n\r\n        if (this._sharedData) {\r\n            activeTextures.push(...this._sharedData.textureBlocks.filter((tb) => tb.texture).map((tb) => tb.texture!));\r\n        }\r\n\r\n        return activeTextures;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of texture blocks\r\n     * Note that this method will only return blocks that are reachable from the final block(s) and only after the material has been built!\r\n     * @returns an array of texture blocks\r\n     */\r\n    public getTextureBlocks(): NodeMaterialTextureBlocks[] {\r\n        if (!this._sharedData) {\r\n            return [];\r\n        }\r\n\r\n        return this._sharedData.textureBlocks;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of all texture blocks\r\n     * Note that this method will scan all attachedBlocks and return blocks that are texture blocks\r\n     * @returns\r\n     */\r\n    public getAllTextureBlocks(): NodeMaterialTextureBlocks[] {\r\n        const textureBlocks: NodeMaterialTextureBlocks[] = [];\r\n\r\n        for (const block of this.attachedBlocks) {\r\n            if (NodeMaterial._BlockIsTextureBlock(block)) {\r\n                textureBlocks.push(block);\r\n            }\r\n        }\r\n\r\n        return textureBlocks;\r\n    }\r\n\r\n    /**\r\n     * Specifies if the material uses a texture\r\n     * @param texture defines the texture to check against the material\r\n     * @returns a boolean specifying if the material uses the texture\r\n     */\r\n    public hasTexture(texture: BaseTexture): boolean {\r\n        if (super.hasTexture(texture)) {\r\n            return true;\r\n        }\r\n\r\n        if (!this._sharedData) {\r\n            return false;\r\n        }\r\n\r\n        for (const t of this._sharedData.textureBlocks) {\r\n            if (t.texture === texture) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Disposes the material\r\n     * @param forceDisposeEffect specifies if effects should be forcefully disposed\r\n     * @param forceDisposeTextures specifies if textures should be forcefully disposed\r\n     * @param notBoundToMesh specifies if the material that is being disposed is known to be not bound to any mesh\r\n     */\r\n    public dispose(forceDisposeEffect?: boolean, forceDisposeTextures?: boolean, notBoundToMesh?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            for (const texture of this.getTextureBlocks()\r\n                .filter((tb) => tb.texture)\r\n                .map((tb) => tb.texture!)) {\r\n                texture.dispose();\r\n            }\r\n        }\r\n\r\n        for (const block of this.attachedBlocks) {\r\n            block.dispose();\r\n        }\r\n\r\n        this.attachedBlocks.length = 0;\r\n        (this._sharedData as any) = null;\r\n        (this._vertexCompilationState as any) = null;\r\n        (this._fragmentCompilationState as any) = null;\r\n\r\n        this.onBuildObservable.clear();\r\n\r\n        if (this._imageProcessingObserver) {\r\n            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);\r\n            this._imageProcessingObserver = null;\r\n        }\r\n\r\n        super.dispose(forceDisposeEffect, forceDisposeTextures, notBoundToMesh);\r\n    }\r\n\r\n    /** Creates the node editor window.\r\n     * @param additionalConfig Define the configuration of the editor\r\n     */\r\n    private _createNodeEditor(additionalConfig?: any) {\r\n        const nodeEditorConfig: any = {\r\n            nodeMaterial: this,\r\n            ...additionalConfig,\r\n        };\r\n        this.BJSNODEMATERIALEDITOR.NodeEditor.Show(nodeEditorConfig);\r\n    }\r\n\r\n    /**\r\n     * Launch the node material editor\r\n     * @param config Define the configuration of the editor\r\n     * @returns a promise fulfilled when the node editor is visible\r\n     */\r\n    public edit(config?: INodeMaterialEditorOptions): Promise<void> {\r\n        return new Promise((resolve) => {\r\n            this.BJSNODEMATERIALEDITOR = this.BJSNODEMATERIALEDITOR || this._getGlobalNodeMaterialEditor();\r\n            if (typeof this.BJSNODEMATERIALEDITOR == \"undefined\") {\r\n                const editorUrl = config && config.editorURL ? config.editorURL : NodeMaterial.EditorURL;\r\n\r\n                // Load editor and add it to the DOM\r\n                Tools.LoadBabylonScript(editorUrl, () => {\r\n                    this.BJSNODEMATERIALEDITOR = this.BJSNODEMATERIALEDITOR || this._getGlobalNodeMaterialEditor();\r\n                    this._createNodeEditor(config?.nodeEditorConfig);\r\n                    resolve();\r\n                });\r\n            } else {\r\n                // Otherwise creates the editor\r\n                this._createNodeEditor(config?.nodeEditorConfig);\r\n                resolve();\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Clear the current material\r\n     */\r\n    public clear() {\r\n        this._vertexOutputNodes.length = 0;\r\n        this._fragmentOutputNodes.length = 0;\r\n        this.attachedBlocks.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Clear the current material and set it to a default state\r\n     */\r\n    public setToDefault() {\r\n        this.clear();\r\n\r\n        this.editorData = null;\r\n\r\n        const positionInput = new InputBlock(\"Position\");\r\n        positionInput.setAsAttribute(\"position\");\r\n\r\n        const worldInput = new InputBlock(\"World\");\r\n        worldInput.setAsSystemValue(NodeMaterialSystemValues.World);\r\n\r\n        const worldPos = new TransformBlock(\"WorldPos\");\r\n        positionInput.connectTo(worldPos);\r\n        worldInput.connectTo(worldPos);\r\n\r\n        const viewProjectionInput = new InputBlock(\"ViewProjection\");\r\n        viewProjectionInput.setAsSystemValue(NodeMaterialSystemValues.ViewProjection);\r\n\r\n        const worldPosdMultipliedByViewProjection = new TransformBlock(\"WorldPos * ViewProjectionTransform\");\r\n        worldPos.connectTo(worldPosdMultipliedByViewProjection);\r\n        viewProjectionInput.connectTo(worldPosdMultipliedByViewProjection);\r\n\r\n        const vertexOutput = new VertexOutputBlock(\"VertexOutput\");\r\n        worldPosdMultipliedByViewProjection.connectTo(vertexOutput);\r\n\r\n        // Pixel\r\n        const pixelColor = new InputBlock(\"color\");\r\n        pixelColor.value = new Color4(0.8, 0.8, 0.8, 1);\r\n\r\n        const fragmentOutput = new FragmentOutputBlock(\"FragmentOutput\");\r\n        pixelColor.connectTo(fragmentOutput);\r\n\r\n        // Add to nodes\r\n        this.addOutputNode(vertexOutput);\r\n        this.addOutputNode(fragmentOutput);\r\n\r\n        this._mode = NodeMaterialModes.Material;\r\n    }\r\n\r\n    /**\r\n     * Clear the current material and set it to a default state for post process\r\n     */\r\n    public setToDefaultPostProcess() {\r\n        this.clear();\r\n\r\n        this.editorData = null;\r\n\r\n        const position = new InputBlock(\"Position\");\r\n        position.setAsAttribute(\"position2d\");\r\n\r\n        const const1 = new InputBlock(\"Constant1\");\r\n        const1.isConstant = true;\r\n        const1.value = 1;\r\n\r\n        const vmerger = new VectorMergerBlock(\"Position3D\");\r\n\r\n        position.connectTo(vmerger);\r\n        const1.connectTo(vmerger, { input: \"w\" });\r\n\r\n        const vertexOutput = new VertexOutputBlock(\"VertexOutput\");\r\n        vmerger.connectTo(vertexOutput);\r\n\r\n        // Pixel\r\n        const scale = new InputBlock(\"Scale\");\r\n        scale.visibleInInspector = true;\r\n        scale.value = new Vector2(1, 1);\r\n\r\n        const uv0 = new RemapBlock(\"uv0\");\r\n        position.connectTo(uv0);\r\n\r\n        const uv = new MultiplyBlock(\"UV scale\");\r\n        uv0.connectTo(uv);\r\n        scale.connectTo(uv);\r\n\r\n        const currentScreen = new CurrentScreenBlock(\"CurrentScreen\");\r\n        uv.connectTo(currentScreen);\r\n\r\n        currentScreen.texture = new Texture(\"https://assets.babylonjs.com/nme/currentScreenPostProcess.png\", this.getScene());\r\n\r\n        const fragmentOutput = new FragmentOutputBlock(\"FragmentOutput\");\r\n        currentScreen.connectTo(fragmentOutput, { output: \"rgba\" });\r\n\r\n        // Add to nodes\r\n        this.addOutputNode(vertexOutput);\r\n        this.addOutputNode(fragmentOutput);\r\n\r\n        this._mode = NodeMaterialModes.PostProcess;\r\n    }\r\n\r\n    /**\r\n     * Clear the current material and set it to a default state for procedural texture\r\n     */\r\n    public setToDefaultProceduralTexture() {\r\n        this.clear();\r\n\r\n        this.editorData = null;\r\n\r\n        const position = new InputBlock(\"Position\");\r\n        position.setAsAttribute(\"position2d\");\r\n\r\n        const const1 = new InputBlock(\"Constant1\");\r\n        const1.isConstant = true;\r\n        const1.value = 1;\r\n\r\n        const vmerger = new VectorMergerBlock(\"Position3D\");\r\n\r\n        position.connectTo(vmerger);\r\n        const1.connectTo(vmerger, { input: \"w\" });\r\n\r\n        const vertexOutput = new VertexOutputBlock(\"VertexOutput\");\r\n        vmerger.connectTo(vertexOutput);\r\n\r\n        // Pixel\r\n        const time = new InputBlock(\"Time\");\r\n        time.value = 0;\r\n        time.min = 0;\r\n        time.max = 0;\r\n        time.isBoolean = false;\r\n        time.matrixMode = 0;\r\n        time.animationType = AnimatedInputBlockTypes.Time;\r\n        time.isConstant = false;\r\n\r\n        const color = new InputBlock(\"Color3\");\r\n        color.value = new Color3(1, 1, 1);\r\n        color.isConstant = false;\r\n        const fragmentOutput = new FragmentOutputBlock(\"FragmentOutput\");\r\n\r\n        const vectorMerger = new VectorMergerBlock(\"VectorMerger\");\r\n        vectorMerger.visibleInInspector = false;\r\n\r\n        const cos = new TrigonometryBlock(\"Cos\");\r\n        cos.operation = TrigonometryBlockOperations.Cos;\r\n\r\n        position.connectTo(vectorMerger);\r\n        time.output.connectTo(cos.input);\r\n        cos.output.connectTo(vectorMerger.z);\r\n        vectorMerger.xyzOut.connectTo(fragmentOutput.rgb);\r\n\r\n        // Add to nodes\r\n        this.addOutputNode(vertexOutput);\r\n        this.addOutputNode(fragmentOutput);\r\n\r\n        this._mode = NodeMaterialModes.ProceduralTexture;\r\n    }\r\n\r\n    /**\r\n     * Clear the current material and set it to a default state for particle\r\n     */\r\n    public setToDefaultParticle() {\r\n        this.clear();\r\n\r\n        this.editorData = null;\r\n\r\n        // Pixel\r\n        const uv = new InputBlock(\"uv\");\r\n        uv.setAsAttribute(\"particle_uv\");\r\n\r\n        const texture = new ParticleTextureBlock(\"ParticleTexture\");\r\n        uv.connectTo(texture);\r\n\r\n        const color = new InputBlock(\"Color\");\r\n        color.setAsAttribute(\"particle_color\");\r\n\r\n        const multiply = new MultiplyBlock(\"Texture * Color\");\r\n        texture.connectTo(multiply);\r\n        color.connectTo(multiply);\r\n\r\n        const rampGradient = new ParticleRampGradientBlock(\"ParticleRampGradient\");\r\n        multiply.connectTo(rampGradient);\r\n\r\n        const cSplitter = new ColorSplitterBlock(\"ColorSplitter\");\r\n        color.connectTo(cSplitter);\r\n\r\n        const blendMultiply = new ParticleBlendMultiplyBlock(\"ParticleBlendMultiply\");\r\n        rampGradient.connectTo(blendMultiply);\r\n        texture.connectTo(blendMultiply, { output: \"a\" });\r\n        cSplitter.connectTo(blendMultiply, { output: \"a\" });\r\n\r\n        const fragmentOutput = new FragmentOutputBlock(\"FragmentOutput\");\r\n        blendMultiply.connectTo(fragmentOutput);\r\n\r\n        // Add to nodes\r\n        this.addOutputNode(fragmentOutput);\r\n\r\n        this._mode = NodeMaterialModes.Particle;\r\n    }\r\n\r\n    /**\r\n     * Loads the current Node Material from a url pointing to a file save by the Node Material Editor\r\n     * @deprecated Please use NodeMaterial.ParseFromFileAsync instead\r\n     * @param url defines the url to load from\r\n     * @param rootUrl defines the root URL for nested url in the node material\r\n     * @returns a promise that will fulfil when the material is fully loaded\r\n     */\r\n    public async loadAsync(url: string, rootUrl: string = \"\") {\r\n        return NodeMaterial.ParseFromFileAsync(\"\", url, this.getScene(), rootUrl, true, this);\r\n    }\r\n\r\n    private _gatherBlocks(rootNode: NodeMaterialBlock, list: NodeMaterialBlock[]) {\r\n        if (list.indexOf(rootNode) !== -1) {\r\n            return;\r\n        }\r\n        list.push(rootNode);\r\n\r\n        for (const input of rootNode.inputs) {\r\n            const connectedPoint = input.connectedPoint;\r\n            if (connectedPoint) {\r\n                const block = connectedPoint.ownerBlock;\r\n                if (block !== rootNode) {\r\n                    this._gatherBlocks(block, list);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Teleportation\r\n        if (rootNode.isTeleportOut) {\r\n            const block = rootNode as NodeMaterialTeleportOutBlock;\r\n            if (block.entryPoint) {\r\n                this._gatherBlocks(block.entryPoint, list);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Generate a string containing the code declaration required to create an equivalent of this material\r\n     * @returns a string\r\n     */\r\n    public generateCode() {\r\n        let alreadyDumped: NodeMaterialBlock[] = [];\r\n        const vertexBlocks: NodeMaterialBlock[] = [];\r\n        const uniqueNames: string[] = [\"const\", \"var\", \"let\"];\r\n        // Gets active blocks\r\n        for (const outputNode of this._vertexOutputNodes) {\r\n            this._gatherBlocks(outputNode, vertexBlocks);\r\n        }\r\n\r\n        const fragmentBlocks: NodeMaterialBlock[] = [];\r\n        for (const outputNode of this._fragmentOutputNodes) {\r\n            this._gatherBlocks(outputNode, fragmentBlocks);\r\n        }\r\n\r\n        // Generate vertex shader\r\n        let codeString = `var nodeMaterial = new BABYLON.NodeMaterial(\"${this.name || \"node material\"}\");\\n`;\r\n        codeString += `nodeMaterial.mode = BABYLON.NodeMaterialModes.${NodeMaterialModes[this.mode]};\\n`;\r\n        for (const node of vertexBlocks) {\r\n            if (node.isInput && alreadyDumped.indexOf(node) === -1) {\r\n                codeString += node._dumpCode(uniqueNames, alreadyDumped);\r\n            }\r\n        }\r\n\r\n        // Generate fragment shader\r\n        for (const node of fragmentBlocks) {\r\n            if (node.isInput && alreadyDumped.indexOf(node) === -1) {\r\n                codeString += node._dumpCode(uniqueNames, alreadyDumped);\r\n            }\r\n        }\r\n\r\n        // Connections\r\n        alreadyDumped = [];\r\n        codeString += \"\\n// Connections\\n\";\r\n        for (const node of this._vertexOutputNodes) {\r\n            codeString += node._dumpCodeForOutputConnections(alreadyDumped);\r\n        }\r\n        for (const node of this._fragmentOutputNodes) {\r\n            codeString += node._dumpCodeForOutputConnections(alreadyDumped);\r\n        }\r\n\r\n        // Output nodes\r\n        codeString += \"\\n// Output nodes\\n\";\r\n        for (const node of this._vertexOutputNodes) {\r\n            codeString += `nodeMaterial.addOutputNode(${node._codeVariableName});\\n`;\r\n        }\r\n\r\n        for (const node of this._fragmentOutputNodes) {\r\n            codeString += `nodeMaterial.addOutputNode(${node._codeVariableName});\\n`;\r\n        }\r\n\r\n        codeString += `nodeMaterial.build();\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Serializes this material in a JSON representation\r\n     * @param selectedBlocks defines an optional list of blocks to serialize\r\n     * @returns the serialized material object\r\n     */\r\n    public serialize(selectedBlocks?: NodeMaterialBlock[]): any {\r\n        const serializationObject = selectedBlocks ? {} : SerializationHelper.Serialize(this);\r\n        serializationObject.editorData = JSON.parse(JSON.stringify(this.editorData)); // Copy\r\n\r\n        let blocks: NodeMaterialBlock[] = [];\r\n\r\n        if (selectedBlocks) {\r\n            blocks = selectedBlocks;\r\n        } else {\r\n            serializationObject.customType = \"BABYLON.NodeMaterial\";\r\n            serializationObject.outputNodes = [];\r\n\r\n            // Outputs\r\n            for (const outputNode of this._vertexOutputNodes) {\r\n                this._gatherBlocks(outputNode, blocks);\r\n                serializationObject.outputNodes.push(outputNode.uniqueId);\r\n            }\r\n\r\n            for (const outputNode of this._fragmentOutputNodes) {\r\n                this._gatherBlocks(outputNode, blocks);\r\n\r\n                if (serializationObject.outputNodes.indexOf(outputNode.uniqueId) === -1) {\r\n                    serializationObject.outputNodes.push(outputNode.uniqueId);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Blocks\r\n        serializationObject.blocks = [];\r\n\r\n        for (const block of blocks) {\r\n            serializationObject.blocks.push(block.serialize());\r\n        }\r\n\r\n        if (!selectedBlocks) {\r\n            for (const block of this.attachedBlocks) {\r\n                if (blocks.indexOf(block) !== -1) {\r\n                    continue;\r\n                }\r\n                serializationObject.blocks.push(block.serialize());\r\n            }\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    private _restoreConnections(block: NodeMaterialBlock, source: any, map: { [key: number]: NodeMaterialBlock }) {\r\n        for (const outputPoint of block.outputs) {\r\n            for (const candidate of source.blocks) {\r\n                const target = map[candidate.id];\r\n\r\n                if (!target) {\r\n                    continue;\r\n                }\r\n\r\n                for (const input of candidate.inputs) {\r\n                    if (map[input.targetBlockId] === block && input.targetConnectionName === outputPoint.name) {\r\n                        const inputPoint = target.getInputByName(input.inputName);\r\n                        if (!inputPoint || inputPoint.isConnected) {\r\n                            continue;\r\n                        }\r\n\r\n                        outputPoint.connectTo(inputPoint, true);\r\n                        this._restoreConnections(target, source, map);\r\n                        continue;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clear the current graph and load a new one from a serialization object\r\n     * @param source defines the JSON representation of the material\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @param merge defines whether or not the source must be merged or replace the current content\r\n     */\r\n    public parseSerializedObject(source: any, rootUrl: string = \"\", merge = false) {\r\n        if (!merge) {\r\n            this.clear();\r\n        }\r\n\r\n        const map: { [key: number]: NodeMaterialBlock } = {};\r\n\r\n        // Create blocks\r\n        for (const parsedBlock of source.blocks) {\r\n            const blockType = GetClass(parsedBlock.customType);\r\n            if (blockType) {\r\n                const block: NodeMaterialBlock = new blockType();\r\n                block._deserialize(parsedBlock, this.getScene(), rootUrl);\r\n                map[parsedBlock.id] = block;\r\n\r\n                this.attachedBlocks.push(block);\r\n            }\r\n        }\r\n\r\n        // Reconnect teleportation\r\n        for (const block of this.attachedBlocks) {\r\n            if (block.isTeleportOut) {\r\n                const teleportOut = block as NodeMaterialTeleportOutBlock;\r\n                const id = teleportOut._tempEntryPointUniqueId;\r\n                if (id) {\r\n                    const source = map[id] as NodeMaterialTeleportInBlock;\r\n                    source.attachToEndpoint(teleportOut);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Connections - Starts with input blocks only (except if in \"merge\" mode where we scan all blocks)\r\n        for (let blockIndex = 0; blockIndex < source.blocks.length; blockIndex++) {\r\n            const parsedBlock = source.blocks[blockIndex];\r\n            const block = map[parsedBlock.id];\r\n\r\n            if (!block) {\r\n                continue;\r\n            }\r\n\r\n            if (block.inputs.length && !merge) {\r\n                continue;\r\n            }\r\n            this._restoreConnections(block, source, map);\r\n        }\r\n\r\n        // Outputs\r\n        if (source.outputNodes) {\r\n            for (const outputNodeId of source.outputNodes) {\r\n                this.addOutputNode(map[outputNodeId]);\r\n            }\r\n        }\r\n\r\n        // UI related info\r\n        if (source.locations || (source.editorData && source.editorData.locations)) {\r\n            const locations: {\r\n                blockId: number;\r\n                x: number;\r\n                y: number;\r\n            }[] = source.locations || source.editorData.locations;\r\n\r\n            for (const location of locations) {\r\n                if (map[location.blockId]) {\r\n                    location.blockId = map[location.blockId].uniqueId;\r\n                }\r\n            }\r\n\r\n            if (merge && this.editorData && this.editorData.locations) {\r\n                locations.concat(this.editorData.locations);\r\n            }\r\n\r\n            if (source.locations) {\r\n                this.editorData = {\r\n                    locations: locations,\r\n                };\r\n            } else {\r\n                this.editorData = source.editorData;\r\n                this.editorData.locations = locations;\r\n            }\r\n\r\n            const blockMap: number[] = [];\r\n\r\n            for (const key in map) {\r\n                blockMap[key] = map[key].uniqueId;\r\n            }\r\n\r\n            this.editorData.map = blockMap;\r\n        }\r\n\r\n        this.comment = source.comment;\r\n\r\n        if (source.forceAlphaBlending !== undefined) {\r\n            this.forceAlphaBlending = source.forceAlphaBlending;\r\n        }\r\n\r\n        if (source.alphaMode !== undefined) {\r\n            this.alphaMode = source.alphaMode;\r\n        }\r\n\r\n        if (!merge) {\r\n            this._mode = source.mode ?? NodeMaterialModes.Material;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clear the current graph and load a new one from a serialization object\r\n     * @param source defines the JSON representation of the material\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @param merge defines whether or not the source must be merged or replace the current content\r\n     * @deprecated Please use the parseSerializedObject method instead\r\n     */\r\n    public loadFromSerialization(source: any, rootUrl: string = \"\", merge = false) {\r\n        this.parseSerializedObject(source, rootUrl, merge);\r\n    }\r\n\r\n    /**\r\n     * Makes a duplicate of the current material.\r\n     * @param name defines the name to use for the new material\r\n     * @param shareEffect defines if the clone material should share the same effect (default is false)\r\n     * @returns the cloned material\r\n     */\r\n    public clone(name: string, shareEffect: boolean = false): NodeMaterial {\r\n        const serializationObject = this.serialize();\r\n\r\n        const clone = SerializationHelper.Clone(() => new NodeMaterial(name, this.getScene(), this.options), this);\r\n        clone.id = name;\r\n        clone.name = name;\r\n\r\n        clone.parseSerializedObject(serializationObject);\r\n        clone._buildId = this._buildId;\r\n        clone.build(false, !shareEffect);\r\n\r\n        return clone;\r\n    }\r\n\r\n    /**\r\n     * Awaits for all the material textures to be ready before resolving the returned promise.\r\n     * @returns A promise that resolves when the textures are ready.\r\n     */\r\n    public whenTexturesReadyAsync(): Promise<void[]> {\r\n        // Ensures all textures are ready to render.\r\n        const textureReadyPromises: Promise<void>[] = [];\r\n        this.getActiveTextures().forEach((texture) => {\r\n            const internalTexture = texture.getInternalTexture();\r\n            if (internalTexture && !internalTexture.isReady) {\r\n                textureReadyPromises.push(\r\n                    new Promise((textureResolve, textureReject) => {\r\n                        internalTexture.onLoadedObservable.addOnce(() => {\r\n                            textureResolve();\r\n                        });\r\n                        internalTexture.onErrorObservable.addOnce((e) => {\r\n                            textureReject(e);\r\n                        });\r\n                    })\r\n                );\r\n            }\r\n        });\r\n\r\n        return Promise.all(textureReadyPromises);\r\n    }\r\n\r\n    /**\r\n     * Creates a node material from parsed material data\r\n     * @param source defines the JSON representation of the material\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a new node material\r\n     */\r\n    public static Parse(source: any, scene: Scene, rootUrl: string = \"\"): NodeMaterial {\r\n        const nodeMaterial = SerializationHelper.Parse(() => new NodeMaterial(source.name, scene), source, scene, rootUrl);\r\n\r\n        nodeMaterial.parseSerializedObject(source, rootUrl);\r\n        nodeMaterial.build();\r\n\r\n        return nodeMaterial;\r\n    }\r\n\r\n    /**\r\n     * Creates a node material from a snippet saved in a remote file\r\n     * @param name defines the name of the material to create\r\n     * @param url defines the url to load from\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL for nested url in the node material\r\n     * @param skipBuild defines whether to build the node material\r\n     * @param targetMaterial defines a material to use instead of creating a new one\r\n     * @returns a promise that will resolve to the new node material\r\n     */\r\n    public static async ParseFromFileAsync(\r\n        name: string,\r\n        url: string,\r\n        scene: Scene,\r\n        rootUrl: string = \"\",\r\n        skipBuild: boolean = false,\r\n        targetMaterial?: NodeMaterial\r\n    ): Promise<NodeMaterial> {\r\n        const material = targetMaterial ?? new NodeMaterial(name, scene);\r\n\r\n        const data = await scene._loadFileAsync(url);\r\n        const serializationObject = JSON.parse(data);\r\n        material.parseSerializedObject(serializationObject, rootUrl);\r\n        if (!skipBuild) {\r\n            material.build();\r\n        }\r\n        return material;\r\n    }\r\n\r\n    /**\r\n     * Creates a node material from a snippet saved by the node material editor\r\n     * @param snippetId defines the snippet to load\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @param nodeMaterial defines a node material to update (instead of creating a new one)\r\n     * @param skipBuild defines whether to build the node material\r\n     * @param waitForTextureReadyness defines whether to wait for texture readiness resolving the promise (default: false)\r\n     * @returns a promise that will resolve to the new node material\r\n     */\r\n    public static ParseFromSnippetAsync(\r\n        snippetId: string,\r\n        scene: Scene = EngineStore.LastCreatedScene!,\r\n        rootUrl: string = \"\",\r\n        nodeMaterial?: NodeMaterial,\r\n        skipBuild: boolean = false,\r\n        waitForTextureReadyness: boolean = false\r\n    ): Promise<NodeMaterial> {\r\n        if (snippetId === \"_BLANK\") {\r\n            return Promise.resolve(NodeMaterial.CreateDefault(\"blank\", scene));\r\n        }\r\n\r\n        return new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        const snippet = JSON.parse(JSON.parse(request.responseText).jsonPayload);\r\n                        const serializationObject = JSON.parse(snippet.nodeMaterial);\r\n\r\n                        if (!nodeMaterial) {\r\n                            nodeMaterial = SerializationHelper.Parse(() => new NodeMaterial(snippetId, scene), serializationObject, scene, rootUrl);\r\n                            nodeMaterial.uniqueId = scene.getUniqueId();\r\n                        }\r\n\r\n                        nodeMaterial.parseSerializedObject(serializationObject);\r\n                        nodeMaterial.snippetId = snippetId;\r\n\r\n                        try {\r\n                            if (!skipBuild) {\r\n                                nodeMaterial.build();\r\n                            }\r\n                        } catch (err) {\r\n                            reject(err);\r\n                        }\r\n\r\n                        if (waitForTextureReadyness) {\r\n                            nodeMaterial\r\n                                .whenTexturesReadyAsync()\r\n                                .then(() => {\r\n                                    resolve(nodeMaterial!);\r\n                                })\r\n                                .catch((err) => {\r\n                                    reject(err);\r\n                                });\r\n                        } else {\r\n                            resolve(nodeMaterial);\r\n                        }\r\n                    } else {\r\n                        reject(\"Unable to load the snippet \" + snippetId);\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", this.SnippetUrl + \"/\" + snippetId.replace(/#/g, \"/\"));\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a new node material set to default basic configuration\r\n     * @param name defines the name of the material\r\n     * @param scene defines the hosting scene\r\n     * @returns a new NodeMaterial\r\n     */\r\n    public static CreateDefault(name: string, scene?: Scene) {\r\n        const newMaterial = new NodeMaterial(name, scene);\r\n\r\n        newMaterial.setToDefault();\r\n        newMaterial.build();\r\n\r\n        return newMaterial;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.NodeMaterial\", NodeMaterial);\r\n"]}