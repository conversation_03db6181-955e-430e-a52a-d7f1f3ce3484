{"version": 3, "file": "nativePipelineContext.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Native/nativePipelineContext.ts"], "names": [], "mappings": "AAOA,MAAM,OAAO,qBAAqB;IAQ9B,IAAW,OAAO;QACd,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,cAAc,GAAG,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACzF;QACD,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAIM,oBAAoB;QACvB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,sBAAsB;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAMD,YAAY,MAAoB,EAAE,OAAgB;QA7B3C,eAAU,GAAY,KAAK,CAAC;QA0B3B,gBAAW,GAA2B,EAAE,CAAC;QAI7C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IAEM,sBAAsB,CACzB,MAAc,EACd,mBAA8C,EAC9C,aAAuB,EACvB,QAA2D,EAC3D,WAAqB,EACrB,QAAmC,EACnC,eAAyB,EACzB,UAAoB;QAEpB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,MAAM,CAAC,sBAAsB,EAAE;YAC/B,KAAK,MAAM,IAAI,IAAI,mBAAmB,EAAE;gBACpC,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;aAC5D;SACJ;QAED,MAAM,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAC9E,uBAAuB,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YAC/C,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC;QAC7C,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE1B,IAAI,KAAa,CAAC;QAClB,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACjD,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;YACtD,IAAI,OAAO,IAAI,IAAI,EAAE;gBACjB,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC7B,KAAK,EAAE,CAAC;aACX;SACJ;QAED,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAChC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC;IACpE,CAAC;IAED;;QAEI;IACG,OAAO;QACV,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,WAAmB,EAAE,MAAmB;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC5C,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC;QAC/B,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;YACvC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QAErC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS;QACzD,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;YACtC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YAChB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,OAAO,GAAG,IAAI,CAAC;SAClB;QACD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YAChB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,OAAO,GAAG,IAAI,CAAC;SAClB;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACpE,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAClB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;YACtC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YAChB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,OAAO,GAAG,IAAI,CAAC;SAClB;QACD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YAChB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,OAAO,GAAG,IAAI,CAAC;SAClB;QACD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YAChB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,OAAO,GAAG,IAAI,CAAC;SAClB;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC/E,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACrB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;YACtC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YAChB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,OAAO,GAAG,IAAI,CAAC;SAClB;QACD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YAChB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,OAAO,GAAG,IAAI,CAAC;SAClB;QACD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YAChB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,OAAO,GAAG,IAAI,CAAC;SAClB;QACD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YAChB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,OAAO,GAAG,IAAI,CAAC;SAClB;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,WAAmB,EAAE,KAAa;QAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,KAAK,EAAE;YACxC,OAAO;SACV;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,EAAE;YAC1D,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;SACzC;IACL,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS;QACpD,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YACtC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC1D,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,OAAO,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC/D,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC7D,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;;;;OAOG;IACI,OAAO,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC1E,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;gBAChE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,WAAmB,EAAE,KAAiB;QACrD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAmB,EAAE,KAAiB;QACtD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAmB,EAAE,KAAiB;QACtD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAmB,EAAE,KAAiB;QACtD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,WAAmB,EAAE,KAAa;QAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,KAAK,EAAE;YACxC,OAAO;SACV;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,EAAE;YAC3D,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;SACzC;IACL,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS;QACrD,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YACtC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC3D,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,QAAQ,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAChE,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC9D,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;;;;OAOG;IACI,QAAQ,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC3E,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;gBACjE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAmB,EAAE,KAAkB;QACvD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,WAAmB,EAAE,KAAkB;QACxD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,WAAmB,EAAE,KAAkB;QACxD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,WAAmB,EAAE,KAAkB;QACxD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,WAAmB,EAAE,KAAmB;QACzD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,WAAmB,EAAE,KAAmB;QAC1D,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IACrE,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,WAAmB,EAAE,KAAmB;QAC1D,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IACrE,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,WAAmB,EAAE,KAAmB;QAC1D,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IACrE,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,WAAmB,EAAE,KAAe;QAChD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IAC/D,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,WAAmB,EAAE,KAAe;QACjD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IAChE,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,WAAmB,EAAE,KAAe;QACjD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IAChE,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,WAAmB,EAAE,KAAe;QACjD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IAChE,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,WAAmB,EAAE,QAAsB;QAC1D,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO;SACV;QAED,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,QAAQ,CAAC,CAAC;IACrE,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,WAAmB,EAAE,MAAmB;QACrD,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE;gBAC3E,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAmB,EAAE,MAAoB;QACzD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,MAAM,CAAC,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAmB,EAAE,MAAoB;QACzD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,MAAM,CAAC,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,WAAmB,EAAE,KAAa;QAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,KAAK,EAAE;YACxC,OAAO;SACV;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,KAAK,CAAC,EAAE;YAC5D,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;SACzC;IACL,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,WAAmB,EAAE,IAAa;QAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;YACvC,OAAO;SACV;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACjE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAChD;IACL,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,WAAmB,EAAE,OAAqB;QACxD,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE;YACtD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE;gBAC7E,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS;QACtD,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YACtC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC7D,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,WAAmB,EAAE,OAAqB;QACxD,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE;YACjE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE;gBACxF,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACjE,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;gBAChE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,WAAmB,EAAE,OAAqB;QACxD,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE;YAC5E,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE;gBACnG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,WAAmB,EAAE,UAA2B;QACjE,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE;YACxF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE;gBAC/G,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;;;;OAOG;IACI,SAAS,CAAC,WAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC5E,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;gBACnE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,WAAmB,EAAE,MAAmB;QACrD,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE;YAC9D,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE;gBACrF,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,WAAmB,EAAE,MAAmB,EAAE,KAAa;QACpE,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE;YACrE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE;gBAC5F,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,WAAmB,EAAE,MAAmB;QAC3D,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE;YACxE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAE,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE;gBAC/F,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;aACxC;SACJ;IACL,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { Effect } from \"../../Materials/effect\";\r\nimport type { IMatrixLike, IVector2<PERSON>ike, IVector3<PERSON>ike, IVector4Like, IColor3Like, IColor4Like, IQuaternionLike } from \"../../Maths/math.like\";\r\nimport type { IPipelineContext } from \"../IPipelineContext\";\r\nimport type { NativeEngine } from \"../nativeEngine\";\r\nimport type { NativeProgram } from \"./nativeInterfaces\";\r\n\r\nexport class NativePipelineContext implements IPipelineContext {\r\n    public isCompiled: boolean = false;\r\n    public compilationError?: Error;\r\n\r\n    public readonly isAsync: boolean;\r\n\r\n    public program: NativeProgram;\r\n\r\n    public get isReady(): boolean {\r\n        if (this.compilationError) {\r\n            const message = this.compilationError.message;\r\n            throw new Error(\"SHADER ERROR\" + (typeof message === \"string\" ? \"\\n\" + message : \"\"));\r\n        }\r\n        return this.isCompiled;\r\n    }\r\n\r\n    public onCompiled?: () => void;\r\n\r\n    public _getVertexShaderCode(): string | null {\r\n        return null;\r\n    }\r\n\r\n    public _getFragmentShaderCode(): string | null {\r\n        return null;\r\n    }\r\n\r\n    private _engine: NativeEngine;\r\n    private _valueCache: { [key: string]: any } = {};\r\n    private _uniforms: { [key: string]: Nullable<WebGLUniformLocation> };\r\n\r\n    constructor(engine: NativeEngine, isAsync: boolean) {\r\n        this._engine = engine;\r\n        this.isAsync = isAsync;\r\n    }\r\n\r\n    public _fillEffectInformation(\r\n        effect: Effect,\r\n        uniformBuffersNames: { [key: string]: number },\r\n        uniformsNames: string[],\r\n        uniforms: { [key: string]: Nullable<WebGLUniformLocation> },\r\n        samplerList: string[],\r\n        samplers: { [key: string]: number },\r\n        attributesNames: string[],\r\n        attributes: number[]\r\n    ) {\r\n        const engine = this._engine;\r\n        if (engine.supportsUniformBuffers) {\r\n            for (const name in uniformBuffersNames) {\r\n                effect.bindUniformBlock(name, uniformBuffersNames[name]);\r\n            }\r\n        }\r\n\r\n        const effectAvailableUniforms = this._engine.getUniforms(this, uniformsNames);\r\n        effectAvailableUniforms.forEach((uniform, index) => {\r\n            uniforms[uniformsNames[index]] = uniform;\r\n        });\r\n        this._uniforms = uniforms;\r\n\r\n        let index: number;\r\n        for (index = 0; index < samplerList.length; index++) {\r\n            const sampler = effect.getUniform(samplerList[index]);\r\n            if (sampler == null) {\r\n                samplerList.splice(index, 1);\r\n                index--;\r\n            }\r\n        }\r\n\r\n        samplerList.forEach((name, index) => {\r\n            samplers[name] = index;\r\n        });\r\n\r\n        attributes.push(...engine.getAttributes(this, attributesNames));\r\n    }\r\n\r\n    /**\r\n     * Release all associated resources.\r\n     **/\r\n    public dispose() {\r\n        this._uniforms = {};\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _cacheMatrix(uniformName: string, matrix: IMatrixLike): boolean {\r\n        const cache = this._valueCache[uniformName];\r\n        const flag = matrix.updateFlag;\r\n        if (cache !== undefined && cache === flag) {\r\n            return false;\r\n        }\r\n\r\n        this._valueCache[uniformName] = flag;\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _cacheFloat2(uniformName: string, x: number, y: number): boolean {\r\n        let cache = this._valueCache[uniformName];\r\n        if (!cache) {\r\n            cache = [x, y];\r\n            this._valueCache[uniformName] = cache;\r\n            return true;\r\n        }\r\n\r\n        let changed = false;\r\n        if (cache[0] !== x) {\r\n            cache[0] = x;\r\n            changed = true;\r\n        }\r\n        if (cache[1] !== y) {\r\n            cache[1] = y;\r\n            changed = true;\r\n        }\r\n\r\n        return changed;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _cacheFloat3(uniformName: string, x: number, y: number, z: number): boolean {\r\n        let cache = this._valueCache[uniformName];\r\n        if (!cache) {\r\n            cache = [x, y, z];\r\n            this._valueCache[uniformName] = cache;\r\n            return true;\r\n        }\r\n\r\n        let changed = false;\r\n        if (cache[0] !== x) {\r\n            cache[0] = x;\r\n            changed = true;\r\n        }\r\n        if (cache[1] !== y) {\r\n            cache[1] = y;\r\n            changed = true;\r\n        }\r\n        if (cache[2] !== z) {\r\n            cache[2] = z;\r\n            changed = true;\r\n        }\r\n\r\n        return changed;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _cacheFloat4(uniformName: string, x: number, y: number, z: number, w: number): boolean {\r\n        let cache = this._valueCache[uniformName];\r\n        if (!cache) {\r\n            cache = [x, y, z, w];\r\n            this._valueCache[uniformName] = cache;\r\n            return true;\r\n        }\r\n\r\n        let changed = false;\r\n        if (cache[0] !== x) {\r\n            cache[0] = x;\r\n            changed = true;\r\n        }\r\n        if (cache[1] !== y) {\r\n            cache[1] = y;\r\n            changed = true;\r\n        }\r\n        if (cache[2] !== z) {\r\n            cache[2] = z;\r\n            changed = true;\r\n        }\r\n        if (cache[3] !== w) {\r\n            cache[3] = w;\r\n            changed = true;\r\n        }\r\n\r\n        return changed;\r\n    }\r\n\r\n    /**\r\n     * Sets an integer value on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param value Value to be set.\r\n     */\r\n    public setInt(uniformName: string, value: number): void {\r\n        const cache = this._valueCache[uniformName];\r\n        if (cache !== undefined && cache === value) {\r\n            return;\r\n        }\r\n\r\n        if (this._engine.setInt(this._uniforms[uniformName]!, value)) {\r\n            this._valueCache[uniformName] = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a int2 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First int in int2.\r\n     * @param y Second int in int2.\r\n     */\r\n    public setInt2(uniformName: string, x: number, y: number): void {\r\n        if (this._cacheFloat2(uniformName, x, y)) {\r\n            if (!this._engine.setInt2(this._uniforms[uniformName], x, y)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a int3 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First int in int3.\r\n     * @param y Second int in int3.\r\n     * @param z Third int in int3.\r\n     */\r\n    public setInt3(uniformName: string, x: number, y: number, z: number): void {\r\n        if (this._cacheFloat3(uniformName, x, y, z)) {\r\n            if (!this._engine.setInt3(this._uniforms[uniformName], x, y, z)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a int4 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First int in int4.\r\n     * @param y Second int in int4.\r\n     * @param z Third int in int4.\r\n     * @param w Fourth int in int4.\r\n     */\r\n    public setInt4(uniformName: string, x: number, y: number, z: number, w: number): void {\r\n        if (this._cacheFloat4(uniformName, x, y, z, w)) {\r\n            if (!this._engine.setInt4(this._uniforms[uniformName], x, y, z, w)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets an int array on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setIntArray(uniformName: string, array: Int32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setIntArray(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an int array 2 on a uniform variable. (Array is specified as single array eg. [1,2,3,4] will result in [[1,2],[3,4]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setIntArray2(uniformName: string, array: Int32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setIntArray2(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an int array 3 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6] will result in [[1,2,3],[4,5,6]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setIntArray3(uniformName: string, array: Int32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setIntArray3(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an int array 4 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6,7,8] will result in [[1,2,3,4],[5,6,7,8]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setIntArray4(uniformName: string, array: Int32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setIntArray4(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an unsigned integer value on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param value Value to be set.\r\n     */\r\n    public setUInt(uniformName: string, value: number): void {\r\n        const cache = this._valueCache[uniformName];\r\n        if (cache !== undefined && cache === value) {\r\n            return;\r\n        }\r\n\r\n        if (this._engine.setUInt(this._uniforms[uniformName]!, value)) {\r\n            this._valueCache[uniformName] = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a unsigned int2 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First unsigned int in uint2.\r\n     * @param y Second unsigned int in uint2.\r\n     */\r\n    public setUInt2(uniformName: string, x: number, y: number): void {\r\n        if (this._cacheFloat2(uniformName, x, y)) {\r\n            if (!this._engine.setUInt2(this._uniforms[uniformName], x, y)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a unsigned int3 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First unsigned int in uint3.\r\n     * @param y Second unsigned int in uint3.\r\n     * @param z Third unsigned int in uint3.\r\n     */\r\n    public setUInt3(uniformName: string, x: number, y: number, z: number): void {\r\n        if (this._cacheFloat3(uniformName, x, y, z)) {\r\n            if (!this._engine.setUInt3(this._uniforms[uniformName], x, y, z)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a unsigned int4 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First unsigned int in uint4.\r\n     * @param y Second unsigned int in uint4.\r\n     * @param z Third unsigned int in uint4.\r\n     * @param w Fourth unsigned int in uint4.\r\n     */\r\n    public setUInt4(uniformName: string, x: number, y: number, z: number, w: number): void {\r\n        if (this._cacheFloat4(uniformName, x, y, z, w)) {\r\n            if (!this._engine.setUInt4(this._uniforms[uniformName], x, y, z, w)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets an unsigned int array on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setUIntArray(uniformName: string, array: Uint32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setUIntArray(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an unsigned int array 2 on a uniform variable. (Array is specified as single array eg. [1,2,3,4] will result in [[1,2],[3,4]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setUIntArray2(uniformName: string, array: Uint32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setUIntArray2(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an unsigned int array 3 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6] will result in [[1,2,3],[4,5,6]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setUIntArray3(uniformName: string, array: Uint32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setUIntArray3(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an unsigned int array 4 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6,7,8] will result in [[1,2,3,4],[5,6,7,8]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setUIntArray4(uniformName: string, array: Uint32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setUIntArray4(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an float array on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setFloatArray(uniformName: string, array: Float32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setFloatArray(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an float array 2 on a uniform variable. (Array is specified as single array eg. [1,2,3,4] will result in [[1,2],[3,4]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setFloatArray2(uniformName: string, array: Float32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setFloatArray2(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an float array 3 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6] will result in [[1,2,3],[4,5,6]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setFloatArray3(uniformName: string, array: Float32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setFloatArray3(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an float array 4 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6,7,8] will result in [[1,2,3,4],[5,6,7,8]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setFloatArray4(uniformName: string, array: Float32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setFloatArray4(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an array on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setArray(uniformName: string, array: number[]): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setArray(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an array 2 on a uniform variable. (Array is specified as single array eg. [1,2,3,4] will result in [[1,2],[3,4]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setArray2(uniformName: string, array: number[]): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setArray2(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an array 3 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6] will result in [[1,2,3],[4,5,6]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setArray3(uniformName: string, array: number[]): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setArray3(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets an array 4 on a uniform variable. (Array is specified as single array eg. [1,2,3,4,5,6,7,8] will result in [[1,2,3,4],[5,6,7,8]] in the shader)\r\n     * @param uniformName Name of the variable.\r\n     * @param array array to be set.\r\n     */\r\n    public setArray4(uniformName: string, array: number[]): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setArray4(this._uniforms[uniformName]!, array);\r\n    }\r\n\r\n    /**\r\n     * Sets matrices on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param matrices matrices to be set.\r\n     */\r\n    public setMatrices(uniformName: string, matrices: Float32Array): void {\r\n        if (!matrices) {\r\n            return;\r\n        }\r\n\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setMatrices(this._uniforms[uniformName]!, matrices);\r\n    }\r\n\r\n    /**\r\n     * Sets matrix on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param matrix matrix to be set.\r\n     */\r\n    public setMatrix(uniformName: string, matrix: IMatrixLike): void {\r\n        if (this._cacheMatrix(uniformName, matrix)) {\r\n            if (!this._engine.setMatrices(this._uniforms[uniformName]!, matrix.asArray())) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a 3x3 matrix on a uniform variable. (Specified as [1,2,3,4,5,6,7,8,9] will result in [1,2,3][4,5,6][7,8,9] matrix)\r\n     * @param uniformName Name of the variable.\r\n     * @param matrix matrix to be set.\r\n     */\r\n    public setMatrix3x3(uniformName: string, matrix: Float32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setMatrix3x3(this._uniforms[uniformName]!, matrix);\r\n    }\r\n\r\n    /**\r\n     * Sets a 2x2 matrix on a uniform variable. (Specified as [1,2,3,4] will result in [1,2][3,4] matrix)\r\n     * @param uniformName Name of the variable.\r\n     * @param matrix matrix to be set.\r\n     */\r\n    public setMatrix2x2(uniformName: string, matrix: Float32Array): void {\r\n        this._valueCache[uniformName] = null;\r\n        this._engine.setMatrix2x2(this._uniforms[uniformName]!, matrix);\r\n    }\r\n\r\n    /**\r\n     * Sets a float on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param value value to be set.\r\n     */\r\n    public setFloat(uniformName: string, value: number): void {\r\n        const cache = this._valueCache[uniformName];\r\n        if (cache !== undefined && cache === value) {\r\n            return;\r\n        }\r\n\r\n        if (this._engine.setFloat(this._uniforms[uniformName]!, value)) {\r\n            this._valueCache[uniformName] = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a boolean on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param bool value to be set.\r\n     */\r\n    public setBool(uniformName: string, bool: boolean): void {\r\n        const cache = this._valueCache[uniformName];\r\n        if (cache !== undefined && cache === bool) {\r\n            return;\r\n        }\r\n\r\n        if (this._engine.setInt(this._uniforms[uniformName]!, bool ? 1 : 0)) {\r\n            this._valueCache[uniformName] = bool ? 1 : 0;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a Vector2 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param vector2 vector2 to be set.\r\n     */\r\n    public setVector2(uniformName: string, vector2: IVector2Like): void {\r\n        if (this._cacheFloat2(uniformName, vector2.x, vector2.y)) {\r\n            if (!this._engine.setFloat2(this._uniforms[uniformName]!, vector2.x, vector2.y)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a float2 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First float in float2.\r\n     * @param y Second float in float2.\r\n     */\r\n    public setFloat2(uniformName: string, x: number, y: number): void {\r\n        if (this._cacheFloat2(uniformName, x, y)) {\r\n            if (!this._engine.setFloat2(this._uniforms[uniformName]!, x, y)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a Vector3 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param vector3 Value to be set.\r\n     */\r\n    public setVector3(uniformName: string, vector3: IVector3Like): void {\r\n        if (this._cacheFloat3(uniformName, vector3.x, vector3.y, vector3.z)) {\r\n            if (!this._engine.setFloat3(this._uniforms[uniformName]!, vector3.x, vector3.y, vector3.z)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a float3 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First float in float3.\r\n     * @param y Second float in float3.\r\n     * @param z Third float in float3.\r\n     */\r\n    public setFloat3(uniformName: string, x: number, y: number, z: number): void {\r\n        if (this._cacheFloat3(uniformName, x, y, z)) {\r\n            if (!this._engine.setFloat3(this._uniforms[uniformName]!, x, y, z)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a Vector4 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param vector4 Value to be set.\r\n     */\r\n    public setVector4(uniformName: string, vector4: IVector4Like): void {\r\n        if (this._cacheFloat4(uniformName, vector4.x, vector4.y, vector4.z, vector4.w)) {\r\n            if (!this._engine.setFloat4(this._uniforms[uniformName]!, vector4.x, vector4.y, vector4.z, vector4.w)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a Quaternion on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param quaternion Value to be set.\r\n     */\r\n    public setQuaternion(uniformName: string, quaternion: IQuaternionLike): void {\r\n        if (this._cacheFloat4(uniformName, quaternion.x, quaternion.y, quaternion.z, quaternion.w)) {\r\n            if (!this._engine.setFloat4(this._uniforms[uniformName]!, quaternion.x, quaternion.y, quaternion.z, quaternion.w)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a float4 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param x First float in float4.\r\n     * @param y Second float in float4.\r\n     * @param z Third float in float4.\r\n     * @param w Fourth float in float4.\r\n     */\r\n    public setFloat4(uniformName: string, x: number, y: number, z: number, w: number): void {\r\n        if (this._cacheFloat4(uniformName, x, y, z, w)) {\r\n            if (!this._engine.setFloat4(this._uniforms[uniformName]!, x, y, z, w)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a Color3 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param color3 Value to be set.\r\n     */\r\n    public setColor3(uniformName: string, color3: IColor3Like): void {\r\n        if (this._cacheFloat3(uniformName, color3.r, color3.g, color3.b)) {\r\n            if (!this._engine.setFloat3(this._uniforms[uniformName]!, color3.r, color3.g, color3.b)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a Color4 on a uniform variable.\r\n     * @param uniformName Name of the variable.\r\n     * @param color3 Value to be set.\r\n     * @param alpha Alpha value to be set.\r\n     */\r\n    public setColor4(uniformName: string, color3: IColor3Like, alpha: number): void {\r\n        if (this._cacheFloat4(uniformName, color3.r, color3.g, color3.b, alpha)) {\r\n            if (!this._engine.setFloat4(this._uniforms[uniformName]!, color3.r, color3.g, color3.b, alpha)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a Color4 on a uniform variable\r\n     * @param uniformName defines the name of the variable\r\n     * @param color4 defines the value to be set\r\n     */\r\n    public setDirectColor4(uniformName: string, color4: IColor4Like): void {\r\n        if (this._cacheFloat4(uniformName, color4.r, color4.g, color4.b, color4.a)) {\r\n            if (!this._engine.setFloat4(this._uniforms[uniformName]!, color4.r, color4.g, color4.b, color4.a)) {\r\n                this._valueCache[uniformName] = null;\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}