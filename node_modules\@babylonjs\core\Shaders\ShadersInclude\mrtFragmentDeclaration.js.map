{"version": 3, "file": "mrtFragmentDeclaration.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Shaders/ShadersInclude/mrtFragmentDeclaration.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAExD,MAAM,IAAI,GAAG,wBAAwB,CAAC;AACtC,MAAM,MAAM,GAAG;;;CAGd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAChD,gBAAgB;AAChB,MAAM,CAAC,MAAM,sBAAsB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore\";\n\nconst name = \"mrtFragmentDeclaration\";\nconst shader = `#if defined(WEBGL2) || defined(WEBGPU) || defined(NATIVE)\nlayout(location=0) out vec4 glFragData[{X}];\n#endif\n`;\n// Sideeffect\nShaderStore.IncludesShadersStore[name] = shader;\n/** @internal */\nexport const mrtFragmentDeclaration = { name, shader };\n"]}