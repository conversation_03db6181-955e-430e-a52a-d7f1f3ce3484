{"version": 3, "file": "pointParticleEmitter.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Particles/EmitterTypes/pointParticleEmitter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAKjD;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IAU7B;;OAEG;IACH;QAZA;;WAEG;QACI,eAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAC3C;;WAEG;QACI,eAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAK5B,CAAC;IAEhB;;;;;;OAMG;IACI,sBAAsB,CAAC,WAAmB,EAAE,iBAA0B,EAAE,QAAkB,EAAE,OAAgB;QAC/G,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACvE,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACvE,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAEvE,IAAI,OAAO,EAAE;YACT,iBAAiB,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO;SACV;QAED,OAAO,CAAC,8BAA8B,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;IAChG,CAAC;IAED;;;;;;OAMG;IACI,qBAAqB,CAAC,WAAmB,EAAE,gBAAyB,EAAE,QAAkB,EAAE,OAAgB;QAC7G,IAAI,OAAO,EAAE;YACT,gBAAgB,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACzC,OAAO;SACV;QACD,OAAO,CAAC,mCAAmC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC;IACxF,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,MAAM,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAE1C,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAElC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,WAA8C;QAC/D,WAAW,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACtD,WAAW,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED;;;OAGG;IACI,kBAAkB,CAAC,GAAkB;QACxC,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAChC,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAC/C,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC3D,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAE3D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,mBAAwB;QACjC,OAAO,CAAC,cAAc,CAAC,mBAAmB,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3E,OAAO,CAAC,cAAc,CAAC,mBAAmB,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAC/E,CAAC;CACJ", "sourcesContent": ["import { DeepCopier } from \"../../Misc/deepCopier\";\r\nimport type { Matrix } from \"../../Maths/math.vector\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { <PERSON>alar } from \"../../Maths/math.scalar\";\r\nimport type { Particle } from \"../../Particles/particle\";\r\nimport type { IParticleEmitterType } from \"./IParticleEmitterType\";\r\nimport type { UniformBufferEffectCommonAccessor } from \"../../Materials/uniformBufferEffectCommonAccessor\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\n/**\r\n * Particle emitter emitting particles from a point.\r\n * It emits the particles randomly between 2 given directions.\r\n */\r\nexport class PointParticleEmitter implements IParticleEmitterType {\r\n    /**\r\n     * Random direction of each particle after it has been emitted, between direction1 and direction2 vectors.\r\n     */\r\n    public direction1 = new Vector3(0, 1.0, 0);\r\n    /**\r\n     * Random direction of each particle after it has been emitted, between direction1 and direction2 vectors.\r\n     */\r\n    public direction2 = new Vector3(0, 1.0, 0);\r\n\r\n    /**\r\n     * Creates a new instance PointParticleEmitter\r\n     */\r\n    constructor() {}\r\n\r\n    /**\r\n     * Called by the particle System when the direction is computed for the created particle.\r\n     * @param worldMatrix is the world matrix of the particle system\r\n     * @param directionToUpdate is the direction vector to update with the result\r\n     * @param particle is the particle we are computed the direction for\r\n     * @param isLocal defines if the direction should be set in local space\r\n     */\r\n    public startDirectionFunction(worldMatrix: Matrix, directionToUpdate: Vector3, particle: Particle, isLocal: boolean): void {\r\n        const randX = Scalar.RandomRange(this.direction1.x, this.direction2.x);\r\n        const randY = Scalar.RandomRange(this.direction1.y, this.direction2.y);\r\n        const randZ = Scalar.RandomRange(this.direction1.z, this.direction2.z);\r\n\r\n        if (isLocal) {\r\n            directionToUpdate.copyFromFloats(randX, randY, randZ);\r\n            return;\r\n        }\r\n\r\n        Vector3.TransformNormalFromFloatsToRef(randX, randY, randZ, worldMatrix, directionToUpdate);\r\n    }\r\n\r\n    /**\r\n     * Called by the particle System when the position is computed for the created particle.\r\n     * @param worldMatrix is the world matrix of the particle system\r\n     * @param positionToUpdate is the position vector to update with the result\r\n     * @param particle is the particle we are computed the position for\r\n     * @param isLocal defines if the position should be set in local space\r\n     */\r\n    public startPositionFunction(worldMatrix: Matrix, positionToUpdate: Vector3, particle: Particle, isLocal: boolean): void {\r\n        if (isLocal) {\r\n            positionToUpdate.copyFromFloats(0, 0, 0);\r\n            return;\r\n        }\r\n        Vector3.TransformCoordinatesFromFloatsToRef(0, 0, 0, worldMatrix, positionToUpdate);\r\n    }\r\n\r\n    /**\r\n     * Clones the current emitter and returns a copy of it\r\n     * @returns the new emitter\r\n     */\r\n    public clone(): PointParticleEmitter {\r\n        const newOne = new PointParticleEmitter();\r\n\r\n        DeepCopier.DeepCopy(this, newOne);\r\n\r\n        return newOne;\r\n    }\r\n\r\n    /**\r\n     * Called by the GPUParticleSystem to setup the update shader\r\n     * @param uboOrEffect defines the update shader\r\n     */\r\n    public applyToShader(uboOrEffect: UniformBufferEffectCommonAccessor): void {\r\n        uboOrEffect.setVector3(\"direction1\", this.direction1);\r\n        uboOrEffect.setVector3(\"direction2\", this.direction2);\r\n    }\r\n\r\n    /**\r\n     * Creates the structure of the ubo for this particle emitter\r\n     * @param ubo ubo to create the structure for\r\n     */\r\n    public buildUniformLayout(ubo: UniformBuffer): void {\r\n        ubo.addUniform(\"direction1\", 3);\r\n        ubo.addUniform(\"direction2\", 3);\r\n    }\r\n\r\n    /**\r\n     * Returns a string to use to update the GPU particles update shader\r\n     * @returns a string containing the defines string\r\n     */\r\n    public getEffectDefines(): string {\r\n        return \"#define POINTEMITTER\";\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"PointParticleEmitter\"\r\n     * @returns a string containing the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"PointParticleEmitter\";\r\n    }\r\n\r\n    /**\r\n     * Serializes the particle system to a JSON object.\r\n     * @returns the JSON object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.type = this.getClassName();\r\n        serializationObject.direction1 = this.direction1.asArray();\r\n        serializationObject.direction2 = this.direction2.asArray();\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parse properties from a JSON object\r\n     * @param serializationObject defines the JSON object\r\n     */\r\n    public parse(serializationObject: any): void {\r\n        Vector3.FromArrayToRef(serializationObject.direction1, 0, this.direction1);\r\n        Vector3.FromArrayToRef(serializationObject.direction2, 0, this.direction2);\r\n    }\r\n}\r\n"]}