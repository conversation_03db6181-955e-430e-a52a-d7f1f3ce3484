{"version": 3, "file": "vrExperienceHelper.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/VR/vrExperienceHelper.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAGnD,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC1D,OAAO,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AAChF,OAAO,EAAE,6BAA6B,EAAE,MAAM,gDAAgD,CAAC;AAC/F,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAE/D,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AACtE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAExD,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AAEjD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAG3D,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;AACxC,OAAO,EAAE,4BAA4B,EAAE,MAAM,8CAA8C,CAAC;AAC5F,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AACpE,OAAO,EAAE,cAAc,EAAE,MAAM,yCAAyC,CAAC;AAEzE,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAC/E,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AAEvD,OAAO,sCAAsC,CAAC;AAC9C,OAAO,6BAA6B,CAAC;AACrC,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AAEnE,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,YAAY,EAAE,MAAM,qCAAqC,CAAC;AAGnE,OAAO,EAAE,WAAW,EAAE,8CAA0C;AA8DhE,MAAM,uBAAuB;IAoCzB,YACW,KAAY,EACnB,qBAAqC,IAAI;QADlC,UAAK,GAAL,KAAK,CAAO;QAxBvB,gBAAgB;QACT,4BAAuB,GAAY,KAAK,CAAC;QAChD,gBAAgB;QACT,sBAAiB,GAAY,KAAK,CAAC;QAM1C,gBAAgB;QACT,mCAA8B,GAAG,KAAK,CAAC;QAC9C,gBAAgB;QACT,uCAAkC,GAAG,KAAK,CAAC;QAClD,gBAAgB;QACT,wBAAmB,GAAG,KAAK,CAAC;QACnC,gBAAgB;QACT,uBAAkB,GAAG,KAAK,CAAC;QAClC,gBAAgB;QACT,iBAAY,GAAG,IAAI,CAAC;QAE3B,gBAAgB;QACT,mBAAc,GAAG,KAAK,CAAC;QAM1B,IAAI,CAAC,GAAG,GAAG,uBAAuB,CAAC,UAAU,EAAE,CAAC;QAEhD,eAAe;QACf,IAAI,CAAC,kBAAkB,EAAE;YACrB,IAAI,CAAC,YAAY,GAAG,WAAW,CAC3B,aAAa,EACb;gBACI,QAAQ,EAAE,MAAM;gBAChB,SAAS,EAAE,MAAM;gBACjB,YAAY,EAAE,EAAE;gBAChB,SAAS,EAAE,KAAK;aACnB,EACD,KAAK,CACR,CAAC;YACF,IAAI,CAAC,YAAY,CAAC,gCAAgC,EAAE,CAAC;YACrD,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,KAAK,CAAC;YACrC,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC;YACpC,MAAM,SAAS,GAAG,IAAI,gBAAgB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAC3D,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;YACzC,SAAS,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YACpD,SAAS,CAAC,eAAe,GAAG,KAAK,CAAC;YAClC,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,SAAS,CAAC;SAC1C;aAAM;YACH,IAAI,CAAC,YAAY,GAAG,kBAAkB,CAAC,KAAK,CAAC,aAAa,CAAS,CAAC;SACvE;IACL,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,MAAc;QAChC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED,gBAAgB;IACT,qBAAqB;QACxB,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACpC,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;SAC7E;IACL,CAAC;IAED,gBAAgB;IACT,mBAAmB;QACtB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;SAC3E;QACD,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;IACzC,CAAC;IAED,gBAAgB;IACT,gBAAgB;QACnB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED,gBAAgB;IACT,kBAAkB;QACrB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,6DAA6D;IACtD,sBAAsB,CAAC,WAAmB,GAAG,IAAG,CAAC;IAEjD,OAAO;QACV,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;SAC/B;IACL,CAAC;;AAvGa,kCAAU,GAAG,CAAC,AAAJ,CAAK;AA0GjC,MAAM,6BAA8B,SAAQ,uBAAuB;IAC/D,YACY,UAAkC,EAC1C,KAAY;QAEZ,KAAK,CAAC,KAAK,CAAC,CAAC;QAHL,eAAU,GAAV,UAAU,CAAwB;IAI9C,CAAC;IAED,cAAc,CAAC,MAAc;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,IAAI,MAAM,EAAE;YACR,OAAO,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;SACvC;aAAM;YACH,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;SACrD;IACL,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,gCAAgC;CAK5C;AAED;;;;GAIG;AACH,MAAM,OAAO,kBAAkB;IA6C3B;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IA4ED;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB,CAAC,KAAW;QACtC,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,IAAI,GAAG,qBAAqB,CAAC;YACnC,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC;YAC3C,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;SACrC;IACL,CAAC;IAED;;;;OAIG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;IAC1C,CAAC;IAED,IAAW,eAAe,CAAC,KAAW;QAClC,IAAI,KAAK,EAAE;YACP,6BAA6B;YAC7B,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;gBAChC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;aAC5C;YAED,uDAAuD;YACvD,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC;YACvC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,gCAAgC,EAAE,CAAC;YAClE,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,GAAG,KAAK,CAAC;YAClD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC;YACjD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,GAAG,aAAa,CAAC;SACvD;IACL,CAAC;IAeD;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW,CAAC,KAAc;QACjC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,KAAK,EAAE;YACR,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC;SACpD;IACL,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB,CAAC,KAAc;QACzC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,IAAY,8BAA8B;QACtC,OAAO,IAAI,CAAC,YAAY,CAAC,8BAA8B,CAAC;IAC5D,CAAC;IAqBD;;;;;OAKG;IACH,YACI,KAAY;IACZ,6DAA6D;IACtD,eAA0C,EAAE;QAA5C,iBAAY,GAAZ,YAAY,CAAgC;QA/QvD,gDAAgD;QACxC,4BAAuB,GAAG,KAAK,CAAC;QAUxC;;WAEG;QACI,oCAA+B,GAAG,KAAK,CAAC;QAE/C;;WAEG;QACI,sBAAiB,GAAG,IAAI,CAAC;QAEhC;;WAEG;QACI,2BAAsB,GAAG,IAAI,UAAU,EAAsB,CAAC;QAErE;;WAEG;QACI,gCAA2B,GAAG,IAAI,UAAU,EAAoC,CAAC;QAExF;;WAEG;QACI,0BAAqB,GAAG,IAAI,UAAU,EAAsB,CAAC;QAgB5D,uBAAkB,GAAY,KAAK,CAAC;QACpC,oBAAe,GAAG,KAAK,CAAC;QAExB,2BAAsB,GAAW,EAAE,CAAC;QACpC,uBAAkB,GAAW,kBAAkB,CAAC,8BAA8B,CAAC;QAC/E,uBAAkB,GAAW,GAAG,CAAC;QACjC,wBAAmB,GAAW,EAAE,CAAC;QAEjC,qBAAgB,GAAY,IAAI,CAAC;QACjC,6BAAwB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAElD,kCAA6B,GAAG,IAAI,CAAC;QAErC,4BAAuB,GAAW,SAAS,CAAC;QAC5C,8BAAyB,GAAW,SAAS,CAAC;QAC9C,mBAAc,GAAW,CAAC,CAAC;QAC3B,gBAAW,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEnC,sBAAiB,GAAG,IAAI,CAAC;QACzB,wBAAmB,GAAG,IAAI,CAAC;QAE3B,sBAAiB,GAAW,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QACpD,qBAAgB,GAAW,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEvD;;WAEG;QACI,sBAAiB,GAAG,IAAI,UAAU,EAAgB,CAAC;QAE1D;;WAEG;QACI,oBAAe,GAAG,IAAI,UAAU,EAAe,CAAC;QAIvD;;WAEG;QACI,2BAAsB,GAAG,IAAI,UAAU,EAAW,CAAC;QAE1D;;WAEG;QACI,0BAAqB,GAAG,IAAI,UAAU,EAAW,CAAC;QAEzD;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAgB,CAAC;QAcjE;;WAEG;QACI,yBAAoB,GAAY,IAAI,CAAC;QAGpC,8BAAyB,GAAG,KAAK,CAAC;QAClC,yBAAoB,GAAG,KAAK,CAAC;QAC7B,iBAAY,GAAG,IAAI,CAAC;QACpB,yBAAoB,GAAG,IAAI,CAAC;QA6CpC;;WAEG;QACI,2BAAsB,GAAG,IAAI,CAAC;QACrC;;WAEG;QACI,2BAAsB,GAAG,IAAI,CAAC;QACrC;;WAEG;QACI,+BAA0B,GAAG,IAAI,CAAC;QAiEzC;;;WAGG;QACI,mCAA8B,GAAG,IAAI,CAAC;QAS7C;;;WAGG;QACI,eAAU,GAAY,KAAK,CAAC;QAwP3B,cAAS,GAAG,GAAG,EAAE;YACrB,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACpC,CAAC,CAAC;QAEM,wBAAmB,GAAG,GAAG,EAAE;YAC/B,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC;YAC5D,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACrD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,EAAE;oBACzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,GAAG,EAAE,GAAG,IAAI,CAAC;oBACnG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,GAAG,GAAG,IAAI,CAAC;oBACrG,6DAA6D;oBAC7D,IAAI,CAAC,uBAAuB,EAAE,CAAC;iBAClC;aACJ;QACL,CAAC,CAAC;QAkCM,8BAAyB,GAAG,EAAE,mBAAmB,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;QAuM/G,kBAAa,GAAG,GAAG,EAAE;YACzB,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,aAAa,IAAI,IAAI,CAAC,+BAA+B,EAAE;gBAC/E,QAAQ;aACX;iBAAM;gBACH,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC;aACpD;QACL,CAAC,CAAC;QAwHM,2BAAsB,GAAG,CAAC,OAAgB,EAAE,EAAE;YAClD,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,YAAY,EAAE;gBACvC,IAAI,OAAO,CAAC,SAAS,EAAE;oBACnB,OAAO,CAAC,kBAAkB,CAAC,CAAC,WAAW,EAAE,EAAE;wBACvC,IAAI,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,oBAAoB,EAAE;4BAC7D,uEAAuE;4BACvE,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;4BAC3D,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;yBAChE;oBACL,CAAC,CAAC,CAAC;iBACN;gBACD,IAAI,OAAO,CAAC,UAAU,EAAE;oBACpB,OAAO,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,EAAE;wBACxC,IAAI,IAAI,CAAC,yBAAyB,EAAE;4BAChC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;yBACrD;oBACL,CAAC,CAAC,CAAC;iBACN;gBACD,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,EAAE;oBAClB,OAAQ,CAAC,YAAY,CAAC,CAAC,aAA4B,EAAE,EAAE;wBAChE,IAAI,IAAI,CAAC,oBAAoB,IAAI,aAAa,KAAK,aAAa,CAAC,CAAC,EAAE;4BAChE,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC;yBAC7C;oBACL,CAAC,CAAC,CAAC;oBACU,OAAQ,CAAC,UAAU,CAAC,CAAC,aAA4B,EAAE,EAAE;wBAC9D,IAAI,IAAI,CAAC,oBAAoB,IAAI,aAAa,KAAK,aAAa,CAAC,CAAC,EAAE;4BAChE,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC;yBAC3C;oBACL,CAAC,CAAC,CAAC;iBACN;aACJ;QACL,CAAC,CAAC;QAgQM,mBAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAChC,uBAAkB,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QAC3C,mBAAc,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAl4BvC,MAAM,CAAC,IAAI,CAAC,4GAA4G,CAAC,CAAC;QAC1H,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,eAAe,EAAE,CAAC;QAEzD,wBAAwB;QAExB,MAAM,WAAW,GAAG,eAAe,IAAI,SAAS,CAAC;QACjD,wGAAwG;QACxG,IAAI,CAAC,WAAW,IAAI,YAAY,CAAC,KAAK,KAAK,SAAS,EAAE;YAClD,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC;SAC7B;QAED,gBAAgB;QAChB,IAAI,YAAY,CAAC,2CAA2C,KAAK,SAAS,EAAE;YACxE,YAAY,CAAC,2CAA2C,GAAG,IAAI,CAAC;SACnE;QACD,IAAI,YAAY,CAAC,6BAA6B,KAAK,SAAS,EAAE;YAC1D,YAAY,CAAC,6BAA6B,GAAG,IAAI,CAAC;SACrD;QACD,IAAI,YAAY,CAAC,WAAW,KAAK,SAAS,EAAE;YACxC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC;SACnC;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,eAAe;QACf,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;SAC9D;aAAM;YACH,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;SAC3D;QAED,oBAAoB;QACpB,IAAI,YAAY,CAAC,6BAA6B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YACzE,IAAI,CAAC,wBAAwB,GAAG,IAAI,uBAAuB,CAAC,2BAA2B,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YAExH,iCAAiC;YACjC,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBAC1B,IAAI,CAAC,wBAAwB,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC;gBACnE,IAAI,CAAC,wBAAwB,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC;gBACnE,oCAAoC;gBACpC,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,YAAY,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE;oBACvF,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;oBAC9C,IAAI,YAAY,CAAC,kBAAkB,EAAE;wBACjC,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;qBAC9F;yBAAM;wBACH,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,CAAC,QAAQ,CACrD,UAAU,CAAC,oBAAoB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAC7G,CAAC;qBACL;oBACD,IAAI,CAAC,wBAAwB,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;iBAC1E;aACJ;YAED,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,wBAAwB,CAAC;YACzD,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;aAC5C;SACJ;aAAM;YACH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;SACnD;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,IAAK,SAAiB,CAAC,EAAE,EAAE;YAClD,iCAAiC;YACjC,mBAAmB,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;gBAC3E,IAAI,SAAS,EAAE;oBACX,MAAM,CAAC,GAAG,CAAC,2EAA2E,CAAC,CAAC;oBACxF,yCAAyC;oBACzC,KAAK;yBACA,8BAA8B,CAAC;wBAC5B,WAAW,EAAE,YAAY,CAAC,WAAW,IAAI,EAAE;qBAC9C,CAAC;yBACD,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE;wBACT,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;wBACb,sBAAsB;wBACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;wBAEvB,IAAI,CAAC,YAAY,GAAG,IAAI,6BAA6B,CAAC,GAAG,EAAE;4BACvD,OAAO,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC;wBACzC,CAAC,EAAE,KAAK,CAAC,CAAC;wBAEV,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;4BAC1D,iCAAiC;4BACjC,QAAQ,KAAK,EAAE;gCACX,KAAK,UAAU,CAAC,WAAW;oCACvB,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oCAClD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;wCAC5B,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;qCACrC;oCACD,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC;oCACzE,MAAM;gCACV,KAAK,UAAU,CAAC,UAAU;oCACtB,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oCAEjD,qEAAqE;oCACrE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC;oCACjC,MAAM;gCACV,KAAK,UAAU,CAAC,KAAK;oCACjB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;oCAC1B,MAAM;gCACV,KAAK,UAAU,CAAC,SAAS;oCACrB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;oCAC3B,MAAM;6BACb;wBACL,CAAC,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC;iBACV;qBAAM;oBACH,sDAAsD;oBACtD,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;iBAC7C;YACL,CAAC,CAAC,CAAC;SACN;aAAM;YACH,mCAAmC;YACnC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;SAC7C;IACL,CAAC;IAEO,eAAe,CAAC,KAAY,EAAE,YAAuC;QACzE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,oBAAoB;QACpB,IAAI,YAAY,CAAC,2CAA2C,EAAE;YAC1D,IAAI,CAAC,0BAA0B,GAAG,IAAI,6BAA6B,CAC/D,6BAA6B,EAC7B,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,EACX,IAAI,EACJ,YAAY,CAAC,gCAAgC,CAChD,CAAC;YACF,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC;SACzE;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,6BAA6B,CAAC,GAAG,EAAE;YACvD,OAAO,IAAI,CAAC,eAAe,CAAC;QAChC,CAAC,EAAE,KAAK,CAAC,CAAC;QACV,wBAAwB;QACxB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,IAAI,CAAC,MAAM,GAAsB,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAClE,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC;YACxC,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,kBAAkB,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,uBAAuB,CAAC;YAC5C,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,aAAa;gBAC7B,CAAC,CAAC,+CAA+C;gBACjD,CAAC,CAAC,yiDAAyiD,CAAC;YAChjD,IAAI,GAAG,GACH,2IAA2I;gBAC3I,GAAG;gBACH,gUAAgU,CAAC;YACrU,GAAG,IAAI,uDAAuD,CAAC;YAC/D,kHAAkH;YAClH,kDAAkD;YAClD,8CAA8C;YAC9C,mDAAmD;YAEnD,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAC9C,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;YAChD,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAE5D,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACnC;QAED,wBAAwB;QACxB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;gBACvC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBAClB,IAAI,CAAC,OAAO,EAAE,CAAC;iBAClB;YACL,CAAC,CAAC,CAAC;SACN;QAED,gBAAgB;QAEhB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC;QAC3D,IAAI,CAAC,UAAU,EAAE;YACb,OAAO;SACV;QAED,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACtD,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;QAE/E,8CAA8C;QAC9C,IAAI,YAAY,CAAC,2CAA2C,EAAE;YAC1D,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;QAED,6CAA6C;QAC7C,IAAI,CAAC,UAAU,GAAG,CAAC,KAAoB,EAAE,EAAE;YACvC,IAAI,KAAK,CAAC,OAAO,KAAK,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;gBACzC,IAAI,CAAC,MAAM,EAAE,CAAC;aACjB;QACL,CAAC,CAAC;QACF,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAEtD,kDAAkD;QAClD,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,CAClC,GAAG,EAAE;YACD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,IAAI,IAAI,CAAC,uBAAuB,EAAE;oBAC9B,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,cAAc,EAAE,CAAC;iBAC5C;aACJ;QACL,CAAC,EACD,iBAAiB,CAAC,gBAAgB,EAClC,KAAK,CACR,CAAC;QAEF,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/B,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,yBAAyB;QACzB,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC;QACpC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;QACpE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC;QAE7C,kDAAkD;QAClD,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YAChC,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,IAAI,KAAK,CAAC,YAAY,KAAK,IAAI,CAAC,yBAAyB,IAAK,CAAC,CAAC,KAAuB,CAAC,WAAW,KAAK,OAAO,EAAE;oBAC7G,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,EAAE;wBAC1C,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC;qBAC7C;yBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,SAAS,EAAE;wBAC/C,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC;qBAC3C;iBACJ;aACJ;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;YAC/B,IAAI,CAAC,mBAAmB,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,CAAC;SAC5E;IACL,CAAC;IAmBD;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,uBAAuB,CAAC;IACrI,CAAC;IAEO,wBAAwB;QAC5B,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,EAAE;YAC/D,MAAM,IAAI,GAAe,IAAI,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;YACpE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC;SAChE;IACL,CAAC;IAEO,gBAAgB;QACpB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,EAAE;YAClE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC/B;IACL,CAAC;IAEO,uBAAuB;QAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzC,OAAO;SACV;QACD,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC;QACxC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,sBAAsB,CAAC;SACnD;IACL,CAAC;IAGD;;;OAGG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,EAAE,EAAE;YACT,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,YAAY,CAAC,cAAc,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;YACzF,OAAO;SACV;QACD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI;gBACA,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;aACrD;YAAC,OAAO,GAAG,EAAE;gBACV,MAAM,CAAC,IAAI,CAAC,2CAA2C,GAAG,GAAG,CAAC,CAAC;aAClE;SACJ;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YAE3D,IAAI,IAAI,CAAC,yBAAyB,EAAE;gBAChC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,GAAG,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,iBAAiB,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC;gBACvJ,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,GAAG,IAAI,CAAC;aAC5D;YAED,qDAAqD;YACrD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YAEhD,2EAA2E;YAC3E,IAAU,IAAI,CAAC,eAAgB,CAAC,mBAAmB,EAAE;gBACjD,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,GAAS,IAAI,CAAC,eAAgB,CAAC,mBAAmB,CAAC;gBAC/F,IAAI,CAAC,eAAgB,CAAC,mBAAmB,GAAG,MAAM,CAAC,SAAS,CAAC;aACtE;YACD,IAAU,IAAI,CAAC,eAAgB,CAAC,mBAAmB,EAAE;gBACjD,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,GAAS,IAAI,CAAC,eAAgB,CAAC,mBAAmB,CAAC;gBAC/F,IAAI,CAAC,eAAgB,CAAC,mBAAmB,GAAG,MAAM,CAAC,SAAS,CAAC;aACtE;YACD,IAAU,IAAI,CAAC,eAAgB,CAAC,kBAAkB,EAAE;gBAChD,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,GAAS,IAAI,CAAC,eAAgB,CAAC,kBAAkB,CAAC;gBAC7F,IAAI,CAAC,eAAgB,CAAC,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC;aACrE;SACJ;QAED,mDAAmD;QACnD,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,IAAI,CAAC,0BAA0B,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;YAC1D,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBAC1B,IAAI,CAAC,0BAA0B,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC;aACxE;YACD,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,0BAA0B,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAC7E,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,0BAA0B,CAAC,6BAA6B,CAAC,OAAO,CAAC,GAAG,EAAE;gBACvE,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;SACN;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;SAC5C;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACxD;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,IAAI,CAAC,EAAE,EAAE;YACT,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;YACrC,OAAO;SACV;QACD,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI;oBACA,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;iBACpD;gBAAC,OAAO,GAAG,EAAE;oBACV,MAAM,CAAC,IAAI,CAAC,0CAA0C,GAAG,GAAG,CAAC,CAAC;iBACjE;aACJ;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;aAC9D;YAED,IAAI,IAAI,CAAC,yBAAyB,EAAE;gBAChC,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC;aACxE;YAED,IAAI,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,wBAAwB,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;gBACxD,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAEzD,8BAA8B;gBAC9B,IAAI,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,EAAE;oBAC9C,IAAI,CAAC,wBAAyB,CAAC,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,CAAC;oBAC9G,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,GAAG,IAAI,CAAC;iBAC7D;gBACD,IAAI,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,EAAE;oBAC9C,IAAI,CAAC,wBAAyB,CAAC,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,CAAC;oBAC9G,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,GAAG,IAAI,CAAC;iBAC7D;gBACD,IAAI,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,EAAE;oBAC7C,IAAI,CAAC,wBAAyB,CAAC,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,CAAC;oBAC5G,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,GAAG,IAAI,CAAC;iBAC5D;aACJ;iBAAM,IAAI,IAAI,CAAC,eAAe,EAAE;gBAC7B,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;gBAC/C,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC;gBAChD,IAAI,IAAI,CAAC,aAAa,EAAE;oBACpB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;iBAC5C;gBAED,8BAA8B;gBAC9B,IAAI,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,EAAE;oBAC9C,IAAI,CAAC,eAAgB,CAAC,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,CAAC;oBACrG,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,GAAG,IAAI,CAAC;iBAC7D;gBACD,IAAI,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,EAAE;oBAC9C,IAAI,CAAC,eAAgB,CAAC,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,CAAC;oBACrG,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,GAAG,IAAI,CAAC;iBAC7D;gBACD,IAAI,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,EAAE;oBAC7C,IAAI,CAAC,eAAgB,CAAC,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,CAAC;oBACnG,IAAI,CAAC,yBAAyB,CAAC,kBAAkB,GAAG,IAAI,CAAC;iBAC5D;aACJ;YAED,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAE/B,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACvD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC;aACpD;YAED,qEAAqE;YACrE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC;YAEjC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;SAC9B;IACL,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ,CAAC,KAAc;QAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC1B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,GAAG,KAAK,CAAC;SAC7C;IACL,CAAC;IAED;;OAEG;IACI,kBAAkB;QACrB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,mEAAmE;YACnE,IAAI,IAAI,CAAC,EAAE,EAAE;gBACT,IAAI,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,EAAE;oBACnD,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;iBACrC;gBACD,OAAO;aACV;YAED,IAAI,CAAC,qBAAqB,GAAG,CAAC,IAAI,EAAE,EAAE;gBAClC,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,cAAc,CAAC,CAAC;YACpF,CAAC,CAAC;YAEF,IAAI,CAAC,sBAAsB,GAAG,GAAG,EAAE;gBAC/B,OAAO,IAAI,CAAC;YAChB,CAAC,CAAC;YAEF,IAAI,CAAC,sBAAsB,GAAG,CAAC,IAAI,EAAE,EAAE;gBACnC,IACI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;oBAChC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,EAChJ;oBACE,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;iBAC3C;gBACD,OAAO,KAAK,CAAC;YACjB,CAAC,CAAC;YAEF,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SACpC;IACL,CAAC;IAUO,qBAAqB,CAAC,IAAkB;QAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzD,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;gBAC/C,OAAO,IAAI,CAAC;aACf;SACJ;QACD,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,cAAc,EAAE;YAC1D,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,SAAe;QAC/B,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,OAAO;SACV;QAED,IAAI,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;YACrD,OAAO;SACV;QAED,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAChD,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,SAAe;QAClC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,OAAO;SACV;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACjE,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;YAClB,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;SACpD;IACL,CAAC;IAED;;;OAGG;IACI,mBAAmB,CAAC,yBAAiD,EAAE;QAC1E,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACjC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,sBAAsB,CAAC,WAAW,IAAI,sBAAsB,CAAC,aAAa,CAAC,EAAE;gBACzG,MAAM,WAAW,GAAmB,sBAAsB,CAAC,WAAW,IAAI,EAAE,CAAC;gBAC7E,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;oBACrB,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,sBAAsB,CAAC,aAAc,CAAC,CAAC;oBACnF,IAAI,SAAS,EAAE;wBACX,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;qBAC/B;iBACJ;gBACD,IAAI,IAAI,CAAC,EAAE,EAAE;oBACT,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;wBACzB,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBAC7C,CAAC,CAAC,CAAC;oBACH,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE;wBACjC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;qBAClC;oBACD,OAAO;iBACV;qBAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBACzB,MAAM,SAAS,GAAG,GAAG,EAAE;wBACnB,IAAI,IAAI,CAAC,UAAU,EAAE;4BACjB,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;4BAC9C,IAAI,IAAI,CAAC,EAAE,EAAE;gCACT,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE;oCACjC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;iCAClC;6BACJ;iCAAM;gCACH,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC;6BACpD;yBACJ;oBACL,CAAC,CAAC;oBACF,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;oBAC5C,OAAO;iBACV;aACJ;YAED,IAAI,sBAAsB,CAAC,aAAa,EAAE;gBACtC,IAAI,CAAC,cAAc,GAAG,sBAAsB,CAAC,aAAa,CAAC;aAC9D;YACD,IAAI,sBAAsB,CAAC,WAAW,EAAE;gBACpC,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC,WAAW,CAAC;aACpE;YAED,IAAI,sBAAsB,CAAC,iBAAiB,EAAE;gBAC1C,IAAI,CAAC,kBAAkB,GAAG,sBAAsB,CAAC,iBAAiB,CAAC;aACtE;YACD,IAAI,sBAAsB,CAAC,iBAAiB,IAAI,sBAAsB,CAAC,iBAAiB,GAAG,CAAC,EAAE;gBAC1F,IAAI,CAAC,kBAAkB,GAAG,sBAAsB,CAAC,iBAAiB,CAAC;aACtE;YACD,IAAI,sBAAsB,CAAC,kBAAkB,IAAI,sBAAsB,CAAC,kBAAkB,GAAG,CAAC,EAAE;gBAC5F,IAAI,CAAC,mBAAmB,GAAG,sBAAsB,CAAC,kBAAkB,CAAC;aACxE;YACD,IAAI,sBAAsB,CAAC,cAAc,KAAK,SAAS,EAAE;gBACrD,IAAI,CAAC,oBAAoB,GAAG,sBAAsB,CAAC,cAAc,CAAC;aACrE;YAED,wEAAwE;YACxE,kFAAkF;YAClF,4BAA4B;YAC5B,MAAM,4BAA4B,GAAG,IAAI,4BAA4B,EAAE,CAAC;YACxE,4BAA4B,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACpE,4BAA4B,CAAC,eAAe,GAAG,IAAI,CAAC;YACpD,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;YACtC,IAAI,IAAI,CAAC,6BAA6B,EAAE;gBACpC,IAAI,CAAC,2BAA2B,EAAE,CAAC;aACtC;SACJ;IACL,CAAC;IAmCO,qBAAqB,CAAC,WAAwB,EAAE,KAA8B;QAClF,gEAAgE;QAChE,IAAI,IAAI,CAAC,8BAA8B,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE;YAC9E,OAAO;SACV;QACD,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE;YACvC,IAAI,WAAW,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,IAAI,KAAK,CAAC,YAAY,EAAE;gBAC/D,KAAK,CAAC,gBAAgB,EAAE,CAAC;gBACzB,KAAK,CAAC,8BAA8B,GAAG,IAAI,CAAC;aAC/C;SACJ;aAAM;YACH,6EAA6E;YAC7E,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE;gBACrG,IAAI,IAAI,CAAC,eAAe,EAAE;oBACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBACzC;gBAED,KAAK,CAAC,8BAA8B,GAAG,KAAK,CAAC;aAChD;SACJ;IACL,CAAC;IACO,YAAY,CAAC,WAAwB,EAAE,KAA8B;QACzE,4EAA4E;QAC5E,IAAI,KAAK,CAAC,8BAA8B,EAAE;YACtC,OAAO;SACV;QAED,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;YAC3B,IAAI,WAAW,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,IAAI,KAAK,CAAC,YAAY,EAAE;gBAC/D,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;iBAC7B;aACJ;SACJ;aAAM;YACH,IAAI,WAAW,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3C,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC;aACpC;SACJ;QAED,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE;YAC5B,IAAI,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,IAAI,KAAK,CAAC,YAAY,EAAE;gBAC9D,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBACjC,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;iBAC5B;aACJ;SACJ;aAAM;YACH,IAAI,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE;gBAC1C,KAAK,CAAC,mBAAmB,GAAG,KAAK,CAAC;aACrC;SACJ;IACL,CAAC;IACO,uBAAuB,CAAC,WAAwB,EAAE,KAA8B;QACpF,wFAAwF;QACxF,IAAI,KAAK,CAAC,8BAA8B,EAAE;YACtC,OAAO;SACV;QACD,qBAAqB;QACrB,IAAI,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,IAAI,KAAK,CAAC,YAAY,EAAE;YAC9D,IAAI,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBAC3C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;oBACvB,OAAO;iBACV;gBAED,kDAAkD;gBAClD,MAAM,QAAQ,GAAG,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,CAAC,iBAAiB,EAAE,CAAC,CAAC;gBAC1G,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;gBAE/C,6DAA6D;gBAC7D,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACjD,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC1B,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC1B,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACnI,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAE9D,gFAAgF;gBAChF,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAE3G,kFAAkF;gBAClF,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBACnD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACtE,IAAI,GAAG,IAAI,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,QAAQ,GAAG,CAAC,EAAE;oBAC5G,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;iBACxC;gBAED,KAAK,CAAC,kCAAkC,GAAG,IAAI,CAAC;aACnD;SACJ;aAAM;YACH,KAAK,CAAC,kCAAkC,GAAG,KAAK,CAAC;SACpD;IACL,CAAC;IAEO,2BAA2B;QAC/B,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC,qBAAqB,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACvH,IAAI,CAAC,oBAAoB,CAAC,UAAU,GAAG,KAAK,CAAC;QAE7C,MAAM,MAAM,GAAG,GAAG,CAAC;QACnB,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,gBAAgB,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACvF,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC/B,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;QAE5C,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,CAAC;QAC3B,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,CAAC;QAC3B,MAAM,MAAM,GAAG,GAAG,CAAC;QAEnB,OAAO,CAAC,SAAS,EAAE,CAAC;QACpB,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAC7D,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC;QACjD,OAAO,CAAC,IAAI,EAAE,CAAC;QACf,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;QACvB,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC;QACrD,OAAO,CAAC,MAAM,EAAE,CAAC;QACjB,OAAO,CAAC,SAAS,EAAE,CAAC;QACpB,cAAc,CAAC,MAAM,EAAE,CAAC;QAExB,MAAM,2BAA2B,GAAG,IAAI,gBAAgB,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3F,2BAA2B,CAAC,cAAc,GAAG,cAAc,CAAC;QAC5D,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG,2BAA2B,CAAC;QAEjE,MAAM,KAAK,GAAG,WAAW,CACrB,oBAAoB,EACpB;YACI,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,GAAG;YACd,YAAY,EAAE,EAAE;YAChB,SAAS,EAAE,KAAK;SACnB,EACD,IAAI,CAAC,MAAM,CACd,CAAC;QACF,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;QACzB,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAEzC,MAAM,oBAAoB,GAAG,IAAI,SAAS,CAAC,sBAAsB,EAAE,YAAY,EAAE,EAAE,EAAE,SAAS,CAAC,mBAAmB,EAAE,SAAS,CAAC,uBAAuB,CAAC,CAAC;QAEvJ,MAAM,IAAI,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,IAAI,CAAC;YACN,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,CAAC;SACX,CAAC,CAAC;QACH,IAAI,CAAC,IAAI,CAAC;YACN,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,GAAG;SACb,CAAC,CAAC;QACH,IAAI,CAAC,IAAI,CAAC;YACN,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,CAAC;SACX,CAAC,CAAC;QAEH,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEnC,MAAM,cAAc,GAAG,IAAI,QAAQ,EAAE,CAAC;QACtC,cAAc,CAAC,aAAa,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;QAClE,oBAAoB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAEvD,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;QACtB,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAE5C,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAEO,wBAAwB;QAC5B,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7B,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,oBAAoB,CAAC,SAAS,GAAG,KAAK,CAAC;YAC5C,IAAI,IAAI,CAAC,6BAA6B,EAAE;gBAC7B,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC,CAAC,CAAE,CAAC,SAAS,GAAG,KAAK,CAAC;aACxE;SACJ;IACL,CAAC;IAEO,aAAa,CAAC,KAAc;QAChC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,YAAY,UAAU,CAAC,EAAE;YAC/C,OAAO;SACV;QAED,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;aAAM;YACH,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;QAED,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,EAAE,CAAC;QAErC,MAAM,MAAM,GAAG,UAAU,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAEpG,MAAM,iBAAiB,GAAG,IAAI,SAAS,CAAC,mBAAmB,EAAE,oBAAoB,EAAE,EAAE,EAAE,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAEjK,MAAM,qBAAqB,GAAG,EAAE,CAAC;QACjC,qBAAqB,CAAC,IAAI,CAAC;YACvB,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,kBAAkB;SACjD,CAAC,CAAC;QACH,qBAAqB,CAAC,IAAI,CAAC;YACvB,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,MAAM;SAChB,CAAC,CAAC;QAEH,iBAAiB,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAEjD,iBAAiB,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEtD,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAExD,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG,EAAE,CAAC;QAEtC,MAAM,WAAW,GAAG,IAAI,SAAS,CAAC,aAAa,EAAE,gBAAgB,EAAE,EAAE,EAAE,SAAS,CAAC,mBAAmB,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAE5I,MAAM,kBAAkB,GAAG,EAAE,CAAC;QAC9B,kBAAkB,CAAC,IAAI,CAAC;YACpB,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,CAAC;SACX,CAAC,CAAC;QACH,kBAAkB,CAAC,IAAI,CAAC;YACpB,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,CAAC;SACX,CAAC,CAAC;QACH,kBAAkB,CAAC,IAAI,CAAC;YACpB,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,CAAC;SACX,CAAC,CAAC;QAEH,WAAW,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACxC,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAChD,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEnD,MAAM,YAAY,GAAG,IAAI,SAAS,CAAC,cAAc,EAAE,iBAAiB,EAAE,EAAE,EAAE,SAAS,CAAC,mBAAmB,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAE/I,MAAM,mBAAmB,GAAG,EAAE,CAAC;QAC/B,mBAAmB,CAAC,IAAI,CAAC;YACrB,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,CAAC;SACX,CAAC,CAAC;QACH,mBAAmB,CAAC,IAAI,CAAC;YACrB,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,EAAE;SACZ,CAAC,CAAC;QACH,mBAAmB,CAAC,IAAI,CAAC;YACrB,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,CAAC;SACX,CAAC,CAAC;QAEH,YAAY,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAC1C,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjD,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEpD,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,cAAc,GAAG,CAAC,CAAC;QACtE,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,eAAe,GAAG,CAAC,CAAC;QACvE,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IACrE,CAAC;IAeD;;;OAGG;IACI,cAAc,CAAC,QAAiB;QACnC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,YAAY,UAAU,CAAC,EAAE;YAC/C,OAAO;SACV;QACD,yGAAyG;QACzG,yCAAyC;QAEzC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACvC,iDAAiD;QACjD,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,QAAQ;SACX;aAAM;YACH,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC;SAChD;QAED,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEjE,iBAAiB;QACjB,MAAM,GAAG,GAAG,EAAE,CAAC;QACf,IAAI,UAAU,EAAE,SAAS,CAAC;QAC1B,IAAI,IAAI,CAAC,kBAAkB,IAAI,kBAAkB,CAAC,+BAA+B,EAAE;YAC/E,SAAS,GAAG,GAAG,CAAC;YAChB,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAClF,UAAU,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SAChD;aAAM;YACH,sDAAsD;YACtD,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;YAC/D,UAAU,GAAG,CAAC,CAAC;SAClB;QAED,kEAAkE;QAClE,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,EAAE,CAAC;QACrC,MAAM,4BAA4B,GAAG,IAAI,SAAS,CAAC,8BAA8B,EAAE,UAAU,EAAE,GAAG,EAAE,SAAS,CAAC,qBAAqB,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAC3K,MAAM,gCAAgC,GAAG;YACrC;gBACI,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ;aACvC;YACD;gBACI,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,IAAI,CAAC,cAAc;aAC7B;SACJ,CAAC;QAEF,4BAA4B,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACvE,4BAA4B,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC1E,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAEnE,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG,EAAE,CAAC;QAEtC,kDAAkD;QAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;QAE3C,MAAM,WAAW,GAAG,IAAI,SAAS,CAAC,aAAa,EAAE,gBAAgB,EAAE,GAAG,EAAE,SAAS,CAAC,mBAAmB,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAE7I,MAAM,kBAAkB,GAAG,EAAE,CAAC;QAC9B,kBAAkB,CAAC,IAAI,CAAC;YACpB,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,CAAC;SACX,CAAC,CAAC;QACH,kBAAkB,CAAC,IAAI,CAAC;YACpB,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,CAAC;SACX,CAAC,CAAC;QACH,kBAAkB,CAAC,IAAI,CAAC;YACpB,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,CAAC;SACX,CAAC,CAAC;QAEH,WAAW,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACxC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEnD,MAAM,YAAY,GAAG,IAAI,SAAS,CAAC,cAAc,EAAE,iBAAiB,EAAE,GAAG,EAAE,SAAS,CAAC,mBAAmB,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAEhJ,MAAM,mBAAmB,GAAG,EAAE,CAAC;QAC/B,mBAAmB,CAAC,IAAI,CAAC;YACrB,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,CAAC;SACX,CAAC,CAAC;QACH,mBAAmB,CAAC,IAAI,CAAC;YACrB,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,EAAE;SACZ,CAAC,CAAC;QACH,mBAAmB,CAAC,IAAI,CAAC;YACrB,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,CAAC;SACX,CAAC,CAAC;QAEH,YAAY,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAC1C,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEpD,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,cAAc,GAAG,CAAC,CAAC;QACtE,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,eAAe,GAAG,CAAC,CAAC;QAEvE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE;YACnF,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,KAAa,EAAE,cAAsB,IAAI,CAAC,iBAAiB;QAC5E,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;IACzC,CAAC;IAED;;;OAGG;IACI,qBAAqB,CAAC,WAAoB,IAAI;QACjD,QAAQ;IACZ,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,KAAa,EAAE,cAAsB,IAAI,CAAC,gBAAgB;QAC1E,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,MAAc;QAClC,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClC,OAAO;SACV;IACL,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,KAAa;QAChC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,OAAO;SACV;QACD,IAAI,CAAoB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAS,EAAE;YAC9D,OAAO;SACV;QACkB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAS,CAAC,aAAa,GAAG,KAAK,CAAC;IACtF,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,EAAE,CAAC;SACjB;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,CAAC;SAC7C;QACD,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACnE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC1C;QAED,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC5F,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,CAAC;SAC3C;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;SAC/B;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;SACvC;QAED,IAAI,IAAI,CAAC,EAAE,EAAE;YACT,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC;SACrB;QAED,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;QAEvC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACzD,MAAM,CAAC,mBAAmB,CAAC,wBAAwB,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAEzF,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACrD,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAE3E,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,4BAA4B,CAAC,cAAc,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAEpG,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC3D,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,oBAAoB,CAAC;IAChC,CAAC;;AA1ND;;GAEG;AACoB,iDAA8B,GAAG,CAAC,AAAJ,CAAK;AAC1D;;GAEG;AACoB,kDAA+B,GAAG,CAAC,AAAJ,CAAK", "sourcesContent": ["import { Logger } from \"../../Misc/logger\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport { FreeCamera } from \"../../Cameras/freeCamera\";\r\nimport { TargetCamera } from \"../../Cameras/targetCamera\";\r\nimport { DeviceOrientationCamera } from \"../../Cameras/deviceOrientationCamera\";\r\nimport { VRDeviceOrientationFreeCamera } from \"../../Cameras/VR/vrDeviceOrientationFreeCamera\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport type { Scene, IDisposable } from \"../../scene\";\r\nimport { Quaternion, Matrix, Vector3 } from \"../../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../../Maths/math.color\";\r\nimport type { StickValues } from \"../../Gamepads/gamepad\";\r\nimport { Gamepad } from \"../../Gamepads/gamepad\";\r\nimport type { Xbox360Pad } from \"../../Gamepads/xboxGamepad\";\r\nimport { Xbox360Button } from \"../../Gamepads/xboxGamepad\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { PickingInfo } from \"../../Collisions/pickingInfo\";\r\nimport { Ray } from \"../../Culling/ray\";\r\nimport { ImageProcessingConfiguration } from \"../../Materials/imageProcessingConfiguration\";\r\nimport { StandardMaterial } from \"../../Materials/standardMaterial\";\r\nimport { DynamicTexture } from \"../../Materials/Textures/dynamicTexture\";\r\nimport type { ImageProcessingPostProcess } from \"../../PostProcesses/imageProcessingPostProcess\";\r\nimport { SineEase, EasingFunction, CircleEase } from \"../../Animations/easing\";\r\nimport { Animation } from \"../../Animations/animation\";\r\nimport type { VRCameraMetrics } from \"../../Cameras/VR/vrCameraMetrics\";\r\nimport \"../../Gamepads/gamepadSceneComponent\";\r\nimport \"../../Animations/animatable\";\r\nimport { WebXRSessionManager } from \"../../XR/webXRSessionManager\";\r\nimport type { WebXRDefaultExperience } from \"../../XR/webXRDefaultExperience\";\r\nimport { WebXRState } from \"../../XR/webXRTypes\";\r\nimport { CreateGround } from \"../../Meshes/Builders/groundBuilder\";\r\nimport type { IPointerEvent } from \"../../Events/deviceInputEvents\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport { CreateTorus } from \"core/Meshes/Builders/torusBuilder\";\r\n\r\n/**\r\n * Options to modify the vr teleportation behavior.\r\n */\r\nexport interface VRTeleportationOptions {\r\n    /**\r\n     * The name of the mesh which should be used as the teleportation floor. (default: null)\r\n     */\r\n    floorMeshName?: string;\r\n    /**\r\n     * A list of meshes to be used as the teleportation floor. (default: empty)\r\n     */\r\n    floorMeshes?: Mesh[];\r\n    /**\r\n     * The teleportation mode. (default: TELEPORTATIONMODE_CONSTANTTIME)\r\n     */\r\n    teleportationMode?: number;\r\n    /**\r\n     * The duration of the animation in ms, apply when animationMode is TELEPORTATIONMODE_CONSTANTTIME. (default 122ms)\r\n     */\r\n    teleportationTime?: number;\r\n    /**\r\n     * The speed of the animation in distance/sec, apply when animationMode is TELEPORTATIONMODE_CONSTANTSPEED. (default 20 units / sec)\r\n     */\r\n    teleportationSpeed?: number;\r\n    /**\r\n     * The easing function used in the animation or null for Linear. (default CircleEase)\r\n     */\r\n    easingFunction?: EasingFunction;\r\n}\r\n\r\n/**\r\n * Options to modify the vr experience helper's behavior.\r\n */\r\nexport interface VRExperienceHelperOptions {\r\n    /**\r\n     * Create a DeviceOrientationCamera to be used as your out of vr camera. (default: true)\r\n     */\r\n    createDeviceOrientationCamera?: boolean;\r\n    /**\r\n     * Create a VRDeviceOrientationFreeCamera to be used for VR when no external HMD is found. (default: true)\r\n     */\r\n    createFallbackVRDeviceOrientationFreeCamera?: boolean;\r\n    /**\r\n     * Uses the main button on the controller to toggle the laser casted. (default: true)\r\n     */\r\n    laserToggle?: boolean;\r\n    /**\r\n     * A list of meshes to be used as the teleportation floor. If specified, teleportation will be enabled (default: undefined)\r\n     */\r\n    floorMeshes?: Mesh[];\r\n    /**\r\n     * Distortion metrics for the fallback vrDeviceOrientationCamera (default: VRCameraMetrics.Default)\r\n     */\r\n    vrDeviceOrientationCameraMetrics?: VRCameraMetrics;\r\n    /**\r\n     * Defines if WebXR should be used (if available)\r\n     */\r\n    useXR?: boolean;\r\n}\r\n\r\nclass VRExperienceHelperGazer implements IDisposable {\r\n    /** @internal */\r\n    public _gazeTracker: Mesh;\r\n\r\n    /** @internal */\r\n    public _currentMeshSelected: Nullable<AbstractMesh>;\r\n    /** @internal */\r\n    public _currentHit: Nullable<PickingInfo>;\r\n\r\n    public static _IdCounter = 0;\r\n    /** @internal */\r\n    public _id: number;\r\n\r\n    /** @internal */\r\n    public _pointerDownOnMeshAsked: boolean = false;\r\n    /** @internal */\r\n    public _isActionableMesh: boolean = false;\r\n\r\n    /** @internal */\r\n    public _interactionsEnabled: boolean;\r\n    /** @internal */\r\n    public _teleportationEnabled: boolean;\r\n    /** @internal */\r\n    public _teleportationRequestInitiated = false;\r\n    /** @internal */\r\n    public _teleportationBackRequestInitiated = false;\r\n    /** @internal */\r\n    public _rotationRightAsked = false;\r\n    /** @internal */\r\n    public _rotationLeftAsked = false;\r\n    /** @internal */\r\n    public _dpadPressed = true;\r\n\r\n    /** @internal */\r\n    public _activePointer = false;\r\n\r\n    constructor(\r\n        public scene: Scene,\r\n        gazeTrackerToClone: Nullable<Mesh> = null\r\n    ) {\r\n        this._id = VRExperienceHelperGazer._IdCounter++;\r\n\r\n        // Gaze tracker\r\n        if (!gazeTrackerToClone) {\r\n            this._gazeTracker = CreateTorus(\r\n                \"gazeTracker\",\r\n                {\r\n                    diameter: 0.0035,\r\n                    thickness: 0.0025,\r\n                    tessellation: 20,\r\n                    updatable: false,\r\n                },\r\n                scene\r\n            );\r\n            this._gazeTracker.bakeCurrentTransformIntoVertices();\r\n            this._gazeTracker.isPickable = false;\r\n            this._gazeTracker.isVisible = false;\r\n            const targetMat = new StandardMaterial(\"targetMat\", scene);\r\n            targetMat.specularColor = Color3.Black();\r\n            targetMat.emissiveColor = new Color3(0.7, 0.7, 0.7);\r\n            targetMat.backFaceCulling = false;\r\n            this._gazeTracker.material = targetMat;\r\n        } else {\r\n            this._gazeTracker = gazeTrackerToClone.clone(\"gazeTracker\") as Mesh;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getForwardRay(length: number): Ray {\r\n        return new Ray(Vector3.Zero(), new Vector3(0, 0, length));\r\n    }\r\n\r\n    /** @internal */\r\n    public _selectionPointerDown() {\r\n        this._pointerDownOnMeshAsked = true;\r\n        if (this._currentHit) {\r\n            this.scene.simulatePointerDown(this._currentHit, { pointerId: this._id });\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _selectionPointerUp() {\r\n        if (this._currentHit) {\r\n            this.scene.simulatePointerUp(this._currentHit, { pointerId: this._id });\r\n        }\r\n        this._pointerDownOnMeshAsked = false;\r\n    }\r\n\r\n    /** @internal */\r\n    public _activatePointer() {\r\n        this._activePointer = true;\r\n    }\r\n\r\n    /** @internal */\r\n    public _deactivatePointer() {\r\n        this._activePointer = false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public _updatePointerDistance(distance: number = 100) {}\r\n\r\n    public dispose() {\r\n        this._interactionsEnabled = false;\r\n        this._teleportationEnabled = false;\r\n        if (this._gazeTracker) {\r\n            this._gazeTracker.dispose();\r\n        }\r\n    }\r\n}\r\n\r\nclass VRExperienceHelperCameraGazer extends VRExperienceHelperGazer {\r\n    constructor(\r\n        private _getCamera: () => Nullable<Camera>,\r\n        scene: Scene\r\n    ) {\r\n        super(scene);\r\n    }\r\n\r\n    _getForwardRay(length: number): Ray {\r\n        const camera = this._getCamera();\r\n        if (camera) {\r\n            return camera.getForwardRay(length);\r\n        } else {\r\n            return new Ray(Vector3.Zero(), Vector3.Forward());\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * Event containing information after VR has been entered\r\n */\r\nexport class OnAfterEnteringVRObservableEvent {\r\n    /**\r\n     * If entering vr was successful\r\n     */\r\n    public success: boolean;\r\n}\r\n\r\n/**\r\n * Helps to quickly add VR support to an existing scene.\r\n * See https://doc.babylonjs.com/features/featuresDeepDive/cameras/webVRHelper\r\n * @deprecated Use WebXR instead!\r\n */\r\nexport class VRExperienceHelper {\r\n    private _scene: Scene;\r\n    private _position: Vector3;\r\n    private _btnVR: Nullable<HTMLButtonElement>;\r\n    private _btnVRDisplayed: boolean;\r\n\r\n    // Have we entered VR? (this is the VRExperienceHelper state)\r\n    private _hasEnteredVR: boolean;\r\n\r\n    // Are we presenting in the fullscreen fallback?\r\n    private _fullscreenVRpresenting = false;\r\n\r\n    private _inputElement: Nullable<HTMLElement>;\r\n    private _vrDeviceOrientationCamera: Nullable<VRDeviceOrientationFreeCamera>;\r\n    private _deviceOrientationCamera: Nullable<DeviceOrientationCamera>;\r\n    private _existingCamera: Camera;\r\n\r\n    private _onKeyDown: (event: KeyboardEvent) => void;\r\n    private _onVrDisplayPresentChangeBind: any;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that gaze can be enabled even if pointer lock is not engage (useful on iOS where fullscreen mode and pointer lock are not supported)\r\n     */\r\n    public enableGazeEvenWhenNoPointerLock = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that the VREXperienceHelper will exit VR if double tap is detected\r\n     */\r\n    public exitVROnDoubleTap = true;\r\n\r\n    /**\r\n     * Observable raised right before entering VR.\r\n     */\r\n    public onEnteringVRObservable = new Observable<VRExperienceHelper>();\r\n\r\n    /**\r\n     * Observable raised when entering VR has completed.\r\n     */\r\n    public onAfterEnteringVRObservable = new Observable<OnAfterEnteringVRObservableEvent>();\r\n\r\n    /**\r\n     * Observable raised when exiting VR.\r\n     */\r\n    public onExitingVRObservable = new Observable<VRExperienceHelper>();\r\n\r\n    /** Return this.onEnteringVRObservable\r\n     * Note: This one is for backward compatibility. Please use onEnteringVRObservable directly\r\n     */\r\n    public get onEnteringVR(): Observable<VRExperienceHelper> {\r\n        return this.onEnteringVRObservable;\r\n    }\r\n\r\n    /** Return this.onExitingVRObservable\r\n     * Note: This one is for backward compatibility. Please use onExitingVRObservable directly\r\n     */\r\n    public get onExitingVR(): Observable<VRExperienceHelper> {\r\n        return this.onExitingVRObservable;\r\n    }\r\n\r\n    private _useCustomVRButton: boolean = false;\r\n    private _teleportActive = false;\r\n    private _floorMeshName: string;\r\n    private _floorMeshesCollection: Mesh[] = [];\r\n    private _teleportationMode: number = VRExperienceHelper.TELEPORTATIONMODE_CONSTANTTIME;\r\n    private _teleportationTime: number = 122;\r\n    private _teleportationSpeed: number = 20;\r\n    private _teleportationEasing: EasingFunction;\r\n    private _rotationAllowed: boolean = true;\r\n    private _teleportBackwardsVector = new Vector3(0, -1, -1);\r\n    private _teleportationTarget: Mesh;\r\n    private _isDefaultTeleportationTarget = true;\r\n    private _postProcessMove: ImageProcessingPostProcess;\r\n    private _teleportationFillColor: string = \"#444444\";\r\n    private _teleportationBorderColor: string = \"#FFFFFF\";\r\n    private _rotationAngle: number = 0;\r\n    private _haloCenter = new Vector3(0, 0, 0);\r\n    private _cameraGazer: VRExperienceHelperCameraGazer;\r\n    private _padSensibilityUp = 0.65;\r\n    private _padSensibilityDown = 0.35;\r\n\r\n    private _pickedLaserColor: Color3 = new Color3(0.2, 0.2, 1);\r\n    private _pickedGazeColor: Color3 = new Color3(0, 0, 1);\r\n\r\n    /**\r\n     * Observable raised when a new mesh is selected based on meshSelectionPredicate\r\n     */\r\n    public onNewMeshSelected = new Observable<AbstractMesh>();\r\n\r\n    /**\r\n     * Observable raised when a new mesh is picked based on meshSelectionPredicate\r\n     */\r\n    public onNewMeshPicked = new Observable<PickingInfo>();\r\n\r\n    private _circleEase: CircleEase;\r\n\r\n    /**\r\n     * Observable raised before camera teleportation\r\n     */\r\n    public onBeforeCameraTeleport = new Observable<Vector3>();\r\n\r\n    /**\r\n     *  Observable raised after camera teleportation\r\n     */\r\n    public onAfterCameraTeleport = new Observable<Vector3>();\r\n\r\n    /**\r\n     * Observable raised when current selected mesh gets unselected\r\n     */\r\n    public onSelectedMeshUnselected = new Observable<AbstractMesh>();\r\n\r\n    private _raySelectionPredicate: (mesh: AbstractMesh) => boolean;\r\n\r\n    /**\r\n     * To be optionally changed by user to define custom ray selection\r\n     */\r\n    public raySelectionPredicate: (mesh: AbstractMesh) => boolean;\r\n\r\n    /**\r\n     * To be optionally changed by user to define custom selection logic (after ray selection)\r\n     */\r\n    public meshSelectionPredicate: (mesh: AbstractMesh) => boolean;\r\n\r\n    /**\r\n     * Set teleportation enabled. If set to false camera teleportation will be disabled but camera rotation will be kept.\r\n     */\r\n    public teleportationEnabled: boolean = true;\r\n\r\n    private _defaultHeight: number;\r\n    private _teleportationInitialized = false;\r\n    private _interactionsEnabled = false;\r\n    private _displayGaze = true;\r\n    private _displayLaserPointer = true;\r\n\r\n    /**\r\n     * The mesh used to display where the user is going to teleport.\r\n     */\r\n    public get teleportationTarget(): Mesh {\r\n        return this._teleportationTarget;\r\n    }\r\n\r\n    /**\r\n     * Sets the mesh to be used to display where the user is going to teleport.\r\n     */\r\n    public set teleportationTarget(value: Mesh) {\r\n        if (value) {\r\n            value.name = \"teleportationTarget\";\r\n            this._isDefaultTeleportationTarget = false;\r\n            this._teleportationTarget = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * The mesh used to display where the user is selecting, this mesh will be cloned and set as the gazeTracker for the left and right controller\r\n     * when set bakeCurrentTransformIntoVertices will be called on the mesh.\r\n     * See https://doc.babylonjs.com/features/featuresDeepDive/mesh/transforms/center_origin/bakingTransforms\r\n     */\r\n    public get gazeTrackerMesh(): Mesh {\r\n        return this._cameraGazer._gazeTracker;\r\n    }\r\n\r\n    public set gazeTrackerMesh(value: Mesh) {\r\n        if (value) {\r\n            // Dispose of existing meshes\r\n            if (this._cameraGazer._gazeTracker) {\r\n                this._cameraGazer._gazeTracker.dispose();\r\n            }\r\n\r\n            // Set and create gaze trackers on head and controllers\r\n            this._cameraGazer._gazeTracker = value;\r\n            this._cameraGazer._gazeTracker.bakeCurrentTransformIntoVertices();\r\n            this._cameraGazer._gazeTracker.isPickable = false;\r\n            this._cameraGazer._gazeTracker.isVisible = false;\r\n            this._cameraGazer._gazeTracker.name = \"gazeTracker\";\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If the gaze trackers scale should be updated to be constant size when pointing at near/far meshes\r\n     */\r\n    public updateGazeTrackerScale = true;\r\n    /**\r\n     * If the gaze trackers color should be updated when selecting meshes\r\n     */\r\n    public updateGazeTrackerColor = true;\r\n    /**\r\n     * If the controller laser color should be updated when selecting meshes\r\n     */\r\n    public updateControllerLaserColor = true;\r\n\r\n    /**\r\n     * If the ray of the gaze should be displayed.\r\n     */\r\n    public get displayGaze(): boolean {\r\n        return this._displayGaze;\r\n    }\r\n\r\n    /**\r\n     * Sets if the ray of the gaze should be displayed.\r\n     */\r\n    public set displayGaze(value: boolean) {\r\n        this._displayGaze = value;\r\n        if (!value) {\r\n            this._cameraGazer._gazeTracker.isVisible = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If the ray of the LaserPointer should be displayed.\r\n     */\r\n    public get displayLaserPointer(): boolean {\r\n        return this._displayLaserPointer;\r\n    }\r\n\r\n    /**\r\n     * Sets if the ray of the LaserPointer should be displayed.\r\n     */\r\n    public set displayLaserPointer(value: boolean) {\r\n        this._displayLaserPointer = value;\r\n    }\r\n\r\n    /**\r\n     * The deviceOrientationCamera used as the camera when not in VR.\r\n     */\r\n    public get deviceOrientationCamera(): Nullable<DeviceOrientationCamera> {\r\n        return this._deviceOrientationCamera;\r\n    }\r\n\r\n    /**\r\n     * Based on the current WebVR support, returns the current VR camera used.\r\n     */\r\n    public get currentVRCamera(): Nullable<Camera> {\r\n        return this._scene.activeCamera;\r\n    }\r\n\r\n    /**\r\n     * The deviceOrientationCamera that is used as a fallback when vr device is not connected.\r\n     */\r\n    public get vrDeviceOrientationCamera(): Nullable<VRDeviceOrientationFreeCamera> {\r\n        return this._vrDeviceOrientationCamera;\r\n    }\r\n\r\n    /**\r\n     * The html button that is used to trigger entering into VR.\r\n     */\r\n    public get vrButton(): Nullable<HTMLButtonElement> {\r\n        return this._btnVR;\r\n    }\r\n\r\n    private get _teleportationRequestInitiated(): boolean {\r\n        return this._cameraGazer._teleportationRequestInitiated;\r\n    }\r\n\r\n    /**\r\n     * Defines whether or not Pointer lock should be requested when switching to\r\n     * full screen.\r\n     */\r\n    public requestPointerLockOnFullScreen = true;\r\n\r\n    // XR\r\n\r\n    /**\r\n     * If asking to force XR, this will be populated with the default xr experience\r\n     */\r\n    public xr: WebXRDefaultExperience;\r\n\r\n    /**\r\n     * Was the XR test done already. If this is true AND this.xr exists, xr is initialized.\r\n     * If this is true and no this.xr, xr exists but is not supported, using WebVR.\r\n     */\r\n    public xrTestDone: boolean = false;\r\n\r\n    /**\r\n     * Instantiates a VRExperienceHelper.\r\n     * Helps to quickly add VR support to an existing scene.\r\n     * @param scene The scene the VRExperienceHelper belongs to.\r\n     * @param webVROptions Options to modify the vr experience helper's behavior.\r\n     */\r\n    constructor(\r\n        scene: Scene,\r\n        /** Options to modify the vr experience helper's behavior. */\r\n        public webVROptions: VRExperienceHelperOptions = {}\r\n    ) {\r\n        Logger.Warn(\"WebVR is deprecated. Please avoid using this experience helper and use the WebXR experience helper instead\");\r\n        this._scene = scene;\r\n        this._inputElement = scene.getEngine().getInputElement();\r\n\r\n        // check for VR support:\r\n\r\n        const vrSupported = \"getVRDisplays\" in navigator;\r\n        // no VR support? force XR but only when it is not set because web vr can work without the getVRDisplays\r\n        if (!vrSupported && webVROptions.useXR === undefined) {\r\n            webVROptions.useXR = true;\r\n        }\r\n\r\n        // Parse options\r\n        if (webVROptions.createFallbackVRDeviceOrientationFreeCamera === undefined) {\r\n            webVROptions.createFallbackVRDeviceOrientationFreeCamera = true;\r\n        }\r\n        if (webVROptions.createDeviceOrientationCamera === undefined) {\r\n            webVROptions.createDeviceOrientationCamera = true;\r\n        }\r\n        if (webVROptions.laserToggle === undefined) {\r\n            webVROptions.laserToggle = true;\r\n        }\r\n\r\n        this._hasEnteredVR = false;\r\n\r\n        // Set position\r\n        if (this._scene.activeCamera) {\r\n            this._position = this._scene.activeCamera.position.clone();\r\n        } else {\r\n            this._position = new Vector3(0, this._defaultHeight, 0);\r\n        }\r\n\r\n        // Set non-vr camera\r\n        if (webVROptions.createDeviceOrientationCamera || !this._scene.activeCamera) {\r\n            this._deviceOrientationCamera = new DeviceOrientationCamera(\"deviceOrientationVRHelper\", this._position.clone(), scene);\r\n\r\n            // Copy data from existing camera\r\n            if (this._scene.activeCamera) {\r\n                this._deviceOrientationCamera.minZ = this._scene.activeCamera.minZ;\r\n                this._deviceOrientationCamera.maxZ = this._scene.activeCamera.maxZ;\r\n                // Set rotation from previous camera\r\n                if (this._scene.activeCamera instanceof TargetCamera && this._scene.activeCamera.rotation) {\r\n                    const targetCamera = this._scene.activeCamera;\r\n                    if (targetCamera.rotationQuaternion) {\r\n                        this._deviceOrientationCamera.rotationQuaternion.copyFrom(targetCamera.rotationQuaternion);\r\n                    } else {\r\n                        this._deviceOrientationCamera.rotationQuaternion.copyFrom(\r\n                            Quaternion.RotationYawPitchRoll(targetCamera.rotation.y, targetCamera.rotation.x, targetCamera.rotation.z)\r\n                        );\r\n                    }\r\n                    this._deviceOrientationCamera.rotation = targetCamera.rotation.clone();\r\n                }\r\n            }\r\n\r\n            this._scene.activeCamera = this._deviceOrientationCamera;\r\n            if (this._inputElement) {\r\n                this._scene.activeCamera.attachControl();\r\n            }\r\n        } else {\r\n            this._existingCamera = this._scene.activeCamera;\r\n        }\r\n\r\n        if (this.webVROptions.useXR && (navigator as any).xr) {\r\n            // force-check XR session support\r\n            WebXRSessionManager.IsSessionSupportedAsync(\"immersive-vr\").then((supported) => {\r\n                if (supported) {\r\n                    Logger.Log(\"Using WebXR. It is recommended to use the WebXRDefaultExperience directly\");\r\n                    // it is possible to use XR, let's do it!\r\n                    scene\r\n                        .createDefaultXRExperienceAsync({\r\n                            floorMeshes: webVROptions.floorMeshes || [],\r\n                        })\r\n                        .then((xr) => {\r\n                            this.xr = xr;\r\n                            // connect observables\r\n                            this.xrTestDone = true;\r\n\r\n                            this._cameraGazer = new VRExperienceHelperCameraGazer(() => {\r\n                                return this.xr.baseExperience.camera;\r\n                            }, scene);\r\n\r\n                            this.xr.baseExperience.onStateChangedObservable.add((state) => {\r\n                                // support for entering / exiting\r\n                                switch (state) {\r\n                                    case WebXRState.ENTERING_XR:\r\n                                        this.onEnteringVRObservable.notifyObservers(this);\r\n                                        if (!this._interactionsEnabled) {\r\n                                            this.xr.pointerSelection.detach();\r\n                                        }\r\n                                        this.xr.pointerSelection.displayLaserPointer = this._displayLaserPointer;\r\n                                        break;\r\n                                    case WebXRState.EXITING_XR:\r\n                                        this.onExitingVRObservable.notifyObservers(this);\r\n\r\n                                        // resize to update width and height when exiting vr exits fullscreen\r\n                                        this._scene.getEngine().resize();\r\n                                        break;\r\n                                    case WebXRState.IN_XR:\r\n                                        this._hasEnteredVR = true;\r\n                                        break;\r\n                                    case WebXRState.NOT_IN_XR:\r\n                                        this._hasEnteredVR = false;\r\n                                        break;\r\n                                }\r\n                            });\r\n                        });\r\n                } else {\r\n                    // XR not supported (thou exists), continue WebVR init\r\n                    this._completeVRInit(scene, webVROptions);\r\n                }\r\n            });\r\n        } else {\r\n            // no XR, continue init synchronous\r\n            this._completeVRInit(scene, webVROptions);\r\n        }\r\n    }\r\n\r\n    private _completeVRInit(scene: Scene, webVROptions: VRExperienceHelperOptions): void {\r\n        this.xrTestDone = true;\r\n        // Create VR cameras\r\n        if (webVROptions.createFallbackVRDeviceOrientationFreeCamera) {\r\n            this._vrDeviceOrientationCamera = new VRDeviceOrientationFreeCamera(\r\n                \"VRDeviceOrientationVRHelper\",\r\n                this._position,\r\n                this._scene,\r\n                true,\r\n                webVROptions.vrDeviceOrientationCameraMetrics\r\n            );\r\n            this._vrDeviceOrientationCamera.angularSensibility = Number.MAX_VALUE;\r\n        }\r\n\r\n        this._cameraGazer = new VRExperienceHelperCameraGazer(() => {\r\n            return this.currentVRCamera;\r\n        }, scene);\r\n        // Create default button\r\n        if (!this._useCustomVRButton) {\r\n            this._btnVR = <HTMLButtonElement>document.createElement(\"BUTTON\");\r\n            this._btnVR.className = \"babylonVRicon\";\r\n            this._btnVR.id = \"babylonVRiconbtn\";\r\n            this._btnVR.title = \"Click to switch to VR\";\r\n            const url = !window.SVGSVGElement\r\n                ? \"https://cdn.babylonjs.com/Assets/vrButton.png\"\r\n                : \"data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%222048%22%20height%3D%221152%22%20viewBox%3D%220%200%202048%201152%22%20version%3D%221.1%22%3E%3Cpath%20transform%3D%22rotate%28180%201024%2C576.0000000000001%29%22%20d%3D%22m1109%2C896q17%2C0%2030%2C-12t13%2C-30t-12.5%2C-30.5t-30.5%2C-12.5l-170%2C0q-18%2C0%20-30.5%2C12.5t-12.5%2C30.5t13%2C30t30%2C12l170%2C0zm-85%2C256q59%2C0%20132.5%2C-1.5t154.5%2C-5.5t164.5%2C-11.5t163%2C-20t150%2C-30t124.5%2C-41.5q23%2C-11%2042%2C-24t38%2C-30q27%2C-25%2041%2C-61.5t14%2C-72.5l0%2C-257q0%2C-123%20-47%2C-232t-128%2C-190t-190%2C-128t-232%2C-47l-81%2C0q-37%2C0%20-68.5%2C14t-60.5%2C34.5t-55.5%2C45t-53%2C45t-53%2C34.5t-55.5%2C14t-55.5%2C-14t-53%2C-34.5t-53%2C-45t-55.5%2C-45t-60.5%2C-34.5t-68.5%2C-14l-81%2C0q-123%2C0%20-232%2C47t-190%2C128t-128%2C190t-47%2C232l0%2C257q0%2C68%2038%2C115t97%2C73q54%2C24%20124.5%2C41.5t150%2C30t163%2C20t164.5%2C11.5t154.5%2C5.5t132.5%2C1.5zm939%2C-298q0%2C39%20-24.5%2C67t-58.5%2C42q-54%2C23%20-122%2C39.5t-143.5%2C28t-155.5%2C19t-157%2C11t-148.5%2C5t-129.5%2C1.5q-59%2C0%20-130%2C-1.5t-148%2C-5t-157%2C-11t-155.5%2C-19t-143.5%2C-28t-122%2C-39.5q-34%2C-14%20-58.5%2C-42t-24.5%2C-67l0%2C-257q0%2C-106%2040.5%2C-199t110%2C-162.5t162.5%2C-109.5t199%2C-40l81%2C0q27%2C0%2052%2C14t50%2C34.5t51%2C44.5t55.5%2C44.5t63.5%2C34.5t74%2C14t74%2C-14t63.5%2C-34.5t55.5%2C-44.5t51%2C-44.5t50%2C-34.5t52%2C-14l14%2C0q37%2C0%2070%2C0.5t64.5%2C4.5t63.5%2C12t68%2C23q71%2C30%20128.5%2C78.5t98.5%2C110t63.5%2C133.5t22.5%2C149l0%2C257z%22%20fill%3D%22white%22%20/%3E%3C/svg%3E%0A\";\r\n            let css =\r\n                \".babylonVRicon { position: absolute; right: 20px; height: 50px; width: 80px; background-color: rgba(51,51,51,0.7); background-image: url(\" +\r\n                url +\r\n                \"); background-size: 80%; background-repeat:no-repeat; background-position: center; border: none; outline: none; transition: transform 0.125s ease-out } .babylonVRicon:hover { transform: scale(1.05) } .babylonVRicon:active {background-color: rgba(51,51,51,1) } .babylonVRicon:focus {background-color: rgba(51,51,51,1) }\";\r\n            css += \".babylonVRicon.vrdisplaypresenting { display: none; }\";\r\n            // TODO: Add user feedback so that they know what state the VRDisplay is in (disconnected, connected, entering-VR)\r\n            // css += \".babylonVRicon.vrdisplaysupported { }\";\r\n            // css += \".babylonVRicon.vrdisplayready { }\";\r\n            // css += \".babylonVRicon.vrdisplayrequesting { }\";\r\n\r\n            const style = document.createElement(\"style\");\r\n            style.appendChild(document.createTextNode(css));\r\n            document.getElementsByTagName(\"head\")[0].appendChild(style);\r\n\r\n            this._moveButtonToBottomRight();\r\n        }\r\n\r\n        // VR button click event\r\n        if (this._btnVR) {\r\n            this._btnVR.addEventListener(\"click\", () => {\r\n                if (!this.isInVRMode) {\r\n                    this.enterVR();\r\n                }\r\n            });\r\n        }\r\n\r\n        // Window events\r\n\r\n        const hostWindow = this._scene.getEngine().getHostWindow();\r\n        if (!hostWindow) {\r\n            return;\r\n        }\r\n\r\n        hostWindow.addEventListener(\"resize\", this._onResize);\r\n        document.addEventListener(\"fullscreenchange\", this._onFullscreenChange, false);\r\n\r\n        // Display vr button when headset is connected\r\n        if (webVROptions.createFallbackVRDeviceOrientationFreeCamera) {\r\n            this._displayVRButton();\r\n        }\r\n\r\n        // Exiting VR mode using 'ESC' key on desktop\r\n        this._onKeyDown = (event: KeyboardEvent) => {\r\n            if (event.keyCode === 27 && this.isInVRMode) {\r\n                this.exitVR();\r\n            }\r\n        };\r\n        document.addEventListener(\"keydown\", this._onKeyDown);\r\n\r\n        // Exiting VR mode double tapping the touch screen\r\n        this._scene.onPrePointerObservable.add(\r\n            () => {\r\n                if (this._hasEnteredVR && this.exitVROnDoubleTap) {\r\n                    this.exitVR();\r\n                    if (this._fullscreenVRpresenting) {\r\n                        this._scene.getEngine().exitFullscreen();\r\n                    }\r\n                }\r\n            },\r\n            PointerEventTypes.POINTERDOUBLETAP,\r\n            false\r\n        );\r\n\r\n        scene.onDisposeObservable.add(() => {\r\n            this.dispose();\r\n        });\r\n\r\n        this._updateButtonVisibility();\r\n\r\n        //create easing functions\r\n        this._circleEase = new CircleEase();\r\n        this._circleEase.setEasingMode(EasingFunction.EASINGMODE_EASEINOUT);\r\n        this._teleportationEasing = this._circleEase;\r\n\r\n        // Allow clicking in the vrDeviceOrientationCamera\r\n        scene.onPointerObservable.add((e) => {\r\n            if (this._interactionsEnabled) {\r\n                if (scene.activeCamera === this.vrDeviceOrientationCamera && (e.event as IPointerEvent).pointerType === \"mouse\") {\r\n                    if (e.type === PointerEventTypes.POINTERDOWN) {\r\n                        this._cameraGazer._selectionPointerDown();\r\n                    } else if (e.type === PointerEventTypes.POINTERUP) {\r\n                        this._cameraGazer._selectionPointerUp();\r\n                    }\r\n                }\r\n            }\r\n        });\r\n\r\n        if (this.webVROptions.floorMeshes) {\r\n            this.enableTeleportation({ floorMeshes: this.webVROptions.floorMeshes });\r\n        }\r\n    }\r\n\r\n    private _onResize = () => {\r\n        this._moveButtonToBottomRight();\r\n    };\r\n\r\n    private _onFullscreenChange = () => {\r\n        this._fullscreenVRpresenting = !!document.fullscreenElement;\r\n        if (!this._fullscreenVRpresenting && this._inputElement) {\r\n            this.exitVR();\r\n            if (!this._useCustomVRButton && this._btnVR) {\r\n                this._btnVR.style.top = this._inputElement.offsetTop + this._inputElement.offsetHeight - 70 + \"px\";\r\n                this._btnVR.style.left = this._inputElement.offsetLeft + this._inputElement.offsetWidth - 100 + \"px\";\r\n                // make sure the button is visible after setting its position\r\n                this._updateButtonVisibility();\r\n            }\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Gets a value indicating if we are currently in VR mode.\r\n     */\r\n    public get isInVRMode(): boolean {\r\n        return (this.xr && this.webVROptions.useXR && this.xr.baseExperience.state === WebXRState.IN_XR) || this._fullscreenVRpresenting;\r\n    }\r\n\r\n    private _moveButtonToBottomRight() {\r\n        if (this._inputElement && !this._useCustomVRButton && this._btnVR) {\r\n            const rect: ClientRect = this._inputElement.getBoundingClientRect();\r\n            this._btnVR.style.top = rect.top + rect.height - 70 + \"px\";\r\n            this._btnVR.style.left = rect.left + rect.width - 100 + \"px\";\r\n        }\r\n    }\r\n\r\n    private _displayVRButton() {\r\n        if (!this._useCustomVRButton && !this._btnVRDisplayed && this._btnVR) {\r\n            document.body.appendChild(this._btnVR);\r\n            this._btnVRDisplayed = true;\r\n        }\r\n    }\r\n\r\n    private _updateButtonVisibility() {\r\n        if (!this._btnVR || this._useCustomVRButton) {\r\n            return;\r\n        }\r\n        this._btnVR.className = \"babylonVRicon\";\r\n        if (this.isInVRMode) {\r\n            this._btnVR.className += \" vrdisplaypresenting\";\r\n        }\r\n    }\r\n\r\n    private _cachedAngularSensibility = { angularSensibilityX: null, angularSensibilityY: null, angularSensibility: null };\r\n    /**\r\n     * Attempt to enter VR. If a headset is connected and ready, will request present on that.\r\n     * Otherwise, will use the fullscreen API.\r\n     */\r\n    public enterVR() {\r\n        if (this.xr) {\r\n            this.xr.baseExperience.enterXRAsync(\"immersive-vr\", \"local-floor\", this.xr.renderTarget);\r\n            return;\r\n        }\r\n        if (this.onEnteringVRObservable) {\r\n            try {\r\n                this.onEnteringVRObservable.notifyObservers(this);\r\n            } catch (err) {\r\n                Logger.Warn(\"Error in your custom logic onEnteringVR: \" + err);\r\n            }\r\n        }\r\n\r\n        if (this._scene.activeCamera) {\r\n            this._position = this._scene.activeCamera.position.clone();\r\n\r\n            if (this.vrDeviceOrientationCamera) {\r\n                this.vrDeviceOrientationCamera.rotation = Quaternion.FromRotationMatrix(this._scene.activeCamera.getWorldMatrix().getRotationMatrix()).toEulerAngles();\r\n                this.vrDeviceOrientationCamera.angularSensibility = 2000;\r\n            }\r\n\r\n            // make sure that we return to the last active camera\r\n            this._existingCamera = this._scene.activeCamera;\r\n\r\n            // Remove and cache angular sensability to avoid camera rotation when in VR\r\n            if ((<any>this._existingCamera).angularSensibilityX) {\r\n                this._cachedAngularSensibility.angularSensibilityX = (<any>this._existingCamera).angularSensibilityX;\r\n                (<any>this._existingCamera).angularSensibilityX = Number.MAX_VALUE;\r\n            }\r\n            if ((<any>this._existingCamera).angularSensibilityY) {\r\n                this._cachedAngularSensibility.angularSensibilityY = (<any>this._existingCamera).angularSensibilityY;\r\n                (<any>this._existingCamera).angularSensibilityY = Number.MAX_VALUE;\r\n            }\r\n            if ((<any>this._existingCamera).angularSensibility) {\r\n                this._cachedAngularSensibility.angularSensibility = (<any>this._existingCamera).angularSensibility;\r\n                (<any>this._existingCamera).angularSensibility = Number.MAX_VALUE;\r\n            }\r\n        }\r\n\r\n        // If WebVR is supported and a headset is connected\r\n        if (this._vrDeviceOrientationCamera) {\r\n            this._vrDeviceOrientationCamera.position = this._position;\r\n            if (this._scene.activeCamera) {\r\n                this._vrDeviceOrientationCamera.minZ = this._scene.activeCamera.minZ;\r\n            }\r\n            this._scene.activeCamera = this._vrDeviceOrientationCamera;\r\n            this._scene.getEngine().enterFullscreen(this.requestPointerLockOnFullScreen);\r\n            this._updateButtonVisibility();\r\n            this._vrDeviceOrientationCamera.onViewMatrixChangedObservable.addOnce(() => {\r\n                this.onAfterEnteringVRObservable.notifyObservers({ success: true });\r\n            });\r\n        }\r\n\r\n        if (this._scene.activeCamera && this._inputElement) {\r\n            this._scene.activeCamera.attachControl();\r\n        }\r\n\r\n        if (this._interactionsEnabled) {\r\n            this._scene.registerBeforeRender(this._beforeRender);\r\n        }\r\n\r\n        this._hasEnteredVR = true;\r\n    }\r\n\r\n    /**\r\n     * Attempt to exit VR, or fullscreen.\r\n     */\r\n    public exitVR() {\r\n        if (this.xr) {\r\n            this.xr.baseExperience.exitXRAsync();\r\n            return;\r\n        }\r\n        if (this._hasEnteredVR) {\r\n            if (this.onExitingVRObservable) {\r\n                try {\r\n                    this.onExitingVRObservable.notifyObservers(this);\r\n                } catch (err) {\r\n                    Logger.Warn(\"Error in your custom logic onExitingVR: \" + err);\r\n                }\r\n            }\r\n            if (this._scene.activeCamera) {\r\n                this._position = this._scene.activeCamera.position.clone();\r\n            }\r\n\r\n            if (this.vrDeviceOrientationCamera) {\r\n                this.vrDeviceOrientationCamera.angularSensibility = Number.MAX_VALUE;\r\n            }\r\n\r\n            if (this._deviceOrientationCamera) {\r\n                this._deviceOrientationCamera.position = this._position;\r\n                this._scene.activeCamera = this._deviceOrientationCamera;\r\n\r\n                // Restore angular sensibility\r\n                if (this._cachedAngularSensibility.angularSensibilityX) {\r\n                    (<any>this._deviceOrientationCamera).angularSensibilityX = this._cachedAngularSensibility.angularSensibilityX;\r\n                    this._cachedAngularSensibility.angularSensibilityX = null;\r\n                }\r\n                if (this._cachedAngularSensibility.angularSensibilityY) {\r\n                    (<any>this._deviceOrientationCamera).angularSensibilityY = this._cachedAngularSensibility.angularSensibilityY;\r\n                    this._cachedAngularSensibility.angularSensibilityY = null;\r\n                }\r\n                if (this._cachedAngularSensibility.angularSensibility) {\r\n                    (<any>this._deviceOrientationCamera).angularSensibility = this._cachedAngularSensibility.angularSensibility;\r\n                    this._cachedAngularSensibility.angularSensibility = null;\r\n                }\r\n            } else if (this._existingCamera) {\r\n                this._existingCamera.position = this._position;\r\n                this._scene.activeCamera = this._existingCamera;\r\n                if (this._inputElement) {\r\n                    this._scene.activeCamera.attachControl();\r\n                }\r\n\r\n                // Restore angular sensibility\r\n                if (this._cachedAngularSensibility.angularSensibilityX) {\r\n                    (<any>this._existingCamera).angularSensibilityX = this._cachedAngularSensibility.angularSensibilityX;\r\n                    this._cachedAngularSensibility.angularSensibilityX = null;\r\n                }\r\n                if (this._cachedAngularSensibility.angularSensibilityY) {\r\n                    (<any>this._existingCamera).angularSensibilityY = this._cachedAngularSensibility.angularSensibilityY;\r\n                    this._cachedAngularSensibility.angularSensibilityY = null;\r\n                }\r\n                if (this._cachedAngularSensibility.angularSensibility) {\r\n                    (<any>this._existingCamera).angularSensibility = this._cachedAngularSensibility.angularSensibility;\r\n                    this._cachedAngularSensibility.angularSensibility = null;\r\n                }\r\n            }\r\n\r\n            this._updateButtonVisibility();\r\n\r\n            if (this._interactionsEnabled) {\r\n                this._scene.unregisterBeforeRender(this._beforeRender);\r\n                this._cameraGazer._gazeTracker.isVisible = false;\r\n            }\r\n\r\n            // resize to update width and height when exiting vr exits fullscreen\r\n            this._scene.getEngine().resize();\r\n\r\n            this._hasEnteredVR = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * The position of the vr experience helper.\r\n     */\r\n    public get position(): Vector3 {\r\n        return this._position;\r\n    }\r\n\r\n    /**\r\n     * Sets the position of the vr experience helper.\r\n     */\r\n    public set position(value: Vector3) {\r\n        this._position = value;\r\n\r\n        if (this._scene.activeCamera) {\r\n            this._scene.activeCamera.position = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enables controllers and user interactions such as selecting and object or clicking on an object.\r\n     */\r\n    public enableInteractions() {\r\n        if (!this._interactionsEnabled) {\r\n            // in XR it is enabled by default, but just to make sure, re-attach\r\n            if (this.xr) {\r\n                if (this.xr.baseExperience.state === WebXRState.IN_XR) {\r\n                    this.xr.pointerSelection.attach();\r\n                }\r\n                return;\r\n            }\r\n\r\n            this.raySelectionPredicate = (mesh) => {\r\n                return mesh.isVisible && (mesh.isPickable || mesh.name === this._floorMeshName);\r\n            };\r\n\r\n            this.meshSelectionPredicate = () => {\r\n                return true;\r\n            };\r\n\r\n            this._raySelectionPredicate = (mesh) => {\r\n                if (\r\n                    this._isTeleportationFloor(mesh) ||\r\n                    (mesh.name.indexOf(\"gazeTracker\") === -1 && mesh.name.indexOf(\"teleportationTarget\") === -1 && mesh.name.indexOf(\"torusTeleportation\") === -1)\r\n                ) {\r\n                    return this.raySelectionPredicate(mesh);\r\n                }\r\n                return false;\r\n            };\r\n\r\n            this._interactionsEnabled = true;\r\n        }\r\n    }\r\n\r\n    private _beforeRender = () => {\r\n        if (this._scene.getEngine().isPointerLock || this.enableGazeEvenWhenNoPointerLock) {\r\n            // no-op\r\n        } else {\r\n            this._cameraGazer._gazeTracker.isVisible = false;\r\n        }\r\n    };\r\n\r\n    private _isTeleportationFloor(mesh: AbstractMesh): boolean {\r\n        for (let i = 0; i < this._floorMeshesCollection.length; i++) {\r\n            if (this._floorMeshesCollection[i].id === mesh.id) {\r\n                return true;\r\n            }\r\n        }\r\n        if (this._floorMeshName && mesh.name === this._floorMeshName) {\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Adds a floor mesh to be used for teleportation.\r\n     * @param floorMesh the mesh to be used for teleportation.\r\n     */\r\n    public addFloorMesh(floorMesh: Mesh): void {\r\n        if (!this._floorMeshesCollection) {\r\n            return;\r\n        }\r\n\r\n        if (this._floorMeshesCollection.indexOf(floorMesh) > -1) {\r\n            return;\r\n        }\r\n\r\n        this._floorMeshesCollection.push(floorMesh);\r\n    }\r\n\r\n    /**\r\n     * Removes a floor mesh from being used for teleportation.\r\n     * @param floorMesh the mesh to be removed.\r\n     */\r\n    public removeFloorMesh(floorMesh: Mesh): void {\r\n        if (!this._floorMeshesCollection) {\r\n            return;\r\n        }\r\n\r\n        const meshIndex = this._floorMeshesCollection.indexOf(floorMesh);\r\n        if (meshIndex !== -1) {\r\n            this._floorMeshesCollection.splice(meshIndex, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enables interactions and teleportation using the VR controllers and gaze.\r\n     * @param vrTeleportationOptions options to modify teleportation behavior.\r\n     */\r\n    public enableTeleportation(vrTeleportationOptions: VRTeleportationOptions = {}) {\r\n        if (!this._teleportationInitialized) {\r\n            this.enableInteractions();\r\n\r\n            if (this.webVROptions.useXR && (vrTeleportationOptions.floorMeshes || vrTeleportationOptions.floorMeshName)) {\r\n                const floorMeshes: AbstractMesh[] = vrTeleportationOptions.floorMeshes || [];\r\n                if (!floorMeshes.length) {\r\n                    const floorMesh = this._scene.getMeshByName(vrTeleportationOptions.floorMeshName!);\r\n                    if (floorMesh) {\r\n                        floorMeshes.push(floorMesh);\r\n                    }\r\n                }\r\n                if (this.xr) {\r\n                    floorMeshes.forEach((mesh) => {\r\n                        this.xr.teleportation.addFloorMesh(mesh);\r\n                    });\r\n                    if (!this.xr.teleportation.attached) {\r\n                        this.xr.teleportation.attach();\r\n                    }\r\n                    return;\r\n                } else if (!this.xrTestDone) {\r\n                    const waitForXr = () => {\r\n                        if (this.xrTestDone) {\r\n                            this._scene.unregisterBeforeRender(waitForXr);\r\n                            if (this.xr) {\r\n                                if (!this.xr.teleportation.attached) {\r\n                                    this.xr.teleportation.attach();\r\n                                }\r\n                            } else {\r\n                                this.enableTeleportation(vrTeleportationOptions);\r\n                            }\r\n                        }\r\n                    };\r\n                    this._scene.registerBeforeRender(waitForXr);\r\n                    return;\r\n                }\r\n            }\r\n\r\n            if (vrTeleportationOptions.floorMeshName) {\r\n                this._floorMeshName = vrTeleportationOptions.floorMeshName;\r\n            }\r\n            if (vrTeleportationOptions.floorMeshes) {\r\n                this._floorMeshesCollection = vrTeleportationOptions.floorMeshes;\r\n            }\r\n\r\n            if (vrTeleportationOptions.teleportationMode) {\r\n                this._teleportationMode = vrTeleportationOptions.teleportationMode;\r\n            }\r\n            if (vrTeleportationOptions.teleportationTime && vrTeleportationOptions.teleportationTime > 0) {\r\n                this._teleportationTime = vrTeleportationOptions.teleportationTime;\r\n            }\r\n            if (vrTeleportationOptions.teleportationSpeed && vrTeleportationOptions.teleportationSpeed > 0) {\r\n                this._teleportationSpeed = vrTeleportationOptions.teleportationSpeed;\r\n            }\r\n            if (vrTeleportationOptions.easingFunction !== undefined) {\r\n                this._teleportationEasing = vrTeleportationOptions.easingFunction;\r\n            }\r\n\r\n            // Creates an image processing post process for the vignette not relying\r\n            // on the main scene configuration for image processing to reduce setup and spaces\r\n            // (gamma/linear) conflicts.\r\n            const imageProcessingConfiguration = new ImageProcessingConfiguration();\r\n            imageProcessingConfiguration.vignetteColor = new Color4(0, 0, 0, 0);\r\n            imageProcessingConfiguration.vignetteEnabled = true;\r\n            this._teleportationInitialized = true;\r\n            if (this._isDefaultTeleportationTarget) {\r\n                this._createTeleportationCircles();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _onNewGamepadConnected = (gamepad: Gamepad) => {\r\n        if (gamepad.type !== Gamepad.POSE_ENABLED) {\r\n            if (gamepad.leftStick) {\r\n                gamepad.onleftstickchanged((stickValues) => {\r\n                    if (this._teleportationInitialized && this.teleportationEnabled) {\r\n                        // Listening to classic/xbox gamepad only if no VR controller is active\r\n                        this._checkTeleportWithRay(stickValues, this._cameraGazer);\r\n                        this._checkTeleportBackwards(stickValues, this._cameraGazer);\r\n                    }\r\n                });\r\n            }\r\n            if (gamepad.rightStick) {\r\n                gamepad.onrightstickchanged((stickValues) => {\r\n                    if (this._teleportationInitialized) {\r\n                        this._checkRotate(stickValues, this._cameraGazer);\r\n                    }\r\n                });\r\n            }\r\n            if (gamepad.type === Gamepad.XBOX) {\r\n                (<Xbox360Pad>gamepad).onbuttondown((buttonPressed: Xbox360Button) => {\r\n                    if (this._interactionsEnabled && buttonPressed === Xbox360Button.A) {\r\n                        this._cameraGazer._selectionPointerDown();\r\n                    }\r\n                });\r\n                (<Xbox360Pad>gamepad).onbuttonup((buttonPressed: Xbox360Button) => {\r\n                    if (this._interactionsEnabled && buttonPressed === Xbox360Button.A) {\r\n                        this._cameraGazer._selectionPointerUp();\r\n                    }\r\n                });\r\n            }\r\n        }\r\n    };\r\n\r\n    private _checkTeleportWithRay(stateObject: StickValues, gazer: VRExperienceHelperGazer) {\r\n        // Dont teleport if another gaze already requested teleportation\r\n        if (this._teleportationRequestInitiated && !gazer._teleportationRequestInitiated) {\r\n            return;\r\n        }\r\n        if (!gazer._teleportationRequestInitiated) {\r\n            if (stateObject.y < -this._padSensibilityUp && gazer._dpadPressed) {\r\n                gazer._activatePointer();\r\n                gazer._teleportationRequestInitiated = true;\r\n            }\r\n        } else {\r\n            // Listening to the proper controller values changes to confirm teleportation\r\n            if (Math.sqrt(stateObject.y * stateObject.y + stateObject.x * stateObject.x) < this._padSensibilityDown) {\r\n                if (this._teleportActive) {\r\n                    this.teleportCamera(this._haloCenter);\r\n                }\r\n\r\n                gazer._teleportationRequestInitiated = false;\r\n            }\r\n        }\r\n    }\r\n    private _checkRotate(stateObject: StickValues, gazer: VRExperienceHelperGazer) {\r\n        // Only rotate when user is not currently selecting a teleportation location\r\n        if (gazer._teleportationRequestInitiated) {\r\n            return;\r\n        }\r\n\r\n        if (!gazer._rotationLeftAsked) {\r\n            if (stateObject.x < -this._padSensibilityUp && gazer._dpadPressed) {\r\n                gazer._rotationLeftAsked = true;\r\n                if (this._rotationAllowed) {\r\n                    this._rotateCamera(false);\r\n                }\r\n            }\r\n        } else {\r\n            if (stateObject.x > -this._padSensibilityDown) {\r\n                gazer._rotationLeftAsked = false;\r\n            }\r\n        }\r\n\r\n        if (!gazer._rotationRightAsked) {\r\n            if (stateObject.x > this._padSensibilityUp && gazer._dpadPressed) {\r\n                gazer._rotationRightAsked = true;\r\n                if (this._rotationAllowed) {\r\n                    this._rotateCamera(true);\r\n                }\r\n            }\r\n        } else {\r\n            if (stateObject.x < this._padSensibilityDown) {\r\n                gazer._rotationRightAsked = false;\r\n            }\r\n        }\r\n    }\r\n    private _checkTeleportBackwards(stateObject: StickValues, gazer: VRExperienceHelperGazer) {\r\n        // Only teleport backwards when user is not currently selecting a teleportation location\r\n        if (gazer._teleportationRequestInitiated) {\r\n            return;\r\n        }\r\n        // Teleport backwards\r\n        if (stateObject.y > this._padSensibilityUp && gazer._dpadPressed) {\r\n            if (!gazer._teleportationBackRequestInitiated) {\r\n                if (!this.currentVRCamera) {\r\n                    return;\r\n                }\r\n\r\n                // Get rotation and position of the current camera\r\n                const rotation = Quaternion.FromRotationMatrix(this.currentVRCamera.getWorldMatrix().getRotationMatrix());\r\n                const position = this.currentVRCamera.position;\r\n\r\n                // Get matrix with only the y rotation of the device rotation\r\n                rotation.toEulerAnglesToRef(this._workingVector);\r\n                this._workingVector.z = 0;\r\n                this._workingVector.x = 0;\r\n                Quaternion.RotationYawPitchRollToRef(this._workingVector.y, this._workingVector.x, this._workingVector.z, this._workingQuaternion);\r\n                this._workingQuaternion.toRotationMatrix(this._workingMatrix);\r\n\r\n                // Rotate backwards ray by device rotation to cast at the ground behind the user\r\n                Vector3.TransformCoordinatesToRef(this._teleportBackwardsVector, this._workingMatrix, this._workingVector);\r\n\r\n                // Teleport if ray hit the ground and is not to far away eg. backwards off a cliff\r\n                const ray = new Ray(position, this._workingVector);\r\n                const hit = this._scene.pickWithRay(ray, this._raySelectionPredicate);\r\n                if (hit && hit.pickedPoint && hit.pickedMesh && this._isTeleportationFloor(hit.pickedMesh) && hit.distance < 5) {\r\n                    this.teleportCamera(hit.pickedPoint);\r\n                }\r\n\r\n                gazer._teleportationBackRequestInitiated = true;\r\n            }\r\n        } else {\r\n            gazer._teleportationBackRequestInitiated = false;\r\n        }\r\n    }\r\n\r\n    private _createTeleportationCircles() {\r\n        this._teleportationTarget = CreateGround(\"teleportationTarget\", { width: 2, height: 2, subdivisions: 2 }, this._scene);\r\n        this._teleportationTarget.isPickable = false;\r\n\r\n        const length = 512;\r\n        const dynamicTexture = new DynamicTexture(\"DynamicTexture\", length, this._scene, true);\r\n        dynamicTexture.hasAlpha = true;\r\n        const context = dynamicTexture.getContext();\r\n\r\n        const centerX = length / 2;\r\n        const centerY = length / 2;\r\n        const radius = 200;\r\n\r\n        context.beginPath();\r\n        context.arc(centerX, centerY, radius, 0, 2 * Math.PI, false);\r\n        context.fillStyle = this._teleportationFillColor;\r\n        context.fill();\r\n        context.lineWidth = 10;\r\n        context.strokeStyle = this._teleportationBorderColor;\r\n        context.stroke();\r\n        context.closePath();\r\n        dynamicTexture.update();\r\n\r\n        const teleportationCircleMaterial = new StandardMaterial(\"TextPlaneMaterial\", this._scene);\r\n        teleportationCircleMaterial.diffuseTexture = dynamicTexture;\r\n        this._teleportationTarget.material = teleportationCircleMaterial;\r\n\r\n        const torus = CreateTorus(\r\n            \"torusTeleportation\",\r\n            {\r\n                diameter: 0.75,\r\n                thickness: 0.1,\r\n                tessellation: 25,\r\n                updatable: false,\r\n            },\r\n            this._scene\r\n        );\r\n        torus.isPickable = false;\r\n        torus.parent = this._teleportationTarget;\r\n\r\n        const animationInnerCircle = new Animation(\"animationInnerCircle\", \"position.y\", 30, Animation.ANIMATIONTYPE_FLOAT, Animation.ANIMATIONLOOPMODE_CYCLE);\r\n\r\n        const keys = [];\r\n        keys.push({\r\n            frame: 0,\r\n            value: 0,\r\n        });\r\n        keys.push({\r\n            frame: 30,\r\n            value: 0.4,\r\n        });\r\n        keys.push({\r\n            frame: 60,\r\n            value: 0,\r\n        });\r\n\r\n        animationInnerCircle.setKeys(keys);\r\n\r\n        const easingFunction = new SineEase();\r\n        easingFunction.setEasingMode(EasingFunction.EASINGMODE_EASEINOUT);\r\n        animationInnerCircle.setEasingFunction(easingFunction);\r\n\r\n        torus.animations = [];\r\n        torus.animations.push(animationInnerCircle);\r\n\r\n        this._scene.beginAnimation(torus, 0, 60, true);\r\n\r\n        this._hideTeleportationTarget();\r\n    }\r\n\r\n    private _hideTeleportationTarget() {\r\n        this._teleportActive = false;\r\n        if (this._teleportationInitialized) {\r\n            this._teleportationTarget.isVisible = false;\r\n            if (this._isDefaultTeleportationTarget) {\r\n                (<Mesh>this._teleportationTarget.getChildren()[0]).isVisible = false;\r\n            }\r\n        }\r\n    }\r\n\r\n    private _rotateCamera(right: boolean) {\r\n        if (!(this.currentVRCamera instanceof FreeCamera)) {\r\n            return;\r\n        }\r\n\r\n        if (right) {\r\n            this._rotationAngle++;\r\n        } else {\r\n            this._rotationAngle--;\r\n        }\r\n\r\n        this.currentVRCamera.animations = [];\r\n\r\n        const target = Quaternion.FromRotationMatrix(Matrix.RotationY((Math.PI / 4) * this._rotationAngle));\r\n\r\n        const animationRotation = new Animation(\"animationRotation\", \"rotationQuaternion\", 90, Animation.ANIMATIONTYPE_QUATERNION, Animation.ANIMATIONLOOPMODE_CONSTANT);\r\n\r\n        const animationRotationKeys = [];\r\n        animationRotationKeys.push({\r\n            frame: 0,\r\n            value: this.currentVRCamera.rotationQuaternion,\r\n        });\r\n        animationRotationKeys.push({\r\n            frame: 6,\r\n            value: target,\r\n        });\r\n\r\n        animationRotation.setKeys(animationRotationKeys);\r\n\r\n        animationRotation.setEasingFunction(this._circleEase);\r\n\r\n        this.currentVRCamera.animations.push(animationRotation);\r\n\r\n        this._postProcessMove.animations = [];\r\n\r\n        const animationPP = new Animation(\"animationPP\", \"vignetteWeight\", 90, Animation.ANIMATIONTYPE_FLOAT, Animation.ANIMATIONLOOPMODE_CONSTANT);\r\n\r\n        const vignetteWeightKeys = [];\r\n        vignetteWeightKeys.push({\r\n            frame: 0,\r\n            value: 0,\r\n        });\r\n        vignetteWeightKeys.push({\r\n            frame: 3,\r\n            value: 4,\r\n        });\r\n        vignetteWeightKeys.push({\r\n            frame: 6,\r\n            value: 0,\r\n        });\r\n\r\n        animationPP.setKeys(vignetteWeightKeys);\r\n        animationPP.setEasingFunction(this._circleEase);\r\n        this._postProcessMove.animations.push(animationPP);\r\n\r\n        const animationPP2 = new Animation(\"animationPP2\", \"vignetteStretch\", 90, Animation.ANIMATIONTYPE_FLOAT, Animation.ANIMATIONLOOPMODE_CONSTANT);\r\n\r\n        const vignetteStretchKeys = [];\r\n        vignetteStretchKeys.push({\r\n            frame: 0,\r\n            value: 0,\r\n        });\r\n        vignetteStretchKeys.push({\r\n            frame: 3,\r\n            value: 10,\r\n        });\r\n        vignetteStretchKeys.push({\r\n            frame: 6,\r\n            value: 0,\r\n        });\r\n\r\n        animationPP2.setKeys(vignetteStretchKeys);\r\n        animationPP2.setEasingFunction(this._circleEase);\r\n        this._postProcessMove.animations.push(animationPP2);\r\n\r\n        this._postProcessMove.imageProcessingConfiguration.vignetteWeight = 0;\r\n        this._postProcessMove.imageProcessingConfiguration.vignetteStretch = 0;\r\n        this._postProcessMove.samples = 4;\r\n        this._scene.beginAnimation(this.currentVRCamera, 0, 6, false, 1);\r\n    }\r\n\r\n    private _workingVector = Vector3.Zero();\r\n    private _workingQuaternion = Quaternion.Identity();\r\n    private _workingMatrix = Matrix.Identity();\r\n\r\n    /**\r\n     * Time Constant Teleportation Mode\r\n     */\r\n    public static readonly TELEPORTATIONMODE_CONSTANTTIME = 0;\r\n    /**\r\n     * Speed Constant Teleportation Mode\r\n     */\r\n    public static readonly TELEPORTATIONMODE_CONSTANTSPEED = 1;\r\n\r\n    /**\r\n     * Teleports the users feet to the desired location\r\n     * @param location The location where the user's feet should be placed\r\n     */\r\n    public teleportCamera(location: Vector3) {\r\n        if (!(this.currentVRCamera instanceof FreeCamera)) {\r\n            return;\r\n        }\r\n        // Teleport the hmd to where the user is looking by moving the anchor to where they are looking minus the\r\n        // offset of the headset from the anchor.\r\n\r\n        this._workingVector.copyFrom(location);\r\n        // Add height to account for user's height offset\r\n        if (this.isInVRMode) {\r\n            // no-op\r\n        } else {\r\n            this._workingVector.y += this._defaultHeight;\r\n        }\r\n\r\n        this.onBeforeCameraTeleport.notifyObservers(this._workingVector);\r\n\r\n        // Animations FPS\r\n        const FPS = 90;\r\n        let speedRatio, lastFrame;\r\n        if (this._teleportationMode == VRExperienceHelper.TELEPORTATIONMODE_CONSTANTSPEED) {\r\n            lastFrame = FPS;\r\n            const dist = Vector3.Distance(this.currentVRCamera.position, this._workingVector);\r\n            speedRatio = this._teleportationSpeed / dist;\r\n        } else {\r\n            // teleportationMode is TELEPORTATIONMODE_CONSTANTTIME\r\n            lastFrame = Math.round((this._teleportationTime * FPS) / 1000);\r\n            speedRatio = 1;\r\n        }\r\n\r\n        // Create animation from the camera's position to the new location\r\n        this.currentVRCamera.animations = [];\r\n        const animationCameraTeleportation = new Animation(\"animationCameraTeleportation\", \"position\", FPS, Animation.ANIMATIONTYPE_VECTOR3, Animation.ANIMATIONLOOPMODE_CONSTANT);\r\n        const animationCameraTeleportationKeys = [\r\n            {\r\n                frame: 0,\r\n                value: this.currentVRCamera.position,\r\n            },\r\n            {\r\n                frame: lastFrame,\r\n                value: this._workingVector,\r\n            },\r\n        ];\r\n\r\n        animationCameraTeleportation.setKeys(animationCameraTeleportationKeys);\r\n        animationCameraTeleportation.setEasingFunction(this._teleportationEasing);\r\n        this.currentVRCamera.animations.push(animationCameraTeleportation);\r\n\r\n        this._postProcessMove.animations = [];\r\n\r\n        // Calculate the mid frame for vignette animations\r\n        const midFrame = Math.round(lastFrame / 2);\r\n\r\n        const animationPP = new Animation(\"animationPP\", \"vignetteWeight\", FPS, Animation.ANIMATIONTYPE_FLOAT, Animation.ANIMATIONLOOPMODE_CONSTANT);\r\n\r\n        const vignetteWeightKeys = [];\r\n        vignetteWeightKeys.push({\r\n            frame: 0,\r\n            value: 0,\r\n        });\r\n        vignetteWeightKeys.push({\r\n            frame: midFrame,\r\n            value: 8,\r\n        });\r\n        vignetteWeightKeys.push({\r\n            frame: lastFrame,\r\n            value: 0,\r\n        });\r\n\r\n        animationPP.setKeys(vignetteWeightKeys);\r\n        this._postProcessMove.animations.push(animationPP);\r\n\r\n        const animationPP2 = new Animation(\"animationPP2\", \"vignetteStretch\", FPS, Animation.ANIMATIONTYPE_FLOAT, Animation.ANIMATIONLOOPMODE_CONSTANT);\r\n\r\n        const vignetteStretchKeys = [];\r\n        vignetteStretchKeys.push({\r\n            frame: 0,\r\n            value: 0,\r\n        });\r\n        vignetteStretchKeys.push({\r\n            frame: midFrame,\r\n            value: 10,\r\n        });\r\n        vignetteStretchKeys.push({\r\n            frame: lastFrame,\r\n            value: 0,\r\n        });\r\n\r\n        animationPP2.setKeys(vignetteStretchKeys);\r\n        this._postProcessMove.animations.push(animationPP2);\r\n\r\n        this._postProcessMove.imageProcessingConfiguration.vignetteWeight = 0;\r\n        this._postProcessMove.imageProcessingConfiguration.vignetteStretch = 0;\r\n\r\n        this._scene.beginAnimation(this.currentVRCamera, 0, lastFrame, false, speedRatio, () => {\r\n            this.onAfterCameraTeleport.notifyObservers(this._workingVector);\r\n        });\r\n\r\n        this._hideTeleportationTarget();\r\n    }\r\n\r\n    /**\r\n     * Permanently set new colors for the laser pointer\r\n     * @param color the new laser color\r\n     * @param pickedColor the new laser color when picked mesh detected\r\n     */\r\n    public setLaserColor(color: Color3, pickedColor: Color3 = this._pickedLaserColor) {\r\n        this._pickedLaserColor = pickedColor;\r\n    }\r\n\r\n    /**\r\n     * Set lighting enabled / disabled on the laser pointer of both controllers\r\n     * @param _enabled should the lighting be enabled on the laser pointer\r\n     */\r\n    public setLaserLightingState(_enabled: boolean = true) {\r\n        // no-op\r\n    }\r\n\r\n    /**\r\n     * Permanently set new colors for the gaze pointer\r\n     * @param color the new gaze color\r\n     * @param pickedColor the new gaze color when picked mesh detected\r\n     */\r\n    public setGazeColor(color: Color3, pickedColor: Color3 = this._pickedGazeColor) {\r\n        this._pickedGazeColor = pickedColor;\r\n    }\r\n\r\n    /**\r\n     * Sets the color of the laser ray from the vr controllers.\r\n     * @param _color new color for the ray.\r\n     */\r\n    public changeLaserColor(_color: Color3) {\r\n        if (!this.updateControllerLaserColor) {\r\n            return;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the color of the ray from the vr headsets gaze.\r\n     * @param color new color for the ray.\r\n     */\r\n    public changeGazeColor(color: Color3) {\r\n        if (!this.updateGazeTrackerColor) {\r\n            return;\r\n        }\r\n        if (!(<StandardMaterial>this._cameraGazer._gazeTracker.material)) {\r\n            return;\r\n        }\r\n        (<StandardMaterial>this._cameraGazer._gazeTracker.material).emissiveColor = color;\r\n    }\r\n\r\n    /**\r\n     * Exits VR and disposes of the vr experience helper\r\n     */\r\n    public dispose() {\r\n        if (this.isInVRMode) {\r\n            this.exitVR();\r\n        }\r\n\r\n        if (this._postProcessMove) {\r\n            this._postProcessMove.dispose();\r\n        }\r\n\r\n        if (this._vrDeviceOrientationCamera) {\r\n            this._vrDeviceOrientationCamera.dispose();\r\n        }\r\n        if (!this._useCustomVRButton && this._btnVR && this._btnVR.parentNode) {\r\n            document.body.removeChild(this._btnVR);\r\n        }\r\n\r\n        if (this._deviceOrientationCamera && this._scene.activeCamera != this._deviceOrientationCamera) {\r\n            this._deviceOrientationCamera.dispose();\r\n        }\r\n\r\n        if (this._cameraGazer) {\r\n            this._cameraGazer.dispose();\r\n        }\r\n\r\n        if (this._teleportationTarget) {\r\n            this._teleportationTarget.dispose();\r\n        }\r\n\r\n        if (this.xr) {\r\n            this.xr.dispose();\r\n        }\r\n\r\n        this._floorMeshesCollection.length = 0;\r\n\r\n        document.removeEventListener(\"keydown\", this._onKeyDown);\r\n        window.removeEventListener(\"vrdisplaypresentchange\", this._onVrDisplayPresentChangeBind);\r\n\r\n        window.removeEventListener(\"resize\", this._onResize);\r\n        document.removeEventListener(\"fullscreenchange\", this._onFullscreenChange);\r\n\r\n        this._scene.gamepadManager.onGamepadConnectedObservable.removeCallback(this._onNewGamepadConnected);\r\n\r\n        this._scene.unregisterBeforeRender(this._beforeRender);\r\n    }\r\n\r\n    /**\r\n     * Gets the name of the VRExperienceHelper class\r\n     * @returns \"VRExperienceHelper\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"VRExperienceHelper\";\r\n    }\r\n}\r\n"]}