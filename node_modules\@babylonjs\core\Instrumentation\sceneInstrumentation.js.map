{"version": 3, "file": "sceneInstrumentation.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Instrumentation/sceneInstrumentation.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAKtC,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IA0D7B,aAAa;IACb;;OAEG;IACH,IAAW,iCAAiC;QACxC,OAAO,IAAI,CAAC,2BAA2B,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAW,iCAAiC;QACxC,OAAO,IAAI,CAAC,kCAAkC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAW,iCAAiC,CAAC,KAAc;QACvD,IAAI,KAAK,KAAK,IAAI,CAAC,kCAAkC,EAAE;YACnD,OAAO;SACV;QAED,IAAI,CAAC,kCAAkC,GAAG,KAAK,CAAC;QAEhD,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,uCAAuC,GAAG,IAAI,CAAC,KAAK,CAAC,wCAAwC,CAAC,GAAG,CAAC,GAAG,EAAE;gBACxG,KAAK,CAAC,uBAAuB,CAAC,0BAA0B,CAAC,CAAC;gBAC1D,IAAI,CAAC,2BAA2B,CAAC,eAAe,EAAE,CAAC;YACvD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC,KAAK,CAAC,uCAAuC,CAAC,GAAG,CAAC,GAAG,EAAE;gBACtG,KAAK,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,CAAC;gBACxD,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;SACN;aAAM;YACH,IAAI,CAAC,KAAK,CAAC,wCAAwC,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACzG,IAAI,CAAC,uCAAuC,GAAG,IAAI,CAAC;YAEpD,IAAI,CAAC,KAAK,CAAC,uCAAuC,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACvG,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC;SACtD;IACL,CAAC;IAED;;OAEG;IACH,IAAW,8BAA8B;QACrC,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAW,8BAA8B;QACrC,OAAO,IAAI,CAAC,+BAA+B,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAW,8BAA8B,CAAC,KAAc;QACpD,IAAI,KAAK,KAAK,IAAI,CAAC,+BAA+B,EAAE;YAChD,OAAO;SACV;QAED,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC;QAE7C,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC,KAAK,CAAC,qCAAqC,CAAC,GAAG,CAAC,GAAG,EAAE;gBAClG,KAAK,CAAC,uBAAuB,CAAC,0BAA0B,CAAC,CAAC;gBAC1D,IAAI,CAAC,wBAAwB,CAAC,eAAe,EAAE,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,mCAAmC,GAAG,IAAI,CAAC,KAAK,CAAC,oCAAoC,CAAC,GAAG,CAAC,GAAG,EAAE;gBAChG,KAAK,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,CAAC;gBACxD,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;SACN;aAAM;YACH,IAAI,CAAC,KAAK,CAAC,qCAAqC,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YACnG,IAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC;YAEjD,IAAI,CAAC,KAAK,CAAC,oCAAoC,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACjG,IAAI,CAAC,mCAAmC,GAAG,IAAI,CAAC;SACnD;IACL,CAAC;IAED;;OAEG;IACH,IAAW,0BAA0B;QACjC,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAW,0BAA0B;QACjC,OAAO,IAAI,CAAC,2BAA2B,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAW,0BAA0B,CAAC,KAAc;QAChD,IAAI,KAAK,KAAK,IAAI,CAAC,2BAA2B,EAAE;YAC5C,OAAO;SACV;QAED,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;QAEzC,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,mCAAmC,GAAG,IAAI,CAAC,KAAK,CAAC,oCAAoC,CAAC,GAAG,CAAC,GAAG,EAAE;gBAChG,KAAK,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;gBAC3C,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC,KAAK,CAAC,mCAAmC,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC9F,KAAK,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;gBACzC,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;SACN;aAAM;YACH,IAAI,CAAC,KAAK,CAAC,oCAAoC,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACjG,IAAI,CAAC,mCAAmC,GAAG,IAAI,CAAC;YAEhD,IAAI,CAAC,KAAK,CAAC,mCAAmC,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAC/F,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC;SAClD;IACL,CAAC;IAED;;OAEG;IACH,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAW,wBAAwB,CAAC,KAAc;QAC9C,IAAI,KAAK,KAAK,IAAI,CAAC,yBAAyB,EAAE;YAC1C,OAAO;SACV;QAED,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;QAEvC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;YAC5B,OAAO;SACV;QAED,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC5F,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;gBACzC,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC1F,KAAK,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;gBACvC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;SACN;aAAM;YACH,IAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC7F,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;YAE9C,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC3F,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;SAChD;IACL,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB,CAAC,KAAc;QACxC,IAAI,KAAK,KAAK,IAAI,CAAC,mBAAmB,EAAE;YACpC,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE;YACvC,OAAO;SACV;QAED,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QAEjC,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC1E,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;gBACzC,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACxE,KAAK,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;gBACvC,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;YACtC,CAAC,CAAC,CAAC;SACN;aAAM;YACH,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC3E,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;YAErC,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACzE,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;SACvC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAW,qBAAqB,CAAC,KAAc;QAC3C,IAAI,KAAK,KAAK,IAAI,CAAC,sBAAsB,EAAE;YACvC,OAAO;SACV;QAED,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;QAEpC,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC9E,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;YACzC,CAAC,CAAC,CAAC;SACN;aAAM;YACH,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC/E,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;SAC1C;IACL,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB,CAAC,KAAc;QACtC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAW,qBAAqB,CAAC,KAAc;QAC3C,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB,CAAC,KAAc;QACvC,IAAI,KAAK,KAAK,IAAI,CAAC,kBAAkB,EAAE;YACnC,OAAO;SACV;QAED,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAEhC,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC9E,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;gBACnC,KAAK,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC5E,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBACtC,KAAK,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;SACN;aAAM;YACH,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC/E,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;YACvC,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAC7E,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;SACzC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,IAAW,uBAAuB,CAAC,KAAc;QAC7C,IAAI,KAAK,KAAK,IAAI,CAAC,wBAAwB,EAAE;YACzC,OAAO;SACV;QAED,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;QAEtC,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC1F,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,CAAC;gBACzC,KAAK,CAAC,uBAAuB,CAAC,oBAAoB,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACxF,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC5C,KAAK,CAAC,qBAAqB,CAAC,oBAAoB,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;SACN;aAAM;YACH,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACrF,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;YAC1C,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACnF,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;SAC5C;IACL,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC;IAC7C,CAAC;IAED;;;;;OAKG;IACH;IACI;;OAEG;IACI,KAAY;QAAZ,UAAK,GAAL,KAAK,CAAO;QA1cf,uCAAkC,GAAG,KAAK,CAAC;QAC3C,gCAA2B,GAAG,IAAI,WAAW,EAAE,CAAC;QAEhD,oCAA+B,GAAG,KAAK,CAAC;QACxC,6BAAwB,GAAG,IAAI,WAAW,EAAE,CAAC;QAE7C,sBAAiB,GAAG,KAAK,CAAC;QAC1B,eAAU,GAAG,IAAI,WAAW,EAAE,CAAC;QAE/B,uBAAkB,GAAG,KAAK,CAAC;QAC3B,gBAAW,GAAG,IAAI,WAAW,EAAE,CAAC;QAEhC,2BAAsB,GAAG,KAAK,CAAC;QAC/B,oBAAe,GAAG,IAAI,WAAW,EAAE,CAAC;QAEpC,gCAA2B,GAAG,KAAK,CAAC;QACpC,yBAAoB,GAAG,IAAI,WAAW,EAAE,CAAC;QAEzC,8BAAyB,GAAG,KAAK,CAAC;QAClC,uBAAkB,GAAG,IAAI,WAAW,EAAE,CAAC;QAEvC,wBAAmB,GAAG,KAAK,CAAC;QAC5B,iBAAY,GAAG,IAAI,WAAW,EAAE,CAAC;QAEjC,2BAAsB,GAAG,KAAK,CAAC;QAC/B,oBAAe,GAAG,IAAI,WAAW,EAAE,CAAC;QAEpC,6BAAwB,GAAG,KAAK,CAAC;QACjC,sBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;QAE9C,YAAY;QACJ,4CAAuC,GAA8B,IAAI,CAAC;QAC1E,2CAAsC,GAA8B,IAAI,CAAC;QACzE,yCAAoC,GAA8B,IAAI,CAAC;QACvE,wCAAmC,GAA8B,IAAI,CAAC;QAEtE,2BAAsB,GAA8B,IAAI,CAAC;QAEzD,+BAA0B,GAA8B,IAAI,CAAC;QAC7D,8BAAyB,GAA8B,IAAI,CAAC;QAE5D,gCAA2B,GAA8B,IAAI,CAAC;QAE9D,wCAAmC,GAA8B,IAAI,CAAC;QACtE,uCAAkC,GAA8B,IAAI,CAAC;QAErE,sCAAiC,GAA8B,IAAI,CAAC;QACpE,qCAAgC,GAA8B,IAAI,CAAC;QAEnE,6BAAwB,GAA8B,IAAI,CAAC;QAC3D,4BAAuB,GAA8B,IAAI,CAAC;QAE1D,+BAA0B,GAA8B,IAAI,CAAC;QAE7D,kCAA6B,GAA+B,IAAI,CAAC;QACjE,iCAA4B,GAA+B,IAAI,CAAC;QAqZpE,gBAAgB;QAChB,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;YAC3E,IAAI,IAAI,CAAC,kCAAkC,EAAE;gBACzC,IAAI,CAAC,2BAA2B,CAAC,aAAa,EAAE,CAAC;aACpD;YAED,IAAI,IAAI,CAAC,+BAA+B,EAAE;gBACtC,IAAI,CAAC,wBAAwB,CAAC,aAAa,EAAE,CAAC;aACjD;YAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxB,KAAK,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;gBACjD,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;aACrC;YAED,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;aACxC;YAED,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBAClC,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,CAAC;aAC7C;YAED,IAAI,IAAI,CAAC,yBAAyB,EAAE;gBAChC,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;aAC3C;YAED,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC;aAC1C;YAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;aACpC;YAED,IAAI,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC;aAC1C;YAED,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,eAAe;QACf,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;YACjE,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxB,KAAK,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;gBAC/C,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;aACnC;YAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aACzC;YAED,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC;aAC1C;YAED,IAAI,IAAI,CAAC,kCAAkC,EAAE;gBACzC,IAAI,CAAC,2BAA2B,CAAC,QAAQ,EAAE,CAAC;aAC/C;YACD,IAAI,IAAI,CAAC,+BAA+B,EAAE;gBACtC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,CAAC;aAC5C;YACD,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBAClC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;aACxC;YACD,IAAI,IAAI,CAAC,yBAAyB,EAAE;gBAChC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;aACtC;YACD,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;aAC/B;YACD,IAAI,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;aACrC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACvE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QAEnC,IAAI,CAAC,KAAK,CAAC,wCAAwC,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACzG,IAAI,CAAC,uCAAuC,GAAG,IAAI,CAAC;QAEpD,IAAI,CAAC,KAAK,CAAC,uCAAuC,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACvG,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC;QAEnD,IAAI,CAAC,KAAK,CAAC,qCAAqC,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACnG,IAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC;QAEjD,IAAI,CAAC,KAAK,CAAC,oCAAoC,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACjG,IAAI,CAAC,mCAAmC,GAAG,IAAI,CAAC;QAEhD,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACjF,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;QAExC,IAAI,CAAC,KAAK,CAAC,oCAAoC,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACjG,IAAI,CAAC,mCAAmC,GAAG,IAAI,CAAC;QAEhD,IAAI,CAAC,KAAK,CAAC,mCAAmC,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAC/F,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC;QAE/C,IAAI,IAAI,CAAC,iCAAiC,EAAE;YACxC,IAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC7F,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;SACjD;QAED,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACvC,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC3F,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;SAChD;QAED,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC/E,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QAEvC,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC7E,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;QAEtC,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC3E,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;SACxC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACzE,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;SACvC;QAED,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC/E,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QAEvC,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACrF,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;QAE1C,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QACnF,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;QAEnC,IAAI,CAAC,KAAM,GAAG,IAAI,CAAC;IAC7B,CAAC;CACJ", "sourcesContent": ["import { Tools } from \"../Misc/tools\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Scene, IDisposable } from \"../scene\";\r\nimport { PerfCounter } from \"../Misc/perfCounter\";\r\n/**\r\n * This class can be used to get instrumentation data from a Babylon engine\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimize_your_scene#sceneinstrumentation\r\n */\r\nexport class SceneInstrumentation implements IDisposable {\r\n    private _captureActiveMeshesEvaluationTime = false;\r\n    private _activeMeshesEvaluationTime = new PerfCounter();\r\n\r\n    private _captureRenderTargetsRenderTime = false;\r\n    private _renderTargetsRenderTime = new PerfCounter();\r\n\r\n    private _captureFrameTime = false;\r\n    private _frameTime = new PerfCounter();\r\n\r\n    private _captureRenderTime = false;\r\n    private _renderTime = new PerfCounter();\r\n\r\n    private _captureInterFrameTime = false;\r\n    private _interFrameTime = new PerfCounter();\r\n\r\n    private _captureParticlesRenderTime = false;\r\n    private _particlesRenderTime = new PerfCounter();\r\n\r\n    private _captureSpritesRenderTime = false;\r\n    private _spritesRenderTime = new PerfCounter();\r\n\r\n    private _capturePhysicsTime = false;\r\n    private _physicsTime = new PerfCounter();\r\n\r\n    private _captureAnimationsTime = false;\r\n    private _animationsTime = new PerfCounter();\r\n\r\n    private _captureCameraRenderTime = false;\r\n    private _cameraRenderTime = new PerfCounter();\r\n\r\n    // Observers\r\n    private _onBeforeActiveMeshesEvaluationObserver: Nullable<Observer<Scene>> = null;\r\n    private _onAfterActiveMeshesEvaluationObserver: Nullable<Observer<Scene>> = null;\r\n    private _onBeforeRenderTargetsRenderObserver: Nullable<Observer<Scene>> = null;\r\n    private _onAfterRenderTargetsRenderObserver: Nullable<Observer<Scene>> = null;\r\n\r\n    private _onAfterRenderObserver: Nullable<Observer<Scene>> = null;\r\n\r\n    private _onBeforeDrawPhaseObserver: Nullable<Observer<Scene>> = null;\r\n    private _onAfterDrawPhaseObserver: Nullable<Observer<Scene>> = null;\r\n\r\n    private _onBeforeAnimationsObserver: Nullable<Observer<Scene>> = null;\r\n\r\n    private _onBeforeParticlesRenderingObserver: Nullable<Observer<Scene>> = null;\r\n    private _onAfterParticlesRenderingObserver: Nullable<Observer<Scene>> = null;\r\n\r\n    private _onBeforeSpritesRenderingObserver: Nullable<Observer<Scene>> = null;\r\n    private _onAfterSpritesRenderingObserver: Nullable<Observer<Scene>> = null;\r\n\r\n    private _onBeforePhysicsObserver: Nullable<Observer<Scene>> = null;\r\n    private _onAfterPhysicsObserver: Nullable<Observer<Scene>> = null;\r\n\r\n    private _onAfterAnimationsObserver: Nullable<Observer<Scene>> = null;\r\n\r\n    private _onBeforeCameraRenderObserver: Nullable<Observer<Camera>> = null;\r\n    private _onAfterCameraRenderObserver: Nullable<Observer<Camera>> = null;\r\n\r\n    // Properties\r\n    /**\r\n     * Gets the perf counter used for active meshes evaluation time\r\n     */\r\n    public get activeMeshesEvaluationTimeCounter(): PerfCounter {\r\n        return this._activeMeshesEvaluationTime;\r\n    }\r\n\r\n    /**\r\n     * Gets the active meshes evaluation time capture status\r\n     */\r\n    public get captureActiveMeshesEvaluationTime(): boolean {\r\n        return this._captureActiveMeshesEvaluationTime;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable the active meshes evaluation time capture\r\n     */\r\n    public set captureActiveMeshesEvaluationTime(value: boolean) {\r\n        if (value === this._captureActiveMeshesEvaluationTime) {\r\n            return;\r\n        }\r\n\r\n        this._captureActiveMeshesEvaluationTime = value;\r\n\r\n        if (value) {\r\n            this._onBeforeActiveMeshesEvaluationObserver = this.scene.onBeforeActiveMeshesEvaluationObservable.add(() => {\r\n                Tools.StartPerformanceCounter(\"Active meshes evaluation\");\r\n                this._activeMeshesEvaluationTime.beginMonitoring();\r\n            });\r\n\r\n            this._onAfterActiveMeshesEvaluationObserver = this.scene.onAfterActiveMeshesEvaluationObservable.add(() => {\r\n                Tools.EndPerformanceCounter(\"Active meshes evaluation\");\r\n                this._activeMeshesEvaluationTime.endMonitoring(false);\r\n            });\r\n        } else {\r\n            this.scene.onBeforeActiveMeshesEvaluationObservable.remove(this._onBeforeActiveMeshesEvaluationObserver);\r\n            this._onBeforeActiveMeshesEvaluationObserver = null;\r\n\r\n            this.scene.onAfterActiveMeshesEvaluationObservable.remove(this._onAfterActiveMeshesEvaluationObserver);\r\n            this._onAfterActiveMeshesEvaluationObserver = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the perf counter used for render targets render time\r\n     */\r\n    public get renderTargetsRenderTimeCounter(): PerfCounter {\r\n        return this._renderTargetsRenderTime;\r\n    }\r\n\r\n    /**\r\n     * Gets the render targets render time capture status\r\n     */\r\n    public get captureRenderTargetsRenderTime(): boolean {\r\n        return this._captureRenderTargetsRenderTime;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable the render targets render time capture\r\n     */\r\n    public set captureRenderTargetsRenderTime(value: boolean) {\r\n        if (value === this._captureRenderTargetsRenderTime) {\r\n            return;\r\n        }\r\n\r\n        this._captureRenderTargetsRenderTime = value;\r\n\r\n        if (value) {\r\n            this._onBeforeRenderTargetsRenderObserver = this.scene.onBeforeRenderTargetsRenderObservable.add(() => {\r\n                Tools.StartPerformanceCounter(\"Render targets rendering\");\r\n                this._renderTargetsRenderTime.beginMonitoring();\r\n            });\r\n\r\n            this._onAfterRenderTargetsRenderObserver = this.scene.onAfterRenderTargetsRenderObservable.add(() => {\r\n                Tools.EndPerformanceCounter(\"Render targets rendering\");\r\n                this._renderTargetsRenderTime.endMonitoring(false);\r\n            });\r\n        } else {\r\n            this.scene.onBeforeRenderTargetsRenderObservable.remove(this._onBeforeRenderTargetsRenderObserver);\r\n            this._onBeforeRenderTargetsRenderObserver = null;\r\n\r\n            this.scene.onAfterRenderTargetsRenderObservable.remove(this._onAfterRenderTargetsRenderObserver);\r\n            this._onAfterRenderTargetsRenderObserver = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the perf counter used for particles render time\r\n     */\r\n    public get particlesRenderTimeCounter(): PerfCounter {\r\n        return this._particlesRenderTime;\r\n    }\r\n\r\n    /**\r\n     * Gets the particles render time capture status\r\n     */\r\n    public get captureParticlesRenderTime(): boolean {\r\n        return this._captureParticlesRenderTime;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable the particles render time capture\r\n     */\r\n    public set captureParticlesRenderTime(value: boolean) {\r\n        if (value === this._captureParticlesRenderTime) {\r\n            return;\r\n        }\r\n\r\n        this._captureParticlesRenderTime = value;\r\n\r\n        if (value) {\r\n            this._onBeforeParticlesRenderingObserver = this.scene.onBeforeParticlesRenderingObservable.add(() => {\r\n                Tools.StartPerformanceCounter(\"Particles\");\r\n                this._particlesRenderTime.beginMonitoring();\r\n            });\r\n\r\n            this._onAfterParticlesRenderingObserver = this.scene.onAfterParticlesRenderingObservable.add(() => {\r\n                Tools.EndPerformanceCounter(\"Particles\");\r\n                this._particlesRenderTime.endMonitoring(false);\r\n            });\r\n        } else {\r\n            this.scene.onBeforeParticlesRenderingObservable.remove(this._onBeforeParticlesRenderingObserver);\r\n            this._onBeforeParticlesRenderingObserver = null;\r\n\r\n            this.scene.onAfterParticlesRenderingObservable.remove(this._onAfterParticlesRenderingObserver);\r\n            this._onAfterParticlesRenderingObserver = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the perf counter used for sprites render time\r\n     */\r\n    public get spritesRenderTimeCounter(): PerfCounter {\r\n        return this._spritesRenderTime;\r\n    }\r\n\r\n    /**\r\n     * Gets the sprites render time capture status\r\n     */\r\n    public get captureSpritesRenderTime(): boolean {\r\n        return this._captureSpritesRenderTime;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable the sprites render time capture\r\n     */\r\n    public set captureSpritesRenderTime(value: boolean) {\r\n        if (value === this._captureSpritesRenderTime) {\r\n            return;\r\n        }\r\n\r\n        this._captureSpritesRenderTime = value;\r\n\r\n        if (!this.scene.spriteManagers) {\r\n            return;\r\n        }\r\n\r\n        if (value) {\r\n            this._onBeforeSpritesRenderingObserver = this.scene.onBeforeSpritesRenderingObservable.add(() => {\r\n                Tools.StartPerformanceCounter(\"Sprites\");\r\n                this._spritesRenderTime.beginMonitoring();\r\n            });\r\n\r\n            this._onAfterSpritesRenderingObserver = this.scene.onAfterSpritesRenderingObservable.add(() => {\r\n                Tools.EndPerformanceCounter(\"Sprites\");\r\n                this._spritesRenderTime.endMonitoring(false);\r\n            });\r\n        } else {\r\n            this.scene.onBeforeSpritesRenderingObservable.remove(this._onBeforeSpritesRenderingObserver);\r\n            this._onBeforeSpritesRenderingObserver = null;\r\n\r\n            this.scene.onAfterSpritesRenderingObservable.remove(this._onAfterSpritesRenderingObserver);\r\n            this._onAfterSpritesRenderingObserver = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the perf counter used for physics time\r\n     */\r\n    public get physicsTimeCounter(): PerfCounter {\r\n        return this._physicsTime;\r\n    }\r\n\r\n    /**\r\n     * Gets the physics time capture status\r\n     */\r\n    public get capturePhysicsTime(): boolean {\r\n        return this._capturePhysicsTime;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable the physics time capture\r\n     */\r\n    public set capturePhysicsTime(value: boolean) {\r\n        if (value === this._capturePhysicsTime) {\r\n            return;\r\n        }\r\n\r\n        if (!this.scene.onBeforePhysicsObservable) {\r\n            return;\r\n        }\r\n\r\n        this._capturePhysicsTime = value;\r\n\r\n        if (value) {\r\n            this._onBeforePhysicsObserver = this.scene.onBeforePhysicsObservable.add(() => {\r\n                Tools.StartPerformanceCounter(\"Physics\");\r\n                this._physicsTime.beginMonitoring();\r\n            });\r\n\r\n            this._onAfterPhysicsObserver = this.scene.onAfterPhysicsObservable.add(() => {\r\n                Tools.EndPerformanceCounter(\"Physics\");\r\n                this._physicsTime.endMonitoring();\r\n            });\r\n        } else {\r\n            this.scene.onBeforePhysicsObservable.remove(this._onBeforePhysicsObserver);\r\n            this._onBeforePhysicsObserver = null;\r\n\r\n            this.scene.onAfterPhysicsObservable.remove(this._onAfterPhysicsObserver);\r\n            this._onAfterPhysicsObserver = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the perf counter used for animations time\r\n     */\r\n    public get animationsTimeCounter(): PerfCounter {\r\n        return this._animationsTime;\r\n    }\r\n\r\n    /**\r\n     * Gets the animations time capture status\r\n     */\r\n    public get captureAnimationsTime(): boolean {\r\n        return this._captureAnimationsTime;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable the animations time capture\r\n     */\r\n    public set captureAnimationsTime(value: boolean) {\r\n        if (value === this._captureAnimationsTime) {\r\n            return;\r\n        }\r\n\r\n        this._captureAnimationsTime = value;\r\n\r\n        if (value) {\r\n            this._onAfterAnimationsObserver = this.scene.onAfterAnimationsObservable.add(() => {\r\n                this._animationsTime.endMonitoring();\r\n            });\r\n        } else {\r\n            this.scene.onAfterAnimationsObservable.remove(this._onAfterAnimationsObserver);\r\n            this._onAfterAnimationsObserver = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the perf counter used for frame time capture\r\n     */\r\n    public get frameTimeCounter(): PerfCounter {\r\n        return this._frameTime;\r\n    }\r\n\r\n    /**\r\n     * Gets the frame time capture status\r\n     */\r\n    public get captureFrameTime(): boolean {\r\n        return this._captureFrameTime;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable the frame time capture\r\n     */\r\n    public set captureFrameTime(value: boolean) {\r\n        this._captureFrameTime = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the perf counter used for inter-frames time capture\r\n     */\r\n    public get interFrameTimeCounter(): PerfCounter {\r\n        return this._interFrameTime;\r\n    }\r\n\r\n    /**\r\n     * Gets the inter-frames time capture status\r\n     */\r\n    public get captureInterFrameTime(): boolean {\r\n        return this._captureInterFrameTime;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable the inter-frames time capture\r\n     */\r\n    public set captureInterFrameTime(value: boolean) {\r\n        this._captureInterFrameTime = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the perf counter used for render time capture\r\n     */\r\n    public get renderTimeCounter(): PerfCounter {\r\n        return this._renderTime;\r\n    }\r\n\r\n    /**\r\n     * Gets the render time capture status\r\n     */\r\n    public get captureRenderTime(): boolean {\r\n        return this._captureRenderTime;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable the render time capture\r\n     */\r\n    public set captureRenderTime(value: boolean) {\r\n        if (value === this._captureRenderTime) {\r\n            return;\r\n        }\r\n\r\n        this._captureRenderTime = value;\r\n\r\n        if (value) {\r\n            this._onBeforeDrawPhaseObserver = this.scene.onBeforeDrawPhaseObservable.add(() => {\r\n                this._renderTime.beginMonitoring();\r\n                Tools.StartPerformanceCounter(\"Main render\");\r\n            });\r\n\r\n            this._onAfterDrawPhaseObserver = this.scene.onAfterDrawPhaseObservable.add(() => {\r\n                this._renderTime.endMonitoring(false);\r\n                Tools.EndPerformanceCounter(\"Main render\");\r\n            });\r\n        } else {\r\n            this.scene.onBeforeDrawPhaseObservable.remove(this._onBeforeDrawPhaseObserver);\r\n            this._onBeforeDrawPhaseObserver = null;\r\n            this.scene.onAfterDrawPhaseObservable.remove(this._onAfterDrawPhaseObserver);\r\n            this._onAfterDrawPhaseObserver = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the perf counter used for camera render time capture\r\n     */\r\n    public get cameraRenderTimeCounter(): PerfCounter {\r\n        return this._cameraRenderTime;\r\n    }\r\n\r\n    /**\r\n     * Gets the camera render time capture status\r\n     */\r\n    public get captureCameraRenderTime(): boolean {\r\n        return this._captureCameraRenderTime;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable the camera render time capture\r\n     */\r\n    public set captureCameraRenderTime(value: boolean) {\r\n        if (value === this._captureCameraRenderTime) {\r\n            return;\r\n        }\r\n\r\n        this._captureCameraRenderTime = value;\r\n\r\n        if (value) {\r\n            this._onBeforeCameraRenderObserver = this.scene.onBeforeCameraRenderObservable.add((camera) => {\r\n                this._cameraRenderTime.beginMonitoring();\r\n                Tools.StartPerformanceCounter(`Rendering camera ${camera.name}`);\r\n            });\r\n\r\n            this._onAfterCameraRenderObserver = this.scene.onAfterCameraRenderObservable.add((camera) => {\r\n                this._cameraRenderTime.endMonitoring(false);\r\n                Tools.EndPerformanceCounter(`Rendering camera ${camera.name}`);\r\n            });\r\n        } else {\r\n            this.scene.onBeforeCameraRenderObservable.remove(this._onBeforeCameraRenderObserver);\r\n            this._onBeforeCameraRenderObserver = null;\r\n            this.scene.onAfterCameraRenderObservable.remove(this._onAfterCameraRenderObserver);\r\n            this._onAfterCameraRenderObserver = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the perf counter used for draw calls\r\n     */\r\n    public get drawCallsCounter(): PerfCounter {\r\n        return this.scene.getEngine()._drawCalls;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new scene instrumentation.\r\n     * This class can be used to get instrumentation data from a Babylon engine\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimize_your_scene#sceneinstrumentation\r\n     * @param scene Defines the scene to instrument\r\n     */\r\n    public constructor(\r\n        /**\r\n         * Defines the scene to instrument\r\n         */\r\n        public scene: Scene\r\n    ) {\r\n        // Before render\r\n        this._onBeforeAnimationsObserver = scene.onBeforeAnimationsObservable.add(() => {\r\n            if (this._captureActiveMeshesEvaluationTime) {\r\n                this._activeMeshesEvaluationTime.fetchNewFrame();\r\n            }\r\n\r\n            if (this._captureRenderTargetsRenderTime) {\r\n                this._renderTargetsRenderTime.fetchNewFrame();\r\n            }\r\n\r\n            if (this._captureFrameTime) {\r\n                Tools.StartPerformanceCounter(\"Scene rendering\");\r\n                this._frameTime.beginMonitoring();\r\n            }\r\n\r\n            if (this._captureInterFrameTime) {\r\n                this._interFrameTime.endMonitoring();\r\n            }\r\n\r\n            if (this._captureParticlesRenderTime) {\r\n                this._particlesRenderTime.fetchNewFrame();\r\n            }\r\n\r\n            if (this._captureSpritesRenderTime) {\r\n                this._spritesRenderTime.fetchNewFrame();\r\n            }\r\n\r\n            if (this._captureAnimationsTime) {\r\n                this._animationsTime.beginMonitoring();\r\n            }\r\n\r\n            if (this._captureRenderTime) {\r\n                this._renderTime.fetchNewFrame();\r\n            }\r\n\r\n            if (this._captureCameraRenderTime) {\r\n                this._cameraRenderTime.fetchNewFrame();\r\n            }\r\n\r\n            this.scene.getEngine()._drawCalls.fetchNewFrame();\r\n        });\r\n\r\n        // After render\r\n        this._onAfterRenderObserver = scene.onAfterRenderObservable.add(() => {\r\n            if (this._captureFrameTime) {\r\n                Tools.EndPerformanceCounter(\"Scene rendering\");\r\n                this._frameTime.endMonitoring();\r\n            }\r\n\r\n            if (this._captureRenderTime) {\r\n                this._renderTime.endMonitoring(false);\r\n            }\r\n\r\n            if (this._captureInterFrameTime) {\r\n                this._interFrameTime.beginMonitoring();\r\n            }\r\n\r\n            if (this._captureActiveMeshesEvaluationTime) {\r\n                this._activeMeshesEvaluationTime.endFrame();\r\n            }\r\n            if (this._captureRenderTargetsRenderTime) {\r\n                this._renderTargetsRenderTime.endFrame();\r\n            }\r\n            if (this._captureParticlesRenderTime) {\r\n                this._particlesRenderTime.endFrame();\r\n            }\r\n            if (this._captureSpritesRenderTime) {\r\n                this._spritesRenderTime.endFrame();\r\n            }\r\n            if (this._captureRenderTime) {\r\n                this._renderTime.endFrame();\r\n            }\r\n            if (this._captureCameraRenderTime) {\r\n                this._cameraRenderTime.endFrame();\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Dispose and release associated resources.\r\n     */\r\n    public dispose() {\r\n        this.scene.onAfterRenderObservable.remove(this._onAfterRenderObserver);\r\n        this._onAfterRenderObserver = null;\r\n\r\n        this.scene.onBeforeActiveMeshesEvaluationObservable.remove(this._onBeforeActiveMeshesEvaluationObserver);\r\n        this._onBeforeActiveMeshesEvaluationObserver = null;\r\n\r\n        this.scene.onAfterActiveMeshesEvaluationObservable.remove(this._onAfterActiveMeshesEvaluationObserver);\r\n        this._onAfterActiveMeshesEvaluationObserver = null;\r\n\r\n        this.scene.onBeforeRenderTargetsRenderObservable.remove(this._onBeforeRenderTargetsRenderObserver);\r\n        this._onBeforeRenderTargetsRenderObserver = null;\r\n\r\n        this.scene.onAfterRenderTargetsRenderObservable.remove(this._onAfterRenderTargetsRenderObserver);\r\n        this._onAfterRenderTargetsRenderObserver = null;\r\n\r\n        this.scene.onBeforeAnimationsObservable.remove(this._onBeforeAnimationsObserver);\r\n        this._onBeforeAnimationsObserver = null;\r\n\r\n        this.scene.onBeforeParticlesRenderingObservable.remove(this._onBeforeParticlesRenderingObserver);\r\n        this._onBeforeParticlesRenderingObserver = null;\r\n\r\n        this.scene.onAfterParticlesRenderingObservable.remove(this._onAfterParticlesRenderingObserver);\r\n        this._onAfterParticlesRenderingObserver = null;\r\n\r\n        if (this._onBeforeSpritesRenderingObserver) {\r\n            this.scene.onBeforeSpritesRenderingObservable.remove(this._onBeforeSpritesRenderingObserver);\r\n            this._onBeforeSpritesRenderingObserver = null;\r\n        }\r\n\r\n        if (this._onAfterSpritesRenderingObserver) {\r\n            this.scene.onAfterSpritesRenderingObservable.remove(this._onAfterSpritesRenderingObserver);\r\n            this._onAfterSpritesRenderingObserver = null;\r\n        }\r\n\r\n        this.scene.onBeforeDrawPhaseObservable.remove(this._onBeforeDrawPhaseObserver);\r\n        this._onBeforeDrawPhaseObserver = null;\r\n\r\n        this.scene.onAfterDrawPhaseObservable.remove(this._onAfterDrawPhaseObserver);\r\n        this._onAfterDrawPhaseObserver = null;\r\n\r\n        if (this._onBeforePhysicsObserver) {\r\n            this.scene.onBeforePhysicsObservable.remove(this._onBeforePhysicsObserver);\r\n            this._onBeforePhysicsObserver = null;\r\n        }\r\n\r\n        if (this._onAfterPhysicsObserver) {\r\n            this.scene.onAfterPhysicsObservable.remove(this._onAfterPhysicsObserver);\r\n            this._onAfterPhysicsObserver = null;\r\n        }\r\n\r\n        this.scene.onAfterAnimationsObservable.remove(this._onAfterAnimationsObserver);\r\n        this._onAfterAnimationsObserver = null;\r\n\r\n        this.scene.onBeforeCameraRenderObservable.remove(this._onBeforeCameraRenderObserver);\r\n        this._onBeforeCameraRenderObserver = null;\r\n\r\n        this.scene.onAfterCameraRenderObservable.remove(this._onAfterCameraRenderObserver);\r\n        this._onAfterCameraRenderObserver = null;\r\n\r\n        (<any>this.scene) = null;\r\n    }\r\n}\r\n"]}