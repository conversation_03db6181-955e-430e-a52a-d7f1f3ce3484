{"version": 3, "file": "webgpuExternalTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuExternalTexture.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,0CAA0C,CAAC;AAE3E;;;;IAII;AACJ,MAAM,OAAO,qBAAsB,SAAQ,eAAe;IACtD,YAAmB,KAAuB;QACtC,KAAK,CAAC,KAAK,CAAC,CAAC;IACjB,CAAC;CACJ", "sourcesContent": ["import { ExternalTexture } from \"../../Materials/Textures/externalTexture\";\r\n\r\n/**\r\n * Nothing specific to WebGPU in this class, but the spec is not final yet so let's remove it later on\r\n * if it is not needed\r\n * @internal\r\n **/\r\nexport class WebGPUExternalTexture extends ExternalTexture {\r\n    public constructor(video: HTMLVideoElement) {\r\n        super(video);\r\n    }\r\n}\r\n"]}