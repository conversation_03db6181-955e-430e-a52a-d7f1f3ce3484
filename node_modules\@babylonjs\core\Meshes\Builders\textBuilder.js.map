{"version": 3, "file": "textBuilder.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Builders/textBuilder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,uBAAuB,CAAC;AAC9C,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAIlD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAwClD,kBAAkB;AAClB,MAAM,SAAS;IAOX;;OAEG;IACH,YAAY,UAAkB;QATtB,WAAM,GAAY,EAAE,CAAC;QACrB,eAAU,GAAY,EAAE,CAAC;QACzB,WAAM,GAAY,EAAE,CAAC;QAQzB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,CAAS,EAAE,CAAS;QACvB,IAAI,CAAC,YAAY,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,CAAS,EAAE,CAAS;QACvB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED;;;;;OAKG;IACH,gBAAgB,CAAC,GAAW,EAAE,GAAW,EAAE,CAAS,EAAE,CAAS;QAC3D,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;;;;OAQG;IACH,aAAa,CAAC,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,CAAS,EAAE,CAAS;QACtF,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IACvF,CAAC;IAED,sCAAsC;IACtC,YAAY;QACR,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;YAChC,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;gBACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC1B;iBAAM;gBACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC1B;SACJ;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;YACzB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC1B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACtB;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED,6BAA6B;IAC7B,IAAI,KAAK;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,6BAA6B;IAC7B,IAAI,KAAK;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;CACJ;AAED,oBAAoB;AACpB,SAAS,eAAe,CACpB,IAAY,EACZ,KAAa,EACb,OAAe,EACf,OAAe,EACf,UAAkB,EAClB,QAAmB;IAKnB,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAE5D,IAAI,CAAC,KAAK,EAAE;QACR,mCAAmC;QACnC,OAAO,IAAI,CAAC;KACf;IAED,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,UAAU,CAAC,CAAC;IAE5C,IAAI,KAAK,CAAC,CAAC,EAAE;QACT,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAI;YACzC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;YAE5B,QAAQ,MAAM,EAAE;gBACZ,KAAK,GAAG,CAAC,CAAC;oBACN,SAAS;oBACT,MAAM,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBACnD,MAAM,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBAEnD,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACvB,MAAM;iBACT;gBACD,KAAK,GAAG,CAAC,CAAC;oBACN,SAAS;oBACT,MAAM,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBACnD,MAAM,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBAEnD,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACvB,MAAM;iBACT;gBACD,KAAK,GAAG,CAAC,CAAC;oBACN,mBAAmB;oBACnB,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBACrD,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBACrD,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBACtD,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBAEtD,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;oBACjD,MAAM;iBACT;gBACD,KAAK,GAAG,CAAC,CAAC;oBACN,gBAAgB;oBAChB,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBACrD,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBACrD,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBACtD,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBACtD,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBACtD,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;oBAEtD,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;oBAC1D,MAAM;iBACT;aACJ;SACJ;KACJ;IAED,0CAA0C;IAC1C,SAAS,CAAC,YAAY,EAAE,CAAC;IAEzB,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,GAAG,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;AAC/D,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,oBAAoB,CAAC,IAAY,EAAE,IAAY,EAAE,UAAkB,EAAE,QAAmB;IACpG,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/B,MAAM,KAAK,GAAG,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC;IACzC,MAAM,WAAW,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC;IAElH,MAAM,UAAU,GAAgB,EAAE,CAAC;IAEnC,IAAI,OAAO,GAAG,CAAC,EACX,OAAO,GAAG,CAAC,CAAC;IAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEtB,IAAI,IAAI,KAAK,IAAI,EAAE;YACf,OAAO,GAAG,CAAC,CAAC;YACZ,OAAO,IAAI,WAAW,CAAC;SAC1B;aAAM;YACH,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEjF,IAAI,GAAG,EAAE;gBACL,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC;gBACvB,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAClC;SACJ;KACJ;IAED,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,UAAU,CACtB,IAAY,EACZ,IAAY,EACZ,QAAmB,EACnB,UASI;IACA,IAAI,EAAE,EAAE;IACR,UAAU,EAAE,CAAC;IACb,KAAK,EAAE,GAAG;CACb,EACD,QAAyB,IAAI,EAC7B,eAAe,GAAG,MAAM;IAExB,sCAAsC;IACtC,MAAM,UAAU,GAAG,oBAAoB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;IAErG,mBAAmB;IACnB,MAAM,MAAM,GAAW,EAAE,CAAC;IAC1B,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;QAChC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE;YACzB,SAAS;SACZ;QAED,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,qCAAqC;QAC5E,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE;YAChC,MAAM,WAAW,GAAgB,EAAE,CAAC;YACpC,MAAM,YAAY,GAAc,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAChC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;gBACxB,YAAY,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,8CAA8C;aACtG;YAED,QAAQ;YACR,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;YACrC,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE;gBAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBAEhC,IAAI,KAAK,GAAG,KAAK,CAAC;gBAClB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;oBACxB,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;wBAC3B,KAAK,GAAG,IAAI,CAAC;wBACb,MAAM;qBACT;iBACJ;gBAED,IAAI,CAAC,KAAK,EAAE;oBACR,SAAS;iBACZ;gBAED,MAAM,UAAU,GAAc,EAAE,CAAC;gBACjC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;oBACxB,UAAU,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,8CAA8C;iBACpG;gBACD,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAE7B,yCAAyC;gBACzC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;aACxC;YAED,iDAAiD;YACjD,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE;gBACrC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;oBACtB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;oBAChC,MAAM,UAAU,GAAc,EAAE,CAAC;oBACjC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;wBACxB,UAAU,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,8CAA8C;qBACpG;oBACD,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBAChC;aACJ;YAED,aAAa;YACb,MAAM,IAAI,GAAG,cAAc,CACvB,IAAI,EACJ;gBACI,KAAK,EAAE,YAAY;gBACnB,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;gBACnD,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,GAAG;gBAC3B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC;gBAChE,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC,WAAW,CAAC;gBAC5E,eAAe,EAAE,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,CAAC;aAC/F,EACD,KAAK,EACL,eAAe,CAClB,CAAC;YACF,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClB,WAAW,EAAE,CAAC;SACjB;KACJ;IAED,kDAAkD;IAClD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAErD,IAAI,OAAO,EAAE;QACT,0DAA0D;QAC1D,MAAM,IAAI,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC;QACnD,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ;QAChF,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,0BAA0B;QAClG,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,6BAA6B;QACzH,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QAEpB,gBAAgB;QAChB,MAAM,KAAK,GAAG,IAAI,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAChD,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAChC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;QAEvB,OAAO,CAAC,gCAAgC,EAAE,CAAC;QAE3C,mBAAmB;QACnB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;QACtB,KAAK,CAAC,OAAO,EAAE,CAAC;KACnB;IAED,OAAO,OAAO,CAAC;AACnB,CAAC", "sourcesContent": ["import type { Color4 } from \"../../Maths/math.color\";\r\nimport { Path2 } from \"../../Maths/math.path\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { Vector4 } from \"../../Maths/math.vector\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Mesh } from \"../mesh\";\r\nimport { TransformNode } from \"../transformNode\";\r\nimport { ExtrudePolygon } from \"./polygonBuilder\";\r\n\r\ndeclare let earcut: any;\r\n\r\n/**\r\n * Parser inspired by https://github.com/mrdoob/three.js/blob/master/examples/jsm/loaders/FontLoader.js\r\n */\r\n\r\n// Interfaces\r\n\r\n/**\r\n * Represents glyph data generated by http://gero3.github.io/facetype.js/\r\n */\r\nexport interface IGlyphData {\r\n    /** Commands used to draw (line, move, curve, etc..) */\r\n    o: string;\r\n\r\n    /** Width */\r\n    ha: number;\r\n}\r\n\r\n/**\r\n * Represents font data generated by http://gero3.github.io/facetype.js/\r\n */\r\nexport interface IFontData {\r\n    /**\r\n     * Font resolution\r\n     */\r\n    resolution: number;\r\n    /** Underline tickness */\r\n    underlineThickness: number;\r\n    /** Bounding box */\r\n    boundingBox: {\r\n        yMax: number;\r\n        yMin: number;\r\n    };\r\n    /** List of supported glyphs */\r\n    glyphs: { [key: string]: IGlyphData };\r\n}\r\n\r\n// Shape functions\r\nclass ShapePath {\r\n    private _paths: Path2[] = [];\r\n    private _tempPaths: Path2[] = [];\r\n    private _holes: Path2[] = [];\r\n    private _currentPath: Path2;\r\n    private _resolution: number;\r\n\r\n    /** Create the ShapePath used to support glyphs\r\n     * @param resolution defines the resolution used to determine the number of points per curve (default is 4)\r\n     */\r\n    constructor(resolution: number) {\r\n        this._resolution = resolution;\r\n    }\r\n\r\n    /** Move the virtual cursor to a coordinate\r\n     * @param x defines the x coordinate\r\n     * @param y defines the y coordinate\r\n     */\r\n    moveTo(x: number, y: number) {\r\n        this._currentPath = new Path2(x, y);\r\n        this._tempPaths.push(this._currentPath);\r\n    }\r\n\r\n    /** Draw a line from the virtual cursor to a given coordinate\r\n     * @param x defines the x coordinate\r\n     * @param y defines the y coordinate\r\n     */\r\n    lineTo(x: number, y: number) {\r\n        this._currentPath.addLineTo(x, y);\r\n    }\r\n\r\n    /** Create a quadratic curve from the virtual cursor to a given coordinate\r\n     * @param cpx defines the x coordinate of the control point\r\n     * @param cpy defines the y coordinate of the control point\r\n     * @param x defines the x coordinate of the end point\r\n     * @param y defines the y coordinate of the end point\r\n     */\r\n    quadraticCurveTo(cpx: number, cpy: number, x: number, y: number) {\r\n        this._currentPath.addQuadraticCurveTo(cpx, cpy, x, y, this._resolution);\r\n    }\r\n\r\n    /**\r\n     * Create a bezier curve from the virtual cursor to a given coordinate\r\n     * @param cpx1 defines the x coordinate of the first control point\r\n     * @param cpy1 defines the y coordinate of the first control point\r\n     * @param cpx2 defines the x coordinate of the second control point\r\n     * @param cpy2 defines the y coordinate of the second control point\r\n     * @param x defines the x coordinate of the end point\r\n     * @param y defines the y coordinate of the end point\r\n     */\r\n    bezierCurveTo(cpx1: number, cpy1: number, cpx2: number, cpy2: number, x: number, y: number) {\r\n        this._currentPath.addBezierCurveTo(cpx1, cpy1, cpx2, cpy2, x, y, this._resolution);\r\n    }\r\n\r\n    /** Extract holes based on CW / CCW */\r\n    extractHoles() {\r\n        for (const path of this._tempPaths) {\r\n            if (path.area() > 0) {\r\n                this._holes.push(path);\r\n            } else {\r\n                this._paths.push(path);\r\n            }\r\n        }\r\n\r\n        if (!this._paths.length && this._holes.length) {\r\n            const temp = this._holes;\r\n            this._holes = this._paths;\r\n            this._paths = temp;\r\n        }\r\n\r\n        this._tempPaths.length = 0;\r\n    }\r\n\r\n    /** Gets the list of paths */\r\n    get paths() {\r\n        return this._paths;\r\n    }\r\n\r\n    /** Gets the list of holes */\r\n    get holes() {\r\n        return this._holes;\r\n    }\r\n}\r\n\r\n// Utility functions\r\nfunction CreateShapePath(\r\n    char: string,\r\n    scale: number,\r\n    offsetX: number,\r\n    offsetY: number,\r\n    resolution: number,\r\n    fontData: IFontData\r\n): Nullable<{\r\n    offsetX: number;\r\n    shapePath: ShapePath;\r\n}> {\r\n    const glyph = fontData.glyphs[char] || fontData.glyphs[\"?\"];\r\n\r\n    if (!glyph) {\r\n        // return if there is no glyph data\r\n        return null;\r\n    }\r\n\r\n    const shapePath = new ShapePath(resolution);\r\n\r\n    if (glyph.o) {\r\n        const outline = glyph.o.split(\" \");\r\n\r\n        for (let i = 0, l = outline.length; i < l; ) {\r\n            const action = outline[i++];\r\n\r\n            switch (action) {\r\n                case \"m\": {\r\n                    // moveTo\r\n                    const x = parseInt(outline[i++]) * scale + offsetX;\r\n                    const y = parseInt(outline[i++]) * scale + offsetY;\r\n\r\n                    shapePath.moveTo(x, y);\r\n                    break;\r\n                }\r\n                case \"l\": {\r\n                    // lineTo\r\n                    const x = parseInt(outline[i++]) * scale + offsetX;\r\n                    const y = parseInt(outline[i++]) * scale + offsetY;\r\n\r\n                    shapePath.lineTo(x, y);\r\n                    break;\r\n                }\r\n                case \"q\": {\r\n                    // quadraticCurveTo\r\n                    const cpx = parseInt(outline[i++]) * scale + offsetX;\r\n                    const cpy = parseInt(outline[i++]) * scale + offsetY;\r\n                    const cpx1 = parseInt(outline[i++]) * scale + offsetX;\r\n                    const cpy1 = parseInt(outline[i++]) * scale + offsetY;\r\n\r\n                    shapePath.quadraticCurveTo(cpx1, cpy1, cpx, cpy);\r\n                    break;\r\n                }\r\n                case \"b\": {\r\n                    // bezierCurveTo\r\n                    const cpx = parseInt(outline[i++]) * scale + offsetX;\r\n                    const cpy = parseInt(outline[i++]) * scale + offsetY;\r\n                    const cpx1 = parseInt(outline[i++]) * scale + offsetX;\r\n                    const cpy1 = parseInt(outline[i++]) * scale + offsetY;\r\n                    const cpx2 = parseInt(outline[i++]) * scale + offsetX;\r\n                    const cpy2 = parseInt(outline[i++]) * scale + offsetY;\r\n\r\n                    shapePath.bezierCurveTo(cpx1, cpy1, cpx2, cpy2, cpx, cpy);\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // Extract holes (based on clockwise data)\r\n    shapePath.extractHoles();\r\n\r\n    return { offsetX: glyph.ha * scale, shapePath: shapePath };\r\n}\r\n\r\n/**\r\n * Creates shape paths from a text and font\r\n * @param text the text\r\n * @param size size of the font\r\n * @param resolution resolution of the font\r\n * @param fontData defines the font data (can be generated with http://gero3.github.io/facetype.js/)\r\n * @returns array of ShapePath objects\r\n */\r\nexport function CreateTextShapePaths(text: string, size: number, resolution: number, fontData: IFontData) {\r\n    const chars = Array.from(text);\r\n    const scale = size / fontData.resolution;\r\n    const line_height = (fontData.boundingBox.yMax - fontData.boundingBox.yMin + fontData.underlineThickness) * scale;\r\n\r\n    const shapePaths: ShapePath[] = [];\r\n\r\n    let offsetX = 0,\r\n        offsetY = 0;\r\n\r\n    for (let i = 0; i < chars.length; i++) {\r\n        const char = chars[i];\r\n\r\n        if (char === \"\\n\") {\r\n            offsetX = 0;\r\n            offsetY -= line_height;\r\n        } else {\r\n            const ret = CreateShapePath(char, scale, offsetX, offsetY, resolution, fontData);\r\n\r\n            if (ret) {\r\n                offsetX += ret.offsetX;\r\n                shapePaths.push(ret.shapePath);\r\n            }\r\n        }\r\n    }\r\n\r\n    return shapePaths;\r\n}\r\n\r\n/**\r\n * Create a text mesh\r\n * @param name defines the name of the mesh\r\n * @param text defines the text to use to build the mesh\r\n * @param fontData defines the font data (can be generated with http://gero3.github.io/facetype.js/)\r\n * @param options defines options used to create the mesh\r\n * @param scene defines the hosting scene\r\n * @param earcutInjection can be used to inject your own earcut reference\r\n * @returns a new Mesh\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set/text\r\n */\r\nexport function CreateText(\r\n    name: string,\r\n    text: string,\r\n    fontData: IFontData,\r\n    options: {\r\n        size?: number;\r\n        resolution?: number;\r\n        depth?: number;\r\n        sideOrientation?: number;\r\n        faceUV?: Vector4[];\r\n        faceColors?: Color4[];\r\n        perLetterFaceUV?: (letterIndex: number) => Vector4[];\r\n        perLetterFaceColors?: (letterIndex: number) => Color4[];\r\n    } = {\r\n        size: 50,\r\n        resolution: 8,\r\n        depth: 1.0,\r\n    },\r\n    scene: Nullable<Scene> = null,\r\n    earcutInjection = earcut\r\n): Nullable<Mesh> {\r\n    // First we need to generate the paths\r\n    const shapePaths = CreateTextShapePaths(text, options.size || 50, options.resolution || 8, fontData);\r\n\r\n    // And extrude them\r\n    const meshes: Mesh[] = [];\r\n    let letterIndex = 0;\r\n    for (const shapePath of shapePaths) {\r\n        if (!shapePath.paths.length) {\r\n            continue;\r\n        }\r\n\r\n        const holes = shapePath.holes.slice(); // Copy it as we will update the copy\r\n        for (const path of shapePath.paths) {\r\n            const holeVectors: Vector3[][] = [];\r\n            const shapeVectors: Vector3[] = [];\r\n            const points = path.getPoints();\r\n            for (const point of points) {\r\n                shapeVectors.push(new Vector3(point.x, 0, point.y)); // ExtrudePolygon expects data on the xz plane\r\n            }\r\n\r\n            // Holes\r\n            const localHolesCopy = holes.slice();\r\n            for (const hole of localHolesCopy) {\r\n                const points = hole.getPoints();\r\n\r\n                let found = false;\r\n                for (const point of points) {\r\n                    if (path.isPointInside(point)) {\r\n                        found = true;\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                if (!found) {\r\n                    continue;\r\n                }\r\n\r\n                const holePoints: Vector3[] = [];\r\n                for (const point of points) {\r\n                    holePoints.push(new Vector3(point.x, 0, point.y)); // ExtrudePolygon expects data on the xz plane\r\n                }\r\n                holeVectors.push(holePoints);\r\n\r\n                // Remove the hole as it was already used\r\n                holes.splice(holes.indexOf(hole), 1);\r\n            }\r\n\r\n            // There is at least a hole but it was unaffected\r\n            if (!holeVectors.length && holes.length) {\r\n                for (const hole of holes) {\r\n                    const points = hole.getPoints();\r\n                    const holePoints: Vector3[] = [];\r\n                    for (const point of points) {\r\n                        holePoints.push(new Vector3(point.x, 0, point.y)); // ExtrudePolygon expects data on the xz plane\r\n                    }\r\n                    holeVectors.push(holePoints);\r\n                }\r\n            }\r\n\r\n            // Extrusion!\r\n            const mesh = ExtrudePolygon(\r\n                name,\r\n                {\r\n                    shape: shapeVectors,\r\n                    holes: holeVectors.length ? holeVectors : undefined,\r\n                    depth: options.depth || 1.0,\r\n                    faceUV: options.faceUV || options.perLetterFaceUV?.(letterIndex),\r\n                    faceColors: options.faceColors || options.perLetterFaceColors?.(letterIndex),\r\n                    sideOrientation: Mesh._GetDefaultSideOrientation(options.sideOrientation || Mesh.DOUBLESIDE),\r\n                },\r\n                scene,\r\n                earcutInjection\r\n            );\r\n            meshes.push(mesh);\r\n            letterIndex++;\r\n        }\r\n    }\r\n\r\n    // Then we can merge everyone into one single mesh\r\n    const newMesh = Mesh.MergeMeshes(meshes, true, true);\r\n\r\n    if (newMesh) {\r\n        // Move pivot to desired center / bottom / center position\r\n        const bbox = newMesh.getBoundingInfo().boundingBox;\r\n        newMesh.position.x += -(bbox.minimumWorld.x + bbox.maximumWorld.x) / 2; // Mid X\r\n        newMesh.position.y += -(bbox.minimumWorld.y + bbox.maximumWorld.y) / 2; // Mid Z as it will rotate\r\n        newMesh.position.z += -(bbox.minimumWorld.z + bbox.maximumWorld.z) / 2 + bbox.extendSize.z; // Bottom Y as it will rotate\r\n        newMesh.name = name;\r\n\r\n        // Rotate 90° Up\r\n        const pivot = new TransformNode(\"pivot\", scene);\r\n        pivot.rotation.x = -Math.PI / 2;\r\n        newMesh.parent = pivot;\r\n\r\n        newMesh.bakeCurrentTransformIntoVertices();\r\n\r\n        // Remove the pivot\r\n        newMesh.parent = null;\r\n        pivot.dispose();\r\n    }\r\n\r\n    return newMesh;\r\n}\r\n"]}