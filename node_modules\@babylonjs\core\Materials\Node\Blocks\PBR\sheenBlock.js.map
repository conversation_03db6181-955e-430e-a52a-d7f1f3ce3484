{"version": 3, "file": "sheenBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/PBR/sheenBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,oCAAoC,EAAE,MAAM,wCAAwC,CAAC;AAC9F,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AACtG,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AAOxG;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,iBAAiB;IAC7C;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAgBnD;;;;WAIG;QAEI,kBAAa,GAAY,KAAK,CAAC;QAEtC;;WAEG;QAEI,wBAAmB,GAAY,KAAK,CAAC;QA1BxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACtH,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,MAAM,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACnH,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAEtH,IAAI,CAAC,cAAc,CACf,OAAO,EACP,qCAAqC,CAAC,MAAM,EAC5C,wBAAwB,CAAC,QAAQ,EACjC,IAAI,uCAAuC,CAAC,OAAO,EAAE,IAAI,EAAE,oCAAoC,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,CAAC,CACpI,CAAC;IACN,CAAC;IAgBD;;;OAGG;IACI,UAAU,CAAC,KAA6B;QAC3C,KAAK,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACvC,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,cAAc,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QAC9F,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;QAElD,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAChC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACrE,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QACzE,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACtE,OAAO,CAAC,QAAQ,CAAC,qBAAqB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;IACtE,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,eAA0C;QACrD,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC;QACtF,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5F,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5F,MAAM,OAAO,GAAG,UAAU,CAAC;QAE3B,IAAI,GAAG;;;sCAGuB,KAAK,KAAK,SAAS;;;;;kBAKvC,SAAS;;;;kBAIT,OAAO;;;;;;;;;;;;;;kBAcP,eAAe,EAAE,iCAAiC;kBAClD,eAAe,EAAE,qBAAqB;kBACtC,eAAe,EAAE,eAAe;;yBAEzB,eAAe,EAAE,aAAa;sBACjC,eAAe,EAAE,gBAAgB;;sBAEjC,eAAe,EAAE,cAAc;;;;;6BAKxB,eAAe,EAAE,aAAa;0BACjC,eAAe,EAAE,gBAAgB;0BACjC,eAAe,EAAE,gBAAgB;;0BAEjC,eAAe,EAAE,cAAc;0BAC/B,eAAe,EAAE,cAAc;;;+BAG1B,eAAe,EAAE,iBAAiB;;;+BAGlC,eAAe,EAAE,iBAAiB,8DAA8D,eAAe,EAAE,aAAa;;;;;;;;;;iBAU5I,CAAC;QAEV,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACpD,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,oBAAoB,IAAI,CAAC,aAAa,KAAK,CAAC;QACnF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,0BAA0B,IAAI,CAAC,mBAAmB,KAAK,CAAC;QAE/F,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvD,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAEnE,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC,aAAa,CAAC;QACvD,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,mBAAmB,CAAC;IACvE,CAAC;CACJ;AA9KU;IADN,sBAAsB,CAAC,gBAAgB,EAAE,sBAAsB,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;iDAClF;AAM/B;IADN,sBAAsB,CAAC,wBAAwB,EAAE,sBAAsB,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;uDACpF;AA0KhD,aAAa,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport type { ReflectionBlock } from \"./reflectionBlock\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport type { Nullable } from \"../../../../types\";\r\n\r\n/**\r\n * Block used to implement the sheen module of the PBR material\r\n */\r\nexport class SheenBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new SheenBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"intensity\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"color\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"roughness\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\r\n            \"sheen\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"sheen\", this, NodeMaterialConnectionPointDirection.Output, SheenBlock, \"SheenBlock\")\r\n        );\r\n    }\r\n\r\n    /**\r\n     * If true, the sheen effect is layered above the base BRDF with the albedo-scaling technique.\r\n     * It allows the strength of the sheen effect to not depend on the base color of the material,\r\n     * making it easier to setup and tweak the effect\r\n     */\r\n    @editableInPropertyPage(\"Albedo scaling\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { notifiers: { update: true } })\r\n    public albedoScaling: boolean = false;\r\n\r\n    /**\r\n     * Defines if the sheen is linked to the sheen color.\r\n     */\r\n    @editableInPropertyPage(\"Link sheen with albedo\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { notifiers: { update: true } })\r\n    public linkSheenWithAlbedo: boolean = false;\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"sheenOut\");\r\n        state._excludeVariableName(\"sheenMapData\");\r\n        state._excludeVariableName(\"vSheenColor\");\r\n        state._excludeVariableName(\"vSheenRoughness\");\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"SheenBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the intensity input component\r\n     */\r\n    public get intensity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the color input component\r\n     */\r\n    public get color(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the roughness input component\r\n     */\r\n    public get roughness(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the sheen object output component\r\n     */\r\n    public get sheen(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public prepareDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        super.prepareDefines(mesh, nodeMaterial, defines);\r\n\r\n        defines.setValue(\"SHEEN\", true);\r\n        defines.setValue(\"SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE\", true, true);\r\n        defines.setValue(\"SHEEN_LINKWITHALBEDO\", this.linkSheenWithAlbedo, true);\r\n        defines.setValue(\"SHEEN_ROUGHNESS\", this.roughness.isConnected, true);\r\n        defines.setValue(\"SHEEN_ALBEDOSCALING\", this.albedoScaling, true);\r\n    }\r\n\r\n    /**\r\n     * Gets the main code of the block (fragment side)\r\n     * @param reflectionBlock instance of a ReflectionBlock null if the code must be generated without an active reflection module\r\n     * @returns the shader code\r\n     */\r\n    public getCode(reflectionBlock: Nullable<ReflectionBlock>): string {\r\n        let code = \"\";\r\n\r\n        const color = this.color.isConnected ? this.color.associatedVariableName : \"vec3(1.)\";\r\n        const intensity = this.intensity.isConnected ? this.intensity.associatedVariableName : \"1.\";\r\n        const roughness = this.roughness.isConnected ? this.roughness.associatedVariableName : \"0.\";\r\n        const texture = \"vec4(0.)\";\r\n\r\n        code = `#ifdef SHEEN\r\n            sheenOutParams sheenOut;\r\n\r\n            vec4 vSheenColor = vec4(${color}, ${intensity});\r\n\r\n            sheenBlock(\r\n                vSheenColor,\r\n            #ifdef SHEEN_ROUGHNESS\r\n                ${roughness},\r\n            #endif\r\n                roughness,\r\n            #ifdef SHEEN_TEXTURE\r\n                ${texture},\r\n                1.0,\r\n            #endif\r\n                reflectance,\r\n            #ifdef SHEEN_LINKWITHALBEDO\r\n                baseColor,\r\n                surfaceAlbedo,\r\n            #endif\r\n            #ifdef ENVIRONMENTBRDF\r\n                NdotV,\r\n                environmentBrdf,\r\n            #endif\r\n            #if defined(REFLECTION) && defined(ENVIRONMENTBRDF)\r\n                AARoughnessFactors,\r\n                ${reflectionBlock?._vReflectionMicrosurfaceInfosName},\r\n                ${reflectionBlock?._vReflectionInfosName},\r\n                ${reflectionBlock?.reflectionColor},\r\n                vLightingIntensity,\r\n                #ifdef ${reflectionBlock?._define3DName}\r\n                    ${reflectionBlock?._cubeSamplerName},\r\n                #else\r\n                    ${reflectionBlock?._2DSamplerName},\r\n                #endif\r\n                reflectionOut.reflectionCoords,\r\n                NdotVUnclamped,\r\n                #ifndef LODBASEDMICROSFURACE\r\n                    #ifdef ${reflectionBlock?._define3DName}\r\n                        ${reflectionBlock?._cubeSamplerName},\r\n                        ${reflectionBlock?._cubeSamplerName},\r\n                    #else\r\n                        ${reflectionBlock?._2DSamplerName},\r\n                        ${reflectionBlock?._2DSamplerName},\r\n                    #endif\r\n                #endif\r\n                #if !defined(${reflectionBlock?._defineSkyboxName}) && defined(RADIANCEOCCLUSION)\r\n                    seo,\r\n                #endif\r\n                #if !defined(${reflectionBlock?._defineSkyboxName}) && defined(HORIZONOCCLUSION) && defined(BUMP) && defined(${reflectionBlock?._define3DName})\r\n                    eho,\r\n                #endif\r\n            #endif\r\n                sheenOut\r\n            );\r\n\r\n            #ifdef SHEEN_LINKWITHALBEDO\r\n                surfaceAlbedo = sheenOut.surfaceAlbedo;\r\n            #endif\r\n        #endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.sharedData.blocksWithDefines.push(this);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        codeString += `${this._codeVariableName}.albedoScaling = ${this.albedoScaling};\\n`;\r\n        codeString += `${this._codeVariableName}.linkSheenWithAlbedo = ${this.linkSheenWithAlbedo};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.albedoScaling = this.albedoScaling;\r\n        serializationObject.linkSheenWithAlbedo = this.linkSheenWithAlbedo;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.albedoScaling = serializationObject.albedoScaling;\r\n        this.linkSheenWithAlbedo = serializationObject.linkSheenWithAlbedo;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SheenBlock\", SheenBlock);\r\n"]}