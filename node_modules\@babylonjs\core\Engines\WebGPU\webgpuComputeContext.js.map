{"version": 3, "file": "webgpuComputeContext.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuComputeContext.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAC;AAExE,OAAO,KAAK,eAAe,MAAM,mBAAmB,CAAC;AAIrD,gBAAgB;AAChB,MAAM,OAAO,oBAAoB;IAUtB,aAAa,CAAC,QAA4B,EAAE,eAAmC,EAAE,eAAuC;QAC3H,IAAI,CAAC,eAAe,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,qHAAqH,CAAC,CAAC;SAC1I;QACD,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,MAAM,qBAAqB,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;YAChE,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE;gBACxB,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,EACzB,QAAQ,GAAG,eAAe,CAAC,GAAG,CAAC,EAC/B,KAAK,GAAG,QAAQ,CAAC,KAAK,EACtB,KAAK,GAAG,QAAQ,CAAC,OAAO,EACxB,IAAI,GAAG,OAAO,CAAC,IAAI,EACnB,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBAC5B,IAAI,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC;gBAEtD,IAAI,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAC5C,IAAI,CAAC,OAAO,EAAE;oBACV,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;iBAChD;gBAED,QAAQ,IAAI,EAAE;oBACV,KAAK,kBAAkB,CAAC,OAAO,CAAC,CAAC;wBAC7B,MAAM,OAAO,GAAG,MAAwB,CAAC;wBACzC,IAAI,mBAAmB,KAAK,SAAS,IAAI,qBAAqB,EAAE;4BAC5D,OAAO,CAAC,mBAAmB,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;yBAClF;6BAAM;4BACH,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,MAAM,CAAC;4BAC7C,OAAO,CAAC,IAAI,CAAC;gCACT,OAAO,EAAE,KAAK;gCACd,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC;6BACnD,CAAC,CAAC;yBACN;wBACD,MAAM;qBACT;oBAED,KAAK,kBAAkB,CAAC,OAAO,CAAC;oBAChC,KAAK,kBAAkB,CAAC,qBAAqB,CAAC,CAAC;wBAC3C,MAAM,OAAO,GAAG,MAAqB,CAAC;wBACtC,MAAM,eAAe,GAAG,OAAO,CAAC,QAAS,CAAC,gBAAyC,CAAC;wBACpF,IAAI,mBAAmB,KAAK,SAAS,IAAI,qBAAqB,EAAE;4BAC5D,IAAI,IAAI,KAAK,kBAAkB,CAAC,OAAO,EAAE;gCACrC,OAAO,CAAC,mBAAmB,EAAE,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,QAAS,CAAC,CAAC;6BAC9F;4BACD,OAAO,CAAC,mBAAmB,CAAC,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAK,CAAC;yBACjE;6BAAM;4BACH,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,MAAM,CAAC;4BAC7C,IAAI,IAAI,KAAK,kBAAkB,CAAC,OAAO,EAAE;gCACrC,OAAO,CAAC,IAAI,CAAC;oCACT,OAAO,EAAE,KAAK,GAAG,CAAC;oCAClB,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,QAAS,CAAC;iCAC7D,CAAC,CAAC;6BACN;4BACD,OAAO,CAAC,IAAI,CAAC;gCACT,OAAO,EAAE,KAAK;gCACd,QAAQ,EAAE,eAAe,CAAC,IAAK;6BAClC,CAAC,CAAC;yBACN;wBACD,MAAM;qBACT;oBAED,KAAK,kBAAkB,CAAC,cAAc,CAAC,CAAC;wBACpC,MAAM,OAAO,GAAG,MAAqB,CAAC;wBACtC,MAAM,eAAe,GAAG,OAAO,CAAC,QAAS,CAAC,gBAAyC,CAAC;wBACpF,IAAI,CAAC,eAAe,CAAC,uBAAuB,GAAG,eAAe,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;4BAC/F,MAAM,CAAC,KAAK,CAAC,sCAAsC,OAAO,CAAC,IAAI,cAAc,OAAO,CAAC,QAAQ,6BAA6B,EAAE,EAAE,CAAC,CAAC;yBACnI;wBACD,IAAI,mBAAmB,KAAK,SAAS,IAAI,qBAAqB,EAAE;4BAC5D,OAAO,CAAC,mBAAmB,CAAC,CAAC,QAAQ,GAAG,eAAe,CAAC,cAAe,CAAC;yBAC3E;6BAAM;4BACH,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,MAAM,CAAC;4BAC7C,OAAO,CAAC,IAAI,CAAC;gCACT,OAAO,EAAE,KAAK;gCACd,QAAQ,EAAE,eAAe,CAAC,cAAe;6BAC5C,CAAC,CAAC;yBACN;wBACD,MAAM;qBACT;oBAED,KAAK,kBAAkB,CAAC,eAAe,CAAC,CAAC;wBACrC,MAAM,OAAO,GAAG,MAAyB,CAAC;wBAC1C,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,CAAC;wBACnD,IAAI,mBAAmB,KAAK,SAAS,IAAI,qBAAqB,EAAE;4BAC5D,OAAO,CAAC,mBAAmB,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC,CAAC;yBAC3G;6BAAM;4BACH,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,MAAM,CAAC;4BAC7C,OAAO,CAAC,IAAI,CAAC;gCACT,OAAO,EAAE,KAAK;gCACd,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC;6BAC5E,CAAC,CAAC;yBACN;wBACD,MAAM;qBACT;oBAED,KAAK,kBAAkB,CAAC,aAAa,CAAC;oBACtC,KAAK,kBAAkB,CAAC,aAAa,CAAC;oBACtC,KAAK,kBAAkB,CAAC,UAAU,CAAC,CAAC;wBAChC,MAAM,UAAU,GACZ,IAAI,KAAK,kBAAkB,CAAC,UAAU;4BAClC,CAAC,CAAE,MAAqB;4BACxB,CAAC,CAAC,IAAI,KAAK,kBAAkB,CAAC,aAAa;gCACzC,CAAC,CAAE,MAAwB,CAAC,SAAS,EAAG;gCACxC,CAAC,CAAE,MAAwB,CAAC,SAAS,EAAG,CAAC;wBACnD,MAAM,YAAY,GAAG,UAAU,CAAC,kBAA+B,CAAC;wBAChE,IAAI,mBAAmB,KAAK,SAAS,IAAI,qBAAqB,EAAE;4BAC3D,OAAO,CAAC,mBAAmB,CAAC,CAAC,QAA6B,CAAC,MAAM,GAAG,YAAY,CAAC;4BACjF,OAAO,CAAC,mBAAmB,CAAC,CAAC,QAA6B,CAAC,IAAI,GAAG,UAAU,CAAC,QAAQ,CAAC;yBAC1F;6BAAM;4BACH,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,MAAM,CAAC;4BAC7C,OAAO,CAAC,IAAI,CAAC;gCACT,OAAO,EAAE,KAAK;gCACd,QAAQ,EAAE;oCACN,MAAM,EAAE,YAAY;oCACpB,MAAM,EAAE,CAAC;oCACT,IAAI,EAAE,UAAU,CAAC,QAAQ;iCAC5B;6BACJ,CAAC,CAAC;yBACN;wBACD,MAAM;qBACT;iBACJ;aACJ;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACpD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBAC1C,IAAI,CAAC,OAAO,EAAE;oBACV,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,SAAgB,CAAC;oBACvC,SAAS;iBACZ;gBACD,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;oBAC/C,MAAM,EAAE,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC;oBAC7C,OAAO;iBACV,CAAC,CAAC;aACN;YAED,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;SAC3D;QAED,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,YAAY,MAAiB,EAAE,YAAgC;QAC3D,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,EAAE,CAAC;QAChD,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,uLAAuL;IAC3L,CAAC;;AAhKc,6BAAQ,GAAG,CAAC,CAAC", "sourcesContent": ["import type { DataBuffer } from \"core/Buffers/dataBuffer\";\r\nimport type { StorageBuffer } from \"../../Buffers/storageBuffer\";\r\nimport type { IComputeContext } from \"../../Compute/IComputeContext\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport type { TextureSampler } from \"../../Materials/Textures/textureSampler\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { ComputeBindingList, ComputeBindingMapping } from \"../Extensions/engine.computeShader\";\r\nimport { ComputeBindingType } from \"../Extensions/engine.computeShader\";\r\nimport type { WebGPUCacheSampler } from \"./webgpuCacheSampler\";\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport type { WebGPUHardwareTexture } from \"./webgpuHardwareTexture\";\r\nimport type { ExternalTexture } from \"core/Materials/Textures/externalTexture\";\r\n\r\n/** @internal */\r\nexport class WebGPUComputeContext implements IComputeContext {\r\n    private static _Counter = 0;\r\n\r\n    public readonly uniqueId: number;\r\n\r\n    private _device: GPUDevice;\r\n    private _cacheSampler: WebGPUCacheSampler;\r\n    private _bindGroups: GPUBindGroup[];\r\n    private _bindGroupEntries: GPUBindGroupEntry[][];\r\n\r\n    public getBindGroups(bindings: ComputeBindingList, computePipeline: GPUComputePipeline, bindingsMapping?: ComputeBindingMapping): GPUBindGroup[] {\r\n        if (!bindingsMapping) {\r\n            throw new Error(\"WebGPUComputeContext.getBindGroups: bindingsMapping is required until browsers support reflection for wgsl shaders!\");\r\n        }\r\n        if (this._bindGroups.length === 0) {\r\n            const bindGroupEntriesExist = this._bindGroupEntries.length > 0;\r\n            for (const key in bindings) {\r\n                const binding = bindings[key],\r\n                    location = bindingsMapping[key],\r\n                    group = location.group,\r\n                    index = location.binding,\r\n                    type = binding.type,\r\n                    object = binding.object;\r\n                let indexInGroupEntries = binding.indexInGroupEntries;\r\n\r\n                let entries = this._bindGroupEntries[group];\r\n                if (!entries) {\r\n                    entries = this._bindGroupEntries[group] = [];\r\n                }\r\n\r\n                switch (type) {\r\n                    case ComputeBindingType.Sampler: {\r\n                        const sampler = object as TextureSampler;\r\n                        if (indexInGroupEntries !== undefined && bindGroupEntriesExist) {\r\n                            entries[indexInGroupEntries].resource = this._cacheSampler.getSampler(sampler);\r\n                        } else {\r\n                            binding.indexInGroupEntries = entries.length;\r\n                            entries.push({\r\n                                binding: index,\r\n                                resource: this._cacheSampler.getSampler(sampler),\r\n                            });\r\n                        }\r\n                        break;\r\n                    }\r\n\r\n                    case ComputeBindingType.Texture:\r\n                    case ComputeBindingType.TextureWithoutSampler: {\r\n                        const texture = object as BaseTexture;\r\n                        const hardwareTexture = texture._texture!._hardwareTexture as WebGPUHardwareTexture;\r\n                        if (indexInGroupEntries !== undefined && bindGroupEntriesExist) {\r\n                            if (type === ComputeBindingType.Texture) {\r\n                                entries[indexInGroupEntries++].resource = this._cacheSampler.getSampler(texture._texture!);\r\n                            }\r\n                            entries[indexInGroupEntries].resource = hardwareTexture.view!;\r\n                        } else {\r\n                            binding.indexInGroupEntries = entries.length;\r\n                            if (type === ComputeBindingType.Texture) {\r\n                                entries.push({\r\n                                    binding: index - 1,\r\n                                    resource: this._cacheSampler.getSampler(texture._texture!),\r\n                                });\r\n                            }\r\n                            entries.push({\r\n                                binding: index,\r\n                                resource: hardwareTexture.view!,\r\n                            });\r\n                        }\r\n                        break;\r\n                    }\r\n\r\n                    case ComputeBindingType.StorageTexture: {\r\n                        const texture = object as BaseTexture;\r\n                        const hardwareTexture = texture._texture!._hardwareTexture as WebGPUHardwareTexture;\r\n                        if ((hardwareTexture.textureAdditionalUsages & WebGPUConstants.TextureUsage.StorageBinding) === 0) {\r\n                            Logger.Error(`computeDispatch: The texture (name=${texture.name}, uniqueId=${texture.uniqueId}) is not a storage texture!`, 50);\r\n                        }\r\n                        if (indexInGroupEntries !== undefined && bindGroupEntriesExist) {\r\n                            entries[indexInGroupEntries].resource = hardwareTexture.viewForWriting!;\r\n                        } else {\r\n                            binding.indexInGroupEntries = entries.length;\r\n                            entries.push({\r\n                                binding: index,\r\n                                resource: hardwareTexture.viewForWriting!,\r\n                            });\r\n                        }\r\n                        break;\r\n                    }\r\n\r\n                    case ComputeBindingType.ExternalTexture: {\r\n                        const texture = object as ExternalTexture;\r\n                        const externalTexture = texture.underlyingResource;\r\n                        if (indexInGroupEntries !== undefined && bindGroupEntriesExist) {\r\n                            entries[indexInGroupEntries].resource = this._device.importExternalTexture({ source: externalTexture });\r\n                        } else {\r\n                            binding.indexInGroupEntries = entries.length;\r\n                            entries.push({\r\n                                binding: index,\r\n                                resource: this._device.importExternalTexture({ source: externalTexture }),\r\n                            });\r\n                        }\r\n                        break;\r\n                    }\r\n\r\n                    case ComputeBindingType.UniformBuffer:\r\n                    case ComputeBindingType.StorageBuffer:\r\n                    case ComputeBindingType.DataBuffer: {\r\n                        const dataBuffer =\r\n                            type === ComputeBindingType.DataBuffer\r\n                                ? (object as DataBuffer)\r\n                                : type === ComputeBindingType.UniformBuffer\r\n                                  ? (object as UniformBuffer).getBuffer()!\r\n                                  : (object as StorageBuffer).getBuffer()!;\r\n                        const webgpuBuffer = dataBuffer.underlyingResource as GPUBuffer;\r\n                        if (indexInGroupEntries !== undefined && bindGroupEntriesExist) {\r\n                            (entries[indexInGroupEntries].resource as GPUBufferBinding).buffer = webgpuBuffer;\r\n                            (entries[indexInGroupEntries].resource as GPUBufferBinding).size = dataBuffer.capacity;\r\n                        } else {\r\n                            binding.indexInGroupEntries = entries.length;\r\n                            entries.push({\r\n                                binding: index,\r\n                                resource: {\r\n                                    buffer: webgpuBuffer,\r\n                                    offset: 0,\r\n                                    size: dataBuffer.capacity,\r\n                                },\r\n                            });\r\n                        }\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n\r\n            for (let i = 0; i < this._bindGroupEntries.length; ++i) {\r\n                const entries = this._bindGroupEntries[i];\r\n                if (!entries) {\r\n                    this._bindGroups[i] = undefined as any;\r\n                    continue;\r\n                }\r\n                this._bindGroups[i] = this._device.createBindGroup({\r\n                    layout: computePipeline.getBindGroupLayout(i),\r\n                    entries,\r\n                });\r\n            }\r\n\r\n            this._bindGroups.length = this._bindGroupEntries.length;\r\n        }\r\n\r\n        return this._bindGroups;\r\n    }\r\n\r\n    constructor(device: GPUDevice, cacheSampler: WebGPUCacheSampler) {\r\n        this._device = device;\r\n        this._cacheSampler = cacheSampler;\r\n        this.uniqueId = WebGPUComputeContext._Counter++;\r\n        this._bindGroupEntries = [];\r\n        this.clear();\r\n    }\r\n\r\n    public clear(): void {\r\n        this._bindGroups = [];\r\n        // Don't reset _bindGroupEntries if they have already been created, they are still ok even if we have to clear _bindGroups (the layout of the compute shader can't change once created)\r\n    }\r\n}\r\n"]}