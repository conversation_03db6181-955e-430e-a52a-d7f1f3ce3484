{"version": 3, "file": "pbrBRDFConfiguration.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/PBR/pbrBRDFConfiguration.ts"], "names": [], "mappings": ";AAAA,yDAAyD;AACzD,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACpE,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAG3D;;GAEG;AACH,MAAM,OAAO,mBAAoB,SAAQ,eAAe;IAAxD;;QACI,6BAAwB,GAAG,KAAK,CAAC;QACjC,gCAA2B,GAAG,KAAK,CAAC;QACpC,wBAAmB,GAAG,KAAK,CAAC;QAC5B,4CAAuC,GAAG,KAAK,CAAC;IACpD,CAAC;CAAA;AAED;;GAEG;AACH,MAAM,OAAO,oBAAqB,SAAQ,kBAAkB;IA0ExD,gBAAgB;IACT,4BAA4B;QAC/B,IAAI,CAAC,oCAAoC,EAAE,CAAC;IAChD,CAAC;IAED,YAAY,QAAyB,EAAE,eAAe,GAAG,IAAI;QACzD,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,mBAAmB,EAAE,EAAE,eAAe,CAAC,CAAC;QArDvE,2BAAsB,GAAG,oBAAoB,CAAC,+BAA+B,CAAC;QACtF;;WAEG;QAGI,0BAAqB,GAAG,oBAAoB,CAAC,+BAA+B,CAAC;QAE5E,wCAAmC,GAAG,oBAAoB,CAAC,8CAA8C,CAAC;QAClH;;;;;;;WAOG;QAGI,uCAAkC,GAAG,oBAAoB,CAAC,8CAA8C,CAAC;QAExG,2BAAsB,GAAG,oBAAoB,CAAC,+BAA+B,CAAC;QACtF;;;;;;WAMG;QAGI,0BAAqB,GAAG,oBAAoB,CAAC,+BAA+B,CAAC;QAE5E,kDAA6C,GAAG,oBAAoB,CAAC,yDAAyD,CAAC;QACvI;;;;;WAKG;QAGI,iDAA4C,GAAG,oBAAoB,CAAC,yDAAyD,CAAC;QAajI,IAAI,CAAC,oCAAoC,GAAG,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;QACvG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAEM,cAAc,CAAC,OAA4B;QAC9C,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC,mCAAmC,CAAC;QAC5E,OAAO,CAAC,2BAA2B,GAAG,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,mCAAmC,CAAC;QAC9G,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAC1D,OAAO,CAAC,uCAAuC,GAAG,IAAI,CAAC,6CAA6C,CAAC;IACzG,CAAC;IAEM,YAAY;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;;AA9FD;;;GAGG;AACW,oDAA+B,GAAG,IAAI,AAAP,CAAQ;AAErD;;;GAGG;AACW,mEAA8C,GAAG,IAAI,AAAP,CAAQ;AAEpE;;;;GAIG;AACW,oDAA+B,GAAG,IAAI,AAAP,CAAQ;AAErD;;;;GAIG;AACW,8EAAyD,GAAG,IAAI,AAAP,CAAQ;AAQxE;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,8BAA8B,CAAC;mEACmC;AAa7E;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,8BAA8B,CAAC;gFAC+D;AAYzG;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,8BAA8B,CAAC;mEACmC;AAW7E;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,8BAA8B,CAAC;0FACoF", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { serialize, expandToProperty } from \"../../Misc/decorators\";\r\nimport { MaterialDefines } from \"../materialDefines\";\r\nimport { MaterialPluginBase } from \"../materialPluginBase\";\r\nimport type { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class MaterialBRDFDefines extends MaterialDefines {\r\n    BRDF_V_HEIGHT_CORRELATED = false;\r\n    MS_BRDF_ENERGY_CONSERVATION = false;\r\n    SPHERICAL_HARMONICS = false;\r\n    SPECULAR_GLOSSINESS_ENERGY_CONSERVATION = false;\r\n}\r\n\r\n/**\r\n * Plugin that implements the BRDF component of the PBR material\r\n */\r\nexport class PBRBRDFConfiguration extends MaterialPluginBase {\r\n    /**\r\n     * Default value used for the energy conservation.\r\n     * This should only be changed to adapt to the type of texture in scene.environmentBRDFTexture.\r\n     */\r\n    public static DEFAULT_USE_ENERGY_CONSERVATION = true;\r\n\r\n    /**\r\n     * Default value used for the Smith Visibility Height Correlated mode.\r\n     * This should only be changed to adapt to the type of texture in scene.environmentBRDFTexture.\r\n     */\r\n    public static DEFAULT_USE_SMITH_VISIBILITY_HEIGHT_CORRELATED = true;\r\n\r\n    /**\r\n     * Default value used for the IBL diffuse part.\r\n     * This can help switching back to the polynomials mode globally which is a tiny bit\r\n     * less GPU intensive at the drawback of a lower quality.\r\n     */\r\n    public static DEFAULT_USE_SPHERICAL_HARMONICS = true;\r\n\r\n    /**\r\n     * Default value used for activating energy conservation for the specular workflow.\r\n     * If activated, the albedo color is multiplied with (1. - maxChannel(specular color)).\r\n     * If deactivated, a material is only physically plausible, when (albedo color + specular color) < 1.\r\n     */\r\n    public static DEFAULT_USE_SPECULAR_GLOSSINESS_INPUT_ENERGY_CONSERVATION = true;\r\n\r\n    private _useEnergyConservation = PBRBRDFConfiguration.DEFAULT_USE_ENERGY_CONSERVATION;\r\n    /**\r\n     * Defines if the material uses energy conservation.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public useEnergyConservation = PBRBRDFConfiguration.DEFAULT_USE_ENERGY_CONSERVATION;\r\n\r\n    private _useSmithVisibilityHeightCorrelated = PBRBRDFConfiguration.DEFAULT_USE_SMITH_VISIBILITY_HEIGHT_CORRELATED;\r\n    /**\r\n     * LEGACY Mode set to false\r\n     * Defines if the material uses height smith correlated visibility term.\r\n     * If you intent to not use our default BRDF, you need to load a separate BRDF Texture for the PBR\r\n     * You can either load https://assets.babylonjs.com/environments/uncorrelatedBRDF.png\r\n     * or https://assets.babylonjs.com/environments/uncorrelatedBRDF.dds to have more precision\r\n     * Not relying on height correlated will also disable energy conservation.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public useSmithVisibilityHeightCorrelated = PBRBRDFConfiguration.DEFAULT_USE_SMITH_VISIBILITY_HEIGHT_CORRELATED;\r\n\r\n    private _useSphericalHarmonics = PBRBRDFConfiguration.DEFAULT_USE_SPHERICAL_HARMONICS;\r\n    /**\r\n     * LEGACY Mode set to false\r\n     * Defines if the material uses spherical harmonics vs spherical polynomials for the\r\n     * diffuse part of the IBL.\r\n     * The harmonics despite a tiny bigger cost has been proven to provide closer results\r\n     * to the ground truth.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public useSphericalHarmonics = PBRBRDFConfiguration.DEFAULT_USE_SPHERICAL_HARMONICS;\r\n\r\n    private _useSpecularGlossinessInputEnergyConservation = PBRBRDFConfiguration.DEFAULT_USE_SPECULAR_GLOSSINESS_INPUT_ENERGY_CONSERVATION;\r\n    /**\r\n     * Defines if the material uses energy conservation, when the specular workflow is active.\r\n     * If activated, the albedo color is multiplied with (1. - maxChannel(specular color)).\r\n     * If deactivated, a material is only physically plausible, when (albedo color + specular color) < 1.\r\n     * In the deactivated case, the material author has to ensure energy conservation, for a physically plausible rendering.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public useSpecularGlossinessInputEnergyConservation = PBRBRDFConfiguration.DEFAULT_USE_SPECULAR_GLOSSINESS_INPUT_ENERGY_CONSERVATION;\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsMiscDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsMiscDirty(): void {\r\n        this._internalMarkAllSubMeshesAsMiscDirty();\r\n    }\r\n\r\n    constructor(material: PBRBaseMaterial, addToPluginList = true) {\r\n        super(material, \"PBRBRDF\", 90, new MaterialBRDFDefines(), addToPluginList);\r\n\r\n        this._internalMarkAllSubMeshesAsMiscDirty = material._dirtyCallbacks[Constants.MATERIAL_MiscDirtyFlag];\r\n        this._enable(true);\r\n    }\r\n\r\n    public prepareDefines(defines: MaterialBRDFDefines): void {\r\n        defines.BRDF_V_HEIGHT_CORRELATED = this._useSmithVisibilityHeightCorrelated;\r\n        defines.MS_BRDF_ENERGY_CONSERVATION = this._useEnergyConservation && this._useSmithVisibilityHeightCorrelated;\r\n        defines.SPHERICAL_HARMONICS = this._useSphericalHarmonics;\r\n        defines.SPECULAR_GLOSSINESS_ENERGY_CONSERVATION = this._useSpecularGlossinessInputEnergyConservation;\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"PBRBRDFConfiguration\";\r\n    }\r\n}\r\n"]}