{"version": 3, "file": "teleportOutBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Teleport/teleportOutBlock.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAGhF;;GAEG;AACH,MAAM,OAAO,4BAA6B,SAAQ,iBAAiB;IAM/D;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAVlD,gBAAgB;QACT,gBAAW,GAA0C,IAAI,CAAC;QACjE,gBAAgB;QACT,4BAAuB,GAAqB,IAAI,CAAC;QASpD,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,YAAY,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,8BAA8B,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;IACrE,CAAC;IAED,IAAW,MAAM,CAAC,KAA+B;QAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE;YAC9B,OAAO;SACV;QACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAED,8BAA8B;IACvB,MAAM;QACT,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO;SACV;QAED,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,sBAAsB,KAAK,CAAC;SAChI;IACL,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,KAAY,EAAE,UAAkB,EAAE;QAC3C,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,KAAqC,CAAC,CAAC;SAC3E;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAES,gBAAgB,CAAC,KAA6B,EAAE,YAAiC;QACvF,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;SAC9C;IACL,CAAC;IAEM,SAAS,CAAC,WAAqB,EAAE,aAAkC;QACtE,IAAI,UAAU,GAAW,EAAE,CAAC;QAC5B,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC/C,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;aACvE;SACJ;QAED,OAAO,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;IACpE,CAAC;IAEM,6BAA6B,CAAC,aAAkC;QACnE,IAAI,UAAU,GAAG,KAAK,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;QAEpE,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;SAC9E;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,UAAU,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,qBAAqB,IAAI,CAAC,iBAAiB,MAAM,CAAC;SACvG;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE,CAAC;QAEjE,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,uBAAuB,GAAG,mBAAmB,CAAC,UAAU,CAAC;IAClE,CAAC;CACJ;AAED,aAAa,CAAC,sCAAsC,EAAE,4BAA4B,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../../../types\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { NodeMaterialTeleportInBlock } from \"./teleportInBlock\";\r\nimport { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\n\r\n/**\r\n * Defines a block used to receive a value from a teleport entry point\r\n */\r\nexport class NodeMaterialTeleportOutBlock extends NodeMaterialBlock {\r\n    /** @internal */\r\n    public _entryPoint: Nullable<NodeMaterialTeleportInBlock> = null;\r\n    /** @internal */\r\n    public _tempEntryPointUniqueId: Nullable<number> = null;\r\n\r\n    /**\r\n     * Create a new TeleportOutBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.BasedOnInput);\r\n    }\r\n\r\n    /**\r\n     * Gets the entry point\r\n     */\r\n    public get entryPoint() {\r\n        return this._entryPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"NodeMaterialTeleportOutBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the target of the block\r\n     */\r\n    public get target() {\r\n        return this._entryPoint ? this._entryPoint.target : this._target;\r\n    }\r\n\r\n    public set target(value: NodeMaterialBlockTargets) {\r\n        if ((this._target & value) !== 0) {\r\n            return;\r\n        }\r\n        this._target = value;\r\n    }\r\n\r\n    /** Detach from entry point */\r\n    public detach() {\r\n        if (!this._entryPoint) {\r\n            return;\r\n        }\r\n\r\n        this._entryPoint.detachFromEndpoint(this);\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (this.entryPoint) {\r\n            state.compilationString += this._declareOutput(this.output, state) + ` = ${this.entryPoint.input.associatedVariableName};\\n`;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clone the current block to a new identical block\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a copy of the current block\r\n     */\r\n    public clone(scene: Scene, rootUrl: string = \"\") {\r\n        const clone = super.clone(scene, rootUrl);\r\n\r\n        if (this.entryPoint) {\r\n            this.entryPoint.attachToEndpoint(clone as NodeMaterialTeleportOutBlock);\r\n        }\r\n\r\n        return clone;\r\n    }\r\n\r\n    protected _customBuildStep(state: NodeMaterialBuildState, activeBlocks: NodeMaterialBlock[]): void {\r\n        if (this.entryPoint) {\r\n            this.entryPoint.build(state, activeBlocks);\r\n        }\r\n    }\r\n\r\n    public _dumpCode(uniqueNames: string[], alreadyDumped: NodeMaterialBlock[]) {\r\n        let codeString: string = \"\";\r\n        if (this.entryPoint) {\r\n            if (alreadyDumped.indexOf(this.entryPoint) === -1) {\r\n                codeString += this.entryPoint._dumpCode(uniqueNames, alreadyDumped);\r\n            }\r\n        }\r\n\r\n        return codeString + super._dumpCode(uniqueNames, alreadyDumped);\r\n    }\r\n\r\n    public _dumpCodeForOutputConnections(alreadyDumped: NodeMaterialBlock[]) {\r\n        let codeString = super._dumpCodeForOutputConnections(alreadyDumped);\r\n\r\n        if (this.entryPoint) {\r\n            codeString += this.entryPoint._dumpCodeForOutputConnections(alreadyDumped);\r\n        }\r\n\r\n        return codeString;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        if (this.entryPoint) {\r\n            codeString += `${this.entryPoint._codeVariableName}.attachToEndpoint(${this._codeVariableName});\\n`;\r\n        }\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Serializes this block in a JSON representation\r\n     * @returns the serialized block object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.entryPoint = this.entryPoint?.uniqueId ?? \"\";\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this._tempEntryPointUniqueId = serializationObject.entryPoint;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.NodeMaterialTeleportOutBlock\", NodeMaterialTeleportOutBlock);\r\n"]}