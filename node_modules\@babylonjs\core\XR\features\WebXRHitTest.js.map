{"version": 3, "file": "WebXRHitTest.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRHitTest.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAEjF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACtE,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAkEzC;;;;;;GAMG;AACH,MAAM,OAAO,YAAa,SAAQ,oBAAoB;IA0DlD;;;;OAIG;IACH,YACI,iBAAsC;IACtC;;OAEG;IACa,UAAgC,EAAE;QAElD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFT,YAAO,GAAP,OAAO,CAA2B;QAnE9C,YAAO,GAAW,IAAI,MAAM,EAAE,CAAC;QAC/B,YAAO,GAAY,IAAI,OAAO,EAAE,CAAC;QACjC,aAAQ,GAAe,IAAI,UAAU,EAAE,CAAC;QAIxC,uBAAkB,GAAG,CAAC,cAAgC,EAAE,EAAE;YAC9D,IAAI,CAAC,cAAc,EAAE;gBACjB,OAAO;aACV;YACD,MAAM,SAAS,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;YAC1D,MAAM,cAAc,GAAyB;gBACzC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB;gBACpG,SAAS,EAAE,SAAS;aACvB,CAAC;YACF,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;gBAC1B,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;aACzD;YACD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;gBACvB,KAAK,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;gBAC/D,OAAO;aACV;YACD,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,oBAAqB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,EAAE;gBACxF,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;iBAClC;gBACD,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC;YAC1C,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QAaF;;;;WAIG;QACI,4BAAuB,GAAY,KAAK,CAAC;QAChD;;;WAGG;QACI,8BAAyB,GAAkC,IAAI,UAAU,EAAE,CAAC;QACnF;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;QAe3B,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC;QACtC,KAAK,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;IACpE,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,qCAAqC;QACrC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,oBAAoB,EAAE;YACtD,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;YACvC,IAAI,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE;gBACvC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;aAClE;YACD,IAAI,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACjF;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YACrC,MAAM,SAAS,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC;YACnE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,qCAAsC,CAAC;gBAClE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,uBAAuB,IAAI,qBAAqB;gBACtE,SAAS;gBACT,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;aACxC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;gBAClB,IAAI,CAAC,yBAAyB,GAAG,SAAS,CAAC;YAC/C,CAAC,CAAC,CAAC;SACN;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC/B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;QACD,IAAI,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACzF,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,CAAC;YACxC,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;SACzC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;IAC3C,CAAC;IAES,UAAU,CAAC,KAAc;QAC/B,kDAAkD;QAClD,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,OAAO,GAAG,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC/D,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;SAC5C;QACD,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,MAAM,4BAA4B,GAAG,KAAK,CAAC,kCAAkC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAE9G,4BAA4B,CAAC,OAAO,CAAC,CAAC,qBAAqB,EAAE,EAAE;gBAC3D,IAAI,CAAC,0BAA0B,CAAC,qBAAqB,CAAC,OAAO,EAAE,qBAAqB,CAAC,WAAW,CAAC,CAAC;YACtG,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAEO,0BAA0B,CAAC,cAA0C,EAAE,WAA2B;QACtG,MAAM,OAAO,GAAsB,EAAE,CAAC;QACtC,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;YACrC,MAAM,IAAI,GAAG,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAC1E,IAAI,CAAC,IAAI,EAAE;gBACP,OAAO;aACV;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;YACpC,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;YACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAC9F,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,2BAA2B,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9E,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBACpD,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACrB,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACtB,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACtB,IAAI,CAAC,OAAO,CAAC,4BAA4B,EAAE,CAAC;aAC/C;YAED,MAAM,MAAM,GAAoB;gBAC5B,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;gBAC5E,kBAAkB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ;gBACxF,oBAAoB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;gBACxF,WAAW,EAAE,WAAW;gBACxB,WAAW,EAAE,CAAC,CAAC,WAAW;gBAC1B,WAAW,EAAE,aAAa;aAC7B,CAAC;YACF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;IAC5D,CAAC;;AAhKD;;GAEG;AACoB,iBAAI,GAAG,gBAAgB,CAAC,QAAQ,AAA5B,CAA6B;AACxD;;;;GAIG;AACoB,oBAAO,GAAG,CAAC,AAAJ,CAAK;AA0JvC,8BAA8B;AAC9B,oBAAoB,CAAC,eAAe,CAChC,YAAY,CAAC,IAAI,EACjB,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AAC7D,CAAC,EACD,YAAY,CAAC,OAAO,EACpB,KAAK,CACR,CAAC", "sourcesContent": ["import { WebXRFeaturesManager, WebXRFeatureName } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { Vector3, Matrix, Quaternion } from \"../../Maths/math.vector\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport type { IWebXRLegacyHitTestOptions, IWebXRLegacyHitResult, IWebXRHitTestFeature } from \"./WebXRHitTestLegacy\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * Options used for hit testing (version 2)\r\n */\r\nexport interface IWebXRHitTestOptions extends IWebXRLegacyHitTestOptions {\r\n    /**\r\n     * Do not create a permanent hit test. Will usually be used when only\r\n     * transient inputs are needed.\r\n     */\r\n    disablePermanentHitTest?: boolean;\r\n    /**\r\n     * Enable transient (for example touch-based) hit test inspections\r\n     */\r\n    enableTransientHitTest?: boolean;\r\n    /**\r\n     * Override the default transient hit test profile (generic-touchscreen).\r\n     */\r\n    transientHitTestProfile?: string;\r\n    /**\r\n     * Offset ray for the permanent hit test\r\n     */\r\n    offsetRay?: Vector3;\r\n    /**\r\n     * Offset ray for the transient hit test\r\n     */\r\n    transientOffsetRay?: Vector3;\r\n    /**\r\n     * Instead of using viewer space for hit tests, use the reference space defined in the session manager\r\n     */\r\n    useReferenceSpace?: boolean;\r\n\r\n    /**\r\n     * Override the default entity type(s) of the hit-test result\r\n     */\r\n    entityTypes?: XRHitTestTrackableType[];\r\n}\r\n\r\n/**\r\n * Interface defining the babylon result of hit-test\r\n */\r\nexport interface IWebXRHitResult extends IWebXRLegacyHitResult {\r\n    /**\r\n     * The input source that generated this hit test (if transient)\r\n     */\r\n    inputSource?: XRInputSource;\r\n    /**\r\n     * Is this a transient hit test\r\n     */\r\n    isTransient?: boolean;\r\n    /**\r\n     * Position of the hit test result\r\n     */\r\n    position: Vector3;\r\n    /**\r\n     * Rotation of the hit test result\r\n     */\r\n    rotationQuaternion: Quaternion;\r\n\r\n    /**\r\n     * The native hit test result\r\n     */\r\n    xrHitResult: XRHitTestResult;\r\n}\r\n\r\n/**\r\n * The currently-working hit-test module.\r\n * Hit test (or Ray-casting) is used to interact with the real world.\r\n * For further information read here - https://github.com/immersive-web/hit-test\r\n *\r\n * Tested on chrome (mobile) 80.\r\n */\r\nexport class WebXRHitTest extends WebXRAbstractFeature implements IWebXRHitTestFeature<IWebXRHitResult> {\r\n    private _tmpMat: Matrix = new Matrix();\r\n    private _tmpPos: Vector3 = new Vector3();\r\n    private _tmpQuat: Quaternion = new Quaternion();\r\n    private _transientXrHitTestSource: Nullable<XRTransientInputHitTestSource>;\r\n    // in XR space z-forward is negative\r\n    private _xrHitTestSource: Nullable<XRHitTestSource>;\r\n    private _initHitTestSource = (referenceSpace: XRReferenceSpace) => {\r\n        if (!referenceSpace) {\r\n            return;\r\n        }\r\n        const offsetRay = new XRRay(this.options.offsetRay || {});\r\n        const hitTestOptions: XRHitTestOptionsInit = {\r\n            space: this.options.useReferenceSpace ? referenceSpace : this._xrSessionManager.viewerReferenceSpace,\r\n            offsetRay: offsetRay,\r\n        };\r\n        if (this.options.entityTypes) {\r\n            hitTestOptions.entityTypes = this.options.entityTypes;\r\n        }\r\n        if (!hitTestOptions.space) {\r\n            Tools.Warn(\"waiting for viewer reference space to initialize\");\r\n            return;\r\n        }\r\n        this._xrSessionManager.session.requestHitTestSource!(hitTestOptions).then((hitTestSource) => {\r\n            if (this._xrHitTestSource) {\r\n                this._xrHitTestSource.cancel();\r\n            }\r\n            this._xrHitTestSource = hitTestSource;\r\n        });\r\n    };\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.HIT_TEST;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 2;\r\n\r\n    /**\r\n     * When set to true, each hit test will have its own position/rotation objects\r\n     * When set to false, position and rotation objects will be reused for each hit test. It is expected that\r\n     * the developers will clone them or copy them as they see fit.\r\n     */\r\n    public autoCloneTransformation: boolean = false;\r\n    /**\r\n     * Triggered when new babylon (transformed) hit test results are available\r\n     * Note - this will be called when results come back from the device. It can be an empty array!!\r\n     */\r\n    public onHitTestResultObservable: Observable<IWebXRHitResult[]> = new Observable();\r\n    /**\r\n     * Use this to temporarily pause hit test checks.\r\n     */\r\n    public paused: boolean = false;\r\n\r\n    /**\r\n     * Creates a new instance of the hit test feature\r\n     * @param _xrSessionManager an instance of WebXRSessionManager\r\n     * @param options options to use when constructing this feature\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        /**\r\n         * options to use when constructing this feature\r\n         */\r\n        public readonly options: IWebXRHitTestOptions = {}\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"hit-test\";\r\n        Tools.Warn(\"Hit test is an experimental and unstable feature.\");\r\n    }\r\n\r\n    /**\r\n     * attach this feature\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public attach(): boolean {\r\n        if (!super.attach()) {\r\n            return false;\r\n        }\r\n\r\n        // Feature enabled, but not available\r\n        if (!this._xrSessionManager.session.requestHitTestSource) {\r\n            return false;\r\n        }\r\n\r\n        if (!this.options.disablePermanentHitTest) {\r\n            if (this._xrSessionManager.referenceSpace) {\r\n                this._initHitTestSource(this._xrSessionManager.referenceSpace);\r\n            }\r\n            this._xrSessionManager.onXRReferenceSpaceChanged.add(this._initHitTestSource);\r\n        }\r\n        if (this.options.enableTransientHitTest) {\r\n            const offsetRay = new XRRay(this.options.transientOffsetRay || {});\r\n            this._xrSessionManager.session.requestHitTestSourceForTransientInput!({\r\n                profile: this.options.transientHitTestProfile || \"generic-touchscreen\",\r\n                offsetRay,\r\n                entityTypes: this.options.entityTypes,\r\n            }).then((hitSource) => {\r\n                this._transientXrHitTestSource = hitSource;\r\n            });\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * detach this feature.\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n        if (this._xrHitTestSource) {\r\n            this._xrHitTestSource.cancel();\r\n            this._xrHitTestSource = null;\r\n        }\r\n        this._xrSessionManager.onXRReferenceSpaceChanged.removeCallback(this._initHitTestSource);\r\n        if (this._transientXrHitTestSource) {\r\n            this._transientXrHitTestSource.cancel();\r\n            this._transientXrHitTestSource = null;\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n        this.onHitTestResultObservable.clear();\r\n    }\r\n\r\n    protected _onXRFrame(frame: XRFrame) {\r\n        // make sure we do nothing if (async) not attached\r\n        if (!this.attached || this.paused) {\r\n            return;\r\n        }\r\n\r\n        if (this._xrHitTestSource) {\r\n            const results = frame.getHitTestResults(this._xrHitTestSource);\r\n            this._processWebXRHitTestResult(results);\r\n        }\r\n        if (this._transientXrHitTestSource) {\r\n            const hitTestResultsPerInputSource = frame.getHitTestResultsForTransientInput(this._transientXrHitTestSource);\r\n\r\n            hitTestResultsPerInputSource.forEach((resultsPerInputSource) => {\r\n                this._processWebXRHitTestResult(resultsPerInputSource.results, resultsPerInputSource.inputSource);\r\n            });\r\n        }\r\n    }\r\n\r\n    private _processWebXRHitTestResult(hitTestResults: readonly XRHitTestResult[], inputSource?: XRInputSource) {\r\n        const results: IWebXRHitResult[] = [];\r\n        hitTestResults.forEach((hitTestResult) => {\r\n            const pose = hitTestResult.getPose(this._xrSessionManager.referenceSpace);\r\n            if (!pose) {\r\n                return;\r\n            }\r\n            const pos = pose.transform.position;\r\n            const quat = pose.transform.orientation;\r\n            this._tmpPos.set(pos.x, pos.y, pos.z).scaleInPlace(this._xrSessionManager.worldScalingFactor);\r\n            this._tmpQuat.set(quat.x, quat.y, quat.z, quat.w);\r\n            Matrix.FromFloat32ArrayToRefScaled(pose.transform.matrix, 0, 1, this._tmpMat);\r\n            if (!this._xrSessionManager.scene.useRightHandedSystem) {\r\n                this._tmpPos.z *= -1;\r\n                this._tmpQuat.z *= -1;\r\n                this._tmpQuat.w *= -1;\r\n                this._tmpMat.toggleModelMatrixHandInPlace();\r\n            }\r\n\r\n            const result: IWebXRHitResult = {\r\n                position: this.autoCloneTransformation ? this._tmpPos.clone() : this._tmpPos,\r\n                rotationQuaternion: this.autoCloneTransformation ? this._tmpQuat.clone() : this._tmpQuat,\r\n                transformationMatrix: this.autoCloneTransformation ? this._tmpMat.clone() : this._tmpMat,\r\n                inputSource: inputSource,\r\n                isTransient: !!inputSource,\r\n                xrHitResult: hitTestResult,\r\n            };\r\n            results.push(result);\r\n        });\r\n\r\n        this.onHitTestResultObservable.notifyObservers(results);\r\n    }\r\n}\r\n\r\n//register the plugin versions\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRHitTest.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRHitTest(xrSessionManager, options);\r\n    },\r\n    WebXRHitTest.Version,\r\n    false\r\n);\r\n"]}