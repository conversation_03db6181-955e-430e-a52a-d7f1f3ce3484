{"version": 3, "file": "WebXRRawCameraAccess.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRRawCameraAccess.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAEjF,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,oBAAoB,EAAE,MAAM,0CAA0C,CAAC;AAChF,OAAO,EAAE,eAAe,EAAE,qBAAqB,EAAE,MAAM,0CAA0C,CAAC;AAClG,OAAO,EAAE,WAAW,EAAE,gDAA4C;AAYlE;;;;GAIG;AACH,MAAM,OAAO,oBAAqB,SAAQ,oBAAoB;IAiD1D;;;;OAIG;IACH,YACI,iBAAsC,EACtB,UAAwC,EAAE;QAE1D,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFT,YAAO,GAAP,OAAO,CAAmC;QAvDtD,4BAAuB,GAAsB,EAAE,CAAC;QACxD;;;;WAIG;QACI,iBAAY,GAAkB,EAAE,CAAC;QACxC;;WAEG;QACI,cAAS,GAAa,EAAE,CAAC;QAEhC;;;WAGG;QACI,qBAAgB,GAUjB,EAAE,CAAC;QAET;;WAEG;QACI,gCAA2B,GAA8B,IAAI,UAAU,EAAE,CAAC;QA2B7E,IAAI,CAAC,mBAAmB,GAAG,eAAe,CAAC;IAC/C,CAAC;IAEM,MAAM,CAAC,KAA2B;QACrC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACtB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC;QAC/D,IAAI,CAAC,UAAU,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAEtF,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;YACpC,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YACzD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9C,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,CAAC;YACxC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7B,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;SACpC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACK,uBAAuB,CAAC,IAAY,EAAE,KAAa;QACvD,MAAM,cAAc,GAAG;YACnB,KAAK,EAAE,IAAI,CAAC,MAAO,CAAC,KAAK;YACzB,MAAM,EAAE,IAAI,CAAC,MAAO,CAAC,MAAM;YAC3B,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;SACP,CAAC;QACF,MAAM,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAEhC,8EAA8E;QAC9E,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;QACtE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC;QAEvE,8DAA8D;QAC9D,MAAM,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,EAAE,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9C,sDAAsD;QACtD,MAAM,KAAK,GAAG,CAAC,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG;YAC3B,EAAE;YACF,EAAE;YACF,EAAE;YACF,EAAE;YACF,KAAK;YACL,KAAK,EAAE,cAAc,CAAC,KAAK;YAC3B,MAAM,EAAE,cAAc,CAAC,MAAM;YAC7B,SAAS,EAAE,cAAc,CAAC,CAAC;YAC3B,SAAS,EAAE,cAAc,CAAC,CAAC;SAC9B,CAAC;IACN,CAAC;IAEO,uBAAuB,CAAC,IAAY,EAAE,KAAK,GAAG,CAAC;QACnD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;QACjC,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAExD,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;YACtC,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAC3H,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC;YAC9B,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC;YAChC,+EAA+E;YAC/E,eAAe,CAAC,MAAM,GAAG,SAAS,CAAC,kBAAkB,CAAC;YACtD,eAAe,CAAC,eAAe,GAAG,IAAI,CAAC;YACvC,eAAe,CAAC,IAAI,GAAG,SAAS,CAAC,iBAAiB,CAAC;YACnD,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC,+BAA+B,CAAC;YACzE,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YAC1C,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YAC5C,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC,wBAAwB,CAAC;YAClE,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC,wBAAwB,CAAC;YAClE,eAAe,CAAC,gBAAgB,GAAG,IAAI,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACjF,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC;YACtD,0BAA0B;YAC1B,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9D,OAAO,CAAC,IAAI,GAAG,4BAA4B,KAAK,GAAG,CAAC;YACpD,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;YACvD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;YACnC,4BAA4B;YAC5B,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SAC7C;aAAM;YACH,sEAAsE;YACtE,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;SACjE;QACD,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,UAAU,CAAC,QAAiB;QAClC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;QAE7D,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACtB,OAAO;SACV;QACD,IAAI,OAAO,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC/B,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QACH,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACvE;IACL,CAAC;;AAnJD;;GAEG;AACoB,yBAAI,GAAG,gBAAgB,CAAC,iBAAiB,AAArC,CAAsC;AAEjE;;;;GAIG;AACoB,4BAAO,GAAG,CAAC,AAAJ,CAAK;AA4IvC,oBAAoB,CAAC,eAAe,CAChC,oBAAoB,CAAC,IAAI,EACzB,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,oBAAoB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AACrE,CAAC,EACD,oBAAoB,CAAC,OAAO,EAC5B,KAAK,CACR,CAAC", "sourcesContent": ["import { WebXRFeatureName, WebXRFeaturesManager } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { WebGLHardwareTexture } from \"../../Engines/WebGL/webGLHardwareTexture\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\n\r\n/**\r\n * Options for raw camera access\r\n */\r\nexport interface IWebXRRawCameraAccessOptions {\r\n    /**\r\n     * Keep the created textures and metadata when detaching the feature.\r\n     */\r\n    doNotDisposeOnDetach?: boolean;\r\n}\r\n\r\n/**\r\n * WebXR Feature for WebXR raw camera access\r\n * @since 6.31.0\r\n * @see https://immersive-web.github.io/raw-camera-access/\r\n */\r\nexport class WebXRRawCameraAccess extends WebXRAbstractFeature {\r\n    private _cachedInternalTextures: InternalTexture[] = [];\r\n    /**\r\n     * This is an array of camera views\r\n     * Note that mostly the array will contain a single view\r\n     * If you want to know the order of the views, use the `viewIndex` array\r\n     */\r\n    public texturesData: BaseTexture[] = [];\r\n    /**\r\n     * If needed, this array will contain the eye definition of each texture in `texturesArray`\r\n     */\r\n    public viewIndex: string[] = [];\r\n\r\n    /**\r\n     * If needed, this array will contain the camera's intrinsics\r\n     * You can use this data to convert from camera space to screen space and vice versa\r\n     */\r\n    public cameraIntrinsics: {\r\n        u0: number;\r\n        v0: number;\r\n        ax: number;\r\n        ay: number;\r\n        gamma: number;\r\n        width: number;\r\n        height: number;\r\n        viewportX: number;\r\n        viewportY: number;\r\n    }[] = [];\r\n\r\n    /**\r\n     * An observable that will notify when the camera's textures are updated\r\n     */\r\n    public onTexturesUpdatedObservable: Observable<BaseTexture[]> = new Observable();\r\n\r\n    private _glBinding?: XRWebGLBinding;\r\n    private _glContext: WebGLRenderingContext;\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.RAW_CAMERA_ACCESS;\r\n\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * Creates a new instance of the feature\r\n     * @param _xrSessionManager the WebXRSessionManager\r\n     * @param options options for the Feature\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        public readonly options: IWebXRRawCameraAccessOptions = {}\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"camera-access\";\r\n    }\r\n\r\n    public attach(force?: boolean | undefined): boolean {\r\n        if (!super.attach(force)) {\r\n            return false;\r\n        }\r\n\r\n        this._glContext = this._xrSessionManager.scene.getEngine()._gl;\r\n        this._glBinding = new XRWebGLBinding(this._xrSessionManager.session, this._glContext);\r\n\r\n        return true;\r\n    }\r\n\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n        this._glBinding = undefined;\r\n        if (!this.options.doNotDisposeOnDetach) {\r\n            this._cachedInternalTextures.forEach((t) => t.dispose());\r\n            this.texturesData.forEach((t) => t.dispose());\r\n            this._cachedInternalTextures.length = 0;\r\n            this.texturesData.length = 0;\r\n            this.cameraIntrinsics.length = 0;\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n        this.onTexturesUpdatedObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * @see https://github.com/immersive-web/raw-camera-access/blob/main/explainer.md\r\n     * @param view the XRView to update\r\n     * @param index the index of the view in the views array\r\n     */\r\n    private _updateCameraIntrinsics(view: XRView, index: number): void {\r\n        const cameraViewport = {\r\n            width: view.camera!.width,\r\n            height: view.camera!.height,\r\n            x: 0,\r\n            y: 0,\r\n        };\r\n        const p = view.projectionMatrix;\r\n\r\n        // Principal point in pixels (typically at or near the center of the viewport)\r\n        const u0 = ((1 - p[8]) * cameraViewport.width) / 2 + cameraViewport.x;\r\n        const v0 = ((1 - p[9]) * cameraViewport.height) / 2 + cameraViewport.y;\r\n\r\n        // Focal lengths in pixels (these are equal for square pixels)\r\n        const ax = (cameraViewport.width / 2) * p[0];\r\n        const ay = (cameraViewport.height / 2) * p[5];\r\n\r\n        // Skew factor in pixels (nonzero for rhomboid pixels)\r\n        const gamma = (cameraViewport.width / 2) * p[4];\r\n        this.cameraIntrinsics[index] = {\r\n            u0,\r\n            v0,\r\n            ax,\r\n            ay,\r\n            gamma,\r\n            width: cameraViewport.width,\r\n            height: cameraViewport.height,\r\n            viewportX: cameraViewport.x,\r\n            viewportY: cameraViewport.y,\r\n        };\r\n    }\r\n\r\n    private _updateInternalTextures(view: XRView, index = 0): boolean {\r\n        if (!view.camera) {\r\n            return false;\r\n        }\r\n        this.viewIndex[index] = view.eye;\r\n        const lp = this._glBinding?.getCameraImage(view.camera);\r\n\r\n        if (!this._cachedInternalTextures[index]) {\r\n            const internalTexture = new InternalTexture(this._xrSessionManager.scene.getEngine(), InternalTextureSource.Unknown, true);\r\n            internalTexture.isCube = true;\r\n            internalTexture.invertY = false;\r\n            // internalTexture._useSRGBBuffer = this.options.reflectionFormat === \"srgba8\";\r\n            internalTexture.format = Constants.TEXTUREFORMAT_RGBA;\r\n            internalTexture.generateMipMaps = true;\r\n            internalTexture.type = Constants.TEXTURETYPE_FLOAT;\r\n            internalTexture.samplingMode = Constants.TEXTURE_LINEAR_LINEAR_MIPLINEAR;\r\n            internalTexture.width = view.camera.width;\r\n            internalTexture.height = view.camera.height;\r\n            internalTexture._cachedWrapU = Constants.TEXTURE_WRAP_ADDRESSMODE;\r\n            internalTexture._cachedWrapV = Constants.TEXTURE_WRAP_ADDRESSMODE;\r\n            internalTexture._hardwareTexture = new WebGLHardwareTexture(lp, this._glContext);\r\n            this._cachedInternalTextures[index] = internalTexture;\r\n            // create the base texture\r\n            const texture = new BaseTexture(this._xrSessionManager.scene);\r\n            texture.name = `WebXR Raw Camera Access (${index})`;\r\n            texture._texture = this._cachedInternalTextures[index];\r\n            this.texturesData[index] = texture;\r\n            // get the camera intrinsics\r\n            this._updateCameraIntrinsics(view, index);\r\n        } else {\r\n            // make sure the webgl texture is updated. Should happen automatically\r\n            this._cachedInternalTextures[index]._hardwareTexture?.set(lp);\r\n        }\r\n        this._cachedInternalTextures[index].isReady = true;\r\n        return true;\r\n    }\r\n\r\n    protected _onXRFrame(_xrFrame: XRFrame): void {\r\n        const referenceSPace = this._xrSessionManager.referenceSpace;\r\n\r\n        const pose = _xrFrame.getViewerPose(referenceSPace);\r\n        if (!pose || !pose.views) {\r\n            return;\r\n        }\r\n        let updated = true;\r\n        pose.views.forEach((view, index) => {\r\n            updated = updated && this._updateInternalTextures(view, index);\r\n        });\r\n        if (updated) {\r\n            this.onTexturesUpdatedObservable.notifyObservers(this.texturesData);\r\n        }\r\n    }\r\n}\r\n\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRRawCameraAccess.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRRawCameraAccess(xrSessionManager, options);\r\n    },\r\n    WebXRRawCameraAccess.Version,\r\n    false\r\n);\r\n"]}