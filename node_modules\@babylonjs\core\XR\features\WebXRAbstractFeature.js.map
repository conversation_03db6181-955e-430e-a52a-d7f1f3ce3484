{"version": 3, "file": "WebXRAbstractFeature.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRAbstractFeature.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAGnD,OAAO,EAAE,MAAM,EAAE,6BAAyB;AAE1C;;;;GAIG;AACH,MAAM,OAAgB,oBAAoB;IAmBtC;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED,IAAW,mBAAmB,CAAC,IAAY;QACvC,0GAA0G;QAC1G,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,iBAAiB,CAAC,WAAW,IAAI,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAChJ,MAAM,CAAC,IAAI,CAAC,eAAe,IAAI,uGAAuG,CAAC,CAAC;SAC3I;QACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IACrC,CAAC;IAgBD;;;OAGG;IACH,YAAsB,iBAAsC;QAAtC,sBAAiB,GAAjB,iBAAiB,CAAqB;QAnDpD,cAAS,GAAY,KAAK,CAAC;QAC3B,oBAAe,GAGjB,EAAE,CAAC;QAET;;WAEG;QACI,eAAU,GAAY,KAAK,CAAC;QAEnC;;WAEG;QACI,sBAAiB,GAAY,KAAK,CAAC;QAEhC,yBAAoB,GAAW,EAAE,CAAC;QAiB5C;;WAEG;QACI,8BAAyB,GAA8B,IAAI,UAAU,EAAE,CAAC;QAC/E;;WAEG;QACI,8BAAyB,GAA8B,IAAI,UAAU,EAAE,CAAC;IAWhB,CAAC;IAEhE;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAAe;QACzB,mCAAmC;QACnC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,KAAK,EAAE;YACR,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,OAAO,KAAK,CAAC;aAChB;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,2BAA2B;gBAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;aACjB;SACJ;QAED,2EAA2E;QAC3E,iDAAiD;QACjD,uFAAuF;QACvF,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE;YACzC,MAAM,CAAC,IAAI,CAAC,6GAA6G,CAAC,CAAC;SAC9H;aAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE;YACxJ,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;QAC1G,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,MAAM;QACT,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACtC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACI,YAAY;QACf,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACO,qBAAqB,CAAI,UAAyB,EAAE,QAAwD,EAAE,WAAqB;QACzI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACtB,UAAU;YACV,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC;SAC7D,CAAC,CAAC;IACP,CAAC;CAQJ", "sourcesContent": ["import type { IWebXRFeature } from \"../webXRFeaturesManager\";\r\nimport type { Observer, EventState } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * This is the base class for all WebXR features.\r\n * Since most features require almost the same resources and callbacks, this class can be used to simplify the development\r\n * Note that since the features manager is using the `IWebXRFeature` you are in no way obligated to use this class\r\n */\r\nexport abstract class WebXRAbstractFeature implements IWebXRFeature {\r\n    private _attached: boolean = false;\r\n    private _removeOnDetach: {\r\n        observer: Nullable<Observer<any>>;\r\n        observable: Observable<any>;\r\n    }[] = [];\r\n\r\n    /**\r\n     * Is this feature disposed?\r\n     */\r\n    public isDisposed: boolean = false;\r\n\r\n    /**\r\n     * Should auto-attach be disabled?\r\n     */\r\n    public disableAutoAttach: boolean = false;\r\n\r\n    protected _xrNativeFeatureName: string = \"\";\r\n\r\n    /**\r\n     * The name of the native xr feature name (like anchor, hit-test, or hand-tracking)\r\n     */\r\n    public get xrNativeFeatureName() {\r\n        return this._xrNativeFeatureName;\r\n    }\r\n\r\n    public set xrNativeFeatureName(name: string) {\r\n        // check if feature was initialized while in session but needs to be initialized before the session starts\r\n        if (!this._xrSessionManager.isNative && name && this._xrSessionManager.inXRSession && this._xrSessionManager.enabledFeatures?.indexOf(name) === -1) {\r\n            Logger.Warn(`The feature ${name} needs to be enabled before starting the XR session. Note - It is still possible it is not supported.`);\r\n        }\r\n        this._xrNativeFeatureName = name;\r\n    }\r\n\r\n    /**\r\n     * Observers registered here will be executed when the feature is attached\r\n     */\r\n    public onFeatureAttachObservable: Observable<IWebXRFeature> = new Observable();\r\n    /**\r\n     * Observers registered here will be executed when the feature is detached\r\n     */\r\n    public onFeatureDetachObservable: Observable<IWebXRFeature> = new Observable();\r\n\r\n    /**\r\n     * The dependencies of this feature, if any\r\n     */\r\n    public dependsOn?: string[];\r\n\r\n    /**\r\n     * Construct a new (abstract) WebXR feature\r\n     * @param _xrSessionManager the xr session manager for this feature\r\n     */\r\n    constructor(protected _xrSessionManager: WebXRSessionManager) {}\r\n\r\n    /**\r\n     * Is this feature attached\r\n     */\r\n    public get attached() {\r\n        return this._attached;\r\n    }\r\n\r\n    /**\r\n     * attach this feature\r\n     *\r\n     * @param force should attachment be forced (even when already attached)\r\n     * @returns true if successful, false is failed or already attached\r\n     */\r\n    public attach(force?: boolean): boolean {\r\n        // do not attach a disposed feature\r\n        if (this.isDisposed) {\r\n            return false;\r\n        }\r\n        if (!force) {\r\n            if (this.attached) {\r\n                return false;\r\n            }\r\n        } else {\r\n            if (this.attached) {\r\n                // detach first, to be sure\r\n                this.detach();\r\n            }\r\n        }\r\n\r\n        // if this is a native WebXR feature, check if it is enabled on the session\r\n        // For now only check if not using babylon native\r\n        // vision OS doesn't support the enabledFeatures array, so just warn instead of failing\r\n        if (!this._xrSessionManager.enabledFeatures) {\r\n            Logger.Warn(\"session.enabledFeatures is not available on this device. It is possible that this feature is not supported.\");\r\n        } else if (!this._xrSessionManager.isNative && this.xrNativeFeatureName && this._xrSessionManager.enabledFeatures.indexOf(this.xrNativeFeatureName) === -1) {\r\n            return false;\r\n        }\r\n\r\n        this._attached = true;\r\n        this._addNewAttachObserver(this._xrSessionManager.onXRFrameObservable, (frame) => this._onXRFrame(frame));\r\n        this.onFeatureAttachObservable.notifyObservers(this);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * detach this feature.\r\n     *\r\n     * @returns true if successful, false if failed or already detached\r\n     */\r\n    public detach(): boolean {\r\n        if (!this._attached) {\r\n            this.disableAutoAttach = true;\r\n            return false;\r\n        }\r\n        this._attached = false;\r\n        this._removeOnDetach.forEach((toRemove) => {\r\n            toRemove.observable.remove(toRemove.observer);\r\n        });\r\n        this.onFeatureDetachObservable.notifyObservers(this);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached\r\n     */\r\n    public dispose(): void {\r\n        this.detach();\r\n        this.isDisposed = true;\r\n        this.onFeatureAttachObservable.clear();\r\n        this.onFeatureDetachObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * This function will be executed during before enabling the feature and can be used to not-allow enabling it.\r\n     * Note that at this point the session has NOT started, so this is purely checking if the browser supports it\r\n     *\r\n     * @returns whether or not the feature is compatible in this environment\r\n     */\r\n    public isCompatible(): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * This is used to register callbacks that will automatically be removed when detach is called.\r\n     * @param observable the observable to which the observer will be attached\r\n     * @param callback the callback to register\r\n     * @param insertFirst should the callback be executed as soon as it is registered\r\n     */\r\n    protected _addNewAttachObserver<T>(observable: Observable<T>, callback: (eventData: T, eventState: EventState) => void, insertFirst?: boolean) {\r\n        this._removeOnDetach.push({\r\n            observable,\r\n            observer: observable.add(callback, undefined, insertFirst),\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Code in this function will be executed on each xrFrame received from the browser.\r\n     * This function will not execute after the feature is detached.\r\n     * @param _xrFrame the current frame\r\n     */\r\n    protected abstract _onXRFrame(_xrFrame: XRFrame): void;\r\n}\r\n"]}