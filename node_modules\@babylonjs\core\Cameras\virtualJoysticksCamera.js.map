{"version": 3, "file": "virtualJoysticksCamera.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Cameras/virtualJoysticksCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,OAAO,yCAAyC,CAAC;AAEjD,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IAC9D,OAAO,GAAG,EAAE,CAAC,IAAI,sBAAsB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AACzE,CAAC,CAAC,CAAC;AAEH;;;;;GAKG;AACH,MAAM,OAAO,sBAAuB,SAAQ,UAAU;IAClD;;;;;;;;OAQG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa;QACtD,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,wBAAwB,CAAC;IACpC,CAAC;CACJ", "sourcesContent": ["import { <PERSON>Camera } from \"./freeCamera\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport { Node } from \"../node\";\r\n\r\nimport \"./Inputs/freeCameraVirtualJoystickInput\";\r\n\r\nNode.AddNodeConstructor(\"VirtualJoysticksCamera\", (name, scene) => {\r\n    return () => new VirtualJoysticksCamera(name, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * This represents a free type of camera. It can be useful in First Person Shooter game for instance.\r\n * It is identical to the Free Camera and simply adds by default a virtual joystick.\r\n * Virtual Joysticks are on-screen 2D graphics that are used to control the camera or other scene items.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#virtual-joysticks-camera\r\n */\r\nexport class VirtualJoysticksCamera extends FreeCamera {\r\n    /**\r\n     * Instantiates a VirtualJoysticksCamera. It can be useful in First Person Shooter game for instance.\r\n     * It is identical to the Free Camera and simply adds by default a virtual joystick.\r\n     * Virtual Joysticks are on-screen 2D graphics that are used to control the camera or other scene items.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#virtual-joysticks-camera\r\n     * @param name Define the name of the camera in the scene\r\n     * @param position Define the start position of the camera in the scene\r\n     * @param scene Define the scene the camera belongs to\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene) {\r\n        super(name, position, scene);\r\n        this.inputs.addVirtualJoystick();\r\n    }\r\n\r\n    /**\r\n     * Gets the current object class name.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"VirtualJoysticksCamera\";\r\n    }\r\n}\r\n"]}