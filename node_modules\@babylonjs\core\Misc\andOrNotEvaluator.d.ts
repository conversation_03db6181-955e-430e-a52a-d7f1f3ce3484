/**
 * Class used to evaluate queries containing `and` and `or` operators
 */
export declare class AndOrNotEvaluator {
    /**
     * Evaluate a query
     * @param query defines the query to evaluate
     * @param evaluateCallback defines the callback used to filter result
     * @returns true if the query matches
     */
    static Eval(query: string, evaluateCallback: (val: any) => boolean): boolean;
    private static _HandleParenthesisContent;
    private static _SimplifyNegation;
}
