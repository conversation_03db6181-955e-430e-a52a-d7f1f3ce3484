{"version": 3, "file": "ssao2RenderingPipeline.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/PostProcesses/RenderPipeline/Pipelines/ssao2RenderingPipeline.ts"], "names": [], "mappings": ";AAAA,yDAAyD;AACzD,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,mBAAmB,EAAE,MAAM,wCAAwC,CAAC;AAC7E,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AAC1E,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAEjD,OAAO,EAAE,OAAO,EAAE,MAAM,qCAAqC,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,oCAAoC,CAAC;AACjE,OAAO,EAAE,yBAAyB,EAAE,MAAM,iEAAiE,CAAC;AAC5G,OAAO,EAAE,uBAAuB,EAAE,MAAM,+DAA+D,CAAC;AACxG,OAAO,EAAE,eAAe,EAAE,MAAM,wCAAwC,CAAC;AAEzE,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAC3D,OAAO,EAAE,kBAAkB,EAAE,MAAM,uCAAuC,CAAC;AAG3E,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AAEvD,OAAO,EAAE,MAAM,EAAE,MAAM,4BAA4B,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,MAAM,wCAAwC,CAAC;AAEpE,OAAO,sFAAsF,CAAC;AAE9F,OAAO,iCAAiC,CAAC;AACzC,OAAO,uCAAuC,CAAC;AAE/C;;GAEG;AACH,MAAM,OAAO,sBAAuB,SAAQ,yBAAyB;IAiDjE;;;;OAIG;IACH,IAAW,OAAO,CAAC,CAAS;QACxB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;IAClE,CAAC;IACD,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAID;;OAEG;IACH,IAAW,OAAO,CAAC,CAAS;QACxB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC9D,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACpD,CAAC;IACD,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAID;;OAEG;IACH,IAAW,cAAc,CAAC,CAAS;QAC/B,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,CAAC,CAAC;SACrC;aAAM;YACH,IAAI,CAAC,yBAAyB,CAAC,OAAO,GAAG,CAAC,CAAC;SAC9C;IACL,CAAC;IACD,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAOD,IAAY,uBAAuB;QAC/B,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;IAC9C,CAAC;IACD,IAAY,gBAAgB;QACxB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;IACvC,CAAC;IAkCD;;;;OAIG;IACH,IAAW,UAAU,CAAC,CAAU;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QAC/D,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IACzB,CAAC;IACD,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAID;;;;;;OAMG;IACH,IAAW,aAAa,CAAC,CAAU;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;IAC5B,CAAC;IACD,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAkCD;;OAEG;IACI,MAAM,KAAK,WAAW;QACzB,MAAM,MAAM,GAAG,WAAW,CAAC,iBAAiB,CAAC;QAC7C,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC;IACzC,CAAC;IAUD;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;;;OAQG;IACH,YAAY,IAAY,EAAE,KAAY,EAAE,KAAU,EAAE,OAAkB,EAAE,mBAAmB,GAAG,KAAK,EAAE,WAAW,GAAG,SAAS,CAAC,wBAAwB;QACjJ,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;QAtPnC,UAAU;QAEV;;;WAGG;QACI,iCAA4B,GAAW,8BAA8B,CAAC;QAC7E;;;WAGG;QACI,qBAAgB,GAAW,kBAAkB,CAAC;QACrD;;;WAGG;QACI,0BAAqB,GAAW,uBAAuB,CAAC;QAC/D;;;WAGG;QACI,0BAAqB,GAAW,uBAAuB,CAAC;QAC/D;;;WAGG;QACI,4BAAuB,GAAW,yBAAyB,CAAC;QAEnE;;WAEG;QAEI,kBAAa,GAAW,GAAG,CAAC;QAEnC;;WAEG;QAEI,SAAI,GAAW,KAAK,CAAC;QAE5B;;WAEG;QAEI,eAAU,GAAW,GAAG,CAAC;QAGxB,aAAQ,GAAW,IAAI,CAAC;QAexB,aAAQ,GAAW,CAAC,CAAC;QAcrB,oBAAe,GAAW,CAAC,CAAC;QAiBpC;;WAEG;QAEK,yBAAoB,GAAY,KAAK,CAAC;QA+B9C;;WAEG;QAEI,WAAM,GAAW,GAAG,CAAC;QAE5B;;;WAGG;QAEI,SAAI,GAAW,CAAC,CAAC;QAGhB,gBAAW,GAAY,KAAK,CAAC;QAkB7B,mBAAc,GAAY,IAAI,CAAC;QAkBvC;;;;;;WAMG;QAEI,qBAAgB,GAAW,EAAE,CAAC;QAErC;;;;;;;;WAQG;QAEI,oBAAe,GAAW,CAAC,CAAC;QAEnC;;;;;;WAMG;QAEI,uBAAkB,GAAW,CAAC,CAAC;QA6O9B,UAAK,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;QArM/B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QAEhD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,MAAM,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;YAC5D,OAAO;SACV;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,KAAK,CAAC;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,KAAK,CAAC;QAEjD,gBAAgB;QAChB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,KAAK,CAAC,4BAA4B,EAAE,CAAC;YACrC,IAAI,KAAK,CAAC,sBAAsB,EAAE,2BAA2B,EAAE;gBAC3D,MAAM,CAAC,KAAK,CAAC,4GAA4G,CAAC,CAAC;aAC9H;SACJ;aAAM;YACH,KAAK,CAAC,qBAAqB,EAAE,CAAC;YAC9B,IAAI,KAAK,CAAC,eAAe,EAAE,2BAA2B,EAAE;gBACpD,MAAM,CAAC,KAAK,CAAC,oGAAoG,CAAC,CAAC;aACtH;SACJ;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,IAAI,CAAC,yBAAyB,GAAG,IAAI,eAAe,CAAC,wBAAwB,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,qBAAqB,EAAE,KAAK,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1K,IAAI,CAAC,yBAAyB,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC;QAC7D,IAAI,CAAC,sBAAsB,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QAC9C,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACrE,IAAI,CAAC,6BAA6B,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAEjE,kBAAkB;QAClB,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,IAAI,CAAC,4BAA4B,EACjC,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,yBAAyB,CAAC;QAC1C,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,IAAI,CAAC,gBAAgB,EACrB,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;QACjC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,IAAI,CAAC,qBAAqB,EAC1B,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAClC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,IAAI,CAAC,qBAAqB,EAC1B,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAClC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,IAAI,CAAC,uBAAuB,EAC5B,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACxC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QAEF,SAAS;QACT,KAAK,CAAC,gCAAgC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,OAAO,EAAE;YACT,KAAK,CAAC,gCAAgC,CAAC,6BAA6B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SACvF;IACL,CAAC;IAED,iBAAiB;IAEjB;;;OAGG;IACI,YAAY;QACf,OAAO,wBAAwB,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,gCAAyC,KAAK;QACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACjD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAEtC,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC/C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACtC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvC,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAChD;QAED,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAE9B,IAAI,6BAA6B,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE,CAAC;SAC/C;QAED,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAE9G,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED,kBAAkB;IAElB,gBAAgB;IACT,QAAQ;QACX,KAAK,CAAC,QAAQ,EAAE,CAAC;IACrB,CAAC;IAEO,mBAAmB,CAAC,QAAiB;QACzC,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;IAC9E,CAAC;IAEO,kBAAkB,CAAC,SAAkB,EAAE,QAAiB;QAC5D,IAAI,MAAM,GAAG,gBAAgB,CAAC;QAC9B,IAAI,QAAQ,EAAE;YACV,MAAM,IAAI,uBAAuB,CAAC;SACrC;QACD,IAAI,CAAC,SAAS,EAAE;YACZ,MAAM,IAAI,uBAAuB,CAAC;SACrC;QACD,OAAO,EAAE,CAAC,EAAE,MAAM,GAAG,kBAAkB,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC;IACzD,CAAC;IAEO,sBAAsB,CAAC,SAAiB,EAAE,SAAiB,EAAE,WAAmB;QACpF,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7E,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE3D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QAC5G,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IACjH,CAAC;IAEO,iBAAiB,CAAC,IAAY,EAAE,QAAuB,EAAE,KAAa,EAAE,OAAe,EAAE,WAAmB,EAAE,UAAmB;QACrI,MAAM,UAAU,GAAG,IAAI,WAAW,CAC9B,IAAI,EACJ,OAAO,EACP,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC,EAC7C,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EACvB,KAAK,EACL,OAAO,EACP,WAAW,CACd,CAAC;QAEF,UAAU,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YACpC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBAC3B,OAAO;aACV;YAED,MAAM,eAAe,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;YAC9G,MAAM,iBAAiB,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;YAEpH,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC;YACtF,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACtD,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;aAC5F;iBAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBAC9B,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC;aAC7J;QACL,CAAC,CAAC;QAEF,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC;QACzC,OAAO,UAAU,CAAC;IACtB,CAAC;IAID,gCAAgC;IACxB,mBAAmB,CAAC,CAAS;QACjC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QACnG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QACnG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QACnG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QACnG,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,sBAAsB,CAAC,CAAC,gCAAgC;IACnF,CAAC;IAEO,WAAW,CAAC,CAAS,EAAE,CAAS;QACpC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC;IAEO,yBAAyB,CAAC,CAAS,EAAE,CAAS;QAClD,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QAC9B,kFAAkF;QAClF,MAAM,QAAQ,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;QAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC;QACtD,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACrF,CAAC;IAEO,mBAAmB;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC;QAChC,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,MAAM,CAAC;QAEX,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,CAAC,GAAG,UAAU,EAAE;YACnB,IAAI,UAAU,GAAG,EAAE,EAAE;gBACjB,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;aACzE;iBAAM;gBACH,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;gBAC7C,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7D;YAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,EAAE,CAAC;SACP;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,kBAAkB;QACtB,MAAM,OAAO,GAAG,iCAAiC,IAAI,CAAC,OAAO,qBAAqB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;QAE5G,OAAO,OAAO,CAAC;IACnB,CAAC;IAMO,sBAAsB,CAAC,KAAa,EAAE,WAAmB;QAC7D,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEhD,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,CAAC,eAAe,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;QAEpE,IAAI,CAAC,gBAAgB,GAAG,IAAI,WAAW,CACnC,OAAO,EACP,OAAO,EACP;YACI,cAAc;YACd,eAAe;YACf,kBAAkB;YAClB,eAAe;YACf,QAAQ;YACR,MAAM;YACN,OAAO;YACP,YAAY;YACZ,MAAM;YACN,WAAW;YACX,WAAW;YACX,WAAW;YACX,MAAM;YACN,YAAY;YACZ,iBAAiB;SACpB,EACD,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EACvB,KAAK,EACL,OAAO,EACP,WAAW,CACd,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBAC3B,OAAO;aACV;YAED,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACjG,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YACvD,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,KAAK,MAAM,CAAC,kBAAkB,EAAE;gBAC7D,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;gBAC5F,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;gBAClJ,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;aAC5E;iBAAM;gBACH,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,cAAc,EAAE,GAAG,GAAG,CAAC;gBACjE,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,EAAE,GAAG,GAAG,CAAC;gBACnE,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC;gBACnE,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,IAAI,SAAS,CAAC;gBACpE,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,IAAI,CAAC,UAAU,CAAC;gBACxE,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,UAAU,CAAC;gBACjE,MAAM,CAAC,YAAY,CAAC,iBAAiB,EAAE,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;gBACtF,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC;gBAC7D,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC;aAChE;YACD,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC;YAElE,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzF,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7F;iBAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBAC9B,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC;gBAC1J,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC;aAC/J;YACD,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5D,CAAC,CAAC;QACF,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC;QAEpD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,GAAG,IAAI,kBAAkB,EAAE,CAAC;SAChF;IACL,CAAC;IAEO,6BAA6B,CAAC,KAAa,EAAE,WAAmB;QACpE,IAAI,CAAC,uBAAuB,GAAG,IAAI,WAAW,CAC1C,aAAa,EACb,aAAa,EACb,EAAE,EACF,CAAC,eAAe,EAAE,UAAU,CAAC,EAC7B,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EACvB,KAAK,EACL,SAAS,EACT,WAAW,CACd,CAAC;QAEF,IAAI,CAAC,uBAAuB,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,YAAa,CAAC,QAAQ,CAAC;YACpD,MAAM,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;YAC7H,MAAM,CAAC,+BAA+B,CAAC,eAAe,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC5F,CAAC,CAAC;QACF,IAAI,CAAC,uBAAuB,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC;IAC/D,CAAC;IAEO,oBAAoB;QACxB,MAAM,IAAI,GAAG,GAAG,CAAC;QAEjB,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC;QAC7C,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,GAAI;YACvC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YACjG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;YAClB,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC;SACvB;QAED,MAAM,OAAO,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,6BAA6B,CAAC,CAAC;QACnI,OAAO,CAAC,IAAI,GAAG,mBAAmB,CAAC;QACnC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACzC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACzC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,mBAAmB,CAAC,UAAU,GAAG,wBAAwB,CAAC;QAE1D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,OAAO,mBAAmB,CAAC,KAAK,CAC5B,GAAG,EAAE,CAAC,IAAI,sBAAsB,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,oBAAoB,EAAE,MAAM,CAAC,YAAY,CAAC,EACjI,MAAM,EACN,KAAK,EACL,OAAO,CACV,CAAC;IACN,CAAC;;AA5JuB,6CAAsB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,AAA9B,CAA+B;AAErD,mDAA4B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,AAA9B,CAA+B;AAnd5E;IADN,SAAS,EAAE;6DACuB;AAM5B;IADN,SAAS,EAAE;oDACgB;AAMrB;IADN,SAAS,EAAE;0DACoB;AAGxB;IADP,SAAS,CAAC,SAAS,CAAC;wDACW;AAexB;IADP,SAAS,CAAC,SAAS,CAAC;wDACQ;AAcrB;IADP,SAAS,CAAC,gBAAgB,CAAC;+DACQ;AAqB5B;IADP,SAAS,EAAE;oEACkC;AAkBtC;IADP,SAAS,EAAE;sDACQ;AAMZ;IADP,SAAS,EAAE;4DACiB;AAWtB;IADN,SAAS,EAAE;sDACgB;AAOrB;IADN,SAAS,EAAE;oDACY;AAGhB;IADP,SAAS,CAAC,YAAY,CAAC;2DACa;AAkB7B;IADP,SAAS,CAAC,eAAe,CAAC;8DACY;AA0BhC;IADN,SAAS,EAAE;gEACyB;AAY9B;IADN,SAAS,EAAE;+DACuB;AAU5B;IADN,SAAS,EAAE;kEAC0B;AAgc1C,aAAa,CAAC,gCAAgC,EAAE,sBAAsB,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { Logger } from \"../../../Misc/logger\";\r\nimport { serialize } from \"../../../Misc/decorators\";\r\nimport { SerializationHelper } from \"../../../Misc/decorators.serialization\";\r\nimport { Vector3, TmpVectors, Vector2 } from \"../../../Maths/math.vector\";\r\nimport { Camera } from \"../../../Cameras/camera\";\r\nimport type { Effect } from \"../../../Materials/effect\";\r\nimport { Texture } from \"../../../Materials/Textures/texture\";\r\nimport { PostProcess } from \"../../../PostProcesses/postProcess\";\r\nimport { PostProcessRenderPipeline } from \"../../../PostProcesses/RenderPipeline/postProcessRenderPipeline\";\r\nimport { PostProcessRenderEffect } from \"../../../PostProcesses/RenderPipeline/postProcessRenderEffect\";\r\nimport { PassPostProcess } from \"../../../PostProcesses/passPostProcess\";\r\nimport type { Scene } from \"../../../scene\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport { EngineStore } from \"../../../Engines/engineStore\";\r\nimport { SSAO2Configuration } from \"../../../Rendering/ssao2Configuration\";\r\nimport type { PrePassRenderer } from \"../../../Rendering/prePassRenderer\";\r\nimport type { GeometryBufferRenderer } from \"../../../Rendering/geometryBufferRenderer\";\r\nimport { Constants } from \"../../../Engines/constants\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport { Scalar } from \"../../../Maths/math.scalar\";\r\nimport { RawTexture } from \"../../../Materials/Textures/rawTexture\";\r\n\r\nimport \"../../../PostProcesses/RenderPipeline/postProcessRenderPipelineManagerSceneComponent\";\r\n\r\nimport \"../../../Shaders/ssao2.fragment\";\r\nimport \"../../../Shaders/ssaoCombine.fragment\";\r\n\r\n/**\r\n * Render pipeline to produce ssao effect\r\n */\r\nexport class SSAO2RenderingPipeline extends PostProcessRenderPipeline {\r\n    // Members\r\n\r\n    /**\r\n     * @ignore\r\n     * The PassPostProcess id in the pipeline that contains the original scene color\r\n     */\r\n    public SSAOOriginalSceneColorEffect: string = \"SSAOOriginalSceneColorEffect\";\r\n    /**\r\n     * @ignore\r\n     * The SSAO PostProcess id in the pipeline\r\n     */\r\n    public SSAORenderEffect: string = \"SSAORenderEffect\";\r\n    /**\r\n     * @ignore\r\n     * The horizontal blur PostProcess id in the pipeline\r\n     */\r\n    public SSAOBlurHRenderEffect: string = \"SSAOBlurHRenderEffect\";\r\n    /**\r\n     * @ignore\r\n     * The vertical blur PostProcess id in the pipeline\r\n     */\r\n    public SSAOBlurVRenderEffect: string = \"SSAOBlurVRenderEffect\";\r\n    /**\r\n     * @ignore\r\n     * The PostProcess id in the pipeline that combines the SSAO-Blur output with the original scene color (SSAOOriginalSceneColorEffect)\r\n     */\r\n    public SSAOCombineRenderEffect: string = \"SSAOCombineRenderEffect\";\r\n\r\n    /**\r\n     * The output strength of the SSAO post-process. Default value is 1.0.\r\n     */\r\n    @serialize()\r\n    public totalStrength: number = 1.0;\r\n\r\n    /**\r\n     * Maximum depth value to still render AO. A smooth falloff makes the dimming more natural, so there will be no abrupt shading change.\r\n     */\r\n    @serialize()\r\n    public maxZ: number = 100.0;\r\n\r\n    /**\r\n     * In order to save performances, SSAO radius is clamped on close geometry. This ratio changes by how much.\r\n     */\r\n    @serialize()\r\n    public minZAspect: number = 0.2;\r\n\r\n    @serialize(\"epsilon\")\r\n    private _epsilon: number = 0.02;\r\n    /**\r\n     * Used in SSAO calculations to compensate for accuracy issues with depth values. Default 0.02.\r\n     *\r\n     * Normally you do not need to change this value, but you can experiment with it if you get a lot of in false self-occlusion on flat surfaces when using fewer than 16 samples. Useful range is normally [0..0.1] but higher values is allowed.\r\n     */\r\n    public set epsilon(n: number) {\r\n        this._epsilon = n;\r\n        this._ssaoPostProcess.updateEffect(this._getDefinesForSSAO());\r\n    }\r\n    public get epsilon(): number {\r\n        return this._epsilon;\r\n    }\r\n\r\n    @serialize(\"samples\")\r\n    private _samples: number = 8;\r\n    /**\r\n     * Number of samples used for the SSAO calculations. Default value is 8.\r\n     */\r\n    public set samples(n: number) {\r\n        this._samples = n;\r\n        this._ssaoPostProcess.updateEffect(this._getDefinesForSSAO());\r\n        this._sampleSphere = this._generateHemisphere();\r\n    }\r\n    public get samples(): number {\r\n        return this._samples;\r\n    }\r\n\r\n    @serialize(\"textureSamples\")\r\n    private _textureSamples: number = 1;\r\n    /**\r\n     * Number of samples to use for antialiasing.\r\n     */\r\n    public set textureSamples(n: number) {\r\n        this._textureSamples = n;\r\n\r\n        if (this._prePassRenderer) {\r\n            this._prePassRenderer.samples = n;\r\n        } else {\r\n            this._originalColorPostProcess.samples = n;\r\n        }\r\n    }\r\n    public get textureSamples(): number {\r\n        return this._textureSamples;\r\n    }\r\n\r\n    /**\r\n     * Force rendering the geometry through geometry buffer.\r\n     */\r\n    @serialize()\r\n    private _forceGeometryBuffer: boolean = false;\r\n    private get _geometryBufferRenderer(): Nullable<GeometryBufferRenderer> {\r\n        if (!this._forceGeometryBuffer) {\r\n            return null;\r\n        }\r\n        return this._scene.geometryBufferRenderer;\r\n    }\r\n    private get _prePassRenderer(): Nullable<PrePassRenderer> {\r\n        if (this._forceGeometryBuffer) {\r\n            return null;\r\n        }\r\n        return this._scene.prePassRenderer;\r\n    }\r\n\r\n    /**\r\n     * Ratio object used for SSAO ratio and blur ratio\r\n     */\r\n    @serialize()\r\n    private _ratio: any;\r\n\r\n    /*\r\n     * The texture type used by the different post processes created by SSAO\r\n     */\r\n    @serialize()\r\n    private _textureType: number;\r\n\r\n    /**\r\n     * Dynamically generated sphere sampler.\r\n     */\r\n    private _sampleSphere: number[];\r\n\r\n    /**\r\n     * The radius around the analyzed pixel used by the SSAO post-process. Default value is 2.0\r\n     */\r\n    @serialize()\r\n    public radius: number = 2.0;\r\n\r\n    /**\r\n     * The base color of the SSAO post-process\r\n     * The final result is \"base + ssao\" between [0, 1]\r\n     */\r\n    @serialize()\r\n    public base: number = 0;\r\n\r\n    @serialize(\"bypassBlur\")\r\n    private _bypassBlur: boolean = false;\r\n    /**\r\n     * Skips the denoising (blur) stage of the SSAO calculations.\r\n     *\r\n     * Useful to temporarily set while experimenting with the other SSAO2 settings.\r\n     */\r\n    public set bypassBlur(b: boolean) {\r\n        const defines = this._getDefinesForBlur(this.expensiveBlur, b);\r\n        const samplers = this._getSamplersForBlur(b);\r\n        this._blurHPostProcess.updateEffect(defines.h, null, samplers);\r\n        this._blurVPostProcess.updateEffect(defines.v, null, samplers);\r\n        this._bypassBlur = b;\r\n    }\r\n    public get bypassBlur(): boolean {\r\n        return this._bypassBlur;\r\n    }\r\n\r\n    @serialize(\"expensiveBlur\")\r\n    private _expensiveBlur: boolean = true;\r\n    /**\r\n     * Enables the configurable bilateral denoising (blurring) filter. Default is true.\r\n     * Set to false to instead use a legacy bilateral filter that can't be configured.\r\n     *\r\n     * The denoising filter runs after the SSAO calculations and is a very important step. Both options results in a so called bilateral being used, but the \"expensive\" one can be\r\n     * configured in several ways to fit your scene.\r\n     */\r\n    public set expensiveBlur(b: boolean) {\r\n        const defines = this._getDefinesForBlur(b, this._bypassBlur);\r\n        this._blurHPostProcess.updateEffect(defines.h);\r\n        this._blurVPostProcess.updateEffect(defines.v);\r\n        this._expensiveBlur = b;\r\n    }\r\n    public get expensiveBlur(): boolean {\r\n        return this._expensiveBlur;\r\n    }\r\n\r\n    /**\r\n     * The number of samples the bilateral filter uses in both dimensions when denoising the SSAO calculations. Default value is 16.\r\n     *\r\n     * A higher value should result in smoother shadows but will use more processing time in the shaders.\r\n     *\r\n     * A high value can cause the shadows to get to blurry or create visible artifacts (bands) near sharp details in the geometry. The artifacts can sometimes be mitigated by increasing the bilateralSoften setting.\r\n     */\r\n    @serialize()\r\n    public bilateralSamples: number = 16;\r\n\r\n    /**\r\n     * Controls the shape of the denoising kernel used by the bilateral filter. Default value is 0.\r\n     *\r\n     * By default the bilateral filter acts like a box-filter, treating all samples on the same depth with equal weights. This is effective to maximize the denoising effect given a limited set of samples. However, it also often results in visible ghosting around sharp shadow regions and can spread out lines over large areas so they are no longer visible.\r\n     *\r\n     * Increasing this setting will make the filter pay less attention to samples further away from the center sample, reducing many artifacts but at the same time increasing noise.\r\n     *\r\n     * Useful value range is [0..1].\r\n     */\r\n    @serialize()\r\n    public bilateralSoften: number = 0;\r\n\r\n    /**\r\n     * How forgiving the bilateral denoiser should be when rejecting samples. Default value is 0.\r\n     *\r\n     * A higher value results in the bilateral filter being more forgiving and thus doing a better job at denoising slanted and curved surfaces, but can lead to shadows spreading out around corners or between objects that are close to each other depth wise.\r\n     *\r\n     * Useful value range is normally [0..1], but higher values are allowed.\r\n     */\r\n    @serialize()\r\n    public bilateralTolerance: number = 0;\r\n\r\n    /**\r\n     *  Support test.\r\n     */\r\n    public static get IsSupported(): boolean {\r\n        const engine = EngineStore.LastCreatedEngine;\r\n        if (!engine) {\r\n            return false;\r\n        }\r\n        return engine._features.supportSSAO2;\r\n    }\r\n\r\n    private _scene: Scene;\r\n    private _randomTexture: Texture;\r\n    private _originalColorPostProcess: PassPostProcess;\r\n    private _ssaoPostProcess: PostProcess;\r\n    private _blurHPostProcess: PostProcess;\r\n    private _blurVPostProcess: PostProcess;\r\n    private _ssaoCombinePostProcess: PostProcess;\r\n\r\n    /**\r\n     * Gets active scene\r\n     */\r\n    public get scene(): Scene {\r\n        return this._scene;\r\n    }\r\n\r\n    /**\r\n     * @constructor\r\n     * @param name The rendering pipeline name\r\n     * @param scene The scene linked to this pipeline\r\n     * @param ratio The size of the postprocesses. Can be a number shared between passes or an object for more precision: { ssaoRatio: 0.5, blurRatio: 1.0 }\r\n     * @param cameras The array of cameras that the rendering pipeline will be attached to\r\n     * @param forceGeometryBuffer Set to true if you want to use the legacy geometry buffer renderer\r\n     * @param textureType The texture type used by the different post processes created by SSAO (default: Constants.TEXTURETYPE_UNSIGNED_INT)\r\n     */\r\n    constructor(name: string, scene: Scene, ratio: any, cameras?: Camera[], forceGeometryBuffer = false, textureType = Constants.TEXTURETYPE_UNSIGNED_INT) {\r\n        super(scene.getEngine(), name);\r\n\r\n        this._scene = scene;\r\n        this._ratio = ratio;\r\n        this._textureType = textureType;\r\n        this._forceGeometryBuffer = forceGeometryBuffer;\r\n\r\n        if (!this.isSupported) {\r\n            Logger.Error(\"The current engine does not support SSAO 2.\");\r\n            return;\r\n        }\r\n\r\n        const ssaoRatio = this._ratio.ssaoRatio || ratio;\r\n        const blurRatio = this._ratio.blurRatio || ratio;\r\n\r\n        // Set up assets\r\n        if (this._forceGeometryBuffer) {\r\n            scene.enableGeometryBufferRenderer();\r\n            if (scene.geometryBufferRenderer?.generateNormalsInWorldSpace) {\r\n                Logger.Error(\"SSAO2RenderingPipeline does not support generateNormalsInWorldSpace=true for the geometry buffer renderer!\");\r\n            }\r\n        } else {\r\n            scene.enablePrePassRenderer();\r\n            if (scene.prePassRenderer?.generateNormalsInWorldSpace) {\r\n                Logger.Error(\"SSAO2RenderingPipeline does not support generateNormalsInWorldSpace=true for the prepass renderer!\");\r\n            }\r\n        }\r\n\r\n        this._createRandomTexture();\r\n\r\n        this._originalColorPostProcess = new PassPostProcess(\"SSAOOriginalSceneColor\", 1.0, null, Texture.BILINEAR_SAMPLINGMODE, scene.getEngine(), undefined, this._textureType);\r\n        this._originalColorPostProcess.samples = this.textureSamples;\r\n        this._createSSAOPostProcess(1.0, textureType);\r\n        this._createBlurPostProcess(ssaoRatio, blurRatio, this._textureType);\r\n        this._createSSAOCombinePostProcess(blurRatio, this._textureType);\r\n\r\n        // Set up pipeline\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                this.SSAOOriginalSceneColorEffect,\r\n                () => {\r\n                    return this._originalColorPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                this.SSAORenderEffect,\r\n                () => {\r\n                    return this._ssaoPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                this.SSAOBlurHRenderEffect,\r\n                () => {\r\n                    return this._blurHPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                this.SSAOBlurVRenderEffect,\r\n                () => {\r\n                    return this._blurVPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                this.SSAOCombineRenderEffect,\r\n                () => {\r\n                    return this._ssaoCombinePostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n\r\n        // Finish\r\n        scene.postProcessRenderPipelineManager.addPipeline(this);\r\n        if (cameras) {\r\n            scene.postProcessRenderPipelineManager.attachCamerasToRenderPipeline(name, cameras);\r\n        }\r\n    }\r\n\r\n    // Public Methods\r\n\r\n    /**\r\n     * Get the class name\r\n     * @returns \"SSAO2RenderingPipeline\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"SSAO2RenderingPipeline\";\r\n    }\r\n\r\n    /**\r\n     * Removes the internal pipeline assets and detaches the pipeline from the scene cameras\r\n     * @param disableGeometryBufferRenderer Set to true if you want to disable the Geometry Buffer renderer\r\n     */\r\n    public dispose(disableGeometryBufferRenderer: boolean = false): void {\r\n        for (let i = 0; i < this._scene.cameras.length; i++) {\r\n            const camera = this._scene.cameras[i];\r\n\r\n            this._originalColorPostProcess.dispose(camera);\r\n            this._ssaoPostProcess.dispose(camera);\r\n            this._blurHPostProcess.dispose(camera);\r\n            this._blurVPostProcess.dispose(camera);\r\n            this._ssaoCombinePostProcess.dispose(camera);\r\n        }\r\n\r\n        this._randomTexture.dispose();\r\n\r\n        if (disableGeometryBufferRenderer) {\r\n            this._scene.disableGeometryBufferRenderer();\r\n        }\r\n\r\n        this._scene.postProcessRenderPipelineManager.detachCamerasFromRenderPipeline(this._name, this._scene.cameras);\r\n\r\n        super.dispose();\r\n    }\r\n\r\n    // Private Methods\r\n\r\n    /** @internal */\r\n    public _rebuild() {\r\n        super._rebuild();\r\n    }\r\n\r\n    private _getSamplersForBlur(disabled: boolean): Array<string> {\r\n        return disabled ? [\"textureSampler\"] : [\"textureSampler\", \"depthSampler\"];\r\n    }\r\n\r\n    private _getDefinesForBlur(bilateral: boolean, disabled: boolean): { h: string; v: string } {\r\n        let define = \"#define BLUR\\n\";\r\n        if (disabled) {\r\n            define += \"#define BLUR_BYPASS\\n\";\r\n        }\r\n        if (!bilateral) {\r\n            define += \"#define BLUR_LEGACY\\n\";\r\n        }\r\n        return { h: define + \"#define BLUR_H\\n\", v: define };\r\n    }\r\n\r\n    private _createBlurPostProcess(ssaoRatio: number, blurRatio: number, textureType: number): void {\r\n        const defines = this._getDefinesForBlur(this.expensiveBlur, this.bypassBlur);\r\n        const samplers = this._getSamplersForBlur(this.bypassBlur);\r\n\r\n        this._blurHPostProcess = this._createBlurFilter(\"BlurH\", samplers, ssaoRatio, defines.h, textureType, true);\r\n        this._blurVPostProcess = this._createBlurFilter(\"BlurV\", samplers, blurRatio, defines.v, textureType, false);\r\n    }\r\n\r\n    private _createBlurFilter(name: string, samplers: Array<string>, ratio: number, defines: string, textureType: number, horizontal: boolean): PostProcess {\r\n        const blurFilter = new PostProcess(\r\n            name,\r\n            \"ssao2\",\r\n            [\"outSize\", \"samples\", \"soften\", \"tolerance\"],\r\n            samplers,\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            this._scene.getEngine(),\r\n            false,\r\n            defines,\r\n            textureType\r\n        );\r\n\r\n        blurFilter.onApply = (effect: Effect) => {\r\n            if (!this._scene.activeCamera) {\r\n                return;\r\n            }\r\n\r\n            const ssaoCombineSize = horizontal ? this._ssaoCombinePostProcess.width : this._ssaoCombinePostProcess.height;\r\n            const originalColorSize = horizontal ? this._originalColorPostProcess.width : this._originalColorPostProcess.height;\r\n\r\n            effect.setFloat(\"outSize\", ssaoCombineSize > 0 ? ssaoCombineSize : originalColorSize);\r\n            effect.setInt(\"samples\", this.bilateralSamples);\r\n            effect.setFloat(\"soften\", this.bilateralSoften);\r\n            effect.setFloat(\"tolerance\", this.bilateralTolerance);\r\n            if (this._geometryBufferRenderer) {\r\n                effect.setTexture(\"depthSampler\", this._geometryBufferRenderer.getGBuffer().textures[0]);\r\n            } else if (this._prePassRenderer) {\r\n                effect.setTexture(\"depthSampler\", this._prePassRenderer.getRenderTarget().textures[this._prePassRenderer.getIndex(Constants.PREPASS_DEPTH_TEXTURE_TYPE)]);\r\n            }\r\n        };\r\n\r\n        blurFilter.samples = this.textureSamples;\r\n        return blurFilter;\r\n    }\r\n\r\n    private _bits = new Uint32Array(1);\r\n\r\n    //Van der Corput radical inverse\r\n    private _radicalInverse_VdC(i: number) {\r\n        this._bits[0] = i;\r\n        this._bits[0] = ((this._bits[0] << 16) | (this._bits[0] >> 16)) >>> 0;\r\n        this._bits[0] = ((this._bits[0] & 0x55555555) << 1) | (((this._bits[0] & 0xaaaaaaaa) >>> 1) >>> 0);\r\n        this._bits[0] = ((this._bits[0] & 0x33333333) << 2) | (((this._bits[0] & 0xcccccccc) >>> 2) >>> 0);\r\n        this._bits[0] = ((this._bits[0] & 0x0f0f0f0f) << 4) | (((this._bits[0] & 0xf0f0f0f0) >>> 4) >>> 0);\r\n        this._bits[0] = ((this._bits[0] & 0x00ff00ff) << 8) | (((this._bits[0] & 0xff00ff00) >>> 8) >>> 0);\r\n        return this._bits[0] * 2.3283064365386963e-10; // / 0x100000000 or / 4294967296\r\n    }\r\n\r\n    private _hammersley(i: number, n: number) {\r\n        return [i / n, this._radicalInverse_VdC(i)];\r\n    }\r\n\r\n    private _hemisphereSample_uniform(u: number, v: number): Vector3 {\r\n        const phi = v * 2.0 * Math.PI;\r\n        // rejecting samples that are close to tangent plane to avoid z-fighting artifacts\r\n        const cosTheta = 1.0 - u * 0.85;\r\n        const sinTheta = Math.sqrt(1.0 - cosTheta * cosTheta);\r\n        return new Vector3(Math.cos(phi) * sinTheta, Math.sin(phi) * sinTheta, cosTheta);\r\n    }\r\n\r\n    private _generateHemisphere(): number[] {\r\n        const numSamples = this.samples;\r\n        const result = [];\r\n        let vector;\r\n\r\n        let i = 0;\r\n        while (i < numSamples) {\r\n            if (numSamples < 16) {\r\n                vector = this._hemisphereSample_uniform(Math.random(), Math.random());\r\n            } else {\r\n                const rand = this._hammersley(i, numSamples);\r\n                vector = this._hemisphereSample_uniform(rand[0], rand[1]);\r\n            }\r\n\r\n            result.push(vector.x, vector.y, vector.z);\r\n            i++;\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    private _getDefinesForSSAO() {\r\n        const defines = `#define SSAO\\n#define SAMPLES ${this.samples}\\n#define EPSILON ${this.epsilon.toFixed(4)}`;\r\n\r\n        return defines;\r\n    }\r\n\r\n    private static readonly ORTHO_DEPTH_PROJECTION = [1, 0, 0, 0, 1, 0, 0, 0, 1];\r\n\r\n    private static readonly PERSPECTIVE_DEPTH_PROJECTION = [0, 0, 0, 0, 0, 0, 1, 1, 1];\r\n\r\n    private _createSSAOPostProcess(ratio: number, textureType: number): void {\r\n        this._sampleSphere = this._generateHemisphere();\r\n\r\n        const defines = this._getDefinesForSSAO();\r\n        const samplers = [\"randomSampler\", \"depthSampler\", \"normalSampler\"];\r\n\r\n        this._ssaoPostProcess = new PostProcess(\r\n            \"ssao2\",\r\n            \"ssao2\",\r\n            [\r\n                \"sampleSphere\",\r\n                \"samplesFactor\",\r\n                \"randTextureTiles\",\r\n                \"totalStrength\",\r\n                \"radius\",\r\n                \"base\",\r\n                \"range\",\r\n                \"projection\",\r\n                \"near\",\r\n                \"texelSize\",\r\n                \"xViewport\",\r\n                \"yViewport\",\r\n                \"maxZ\",\r\n                \"minZAspect\",\r\n                \"depthProjection\",\r\n            ],\r\n            samplers,\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            this._scene.getEngine(),\r\n            false,\r\n            defines,\r\n            textureType\r\n        );\r\n\r\n        this._ssaoPostProcess.onApply = (effect: Effect) => {\r\n            if (!this._scene.activeCamera) {\r\n                return;\r\n            }\r\n\r\n            effect.setArray3(\"sampleSphere\", this._sampleSphere);\r\n            effect.setFloat(\"randTextureTiles\", 32.0);\r\n            effect.setFloat(\"samplesFactor\", 1 / this.samples);\r\n            effect.setFloat(\"totalStrength\", this.totalStrength);\r\n            effect.setFloat2(\"texelSize\", 1 / this._ssaoPostProcess.width, 1 / this._ssaoPostProcess.height);\r\n            effect.setFloat(\"radius\", this.radius);\r\n            effect.setFloat(\"maxZ\", this.maxZ);\r\n            effect.setFloat(\"minZAspect\", this.minZAspect);\r\n            effect.setFloat(\"base\", this.base);\r\n            effect.setFloat(\"near\", this._scene.activeCamera.minZ);\r\n            if (this._scene.activeCamera.mode === Camera.PERSPECTIVE_CAMERA) {\r\n                effect.setMatrix3x3(\"depthProjection\", SSAO2RenderingPipeline.PERSPECTIVE_DEPTH_PROJECTION);\r\n                effect.setFloat(\"xViewport\", Math.tan(this._scene.activeCamera.fov / 2) * this._scene.getEngine().getAspectRatio(this._scene.activeCamera, true));\r\n                effect.setFloat(\"yViewport\", Math.tan(this._scene.activeCamera.fov / 2));\r\n            } else {\r\n                const halfWidth = this._scene.getEngine().getRenderWidth() / 2.0;\r\n                const halfHeight = this._scene.getEngine().getRenderHeight() / 2.0;\r\n                const orthoLeft = this._scene.activeCamera.orthoLeft ?? -halfWidth;\r\n                const orthoRight = this._scene.activeCamera.orthoRight ?? halfWidth;\r\n                const orthoBottom = this._scene.activeCamera.orthoBottom ?? -halfHeight;\r\n                const orthoTop = this._scene.activeCamera.orthoTop ?? halfHeight;\r\n                effect.setMatrix3x3(\"depthProjection\", SSAO2RenderingPipeline.ORTHO_DEPTH_PROJECTION);\r\n                effect.setFloat(\"xViewport\", (orthoRight - orthoLeft) * 0.5);\r\n                effect.setFloat(\"yViewport\", (orthoTop - orthoBottom) * 0.5);\r\n            }\r\n            effect.setMatrix(\"projection\", this._scene.getProjectionMatrix());\r\n\r\n            if (this._geometryBufferRenderer) {\r\n                effect.setTexture(\"depthSampler\", this._geometryBufferRenderer.getGBuffer().textures[0]);\r\n                effect.setTexture(\"normalSampler\", this._geometryBufferRenderer.getGBuffer().textures[1]);\r\n            } else if (this._prePassRenderer) {\r\n                effect.setTexture(\"depthSampler\", this._prePassRenderer.getRenderTarget().textures[this._prePassRenderer.getIndex(Constants.PREPASS_DEPTH_TEXTURE_TYPE)]);\r\n                effect.setTexture(\"normalSampler\", this._prePassRenderer.getRenderTarget().textures[this._prePassRenderer.getIndex(Constants.PREPASS_NORMAL_TEXTURE_TYPE)]);\r\n            }\r\n            effect.setTexture(\"randomSampler\", this._randomTexture);\r\n        };\r\n        this._ssaoPostProcess.samples = this.textureSamples;\r\n\r\n        if (!this._forceGeometryBuffer) {\r\n            this._ssaoPostProcess._prePassEffectConfiguration = new SSAO2Configuration();\r\n        }\r\n    }\r\n\r\n    private _createSSAOCombinePostProcess(ratio: number, textureType: number): void {\r\n        this._ssaoCombinePostProcess = new PostProcess(\r\n            \"ssaoCombine\",\r\n            \"ssaoCombine\",\r\n            [],\r\n            [\"originalColor\", \"viewport\"],\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            this._scene.getEngine(),\r\n            false,\r\n            undefined,\r\n            textureType\r\n        );\r\n\r\n        this._ssaoCombinePostProcess.onApply = (effect: Effect) => {\r\n            const viewport = this._scene.activeCamera!.viewport;\r\n            effect.setVector4(\"viewport\", TmpVectors.Vector4[0].copyFromFloats(viewport.x, viewport.y, viewport.width, viewport.height));\r\n            effect.setTextureFromPostProcessOutput(\"originalColor\", this._originalColorPostProcess);\r\n        };\r\n        this._ssaoCombinePostProcess.samples = this.textureSamples;\r\n    }\r\n\r\n    private _createRandomTexture(): void {\r\n        const size = 128;\r\n\r\n        const data = new Uint8Array(size * size * 4);\r\n        const randVector = Vector2.Zero();\r\n        for (let index = 0; index < data.length; ) {\r\n            randVector.set(Scalar.RandomRange(0, 1), Scalar.RandomRange(0, 1)).normalize().scaleInPlace(255);\r\n            data[index++] = Math.floor(randVector.x);\r\n            data[index++] = Math.floor(randVector.y);\r\n            data[index++] = 0;\r\n            data[index++] = 255;\r\n        }\r\n\r\n        const texture = RawTexture.CreateRGBATexture(data, size, size, this._scene, false, false, Constants.TEXTURE_BILINEAR_SAMPLINGMODE);\r\n        texture.name = \"SSAORandomTexture\";\r\n        texture.wrapU = Texture.WRAP_ADDRESSMODE;\r\n        texture.wrapV = Texture.WRAP_ADDRESSMODE;\r\n        this._randomTexture = texture;\r\n    }\r\n\r\n    /**\r\n     * Serialize the rendering pipeline (Used when exporting)\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n        serializationObject.customType = \"SSAO2RenderingPipeline\";\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parse the serialized pipeline\r\n     * @param source Source pipeline.\r\n     * @param scene The scene to load the pipeline to.\r\n     * @param rootUrl The URL of the serialized pipeline.\r\n     * @returns An instantiated pipeline from the serialized object.\r\n     */\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): SSAO2RenderingPipeline {\r\n        return SerializationHelper.Parse(\r\n            () => new SSAO2RenderingPipeline(source._name, scene, source._ratio, undefined, source._forceGeometryBuffer, source._textureType),\r\n            source,\r\n            scene,\r\n            rootUrl\r\n        );\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SSAO2RenderingPipeline\", SSAO2RenderingPipeline);\r\n"]}