{"version": 3, "file": "thinSprite.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Sprites/thinSprite.ts"], "names": [], "mappings": "AAGA;;;;GAIG;AACH,MAAM,OAAO,UAAU;IAsBnB;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED,sFAAsF;IACtF,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,0FAA0F;IAC1F,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,6GAA6G;IAC7G,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,2FAA2F;IAC3F,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACpC,CAAC;IAoBD;;OAEG;IACH;QA7DA,6BAA6B;QACtB,UAAK,GAAG,GAAG,CAAC;QACnB,8BAA8B;QACvB,WAAM,GAAG,GAAG,CAAC;QACpB,kCAAkC;QAC3B,UAAK,GAAG,CAAC,CAAC;QACjB,uFAAuF;QAChF,YAAO,GAAG,KAAK,CAAC;QACvB,uFAAuF;QAChF,YAAO,GAAG,KAAK,CAAC;QACvB,+FAA+F;QACxF,cAAS,GAAG,IAAI,CAAC;QAsChB,sBAAiB,GAAG,KAAK,CAAC;QACxB,mBAAc,GAAG,KAAK,CAAC;QACvB,eAAU,GAAG,CAAC,CAAC;QACf,aAAQ,GAAG,CAAC,CAAC;QACb,WAAM,GAAG,CAAC,CAAC;QACb,eAAU,GAAG,CAAC,CAAC;QACf,UAAK,GAAG,CAAC,CAAC;QACV,wBAAmB,GAAyB,IAAI,CAAC;QAMrD,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;QAC3C,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;IACpD,CAAC;IAED;;;;;;;OAOG;IACI,aAAa,CAAC,IAAY,EAAE,EAAU,EAAE,IAAa,EAAE,KAAa,EAAE,cAAoC;QAC7G,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,mBAAmB,GAAG,cAAc,CAAC;QAE1C,IAAI,IAAI,GAAG,EAAE,EAAE;YACX,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;SACvB;aAAM;YACH,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;YACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACxB;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACnB,CAAC;IAED,uCAAuC;IAChC,aAAa;QAChB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,SAAiB;QAC7B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,OAAO;SACV;QAED,IAAI,CAAC,KAAK,IAAI,SAAS,CAAC;QACxB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;YAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;YACtC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE;gBACtH,IAAI,IAAI,CAAC,cAAc,EAAE;oBACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;iBAC1E;qBAAM;oBACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC/B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBAC/B,IAAI,IAAI,CAAC,mBAAmB,EAAE;wBAC1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;qBAC9B;iBACJ;aACJ;SACJ;IACL,CAAC;CACJ", "sourcesContent": ["import type { IVector3Like, IColor4Like } from \"../Maths/math.like\";\r\nimport type { Nullable } from \"../types\";\r\n\r\n/**\r\n * ThinSprite Class used to represent a thin sprite\r\n * This is the base class for sprites but can also directly be used with ThinEngine\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/sprites\r\n */\r\nexport class ThinSprite {\r\n    /** Gets or sets the cell index in the sprite sheet */\r\n    public cellIndex: number;\r\n    /** Gets or sets the cell reference in the sprite sheet, uses sprite's filename when added to sprite sheet */\r\n    public cellRef: string;\r\n    /** Gets or sets the current world position */\r\n    public position: IVector3Like;\r\n    /** Gets or sets the main color */\r\n    public color: IColor4Like;\r\n    /** Gets or sets the width */\r\n    public width = 1.0;\r\n    /** Gets or sets the height */\r\n    public height = 1.0;\r\n    /** Gets or sets rotation angle */\r\n    public angle = 0;\r\n    /** Gets or sets a boolean indicating if UV coordinates should be inverted in U axis */\r\n    public invertU = false;\r\n    /** Gets or sets a boolean indicating if UV coordinates should be inverted in B axis */\r\n    public invertV = false;\r\n    /** Gets or sets a boolean indicating if the sprite is visible (renderable). Default is true */\r\n    public isVisible = true;\r\n\r\n    /**\r\n     * Returns a boolean indicating if the animation is started\r\n     */\r\n    public get animationStarted() {\r\n        return this._animationStarted;\r\n    }\r\n\r\n    /** Gets the initial key for the animation (setting it will restart the animation)  */\r\n    public get fromIndex() {\r\n        return this._fromIndex;\r\n    }\r\n\r\n    /** Gets or sets the end key for the animation (setting it will restart the animation)  */\r\n    public get toIndex() {\r\n        return this._toIndex;\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating if the animation is looping (setting it will restart the animation)  */\r\n    public get loopAnimation() {\r\n        return this._loopAnimation;\r\n    }\r\n\r\n    /** Gets or sets the delay between cell changes (setting it will restart the animation)  */\r\n    public get delay() {\r\n        return Math.max(this._delay, 1);\r\n    }\r\n\r\n    /** @internal */\r\n    public _xOffset: number;\r\n    /** @internal */\r\n    public _yOffset: number;\r\n    /** @internal */\r\n    public _xSize: number;\r\n    /** @internal */\r\n    public _ySize: number;\r\n\r\n    private _animationStarted = false;\r\n    protected _loopAnimation = false;\r\n    protected _fromIndex = 0;\r\n    protected _toIndex = 0;\r\n    protected _delay = 0;\r\n    private _direction = 1;\r\n    private _time = 0;\r\n    private _onBaseAnimationEnd: Nullable<() => void> = null;\r\n\r\n    /**\r\n     * Creates a new Thin Sprite\r\n     */\r\n    constructor() {\r\n        this.position = { x: 1.0, y: 1.0, z: 1.0 };\r\n        this.color = { r: 1.0, g: 1.0, b: 1.0, a: 1.0 };\r\n    }\r\n\r\n    /**\r\n     * Starts an animation\r\n     * @param from defines the initial key\r\n     * @param to defines the end key\r\n     * @param loop defines if the animation must loop\r\n     * @param delay defines the start delay (in ms)\r\n     * @param onAnimationEnd defines a callback for when the animation ends\r\n     */\r\n    public playAnimation(from: number, to: number, loop: boolean, delay: number, onAnimationEnd: Nullable<() => void>): void {\r\n        this._fromIndex = from;\r\n        this._toIndex = to;\r\n        this._loopAnimation = loop;\r\n        this._delay = delay || 1;\r\n        this._animationStarted = true;\r\n        this._onBaseAnimationEnd = onAnimationEnd;\r\n\r\n        if (from < to) {\r\n            this._direction = 1;\r\n        } else {\r\n            this._direction = -1;\r\n            this._toIndex = from;\r\n            this._fromIndex = to;\r\n        }\r\n\r\n        this.cellIndex = from;\r\n        this._time = 0;\r\n    }\r\n\r\n    /** Stops current animation (if any) */\r\n    public stopAnimation(): void {\r\n        this._animationStarted = false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _animate(deltaTime: number): void {\r\n        if (!this._animationStarted) {\r\n            return;\r\n        }\r\n\r\n        this._time += deltaTime;\r\n        if (this._time > this._delay) {\r\n            this._time = this._time % this._delay;\r\n            this.cellIndex += this._direction;\r\n            if ((this._direction > 0 && this.cellIndex > this._toIndex) || (this._direction < 0 && this.cellIndex < this._fromIndex)) {\r\n                if (this._loopAnimation) {\r\n                    this.cellIndex = this._direction > 0 ? this._fromIndex : this._toIndex;\r\n                } else {\r\n                    this.cellIndex = this._toIndex;\r\n                    this._animationStarted = false;\r\n                    if (this._onBaseAnimationEnd) {\r\n                        this._onBaseAnimationEnd();\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}