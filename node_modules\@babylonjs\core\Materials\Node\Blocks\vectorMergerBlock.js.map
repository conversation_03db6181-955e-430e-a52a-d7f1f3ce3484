{"version": 3, "file": "vectorMergerBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/vectorMergerBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAEvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAE7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAGxD;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,iBAAiB;IAkBpD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAtBlD;;WAEG;QACI,aAAQ,GAA0B,GAAG,CAAC;QAC7C;;WAEG;QACI,aAAQ,GAA0B,GAAG,CAAC;QAC7C;;WAEG;QACI,aAAQ,GAA0B,GAAG,CAAC;QAC7C;;WAEG;QACI,aAAQ,GAA0B,GAAG,CAAC;QASzC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACjF,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAChF,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC3E,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC3E,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC3E,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAE3E,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC3E,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC1E,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;IAC7E,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,IAAW,EAAE;QACT,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;;OAGG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAES,YAAY,CAAC,IAAY;QAC/B,IAAI,IAAI,KAAK,OAAO,EAAE;YAClB,OAAO,QAAQ,CAAC;SACnB;QACD,IAAI,IAAI,KAAK,MAAM,EAAE;YACjB,OAAO,OAAO,CAAC;SAClB;QACD,IAAI,IAAI,KAAK,KAAK,EAAE;YAChB,OAAO,MAAM,CAAC;SACjB;QACD,IAAI,IAAI,KAAK,KAAK,EAAE;YAChB,OAAO,MAAM,CAAC;SACjB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,aAAa,CAAC,GAAW;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE9E,OAAO,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACxC,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;QACtB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;QAE9B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEtC,IAAI,SAAS,CAAC,WAAW,EAAE;YACvB,IAAI,QAAQ,CAAC,YAAY,EAAE;gBACvB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,SAAS,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;aACzI;YAED,IAAI,QAAQ,CAAC,YAAY,EAAE;gBACvB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,SAAS,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;aACzI;YAED,IAAI,QAAQ,CAAC,YAAY,EAAE;gBACvB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,SAAS,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;aACzI;SACJ;aAAM,IAAI,QAAQ,CAAC,WAAW,EAAE;YAC7B,IAAI,QAAQ,CAAC,YAAY,EAAE;gBACvB,KAAK,CAAC,iBAAiB;oBACnB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC;wBACpC,WAAW,QAAQ,CAAC,sBAAsB,KAAK,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;aAC7I;YAED,IAAI,QAAQ,CAAC,YAAY,EAAE;gBACvB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,QAAQ,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;aACxI;YAED,IAAI,QAAQ,CAAC,YAAY,EAAE;gBACvB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,QAAQ,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;aACxI;SACJ;aAAM,IAAI,OAAO,CAAC,WAAW,EAAE;YAC5B,IAAI,QAAQ,CAAC,YAAY,EAAE;gBACvB,IAAI,OAAO,CAAC,WAAW,EAAE;oBACrB,KAAK,CAAC,iBAAiB;wBACnB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,WAAW,OAAO,CAAC,sBAAsB,KAAK,OAAO,CAAC,sBAAsB,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;iBACzJ;qBAAM;oBACH,KAAK,CAAC,iBAAiB;wBACnB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC;4BACpC,WAAW,OAAO,CAAC,sBAAsB,KAAK,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAClG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KACvD,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;iBACtC;aACJ;YAED,IAAI,QAAQ,CAAC,YAAY,EAAE;gBACvB,KAAK,CAAC,iBAAiB;oBACnB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC;wBACpC,WAAW,OAAO,CAAC,sBAAsB,KAAK,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;aAC5I;YAED,IAAI,QAAQ,CAAC,YAAY,EAAE;gBACvB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;aACvI;YAED,IAAI,YAAY,CAAC,YAAY,EAAE;gBAC3B,IAAI,OAAO,CAAC,WAAW,EAAE;oBACrB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;iBAC3I;qBAAM;oBACH,KAAK,CAAC,iBAAiB;wBACnB,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,KAAK,CAAC;4BACxC,WAAW,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAClJ,CAAC,CACJ,KAAK,CAAC;iBACd;aACJ;SACJ;aAAM;YACH,IAAI,QAAQ,CAAC,YAAY,EAAE;gBACvB,IAAI,OAAO,CAAC,WAAW,EAAE;oBACrB,KAAK,CAAC,iBAAiB;wBACnB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC;4BACpC,WAAW,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAC5H,OAAO,CAAC,sBACZ,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;iBACtC;qBAAM;oBACH,KAAK,CAAC,iBAAiB;wBACnB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC;4BACpC,WAAW,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAC5H,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KACvD,KAAK,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;iBACnG;aACJ;YAED,IAAI,QAAQ,CAAC,YAAY,EAAE;gBACvB,KAAK,CAAC,iBAAiB;oBACnB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC;wBACpC,WAAW,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAC5H,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KACvD,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;aACtC;YAED,IAAI,QAAQ,CAAC,YAAY,EAAE;gBACvB,KAAK,CAAC,iBAAiB;oBACnB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC;wBACpC,WAAW,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;aACtK;YAED,IAAI,YAAY,CAAC,YAAY,EAAE;gBAC3B,IAAI,OAAO,CAAC,WAAW,EAAE;oBACrB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;iBAC3I;qBAAM;oBACH,KAAK,CAAC,iBAAiB;wBACnB,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,KAAK,CAAC;4BACxC,WAAW,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAClJ,CAAC,CACJ,KAAK,CAAC;iBACd;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7C,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7C,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7C,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE7C,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,IAAI,GAAG,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,IAAI,GAAG,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,IAAI,GAAG,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,IAAI,GAAG,CAAC;IACxD,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAC7C,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,gBAAgB,IAAI,CAAC,QAAQ,MAAM,CAAC;QAC3E,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,gBAAgB,IAAI,CAAC,QAAQ,MAAM,CAAC;QAC3E,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,gBAAgB,IAAI,CAAC,QAAQ,MAAM,CAAC;QAC3E,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,gBAAgB,IAAI,CAAC,QAAQ,MAAM,CAAC;QAE3E,OAAO,UAAU,CAAC;IACtB,CAAC;CACJ;AAED,aAAa,CAAC,2BAA2B,EAAE,iBAAiB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport type { Scene } from \"../../../scene\";\r\n\r\n/**\r\n * Block used to create a Vector2/3/4 out of individual inputs (one for each component)\r\n */\r\nexport class VectorMergerBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Gets or sets the swizzle for x (meaning which component to affect to the output.x)\r\n     */\r\n    public xSwizzle: \"x\" | \"y\" | \"z\" | \"w\" = \"x\";\r\n    /**\r\n     * Gets or sets the swizzle for y (meaning which component to affect to the output.y)\r\n     */\r\n    public ySwizzle: \"x\" | \"y\" | \"z\" | \"w\" = \"y\";\r\n    /**\r\n     * Gets or sets the swizzle for z (meaning which component to affect to the output.z)\r\n     */\r\n    public zSwizzle: \"x\" | \"y\" | \"z\" | \"w\" = \"z\";\r\n    /**\r\n     * Gets or sets the swizzle for w (meaning which component to affect to the output.w)\r\n     */\r\n    public wSwizzle: \"x\" | \"y\" | \"z\" | \"w\" = \"w\";\r\n\r\n    /**\r\n     * Create a new VectorMergerBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerInput(\"xyzw \", NodeMaterialBlockConnectionPointTypes.Vector4, true);\r\n        this.registerInput(\"xyz \", NodeMaterialBlockConnectionPointTypes.Vector3, true);\r\n        this.registerInput(\"xy \", NodeMaterialBlockConnectionPointTypes.Vector2, true);\r\n        this.registerInput(\"zw \", NodeMaterialBlockConnectionPointTypes.Vector2, true);\r\n        this.registerInput(\"x\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"y\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"z\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"w\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n\r\n        this.registerOutput(\"xyzw\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        this.registerOutput(\"xyz\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n        this.registerOutput(\"xy\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerOutput(\"zw\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"VectorMergerBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the xyzw component (input)\r\n     */\r\n    public get xyzwIn(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the xyz component (input)\r\n     */\r\n    public get xyzIn(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the xy component (input)\r\n     */\r\n    public get xyIn(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the zw component (input)\r\n     */\r\n    public get zwIn(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the x component (input)\r\n     */\r\n    public get x(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the y component (input)\r\n     */\r\n    public get y(): NodeMaterialConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the z component (input)\r\n     */\r\n    public get z(): NodeMaterialConnectionPoint {\r\n        return this._inputs[6];\r\n    }\r\n\r\n    /**\r\n     * Gets the w component (input)\r\n     */\r\n    public get w(): NodeMaterialConnectionPoint {\r\n        return this._inputs[7];\r\n    }\r\n\r\n    /**\r\n     * Gets the xyzw component (output)\r\n     */\r\n    public get xyzw(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the xyz component (output)\r\n     */\r\n    public get xyzOut(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the xy component (output)\r\n     */\r\n    public get xyOut(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the zw component (output)\r\n     */\r\n    public get zwOut(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the xy component (output)\r\n     * @deprecated Please use xyOut instead.\r\n     */\r\n    public get xy(): NodeMaterialConnectionPoint {\r\n        return this.xyOut;\r\n    }\r\n\r\n    /**\r\n     * Gets the xyz component (output)\r\n     * @deprecated Please use xyzOut instead.\r\n     */\r\n    public get xyz(): NodeMaterialConnectionPoint {\r\n        return this.xyzOut;\r\n    }\r\n\r\n    protected _inputRename(name: string) {\r\n        if (name === \"xyzw \") {\r\n            return \"xyzwIn\";\r\n        }\r\n        if (name === \"xyz \") {\r\n            return \"xyzIn\";\r\n        }\r\n        if (name === \"xy \") {\r\n            return \"xyIn\";\r\n        }\r\n        if (name === \"zw \") {\r\n            return \"zwIn\";\r\n        }\r\n        return name;\r\n    }\r\n\r\n    private _buildSwizzle(len: number) {\r\n        const swizzle = this.xSwizzle + this.ySwizzle + this.zSwizzle + this.wSwizzle;\r\n\r\n        return \".\" + swizzle.substr(0, len);\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const xInput = this.x;\r\n        const yInput = this.y;\r\n        const zInput = this.z;\r\n        const wInput = this.w;\r\n        const xyInput = this.xyIn;\r\n        const zwInput = this.zwIn;\r\n        const xyzInput = this.xyzIn;\r\n        const xyzwInput = this.xyzwIn;\r\n\r\n        const v4Output = this._outputs[0];\r\n        const v3Output = this._outputs[1];\r\n        const v2Output = this._outputs[2];\r\n        const v2CompOutput = this._outputs[3];\r\n\r\n        if (xyzwInput.isConnected) {\r\n            if (v4Output.hasEndpoints) {\r\n                state.compilationString += this._declareOutput(v4Output, state) + ` = ${xyzwInput.associatedVariableName}${this._buildSwizzle(4)};\\n`;\r\n            }\r\n\r\n            if (v3Output.hasEndpoints) {\r\n                state.compilationString += this._declareOutput(v3Output, state) + ` = ${xyzwInput.associatedVariableName}${this._buildSwizzle(3)};\\n`;\r\n            }\r\n\r\n            if (v2Output.hasEndpoints) {\r\n                state.compilationString += this._declareOutput(v2Output, state) + ` = ${xyzwInput.associatedVariableName}${this._buildSwizzle(2)};\\n`;\r\n            }\r\n        } else if (xyzInput.isConnected) {\r\n            if (v4Output.hasEndpoints) {\r\n                state.compilationString +=\r\n                    this._declareOutput(v4Output, state) +\r\n                    ` = vec4(${xyzInput.associatedVariableName}, ${wInput.isConnected ? this._writeVariable(wInput) : \"0.0\"})${this._buildSwizzle(4)};\\n`;\r\n            }\r\n\r\n            if (v3Output.hasEndpoints) {\r\n                state.compilationString += this._declareOutput(v3Output, state) + ` = ${xyzInput.associatedVariableName}${this._buildSwizzle(3)};\\n`;\r\n            }\r\n\r\n            if (v2Output.hasEndpoints) {\r\n                state.compilationString += this._declareOutput(v2Output, state) + ` = ${xyzInput.associatedVariableName}${this._buildSwizzle(2)};\\n`;\r\n            }\r\n        } else if (xyInput.isConnected) {\r\n            if (v4Output.hasEndpoints) {\r\n                if (zwInput.isConnected) {\r\n                    state.compilationString +=\r\n                        this._declareOutput(v4Output, state) + ` = vec4(${xyInput.associatedVariableName}, ${zwInput.associatedVariableName})${this._buildSwizzle(4)};\\n`;\r\n                } else {\r\n                    state.compilationString +=\r\n                        this._declareOutput(v4Output, state) +\r\n                        ` = vec4(${xyInput.associatedVariableName}, ${zInput.isConnected ? this._writeVariable(zInput) : \"0.0\"}, ${\r\n                            wInput.isConnected ? this._writeVariable(wInput) : \"0.0\"\r\n                        })${this._buildSwizzle(4)};\\n`;\r\n                }\r\n            }\r\n\r\n            if (v3Output.hasEndpoints) {\r\n                state.compilationString +=\r\n                    this._declareOutput(v3Output, state) +\r\n                    ` = vec3(${xyInput.associatedVariableName}, ${zInput.isConnected ? this._writeVariable(zInput) : \"0.0\"})${this._buildSwizzle(3)};\\n`;\r\n            }\r\n\r\n            if (v2Output.hasEndpoints) {\r\n                state.compilationString += this._declareOutput(v2Output, state) + ` = ${xyInput.associatedVariableName}${this._buildSwizzle(2)};\\n`;\r\n            }\r\n\r\n            if (v2CompOutput.hasEndpoints) {\r\n                if (zwInput.isConnected) {\r\n                    state.compilationString += this._declareOutput(v2CompOutput, state) + ` = ${zwInput.associatedVariableName}${this._buildSwizzle(2)};\\n`;\r\n                } else {\r\n                    state.compilationString +=\r\n                        this._declareOutput(v2CompOutput, state) +\r\n                        ` = vec2(${zInput.isConnected ? this._writeVariable(zInput) : \"0.0\"}, ${wInput.isConnected ? this._writeVariable(wInput) : \"0.0\"})${this._buildSwizzle(\r\n                            2\r\n                        )};\\n`;\r\n                }\r\n            }\r\n        } else {\r\n            if (v4Output.hasEndpoints) {\r\n                if (zwInput.isConnected) {\r\n                    state.compilationString +=\r\n                        this._declareOutput(v4Output, state) +\r\n                        ` = vec4(${xInput.isConnected ? this._writeVariable(xInput) : \"0.0\"}, ${yInput.isConnected ? this._writeVariable(yInput) : \"0.0\"}, ${\r\n                            zwInput.associatedVariableName\r\n                        })${this._buildSwizzle(4)};\\n`;\r\n                } else {\r\n                    state.compilationString +=\r\n                        this._declareOutput(v4Output, state) +\r\n                        ` = vec4(${xInput.isConnected ? this._writeVariable(xInput) : \"0.0\"}, ${yInput.isConnected ? this._writeVariable(yInput) : \"0.0\"}, ${\r\n                            zInput.isConnected ? this._writeVariable(zInput) : \"0.0\"\r\n                        }, ${wInput.isConnected ? this._writeVariable(wInput) : \"0.0\"})${this._buildSwizzle(4)};\\n`;\r\n                }\r\n            }\r\n\r\n            if (v3Output.hasEndpoints) {\r\n                state.compilationString +=\r\n                    this._declareOutput(v3Output, state) +\r\n                    ` = vec3(${xInput.isConnected ? this._writeVariable(xInput) : \"0.0\"}, ${yInput.isConnected ? this._writeVariable(yInput) : \"0.0\"}, ${\r\n                        zInput.isConnected ? this._writeVariable(zInput) : \"0.0\"\r\n                    })${this._buildSwizzle(3)};\\n`;\r\n            }\r\n\r\n            if (v2Output.hasEndpoints) {\r\n                state.compilationString +=\r\n                    this._declareOutput(v2Output, state) +\r\n                    ` = vec2(${xInput.isConnected ? this._writeVariable(xInput) : \"0.0\"}, ${yInput.isConnected ? this._writeVariable(yInput) : \"0.0\"})${this._buildSwizzle(2)};\\n`;\r\n            }\r\n\r\n            if (v2CompOutput.hasEndpoints) {\r\n                if (zwInput.isConnected) {\r\n                    state.compilationString += this._declareOutput(v2CompOutput, state) + ` = ${zwInput.associatedVariableName}${this._buildSwizzle(2)};\\n`;\r\n                } else {\r\n                    state.compilationString +=\r\n                        this._declareOutput(v2CompOutput, state) +\r\n                        ` = vec2(${zInput.isConnected ? this._writeVariable(zInput) : \"0.0\"}, ${wInput.isConnected ? this._writeVariable(wInput) : \"0.0\"})${this._buildSwizzle(\r\n                            2\r\n                        )};\\n`;\r\n                }\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.xSwizzle = this.xSwizzle;\r\n        serializationObject.ySwizzle = this.ySwizzle;\r\n        serializationObject.zSwizzle = this.zSwizzle;\r\n        serializationObject.wSwizzle = this.wSwizzle;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.xSwizzle = serializationObject.xSwizzle ?? \"x\";\r\n        this.ySwizzle = serializationObject.ySwizzle ?? \"y\";\r\n        this.zSwizzle = serializationObject.zSwizzle ?? \"z\";\r\n        this.wSwizzle = serializationObject.wSwizzle ?? \"w\";\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n        codeString += `${this._codeVariableName}.xSwizzle = \"${this.xSwizzle}\";\\n`;\r\n        codeString += `${this._codeVariableName}.ySwizzle = \"${this.ySwizzle}\";\\n`;\r\n        codeString += `${this._codeVariableName}.zSwizzle = \"${this.zSwizzle}\";\\n`;\r\n        codeString += `${this._codeVariableName}.wSwizzle = \"${this.wSwizzle}\";\\n`;\r\n\r\n        return codeString;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.VectorMergerBlock\", VectorMergerBlock);\r\n"]}