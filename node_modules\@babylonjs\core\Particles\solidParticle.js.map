{"version": 3, "file": "solidParticle.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Particles/solidParticle.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACzF,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAE3D,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAGtD;;GAEG;AACH,MAAM,OAAO,aAAa;IA2HtB;;;OAGG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC;IACvC,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,YACI,aAAqB,EACrB,UAAkB,EAClB,aAAqB,EACrB,WAAmB,EACnB,KAA2B,EAC3B,OAAe,EACf,UAAkB,EAClB,GAAwB,EACxB,oBAA4C,IAAI,EAChD,gBAAkC,IAAI;QAjK1C;;WAEG;QACI,QAAG,GAAW,CAAC,CAAC;QACvB;;WAEG;QACI,OAAE,GAAW,CAAC,CAAC;QACtB;;WAEG;QACI,UAAK,GAAqB,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAChE;;WAEG;QACI,aAAQ,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAC1C;;WAEG;QACI,aAAQ,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAK1C;;WAEG;QACI,YAAO,GAAY,OAAO,CAAC,GAAG,EAAE,CAAC;QACxC;;WAEG;QACI,QAAG,GAAY,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACtD;;WAEG;QACI,aAAQ,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAC1C;;WAEG;QACI,UAAK,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QACvC;;;;WAIG;QACI,uBAAkB,GAAY,KAAK,CAAC;QAC3C;;WAEG;QACI,UAAK,GAAY,IAAI,CAAC;QAC7B;;WAEG;QACI,cAAS,GAAY,IAAI,CAAC;QACjC;;;WAGG;QACI,SAAI,GAAW,CAAC,CAAC;QACxB;;WAEG;QACI,SAAI,GAAW,CAAC,CAAC;QAKxB;;WAEG;QACI,YAAO,GAAW,CAAC,CAAC;QAC3B;;WAEG;QACI,eAAU,GAAW,CAAC,CAAC;QAU9B;;WAEG;QACI,oBAAe,GAAY,KAAK,CAAC;QACxC;;WAEG;QACI,oBAAe,GAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACjF;;;WAGG;QACI,aAAQ,GAAqB,IAAI,CAAC;QACzC;;WAEG;QACI,kBAAa,GAAqB,IAAI,CAAC;QAC9C;;WAEG;QACI,UAAK,GAAkB,IAAI,CAAC;QACnC;;;;;;;;;aASK;QACE,oBAAe,GAAG,YAAY,CAAC,mCAAmC,CAAC;QAE1E;;WAEG;QACI,oBAAe,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QA2C7C,IAAI,CAAC,GAAG,GAAG,aAAa,CAAC;QACzB,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;QAC1B,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,MAAM,GAAe,KAAK,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,iBAAiB,EAAE;YACnB,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;YAC5C,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CAAC,iBAAiB,CAAC,OAAO,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;SAC/F;QACD,IAAI,aAAa,KAAK,IAAI,EAAE;YACxB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;SACtC;IACL,CAAC;IACD;;;;OAIG;IACI,SAAS,CAAC,MAAqB;QAClC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,MAAM,CAAC,kBAAkB,EAAE;gBAC3B,MAAM,CAAC,kBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAmB,CAAC,CAAC;aACjE;iBAAM;gBACH,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;aAC/D;SACJ;QACD,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,MAAM,CAAC,KAAK,EAAE;gBACd,MAAM,CAAC,KAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAM,CAAC,CAAC;aACvC;iBAAM;gBACH,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;aACrC;SACJ;QACD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACpD,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAC1B,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAClC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAChC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC9C,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;YAC7B,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;SAC7C;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IACD;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,KAAK,CAAC,KAAc;QAC3B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,UAAU,CAAC,CAAuB;QACzC,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;IAChC,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,MAA4B;QAC9C,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;YAChD,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACxB,OAAO,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,cAAc,CAAC,CAAC;SAChH;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE,KAAK,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,aAAsB;QACrC,OAAO,IAAI,CAAC,aAAa,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IAC9G,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,CAAS;QAC9B,IAAI,UAAsB,CAAC;QAC3B,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC;SACxC;aAAM;YACH,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/B,UAAU,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;SACxF;QAED,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,UAAU;IACnB;;;OAGG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IACD,IAAW,OAAO,CAAC,OAAe;QAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IAoDD;;;;OAIG;IACH,YACI,EAAU,EACV,KAAgB,EAChB,OAAiB,EACjB,OAAiB,EACjB,MAAgB,EAChB,OAAiB,EACjB,WAA8E,EAC9E,WAAoF,EACpF,QAA4B;QAnChC;;;WAGG;QACI,mBAAc,GAAW,CAAC,CAAC;QAiC9B,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;QACrC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,mBAAmB;IAsB5B;;;;;;OAMG;IACH,YAAY,GAAW,EAAE,GAAW,EAAE,SAAiB,EAAE,aAAqB;QA5B9E;;WAEG;QACI,QAAG,GAAW,CAAC,CAAC;QACvB;;WAEG;QACI,QAAG,GAAW,CAAC,CAAC;QACvB;;WAEG;QACI,kBAAa,GAAW,CAAC,CAAC;QACjC;;WAEG;QACI,eAAU,GAAW,GAAG,CAAC;QAChC;;WAEG;QACI,kBAAa,GAAW,CAAC,CAAC;QAU7B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACvC,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,mBAAmB;IAa5B;;OAEG;IACH;QACI,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/B,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAC7B,CAAC;IACD,6CAA6C;IAC7C,0BAA0B;IAC1B,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,IAAW,CAAC,CAAC,GAAW;QACpB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;IAC1B,CAAC;IACD,0BAA0B;IAC1B,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,IAAW,CAAC,CAAC,GAAW;QACpB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;IAC1B,CAAC;IACD,0BAA0B;IAC1B,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,IAAW,CAAC,CAAC,GAAW;QACpB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;IAC1B,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { <PERSON> } from \"../Maths/math.vector\";\r\nimport { Vector3, TmpVectors, Quaternion, Vector4, Vector2 } from \"../Maths/math.vector\";\r\nimport { Color4 } from \"../Maths/math.color\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport { BoundingInfo } from \"../Culling/boundingInfo\";\r\nimport { BoundingSphere } from \"../Culling/boundingSphere\";\r\nimport type { SolidParticleSystem } from \"./solidParticleSystem\";\r\nimport { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Plane } from \"../Maths/math.plane\";\r\nimport type { Material } from \"../Materials/material\";\r\n/**\r\n * Represents one particle of a solid particle system.\r\n */\r\nexport class SolidParticle {\r\n    /**\r\n     * particle global index\r\n     */\r\n    public idx: number = 0;\r\n    /**\r\n     * particle identifier\r\n     */\r\n    public id: number = 0;\r\n    /**\r\n     * The color of the particle\r\n     */\r\n    public color: Nullable<Color4> = new Color4(1.0, 1.0, 1.0, 1.0);\r\n    /**\r\n     * The world space position of the particle.\r\n     */\r\n    public position: Vector3 = Vector3.Zero();\r\n    /**\r\n     * The world space rotation of the particle. (Not use if rotationQuaternion is set)\r\n     */\r\n    public rotation: Vector3 = Vector3.Zero();\r\n    /**\r\n     * The world space rotation quaternion of the particle.\r\n     */\r\n    public rotationQuaternion: Nullable<Quaternion>;\r\n    /**\r\n     * The scaling of the particle.\r\n     */\r\n    public scaling: Vector3 = Vector3.One();\r\n    /**\r\n     * The uvs of the particle.\r\n     */\r\n    public uvs: Vector4 = new Vector4(0.0, 0.0, 1.0, 1.0);\r\n    /**\r\n     * The current speed of the particle.\r\n     */\r\n    public velocity: Vector3 = Vector3.Zero();\r\n    /**\r\n     * The pivot point in the particle local space.\r\n     */\r\n    public pivot: Vector3 = Vector3.Zero();\r\n    /**\r\n     * Must the particle be translated from its pivot point in its local space ?\r\n     * In this case, the pivot point is set at the origin of the particle local space and the particle is translated.\r\n     * Default : false\r\n     */\r\n    public translateFromPivot: boolean = false;\r\n    /**\r\n     * Is the particle active or not ?\r\n     */\r\n    public alive: boolean = true;\r\n    /**\r\n     * Is the particle visible or not ?\r\n     */\r\n    public isVisible: boolean = true;\r\n    /**\r\n     * Index of this particle in the global \"positions\" array (Internal use)\r\n     * @internal\r\n     */\r\n    public _pos: number = 0;\r\n    /**\r\n     * @internal Index of this particle in the global \"indices\" array (Internal use)\r\n     */\r\n    public _ind: number = 0;\r\n    /**\r\n     * @internal ModelShape of this particle (Internal use)\r\n     */\r\n    public _model: ModelShape;\r\n    /**\r\n     * ModelShape id of this particle\r\n     */\r\n    public shapeId: number = 0;\r\n    /**\r\n     * Index of the particle in its shape id\r\n     */\r\n    public idxInShape: number = 0;\r\n    /**\r\n     * @internal Reference to the shape model BoundingInfo object (Internal use)\r\n     */\r\n    public _modelBoundingInfo: BoundingInfo;\r\n    private _boundingInfo: BoundingInfo;\r\n    /**\r\n     * @internal Reference to the SPS what the particle belongs to (Internal use)\r\n     */\r\n    public _sps: SolidParticleSystem;\r\n    /**\r\n     * @internal Still set as invisible in order to skip useless computations (Internal use)\r\n     */\r\n    public _stillInvisible: boolean = false;\r\n    /**\r\n     * @internal Last computed particle rotation matrix\r\n     */\r\n    public _rotationMatrix: number[] = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0];\r\n    /**\r\n     * Parent particle Id, if any.\r\n     * Default null.\r\n     */\r\n    public parentId: Nullable<number> = null;\r\n    /**\r\n     * The particle material identifier (integer) when MultiMaterials are enabled in the SPS.\r\n     */\r\n    public materialIndex: Nullable<number> = null;\r\n    /**\r\n     * Custom object or properties.\r\n     */\r\n    public props: Nullable<any> = null;\r\n    /**\r\n     * The culling strategy to use to check whether the solid particle must be culled or not when using isInFrustum().\r\n     * The possible values are :\r\n     * - AbstractMesh.CULLINGSTRATEGY_STANDARD\r\n     * - AbstractMesh.CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY\r\n     * - AbstractMesh.CULLINGSTRATEGY_OPTIMISTIC_INCLUSION\r\n     * - AbstractMesh.CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY\r\n     * The default value for solid particles is AbstractMesh.CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY\r\n     * Please read each static variable documentation in the class AbstractMesh to get details about the culling process.\r\n     * */\r\n    public cullingStrategy = AbstractMesh.CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY;\r\n\r\n    /**\r\n     * @internal Internal global position in the SPS.\r\n     */\r\n    public _globalPosition: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * Particle BoundingInfo object\r\n     * @returns a BoundingInfo\r\n     */\r\n    public getBoundingInfo(): BoundingInfo {\r\n        return this._boundingInfo;\r\n    }\r\n\r\n    /**\r\n     * Returns true if there is already a bounding info\r\n     */\r\n    public get hasBoundingInfo(): boolean {\r\n        return this._boundingInfo !== null;\r\n    }\r\n\r\n    /**\r\n     * Creates a Solid Particle object.\r\n     * Don't create particles manually, use instead the Solid Particle System internal tools like _addParticle()\r\n     * @param particleIndex (integer) is the particle index in the Solid Particle System pool.\r\n     * @param particleId (integer) is the particle identifier. Unless some particles are removed from the SPS, it's the same value than the particle idx.\r\n     * @param positionIndex (integer) is the starting index of the particle vertices in the SPS \"positions\" array.\r\n     * @param indiceIndex (integer) is the starting index of the particle indices in the SPS \"indices\" array.\r\n     * @param model (ModelShape) is a reference to the model shape on what the particle is designed.\r\n     * @param shapeId (integer) is the model shape identifier in the SPS.\r\n     * @param idxInShape (integer) is the index of the particle in the current model (ex: the 10th box of addShape(box, 30))\r\n     * @param sps defines the sps it is associated to\r\n     * @param modelBoundingInfo is the reference to the model BoundingInfo used for intersection computations.\r\n     * @param materialIndex is the particle material identifier (integer) when the MultiMaterials are enabled in the SPS.\r\n     */\r\n    constructor(\r\n        particleIndex: number,\r\n        particleId: number,\r\n        positionIndex: number,\r\n        indiceIndex: number,\r\n        model: Nullable<ModelShape>,\r\n        shapeId: number,\r\n        idxInShape: number,\r\n        sps: SolidParticleSystem,\r\n        modelBoundingInfo: Nullable<BoundingInfo> = null,\r\n        materialIndex: Nullable<number> = null\r\n    ) {\r\n        this.idx = particleIndex;\r\n        this.id = particleId;\r\n        this._pos = positionIndex;\r\n        this._ind = indiceIndex;\r\n        this._model = <ModelShape>model;\r\n        this.shapeId = shapeId;\r\n        this.idxInShape = idxInShape;\r\n        this._sps = sps;\r\n        if (modelBoundingInfo) {\r\n            this._modelBoundingInfo = modelBoundingInfo;\r\n            this._boundingInfo = new BoundingInfo(modelBoundingInfo.minimum, modelBoundingInfo.maximum);\r\n        }\r\n        if (materialIndex !== null) {\r\n            this.materialIndex = materialIndex;\r\n        }\r\n    }\r\n    /**\r\n     * Copies the particle property values into the existing target : position, rotation, scaling, uvs, colors, pivot, parent, visibility, alive\r\n     * @param target the particle target\r\n     * @returns the current particle\r\n     */\r\n    public copyToRef(target: SolidParticle): SolidParticle {\r\n        target.position.copyFrom(this.position);\r\n        target.rotation.copyFrom(this.rotation);\r\n        if (this.rotationQuaternion) {\r\n            if (target.rotationQuaternion) {\r\n                target.rotationQuaternion!.copyFrom(this.rotationQuaternion!);\r\n            } else {\r\n                target.rotationQuaternion = this.rotationQuaternion.clone();\r\n            }\r\n        }\r\n        target.scaling.copyFrom(this.scaling);\r\n        if (this.color) {\r\n            if (target.color) {\r\n                target.color!.copyFrom(this.color!);\r\n            } else {\r\n                target.color = this.color.clone();\r\n            }\r\n        }\r\n        target.uvs.copyFrom(this.uvs);\r\n        target.velocity.copyFrom(this.velocity);\r\n        target.pivot.copyFrom(this.pivot);\r\n        target.translateFromPivot = this.translateFromPivot;\r\n        target.alive = this.alive;\r\n        target.isVisible = this.isVisible;\r\n        target.parentId = this.parentId;\r\n        target.cullingStrategy = this.cullingStrategy;\r\n        if (this.materialIndex !== null) {\r\n            target.materialIndex = this.materialIndex;\r\n        }\r\n        return this;\r\n    }\r\n    /**\r\n     * Legacy support, changed scale to scaling\r\n     */\r\n    public get scale(): Vector3 {\r\n        return this.scaling;\r\n    }\r\n\r\n    /**\r\n     * Legacy support, changed scale to scaling\r\n     */\r\n    public set scale(scale: Vector3) {\r\n        this.scaling = scale;\r\n    }\r\n\r\n    /**\r\n     * Legacy support, changed quaternion to rotationQuaternion\r\n     */\r\n    public get quaternion(): Nullable<Quaternion> {\r\n        return this.rotationQuaternion;\r\n    }\r\n\r\n    /**\r\n     * Legacy support, changed quaternion to rotationQuaternion\r\n     */\r\n    public set quaternion(q: Nullable<Quaternion>) {\r\n        this.rotationQuaternion = q;\r\n    }\r\n\r\n    /**\r\n     * Returns a boolean. True if the particle intersects another particle or another mesh, else false.\r\n     * The intersection is computed on the particle bounding sphere and Axis Aligned Bounding Box (AABB)\r\n     * @param target is the object (solid particle or mesh) what the intersection is computed against.\r\n     * @returns true if it intersects\r\n     */\r\n    public intersectsMesh(target: Mesh | SolidParticle): boolean {\r\n        if (!this._boundingInfo || !target.hasBoundingInfo) {\r\n            return false;\r\n        }\r\n        if (this._sps._bSphereOnly) {\r\n            return BoundingSphere.Intersects(this._boundingInfo.boundingSphere, target.getBoundingInfo().boundingSphere);\r\n        }\r\n        return this._boundingInfo.intersects(target.getBoundingInfo(), false);\r\n    }\r\n\r\n    /**\r\n     * Returns `true` if the solid particle is within the frustum defined by the passed array of planes.\r\n     * A particle is in the frustum if its bounding box intersects the frustum\r\n     * @param frustumPlanes defines the frustum to test\r\n     * @returns true if the particle is in the frustum planes\r\n     */\r\n    public isInFrustum(frustumPlanes: Plane[]): boolean {\r\n        return this._boundingInfo !== null && this._boundingInfo.isInFrustum(frustumPlanes, this.cullingStrategy);\r\n    }\r\n\r\n    /**\r\n     * get the rotation matrix of the particle\r\n     * @internal\r\n     */\r\n    public getRotationMatrix(m: Matrix) {\r\n        let quaternion: Quaternion;\r\n        if (this.rotationQuaternion) {\r\n            quaternion = this.rotationQuaternion;\r\n        } else {\r\n            quaternion = TmpVectors.Quaternion[0];\r\n            const rotation = this.rotation;\r\n            Quaternion.RotationYawPitchRollToRef(rotation.y, rotation.x, rotation.z, quaternion);\r\n        }\r\n\r\n        quaternion.toRotationMatrix(m);\r\n    }\r\n}\r\n\r\n/**\r\n * Represents the shape of the model used by one particle of a solid particle system.\r\n * SPS internal tool, don't use it manually.\r\n */\r\nexport class ModelShape {\r\n    /**\r\n     * Get or set the shapeId\r\n     * @deprecated Please use shapeId instead\r\n     */\r\n    public get shapeID(): number {\r\n        return this.shapeId;\r\n    }\r\n    public set shapeID(shapeID: number) {\r\n        this.shapeId = shapeID;\r\n    }\r\n    /**\r\n     * The shape id\r\n     * @internal\r\n     */\r\n    public shapeId: number;\r\n    /**\r\n     * flat array of model positions (internal use)\r\n     * @internal\r\n     */\r\n    public _shape: Vector3[];\r\n    /**\r\n     * flat array of model UVs (internal use)\r\n     * @internal\r\n     */\r\n    public _shapeUV: number[];\r\n    /**\r\n     * color array of the model\r\n     * @internal\r\n     */\r\n    public _shapeColors: number[];\r\n    /**\r\n     * indices array of the model\r\n     * @internal\r\n     */\r\n    public _indices: number[];\r\n    /**\r\n     * normals array of the model\r\n     * @internal\r\n     */\r\n    public _normals: number[];\r\n    /**\r\n     * length of the shape in the model indices array (internal use)\r\n     * @internal\r\n     */\r\n    public _indicesLength: number = 0;\r\n    /**\r\n     * Custom position function (internal use)\r\n     * @internal\r\n     */\r\n    public _positionFunction: Nullable<(particle: SolidParticle, i: number, s: number) => void>;\r\n    /**\r\n     * Custom vertex function (internal use)\r\n     * @internal\r\n     */\r\n    public _vertexFunction: Nullable<(particle: SolidParticle, vertex: Vector3, i: number) => void>;\r\n    /**\r\n     * Model material (internal use)\r\n     * @internal\r\n     */\r\n    public _material: Nullable<Material>;\r\n\r\n    /**\r\n     * Creates a ModelShape object. This is an internal simplified reference to a mesh used as for a model to replicate particles from by the SPS.\r\n     * SPS internal tool, don't use it manually.\r\n     * @internal\r\n     */\r\n    constructor(\r\n        id: number,\r\n        shape: Vector3[],\r\n        indices: number[],\r\n        normals: number[],\r\n        colors: number[],\r\n        shapeUV: number[],\r\n        posFunction: Nullable<(particle: SolidParticle, i: number, s: number) => void>,\r\n        vtxFunction: Nullable<(particle: SolidParticle, vertex: Vector3, i: number) => void>,\r\n        material: Nullable<Material>\r\n    ) {\r\n        this.shapeId = id;\r\n        this._shape = shape;\r\n        this._indices = indices;\r\n        this._indicesLength = indices.length;\r\n        this._shapeUV = shapeUV;\r\n        this._shapeColors = colors;\r\n        this._normals = normals;\r\n        this._positionFunction = posFunction;\r\n        this._vertexFunction = vtxFunction;\r\n        this._material = material;\r\n    }\r\n}\r\n\r\n/**\r\n * Represents a Depth Sorted Particle in the solid particle system.\r\n * @internal\r\n */\r\nexport class DepthSortedParticle {\r\n    /**\r\n     * Particle index\r\n     */\r\n    public idx: number = 0;\r\n    /**\r\n     * Index of the particle in the \"indices\" array\r\n     */\r\n    public ind: number = 0;\r\n    /**\r\n     * Length of the particle shape in the \"indices\" array\r\n     */\r\n    public indicesLength: number = 0;\r\n    /**\r\n     * Squared distance from the particle to the camera\r\n     */\r\n    public sqDistance: number = 0.0;\r\n    /**\r\n     * Material index when used with MultiMaterials\r\n     */\r\n    public materialIndex: number = 0;\r\n\r\n    /**\r\n     * Creates a new sorted particle\r\n     * @param idx\r\n     * @param ind\r\n     * @param indLength\r\n     * @param materialIndex\r\n     */\r\n    constructor(idx: number, ind: number, indLength: number, materialIndex: number) {\r\n        this.idx = idx;\r\n        this.ind = ind;\r\n        this.indicesLength = indLength;\r\n        this.materialIndex = materialIndex;\r\n    }\r\n}\r\n\r\n/**\r\n * Represents a solid particle vertex\r\n */\r\nexport class SolidParticleVertex {\r\n    /**\r\n     * Vertex position\r\n     */\r\n    public position: Vector3;\r\n    /**\r\n     * Vertex color\r\n     */\r\n    public color: Color4;\r\n    /**\r\n     * Vertex UV\r\n     */\r\n    public uv: Vector2;\r\n    /**\r\n     * Creates a new solid particle vertex\r\n     */\r\n    constructor() {\r\n        this.position = Vector3.Zero();\r\n        this.color = new Color4(1.0, 1.0, 1.0, 1.0);\r\n        this.uv = Vector2.Zero();\r\n    }\r\n    // Getters and Setters for back-compatibility\r\n    /** Vertex x coordinate */\r\n    public get x(): number {\r\n        return this.position.x;\r\n    }\r\n    public set x(val: number) {\r\n        this.position.x = val;\r\n    }\r\n    /** Vertex y coordinate */\r\n    public get y(): number {\r\n        return this.position.y;\r\n    }\r\n    public set y(val: number) {\r\n        this.position.y = val;\r\n    }\r\n    /** Vertex z coordinate */\r\n    public get z(): number {\r\n        return this.position.z;\r\n    }\r\n    public set z(val: number) {\r\n        this.position.z = val;\r\n    }\r\n}\r\n"]}