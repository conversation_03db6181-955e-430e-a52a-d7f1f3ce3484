{"version": 3, "file": "webXRFeaturesManager.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/XR/webXRFeaturesManager.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAsEtC;;GAEG;AACH,MAAM,OAAO,gBAAgB;;AACzB;;GAEG;AACoB,8BAAa,GAAG,kBAAkB,CAAC;AAC1D;;GAEG;AACoB,mCAAkB,GAAG,uBAAuB,CAAC;AACpE;;GAEG;AACoB,yBAAQ,GAAG,aAAa,CAAC;AAChD;;GAEG;AACoB,+BAAc,GAAG,mBAAmB,CAAC;AAC5D;;GAEG;AACoB,oCAAmB,GAAG,uBAAuB,CAAC;AACrE;;GAEG;AACoB,gCAAe,GAAG,oBAAoB,CAAC;AAC9D;;GAEG;AACoB,kCAAiB,GAAG,iCAAiC,CAAC;AAC7E;;GAEG;AACoB,8BAAa,GAAG,6BAA6B,CAAC;AACrE;;GAEG;AACoB,+BAAc,GAAG,mBAAmB,CAAC;AAC5D;;GAEG;AACoB,8BAAa,GAAG,kBAAkB,CAAC;AAC1D;;GAEG;AACoB,+BAAc,GAAG,mBAAmB,CAAC;AAC5D;;GAEG;AACoB,iCAAgB,GAAG,qBAAqB,CAAC;AAChE;;GAEG;AACoB,4BAAW,GAAG,gBAAgB,CAAC;AACtD;;GAEG;AACoB,yBAAQ,GAAG,wBAAwB,CAAC;AAC3D;;GAEG;AACoB,iCAAgB,GAAG,qBAAqB,CAAC;AAChE;;GAEG;AACoB,6BAAY,GAAG,iBAAiB,CAAC;AACxD;;GAEG;AACoB,mCAAkB,GAAG,uBAAuB,CAAC;AACpE;;GAEG;AACoB,uBAAM,GAAG,WAAW,CAAC;AAC5C;;GAEG;AACoB,8BAAa,GAAG,kBAAkB,CAAC;AAC1D;;GAEG;AACoB,2BAAU,GAAG,eAAe,CAAC;AACpD;;GAEG;AACoB,kCAAiB,GAAG,sBAAsB,CAAC;AAQtE;;;;;GAKG;AACH,MAAM,OAAO,oBAAoB;IA0B7B;;;;OAIG;IACH,YAAoB,iBAAsC;QAAtC,sBAAiB,GAAjB,iBAAiB,CAAqB;QAtBlD,cAAS,GAOb,EAAE,CAAC;QAgBH,6CAA6C;QAC7C,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,kBAAkB,EAAE,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;gBAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAC5C,IAAI,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,iBAAiB,EAAE;oBAChH,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;iBACnC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC7C,IAAI,CAAC,kBAAkB,EAAE,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;gBAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAC5C,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,qBAAqB,CAAC,QAAQ,EAAE;oBAC3D,6BAA6B;oBAC7B,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;iBACnC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,eAAe,CAAC,WAAmB,EAAE,mBAA4C,EAAE,UAAkB,CAAC,EAAE,SAAkB,KAAK;QACzI,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QACnG,IAAI,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE;YACvD,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC;SACzD;QACD,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC;SACzD;QACD,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,GAAG,mBAAmB,CAAC;IACxE,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,gBAAgB,CAAC,WAAmB,EAAE,UAAkB,CAAC,EAAE,gBAAqC,EAAE,OAAa;QACzH,MAAM,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC;QAC1E,IAAI,CAAC,mBAAmB,EAAE;YACtB,kCAAkC;YAClC,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;SACxC;QAED,OAAO,mBAAmB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IAC1D,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,oBAAoB;QAC9B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAChD,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,oBAAoB,CAAC,WAAmB;QAClD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,yBAAyB,CAAC,WAAmB;QACvD,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACvG,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,yBAAyB,CAAC,WAAmB;QACvD,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACvG,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,WAAmB;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,QAAQ,EAAE;YACvE,MAAM,QAAQ,GAAG,OAAO,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC;YACxD,IAAI,CAAC,QAAQ,EAAE;gBACX,KAAK,CAAC,IAAI,CAAC,WAAW,WAAW,mBAAmB,CAAC,CAAC;aACzD;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,WAAmB;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,OAAO,IAAI,OAAO,CAAC,qBAAqB,CAAC,QAAQ,EAAE;YACnD,MAAM,QAAQ,GAAG,OAAO,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC;YACxD,IAAI,CAAC,QAAQ,EAAE;gBACX,KAAK,CAAC,IAAI,CAAC,WAAW,WAAW,mBAAmB,CAAC,CAAC;aACzD;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACH,gEAAgE;IACzD,cAAc,CAAC,WAAsC;QACxD,MAAM,IAAI,GAAG,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC;QAC9E,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;YAC5B,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACzB,OAAO,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC5B,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,kBAAkB,EAAE,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC1C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;OAUG;IACI,aAAa;IAChB,gEAAgE;IAChE,WAAsC,EACtC,UAA2B,QAAQ,EACnC,gBAAqB,EAAE,EACvB,mBAA4B,IAAI,EAChC,WAAoB,IAAI;QAExB,MAAM,IAAI,GAAG,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC;QAC9E,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC7B,IAAI,CAAC,OAAO,EAAE;gBACV,MAAM,IAAI,KAAK,CAAC,+BAA+B,IAAI,KAAK,OAAO,GAAG,CAAC,CAAC;aACvE;YACD,IAAI,OAAO,KAAK,QAAQ,EAAE;gBACtB,aAAa,GAAG,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;aACxE;iBAAM,IAAI,OAAO,KAAK,QAAQ,EAAE;gBAC7B,aAAa,GAAG,oBAAoB,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;aACxE;iBAAM;gBACH,+CAA+C;gBAC/C,aAAa,GAAG,CAAC,OAAO,CAAC;aAC5B;YACD,IAAI,aAAa,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC,EAAE;gBAC9C,MAAM,IAAI,KAAK,CAAC,uBAAuB,IAAI,KAAK,OAAO,GAAG,CAAC,CAAC;aAC/D;SACJ;aAAM;YACH,aAAa,GAAG,OAAO,CAAC;SAC3B;QAED,uCAAuC;QACvC,MAAM,kBAAkB,GAAG,oBAAoB,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC3E,IAAI,kBAAkB,KAAK,SAAS,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE;YAClG,MAAM,IAAI,KAAK,CAAC,WAAW,IAAI,4BAA4B,kBAAkB,cAAc,CAAC,CAAC;SAChG;QAED,+BAA+B;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACrC,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;QAC5H,IAAI,CAAC,iBAAiB,EAAE;YACpB,gBAAgB;YAChB,MAAM,IAAI,KAAK,CAAC,uBAAuB,IAAI,EAAE,CAAC,CAAC;SAClD;QAED,oFAAoF;QACpF,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SAC7B;QAED,MAAM,WAAW,GAAG,iBAAiB,EAAE,CAAC;QACxC,IAAI,WAAW,CAAC,SAAS,EAAE;YACvB,MAAM,eAAe,GAAG,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;YACpG,IAAI,CAAC,eAAe,EAAE;gBAClB,MAAM,IAAI,KAAK,CAAC,8EAA8E,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACrI;SACJ;QACD,IAAI,WAAW,CAAC,YAAY,EAAE,EAAE;YAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;gBACnB,qBAAqB,EAAE,WAAW;gBAClC,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,aAAa;gBACtB,QAAQ;aACX,CAAC;YAEF,IAAI,gBAAgB,EAAE;gBAClB,iDAAiD;gBACjD,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,QAAQ,EAAE;oBACxF,iBAAiB;oBACjB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;iBAC5B;aACJ;iBAAM;gBACH,0CAA0C;gBAC1C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,iBAAiB,GAAG,IAAI,CAAC;aACvE;YAED,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC;SACrD;aAAM;YACH,IAAI,QAAQ,EAAE;gBACV,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;aACtD;iBAAM;gBACH,KAAK,CAAC,IAAI,CAAC,WAAW,IAAI,2EAA2E,CAAC,CAAC;gBACvG,OAAO,WAAW,CAAC;aACtB;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,WAAmB;QACxC,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,qBAAqB,CAAC;IAC5F,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,0BAA0B,CAAC,aAA4B;QAChE,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClD,KAAK,MAAM,WAAW,IAAI,eAAe,EAAE;YACvC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC5C,MAAM,UAAU,GAAG,OAAO,CAAC,qBAAqB,CAAC,mBAAmB,CAAC;YACrE,IAAI,UAAU,EAAE;gBACZ,IAAI,OAAO,CAAC,QAAQ,EAAE;oBAClB,aAAa,CAAC,gBAAgB,GAAG,aAAa,CAAC,gBAAgB,IAAI,EAAE,CAAC;oBACtE,IAAI,aAAa,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;wBAC3D,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;qBACnD;iBACJ;qBAAM;oBACH,aAAa,CAAC,gBAAgB,GAAG,aAAa,CAAC,gBAAgB,IAAI,EAAE,CAAC;oBACtE,IAAI,aAAa,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;wBAC3D,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;qBACnD;iBACJ;aACJ;YACD,IAAI,OAAO,CAAC,qBAAqB,CAAC,yBAAyB,EAAE;gBACzD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,qBAAqB,CAAC,yBAAyB,EAAE,CAAC;gBACjF,aAAa,GAAG;oBACZ,GAAG,aAAa;oBAChB,GAAG,QAAQ;iBACd,CAAC;aACL;SACJ;QACD,OAAO,aAAa,CAAC;IACzB,CAAC;;AA9UuB,uCAAkB,GAMtC,EAAE,AANoC,CAMnC;AAWP;;GAEG;AACqB,yCAAoB,GAA8B;IACtE,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,gBAAgB,CAAC,QAAQ;IAC3D,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,gBAAgB,CAAC,aAAa;CAC9D,AAH2C,CAG1C", "sourcesContent": ["import type { WebXRSessionManager } from \"./webXRSessionManager\";\r\nimport type { IDisposable } from \"../scene\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport type { Observable } from \"core/Misc/observable\";\r\n\r\n/**\r\n * Defining the interface required for a (webxr) feature\r\n */\r\nexport interface IWebXRFeature extends IDisposable {\r\n    /**\r\n     * Is this feature attached\r\n     */\r\n    attached: boolean;\r\n    /**\r\n     * Should auto-attach be disabled?\r\n     */\r\n    disableAutoAttach: boolean;\r\n\r\n    /**\r\n     * Attach the feature to the session\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @param force should attachment be forced (even when already attached)\r\n     * @returns true if successful.\r\n     */\r\n    attach(force?: boolean): boolean;\r\n    /**\r\n     * Detach the feature from the session\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    detach(): boolean;\r\n\r\n    /**\r\n     * This function will be executed during before enabling the feature and can be used to not-allow enabling it.\r\n     * Note that at this point the session has NOT started, so this is purely checking if the browser supports it\r\n     *\r\n     * @returns whether or not the feature is compatible in this environment\r\n     */\r\n    isCompatible(): boolean;\r\n\r\n    /**\r\n     * Was this feature disposed;\r\n     */\r\n    isDisposed: boolean;\r\n\r\n    /**\r\n     * The name of the native xr feature name, if applicable (like anchor, hit-test, or hand-tracking)\r\n     */\r\n    xrNativeFeatureName?: string;\r\n\r\n    /**\r\n     * A list of (Babylon WebXR) features this feature depends on\r\n     */\r\n    dependsOn?: string[];\r\n\r\n    /**\r\n     * If this feature requires to extend the XRSessionInit object, this function will return the partial XR session init object\r\n     */\r\n    getXRSessionInitExtension?: () => Promise<Partial<XRSessionInit>>;\r\n\r\n    /**\r\n     * Triggered when the feature is attached\r\n     */\r\n    onFeatureAttachObservable: Observable<IWebXRFeature>;\r\n    /**\r\n     * Triggered when the feature is detached\r\n     */\r\n    onFeatureDetachObservable: Observable<IWebXRFeature>;\r\n}\r\n\r\n/**\r\n * A list of the currently available features without referencing them\r\n */\r\nexport class WebXRFeatureName {\r\n    /**\r\n     * The name of the anchor system feature\r\n     */\r\n    public static readonly ANCHOR_SYSTEM = \"xr-anchor-system\";\r\n    /**\r\n     * The name of the background remover feature\r\n     */\r\n    public static readonly BACKGROUND_REMOVER = \"xr-background-remover\";\r\n    /**\r\n     * The name of the hit test feature\r\n     */\r\n    public static readonly HIT_TEST = \"xr-hit-test\";\r\n    /**\r\n     * The name of the mesh detection feature\r\n     */\r\n    public static readonly MESH_DETECTION = \"xr-mesh-detection\";\r\n    /**\r\n     * physics impostors for xr controllers feature\r\n     */\r\n    public static readonly PHYSICS_CONTROLLERS = \"xr-physics-controller\";\r\n    /**\r\n     * The name of the plane detection feature\r\n     */\r\n    public static readonly PLANE_DETECTION = \"xr-plane-detection\";\r\n    /**\r\n     * The name of the pointer selection feature\r\n     */\r\n    public static readonly POINTER_SELECTION = \"xr-controller-pointer-selection\";\r\n    /**\r\n     * The name of the teleportation feature\r\n     */\r\n    public static readonly TELEPORTATION = \"xr-controller-teleportation\";\r\n    /**\r\n     * The name of the feature points feature.\r\n     */\r\n    public static readonly FEATURE_POINTS = \"xr-feature-points\";\r\n    /**\r\n     * The name of the hand tracking feature.\r\n     */\r\n    public static readonly HAND_TRACKING = \"xr-hand-tracking\";\r\n    /**\r\n     * The name of the image tracking feature\r\n     */\r\n    public static readonly IMAGE_TRACKING = \"xr-image-tracking\";\r\n    /**\r\n     * The name of the near interaction feature\r\n     */\r\n    public static readonly NEAR_INTERACTION = \"xr-near-interaction\";\r\n    /**\r\n     * The name of the DOM overlay feature\r\n     */\r\n    public static readonly DOM_OVERLAY = \"xr-dom-overlay\";\r\n    /**\r\n     * The name of the movement feature\r\n     */\r\n    public static readonly MOVEMENT = \"xr-controller-movement\";\r\n    /**\r\n     * The name of the light estimation feature\r\n     */\r\n    public static readonly LIGHT_ESTIMATION = \"xr-light-estimation\";\r\n    /**\r\n     * The name of the eye tracking feature\r\n     */\r\n    public static readonly EYE_TRACKING = \"xr-eye-tracking\";\r\n    /**\r\n     * The name of the walking locomotion feature\r\n     */\r\n    public static readonly WALKING_LOCOMOTION = \"xr-walking-locomotion\";\r\n    /**\r\n     * The name of the composition layers feature\r\n     */\r\n    public static readonly LAYERS = \"xr-layers\";\r\n    /**\r\n     * The name of the depth sensing feature\r\n     */\r\n    public static readonly DEPTH_SENSING = \"xr-depth-sensing\";\r\n    /**\r\n     * The name of the WebXR Space Warp feature\r\n     */\r\n    public static readonly SPACE_WARP = \"xr-space-warp\";\r\n    /**\r\n     * The name of the WebXR Raw Camera Access feature\r\n     */\r\n    public static readonly RAW_CAMERA_ACCESS = \"xr-raw-camera-access\";\r\n}\r\n\r\n/**\r\n * Defining the constructor of a feature. Used to register the modules.\r\n */\r\nexport type WebXRFeatureConstructor = (xrSessionManager: WebXRSessionManager, options?: any) => () => IWebXRFeature;\r\n\r\n/**\r\n * The WebXR features manager is responsible of enabling or disabling features required for the current XR session.\r\n * It is mainly used in AR sessions.\r\n *\r\n * A feature can have a version that is defined by Babylon (and does not correspond with the webxr version).\r\n */\r\nexport class WebXRFeaturesManager implements IDisposable {\r\n    private static readonly _AvailableFeatures: {\r\n        [name: string]: {\r\n            stable: number;\r\n            latest: number;\r\n            [version: number]: WebXRFeatureConstructor;\r\n        };\r\n    } = {};\r\n\r\n    private _features: {\r\n        [name: string]: {\r\n            featureImplementation: IWebXRFeature;\r\n            version: number;\r\n            enabled: boolean;\r\n            required: boolean;\r\n        };\r\n    } = {};\r\n\r\n    /**\r\n     * The key is the feature to check and the value is the feature that conflicts.\r\n     */\r\n    private static readonly _ConflictingFeatures: { [key: string]: string } = {\r\n        [WebXRFeatureName.TELEPORTATION]: WebXRFeatureName.MOVEMENT,\r\n        [WebXRFeatureName.MOVEMENT]: WebXRFeatureName.TELEPORTATION,\r\n    };\r\n\r\n    /**\r\n     * constructs a new features manages.\r\n     *\r\n     * @param _xrSessionManager an instance of WebXRSessionManager\r\n     */\r\n    constructor(private _xrSessionManager: WebXRSessionManager) {\r\n        // when session starts / initialized - attach\r\n        this._xrSessionManager.onXRSessionInit.add(() => {\r\n            this.getEnabledFeatures().forEach((featureName) => {\r\n                const feature = this._features[featureName];\r\n                if (feature.enabled && !feature.featureImplementation.attached && !feature.featureImplementation.disableAutoAttach) {\r\n                    this.attachFeature(featureName);\r\n                }\r\n            });\r\n        });\r\n\r\n        // when session ends - detach\r\n        this._xrSessionManager.onXRSessionEnded.add(() => {\r\n            this.getEnabledFeatures().forEach((featureName) => {\r\n                const feature = this._features[featureName];\r\n                if (feature.enabled && feature.featureImplementation.attached) {\r\n                    // detach, but don't disable!\r\n                    this.detachFeature(featureName);\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Used to register a module. After calling this function a developer can use this feature in the scene.\r\n     * Mainly used internally.\r\n     *\r\n     * @param featureName the name of the feature to register\r\n     * @param constructorFunction the function used to construct the module\r\n     * @param version the (babylon) version of the module\r\n     * @param stable is that a stable version of this module\r\n     */\r\n    public static AddWebXRFeature(featureName: string, constructorFunction: WebXRFeatureConstructor, version: number = 1, stable: boolean = false) {\r\n        this._AvailableFeatures[featureName] = this._AvailableFeatures[featureName] || { latest: version };\r\n        if (version > this._AvailableFeatures[featureName].latest) {\r\n            this._AvailableFeatures[featureName].latest = version;\r\n        }\r\n        if (stable) {\r\n            this._AvailableFeatures[featureName].stable = version;\r\n        }\r\n        this._AvailableFeatures[featureName][version] = constructorFunction;\r\n    }\r\n\r\n    /**\r\n     * Returns a constructor of a specific feature.\r\n     *\r\n     * @param featureName the name of the feature to construct\r\n     * @param version the version of the feature to load\r\n     * @param xrSessionManager the xrSessionManager. Used to construct the module\r\n     * @param options optional options provided to the module.\r\n     * @returns a function that, when called, will return a new instance of this feature\r\n     */\r\n    public static ConstructFeature(featureName: string, version: number = 1, xrSessionManager: WebXRSessionManager, options?: any): () => IWebXRFeature {\r\n        const constructorFunction = this._AvailableFeatures[featureName][version];\r\n        if (!constructorFunction) {\r\n            // throw an error? return nothing?\r\n            throw new Error(\"feature not found\");\r\n        }\r\n\r\n        return constructorFunction(xrSessionManager, options);\r\n    }\r\n\r\n    /**\r\n     * Can be used to return the list of features currently registered\r\n     *\r\n     * @returns an Array of available features\r\n     */\r\n    public static GetAvailableFeatures() {\r\n        return Object.keys(this._AvailableFeatures);\r\n    }\r\n\r\n    /**\r\n     * Gets the versions available for a specific feature\r\n     * @param featureName the name of the feature\r\n     * @returns an array with the available versions\r\n     */\r\n    public static GetAvailableVersions(featureName: string) {\r\n        return Object.keys(this._AvailableFeatures[featureName]);\r\n    }\r\n\r\n    /**\r\n     * Return the latest unstable version of this feature\r\n     * @param featureName the name of the feature to search\r\n     * @returns the version number. if not found will return -1\r\n     */\r\n    public static GetLatestVersionOfFeature(featureName: string): number {\r\n        return (this._AvailableFeatures[featureName] && this._AvailableFeatures[featureName].latest) || -1;\r\n    }\r\n\r\n    /**\r\n     * Return the latest stable version of this feature\r\n     * @param featureName the name of the feature to search\r\n     * @returns the version number. if not found will return -1\r\n     */\r\n    public static GetStableVersionOfFeature(featureName: string): number {\r\n        return (this._AvailableFeatures[featureName] && this._AvailableFeatures[featureName].stable) || -1;\r\n    }\r\n\r\n    /**\r\n     * Attach a feature to the current session. Mainly used when session started to start the feature effect.\r\n     * Can be used during a session to start a feature\r\n     * @param featureName the name of feature to attach\r\n     */\r\n    public attachFeature(featureName: string) {\r\n        const feature = this._features[featureName];\r\n        if (feature && feature.enabled && !feature.featureImplementation.attached) {\r\n            const attached = feature.featureImplementation.attach();\r\n            if (!attached) {\r\n                Tools.Warn(`Feature ${featureName} failed to attach`);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Can be used inside a session or when the session ends to detach a specific feature\r\n     * @param featureName the name of the feature to detach\r\n     */\r\n    public detachFeature(featureName: string) {\r\n        const feature = this._features[featureName];\r\n        if (feature && feature.featureImplementation.attached) {\r\n            const detached = feature.featureImplementation.detach();\r\n            if (!detached) {\r\n                Tools.Warn(`Feature ${featureName} failed to detach`);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Used to disable an already-enabled feature\r\n     * The feature will be disposed and will be recreated once enabled.\r\n     * @param featureName the feature to disable\r\n     * @returns true if disable was successful\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public disableFeature(featureName: string | { Name: string }): boolean {\r\n        const name = typeof featureName === \"string\" ? featureName : featureName.Name;\r\n        const feature = this._features[name];\r\n        if (feature && feature.enabled) {\r\n            feature.enabled = false;\r\n            this.detachFeature(name);\r\n            feature.featureImplementation.dispose();\r\n            delete this._features[name];\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * dispose this features manager\r\n     */\r\n    public dispose(): void {\r\n        this.getEnabledFeatures().forEach((feature) => {\r\n            this.disableFeature(feature);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Enable a feature using its name and a version. This will enable it in the scene, and will be responsible to attach it when the session starts.\r\n     * If used twice, the old version will be disposed and a new one will be constructed. This way you can re-enable with different configuration.\r\n     *\r\n     * @param featureName the name of the feature to load or the class of the feature\r\n     * @param version optional version to load. if not provided the latest version will be enabled\r\n     * @param moduleOptions options provided to the module. Ses the module documentation / constructor\r\n     * @param attachIfPossible if set to true (default) the feature will be automatically attached, if it is currently possible\r\n     * @param required is this feature required to the app. If set to true the session init will fail if the feature is not available.\r\n     * @returns a new constructed feature or throws an error if feature not found or conflicts with another enabled feature.\r\n     */\r\n    public enableFeature(\r\n        // eslint-disable-next-line @typescript-eslint/naming-convention\r\n        featureName: string | { Name: string },\r\n        version: number | string = \"latest\",\r\n        moduleOptions: any = {},\r\n        attachIfPossible: boolean = true,\r\n        required: boolean = true\r\n    ): IWebXRFeature {\r\n        const name = typeof featureName === \"string\" ? featureName : featureName.Name;\r\n        let versionToLoad = 0;\r\n        if (typeof version === \"string\") {\r\n            if (!version) {\r\n                throw new Error(`Error in provided version - ${name} (${version})`);\r\n            }\r\n            if (version === \"stable\") {\r\n                versionToLoad = WebXRFeaturesManager.GetStableVersionOfFeature(name);\r\n            } else if (version === \"latest\") {\r\n                versionToLoad = WebXRFeaturesManager.GetLatestVersionOfFeature(name);\r\n            } else {\r\n                // try loading the number the string represents\r\n                versionToLoad = +version;\r\n            }\r\n            if (versionToLoad === -1 || isNaN(versionToLoad)) {\r\n                throw new Error(`feature not found - ${name} (${version})`);\r\n            }\r\n        } else {\r\n            versionToLoad = version;\r\n        }\r\n\r\n        // check if there is a feature conflict\r\n        const conflictingFeature = WebXRFeaturesManager._ConflictingFeatures[name];\r\n        if (conflictingFeature !== undefined && this.getEnabledFeatures().indexOf(conflictingFeature) !== -1) {\r\n            throw new Error(`Feature ${name} cannot be enabled while ${conflictingFeature} is enabled.`);\r\n        }\r\n\r\n        // check if already initialized\r\n        const feature = this._features[name];\r\n        const constructFunction = WebXRFeaturesManager.ConstructFeature(name, versionToLoad, this._xrSessionManager, moduleOptions);\r\n        if (!constructFunction) {\r\n            // report error?\r\n            throw new Error(`feature not found - ${name}`);\r\n        }\r\n\r\n        /* If the feature is already enabled, detach and dispose it, and create a new one */\r\n        if (feature) {\r\n            this.disableFeature(name);\r\n        }\r\n\r\n        const constructed = constructFunction();\r\n        if (constructed.dependsOn) {\r\n            const dependentsFound = constructed.dependsOn.every((featureName) => !!this._features[featureName]);\r\n            if (!dependentsFound) {\r\n                throw new Error(`Dependant features missing. Make sure the following features are enabled - ${constructed.dependsOn.join(\", \")}`);\r\n            }\r\n        }\r\n        if (constructed.isCompatible()) {\r\n            this._features[name] = {\r\n                featureImplementation: constructed,\r\n                enabled: true,\r\n                version: versionToLoad,\r\n                required,\r\n            };\r\n\r\n            if (attachIfPossible) {\r\n                // if session started already, request and enable\r\n                if (this._xrSessionManager.session && !this._features[name].featureImplementation.attached) {\r\n                    // enable feature\r\n                    this.attachFeature(name);\r\n                }\r\n            } else {\r\n                // disable auto-attach when session starts\r\n                this._features[name].featureImplementation.disableAutoAttach = true;\r\n            }\r\n\r\n            return this._features[name].featureImplementation;\r\n        } else {\r\n            if (required) {\r\n                throw new Error(\"required feature not compatible\");\r\n            } else {\r\n                Tools.Warn(`Feature ${name} not compatible with the current environment/browser and was not enabled.`);\r\n                return constructed;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * get the implementation of an enabled feature.\r\n     * @param featureName the name of the feature to load\r\n     * @returns the feature class, if found\r\n     */\r\n    public getEnabledFeature(featureName: string): IWebXRFeature {\r\n        return this._features[featureName] && this._features[featureName].featureImplementation;\r\n    }\r\n\r\n    /**\r\n     * Get the list of enabled features\r\n     * @returns an array of enabled features\r\n     */\r\n    public getEnabledFeatures() {\r\n        return Object.keys(this._features);\r\n    }\r\n\r\n    /**\r\n     * This function will extend the session creation configuration object with enabled features.\r\n     * If, for example, the anchors feature is enabled, it will be automatically added to the optional or required features list,\r\n     * according to the defined \"required\" variable, provided during enableFeature call\r\n     * @param xrSessionInit the xr Session init object to extend\r\n     *\r\n     * @returns an extended XRSessionInit object\r\n     */\r\n    public async _extendXRSessionInitObject(xrSessionInit: XRSessionInit): Promise<XRSessionInit> {\r\n        const enabledFeatures = this.getEnabledFeatures();\r\n        for (const featureName of enabledFeatures) {\r\n            const feature = this._features[featureName];\r\n            const nativeName = feature.featureImplementation.xrNativeFeatureName;\r\n            if (nativeName) {\r\n                if (feature.required) {\r\n                    xrSessionInit.requiredFeatures = xrSessionInit.requiredFeatures || [];\r\n                    if (xrSessionInit.requiredFeatures.indexOf(nativeName) === -1) {\r\n                        xrSessionInit.requiredFeatures.push(nativeName);\r\n                    }\r\n                } else {\r\n                    xrSessionInit.optionalFeatures = xrSessionInit.optionalFeatures || [];\r\n                    if (xrSessionInit.optionalFeatures.indexOf(nativeName) === -1) {\r\n                        xrSessionInit.optionalFeatures.push(nativeName);\r\n                    }\r\n                }\r\n            }\r\n            if (feature.featureImplementation.getXRSessionInitExtension) {\r\n                const extended = await feature.featureImplementation.getXRSessionInitExtension();\r\n                xrSessionInit = {\r\n                    ...xrSessionInit,\r\n                    ...extended,\r\n                };\r\n            }\r\n        }\r\n        return xrSessionInit;\r\n    }\r\n}\r\n"]}