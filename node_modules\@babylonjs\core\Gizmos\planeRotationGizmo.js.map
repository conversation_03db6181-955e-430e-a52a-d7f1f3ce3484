{"version": 3, "file": "planeRotationGizmo.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Gizmos/planeRotationGizmo.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAC/E,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,iCAAiC,CAAC;AAEzC,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AAEtC,OAAO,EAAE,mBAAmB,EAAE,MAAM,yCAAyC,CAAC;AAE9E,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAEjE,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAC7D,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAgCxC;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,KAAK;IAiCzC,4EAA4E;IAC5E,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,+DAA+D;IAC/D,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,kFAAkF;IAClF,IAAW,aAAa,CAAC,KAAa;QAClC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED,sEAAsE;IACtE,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IA4DD;;;;;;;;;;;OAWG;IACH,YACI,WAAoB,EACpB,QAAgB,MAAM,CAAC,IAAI,EAAE,EAC7B,aAAmC,oBAAoB,CAAC,mBAAmB,EAC3E,YAAY,GAAG,EAAE,EACjB,SAAkC,IAAI;IACtC,6DAA6D;IAC7D,gBAAgB,GAAG,KAAK,EACxB,YAAoB,CAAC,EACrB,aAAqB,MAAM,CAAC,MAAM,EAAE,EACpC,eAAuB,MAAM,CAAC,IAAI,EAAE;QAEpC,KAAK,CAAC,UAAU,CAAC,CAAC;QAlIZ,qBAAgB,GAAoC,IAAI,CAAC;QAEnE;;WAEG;QACI,iBAAY,GAAG,CAAC,CAAC;QACxB;;;WAGG;QACI,qBAAgB,GAAG,IAAI,UAAU,EAA4B,CAAC;QAQrE;;WAEG;QACI,UAAK,GAAW,CAAC,CAAC;QAEzB;;WAEG;QACI,gBAAW,GAAG,CAAC,CAAC;QAsBb,eAAU,GAAY,IAAI,CAAC;QAC3B,YAAO,GAA4B,IAAI,CAAC;QAMxC,cAAS,GAAY,KAAK,CAAC;QAC3B,YAAO,GAAG,IAAI,OAAO,EAAE,CAAC;QA2E9B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,kBAAkB;QAClB,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAEhF,IAAI,CAAC,cAAc,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,UAAU,CAAC;QAC9C,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,UAAU,CAAC;QAE/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,YAAY,CAAC;QAClD,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,GAAG,CAAC;QAElC,0BAA0B;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC7D,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QAEnG,wBAAwB;QACxB,IAAI,CAAC,qBAAqB,GAAG,WAAW,CACpC,iBAAiB,EACjB;YACI,IAAI,EAAE,GAAG;YACT,SAAS,EAAE,KAAK;SACnB,EACD,IAAI,CAAC,UAAU,CAAC,iBAAiB,CACpC,CAAC;QACF,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QACtD,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QACpD,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAE7C,MAAM,CAAC,YAAY,CAAC,2BAA2B,CAAC,GAAG,kBAAkB,CAAC,0BAA0B,CAAC;QACjG,MAAM,CAAC,YAAY,CAAC,6BAA6B,CAAC,GAAG,kBAAkB,CAAC,4BAA4B,CAAC;QACrG,IAAI,CAAC,uBAAuB,GAAG,IAAI,cAAc,CAC7C,QAAQ,EACR,IAAI,CAAC,UAAU,CAAC,iBAAiB,EACjC;YACI,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,eAAe;SAC5B,EACD;YACI,UAAU,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC;YAC9B,QAAQ,EAAE,CAAC,qBAAqB,EAAE,QAAQ,EAAE,eAAe,CAAC;SAC/D,CACJ,CAAC;QACF,IAAI,CAAC,uBAAuB,CAAC,eAAe,GAAG,KAAK,CAAC;QACrD,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC;QAEhC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC;QACnE,IAAI,CAAC,qBAAqB,CAAC,UAAU,GAAG,KAAK,CAAC;QAE9C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;QAChE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5C,+DAA+D;QAC/D,IAAI,CAAC,YAAY,GAAG,IAAI,mBAAmB,CAAC,EAAE,eAAe,EAAE,WAAW,EAAE,CAAC,CAAC;QAC9E,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC;QACvC,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,kBAAkB,CAAC,YAAY,CAAC;QACjE,IAAI,CAAC,YAAY,CAAC,yCAAyC,GAAG,IAAI,CAAC;QACnE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE9C,0BAA0B;QAC1B,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAE,CAAC;QAEvC,MAAM,cAAc,GAAG,IAAI,MAAM,EAAE,CAAC;QACpC,MAAM,wBAAwB,GAAG,IAAI,OAAO,EAAE,CAAC;QAC/C,IAAI,6BAA6B,GAAG,IAAI,OAAO,EAAE,CAAC;QAElD,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YAC9C,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;gBAC5C,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAE5C,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;gBACxE,OAAO,CAAC,yBAAyB,CAAC,CAAC,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;gBAEtF,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;gBAC9E,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;gBACnB,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,sCAAsC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;gBAC5C,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;aAClB;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC3C,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC;QACzC,IAAI,uBAAuB,GAAG,CAAC,CAAC;QAChC,MAAM,SAAS,GAAG,IAAI,MAAM,EAAE,CAAC;QAC/B,MAAM,cAAc,GAAG,IAAI,UAAU,EAAE,CAAC;QACxC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,4IAA4I;gBAC5I,MAAM,SAAS,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvC,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAClD,MAAM,eAAe,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAE7C,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;gBACzF,kDAAkD;gBAClD,6CAA6C;gBAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC;gBAChK,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,sCAAsC,EAAE;oBAChE,MAAM,CAAC,IAAI,CACP,6JAA6J,CAChK,CAAC;oBACF,OAAO;iBACV;gBACD,cAAc,CAAC,SAAS,EAAE,CAAC;gBAE3B,MAAM,2BAA2B,GAAG,IAAI,CAAC,sCAAsC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;gBACpI,MAAM,SAAS,GAAG,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC,SAAS,EAAE,CAAC;gBACzF,MAAM,cAAc,GAAG,gBAAgB,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC,SAAS,EAAE,CAAC;gBAC1F,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;gBACvD,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;gBACnD,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;gBAC/D,wBAAwB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAC/C,6BAA6B,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBACpD,IAAI,IAAI,CAAC,sCAAsC,EAAE;oBAC7C,cAAc,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;oBAChD,6BAA6B,GAAG,OAAO,CAAC,oBAAoB,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC;iBAC1G;gBACD,0DAA0D;gBAC1D,IAAI,aAAa,GAAG,KAAK,CAAC;gBAC1B,IAAI,UAAU,CAAC,iBAAiB,CAAC,YAAY,EAAE;oBAC3C,MAAM,MAAM,GAAG,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC,SAAS,EAAE,CAAC;oBACpH,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,6BAA6B,CAAC,GAAG,CAAC,EAAE;wBACxD,wBAAwB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1C,6BAA6B,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/C,aAAa,GAAG,IAAI,CAAC;qBACxB;iBACJ;gBACD,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,KAAK,CAAC,GAAG,GAAG,CAAC;gBAC/E,IAAI,cAAc,EAAE;oBAChB,KAAK,GAAG,CAAC,KAAK,CAAC;iBAClB;gBAED,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;oBACxD,KAAK,GAAG,CAAC,CAAC;iBACb;gBAED,iBAAiB;gBACjB,IAAI,OAAO,GAAG,KAAK,CAAC;gBACpB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,EAAE;oBACxB,uBAAuB,IAAI,KAAK,CAAC;oBACjC,IAAI,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE;wBACvD,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;wBAClF,IAAI,uBAAuB,GAAG,CAAC,EAAE;4BAC7B,SAAS,IAAI,CAAC,CAAC,CAAC;yBACnB;wBACD,uBAAuB,GAAG,uBAAuB,GAAG,IAAI,CAAC,YAAY,CAAC;wBACtE,KAAK,GAAG,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;wBACtC,OAAO,GAAG,IAAI,CAAC;qBAClB;yBAAM;wBACH,KAAK,GAAG,CAAC,CAAC;qBACb;iBACJ;gBAED,wIAAwI;gBACxI,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAClD,cAAc,CAAC,GAAG,CACd,wBAAwB,CAAC,CAAC,GAAG,qBAAqB,EAClD,wBAAwB,CAAC,CAAC,GAAG,qBAAqB,EAClD,wBAAwB,CAAC,CAAC,GAAG,qBAAqB,EAClD,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CACtB,CAAC;gBAEF,yIAAyI;gBACzI,IAAI,SAAS,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;oBAC7B,MAAM,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;oBAChC,cAAc,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;oBAC7C,UAAU,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;iBACjG;gBAED,IAAI,IAAI,CAAC,sCAAsC,EAAE;oBAC7C,kDAAkD;oBAClD,cAAc,CAAC,aAAa,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;oBAC7D,cAAc,CAAC,SAAS,EAAE,CAAC;oBAC3B,mBAAmB;oBACnB,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,cAAc,EAAE,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,CAAC;iBACvG;qBAAM;oBACH,oDAAoD;oBACpD,cAAc,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,cAAc,EAAE,CAAC;oBACxE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,CAAC;oBAC3G,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;iBAClE;gBAED,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;gBAChD,IAAI,OAAO,EAAE;oBACT,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC;oBAClC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;iBACvD;gBACD,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,CAAC;gBACxB,IAAI,CAAC,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;gBAC7C,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChE,IAAI,CAAC,cAAc,EAAE,CAAC;aACzB;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC;QAChD,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;QAEjG,MAAM,KAAK,GAAmB;YAC1B,cAAc,EAAE,CAAC,QAAQ,CAAC;YAC1B,WAAW,EAAE,CAAC,YAAY,CAAC;YAC3B,QAAQ,EAAE,IAAI,CAAC,gBAAgB;YAC/B,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,MAAM,EAAE,KAAK;YACb,YAAY,EAAE,IAAI,CAAC,YAAY;SAClC,CAAC;QACF,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAErD,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACzF,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,OAAO;aACV;YACD,kIAAkI;YAClI,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,kBAAkB,CAAC,YAAY,CAAC;YACjE,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAO,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,MAAM,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;gBACxJ,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;aAC3D;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YACnD,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5G,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACO,gBAAgB,CAAC,UAAwB,EAAE,SAAiB,EAAE,YAAoB;QACxF,MAAM,QAAQ,GAAG,WAAW,CACxB,QAAQ,EACR;YACI,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE,IAAI,GAAG,SAAS;YAC3B,YAAY;SACf,EACD,IAAI,CAAC,UAAU,CAAC,iBAAiB,CACpC,CAAC;QACF,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;QACxB,MAAM,YAAY,GAAG,WAAW,CAC5B,EAAE,EACF;YACI,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE,KAAK,GAAG,SAAS;YAC5B,YAAY;SACf,EACD,IAAI,CAAC,UAAU,CAAC,iBAAiB,CACpC,CAAC;QACF,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE9C,2CAA2C;QAC3C,YAAY,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACtC,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAElC,UAAU,CAAC,QAAQ,CAAC,YAAY,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;QACzD,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;QACrD,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC;IACtC,CAAC;IAES,oBAAoB,CAAC,KAAqB;QAChD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;SACpD;IACL,CAAC;IAED;;OAEG;IACH,IAAW,SAAS,CAAC,KAAc;QAC/B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,KAAK,EAAE;YACR,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;aAAM;YACH,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;aACjD;SACJ;IACL,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpF,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QAC3B,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;SAC7B;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;SACxC;QACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;SAC1C;QACD,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACjF,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,OAAO,EAAE,CAAC;aAClB;QACL,CAAC,CAAC,CAAC;QACH,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;;AAzbD;;;GAGG;AACW,+BAAY,GAAW,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,AAA7B,CAA8B;AA0CvC,6CAA0B,GAAG;;;;;;;;;;;UAWxC,AAXqC,CAWpC;AAEU,+CAA4B,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+B/C,AA/B4C,CA+B3C", "sourcesContent": ["import type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport { Quaternion, Matrix, Vector3, TmpVectors } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport \"../Meshes/Builders/linesBuilder\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport type { Node } from \"../node\";\r\nimport { PointerDragBehavior } from \"../Behaviors/Meshes/pointerDragBehavior\";\r\nimport type { GizmoAxisCache, IGizmo } from \"./gizmo\";\r\nimport { Gizmo } from \"./gizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport type { RotationGizmo } from \"./rotationGizmo\";\r\nimport { ShaderMaterial } from \"../Materials/shaderMaterial\";\r\nimport { Effect } from \"../Materials/effect\";\r\nimport { CreatePlane } from \"../Meshes/Builders/planeBuilder\";\r\nimport { CreateTorus } from \"../Meshes/Builders/torusBuilder\";\r\nimport { Epsilon } from \"../Maths/math.constants\";\r\nimport { Logger } from \"../Misc/logger\";\r\n\r\n/**\r\n * Interface for plane rotation gizmo\r\n */\r\nexport interface IPlaneRotationGizmo extends IGizmo {\r\n    /** Drag behavior responsible for the gizmos dragging interactions */\r\n    dragBehavior: PointerDragBehavior;\r\n    /** Drag distance in babylon units that the gizmo will snap to when dragged */\r\n    snapDistance: number;\r\n    /** Sensitivity factor for dragging */\r\n    sensitivity: number;\r\n    /**\r\n     * Event that fires each time the gizmo snaps to a new location.\r\n     * * snapDistance is the change in distance\r\n     */\r\n    onSnapObservable: Observable<{ snapDistance: number }>;\r\n    /** Accumulated relative angle value for rotation on the axis. */\r\n    angle: number;\r\n    /** If the gizmo is enabled */\r\n    isEnabled: boolean;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    coloredMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is hovered with mouse */\r\n    hoverMaterial: StandardMaterial;\r\n    /** Color used to render the drag angle sector when gizmo is rotated with mouse */\r\n    rotationColor: Color3;\r\n    /** Material used to render when gizmo is disabled. typically grey. */\r\n    disableMaterial: StandardMaterial;\r\n}\r\n\r\n/**\r\n * Single plane rotation gizmo\r\n */\r\nexport class PlaneRotationGizmo extends Gizmo implements IPlaneRotationGizmo {\r\n    /**\r\n     * Drag behavior responsible for the gizmos dragging interactions\r\n     */\r\n    public dragBehavior: PointerDragBehavior;\r\n    protected _pointerObserver: Nullable<Observer<PointerInfo>> = null;\r\n\r\n    /**\r\n     * Rotation distance in radians that the gizmo will snap to (Default: 0)\r\n     */\r\n    public snapDistance = 0;\r\n    /**\r\n     * Event that fires each time the gizmo snaps to a new location.\r\n     * * snapDistance is the change in distance\r\n     */\r\n    public onSnapObservable = new Observable<{ snapDistance: number }>();\r\n\r\n    /**\r\n     * The maximum angle between the camera and the rotation allowed for interaction\r\n     * If a rotation plane appears 'flat', a lower value allows interaction.\r\n     */\r\n    public static MaxDragAngle: number = (Math.PI * 9) / 20;\r\n\r\n    /**\r\n     * Accumulated relative angle value for rotation on the axis. Reset to 0 when a dragStart occurs\r\n     */\r\n    public angle: number = 0;\r\n\r\n    /**\r\n     * Custom sensitivity value for the drag strength\r\n     */\r\n    public sensitivity = 1;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    public get coloredMaterial() {\r\n        return this._coloredMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is hovered with mouse */\r\n    public get hoverMaterial() {\r\n        return this._hoverMaterial;\r\n    }\r\n\r\n    /** Color used to render the drag angle sector when gizmo is rotated with mouse */\r\n    public set rotationColor(color: Color3) {\r\n        this._rotationShaderMaterial.setColor3(\"rotationColor\", color);\r\n    }\r\n\r\n    /** Material used to render when gizmo is disabled. typically grey. */\r\n    public get disableMaterial() {\r\n        return this._disableMaterial;\r\n    }\r\n\r\n    protected _isEnabled: boolean = true;\r\n    protected _parent: Nullable<RotationGizmo> = null;\r\n    protected _coloredMaterial: StandardMaterial;\r\n    protected _hoverMaterial: StandardMaterial;\r\n    protected _disableMaterial: StandardMaterial;\r\n    protected _gizmoMesh: Mesh;\r\n    protected _rotationDisplayPlane: Mesh;\r\n    protected _dragging: boolean = false;\r\n    protected _angles = new Vector3();\r\n\r\n    protected static _RotationGizmoVertexShader = `\r\n        precision highp float;\r\n        attribute vec3 position;\r\n        attribute vec2 uv;\r\n        uniform mat4 worldViewProjection;\r\n        varying vec3 vPosition;\r\n        varying vec2 vUV;\r\n\r\n        void main(void) {\r\n            gl_Position = worldViewProjection * vec4(position, 1.0);\r\n            vUV = uv;\r\n        }`;\r\n\r\n    protected static _RotationGizmoFragmentShader = `\r\n        precision highp float;\r\n        varying vec2 vUV;\r\n        varying vec3 vPosition;\r\n        uniform vec3 angles;\r\n        uniform vec3 rotationColor;\r\n\r\n        #define twopi 6.283185307\r\n\r\n        void main(void) {\r\n            vec2 uv = vUV - vec2(0.5);\r\n            float angle = atan(uv.y, uv.x) + 3.141592;\r\n            float delta = gl_FrontFacing ? angles.y : -angles.y;\r\n            float begin = angles.x - delta * angles.z;\r\n            float start = (begin < (begin + delta)) ? begin : (begin + delta);\r\n            float end = (begin > (begin + delta)) ? begin : (begin + delta);\r\n            float len = sqrt(dot(uv,uv));\r\n            float opacity = 1. - step(0.5, len);\r\n\r\n            float base = abs(floor(start / twopi)) * twopi;\r\n            start += base;\r\n            end += base;\r\n\r\n            float intensity = 0.;\r\n            for (int i = 0; i < 5; i++)\r\n            {\r\n                intensity += max(step(start, angle) - step(end, angle), 0.);\r\n                angle += twopi;\r\n            }\r\n            gl_FragColor = vec4(rotationColor, min(intensity * 0.25, 0.8)) * opacity;\r\n        }\r\n    `;\r\n\r\n    protected _rotationShaderMaterial: ShaderMaterial;\r\n\r\n    /**\r\n     * Creates a PlaneRotationGizmo\r\n     * @param planeNormal The normal of the plane which the gizmo will be able to rotate on\r\n     * @param color The color of the gizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param tessellation Amount of tessellation to be used when creating rotation circles\r\n     * @param parent\r\n     * @param useEulerRotation Use and update Euler angle instead of quaternion\r\n     * @param thickness display gizmo axis thickness\r\n     * @param hoverColor The color of the gizmo when hovering over and dragging\r\n     * @param disableColor The Color of the gizmo when its disabled\r\n     */\r\n    constructor(\r\n        planeNormal: Vector3,\r\n        color: Color3 = Color3.Gray(),\r\n        gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer,\r\n        tessellation = 32,\r\n        parent: Nullable<RotationGizmo> = null,\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        useEulerRotation = false,\r\n        thickness: number = 1,\r\n        hoverColor: Color3 = Color3.Yellow(),\r\n        disableColor: Color3 = Color3.Gray()\r\n    ) {\r\n        super(gizmoLayer);\r\n        this._parent = parent;\r\n        // Create Material\r\n        this._coloredMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._coloredMaterial.diffuseColor = color;\r\n        this._coloredMaterial.specularColor = color.subtract(new Color3(0.1, 0.1, 0.1));\r\n\r\n        this._hoverMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._hoverMaterial.diffuseColor = hoverColor;\r\n        this._hoverMaterial.specularColor = hoverColor;\r\n\r\n        this._disableMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._disableMaterial.diffuseColor = disableColor;\r\n        this._disableMaterial.alpha = 0.4;\r\n\r\n        // Build mesh on root node\r\n        this._gizmoMesh = new Mesh(\"\", gizmoLayer.utilityLayerScene);\r\n        const { rotationMesh, collider } = this._createGizmoMesh(this._gizmoMesh, thickness, tessellation);\r\n\r\n        // Setup Rotation Circle\r\n        this._rotationDisplayPlane = CreatePlane(\r\n            \"rotationDisplay\",\r\n            {\r\n                size: 0.6,\r\n                updatable: false,\r\n            },\r\n            this.gizmoLayer.utilityLayerScene\r\n        );\r\n        this._rotationDisplayPlane.rotation.z = Math.PI * 0.5;\r\n        this._rotationDisplayPlane.parent = this._gizmoMesh;\r\n        this._rotationDisplayPlane.setEnabled(false);\r\n\r\n        Effect.ShadersStore[\"rotationGizmoVertexShader\"] = PlaneRotationGizmo._RotationGizmoVertexShader;\r\n        Effect.ShadersStore[\"rotationGizmoFragmentShader\"] = PlaneRotationGizmo._RotationGizmoFragmentShader;\r\n        this._rotationShaderMaterial = new ShaderMaterial(\r\n            \"shader\",\r\n            this.gizmoLayer.utilityLayerScene,\r\n            {\r\n                vertex: \"rotationGizmo\",\r\n                fragment: \"rotationGizmo\",\r\n            },\r\n            {\r\n                attributes: [\"position\", \"uv\"],\r\n                uniforms: [\"worldViewProjection\", \"angles\", \"rotationColor\"],\r\n            }\r\n        );\r\n        this._rotationShaderMaterial.backFaceCulling = false;\r\n        this.rotationColor = hoverColor;\r\n\r\n        this._rotationDisplayPlane.material = this._rotationShaderMaterial;\r\n        this._rotationDisplayPlane.visibility = 0.999;\r\n\r\n        this._gizmoMesh.lookAt(this._rootMesh.position.add(planeNormal));\r\n        this._rootMesh.addChild(this._gizmoMesh, Gizmo.PreserveScaling);\r\n        this._gizmoMesh.scaling.scaleInPlace(1 / 3);\r\n        // Add drag behavior to handle events when the gizmo is dragged\r\n        this.dragBehavior = new PointerDragBehavior({ dragPlaneNormal: planeNormal });\r\n        this.dragBehavior.moveAttached = false;\r\n        this.dragBehavior.maxDragAngle = PlaneRotationGizmo.MaxDragAngle;\r\n        this.dragBehavior._useAlternatePickedPointAboveMaxDragAngle = true;\r\n        this._rootMesh.addBehavior(this.dragBehavior);\r\n\r\n        // Closures for drag logic\r\n        const lastDragPosition = new Vector3();\r\n\r\n        const rotationMatrix = new Matrix();\r\n        const planeNormalTowardsCamera = new Vector3();\r\n        let localPlaneNormalTowardsCamera = new Vector3();\r\n\r\n        this.dragBehavior.onDragStartObservable.add((e) => {\r\n            if (this.attachedNode) {\r\n                lastDragPosition.copyFrom(e.dragPlanePoint);\r\n                this._rotationDisplayPlane.setEnabled(true);\r\n\r\n                this._rotationDisplayPlane.getWorldMatrix().invertToRef(rotationMatrix);\r\n                Vector3.TransformCoordinatesToRef(e.dragPlanePoint, rotationMatrix, lastDragPosition);\r\n\r\n                this._angles.x = Math.atan2(lastDragPosition.y, lastDragPosition.x) + Math.PI;\r\n                this._angles.y = 0;\r\n                this._angles.z = this.updateGizmoRotationToMatchAttachedMesh ? 1 : 0;\r\n                this._dragging = true;\r\n                lastDragPosition.copyFrom(e.dragPlanePoint);\r\n                this._rotationShaderMaterial.setVector3(\"angles\", this._angles);\r\n                this.angle = 0;\r\n            }\r\n        });\r\n\r\n        this.dragBehavior.onDragEndObservable.add(() => {\r\n            this._dragging = false;\r\n            this._rotationDisplayPlane.setEnabled(false);\r\n        });\r\n\r\n        const tmpSnapEvent = { snapDistance: 0 };\r\n        let currentSnapDragDistance = 0;\r\n        const tmpMatrix = new Matrix();\r\n        const amountToRotate = new Quaternion();\r\n        this.dragBehavior.onDragObservable.add((event) => {\r\n            if (this.attachedNode) {\r\n                // Calc angle over full 360 degree (https://stackoverflow.com/questions/43493711/the-angle-between-two-3d-vectors-with-a-result-range-0-360)\r\n                const nodeScale = new Vector3(1, 1, 1);\r\n                const nodeQuaternion = new Quaternion(0, 0, 0, 1);\r\n                const nodeTranslation = new Vector3(0, 0, 0);\r\n\r\n                this.attachedNode.getWorldMatrix().decompose(nodeScale, nodeQuaternion, nodeTranslation);\r\n                // uniform scaling of absolute value of components\r\n                // (-1,1,1) is uniform but (1,1.001,1) is not\r\n                const uniformScaling = Math.abs(Math.abs(nodeScale.x) - Math.abs(nodeScale.y)) <= Epsilon && Math.abs(Math.abs(nodeScale.x) - Math.abs(nodeScale.z)) <= Epsilon;\r\n                if (!uniformScaling && this.updateGizmoRotationToMatchAttachedMesh) {\r\n                    Logger.Warn(\r\n                        \"Unable to use a rotation gizmo matching mesh rotation with non uniform scaling. Use uniform scaling or set updateGizmoRotationToMatchAttachedMesh to false.\"\r\n                    );\r\n                    return;\r\n                }\r\n                nodeQuaternion.normalize();\r\n\r\n                const nodeTranslationForOperation = this.updateGizmoPositionToMatchAttachedMesh ? nodeTranslation : this._rootMesh.absolutePosition;\r\n                const newVector = event.dragPlanePoint.subtract(nodeTranslationForOperation).normalize();\r\n                const originalVector = lastDragPosition.subtract(nodeTranslationForOperation).normalize();\r\n                const cross = Vector3.Cross(newVector, originalVector);\r\n                const dot = Vector3.Dot(newVector, originalVector);\r\n                let angle = Math.atan2(cross.length(), dot) * this.sensitivity;\r\n                planeNormalTowardsCamera.copyFrom(planeNormal);\r\n                localPlaneNormalTowardsCamera.copyFrom(planeNormal);\r\n                if (this.updateGizmoRotationToMatchAttachedMesh) {\r\n                    nodeQuaternion.toRotationMatrix(rotationMatrix);\r\n                    localPlaneNormalTowardsCamera = Vector3.TransformCoordinates(planeNormalTowardsCamera, rotationMatrix);\r\n                }\r\n                // Flip up vector depending on which side the camera is on\r\n                let cameraFlipped = false;\r\n                if (gizmoLayer.utilityLayerScene.activeCamera) {\r\n                    const camVec = gizmoLayer.utilityLayerScene.activeCamera.position.subtract(nodeTranslationForOperation).normalize();\r\n                    if (Vector3.Dot(camVec, localPlaneNormalTowardsCamera) > 0) {\r\n                        planeNormalTowardsCamera.scaleInPlace(-1);\r\n                        localPlaneNormalTowardsCamera.scaleInPlace(-1);\r\n                        cameraFlipped = true;\r\n                    }\r\n                }\r\n                const halfCircleSide = Vector3.Dot(localPlaneNormalTowardsCamera, cross) > 0.0;\r\n                if (halfCircleSide) {\r\n                    angle = -angle;\r\n                }\r\n\r\n                TmpVectors.Vector3[0].set(angle, 0, 0);\r\n                if (!this.dragBehavior.validateDrag(TmpVectors.Vector3[0])) {\r\n                    angle = 0;\r\n                }\r\n\r\n                // Snapping logic\r\n                let snapped = false;\r\n                if (this.snapDistance != 0) {\r\n                    currentSnapDragDistance += angle;\r\n                    if (Math.abs(currentSnapDragDistance) > this.snapDistance) {\r\n                        let dragSteps = Math.floor(Math.abs(currentSnapDragDistance) / this.snapDistance);\r\n                        if (currentSnapDragDistance < 0) {\r\n                            dragSteps *= -1;\r\n                        }\r\n                        currentSnapDragDistance = currentSnapDragDistance % this.snapDistance;\r\n                        angle = this.snapDistance * dragSteps;\r\n                        snapped = true;\r\n                    } else {\r\n                        angle = 0;\r\n                    }\r\n                }\r\n\r\n                // Convert angle and axis to quaternion (http://www.euclideanspace.com/maths/geometry/rotations/conversions/angleToQuaternion/index.htm)\r\n                const quaternionCoefficient = Math.sin(angle / 2);\r\n                amountToRotate.set(\r\n                    planeNormalTowardsCamera.x * quaternionCoefficient,\r\n                    planeNormalTowardsCamera.y * quaternionCoefficient,\r\n                    planeNormalTowardsCamera.z * quaternionCoefficient,\r\n                    Math.cos(angle / 2)\r\n                );\r\n\r\n                // If the meshes local scale is inverted (eg. loaded gltf file parent with z scale of -1) the rotation needs to be inverted on the y axis\r\n                if (tmpMatrix.determinant() > 0) {\r\n                    const tmpVector = new Vector3();\r\n                    amountToRotate.toEulerAnglesToRef(tmpVector);\r\n                    Quaternion.RotationYawPitchRollToRef(tmpVector.y, -tmpVector.x, -tmpVector.z, amountToRotate);\r\n                }\r\n\r\n                if (this.updateGizmoRotationToMatchAttachedMesh) {\r\n                    // Rotate selected mesh quaternion over fixed axis\r\n                    nodeQuaternion.multiplyToRef(amountToRotate, nodeQuaternion);\r\n                    nodeQuaternion.normalize();\r\n                    // recompose matrix\r\n                    Matrix.ComposeToRef(nodeScale, nodeQuaternion, nodeTranslation, this.attachedNode.getWorldMatrix());\r\n                } else {\r\n                    // Rotate selected mesh quaternion over rotated axis\r\n                    amountToRotate.toRotationMatrix(TmpVectors.Matrix[0]);\r\n                    const translation = this.attachedNode.getWorldMatrix().getTranslation();\r\n                    this.attachedNode.getWorldMatrix().multiplyToRef(TmpVectors.Matrix[0], this.attachedNode.getWorldMatrix());\r\n                    this.attachedNode.getWorldMatrix().setTranslation(translation);\r\n                }\r\n\r\n                lastDragPosition.copyFrom(event.dragPlanePoint);\r\n                if (snapped) {\r\n                    tmpSnapEvent.snapDistance = angle;\r\n                    this.onSnapObservable.notifyObservers(tmpSnapEvent);\r\n                }\r\n                this._angles.y += angle;\r\n                this.angle += cameraFlipped ? -angle : angle;\r\n                this._rotationShaderMaterial.setVector3(\"angles\", this._angles);\r\n                this._matrixChanged();\r\n            }\r\n        });\r\n\r\n        const light = gizmoLayer._getSharedGizmoLight();\r\n        light.includedOnlyMeshes = light.includedOnlyMeshes.concat(this._rootMesh.getChildMeshes(false));\r\n\r\n        const cache: GizmoAxisCache = {\r\n            colliderMeshes: [collider],\r\n            gizmoMeshes: [rotationMesh],\r\n            material: this._coloredMaterial,\r\n            hoverMaterial: this._hoverMaterial,\r\n            disableMaterial: this._disableMaterial,\r\n            active: false,\r\n            dragBehavior: this.dragBehavior,\r\n        };\r\n        this._parent?.addToAxisCache(this._gizmoMesh, cache);\r\n\r\n        this._pointerObserver = gizmoLayer.utilityLayerScene.onPointerObservable.add((pointerInfo) => {\r\n            if (this._customMeshSet) {\r\n                return;\r\n            }\r\n            // updating here the maxangle because ondragstart is too late (value already used) and the updated value is not taken into account\r\n            this.dragBehavior.maxDragAngle = PlaneRotationGizmo.MaxDragAngle;\r\n            this._isHovered = !!(cache.colliderMeshes.indexOf(<Mesh>pointerInfo?.pickInfo?.pickedMesh) != -1);\r\n            if (!this._parent) {\r\n                const material = cache.dragBehavior.enabled ? (this._isHovered || this._dragging ? this._hoverMaterial : this._coloredMaterial) : this._disableMaterial;\r\n                this._setGizmoMeshMaterial(cache.gizmoMeshes, material);\r\n            }\r\n        });\r\n\r\n        this.dragBehavior.onEnabledObservable.add((newState) => {\r\n            this._setGizmoMeshMaterial(cache.gizmoMeshes, newState ? this._coloredMaterial : this._disableMaterial);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Create Geometry for Gizmo\r\n     * @param parentMesh\r\n     * @param thickness\r\n     * @param tessellation\r\n     * @returns\r\n     */\r\n    protected _createGizmoMesh(parentMesh: AbstractMesh, thickness: number, tessellation: number) {\r\n        const collider = CreateTorus(\r\n            \"ignore\",\r\n            {\r\n                diameter: 0.6,\r\n                thickness: 0.03 * thickness,\r\n                tessellation,\r\n            },\r\n            this.gizmoLayer.utilityLayerScene\r\n        );\r\n        collider.visibility = 0;\r\n        const rotationMesh = CreateTorus(\r\n            \"\",\r\n            {\r\n                diameter: 0.6,\r\n                thickness: 0.005 * thickness,\r\n                tessellation,\r\n            },\r\n            this.gizmoLayer.utilityLayerScene\r\n        );\r\n        rotationMesh.material = this._coloredMaterial;\r\n\r\n        // Position arrow pointing in its drag axis\r\n        rotationMesh.rotation.x = Math.PI / 2;\r\n        collider.rotation.x = Math.PI / 2;\r\n\r\n        parentMesh.addChild(rotationMesh, Gizmo.PreserveScaling);\r\n        parentMesh.addChild(collider, Gizmo.PreserveScaling);\r\n        return { rotationMesh, collider };\r\n    }\r\n\r\n    protected _attachedNodeChanged(value: Nullable<Node>) {\r\n        if (this.dragBehavior) {\r\n            this.dragBehavior.enabled = value ? true : false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If the gizmo is enabled\r\n     */\r\n    public set isEnabled(value: boolean) {\r\n        this._isEnabled = value;\r\n        if (!value) {\r\n            this.attachedMesh = null;\r\n        } else {\r\n            if (this._parent) {\r\n                this.attachedMesh = this._parent.attachedMesh;\r\n            }\r\n        }\r\n    }\r\n\r\n    public get isEnabled(): boolean {\r\n        return this._isEnabled;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public dispose() {\r\n        this.onSnapObservable.clear();\r\n        this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(this._pointerObserver);\r\n        this.dragBehavior.detach();\r\n        if (this._gizmoMesh) {\r\n            this._gizmoMesh.dispose();\r\n        }\r\n        if (this._rotationDisplayPlane) {\r\n            this._rotationDisplayPlane.dispose();\r\n        }\r\n        if (this._rotationShaderMaterial) {\r\n            this._rotationShaderMaterial.dispose();\r\n        }\r\n        [this._coloredMaterial, this._hoverMaterial, this._disableMaterial].forEach((matl) => {\r\n            if (matl) {\r\n                matl.dispose();\r\n            }\r\n        });\r\n        super.dispose();\r\n    }\r\n}\r\n"]}