{"version": 3, "file": "performanceViewerSceneExtension.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Misc/PerformanceViewer/performanceViewerSceneExtension.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AACpC,OAAO,EAAE,0BAA0B,EAAE,MAAM,8BAA8B,CAAC;AAE1E,KAAK,CAAC,SAAS,CAAC,gBAAgB,GAAG;IAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,0BAA0B,CAAC,IAAI,CAAC,CAAC;KAC9D;IAED,OAAO,IAAI,CAAC,cAAc,CAAC;AAC/B,CAAC,CAAC", "sourcesContent": ["import { Scene } from \"../../scene\";\r\nimport { PerformanceViewerCollector } from \"./performanceViewerCollector\";\r\n\r\nScene.prototype.getPerfCollector = function (this: Scene): PerformanceViewerCollector {\r\n    if (!this._perfCollector) {\r\n        this._perfCollector = new PerformanceViewerCollector(this);\r\n    }\r\n\r\n    return this._perfCollector;\r\n};\r\n"]}