{"version": 3, "file": "sixDofDragBehavior.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Behaviors/Meshes/sixDofDragBehavior.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAElF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,KAAK,EAAE,MAAM,uBAAuB,CAAC;AAE9C;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,sBAAsB;IAA9D;;QACY,yBAAoB,GAA8B,IAAI,CAAC;QAGrD,oBAAe,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,uBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;QACtC,mBAAc,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACtC,sBAAiB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,yBAAoB,GAAG,IAAI,UAAU,EAAE,CAAC;QACxC,qBAAgB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAElD;;WAEG;QACI,gCAA2B,GAAG,IAAI,UAAU,EAAyB,CAAC;QAE7E;;WAEG;QACI,mBAAc,GAAG,GAAG,CAAC;QAE5B;;WAEG;QACI,wBAAmB,GAAG,IAAI,CAAC;QAElC;;WAEG;QACI,sBAAiB,GAAG,KAAK,CAAC;QAEjC;;WAEG;QACI,+BAA0B,GAAG,IAAI,CAAC;QASzC;;WAEG;QACI,oBAAe,GAAY,KAAK,CAAC;QAExC;;WAEG;QACI,0BAAqB,GAAG,KAAK,CAAC;IA6MzC,CAAC;IA5NG;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,YAAY,CAAC;IACxB,CAAC;IAYD;;;;OAIG;IACI,MAAM,CAAC,SAAe;QACzB,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAExB,SAAS,CAAC,eAAe,GAAG,IAAI,CAAC;QACjC,uDAAuD;QACvD,SAAS,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACrC,CAAC,CAAC,eAAe,GAAG,IAAI,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,4CAA4C;QAC5C,IAAI,CAAC,qBAAqB,GAAG,IAAI,aAAa,CAAC,gBAAgB,EAAE,sBAAsB,CAAC,aAAa,CAAC,CAAC;QACvG,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QAEtE,sFAAsF;QACtF,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/E,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACtF,4BAA4B;gBAC5B,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACzC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACxH,MAAM,qBAAqB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACpD,qBAAqB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAC3C,0FAA0F;gBAC1F,0BAA0B;gBAC1B,IAAI,SAAS,CAAC,MAAM,EAAE;oBAClB,MAAM,2BAA2B,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACxD,SAAS,CAAC,MAAwB,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;oBAC7G,2BAA2B,CAAC,MAAM,EAAE,CAAC;oBACrC,OAAO,CAAC,oBAAoB,CAAC,UAAU,EAAE,2BAA2B,EAAE,qBAAqB,CAAC,CAAC;iBAChG;gBACD,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;gBAErD,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC;gBAE3F,0DAA0D;gBAC1D,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAE,SAAS,CAAC,MAAwB,CAAC,OAAO,IAAI,CAAE,SAAS,CAAC,MAAwB,CAAC,OAAO,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,EAAE;oBACrJ,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBACjD,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBAClD,IAAI,SAAS,CAAC,MAAM,EAAE;wBAClB,MAAM,qBAAqB,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;wBACvD,qBAAqB,CAAC,QAAQ,CAAE,SAAS,CAAC,MAAwB,CAAC,0BAA0B,CAAC,CAAC;wBAC/F,qBAAqB,CAAC,aAAa,EAAE,CAAC;wBACtC,qBAAqB,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;qBACjF;oBACD,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,kBAAmB,EAAE,eAAe,EAAE,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,kBAAmB,CAAC,CAAC;iBAC7H;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,wBAAwB,CAAC,yBAAkC,EAAE,OAAe,EAAE,QAAoB;QACtG,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;QACpD,MAAM,oBAAoB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;QACxD,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;QACjD,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;QAC9C,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB;QAE3D,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC,EAAE,yBAAyB,CAAC,CAAC,EAAE,yBAAyB,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI;QACvI,MAAM,CAAC,gBAAgB,CAAC,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,yBAAyB,CAAC,CAAC,EAAE,oBAAoB,CAAC,CAAC,CAAC,KAAK;QAC9I,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI;QAC1D,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QAC5D,oBAAoB,CAAC,aAAa,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS;QAC1E,WAAW,CAAC,aAAa,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,aAAa;QAClE,WAAW,CAAC,aAAa,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC,CAAC,iBAAiB;QAE5E,OAAO,WAAW,CAAC,cAAc,EAAE,CAAC;IACxC,CAAC;IAEO,0BAA0B,CAAC,kBAA2B,EAAE,kBAA8B;QAC1F,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC3C,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAEvB,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YACxC,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBACxB,qDAAqD;oBACrD,UAAU,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC9G;qBAAM;oBACH,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;iBACzD;gBACD,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC9F;SACJ;aAAM,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC,oBAAoB,IAAI,IAAI,CAAC,0BAA0B,CAAC,EAAE;YACnJ,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACxF;QAED,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;IACzF,CAAC;IAEO,2BAA2B;QAC/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;QACtG,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;QACtG,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7C,iBAAiB,CAAC,QAAQ,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;QAC9D,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7C,iBAAiB,CAAC,aAAa,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;QAEnE,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QAC9G,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QAC9G,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5C,gBAAgB,CAAC,QAAQ,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QAC3D,aAAa,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAChC,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5C,gBAAgB,CAAC,aAAa,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QAEhE,MAAM,OAAO,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC;QACjE,MAAM,WAAW,GAAG,aAAa,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QAC3D,MAAM,kBAAkB,GAAG,UAAU,CAAC,eAAe,CACjD,CAAC,EACD,OAAO,CAAC,6BAA6B,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,aAAa,CAAC,SAAS,EAAE,EAAE,OAAO,CAAC,UAAU,CAAC,EAChH,CAAC,CACJ,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAEhC,MAAM,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;QAC/J,IAAI,CAAC,qBAAqB,CAAC,kBAAmB,CAAC,aAAa,CAAC,kBAAkB,EAAE,IAAI,CAAC,UAAU,CAAC,kBAAmB,CAAC,CAAC;QACtH,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAChF,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC/G,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEzF,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IACzC,CAAC;IAES,gBAAgB;QACtB,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;QAE3D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE;YACrC,IAAI,CAAC,UAAU,CAAC,kBAAkB,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SAC5J;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC;QAE3D,IAAI,YAAY,KAAK,CAAC,EAAE;YACpB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAChE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC;YAC7E,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YAE9D,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACxD,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBACtE,QAAQ,CAAC,SAAS,EAAE,CAAC;gBACrB,MAAM,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACtC,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;oBAClC,UAAU,CAAC,wBAAwB,CAAC,QAAQ,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;iBAC7E;qBAAM;oBACH,UAAU,CAAC,wBAAwB,CAAC,QAAQ,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;iBAC7E;gBACD,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7F,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9D;YACD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtD,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC5D,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACvD;aAAM,IAAI,YAAY,KAAK,CAAC,EAAE;YAC3B,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC5E,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAC/E,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YAC7E,IAAI,CAAC,qBAAqB,CAAC,kBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC;YACpG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAClE,IAAI,CAAC,2BAA2B,EAAE,CAAC;SACtC;IACL,CAAC;IAES,WAAW,CAAC,kBAA2B,EAAE,kBAA8B;QAC7E,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7C,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;SAC3E;aAAM,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,IAAI,CAAC,2BAA2B,EAAE,CAAC;SACtC;IACL,CAAC;IAES,cAAc;QACpB,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7C,mGAAmG;YACnG,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACnC,MAAM,sBAAsB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC1D,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;YACnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,qBAAqB,GAAG,sBAAsB,CAAC;SACvD;IACL,CAAC;IAED;;OAEG;IACI,MAAM;QACT,KAAK,CAAC,MAAM,EAAE,CAAC;QAEf,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACzF;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;SACxC;IACL,CAAC;CACJ", "sourcesContent": ["import type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Vector3, Quaternion, Matrix, TmpVectors } from \"../../Maths/math.vector\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { BaseSixDofDragBehavior } from \"./baseSixDofDragBehavior\";\r\nimport { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { Space } from \"../../Maths/math.axis\";\r\n\r\n/**\r\n * A behavior that when attached to a mesh will allow the mesh to be dragged around based on directions and origin of the pointer's ray\r\n */\r\nexport class SixDofDragBehavior extends BaseSixDofDragBehavior {\r\n    private _sceneRenderObserver: Nullable<Observer<Scene>> = null;\r\n    private _virtualTransformNode: TransformNode;\r\n\r\n    protected _targetPosition = new Vector3(0, 0, 0);\r\n    protected _targetOrientation = new Quaternion();\r\n    protected _targetScaling = new Vector3(1, 1, 1);\r\n    protected _startingPosition = new Vector3(0, 0, 0);\r\n    protected _startingOrientation = new Quaternion();\r\n    protected _startingScaling = new Vector3(1, 1, 1);\r\n\r\n    /**\r\n     * Fires when position is updated\r\n     */\r\n    public onPositionChangedObservable = new Observable<{ position: Vector3 }>();\r\n\r\n    /**\r\n     * The distance towards the target drag position to move each frame. This can be useful to avoid jitter. Set this to 1 for no delay. (Default: 0.2)\r\n     */\r\n    public dragDeltaRatio = 0.2;\r\n\r\n    /**\r\n     * If the object should rotate to face the drag origin\r\n     */\r\n    public rotateDraggedObject = true;\r\n\r\n    /**\r\n     * If `rotateDraggedObject` is set to `true`, this parameter determines if we are only rotating around the y axis (yaw)\r\n     */\r\n    public rotateAroundYOnly = false;\r\n\r\n    /**\r\n     * Should the behavior rotate 1:1 with the motion controller, when one is used.\r\n     */\r\n    public rotateWithMotionController = true;\r\n\r\n    /**\r\n     *  The name of the behavior\r\n     */\r\n    public get name(): string {\r\n        return \"SixDofDrag\";\r\n    }\r\n\r\n    /**\r\n     * Use this flag to update the target but not move the owner node towards the target\r\n     */\r\n    public disableMovement: boolean = false;\r\n\r\n    /**\r\n     * Should the object rotate towards the camera when we start dragging it\r\n     */\r\n    public faceCameraOnDragStart = false;\r\n\r\n    /**\r\n     * Attaches the six DoF drag behavior\r\n     * In XR mode the mesh and its children will have their isNearGrabbable property set to true\r\n     * @param ownerNode The mesh that will be dragged around once attached\r\n     */\r\n    public attach(ownerNode: Mesh): void {\r\n        super.attach(ownerNode);\r\n\r\n        ownerNode.isNearGrabbable = true;\r\n        // if it has children, make sure they are grabbable too\r\n        ownerNode.getChildMeshes().forEach((m) => {\r\n            m.isNearGrabbable = true;\r\n        });\r\n\r\n        // Node that will save the owner's transform\r\n        this._virtualTransformNode = new TransformNode(\"virtual_sixDof\", BaseSixDofDragBehavior._virtualScene);\r\n        this._virtualTransformNode.rotationQuaternion = Quaternion.Identity();\r\n\r\n        // On every frame move towards target scaling to avoid jitter caused by vr controllers\r\n        this._sceneRenderObserver = ownerNode.getScene().onBeforeRenderObservable.add(() => {\r\n            if (this.currentDraggingPointerIds.length === 1 && this._moving && !this.disableMovement) {\r\n                // 1 pointer only drags mesh\r\n                const deltaToAdd = TmpVectors.Vector3[0];\r\n                deltaToAdd.copyFrom(this._targetPosition).subtractInPlace(ownerNode.absolutePosition).scaleInPlace(this.dragDeltaRatio);\r\n                const deltaToAddTransformed = TmpVectors.Vector3[1];\r\n                deltaToAddTransformed.copyFrom(deltaToAdd);\r\n                // If the node has a parent, transform the delta to local space, so it can be added to the\r\n                // position in local space\r\n                if (ownerNode.parent) {\r\n                    const parentRotationMatrixInverse = TmpVectors.Matrix[0];\r\n                    (ownerNode.parent as TransformNode).absoluteRotationQuaternion.toRotationMatrix(parentRotationMatrixInverse);\r\n                    parentRotationMatrixInverse.invert();\r\n                    Vector3.TransformNormalToRef(deltaToAdd, parentRotationMatrixInverse, deltaToAddTransformed);\r\n                }\r\n                ownerNode.position.addInPlace(deltaToAddTransformed);\r\n\r\n                this.onPositionChangedObservable.notifyObservers({ position: ownerNode.absolutePosition });\r\n\r\n                // Only rotate the mesh if it's parent has uniform scaling\r\n                if (!ownerNode.parent || ((ownerNode.parent as TransformNode).scaling && !(ownerNode.parent as TransformNode).scaling.isNonUniformWithinEpsilon(0.001))) {\r\n                    const rotationToApply = TmpVectors.Quaternion[0];\r\n                    rotationToApply.copyFrom(this._targetOrientation);\r\n                    if (ownerNode.parent) {\r\n                        const parentRotationInverse = TmpVectors.Quaternion[0];\r\n                        parentRotationInverse.copyFrom((ownerNode.parent as TransformNode).absoluteRotationQuaternion);\r\n                        parentRotationInverse.invertInPlace();\r\n                        parentRotationInverse.multiplyToRef(this._targetOrientation, rotationToApply);\r\n                    }\r\n                    Quaternion.SlerpToRef(ownerNode.rotationQuaternion!, rotationToApply, this.dragDeltaRatio, ownerNode.rotationQuaternion!);\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    private _getPositionOffsetAround(transformationLocalOrigin: Vector3, scaling: number, rotation: Quaternion): Vector3 {\r\n        const translationMatrix = TmpVectors.Matrix[0]; // T\r\n        const translationMatrixInv = TmpVectors.Matrix[1]; // T'\r\n        const rotationMatrix = TmpVectors.Matrix[2]; // R\r\n        const scaleMatrix = TmpVectors.Matrix[3]; // S\r\n        const finalMatrix = TmpVectors.Matrix[4]; // T' x R x S x T\r\n\r\n        Matrix.TranslationToRef(transformationLocalOrigin.x, transformationLocalOrigin.y, transformationLocalOrigin.z, translationMatrix); // T\r\n        Matrix.TranslationToRef(-transformationLocalOrigin.x, -transformationLocalOrigin.y, -transformationLocalOrigin.z, translationMatrixInv); // T'\r\n        Matrix.FromQuaternionToRef(rotation, rotationMatrix); // R\r\n        Matrix.ScalingToRef(scaling, scaling, scaling, scaleMatrix);\r\n        translationMatrixInv.multiplyToRef(rotationMatrix, finalMatrix); // T' x R\r\n        finalMatrix.multiplyToRef(scaleMatrix, finalMatrix); // T' x R x S\r\n        finalMatrix.multiplyToRef(translationMatrix, finalMatrix); // T' x R x S x T\r\n\r\n        return finalMatrix.getTranslation();\r\n    }\r\n\r\n    private _onePointerPositionUpdated(worldDeltaPosition: Vector3, worldDeltaRotation: Quaternion) {\r\n        const pointerDelta = TmpVectors.Vector3[0];\r\n        pointerDelta.setAll(0);\r\n\r\n        if (this._dragging === this._dragType.DRAG) {\r\n            if (this.rotateDraggedObject) {\r\n                if (this.rotateAroundYOnly) {\r\n                    // Convert change in rotation to only y axis rotation\r\n                    Quaternion.RotationYawPitchRollToRef(worldDeltaRotation.toEulerAngles().y, 0, 0, TmpVectors.Quaternion[0]);\r\n                } else {\r\n                    TmpVectors.Quaternion[0].copyFrom(worldDeltaRotation);\r\n                }\r\n                TmpVectors.Quaternion[0].multiplyToRef(this._startingOrientation, this._targetOrientation);\r\n            }\r\n        } else if (this._dragging === this._dragType.NEAR_DRAG || (this._dragging === this._dragType.DRAG_WITH_CONTROLLER && this.rotateWithMotionController)) {\r\n            worldDeltaRotation.multiplyToRef(this._startingOrientation, this._targetOrientation);\r\n        }\r\n\r\n        this._targetPosition.copyFrom(this._startingPosition).addInPlace(worldDeltaPosition);\r\n    }\r\n\r\n    private _twoPointersPositionUpdated() {\r\n        const startingPosition0 = this._virtualMeshesInfo[this.currentDraggingPointerIds[0]].startingPosition;\r\n        const startingPosition1 = this._virtualMeshesInfo[this.currentDraggingPointerIds[1]].startingPosition;\r\n        const startingCenter = TmpVectors.Vector3[0];\r\n        startingPosition0.addToRef(startingPosition1, startingCenter);\r\n        startingCenter.scaleInPlace(0.5);\r\n        const startingVector = TmpVectors.Vector3[1];\r\n        startingPosition1.subtractToRef(startingPosition0, startingVector);\r\n\r\n        const currentPosition0 = this._virtualMeshesInfo[this.currentDraggingPointerIds[0]].dragMesh.absolutePosition;\r\n        const currentPosition1 = this._virtualMeshesInfo[this.currentDraggingPointerIds[1]].dragMesh.absolutePosition;\r\n        const currentCenter = TmpVectors.Vector3[2];\r\n        currentPosition0.addToRef(currentPosition1, currentCenter);\r\n        currentCenter.scaleInPlace(0.5);\r\n        const currentVector = TmpVectors.Vector3[3];\r\n        currentPosition1.subtractToRef(currentPosition0, currentVector);\r\n\r\n        const scaling = currentVector.length() / startingVector.length();\r\n        const translation = currentCenter.subtract(startingCenter);\r\n        const rotationQuaternion = Quaternion.FromEulerAngles(\r\n            0,\r\n            Vector3.GetAngleBetweenVectorsOnPlane(startingVector.normalize(), currentVector.normalize(), Vector3.UpReadOnly),\r\n            0\r\n        );\r\n\r\n        const oldParent = this._ownerNode.parent;\r\n        this._ownerNode.setParent(null);\r\n\r\n        const positionOffset = this._getPositionOffsetAround(startingCenter.subtract(this._virtualTransformNode.getAbsolutePivotPoint()), scaling, rotationQuaternion);\r\n        this._virtualTransformNode.rotationQuaternion!.multiplyToRef(rotationQuaternion, this._ownerNode.rotationQuaternion!);\r\n        this._virtualTransformNode.scaling.scaleToRef(scaling, this._ownerNode.scaling);\r\n        this._virtualTransformNode.position.addToRef(translation.addInPlace(positionOffset), this._ownerNode.position);\r\n        this.onPositionChangedObservable.notifyObservers({ position: this._ownerNode.position });\r\n\r\n        this._ownerNode.setParent(oldParent);\r\n    }\r\n\r\n    protected _targetDragStart() {\r\n        const pointerCount = this.currentDraggingPointerIds.length;\r\n\r\n        if (!this._ownerNode.rotationQuaternion) {\r\n            this._ownerNode.rotationQuaternion = Quaternion.RotationYawPitchRoll(this._ownerNode.rotation.y, this._ownerNode.rotation.x, this._ownerNode.rotation.z);\r\n        }\r\n        const worldPivot = this._ownerNode.getAbsolutePivotPoint();\r\n\r\n        if (pointerCount === 1) {\r\n            this._targetPosition.copyFrom(this._ownerNode.absolutePosition);\r\n            this._targetOrientation.copyFrom(this._ownerNode.absoluteRotationQuaternion);\r\n            this._targetScaling.copyFrom(this._ownerNode.absoluteScaling);\r\n\r\n            if (this.faceCameraOnDragStart && this._scene.activeCamera) {\r\n                const toCamera = TmpVectors.Vector3[0];\r\n                this._scene.activeCamera.position.subtractToRef(worldPivot, toCamera);\r\n                toCamera.normalize();\r\n                const quat = TmpVectors.Quaternion[0];\r\n                if (this._scene.useRightHandedSystem) {\r\n                    Quaternion.FromLookDirectionRHToRef(toCamera, new Vector3(0, 1, 0), quat);\r\n                } else {\r\n                    Quaternion.FromLookDirectionLHToRef(toCamera, new Vector3(0, 1, 0), quat);\r\n                }\r\n                quat.normalize();\r\n                Quaternion.RotationYawPitchRollToRef(quat.toEulerAngles().y, 0, 0, TmpVectors.Quaternion[0]);\r\n                this._targetOrientation.copyFrom(TmpVectors.Quaternion[0]);\r\n            }\r\n            this._startingPosition.copyFrom(this._targetPosition);\r\n            this._startingOrientation.copyFrom(this._targetOrientation);\r\n            this._startingScaling.copyFrom(this._targetScaling);\r\n        } else if (pointerCount === 2) {\r\n            this._virtualTransformNode.setPivotPoint(new Vector3(0, 0, 0), Space.LOCAL);\r\n            this._virtualTransformNode.position.copyFrom(this._ownerNode.absolutePosition);\r\n            this._virtualTransformNode.scaling.copyFrom(this._ownerNode.absoluteScaling);\r\n            this._virtualTransformNode.rotationQuaternion!.copyFrom(this._ownerNode.absoluteRotationQuaternion);\r\n            this._virtualTransformNode.setPivotPoint(worldPivot, Space.WORLD);\r\n            this._resetVirtualMeshesPosition();\r\n        }\r\n    }\r\n\r\n    protected _targetDrag(worldDeltaPosition: Vector3, worldDeltaRotation: Quaternion) {\r\n        if (this.currentDraggingPointerIds.length === 1) {\r\n            this._onePointerPositionUpdated(worldDeltaPosition, worldDeltaRotation);\r\n        } else if (this.currentDraggingPointerIds.length === 2) {\r\n            this._twoPointersPositionUpdated();\r\n        }\r\n    }\r\n\r\n    protected _targetDragEnd() {\r\n        if (this.currentDraggingPointerIds.length === 1) {\r\n            // We still have 1 active pointer, we must simulate a dragstart with a reseted position/orientation\r\n            this._resetVirtualMeshesPosition();\r\n            const previousFaceCameraFlag = this.faceCameraOnDragStart;\r\n            this.faceCameraOnDragStart = false;\r\n            this._targetDragStart();\r\n            this.faceCameraOnDragStart = previousFaceCameraFlag;\r\n        }\r\n    }\r\n\r\n    /**\r\n     *  Detaches the behavior from the mesh\r\n     */\r\n    public detach(): void {\r\n        super.detach();\r\n\r\n        if (this._ownerNode) {\r\n            this._ownerNode.getScene().onBeforeRenderObservable.remove(this._sceneRenderObserver);\r\n        }\r\n\r\n        if (this._virtualTransformNode) {\r\n            this._virtualTransformNode.dispose();\r\n        }\r\n    }\r\n}\r\n"]}