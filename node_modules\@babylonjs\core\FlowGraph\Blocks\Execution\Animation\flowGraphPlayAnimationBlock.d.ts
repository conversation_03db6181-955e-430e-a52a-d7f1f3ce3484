import type { FlowGraphContext } from "../../../flowGraphContext";
import type { Animatable } from "../../../../Animations/animatable";
import type { FlowGraphDataConnection } from "../../../flowGraphDataConnection";
import { FlowGraphAsyncExecutionBlock } from "../../../flowGraphAsyncExecutionBlock";
import type { IFlowGraphBlockConfiguration } from "../../../flowGraphBlock";
import type { IPathToObjectConverter } from "../../../../ObjectModel/objectModelInterfaces";
import { FlowGraphPathConverterComponent } from "../../../flowGraphPathConverterComponent";
import type { IObjectAccessor } from "../../../typeDefinitions";
/**
 * @experimental
 */
export interface IFlowGraphPlayAnimationBlockConfiguration extends IFlowGraphBlockConfiguration {
    /**
     * The path to the target object that will be animated.
     */
    targetPath: string;
    /**
     * The path to the animation that will be played.
     */
    animationPath: string;
    /**
     * The path converter to use to convert the path to an object accessor.
     */
    pathConverter: IPathToObjectConverter<IObjectAccessor>;
}
/**
 * @experimental
 * A block that plays an animation on an animatable object.
 */
export declare class FlowGraphPlayAnimationBlock extends FlowGraphAsyncExecutionBlock {
    /**
     * the configuration of the block
     */
    config: IFlowGraphPlayAnimationBlockConfiguration;
    /**
     * The substitution inputs for template strings in the target
     */
    readonly templateTargetComponent: FlowGraphPathConverterComponent;
    /**
     * The substitution inputs for template strings in the animation
     */
    readonly templateAnimationComponent: FlowGraphPathConverterComponent;
    /**
     * Input connection: The speed of the animation.
     */
    readonly speed: FlowGraphDataConnection<number>;
    /**
     * Input connection: Should the animation loop?
     */
    readonly loop: FlowGraphDataConnection<boolean>;
    /**
     * Input connection: The starting frame of the animation.
     */
    readonly from: FlowGraphDataConnection<number>;
    /**
     * Input connection: The ending frame of the animation.
     */
    readonly to: FlowGraphDataConnection<number>;
    /**
     * Output connection: The animatable that is currently running.
     */
    readonly runningAnimatable: FlowGraphDataConnection<Animatable>;
    constructor(
    /**
     * the configuration of the block
     */
    config: IFlowGraphPlayAnimationBlockConfiguration);
    /**
     * @internal
     * @param context
     */
    _preparePendingTasks(context: FlowGraphContext): void;
    _execute(context: FlowGraphContext): void;
    private _onAnimationEnd;
    /**
     * @internal
     * Stop any currently running animations.
     */
    _cancelPendingTasks(context: FlowGraphContext): void;
    /**
     * @returns class name of the block.
     */
    getClassName(): string;
    /**
     * Serializes the block to a JSON object.
     * @param serializationObject the object to serialize to.
     */
    serialize(serializationObject?: any): void;
    /**
     * Class name of the block.
     */
    static ClassName: string;
}
