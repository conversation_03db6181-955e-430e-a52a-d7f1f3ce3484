{"version": 3, "file": "webXRInput.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/XR/webXRInput.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAGtD,OAAO,EAAE,4BAA4B,EAAE,MAAM,iDAAiD,CAAC;AAwC/F;;GAEG;AACH,MAAM,OAAO,UAAU;IAiBnB;;;;;OAKG;IACH;IACI;;OAEG;IACI,gBAAqC;IAC5C;;OAEG;IACI,QAAqB,EACX,WAA+B,EAAE;QAL3C,qBAAgB,GAAhB,gBAAgB,CAAqB;QAIrC,aAAQ,GAAR,QAAQ,CAAa;QACX,aAAQ,GAAR,QAAQ,CAAyB;QA/BtD;;WAEG;QACI,gBAAW,GAA4B,EAAE,CAAC;QAIjD;;WAEG;QACI,gCAA2B,GAAG,IAAI,UAAU,EAAoB,CAAC;QACxE;;WAEG;QACI,kCAA6B,GAAG,IAAI,UAAU,EAAoB,CAAC;QAyDlE,0BAAqB,GAAG,CAAC,KAA+B,EAAE,EAAE;YAChE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAC9D,CAAC,CAAC;QAxCE,qCAAqC;QACrC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE;YACzE,IAAI,CAAC,wBAAwB,CACzB,EAAE,EACF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;gBACvB,OAAO,CAAC,CAAC,WAAW,CAAC;YACzB,CAAC,CAAC,CACL,CAAC;QACN,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YAC9E,OAAO,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC1E,8BAA8B;YAC9B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACpC,UAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACpH,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,QAAQ,CAAC,8BAA8B,EAAE;YAC9C,4BAA4B,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,8BAA8B,CAAC;SACjG;QAED,4BAA4B,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,iCAAiC,CAAC;QACpG,IAAI,4BAA4B,CAAC,mBAAmB,EAAE;YAClD,wEAAwE;YACxE,IAAI;gBACA,4BAA4B,CAAC,kBAAkB,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE;oBACzD,4BAA4B,CAAC,mBAAmB,GAAG,KAAK,CAAC;gBAC7D,CAAC,CAAC,CAAC;aACN;YAAC,OAAO,CAAC,EAAE;gBACR,4BAA4B,CAAC,mBAAmB,GAAG,KAAK,CAAC;aAC5D;SACJ;IACL,CAAC;IAMO,wBAAwB,CAAC,SAAmC,EAAE,YAAsC;QACxG,8CAA8C;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACvC,OAAO,CAAC,CAAC,WAAW,CAAC;QACzB,CAAC,CAAC,CAAC;QACH,KAAK,MAAM,KAAK,IAAI,SAAS,EAAE;YAC3B,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC/B,MAAM,UAAU,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE;oBACxE,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,IAAI,EAAE,CAAC;oBAC1C,sBAAsB,EAAE,IAAI,CAAC,QAAQ,CAAC,iBAAiB;oBACvD,uBAAuB,EAAE,IAAI,CAAC,QAAQ,CAAC,yBAAyB;oBAChE,gCAAgC,EAAE,IAAI,CAAC,QAAQ,CAAC,0BAA0B;iBAC7E,CAAC,CAAC;gBACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAClC,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;aAChE;SACJ;QAED,mDAAmD;QACnD,MAAM,eAAe,GAA4B,EAAE,CAAC;QACpD,MAAM,kBAAkB,GAA4B,EAAE,CAAC;QACvD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAC3B,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC5C,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aAC3B;iBAAM;gBACH,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aAC9B;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC;QACnC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAC7B,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACtD,CAAC,CAAC,OAAO,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAC3B,CAAC,CAAC,OAAO,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACtE,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACxE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC1E,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAE3C,6BAA6B;QAC7B,4BAA4B,CAAC,oBAAoB,EAAE,CAAC;IACxD,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { IDisposable } from \"../scene\";\r\nimport type { IWebXRControllerOptions } from \"./webXRInputSource\";\r\nimport { WebXRInputSource } from \"./webXRInputSource\";\r\nimport type { WebXRSessionManager } from \"./webXRSessionManager\";\r\nimport type { WebXRCamera } from \"./webXRCamera\";\r\nimport { WebXRMotionControllerManager } from \"./motionController/webXRMotionControllerManager\";\r\n\r\n/**\r\n * The schema for initialization options of the XR Input class\r\n */\r\nexport interface IWebXRInputOptions {\r\n    /**\r\n     * If set to true no model will be automatically loaded\r\n     */\r\n    doNotLoadControllerMeshes?: boolean;\r\n\r\n    /**\r\n     * If set, this profile will be used for all controllers loaded (for example \"microsoft-mixed-reality\")\r\n     * If not found, the xr input profile data will be used.\r\n     * Profiles are defined here - https://github.com/immersive-web/webxr-input-profiles/\r\n     */\r\n    forceInputProfile?: string;\r\n\r\n    /**\r\n     * Do not send a request to the controller repository to load the profile.\r\n     *\r\n     * Instead, use the controllers available in babylon itself.\r\n     */\r\n    disableOnlineControllerRepository?: boolean;\r\n\r\n    /**\r\n     * A custom URL for the controllers repository\r\n     */\r\n    customControllersRepositoryURL?: string;\r\n\r\n    /**\r\n     * Should the controller model's components not move according to the user input\r\n     */\r\n    disableControllerAnimation?: boolean;\r\n\r\n    /**\r\n     * Optional options to pass to the controller. Will be overridden by the Input options where applicable\r\n     */\r\n    controllerOptions?: IWebXRControllerOptions;\r\n}\r\n/**\r\n * XR input used to track XR inputs such as controllers/rays\r\n */\r\nexport class WebXRInput implements IDisposable {\r\n    /**\r\n     * XR controllers being tracked\r\n     */\r\n    public controllers: Array<WebXRInputSource> = [];\r\n    private _frameObserver: Nullable<Observer<any>>;\r\n    private _sessionEndedObserver: Nullable<Observer<any>>;\r\n    private _sessionInitObserver: Nullable<Observer<any>>;\r\n    /**\r\n     * Event when a controller has been connected/added\r\n     */\r\n    public onControllerAddedObservable = new Observable<WebXRInputSource>();\r\n    /**\r\n     * Event when a controller has been removed/disconnected\r\n     */\r\n    public onControllerRemovedObservable = new Observable<WebXRInputSource>();\r\n\r\n    /**\r\n     * Initializes the WebXRInput\r\n     * @param xrSessionManager the xr session manager for this session\r\n     * @param xrCamera the WebXR camera for this session. Mainly used for teleportation\r\n     * @param _options = initialization options for this xr input\r\n     */\r\n    public constructor(\r\n        /**\r\n         * the xr session manager for this session\r\n         */\r\n        public xrSessionManager: WebXRSessionManager,\r\n        /**\r\n         * the WebXR camera for this session. Mainly used for teleportation\r\n         */\r\n        public xrCamera: WebXRCamera,\r\n        private readonly _options: IWebXRInputOptions = {}\r\n    ) {\r\n        // Remove controllers when exiting XR\r\n        this._sessionEndedObserver = this.xrSessionManager.onXRSessionEnded.add(() => {\r\n            this._addAndRemoveControllers(\r\n                [],\r\n                this.controllers.map((c) => {\r\n                    return c.inputSource;\r\n                })\r\n            );\r\n        });\r\n\r\n        this._sessionInitObserver = this.xrSessionManager.onXRSessionInit.add((session) => {\r\n            session.addEventListener(\"inputsourceschange\", this._onInputSourcesChange);\r\n        });\r\n\r\n        this._frameObserver = this.xrSessionManager.onXRFrameObservable.add((frame) => {\r\n            // Update controller pose info\r\n            this.controllers.forEach((controller) => {\r\n                controller.updateFromXRFrame(frame, this.xrSessionManager.referenceSpace, this.xrCamera, this.xrSessionManager);\r\n            });\r\n        });\r\n\r\n        if (this._options.customControllersRepositoryURL) {\r\n            WebXRMotionControllerManager.BaseRepositoryUrl = this._options.customControllersRepositoryURL;\r\n        }\r\n\r\n        WebXRMotionControllerManager.UseOnlineRepository = !this._options.disableOnlineControllerRepository;\r\n        if (WebXRMotionControllerManager.UseOnlineRepository) {\r\n            // pre-load the profiles list to load the controllers quicker afterwards\r\n            try {\r\n                WebXRMotionControllerManager.UpdateProfilesList().catch(() => {\r\n                    WebXRMotionControllerManager.UseOnlineRepository = false;\r\n                });\r\n            } catch (e) {\r\n                WebXRMotionControllerManager.UseOnlineRepository = false;\r\n            }\r\n        }\r\n    }\r\n\r\n    private _onInputSourcesChange = (event: XRInputSourceChangeEvent) => {\r\n        this._addAndRemoveControllers(event.added, event.removed);\r\n    };\r\n\r\n    private _addAndRemoveControllers(addInputs: readonly XRInputSource[], removeInputs: readonly XRInputSource[]) {\r\n        // Add controllers if they don't already exist\r\n        const sources = this.controllers.map((c) => {\r\n            return c.inputSource;\r\n        });\r\n        for (const input of addInputs) {\r\n            if (sources.indexOf(input) === -1) {\r\n                const controller = new WebXRInputSource(this.xrSessionManager.scene, input, {\r\n                    ...(this._options.controllerOptions || {}),\r\n                    forceControllerProfile: this._options.forceInputProfile,\r\n                    doNotLoadControllerMesh: this._options.doNotLoadControllerMeshes,\r\n                    disableMotionControllerAnimation: this._options.disableControllerAnimation,\r\n                });\r\n                this.controllers.push(controller);\r\n                this.onControllerAddedObservable.notifyObservers(controller);\r\n            }\r\n        }\r\n\r\n        // Remove and dispose of controllers to be disposed\r\n        const keepControllers: Array<WebXRInputSource> = [];\r\n        const removedControllers: Array<WebXRInputSource> = [];\r\n        this.controllers.forEach((c) => {\r\n            if (removeInputs.indexOf(c.inputSource) === -1) {\r\n                keepControllers.push(c);\r\n            } else {\r\n                removedControllers.push(c);\r\n            }\r\n        });\r\n        this.controllers = keepControllers;\r\n        removedControllers.forEach((c) => {\r\n            this.onControllerRemovedObservable.notifyObservers(c);\r\n            c.dispose();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Disposes of the object\r\n     */\r\n    public dispose() {\r\n        this.controllers.forEach((c) => {\r\n            c.dispose();\r\n        });\r\n        this.xrSessionManager.onXRFrameObservable.remove(this._frameObserver);\r\n        this.xrSessionManager.onXRSessionInit.remove(this._sessionInitObserver);\r\n        this.xrSessionManager.onXRSessionEnded.remove(this._sessionEndedObserver);\r\n        this.onControllerAddedObservable.clear();\r\n        this.onControllerRemovedObservable.clear();\r\n\r\n        // clear the controller cache\r\n        WebXRMotionControllerManager.ClearControllerCache();\r\n    }\r\n}\r\n"]}