{"version": 3, "file": "shadowGeneratorSceneComponent.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Lights/Shadows/shadowGeneratorSceneComponent.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAEpE,OAAO,EAAE,uBAAuB,EAAE,MAAM,sBAAsB,CAAC;AAC/D,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACpD,wCAAwC;AACxC,aAAa,CAAC,SAAS,CAAC,uBAAuB,CAAC,oBAAoB,EAAE,CAAC,UAAe,EAAE,KAAY,EAAE,EAAE;IACpG,UAAU;IACV,IAAI,UAAU,CAAC,gBAAgB,KAAK,SAAS,IAAI,UAAU,CAAC,gBAAgB,KAAK,IAAI,EAAE;QACnF,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE;YACpF,MAAM,qBAAqB,GAAG,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACjE,IAAI,qBAAqB,CAAC,SAAS,KAAK,uBAAuB,CAAC,SAAS,EAAE;gBACvE,uBAAuB,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;aAC/D;iBAAM;gBACH,eAAe,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;aACvD;YACD,mDAAmD;SACtD;KACJ;AACL,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,MAAM,OAAO,6BAA6B;IAWtC;;;OAGG;IACH,YAAY,KAAY;QAdxB;;WAEG;QACa,SAAI,GAAG,uBAAuB,CAAC,oBAAoB,CAAC;QAYhE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,YAAY,CAAC,uBAAuB,CAAC,wCAAwC,EAAE,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACzJ,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,sBAAsB;IAC1B,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,mBAAwB;QACrC,UAAU;QACV,mBAAmB,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACjC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YACxB,MAAM,gBAAgB,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;YACrD,IAAI,gBAAgB,EAAE;gBAClB,MAAM,QAAQ,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC;gBAC3C,KAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE;oBACtE,MAAM,eAAe,GAAG,GAAG,CAAC,KAAK,CAAC;oBAClC,mBAAmB,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC;iBAC1E;aACJ;SACJ;IACL,CAAC;IAED;;;OAGG;IACH,6DAA6D;IACtD,gBAAgB,CAAC,SAAwB;QAC5C,qDAAqD;IACzD,CAAC;IAED;;;;OAIG;IACH,6DAA6D;IACtD,mBAAmB,CAAC,SAAwB,EAAE,OAAiB;QAClE,qDAAqD;IACzD,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,sBAAsB;IAC1B,CAAC;IAEO,oBAAoB,CAAC,aAAyD;QAClF,UAAU;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;YAC3B,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;gBACrE,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBACvC,MAAM,gBAAgB,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;gBAErD,IAAI,KAAK,CAAC,SAAS,EAAE,IAAI,KAAK,CAAC,aAAa,IAAI,gBAAgB,EAAE;oBAC9D,MAAM,QAAQ,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC;oBAC3C,KAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE;wBACtE,MAAM,eAAe,GAAG,GAAG,CAAC,KAAK,CAAC;wBAClC,MAAM,SAAS,GAAwB,eAAe,CAAC,YAAY,EAAE,CAAC;wBACtE,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;4BAC1C,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBACjC;qBACJ;iBACJ;aACJ;SACJ;IACL,CAAC;CACJ;AAED,eAAe,CAAC,6BAA6B,GAAG,CAAC,KAAY,EAAE,EAAE;IAC7D,IAAI,SAAS,GAAG,KAAK,CAAC,aAAa,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,CAAC;IAClF,IAAI,CAAC,SAAS,EAAE;QACZ,SAAS,GAAG,IAAI,6BAA6B,CAAC,KAAK,CAAC,CAAC;QACrD,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;KAClC;AACL,CAAC,CAAC", "sourcesContent": ["import type { SmartArrayNoDuplicate } from \"../../Misc/smartArray\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\nimport { ShadowGenerator } from \"./shadowGenerator\";\r\nimport { CascadedShadowGenerator } from \"./cascadedShadowGenerator\";\r\nimport type { ISceneSerializableComponent } from \"../../sceneComponent\";\r\nimport { SceneComponentConstants } from \"../../sceneComponent\";\r\nimport { AbstractScene } from \"../../abstractScene\";\r\n// Adds the parser to the scene parsers.\r\nAbstractScene.AddParser(SceneComponentConstants.NAME_SHADOWGENERATOR, (parsedData: any, scene: Scene) => {\r\n    // Shadows\r\n    if (parsedData.shadowGenerators !== undefined && parsedData.shadowGenerators !== null) {\r\n        for (let index = 0, cache = parsedData.shadowGenerators.length; index < cache; index++) {\r\n            const parsedShadowGenerator = parsedData.shadowGenerators[index];\r\n            if (parsedShadowGenerator.className === CascadedShadowGenerator.CLASSNAME) {\r\n                CascadedShadowGenerator.Parse(parsedShadowGenerator, scene);\r\n            } else {\r\n                ShadowGenerator.Parse(parsedShadowGenerator, scene);\r\n            }\r\n            // SG would be available on their associated lights\r\n        }\r\n    }\r\n});\r\n\r\n/**\r\n * Defines the shadow generator component responsible to manage any shadow generators\r\n * in a given scene.\r\n */\r\nexport class ShadowGeneratorSceneComponent implements ISceneSerializableComponent {\r\n    /**\r\n     * The component name helpful to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_SHADOWGENERATOR;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene: Scene) {\r\n        this.scene = scene;\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {\r\n        this.scene._gatherRenderTargetsStage.registerStep(SceneComponentConstants.STEP_GATHERRENDERTARGETS_SHADOWGENERATOR, this, this._gatherRenderTargets);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        // Nothing To Do Here.\r\n    }\r\n\r\n    /**\r\n     * Serializes the component data to the specified json object\r\n     * @param serializationObject The object to serialize to\r\n     */\r\n    public serialize(serializationObject: any): void {\r\n        // Shadows\r\n        serializationObject.shadowGenerators = [];\r\n        const lights = this.scene.lights;\r\n        for (const light of lights) {\r\n            const shadowGenerators = light.getShadowGenerators();\r\n            if (shadowGenerators) {\r\n                const iterator = shadowGenerators.values();\r\n                for (let key = iterator.next(); key.done !== true; key = iterator.next()) {\r\n                    const shadowGenerator = key.value;\r\n                    serializationObject.shadowGenerators.push(shadowGenerator.serialize());\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds all the elements from the container to the scene\r\n     * @param container the container holding the elements\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public addFromContainer(container: AbstractScene): void {\r\n        // Nothing To Do Here. (directly attached to a light)\r\n    }\r\n\r\n    /**\r\n     * Removes all the elements in the container from the scene\r\n     * @param container contains the elements to remove\r\n     * @param dispose if the removed element should be disposed (default: false)\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public removeFromContainer(container: AbstractScene, dispose?: boolean): void {\r\n        // Nothing To Do Here. (directly attached to a light)\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public dispose(): void {\r\n        // Nothing To Do Here.\r\n    }\r\n\r\n    private _gatherRenderTargets(renderTargets: SmartArrayNoDuplicate<RenderTargetTexture>): void {\r\n        // Shadows\r\n        const scene = this.scene;\r\n        if (this.scene.shadowsEnabled) {\r\n            for (let lightIndex = 0; lightIndex < scene.lights.length; lightIndex++) {\r\n                const light = scene.lights[lightIndex];\r\n                const shadowGenerators = light.getShadowGenerators();\r\n\r\n                if (light.isEnabled() && light.shadowEnabled && shadowGenerators) {\r\n                    const iterator = shadowGenerators.values();\r\n                    for (let key = iterator.next(); key.done !== true; key = iterator.next()) {\r\n                        const shadowGenerator = key.value;\r\n                        const shadowMap = <RenderTargetTexture>shadowGenerator.getShadowMap();\r\n                        if (scene.textures.indexOf(shadowMap) !== -1) {\r\n                            renderTargets.push(shadowMap);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nShadowGenerator._SceneComponentInitialization = (scene: Scene) => {\r\n    let component = scene._getComponent(SceneComponentConstants.NAME_SHADOWGENERATOR);\r\n    if (!component) {\r\n        component = new ShadowGeneratorSceneComponent(scene);\r\n        scene._addComponent(component);\r\n    }\r\n};\r\n"]}