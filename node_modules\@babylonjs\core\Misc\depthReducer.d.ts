import type { Nullable } from "../types";
import type { RenderTargetTexture } from "../Materials/Textures/renderTargetTexture";
import type { Camera } from "../Cameras/camera";
import { DepthRenderer } from "../Rendering/depthRenderer";
import { MinMaxReducer } from "./minMaxReducer";
/**
 * This class is a small wrapper around the MinMaxReducer class to compute the min/max values of a depth texture
 */
export declare class DepthReducer extends MinMaxReducer {
    private _depthRenderer;
    private _depthRendererId;
    /**
     * Gets the depth renderer used for the computation.
     * Note that the result is null if you provide your own renderer when calling setDepthRenderer.
     */
    get depthRenderer(): Nullable<DepthRenderer>;
    /**
     * Creates a depth reducer
     * @param camera The camera used to render the depth texture
     */
    constructor(camera: Camera);
    /**
     * Sets the depth renderer to use to generate the depth map
     * @param depthRenderer The depth renderer to use. If not provided, a new one will be created automatically
     * @param type The texture type of the depth map (default: TEXTURETYPE_HALF_FLOAT)
     * @param forceFullscreenViewport Forces the post processes used for the reduction to be applied without taking into account viewport (defaults to true)
     */
    setDepthRenderer(depthRenderer?: Nullable<DepthRenderer>, type?: number, forceFullscreenViewport?: boolean): void;
    /**
     * @internal
     */
    setSourceTexture(sourceTexture: RenderTargetTexture, depthRedux: boolean, type?: number, forceFullscreenViewport?: boolean): void;
    /**
     * Activates the reduction computation.
     * When activated, the observers registered in onAfterReductionPerformed are
     * called after the computation is performed
     */
    activate(): void;
    /**
     * Deactivates the reduction computation.
     */
    deactivate(): void;
    /**
     * Disposes the depth reducer
     * @param disposeAll true to dispose all the resources. You should always call this function with true as the parameter (or without any parameter as it is the default one). This flag is meant to be used internally.
     */
    dispose(disposeAll?: boolean): void;
}
