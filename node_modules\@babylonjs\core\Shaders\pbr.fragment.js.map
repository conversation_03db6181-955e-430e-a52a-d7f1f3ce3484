{"version": 3, "file": "pbr.fragment.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Shaders/pbr.fragment.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,qCAAqC,CAAC;AAC7C,OAAO,iCAAiC,CAAC;AACzC,OAAO,yCAAyC,CAAC;AACjD,OAAO,oCAAoC,CAAC;AAC5C,OAAO,8CAA8C,CAAC;AACtD,OAAO,2CAA2C,CAAC;AACnD,OAAO,sCAAsC,CAAC;AAC9C,OAAO,iDAAiD,CAAC;AACzD,OAAO,6CAA6C,CAAC;AACrD,OAAO,+CAA+C,CAAC;AACvD,OAAO,sCAAsC,CAAC;AAC9C,OAAO,yCAAyC,CAAC;AACjD,OAAO,kCAAkC,CAAC;AAC1C,OAAO,gDAAgD,CAAC;AACxD,OAAO,qCAAqC,CAAC;AAC7C,OAAO,qCAAqC,CAAC;AAC7C,OAAO,2CAA2C,CAAC;AACnD,OAAO,2CAA2C,CAAC;AACnD,OAAO,qCAAqC,CAAC;AAC7C,OAAO,kDAAkD,CAAC;AAC1D,OAAO,oDAAoD,CAAC;AAC5D,OAAO,mCAAmC,CAAC;AAC3C,OAAO,wCAAwC,CAAC;AAChD,OAAO,6CAA6C,CAAC;AACrD,OAAO,kCAAkC,CAAC;AAC1C,OAAO,4CAA4C,CAAC;AACpD,OAAO,wCAAwC,CAAC;AAChD,OAAO,qCAAqC,CAAC;AAC7C,OAAO,wCAAwC,CAAC;AAChD,OAAO,uCAAuC,CAAC;AAC/C,OAAO,2CAA2C,CAAC;AACnD,OAAO,uCAAuC,CAAC;AAC/C,OAAO,sCAAsC,CAAC;AAC9C,OAAO,qCAAqC,CAAC;AAC7C,OAAO,gCAAgC,CAAC;AACxC,OAAO,oCAAoC,CAAC;AAC5C,OAAO,sCAAsC,CAAC;AAC9C,OAAO,qCAAqC,CAAC;AAC7C,OAAO,oCAAoC,CAAC;AAC5C,OAAO,0CAA0C,CAAC;AAClD,OAAO,+BAA+B,CAAC;AACvC,OAAO,sCAAsC,CAAC;AAC9C,OAAO,+BAA+B,CAAC;AACvC,OAAO,uCAAuC,CAAC;AAC/C,OAAO,uCAAuC,CAAC;AAC/C,OAAO,uCAAuC,CAAC;AAC/C,OAAO,sCAAsC,CAAC;AAC9C,OAAO,yCAAyC,CAAC;AACjD,OAAO,gCAAgC,CAAC;AACxC,OAAO,6CAA6C,CAAC;AACrD,OAAO,+CAA+C,CAAC;AACvD,OAAO,gDAAgD,CAAC;AACxD,OAAO,mCAAmC,CAAC;AAC3C,OAAO,8BAA8B,CAAC;AACtC,OAAO,0CAA0C,CAAC;AAClD,OAAO,8BAA8B,CAAC;AACtC,OAAO,2BAA2B,CAAC;AAEnC,MAAM,IAAI,GAAG,gBAAgB,CAAC;AAC9B,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgkBd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACxC,gBAAgB;AAChB,MAAM,CAAC,MAAM,cAAc,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore\";\nimport \"./ShadersInclude/prePassDeclaration\";\nimport \"./ShadersInclude/oitDeclaration\";\nimport \"./ShadersInclude/pbrFragmentDeclaration\";\nimport \"./ShadersInclude/pbrUboDeclaration\";\nimport \"./ShadersInclude/pbrFragmentExtraDeclaration\";\nimport \"./ShadersInclude/lightFragmentDeclaration\";\nimport \"./ShadersInclude/lightUboDeclaration\";\nimport \"./ShadersInclude/pbrFragmentSamplersDeclaration\";\nimport \"./ShadersInclude/imageProcessingDeclaration\";\nimport \"./ShadersInclude/clipPlaneFragmentDeclaration\";\nimport \"./ShadersInclude/logDepthDeclaration\";\nimport \"./ShadersInclude/fogFragmentDeclaration\";\nimport \"./ShadersInclude/helperFunctions\";\nimport \"./ShadersInclude/subSurfaceScatteringFunctions\";\nimport \"./ShadersInclude/importanceSampling\";\nimport \"./ShadersInclude/pbrHelperFunctions\";\nimport \"./ShadersInclude/imageProcessingFunctions\";\nimport \"./ShadersInclude/shadowsFragmentFunctions\";\nimport \"./ShadersInclude/harmonicsFunctions\";\nimport \"./ShadersInclude/pbrDirectLightingSetupFunctions\";\nimport \"./ShadersInclude/pbrDirectLightingFalloffFunctions\";\nimport \"./ShadersInclude/pbrBRDFFunctions\";\nimport \"./ShadersInclude/hdrFilteringFunctions\";\nimport \"./ShadersInclude/pbrDirectLightingFunctions\";\nimport \"./ShadersInclude/pbrIBLFunctions\";\nimport \"./ShadersInclude/bumpFragmentMainFunctions\";\nimport \"./ShadersInclude/bumpFragmentFunctions\";\nimport \"./ShadersInclude/reflectionFunction\";\nimport \"./ShadersInclude/pbrBlockAlbedoOpacity\";\nimport \"./ShadersInclude/pbrBlockReflectivity\";\nimport \"./ShadersInclude/pbrBlockAmbientOcclusion\";\nimport \"./ShadersInclude/pbrBlockAlphaFresnel\";\nimport \"./ShadersInclude/pbrBlockAnisotropic\";\nimport \"./ShadersInclude/pbrBlockReflection\";\nimport \"./ShadersInclude/pbrBlockSheen\";\nimport \"./ShadersInclude/pbrBlockClearcoat\";\nimport \"./ShadersInclude/pbrBlockIridescence\";\nimport \"./ShadersInclude/pbrBlockSubSurface\";\nimport \"./ShadersInclude/clipPlaneFragment\";\nimport \"./ShadersInclude/pbrBlockNormalGeometric\";\nimport \"./ShadersInclude/bumpFragment\";\nimport \"./ShadersInclude/pbrBlockNormalFinal\";\nimport \"./ShadersInclude/depthPrePass\";\nimport \"./ShadersInclude/pbrBlockLightmapInit\";\nimport \"./ShadersInclude/pbrBlockGeometryInfo\";\nimport \"./ShadersInclude/pbrBlockReflectance0\";\nimport \"./ShadersInclude/pbrBlockReflectance\";\nimport \"./ShadersInclude/pbrBlockDirectLighting\";\nimport \"./ShadersInclude/lightFragment\";\nimport \"./ShadersInclude/pbrBlockFinalLitComponents\";\nimport \"./ShadersInclude/pbrBlockFinalUnlitComponents\";\nimport \"./ShadersInclude/pbrBlockFinalColorComposition\";\nimport \"./ShadersInclude/logDepthFragment\";\nimport \"./ShadersInclude/fogFragment\";\nimport \"./ShadersInclude/pbrBlockImageProcessing\";\nimport \"./ShadersInclude/oitFragment\";\nimport \"./ShadersInclude/pbrDebug\";\n\nconst name = \"pbrPixelShader\";\nconst shader = `#if defined(BUMP) || !defined(NORMAL) || defined(FORCENORMALFORWARD) || defined(SPECULARAA) || defined(CLEARCOAT_BUMP) || defined(ANISOTROPIC)\n#extension GL_OES_standard_derivatives : enable\n#endif\n#ifdef LODBASEDMICROSFURACE\n#extension GL_EXT_shader_texture_lod : enable\n#endif\n#define CUSTOM_FRAGMENT_BEGIN\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<prePassDeclaration>[SCENE_MRT_COUNT]\nprecision highp float;\n#include<oitDeclaration>\n#ifndef FROMLINEARSPACE\n#define FROMLINEARSPACE\n#endif\n#include<__decl__pbrFragment>\n#include<pbrFragmentExtraDeclaration>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#include<pbrFragmentSamplersDeclaration>\n#include<imageProcessingDeclaration>\n#include<clipPlaneFragmentDeclaration>\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\n#include<helperFunctions>\n#include<subSurfaceScatteringFunctions>\n#include<importanceSampling>\n#include<pbrHelperFunctions>\n#include<imageProcessingFunctions>\n#include<shadowsFragmentFunctions>\n#include<harmonicsFunctions>\n#include<pbrDirectLightingSetupFunctions>\n#include<pbrDirectLightingFalloffFunctions>\n#include<pbrBRDFFunctions>\n#include<hdrFilteringFunctions>\n#include<pbrDirectLightingFunctions>\n#include<pbrIBLFunctions>\n#include<bumpFragmentMainFunctions>\n#include<bumpFragmentFunctions>\n#ifdef REFLECTION\n#include<reflectionFunction>\n#endif\n#define CUSTOM_FRAGMENT_DEFINITIONS\n#include<pbrBlockAlbedoOpacity>\n#include<pbrBlockReflectivity>\n#include<pbrBlockAmbientOcclusion>\n#include<pbrBlockAlphaFresnel>\n#include<pbrBlockAnisotropic>\n#include<pbrBlockReflection>\n#include<pbrBlockSheen>\n#include<pbrBlockClearcoat>\n#include<pbrBlockIridescence>\n#include<pbrBlockSubSurface>\nvoid main(void) {\n#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\n#include<pbrBlockNormalGeometric>\n#include<bumpFragment>\n#include<pbrBlockNormalFinal>\nalbedoOpacityOutParams albedoOpacityOut;\n#ifdef ALBEDO\nvec4 albedoTexture=texture2D(albedoSampler,vAlbedoUV+uvOffset);\n#endif\n#ifdef OPACITY\nvec4 opacityMap=texture2D(opacitySampler,vOpacityUV+uvOffset);\n#endif\n#ifdef DECAL\nvec4 decalColor=texture2D(decalSampler,vDecalUV+uvOffset);\n#endif\nalbedoOpacityBlock(\nvAlbedoColor,\n#ifdef ALBEDO\nalbedoTexture,\nvAlbedoInfos,\n#endif\n#ifdef OPACITY\nopacityMap,\nvOpacityInfos,\n#endif\n#ifdef DETAIL\ndetailColor,\nvDetailInfos,\n#endif\n#ifdef DECAL\ndecalColor,\nvDecalInfos,\n#endif\nalbedoOpacityOut\n);vec3 surfaceAlbedo=albedoOpacityOut.surfaceAlbedo;float alpha=albedoOpacityOut.alpha;\n#define CUSTOM_FRAGMENT_UPDATE_ALPHA\n#include<depthPrePass>\n#define CUSTOM_FRAGMENT_BEFORE_LIGHTS\nambientOcclusionOutParams aoOut;\n#ifdef AMBIENT\nvec3 ambientOcclusionColorMap=texture2D(ambientSampler,vAmbientUV+uvOffset).rgb;\n#endif\nambientOcclusionBlock(\n#ifdef AMBIENT\nambientOcclusionColorMap,\nvAmbientInfos,\n#endif\naoOut\n);\n#include<pbrBlockLightmapInit>\n#ifdef UNLIT\nvec3 diffuseBase=vec3(1.,1.,1.);\n#else\nvec3 baseColor=surfaceAlbedo;reflectivityOutParams reflectivityOut;\n#if defined(REFLECTIVITY)\nvec4 surfaceMetallicOrReflectivityColorMap=texture2D(reflectivitySampler,vReflectivityUV+uvOffset);vec4 baseReflectivity=surfaceMetallicOrReflectivityColorMap;\n#ifndef METALLICWORKFLOW\n#ifdef REFLECTIVITY_GAMMA\nsurfaceMetallicOrReflectivityColorMap=toLinearSpace(surfaceMetallicOrReflectivityColorMap);\n#endif\nsurfaceMetallicOrReflectivityColorMap.rgb*=vReflectivityInfos.y;\n#endif\n#endif\n#if defined(MICROSURFACEMAP)\nvec4 microSurfaceTexel=texture2D(microSurfaceSampler,vMicroSurfaceSamplerUV+uvOffset)*vMicroSurfaceSamplerInfos.y;\n#endif\n#ifdef METALLICWORKFLOW\nvec4 metallicReflectanceFactors=vMetallicReflectanceFactors;\n#ifdef REFLECTANCE\nvec4 reflectanceFactorsMap=texture2D(reflectanceSampler,vReflectanceUV+uvOffset);\n#ifdef REFLECTANCE_GAMMA\nreflectanceFactorsMap=toLinearSpace(reflectanceFactorsMap);\n#endif\nmetallicReflectanceFactors.rgb*=reflectanceFactorsMap.rgb;\n#endif\n#ifdef METALLIC_REFLECTANCE\nvec4 metallicReflectanceFactorsMap=texture2D(metallicReflectanceSampler,vMetallicReflectanceUV+uvOffset);\n#ifdef METALLIC_REFLECTANCE_GAMMA\nmetallicReflectanceFactorsMap=toLinearSpace(metallicReflectanceFactorsMap);\n#endif\n#ifndef METALLIC_REFLECTANCE_USE_ALPHA_ONLY\nmetallicReflectanceFactors.rgb*=metallicReflectanceFactorsMap.rgb;\n#endif\nmetallicReflectanceFactors*=metallicReflectanceFactorsMap.a;\n#endif\n#endif\nreflectivityBlock(\nvReflectivityColor,\n#ifdef METALLICWORKFLOW\nsurfaceAlbedo,\nmetallicReflectanceFactors,\n#endif\n#ifdef REFLECTIVITY\nvReflectivityInfos,\nsurfaceMetallicOrReflectivityColorMap,\n#endif\n#if defined(METALLICWORKFLOW) && defined(REFLECTIVITY) && defined(AOSTOREINMETALMAPRED)\naoOut.ambientOcclusionColor,\n#endif\n#ifdef MICROSURFACEMAP\nmicroSurfaceTexel,\n#endif\n#ifdef DETAIL\ndetailColor,\nvDetailInfos,\n#endif\nreflectivityOut\n);float microSurface=reflectivityOut.microSurface;float roughness=reflectivityOut.roughness;\n#ifdef METALLICWORKFLOW\nsurfaceAlbedo=reflectivityOut.surfaceAlbedo;\n#endif\n#if defined(METALLICWORKFLOW) && defined(REFLECTIVITY) && defined(AOSTOREINMETALMAPRED)\naoOut.ambientOcclusionColor=reflectivityOut.ambientOcclusionColor;\n#endif\n#ifdef ALPHAFRESNEL\n#if defined(ALPHATEST) || defined(ALPHABLEND)\nalphaFresnelOutParams alphaFresnelOut;alphaFresnelBlock(\nnormalW,\nviewDirectionW,\nalpha,\nmicroSurface,\nalphaFresnelOut\n);alpha=alphaFresnelOut.alpha;\n#endif\n#endif\n#include<pbrBlockGeometryInfo>\n#ifdef ANISOTROPIC\nanisotropicOutParams anisotropicOut;\n#ifdef ANISOTROPIC_TEXTURE\nvec3 anisotropyMapData=texture2D(anisotropySampler,vAnisotropyUV+uvOffset).rgb*vAnisotropyInfos.y;\n#endif\nanisotropicBlock(\nvAnisotropy,\nroughness,\n#ifdef ANISOTROPIC_TEXTURE\nanisotropyMapData,\n#endif\nTBN,\nnormalW,\nviewDirectionW,\nanisotropicOut\n);\n#endif\n#ifdef REFLECTION\nreflectionOutParams reflectionOut;\n#ifndef USE_CUSTOM_REFLECTION\nreflectionBlock(\nvPositionW,\nnormalW,\nalphaG,\nvReflectionMicrosurfaceInfos,\nvReflectionInfos,\nvReflectionColor,\n#ifdef ANISOTROPIC\nanisotropicOut,\n#endif\n#if defined(LODINREFLECTIONALPHA) && !defined(REFLECTIONMAP_SKYBOX)\nNdotVUnclamped,\n#endif\n#ifdef LINEARSPECULARREFLECTION\nroughness,\n#endif\nreflectionSampler,\n#if defined(NORMAL) && defined(USESPHERICALINVERTEX)\nvEnvironmentIrradiance,\n#endif\n#ifdef USESPHERICALFROMREFLECTIONMAP\n#if !defined(NORMAL) || !defined(USESPHERICALINVERTEX)\nreflectionMatrix,\n#endif\n#endif\n#ifdef USEIRRADIANCEMAP\nirradianceSampler,\n#endif\n#ifndef LODBASEDMICROSFURACE\nreflectionSamplerLow,\nreflectionSamplerHigh,\n#endif\n#ifdef REALTIME_FILTERING\nvReflectionFilteringInfo,\n#endif\nreflectionOut\n);\n#else\n#define CUSTOM_REFLECTION\n#endif\n#endif\n#include<pbrBlockReflectance0>\n#ifdef SHEEN\nsheenOutParams sheenOut;\n#ifdef SHEEN_TEXTURE\nvec4 sheenMapData=texture2D(sheenSampler,vSheenUV+uvOffset);\n#endif\n#if defined(SHEEN_ROUGHNESS) && defined(SHEEN_TEXTURE_ROUGHNESS) && !defined(SHEEN_TEXTURE_ROUGHNESS_IDENTICAL) && !defined(SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE)\nvec4 sheenMapRoughnessData=texture2D(sheenRoughnessSampler,vSheenRoughnessUV+uvOffset)*vSheenInfos.w;\n#endif\nsheenBlock(\nvSheenColor,\n#ifdef SHEEN_ROUGHNESS\nvSheenRoughness,\n#if defined(SHEEN_TEXTURE_ROUGHNESS) && !defined(SHEEN_TEXTURE_ROUGHNESS_IDENTICAL) && !defined(SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE)\nsheenMapRoughnessData,\n#endif\n#endif\nroughness,\n#ifdef SHEEN_TEXTURE\nsheenMapData,\nvSheenInfos.y,\n#endif\nreflectance,\n#ifdef SHEEN_LINKWITHALBEDO\nbaseColor,\nsurfaceAlbedo,\n#endif\n#ifdef ENVIRONMENTBRDF\nNdotV,\nenvironmentBrdf,\n#endif\n#if defined(REFLECTION) && defined(ENVIRONMENTBRDF)\nAARoughnessFactors,\nvReflectionMicrosurfaceInfos,\nvReflectionInfos,\nvReflectionColor,\nvLightingIntensity,\nreflectionSampler,\nreflectionOut.reflectionCoords,\nNdotVUnclamped,\n#ifndef LODBASEDMICROSFURACE\nreflectionSamplerLow,\nreflectionSamplerHigh,\n#endif\n#ifdef REALTIME_FILTERING\nvReflectionFilteringInfo,\n#endif\n#if !defined(REFLECTIONMAP_SKYBOX) && defined(RADIANCEOCCLUSION)\nseo,\n#endif\n#if !defined(REFLECTIONMAP_SKYBOX) && defined(HORIZONOCCLUSION) && defined(BUMP) && defined(REFLECTIONMAP_3D)\neho,\n#endif\n#endif\nsheenOut\n);\n#ifdef SHEEN_LINKWITHALBEDO\nsurfaceAlbedo=sheenOut.surfaceAlbedo;\n#endif\n#endif\n#ifdef CLEARCOAT\n#ifdef CLEARCOAT_TEXTURE\nvec2 clearCoatMapData=texture2D(clearCoatSampler,vClearCoatUV+uvOffset).rg*vClearCoatInfos.y;\n#endif\n#endif\n#ifdef IRIDESCENCE\niridescenceOutParams iridescenceOut;\n#ifdef IRIDESCENCE_TEXTURE\nvec2 iridescenceMapData=texture2D(iridescenceSampler,vIridescenceUV+uvOffset).rg*vIridescenceInfos.y;\n#endif\n#ifdef IRIDESCENCE_THICKNESS_TEXTURE\nvec2 iridescenceThicknessMapData=texture2D(iridescenceThicknessSampler,vIridescenceThicknessUV+uvOffset).rg*vIridescenceInfos.w;\n#endif\niridescenceBlock(\nvIridescenceParams,\nNdotV,\nspecularEnvironmentR0,\n#ifdef IRIDESCENCE_TEXTURE\niridescenceMapData,\n#endif\n#ifdef IRIDESCENCE_THICKNESS_TEXTURE\niridescenceThicknessMapData,\n#endif\n#ifdef CLEARCOAT\nNdotVUnclamped,\n#ifdef CLEARCOAT_TEXTURE\nclearCoatMapData,\n#endif\n#endif\niridescenceOut\n);float iridescenceIntensity=iridescenceOut.iridescenceIntensity;specularEnvironmentR0=iridescenceOut.specularEnvironmentR0;\n#endif\nclearcoatOutParams clearcoatOut;\n#ifdef CLEARCOAT\n#if defined(CLEARCOAT_TEXTURE_ROUGHNESS) && !defined(CLEARCOAT_TEXTURE_ROUGHNESS_IDENTICAL) && !defined(CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE)\nvec4 clearCoatMapRoughnessData=texture2D(clearCoatRoughnessSampler,vClearCoatRoughnessUV+uvOffset)*vClearCoatInfos.w;\n#endif\n#if defined(CLEARCOAT_TINT) && defined(CLEARCOAT_TINT_TEXTURE)\nvec4 clearCoatTintMapData=texture2D(clearCoatTintSampler,vClearCoatTintUV+uvOffset);\n#endif\n#ifdef CLEARCOAT_BUMP\nvec4 clearCoatBumpMapData=texture2D(clearCoatBumpSampler,vClearCoatBumpUV+uvOffset);\n#endif\nclearcoatBlock(\nvPositionW,\ngeometricNormalW,\nviewDirectionW,\nvClearCoatParams,\n#if defined(CLEARCOAT_TEXTURE_ROUGHNESS) && !defined(CLEARCOAT_TEXTURE_ROUGHNESS_IDENTICAL) && !defined(CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE)\nclearCoatMapRoughnessData,\n#endif\nspecularEnvironmentR0,\n#ifdef CLEARCOAT_TEXTURE\nclearCoatMapData,\n#endif\n#ifdef CLEARCOAT_TINT\nvClearCoatTintParams,\nclearCoatColorAtDistance,\nvClearCoatRefractionParams,\n#ifdef CLEARCOAT_TINT_TEXTURE\nclearCoatTintMapData,\n#endif\n#endif\n#ifdef CLEARCOAT_BUMP\nvClearCoatBumpInfos,\nclearCoatBumpMapData,\nvClearCoatBumpUV,\n#if defined(TANGENT) && defined(NORMAL)\nvTBN,\n#else\nvClearCoatTangentSpaceParams,\n#endif\n#ifdef OBJECTSPACE_NORMALMAP\nnormalMatrix,\n#endif\n#endif\n#if defined(FORCENORMALFORWARD) && defined(NORMAL)\nfaceNormal,\n#endif\n#ifdef REFLECTION\nvReflectionMicrosurfaceInfos,\nvReflectionInfos,\nvReflectionColor,\nvLightingIntensity,\nreflectionSampler,\n#ifndef LODBASEDMICROSFURACE\nreflectionSamplerLow,\nreflectionSamplerHigh,\n#endif\n#ifdef REALTIME_FILTERING\nvReflectionFilteringInfo,\n#endif\n#endif\n#if defined(ENVIRONMENTBRDF) && !defined(REFLECTIONMAP_SKYBOX)\n#ifdef RADIANCEOCCLUSION\nambientMonochrome,\n#endif\n#endif\n#if defined(CLEARCOAT_BUMP) || defined(TWOSIDEDLIGHTING)\n(gl_FrontFacing ? 1. : -1.),\n#endif\nclearcoatOut\n);\n#else\nclearcoatOut.specularEnvironmentR0=specularEnvironmentR0;\n#endif\n#include<pbrBlockReflectance>\nsubSurfaceOutParams subSurfaceOut;\n#ifdef SUBSURFACE\n#ifdef SS_THICKNESSANDMASK_TEXTURE\nvec4 thicknessMap=texture2D(thicknessSampler,vThicknessUV+uvOffset);\n#endif\n#ifdef SS_REFRACTIONINTENSITY_TEXTURE\nvec4 refractionIntensityMap=texture2D(refractionIntensitySampler,vRefractionIntensityUV+uvOffset);\n#endif\n#ifdef SS_TRANSLUCENCYINTENSITY_TEXTURE\nvec4 translucencyIntensityMap=texture2D(translucencyIntensitySampler,vTranslucencyIntensityUV+uvOffset);\n#endif\nsubSurfaceBlock(\nvSubSurfaceIntensity,\nvThicknessParam,\nvTintColor,\nnormalW,\nspecularEnvironmentReflectance,\n#ifdef SS_THICKNESSANDMASK_TEXTURE\nthicknessMap,\n#endif\n#ifdef SS_REFRACTIONINTENSITY_TEXTURE\nrefractionIntensityMap,\n#endif\n#ifdef SS_TRANSLUCENCYINTENSITY_TEXTURE\ntranslucencyIntensityMap,\n#endif\n#ifdef REFLECTION\n#ifdef SS_TRANSLUCENCY\nreflectionMatrix,\n#ifdef USESPHERICALFROMREFLECTIONMAP\n#if !defined(NORMAL) || !defined(USESPHERICALINVERTEX)\nreflectionOut.irradianceVector,\n#endif\n#if defined(REALTIME_FILTERING)\nreflectionSampler,\nvReflectionFilteringInfo,\n#endif\n#endif\n#ifdef USEIRRADIANCEMAP\nirradianceSampler,\n#endif\n#endif\n#endif\n#if defined(SS_REFRACTION) || defined(SS_TRANSLUCENCY)\nsurfaceAlbedo,\n#endif\n#ifdef SS_REFRACTION\nvPositionW,\nviewDirectionW,\nview,\nvRefractionInfos,\nrefractionMatrix,\nvRefractionMicrosurfaceInfos,\nvLightingIntensity,\n#ifdef SS_LINKREFRACTIONTOTRANSPARENCY\nalpha,\n#endif\n#ifdef SS_LODINREFRACTIONALPHA\nNdotVUnclamped,\n#endif\n#ifdef SS_LINEARSPECULARREFRACTION\nroughness,\n#endif\nalphaG,\nrefractionSampler,\n#ifndef LODBASEDMICROSFURACE\nrefractionSamplerLow,\nrefractionSamplerHigh,\n#endif\n#ifdef ANISOTROPIC\nanisotropicOut,\n#endif\n#ifdef REALTIME_FILTERING\nvRefractionFilteringInfo,\n#endif\n#ifdef SS_USE_LOCAL_REFRACTIONMAP_CUBIC\nvRefractionPosition,\nvRefractionSize,\n#endif\n#ifdef SS_DISPERSION\ndispersion,\n#endif\n#endif\n#ifdef SS_TRANSLUCENCY\nvDiffusionDistance,\n#endif\nsubSurfaceOut\n);\n#ifdef SS_REFRACTION\nsurfaceAlbedo=subSurfaceOut.surfaceAlbedo;\n#ifdef SS_LINKREFRACTIONTOTRANSPARENCY\nalpha=subSurfaceOut.alpha;\n#endif\n#endif\n#else\nsubSurfaceOut.specularEnvironmentReflectance=specularEnvironmentReflectance;\n#endif\n#include<pbrBlockDirectLighting>\n#include<lightFragment>[0..maxSimultaneousLights]\n#include<pbrBlockFinalLitComponents>\n#endif \n#include<pbrBlockFinalUnlitComponents>\n#define CUSTOM_FRAGMENT_BEFORE_FINALCOLORCOMPOSITION\n#include<pbrBlockFinalColorComposition>\n#include<logDepthFragment>\n#include<fogFragment>(color,finalColor)\n#include<pbrBlockImageProcessing>\n#define CUSTOM_FRAGMENT_BEFORE_FRAGCOLOR\n#ifdef PREPASS\nfloat writeGeometryInfo=finalColor.a>0.4 ? 1.0 : 0.0;\n#ifdef PREPASS_POSITION\ngl_FragData[PREPASS_POSITION_INDEX]=vec4(vPositionW,writeGeometryInfo);\n#endif\n#ifdef PREPASS_VELOCITY\nvec2 a=(vCurrentPosition.xy/vCurrentPosition.w)*0.5+0.5;vec2 b=(vPreviousPosition.xy/vPreviousPosition.w)*0.5+0.5;vec2 velocity=abs(a-b);velocity=vec2(pow(velocity.x,1.0/3.0),pow(velocity.y,1.0/3.0))*sign(a-b)*0.5+0.5;gl_FragData[PREPASS_VELOCITY_INDEX]=vec4(velocity,0.0,writeGeometryInfo);\n#endif\n#ifdef PREPASS_ALBEDO_SQRT\nvec3 sqAlbedo=sqrt(surfaceAlbedo); \n#endif\n#ifdef PREPASS_IRRADIANCE\nvec3 irradiance=finalDiffuse;\n#ifndef UNLIT\n#ifdef REFLECTION\nirradiance+=finalIrradiance;\n#endif\n#endif\n#ifdef SS_SCATTERING\ngl_FragData[0]=vec4(finalColor.rgb-irradiance,finalColor.a); \nirradiance/=sqAlbedo;\n#else\ngl_FragData[0]=finalColor; \nfloat scatteringDiffusionProfile=255.;\n#endif\ngl_FragData[PREPASS_IRRADIANCE_INDEX]=vec4(clamp(irradiance,vec3(0.),vec3(1.)),writeGeometryInfo*scatteringDiffusionProfile/255.); \n#else\ngl_FragData[0]=vec4(finalColor.rgb,finalColor.a);\n#endif\n#ifdef PREPASS_DEPTH\ngl_FragData[PREPASS_DEPTH_INDEX]=vec4(vViewPos.z,0.0,0.0,writeGeometryInfo); \n#endif\n#ifdef PREPASS_NORMAL\n#ifdef PREPASS_NORMAL_WORLDSPACE\ngl_FragData[PREPASS_NORMAL_INDEX]=vec4(normalW,writeGeometryInfo); \n#else\ngl_FragData[PREPASS_NORMAL_INDEX]=vec4(normalize((view*vec4(normalW,0.0)).rgb),writeGeometryInfo); \n#endif\n#endif\n#ifdef PREPASS_ALBEDO_SQRT\ngl_FragData[PREPASS_ALBEDO_SQRT_INDEX]=vec4(sqAlbedo,writeGeometryInfo); \n#endif\n#ifdef PREPASS_REFLECTIVITY\n#ifndef UNLIT\ngl_FragData[PREPASS_REFLECTIVITY_INDEX]=vec4(specularEnvironmentR0,microSurface)*writeGeometryInfo;\n#else\ngl_FragData[PREPASS_REFLECTIVITY_INDEX]=vec4( 0.0,0.0,0.0,1.0 )*writeGeometryInfo;\n#endif\n#endif\n#endif\n#if !defined(PREPASS) || defined(WEBGL2)\ngl_FragColor=finalColor;\n#endif\n#include<oitFragment>\n#if ORDER_INDEPENDENT_TRANSPARENCY\nif (fragDepth==nearestDepth) {frontColor.rgb+=finalColor.rgb*finalColor.a*alphaMultiplier;frontColor.a=1.0-alphaMultiplier*(1.0-finalColor.a);} else {backColor+=finalColor;}\n#endif\n#include<pbrDebug>\n#define CUSTOM_FRAGMENT_MAIN_END\n}\n`;\n// Sideeffect\nShaderStore.ShadersStore[name] = shader;\n/** @internal */\nexport const pbrPixelShader = { name, shader };\n"]}