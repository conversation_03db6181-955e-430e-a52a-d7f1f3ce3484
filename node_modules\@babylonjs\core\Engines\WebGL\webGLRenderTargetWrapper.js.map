{"version": 3, "file": "webGLRenderTargetWrapper.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGL/webGLRenderTargetWrapper.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;AAG7D,gBAAgB;AAChB,MAAM,OAAO,wBAAyB,SAAQ,mBAAmB;IAoC7D,YAAY,OAAgB,EAAE,MAAe,EAAE,IAAiB,EAAE,MAAkB,EAAE,OAA8B;QAChH,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAlCzC;;WAEG;QACI,iBAAY,GAA+B,IAAI,CAAC;QACvD;;WAEG;QACI,wBAAmB,GAAgC,IAAI,CAAC;QAC/D,gEAAgE;QAChE;;WAEG;QACH,gEAAgE;QACzD,qBAAgB,GAA+B,IAAI,CAAC;QAE3D,YAAY;QACZ;;WAEG;QACI,uBAAkB,GAA2B,IAAI,CAAC;QACzD;;WAEG;QACI,8BAAyB,GAA2B,IAAI,CAAC;QAChE;;WAEG;QACI,6BAAwB,GAAG,KAAK,CAAC;QACxC;;WAEG;QACI,gBAAW,GAAG,CAAC,CAAC;QAKnB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAES,yBAAyB;QAC/B,IAAI,GAAG,GAAkC,IAAI,CAAC;QAE9C,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAC3D,GAAG,GAAI,IAAI,CAAC,OAAkB,CAAC,kCAAkC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3F,GAAG,CAAC,OAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;SAC/B;aAAM;YACH,GAAG,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;SAC3C;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAES,wBAAwB,CAAC,MAAgC;QAC/D,KAAK,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAEvC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACxC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACtD,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAChD,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACpD,MAAM,CAAC,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAC;QAElE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;IAC3I,CAAC;IAED;;;;;;;;;OASG;IACI,yBAAyB,CAC5B,qBAA6B,CAAC,EAC9B,oBAA6B,IAAI,EACjC,kBAA2B,KAAK,EAChC,UAAkB,CAAC,EACnB,SAAiB,SAAS,CAAC,2BAA2B,EACtD,KAAc;QAEd,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,wFAAwF;YACxF,uFAAuF;YACvF,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;YAC5D,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;YAEzB,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACxD,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,wBAAwB,EAAE,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC/F,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YACvF,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YACzF,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;YACzD,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEhD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACnC;QAED,OAAO,KAAK,CAAC,yBAAyB,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,eAAe,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IAC3H,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,YAAsC;QACrD,KAAK,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAEhC,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;QACzB,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAC7C,MAAM,WAAW,GAAG,YAAY,CAAC,gBAAgB,IAAI,YAAY,CAAC,YAAY,CAAC;QAE/E,IAAI,YAAY,CAAC,mBAAmB,IAAI,YAAY,CAAC,mBAAmB,KAAK,WAAW,EAAE;YACtF,EAAE,CAAC,kBAAkB,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;SAC3D;QACD,YAAY,CAAC,mBAAmB,GAAG,WAAW,CAAC;QAC/C,MAAM,UAAU,GAAG,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC;QAC3G,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;QAClD,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QACrF,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;OAMG;IACK,wBAAwB,CAAC,OAAwB,EAAE,kBAA0B,CAAC,EAAE,gBAAyB,EAAE,WAAmB,CAAC;QACnI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YAC3B,OAAO;SACV;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;QAEtC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;QACnD,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;QAElD,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,EAAE;YAC/B,MAAM,EAAE,GAAG,IAAI,CAAC,QAAkC,CAAC;YAEnD,MAAM,UAAU,GAAS,EAAG,CAAC,kBAAkB,GAAG,eAAe,CAAC,CAAC;YACnE,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,EAAE;gBACnC,gBAAgB,GAAG,gBAAgB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACjF,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC;aACnI;iBAAM,IAAI,OAAO,CAAC,MAAM,EAAE;gBACvB,mEAAmE;gBACnE,oBAAoB;gBACpB,gBAAgB,GAAG,gBAAgB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAChF,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,2BAA2B,GAAG,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;aACjK;iBAAM;gBACH,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;aAC7H;SACJ;aAAM;YACH,2BAA2B;YAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;YAEzB,MAAM,UAAU,GAAS,EAAG,CAAC,kBAAkB,GAAG,eAAe,GAAG,QAAQ,CAAC,CAAC;YAC9E,MAAM,MAAM,GAAG,gBAAgB,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,2BAA2B,GAAG,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC;YAElH,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;SACtH;QAED,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;IAED;;;;;OAKG;IACI,UAAU,CAAC,OAAwB,EAAE,QAAgB,CAAC,EAAE,kBAA2B,IAAI;QAC1F,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;QAClD,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,MAAgB,EAAE,KAAe;QAC3D,KAAK,CAAC,sBAAsB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAE5C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YAC3D,OAAO;SACV;QAED,mHAAmH;QACnH,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACvE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,EAAE,KAAK,EAAE,EAAE;YAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,CAAC,OAAO,EAAE;gBACV,yGAAyG;gBACzG,SAAS;aACZ;YACD,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,EAAE;gBACnC,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;aAC3E;iBAAM,IAAI,OAAO,CAAC,MAAM,EAAE;gBACvB,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;aAC1E;iBAAM;gBACH,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aACjD;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,oBAAoB,CAAC,QAAgB,CAAC,EAAE,KAAc,EAAE,IAAa;QACxE,KAAK,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YAC3D,OAAO;SACV;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,EAAE;YACnC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;SACxF;aAAM,IAAI,OAAO,CAAC,MAAM,EAAE;YACvB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;SACvF;IACL,CAAC;IAEM,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC,wBAAwB;QAClE,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEzB,IAAI,CAAC,uBAAuB,EAAE;YAC1B,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACrD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;aAClC;YACD,IAAI,IAAI,CAAC,yBAAyB,EAAE;gBAChC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;gBAC5D,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;aACzC;SACJ;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACxC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAChD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC5C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;QAED,KAAK,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;IAC3C,CAAC;CACJ", "sourcesContent": ["import type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport type { TextureSize } from \"../../Materials/Textures/textureCreationOptions\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Constants } from \"../constants\";\r\nimport type { Engine } from \"../engine\";\r\nimport { RenderTargetWrapper } from \"../renderTargetWrapper\";\r\nimport type { ThinEngine } from \"../thinEngine\";\r\n\r\n/** @internal */\r\nexport class WebGLRenderTargetWrapper extends RenderTargetWrapper {\r\n    private _context: WebGLRenderingContext;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _framebuffer: Nullable<WebGLFramebuffer> = null;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _depthStencilBuffer: Nullable<WebGLRenderbuffer> = null;\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public _MSAAFramebuffer: Nullable<WebGLFramebuffer> = null;\r\n\r\n    // Multiview\r\n    /**\r\n     * @internal\r\n     */\r\n    public _colorTextureArray: Nullable<WebGLTexture> = null;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _depthStencilTextureArray: Nullable<WebGLTexture> = null;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _disposeOnlyFramebuffers = false;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _currentLOD = 0;\r\n\r\n    constructor(isMulti: boolean, isCube: boolean, size: TextureSize, engine: ThinEngine, context: WebGLRenderingContext) {\r\n        super(isMulti, isCube, size, engine);\r\n\r\n        this._context = context;\r\n    }\r\n\r\n    protected _cloneRenderTargetWrapper(): Nullable<RenderTargetWrapper> {\r\n        let rtw: Nullable<RenderTargetWrapper> = null;\r\n\r\n        if (this._colorTextureArray && this._depthStencilTextureArray) {\r\n            rtw = (this._engine as Engine).createMultiviewRenderTargetTexture(this.width, this.height);\r\n            rtw.texture!.isReady = true;\r\n        } else {\r\n            rtw = super._cloneRenderTargetWrapper();\r\n        }\r\n\r\n        return rtw;\r\n    }\r\n\r\n    protected _swapRenderTargetWrapper(target: WebGLRenderTargetWrapper): void {\r\n        super._swapRenderTargetWrapper(target);\r\n\r\n        target._framebuffer = this._framebuffer;\r\n        target._depthStencilBuffer = this._depthStencilBuffer;\r\n        target._MSAAFramebuffer = this._MSAAFramebuffer;\r\n        target._colorTextureArray = this._colorTextureArray;\r\n        target._depthStencilTextureArray = this._depthStencilTextureArray;\r\n\r\n        this._framebuffer = this._depthStencilBuffer = this._MSAAFramebuffer = this._colorTextureArray = this._depthStencilTextureArray = null;\r\n    }\r\n\r\n    /**\r\n     * Creates the depth/stencil texture\r\n     * @param comparisonFunction Comparison function to use for the texture\r\n     * @param bilinearFiltering true if bilinear filtering should be used when sampling the texture\r\n     * @param generateStencil true if the stencil aspect should also be created\r\n     * @param samples sample count to use when creating the texture\r\n     * @param format format of the depth texture\r\n     * @param label defines the label to use for the texture (for debugging purpose only)\r\n     * @returns the depth/stencil created texture\r\n     */\r\n    public createDepthStencilTexture(\r\n        comparisonFunction: number = 0,\r\n        bilinearFiltering: boolean = true,\r\n        generateStencil: boolean = false,\r\n        samples: number = 1,\r\n        format: number = Constants.TEXTUREFORMAT_DEPTH32_FLOAT,\r\n        label?: string\r\n    ): InternalTexture {\r\n        if (this._depthStencilBuffer) {\r\n            // Dispose previous depth/stencil render buffers and clear the corresponding attachment.\r\n            // Next time this framebuffer is bound, the new depth/stencil texture will be attached.\r\n            const currentFrameBuffer = this._engine._currentFramebuffer;\r\n            const gl = this._context;\r\n\r\n            this._engine._bindUnboundFramebuffer(this._framebuffer);\r\n            gl.framebufferRenderbuffer(gl.FRAMEBUFFER, gl.DEPTH_STENCIL_ATTACHMENT, gl.RENDERBUFFER, null);\r\n            gl.framebufferRenderbuffer(gl.FRAMEBUFFER, gl.DEPTH_ATTACHMENT, gl.RENDERBUFFER, null);\r\n            gl.framebufferRenderbuffer(gl.FRAMEBUFFER, gl.STENCIL_ATTACHMENT, gl.RENDERBUFFER, null);\r\n            this._engine._bindUnboundFramebuffer(currentFrameBuffer);\r\n            gl.deleteRenderbuffer(this._depthStencilBuffer);\r\n\r\n            this._depthStencilBuffer = null;\r\n        }\r\n\r\n        return super.createDepthStencilTexture(comparisonFunction, bilinearFiltering, generateStencil, samples, format, label);\r\n    }\r\n\r\n    /**\r\n     * Shares the depth buffer of this render target with another render target.\r\n     * @internal\r\n     * @param renderTarget Destination renderTarget\r\n     */\r\n    public _shareDepth(renderTarget: WebGLRenderTargetWrapper): void {\r\n        super._shareDepth(renderTarget);\r\n\r\n        const gl = this._context;\r\n        const depthbuffer = this._depthStencilBuffer;\r\n        const framebuffer = renderTarget._MSAAFramebuffer || renderTarget._framebuffer;\r\n\r\n        if (renderTarget._depthStencilBuffer && renderTarget._depthStencilBuffer !== depthbuffer) {\r\n            gl.deleteRenderbuffer(renderTarget._depthStencilBuffer);\r\n        }\r\n        renderTarget._depthStencilBuffer = depthbuffer;\r\n        const attachment = renderTarget._generateStencilBuffer ? gl.DEPTH_STENCIL_ATTACHMENT : gl.DEPTH_ATTACHMENT;\r\n        this._engine._bindUnboundFramebuffer(framebuffer);\r\n        gl.framebufferRenderbuffer(gl.FRAMEBUFFER, attachment, gl.RENDERBUFFER, depthbuffer);\r\n        this._engine._bindUnboundFramebuffer(null);\r\n    }\r\n\r\n    /**\r\n     * Binds a texture to this render target on a specific attachment\r\n     * @param texture The texture to bind to the framebuffer\r\n     * @param attachmentIndex Index of the attachment\r\n     * @param faceIndexOrLayer The face or layer of the texture to render to in case of cube texture or array texture\r\n     * @param lodLevel defines the lod level to bind to the frame buffer\r\n     */\r\n    private _bindTextureRenderTarget(texture: InternalTexture, attachmentIndex: number = 0, faceIndexOrLayer?: number, lodLevel: number = 0) {\r\n        if (!texture._hardwareTexture) {\r\n            return;\r\n        }\r\n\r\n        const framebuffer = this._framebuffer;\r\n\r\n        const currentFB = this._engine._currentFramebuffer;\r\n        this._engine._bindUnboundFramebuffer(framebuffer);\r\n\r\n        if (this._engine.webGLVersion > 1) {\r\n            const gl = this._context as WebGL2RenderingContext;\r\n\r\n            const attachment = (<any>gl)[\"COLOR_ATTACHMENT\" + attachmentIndex];\r\n            if (texture.is2DArray || texture.is3D) {\r\n                faceIndexOrLayer = faceIndexOrLayer ?? this.layerIndices?.[attachmentIndex] ?? 0;\r\n                gl.framebufferTextureLayer(gl.FRAMEBUFFER, attachment, texture._hardwareTexture.underlyingResource, lodLevel, faceIndexOrLayer);\r\n            } else if (texture.isCube) {\r\n                // if face index is not specified, try to query it from faceIndices\r\n                // default is face 0\r\n                faceIndexOrLayer = faceIndexOrLayer ?? this.faceIndices?.[attachmentIndex] ?? 0;\r\n                gl.framebufferTexture2D(gl.FRAMEBUFFER, attachment, gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndexOrLayer, texture._hardwareTexture.underlyingResource, lodLevel);\r\n            } else {\r\n                gl.framebufferTexture2D(gl.FRAMEBUFFER, attachment, gl.TEXTURE_2D, texture._hardwareTexture.underlyingResource, lodLevel);\r\n            }\r\n        } else {\r\n            // Default behavior (WebGL)\r\n            const gl = this._context;\r\n\r\n            const attachment = (<any>gl)[\"COLOR_ATTACHMENT\" + attachmentIndex + \"_WEBGL\"];\r\n            const target = faceIndexOrLayer !== undefined ? gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndexOrLayer : gl.TEXTURE_2D;\r\n\r\n            gl.framebufferTexture2D(gl.FRAMEBUFFER, attachment, target, texture._hardwareTexture.underlyingResource, lodLevel);\r\n        }\r\n\r\n        this._engine._bindUnboundFramebuffer(currentFB);\r\n    }\r\n\r\n    /**\r\n     * Set a texture in the textures array\r\n     * @param texture the texture to set\r\n     * @param index the index in the textures array to set\r\n     * @param disposePrevious If this function should dispose the previous texture\r\n     */\r\n    public setTexture(texture: InternalTexture, index: number = 0, disposePrevious: boolean = true) {\r\n        super.setTexture(texture, index, disposePrevious);\r\n        this._bindTextureRenderTarget(texture, index);\r\n    }\r\n\r\n    /**\r\n     * Sets the layer and face indices of every render target texture\r\n     * @param layers The layer of the texture to be set (make negative to not modify)\r\n     * @param faces The face of the texture to be set (make negative to not modify)\r\n     */\r\n    public setLayerAndFaceIndices(layers: number[], faces: number[]) {\r\n        super.setLayerAndFaceIndices(layers, faces);\r\n\r\n        if (!this.textures || !this.layerIndices || !this.faceIndices) {\r\n            return;\r\n        }\r\n\r\n        // the length of this._attachments is the right one as it does not count the depth texture, in case we generated it\r\n        const textureCount = this._attachments?.length ?? this.textures.length;\r\n        for (let index = 0; index < textureCount; index++) {\r\n            const texture = this.textures[index];\r\n            if (!texture) {\r\n                // The target type was probably -1 at creation time and setTexture has not been called yet for this index\r\n                continue;\r\n            }\r\n            if (texture.is2DArray || texture.is3D) {\r\n                this._bindTextureRenderTarget(texture, index, this.layerIndices[index]);\r\n            } else if (texture.isCube) {\r\n                this._bindTextureRenderTarget(texture, index, this.faceIndices[index]);\r\n            } else {\r\n                this._bindTextureRenderTarget(texture, index);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the face and layer indices of a texture in the textures array\r\n     * @param index The index of the texture in the textures array to modify\r\n     * @param layer The layer of the texture to be set\r\n     * @param face The face of the texture to be set\r\n     */\r\n    public setLayerAndFaceIndex(index: number = 0, layer?: number, face?: number): void {\r\n        super.setLayerAndFaceIndex(index, layer, face);\r\n\r\n        if (!this.textures || !this.layerIndices || !this.faceIndices) {\r\n            return;\r\n        }\r\n\r\n        const texture = this.textures[index];\r\n        if (texture.is2DArray || texture.is3D) {\r\n            this._bindTextureRenderTarget(this.textures[index], index, this.layerIndices[index]);\r\n        } else if (texture.isCube) {\r\n            this._bindTextureRenderTarget(this.textures[index], index, this.faceIndices[index]);\r\n        }\r\n    }\r\n\r\n    public dispose(disposeOnlyFramebuffers = this._disposeOnlyFramebuffers): void {\r\n        const gl = this._context;\r\n\r\n        if (!disposeOnlyFramebuffers) {\r\n            if (this._colorTextureArray) {\r\n                this._context.deleteTexture(this._colorTextureArray);\r\n                this._colorTextureArray = null;\r\n            }\r\n            if (this._depthStencilTextureArray) {\r\n                this._context.deleteTexture(this._depthStencilTextureArray);\r\n                this._depthStencilTextureArray = null;\r\n            }\r\n        }\r\n\r\n        if (this._framebuffer) {\r\n            gl.deleteFramebuffer(this._framebuffer);\r\n            this._framebuffer = null;\r\n        }\r\n\r\n        if (this._depthStencilBuffer) {\r\n            gl.deleteRenderbuffer(this._depthStencilBuffer);\r\n            this._depthStencilBuffer = null;\r\n        }\r\n\r\n        if (this._MSAAFramebuffer) {\r\n            gl.deleteFramebuffer(this._MSAAFramebuffer);\r\n            this._MSAAFramebuffer = null;\r\n        }\r\n\r\n        super.dispose(disposeOnlyFramebuffers);\r\n    }\r\n}\r\n"]}