{"version": 3, "file": "trigonometryBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/trigonometryBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAGvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAGxD;;GAEG;AACH,MAAM,CAAN,IAAY,2BAqCX;AArCD,WAAY,2BAA2B;IACnC,UAAU;IACV,2EAAG,CAAA;IACH,UAAU;IACV,2EAAG,CAAA;IACH,UAAU;IACV,2EAAG,CAAA;IACH,UAAU;IACV,2EAAG,CAAA;IACH,WAAW;IACX,6EAAI,CAAA;IACJ,YAAY;IACZ,+EAAK,CAAA;IACL,YAAY;IACZ,+EAAK,CAAA;IACL,cAAc;IACd,mFAAO,CAAA;IACP,kBAAkB;IAClB,6EAAI,CAAA;IACJ,UAAU;IACV,2EAAG,CAAA;IACH,cAAc;IACd,4EAAG,CAAA;IACH,kBAAkB;IAClB,kFAAM,CAAA;IACN,kBAAkB;IAClB,kFAAM,CAAA;IACN,gBAAgB;IAChB,kFAAM,CAAA;IACN,eAAe;IACf,gFAAK,CAAA;IACL,WAAW;IACX,8EAAI,CAAA;IACJ,gCAAgC;IAChC,oFAAO,CAAA;IACP,gCAAgC;IAChC,oFAAO,CAAA;AACX,CAAC,EArCW,2BAA2B,KAA3B,2BAA2B,QAqCtC;AAED;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,iBAAiB;IAMpD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAVlD;;WAEG;QACI,cAAS,GAAG,2BAA2B,CAAC,GAAG,CAAC;QAS/C,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAC9E,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,YAAY,CAAC,CAAC;QAElF,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,SAAS,GAAG,EAAE,CAAC;QAEnB,QAAQ,IAAI,CAAC,SAAS,EAAE;YACpB,KAAK,2BAA2B,CAAC,GAAG,CAAC,CAAC;gBAClC,SAAS,GAAG,KAAK,CAAC;gBAClB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,GAAG,CAAC,CAAC;gBAClC,SAAS,GAAG,KAAK,CAAC;gBAClB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,GAAG,CAAC,CAAC;gBAClC,SAAS,GAAG,KAAK,CAAC;gBAClB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,GAAG,CAAC,CAAC;gBAClC,SAAS,GAAG,KAAK,CAAC;gBAClB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,IAAI,CAAC,CAAC;gBACnC,SAAS,GAAG,MAAM,CAAC;gBACnB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,KAAK,CAAC,CAAC;gBACpC,SAAS,GAAG,OAAO,CAAC;gBACpB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,KAAK,CAAC,CAAC;gBACpC,SAAS,GAAG,OAAO,CAAC;gBACpB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,OAAO,CAAC,CAAC;gBACtC,SAAS,GAAG,MAAM,CAAC;gBACnB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,IAAI,CAAC,CAAC;gBACnC,SAAS,GAAG,MAAM,CAAC;gBACnB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,GAAG,CAAC,CAAC;gBAClC,SAAS,GAAG,KAAK,CAAC;gBAClB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,GAAG,CAAC,CAAC;gBAClC,SAAS,GAAG,KAAK,CAAC;gBAClB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,MAAM,CAAC,CAAC;gBACrC,SAAS,GAAG,MAAM,CAAC;gBACnB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,MAAM,CAAC,CAAC;gBACrC,SAAS,GAAG,MAAM,CAAC;gBACnB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,MAAM,CAAC,CAAC;gBACrC,SAAS,GAAG,MAAM,CAAC;gBACnB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,KAAK,CAAC,CAAC;gBACpC,SAAS,GAAG,OAAO,CAAC;gBACpB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,IAAI,CAAC,CAAC;gBACnC,SAAS,GAAG,MAAM,CAAC;gBACnB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,OAAO,CAAC,CAAC;gBACtC,SAAS,GAAG,SAAS,CAAC;gBACtB,MAAM;aACT;YACD,KAAK,2BAA2B,CAAC,OAAO,CAAC,CAAC;gBACtC,SAAS,GAAG,SAAS,CAAC;gBACtB,MAAM;aACT;SACJ;QAED,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,MAAM,CAAC;QAE3H,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAE/C,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,SAAS,GAAG,mBAAmB,CAAC,SAAS,CAAC;IACnD,CAAC;IAES,mBAAmB;QACzB,MAAM,UAAU,GACZ,KAAK,CAAC,mBAAmB,EAAE,GAAG,GAAG,IAAI,CAAC,iBAAiB,oDAAoD,2BAA2B,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QAChK,OAAO,UAAU,CAAC;IACtB,CAAC;CACJ;AAED,aAAa,CAAC,2BAA2B,EAAE,iBAAiB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport type { Scene } from \"../../../scene\";\r\n\r\n/**\r\n * Operations supported by the Trigonometry block\r\n */\r\nexport enum TrigonometryBlockOperations {\r\n    /** Cos */\r\n    Cos,\r\n    /** Sin */\r\n    Sin,\r\n    /** Abs */\r\n    Abs,\r\n    /** Exp */\r\n    Exp,\r\n    /** Exp2 */\r\n    Exp2,\r\n    /** Round */\r\n    Round,\r\n    /** Floor */\r\n    Floor,\r\n    /** Ceiling */\r\n    Ceiling,\r\n    /** Square root */\r\n    Sqrt,\r\n    /** Log */\r\n    Log,\r\n    /** Tangent */\r\n    Tan,\r\n    /** Arc tangent */\r\n    ArcTan,\r\n    /** Arc cosinus */\r\n    ArcCos,\r\n    /** Arc sinus */\r\n    ArcSin,\r\n    /** Fraction */\r\n    Fract,\r\n    /** Sign */\r\n    Sign,\r\n    /** To radians (from degrees) */\r\n    Radians,\r\n    /** To degrees (from radians) */\r\n    Degrees,\r\n}\r\n\r\n/**\r\n * Block used to apply trigonometry operation to floats\r\n */\r\nexport class TrigonometryBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Gets or sets the operation applied by the block\r\n     */\r\n    public operation = TrigonometryBlockOperations.Cos;\r\n\r\n    /**\r\n     * Creates a new TrigonometryBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerInput(\"input\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.BasedOnInput);\r\n\r\n        this._outputs[0]._typeConnectionSource = this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"TrigonometryBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the input component\r\n     */\r\n    public get input(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const output = this._outputs[0];\r\n        let operation = \"\";\r\n\r\n        switch (this.operation) {\r\n            case TrigonometryBlockOperations.Cos: {\r\n                operation = \"cos\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Sin: {\r\n                operation = \"sin\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Abs: {\r\n                operation = \"abs\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Exp: {\r\n                operation = \"exp\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Exp2: {\r\n                operation = \"exp2\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Round: {\r\n                operation = \"round\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Floor: {\r\n                operation = \"floor\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Ceiling: {\r\n                operation = \"ceil\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Sqrt: {\r\n                operation = \"sqrt\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Log: {\r\n                operation = \"log\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Tan: {\r\n                operation = \"tan\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.ArcTan: {\r\n                operation = \"atan\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.ArcCos: {\r\n                operation = \"acos\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.ArcSin: {\r\n                operation = \"asin\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Fract: {\r\n                operation = \"fract\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Sign: {\r\n                operation = \"sign\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Radians: {\r\n                operation = \"radians\";\r\n                break;\r\n            }\r\n            case TrigonometryBlockOperations.Degrees: {\r\n                operation = \"degrees\";\r\n                break;\r\n            }\r\n        }\r\n\r\n        state.compilationString += this._declareOutput(output, state) + ` = ${operation}(${this.input.associatedVariableName});\\n`;\r\n\r\n        return this;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.operation = this.operation;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.operation = serializationObject.operation;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        const codeString =\r\n            super._dumpPropertiesCode() + `${this._codeVariableName}.operation = BABYLON.TrigonometryBlockOperations.${TrigonometryBlockOperations[this.operation]};\\n`;\r\n        return codeString;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.TrigonometryBlock\", TrigonometryBlock);\r\n"]}