{"version": 3, "file": "webXRCamera.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/XR/webXRCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAE/E,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAEvD,OAAO,EAAE,QAAQ,EAAE,MAAM,wBAAwB,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,kBAAkB,EAAE,MAAM,cAAc,CAAC;AAElD;;;GAGG;AACH,MAAM,OAAO,WAAY,SAAQ,UAAU;IA4CvC;;;;;OAKG;IACH,YACI,IAAY,EACZ,KAAY,EACJ,iBAAsC;QAE9C,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;QAF3B,sBAAiB,GAAjB,iBAAiB,CAAqB;QAlD1C,gBAAW,GAAG,KAAK,CAAC;QACpB,yBAAoB,GAAe,UAAU,CAAC,QAAQ,EAAE,CAAC;QACzD,wBAAmB,GAAY,IAAI,OAAO,EAAE,CAAC;QAC7C,mBAAc,GAAuB,kBAAkB,CAAC,YAAY,CAAC;QAE7E;;;WAGG;QACI,oCAA+B,GAAG,IAAI,UAAU,EAAe,CAAC;QAEvE;;;WAGG;QACI,2BAAsB,GAAG,IAAI,UAAU,EAAW,CAAC;QAE1D;;;WAGG;QACI,0BAAqB,GAAG,IAAI,UAAU,EAAW,CAAC;QAEzD;;;WAGG;QACI,2BAAsB,GAAG,IAAI,UAAU,EAAsB,CAAC;QAErE;;;WAGG;QACI,2BAAsB,GAAY,IAAI,CAAC;QA6KtC,eAAU,GAAG,IAAI,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAxJ5C,+BAA+B;QAC/B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;QAC3C,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,eAAe,CAAC;QAC5C,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QAClC,uDAAuD;QACvD,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACjD,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACrD,uEAAuE;YACvE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAC/C,IAAI,CAAC,iBAAiB,CAAC,mCAAmC,CAAC,GAAG,CAAC,GAAG,EAAE;gBAChE,yBAAyB;gBACzB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE;oBACtC,OAAO;iBACV;gBACD,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,+GAA+G;QAC/G,sEAAsE;QACtE,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAC1C,GAAG,EAAE;YACD,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,oBAAoB,EAAE,CAAC;aAC/B;YACD,IAAI,IAAI,CAAC,+BAA+B,CAAC,YAAY,EAAE,EAAE;gBACrD,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC3D,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,CAAC;aAChD;YAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBACrD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;aAC5E;YAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,EACD,SAAS,EACT,IAAI,CACP,CAAC;IACN,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAEO,iBAAiB,CAAC,QAA4B;QAClD,IAAI,IAAI,CAAC,cAAc,KAAK,QAAQ,EAAE;YAClC,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;YAC/B,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;SACzD;IACL,CAAC;IAED;;;;;OAKG;IACH,IAAW,eAAe;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;QACrJ,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,EAAE;YAChC,OAAO,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;SACpF;aAAM;YACH,OAAO,CAAC,CAAC;SACZ;IACL,CAAC;IAED,gBAAgB;IACT,0BAA0B,EAAC,wBAAwB;QACtD,6BAA6B;QAC7B,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC3D,sDAAsD;QACtD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC7C,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC7D,qDAAqD;QACrD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACjD,CAAC;IAED;;;;OAIG;IACI,gCAAgC,CAAC,cAAsB,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAa,EAAE,4BAAqC,IAAI;QAClI,IAAI,CAAC,WAAW,IAAI,WAAW,KAAK,IAAI,EAAE;YACtC,OAAO;SACV;QACD,MAAM,GAAG,GAAG,WAAW,CAAC,kBAAkB,EAAE,CAAC;QAC7C,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjE,uBAAuB;QACvB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QACpB,UAAU,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC1G,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,yBAAyB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;SAChD;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,MAAe;QAC5B,iCAAiC;QACjC,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACxC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAC/C,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;QAChB,SAAS,CAAC,SAAS,EAAE,CAAC;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;QACvD,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACtD,UAAU,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAClG,CAAC;IAEM,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;IACvC,CAAC;IAEO,mBAAmB;QACvB,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;QAC7E,MAAM,aAAa,GAAsB;YACrC,2GAA2G;YAC3G,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE,IAAI,CAAC,IAAI;SACvB,CAAC;QAEF,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC;IAC3B,CAAC;IAIO,oBAAoB;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAC7I,IAAI,CAAC,iBAAiB,GAAG,IAAI,IAAI,SAAS,CAAC;QAC3C,IAAI,CAAC,IAAI,EAAE;YACP,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;YACxD,OAAO;SACV;QAED,4DAA4D;QAC5D,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC,kBAAkB,CAAC,QAAQ,CAAC;QAC7G,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAEtC,yDAAyD;QACzD,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YAClE,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC9B;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;YAC/C,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC5C,oEAAoE;gBACpE,uBAAuB;gBACvB,OAAO;aACV;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;YACpC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAE1G,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC1F,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBACnC,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACjC,IAAI,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAClC,IAAI,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;aACrC;YAED,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,oFAAoF;gBACpF,wBAAwB;gBAExB,oDAAoD;gBACpD,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;gBAC9C,oDAAoD;gBACpD,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACxD;iBAAM;gBACH,4CAA4C;gBAC5C,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC5D,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;aACpD;SACJ;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAC9C,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SACrD;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAY,EAAE,CAAS,EAAE,EAAE;YAC3C,MAAM,UAAU,GAAiB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACpD,0CAA0C;YAC1C,IAAI,CAAC,UAAU,CAAC,YAAY,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE;gBACvD,IAAI,IAAI,CAAC,GAAG,KAAK,OAAO,EAAE;oBACtB,UAAU,CAAC,cAAc,GAAG,IAAI,CAAC;iBACpC;qBAAM,IAAI,IAAI,CAAC,GAAG,KAAK,MAAM,EAAE;oBAC5B,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC;iBACnC;aACJ;YACD,0EAA0E;YAC1E,MAAM,mBAAmB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,mBAAmB,CAAC;YAChE,iBAAiB;YACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACjD,MAAM,EAAE,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;gBAClC,sDAAsD;gBACtD,IAAI,UAAU,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;oBACnD,UAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBAC3C;aACJ;YACD,gCAAgC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;YAE/C,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAEhC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YACrG,UAAU,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC9F,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBACnC,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC5B,UAAU,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACtC,UAAU,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;aACzC;iBAAM;gBACH,UAAU,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAClE;YACD,MAAM,CAAC,2BAA2B,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAE9F,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBACnC,UAAU,CAAC,iBAAiB,CAAC,iCAAiC,EAAE,CAAC;aACpE;YAED,gBAAgB;YAChB,IAAI,CAAC,KAAK,CAAC,EAAE;gBACT,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;aACjE;YAED,MAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC;YACvF,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,EAAE,QAAQ,EAAE,WAAW,IAAI,KAAK,CAAC;YAC/E,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,qGAAqG;gBACrG,2EAA2E;gBAC3E,IAAI,CAAC,IAAI,CAAC,EAAE;oBACR,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;oBAClE,IAAI,CAAC,kBAAkB,GAAG,mBAAmB,CAAC;iBACjD;aACJ;iBAAM;gBACH,kBAAkB;gBAClB,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAExE,uDAAuD;gBACvD,UAAU,CAAC,kBAAkB,GAAG,mBAAmB,IAAI,IAAI,CAAC,iBAAiB,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC;aACrH;YAED,uCAAuC;YACvC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC1C,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,yBAAyB,CAAC,SAAS,GAAG,CAAC;QAC3C,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,SAAS,EAAE;YACvC,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC/G,SAAS,CAAC,IAAI,GAAG,GAAG,CAAC;YACrB,SAAS,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;YAChD,SAAS,CAAC,0BAA0B,GAAG,IAAI,CAAC;YAC5C,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;YAC7B,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;YAC3B,mDAAmD;YACnD,SAAS,CAAC,sBAAsB,EAAE,CAAC;YACnC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACnC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,SAAS,EAAE;YACvC,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YAC5C,IAAI,aAAa,EAAE;gBACf,aAAa,CAAC,OAAO,EAAE,CAAC;aAC3B;SACJ;IACL,CAAC;IAEO,qBAAqB;QACzB,iEAAiE;QACjE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE;YAC/G,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAE1C,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;YACpH,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACjG,aAAa,CAAC,MAAM,EAAE,CAAC,aAAa,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAC5D,YAAY,CAAC,MAAM,EAAE,CAAC;YAEtB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBACnC,YAAY,CAAC,4BAA4B,EAAE,CAAC;aAC/C;YAED,YAAY,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACvF,MAAM,SAAS,GAAG,IAAI,gBAAgB,CAClC;gBACI,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB;gBACzE,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB;gBACzE,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB;aAC5E,EACD;gBACI,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC9B,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC9B,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC9B,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;aACjC,CACJ,CAAC;YACF,IAAI,CAAC,iBAAiB,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;SACpH;IACL,CAAC;;AAjYc,0BAAc,GAAG,OAAO,CAAC,GAAG,EAAE,AAAhB,CAAiB", "sourcesContent": ["import { Vector3, <PERSON>, Quaternion, TmpVectors } from \"../Maths/math.vector\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Camera } from \"../Cameras/camera\";\r\nimport { FreeCamera } from \"../Cameras/freeCamera\";\r\nimport { TargetCamera } from \"../Cameras/targetCamera\";\r\nimport type { WebXRSessionManager } from \"./webXRSessionManager\";\r\nimport { Viewport } from \"../Maths/math.viewport\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { WebXRTrackingState } from \"./webXRTypes\";\r\n\r\n/**\r\n * WebXR Camera which holds the views for the xrSession\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/webXR/webXRCamera\r\n */\r\nexport class WebXRCamera extends FreeCamera {\r\n    private static _ScaleReadOnly = Vector3.One();\r\n\r\n    private _firstFrame = false;\r\n    private _referenceQuaternion: Quaternion = Quaternion.Identity();\r\n    private _referencedPosition: Vector3 = new Vector3();\r\n    private _trackingState: WebXRTrackingState = WebXRTrackingState.NOT_TRACKING;\r\n\r\n    /**\r\n     * This will be triggered after the first XR Frame initialized the camera,\r\n     * including the right number of views and their rendering parameters\r\n     */\r\n    public onXRCameraInitializedObservable = new Observable<WebXRCamera>();\r\n\r\n    /**\r\n     * Observable raised before camera teleportation\r\n     * @deprecated use onBeforeCameraTeleport of the teleportation feature instead\r\n     */\r\n    public onBeforeCameraTeleport = new Observable<Vector3>();\r\n\r\n    /**\r\n     *  Observable raised after camera teleportation\r\n     * @deprecated use onAfterCameraTeleport of the teleportation feature instead\r\n     */\r\n    public onAfterCameraTeleport = new Observable<Vector3>();\r\n\r\n    /**\r\n     * Notifies when the camera's tracking state has changed.\r\n     * Notice - will also be triggered when tracking has started (at the beginning of the session)\r\n     */\r\n    public onTrackingStateChanged = new Observable<WebXRTrackingState>();\r\n\r\n    /**\r\n     * Should position compensation execute on first frame.\r\n     * This is used when copying the position from a native (non XR) camera\r\n     */\r\n    public compensateOnFirstFrame: boolean = true;\r\n\r\n    /**\r\n     * The last XRViewerPose from the current XRFrame\r\n     * @internal\r\n     */\r\n    public _lastXRViewerPose?: XRViewerPose;\r\n\r\n    /**\r\n     * Creates a new webXRCamera, this should only be set at the camera after it has been updated by the xrSessionManager\r\n     * @param name the name of the camera\r\n     * @param scene the scene to add the camera to\r\n     * @param _xrSessionManager a constructed xr session manager\r\n     */\r\n    constructor(\r\n        name: string,\r\n        scene: Scene,\r\n        private _xrSessionManager: WebXRSessionManager\r\n    ) {\r\n        super(name, Vector3.Zero(), scene);\r\n\r\n        // Initial camera configuration\r\n        this.minZ = 0.1;\r\n        this.rotationQuaternion = new Quaternion();\r\n        this.cameraRigMode = Camera.RIG_MODE_CUSTOM;\r\n        this.updateUpVectorFromRotation = true;\r\n        this._updateNumberOfRigCameras(1);\r\n        // freeze projection matrix, which will be copied later\r\n        this.freezeProjectionMatrix();\r\n        this._deferOnly = true;\r\n\r\n        this._xrSessionManager.onXRSessionInit.add(() => {\r\n            this._referencedPosition.copyFromFloats(0, 0, 0);\r\n            this._referenceQuaternion.copyFromFloats(0, 0, 0, 1);\r\n            // first frame - camera's y position should be 0 for the correct offset\r\n            this._firstFrame = this.compensateOnFirstFrame;\r\n            this._xrSessionManager.onWorldScaleFactorChangedObservable.add(() => {\r\n                // only run if in session\r\n                if (!this._xrSessionManager.currentFrame) {\r\n                    return;\r\n                }\r\n                this._updateDepthNearFar();\r\n            });\r\n        });\r\n\r\n        // Check transformation changes on each frame. Callback is added to be first so that the transformation will be\r\n        // applied to the rest of the elements using the referenceSpace object\r\n        this._xrSessionManager.onXRFrameObservable.add(\r\n            () => {\r\n                if (this._firstFrame) {\r\n                    this._updateFromXRSession();\r\n                }\r\n                if (this.onXRCameraInitializedObservable.hasObservers()) {\r\n                    this.onXRCameraInitializedObservable.notifyObservers(this);\r\n                    this.onXRCameraInitializedObservable.clear();\r\n                }\r\n\r\n                if (this._deferredUpdated) {\r\n                    this.position.copyFrom(this._deferredPositionUpdate);\r\n                    this.rotationQuaternion.copyFrom(this._deferredRotationQuaternionUpdate);\r\n                }\r\n\r\n                this._updateReferenceSpace();\r\n                this._updateFromXRSession();\r\n            },\r\n            undefined,\r\n            true\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Get the current XR tracking state of the camera\r\n     */\r\n    public get trackingState(): WebXRTrackingState {\r\n        return this._trackingState;\r\n    }\r\n\r\n    private _setTrackingState(newState: WebXRTrackingState) {\r\n        if (this._trackingState !== newState) {\r\n            this._trackingState = newState;\r\n            this.onTrackingStateChanged.notifyObservers(newState);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Return the user's height, unrelated to the current ground.\r\n     * This will be the y position of this camera, when ground level is 0.\r\n     *\r\n     * Note - this value is multiplied by the worldScalingFactor (if set), so it will be in the same units as the scene.\r\n     */\r\n    public get realWorldHeight(): number {\r\n        const basePose = this._xrSessionManager.currentFrame && this._xrSessionManager.currentFrame.getViewerPose(this._xrSessionManager.baseReferenceSpace);\r\n        if (basePose && basePose.transform) {\r\n            return basePose.transform.position.y * this._xrSessionManager.worldScalingFactor;\r\n        } else {\r\n            return 0;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _updateForDualEyeDebugging(/*pupilDistance = 0.01*/) {\r\n        // Create initial camera rigs\r\n        this._updateNumberOfRigCameras(2);\r\n        this.rigCameras[0].viewport = new Viewport(0, 0, 0.5, 1.0);\r\n        // this.rigCameras[0].position.x = -pupilDistance / 2;\r\n        this.rigCameras[0].outputRenderTarget = null;\r\n        this.rigCameras[1].viewport = new Viewport(0.5, 0, 0.5, 1.0);\r\n        // this.rigCameras[1].position.x = pupilDistance / 2;\r\n        this.rigCameras[1].outputRenderTarget = null;\r\n    }\r\n\r\n    /**\r\n     * Sets this camera's transformation based on a non-vr camera\r\n     * @param otherCamera the non-vr camera to copy the transformation from\r\n     * @param resetToBaseReferenceSpace should XR reset to the base reference space\r\n     */\r\n    public setTransformationFromNonVRCamera(otherCamera: Camera = this.getScene().activeCamera!, resetToBaseReferenceSpace: boolean = true) {\r\n        if (!otherCamera || otherCamera === this) {\r\n            return;\r\n        }\r\n        const mat = otherCamera.computeWorldMatrix();\r\n        mat.decompose(undefined, this.rotationQuaternion, this.position);\r\n        // set the ground level\r\n        this.position.y = 0;\r\n        Quaternion.FromEulerAnglesToRef(0, this.rotationQuaternion.toEulerAngles().y, 0, this.rotationQuaternion);\r\n        this._firstFrame = true;\r\n        if (resetToBaseReferenceSpace) {\r\n            this._xrSessionManager.resetReferenceSpace();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the current instance class name (\"WebXRCamera\").\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"WebXRCamera\";\r\n    }\r\n\r\n    /**\r\n     * Set the target for the camera to look at.\r\n     * Note that this only rotates around the Y axis, as opposed to the default behavior of other cameras\r\n     * @param target the target to set the camera to look at\r\n     */\r\n    public setTarget(target: Vector3): void {\r\n        // only rotate around the y axis!\r\n        const tmpVector = TmpVectors.Vector3[1];\r\n        target.subtractToRef(this.position, tmpVector);\r\n        tmpVector.y = 0;\r\n        tmpVector.normalize();\r\n        const yRotation = Math.atan2(tmpVector.x, tmpVector.z);\r\n        this.rotationQuaternion.toEulerAnglesToRef(tmpVector);\r\n        Quaternion.FromEulerAnglesToRef(tmpVector.x, yRotation, tmpVector.z, this.rotationQuaternion);\r\n    }\r\n\r\n    public dispose() {\r\n        super.dispose();\r\n        this._lastXRViewerPose = undefined;\r\n    }\r\n\r\n    private _updateDepthNearFar() {\r\n        const far = (this.maxZ || 10000) * this._xrSessionManager.worldScalingFactor;\r\n        const xrRenderState: XRRenderStateInit = {\r\n            // if maxZ is 0 it should be \"Infinity\", but it doesn't work with the WebXR API. Setting to a large number.\r\n            depthFar: far,\r\n            depthNear: this.minZ,\r\n        };\r\n\r\n        this._xrSessionManager.updateRenderState(xrRenderState);\r\n        this._cache.minZ = this.minZ;\r\n        this._cache.maxZ = far;\r\n    }\r\n\r\n    private _rotate180 = new Quaternion(0, 1, 0, 0);\r\n\r\n    private _updateFromXRSession() {\r\n        const pose = this._xrSessionManager.currentFrame && this._xrSessionManager.currentFrame.getViewerPose(this._xrSessionManager.referenceSpace);\r\n        this._lastXRViewerPose = pose || undefined;\r\n        if (!pose) {\r\n            this._setTrackingState(WebXRTrackingState.NOT_TRACKING);\r\n            return;\r\n        }\r\n\r\n        // Set the tracking state. if it didn't change it is a no-op\r\n        const trackingState = pose.emulatedPosition ? WebXRTrackingState.TRACKING_LOST : WebXRTrackingState.TRACKING;\r\n        this._setTrackingState(trackingState);\r\n\r\n        // check min/max Z and update if not the same as in cache\r\n        if (this.minZ !== this._cache.minZ || this.maxZ !== this._cache.maxZ) {\r\n            this._updateDepthNearFar();\r\n        }\r\n\r\n        if (pose.transform) {\r\n            const orientation = pose.transform.orientation;\r\n            if (pose.transform.orientation.x === undefined) {\r\n                // Babylon native polyfill can return an undefined orientation value\r\n                // When not initialized\r\n                return;\r\n            }\r\n            const pos = pose.transform.position;\r\n            this._referencedPosition.set(pos.x, pos.y, pos.z).scaleInPlace(this._xrSessionManager.worldScalingFactor);\r\n\r\n            this._referenceQuaternion.set(orientation.x, orientation.y, orientation.z, orientation.w);\r\n            if (!this._scene.useRightHandedSystem) {\r\n                this._referencedPosition.z *= -1;\r\n                this._referenceQuaternion.z *= -1;\r\n                this._referenceQuaternion.w *= -1;\r\n            }\r\n\r\n            if (this._firstFrame) {\r\n                this._firstFrame = false;\r\n                // we have the XR reference, now use this to find the offset to get the camera to be\r\n                // in the right position\r\n\r\n                // set the height to correlate to the current height\r\n                this.position.y += this._referencedPosition.y;\r\n                // avoid using the head rotation on the first frame.\r\n                this._referenceQuaternion.copyFromFloats(0, 0, 0, 1);\r\n            } else {\r\n                // update position and rotation as reference\r\n                this.rotationQuaternion.copyFrom(this._referenceQuaternion);\r\n                this.position.copyFrom(this._referencedPosition);\r\n            }\r\n        }\r\n\r\n        // Update camera rigs\r\n        if (this.rigCameras.length !== pose.views.length) {\r\n            this._updateNumberOfRigCameras(pose.views.length);\r\n        }\r\n\r\n        pose.views.forEach((view: XRView, i: number) => {\r\n            const currentRig = <TargetCamera>this.rigCameras[i];\r\n            // update right and left, where applicable\r\n            if (!currentRig.isLeftCamera && !currentRig.isRightCamera) {\r\n                if (view.eye === \"right\") {\r\n                    currentRig._isRightCamera = true;\r\n                } else if (view.eye === \"left\") {\r\n                    currentRig._isLeftCamera = true;\r\n                }\r\n            }\r\n            // add any custom render targets to this camera, if available in the scene\r\n            const customRenderTargets = this.getScene().customRenderTargets;\r\n            // use a for loop\r\n            for (let i = 0; i < customRenderTargets.length; i++) {\r\n                const rt = customRenderTargets[i];\r\n                // make sure we don't add the same render target twice\r\n                if (currentRig.customRenderTargets.indexOf(rt) === -1) {\r\n                    currentRig.customRenderTargets.push(rt);\r\n                }\r\n            }\r\n            // Update view/projection matrix\r\n            const pos = view.transform.position;\r\n            const orientation = view.transform.orientation;\r\n\r\n            currentRig.parent = this.parent;\r\n\r\n            currentRig.position.set(pos.x, pos.y, pos.z).scaleInPlace(this._xrSessionManager.worldScalingFactor);\r\n            currentRig.rotationQuaternion.set(orientation.x, orientation.y, orientation.z, orientation.w);\r\n            if (!this._scene.useRightHandedSystem) {\r\n                currentRig.position.z *= -1;\r\n                currentRig.rotationQuaternion.z *= -1;\r\n                currentRig.rotationQuaternion.w *= -1;\r\n            } else {\r\n                currentRig.rotationQuaternion.multiplyInPlace(this._rotate180);\r\n            }\r\n            Matrix.FromFloat32ArrayToRefScaled(view.projectionMatrix, 0, 1, currentRig._projectionMatrix);\r\n\r\n            if (!this._scene.useRightHandedSystem) {\r\n                currentRig._projectionMatrix.toggleProjectionMatrixHandInPlace();\r\n            }\r\n\r\n            // first camera?\r\n            if (i === 0) {\r\n                this._projectionMatrix.copyFrom(currentRig._projectionMatrix);\r\n            }\r\n\r\n            const renderTargetTexture = this._xrSessionManager.getRenderTargetTextureForView(view);\r\n            this._renderingMultiview = renderTargetTexture?._texture?.isMultiview || false;\r\n            if (this._renderingMultiview) {\r\n                // For multiview, the render target texture is the same per-view (just the slice index is different),\r\n                // so we only need to set the output render target once for the rig parent.\r\n                if (i == 0) {\r\n                    this._xrSessionManager.trySetViewportForView(this.viewport, view);\r\n                    this.outputRenderTarget = renderTargetTexture;\r\n                }\r\n            } else {\r\n                // Update viewport\r\n                this._xrSessionManager.trySetViewportForView(currentRig.viewport, view);\r\n\r\n                // Set cameras to render to the session's render target\r\n                currentRig.outputRenderTarget = renderTargetTexture || this._xrSessionManager.getRenderTargetTextureForView(view);\r\n            }\r\n\r\n            // Replicate parent rig camera behavior\r\n            currentRig.layerMask = this.layerMask;\r\n        });\r\n    }\r\n\r\n    private _updateNumberOfRigCameras(viewCount = 1) {\r\n        while (this.rigCameras.length < viewCount) {\r\n            const newCamera = new TargetCamera(\"XR-RigCamera: \" + this.rigCameras.length, Vector3.Zero(), this.getScene());\r\n            newCamera.minZ = 0.1;\r\n            newCamera.rotationQuaternion = new Quaternion();\r\n            newCamera.updateUpVectorFromRotation = true;\r\n            newCamera.isRigCamera = true;\r\n            newCamera.rigParent = this;\r\n            // do not compute projection matrix, provided by XR\r\n            newCamera.freezeProjectionMatrix();\r\n            this.rigCameras.push(newCamera);\r\n        }\r\n        while (this.rigCameras.length > viewCount) {\r\n            const removedCamera = this.rigCameras.pop();\r\n            if (removedCamera) {\r\n                removedCamera.dispose();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _updateReferenceSpace() {\r\n        // were position & rotation updated OUTSIDE of the xr update loop\r\n        if (!this.position.equals(this._referencedPosition) || !this.rotationQuaternion.equals(this._referenceQuaternion)) {\r\n            const referencedMat = TmpVectors.Matrix[0];\r\n            const poseMat = TmpVectors.Matrix[1];\r\n            const transformMat = TmpVectors.Matrix[2];\r\n\r\n            Matrix.ComposeToRef(WebXRCamera._ScaleReadOnly, this._referenceQuaternion, this._referencedPosition, referencedMat);\r\n            Matrix.ComposeToRef(WebXRCamera._ScaleReadOnly, this.rotationQuaternion, this.position, poseMat);\r\n            referencedMat.invert().multiplyToRef(poseMat, transformMat);\r\n            transformMat.invert();\r\n\r\n            if (!this._scene.useRightHandedSystem) {\r\n                transformMat.toggleModelMatrixHandInPlace();\r\n            }\r\n\r\n            transformMat.decompose(undefined, this._referenceQuaternion, this._referencedPosition);\r\n            const transform = new XRRigidTransform(\r\n                {\r\n                    x: this._referencedPosition.x / this._xrSessionManager.worldScalingFactor,\r\n                    y: this._referencedPosition.y / this._xrSessionManager.worldScalingFactor,\r\n                    z: this._referencedPosition.z / this._xrSessionManager.worldScalingFactor,\r\n                },\r\n                {\r\n                    x: this._referenceQuaternion.x,\r\n                    y: this._referenceQuaternion.y,\r\n                    z: this._referenceQuaternion.z,\r\n                    w: this._referenceQuaternion.w,\r\n                }\r\n            );\r\n            this._xrSessionManager.referenceSpace = this._xrSessionManager.referenceSpace.getOffsetReferenceSpace(transform);\r\n        }\r\n    }\r\n}\r\n"]}