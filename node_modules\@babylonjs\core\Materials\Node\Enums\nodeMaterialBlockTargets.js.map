{"version": 3, "file": "nodeMaterialBlockTargets.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Enums/nodeMaterialBlockTargets.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,CAAN,IAAY,wBASX;AATD,WAAY,wBAAwB;IAChC,oBAAoB;IACpB,2EAAU,CAAA;IACV,sBAAsB;IACtB,+EAAY,CAAA;IACZ,cAAc;IACd,6EAAW,CAAA;IACX,0BAA0B;IAC1B,iGAAqC,CAAA;AACzC,CAAC,EATW,wBAAwB,KAAxB,wBAAwB,QASnC", "sourcesContent": ["/**\r\n * Enum used to define the target of a block\r\n */\r\nexport enum NodeMaterialBlockTargets {\r\n    /** Vertex shader */\r\n    Vertex = 1,\r\n    /** Fragment shader */\r\n    Fragment = 2,\r\n    /** Neutral */\r\n    Neutral = 4,\r\n    /** Vertex and Fragment */\r\n    VertexAndFragment = Vertex | Fragment,\r\n}\r\n"]}