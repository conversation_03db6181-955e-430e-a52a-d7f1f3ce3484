{"version": 3, "file": "reflectiveShadowMap.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Rendering/reflectiveShadowMap.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;AACH,OAAO,EAAE,SAAS,EAAE,gCAA+B;AACnD,OAAO,EAAE,iBAAiB,EAAE,mDAAkD;AAE9E,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,+BAA8B;AACvD,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,gCAA+B;AAI5D,OAAO,EAAE,kBAAkB,EAAE,2CAA0C;AAGvE,OAAO,EAAE,eAAe,EAAE,wCAAuC;AAEjE,OAAO,EAAE,eAAe,EAAE,4CAA2C;AACrE,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE,8BAA6B;AACnE,OAAO,EAAE,aAAa,EAAE,6BAA4B;AACpD,OAAO,EAAE,KAAK,EAAE,2BAA0B;AAG1C;;;GAGG;AACH,MAAM,OAAO,mBAAmB;IAW5B;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,KAAc;QAC5B,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;YACxB,OAAO;SACV;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IASD;;;;;OAKG;IACH,YAAY,KAAY,EAAE,KAAmC,EAAE,iBAAiB,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;QAxEtG,0BAAqB,GAAW,MAAM,CAAC,QAAQ,EAAE,CAAC;QAMlD,YAAO,GAAG,KAAK,CAAC;QAqDxB;;;;WAIG;QACI,+BAA0B,GAAG,KAAK,CAAC;QAStC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,0BAA0B,GAAG,IAAI,GAAG,EAAE,CAAC;QAC5C,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI,EAAE,iBAAiB,GAAG,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;QAEtE,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,CAAC,mCAAmC,EAAE,CAAC;QAE3C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,oBAAoB,CAAC,UAA6C;QACrE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;QAExC,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACrC,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEhC,UAAU,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACzB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,IAAmB;QAC9B,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAC5B;aAAM;YACH,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAChC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;SACN;QACD,IAAI,CAAC,mCAAmC,EAAE,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACI,qBAAqB;QACxB,IAAI,CAAC,mCAAmC,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB;QAChC,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAChC;QACD,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAES,wBAAwB;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QAE9B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC;QAE/C,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC,CAAC,SAAS,CAAC,sBAAsB,CAAC;QAC9I,MAAM,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC;QAErH,IAAI,CAAC,IAAI,GAAG,IAAI,iBAAiB,CAC7B,SAAS,GAAG,IAAI,EAChB,IAAI,CAAC,kBAAkB,EACvB,CAAC,EAAE,2CAA2C;QAC9C,IAAI,CAAC,MAAM,EACX;YACI,KAAK,EAAE,CAAC,SAAS,CAAC,sBAAsB,EAAE,SAAS,CAAC,uCAAuC,EAAE,eAAe,CAAC;YAC7G,aAAa,EAAE,CAAC,SAAS,CAAC,6BAA6B,EAAE,SAAS,CAAC,6BAA6B,EAAE,SAAS,CAAC,6BAA6B,CAAC;YAC1I,eAAe,EAAE,KAAK;YACtB,WAAW,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,UAAU,CAAC;YAC/E,OAAO,EAAE,CAAC,SAAS,CAAC,kBAAkB,EAAE,SAAS,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;SAC3F,EACD,CAAC,cAAc,GAAG,IAAI,EAAE,YAAY,GAAG,IAAI,EAAE,UAAU,GAAG,IAAI,CAAC,CAClE,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAEnC,IAAI,QAAuB,CAAC;QAC5B,IAAI,eAA8B,CAAC;QAEnC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,sBAAsB,CAAC;QAE9D,IAAI,MAAM,EAAE;YACR,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,yBAAyB,IAAI,IAAI,CAAC,CAAC;SACtF;QAED,IAAI,aAAsB,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,EAAE;YACtC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;YACtD,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,CAAC,8EAA8E;QACrH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,SAAiB,EAAE,EAAE;YACzD,IAAI,QAAQ,EAAE;gBACV,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;aAC/C;YACD,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YACxD,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,UAAU,IAAI,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC,CAAC;YACrH,IAAI,UAAU,IAAI,gBAAgB,EAAE;gBAChC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;aAChE;YACD,IAAI,MAAM,EAAE;gBACR,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,YAAY,EAAE,CAAC;gBACnD,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;aAClC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;YACvC,IAAI,QAAQ,EAAE;gBACV,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;aACtD;YACD,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,CAAC,4DAA4D;YACjG,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,aAAa,CAAC;YAC1C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,GAAI,IAAI,CAAC,IAAI,CAAC,YAA0C,CAAC,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,CAAC,CAAC;QAChI,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAES,mBAAmB,CAAC,GAAY;QACtC,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/D,IAAI,GAAG,EAAE;YACL,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;gBACZ,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACnD;SACJ;aAAM,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;SAClD;IACL,CAAC;IAES,mCAAmC;QACzC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;QAC/C,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,UAAU,IAAI,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC,CAAC;QACrH,IAAI,UAAU,IAAI,gBAAgB,EAAE;YAChC,UAAU,CAAC,aAAa,CAAC,gBAAgB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;SAC1E;IACL,CAAC;IAES,aAAa,CAAC,IAAkB;QACtC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAEjC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC5C,OAAO;SACV;QAED,IAAI,WAAW,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChE,IAAI,CAAC,WAAW,EAAE;YACd,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC;YACxE,IAAI,WAAW,EAAE;gBACb,iDAAiD;gBACjD,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,gBAAgB,EAAE;oBACjD,GAAG,EAAE;wBACD,OAAO,KAAK,CAAC;oBACjB,CAAC;oBACD,UAAU,EAAE,IAAI;oBAChB,YAAY,EAAE,IAAI;iBACrB,CAAC,CAAC;gBAEF,WAAmB,CAAC,eAAe,GAAG,IAAI,CAAC;gBAE5C,MAAM,eAAe,GAAG,IAAI,uBAAuB,CAAC,WAAW,CAAC,CAAC;gBAEjE,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC;gBACjC,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;gBAEpC,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;aAC9D;SACJ;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACzD,CAAC;IAES,yBAAyB;QAC/B,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,wBAAyB,SAAQ,eAAe;IAAtD;;QACW,cAAS,GAAG,KAAK,CAAC;QAClB,0BAAqB,GAAG,KAAK,CAAC;QAC9B,4BAAuB,GAAG,KAAK,CAAC;IAC3C,CAAC;CAAA;AAED;;GAEG;AACH,MAAM,OAAO,uBAAwB,SAAQ,kBAAkB;IAwBjD,gCAAgC;QACtC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,IAAI,CAAC,wCAAwC,EAAE,CAAC;IACpD,CAAC;IAID;;;OAGG;IACH,YAAY,QAAuD;QAC/D,KAAK,CAAC,QAAQ,EAAE,uBAAuB,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,wBAAwB,EAAE,CAAC,CAAC;QAlC/E,gBAAW,GAAG,IAAI,MAAM,EAAE,CAAC;QAC3B,0BAAqB,GAAG,KAAK,CAAC;QAa9B,eAAU,GAAG,KAAK,CAAC;QAC3B;;WAEG;QAGI,cAAS,GAAG,KAAK,CAAC;QAgBrB,IAAI,CAAC,wCAAwC,GAAG,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;QAE9G,IAAI,CAAC,cAAc,GAAG,QAAQ,YAAY,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC;IAClG,CAAC;IAEM,cAAc,CAAC,OAAiC;QACnD,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAEpC,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QAEnC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC,qBAAqB,CAAC;QACtE,IAAI,MAAM,EAAE;YACR,MAAM,IAAI,GAAG,IAAI,CAAC,KAAkB,CAAC;YACrC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;SAClG;QAED,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAC3D,OAAO,CAAC,uBAAuB,GAAG,MAAM,CAAC;IAC7C,CAAC;IAEM,YAAY;QACf,OAAO,yBAAyB,CAAC;IACrC,CAAC;IAEM,WAAW;QACd,OAAO;YACH,GAAG,EAAE;gBACD,EAAE,IAAI,EAAE,4BAA4B,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC9D,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC9C,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAChD,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;aACtD;YACD,QAAQ,EAAE;;;;;uBAKC;SACd,CAAC;IACN,CAAC;IAEM,WAAW,CAAC,QAAkB;QACjC,QAAQ,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;IACjD,CAAC;IAEM,cAAc,CAAC,aAA4B;QAC9C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO;SACV;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACjF,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC,qBAAqB,EAAE;YACxD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAkB,CAAC;YAErC,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,aAAa,CAAC,YAAY,CAAC,4BAA4B,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBACvF,aAAa,CAAC,UAAU,CAAC,6BAA6B,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;aACnF;YAED,MAAM,kBAAkB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAEjD,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE;gBACtC,aAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;gBACrJ,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;aAChE;iBAAM;gBACH,aAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACpH,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;aACrD;YAED,aAAa,CAAC,YAAY,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;SAC3I;IACL,CAAC;IAEM,aAAa,CAAC,UAAkB;QACnC,OAAO,UAAU,KAAK,QAAQ;YAC1B,CAAC,CAAC,IAAI;YACN,CAAC,CAAC;gBACI,gEAAgE;gBAChE,qBAAqB,EAAE;;;;aAI5B;gBAEK,gEAAgE;gBAChE,2BAA2B,EAAE;;;;;;;;aAQlC;gBAEK,gEAAgE;gBAChE,gCAAgC,EAAE;;sCAEd,IAAI,CAAC,cAAc;;;;;;;;;;;;;;;;;;aAkB5C;aACE,CAAC;IACZ,CAAC;;AAzJD;;GAEG;AACoB,4BAAI,GAAG,WAAW,AAAd,CAAe;AAMnC;IADN,SAAS,EAAE;sDAC+B;AAQpC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;0DAC5B;AA2I7B,aAAa,CAAC,iCAAiC,EAAE,uBAAuB,CAAC,CAAC", "sourcesContent": ["/**\r\n * Reflective Shadow Maps were first described in http://www.klayge.org/material/3_12/GI/rsm.pdf by <PERSON><PERSON> and <PERSON>\r\n * The ReflectiveShadowMap class only implements the position / normal / flux texture generation part.\r\n * For the global illumination effect, see the GIRSMManager class.\r\n */\r\nimport { Constants } from \"core/Engines/constants\";\r\nimport { MultiRenderTarget } from \"core/Materials/Textures/multiRenderTarget\";\r\nimport type { UniformBuffer } from \"core/Materials/uniformBuffer\";\r\nimport { Color3, Color4 } from \"core/Maths/math.color\";\r\nimport { Matrix, TmpVectors } from \"core/Maths/math.vector\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { Scene } from \"core/scene\";\r\nimport type { WebGPURenderTargetWrapper } from \"core/Engines/WebGPU/webgpuRenderTargetWrapper\";\r\nimport { MaterialPluginBase } from \"core/Materials/materialPluginBase\";\r\nimport type { Material } from \"core/Materials/material\";\r\nimport type { StandardMaterial } from \"core/Materials/standardMaterial\";\r\nimport { MaterialDefines } from \"core/Materials/materialDefines\";\r\nimport type { SpotLight } from \"core/Lights/spotLight\";\r\nimport { PBRBaseMaterial } from \"core/Materials/PBR/pbrBaseMaterial\";\r\nimport { expandToProperty, serialize } from \"core/Misc/decorators\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\nimport { Light } from \"core/Lights/light\";\r\nimport type { DirectionalLight } from \"core/Lights/directionalLight\";\r\n\r\n/**\r\n * Class used to generate the RSM (Reflective Shadow Map) textures for a given light.\r\n * The textures are: position (in world space), normal (in world space) and flux (light intensity)\r\n */\r\nexport class ReflectiveShadowMap {\r\n    private _scene: Scene;\r\n    private _light: DirectionalLight | SpotLight;\r\n    private _lightTransformMatrix: Matrix = Matrix.Identity();\r\n    private _mrt: MultiRenderTarget;\r\n    private _textureDimensions: { width: number; height: number };\r\n    private _regularMatToMatWithPlugin: Map<Material, Material>;\r\n    private _counters: Array<{ name: string; value: number }>;\r\n\r\n    private _enable = false;\r\n\r\n    /**\r\n     * Enables or disables the RSM generation.\r\n     */\r\n    public get enable() {\r\n        return this._enable;\r\n    }\r\n\r\n    public set enable(value: boolean) {\r\n        if (this._enable === value) {\r\n            return;\r\n        }\r\n\r\n        this._enable = value;\r\n        this._customRenderTarget(value);\r\n    }\r\n\r\n    /**\r\n     * Gets the position texture generated by the RSM process.\r\n     */\r\n    public get positionWorldTexture() {\r\n        return this._mrt.textures[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the normal texture generated by the RSM process.\r\n     */\r\n    public get normalWorldTexture() {\r\n        return this._mrt.textures[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the flux texture generated by the RSM process.\r\n     */\r\n    public get fluxTexture() {\r\n        return this._mrt.textures[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the render list used to generate the RSM textures.\r\n     */\r\n    public get renderList() {\r\n        return this._mrt.renderList;\r\n    }\r\n\r\n    /**\r\n     * Gets the light used to generate the RSM textures.\r\n     */\r\n    public get light() {\r\n        return this._light;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the light parameters should be recomputed even if the light parameters (position, direction) did not change.\r\n     * You should not set this value to true, except for debugging purpose (if you want to see changes from the inspector, for eg).\r\n     * Instead, you should call updateLightParameters() explicitely at the right time (once the light parameters changed).\r\n     */\r\n    public forceUpdateLightParameters = false;\r\n\r\n    /**\r\n     * Creates a new RSM for the given light.\r\n     * @param scene The scene\r\n     * @param light The light to use to generate the RSM textures\r\n     * @param textureDimensions The dimensions of the textures to generate. Default: \\{ width: 512, height: 512 \\}\r\n     */\r\n    constructor(scene: Scene, light: DirectionalLight | SpotLight, textureDimensions = { width: 512, height: 512 }) {\r\n        this._scene = scene;\r\n        this._light = light;\r\n        this._textureDimensions = textureDimensions;\r\n        this._regularMatToMatWithPlugin = new Map();\r\n        this._counters = [{ name: \"RSM Generation \" + light.name, value: 0 }];\r\n\r\n        this._createMultiRenderTarget();\r\n        this._recomputeLightTransformationMatrix();\r\n\r\n        this.enable = true;\r\n    }\r\n\r\n    /**\r\n     * Sets the dimensions of the textures to generate.\r\n     * @param dimensions The dimensions of the textures to generate.\r\n     */\r\n    public setTextureDimensions(dimensions: { width: number; height: number }) {\r\n        const renderList = this._mrt.renderList;\r\n\r\n        this._textureDimensions = dimensions;\r\n        this._disposeMultiRenderTarget();\r\n        this._createMultiRenderTarget();\r\n\r\n        renderList?.forEach((mesh) => {\r\n            this._addMeshToMRT(mesh);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Adds the given mesh to the render list used to generate the RSM textures.\r\n     * @param mesh The mesh to add to the render list used to generate the RSM textures. If not provided, all scene meshes will be added to the render list.\r\n     */\r\n    public addMesh(mesh?: AbstractMesh) {\r\n        if (mesh) {\r\n            this._addMeshToMRT(mesh);\r\n        } else {\r\n            this._scene.meshes.forEach((mesh) => {\r\n                this._addMeshToMRT(mesh);\r\n            });\r\n        }\r\n        this._recomputeLightTransformationMatrix();\r\n    }\r\n\r\n    /**\r\n     * Recomputes the light transformation matrix. Call this method if you manually changed the light position / direction / etc. and you want to update the RSM textures accordingly.\r\n     * You should also call this method if you add/remove meshes to/from the render list.\r\n     */\r\n    public updateLightParameters() {\r\n        this._recomputeLightTransformationMatrix();\r\n    }\r\n\r\n    /**\r\n     * Gets the light transformation matrix used to generate the RSM textures.\r\n     */\r\n    public get lightTransformationMatrix() {\r\n        if (this.forceUpdateLightParameters) {\r\n            this.updateLightParameters();\r\n        }\r\n        return this._lightTransformMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the GPU time spent to generate the RSM textures.\r\n     */\r\n    public get countersGPU(): Array<{ name: string; value: number }> {\r\n        return this._counters;\r\n    }\r\n\r\n    /**\r\n     * Disposes the RSM.\r\n     */\r\n    public dispose() {\r\n        this._disposeMultiRenderTarget();\r\n    }\r\n\r\n    protected _createMultiRenderTarget() {\r\n        const name = this._light.name;\r\n\r\n        const caps = this._scene.getEngine().getCaps();\r\n\r\n        const fluxTextureType = caps.rg11b10ufColorRenderable ? Constants.TEXTURETYPE_UNSIGNED_INT_10F_11F_11F_REV : Constants.TEXTURETYPE_HALF_FLOAT;\r\n        const fluxTextureFormat = caps.rg11b10ufColorRenderable ? Constants.TEXTUREFORMAT_RGB : Constants.TEXTUREFORMAT_RGBA;\r\n\r\n        this._mrt = new MultiRenderTarget(\r\n            \"RSMmrt_\" + name,\r\n            this._textureDimensions,\r\n            3, // number of RTT - position / normal / flux\r\n            this._scene,\r\n            {\r\n                types: [Constants.TEXTURETYPE_HALF_FLOAT, Constants.TEXTURETYPE_UNSIGNED_INT_2_10_10_10_REV, fluxTextureType],\r\n                samplingModes: [Constants.TEXTURE_BILINEAR_SAMPLINGMODE, Constants.TEXTURE_BILINEAR_SAMPLINGMODE, Constants.TEXTURE_BILINEAR_SAMPLINGMODE],\r\n                generateMipMaps: false,\r\n                targetTypes: [Constants.TEXTURE_2D, Constants.TEXTURE_2D, Constants.TEXTURE_2D],\r\n                formats: [Constants.TEXTUREFORMAT_RGBA, Constants.TEXTUREFORMAT_RGBA, fluxTextureFormat],\r\n            },\r\n            [\"RSMPosition_\" + name, \"RSMNormal_\" + name, \"RSMFlux_\" + name]\r\n        );\r\n\r\n        this._mrt.renderList = [];\r\n        this._mrt.clearColor = new Color4(0, 0, 0, 1);\r\n        this._mrt.noPrePassRenderer = true;\r\n\r\n        let sceneUBO: UniformBuffer;\r\n        let currentSceneUBO: UniformBuffer;\r\n\r\n        const useUBO = this._scene.getEngine().supportsUniformBuffers;\r\n\r\n        if (useUBO) {\r\n            sceneUBO = this._scene.createSceneUniformBuffer(`Scene for RSM (light \"${name}\")`);\r\n        }\r\n\r\n        let shadowEnabled: boolean;\r\n\r\n        this._mrt.onBeforeBindObservable.add(() => {\r\n            currentSceneUBO = this._scene.getSceneUniformBuffer();\r\n            shadowEnabled = this._light.shadowEnabled;\r\n            this._light.shadowEnabled = false; // we render from the light point of view, so we won't have any shadow anyway!\r\n        });\r\n\r\n        this._mrt.onBeforeRenderObservable.add((faceIndex: number) => {\r\n            if (sceneUBO) {\r\n                this._scene.setSceneUniformBuffer(sceneUBO);\r\n            }\r\n            const viewMatrix = this._light.getViewMatrix(faceIndex);\r\n            const projectionMatrix = this._light.getProjectionMatrix(viewMatrix || undefined, this._mrt.renderList || undefined);\r\n            if (viewMatrix && projectionMatrix) {\r\n                this._scene.setTransformMatrix(viewMatrix, projectionMatrix);\r\n            }\r\n            if (useUBO) {\r\n                this._scene.getSceneUniformBuffer().unbindEffect();\r\n                this._scene.finalizeSceneUbo();\r\n            }\r\n        });\r\n\r\n        this._mrt.onAfterUnbindObservable.add(() => {\r\n            if (sceneUBO) {\r\n                this._scene.setSceneUniformBuffer(currentSceneUBO);\r\n            }\r\n            this._scene.updateTransformMatrix(); // restore the view/projection matrices of the active camera\r\n            this._light.shadowEnabled = shadowEnabled;\r\n            this._counters[0].value = (this._mrt.renderTarget as WebGPURenderTargetWrapper).gpuTimeInFrame?.counter.lastSecAverage ?? 0;\r\n        });\r\n\r\n        this._customRenderTarget(true);\r\n    }\r\n\r\n    protected _customRenderTarget(add: boolean) {\r\n        const idx = this._scene.customRenderTargets.indexOf(this._mrt);\r\n        if (add) {\r\n            if (idx === -1) {\r\n                this._scene.customRenderTargets.push(this._mrt);\r\n            }\r\n        } else if (idx !== -1) {\r\n            this._scene.customRenderTargets.splice(idx, 1);\r\n        }\r\n    }\r\n\r\n    protected _recomputeLightTransformationMatrix() {\r\n        const viewMatrix = this._light.getViewMatrix();\r\n        const projectionMatrix = this._light.getProjectionMatrix(viewMatrix || undefined, this._mrt.renderList || undefined);\r\n        if (viewMatrix && projectionMatrix) {\r\n            viewMatrix.multiplyToRef(projectionMatrix, this._lightTransformMatrix);\r\n        }\r\n    }\r\n\r\n    protected _addMeshToMRT(mesh: AbstractMesh) {\r\n        this._mrt.renderList?.push(mesh);\r\n\r\n        const material = mesh.material;\r\n        if (mesh.getTotalVertices() === 0 || !material) {\r\n            return;\r\n        }\r\n\r\n        let rsmMaterial = this._regularMatToMatWithPlugin.get(material);\r\n        if (!rsmMaterial) {\r\n            rsmMaterial = material.clone(\"RSMCreate_\" + material.name) || undefined;\r\n            if (rsmMaterial) {\r\n                // Disable the prepass renderer for this material\r\n                Object.defineProperty(rsmMaterial, \"canRenderToMRT\", {\r\n                    get: function () {\r\n                        return false;\r\n                    },\r\n                    enumerable: true,\r\n                    configurable: true,\r\n                });\r\n\r\n                (rsmMaterial as any).disableLighting = true;\r\n\r\n                const rsmCreatePlugin = new RSMCreatePluginMaterial(rsmMaterial);\r\n\r\n                rsmCreatePlugin.isEnabled = true;\r\n                rsmCreatePlugin.light = this._light;\r\n\r\n                this._regularMatToMatWithPlugin.set(material, rsmMaterial);\r\n            }\r\n        }\r\n\r\n        this._mrt.setMaterialForRendering(mesh, rsmMaterial);\r\n    }\r\n\r\n    protected _disposeMultiRenderTarget() {\r\n        this._customRenderTarget(false);\r\n        this._mrt.dispose();\r\n    }\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nclass MaterialRSMCreateDefines extends MaterialDefines {\r\n    public RSMCREATE = false;\r\n    public RSMCREATE_PROJTEXTURE = false;\r\n    public RSMCREATE_LIGHT_IS_SPOT = false;\r\n}\r\n\r\n/**\r\n * Plugin that implements the creation of the RSM textures\r\n */\r\nexport class RSMCreatePluginMaterial extends MaterialPluginBase {\r\n    private _varAlbedoName: string;\r\n    private _lightColor = new Color3();\r\n    private _hasProjectionTexture = false;\r\n\r\n    /**\r\n     * Defines the name of the plugin.\r\n     */\r\n    public static readonly Name = \"RSMCreate\";\r\n\r\n    /**\r\n     * Defines the light that should be used to generate the RSM textures.\r\n     */\r\n    @serialize()\r\n    public light: DirectionalLight | SpotLight;\r\n\r\n    private _isEnabled = false;\r\n    /**\r\n     * Defines if the plugin is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isEnabled = false;\r\n\r\n    protected _markAllSubMeshesAsTexturesDirty(): void {\r\n        this._enable(this._isEnabled);\r\n        this._internalMarkAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    private _internalMarkAllSubMeshesAsTexturesDirty: () => void;\r\n\r\n    /**\r\n     * Create a new RSMCreatePluginMaterial\r\n     * @param material Parent material of the plugin\r\n     */\r\n    constructor(material: Material | StandardMaterial | PBRBaseMaterial) {\r\n        super(material, RSMCreatePluginMaterial.Name, 300, new MaterialRSMCreateDefines());\r\n\r\n        this._internalMarkAllSubMeshesAsTexturesDirty = material._dirtyCallbacks[Constants.MATERIAL_TextureDirtyFlag];\r\n\r\n        this._varAlbedoName = material instanceof PBRBaseMaterial ? \"surfaceAlbedo\" : \"baseColor.rgb\";\r\n    }\r\n\r\n    public prepareDefines(defines: MaterialRSMCreateDefines) {\r\n        defines.RSMCREATE = this._isEnabled;\r\n\r\n        this._hasProjectionTexture = false;\r\n\r\n        const isSpot = this.light.getTypeID() === Light.LIGHTTYPEID_SPOTLIGHT;\r\n        if (isSpot) {\r\n            const spot = this.light as SpotLight;\r\n            this._hasProjectionTexture = spot.projectionTexture ? spot.projectionTexture.isReady() : false;\r\n        }\r\n\r\n        defines.RSMCREATE_PROJTEXTURE = this._hasProjectionTexture;\r\n        defines.RSMCREATE_LIGHT_IS_SPOT = isSpot;\r\n    }\r\n\r\n    public getClassName() {\r\n        return \"RSMCreatePluginMaterial\";\r\n    }\r\n\r\n    public getUniforms() {\r\n        return {\r\n            ubo: [\r\n                { name: \"rsmTextureProjectionMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"rsmSpotInfo\", size: 4, type: \"vec4\" },\r\n                { name: \"rsmLightColor\", size: 3, type: \"vec3\" },\r\n                { name: \"rsmLightPosition\", size: 3, type: \"vec3\" },\r\n            ],\r\n            fragment: `#ifdef RSMCREATE\r\n                    uniform mat4 rsmTextureProjectionMatrix;\r\n                    uniform vec4 rsmSpotInfo;\r\n                    uniform vec3 rsmLightColor;\r\n                    unfiorm vec3 rsmLightPosition;\r\n                #endif`,\r\n        };\r\n    }\r\n\r\n    public getSamplers(samplers: string[]) {\r\n        samplers.push(\"rsmTextureProjectionSampler\");\r\n    }\r\n\r\n    public bindForSubMesh(uniformBuffer: UniformBuffer) {\r\n        if (!this._isEnabled) {\r\n            return;\r\n        }\r\n\r\n        this.light.diffuse.scaleToRef(this.light.getScaledIntensity(), this._lightColor);\r\n        uniformBuffer.updateColor3(\"rsmLightColor\", this._lightColor);\r\n\r\n        if (this.light.getTypeID() === Light.LIGHTTYPEID_SPOTLIGHT) {\r\n            const spot = this.light as SpotLight;\r\n\r\n            if (this._hasProjectionTexture) {\r\n                uniformBuffer.updateMatrix(\"rsmTextureProjectionMatrix\", spot.projectionTextureMatrix);\r\n                uniformBuffer.setTexture(\"rsmTextureProjectionSampler\", spot.projectionTexture);\r\n            }\r\n\r\n            const normalizeDirection = TmpVectors.Vector3[0];\r\n\r\n            if (spot.computeTransformedInformation()) {\r\n                uniformBuffer.updateFloat3(\"rsmLightPosition\", this.light.transformedPosition.x, this.light.transformedPosition.y, this.light.transformedPosition.z);\r\n                spot.transformedDirection.normalizeToRef(normalizeDirection);\r\n            } else {\r\n                uniformBuffer.updateFloat3(\"rsmLightPosition\", this.light.position.x, this.light.position.y, this.light.position.z);\r\n                spot.direction.normalizeToRef(normalizeDirection);\r\n            }\r\n\r\n            uniformBuffer.updateFloat4(\"rsmSpotInfo\", normalizeDirection.x, normalizeDirection.y, normalizeDirection.z, Math.cos(spot.angle * 0.5));\r\n        }\r\n    }\r\n\r\n    public getCustomCode(shaderType: string) {\r\n        return shaderType === \"vertex\"\r\n            ? null\r\n            : {\r\n                  // eslint-disable-next-line @typescript-eslint/naming-convention\r\n                  CUSTOM_FRAGMENT_BEGIN: `\r\n                #ifdef RSMCREATE\r\n                    #extension GL_EXT_draw_buffers : require\r\n                #endif\r\n            `,\r\n\r\n                  // eslint-disable-next-line @typescript-eslint/naming-convention\r\n                  CUSTOM_FRAGMENT_DEFINITIONS: `\r\n                #ifdef RSMCREATE\r\n                    #ifdef RSMCREATE_PROJTEXTURE\r\n                        uniform highp sampler2D rsmTextureProjectionSampler;                    \r\n                    #endif\r\n                    layout(location = 0) out highp vec4 glFragData[3];\r\n                    vec4 glFragColor;\r\n                #endif\r\n            `,\r\n\r\n                  // eslint-disable-next-line @typescript-eslint/naming-convention\r\n                  CUSTOM_FRAGMENT_BEFORE_FRAGCOLOR: `\r\n                #ifdef RSMCREATE\r\n                    vec3 rsmColor = ${this._varAlbedoName} * rsmLightColor;\r\n                    #ifdef RSMCREATE_PROJTEXTURE\r\n                    {\r\n                        vec4 strq = rsmTextureProjectionMatrix * vec4(vPositionW, 1.0);\r\n                        strq /= strq.w;\r\n                        rsmColor *= texture2D(rsmTextureProjectionSampler, strq.xy).rgb;\r\n                    }\r\n                    #endif\r\n                    #ifdef RSMCREATE_LIGHT_IS_SPOT\r\n                    {\r\n                        float cosAngle = max(0., dot(rsmSpotInfo.xyz, normalize(vPositionW - rsmLightPosition)));\r\n                        rsmColor = sign(cosAngle - rsmSpotInfo.w) * rsmColor;\r\n                    }\r\n                    #endif\r\n                    glFragData[0] = vec4(vPositionW, 1.);\r\n                    glFragData[1] = vec4(normalize(normalW) * 0.5 + 0.5, 1.);\r\n                    glFragData[2] = vec4(rsmColor, 1.);\r\n                #endif\r\n            `,\r\n              };\r\n    }\r\n}\r\n\r\nRegisterClass(`BABYLON.RSMCreatePluginMaterial`, RSMCreatePluginMaterial);\r\n"]}