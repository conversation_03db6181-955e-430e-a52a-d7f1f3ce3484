{"version": 3, "file": "nodeMaterialBlock.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Node/nodeMaterialBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,+CAA+C,CAAC;AAGtG,OAAO,EAAE,2BAA2B,EAAE,oCAAoC,EAAE,MAAM,oCAAoC,CAAC;AACvH,OAAO,EAAE,wBAAwB,EAAE,MAAM,kCAAkC,CAAC;AAO5E,OAAO,EAAE,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;AAEjE,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAEhD,OAAO,EAAE,MAAM,EAAE,6BAAyB;AAE1C;;GAEG;AACH,MAAM,OAAO,iBAAiB;IA4B1B;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,IAAW,IAAI,CAAC,OAAe;QAC3B,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE;YAClC,OAAO;SACV;QAED,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;IACzB,CAAC;IAYD;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,KAAa;QAC5B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,KAA+B;QAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE;YAC9B,OAAO;SACV;QACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,qCAAqC;IACrC,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,IAAY;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;QAE3D,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;SACpB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,IAAY;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;QAE5D,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;SACpB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAQD;;;;;OAKG;IACH,YAAmB,IAAY,EAAE,MAAM,GAAG,wBAAwB,CAAC,MAAM,EAAE,aAAa,GAAG,KAAK;QAtKxF,mBAAc,GAAG,KAAK,CAAC;QACvB,aAAQ,GAAG,KAAK,CAAC;QACjB,mBAAc,GAAG,KAAK,CAAC;QACvB,kBAAa,GAAG,KAAK,CAAC;QACtB,UAAK,GAAG,EAAE,CAAC;QACT,cAAS,GAAG,KAAK,CAAC;QAE5B,uFAAuF;QAChF,uBAAkB,GAAG,KAAK,CAAC;QAElC,gBAAgB;QACT,sBAAiB,GAAG,EAAE,CAAC;QAE9B,gBAAgB;QACT,YAAO,GAAG,IAAI,KAAK,EAA+B,CAAC;QAC1D,gBAAgB;QACT,aAAQ,GAAG,IAAI,KAAK,EAA+B,CAAC;QA+B3D;;WAEG;QACI,aAAQ,GAAW,EAAE,CAAC;QAwG7B,0GAA0G;QACnG,uBAAkB,GAAG,KAAK,CAAC;QAElC,6FAA6F;QACtF,mBAAc,GAAG,KAAK,CAAC;QAS1B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,wBAAwB,GAAG,MAAM,KAAK,wBAAwB,CAAC,OAAO,CAAC;QAC5E,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE,KAAK,YAAY,CAAC;QACrD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,EAAE,KAAK,8BAA8B,CAAC;QAC7E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,EAAE,KAAK,6BAA6B,CAAC;QAC3E,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC;IAC/C,CAAC;IAED,gBAAgB;IACT,iBAAiB,CAAC,MAAgC;QACrD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,wBAAoC,GAAG,MAAM,KAAK,wBAAwB,CAAC,OAAO,CAAC;IAC7F,CAAC;IAED;;;OAGG;IACH,6DAA6D;IACtD,UAAU,CAAC,KAA6B;QAC3C,aAAa;IACjB,CAAC;IAED;;;;;;OAMG;IACH,6DAA6D;IACtD,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAE,OAAiB;QAClF,aAAa;IACjB,CAAC;IAES,cAAc,CAAC,MAAmC,EAAE,KAA6B;QACvF,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,sBAAsB,EAAE,CAAC;IAC/E,CAAC;IAES,cAAc,CAAC,YAAyC;QAC9D,MAAM,eAAe,GAAG,YAAY,CAAC,cAAc,CAAC;QAEpD,IAAI,eAAe,EAAE;YACjB,OAAO,GAAG,YAAY,CAAC,sBAAsB,EAAE,CAAC;SACnD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,WAAW,CAAC,KAAa;QAC/B,IAAI,aAAa,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAErC,IAAI,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YACnC,aAAa,IAAI,IAAI,CAAC;SACzB;QACD,OAAO,GAAG,aAAa,EAAE,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,2BAA2B;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC;IACnE,CAAC;IAED;;;;;;;;OAQG;IACI,aAAa,CAChB,IAAY,EACZ,IAA2C,EAC3C,aAAsB,KAAK,EAC3B,MAAiC,EACjC,KAAmC;QAEnC,KAAK,GAAG,KAAK,IAAI,IAAI,2BAA2B,CAAC,IAAI,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,CAAC,CAAC;QACzG,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;QAC9B,IAAI,MAAM,EAAE;YACR,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;SACzB;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACI,cAAc,CAAC,IAAY,EAAE,IAA2C,EAAE,MAAiC,EAAE,KAAmC;QACnJ,KAAK,GAAG,KAAK,IAAI,IAAI,2BAA2B,CAAC,IAAI,EAAE,IAAI,EAAE,oCAAoC,CAAC,MAAM,CAAC,CAAC;QAC1G,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,MAAM,EAAE;YACR,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;SACzB;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE1B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,YAAmD,IAAI;QACjF,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;YAC9B,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,qCAAqC,CAAC,UAAU,EAAE;oBAChH,OAAO,KAAK,CAAC;iBAChB;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,WAAwC,IAAI;QACvE,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,wBAAwB,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAClI,OAAO,MAAM,CAAC;aACjB;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,OAAoC;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAE7C,IAAI,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YAC/C,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,KAAwB;QAC1C,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACtB,SAAS;aACZ;YAED,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE;gBACrC,IAAI,QAAQ,CAAC,UAAU,KAAK,KAAK,EAAE;oBAC/B,OAAO,IAAI,CAAC;iBACf;gBACD,IAAI,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC3C,OAAO,IAAI,CAAC;iBACf;aACJ;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;OAQG;IACI,SAAS,CACZ,KAAwB,EACxB,OAIC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,OAAO;SACV;QAED,IAAI,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QAEpH,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,OAAO,QAAQ,EAAE;YACb,MAAM,KAAK,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YAEpH,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;gBAC/C,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACxB,QAAQ,GAAG,KAAK,CAAC;aACpB;iBAAM,IAAI,CAAC,MAAM,EAAE;gBAChB,4CAA4C;gBAC5C,MAAM,mCAAmC,CAAC;aAC7C;iBAAM;gBACH,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;aAC1C;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,6DAA6D;IACnD,WAAW,CAAC,KAA6B;QAC/C,wCAAwC;IAC5C,CAAC;IAED;;;;;;OAMG;IACH,6DAA6D;IACtD,wBAAwB,CAAC,KAA6B,EAAE,YAA0B,EAAE,OAA4B,EAAE,cAAwB;QAC7I,aAAa;IACjB,CAAC;IAED;;;;OAIG;IACH,6DAA6D;IACtD,gBAAgB,CAAC,IAAkB,EAAE,SAA0B;QAClE,aAAa;IACjB,CAAC;IAED;;;;;;OAMG;IACH,6DAA6D;IACtD,iBAAiB,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B,EAAE,eAAwB,KAAK,IAAG,CAAC;IAExI;;;;;;;OAOG;IACH,6DAA6D;IACtD,cAAc,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B,EAAE,eAAwB,KAAK,EAAE,OAAiB;QAChJ,aAAa;IACjB,CAAC;IAED;;;;OAIG;IACH,6DAA6D;IACtD,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,EAAE,CAAC,IAAI;QACnH,aAAa;IACjB,CAAC;IAED;;;;;;OAMG;IACH,6DAA6D;IACtD,wBAAwB,CAAC,iBAAyC,EAAE,mBAA2C,EAAE,IAAkB,EAAE,OAA4B;QACpK,aAAa;IACjB,CAAC;IAED,mIAAmI;IACnI,IAAW,iDAAiD;QACxD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,iCAAiC,CAAC,EAAE;YAChE,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,EAAE;YACjD,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAwB,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAwB,CAAC,OAAO,EAAE;YAChH,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,yBAAyB,CAAC,EAAE;gBACxD,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;OAOG;IACH,6DAA6D;IACtD,OAAO,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B,EAAE,eAAwB,KAAK;QACtH,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,oBAAoB,CAAC,WAAmB,EAAE,WAAmB,EAAE,aAAa,GAAG,KAAK;QAC1F,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,4BAA4B,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SACtF;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SACjF;QACD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IAClF,CAAC;IAEO,aAAa,CAAC,KAAwB,EAAE,KAA6B,EAAE,KAAkC,EAAE,YAAiC;QAChJ,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAEjC,MAAM,oBAAoB,GAAG,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC;QACxD,MAAM,oCAAoC,GAAG,KAAK,CAAC,YAAY,KAAK,wBAAwB,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,iBAAiB,CAAC;QAEnK,IACI,oBAAoB;YACpB,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC;gBACtC,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;gBACnC,CAAC,IAAI,CAAC,MAAM,KAAK,wBAAwB,CAAC,iBAAiB,IAAI,oCAAoC,CAAC,CAAC,EAC3G;YACE,oCAAoC;YACpC,IACI,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,YAAY,CAAC,IAAI,6CAA6C;gBACxG,CAAC,KAAK,CAAC,OAAO,IAAK,KAAoB,CAAC,WAAW,IAAI,CAAE,KAAoB,CAAC,gBAAgB,CAAC,CAAC,wBAAwB;cAC1H;gBACE,MAAM,cAAc,GAAG,KAAK,CAAC,cAAe,CAAC;gBAC7C,IAAI,KAAK,CAAC,YAAY,CAAC,sBAAsB,CAAC,IAAI,GAAG,cAAc,CAAC,sBAAsB,EAAE,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE;oBAChI,KAAK,CAAC,YAAY,CAAC,iBAAiB,IAAI,GAAG,IAAI,GAAG,cAAc,CAAC,sBAAsB,MAAM,cAAc,CAAC,sBAAsB,KAAK,CAAC;iBAC3I;gBACD,KAAK,CAAC,sBAAsB,GAAG,IAAI,GAAG,cAAc,CAAC,sBAAsB,CAAC;gBAC5E,KAAK,CAAC,8BAA8B,GAAG,IAAI,CAAC;aAC/C;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,OAAe;QACpC,MAAM,aAAa,GAAkB;YACjC,UAAU;YACV,QAAQ;YACR,SAAS;YACT,oBAAoB;YACpB,IAAI;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,YAAY;YACZ,aAAa;YACb,iBAAiB;YACjB,iBAAiB;YACjB,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,gBAAgB;YAChB,sBAAsB;SACzB,CAAC;QACF,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;YACtC,IAAI,OAAO,KAAK,YAAY,EAAE;gBAC1B,OAAO,KAAK,CAAC;aAChB;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,6DAA6D;IACnD,gBAAgB,CAAC,KAA6B,EAAE,YAAiC;QACvF,kCAAkC;IACtC,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,KAA6B,EAAE,YAAiC;QACzE,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE;YAC5C,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,sBAAsB;YACtB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAChC,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE;oBAChC,MAAM,CAAC,sBAAsB,GAAG,KAAK,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBAC3E;aACJ;SACJ;QAED,wCAAwC;QACxC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;YAC9B,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;oBACnB,iBAAiB;oBACjB,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,6BAA6B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACrE;gBACD,SAAS;aACZ;YAED,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAwB,CAAC,OAAO,EAAE;gBAClD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAO,CAAC,KAAK,CAAC,EAAE;oBACrC,SAAS;iBACZ;gBAED,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAO,CAAC,KAAK,CAAC,EAAE;oBACtC,SAAS;iBACZ;aACJ;YAED,MAAM,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC;YAC9C,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,EAAE;gBACzB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;aACzD;SACJ;QAED,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAE3C,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE;YAC5C,OAAO,IAAI,CAAC,CAAC,gFAAgF;SAChG;QAED,OAAO;QACP,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE;YAC1B,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,iBAAiB,cAAc,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;SAC3J;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,QAAQ,KAAK,CAAC,MAAM,EAAE;gBAClB,KAAK,wBAAwB,CAAC,MAAM;oBAChC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;oBAC1C,MAAM;gBACV,KAAK,wBAAwB,CAAC,QAAQ;oBAClC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;oBAC5C,MAAM;aACb;SACJ;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,UAAU,CAAC,YAAY,EAAE;YAChD,KAAK,CAAC,iBAAiB,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC;SACnD;QAED,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAExB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC;QAEjC,2BAA2B;QAC3B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACtC,SAAS;aACZ;YAED,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE;gBACrC,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC;gBAElC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;oBACpF,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;iBAC5D;aACJ;SACJ;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAES,YAAY,CAAC,IAAY;QAC/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,aAAa,CAAC,IAAY;QAChC,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB;QACzB,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC5C,OAAO,GAAG,YAAY,yBAAyB,IAAI,CAAC,kBAAkB,MAAM,YAAY,qBAAqB,IAAI,CAAC,cAAc,MAAM,YAAY,aAAa,IAAI,CAAC,MAAM,KAAK,CAAC;IACpL,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,WAAqB,EAAE,aAAkC;QACtE,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzB,kBAAkB;QAClB,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QACjE,IAAI,CAAC,iBAAiB,GAAG,kBAAkB,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEzF,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE;YACpD,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,GAAG;gBACC,KAAK,EAAE,CAAC;gBACR,IAAI,CAAC,iBAAiB,GAAG,kBAAkB,GAAG,KAAK,CAAC;aACvD,QAAQ,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE;SAChE;QAED,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEzC,cAAc;QACd,IAAI,UAAU,GAAG,QAAQ,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC;QACjD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,UAAU,IAAI,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC;SACzC;QACD,UAAU,IAAI,OAAO,IAAI,CAAC,iBAAiB,kBAAkB,IAAI,CAAC,YAAY,EAAE,KAAK,IAAI,CAAC,IAAI,OAAO,CAAC;QAEtG,aAAa;QACb,UAAU,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEzC,SAAS;QACT,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;gBACpB,SAAS;aACZ;YAED,MAAM,eAAe,GAAG,KAAK,CAAC,cAAe,CAAC;YAC9C,MAAM,cAAc,GAAG,eAAe,CAAC,UAAU,CAAC;YAElD,IAAI,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC9C,UAAU,IAAI,cAAc,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;aACtE;SACJ;QAED,UAAU;QACV,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACtB,SAAS;aACZ;YAED,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE;gBACrC,MAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC;gBAC3C,IAAI,cAAc,IAAI,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE;oBAChE,UAAU,IAAI,cAAc,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;iBACtE;aACJ;SACJ;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,6BAA6B,CAAC,aAAkC;QACnE,IAAI,UAAU,GAAG,EAAE,CAAC;QAEpB,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACpC,OAAO,UAAU,CAAC;SACrB;QAED,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;gBACpB,SAAS;aACZ;YAED,MAAM,eAAe,GAAG,KAAK,CAAC,cAAe,CAAC;YAC9C,MAAM,cAAc,GAAG,eAAe,CAAC,UAAU,CAAC;YAElD,UAAU,IAAI,cAAc,CAAC,6BAA6B,CAAC,aAAa,CAAC,CAAC;YAC1E,UAAU,IAAI,GAAG,cAAc,CAAC,iBAAiB,IAAI,cAAc,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAC5J,KAAK,CAAC,IAAI,CACb,MAAM,CAAC;SACX;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,KAAY,EAAE,UAAkB,EAAE;QAC3C,MAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE7C,MAAM,SAAS,GAAG,QAAQ,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAC3D,IAAI,SAAS,EAAE;YACX,MAAM,KAAK,GAAsB,IAAI,SAAS,EAAE,CAAC;YACjD,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAExD,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QACpC,mBAAmB,CAAC,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAClE,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;QACvC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7C,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACjE,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzD,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAEzC,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAChC,mBAAmB,CAAC,OAAO,GAAG,EAAE,CAAC;QAEjC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC7B,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;SACtD;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;SAC7D;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,6DAA6D;IACtD,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC;QAC7C,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,mBAAmB,CAAC,kBAAkB,CAAC;QACnE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,mBAAmB,CAAC,cAAc,CAAC;QAC3D,IAAI,CAAC,OAAO,GAAG,mBAAmB,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;QACzD,IAAI,CAAC,6CAA6C,CAAC,mBAAmB,CAAC,CAAC;IAC5E,CAAC;IAEO,6CAA6C,CAAC,mBAAwB;QAC1E,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,MAAM,CAAC;QACpD,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,OAAO,CAAC;QACtD,IAAI,gBAAgB,EAAE;YAClB,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,CAAS,EAAE,EAAE;gBAC9C,IAAI,IAAI,CAAC,WAAW,EAAE;oBAClB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;iBACjD;gBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;oBACxD,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;iBACjE;YACL,CAAC,CAAC,CAAC;SACN;QACD,IAAI,iBAAiB,EAAE;YACnB,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,CAAS,EAAE,EAAE;gBAC/C,IAAI,IAAI,CAAC,WAAW,EAAE;oBAClB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;iBAClD;gBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;oBACzD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;iBAClE;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC7B,KAAK,CAAC,OAAO,EAAE,CAAC;SACnB;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,MAAM,CAAC,OAAO,EAAE,CAAC;SACpB;IACL,CAAC;CACJ", "sourcesContent": ["import { NodeMaterialBlockConnectionPointTypes } from \"./Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"./nodeMaterialBuildState\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { NodeMaterialConnectionPoint, NodeMaterialConnectionPointDirection } from \"./nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"./Enums/nodeMaterialBlockTargets\";\r\nimport type { Effect } from \"../effect\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"./nodeMaterial\";\r\nimport type { InputBlock } from \"./Blocks/Input/inputBlock\";\r\nimport { UniqueIdGenerator } from \"../../Misc/uniqueIdGenerator\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { GetClass } from \"../../Misc/typeStore\";\r\nimport type { EffectFallbacks } from \"../effectFallbacks\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * Defines a block that can be used inside a node based material\r\n */\r\nexport class NodeMaterialBlock {\r\n    private _buildId: number;\r\n    private _buildTarget: NodeMaterialBlockTargets;\r\n    protected _target: NodeMaterialBlockTargets;\r\n    private _isFinalMerger = false;\r\n    private _isInput = false;\r\n    private _isTeleportOut = false;\r\n    private _isTeleportIn = false;\r\n    private _name = \"\";\r\n    protected _isUnique = false;\r\n\r\n    /** Gets or sets a boolean indicating that only one input can be connected at a time */\r\n    public inputsAreExclusive = false;\r\n\r\n    /** @internal */\r\n    public _codeVariableName = \"\";\r\n\r\n    /** @internal */\r\n    public _inputs = new Array<NodeMaterialConnectionPoint>();\r\n    /** @internal */\r\n    public _outputs = new Array<NodeMaterialConnectionPoint>();\r\n\r\n    /** @internal */\r\n    public _preparationId: number;\r\n\r\n    /** @internal */\r\n    public readonly _originalTargetIsNeutral: boolean;\r\n\r\n    /**\r\n     * Gets the name of the block\r\n     */\r\n    public get name(): string {\r\n        return this._name;\r\n    }\r\n\r\n    /**\r\n     * Sets the name of the block. Will check if the name is valid.\r\n     */\r\n    public set name(newName: string) {\r\n        if (!this.validateBlockName(newName)) {\r\n            return;\r\n        }\r\n\r\n        this._name = newName;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the unique id of the node\r\n     */\r\n    public uniqueId: number;\r\n\r\n    /**\r\n     * Gets or sets the comments associated with this block\r\n     */\r\n    public comments: string = \"\";\r\n\r\n    /**\r\n     * Gets a boolean indicating that this block can only be used once per NodeMaterial\r\n     */\r\n    public get isUnique() {\r\n        return this._isUnique;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that this block is an end block (e.g. it is generating a system value)\r\n     */\r\n    public get isFinalMerger(): boolean {\r\n        return this._isFinalMerger;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that this block is an input (e.g. it sends data to the shader)\r\n     */\r\n    public get isInput(): boolean {\r\n        return this._isInput;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if this block is a teleport out\r\n     */\r\n    public get isTeleportOut(): boolean {\r\n        return this._isTeleportOut;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if this block is a teleport in\r\n     */\r\n    public get isTeleportIn(): boolean {\r\n        return this._isTeleportIn;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the build Id\r\n     */\r\n    public get buildId(): number {\r\n        return this._buildId;\r\n    }\r\n\r\n    public set buildId(value: number) {\r\n        this._buildId = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the target of the block\r\n     */\r\n    public get target() {\r\n        return this._target;\r\n    }\r\n\r\n    public set target(value: NodeMaterialBlockTargets) {\r\n        if ((this._target & value) !== 0) {\r\n            return;\r\n        }\r\n        this._target = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of input points\r\n     */\r\n    public get inputs(): NodeMaterialConnectionPoint[] {\r\n        return this._inputs;\r\n    }\r\n\r\n    /** Gets the list of output points */\r\n    public get outputs(): NodeMaterialConnectionPoint[] {\r\n        return this._outputs;\r\n    }\r\n\r\n    /**\r\n     * Find an input by its name\r\n     * @param name defines the name of the input to look for\r\n     * @returns the input or null if not found\r\n     */\r\n    public getInputByName(name: string) {\r\n        const filter = this._inputs.filter((e) => e.name === name);\r\n\r\n        if (filter.length) {\r\n            return filter[0];\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Find an output by its name\r\n     * @param name defines the name of the output to look for\r\n     * @returns the output or null if not found\r\n     */\r\n    public getOutputByName(name: string) {\r\n        const filter = this._outputs.filter((e) => e.name === name);\r\n\r\n        if (filter.length) {\r\n            return filter[0];\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating that this input can be edited in the Inspector (false by default) */\r\n    public visibleInInspector = false;\r\n\r\n    /** Gets or sets a boolean indicating that this input can be edited from a collapsed frame */\r\n    public visibleOnFrame = false;\r\n\r\n    /**\r\n     * Creates a new NodeMaterialBlock\r\n     * @param name defines the block name\r\n     * @param target defines the target of that block (Vertex by default)\r\n     * @param isFinalMerger defines a boolean indicating that this block is an end block (e.g. it is generating a system value). Default is false\r\n     */\r\n    public constructor(name: string, target = NodeMaterialBlockTargets.Vertex, isFinalMerger = false) {\r\n        this._target = target;\r\n        this._originalTargetIsNeutral = target === NodeMaterialBlockTargets.Neutral;\r\n        this._isFinalMerger = isFinalMerger;\r\n        this._isInput = this.getClassName() === \"InputBlock\";\r\n        this._isTeleportOut = this.getClassName() === \"NodeMaterialTeleportOutBlock\";\r\n        this._isTeleportIn = this.getClassName() === \"NodeMaterialTeleportInBlock\";\r\n        this._name = name;\r\n        this.uniqueId = UniqueIdGenerator.UniqueId;\r\n    }\r\n\r\n    /** @internal */\r\n    public _setInitialTarget(target: NodeMaterialBlockTargets): void {\r\n        this._target = target;\r\n        (this._originalTargetIsNeutral as boolean) = target === NodeMaterialBlockTargets.Neutral;\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public initialize(state: NodeMaterialBuildState) {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Bind data to effect. Will only be called for blocks with isBindable === true\r\n     * @param effect defines the effect to bind data to\r\n     * @param nodeMaterial defines the hosting NodeMaterial\r\n     * @param mesh defines the mesh that will be rendered\r\n     * @param subMesh defines the submesh that will be rendered\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh, subMesh?: SubMesh) {\r\n        // Do nothing\r\n    }\r\n\r\n    protected _declareOutput(output: NodeMaterialConnectionPoint, state: NodeMaterialBuildState): string {\r\n        return `${state._getGLType(output.type)} ${output.associatedVariableName}`;\r\n    }\r\n\r\n    protected _writeVariable(currentPoint: NodeMaterialConnectionPoint): string {\r\n        const connectionPoint = currentPoint.connectedPoint;\r\n\r\n        if (connectionPoint) {\r\n            return `${currentPoint.associatedVariableName}`;\r\n        }\r\n\r\n        return `0.`;\r\n    }\r\n\r\n    protected _writeFloat(value: number) {\r\n        let stringVersion = value.toString();\r\n\r\n        if (stringVersion.indexOf(\".\") === -1) {\r\n            stringVersion += \".0\";\r\n        }\r\n        return `${stringVersion}`;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name e.g. \"NodeMaterialBlock\"\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"NodeMaterialBlock\";\r\n    }\r\n\r\n    /** Gets a boolean indicating that this connection will be used in the fragment shader\r\n     * @returns true if connected in fragment shader\r\n     */\r\n    public isConnectedInFragmentShader() {\r\n        return this.outputs.some((o) => o.isConnectedInFragmentShader);\r\n    }\r\n\r\n    /**\r\n     * Register a new input. Must be called inside a block constructor\r\n     * @param name defines the connection point name\r\n     * @param type defines the connection point type\r\n     * @param isOptional defines a boolean indicating that this input can be omitted\r\n     * @param target defines the target to use to limit the connection point (will be VertexAndFragment by default)\r\n     * @param point an already created connection point. If not provided, create a new one\r\n     * @returns the current block\r\n     */\r\n    public registerInput(\r\n        name: string,\r\n        type: NodeMaterialBlockConnectionPointTypes,\r\n        isOptional: boolean = false,\r\n        target?: NodeMaterialBlockTargets,\r\n        point?: NodeMaterialConnectionPoint\r\n    ) {\r\n        point = point ?? new NodeMaterialConnectionPoint(name, this, NodeMaterialConnectionPointDirection.Input);\r\n        point.type = type;\r\n        point.isOptional = isOptional;\r\n        if (target) {\r\n            point.target = target;\r\n        }\r\n\r\n        this._inputs.push(point);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Register a new output. Must be called inside a block constructor\r\n     * @param name defines the connection point name\r\n     * @param type defines the connection point type\r\n     * @param target defines the target to use to limit the connection point (will be VertexAndFragment by default)\r\n     * @param point an already created connection point. If not provided, create a new one\r\n     * @returns the current block\r\n     */\r\n    public registerOutput(name: string, type: NodeMaterialBlockConnectionPointTypes, target?: NodeMaterialBlockTargets, point?: NodeMaterialConnectionPoint) {\r\n        point = point ?? new NodeMaterialConnectionPoint(name, this, NodeMaterialConnectionPointDirection.Output);\r\n        point.type = type;\r\n        if (target) {\r\n            point.target = target;\r\n        }\r\n\r\n        this._outputs.push(point);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Will return the first available input e.g. the first one which is not an uniform or an attribute\r\n     * @param forOutput defines an optional connection point to check compatibility with\r\n     * @returns the first available input or null\r\n     */\r\n    public getFirstAvailableInput(forOutput: Nullable<NodeMaterialConnectionPoint> = null) {\r\n        for (const input of this._inputs) {\r\n            if (!input.connectedPoint) {\r\n                if (!forOutput || forOutput.type === input.type || input.type === NodeMaterialBlockConnectionPointTypes.AutoDetect) {\r\n                    return input;\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Will return the first available output e.g. the first one which is not yet connected and not a varying\r\n     * @param forBlock defines an optional block to check compatibility with\r\n     * @returns the first available input or null\r\n     */\r\n    public getFirstAvailableOutput(forBlock: Nullable<NodeMaterialBlock> = null) {\r\n        for (const output of this._outputs) {\r\n            if (!forBlock || !forBlock.target || forBlock.target === NodeMaterialBlockTargets.Neutral || (forBlock.target & output.target) !== 0) {\r\n                return output;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets the sibling of the given output\r\n     * @param current defines the current output\r\n     * @returns the next output in the list or null\r\n     */\r\n    public getSiblingOutput(current: NodeMaterialConnectionPoint) {\r\n        const index = this._outputs.indexOf(current);\r\n\r\n        if (index === -1 || index >= this._outputs.length) {\r\n            return null;\r\n        }\r\n\r\n        return this._outputs[index + 1];\r\n    }\r\n\r\n    /**\r\n     * Checks if the current block is an ancestor of a given block\r\n     * @param block defines the potential descendant block to check\r\n     * @returns true if block is a descendant\r\n     */\r\n    public isAnAncestorOf(block: NodeMaterialBlock): boolean {\r\n        for (const output of this._outputs) {\r\n            if (!output.hasEndpoints) {\r\n                continue;\r\n            }\r\n\r\n            for (const endpoint of output.endpoints) {\r\n                if (endpoint.ownerBlock === block) {\r\n                    return true;\r\n                }\r\n                if (endpoint.ownerBlock.isAnAncestorOf(block)) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Connect current block with another block\r\n     * @param other defines the block to connect with\r\n     * @param options define the various options to help pick the right connections\r\n     * @param options.input\r\n     * @param options.output\r\n     * @param options.outputSwizzle\r\n     * @returns the current block\r\n     */\r\n    public connectTo(\r\n        other: NodeMaterialBlock,\r\n        options?: {\r\n            input?: string;\r\n            output?: string;\r\n            outputSwizzle?: string;\r\n        }\r\n    ) {\r\n        if (this._outputs.length === 0) {\r\n            return;\r\n        }\r\n\r\n        let output = options && options.output ? this.getOutputByName(options.output) : this.getFirstAvailableOutput(other);\r\n\r\n        let notFound = true;\r\n        while (notFound) {\r\n            const input = options && options.input ? other.getInputByName(options.input) : other.getFirstAvailableInput(output);\r\n\r\n            if (output && input && output.canConnectTo(input)) {\r\n                output.connectTo(input);\r\n                notFound = false;\r\n            } else if (!output) {\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"Unable to find a compatible match\";\r\n            } else {\r\n                output = this.getSiblingOutput(output);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        // Empty. Must be defined by child nodes\r\n    }\r\n\r\n    /**\r\n     * Add uniforms, samplers and uniform buffers at compilation time\r\n     * @param state defines the state to update\r\n     * @param nodeMaterial defines the node material requesting the update\r\n     * @param defines defines the material defines to update\r\n     * @param uniformBuffers defines the list of uniform buffer names\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public updateUniformsAndSamples(state: NodeMaterialBuildState, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines, uniformBuffers: string[]) {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Add potential fallbacks if shader compilation fails\r\n     * @param mesh defines the mesh to be rendered\r\n     * @param fallbacks defines the current prioritized list of fallbacks\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public provideFallbacks(mesh: AbstractMesh, fallbacks: EffectFallbacks) {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Initialize defines for shader compilation\r\n     * @param mesh defines the mesh to be rendered\r\n     * @param nodeMaterial defines the node material requesting the update\r\n     * @param defines defines the material defines to update\r\n     * @param useInstances specifies that instances should be used\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public initializeDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines, useInstances: boolean = false) {}\r\n\r\n    /**\r\n     * Update defines for shader compilation\r\n     * @param mesh defines the mesh to be rendered\r\n     * @param nodeMaterial defines the node material requesting the update\r\n     * @param defines defines the material defines to update\r\n     * @param useInstances specifies that instances should be used\r\n     * @param subMesh defines which submesh to render\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public prepareDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines, useInstances: boolean = false, subMesh?: SubMesh) {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Lets the block try to connect some inputs automatically\r\n     * @param material defines the hosting NodeMaterial\r\n     * @param additionalFilteringInfo optional additional filtering condition when looking for compatible blocks\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Function called when a block is declared as repeatable content generator\r\n     * @param vertexShaderState defines the current compilation state for the vertex shader\r\n     * @param fragmentShaderState defines the current compilation state for the fragment shader\r\n     * @param mesh defines the mesh to be rendered\r\n     * @param defines defines the material defines to update\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public replaceRepeatableContent(vertexShaderState: NodeMaterialBuildState, fragmentShaderState: NodeMaterialBuildState, mesh: AbstractMesh, defines: NodeMaterialDefines) {\r\n        // Do nothing\r\n    }\r\n\r\n    /** Gets a boolean indicating that the code of this block will be promoted to vertex shader even if connected to fragment output */\r\n    public get willBeGeneratedIntoVertexShaderFromFragmentShader(): boolean {\r\n        if (this.isInput || this.isFinalMerger) {\r\n            return false;\r\n        }\r\n\r\n        if (this._outputs.some((o) => o.isDirectlyConnectedToVertexOutput)) {\r\n            return false;\r\n        }\r\n\r\n        if (this.target === NodeMaterialBlockTargets.Vertex) {\r\n            return false;\r\n        }\r\n\r\n        if (this.target === NodeMaterialBlockTargets.VertexAndFragment || this.target === NodeMaterialBlockTargets.Neutral) {\r\n            if (this._outputs.some((o) => o.isConnectedInVertexShader)) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Checks if the block is ready\r\n     * @param mesh defines the mesh to be rendered\r\n     * @param nodeMaterial defines the node material requesting the update\r\n     * @param defines defines the material defines to update\r\n     * @param useInstances specifies that instances should be used\r\n     * @returns true if the block is ready\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public isReady(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines, useInstances: boolean = false) {\r\n        return true;\r\n    }\r\n\r\n    protected _linkConnectionTypes(inputIndex0: number, inputIndex1: number, looseCoupling = false) {\r\n        if (looseCoupling) {\r\n            this._inputs[inputIndex1]._acceptedConnectionPointType = this._inputs[inputIndex0];\r\n        } else {\r\n            this._inputs[inputIndex0]._linkedConnectionSource = this._inputs[inputIndex1];\r\n        }\r\n        this._inputs[inputIndex1]._linkedConnectionSource = this._inputs[inputIndex0];\r\n    }\r\n\r\n    private _processBuild(block: NodeMaterialBlock, state: NodeMaterialBuildState, input: NodeMaterialConnectionPoint, activeBlocks: NodeMaterialBlock[]) {\r\n        block.build(state, activeBlocks);\r\n\r\n        const localBlockIsFragment = state._vertexState != null;\r\n        const otherBlockWasGeneratedInVertexShader = block._buildTarget === NodeMaterialBlockTargets.Vertex && block.target !== NodeMaterialBlockTargets.VertexAndFragment;\r\n\r\n        if (\r\n            localBlockIsFragment &&\r\n            ((block.target & block._buildTarget) === 0 ||\r\n                (block.target & input.target) === 0 ||\r\n                (this.target !== NodeMaterialBlockTargets.VertexAndFragment && otherBlockWasGeneratedInVertexShader))\r\n        ) {\r\n            // context switch! We need a varying\r\n            if (\r\n                (!block.isInput && state.target !== block._buildTarget) || // block was already emitted by vertex shader\r\n                (block.isInput && (block as InputBlock).isAttribute && !(block as InputBlock)._noContextSwitch) // block is an attribute\r\n            ) {\r\n                const connectedPoint = input.connectedPoint!;\r\n                if (state._vertexState._emitVaryingFromString(\"v_\" + connectedPoint.associatedVariableName, state._getGLType(connectedPoint.type))) {\r\n                    state._vertexState.compilationString += `${\"v_\" + connectedPoint.associatedVariableName} = ${connectedPoint.associatedVariableName};\\n`;\r\n                }\r\n                input.associatedVariableName = \"v_\" + connectedPoint.associatedVariableName;\r\n                input._enforceAssociatedVariableName = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Validates the new name for the block node.\r\n     * @param newName the new name to be given to the node.\r\n     * @returns false if the name is a reserve word, else true.\r\n     */\r\n    public validateBlockName(newName: string) {\r\n        const reservedNames: Array<string> = [\r\n            \"position\",\r\n            \"normal\",\r\n            \"tangent\",\r\n            \"particle_positionw\",\r\n            \"uv\",\r\n            \"uv2\",\r\n            \"uv3\",\r\n            \"uv4\",\r\n            \"uv5\",\r\n            \"uv6\",\r\n            \"position2d\",\r\n            \"particle_uv\",\r\n            \"matricesIndices\",\r\n            \"matricesWeights\",\r\n            \"world0\",\r\n            \"world1\",\r\n            \"world2\",\r\n            \"world3\",\r\n            \"particle_color\",\r\n            \"particle_texturemask\",\r\n        ];\r\n        for (const reservedName of reservedNames) {\r\n            if (newName === reservedName) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _customBuildStep(state: NodeMaterialBuildState, activeBlocks: NodeMaterialBlock[]): void {\r\n        // Must be implemented by children\r\n    }\r\n\r\n    /**\r\n     * Compile the current node and generate the shader code\r\n     * @param state defines the current compilation state (uniforms, samplers, current string)\r\n     * @param activeBlocks defines the list of active blocks (i.e. blocks to compile)\r\n     * @returns true if already built\r\n     */\r\n    public build(state: NodeMaterialBuildState, activeBlocks: NodeMaterialBlock[]): boolean {\r\n        if (this._buildId === state.sharedData.buildId) {\r\n            return true;\r\n        }\r\n\r\n        if (!this.isInput) {\r\n            /** Prepare outputs */\r\n            for (const output of this._outputs) {\r\n                if (!output.associatedVariableName) {\r\n                    output.associatedVariableName = state._getFreeVariableName(output.name);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Check if \"parent\" blocks are compiled\r\n        for (const input of this._inputs) {\r\n            if (!input.connectedPoint) {\r\n                if (!input.isOptional) {\r\n                    // Emit a warning\r\n                    state.sharedData.checks.notConnectedNonOptionalInputs.push(input);\r\n                }\r\n                continue;\r\n            }\r\n\r\n            if (this.target !== NodeMaterialBlockTargets.Neutral) {\r\n                if ((input.target & this.target!) === 0) {\r\n                    continue;\r\n                }\r\n\r\n                if ((input.target & state.target!) === 0) {\r\n                    continue;\r\n                }\r\n            }\r\n\r\n            const block = input.connectedPoint.ownerBlock;\r\n            if (block && block !== this) {\r\n                this._processBuild(block, state, input, activeBlocks);\r\n            }\r\n        }\r\n\r\n        this._customBuildStep(state, activeBlocks);\r\n\r\n        if (this._buildId === state.sharedData.buildId) {\r\n            return true; // Need to check again as inputs can be connected multiple time to this endpoint\r\n        }\r\n\r\n        // Logs\r\n        if (state.sharedData.verbose) {\r\n            Logger.Log(`${state.target === NodeMaterialBlockTargets.Vertex ? \"Vertex shader\" : \"Fragment shader\"}: Building ${this.name} [${this.getClassName()}]`);\r\n        }\r\n\r\n        // Checks final outputs\r\n        if (this.isFinalMerger) {\r\n            switch (state.target) {\r\n                case NodeMaterialBlockTargets.Vertex:\r\n                    state.sharedData.checks.emitVertex = true;\r\n                    break;\r\n                case NodeMaterialBlockTargets.Fragment:\r\n                    state.sharedData.checks.emitFragment = true;\r\n                    break;\r\n            }\r\n        }\r\n\r\n        if (!this.isInput && state.sharedData.emitComments) {\r\n            state.compilationString += `\\n//${this.name}\\n`;\r\n        }\r\n\r\n        this._buildBlock(state);\r\n\r\n        this._buildId = state.sharedData.buildId;\r\n        this._buildTarget = state.target;\r\n\r\n        // Compile connected blocks\r\n        for (const output of this._outputs) {\r\n            if ((output.target & state.target) === 0) {\r\n                continue;\r\n            }\r\n\r\n            for (const endpoint of output.endpoints) {\r\n                const block = endpoint.ownerBlock;\r\n\r\n                if (block && (block.target & state.target) !== 0 && activeBlocks.indexOf(block) !== -1) {\r\n                    this._processBuild(block, state, endpoint, activeBlocks);\r\n                }\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected _inputRename(name: string) {\r\n        return name;\r\n    }\r\n\r\n    protected _outputRename(name: string) {\r\n        return name;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        const variableName = this._codeVariableName;\r\n        return `${variableName}.visibleInInspector = ${this.visibleInInspector};\\n${variableName}.visibleOnFrame = ${this.visibleOnFrame};\\n${variableName}.target = ${this.target};\\n`;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _dumpCode(uniqueNames: string[], alreadyDumped: NodeMaterialBlock[]) {\r\n        alreadyDumped.push(this);\r\n\r\n        // Get unique name\r\n        const nameAsVariableName = this.name.replace(/[^A-Za-z_]+/g, \"\");\r\n        this._codeVariableName = nameAsVariableName || `${this.getClassName()}_${this.uniqueId}`;\r\n\r\n        if (uniqueNames.indexOf(this._codeVariableName) !== -1) {\r\n            let index = 0;\r\n            do {\r\n                index++;\r\n                this._codeVariableName = nameAsVariableName + index;\r\n            } while (uniqueNames.indexOf(this._codeVariableName) !== -1);\r\n        }\r\n\r\n        uniqueNames.push(this._codeVariableName);\r\n\r\n        // Declaration\r\n        let codeString = `\\n// ${this.getClassName()}\\n`;\r\n        if (this.comments) {\r\n            codeString += `// ${this.comments}\\n`;\r\n        }\r\n        codeString += `var ${this._codeVariableName} = new BABYLON.${this.getClassName()}(\"${this.name}\");\\n`;\r\n\r\n        // Properties\r\n        codeString += this._dumpPropertiesCode();\r\n\r\n        // Inputs\r\n        for (const input of this.inputs) {\r\n            if (!input.isConnected) {\r\n                continue;\r\n            }\r\n\r\n            const connectedOutput = input.connectedPoint!;\r\n            const connectedBlock = connectedOutput.ownerBlock;\r\n\r\n            if (alreadyDumped.indexOf(connectedBlock) === -1) {\r\n                codeString += connectedBlock._dumpCode(uniqueNames, alreadyDumped);\r\n            }\r\n        }\r\n\r\n        // Outputs\r\n        for (const output of this.outputs) {\r\n            if (!output.hasEndpoints) {\r\n                continue;\r\n            }\r\n\r\n            for (const endpoint of output.endpoints) {\r\n                const connectedBlock = endpoint.ownerBlock;\r\n                if (connectedBlock && alreadyDumped.indexOf(connectedBlock) === -1) {\r\n                    codeString += connectedBlock._dumpCode(uniqueNames, alreadyDumped);\r\n                }\r\n            }\r\n        }\r\n\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _dumpCodeForOutputConnections(alreadyDumped: NodeMaterialBlock[]) {\r\n        let codeString = \"\";\r\n\r\n        if (alreadyDumped.indexOf(this) !== -1) {\r\n            return codeString;\r\n        }\r\n\r\n        alreadyDumped.push(this);\r\n\r\n        for (const input of this.inputs) {\r\n            if (!input.isConnected) {\r\n                continue;\r\n            }\r\n\r\n            const connectedOutput = input.connectedPoint!;\r\n            const connectedBlock = connectedOutput.ownerBlock;\r\n\r\n            codeString += connectedBlock._dumpCodeForOutputConnections(alreadyDumped);\r\n            codeString += `${connectedBlock._codeVariableName}.${connectedBlock._outputRename(connectedOutput.name)}.connectTo(${this._codeVariableName}.${this._inputRename(\r\n                input.name\r\n            )});\\n`;\r\n        }\r\n\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Clone the current block to a new identical block\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a copy of the current block\r\n     */\r\n    public clone(scene: Scene, rootUrl: string = \"\") {\r\n        const serializationObject = this.serialize();\r\n\r\n        const blockType = GetClass(serializationObject.customType);\r\n        if (blockType) {\r\n            const block: NodeMaterialBlock = new blockType();\r\n            block._deserialize(serializationObject, scene, rootUrl);\r\n\r\n            return block;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Serializes this block in a JSON representation\r\n     * @returns the serialized block object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n        serializationObject.customType = \"BABYLON.\" + this.getClassName();\r\n        serializationObject.id = this.uniqueId;\r\n        serializationObject.name = this.name;\r\n        serializationObject.comments = this.comments;\r\n        serializationObject.visibleInInspector = this.visibleInInspector;\r\n        serializationObject.visibleOnFrame = this.visibleOnFrame;\r\n        serializationObject.target = this.target;\r\n\r\n        serializationObject.inputs = [];\r\n        serializationObject.outputs = [];\r\n\r\n        for (const input of this.inputs) {\r\n            serializationObject.inputs.push(input.serialize());\r\n        }\r\n\r\n        for (const output of this.outputs) {\r\n            serializationObject.outputs.push(output.serialize(false));\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        this.name = serializationObject.name;\r\n        this.comments = serializationObject.comments;\r\n        this.visibleInInspector = !!serializationObject.visibleInInspector;\r\n        this.visibleOnFrame = !!serializationObject.visibleOnFrame;\r\n        this._target = serializationObject.target ?? this.target;\r\n        this._deserializePortDisplayNamesAndExposedOnFrame(serializationObject);\r\n    }\r\n\r\n    private _deserializePortDisplayNamesAndExposedOnFrame(serializationObject: any) {\r\n        const serializedInputs = serializationObject.inputs;\r\n        const serializedOutputs = serializationObject.outputs;\r\n        if (serializedInputs) {\r\n            serializedInputs.forEach((port: any, i: number) => {\r\n                if (port.displayName) {\r\n                    this.inputs[i].displayName = port.displayName;\r\n                }\r\n                if (port.isExposedOnFrame) {\r\n                    this.inputs[i].isExposedOnFrame = port.isExposedOnFrame;\r\n                    this.inputs[i].exposedPortPosition = port.exposedPortPosition;\r\n                }\r\n            });\r\n        }\r\n        if (serializedOutputs) {\r\n            serializedOutputs.forEach((port: any, i: number) => {\r\n                if (port.displayName) {\r\n                    this.outputs[i].displayName = port.displayName;\r\n                }\r\n                if (port.isExposedOnFrame) {\r\n                    this.outputs[i].isExposedOnFrame = port.isExposedOnFrame;\r\n                    this.outputs[i].exposedPortPosition = port.exposedPortPosition;\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Release resources\r\n     */\r\n    public dispose() {\r\n        for (const input of this.inputs) {\r\n            input.dispose();\r\n        }\r\n\r\n        for (const output of this.outputs) {\r\n            output.dispose();\r\n        }\r\n    }\r\n}\r\n"]}