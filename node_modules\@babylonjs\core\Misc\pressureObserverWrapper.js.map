{"version": 3, "file": "pressureObserverWrapper.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/pressureObserverWrapper.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C;;GAEG;AACH,MAAM,OAAO,uBAAuB;IAUhC;;;OAGG;IACH,YAAY,OAAiC;QAbrC,cAAS,GAA+B,IAAI,CAAC;QAC7C,kBAAa,GAAqB,EAAE,CAAC;QAE7C;;;WAGG;QACI,sBAAiB,GAAG,IAAI,UAAU,EAAoB,CAAC;QAO1D,IAAI,uBAAuB,CAAC,WAAW,EAAE;YACrC,IAAI,CAAC,SAAS,GAAG,IAAI,gBAAgB,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC7C,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;gBAC5B,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YACnD,CAAC,EAAE,OAAO,CAAC,CAAC;SACf;IACL,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,WAAW;QACzB,OAAO,OAAO,gBAAgB,KAAK,WAAW,IAAI,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACxG,CAAC;IAED;;;OAGG;IACH,OAAO,CAAC,MAAsB;QAC1B,IAAI;YACA,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAChC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAC9D;QAAC,MAAM;YACJ,eAAe;SAClB;IACL,CAAC;IAED;;;OAGG;IACH,SAAS,CAAC,MAAsB;QAC5B,IAAI;YACA,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;SACrC;QAAC,MAAM;YACJ,eAAe;SAClB;IACL,CAAC;IAED;;OAEG;IACH,OAAO;QACH,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;IACnC,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Observable } from \"./observable\";\r\n\r\n/**\r\n * A wrapper for the experimental pressure api which allows a callback to be called whenever certain thresholds are met.\r\n */\r\nexport class PressureObserverWrapper {\r\n    private _observer: Nullable<PressureObserver> = null;\r\n    private _currentState: PressureRecord[] = [];\r\n\r\n    /**\r\n     * An event triggered when the cpu usage/speed meets certain thresholds.\r\n     * Note: pressure is an experimental API.\r\n     */\r\n    public onPressureChanged = new Observable<PressureRecord[]>();\r\n\r\n    /**\r\n     * A pressure observer will call this callback, whenever these thresholds are met.\r\n     * @param options An object containing the thresholds used to decide what value to to return for each update property (average of start and end of a threshold boundary).\r\n     */\r\n    constructor(options?: PressureObserverOptions) {\r\n        if (PressureObserverWrapper.IsAvailable) {\r\n            this._observer = new PressureObserver((update) => {\r\n                this._currentState = update;\r\n                this.onPressureChanged.notifyObservers(update);\r\n            }, options);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns true if PressureObserver is available for use, false otherwise.\r\n     */\r\n    public static get IsAvailable() {\r\n        return typeof PressureObserver !== \"undefined\" && PressureObserver.supportedSources.includes(\"cpu\");\r\n    }\r\n\r\n    /**\r\n     * Method that must be called to begin observing changes, and triggering callbacks.\r\n     * @param source defines the source to observe\r\n     */\r\n    observe(source: PressureSource): void {\r\n        try {\r\n            this._observer?.observe(source);\r\n            this.onPressureChanged.notifyObservers(this._currentState);\r\n        } catch {\r\n            // Ignore error\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Method that must be called to stop observing changes and triggering callbacks (cleanup function).\r\n     * @param source defines the source to unobserve\r\n     */\r\n    unobserve(source: PressureSource): void {\r\n        try {\r\n            this._observer?.unobserve(source);\r\n        } catch {\r\n            // Ignore error\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Release the associated resources.\r\n     */\r\n    dispose() {\r\n        this._observer?.disconnect();\r\n        this._observer = null;\r\n        this.onPressureChanged.clear();\r\n    }\r\n}\r\n"]}