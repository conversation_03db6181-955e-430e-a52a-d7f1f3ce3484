{"version": 3, "file": "shaderCodeTestNode.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Processors/shaderCodeTestNode.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAGlD,gBAAgB;AAChB,MAAM,OAAO,kBAAmB,SAAQ,cAAc;IAG3C,OAAO,CAAC,aAAwC;QACnD,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACrD,CAAC;CACJ", "sourcesContent": ["import { ShaderCodeNode } from \"./shaderCodeNode\";\r\nimport type { ShaderDefineExpression } from \"./Expressions/shaderDefineExpression\";\r\n\r\n/** @internal */\r\nexport class ShaderCodeTestNode extends ShaderCodeNode {\r\n    public testExpression: ShaderDefineExpression;\r\n\r\n    public isValid(preprocessors: { [key: string]: string }) {\r\n        return this.testExpression.isTrue(preprocessors);\r\n    }\r\n}\r\n"]}