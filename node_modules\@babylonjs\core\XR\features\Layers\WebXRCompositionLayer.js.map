{"version": 3, "file": "WebXRCompositionLayer.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/XR/features/Layers/WebXRCompositionLayer.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,UAAU,EAAE,oCAA6B;AAElD,OAAO,EAAE,iBAAiB,EAAE,mCAAkC;AAC9D,OAAO,EAAE,qCAAqC,EAAE,kDAAiD;AAIjG;;;GAGG;AACH,MAAM,OAAO,4BAA6B,SAAQ,iBAAiB;IAC/D,YACW,QAAsB,EACtB,SAAuB,EACd,KAAyB,EACzB,SAAyB,EACzB,WAAoB,EAC7B,iBAAmG,EACnG,2BAAsD,IAAI;QAEjE,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;QARzD,aAAQ,GAAR,QAAQ,CAAc;QACtB,cAAS,GAAT,SAAS,CAAc;QACd,UAAK,GAAL,KAAK,CAAoB;QACzB,cAAS,GAAT,SAAS,CAAgB;QACzB,gBAAW,GAAX,WAAW,CAAS;QAC7B,sBAAiB,GAAjB,iBAAiB,CAAkF;QACnG,6BAAwB,GAAxB,wBAAwB,CAAkC;IAGrE,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,gDAAiD,SAAQ,qCAAqC;IAQvG,YACuB,iBAAsC,EACtC,eAA+B,EAClC,YAA0C;QAE1D,KAAK,CAAC,iBAAiB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAJ1B,sBAAiB,GAAjB,iBAAiB,CAAqB;QACtC,oBAAe,GAAf,eAAe,CAAgB;QAClC,iBAAY,GAAZ,YAAY,CAA8B;QAVpD,mBAAc,GAAG,IAAI,GAAG,EAA0B,CAAC;QAE7D;;WAEG;QACI,2CAAsC,GAAG,IAAI,UAAU,EAAiD,CAAC;QAQ5G,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,KAAK,CAAC;IAChD,CAAC;IAES,2BAA2B,CAAC,QAAyB,EAAE,MAAa,MAAM;QAChF,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClD,MAAM,QAAQ,GAAG,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAExC,MAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,IAAI,QAAQ,CAAC,YAAY,CAAC;QAC9E,MAAM,kBAAkB,GAAG,QAAQ,CAAC,kBAAkB,IAAI,QAAQ,CAAC,aAAa,CAAC;QAEjF,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,YAAY,EAAE,YAAY,KAAK,iBAAiB,IAAI,YAAY,EAAE,aAAa,KAAK,kBAAkB,EAAE;YACjJ,IAAI,mBAAmB,CAAC;YACxB,MAAM,wBAAwB,GAAG,QAAQ,CAAC,wBAAwB,IAAI,iBAAiB,CAAC;YACxF,MAAM,yBAAyB,GAAG,QAAQ,CAAC,yBAAyB,IAAI,kBAAkB,CAAC;YAC3F,IAAI,iBAAiB,KAAK,wBAAwB,IAAI,kBAAkB,KAAK,yBAAyB,EAAE;gBACpG,mBAAmB,GAAG,QAAQ,CAAC,mBAAmB,CAAC;aACtD;YAED,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAClE,iBAAiB,EACjB,kBAAkB,EAClB,IAAI,EACJ,QAAQ,CAAC,YAAY,EACrB,mBAAmB,EACnB,IAAI,CAAC,YAAY,CAAC,WAAW,CAChC,CAAC;YAEF,IAAI,CAAC,sBAAsB,GAAG;gBAC1B,gBAAgB,EAAE,iBAAiB;gBACnC,iBAAiB,EAAE,kBAAkB;aACxC,CAAC;YACF,IAAI,CAAC,sCAAsC,CAAC,eAAe,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;SACvH;QAED,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IAChD,CAAC;IACO,kBAAkB,CAAC,GAAW;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;QACzD,IAAI,YAAY,EAAE;YACd,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;SACtF;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IACM,4BAA4B,CAAC,GAAW;QAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,QAAQ,EAAE;YACV,OAAO,IAAI,CAAC,2BAA2B,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;SAC1D;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IACM,6BAA6B,CAAC,IAAa;QAC9C,OAAO,IAAI,CAAC,4BAA4B,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACxD,CAAC;IAES,uBAAuB,CAAC,QAAkB,EAAE,QAAyB;QAC3E,MAAM,YAAY,GAAG,QAAQ,CAAC,iBAAiB,IAAI,QAAQ,CAAC,YAAY,CAAC;QACzE,MAAM,aAAa,GAAG,QAAQ,CAAC,kBAAkB,IAAI,QAAQ,CAAC,aAAa,CAAC;QAC5E,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC;QACrC,QAAQ,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,YAAY,CAAC;QACzC,QAAQ,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,aAAa,CAAC;QAC1C,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG,YAAY,CAAC;QACjD,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,aAAa,CAAC;IACxD,CAAC;IAEM,qBAAqB,CAAC,QAAkB,EAAE,IAAY;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxF,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ", "sourcesContent": ["import type { InternalTexture } from \"core/Materials/Textures/internalTexture\";\r\nimport type { RenderTargetTexture } from \"core/Materials/Textures/renderTargetTexture\";\r\nimport type { Viewport } from \"core/Maths/math.viewport\";\r\nimport { Observable } from \"core/Misc/observable\";\r\nimport type { WebXRLayerType } from \"core/XR/webXRLayerWrapper\";\r\nimport { WebXRLayerWrapper } from \"core/XR/webXRLayerWrapper\";\r\nimport { WebXRLayerRenderTargetTextureProvider } from \"core/XR/webXRRenderTargetTextureProvider\";\r\nimport type { WebXRSessionManager } from \"core/XR/webXRSessionManager\";\r\nimport type { Nullable } from \"core/types\";\r\n\r\n/**\r\n * Wraps xr composition layers.\r\n * @internal\r\n */\r\nexport class WebXRCompositionLayerWrapper extends WebXRLayerWrapper {\r\n    constructor(\r\n        public getWidth: () => number,\r\n        public getHeight: () => number,\r\n        public readonly layer: XRCompositionLayer,\r\n        public readonly layerType: WebXRLayerType,\r\n        public readonly isMultiview: boolean,\r\n        public createRTTProvider: (xrSessionManager: WebXRSessionManager) => WebXRLayerRenderTargetTextureProvider,\r\n        public _originalInternalTexture: Nullable<InternalTexture> = null\r\n    ) {\r\n        super(getWidth, getHeight, layer, layerType, createRTTProvider);\r\n    }\r\n}\r\n\r\n/**\r\n * Provides render target textures and other important rendering information for a given XRCompositionLayer.\r\n * @internal\r\n */\r\nexport class WebXRCompositionLayerRenderTargetTextureProvider extends WebXRLayerRenderTargetTextureProvider {\r\n    protected _lastSubImages = new Map<XREye, XRWebGLSubImage>();\r\n    private _compositionLayer: XRCompositionLayer;\r\n    /**\r\n     * Fires every time a new render target texture is created (either for eye, for view, or for the entire frame)\r\n     */\r\n    public onRenderTargetTextureCreatedObservable = new Observable<{ texture: RenderTargetTexture; eye?: XREye }>();\r\n\r\n    constructor(\r\n        protected readonly _xrSessionManager: WebXRSessionManager,\r\n        protected readonly _xrWebGLBinding: XRWebGLBinding,\r\n        public readonly layerWrapper: WebXRCompositionLayerWrapper\r\n    ) {\r\n        super(_xrSessionManager.scene, layerWrapper);\r\n        this._compositionLayer = layerWrapper.layer;\r\n    }\r\n\r\n    protected _getRenderTargetForSubImage(subImage: XRWebGLSubImage, eye: XREye = \"none\") {\r\n        const lastSubImage = this._lastSubImages.get(eye);\r\n        const eyeIndex = eye == \"right\" ? 1 : 0;\r\n\r\n        const colorTextureWidth = subImage.colorTextureWidth ?? subImage.textureWidth;\r\n        const colorTextureHeight = subImage.colorTextureHeight ?? subImage.textureHeight;\r\n\r\n        if (!this._renderTargetTextures[eyeIndex] || lastSubImage?.textureWidth !== colorTextureWidth || lastSubImage?.textureHeight !== colorTextureHeight) {\r\n            let depthStencilTexture;\r\n            const depthStencilTextureWidth = subImage.depthStencilTextureWidth ?? colorTextureWidth;\r\n            const depthStencilTextureHeight = subImage.depthStencilTextureHeight ?? colorTextureHeight;\r\n            if (colorTextureWidth === depthStencilTextureWidth || colorTextureHeight === depthStencilTextureHeight) {\r\n                depthStencilTexture = subImage.depthStencilTexture;\r\n            }\r\n\r\n            this._renderTargetTextures[eyeIndex] = this._createRenderTargetTexture(\r\n                colorTextureWidth,\r\n                colorTextureHeight,\r\n                null,\r\n                subImage.colorTexture,\r\n                depthStencilTexture,\r\n                this.layerWrapper.isMultiview\r\n            );\r\n\r\n            this._framebufferDimensions = {\r\n                framebufferWidth: colorTextureWidth,\r\n                framebufferHeight: colorTextureHeight,\r\n            };\r\n            this.onRenderTargetTextureCreatedObservable.notifyObservers({ texture: this._renderTargetTextures[eyeIndex], eye });\r\n        }\r\n\r\n        this._lastSubImages.set(eye, subImage);\r\n        return this._renderTargetTextures[eyeIndex];\r\n    }\r\n    private _getSubImageForEye(eye?: XREye): Nullable<XRWebGLSubImage> {\r\n        const currentFrame = this._xrSessionManager.currentFrame;\r\n        if (currentFrame) {\r\n            return this._xrWebGLBinding.getSubImage(this._compositionLayer, currentFrame, eye);\r\n        }\r\n        return null;\r\n    }\r\n    public getRenderTargetTextureForEye(eye?: XREye): Nullable<RenderTargetTexture> {\r\n        const subImage = this._getSubImageForEye(eye);\r\n        if (subImage) {\r\n            return this._getRenderTargetForSubImage(subImage, eye);\r\n        }\r\n        return null;\r\n    }\r\n    public getRenderTargetTextureForView(view?: XRView): Nullable<RenderTargetTexture> {\r\n        return this.getRenderTargetTextureForEye(view?.eye);\r\n    }\r\n\r\n    protected _setViewportForSubImage(viewport: Viewport, subImage: XRWebGLSubImage) {\r\n        const textureWidth = subImage.colorTextureWidth ?? subImage.textureWidth;\r\n        const textureHeight = subImage.colorTextureHeight ?? subImage.textureHeight;\r\n        const xrViewport = subImage.viewport;\r\n        viewport.x = xrViewport.x / textureWidth;\r\n        viewport.y = xrViewport.y / textureHeight;\r\n        viewport.width = xrViewport.width / textureWidth;\r\n        viewport.height = xrViewport.height / textureHeight;\r\n    }\r\n\r\n    public trySetViewportForView(viewport: Viewport, view: XRView): boolean {\r\n        const subImage = this._lastSubImages.get(view.eye) || this._getSubImageForEye(view.eye);\r\n        if (subImage) {\r\n            this._setViewportForSubImage(viewport, subImage);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n}\r\n"]}