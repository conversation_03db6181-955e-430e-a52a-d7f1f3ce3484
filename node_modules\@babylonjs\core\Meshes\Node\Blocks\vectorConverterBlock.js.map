{"version": 3, "file": "vectorConverterBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Meshes/Node/Blocks/vectorConverterBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AACvE,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,qCAAqC,EAAE,MAAM,2CAA2C,CAAC;AAClG,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AAIzD;;GAEG;AACH,MAAM,OAAO,oBAAqB,SAAQ,iBAAiB;IACvD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAEZ,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACjF,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAChF,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAE5E,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC3E,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC1E,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;IAC1E,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,YAAY,CAAC,IAAY;QAC/B,IAAI,IAAI,KAAK,OAAO,EAAE;YAClB,OAAO,QAAQ,CAAC;SACnB;QACD,IAAI,IAAI,KAAK,MAAM,EAAE;YACjB,OAAO,OAAO,CAAC;SAClB;QACD,IAAI,IAAI,KAAK,KAAK,EAAE;YAChB,OAAO,MAAM,CAAC;SACjB;QACD,IAAI,IAAI,KAAK,KAAK,EAAE;YAChB,OAAO,MAAM,CAAC;SACjB;QACD,IAAI,IAAI,KAAK,IAAI,EAAE;YACf,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,IAAI,KAAK,IAAI,EAAE;YACf,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,IAAI,KAAK,IAAI,EAAE;YACf,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,IAAI,KAAK,IAAI,EAAE;YACf,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,aAAa,CAAC,IAAY;QAChC,QAAQ,IAAI,EAAE;YACV,KAAK,GAAG;gBACJ,OAAO,MAAM,CAAC;YAClB,KAAK,GAAG;gBACJ,OAAO,MAAM,CAAC;YAClB,KAAK,GAAG;gBACJ,OAAO,MAAM,CAAC;YAClB,KAAK,GAAG;gBACJ,OAAO,MAAM,CAAC;YAClB,KAAK,IAAI;gBACL,OAAO,OAAO,CAAC;YACnB,KAAK,IAAI;gBACL,OAAO,OAAO,CAAC;YACnB,KAAK,KAAK;gBACN,OAAO,QAAQ,CAAC;YACpB,KAAK,MAAM;gBACP,OAAO,SAAS,CAAC;YACrB;gBACI,OAAO,IAAI,CAAC;SACnB;IACL,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;QACxB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;QAE9B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC;QAChC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAE1B,MAAM,OAAO,GAAG,CAAC,KAA6B,EAAW,EAAE;YACvD,IAAI,SAAS,CAAC,WAAW,EAAE;gBACvB,OAAO,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;aAC7C;YAED,IAAI,CAAC,GAAW,CAAC,CAAC;YAClB,IAAI,CAAC,GAAW,CAAC,CAAC;YAClB,IAAI,CAAC,GAAW,CAAC,CAAC;YAClB,IAAI,CAAC,GAAW,CAAC,CAAC;YAElB,IAAI,MAAM,CAAC,WAAW,EAAE;gBACpB,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;aACvC;YACD,IAAI,MAAM,CAAC,WAAW,EAAE;gBACpB,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;aACvC;YACD,IAAI,MAAM,CAAC,WAAW,EAAE;gBACpB,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;aACvC;YACD,IAAI,MAAM,CAAC,WAAW,EAAE;gBACpB,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;aACvC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAC9C,IAAI,IAAI,EAAE;oBACN,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;oBACX,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;iBACd;aACJ;YACD,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAC9C,IAAI,IAAI,EAAE;oBACN,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;oBACX,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;iBACd;aACJ;YACD,IAAI,QAAQ,CAAC,WAAW,EAAE;gBACtB,MAAM,IAAI,GAAG,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAC/C,IAAI,IAAI,EAAE;oBACN,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;oBACX,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;oBACX,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;iBACd;aACJ;YAED,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC;QAEF,UAAU,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvD,SAAS,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;YAClC,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAC5B,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC;QACF,QAAQ,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;YACjC,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAC5B,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC;QACF,QAAQ,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;YACjC,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAC5B,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC;QACF,OAAO,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACtD,OAAO,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACtD,OAAO,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACtD,OAAO,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;CACJ;AAED,aAAa,CAAC,8BAA8B,EAAE,oBAAoB,CAAC,CAAC", "sourcesContent": ["import { Vector2, Vector3, Vector4 } from \"../../../Maths/math.vector\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"../Enums/nodeGeometryConnectionPointTypes\";\r\nimport { NodeGeometryBlock } from \"../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../nodeGeometryBlockConnectionPoint\";\r\nimport type { NodeGeometryBuildState } from \"../nodeGeometryBuildState\";\r\n\r\n/**\r\n * Block used to create a Vector2/3/4 out of individual or partial inputs\r\n */\r\nexport class VectorConverterBlock extends NodeGeometryBlock {\r\n    /**\r\n     * Create a new VectorConverterBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"xyzw \", NodeGeometryBlockConnectionPointTypes.Vector4, true);\r\n        this.registerInput(\"xyz \", NodeGeometryBlockConnectionPointTypes.Vector3, true);\r\n        this.registerInput(\"xy \", NodeGeometryBlockConnectionPointTypes.Vector2, true);\r\n        this.registerInput(\"zw \", NodeGeometryBlockConnectionPointTypes.Vector2, true);\r\n        this.registerInput(\"x \", NodeGeometryBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"y \", NodeGeometryBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"z \", NodeGeometryBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"w \", NodeGeometryBlockConnectionPointTypes.Float, true);\r\n\r\n        this.registerOutput(\"xyzw\", NodeGeometryBlockConnectionPointTypes.Vector4);\r\n        this.registerOutput(\"xyz\", NodeGeometryBlockConnectionPointTypes.Vector3);\r\n        this.registerOutput(\"xy\", NodeGeometryBlockConnectionPointTypes.Vector2);\r\n        this.registerOutput(\"zw\", NodeGeometryBlockConnectionPointTypes.Vector2);\r\n        this.registerOutput(\"x\", NodeGeometryBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"y\", NodeGeometryBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"z\", NodeGeometryBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"w\", NodeGeometryBlockConnectionPointTypes.Float);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"VectorConverterBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the xyzw component (input)\r\n     */\r\n    public get xyzwIn(): NodeGeometryConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the xyz component (input)\r\n     */\r\n    public get xyzIn(): NodeGeometryConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the xy component (input)\r\n     */\r\n    public get xyIn(): NodeGeometryConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the zw component (input)\r\n     */\r\n    public get zwIn(): NodeGeometryConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the x component (input)\r\n     */\r\n    public get xIn(): NodeGeometryConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the y component (input)\r\n     */\r\n    public get yIn(): NodeGeometryConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the z component (input)\r\n     */\r\n    public get zIn(): NodeGeometryConnectionPoint {\r\n        return this._inputs[6];\r\n    }\r\n\r\n    /**\r\n     * Gets the w component (input)\r\n     */\r\n    public get wIn(): NodeGeometryConnectionPoint {\r\n        return this._inputs[7];\r\n    }\r\n\r\n    /**\r\n     * Gets the xyzw component (output)\r\n     */\r\n    public get xyzwOut(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the xyz component (output)\r\n     */\r\n    public get xyzOut(): NodeGeometryConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the xy component (output)\r\n     */\r\n    public get xyOut(): NodeGeometryConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the zw component (output)\r\n     */\r\n    public get zwOut(): NodeGeometryConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the x component (output)\r\n     */\r\n    public get xOut(): NodeGeometryConnectionPoint {\r\n        return this._outputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the y component (output)\r\n     */\r\n    public get yOut(): NodeGeometryConnectionPoint {\r\n        return this._outputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the z component (output)\r\n     */\r\n    public get zOut(): NodeGeometryConnectionPoint {\r\n        return this._outputs[6];\r\n    }\r\n\r\n    /**\r\n     * Gets the w component (output)\r\n     */\r\n    public get wOut(): NodeGeometryConnectionPoint {\r\n        return this._outputs[7];\r\n    }\r\n\r\n    protected _inputRename(name: string) {\r\n        if (name === \"xyzw \") {\r\n            return \"xyzwIn\";\r\n        }\r\n        if (name === \"xyz \") {\r\n            return \"xyzIn\";\r\n        }\r\n        if (name === \"xy \") {\r\n            return \"xyIn\";\r\n        }\r\n        if (name === \"zw \") {\r\n            return \"zwIn\";\r\n        }\r\n        if (name === \"x \") {\r\n            return \"xIn\";\r\n        }\r\n        if (name === \"y \") {\r\n            return \"yIn\";\r\n        }\r\n        if (name === \"z \") {\r\n            return \"zIn\";\r\n        }\r\n        if (name === \"w \") {\r\n            return \"wIn\";\r\n        }\r\n        return name;\r\n    }\r\n\r\n    protected _outputRename(name: string) {\r\n        switch (name) {\r\n            case \"x\":\r\n                return \"xOut\";\r\n            case \"y\":\r\n                return \"yOut\";\r\n            case \"z\":\r\n                return \"zOut\";\r\n            case \"w\":\r\n                return \"wOut\";\r\n            case \"xy\":\r\n                return \"xyOut\";\r\n            case \"zw\":\r\n                return \"zwOut\";\r\n            case \"xyz\":\r\n                return \"xyzOut\";\r\n            case \"xyzw\":\r\n                return \"xyzwOut\";\r\n            default:\r\n                return name;\r\n        }\r\n    }\r\n\r\n    protected _buildBlock(state: NodeGeometryBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const xInput = this.xIn;\r\n        const yInput = this.yIn;\r\n        const zInput = this.zIn;\r\n        const wInput = this.wIn;\r\n        const xyInput = this.xyIn;\r\n        const zwInput = this.zwIn;\r\n        const xyzInput = this.xyzIn;\r\n        const xyzwInput = this.xyzwIn;\r\n\r\n        const xyzwOutput = this.xyzwOut;\r\n        const xyzOutput = this.xyzOut;\r\n        const xyOutput = this.xyOut;\r\n        const zwOutput = this.zwOut;\r\n        const xOutput = this.xOut;\r\n        const yOutput = this.yOut;\r\n        const zOutput = this.zOut;\r\n        const wOutput = this.wOut;\r\n\r\n        const getData = (state: NodeGeometryBuildState): Vector4 => {\r\n            if (xyzwInput.isConnected) {\r\n                return xyzwInput.getConnectedValue(state);\r\n            }\r\n\r\n            let x: number = 0;\r\n            let y: number = 0;\r\n            let z: number = 0;\r\n            let w: number = 0;\r\n\r\n            if (xInput.isConnected) {\r\n                x = xInput.getConnectedValue(state);\r\n            }\r\n            if (yInput.isConnected) {\r\n                y = yInput.getConnectedValue(state);\r\n            }\r\n            if (zInput.isConnected) {\r\n                z = zInput.getConnectedValue(state);\r\n            }\r\n            if (wInput.isConnected) {\r\n                w = wInput.getConnectedValue(state);\r\n            }\r\n\r\n            if (xyInput.isConnected) {\r\n                const temp = xyInput.getConnectedValue(state);\r\n                if (temp) {\r\n                    x = temp.x;\r\n                    y = temp.y;\r\n                }\r\n            }\r\n            if (zwInput.isConnected) {\r\n                const temp = zwInput.getConnectedValue(state);\r\n                if (temp) {\r\n                    z = temp.x;\r\n                    w = temp.y;\r\n                }\r\n            }\r\n            if (xyzInput.isConnected) {\r\n                const temp = xyzInput.getConnectedValue(state);\r\n                if (temp) {\r\n                    x = temp.x;\r\n                    y = temp.y;\r\n                    z = temp.z;\r\n                }\r\n            }\r\n\r\n            return new Vector4(x, y, z, w);\r\n        };\r\n\r\n        xyzwOutput._storedFunction = (state) => getData(state);\r\n        xyzOutput._storedFunction = (state) => {\r\n            const data = getData(state);\r\n            return new Vector3(data.x, data.y, data.z);\r\n        };\r\n        xyOutput._storedFunction = (state) => {\r\n            const data = getData(state);\r\n            return new Vector2(data.x, data.y);\r\n        };\r\n        zwOutput._storedFunction = (state) => {\r\n            const data = getData(state);\r\n            return new Vector2(data.z, data.w);\r\n        };\r\n        xOutput._storedFunction = (state) => getData(state).x;\r\n        yOutput._storedFunction = (state) => getData(state).y;\r\n        zOutput._storedFunction = (state) => getData(state).z;\r\n        wOutput._storedFunction = (state) => getData(state).w;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.VectorConverterBlock\", VectorConverterBlock);\r\n"]}