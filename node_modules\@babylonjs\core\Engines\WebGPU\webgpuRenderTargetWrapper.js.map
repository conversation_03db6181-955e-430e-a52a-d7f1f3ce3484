{"version": 3, "file": "webgpuRenderTargetWrapper.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuRenderTargetWrapper.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;AAC7D,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD;;GAEG;AACH,MAAM,OAAO,yBAA0B,SAAQ,mBAAmB;IAU9D;;;;;;;OAOG;IACH,YAAY,OAAgB,EAAE,MAAe,EAAE,IAAiB,EAAE,MAAoB,EAAE,KAAc;QAClG,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAE5C,IAAI,MAAM,CAAC,2BAA2B,EAAE;YACpC,IAAI,CAAC,cAAc,GAAG,IAAI,iBAAiB,EAAE,CAAC;SACjD;IACL,CAAC;CACJ", "sourcesContent": ["import type { TextureSize } from \"../../Materials/Textures/textureCreationOptions\";\r\nimport type { WebGPUEngine } from \"../webgpuEngine\";\r\nimport { RenderTargetWrapper } from \"../renderTargetWrapper\";\r\nimport { WebGPUPerfCounter } from \"./webgpuPerfCounter\";\r\n\r\n/**\r\n * Specialized class used to store a render target of a WebGPU engine\r\n */\r\nexport class WebGPURenderTargetWrapper extends RenderTargetWrapper {\r\n    /** @internal */\r\n    public _defaultAttachments: number[];\r\n\r\n    /**\r\n     * Gets the GPU time spent rendering this render target in the last frame (in nanoseconds).\r\n     * You have to enable the \"timestamp-query\" extension in the engine constructor options and set engine.enableGPUTimingMeasurements = true.\r\n     */\r\n    public readonly gpuTimeInFrame?: WebGPUPerfCounter;\r\n\r\n    /**\r\n     * Initializes the render target wrapper\r\n     * @param isMulti true if the wrapper is a multi render target\r\n     * @param isCube true if the wrapper should render to a cube texture\r\n     * @param size size of the render target (width/height/layers)\r\n     * @param engine engine used to create the render target\r\n     * @param label defines the label to use for the wrapper (for debugging purpose only)\r\n     */\r\n    constructor(isMulti: boolean, isCube: boolean, size: TextureSize, engine: WebGPUEngine, label?: string) {\r\n        super(isMulti, isCube, size, engine, label);\r\n\r\n        if (engine.enableGPUTimingMeasurements) {\r\n            this.gpuTimeInFrame = new WebGPUPerfCounter();\r\n        }\r\n    }\r\n}\r\n"]}