{"version": 3, "file": "textureTools.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/textureTools.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,mBAAmB,EAAE,MAAM,2CAA2C,CAAC;AAChF,OAAO,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AACnE,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEjD,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAG3D;;;;;;;GAOG;AACH,MAAM,UAAU,iBAAiB,CAAC,OAAgB,EAAE,KAAa,EAAE,MAAc,EAAE,kBAA2B,IAAI;IAC9G,MAAM,KAAK,GAAU,OAAO,CAAC,QAAQ,EAAE,CAAC;IACxC,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;IAEjC,MAAM,GAAG,GAAG,IAAI,mBAAmB,CAC/B,SAAS,GAAG,OAAO,CAAC,IAAI,EACxB,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAChC,KAAK,EACL,CAAC,OAAO,CAAC,QAAQ,EACjB,IAAI,EACc,OAAO,CAAC,QAAS,CAAC,IAAI,EACxC,KAAK,EACL,OAAO,CAAC,YAAY,EACpB,KAAK,CACR,CAAC;IAEF,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC1B,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC1B,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAC9B,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAC9B,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAC5B,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAC5B,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IACxB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IACxB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IACxB,GAAG,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;IAChD,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC1B,GAAG,CAAC,yBAAyB,GAAG,OAAO,CAAC,yBAAyB,CAAC;IAChD,GAAG,CAAC,QAAS,CAAC,OAAO,GAAG,KAAK,CAAC;IAEhD,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAC1C,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAE1C,MAAM,eAAe,GAAG,IAAI,eAAe,CACvC,MAAM,EACN,CAAC,EACD,IAAI,EACJ,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,EAC9E,MAAM,EACN,KAAK,EACL,SAAS,CAAC,wBAAwB,CACrC,CAAC;IACF,eAAe,CAAC,6BAA6B,GAAG,IAAI,CAAC;IACrD,eAAe,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE;QACjD,eAAe,CAAC,OAAO,GAAG,UAAU,MAAM;YACtC,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QACjD,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,GAAG,CAAC,YAAY,CAAC;QAEzC,IAAI,eAAe,EAAE;YACjB,KAAK,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,EAAE,eAAe,CAAC,CAAC;YAE1E,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC1C,GAAG,CAAC,yBAAyB,EAAE,CAAC;YAChC,eAAe,CAAC,OAAO,EAAE,CAAC;YAE1B,GAAG,CAAC,kBAAkB,EAAG,CAAC,OAAO,GAAG,IAAI,CAAC;SAC5C;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC;AACf,CAAC;AAED;;;;;;;;;;;GAWG;AACH,MAAM,UAAU,gBAAgB,CAC5B,eAAuB,EACvB,eAAgC,EAChC,KAAY,EACZ,IAAa,EACb,YAAqB,EACrB,MAAe,EACf,KAAc,EACd,MAAe;IAEf,yBAAyB;IACzB,MAAM,MAAM,GAAG,eAAe,CAAC,SAAS,EAAY,CAAC;IAErD,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC;IAEhC,YAAY,GAAG,YAAY,IAAI,eAAe,CAAC,YAAY,CAAC;IAC5D,IAAI,GAAG,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC;IACpC,MAAM,GAAG,MAAM,IAAI,eAAe,CAAC,MAAM,CAAC;IAC1C,KAAK,GAAG,KAAK,IAAI,eAAe,CAAC,KAAK,CAAC;IACvC,MAAM,GAAG,MAAM,IAAI,eAAe,CAAC,MAAM,CAAC;IAE1C,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE;QACb,IAAI,GAAG,SAAS,CAAC,yBAAyB,CAAC;KAC9C;IAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC3B,0BAA0B;QAC1B,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,aAAa,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACvK,WAAW,CAAC,6BAA6B,GAAG,IAAI,CAAC;QAEjD,mCAAmC;QACnC,MAAM,cAAc,GAAG,MAAM,CAAC,yBAAyB,CACnD,EAAE,KAAK,EAAE,KAAe,EAAE,MAAM,EAAE,MAAgB,EAAE,EACpD;YACI,mBAAmB,EAAE,KAAK;YAC1B,eAAe,EAAE,KAAK;YACtB,qBAAqB,EAAE,KAAK;YAC5B,YAAY;YACZ,IAAI;YACJ,MAAM;SACT,CACJ,CAAC;QAEF,WAAW,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE;YAC7C,iBAAiB;YACjB,WAAW,CAAC,OAAO,GAAG,CAAC,MAAM,EAAE,EAAE;gBAC7B,MAAM,CAAC,YAAY,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;gBACvD,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC;YACF,KAAK,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,WAAY,CAAC,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;YAE5E,UAAU;YACV,MAAM,CAAC,yBAAyB,EAAE,CAAC;YACnC,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YACxC,IAAI,WAAW,EAAE;gBACb,WAAW,CAAC,OAAO,EAAE,CAAC;aACzB;YAED,gBAAgB;YAChB,cAAc,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;YAE5C,8BAA8B;YAC9B,eAAe,CAAC,IAAI,GAAG,IAAK,CAAC;YAC7B,eAAe,CAAC,MAAM,GAAG,SAAS,CAAC,kBAAkB,CAAC;YACtD,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC;YAE/B,OAAO,CAAC,eAAe,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACP,CAAC;AAED,mGAAmG;AACnG,IAAI,SAAuB,CAAC;AAC5B,IAAI,SAAqB,CAAC;AAC1B;;;;GAIG;AACH,MAAM,UAAU,WAAW,CAAC,KAAa;IACrC,IAAI,CAAC,SAAS,EAAE;QACZ,SAAS,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;QAChC,SAAS,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KAChD;IAED,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IACrB,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;IAEvB,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,kBAAkB;IACjD,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,qCAAqC;IACjE,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,8BAA8B;IAE1D;mCAC+B;IAC/B,IAAI,CAAC,GAAG,GAAG,EAAE;QACT,OAAO,IAAI,CAAC;KACf;IAED,kEAAkE;IAClE,IAAI,CAAC,GAAG,GAAG,EAAE;QACT,IAAI,IAAI,MAAM,CAAC;QACf;gEACwD;QACxD,IAAI,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;QAC7C,OAAO,IAAI,CAAC;KACf;IAED,gEAAgE;IAChE,IAAI,CAAC,GAAG,GAAG,EAAE;QACT,CAAC,IAAI,MAAM,CAAC;QACZ;gCACwB;QACxB,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;KACf;IAED,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACrC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;IACd,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,aAAa,CAAC,KAAa;IACvC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;IACjC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;IACjC,MAAM,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;IAEzB,IAAI,CAAC,KAAK,CAAC,EAAE;QACT,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;KAClE;SAAM,IAAI,CAAC,IAAI,IAAI,EAAE;QAClB,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;KAC5C;IAED,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAC1E,CAAC;AAED,MAAM,YAAY,GAAG,KAAK,EAAE,OAAoB,EAAE,KAAa,EAAE,MAAc,EAAE,IAAY,EAAE,GAAW,EAAuB,EAAE;IAC/H,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,EAAG,CAAC;IAClC,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;IAEjC,IAAI,cAA2B,CAAC;IAEhC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QACjB,cAAc,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAC;KACjI;SAAM;QACH,MAAM,WAAW,GAAG,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;QACnJ,cAAc,GAAG,IAAI,WAAW,CAAC,SAAS,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,0BAA0B,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;KACnK;IAED,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC1B,cAAc,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE;YAChD,OAAO,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,MAAM,GAAG,GAAG,IAAI,mBAAmB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAE5F,cAAc,CAAC,OAAO,GAAG,UAAU,MAAM;QACrC,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QAC7C,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC5B,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;IAChD,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;IAErD,IAAI;QACA,IAAI,GAAG,CAAC,YAAY,IAAI,eAAe,EAAE;YACrC,MAAM,YAAY,GAAG,eAAe,CAAC,YAAY,CAAC;YAClD,IAAI,GAAG,KAAK,CAAC,EAAE;gBACX,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;aAClE;iBAAM;gBACH,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;aACvD;YAED,KAAK,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAChF,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;YAEzC,0BAA0B;YAC1B,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAChE,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;YAEzE,SAAS;YACT,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAE3C,OAAO,IAAI,CAAC;SACf;aAAM;YACH,MAAM,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAC5C;KACJ;YAAS;QACN,GAAG,CAAC,OAAO,EAAE,CAAC;QACd,cAAc,CAAC,OAAO,EAAE,CAAC;KAC5B;AACL,CAAC,CAAC;AAEF;;;;;;;;;GASG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB,CAAC,OAAoB,EAAE,KAAa,EAAE,MAAc,EAAE,OAAe,CAAC,EAAE,MAAc,CAAC;IAC5H,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,OAAO,CAAC,QAAQ,EAAE;QACxC,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAClC,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI,EAAE;gBAC3B,MAAM,CAAC,CAAC,CAAC,CAAC;gBACV,OAAO;aACV;YACD,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,EAAE;gBAC7C,OAAO,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;KACN;IACD,OAAO,MAAM,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AACjE,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG;IACxB;;;;;;;OAOG;IACH,iBAAiB;IAEjB;;;;;;;;;OASG;IACH,gBAAgB;IAChB;;;;OAIG;IACH,WAAW;IAEX;;;;OAIG;IACH,aAAa;IAEb;;;;;;;;;;OAUG;IACH,mBAAmB;CACtB,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { InternalTexture } from \"../Materials/Textures/internalTexture\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport { PassPostProcess } from \"../PostProcesses/passPostProcess\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { Scene } from \"../scene\";\r\nimport { PostProcess } from \"../PostProcesses/postProcess\";\r\nimport type { Engine } from \"../Engines/engine\";\r\n\r\n/**\r\n * Uses the GPU to create a copy texture rescaled at a given size\r\n * @param texture Texture to copy from\r\n * @param width defines the desired width\r\n * @param height defines the desired height\r\n * @param useBilinearMode defines if bilinear mode has to be used\r\n * @returns the generated texture\r\n */\r\nexport function CreateResizedCopy(texture: Texture, width: number, height: number, useBilinearMode: boolean = true): Texture {\r\n    const scene = <Scene>texture.getScene();\r\n    const engine = scene.getEngine();\r\n\r\n    const rtt = new RenderTargetTexture(\r\n        \"resized\" + texture.name,\r\n        { width: width, height: height },\r\n        scene,\r\n        !texture.noMipmap,\r\n        true,\r\n        (<InternalTexture>texture._texture).type,\r\n        false,\r\n        texture.samplingMode,\r\n        false\r\n    );\r\n\r\n    rtt.wrapU = texture.wrapU;\r\n    rtt.wrapV = texture.wrapV;\r\n    rtt.uOffset = texture.uOffset;\r\n    rtt.vOffset = texture.vOffset;\r\n    rtt.uScale = texture.uScale;\r\n    rtt.vScale = texture.vScale;\r\n    rtt.uAng = texture.uAng;\r\n    rtt.vAng = texture.vAng;\r\n    rtt.wAng = texture.wAng;\r\n    rtt.coordinatesIndex = texture.coordinatesIndex;\r\n    rtt.level = texture.level;\r\n    rtt.anisotropicFilteringLevel = texture.anisotropicFilteringLevel;\r\n    (<InternalTexture>rtt._texture).isReady = false;\r\n\r\n    texture.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n    texture.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n\r\n    const passPostProcess = new PassPostProcess(\r\n        \"pass\",\r\n        1,\r\n        null,\r\n        useBilinearMode ? Texture.BILINEAR_SAMPLINGMODE : Texture.NEAREST_SAMPLINGMODE,\r\n        engine,\r\n        false,\r\n        Constants.TEXTURETYPE_UNSIGNED_INT\r\n    );\r\n    passPostProcess.externalTextureSamplerBinding = true;\r\n    passPostProcess.getEffect().executeWhenCompiled(() => {\r\n        passPostProcess.onApply = function (effect) {\r\n            effect.setTexture(\"textureSampler\", texture);\r\n        };\r\n\r\n        const internalTexture = rtt.renderTarget;\r\n\r\n        if (internalTexture) {\r\n            scene.postProcessManager.directRender([passPostProcess], internalTexture);\r\n\r\n            engine.unBindFramebuffer(internalTexture);\r\n            rtt.disposeFramebufferObjects();\r\n            passPostProcess.dispose();\r\n\r\n            rtt.getInternalTexture()!.isReady = true;\r\n        }\r\n    });\r\n\r\n    return rtt;\r\n}\r\n\r\n/**\r\n * Apply a post process to a texture\r\n * @param postProcessName name of the fragment post process\r\n * @param internalTexture the texture to encode\r\n * @param scene the scene hosting the texture\r\n * @param type type of the output texture. If not provided, use the one from internalTexture\r\n * @param samplingMode sampling mode to use to sample the source texture. If not provided, use the one from internalTexture\r\n * @param format format of the output texture. If not provided, use the one from internalTexture\r\n * @param width width of the output texture. If not provided, use the one from internalTexture\r\n * @param height height of the output texture. If not provided, use the one from internalTexture\r\n * @returns a promise with the internalTexture having its texture replaced by the result of the processing\r\n */\r\nexport function ApplyPostProcess(\r\n    postProcessName: string,\r\n    internalTexture: InternalTexture,\r\n    scene: Scene,\r\n    type?: number,\r\n    samplingMode?: number,\r\n    format?: number,\r\n    width?: number,\r\n    height?: number\r\n): Promise<InternalTexture> {\r\n    // Gets everything ready.\r\n    const engine = internalTexture.getEngine() as Engine;\r\n\r\n    internalTexture.isReady = false;\r\n\r\n    samplingMode = samplingMode ?? internalTexture.samplingMode;\r\n    type = type ?? internalTexture.type;\r\n    format = format ?? internalTexture.format;\r\n    width = width ?? internalTexture.width;\r\n    height = height ?? internalTexture.height;\r\n\r\n    if (type === -1) {\r\n        type = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n    }\r\n\r\n    return new Promise((resolve) => {\r\n        // Create the post process\r\n        const postProcess = new PostProcess(\"postprocess\", postProcessName, null, null, 1, null, samplingMode, engine, false, undefined, type, undefined, null, false, format);\r\n        postProcess.externalTextureSamplerBinding = true;\r\n\r\n        // Hold the output of the decoding.\r\n        const encodedTexture = engine.createRenderTargetTexture(\r\n            { width: width as number, height: height as number },\r\n            {\r\n                generateDepthBuffer: false,\r\n                generateMipMaps: false,\r\n                generateStencilBuffer: false,\r\n                samplingMode,\r\n                type,\r\n                format,\r\n            }\r\n        );\r\n\r\n        postProcess.getEffect().executeWhenCompiled(() => {\r\n            // PP Render Pass\r\n            postProcess.onApply = (effect) => {\r\n                effect._bindTexture(\"textureSampler\", internalTexture);\r\n                effect.setFloat2(\"scale\", 1, 1);\r\n            };\r\n            scene.postProcessManager.directRender([postProcess!], encodedTexture, true);\r\n\r\n            // Cleanup\r\n            engine.restoreDefaultFramebuffer();\r\n            engine._releaseTexture(internalTexture);\r\n            if (postProcess) {\r\n                postProcess.dispose();\r\n            }\r\n\r\n            // Internal Swap\r\n            encodedTexture._swapAndDie(internalTexture);\r\n\r\n            // Ready to get rolling again.\r\n            internalTexture.type = type!;\r\n            internalTexture.format = Constants.TEXTUREFORMAT_RGBA;\r\n            internalTexture.isReady = true;\r\n\r\n            resolve(internalTexture);\r\n        });\r\n    });\r\n}\r\n\r\n// ref: http://stackoverflow.com/questions/32633585/how-do-you-convert-to-half-floats-in-javascript\r\nlet floatView: Float32Array;\r\nlet int32View: Int32Array;\r\n/**\r\n * Converts a number to half float\r\n * @param value number to convert\r\n * @returns converted number\r\n */\r\nexport function ToHalfFloat(value: number): number {\r\n    if (!floatView) {\r\n        floatView = new Float32Array(1);\r\n        int32View = new Int32Array(floatView.buffer);\r\n    }\r\n\r\n    floatView[0] = value;\r\n    const x = int32View[0];\r\n\r\n    let bits = (x >> 16) & 0x8000; /* Get the sign */\r\n    let m = (x >> 12) & 0x07ff; /* Keep one extra bit for rounding */\r\n    const e = (x >> 23) & 0xff; /* Using int is faster here */\r\n\r\n    /* If zero, or denormal, or exponent underflows too much for a denormal\r\n     * half, return signed zero. */\r\n    if (e < 103) {\r\n        return bits;\r\n    }\r\n\r\n    /* If NaN, return NaN. If Inf or exponent overflow, return Inf. */\r\n    if (e > 142) {\r\n        bits |= 0x7c00;\r\n        /* If exponent was 0xff and one mantissa bit was set, it means NaN,\r\n         * not Inf, so make sure we set one mantissa bit too. */\r\n        bits |= (e == 255 ? 0 : 1) && x & 0x007fffff;\r\n        return bits;\r\n    }\r\n\r\n    /* If exponent underflows but not too much, return a denormal */\r\n    if (e < 113) {\r\n        m |= 0x0800;\r\n        /* Extra rounding may overflow and set mantissa to 0 and exponent\r\n         * to 1, which is OK. */\r\n        bits |= (m >> (114 - e)) + ((m >> (113 - e)) & 1);\r\n        return bits;\r\n    }\r\n\r\n    bits |= ((e - 112) << 10) | (m >> 1);\r\n    bits += m & 1;\r\n    return bits;\r\n}\r\n\r\n/**\r\n * Converts a half float to a number\r\n * @param value half float to convert\r\n * @returns converted half float\r\n */\r\nexport function FromHalfFloat(value: number): number {\r\n    const s = (value & 0x8000) >> 15;\r\n    const e = (value & 0x7c00) >> 10;\r\n    const f = value & 0x03ff;\r\n\r\n    if (e === 0) {\r\n        return (s ? -1 : 1) * Math.pow(2, -14) * (f / Math.pow(2, 10));\r\n    } else if (e == 0x1f) {\r\n        return f ? NaN : (s ? -1 : 1) * Infinity;\r\n    }\r\n\r\n    return (s ? -1 : 1) * Math.pow(2, e - 15) * (1 + f / Math.pow(2, 10));\r\n}\r\n\r\nconst ProcessAsync = async (texture: BaseTexture, width: number, height: number, face: number, lod: number): Promise<Uint8Array> => {\r\n    const scene = texture.getScene()!;\r\n    const engine = scene.getEngine();\r\n\r\n    let lodPostProcess: PostProcess;\r\n\r\n    if (!texture.isCube) {\r\n        lodPostProcess = new PostProcess(\"lod\", \"lod\", [\"lod\", \"gamma\"], null, 1.0, null, Texture.NEAREST_NEAREST_MIPNEAREST, engine);\r\n    } else {\r\n        const faceDefines = [\"#define POSITIVEX\", \"#define NEGATIVEX\", \"#define POSITIVEY\", \"#define NEGATIVEY\", \"#define POSITIVEZ\", \"#define NEGATIVEZ\"];\r\n        lodPostProcess = new PostProcess(\"lodCube\", \"lodCube\", [\"lod\", \"gamma\"], null, 1.0, null, Texture.NEAREST_NEAREST_MIPNEAREST, engine, false, faceDefines[face]);\r\n    }\r\n\r\n    await new Promise((resolve) => {\r\n        lodPostProcess.getEffect().executeWhenCompiled(() => {\r\n            resolve(0);\r\n        });\r\n    });\r\n\r\n    const rtt = new RenderTargetTexture(\"temp\", { width: width, height: height }, scene, false);\r\n\r\n    lodPostProcess.onApply = function (effect) {\r\n        effect.setTexture(\"textureSampler\", texture);\r\n        effect.setFloat(\"lod\", lod);\r\n        effect.setBool(\"gamma\", texture.gammaSpace);\r\n    };\r\n\r\n    const internalTexture = texture.getInternalTexture();\r\n\r\n    try {\r\n        if (rtt.renderTarget && internalTexture) {\r\n            const samplingMode = internalTexture.samplingMode;\r\n            if (lod !== 0) {\r\n                texture.updateSamplingMode(Texture.NEAREST_NEAREST_MIPNEAREST);\r\n            } else {\r\n                texture.updateSamplingMode(Texture.NEAREST_NEAREST);\r\n            }\r\n\r\n            scene.postProcessManager.directRender([lodPostProcess], rtt.renderTarget, true);\r\n            texture.updateSamplingMode(samplingMode);\r\n\r\n            //Reading datas from WebGL\r\n            const bufferView = await engine.readPixels(0, 0, width, height);\r\n            const data = new Uint8Array(bufferView.buffer, 0, bufferView.byteLength);\r\n\r\n            // Unbind\r\n            engine.unBindFramebuffer(rtt.renderTarget);\r\n\r\n            return data;\r\n        } else {\r\n            throw Error(\"Render to texture failed.\");\r\n        }\r\n    } finally {\r\n        rtt.dispose();\r\n        lodPostProcess.dispose();\r\n    }\r\n};\r\n\r\n/**\r\n * Gets the data of the specified texture by rendering it to an intermediate RGBA texture and retrieving the bytes from it.\r\n * This is convienent to get 8-bit RGBA values for a texture in a GPU compressed format.\r\n * @param texture the source texture\r\n * @param width the width of the result, which does not have to match the source texture width\r\n * @param height the height of the result, which does not have to match the source texture height\r\n * @param face if the texture has multiple faces, the face index to use for the source\r\n * @param lod if the texture has multiple LODs, the lod index to use for the source\r\n * @returns the 8-bit texture data\r\n */\r\nexport async function GetTextureDataAsync(texture: BaseTexture, width: number, height: number, face: number = 0, lod: number = 0): Promise<Uint8Array> {\r\n    if (!texture.isReady() && texture._texture) {\r\n        await new Promise((resolve, reject) => {\r\n            if (texture._texture === null) {\r\n                reject(0);\r\n                return;\r\n            }\r\n            texture._texture.onLoadedObservable.addOnce(() => {\r\n                resolve(0);\r\n            });\r\n        });\r\n    }\r\n    return await ProcessAsync(texture, width, height, face, lod);\r\n}\r\n\r\n/**\r\n * Class used to host texture specific utilities\r\n */\r\nexport const TextureTools = {\r\n    /**\r\n     * Uses the GPU to create a copy texture rescaled at a given size\r\n     * @param texture Texture to copy from\r\n     * @param width defines the desired width\r\n     * @param height defines the desired height\r\n     * @param useBilinearMode defines if bilinear mode has to be used\r\n     * @returns the generated texture\r\n     */\r\n    CreateResizedCopy,\r\n\r\n    /**\r\n     * Apply a post process to a texture\r\n     * @param postProcessName name of the fragment post process\r\n     * @param internalTexture the texture to encode\r\n     * @param scene the scene hosting the texture\r\n     * @param type type of the output texture. If not provided, use the one from internalTexture\r\n     * @param samplingMode sampling mode to use to sample the source texture. If not provided, use the one from internalTexture\r\n     * @param format format of the output texture. If not provided, use the one from internalTexture\r\n     * @returns a promise with the internalTexture having its texture replaced by the result of the processing\r\n     */\r\n    ApplyPostProcess,\r\n    /**\r\n     * Converts a number to half float\r\n     * @param value number to convert\r\n     * @returns converted number\r\n     */\r\n    ToHalfFloat,\r\n\r\n    /**\r\n     * Converts a half float to a number\r\n     * @param value half float to convert\r\n     * @returns converted half float\r\n     */\r\n    FromHalfFloat,\r\n\r\n    /**\r\n     * Gets the data of the specified texture by rendering it to an intermediate RGBA texture and retrieving the bytes from it.\r\n     * This is convienent to get 8-bit RGBA values for a texture in a GPU compressed format.\r\n     * @param texture the source texture\r\n     * @param width the width of the result, which does not have to match the source texture width\r\n     * @param height the height of the result, which does not have to match the source texture height\r\n     * @param face if the texture has multiple faces, the face index to use for the source\r\n     * @param channels a filter for which of the RGBA channels to return in the result\r\n     * @param lod if the texture has multiple LODs, the lod index to use for the source\r\n     * @returns the 8-bit texture data\r\n     */\r\n    GetTextureDataAsync,\r\n};\r\n"]}