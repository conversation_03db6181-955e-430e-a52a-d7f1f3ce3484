{"version": 3, "file": "occlusionMaterial.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Occlusion/occlusionMaterial.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAEhD,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAEnD,OAAO,8BAA8B,CAAC;AACtC,OAAO,4BAA4B,CAAC;AAEpC;;;GAGG;AACH,MAAM,OAAO,iBAAkB,SAAQ,cAAc;IACjD,YAAY,IAAY,EAAE,KAAY;QAClC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;YACxB,UAAU,EAAE,CAAC,UAAU,CAAC;YACxB,QAAQ,EAAE,CAAC,OAAO,EAAE,gBAAgB,EAAE,OAAO,CAAC;SACjD,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;CACJ", "sourcesContent": ["import { Color4 } from \"../../Maths/math.color\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { ShaderMaterial } from \"../shaderMaterial\";\r\n\r\nimport \"../../Shaders/color.fragment\";\r\nimport \"../../Shaders/color.vertex\";\r\n\r\n/**\r\n * A material to use for fast depth-only rendering.\r\n * @since 5.0.0\r\n */\r\nexport class OcclusionMaterial extends ShaderMaterial {\r\n    constructor(name: string, scene: Scene) {\r\n        super(name, scene, \"color\", {\r\n            attributes: [\"position\"],\r\n            uniforms: [\"world\", \"viewProjection\", \"color\"],\r\n        });\r\n        this.disableColorWrite = true;\r\n        this.forceDepthWrite = true;\r\n        this.setColor4(\"color\", new Color4(0, 0, 0, 1));\r\n    }\r\n}\r\n"]}