import type { Nullable } from "../types";
import { Vector2, Vector3 } from "../Maths/math.vector";
import type { AbstractMesh } from "../Meshes/abstractMesh";
import type { ImageProcessingConfiguration } from "../Materials/imageProcessingConfiguration";
import { ImageProcessingConfigurationDefines } from "../Materials/imageProcessingConfiguration.defines";
import type { ColorGradient, FactorGradient, Color3Gradient, IValueGradient } from "../Misc/gradients";
import type { BoxParticleEmitter } from "../Particles/EmitterTypes/boxParticleEmitter";
import type { BaseTexture } from "../Materials/Textures/baseTexture";
import { Color4 } from "../Maths/math.color";
import type { ThinEngine } from "../Engines/thinEngine";
import "../Engines/Extensions/engine.dynamicBuffer";
import type { IClipPlanesHolder } from "../Misc/interfaces/iClipPlanesHolder";
import type { Plane } from "../Maths/math.plane";
import type { Animation } from "../Animations/animation";
import type { Scene } from "../scene";
import type { ProceduralTexture } from "../Materials/Textures/Procedurals/proceduralTexture";
import type { RawTexture } from "../Materials/Textures/rawTexture";
import type { IParticleEmitterType } from "./EmitterTypes/IParticleEmitterType";
import type { PointParticleEmitter } from "./EmitterTypes/pointParticleEmitter";
import type { HemisphericParticleEmitter } from "./EmitterTypes/hemisphericParticleEmitter";
import type { SphereDirectedParticleEmitter, SphereParticleEmitter } from "./EmitterTypes/sphereParticleEmitter";
import type { CylinderDirectedParticleEmitter, CylinderParticleEmitter } from "./EmitterTypes/cylinderParticleEmitter";
import type { ConeParticleEmitter } from "./EmitterTypes/coneParticleEmitter";
/**
 * This represents the base class for particle system in Babylon.
 * Particles are often small sprites used to simulate hard-to-reproduce phenomena like fire, smoke, water, or abstract visual effects like magic glitter and faery dust.
 * Particles can take different shapes while emitted like box, sphere, cone or you can write your custom function.
 * @example https://doc.babylonjs.com/features/featuresDeepDive/particles/particle_system/particle_system_intro
 */
export declare class BaseParticleSystem implements IClipPlanesHolder {
    /**
     * Source color is added to the destination color without alpha affecting the result
     */
    static BLENDMODE_ONEONE: number;
    /**
     * Blend current color and particle color using particle’s alpha
     */
    static BLENDMODE_STANDARD: number;
    /**
     * Add current color and particle color multiplied by particle’s alpha
     */
    static BLENDMODE_ADD: number;
    /**
     * Multiply current color with particle color
     */
    static BLENDMODE_MULTIPLY: number;
    /**
     * Multiply current color with particle color then add current color and particle color multiplied by particle’s alpha
     */
    static BLENDMODE_MULTIPLYADD: number;
    /**
     * List of animations used by the particle system.
     */
    animations: Animation[];
    /**
     * Gets or sets the unique id of the particle system
     */
    uniqueId: number;
    /**
     * The id of the Particle system.
     */
    id: string;
    /**
     * The friendly name of the Particle system.
     */
    name: string;
    /**
     * Snippet ID if the particle system was created from the snippet server
     */
    snippetId: string;
    /**
     * The rendering group used by the Particle system to chose when to render.
     */
    renderingGroupId: number;
    /**
     * The emitter represents the Mesh or position we are attaching the particle system to.
     */
    emitter: Nullable<AbstractMesh | Vector3>;
    /**
     * The maximum number of particles to emit per frame
     */
    emitRate: number;
    /**
     * If you want to launch only a few particles at once, that can be done, as well.
     */
    manualEmitCount: number;
    /**
     * The overall motion speed (0.01 is default update speed, faster updates = faster animation)
     */
    updateSpeed: number;
    /**
     * The amount of time the particle system is running (depends of the overall update speed).
     */
    targetStopDuration: number;
    /**
     * Specifies whether the particle system will be disposed once it reaches the end of the animation.
     */
    disposeOnStop: boolean;
    /**
     * Minimum power of emitting particles.
     */
    minEmitPower: number;
    /**
     * Maximum power of emitting particles.
     */
    maxEmitPower: number;
    /**
     * Minimum life time of emitting particles.
     */
    minLifeTime: number;
    /**
     * Maximum life time of emitting particles.
     */
    maxLifeTime: number;
    /**
     * Minimum Size of emitting particles.
     */
    minSize: number;
    /**
     * Maximum Size of emitting particles.
     */
    maxSize: number;
    /**
     * Minimum scale of emitting particles on X axis.
     */
    minScaleX: number;
    /**
     * Maximum scale of emitting particles on X axis.
     */
    maxScaleX: number;
    /**
     * Minimum scale of emitting particles on Y axis.
     */
    minScaleY: number;
    /**
     * Maximum scale of emitting particles on Y axis.
     */
    maxScaleY: number;
    /**
     * Gets or sets the minimal initial rotation in radians.
     */
    minInitialRotation: number;
    /**
     * Gets or sets the maximal initial rotation in radians.
     */
    maxInitialRotation: number;
    /**
     * Minimum angular speed of emitting particles (Z-axis rotation for each particle).
     */
    minAngularSpeed: number;
    /**
     * Maximum angular speed of emitting particles (Z-axis rotation for each particle).
     */
    maxAngularSpeed: number;
    /**
     * The texture used to render each particle. (this can be a spritesheet)
     */
    particleTexture: Nullable<BaseTexture>;
    /**
     * The layer mask we are rendering the particles through.
     */
    layerMask: number;
    /**
     * This can help using your own shader to render the particle system.
     * The according effect will be created
     */
    customShader: any;
    /**
     * By default particle system starts as soon as they are created. This prevents the
     * automatic start to happen and let you decide when to start emitting particles.
     */
    preventAutoStart: boolean;
    /**
     * Gets or sets a boolean indicating that this particle system will allow fog to be rendered on it (false by default)
     */
    applyFog: boolean;
    /** @internal */
    _wasDispatched: boolean;
    protected _rootUrl: string;
    private _noiseTexture;
    /**
     * Gets or sets a texture used to add random noise to particle positions
     */
    get noiseTexture(): Nullable<ProceduralTexture>;
    set noiseTexture(value: Nullable<ProceduralTexture>);
    /** Gets or sets the strength to apply to the noise value (default is (10, 10, 10)) */
    noiseStrength: Vector3;
    /**
     * Callback triggered when the particle animation is ending.
     */
    onAnimationEnd: Nullable<() => void>;
    /**
     * Blend mode use to render the particle, it can be either ParticleSystem.BLENDMODE_ONEONE or ParticleSystem.BLENDMODE_STANDARD.
     */
    blendMode: number;
    /**
     * Forces the particle to write their depth information to the depth buffer. This can help preventing other draw calls
     * to override the particles.
     */
    forceDepthWrite: boolean;
    /** Gets or sets a value indicating how many cycles (or frames) must be executed before first rendering (this value has to be set before starting the system). Default is 0 */
    preWarmCycles: number;
    /** Gets or sets a value indicating the time step multiplier to use in pre-warm mode (default is 1) */
    preWarmStepOffset: number;
    /**
     * If using a spritesheet (isAnimationSheetEnabled) defines the speed of the sprite loop (default is 1 meaning the animation will play once during the entire particle lifetime)
     */
    spriteCellChangeSpeed: number;
    /**
     * If using a spritesheet (isAnimationSheetEnabled) defines the first sprite cell to display
     */
    startSpriteCellID: number;
    /**
     * If using a spritesheet (isAnimationSheetEnabled) defines the last sprite cell to display
     */
    endSpriteCellID: number;
    /**
     * If using a spritesheet (isAnimationSheetEnabled), defines the sprite cell width to use
     */
    spriteCellWidth: number;
    /**
     * If using a spritesheet (isAnimationSheetEnabled), defines the sprite cell height to use
     */
    spriteCellHeight: number;
    /**
     * If using a spritesheet (isAnimationSheetEnabled), defines wether the sprite animation is looping
     */
    spriteCellLoop: boolean;
    /**
     * This allows the system to random pick the start cell ID between startSpriteCellID and endSpriteCellID
     */
    spriteRandomStartCell: boolean;
    /** Gets or sets a Vector2 used to move the pivot (by default (0,0)) */
    translationPivot: Vector2;
    /** @internal */
    _isAnimationSheetEnabled: boolean;
    /**
     * Gets or sets a boolean indicating that hosted animations (in the system.animations array) must be started when system.start() is called
     */
    beginAnimationOnStart: boolean;
    /**
     * Gets or sets the frame to start the animation from when beginAnimationOnStart is true
     */
    beginAnimationFrom: number;
    /**
     * Gets or sets the frame to end the animation on when beginAnimationOnStart is true
     */
    beginAnimationTo: number;
    /**
     * Gets or sets a boolean indicating if animations must loop when beginAnimationOnStart is true
     */
    beginAnimationLoop: boolean;
    /**
     * Gets or sets a world offset applied to all particles
     */
    worldOffset: Vector3;
    /**
     * Gets or sets the active clipplane 1
     */
    clipPlane: Nullable<Plane>;
    /**
     * Gets or sets the active clipplane 2
     */
    clipPlane2: Nullable<Plane>;
    /**
     * Gets or sets the active clipplane 3
     */
    clipPlane3: Nullable<Plane>;
    /**
     * Gets or sets the active clipplane 4
     */
    clipPlane4: Nullable<Plane>;
    /**
     * Gets or sets the active clipplane 5
     */
    clipPlane5: Nullable<Plane>;
    /**
     * Gets or sets the active clipplane 6
     */
    clipPlane6: Nullable<Plane>;
    /**
     * Gets or sets whether an animation sprite sheet is enabled or not on the particle system
     */
    get isAnimationSheetEnabled(): boolean;
    set isAnimationSheetEnabled(value: boolean);
    private _useLogarithmicDepth;
    /**
     * Gets or sets a boolean enabling the use of logarithmic depth buffers, which is good for wide depth buffers.
     */
    get useLogarithmicDepth(): boolean;
    set useLogarithmicDepth(value: boolean);
    /**
     * Get hosting scene
     * @returns the scene
     */
    getScene(): Nullable<Scene>;
    /**
     * You can use gravity if you want to give an orientation to your particles.
     */
    gravity: Vector3;
    protected _colorGradients: Nullable<Array<ColorGradient>>;
    protected _sizeGradients: Nullable<Array<FactorGradient>>;
    protected _lifeTimeGradients: Nullable<Array<FactorGradient>>;
    protected _angularSpeedGradients: Nullable<Array<FactorGradient>>;
    protected _velocityGradients: Nullable<Array<FactorGradient>>;
    protected _limitVelocityGradients: Nullable<Array<FactorGradient>>;
    protected _dragGradients: Nullable<Array<FactorGradient>>;
    protected _emitRateGradients: Nullable<Array<FactorGradient>>;
    protected _startSizeGradients: Nullable<Array<FactorGradient>>;
    protected _rampGradients: Nullable<Array<Color3Gradient>>;
    protected _colorRemapGradients: Nullable<Array<FactorGradient>>;
    protected _alphaRemapGradients: Nullable<Array<FactorGradient>>;
    protected _hasTargetStopDurationDependantGradient(): boolean | null;
    /**
     * Defines the delay in milliseconds before starting the system (0 by default)
     */
    startDelay: number;
    /**
     * Gets the current list of drag gradients.
     * You must use addDragGradient and removeDragGradient to update this list
     * @returns the list of drag gradients
     */
    getDragGradients(): Nullable<Array<FactorGradient>>;
    /** Gets or sets a value indicating the damping to apply if the limit velocity factor is reached */
    limitVelocityDamping: number;
    /**
     * Gets the current list of limit velocity gradients.
     * You must use addLimitVelocityGradient and removeLimitVelocityGradient to update this list
     * @returns the list of limit velocity gradients
     */
    getLimitVelocityGradients(): Nullable<Array<FactorGradient>>;
    /**
     * Gets the current list of color gradients.
     * You must use addColorGradient and removeColorGradient to update this list
     * @returns the list of color gradients
     */
    getColorGradients(): Nullable<Array<ColorGradient>>;
    /**
     * Gets the current list of size gradients.
     * You must use addSizeGradient and removeSizeGradient to update this list
     * @returns the list of size gradients
     */
    getSizeGradients(): Nullable<Array<FactorGradient>>;
    /**
     * Gets the current list of color remap gradients.
     * You must use addColorRemapGradient and removeColorRemapGradient to update this list
     * @returns the list of color remap gradients
     */
    getColorRemapGradients(): Nullable<Array<FactorGradient>>;
    /**
     * Gets the current list of alpha remap gradients.
     * You must use addAlphaRemapGradient and removeAlphaRemapGradient to update this list
     * @returns the list of alpha remap gradients
     */
    getAlphaRemapGradients(): Nullable<Array<FactorGradient>>;
    /**
     * Gets the current list of life time gradients.
     * You must use addLifeTimeGradient and removeLifeTimeGradient to update this list
     * @returns the list of life time gradients
     */
    getLifeTimeGradients(): Nullable<Array<FactorGradient>>;
    /**
     * Gets the current list of angular speed gradients.
     * You must use addAngularSpeedGradient and removeAngularSpeedGradient to update this list
     * @returns the list of angular speed gradients
     */
    getAngularSpeedGradients(): Nullable<Array<FactorGradient>>;
    /**
     * Gets the current list of velocity gradients.
     * You must use addVelocityGradient and removeVelocityGradient to update this list
     * @returns the list of velocity gradients
     */
    getVelocityGradients(): Nullable<Array<FactorGradient>>;
    /**
     * Gets the current list of start size gradients.
     * You must use addStartSizeGradient and removeStartSizeGradient to update this list
     * @returns the list of start size gradients
     */
    getStartSizeGradients(): Nullable<Array<FactorGradient>>;
    /**
     * Gets the current list of emit rate gradients.
     * You must use addEmitRateGradient and removeEmitRateGradient to update this list
     * @returns the list of emit rate gradients
     */
    getEmitRateGradients(): Nullable<Array<FactorGradient>>;
    /**
     * Random direction of each particle after it has been emitted, between direction1 and direction2 vectors.
     * This only works when particleEmitterTyps is a BoxParticleEmitter
     */
    get direction1(): Vector3;
    set direction1(value: Vector3);
    /**
     * Random direction of each particle after it has been emitted, between direction1 and direction2 vectors.
     * This only works when particleEmitterTyps is a BoxParticleEmitter
     */
    get direction2(): Vector3;
    set direction2(value: Vector3);
    /**
     * Minimum box point around our emitter. Our emitter is the center of particles source, but if you want your particles to emit from more than one point, then you can tell it to do so.
     * This only works when particleEmitterTyps is a BoxParticleEmitter
     */
    get minEmitBox(): Vector3;
    set minEmitBox(value: Vector3);
    /**
     * Maximum box point around our emitter. Our emitter is the center of particles source, but if you want your particles to emit from more than one point, then you can tell it to do so.
     * This only works when particleEmitterTyps is a BoxParticleEmitter
     */
    get maxEmitBox(): Vector3;
    set maxEmitBox(value: Vector3);
    /**
     * Random color of each particle after it has been emitted, between color1 and color2 vectors
     */
    color1: Color4;
    /**
     * Random color of each particle after it has been emitted, between color1 and color2 vectors
     */
    color2: Color4;
    /**
     * Color the particle will have at the end of its lifetime
     */
    colorDead: Color4;
    /**
     * An optional mask to filter some colors out of the texture, or filter a part of the alpha channel
     */
    textureMask: Color4;
    /**
     * The particle emitter type defines the emitter used by the particle system.
     * It can be for example box, sphere, or cone...
     */
    particleEmitterType: IParticleEmitterType;
    /** @internal */
    _isSubEmitter: boolean;
    /** @internal */
    _billboardMode: number;
    /**
     * Gets or sets the billboard mode to use when isBillboardBased = true.
     * Value can be: ParticleSystem.BILLBOARDMODE_ALL, ParticleSystem.BILLBOARDMODE_Y, ParticleSystem.BILLBOARDMODE_STRETCHED
     */
    get billboardMode(): number;
    set billboardMode(value: number);
    /** @internal */
    _isBillboardBased: boolean;
    /**
     * Gets or sets a boolean indicating if the particles must be rendered as billboard or aligned with the direction
     */
    get isBillboardBased(): boolean;
    set isBillboardBased(value: boolean);
    /**
     * The scene the particle system belongs to.
     */
    protected _scene: Nullable<Scene>;
    /**
     * The engine the particle system belongs to.
     */
    protected _engine: ThinEngine;
    /**
     * Local cache of defines for image processing.
     */
    protected _imageProcessingConfigurationDefines: ImageProcessingConfigurationDefines;
    /**
     * Default configuration related to image processing available in the standard Material.
     */
    protected _imageProcessingConfiguration: Nullable<ImageProcessingConfiguration>;
    /**
     * Gets the image processing configuration used either in this material.
     */
    get imageProcessingConfiguration(): Nullable<ImageProcessingConfiguration>;
    /**
     * Sets the Default image processing configuration used either in the this material.
     *
     * If sets to null, the scene one is in use.
     */
    set imageProcessingConfiguration(value: Nullable<ImageProcessingConfiguration>);
    /**
     * Attaches a new image processing configuration to the Standard Material.
     * @param configuration
     */
    protected _attachImageProcessingConfiguration(configuration: Nullable<ImageProcessingConfiguration>): void;
    /** @internal */
    protected _reset(): void;
    /**
     * @internal
     */
    protected _removeGradientAndTexture(gradient: number, gradients: Nullable<IValueGradient[]>, texture: Nullable<RawTexture>): BaseParticleSystem;
    /**
     * Instantiates a particle system.
     * Particles are often small sprites used to simulate hard-to-reproduce phenomena like fire, smoke, water, or abstract visual effects like magic glitter and faery dust.
     * @param name The name of the particle system
     */
    constructor(name: string);
    /**
     * Creates a Point Emitter for the particle system (emits directly from the emitter position)
     * @param direction1 Particles are emitted between the direction1 and direction2 from within the box
     * @param direction2 Particles are emitted between the direction1 and direction2 from within the box
     */
    createPointEmitter(direction1: Vector3, direction2: Vector3): PointParticleEmitter;
    /**
     * Creates a Hemisphere Emitter for the particle system (emits along the hemisphere radius)
     * @param radius The radius of the hemisphere to emit from
     * @param radiusRange The range of the hemisphere to emit from [0-1] 0 Surface Only, 1 Entire Radius
     */
    createHemisphericEmitter(radius?: number, radiusRange?: number): HemisphericParticleEmitter;
    /**
     * Creates a Sphere Emitter for the particle system (emits along the sphere radius)
     * @param radius The radius of the sphere to emit from
     * @param radiusRange The range of the sphere to emit from [0-1] 0 Surface Only, 1 Entire Radius
     */
    createSphereEmitter(radius?: number, radiusRange?: number): SphereParticleEmitter;
    /**
     * Creates a Directed Sphere Emitter for the particle system (emits between direction1 and direction2)
     * @param radius The radius of the sphere to emit from
     * @param direction1 Particles are emitted between the direction1 and direction2 from within the sphere
     * @param direction2 Particles are emitted between the direction1 and direction2 from within the sphere
     */
    createDirectedSphereEmitter(radius?: number, direction1?: Vector3, direction2?: Vector3): SphereDirectedParticleEmitter;
    /**
     * Creates a Cylinder Emitter for the particle system (emits from the cylinder to the particle position)
     * @param radius The radius of the emission cylinder
     * @param height The height of the emission cylinder
     * @param radiusRange The range of emission [0-1] 0 Surface only, 1 Entire Radius
     * @param directionRandomizer How much to randomize the particle direction [0-1]
     */
    createCylinderEmitter(radius?: number, height?: number, radiusRange?: number, directionRandomizer?: number): CylinderParticleEmitter;
    /**
     * Creates a Directed Cylinder Emitter for the particle system (emits between direction1 and direction2)
     * @param radius The radius of the cylinder to emit from
     * @param height The height of the emission cylinder
     * @param radiusRange the range of the emission cylinder [0-1] 0 Surface only, 1 Entire Radius (1 by default)
     * @param direction1 Particles are emitted between the direction1 and direction2 from within the cylinder
     * @param direction2 Particles are emitted between the direction1 and direction2 from within the cylinder
     */
    createDirectedCylinderEmitter(radius?: number, height?: number, radiusRange?: number, direction1?: Vector3, direction2?: Vector3): CylinderDirectedParticleEmitter;
    /**
     * Creates a Cone Emitter for the particle system (emits from the cone to the particle position)
     * @param radius The radius of the cone to emit from
     * @param angle The base angle of the cone
     */
    createConeEmitter(radius?: number, angle?: number): ConeParticleEmitter;
    /**
     * Creates a Box Emitter for the particle system. (emits between direction1 and direction2 from withing the box defined by minEmitBox and maxEmitBox)
     * @param direction1 Particles are emitted between the direction1 and direction2 from within the box
     * @param direction2 Particles are emitted between the direction1 and direction2 from within the box
     * @param minEmitBox Particles are emitted from the box between minEmitBox and maxEmitBox
     * @param maxEmitBox  Particles are emitted from the box between minEmitBox and maxEmitBox
     */
    createBoxEmitter(direction1: Vector3, direction2: Vector3, minEmitBox: Vector3, maxEmitBox: Vector3): BoxParticleEmitter;
}
