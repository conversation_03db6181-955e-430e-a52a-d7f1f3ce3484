{"version": 3, "file": "polygonBuilder.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Builders/polygonBuilder.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAC3D,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,kBAAkB,EAAE,MAAM,gBAAgB,CAAC;AAEpD,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACpD,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAIzE;;;;;;;;;;;GAWG;AACH,MAAM,UAAU,uBAAuB,CAAC,OAAa,EAAE,eAAuB,EAAE,GAAe,EAAE,OAAkB,EAAE,QAAkB,EAAE,OAAiB,EAAE,GAAa;IACrK,MAAM,MAAM,GAAc,GAAG,IAAI,IAAI,KAAK,CAAU,CAAC,CAAC,CAAC;IACvD,MAAM,UAAU,GAAG,OAAO,CAAC;IAC3B,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,IAAI,GAAY,GAAG,IAAI,KAAK,CAAC;IAEnC,0CAA0C;IAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACxB,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YACzB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACvC;QACD,IAAI,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YAC3C,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC1C;KACJ;IAED,MAAM,SAAS,GAAe,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IACjF,MAAM,OAAO,GAAe,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;IAC7E,MAAM,GAAG,GAAe,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IACrE,MAAM,OAAO,GAAiB,OAAO,CAAC,UAAU,EAAE,CAAC;IACnD,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IACxC,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;IACrB,IAAI,IAAI,EAAE;QACN,KAAK,IAAI,GAAG,GAAG,UAAU,EAAE,GAAG,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;YAC7D,KAAK,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YACtD,KAAK,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC9D,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC;YAChD,QAAQ,IAAI,IAAI,CAAC;YACjB,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC3B;KACJ;IACD,gCAAgC;IAChC,IAAI,GAAG,GAAW,CAAC,CAAC;IACpB,IAAI,IAAI,GAAW,CAAC,CAAC;IACrB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE;QACpD,kBAAkB;QAClB,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE;YACtC,IAAI,GAAG,CAAC,CAAC;SACZ;QACD,iBAAiB;QACjB,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE;YAC1C,IAAI,GAAG,CAAC,CAAC;SACZ;QACD,oBAAoB;QACpB,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE;YAC1C,IAAI,GAAG,CAAC,CAAC;SACZ;QACD,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC;QAChB,IAAI,IAAI,KAAK,CAAC,EAAE;YACZ,IAAI,GAAG,GAAG,GAAG,UAAU,CAAC;YACxB,IAAI,IAAI,GAAG,CAAC,GAAG,GAAG,EAAE;gBAChB,IAAI,IAAI,EAAE;oBACN,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;iBACnH;qBAAM;oBACH,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACjC;aACJ;iBAAM;gBACH,IAAI,IAAI,EAAE;oBACN,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;iBACvH;qBAAM;oBACH,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACjC;aACJ;YACD,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE;gBAChB,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aAC7G;iBAAM;gBACH,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aAC7G;SACJ;aAAM;YACH,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnF,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE/F,IAAI,oBAAoB,CAAC,yBAAyB,EAAE;gBAChD,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;aAC7C;SACJ;QACD,IAAI,UAAU,EAAE;YACZ,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/F;KACJ;IAED,QAAQ;IACR,UAAU,CAAC,aAAa,CAAC,eAAe,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAE/F,SAAS;IACT,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IACpC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;IACjC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;IAErB,IAAI,UAAU,EAAE;QACZ,MAAM,WAAW,GAAG,eAAe,KAAK,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC/F,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC;KACnC;IAED,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,aAAa,CACzB,IAAY,EACZ,OAYC,EACD,QAAyB,IAAI,EAC7B,eAAe,GAAG,MAAM;IAExB,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACnF,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC5B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;IAClC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC;IACjC,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,CAAC,CAAC;IAC3D,MAAM,QAAQ,GAAmB,EAAE,CAAC;IACpC,IAAI,IAAI,GAAmB,EAAE,CAAC;IAE9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACrD;IACD,MAAM,OAAO,GAAG,UAAU,CAAC;IAC3B,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE;QACvE,QAAQ,CAAC,GAAG,EAAE,CAAC;KAClB;IAED,MAAM,oBAAoB,GAAG,IAAI,kBAAkB,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,IAAI,WAAW,CAAC,gBAAiB,EAAE,eAAe,CAAC,CAAC;IAC7H,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;QACzC,IAAI,GAAG,EAAE,CAAC;QACV,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;YACvD,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACtE;QACD,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KACtC;IACD,gGAAgG;IAChG,MAAM,OAAO,GAAG,oBAAoB,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;IAC7E,OAAO,CAAC,+BAA+B,GAAG,OAAO,CAAC,eAAe,CAAC;IAClE,MAAM,UAAU,GAAG,uBAAuB,CAAC,OAAO,EAAE,OAAO,CAAC,eAAe,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAClK,UAAU,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAEnD,OAAO,OAAO,CAAC;AACnB,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,cAAc,CAC1B,IAAY,EACZ,OAWC,EACD,QAAyB,IAAI,EAC7B,eAAe,GAAG,MAAM;IAExB,OAAO,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;AAChE,CAAC;AACD;;;GAGG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG;IAC1B,cAAc;IACd,aAAa;CAChB,CAAC;AAEF,UAAU,CAAC,aAAa,GAAG,uBAAuB,CAAC;AACnD,IAAI,CAAC,aAAa,GAAG,CAAC,IAAY,EAAE,KAAgB,EAAE,KAAY,EAAE,KAAmB,EAAE,SAAmB,EAAE,eAAwB,EAAE,eAAe,GAAG,MAAM,EAAQ,EAAE;IACtK,MAAM,OAAO,GAAG;QACZ,KAAK,EAAE,KAAK;QACZ,KAAK,EAAE,KAAK;QACZ,SAAS,EAAE,SAAS;QACpB,eAAe,EAAE,eAAe;KACnC,CAAC;IACF,OAAO,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;AAChE,CAAC,CAAC;AAEF,IAAI,CAAC,cAAc,GAAG,CAClB,IAAY,EACZ,KAAgB,EAChB,KAAa,EACb,KAAY,EACZ,KAAmB,EACnB,SAAmB,EACnB,eAAwB,EACxB,eAAe,GAAG,MAAM,EACpB,EAAE;IACN,MAAM,OAAO,GAAG;QACZ,KAAK,EAAE,KAAK;QACZ,KAAK,EAAE,KAAK;QACZ,KAAK,EAAE,KAAK;QACZ,SAAS,EAAE,SAAS;QACpB,eAAe,EAAE,eAAe;KACnC,CAAC;IACF,OAAO,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;AACjE,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Vector3 } from \"../../Maths/math.vector\";\r\nimport { Vector2, Vector4 } from \"../../Maths/math.vector\";\r\nimport { Color4 } from \"../../Maths/math.color\";\r\nimport { Mesh } from \"../mesh\";\r\nimport { VertexData } from \"../mesh.vertexData\";\r\nimport { PolygonMeshBuilder } from \"../polygonMesh\";\r\nimport type { FloatArray, IndicesArray, Nullable } from \"../../types\";\r\nimport { VertexBuffer } from \"../../Buffers/buffer\";\r\nimport { EngineStore } from \"../../Engines/engineStore\";\r\nimport { CompatibilityOptions } from \"../../Compat/compatibilityOptions\";\r\n\r\ndeclare let earcut: any;\r\n\r\n/**\r\n * Creates the VertexData for an irregular Polygon in the XoZ plane using a mesh built by polygonTriangulation.build()\r\n * All parameters are provided by CreatePolygon as needed\r\n * @param polygon a mesh built from polygonTriangulation.build()\r\n * @param sideOrientation takes the values Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n * @param fUV an array of Vector4 elements used to set different images to the top, rings and bottom respectively\r\n * @param fColors an array of Color3 elements used to set different colors to the top, rings and bottom respectively\r\n * @param frontUVs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the front side, optional, default vector4 (0, 0, 1, 1)\r\n * @param backUVs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the back side, optional, default vector4 (0, 0, 1, 1)\r\n * @param wrp a boolean, default false, when true and fUVs used texture is wrapped around all sides, when false texture is applied side\r\n * @returns the VertexData of the Polygon\r\n */\r\nexport function CreatePolygonVertexData(polygon: Mesh, sideOrientation: number, fUV?: Vector4[], fColors?: Color4[], frontUVs?: Vector4, backUVs?: Vector4, wrp?: boolean) {\r\n    const faceUV: Vector4[] = fUV || new Array<Vector4>(3);\r\n    const faceColors = fColors;\r\n    const colors = [];\r\n    const wrap: boolean = wrp || false;\r\n\r\n    // default face colors and UV if undefined\r\n    for (let f = 0; f < 3; f++) {\r\n        if (faceUV[f] === undefined) {\r\n            faceUV[f] = new Vector4(0, 0, 1, 1);\r\n        }\r\n        if (faceColors && faceColors[f] === undefined) {\r\n            faceColors[f] = new Color4(1, 1, 1, 1);\r\n        }\r\n    }\r\n\r\n    const positions = <FloatArray>polygon.getVerticesData(VertexBuffer.PositionKind);\r\n    const normals = <FloatArray>polygon.getVerticesData(VertexBuffer.NormalKind);\r\n    const uvs = <FloatArray>polygon.getVerticesData(VertexBuffer.UVKind);\r\n    const indices = <IndicesArray>polygon.getIndices();\r\n    const startIndex = positions.length / 9;\r\n    let disp = 0;\r\n    let distX = 0;\r\n    let distZ = 0;\r\n    let dist = 0;\r\n    let totalLen = 0;\r\n    const cumulate = [0];\r\n    if (wrap) {\r\n        for (let idx = startIndex; idx < positions.length / 3; idx += 4) {\r\n            distX = positions[3 * (idx + 2)] - positions[3 * idx];\r\n            distZ = positions[3 * (idx + 2) + 2] - positions[3 * idx + 2];\r\n            dist = Math.sqrt(distX * distX + distZ * distZ);\r\n            totalLen += dist;\r\n            cumulate.push(totalLen);\r\n        }\r\n    }\r\n    // set face colours and textures\r\n    let idx: number = 0;\r\n    let face: number = 0;\r\n    for (let index = 0; index < normals.length; index += 3) {\r\n        //Edge Face  no. 1\r\n        if (Math.abs(normals[index + 1]) < 0.001) {\r\n            face = 1;\r\n        }\r\n        //Top Face  no. 0\r\n        if (Math.abs(normals[index + 1] - 1) < 0.001) {\r\n            face = 0;\r\n        }\r\n        //Bottom Face  no. 2\r\n        if (Math.abs(normals[index + 1] + 1) < 0.001) {\r\n            face = 2;\r\n        }\r\n        idx = index / 3;\r\n        if (face === 1) {\r\n            disp = idx - startIndex;\r\n            if (disp % 4 < 1.5) {\r\n                if (wrap) {\r\n                    uvs[2 * idx] = faceUV[face].x + ((faceUV[face].z - faceUV[face].x) * cumulate[Math.floor(disp / 4)]) / totalLen;\r\n                } else {\r\n                    uvs[2 * idx] = faceUV[face].x;\r\n                }\r\n            } else {\r\n                if (wrap) {\r\n                    uvs[2 * idx] = faceUV[face].x + ((faceUV[face].z - faceUV[face].x) * cumulate[Math.floor(disp / 4) + 1]) / totalLen;\r\n                } else {\r\n                    uvs[2 * idx] = faceUV[face].z;\r\n                }\r\n            }\r\n            if (disp % 2 === 0) {\r\n                uvs[2 * idx + 1] = CompatibilityOptions.UseOpenGLOrientationForUV ? 1.0 - faceUV[face].w : faceUV[face].w;\r\n            } else {\r\n                uvs[2 * idx + 1] = CompatibilityOptions.UseOpenGLOrientationForUV ? 1.0 - faceUV[face].y : faceUV[face].y;\r\n            }\r\n        } else {\r\n            uvs[2 * idx] = (1 - uvs[2 * idx]) * faceUV[face].x + uvs[2 * idx] * faceUV[face].z;\r\n            uvs[2 * idx + 1] = (1 - uvs[2 * idx + 1]) * faceUV[face].y + uvs[2 * idx + 1] * faceUV[face].w;\r\n\r\n            if (CompatibilityOptions.UseOpenGLOrientationForUV) {\r\n                uvs[2 * idx + 1] = 1.0 - uvs[2 * idx + 1];\r\n            }\r\n        }\r\n        if (faceColors) {\r\n            colors.push(faceColors[face].r, faceColors[face].g, faceColors[face].b, faceColors[face].a);\r\n        }\r\n    }\r\n\r\n    // sides\r\n    VertexData._ComputeSides(sideOrientation, positions, indices, normals, uvs, frontUVs, backUVs);\r\n\r\n    // Result\r\n    const vertexData = new VertexData();\r\n    vertexData.indices = indices;\r\n    vertexData.positions = positions;\r\n    vertexData.normals = normals;\r\n    vertexData.uvs = uvs;\r\n\r\n    if (faceColors) {\r\n        const totalColors = sideOrientation === VertexData.DOUBLESIDE ? colors.concat(colors) : colors;\r\n        vertexData.colors = totalColors;\r\n    }\r\n\r\n    return vertexData;\r\n}\r\n\r\n/**\r\n * Creates a polygon mesh\r\n * The polygon's shape will depend on the input parameters and is constructed parallel to a ground mesh\r\n * * The parameter `shape` is a required array of successive Vector3 representing the corners of the polygon in th XoZ plane, that is y = 0 for all vectors\r\n * * You can set the mesh side orientation with the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n * * The mesh can be set to updatable with the boolean parameter `updatable` (default false) if its internal geometry is supposed to change once created\r\n * * If you create a double-sided mesh, you can choose what parts of the texture image to crop and stick respectively on the front and the back sides with the parameters `frontUVs` and `backUVs` (Vector4)\r\n * * Remember you can only change the shape positions, not their number when updating a polygon\r\n * @param name defines the name of the mesh\r\n * @param options defines the options used to create the mesh\r\n * @param scene defines the hosting scene\r\n * @param earcutInjection can be used to inject your own earcut reference\r\n * @returns the polygon mesh\r\n */\r\nexport function CreatePolygon(\r\n    name: string,\r\n    options: {\r\n        shape: Vector3[];\r\n        holes?: Vector3[][];\r\n        depth?: number;\r\n        smoothingThreshold?: number;\r\n        faceUV?: Vector4[];\r\n        faceColors?: Color4[];\r\n        updatable?: boolean;\r\n        sideOrientation?: number;\r\n        frontUVs?: Vector4;\r\n        backUVs?: Vector4;\r\n        wrap?: boolean;\r\n    },\r\n    scene: Nullable<Scene> = null,\r\n    earcutInjection = earcut\r\n): Mesh {\r\n    options.sideOrientation = Mesh._GetDefaultSideOrientation(options.sideOrientation);\r\n    const shape = options.shape;\r\n    const holes = options.holes || [];\r\n    const depth = options.depth || 0;\r\n    const smoothingThreshold = options.smoothingThreshold || 2;\r\n    const contours: Array<Vector2> = [];\r\n    let hole: Array<Vector2> = [];\r\n\r\n    for (let i = 0; i < shape.length; i++) {\r\n        contours[i] = new Vector2(shape[i].x, shape[i].z);\r\n    }\r\n    const epsilon = 0.00000001;\r\n    if (contours[0].equalsWithEpsilon(contours[contours.length - 1], epsilon)) {\r\n        contours.pop();\r\n    }\r\n\r\n    const polygonTriangulation = new PolygonMeshBuilder(name, contours, scene || EngineStore.LastCreatedScene!, earcutInjection);\r\n    for (let hNb = 0; hNb < holes.length; hNb++) {\r\n        hole = [];\r\n        for (let hPoint = 0; hPoint < holes[hNb].length; hPoint++) {\r\n            hole.push(new Vector2(holes[hNb][hPoint].x, holes[hNb][hPoint].z));\r\n        }\r\n        polygonTriangulation.addHole(hole);\r\n    }\r\n    //updatability is set during applyToMesh; setting to true in triangulation build produces errors\r\n    const polygon = polygonTriangulation.build(false, depth, smoothingThreshold);\r\n    polygon._originalBuilderSideOrientation = options.sideOrientation;\r\n    const vertexData = CreatePolygonVertexData(polygon, options.sideOrientation, options.faceUV, options.faceColors, options.frontUVs, options.backUVs, options.wrap);\r\n    vertexData.applyToMesh(polygon, options.updatable);\r\n\r\n    return polygon;\r\n}\r\n\r\n/**\r\n * Creates an extruded polygon mesh, with depth in the Y direction.\r\n * * You can set different colors and different images to the top, bottom and extruded side by using the parameters `faceColors` (an array of 3 Color3 elements) and `faceUV` (an array of 3 Vector4 elements)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/texturePerBoxFace\r\n * @param name defines the name of the mesh\r\n * @param options defines the options used to create the mesh\r\n * @param scene defines the hosting scene\r\n * @param earcutInjection can be used to inject your own earcut reference\r\n * @returns the polygon mesh\r\n */\r\nexport function ExtrudePolygon(\r\n    name: string,\r\n    options: {\r\n        shape: Vector3[];\r\n        holes?: Vector3[][];\r\n        depth?: number;\r\n        faceUV?: Vector4[];\r\n        faceColors?: Color4[];\r\n        updatable?: boolean;\r\n        sideOrientation?: number;\r\n        frontUVs?: Vector4;\r\n        backUVs?: Vector4;\r\n        wrap?: boolean;\r\n    },\r\n    scene: Nullable<Scene> = null,\r\n    earcutInjection = earcut\r\n): Mesh {\r\n    return CreatePolygon(name, options, scene, earcutInjection);\r\n}\r\n/**\r\n * Class containing static functions to help procedurally build meshes\r\n * @deprecated use the functions directly from the module\r\n */\r\nexport const PolygonBuilder = {\r\n    ExtrudePolygon,\r\n    CreatePolygon,\r\n};\r\n\r\nVertexData.CreatePolygon = CreatePolygonVertexData;\r\nMesh.CreatePolygon = (name: string, shape: Vector3[], scene: Scene, holes?: Vector3[][], updatable?: boolean, sideOrientation?: number, earcutInjection = earcut): Mesh => {\r\n    const options = {\r\n        shape: shape,\r\n        holes: holes,\r\n        updatable: updatable,\r\n        sideOrientation: sideOrientation,\r\n    };\r\n    return CreatePolygon(name, options, scene, earcutInjection);\r\n};\r\n\r\nMesh.ExtrudePolygon = (\r\n    name: string,\r\n    shape: Vector3[],\r\n    depth: number,\r\n    scene: Scene,\r\n    holes?: Vector3[][],\r\n    updatable?: boolean,\r\n    sideOrientation?: number,\r\n    earcutInjection = earcut\r\n): Mesh => {\r\n    const options = {\r\n        shape: shape,\r\n        holes: holes,\r\n        depth: depth,\r\n        updatable: updatable,\r\n        sideOrientation: sideOrientation,\r\n    };\r\n    return ExtrudePolygon(name, options, scene, earcutInjection);\r\n};\r\n"]}