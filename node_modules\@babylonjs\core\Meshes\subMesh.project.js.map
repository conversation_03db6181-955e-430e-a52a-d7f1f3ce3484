{"version": 3, "file": "subMesh.project.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Meshes/subMesh.project.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE3D,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAqBpC;;GAEG;AACH,OAAO,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAAU,MAAe,EAAE,SAAoB,EAAE,OAAqB,EAAE,IAAY,EAAE,YAAqB,EAAE,GAAY;IAClK,iBAAiB;IACjB,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACnC,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAClC,IAAI,QAAQ,GAAG,CAAC,QAAQ,CAAC;IAEzB,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,KAAK,IAAI,IAAI,EAAE;QACrG,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAClC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAElC,IAAI,YAAY,IAAI,MAAM,KAAK,UAAU,EAAE;YACvC,KAAK,IAAI,CAAC,CAAC;YACX,SAAS;SACZ;QAED,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7B,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7B,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QAE7B,8DAA8D;QAC9D,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE;YACnB,SAAS;SACZ;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,sBAAsB,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;QACxE,IAAI,OAAO,GAAG,QAAQ,EAAE;YACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACnB,QAAQ,GAAG,OAAO,CAAC;SACtB;KACJ;IAED,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAEnB,OAAO,QAAQ,CAAC;AACpB,CAAC,CAAC;AAEF;;GAEG;AACH,OAAO,CAAC,SAAS,CAAC,iCAAiC,GAAG,UAAU,MAAe,EAAE,SAAoB,EAAE,OAAqB,EAAE,GAAY;IACtI,iBAAiB;IACjB,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACnC,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAClC,IAAI,QAAQ,GAAG,CAAC,QAAQ,CAAC;IAEzB,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,KAAK,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,KAAK,IAAI,CAAC,EAAE;QAC9F,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;QAC5B,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAChC,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAEhC,MAAM,OAAO,GAAG,OAAO,CAAC,sBAAsB,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;QACxE,IAAI,OAAO,GAAG,QAAQ,EAAE;YACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACnB,QAAQ,GAAG,OAAO,CAAC;SACtB;KACJ;IAED,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAEnB,OAAO,QAAQ,CAAC;AACpB,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,MAAe,EAAE,SAAoB,EAAE,OAAqB,EAAE,GAAY;IACjH,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IACpC,IAAI,CAAC,QAAQ,EAAE;QACX,OAAO,CAAC,CAAC,CAAC;KACb;IACD,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,YAAY,GAAG,KAAK,CAAC;IAEzB,QAAQ,QAAQ,CAAC,QAAQ,EAAE;QACvB,KAAK,SAAS,CAAC,0BAA0B,CAAC;QAC1C,KAAK,SAAS,CAAC,yBAAyB,CAAC;QACzC,KAAK,SAAS,CAAC,0BAA0B,CAAC;QAC1C,KAAK,SAAS,CAAC,4BAA4B;YACvC,OAAO,CAAC,CAAC,CAAC;QACd,KAAK,SAAS,CAAC,8BAA8B;YACzC,IAAI,GAAG,CAAC,CAAC;YACT,YAAY,GAAG,IAAI,CAAC;YACpB,MAAM;QACV;YACI,MAAM;KACb;IAED,wCAAwC;IACxC,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,CAAC,yBAAyB,EAAE;QAC3D,OAAO,CAAC,CAAC,CAAC;KACb;SAAM;QACH,6BAA6B;QAC7B,IAAI,CAAC,OAAO,CAAC,MAAM,IAAK,IAAY,CAAC,KAAK,CAAC,UAAU,EAAE;YACnD,OAAO,IAAI,CAAC,iCAAiC,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;SAClF;QAED,OAAO,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;KAC7F;AACL,CAAC,CAAC", "sourcesContent": ["import { Constants } from \"../Engines/constants\";\r\nimport { TmpVectors, Vector3 } from \"../Maths/math.vector\";\r\nimport type { IndicesArray } from \"../types\";\r\nimport { SubMesh } from \"./subMesh\";\r\n\r\ndeclare module \"./subMesh\" {\r\n    export interface SubMesh {\r\n        /** @internal */\r\n        _projectOnTrianglesToRef(vector: Vector3, positions: Vector3[], indices: IndicesArray, step: number, checkStopper: boolean, ref: Vector3): number;\r\n        /** @internal */\r\n        _projectOnUnIndexedTrianglesToRef(vector: Vector3, positions: Vector3[], indices: IndicesArray, ref: Vector3): number;\r\n        /**\r\n         * Projects a point on this submesh and stores the result in \"ref\"\r\n         *\r\n         * @param vector point to project\r\n         * @param positions defines mesh's positions array\r\n         * @param indices defines mesh's indices array\r\n         * @param ref vector that will store the result\r\n         * @returns distance from the point and the submesh, or -1 if the mesh rendering mode doesn't support projections\r\n         */\r\n        projectToRef(vector: Vector3, positions: Vector3[], indices: IndicesArray, ref: Vector3): number;\r\n    }\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nSubMesh.prototype._projectOnTrianglesToRef = function (vector: Vector3, positions: Vector3[], indices: IndicesArray, step: number, checkStopper: boolean, ref: Vector3): number {\r\n    // Triangles test\r\n    const proj = TmpVectors.Vector3[0];\r\n    const tmp = TmpVectors.Vector3[1];\r\n    let distance = +Infinity;\r\n\r\n    for (let index = this.indexStart; index < this.indexStart + this.indexCount - (3 - step); index += step) {\r\n        const indexA = indices[index];\r\n        const indexB = indices[index + 1];\r\n        const indexC = indices[index + 2];\r\n\r\n        if (checkStopper && indexC === 0xffffffff) {\r\n            index += 2;\r\n            continue;\r\n        }\r\n\r\n        const p0 = positions[indexA];\r\n        const p1 = positions[indexB];\r\n        const p2 = positions[indexC];\r\n\r\n        // stay defensive and don't check against undefined positions.\r\n        if (!p0 || !p1 || !p2) {\r\n            continue;\r\n        }\r\n\r\n        const tmpDist = Vector3.ProjectOnTriangleToRef(vector, p0, p1, p2, tmp);\r\n        if (tmpDist < distance) {\r\n            proj.copyFrom(tmp);\r\n            distance = tmpDist;\r\n        }\r\n    }\r\n\r\n    ref.copyFrom(proj);\r\n\r\n    return distance;\r\n};\r\n\r\n/**\r\n * @internal\r\n */\r\nSubMesh.prototype._projectOnUnIndexedTrianglesToRef = function (vector: Vector3, positions: Vector3[], indices: IndicesArray, ref: Vector3): number {\r\n    // Triangles test\r\n    const proj = TmpVectors.Vector3[0];\r\n    const tmp = TmpVectors.Vector3[1];\r\n    let distance = +Infinity;\r\n\r\n    for (let index = this.verticesStart; index < this.verticesStart + this.verticesCount; index += 3) {\r\n        const p0 = positions[index];\r\n        const p1 = positions[index + 1];\r\n        const p2 = positions[index + 2];\r\n\r\n        const tmpDist = Vector3.ProjectOnTriangleToRef(vector, p0, p1, p2, tmp);\r\n        if (tmpDist < distance) {\r\n            proj.copyFrom(tmp);\r\n            distance = tmpDist;\r\n        }\r\n    }\r\n\r\n    ref.copyFrom(proj);\r\n\r\n    return distance;\r\n};\r\n\r\nSubMesh.prototype.projectToRef = function (vector: Vector3, positions: Vector3[], indices: IndicesArray, ref: Vector3): number {\r\n    const material = this.getMaterial();\r\n    if (!material) {\r\n        return -1;\r\n    }\r\n    let step = 3;\r\n    let checkStopper = false;\r\n\r\n    switch (material.fillMode) {\r\n        case Constants.MATERIAL_PointListDrawMode:\r\n        case Constants.MATERIAL_LineLoopDrawMode:\r\n        case Constants.MATERIAL_LineStripDrawMode:\r\n        case Constants.MATERIAL_TriangleFanDrawMode:\r\n            return -1;\r\n        case Constants.MATERIAL_TriangleStripDrawMode:\r\n            step = 1;\r\n            checkStopper = true;\r\n            break;\r\n        default:\r\n            break;\r\n    }\r\n\r\n    // LineMesh first as it's also a Mesh...\r\n    if (material.fillMode === Constants.MATERIAL_LineListDrawMode) {\r\n        return -1;\r\n    } else {\r\n        // Check if mesh is unindexed\r\n        if (!indices.length && (this as any)._mesh._unIndexed) {\r\n            return this._projectOnUnIndexedTrianglesToRef(vector, positions, indices, ref);\r\n        }\r\n\r\n        return this._projectOnTrianglesToRef(vector, positions, indices, step, checkStopper, ref);\r\n    }\r\n};\r\n"]}