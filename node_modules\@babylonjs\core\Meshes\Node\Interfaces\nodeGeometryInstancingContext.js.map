{"version": 3, "file": "nodeGeometryInstancingContext.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Meshes/Node/Interfaces/nodeGeometryInstancingContext.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\r\n * Interface used to convey instancing context through execution nodes\r\n */\r\nexport interface INodeGeometryInstancingContext {\r\n    /**\r\n     * Gets the current instance index in the current flow\r\n     * @returns the current index\r\n     */\r\n    getInstanceIndex(): number;\r\n}\r\n"]}