{"version": 3, "file": "octreeBlock.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Culling/Octrees/octreeBlock.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAElD,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAaxD;;;GAGG;AACH,MAAM,OAAO,WAAW;IAmBpB;;;;;;;;OAQG;IACH,YAAY,QAAiB,EAAE,QAAiB,EAAE,QAAgB,EAAE,KAAa,EAAE,QAAgB,EAAE,YAAuD;QA3B5J;;WAEG;QACI,YAAO,GAAQ,EAAE,CAAC;QAYjB,qBAAgB,GAAG,IAAI,KAAK,EAAW,CAAC;QAa5C,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE1B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,WAAW;IAEX;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,UAAU;IAEV;;;OAGG;IACI,QAAQ,CAAC,KAAQ;QACpB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACjC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aACzB;YACD,OAAO;SACV;QAED,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAEhC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE;YACrE,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;IACL,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,KAAQ;QACvB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACjC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aAC5B;YACD,OAAO;SACV;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE/C,IAAI,UAAU,GAAG,CAAC,CAAC,EAAE;YACjB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,OAAY;QAC1B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACjD,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACvB;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,aAAsB,EAAE,SAAmC,EAAE,cAAwB;QAC/F,IAAI,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC,EAAE;YAC/D,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACjC,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;iBAC1D;gBACD,OAAO;aACV;YAED,IAAI,cAAc,EAAE;gBAChB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAClC;iBAAM;gBACH,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACjD;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,UAAU,CAAC,YAAqB,EAAE,YAAoB,EAAE,SAAmC,EAAE,cAAwB;QACxH,IAAI,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE;YAC1F,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACjC,KAAK,CAAC,UAAU,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;iBAC3E;gBACD,OAAO;aACV;YAED,IAAI,cAAc,EAAE;gBAChB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAClC;iBAAM;gBACH,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACjD;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,GAAQ,EAAE,SAAmC;QAC9D,IAAI,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;YACzD,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACjC,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;iBACvC;gBACD,OAAO;aACV;YACD,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACjD;IACL,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC/I,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,aAAa,CACvB,QAAiB,EACjB,QAAiB,EACjB,OAAY,EACZ,gBAAwB,EACxB,YAAoB,EACpB,QAAgB,EAChB,MAA2B,EAC3B,YAAuD;QAEvD,MAAM,CAAC,MAAM,GAAG,IAAI,KAAK,EAAkB,CAAC;QAC5C,MAAM,SAAS,GAAG,IAAI,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE3H,mBAAmB;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBACxB,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACnE,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAE/E,MAAM,KAAK,GAAG,IAAI,WAAW,CAAI,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAE,YAAY,GAAG,CAAC,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;oBACjH,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBAC1B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAC7B;aACJ;SACJ;IACL,CAAC;CACJ", "sourcesContent": ["import type { SmartArrayNoDuplicate } from \"../../Misc/smartArray\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { <PERSON> } from \"../../Culling/ray\";\r\nimport { BoundingBox } from \"../../Culling/boundingBox\";\r\nimport type { Plane } from \"../../Maths/math.plane\";\r\n\r\n/**\r\n * Contains an array of blocks representing the octree\r\n */\r\nexport interface IOctreeContainer<T> {\r\n    /**\r\n     * Blocks within the octree\r\n     */\r\n    blocks: Array<OctreeBlock<T>>;\r\n}\r\n\r\n/**\r\n * Class used to store a cell in an octree\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n */\r\nexport class OctreeBlock<T> {\r\n    /**\r\n     * Gets the content of the current block\r\n     */\r\n    public entries: T[] = [];\r\n\r\n    /**\r\n     * Gets the list of block children\r\n     */\r\n    public blocks: Array<OctreeBlock<T>>;\r\n\r\n    private _depth: number;\r\n    private _maxDepth: number;\r\n    private _capacity: number;\r\n    private _minPoint: Vector3;\r\n    private _maxPoint: Vector3;\r\n    private _boundingVectors = new Array<Vector3>();\r\n    private _creationFunc: (entry: T, block: OctreeBlock<T>) => void;\r\n\r\n    /**\r\n     * Creates a new block\r\n     * @param minPoint defines the minimum vector (in world space) of the block's bounding box\r\n     * @param maxPoint defines the maximum vector (in world space) of the block's bounding box\r\n     * @param capacity defines the maximum capacity of this block (if capacity is reached the block will be split into sub blocks)\r\n     * @param depth defines the current depth of this block in the octree\r\n     * @param maxDepth defines the maximal depth allowed (beyond this value, the capacity is ignored)\r\n     * @param creationFunc defines a callback to call when an element is added to the block\r\n     */\r\n    constructor(minPoint: Vector3, maxPoint: Vector3, capacity: number, depth: number, maxDepth: number, creationFunc: (entry: T, block: OctreeBlock<T>) => void) {\r\n        this._capacity = capacity;\r\n        this._depth = depth;\r\n        this._maxDepth = maxDepth;\r\n        this._creationFunc = creationFunc;\r\n\r\n        this._minPoint = minPoint;\r\n        this._maxPoint = maxPoint;\r\n\r\n        this._boundingVectors.push(minPoint.clone());\r\n        this._boundingVectors.push(maxPoint.clone());\r\n\r\n        this._boundingVectors.push(minPoint.clone());\r\n        this._boundingVectors[2].x = maxPoint.x;\r\n\r\n        this._boundingVectors.push(minPoint.clone());\r\n        this._boundingVectors[3].y = maxPoint.y;\r\n\r\n        this._boundingVectors.push(minPoint.clone());\r\n        this._boundingVectors[4].z = maxPoint.z;\r\n\r\n        this._boundingVectors.push(maxPoint.clone());\r\n        this._boundingVectors[5].z = minPoint.z;\r\n\r\n        this._boundingVectors.push(maxPoint.clone());\r\n        this._boundingVectors[6].x = minPoint.x;\r\n\r\n        this._boundingVectors.push(maxPoint.clone());\r\n        this._boundingVectors[7].y = minPoint.y;\r\n    }\r\n\r\n    // Property\r\n\r\n    /**\r\n     * Gets the maximum capacity of this block (if capacity is reached the block will be split into sub blocks)\r\n     */\r\n    public get capacity(): number {\r\n        return this._capacity;\r\n    }\r\n\r\n    /**\r\n     * Gets the minimum vector (in world space) of the block's bounding box\r\n     */\r\n    public get minPoint(): Vector3 {\r\n        return this._minPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the maximum vector (in world space) of the block's bounding box\r\n     */\r\n    public get maxPoint(): Vector3 {\r\n        return this._maxPoint;\r\n    }\r\n\r\n    // Methods\r\n\r\n    /**\r\n     * Add a new element to this block\r\n     * @param entry defines the element to add\r\n     */\r\n    public addEntry(entry: T): void {\r\n        if (this.blocks) {\r\n            for (let index = 0; index < this.blocks.length; index++) {\r\n                const block = this.blocks[index];\r\n                block.addEntry(entry);\r\n            }\r\n            return;\r\n        }\r\n\r\n        this._creationFunc(entry, this);\r\n\r\n        if (this.entries.length > this.capacity && this._depth < this._maxDepth) {\r\n            this.createInnerBlocks();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove an element from this block\r\n     * @param entry defines the element to remove\r\n     */\r\n    public removeEntry(entry: T): void {\r\n        if (this.blocks) {\r\n            for (let index = 0; index < this.blocks.length; index++) {\r\n                const block = this.blocks[index];\r\n                block.removeEntry(entry);\r\n            }\r\n            return;\r\n        }\r\n\r\n        const entryIndex = this.entries.indexOf(entry);\r\n\r\n        if (entryIndex > -1) {\r\n            this.entries.splice(entryIndex, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Add an array of elements to this block\r\n     * @param entries defines the array of elements to add\r\n     */\r\n    public addEntries(entries: T[]): void {\r\n        for (let index = 0; index < entries.length; index++) {\r\n            const mesh = entries[index];\r\n            this.addEntry(mesh);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Test if the current block intersects the frustum planes and if yes, then add its content to the selection array\r\n     * @param frustumPlanes defines the frustum planes to test\r\n     * @param selection defines the array to store current content if selection is positive\r\n     * @param allowDuplicate defines if the selection array can contains duplicated entries\r\n     */\r\n    public select(frustumPlanes: Plane[], selection: SmartArrayNoDuplicate<T>, allowDuplicate?: boolean): void {\r\n        if (BoundingBox.IsInFrustum(this._boundingVectors, frustumPlanes)) {\r\n            if (this.blocks) {\r\n                for (let index = 0; index < this.blocks.length; index++) {\r\n                    const block = this.blocks[index];\r\n                    block.select(frustumPlanes, selection, allowDuplicate);\r\n                }\r\n                return;\r\n            }\r\n\r\n            if (allowDuplicate) {\r\n                selection.concat(this.entries);\r\n            } else {\r\n                selection.concatWithNoDuplicate(this.entries);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Test if the current block intersect with the given bounding sphere and if yes, then add its content to the selection array\r\n     * @param sphereCenter defines the bounding sphere center\r\n     * @param sphereRadius defines the bounding sphere radius\r\n     * @param selection defines the array to store current content if selection is positive\r\n     * @param allowDuplicate defines if the selection array can contains duplicated entries\r\n     */\r\n    public intersects(sphereCenter: Vector3, sphereRadius: number, selection: SmartArrayNoDuplicate<T>, allowDuplicate?: boolean): void {\r\n        if (BoundingBox.IntersectsSphere(this._minPoint, this._maxPoint, sphereCenter, sphereRadius)) {\r\n            if (this.blocks) {\r\n                for (let index = 0; index < this.blocks.length; index++) {\r\n                    const block = this.blocks[index];\r\n                    block.intersects(sphereCenter, sphereRadius, selection, allowDuplicate);\r\n                }\r\n                return;\r\n            }\r\n\r\n            if (allowDuplicate) {\r\n                selection.concat(this.entries);\r\n            } else {\r\n                selection.concatWithNoDuplicate(this.entries);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Test if the current block intersect with the given ray and if yes, then add its content to the selection array\r\n     * @param ray defines the ray to test with\r\n     * @param selection defines the array to store current content if selection is positive\r\n     */\r\n    public intersectsRay(ray: Ray, selection: SmartArrayNoDuplicate<T>): void {\r\n        if (ray.intersectsBoxMinMax(this._minPoint, this._maxPoint)) {\r\n            if (this.blocks) {\r\n                for (let index = 0; index < this.blocks.length; index++) {\r\n                    const block = this.blocks[index];\r\n                    block.intersectsRay(ray, selection);\r\n                }\r\n                return;\r\n            }\r\n            selection.concatWithNoDuplicate(this.entries);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Subdivide the content into child blocks (this block will then be empty)\r\n     */\r\n    public createInnerBlocks(): void {\r\n        OctreeBlock._CreateBlocks(this._minPoint, this._maxPoint, this.entries, this._capacity, this._depth, this._maxDepth, this, this._creationFunc);\r\n        this.entries.splice(0);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _CreateBlocks<T>(\r\n        worldMin: Vector3,\r\n        worldMax: Vector3,\r\n        entries: T[],\r\n        maxBlockCapacity: number,\r\n        currentDepth: number,\r\n        maxDepth: number,\r\n        target: IOctreeContainer<T>,\r\n        creationFunc: (entry: T, block: OctreeBlock<T>) => void\r\n    ): void {\r\n        target.blocks = new Array<OctreeBlock<T>>();\r\n        const blockSize = new Vector3((worldMax.x - worldMin.x) / 2, (worldMax.y - worldMin.y) / 2, (worldMax.z - worldMin.z) / 2);\r\n\r\n        // Segmenting space\r\n        for (let x = 0; x < 2; x++) {\r\n            for (let y = 0; y < 2; y++) {\r\n                for (let z = 0; z < 2; z++) {\r\n                    const localMin = worldMin.add(blockSize.multiplyByFloats(x, y, z));\r\n                    const localMax = worldMin.add(blockSize.multiplyByFloats(x + 1, y + 1, z + 1));\r\n\r\n                    const block = new OctreeBlock<T>(localMin, localMax, maxBlockCapacity, currentDepth + 1, maxDepth, creationFunc);\r\n                    block.addEntries(entries);\r\n                    target.blocks.push(block);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}