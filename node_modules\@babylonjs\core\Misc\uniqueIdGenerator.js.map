{"version": 3, "file": "uniqueIdGenerator.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/uniqueIdGenerator.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAI1B;;OAEG;IACI,MAAM,KAAK,QAAQ;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACrC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,OAAO,MAAM,CAAC;IAClB,CAAC;;AAVD,UAAU;AACK,kCAAgB,GAAG,CAAC,CAAC", "sourcesContent": ["/**\r\n * Helper class used to generate session unique ID\r\n */\r\nexport class UniqueIdGenerator {\r\n    // Statics\r\n    private static _UniqueIdCounter = 1;\r\n\r\n    /**\r\n     * Gets an unique (relatively to the current scene) Id\r\n     */\r\n    public static get UniqueId() {\r\n        const result = this._UniqueIdCounter;\r\n        this._UniqueIdCounter++;\r\n        return result;\r\n    }\r\n}\r\n"]}