{"version": 3, "file": "sceneVertexDeclaration.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Shaders/ShadersInclude/sceneVertexDeclaration.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAExD,MAAM,IAAI,GAAG,wBAAwB,CAAC;AACtC,MAAM,MAAM,GAAG;;;;;CAKd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAChD,gBAAgB;AAChB,MAAM,CAAC,MAAM,sBAAsB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore\";\n\nconst name = \"sceneVertexDeclaration\";\nconst shader = `uniform mat4 viewProjection;\n#ifdef MULTIVIEW\nuniform mat4 viewProjectionR;\n#endif\nuniform mat4 view;uniform mat4 projection;uniform vec4 vEyePosition;\n`;\n// Sideeffect\nShaderStore.IncludesShadersStore[name] = shader;\n/** @internal */\nexport const sceneVertexDeclaration = { name, shader };\n"]}