{"version": 3, "file": "subSurfaceScatteringFunctions.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Shaders/ShadersInclude/subSurfaceScatteringFunctions.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAExD,MAAM,IAAI,GAAG,+BAA+B,CAAC;AAC7C,MAAM,MAAM,GAAG;8BACe,CAAC;AAC/B,aAAa;AACb,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAChD,gBAAgB;AAChB,MAAM,CAAC,MAAM,6BAA6B,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore\";\n\nconst name = \"subSurfaceScatteringFunctions\";\nconst shader = `bool testLightingForSSS(float diffusionProfile)\n{return diffusionProfile<1.;}`;\n// Sideeffect\nShaderStore.IncludesShadersStore[name] = shader;\n/** @internal */\nexport const subSurfaceScatteringFunctions = { name, shader };\n"]}