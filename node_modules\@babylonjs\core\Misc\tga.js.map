{"version": 3, "file": "tga.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/tga.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,mCAAmC;AACnC,MAAM,aAAa,GAAG,CAAC,CAAC;AACxB,MAAM,SAAS,GAAG,CAAC,CAAC;AACpB,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,iBAAiB,GAAG,CAAC,CAAC;AAC5B,MAAM,aAAa,GAAG,EAAE,CAAC;AACzB,MAAM,cAAc,GAAG,EAAE,CAAC;AAC1B,MAAM,YAAY,GAAG,IAAI,CAAC;AAC1B,MAAM,aAAa,GAAG,IAAI,CAAC;AAC3B,MAAM,UAAU,GAAG,IAAI,CAAC;AACxB,MAAM,UAAU,GAAG,IAAI,CAAC;AACxB,MAAM,UAAU,GAAG,IAAI,CAAC;AACxB,MAAM,UAAU,GAAG,IAAI,CAAC;AAExB;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,IAAgB;IACzC,IAAI,MAAM,GAAG,CAAC,CAAC;IAEf,MAAM,MAAM,GAAG;QACX,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;QACzB,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;QAC7B,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;QAC1B,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC;QACtD,eAAe,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC;QACvD,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;QAC7B,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QACxF,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC;QAC7C,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC;QAC9C,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;QAC1B,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;KACxB,CAAC;IAEF,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,aAAa,CAAC,OAAwB,EAAE,IAAgB;IACpE,sCAAsC;IACtC,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,EAAE;QAClB,MAAM,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;QAC5E,OAAO;KACV;IAED,cAAc;IACd,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IAElC,kCAAkC;IAClC,IAAI,MAAM,CAAC,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;QACzC,MAAM,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC1D,OAAO;KACV;IAED,uBAAuB;IACvB,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC;IAE3B,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,IAAI,QAAQ,GAAG,KAAK,CAAC;IAErB,yBAAyB;IACzB,QAAQ,MAAM,CAAC,UAAU,EAAE;QACvB,KAAK,iBAAiB;YAClB,OAAO,GAAG,IAAI,CAAC;QACnB,0CAA0C;QAC1C,KAAK,aAAa;YACd,OAAO,GAAG,IAAI,CAAC;YACf,MAAM;QAEV,KAAK,aAAa;YACd,OAAO,GAAG,IAAI,CAAC;QACnB,0CAA0C;QAC1C,KAAK,SAAS;YACV,kBAAkB;YAClB,MAAM;QAEV,KAAK,cAAc;YACf,OAAO,GAAG,IAAI,CAAC;QACnB,0CAA0C;QAC1C,KAAK,UAAU;YACX,QAAQ,GAAG,IAAI,CAAC;YAChB,MAAM;KACb;IAED,IAAI,UAAU,CAAC;IAEf,yCAAyC;IACzC,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC;IAC1C,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;IAE9D,gBAAgB;IAChB,IAAI,QAAQ,CAAC;IAEb,IAAI,OAAO,EAAE;QACT,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,MAAM,IAAI,MAAM,CAAC,eAAe,GAAG,CAAC,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;KACtG;IAED,WAAW;IACX,IAAI,OAAO,EAAE;QACT,UAAU,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;QAEzC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;QAChB,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;QAE1C,OAAO,MAAM,GAAG,WAAW,IAAI,WAAW,GAAG,WAAW,EAAE;YACtD,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YACnB,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YAEvB,aAAa;YACb,IAAI,CAAC,GAAG,IAAI,EAAE;gBACV,uBAAuB;gBACvB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,EAAE,CAAC,EAAE;oBAC7B,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;iBAC9B;gBAED,mBAAmB;gBACnB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;oBACxB,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;iBACxD;gBAED,WAAW,IAAI,UAAU,GAAG,KAAK,CAAC;aACrC;YACD,aAAa;iBACR;gBACD,KAAK,IAAI,UAAU,CAAC;gBACpB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;oBACxB,UAAU,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;iBAChD;gBACD,WAAW,IAAI,KAAK,CAAC;aACxB;SACJ;KACJ;IACD,aAAa;SACR;QACD,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;KACxG;IAED,kBAAkB;IAClB,IAAI,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;IAEnD,QAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,aAAa,EAAE;QACpD,QAAQ;QACR,KAAK,UAAU;YACX,OAAO,GAAG,CAAC,CAAC;YACZ,MAAM,GAAG,CAAC,CAAC;YACX,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;YACrB,OAAO,GAAG,CAAC,CAAC;YACZ,MAAM,GAAG,CAAC,CAAC;YACX,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,MAAM;QAEV,KAAK,UAAU;YACX,OAAO,GAAG,CAAC,CAAC;YACZ,MAAM,GAAG,CAAC,CAAC;YACX,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;YACrB,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YAC5B,MAAM,GAAG,CAAC,CAAC,CAAC;YACZ,KAAK,GAAG,CAAC,CAAC,CAAC;YACX,MAAM;QAEV,KAAK,UAAU;YACX,OAAO,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;YAC3B,MAAM,GAAG,CAAC,CAAC,CAAC;YACZ,KAAK,GAAG,CAAC,CAAC,CAAC;YACX,OAAO,GAAG,CAAC,CAAC;YACZ,MAAM,GAAG,CAAC,CAAC;YACX,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;YACtB,MAAM;QAEV,KAAK,UAAU;YACX,OAAO,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;YAC3B,MAAM,GAAG,CAAC,CAAC,CAAC;YACZ,KAAK,GAAG,CAAC,CAAC,CAAC;YACX,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YAC5B,MAAM,GAAG,CAAC,CAAC,CAAC;YACZ,KAAK,GAAG,CAAC,CAAC,CAAC;YACX,MAAM;KACb;IAED,0BAA0B;IAC1B,MAAM,IAAI,GAAG,eAAe,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC;IACrF,MAAM,SAAS,GAAS,QAAS,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IAEtH,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;IACnC,MAAM,CAAC,4BAA4B,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC5D,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CACvB,MAAW,EACX,QAAoB,EACpB,UAAsB,EACtB,OAAe,EACf,MAAc,EACd,KAAa,EACb,OAAe,EACf,MAAc,EACd,KAAa;IAEb,MAAM,KAAK,GAAG,UAAU,EACpB,QAAQ,GAAG,QAAQ,CAAC;IACxB,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,EACtB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAC3B,IAAI,KAAK,EACL,CAAC,GAAG,CAAC,EACL,CAAC,EACD,CAAC,CAAC;IAEN,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IAErD,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,EAAE;QACxC,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACjB,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;YACzC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7D,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7D,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SAChE;KACJ;IAED,OAAO,SAAS,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CACxB,MAAW,EACX,QAAoB,EACpB,UAAsB,EACtB,OAAe,EACf,MAAc,EACd,KAAa,EACb,OAAe,EACf,MAAc,EACd,KAAa;IAEb,MAAM,KAAK,GAAG,UAAU,CAAC;IACzB,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,EACtB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAC3B,IAAI,KAAK,EACL,CAAC,GAAG,CAAC,EACL,CAAC,EACD,CAAC,CAAC;IAEN,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IAErD,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,EAAE;QACxC,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YAChD,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,aAAa;YACzD,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YACxD,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YACvD,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YAEhD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YACvC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YACvC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YACvC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;SACjE;KACJ;IAED,OAAO,SAAS,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CACxB,MAAW,EACX,QAAoB,EACpB,UAAsB,EACtB,OAAe,EACf,MAAc,EACd,KAAa,EACb,OAAe,EACf,MAAc,EACd,KAAa;IAEb,MAAM,KAAK,GAAG,UAAU,CAAC;IACzB,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,EACtB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAC3B,IAAI,CAAC,GAAG,CAAC,EACL,CAAC,EACD,CAAC,CAAC;IAEN,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IAErD,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,EAAE;QACxC,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YAChD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;YACzC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAClD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAClD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SACrD;KACJ;IAED,OAAO,SAAS,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CACxB,MAAW,EACX,QAAoB,EACpB,UAAsB,EACtB,OAAe,EACf,MAAc,EACd,KAAa,EACb,OAAe,EACf,MAAc,EACd,KAAa;IAEb,MAAM,KAAK,GAAG,UAAU,CAAC;IACzB,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,EACtB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAC3B,IAAI,CAAC,GAAG,CAAC,EACL,CAAC,EACD,CAAC,CAAC;IAEN,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IAErD,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,EAAE;QACxC,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YAChD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAClD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAClD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAClD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SACrD;KACJ;IAED,OAAO,SAAS,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,SAAS,sBAAsB,CAC3B,MAAW,EACX,QAAoB,EACpB,UAAsB,EACtB,OAAe,EACf,MAAc,EACd,KAAa,EACb,OAAe,EACf,MAAc,EACd,KAAa;IAEb,MAAM,KAAK,GAAG,UAAU,CAAC;IACzB,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,EACtB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAC3B,IAAI,KAAK,EACL,CAAC,GAAG,CAAC,EACL,CAAC,EACD,CAAC,CAAC;IAEN,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IAErD,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,EAAE;QACxC,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACjB,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAC3C,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAC3C,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAC3C,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;SAC5C;KACJ;IAED,OAAO,SAAS,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAC5B,MAAW,EACX,QAAoB,EACpB,UAAsB,EACtB,OAAe,EACf,MAAc,EACd,KAAa,EACb,OAAe,EACf,MAAc,EACd,KAAa;IAEb,MAAM,KAAK,GAAG,UAAU,CAAC;IACzB,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,EACtB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAC3B,IAAI,CAAC,GAAG,CAAC,EACL,CAAC,EACD,CAAC,CAAC;IAEN,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IAErD,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,EAAE;QACxC,KAAK,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YAChD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAClD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAClD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAClD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SACrD;KACJ;IAED,OAAO,SAAS,CAAC;AACrB,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG;IACpB;;;;OAIG;IACH,YAAY;IAEZ;;;OAGG;IACH,aAAa;IAEb,gBAAgB;IAChB,kBAAkB;IAElB,gBAAgB;IAChB,mBAAmB;IACnB,gBAAgB;IAChB,mBAAmB;IAEnB,gBAAgB;IAChB,mBAAmB;IAEnB,gBAAgB;IAChB,sBAAsB;IACtB,gBAAgB;IAChB,uBAAuB;CAC1B,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { InternalTexture } from \"../Materials/Textures/internalTexture\";\r\nimport { Logger } from \"../Misc/logger\";\r\n\r\n//private static _TYPE_NO_DATA = 0;\r\nconst _TYPE_INDEXED = 1;\r\nconst _TYPE_RGB = 2;\r\nconst _TYPE_GREY = 3;\r\nconst _TYPE_RLE_INDEXED = 9;\r\nconst _TYPE_RLE_RGB = 10;\r\nconst _TYPE_RLE_GREY = 11;\r\nconst _ORIGIN_MASK = 0x30;\r\nconst _ORIGIN_SHIFT = 0x04;\r\nconst _ORIGIN_BL = 0x00;\r\nconst _ORIGIN_BR = 0x01;\r\nconst _ORIGIN_UL = 0x02;\r\nconst _ORIGIN_UR = 0x03;\r\n\r\n/**\r\n * Gets the header of a TGA file\r\n * @param data defines the TGA data\r\n * @returns the header\r\n */\r\nexport function GetTGAHeader(data: Uint8Array): any {\r\n    let offset = 0;\r\n\r\n    const header = {\r\n        id_length: data[offset++],\r\n        colormap_type: data[offset++],\r\n        image_type: data[offset++],\r\n        colormap_index: data[offset++] | (data[offset++] << 8),\r\n        colormap_length: data[offset++] | (data[offset++] << 8),\r\n        colormap_size: data[offset++],\r\n        origin: [data[offset++] | (data[offset++] << 8), data[offset++] | (data[offset++] << 8)],\r\n        width: data[offset++] | (data[offset++] << 8),\r\n        height: data[offset++] | (data[offset++] << 8),\r\n        pixel_size: data[offset++],\r\n        flags: data[offset++],\r\n    };\r\n\r\n    return header;\r\n}\r\n\r\n/**\r\n * Uploads TGA content to a Babylon Texture\r\n * @internal\r\n */\r\nexport function UploadContent(texture: InternalTexture, data: Uint8Array): void {\r\n    // Not enough data to contain header ?\r\n    if (data.length < 19) {\r\n        Logger.Error(\"Unable to load TGA file - Not enough data to contain header\");\r\n        return;\r\n    }\r\n\r\n    // Read Header\r\n    let offset = 18;\r\n    const header = GetTGAHeader(data);\r\n\r\n    // Assume it's a valid Targa file.\r\n    if (header.id_length + offset > data.length) {\r\n        Logger.Error(\"Unable to load TGA file - Not enough data\");\r\n        return;\r\n    }\r\n\r\n    // Skip not needed data\r\n    offset += header.id_length;\r\n\r\n    let use_rle = false;\r\n    let use_pal = false;\r\n    let use_grey = false;\r\n\r\n    // Get some informations.\r\n    switch (header.image_type) {\r\n        case _TYPE_RLE_INDEXED:\r\n            use_rle = true;\r\n        // eslint-disable-next-line no-fallthrough\r\n        case _TYPE_INDEXED:\r\n            use_pal = true;\r\n            break;\r\n\r\n        case _TYPE_RLE_RGB:\r\n            use_rle = true;\r\n        // eslint-disable-next-line no-fallthrough\r\n        case _TYPE_RGB:\r\n            // use_rgb = true;\r\n            break;\r\n\r\n        case _TYPE_RLE_GREY:\r\n            use_rle = true;\r\n        // eslint-disable-next-line no-fallthrough\r\n        case _TYPE_GREY:\r\n            use_grey = true;\r\n            break;\r\n    }\r\n\r\n    let pixel_data;\r\n\r\n    // var numAlphaBits = header.flags & 0xf;\r\n    const pixel_size = header.pixel_size >> 3;\r\n    const pixel_total = header.width * header.height * pixel_size;\r\n\r\n    // Read palettes\r\n    let palettes;\r\n\r\n    if (use_pal) {\r\n        palettes = data.subarray(offset, (offset += header.colormap_length * (header.colormap_size >> 3)));\r\n    }\r\n\r\n    // Read LRE\r\n    if (use_rle) {\r\n        pixel_data = new Uint8Array(pixel_total);\r\n\r\n        let c, count, i;\r\n        let localOffset = 0;\r\n        const pixels = new Uint8Array(pixel_size);\r\n\r\n        while (offset < pixel_total && localOffset < pixel_total) {\r\n            c = data[offset++];\r\n            count = (c & 0x7f) + 1;\r\n\r\n            // RLE pixels\r\n            if (c & 0x80) {\r\n                // Bind pixel tmp array\r\n                for (i = 0; i < pixel_size; ++i) {\r\n                    pixels[i] = data[offset++];\r\n                }\r\n\r\n                // Copy pixel array\r\n                for (i = 0; i < count; ++i) {\r\n                    pixel_data.set(pixels, localOffset + i * pixel_size);\r\n                }\r\n\r\n                localOffset += pixel_size * count;\r\n            }\r\n            // Raw pixels\r\n            else {\r\n                count *= pixel_size;\r\n                for (i = 0; i < count; ++i) {\r\n                    pixel_data[localOffset + i] = data[offset++];\r\n                }\r\n                localOffset += count;\r\n            }\r\n        }\r\n    }\r\n    // RAW Pixels\r\n    else {\r\n        pixel_data = data.subarray(offset, (offset += use_pal ? header.width * header.height : pixel_total));\r\n    }\r\n\r\n    // Load to texture\r\n    let x_start, y_start, x_step, y_step, y_end, x_end;\r\n\r\n    switch ((header.flags & _ORIGIN_MASK) >> _ORIGIN_SHIFT) {\r\n        default:\r\n        case _ORIGIN_UL:\r\n            x_start = 0;\r\n            x_step = 1;\r\n            x_end = header.width;\r\n            y_start = 0;\r\n            y_step = 1;\r\n            y_end = header.height;\r\n            break;\r\n\r\n        case _ORIGIN_BL:\r\n            x_start = 0;\r\n            x_step = 1;\r\n            x_end = header.width;\r\n            y_start = header.height - 1;\r\n            y_step = -1;\r\n            y_end = -1;\r\n            break;\r\n\r\n        case _ORIGIN_UR:\r\n            x_start = header.width - 1;\r\n            x_step = -1;\r\n            x_end = -1;\r\n            y_start = 0;\r\n            y_step = 1;\r\n            y_end = header.height;\r\n            break;\r\n\r\n        case _ORIGIN_BR:\r\n            x_start = header.width - 1;\r\n            x_step = -1;\r\n            x_end = -1;\r\n            y_start = header.height - 1;\r\n            y_step = -1;\r\n            y_end = -1;\r\n            break;\r\n    }\r\n\r\n    // Load the specify method\r\n    const func = \"_getImageData\" + (use_grey ? \"Grey\" : \"\") + header.pixel_size + \"bits\";\r\n    const imageData = (<any>TGATools)[func](header, palettes, pixel_data, y_start, y_step, y_end, x_start, x_step, x_end);\r\n\r\n    const engine = texture.getEngine();\r\n    engine._uploadDataToTextureDirectly(texture, imageData);\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nfunction _getImageData8bits(\r\n    header: any,\r\n    palettes: Uint8Array,\r\n    pixel_data: Uint8Array,\r\n    y_start: number,\r\n    y_step: number,\r\n    y_end: number,\r\n    x_start: number,\r\n    x_step: number,\r\n    x_end: number\r\n): Uint8Array {\r\n    const image = pixel_data,\r\n        colormap = palettes;\r\n    const width = header.width,\r\n        height = header.height;\r\n    let color,\r\n        i = 0,\r\n        x,\r\n        y;\r\n\r\n    const imageData = new Uint8Array(width * height * 4);\r\n\r\n    for (y = y_start; y !== y_end; y += y_step) {\r\n        for (x = x_start; x !== x_end; x += x_step, i++) {\r\n            color = image[i];\r\n            imageData[(x + width * y) * 4 + 3] = 255;\r\n            imageData[(x + width * y) * 4 + 2] = colormap[color * 3 + 0];\r\n            imageData[(x + width * y) * 4 + 1] = colormap[color * 3 + 1];\r\n            imageData[(x + width * y) * 4 + 0] = colormap[color * 3 + 2];\r\n        }\r\n    }\r\n\r\n    return imageData;\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nfunction _getImageData16bits(\r\n    header: any,\r\n    palettes: Uint8Array,\r\n    pixel_data: Uint8Array,\r\n    y_start: number,\r\n    y_step: number,\r\n    y_end: number,\r\n    x_start: number,\r\n    x_step: number,\r\n    x_end: number\r\n): Uint8Array {\r\n    const image = pixel_data;\r\n    const width = header.width,\r\n        height = header.height;\r\n    let color,\r\n        i = 0,\r\n        x,\r\n        y;\r\n\r\n    const imageData = new Uint8Array(width * height * 4);\r\n\r\n    for (y = y_start; y !== y_end; y += y_step) {\r\n        for (x = x_start; x !== x_end; x += x_step, i += 2) {\r\n            color = image[i + 0] + (image[i + 1] << 8); // Inversed ?\r\n            const r = ((((color & 0x7c00) >> 10) * 255) / 0x1f) | 0;\r\n            const g = ((((color & 0x03e0) >> 5) * 255) / 0x1f) | 0;\r\n            const b = (((color & 0x001f) * 255) / 0x1f) | 0;\r\n\r\n            imageData[(x + width * y) * 4 + 0] = r;\r\n            imageData[(x + width * y) * 4 + 1] = g;\r\n            imageData[(x + width * y) * 4 + 2] = b;\r\n            imageData[(x + width * y) * 4 + 3] = color & 0x8000 ? 0 : 255;\r\n        }\r\n    }\r\n\r\n    return imageData;\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nfunction _getImageData24bits(\r\n    header: any,\r\n    palettes: Uint8Array,\r\n    pixel_data: Uint8Array,\r\n    y_start: number,\r\n    y_step: number,\r\n    y_end: number,\r\n    x_start: number,\r\n    x_step: number,\r\n    x_end: number\r\n): Uint8Array {\r\n    const image = pixel_data;\r\n    const width = header.width,\r\n        height = header.height;\r\n    let i = 0,\r\n        x,\r\n        y;\r\n\r\n    const imageData = new Uint8Array(width * height * 4);\r\n\r\n    for (y = y_start; y !== y_end; y += y_step) {\r\n        for (x = x_start; x !== x_end; x += x_step, i += 3) {\r\n            imageData[(x + width * y) * 4 + 3] = 255;\r\n            imageData[(x + width * y) * 4 + 2] = image[i + 0];\r\n            imageData[(x + width * y) * 4 + 1] = image[i + 1];\r\n            imageData[(x + width * y) * 4 + 0] = image[i + 2];\r\n        }\r\n    }\r\n\r\n    return imageData;\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nfunction _getImageData32bits(\r\n    header: any,\r\n    palettes: Uint8Array,\r\n    pixel_data: Uint8Array,\r\n    y_start: number,\r\n    y_step: number,\r\n    y_end: number,\r\n    x_start: number,\r\n    x_step: number,\r\n    x_end: number\r\n): Uint8Array {\r\n    const image = pixel_data;\r\n    const width = header.width,\r\n        height = header.height;\r\n    let i = 0,\r\n        x,\r\n        y;\r\n\r\n    const imageData = new Uint8Array(width * height * 4);\r\n\r\n    for (y = y_start; y !== y_end; y += y_step) {\r\n        for (x = x_start; x !== x_end; x += x_step, i += 4) {\r\n            imageData[(x + width * y) * 4 + 2] = image[i + 0];\r\n            imageData[(x + width * y) * 4 + 1] = image[i + 1];\r\n            imageData[(x + width * y) * 4 + 0] = image[i + 2];\r\n            imageData[(x + width * y) * 4 + 3] = image[i + 3];\r\n        }\r\n    }\r\n\r\n    return imageData;\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nfunction _getImageDataGrey8bits(\r\n    header: any,\r\n    palettes: Uint8Array,\r\n    pixel_data: Uint8Array,\r\n    y_start: number,\r\n    y_step: number,\r\n    y_end: number,\r\n    x_start: number,\r\n    x_step: number,\r\n    x_end: number\r\n): Uint8Array {\r\n    const image = pixel_data;\r\n    const width = header.width,\r\n        height = header.height;\r\n    let color,\r\n        i = 0,\r\n        x,\r\n        y;\r\n\r\n    const imageData = new Uint8Array(width * height * 4);\r\n\r\n    for (y = y_start; y !== y_end; y += y_step) {\r\n        for (x = x_start; x !== x_end; x += x_step, i++) {\r\n            color = image[i];\r\n            imageData[(x + width * y) * 4 + 0] = color;\r\n            imageData[(x + width * y) * 4 + 1] = color;\r\n            imageData[(x + width * y) * 4 + 2] = color;\r\n            imageData[(x + width * y) * 4 + 3] = 255;\r\n        }\r\n    }\r\n\r\n    return imageData;\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nfunction _getImageDataGrey16bits(\r\n    header: any,\r\n    palettes: Uint8Array,\r\n    pixel_data: Uint8Array,\r\n    y_start: number,\r\n    y_step: number,\r\n    y_end: number,\r\n    x_start: number,\r\n    x_step: number,\r\n    x_end: number\r\n): Uint8Array {\r\n    const image = pixel_data;\r\n    const width = header.width,\r\n        height = header.height;\r\n    let i = 0,\r\n        x,\r\n        y;\r\n\r\n    const imageData = new Uint8Array(width * height * 4);\r\n\r\n    for (y = y_start; y !== y_end; y += y_step) {\r\n        for (x = x_start; x !== x_end; x += x_step, i += 2) {\r\n            imageData[(x + width * y) * 4 + 0] = image[i + 0];\r\n            imageData[(x + width * y) * 4 + 1] = image[i + 0];\r\n            imageData[(x + width * y) * 4 + 2] = image[i + 0];\r\n            imageData[(x + width * y) * 4 + 3] = image[i + 1];\r\n        }\r\n    }\r\n\r\n    return imageData;\r\n}\r\n\r\n/**\r\n * Based on jsTGALoader - Javascript loader for TGA file\r\n * By Vincent Thibault\r\n * @see http://blog.robrowser.com/javascript-tga-loader.html\r\n */\r\nexport const TGATools = {\r\n    /**\r\n     * Gets the header of a TGA file\r\n     * @param data defines the TGA data\r\n     * @returns the header\r\n     */\r\n    GetTGAHeader,\r\n\r\n    /**\r\n     * Uploads TGA content to a Babylon Texture\r\n     * @internal\r\n     */\r\n    UploadContent,\r\n\r\n    /** @internal */\r\n    _getImageData8bits,\r\n\r\n    /** @internal */\r\n    _getImageData16bits,\r\n    /** @internal */\r\n    _getImageData24bits,\r\n\r\n    /** @internal */\r\n    _getImageData32bits,\r\n\r\n    /** @internal */\r\n    _getImageDataGrey8bits,\r\n    /** @internal */\r\n    _getImageDataGrey16bits,\r\n};\r\n"]}