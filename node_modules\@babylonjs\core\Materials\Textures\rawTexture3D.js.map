{"version": 3, "file": "rawTexture3D.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/rawTexture3D.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,4CAA4C,CAAC;AAEpD;;GAEG;AACH,MAAM,OAAO,YAAa,SAAQ,OAAO;IACrC;;;;;;;;;;;;;OAaG;IACH,YACI,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,KAAa;IACb,6CAA6C;IACtC,MAAc,EACrB,KAAY,EACZ,kBAA2B,IAAI,EAC/B,UAAmB,KAAK,EACxB,eAAuB,OAAO,CAAC,sBAAsB,EACrD,WAAW,GAAG,SAAS,CAAC,wBAAwB,EAChD,aAAsB;QAEtB,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;QARvC,WAAM,GAAN,MAAM,CAAQ;QAUrB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;QAEnK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACrB,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,IAAqB;QAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QACD,IAAI,CAAC,UAAU,EAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,QAAS,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACvI,CAAC;CACJ", "sourcesContent": ["import type { Scene } from \"../../scene\";\r\nimport { Texture } from \"./texture\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport \"../../Engines/Extensions/engine.rawTexture\";\r\nimport type { Nullable } from \"../../types\";\r\n/**\r\n * Class used to store 3D textures containing user data\r\n */\r\nexport class RawTexture3D extends Texture {\r\n    /**\r\n     * Create a new RawTexture3D\r\n     * @param data defines the data of the texture\r\n     * @param width defines the width of the texture\r\n     * @param height defines the height of the texture\r\n     * @param depth defines the depth of the texture\r\n     * @param format defines the texture format to use\r\n     * @param scene defines the hosting scene\r\n     * @param generateMipMaps defines a boolean indicating if mip levels should be generated (true by default)\r\n     * @param invertY defines if texture must be stored with Y axis inverted\r\n     * @param samplingMode defines the sampling mode to use (Texture.TRILINEAR_SAMPLINGMODE by default)\r\n     * @param textureType defines the texture Type (Engine.TEXTURETYPE_UNSIGNED_INT, Engine.TEXTURETYPE_FLOAT...)\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     */\r\n    constructor(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        depth: number,\r\n        /** Gets or sets the texture format to use */\r\n        public format: number,\r\n        scene: Scene,\r\n        generateMipMaps: boolean = true,\r\n        invertY: boolean = false,\r\n        samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE,\r\n        textureType = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        creationFlags?: number\r\n    ) {\r\n        super(null, scene, !generateMipMaps, invertY);\r\n\r\n        this._texture = scene.getEngine().createRawTexture3D(data, width, height, depth, format, generateMipMaps, invertY, samplingMode, null, textureType, creationFlags);\r\n\r\n        this.is3D = true;\r\n    }\r\n\r\n    /**\r\n     * Update the texture with new data\r\n     * @param data defines the data to store in the texture\r\n     */\r\n    public update(data: ArrayBufferView): void {\r\n        if (!this._texture) {\r\n            return;\r\n        }\r\n        this._getEngine()!.updateRawTexture3D(this._texture, data, this._texture.format, this._texture!.invertY, null, this._texture.type);\r\n    }\r\n}\r\n"]}