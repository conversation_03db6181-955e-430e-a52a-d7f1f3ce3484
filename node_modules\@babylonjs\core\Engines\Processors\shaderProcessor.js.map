{"version": 3, "file": "shaderProcessor.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Processors/shaderProcessor.ts"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AACpE,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,6BAA6B,EAAE,MAAM,uDAAuD,CAAC;AACtG,OAAO,EAAE,sBAAsB,EAAE,MAAM,gDAAgD,CAAC;AACxF,OAAO,EAAE,uBAAuB,EAAE,MAAM,iDAAiD,CAAC;AAC1F,OAAO,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAC9E,OAAO,EAAE,8BAA8B,EAAE,MAAM,wDAAwD,CAAC;AAExG,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAQhE,MAAM,OAAO,GAAG,uBAAuB,CAAC;AACxC,MAAM,aAAa,GAAG,uBAAuB,CAAC;AAC9C,MAAM,kBAAkB,GAAG,0CAA0C,CAAC;AACtE,MAAM,eAAe,GAAG,UAAU,CAAC;AACnC,MAAM,WAAW,GAAG,mBAAmB,CAAC;AACxC,MAAM,MAAM,GAAG,QAAQ,CAAC;AACxB,MAAM,eAAe,GAAuB,EAAE,CAAC;AAE/C,gBAAgB;AAChB,MAAM,OAAO,eAAe;IAGjB,MAAM,CAAC,UAAU,CAAC,OAA0B;QAC/C,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,iBAAiB,EAAE;YAC1D,OAAO,CAAC,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;SAClE;IACL,CAAC;IAEM,MAAM,CAAC,OAAO,CAAC,UAAkB,EAAE,OAA0B,EAAE,QAAqE,EAAE,MAAkB;QAC3J,IAAI,OAAO,CAAC,SAAS,EAAE,oBAAoB,EAAE;YACzC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;SACvF;QACD,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,gBAAgB,EAAE,EAAE;YAC5D,IAAI,OAAO,CAAC,wBAAwB,EAAE;gBAClC,gBAAgB,GAAG,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;aACrH;YACD,MAAM,YAAY,GAAG,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YACtF,QAAQ,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,MAAM,CAAC,UAAU,CAAC,UAAkB,EAAE,OAA0B,EAAE,QAAqE,EAAE,MAAkB;QAC9J,IAAI,OAAO,CAAC,SAAS,EAAE,oBAAoB,EAAE;YACzC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;SACvF;QACD,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,gBAAgB,EAAE,EAAE;YAC5D,IAAI,OAAO,CAAC,wBAAwB,EAAE;gBAClC,gBAAgB,GAAG,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;aACrH;YACD,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YACjF,QAAQ,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,MAAM,CAAC,QAAQ,CAAC,UAAkB,EAAE,YAAoB,EAAE,OAA0B;QACvF,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,EAAE;YAC1D,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC;SACvC;QAED,OAAO,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU,EAAE,YAAY,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAClG,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,MAAc,EAAE,OAA0B;QACvE,IAAI,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE;YAChC,OAAO,MAAM,CAAC;SACjB;QAED,MAAM,4BAA4B,GAAG,OAAO,CAAC,4BAA4B,CAAC;QAE1E,IAAI,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,EAAE;YAChD,IAAI,CAAC,4BAA4B,EAAE;gBAC/B,MAAM,GAAG,4BAA4B,GAAG,MAAM,CAAC;aAClD;iBAAM;gBACH,MAAM,GAAG,0BAA0B,GAAG,MAAM,CAAC;aAChD;SACJ;aAAM;YACH,IAAI,CAAC,4BAA4B,EAAE;gBAC/B,0BAA0B;gBAC1B,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,uBAAuB,EAAE,yBAAyB,CAAC,CAAC;aAC/E;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,UAAkB;QAC/C,MAAM,KAAK,GAAG,iBAAiB,CAAC;QAEhC,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAErC,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;YACvB,OAAO,IAAI,6BAA6B,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;SACpF;QAED,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACrD,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,KAAK,QAAQ,IAAI,SAAS,EAAE;YACxB,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAE7C,IAAI,aAAa,GAAG,CAAC,CAAC,EAAE;gBACpB,MAAM;aACT;SACJ;QAED,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;YACtB,OAAO,IAAI,6BAA6B,CAAC,UAAU,CAAC,CAAC;SACxD;QAED,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,IAAI,EAAE,CAAC;QAC7D,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;QAE3E,OAAO,IAAI,8BAA8B,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;IACvE,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,UAAkB;QACjD,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAExD,MAAM,OAAO,GAAG,sBAAsB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAElE,MAAM,KAAK,GAAwC,EAAE,CAAC;QAEtD,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE;YACrB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;gBAC1B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACjB;iBAAM,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;gBAC1B,IAAI,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5B,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAEjC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC;gBAElB,MAAM,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,uBAAuB,EAAE,CAAC,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;gBAE1F,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;oBACxB,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;iBACjD;gBAED,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;oBACxB,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;iBACjD;gBAED,QAAQ,CAAC,WAAW,GAAG,OAAO,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAChF,QAAQ,CAAC,YAAY,GAAG,OAAO,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAEjF,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACxB;SACJ;QAED,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAErC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC5B,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;SACzD;QAED,gEAAgE;QAEhE,OAAO,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAChF,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,IAAY,EAAE,KAAa;QACvD,MAAM,IAAI,GAAG,IAAI,kBAAkB,EAAE,CAAC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACzC,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAEvC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAEzG,IAAI,OAAO,KAAK,QAAQ,EAAE;YACtB,IAAI,CAAC,cAAc,GAAG,IAAI,6BAA6B,CAAC,UAAU,CAAC,CAAC;SACvE;aAAM,IAAI,OAAO,KAAK,SAAS,EAAE;YAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,6BAA6B,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;SAC7E;aAAM;YACH,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;SAC9D;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,MAAwB,EAAE,QAAiC,EAAE,MAAsB;QAClH,IAAI,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC;QAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;YACrC,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YAElD,IAAI,MAAM,KAAK,OAAO,EAAE;gBACpB,MAAM,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;gBACtC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACnC,OAAO;aACV;iBAAM,IAAI,MAAM,KAAK,OAAO,EAAE;gBAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBAEhD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjC,MAAM,GAAG,QAAQ,CAAC;aACrB;SACJ;IACL,CAAC;IAEO,MAAM,CAAC,WAAW,CAAC,MAAwB,EAAE,QAAwB;QACzE,OAAO,MAAM,CAAC,OAAO,EAAE;YACnB,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC;YAEhC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACxB,MAAM,OAAO,GAAG,eAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE5D,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;oBAC3B,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBAE3B,QAAQ,OAAO,EAAE;wBACb,KAAK,QAAQ,CAAC,CAAC;4BACX,MAAM,WAAW,GAAG,IAAI,uBAAuB,EAAE,CAAC;4BAClD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;4BAEpC,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;4BAC9C,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;4BAClC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;4BACtD,MAAM;yBACT;wBACD,KAAK,OAAO,CAAC;wBACb,KAAK,OAAO;4BACR,OAAO,IAAI,CAAC;wBAChB,KAAK,QAAQ;4BACT,OAAO,KAAK,CAAC;wBACjB,KAAK,SAAS,CAAC,CAAC;4BACZ,MAAM,WAAW,GAAG,IAAI,uBAAuB,EAAE,CAAC;4BAClD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;4BAEpC,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;4BAC9C,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;4BAClC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;4BACtD,MAAM;yBACT;wBACD,KAAK,KAAK,CAAC,CAAC;4BACR,MAAM,WAAW,GAAG,IAAI,uBAAuB,EAAE,CAAC;4BAClD,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;4BAC9C,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;4BAEpC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;4BAClC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;4BACtD,MAAM;yBACT;qBACJ;oBACD,SAAS;iBACZ;aACJ;YAED,MAAM,OAAO,GAAG,IAAI,cAAc,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YACpB,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEhC,4BAA4B;YAC5B,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBACpC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC/C,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEvC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;oBACpB,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;iBAC5C;aACJ;SACJ;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,UAAkB,EAAE,aAAwC,EAAE,OAA0B;QAC1H,MAAM,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;QACtC,MAAM,MAAM,GAAG,IAAI,gBAAgB,EAAE,CAAC;QAEtC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;QACtB,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEtC,8FAA8F;QAC9F,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAEnC,YAAY;QACZ,OAAO,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,OAA0B,EAAE,MAAkB;QAC/E,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAChC,MAAM,aAAa,GAA8B,EAAE,CAAC;QAEpD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC1B,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YACvE,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;SAC9D;QAED,IAAI,OAAO,CAAC,SAAS,EAAE,cAAc,KAAK,cAAc,CAAC,IAAI,EAAE;YAC3D,aAAa,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;SACnC;QACD,aAAa,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/C,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;QAE7C,MAAM,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAExC,OAAO,aAAa,CAAC;IACzB,CAAC;IAEO,MAAM,CAAC,wBAAwB,CAAC,UAAkB,EAAE,OAA0B,EAAE,MAAkB;QACtG,IAAI,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAErE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACpB,OAAO,kBAAkB,CAAC;SAC7B;QAED,oBAAoB;QACpB,IAAI,OAAO,CAAC,SAAS,CAAC,cAAc,KAAK,cAAc,CAAC,IAAI,IAAI,kBAAkB,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;YAC7G,kBAAkB,GAAG,kBAAkB,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;YACvE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,EAAE;gBAC/B,OAAO,kBAAkB,CAAC;aAC7B;SACJ;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAEhC,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAElE,yBAAyB;QACzB,IAAI,OAAO,CAAC,SAAS,CAAC,YAAY,EAAE;YAChC,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,kBAAkB,EAAE,OAAO,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;SACnI;QAED,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAE7F,kBAAkB;QAClB,IAAI,OAAO,CAAC,SAAS,CAAC,aAAa,EAAE;YACjC,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,kBAAkB,EAAE,OAAO,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;SAC5I;QAED,8CAA8C;QAC9C,IAAI,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE;YACzC,kBAAkB,GAAG,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;SACpE;QAED,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,UAAkB,EAAE,OAA0B,EAAE,MAAkB;QACjG,IAAI,kBAAkB,GAAG,UAAU,CAAC;QAEpC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAEhC,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAElE,yBAAyB;QACzB,IAAI,OAAO,CAAC,SAAS,EAAE,YAAY,EAAE;YACjC,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,kBAAkB,EAAE,OAAO,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;SACnI;QAED,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAE7F,kBAAkB;QAClB,IAAI,OAAO,CAAC,SAAS,EAAE,aAAa,EAAE;YAClC,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,kBAAkB,EAAE,OAAO,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;SAC5I;QAED,8CAA8C;QAC9C,IAAI,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE;YACzC,kBAAkB,GAAG,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;SACpE;QAED,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED,gBAAgB;IACT,MAAM,CAAC,gBAAgB,CAAC,UAAkB,EAAE,OAA0B,EAAE,QAA6B;QACxG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAC3B,IAAI,KAA8B,CAAC;QACnC,8CAA8C;QAC9C,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,IAAI,EAAE;YAC3D,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC/B;QAED,IAAI,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;QACrC,IAAI,KAAK,GAAG,CAAC,UAAU,CAAC,CAAC;QAEzB,IAAI,cAAc,GAAG,KAAK,CAAC;QAE3B,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE;YACjC,IAAI,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAE3B,sBAAsB;YACtB,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;gBACxC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;gBACvD,IAAI,OAAO,CAAC,sBAAsB,EAAE;oBAChC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;iBACjF;gBACD,WAAW,GAAG,WAAW,GAAG,aAAa,CAAC;aAC7C;YAED,IAAI,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,EAAE;gBAC3C,eAAe;gBACf,IAAI,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;gBAC/D,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;oBACV,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAEnC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE;wBACnD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;wBAC9C,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;wBAE/B,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;qBACzD;iBACJ;gBAED,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;oBACV,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;oBAE7B,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;wBAClC,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBAC5C,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1C,IAAI,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxC,IAAI,oBAAoB,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBACnD,cAAc,GAAG,EAAE,CAAC;wBAEpB,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE;4BACjB,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;yBACtD;wBAED,KAAK,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;4BACtC,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;gCACjC,kBAAkB;gCAClB,oBAAoB,GAAG,oBAAoB,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,GAAW,EAAE,EAAU,EAAE,EAAE;oCACzF,OAAO,EAAE,GAAG,KAAK,CAAC;gCACtB,CAAC,CAAC,CAAC;6BACN;4BACD,cAAc,IAAI,oBAAoB,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC;yBAC/E;qBACJ;yBAAM;wBACH,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;4BACjC,kBAAkB;4BAClB,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,GAAW,EAAE,EAAU,EAAE,EAAE;gCAC7E,OAAO,EAAE,GAAG,KAAK,CAAC;4BACtB,CAAC,CAAC,CAAC;yBACN;wBACD,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;qBAChE;iBACJ;gBAED,UAAU;gBACV,iFAAiF;gBACjF,MAAM,QAAQ,GAAG,EAAE,CAAC;gBACpB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;oBACtB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;wBAC3C,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5B,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;qBACjC;oBACD,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;iBAClD;gBACD,KAAK,GAAG,QAAQ,CAAC;gBAEjB,cAAc,GAAG,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;aAC5H;iBAAM;gBACH,MAAM,gBAAgB,GAAG,OAAO,CAAC,iBAAiB,GAAG,iBAAiB,GAAG,WAAW,GAAG,KAAK,CAAC;gBAE7F,eAAe,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,WAAW,EAAE,EAAE;oBACjE,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,GAAG,WAAqB,CAAC;oBAClE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;gBAC7D,CAAC,CAAC,CAAC;gBACH,OAAO;aACV;SACJ;QACD,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAE3B,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAE7B,IAAI,cAAc,EAAE;YAChB,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;SACpE;aAAM;YACH,QAAQ,CAAC,WAAW,CAAC,CAAC;SACzB;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAC,kBAAkB,CAC5B,GAAW,EACX,SAAqE,EACrE,UAAwC,EACxC,eAAkC,EAClC,cAAwB,EACxB,OAAmE;QAEnE,MAAM,WAAW,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;;AA1dc,gCAAgB,GAAG,mDAAmD,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { ShaderCodeNode } from \"./shaderCodeNode\";\r\nimport { ShaderCodeCursor } from \"./shaderCodeCursor\";\r\nimport { ShaderCodeConditionNode } from \"./shaderCodeConditionNode\";\r\nimport { ShaderCodeTestNode } from \"./shaderCodeTestNode\";\r\nimport { ShaderDefineIsDefinedOperator } from \"./Expressions/Operators/shaderDefineIsDefinedOperator\";\r\nimport { ShaderDefineOrOperator } from \"./Expressions/Operators/shaderDefineOrOperator\";\r\nimport { ShaderDefineAndOperator } from \"./Expressions/Operators/shaderDefineAndOperator\";\r\nimport { ShaderDefineExpression } from \"./Expressions/shaderDefineExpression\";\r\nimport { ShaderDefineArithmeticOperator } from \"./Expressions/Operators/shaderDefineArithmeticOperator\";\r\nimport type { ProcessingOptions } from \"./shaderProcessingOptions\";\r\nimport { _WarnImport } from \"../../Misc/devTools\";\r\nimport { ShaderLanguage } from \"../../Materials/shaderLanguage\";\r\n\r\nimport type { WebRequest } from \"../../Misc/webRequest\";\r\nimport type { LoadFileError } from \"../../Misc/fileTools\";\r\nimport type { IOfflineProvider } from \"../../Offline/IOfflineProvider\";\r\nimport type { IFileRequest } from \"../../Misc/fileRequest\";\r\nimport type { ThinEngine } from \"../thinEngine\";\r\n\r\nconst regexSE = /defined\\s*?\\((.+?)\\)/g;\r\nconst regexSERevert = /defined\\s*?\\[(.+?)\\]/g;\r\nconst regexShaderInclude = /#include\\s?<(.+)>(\\((.*)\\))*(\\[(.*)\\])*/g;\r\nconst regexShaderDecl = /__decl__/;\r\nconst regexLightX = /light\\{X\\}.(\\w*)/g;\r\nconst regexX = /\\{X\\}/g;\r\nconst reusableMatches: RegExpMatchArray[] = [];\r\n\r\n/** @internal */\r\nexport class ShaderProcessor {\r\n    private static _MoveCursorRegex = /(#ifdef)|(#else)|(#elif)|(#endif)|(#ifndef)|(#if)/;\r\n\r\n    public static Initialize(options: ProcessingOptions): void {\r\n        if (options.processor && options.processor.initializeShaders) {\r\n            options.processor.initializeShaders(options.processingContext);\r\n        }\r\n    }\r\n\r\n    public static Process(sourceCode: string, options: ProcessingOptions, callback: (migratedCode: string, codeBeforeMigration: string) => void, engine: ThinEngine) {\r\n        if (options.processor?.preProcessShaderCode) {\r\n            sourceCode = options.processor.preProcessShaderCode(sourceCode, options.isFragment);\r\n        }\r\n        this._ProcessIncludes(sourceCode, options, (codeWithIncludes) => {\r\n            if (options.processCodeAfterIncludes) {\r\n                codeWithIncludes = options.processCodeAfterIncludes(options.isFragment ? \"fragment\" : \"vertex\", codeWithIncludes);\r\n            }\r\n            const migratedCode = this._ProcessShaderConversion(codeWithIncludes, options, engine);\r\n            callback(migratedCode, codeWithIncludes);\r\n        });\r\n    }\r\n\r\n    public static PreProcess(sourceCode: string, options: ProcessingOptions, callback: (migratedCode: string, codeBeforeMigration: string) => void, engine: ThinEngine) {\r\n        if (options.processor?.preProcessShaderCode) {\r\n            sourceCode = options.processor.preProcessShaderCode(sourceCode, options.isFragment);\r\n        }\r\n        this._ProcessIncludes(sourceCode, options, (codeWithIncludes) => {\r\n            if (options.processCodeAfterIncludes) {\r\n                codeWithIncludes = options.processCodeAfterIncludes(options.isFragment ? \"fragment\" : \"vertex\", codeWithIncludes);\r\n            }\r\n            const migratedCode = this._ApplyPreProcessing(codeWithIncludes, options, engine);\r\n            callback(migratedCode, codeWithIncludes);\r\n        });\r\n    }\r\n\r\n    public static Finalize(vertexCode: string, fragmentCode: string, options: ProcessingOptions): { vertexCode: string; fragmentCode: string } {\r\n        if (!options.processor || !options.processor.finalizeShaders) {\r\n            return { vertexCode, fragmentCode };\r\n        }\r\n\r\n        return options.processor.finalizeShaders(vertexCode, fragmentCode, options.processingContext);\r\n    }\r\n\r\n    private static _ProcessPrecision(source: string, options: ProcessingOptions): string {\r\n        if (options.processor?.noPrecision) {\r\n            return source;\r\n        }\r\n\r\n        const shouldUseHighPrecisionShader = options.shouldUseHighPrecisionShader;\r\n\r\n        if (source.indexOf(\"precision highp float\") === -1) {\r\n            if (!shouldUseHighPrecisionShader) {\r\n                source = \"precision mediump float;\\n\" + source;\r\n            } else {\r\n                source = \"precision highp float;\\n\" + source;\r\n            }\r\n        } else {\r\n            if (!shouldUseHighPrecisionShader) {\r\n                // Moving highp to mediump\r\n                source = source.replace(\"precision highp float\", \"precision mediump float\");\r\n            }\r\n        }\r\n\r\n        return source;\r\n    }\r\n\r\n    private static _ExtractOperation(expression: string) {\r\n        const regex = /defined\\((.+)\\)/;\r\n\r\n        const match = regex.exec(expression);\r\n\r\n        if (match && match.length) {\r\n            return new ShaderDefineIsDefinedOperator(match[1].trim(), expression[0] === \"!\");\r\n        }\r\n\r\n        const operators = [\"==\", \"!=\", \">=\", \"<=\", \"<\", \">\"];\r\n        let operator = \"\";\r\n        let indexOperator = 0;\r\n\r\n        for (operator of operators) {\r\n            indexOperator = expression.indexOf(operator);\r\n\r\n            if (indexOperator > -1) {\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (indexOperator === -1) {\r\n            return new ShaderDefineIsDefinedOperator(expression);\r\n        }\r\n\r\n        const define = expression.substring(0, indexOperator).trim();\r\n        const value = expression.substring(indexOperator + operator.length).trim();\r\n\r\n        return new ShaderDefineArithmeticOperator(define, operator, value);\r\n    }\r\n\r\n    private static _BuildSubExpression(expression: string): ShaderDefineExpression {\r\n        expression = expression.replace(regexSE, \"defined[$1]\");\r\n\r\n        const postfix = ShaderDefineExpression.infixToPostfix(expression);\r\n\r\n        const stack: (string | ShaderDefineExpression)[] = [];\r\n\r\n        for (const c of postfix) {\r\n            if (c !== \"||\" && c !== \"&&\") {\r\n                stack.push(c);\r\n            } else if (stack.length >= 2) {\r\n                let v1 = stack[stack.length - 1],\r\n                    v2 = stack[stack.length - 2];\r\n\r\n                stack.length -= 2;\r\n\r\n                const operator = c == \"&&\" ? new ShaderDefineAndOperator() : new ShaderDefineOrOperator();\r\n\r\n                if (typeof v1 === \"string\") {\r\n                    v1 = v1.replace(regexSERevert, \"defined($1)\");\r\n                }\r\n\r\n                if (typeof v2 === \"string\") {\r\n                    v2 = v2.replace(regexSERevert, \"defined($1)\");\r\n                }\r\n\r\n                operator.leftOperand = typeof v2 === \"string\" ? this._ExtractOperation(v2) : v2;\r\n                operator.rightOperand = typeof v1 === \"string\" ? this._ExtractOperation(v1) : v1;\r\n\r\n                stack.push(operator);\r\n            }\r\n        }\r\n\r\n        let result = stack[stack.length - 1];\r\n\r\n        if (typeof result === \"string\") {\r\n            result = result.replace(regexSERevert, \"defined($1)\");\r\n        }\r\n\r\n        // note: stack.length !== 1 if there was an error in the parsing\r\n\r\n        return typeof result === \"string\" ? this._ExtractOperation(result) : result;\r\n    }\r\n\r\n    private static _BuildExpression(line: string, start: number): ShaderCodeTestNode {\r\n        const node = new ShaderCodeTestNode();\r\n        const command = line.substring(0, start);\r\n        let expression = line.substring(start);\r\n\r\n        expression = expression.substring(0, (expression.indexOf(\"//\") + 1 || expression.length + 1) - 1).trim();\r\n\r\n        if (command === \"#ifdef\") {\r\n            node.testExpression = new ShaderDefineIsDefinedOperator(expression);\r\n        } else if (command === \"#ifndef\") {\r\n            node.testExpression = new ShaderDefineIsDefinedOperator(expression, true);\r\n        } else {\r\n            node.testExpression = this._BuildSubExpression(expression);\r\n        }\r\n\r\n        return node;\r\n    }\r\n\r\n    private static _MoveCursorWithinIf(cursor: ShaderCodeCursor, rootNode: ShaderCodeConditionNode, ifNode: ShaderCodeNode) {\r\n        let line = cursor.currentLine;\r\n        while (this._MoveCursor(cursor, ifNode)) {\r\n            line = cursor.currentLine;\r\n            const first5 = line.substring(0, 5).toLowerCase();\r\n\r\n            if (first5 === \"#else\") {\r\n                const elseNode = new ShaderCodeNode();\r\n                rootNode.children.push(elseNode);\r\n                this._MoveCursor(cursor, elseNode);\r\n                return;\r\n            } else if (first5 === \"#elif\") {\r\n                const elifNode = this._BuildExpression(line, 5);\r\n\r\n                rootNode.children.push(elifNode);\r\n                ifNode = elifNode;\r\n            }\r\n        }\r\n    }\r\n\r\n    private static _MoveCursor(cursor: ShaderCodeCursor, rootNode: ShaderCodeNode): boolean {\r\n        while (cursor.canRead) {\r\n            cursor.lineIndex++;\r\n            const line = cursor.currentLine;\r\n\r\n            if (line.indexOf(\"#\") >= 0) {\r\n                const matches = ShaderProcessor._MoveCursorRegex.exec(line);\r\n\r\n                if (matches && matches.length) {\r\n                    const keyword = matches[0];\r\n\r\n                    switch (keyword) {\r\n                        case \"#ifdef\": {\r\n                            const newRootNode = new ShaderCodeConditionNode();\r\n                            rootNode.children.push(newRootNode);\r\n\r\n                            const ifNode = this._BuildExpression(line, 6);\r\n                            newRootNode.children.push(ifNode);\r\n                            this._MoveCursorWithinIf(cursor, newRootNode, ifNode);\r\n                            break;\r\n                        }\r\n                        case \"#else\":\r\n                        case \"#elif\":\r\n                            return true;\r\n                        case \"#endif\":\r\n                            return false;\r\n                        case \"#ifndef\": {\r\n                            const newRootNode = new ShaderCodeConditionNode();\r\n                            rootNode.children.push(newRootNode);\r\n\r\n                            const ifNode = this._BuildExpression(line, 7);\r\n                            newRootNode.children.push(ifNode);\r\n                            this._MoveCursorWithinIf(cursor, newRootNode, ifNode);\r\n                            break;\r\n                        }\r\n                        case \"#if\": {\r\n                            const newRootNode = new ShaderCodeConditionNode();\r\n                            const ifNode = this._BuildExpression(line, 3);\r\n                            rootNode.children.push(newRootNode);\r\n\r\n                            newRootNode.children.push(ifNode);\r\n                            this._MoveCursorWithinIf(cursor, newRootNode, ifNode);\r\n                            break;\r\n                        }\r\n                    }\r\n                    continue;\r\n                }\r\n            }\r\n\r\n            const newNode = new ShaderCodeNode();\r\n            newNode.line = line;\r\n            rootNode.children.push(newNode);\r\n\r\n            // Detect additional defines\r\n            if (line[0] === \"#\" && line[1] === \"d\") {\r\n                const split = line.replace(\";\", \"\").split(\" \");\r\n                newNode.additionalDefineKey = split[1];\r\n\r\n                if (split.length === 3) {\r\n                    newNode.additionalDefineValue = split[2];\r\n                }\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    private static _EvaluatePreProcessors(sourceCode: string, preprocessors: { [key: string]: string }, options: ProcessingOptions): string {\r\n        const rootNode = new ShaderCodeNode();\r\n        const cursor = new ShaderCodeCursor();\r\n\r\n        cursor.lineIndex = -1;\r\n        cursor.lines = sourceCode.split(\"\\n\");\r\n\r\n        // Decompose (We keep it in 2 steps so it is easier to maintain and perf hit is insignificant)\r\n        this._MoveCursor(cursor, rootNode);\r\n\r\n        // Recompose\r\n        return rootNode.process(preprocessors, options);\r\n    }\r\n\r\n    private static _PreparePreProcessors(options: ProcessingOptions, engine: ThinEngine): { [key: string]: string } {\r\n        const defines = options.defines;\r\n        const preprocessors: { [key: string]: string } = {};\r\n\r\n        for (const define of defines) {\r\n            const keyValue = define.replace(\"#define\", \"\").replace(\";\", \"\").trim();\r\n            const split = keyValue.split(\" \");\r\n            preprocessors[split[0]] = split.length > 1 ? split[1] : \"\";\r\n        }\r\n\r\n        if (options.processor?.shaderLanguage === ShaderLanguage.GLSL) {\r\n            preprocessors[\"GL_ES\"] = \"true\";\r\n        }\r\n        preprocessors[\"__VERSION__\"] = options.version;\r\n        preprocessors[options.platformName] = \"true\";\r\n\r\n        engine._getGlobalDefines(preprocessors);\r\n\r\n        return preprocessors;\r\n    }\r\n\r\n    private static _ProcessShaderConversion(sourceCode: string, options: ProcessingOptions, engine: ThinEngine): string {\r\n        let preparedSourceCode = this._ProcessPrecision(sourceCode, options);\r\n\r\n        if (!options.processor) {\r\n            return preparedSourceCode;\r\n        }\r\n\r\n        // Already converted\r\n        if (options.processor.shaderLanguage === ShaderLanguage.GLSL && preparedSourceCode.indexOf(\"#version 3\") !== -1) {\r\n            preparedSourceCode = preparedSourceCode.replace(\"#version 300 es\", \"\");\r\n            if (!options.processor.parseGLES3) {\r\n                return preparedSourceCode;\r\n            }\r\n        }\r\n\r\n        const defines = options.defines;\r\n\r\n        const preprocessors = this._PreparePreProcessors(options, engine);\r\n\r\n        // General pre processing\r\n        if (options.processor.preProcessor) {\r\n            preparedSourceCode = options.processor.preProcessor(preparedSourceCode, defines, options.isFragment, options.processingContext);\r\n        }\r\n\r\n        preparedSourceCode = this._EvaluatePreProcessors(preparedSourceCode, preprocessors, options);\r\n\r\n        // Post processing\r\n        if (options.processor.postProcessor) {\r\n            preparedSourceCode = options.processor.postProcessor(preparedSourceCode, defines, options.isFragment, options.processingContext, engine);\r\n        }\r\n\r\n        // Inline functions tagged with #define inline\r\n        if (engine._features.needShaderCodeInlining) {\r\n            preparedSourceCode = engine.inlineShaderCode(preparedSourceCode);\r\n        }\r\n\r\n        return preparedSourceCode;\r\n    }\r\n\r\n    private static _ApplyPreProcessing(sourceCode: string, options: ProcessingOptions, engine: ThinEngine): string {\r\n        let preparedSourceCode = sourceCode;\r\n\r\n        const defines = options.defines;\r\n\r\n        const preprocessors = this._PreparePreProcessors(options, engine);\r\n\r\n        // General pre processing\r\n        if (options.processor?.preProcessor) {\r\n            preparedSourceCode = options.processor.preProcessor(preparedSourceCode, defines, options.isFragment, options.processingContext);\r\n        }\r\n\r\n        preparedSourceCode = this._EvaluatePreProcessors(preparedSourceCode, preprocessors, options);\r\n\r\n        // Post processing\r\n        if (options.processor?.postProcessor) {\r\n            preparedSourceCode = options.processor.postProcessor(preparedSourceCode, defines, options.isFragment, options.processingContext, engine);\r\n        }\r\n\r\n        // Inline functions tagged with #define inline\r\n        if (engine._features.needShaderCodeInlining) {\r\n            preparedSourceCode = engine.inlineShaderCode(preparedSourceCode);\r\n        }\r\n\r\n        return preparedSourceCode;\r\n    }\r\n\r\n    /** @internal */\r\n    public static _ProcessIncludes(sourceCode: string, options: ProcessingOptions, callback: (data: any) => void): void {\r\n        reusableMatches.length = 0;\r\n        let match: RegExpMatchArray | null;\r\n        // stay back-compat to the old matchAll syntax\r\n        while ((match = regexShaderInclude.exec(sourceCode)) !== null) {\r\n            reusableMatches.push(match);\r\n        }\r\n\r\n        let returnValue = String(sourceCode);\r\n        let parts = [sourceCode];\r\n\r\n        let keepProcessing = false;\r\n\r\n        for (const match of reusableMatches) {\r\n            let includeFile = match[1];\r\n\r\n            // Uniform declaration\r\n            if (includeFile.indexOf(\"__decl__\") !== -1) {\r\n                includeFile = includeFile.replace(regexShaderDecl, \"\");\r\n                if (options.supportsUniformBuffers) {\r\n                    includeFile = includeFile.replace(\"Vertex\", \"Ubo\").replace(\"Fragment\", \"Ubo\");\r\n                }\r\n                includeFile = includeFile + \"Declaration\";\r\n            }\r\n\r\n            if (options.includesShadersStore[includeFile]) {\r\n                // Substitution\r\n                let includeContent = options.includesShadersStore[includeFile];\r\n                if (match[2]) {\r\n                    const splits = match[3].split(\",\");\r\n\r\n                    for (let index = 0; index < splits.length; index += 2) {\r\n                        const source = new RegExp(splits[index], \"g\");\r\n                        const dest = splits[index + 1];\r\n\r\n                        includeContent = includeContent.replace(source, dest);\r\n                    }\r\n                }\r\n\r\n                if (match[4]) {\r\n                    const indexString = match[5];\r\n\r\n                    if (indexString.indexOf(\"..\") !== -1) {\r\n                        const indexSplits = indexString.split(\"..\");\r\n                        const minIndex = parseInt(indexSplits[0]);\r\n                        let maxIndex = parseInt(indexSplits[1]);\r\n                        let sourceIncludeContent = includeContent.slice(0);\r\n                        includeContent = \"\";\r\n\r\n                        if (isNaN(maxIndex)) {\r\n                            maxIndex = options.indexParameters[indexSplits[1]];\r\n                        }\r\n\r\n                        for (let i = minIndex; i < maxIndex; i++) {\r\n                            if (!options.supportsUniformBuffers) {\r\n                                // Ubo replacement\r\n                                sourceIncludeContent = sourceIncludeContent.replace(regexLightX, (str: string, p1: string) => {\r\n                                    return p1 + \"{X}\";\r\n                                });\r\n                            }\r\n                            includeContent += sourceIncludeContent.replace(regexX, i.toString()) + \"\\n\";\r\n                        }\r\n                    } else {\r\n                        if (!options.supportsUniformBuffers) {\r\n                            // Ubo replacement\r\n                            includeContent = includeContent.replace(regexLightX, (str: string, p1: string) => {\r\n                                return p1 + \"{X}\";\r\n                            });\r\n                        }\r\n                        includeContent = includeContent.replace(regexX, indexString);\r\n                    }\r\n                }\r\n\r\n                // Replace\r\n                // Split all parts on match[0] and intersperse the parts with the include content\r\n                const newParts = [];\r\n                for (const part of parts) {\r\n                    const splitPart = part.split(match[0]);\r\n                    for (let i = 0; i < splitPart.length - 1; i++) {\r\n                        newParts.push(splitPart[i]);\r\n                        newParts.push(includeContent);\r\n                    }\r\n                    newParts.push(splitPart[splitPart.length - 1]);\r\n                }\r\n                parts = newParts;\r\n\r\n                keepProcessing = keepProcessing || includeContent.indexOf(\"#include<\") >= 0 || includeContent.indexOf(\"#include <\") >= 0;\r\n            } else {\r\n                const includeShaderUrl = options.shadersRepository + \"ShadersInclude/\" + includeFile + \".fx\";\r\n\r\n                ShaderProcessor._FileToolsLoadFile(includeShaderUrl, (fileContent) => {\r\n                    options.includesShadersStore[includeFile] = fileContent as string;\r\n                    this._ProcessIncludes(parts.join(\"\"), options, callback);\r\n                });\r\n                return;\r\n            }\r\n        }\r\n        reusableMatches.length = 0;\r\n\r\n        returnValue = parts.join(\"\");\r\n\r\n        if (keepProcessing) {\r\n            this._ProcessIncludes(returnValue.toString(), options, callback);\r\n        } else {\r\n            callback(returnValue);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Loads a file from a url\r\n     * @param url url to load\r\n     * @param onSuccess callback called when the file successfully loads\r\n     * @param onProgress callback called while file is loading (if the server supports this mode)\r\n     * @param offlineProvider defines the offline provider for caching\r\n     * @param useArrayBuffer defines a boolean indicating that date must be returned as ArrayBuffer\r\n     * @param onError callback called when the file fails to load\r\n     * @returns a file request object\r\n     * @internal\r\n     */\r\n    public static _FileToolsLoadFile(\r\n        url: string,\r\n        onSuccess: (data: string | ArrayBuffer, responseURL?: string) => void,\r\n        onProgress?: (ev: ProgressEvent) => void,\r\n        offlineProvider?: IOfflineProvider,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (request?: WebRequest, exception?: LoadFileError) => void\r\n    ): IFileRequest {\r\n        throw _WarnImport(\"FileTools\");\r\n    }\r\n}\r\n"]}