{"version": 3, "file": "shaderStore.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Engines/shaderStore.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAE7D;;GAEG;AACH,MAAM,OAAO,WAAW;IA2BpB;;;;OAIG;IACI,MAAM,CAAC,oBAAoB,CAAC,cAAc,GAAG,cAAc,CAAC,IAAI;QACnE,OAAO,cAAc,KAAK,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAAC,WAAW,CAAC,qBAAqB,CAAC;IACtH,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,eAAe,CAAC,cAAc,GAAG,cAAc,CAAC,IAAI;QAC9D,OAAO,cAAc,KAAK,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,gBAAgB,CAAC;IAC5G,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,uBAAuB,CAAC,cAAc,GAAG,cAAc,CAAC,IAAI;QACtE,OAAO,cAAc,KAAK,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,WAAW,CAAC,wBAAwB,CAAC;IAC5H,CAAC;;AAnDD;;GAEG;AACW,6BAAiB,GAAG,cAAc,CAAC;AACjD;;GAEG;AACW,wBAAY,GAA8B,EAAE,CAAC;AAC3D;;GAEG;AACW,gCAAoB,GAA8B,EAAE,CAAC;AAEnE;;GAEG;AACW,iCAAqB,GAAG,kBAAkB,CAAC;AACzD;;GAEG;AACW,4BAAgB,GAA8B,EAAE,CAAC;AAC/D;;GAEG;AACW,oCAAwB,GAA8B,EAAE,CAAC", "sourcesContent": ["import { ShaderLanguage } from \"../Materials/shaderLanguage\";\r\n\r\n/**\r\n * Defines the shader related stores and directory\r\n */\r\nexport class ShaderStore {\r\n    /**\r\n     * Gets or sets the relative url used to load shaders if using the engine in non-minified mode\r\n     */\r\n    public static ShadersRepository = \"src/Shaders/\";\r\n    /**\r\n     * Store of each shader (The can be looked up using effect.key)\r\n     */\r\n    public static ShadersStore: { [key: string]: string } = {};\r\n    /**\r\n     * Store of each included file for a shader (The can be looked up using effect.key)\r\n     */\r\n    public static IncludesShadersStore: { [key: string]: string } = {};\r\n\r\n    /**\r\n     * Gets or sets the relative url used to load shaders (WGSL) if using the engine in non-minified mode\r\n     */\r\n    public static ShadersRepositoryWGSL = \"src/ShadersWGSL/\";\r\n    /**\r\n     * Store of each shader  (WGSL)\r\n     */\r\n    public static ShadersStoreWGSL: { [key: string]: string } = {};\r\n    /**\r\n     * Store of each included file for a shader (WGSL)\r\n     */\r\n    public static IncludesShadersStoreWGSL: { [key: string]: string } = {};\r\n\r\n    /**\r\n     * Gets the shaders repository path for a given shader language\r\n     * @param shaderLanguage the shader language\r\n     * @returns the path to the shaders repository\r\n     */\r\n    public static GetShadersRepository(shaderLanguage = ShaderLanguage.GLSL): string {\r\n        return shaderLanguage === ShaderLanguage.GLSL ? ShaderStore.ShadersRepository : ShaderStore.ShadersRepositoryWGSL;\r\n    }\r\n\r\n    /**\r\n     * Gets the shaders store of a given shader language\r\n     * @param shaderLanguage the shader language\r\n     * @returns the shaders store\r\n     */\r\n    public static GetShadersStore(shaderLanguage = ShaderLanguage.GLSL): { [key: string]: string } {\r\n        return shaderLanguage === ShaderLanguage.GLSL ? ShaderStore.ShadersStore : ShaderStore.ShadersStoreWGSL;\r\n    }\r\n\r\n    /**\r\n     * Gets the include shaders store of a given shader language\r\n     * @param shaderLanguage the shader language\r\n     * @returns the include shaders store\r\n     */\r\n    public static GetIncludesShadersStore(shaderLanguage = ShaderLanguage.GLSL): { [key: string]: string } {\r\n        return shaderLanguage === ShaderLanguage.GLSL ? ShaderStore.IncludesShadersStore : ShaderStore.IncludesShadersStoreWGSL;\r\n    }\r\n}\r\n"]}