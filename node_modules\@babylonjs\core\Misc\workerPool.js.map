{"version": 3, "file": "workerPool.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/workerPool.ts"], "names": [], "mappings": "AASA;;GAEG;AACH,MAAM,OAAO,UAAU;IAInB;;;OAGG;IACH,YAAY,OAAsB;QANxB,oBAAe,GAAG,IAAI,KAAK,EAAoD,CAAC;QAOtF,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YACzC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;YACtC,IAAI,EAAE,IAAI;SACb,CAAC,CAAC,CAAC;IACR,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,YAAY,EAAE;YACxC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACrC,MAAM,CAAC,SAAS,EAAE,CAAC;YACvB,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,IAAI,CAAC,MAAwD;QAChE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE;YACpC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACrC;IACL,CAAC;IAES,oBAAoB,CAAC,MAAwD;QACnF,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,YAAY,EAAE;YACxC,IAAI,UAAU,CAAC,IAAI,EAAE;gBACjB,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAClC,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAES,QAAQ,CAAC,UAAsB,EAAE,MAAwD;QAC/F,UAAU,CAAC,IAAI,GAAG,KAAK,CAAC;QACxB,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;YACrC,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE;gBAChB,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;gBAChD,IAAI,UAAU,EAAE;oBACZ,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;iBACzC;qBAAM;oBACH,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;iBAC1B;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAYD;;;GAGG;AACH,MAAM,OAAO,qBAAsB,SAAQ,UAAU;IAajD,YAAY,UAAkB,EAAE,iBAAwC,EAAE,OAAO,GAAG,qBAAqB,CAAC,cAAc;QACpH,KAAK,CAAC,EAAE,CAAC,CAAC;QAEV,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAEM,IAAI,CAAC,MAAwD;QAChE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE;YACpC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE;gBAC7C,MAAM,UAAU,GAAe;oBAC3B,aAAa,EAAE,IAAI,CAAC,kBAAkB,EAAE;oBACxC,IAAI,EAAE,KAAK;iBACd,CAAC;gBACF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACnC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;aACrC;iBAAM;gBACH,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACrC;SACJ;IACL,CAAC;IAES,QAAQ,CAAC,UAAsB,EAAE,MAAwD;QAC/F,0BAA0B;QAC1B,IAAI,UAAU,CAAC,SAAS,EAAE;YACtB,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACnC,OAAO,UAAU,CAAC,SAAS,CAAC;SAC/B;QAED,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;YAC9C,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE;gBAChB,UAAU,EAAE,CAAC;gBAEb,IAAI,UAAU,CAAC,IAAI,EAAE;oBACjB,+DAA+D;oBAC/D,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;wBACnC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;4BACrC,MAAM,CAAC,SAAS,EAAE,CAAC;wBACvB,CAAC,CAAC,CAAC;wBAEH,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;wBACtD,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE;4BAChB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;yBACxC;oBACL,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,CAAC;iBAClD;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;;AA7DD;;;GAGG;AACW,oCAAc,GAAiC;IACzD,4BAA4B,EAAE,IAAI;CACrC,CAAC", "sourcesContent": ["import type { IDisposable } from \"../scene\";\r\n\r\n/** @ignore */\r\ninterface WorkerInfo {\r\n    workerPromise: Promise<Worker>;\r\n    idle: boolean;\r\n    timeoutId?: ReturnType<typeof setTimeout>;\r\n}\r\n\r\n/**\r\n * Helper class to push actions to a pool of workers.\r\n */\r\nexport class WorkerPool implements IDisposable {\r\n    protected _workerInfos: Array<WorkerInfo>;\r\n    protected _pendingActions = new Array<(worker: Worker, onComplete: () => void) => void>();\r\n\r\n    /**\r\n     * Constructor\r\n     * @param workers Array of workers to use for actions\r\n     */\r\n    constructor(workers: Array<Worker>) {\r\n        this._workerInfos = workers.map((worker) => ({\r\n            workerPromise: Promise.resolve(worker),\r\n            idle: true,\r\n        }));\r\n    }\r\n\r\n    /**\r\n     * Terminates all workers and clears any pending actions.\r\n     */\r\n    public dispose(): void {\r\n        for (const workerInfo of this._workerInfos) {\r\n            workerInfo.workerPromise.then((worker) => {\r\n                worker.terminate();\r\n            });\r\n        }\r\n\r\n        this._workerInfos.length = 0;\r\n        this._pendingActions.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Pushes an action to the worker pool. If all the workers are active, the action will be\r\n     * pended until a worker has completed its action.\r\n     * @param action The action to perform. Call onComplete when the action is complete.\r\n     */\r\n    public push(action: (worker: Worker, onComplete: () => void) => void): void {\r\n        if (!this._executeOnIdleWorker(action)) {\r\n            this._pendingActions.push(action);\r\n        }\r\n    }\r\n\r\n    protected _executeOnIdleWorker(action: (worker: Worker, onComplete: () => void) => void): boolean {\r\n        for (const workerInfo of this._workerInfos) {\r\n            if (workerInfo.idle) {\r\n                this._execute(workerInfo, action);\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    protected _execute(workerInfo: WorkerInfo, action: (worker: Worker, onComplete: () => void) => void): void {\r\n        workerInfo.idle = false;\r\n        workerInfo.workerPromise.then((worker) => {\r\n            action(worker, () => {\r\n                const nextAction = this._pendingActions.shift();\r\n                if (nextAction) {\r\n                    this._execute(workerInfo, nextAction);\r\n                } else {\r\n                    workerInfo.idle = true;\r\n                }\r\n            });\r\n        });\r\n    }\r\n}\r\n\r\n/**\r\n * Options for AutoReleaseWorkerPool\r\n */\r\nexport interface AutoReleaseWorkerPoolOptions {\r\n    /**\r\n     * Idle time elapsed before workers are terminated.\r\n     */\r\n    idleTimeElapsedBeforeRelease: number;\r\n}\r\n\r\n/**\r\n * Similar to the WorkerPool class except it creates and destroys workers automatically with a maximum of `maxWorkers` workers.\r\n * Workers are terminated when it is idle for at least `idleTimeElapsedBeforeRelease` milliseconds.\r\n */\r\nexport class AutoReleaseWorkerPool extends WorkerPool {\r\n    /**\r\n     * Default options for the constructor.\r\n     * Override to change the defaults.\r\n     */\r\n    public static DefaultOptions: AutoReleaseWorkerPoolOptions = {\r\n        idleTimeElapsedBeforeRelease: 1000,\r\n    };\r\n\r\n    private readonly _maxWorkers: number;\r\n    private readonly _createWorkerAsync: () => Promise<Worker>;\r\n    private readonly _options: AutoReleaseWorkerPoolOptions;\r\n\r\n    constructor(maxWorkers: number, createWorkerAsync: () => Promise<Worker>, options = AutoReleaseWorkerPool.DefaultOptions) {\r\n        super([]);\r\n\r\n        this._maxWorkers = maxWorkers;\r\n        this._createWorkerAsync = createWorkerAsync;\r\n        this._options = options;\r\n    }\r\n\r\n    public push(action: (worker: Worker, onComplete: () => void) => void): void {\r\n        if (!this._executeOnIdleWorker(action)) {\r\n            if (this._workerInfos.length < this._maxWorkers) {\r\n                const workerInfo: WorkerInfo = {\r\n                    workerPromise: this._createWorkerAsync(),\r\n                    idle: false,\r\n                };\r\n                this._workerInfos.push(workerInfo);\r\n                this._execute(workerInfo, action);\r\n            } else {\r\n                this._pendingActions.push(action);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected _execute(workerInfo: WorkerInfo, action: (worker: Worker, onComplete: () => void) => void): void {\r\n        // Reset the idle timeout.\r\n        if (workerInfo.timeoutId) {\r\n            clearTimeout(workerInfo.timeoutId);\r\n            delete workerInfo.timeoutId;\r\n        }\r\n\r\n        super._execute(workerInfo, (worker, onComplete) => {\r\n            action(worker, () => {\r\n                onComplete();\r\n\r\n                if (workerInfo.idle) {\r\n                    // Schedule the worker to be terminated after the elapsed time.\r\n                    workerInfo.timeoutId = setTimeout(() => {\r\n                        workerInfo.workerPromise.then((worker) => {\r\n                            worker.terminate();\r\n                        });\r\n\r\n                        const indexOf = this._workerInfos.indexOf(workerInfo);\r\n                        if (indexOf !== -1) {\r\n                            this._workerInfos.splice(indexOf, 1);\r\n                        }\r\n                    }, this._options.idleTimeElapsedBeforeRelease);\r\n                }\r\n            });\r\n        });\r\n    }\r\n}\r\n"]}