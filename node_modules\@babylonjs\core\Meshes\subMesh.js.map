{"version": 3, "file": "subMesh.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Meshes/subMesh.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAElE,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAEvD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEjD,OAAO,EAAE,uBAAuB,EAAE,MAAM,yBAAyB,CAAC;AAElE,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAYvD;;GAEG;AACH,MAAM,OAAO,OAAO;IAMhB;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAE,IAAI,CAAC,wBAAwB,CAAC,OAA2B,CAAC,CAAC,CAAE,IAAI,CAAC,eAAe,EAAE,EAAE,OAAqC,CAAC;IACvK,CAAC;IAED;;OAEG;IACH,IAAW,eAAe,CAAC,OAAkC;QACzD,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAE,CAAC;QAC5F,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,MAAe,EAAE,mBAAmB,GAAG,KAAK;QAC/D,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;QACpD,IAAI,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,WAAW,IAAI,mBAAmB,EAAE;YACrC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC;SACjG;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,MAAc,EAAE,cAAc,GAAG,IAAI;QAC3D,IAAI,cAAc,EAAE;YAChB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,CAAC;SACzC;QACD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,SAAgB,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,MAAM,IAAI,IAAI,CAAC;IACzH,CAAC;IAED,gBAAgB;IAChB,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAE,CAAC;IACnF,CAAC;IAED,gBAAgB;IAChB,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,OAA8B;QAC7D,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC;IAC5C,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CAAC,MAAwB,EAAE,UAA8C,IAAI,EAAE,eAAkC,EAAE,YAAY,GAAG,IAAI;QAClJ,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;QACtC,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QACrD,IAAI,eAAe,KAAK,SAAS,EAAE;YAC/B,WAAW,CAAC,eAAe,GAAG,eAAe,CAAC;SACjD;QACD,IAAI,CAAC,MAAM,EAAE;YACT,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC;YAC3B,WAAW,CAAC,eAAe,GAAG,SAAS,CAAC;SAC3C;IACL,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,MAAe;QACjC,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,MAAM,KAAK,SAAS,EAAE;gBACtB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;gBAChC,OAAO;aACV;iBAAM;gBACH,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,aAAa,EAAE;oBAC1C,WAAW,EAAE,OAAO,EAAE,CAAC;iBAC1B;aACJ;SACJ;QACD,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC5B,CAAC;IA4BD;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,SAAS,CACnB,aAAqB,EACrB,aAAqB,EACrB,aAAqB,EACrB,UAAkB,EAClB,UAAkB,EAClB,IAAkB,EAClB,aAAoB,EACpB,oBAA6B,IAAI;QAEjC,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,aAAa,EAAE,iBAAiB,CAAC,CAAC;IACpI,CAAC;IAED;;;;;;;;;;;OAWG;IACH;IACI,gCAAgC;IACzB,aAAqB;IAC5B,yBAAyB;IAClB,aAAqB;IAC5B,qBAAqB;IACd,aAAqB;IAC5B,kBAAkB;IACX,UAAkB;IACzB,oBAAoB;IACb,UAAkB,EACzB,IAAkB,EAClB,aAAoB,EACpB,oBAA6B,IAAI,EACjC,SAAS,GAAG,IAAI;QAZT,kBAAa,GAAb,aAAa,CAAQ;QAErB,kBAAa,GAAb,aAAa,CAAQ;QAErB,kBAAa,GAAb,aAAa,CAAQ;QAErB,eAAU,GAAV,UAAU,CAAQ;QAElB,eAAU,GAAV,UAAU,CAAQ;QA7KrB,6BAAwB,GAA0B,IAAI,CAAC;QAoG/D,gBAAgB;QACT,qBAAgB,GAAW,CAAC,CAAC;QAI5B,sBAAiB,GAAyB,IAAI,CAAC;QACvD,gBAAgB;QACT,+BAA0B,GAAwB,IAAI,CAAC;QAG9D,gBAAgB;QACT,iCAA4B,GAAqB,IAAI,CAAC;QAC7D,gBAAgB;QACT,mBAAc,GAAG,KAAK,CAAC;QAE9B,gBAAgB;QACT,cAAS,GAAG,CAAC,CAAC;QACrB,gBAAgB;QACT,gBAAW,GAAW,CAAC,CAAC;QAC/B,gBAAgB;QACT,sBAAiB,GAAW,CAAC,CAAC;QAI7B,qBAAgB,GAAuB,IAAI,CAAC;QAuDhD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,cAAc,GAAG,aAAa,IAAU,IAAI,CAAC;QAClD,IAAI,SAAS,EAAE;YACX,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7B;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC;QACjD,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAE1B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QAErC,IAAI,iBAAiB,EAAE;YACnB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;SACjC;IACL,CAAC;IAED;;;OAGG;IACH,gEAAgE;IAChE,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,aAAa,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACzK,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;YAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;SACvC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,YAA0B;QAC7C,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1F,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAEvG,OAAO,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,kBAAkB,GAAG,IAAI;QACxC,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;QAEpI,IAAI,CAAC,YAAY,EAAE;YACf,OAAO,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC;SAC5E;aAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE;YAC5C,MAAM,iBAAiB,GAAG,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE1E,IAAI,IAAI,CAAC,gBAAgB,KAAK,iBAAiB,EAAE;gBAC7C,IAAI,CAAC,gBAAgB,GAAG,iBAAiB,CAAC;gBAC1C,IAAI,CAAC,cAAc,EAAE,CAAC;aACzB;YAED,OAAO,iBAAiB,CAAC;SAC5B;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAEO,gBAAgB,CAAC,QAAkB;QACvC,OAAQ,QAA0B,CAAC,cAAc,KAAK,SAAS,CAAC;IACpE,CAAC;IAED,UAAU;IAEV;;;;OAIG;IACI,mBAAmB,CAAC,OAA6B,IAAI;QACxD,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QAEvC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;YACxE,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,IAAI,EAAE;YACP,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;SACzE;QAED,IAAI,CAAC,IAAI,EAAE;YACP,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YAClD,OAAO,IAAI,CAAC;SACf;QAED,MAAM,OAAO,GAAiB,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;QAC/D,IAAI,MAA8C,CAAC;QAEnD,2BAA2B;QAC3B,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO,CAAC,MAAM,EAAE;YAC7D,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;YAE3D,6FAA6F;YAC7F,MAAM,GAAG,EAAE,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;SAC7F;aAAM;YACH,MAAM,GAAG,uBAAuB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;SAChI;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;SAClE;aAAM;YACH,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;SACzE;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,QAAkB;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAE5C,OAAO,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;IAClD,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,KAA4B;QAClD,IAAI,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAE1C,IAAI,CAAC,YAAY,EAAE;YACf,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;SACzC;QACD,IAAI,YAAY,EAAE;YACC,YAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAC9C;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,aAAsB;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAE5C,IAAI,CAAC,YAAY,EAAE;YACf,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,YAAY,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IAC/E,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,aAAsB;QAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAE5C,IAAI,CAAC,YAAY,EAAE;YACf,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,YAAY,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;IAC7D,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,eAAwB;QAClC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QACvI,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,OAAqB,EAAE,MAAc;QAC7D,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,MAAM,YAAY,GAAG,EAAE,CAAC;YAExB,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,EAAE;gBACrF,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;aACrI;YAED,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAChE,IAAI,CAAC,gBAAgB,GAAG,YAAY,CAAC,MAAM,CAAC;SAC/C;QACD,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,GAAQ;QACzB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAE5C,IAAI,CAAC,YAAY,EAAE;YACf,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,GAAG,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;;;OAQG;IACI,UAAU,CAAC,GAAQ,EAAE,SAAoB,EAAE,OAAqB,EAAE,SAAmB,EAAE,iBAA4C;QACtI,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,IAAI,CAAC;SACf;QACD,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,YAAY,GAAG,KAAK,CAAC;QAEzB,QAAQ,QAAQ,CAAC,QAAQ,EAAE;YACvB,KAAK,SAAS,CAAC,0BAA0B,CAAC;YAC1C,KAAK,SAAS,CAAC,yBAAyB,CAAC;YACzC,KAAK,SAAS,CAAC,0BAA0B,CAAC;YAC1C,KAAK,SAAS,CAAC,4BAA4B;gBACvC,OAAO,IAAI,CAAC;YAChB,KAAK,SAAS,CAAC,8BAA8B;gBACzC,IAAI,GAAG,CAAC,CAAC;gBACT,YAAY,GAAG,IAAI,CAAC;gBACpB,MAAM;YACV;gBACI,MAAM;SACb;QAED,wCAAwC;QACxC,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,CAAC,yBAAyB,EAAE;YAC3D,6BAA6B;YAC7B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACjB,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,EAAE,SAAS,EAAE,OAAO,EAAG,IAAI,CAAC,KAAa,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;aACvH;YACD,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,SAAS,EAAE,OAAO,EAAG,IAAI,CAAC,KAAa,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;SAC9G;aAAM;YACH,6BAA6B;YAC7B,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;gBAC1C,OAAO,IAAI,CAAC,4BAA4B,CAAC,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;aACnG;YAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;SAC9G;IACL,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,GAAQ,EAAE,SAAoB,EAAE,OAAqB,EAAE,qBAA6B,EAAE,SAAmB;QAC7H,IAAI,aAAa,GAA+B,IAAI,CAAC;QAErD,YAAY;QACZ,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,EAAE;YACrF,MAAM,EAAE,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;YACrC,MAAM,EAAE,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;YAEzC,MAAM,MAAM,GAAG,GAAG,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,EAAE,qBAAqB,CAAC,CAAC;YACtE,IAAI,MAAM,GAAG,CAAC,EAAE;gBACZ,SAAS;aACZ;YAED,IAAI,SAAS,IAAI,CAAC,aAAa,IAAI,MAAM,GAAG,aAAa,CAAC,QAAQ,EAAE;gBAChE,aAAa,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;gBACzD,aAAa,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;gBACjC,IAAI,SAAS,EAAE;oBACX,MAAM;iBACT;aACJ;SACJ;QACD,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,GAAQ,EAAE,SAAoB,EAAE,OAAqB,EAAE,qBAA6B,EAAE,SAAmB;QACtI,IAAI,aAAa,GAA+B,IAAI,CAAC;QAErD,YAAY;QACZ,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,KAAK,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,KAAK,IAAI,CAAC,EAAE;YAC9F,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;YAC5B,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAEhC,MAAM,MAAM,GAAG,GAAG,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,EAAE,qBAAqB,CAAC,CAAC;YACtE,IAAI,MAAM,GAAG,CAAC,EAAE;gBACZ,SAAS;aACZ;YAED,IAAI,SAAS,IAAI,CAAC,aAAa,IAAI,MAAM,GAAG,aAAa,CAAC,QAAQ,EAAE;gBAChE,aAAa,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;gBACzD,aAAa,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;gBACjC,IAAI,SAAS,EAAE;oBACX,MAAM;iBACT;aACJ;SACJ;QAED,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,mBAAmB,CACvB,GAAQ,EACR,SAAoB,EACpB,OAAqB,EACrB,IAAY,EACZ,YAAqB,EACrB,SAAmB,EACnB,iBAA4C;QAE5C,IAAI,aAAa,GAA+B,IAAI,CAAC;QAErD,iBAAiB;QACjB,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;QAChB,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,KAAK,IAAI,IAAI,EAAE;YACrG,MAAM,EAAE,CAAC;YACT,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAC9B,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAClC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAElC,IAAI,YAAY,IAAI,MAAM,KAAK,UAAU,EAAE;gBACvC,KAAK,IAAI,CAAC,CAAC;gBACX,SAAS;aACZ;YAED,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;YAC7B,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;YAC7B,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;YAE7B,8DAA8D;YAC9D,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE;gBACnB,SAAS;aACZ;YAED,IAAI,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;gBAClF,SAAS;aACZ;YAED,MAAM,oBAAoB,GAAG,GAAG,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAEhE,IAAI,oBAAoB,EAAE;gBACtB,IAAI,oBAAoB,CAAC,QAAQ,GAAG,CAAC,EAAE;oBACnC,SAAS;iBACZ;gBAED,IAAI,SAAS,IAAI,CAAC,aAAa,IAAI,oBAAoB,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,EAAE;oBACvF,aAAa,GAAG,oBAAoB,CAAC;oBACrC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;oBAE9B,IAAI,SAAS,EAAE;wBACX,MAAM;qBACT;iBACJ;aACJ;SACJ;QACD,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,4BAA4B,CAChC,GAAQ,EACR,SAAoB,EACpB,OAAqB,EACrB,SAAmB,EACnB,iBAA4C;QAE5C,IAAI,aAAa,GAA+B,IAAI,CAAC;QACrD,iBAAiB;QACjB,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,KAAK,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,KAAK,IAAI,CAAC,EAAE;YAC9F,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;YAC5B,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAChC,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAEhC,IAAI,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACtE,SAAS;aACZ;YAED,MAAM,oBAAoB,GAAG,GAAG,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAEhE,IAAI,oBAAoB,EAAE;gBACtB,IAAI,oBAAoB,CAAC,QAAQ,GAAG,CAAC,EAAE;oBACnC,SAAS;iBACZ;gBAED,IAAI,SAAS,IAAI,CAAC,aAAa,IAAI,oBAAoB,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,EAAE;oBACvF,aAAa,GAAG,oBAAoB,CAAC;oBACrC,aAAa,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;oBAEjC,IAAI,SAAS,EAAE;wBACX,MAAM;qBACT;iBACJ;aACJ;SACJ;QACD,OAAO,aAAa,CAAC;IACzB,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACjC;IACL,CAAC;IAED,QAAQ;IACR;;;;;OAKG;IACI,KAAK,CAAC,OAAqB,EAAE,gBAAuB;QACvD,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;QAE3J,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAE5C,IAAI,CAAC,YAAY,EAAE;gBACf,OAAO,MAAM,CAAC;aACjB;YAED,MAAM,CAAC,aAAa,GAAG,IAAI,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;SACvF;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,UAAU;IAEV;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACzE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACjC;QAED,mBAAmB;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAEtC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,UAAU;IACV;;;;;;;;;OASG;IACI,MAAM,CAAC,iBAAiB,CAC3B,aAAqB,EACrB,UAAkB,EAClB,UAAkB,EAClB,IAAkB,EAClB,aAAoB,EACpB,oBAA6B,IAAI;QAEjC,IAAI,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC;QACtC,IAAI,cAAc,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QAEvC,MAAM,cAAc,GAAG,aAAa,IAAI,IAAI,CAAC;QAC7C,MAAM,OAAO,GAAG,cAAe,CAAC,UAAU,EAAG,CAAC;QAE9C,KAAK,IAAI,KAAK,GAAG,UAAU,EAAE,KAAK,GAAG,UAAU,GAAG,UAAU,EAAE,KAAK,EAAE,EAAE;YACnE,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAEnC,IAAI,WAAW,GAAG,cAAc,EAAE;gBAC9B,cAAc,GAAG,WAAW,CAAC;aAChC;YACD,IAAI,WAAW,GAAG,cAAc,EAAE;gBAC9B,cAAc,GAAG,WAAW,CAAC;aAChC;SACJ;QAED,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE,cAAc,EAAE,cAAc,GAAG,cAAc,GAAG,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,aAAa,EAAE,iBAAiB,CAAC,CAAC;IAC3J,CAAC;CACJ", "sourcesContent": ["import type { Nullable, IndicesArray, DeepImmutable, FloatArray } from \"../types\";\r\nimport type { Matrix, Vector3 } from \"../Maths/math.vector\";\r\nimport type { Engine } from \"../Engines/engine\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport { IntersectionInfo } from \"../Collisions/intersectionInfo\";\r\nimport type { ICullable } from \"../Culling/boundingInfo\";\r\nimport { BoundingInfo } from \"../Culling/boundingInfo\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport { extractMinAndMaxIndexed } from \"../Maths/math.functions\";\r\nimport type { Plane } from \"../Maths/math.plane\";\r\nimport { DrawWrapper } from \"../Materials/drawWrapper\";\r\nimport type { IMaterialContext } from \"../Engines/IMaterialContext\";\r\n\r\nimport type { Collider } from \"../Collisions/collider\";\r\nimport type { Material } from \"../Materials/material\";\r\nimport type { MaterialDefines } from \"../Materials/materialDefines\";\r\nimport type { MultiMaterial } from \"../Materials/multiMaterial\";\r\nimport type { AbstractMesh } from \"./abstractMesh\";\r\nimport type { Mesh } from \"./mesh\";\r\nimport type { Ray } from \"../Culling/ray\";\r\nimport type { TrianglePickingPredicate } from \"../Culling/ray\";\r\n\r\n/**\r\n * Defines a subdivision inside a mesh\r\n */\r\nexport class SubMesh implements ICullable {\r\n    private _engine: Engine;\r\n    /** @internal */\r\n    public _drawWrappers: Array<DrawWrapper>; // index in this array = pass id\r\n    private _mainDrawWrapperOverride: Nullable<DrawWrapper> = null;\r\n\r\n    /**\r\n     * Gets material defines used by the effect associated to the sub mesh\r\n     */\r\n    public get materialDefines(): Nullable<MaterialDefines> {\r\n        return this._mainDrawWrapperOverride ? (this._mainDrawWrapperOverride.defines as MaterialDefines) : (this._getDrawWrapper()?.defines as Nullable<MaterialDefines>);\r\n    }\r\n\r\n    /**\r\n     * Sets material defines used by the effect associated to the sub mesh\r\n     */\r\n    public set materialDefines(defines: Nullable<MaterialDefines>) {\r\n        const drawWrapper = this._mainDrawWrapperOverride ?? this._getDrawWrapper(undefined, true)!;\r\n        drawWrapper.defines = defines;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getDrawWrapper(passId?: number, createIfNotExisting = false): DrawWrapper | undefined {\r\n        passId = passId ?? this._engine.currentRenderPassId;\r\n        let drawWrapper = this._drawWrappers[passId];\r\n        if (!drawWrapper && createIfNotExisting) {\r\n            this._drawWrappers[passId] = drawWrapper = new DrawWrapper(this._mesh.getScene().getEngine());\r\n        }\r\n        return drawWrapper;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _removeDrawWrapper(passId: number, disposeWrapper = true) {\r\n        if (disposeWrapper) {\r\n            this._drawWrappers[passId]?.dispose();\r\n        }\r\n        this._drawWrappers[passId] = undefined as any;\r\n    }\r\n\r\n    /**\r\n     * Gets associated (main) effect (possibly the effect override if defined)\r\n     */\r\n    public get effect(): Nullable<Effect> {\r\n        return this._mainDrawWrapperOverride ? this._mainDrawWrapperOverride.effect : this._getDrawWrapper()?.effect ?? null;\r\n    }\r\n\r\n    /** @internal */\r\n    public get _drawWrapper(): DrawWrapper {\r\n        return this._mainDrawWrapperOverride ?? this._getDrawWrapper(undefined, true)!;\r\n    }\r\n\r\n    /** @internal */\r\n    public get _drawWrapperOverride(): Nullable<DrawWrapper> {\r\n        return this._mainDrawWrapperOverride;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setMainDrawWrapperOverride(wrapper: Nullable<DrawWrapper>): void {\r\n        this._mainDrawWrapperOverride = wrapper;\r\n    }\r\n\r\n    /**\r\n     * Sets associated effect (effect used to render this submesh)\r\n     * @param effect defines the effect to associate with\r\n     * @param defines defines the set of defines used to compile this effect\r\n     * @param materialContext material context associated to the effect\r\n     * @param resetContext true to reset the draw context\r\n     */\r\n    public setEffect(effect: Nullable<Effect>, defines: Nullable<string | MaterialDefines> = null, materialContext?: IMaterialContext, resetContext = true) {\r\n        const drawWrapper = this._drawWrapper;\r\n        drawWrapper.setEffect(effect, defines, resetContext);\r\n        if (materialContext !== undefined) {\r\n            drawWrapper.materialContext = materialContext;\r\n        }\r\n        if (!effect) {\r\n            drawWrapper.defines = null;\r\n            drawWrapper.materialContext = undefined;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Resets the draw wrappers cache\r\n     * @param passId If provided, releases only the draw wrapper corresponding to this render pass id\r\n     */\r\n    public resetDrawCache(passId?: number): void {\r\n        if (this._drawWrappers) {\r\n            if (passId !== undefined) {\r\n                this._removeDrawWrapper(passId);\r\n                return;\r\n            } else {\r\n                for (const drawWrapper of this._drawWrappers) {\r\n                    drawWrapper?.dispose();\r\n                }\r\n            }\r\n        }\r\n        this._drawWrappers = [];\r\n    }\r\n\r\n    /** @internal */\r\n    public _linesIndexCount: number = 0;\r\n    private _mesh: AbstractMesh;\r\n    private _renderingMesh: Mesh;\r\n    private _boundingInfo: BoundingInfo;\r\n    private _linesIndexBuffer: Nullable<DataBuffer> = null;\r\n    /** @internal */\r\n    public _lastColliderWorldVertices: Nullable<Vector3[]> = null;\r\n    /** @internal */\r\n    public _trianglePlanes: Plane[];\r\n    /** @internal */\r\n    public _lastColliderTransformMatrix: Nullable<Matrix> = null;\r\n    /** @internal */\r\n    public _wasDispatched = false;\r\n\r\n    /** @internal */\r\n    public _renderId = 0;\r\n    /** @internal */\r\n    public _alphaIndex: number = 0;\r\n    /** @internal */\r\n    public _distanceToCamera: number = 0;\r\n    /** @internal */\r\n    public _id: number;\r\n\r\n    private _currentMaterial: Nullable<Material> = null;\r\n\r\n    /**\r\n     * Add a new submesh to a mesh\r\n     * @param materialIndex defines the material index to use\r\n     * @param verticesStart defines vertex index start\r\n     * @param verticesCount defines vertices count\r\n     * @param indexStart defines index start\r\n     * @param indexCount defines indices count\r\n     * @param mesh defines the parent mesh\r\n     * @param renderingMesh defines an optional rendering mesh\r\n     * @param createBoundingBox defines if bounding box should be created for this submesh\r\n     * @returns the new submesh\r\n     */\r\n    public static AddToMesh(\r\n        materialIndex: number,\r\n        verticesStart: number,\r\n        verticesCount: number,\r\n        indexStart: number,\r\n        indexCount: number,\r\n        mesh: AbstractMesh,\r\n        renderingMesh?: Mesh,\r\n        createBoundingBox: boolean = true\r\n    ): SubMesh {\r\n        return new SubMesh(materialIndex, verticesStart, verticesCount, indexStart, indexCount, mesh, renderingMesh, createBoundingBox);\r\n    }\r\n\r\n    /**\r\n     * Creates a new submesh\r\n     * @param materialIndex defines the material index to use\r\n     * @param verticesStart defines vertex index start\r\n     * @param verticesCount defines vertices count\r\n     * @param indexStart defines index start\r\n     * @param indexCount defines indices count\r\n     * @param mesh defines the parent mesh\r\n     * @param renderingMesh defines an optional rendering mesh\r\n     * @param createBoundingBox defines if bounding box should be created for this submesh\r\n     * @param addToMesh defines a boolean indicating that the submesh must be added to the mesh.subMeshes array (true by default)\r\n     */\r\n    constructor(\r\n        /** the material index to use */\r\n        public materialIndex: number,\r\n        /** vertex index start */\r\n        public verticesStart: number,\r\n        /** vertices count */\r\n        public verticesCount: number,\r\n        /** index start */\r\n        public indexStart: number,\r\n        /** indices count */\r\n        public indexCount: number,\r\n        mesh: AbstractMesh,\r\n        renderingMesh?: Mesh,\r\n        createBoundingBox: boolean = true,\r\n        addToMesh = true\r\n    ) {\r\n        this._mesh = mesh;\r\n        this._renderingMesh = renderingMesh || <Mesh>mesh;\r\n        if (addToMesh) {\r\n            mesh.subMeshes.push(this);\r\n        }\r\n\r\n        this._engine = this._mesh.getScene().getEngine();\r\n        this.resetDrawCache();\r\n        this._trianglePlanes = [];\r\n\r\n        this._id = mesh.subMeshes.length - 1;\r\n\r\n        if (createBoundingBox) {\r\n            this.refreshBoundingInfo();\r\n            mesh.computeWorldMatrix(true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns true if this submesh covers the entire parent mesh\r\n     * @ignorenaming\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get IsGlobal(): boolean {\r\n        return this.verticesStart === 0 && this.verticesCount === this._mesh.getTotalVertices() && this.indexStart === 0 && this.indexCount === this._mesh.getTotalIndices();\r\n    }\r\n\r\n    /**\r\n     * Returns the submesh BoundingInfo object\r\n     * @returns current bounding info (or mesh's one if the submesh is global)\r\n     */\r\n    public getBoundingInfo(): BoundingInfo {\r\n        if (this.IsGlobal || this._mesh.hasThinInstances) {\r\n            return this._mesh.getBoundingInfo();\r\n        }\r\n\r\n        return this._boundingInfo;\r\n    }\r\n\r\n    /**\r\n     * Sets the submesh BoundingInfo\r\n     * @param boundingInfo defines the new bounding info to use\r\n     * @returns the SubMesh\r\n     */\r\n    public setBoundingInfo(boundingInfo: BoundingInfo): SubMesh {\r\n        this._boundingInfo = boundingInfo;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Returns the mesh of the current submesh\r\n     * @returns the parent mesh\r\n     */\r\n    public getMesh(): AbstractMesh {\r\n        return this._mesh;\r\n    }\r\n\r\n    /**\r\n     * Returns the rendering mesh of the submesh\r\n     * @returns the rendering mesh (could be different from parent mesh)\r\n     */\r\n    public getRenderingMesh(): Mesh {\r\n        return this._renderingMesh;\r\n    }\r\n\r\n    /**\r\n     * Returns the replacement mesh of the submesh\r\n     * @returns the replacement mesh (could be different from parent mesh)\r\n     */\r\n    public getReplacementMesh(): Nullable<AbstractMesh> {\r\n        return this._mesh._internalAbstractMeshDataInfo._actAsRegularMesh ? this._mesh : null;\r\n    }\r\n\r\n    /**\r\n     * Returns the effective mesh of the submesh\r\n     * @returns the effective mesh (could be different from parent mesh)\r\n     */\r\n    public getEffectiveMesh(): AbstractMesh {\r\n        const replacementMesh = this._mesh._internalAbstractMeshDataInfo._actAsRegularMesh ? this._mesh : null;\r\n\r\n        return replacementMesh ? replacementMesh : this._renderingMesh;\r\n    }\r\n\r\n    /**\r\n     * Returns the submesh material\r\n     * @param getDefaultMaterial Defines whether or not to get the default material if nothing has been defined.\r\n     * @returns null or the current material\r\n     */\r\n    public getMaterial(getDefaultMaterial = true): Nullable<Material> {\r\n        const rootMaterial = this._renderingMesh.getMaterialForRenderPass(this._engine.currentRenderPassId) ?? this._renderingMesh.material;\r\n\r\n        if (!rootMaterial) {\r\n            return getDefaultMaterial ? this._mesh.getScene().defaultMaterial : null;\r\n        } else if (this._isMultiMaterial(rootMaterial)) {\r\n            const effectiveMaterial = rootMaterial.getSubMaterial(this.materialIndex);\r\n\r\n            if (this._currentMaterial !== effectiveMaterial) {\r\n                this._currentMaterial = effectiveMaterial;\r\n                this.resetDrawCache();\r\n            }\r\n\r\n            return effectiveMaterial;\r\n        }\r\n\r\n        return rootMaterial;\r\n    }\r\n\r\n    private _isMultiMaterial(material: Material): material is MultiMaterial {\r\n        return (material as MultiMaterial).getSubMaterial !== undefined;\r\n    }\r\n\r\n    // Methods\r\n\r\n    /**\r\n     * Sets a new updated BoundingInfo object to the submesh\r\n     * @param data defines an optional position array to use to determine the bounding info\r\n     * @returns the SubMesh\r\n     */\r\n    public refreshBoundingInfo(data: Nullable<FloatArray> = null): SubMesh {\r\n        this._lastColliderWorldVertices = null;\r\n\r\n        if (this.IsGlobal || !this._renderingMesh || !this._renderingMesh.geometry) {\r\n            return this;\r\n        }\r\n\r\n        if (!data) {\r\n            data = this._renderingMesh.getVerticesData(VertexBuffer.PositionKind);\r\n        }\r\n\r\n        if (!data) {\r\n            this._boundingInfo = this._mesh.getBoundingInfo();\r\n            return this;\r\n        }\r\n\r\n        const indices = <IndicesArray>this._renderingMesh.getIndices();\r\n        let extend: { minimum: Vector3; maximum: Vector3 };\r\n\r\n        //is this the only submesh?\r\n        if (this.indexStart === 0 && this.indexCount === indices.length) {\r\n            const boundingInfo = this._renderingMesh.getBoundingInfo();\r\n\r\n            //the rendering mesh's bounding info can be used, it is the standard submesh for all indices.\r\n            extend = { minimum: boundingInfo.minimum.clone(), maximum: boundingInfo.maximum.clone() };\r\n        } else {\r\n            extend = extractMinAndMaxIndexed(data, indices, this.indexStart, this.indexCount, this._renderingMesh.geometry.boundingBias);\r\n        }\r\n\r\n        if (this._boundingInfo) {\r\n            this._boundingInfo.reConstruct(extend.minimum, extend.maximum);\r\n        } else {\r\n            this._boundingInfo = new BoundingInfo(extend.minimum, extend.maximum);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _checkCollision(collider: Collider): boolean {\r\n        const boundingInfo = this.getBoundingInfo();\r\n\r\n        return boundingInfo._checkCollision(collider);\r\n    }\r\n\r\n    /**\r\n     * Updates the submesh BoundingInfo\r\n     * @param world defines the world matrix to use to update the bounding info\r\n     * @returns the submesh\r\n     */\r\n    public updateBoundingInfo(world: DeepImmutable<Matrix>): SubMesh {\r\n        let boundingInfo = this.getBoundingInfo();\r\n\r\n        if (!boundingInfo) {\r\n            this.refreshBoundingInfo();\r\n            boundingInfo = this.getBoundingInfo();\r\n        }\r\n        if (boundingInfo) {\r\n            (<BoundingInfo>boundingInfo).update(world);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * True is the submesh bounding box intersects the frustum defined by the passed array of planes.\r\n     * @param frustumPlanes defines the frustum planes\r\n     * @returns true if the submesh is intersecting with the frustum\r\n     */\r\n    public isInFrustum(frustumPlanes: Plane[]): boolean {\r\n        const boundingInfo = this.getBoundingInfo();\r\n\r\n        if (!boundingInfo) {\r\n            return false;\r\n        }\r\n        return boundingInfo.isInFrustum(frustumPlanes, this._mesh.cullingStrategy);\r\n    }\r\n\r\n    /**\r\n     * True is the submesh bounding box is completely inside the frustum defined by the passed array of planes\r\n     * @param frustumPlanes defines the frustum planes\r\n     * @returns true if the submesh is inside the frustum\r\n     */\r\n    public isCompletelyInFrustum(frustumPlanes: Plane[]): boolean {\r\n        const boundingInfo = this.getBoundingInfo();\r\n\r\n        if (!boundingInfo) {\r\n            return false;\r\n        }\r\n        return boundingInfo.isCompletelyInFrustum(frustumPlanes);\r\n    }\r\n\r\n    /**\r\n     * Renders the submesh\r\n     * @param enableAlphaMode defines if alpha needs to be used\r\n     * @returns the submesh\r\n     */\r\n    public render(enableAlphaMode: boolean): SubMesh {\r\n        this._renderingMesh.render(this, enableAlphaMode, this._mesh._internalAbstractMeshDataInfo._actAsRegularMesh ? this._mesh : undefined);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getLinesIndexBuffer(indices: IndicesArray, engine: Engine): DataBuffer {\r\n        if (!this._linesIndexBuffer) {\r\n            const linesIndices = [];\r\n\r\n            for (let index = this.indexStart; index < this.indexStart + this.indexCount; index += 3) {\r\n                linesIndices.push(indices[index], indices[index + 1], indices[index + 1], indices[index + 2], indices[index + 2], indices[index]);\r\n            }\r\n\r\n            this._linesIndexBuffer = engine.createIndexBuffer(linesIndices);\r\n            this._linesIndexCount = linesIndices.length;\r\n        }\r\n        return this._linesIndexBuffer;\r\n    }\r\n\r\n    /**\r\n     * Checks if the submesh intersects with a ray\r\n     * @param ray defines the ray to test\r\n     * @returns true is the passed ray intersects the submesh bounding box\r\n     */\r\n    public canIntersects(ray: Ray): boolean {\r\n        const boundingInfo = this.getBoundingInfo();\r\n\r\n        if (!boundingInfo) {\r\n            return false;\r\n        }\r\n        return ray.intersectsBox(boundingInfo.boundingBox);\r\n    }\r\n\r\n    /**\r\n     * Intersects current submesh with a ray\r\n     * @param ray defines the ray to test\r\n     * @param positions defines mesh's positions array\r\n     * @param indices defines mesh's indices array\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n     * @returns intersection info or null if no intersection\r\n     */\r\n    public intersects(ray: Ray, positions: Vector3[], indices: IndicesArray, fastCheck?: boolean, trianglePredicate?: TrianglePickingPredicate): Nullable<IntersectionInfo> {\r\n        const material = this.getMaterial();\r\n        if (!material) {\r\n            return null;\r\n        }\r\n        let step = 3;\r\n        let checkStopper = false;\r\n\r\n        switch (material.fillMode) {\r\n            case Constants.MATERIAL_PointListDrawMode:\r\n            case Constants.MATERIAL_LineLoopDrawMode:\r\n            case Constants.MATERIAL_LineStripDrawMode:\r\n            case Constants.MATERIAL_TriangleFanDrawMode:\r\n                return null;\r\n            case Constants.MATERIAL_TriangleStripDrawMode:\r\n                step = 1;\r\n                checkStopper = true;\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n\r\n        // LineMesh first as it's also a Mesh...\r\n        if (material.fillMode === Constants.MATERIAL_LineListDrawMode) {\r\n            // Check if mesh is unindexed\r\n            if (!indices.length) {\r\n                return this._intersectUnIndexedLines(ray, positions, indices, (this._mesh as any).intersectionThreshold, fastCheck);\r\n            }\r\n            return this._intersectLines(ray, positions, indices, (this._mesh as any).intersectionThreshold, fastCheck);\r\n        } else {\r\n            // Check if mesh is unindexed\r\n            if (!indices.length && this._mesh._unIndexed) {\r\n                return this._intersectUnIndexedTriangles(ray, positions, indices, fastCheck, trianglePredicate);\r\n            }\r\n\r\n            return this._intersectTriangles(ray, positions, indices, step, checkStopper, fastCheck, trianglePredicate);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    private _intersectLines(ray: Ray, positions: Vector3[], indices: IndicesArray, intersectionThreshold: number, fastCheck?: boolean): Nullable<IntersectionInfo> {\r\n        let intersectInfo: Nullable<IntersectionInfo> = null;\r\n\r\n        // Line test\r\n        for (let index = this.indexStart; index < this.indexStart + this.indexCount; index += 2) {\r\n            const p0 = positions[indices[index]];\r\n            const p1 = positions[indices[index + 1]];\r\n\r\n            const length = ray.intersectionSegment(p0, p1, intersectionThreshold);\r\n            if (length < 0) {\r\n                continue;\r\n            }\r\n\r\n            if (fastCheck || !intersectInfo || length < intersectInfo.distance) {\r\n                intersectInfo = new IntersectionInfo(null, null, length);\r\n                intersectInfo.faceId = index / 2;\r\n                if (fastCheck) {\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n        return intersectInfo;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    private _intersectUnIndexedLines(ray: Ray, positions: Vector3[], indices: IndicesArray, intersectionThreshold: number, fastCheck?: boolean): Nullable<IntersectionInfo> {\r\n        let intersectInfo: Nullable<IntersectionInfo> = null;\r\n\r\n        // Line test\r\n        for (let index = this.verticesStart; index < this.verticesStart + this.verticesCount; index += 2) {\r\n            const p0 = positions[index];\r\n            const p1 = positions[index + 1];\r\n\r\n            const length = ray.intersectionSegment(p0, p1, intersectionThreshold);\r\n            if (length < 0) {\r\n                continue;\r\n            }\r\n\r\n            if (fastCheck || !intersectInfo || length < intersectInfo.distance) {\r\n                intersectInfo = new IntersectionInfo(null, null, length);\r\n                intersectInfo.faceId = index / 2;\r\n                if (fastCheck) {\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n\r\n        return intersectInfo;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    private _intersectTriangles(\r\n        ray: Ray,\r\n        positions: Vector3[],\r\n        indices: IndicesArray,\r\n        step: number,\r\n        checkStopper: boolean,\r\n        fastCheck?: boolean,\r\n        trianglePredicate?: TrianglePickingPredicate\r\n    ): Nullable<IntersectionInfo> {\r\n        let intersectInfo: Nullable<IntersectionInfo> = null;\r\n\r\n        // Triangles test\r\n        let faceId = -1;\r\n        for (let index = this.indexStart; index < this.indexStart + this.indexCount - (3 - step); index += step) {\r\n            faceId++;\r\n            const indexA = indices[index];\r\n            const indexB = indices[index + 1];\r\n            const indexC = indices[index + 2];\r\n\r\n            if (checkStopper && indexC === 0xffffffff) {\r\n                index += 2;\r\n                continue;\r\n            }\r\n\r\n            const p0 = positions[indexA];\r\n            const p1 = positions[indexB];\r\n            const p2 = positions[indexC];\r\n\r\n            // stay defensive and don't check against undefined positions.\r\n            if (!p0 || !p1 || !p2) {\r\n                continue;\r\n            }\r\n\r\n            if (trianglePredicate && !trianglePredicate(p0, p1, p2, ray, indexA, indexB, indexC)) {\r\n                continue;\r\n            }\r\n\r\n            const currentIntersectInfo = ray.intersectsTriangle(p0, p1, p2);\r\n\r\n            if (currentIntersectInfo) {\r\n                if (currentIntersectInfo.distance < 0) {\r\n                    continue;\r\n                }\r\n\r\n                if (fastCheck || !intersectInfo || currentIntersectInfo.distance < intersectInfo.distance) {\r\n                    intersectInfo = currentIntersectInfo;\r\n                    intersectInfo.faceId = faceId;\r\n\r\n                    if (fastCheck) {\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return intersectInfo;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    private _intersectUnIndexedTriangles(\r\n        ray: Ray,\r\n        positions: Vector3[],\r\n        indices: IndicesArray,\r\n        fastCheck?: boolean,\r\n        trianglePredicate?: TrianglePickingPredicate\r\n    ): Nullable<IntersectionInfo> {\r\n        let intersectInfo: Nullable<IntersectionInfo> = null;\r\n        // Triangles test\r\n        for (let index = this.verticesStart; index < this.verticesStart + this.verticesCount; index += 3) {\r\n            const p0 = positions[index];\r\n            const p1 = positions[index + 1];\r\n            const p2 = positions[index + 2];\r\n\r\n            if (trianglePredicate && !trianglePredicate(p0, p1, p2, ray, -1, -1, -1)) {\r\n                continue;\r\n            }\r\n\r\n            const currentIntersectInfo = ray.intersectsTriangle(p0, p1, p2);\r\n\r\n            if (currentIntersectInfo) {\r\n                if (currentIntersectInfo.distance < 0) {\r\n                    continue;\r\n                }\r\n\r\n                if (fastCheck || !intersectInfo || currentIntersectInfo.distance < intersectInfo.distance) {\r\n                    intersectInfo = currentIntersectInfo;\r\n                    intersectInfo.faceId = index / 3;\r\n\r\n                    if (fastCheck) {\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return intersectInfo;\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuild(): void {\r\n        if (this._linesIndexBuffer) {\r\n            this._linesIndexBuffer = null;\r\n        }\r\n    }\r\n\r\n    // Clone\r\n    /**\r\n     * Creates a new submesh from the passed mesh\r\n     * @param newMesh defines the new hosting mesh\r\n     * @param newRenderingMesh defines an optional rendering mesh\r\n     * @returns the new submesh\r\n     */\r\n    public clone(newMesh: AbstractMesh, newRenderingMesh?: Mesh): SubMesh {\r\n        const result = new SubMesh(this.materialIndex, this.verticesStart, this.verticesCount, this.indexStart, this.indexCount, newMesh, newRenderingMesh, false);\r\n\r\n        if (!this.IsGlobal) {\r\n            const boundingInfo = this.getBoundingInfo();\r\n\r\n            if (!boundingInfo) {\r\n                return result;\r\n            }\r\n\r\n            result._boundingInfo = new BoundingInfo(boundingInfo.minimum, boundingInfo.maximum);\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    // Dispose\r\n\r\n    /**\r\n     * Release associated resources\r\n     */\r\n    public dispose(): void {\r\n        if (this._linesIndexBuffer) {\r\n            this._mesh.getScene().getEngine()._releaseBuffer(this._linesIndexBuffer);\r\n            this._linesIndexBuffer = null;\r\n        }\r\n\r\n        // Remove from mesh\r\n        const index = this._mesh.subMeshes.indexOf(this);\r\n        this._mesh.subMeshes.splice(index, 1);\r\n\r\n        this.resetDrawCache();\r\n    }\r\n\r\n    /**\r\n     * Gets the class name\r\n     * @returns the string \"SubMesh\".\r\n     */\r\n    public getClassName(): string {\r\n        return \"SubMesh\";\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * Creates a new submesh from indices data\r\n     * @param materialIndex the index of the main mesh material\r\n     * @param startIndex the index where to start the copy in the mesh indices array\r\n     * @param indexCount the number of indices to copy then from the startIndex\r\n     * @param mesh the main mesh to create the submesh from\r\n     * @param renderingMesh the optional rendering mesh\r\n     * @param createBoundingBox defines if bounding box should be created for this submesh\r\n     * @returns a new submesh\r\n     */\r\n    public static CreateFromIndices(\r\n        materialIndex: number,\r\n        startIndex: number,\r\n        indexCount: number,\r\n        mesh: AbstractMesh,\r\n        renderingMesh?: Mesh,\r\n        createBoundingBox: boolean = true\r\n    ): SubMesh {\r\n        let minVertexIndex = Number.MAX_VALUE;\r\n        let maxVertexIndex = -Number.MAX_VALUE;\r\n\r\n        const whatWillRender = renderingMesh || mesh;\r\n        const indices = whatWillRender!.getIndices()!;\r\n\r\n        for (let index = startIndex; index < startIndex + indexCount; index++) {\r\n            const vertexIndex = indices[index];\r\n\r\n            if (vertexIndex < minVertexIndex) {\r\n                minVertexIndex = vertexIndex;\r\n            }\r\n            if (vertexIndex > maxVertexIndex) {\r\n                maxVertexIndex = vertexIndex;\r\n            }\r\n        }\r\n\r\n        return new SubMesh(materialIndex, minVertexIndex, maxVertexIndex - minVertexIndex + 1, startIndex, indexCount, mesh, renderingMesh, createBoundingBox);\r\n    }\r\n}\r\n"]}