{"version": 3, "file": "scalingBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Meshes/Node/Blocks/Matrices/scalingBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,qCAAqC,EAAE,MAAM,8CAA8C,CAAC;AAErG,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAEhE;;GAEG;AACH,MAAM,OAAO,YAAa,SAAQ,iBAAiB;IAC/C;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAEZ,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;QACjG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,MAAM,CAAC,CAAC;IAChF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa;QAChB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YACzB,MAAM,UAAU,GAAG,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACnD,UAAU,CAAC,KAAK,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACxC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC3C;IACL,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;YACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAY,CAAC;YAC7D,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC;IACN,CAAC;CACJ;AAED,aAAa,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC", "sourcesContent": ["import { NodeGeometry<PERSON>lock } from \"../../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../../nodeGeometryBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"../../Enums/nodeGeometryConnectionPointTypes\";\r\nimport type { NodeGeometryBuildState } from \"../../nodeGeometryBuildState\";\r\nimport { GeometryInputBlock } from \"../geometryInputBlock\";\r\nimport { Matrix, Vector3 } from \"../../../../Maths/math.vector\";\r\n\r\n/**\r\n * Block used to get a scaling matrix\r\n */\r\nexport class ScalingBlock extends NodeGeometryBlock {\r\n    /**\r\n     * Create a new ScalingBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"scale\", NodeGeometryBlockConnectionPointTypes.Vector3, false, Vector3.One());\r\n        this.registerOutput(\"matrix\", NodeGeometryBlockConnectionPointTypes.Matrix);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"ScalingBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the scale input component\r\n     */\r\n    public get scale(): NodeGeometryConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the matrix output component\r\n     */\r\n    public get matrix(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public autoConfigure() {\r\n        if (!this.scale.isConnected) {\r\n            const scaleInput = new GeometryInputBlock(\"Scale\");\r\n            scaleInput.value = new Vector3(1, 1, 1);\r\n            scaleInput.output.connectTo(this.scale);\r\n        }\r\n    }\r\n\r\n    protected _buildBlock(state: NodeGeometryBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        this.matrix._storedFunction = (state) => {\r\n            const value = this.scale.getConnectedValue(state) as Vector3;\r\n            return Matrix.Scaling(value.x, value.y, value.z);\r\n        };\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ScalingBlock\", ScalingBlock);\r\n"]}