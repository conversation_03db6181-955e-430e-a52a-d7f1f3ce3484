{"version": 3, "file": "pbrMaterial.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/PBR/pbrMaterial.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3G,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAGxE,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAIhD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AACrD,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AACvC,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AAE1E;;;;;;GAMG;AACH,MAAM,OAAO,WAAY,SAAQ,eAAe;IA+N5C;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;IAC7C,CAAC;IACD,IAAW,iBAAiB,CAAC,KAA4B;QACrD,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC1C,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;SAC9C;aAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,8BAA8B,EAAE;YACxD,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,KAAK,CAAC;SAC/C;IACL,CAAC;IA4CD;;;;;;;OAOG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;IAC7C,CAAC;IACD,IAAW,iBAAiB,CAAC,KAAa;QACtC,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,KAAK,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;IAC7C,CAAC;IACD,IAAW,iBAAiB,CAAC,KAAc;QACvC,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,KAAK,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACH,IAAW,8BAA8B;QACrC,OAAO,IAAI,CAAC,UAAU,CAAC,8BAA8B,CAAC;IAC1D,CAAC;IACD,IAAW,8BAA8B,CAAC,KAAc;QACpD,IAAI,CAAC,UAAU,CAAC,8BAA8B,GAAG,KAAK,CAAC;QACvD,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;SAC9C;IACL,CAAC;IAyFD;;;;OAIG;IAEH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,qBAAqB,CAAC;IACxE,CAAC;IAED;;;;OAIG;IACH,IAAW,uBAAuB,CAAC,KAAc;QAC7C,IAAI,KAAK,KAAK,IAAI,CAAC,uBAAuB,EAAE;YACxC,qCAAqC;YACrC,IAAI,CAAC,gCAAgC,EAAE,CAAC;YAExC,IAAI,KAAK,EAAE;gBACP,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,qBAAqB,CAAC;aAC9D;iBAAM;gBACH,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,qBAAqB,CAAC;aAC9D;SACJ;IACL,CAAC;IAED;;;OAGG;IAEH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,iBAAiB,CAAC;IACpE,CAAC;IAED;;;OAGG;IACH,IAAW,mBAAmB,CAAC,KAAc;QACzC,IAAI,KAAK,KAAK,IAAI,CAAC,mBAAmB,EAAE;YACpC,qCAAqC;YACrC,IAAI,CAAC,gCAAgC,EAAE,CAAC;YAExC,IAAI,KAAK,EAAE;gBACP,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,iBAAiB,CAAC;aAC1D;iBAAM;gBACH,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,qBAAqB,CAAC;aAC9D;SACJ;IACL,CAAC;IAyJD;;OAEG;IACH,IAAW,4BAA4B;QACnC,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;;;OAIG;IACH,IAAW,4BAA4B,CAAC,KAAmC;QACvE,IAAI,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC;QAEhD,qCAAqC;QACrC,IAAI,CAAC,gCAAgC,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;IAChE,CAAC;IACD;;OAEG;IACH,IAAW,wBAAwB,CAAC,KAAc;QAC9C,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,4BAA4B,CAAC,mBAAmB,CAAC;IACjE,CAAC;IACD;;OAEG;IACH,IAAW,yBAAyB,CAAC,KAAc;QAC/C,IAAI,CAAC,4BAA4B,CAAC,mBAAmB,GAAG,KAAK,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC;IACjE,CAAC;IACD;;OAEG;IACH,IAAW,wBAAwB,CAAC,KAAc;QAC9C,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC;IACvD,CAAC;IACD;;;;OAIG;IACH,IAAW,cAAc,CAAC,KAAa;QACnC,IAAI,CAAC,6BAA6B,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,IAAW,cAAc,CAAC,KAAa;QACnC,IAAI,CAAC,6BAA6B,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,6BAA6B,CAAC,mBAAmB,CAAC;IAClE,CAAC;IACD;;OAEG;IACH,IAAW,yBAAyB,CAAC,KAA4B;QAC7D,IAAI,CAAC,6BAA6B,CAAC,mBAAmB,GAAG,KAAK,CAAC;IACnE,CAAC;IAED;;;;;OAKG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,6BAA6B,CAAC,WAAW,CAAC;IAC1D,CAAC;IACD;;;;;OAKG;IACH,IAAW,iBAAiB,CAAC,KAA4B;QACrD,IAAI,CAAC,6BAA6B,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3D,CAAC;IAED;;;;;OAKG;IACH,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAvsBvB;;;WAGG;QAGI,oBAAe,GAAW,GAAG,CAAC;QAErC;;;WAGG;QAGI,sBAAiB,GAAW,GAAG,CAAC;QAEvC;;;WAGG;QAGI,yBAAoB,GAAW,GAAG,CAAC;QAE1C;;;WAGG;QAGI,sBAAiB,GAAW,GAAG,CAAC;QAEvC;;WAEG;QAGI,mBAAc,GAAY,KAAK,CAAC;QAgBvC;;WAEG;QAGI,2BAAsB,GAAW,GAAG,CAAC;QAE5C;;;;WAIG;QAGI,2CAAsC,GAAW,WAAW,CAAC,+BAA+B,CAAC;QAqDpG;;;;;;;;WAQG;QAGI,qBAAgB,GAAG,CAAC,CAAC;QAE5B;;;;;;;;WAQG;QAGI,6BAAwB,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAEjD;;;WAGG;QAGI,kDAA6C,GAAG,KAAK,CAAC;QA0D7D;;WAEG;QAGI,iBAAY,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1C;;WAEG;QAGI,gBAAW,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzC;;WAEG;QAGI,sBAAiB,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE/C;;WAEG;QAGI,oBAAe,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAEnD;;WAEG;QAGI,kBAAa,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3C;;WAEG;QAGI,iBAAY,GAAG,GAAG,CAAC;QAyC1B;;WAEG;QAGI,2BAAsB,GAAG,KAAK,CAAC;QAEtC;;WAEG;QAGI,8BAAyB,GAAG,KAAK,CAAC;QAEzC;;WAEG;QAGI,mBAAc,GAAG,KAAK,CAAC;QAE9B;;WAEG;QAGI,gBAAW,GAAG,GAAG,CAAC;QAEzB;;;WAGG;QAGI,yBAAoB,GAAG,IAAI,CAAC;QAEnC;;WAEG;QAGI,4CAAuC,GAAG,KAAK,CAAC;QAEvD;;WAEG;QAGI,yCAAoC,GAAG,IAAI,CAAC;QAEnD;;;WAGG;QAGI,yCAAoC,GAAG,KAAK,CAAC;QAEpD;;WAEG;QAGI,yCAAoC,GAAG,KAAK,CAAC;QAEpD;;WAEG;QAGI,8CAAyC,GAAG,KAAK,CAAC;QAEzD;;WAEG;QAGI,0BAAqB,GAAG,KAAK,CAAC;QAErC;;;WAGG;QAGI,2CAAsC,GAAG,KAAK,CAAC;QAwDtD;;;WAGG;QAGI,yBAAoB,GAAG,IAAI,CAAC;QAEnC;;WAEG;QAGI,4BAAuB,GAAG,KAAK,CAAC;QAEvC;;WAEG;QAGI,gBAAW,GAAG,KAAK,CAAC;QAE3B;;WAEG;QAGI,yBAAoB,GAAG,KAAK,CAAC;QAEpC;;WAEG;QAGI,sBAAiB,GAAG,IAAI,CAAC;QAEhC;;WAEG;QAGI,oBAAe,GAAG,KAAK,CAAC;QAE/B;;WAEG;QAGI,8BAAyB,GAAG,KAAK,CAAC;QAEzC;;WAEG;QAGI,0BAAqB,GAAG,CAAC,CAAC;QAEjC;;WAEG;QAGI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG;QAGI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG;QAGI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;;WAGG;QAGI,oBAAe,GAAG,KAAK,CAAC;QAE/B;;;WAGG;QAGI,0BAAqB,GAAG,KAAK,CAAC;QAErC;;;;;;;WAOG;QAEI,2BAAsB,GAA0B,IAAI,CAAC;QAE5D;;WAEG;QAGI,uBAAkB,GAAG,KAAK,CAAC;QAElC;;;;WAIG;QAGI,+BAA0B,GAAG,KAAK,CAAC;QAE1C;;;WAGG;QAGI,wBAAmB,GAAG,IAAI,CAAC;QAElC;;;WAGG;QAGI,yBAAoB,GAAG,IAAI,CAAC;QAEnC;;WAEG;QAGI,UAAK,GAAG,KAAK,CAAC;QAErB;;WAEG;QAGI,gCAA2B,GAAG,KAAK,CAAC;QAoIvC,IAAI,CAAC,uBAAuB,GAAG,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,IAAY,EAAE,wBAAiC,IAAI,EAAE,OAAO,GAAG,EAAE;QAC1E,MAAM,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,qBAAqB,EAAE,CAAC,CAAC;QAEvH,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC;QAChB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAEnC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAEnC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC9C,mBAAmB,CAAC,UAAU,GAAG,qBAAqB,CAAC;QAEvD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED,UAAU;IACV;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,MAAM,QAAQ,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAE9G,IAAI,MAAM,CAAC,OAAO,EAAE;YAChB,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAC1D;QAED,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAEzD,6HAA6H;QAC7H,IAAI,MAAM,CAAC,SAAS,EAAE;YAClB,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAC9D;QACD,IAAI,MAAM,CAAC,UAAU,EAAE;YACnB,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAChE;QACD,IAAI,MAAM,CAAC,IAAI,EAAE;YACb,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACpD;QACD,IAAI,MAAM,CAAC,KAAK,EAAE;YACd,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACtD;QACD,IAAI,MAAM,CAAC,UAAU,EAAE;YACnB,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAChE;QACD,IAAI,MAAM,CAAC,WAAW,EAAE;YACpB,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAClE;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;;AAnzBD;;GAEG;AACoB,8BAAkB,GAAG,eAAe,CAAC,kBAAkB,AAArC,CAAsC;AAE/E;;GAEG;AACoB,iCAAqB,GAAG,eAAe,CAAC,qBAAqB,AAAxC,CAAyC;AAErF;;GAEG;AACoB,kCAAsB,GAAG,eAAe,CAAC,sBAAsB,AAAzC,CAA0C;AAEvF;;;GAGG;AACoB,yCAA6B,GAAG,eAAe,CAAC,6BAA6B,AAAhD,CAAiD;AAErG;;;GAGG;AACW,2CAA+B,GAAG,eAAe,CAAC,+BAA+B,AAAlD,CAAmD;AAQzF;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;oDAChB;AAQ9B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;sDACd;AAQhC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;yDACX;AAQnC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;sDACd;AAOhC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;mDACd;AAOhC;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;kDACT;AAOrC;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;mDACR;AAOtC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;2DACT;AASrC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;2EAC+C;AAO7F;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,yCAAyC,CAAC;mDACf;AAOtC;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;sDACL;AAOzC;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;oDACP;AAOvC;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;wDACH;AAO3C;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;oDACP;AAQvC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;6CACnB;AAQ3B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;8CAClB;AAa5B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;qDACzB;AAarB;IAFN,iBAAiB,EAAE;IACnB,gBAAgB,CAAC,kCAAkC,CAAC;6DACJ;AAQ1C;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;kFACQ;AAStD;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;+DACI;AAUlD;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;uDACJ;AAQ1C;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;wDACH;AAO3C;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;gDACX;AAOnC;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,EAAE,IAAI,CAAC;oDACb;AAsBvC;IAFN,iBAAiB,CAAC,SAAS,CAAC;IAC5B,gBAAgB,CAAC,kCAAkC,CAAC;iDACX;AAOnC;IAFN,iBAAiB,CAAC,QAAQ,CAAC;IAC3B,gBAAgB,CAAC,kCAAkC,CAAC;gDACZ;AAOlC;IAFN,iBAAiB,CAAC,cAAc,CAAC;IACjC,gBAAgB,CAAC,kCAAkC,CAAC;sDACN;AAOxC;IAFN,iBAAiB,CAAC,YAAY,CAAC;IAC/B,gBAAgB,CAAC,kCAAkC,CAAC;oDACF;AAO5C;IAFN,iBAAiB,CAAC,UAAU,CAAC;IAC7B,gBAAgB,CAAC,kCAAkC,CAAC;kDACV;AAOpC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;iDAC3B;AA8CnB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;2DACf;AAO/B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,yCAAyC,CAAC;8DACnB;AAOlC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,yCAAyC,CAAC;mDAC9B;AAOvB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,yCAAyC,CAAC;gDACnC;AAQlB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;yDAClB;AAO5B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;4EACE;AAOhD;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;yEACF;AAQ5C;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;yEACD;AAO7C;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;yEACD;AAO7C;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;8EACI;AAOlD;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;0DAChB;AAQ9B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;2EACC;AAQtD;IADC,SAAS,EAAE;0DAGX;AAyBD;IADC,SAAS,EAAE;sDAGX;AAyBM;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;yDAClB;AAO5B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;4DACd;AAOhC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;gDAC1B;AAOpB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;yDACjB;AAO7B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;sDACrB;AAOzB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,gCAAgC,CAAC;oDACpB;AAOxB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;8DACZ;AAOlC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,gCAAgC,CAAC;0DAClB;AAO1B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;qDACrB;AAOzB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;qDACrB;AAOzB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;qDACrB;AAQzB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;oDACtB;AAQxB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;0DAChB;AAW9B;IADN,gBAAgB,CAAC,kCAAkC,CAAC;2DACO;AAOrD;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;uDACnB;AAS3B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;+DACX;AAQnC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;wDACnB;AAQ3B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;yDAClB;AAO5B;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,8BAA8B,CAAC;0CAC5B;AAOd;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,8BAA8B,CAAC;gEACN;AAsN/C,aAAa,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC", "sourcesContent": ["import { serialize, serializeAsColor3, expandToProperty, serializeAsTexture } from \"../../Misc/decorators\";\r\nimport { GetEnvironmentBRDFTexture } from \"../../Misc/brdfTextureTools\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport type { ImageProcessingConfiguration } from \"../../Materials/imageProcessingConfiguration\";\r\nimport type { ColorCurves } from \"../../Materials/colorCurves\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport { RegisterClass } from \"../../Misc/typeStore\";\r\nimport { Material } from \"../material\";\r\nimport { SerializationHelper } from \"../../Misc/decorators.serialization\";\r\n\r\n/**\r\n * The Physically based material of BJS.\r\n *\r\n * This offers the main features of a standard PBR material.\r\n * For more information, please refer to the documentation :\r\n * https://doc.babylonjs.com/features/featuresDeepDive/materials/using/introToPBR\r\n */\r\nexport class PBRMaterial extends PBRBaseMaterial {\r\n    /**\r\n     * PBRMaterialTransparencyMode: No transparency mode, Alpha channel is not use.\r\n     */\r\n    public static readonly PBRMATERIAL_OPAQUE = PBRBaseMaterial.PBRMATERIAL_OPAQUE;\r\n\r\n    /**\r\n     * PBRMaterialTransparencyMode: Alpha Test mode, pixel are discarded below a certain threshold defined by the alpha cutoff value.\r\n     */\r\n    public static readonly PBRMATERIAL_ALPHATEST = PBRBaseMaterial.PBRMATERIAL_ALPHATEST;\r\n\r\n    /**\r\n     * PBRMaterialTransparencyMode: Pixels are blended (according to the alpha mode) with the already drawn pixels in the current frame buffer.\r\n     */\r\n    public static readonly PBRMATERIAL_ALPHABLEND = PBRBaseMaterial.PBRMATERIAL_ALPHABLEND;\r\n\r\n    /**\r\n     * PBRMaterialTransparencyMode: Pixels are blended (according to the alpha mode) with the already drawn pixels in the current frame buffer.\r\n     * They are also discarded below the alpha cutoff threshold to improve performances.\r\n     */\r\n    public static readonly PBRMATERIAL_ALPHATESTANDBLEND = PBRBaseMaterial.PBRMATERIAL_ALPHATESTANDBLEND;\r\n\r\n    /**\r\n     * Defines the default value of how much AO map is occluding the analytical lights\r\n     * (point spot...).\r\n     */\r\n    public static DEFAULT_AO_ON_ANALYTICAL_LIGHTS = PBRBaseMaterial.DEFAULT_AO_ON_ANALYTICAL_LIGHTS;\r\n\r\n    /**\r\n     * Intensity of the direct lights e.g. the four lights available in your scene.\r\n     * This impacts both the direct diffuse and specular highlights.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public directIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Intensity of the emissive part of the material.\r\n     * This helps controlling the emissive effect without modifying the emissive color.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public emissiveIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Intensity of the environment e.g. how much the environment will light the object\r\n     * either through harmonics for rough material or through the reflection for shiny ones.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public environmentIntensity: number = 1.0;\r\n\r\n    /**\r\n     * This is a special control allowing the reduction of the specular highlights coming from the\r\n     * four lights of the scene. Those highlights may not be needed in full environment lighting.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public specularIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Debug Control allowing disabling the bump map on this material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public disableBumpMap: boolean = false;\r\n\r\n    /**\r\n     * AKA Diffuse Texture in standard nomenclature.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public albedoTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * AKA Occlusion Texture in other nomenclature.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public ambientTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * AKA Occlusion Texture Intensity in other nomenclature.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public ambientTextureStrength: number = 1.0;\r\n\r\n    /**\r\n     * Defines how much the AO map is occluding the analytical lights (point spot...).\r\n     * 1 means it completely occludes it\r\n     * 0 mean it has no impact\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public ambientTextureImpactOnAnalyticalLights: number = PBRMaterial.DEFAULT_AO_ON_ANALYTICAL_LIGHTS;\r\n\r\n    /**\r\n     * Stores the alpha values in a texture. Use luminance if texture.getAlphaFromRGB is true.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\r\n    public opacityTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Stores the reflection values in a texture.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public reflectionTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Stores the emissive values in a texture.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public emissiveTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * AKA Specular texture in other nomenclature.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public reflectivityTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Used to switch from specular/glossiness to metallic/roughness workflow.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public metallicTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Specifies the metallic scalar of the metallic/roughness workflow.\r\n     * Can also be used to scale the metalness values of the metallic texture.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public metallic: Nullable<number>;\r\n\r\n    /**\r\n     * Specifies the roughness scalar of the metallic/roughness workflow.\r\n     * Can also be used to scale the roughness values of the metallic texture.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public roughness: Nullable<number>;\r\n\r\n    /**\r\n     * In metallic workflow, specifies an F0 factor to help configuring the material F0.\r\n     * By default the indexOfrefraction is used to compute F0;\r\n     *\r\n     * This is used as a factor against the default reflectance at normal incidence to tweak it.\r\n     *\r\n     * F0 = defaultF0 * metallicF0Factor * metallicReflectanceColor;\r\n     * F90 = metallicReflectanceColor;\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public metallicF0Factor = 1;\r\n\r\n    /**\r\n     * In metallic workflow, specifies an F0 color.\r\n     * By default the F90 is always 1;\r\n     *\r\n     * Please note that this factor is also used as a factor against the default reflectance at normal incidence.\r\n     *\r\n     * F0 = defaultF0_from_IOR * metallicF0Factor * metallicReflectanceColor\r\n     * F90 = metallicF0Factor;\r\n     */\r\n    @serializeAsColor3()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public metallicReflectanceColor = Color3.White();\r\n\r\n    /**\r\n     * Specifies that only the A channel from metallicReflectanceTexture should be used.\r\n     * If false, both RGB and A channels will be used\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useOnlyMetallicFromMetallicReflectanceTexture = false;\r\n\r\n    /**\r\n     * Defines to store metallicReflectanceColor in RGB and metallicF0Factor in A\r\n     * This is multiplied against the scalar values defined in the material.\r\n     * If useOnlyMetallicFromMetallicReflectanceTexture is true, don't use the RGB channels, only A\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public metallicReflectanceTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Defines to store reflectanceColor in RGB\r\n     * This is multiplied against the scalar values defined in the material.\r\n     * If both reflectanceTexture and metallicReflectanceTexture textures are provided and useOnlyMetallicFromMetallicReflectanceTexture\r\n     * is false, metallicReflectanceTexture takes priority and reflectanceTexture is not used\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public reflectanceTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Used to enable roughness/glossiness fetch from a separate channel depending on the current mode.\r\n     * Gray Scale represents roughness in metallic mode and glossiness in specular mode.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public microSurfaceTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Stores surface normal data used to displace a mesh in a texture.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public bumpTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Stores the pre-calculated light information of a mesh in a texture.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", null)\r\n    public lightmapTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Stores the refracted light information in a texture.\r\n     */\r\n    public get refractionTexture(): Nullable<BaseTexture> {\r\n        return this.subSurface.refractionTexture;\r\n    }\r\n    public set refractionTexture(value: Nullable<BaseTexture>) {\r\n        this.subSurface.refractionTexture = value;\r\n        if (value) {\r\n            this.subSurface.isRefractionEnabled = true;\r\n        } else if (!this.subSurface.linkRefractionWithTransparency) {\r\n            this.subSurface.isRefractionEnabled = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * The color of a material in ambient lighting.\r\n     */\r\n    @serializeAsColor3(\"ambient\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public ambientColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * AKA Diffuse Color in other nomenclature.\r\n     */\r\n    @serializeAsColor3(\"albedo\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public albedoColor = new Color3(1, 1, 1);\r\n\r\n    /**\r\n     * AKA Specular Color in other nomenclature.\r\n     */\r\n    @serializeAsColor3(\"reflectivity\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public reflectivityColor = new Color3(1, 1, 1);\r\n\r\n    /**\r\n     * The color reflected from the material.\r\n     */\r\n    @serializeAsColor3(\"reflection\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public reflectionColor = new Color3(1.0, 1.0, 1.0);\r\n\r\n    /**\r\n     * The color emitted from the material.\r\n     */\r\n    @serializeAsColor3(\"emissive\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public emissiveColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * AKA Glossiness in other nomenclature.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public microSurface = 1.0;\r\n\r\n    /**\r\n     * Index of refraction of the material base layer.\r\n     * https://en.wikipedia.org/wiki/List_of_refractive_indices\r\n     *\r\n     * This does not only impact refraction but also the Base F0 of Dielectric Materials.\r\n     *\r\n     * From dielectric fresnel rules: F0 = square((iorT - iorI) / (iorT + iorI))\r\n     */\r\n    public get indexOfRefraction(): number {\r\n        return this.subSurface.indexOfRefraction;\r\n    }\r\n    public set indexOfRefraction(value: number) {\r\n        this.subSurface.indexOfRefraction = value;\r\n    }\r\n\r\n    /**\r\n     * Controls if refraction needs to be inverted on Y. This could be useful for procedural texture.\r\n     */\r\n    public get invertRefractionY(): boolean {\r\n        return this.subSurface.invertRefractionY;\r\n    }\r\n    public set invertRefractionY(value: boolean) {\r\n        this.subSurface.invertRefractionY = value;\r\n    }\r\n\r\n    /**\r\n     * This parameters will make the material used its opacity to control how much it is refracting against not.\r\n     * Materials half opaque for instance using refraction could benefit from this control.\r\n     */\r\n    public get linkRefractionWithTransparency(): boolean {\r\n        return this.subSurface.linkRefractionWithTransparency;\r\n    }\r\n    public set linkRefractionWithTransparency(value: boolean) {\r\n        this.subSurface.linkRefractionWithTransparency = value;\r\n        if (value) {\r\n            this.subSurface.isRefractionEnabled = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If true, the light map contains occlusion information instead of lighting info.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useLightmapAsShadowmap = false;\r\n\r\n    /**\r\n     * Specifies that the alpha is coming form the albedo channel alpha channel for alpha blending.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\r\n    public useAlphaFromAlbedoTexture = false;\r\n\r\n    /**\r\n     * Enforces alpha test in opaque or blend mode in order to improve the performances of some situations.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\r\n    public forceAlphaTest = false;\r\n\r\n    /**\r\n     * Defines the alpha limits in alpha test mode.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\r\n    public alphaCutOff = 0.4;\r\n\r\n    /**\r\n     * Specifies that the material will keep the specular highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good example of that. When sun reflects on it you can not see what is behind.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useSpecularOverAlpha = true;\r\n\r\n    /**\r\n     * Specifies if the reflectivity texture contains the glossiness information in its alpha channel.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useMicroSurfaceFromReflectivityMapAlpha = false;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the roughness information in its alpha channel.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useRoughnessFromMetallicTextureAlpha = true;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the roughness information in its green channel.\r\n     * Needs useRoughnessFromMetallicTextureAlpha to be false.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useRoughnessFromMetallicTextureGreen = false;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the metallness information in its blue channel.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useMetallnessFromMetallicTextureBlue = false;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the ambient occlusion information in its red channel.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useAmbientOcclusionFromMetallicTextureRed = false;\r\n\r\n    /**\r\n     * Specifies if the ambient texture contains the ambient occlusion information in its red channel only.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useAmbientInGrayScale = false;\r\n\r\n    /**\r\n     * In case the reflectivity map does not contain the microsurface information in its alpha channel,\r\n     * The material will try to infer what glossiness each pixel should be.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useAutoMicroSurfaceFromReflectivityMap = false;\r\n\r\n    /**\r\n     * BJS is using an hardcoded light falloff based on a manually sets up range.\r\n     * In PBR, one way to represents the falloff is to use the inverse squared root algorithm.\r\n     * This parameter can help you switch back to the BJS mode in order to create scenes using both materials.\r\n     */\r\n    @serialize()\r\n    public get usePhysicalLightFalloff(): boolean {\r\n        return this._lightFalloff === PBRBaseMaterial.LIGHTFALLOFF_PHYSICAL;\r\n    }\r\n\r\n    /**\r\n     * BJS is using an hardcoded light falloff based on a manually sets up range.\r\n     * In PBR, one way to represents the falloff is to use the inverse squared root algorithm.\r\n     * This parameter can help you switch back to the BJS mode in order to create scenes using both materials.\r\n     */\r\n    public set usePhysicalLightFalloff(value: boolean) {\r\n        if (value !== this.usePhysicalLightFalloff) {\r\n            // Ensure the effect will be rebuilt.\r\n            this._markAllSubMeshesAsTexturesDirty();\r\n\r\n            if (value) {\r\n                this._lightFalloff = PBRBaseMaterial.LIGHTFALLOFF_PHYSICAL;\r\n            } else {\r\n                this._lightFalloff = PBRBaseMaterial.LIGHTFALLOFF_STANDARD;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * In order to support the falloff compatibility with gltf, a special mode has been added\r\n     * to reproduce the gltf light falloff.\r\n     */\r\n    @serialize()\r\n    public get useGLTFLightFalloff(): boolean {\r\n        return this._lightFalloff === PBRBaseMaterial.LIGHTFALLOFF_GLTF;\r\n    }\r\n\r\n    /**\r\n     * In order to support the falloff compatibility with gltf, a special mode has been added\r\n     * to reproduce the gltf light falloff.\r\n     */\r\n    public set useGLTFLightFalloff(value: boolean) {\r\n        if (value !== this.useGLTFLightFalloff) {\r\n            // Ensure the effect will be rebuilt.\r\n            this._markAllSubMeshesAsTexturesDirty();\r\n\r\n            if (value) {\r\n                this._lightFalloff = PBRBaseMaterial.LIGHTFALLOFF_GLTF;\r\n            } else {\r\n                this._lightFalloff = PBRBaseMaterial.LIGHTFALLOFF_STANDARD;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Specifies that the material will keeps the reflection highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good example of that. When the street lights reflects on it you can not see what is behind.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useRadianceOverAlpha = true;\r\n\r\n    /**\r\n     * Allows using an object space normal map (instead of tangent space).\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useObjectSpaceNormalMap = false;\r\n\r\n    /**\r\n     * Allows using the bump map in parallax mode.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useParallax = false;\r\n\r\n    /**\r\n     * Allows using the bump map in parallax occlusion mode.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useParallaxOcclusion = false;\r\n\r\n    /**\r\n     * Controls the scale bias of the parallax mode.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public parallaxScaleBias = 0.05;\r\n\r\n    /**\r\n     * If sets to true, disables all the lights affecting the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public disableLighting = false;\r\n\r\n    /**\r\n     * Force the shader to compute irradiance in the fragment shader in order to take bump in account.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public forceIrradianceInFragment = false;\r\n\r\n    /**\r\n     * Number of Simultaneous lights allowed on the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public maxSimultaneousLights = 4;\r\n\r\n    /**\r\n     * If sets to true, x component of normal map value will invert (x = 1.0 - x).\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public invertNormalMapX = false;\r\n\r\n    /**\r\n     * If sets to true, y component of normal map value will invert (y = 1.0 - y).\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public invertNormalMapY = false;\r\n\r\n    /**\r\n     * If sets to true and backfaceCulling is false, normals will be flipped on the backside.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public twoSidedLighting = false;\r\n\r\n    /**\r\n     * A fresnel is applied to the alpha of the model to ensure grazing angles edges are not alpha tested.\r\n     * And/Or occlude the blended part. (alpha is converted to gamma to compute the fresnel)\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useAlphaFresnel = false;\r\n\r\n    /**\r\n     * A fresnel is applied to the alpha of the model to ensure grazing angles edges are not alpha tested.\r\n     * And/Or occlude the blended part. (alpha stays linear to compute the fresnel)\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useLinearAlphaFresnel = false;\r\n\r\n    /**\r\n     * Let user defines the brdf lookup texture used for IBL.\r\n     * A default 8bit version is embedded but you could point at :\r\n     * * Default texture: https://assets.babylonjs.com/environments/correlatedMSBRDF_RGBD.png\r\n     * * Default 16bit pixel depth texture: https://assets.babylonjs.com/environments/correlatedMSBRDF.dds\r\n     * * LEGACY Default None correlated https://assets.babylonjs.com/environments/uncorrelatedBRDF_RGBD.png\r\n     * * LEGACY Default None correlated 16bit pixel depth https://assets.babylonjs.com/environments/uncorrelatedBRDF.dds\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public environmentBRDFTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Force normal to face away from face.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public forceNormalForward = false;\r\n\r\n    /**\r\n     * Enables specular anti aliasing in the PBR shader.\r\n     * It will both interacts on the Geometry for analytical and IBL lighting.\r\n     * It also prefilter the roughness map based on the bump values.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public enableSpecularAntiAliasing = false;\r\n\r\n    /**\r\n     * This parameters will enable/disable Horizon occlusion to prevent normal maps to look shiny when the normal\r\n     * makes the reflect vector face the model (under horizon).\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useHorizonOcclusion = true;\r\n\r\n    /**\r\n     * This parameters will enable/disable radiance occlusion by preventing the radiance to lit\r\n     * too much the area relying on ambient texture to define their ambient occlusion.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useRadianceOcclusion = true;\r\n\r\n    /**\r\n     * If set to true, no lighting calculations will be applied.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public unlit = false;\r\n\r\n    /**\r\n     * If sets to true, the decal map will be applied after the detail map. Else, it is applied before (default: false)\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public applyDecalMapAfterDetailMap = false;\r\n\r\n    /**\r\n     * Gets the image processing configuration used either in this material.\r\n     */\r\n    public get imageProcessingConfiguration(): ImageProcessingConfiguration {\r\n        return this._imageProcessingConfiguration;\r\n    }\r\n\r\n    /**\r\n     * Sets the Default image processing configuration used either in the this material.\r\n     *\r\n     * If sets to null, the scene one is in use.\r\n     */\r\n    public set imageProcessingConfiguration(value: ImageProcessingConfiguration) {\r\n        this._attachImageProcessingConfiguration(value);\r\n\r\n        // Ensure the effect will be rebuilt.\r\n        this._markAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets whether the color curves effect is enabled.\r\n     */\r\n    public get cameraColorCurvesEnabled(): boolean {\r\n        return this.imageProcessingConfiguration.colorCurvesEnabled;\r\n    }\r\n    /**\r\n     * Sets whether the color curves effect is enabled.\r\n     */\r\n    public set cameraColorCurvesEnabled(value: boolean) {\r\n        this.imageProcessingConfiguration.colorCurvesEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Gets whether the color grading effect is enabled.\r\n     */\r\n    public get cameraColorGradingEnabled(): boolean {\r\n        return this.imageProcessingConfiguration.colorGradingEnabled;\r\n    }\r\n    /**\r\n     * Gets whether the color grading effect is enabled.\r\n     */\r\n    public set cameraColorGradingEnabled(value: boolean) {\r\n        this.imageProcessingConfiguration.colorGradingEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Gets whether tonemapping is enabled or not.\r\n     */\r\n    public get cameraToneMappingEnabled(): boolean {\r\n        return this._imageProcessingConfiguration.toneMappingEnabled;\r\n    }\r\n    /**\r\n     * Sets whether tonemapping is enabled or not\r\n     */\r\n    public set cameraToneMappingEnabled(value: boolean) {\r\n        this._imageProcessingConfiguration.toneMappingEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * The camera exposure used on this material.\r\n     * This property is here and not in the camera to allow controlling exposure without full screen post process.\r\n     * This corresponds to a photographic exposure.\r\n     */\r\n    public get cameraExposure(): number {\r\n        return this._imageProcessingConfiguration.exposure;\r\n    }\r\n    /**\r\n     * The camera exposure used on this material.\r\n     * This property is here and not in the camera to allow controlling exposure without full screen post process.\r\n     * This corresponds to a photographic exposure.\r\n     */\r\n    public set cameraExposure(value: number) {\r\n        this._imageProcessingConfiguration.exposure = value;\r\n    }\r\n\r\n    /**\r\n     * Gets The camera contrast used on this material.\r\n     */\r\n    public get cameraContrast(): number {\r\n        return this._imageProcessingConfiguration.contrast;\r\n    }\r\n\r\n    /**\r\n     * Sets The camera contrast used on this material.\r\n     */\r\n    public set cameraContrast(value: number) {\r\n        this._imageProcessingConfiguration.contrast = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the Color Grading 2D Lookup Texture.\r\n     */\r\n    public get cameraColorGradingTexture(): Nullable<BaseTexture> {\r\n        return this._imageProcessingConfiguration.colorGradingTexture;\r\n    }\r\n    /**\r\n     * Sets the Color Grading 2D Lookup Texture.\r\n     */\r\n    public set cameraColorGradingTexture(value: Nullable<BaseTexture>) {\r\n        this._imageProcessingConfiguration.colorGradingTexture = value;\r\n    }\r\n\r\n    /**\r\n     * The color grading curves provide additional color adjustment that is applied after any color grading transform (3D LUT).\r\n     * They allow basic adjustment of saturation and small exposure adjustments, along with color filter tinting to provide white balance adjustment or more stylistic effects.\r\n     * These are similar to controls found in many professional imaging or colorist software. The global controls are applied to the entire image. For advanced tuning, extra controls are provided to adjust the shadow, midtone and highlight areas of the image;\r\n     * corresponding to low luminance, medium luminance, and high luminance areas respectively.\r\n     */\r\n    public get cameraColorCurves(): Nullable<ColorCurves> {\r\n        return this._imageProcessingConfiguration.colorCurves;\r\n    }\r\n    /**\r\n     * The color grading curves provide additional color adjustment that is applied after any color grading transform (3D LUT).\r\n     * They allow basic adjustment of saturation and small exposure adjustments, along with color filter tinting to provide white balance adjustment or more stylistic effects.\r\n     * These are similar to controls found in many professional imaging or colorist software. The global controls are applied to the entire image. For advanced tuning, extra controls are provided to adjust the shadow, midtone and highlight areas of the image;\r\n     * corresponding to low luminance, medium luminance, and high luminance areas respectively.\r\n     */\r\n    public set cameraColorCurves(value: Nullable<ColorCurves>) {\r\n        this._imageProcessingConfiguration.colorCurves = value;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new PBRMaterial instance.\r\n     *\r\n     * @param name The material name\r\n     * @param scene The scene the material will be use in.\r\n     */\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n\r\n        this._environmentBRDFTexture = GetEnvironmentBRDFTexture(this.getScene());\r\n    }\r\n\r\n    /**\r\n     * @returns the name of this material class.\r\n     */\r\n    public getClassName(): string {\r\n        return \"PBRMaterial\";\r\n    }\r\n\r\n    /**\r\n     * Makes a duplicate of the current material.\r\n     * @param name - name to use for the new material.\r\n     * @param cloneTexturesOnlyOnce - if a texture is used in more than one channel (e.g diffuse and opacity), only clone it once and reuse it on the other channels. Default false.\r\n     * @param rootUrl defines the root URL to use to load textures\r\n     * @returns cloned material instance\r\n     */\r\n    public clone(name: string, cloneTexturesOnlyOnce: boolean = true, rootUrl = \"\"): PBRMaterial {\r\n        const clone = SerializationHelper.Clone(() => new PBRMaterial(name, this.getScene()), this, { cloneTexturesOnlyOnce });\r\n\r\n        clone.id = name;\r\n        clone.name = name;\r\n\r\n        this.stencil.copyTo(clone.stencil);\r\n\r\n        this._clonePlugins(clone, rootUrl);\r\n\r\n        return clone;\r\n    }\r\n\r\n    /**\r\n     * Serializes this PBR Material.\r\n     * @returns - An object with the serialized material.\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n        serializationObject.customType = \"BABYLON.PBRMaterial\";\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * Parses a PBR Material from a serialized object.\r\n     * @param source - Serialized object.\r\n     * @param scene - BJS scene instance.\r\n     * @param rootUrl - url for the scene object\r\n     * @returns - PBRMaterial\r\n     */\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): PBRMaterial {\r\n        const material = SerializationHelper.Parse(() => new PBRMaterial(source.name, scene), source, scene, rootUrl);\r\n\r\n        if (source.stencil) {\r\n            material.stencil.parse(source.stencil, scene, rootUrl);\r\n        }\r\n\r\n        Material._ParsePlugins(source, material, scene, rootUrl);\r\n\r\n        // The code block below ensures backward compatibility with serialized materials before plugins are automatically serialized.\r\n        if (source.clearCoat) {\r\n            material.clearCoat.parse(source.clearCoat, scene, rootUrl);\r\n        }\r\n        if (source.anisotropy) {\r\n            material.anisotropy.parse(source.anisotropy, scene, rootUrl);\r\n        }\r\n        if (source.brdf) {\r\n            material.brdf.parse(source.brdf, scene, rootUrl);\r\n        }\r\n        if (source.sheen) {\r\n            material.sheen.parse(source.sheen, scene, rootUrl);\r\n        }\r\n        if (source.subSurface) {\r\n            material.subSurface.parse(source.subSurface, scene, rootUrl);\r\n        }\r\n        if (source.iridescence) {\r\n            material.iridescence.parse(source.iridescence, scene, rootUrl);\r\n        }\r\n\r\n        return material;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PBRMaterial\", PBRMaterial);\r\n"]}