{"version": 3, "file": "screenSpaceReflectionPostProcess.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/screenSpaceReflectionPostProcess.ts"], "names": [], "mappings": ";AAIA,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,sBAAsB,EAAE,MAAM,qCAAqC,CAAC;AAC7E,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AAEvE,OAAO,EAAE,mCAAmC,EAAE,MAAM,kDAAkD,CAAC;AAEvG,OAAO,2CAA2C,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAIlD,OAAO,EAAE,MAAM,EAAE,0BAAyB;AAE1C;;;;GAIG;AACH,MAAM,OAAO,gCAAiC,SAAQ,WAAW;IA4B7D,IAAY,uBAAuB;QAC/B,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;IAC9C,CAAC;IAED,IAAY,gBAAgB;QACxB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;IACvC,CAAC;IAOD;;;OAGG;IACI,YAAY;QACf,OAAO,kCAAkC,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,YACI,IAAY,EACZ,KAAY,EACZ,OAAoC,EACpC,MAAwB,EACxB,YAAqB,EACrB,MAAe,EACf,QAAkB,EAClB,cAAsB,SAAS,CAAC,wBAAwB,EACxD,gBAAgB,GAAG,KAAK,EACxB,mBAAmB,GAAG,KAAK;QAE3B,KAAK,CACD,IAAI,EACJ,uBAAuB,EACvB,CAAC,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,mCAAmC,EAAE,UAAU,EAAE,UAAU,EAAE,iBAAiB,CAAC,EACnH,CAAC,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE,qBAAqB,CAAC,EAC7E,OAAO,EACP,MAAM,EACN,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,gFAAgF,EAChF,WAAW,EACX,SAAS,EACT,IAAI,EACJ,gBAAgB,CACnB,CAAC;QAhGN;;WAEG;QAEI,cAAS,GAAW,GAAG,CAAC;QAC/B;;WAEG;QAEI,aAAQ,GAAW,CAAC,CAAC;QAC5B;;WAEG;QAEI,sCAAiC,GAAW,CAAC,CAAC;QACrD;;WAEG;QAEI,SAAI,GAAW,GAAG,CAAC;QAC1B;;WAEG;QAEI,oBAAe,GAAW,GAAG,CAAC;QAE7B,yBAAoB,GAAY,KAAK,CAAC;QAiBtC,6BAAwB,GAAY,KAAK,CAAC;QAC1C,uBAAkB,GAAW,EAAE,CAAC;QAChC,iBAAY,GAAW,CAAC,CAAC;QAqD7B,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QAEhD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,iDAAiD;YACjD,MAAM,sBAAsB,GAAG,KAAK,CAAC,4BAA4B,EAAE,CAAC;YACpE,IAAI,sBAAsB,EAAE;gBACxB,IAAI,sBAAsB,CAAC,WAAW,EAAE;oBACpC,sBAAsB,CAAC,cAAc,GAAG,IAAI,CAAC;oBAC7C,sBAAsB,CAAC,kBAAkB,GAAG,IAAI,CAAC;oBAEjD,IAAI,sBAAsB,CAAC,2BAA2B,EAAE;wBACpD,MAAM,CAAC,KAAK,CAAC,sHAAsH,CAAC,CAAC;qBACxI;iBACJ;aACJ;SACJ;aAAM;YACH,MAAM,eAAe,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC;YACtD,eAAe,EAAE,WAAW,EAAE,CAAC;YAC/B,IAAI,eAAe,EAAE,2BAA2B,EAAE;gBAC9C,MAAM,CAAC,KAAK,CAAC,8GAA8G,CAAC,CAAC;aAChI;YACD,IAAI,CAAC,2BAA2B,GAAG,IAAI,mCAAmC,EAAE,CAAC;SAChF;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,0BAA0B;QAC1B,IAAI,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YAC9B,MAAM,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC;YAC5D,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAE9C,IAAI,CAAC,eAAe,IAAI,CAAC,sBAAsB,EAAE;gBAC7C,OAAO;aACV;YAED,IAAI,sBAAsB,EAAE;gBACxB,WAAW;gBACX,MAAM,aAAa,GAAG,sBAAsB,CAAC,eAAe,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;gBAC3G,MAAM,cAAc,GAAG,sBAAsB,CAAC,eAAe,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;gBAEhH,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,sBAAsB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpF,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,sBAAsB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;gBAClG,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,sBAAsB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;aAC1G;iBAAM,IAAI,eAAe,EAAE;gBACxB,WAAW;gBACX,MAAM,aAAa,GAAG,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;gBACxF,MAAM,cAAc,GAAG,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC;gBAC7F,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;gBAEpF,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,eAAe,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC5F,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,eAAe,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;gBAChG,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,eAAe,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;aACxG;YAED,WAAW;YACX,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC;YAClC,IAAI,CAAC,MAAM,EAAE;gBACT,OAAO;aACV;YAED,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,gBAAgB,GAAG,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAE1D,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;YACjD,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,mCAAmC,EAAE,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC7F,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7D,CAAC,CAAC;QAEF,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,oBAAoB,CAAC;IAC1D,CAAC;IAED;;;OAGG;IAEH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED;;;OAGG;IACH,IAAW,uBAAuB,CAAC,OAAgB;QAC/C,IAAI,OAAO,KAAK,IAAI,CAAC,wBAAwB,EAAE;YAC3C,OAAO;SACV;QAED,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC;QACxC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAED;;;OAGG;IAEH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,IAAW,iBAAiB,CAAC,OAAe;QACxC,IAAI,OAAO,KAAK,IAAI,CAAC,kBAAkB,EAAE;YACrC,OAAO;SACV;QAED,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAED;;;;OAIG;IAEH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,KAAK,KAAK,IAAI,CAAC,YAAY,EAAE;YAC7B,OAAO;SACV;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAEO,oBAAoB;QACxB,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvD,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACzC;QACD,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;SACrD;QACD,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAC9C;QAED,OAAO,CAAC,IAAI,CAAC,6BAA6B,GAAG,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7E,OAAO,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC;QAEjE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,iBAAsB,EAAE,YAAoB,EAAE,KAAY,EAAE,OAAe;QAC5F,OAAO,mBAAmB,CAAC,KAAK,CAC5B,GAAG,EAAE;YACD,OAAO,IAAI,gCAAgC,CACvC,iBAAiB,CAAC,IAAI,EACtB,KAAK,EACL,iBAAiB,CAAC,OAAO,EACzB,YAAY,EACZ,iBAAiB,CAAC,wBAAwB,EAC1C,KAAK,CAAC,SAAS,EAAE,EACjB,iBAAiB,CAAC,WAAW,EAC7B,iBAAiB,CAAC,QAAQ,CAC7B,CAAC;QACN,CAAC,EACD,iBAAiB,EACjB,KAAK,EACL,OAAO,CACV,CAAC;IACN,CAAC;CACJ;AArRU;IADN,SAAS,EAAE;mEACmB;AAKxB;IADN,SAAS,EAAE;kEACgB;AAKrB;IADN,SAAS,EAAE;2FACyC;AAK9C;IADN,SAAS,EAAE;8DACc;AAKnB;IADN,SAAS,EAAE;yEACyB;AA0JrC;IADC,SAAS,EAAE;+EAGX;AAoBD;IADC,SAAS,EAAE;yEAGX;AAqBD;IADC,SAAS,EAAE;mEAGX;AA0DL,aAAa,CAAC,0CAA0C,EAAE,gCAAgC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport type { PostProcessOptions } from \"./postProcess\";\r\nimport { PostProcess } from \"./postProcess\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { Geo<PERSON>BufferRenderer } from \"../Rendering/geometryBufferRenderer\";\r\nimport { serialize } from \"../Misc/decorators\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\nimport type { PrePassRenderer } from \"../Rendering/prePassRenderer\";\r\nimport { ScreenSpaceReflectionsConfiguration } from \"../Rendering/screenSpaceReflectionsConfiguration\";\r\n\r\nimport \"../Shaders/screenSpaceReflection.fragment\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\n\r\nimport type { Engine } from \"../Engines/engine\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * The ScreenSpaceReflectionPostProcess performs realtime reflections using only and only the available informations on the screen (positions and normals).\r\n * Basically, the screen space reflection post-process will compute reflections according the material's reflectivity.\r\n * @deprecated Use the new SSRRenderingPipeline instead.\r\n */\r\nexport class ScreenSpaceReflectionPostProcess extends PostProcess {\r\n    /**\r\n     * Gets or sets a reflection threshold mainly used to adjust the reflection's height.\r\n     */\r\n    @serialize()\r\n    public threshold: number = 1.2;\r\n    /**\r\n     * Gets or sets the current reflection strength. 1.0 is an ideal value but can be increased/decreased for particular results.\r\n     */\r\n    @serialize()\r\n    public strength: number = 1;\r\n    /**\r\n     * Gets or sets the falloff exponent used while computing fresnel. More the exponent is high, more the reflections will be discrete.\r\n     */\r\n    @serialize()\r\n    public reflectionSpecularFalloffExponent: number = 3;\r\n    /**\r\n     * Gets or sets the step size used to iterate until the effect finds the color of the reflection's pixel. Typically in interval [0.1, 1.0]\r\n     */\r\n    @serialize()\r\n    public step: number = 1.0;\r\n    /**\r\n     * Gets or sets the factor applied when computing roughness. Default value is 0.2.\r\n     */\r\n    @serialize()\r\n    public roughnessFactor: number = 0.2;\r\n\r\n    private _forceGeometryBuffer: boolean = false;\r\n    private get _geometryBufferRenderer(): Nullable<GeometryBufferRenderer> {\r\n        if (!this._forceGeometryBuffer) {\r\n            return null;\r\n        }\r\n\r\n        return this._scene.geometryBufferRenderer;\r\n    }\r\n\r\n    private get _prePassRenderer(): Nullable<PrePassRenderer> {\r\n        if (this._forceGeometryBuffer) {\r\n            return null;\r\n        }\r\n\r\n        return this._scene.prePassRenderer;\r\n    }\r\n\r\n    private _enableSmoothReflections: boolean = false;\r\n    private _reflectionSamples: number = 64;\r\n    private _smoothSteps: number = 5;\r\n    private _isSceneRightHanded: boolean;\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"ScreenSpaceReflectionPostProcess\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"ScreenSpaceReflectionPostProcess\";\r\n    }\r\n\r\n    /**\r\n     * Creates a new instance of ScreenSpaceReflectionPostProcess.\r\n     * @param name The name of the effect.\r\n     * @param scene The scene containing the objects to calculate reflections.\r\n     * @param options The required width/height ratio to downsize to before computing the render pass.\r\n     * @param camera The camera to apply the render pass to.\r\n     * @param samplingMode The sampling mode to be used when computing the pass. (default: 0)\r\n     * @param engine The engine which the post process will be applied. (default: current engine)\r\n     * @param reusable If the post process can be reused on the same frame. (default: false)\r\n     * @param textureType Type of textures used when performing the post process. (default: 0)\r\n     * @param blockCompilation If compilation of the shader should not be done in the constructor. The updateEffect method can be used to compile the shader at a later time. (default: true)\r\n     * @param forceGeometryBuffer If this post process should use geometry buffer instead of prepass (default: false)\r\n     */\r\n    constructor(\r\n        name: string,\r\n        scene: Scene,\r\n        options: number | PostProcessOptions,\r\n        camera: Nullable<Camera>,\r\n        samplingMode?: number,\r\n        engine?: Engine,\r\n        reusable?: boolean,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        blockCompilation = false,\r\n        forceGeometryBuffer = false\r\n    ) {\r\n        super(\r\n            name,\r\n            \"screenSpaceReflection\",\r\n            [\"projection\", \"view\", \"threshold\", \"reflectionSpecularFalloffExponent\", \"strength\", \"stepSize\", \"roughnessFactor\"],\r\n            [\"textureSampler\", \"normalSampler\", \"positionSampler\", \"reflectivitySampler\"],\r\n            options,\r\n            camera,\r\n            samplingMode,\r\n            engine,\r\n            reusable,\r\n            \"#define SSR_SUPPORTED\\n#define REFLECTION_SAMPLES 64\\n#define SMOOTH_STEPS 5\\n\",\r\n            textureType,\r\n            undefined,\r\n            null,\r\n            blockCompilation\r\n        );\r\n\r\n        this._forceGeometryBuffer = forceGeometryBuffer;\r\n\r\n        if (this._forceGeometryBuffer) {\r\n            // Get geometry buffer renderer and update effect\r\n            const geometryBufferRenderer = scene.enableGeometryBufferRenderer();\r\n            if (geometryBufferRenderer) {\r\n                if (geometryBufferRenderer.isSupported) {\r\n                    geometryBufferRenderer.enablePosition = true;\r\n                    geometryBufferRenderer.enableReflectivity = true;\r\n\r\n                    if (geometryBufferRenderer.generateNormalsInWorldSpace) {\r\n                        Logger.Error(\"ScreenSpaceReflectionPostProcess does not support generateNormalsInWorldSpace=true for the geometry buffer renderer!\");\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            const prePassRenderer = scene.enablePrePassRenderer();\r\n            prePassRenderer?.markAsDirty();\r\n            if (prePassRenderer?.generateNormalsInWorldSpace) {\r\n                Logger.Error(\"ScreenSpaceReflectionPostProcess does not support generateNormalsInWorldSpace=true for the prepass renderer!\");\r\n            }\r\n            this._prePassEffectConfiguration = new ScreenSpaceReflectionsConfiguration();\r\n        }\r\n\r\n        this._updateEffectDefines();\r\n\r\n        // On apply, send uniforms\r\n        this.onApply = (effect: Effect) => {\r\n            const geometryBufferRenderer = this._geometryBufferRenderer;\r\n            const prePassRenderer = this._prePassRenderer;\r\n\r\n            if (!prePassRenderer && !geometryBufferRenderer) {\r\n                return;\r\n            }\r\n\r\n            if (geometryBufferRenderer) {\r\n                // Samplers\r\n                const positionIndex = geometryBufferRenderer.getTextureIndex(GeometryBufferRenderer.POSITION_TEXTURE_TYPE);\r\n                const roughnessIndex = geometryBufferRenderer.getTextureIndex(GeometryBufferRenderer.REFLECTIVITY_TEXTURE_TYPE);\r\n\r\n                effect.setTexture(\"normalSampler\", geometryBufferRenderer.getGBuffer().textures[1]);\r\n                effect.setTexture(\"positionSampler\", geometryBufferRenderer.getGBuffer().textures[positionIndex]);\r\n                effect.setTexture(\"reflectivitySampler\", geometryBufferRenderer.getGBuffer().textures[roughnessIndex]);\r\n            } else if (prePassRenderer) {\r\n                // Samplers\r\n                const positionIndex = prePassRenderer.getIndex(Constants.PREPASS_POSITION_TEXTURE_TYPE);\r\n                const roughnessIndex = prePassRenderer.getIndex(Constants.PREPASS_REFLECTIVITY_TEXTURE_TYPE);\r\n                const normalIndex = prePassRenderer.getIndex(Constants.PREPASS_NORMAL_TEXTURE_TYPE);\r\n\r\n                effect.setTexture(\"normalSampler\", prePassRenderer.getRenderTarget().textures[normalIndex]);\r\n                effect.setTexture(\"positionSampler\", prePassRenderer.getRenderTarget().textures[positionIndex]);\r\n                effect.setTexture(\"reflectivitySampler\", prePassRenderer.getRenderTarget().textures[roughnessIndex]);\r\n            }\r\n\r\n            // Uniforms\r\n            const camera = scene.activeCamera;\r\n            if (!camera) {\r\n                return;\r\n            }\r\n\r\n            const viewMatrix = camera.getViewMatrix(true);\r\n            const projectionMatrix = camera.getProjectionMatrix(true);\r\n\r\n            effect.setMatrix(\"projection\", projectionMatrix);\r\n            effect.setMatrix(\"view\", viewMatrix);\r\n            effect.setFloat(\"threshold\", this.threshold);\r\n            effect.setFloat(\"reflectionSpecularFalloffExponent\", this.reflectionSpecularFalloffExponent);\r\n            effect.setFloat(\"strength\", this.strength);\r\n            effect.setFloat(\"stepSize\", this.step);\r\n            effect.setFloat(\"roughnessFactor\", this.roughnessFactor);\r\n        };\r\n\r\n        this._isSceneRightHanded = scene.useRightHandedSystem;\r\n    }\r\n\r\n    /**\r\n     * Gets whether or not smoothing reflections is enabled.\r\n     * Enabling smoothing will require more GPU power and can generate a drop in FPS.\r\n     */\r\n    @serialize()\r\n    public get enableSmoothReflections(): boolean {\r\n        return this._enableSmoothReflections;\r\n    }\r\n\r\n    /**\r\n     * Sets whether or not smoothing reflections is enabled.\r\n     * Enabling smoothing will require more GPU power and can generate a drop in FPS.\r\n     */\r\n    public set enableSmoothReflections(enabled: boolean) {\r\n        if (enabled === this._enableSmoothReflections) {\r\n            return;\r\n        }\r\n\r\n        this._enableSmoothReflections = enabled;\r\n        this._updateEffectDefines();\r\n    }\r\n\r\n    /**\r\n     * Gets the number of samples taken while computing reflections. More samples count is high,\r\n     * more the post-process wil require GPU power and can generate a drop in FPS. Basically in interval [25, 100].\r\n     */\r\n    @serialize()\r\n    public get reflectionSamples(): number {\r\n        return this._reflectionSamples;\r\n    }\r\n\r\n    /**\r\n     * Sets the number of samples taken while computing reflections. More samples count is high,\r\n     * more the post-process wil require GPU power and can generate a drop in FPS. Basically in interval [25, 100].\r\n     */\r\n    public set reflectionSamples(samples: number) {\r\n        if (samples === this._reflectionSamples) {\r\n            return;\r\n        }\r\n\r\n        this._reflectionSamples = samples;\r\n        this._updateEffectDefines();\r\n    }\r\n\r\n    /**\r\n     * Gets the number of samples taken while smoothing reflections. More samples count is high,\r\n     * more the post-process will require GPU power and can generate a drop in FPS.\r\n     * Default value (5.0) work pretty well in all cases but can be adjusted.\r\n     */\r\n    @serialize()\r\n    public get smoothSteps(): number {\r\n        return this._smoothSteps;\r\n    }\r\n\r\n    /*\r\n     * Sets the number of samples taken while smoothing reflections. More samples count is high,\r\n     * more the post-process will require GPU power and can generate a drop in FPS.\r\n     * Default value (5.0) work pretty well in all cases but can be adjusted.\r\n     */\r\n    public set smoothSteps(steps: number) {\r\n        if (steps === this._smoothSteps) {\r\n            return;\r\n        }\r\n\r\n        this._smoothSteps = steps;\r\n        this._updateEffectDefines();\r\n    }\r\n\r\n    private _updateEffectDefines(): void {\r\n        const defines: string[] = [];\r\n        if (this._geometryBufferRenderer || this._prePassRenderer) {\r\n            defines.push(\"#define SSR_SUPPORTED\");\r\n        }\r\n        if (this._enableSmoothReflections) {\r\n            defines.push(\"#define ENABLE_SMOOTH_REFLECTIONS\");\r\n        }\r\n        if (this._isSceneRightHanded) {\r\n            defines.push(\"#define RIGHT_HANDED_SCENE\");\r\n        }\r\n\r\n        defines.push(\"#define REFLECTION_SAMPLES \" + (this._reflectionSamples >> 0));\r\n        defines.push(\"#define SMOOTH_STEPS \" + (this._smoothSteps >> 0));\r\n\r\n        this.updateEffect(defines.join(\"\\n\"));\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _Parse(parsedPostProcess: any, targetCamera: Camera, scene: Scene, rootUrl: string) {\r\n        return SerializationHelper.Parse(\r\n            () => {\r\n                return new ScreenSpaceReflectionPostProcess(\r\n                    parsedPostProcess.name,\r\n                    scene,\r\n                    parsedPostProcess.options,\r\n                    targetCamera,\r\n                    parsedPostProcess.renderTargetSamplingMode,\r\n                    scene.getEngine(),\r\n                    parsedPostProcess.textureType,\r\n                    parsedPostProcess.reusable\r\n                );\r\n            },\r\n            parsedPostProcess,\r\n            scene,\r\n            rootUrl\r\n        );\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ScreenSpaceReflectionPostProcess\", ScreenSpaceReflectionPostProcess);\r\n"]}