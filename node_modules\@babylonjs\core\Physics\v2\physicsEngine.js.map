{"version": 3, "file": "physicsEngine.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Physics/v2/physicsEngine.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAIlD,OAAO,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAC/D,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAGlD;;;GAGG;AACH,MAAM,OAAO,aAAa;IAUtB;;;OAGG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;IAClD,CAAC;IACD,uDAAuD;IACvD;;;OAGG;IACI,MAAM,CAAC,oBAAoB;QAC9B,MAAM,WAAW,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACH,YACI,OAA0B,EAClB,iBAAyC,aAAa,CAAC,oBAAoB,EAAE;QAA7E,mBAAc,GAAd,cAAc,CAA+D;QAhCzF,gBAAgB;QACR,mBAAc,GAAuB,EAAE,CAAC;QACxC,iBAAY,GAAW,CAAC,CAAC;QAgC7B,OAAO,GAAG,OAAO,IAAI,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,OAAgB;QAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;;OAOG;IACI,WAAW,CAAC,cAAsB,CAAC,GAAG,EAAE;QAC3C,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;IAC7C,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,cAAsB,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,oBAAoB;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;IACpC,CAAC;IAED;;;;OAIG;IAEH;;;OAGG;IACI,KAAK,CAAC,KAAa;QACtB,IAAI,KAAK,GAAG,GAAG,EAAE;YACb,KAAK,GAAG,GAAG,CAAC;SACf;aAAM,IAAI,KAAK,IAAI,CAAC,EAAE;YACnB,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC;SACtB;QAED,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IAChE,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,WAAwB;QACnC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC1C,CAAC;IACD;;;OAGG;IACI,UAAU,CAAC,WAAwB;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACvD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC5D;IACL,CAAC;IACD;;OAEG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,YAAY,CAAC,IAAa,EAAE,EAAW,EAAE,MAA4B,EAAE,KAAqB;QAC/F,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC;IAED;;;;;;OAMG;IACI,OAAO,CAAC,IAAa,EAAE,EAAW,EAAE,KAAqB;QAC5D,MAAM,MAAM,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC1C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACrD,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { IPhysicsEngine } from \"../IPhysicsEngine\";\r\nimport type { IPhysicsEnginePluginV2 } from \"./IPhysicsEnginePlugin\";\r\nimport type { IRaycastQuery } from \"../physicsRaycastResult\";\r\nimport { PhysicsRaycastResult } from \"../physicsRaycastResult\";\r\nimport { _WarnImport } from \"../../Misc/devTools\";\r\nimport type { PhysicsBody } from \"./physicsBody\";\r\n\r\n/**\r\n * Class used to control physics engine\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport class PhysicsEngine implements IPhysicsEngine {\r\n    /** @internal */\r\n    private _physicsBodies: Array<PhysicsBody> = [];\r\n    private _subTimeStep: number = 0;\r\n\r\n    /**\r\n     * Gets the gravity vector used by the simulation\r\n     */\r\n    public gravity: Vector3;\r\n\r\n    /**\r\n     *\r\n     * @returns physics plugin version\r\n     */\r\n    public getPluginVersion(): number {\r\n        return this._physicsPlugin.getPluginVersion();\r\n    }\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Factory used to create the default physics plugin.\r\n     * @returns The default physics plugin\r\n     */\r\n    public static DefaultPluginFactory(): IPhysicsEnginePluginV2 {\r\n        throw _WarnImport(\"\");\r\n    }\r\n\r\n    /**\r\n     * Creates a new Physics Engine\r\n     * @param gravity defines the gravity vector used by the simulation\r\n     * @param _physicsPlugin defines the plugin to use (CannonJS by default)\r\n     */\r\n    constructor(\r\n        gravity: Nullable<Vector3>,\r\n        private _physicsPlugin: IPhysicsEnginePluginV2 = PhysicsEngine.DefaultPluginFactory()\r\n    ) {\r\n        gravity = gravity || new Vector3(0, -9.807, 0);\r\n        this.setGravity(gravity);\r\n        this.setTimeStep();\r\n    }\r\n\r\n    /**\r\n     * Sets the gravity vector used by the simulation\r\n     * @param gravity defines the gravity vector to use\r\n     */\r\n    public setGravity(gravity: Vector3): void {\r\n        this.gravity = gravity;\r\n        this._physicsPlugin.setGravity(this.gravity);\r\n    }\r\n\r\n    /**\r\n     * Set the time step of the physics engine.\r\n     * Default is 1/60.\r\n     * To slow it down, enter 1/600 for example.\r\n     * To speed it up, 1/30\r\n     * Unit is seconds.\r\n     * @param newTimeStep defines the new timestep to apply to this world.\r\n     */\r\n    public setTimeStep(newTimeStep: number = 1 / 60) {\r\n        this._physicsPlugin.setTimeStep(newTimeStep);\r\n    }\r\n\r\n    /**\r\n     * Get the time step of the physics engine.\r\n     * @returns the current time step\r\n     */\r\n    public getTimeStep(): number {\r\n        return this._physicsPlugin.getTimeStep();\r\n    }\r\n\r\n    /**\r\n     * Set the sub time step of the physics engine.\r\n     * Default is 0 meaning there is no sub steps\r\n     * To increase physics resolution precision, set a small value (like 1 ms)\r\n     * @param subTimeStep defines the new sub timestep used for physics resolution.\r\n     */\r\n    public setSubTimeStep(subTimeStep: number = 0) {\r\n        this._subTimeStep = subTimeStep;\r\n    }\r\n\r\n    /**\r\n     * Get the sub time step of the physics engine.\r\n     * @returns the current sub time step\r\n     */\r\n    public getSubTimeStep() {\r\n        return this._subTimeStep;\r\n    }\r\n\r\n    /**\r\n     * Release all resources\r\n     */\r\n    public dispose(): void {\r\n        this._physicsPlugin.dispose();\r\n    }\r\n\r\n    /**\r\n     * Gets the name of the current physics plugin\r\n     * @returns the name of the plugin\r\n     */\r\n    public getPhysicsPluginName(): string {\r\n        return this._physicsPlugin.name;\r\n    }\r\n\r\n    /**\r\n     * Adding a new impostor for the impostor tracking.\r\n     * This will be done by the impostor itself.\r\n     * @param impostor the impostor to add\r\n     */\r\n\r\n    /**\r\n     * Called by the scene. No need to call it.\r\n     * @param delta defines the timespan between frames\r\n     */\r\n    public _step(delta: number) {\r\n        if (delta > 0.1) {\r\n            delta = 0.1;\r\n        } else if (delta <= 0) {\r\n            delta = 1.0 / 60.0;\r\n        }\r\n\r\n        this._physicsPlugin.executeStep(delta, this._physicsBodies);\r\n    }\r\n\r\n    /**\r\n     * Add a body as an active component of this engine\r\n     * @param physicsBody The body to add\r\n     */\r\n    public addBody(physicsBody: PhysicsBody): void {\r\n        this._physicsBodies.push(physicsBody);\r\n    }\r\n    /**\r\n     * Removes a particular body from this engine\r\n     * @param physicsBody The body to remove from the simulation\r\n     */\r\n    public removeBody(physicsBody: PhysicsBody): void {\r\n        const index = this._physicsBodies.indexOf(physicsBody);\r\n        if (index > -1) {\r\n            /*const removed =*/ this._physicsBodies.splice(index, 1);\r\n        }\r\n    }\r\n    /**\r\n     * @returns an array of bodies added to this engine\r\n     */\r\n    public getBodies(): Array<PhysicsBody> {\r\n        return this._physicsBodies;\r\n    }\r\n\r\n    /**\r\n     * Gets the current plugin used to run the simulation\r\n     * @returns current plugin\r\n     */\r\n    public getPhysicsPlugin(): IPhysicsEnginePluginV2 {\r\n        return this._physicsPlugin;\r\n    }\r\n\r\n    /**\r\n     * Does a raycast in the physics world\r\n     * @param from when should the ray start?\r\n     * @param to when should the ray end?\r\n     * @param result resulting PhysicsRaycastResult\r\n     * @param query raycast query object\r\n     */\r\n    public raycastToRef(from: Vector3, to: Vector3, result: PhysicsRaycastResult, query?: IRaycastQuery): void {\r\n        this._physicsPlugin.raycast(from, to, result, query);\r\n    }\r\n\r\n    /**\r\n     * Does a raycast in the physics world\r\n     * @param from when should the ray start?\r\n     * @param to when should the ray end?\r\n     * @param query raycast query object\r\n     * @returns PhysicsRaycastResult\r\n     */\r\n    public raycast(from: Vector3, to: Vector3, query?: IRaycastQuery): PhysicsRaycastResult {\r\n        const result = new PhysicsRaycastResult();\r\n        this._physicsPlugin.raycast(from, to, result, query);\r\n        return result;\r\n    }\r\n}\r\n"]}