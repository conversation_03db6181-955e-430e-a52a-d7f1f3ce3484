{"version": 3, "file": "webgpuEngine.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Engines/webgpuEngine.ts"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AACvC,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,eAAe,EAAE,qBAAqB,EAAE,MAAM,uCAAuC,CAAC;AAE/F,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,gEAAgE;AAChE,OAAO,KAAK,eAAe,MAAM,0BAA0B,CAAC;AAC5D,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAEjD,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AAKvE,OAAO,EAAE,yBAAyB,EAAE,MAAM,qCAAqC,CAAC;AAChF,OAAO,EAAE,yBAAyB,EAAE,MAAM,qCAAqC,CAAC;AAEhF,OAAO,EAAE,6BAA6B,EAAE,MAAM,wCAAwC,CAAC;AACvF,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EAAE,oBAAoB,EAAE,MAAM,+BAA+B,CAAC;AAErE,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AAEnE,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AAEvE,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAEjE,OAAO,EAAE,6BAA6B,EAAE,MAAM,wCAAwC,CAAC;AACvF,OAAO,EAAE,0BAA0B,EAAE,MAAM,qCAAqC,CAAC;AACjF,OAAO,EAAE,uBAAuB,EAAE,MAAM,kCAAkC,CAAC;AAC3E,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AACvE,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AACvE,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAE3D,OAAO,EAAE,0BAA0B,EAAE,uBAAuB,EAAE,0BAA0B,EAAE,wBAAwB,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AACxK,OAAO,EAAE,oBAAoB,EAAE,MAAM,+BAA+B,CAAC;AAErE,OAAO,EAAE,oBAAoB,EAAE,MAAM,+BAA+B,CAAC;AACrE,OAAO,EAAE,iBAAiB,EAAE,MAAM,gCAAgC,CAAC;AAEnE,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAEzD,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AACvE,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAE7D,OAAO,EAAE,uBAAuB,EAAE,MAAM,kCAAkC,CAAC;AAI3E,OAAO,yBAAyB,CAAC;AAEjC,OAAO,mCAAmC,CAAC;AAK3C,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAG/D,MAAM,mCAAmC,GAA6B;IAClE,KAAK,EAAE,qCAAqC;IAC5C,SAAS,EAAE,eAAe,CAAC,gBAAgB,CAAC,GAAG;IAC/C,MAAM,EAAE,SAAgB;IACxB,aAAa,EAAE,CAAC;IAChB,eAAe,EAAE,CAAC;CACrB,CAAC;AAEF,MAAM,uBAAuB,GAA6B;IACtD,KAAK,EAAE,uBAAuB;IAC9B,SAAS,EAAE,eAAe,CAAC,gBAAgB,CAAC,GAAG;IAC/C,MAAM,EAAE,SAAgB;IACxB,aAAa,EAAE,CAAC;IAChB,eAAe,EAAE,CAAC;CACrB,CAAC;AAEF,MAAM,+BAA+B,GAAG,mCAAmC,CAAC;AAE5E,MAAM,UAAU,GAAG,IAAI,MAAM,EAAE,CAAC;AAqFhC;;;GAGG;AACH,MAAM,OAAO,YAAa,SAAQ,MAAM;IAgLpC;;OAEG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;IACxC,CAAC;IAED,IAAW,qBAAqB,CAAC,IAAY;QACzC,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,IAAI,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,sBAAsB;QACzB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;IACpC,CAAC;IAED;;;OAGG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;IAC3C,CAAC;IAED,IAAW,iBAAiB,CAAC,QAAQ;QACjC,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,QAAQ,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;IACpE,CAAC;IAED,IAAW,oBAAoB,CAAC,OAAgB;QAC5C,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,OAAO,CAAC;SACzC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,2BAA2B;QAClC,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;IAClF,CAAC;IAED,IAAW,2BAA2B,CAAC,OAAgB;QACnD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG,OAAO,CAAC;SAChD;IACL,CAAC;IAED;;OAEG;IACH,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;IAC1E,CAAC;IAED,IAAW,sBAAsB,CAAC,OAAgB;QAC9C,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,OAAO,CAAC;SAC5C;IACL,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,gBAAgB;QAC9B,OAAO,CAAC,SAAS,CAAC,GAAG;YACjB,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;YACxB,CAAC,CAAC,SAAS,CAAC,GAAG;iBACR,cAAc,EAAE;iBAChB,IAAI,CACD,CAAC,OAA+B,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAC9C,GAAG,EAAE,CAAC,KAAK,CACd;iBACA,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,WAAW;QACzB,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAC1D,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,0DAA0D;IAC1D,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,2BAA2B,CAAC;IAC5C,CAAC;IAED,iEAAiE;IACjE,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED,sDAAsD;IACtD,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED,mDAAmD;IACnD,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;QAE7C,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO;YACH,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,gBAAgB;YACpD,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,YAAY,IAAI,kBAAkB;YAC9D,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,iBAAiB;SAC9D,CAAC;IACN,CAAC;IAED;;;;OAIG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED,IAAW,iBAAiB,CAAC,IAAa;QACtC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,IAAW,2BAA2B;QAClC,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;IACvC,CAAC;IAED,IAAW,2BAA2B,CAAC,MAAe;QAClD,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,MAAM,EAAE;YACxC,OAAO;SACV;QACA,IAAI,CAAC,yBAAiC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QACvF,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;IACzC,CAAC;IASD,gBAAgB;IAChB,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC;IACrG,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,WAAW,CAAC,MAAyB,EAAE,UAA+B,EAAE;QAClF,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAEjD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;IACP,CAAC;IAYD;;;;OAIG;IACH,YAAmB,MAA2C,EAAE,UAA+B,EAAE;QAC7F,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;QA9XpD,4CAA4C;QAC5B,aAAQ,GAAG,CAAC,CAAC,CAAC;QAE9B,gCAAgC;QACf,6BAAwB,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;QAC/C,6BAAwB,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;QAChE,gBAAgB;QACA,qBAAgB,GAAG,CAAC,CAAC;QACrC,gBAAgB;QACA,4BAAuB,GAAG,CAAC,CAAC;QAC5C,gBAAgB;QACA,uBAAkB,GAAG,CAAC,CAAC;QACtB,wBAAmB,GAAG,CAAC,CAAC,CAAC,gCAAgC;QAKlE,aAAQ,GAAQ,IAAI,CAAC;QACrB,cAAS,GAA6B,IAAI,CAAC;QAG3C,iBAAY,GAAmB;YACnC,MAAM,EAAE,EAAE;YACV,YAAY,EAAE,EAAE;YAChB,MAAM,EAAE,EAAE;YACV,WAAW,EAAE,EAAE;SAClB,CAAC;QAyBF,gBAAgB;QACT,oBAAe,GAAG,CAAC,CAAC;QAG3B,gBAAgB;QACT,4BAAuB,GAAqC,EAAE,CAAC;QACtE,gBAAgB;QACT,cAAS,GAKZ;YACA,gBAAgB,EAAE,CAAC;YACnB,oBAAoB,EAAE,CAAC;YACvB,8BAA8B,EAAE,CAAC;YACjC,2BAA2B,EAAE,CAAC;SACjC,CAAC;QACF;;WAEG;QACa,sBAAiB,GAK7B;YACA,gBAAgB,EAAE,CAAC;YACnB,oBAAoB,EAAE,CAAC;YACvB,8BAA8B,EAAE,CAAC;YACjC,2BAA2B,EAAE,CAAC;SACjC,CAAC;QACF;;WAEG;QACI,2BAAsB,GAAG,EAAE,CAAC;QAqB3B,oBAAe,GAAuB,CAAC,IAAW,EAAE,IAAW,CAAC,CAAC;QAEzE,kEAAkE;QAClE,gBAAgB;QACT,uBAAkB,GAAmC,IAAI,CAAC;QACzD,2BAAsB,GAA6B;YACvD,oBAAoB,EAAE,IAAI;YAC1B,6BAA6B,EAAE,IAAI;YACnC,6BAA6B,EAAE,IAAI;YACnC,0BAA0B,EAAE,EAAE;YAC9B,kBAAkB,EAAE,SAAS;SAChC,CAAC;QACM,0BAAqB,GAA6B;YACtD,oBAAoB,EAAE,IAAI;YAC1B,6BAA6B,EAAE,IAAI;YACnC,6BAA6B,EAAE,IAAI;YACnC,0BAA0B,EAAE,EAAE;YAC9B,kBAAkB,EAAE,SAAS;SAChC,CAAC;QACF,gBAAgB;QACT,0BAAqB,GAAsC,EAAE,CAAC;QAgB7D,kCAA6B,GAAwD,IAAI,CAAC;QAC1F,wBAAmB,GAAyB,IAAI,CAAC;QACjD,qBAAgB,GAAG,IAAI,CAAC;QACxB,uBAAkB,GAAG,KAAK,CAAC;QAEnC,0DAA0D;QAC1D,gBAAgB;QACT,sBAAiB,GAAG,KAAK,CAAC;QACjC,gBAAgB;QACT,oBAAe,GAAG,IAAI,CAAC;QAC9B,gBAAgB;QACT,iCAA4B,GAAG,KAAK,CAAC;QAC5C,gBAAgB;QACT,4BAAuB,GAAG,EAAE,CAAC;QACpC,gBAAgB;QACT,2BAAsB,GAAG,IAAI,CAAC;QACrC,gBAAgB;QACT,kCAA6B,GAAG,IAAI,CAAC;QA8M5C;;WAEG;QACa,oBAAe,GAAY,IAAI,CAAC;QAEhD;;WAEG;QACa,wBAAmB,GAAY,KAAK,CAAC;QAooBrD,gFAAgF;QAChF,qDAAqD;QACrD,gFAAgF;QAEhF,yDAAyD;QACjD,sBAAiB,GAAmD,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAgE/F,qBAAgB,GAAmD,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAC5F,mBAAc,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QA8D9C,wBAAmB,GAAG,CAAC,CAAC,CAAC;QAkBzB,wBAAmB,GAA4B,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAjxB5E,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;QAEtB,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC;QAC1D,OAAO,CAAC,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,IAAI,KAAK,CAAC;QAEvE,MAAM,CAAC,GAAG,CAAC,eAAe,MAAM,CAAC,OAAO,MAAM,IAAI,CAAC,WAAW,SAAS,CAAC,CAAC;QACzE,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE;YAChB,MAAM,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;YACzD,OAAO;SACV;QAED,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,SAAS,CAAC,GAAG,CAAC,wBAAwB,EAAE,CAAC;QAE9F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;QAEpC,IAAI,CAAC,gBAAgB,GAAG,MAA2B,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7E,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB,GAAG,IAAI,yBAAyB,EAAE,CAAC;QACxD,IAAI,CAAC,oBAAoB,GAAG,IAAI,yBAAyB,EAAE,CAAC;IAChE,CAAC;IAED,gFAAgF;IAChF,8CAA8C;IAC9C,gFAAgF;IAEhF;;;;;OAKG;IACI,SAAS,CAAC,cAA+B,EAAE,YAA2B;QACxE,IAAI,CAAC,QAAmB,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;QACvD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc,IAAI,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC;aACpE,IAAI,CAAC,CAAC,OAAY,EAAE,EAAE;YACnB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;YACxB,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YACrE,OAAO,IAAI,CAAC,SAAS;gBACjB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;oBAC5E,OAAO,SAAS,CAAC,GAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACxD,CAAC,CAAC;gBACJ,CAAC,CAAC,SAAS,CAAC,GAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvD,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,OAA+B,EAAE,EAAE;YACtC,IAAI,CAAC,OAAO,EAAE;gBACV,4CAA4C;gBAC5C,MAAM,wDAAwD,CAAC;aAClE;iBAAM;gBACH,IAAI,CAAC,QAAQ,GAAG,OAAQ,CAAC;gBACzB,IAAI,CAAC,2BAA2B,GAAG,EAAE,CAAC;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,OAAsC,CAAC,CAAC,CAAC;gBAC5H,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAEpD,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;oBACpD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;gBACpC,CAAC,CAAC,CAAC;gBAEH,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,IAAI,EAAE,CAAC;gBAC9D,MAAM,gBAAgB,GAAG,gBAAgB,EAAE,gBAAgB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;gBAEhJ,IAAI,gBAAgB,EAAE;oBAClB,MAAM,mBAAmB,GAAG,gBAAgB,CAAC;oBAC7C,MAAM,eAAe,GAAqB,EAAE,CAAC;oBAE7C,KAAK,MAAM,SAAS,IAAI,mBAAmB,EAAE;wBACzC,IAAI,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;4BAC5D,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBACnC;qBACJ;oBAED,gBAAgB,CAAC,gBAAgB,GAAG,eAAe,CAAC;iBACvD;gBAED,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE;oBACpE,gBAAgB,CAAC,cAAc,GAAG,EAAE,CAAC;oBACrC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,uBAAuB,EAAE;wBAC7C,IAAI,IAAI,KAAK,iBAAiB,IAAI,IAAI,KAAK,iBAAiB,EAAE;4BAC1D,gGAAgG;4BAChG,SAAS;yBACZ;wBACD,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;qBAC9E;iBACJ;gBAED,gBAAgB,CAAC,KAAK,GAAG,sBAAsB,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAE/D,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;aACxD;QACL,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,MAAiB,EAAE,EAAE;YACxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,wBAAwB,GAAG,EAAE,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAsC,CAAC,CAAC,CAAC;YACxH,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC;YAEnC,IAAI,mBAAmB,GAAG,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,KAAK,EAAE,EAAE;gBACvD,IAAI,EAAE,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,EAAE;oBACrD,MAAM,CAAC,IAAI,CAAC,4BAA4B,mBAAmB,GAAG,CAAC,MAAgC,KAAM,CAAC,KAAK,MAAY,KAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;iBAClJ;qBAAM,IAAI,mBAAmB,EAAE,KAAK,IAAI,CAAC,sBAAsB,EAAE;oBAC9D,MAAM,CAAC,IAAI,CACP,+CAA+C,IAAI,CAAC,sBAAsB,sEAAsE,CACnJ,CAAC;iBACL;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC7B,IAAI,IAAI,CAAC,WAAW,EAAE;wBAClB,OAAO;qBACV;oBACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC5B,MAAM,CAAC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,CAAC;oBAC5C,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oBACnD,IAAI,CAAC,8BAA8B,CAAC,KAAK,IAAI,EAAE;wBAC3C,MAAM,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;wBACzD,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;wBACjD,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;wBACvD,MAAM,2BAA2B,GAAG,IAAI,CAAC,2BAA2B,CAAC;wBACrE,MAAM,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;wBAC3D,MAAM,2BAA2B,GAAG,IAAI,CAAC,2BAA2B,CAAC;wBAErE,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,QAAQ,EAAE,cAAc,EAAE,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;wBAE/H,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;wBACnD,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;wBAC3C,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;wBACjD,IAAI,CAAC,2BAA2B,GAAG,2BAA2B,CAAC;wBAC/D,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;wBACrD,IAAI,CAAC,2BAA2B,GAAG,2BAA2B,CAAC;wBAC/D,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;oBACnC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;aACN;QACL,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,EAAE;YACP,IAAI,CAAC,cAAc,GAAG,IAAI,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAClE,IAAI,CAAC,cAAc,GAAG,IAAI,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACtJ,IAAI,CAAC,aAAa,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1D,IAAI,CAAC,gBAAgB,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YAC1F,IAAI,CAAC,eAAe,GAAG,IAAI,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACzF,IAAI,CAAC,eAAe,GAAI,IAAI,CAAC,OAAe,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAE,SAAiB,CAAC;YACrJ,IAAI,CAAC,WAAW,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtD,IAAI,CAAC,kBAAkB,GAAG,IAAI,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAE3G,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAC9C,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACzB,eAAe,CAAC,WAAW,CAAC,OAAO,GAAG,eAAe,CAAC,WAAW,CAAC,OAAO,EACzE,WAAW,CACd,CAAC;YACF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAClD,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACxB,eAAe,CAAC,WAAW,CAAC,OAAO,GAAG,eAAe,CAAC,WAAW,CAAC,OAAO,EACzE,eAAe,CAClB,CAAC;YAEF,IAAI,IAAI,CAAC,4BAA4B,EAAE;gBACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;oBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;oBACzB,MAAM,CAAC,GAAG,CAAC,CAAC,YAAY,GAAI,IAAY,CAAC,MAAM,GAAG,UAAU,EAAE,qBAAqB,CAAC,CAAC,CAAC;iBACzF;aACJ;YAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACvF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAEvF,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,IAAI,CAAC,kBAAkB,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE;gBACtD,MAAM,EAAE,CAAC;gBACT,MAAM,EAAE,CAAC;gBACT,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,mBAAmB;aAC7B,CAAC,CAAC;YAEH,IAAI,CAAC,oBAAoB,GAAG,IAAI,6BAA6B,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAErG,IAAI,CAAC,kBAAkB,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACjF,IAAI,CAAC,qBAAqB,GAAG,IAAI,0BAA0B,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACvF,IAAI,CAAC,qBAAqB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;YAE9D,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;YACzC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC;YACrD,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;YAEzC,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAE3D,IAAI,CAAC,UAAU,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACnF,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,EAAG,CAAC;YACrD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACpD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,qBAAqB,EAAG,CAAC;YAC7D,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;YAE5D,IAAI,CAAC,8BAA8B,EAAE,CAAC;YACtC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,+DAA+D,CAAC,CAAC;YAC9E,MAAM,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC;IACX,CAAC;IAEO,YAAY,CAAC,cAA+B;QAChD,cAAc,GAAG,cAAc,IAAI,EAAE,CAAC;QACtC,cAAc,GAAG;YACb,GAAG,YAAY,CAAC,wBAAwB;YACxC,GAAG,cAAc;SACpB,CAAC;QAEF,IAAI,cAAc,CAAC,OAAO,EAAE;YACxB,OAAO,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;SAClD;QAED,IAAK,IAAY,CAAC,OAAO,EAAE;YACvB,OAAQ,IAAY,CAAC,OAAO,CAAC,cAAe,CAAC,QAAQ,CAAC,CAAC;SAC1D;QAED,IAAI,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,QAAQ,EAAE;YAClD,OAAO,KAAK,CAAC,sBAAsB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBACjE,OAAQ,IAAY,CAAC,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,cAAe,CAAC,QAAS,CAAC,CAAC,CAAC;YACvF,CAAC,CAAC,CAAC;SACN;QAED,OAAO,OAAO,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;IACtD,CAAC;IAEO,iBAAiB;QACrB,YAAY;QACZ,iEAAiE;QAEjE,IAAI,CAAC,KAAK,GAAG;YACT,qBAAqB,EAAE,IAAI,CAAC,aAAa,CAAC,gCAAgC;YAC1E,0BAA0B,EAAE,IAAI,CAAC,aAAa,CAAC,gCAAgC;YAC/E,6BAA6B,EAAE,IAAI,CAAC,aAAa,CAAC,gCAAgC,GAAG,CAAC;YACtF,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,qBAAqB;YACxD,qBAAqB,EAAE,IAAI,CAAC,aAAa,CAAC,qBAAqB;YAC/D,oBAAoB,EAAE,IAAI,CAAC,aAAa,CAAC,qBAAqB;YAC9D,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,mBAAmB;YACxD,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,4BAA4B;YAClE,yBAAyB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,2BAA2B,GAAG,CAAC,CAAC;YACzF,uBAAuB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,2BAA2B,GAAG,CAAC,CAAC;YACvF,mBAAmB,EAAE,IAAI;YACzB,IAAI,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAQ;YAChI,IAAI,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAQ;YAC9H,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAQ;YAChI,IAAI,EAAE,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YACrH,aAAa,EAAE,EAAE;YACjB,WAAW,EAAE,IAAI;YACjB,sBAAsB,EAAE,IAAI;YAC5B,4BAA4B,EAAE,IAAI;YAClC,gBAAgB,EAAE,IAAI;YACtB,2BAA2B,EAAE,KAAK;YAClC,wBAAwB,EAAE,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,uBAAuB,CAAC,IAAI,CAAC;YACzH,YAAY,EAAE,IAAI;YAClB,2BAA2B,EAAE,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC;YACtH,kBAAkB,EAAE,IAAI;YACxB,gBAAgB,EAAE,IAAI;YACtB,+BAA+B,EAAE,IAAI;YACrC,sBAAsB,EAAE,IAAI;YAC5B,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,IAAI;YAChB,oBAAoB,EAAE,IAAI;YAC1B,qBAAqB,EAAE,IAAI;YAC3B,iBAAiB,EAAE,KAAK;YACxB,eAAe,EAAE,IAAI;YACrB,UAAU,EACN,OAAO,cAAc,KAAK,WAAW,IAAI,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAE,IAAY,CAAC,CAAC,CAAC,SAAS;YACjK,qBAAqB,EAAE,OAAO,cAAc,KAAK,WAAW;YAC5D,4BAA4B,EAAE,IAAI;YAClC,SAAS,EAAE,KAAK;YAChB,eAAe,EAAE,KAAK;YACtB,qBAAqB,EAAE,SAAS;YAChC,WAAW,EAAE,IAAI;YACjB,cAAc,EAAE,CAAC;YACjB,kBAAkB,EAAE,IAAI;YACxB,gBAAgB,EAAE,IAAI;YACtB,qBAAqB,EAAE,IAAI;YAC3B,kBAAkB,EAAE,IAAI;YACxB,yBAAyB,EAAE,KAAK;YAChC,eAAe,EAAE,IAAI;YACrB,2BAA2B,EAAE,IAAI,CAAC,aAAa,CAAC,qBAAqB;YACrE,yBAAyB,EAAE,KAAK;SACnC,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG;YACb,+BAA+B,EAAE,IAAI;YACrC,yCAAyC,EAAE,IAAI;YAC/C,0BAA0B,EAAE,IAAI;YAChC,qBAAqB,EAAE,IAAI;YAC3B,4BAA4B,EAAE,KAAK;YACnC,wBAAwB,EAAE,IAAI;YAC9B,gBAAgB,EAAE,IAAI;YACtB,4BAA4B,EAAE,IAAI;YAClC,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,KAAK;YACpB,iBAAiB,EAAE,IAAI;YACvB,+BAA+B,EAAE,IAAI;YACrC,WAAW,EAAE,IAAI;YACjB,YAAY,EAAE,IAAI;YAClB,6BAA6B,EAAE,IAAI;YACnC,yBAAyB,EAAE,IAAI;YAC/B,sBAAsB,EAAE,KAAK;YAC7B,oBAAoB,EAAE,KAAK;YAC3B,kBAAkB,EAAE,KAAK;YACzB,sBAAsB,EAAE,IAAI;YAC5B,8BAA8B,EAAE,IAAI;YACpC,mBAAmB,EAAE,IAAI;YACzB,uBAAuB,EAAE,IAAI;YAC7B,8CAA8C,EAAE,IAAI;YACpD,0BAA0B,EAAE,KAAK;SACpC,CAAC;IACN,CAAC;IAEO,8BAA8B;QAClC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,4CAA4C;YAC5C,MAAM,wCAAwC,CAAC;SAClD;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,QAAQ,CAAgC,CAAC;QAC1F,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAgB,CAAC;QACnD,IAAI,CAAC,sBAAsB,CAAC,0BAA0B,GAAG,CAAC,IAAI,qBAAqB,EAAE,CAAC,CAAC;QACvF,IAAI,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,CAAC,CAAE,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;QACtF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IACtD,CAAC;IAED,yFAAyF;IACjF,0BAA0B;QAC9B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,IAAI,CAAC,mBAAmB,GAAG;YACvB,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YAChC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAClC,kBAAkB,EAAE,CAAC;SACxB,CAAC;QAEF,MAAM,gBAAgB,GAAG,IAAI,YAAY,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAExE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC;QACrE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC;QAEzE,IAAI,oBAAoD,CAAC;QAEzD,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YACzB,MAAM,qBAAqB,GAAyB;gBAChD,KAAK,EAAE,qBAAqB,IAAI,CAAC,mBAAmB,CAAC,KAAK,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,eAAe;gBAC5G,IAAI,EAAE,IAAI,CAAC,mBAAmB;gBAC9B,aAAa,EAAE,CAAC;gBAChB,WAAW,EAAE,IAAI,CAAC,oBAAoB;gBACtC,SAAS,EAAE,eAAe,CAAC,gBAAgB,CAAC,GAAG;gBAC/C,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAgB;gBACtC,KAAK,EAAE,eAAe,CAAC,YAAY,CAAC,gBAAgB;aACvD,CAAC;YAEF,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aACzD;YACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YACtE,oBAAoB,GAAG;gBACnB;oBACI,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;wBAC/B,KAAK,EAAE,oCAAoC;wBAC3C,SAAS,EAAE,eAAe,CAAC,gBAAgB,CAAC,GAAG;wBAC/C,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAgB;wBACtC,aAAa,EAAE,CAAC;wBAChB,eAAe,EAAE,CAAC;qBACrB,CAAC;oBACF,UAAU,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBAClC,MAAM,EAAE,eAAe,CAAC,MAAM,CAAC,KAAK;oBACpC,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,oMAAoM;iBAC/O;aACJ,CAAC;SACL;aAAM;YACH,oBAAoB,GAAG;gBACnB;oBACI,IAAI,EAAE,SAAgB;oBACtB,UAAU,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBAClC,MAAM,EAAE,eAAe,CAAC,MAAM,CAAC,KAAK;oBACpC,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,KAAK;iBACzC;aACJ,CAAC;SACL;QAED,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;QAEvK,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACzD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAElD,MAAM,sBAAsB,GAAyB;YACjD,KAAK,EAAE,4BAA4B,IAAI,CAAC,mBAAmB,CAAC,KAAK,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE;YACtG,IAAI,EAAE,IAAI,CAAC,mBAAmB;YAC9B,aAAa,EAAE,CAAC;YAChB,WAAW,EAAE,IAAI,CAAC,oBAAoB;YACtC,SAAS,EAAE,eAAe,CAAC,gBAAgB,CAAC,GAAG;YAC/C,MAAM,EAAE,IAAI,CAAC,sBAAsB,CAAC,kBAAkB;YACtD,KAAK,EAAE,eAAe,CAAC,YAAY,CAAC,gBAAgB;SACvD,CAAC;QAEF,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAC1D;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;QACxE,MAAM,mBAAmB,GAAwC;YAC7D,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;gBAChC,KAAK,EAAE,gCAAgC,IAAI,CAAC,mBAAmB,CAAC,KAAK,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE;gBAC1G,SAAS,EAAE,eAAe,CAAC,gBAAgB,CAAC,GAAG;gBAC/C,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM;gBACjC,aAAa,EAAE,CAAC;gBAChB,eAAe,EAAE,CAAC;aACrB,CAAC;YAEF,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,WAAW,EAAE,eAAe,CAAC,MAAM,CAAC,KAAK;YACzC,YAAY,EAAE,eAAe,CAAC,OAAO,CAAC,KAAK;YAC3C,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,aAAa,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK;YAC/E,cAAc,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK;SACpF,CAAC;QAEF,IAAI,CAAC,sBAAsB,CAAC,oBAAoB,GAAG;YAC/C,KAAK,EAAE,gBAAgB;YACvB,gBAAgB,EAAE,oBAAoB;YACtC,sBAAsB,EAAE,mBAAmB;SAC9C,CAAC;IACN,CAAC;IAEO,iBAAiB;QACrB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YACpB,MAAM,EAAE,IAAI,CAAC,OAAO;YACpB,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAgB;YACtC,KAAK,EAAE,eAAe,CAAC,YAAY,CAAC,gBAAgB,GAAG,eAAe,CAAC,YAAY,CAAC,OAAO;YAC3F,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM;SAC9H,CAAC,CAAC;IACP,CAAC;IAES,eAAe;QACrB,KAAK,CAAC,eAAe,EAAE,CAAC;QAExB,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,eAAe,EAAE;YAC9C,iJAAiJ;YACjJ,IAAK,aAAa,CAAC,SAAS,EAAuB,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAC5E,aAAa,CAAC,QAAQ,EAAE,CAAC;aAC5B;SACJ;IACL,CAAC;IAES,8BAA8B,CAAC,UAAsB;QAC3D,6BAA6B,CAAC,UAAU,EAAE,CAAC;QAC3C,qBAAqB,CAAC,UAAU,EAAE,CAAC;QAEnC,gDAAgD;QAChD,MAAM,WAAW,GAAG,CAAC,MAAe,EAAE,EAAE;YACpC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;gBACxB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE;oBAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;oBACjC,IAAI,CAAC,SAAS,EAAE;wBACZ,SAAS;qBACZ;oBACD,KAAK,MAAM,OAAO,IAAI,SAAS,EAAE;wBAC7B,OAAO,CAAC,aAAa,GAAG,EAAE,CAAC;qBAC9B;iBACJ;gBAED,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,SAAS,EAAE;oBACpC,QAAQ,CAAC,gBAAgB,EAAE,KAAK,EAAE,CAAC;iBACtC;aACJ;QACL,CAAC,CAAC;QAEF,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzB,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEjC,oHAAoH;QACpH,MAAM,OAAO,GAAoB,EAAE,CAAC;QACpC,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,eAAe,EAAE;YAC9C,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBAC5C,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aAC/B;SACJ;QACD,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;QAE/B,KAAK,CAAC,8BAA8B,CAAC,UAAU,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;OAMG;IACI,OAAO,CAAC,KAAa,EAAE,MAAc,EAAE,YAAY,GAAG,KAAK;QAC9D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE;YAC7C,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;gBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;aAC5B;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;gBAC9E,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,cAAc,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;aAClF;SACJ;QAED,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,oFAAoF;YACpF,IAAI,CAAC,sBAAsB,EAAE,CAAC;SACjC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAID;;OAEG;IACI,mBAAmB,CAAC,cAA8B;QACrD,IAAI,cAAc,KAAK,cAAc,CAAC,IAAI,EAAE;YACxC,OAAO,IAAI,CAAC,oBAAoB,CAAC;SACpC;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,cAA8B;QAC7D,OAAO,IAAI,6BAA6B,CAAC,cAAc,CAAC,CAAC;IAC7D,CAAC;IAEO,sBAAsB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,KAAK,IAAI,CAAC;IAC9C,CAAC;IAEO,qBAAqB;QACzB,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACvD,+GAA+G;YAC/G,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,oBAAoB,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SAC3F;aAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACjC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;SACpC;QAED,OAAO,IAAI,CAAC,kBAAmB,CAAC;IACpC,CAAC;IAED,gBAAgB;IACT,4BAA4B;QAC/B,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC;IAChG,CAAC;IAED,gFAAgF;IAChF,yDAAyD;IACzD,gFAAgF;IAEhF,gBAAgB;IACT,WAAW;QACd,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAChF,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,UAAoB;QAClC,IAAI,IAAI,CAAC,6BAA6B,IAAI,CAAC,UAAU,EAAE;YACnD,OAAO;SACV;QAED,gPAAgP;QAChP,sEAAsE;QACtE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;QAC1C,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAEvD,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;YAEnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC;YAErD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC;YACtC,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,aAAa,CAAC;YAC9C,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAAC;YACrI,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAEtD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAC5B;QAED,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,MAAe;QAChC,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;QAC/B,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IASO,mBAAmB;QACvB,MAAM,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAC5B,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAC1B,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAC1B,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;QAE/B,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,CAAC;QAEpJ,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;SACrD;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,cAAc,CAAC,UAAsC;QACzD,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAE7C,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAE3C,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAC1C;QAED,IAAI,UAAU,EAAE;YACZ,UAAU,CAAC,OAAO,CAAC,IAAI,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAChE;aAAM;YACH,IAAI,CAAC,qBAAqB,EAAE,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC9D;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;gBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;aAC5B;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;gBAC9E,MAAM,CAAC,GAAG,CAAC;oBACP,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,yBAAyB;oBAC5D,IAAI,CAAC,eAAe,CAAC,CAAC;oBACtB,IAAI,CAAC,eAAe,CAAC,CAAC;oBACtB,IAAI,CAAC,eAAe,CAAC,CAAC;oBACtB,IAAI,CAAC,eAAe,CAAC,CAAC;oBACtB,8BAA8B,GAAG,IAAI,CAAC,sBAAsB,EAAE;iBACjE,CAAC,CAAC;aACN;SACJ;IACL,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc;QAChE,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,MAAM,CAAC;IACpC,CAAC;IAKO,kBAAkB;QACtB,MAAM,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,EAC3B,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,EACzB,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,EACzB,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAE9B,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,CAAC;QAEhJ,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;SACnD;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,aAAa,CAAC,UAAsC;QACxD,MAAM,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAErI,IAAI,UAAU,EAAE;YACZ,UAAU,CAAC,OAAO,CAAC,IAAI,uBAAuB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;SAC3H;aAAM;YACH,IAAI,CAAC,qBAAqB,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;SACvH;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;gBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;aAC5B;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;gBAC9E,MAAM,CAAC,GAAG,CAAC;oBACP,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,wBAAwB;oBAC3D,IAAI,CAAC,cAAc,CAAC,CAAC;oBACrB,IAAI,CAAC,cAAc,CAAC,CAAC;oBACrB,IAAI,CAAC,cAAc,CAAC,CAAC;oBACrB,IAAI,CAAC,cAAc,CAAC,CAAC;oBACrB,8BAA8B,GAAG,IAAI,CAAC,sBAAsB,EAAE;iBACjE,CAAC,CAAC;aACN;SACJ;IACL,CAAC;IAEO,gBAAgB;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,CAAC;IACpI,CAAC;IAEM,aAAa,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc;QACpE,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,MAAM,CAAC;IACnC,CAAC;IAEM,cAAc;QACjB,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;QAClG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9G,CAAC;IAIO,qBAAqB;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,KAAK,IAAI,CAAC,mBAAmB,CAAC;QAC/E,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;SACjE;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,gBAAgB,CAAC,UAAsC;QAC3D,IAAI,UAAU,EAAE;YACZ,UAAU,CAAC,OAAO,CAAC,IAAI,0BAA0B,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;SAC/F;aAAM;YACH,IAAI,CAAC,qBAAqB,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;SAC7F;IACL,CAAC;IAIO,qBAAqB;QACzB,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;QAEpD,MAAM,MAAM,GACR,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;YAC7C,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;YAC7C,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;YAC7C,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAElD,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;SAC/C;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,gBAAgB,CAAC,UAAsC;QAC3D,IAAI,UAAU,EAAE;YACZ,UAAU,CAAC,OAAO,CAAC,IAAI,0BAA0B,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAChG;aAAM;YACH,IAAI,CAAC,qBAAqB,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,eAA2B,CAAC,CAAC;SAC/F;IACL,CAAC;IAEO,sBAAsB;QAC1B,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC;QAC9G,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1G,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACjI,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,KAA4B,EAAE,UAAmB,EAAE,KAAc,EAAE,UAAmB,KAAK;QACpG,+BAA+B;QAC/B,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE;YAChC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;SACf;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE3C,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;gBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;aAC5B;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;gBAC9E,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,wBAAwB,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,qBAAqB,EAAE,UAAU,CAAC,CAAC,CAAC;aACpK;SACJ;QAED,0HAA0H;QAC1H,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,UAAU,EAAE;gBACZ,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;oBAC1B,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,oBAAqB,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;iBACnH;gBACD,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACtE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;aAClE;iBAAM;gBACH,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBACzB,IAAI,CAAC,qBAAqB,EAAE,CAAC;iBAChC;gBACD,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,oBAAqB,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;aAClH;SACJ;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,UAAU,EAAE;gBACzC,IAAI,CAAC,oBAAoB,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;aACrF;YACD,IAAI,UAAU,EAAE;gBACZ,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACtE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;aAClE;SACJ;IACL,CAAC;IAEO,cAAc,CAAC,UAAkC,EAAE,UAAoB,EAAE,YAAsB;QACnG,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAEjF,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAChE,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAC7B,IAAI,CAAC,oBAAoB,CAAC,cAAc,IAAI,EAAE,EAC9C,IAAI,CAAC,oBAAoB,CAAC,eAAe,IAAI,EAAE,EAC/C,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAC5C,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,0BAA0B,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;SACrF;aAAM;YACH,UAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC5D;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAEhH,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAO,CAAC,CAAC;YACpC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxC,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;aAAM;YACH,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;SAC/B;IACL,CAAC;IAED,gFAAgF;IAChF,4DAA4D;IAC5D,gFAAgF;IAEhF;;;;;;OAMG;IACI,kBAAkB,CAAC,IAAwB,EAAE,UAAoB,EAAE,KAAc;QACpF,IAAI,IAA8B,CAAC;QAEnC,IAAI,IAAI,YAAY,KAAK,EAAE;YACvB,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;SACjC;aAAM,IAAI,IAAI,YAAY,WAAW,EAAE;YACpC,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;SAC/B;aAAM;YACH,IAAI,GAAG,IAAI,CAAC;SACf;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,eAAe,CAAC,WAAW,CAAC,MAAM,GAAG,eAAe,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC3I,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACI,yBAAyB,CAAC,IAAe,EAAE,KAAc;QAC5D,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;OAMG;IACI,iBAAiB,CAAC,OAAqB,EAAE,UAAoB,EAAE,KAAc;QAChF,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,IAAI,IAAqB,CAAC;QAE1B,IAAI,OAAO,YAAY,WAAW,IAAI,OAAO,YAAY,UAAU,EAAE;YACjE,IAAI,GAAG,OAAO,CAAC;SAClB;aAAM,IAAI,OAAO,YAAY,WAAW,EAAE;YACvC,IAAI,GAAG,OAAO,CAAC;YACf,QAAQ,GAAG,KAAK,CAAC;SACpB;aAAM;YACH,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,EAAE;gBACxB,IAAI,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;aACnC;iBAAM;gBACH,IAAI,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;gBAChC,QAAQ,GAAG,KAAK,CAAC;aACpB;SACJ;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,eAAe,CAAC,WAAW,CAAC,KAAK,GAAG,eAAe,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC1I,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC/B,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACI,wBAAwB,CAAC,WAAuB,EAAE,OAAqB,EAAE,SAAiB,CAAC;QAC9F,MAAM,SAAS,GAAG,WAA+B,CAAC;QAElD,IAAI,IAAqB,CAAC;QAC1B,IAAI,WAAW,CAAC,QAAQ,EAAE;YACtB,IAAI,GAAG,OAAO,YAAY,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;SAC9E;aAAM;YACH,IAAI,GAAG,OAAO,YAAY,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;SAC9E;QAED,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;OAMG;IACI,yBAAyB,CAAC,YAAwB,EAAE,IAAe,EAAE,UAAmB,EAAE,UAAmB;QAChH,MAAM,UAAU,GAAG,YAAgC,CAAC;QACpD,IAAI,UAAU,KAAK,SAAS,EAAE;YAC1B,UAAU,GAAG,CAAC,CAAC;SAClB;QAED,IAAI,IAAqB,CAAC;QAC1B,IAAI,UAAU,KAAK,SAAS,EAAE;YAC1B,IAAI,IAAI,YAAY,KAAK,EAAE;gBACvB,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;aACjC;iBAAM,IAAI,IAAI,YAAY,WAAW,EAAE;gBACpC,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;aAC/B;iBAAM;gBACH,IAAI,GAAG,IAAI,CAAC;aACf;YACD,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;SAChC;aAAM;YACH,IAAI,IAAI,YAAY,KAAK,EAAE;gBACvB,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;aACjC;iBAAM,IAAI,IAAI,YAAY,WAAW,EAAE;gBACpC,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;aAC/B;iBAAM;gBACH,IAAI,GAAG,IAAI,CAAC;aACf;SACJ;QAED,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,IAAwB,EAAE,aAAqB,EAAE,KAAc;QAChF,IAAI,IAA8B,CAAC;QAEnC,IAAI,IAAI,YAAY,KAAK,EAAE;YACvB,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;SACjC;aAAM,IAAI,IAAI,YAAY,WAAW,EAAE;YACpC,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;SAC/B;aAAM;YACH,IAAI,GAAG,IAAI,CAAC;SACf;QAED,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,aAAa,GAAG,SAAS,CAAC,wBAAwB,EAAE;YACpD,KAAK,IAAI,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC;SAChD;QACD,IAAI,aAAa,GAAG,SAAS,CAAC,yBAAyB,EAAE;YACrD,KAAK,IAAI,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC;SAChD;QACD,IAAI,aAAa,GAAG,SAAS,CAAC,2BAA2B,EAAE;YACvD,KAAK,IAAI,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC;SAChD;QACD,IAAI,aAAa,GAAG,SAAS,CAAC,0BAA0B,EAAE;YACtD,KAAK,IAAI,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC;SAC/C;QACD,IAAI,aAAa,GAAG,SAAS,CAAC,yBAAyB,EAAE;YACrD,KAAK,IAAI,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC;SAC9C;QACD,IAAI,aAAa,GAAG,SAAS,CAAC,2BAA2B,EAAE;YACvD,KAAK,IAAI,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC;SAChD;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,4CAA4C;QAC5C,MAAM,2BAA2B,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,4BAA4B;QAC/B,4CAA4C;QAC5C,MAAM,2BAA2B,CAAC;IACtC,CAAC;IAED;;;;;;OAMG;IACI,WAAW,CACd,aAAwD,EACxD,WAAiC,EACjC,MAAc,EACd,qBAAkE;QAElE,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC;QACvC,IAAI,CAAC,6BAA6B,GAAG,qBAAqB,IAAI,IAAI,CAAC;QACnE,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAC;IACzG,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,MAAkB;QACpC,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAED,gFAAgF;IAChF,+CAA+C;IAC/C,gFAAgF;IAEhF;;;;;;OAMG;IACI,mBAAmB,CAAC,QAAoB,EAAE,KAAc;QAC3D,IAAI,IAAkB,CAAC;QACvB,IAAI,QAAQ,YAAY,KAAK,EAAE;YAC3B,IAAI,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;SACrC;aAAM;YACH,IAAI,GAAG,QAAQ,CAAC;SACnB;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,eAAe,CAAC,WAAW,CAAC,OAAO,GAAG,eAAe,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC5I,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;;OAMG;IACI,0BAA0B,CAAC,QAAoB,EAAE,KAAc;QAClE,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;;OAOG;IACI,mBAAmB,CAAC,aAAyB,EAAE,QAAoB,EAAE,MAAe,EAAE,KAAc;QACvG,IAAI,MAAM,KAAK,SAAS,EAAE;YACtB,MAAM,GAAG,CAAC,CAAC;SACd;QAED,MAAM,UAAU,GAAG,aAAiC,CAAC;QACrD,IAAI,IAAkB,CAAC;QACvB,IAAI,KAAK,KAAK,SAAS,EAAE;YACrB,IAAI,QAAQ,YAAY,YAAY,EAAE;gBAClC,IAAI,GAAG,QAAQ,CAAC;aACnB;iBAAM;gBACH,IAAI,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;aACrC;YACD,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;SAC3B;aAAM;YACH,IAAI,QAAQ,YAAY,YAAY,EAAE;gBAClC,IAAI,GAAG,QAAQ,CAAC;aACnB;iBAAM;gBACH,IAAI,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;aACrC;SACJ;QAED,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACvE,CAAC;IAED;;;;;OAKG;IACI,qBAAqB,CAAC,MAAkB,EAAE,SAAiB,EAAE,IAAY;QAC5E,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,MAA0B,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACI,gBAAgB,KAAU,CAAC;IAElC,gFAAgF;IAChF,uCAAuC;IACvC,gFAAgF;IAEhF;;;;;;;;;;;;;OAaG;IACI,YAAY,CACf,QAAa,EACb,wBAA2D,EAC3D,qBAAwC,EACxC,QAAmB,EACnB,OAAgB,EAChB,SAA2B,EAC3B,UAA+C,EAC/C,OAA4D,EAC5D,eAAqB,EACrB,cAAc,GAAG,cAAc,CAAC,IAAI;QAEpC,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC;QACtH,MAAM,QAAQ,GAAG,QAAQ,CAAC,eAAe,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC;QAChI,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAG,CAAC;QAEhD,IAAI,WAAW,GAAG,OAAO,IAA6B,wBAAyB,CAAC,OAAO,IAAI,EAAE,CAAC;QAE9F,IAAI,aAAa,EAAE;YACf,WAAW,IAAI,IAAI,GAAG,aAAa,CAAC;SACvC;QAED,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,GAAG,QAAQ,GAAG,GAAG,GAAG,WAAW,CAAC;QACzD,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC7B,MAAM,cAAc,GAAW,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC3D,IAAI,UAAU,IAAI,cAAc,CAAC,OAAO,EAAE,EAAE;gBACxC,UAAU,CAAC,cAAc,CAAC,CAAC;aAC9B;YAED,OAAO,cAAc,CAAC;SACzB;QACD,MAAM,MAAM,GAAG,IAAI,MAAM,CACrB,QAAQ,EACR,wBAAwB,EACxB,qBAAqB,EACrB,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,eAAe,EACf,IAAI,EACJ,cAAc,CACjB,CAAC;QACF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;QAErC,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,wBAAwB,CAAC,MAAc,EAAE,IAAY;QACzD,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;IAEO,qBAAqB,CAAC,MAAc,EAAE,IAAY,EAAE,OAAyB,EAAE,aAAqB;QACxG,OAAO,IAAI,CAAC,wBAAwB,CAAC,aAAa,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,IAAI,CAAC,CAAC;IACzG,CAAC;IAEO,cAAc,CAAC,MAAc,EAAE,IAAY,EAAE,OAAyB;QAC1E,IAAI,OAAO,EAAE;YACT,OAAO,GAAG,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;SAC5D;aAAM;YACH,OAAO,GAAG,EAAE,CAAC;SAChB;QACD,OAAO,OAAO,GAAG,MAAM,CAAC;IAC5B,CAAC;IAEO,8BAA8B,CAClC,YAAkC,EAClC,cAAoC,EACpC,cAA8B,EAC9B,iCAA0C,EAC1C,mCAA4C;QAE5C,IAAI,IAAI,CAAC,SAAS,IAAI,cAAc,KAAK,cAAc,CAAC,IAAI,EAAE;YAC1D,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,YAA2B,EAAE,iCAAiC,CAAC,CAAC;YAChH,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,cAA6B,EAAE,mCAAmC,CAAC,CAAC;SACzH;QAED,OAAO;YACH,WAAW,EAAE;gBACT,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;oBACpC,IAAI,EAAE,YAAY;iBACrB,CAAC;gBACF,UAAU,EAAE,MAAM;aACrB;YACD,aAAa,EAAE;gBACX,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;oBACpC,IAAI,EAAE,cAAc;iBACvB,CAAC;gBACF,UAAU,EAAE,MAAM;aACrB;SACJ,CAAC;IACN,CAAC;IAEO,kCAAkC,CAAC,UAAkB,EAAE,YAAoB,EAAE,cAA8B;QAC/G,MAAM,iCAAiC,GAAG,UAAU,CAAC,OAAO,CAAC,+BAA+B,CAAC,IAAI,CAAC,CAAC;QACnG,MAAM,mCAAmC,GAAG,YAAY,CAAC,OAAO,CAAC,+BAA+B,CAAC,IAAI,CAAC,CAAC;QAEvG,MAAM,YAAY,GAAG,cAAc,KAAK,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QAC/H,MAAM,cAAc,GAAG,cAAc,KAAK,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;QAEvI,OAAO,IAAI,CAAC,8BAA8B,CAAC,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,iCAAiC,EAAE,mCAAmC,CAAC,CAAC;IACrK,CAAC;IAEO,+BAA+B,CACnC,UAAkB,EAClB,YAAoB,EACpB,OAAyB,EACzB,cAA8B;QAE9B,IAAI,CAAC,mCAAmC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE/D,MAAM,iCAAiC,GAAG,UAAU,CAAC,OAAO,CAAC,+BAA+B,CAAC,IAAI,CAAC,CAAC;QACnG,MAAM,mCAAmC,GAAG,YAAY,CAAC,OAAO,CAAC,+BAA+B,CAAC,IAAI,CAAC,CAAC;QAEvG,MAAM,aAAa,GAAG,gBAAgB,CAAC;QACvC,MAAM,YAAY,GACd,cAAc,KAAK,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC3K,MAAM,cAAc,GAChB,cAAc,KAAK,cAAc,CAAC,IAAI;YAClC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,CAAC;YAC9E,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAEjE,MAAM,OAAO,GAAG,IAAI,CAAC,8BAA8B,CAAC,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,iCAAiC,EAAE,mCAAmC,CAAC,CAAC;QAE1K,IAAI,CAAC,kCAAkC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE9D,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,sBAAsB;QACzB,4CAA4C;QAC5C,MAAM,yBAAyB,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,4CAA4C;QAC5C,MAAM,yBAAyB,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,IAAY;QAChC,MAAM,GAAG,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACxC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;QAClB,GAAG,CAAC,WAAW,EAAE,CAAC;QAClB,OAAO,GAAG,CAAC,IAAI,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,uBAA0D;QACnF,OAAO,IAAI,qBAAqB,CAAC,uBAAyD,EAAE,IAAI,CAAC,CAAC;IACtG,CAAC;IAED;;;OAGG;IACI,qBAAqB;QACxB,OAAO,IAAI,qBAAqB,EAAE,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,uBAAuB,CAC1B,eAAiC,EACjC,gBAAwB,EACxB,kBAA0B,EAC1B,WAAoB,EACpB,mBAA2B,EAC3B,qBAA6B,EAC7B,aAAkB,EAClB,OAAyB;QAEzB,MAAM,aAAa,GAAG,eAAwC,CAAC;QAC/D,MAAM,cAAc,GAAG,aAAa,CAAC,uBAAuB,CAAC,cAAc,CAAC;QAE5E,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC7B,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAC/B,MAAM,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;SACjE;QAED,aAAa,CAAC,OAAO,GAAG;YACpB,QAAQ,EAAE,kBAAkB;YAC5B,MAAM,EAAE,gBAAgB;YACxB,SAAS,EAAE,mBAAmB;YAC9B,WAAW,EAAE,qBAAqB;SACrC,CAAC;QAEF,IAAI,WAAW,EAAE;YACb,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,kCAAkC,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,cAAc,CAAC,CAAC;SACxH;aAAM;YACH,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,+BAA+B,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;SAC9H;IACL,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,eAAiC,EAAE,eAAyB;QAC7E,MAAM,OAAO,GAAG,IAAI,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,kBAAkB,GAAG,eAAwC,CAAC;QAEpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;YACxG,IAAI,iBAAiB,KAAK,SAAS,EAAE;gBACjC,SAAS;aACZ;YAED,OAAO,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC;SAClC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,MAAsC;QACtD,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QAED,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YAChC,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;YAC7B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;YAC5D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACpD,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,MAAM,CAAC,IAAI,CACP,kFAAkF,MAAM,CAAC,QAAQ,iBAAiB,MAAM,CAAC,IAAI,wBAAwB,MAAM,CAAC,IAAI,CAAC,MAAM,0BAA0B,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EACvN,EAAE,CACL,CAAC;aACL;SACJ;aAAM,IACH,CAAC,MAAM,CAAC,MAAM;YACd,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc;gBAClC,MAAM,CAAC,eAAe,KAAK,IAAI,CAAC,uBAAuB;gBACvD,MAAM,CAAC,WAAW,KAAK,IAAI,CAAC,mBAAmB;gBAC/C,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAC/B;YACE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,6BAA6B,EAAE;gBACtD,MAAM,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC;gBACrC,4CAA4C;gBAC5C,MAAM,6DAA6D,CAAC;aACvE;YACD,OAAO;SACV;aAAM;YACH,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC;YACpC,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,eAAwC,CAAC;YAC/E,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,WAAgC,CAAC;YACnE,IAAI,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC;gBACrC,4CAA4C;gBAC5C,MAAM,sEAAsE,CAAC;aAChF;SACJ;QAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,GAAG,SAAS,CAAC;QAEvD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAEhC,IAAI,IAAI,CAAC,cAAe,CAAC,MAAM,EAAE;YAC7B,IAAI,CAAC,cAAe,CAAC,MAAM,CAAC,IAAI,CAAC,cAAe,CAAC,CAAC;SACrD;QACD,IAAI,IAAI,CAAC,cAAe,CAAC,iBAAiB,EAAE;YACxC,IAAI,CAAC,cAAe,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,cAAe,CAAC,CAAC;SAChF;IACL,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,MAAc;QAChC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAE1C,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,kBAAkB,EAA2B,CAAC,CAAC;SACrF;IACL,CAAC;IAED;;OAEG;IACI,cAAc;QACjB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACtC,MAAM,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAA2B,CAAC;YACxG,IAAI,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;SACtD;QAED,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;IAC/B,CAAC;IAEM,sBAAsB,CAAC,eAAiC;QAC3D,MAAM,qBAAqB,GAAG,eAAwC,CAAC;QACvE,IAAI,qBAAqB,EAAE;YACvB,eAAe,CAAC,OAAO,EAAE,CAAC;SAC7B;IACL,CAAC;IAED,gFAAgF;IAChF,wCAAwC;IACxC,gFAAgF;IAEhF;;;OAGG;IACH,IAAW,eAAe;QACtB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,gBAAgB;IACT,sBAAsB;QACzB,OAAO,IAAI,qBAAqB,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,OAAwB;QAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAChD;QAED,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,iCAAiC;QACpC,OAAO,SAAS,CAAC,kBAAkB,CAAC;IACxC,CAAC;IAEM,+BAA+B,CAAC,OAAwB,EAAE,kBAA0B;QACvF,OAAO,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;IACrD,CAAC;IAED;;;;;;;;OAQG;IACI,sBAAsB,CACzB,IAAiB,EACjB,OAAiD,EACjD,uBAAuB,GAAG,IAAI,EAC9B,MAAM,GAAG,qBAAqB,CAAC,OAAO;QAEtC,MAAM,WAAW,GAAmC,EAAE,CAAC;QAEvD,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YACtD,WAAW,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;YACtD,WAAW,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;YAClG,WAAW,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;YAChI,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;YAClG,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC;YAC3C,WAAW,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC;YACvD,WAAW,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,KAAK,CAAC;YAC3D,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;SACrC;aAAM;YACH,WAAW,CAAC,eAAe,GAAY,OAAO,CAAC;YAC/C,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC,wBAAwB,CAAC;YACtD,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,8BAA8B,CAAC;YACpE,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC,kBAAkB,CAAC;YAClD,WAAW,CAAC,OAAO,GAAG,CAAC,CAAC;YACxB,WAAW,CAAC,aAAa,GAAG,CAAC,CAAC;YAC9B,WAAW,CAAC,aAAa,GAAG,KAAK,CAAC;SACrC;QAED,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE;YAC7F,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;SACrE;aAAM,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE;YAC7G,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;SACrE;QACD,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;YAC9E,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC,wBAAwB,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;SAC7F;QAED,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAElD,MAAM,KAAK,GAAwD,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;QAC/F,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAY,IAAI,CAAC;QACjG,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAI,CAAC,CAAC;QAEtF,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC;QACvB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,OAAO,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;QACtC,OAAO,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACrE,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;QAChD,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;QAChC,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QACpC,OAAO,CAAC,SAAS,GAAG,MAAM,GAAG,CAAC,CAAC;QAC/B,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;QAC3D,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;QAC3D,OAAO,CAAC,cAAc,GAAG,WAAW,CAAC,aAAa,CAAC;QACnD,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;QAElC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1C,IAAI,CAAC,uBAAuB,EAAE;YAC1B,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC;SAC1H;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACI,aAAa,CAChB,GAAqB,EACrB,QAAiB,EACjB,OAAgB,EAChB,KAA2B,EAC3B,eAAuB,SAAS,CAAC,8BAA8B,EAC/D,SAAuD,IAAI,EAC3D,UAA+D,IAAI,EACnE,SAAmG,IAAI,EACvG,WAAsC,IAAI,EAC1C,SAA2B,IAAI,EAC/B,kBAAoC,IAAI,EACxC,QAAiB,EACjB,aAAmB,EACnB,aAAsB,EACtB,aAAuB;QAEvB,OAAO,IAAI,CAAC,kBAAkB,CAC1B,GAAG,EACH,QAAQ,EACR,OAAO,EACP,KAAK,EACL,YAAY,EACZ,MAAM,EACN,OAAO,EACP,CACI,OAAwB,EACxB,SAAiB,EACjB,KAA2B,EAC3B,GAAuE,EACvE,OAAgB,EAChB,QAAiB,EACjB,YAAqB,EACrB,eAOY,EACd,EAAE;YACA,MAAM,WAAW,GAAG,GAAsD,CAAC,CAAC,kDAAkD;YAE9H,OAAO,CAAC,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC;YACtC,OAAO,CAAC,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC;YACxC,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;YAClC,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;YACpC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,IAAI,SAAS,CAAC,kBAAkB,CAAC;YACjG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC;YACxF,OAAO,CAAC,cAAc,GAAG,aAAa,IAAI,CAAC,CAAC;YAE5C,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;YAE1F,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,EAAE;gBAC/C,0GAA0G;gBAC1G,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;gBAE3J,IAAI,mBAAmB,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE;oBAChD,IAAI,CAAC,cAAc,CAAC,aAAa,CAC7B,WAAW,EACX,OAAO,EACP,WAAW,CAAC,KAAK,EACjB,WAAW,CAAC,MAAM,EAClB,OAAO,CAAC,KAAK,EACb,iBAAiB,CAAC,MAAM,EACxB,CAAC,EACD,CAAC,EACD,OAAO,EACP,KAAK,EACL,CAAC,EACD,CAAC,CACJ,CAAC;oBACF,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,EAAE;wBAC5B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;qBACvD;iBACJ;aACJ;iBAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,EAAE;gBACnC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;aACvD;YAED,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;aACpC;YAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YAEvB,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpD,OAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QACvC,CAAC,EACD,GAAG,EAAE,CAAC,KAAK,EACX,MAAM,EACN,QAAQ,EACR,MAAM,EACN,eAAe,EACf,QAAQ,EACR,aAAa,EACb,aAAa,CAChB,CAAC;IACN,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,OAAmB;QACxC,MAAM,eAAe,GAAG,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAC3D,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACvF,eAAe,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACnD,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC;QAC/B,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED,uDAAuD;IACvD;;;OAGG;IACI,gBAAgB;QACnB,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;IACzF,CAAC;IAEM,yBAAyB,CAAC,OAAwB;QACrD,IAAI,OAAO,CAAC,eAAe,EAAE;YACzB,MAAM,UAAU,GAAG,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,CAAC;YAEhE,IAAI,CAAC,UAAU,EAAE;gBACb,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;aACnE;YAED,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;SAClC;IACL,CAAC;IAED;;;;;OAKG;IACI,yBAAyB,CAAC,YAAoB,EAAE,OAAwB,EAAE,kBAA2B,KAAK;QAC7G,IAAI,eAAe,EAAE;YACjB,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;SAClC;QAED,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACI,yBAAyB,CAAC,OAAwB,EAAE,KAAuB,EAAE,QAA0B,IAAI,EAAE,QAA0B,IAAI;QAC9I,IAAI,KAAK,KAAK,IAAI,EAAE;YAChB,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;SAChC;QACD,IAAI,KAAK,KAAK,IAAI,EAAE;YAChB,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;SAChC;QACD,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE;YACvD,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;SAChC;IACL,CAAC;IAED;;;;;;OAMG;IACI,uBAAuB,CAAC,OAAwB,EAAE,KAAa,EAAE,MAAc,EAAE,QAAgB,CAAC;QACrG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YAC3B,iGAAiG;YACjG,OAAO;SACV;QAED,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,EAAE;YACjF,OAAO;SACV;QAED,MAAM,gBAAgB,GAAI,OAAO,CAAC,gBAA0C,CAAC,uBAAuB,CAAC;QAErG,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,sIAAsI;QAE1K,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;IAC5G,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,IAAY,EAAE,OAAoD,EAAE,QAAiB;QAC5G,QAAQ,GAAG,QAAQ,IAAI,IAAI,CAAC;QAC5B,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,MAAM,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAyC,CAAC;YAC5F,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEnG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAEvD,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,eAAe,EAAE;gBACtD,MAAM,WAAW,GAAG,QAAQ,GAAG,qBAAqB,CAAC,iBAAiB,CAAC;gBACvE,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,WAAW,EAAE,OAA0B,CAAC,CAAC,CAAC,mGAAmG;aACxL;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,UAAU,CAAC,OAAe,EAAE,MAAsC,EAAE,OAA8B,EAAE,IAAY;QACnH,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;OAMG;IACI,eAAe,CAAC,OAAe,EAAE,MAAsC,EAAE,QAAuB,EAAE,IAAY;QACjH,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAClD,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;SACrF;IACL,CAAC;IAES,WAAW,CACjB,OAAe,EACf,OAA8B;IAC9B,6DAA6D;IAC7D,oBAAoB,GAAG,KAAK,EAC5B,mBAAmB,GAAG,KAAK,EAC3B,IAAI,GAAG,EAAE,EACT,QAAiB;QAEjB,qEAAqE;QACrE,6FAA6F;QAC7F,+FAA+F;QAC/F,kFAAkF;QAClF,QAAQ,GAAG,QAAQ,IAAI,IAAI,CAAC;QAC5B,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,OAAO,EAAE;gBACV,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACpD,OAAO,KAAK,CAAC;aAChB;YAED,QAAQ;YACR,IAAmB,OAAQ,CAAC,KAAK,EAAE;gBAChB,OAAQ,CAAC,MAAM,EAAE,CAAC;aACpC;iBAAM,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,CAAC,wBAAwB,EAAE;gBACtE,gBAAgB;gBAChB,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpB,OAAO,KAAK,CAAC;aAChB;YAED,IAAI,eAAe,GAA8B,IAAI,CAAC;YACtD,IAAI,mBAAmB,EAAE;gBACrB,eAAe,GAAyB,OAAQ,CAAC,mBAAoB,CAAC;aACzE;iBAAM,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE;gBAC1B,eAAe,GAAoB,OAAO,CAAC,kBAAkB,EAAE,CAAC;aACnE;iBAAM,IAAI,OAAO,CAAC,MAAM,EAAE;gBACvB,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;aAC3C;iBAAM,IAAI,OAAO,CAAC,IAAI,EAAE;gBACrB,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC;aACzC;iBAAM,IAAI,OAAO,CAAC,SAAS,EAAE;gBAC1B,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC;aAC9C;iBAAM;gBACH,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC;aACvC;YAED,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE;gBACjD,sFAAsF;gBACtF,IAAI,eAAe,CAAC,MAAM,IAAI,eAAe,CAAC,sBAAsB,KAAK,OAAO,CAAC,eAAe,EAAE;oBAC9F,eAAe,CAAC,sBAAsB,GAAG,OAAO,CAAC,eAAe,CAAC;oBAEjE,MAAM,eAAe,GACjB,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,kBAAkB,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,mBAAmB;wBACjH,CAAC,CAAC,SAAS,CAAC,wBAAwB;wBACpC,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC;oBAC9C,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC;oBAChC,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC;iBACnC;gBAED,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC7C,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC7C,IAAI,eAAe,CAAC,IAAI,EAAE;oBACtB,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;iBAChD;gBAED,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,eAAe,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;aACpF;YAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAC;SAC7D;aAAM;YACH,IAAI,IAAI,CAAC,4BAA4B,EAAE;gBACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;oBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;iBAC5B;gBACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;oBAC9E,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,4DAA4D,EAAE,OAAO,CAAC,CAAC,CAAC;iBAC1H;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,MAAc,EAAE,eAAgC,EAAE,yBAAiC;QAC3G,IAAI,eAAe,CAAC,gCAAgC,KAAK,yBAAyB,EAAE;YAChF,eAAe,CAAC,gCAAgC,GAAG,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;SACpH;IACL,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,OAAe,EAAE,OAAwB,EAAE,IAAY;QACvE,IAAI,OAAO,KAAK,SAAS,EAAE;YACvB,OAAO;SACV;QAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,OAAwB;QAC3C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,OAAwB,EAAE,cAAkC;QAChF,cAAc,GAAG,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC;QAEvD,MAAM,kBAAkB,GAAG,OAAO,CAAC,gBAAmD,CAAC;QAEvF,IAAI,CAAC,kBAAkB,EAAE;YACrB,OAAO;SACV;QAED,IAAI,cAAc,KAAK,IAAI,CAAC,cAAc,EAAE;YACxC,gKAAgK;YAChK,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAChC;QAED,MAAM,MAAM,GAAI,OAAO,CAAC,gBAA0C,CAAC,MAAM,CAAC;QAC1E,MAAM,WAAW,GAAG,mBAAmB,CAAC,sBAAsB,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAE9F,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;gBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;aAC5B;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;gBAC9E,MAAM,CAAC,GAAG,CACN,SAAS;oBACJ,IAAY,CAAC,MAAM;oBACpB,8BAA8B;oBAC9B,OAAO,CAAC,KAAK;oBACb,WAAW;oBACX,OAAO,CAAC,MAAM;oBACd,WAAW;oBACX,OAAO,CAAC,MAAM;oBACd,oBAAoB;oBACpB,CAAC,cAAc,KAAK,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CACnE,CAAC;aACL;SACJ;QAED,IAAI,OAAO,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,MAAM,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;SACpG;aAAM;YACH,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,kBAAkB,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC;SACnG;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACI,iBAAiB,CACpB,OAAwB,EACxB,SAA0B,EAC1B,OAAe,EACf,OAAe,EACf,KAAa,EACb,MAAc,EACd,YAAoB,CAAC,EACrB,MAAc,CAAC,EACf,eAAe,GAAG,KAAK;QAEvB,IAAI,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAE1E,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,EAAE;YAC/C,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;SACvF;QAED,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;QAE1F,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAEnK,IAAI,eAAe,EAAE;YACjB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;SAClC;IACL,CAAC;IAED;;OAEG;IACI,sCAAsC,CACzC,OAAwB,EACxB,cAAsB,EACtB,KAAa,EACb,MAAc,EACd,SAA0B,EAC1B,YAAoB,CAAC,EACrB,MAAc,CAAC;QAEf,IAAI,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAE1E,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,EAAE;YAC/C,OAAO,CAAC,MAAM,GAAG,cAAc,CAAC;YAChC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;SACtG;QAED,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;QAE1F,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACjJ,CAAC;IAED;;OAEG;IACI,4BAA4B,CAC/B,OAAwB,EACxB,SAA0B,EAC1B,YAAoB,CAAC,EACrB,MAAc,CAAC,EACf,qBAA8B,EAC9B,wBAAwB,GAAG,KAAK;QAEhC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QACrE,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAEvE,MAAM,KAAK,GAAG,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACrG,MAAM,MAAM,GAAG,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAExG,IAAI,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAE1E,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,EAAE;YAC/C,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;SACtG;QAED,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;QAE1F,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3J,CAAC;IAED;;OAEG;IACI,+BAA+B,CAAC,OAAwB,EAAE,SAA0B,EAAE,YAAoB,CAAC,EAAE,MAAc,CAAC;QAC/H,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,OAAwB,EAAE,KAAqC,EAAE,YAAoB,CAAC,EAAE,MAAc,CAAC;QAChI,IAAI,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAE1E,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,EAAE;YAC/C,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;SACvF;QAED,IAAI,KAAK,YAAY,gBAAgB,EAAE;YACnC,4CAA4C;YAC5C,MAAM,yEAAyE,CAAC;SACnF;QAED,MAAM,MAAM,GAAG,KAAoB,CAAC,CAAC,uEAAuE;QAE5G,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QAEtD,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7J,CAAC;IAED;;;;;;;;;OASG;IACH,6DAA6D;IACtD,UAAU,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAE,QAAQ,GAAG,IAAI,EAAE,aAAa,GAAG,IAAI;QACxG,MAAM,iBAAiB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAC9D,MAAM,eAAe,GAAG,iBAAiB,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,eAAe,EAAE;YAClB,0EAA0E;YAC1E,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7C;QACD,MAAM,UAAU,GAAG,eAAe,CAAC,kBAAkB,CAAC;QACtD,MAAM,gBAAgB,GAAG,eAAe,CAAC,MAAM,CAAC;QAChD,IAAI,CAAC,UAAU,EAAE;YACb,kIAAkI;YAClI,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7C;QACD,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;QACD,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;IAC7F,CAAC;IAED,gFAAgF;IAChF,gDAAgD;IAChD,gFAAgF;IAEhF;;OAEG;IACI,UAAU;QACb,KAAK,CAAC,UAAU,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;QAEnC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACnD,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE,CAAC;QAC9C,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC;QAE7C,IAAI,IAAI,CAAC,SAAS,CAAC,0BAA0B,EAAE;YAC3C,IAAI,IAAI,CAAC,4BAA4B,EAAE;gBACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;oBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;iBAC5B;gBACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;oBAC9E,MAAM,IAAI,GAAkB,EAAE,CAAC;oBAC/B,KAAK,MAAM,IAAI,IAAI,aAAa,CAAC,mBAAmB,EAAE;wBAClD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;qBACnE;oBACD,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACzF;aACJ;YACD,aAAa,CAAC,mBAAmB,GAAG,EAAE,CAAC;SAC1C;QAED,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC1E,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;QAClF,IAAI,CAAC,iBAAiB,CAAC,8BAA8B,GAAG,IAAI,CAAC,SAAS,CAAC,8BAA8B,CAAC;QACtG,IAAI,CAAC,iBAAiB,CAAC,2BAA2B,GAAG,IAAI,CAAC,SAAS,CAAC,2BAA2B,CAAC;QAChG,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,SAAS,CAAC,oBAAoB,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,SAAS,CAAC,8BAA8B,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,SAAS,CAAC,2BAA2B,GAAG,CAAC,CAAC;QAE/C,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;QACrC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QAEjC,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;QAEtC,KAAK,CAAC,QAAQ,EAAE,CAAC;QAEjB,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;gBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;aAC5B;YACD,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;gBACrD,MAAM,CAAC,GAAG,CAAC,CAAC,YAAY,GAAI,IAAY,CAAC,MAAM,GAAG,QAAQ,EAAE,qBAAqB,CAAC,CAAC,CAAC;aACvF;YACD,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;gBACpD,IAAY,CAAC,MAAM,EAAE,CAAC;gBACvB,IAAK,IAAY,CAAC,MAAM,KAAK,IAAI,CAAC,uBAAuB,EAAE;oBACvD,MAAM,CAAC,GAAG,CAAC,CAAC,YAAY,GAAI,IAAY,CAAC,MAAM,GAAG,UAAU,EAAE,qBAAqB,CAAC,CAAC,CAAC;iBACzF;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,uIAAuI;QACvI,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;QACvD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;QAEvD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEhD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACvF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAEvF,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAErD,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE3D,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;IAED,gBAAgB;IACT,uCAAuC;QAC1C,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAC;IACzC,CAAC;IAED,gFAAgF;IAChF,2CAA2C;IAC3C,gFAAgF;IAExE,4BAA4B,CAChC,mBAAwC,EACxC,cAAuB,EACvB,UAAiC,EACjC,UAAmB,EACnB,YAAqB;QAErB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,MAAM,SAAS,GAAG,mBAAgD,CAAC;QAEnE,MAAM,mBAAmB,GAAG,SAAS,CAAC,oBAAoB,CAAC;QAC3D,MAAM,sBAAsB,GAAG,mBAAmB,EAAE,gBAAmD,CAAC;QACxG,MAAM,sBAAsB,GAAG,sBAAsB,EAAE,kBAA0C,CAAC;QAClG,MAAM,0BAA0B,GAAG,sBAAsB,EAAE,cAAc,EAAE,CAAC;QAE5E,MAAM,gBAAgB,GAAG,sBAAsB,EAAE,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,6BAA8B,CAAC,CAAC;QACvH,MAAM,oBAAoB,GAAG,0BAA0B,EAAE,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,6BAA8B,CAAC,CAAC;QAC/H,MAAM,sBAAsB,GAAG,sBAAsB,CAAC,CAAC,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAEpI,MAAM,gBAAgB,GAA4C,EAAE,CAAC;QAErE,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,gCAAgC,EAAE,CAAC;SAC3C;QAED,MAAM,sBAAsB,GAAG,UAAU,CAAC;QAC1C,IAAI,UAAU,EAAE;YACZ,sBAAsB,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,CAAC;YAC9C,sBAAsB,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,CAAC;YAC9C,sBAAsB,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,CAAC;YAC9C,sBAAsB,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,CAAC;SACjD;QAED,MAAM,cAAc,GAAG,cAAc,IAAI,UAAU,CAAC;QACpD,MAAM,cAAc,GAAG,cAAc,IAAI,UAAU,CAAC;QACpD,MAAM,gBAAgB,GAAG,cAAc,IAAI,YAAY,CAAC;QAExD,IAAI,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,OAAO,EAAE;YAC7C,uBAAuB;YACvB,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC5D,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,mBAAmB,CAAC;aACxD;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBAClD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,+HAA+H;gBACtK,MAAM,UAAU,GAAG,SAAS,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC;gBAC1C,MAAM,aAAa,GAAG,UAAU,EAAE,gBAAmD,CAAC;gBACtF,MAAM,aAAa,GAAG,aAAa,EAAE,kBAAkB,CAAC;gBACxD,IAAI,aAAa,IAAI,aAAa,EAAE;oBAChC,MAAM,cAAc,GAAG,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;oBAEvD,MAAM,UAAU,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;oBACpD,MAAM,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;oBAClD,MAAM,cAAc,GAAG;wBACnB,GAAG,IAAI,CAAC,qBAAqB,CAAC,6BAA8B;wBAC5D,MAAM,EAAE,aAAa,CAAC,MAAM;wBAC5B,cAAc,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,UAAU;qBAC9E,CAAC;oBACF,MAAM,kBAAkB,GAAG;wBACvB,GAAG,IAAI,CAAC,qBAAqB,CAAC,6BAA8B;wBAC5D,MAAM,EAAE,aAAa,CAAC,MAAM;wBAC5B,cAAc,EAAE,CAAC;qBACpB,CAAC;oBACF,MAAM,WAAW,GAAG,UAAU,CAAC,IAAI,KAAK,SAAS,CAAC,4BAA4B,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,CAAC,0BAA0B,CAAC;oBAE3I,MAAM,gBAAgB,GAAG,aAAa,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;oBAClE,MAAM,oBAAoB,GAAG,cAAc,EAAE,UAAU,CAAC,kBAAkB,CAAC,CAAC;oBAE5E,gBAAgB,CAAC,IAAI,CAAC;wBAClB,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,gBAAgB;wBACpE,aAAa,EAAE,cAAc,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS;wBAC5D,UAAU,EAAE,KAAK,KAAK,CAAC,IAAI,cAAc,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS;wBAC3G,MAAM,EAAE,KAAK,KAAK,CAAC,IAAI,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI;wBAClG,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,KAAK;qBACzC,CAAC,CAAC;iBACN;aACJ;YACD,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,QAAS,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YACnF,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACrE;aAAM;YACH,uBAAuB;YACvB,MAAM,eAAe,GAAG,SAAS,CAAC,OAAO,CAAC;YAC1C,IAAI,eAAe,EAAE;gBACjB,MAAM,UAAU,GAAG,eAAe,CAAC,gBAAyC,CAAC;gBAC7E,MAAM,UAAU,GAAG,UAAU,CAAC,kBAAmB,CAAC;gBAElD,MAAM,cAAc,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;gBACnD,MAAM,gBAAgB,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,6BAA8B,CAAC,CAAC;gBAC1G,MAAM,oBAAoB,GAAG,cAAc,EAAE,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,6BAA8B,CAAC,CAAC;gBACnH,MAAM,WAAW,GAAG,eAAe,CAAC,IAAI,KAAK,SAAS,CAAC,4BAA4B,IAAI,eAAe,CAAC,IAAI,KAAK,SAAS,CAAC,0BAA0B,CAAC;gBAErJ,gBAAgB,CAAC,IAAI,CAAC;oBAClB,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,gBAAgB;oBACpE,aAAa,EAAE,cAAc,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS;oBAC5D,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS;oBAC5F,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI;oBACnF,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,KAAK;iBACzC,CAAC,CAAC;aACN;iBAAM;gBACH,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC/B;SACJ;QAED,IAAI,CAAC,eAAe,EAAE,CAAC,oBAAoB,GAAG,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,mBAAmB,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE5H,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,GAAG;YAC9C,KAAK,EAAE,CAAC,mBAAmB,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,YAAY;YAC1D,gBAAgB;YAChB,sBAAsB,EAClB,mBAAmB,IAAI,sBAAsB;gBACzC,CAAC,CAAC;oBACI,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,gBAAiB;oBACrE,eAAe,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,SAAS;oBACjI,WAAW,EAAE,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI;oBACxF,YAAY,EAAE,eAAe,CAAC,OAAO,CAAC,KAAK;oBAC3C,iBAAiB,EAAE,SAAS,CAAC,+BAA+B,IAAI,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS;oBACtH,aAAa,EAAE,CAAC,sBAAsB;wBAClC,CAAC,CAAC,SAAS;wBACX,CAAC,CAAC,SAAS,CAAC,+BAA+B,IAAI,gBAAgB;4BAC7D,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK;4BAC9B,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI;oBACnC,cAAc,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK;iBACtF;gBACH,CAAC,CAAC,SAAS;YACnB,iBAAiB,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;SAClG,CAAC;QACF,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACtG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,CAAC;QAE/G,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;gBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;aAC5B;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;gBAC9E,MAAM,eAAe,GAAG,SAAS,CAAC,OAAQ,CAAC;gBAC3C,MAAM,CAAC,GAAG,CAAC;oBACP,SAAS;wBACJ,IAAY,CAAC,MAAM;wBACpB,yCAAyC;wBACzC,mBAAmB,CAAC,KAAK;wBACzB,6BAA6B;wBAC7B,eAAe,CAAC,QAAQ;wBACxB,UAAU;wBACV,eAAe,CAAC,KAAK;wBACrB,WAAW;wBACX,eAAe,CAAC,MAAM;wBACtB,mBAAmB;wBACnB,cAAc;oBAClB,uBAAuB;oBACvB,IAAI,CAAC,qBAAqB,CAAC,oBAAoB;iBAClD,CAAC,CAAC;aACN;SACJ;QAED,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;QAEpC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,IAAI,CAAC,sBAAsB,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAAE;YACjG,IAAI,CAAC,qBAAqB,CAAC,OAAO,GAAG,KAAK,CAAC;SAC9C;IACL,CAAC;IAEO,oBAAoB,CAAC,cAAuB,EAAE,UAAkC,EAAE,UAAoB,EAAE,YAAsB;QAClI,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,gCAAgC,EAAE,CAAC;SAC3C;QAED,MAAM,cAAc,GAAG,cAAc,IAAI,UAAU,CAAC;QACpD,MAAM,cAAc,GAAG,cAAc,IAAI,UAAU,CAAC;QACpD,MAAM,gBAAgB,GAAG,cAAc,IAAI,YAAY,CAAC;QAExD,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAE,CAAC,UAAU,GAAG,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;QAC5H,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAE,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC;QAC5J,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,sBAAuB,CAAC,eAAe,GAAG,cAAc;YACtG,CAAC,CAAC,IAAI,CAAC,qBAAqB;gBACxB,CAAC,CAAC,IAAI,CAAC,uBAAuB;gBAC9B,CAAC,CAAC,IAAI,CAAC,gBAAgB;YAC3B,CAAC,CAAC,SAAS,CAAC;QAChB,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,sBAAuB,CAAC,WAAW,GAAG,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC;QACpK,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,sBAAuB,CAAC,iBAAiB,GAAG,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC;QACrJ,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,sBAAuB,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,eAAe;YAC3G,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,gBAAgB;gBAChB,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK;gBAC9B,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC;QACpC,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,iBAAiB,GAAG,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC;QAEnJ,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;QAC3D,IAAI,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,CAAC,CAAE,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAEjF,0BAA0B;QAC1B,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YACzB,mCAAmC,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;YACrE,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAE,CAAC,aAAa,GAAG,gBAAgB,CAAC,UAAU,CAAC,mCAAmC,CAAC,CAAC;SAC3J;aAAM;YACH,uBAAuB,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;YACzD,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAE,CAAC,IAAI,GAAG,gBAAgB,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC;SACtI;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;gBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;aAC5B;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;gBAC9E,MAAM,CAAC,GAAG,CAAC;oBACP,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,qCAAqC,GAAI,IAAI,CAAC,mBAA2B,CAAC,KAAK;oBAClH,UAAU,GAAI,IAAI,CAAC,mBAA2B,CAAC,MAAM,GAAG,mBAAmB,GAAG,cAAc;oBAC5F,uBAAuB;oBACvB,IAAI,CAAC,sBAAsB,CAAC,oBAAoB;iBACnD,CAAC,CAAC;aACN;SACJ;QAED,IAAI,CAAC,eAAe,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAEvC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACxG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,CAAC;QAEjH,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACzD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAElD,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;QAEpC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,IAAI,CAAC,qBAAqB,CAAC,OAAO,GAAG,KAAK,CAAC;SAC9C;IACL,CAAC;IAED,gBAAgB;IACT,qBAAqB;QACxB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,OAAO,CAAC,CAAC;SACZ;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/D,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC5F,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC9C,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;SAC5B;QACD,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC;QAE9B,IAAI,CAAC,eAAe,CAAC,OAAO,CACxB,IAAI,CAAC,eAAe,EACpB,CAAC,IAAI,CAAC,oBAAoB,IAAK,IAAI,CAAC,oBAAkD,CAAC,cAAc;YACjG,CAAC,CAAE,IAAI,CAAC,oBAAkD,CAAC,cAAc;YACzE,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAsB,CAC7D,CAAC;QACF,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC;QAE1B,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;gBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;aAC5B;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;gBAC9E,MAAM,CAAC,GAAG,CACN,SAAS;oBACJ,IAAY,CAAC,MAAM;oBACpB,KAAK;oBACL,CAAC,gBAAgB,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC;oBACnD,WAAW;oBACX,CAAC,gBAAgB,KAAK,CAAC,CAAC,CAAC,CAAC,8BAA8B,GAAG,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CACpH,CAAC;aACL;SACJ;QACD,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAE/B,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;;;;;;;;OASG;IACI,eAAe,CAClB,OAA4B,EAC5B,YAAoB,CAAC,EACrB,aAAsB,EACtB,cAAuB,EACvB,uBAAiC,EACjC,QAAQ,GAAG,CAAC,EACZ,KAAK,GAAG,CAAC;QAET,MAAM,eAAe,GAAG,OAAO,CAAC,OAAO,EAAE,gBAAmD,CAAC;QAE7F,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACrD;aAAM;YACH,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAChC;QACD,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QAEpC,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;QAC3E,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAAC,oBAAoB;YAC1F,CAAC,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,MAAM,CAAC;YACvG,CAAC,CAAC,SAAS,CAAC;QAEhB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACxD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAEjD,IAAI,CAAC,qBAAqB,CAAC,6BAA6B,GAAG;YACvD,MAAM,EAAE,IAAI,CAAC,YAAgC;YAC7C,SAAS,EAAE,eAAe,CAAC,oBAAoB,CAAC,GAAG;YACnD,aAAa,EAAE,CAAC;YAChB,cAAc,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK;YAC9D,YAAY,EAAE,QAAQ;YACtB,eAAe,EAAE,CAAC;YAClB,MAAM,EAAE,eAAe,CAAC,aAAa,CAAC,GAAG;SAC5C,CAAC;QAEF,IAAI,CAAC,qBAAqB,CAAC,6BAA6B,GAAG;YACvD,MAAM,EAAE,IAAI,CAAC,mBAAoB;YACjC,SAAS,EAAE,eAAe,CAAC,oBAAoB,CAAC,GAAG;YACnD,aAAa,EAAE,CAAC;YAChB,cAAc,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK;YAC9D,YAAY,EAAE,CAAC;YACf,eAAe,EAAE,CAAC;YAClB,MAAM,EAAE,eAAe,CAAC,aAAa,CAAC,GAAG;SAC5C,CAAC;QAEF,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;gBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;aAC5B;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;gBAC9E,MAAM,CAAC,GAAG,CAAC;oBACP,SAAS;wBACJ,IAAY,CAAC,MAAM;wBACpB,gCAAgC;wBAChC,OAAO,CAAC,KAAK;wBACb,6BAA6B;wBAC7B,OAAO,CAAC,OAAO,EAAE,QAAQ;wBACzB,SAAS;wBACT,SAAS;wBACT,aAAa;wBACb,QAAQ;wBACR,UAAU;wBACV,KAAK;oBACT,gCAAgC;oBAChC,IAAI,CAAC,qBAAqB,CAAC,6BAA6B;oBACxD,gCAAgC;oBAChC,IAAI,CAAC,qBAAqB,CAAC,6BAA6B;iBAC3D,CAAC,CAAC;aACN;SACJ;QAED,qJAAqJ;QAErJ,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,uBAAuB,EAAE;YAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;SACzE;aAAM;YACH,IAAI,CAAC,aAAa,EAAE;gBAChB,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC9B,IAAI,QAAQ,EAAE;oBACV,aAAa,GAAG,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;iBACzD;aACJ;YACD,IAAI,CAAC,cAAc,EAAE;gBACjB,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;gBAChC,IAAI,QAAQ,EAAE;oBACV,cAAc,GAAG,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;iBAC3D;aACJ;YAED,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;SACvD;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,OAA4B,EAAE,sBAAsB,GAAG,KAAK,EAAE,cAA2B;QAC9G,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAE1C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC,kGAAkG;QAEpI,IAAI,cAAc,EAAE;YAChB,cAAc,EAAE,CAAC;SACpB;QAED,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QAEpC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,OAAO,CAAC,OAAO,EAAE,eAAe,IAAI,CAAC,sBAAsB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YAChF,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE;gBACnC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;aAC5B;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE;gBAC9E,MAAM,CAAC,GAAG,CAAC,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,kCAAkC,GAAG,OAAO,CAAC,KAAK,GAAG,6BAA6B,EAAE,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;aAChK;SACJ;QAED,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACrC,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACI,yBAAyB;QAC5B,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACrD;aAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACjC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;SACpC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAED,gFAAgF;IAChF,sCAAsC;IACtC,gFAAgF;IAEhF;;OAEG;IACI,eAAe,CAAC,OAAiC;QACpD,MAAM,MAAM,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC;QACrE,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,YAAY,KAAK,MAAM,EAAE;YAC9B,OAAO;SACV;QACD,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,OAAiC;QAC3D,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC5E,IAAI,IAAI,CAAC,mBAAmB,KAAK,OAAO,CAAC,kBAAkB,EAAE;YACzD,OAAO;SACV;QACD,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAC1D,CAAC;IAEM,iBAAiB;QACpB,2BAA2B;IAC/B,CAAC;IAEM,kBAAkB;QACrB,2BAA2B;IAC/B,CAAC;IAED;;;;;;;;;OASG;IACI,QAAQ,CAAC,OAAgB,EAAE,UAAkB,CAAC,EAAE,KAAe,EAAE,WAAW,GAAG,KAAK,EAAE,aAAuB,EAAE,OAAuB,EAAE,eAAuB,CAAC;QACnK,UAAU;QACV,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,EAAE;YACnD,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,OAAO,CAAC;SAC1C;QAED,YAAY;QACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,IAAI,aAAa,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,IAAI,IAAI,CAAC,kBAAkB,CAAC,QAAQ,KAAK,QAAQ,IAAI,KAAK,EAAE;YACxD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,QAAQ,CAAC;SAC/C;QAED,WAAW;QACX,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAEnC,aAAa;QACb,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxG,IAAI,IAAI,CAAC,kBAAkB,CAAC,SAAS,KAAK,SAAS,IAAI,KAAK,EAAE;YAC1D,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC;SACjD;QAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,GAAG,OAAO,CAAC;IACzD,CAAC;IAEO,uBAAuB,CAAC,UAAsC;QAClE,MAAM,oBAAoB,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACxG,MAAM,oBAAoB,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAEjG,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE;YAC5B,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;SACnC;QACD,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;YAC3B,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;SAClC;QACD,IAAI,oBAAoB,EAAE;YACtB,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;SACrC;QACD,IAAI,oBAAoB,EAAE;YACtB,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;SACrC;IACL,CAAC;IAEO,KAAK,CAAC,QAAgB,EAAE,QAAgB,EAAE,KAAa,EAAE,KAAa,EAAE,cAAsB;QAClG,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAChD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAEpC,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,MAAM,qBAAqB,GAAG,IAAI,CAAC,cAAe,CAAC,gBAAyC,CAAC;QAE7F,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;QAEzI,IAAI,qBAAqB,CAAC,aAAa,EAAE;YACrC,qBAAqB,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC7C,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,aAAa,CAAC,SAAS,EAAG,EAAE,CAAC,EAAE,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;SAC3H;QAED,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE;YAC9B,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,OAAO;SACV;QAED,IACI,CAAC,IAAI,CAAC,iBAAiB;YACvB,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,uBAAuB,CAAC,OAAO,IAAI,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,EAC1K;YACE,IAAI,CAAC,mBAAmB,CAAC,UAAU,GAAG,SAAS,CAAC;SACnD;QAED,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC;QACnF,IAAI,WAAW,GAAkD,UAAU,CAAC;QAE5E,IAAI,WAAW,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;YAC/C,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;gBACjC,IAAI,CAAC,SAAS,CAAC,2BAA2B,EAAE,CAAC;gBAC7C,IAAI,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAAE;oBAC7C,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,KAAK,EAAE,cAAc,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;iBAC/E;gBACD,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;gBAC1D,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,OAAO;aACV;YAED,WAAW,GAAG,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,8BAA8B;YACpK,UAAU,CAAC,YAAY,EAAE,CAAC;SAC7B;QAED,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,EAAE;YACtD,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACxF,MAAM,WAAW,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBAClF,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC;gBAC5E,MAAM,cAAc,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,8BAA8B,IAAI,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC;gBAChK,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,IAAI,cAAc,EAAE;oBAC9G,YAAY,IAAI,MAAM,CAAC;iBAC1B;gBACD,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;aACxB;SACJ;QAED,IAAI,CAAC,uBAAuB,CAAC,YAAY,GAAG,YAAY,CAAC;QAEzD,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;QACpI,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,qBAAqB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAEtI,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;YACjC,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC1E,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBACzB,IAAI,CAAC,SAAS,CAAC,8BAA8B,EAAE,CAAC;gBAChD,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC;oBACjD,YAAY,EAAE,IAAI,CAAC,oBAAoB,CAAC,YAAY;oBACpD,kBAAkB,EAAE,IAAI,CAAC,mBAAmB;oBAC5C,WAAW,EAAE,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC;iBACtE,CAAC,CAAC;aACN;SACJ;QAED,gBAAgB;QAChB,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAElC,4BAA4B;QAC5B,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,WAAW,CAAC,cAAc,CACtB,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAC3C,IAAI,CAAC,mBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,EAC5G,CAAC,CACJ,CAAC;SACL;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC;QAC9D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACvD,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;YAE1C,MAAM,MAAM,GAAG,YAAY,CAAC,eAAe,CAAC;YAC5C,IAAI,MAAM,EAAE;gBACR,WAAW,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,kBAAkB,EAAE,YAAY,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;aAC/H;SACJ;QAED,mBAAmB;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,WAAW,CAAC,YAAY,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9C;QAED,OAAO;QACP,MAAM,aAAa,GAAG,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;QAEjF,IAAI,aAAa,IAAI,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAAE;YAC9D,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,KAAK,EAAE,cAAc,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;YAC5E,IAAI,QAAQ,KAAK,CAAC,EAAE;gBAChB,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;aACnF;iBAAM;gBACH,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;aAC5E;SACJ;aAAM,IAAI,QAAQ,KAAK,CAAC,EAAE;YACvB,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,cAAc,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACpE;aAAM;YACH,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,cAAc,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;SAC1D;QAED,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,mBAAmB,CAAC,UAAU,GAAI,WAAsC,CAAC,MAAM,EAAE,CAAC;YACvF,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;SAC7D;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG;IACI,gBAAgB,CAAC,QAAgB,EAAE,UAAkB,EAAE,UAAkB,EAAE,iBAAyB,CAAC;QACxG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;OAMG;IACI,cAAc,CAAC,QAAgB,EAAE,aAAqB,EAAE,aAAqB,EAAE,iBAAyB,CAAC;QAC5G,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IAC1E,CAAC;IAED,gFAAgF;IAChF,uCAAuC;IACvC,gFAAgF;IAEhF;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QAC/B,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE,CAAC;QAC9C,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED,gFAAgF;IAChF,oCAAoC;IACpC,gFAAgF;IAEhF;;;;OAIG;IACI,cAAc,CAAC,SAAS,GAAG,KAAK;QACnC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;SAC1C;QAED,OAAO,IAAI,CAAC,gBAAgB,EAAE,KAAK,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,SAAS,GAAG,KAAK;QACpC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;SAC3C;QAED,OAAO,IAAI,CAAC,gBAAgB,EAAE,MAAM,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED,gFAAgF;IAChF,sCAAsC;IACtC,gFAAgF;IAEhF;;;OAGG;IACI,QAAQ;QACX,uCAAuC;QACvC,OAAO,CAAC,CAAC;IACb,CAAC;IAED,gFAAgF;IAChF,6CAA6C;IAC7C,gFAAgF;IAEhF;;OAEG;IACI,YAAY,KAAU,CAAC;IAE9B;;OAEG;IACI,oBAAoB;QACvB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,oCAAoC,CAAC,eAAiC,EAAE,MAAkB;QAC7F,kCAAkC;QAClC,6BAA6B;QAC7B,MAAM,EAAE,CAAC;IACb,CAAC;IAED;;OAEG;IACI,yBAAyB;QAC5B,kCAAkC;QAClC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gBAAgB;IACT,oBAAoB;QACvB,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;OAEG;IACI,YAAY,KAAI,CAAC;IAExB;;OAEG;IACI,uBAAuB;QAC1B,4CAA4C;QAC5C,MAAM,sIAAsI,CAAC;IACjJ,CAAC;IAED,6EAA6E;IAE7E;;OAEG;IACI,sBAAsB;QACzB,4CAA4C;QAC5C,MAAM,mDAAmD,CAAC;IAC9D,CAAC;IAED;;OAEG;IACI,WAAW;QACd,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACI,WAAW;QACd,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,SAAS;QACZ,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,SAAS;QACZ,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,SAAS;QACZ,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,WAAW;QACd,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,SAAS;QACZ,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,SAAS;QACZ,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,SAAS;QACZ,OAAO,KAAK,CAAC;IACjB,CAAC;;AA3iHD,2BAA2B;AACH,qCAAwB,GAAmB;IAC/D,MAAM,EAAE,GAAG,KAAK,CAAC,cAAc,qBAAqB;IACpD,QAAQ,EAAE,GAAG,KAAK,CAAC,cAAc,uBAAuB;CAC3D,AAH+C,CAG9C;AAEa,wBAAW,GAAG,CAAC,AAAJ,CAAK;AAE/B,8DAA8D;AAChD,qBAAQ,GAAG,IAAI,AAAP,CAAQ", "sourcesContent": ["/* eslint-disable babylonjs/available */\r\nimport { Logger } from \"../Misc/logger\";\r\nimport type { Nullable, DataArray, IndicesArray, Immutable, FloatArray } from \"../types\";\r\nimport { Color4 } from \"../Maths/math\";\r\nimport { Engine } from \"../Engines/engine\";\r\nimport { InternalTexture, InternalTextureSource } from \"../Materials/Textures/internalTexture\";\r\nimport type { IEffectCreationOptions } from \"../Materials/effect\";\r\nimport { Effect } from \"../Materials/effect\";\r\nimport type { EffectFallbacks } from \"../Materials/effectFallbacks\";\r\nimport { Constants } from \"./constants\";\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nimport * as WebGPUConstants from \"./WebGPU/webgpuConstants\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport type { IWebGPURenderPipelineStageDescriptor } from \"./WebGPU/webgpuPipelineContext\";\r\nimport { WebGPUPipelineContext } from \"./WebGPU/webgpuPipelineContext\";\r\nimport type { IPipelineContext } from \"./IPipelineContext\";\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport type { IShaderProcessor } from \"./Processors/iShaderProcessor\";\r\nimport { WebGPUShaderProcessorGLSL } from \"./WebGPU/webgpuShaderProcessorsGLSL\";\r\nimport { WebGPUShaderProcessorWGSL } from \"./WebGPU/webgpuShaderProcessorsWGSL\";\r\nimport type { ShaderProcessingContext } from \"./Processors/shaderProcessingOptions\";\r\nimport { WebGPUShaderProcessingContext } from \"./WebGPU/webgpuShaderProcessingContext\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport { WebGPUTextureHelper } from \"./WebGPU/webgpuTextureHelper\";\r\nimport { WebGPUTextureManager } from \"./WebGPU/webgpuTextureManager\";\r\nimport type { ISceneLike, ThinEngineOptions } from \"./thinEngine\";\r\nimport { WebGPUBufferManager } from \"./WebGPU/webgpuBufferManager\";\r\nimport type { HardwareTextureWrapper } from \"../Materials/Textures/hardwareTextureWrapper\";\r\nimport { WebGPUHardwareTexture } from \"./WebGPU/webgpuHardwareTexture\";\r\nimport type { IColor4Like } from \"../Maths/math.like\";\r\nimport { UniformBuffer } from \"../Materials/uniformBuffer\";\r\nimport { WebGPUCacheSampler } from \"./WebGPU/webgpuCacheSampler\";\r\nimport type { WebGPUCacheRenderPipeline } from \"./WebGPU/webgpuCacheRenderPipeline\";\r\nimport { WebGPUCacheRenderPipelineTree } from \"./WebGPU/webgpuCacheRenderPipelineTree\";\r\nimport { WebGPUStencilStateComposer } from \"./WebGPU/webgpuStencilStateComposer\";\r\nimport { WebGPUDepthCullingState } from \"./WebGPU/webgpuDepthCullingState\";\r\nimport { DrawWrapper } from \"../Materials/drawWrapper\";\r\nimport { WebGPUMaterialContext } from \"./WebGPU/webgpuMaterialContext\";\r\nimport { WebGPUDrawContext } from \"./WebGPU/webgpuDrawContext\";\r\nimport { WebGPUCacheBindGroups } from \"./WebGPU/webgpuCacheBindGroups\";\r\nimport { WebGPUClearQuad } from \"./WebGPU/webgpuClearQuad\";\r\nimport type { IStencilState } from \"../States/IStencilState\";\r\nimport { WebGPURenderItemBlendColor, WebGPURenderItemScissor, WebGPURenderItemStencilRef, WebGPURenderItemViewport, WebGPUBundleList } from \"./WebGPU/webgpuBundleList\";\r\nimport { WebGPUTimestampQuery } from \"./WebGPU/webgpuTimestampQuery\";\r\nimport type { ComputeEffect } from \"../Compute/computeEffect\";\r\nimport { WebGPUOcclusionQuery } from \"./WebGPU/webgpuOcclusionQuery\";\r\nimport { ShaderCodeInliner } from \"./Processors/shaderCodeInliner\";\r\nimport type { TwgslOptions } from \"./WebGPU/webgpuTintWASM\";\r\nimport { WebGPUTintWASM } from \"./WebGPU/webgpuTintWASM\";\r\nimport type { ExternalTexture } from \"../Materials/Textures/externalTexture\";\r\nimport { WebGPUShaderProcessor } from \"./WebGPU/webgpuShaderProcessor\";\r\nimport { ShaderLanguage } from \"../Materials/shaderLanguage\";\r\nimport type { InternalTextureCreationOptions, TextureSize } from \"../Materials/Textures/textureCreationOptions\";\r\nimport { WebGPUSnapshotRendering } from \"./WebGPU/webgpuSnapshotRendering\";\r\nimport type { WebGPUDataBuffer } from \"../Meshes/WebGPU/webgpuDataBuffer\";\r\nimport type { WebGPURenderTargetWrapper } from \"./WebGPU/webgpuRenderTargetWrapper\";\r\n\r\nimport \"../Buffers/buffer.align\";\r\n\r\nimport \"../ShadersWGSL/postprocess.vertex\";\r\n\r\nimport type { VideoTexture } from \"../Materials/Textures/videoTexture\";\r\nimport type { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport type { RenderTargetWrapper } from \"./renderTargetWrapper\";\r\nimport { WebGPUPerfCounter } from \"./WebGPU/webgpuPerfCounter\";\r\nimport type { Scene } from \"core/scene\";\r\n\r\nconst viewDescriptorSwapChainAntialiasing: GPUTextureViewDescriptor = {\r\n    label: `TextureView_SwapChain_ResolveTarget`,\r\n    dimension: WebGPUConstants.TextureDimension.E2d,\r\n    format: undefined as any, // will be updated with the right value\r\n    mipLevelCount: 1,\r\n    arrayLayerCount: 1,\r\n};\r\n\r\nconst viewDescriptorSwapChain: GPUTextureViewDescriptor = {\r\n    label: `TextureView_SwapChain`,\r\n    dimension: WebGPUConstants.TextureDimension.E2d,\r\n    format: undefined as any, // will be updated with the right value\r\n    mipLevelCount: 1,\r\n    arrayLayerCount: 1,\r\n};\r\n\r\nconst disableUniformityAnalysisMarker = \"/* disable_uniformity_analysis */\";\r\n\r\nconst tempColor4 = new Color4();\r\n\r\n/** @internal */\r\ninterface IWebGPURenderPassWrapper {\r\n    renderPassDescriptor: Nullable<GPURenderPassDescriptor>;\r\n\r\n    colorAttachmentViewDescriptor: Nullable<GPUTextureViewDescriptor>;\r\n    depthAttachmentViewDescriptor: Nullable<GPUTextureViewDescriptor>;\r\n    colorAttachmentGPUTextures: (WebGPUHardwareTexture | null)[];\r\n    depthTextureFormat: GPUTextureFormat | undefined;\r\n}\r\n\r\n/**\r\n * Options to load the associated Glslang library\r\n */\r\nexport interface GlslangOptions {\r\n    /**\r\n     * Defines an existing instance of Glslang (useful in modules who do not access the global instance).\r\n     */\r\n    glslang?: any;\r\n    /**\r\n     * Defines the URL of the glslang JS File.\r\n     */\r\n    jsPath?: string;\r\n    /**\r\n     * Defines the URL of the glslang WASM File.\r\n     */\r\n    wasmPath?: string;\r\n}\r\n\r\n/**\r\n * Options to create the WebGPU engine\r\n */\r\nexport interface WebGPUEngineOptions extends ThinEngineOptions, GPURequestAdapterOptions {\r\n    /**\r\n     * Defines the category of adapter to use.\r\n     * Is it the discrete or integrated device.\r\n     */\r\n    powerPreference?: GPUPowerPreference;\r\n\r\n    /**\r\n     * When set to true, indicates that only a fallback adapter may be returned when requesting an adapter.\r\n     * If the user agent does not support a fallback adapter, will cause requestAdapter() to resolve to null.\r\n     * Default: false\r\n     */\r\n    forceFallbackAdapter?: boolean;\r\n\r\n    /**\r\n     * Defines the device descriptor used to create a device once we have retrieved an appropriate adapter\r\n     */\r\n    deviceDescriptor?: GPUDeviceDescriptor;\r\n\r\n    /**\r\n     * When requesting the device, enable all the features supported by the adapter. Default: false\r\n     * Note that this setting is ignored if you explicitely set deviceDescriptor.requiredFeatures\r\n     */\r\n    enableAllFeatures?: boolean;\r\n\r\n    /**\r\n     * When requesting the device, set the required limits to the maximum possible values (the ones from adapter.limits). Default: false\r\n     * Note that this setting is ignored if you explicitely set deviceDescriptor.requiredLimits\r\n     */\r\n    setMaximumLimits?: boolean;\r\n\r\n    /**\r\n     * Defines the requested Swap Chain Format.\r\n     */\r\n    swapChainFormat?: GPUTextureFormat;\r\n\r\n    /**\r\n     * Defines whether we should generate debug markers in the gpu command lists (can be seen with PIX for eg). Default: false\r\n     */\r\n    enableGPUDebugMarkers?: boolean;\r\n\r\n    /**\r\n     * Options to load the associated Glslang library\r\n     */\r\n    glslangOptions?: GlslangOptions;\r\n\r\n    /**\r\n     * Options to load the associated Twgsl library\r\n     */\r\n    twgslOptions?: TwgslOptions;\r\n}\r\n\r\n/**\r\n * The web GPU engine class provides support for WebGPU version of babylon.js.\r\n * @since 5.0.0\r\n */\r\nexport class WebGPUEngine extends Engine {\r\n    // Default glslang options.\r\n    private static readonly _GLSLslangDefaultOptions: GlslangOptions = {\r\n        jsPath: `${Tools._DefaultCdnUrl}/glslang/glslang.js`,\r\n        wasmPath: `${Tools._DefaultCdnUrl}/glslang/glslang.wasm`,\r\n    };\r\n\r\n    private static _InstanceId = 0;\r\n\r\n    /** true to enable using TintWASM to convert Spir-V to WGSL */\r\n    public static UseTWGSL = true;\r\n\r\n    /** A unique id to identify this instance */\r\n    public readonly uniqueId = -1;\r\n\r\n    // Page Life cycle and constants\r\n    private readonly _uploadEncoderDescriptor = { label: \"upload\" };\r\n    private readonly _renderEncoderDescriptor = { label: \"render\" };\r\n    /** @internal */\r\n    public readonly _clearDepthValue = 1;\r\n    /** @internal */\r\n    public readonly _clearReverseDepthValue = 0;\r\n    /** @internal */\r\n    public readonly _clearStencilValue = 0;\r\n    private readonly _defaultSampleCount = 4; // Only supported value for now.\r\n\r\n    // Engine Life Cycle\r\n    /** @internal */\r\n    public _options: WebGPUEngineOptions;\r\n    private _glslang: any = null;\r\n    private _tintWASM: Nullable<WebGPUTintWASM> = null;\r\n    private _adapter: GPUAdapter;\r\n    private _adapterSupportedExtensions: GPUFeatureName[];\r\n    private _adapterInfo: GPUAdapterInfo = {\r\n        vendor: \"\",\r\n        architecture: \"\",\r\n        device: \"\",\r\n        description: \"\",\r\n    };\r\n    private _adapterSupportedLimits: GPUSupportedLimits;\r\n    /** @internal */\r\n    public _device: GPUDevice;\r\n    private _deviceEnabledExtensions: GPUFeatureName[];\r\n    private _deviceLimits: GPUSupportedLimits;\r\n    private _context: GPUCanvasContext;\r\n    private _mainPassSampleCount: number;\r\n    private _glslangOptions?: GlslangOptions;\r\n    private _twgslOptions?: TwgslOptions;\r\n    /** @internal */\r\n    public _textureHelper: WebGPUTextureManager;\r\n    /** @internal */\r\n    public _bufferManager: WebGPUBufferManager;\r\n    private _clearQuad: WebGPUClearQuad;\r\n    /** @internal */\r\n    public _cacheSampler: WebGPUCacheSampler;\r\n    /** @internal */\r\n    public _cacheRenderPipeline: WebGPUCacheRenderPipeline;\r\n    private _cacheBindGroups: WebGPUCacheBindGroups;\r\n    private _emptyVertexBuffer: VertexBuffer;\r\n    /** @internal */\r\n    public _mrtAttachments: number[];\r\n    /** @internal */\r\n    public _timestampQuery: WebGPUTimestampQuery;\r\n    /** @internal */\r\n    public _timestampIndex = 0;\r\n    /** @internal */\r\n    public _occlusionQuery: WebGPUOcclusionQuery;\r\n    /** @internal */\r\n    public _compiledComputeEffects: { [key: string]: ComputeEffect } = {};\r\n    /** @internal */\r\n    public _counters: {\r\n        numEnableEffects: number;\r\n        numEnableDrawWrapper: number;\r\n        numBundleCreationNonCompatMode: number;\r\n        numBundleReuseNonCompatMode: number;\r\n    } = {\r\n        numEnableEffects: 0,\r\n        numEnableDrawWrapper: 0,\r\n        numBundleCreationNonCompatMode: 0,\r\n        numBundleReuseNonCompatMode: 0,\r\n    };\r\n    /**\r\n     * Counters from last frame\r\n     */\r\n    public readonly countersLastFrame: {\r\n        numEnableEffects: number;\r\n        numEnableDrawWrapper: number;\r\n        numBundleCreationNonCompatMode: number;\r\n        numBundleReuseNonCompatMode: number;\r\n    } = {\r\n        numEnableEffects: 0,\r\n        numEnableDrawWrapper: 0,\r\n        numBundleCreationNonCompatMode: 0,\r\n        numBundleReuseNonCompatMode: 0,\r\n    };\r\n    /**\r\n     * Max number of uncaptured error messages to log\r\n     */\r\n    public numMaxUncapturedErrors = 20;\r\n\r\n    // Some of the internal state might change during the render pass.\r\n    // This happens mainly during clear for the state\r\n    // And when the frame starts to swap the target texture from the swap chain\r\n    private _mainTexture: GPUTexture;\r\n    private _depthTexture: GPUTexture;\r\n    private _mainTextureExtends: GPUExtent3D;\r\n    private _depthTextureFormat: GPUTextureFormat | undefined;\r\n    private _colorFormat: GPUTextureFormat | null;\r\n    /** @internal */\r\n    public _ubInvertY: WebGPUDataBuffer;\r\n    /** @internal */\r\n    public _ubDontInvertY: WebGPUDataBuffer;\r\n\r\n    // Frame Life Cycle (recreated each frame)\r\n    /** @internal */\r\n    public _uploadEncoder: GPUCommandEncoder;\r\n    /** @internal */\r\n    public _renderEncoder: GPUCommandEncoder;\r\n\r\n    private _commandBuffers: GPUCommandBuffer[] = [null as any, null as any];\r\n\r\n    // Frame Buffer Life Cycle (recreated for each render target pass)\r\n    /** @internal */\r\n    public _currentRenderPass: Nullable<GPURenderPassEncoder> = null;\r\n    private _mainRenderPassWrapper: IWebGPURenderPassWrapper = {\r\n        renderPassDescriptor: null,\r\n        colorAttachmentViewDescriptor: null,\r\n        depthAttachmentViewDescriptor: null,\r\n        colorAttachmentGPUTextures: [],\r\n        depthTextureFormat: undefined,\r\n    };\r\n    private _rttRenderPassWrapper: IWebGPURenderPassWrapper = {\r\n        renderPassDescriptor: null,\r\n        colorAttachmentViewDescriptor: null,\r\n        depthAttachmentViewDescriptor: null,\r\n        colorAttachmentGPUTextures: [],\r\n        depthTextureFormat: undefined,\r\n    };\r\n    /** @internal */\r\n    public _pendingDebugCommands: Array<[string, Nullable<string>]> = [];\r\n    /**\r\n     * Used for both the compatibilityMode=false and the snapshot rendering modes (as both can't be enabled at the same time)\r\n     * @internal\r\n     */\r\n    public _bundleList: WebGPUBundleList;\r\n\r\n    // DrawCall Life Cycle\r\n    // Effect is on the parent class\r\n    // protected _currentEffect: Nullable<Effect> = null;\r\n    private _defaultDrawContext: WebGPUDrawContext;\r\n    private _defaultMaterialContext: WebGPUMaterialContext;\r\n    /** @internal */\r\n    public _currentDrawContext: WebGPUDrawContext;\r\n    /** @internal */\r\n    public _currentMaterialContext: WebGPUMaterialContext;\r\n    private _currentOverrideVertexBuffers: Nullable<{ [key: string]: Nullable<VertexBuffer> }> = null;\r\n    private _currentIndexBuffer: Nullable<DataBuffer> = null;\r\n    private _colorWriteLocal = true;\r\n    private _forceEnableEffect = false;\r\n\r\n    // TODO WEBGPU remove those variables when code stabilized\r\n    /** @internal */\r\n    public dbgShowShaderCode = false;\r\n    /** @internal */\r\n    public dbgSanityChecks = true;\r\n    /** @internal */\r\n    public dbgVerboseLogsForFirstFrames = false;\r\n    /** @internal */\r\n    public dbgVerboseLogsNumFrames = 10;\r\n    /** @internal */\r\n    public dbgLogIfNotDrawWrapper = true;\r\n    /** @internal */\r\n    public dbgShowEmptyEnableEffectCalls = true;\r\n\r\n    private _snapshotRendering: WebGPUSnapshotRendering;\r\n\r\n    /**\r\n     * Gets or sets the snapshot rendering mode\r\n     */\r\n    public get snapshotRenderingMode(): number {\r\n        return this._snapshotRendering.mode;\r\n    }\r\n\r\n    public set snapshotRenderingMode(mode: number) {\r\n        this._snapshotRendering.mode = mode;\r\n    }\r\n\r\n    /**\r\n     * Creates a new snapshot at the next frame using the current snapshotRenderingMode\r\n     */\r\n    public snapshotRenderingReset(): void {\r\n        this._snapshotRendering.reset();\r\n    }\r\n\r\n    /**\r\n     * Enables or disables the snapshot rendering mode\r\n     * Note that the WebGL engine does not support snapshot rendering so setting the value won't have any effect for this engine\r\n     */\r\n    public get snapshotRendering(): boolean {\r\n        return this._snapshotRendering.enabled;\r\n    }\r\n\r\n    public set snapshotRendering(activate) {\r\n        this._snapshotRendering.enabled = activate;\r\n    }\r\n\r\n    /**\r\n     * Sets this to true to disable the cache for the samplers. You should do it only for testing purpose!\r\n     */\r\n    public get disableCacheSamplers(): boolean {\r\n        return this._cacheSampler ? this._cacheSampler.disabled : false;\r\n    }\r\n\r\n    public set disableCacheSamplers(disable: boolean) {\r\n        if (this._cacheSampler) {\r\n            this._cacheSampler.disabled = disable;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets this to true to disable the cache for the render pipelines. You should do it only for testing purpose!\r\n     */\r\n    public get disableCacheRenderPipelines(): boolean {\r\n        return this._cacheRenderPipeline ? this._cacheRenderPipeline.disabled : false;\r\n    }\r\n\r\n    public set disableCacheRenderPipelines(disable: boolean) {\r\n        if (this._cacheRenderPipeline) {\r\n            this._cacheRenderPipeline.disabled = disable;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets this to true to disable the cache for the bind groups. You should do it only for testing purpose!\r\n     */\r\n    public get disableCacheBindGroups(): boolean {\r\n        return this._cacheBindGroups ? this._cacheBindGroups.disabled : false;\r\n    }\r\n\r\n    public set disableCacheBindGroups(disable: boolean) {\r\n        if (this._cacheBindGroups) {\r\n            this._cacheBindGroups.disabled = disable;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a Promise<boolean> indicating if the engine can be instantiated (ie. if a WebGPU context can be found)\r\n     */\r\n    public static get IsSupportedAsync(): Promise<boolean> {\r\n        return !navigator.gpu\r\n            ? Promise.resolve(false)\r\n            : navigator.gpu\r\n                  .requestAdapter()\r\n                  .then(\r\n                      (adapter: GPUAdapter | undefined) => !!adapter,\r\n                      () => false\r\n                  )\r\n                  .catch(() => false);\r\n    }\r\n\r\n    /**\r\n     * Not supported by WebGPU, you should call IsSupportedAsync instead!\r\n     */\r\n    public static get IsSupported(): boolean {\r\n        Logger.Warn(\"You must call IsSupportedAsync for WebGPU!\");\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the engine supports uniform buffers\r\n     */\r\n    public get supportsUniformBuffers(): boolean {\r\n        return true;\r\n    }\r\n\r\n    /** Gets the supported extensions by the WebGPU adapter */\r\n    public get supportedExtensions(): Immutable<GPUFeatureName[]> {\r\n        return this._adapterSupportedExtensions;\r\n    }\r\n\r\n    /** Gets the currently enabled extensions on the WebGPU device */\r\n    public get enabledExtensions(): Immutable<GPUFeatureName[]> {\r\n        return this._deviceEnabledExtensions;\r\n    }\r\n\r\n    /** Gets the supported limits by the WebGPU adapter */\r\n    public get supportedLimits(): GPUSupportedLimits {\r\n        return this._adapterSupportedLimits;\r\n    }\r\n\r\n    /** Gets the current limits of the WebGPU device */\r\n    public get currentLimits() {\r\n        return this._deviceLimits;\r\n    }\r\n\r\n    /**\r\n     * Returns a string describing the current engine\r\n     */\r\n    public get description(): string {\r\n        const description = this.name + this.version;\r\n\r\n        return description;\r\n    }\r\n\r\n    /**\r\n     * Returns the version of the engine\r\n     */\r\n    public get version(): number {\r\n        return 1;\r\n    }\r\n\r\n    /**\r\n     * Gets an object containing information about the current engine context\r\n     * @returns an object containing the vendor, the renderer and the version of the current engine context\r\n     */\r\n    public getInfo() {\r\n        return {\r\n            vendor: this._adapterInfo.vendor || \"unknown vendor\",\r\n            renderer: this._adapterInfo.architecture || \"unknown renderer\",\r\n            version: this._adapterInfo.description || \"unknown version\",\r\n        };\r\n    }\r\n\r\n    /**\r\n     * (WebGPU only) True (default) to be in compatibility mode, meaning rendering all existing scenes without artifacts (same rendering than WebGL).\r\n     * Setting the property to false will improve performances but may not work in some scenes if some precautions are not taken.\r\n     * See https://doc.babylonjs.com/setup/support/webGPU/webGPUOptimization/webGPUNonCompatibilityMode for more details\r\n     */\r\n    public get compatibilityMode() {\r\n        return this._compatibilityMode;\r\n    }\r\n\r\n    public set compatibilityMode(mode: boolean) {\r\n        this._compatibilityMode = mode;\r\n    }\r\n\r\n    /**\r\n     * Enables or disables GPU timing measurements.\r\n     * Note that this is only supported if the \"timestamp-query\" extension is enabled in the options.\r\n     */\r\n    public get enableGPUTimingMeasurements(): boolean {\r\n        return this._timestampQuery.enable;\r\n    }\r\n\r\n    public set enableGPUTimingMeasurements(enable: boolean) {\r\n        if (this._timestampQuery.enable === enable) {\r\n            return;\r\n        }\r\n        (this.gpuTimeInFrameForMainPass as any) = enable ? new WebGPUPerfCounter() : undefined;\r\n        this._timestampQuery.enable = enable;\r\n    }\r\n\r\n    /**\r\n     * Gets the GPU time spent in the main render pass for the last frame rendered (in nanoseconds).\r\n     * You have to enable the \"timestamp-query\" extension in the engine constructor options and set engine.enableGPUTimingMeasurements = true.\r\n     * It will only return time spent in the main pass, not additional render target / compute passes (if any)!\r\n     */\r\n    public readonly gpuTimeInFrameForMainPass?: WebGPUPerfCounter;\r\n\r\n    /** @internal */\r\n    public get currentSampleCount(): number {\r\n        return this._currentRenderTarget ? this._currentRenderTarget.samples : this._mainPassSampleCount;\r\n    }\r\n\r\n    /**\r\n     * Create a new instance of the gpu engine asynchronously\r\n     * @param canvas Defines the canvas to use to display the result\r\n     * @param options Defines the options passed to the engine to create the GPU context dependencies\r\n     * @returns a promise that resolves with the created engine\r\n     */\r\n    public static CreateAsync(canvas: HTMLCanvasElement, options: WebGPUEngineOptions = {}): Promise<WebGPUEngine> {\r\n        const engine = new WebGPUEngine(canvas, options);\r\n\r\n        return new Promise((resolve) => {\r\n            engine.initAsync(options.glslangOptions, options.twgslOptions).then(() => resolve(engine));\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Indicates if the z range in NDC space is 0..1 (value: true) or -1..1 (value: false)\r\n     */\r\n    public readonly isNDCHalfZRange: boolean = true;\r\n\r\n    /**\r\n     * Indicates that the origin of the texture/framebuffer space is the bottom left corner. If false, the origin is top left\r\n     */\r\n    public readonly hasOriginBottomLeft: boolean = false;\r\n\r\n    /**\r\n     * Create a new instance of the gpu engine.\r\n     * @param canvas Defines the canvas to use to display the result\r\n     * @param options Defines the options passed to the engine to create the GPU context dependencies\r\n     */\r\n    public constructor(canvas: HTMLCanvasElement | OffscreenCanvas, options: WebGPUEngineOptions = {}) {\r\n        super(null, options.antialias ?? true, options);\r\n        this._name = \"WebGPU\";\r\n\r\n        options.deviceDescriptor = options.deviceDescriptor || {};\r\n        options.enableGPUDebugMarkers = options.enableGPUDebugMarkers ?? false;\r\n\r\n        Logger.Log(`Babylon.js v${Engine.Version} - ${this.description} engine`);\r\n        if (!navigator.gpu) {\r\n            Logger.Error(\"WebGPU is not supported by your browser.\");\r\n            return;\r\n        }\r\n\r\n        options.swapChainFormat = options.swapChainFormat || navigator.gpu.getPreferredCanvasFormat();\r\n\r\n        this._isWebGPU = true;\r\n        this._shaderPlatformName = \"WEBGPU\";\r\n\r\n        this._renderingCanvas = canvas as HTMLCanvasElement;\r\n        this._options = options;\r\n\r\n        this._mainPassSampleCount = options.antialias ? this._defaultSampleCount : 1;\r\n\r\n        this._setupMobileChecks();\r\n\r\n        this._sharedInit(this._renderingCanvas);\r\n\r\n        this._shaderProcessor = new WebGPUShaderProcessorGLSL();\r\n        this._shaderProcessorWGSL = new WebGPUShaderProcessorWGSL();\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Initialization\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Initializes the WebGPU context and dependencies.\r\n     * @param glslangOptions Defines the GLSLang compiler options if necessary\r\n     * @param twgslOptions Defines the Twgsl compiler options if necessary\r\n     * @returns a promise notifying the readiness of the engine.\r\n     */\r\n    public initAsync(glslangOptions?: GlslangOptions, twgslOptions?: TwgslOptions): Promise<void> {\r\n        (this.uniqueId as number) = WebGPUEngine._InstanceId++;\r\n        this._glslangOptions = glslangOptions;\r\n        this._twgslOptions = twgslOptions;\r\n        return this._initGlslang(glslangOptions ?? this._options?.glslangOptions)\r\n            .then((glslang: any) => {\r\n                this._glslang = glslang;\r\n                this._tintWASM = WebGPUEngine.UseTWGSL ? new WebGPUTintWASM() : null;\r\n                return this._tintWASM\r\n                    ? this._tintWASM.initTwgsl(twgslOptions ?? this._options?.twgslOptions).then(() => {\r\n                          return navigator.gpu!.requestAdapter(this._options);\r\n                      })\r\n                    : navigator.gpu!.requestAdapter(this._options);\r\n            })\r\n            .then((adapter: GPUAdapter | undefined) => {\r\n                if (!adapter) {\r\n                    // eslint-disable-next-line no-throw-literal\r\n                    throw \"Could not retrieve a WebGPU adapter (adapter is null).\";\r\n                } else {\r\n                    this._adapter = adapter!;\r\n                    this._adapterSupportedExtensions = [];\r\n                    this._adapter.features?.forEach((feature) => this._adapterSupportedExtensions.push(feature as WebGPUConstants.FeatureName));\r\n                    this._adapterSupportedLimits = this._adapter.limits;\r\n\r\n                    this._adapter.requestAdapterInfo().then((adapterInfo) => {\r\n                        this._adapterInfo = adapterInfo;\r\n                    });\r\n\r\n                    const deviceDescriptor = this._options.deviceDescriptor ?? {};\r\n                    const requiredFeatures = deviceDescriptor?.requiredFeatures ?? (this._options.enableAllFeatures ? this._adapterSupportedExtensions : undefined);\r\n\r\n                    if (requiredFeatures) {\r\n                        const requestedExtensions = requiredFeatures;\r\n                        const validExtensions: GPUFeatureName[] = [];\r\n\r\n                        for (const extension of requestedExtensions) {\r\n                            if (this._adapterSupportedExtensions.indexOf(extension) !== -1) {\r\n                                validExtensions.push(extension);\r\n                            }\r\n                        }\r\n\r\n                        deviceDescriptor.requiredFeatures = validExtensions;\r\n                    }\r\n\r\n                    if (this._options.setMaximumLimits && !deviceDescriptor.requiredLimits) {\r\n                        deviceDescriptor.requiredLimits = {};\r\n                        for (const name in this._adapterSupportedLimits) {\r\n                            if (name === \"minSubgroupSize\" || name === \"maxSubgroupSize\") {\r\n                                // Chrome exposes these limits in \"webgpu developer\" mode, but these can't be set on the device.\r\n                                continue;\r\n                            }\r\n                            deviceDescriptor.requiredLimits[name] = this._adapterSupportedLimits[name];\r\n                        }\r\n                    }\r\n\r\n                    deviceDescriptor.label = `BabylonWebGPUDevice${this.uniqueId}`;\r\n\r\n                    return this._adapter.requestDevice(deviceDescriptor);\r\n                }\r\n            })\r\n            .then((device: GPUDevice) => {\r\n                this._device = device;\r\n                this._deviceEnabledExtensions = [];\r\n                this._device.features?.forEach((feature) => this._deviceEnabledExtensions.push(feature as WebGPUConstants.FeatureName));\r\n                this._deviceLimits = device.limits;\r\n\r\n                let numUncapturedErrors = -1;\r\n                this._device.addEventListener(\"uncapturederror\", (event) => {\r\n                    if (++numUncapturedErrors < this.numMaxUncapturedErrors) {\r\n                        Logger.Warn(`WebGPU uncaptured error (${numUncapturedErrors + 1}): ${(<GPUUncapturedErrorEvent>event).error} - ${(<any>event).error.message}`);\r\n                    } else if (numUncapturedErrors++ === this.numMaxUncapturedErrors) {\r\n                        Logger.Warn(\r\n                            `WebGPU uncaptured error: too many warnings (${this.numMaxUncapturedErrors}), no more warnings will be reported to the console for this engine.`\r\n                        );\r\n                    }\r\n                });\r\n\r\n                if (!this._doNotHandleContextLost) {\r\n                    this._device.lost?.then((info) => {\r\n                        if (this._isDisposed) {\r\n                            return;\r\n                        }\r\n                        this._contextWasLost = true;\r\n                        Logger.Warn(\"WebGPU context lost. \" + info);\r\n                        this.onContextLostObservable.notifyObservers(this);\r\n                        this._restoreEngineAfterContextLost(async () => {\r\n                            const snapshotRenderingMode = this.snapshotRenderingMode;\r\n                            const snapshotRendering = this.snapshotRendering;\r\n                            const disableCacheSamplers = this.disableCacheSamplers;\r\n                            const disableCacheRenderPipelines = this.disableCacheRenderPipelines;\r\n                            const disableCacheBindGroups = this.disableCacheBindGroups;\r\n                            const enableGPUTimingMeasurements = this.enableGPUTimingMeasurements;\r\n\r\n                            await this.initAsync(this._glslangOptions ?? this._options?.glslangOptions, this._twgslOptions ?? this._options?.twgslOptions);\r\n\r\n                            this.snapshotRenderingMode = snapshotRenderingMode;\r\n                            this.snapshotRendering = snapshotRendering;\r\n                            this.disableCacheSamplers = disableCacheSamplers;\r\n                            this.disableCacheRenderPipelines = disableCacheRenderPipelines;\r\n                            this.disableCacheBindGroups = disableCacheBindGroups;\r\n                            this.enableGPUTimingMeasurements = enableGPUTimingMeasurements;\r\n                            this._currentRenderPass = null;\r\n                        });\r\n                    });\r\n                }\r\n            })\r\n            .then(() => {\r\n                this._bufferManager = new WebGPUBufferManager(this, this._device);\r\n                this._textureHelper = new WebGPUTextureManager(this, this._device, this._glslang, this._tintWASM, this._bufferManager, this._deviceEnabledExtensions);\r\n                this._cacheSampler = new WebGPUCacheSampler(this._device);\r\n                this._cacheBindGroups = new WebGPUCacheBindGroups(this._device, this._cacheSampler, this);\r\n                this._timestampQuery = new WebGPUTimestampQuery(this, this._device, this._bufferManager);\r\n                this._occlusionQuery = (this._device as any).createQuerySet ? new WebGPUOcclusionQuery(this, this._device, this._bufferManager) : (undefined as any);\r\n                this._bundleList = new WebGPUBundleList(this._device);\r\n                this._snapshotRendering = new WebGPUSnapshotRendering(this, this._snapshotRenderingMode, this._bundleList);\r\n\r\n                this._ubInvertY = this._bufferManager.createBuffer(\r\n                    new Float32Array([-1, 0]),\r\n                    WebGPUConstants.BufferUsage.Uniform | WebGPUConstants.BufferUsage.CopyDst,\r\n                    \"UBInvertY\"\r\n                );\r\n                this._ubDontInvertY = this._bufferManager.createBuffer(\r\n                    new Float32Array([1, 0]),\r\n                    WebGPUConstants.BufferUsage.Uniform | WebGPUConstants.BufferUsage.CopyDst,\r\n                    \"UBDontInvertY\"\r\n                );\r\n\r\n                if (this.dbgVerboseLogsForFirstFrames) {\r\n                    if ((this as any)._count === undefined) {\r\n                        (this as any)._count = 0;\r\n                        Logger.Log([\"%c frame #\" + (this as any)._count + \" - begin\", \"background: #ffff00\"]);\r\n                    }\r\n                }\r\n\r\n                this._uploadEncoder = this._device.createCommandEncoder(this._uploadEncoderDescriptor);\r\n                this._renderEncoder = this._device.createCommandEncoder(this._renderEncoderDescriptor);\r\n\r\n                this._initializeLimits();\r\n\r\n                this._emptyVertexBuffer = new VertexBuffer(this, [0], \"\", {\r\n                    stride: 1,\r\n                    offset: 0,\r\n                    size: 1,\r\n                    label: \"EmptyVertexBuffer\",\r\n                });\r\n\r\n                this._cacheRenderPipeline = new WebGPUCacheRenderPipelineTree(this._device, this._emptyVertexBuffer);\r\n\r\n                this._depthCullingState = new WebGPUDepthCullingState(this._cacheRenderPipeline);\r\n                this._stencilStateComposer = new WebGPUStencilStateComposer(this._cacheRenderPipeline);\r\n                this._stencilStateComposer.stencilGlobal = this._stencilState;\r\n\r\n                this._depthCullingState.depthTest = true;\r\n                this._depthCullingState.depthFunc = Constants.LEQUAL;\r\n                this._depthCullingState.depthMask = true;\r\n\r\n                this._textureHelper.setCommandEncoder(this._uploadEncoder);\r\n\r\n                this._clearQuad = new WebGPUClearQuad(this._device, this, this._emptyVertexBuffer);\r\n                this._defaultDrawContext = this.createDrawContext()!;\r\n                this._currentDrawContext = this._defaultDrawContext;\r\n                this._defaultMaterialContext = this.createMaterialContext()!;\r\n                this._currentMaterialContext = this._defaultMaterialContext;\r\n\r\n                this._initializeContextAndSwapChain();\r\n                this._initializeMainAttachments();\r\n                this.resize();\r\n            })\r\n            .catch((e: any) => {\r\n                Logger.Error(\"A fatal error occurred during WebGPU creation/initialization.\");\r\n                throw e;\r\n            });\r\n    }\r\n\r\n    private _initGlslang(glslangOptions?: GlslangOptions): Promise<any> {\r\n        glslangOptions = glslangOptions || {};\r\n        glslangOptions = {\r\n            ...WebGPUEngine._GLSLslangDefaultOptions,\r\n            ...glslangOptions,\r\n        };\r\n\r\n        if (glslangOptions.glslang) {\r\n            return Promise.resolve(glslangOptions.glslang);\r\n        }\r\n\r\n        if ((self as any).glslang) {\r\n            return (self as any).glslang(glslangOptions!.wasmPath);\r\n        }\r\n\r\n        if (glslangOptions.jsPath && glslangOptions.wasmPath) {\r\n            return Tools.LoadBabylonScriptAsync(glslangOptions.jsPath).then(() => {\r\n                return (self as any).glslang(Tools.GetBabylonScriptURL(glslangOptions!.wasmPath!));\r\n            });\r\n        }\r\n\r\n        return Promise.reject(\"gslang is not available.\");\r\n    }\r\n\r\n    private _initializeLimits(): void {\r\n        // Init caps\r\n        // TODO WEBGPU Real Capability check once limits will be working.\r\n\r\n        this._caps = {\r\n            maxTexturesImageUnits: this._deviceLimits.maxSampledTexturesPerShaderStage,\r\n            maxVertexTextureImageUnits: this._deviceLimits.maxSampledTexturesPerShaderStage,\r\n            maxCombinedTexturesImageUnits: this._deviceLimits.maxSampledTexturesPerShaderStage * 2,\r\n            maxTextureSize: this._deviceLimits.maxTextureDimension2D,\r\n            maxCubemapTextureSize: this._deviceLimits.maxTextureDimension2D,\r\n            maxRenderTextureSize: this._deviceLimits.maxTextureDimension2D,\r\n            maxVertexAttribs: this._deviceLimits.maxVertexAttributes,\r\n            maxVaryingVectors: this._deviceLimits.maxInterStageShaderVariables,\r\n            maxFragmentUniformVectors: Math.floor(this._deviceLimits.maxUniformBufferBindingSize / 4),\r\n            maxVertexUniformVectors: Math.floor(this._deviceLimits.maxUniformBufferBindingSize / 4),\r\n            standardDerivatives: true,\r\n            astc: (this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.TextureCompressionASTC) >= 0 ? true : undefined) as any,\r\n            s3tc: (this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.TextureCompressionBC) >= 0 ? true : undefined) as any,\r\n            pvrtc: null,\r\n            etc1: null,\r\n            etc2: (this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.TextureCompressionETC2) >= 0 ? true : undefined) as any,\r\n            bptc: this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.TextureCompressionBC) >= 0 ? true : undefined,\r\n            maxAnisotropy: 16, // Most implementations support maxAnisotropy values in range between 1 and 16, inclusive. The used value of maxAnisotropy will be clamped to the maximum value that the platform supports.\r\n            uintIndices: true,\r\n            fragmentDepthSupported: true,\r\n            highPrecisionShaderSupported: true,\r\n            colorBufferFloat: true,\r\n            supportFloatTexturesResolve: false, // See https://github.com/gpuweb/gpuweb/issues/3844\r\n            rg11b10ufColorRenderable: this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.RG11B10UFloatRenderable) >= 0,\r\n            textureFloat: true,\r\n            textureFloatLinearFiltering: this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.Float32Filterable) >= 0,\r\n            textureFloatRender: true,\r\n            textureHalfFloat: true,\r\n            textureHalfFloatLinearFiltering: true,\r\n            textureHalfFloatRender: true,\r\n            textureLOD: true,\r\n            texelFetch: true,\r\n            drawBuffersExtension: true,\r\n            depthTextureExtension: true,\r\n            vertexArrayObject: false,\r\n            instancedArrays: true,\r\n            timerQuery:\r\n                typeof BigUint64Array !== \"undefined\" && this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.TimestampQuery) !== -1 ? (true as any) : undefined,\r\n            supportOcclusionQuery: typeof BigUint64Array !== \"undefined\",\r\n            canUseTimestampForTimerQuery: true,\r\n            multiview: false,\r\n            oculusMultiview: false,\r\n            parallelShaderCompile: undefined,\r\n            blendMinMax: true,\r\n            maxMSAASamples: 4, // the spec only supports values of 1 and 4\r\n            canUseGLInstanceID: true,\r\n            canUseGLVertexID: true,\r\n            supportComputeShaders: true,\r\n            supportSRGBBuffers: true,\r\n            supportTransformFeedbacks: false,\r\n            textureMaxLevel: true,\r\n            texture2DArrayMaxLayerCount: this._deviceLimits.maxTextureArrayLayers,\r\n            disableMorphTargetTexture: false,\r\n        };\r\n\r\n        this._features = {\r\n            forceBitmapOverHTMLImageElement: true,\r\n            supportRenderAndCopyToLodForFloatTextures: true,\r\n            supportDepthStencilTexture: true,\r\n            supportShadowSamplers: true,\r\n            uniformBufferHardCheckMatrix: false,\r\n            allowTexturePrefiltering: true,\r\n            trackUbosInFrame: true,\r\n            checkUbosContentBeforeUpload: true,\r\n            supportCSM: true,\r\n            basisNeedsPOT: false,\r\n            support3DTextures: true,\r\n            needTypeSuffixInShaderConstants: true,\r\n            supportMSAA: true,\r\n            supportSSAO2: true,\r\n            supportExtendedTextureFormats: true,\r\n            supportSwitchCaseInShader: true,\r\n            supportSyncTextureRead: false,\r\n            needsInvertingBitmap: false,\r\n            useUBOBindingCache: false,\r\n            needShaderCodeInlining: true,\r\n            needToAlwaysBindUniformBuffers: true,\r\n            supportRenderPasses: true,\r\n            supportSpriteInstancing: true,\r\n            forceVertexBufferStrideAndOffsetMultiple4Bytes: true,\r\n            _collectUbosUpdatedInFrame: false,\r\n        };\r\n    }\r\n\r\n    private _initializeContextAndSwapChain(): void {\r\n        if (!this._renderingCanvas) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"The rendering canvas has not been set!\";\r\n        }\r\n        this._context = this._renderingCanvas.getContext(\"webgpu\") as unknown as GPUCanvasContext;\r\n        this._configureContext();\r\n        this._colorFormat = this._options.swapChainFormat!;\r\n        this._mainRenderPassWrapper.colorAttachmentGPUTextures = [new WebGPUHardwareTexture()];\r\n        this._mainRenderPassWrapper.colorAttachmentGPUTextures[0]!.format = this._colorFormat;\r\n        this._setColorFormat(this._mainRenderPassWrapper);\r\n    }\r\n\r\n    // Set default values as WebGL with depth and stencil attachment for the broadest Compat.\r\n    private _initializeMainAttachments(): void {\r\n        if (!this._bufferManager) {\r\n            return;\r\n        }\r\n\r\n        this.flushFramebuffer();\r\n\r\n        this._mainTextureExtends = {\r\n            width: this.getRenderWidth(true),\r\n            height: this.getRenderHeight(true),\r\n            depthOrArrayLayers: 1,\r\n        };\r\n\r\n        const bufferDataUpdate = new Float32Array([this.getRenderHeight(true)]);\r\n\r\n        this._bufferManager.setSubData(this._ubInvertY, 4, bufferDataUpdate);\r\n        this._bufferManager.setSubData(this._ubDontInvertY, 4, bufferDataUpdate);\r\n\r\n        let mainColorAttachments: GPURenderPassColorAttachment[];\r\n\r\n        if (this._options.antialias) {\r\n            const mainTextureDescriptor: GPUTextureDescriptor = {\r\n                label: `Texture_MainColor_${this._mainTextureExtends.width}x${this._mainTextureExtends.height}_antialiasing`,\r\n                size: this._mainTextureExtends,\r\n                mipLevelCount: 1,\r\n                sampleCount: this._mainPassSampleCount,\r\n                dimension: WebGPUConstants.TextureDimension.E2d,\r\n                format: this._options.swapChainFormat!,\r\n                usage: WebGPUConstants.TextureUsage.RenderAttachment,\r\n            };\r\n\r\n            if (this._mainTexture) {\r\n                this._textureHelper.releaseTexture(this._mainTexture);\r\n            }\r\n            this._mainTexture = this._device.createTexture(mainTextureDescriptor);\r\n            mainColorAttachments = [\r\n                {\r\n                    view: this._mainTexture.createView({\r\n                        label: \"TextureView_MainColor_antialiasing\",\r\n                        dimension: WebGPUConstants.TextureDimension.E2d,\r\n                        format: this._options.swapChainFormat!,\r\n                        mipLevelCount: 1,\r\n                        arrayLayerCount: 1,\r\n                    }),\r\n                    clearValue: new Color4(0, 0, 0, 1),\r\n                    loadOp: WebGPUConstants.LoadOp.Clear,\r\n                    storeOp: WebGPUConstants.StoreOp.Store, // don't use StoreOp.Discard, else using several cameras with different viewports or using scissors will fail because we call beginRenderPass / endPass several times for the same color attachment!\r\n                },\r\n            ];\r\n        } else {\r\n            mainColorAttachments = [\r\n                {\r\n                    view: undefined as any,\r\n                    clearValue: new Color4(0, 0, 0, 1),\r\n                    loadOp: WebGPUConstants.LoadOp.Clear,\r\n                    storeOp: WebGPUConstants.StoreOp.Store,\r\n                },\r\n            ];\r\n        }\r\n\r\n        this._mainRenderPassWrapper.depthTextureFormat = this.isStencilEnable ? WebGPUConstants.TextureFormat.Depth24PlusStencil8 : WebGPUConstants.TextureFormat.Depth32Float;\r\n\r\n        this._setDepthTextureFormat(this._mainRenderPassWrapper);\r\n        this._setColorFormat(this._mainRenderPassWrapper);\r\n\r\n        const depthTextureDescriptor: GPUTextureDescriptor = {\r\n            label: `Texture_MainDepthStencil_${this._mainTextureExtends.width}x${this._mainTextureExtends.height}`,\r\n            size: this._mainTextureExtends,\r\n            mipLevelCount: 1,\r\n            sampleCount: this._mainPassSampleCount,\r\n            dimension: WebGPUConstants.TextureDimension.E2d,\r\n            format: this._mainRenderPassWrapper.depthTextureFormat,\r\n            usage: WebGPUConstants.TextureUsage.RenderAttachment,\r\n        };\r\n\r\n        if (this._depthTexture) {\r\n            this._textureHelper.releaseTexture(this._depthTexture);\r\n        }\r\n        this._depthTexture = this._device.createTexture(depthTextureDescriptor);\r\n        const mainDepthAttachment: GPURenderPassDepthStencilAttachment = {\r\n            view: this._depthTexture.createView({\r\n                label: `TextureView_MainDepthStencil_${this._mainTextureExtends.width}x${this._mainTextureExtends.height}`,\r\n                dimension: WebGPUConstants.TextureDimension.E2d,\r\n                format: this._depthTexture.format,\r\n                mipLevelCount: 1,\r\n                arrayLayerCount: 1,\r\n            }),\r\n\r\n            depthClearValue: this._clearDepthValue,\r\n            depthLoadOp: WebGPUConstants.LoadOp.Clear,\r\n            depthStoreOp: WebGPUConstants.StoreOp.Store,\r\n            stencilClearValue: this._clearStencilValue,\r\n            stencilLoadOp: !this.isStencilEnable ? undefined : WebGPUConstants.LoadOp.Clear,\r\n            stencilStoreOp: !this.isStencilEnable ? undefined : WebGPUConstants.StoreOp.Store,\r\n        };\r\n\r\n        this._mainRenderPassWrapper.renderPassDescriptor = {\r\n            label: \"MainRenderPass\",\r\n            colorAttachments: mainColorAttachments,\r\n            depthStencilAttachment: mainDepthAttachment,\r\n        };\r\n    }\r\n\r\n    private _configureContext(): void {\r\n        this._context.configure({\r\n            device: this._device,\r\n            format: this._options.swapChainFormat!,\r\n            usage: WebGPUConstants.TextureUsage.RenderAttachment | WebGPUConstants.TextureUsage.CopySrc,\r\n            alphaMode: this.premultipliedAlpha ? WebGPUConstants.CanvasAlphaMode.Premultiplied : WebGPUConstants.CanvasAlphaMode.Opaque,\r\n        });\r\n    }\r\n\r\n    protected _rebuildBuffers(): void {\r\n        super._rebuildBuffers();\r\n\r\n        for (const storageBuffer of this._storageBuffers) {\r\n            // The buffer can already be rebuilt by the call to _rebuildGeometries(), which recreates the storage buffers for the ComputeShaderParticleSystem\r\n            if ((storageBuffer.getBuffer() as WebGPUDataBuffer).engineId !== this.uniqueId) {\r\n                storageBuffer._rebuild();\r\n            }\r\n        }\r\n    }\r\n\r\n    protected _restoreEngineAfterContextLost(initEngine: () => void) {\r\n        WebGPUCacheRenderPipelineTree.ResetCache();\r\n        WebGPUCacheBindGroups.ResetCache();\r\n\r\n        // Clear the draw wrappers and material contexts\r\n        const cleanScenes = (scenes: Scene[]) => {\r\n            for (const scene of scenes) {\r\n                for (const mesh of scene.meshes) {\r\n                    const subMeshes = mesh.subMeshes;\r\n                    if (!subMeshes) {\r\n                        continue;\r\n                    }\r\n                    for (const subMesh of subMeshes) {\r\n                        subMesh._drawWrappers = [];\r\n                    }\r\n                }\r\n\r\n                for (const material of scene.materials) {\r\n                    material._materialContext?.reset();\r\n                }\r\n            }\r\n        };\r\n\r\n        cleanScenes(this.scenes);\r\n        cleanScenes(this._virtualScenes);\r\n\r\n        // The leftOver uniform buffers are removed from the list because they will be recreated when we rebuild the effects\r\n        const uboList: UniformBuffer[] = [];\r\n        for (const uniformBuffer of this._uniformBuffers) {\r\n            if (uniformBuffer.name.indexOf(\"leftOver\") < 0) {\r\n                uboList.push(uniformBuffer);\r\n            }\r\n        }\r\n        this._uniformBuffers = uboList;\r\n\r\n        super._restoreEngineAfterContextLost(initEngine);\r\n    }\r\n\r\n    /**\r\n     * Force a specific size of the canvas\r\n     * @param width defines the new canvas' width\r\n     * @param height defines the new canvas' height\r\n     * @param forceSetSize true to force setting the sizes of the underlying canvas\r\n     * @returns true if the size was changed\r\n     */\r\n    public setSize(width: number, height: number, forceSetSize = false): boolean {\r\n        if (!super.setSize(width, height, forceSetSize)) {\r\n            return false;\r\n        }\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\"frame #\" + (this as any)._count + \" - setSize -\", width, height]);\r\n            }\r\n        }\r\n\r\n        this._initializeMainAttachments();\r\n\r\n        if (this.snapshotRendering) {\r\n            // reset snapshot rendering so that the next frame will record a new list of bundles\r\n            this.snapshotRenderingReset();\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    private _shaderProcessorWGSL: Nullable<IShaderProcessor>;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getShaderProcessor(shaderLanguage: ShaderLanguage): Nullable<IShaderProcessor> {\r\n        if (shaderLanguage === ShaderLanguage.WGSL) {\r\n            return this._shaderProcessorWGSL;\r\n        }\r\n        return this._shaderProcessor;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getShaderProcessingContext(shaderLanguage: ShaderLanguage): Nullable<ShaderProcessingContext> {\r\n        return new WebGPUShaderProcessingContext(shaderLanguage);\r\n    }\r\n\r\n    private _currentPassIsMainPass() {\r\n        return this._currentRenderTarget === null;\r\n    }\r\n\r\n    private _getCurrentRenderPass(): GPURenderPassEncoder {\r\n        if (this._currentRenderTarget && !this._currentRenderPass) {\r\n            // delayed creation of the render target pass, but we now need to create it as we are requested the render pass\r\n            this._startRenderTargetRenderPass(this._currentRenderTarget, false, null, false, false);\r\n        } else if (!this._currentRenderPass) {\r\n            this._startMainRenderPass(false);\r\n        }\r\n\r\n        return this._currentRenderPass!;\r\n    }\r\n\r\n    /** @internal */\r\n    public _getCurrentRenderPassWrapper() {\r\n        return this._currentRenderTarget ? this._rttRenderPassWrapper : this._mainRenderPassWrapper;\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                          Static Pipeline WebGPU States\r\n    //------------------------------------------------------------------------------\r\n\r\n    /** @internal */\r\n    public applyStates() {\r\n        this._stencilStateComposer.apply();\r\n        this._cacheRenderPipeline.setAlphaBlendEnabled(this._alphaState.alphaBlend);\r\n    }\r\n\r\n    /**\r\n     * Force the entire cache to be cleared\r\n     * You should not have to use this function unless your engine needs to share the WebGPU context with another engine\r\n     * @param bruteForce defines a boolean to force clearing ALL caches (including stencil, detoh and alpha states)\r\n     */\r\n    public wipeCaches(bruteForce?: boolean): void {\r\n        if (this.preventCacheWipeBetweenFrames && !bruteForce) {\r\n            return;\r\n        }\r\n\r\n        //this._currentEffect = null; // can't reset _currentEffect, else some crashes can occur (for eg in ProceduralTexture which calls bindFrameBuffer (which calls wipeCaches) after having called enableEffect and before drawing into the texture)\r\n        // _forceEnableEffect = true assumes the role of _currentEffect = null\r\n        this._forceEnableEffect = true;\r\n        this._currentIndexBuffer = null;\r\n        this._currentOverrideVertexBuffers = null;\r\n        this._cacheRenderPipeline.setBuffers(null, null, null);\r\n\r\n        if (bruteForce) {\r\n            this._stencilStateComposer.reset();\r\n\r\n            this._depthCullingState.reset();\r\n            this._depthCullingState.depthFunc = Constants.LEQUAL;\r\n\r\n            this._alphaState.reset();\r\n            this._alphaMode = Constants.ALPHA_ADD;\r\n            this._alphaEquation = Constants.ALPHA_DISABLE;\r\n            this._cacheRenderPipeline.setAlphaBlendFactors(this._alphaState._blendFunctionParameters, this._alphaState._blendEquationParameters);\r\n            this._cacheRenderPipeline.setAlphaBlendEnabled(false);\r\n\r\n            this.setColorWrite(true);\r\n        }\r\n\r\n        this._cachedVertexBuffers = null;\r\n        this._cachedIndexBuffer = null;\r\n        this._cachedEffectForVertexBuffers = null;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable color writing\r\n     * @param enable defines the state to set\r\n     */\r\n    public setColorWrite(enable: boolean): void {\r\n        this._colorWriteLocal = enable;\r\n        this._cacheRenderPipeline.setWriteMask(enable ? 0xf : 0);\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if color writing is enabled\r\n     * @returns the current color writing state\r\n     */\r\n    public getColorWrite(): boolean {\r\n        return this._colorWriteLocal;\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Dynamic WebGPU States\r\n    //------------------------------------------------------------------------------\r\n\r\n    // index 0 is for main render pass, 1 for RTT render pass\r\n    private _viewportsCurrent: { x: number; y: number; w: number; h: number } = { x: 0, y: 0, w: 0, h: 0 };\r\n\r\n    private _mustUpdateViewport(): boolean {\r\n        const x = this._viewportCached.x,\r\n            y = this._viewportCached.y,\r\n            w = this._viewportCached.z,\r\n            h = this._viewportCached.w;\r\n\r\n        const update = this._viewportsCurrent.x !== x || this._viewportsCurrent.y !== y || this._viewportsCurrent.w !== w || this._viewportsCurrent.h !== h;\r\n\r\n        if (update) {\r\n            this._viewportsCurrent.x = this._viewportCached.x;\r\n            this._viewportsCurrent.y = this._viewportCached.y;\r\n            this._viewportsCurrent.w = this._viewportCached.z;\r\n            this._viewportsCurrent.h = this._viewportCached.w;\r\n        }\r\n\r\n        return update;\r\n    }\r\n\r\n    private _applyViewport(bundleList: Nullable<WebGPUBundleList>): void {\r\n        const x = Math.floor(this._viewportCached.x);\r\n        const w = Math.floor(this._viewportCached.z);\r\n        const h = Math.floor(this._viewportCached.w);\r\n\r\n        let y = Math.floor(this._viewportCached.y);\r\n\r\n        if (!this._currentRenderTarget) {\r\n            y = this.getRenderHeight(true) - y - h;\r\n        }\r\n\r\n        if (bundleList) {\r\n            bundleList.addItem(new WebGPURenderItemViewport(x, y, w, h));\r\n        } else {\r\n            this._getCurrentRenderPass().setViewport(x, y, w, h, 0, 1);\r\n        }\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\r\n                    \"frame #\" + (this as any)._count + \" - viewport applied - (\",\r\n                    this._viewportCached.x,\r\n                    this._viewportCached.y,\r\n                    this._viewportCached.z,\r\n                    this._viewportCached.w,\r\n                    \") current pass is main pass=\" + this._currentPassIsMainPass(),\r\n                ]);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _viewport(x: number, y: number, width: number, height: number): void {\r\n        this._viewportCached.x = x;\r\n        this._viewportCached.y = y;\r\n        this._viewportCached.z = width;\r\n        this._viewportCached.w = height;\r\n    }\r\n\r\n    private _scissorsCurrent: { x: number; y: number; w: number; h: number } = { x: 0, y: 0, w: 0, h: 0 };\r\n    protected _scissorCached = { x: 0, y: 0, z: 0, w: 0 };\r\n\r\n    private _mustUpdateScissor(): boolean {\r\n        const x = this._scissorCached.x,\r\n            y = this._scissorCached.y,\r\n            w = this._scissorCached.z,\r\n            h = this._scissorCached.w;\r\n\r\n        const update = this._scissorsCurrent.x !== x || this._scissorsCurrent.y !== y || this._scissorsCurrent.w !== w || this._scissorsCurrent.h !== h;\r\n\r\n        if (update) {\r\n            this._scissorsCurrent.x = this._scissorCached.x;\r\n            this._scissorsCurrent.y = this._scissorCached.y;\r\n            this._scissorsCurrent.w = this._scissorCached.z;\r\n            this._scissorsCurrent.h = this._scissorCached.w;\r\n        }\r\n\r\n        return update;\r\n    }\r\n\r\n    private _applyScissor(bundleList: Nullable<WebGPUBundleList>): void {\r\n        const y = this._currentRenderTarget ? this._scissorCached.y : this.getRenderHeight() - this._scissorCached.w - this._scissorCached.y;\r\n\r\n        if (bundleList) {\r\n            bundleList.addItem(new WebGPURenderItemScissor(this._scissorCached.x, y, this._scissorCached.z, this._scissorCached.w));\r\n        } else {\r\n            this._getCurrentRenderPass().setScissorRect(this._scissorCached.x, y, this._scissorCached.z, this._scissorCached.w);\r\n        }\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\r\n                    \"frame #\" + (this as any)._count + \" - scissor applied - (\",\r\n                    this._scissorCached.x,\r\n                    this._scissorCached.y,\r\n                    this._scissorCached.z,\r\n                    this._scissorCached.w,\r\n                    \") current pass is main pass=\" + this._currentPassIsMainPass(),\r\n                ]);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _scissorIsActive() {\r\n        return this._scissorCached.x !== 0 || this._scissorCached.y !== 0 || this._scissorCached.z !== 0 || this._scissorCached.w !== 0;\r\n    }\r\n\r\n    public enableScissor(x: number, y: number, width: number, height: number): void {\r\n        this._scissorCached.x = x;\r\n        this._scissorCached.y = y;\r\n        this._scissorCached.z = width;\r\n        this._scissorCached.w = height;\r\n    }\r\n\r\n    public disableScissor() {\r\n        this._scissorCached.x = this._scissorCached.y = this._scissorCached.z = this._scissorCached.w = 0;\r\n        this._scissorsCurrent.x = this._scissorsCurrent.y = this._scissorsCurrent.w = this._scissorsCurrent.h = 0;\r\n    }\r\n\r\n    private _stencilRefsCurrent = -1;\r\n\r\n    private _mustUpdateStencilRef(): boolean {\r\n        const update = this._stencilStateComposer.funcRef !== this._stencilRefsCurrent;\r\n        if (update) {\r\n            this._stencilRefsCurrent = this._stencilStateComposer.funcRef;\r\n        }\r\n        return update;\r\n    }\r\n\r\n    private _applyStencilRef(bundleList: Nullable<WebGPUBundleList>): void {\r\n        if (bundleList) {\r\n            bundleList.addItem(new WebGPURenderItemStencilRef(this._stencilStateComposer.funcRef ?? 0));\r\n        } else {\r\n            this._getCurrentRenderPass().setStencilReference(this._stencilStateComposer.funcRef ?? 0);\r\n        }\r\n    }\r\n\r\n    private _blendColorsCurrent: Array<Nullable<number>> = [null, null, null, null];\r\n\r\n    private _mustUpdateBlendColor(): boolean {\r\n        const colorBlend = this._alphaState._blendConstants;\r\n\r\n        const update =\r\n            colorBlend[0] !== this._blendColorsCurrent[0] ||\r\n            colorBlend[1] !== this._blendColorsCurrent[1] ||\r\n            colorBlend[2] !== this._blendColorsCurrent[2] ||\r\n            colorBlend[3] !== this._blendColorsCurrent[3];\r\n\r\n        if (update) {\r\n            this._blendColorsCurrent[0] = colorBlend[0];\r\n            this._blendColorsCurrent[1] = colorBlend[1];\r\n            this._blendColorsCurrent[2] = colorBlend[2];\r\n            this._blendColorsCurrent[3] = colorBlend[3];\r\n        }\r\n\r\n        return update;\r\n    }\r\n\r\n    private _applyBlendColor(bundleList: Nullable<WebGPUBundleList>): void {\r\n        if (bundleList) {\r\n            bundleList.addItem(new WebGPURenderItemBlendColor(this._alphaState._blendConstants.slice()));\r\n        } else {\r\n            this._getCurrentRenderPass().setBlendConstant(this._alphaState._blendConstants as GPUColor);\r\n        }\r\n    }\r\n\r\n    private _resetRenderPassStates() {\r\n        this._viewportsCurrent.x = this._viewportsCurrent.y = this._viewportsCurrent.w = this._viewportsCurrent.h = 0;\r\n        this._scissorsCurrent.x = this._scissorsCurrent.y = this._scissorsCurrent.w = this._scissorsCurrent.h = 0;\r\n        this._stencilRefsCurrent = -1;\r\n        this._blendColorsCurrent[0] = this._blendColorsCurrent[1] = this._blendColorsCurrent[2] = this._blendColorsCurrent[3] = null;\r\n    }\r\n\r\n    /**\r\n     * Clear the current render buffer or the current render target (if any is set up)\r\n     * @param color defines the color to use\r\n     * @param backBuffer defines if the back buffer must be cleared\r\n     * @param depth defines if the depth buffer must be cleared\r\n     * @param stencil defines if the stencil buffer must be cleared\r\n     */\r\n    public clear(color: Nullable<IColor4Like>, backBuffer: boolean, depth: boolean, stencil: boolean = false): void {\r\n        // Some PGs are using color3...\r\n        if (color && color.a === undefined) {\r\n            color.a = 1;\r\n        }\r\n\r\n        const hasScissor = this._scissorIsActive();\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\"frame #\" + (this as any)._count + \" - clear - backBuffer=\", backBuffer, \" depth=\", depth, \" stencil=\", stencil, \" scissor is active=\", hasScissor]);\r\n            }\r\n        }\r\n\r\n        // We need to recreate the render pass so that the new parameters for clear color / depth / stencil are taken into account\r\n        if (this._currentRenderTarget) {\r\n            if (hasScissor) {\r\n                if (!this._currentRenderPass) {\r\n                    this._startRenderTargetRenderPass(this._currentRenderTarget!, false, backBuffer ? color : null, depth, stencil);\r\n                }\r\n                this._applyScissor(!this.compatibilityMode ? this._bundleList : null);\r\n                this._clearFullQuad(backBuffer ? color : null, depth, stencil);\r\n            } else {\r\n                if (this._currentRenderPass) {\r\n                    this._endCurrentRenderPass();\r\n                }\r\n                this._startRenderTargetRenderPass(this._currentRenderTarget!, true, backBuffer ? color : null, depth, stencil);\r\n            }\r\n        } else {\r\n            if (!this._currentRenderPass || !hasScissor) {\r\n                this._startMainRenderPass(!hasScissor, backBuffer ? color : null, depth, stencil);\r\n            }\r\n            if (hasScissor) {\r\n                this._applyScissor(!this.compatibilityMode ? this._bundleList : null);\r\n                this._clearFullQuad(backBuffer ? color : null, depth, stencil);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _clearFullQuad(clearColor?: Nullable<IColor4Like>, clearDepth?: boolean, clearStencil?: boolean): void {\r\n        const renderPass = !this.compatibilityMode ? null : this._getCurrentRenderPass();\r\n\r\n        this._clearQuad.setColorFormat(this._colorFormat);\r\n        this._clearQuad.setDepthStencilFormat(this._depthTextureFormat);\r\n        this._clearQuad.setMRTAttachments(\r\n            this._cacheRenderPipeline.mrtAttachments ?? [],\r\n            this._cacheRenderPipeline.mrtTextureArray ?? [],\r\n            this._cacheRenderPipeline.mrtTextureCount\r\n        );\r\n\r\n        if (!this.compatibilityMode) {\r\n            this._bundleList.addItem(new WebGPURenderItemStencilRef(this._clearStencilValue));\r\n        } else {\r\n            renderPass!.setStencilReference(this._clearStencilValue);\r\n        }\r\n\r\n        const bundle = this._clearQuad.clear(renderPass, clearColor, clearDepth, clearStencil, this.currentSampleCount);\r\n\r\n        if (!this.compatibilityMode) {\r\n            this._bundleList.addBundle(bundle!);\r\n            this._applyStencilRef(this._bundleList);\r\n            this._reportDrawCall();\r\n        } else {\r\n            this._applyStencilRef(null);\r\n        }\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Vertex/Index/Storage Buffers\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Creates a vertex buffer\r\n     * @param data the data or the size for the vertex buffer\r\n     * @param _updatable whether the buffer should be created as updatable\r\n     * @param label defines the label of the buffer (for debug purpose)\r\n     * @returns the new buffer\r\n     */\r\n    public createVertexBuffer(data: DataArray | number, _updatable?: boolean, label?: string): DataBuffer {\r\n        let view: ArrayBufferView | number;\r\n\r\n        if (data instanceof Array) {\r\n            view = new Float32Array(data);\r\n        } else if (data instanceof ArrayBuffer) {\r\n            view = new Uint8Array(data);\r\n        } else {\r\n            view = data;\r\n        }\r\n\r\n        const dataBuffer = this._bufferManager.createBuffer(view, WebGPUConstants.BufferUsage.Vertex | WebGPUConstants.BufferUsage.CopyDst, label);\r\n        return dataBuffer;\r\n    }\r\n\r\n    /**\r\n     * Creates a vertex buffer\r\n     * @param data the data for the dynamic vertex buffer\r\n     * @param label defines the label of the buffer (for debug purpose)\r\n     * @returns the new buffer\r\n     */\r\n    public createDynamicVertexBuffer(data: DataArray, label?: string): DataBuffer {\r\n        return this.createVertexBuffer(data, undefined, label);\r\n    }\r\n\r\n    /**\r\n     * Creates a new index buffer\r\n     * @param indices defines the content of the index buffer\r\n     * @param _updatable defines if the index buffer must be updatable\r\n     * @param label defines the label of the buffer (for debug purpose)\r\n     * @returns a new buffer\r\n     */\r\n    public createIndexBuffer(indices: IndicesArray, _updatable?: boolean, label?: string): DataBuffer {\r\n        let is32Bits = true;\r\n        let view: ArrayBufferView;\r\n\r\n        if (indices instanceof Uint32Array || indices instanceof Int32Array) {\r\n            view = indices;\r\n        } else if (indices instanceof Uint16Array) {\r\n            view = indices;\r\n            is32Bits = false;\r\n        } else {\r\n            if (indices.length > 65535) {\r\n                view = new Uint32Array(indices);\r\n            } else {\r\n                view = new Uint16Array(indices);\r\n                is32Bits = false;\r\n            }\r\n        }\r\n\r\n        const dataBuffer = this._bufferManager.createBuffer(view, WebGPUConstants.BufferUsage.Index | WebGPUConstants.BufferUsage.CopyDst, label);\r\n        dataBuffer.is32Bits = is32Bits;\r\n        return dataBuffer;\r\n    }\r\n\r\n    /**\r\n     * Update a dynamic index buffer\r\n     * @param indexBuffer defines the target index buffer\r\n     * @param indices defines the data to update\r\n     * @param offset defines the offset in the target index buffer where update should start\r\n     */\r\n    public updateDynamicIndexBuffer(indexBuffer: DataBuffer, indices: IndicesArray, offset: number = 0): void {\r\n        const gpuBuffer = indexBuffer as WebGPUDataBuffer;\r\n\r\n        let view: ArrayBufferView;\r\n        if (indexBuffer.is32Bits) {\r\n            view = indices instanceof Uint32Array ? indices : new Uint32Array(indices);\r\n        } else {\r\n            view = indices instanceof Uint16Array ? indices : new Uint16Array(indices);\r\n        }\r\n\r\n        this._bufferManager.setSubData(gpuBuffer, offset, view);\r\n    }\r\n\r\n    /**\r\n     * Updates a dynamic vertex buffer.\r\n     * @param vertexBuffer the vertex buffer to update\r\n     * @param data the data used to update the vertex buffer\r\n     * @param byteOffset the byte offset of the data\r\n     * @param byteLength the byte length of the data\r\n     */\r\n    public updateDynamicVertexBuffer(vertexBuffer: DataBuffer, data: DataArray, byteOffset?: number, byteLength?: number): void {\r\n        const dataBuffer = vertexBuffer as WebGPUDataBuffer;\r\n        if (byteOffset === undefined) {\r\n            byteOffset = 0;\r\n        }\r\n\r\n        let view: ArrayBufferView;\r\n        if (byteLength === undefined) {\r\n            if (data instanceof Array) {\r\n                view = new Float32Array(data);\r\n            } else if (data instanceof ArrayBuffer) {\r\n                view = new Uint8Array(data);\r\n            } else {\r\n                view = data;\r\n            }\r\n            byteLength = view.byteLength;\r\n        } else {\r\n            if (data instanceof Array) {\r\n                view = new Float32Array(data);\r\n            } else if (data instanceof ArrayBuffer) {\r\n                view = new Uint8Array(data);\r\n            } else {\r\n                view = data;\r\n            }\r\n        }\r\n\r\n        this._bufferManager.setSubData(dataBuffer, byteOffset, view, 0, byteLength);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _createBuffer(data: DataArray | number, creationFlags: number, label?: string): DataBuffer {\r\n        let view: ArrayBufferView | number;\r\n\r\n        if (data instanceof Array) {\r\n            view = new Float32Array(data);\r\n        } else if (data instanceof ArrayBuffer) {\r\n            view = new Uint8Array(data);\r\n        } else {\r\n            view = data;\r\n        }\r\n\r\n        let flags = 0;\r\n        if (creationFlags & Constants.BUFFER_CREATIONFLAG_READ) {\r\n            flags |= WebGPUConstants.BufferUsage.CopySrc;\r\n        }\r\n        if (creationFlags & Constants.BUFFER_CREATIONFLAG_WRITE) {\r\n            flags |= WebGPUConstants.BufferUsage.CopyDst;\r\n        }\r\n        if (creationFlags & Constants.BUFFER_CREATIONFLAG_UNIFORM) {\r\n            flags |= WebGPUConstants.BufferUsage.Uniform;\r\n        }\r\n        if (creationFlags & Constants.BUFFER_CREATIONFLAG_VERTEX) {\r\n            flags |= WebGPUConstants.BufferUsage.Vertex;\r\n        }\r\n        if (creationFlags & Constants.BUFFER_CREATIONFLAG_INDEX) {\r\n            flags |= WebGPUConstants.BufferUsage.Index;\r\n        }\r\n        if (creationFlags & Constants.BUFFER_CREATIONFLAG_STORAGE) {\r\n            flags |= WebGPUConstants.BufferUsage.Storage;\r\n        }\r\n\r\n        return this._bufferManager.createBuffer(view, flags, label);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public bindBuffersDirectly(): void {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"Not implemented on WebGPU\";\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public updateAndBindInstancesBuffer(): void {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"Not implemented on WebGPU\";\r\n    }\r\n\r\n    /**\r\n     * Bind a list of vertex buffers with the engine\r\n     * @param vertexBuffers defines the list of vertex buffers to bind\r\n     * @param indexBuffer defines the index buffer to bind\r\n     * @param effect defines the effect associated with the vertex buffers\r\n     * @param overrideVertexBuffers defines optional list of avertex buffers that overrides the entries in vertexBuffers\r\n     */\r\n    public bindBuffers(\r\n        vertexBuffers: { [key: string]: Nullable<VertexBuffer> },\r\n        indexBuffer: Nullable<DataBuffer>,\r\n        effect: Effect,\r\n        overrideVertexBuffers?: { [kind: string]: Nullable<VertexBuffer> }\r\n    ): void {\r\n        this._currentIndexBuffer = indexBuffer;\r\n        this._currentOverrideVertexBuffers = overrideVertexBuffers ?? null;\r\n        this._cacheRenderPipeline.setBuffers(vertexBuffers, indexBuffer, this._currentOverrideVertexBuffers);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseBuffer(buffer: DataBuffer): boolean {\r\n        return this._bufferManager.releaseBuffer(buffer);\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Uniform Buffers\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Create an uniform buffer\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n     * @param elements defines the content of the uniform buffer\r\n     * @param label defines a name for the buffer (for debugging purpose)\r\n     * @returns the webGL uniform buffer\r\n     */\r\n    public createUniformBuffer(elements: FloatArray, label?: string): DataBuffer {\r\n        let view: Float32Array;\r\n        if (elements instanceof Array) {\r\n            view = new Float32Array(elements);\r\n        } else {\r\n            view = elements;\r\n        }\r\n\r\n        const dataBuffer = this._bufferManager.createBuffer(view, WebGPUConstants.BufferUsage.Uniform | WebGPUConstants.BufferUsage.CopyDst, label);\r\n        return dataBuffer;\r\n    }\r\n\r\n    /**\r\n     * Create a dynamic uniform buffer (no different from a non dynamic uniform buffer in WebGPU)\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n     * @param elements defines the content of the uniform buffer\r\n     * @param label defines a name for the buffer (for debugging purpose)\r\n     * @returns the webGL uniform buffer\r\n     */\r\n    public createDynamicUniformBuffer(elements: FloatArray, label?: string): DataBuffer {\r\n        return this.createUniformBuffer(elements, label);\r\n    }\r\n\r\n    /**\r\n     * Update an existing uniform buffer\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n     * @param uniformBuffer defines the target uniform buffer\r\n     * @param elements defines the content to update\r\n     * @param offset defines the offset in the uniform buffer where update should start\r\n     * @param count defines the size of the data to update\r\n     */\r\n    public updateUniformBuffer(uniformBuffer: DataBuffer, elements: FloatArray, offset?: number, count?: number): void {\r\n        if (offset === undefined) {\r\n            offset = 0;\r\n        }\r\n\r\n        const dataBuffer = uniformBuffer as WebGPUDataBuffer;\r\n        let view: Float32Array;\r\n        if (count === undefined) {\r\n            if (elements instanceof Float32Array) {\r\n                view = elements;\r\n            } else {\r\n                view = new Float32Array(elements);\r\n            }\r\n            count = view.byteLength;\r\n        } else {\r\n            if (elements instanceof Float32Array) {\r\n                view = elements;\r\n            } else {\r\n                view = new Float32Array(elements);\r\n            }\r\n        }\r\n\r\n        this._bufferManager.setSubData(dataBuffer, offset, view, 0, count);\r\n    }\r\n\r\n    /**\r\n     * Bind a buffer to the current draw context\r\n     * @param buffer defines the buffer to bind\r\n     * @param _location not used in WebGPU\r\n     * @param name Name of the uniform variable to bind\r\n     */\r\n    public bindUniformBufferBase(buffer: DataBuffer, _location: number, name: string): void {\r\n        this._currentDrawContext.setBuffer(name, buffer as WebGPUDataBuffer);\r\n    }\r\n\r\n    /**\r\n     * Unused in WebGPU\r\n     */\r\n    public bindUniformBlock(): void {}\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Effects\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Create a new effect (used to store vertex/fragment shaders)\r\n     * @param baseName defines the base name of the effect (The name of file without .fragment.fx or .vertex.fx)\r\n     * @param attributesNamesOrOptions defines either a list of attribute names or an IEffectCreationOptions object\r\n     * @param uniformsNamesOrEngine defines either a list of uniform names or the engine to use\r\n     * @param samplers defines an array of string used to represent textures\r\n     * @param defines defines the string containing the defines to use to compile the shaders\r\n     * @param fallbacks defines the list of potential fallbacks to use if shader compilation fails\r\n     * @param onCompiled defines a function to call when the effect creation is successful\r\n     * @param onError defines a function to call when the effect creation has failed\r\n     * @param indexParameters defines an object containing the index values to use to compile shaders (like the maximum number of simultaneous lights)\r\n     * @param shaderLanguage the language the shader is written in (default: GLSL)\r\n     * @returns the new Effect\r\n     */\r\n    public createEffect(\r\n        baseName: any,\r\n        attributesNamesOrOptions: string[] | IEffectCreationOptions,\r\n        uniformsNamesOrEngine: string[] | Engine,\r\n        samplers?: string[],\r\n        defines?: string,\r\n        fallbacks?: EffectFallbacks,\r\n        onCompiled?: Nullable<(effect: Effect) => void>,\r\n        onError?: Nullable<(effect: Effect, errors: string) => void>,\r\n        indexParameters?: any,\r\n        shaderLanguage = ShaderLanguage.GLSL\r\n    ): Effect {\r\n        const vertex = baseName.vertexElement || baseName.vertex || baseName.vertexToken || baseName.vertexSource || baseName;\r\n        const fragment = baseName.fragmentElement || baseName.fragment || baseName.fragmentToken || baseName.fragmentSource || baseName;\r\n        const globalDefines = this._getGlobalDefines()!;\r\n\r\n        let fullDefines = defines ?? (<IEffectCreationOptions>attributesNamesOrOptions).defines ?? \"\";\r\n\r\n        if (globalDefines) {\r\n            fullDefines += \"\\n\" + globalDefines;\r\n        }\r\n\r\n        const name = vertex + \"+\" + fragment + \"@\" + fullDefines;\r\n        if (this._compiledEffects[name]) {\r\n            const compiledEffect = <Effect>this._compiledEffects[name];\r\n            if (onCompiled && compiledEffect.isReady()) {\r\n                onCompiled(compiledEffect);\r\n            }\r\n\r\n            return compiledEffect;\r\n        }\r\n        const effect = new Effect(\r\n            baseName,\r\n            attributesNamesOrOptions,\r\n            uniformsNamesOrEngine,\r\n            samplers,\r\n            this,\r\n            defines,\r\n            fallbacks,\r\n            onCompiled,\r\n            onError,\r\n            indexParameters,\r\n            name,\r\n            shaderLanguage\r\n        );\r\n        this._compiledEffects[name] = effect;\r\n\r\n        return effect;\r\n    }\r\n\r\n    private _compileRawShaderToSpirV(source: string, type: string): Uint32Array {\r\n        return this._glslang.compileGLSL(source, type);\r\n    }\r\n\r\n    private _compileShaderToSpirV(source: string, type: string, defines: Nullable<string>, shaderVersion: string): Uint32Array {\r\n        return this._compileRawShaderToSpirV(shaderVersion + (defines ? defines + \"\\n\" : \"\") + source, type);\r\n    }\r\n\r\n    private _getWGSLShader(source: string, type: string, defines: Nullable<string>): string {\r\n        if (defines) {\r\n            defines = \"//\" + defines.split(\"\\n\").join(\"\\n//\") + \"\\n\";\r\n        } else {\r\n            defines = \"\";\r\n        }\r\n        return defines + source;\r\n    }\r\n\r\n    private _createPipelineStageDescriptor(\r\n        vertexShader: Uint32Array | string,\r\n        fragmentShader: Uint32Array | string,\r\n        shaderLanguage: ShaderLanguage,\r\n        disableUniformityAnalysisInVertex: boolean,\r\n        disableUniformityAnalysisInFragment: boolean\r\n    ): IWebGPURenderPipelineStageDescriptor {\r\n        if (this._tintWASM && shaderLanguage === ShaderLanguage.GLSL) {\r\n            vertexShader = this._tintWASM.convertSpirV2WGSL(vertexShader as Uint32Array, disableUniformityAnalysisInVertex);\r\n            fragmentShader = this._tintWASM.convertSpirV2WGSL(fragmentShader as Uint32Array, disableUniformityAnalysisInFragment);\r\n        }\r\n\r\n        return {\r\n            vertexStage: {\r\n                module: this._device.createShaderModule({\r\n                    code: vertexShader,\r\n                }),\r\n                entryPoint: \"main\",\r\n            },\r\n            fragmentStage: {\r\n                module: this._device.createShaderModule({\r\n                    code: fragmentShader,\r\n                }),\r\n                entryPoint: \"main\",\r\n            },\r\n        };\r\n    }\r\n\r\n    private _compileRawPipelineStageDescriptor(vertexCode: string, fragmentCode: string, shaderLanguage: ShaderLanguage): IWebGPURenderPipelineStageDescriptor {\r\n        const disableUniformityAnalysisInVertex = vertexCode.indexOf(disableUniformityAnalysisMarker) >= 0;\r\n        const disableUniformityAnalysisInFragment = fragmentCode.indexOf(disableUniformityAnalysisMarker) >= 0;\r\n\r\n        const vertexShader = shaderLanguage === ShaderLanguage.GLSL ? this._compileRawShaderToSpirV(vertexCode, \"vertex\") : vertexCode;\r\n        const fragmentShader = shaderLanguage === ShaderLanguage.GLSL ? this._compileRawShaderToSpirV(fragmentCode, \"fragment\") : fragmentCode;\r\n\r\n        return this._createPipelineStageDescriptor(vertexShader, fragmentShader, shaderLanguage, disableUniformityAnalysisInVertex, disableUniformityAnalysisInFragment);\r\n    }\r\n\r\n    private _compilePipelineStageDescriptor(\r\n        vertexCode: string,\r\n        fragmentCode: string,\r\n        defines: Nullable<string>,\r\n        shaderLanguage: ShaderLanguage\r\n    ): IWebGPURenderPipelineStageDescriptor {\r\n        this.onBeforeShaderCompilationObservable.notifyObservers(this);\r\n\r\n        const disableUniformityAnalysisInVertex = vertexCode.indexOf(disableUniformityAnalysisMarker) >= 0;\r\n        const disableUniformityAnalysisInFragment = fragmentCode.indexOf(disableUniformityAnalysisMarker) >= 0;\r\n\r\n        const shaderVersion = \"#version 450\\n\";\r\n        const vertexShader =\r\n            shaderLanguage === ShaderLanguage.GLSL ? this._compileShaderToSpirV(vertexCode, \"vertex\", defines, shaderVersion) : this._getWGSLShader(vertexCode, \"vertex\", defines);\r\n        const fragmentShader =\r\n            shaderLanguage === ShaderLanguage.GLSL\r\n                ? this._compileShaderToSpirV(fragmentCode, \"fragment\", defines, shaderVersion)\r\n                : this._getWGSLShader(fragmentCode, \"fragment\", defines);\r\n\r\n        const program = this._createPipelineStageDescriptor(vertexShader, fragmentShader, shaderLanguage, disableUniformityAnalysisInVertex, disableUniformityAnalysisInFragment);\r\n\r\n        this.onAfterShaderCompilationObservable.notifyObservers(this);\r\n\r\n        return program;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public createRawShaderProgram(): WebGLProgram {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"Not available on WebGPU\";\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public createShaderProgram(): WebGLProgram {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"Not available on WebGPU\";\r\n    }\r\n\r\n    /**\r\n     * Inline functions in shader code that are marked to be inlined\r\n     * @param code code to inline\r\n     * @returns inlined code\r\n     */\r\n    public inlineShaderCode(code: string): string {\r\n        const sci = new ShaderCodeInliner(code);\r\n        sci.debug = false;\r\n        sci.processCode();\r\n        return sci.code;\r\n    }\r\n\r\n    /**\r\n     * Creates a new pipeline context\r\n     * @param shaderProcessingContext defines the shader processing context used during the processing if available\r\n     * @returns the new pipeline\r\n     */\r\n    public createPipelineContext(shaderProcessingContext: Nullable<ShaderProcessingContext>): IPipelineContext {\r\n        return new WebGPUPipelineContext(shaderProcessingContext! as WebGPUShaderProcessingContext, this);\r\n    }\r\n\r\n    /**\r\n     * Creates a new material context\r\n     * @returns the new context\r\n     */\r\n    public createMaterialContext(): WebGPUMaterialContext | undefined {\r\n        return new WebGPUMaterialContext();\r\n    }\r\n\r\n    /**\r\n     * Creates a new draw context\r\n     * @returns the new context\r\n     */\r\n    public createDrawContext(): WebGPUDrawContext | undefined {\r\n        return new WebGPUDrawContext(this._bufferManager);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _preparePipelineContext(\r\n        pipelineContext: IPipelineContext,\r\n        vertexSourceCode: string,\r\n        fragmentSourceCode: string,\r\n        createAsRaw: boolean,\r\n        rawVertexSourceCode: string,\r\n        rawFragmentSourceCode: string,\r\n        rebuildRebind: any,\r\n        defines: Nullable<string>\r\n    ) {\r\n        const webGpuContext = pipelineContext as WebGPUPipelineContext;\r\n        const shaderLanguage = webGpuContext.shaderProcessingContext.shaderLanguage;\r\n\r\n        if (this.dbgShowShaderCode) {\r\n            Logger.Log([\"defines\", defines]);\r\n            Logger.Log(vertexSourceCode);\r\n            Logger.Log(fragmentSourceCode);\r\n            Logger.Log(\"***********************************************\");\r\n        }\r\n\r\n        webGpuContext.sources = {\r\n            fragment: fragmentSourceCode,\r\n            vertex: vertexSourceCode,\r\n            rawVertex: rawVertexSourceCode,\r\n            rawFragment: rawFragmentSourceCode,\r\n        };\r\n\r\n        if (createAsRaw) {\r\n            webGpuContext.stages = this._compileRawPipelineStageDescriptor(vertexSourceCode, fragmentSourceCode, shaderLanguage);\r\n        } else {\r\n            webGpuContext.stages = this._compilePipelineStageDescriptor(vertexSourceCode, fragmentSourceCode, defines, shaderLanguage);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the list of active attributes for a given WebGPU program\r\n     * @param pipelineContext defines the pipeline context to use\r\n     * @param attributesNames defines the list of attribute names to get\r\n     * @returns an array of indices indicating the offset of each attribute\r\n     */\r\n    public getAttributes(pipelineContext: IPipelineContext, attributesNames: string[]): number[] {\r\n        const results = new Array(attributesNames.length);\r\n        const gpuPipelineContext = pipelineContext as WebGPUPipelineContext;\r\n\r\n        for (let i = 0; i < attributesNames.length; i++) {\r\n            const attributeName = attributesNames[i];\r\n            const attributeLocation = gpuPipelineContext.shaderProcessingContext.availableAttributes[attributeName];\r\n            if (attributeLocation === undefined) {\r\n                continue;\r\n            }\r\n\r\n            results[i] = attributeLocation;\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Activates an effect, making it the current one (ie. the one used for rendering)\r\n     * @param effect defines the effect to activate\r\n     */\r\n    public enableEffect(effect: Nullable<Effect | DrawWrapper>): void {\r\n        if (!effect) {\r\n            return;\r\n        }\r\n\r\n        if (!DrawWrapper.IsWrapper(effect)) {\r\n            this._currentEffect = effect;\r\n            this._currentMaterialContext = this._defaultMaterialContext;\r\n            this._currentDrawContext = this._defaultDrawContext;\r\n            this._counters.numEnableEffects++;\r\n            if (this.dbgLogIfNotDrawWrapper) {\r\n                Logger.Warn(\r\n                    `enableEffect has been called with an Effect and not a Wrapper! effect.uniqueId=${effect.uniqueId}, effect.name=${effect.name}, effect.name.vertex=${effect.name.vertex}, effect.name.fragment=${effect.name.fragment}`,\r\n                    10\r\n                );\r\n            }\r\n        } else if (\r\n            !effect.effect ||\r\n            (effect.effect === this._currentEffect &&\r\n                effect.materialContext === this._currentMaterialContext &&\r\n                effect.drawContext === this._currentDrawContext &&\r\n                !this._forceEnableEffect)\r\n        ) {\r\n            if (!effect.effect && this.dbgShowEmptyEnableEffectCalls) {\r\n                Logger.Log([\"drawWrapper=\", effect]);\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"Invalid call to enableEffect: the effect property is empty!\";\r\n            }\r\n            return;\r\n        } else {\r\n            this._currentEffect = effect.effect;\r\n            this._currentMaterialContext = effect.materialContext as WebGPUMaterialContext;\r\n            this._currentDrawContext = effect.drawContext as WebGPUDrawContext;\r\n            this._counters.numEnableDrawWrapper++;\r\n            if (!this._currentMaterialContext) {\r\n                Logger.Log([\"drawWrapper=\", effect]);\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw `Invalid call to enableEffect: the materialContext property is empty!`;\r\n            }\r\n        }\r\n\r\n        this._stencilStateComposer.stencilMaterial = undefined;\r\n\r\n        this._forceEnableEffect = false;\r\n\r\n        if (this._currentEffect!.onBind) {\r\n            this._currentEffect!.onBind(this._currentEffect!);\r\n        }\r\n        if (this._currentEffect!._onBindObservable) {\r\n            this._currentEffect!._onBindObservable.notifyObservers(this._currentEffect!);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseEffect(effect: Effect): void {\r\n        if (this._compiledEffects[effect._key]) {\r\n            delete this._compiledEffects[effect._key];\r\n\r\n            this._deletePipelineContext(effect.getPipelineContext() as WebGPUPipelineContext);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Force the engine to release all cached effects. This means that next effect compilation will have to be done completely even if a similar effect was already compiled\r\n     */\r\n    public releaseEffects() {\r\n        for (const name in this._compiledEffects) {\r\n            const webGPUPipelineContext = this._compiledEffects[name].getPipelineContext() as WebGPUPipelineContext;\r\n            this._deletePipelineContext(webGPUPipelineContext);\r\n        }\r\n\r\n        this._compiledEffects = {};\r\n    }\r\n\r\n    public _deletePipelineContext(pipelineContext: IPipelineContext): void {\r\n        const webgpuPipelineContext = pipelineContext as WebGPUPipelineContext;\r\n        if (webgpuPipelineContext) {\r\n            pipelineContext.dispose();\r\n        }\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Textures\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Gets a boolean indicating that only power of 2 textures are supported\r\n     * Please note that you can still use non power of 2 textures but in this case the engine will forcefully convert them\r\n     */\r\n    public get needPOTTextures(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /** @internal */\r\n    public _createHardwareTexture(): HardwareTextureWrapper {\r\n        return new WebGPUHardwareTexture();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseTexture(texture: InternalTexture): void {\r\n        const index = this._internalTexturesCache.indexOf(texture);\r\n        if (index !== -1) {\r\n            this._internalTexturesCache.splice(index, 1);\r\n        }\r\n\r\n        this._textureHelper.releaseTexture(texture);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getRGBABufferInternalSizedFormat(): number {\r\n        return Constants.TEXTUREFORMAT_RGBA;\r\n    }\r\n\r\n    public updateTextureComparisonFunction(texture: InternalTexture, comparisonFunction: number): void {\r\n        texture._comparisonFunction = comparisonFunction;\r\n    }\r\n\r\n    /**\r\n     * Creates an internal texture without binding it to a framebuffer\r\n     * @internal\r\n     * @param size defines the size of the texture\r\n     * @param options defines the options used to create the texture\r\n     * @param delayGPUTextureCreation true to delay the texture creation the first time it is really needed. false to create it right away\r\n     * @param source source type of the texture\r\n     * @returns a new internal texture\r\n     */\r\n    public _createInternalTexture(\r\n        size: TextureSize,\r\n        options: boolean | InternalTextureCreationOptions,\r\n        delayGPUTextureCreation = true,\r\n        source = InternalTextureSource.Unknown\r\n    ): InternalTexture {\r\n        const fullOptions: InternalTextureCreationOptions = {};\r\n\r\n        if (options !== undefined && typeof options === \"object\") {\r\n            fullOptions.generateMipMaps = options.generateMipMaps;\r\n            fullOptions.type = options.type === undefined ? Constants.TEXTURETYPE_UNSIGNED_INT : options.type;\r\n            fullOptions.samplingMode = options.samplingMode === undefined ? Constants.TEXTURE_TRILINEAR_SAMPLINGMODE : options.samplingMode;\r\n            fullOptions.format = options.format === undefined ? Constants.TEXTUREFORMAT_RGBA : options.format;\r\n            fullOptions.samples = options.samples ?? 1;\r\n            fullOptions.creationFlags = options.creationFlags ?? 0;\r\n            fullOptions.useSRGBBuffer = options.useSRGBBuffer ?? false;\r\n            fullOptions.label = options.label;\r\n        } else {\r\n            fullOptions.generateMipMaps = <boolean>options;\r\n            fullOptions.type = Constants.TEXTURETYPE_UNSIGNED_INT;\r\n            fullOptions.samplingMode = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE;\r\n            fullOptions.format = Constants.TEXTUREFORMAT_RGBA;\r\n            fullOptions.samples = 1;\r\n            fullOptions.creationFlags = 0;\r\n            fullOptions.useSRGBBuffer = false;\r\n        }\r\n\r\n        if (fullOptions.type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n            fullOptions.samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        } else if (fullOptions.type === Constants.TEXTURETYPE_HALF_FLOAT && !this._caps.textureHalfFloatLinearFiltering) {\r\n            fullOptions.samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        }\r\n        if (fullOptions.type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloat) {\r\n            fullOptions.type = Constants.TEXTURETYPE_UNSIGNED_INT;\r\n            Logger.Warn(\"Float textures are not supported. Type forced to TEXTURETYPE_UNSIGNED_BYTE\");\r\n        }\r\n\r\n        const texture = new InternalTexture(this, source);\r\n\r\n        const width = (<{ width: number; height: number; layers?: number }>size).width || <number>size;\r\n        const height = (<{ width: number; height: number; layers?: number }>size).height || <number>size;\r\n        const layers = (<{ width: number; height: number; layers?: number }>size).layers || 0;\r\n\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.width = width;\r\n        texture.height = height;\r\n        texture.depth = layers;\r\n        texture.isReady = true;\r\n        texture.samples = fullOptions.samples;\r\n        texture.generateMipMaps = fullOptions.generateMipMaps ? true : false;\r\n        texture.samplingMode = fullOptions.samplingMode;\r\n        texture.type = fullOptions.type;\r\n        texture.format = fullOptions.format;\r\n        texture.is2DArray = layers > 0;\r\n        texture._cachedWrapU = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n        texture._cachedWrapV = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n        texture._useSRGBBuffer = fullOptions.useSRGBBuffer;\r\n        texture.label = fullOptions.label;\r\n\r\n        this._internalTexturesCache.push(texture);\r\n\r\n        if (!delayGPUTextureCreation) {\r\n            this._textureHelper.createGPUTextureForInternalTexture(texture, width, height, layers || 1, fullOptions.creationFlags);\r\n        }\r\n\r\n        return texture;\r\n    }\r\n\r\n    /**\r\n     * Usually called from Texture.ts.\r\n     * Passed information to create a hardware texture\r\n     * @param url defines a value which contains one of the following:\r\n     * * A conventional http URL, e.g. 'http://...' or 'file://...'\r\n     * * A base64 string of in-line texture data, e.g. 'data:image/jpg;base64,/...'\r\n     * * An indicator that data being passed using the buffer parameter, e.g. 'data:mytexture.jpg'\r\n     * @param noMipmap defines a boolean indicating that no mipmaps shall be generated.  Ignored for compressed textures.  They must be in the file\r\n     * @param invertY when true, image is flipped when loaded.  You probably want true. Certain compressed textures may invert this if their default is inverted (eg. ktx)\r\n     * @param scene needed for loading to the correct scene\r\n     * @param samplingMode mode with should be used sample / access the texture (Default: Texture.TRILINEAR_SAMPLINGMODE)\r\n     * @param onLoad optional callback to be called upon successful completion\r\n     * @param onError optional callback to be called upon failure\r\n     * @param buffer a source of a file previously fetched as either a base64 string, an ArrayBuffer (compressed or image format), HTMLImageElement (image format), or a Blob\r\n     * @param fallback an internal argument in case the function must be called again, due to etc1 not having alpha capabilities\r\n     * @param format internal format.  Default: RGB when extension is '.jpg' else RGBA.  Ignored for compressed textures\r\n     * @param forcedExtension defines the extension to use to pick the right loader\r\n     * @param mimeType defines an optional mime type\r\n     * @param loaderOptions options to be passed to the loader\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n     * @returns a InternalTexture for assignment back into BABYLON.Texture\r\n     */\r\n    public createTexture(\r\n        url: Nullable<string>,\r\n        noMipmap: boolean,\r\n        invertY: boolean,\r\n        scene: Nullable<ISceneLike>,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        onLoad: Nullable<(texture: InternalTexture) => void> = null,\r\n        onError: Nullable<(message: string, exception: any) => void> = null,\r\n        buffer: Nullable<string | ArrayBuffer | ArrayBufferView | HTMLImageElement | Blob | ImageBitmap> = null,\r\n        fallback: Nullable<InternalTexture> = null,\r\n        format: Nullable<number> = null,\r\n        forcedExtension: Nullable<string> = null,\r\n        mimeType?: string,\r\n        loaderOptions?: any,\r\n        creationFlags?: number,\r\n        useSRGBBuffer?: boolean\r\n    ): InternalTexture {\r\n        return this._createTextureBase(\r\n            url,\r\n            noMipmap,\r\n            invertY,\r\n            scene,\r\n            samplingMode,\r\n            onLoad,\r\n            onError,\r\n            (\r\n                texture: InternalTexture,\r\n                extension: string,\r\n                scene: Nullable<ISceneLike>,\r\n                img: HTMLImageElement | ImageBitmap | { width: number; height: number },\r\n                invertY: boolean,\r\n                noMipmap: boolean,\r\n                isCompressed: boolean,\r\n                processFunction: (\r\n                    width: number,\r\n                    height: number,\r\n                    img: HTMLImageElement | ImageBitmap | { width: number; height: number },\r\n                    extension: string,\r\n                    texture: InternalTexture,\r\n                    continuationCallback: () => void\r\n                ) => boolean\r\n            ) => {\r\n                const imageBitmap = img as ImageBitmap | { width: number; height: number }; // we will never get an HTMLImageElement in WebGPU\r\n\r\n                texture.baseWidth = imageBitmap.width;\r\n                texture.baseHeight = imageBitmap.height;\r\n                texture.width = imageBitmap.width;\r\n                texture.height = imageBitmap.height;\r\n                texture.format = texture.format !== -1 ? texture.format : format ?? Constants.TEXTUREFORMAT_RGBA;\r\n                texture.type = texture.type !== -1 ? texture.type : Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n                texture._creationFlags = creationFlags ?? 0;\r\n\r\n                processFunction(texture.width, texture.height, imageBitmap, extension, texture, () => {});\r\n\r\n                if (!texture._hardwareTexture?.underlyingResource) {\r\n                    // the texture could have been created before reaching this point so don't recreate it if already existing\r\n                    const gpuTextureWrapper = this._textureHelper.createGPUTextureForInternalTexture(texture, imageBitmap.width, imageBitmap.height, undefined, creationFlags);\r\n\r\n                    if (WebGPUTextureHelper.IsImageBitmap(imageBitmap)) {\r\n                        this._textureHelper.updateTexture(\r\n                            imageBitmap,\r\n                            texture,\r\n                            imageBitmap.width,\r\n                            imageBitmap.height,\r\n                            texture.depth,\r\n                            gpuTextureWrapper.format,\r\n                            0,\r\n                            0,\r\n                            invertY,\r\n                            false,\r\n                            0,\r\n                            0\r\n                        );\r\n                        if (!noMipmap && !isCompressed) {\r\n                            this._generateMipmaps(texture, this._uploadEncoder);\r\n                        }\r\n                    }\r\n                } else if (!noMipmap && !isCompressed) {\r\n                    this._generateMipmaps(texture, this._uploadEncoder);\r\n                }\r\n\r\n                if (scene) {\r\n                    scene.removePendingData(texture);\r\n                }\r\n\r\n                texture.isReady = true;\r\n\r\n                texture.onLoadedObservable.notifyObservers(texture);\r\n                texture.onLoadedObservable.clear();\r\n            },\r\n            () => false,\r\n            buffer,\r\n            fallback,\r\n            format,\r\n            forcedExtension,\r\n            mimeType,\r\n            loaderOptions,\r\n            useSRGBBuffer\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Wraps an external web gpu texture in a Babylon texture.\r\n     * @param texture defines the external texture\r\n     * @returns the babylon internal texture\r\n     */\r\n    public wrapWebGPUTexture(texture: GPUTexture): InternalTexture {\r\n        const hardwareTexture = new WebGPUHardwareTexture(texture);\r\n        const internalTexture = new InternalTexture(this, InternalTextureSource.Unknown, true);\r\n        internalTexture._hardwareTexture = hardwareTexture;\r\n        internalTexture.isReady = true;\r\n        return internalTexture;\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Wraps an external web gl texture in a Babylon texture.\r\n     * @returns the babylon internal texture\r\n     */\r\n    public wrapWebGLTexture(): InternalTexture {\r\n        throw new Error(\"wrapWebGLTexture is not supported, use wrapWebGPUTexture instead.\");\r\n    }\r\n\r\n    public generateMipMapsForCubemap(texture: InternalTexture) {\r\n        if (texture.generateMipMaps) {\r\n            const gpuTexture = texture._hardwareTexture?.underlyingResource;\r\n\r\n            if (!gpuTexture) {\r\n                this._textureHelper.createGPUTextureForInternalTexture(texture);\r\n            }\r\n\r\n            this._generateMipmaps(texture);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the sampling mode of a given texture\r\n     * @param samplingMode defines the required sampling mode\r\n     * @param texture defines the texture to update\r\n     * @param generateMipMaps defines whether to generate mipmaps for the texture\r\n     */\r\n    public updateTextureSamplingMode(samplingMode: number, texture: InternalTexture, generateMipMaps: boolean = false): void {\r\n        if (generateMipMaps) {\r\n            texture.generateMipMaps = true;\r\n            this._generateMipmaps(texture);\r\n        }\r\n\r\n        texture.samplingMode = samplingMode;\r\n    }\r\n\r\n    /**\r\n     * Update the sampling mode of a given texture\r\n     * @param texture defines the texture to update\r\n     * @param wrapU defines the texture wrap mode of the u coordinates\r\n     * @param wrapV defines the texture wrap mode of the v coordinates\r\n     * @param wrapR defines the texture wrap mode of the r coordinates\r\n     */\r\n    public updateTextureWrappingMode(texture: InternalTexture, wrapU: Nullable<number>, wrapV: Nullable<number> = null, wrapR: Nullable<number> = null): void {\r\n        if (wrapU !== null) {\r\n            texture._cachedWrapU = wrapU;\r\n        }\r\n        if (wrapV !== null) {\r\n            texture._cachedWrapV = wrapV;\r\n        }\r\n        if ((texture.is2DArray || texture.is3D) && wrapR !== null) {\r\n            texture._cachedWrapR = wrapR;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the dimensions of a texture\r\n     * @param texture texture to update\r\n     * @param width new width of the texture\r\n     * @param height new height of the texture\r\n     * @param depth new depth of the texture\r\n     */\r\n    public updateTextureDimensions(texture: InternalTexture, width: number, height: number, depth: number = 1): void {\r\n        if (!texture._hardwareTexture) {\r\n            // the gpu texture is not created yet, so when it is it will be created with the right dimensions\r\n            return;\r\n        }\r\n\r\n        if (texture.width === width && texture.height === height && texture.depth === depth) {\r\n            return;\r\n        }\r\n\r\n        const additionalUsages = (texture._hardwareTexture as WebGPUHardwareTexture).textureAdditionalUsages;\r\n\r\n        texture._hardwareTexture.release(); // don't defer the releasing! Else we will release at the end of this frame the gpu texture we are about to create in the next line...\r\n\r\n        this._textureHelper.createGPUTextureForInternalTexture(texture, width, height, depth, additionalUsages);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setInternalTexture(name: string, texture: Nullable<InternalTexture | ExternalTexture>, baseName?: string): void {\r\n        baseName = baseName ?? name;\r\n        if (this._currentEffect) {\r\n            const webgpuPipelineContext = this._currentEffect._pipelineContext as WebGPUPipelineContext;\r\n            const availableTexture = webgpuPipelineContext.shaderProcessingContext.availableTextures[baseName];\r\n\r\n            this._currentMaterialContext.setTexture(name, texture);\r\n\r\n            if (availableTexture && availableTexture.autoBindSampler) {\r\n                const samplerName = baseName + WebGPUShaderProcessor.AutoSamplerSuffix;\r\n                this._currentMaterialContext.setSampler(samplerName, texture as InternalTexture); // we can safely cast to InternalTexture because ExternalTexture always has autoBindSampler = false\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a texture to the according uniform.\r\n     * @param channel The texture channel\r\n     * @param unused unused parameter\r\n     * @param texture The texture to apply\r\n     * @param name The name of the uniform in the effect\r\n     */\r\n    public setTexture(channel: number, unused: Nullable<WebGLUniformLocation>, texture: Nullable<BaseTexture>, name: string): void {\r\n        this._setTexture(channel, texture, false, false, name, name);\r\n    }\r\n\r\n    /**\r\n     * Sets an array of texture to the WebGPU context\r\n     * @param channel defines the channel where the texture array must be set\r\n     * @param unused unused parameter\r\n     * @param textures defines the array of textures to bind\r\n     * @param name name of the channel\r\n     */\r\n    public setTextureArray(channel: number, unused: Nullable<WebGLUniformLocation>, textures: BaseTexture[], name: string): void {\r\n        for (let index = 0; index < textures.length; index++) {\r\n            this._setTexture(-1, textures[index], true, false, name + index.toString(), name);\r\n        }\r\n    }\r\n\r\n    protected _setTexture(\r\n        channel: number,\r\n        texture: Nullable<BaseTexture>,\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        isPartOfTextureArray = false,\r\n        depthStencilTexture = false,\r\n        name = \"\",\r\n        baseName?: string\r\n    ): boolean {\r\n        // name == baseName for a texture that is not part of a texture array\r\n        // Else, name is something like 'myTexture0' / 'myTexture1' / ... and baseName is 'myTexture'\r\n        // baseName is used to look up the texture in the shaderProcessingContext.availableTextures map\r\n        // name is used to look up the texture in the _currentMaterialContext.textures map\r\n        baseName = baseName ?? name;\r\n        if (this._currentEffect) {\r\n            if (!texture) {\r\n                this._currentMaterialContext.setTexture(name, null);\r\n                return false;\r\n            }\r\n\r\n            // Video\r\n            if ((<VideoTexture>texture).video) {\r\n                (<VideoTexture>texture).update();\r\n            } else if (texture.delayLoadState === Constants.DELAYLOADSTATE_NOTLOADED) {\r\n                // Delay loading\r\n                texture.delayLoad();\r\n                return false;\r\n            }\r\n\r\n            let internalTexture: Nullable<InternalTexture> = null;\r\n            if (depthStencilTexture) {\r\n                internalTexture = (<RenderTargetTexture>texture).depthStencilTexture!;\r\n            } else if (texture.isReady()) {\r\n                internalTexture = <InternalTexture>texture.getInternalTexture();\r\n            } else if (texture.isCube) {\r\n                internalTexture = this.emptyCubeTexture;\r\n            } else if (texture.is3D) {\r\n                internalTexture = this.emptyTexture3D;\r\n            } else if (texture.is2DArray) {\r\n                internalTexture = this.emptyTexture2DArray;\r\n            } else {\r\n                internalTexture = this.emptyTexture;\r\n            }\r\n\r\n            if (internalTexture && !internalTexture.isMultiview) {\r\n                // CUBIC_MODE and SKYBOX_MODE both require CLAMP_TO_EDGE.  All other modes use REPEAT.\r\n                if (internalTexture.isCube && internalTexture._cachedCoordinatesMode !== texture.coordinatesMode) {\r\n                    internalTexture._cachedCoordinatesMode = texture.coordinatesMode;\r\n\r\n                    const textureWrapMode =\r\n                        texture.coordinatesMode !== Constants.TEXTURE_CUBIC_MODE && texture.coordinatesMode !== Constants.TEXTURE_SKYBOX_MODE\r\n                            ? Constants.TEXTURE_WRAP_ADDRESSMODE\r\n                            : Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n                    texture.wrapU = textureWrapMode;\r\n                    texture.wrapV = textureWrapMode;\r\n                }\r\n\r\n                internalTexture._cachedWrapU = texture.wrapU;\r\n                internalTexture._cachedWrapV = texture.wrapV;\r\n                if (internalTexture.is3D) {\r\n                    internalTexture._cachedWrapR = texture.wrapR;\r\n                }\r\n\r\n                this._setAnisotropicLevel(0, internalTexture, texture.anisotropicFilteringLevel);\r\n            }\r\n\r\n            this._setInternalTexture(name, internalTexture, baseName);\r\n        } else {\r\n            if (this.dbgVerboseLogsForFirstFrames) {\r\n                if ((this as any)._count === undefined) {\r\n                    (this as any)._count = 0;\r\n                }\r\n                if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                    Logger.Log([\"frame #\" + (this as any)._count + \" - _setTexture called with a null _currentEffect! texture=\", texture]);\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setAnisotropicLevel(target: number, internalTexture: InternalTexture, anisotropicFilteringLevel: number) {\r\n        if (internalTexture._cachedAnisotropicFilteringLevel !== anisotropicFilteringLevel) {\r\n            internalTexture._cachedAnisotropicFilteringLevel = Math.min(anisotropicFilteringLevel, this._caps.maxAnisotropy);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindTexture(channel: number, texture: InternalTexture, name: string): void {\r\n        if (channel === undefined) {\r\n            return;\r\n        }\r\n\r\n        this._setInternalTexture(name, texture);\r\n    }\r\n\r\n    /**\r\n     * Generates the mipmaps for a texture\r\n     * @param texture texture to generate the mipmaps for\r\n     */\r\n    public generateMipmaps(texture: InternalTexture): void {\r\n        this._generateMipmaps(texture);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _generateMipmaps(texture: InternalTexture, commandEncoder?: GPUCommandEncoder) {\r\n        commandEncoder = commandEncoder ?? this._renderEncoder;\r\n\r\n        const gpuHardwareTexture = texture._hardwareTexture as Nullable<WebGPUHardwareTexture>;\r\n\r\n        if (!gpuHardwareTexture) {\r\n            return;\r\n        }\r\n\r\n        if (commandEncoder === this._renderEncoder) {\r\n            // We must close the current pass (if any) because we are going to use the render encoder to generate the mipmaps (so, we are going to create a new render pass)\r\n            this._endCurrentRenderPass();\r\n        }\r\n\r\n        const format = (texture._hardwareTexture as WebGPUHardwareTexture).format;\r\n        const mipmapCount = WebGPUTextureHelper.ComputeNumMipmapLevels(texture.width, texture.height);\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log(\r\n                    \"frame #\" +\r\n                        (this as any)._count +\r\n                        \" - generate mipmaps - width=\" +\r\n                        texture.width +\r\n                        \", height=\" +\r\n                        texture.height +\r\n                        \", isCube=\" +\r\n                        texture.isCube +\r\n                        \", command encoder=\" +\r\n                        (commandEncoder === this._renderEncoder ? \"render\" : \"copy\")\r\n                );\r\n            }\r\n        }\r\n\r\n        if (texture.isCube) {\r\n            this._textureHelper.generateCubeMipmaps(gpuHardwareTexture, format, mipmapCount, commandEncoder);\r\n        } else {\r\n            this._textureHelper.generateMipmaps(gpuHardwareTexture, format, mipmapCount, 0, commandEncoder);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update a portion of an internal texture\r\n     * @param texture defines the texture to update\r\n     * @param imageData defines the data to store into the texture\r\n     * @param xOffset defines the x coordinates of the update rectangle\r\n     * @param yOffset defines the y coordinates of the update rectangle\r\n     * @param width defines the width of the update rectangle\r\n     * @param height defines the height of the update rectangle\r\n     * @param faceIndex defines the face index if texture is a cube (0 by default)\r\n     * @param lod defines the lod level to update (0 by default)\r\n     * @param generateMipMaps defines whether to generate mipmaps or not\r\n     */\r\n    public updateTextureData(\r\n        texture: InternalTexture,\r\n        imageData: ArrayBufferView,\r\n        xOffset: number,\r\n        yOffset: number,\r\n        width: number,\r\n        height: number,\r\n        faceIndex: number = 0,\r\n        lod: number = 0,\r\n        generateMipMaps = false\r\n    ): void {\r\n        let gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n        if (!texture._hardwareTexture?.underlyingResource) {\r\n            gpuTextureWrapper = this._textureHelper.createGPUTextureForInternalTexture(texture);\r\n        }\r\n\r\n        const data = new Uint8Array(imageData.buffer, imageData.byteOffset, imageData.byteLength);\r\n\r\n        this._textureHelper.updateTexture(data, texture, width, height, texture.depth, gpuTextureWrapper.format, faceIndex, lod, texture.invertY, false, xOffset, yOffset);\r\n\r\n        if (generateMipMaps) {\r\n            this._generateMipmaps(texture);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadCompressedDataToTextureDirectly(\r\n        texture: InternalTexture,\r\n        internalFormat: number,\r\n        width: number,\r\n        height: number,\r\n        imageData: ArrayBufferView,\r\n        faceIndex: number = 0,\r\n        lod: number = 0\r\n    ) {\r\n        let gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n        if (!texture._hardwareTexture?.underlyingResource) {\r\n            texture.format = internalFormat;\r\n            gpuTextureWrapper = this._textureHelper.createGPUTextureForInternalTexture(texture, width, height);\r\n        }\r\n\r\n        const data = new Uint8Array(imageData.buffer, imageData.byteOffset, imageData.byteLength);\r\n\r\n        this._textureHelper.updateTexture(data, texture, width, height, texture.depth, gpuTextureWrapper.format, faceIndex, lod, false, false, 0, 0);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadDataToTextureDirectly(\r\n        texture: InternalTexture,\r\n        imageData: ArrayBufferView,\r\n        faceIndex: number = 0,\r\n        lod: number = 0,\r\n        babylonInternalFormat?: number,\r\n        useTextureWidthAndHeight = false\r\n    ): void {\r\n        const lodMaxWidth = Math.round(Math.log(texture.width) * Math.LOG2E);\r\n        const lodMaxHeight = Math.round(Math.log(texture.height) * Math.LOG2E);\r\n\r\n        const width = useTextureWidthAndHeight ? texture.width : Math.pow(2, Math.max(lodMaxWidth - lod, 0));\r\n        const height = useTextureWidthAndHeight ? texture.height : Math.pow(2, Math.max(lodMaxHeight - lod, 0));\r\n\r\n        let gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n        if (!texture._hardwareTexture?.underlyingResource) {\r\n            gpuTextureWrapper = this._textureHelper.createGPUTextureForInternalTexture(texture, width, height);\r\n        }\r\n\r\n        const data = new Uint8Array(imageData.buffer, imageData.byteOffset, imageData.byteLength);\r\n\r\n        this._textureHelper.updateTexture(data, texture, width, height, texture.depth, gpuTextureWrapper.format, faceIndex, lod, texture.invertY, false, 0, 0);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadArrayBufferViewToTexture(texture: InternalTexture, imageData: ArrayBufferView, faceIndex: number = 0, lod: number = 0): void {\r\n        this._uploadDataToTextureDirectly(texture, imageData, faceIndex, lod);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadImageToTexture(texture: InternalTexture, image: HTMLImageElement | ImageBitmap, faceIndex: number = 0, lod: number = 0) {\r\n        let gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n        if (!texture._hardwareTexture?.underlyingResource) {\r\n            gpuTextureWrapper = this._textureHelper.createGPUTextureForInternalTexture(texture);\r\n        }\r\n\r\n        if (image instanceof HTMLImageElement) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"WebGPU engine: HTMLImageElement not supported in _uploadImageToTexture!\";\r\n        }\r\n\r\n        const bitmap = image as ImageBitmap; // in WebGPU we will always get an ImageBitmap, not an HTMLImageElement\r\n\r\n        const width = Math.ceil(texture.width / (1 << lod));\r\n        const height = Math.ceil(texture.height / (1 << lod));\r\n\r\n        this._textureHelper.updateTexture(bitmap, texture, width, height, texture.depth, gpuTextureWrapper.format, faceIndex, lod, texture.invertY, false, 0, 0);\r\n    }\r\n\r\n    /**\r\n     * Reads pixels from the current frame buffer. Please note that this function can be slow\r\n     * @param x defines the x coordinate of the rectangle where pixels must be read\r\n     * @param y defines the y coordinate of the rectangle where pixels must be read\r\n     * @param width defines the width of the rectangle where pixels must be read\r\n     * @param height defines the height of the rectangle where pixels must be read\r\n     * @param hasAlpha defines whether the output should have alpha or not (defaults to true)\r\n     * @param flushRenderer true to flush the renderer from the pending commands before reading the pixels\r\n     * @returns a ArrayBufferView promise (Uint8Array) containing RGBA colors\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public readPixels(x: number, y: number, width: number, height: number, hasAlpha = true, flushRenderer = true): Promise<ArrayBufferView> {\r\n        const renderPassWrapper = this._getCurrentRenderPassWrapper();\r\n        const hardwareTexture = renderPassWrapper.colorAttachmentGPUTextures[0];\r\n        if (!hardwareTexture) {\r\n            // we are calling readPixels for a render pass with no color texture bound\r\n            return Promise.resolve(new Uint8Array(0));\r\n        }\r\n        const gpuTexture = hardwareTexture.underlyingResource;\r\n        const gpuTextureFormat = hardwareTexture.format;\r\n        if (!gpuTexture) {\r\n            // we are calling readPixels before startMainRenderPass has been called and no RTT is bound, so swapChainTexture is not setup yet!\r\n            return Promise.resolve(new Uint8Array(0));\r\n        }\r\n        if (flushRenderer) {\r\n            this.flushFramebuffer();\r\n        }\r\n        return this._textureHelper.readPixels(gpuTexture, x, y, width, height, gpuTextureFormat);\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Frame management\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Begin a new frame\r\n     */\r\n    public beginFrame(): void {\r\n        super.beginFrame();\r\n    }\r\n\r\n    /**\r\n     * End the current frame\r\n     */\r\n    public endFrame() {\r\n        this._endCurrentRenderPass();\r\n\r\n        this._snapshotRendering.endFrame();\r\n\r\n        this._timestampQuery.endFrame(this._renderEncoder);\r\n        this._timestampIndex = 0;\r\n\r\n        this.flushFramebuffer();\r\n\r\n        this._textureHelper.destroyDeferredTextures();\r\n        this._bufferManager.destroyDeferredBuffers();\r\n\r\n        if (this._features._collectUbosUpdatedInFrame) {\r\n            if (this.dbgVerboseLogsForFirstFrames) {\r\n                if ((this as any)._count === undefined) {\r\n                    (this as any)._count = 0;\r\n                }\r\n                if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                    const list: Array<string> = [];\r\n                    for (const name in UniformBuffer._UpdatedUbosInFrame) {\r\n                        list.push(name + \":\" + UniformBuffer._UpdatedUbosInFrame[name]);\r\n                    }\r\n                    Logger.Log([\"frame #\" + (this as any)._count + \" - updated ubos -\", list.join(\", \")]);\r\n                }\r\n            }\r\n            UniformBuffer._UpdatedUbosInFrame = {};\r\n        }\r\n\r\n        this.countersLastFrame.numEnableEffects = this._counters.numEnableEffects;\r\n        this.countersLastFrame.numEnableDrawWrapper = this._counters.numEnableDrawWrapper;\r\n        this.countersLastFrame.numBundleCreationNonCompatMode = this._counters.numBundleCreationNonCompatMode;\r\n        this.countersLastFrame.numBundleReuseNonCompatMode = this._counters.numBundleReuseNonCompatMode;\r\n        this._counters.numEnableEffects = 0;\r\n        this._counters.numEnableDrawWrapper = 0;\r\n        this._counters.numBundleCreationNonCompatMode = 0;\r\n        this._counters.numBundleReuseNonCompatMode = 0;\r\n\r\n        this._cacheRenderPipeline.endFrame();\r\n        this._cacheBindGroups.endFrame();\r\n\r\n        this._pendingDebugCommands.length = 0;\r\n\r\n        super.endFrame();\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if ((this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\"%c frame #\" + (this as any)._count + \" - end\", \"background: #ffff00\"]);\r\n            }\r\n            if ((this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                (this as any)._count++;\r\n                if ((this as any)._count !== this.dbgVerboseLogsNumFrames) {\r\n                    Logger.Log([\"%c frame #\" + (this as any)._count + \" - begin\", \"background: #ffff00\"]);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Force a WebGPU flush (ie. a flush of all waiting commands)\r\n     */\r\n    public flushFramebuffer(): void {\r\n        // we need to end the current render pass (main or rtt) if any as we are not allowed to submit the command buffers when being in a pass\r\n        this._endCurrentRenderPass();\r\n\r\n        this._commandBuffers[0] = this._uploadEncoder.finish();\r\n        this._commandBuffers[1] = this._renderEncoder.finish();\r\n\r\n        this._device.queue.submit(this._commandBuffers);\r\n\r\n        this._uploadEncoder = this._device.createCommandEncoder(this._uploadEncoderDescriptor);\r\n        this._renderEncoder = this._device.createCommandEncoder(this._renderEncoderDescriptor);\r\n\r\n        this._timestampQuery.startFrame(this._uploadEncoder);\r\n\r\n        this._textureHelper.setCommandEncoder(this._uploadEncoder);\r\n\r\n        this._bundleList.reset();\r\n    }\r\n\r\n    /** @internal */\r\n    public _currentFrameBufferIsDefaultFrameBuffer() {\r\n        return this._currentPassIsMainPass();\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Render Pass\r\n    //------------------------------------------------------------------------------\r\n\r\n    private _startRenderTargetRenderPass(\r\n        renderTargetWrapper: RenderTargetWrapper,\r\n        setClearStates: boolean,\r\n        clearColor: Nullable<IColor4Like>,\r\n        clearDepth: boolean,\r\n        clearStencil: boolean\r\n    ) {\r\n        this._endCurrentRenderPass();\r\n\r\n        const rtWrapper = renderTargetWrapper as WebGPURenderTargetWrapper;\r\n\r\n        const depthStencilTexture = rtWrapper._depthStencilTexture;\r\n        const gpuDepthStencilWrapper = depthStencilTexture?._hardwareTexture as Nullable<WebGPUHardwareTexture>;\r\n        const gpuDepthStencilTexture = gpuDepthStencilWrapper?.underlyingResource as Nullable<GPUTexture>;\r\n        const gpuDepthStencilMSAATexture = gpuDepthStencilWrapper?.getMSAATexture();\r\n\r\n        const depthTextureView = gpuDepthStencilTexture?.createView(this._rttRenderPassWrapper.depthAttachmentViewDescriptor!);\r\n        const depthMSAATextureView = gpuDepthStencilMSAATexture?.createView(this._rttRenderPassWrapper.depthAttachmentViewDescriptor!);\r\n        const depthTextureHasStencil = gpuDepthStencilWrapper ? WebGPUTextureHelper.HasStencilAspect(gpuDepthStencilWrapper.format) : false;\r\n\r\n        const colorAttachments: (GPURenderPassColorAttachment | null)[] = [];\r\n\r\n        if (this.useReverseDepthBuffer) {\r\n            this.setDepthFunctionToGreaterOrEqual();\r\n        }\r\n\r\n        const clearColorForIntegerRT = tempColor4;\r\n        if (clearColor) {\r\n            clearColorForIntegerRT.r = clearColor.r * 255;\r\n            clearColorForIntegerRT.g = clearColor.g * 255;\r\n            clearColorForIntegerRT.b = clearColor.b * 255;\r\n            clearColorForIntegerRT.a = clearColor.a * 255;\r\n        }\r\n\r\n        const mustClearColor = setClearStates && clearColor;\r\n        const mustClearDepth = setClearStates && clearDepth;\r\n        const mustClearStencil = setClearStates && clearStencil;\r\n\r\n        if (rtWrapper._attachments && rtWrapper.isMulti) {\r\n            // multi render targets\r\n            if (!this._mrtAttachments || this._mrtAttachments.length === 0) {\r\n                this._mrtAttachments = rtWrapper._defaultAttachments;\r\n            }\r\n            for (let i = 0; i < this._mrtAttachments.length; ++i) {\r\n                const index = this._mrtAttachments[i]; // if index == 0 it means the texture should not be written to => at render pass creation time, it means we should not clear it\r\n                const mrtTexture = rtWrapper.textures![i];\r\n                const gpuMRTWrapper = mrtTexture?._hardwareTexture as Nullable<WebGPUHardwareTexture>;\r\n                const gpuMRTTexture = gpuMRTWrapper?.underlyingResource;\r\n                if (gpuMRTWrapper && gpuMRTTexture) {\r\n                    const gpuMSAATexture = gpuMRTWrapper.getMSAATexture(i);\r\n\r\n                    const layerIndex = rtWrapper.layerIndices?.[i] ?? 0;\r\n                    const faceIndex = rtWrapper.faceIndices?.[i] ?? 0;\r\n                    const viewDescriptor = {\r\n                        ...this._rttRenderPassWrapper.colorAttachmentViewDescriptor!,\r\n                        format: gpuMRTWrapper.format,\r\n                        baseArrayLayer: mrtTexture.isCube ? layerIndex * 6 + faceIndex : layerIndex,\r\n                    };\r\n                    const msaaViewDescriptor = {\r\n                        ...this._rttRenderPassWrapper.colorAttachmentViewDescriptor!,\r\n                        format: gpuMRTWrapper.format,\r\n                        baseArrayLayer: 0,\r\n                    };\r\n                    const isRTInteger = mrtTexture.type === Constants.TEXTURETYPE_UNSIGNED_INTEGER || mrtTexture.type === Constants.TEXTURETYPE_UNSIGNED_SHORT;\r\n\r\n                    const colorTextureView = gpuMRTTexture.createView(viewDescriptor);\r\n                    const colorMSAATextureView = gpuMSAATexture?.createView(msaaViewDescriptor);\r\n\r\n                    colorAttachments.push({\r\n                        view: colorMSAATextureView ? colorMSAATextureView : colorTextureView,\r\n                        resolveTarget: gpuMSAATexture ? colorTextureView : undefined,\r\n                        clearValue: index !== 0 && mustClearColor ? (isRTInteger ? clearColorForIntegerRT : clearColor) : undefined,\r\n                        loadOp: index !== 0 && mustClearColor ? WebGPUConstants.LoadOp.Clear : WebGPUConstants.LoadOp.Load,\r\n                        storeOp: WebGPUConstants.StoreOp.Store,\r\n                    });\r\n                }\r\n            }\r\n            this._cacheRenderPipeline.setMRT(rtWrapper.textures!, this._mrtAttachments.length);\r\n            this._cacheRenderPipeline.setMRTAttachments(this._mrtAttachments);\r\n        } else {\r\n            // single render target\r\n            const internalTexture = rtWrapper.texture;\r\n            if (internalTexture) {\r\n                const gpuWrapper = internalTexture._hardwareTexture as WebGPUHardwareTexture;\r\n                const gpuTexture = gpuWrapper.underlyingResource!;\r\n\r\n                const gpuMSAATexture = gpuWrapper.getMSAATexture();\r\n                const colorTextureView = gpuTexture.createView(this._rttRenderPassWrapper.colorAttachmentViewDescriptor!);\r\n                const colorMSAATextureView = gpuMSAATexture?.createView(this._rttRenderPassWrapper.colorAttachmentViewDescriptor!);\r\n                const isRTInteger = internalTexture.type === Constants.TEXTURETYPE_UNSIGNED_INTEGER || internalTexture.type === Constants.TEXTURETYPE_UNSIGNED_SHORT;\r\n\r\n                colorAttachments.push({\r\n                    view: colorMSAATextureView ? colorMSAATextureView : colorTextureView,\r\n                    resolveTarget: gpuMSAATexture ? colorTextureView : undefined,\r\n                    clearValue: mustClearColor ? (isRTInteger ? clearColorForIntegerRT : clearColor) : undefined,\r\n                    loadOp: mustClearColor ? WebGPUConstants.LoadOp.Clear : WebGPUConstants.LoadOp.Load,\r\n                    storeOp: WebGPUConstants.StoreOp.Store,\r\n                });\r\n            } else {\r\n                colorAttachments.push(null);\r\n            }\r\n        }\r\n\r\n        this._debugPushGroup?.(\"render target pass\" + (renderTargetWrapper.label ? \" (\" + renderTargetWrapper.label + \")\" : \"\"), 1);\r\n\r\n        this._rttRenderPassWrapper.renderPassDescriptor = {\r\n            label: (renderTargetWrapper.label ?? \"RTT\") + \"RenderPass\",\r\n            colorAttachments,\r\n            depthStencilAttachment:\r\n                depthStencilTexture && gpuDepthStencilTexture\r\n                    ? {\r\n                          view: depthMSAATextureView ? depthMSAATextureView : depthTextureView!,\r\n                          depthClearValue: mustClearDepth ? (this.useReverseDepthBuffer ? this._clearReverseDepthValue : this._clearDepthValue) : undefined,\r\n                          depthLoadOp: mustClearDepth ? WebGPUConstants.LoadOp.Clear : WebGPUConstants.LoadOp.Load,\r\n                          depthStoreOp: WebGPUConstants.StoreOp.Store,\r\n                          stencilClearValue: rtWrapper._depthStencilTextureWithStencil && mustClearStencil ? this._clearStencilValue : undefined,\r\n                          stencilLoadOp: !depthTextureHasStencil\r\n                              ? undefined\r\n                              : rtWrapper._depthStencilTextureWithStencil && mustClearStencil\r\n                                ? WebGPUConstants.LoadOp.Clear\r\n                                : WebGPUConstants.LoadOp.Load,\r\n                          stencilStoreOp: !depthTextureHasStencil ? undefined : WebGPUConstants.StoreOp.Store,\r\n                      }\r\n                    : undefined,\r\n            occlusionQuerySet: this._occlusionQuery?.hasQueries ? this._occlusionQuery.querySet : undefined,\r\n        };\r\n        this._timestampQuery.startPass(this._rttRenderPassWrapper.renderPassDescriptor, this._timestampIndex);\r\n        this._currentRenderPass = this._renderEncoder.beginRenderPass(this._rttRenderPassWrapper.renderPassDescriptor);\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                const internalTexture = rtWrapper.texture!;\r\n                Logger.Log([\r\n                    \"frame #\" +\r\n                        (this as any)._count +\r\n                        \" - render target begin pass - rtt name=\" +\r\n                        renderTargetWrapper.label +\r\n                        \", internalTexture.uniqueId=\" +\r\n                        internalTexture.uniqueId +\r\n                        \", width=\" +\r\n                        internalTexture.width +\r\n                        \", height=\" +\r\n                        internalTexture.height +\r\n                        \", setClearStates=\" +\r\n                        setClearStates,\r\n                    \"renderPassDescriptor=\",\r\n                    this._rttRenderPassWrapper.renderPassDescriptor,\r\n                ]);\r\n            }\r\n        }\r\n\r\n        this._debugFlushPendingCommands?.();\r\n\r\n        this._resetRenderPassStates();\r\n\r\n        if (!gpuDepthStencilWrapper || !WebGPUTextureHelper.HasStencilAspect(gpuDepthStencilWrapper.format)) {\r\n            this._stencilStateComposer.enabled = false;\r\n        }\r\n    }\r\n\r\n    private _startMainRenderPass(setClearStates: boolean, clearColor?: Nullable<IColor4Like>, clearDepth?: boolean, clearStencil?: boolean): void {\r\n        this._endCurrentRenderPass();\r\n\r\n        if (this.useReverseDepthBuffer) {\r\n            this.setDepthFunctionToGreaterOrEqual();\r\n        }\r\n\r\n        const mustClearColor = setClearStates && clearColor;\r\n        const mustClearDepth = setClearStates && clearDepth;\r\n        const mustClearStencil = setClearStates && clearStencil;\r\n\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.colorAttachments[0]!.clearValue = mustClearColor ? clearColor : undefined;\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.colorAttachments[0]!.loadOp = mustClearColor ? WebGPUConstants.LoadOp.Clear : WebGPUConstants.LoadOp.Load;\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.depthStencilAttachment!.depthClearValue = mustClearDepth\r\n            ? this.useReverseDepthBuffer\r\n                ? this._clearReverseDepthValue\r\n                : this._clearDepthValue\r\n            : undefined;\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.depthStencilAttachment!.depthLoadOp = mustClearDepth ? WebGPUConstants.LoadOp.Clear : WebGPUConstants.LoadOp.Load;\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.depthStencilAttachment!.stencilClearValue = mustClearStencil ? this._clearStencilValue : undefined;\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.depthStencilAttachment!.stencilLoadOp = !this.isStencilEnable\r\n            ? undefined\r\n            : mustClearStencil\r\n              ? WebGPUConstants.LoadOp.Clear\r\n              : WebGPUConstants.LoadOp.Load;\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.occlusionQuerySet = this._occlusionQuery?.hasQueries ? this._occlusionQuery.querySet : undefined;\r\n\r\n        const swapChainTexture = this._context.getCurrentTexture();\r\n        this._mainRenderPassWrapper.colorAttachmentGPUTextures[0]!.set(swapChainTexture);\r\n\r\n        // Resolve in case of MSAA\r\n        if (this._options.antialias) {\r\n            viewDescriptorSwapChainAntialiasing.format = swapChainTexture.format;\r\n            this._mainRenderPassWrapper.renderPassDescriptor!.colorAttachments[0]!.resolveTarget = swapChainTexture.createView(viewDescriptorSwapChainAntialiasing);\r\n        } else {\r\n            viewDescriptorSwapChain.format = swapChainTexture.format;\r\n            this._mainRenderPassWrapper.renderPassDescriptor!.colorAttachments[0]!.view = swapChainTexture.createView(viewDescriptorSwapChain);\r\n        }\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\r\n                    \"frame #\" + (this as any)._count + \" - main begin pass - texture width=\" + (this._mainTextureExtends as any).width,\r\n                    \" height=\" + (this._mainTextureExtends as any).height + \", setClearStates=\" + setClearStates,\r\n                    \"renderPassDescriptor=\",\r\n                    this._mainRenderPassWrapper.renderPassDescriptor,\r\n                ]);\r\n            }\r\n        }\r\n\r\n        this._debugPushGroup?.(\"main pass\", 0);\r\n\r\n        this._timestampQuery.startPass(this._mainRenderPassWrapper.renderPassDescriptor!, this._timestampIndex);\r\n        this._currentRenderPass = this._renderEncoder.beginRenderPass(this._mainRenderPassWrapper.renderPassDescriptor!);\r\n\r\n        this._setDepthTextureFormat(this._mainRenderPassWrapper);\r\n        this._setColorFormat(this._mainRenderPassWrapper);\r\n\r\n        this._debugFlushPendingCommands?.();\r\n\r\n        this._resetRenderPassStates();\r\n\r\n        if (!this._isStencilEnable) {\r\n            this._stencilStateComposer.enabled = false;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _endCurrentRenderPass(): number {\r\n        if (!this._currentRenderPass) {\r\n            return 0;\r\n        }\r\n\r\n        const currentPassIndex = this._currentPassIsMainPass() ? 2 : 1;\r\n\r\n        if (!this._snapshotRendering.endRenderPass(this._currentRenderPass) && !this.compatibilityMode) {\r\n            this._bundleList.run(this._currentRenderPass);\r\n            this._bundleList.reset();\r\n        }\r\n        this._currentRenderPass.end();\r\n\r\n        this._timestampQuery.endPass(\r\n            this._timestampIndex,\r\n            (this._currentRenderTarget && (this._currentRenderTarget as WebGPURenderTargetWrapper).gpuTimeInFrame\r\n                ? (this._currentRenderTarget as WebGPURenderTargetWrapper).gpuTimeInFrame\r\n                : this.gpuTimeInFrameForMainPass) as WebGPUPerfCounter\r\n        );\r\n        this._timestampIndex += 2;\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log(\r\n                    \"frame #\" +\r\n                        (this as any)._count +\r\n                        \" - \" +\r\n                        (currentPassIndex === 2 ? \"main\" : \"render target\") +\r\n                        \" end pass\" +\r\n                        (currentPassIndex === 1 ? \" - internalTexture.uniqueId=\" + this._currentRenderTarget?.texture?.uniqueId : \"\")\r\n                );\r\n            }\r\n        }\r\n        this._debugPopGroup?.(0);\r\n        this._currentRenderPass = null;\r\n\r\n        return currentPassIndex;\r\n    }\r\n\r\n    /**\r\n     * Binds the frame buffer to the specified texture.\r\n     * @param texture The render target wrapper to render to\r\n     * @param faceIndex The face of the texture to render to in case of cube texture\r\n     * @param requiredWidth The width of the target to render to\r\n     * @param requiredHeight The height of the target to render to\r\n     * @param forceFullscreenViewport Forces the viewport to be the entire texture/screen if true\r\n     * @param lodLevel defines the lod level to bind to the frame buffer\r\n     * @param layer defines the 2d array index to bind to frame buffer to\r\n     */\r\n    public bindFramebuffer(\r\n        texture: RenderTargetWrapper,\r\n        faceIndex: number = 0,\r\n        requiredWidth?: number,\r\n        requiredHeight?: number,\r\n        forceFullscreenViewport?: boolean,\r\n        lodLevel = 0,\r\n        layer = 0\r\n    ): void {\r\n        const hardwareTexture = texture.texture?._hardwareTexture as Nullable<WebGPUHardwareTexture>;\r\n\r\n        if (this._currentRenderTarget) {\r\n            this.unBindFramebuffer(this._currentRenderTarget);\r\n        } else {\r\n            this._endCurrentRenderPass();\r\n        }\r\n        this._currentRenderTarget = texture;\r\n\r\n        this._rttRenderPassWrapper.colorAttachmentGPUTextures[0] = hardwareTexture;\r\n        this._rttRenderPassWrapper.depthTextureFormat = this._currentRenderTarget._depthStencilTexture\r\n            ? WebGPUTextureHelper.GetWebGPUTextureFormat(-1, this._currentRenderTarget._depthStencilTexture.format)\r\n            : undefined;\r\n\r\n        this._setDepthTextureFormat(this._rttRenderPassWrapper);\r\n        this._setColorFormat(this._rttRenderPassWrapper);\r\n\r\n        this._rttRenderPassWrapper.colorAttachmentViewDescriptor = {\r\n            format: this._colorFormat as GPUTextureFormat,\r\n            dimension: WebGPUConstants.TextureViewDimension.E2d,\r\n            mipLevelCount: 1,\r\n            baseArrayLayer: texture.isCube ? layer * 6 + faceIndex : layer,\r\n            baseMipLevel: lodLevel,\r\n            arrayLayerCount: 1,\r\n            aspect: WebGPUConstants.TextureAspect.All,\r\n        };\r\n\r\n        this._rttRenderPassWrapper.depthAttachmentViewDescriptor = {\r\n            format: this._depthTextureFormat!,\r\n            dimension: WebGPUConstants.TextureViewDimension.E2d,\r\n            mipLevelCount: 1,\r\n            baseArrayLayer: texture.isCube ? layer * 6 + faceIndex : layer,\r\n            baseMipLevel: 0,\r\n            arrayLayerCount: 1,\r\n            aspect: WebGPUConstants.TextureAspect.All,\r\n        };\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\r\n                    \"frame #\" +\r\n                        (this as any)._count +\r\n                        \" - bindFramebuffer - rtt name=\" +\r\n                        texture.label +\r\n                        \", internalTexture.uniqueId=\" +\r\n                        texture.texture?.uniqueId +\r\n                        \", face=\" +\r\n                        faceIndex +\r\n                        \", lodLevel=\" +\r\n                        lodLevel +\r\n                        \", layer=\" +\r\n                        layer,\r\n                    \"colorAttachmentViewDescriptor=\",\r\n                    this._rttRenderPassWrapper.colorAttachmentViewDescriptor,\r\n                    \"depthAttachmentViewDescriptor=\",\r\n                    this._rttRenderPassWrapper.depthAttachmentViewDescriptor,\r\n                ]);\r\n            }\r\n        }\r\n\r\n        // We don't create the render pass just now, we do a lazy creation of the render pass, hoping the render pass will be created by a call to clear()...\r\n\r\n        if (this._cachedViewport && !forceFullscreenViewport) {\r\n            this.setViewport(this._cachedViewport, requiredWidth, requiredHeight);\r\n        } else {\r\n            if (!requiredWidth) {\r\n                requiredWidth = texture.width;\r\n                if (lodLevel) {\r\n                    requiredWidth = requiredWidth / Math.pow(2, lodLevel);\r\n                }\r\n            }\r\n            if (!requiredHeight) {\r\n                requiredHeight = texture.height;\r\n                if (lodLevel) {\r\n                    requiredHeight = requiredHeight / Math.pow(2, lodLevel);\r\n                }\r\n            }\r\n\r\n            this._viewport(0, 0, requiredWidth, requiredHeight);\r\n        }\r\n\r\n        this.wipeCaches();\r\n    }\r\n\r\n    /**\r\n     * Unbind the current render target texture from the WebGPU context\r\n     * @param texture defines the render target wrapper to unbind\r\n     * @param disableGenerateMipMaps defines a boolean indicating that mipmaps must not be generated\r\n     * @param onBeforeUnbind defines a function which will be called before the effective unbind\r\n     */\r\n    public unBindFramebuffer(texture: RenderTargetWrapper, disableGenerateMipMaps = false, onBeforeUnbind?: () => void): void {\r\n        const saveCRT = this._currentRenderTarget;\r\n\r\n        this._currentRenderTarget = null; // to be iso with thinEngine, this._currentRenderTarget must be null when onBeforeUnbind is called\r\n\r\n        if (onBeforeUnbind) {\r\n            onBeforeUnbind();\r\n        }\r\n\r\n        this._currentRenderTarget = saveCRT;\r\n\r\n        this._endCurrentRenderPass();\r\n\r\n        if (texture.texture?.generateMipMaps && !disableGenerateMipMaps && !texture.isCube) {\r\n            this._generateMipmaps(texture.texture);\r\n        }\r\n\r\n        this._currentRenderTarget = null;\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log(\"frame #\" + (this as any)._count + \" - unBindFramebuffer - rtt name=\" + texture.label + \", internalTexture.uniqueId=\", texture.texture?.uniqueId);\r\n            }\r\n        }\r\n\r\n        this._mrtAttachments = [];\r\n        this._cacheRenderPipeline.setMRT([]);\r\n        this._cacheRenderPipeline.setMRTAttachments(this._mrtAttachments);\r\n    }\r\n\r\n    /**\r\n     * Unbind the current render target and bind the default framebuffer\r\n     */\r\n    public restoreDefaultFramebuffer(): void {\r\n        if (this._currentRenderTarget) {\r\n            this.unBindFramebuffer(this._currentRenderTarget);\r\n        } else if (!this._currentRenderPass) {\r\n            this._startMainRenderPass(false);\r\n        }\r\n\r\n        if (this._cachedViewport) {\r\n            this.setViewport(this._cachedViewport);\r\n        }\r\n\r\n        this.wipeCaches();\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Render\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setColorFormat(wrapper: IWebGPURenderPassWrapper): void {\r\n        const format = wrapper.colorAttachmentGPUTextures[0]?.format ?? null;\r\n        this._cacheRenderPipeline.setColorFormat(format);\r\n        if (this._colorFormat === format) {\r\n            return;\r\n        }\r\n        this._colorFormat = format;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setDepthTextureFormat(wrapper: IWebGPURenderPassWrapper): void {\r\n        this._cacheRenderPipeline.setDepthStencilFormat(wrapper.depthTextureFormat);\r\n        if (this._depthTextureFormat === wrapper.depthTextureFormat) {\r\n            return;\r\n        }\r\n        this._depthTextureFormat = wrapper.depthTextureFormat;\r\n    }\r\n\r\n    public setDitheringState(): void {\r\n        // Does not exist in WebGPU\r\n    }\r\n\r\n    public setRasterizerState(): void {\r\n        // Does not exist in WebGPU\r\n    }\r\n\r\n    /**\r\n     * Set various states to the webGL context\r\n     * @param culling defines culling state: true to enable culling, false to disable it\r\n     * @param zOffset defines the value to apply to zOffset (0 by default)\r\n     * @param force defines if states must be applied even if cache is up to date\r\n     * @param reverseSide defines if culling must be reversed (CCW if false, CW if true)\r\n     * @param cullBackFaces true to cull back faces, false to cull front faces (if culling is enabled)\r\n     * @param stencil stencil states to set\r\n     * @param zOffsetUnits defines the value to apply to zOffsetUnits (0 by default)\r\n     */\r\n    public setState(culling: boolean, zOffset: number = 0, force?: boolean, reverseSide = false, cullBackFaces?: boolean, stencil?: IStencilState, zOffsetUnits: number = 0): void {\r\n        // Culling\r\n        if (this._depthCullingState.cull !== culling || force) {\r\n            this._depthCullingState.cull = culling;\r\n        }\r\n\r\n        // Cull face\r\n        const cullFace = this.cullBackFaces ?? cullBackFaces ?? true ? 1 : 2;\r\n        if (this._depthCullingState.cullFace !== cullFace || force) {\r\n            this._depthCullingState.cullFace = cullFace;\r\n        }\r\n\r\n        // Z offset\r\n        this.setZOffset(zOffset);\r\n        this.setZOffsetUnits(zOffsetUnits);\r\n\r\n        // Front face\r\n        const frontFace = reverseSide ? (this._currentRenderTarget ? 1 : 2) : this._currentRenderTarget ? 2 : 1;\r\n        if (this._depthCullingState.frontFace !== frontFace || force) {\r\n            this._depthCullingState.frontFace = frontFace;\r\n        }\r\n\r\n        this._stencilStateComposer.stencilMaterial = stencil;\r\n    }\r\n\r\n    private _applyRenderPassChanges(bundleList: Nullable<WebGPUBundleList>): void {\r\n        const mustUpdateStencilRef = !this._stencilStateComposer.enabled ? false : this._mustUpdateStencilRef();\r\n        const mustUpdateBlendColor = !this._alphaState.alphaBlend ? false : this._mustUpdateBlendColor();\r\n\r\n        if (this._mustUpdateViewport()) {\r\n            this._applyViewport(bundleList);\r\n        }\r\n        if (this._mustUpdateScissor()) {\r\n            this._applyScissor(bundleList);\r\n        }\r\n        if (mustUpdateStencilRef) {\r\n            this._applyStencilRef(bundleList);\r\n        }\r\n        if (mustUpdateBlendColor) {\r\n            this._applyBlendColor(bundleList);\r\n        }\r\n    }\r\n\r\n    private _draw(drawType: number, fillMode: number, start: number, count: number, instancesCount: number): void {\r\n        const renderPass = this._getCurrentRenderPass();\r\n        const bundleList = this._bundleList;\r\n\r\n        this.applyStates();\r\n\r\n        const webgpuPipelineContext = this._currentEffect!._pipelineContext as WebGPUPipelineContext;\r\n\r\n        this.bindUniformBufferBase(this._currentRenderTarget ? this._ubInvertY : this._ubDontInvertY, 0, WebGPUShaderProcessor.InternalsUBOName);\r\n\r\n        if (webgpuPipelineContext.uniformBuffer) {\r\n            webgpuPipelineContext.uniformBuffer.update();\r\n            this.bindUniformBufferBase(webgpuPipelineContext.uniformBuffer.getBuffer()!, 0, WebGPUShaderProcessor.LeftOvertUBOName);\r\n        }\r\n\r\n        if (this._snapshotRendering.play) {\r\n            this._reportDrawCall();\r\n            return;\r\n        }\r\n\r\n        if (\r\n            !this.compatibilityMode &&\r\n            (this._currentDrawContext.isDirty(this._currentMaterialContext.updateId) || this._currentMaterialContext.isDirty || this._currentMaterialContext.forceBindGroupCreation)\r\n        ) {\r\n            this._currentDrawContext.fastBundle = undefined;\r\n        }\r\n\r\n        const useFastPath = !this.compatibilityMode && this._currentDrawContext.fastBundle;\r\n        let renderPass2: GPURenderPassEncoder | GPURenderBundleEncoder = renderPass;\r\n\r\n        if (useFastPath || this._snapshotRendering.record) {\r\n            this._applyRenderPassChanges(bundleList);\r\n            if (!this._snapshotRendering.record) {\r\n                this._counters.numBundleReuseNonCompatMode++;\r\n                if (this._currentDrawContext.indirectDrawBuffer) {\r\n                    this._currentDrawContext.setIndirectData(count, instancesCount || 1, start);\r\n                }\r\n                bundleList.addBundle(this._currentDrawContext.fastBundle);\r\n                this._reportDrawCall();\r\n                return;\r\n            }\r\n\r\n            renderPass2 = bundleList.getBundleEncoder(this._cacheRenderPipeline.colorFormats, this._depthTextureFormat, this.currentSampleCount); // for snapshot recording mode\r\n            bundleList.numDrawCalls++;\r\n        }\r\n\r\n        let textureState = 0;\r\n        if (this._currentMaterialContext.hasFloatOrDepthTextures) {\r\n            let bitVal = 1;\r\n            for (let i = 0; i < webgpuPipelineContext.shaderProcessingContext.textureNames.length; ++i) {\r\n                const textureName = webgpuPipelineContext.shaderProcessingContext.textureNames[i];\r\n                const texture = this._currentMaterialContext.textures[textureName]?.texture;\r\n                const textureIsDepth = texture && texture.format >= Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 && texture.format <= Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8;\r\n                if ((texture?.type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) || textureIsDepth) {\r\n                    textureState |= bitVal;\r\n                }\r\n                bitVal = bitVal << 1;\r\n            }\r\n        }\r\n\r\n        this._currentMaterialContext.textureState = textureState;\r\n\r\n        const pipeline = this._cacheRenderPipeline.getRenderPipeline(fillMode, this._currentEffect!, this.currentSampleCount, textureState);\r\n        const bindGroups = this._cacheBindGroups.getBindGroups(webgpuPipelineContext, this._currentDrawContext, this._currentMaterialContext);\r\n\r\n        if (!this._snapshotRendering.record) {\r\n            this._applyRenderPassChanges(!this.compatibilityMode ? bundleList : null);\r\n            if (!this.compatibilityMode) {\r\n                this._counters.numBundleCreationNonCompatMode++;\r\n                renderPass2 = this._device.createRenderBundleEncoder({\r\n                    colorFormats: this._cacheRenderPipeline.colorFormats,\r\n                    depthStencilFormat: this._depthTextureFormat,\r\n                    sampleCount: WebGPUTextureHelper.GetSample(this.currentSampleCount),\r\n                });\r\n            }\r\n        }\r\n\r\n        // bind pipeline\r\n        renderPass2.setPipeline(pipeline);\r\n\r\n        // bind index/vertex buffers\r\n        if (this._currentIndexBuffer) {\r\n            renderPass2.setIndexBuffer(\r\n                this._currentIndexBuffer.underlyingResource,\r\n                this._currentIndexBuffer!.is32Bits ? WebGPUConstants.IndexFormat.Uint32 : WebGPUConstants.IndexFormat.Uint16,\r\n                0\r\n            );\r\n        }\r\n\r\n        const vertexBuffers = this._cacheRenderPipeline.vertexBuffers;\r\n        for (let index = 0; index < vertexBuffers.length; index++) {\r\n            const vertexBuffer = vertexBuffers[index];\r\n\r\n            const buffer = vertexBuffer.effectiveBuffer;\r\n            if (buffer) {\r\n                renderPass2.setVertexBuffer(index, buffer.underlyingResource, vertexBuffer._validOffsetRange ? 0 : vertexBuffer.byteOffset);\r\n            }\r\n        }\r\n\r\n        // bind bind groups\r\n        for (let i = 0; i < bindGroups.length; i++) {\r\n            renderPass2.setBindGroup(i, bindGroups[i]);\r\n        }\r\n\r\n        // draw\r\n        const nonCompatMode = !this.compatibilityMode && !this._snapshotRendering.record;\r\n\r\n        if (nonCompatMode && this._currentDrawContext.indirectDrawBuffer) {\r\n            this._currentDrawContext.setIndirectData(count, instancesCount || 1, start);\r\n            if (drawType === 0) {\r\n                renderPass2.drawIndexedIndirect(this._currentDrawContext.indirectDrawBuffer, 0);\r\n            } else {\r\n                renderPass2.drawIndirect(this._currentDrawContext.indirectDrawBuffer, 0);\r\n            }\r\n        } else if (drawType === 0) {\r\n            renderPass2.drawIndexed(count, instancesCount || 1, start, 0, 0);\r\n        } else {\r\n            renderPass2.draw(count, instancesCount || 1, start, 0);\r\n        }\r\n\r\n        if (nonCompatMode) {\r\n            this._currentDrawContext.fastBundle = (renderPass2 as GPURenderBundleEncoder).finish();\r\n            bundleList.addBundle(this._currentDrawContext.fastBundle);\r\n        }\r\n\r\n        this._reportDrawCall();\r\n    }\r\n\r\n    /**\r\n     * Draw a list of indexed primitives\r\n     * @param fillMode defines the primitive to use\r\n     * @param indexStart defines the starting index\r\n     * @param indexCount defines the number of index to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawElementsType(fillMode: number, indexStart: number, indexCount: number, instancesCount: number = 1): void {\r\n        this._draw(0, fillMode, indexStart, indexCount, instancesCount);\r\n    }\r\n\r\n    /**\r\n     * Draw a list of unindexed primitives\r\n     * @param fillMode defines the primitive to use\r\n     * @param verticesStart defines the index of first vertex to draw\r\n     * @param verticesCount defines the count of vertices to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawArraysType(fillMode: number, verticesStart: number, verticesCount: number, instancesCount: number = 1): void {\r\n        this._currentIndexBuffer = null;\r\n        this._draw(1, fillMode, verticesStart, verticesCount, instancesCount);\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Dispose\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Dispose and release all associated resources\r\n     */\r\n    public dispose(): void {\r\n        this._isDisposed = true;\r\n        this._timestampQuery.dispose();\r\n        this._mainTexture?.destroy();\r\n        this._depthTexture?.destroy();\r\n        this._textureHelper.destroyDeferredTextures();\r\n        this._bufferManager.destroyDeferredBuffers();\r\n        this._device.destroy();\r\n        super.dispose();\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Misc\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Gets the current render width\r\n     * @param useScreen defines if screen size must be used (or the current render target if any)\r\n     * @returns a number defining the current render width\r\n     */\r\n    public getRenderWidth(useScreen = false): number {\r\n        if (!useScreen && this._currentRenderTarget) {\r\n            return this._currentRenderTarget.width;\r\n        }\r\n\r\n        return this._renderingCanvas?.width ?? 0;\r\n    }\r\n\r\n    /**\r\n     * Gets the current render height\r\n     * @param useScreen defines if screen size must be used (or the current render target if any)\r\n     * @returns a number defining the current render height\r\n     */\r\n    public getRenderHeight(useScreen = false): number {\r\n        if (!useScreen && this._currentRenderTarget) {\r\n            return this._currentRenderTarget.height;\r\n        }\r\n\r\n        return this._renderingCanvas?.height ?? 0;\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Errors\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Get the current error code of the WebGPU context\r\n     * @returns the error code\r\n     */\r\n    public getError(): number {\r\n        // TODO WEBGPU. from the webgpu errors.\r\n        return 0;\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Unused WebGPU\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public bindSamplers(): void {}\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindTextureDirectly(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if all created effects are ready\r\n     * @returns always true - No parallel shader compilation\r\n     */\r\n    public areAllEffectsReady(): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _executeWhenRenderingStateIsCompiled(pipelineContext: IPipelineContext, action: () => void) {\r\n        // No parallel shader compilation.\r\n        // No Async, so direct launch\r\n        action();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _isRenderingStateCompiled(): boolean {\r\n        // No parallel shader compilation.\r\n        return true;\r\n    }\r\n\r\n    /** @internal */\r\n    public _getUnpackAlignement(): number {\r\n        return 1;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _unpackFlipY() {}\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindUnboundFramebuffer() {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"_bindUnboundFramebuffer is not implementedin WebGPU! You probably want to use restoreDefaultFramebuffer or unBindFramebuffer instead\";\r\n    }\r\n\r\n    // TODO WEBGPU. All of the below should go once engine split with baseEngine.\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getSamplingParameters(): { min: number; mag: number } {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"_getSamplingParameters is not available in WebGPU\";\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public getUniforms(): Nullable<WebGLUniformLocation>[] {\r\n        return [];\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setIntArray(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setIntArray2(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setIntArray3(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setIntArray4(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setArray(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setArray2(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setArray3(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setArray4(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setMatrices(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setMatrix3x3(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setMatrix2x2(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setFloat(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setFloat2(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setFloat3(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public setFloat4(): boolean {\r\n        return false;\r\n    }\r\n}\r\n"]}