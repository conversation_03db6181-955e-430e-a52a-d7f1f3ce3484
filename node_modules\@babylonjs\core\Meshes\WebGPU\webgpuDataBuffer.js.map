{"version": 3, "file": "webgpuDataBuffer.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/WebGPU/webgpuDataBuffer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAGtD,gBAAgB;AAChB,MAAM,OAAO,gBAAiB,SAAQ,UAAU;IAM5C,YAAmB,QAAmB,EAAE,QAAQ,GAAG,CAAC;QAChD,KAAK,EAAE,CAAC;QAJZ,uFAAuF;QAChF,aAAQ,GAAG,CAAC,CAAC,CAAC;QAIjB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;CACJ", "sourcesContent": ["import { DataBuffer } from \"../../Buffers/dataBuffer\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/** @internal */\r\nexport class WebGPUDataBuffer extends DataBuffer {\r\n    private _buffer: Nullable<GPUBuffer>;\r\n\r\n    // Used to make sure the buffer is not recreated twice after a context loss/restoration\r\n    public engineId = -1;\r\n\r\n    public constructor(resource: GPUBuffer, capacity = 0) {\r\n        super();\r\n        this.capacity = capacity;\r\n        this._buffer = resource;\r\n    }\r\n\r\n    public get underlyingResource(): any {\r\n        return this._buffer;\r\n    }\r\n}\r\n"]}