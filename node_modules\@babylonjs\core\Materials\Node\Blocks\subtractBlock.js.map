{"version": 3, "file": "subtractBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/subtractBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAGvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD;;GAEG;AACH,MAAM,OAAO,aAAc,SAAQ,iBAAiB;IAChD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAE9C,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAC7E,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAC9E,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,YAAY,CAAC,CAAC;QAElF,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAEtC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAC/F,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,KAAK,CAAC,CAAC;IACnG,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEhC,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,sBAAsB,MAAM,IAAI,CAAC,KAAK,CAAC,sBAAsB,KAAK,CAAC;QAEnJ,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\n/**\r\n * Block used to subtract 2 vectors\r\n */\r\nexport class SubtractBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new SubtractBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerInput(\"left\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerInput(\"right\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.BasedOnInput);\r\n\r\n        this._outputs[0]._typeConnectionSource = this._inputs[0];\r\n        this._linkConnectionTypes(0, 1, true);\r\n\r\n        this._inputs[0].acceptedConnectionPointTypes.push(NodeMaterialBlockConnectionPointTypes.Float);\r\n        this._inputs[1].acceptedConnectionPointTypes.push(NodeMaterialBlockConnectionPointTypes.Float);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"SubtractBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the left operand input component\r\n     */\r\n    public get left(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the right operand input component\r\n     */\r\n    public get right(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const output = this._outputs[0];\r\n\r\n        state.compilationString += this._declareOutput(output, state) + ` = ${this.left.associatedVariableName} - ${this.right.associatedVariableName};\\n`;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SubtractBlock\", SubtractBlock);\r\n"]}