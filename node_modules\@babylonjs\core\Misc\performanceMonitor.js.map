{"version": 3, "file": "performanceMonitor.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/performanceMonitor.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD;;GAEG;AACH,MAAM,OAAO,kBAAkB;IAK3B;;;OAGG;IACH,YAAY,kBAA0B,EAAE;QARhC,aAAQ,GAAY,IAAI,CAAC;QAS7B,IAAI,CAAC,iBAAiB,GAAG,IAAI,cAAc,CAAC,eAAe,CAAC,CAAC;IACjE,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,SAAiB,aAAa,CAAC,GAAG;QACjD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE;YAC/B,MAAM,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAC1C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SAClC;QAED,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAElD,IAAI,OAAO,KAAK,CAAC,EAAE;YACf,OAAO,CAAC,CAAC;SACZ;QAED,OAAO,MAAM,GAAG,OAAO,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,qFAAqF;QACrF,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,KAAK;QACR,qFAAqF;QACrF,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,aAAa;QACb,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;IACnC,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,cAAc;IAevB;;;OAGG;IACH,YAAY,MAAc;QACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,KAAK,CAAS,MAAM,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,GAAG,CAAC,CAAS;QAChB,kEAAkE;QAClE,IAAI,KAAa,CAAC;QAElB,iDAAiD;QACjD,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACpB,kCAAkC;YAClC,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,KAAK,GAAG,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;SACpD;aAAM;YACH,IAAI,CAAC,YAAY,EAAE,CAAC;SACvB;QAED,uBAAuB;QACvB,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QACzB,IAAI,CAAC,OAAO,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;QAC1C,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;QAEvC,sBAAsB;QACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAEnD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,sBAAsB;IAC7D,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,CAAS;QACpB,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACrD,OAAO,CAAC,CAAC;SACZ;QAED,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACO,aAAa,CAAC,CAAS;QAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACjC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACnC,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { PrecisionDate } from \"./precisionDate\";\r\n\r\n/**\r\n * Performance monitor tracks rolling average frame-time and frame-time variance over a user defined sliding-window\r\n */\r\nexport class PerformanceMonitor {\r\n    private _enabled: boolean = true;\r\n    private _rollingFrameTime: RollingAverage;\r\n    private _lastFrameTimeMs: Nullable<number>;\r\n\r\n    /**\r\n     * constructor\r\n     * @param frameSampleSize The number of samples required to saturate the sliding window\r\n     */\r\n    constructor(frameSampleSize: number = 30) {\r\n        this._rollingFrameTime = new RollingAverage(frameSampleSize);\r\n    }\r\n\r\n    /**\r\n     * Samples current frame\r\n     * @param timeMs A timestamp in milliseconds of the current frame to compare with other frames\r\n     */\r\n    public sampleFrame(timeMs: number = PrecisionDate.Now) {\r\n        if (!this._enabled) {\r\n            return;\r\n        }\r\n\r\n        if (this._lastFrameTimeMs != null) {\r\n            const dt = timeMs - this._lastFrameTimeMs;\r\n            this._rollingFrameTime.add(dt);\r\n        }\r\n\r\n        this._lastFrameTimeMs = timeMs;\r\n    }\r\n\r\n    /**\r\n     * Returns the average frame time in milliseconds over the sliding window (or the subset of frames sampled so far)\r\n     */\r\n    public get averageFrameTime(): number {\r\n        return this._rollingFrameTime.average;\r\n    }\r\n\r\n    /**\r\n     * Returns the variance frame time in milliseconds over the sliding window (or the subset of frames sampled so far)\r\n     */\r\n    public get averageFrameTimeVariance(): number {\r\n        return this._rollingFrameTime.variance;\r\n    }\r\n\r\n    /**\r\n     * Returns the frame time of the most recent frame\r\n     */\r\n    public get instantaneousFrameTime(): number {\r\n        return this._rollingFrameTime.history(0);\r\n    }\r\n\r\n    /**\r\n     * Returns the average framerate in frames per second over the sliding window (or the subset of frames sampled so far)\r\n     */\r\n    public get averageFPS(): number {\r\n        return 1000.0 / this._rollingFrameTime.average;\r\n    }\r\n\r\n    /**\r\n     * Returns the average framerate in frames per second using the most recent frame time\r\n     */\r\n    public get instantaneousFPS(): number {\r\n        const history = this._rollingFrameTime.history(0);\r\n\r\n        if (history === 0) {\r\n            return 0;\r\n        }\r\n\r\n        return 1000.0 / history;\r\n    }\r\n\r\n    /**\r\n     * Returns true if enough samples have been taken to completely fill the sliding window\r\n     */\r\n    public get isSaturated(): boolean {\r\n        return this._rollingFrameTime.isSaturated();\r\n    }\r\n\r\n    /**\r\n     * Enables contributions to the sliding window sample set\r\n     */\r\n    public enable() {\r\n        this._enabled = true;\r\n    }\r\n\r\n    /**\r\n     * Disables contributions to the sliding window sample set\r\n     * Samples will not be interpolated over the disabled period\r\n     */\r\n    public disable() {\r\n        this._enabled = false;\r\n        //clear last sample to avoid interpolating over the disabled period when next enabled\r\n        this._lastFrameTimeMs = null;\r\n    }\r\n\r\n    /**\r\n     * Returns true if sampling is enabled\r\n     */\r\n    public get isEnabled(): boolean {\r\n        return this._enabled;\r\n    }\r\n\r\n    /**\r\n     * Resets performance monitor\r\n     */\r\n    public reset() {\r\n        //clear last sample to avoid interpolating over the disabled period when next enabled\r\n        this._lastFrameTimeMs = null;\r\n        //wipe record\r\n        this._rollingFrameTime.reset();\r\n    }\r\n}\r\n\r\n/**\r\n * RollingAverage\r\n *\r\n * Utility to efficiently compute the rolling average and variance over a sliding window of samples\r\n */\r\nexport class RollingAverage {\r\n    /**\r\n     * Current average\r\n     */\r\n    public average: number;\r\n    /**\r\n     * Current variance\r\n     */\r\n    public variance: number;\r\n\r\n    protected _samples: Array<number>;\r\n    protected _sampleCount: number;\r\n    protected _pos: number;\r\n    protected _m2: number; //sum of squares of differences from the (current) mean\r\n\r\n    /**\r\n     * constructor\r\n     * @param length The number of samples required to saturate the sliding window\r\n     */\r\n    constructor(length: number) {\r\n        this._samples = new Array<number>(length);\r\n        this.reset();\r\n    }\r\n\r\n    /**\r\n     * Adds a sample to the sample set\r\n     * @param v The sample value\r\n     */\r\n    public add(v: number) {\r\n        //http://en.wikipedia.org/wiki/Algorithms_for_calculating_variance\r\n        let delta: number;\r\n\r\n        //we need to check if we've already wrapped round\r\n        if (this.isSaturated()) {\r\n            //remove bottom of stack from mean\r\n            const bottomValue = this._samples[this._pos];\r\n            delta = bottomValue - this.average;\r\n            this.average -= delta / (this._sampleCount - 1);\r\n            this._m2 -= delta * (bottomValue - this.average);\r\n        } else {\r\n            this._sampleCount++;\r\n        }\r\n\r\n        //add new value to mean\r\n        delta = v - this.average;\r\n        this.average += delta / this._sampleCount;\r\n        this._m2 += delta * (v - this.average);\r\n\r\n        //set the new variance\r\n        this.variance = this._m2 / (this._sampleCount - 1);\r\n\r\n        this._samples[this._pos] = v;\r\n        this._pos++;\r\n\r\n        this._pos %= this._samples.length; //positive wrap around\r\n    }\r\n\r\n    /**\r\n     * Returns previously added values or null if outside of history or outside the sliding window domain\r\n     * @param i Index in history. For example, pass 0 for the most recent value and 1 for the value before that\r\n     * @returns Value previously recorded with add() or null if outside of range\r\n     */\r\n    public history(i: number): number {\r\n        if (i >= this._sampleCount || i >= this._samples.length) {\r\n            return 0;\r\n        }\r\n\r\n        const i0 = this._wrapPosition(this._pos - 1.0);\r\n        return this._samples[this._wrapPosition(i0 - i)];\r\n    }\r\n\r\n    /**\r\n     * Returns true if enough samples have been taken to completely fill the sliding window\r\n     * @returns true if sample-set saturated\r\n     */\r\n    public isSaturated(): boolean {\r\n        return this._sampleCount >= this._samples.length;\r\n    }\r\n\r\n    /**\r\n     * Resets the rolling average (equivalent to 0 samples taken so far)\r\n     */\r\n    public reset() {\r\n        this.average = 0;\r\n        this.variance = 0;\r\n        this._sampleCount = 0;\r\n        this._pos = 0;\r\n        this._m2 = 0;\r\n    }\r\n\r\n    /**\r\n     * Wraps a value around the sample range boundaries\r\n     * @param i Position in sample range, for example if the sample length is 5, and i is -3, then 2 will be returned.\r\n     * @returns Wrapped position in sample range\r\n     */\r\n    protected _wrapPosition(i: number): number {\r\n        const max = this._samples.length;\r\n        return ((i % max) + max) % max;\r\n    }\r\n}\r\n"]}