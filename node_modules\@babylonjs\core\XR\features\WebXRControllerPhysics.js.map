{"version": 3, "file": "WebXRControllerPhysics.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRControllerPhysics.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAE9D,OAAO,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AAInE,OAAO,EAAE,YAAY,EAAE,MAAM,qCAAqC,CAAC;AACnE,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AACjF,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C;;GAEG;AACH,MAAM,OAAO,8BAA8B;CAwD1C;AAED;;;GAGG;AACH,MAAM,OAAO,sBAAuB,SAAQ,oBAAoB;IAqCpD,sBAAsB,CAAC,YAA8B;QACzD,MAAM,YAAY,GAAW,IAAI,CAAC,QAAQ,CAAC,iBAAkB,CAAC,YAAY,IAAI,eAAe,CAAC,cAAc,CAAC;QAC7G,MAAM,YAAY,GAA8D,IAAI,CAAC,QAAQ,CAAC,iBAAkB,CAAC,YAAY,IAAI,GAAG,CAAC;QACrI,MAAM,YAAY,GAAG,YAAY,CAAC,gBAAgB,GAAG,YAAY,CAAC,QAAQ,EAAE;YACxE,SAAS,EAAE,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK;YAC/E,SAAS,EAAE,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM;YAChF,SAAS,EAAE,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK;SAClF,CAAC,CAAC;QACH,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QACzC,YAAY,CAAC,UAAU,GAAG,KAAK,CAAC;QAChC,YAAY,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;QACnD,MAAM,cAAc,GAAG,YAAY,CAAC,IAAI,IAAI,YAAY,CAAC,OAAO,CAAC;QACjE,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACxD,YAAY,CAAC,kBAAmB,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAmB,CAAC,CAAC;QAC9E,MAAM,QAAQ,GAAG,IAAI,eAAe,CAAC,YAAY,EAAE,YAAY,EAAE;YAC7D,IAAI,EAAE,CAAC;YACP,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB;SACrC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG;YACvC,YAAY;YACZ,QAAQ;YACR,YAAY;SACf,CAAC;IACN,CAAC;IA+BD;;;;OAIG;IACH,YACI,iBAAsC,EACrB,QAAwC;QAEzD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFR,aAAQ,GAAR,QAAQ,CAAgC;QAjGrD,sBAAiB,GAAG,CAAC,YAA8B,EAAE,EAAE;YAC3D,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;gBAC1C,mBAAmB;gBACnB,OAAO;aACV;YACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,EAAE,EAAE;gBAClD,MAAM,CAAC,IAAI,CAAC,2EAA2E,CAAC,CAAC;aAC5F;YACD,uDAAuD;YACvD,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAkB,CAAC,iBAAiB,IAAI,YAAY,CAAC,WAAW,CAAC,OAAO,EAAE;gBACxF,YAAY,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAAE,EAAE;oBACvE,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,EAAE;wBAC5C,gBAAgB,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,EAAE;4BAClD,MAAM,QAAQ,GAAG,IAAI,eAAe,CAAC,gBAAgB,CAAC,QAAS,EAAE,eAAe,CAAC,YAAY,EAAE;gCAC3F,IAAI,EAAE,CAAC;gCACP,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB;6BACrC,CAAC,CAAC;4BAEH,MAAM,cAAc,GAAG,YAAY,CAAC,IAAI,IAAI,YAAY,CAAC,OAAO,CAAC;4BACjE,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG;gCACvC,YAAY;gCACZ,QAAQ;gCACR,MAAM,EAAE,cAAc,CAAC,QAAQ,CAAC,KAAK,EAAE;gCACvC,WAAW,EAAE,cAAc,CAAC,kBAAmB,CAAC,KAAK,EAAE;6BAC1D,CAAC;wBACN,CAAC,CAAC,CAAC;qBACN;yBAAM;wBACH,gEAAgE;wBAChE,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;qBAC7C;gBACL,CAAC,CAAC,CAAC;aACN;iBAAM;gBACH,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;aAC7C;QACL,CAAC,CAAC;QA2BM,iBAAY,GAShB,EAAE,CAAC;QACC,eAAU,GAAG,KAAK,CAAC;QACnB,WAAM,GAAW,CAAC,CAAC;QAGnB,mBAAc,GAAW,CAAC,CAAC;QAC3B,mBAAc,GAAe,IAAI,UAAU,EAAE,CAAC;QAC9C,eAAU,GAAY,IAAI,OAAO,EAAE,CAAC;QAuBxC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE;YAClC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,EAAE,CAAC;SACxC;IACL,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YACpD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YACvD,IAAI,cAAc,CAAC,YAAY,EAAE;gBAC7B,cAAc,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC;aAChD;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,YAA8B;QAC/C,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IACzC,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;YACxB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,2BAA2B,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,6BAA6B,EAAE,CAAC,UAAU,EAAE,EAAE;YAC3F,wBAAwB;YACxB,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE;YACrC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,IAAI;gBAClD,YAAY,EAAE,eAAe,CAAC,cAAc;gBAC5C,WAAW,EAAE,GAAG;gBAChB,YAAY,EAAE,GAAG;aACpB,CAAC;YACF,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,GAAG,CAAC;YAChD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,cAAc,EAAE;gBAC7C,SAAS,EAAE,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK;gBAC/E,SAAS,EAAE,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM;gBAChF,SAAS,EAAE,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK;aAClF,CAAC,CAAC;YACH,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;YACxD,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC;YACpC,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC;SAC/G;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YACpD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;SAC/B;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;;;OAKG;IACI,wBAAwB,CAAC,UAAqC;QACjE,MAAM,EAAE,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC7E,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EAAE;YACvB,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC;SACzC;aAAM;YACH,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAED;;;;;;;OAOG;IACI,oBAAoB,CAAC,aAK3B;QACG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG;YAC9B,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB;YAClC,GAAG,aAAa;SACnB,CAAC;IACN,CAAC;IAES,UAAU,CAAC,QAAa;QAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC;QAC5E,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;QAC9D,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC5C,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YACnF,IAAI,CAAC,YAAY,CAAC,kBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAChG,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,cAAc,EAAE;gBAClE,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC3E,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;gBACtC,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAC5D;YACD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,eAAe,EAAE;gBACnE,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,eAAe,CAAC;gBAC5E,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;gBACtC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAC7D;SACJ;QACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YACpD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YACvD,MAAM,cAAc,GAAG,cAAc,CAAC,YAAY,CAAC,IAAI,IAAI,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC;YAC/F,MAAM,gBAAgB,GAAG,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,YAAa,CAAC,QAAQ,CAAC;YACxF,IAAI,cAAc,CAAC,YAAY,CAAC,WAAW,EAAE,cAAc,EAAE;gBACzD,MAAM,EAAE,GAAG,cAAc,CAAC,YAAY,CAAC,WAAW,CAAC,cAAc,CAAC;gBAClE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;gBACtC,cAAc,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAC9D;iBAAM;gBACH,cAAc,CAAC,QAAQ,CAAC,aAAa,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACzE,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;gBACjD,cAAc,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAC9D;YACD,gBAAgB,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;aAC3C;YAED,MAAM,kBAAkB,GAAG,cAAc,CAAC,WAAW,IAAI,cAAc,CAAC,YAAa,CAAC,kBAAmB,CAAC;YAC1G,IAAI,cAAc,CAAC,YAAY,CAAC,WAAW,EAAE,eAAe,EAAE;gBAC1D,MAAM,EAAE,GAAG,cAAc,CAAC,YAAY,CAAC,WAAW,CAAC,eAAe,CAAC;gBACnE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;gBACtC,cAAc,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAC/D;iBAAM;gBACH,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,cAAc,CAAC,kBAAmB,CAAC,EAAE;oBAC3E,uGAAuG;oBACvG,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,aAAa,CAAC,cAAc,CAAC,kBAAmB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;oBAC7G,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CACjB,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAChJ,CAAC;oBACF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;oBACzF,0BAA0B;oBAC1B,IAAI,GAAG,GAAG,KAAK,EAAE;wBACb,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;qBACnC;yBAAM;wBACH,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;wBACzD,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;qBACtE;oBACD,cAAc,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBAC/D;aACJ;YACD,kBAAkB,CAAC,QAAQ,CAAC,cAAc,CAAC,kBAAmB,CAAC,CAAC;YAChE,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC;aACjE;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,iBAAiB,CAAC,oBAA4B;QAClD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QAC/D,IAAI,CAAC,cAAc,EAAE;YACjB,OAAO;SACV;QACD,IAAI,cAAc,CAAC,YAAY,EAAE;YAC7B,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;SACzC;QACD,sBAAsB;QACtB,OAAO,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;IACnD,CAAC;;AAtOD;;GAEG;AACoB,2BAAI,GAAG,gBAAgB,CAAC,mBAAmB,AAAvC,CAAwC;AACnE;;;;GAIG;AACoB,8BAAO,GAAG,CAAC,AAAJ,CAAK;AAgOvC,qBAAqB;AACrB,oBAAoB,CAAC,eAAe,CAChC,sBAAsB,CAAC,IAAI,EAC3B,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,sBAAsB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AACvE,CAAC,EACD,sBAAsB,CAAC,OAAO,EAC9B,IAAI,CACP,CAAC", "sourcesContent": ["import { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport { Vector3, Quaternion } from \"../../Maths/math.vector\";\r\nimport type { WebXRInputSource } from \"../webXRInputSource\";\r\nimport { PhysicsImpostor } from \"../../Physics/v1/physicsImpostor\";\r\nimport type { WebXRInput } from \"../webXRInput\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { CreateSphere } from \"../../Meshes/Builders/sphereBuilder\";\r\nimport { WebXRFeatureName, WebXRFeaturesManager } from \"../webXRFeaturesManager\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * Options for the controller physics feature\r\n */\r\nexport class IWebXRControllerPhysicsOptions {\r\n    /**\r\n     * Should the headset get its own impostor\r\n     */\r\n    enableHeadsetImpostor?: boolean;\r\n    /**\r\n     * Optional parameters for the headset impostor\r\n     */\r\n    headsetImpostorParams?: {\r\n        /**\r\n         * The type of impostor to create. Default is sphere\r\n         */\r\n        impostorType: number;\r\n        /**\r\n         * the size of the impostor. Defaults to 10cm\r\n         */\r\n        impostorSize?: number | { width: number; height: number; depth: number };\r\n        /**\r\n         * Friction definitions\r\n         */\r\n        friction?: number;\r\n        /**\r\n         * Restitution\r\n         */\r\n        restitution?: number;\r\n    };\r\n    /**\r\n     * The physics properties of the future impostors\r\n     */\r\n    physicsProperties?: {\r\n        /**\r\n         * If set to true, a mesh impostor will be created when the controller mesh was loaded\r\n         * Note that this requires a physics engine that supports mesh impostors!\r\n         */\r\n        useControllerMesh?: boolean;\r\n        /**\r\n         * The type of impostor to create. Default is sphere\r\n         */\r\n        impostorType?: number;\r\n        /**\r\n         * the size of the impostor. Defaults to 10cm\r\n         */\r\n        impostorSize?: number | { width: number; height: number; depth: number };\r\n        /**\r\n         * Friction definitions\r\n         */\r\n        friction?: number;\r\n        /**\r\n         * Restitution\r\n         */\r\n        restitution?: number;\r\n    };\r\n    /**\r\n     * the xr input to use with this pointer selection\r\n     */\r\n    public xrInput: WebXRInput;\r\n}\r\n\r\n/**\r\n * Add physics impostor to your webxr controllers,\r\n * including naive calculation of their linear and angular velocity\r\n */\r\nexport class WebXRControllerPhysics extends WebXRAbstractFeature {\r\n    private _attachController = (xrController: WebXRInputSource) => {\r\n        if (this._controllers[xrController.uniqueId]) {\r\n            // already attached\r\n            return;\r\n        }\r\n        if (!this._xrSessionManager.scene.isPhysicsEnabled()) {\r\n            Logger.Warn(\"physics engine not enabled, skipped. Please add this controller manually.\");\r\n        }\r\n        // if no motion controller available, create impostors!\r\n        if (this._options.physicsProperties!.useControllerMesh && xrController.inputSource.gamepad) {\r\n            xrController.onMotionControllerInitObservable.addOnce((motionController) => {\r\n                if (!motionController._doNotLoadControllerMesh) {\r\n                    motionController.onModelLoadedObservable.addOnce(() => {\r\n                        const impostor = new PhysicsImpostor(motionController.rootMesh!, PhysicsImpostor.MeshImpostor, {\r\n                            mass: 0,\r\n                            ...this._options.physicsProperties,\r\n                        });\r\n\r\n                        const controllerMesh = xrController.grip || xrController.pointer;\r\n                        this._controllers[xrController.uniqueId] = {\r\n                            xrController,\r\n                            impostor,\r\n                            oldPos: controllerMesh.position.clone(),\r\n                            oldRotation: controllerMesh.rotationQuaternion!.clone(),\r\n                        };\r\n                    });\r\n                } else {\r\n                    // This controller isn't using a model, create impostors instead\r\n                    this._createPhysicsImpostor(xrController);\r\n                }\r\n            });\r\n        } else {\r\n            this._createPhysicsImpostor(xrController);\r\n        }\r\n    };\r\n\r\n    private _createPhysicsImpostor(xrController: WebXRInputSource) {\r\n        const impostorType: number = this._options.physicsProperties!.impostorType || PhysicsImpostor.SphereImpostor;\r\n        const impostorSize: number | { width: number; height: number; depth: number } = this._options.physicsProperties!.impostorSize || 0.1;\r\n        const impostorMesh = CreateSphere(\"impostor-mesh-\" + xrController.uniqueId, {\r\n            diameterX: typeof impostorSize === \"number\" ? impostorSize : impostorSize.width,\r\n            diameterY: typeof impostorSize === \"number\" ? impostorSize : impostorSize.height,\r\n            diameterZ: typeof impostorSize === \"number\" ? impostorSize : impostorSize.depth,\r\n        });\r\n        impostorMesh.isVisible = this._debugMode;\r\n        impostorMesh.isPickable = false;\r\n        impostorMesh.rotationQuaternion = new Quaternion();\r\n        const controllerMesh = xrController.grip || xrController.pointer;\r\n        impostorMesh.position.copyFrom(controllerMesh.position);\r\n        impostorMesh.rotationQuaternion!.copyFrom(controllerMesh.rotationQuaternion!);\r\n        const impostor = new PhysicsImpostor(impostorMesh, impostorType, {\r\n            mass: 0,\r\n            ...this._options.physicsProperties,\r\n        });\r\n        this._controllers[xrController.uniqueId] = {\r\n            xrController,\r\n            impostor,\r\n            impostorMesh,\r\n        };\r\n    }\r\n\r\n    private _controllers: {\r\n        [id: string]: {\r\n            xrController: WebXRInputSource;\r\n            impostorMesh?: AbstractMesh;\r\n            impostor: PhysicsImpostor;\r\n            oldPos?: Vector3;\r\n            oldSpeed?: Vector3;\r\n            oldRotation?: Quaternion;\r\n        };\r\n    } = {};\r\n    private _debugMode = false;\r\n    private _delta: number = 0;\r\n    private _headsetImpostor?: PhysicsImpostor;\r\n    private _headsetMesh?: AbstractMesh;\r\n    private _lastTimestamp: number = 0;\r\n    private _tmpQuaternion: Quaternion = new Quaternion();\r\n    private _tmpVector: Vector3 = new Vector3();\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.PHYSICS_CONTROLLERS;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the webxr specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * Construct a new Controller Physics Feature\r\n     * @param _xrSessionManager the corresponding xr session manager\r\n     * @param _options options to create this feature with\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        private readonly _options: IWebXRControllerPhysicsOptions\r\n    ) {\r\n        super(_xrSessionManager);\r\n        if (!this._options.physicsProperties) {\r\n            this._options.physicsProperties = {};\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * enable debugging - will show console outputs and the impostor mesh\r\n     */\r\n    public _enablePhysicsDebug() {\r\n        this._debugMode = true;\r\n        Object.keys(this._controllers).forEach((controllerId) => {\r\n            const controllerData = this._controllers[controllerId];\r\n            if (controllerData.impostorMesh) {\r\n                controllerData.impostorMesh.isVisible = true;\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Manually add a controller (if no xrInput was provided or physics engine was not enabled)\r\n     * @param xrController the controller to add\r\n     */\r\n    public addController(xrController: WebXRInputSource) {\r\n        this._attachController(xrController);\r\n    }\r\n\r\n    /**\r\n     * attach this feature\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public attach(): boolean {\r\n        if (!super.attach()) {\r\n            return false;\r\n        }\r\n\r\n        if (!this._options.xrInput) {\r\n            return true;\r\n        }\r\n\r\n        this._options.xrInput.controllers.forEach(this._attachController);\r\n        this._addNewAttachObserver(this._options.xrInput.onControllerAddedObservable, this._attachController);\r\n        this._addNewAttachObserver(this._options.xrInput.onControllerRemovedObservable, (controller) => {\r\n            // REMOVE the controller\r\n            this._detachController(controller.uniqueId);\r\n        });\r\n\r\n        if (this._options.enableHeadsetImpostor) {\r\n            const params = this._options.headsetImpostorParams || {\r\n                impostorType: PhysicsImpostor.SphereImpostor,\r\n                restitution: 0.8,\r\n                impostorSize: 0.3,\r\n            };\r\n            const impostorSize = params.impostorSize || 0.3;\r\n            this._headsetMesh = CreateSphere(\"headset-mesh\", {\r\n                diameterX: typeof impostorSize === \"number\" ? impostorSize : impostorSize.width,\r\n                diameterY: typeof impostorSize === \"number\" ? impostorSize : impostorSize.height,\r\n                diameterZ: typeof impostorSize === \"number\" ? impostorSize : impostorSize.depth,\r\n            });\r\n            this._headsetMesh.rotationQuaternion = new Quaternion();\r\n            this._headsetMesh.isVisible = false;\r\n            this._headsetImpostor = new PhysicsImpostor(this._headsetMesh, params.impostorType, { mass: 0, ...params });\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * detach this feature.\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n\r\n        Object.keys(this._controllers).forEach((controllerId) => {\r\n            this._detachController(controllerId);\r\n        });\r\n\r\n        if (this._headsetMesh) {\r\n            this._headsetMesh.dispose();\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Get the headset impostor, if enabled\r\n     * @returns the impostor\r\n     */\r\n    public getHeadsetImpostor() {\r\n        return this._headsetImpostor;\r\n    }\r\n\r\n    /**\r\n     * Get the physics impostor of a specific controller.\r\n     * The impostor is not attached to a mesh because a mesh for each controller is not obligatory\r\n     * @param controller the controller or the controller id of which to get the impostor\r\n     * @returns the impostor or null\r\n     */\r\n    public getImpostorForController(controller: WebXRInputSource | string): Nullable<PhysicsImpostor> {\r\n        const id = typeof controller === \"string\" ? controller : controller.uniqueId;\r\n        if (this._controllers[id]) {\r\n            return this._controllers[id].impostor;\r\n        } else {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the physics properties provided in the constructor\r\n     * @param newProperties the new properties object\r\n     * @param newProperties.impostorType\r\n     * @param newProperties.impostorSize\r\n     * @param newProperties.friction\r\n     * @param newProperties.restitution\r\n     */\r\n    public setPhysicsProperties(newProperties: {\r\n        impostorType?: number;\r\n        impostorSize?: number | { width: number; height: number; depth: number };\r\n        friction?: number;\r\n        restitution?: number;\r\n    }) {\r\n        this._options.physicsProperties = {\r\n            ...this._options.physicsProperties,\r\n            ...newProperties,\r\n        };\r\n    }\r\n\r\n    protected _onXRFrame(_xrFrame: any): void {\r\n        this._delta = this._xrSessionManager.currentTimestamp - this._lastTimestamp;\r\n        this._lastTimestamp = this._xrSessionManager.currentTimestamp;\r\n        if (this._headsetMesh && this._headsetImpostor) {\r\n            this._headsetMesh.position.copyFrom(this._options.xrInput.xrCamera.globalPosition);\r\n            this._headsetMesh.rotationQuaternion!.copyFrom(this._options.xrInput.xrCamera.absoluteRotation);\r\n            if (this._options.xrInput.xrCamera._lastXRViewerPose?.linearVelocity) {\r\n                const lv = this._options.xrInput.xrCamera._lastXRViewerPose.linearVelocity;\r\n                this._tmpVector.set(lv.x, lv.y, lv.z);\r\n                this._headsetImpostor.setLinearVelocity(this._tmpVector);\r\n            }\r\n            if (this._options.xrInput.xrCamera._lastXRViewerPose?.angularVelocity) {\r\n                const av = this._options.xrInput.xrCamera._lastXRViewerPose.angularVelocity;\r\n                this._tmpVector.set(av.x, av.y, av.z);\r\n                this._headsetImpostor.setAngularVelocity(this._tmpVector);\r\n            }\r\n        }\r\n        Object.keys(this._controllers).forEach((controllerId) => {\r\n            const controllerData = this._controllers[controllerId];\r\n            const controllerMesh = controllerData.xrController.grip || controllerData.xrController.pointer;\r\n            const comparedPosition = controllerData.oldPos || controllerData.impostorMesh!.position;\r\n            if (controllerData.xrController._lastXRPose?.linearVelocity) {\r\n                const lv = controllerData.xrController._lastXRPose.linearVelocity;\r\n                this._tmpVector.set(lv.x, lv.y, lv.z);\r\n                controllerData.impostor.setLinearVelocity(this._tmpVector);\r\n            } else {\r\n                controllerMesh.position.subtractToRef(comparedPosition, this._tmpVector);\r\n                this._tmpVector.scaleInPlace(1000 / this._delta);\r\n                controllerData.impostor.setLinearVelocity(this._tmpVector);\r\n            }\r\n            comparedPosition.copyFrom(controllerMesh.position);\r\n            if (this._debugMode) {\r\n                Logger.Log([this._tmpVector, \"linear\"]);\r\n            }\r\n\r\n            const comparedQuaternion = controllerData.oldRotation || controllerData.impostorMesh!.rotationQuaternion!;\r\n            if (controllerData.xrController._lastXRPose?.angularVelocity) {\r\n                const av = controllerData.xrController._lastXRPose.angularVelocity;\r\n                this._tmpVector.set(av.x, av.y, av.z);\r\n                controllerData.impostor.setAngularVelocity(this._tmpVector);\r\n            } else {\r\n                if (!comparedQuaternion.equalsWithEpsilon(controllerMesh.rotationQuaternion!)) {\r\n                    // roughly based on this - https://www.gamedev.net/forums/topic/347752-quaternion-and-angular-velocity/\r\n                    comparedQuaternion.conjugateInPlace().multiplyToRef(controllerMesh.rotationQuaternion!, this._tmpQuaternion);\r\n                    const len = Math.sqrt(\r\n                        this._tmpQuaternion.x * this._tmpQuaternion.x + this._tmpQuaternion.y * this._tmpQuaternion.y + this._tmpQuaternion.z * this._tmpQuaternion.z\r\n                    );\r\n                    this._tmpVector.set(this._tmpQuaternion.x, this._tmpQuaternion.y, this._tmpQuaternion.z);\r\n                    // define a better epsilon\r\n                    if (len < 0.001) {\r\n                        this._tmpVector.scaleInPlace(2);\r\n                    } else {\r\n                        const angle = 2 * Math.atan2(len, this._tmpQuaternion.w);\r\n                        this._tmpVector.scaleInPlace(angle / (len * (this._delta / 1000)));\r\n                    }\r\n                    controllerData.impostor.setAngularVelocity(this._tmpVector);\r\n                }\r\n            }\r\n            comparedQuaternion.copyFrom(controllerMesh.rotationQuaternion!);\r\n            if (this._debugMode) {\r\n                Logger.Log([this._tmpVector, this._tmpQuaternion, \"angular\"]);\r\n            }\r\n        });\r\n    }\r\n\r\n    private _detachController(xrControllerUniqueId: string) {\r\n        const controllerData = this._controllers[xrControllerUniqueId];\r\n        if (!controllerData) {\r\n            return;\r\n        }\r\n        if (controllerData.impostorMesh) {\r\n            controllerData.impostorMesh.dispose();\r\n        }\r\n        // remove from the map\r\n        delete this._controllers[xrControllerUniqueId];\r\n    }\r\n}\r\n\r\n//register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRControllerPhysics.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRControllerPhysics(xrSessionManager, options);\r\n    },\r\n    WebXRControllerPhysics.Version,\r\n    true\r\n);\r\n"]}