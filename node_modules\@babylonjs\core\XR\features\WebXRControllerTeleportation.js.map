{"version": 3, "file": "WebXRControllerTeleportation.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRControllerTeleportation.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAEjF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAMnD,OAAO,EAAE,wBAAwB,EAAE,MAAM,8CAA8C,CAAC;AAExF,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAC9D,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;AAExC,OAAO,EAAE,cAAc,EAAE,MAAM,yCAAyC,CAAC;AACzE,OAAO,EAAE,cAAc,EAAE,MAAM,uCAAuC,CAAC;AACvE,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACnE,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AACvD,OAAO,EAAE,IAAI,EAAE,MAAM,uBAAuB,CAAC;AAC7C,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AACpE,OAAO,EAAE,YAAY,EAAE,MAAM,qCAAqC,CAAC;AACnE,OAAO,EAAE,WAAW,EAAE,MAAM,oCAAoC,CAAC;AAEjE,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,WAAW,EAAE,MAAM,oCAAoC,CAAC;AACjE,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAExD,OAAO,EAAE,oBAAoB,EAAE,MAAM,sCAAsC,CAAC;AAC5E,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,gBAAgB,EAAE,MAAM,kBAAkB,CAAC;AAgIpD;;;;GAIG;AACH,MAAM,OAAO,kCAAmC,SAAQ,oBAAoB;IAkHxE;;;OAGG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,IAAW,eAAe,CAAC,OAAgB;QACvC,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;QAEhC,IAAI,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE;YACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,cAAc,CAAC,CAAC;YACrH,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;gBACb,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;aACnC;SACJ;IACL,CAAC;IAED;;OAEG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,IAAI,CAAC;IACzD,CAAC;IAED;;;;OAIG;IACH,YACI,iBAAsC,EAC9B,QAAoC;QAE5C,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFjB,aAAQ,GAAR,QAAQ,CAA4B;QAtJxC,iBAAY,GAiBhB,EAAE,CAAC;QAMC,oBAAe,GAAY,KAAK,CAAC;QAGjC,uBAAkB,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5C,YAAO,GAAG,IAAI,GAAG,CAAC,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,CAAC,CAAC;QAChD,eAAU,GAAG,IAAI,OAAO,EAAE,CAAC;QAC3B,mBAAc,GAAG,IAAI,UAAU,EAAE,CAAC;QAClC,wBAAmB,GAAiF,IAAI,CAAC;QAEjH;;;WAGG;QACI,0BAAqB,GAAG,KAAK,CAAC;QAarC;;WAEG;QACI,6BAAwB,GAAG,IAAI,CAAC;QACvC;;WAEG;QACI,mCAA8B,GAAW,GAAG,CAAC;QACpD;;;;WAIG;QACI,yBAAoB,GAAW,CAAC,CAAC;QACxC;;;;WAIG;QACI,wBAAmB,GAAY,IAAI,CAAC;QAE3C;;;WAGG;QACI,uBAAkB,GAAY,IAAI,CAAC;QAC1C;;WAEG;QACI,kBAAa,GAAW,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAE3C;;;WAGG;QACI,0CAAqC,GAA4B,IAAI,UAAU,EAAE,CAAC;QAEzF;;WAEG;QACI,yBAAoB,GAAY,IAAI,CAAC;QAEpC,qBAAgB,GAAY,IAAI,CAAC;QAEzC;;WAEG;QACI,mCAA8B,GAAG,IAAI,UAAU,EAAU,CAAC;QAEjE;;WAEG;QACI,kCAA6B,GAAG,IAAI,UAAU,EAAc,CAAC;QA8V5D,sBAAiB,GAAG,CAAC,YAA8B,EAAE,EAAE;YAC3D,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,YAAY,CAAC,WAAW,CAAC,UAAU,KAAK,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;gBACtJ,mBAAmB;gBACnB,OAAO;aACV;YACD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG;gBACvC,YAAY;gBACZ,kBAAkB,EAAE;oBAChB,OAAO,EAAE,KAAK;oBACd,SAAS,EAAE,KAAK;oBAChB,QAAQ,EAAE,KAAK;oBACf,eAAe,EAAE,CAAC;oBAClB,YAAY,EAAE,CAAC;oBACf,OAAO,EAAE,KAAK;oBACd,UAAU,EAAE,KAAK;oBACjB,iBAAiB,EAAE,KAAK;iBAC3B;aACJ,CAAC;YACF,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAChE,qEAAqE;YACrE,IAAI,cAAc,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,KAAK,iBAAiB,IAAI,cAAc,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,EAAE;gBAChI,4BAA4B;gBAC5B,MAAM,oBAAoB,GAAG,GAAG,EAAE;oBAC9B,IAAI,YAAY,CAAC,gBAAgB,EAAE;wBAC/B,MAAM,kBAAkB,GACpB,YAAY,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,eAAe,CAAC;4BAC1F,YAAY,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;wBAC7F,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE;4BAC3D,oCAAoC;4BACpC,MAAM,aAAa,GAAG,YAAY,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;4BACvE,IAAI,CAAC,aAAa,EAAE;gCAChB,OAAO;6BACV;4BACD,cAAc,CAAC,kBAAkB,CAAC,iBAAiB,GAAG,IAAI,CAAC;4BAC3D,cAAc,CAAC,sBAAsB,GAAG,aAAa,CAAC;4BACtD,cAAc,CAAC,uBAAuB,GAAG,aAAa,CAAC,8BAA8B,CAAC,GAAG,CAAC,GAAG,EAAE;gCAC3F,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;oCAC5B,OAAO;iCACV;gCAED,MAAM,aAAa,GAAG,GAAG,EAAE;oCACvB,qCAAqC;oCACrC,cAAc,CAAC,kBAAkB,CAAC,OAAO,GAAG,IAAI,CAAC;oCACjD,cAAc,CAAC,kBAAkB,CAAC,UAAU,GAAG,KAAK,CAAC;oCACrD,IAAI,CAAC,iCAAiC,GAAG,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC;oCAC9E,cAAc,CAAC,kBAAkB,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;oCACrH,cAAc,CAAC,kBAAkB,CAAC,eAAe,GAAG,CAAC,CAAC;oCACtD,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI,IAAI,CAAC;oCAC1D,gBAAgB,CAAC;wCACb,OAAO,EAAE,YAAY;wCACrB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,mBAAmB;wCAC7D,cAAc,EAAE,GAAG,EAAE,CAAC,CAAC,aAAa,CAAC,OAAO;wCAC5C,OAAO,EAAE,GAAG,EAAE;4CACV,IAAI,IAAI,CAAC,iCAAiC,KAAK,cAAc,CAAC,YAAY,CAAC,QAAQ,IAAI,cAAc,CAAC,kBAAkB,CAAC,OAAO,EAAE;gDAC9H,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;6CAChD;wCACL,CAAC;qCACJ,CAAC,CAAC;gCACP,CAAC,CAAC;gCACF,yBAAyB;gCACzB,IAAI,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE;oCAC/B,IAAI,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;wCACvC,qCAAqC;wCACrC,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE;4CACnC,gBAAgB,CAAC;gDACb,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,mBAAmB;gDAC1C,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,mBAAmB;gDAC7D,OAAO,EAAE,GAAG,EAAE;oDACV,yBAAyB;oDACzB,IAAI,aAAa,CAAC,OAAO,EAAE;wDACvB,aAAa,EAAE,CAAC;qDACnB;gDACL,CAAC;6CACJ,CAAC,CAAC;yCACN;6CAAM;4CACH,aAAa,EAAE,CAAC;yCACnB;qCACJ;yCAAM;wCACH,cAAc,CAAC,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;wCAClD,IAAI,CAAC,iCAAiC,GAAG,EAAE,CAAC;qCAC/C;iCACJ;4BACL,CAAC,CAAC,CAAC;yBACN;6BAAM;4BACH,cAAc,CAAC,sBAAsB,GAAG,kBAAkB,CAAC;4BAC3D,2DAA2D;4BAC3D,cAAc,CAAC,qBAAqB,GAAG,kBAAkB,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;gCACpG,IAAI,QAAQ,CAAC,CAAC,IAAI,GAAG,IAAI,cAAc,CAAC,kBAAkB,CAAC,SAAS,EAAE;oCAClE,cAAc,CAAC,kBAAkB,CAAC,SAAS,GAAG,KAAK,CAAC;iCACvD;gCACD,IAAI,QAAQ,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,OAAO,IAAI,IAAI,CAAC,wBAAwB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;oCACzH,qBAAqB;oCAErB,kFAAkF;oCAClF,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,SAAS,EAAE;wCAC9C,cAAc,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;wCACnD,0BAA0B;wCAC1B,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAmB,CAAC,CAAC;wCACjF,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wCACxD,0BAA0B;wCAC1B,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;wCACtB,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;wCACtB,qBAAqB;wCACrB,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;wCACtE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,8BAA8B,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;wCAClI,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;wCAC9E,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;wCACpE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wCAC9C,iEAAiE;wCACjE,oEAAoE;wCACpE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,GAAG,GAAG,CAAC;wCAC3E,oEAAoE;wCACpE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wCACrC,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;4CACtE,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;wCAC/C,CAAC,CAAC,CAAC;wCAEH,iCAAiC;wCACjC,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;4CAC1B,qEAAqE;4CACrE,mGAAmG;4CACnG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;4CAC/D,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;yCAClE;qCACJ;iCACJ;gCACD,IAAI,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,iCAAiC,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,QAAQ,IAAI,IAAI,CAAC,oBAAoB,EAAE;oCAC1I,cAAc,CAAC,kBAAkB,CAAC,OAAO,GAAG,IAAI,CAAC;oCACjD,IAAI,CAAC,iCAAiC,GAAG,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC;oCAC9E,cAAc,CAAC,kBAAkB,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;iCACxH;gCACD,IAAI,QAAQ,CAAC,CAAC,EAAE;oCACZ,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,OAAO,EAAE;wCAC5C,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE;4CAC3E,kDAAkD;4CAClD,cAAc,CAAC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,CAAC;4CAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4CAC/H,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;4CAC9D,UAAU,CAAC,eAAe,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,aAAa,CACpD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,EACjD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CACpD,CAAC;4CACF,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;yCACzG;qCACJ;yCAAM;wCACH,IAAI,IAAI,CAAC,iCAAiC,KAAK,cAAc,CAAC,YAAY,CAAC,QAAQ,EAAE;4CACjF,2CAA2C;4CAC3C,IAAI,IAAI,CAAC,eAAe,EAAE;gDACtB,UAAU,CAAC,GAAG,EAAE;oDACZ,cAAc,CAAC,kBAAkB,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAC1D,QAAQ,CAAC,CAAC,EACV,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC5E,CAAC;gDACN,CAAC,CAAC,CAAC;6CACN;iDAAM;gDACH,cAAc,CAAC,kBAAkB,CAAC,eAAe,GAAG,CAAC,CAAC;6CACzD;yCACJ;qCACJ;iCACJ;qCAAM;oCACH,cAAc,CAAC,kBAAkB,CAAC,QAAQ,GAAG,KAAK,CAAC;iCACtD;gCAED,IAAI,QAAQ,CAAC,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;oCACtC,IAAI,cAAc,CAAC,kBAAkB,CAAC,OAAO,EAAE;wCAC3C,cAAc,CAAC,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;wCAClD,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;qCACxC;oCACD,IAAI,cAAc,CAAC,kBAAkB,CAAC,OAAO,EAAE;wCAC3C,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;qCAChD;iCACJ;4BACL,CAAC,CAAC,CAAC;yBACN;qBACJ;gBACL,CAAC,CAAC;gBACF,IAAI,YAAY,CAAC,gBAAgB,EAAE;oBAC/B,oBAAoB,EAAE,CAAC;iBAC1B;qBAAM;oBACH,YAAY,CAAC,gCAAgC,CAAC,OAAO,CAAC,GAAG,EAAE;wBACvD,oBAAoB,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC;iBACN;aACJ;iBAAM;gBACH,cAAc,CAAC,kBAAkB,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBAC3D,IAAI,aAAa,GAAG,KAAK,CAAC;gBAC1B,MAAM,aAAa,GAAG,GAAG,EAAE;oBACvB,IAAI,CAAC,iCAAiC,GAAG,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC;oBAC9E,cAAc,CAAC,kBAAkB,CAAC,OAAO,GAAG,IAAI,CAAC;oBACjD,cAAc,CAAC,kBAAkB,CAAC,UAAU,GAAG,KAAK,CAAC;oBACrD,cAAc,CAAC,kBAAkB,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;oBACrH,cAAc,CAAC,kBAAkB,CAAC,eAAe,GAAG,CAAC,CAAC;oBACtD,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI,IAAI,CAAC;oBAC1D,gBAAgB,CAAC;wBACb,OAAO,EAAE,YAAY;wBACrB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,mBAAmB;wBAC7D,OAAO,EAAE,GAAG,EAAE;4BACV,IAAI,IAAI,CAAC,iCAAiC,KAAK,cAAc,CAAC,YAAY,CAAC,QAAQ,IAAI,cAAc,CAAC,kBAAkB,CAAC,OAAO,EAAE;gCAC9H,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;6BAChD;wBACL,CAAC;qBACJ,CAAC,CAAC;gBACP,CAAC,CAAC;gBACF,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;oBACjE,IAAI,WAAW,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,EAAE;wBACpD,aAAa,GAAG,KAAK,CAAC;wBACtB,iCAAiC;wBACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE;4BACnC,gBAAgB,CAAC;gCACb,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,mBAAmB;gCAC1C,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,mBAAmB;gCAC7D,OAAO,EAAE,GAAG,EAAE;oCACV,0DAA0D;oCAC1D,IAAI,IAAI,CAAC,iCAAiC,KAAK,cAAc,CAAC,YAAY,CAAC,QAAQ,EAAE;wCACjF,aAAa,EAAE,CAAC;qCACnB;gCACL,CAAC;gCACD,cAAc,EAAE,GAAG,EAAE;oCACjB,IAAI,aAAa,EAAE;wCACf,aAAa,GAAG,KAAK,CAAC;wCACtB,OAAO,IAAI,CAAC;qCACf;oCACD,OAAO,KAAK,CAAC;gCACjB,CAAC;6BACJ,CAAC,CAAC;yBACN;6BAAM;4BACH,aAAa,EAAE,CAAC;yBACnB;qBACJ;yBAAM,IAAI,WAAW,CAAC,IAAI,KAAK,iBAAiB,CAAC,SAAS,EAAE;wBACzD,aAAa,GAAG,IAAI,CAAC;wBACrB,cAAc,CAAC,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;wBAClD,IAAI,CAAC,iCAAiC,GAAG,EAAE,CAAC;qBAC/C;gBACL,CAAC,CAAC,CAAC;aACN;QACL,CAAC,CAAC;QAgMM,gBAAW,GAAa,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QArtBpE,sCAAsC;QACtC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE;YACxC,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACnC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,EAAE,CAAC;QACpD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,EAAE,CAAC;QAC1D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAEnF,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAErC,sBAAsB;QACtB,IAAI,CAAC,sBAAsB,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC;QAC/E,IAAI,CAAC,qBAAqB,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAC;QAE7E,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;QACvE,IAAI,CAAC,mBAAmB,GAAG,iBAAiB,CAAC,mCAAmC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YAC5F,IAAI,CAAC,oBAAoB,GAAG,CAAC,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,mBAAmB,CAAC,GAAG,MAAM,CAAC,cAAc,CAAC;YAC7G,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;QACpH,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACH,IAAW,cAAc,CAAC,YAAqB;QAC3C,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,YAAY,CAAC;IAChD,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,IAAkB;QAClC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,IAAkB;QACpC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,IAAI,EAAE,CAAC;QACxE,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,YAAqB;QACrC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC7C,CAAC;IAEM,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,eAAe;QACf,IAAI,CAAC,iCAAiC,GAAG,EAAE,CAAC;QAE5C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,2BAA2B,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,6BAA6B,EAAE,CAAC,UAAU,EAAE,EAAE;YAC3F,wBAAwB;YACxB,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YACpD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,iCAAiC,GAAG,EAAE,CAAC;QAC5C,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QAEvB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACpG,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,iBAAiB,CAAC,mCAAmC,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC/F;IACL,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,IAAkB;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,IAAkB;QACvC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,IAAI,EAAE,CAAC;QACxE,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACpD;IACL,CAAC;IAED;;;OAGG;IACI,qBAAqB,CAAC,IAAY;QACrC,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC9D,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAC9B;IACL,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,iBAA0B;QAC7C,sCAAsC;QACtC,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAC7D,mDAAmD;QACnD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACnD,qCAAqC;gBACrC,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE;oBACpD,KAAK,GAAG,CAAC,CAAC;oBACV,MAAM;iBACT;aACJ;SACJ;QACD,qCAAqC;QACrC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACvC,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,gBAAyC;QAChE,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;IAC9C,CAAC;IAES,UAAU,CAAC,QAAiB;QAClC,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;QAClD,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;QAC3C,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE;YACxB,OAAO;SACV;QAED,0BAA0B;QAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC;QACzD,IAAI,IAAI,CAAC,iCAAiC,EAAE;YACxC,IAAI,CAAC,UAAU,EAAE;gBACb,OAAO;aACV;YACD,UAAU,CAAC,kBAAkB,GAAG,UAAU,CAAC,kBAAkB,IAAI,IAAI,UAAU,EAAE,CAAC;YAClF,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YACjF,IAAI,cAAc,IAAI,cAAc,CAAC,kBAAkB,CAAC,OAAO,EAAE;gBAC7D,mBAAmB;gBACnB,UAAU,CAAC,yBAAyB,CAChC,cAAc,CAAC,kBAAkB,CAAC,eAAe,GAAG,cAAc,CAAC,kBAAkB,CAAC,YAAY,EAClG,CAAC,EACD,CAAC,EACD,UAAU,CAAC,kBAAkB,CAChC,CAAC;gBACF,2BAA2B;gBAE3B,IAAI,WAAW,GAAG,KAAK,CAAC;gBACxB,MAAM,uBAAuB,GAAG,cAAc,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,KAAK,mBAAmB,CAAC;gBAC9G,cAAc,CAAC,YAAY,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAClE,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBACzB,qCAAqC;oBACrC,kEAAkE;oBAClE,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wBAC/C,IAAI,IAAI,CAAC,QAAQ,CAAC,sBAAsB,IAAI,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE;4BACjF,OAAO,IAAI,CAAC;yBACf;wBACD,IAAI,IAAI,CAAC,QAAQ,CAAC,sBAAsB,IAAI,CAAC,CAAC,UAAU,EAAE;4BACtD,OAAO,IAAI,CAAC;yBACf;wBACD,0BAA0B;wBAC1B,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;4BACtF,OAAO,IAAI,CAAC;yBACf;wBACD,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;wBAC3C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;4BACd,OAAO,KAAK,CAAC;yBAChB;wBACD,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;oBACzG,CAAC,CAAC,CAAC;oBACH,MAAM,eAAe,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;oBACrG,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,eAAe,EAAE;wBAC7C,IAAI,cAAc,CAAC,kBAAkB,CAAC,iBAAiB,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,UAAU,EAAE;4BACtG,cAAc,CAAC,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;4BAClD,OAAO;yBACV;wBACD,cAAc,CAAC,kBAAkB,CAAC,OAAO,GAAG,IAAI,CAAC;wBACjD,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,uBAAuB,CAAC,CAAC;wBACrE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;wBAC9B,OAAO;qBACV;yBAAM,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;wBACjC,cAAc,CAAC,kBAAkB,CAAC,UAAU,GAAG,IAAI,CAAC;wBACpD,cAAc,CAAC,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;wBAClD,WAAW,GAAG,IAAI,CAAC;wBACnB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,KAAK,EAAE,uBAAuB,CAAC,CAAC;wBACpE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;qBACjC;iBACJ;gBACD,iGAAiG;gBACjG,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,WAAW,EAAE;oBAC1C,6DAA6D;oBAC7D,MAAM,SAAS,GAAG,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,kBAAmB,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;oBAC5F,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;oBAC7D,sBAAsB;oBACtB,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC;oBACxD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;oBACxF,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC1C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;oBACrE,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;oBAC3E,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;oBAEnC,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wBAC/C,IAAI,IAAI,CAAC,QAAQ,CAAC,sBAAsB,IAAI,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE;4BACjF,OAAO,IAAI,CAAC;yBACf;wBACD,IAAI,IAAI,CAAC,QAAQ,CAAC,sBAAsB,IAAI,CAAC,CAAC,UAAU,EAAE;4BACtD,OAAO,IAAI,CAAC;yBACf;wBACD,0BAA0B;wBAC1B,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;4BACtF,OAAO,IAAI,CAAC;yBACf;wBACD,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC/C,CAAC,CAAC,CAAC;oBACH,MAAM,eAAe,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;oBACrG,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,eAAe,EAAE;wBAC7C,IAAI,cAAc,CAAC,kBAAkB,CAAC,iBAAiB,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,UAAU,EAAE;4BACtG,cAAc,CAAC,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;4BAClD,OAAO;yBACV;wBACD,cAAc,CAAC,kBAAkB,CAAC,OAAO,GAAG,IAAI,CAAC;wBACjD,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,uBAAuB,CAAC,CAAC;wBACrE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;wBAC9B,OAAO;qBACV;yBAAM,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;wBACjC,cAAc,CAAC,kBAAkB,CAAC,UAAU,GAAG,IAAI,CAAC;wBACpD,cAAc,CAAC,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;wBAClD,WAAW,GAAG,IAAI,CAAC;wBACnB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,KAAK,EAAE,uBAAuB,CAAC,CAAC;wBACpE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;qBACjC;iBACJ;gBAED,0BAA0B;gBAC1B,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,KAAK,EAAE,uBAAuB,CAAC,CAAC;aAC9E;iBAAM;gBACH,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aACrD;SACJ;aAAM;YACH,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SACrD;IACL,CAAC;IA+OO,wBAAwB;QAC5B,eAAe;QACf,IAAI,CAAC,QAAQ,CAAC,wBAAwB,GAAG,IAAI,CAAC,QAAQ,CAAC,wBAAwB,IAAI,EAAE,CAAC;QACtF,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe;YACjD,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,oBAAoB,CAAC,mBAAmB,CAAC,iBAAiB;YACrG,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;QACnC,MAAM,mBAAmB,GAAG,YAAY,CAAC,qBAAqB,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;QAC3H,mBAAmB,CAAC,UAAU,GAAG,KAAK,CAAC;QAEvC,IAAI,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,2BAA2B,EAAE;YACpE,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,2BAA2B,CAAC;SACrG;aAAM;YACH,MAAM,MAAM,GAAG,GAAG,CAAC;YACnB,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,kCAAkC,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;YAC7G,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC/B,MAAM,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;YAC5C,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,CAAC;YAC3B,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,CAAC;YAC3B,MAAM,MAAM,GAAG,GAAG,CAAC;YACnB,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7D,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,sBAAsB,IAAI,SAAS,CAAC;YAC/F,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;YACvB,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,wBAAwB,IAAI,SAAS,CAAC;YACnG,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,cAAc,CAAC,MAAM,EAAE,CAAC;YACxB,MAAM,2BAA2B,GAAG,IAAI,gBAAgB,CAAC,4BAA4B,EAAE,eAAe,CAAC,CAAC;YACxG,2BAA2B,CAAC,cAAc,GAAG,cAAc,CAAC;YAC5D,mBAAmB,CAAC,QAAQ,GAAG,2BAA2B,CAAC;SAC9D;QAED,MAAM,KAAK,GAAG,WAAW,CACrB,oBAAoB,EACpB;YACI,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,GAAG;YACd,YAAY,EAAE,EAAE;SACnB,EACD,eAAe,CAClB,CAAC;QACF,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;QACzB,KAAK,CAAC,MAAM,GAAG,mBAAmB,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,gBAAgB,EAAE;YAC1D,MAAM,oBAAoB,GAAG,IAAI,SAAS,CAAC,sBAAsB,EAAE,YAAY,EAAE,EAAE,EAAE,SAAS,CAAC,mBAAmB,EAAE,SAAS,CAAC,uBAAuB,CAAC,CAAC;YACvJ,MAAM,IAAI,GAAuC,EAAE,CAAC;YACpD,IAAI,CAAC,IAAI,CAAC;gBACN,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,CAAC;aACX,CAAC,CAAC;YACH,IAAI,CAAC,IAAI,CAAC;gBACN,KAAK,EAAE,EAAE;gBACT,KAAK,EAAE,GAAG;aACb,CAAC,CAAC;YACH,IAAI,CAAC,IAAI,CAAC;gBACN,KAAK,EAAE,EAAE;gBACT,KAAK,EAAE,CAAC;aACX,CAAC,CAAC;YACH,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,cAAc,GAAG,IAAI,QAAQ,EAAE,CAAC;YACtC,cAAc,CAAC,aAAa,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;YAClE,oBAAoB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YACvD,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;YACtB,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC5C,eAAe,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;SACtD;QAED,MAAM,IAAI,GAAG,cAAc,CAAC,cAAc,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;QAClG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QAEjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAEjC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,kBAAkB,EAAE;YAC3D,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,kBAAkB,CAAC;YAC3E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,kBAAkB,CAAC;SAC7E;aAAM;YACH,MAAM,iBAAiB,GAAG,IAAI,gBAAgB,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;YAChF,iBAAiB,CAAC,eAAe,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,eAAe,CAAC;YAC7F,IAAI,iBAAiB,CAAC,eAAe,EAAE;gBACnC,iBAAiB,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;aAC/D;iBAAM;gBACH,iBAAiB,CAAC,YAAY,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;aAC9D;YACD,iBAAiB,CAAC,KAAK,GAAG,GAAG,CAAC;YAC9B,KAAK,CAAC,QAAQ,GAAG,iBAAiB,CAAC;YACnC,IAAI,CAAC,QAAQ,GAAG,iBAAiB,CAAC;YAClC,IAAI,CAAC,0BAA0B,GAAG,iBAAiB,CAAC;SACvD;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,KAAK,SAAS,EAAE;YAC9C,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;YACtE,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;YACxD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;SAC1D;QAED,IAAI,CAAC,QAAQ,CAAC,uBAAuB,GAAG,mBAAmB,CAAC;QAC5D,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;QAChG,8DAA8D;QAC9D,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAEO,iBAAiB,CAAC,oBAA4B;QAClD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QAC/D,IAAI,CAAC,cAAc,EAAE;YACjB,OAAO;SACV;QACD,IAAI,cAAc,CAAC,sBAAsB,EAAE;YACvC,IAAI,cAAc,CAAC,qBAAqB,EAAE;gBACtC,cAAc,CAAC,sBAAsB,CAAC,4BAA4B,CAAC,MAAM,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;aACnH;YACD,IAAI,cAAc,CAAC,uBAAuB,EAAE;gBACxC,cAAc,CAAC,sBAAsB,CAAC,8BAA8B,CAAC,MAAM,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;aACvH;SACJ;QACD,sBAAsB;QACtB,OAAO,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;IACnD,CAAC;IAEO,+BAA+B,CAAC,YAAqB,EAAE,SAAiB,IAAI,CAAC,QAAQ,CAAC,oBAAoB,IAAI,GAAG;QACrH,IAAI,YAAY,GAAsB,IAAI,CAAC;QAC3C,IAAI,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC;QACvC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;YAC9B,MAAM,aAAa,GAAG,MAAM,GAAG,MAAM,CAAC;YACtC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACvC,MAAM,IAAI,GAAG,OAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;gBAC7D,IAAI,IAAI,IAAI,aAAa,IAAI,IAAI,GAAG,eAAe,EAAE;oBACjD,eAAe,GAAG,IAAI,CAAC;oBACvB,YAAY,GAAG,QAAQ,CAAC;iBAC3B;YACL,CAAC,CAAC,CAAC;SACN;QACD,OAAO,YAAY,CAAC;IACxB,CAAC;IAEO,sBAAsB,CAAC,QAAqB;QAChD,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,CAAC,WAAW,EAAE;YACxD,OAAO;SACV;QACD,MAAM,YAAY,GAAG,IAAI,CAAC,+BAA+B,CAAC,WAAW,CAAC,CAAC;QACvE,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,YAAY,CAAC;QACtC,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjF,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;SACnE;aAAM,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACvF,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;SACnE;QACD,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,IAAI,WAAW,CAAC,CAAC;QACrF,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC;QACzD,IAAI,CAAC,qCAAqC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;IACzE,CAAC;IAEO,wBAAwB,CAAC,OAAgB,EAAE,KAAe,EAAE,uBAAiC;QACjG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE;YACxC,OAAO;SACV;QACD,IAAI,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,SAAS,KAAK,OAAO,IAAI,CAAC,KAAK,EAAE;YACvE,OAAO;SACV;QACD,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,SAAS,GAAG,OAAO,CAAC;QAC1D,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,WAAW,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACxE,CAAE,CAAC,SAAS,GAAG,OAAO,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE;YACV,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;gBACrC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;aACrC;YACD,IAAI,IAAI,CAAC,iBAAiB,IAAI,uBAAuB,EAAE;gBACnD,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;aACnC;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,iBAAiB,IAAI,uBAAuB,EAAE;gBACnD,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;aACnC;SACJ;IACL,CAAC;IAEO,mBAAmB;QACvB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;YACrC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;SACrC;IACL,CAAC;IAIO,kBAAkB,CAAC,QAAqB;QAC5C,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,iCAAiC,EAAE;YAClE,OAAO;SACV;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe;YACjD,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,oBAAoB,CAAC,mBAAmB,CAAC,iBAAiB;YACrG,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;QAEnC,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAEjF,MAAM,sBAAsB,GAAG,MAAM,CAAC,qBAAqB,CAAC,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC,GAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAClK,MAAM,KAAK,GAAG,cAAc,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC;QAC5F,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC5E,yEAAyE;QACzE,MAAM,MAAM,GAAG,sBAAsB,CAAC,SAAS,EAAE,CAAC;QAClD,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE;YACpC,IAAI,CAAC,qBAAqB,GAAG,WAAW,CACpC,yBAAyB,EACzB,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,qBAAkC,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,EAC3G,eAAe,CAClB,CAAC;SACL;aAAM;YACH,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,SAAS,EAAE,EAAE,QAAQ,CAAC,CAAC;SAChH;QACD,IAAI,CAAC,qBAAqB,CAAC,UAAU,GAAG,KAAK,CAAC;QAC9C,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,KAAK,SAAS,EAAE;YAC9C,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;SAChF;IACL,CAAC;IAEO,gBAAgB,CAAC,YAAoB;QACzC,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QACvD,IAAI,CAAC,cAAc,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC7F,OAAO;SACV;QACD,cAAc,CAAC,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;QAClD,IAAI,CAAC,iCAAiC,GAAG,EAAE,CAAC;QAC5C,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC9C,OAAO;SACV;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;YACnC,OAAO;SACV;QACD,+BAA+B;QAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,SAAS,EAAE;YAC1F,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;YAC9D,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACrF,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YACjG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,MAAM,CAAC;YACpD,UAAU,CAAC,eAAe,CAAC,CAAC,EAAE,cAAc,CAAC,kBAAkB,CAAC,eAAe,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CACjK,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,EACjD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CACpD,CAAC;YACF,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;SACvF;IACL,CAAC;;AAt4BD;;GAEG;AACoB,uCAAI,GAAG,gBAAgB,CAAC,aAAa,AAAjC,CAAkC;AAC7D;;;;GAIG;AACoB,0CAAO,GAAG,CAAC,AAAJ,CAAK;AAg4BvC,oBAAoB,CAAC,eAAe,CAChC,kCAAkC,CAAC,IAAI,EACvC,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,kCAAkC,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AACnF,CAAC,EACD,kCAAkC,CAAC,OAAO,EAC1C,IAAI,CACP,CAAC", "sourcesContent": ["import type { IWebXRFeature } from \"../webXRFeaturesManager\";\r\nimport { WebXRFeaturesManager, WebXRFeatureName } from \"../webXRFeaturesManager\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { WebXRInput } from \"../webXRInput\";\r\nimport type { WebXRInputSource } from \"../webXRInputSource\";\r\nimport type { IWebXRMotionControllerAxesValue } from \"../motionController/webXRControllerComponent\";\r\nimport { WebXRControllerComponent } from \"../motionController/webXRControllerComponent\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Vector3, Quaternion } from \"../../Maths/math.vector\";\r\nimport { Ray } from \"../../Culling/ray\";\r\nimport type { Material } from \"../../Materials/material\";\r\nimport { DynamicTexture } from \"../../Materials/Textures/dynamicTexture\";\r\nimport { CreateCylinder } from \"../../Meshes/Builders/cylinderBuilder\";\r\nimport { SineEase, EasingFunction } from \"../../Animations/easing\";\r\nimport { Animation } from \"../../Animations/animation\";\r\nimport { Axis } from \"../../Maths/math.axis\";\r\nimport { StandardMaterial } from \"../../Materials/standardMaterial\";\r\nimport { CreateGround } from \"../../Meshes/Builders/groundBuilder\";\r\nimport { CreateTorus } from \"../../Meshes/Builders/torusBuilder\";\r\nimport type { PickingInfo } from \"../../Collisions/pickingInfo\";\r\nimport { Curve3 } from \"../../Maths/math.path\";\r\nimport { CreateLines } from \"../../Meshes/Builders/linesBuilder\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport { Color3, Color4 } from \"../../Maths/math.color\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { UtilityLayerRenderer } from \"../../Rendering/utilityLayerRenderer\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { setAndStartTimer } from \"../../Misc/timer\";\r\nimport type { LinesMesh } from \"../../Meshes/linesMesh\";\r\n\r\n/**\r\n * The options container for the teleportation module\r\n */\r\nexport interface IWebXRTeleportationOptions {\r\n    /**\r\n     * if provided, this scene will be used to render meshes.\r\n     */\r\n    customUtilityLayerScene?: Scene;\r\n    /**\r\n     * Values to configure the default target mesh\r\n     */\r\n    defaultTargetMeshOptions?: {\r\n        /**\r\n         * Fill color of the teleportation area\r\n         */\r\n        teleportationFillColor?: string;\r\n        /**\r\n         * Border color for the teleportation area\r\n         */\r\n        teleportationBorderColor?: string;\r\n        /**\r\n         * Disable the mesh's animation sequence\r\n         */\r\n        disableAnimation?: boolean;\r\n        /**\r\n         * Disable lighting on the material or the ring and arrow\r\n         */\r\n        disableLighting?: boolean;\r\n        /**\r\n         * Override the default material of the torus and arrow\r\n         */\r\n        torusArrowMaterial?: Material;\r\n        /**\r\n         * Override the default material of the Landing Zone\r\n         */\r\n        teleportationCircleMaterial?: Material;\r\n    };\r\n    /**\r\n     * A list of meshes to use as floor meshes.\r\n     * Meshes can be added and removed after initializing the feature using the\r\n     * addFloorMesh and removeFloorMesh functions\r\n     * If empty, rotation will still work\r\n     */\r\n    floorMeshes?: AbstractMesh[];\r\n    /**\r\n     *  use this rendering group id for the meshes (optional)\r\n     */\r\n    renderingGroupId?: number;\r\n    /**\r\n     * Should teleportation move only to snap points\r\n     */\r\n    snapPointsOnly?: boolean;\r\n    /**\r\n     * An array of points to which the teleportation will snap to.\r\n     * If the teleportation ray is in the proximity of one of those points, it will be corrected to this point.\r\n     */\r\n    snapPositions?: Vector3[];\r\n    /**\r\n     * How close should the teleportation ray be in order to snap to position.\r\n     * Default to 0.8 units (meters)\r\n     */\r\n    snapToPositionRadius?: number;\r\n    /**\r\n     * Provide your own teleportation mesh instead of babylon's wonderful doughnut.\r\n     * If you want to support rotation, make sure your mesh has a direction indicator.\r\n     *\r\n     * When left untouched, the default mesh will be initialized.\r\n     */\r\n    teleportationTargetMesh?: AbstractMesh;\r\n    /**\r\n     * If main component is used (no thumbstick), how long in milliseconds should the \"long press\" take before teleport. Defaults to 3 seconds\r\n     */\r\n    timeToTeleport?: number;\r\n\r\n    /**\r\n     * If the main component is used, how long in milliseconds should the \"long press\" take before teleport starts. Defaults to 0\r\n     */\r\n    timeToTeleportStart?: number;\r\n    /**\r\n     * Disable using the thumbstick and use the main component (usually trigger) on long press.\r\n     * This will be automatically true if the controller doesn't have a thumbstick or touchpad.\r\n     */\r\n    useMainComponentOnly?: boolean;\r\n    /**\r\n     * Should meshes created here be added to a utility layer or the main scene\r\n     */\r\n    useUtilityLayer?: boolean;\r\n    /**\r\n     * Babylon XR Input class for controller\r\n     */\r\n    xrInput: WebXRInput;\r\n\r\n    /**\r\n     * Meshes that the teleportation ray cannot go through\r\n     */\r\n    pickBlockerMeshes?: AbstractMesh[];\r\n\r\n    /**\r\n     * define an optional predicate to select which meshes should block the teleportation ray\r\n     */\r\n    blockerMeshesPredicate?: (mesh: AbstractMesh) => boolean;\r\n\r\n    /**\r\n     * Should the teleportation ray be blocked by all of the scene's pickable meshes?\r\n     * Defaults to false\r\n     */\r\n    blockAllPickableMeshes?: boolean;\r\n\r\n    /**\r\n     * Color of the teleportation ray when it is blocked by a mesh in the pickBlockerMeshes array\r\n     * Defaults to red.\r\n     */\r\n    blockedRayColor?: Color4;\r\n\r\n    /**\r\n     * Should teleport work only on a specific hand?\r\n     */\r\n    forceHandedness?: XRHandedness;\r\n\r\n    /**\r\n     * If provided, this function will be used to generate the ray mesh instead of the lines mesh being used per default\r\n     */\r\n    generateRayPathMesh?: (points: Vector3[], pickingInfo: PickingInfo) => AbstractMesh;\r\n}\r\n\r\n/**\r\n * This is a teleportation feature to be used with WebXR-enabled motion controllers.\r\n * When enabled and attached, the feature will allow a user to move around and rotate in the scene using\r\n * the input of the attached controllers.\r\n */\r\nexport class WebXRMotionControllerTeleportation extends WebXRAbstractFeature {\r\n    private _controllers: {\r\n        [controllerUniqueId: string]: {\r\n            xrController: WebXRInputSource;\r\n            teleportationComponent?: WebXRControllerComponent;\r\n            teleportationState: {\r\n                forward: boolean;\r\n                backwards: boolean;\r\n                currentRotation: number;\r\n                baseRotation: number;\r\n                rotating: boolean;\r\n                blocked: boolean;\r\n                initialHit: boolean;\r\n                mainComponentUsed: boolean;\r\n            };\r\n            onAxisChangedObserver?: Nullable<Observer<IWebXRMotionControllerAxesValue>>;\r\n            onButtonChangedObserver?: Nullable<Observer<WebXRControllerComponent>>;\r\n        };\r\n    } = {};\r\n    private _currentTeleportationControllerId: string;\r\n    private _floorMeshes: AbstractMesh[];\r\n    private _quadraticBezierCurve: Nullable<AbstractMesh>;\r\n    private _selectionFeature: Nullable<IWebXRFeature>;\r\n    private _snapToPositions: Vector3[];\r\n    private _snappedToPoint: boolean = false;\r\n    private _teleportationRingMaterial?: StandardMaterial;\r\n    private _blockedRayColor: Color4;\r\n    private _cachedColor4White = new Color4(1, 1, 1, 1);\r\n    private _tmpRay = new Ray(new Vector3(), new Vector3());\r\n    private _tmpVector = new Vector3();\r\n    private _tmpQuaternion = new Quaternion();\r\n    private _worldScaleObserver?: Nullable<Observer<{ previousScaleFactor: number; newScaleFactor: number }>> = null;\r\n\r\n    /**\r\n     * Skip the next teleportation. This can be controlled by the user to prevent the user from teleportation\r\n     * to sections that are not yet \"unlocked\", but should still show the teleportation mesh.\r\n     */\r\n    public skipNextTeleportation = false;\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.TELEPORTATION;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the webxr specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * Is movement backwards enabled\r\n     */\r\n    public backwardsMovementEnabled = true;\r\n    /**\r\n     * Distance to travel when moving backwards\r\n     */\r\n    public backwardsTeleportationDistance: number = 0.7;\r\n    /**\r\n     * The distance from the user to the inspection point in the direction of the controller\r\n     * A higher number will allow the user to move further\r\n     * defaults to 5 (meters, in xr units)\r\n     */\r\n    public parabolicCheckRadius: number = 5;\r\n    /**\r\n     * Should the module support parabolic ray on top of direct ray\r\n     * If enabled, the user will be able to point \"at the sky\" and move according to predefined radius distance\r\n     * Very helpful when moving between floors / different heights\r\n     */\r\n    public parabolicRayEnabled: boolean = true;\r\n\r\n    /**\r\n     * The second type of ray - straight line.\r\n     * Should it be enabled or should the parabolic line be the only one.\r\n     */\r\n    public straightRayEnabled: boolean = true;\r\n    /**\r\n     * How much rotation should be applied when rotating right and left\r\n     */\r\n    public rotationAngle: number = Math.PI / 8;\r\n\r\n    /**\r\n     * This observable will notify when the target mesh position was updated.\r\n     * The picking info it provides contains the point to which the target mesh will move ()\r\n     */\r\n    public onTargetMeshPositionUpdatedObservable: Observable<PickingInfo> = new Observable();\r\n\r\n    /**\r\n     * Is teleportation enabled. Can be used to allow rotation only.\r\n     */\r\n    public teleportationEnabled: boolean = true;\r\n\r\n    private _rotationEnabled: boolean = true;\r\n\r\n    /**\r\n     * Observable raised before camera rotation\r\n     */\r\n    public onBeforeCameraTeleportRotation = new Observable<Number>();\r\n\r\n    /**\r\n     *  Observable raised after camera rotation\r\n     */\r\n    public onAfterCameraTeleportRotation = new Observable<Quaternion>();\r\n\r\n    /**\r\n     * Observable raised before camera teleportation\r\n     */\r\n    public onBeforeCameraTeleport: Observable<Vector3>;\r\n\r\n    /**\r\n     *  Observable raised after camera teleportation\r\n     */\r\n    public onAfterCameraTeleport: Observable<Vector3>;\r\n\r\n    /**\r\n     * Is rotation enabled when moving forward?\r\n     * Disabling this feature will prevent the user from deciding the direction when teleporting\r\n     */\r\n    public get rotationEnabled(): boolean {\r\n        return this._rotationEnabled;\r\n    }\r\n\r\n    /**\r\n     * Sets whether rotation is enabled or not\r\n     * @param enabled is rotation enabled when teleportation is shown\r\n     */\r\n    public set rotationEnabled(enabled: boolean) {\r\n        this._rotationEnabled = enabled;\r\n\r\n        if (this._options.teleportationTargetMesh) {\r\n            const children = this._options.teleportationTargetMesh.getChildMeshes(false, (node) => node.name === \"rotationCone\");\r\n            if (children[0]) {\r\n                children[0].setEnabled(enabled);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Exposes the currently set teleportation target mesh.\r\n     */\r\n    public get teleportationTargetMesh(): Nullable<AbstractMesh> {\r\n        return this._options.teleportationTargetMesh || null;\r\n    }\r\n\r\n    /**\r\n     * constructs a new teleportation system\r\n     * @param _xrSessionManager an instance of WebXRSessionManager\r\n     * @param _options configuration object for this feature\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        private _options: IWebXRTeleportationOptions\r\n    ) {\r\n        super(_xrSessionManager);\r\n        // create default mesh if not provided\r\n        if (!this._options.teleportationTargetMesh) {\r\n            this._createDefaultTargetMesh();\r\n        }\r\n\r\n        this._floorMeshes = this._options.floorMeshes || [];\r\n        this._snapToPositions = this._options.snapPositions || [];\r\n        this._blockedRayColor = this._options.blockedRayColor || new Color4(1, 0, 0, 0.75);\r\n\r\n        this._setTargetMeshVisibility(false);\r\n\r\n        // set the observables\r\n        this.onBeforeCameraTeleport = _options.xrInput.xrCamera.onBeforeCameraTeleport;\r\n        this.onAfterCameraTeleport = _options.xrInput.xrCamera.onAfterCameraTeleport;\r\n\r\n        this.parabolicCheckRadius *= this._xrSessionManager.worldScalingFactor;\r\n        this._worldScaleObserver = _xrSessionManager.onWorldScaleFactorChangedObservable.add((values) => {\r\n            this.parabolicCheckRadius = (this.parabolicCheckRadius / values.previousScaleFactor) * values.newScaleFactor;\r\n            this._options.teleportationTargetMesh?.scaling.scaleInPlace(values.newScaleFactor / values.previousScaleFactor);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Get the snapPointsOnly flag\r\n     */\r\n    public get snapPointsOnly(): boolean {\r\n        return !!this._options.snapPointsOnly;\r\n    }\r\n\r\n    /**\r\n     * Sets the snapPointsOnly flag\r\n     * @param snapToPoints should teleportation be exclusively to snap points\r\n     */\r\n    public set snapPointsOnly(snapToPoints: boolean) {\r\n        this._options.snapPointsOnly = snapToPoints;\r\n    }\r\n\r\n    /**\r\n     * Add a new mesh to the floor meshes array\r\n     * @param mesh the mesh to use as floor mesh\r\n     */\r\n    public addFloorMesh(mesh: AbstractMesh) {\r\n        this._floorMeshes.push(mesh);\r\n    }\r\n\r\n    /**\r\n     * Add a mesh to the list of meshes blocking the teleportation ray\r\n     * @param mesh The mesh to add to the teleportation-blocking meshes\r\n     */\r\n    public addBlockerMesh(mesh: AbstractMesh) {\r\n        this._options.pickBlockerMeshes = this._options.pickBlockerMeshes || [];\r\n        this._options.pickBlockerMeshes.push(mesh);\r\n    }\r\n\r\n    /**\r\n     * Add a new snap-to point to fix teleportation to this position\r\n     * @param newSnapPoint The new Snap-To point\r\n     */\r\n    public addSnapPoint(newSnapPoint: Vector3) {\r\n        this._snapToPositions.push(newSnapPoint);\r\n    }\r\n\r\n    public attach(): boolean {\r\n        if (!super.attach()) {\r\n            return false;\r\n        }\r\n\r\n        // Safety reset\r\n        this._currentTeleportationControllerId = \"\";\r\n\r\n        this._options.xrInput.controllers.forEach(this._attachController);\r\n        this._addNewAttachObserver(this._options.xrInput.onControllerAddedObservable, this._attachController);\r\n        this._addNewAttachObserver(this._options.xrInput.onControllerRemovedObservable, (controller) => {\r\n            // REMOVE the controller\r\n            this._detachController(controller.uniqueId);\r\n        });\r\n\r\n        return true;\r\n    }\r\n\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n\r\n        Object.keys(this._controllers).forEach((controllerId) => {\r\n            this._detachController(controllerId);\r\n        });\r\n\r\n        this._setTargetMeshVisibility(false);\r\n        this._currentTeleportationControllerId = \"\";\r\n        this._controllers = {};\r\n\r\n        return true;\r\n    }\r\n\r\n    public dispose(): void {\r\n        super.dispose();\r\n        this._options.teleportationTargetMesh && this._options.teleportationTargetMesh.dispose(false, true);\r\n        if (this._worldScaleObserver) {\r\n            this._xrSessionManager.onWorldScaleFactorChangedObservable.remove(this._worldScaleObserver);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from the floor meshes array\r\n     * @param mesh the mesh to remove\r\n     */\r\n    public removeFloorMesh(mesh: AbstractMesh) {\r\n        const index = this._floorMeshes.indexOf(mesh);\r\n        if (index !== -1) {\r\n            this._floorMeshes.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from the blocker meshes array\r\n     * @param mesh the mesh to remove\r\n     */\r\n    public removeBlockerMesh(mesh: AbstractMesh) {\r\n        this._options.pickBlockerMeshes = this._options.pickBlockerMeshes || [];\r\n        const index = this._options.pickBlockerMeshes.indexOf(mesh);\r\n        if (index !== -1) {\r\n            this._options.pickBlockerMeshes.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from the floor meshes array using its name\r\n     * @param name the mesh name to remove\r\n     */\r\n    public removeFloorMeshByName(name: string) {\r\n        const mesh = this._xrSessionManager.scene.getMeshByName(name);\r\n        if (mesh) {\r\n            this.removeFloorMesh(mesh);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * This function will iterate through the array, searching for this point or equal to it. It will then remove it from the snap-to array\r\n     * @param snapPointToRemove the point (or a clone of it) to be removed from the array\r\n     * @returns was the point found and removed or not\r\n     */\r\n    public removeSnapPoint(snapPointToRemove: Vector3): boolean {\r\n        // check if the object is in the array\r\n        let index = this._snapToPositions.indexOf(snapPointToRemove);\r\n        // if not found as an object, compare to the points\r\n        if (index === -1) {\r\n            for (let i = 0; i < this._snapToPositions.length; ++i) {\r\n                // equals? index is i, break the loop\r\n                if (this._snapToPositions[i].equals(snapPointToRemove)) {\r\n                    index = i;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n        // index is not -1? remove the object\r\n        if (index !== -1) {\r\n            this._snapToPositions.splice(index, 1);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * This function sets a selection feature that will be disabled when\r\n     * the forward ray is shown and will be reattached when hidden.\r\n     * This is used to remove the selection rays when moving.\r\n     * @param selectionFeature the feature to disable when forward movement is enabled\r\n     */\r\n    public setSelectionFeature(selectionFeature: Nullable<IWebXRFeature>) {\r\n        this._selectionFeature = selectionFeature;\r\n    }\r\n\r\n    protected _onXRFrame(_xrFrame: XRFrame) {\r\n        const frame = this._xrSessionManager.currentFrame;\r\n        const scene = this._xrSessionManager.scene;\r\n        if (!this.attach || !frame) {\r\n            return;\r\n        }\r\n\r\n        // render target if needed\r\n        const targetMesh = this._options.teleportationTargetMesh;\r\n        if (this._currentTeleportationControllerId) {\r\n            if (!targetMesh) {\r\n                return;\r\n            }\r\n            targetMesh.rotationQuaternion = targetMesh.rotationQuaternion || new Quaternion();\r\n            const controllerData = this._controllers[this._currentTeleportationControllerId];\r\n            if (controllerData && controllerData.teleportationState.forward) {\r\n                // set the rotation\r\n                Quaternion.RotationYawPitchRollToRef(\r\n                    controllerData.teleportationState.currentRotation + controllerData.teleportationState.baseRotation,\r\n                    0,\r\n                    0,\r\n                    targetMesh.rotationQuaternion\r\n                );\r\n                // set the ray and position\r\n\r\n                let hitPossible = false;\r\n                const controlSelectionFeature = controllerData.xrController.inputSource.targetRayMode !== \"transient-pointer\";\r\n                controllerData.xrController.getWorldPointerRayToRef(this._tmpRay);\r\n                if (this.straightRayEnabled) {\r\n                    // first check if direct ray possible\r\n                    // pick grounds that are LOWER only. upper will use parabolic path\r\n                    const pick = scene.pickWithRay(this._tmpRay, (o) => {\r\n                        if (this._options.blockerMeshesPredicate && this._options.blockerMeshesPredicate(o)) {\r\n                            return true;\r\n                        }\r\n                        if (this._options.blockAllPickableMeshes && o.isPickable) {\r\n                            return true;\r\n                        }\r\n                        // check for mesh-blockers\r\n                        if (this._options.pickBlockerMeshes && this._options.pickBlockerMeshes.indexOf(o) !== -1) {\r\n                            return true;\r\n                        }\r\n                        const index = this._floorMeshes.indexOf(o);\r\n                        if (index === -1) {\r\n                            return false;\r\n                        }\r\n                        return this._floorMeshes[index].absolutePosition.y < this._options.xrInput.xrCamera.globalPosition.y;\r\n                    });\r\n                    const floorMeshPicked = pick && pick.pickedMesh && this._floorMeshes.indexOf(pick.pickedMesh) !== -1;\r\n                    if (pick && pick.pickedMesh && !floorMeshPicked) {\r\n                        if (controllerData.teleportationState.mainComponentUsed && !controllerData.teleportationState.initialHit) {\r\n                            controllerData.teleportationState.forward = false;\r\n                            return;\r\n                        }\r\n                        controllerData.teleportationState.blocked = true;\r\n                        this._setTargetMeshVisibility(false, false, controlSelectionFeature);\r\n                        this._showParabolicPath(pick);\r\n                        return;\r\n                    } else if (pick && pick.pickedPoint) {\r\n                        controllerData.teleportationState.initialHit = true;\r\n                        controllerData.teleportationState.blocked = false;\r\n                        hitPossible = true;\r\n                        this._setTargetMeshPosition(pick);\r\n                        this._setTargetMeshVisibility(true, false, controlSelectionFeature);\r\n                        this._showParabolicPath(pick);\r\n                    }\r\n                }\r\n                // straight ray is still the main ray, but disabling the straight line will force parabolic line.\r\n                if (this.parabolicRayEnabled && !hitPossible) {\r\n                    // radius compensation according to pointer rotation around X\r\n                    const xRotation = controllerData.xrController.pointer.rotationQuaternion!.toEulerAngles().x;\r\n                    const compensation = 1 + (Math.PI / 2 - Math.abs(xRotation));\r\n                    // check parabolic ray\r\n                    const radius = this.parabolicCheckRadius * compensation;\r\n                    this._tmpRay.origin.addToRef(this._tmpRay.direction.scale(radius * 2), this._tmpVector);\r\n                    this._tmpVector.y = this._tmpRay.origin.y;\r\n                    this._tmpRay.origin.addInPlace(this._tmpRay.direction.scale(radius));\r\n                    this._tmpVector.subtractToRef(this._tmpRay.origin, this._tmpRay.direction);\r\n                    this._tmpRay.direction.normalize();\r\n\r\n                    const pick = scene.pickWithRay(this._tmpRay, (o) => {\r\n                        if (this._options.blockerMeshesPredicate && this._options.blockerMeshesPredicate(o)) {\r\n                            return true;\r\n                        }\r\n                        if (this._options.blockAllPickableMeshes && o.isPickable) {\r\n                            return true;\r\n                        }\r\n                        // check for mesh-blockers\r\n                        if (this._options.pickBlockerMeshes && this._options.pickBlockerMeshes.indexOf(o) !== -1) {\r\n                            return true;\r\n                        }\r\n                        return this._floorMeshes.indexOf(o) !== -1;\r\n                    });\r\n                    const floorMeshPicked = pick && pick.pickedMesh && this._floorMeshes.indexOf(pick.pickedMesh) !== -1;\r\n                    if (pick && pick.pickedMesh && !floorMeshPicked) {\r\n                        if (controllerData.teleportationState.mainComponentUsed && !controllerData.teleportationState.initialHit) {\r\n                            controllerData.teleportationState.forward = false;\r\n                            return;\r\n                        }\r\n                        controllerData.teleportationState.blocked = true;\r\n                        this._setTargetMeshVisibility(false, false, controlSelectionFeature);\r\n                        this._showParabolicPath(pick);\r\n                        return;\r\n                    } else if (pick && pick.pickedPoint) {\r\n                        controllerData.teleportationState.initialHit = true;\r\n                        controllerData.teleportationState.blocked = false;\r\n                        hitPossible = true;\r\n                        this._setTargetMeshPosition(pick);\r\n                        this._setTargetMeshVisibility(true, false, controlSelectionFeature);\r\n                        this._showParabolicPath(pick);\r\n                    }\r\n                }\r\n\r\n                // if needed, set visible:\r\n                this._setTargetMeshVisibility(hitPossible, false, controlSelectionFeature);\r\n            } else {\r\n                this._setTargetMeshVisibility(false, false, true);\r\n            }\r\n        } else {\r\n            this._disposeBezierCurve();\r\n            this._setTargetMeshVisibility(false, false, true);\r\n        }\r\n    }\r\n\r\n    private _attachController = (xrController: WebXRInputSource) => {\r\n        if (this._controllers[xrController.uniqueId] || (this._options.forceHandedness && xrController.inputSource.handedness !== this._options.forceHandedness)) {\r\n            // already attached\r\n            return;\r\n        }\r\n        this._controllers[xrController.uniqueId] = {\r\n            xrController,\r\n            teleportationState: {\r\n                forward: false,\r\n                backwards: false,\r\n                rotating: false,\r\n                currentRotation: 0,\r\n                baseRotation: 0,\r\n                blocked: false,\r\n                initialHit: false,\r\n                mainComponentUsed: false,\r\n            },\r\n        };\r\n        const controllerData = this._controllers[xrController.uniqueId];\r\n        // motion controller only available to gamepad-enabled input sources.\r\n        if (controllerData.xrController.inputSource.targetRayMode === \"tracked-pointer\" && controllerData.xrController.inputSource.gamepad) {\r\n            // motion controller support\r\n            const initMotionController = () => {\r\n                if (xrController.motionController) {\r\n                    const movementController =\r\n                        xrController.motionController.getComponentOfType(WebXRControllerComponent.THUMBSTICK_TYPE) ||\r\n                        xrController.motionController.getComponentOfType(WebXRControllerComponent.TOUCHPAD_TYPE);\r\n                    if (!movementController || this._options.useMainComponentOnly) {\r\n                        // use trigger to move on long press\r\n                        const mainComponent = xrController.motionController.getMainComponent();\r\n                        if (!mainComponent) {\r\n                            return;\r\n                        }\r\n                        controllerData.teleportationState.mainComponentUsed = true;\r\n                        controllerData.teleportationComponent = mainComponent;\r\n                        controllerData.onButtonChangedObserver = mainComponent.onButtonStateChangedObservable.add(() => {\r\n                            if (!this.teleportationEnabled) {\r\n                                return;\r\n                            }\r\n\r\n                            const teleportLocal = () => {\r\n                                // simulate \"forward\" thumbstick push\r\n                                controllerData.teleportationState.forward = true;\r\n                                controllerData.teleportationState.initialHit = false;\r\n                                this._currentTeleportationControllerId = controllerData.xrController.uniqueId;\r\n                                controllerData.teleportationState.baseRotation = this._options.xrInput.xrCamera.rotationQuaternion.toEulerAngles().y;\r\n                                controllerData.teleportationState.currentRotation = 0;\r\n                                const timeToSelect = this._options.timeToTeleport || 3000;\r\n                                setAndStartTimer({\r\n                                    timeout: timeToSelect,\r\n                                    contextObservable: this._xrSessionManager.onXRFrameObservable,\r\n                                    breakCondition: () => !mainComponent.pressed,\r\n                                    onEnded: () => {\r\n                                        if (this._currentTeleportationControllerId === controllerData.xrController.uniqueId && controllerData.teleportationState.forward) {\r\n                                            this._teleportForward(xrController.uniqueId);\r\n                                        }\r\n                                    },\r\n                                });\r\n                            };\r\n                            // did \"pressed\" changed?\r\n                            if (mainComponent.changes.pressed) {\r\n                                if (mainComponent.changes.pressed.current) {\r\n                                    // delay if the start time is defined\r\n                                    if (this._options.timeToTeleportStart) {\r\n                                        setAndStartTimer({\r\n                                            timeout: this._options.timeToTeleportStart,\r\n                                            contextObservable: this._xrSessionManager.onXRFrameObservable,\r\n                                            onEnded: () => {\r\n                                                // check if still pressed\r\n                                                if (mainComponent.pressed) {\r\n                                                    teleportLocal();\r\n                                                }\r\n                                            },\r\n                                        });\r\n                                    } else {\r\n                                        teleportLocal();\r\n                                    }\r\n                                } else {\r\n                                    controllerData.teleportationState.forward = false;\r\n                                    this._currentTeleportationControllerId = \"\";\r\n                                }\r\n                            }\r\n                        });\r\n                    } else {\r\n                        controllerData.teleportationComponent = movementController;\r\n                        // use thumbstick (or touchpad if thumbstick not available)\r\n                        controllerData.onAxisChangedObserver = movementController.onAxisValueChangedObservable.add((axesData) => {\r\n                            if (axesData.y <= 0.7 && controllerData.teleportationState.backwards) {\r\n                                controllerData.teleportationState.backwards = false;\r\n                            }\r\n                            if (axesData.y > 0.7 && !controllerData.teleportationState.forward && this.backwardsMovementEnabled && !this.snapPointsOnly) {\r\n                                // teleport backwards\r\n\r\n                                // General gist: Go Back N units, cast a ray towards the floor. If collided, move.\r\n                                if (!controllerData.teleportationState.backwards) {\r\n                                    controllerData.teleportationState.backwards = true;\r\n                                    // teleport backwards ONCE\r\n                                    this._tmpQuaternion.copyFrom(this._options.xrInput.xrCamera.rotationQuaternion!);\r\n                                    this._tmpQuaternion.toEulerAnglesToRef(this._tmpVector);\r\n                                    // get only the y rotation\r\n                                    this._tmpVector.x = 0;\r\n                                    this._tmpVector.z = 0;\r\n                                    // get the quaternion\r\n                                    Quaternion.FromEulerVectorToRef(this._tmpVector, this._tmpQuaternion);\r\n                                    this._tmpVector.set(0, 0, this.backwardsTeleportationDistance * (this._xrSessionManager.scene.useRightHandedSystem ? 1.0 : -1.0));\r\n                                    this._tmpVector.rotateByQuaternionToRef(this._tmpQuaternion, this._tmpVector);\r\n                                    this._tmpVector.addInPlace(this._options.xrInput.xrCamera.position);\r\n                                    this._tmpRay.origin.copyFrom(this._tmpVector);\r\n                                    // This will prevent the user from \"falling\" to a lower platform!\r\n                                    // TODO - should this be a flag? 'allow falling to lower platforms'?\r\n                                    this._tmpRay.length = this._options.xrInput.xrCamera.realWorldHeight + 0.1;\r\n                                    // Right handed system had here \"1\" instead of -1. This is unneeded.\r\n                                    this._tmpRay.direction.set(0, -1, 0);\r\n                                    const pick = this._xrSessionManager.scene.pickWithRay(this._tmpRay, (o) => {\r\n                                        return this._floorMeshes.indexOf(o) !== -1;\r\n                                    });\r\n\r\n                                    // pick must exist, but stay safe\r\n                                    if (pick && pick.pickedPoint) {\r\n                                        // Teleport the users feet to where they targeted. Ignore the Y axis.\r\n                                        // If the \"falling to lower platforms\" feature is implemented the Y axis should be set here as well\r\n                                        this._options.xrInput.xrCamera.position.x = pick.pickedPoint.x;\r\n                                        this._options.xrInput.xrCamera.position.z = pick.pickedPoint.z;\r\n                                    }\r\n                                }\r\n                            }\r\n                            if (axesData.y < -0.7 && !this._currentTeleportationControllerId && !controllerData.teleportationState.rotating && this.teleportationEnabled) {\r\n                                controllerData.teleportationState.forward = true;\r\n                                this._currentTeleportationControllerId = controllerData.xrController.uniqueId;\r\n                                controllerData.teleportationState.baseRotation = this._options.xrInput.xrCamera.rotationQuaternion.toEulerAngles().y;\r\n                            }\r\n                            if (axesData.x) {\r\n                                if (!controllerData.teleportationState.forward) {\r\n                                    if (!controllerData.teleportationState.rotating && Math.abs(axesData.x) > 0.7) {\r\n                                        // rotate in the right direction positive is right\r\n                                        controllerData.teleportationState.rotating = true;\r\n                                        const rotation = this.rotationAngle * (axesData.x > 0 ? 1 : -1) * (this._xrSessionManager.scene.useRightHandedSystem ? -1 : 1);\r\n                                        this.onBeforeCameraTeleportRotation.notifyObservers(rotation);\r\n                                        Quaternion.FromEulerAngles(0, rotation, 0).multiplyToRef(\r\n                                            this._options.xrInput.xrCamera.rotationQuaternion,\r\n                                            this._options.xrInput.xrCamera.rotationQuaternion\r\n                                        );\r\n                                        this.onAfterCameraTeleportRotation.notifyObservers(this._options.xrInput.xrCamera.rotationQuaternion);\r\n                                    }\r\n                                } else {\r\n                                    if (this._currentTeleportationControllerId === controllerData.xrController.uniqueId) {\r\n                                        // set the rotation of the forward movement\r\n                                        if (this.rotationEnabled) {\r\n                                            setTimeout(() => {\r\n                                                controllerData.teleportationState.currentRotation = Math.atan2(\r\n                                                    axesData.x,\r\n                                                    axesData.y * (this._xrSessionManager.scene.useRightHandedSystem ? 1 : -1)\r\n                                                );\r\n                                            });\r\n                                        } else {\r\n                                            controllerData.teleportationState.currentRotation = 0;\r\n                                        }\r\n                                    }\r\n                                }\r\n                            } else {\r\n                                controllerData.teleportationState.rotating = false;\r\n                            }\r\n\r\n                            if (axesData.x === 0 && axesData.y === 0) {\r\n                                if (controllerData.teleportationState.blocked) {\r\n                                    controllerData.teleportationState.blocked = false;\r\n                                    this._setTargetMeshVisibility(false);\r\n                                }\r\n                                if (controllerData.teleportationState.forward) {\r\n                                    this._teleportForward(xrController.uniqueId);\r\n                                }\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            };\r\n            if (xrController.motionController) {\r\n                initMotionController();\r\n            } else {\r\n                xrController.onMotionControllerInitObservable.addOnce(() => {\r\n                    initMotionController();\r\n                });\r\n            }\r\n        } else {\r\n            controllerData.teleportationState.mainComponentUsed = true;\r\n            let breakObserver = false;\r\n            const teleportLocal = () => {\r\n                this._currentTeleportationControllerId = controllerData.xrController.uniqueId;\r\n                controllerData.teleportationState.forward = true;\r\n                controllerData.teleportationState.initialHit = false;\r\n                controllerData.teleportationState.baseRotation = this._options.xrInput.xrCamera.rotationQuaternion.toEulerAngles().y;\r\n                controllerData.teleportationState.currentRotation = 0;\r\n                const timeToSelect = this._options.timeToTeleport || 3000;\r\n                setAndStartTimer({\r\n                    timeout: timeToSelect,\r\n                    contextObservable: this._xrSessionManager.onXRFrameObservable,\r\n                    onEnded: () => {\r\n                        if (this._currentTeleportationControllerId === controllerData.xrController.uniqueId && controllerData.teleportationState.forward) {\r\n                            this._teleportForward(xrController.uniqueId);\r\n                        }\r\n                    },\r\n                });\r\n            };\r\n            this._xrSessionManager.scene.onPointerObservable.add((pointerInfo) => {\r\n                if (pointerInfo.type === PointerEventTypes.POINTERDOWN) {\r\n                    breakObserver = false;\r\n                    // check if start time is defined\r\n                    if (this._options.timeToTeleportStart) {\r\n                        setAndStartTimer({\r\n                            timeout: this._options.timeToTeleportStart,\r\n                            contextObservable: this._xrSessionManager.onXRFrameObservable,\r\n                            onEnded: () => {\r\n                                // make sure pointer up was not triggered during this time\r\n                                if (this._currentTeleportationControllerId === controllerData.xrController.uniqueId) {\r\n                                    teleportLocal();\r\n                                }\r\n                            },\r\n                            breakCondition: () => {\r\n                                if (breakObserver) {\r\n                                    breakObserver = false;\r\n                                    return true;\r\n                                }\r\n                                return false;\r\n                            },\r\n                        });\r\n                    } else {\r\n                        teleportLocal();\r\n                    }\r\n                } else if (pointerInfo.type === PointerEventTypes.POINTERUP) {\r\n                    breakObserver = true;\r\n                    controllerData.teleportationState.forward = false;\r\n                    this._currentTeleportationControllerId = \"\";\r\n                }\r\n            });\r\n        }\r\n    };\r\n\r\n    private _createDefaultTargetMesh() {\r\n        // set defaults\r\n        this._options.defaultTargetMeshOptions = this._options.defaultTargetMeshOptions || {};\r\n        const sceneToRenderTo = this._options.useUtilityLayer\r\n            ? this._options.customUtilityLayerScene || UtilityLayerRenderer.DefaultUtilityLayer.utilityLayerScene\r\n            : this._xrSessionManager.scene;\r\n        const teleportationTarget = CreateGround(\"teleportationTarget\", { width: 2, height: 2, subdivisions: 2 }, sceneToRenderTo);\r\n        teleportationTarget.isPickable = false;\r\n\r\n        if (this._options.defaultTargetMeshOptions.teleportationCircleMaterial) {\r\n            teleportationTarget.material = this._options.defaultTargetMeshOptions.teleportationCircleMaterial;\r\n        } else {\r\n            const length = 512;\r\n            const dynamicTexture = new DynamicTexture(\"teleportationPlaneDynamicTexture\", length, sceneToRenderTo, true);\r\n            dynamicTexture.hasAlpha = true;\r\n            const context = dynamicTexture.getContext();\r\n            const centerX = length / 2;\r\n            const centerY = length / 2;\r\n            const radius = 200;\r\n            context.beginPath();\r\n            context.arc(centerX, centerY, radius, 0, 2 * Math.PI, false);\r\n            context.fillStyle = this._options.defaultTargetMeshOptions.teleportationFillColor || \"#444444\";\r\n            context.fill();\r\n            context.lineWidth = 10;\r\n            context.strokeStyle = this._options.defaultTargetMeshOptions.teleportationBorderColor || \"#FFFFFF\";\r\n            context.stroke();\r\n            context.closePath();\r\n            dynamicTexture.update();\r\n            const teleportationCircleMaterial = new StandardMaterial(\"teleportationPlaneMaterial\", sceneToRenderTo);\r\n            teleportationCircleMaterial.diffuseTexture = dynamicTexture;\r\n            teleportationTarget.material = teleportationCircleMaterial;\r\n        }\r\n\r\n        const torus = CreateTorus(\r\n            \"torusTeleportation\",\r\n            {\r\n                diameter: 0.75,\r\n                thickness: 0.1,\r\n                tessellation: 20,\r\n            },\r\n            sceneToRenderTo\r\n        );\r\n        torus.isPickable = false;\r\n        torus.parent = teleportationTarget;\r\n        if (!this._options.defaultTargetMeshOptions.disableAnimation) {\r\n            const animationInnerCircle = new Animation(\"animationInnerCircle\", \"position.y\", 30, Animation.ANIMATIONTYPE_FLOAT, Animation.ANIMATIONLOOPMODE_CYCLE);\r\n            const keys: { frame: number; value: number }[] = [];\r\n            keys.push({\r\n                frame: 0,\r\n                value: 0,\r\n            });\r\n            keys.push({\r\n                frame: 30,\r\n                value: 0.4,\r\n            });\r\n            keys.push({\r\n                frame: 60,\r\n                value: 0,\r\n            });\r\n            animationInnerCircle.setKeys(keys);\r\n            const easingFunction = new SineEase();\r\n            easingFunction.setEasingMode(EasingFunction.EASINGMODE_EASEINOUT);\r\n            animationInnerCircle.setEasingFunction(easingFunction);\r\n            torus.animations = [];\r\n            torus.animations.push(animationInnerCircle);\r\n            sceneToRenderTo.beginAnimation(torus, 0, 60, true);\r\n        }\r\n\r\n        const cone = CreateCylinder(\"rotationCone\", { diameterTop: 0, tessellation: 4 }, sceneToRenderTo);\r\n        cone.isPickable = false;\r\n        cone.scaling.set(0.5, 0.12, 0.2);\r\n\r\n        cone.rotate(Axis.X, Math.PI / 2);\r\n\r\n        cone.position.z = 0.6;\r\n        cone.parent = torus;\r\n\r\n        if (this._options.defaultTargetMeshOptions.torusArrowMaterial) {\r\n            torus.material = this._options.defaultTargetMeshOptions.torusArrowMaterial;\r\n            cone.material = this._options.defaultTargetMeshOptions.torusArrowMaterial;\r\n        } else {\r\n            const torusConeMaterial = new StandardMaterial(\"torusConsMat\", sceneToRenderTo);\r\n            torusConeMaterial.disableLighting = !!this._options.defaultTargetMeshOptions.disableLighting;\r\n            if (torusConeMaterial.disableLighting) {\r\n                torusConeMaterial.emissiveColor = new Color3(0.3, 0.3, 1.0);\r\n            } else {\r\n                torusConeMaterial.diffuseColor = new Color3(0.3, 0.3, 1.0);\r\n            }\r\n            torusConeMaterial.alpha = 0.9;\r\n            torus.material = torusConeMaterial;\r\n            cone.material = torusConeMaterial;\r\n            this._teleportationRingMaterial = torusConeMaterial;\r\n        }\r\n\r\n        if (this._options.renderingGroupId !== undefined) {\r\n            teleportationTarget.renderingGroupId = this._options.renderingGroupId;\r\n            torus.renderingGroupId = this._options.renderingGroupId;\r\n            cone.renderingGroupId = this._options.renderingGroupId;\r\n        }\r\n\r\n        this._options.teleportationTargetMesh = teleportationTarget;\r\n        this._options.teleportationTargetMesh.scaling.setAll(this._xrSessionManager.worldScalingFactor);\r\n        // hide the teleportation target mesh right after creating it.\r\n        this._setTargetMeshVisibility(false);\r\n    }\r\n\r\n    private _detachController(xrControllerUniqueId: string) {\r\n        const controllerData = this._controllers[xrControllerUniqueId];\r\n        if (!controllerData) {\r\n            return;\r\n        }\r\n        if (controllerData.teleportationComponent) {\r\n            if (controllerData.onAxisChangedObserver) {\r\n                controllerData.teleportationComponent.onAxisValueChangedObservable.remove(controllerData.onAxisChangedObserver);\r\n            }\r\n            if (controllerData.onButtonChangedObserver) {\r\n                controllerData.teleportationComponent.onButtonStateChangedObservable.remove(controllerData.onButtonChangedObserver);\r\n            }\r\n        }\r\n        // remove from the map\r\n        delete this._controllers[xrControllerUniqueId];\r\n    }\r\n\r\n    private _findClosestSnapPointWithRadius(realPosition: Vector3, radius: number = this._options.snapToPositionRadius || 0.8) {\r\n        let closestPoint: Nullable<Vector3> = null;\r\n        let closestDistance = Number.MAX_VALUE;\r\n        if (this._snapToPositions.length) {\r\n            const radiusSquared = radius * radius;\r\n            this._snapToPositions.forEach((position) => {\r\n                const dist = Vector3.DistanceSquared(position, realPosition);\r\n                if (dist <= radiusSquared && dist < closestDistance) {\r\n                    closestDistance = dist;\r\n                    closestPoint = position;\r\n                }\r\n            });\r\n        }\r\n        return closestPoint;\r\n    }\r\n\r\n    private _setTargetMeshPosition(pickInfo: PickingInfo) {\r\n        const newPosition = pickInfo.pickedPoint;\r\n        if (!this._options.teleportationTargetMesh || !newPosition) {\r\n            return;\r\n        }\r\n        const snapPosition = this._findClosestSnapPointWithRadius(newPosition);\r\n        this._snappedToPoint = !!snapPosition;\r\n        if (this.snapPointsOnly && !this._snappedToPoint && this._teleportationRingMaterial) {\r\n            this._teleportationRingMaterial.diffuseColor.set(1.0, 0.3, 0.3);\r\n        } else if (this.snapPointsOnly && this._snappedToPoint && this._teleportationRingMaterial) {\r\n            this._teleportationRingMaterial.diffuseColor.set(0.3, 0.3, 1.0);\r\n        }\r\n        this._options.teleportationTargetMesh.position.copyFrom(snapPosition || newPosition);\r\n        this._options.teleportationTargetMesh.position.y += 0.01;\r\n        this.onTargetMeshPositionUpdatedObservable.notifyObservers(pickInfo);\r\n    }\r\n\r\n    private _setTargetMeshVisibility(visible: boolean, force?: boolean, controlSelectionFeature?: boolean) {\r\n        if (!this._options.teleportationTargetMesh) {\r\n            return;\r\n        }\r\n        if (this._options.teleportationTargetMesh.isVisible === visible && !force) {\r\n            return;\r\n        }\r\n        this._options.teleportationTargetMesh.isVisible = visible;\r\n        this._options.teleportationTargetMesh.getChildren(undefined, false).forEach((m) => {\r\n            (<any>m).isVisible = visible;\r\n        });\r\n\r\n        if (!visible) {\r\n            if (this._quadraticBezierCurve) {\r\n                this._quadraticBezierCurve.dispose();\r\n                this._quadraticBezierCurve = null;\r\n            }\r\n            if (this._selectionFeature && controlSelectionFeature) {\r\n                this._selectionFeature.attach();\r\n            }\r\n        } else {\r\n            if (this._selectionFeature && controlSelectionFeature) {\r\n                this._selectionFeature.detach();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _disposeBezierCurve() {\r\n        if (this._quadraticBezierCurve) {\r\n            this._quadraticBezierCurve.dispose();\r\n            this._quadraticBezierCurve = null;\r\n        }\r\n    }\r\n\r\n    private _colorArray: Color4[] = Array(24).fill(this._cachedColor4White);\r\n\r\n    private _showParabolicPath(pickInfo: PickingInfo) {\r\n        if (!pickInfo.pickedPoint || !this._currentTeleportationControllerId) {\r\n            return;\r\n        }\r\n\r\n        const sceneToRenderTo = this._options.useUtilityLayer\r\n            ? this._options.customUtilityLayerScene || UtilityLayerRenderer.DefaultUtilityLayer.utilityLayerScene\r\n            : this._xrSessionManager.scene;\r\n\r\n        const controllerData = this._controllers[this._currentTeleportationControllerId];\r\n\r\n        const quadraticBezierVectors = Curve3.CreateQuadraticBezier(controllerData.xrController.pointer.absolutePosition, pickInfo.ray!.origin, pickInfo.pickedPoint, 25);\r\n        const color = controllerData.teleportationState.blocked ? this._blockedRayColor : undefined;\r\n        const colorsArray = this._colorArray.fill(color || this._cachedColor4White);\r\n        // take out the first 2 points, to not start directly from the controller\r\n        const points = quadraticBezierVectors.getPoints();\r\n        points.shift();\r\n        points.shift();\r\n        if (!this._options.generateRayPathMesh) {\r\n            this._quadraticBezierCurve = CreateLines(\r\n                \"teleportation path line\",\r\n                { points: points, instance: this._quadraticBezierCurve as LinesMesh, updatable: true, colors: colorsArray },\r\n                sceneToRenderTo\r\n            );\r\n        } else {\r\n            this._quadraticBezierCurve = this._options.generateRayPathMesh(quadraticBezierVectors.getPoints(), pickInfo);\r\n        }\r\n        this._quadraticBezierCurve.isPickable = false;\r\n        if (this._options.renderingGroupId !== undefined) {\r\n            this._quadraticBezierCurve.renderingGroupId = this._options.renderingGroupId;\r\n        }\r\n    }\r\n\r\n    private _teleportForward(controllerId: string) {\r\n        const controllerData = this._controllers[controllerId];\r\n        if (!controllerData || !controllerData.teleportationState.forward || !this.teleportationEnabled) {\r\n            return;\r\n        }\r\n        controllerData.teleportationState.forward = false;\r\n        this._currentTeleportationControllerId = \"\";\r\n        if (this.snapPointsOnly && !this._snappedToPoint) {\r\n            return;\r\n        }\r\n\r\n        if (this.skipNextTeleportation) {\r\n            this.skipNextTeleportation = false;\r\n            return;\r\n        }\r\n        // do the movement forward here\r\n        if (this._options.teleportationTargetMesh && this._options.teleportationTargetMesh.isVisible) {\r\n            const height = this._options.xrInput.xrCamera.realWorldHeight;\r\n            this.onBeforeCameraTeleport.notifyObservers(this._options.xrInput.xrCamera.position);\r\n            this._options.xrInput.xrCamera.position.copyFrom(this._options.teleportationTargetMesh.position);\r\n            this._options.xrInput.xrCamera.position.y += height;\r\n            Quaternion.FromEulerAngles(0, controllerData.teleportationState.currentRotation - (this._xrSessionManager.scene.useRightHandedSystem ? Math.PI : 0), 0).multiplyToRef(\r\n                this._options.xrInput.xrCamera.rotationQuaternion,\r\n                this._options.xrInput.xrCamera.rotationQuaternion\r\n            );\r\n            this.onAfterCameraTeleport.notifyObservers(this._options.xrInput.xrCamera.position);\r\n        }\r\n    }\r\n}\r\n\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRMotionControllerTeleportation.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRMotionControllerTeleportation(xrSessionManager, options);\r\n    },\r\n    WebXRMotionControllerTeleportation.Version,\r\n    true\r\n);\r\n"]}