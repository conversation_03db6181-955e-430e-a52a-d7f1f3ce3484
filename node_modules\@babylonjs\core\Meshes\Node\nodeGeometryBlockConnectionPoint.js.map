{"version": 3, "file": "nodeGeometryBlockConnectionPoint.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Node/nodeGeometryBlockConnectionPoint.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,qCAAqC,EAAE,MAAM,0CAA0C,CAAC;AAIjG;;GAEG;AACH,MAAM,CAAN,IAAY,8CAOX;AAPD,WAAY,8CAA8C;IACtD,6BAA6B;IAC7B,+HAAU,CAAA;IACV,qDAAqD;IACrD,2IAAgB,CAAA;IAChB,sEAAsE;IACtE,uIAAc,CAAA;AAClB,CAAC,EAPW,8CAA8C,KAA9C,8CAA8C,QAOzD;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,oCAKX;AALD,WAAY,oCAAoC;IAC5C,YAAY;IACZ,iGAAK,CAAA;IACL,aAAa;IACb,mGAAM,CAAA;AACV,CAAC,EALW,oCAAoC,KAApC,oCAAoC,QAK/C;AAED;;GAEG;AACH,MAAM,OAAO,2BAA2B;IA0BpC,sCAAsC;IACtC,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IA+CD;;OAEG;IACH,IAAW,IAAI;QACX,IAAI,IAAI,CAAC,KAAK,KAAK,qCAAqC,CAAC,UAAU,EAAE;YACjE,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;gBAC1B,OAAQ,IAAI,CAAC,WAAkC,CAAC,IAAI,CAAC;aACxD;YAED,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;aACpC;YAED,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE;gBAC1E,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;aAC5C;SACJ;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,qCAAqC,CAAC,YAAY,EAAE;YACnE,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,IAAI,IAAI,CAAC,2BAA2B,EAAE;oBAC7E,OAAO,IAAI,CAAC,2BAA2B,CAAC;iBAC3C;gBACD,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;aAC1C;iBAAM,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBACzC,OAAO,IAAI,CAAC,2BAA2B,CAAC;aAC3C;SACJ;QAED,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAA4C;QACxD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAiBD;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC;IAC7D,CAAC;IAED,oDAAoD;IACpD,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,oDAAoD;IACpD,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,4EAA4E;IAC5E,IAAW,WAAW;QAClB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;IAC3C,CAAC;IAED,2EAA2E;IAC3E,IAAW,eAAe;QACtB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO,EAAE,CAAC;SACb;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;IACpD,CAAC;IAED,2CAA2C;IAC3C,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,wFAAwF;IACxF,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IACzD,CAAC;IAED,kFAAkF;IAClF,IAAW,SAAS;QAChB,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE;YAC1E,OAAO,IAAI,CAAC,IAAI,CAAC;SACpB;QACD,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAQD,gBAAgB;IACT,cAAc;QACjB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,KAA6B;QAClD,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,IAAI,CAAC,eAAe,EAAE,eAAe,EAAE;gBACvC,IAAI,CAAC,eAAgB,CAAC,UAAU,EAAE,CAAC;gBACnC,IAAI,CAAC,eAAgB,CAAC,eAAe,EAAE,CAAC;gBACxC,OAAO,IAAI,CAAC,eAAgB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;aACvD;YACD,IAAI,CAAC,eAAgB,CAAC,UAAU,EAAE,CAAC;YACnC,IAAI,CAAC,eAAgB,CAAC,eAAe,GAAG,CAAC,CAAC;YAC1C,OAAO,IAAI,CAAC,eAAgB,CAAC,YAAY,CAAC;SAC7C;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACH,YAAmB,IAAY,EAAE,UAA6B,EAAE,SAA+C;QAtO/G,gBAAgB;QACT,oBAAe,GAA0C,IAAI,CAAC;QACrE,gBAAgB;QACT,iBAAY,GAAQ,IAAI,CAAC;QAChC,gBAAgB;QACT,oBAAe,GAAqD,IAAI,CAAC;QAEhF,gBAAgB;QACT,iCAA4B,GAA0C,IAAI,CAAC;QAE1E,eAAU,GAAG,IAAI,KAAK,EAA+B,CAAC;QAEtD,UAAK,GAAG,qCAAqC,CAAC,QAAQ,CAAC;QAE/D,gBAAgB;QACT,4BAAuB,GAA0C,IAAI,CAAC;QAE7E,gBAAgB;QACT,0BAAqB,GAA0C,IAAI,CAAC;QAE3E,gBAAgB;QACT,gCAA2B,GAAoD,IAAI,CAAC;QAO3F;;WAEG;QACI,iCAA4B,GAA4C,EAAE,CAAC;QAElF;;WAEG;QACI,iCAA4B,GAA4C,EAAE,CAAC;QAElF;;WAEG;QACI,2BAAsB,GAAG,IAAI,UAAU,EAA+B,CAAC;QAE9E;;WAEG;QACI,qBAAgB,GAAY,KAAK,CAAC;QAEzC;;WAEG;QACI,wBAAmB,GAAW,CAAC,CAAC,CAAC;QAExC;;WAEG;QACI,iBAAY,GAAkB,IAAI,CAAC;QAE1C;;WAEG;QACI,UAAK,GAAkB,IAAI,CAAC;QAEnC;;WAEG;QACI,aAAQ,GAAkB,IAAI,CAAC;QAEtC;;WAEG;QACI,aAAQ,GAAkB,IAAI,CAAC;QA0GtC,gBAAgB;QACT,eAAU,GAAG,CAAC,CAAC;QAEtB,gBAAgB;QACT,oBAAe,GAAG,CAAC,CAAC;QAkDvB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,6BAA6B,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,eAA4C;QAC5D,OAAO,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,KAAK,8CAA8C,CAAC,UAAU,CAAC;IACvH,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,eAA4C;QACvE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QACpC,MAAM,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC;QAE9C,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,IAAI,eAAe,CAAC,SAAS,KAAK,qCAAqC,CAAC,UAAU,EAAE;YACtH,iBAAiB;YACjB,IAAI,eAAe,CAAC,4BAA4B,IAAI,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACxH,OAAO,8CAA8C,CAAC,UAAU,CAAC;aACpE;iBAAM;gBACH,OAAO,8CAA8C,CAAC,gBAAgB,CAAC;aAC1E;SACJ;QAED,WAAW;QACX,IAAI,eAAe,CAAC,4BAA4B,IAAI,eAAe,CAAC,4BAA4B,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACxH,OAAO,8CAA8C,CAAC,gBAAgB,CAAC;SAC1E;QAED,kBAAkB;QAClB,IAAI,WAAW,GAAG,UAAU,CAAC;QAC7B,IAAI,WAAW,GAAG,UAAU,CAAC;QAC7B,IAAI,IAAI,CAAC,SAAS,KAAK,oCAAoC,CAAC,KAAK,EAAE;YAC/D,WAAW,GAAG,UAAU,CAAC;YACzB,WAAW,GAAG,UAAU,CAAC;SAC5B;QAED,IAAI,WAAW,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;YACzC,OAAO,8CAA8C,CAAC,cAAc,CAAC;SACxE;QAED,OAAO,8CAA8C,CAAC,UAAU,CAAC;IACrE,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,eAA4C,EAAE,iBAAiB,GAAG,KAAK;QACpF,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE;YAC3D,4CAA4C;YAC5C,MAAM,sCAAsC,CAAC;SAChD;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtC,eAAe,CAAC,eAAe,GAAG,IAAI,CAAC;QAEvC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;QAC7D,eAAe,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,QAAqC;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACjC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;QAChC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,0CAA0C,CAAC,IAAY;QAC1D,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,OAAO,OAAO,GAAG,qCAAqC,CAAC,GAAG,EAAE;YACxD,IAAI,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,EAAE;gBACnB,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACnD;YACD,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC;SAC1B;IACL,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,OAAO,GAAG,IAAI;QAC3B,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACnD,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;YACjD,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;gBACpB,mBAAmB,CAAC,SAAS,GAAG,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;gBACvE,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;aACpD;iBAAM;gBACH,mBAAmB,CAAC,SAAS,GAAG,QAAQ,CAAC;gBACzC,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;aAC1C;SACJ;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE;YAChC,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC;YAC1C,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC;YAC5E,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;SACvE;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;IACxC,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { NodeGeometryBlock } from \"./nodeGeometryBlock\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"./Enums/nodeGeometryConnectionPointTypes\";\r\nimport type { GeometryInputBlock } from \"./Blocks/geometryInputBlock\";\r\nimport type { NodeGeometryBuildState } from \"./nodeGeometryBuildState\";\r\n\r\n/**\r\n * Enum used to define the compatibility state between two connection points\r\n */\r\nexport enum NodeGeometryConnectionPointCompatibilityStates {\r\n    /** Points are compatibles */\r\n    Compatible,\r\n    /** Points are incompatible because of their types */\r\n    TypeIncompatible,\r\n    /** Points are incompatible because they are in the same hierarchy **/\r\n    HierarchyIssue,\r\n}\r\n\r\n/**\r\n * Defines the direction of a connection point\r\n */\r\nexport enum NodeGeometryConnectionPointDirection {\r\n    /** Input */\r\n    Input,\r\n    /** Output */\r\n    Output,\r\n}\r\n\r\n/**\r\n * Defines a connection point for a block\r\n */\r\nexport class NodeGeometryConnectionPoint {\r\n    /** @internal */\r\n    public _ownerBlock: NodeGeometryBlock;\r\n    /** @internal */\r\n    public _connectedPoint: Nullable<NodeGeometryConnectionPoint> = null;\r\n    /** @internal */\r\n    public _storedValue: any = null;\r\n    /** @internal */\r\n    public _storedFunction: Nullable<(state: NodeGeometryBuildState) => any> = null;\r\n\r\n    /** @internal */\r\n    public _acceptedConnectionPointType: Nullable<NodeGeometryConnectionPoint> = null;\r\n\r\n    private _endpoints = new Array<NodeGeometryConnectionPoint>();\r\n    private _direction: NodeGeometryConnectionPointDirection;\r\n    private _type = NodeGeometryBlockConnectionPointTypes.Geometry;\r\n\r\n    /** @internal */\r\n    public _linkedConnectionSource: Nullable<NodeGeometryConnectionPoint> = null;\r\n\r\n    /** @internal */\r\n    public _typeConnectionSource: Nullable<NodeGeometryConnectionPoint> = null;\r\n\r\n    /** @internal */\r\n    public _defaultConnectionPointType: Nullable<NodeGeometryBlockConnectionPointTypes> = null;\r\n\r\n    /** Gets the direction of the point */\r\n    public get direction() {\r\n        return this._direction;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the additional types supported by this connection point\r\n     */\r\n    public acceptedConnectionPointTypes: NodeGeometryBlockConnectionPointTypes[] = [];\r\n\r\n    /**\r\n     * Gets or sets the additional types excluded by this connection point\r\n     */\r\n    public excludedConnectionPointTypes: NodeGeometryBlockConnectionPointTypes[] = [];\r\n\r\n    /**\r\n     * Observable triggered when this point is connected\r\n     */\r\n    public onConnectionObservable = new Observable<NodeGeometryConnectionPoint>();\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that this connection point is exposed on a frame\r\n     */\r\n    public isExposedOnFrame: boolean = false;\r\n\r\n    /**\r\n     * Gets or sets number indicating the position that the port is exposed to on a frame\r\n     */\r\n    public exposedPortPosition: number = -1;\r\n\r\n    /**\r\n     * Gets the default value used for this point at creation time\r\n     */\r\n    public defaultValue: Nullable<any> = null;\r\n\r\n    /**\r\n     * Gets or sets the default value used for this point if nothing is connected\r\n     */\r\n    public value: Nullable<any> = null;\r\n\r\n    /**\r\n     * Gets or sets the min value accepted for this point if nothing is connected\r\n     */\r\n    public valueMin: Nullable<any> = null;\r\n\r\n    /**\r\n     * Gets or sets the max value accepted for this point if nothing is connected\r\n     */\r\n    public valueMax: Nullable<any> = null;\r\n\r\n    /**\r\n     * Gets or sets the connection point type (default is float)\r\n     */\r\n    public get type(): NodeGeometryBlockConnectionPointTypes {\r\n        if (this._type === NodeGeometryBlockConnectionPointTypes.AutoDetect) {\r\n            if (this._ownerBlock.isInput) {\r\n                return (this._ownerBlock as GeometryInputBlock).type;\r\n            }\r\n\r\n            if (this._connectedPoint) {\r\n                return this._connectedPoint.type;\r\n            }\r\n\r\n            if (this._linkedConnectionSource && this._linkedConnectionSource.isConnected) {\r\n                return this._linkedConnectionSource.type;\r\n            }\r\n        }\r\n\r\n        if (this._type === NodeGeometryBlockConnectionPointTypes.BasedOnInput) {\r\n            if (this._typeConnectionSource) {\r\n                if (!this._typeConnectionSource.isConnected && this._defaultConnectionPointType) {\r\n                    return this._defaultConnectionPointType;\r\n                }\r\n                return this._typeConnectionSource.type;\r\n            } else if (this._defaultConnectionPointType) {\r\n                return this._defaultConnectionPointType;\r\n            }\r\n        }\r\n\r\n        return this._type;\r\n    }\r\n\r\n    public set type(value: NodeGeometryBlockConnectionPointTypes) {\r\n        this._type = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the connection point name\r\n     */\r\n    public name: string;\r\n\r\n    /**\r\n     * Gets or sets the connection point display name\r\n     */\r\n    public displayName: string;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that this connection point can be omitted\r\n     */\r\n    public isOptional: boolean;\r\n\r\n    /**\r\n     * Gets a boolean indicating that the current point is connected to another NodeMaterialBlock\r\n     */\r\n    public get isConnected(): boolean {\r\n        return this.connectedPoint !== null || this.hasEndpoints;\r\n    }\r\n\r\n    /** Get the other side of the connection (if any) */\r\n    public get connectedPoint(): Nullable<NodeGeometryConnectionPoint> {\r\n        return this._connectedPoint;\r\n    }\r\n\r\n    /** Get the block that owns this connection point */\r\n    public get ownerBlock(): NodeGeometryBlock {\r\n        return this._ownerBlock;\r\n    }\r\n\r\n    /** Get the block connected on the other side of this connection (if any) */\r\n    public get sourceBlock(): Nullable<NodeGeometryBlock> {\r\n        if (!this._connectedPoint) {\r\n            return null;\r\n        }\r\n\r\n        return this._connectedPoint.ownerBlock;\r\n    }\r\n\r\n    /** Get the block connected on the endpoints of this connection (if any) */\r\n    public get connectedBlocks(): Array<NodeGeometryBlock> {\r\n        if (this._endpoints.length === 0) {\r\n            return [];\r\n        }\r\n\r\n        return this._endpoints.map((e) => e.ownerBlock);\r\n    }\r\n\r\n    /** Gets the list of connected endpoints */\r\n    public get endpoints() {\r\n        return this._endpoints;\r\n    }\r\n\r\n    /** Gets a boolean indicating if that output point is connected to at least one input */\r\n    public get hasEndpoints(): boolean {\r\n        return this._endpoints && this._endpoints.length > 0;\r\n    }\r\n\r\n    /** Get the inner type (ie AutoDetect for instance instead of the inferred one) */\r\n    public get innerType() {\r\n        if (this._linkedConnectionSource && this._linkedConnectionSource.isConnected) {\r\n            return this.type;\r\n        }\r\n        return this._type;\r\n    }\r\n\r\n    /** @internal */\r\n    public _callCount = 0;\r\n\r\n    /** @internal */\r\n    public _executionCount = 0;\r\n\r\n    /** @internal */\r\n    public _resetCounters() {\r\n        this._callCount = 0;\r\n        this._executionCount = 0;\r\n    }\r\n\r\n    /**\r\n     * Gets the number of times this point was called\r\n     */\r\n    public get callCount() {\r\n        return this._callCount;\r\n    }\r\n\r\n    /**\r\n     * Gets the number of times this point was executed\r\n     */\r\n    public get executionCount() {\r\n        return this._executionCount;\r\n    }\r\n\r\n    /**\r\n     * Gets the value represented by this connection point\r\n     * @param state current evaluation state\r\n     * @returns the connected value or the value if nothing is connected\r\n     */\r\n    public getConnectedValue(state: NodeGeometryBuildState) {\r\n        if (this.isConnected) {\r\n            if (this._connectedPoint?._storedFunction) {\r\n                this._connectedPoint!._callCount++;\r\n                this._connectedPoint!._executionCount++;\r\n                return this._connectedPoint!._storedFunction(state);\r\n            }\r\n            this._connectedPoint!._callCount++;\r\n            this._connectedPoint!._executionCount = 1;\r\n            return this._connectedPoint!._storedValue;\r\n        }\r\n        this._callCount++;\r\n        this._executionCount = 1;\r\n        return this.value;\r\n    }\r\n\r\n    /**\r\n     * Creates a new connection point\r\n     * @param name defines the connection point name\r\n     * @param ownerBlock defines the block hosting this connection point\r\n     * @param direction defines the direction of the connection point\r\n     */\r\n    public constructor(name: string, ownerBlock: NodeGeometryBlock, direction: NodeGeometryConnectionPointDirection) {\r\n        this._ownerBlock = ownerBlock;\r\n        this.name = name;\r\n        this._direction = direction;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name e.g. \"NodeMaterialConnectionPoint\"\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"NodeGeometryConnectionPoint\";\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the current point can be connected to another point\r\n     * @param connectionPoint defines the other connection point\r\n     * @returns a boolean\r\n     */\r\n    public canConnectTo(connectionPoint: NodeGeometryConnectionPoint) {\r\n        return this.checkCompatibilityState(connectionPoint) === NodeGeometryConnectionPointCompatibilityStates.Compatible;\r\n    }\r\n\r\n    /**\r\n     * Gets a number indicating if the current point can be connected to another point\r\n     * @param connectionPoint defines the other connection point\r\n     * @returns a number defining the compatibility state\r\n     */\r\n    public checkCompatibilityState(connectionPoint: NodeGeometryConnectionPoint): NodeGeometryConnectionPointCompatibilityStates {\r\n        const ownerBlock = this._ownerBlock;\r\n        const otherBlock = connectionPoint.ownerBlock;\r\n\r\n        if (this.type !== connectionPoint.type && connectionPoint.innerType !== NodeGeometryBlockConnectionPointTypes.AutoDetect) {\r\n            // Accepted types\r\n            if (connectionPoint.acceptedConnectionPointTypes && connectionPoint.acceptedConnectionPointTypes.indexOf(this.type) !== -1) {\r\n                return NodeGeometryConnectionPointCompatibilityStates.Compatible;\r\n            } else {\r\n                return NodeGeometryConnectionPointCompatibilityStates.TypeIncompatible;\r\n            }\r\n        }\r\n\r\n        // Excluded\r\n        if (connectionPoint.excludedConnectionPointTypes && connectionPoint.excludedConnectionPointTypes.indexOf(this.type) !== -1) {\r\n            return NodeGeometryConnectionPointCompatibilityStates.TypeIncompatible;\r\n        }\r\n\r\n        // Check hierarchy\r\n        let targetBlock = otherBlock;\r\n        let sourceBlock = ownerBlock;\r\n        if (this.direction === NodeGeometryConnectionPointDirection.Input) {\r\n            targetBlock = ownerBlock;\r\n            sourceBlock = otherBlock;\r\n        }\r\n\r\n        if (targetBlock.isAnAncestorOf(sourceBlock)) {\r\n            return NodeGeometryConnectionPointCompatibilityStates.HierarchyIssue;\r\n        }\r\n\r\n        return NodeGeometryConnectionPointCompatibilityStates.Compatible;\r\n    }\r\n\r\n    /**\r\n     * Connect this point to another connection point\r\n     * @param connectionPoint defines the other connection point\r\n     * @param ignoreConstraints defines if the system will ignore connection type constraints (default is false)\r\n     * @returns the current connection point\r\n     */\r\n    public connectTo(connectionPoint: NodeGeometryConnectionPoint, ignoreConstraints = false): NodeGeometryConnectionPoint {\r\n        if (!ignoreConstraints && !this.canConnectTo(connectionPoint)) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"Cannot connect these two connectors.\";\r\n        }\r\n\r\n        this._endpoints.push(connectionPoint);\r\n        connectionPoint._connectedPoint = this;\r\n\r\n        this.onConnectionObservable.notifyObservers(connectionPoint);\r\n        connectionPoint.onConnectionObservable.notifyObservers(this);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Disconnect this point from one of his endpoint\r\n     * @param endpoint defines the other connection point\r\n     * @returns the current connection point\r\n     */\r\n    public disconnectFrom(endpoint: NodeGeometryConnectionPoint): NodeGeometryConnectionPoint {\r\n        const index = this._endpoints.indexOf(endpoint);\r\n\r\n        if (index === -1) {\r\n            return this;\r\n        }\r\n\r\n        this._endpoints.splice(index, 1);\r\n        endpoint._connectedPoint = null;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Fill the list of excluded connection point types with all types other than those passed in the parameter\r\n     * @param mask Types (ORed values of NodeMaterialBlockConnectionPointTypes) that are allowed, and thus will not be pushed to the excluded list\r\n     */\r\n    public addExcludedConnectionPointFromAllowedTypes(mask: number): void {\r\n        let bitmask = 1;\r\n        while (bitmask < NodeGeometryBlockConnectionPointTypes.All) {\r\n            if (!(mask & bitmask)) {\r\n                this.excludedConnectionPointTypes.push(bitmask);\r\n            }\r\n            bitmask = bitmask << 1;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serializes this point in a JSON representation\r\n     * @param isInput defines if the connection point is an input (default is true)\r\n     * @returns the serialized point object\r\n     */\r\n    public serialize(isInput = true): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.displayName = this.displayName;\r\n        if (this.value !== undefined && this.value !== null) {\r\n            if (this.value.asArray) {\r\n                serializationObject.valueType = \"BABYLON.\" + this.value.getClassName();\r\n                serializationObject.value = this.value.asArray();\r\n            } else {\r\n                serializationObject.valueType = \"number\";\r\n                serializationObject.value = this.value;\r\n            }\r\n        }\r\n\r\n        if (isInput && this.connectedPoint) {\r\n            serializationObject.inputName = this.name;\r\n            serializationObject.targetBlockId = this.connectedPoint.ownerBlock.uniqueId;\r\n            serializationObject.targetConnectionName = this.connectedPoint.name;\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Release resources\r\n     */\r\n    public dispose() {\r\n        this.onConnectionObservable.clear();\r\n    }\r\n}\r\n"]}