{"version": 3, "file": "stereoscopicGamepadCamera.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/Stereoscopic/stereoscopicGamepadCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAE5D,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,sBAAsB,EAAE,MAAM,iCAAiC,CAAC;AAEzE,IAAI,CAAC,kBAAkB,CAAC,2BAA2B,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;IAC1E,OAAO,GAAG,EAAE,CAAC,IAAI,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,mBAAmB,EAAE,OAAO,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;AAC3I,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,MAAM,OAAO,yBAA0B,SAAQ,aAAa;IACxD;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,kBAA0B,EAAE,wBAAiC,EAAE,KAAa;QACrH,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAgBvB,gBAAW,GAAG,GAAG,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAfvD,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;QACzD,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC,CAAC,MAAM,CAAC,yCAAyC,CAAC,CAAC,CAAC,MAAM,CAAC,+BAA+B,EAAE;YACxI,kBAAkB,EAAE,kBAAkB;SACzC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,2BAA2B,CAAC;IACvC,CAAC;CAGJ", "sourcesContent": ["import { Camera } from \"../../Cameras/camera\";\r\nimport { GamepadCamera } from \"../../Cameras/gamepadCamera\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { Node } from \"../../node\";\r\nimport { setStereoscopicRigMode } from \"../RigModes/stereoscopicRigMode\";\r\n\r\nNode.AddNodeConstructor(\"StereoscopicGamepadCamera\", (name, scene, options) => {\r\n    return () => new StereoscopicGamepadCamera(name, Vector3.Zero(), options.interaxial_distance, options.isStereoscopicSideBySide, scene);\r\n});\r\n\r\n/**\r\n * Camera used to simulate stereoscopic rendering (based on GamepadCamera)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras\r\n */\r\nexport class StereoscopicGamepadCamera extends GamepadCamera {\r\n    /**\r\n     * Creates a new StereoscopicGamepadCamera\r\n     * @param name defines camera name\r\n     * @param position defines initial position\r\n     * @param interaxialDistance defines distance between each color axis\r\n     * @param isStereoscopicSideBySide defines is stereoscopic is done side by side or over under\r\n     * @param scene defines the hosting scene\r\n     */\r\n    constructor(name: string, position: Vector3, interaxialDistance: number, isStereoscopicSideBySide: boolean, scene?: Scene) {\r\n        super(name, position, scene);\r\n        this.interaxialDistance = interaxialDistance;\r\n        this.isStereoscopicSideBySide = isStereoscopicSideBySide;\r\n        this.setCameraRigMode(isStereoscopicSideBySide ? Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL : Camera.RIG_MODE_STEREOSCOPIC_OVERUNDER, {\r\n            interaxialDistance: interaxialDistance,\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Gets camera class name\r\n     * @returns StereoscopicGamepadCamera\r\n     */\r\n    public getClassName(): string {\r\n        return \"StereoscopicGamepadCamera\";\r\n    }\r\n\r\n    protected _setRigMode = () => setStereoscopicRigMode(this);\r\n}\r\n"]}