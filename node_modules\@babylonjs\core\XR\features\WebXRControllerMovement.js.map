{"version": 3, "file": "WebXRControllerMovement.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRControllerMovement.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAOjF,OAAO,EAAE,wBAAwB,EAAE,MAAM,8CAA8C,CAAC;AACxF,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AACtE,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAuHzC;;;;GAIG;AACH,MAAM,OAAO,uBAAwB,SAAQ,oBAAoB;IA0D7D;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC;IAChD,CAAC;IAED;;;OAGG;IACH,IAAW,eAAe,CAAC,OAAgB;QACvC,IAAI,CAAC,eAAe,CAAC,eAAe,GAAG,OAAO,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAW,oCAAoC;QAC3C,OAAO,IAAI,CAAC,eAAe,CAAC,oCAAoC,CAAC;IACrE,CAAC;IAED;;;OAGG;IACH,IAAW,oCAAoC,CAAC,WAAoB;QAChE,IAAI,CAAC,eAAe,CAAC,oCAAoC,GAAG,WAAW,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACH,IAAW,aAAa,CAAC,aAAqB;QAC1C,IAAI,CAAC,eAAe,CAAC,aAAa,GAAG,aAAa,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;IAClD,CAAC;IAED;;;OAGG;IACH,IAAW,iBAAiB,CAAC,iBAAyB;QAClD,IAAI,CAAC,eAAe,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC;IAChD,CAAC;IAED;;;OAGG;IACH,IAAW,eAAe,CAAC,OAAgB;QACvC,IAAI,CAAC,eAAe,CAAC,eAAe,GAAG,OAAO,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACH,IAAW,aAAa,CAAC,aAAqB;QAC1C,IAAI,CAAC,eAAe,CAAC,aAAa,GAAG,aAAa,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;IAClD,CAAC;IAED;;;OAGG;IACH,IAAW,iBAAiB,CAAC,SAAiB;QAC1C,IAAI,CAAC,eAAe,CAAC,iBAAiB,GAAG,SAAS,CAAC;IACvD,CAAC;IACD;;;;OAIG;IACH,YAAY,iBAAsC,EAAE,OAAwC;QACxF,KAAK,CAAC,iBAAiB,CAAC,CAAC;QA9KrB,iBAAY,GAKhB,EAAE,CAAC;QAEC,uCAAkC,GAAuD,EAAE,CAAC;QAGpG,qEAAqE;QAC7D,uBAAkB,GAAe,IAAI,UAAU,EAAE,CAAC;QAI1D,SAAS;QACD,uBAAkB,GAAW,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC/C,6BAAwB,GAAY,IAAI,OAAO,EAAE,CAAC;QAClD,4BAAuB,GAAY,IAAI,OAAO,EAAE,CAAC;QACjD,yBAAoB,GAAe,IAAI,UAAU,EAAE,CAAC;QAkQpD,sBAAiB,GAAG,CAAC,YAA8B,EAAE,EAAE;YAC3D,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;gBAC1C,mBAAmB;gBACnB,OAAO;aACV;YAED,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG;gBACvC,YAAY;gBACZ,oBAAoB,EAAE,EAAE;aAC3B,CAAC;YACF,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAEhE,uEAAuE;YACvE,IAAI,cAAc,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,KAAK,iBAAiB,IAAI,cAAc,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,EAAE;gBAChI,4BAA4B;gBAC5B,MAAM,cAAc,GAAG,GAAG,EAAE;oBACxB,IAAI,YAAY,CAAC,gBAAgB,EAAE;wBAC/B,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,kCAAkC,EAAE;4BAChE,IAAI,SAAS,GAAuC,IAAI,CAAC;4BAEzD,IAAI,YAAY,CAAC,qBAAqB,EAAE;gCACpC,KAAK,MAAM,aAAa,IAAI,YAAY,CAAC,qBAAqB,EAAE;oCAC5D,MAAM,eAAe,GAAG,YAAY,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;oCACxF,IAAI,eAAe,KAAK,IAAI,EAAE;wCAC1B,SAAS,GAAG,eAAe,CAAC;wCAC5B,MAAM;qCACT;iCACJ;6BACJ;4BAED,IAAI,YAAY,CAAC,iBAAiB,EAAE;gCAChC,MAAM,aAAa,GAAG,YAAY,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;gCACvE,IAAI,aAAa,KAAK,IAAI,EAAE;oCACxB,SAAS;iCACZ;gCACD,SAAS,GAAG,aAAa,CAAC;6BAC7B;4BAED,IAAI,OAAO,YAAY,CAAC,2BAA2B,KAAK,UAAU,EAAE;gCAChE,sEAAsE;gCACtE,SAAS,GAAG,YAAY,CAAC,2BAA2B,CAAC,YAAY,CAAC,CAAC;6BACtE;4BAED,IAAI,SAAS,IAAI,YAAY,CAAC,eAAe,EAAE;gCAC3C,IAAI,YAAY,CAAC,WAAW,CAAC,UAAU,KAAK,YAAY,CAAC,eAAe,EAAE;oCACtE,SAAS,CAAC,kBAAkB;iCAC/B;6BACJ;4BAED,IAAI,SAAS,KAAK,IAAI,EAAE;gCACpB,SAAS,CAAC,kBAAkB;6BAC/B;4BAED,MAAM,mBAAmB,GAAwB;gCAC7C,yBAAyB,EAAE,YAAY;gCACvC,SAAS;6BACZ,CAAC;4BACF,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;4BAE9D,IAAI,oBAAoB,IAAI,YAAY,EAAE;gCACtC,mBAAmB,CAAC,qBAAqB,GAAG,SAAS,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;oCAChG,YAAY,CAAC,kBAAkB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gCACxG,CAAC,CAAC,CAAC;6BACN;4BAED,IAAI,sBAAsB,IAAI,YAAY,EAAE;gCACxC,mBAAmB,CAAC,uBAAuB,GAAG,SAAS,CAAC,8BAA8B,CAAC,GAAG,CAAC,GAAG,EAAE;oCAC5F,IAAI,SAAU,CAAC,OAAO,CAAC,OAAO,EAAE;wCAC5B,YAAY,CAAC,oBAAoB,CAAC,SAAU,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;qCAC3H;gCACL,CAAC,CAAC,CAAC;6BACN;yBACJ;qBACJ;gBACL,CAAC,CAAC;gBAEF,IAAI,YAAY,CAAC,gBAAgB,EAAE;oBAC/B,cAAc,EAAE,CAAC;iBACpB;qBAAM;oBACH,YAAY,CAAC,gCAAgC,CAAC,OAAO,CAAC,GAAG,EAAE;wBACvD,cAAc,EAAE,CAAC;oBACrB,CAAC,CAAC,CAAC;iBACN;aACJ;QACL,CAAC,CAAC;QAzLE,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;YAC3C,KAAK,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAC1E,OAAO;SACV;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,gCAAgC,CAAC,EAAE;YACzD,IAAI,CAAC,kCAAkC,GAAG,OAAO,CAAC,gCAAgC,CAAC;SACtF;aAAM;YACH,IAAI,CAAC,kCAAkC,GAAG,uBAAuB,CAAC,aAAa,CAAC,OAAO,CAAC;SAC3F;QAED,8CAA8C;QAC9C,IAAI,CAAC,eAAe,GAAG;YACnB,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,IAAI;YAChD,oCAAoC,EAAE,OAAO,CAAC,oCAAoC,IAAI,IAAI;YAC1F,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,CAAC;YACzC,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,IAAI,IAAI;YACpD,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,IAAI;YAChD,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,GAAG;YAC3C,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,IAAI,IAAI;SACvD,CAAC;QAEF,IAAI,CAAC,cAAc,GAAG;YAClB,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,CAAC;YACR,OAAO,EAAE,CAAC;YACV,OAAO,EAAE,CAAC;SACb,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC;IACpC,CAAC;IAEM,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC1D,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,2BAA2B,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC9F,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,6BAA6B,EAAE,CAAC,UAA4B,EAAE,EAAE;YACrG,wBAAwB;YACxB,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YACpD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QAEvB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACO,UAAU,CAAC,QAAiB;QAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE;YAC3E,kBAAkB;YAClB,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,CAAC;YAC5E,MAAM,SAAS,GAAG,WAAW,GAAG,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAExK,IAAI,IAAI,CAAC,eAAe,CAAC,oCAAoC,EAAE;gBAC3D,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,SAAS,CAAC;gBACrD,UAAU,CAAC,yBAAyB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACjF,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC/G;iBAAM;gBACH,2FAA2F;gBAC3F,qFAAqF;gBAErF,UAAU,CAAC,yBAAyB,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACvF,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;aACtE;SACJ;aAAM,IAAI,IAAI,CAAC,eAAe,CAAC,oCAAoC,EAAE;YAClE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;SAC/E;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE;YAClG,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC7E,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC9J,4DAA4D;YAC5D,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACxH,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,wBAAwB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAElI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACnF;IACL,CAAC;IAwFO,iBAAiB,CAAC,oBAA4B;QAClD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QAC/D,IAAI,CAAC,cAAc,EAAE;YACjB,OAAO;SACV;QAED,KAAK,MAAM,mBAAmB,IAAI,cAAc,CAAC,oBAAoB,EAAE;YACnE,IAAI,mBAAmB,CAAC,qBAAqB,EAAE;gBAC3C,mBAAmB,CAAC,SAAS,CAAC,4BAA4B,CAAC,MAAM,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,CAAC;aAChH;YACD,IAAI,mBAAmB,CAAC,uBAAuB,EAAE;gBAC7C,mBAAmB,CAAC,SAAS,CAAC,8BAA8B,CAAC,MAAM,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,CAAC;aACpH;SACJ;QAED,sBAAsB;QACtB,OAAO,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;IACnD,CAAC;;AAvWD;;GAEG;AACoB,4BAAI,GAAG,gBAAgB,CAAC,QAAQ,AAA5B,CAA6B;AAExD;;GAEG;AACoB,qCAAa,GAA0E;IAC1G,OAAO,EAAE;QACL;YACI,qBAAqB,EAAE,CAAC,wBAAwB,CAAC,eAAe,EAAE,wBAAwB,CAAC,aAAa,CAAC;YACzG,eAAe,EAAE,MAAM;YACvB,kBAAkB,EAAE,CAAC,IAAqC,EAAE,aAA2C,EAAE,cAAqD,EAAE,EAAE;gBAC9J,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzF,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7F,CAAC;SACJ;QACD;YACI,qBAAqB,EAAE,CAAC,wBAAwB,CAAC,eAAe,EAAE,wBAAwB,CAAC,aAAa,CAAC;YACzG,eAAe,EAAE,OAAO;YACxB,kBAAkB,EAAE,CAAC,IAAqC,EAAE,aAA2C,EAAE,cAAqD,EAAE,EAAE;gBAC9J,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvF,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3F,CAAC;SACJ;KACJ;CACJ,AAnBmC,CAmBlC;AAEF;;;;GAIG;AACoB,+BAAO,GAAG,CAAC,AAAJ,CAAK;AAwUvC,oBAAoB,CAAC,eAAe,CAChC,uBAAuB,CAAC,IAAI,EAC5B,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,uBAAuB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AACxE,CAAC,EACD,uBAAuB,CAAC,OAAO,EAC/B,IAAI,CACP,CAAC", "sourcesContent": ["import { WebXRFeaturesManager, WebXRFeatureName } from \"../webXRFeaturesManager\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { WebXRInput } from \"../webXRInput\";\r\nimport type { WebXRInputSource } from \"../webXRInputSource\";\r\nimport type { IWebXRMotionControllerAxesValue, IWebXRMotionControllerComponentChangesValues } from \"../motionController/webXRControllerComponent\";\r\nimport { WebXRControllerComponent } from \"../motionController/webXRControllerComponent\";\r\nimport { Matrix, Quaternion, Vector3 } from \"../../Maths/math.vector\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport type { MotionControllerComponentType } from \"../motionController/webXRAbstractMotionController\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n\r\n/**\r\n * The options container for the controller movement module\r\n */\r\nexport interface IWebXRControllerMovementOptions {\r\n    /**\r\n     * Override default behaviour and provide your own movement controls\r\n     */\r\n    customRegistrationConfigurations?: WebXRControllerMovementRegistrationConfiguration[];\r\n    /**\r\n     * Is movement enabled\r\n     */\r\n    movementEnabled?: boolean;\r\n    /**\r\n     * Camera direction follows view pose and movement by default will move independently of the viewer's pose.\r\n     */\r\n    movementOrientationFollowsViewerPose: boolean;\r\n    /**\r\n     * Movement speed factor (default is 1.0)\r\n     */\r\n    movementSpeed?: number;\r\n    /**\r\n     * Minimum threshold the controller's thumbstick/touchpad must pass before being recognized for movement (avoids jitter/unintentional movement)\r\n     */\r\n    movementThreshold?: number;\r\n    /**\r\n     * Is rotation enabled\r\n     */\r\n    rotationEnabled?: boolean;\r\n    /**\r\n     * Minimum threshold the controller's thumstick/touchpad must pass before being recognized for rotation (avoids jitter/unintentional rotation)\r\n     */\r\n    rotationThreshold?: number;\r\n    /**\r\n     * Movement speed factor (default is 1.0)\r\n     */\r\n    rotationSpeed?: number;\r\n    /**\r\n     * Babylon XR Input class for controller\r\n     */\r\n    xrInput: WebXRInput;\r\n}\r\n\r\n/**\r\n * Feature context is used in handlers and on each XR frame to control the camera movement/direction.\r\n */\r\nexport type WebXRControllerMovementFeatureContext = {\r\n    movementEnabled: boolean;\r\n    movementOrientationFollowsViewerPose: boolean;\r\n    movementSpeed: number;\r\n    movementThreshold: number;\r\n    rotationEnabled: boolean;\r\n    rotationSpeed: number;\r\n    rotationThreshold: number;\r\n};\r\n\r\n/**\r\n * Current state of Movements shared across components and handlers.\r\n */\r\nexport type WebXRControllerMovementState = {\r\n    moveX: number;\r\n    moveY: number;\r\n    rotateX: number;\r\n    rotateY: number;\r\n};\r\n\r\n/**\r\n * Button of Axis Handler must be specified.\r\n */\r\nexport type WebXRControllerMovementRegistrationConfiguration = {\r\n    /**\r\n     * handlers are filtered to these types only\r\n     */\r\n    allowedComponentTypes?: MotionControllerComponentType[];\r\n    /**\r\n     * For registering movement to specific hand only.  Useful if your app has a \"main hand\" and \"off hand\" for determining the functionality of a controller.\r\n     */\r\n    forceHandedness?: XRHandedness;\r\n    /**\r\n     * For main component only (useful for buttons and may not trigger axis changes).\r\n     */\r\n    mainComponentOnly?: boolean;\r\n    /**\r\n     * Additional predicate to apply to controllers to restrict a handler being added.\r\n     */\r\n    componentSelectionPredicate?: (xrController: WebXRInputSource) => Nullable<WebXRControllerComponent>;\r\n} & (\r\n    | {\r\n          /**\r\n           * Called when axis changes occur.\r\n           */\r\n          axisChangedHandler: (\r\n              axes: IWebXRMotionControllerAxesValue,\r\n              movementState: WebXRControllerMovementState,\r\n              featureContext: WebXRControllerMovementFeatureContext,\r\n              xrInput: WebXRInput\r\n          ) => void;\r\n      }\r\n    | {\r\n          /**\r\n           * Called when the button state changes.\r\n           */\r\n          buttonChangedhandler: (\r\n              pressed: IWebXRMotionControllerComponentChangesValues<boolean>,\r\n              movementState: WebXRControllerMovementState,\r\n              featureContext: WebXRControllerMovementFeatureContext,\r\n              xrInput: WebXRInput\r\n          ) => void;\r\n      }\r\n);\r\n\r\ntype RegisteredComponent = {\r\n    registrationConfiguration: WebXRControllerMovementRegistrationConfiguration;\r\n    component: WebXRControllerComponent;\r\n    onAxisChangedObserver?: Nullable<Observer<IWebXRMotionControllerAxesValue>>;\r\n    onButtonChangedObserver?: Nullable<Observer<WebXRControllerComponent>>;\r\n};\r\n\r\n/**\r\n * This is a movement feature to be used with WebXR-enabled motion controllers.\r\n * When enabled and attached, the feature will allow a user to move around and rotate in the scene using\r\n * the input of the attached controllers.\r\n */\r\nexport class WebXRControllerMovement extends WebXRAbstractFeature {\r\n    private _controllers: {\r\n        [controllerUniqueId: string]: {\r\n            xrController: WebXRInputSource;\r\n            registeredComponents: RegisteredComponent[];\r\n        };\r\n    } = {};\r\n\r\n    private _currentRegistrationConfigurations: WebXRControllerMovementRegistrationConfiguration[] = [];\r\n    // Feature configuration is syncronized - this is passed to all handlers (reduce GC pressure).\r\n    private _featureContext: WebXRControllerMovementFeatureContext;\r\n    // forward direction for movement, which may differ from viewer pose.\r\n    private _movementDirection: Quaternion = new Quaternion();\r\n    private _movementState: WebXRControllerMovementState;\r\n    private _xrInput: WebXRInput;\r\n\r\n    // unused\r\n    private _tmpRotationMatrix: Matrix = Matrix.Identity();\r\n    private _tmpTranslationDirection: Vector3 = new Vector3();\r\n    private _tmpMovementTranslation: Vector3 = new Vector3();\r\n    private _tempCacheQuaternion: Quaternion = new Quaternion();\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.MOVEMENT;\r\n\r\n    /**\r\n     * Standard controller configurations.\r\n     */\r\n    public static readonly REGISTRATIONS: { [key: string]: WebXRControllerMovementRegistrationConfiguration[] } = {\r\n        default: [\r\n            {\r\n                allowedComponentTypes: [WebXRControllerComponent.THUMBSTICK_TYPE, WebXRControllerComponent.TOUCHPAD_TYPE],\r\n                forceHandedness: \"left\",\r\n                axisChangedHandler: (axes: IWebXRMotionControllerAxesValue, movementState: WebXRControllerMovementState, featureContext: WebXRControllerMovementFeatureContext) => {\r\n                    movementState.rotateX = Math.abs(axes.x) > featureContext.rotationThreshold ? axes.x : 0;\r\n                    movementState.rotateY = Math.abs(axes.y) > featureContext.rotationThreshold ? axes.y : 0;\r\n                },\r\n            },\r\n            {\r\n                allowedComponentTypes: [WebXRControllerComponent.THUMBSTICK_TYPE, WebXRControllerComponent.TOUCHPAD_TYPE],\r\n                forceHandedness: \"right\",\r\n                axisChangedHandler: (axes: IWebXRMotionControllerAxesValue, movementState: WebXRControllerMovementState, featureContext: WebXRControllerMovementFeatureContext) => {\r\n                    movementState.moveX = Math.abs(axes.x) > featureContext.movementThreshold ? axes.x : 0;\r\n                    movementState.moveY = Math.abs(axes.y) > featureContext.movementThreshold ? axes.y : 0;\r\n                },\r\n            },\r\n        ],\r\n    };\r\n\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the webxr specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * Current movement direction.  Will be null before XR Frames have been processed.\r\n     */\r\n    public get movementDirection(): Quaternion {\r\n        return this._movementDirection;\r\n    }\r\n\r\n    /**\r\n     * Is movement enabled\r\n     */\r\n    public get movementEnabled(): boolean {\r\n        return this._featureContext.movementEnabled;\r\n    }\r\n\r\n    /**\r\n     * Sets whether movement is enabled or not\r\n     * @param enabled is movement enabled\r\n     */\r\n    public set movementEnabled(enabled: boolean) {\r\n        this._featureContext.movementEnabled = enabled;\r\n    }\r\n\r\n    /**\r\n     * If movement follows viewer pose\r\n     */\r\n    public get movementOrientationFollowsViewerPose(): boolean {\r\n        return this._featureContext.movementOrientationFollowsViewerPose;\r\n    }\r\n\r\n    /**\r\n     * Sets whether movement follows viewer pose\r\n     * @param followsPose is movement should follow viewer pose\r\n     */\r\n    public set movementOrientationFollowsViewerPose(followsPose: boolean) {\r\n        this._featureContext.movementOrientationFollowsViewerPose = followsPose;\r\n    }\r\n\r\n    /**\r\n     * Gets movement speed\r\n     */\r\n    public get movementSpeed(): number {\r\n        return this._featureContext.movementSpeed;\r\n    }\r\n\r\n    /**\r\n     * Sets movement speed\r\n     * @param movementSpeed movement speed\r\n     */\r\n    public set movementSpeed(movementSpeed: number) {\r\n        this._featureContext.movementSpeed = movementSpeed;\r\n    }\r\n\r\n    /**\r\n     * Gets minimum threshold the controller's thumbstick/touchpad must pass before being recognized for movement (avoids jitter/unintentional movement)\r\n     */\r\n    public get movementThreshold(): number {\r\n        return this._featureContext.movementThreshold;\r\n    }\r\n\r\n    /**\r\n     * Sets minimum threshold the controller's thumbstick/touchpad must pass before being recognized for movement (avoids jitter/unintentional movement)\r\n     * @param movementThreshold new threshold\r\n     */\r\n    public set movementThreshold(movementThreshold: number) {\r\n        this._featureContext.movementThreshold = movementThreshold;\r\n    }\r\n\r\n    /**\r\n     * Is rotation enabled\r\n     */\r\n    public get rotationEnabled(): boolean {\r\n        return this._featureContext.rotationEnabled;\r\n    }\r\n\r\n    /**\r\n     * Sets whether rotation is enabled or not\r\n     * @param enabled is rotation enabled\r\n     */\r\n    public set rotationEnabled(enabled: boolean) {\r\n        this._featureContext.rotationEnabled = enabled;\r\n    }\r\n\r\n    /**\r\n     * Gets rotation speed factor\r\n     */\r\n    public get rotationSpeed(): number {\r\n        return this._featureContext.rotationSpeed;\r\n    }\r\n\r\n    /**\r\n     * Sets rotation speed factor (1.0 is default)\r\n     * @param rotationSpeed new rotation speed factor\r\n     */\r\n    public set rotationSpeed(rotationSpeed: number) {\r\n        this._featureContext.rotationSpeed = rotationSpeed;\r\n    }\r\n\r\n    /**\r\n     * Gets minimum threshold the controller's thumbstick/touchpad must pass before being recognized for rotation (avoids jitter/unintentional rotation)\r\n     */\r\n    public get rotationThreshold(): number {\r\n        return this._featureContext.rotationThreshold;\r\n    }\r\n\r\n    /**\r\n     * Sets minimum threshold the controller's thumbstick/touchpad must pass before being recognized for rotation (avoids jitter/unintentional rotation)\r\n     * @param threshold new threshold\r\n     */\r\n    public set rotationThreshold(threshold: number) {\r\n        this._featureContext.rotationThreshold = threshold;\r\n    }\r\n    /**\r\n     * constructs a new movement controller system\r\n     * @param _xrSessionManager an instance of WebXRSessionManager\r\n     * @param options configuration object for this feature\r\n     */\r\n    constructor(_xrSessionManager: WebXRSessionManager, options: IWebXRControllerMovementOptions) {\r\n        super(_xrSessionManager);\r\n\r\n        if (!options || options.xrInput === undefined) {\r\n            Tools.Error('WebXRControllerMovement feature requires \"xrInput\" option.');\r\n            return;\r\n        }\r\n\r\n        if (Array.isArray(options.customRegistrationConfigurations)) {\r\n            this._currentRegistrationConfigurations = options.customRegistrationConfigurations;\r\n        } else {\r\n            this._currentRegistrationConfigurations = WebXRControllerMovement.REGISTRATIONS.default;\r\n        }\r\n\r\n        // synchronized from feature setter properties\r\n        this._featureContext = {\r\n            movementEnabled: options.movementEnabled || true,\r\n            movementOrientationFollowsViewerPose: options.movementOrientationFollowsViewerPose ?? true,\r\n            movementSpeed: options.movementSpeed ?? 1,\r\n            movementThreshold: options.movementThreshold ?? 0.25,\r\n            rotationEnabled: options.rotationEnabled ?? true,\r\n            rotationSpeed: options.rotationSpeed ?? 1.0,\r\n            rotationThreshold: options.rotationThreshold ?? 0.25,\r\n        };\r\n\r\n        this._movementState = {\r\n            moveX: 0,\r\n            moveY: 0,\r\n            rotateX: 0,\r\n            rotateY: 0,\r\n        };\r\n\r\n        this._xrInput = options.xrInput;\r\n    }\r\n\r\n    public attach(): boolean {\r\n        if (!super.attach()) {\r\n            return false;\r\n        }\r\n\r\n        this._xrInput.controllers.forEach(this._attachController);\r\n        this._addNewAttachObserver(this._xrInput.onControllerAddedObservable, this._attachController);\r\n        this._addNewAttachObserver(this._xrInput.onControllerRemovedObservable, (controller: WebXRInputSource) => {\r\n            // REMOVE the controller\r\n            this._detachController(controller.uniqueId);\r\n        });\r\n\r\n        return true;\r\n    }\r\n\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n\r\n        Object.keys(this._controllers).forEach((controllerId) => {\r\n            this._detachController(controllerId);\r\n        });\r\n\r\n        this._controllers = {};\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Occurs on every XR frame.\r\n     * @param _xrFrame\r\n     */\r\n    protected _onXRFrame(_xrFrame: XRFrame) {\r\n        if (!this.attached) {\r\n            return;\r\n        }\r\n\r\n        if (this._movementState.rotateX !== 0 && this._featureContext.rotationEnabled) {\r\n            // smooth rotation\r\n            const deltaMillis = this._xrSessionManager.scene.getEngine().getDeltaTime();\r\n            const rotationY = deltaMillis * 0.001 * this._featureContext.rotationSpeed * this._movementState.rotateX * (this._xrSessionManager.scene.useRightHandedSystem ? -1 : 1);\r\n\r\n            if (this._featureContext.movementOrientationFollowsViewerPose) {\r\n                this._xrInput.xrCamera.cameraRotation.y += rotationY;\r\n                Quaternion.RotationYawPitchRollToRef(rotationY, 0, 0, this._tempCacheQuaternion);\r\n                this._xrInput.xrCamera.rotationQuaternion.multiplyToRef(this._tempCacheQuaternion, this._movementDirection);\r\n            } else {\r\n                // movement orientation direction does not affect camera.  We use rotation speed multiplier\r\n                // otherwise need to implement inertia and constraints for same feel as TargetCamera.\r\n\r\n                Quaternion.RotationYawPitchRollToRef(rotationY * 3.0, 0, 0, this._tempCacheQuaternion);\r\n                this._movementDirection.multiplyInPlace(this._tempCacheQuaternion);\r\n            }\r\n        } else if (this._featureContext.movementOrientationFollowsViewerPose) {\r\n            this._movementDirection.copyFrom(this._xrInput.xrCamera.rotationQuaternion);\r\n        }\r\n\r\n        if ((this._movementState.moveX || this._movementState.moveY) && this._featureContext.movementEnabled) {\r\n            Matrix.FromQuaternionToRef(this._movementDirection, this._tmpRotationMatrix);\r\n            this._tmpTranslationDirection.set(this._movementState.moveX, 0, this._movementState.moveY * (this._xrSessionManager.scene.useRightHandedSystem ? 1.0 : -1.0));\r\n            // move according to forward direction based on camera speed\r\n            Vector3.TransformCoordinatesToRef(this._tmpTranslationDirection, this._tmpRotationMatrix, this._tmpMovementTranslation);\r\n            this._tmpMovementTranslation.scaleInPlace(this._xrInput.xrCamera._computeLocalCameraSpeed() * this._featureContext.movementSpeed);\r\n\r\n            this._xrInput.xrCamera.cameraDirection.addInPlace(this._tmpMovementTranslation);\r\n        }\r\n    }\r\n\r\n    private _attachController = (xrController: WebXRInputSource) => {\r\n        if (this._controllers[xrController.uniqueId]) {\r\n            // already attached\r\n            return;\r\n        }\r\n\r\n        this._controllers[xrController.uniqueId] = {\r\n            xrController,\r\n            registeredComponents: [],\r\n        };\r\n        const controllerData = this._controllers[xrController.uniqueId];\r\n\r\n        // movement controller only available to gamepad-enabled input sources.\r\n        if (controllerData.xrController.inputSource.targetRayMode === \"tracked-pointer\" && controllerData.xrController.inputSource.gamepad) {\r\n            // motion controller support\r\n            const initController = () => {\r\n                if (xrController.motionController) {\r\n                    for (const registration of this._currentRegistrationConfigurations) {\r\n                        let component: Nullable<WebXRControllerComponent> = null;\r\n\r\n                        if (registration.allowedComponentTypes) {\r\n                            for (const componentType of registration.allowedComponentTypes) {\r\n                                const componentOfType = xrController.motionController.getComponentOfType(componentType);\r\n                                if (componentOfType !== null) {\r\n                                    component = componentOfType;\r\n                                    break;\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        if (registration.mainComponentOnly) {\r\n                            const mainComponent = xrController.motionController.getMainComponent();\r\n                            if (mainComponent === null) {\r\n                                continue;\r\n                            }\r\n                            component = mainComponent;\r\n                        }\r\n\r\n                        if (typeof registration.componentSelectionPredicate === \"function\") {\r\n                            // if does not match we do want to ignore a previously found component\r\n                            component = registration.componentSelectionPredicate(xrController);\r\n                        }\r\n\r\n                        if (component && registration.forceHandedness) {\r\n                            if (xrController.inputSource.handedness !== registration.forceHandedness) {\r\n                                continue; // do not register\r\n                            }\r\n                        }\r\n\r\n                        if (component === null) {\r\n                            continue; // do not register\r\n                        }\r\n\r\n                        const registeredComponent: RegisteredComponent = {\r\n                            registrationConfiguration: registration,\r\n                            component,\r\n                        };\r\n                        controllerData.registeredComponents.push(registeredComponent);\r\n\r\n                        if (\"axisChangedHandler\" in registration) {\r\n                            registeredComponent.onAxisChangedObserver = component.onAxisValueChangedObservable.add((axesData) => {\r\n                                registration.axisChangedHandler(axesData, this._movementState, this._featureContext, this._xrInput);\r\n                            });\r\n                        }\r\n\r\n                        if (\"buttonChangedhandler\" in registration) {\r\n                            registeredComponent.onButtonChangedObserver = component.onButtonStateChangedObservable.add(() => {\r\n                                if (component!.changes.pressed) {\r\n                                    registration.buttonChangedhandler(component!.changes.pressed, this._movementState, this._featureContext, this._xrInput);\r\n                                }\r\n                            });\r\n                        }\r\n                    }\r\n                }\r\n            };\r\n\r\n            if (xrController.motionController) {\r\n                initController();\r\n            } else {\r\n                xrController.onMotionControllerInitObservable.addOnce(() => {\r\n                    initController();\r\n                });\r\n            }\r\n        }\r\n    };\r\n\r\n    private _detachController(xrControllerUniqueId: string) {\r\n        const controllerData = this._controllers[xrControllerUniqueId];\r\n        if (!controllerData) {\r\n            return;\r\n        }\r\n\r\n        for (const registeredComponent of controllerData.registeredComponents) {\r\n            if (registeredComponent.onAxisChangedObserver) {\r\n                registeredComponent.component.onAxisValueChangedObservable.remove(registeredComponent.onAxisChangedObserver);\r\n            }\r\n            if (registeredComponent.onButtonChangedObserver) {\r\n                registeredComponent.component.onButtonStateChangedObservable.remove(registeredComponent.onButtonChangedObserver);\r\n            }\r\n        }\r\n\r\n        // remove from the map\r\n        delete this._controllers[xrControllerUniqueId];\r\n    }\r\n}\r\n\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRControllerMovement.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRControllerMovement(xrSessionManager, options);\r\n    },\r\n    WebXRControllerMovement.Version,\r\n    true\r\n);\r\n"]}