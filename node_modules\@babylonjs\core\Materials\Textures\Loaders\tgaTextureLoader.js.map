{"version": 3, "file": "tgaTextureLoader.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Textures/Loaders/tgaTextureLoader.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAChE,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAIjD;;;GAGG;AACH,gEAAgE;AAChE,MAAM,OAAO,iBAAiB;IAA9B;QACI;;WAEG;QACa,oBAAe,GAAG,KAAK,CAAC;IAqC5C,CAAC;IAnCG;;;;OAIG;IACI,OAAO,CAAC,SAAiB;QAC5B,OAAO,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,YAAY;QACf,4CAA4C;QAC5C,MAAM,6BAA6B,CAAC;IACxC,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CACX,IAAqB,EACrB,OAAwB,EACxB,QAA+G;QAE/G,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAE5E,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;QACnC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,eAAe,EAAE,KAAK,EAAE,GAAG,EAAE;YACvE,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAED,uBAAuB;AACvB,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,CAAC,CAAC", "sourcesContent": ["import { GetTGAHeader, UploadContent } from \"../../../Misc/tga\";\r\nimport { Engine } from \"../../../Engines/engine\";\r\nimport type { InternalTexture } from \"../../../Materials/Textures/internalTexture\";\r\nimport type { IInternalTextureLoader } from \"../../../Materials/Textures/internalTextureLoader\";\r\n\r\n/**\r\n * Implementation of the TGA Texture Loader.\r\n * @internal\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class _TGATextureLoader implements IInternalTextureLoader {\r\n    /**\r\n     * Defines whether the loader supports cascade loading the different faces.\r\n     */\r\n    public readonly supportCascades = false;\r\n\r\n    /**\r\n     * This returns if the loader support the current file information.\r\n     * @param extension defines the file extension of the file being loaded\r\n     * @returns true if the loader can load the specified file\r\n     */\r\n    public canLoad(extension: string): boolean {\r\n        return extension.endsWith(\".tga\");\r\n    }\r\n\r\n    /**\r\n     * Uploads the cube texture data to the WebGL texture. It has already been bound.\r\n     */\r\n    public loadCubeData(): void {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \".env not supported in Cube.\";\r\n    }\r\n\r\n    /**\r\n     * Uploads the 2D texture data to the WebGL texture. It has already been bound once in the callback.\r\n     * @param data contains the texture data\r\n     * @param texture defines the BabylonJS internal texture\r\n     * @param callback defines the method to call once ready to upload\r\n     */\r\n    public loadData(\r\n        data: ArrayBufferView,\r\n        texture: InternalTexture,\r\n        callback: (width: number, height: number, loadMipmap: boolean, isCompressed: boolean, done: () => void) => void\r\n    ): void {\r\n        const bytes = new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\r\n\r\n        const header = GetTGAHeader(bytes);\r\n        callback(header.width, header.height, texture.generateMipMaps, false, () => {\r\n            UploadContent(texture, bytes);\r\n        });\r\n    }\r\n}\r\n\r\n// Register the loader.\r\nEngine._TextureLoaders.push(new _TGATextureLoader());\r\n"]}