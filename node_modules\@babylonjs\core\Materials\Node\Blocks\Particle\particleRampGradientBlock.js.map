{"version": 3, "file": "particleRampGradientBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Particle/particleRampGradientBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D;;GAEG;AACH,MAAM,OAAO,yBAA0B,SAAQ,iBAAiB;IAC5D;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,MAAM,EAAE,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAEpH,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;IACtH,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,2BAA2B,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,KAA6B;QAC3C,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACxC,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACpC,KAAK,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;QACjD,KAAK,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACxC,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;IAC7C,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,EAAE;YAClD,OAAO;SACV;QAED,KAAK,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QACpC,KAAK,CAAC,sBAAsB,CAAC,aAAa,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;QAEpE,KAAK,CAAC,iBAAiB,IAAI;;mCAEA,IAAI,CAAC,KAAK,CAAC,sBAAsB;gCACpC,IAAI,CAAC,KAAK,CAAC,sBAAsB;;;;;;;;;;;kBAW/C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC;;kBAE1C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,sBAAsB;;SAE1F,CAAC;QAEF,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,mCAAmC,EAAE,yBAAyB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\n\r\n/**\r\n * Block used for the particle ramp gradient section\r\n */\r\nexport class ParticleRampGradientBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new ParticleRampGradientBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"color\", NodeMaterialBlockConnectionPointTypes.Color4, false, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\"rampColor\", NodeMaterialBlockConnectionPointTypes.Color4, NodeMaterialBlockTargets.Fragment);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"ParticleRampGradientBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the color input component\r\n     */\r\n    public get color(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the rampColor output component\r\n     */\r\n    public get rampColor(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"remapRanges\");\r\n        state._excludeVariableName(\"rampSampler\");\r\n        state._excludeVariableName(\"baseColor\");\r\n        state._excludeVariableName(\"alpha\");\r\n        state._excludeVariableName(\"remappedColorIndex\");\r\n        state._excludeVariableName(\"rampColor\");\r\n        state._excludeVariableName(\"finalAlpha\");\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Vertex) {\r\n            return;\r\n        }\r\n\r\n        state._emit2DSampler(\"rampSampler\");\r\n        state._emitVaryingFromString(\"remapRanges\", \"vec4\", \"RAMPGRADIENT\");\r\n\r\n        state.compilationString += `\r\n            #ifdef RAMPGRADIENT\r\n                vec4 baseColor = ${this.color.associatedVariableName};\r\n                float alpha = ${this.color.associatedVariableName}.a;\r\n\r\n                float remappedColorIndex = clamp((alpha - remapRanges.x) / remapRanges.y, 0.0, 1.0);\r\n\r\n                vec4 rampColor = texture2D(rampSampler, vec2(1.0 - remappedColorIndex, 0.));\r\n                baseColor.rgb *= rampColor.rgb;\r\n\r\n                // Remapped alpha\r\n                float finalAlpha = baseColor.a;\r\n                baseColor.a = clamp((alpha * rampColor.a - remapRanges.z) / remapRanges.w, 0.0, 1.0);\r\n\r\n                ${this._declareOutput(this.rampColor, state)} = baseColor;\r\n            #else\r\n                ${this._declareOutput(this.rampColor, state)} = ${this.color.associatedVariableName};\r\n            #endif\r\n        `;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ParticleRampGradientBlock\", ParticleRampGradientBlock);\r\n"]}