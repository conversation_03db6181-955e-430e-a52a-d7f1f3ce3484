{"version": 3, "file": "skeletonViewer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Debug/skeletonViewer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AACnE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAMrD,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AAEtC,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AACnE,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAC7D,OAAO,EAAE,cAAc,EAAE,MAAM,sCAAsC,CAAC;AACtE,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAK7C,OAAO,EAAE,YAAY,EAAE,MAAM,kCAAkC,CAAC;AAChE,OAAO,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AACrE,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,0BAAyB;AAE1C;;;GAGG;AACH,MAAM,OAAO,cAAc;IAQvB;;;;;OAKG;IACH,MAAM,CAAC,sBAAsB,CAAC,OAAiC,EAAE,KAAY;QACzE,MAAM,QAAQ,GAAa,OAAO,CAAC,QAAQ,CAAC;QAC5C,MAAM,SAAS,GAAW,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;QAC9D,MAAM,SAAS,GAAW,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QAC7D,MAAM,YAAY,GAAW,OAAO,CAAC,YAAY,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;QACpE,MAAM,SAAS,GAAW,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;QAC/D,MAAM,SAAS,GAAW,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;QAC5D,MAAM,eAAe,GAAW,OAAO,CAAC,eAAe,IAAI,CAAC,CAAC;QAE7D,MAAM,CAAC,YAAY,CAAC,cAAc,GAAG,QAAQ,CAAC,IAAI,GAAG,cAAc,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UA2DrE,CAAC;QACH,MAAM,CAAC,YAAY,CAAC,cAAc,GAAG,QAAQ,CAAC,IAAI,GAAG,gBAAgB,CAAC,GAAG;;;;;;;;;;SAUxE,CAAC;QACF,MAAM,MAAM,GAAmB,IAAI,cAAc,CAC7C,aAAa,GAAG,QAAQ,CAAC,IAAI,EAC7B,KAAK,EACL;YACI,MAAM,EAAE,cAAc,GAAG,QAAQ,CAAC,IAAI;YACtC,QAAQ,EAAE,cAAc,GAAG,QAAQ,CAAC,IAAI;SAC3C,EACD;YACI,UAAU,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,iBAAiB,EAAE,iBAAiB,CAAC;YACxE,QAAQ,EAAE;gBACN,OAAO;gBACP,WAAW;gBACX,qBAAqB;gBACrB,MAAM;gBACN,YAAY;gBACZ,gBAAgB;gBAChB,WAAW;gBACX,WAAW;gBACX,cAAc;gBACd,WAAW;gBACX,WAAW;gBACX,iBAAiB;aACpB;SACJ,CACJ,CAAC;QAEF,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACzC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACzC,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAC/C,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACzC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QACzC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;QAEpD,MAAM,CAAC,YAAY,GAAG,GAAW,EAAE;YAC/B,OAAO,kBAAkB,CAAC;QAC9B,CAAC,CAAC;QAEF,MAAM,CAAC,gBAAgB,GAAG,QAAQ,CAAC,eAAe,CAAC;QAEnD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,uBAAuB,CAAC,OAAkC,EAAE,KAAY;QAC3E,MAAM,QAAQ,GAAa,OAAO,CAAC,QAAQ,CAAC;QAC5C,MAAM,QAAQ,GAAqC,OAAO,CAAC,QAAQ,IAAI;YACnE;gBACI,KAAK,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;gBAChC,QAAQ,EAAE,CAAC;aACd;YACD;gBACI,KAAK,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;gBAClC,QAAQ,EAAE,GAAG;aAChB;YACD;gBACI,KAAK,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC;gBAChC,QAAQ,EAAE,GAAG;aAChB;YACD;gBACI,KAAK,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;gBAChC,QAAQ,EAAE,GAAG;aAChB;YACD;gBACI,KAAK,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;gBAChC,QAAQ,EAAE,GAAG;aAChB;YACD;gBACI,KAAK,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;gBAClC,QAAQ,EAAE,GAAG;aAChB;SACJ,CAAC;QAEF,MAAM,WAAW,GAAW,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QACtD,MAAM,cAAc,GAAa,cAAc,CAAC,yBAAyB,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACxG,MAAM,MAAM,GAAG,IAAI,cAAc,CAC7B,cAAc,GAAG,QAAQ,CAAC,IAAI,EAC9B,KAAK,EACL;YACI,YAAY,EACR;;;;;;;;oCAQgB;gBAChB,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;gBACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cA2CN;YACE,cAAc,EAAE;;;;;;;;aAQnB;SACA,EACD;YACI,UAAU,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,iBAAiB,EAAE,iBAAiB,CAAC;YACxE,QAAQ,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,qBAAqB,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,UAAU,CAAC;SAC9G,CACJ,CAAC;QAEF,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QAE7C,MAAM,CAAC,YAAY,GAAG,GAAW,EAAE;YAC/B,OAAO,mBAAmB,CAAC;QAC/B,CAAC,CAAC;QAEF,MAAM,CAAC,gBAAgB,GAAG,QAAQ,CAAC,eAAe,CAAC;QAEnD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACK,MAAM,CAAC,yBAAyB,CAAC,IAAY,EAAE,QAA0C,EAAE,KAAY;QAC3G,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACtF,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC;QAClC,MAAM,IAAI,GAAG,GAAG,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAErD,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACtB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC;QACrB,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAC5B,QAAQ,CAAC,MAAM,EAAE,CAAC;QAClB,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,IAAI,GAAsB,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QACrE,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;SAChC;QACD,QAAQ,CAAC,OAAO,EAAE,CAAC;QACnB,OAAO,MAAM,CAAC;IAClB,CAAC;IA+BD,sBAAsB;IACtB,IAAI,KAAK;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IACD,6BAA6B;IAC7B,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,2BAA2B;IAC3B,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IACD,yBAAyB;IACzB,IAAI,KAAK,CAAC,KAAc;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IACD,yBAAyB;IACzB,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD,yBAAyB;IACzB,IAAI,SAAS,CAAC,KAAmD;QAC7D,IAAI,CAAC,UAAU,GAAG,KAAY,CAAC;IACnC,CAAC;IACD,2BAA2B;IAC3B,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,cAAc,CAAC,aAAa,CAAC;IACpE,CAAC;IACD,2BAA2B;IAC3B,IAAI,WAAW,CAAC,KAAa;QACzB,IAAI,KAAK,GAAG,cAAc,CAAC,wBAAwB,EAAE;YACjD,KAAK,GAAG,cAAc,CAAC,aAAa,CAAC;SACxC;QACD,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;IACrC,CAAC;IACD;;;;;;;;OAQG;IACH;IACI,qCAAqC;IAC9B,QAAkB;IACzB,gDAAgD;IACzC,IAA4B;IACnC,qBAAqB;IACrB,KAAY;IACZ,kHAAkH;IAC3G,0BAAmC,IAAI;IAC9C,4DAA4D;IACrD,mBAA2B,CAAC;IACnC,oCAAoC;IAC7B,UAA2C,EAAE;QAV7C,aAAQ,GAAR,QAAQ,CAAU;QAElB,SAAI,GAAJ,IAAI,CAAwB;QAI5B,4BAAuB,GAAvB,uBAAuB,CAAgB;QAEvC,qBAAgB,GAAhB,gBAAgB,CAAY;QAE5B,YAAO,GAAP,OAAO,CAAsC;QAlFxD,yDAAyD;QAClD,UAAK,GAAW,MAAM,CAAC,KAAK,EAAE,CAAC;QAEtC,4DAA4D;QACpD,gBAAW,GAAG,IAAI,KAAK,EAAkB,CAAC;QAKlD,6BAA6B;QACrB,eAAU,GAAwB,IAAI,CAAC;QAE/C,oCAAoC;QAC5B,eAAU,GAAG,IAAI,CAAC;QAK1B,wCAAwC;QAChC,SAAI,GAA8B,IAAI,CAAC;QAiE3C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,UAAU;QACV,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC;QAC1D,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,KAAK,CAAC;QACrD,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,cAAc,CAAC,aAAa,CAAC;QAC1E,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,EAAE,CAAC;QACtD,OAAO,CAAC,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,IAAI,KAAK,CAAC;QACzE,OAAO,CAAC,cAAc,CAAC,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,aAAa,IAAI,KAAK,CAAC;QACrF,OAAO,CAAC,cAAc,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC,cAAc,IAAI,IAAI,CAAC;QACtF,OAAO,CAAC,cAAc,CAAC,eAAe,GAAG,OAAO,CAAC,cAAc,CAAC,eAAe,IAAI,CAAC,CAAC;QACrF,OAAO,CAAC,cAAc,CAAC,YAAY,GAAG,OAAO,CAAC,cAAc,CAAC,YAAY,IAAI,KAAK,CAAC;QACnF,OAAO,CAAC,cAAc,CAAC,gBAAgB,GAAG,OAAO,CAAC,cAAc,CAAC,gBAAgB,IAAI,KAAK,CAAC;QAC3F,OAAO,CAAC,cAAc,CAAC,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,aAAa,IAAI,KAAK,CAAC;QACrF,OAAO,CAAC,cAAc,CAAC,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,aAAa,IAAI,KAAK,CAAC;QACrF,OAAO,CAAC,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC;QAC5E,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC;QAElD,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;QAE9B,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YACtB,MAAM,sBAAsB,GAAG,IAAI,EAAE,eAAe,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;YACvF,MAAM,sBAAsB,GAAG,IAAI,EAAE,eAAe,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;YAEvF,IAAI,sBAAsB,IAAI,sBAAsB,EAAE;gBAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,sBAAsB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;oBACpD,MAAM,KAAK,GAAG,sBAAsB,CAAC,CAAC,CAAC,EACnC,MAAM,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;oBACvC,IAAI,MAAM,KAAK,CAAC,EAAE;wBACd,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;qBAChC;iBACJ;aACJ;SACJ;QAED,0BAA0B;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAClE,IAAI,CAAC,aAAa,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACjD,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAErE,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC;QAChD,IAAI,WAAW,GAAG,cAAc,CAAC,wBAAwB,EAAE;YACvD,WAAW,GAAG,cAAc,CAAC,aAAa,CAAC;SAC9C;QACD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,kBAAkB;QAClB,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,QAAQ,EAAE,CAAC;IACpB,CAAC;IAED,oDAAoD;IAC5C,QAAQ;QACZ,QAAQ,IAAI,CAAC,WAAW,EAAE;YACtB,KAAK,cAAc,CAAC,aAAa,CAAC,CAAC;gBAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;oBACrD,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC/B,CAAC,CAAC,CAAC;gBACH,MAAM;aACT;SACJ;IACL,CAAC;IAED,2FAA2F;IACpF,MAAM;QACT,QAAQ,IAAI,CAAC,WAAW,EAAE;YACtB,KAAK,cAAc,CAAC,aAAa,CAAC,CAAC;gBAC/B,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,MAAM;aACT;YACD,KAAK,cAAc,CAAC,eAAe,CAAC,CAAC;gBACjC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM;aACT;YACD,KAAK,cAAc,CAAC,wBAAwB,CAAC,CAAC;gBAC1C,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;gBAClC,MAAM;aACT;SACJ;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,iEAAiE;IACjE,IAAW,SAAS,CAAC,KAAc;QAC/B,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;YAC1B,OAAO;SACV;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SACpC;QAED,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACrB,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB;aAAM,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SACpB;IACL,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAEO,gBAAgB,CAAC,QAAiB,EAAE,IAAU,EAAE,OAAe,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC;QACxF,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACpC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QAErC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC/B,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC5B,KAAK,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACxC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACnC;QAED,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,CAAC;SAC5D;QAED,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAElC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACxB,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACxB,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAEO,2BAA2B,CAAC,KAAa,EAAE,IAA4B;QAC3E,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QAEzB,IAAI,MAAM,CAAC;QACX,IAAI,OAAO,CAAC;QACZ,IAAI,IAAI,EAAE;YACN,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/B,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;SAC3B;aAAM;YACH,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YACtB,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;SAC/B;QACD,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC1B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAEnC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBAC9F,SAAS;aACZ;YACD,IAAI,CAAC,MAAM,EAAE;gBACT,MAAM,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC1C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;aAClC;YACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;YAC/C,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAClE,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACnC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACnC,GAAG,EAAE,CAAC;SACT;IACL,CAAC;IAEO,yBAAyB,CAAC,KAAa;QAC3C,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QACzB,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,aAAa,CAAC;QAClB,IAAI,OAAO,CAAC;QACZ,IAAI,IAAI,EAAE;YACN,aAAa,GAAG,IAAI,CAAC;YACrB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;SAC3B;aAAM;YACH,aAAa,GAAG,IAAI,aAAa,CAAC,EAAE,CAAC,CAAC;YACtC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;SAC/B;QACD,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC/B,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,UAAU,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YACzC,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBAC5F,SAAS;aACZ;YACD,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACvC,IAAI,CAAC,MAAM,EAAE;gBACT,MAAM,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC1C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;aACtC;YACD,SAAS,CAAC,wBAAwB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,UAAU,CAAC,wBAAwB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACnC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACnC,OAAO,EAAE,CAAC;SACb;QACD,IAAI,CAAC,IAAI,EAAE;YACP,aAAa,CAAC,OAAO,EAAE,CAAC;SAC3B;IACL,CAAC;IAED;;;OAGG;IACK,OAAO,CAAC,cAAuB;QACnC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC9B,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,cAAc,CAAC;YAC9C,IAAI,CAAC,YAAa,CAAC,iBAAkB,CAAC,iBAAiB,GAAG,cAAc,CAAC;SAC5E;IACL,CAAC;IAED;;;;OAIG;IACK,yBAAyB,CAAC,IAAoB,EAAE,MAAc;QAClE,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;YACrC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnC,OAAO;SACV;QAED,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;QACzD,IAAI,CAAC,aAAa,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACnD,OAAO;IACX,CAAC;IAEO,WAAW,CAAC,WAAoB,EAAE,IAAU,EAAE,UAAmB,EAAE,SAAyB,EAAE,cAA6C,EAAE,iBAAwB;QACzK,MAAM,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC7C,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;QACvB,MAAM,EAAE,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAEpC,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,IAAI,KAAK,CAAC;QAChD,MAAM,aAAa,GAAG,cAAc,CAAC,aAAa,IAAI,KAAK,CAAC;QAE5D,MAAM,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAE9B,MAAM,IAAI,GAAG,kBAAkB,CAC3B,gBAAgB,EAChB;YACI,KAAK,EAAE,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1H,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;YAC/B,aAAa,EAAE,CAAC,CAAS,EAAE,EAAE;gBACzB,QAAQ,CAAC,EAAE;oBACP,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,CAAC,CAAC;oBACb,KAAK,CAAC;wBACF,OAAO,CAAC,GAAG,aAAa,CAAC;iBAChC;gBACD,OAAO,CAAC,CAAC;YACb,CAAC;YACD,eAAe,EAAE,IAAI,CAAC,WAAW;YACjC,SAAS,EAAE,KAAK;SACnB,EACD,iBAAiB,CACpB,CAAC;QAEF,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5C,MAAM,GAAG,GAAa,EAAE,EACpB,GAAG,GAAa,EAAE,CAAC;QAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;YAClC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAErB,mEAAmE;YACnE,uCAAuC;YACvC,IAAI,SAAS,IAAI,cAAc,CAAC,gBAAgB,IAAI,CAAC,GAAG,CAAC,EAAE;gBACvD,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAC3C;iBAAM;gBACH,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACtC;SACJ;QAED,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;QAEpC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,mBAAmB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACnE,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,mBAAmB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACnE,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,yBAAyB,CAAC,SAAiB;QAC/C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QACvE,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;QACvC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAChF,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAChF,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,EAAE;YACxD,OAAO,IAAI,CAAC;SACf;QAED,MAAM,GAAG,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC9E,MAAM,GAAG,GAAG,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEjF,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACrC,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;gBACxB,MAAM,MAAM,GAAG,WAAW,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChD,MAAM,OAAO,GAAG,WAAW,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBAEjD,IAAI,MAAM,KAAK,SAAS,IAAI,OAAO,GAAG,IAAI,EAAE;oBACxC,OAAO,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,GAAG,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1E,GAAG,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,GAAG,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,KAAK,EAAE,CAAC;oBACR,MAAM;iBACT;aACJ;SACJ;QAED,OAAO,KAAK,GAAG,CAAC;YACZ,CAAC,CAAC;gBACI,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC;gBAChC,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;aACzC;YACH,CAAC,CAAC,IAAI,CAAC;IACf,CAAC;IAED;;;OAGG;IACK,qBAAqB,CAAC,WAAW,GAAG,IAAI;QAC5C,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACtB;QAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,EAAE,iBAAkB,CAAC;QAChE,MAAM,KAAK,GAAW,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QAC1C,MAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,MAAM,KAAK,GAAW,EAAE,CAAC;QAEzB,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;QAEpD,IAAI;YACA,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;gBAC9B,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC;gBACrC,iBAAiB,CAAC,iBAAiB,GAAG,KAAK,CAAC;aAC/C;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;gBAC3B,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;aAChC;YAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC;aAC3C;YAED,IAAI,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,CAAC;YACjD,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,EAAE,CAAC;YAEzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEtB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;oBAC9F,SAAS;iBACZ;gBAED,MAAM,6BAA6B,GAAG,IAAI,MAAM,EAAE,CAAC;gBACnD,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,6BAA6B,CAAC,CAAC;gBAEpE,MAAM,WAAW,GAAG,IAAI,OAAO,EAAE,CAAC;gBAElC,6BAA6B,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;gBAE3E,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC1B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;wBACzB,MAAM,8BAA8B,GAAW,IAAI,MAAM,EAAE,CAAC;wBAC5D,EAAE,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,6BAA6B,EAAE,8BAA8B,CAAC,CAAC;wBACjG,MAAM,UAAU,GAAG,IAAI,OAAO,EAAE,CAAC;wBACjC,8BAA8B,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;wBAC3E,MAAM,kBAAkB,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;wBACrE,IAAI,kBAAkB,GAAG,iBAAiB,EAAE;4BACxC,iBAAiB,GAAG,kBAAkB,CAAC;yBAC1C;wBACD,IAAI,WAAW,EAAE;4BACb,OAAO;yBACV;wBAED,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC,CAAC;oBACvG,CAAC,CAAC,CAAC;iBACN;qBAAM;oBACH,MAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;oBACvE,IAAI,cAAc,EAAE;wBAChB,IAAI,cAAc,CAAC,MAAM,GAAG,iBAAiB,EAAE;4BAC3C,iBAAiB,GAAG,cAAc,CAAC,MAAM,CAAC;yBAC7C;wBACD,IAAI,CAAC,WAAW,EAAE;4BACd,IAAI,UAAU,CAAC;4BACf,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;4BACpC,IAAI,UAAU,EAAE;gCACZ,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,6BAA6B,CAAC,CAAC;gCAC1E,6BAA6B,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gCACrF,UAAU,GAAG,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;6BACtH;iCAAM;gCACH,UAAU,GAAG,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;6BACtH;4BACD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC,CAAC;yBACxG;qBACJ;iBACJ;gBAED,MAAM,cAAc,GAAG,cAAc,CAAC,cAAc,IAAI,GAAG,CAAC;gBAE5D,MAAM,MAAM,GAAG,YAAY,CACvB,gBAAgB,EAChB;oBACI,QAAQ,EAAE,CAAC;oBACX,QAAQ,EAAE,cAAc;oBACxB,SAAS,EAAE,IAAI;iBAClB,EACD,iBAAiB,CACpB,CAAC;gBAEF,MAAM,WAAW,GAAG,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAE9C,MAAM,GAAG,GAAa,EAAE,EACpB,GAAG,GAAa,EAAE,CAAC;gBAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;oBAClC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACrB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;iBACtC;gBAED,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,mBAAmB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBACrE,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,mBAAmB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBAErE,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;gBACtC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;aAChC;YAED,MAAM,eAAe,GAAG,cAAc,CAAC,eAAe,IAAI,CAAC,CAAC;YAC5D,MAAM,YAAY,GAAG,cAAc,CAAC,YAAY,IAAI,IAAI,CAAC;YAEzD,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAClC,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,eAAe,GAAG,iBAAiB,CAAC,CAAC;gBAExD,IAAI,SAAS,GAAG,CAAC,CAAC;gBAClB,IAAI,EAAE,GAAG,IAAI,CAAC;gBAEd,OAAO,EAAE,CAAC,SAAS,EAAE,IAAK,EAAE,CAAC,SAAS,EAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE;oBACjE,SAAS,EAAE,CAAC;oBACZ,EAAE,GAAG,EAAE,CAAC,SAAS,EAAU,CAAC;iBAC/B;gBACD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,CAAC;gBACvE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACvB;YAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACpE,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;gBACxD,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBACxC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;gBAClC,IAAI,CAAC,SAAS,CAAC,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC;gBACxF,IAAI,CAAC,SAAS,CAAC,wBAAwB,GAAG,IAAI,CAAC;aAClD;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,YAAa,CAAC,oBAAoB,EAAE,CAAC;YACxD,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC;YAEtB,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACrB;QAAC,OAAO,GAAG,EAAE;YACV,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClB,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC7B,IAAI,CAAC,OAAO,EAAE,CAAC;SAClB;IACL,CAAC;IAEO,eAAe;QACnB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;SAC7B;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,EAAE,CAAC;QAEzD,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;YAC/B,OAAO;SACV;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,aAAc,CAAC,iBAAiB,CAAC;QAC1D,MAAM,IAAI,GAAG,cAAc,CAAC,aAAa,IAAI,KAAK,CAAC;QACnD,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEpC,MAAM,GAAG,GAAa,EAAE,CAAC;QACzB,MAAM,GAAG,GAAa,EAAE,CAAC;QACzB,MAAM,YAAY,GAAG,CAAC,CAAC;QAEvB,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;YACjC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAEpC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBAC9F,SAAS;aACZ;YAED,MAAM,6BAA6B,GAAG,IAAI,MAAM,EAAE,CAAC;YACnD,MAAM,UAAU,GAAG,IAAI,OAAO,EAAE,CAAC;YAEjC,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,6BAA6B,CAAC,CAAC;YACpE,6BAA6B,CAAC,SAAS,CAAC,SAAS,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YAEzF,MAAM,CAAC,GAAG,IAAI,MAAM,EAAE,CAAC;YACvB,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YAE7C,MAAM,SAAS,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/E,MAAM,SAAS,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/E,MAAM,SAAS,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YAE/E,MAAM,KAAK,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;YACtD,MAAM,KAAK,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;YACtD,MAAM,KAAK,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;YAEtD,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YACzC,MAAM,UAAU,GAAG;gBACf,CAAC,GAAG,EAAE,GAAG,CAAC;gBACV,CAAC,KAAK,EAAE,KAAK,CAAC;gBACd,CAAC,IAAI,EAAE,IAAI,CAAC;aACf,CAAC;YAEF,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;YAE3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;gBACnC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACrB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACtC;SACJ;QAED,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,WAAW,CAAC,CAAC;QAChH,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,YAAY,CAAC,mBAAmB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9E,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,YAAY,CAAC,mBAAmB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9E,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QACnC,IAAI,CAAC,UAAU,CAAC,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC;IAC7F,CAAC;IAED,6FAA6F;IACrF,mBAAmB;QACvB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO;SACV;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC;SAC3C;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,EAAE;YAC7C,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACvD;aAAM;YACH,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;SACpE;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAEzD,IAAI,WAAW,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClB,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,WAAW,CAAC,CAAC;gBAClH,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;aAC5D;iBAAM;gBACH,gBAAgB,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,WAAW,CAAC,CAAC;aAC9G;YACD,IAAI,IAAI,CAAC,IAAI,EAAE;gBACX,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACzD;iBAAM;gBACH,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;aACtE;YACD,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;SACtC;IACL,CAAC;IACD;;OAEG;IACI,iBAAiB,CAAC,IAAY;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACjD,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE;YAC3B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACtB;YACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YAExB,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;SAC/B;IACL,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACI,oBAAoB,CAAC,MAAc,EAAE,KAAa;QACrD,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAChD,IAAI,CAAC,OAAO,CAAC,cAAsB,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;QACrD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACtB;QACD,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;IAChC,CAAC;IAED,mCAAmC;IAC5B,OAAO;QACV,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SAC1B;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;;AA7/BD,oEAAoE;AAC7C,4BAAa,GAAG,CAAC,AAAJ,CAAK;AACzC,sEAAsE;AAC/C,8BAAe,GAAG,CAAC,AAAJ,CAAK;AAC3C,+EAA+E;AACxD,uCAAwB,GAAG,CAAC,AAAJ,CAAK", "sourcesContent": ["import { Vector3, <PERSON>, TmpVectors } from \"../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../Maths/math.color\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Bone } from \"../Bones/bone\";\r\nimport type { Skeleton } from \"../Bones/skeleton\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport type { LinesMesh } from \"../Meshes/linesMesh\";\r\nimport { CreateLineSystem } from \"../Meshes/Builders/linesBuilder\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport { Material } from \"../Materials/material\";\r\nimport { ShaderMaterial } from \"../Materials/shaderMaterial\";\r\nimport { DynamicTexture } from \"../Materials/Textures/dynamicTexture\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport { Effect } from \"../Materials/effect\";\r\n\r\nimport type { ISkeletonViewerOptions, IBoneWeightShaderOptions, ISkeletonMapShaderOptions, ISkeletonMapShaderColorMapKnot, ISkeletonViewerDisplayOptions } from \"./ISkeletonViewer\";\r\nimport type { Observer } from \"../Misc/observable\";\r\n\r\nimport { CreateSphere } from \"../Meshes/Builders/sphereBuilder\";\r\nimport { ExtrudeShapeCustom } from \"../Meshes/Builders/shapeBuilder\";\r\nimport { TransformNode } from \"../Meshes/transformNode\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * Class used to render a debug view of a given skeleton\r\n * @see http://www.babylonjs-playground.com/#1BZJVJ#8\r\n */\r\nexport class SkeletonViewer {\r\n    /** public Display constants BABYLON.SkeletonViewer.DISPLAY_LINES */\r\n    public static readonly DISPLAY_LINES = 0;\r\n    /** public Display constants BABYLON.SkeletonViewer.DISPLAY_SPHERES */\r\n    public static readonly DISPLAY_SPHERES = 1;\r\n    /** public Display constants BABYLON.SkeletonViewer.DISPLAY_SPHERE_AND_SPURS */\r\n    public static readonly DISPLAY_SPHERE_AND_SPURS = 2;\r\n\r\n    /** public static method to create a BoneWeight Shader\r\n     * @param options The constructor options\r\n     * @param scene The scene that the shader is scoped to\r\n     * @returns The created ShaderMaterial\r\n     * @see http://www.babylonjs-playground.com/#1BZJVJ#395\r\n     */\r\n    static CreateBoneWeightShader(options: IBoneWeightShaderOptions, scene: Scene): ShaderMaterial {\r\n        const skeleton: Skeleton = options.skeleton;\r\n        const colorBase: Color3 = options.colorBase ?? Color3.Black();\r\n        const colorZero: Color3 = options.colorZero ?? Color3.Blue();\r\n        const colorQuarter: Color3 = options.colorQuarter ?? Color3.Green();\r\n        const colorHalf: Color3 = options.colorHalf ?? Color3.Yellow();\r\n        const colorFull: Color3 = options.colorFull ?? Color3.Red();\r\n        const targetBoneIndex: number = options.targetBoneIndex ?? 0;\r\n\r\n        Effect.ShadersStore[\"boneWeights:\" + skeleton.name + \"VertexShader\"] = `precision highp float;\r\n\r\n        attribute vec3 position;\r\n        attribute vec2 uv;\r\n\r\n        uniform mat4 view;\r\n        uniform mat4 projection;\r\n        uniform mat4 worldViewProjection;\r\n\r\n        #include<bonesDeclaration>\r\n        #if NUM_BONE_INFLUENCERS == 0\r\n            attribute vec4 matricesIndices;\r\n            attribute vec4 matricesWeights;\r\n        #endif\r\n        #include<bakedVertexAnimationDeclaration>\r\n\r\n        #include<instancesDeclaration>\r\n\r\n        varying vec3 vColor;\r\n\r\n        uniform vec3 colorBase;\r\n        uniform vec3 colorZero;\r\n        uniform vec3 colorQuarter;\r\n        uniform vec3 colorHalf;\r\n        uniform vec3 colorFull;\r\n\r\n        uniform float targetBoneIndex;\r\n\r\n        void main() {\r\n            vec3 positionUpdated = position;\r\n\r\n            #include<instancesVertex>\r\n            #include<bonesVertex>\r\n            #include<bakedVertexAnimation>\r\n\r\n            vec4 worldPos = finalWorld * vec4(positionUpdated, 1.0);\r\n\r\n            vec3 color = colorBase;\r\n            float totalWeight = 0.;\r\n            if(matricesIndices[0] == targetBoneIndex && matricesWeights[0] > 0.){\r\n                totalWeight += matricesWeights[0];\r\n            }\r\n            if(matricesIndices[1] == targetBoneIndex && matricesWeights[1] > 0.){\r\n                totalWeight += matricesWeights[1];\r\n            }\r\n            if(matricesIndices[2] == targetBoneIndex && matricesWeights[2] > 0.){\r\n                totalWeight += matricesWeights[2];\r\n            }\r\n            if(matricesIndices[3] == targetBoneIndex && matricesWeights[3] > 0.){\r\n                totalWeight += matricesWeights[3];\r\n            }\r\n\r\n            color = mix(color, colorZero, smoothstep(0., 0.25, totalWeight));\r\n            color = mix(color, colorQuarter, smoothstep(0.25, 0.5, totalWeight));\r\n            color = mix(color, colorHalf, smoothstep(0.5, 0.75, totalWeight));\r\n            color = mix(color, colorFull, smoothstep(0.75, 1.0, totalWeight));\r\n            vColor = color;\r\n\r\n        gl_Position = projection * view * worldPos;\r\n        }`;\r\n        Effect.ShadersStore[\"boneWeights:\" + skeleton.name + \"FragmentShader\"] = `\r\n            precision highp float;\r\n            varying vec3 vPosition;\r\n\r\n            varying vec3 vColor;\r\n\r\n            void main() {\r\n                vec4 color = vec4(vColor, 1.0);\r\n                gl_FragColor = color;\r\n            }\r\n        `;\r\n        const shader: ShaderMaterial = new ShaderMaterial(\r\n            \"boneWeight:\" + skeleton.name,\r\n            scene,\r\n            {\r\n                vertex: \"boneWeights:\" + skeleton.name,\r\n                fragment: \"boneWeights:\" + skeleton.name,\r\n            },\r\n            {\r\n                attributes: [\"position\", \"normal\", \"matricesIndices\", \"matricesWeights\"],\r\n                uniforms: [\r\n                    \"world\",\r\n                    \"worldView\",\r\n                    \"worldViewProjection\",\r\n                    \"view\",\r\n                    \"projection\",\r\n                    \"viewProjection\",\r\n                    \"colorBase\",\r\n                    \"colorZero\",\r\n                    \"colorQuarter\",\r\n                    \"colorHalf\",\r\n                    \"colorFull\",\r\n                    \"targetBoneIndex\",\r\n                ],\r\n            }\r\n        );\r\n\r\n        shader.setColor3(\"colorBase\", colorBase);\r\n        shader.setColor3(\"colorZero\", colorZero);\r\n        shader.setColor3(\"colorQuarter\", colorQuarter);\r\n        shader.setColor3(\"colorHalf\", colorHalf);\r\n        shader.setColor3(\"colorFull\", colorFull);\r\n        shader.setFloat(\"targetBoneIndex\", targetBoneIndex);\r\n\r\n        shader.getClassName = (): string => {\r\n            return \"BoneWeightShader\";\r\n        };\r\n\r\n        shader.transparencyMode = Material.MATERIAL_OPAQUE;\r\n\r\n        return shader;\r\n    }\r\n\r\n    /** public static method to create a BoneWeight Shader\r\n     * @param options The constructor options\r\n     * @param scene The scene that the shader is scoped to\r\n     * @returns The created ShaderMaterial\r\n     */\r\n    static CreateSkeletonMapShader(options: ISkeletonMapShaderOptions, scene: Scene) {\r\n        const skeleton: Skeleton = options.skeleton;\r\n        const colorMap: ISkeletonMapShaderColorMapKnot[] = options.colorMap ?? [\r\n            {\r\n                color: new Color3(1, 0.38, 0.18),\r\n                location: 0,\r\n            },\r\n            {\r\n                color: new Color3(0.59, 0.18, 1.0),\r\n                location: 0.2,\r\n            },\r\n            {\r\n                color: new Color3(0.59, 1, 0.18),\r\n                location: 0.4,\r\n            },\r\n            {\r\n                color: new Color3(1, 0.87, 0.17),\r\n                location: 0.6,\r\n            },\r\n            {\r\n                color: new Color3(1, 0.17, 0.42),\r\n                location: 0.8,\r\n            },\r\n            {\r\n                color: new Color3(0.17, 0.68, 1.0),\r\n                location: 1.0,\r\n            },\r\n        ];\r\n\r\n        const bufferWidth: number = skeleton.bones.length + 1;\r\n        const colorMapBuffer: number[] = SkeletonViewer._CreateBoneMapColorBuffer(bufferWidth, colorMap, scene);\r\n        const shader = new ShaderMaterial(\r\n            \"boneWeights:\" + skeleton.name,\r\n            scene,\r\n            {\r\n                vertexSource:\r\n                    `precision highp float;\r\n\r\n            attribute vec3 position;\r\n            attribute vec2 uv;\r\n\r\n            uniform mat4 view;\r\n            uniform mat4 projection;\r\n            uniform mat4 worldViewProjection;\r\n            uniform float colorMap[` +\r\n                    skeleton.bones.length * 4 +\r\n                    `];\r\n\r\n            #include<bonesDeclaration>\r\n            #if NUM_BONE_INFLUENCERS == 0\r\n                attribute vec4 matricesIndices;\r\n                attribute vec4 matricesWeights;\r\n            #endif\r\n            #include<bakedVertexAnimationDeclaration>\r\n            #include<instancesDeclaration>\r\n\r\n            varying vec3 vColor;\r\n\r\n            void main() {\r\n                vec3 positionUpdated = position;\r\n\r\n                #include<instancesVertex>\r\n                #include<bonesVertex>\r\n                #include<bakedVertexAnimation>\r\n\r\n                vec3 color = vec3(0.);\r\n                bool first = true;\r\n\r\n                for (int i = 0; i < 4; i++) {\r\n                    int boneIdx = int(matricesIndices[i]);\r\n                    float boneWgt = matricesWeights[i];\r\n\r\n                    vec3 c = vec3(colorMap[boneIdx * 4 + 0], colorMap[boneIdx * 4 + 1], colorMap[boneIdx * 4 + 2]);\r\n\r\n                    if (boneWgt > 0.) {\r\n                        if (first) {\r\n                            first = false;\r\n                            color = c;\r\n                        } else {\r\n                            color = mix(color, c, boneWgt);\r\n                        }\r\n                    }\r\n                }\r\n\r\n                vColor = color;\r\n\r\n                vec4 worldPos = finalWorld * vec4(positionUpdated, 1.0);\r\n\r\n                gl_Position = projection * view * worldPos;\r\n            }`,\r\n                fragmentSource: `\r\n            precision highp float;\r\n            varying vec3 vColor;\r\n\r\n            void main() {\r\n                vec4 color = vec4( vColor, 1.0 );\r\n                gl_FragColor = color;\r\n            }\r\n            `,\r\n            },\r\n            {\r\n                attributes: [\"position\", \"normal\", \"matricesIndices\", \"matricesWeights\"],\r\n                uniforms: [\"world\", \"worldView\", \"worldViewProjection\", \"view\", \"projection\", \"viewProjection\", \"colorMap\"],\r\n            }\r\n        );\r\n\r\n        shader.setFloats(\"colorMap\", colorMapBuffer);\r\n\r\n        shader.getClassName = (): string => {\r\n            return \"SkeletonMapShader\";\r\n        };\r\n\r\n        shader.transparencyMode = Material.MATERIAL_OPAQUE;\r\n\r\n        return shader;\r\n    }\r\n\r\n    /** private static method to create a BoneWeight Shader\r\n     * @param size The size of the buffer to create (usually the bone count)\r\n     * @param colorMap The gradient data to generate\r\n     * @param scene The scene that the shader is scoped to\r\n     * @returns an Array of floats from the color gradient values\r\n     */\r\n    private static _CreateBoneMapColorBuffer(size: number, colorMap: ISkeletonMapShaderColorMapKnot[], scene: Scene) {\r\n        const tempGrad = new DynamicTexture(\"temp\", { width: size, height: 1 }, scene, false);\r\n        const ctx = tempGrad.getContext();\r\n        const grad = ctx.createLinearGradient(0, 0, size, 0);\r\n\r\n        colorMap.forEach((stop) => {\r\n            grad.addColorStop(stop.location, stop.color.toHexString());\r\n        });\r\n\r\n        ctx.fillStyle = grad;\r\n        ctx.fillRect(0, 0, size, 1);\r\n        tempGrad.update();\r\n        const buffer: number[] = [];\r\n        const data: Uint8ClampedArray = ctx.getImageData(0, 0, size, 1).data;\r\n        const rUnit = 1 / 255;\r\n        for (let i = 0; i < data.length; i++) {\r\n            buffer.push(data[i] * rUnit);\r\n        }\r\n        tempGrad.dispose();\r\n        return buffer;\r\n    }\r\n\r\n    /** If SkeletonViewer scene scope. */\r\n    private _scene: Scene;\r\n\r\n    /** Gets or sets the color used to render the skeleton */\r\n    public color: Color3 = Color3.White();\r\n\r\n    /** Array of the points of the skeleton fo the line view. */\r\n    private _debugLines = new Array<Array<Vector3>>();\r\n\r\n    /** The SkeletonViewers Mesh. */\r\n    private _debugMesh: Nullable<LinesMesh>;\r\n\r\n    /** The local axes Meshes. */\r\n    private _localAxes: Nullable<LinesMesh> = null;\r\n\r\n    /** If SkeletonViewer is enabled. */\r\n    private _isEnabled = true;\r\n\r\n    /** If SkeletonViewer is ready. */\r\n    private _ready: boolean;\r\n\r\n    /** SkeletonViewer render observable. */\r\n    private _obs: Nullable<Observer<Scene>> = null;\r\n\r\n    /** The Utility Layer to render the gizmos in. */\r\n    private _utilityLayer: Nullable<UtilityLayerRenderer>;\r\n\r\n    private _boneIndices: Set<number>;\r\n\r\n    /** Gets the Scene. */\r\n    get scene(): Scene {\r\n        return this._scene;\r\n    }\r\n    /** Gets the utilityLayer. */\r\n    get utilityLayer(): Nullable<UtilityLayerRenderer> {\r\n        return this._utilityLayer;\r\n    }\r\n    /** Checks Ready Status. */\r\n    get isReady(): Boolean {\r\n        return this._ready;\r\n    }\r\n    /** Sets Ready Status. */\r\n    set ready(value: boolean) {\r\n        this._ready = value;\r\n    }\r\n    /** Gets the debugMesh */\r\n    get debugMesh(): Nullable<AbstractMesh> | Nullable<LinesMesh> {\r\n        return this._debugMesh;\r\n    }\r\n    /** Sets the debugMesh */\r\n    set debugMesh(value: Nullable<AbstractMesh> | Nullable<LinesMesh>) {\r\n        this._debugMesh = value as any;\r\n    }\r\n    /** Gets the displayMode */\r\n    get displayMode(): number {\r\n        return this.options.displayMode || SkeletonViewer.DISPLAY_LINES;\r\n    }\r\n    /** Sets the displayMode */\r\n    set displayMode(value: number) {\r\n        if (value > SkeletonViewer.DISPLAY_SPHERE_AND_SPURS) {\r\n            value = SkeletonViewer.DISPLAY_LINES;\r\n        }\r\n        this.options.displayMode = value;\r\n    }\r\n    /**\r\n     * Creates a new SkeletonViewer\r\n     * @param skeleton defines the skeleton to render\r\n     * @param mesh defines the mesh attached to the skeleton\r\n     * @param scene defines the hosting scene\r\n     * @param autoUpdateBonesMatrices defines a boolean indicating if bones matrices must be forced to update before rendering (true by default)\r\n     * @param renderingGroupId defines the rendering group id to use with the viewer\r\n     * @param options All of the extra constructor options for the SkeletonViewer\r\n     */\r\n    constructor(\r\n        /** defines the skeleton to render */\r\n        public skeleton: Skeleton,\r\n        /** defines the mesh attached to the skeleton */\r\n        public mesh: Nullable<AbstractMesh>,\r\n        /** The Scene scope*/\r\n        scene: Scene,\r\n        /** defines a boolean indicating if bones matrices must be forced to update before rendering (true by default)  */\r\n        public autoUpdateBonesMatrices: boolean = true,\r\n        /** defines the rendering group id to use with the viewer */\r\n        public renderingGroupId: number = 3,\r\n        /** is the options for the viewer */\r\n        public options: Partial<ISkeletonViewerOptions> = {}\r\n    ) {\r\n        this._scene = scene;\r\n        this._ready = false;\r\n\r\n        //Defaults\r\n        options.pauseAnimations = options.pauseAnimations ?? true;\r\n        options.returnToRest = options.returnToRest ?? false;\r\n        options.displayMode = options.displayMode ?? SkeletonViewer.DISPLAY_LINES;\r\n        options.displayOptions = options.displayOptions ?? {};\r\n        options.displayOptions.midStep = options.displayOptions.midStep ?? 0.235;\r\n        options.displayOptions.midStepFactor = options.displayOptions.midStepFactor ?? 0.155;\r\n        options.displayOptions.sphereBaseSize = options.displayOptions.sphereBaseSize ?? 0.15;\r\n        options.displayOptions.sphereScaleUnit = options.displayOptions.sphereScaleUnit ?? 2;\r\n        options.displayOptions.sphereFactor = options.displayOptions.sphereFactor ?? 0.865;\r\n        options.displayOptions.spurFollowsChild = options.displayOptions.spurFollowsChild ?? false;\r\n        options.displayOptions.showLocalAxes = options.displayOptions.showLocalAxes ?? false;\r\n        options.displayOptions.localAxesSize = options.displayOptions.localAxesSize ?? 0.075;\r\n        options.computeBonesUsingShaders = options.computeBonesUsingShaders ?? true;\r\n        options.useAllBones = options.useAllBones ?? true;\r\n\r\n        this._boneIndices = new Set();\r\n\r\n        if (!options.useAllBones) {\r\n            const initialMeshBoneIndices = mesh?.getVerticesData(VertexBuffer.MatricesIndicesKind);\r\n            const initialMeshBoneWeights = mesh?.getVerticesData(VertexBuffer.MatricesWeightsKind);\r\n\r\n            if (initialMeshBoneIndices && initialMeshBoneWeights) {\r\n                for (let i = 0; i < initialMeshBoneIndices.length; ++i) {\r\n                    const index = initialMeshBoneIndices[i],\r\n                        weight = initialMeshBoneWeights[i];\r\n                    if (weight !== 0) {\r\n                        this._boneIndices.add(index);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        /* Create Utility Layer */\r\n        this._utilityLayer = new UtilityLayerRenderer(this._scene, false);\r\n        this._utilityLayer.pickUtilitySceneFirst = false;\r\n        this._utilityLayer.utilityLayerScene.autoClearDepthAndStencil = true;\r\n\r\n        let displayMode = this.options.displayMode || 0;\r\n        if (displayMode > SkeletonViewer.DISPLAY_SPHERE_AND_SPURS) {\r\n            displayMode = SkeletonViewer.DISPLAY_LINES;\r\n        }\r\n        this.displayMode = displayMode;\r\n        //Prep the Systems\r\n        this.update();\r\n        this._bindObs();\r\n    }\r\n\r\n    /** The Dynamic bindings for the update functions */\r\n    private _bindObs(): void {\r\n        switch (this.displayMode) {\r\n            case SkeletonViewer.DISPLAY_LINES: {\r\n                this._obs = this.scene.onBeforeRenderObservable.add(() => {\r\n                    this._displayLinesUpdate();\r\n                });\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /** Update the viewer to sync with current skeleton state, only used to manually update. */\r\n    public update(): void {\r\n        switch (this.displayMode) {\r\n            case SkeletonViewer.DISPLAY_LINES: {\r\n                this._displayLinesUpdate();\r\n                break;\r\n            }\r\n            case SkeletonViewer.DISPLAY_SPHERES: {\r\n                this._buildSpheresAndSpurs(true);\r\n                break;\r\n            }\r\n            case SkeletonViewer.DISPLAY_SPHERE_AND_SPURS: {\r\n                this._buildSpheresAndSpurs(false);\r\n                break;\r\n            }\r\n        }\r\n\r\n        this._buildLocalAxes();\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating if the viewer is enabled */\r\n    public set isEnabled(value: boolean) {\r\n        if (this.isEnabled === value) {\r\n            return;\r\n        }\r\n\r\n        this._isEnabled = value;\r\n\r\n        if (this.debugMesh) {\r\n            this.debugMesh.setEnabled(value);\r\n        }\r\n\r\n        if (value && !this._obs) {\r\n            this._bindObs();\r\n        } else if (!value && this._obs) {\r\n            this.scene.onBeforeRenderObservable.remove(this._obs);\r\n            this._obs = null;\r\n        }\r\n    }\r\n\r\n    public get isEnabled(): boolean {\r\n        return this._isEnabled;\r\n    }\r\n\r\n    private _getBonePosition(position: Vector3, bone: Bone, meshMat: Matrix, x = 0, y = 0, z = 0): void {\r\n        const tmat = TmpVectors.Matrix[0];\r\n        const parentBone = bone.getParent();\r\n        tmat.copyFrom(bone.getLocalMatrix());\r\n\r\n        if (x !== 0 || y !== 0 || z !== 0) {\r\n            const tmat2 = TmpVectors.Matrix[1];\r\n            Matrix.IdentityToRef(tmat2);\r\n            tmat2.setTranslationFromFloats(x, y, z);\r\n            tmat2.multiplyToRef(tmat, tmat);\r\n        }\r\n\r\n        if (parentBone) {\r\n            tmat.multiplyToRef(parentBone.getAbsoluteMatrix(), tmat);\r\n        }\r\n\r\n        tmat.multiplyToRef(meshMat, tmat);\r\n\r\n        position.x = tmat.m[12];\r\n        position.y = tmat.m[13];\r\n        position.z = tmat.m[14];\r\n    }\r\n\r\n    private _getLinesForBonesWithLength(bones: Bone[], mesh: Nullable<AbstractMesh>): void {\r\n        const len = bones.length;\r\n\r\n        let matrix;\r\n        let meshPos;\r\n        if (mesh) {\r\n            matrix = mesh.getWorldMatrix();\r\n            meshPos = mesh.position;\r\n        } else {\r\n            matrix = new Matrix();\r\n            meshPos = bones[0].position;\r\n        }\r\n        let idx = 0;\r\n        for (let i = 0; i < len; i++) {\r\n            const bone = bones[i];\r\n            let points = this._debugLines[idx];\r\n\r\n            if (bone._index === -1 || (!this._boneIndices.has(bone.getIndex()) && !this.options.useAllBones)) {\r\n                continue;\r\n            }\r\n            if (!points) {\r\n                points = [Vector3.Zero(), Vector3.Zero()];\r\n                this._debugLines[idx] = points;\r\n            }\r\n            this._getBonePosition(points[0], bone, matrix);\r\n            this._getBonePosition(points[1], bone, matrix, 0, bone.length, 0);\r\n            points[0].subtractInPlace(meshPos);\r\n            points[1].subtractInPlace(meshPos);\r\n            idx++;\r\n        }\r\n    }\r\n\r\n    private _getLinesForBonesNoLength(bones: Bone[]): void {\r\n        const len = bones.length;\r\n        let boneNum = 0;\r\n\r\n        const mesh = this.mesh;\r\n        let transformNode;\r\n        let meshPos;\r\n        if (mesh) {\r\n            transformNode = mesh;\r\n            meshPos = mesh.position;\r\n        } else {\r\n            transformNode = new TransformNode(\"\");\r\n            meshPos = bones[0].position;\r\n        }\r\n        for (let i = len - 1; i >= 0; i--) {\r\n            const childBone = bones[i];\r\n            const parentBone = childBone.getParent();\r\n            if (!parentBone || (!this._boneIndices.has(childBone.getIndex()) && !this.options.useAllBones)) {\r\n                continue;\r\n            }\r\n            let points = this._debugLines[boneNum];\r\n            if (!points) {\r\n                points = [Vector3.Zero(), Vector3.Zero()];\r\n                this._debugLines[boneNum] = points;\r\n            }\r\n            childBone.getAbsolutePositionToRef(transformNode, points[0]);\r\n            parentBone.getAbsolutePositionToRef(transformNode, points[1]);\r\n            points[0].subtractInPlace(meshPos);\r\n            points[1].subtractInPlace(meshPos);\r\n            boneNum++;\r\n        }\r\n        if (!mesh) {\r\n            transformNode.dispose();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * function to revert the mesh and scene back to the initial state.\r\n     * @param animationState\r\n     */\r\n    private _revert(animationState: boolean): void {\r\n        if (this.options.pauseAnimations) {\r\n            this.scene.animationsEnabled = animationState;\r\n            this.utilityLayer!.utilityLayerScene!.animationsEnabled = animationState;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * function to get the absolute bind pose of a bone by accumulating transformations up the bone hierarchy.\r\n     * @param bone\r\n     * @param matrix\r\n     */\r\n    private _getAbsoluteBindPoseToRef(bone: Nullable<Bone>, matrix: Matrix) {\r\n        if (bone === null || bone._index === -1) {\r\n            matrix.copyFrom(Matrix.Identity());\r\n            return;\r\n        }\r\n\r\n        this._getAbsoluteBindPoseToRef(bone.getParent(), matrix);\r\n        bone.getBindMatrix().multiplyToRef(matrix, matrix);\r\n        return;\r\n    }\r\n\r\n    private _createSpur(anchorPoint: Vector3, bone: Bone, childPoint: Vector3, childBone: Nullable<Bone>, displayOptions: ISkeletonViewerDisplayOptions, utilityLayerScene: Scene) {\r\n        const dir = childPoint.subtract(anchorPoint);\r\n        const h = dir.length();\r\n        const up = dir.normalize().scale(h);\r\n\r\n        const midStep = displayOptions.midStep || 0.165;\r\n        const midStepFactor = displayOptions.midStepFactor || 0.215;\r\n\r\n        const up0 = up.scale(midStep);\r\n\r\n        const spur = ExtrudeShapeCustom(\r\n            \"skeletonViewer\",\r\n            {\r\n                shape: [new Vector3(1, -1, 0), new Vector3(1, 1, 0), new Vector3(-1, 1, 0), new Vector3(-1, -1, 0), new Vector3(1, -1, 0)],\r\n                path: [Vector3.Zero(), up0, up],\r\n                scaleFunction: (i: number) => {\r\n                    switch (i) {\r\n                        case 0:\r\n                        case 2:\r\n                            return 0;\r\n                        case 1:\r\n                            return h * midStepFactor;\r\n                    }\r\n                    return 0;\r\n                },\r\n                sideOrientation: Mesh.DEFAULTSIDE,\r\n                updatable: false,\r\n            },\r\n            utilityLayerScene\r\n        );\r\n\r\n        const numVertices = spur.getTotalVertices();\r\n        const mwk: number[] = [],\r\n            mik: number[] = [];\r\n\r\n        for (let i = 0; i < numVertices; i++) {\r\n            mwk.push(1, 0, 0, 0);\r\n\r\n            // Select verts at end of spur (ie vert 10 to 14) and bind to child\r\n            // bone if spurFollowsChild is enabled.\r\n            if (childBone && displayOptions.spurFollowsChild && i > 9) {\r\n                mik.push(childBone.getIndex(), 0, 0, 0);\r\n            } else {\r\n                mik.push(bone.getIndex(), 0, 0, 0);\r\n            }\r\n        }\r\n\r\n        spur.position = anchorPoint.clone();\r\n\r\n        spur.setVerticesData(VertexBuffer.MatricesWeightsKind, mwk, false);\r\n        spur.setVerticesData(VertexBuffer.MatricesIndicesKind, mik, false);\r\n        spur.convertToFlatShadedMesh();\r\n\r\n        return spur;\r\n    }\r\n\r\n    private _getBoundingSphereForBone(boneIndex: number) {\r\n        if (!this.mesh) {\r\n            return null;\r\n        }\r\n\r\n        const positions = this.mesh.getVerticesData(VertexBuffer.PositionKind);\r\n        const indices = this.mesh.getIndices();\r\n        const boneWeights = this.mesh.getVerticesData(VertexBuffer.MatricesWeightsKind);\r\n        const boneIndices = this.mesh.getVerticesData(VertexBuffer.MatricesIndicesKind);\r\n        if (!positions || !indices || !boneWeights || !boneIndices) {\r\n            return null;\r\n        }\r\n\r\n        const min = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        const max = new Vector3(-Number.MAX_VALUE, -Number.MAX_VALUE, -Number.MAX_VALUE);\r\n\r\n        let found = 0;\r\n        for (let i = 0; i < indices.length; ++i) {\r\n            const vertexIndex = indices[i];\r\n\r\n            for (let b = 0; b < 4; ++b) {\r\n                const bIndex = boneIndices[vertexIndex * 4 + b];\r\n                const bWeight = boneWeights[vertexIndex * 4 + b];\r\n\r\n                if (bIndex === boneIndex && bWeight > 1e-5) {\r\n                    Vector3.FromArrayToRef(positions, vertexIndex * 3, TmpVectors.Vector3[0]);\r\n                    min.minimizeInPlace(TmpVectors.Vector3[0]);\r\n                    max.maximizeInPlace(TmpVectors.Vector3[0]);\r\n                    found++;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n\r\n        return found > 1\r\n            ? {\r\n                  center: Vector3.Center(min, max),\r\n                  radius: Vector3.Distance(min, max) / 2,\r\n              }\r\n            : null;\r\n    }\r\n\r\n    /**\r\n     * function to build and bind sphere joint points and spur bone representations.\r\n     * @param spheresOnly\r\n     */\r\n    private _buildSpheresAndSpurs(spheresOnly = true): void {\r\n        if (this._debugMesh) {\r\n            this._debugMesh.dispose();\r\n            this._debugMesh = null;\r\n            this.ready = false;\r\n        }\r\n\r\n        this._ready = false;\r\n        const utilityLayerScene = this.utilityLayer?.utilityLayerScene!;\r\n        const bones: Bone[] = this.skeleton.bones;\r\n        const spheres: Array<[Mesh, Bone]> = [];\r\n        const spurs: Mesh[] = [];\r\n\r\n        const animationState = this.scene.animationsEnabled;\r\n\r\n        try {\r\n            if (this.options.pauseAnimations) {\r\n                this.scene.animationsEnabled = false;\r\n                utilityLayerScene.animationsEnabled = false;\r\n            }\r\n\r\n            if (this.options.returnToRest) {\r\n                this.skeleton.returnToRest();\r\n            }\r\n\r\n            if (this.autoUpdateBonesMatrices) {\r\n                this.skeleton.computeAbsoluteMatrices();\r\n            }\r\n\r\n            let longestBoneLength = Number.NEGATIVE_INFINITY;\r\n            const displayOptions = this.options.displayOptions || {};\r\n\r\n            for (let i = 0; i < bones.length; i++) {\r\n                const bone = bones[i];\r\n\r\n                if (bone._index === -1 || (!this._boneIndices.has(bone.getIndex()) && !this.options.useAllBones)) {\r\n                    continue;\r\n                }\r\n\r\n                const boneAbsoluteBindPoseTransform = new Matrix();\r\n                this._getAbsoluteBindPoseToRef(bone, boneAbsoluteBindPoseTransform);\r\n\r\n                const anchorPoint = new Vector3();\r\n\r\n                boneAbsoluteBindPoseTransform.decompose(undefined, undefined, anchorPoint);\r\n\r\n                if (bone.children.length > 0) {\r\n                    bone.children.forEach((bc) => {\r\n                        const childAbsoluteBindPoseTransform: Matrix = new Matrix();\r\n                        bc.getLocalMatrix().multiplyToRef(boneAbsoluteBindPoseTransform, childAbsoluteBindPoseTransform);\r\n                        const childPoint = new Vector3();\r\n                        childAbsoluteBindPoseTransform.decompose(undefined, undefined, childPoint);\r\n                        const distanceFromParent = Vector3.Distance(anchorPoint, childPoint);\r\n                        if (distanceFromParent > longestBoneLength) {\r\n                            longestBoneLength = distanceFromParent;\r\n                        }\r\n                        if (spheresOnly) {\r\n                            return;\r\n                        }\r\n\r\n                        spurs.push(this._createSpur(anchorPoint, bone, childPoint, bc, displayOptions, utilityLayerScene));\r\n                    });\r\n                } else {\r\n                    const boundingSphere = this._getBoundingSphereForBone(bone.getIndex());\r\n                    if (boundingSphere) {\r\n                        if (boundingSphere.radius > longestBoneLength) {\r\n                            longestBoneLength = boundingSphere.radius;\r\n                        }\r\n                        if (!spheresOnly) {\r\n                            let childPoint;\r\n                            const parentBone = bone.getParent();\r\n                            if (parentBone) {\r\n                                this._getAbsoluteBindPoseToRef(parentBone, boneAbsoluteBindPoseTransform);\r\n                                boneAbsoluteBindPoseTransform.decompose(undefined, undefined, TmpVectors.Vector3[0]);\r\n                                childPoint = anchorPoint.subtract(TmpVectors.Vector3[0]).normalize().scale(boundingSphere.radius).add(anchorPoint);\r\n                            } else {\r\n                                childPoint = boundingSphere.center.subtract(anchorPoint).normalize().scale(boundingSphere.radius).add(anchorPoint);\r\n                            }\r\n                            spurs.push(this._createSpur(anchorPoint, bone, childPoint, null, displayOptions, utilityLayerScene));\r\n                        }\r\n                    }\r\n                }\r\n\r\n                const sphereBaseSize = displayOptions.sphereBaseSize || 0.2;\r\n\r\n                const sphere = CreateSphere(\r\n                    \"skeletonViewer\",\r\n                    {\r\n                        segments: 6,\r\n                        diameter: sphereBaseSize,\r\n                        updatable: true,\r\n                    },\r\n                    utilityLayerScene\r\n                );\r\n\r\n                const numVertices = sphere.getTotalVertices();\r\n\r\n                const mwk: number[] = [],\r\n                    mik: number[] = [];\r\n\r\n                for (let i = 0; i < numVertices; i++) {\r\n                    mwk.push(1, 0, 0, 0);\r\n                    mik.push(bone.getIndex(), 0, 0, 0);\r\n                }\r\n\r\n                sphere.setVerticesData(VertexBuffer.MatricesWeightsKind, mwk, false);\r\n                sphere.setVerticesData(VertexBuffer.MatricesIndicesKind, mik, false);\r\n\r\n                sphere.position = anchorPoint.clone();\r\n                spheres.push([sphere, bone]);\r\n            }\r\n\r\n            const sphereScaleUnit = displayOptions.sphereScaleUnit || 2;\r\n            const sphereFactor = displayOptions.sphereFactor || 0.85;\r\n\r\n            const meshes = [];\r\n            for (let i = 0; i < spheres.length; i++) {\r\n                const [sphere, bone] = spheres[i];\r\n                const scale = 1 / (sphereScaleUnit / longestBoneLength);\r\n\r\n                let _stepsOut = 0;\r\n                let _b = bone;\r\n\r\n                while (_b.getParent() && (_b.getParent() as Bone).getIndex() !== -1) {\r\n                    _stepsOut++;\r\n                    _b = _b.getParent() as Bone;\r\n                }\r\n                sphere.scaling.scaleInPlace(scale * Math.pow(sphereFactor, _stepsOut));\r\n                meshes.push(sphere);\r\n            }\r\n\r\n            this.debugMesh = Mesh.MergeMeshes(meshes.concat(spurs), true, true);\r\n            if (this.debugMesh) {\r\n                this.debugMesh.renderingGroupId = this.renderingGroupId;\r\n                this.debugMesh.skeleton = this.skeleton;\r\n                this.debugMesh.parent = this.mesh;\r\n                this.debugMesh.computeBonesUsingShaders = this.options.computeBonesUsingShaders ?? true;\r\n                this.debugMesh.alwaysSelectAsActiveMesh = true;\r\n            }\r\n\r\n            const light = this.utilityLayer!._getSharedGizmoLight();\r\n            light.intensity = 0.7;\r\n\r\n            this._revert(animationState);\r\n            this.ready = true;\r\n        } catch (err) {\r\n            Logger.Error(err);\r\n            this._revert(animationState);\r\n            this.dispose();\r\n        }\r\n    }\r\n\r\n    private _buildLocalAxes(): void {\r\n        if (this._localAxes) {\r\n            this._localAxes.dispose();\r\n        }\r\n\r\n        this._localAxes = null;\r\n\r\n        const displayOptions = this.options.displayOptions || {};\r\n\r\n        if (!displayOptions.showLocalAxes) {\r\n            return;\r\n        }\r\n\r\n        const targetScene = this._utilityLayer!.utilityLayerScene;\r\n        const size = displayOptions.localAxesSize || 0.075;\r\n        const lines = [];\r\n        const colors = [];\r\n        const red = new Color4(1, 0, 0, 1);\r\n        const green = new Color4(0, 1, 0, 1);\r\n        const blue = new Color4(0, 0, 1, 1);\r\n\r\n        const mwk: number[] = [];\r\n        const mik: number[] = [];\r\n        const vertsPerBone = 6;\r\n\r\n        for (const i in this.skeleton.bones) {\r\n            const bone = this.skeleton.bones[i];\r\n\r\n            if (bone._index === -1 || (!this._boneIndices.has(bone.getIndex()) && !this.options.useAllBones)) {\r\n                continue;\r\n            }\r\n\r\n            const boneAbsoluteBindPoseTransform = new Matrix();\r\n            const boneOrigin = new Vector3();\r\n\r\n            this._getAbsoluteBindPoseToRef(bone, boneAbsoluteBindPoseTransform);\r\n            boneAbsoluteBindPoseTransform.decompose(undefined, TmpVectors.Quaternion[0], boneOrigin);\r\n\r\n            const m = new Matrix();\r\n            TmpVectors.Quaternion[0].toRotationMatrix(m);\r\n\r\n            const boneAxisX = Vector3.TransformCoordinates(new Vector3(0 + size, 0, 0), m);\r\n            const boneAxisY = Vector3.TransformCoordinates(new Vector3(0, 0 + size, 0), m);\r\n            const boneAxisZ = Vector3.TransformCoordinates(new Vector3(0, 0, 0 + size), m);\r\n\r\n            const axisX = [boneOrigin, boneOrigin.add(boneAxisX)];\r\n            const axisY = [boneOrigin, boneOrigin.add(boneAxisY)];\r\n            const axisZ = [boneOrigin, boneOrigin.add(boneAxisZ)];\r\n\r\n            const linePoints = [axisX, axisY, axisZ];\r\n            const lineColors = [\r\n                [red, red],\r\n                [green, green],\r\n                [blue, blue],\r\n            ];\r\n\r\n            lines.push(...linePoints);\r\n            colors.push(...lineColors);\r\n\r\n            for (let j = 0; j < vertsPerBone; j++) {\r\n                mwk.push(1, 0, 0, 0);\r\n                mik.push(bone.getIndex(), 0, 0, 0);\r\n            }\r\n        }\r\n\r\n        this._localAxes = CreateLineSystem(\"localAxes\", { lines: lines, colors: colors, updatable: true }, targetScene);\r\n        this._localAxes.setVerticesData(VertexBuffer.MatricesWeightsKind, mwk, false);\r\n        this._localAxes.setVerticesData(VertexBuffer.MatricesIndicesKind, mik, false);\r\n        this._localAxes.skeleton = this.skeleton;\r\n        this._localAxes.renderingGroupId = this.renderingGroupId + 1;\r\n        this._localAxes.parent = this.mesh;\r\n        this._localAxes.computeBonesUsingShaders = this.options.computeBonesUsingShaders ?? true;\r\n    }\r\n\r\n    /** Update the viewer to sync with current skeleton state, only used for the line display. */\r\n    private _displayLinesUpdate(): void {\r\n        if (!this._utilityLayer) {\r\n            return;\r\n        }\r\n\r\n        if (this.autoUpdateBonesMatrices) {\r\n            this.skeleton.computeAbsoluteMatrices();\r\n        }\r\n\r\n        if (this.skeleton.bones[0].length === undefined) {\r\n            this._getLinesForBonesNoLength(this.skeleton.bones);\r\n        } else {\r\n            this._getLinesForBonesWithLength(this.skeleton.bones, this.mesh);\r\n        }\r\n\r\n        const targetScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        if (targetScene) {\r\n            if (!this._debugMesh) {\r\n                this._debugMesh = CreateLineSystem(\"\", { lines: this._debugLines, updatable: true, instance: null }, targetScene);\r\n                this._debugMesh.renderingGroupId = this.renderingGroupId;\r\n            } else {\r\n                CreateLineSystem(\"\", { lines: this._debugLines, updatable: true, instance: this._debugMesh }, targetScene);\r\n            }\r\n            if (this.mesh) {\r\n                this._debugMesh.position.copyFrom(this.mesh.position);\r\n            } else {\r\n                this._debugMesh.position.copyFrom(this.skeleton.bones[0].position);\r\n            }\r\n            this._debugMesh.color = this.color;\r\n        }\r\n    }\r\n    /** Changes the displayMode of the skeleton viewer\r\n     * @param mode The displayMode numerical value\r\n     */\r\n    public changeDisplayMode(mode: number): void {\r\n        const wasEnabled = this.isEnabled ? true : false;\r\n        if (this.displayMode !== mode) {\r\n            this.isEnabled = false;\r\n            if (this._debugMesh) {\r\n                this._debugMesh.dispose();\r\n                this._debugMesh = null;\r\n                this.ready = false;\r\n            }\r\n            this.displayMode = mode;\r\n\r\n            this.update();\r\n            this._bindObs();\r\n            this.isEnabled = wasEnabled;\r\n        }\r\n    }\r\n\r\n    /** Sets a display option of the skeleton viewer\r\n     *\r\n     * | Option           | Type    | Default | Description |\r\n     * | ---------------- | ------- | ------- | ----------- |\r\n     * | midStep          | float   | 0.235   | A percentage between a bone and its child that determines the widest part of a spur. Only used when `displayMode` is set to `DISPLAY_SPHERE_AND_SPURS`. |\r\n     * | midStepFactor    | float   | 0.15    | Mid step width expressed as a factor of the length. A value of 0.5 makes the spur width half of the spur length. Only used when `displayMode` is set to `DISPLAY_SPHERE_AND_SPURS`. |\r\n     * | sphereBaseSize   | float   | 2       | Sphere base size. Only used when `displayMode` is set to `DISPLAY_SPHERE_AND_SPURS`. |\r\n     * | sphereScaleUnit  | float   | 0.865   | Sphere scale factor used to scale spheres in relation to the longest bone. Only used when `displayMode` is set to `DISPLAY_SPHERE_AND_SPURS`. |\r\n     * | spurFollowsChild | boolean | false   | Whether a spur should attach its far end to the child bone. |\r\n     * | showLocalAxes    | boolean | false   | Displays local axes on all bones. |\r\n     * | localAxesSize    | float   | 0.075   | Determines the length of each local axis. |\r\n     *\r\n     * @param option String of the option name\r\n     * @param value The numerical option value\r\n     */\r\n    public changeDisplayOptions(option: string, value: number): void {\r\n        const wasEnabled = this.isEnabled ? true : false;\r\n        (this.options.displayOptions as any)[option] = value;\r\n        this.isEnabled = false;\r\n        if (this._debugMesh) {\r\n            this._debugMesh.dispose();\r\n            this._debugMesh = null;\r\n            this.ready = false;\r\n        }\r\n        this.update();\r\n        this._bindObs();\r\n        this.isEnabled = wasEnabled;\r\n    }\r\n\r\n    /** Release associated resources */\r\n    public dispose(): void {\r\n        this.isEnabled = false;\r\n        if (this._debugMesh) {\r\n            this._debugMesh.dispose();\r\n            this._debugMesh = null;\r\n        }\r\n\r\n        if (this._utilityLayer) {\r\n            this._utilityLayer.dispose();\r\n            this._utilityLayer = null;\r\n        }\r\n\r\n        this.ready = false;\r\n    }\r\n}\r\n"]}