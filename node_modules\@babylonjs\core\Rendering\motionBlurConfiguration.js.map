{"version": 3, "file": "motionBlurConfiguration.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Rendering/motionBlurConfiguration.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAGjD;;;GAGG;AACH,MAAM,OAAO,uBAAuB;IAApC;QACI;;WAEG;QACI,YAAO,GAAG,KAAK,CAAC;QAEvB;;WAEG;QACI,SAAI,GAAG,YAAY,CAAC;QAE3B;;WAEG;QACa,qBAAgB,GAAa,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC;IAC3F,CAAC;CAAA", "sourcesContent": ["import { Constants } from \"../Engines/constants\";\r\nimport type { PrePassEffectConfiguration } from \"./prePassEffectConfiguration\";\r\n\r\n/**\r\n * Contains all parameters needed for the prepass to perform\r\n * motion blur\r\n */\r\nexport class MotionBlurConfiguration implements PrePassEffectConfiguration {\r\n    /**\r\n     * Is motion blur enabled\r\n     */\r\n    public enabled = false;\r\n\r\n    /**\r\n     * Name of the configuration\r\n     */\r\n    public name = \"motionBlur\";\r\n\r\n    /**\r\n     * Textures that should be present in the MRT for this effect to work\r\n     */\r\n    public readonly texturesRequired: number[] = [Constants.PREPASS_VELOCITY_TEXTURE_TYPE];\r\n}\r\n"]}