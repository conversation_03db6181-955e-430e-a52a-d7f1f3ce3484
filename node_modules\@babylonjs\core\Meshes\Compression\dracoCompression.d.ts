import { AutoReleaseWorkerPool } from "../../Misc/workerPool";
import type { IDisposable, Scene } from "../../scene";
import { Geometry } from "../geometry";
import { VertexData } from "../mesh.vertexData";
/**
 * Configuration for Draco compression
 */
export interface IDracoCompressionConfiguration {
    /**
     * Configuration for the decoder.
     */
    decoder: {
        /**
         * The url to the WebAssembly module.
         */
        wasmUrl?: string;
        /**
         * The url to the WebAssembly binary.
         */
        wasmBinaryUrl?: string;
        /**
         * The url to the fallback JavaScript module.
         */
        fallbackUrl?: string;
        /**
         * Optional worker pool to use for async decoding instead of creating a new worker pool.
         */
        workerPool?: AutoReleaseWorkerPool;
        /**
         * Optional ArrayBuffer of the WebAssembly binary
         */
        wasmBinary?: ArrayBuffer;
        /**
         * The decoder module if already available.
         */
        jsModule?: any;
    };
}
/**
 * Options for Draco compression
 */
export interface IDracoCompressionOptions {
    /**
     * The number of workers for async operations. Specify `0` to disable web workers and run synchronously in the current context.
     */
    numWorkers?: number;
    /**
     * Optional ArrayBuffer of the WebAssembly binary.
     * If provided it will be used instead of loading the binary from wasmBinaryUrl.
     */
    wasmBinary?: ArrayBuffer;
    /**
     * Optional worker pool to use for async decoding.
     * If provided, numWorkers will be ignored and the worker pool will be used instead.
     * If provided the draco script will not be loaded from the DracoConfiguration.
     */
    workerPool?: AutoReleaseWorkerPool;
}
/**
 * Draco compression (https://google.github.io/draco/)
 *
 * This class wraps the Draco module.
 *
 * **Encoder**
 *
 * The encoder is not currently implemented.
 *
 * **Decoder**
 *
 * By default, the configuration points to a copy of the Draco decoder files for glTF from the babylon.js preview cdn https://preview.babylonjs.com/draco_wasm_wrapper_gltf.js.
 *
 * To update the configuration, use the following code:
 * ```javascript
 *     DracoCompression.Configuration = {
 *         decoder: {
 *             wasmUrl: "<url to the WebAssembly library>",
 *             wasmBinaryUrl: "<url to the WebAssembly binary>",
 *             fallbackUrl: "<url to the fallback JavaScript library>",
 *         }
 *     };
 * ```
 *
 * Draco has two versions, one for WebAssembly and one for JavaScript. The decoder configuration can be set to only support WebAssembly or only support the JavaScript version.
 * Decoding will automatically fallback to the JavaScript version if WebAssembly version is not configured or if WebAssembly is not supported by the browser.
 * Use `DracoCompression.DecoderAvailable` to determine if the decoder configuration is available for the current context.
 *
 * To decode Draco compressed data, get the default DracoCompression object and call decodeMeshToGeometryAsync:
 * ```javascript
 *     var geometry = await DracoCompression.Default.decodeMeshToGeometryAsync(data);
 * ```
 *
 * @see https://playground.babylonjs.com/#DMZIBD#0
 */
export declare class DracoCompression implements IDisposable {
    private _workerPoolPromise?;
    private _decoderModulePromise?;
    /**
     * The configuration. Defaults to the following urls:
     * - wasmUrl: "https://cdn.babylonjs.com/draco_wasm_wrapper_gltf.js"
     * - wasmBinaryUrl: "https://cdn.babylonjs.com/draco_decoder_gltf.wasm"
     * - fallbackUrl: "https://cdn.babylonjs.com/draco_decoder_gltf.js"
     */
    static Configuration: IDracoCompressionConfiguration;
    /**
     * Returns true if the decoder configuration is available.
     */
    static get DecoderAvailable(): boolean;
    /**
     * Default number of workers to create when creating the draco compression object.
     */
    static DefaultNumWorkers: number;
    private static GetDefaultNumWorkers;
    private static _Default;
    /**
     * Default instance for the draco compression object.
     */
    static get Default(): DracoCompression;
    /**
     * Constructor
     * @param numWorkers The number of workers for async operations Or an options object. Specify `0` to disable web workers and run synchronously in the current context.
     */
    constructor(numWorkers?: number | IDracoCompressionOptions);
    /**
     * Stop all async operations and release resources.
     */
    dispose(): void;
    /**
     * Returns a promise that resolves when ready. Call this manually to ensure draco compression is ready before use.
     * @returns a promise that resolves when ready
     */
    whenReadyAsync(): Promise<void>;
    private _decodeMeshAsync;
    /**
     * Decode Draco compressed mesh data to Babylon geometry.
     * @param name The name to use when creating the geometry
     * @param scene The scene to use when creating the geometry
     * @param data The ArrayBuffer or ArrayBufferView for the Draco compression data
     * @param attributes A map of attributes from vertex buffer kinds to Draco unique ids
     * @returns A promise that resolves with the decoded geometry
     */
    decodeMeshToGeometryAsync(name: string, scene: Scene, data: ArrayBuffer | ArrayBufferView, attributes?: {
        [kind: string]: number;
    }): Promise<Geometry>;
    /** @internal */
    _decodeMeshToGeometryForGltfAsync(name: string, scene: Scene, data: ArrayBuffer | ArrayBufferView, attributes: {
        [kind: string]: number;
    }, gltfNormalizedOverride: {
        [kind: string]: boolean;
    }): Promise<Geometry>;
    /**
     * Decode Draco compressed mesh data to Babylon vertex data.
     * @param data The ArrayBuffer or ArrayBufferView for the Draco compression data
     * @param attributes A map of attributes from vertex buffer kinds to Draco unique ids
     * @returns A promise that resolves with the decoded vertex data
     * @deprecated Use {@link decodeMeshToGeometryAsync} for better performance in some cases
     */
    decodeMeshAsync(data: ArrayBuffer | ArrayBufferView, attributes?: {
        [kind: string]: number;
    }): Promise<VertexData>;
}
