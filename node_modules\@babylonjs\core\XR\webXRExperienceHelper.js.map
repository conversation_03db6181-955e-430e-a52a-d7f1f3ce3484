{"version": 3, "file": "webXRExperienceHelper.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/XR/webXRExperienceHelper.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAChF,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;AAC7D,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAgB3D;;;GAGG;AACH,MAAM,OAAO,qBAAqB;IAkC9B;;;OAGG;IACH,YAA4B,MAAa;QAAb,WAAM,GAAN,MAAM,CAAO;QArCjC,iBAAY,GAAqB,IAAI,CAAC;QACtC,uBAAkB,GAAY,KAAK,CAAC;QACpC,qBAAgB,GAA8B,IAAI,CAAC;QACnD,4BAAuB,GAAG,IAAI,CAAC;QAC/B,eAAU,GAAG,KAAK,CAAC;QACnB,mBAAc,GAAG,KAAK,CAAC;QACvB,mBAAc,GAAG,CAAC,CAAC;QAQ3B;;;;;;WAMG;QACI,iCAA4B,GAAG,IAAI,UAAU,EAAe,CAAC;QACpE;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAc,CAAC;QAG/D;;WAEG;QACI,UAAK,GAAe,UAAU,CAAC,SAAS,CAAC;QAO5C,IAAI,CAAC,cAAc,GAAG,IAAI,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACtD,IAAI,CAAC,MAAM,GAAG,IAAI,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACpE,IAAI,CAAC,eAAe,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAErE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;YACpC,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,WAAW,CAAC,KAAY;QAClC,MAAM,MAAM,GAAG,IAAI,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAChD,OAAO,MAAM,CAAC,cAAc;aACvB,eAAe,EAAE;aACjB,IAAI,CAAC,GAAG,EAAE;YACP,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;YACzB,OAAO,MAAM,CAAC;QAClB,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACtB,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;SAChD;IACL,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,YAAY,CACrB,WAA0B,EAC1B,kBAAwC,EACxC,eAAkC,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE,EAC5E,yBAAwC,EAAE;QAE1C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,4CAA4C;YAC5C,MAAM,oDAAoD,CAAC;SAC9D;QACD,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QACvC,IAAI,kBAAkB,KAAK,QAAQ,IAAI,kBAAkB,KAAK,OAAO,EAAE;YACnE,sBAAsB,CAAC,gBAAgB,GAAG,sBAAsB,CAAC,gBAAgB,IAAI,EAAE,CAAC;YACxF,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACpE;QACD,sBAAsB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,0BAA0B,CAAC,sBAAsB,CAAC,CAAC;QACvG,yDAAyD;QACzD,IAAI,WAAW,KAAK,cAAc,IAAI,kBAAkB,KAAK,WAAW,EAAE;YACtE,MAAM,CAAC,IAAI,CAAC,4FAA4F,CAAC,CAAC;SAC7G;QACD,+CAA+C;QAC/C,IAAI;YACA,MAAM,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,WAAW,EAAE,sBAAsB,CAAC,CAAC;YACtF,MAAM,IAAI,CAAC,cAAc,CAAC,0BAA0B,CAAC,kBAAkB,CAAC,CAAC;YAEzE,MAAM,aAAa,GAAsB;gBACrC,2GAA2G;gBAC3G,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,KAAK;gBACnC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;aAC9B,CAAC;YAEF,4FAA4F;YAC5F,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE;gBAClE,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACzF,aAAa,CAAC,SAAS,GAAG,SAAS,CAAC;aACvC;YAED,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YACrD,sBAAsB;YACtB,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;YACtC,8BAA8B;YAC9B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YACrD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7C,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,iBAAiB,CAAC;YACzE,IAAI,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;YAEnC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC;YACvC,4CAA4C;YAC5C,IAAI,WAAW,KAAK,cAAc,EAAE;gBAChC,IAAI,CAAC,gBAAgB,EAAE,CAAC;aAC3B;iBAAM;gBACH,oCAAoC;gBACpC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;gBAC9B,IAAI,CAAC,MAAM,CAAC,sBAAsB,GAAG,KAAK,CAAC;gBAC3C,4CAA4C;gBAC5C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAClC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC/C,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAClE;YAED,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,EAAE;gBAC9C,kIAAkI;gBAClI,IAAI,IAAI,CAAC,KAAK,KAAK,UAAU,CAAC,UAAU,EAAE;oBACtC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;iBACzC;gBACD,qGAAqG;gBACrG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;oBACjC,CAAC,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAChC,CAAC,CAAC,CAAC;gBAEH,yBAAyB;gBACzB,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC;gBACrD,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;gBAC7C,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,YAAY,EAAE;oBAC9C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;iBAChF;gBACD,IAAI,WAAW,KAAK,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE;oBACtE,IAAU,IAAI,CAAC,YAAa,CAAC,WAAW,EAAE;wBAChC,IAAI,CAAC,YAAa,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;qBAC9D;yBAAM;wBACH,IAAI,CAAC,YAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;qBAC9D;iBACJ;gBAED,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YAEH,mEAAmE;YACnE,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;gBACjD,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;YACH,OAAO,IAAI,CAAC,cAAc,CAAC;SAC9B;QAAC,OAAO,CAAC,EAAE;YACR,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACrC,MAAM,CAAC,CAAC;SACX;IACL,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,8BAA8B;QAC9B,IAAI,IAAI,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,EAAE;YACjC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QACD,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;IAC7C,CAAC;IAED;;;;;;;OAOG;IACI,mBAAmB,CAAC,OAAkC;QACzD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;OAEG;IACI,qBAAqB;QACxB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC/B;IACL,CAAC;IAEO,oBAAoB,CAAC,OAAkC;QAC3D,MAAM,GAAG,GAAG,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;QAChD,MAAM,WAAW,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC;QACzC,MAAM,WAAW,GAAG,OAAO,EAAE,oBAAoB,CAAC,CAAC,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtF,MAAM,qBAAqB,GAAG,GAAG,EAAE;YAC/B,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC;gBACzE,IAAI,KAAK,IAAI,WAAW,EAAE;oBACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC;oBAC3D,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,CAAC;oBAC5F,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,gBAAgB,CAAC,CAAC;iBAC3G;aACJ;QACL,CAAC,CAAC;QACF,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE;gBAC9C,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;aAC3F;YACD,MAAM,cAAc,GAAG,GAAG,EAAE;gBACxB,IAAI,IAAI,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,EAAE;oBACjC,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,CAAC,iBAAiB,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC5F,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;oBAC5D,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBACjE,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;oBACnE,IAAI,CAAC,MAAM,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;wBACrD,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;4BACxB,mDAAmD;4BACnD,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,2BAA2B,GAAG,IAAI,CAAC;yBAC9D;oBACL,CAAC,CAAC,CAAC;iBACN;qBAAM,IAAI,IAAI,CAAC,KAAK,KAAK,UAAU,CAAC,UAAU,EAAE;oBAC7C,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;oBAC9E,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;iBACpC;YACL,CAAC,CAAC;YACF,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAClD,cAAc,EAAE,CAAC;SACpB;aAAM;YACH,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;YAC9E,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC7C;IACL,CAAC;IAEO,gBAAgB;QACpB,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,IAAI,CAAC,YAAa,CAAC,CAAC;QACjE,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnE,CAAC;IAEO,SAAS,CAAC,GAAe;QAC7B,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,EAAE;YACpB,OAAO;SACV;QACD,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;QACjB,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9D,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { IDisposable, Scene } from \"../scene\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { WebXRSessionManager } from \"./webXRSessionManager\";\r\nimport { WebXRCamera } from \"./webXRCamera\";\r\nimport type { WebXRRenderTarget } from \"./webXRTypes\";\r\nimport { WebXRState } from \"./webXRTypes\";\r\nimport { WebXRFeatureName, WebXRFeaturesManager } from \"./webXRFeaturesManager\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { UniversalCamera } from \"../Cameras/universalCamera\";\r\nimport { Quaternion, Vector3 } from \"../Maths/math.vector\";\r\n\r\n/**\r\n * Options for setting up XR spectator camera.\r\n */\r\nexport interface WebXRSpectatorModeOption {\r\n    /**\r\n     * Expected refresh rate (frames per sec) for a spectator camera.\r\n     */\r\n    fps?: number;\r\n    /**\r\n     * The index of rigCameras array in a WebXR camera.\r\n     */\r\n    preferredCameraIndex?: number;\r\n}\r\n\r\n/**\r\n * Base set of functionality needed to create an XR experience (WebXRSessionManager, Camera, StateManagement, etc.)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/webXR/webXRExperienceHelpers\r\n */\r\nexport class WebXRExperienceHelper implements IDisposable {\r\n    private _nonVRCamera: Nullable<Camera> = null;\r\n    private _attachedToElement: boolean = false;\r\n    private _spectatorCamera: Nullable<UniversalCamera> = null;\r\n    private _originalSceneAutoClear = true;\r\n    private _supported = false;\r\n    private _spectatorMode = false;\r\n    private _lastTimestamp = 0;\r\n\r\n    /**\r\n     * Camera used to render xr content\r\n     */\r\n    public camera: WebXRCamera;\r\n    /** A features manager for this xr session */\r\n    public featuresManager: WebXRFeaturesManager;\r\n    /**\r\n     * Observers registered here will be triggered after the camera's initial transformation is set\r\n     * This can be used to set a different ground level or an extra rotation.\r\n     *\r\n     * Note that ground level is considered to be at 0. The height defined by the XR camera will be added\r\n     * to the position set after this observable is done executing.\r\n     */\r\n    public onInitialXRPoseSetObservable = new Observable<WebXRCamera>();\r\n    /**\r\n     * Fires when the state of the experience helper has changed\r\n     */\r\n    public onStateChangedObservable = new Observable<WebXRState>();\r\n    /** Session manager used to keep track of xr session */\r\n    public sessionManager: WebXRSessionManager;\r\n    /**\r\n     * The current state of the XR experience (eg. transitioning, in XR or not in XR)\r\n     */\r\n    public state: WebXRState = WebXRState.NOT_IN_XR;\r\n\r\n    /**\r\n     * Creates a WebXRExperienceHelper\r\n     * @param _scene The scene the helper should be created in\r\n     */\r\n    private constructor(private _scene: Scene) {\r\n        this.sessionManager = new WebXRSessionManager(_scene);\r\n        this.camera = new WebXRCamera(\"webxr\", _scene, this.sessionManager);\r\n        this.featuresManager = new WebXRFeaturesManager(this.sessionManager);\r\n\r\n        _scene.onDisposeObservable.addOnce(() => {\r\n            this.dispose();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates the experience helper\r\n     * @param scene the scene to attach the experience helper to\r\n     * @returns a promise for the experience helper\r\n     */\r\n    public static CreateAsync(scene: Scene): Promise<WebXRExperienceHelper> {\r\n        const helper = new WebXRExperienceHelper(scene);\r\n        return helper.sessionManager\r\n            .initializeAsync()\r\n            .then(() => {\r\n                helper._supported = true;\r\n                return helper;\r\n            })\r\n            .catch((e) => {\r\n                helper._setState(WebXRState.NOT_IN_XR);\r\n                helper.dispose();\r\n                throw e;\r\n            });\r\n    }\r\n\r\n    /**\r\n     * Disposes of the experience helper\r\n     */\r\n    public dispose() {\r\n        this.exitXRAsync();\r\n        this.camera.dispose();\r\n        this.onStateChangedObservable.clear();\r\n        this.onInitialXRPoseSetObservable.clear();\r\n        this.sessionManager.dispose();\r\n        this._spectatorCamera?.dispose();\r\n        if (this._nonVRCamera) {\r\n            this._scene.activeCamera = this._nonVRCamera;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enters XR mode (This must be done within a user interaction in most browsers eg. button click)\r\n     * @param sessionMode options for the XR session\r\n     * @param referenceSpaceType frame of reference of the XR session\r\n     * @param renderTarget the output canvas that will be used to enter XR mode\r\n     * @param sessionCreationOptions optional XRSessionInit object to init the session with\r\n     * @returns promise that resolves after xr mode has entered\r\n     */\r\n    public async enterXRAsync(\r\n        sessionMode: XRSessionMode,\r\n        referenceSpaceType: XRReferenceSpaceType,\r\n        renderTarget: WebXRRenderTarget = this.sessionManager.getWebXRRenderTarget(),\r\n        sessionCreationOptions: XRSessionInit = {}\r\n    ): Promise<WebXRSessionManager> {\r\n        if (!this._supported) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"WebXR not supported in this browser or environment\";\r\n        }\r\n        this._setState(WebXRState.ENTERING_XR);\r\n        if (referenceSpaceType !== \"viewer\" && referenceSpaceType !== \"local\") {\r\n            sessionCreationOptions.optionalFeatures = sessionCreationOptions.optionalFeatures || [];\r\n            sessionCreationOptions.optionalFeatures.push(referenceSpaceType);\r\n        }\r\n        sessionCreationOptions = await this.featuresManager._extendXRSessionInitObject(sessionCreationOptions);\r\n        // we currently recommend \"unbounded\" space in AR (#7959)\r\n        if (sessionMode === \"immersive-ar\" && referenceSpaceType !== \"unbounded\") {\r\n            Logger.Warn(\"We recommend using 'unbounded' reference space type when using 'immersive-ar' session mode\");\r\n        }\r\n        // make sure that the session mode is supported\r\n        try {\r\n            await this.sessionManager.initializeSessionAsync(sessionMode, sessionCreationOptions);\r\n            await this.sessionManager.setReferenceSpaceTypeAsync(referenceSpaceType);\r\n\r\n            const xrRenderState: XRRenderStateInit = {\r\n                // if maxZ is 0 it should be \"Infinity\", but it doesn't work with the WebXR API. Setting to a large number.\r\n                depthFar: this.camera.maxZ || 10000,\r\n                depthNear: this.camera.minZ,\r\n            };\r\n\r\n            // The layers feature will have already initialized the xr session's layers on session init.\r\n            if (!this.featuresManager.getEnabledFeature(WebXRFeatureName.LAYERS)) {\r\n                const baseLayer = await renderTarget.initializeXRLayerAsync(this.sessionManager.session);\r\n                xrRenderState.baseLayer = baseLayer;\r\n            }\r\n\r\n            this.sessionManager.updateRenderState(xrRenderState);\r\n            // run the render loop\r\n            this.sessionManager.runXRRenderLoop();\r\n            // Cache pre xr scene settings\r\n            this._originalSceneAutoClear = this._scene.autoClear;\r\n            this._nonVRCamera = this._scene.activeCamera;\r\n            this._attachedToElement = !!this._nonVRCamera?.inputs?.attachedToElement;\r\n            this._nonVRCamera?.detachControl();\r\n\r\n            this._scene.activeCamera = this.camera;\r\n            // do not compensate when AR session is used\r\n            if (sessionMode !== \"immersive-ar\") {\r\n                this._nonXRToXRCamera();\r\n            } else {\r\n                // Kept here, TODO - check if needed\r\n                this._scene.autoClear = false;\r\n                this.camera.compensateOnFirstFrame = false;\r\n                // reset the camera's position to the origin\r\n                this.camera.position.set(0, 0, 0);\r\n                this.camera.rotationQuaternion.set(0, 0, 0, 1);\r\n                this.onInitialXRPoseSetObservable.notifyObservers(this.camera);\r\n            }\r\n\r\n            this.sessionManager.onXRSessionEnded.addOnce(() => {\r\n                // when using the back button and not the exit button (default on mobile), the session is ending but the EXITING state was not set\r\n                if (this.state !== WebXRState.EXITING_XR) {\r\n                    this._setState(WebXRState.EXITING_XR);\r\n                }\r\n                // Reset camera rigs output render target to ensure sessions render target is not drawn after it ends\r\n                this.camera.rigCameras.forEach((c) => {\r\n                    c.outputRenderTarget = null;\r\n                });\r\n\r\n                // Restore scene settings\r\n                this._scene.autoClear = this._originalSceneAutoClear;\r\n                this._scene.activeCamera = this._nonVRCamera;\r\n                if (this._attachedToElement && this._nonVRCamera) {\r\n                    this._nonVRCamera.attachControl(!!this._nonVRCamera.inputs.noPreventDefault);\r\n                }\r\n                if (sessionMode !== \"immersive-ar\" && this.camera.compensateOnFirstFrame) {\r\n                    if ((<any>this._nonVRCamera).setPosition) {\r\n                        (<any>this._nonVRCamera).setPosition(this.camera.position);\r\n                    } else {\r\n                        this._nonVRCamera!.position.copyFrom(this.camera.position);\r\n                    }\r\n                }\r\n\r\n                this._setState(WebXRState.NOT_IN_XR);\r\n            });\r\n\r\n            // Wait until the first frame arrives before setting state to in xr\r\n            this.sessionManager.onXRFrameObservable.addOnce(() => {\r\n                this._setState(WebXRState.IN_XR);\r\n            });\r\n            return this.sessionManager;\r\n        } catch (e) {\r\n            Logger.Log(e);\r\n            Logger.Log(e.message);\r\n            this._setState(WebXRState.NOT_IN_XR);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Exits XR mode and returns the scene to its original state\r\n     * @returns promise that resolves after xr mode has exited\r\n     */\r\n    public exitXRAsync() {\r\n        // only exit if state is IN_XR\r\n        if (this.state !== WebXRState.IN_XR) {\r\n            return Promise.resolve();\r\n        }\r\n        this._setState(WebXRState.EXITING_XR);\r\n        return this.sessionManager.exitXRAsync();\r\n    }\r\n\r\n    /**\r\n     * Enable spectator mode for desktop VR experiences.\r\n     * When spectator mode is enabled a camera will be attached to the desktop canvas and will\r\n     * display the first rig camera's view on the desktop canvas.\r\n     * Please note that this will degrade performance, as it requires another camera render.\r\n     * It is also not recommended to enable this in devices like the quest, as it brings no benefit there.\r\n     * @param options giving WebXRSpectatorModeOption for specutator camera to setup when the spectator mode is enabled.\r\n     */\r\n    public enableSpectatorMode(options?: WebXRSpectatorModeOption): void {\r\n        if (!this._spectatorMode) {\r\n            this._spectatorMode = true;\r\n            this._switchSpectatorMode(options);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disable spectator mode for desktop VR experiences.\r\n     */\r\n    public disableSpecatatorMode(): void {\r\n        if (this._spectatorMode) {\r\n            this._spectatorMode = false;\r\n            this._switchSpectatorMode();\r\n        }\r\n    }\r\n\r\n    private _switchSpectatorMode(options?: WebXRSpectatorModeOption): void {\r\n        const fps = options?.fps ? options.fps : 1000.0;\r\n        const refreshRate = (1.0 / fps) * 1000.0;\r\n        const cameraIndex = options?.preferredCameraIndex ? options?.preferredCameraIndex : 0;\r\n\r\n        const updateSpectatorCamera = () => {\r\n            if (this._spectatorCamera) {\r\n                const delta = this.sessionManager.currentTimestamp - this._lastTimestamp;\r\n                if (delta >= refreshRate) {\r\n                    this._lastTimestamp = this.sessionManager.currentTimestamp;\r\n                    this._spectatorCamera.position.copyFrom(this.camera.rigCameras[cameraIndex].globalPosition);\r\n                    this._spectatorCamera.rotationQuaternion.copyFrom(this.camera.rigCameras[cameraIndex].absoluteRotation);\r\n                }\r\n            }\r\n        };\r\n        if (this._spectatorMode) {\r\n            if (cameraIndex >= this.camera.rigCameras.length) {\r\n                throw new Error(\"the preferred camera index is beyond the length of rig camera array.\");\r\n            }\r\n            const onStateChanged = () => {\r\n                if (this.state === WebXRState.IN_XR) {\r\n                    this._spectatorCamera = new UniversalCamera(\"webxr-spectator\", Vector3.Zero(), this._scene);\r\n                    this._spectatorCamera.rotationQuaternion = new Quaternion();\r\n                    this._scene.activeCameras = [this.camera, this._spectatorCamera];\r\n                    this.sessionManager.onXRFrameObservable.add(updateSpectatorCamera);\r\n                    this._scene.onAfterRenderCameraObservable.add((camera) => {\r\n                        if (camera === this.camera) {\r\n                            // reset the dimensions object for correct resizing\r\n                            this._scene.getEngine().framebufferDimensionsObject = null;\r\n                        }\r\n                    });\r\n                } else if (this.state === WebXRState.EXITING_XR) {\r\n                    this.sessionManager.onXRFrameObservable.removeCallback(updateSpectatorCamera);\r\n                    this._scene.activeCameras = null;\r\n                }\r\n            };\r\n            this.onStateChangedObservable.add(onStateChanged);\r\n            onStateChanged();\r\n        } else {\r\n            this.sessionManager.onXRFrameObservable.removeCallback(updateSpectatorCamera);\r\n            this._scene.activeCameras = [this.camera];\r\n        }\r\n    }\r\n\r\n    private _nonXRToXRCamera() {\r\n        this.camera.setTransformationFromNonVRCamera(this._nonVRCamera!);\r\n        this.onInitialXRPoseSetObservable.notifyObservers(this.camera);\r\n    }\r\n\r\n    private _setState(val: WebXRState) {\r\n        if (this.state === val) {\r\n            return;\r\n        }\r\n        this.state = val;\r\n        this.onStateChangedObservable.notifyObservers(this.state);\r\n    }\r\n}\r\n"]}