{"version": 3, "file": "nodeGeometryExecutionContext.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Meshes/Node/Interfaces/nodeGeometryExecutionContext.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\r\n * Interface used to convey context through execution nodes\r\n */\r\nexport interface INodeGeometryExecutionContext {\r\n    /**\r\n     * Gets the current vertex index in the current flow\r\n     * @returns the current index\r\n     */\r\n    getExecutionIndex(): number;\r\n    /**\r\n     * Gets the current face index in the current flow\r\n     * @returns the current face index\r\n     */\r\n    getExecutionFaceIndex(): number;\r\n    /**\r\n     * Gets the current loop index in the current flow\r\n     * @returns the current loop index\r\n     */\r\n    getExecutionLoopIndex(): number;\r\n\r\n    /**\r\n     * Gets the value associated with a contextual positions\r\n     * @returns the value associated with the source\r\n     */\r\n    getOverridePositionsContextualValue?(): any;\r\n\r\n    /**\r\n     * Gets the value associated with a contextual normals\r\n     * @returns the value associated with the source\r\n     */\r\n    getOverrideNormalsContextualValue?(): any;\r\n\r\n    /**\r\n     * Gets the value associated with a contextual UV1 set\r\n     * @returns the value associated with the source\r\n     */\r\n    getOverrideUVs1ContextualValue?(): any;\r\n}\r\n"]}