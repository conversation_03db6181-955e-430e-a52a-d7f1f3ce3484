{"version": 3, "file": "sprite.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Sprites/sprite.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAI/C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAI1C;;;GAGG;AACH,MAAM,OAAO,MAAO,SAAQ,UAAU;IA2BlC;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAa;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAOD;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACH;IACI,uBAAuB;IAChB,IAAY,EACnB,OAAuB;QAEvB,KAAK,EAAE,CAAC;QAHD,SAAI,GAAJ,IAAI,CAAQ;QAnDvB,2CAA2C;QACpC,eAAU,GAA+B,IAAI,KAAK,EAAa,CAAC;QACvE,oEAAoE;QAC7D,eAAU,GAAG,KAAK,CAAC;QAC1B,sHAAsH;QAC/G,uBAAkB,GAAG,KAAK,CAAC;QAOlC;;WAEG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAU,CAAC;QAG9C,oBAAe,GAAyB,IAAI,CAAC;QAmG7C,kBAAa,GAAG,GAAG,EAAE;YACzB,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,CAAC,eAAe,EAAE,CAAC;aAC1B;YACD,IAAI,IAAI,CAAC,4BAA4B,EAAE;gBACnC,IAAI,CAAC,OAAO,EAAE,CAAC;aAClB;QACL,CAAC,CAAC;QArEE,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE/B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;IACtD,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,8FAA8F;IAC9F,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD,IAAW,SAAS,CAAC,KAAa;QAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACrG,CAAC;IAED,0FAA0F;IAC1F,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IACD,IAAW,OAAO,CAAC,KAAa;QAC5B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACvG,CAAC;IAED,6GAA6G;IAC7G,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IACD,IAAW,aAAa,CAAC,KAAc;QACnC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACjG,CAAC;IAED,2FAA2F;IAC3F,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACpC,CAAC;IACD,IAAW,KAAK,CAAC,KAAa;QAC1B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACzG,CAAC;IAED;;;;;;;OAOG;IACI,aAAa,CAAC,IAAY,EAAE,EAAU,EAAE,IAAa,EAAE,KAAa,EAAE,iBAAuC,IAAI;QACpH,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QAEtC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACnE,CAAC;IAWD,mCAAmC;IAC5B,OAAO;QACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;gBAClC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACtC;SACJ;QAED,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACvD,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACjD,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzC,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvC,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,4BAA4B,GAAG,IAAI,CAAC,4BAA4B,CAAC;QACrF,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAEjE,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC7D,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvD,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAEvC,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,YAAiB,EAAE,OAAsB;QACzD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEtD,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC3D,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACpD,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;QAClC,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;QACpC,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;QAClC,MAAM,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;QAC1C,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;QACtC,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;QACtC,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;QACtC,MAAM,CAAC,4BAA4B,GAAG,YAAY,CAAC,4BAA4B,CAAC;QAChF,MAAM,CAAC,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;QAC5C,MAAM,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;QAC1C,MAAM,CAAC,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,CAAC;QAE5D,MAAM,CAAC,UAAU,GAAG,YAAY,CAAC,SAAS,CAAC;QAC3C,MAAM,CAAC,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC;QACvC,MAAM,CAAC,cAAc,GAAG,YAAY,CAAC,aAAa,CAAC;QACnD,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC;QAEnC,IAAI,YAAY,CAAC,gBAAgB,EAAE;YAC/B,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;SAC9F;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ", "sourcesContent": ["import { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { ActionManager } from \"../Actions/actionManager\";\r\nimport type { ISpriteManager, SpriteManager } from \"./spriteManager\";\r\nimport { Color4 } from \"../Maths/math.color\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { IAnimatable } from \"../Animations/animatable.interface\";\r\nimport { ThinSprite } from \"./thinSprite\";\r\n\r\nimport type { Animation } from \"../Animations/animation\";\r\n\r\n/**\r\n * Class used to represent a sprite\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/sprites\r\n */\r\nexport class Sprite extends ThinSprite implements IAnimatable {\r\n    /** Gets or sets the current world position */\r\n    public position: Vector3;\r\n    /** Gets or sets the main color */\r\n    public color: Color4;\r\n    /** Gets or sets a boolean indicating that this sprite should be disposed after animation ends */\r\n    public disposeWhenFinishedAnimating: boolean;\r\n    /** Gets the list of attached animations */\r\n    public animations: Nullable<Array<Animation>> = new Array<Animation>();\r\n    /** Gets or sets a boolean indicating if the sprite can be picked */\r\n    public isPickable = false;\r\n    /** Gets or sets a boolean indicating that sprite texture alpha will be used for precise picking (false by default) */\r\n    public useAlphaForPicking = false;\r\n\r\n    /**\r\n     * Gets or sets the associated action manager\r\n     */\r\n    public actionManager: Nullable<ActionManager>;\r\n\r\n    /**\r\n     * An event triggered when the control has been disposed\r\n     */\r\n    public onDisposeObservable = new Observable<Sprite>();\r\n\r\n    private _manager: ISpriteManager;\r\n    private _onAnimationEnd: Nullable<() => void> = null;\r\n\r\n    /**\r\n     * Gets or sets the sprite size\r\n     */\r\n    public get size(): number {\r\n        return this.width;\r\n    }\r\n\r\n    public set size(value: number) {\r\n        this.width = value;\r\n        this.height = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the unique id of the sprite\r\n     */\r\n    public uniqueId: number;\r\n\r\n    /**\r\n     * Gets the manager of this sprite\r\n     */\r\n    public get manager() {\r\n        return this._manager;\r\n    }\r\n\r\n    /**\r\n     * Creates a new Sprite\r\n     * @param name defines the name\r\n     * @param manager defines the manager\r\n     */\r\n    constructor(\r\n        /** defines the name */\r\n        public name: string,\r\n        manager: ISpriteManager\r\n    ) {\r\n        super();\r\n        this.color = new Color4(1.0, 1.0, 1.0, 1.0);\r\n        this.position = Vector3.Zero();\r\n\r\n        this._manager = manager;\r\n        this._manager.sprites.push(this);\r\n        this.uniqueId = this._manager.scene.getUniqueId();\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"Sprite\"\r\n     * @returns \"Sprite\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"Sprite\";\r\n    }\r\n\r\n    /** Gets or sets the initial key for the animation (setting it will restart the animation)  */\r\n    public get fromIndex() {\r\n        return this._fromIndex;\r\n    }\r\n    public set fromIndex(value: number) {\r\n        this.playAnimation(value, this._toIndex, this._loopAnimation, this._delay, this._onAnimationEnd);\r\n    }\r\n\r\n    /** Gets or sets the end key for the animation (setting it will restart the animation)  */\r\n    public get toIndex() {\r\n        return this._toIndex;\r\n    }\r\n    public set toIndex(value: number) {\r\n        this.playAnimation(this._fromIndex, value, this._loopAnimation, this._delay, this._onAnimationEnd);\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating if the animation is looping (setting it will restart the animation)  */\r\n    public get loopAnimation() {\r\n        return this._loopAnimation;\r\n    }\r\n    public set loopAnimation(value: boolean) {\r\n        this.playAnimation(this._fromIndex, this._toIndex, value, this._delay, this._onAnimationEnd);\r\n    }\r\n\r\n    /** Gets or sets the delay between cell changes (setting it will restart the animation)  */\r\n    public get delay() {\r\n        return Math.max(this._delay, 1);\r\n    }\r\n    public set delay(value: number) {\r\n        this.playAnimation(this._fromIndex, this._toIndex, this._loopAnimation, value, this._onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Starts an animation\r\n     * @param from defines the initial key\r\n     * @param to defines the end key\r\n     * @param loop defines if the animation must loop\r\n     * @param delay defines the start delay (in ms)\r\n     * @param onAnimationEnd defines a callback to call when animation ends\r\n     */\r\n    public playAnimation(from: number, to: number, loop: boolean, delay: number, onAnimationEnd: Nullable<() => void> = null): void {\r\n        this._onAnimationEnd = onAnimationEnd;\r\n\r\n        super.playAnimation(from, to, loop, delay, this._endAnimation);\r\n    }\r\n\r\n    private _endAnimation = () => {\r\n        if (this._onAnimationEnd) {\r\n            this._onAnimationEnd();\r\n        }\r\n        if (this.disposeWhenFinishedAnimating) {\r\n            this.dispose();\r\n        }\r\n    };\r\n\r\n    /** Release associated resources */\r\n    public dispose(): void {\r\n        for (let i = 0; i < this._manager.sprites.length; i++) {\r\n            if (this._manager.sprites[i] == this) {\r\n                this._manager.sprites.splice(i, 1);\r\n            }\r\n        }\r\n\r\n        // Callback\r\n        this.onDisposeObservable.notifyObservers(this);\r\n        this.onDisposeObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Serializes the sprite to a JSON object\r\n     * @returns the JSON object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.position = this.position.asArray();\r\n        serializationObject.color = this.color.asArray();\r\n        serializationObject.width = this.width;\r\n        serializationObject.height = this.height;\r\n        serializationObject.angle = this.angle;\r\n        serializationObject.cellIndex = this.cellIndex;\r\n        serializationObject.cellRef = this.cellRef;\r\n        serializationObject.invertU = this.invertU;\r\n        serializationObject.invertV = this.invertV;\r\n        serializationObject.disposeWhenFinishedAnimating = this.disposeWhenFinishedAnimating;\r\n        serializationObject.isPickable = this.isPickable;\r\n        serializationObject.isVisible = this.isVisible;\r\n        serializationObject.useAlphaForPicking = this.useAlphaForPicking;\r\n\r\n        serializationObject.animationStarted = this.animationStarted;\r\n        serializationObject.fromIndex = this.fromIndex;\r\n        serializationObject.toIndex = this.toIndex;\r\n        serializationObject.loopAnimation = this.loopAnimation;\r\n        serializationObject.delay = this.delay;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parses a JSON object to create a new sprite\r\n     * @param parsedSprite The JSON object to parse\r\n     * @param manager defines the hosting manager\r\n     * @returns the new sprite\r\n     */\r\n    public static Parse(parsedSprite: any, manager: SpriteManager): Sprite {\r\n        const sprite = new Sprite(parsedSprite.name, manager);\r\n\r\n        sprite.position = Vector3.FromArray(parsedSprite.position);\r\n        sprite.color = Color4.FromArray(parsedSprite.color);\r\n        sprite.width = parsedSprite.width;\r\n        sprite.height = parsedSprite.height;\r\n        sprite.angle = parsedSprite.angle;\r\n        sprite.cellIndex = parsedSprite.cellIndex;\r\n        sprite.cellRef = parsedSprite.cellRef;\r\n        sprite.invertU = parsedSprite.invertU;\r\n        sprite.invertV = parsedSprite.invertV;\r\n        sprite.disposeWhenFinishedAnimating = parsedSprite.disposeWhenFinishedAnimating;\r\n        sprite.isPickable = parsedSprite.isPickable;\r\n        sprite.isVisible = parsedSprite.isVisible;\r\n        sprite.useAlphaForPicking = parsedSprite.useAlphaForPicking;\r\n\r\n        sprite._fromIndex = parsedSprite.fromIndex;\r\n        sprite._toIndex = parsedSprite.toIndex;\r\n        sprite._loopAnimation = parsedSprite.loopAnimation;\r\n        sprite._delay = parsedSprite.delay;\r\n\r\n        if (parsedSprite.animationStarted) {\r\n            sprite.playAnimation(sprite.fromIndex, sprite.toIndex, sprite.loopAnimation, sprite.delay);\r\n        }\r\n\r\n        return sprite;\r\n    }\r\n}\r\n"]}