{"version": 3, "file": "taaRenderingPipeline.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/PostProcesses/RenderPipeline/Pipelines/taaRenderingPipeline.ts"], "names": [], "mappings": ";AAAA,yDAAyD;AACzD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,mBAAmB,EAAE,MAAM,wCAAwC,CAAC;AAC7E,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAEjD,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,yBAAyB,EAAE,MAAM,8BAA8B,CAAC;AACzE,OAAO,EAAE,uBAAuB,EAAE,MAAM,4BAA4B,CAAC;AAErE,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AAEvD,OAAO,EAAE,eAAe,EAAE,iCAA2C;AAErE,OAAO,EAAE,gBAAgB,EAAE,2CAAoC;AAE/D,OAAO,mDAAmD,CAAC;AAE3D,OAAO,+BAA+B,CAAC;AAEvC;;;GAGG;AACH,MAAM,OAAO,oBAAqB,SAAQ,yBAAyB;IAY/D;;OAEG;IACH,IAAW,OAAO,CAAC,OAAe;QAC9B,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;YAC3B,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;IAED,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAID;;OAEG;IACH,IAAW,WAAW,CAAC,OAAe;QAClC,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAC5B,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,OAAO,CAAC;SAC1C;IACL,CAAC;IAED,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAiBD;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,SAAS,CAAC,KAAc;QAC/B,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE;YAC3B,OAAO;SACV;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,IAAI,CAAC,KAAK,EAAE;YACR,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;gBACxB,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACxG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;aACrD;SACJ;aAAM,IAAI,KAAK,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChB,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;oBACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;oBACzB,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,6BAA6B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACzG;aACJ;iBAAM;gBACH,IAAI,CAAC,cAAc,EAAE,CAAC;aACzB;SACJ;IACL,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAcD;;OAEG;IACH,IAAW,WAAW;QAClB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC;QAE/C,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG;IACH,YAAY,IAAY,EAAE,KAAY,EAAE,OAAkB,EAAE,WAAW,GAAG,SAAS,CAAC,yBAAyB;QACzG,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAnIxB;;WAEG;QACI,oBAAe,GAAW,iBAAiB,CAAC;QACnD;;WAEG;QACI,kBAAa,GAAW,eAAe,CAAC;QAGvC,aAAQ,GAAG,CAAC,CAAC;QAkBb,iBAAY,GAAG,CAAC,CAAC;QAmBzB;;WAEG;QAEI,WAAM,GAAG,IAAI,CAAC;QAErB;;;WAGG;QAEI,wBAAmB,GAAG,IAAI,CAAC;QAG1B,eAAU,GAAG,IAAI,CAAC;QAwClB,aAAQ,GAAG,KAAK,CAAC;QACjB,yBAAoB,GAAkB,EAAE,CAAC;QAMzC,cAAS,GAAG,CAAC,CAAC;QAEd,iBAAY,GAAG,IAAI,CAAC;QAuBxB,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAElD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,GAAG,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE9C,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;YAEhF,KAAK,CAAC,gCAAgC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAEzD,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,MAAc;QAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,MAAc;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3C,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAExG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACrB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QAErB,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAEO,uBAAuB,CAAC,KAAa,EAAE,MAAc;QACzD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;QACtB,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;QAEtB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,yBAAyB,CACzC,EAAE,KAAK,EAAE,MAAM,EAAE,EACjB,EAAE,eAAe,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC,sBAAsB,EAAE,YAAY,EAAE,SAAS,CAAC,uBAAuB,EAAE,CAClJ,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,yBAAyB,CACzC,EAAE,KAAK,EAAE,MAAM,EAAE,EACjB,EAAE,eAAe,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC,sBAAsB,EAAE,YAAY,EAAE,SAAS,CAAC,uBAAuB,EAAE,CAClJ,CAAC;QAEF,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,CAAC;IAEO,oBAAoB;QACxB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,CAAC,eAAe,EAAE,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,cAAc;QAClB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YACxB,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxG,mDAAmD;YACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;SACrD;QACD,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,MAAM,EACN,IAAI,CAAC,eAAe,EACpB,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,eAAe,CAAC;QAChC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QAEF,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,MAAM,EACN,IAAI,CAAC,aAAa,EAClB,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;QACjC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YACxB,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,6BAA6B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SACzG;IACL,CAAC;IAEO,qBAAqB;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEhC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YACtC,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAEvC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,kCAAkC;SACvE;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACjC,CAAC;IAEO,qBAAqB;QACzB,IAAI,CAAC,eAAe,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE;YACjD,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACpB,QAAQ,EAAE,CAAC,gBAAgB,CAAC;YAC5B,IAAI,EAAE,GAAG;YACT,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YAC/B,WAAW,EAAE,IAAI,CAAC,YAAY;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;QAEjD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YAExC,IAAI,IAAI,CAAC,eAAe,EAAE,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACxG,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACvC,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;aACnF;YAED,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBAC5B,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,kBAAkB,EAAE;oBAC3C,MAAM,OAAO,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC;oBAC7C,OAAO,CAAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACrF;qBAAM;oBACH,mIAAmI;oBACnI,MAAM,OAAO,GAAG,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;oBACjD,OAAO,CAAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACrH;aACJ;YAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;aACjF;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;YACpC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAc,EAAE,EAAE;YAC1D,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YAExC,MAAM,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAChG,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,QAAQ,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAEjH,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC9B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,sBAAsB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC;QAC3G,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;QAChD,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,mBAAmB,CAAC,UAAU,GAAG,sBAAsB,CAAC;QAExD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,OAAO,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,oBAAoB,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACjI,CAAC;CACJ;AA9VW;IADP,SAAS,CAAC,SAAS,CAAC;sDACA;AAkBb;IADP,SAAS,CAAC,aAAa,CAAC;0DACA;AAuBlB;IADN,SAAS,EAAE;oDACS;AAOd;IADN,SAAS,EAAE;iEACsB;AAG1B;IADP,SAAS,CAAC,WAAW,CAAC;wDACG;AA6S9B,aAAa,CAAC,8BAA8B,EAAE,oBAAoB,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { serialize } from \"../../../Misc/decorators\";\r\nimport { SerializationHelper } from \"../../../Misc/decorators.serialization\";\r\nimport { Camera } from \"../../../Cameras/camera\";\r\nimport type { Effect } from \"../../../Materials/effect\";\r\nimport { PostProcess } from \"../../postProcess\";\r\nimport { PostProcessRenderPipeline } from \"../postProcessRenderPipeline\";\r\nimport { PostProcessRenderEffect } from \"../postProcessRenderEffect\";\r\nimport type { Scene } from \"../../../scene\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport { Constants } from \"../../../Engines/constants\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport { PassPostProcess } from \"core/PostProcesses/passPostProcess\";\r\nimport type { RenderTargetWrapper } from \"core/Engines/renderTargetWrapper\";\r\nimport { Halton2DSequence } from \"core/Maths/halton2DSequence\";\r\n\r\nimport \"../postProcessRenderPipelineManagerSceneComponent\";\r\n\r\nimport \"../../../Shaders/taa.fragment\";\r\n\r\n/**\r\n * Simple implementation of Temporal Anti-Aliasing (TAA).\r\n * This can be used to improve image quality for still pictures (screenshots for e.g.).\r\n */\r\nexport class TAARenderingPipeline extends PostProcessRenderPipeline {\r\n    /**\r\n     * The TAA PostProcess effect id in the pipeline\r\n     */\r\n    public TAARenderEffect: string = \"TAARenderEffect\";\r\n    /**\r\n     * The pass PostProcess effect id in the pipeline\r\n     */\r\n    public TAAPassEffect: string = \"TAAPassEffect\";\r\n\r\n    @serialize(\"samples\")\r\n    private _samples = 8;\r\n    /**\r\n     * Number of accumulated samples (default: 16)\r\n     */\r\n    public set samples(samples: number) {\r\n        if (this._samples === samples) {\r\n            return;\r\n        }\r\n\r\n        this._samples = samples;\r\n        this._hs.regenerate(samples);\r\n    }\r\n\r\n    public get samples(): number {\r\n        return this._samples;\r\n    }\r\n\r\n    @serialize(\"msaaSamples\")\r\n    private _msaaSamples = 1;\r\n    /**\r\n     * MSAA samples (default: 1)\r\n     */\r\n    public set msaaSamples(samples: number) {\r\n        if (this._msaaSamples === samples) {\r\n            return;\r\n        }\r\n\r\n        this._msaaSamples = samples;\r\n        if (this._taaPostProcess) {\r\n            this._taaPostProcess.samples = samples;\r\n        }\r\n    }\r\n\r\n    public get msaaSamples(): number {\r\n        return this._msaaSamples;\r\n    }\r\n\r\n    /**\r\n     * The factor used to blend the history frame with current frame (default: 0.05)\r\n     */\r\n    @serialize()\r\n    public factor = 0.05;\r\n\r\n    /**\r\n     * Disable TAA on camera move (default: true).\r\n     * You generally want to keep this enabled, otherwise you will get a ghost effect when the camera moves (but if it's what you want, go for it!)\r\n     */\r\n    @serialize()\r\n    public disableOnCameraMove = true;\r\n\r\n    @serialize(\"isEnabled\")\r\n    private _isEnabled = true;\r\n    /**\r\n     * Gets or sets a boolean indicating if the render pipeline is enabled (default: true).\r\n     */\r\n    public get isEnabled(): boolean {\r\n        return this._isEnabled;\r\n    }\r\n\r\n    public set isEnabled(value: boolean) {\r\n        if (this._isEnabled === value) {\r\n            return;\r\n        }\r\n\r\n        this._isEnabled = value;\r\n\r\n        if (!value) {\r\n            if (this._cameras !== null) {\r\n                this._scene.postProcessRenderPipelineManager.detachCamerasFromRenderPipeline(this._name, this._cameras);\r\n                this._cameras = this._camerasToBeAttached.slice();\r\n            }\r\n        } else if (value) {\r\n            if (!this._isDirty) {\r\n                if (this._cameras !== null) {\r\n                    this._firstUpdate = true;\r\n                    this._scene.postProcessRenderPipelineManager.attachCamerasToRenderPipeline(this._name, this._cameras);\r\n                }\r\n            } else {\r\n                this._buildPipeline();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets active scene\r\n     */\r\n    public get scene(): Scene {\r\n        return this._scene;\r\n    }\r\n\r\n    private _scene: Scene;\r\n    private _isDirty = false;\r\n    private _camerasToBeAttached: Array<Camera> = [];\r\n    private _textureType: number;\r\n    private _taaPostProcess: Nullable<PostProcess>;\r\n    private _passPostProcess: Nullable<PassPostProcess>;\r\n    private _ping: RenderTargetWrapper;\r\n    private _pong: RenderTargetWrapper;\r\n    private _pingpong = 0;\r\n    private _hs: Halton2DSequence;\r\n    private _firstUpdate = true;\r\n\r\n    /**\r\n     * Returns true if TAA is supported by the running hardware\r\n     */\r\n    public get isSupported(): boolean {\r\n        const caps = this._scene.getEngine().getCaps();\r\n\r\n        return caps.texelFetch;\r\n    }\r\n\r\n    /**\r\n     * Constructor of the TAA rendering pipeline\r\n     * @param name The rendering pipeline name\r\n     * @param scene The scene linked to this pipeline\r\n     * @param cameras The array of cameras that the rendering pipeline will be attached to (default: scene.cameras)\r\n     * @param textureType The type of texture where the scene will be rendered (default: Constants.TEXTURETYPE_UNSIGNED_BYTE)\r\n     */\r\n    constructor(name: string, scene: Scene, cameras?: Camera[], textureType = Constants.TEXTURETYPE_UNSIGNED_BYTE) {\r\n        const engine = scene.getEngine();\r\n\r\n        super(engine, name);\r\n\r\n        this._cameras = cameras || scene.cameras;\r\n        this._cameras = this._cameras.slice();\r\n        this._camerasToBeAttached = this._cameras.slice();\r\n\r\n        this._scene = scene;\r\n        this._textureType = textureType;\r\n        this._hs = new Halton2DSequence(this.samples);\r\n\r\n        if (this.isSupported) {\r\n            this._createPingPongTextures(engine.getRenderWidth(), engine.getRenderHeight());\r\n\r\n            scene.postProcessRenderPipelineManager.addPipeline(this);\r\n\r\n            this._buildPipeline();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the class name\r\n     * @returns \"TAARenderingPipeline\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"TAARenderingPipeline\";\r\n    }\r\n\r\n    /**\r\n     * Adds a camera to the pipeline\r\n     * @param camera the camera to be added\r\n     */\r\n    public addCamera(camera: Camera): void {\r\n        this._camerasToBeAttached.push(camera);\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * Removes a camera from the pipeline\r\n     * @param camera the camera to remove\r\n     */\r\n    public removeCamera(camera: Camera): void {\r\n        const index = this._camerasToBeAttached.indexOf(camera);\r\n        this._camerasToBeAttached.splice(index, 1);\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * Removes the internal pipeline assets and detaches the pipeline from the scene cameras\r\n     */\r\n    public dispose(): void {\r\n        this._disposePostProcesses();\r\n\r\n        this._scene.postProcessRenderPipelineManager.detachCamerasFromRenderPipeline(this._name, this._cameras);\r\n\r\n        this._ping.dispose();\r\n        this._pong.dispose();\r\n\r\n        super.dispose();\r\n    }\r\n\r\n    private _createPingPongTextures(width: number, height: number) {\r\n        const engine = this._scene.getEngine();\r\n\r\n        this._ping?.dispose();\r\n        this._pong?.dispose();\r\n\r\n        this._ping = engine.createRenderTargetTexture(\r\n            { width, height },\r\n            { generateMipMaps: false, generateDepthBuffer: false, type: Constants.TEXTURETYPE_HALF_FLOAT, samplingMode: Constants.TEXTURE_NEAREST_NEAREST }\r\n        );\r\n\r\n        this._pong = engine.createRenderTargetTexture(\r\n            { width, height },\r\n            { generateMipMaps: false, generateDepthBuffer: false, type: Constants.TEXTURETYPE_HALF_FLOAT, samplingMode: Constants.TEXTURE_NEAREST_NEAREST }\r\n        );\r\n\r\n        this._hs.setDimensions(width / 2, height / 2);\r\n        this._firstUpdate = true;\r\n    }\r\n\r\n    private _updateEffectDefines(): void {\r\n        const defines: string[] = [];\r\n\r\n        this._taaPostProcess?.updateEffect(defines.join(\"\\n\"));\r\n    }\r\n\r\n    private _buildPipeline() {\r\n        if (!this.isSupported) {\r\n            return;\r\n        }\r\n\r\n        if (!this._isEnabled) {\r\n            this._isDirty = true;\r\n            return;\r\n        }\r\n\r\n        this._isDirty = false;\r\n\r\n        const engine = this._scene.getEngine();\r\n\r\n        this._disposePostProcesses();\r\n        if (this._cameras !== null) {\r\n            this._scene.postProcessRenderPipelineManager.detachCamerasFromRenderPipeline(this._name, this._cameras);\r\n            // get back cameras to be used to reattach pipeline\r\n            this._cameras = this._camerasToBeAttached.slice();\r\n        }\r\n        this._reset();\r\n\r\n        this._createTAAPostProcess();\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                engine,\r\n                this.TAARenderEffect,\r\n                () => {\r\n                    return this._taaPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n\r\n        this._createPassPostProcess();\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                engine,\r\n                this.TAAPassEffect,\r\n                () => {\r\n                    return this._passPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n\r\n        if (this._cameras !== null) {\r\n            this._scene.postProcessRenderPipelineManager.attachCamerasToRenderPipeline(this._name, this._cameras);\r\n        }\r\n    }\r\n\r\n    private _disposePostProcesses(): void {\r\n        for (let i = 0; i < this._cameras.length; i++) {\r\n            const camera = this._cameras[i];\r\n\r\n            this._taaPostProcess?.dispose(camera);\r\n            this._passPostProcess?.dispose(camera);\r\n\r\n            camera.getProjectionMatrix(true); // recompute the projection matrix\r\n        }\r\n\r\n        this._taaPostProcess = null;\r\n        this._passPostProcess = null;\r\n    }\r\n\r\n    private _createTAAPostProcess(): void {\r\n        this._taaPostProcess = new PostProcess(\"TAA\", \"taa\", {\r\n            uniforms: [\"factor\"],\r\n            samplers: [\"historySampler\"],\r\n            size: 1.0,\r\n            engine: this._scene.getEngine(),\r\n            textureType: this._textureType,\r\n        });\r\n\r\n        this._taaPostProcess.samples = this._msaaSamples;\r\n\r\n        this._updateEffectDefines();\r\n\r\n        this._taaPostProcess.onActivateObservable.add(() => {\r\n            const camera = this._scene.activeCamera;\r\n\r\n            if (this._taaPostProcess?.width !== this._ping.width || this._taaPostProcess?.height !== this._ping.height) {\r\n                const engine = this._scene.getEngine();\r\n                this._createPingPongTextures(engine.getRenderWidth(), engine.getRenderHeight());\r\n            }\r\n\r\n            if (camera && !camera.hasMoved) {\r\n                if (camera.mode === Camera.PERSPECTIVE_CAMERA) {\r\n                    const projMat = camera.getProjectionMatrix();\r\n                    projMat.setRowFromFloats(2, this._hs.x, this._hs.y, projMat.m[10], projMat.m[11]);\r\n                } else {\r\n                    // We must force the update of the projection matrix so that m[12] and m[13] are recomputed, as we modified them the previous frame\r\n                    const projMat = camera.getProjectionMatrix(true);\r\n                    projMat.setRowFromFloats(3, this._hs.x + projMat.m[12], this._hs.y + projMat.m[13], projMat.m[14], projMat.m[15]);\r\n                }\r\n            }\r\n\r\n            if (this._passPostProcess) {\r\n                this._passPostProcess.inputTexture = this._pingpong ? this._ping : this._pong;\r\n            }\r\n            this._pingpong = this._pingpong ^ 1;\r\n            this._hs.next();\r\n        });\r\n\r\n        this._taaPostProcess.onApplyObservable.add((effect: Effect) => {\r\n            const camera = this._scene.activeCamera;\r\n\r\n            effect._bindTexture(\"historySampler\", this._pingpong ? this._ping.texture : this._pong.texture);\r\n            effect.setFloat(\"factor\", (camera?.hasMoved && this.disableOnCameraMove) || this._firstUpdate ? 1 : this.factor);\r\n\r\n            this._firstUpdate = false;\r\n        });\r\n    }\r\n\r\n    private _createPassPostProcess() {\r\n        const engine = this._scene.getEngine();\r\n\r\n        this._passPostProcess = new PassPostProcess(\"TAAPass\", 1, null, Constants.TEXTURE_NEAREST_NEAREST, engine);\r\n        this._passPostProcess.inputTexture = this._ping;\r\n        this._passPostProcess.autoClear = false;\r\n    }\r\n\r\n    /**\r\n     * Serializes the rendering pipeline (Used when exporting)\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n        serializationObject.customType = \"TAARenderingPipeline\";\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parse the serialized pipeline\r\n     * @param source Source pipeline.\r\n     * @param scene The scene to load the pipeline to.\r\n     * @param rootUrl The URL of the serialized pipeline.\r\n     * @returns An instantiated pipeline from the serialized object.\r\n     */\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): TAARenderingPipeline {\r\n        return SerializationHelper.Parse(() => new TAARenderingPipeline(source._name, scene, source._ratio), source, scene, rootUrl);\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.TAARenderingPipeline\", TAARenderingPipeline);\r\n"]}