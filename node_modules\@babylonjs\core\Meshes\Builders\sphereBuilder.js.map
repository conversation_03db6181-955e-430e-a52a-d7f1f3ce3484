{"version": 3, "file": "sphereBuilder.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Builders/sphereBuilder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAEzE;;;;;;;;;;;;;;GAcG;AACH,MAAM,UAAU,sBAAsB,CAAC,OAYtC;IACG,MAAM,QAAQ,GAAW,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC;IACtD,MAAM,SAAS,GAAW,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;IACrE,MAAM,SAAS,GAAW,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;IACrE,MAAM,SAAS,GAAW,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;IACrE,MAAM,GAAG,GAAW,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC;IACpG,MAAM,KAAK,GAAW,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI,GAAG,CAAC;IACvF,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,IAAI,UAAU,CAAC,WAAW,CAAC;IAC9G,MAAM,qBAAqB,GAAG,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC;IAE9D,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;IAExE,MAAM,mBAAmB,GAAG,CAAC,GAAG,QAAQ,CAAC;IACzC,MAAM,mBAAmB,GAAG,CAAC,GAAG,mBAAmB,CAAC;IAEpD,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,MAAM,SAAS,GAAG,EAAE,CAAC;IACrB,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,MAAM,GAAG,GAAG,EAAE,CAAC;IAEf,KAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,IAAI,mBAAmB,EAAE,aAAa,EAAE,EAAE;QAC/E,MAAM,WAAW,GAAG,aAAa,GAAG,mBAAmB,CAAC;QACxD,MAAM,MAAM,GAAG,WAAW,GAAG,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;QAE7C,KAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,IAAI,mBAAmB,EAAE,aAAa,EAAE,EAAE;YAC/E,MAAM,WAAW,GAAG,aAAa,GAAG,mBAAmB,CAAC;YAExD,MAAM,MAAM,GAAG,WAAW,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;YAE/C,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,SAAS,GAAG,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;YACxE,MAAM,QAAQ,GAAG,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAEpE,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,CAAC;YAEnD,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;YAC7C,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;YAC3C,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;SAC3G;QAED,IAAI,aAAa,GAAG,CAAC,EAAE;YACnB,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YAC3C,KAAK,IAAI,UAAU,GAAG,aAAa,GAAG,CAAC,GAAG,CAAC,mBAAmB,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,mBAAmB,GAAG,CAAC,GAAG,aAAa,EAAE,UAAU,EAAE,EAAE;gBACrI,IAAI,qBAAqB,EAAE;oBACvB,IAAI,aAAa,GAAG,CAAC,EAAE;wBACnB,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBACzB,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;wBAC7B,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,mBAAmB,GAAG,CAAC,CAAC,CAAC;qBACtD;oBACD,IAAI,aAAa,GAAG,mBAAmB,IAAI,KAAK,GAAG,GAAG,EAAE;wBACpD,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,mBAAmB,GAAG,CAAC,CAAC,CAAC;wBACnD,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;wBAC7B,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,mBAAmB,GAAG,CAAC,CAAC,CAAC;qBACtD;iBACJ;qBAAM;oBACH,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACzB,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;oBAC7B,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,mBAAmB,GAAG,CAAC,CAAC,CAAC;oBAEnD,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,mBAAmB,GAAG,CAAC,CAAC,CAAC;oBACnD,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;oBAC7B,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,mBAAmB,GAAG,CAAC,CAAC,CAAC;iBACtD;aACJ;SACJ;KACJ;IAED,QAAQ;IACR,UAAU,CAAC,aAAa,CAAC,eAAe,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IAE/G,SAAS;IACT,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IAEpC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;IACjC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;IAErB,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;;;;;;;;;;;;GAeG;AACH,MAAM,UAAU,YAAY,CACxB,IAAY,EACZ,UAYI,EAAE,EACN,QAAyB,IAAI;IAE7B,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAErC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACnF,MAAM,CAAC,+BAA+B,GAAG,OAAO,CAAC,eAAe,CAAC;IAEjE,MAAM,UAAU,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;IAEnD,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAElD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG;IACzB,gEAAgE;IAChE,YAAY;CACf,CAAC;AAEF,UAAU,CAAC,YAAY,GAAG,sBAAsB,CAAC;AAEjD,IAAI,CAAC,YAAY,GAAG,CAAC,IAAY,EAAE,QAAgB,EAAE,QAAgB,EAAE,KAAa,EAAE,SAAmB,EAAE,eAAwB,EAAQ,EAAE;IACzI,MAAM,OAAO,GAAG;QACZ,QAAQ,EAAE,QAAQ;QAClB,SAAS,EAAE,QAAQ;QACnB,SAAS,EAAE,QAAQ;QACnB,SAAS,EAAE,QAAQ;QACnB,eAAe,EAAE,eAAe;QAChC,SAAS,EAAE,SAAS;KACvB,CAAC;IAEF,OAAO,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC9C,CAAC,CAAC", "sourcesContent": ["import type { Vector4 } from \"../../Maths/math.vector\";\r\nimport { Vector3, Matrix } from \"../../Maths/math.vector\";\r\nimport { Mesh } from \"../mesh\";\r\nimport { VertexData } from \"../mesh.vertexData\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { CompatibilityOptions } from \"../../Compat/compatibilityOptions\";\r\n\r\n/**\r\n * Creates the VertexData for an ellipsoid, defaults to a sphere\r\n * @param options an object used to set the following optional parameters for the box, required but can be empty\r\n * * segments sets the number of horizontal strips optional, default 32\r\n * * diameter sets the axes dimensions, diameterX, diameterY and diameterZ to the value of diameter, optional default 1\r\n * * diameterX sets the diameterX (x direction) of the ellipsoid, overwrites the diameterX set by diameter, optional, default diameter\r\n * * diameterY sets the diameterY (y direction) of the ellipsoid, overwrites the diameterY set by diameter, optional, default diameter\r\n * * diameterZ sets the diameterZ (z direction) of the ellipsoid, overwrites the diameterZ set by diameter, optional, default diameter\r\n * * arc a number from 0 to 1, to create an unclosed ellipsoid based on the fraction of the circumference (latitude) given by the arc value, optional, default 1\r\n * * slice a number from 0 to 1, to create an unclosed ellipsoid based on the fraction of the height (latitude) given by the arc value, optional, default 1\r\n * * sideOrientation optional and takes the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n * * frontUvs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the front side, optional, default vector4 (0, 0, 1, 1)\r\n * * backUVs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the back side, optional, default vector4 (0, 0, 1, 1)\r\n * @returns the VertexData of the ellipsoid\r\n */\r\nexport function CreateSphereVertexData(options: {\r\n    segments?: number;\r\n    diameter?: number;\r\n    diameterX?: number;\r\n    diameterY?: number;\r\n    diameterZ?: number;\r\n    arc?: number;\r\n    slice?: number;\r\n    sideOrientation?: number;\r\n    frontUVs?: Vector4;\r\n    backUVs?: Vector4;\r\n    dedupTopBottomIndices?: boolean;\r\n}): VertexData {\r\n    const segments: number = (options.segments || 32) | 0;\r\n    const diameterX: number = options.diameterX || options.diameter || 1;\r\n    const diameterY: number = options.diameterY || options.diameter || 1;\r\n    const diameterZ: number = options.diameterZ || options.diameter || 1;\r\n    const arc: number = options.arc && (options.arc <= 0 || options.arc > 1) ? 1.0 : options.arc || 1.0;\r\n    const slice: number = options.slice && options.slice <= 0 ? 1.0 : options.slice || 1.0;\r\n    const sideOrientation = options.sideOrientation === 0 ? 0 : options.sideOrientation || VertexData.DEFAULTSIDE;\r\n    const dedupTopBottomIndices = !!options.dedupTopBottomIndices;\r\n\r\n    const radius = new Vector3(diameterX / 2, diameterY / 2, diameterZ / 2);\r\n\r\n    const totalZRotationSteps = 2 + segments;\r\n    const totalYRotationSteps = 2 * totalZRotationSteps;\r\n\r\n    const indices = [];\r\n    const positions = [];\r\n    const normals = [];\r\n    const uvs = [];\r\n\r\n    for (let zRotationStep = 0; zRotationStep <= totalZRotationSteps; zRotationStep++) {\r\n        const normalizedZ = zRotationStep / totalZRotationSteps;\r\n        const angleZ = normalizedZ * Math.PI * slice;\r\n\r\n        for (let yRotationStep = 0; yRotationStep <= totalYRotationSteps; yRotationStep++) {\r\n            const normalizedY = yRotationStep / totalYRotationSteps;\r\n\r\n            const angleY = normalizedY * Math.PI * 2 * arc;\r\n\r\n            const rotationZ = Matrix.RotationZ(-angleZ);\r\n            const rotationY = Matrix.RotationY(angleY);\r\n            const afterRotZ = Vector3.TransformCoordinates(Vector3.Up(), rotationZ);\r\n            const complete = Vector3.TransformCoordinates(afterRotZ, rotationY);\r\n\r\n            const vertex = complete.multiply(radius);\r\n            const normal = complete.divide(radius).normalize();\r\n\r\n            positions.push(vertex.x, vertex.y, vertex.z);\r\n            normals.push(normal.x, normal.y, normal.z);\r\n            uvs.push(normalizedY, CompatibilityOptions.UseOpenGLOrientationForUV ? 1.0 - normalizedZ : normalizedZ);\r\n        }\r\n\r\n        if (zRotationStep > 0) {\r\n            const verticesCount = positions.length / 3;\r\n            for (let firstIndex = verticesCount - 2 * (totalYRotationSteps + 1); firstIndex + totalYRotationSteps + 2 < verticesCount; firstIndex++) {\r\n                if (dedupTopBottomIndices) {\r\n                    if (zRotationStep > 1) {\r\n                        indices.push(firstIndex);\r\n                        indices.push(firstIndex + 1);\r\n                        indices.push(firstIndex + totalYRotationSteps + 1);\r\n                    }\r\n                    if (zRotationStep < totalZRotationSteps || slice < 1.0) {\r\n                        indices.push(firstIndex + totalYRotationSteps + 1);\r\n                        indices.push(firstIndex + 1);\r\n                        indices.push(firstIndex + totalYRotationSteps + 2);\r\n                    }\r\n                } else {\r\n                    indices.push(firstIndex);\r\n                    indices.push(firstIndex + 1);\r\n                    indices.push(firstIndex + totalYRotationSteps + 1);\r\n\r\n                    indices.push(firstIndex + totalYRotationSteps + 1);\r\n                    indices.push(firstIndex + 1);\r\n                    indices.push(firstIndex + totalYRotationSteps + 2);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // Sides\r\n    VertexData._ComputeSides(sideOrientation, positions, indices, normals, uvs, options.frontUVs, options.backUVs);\r\n\r\n    // Result\r\n    const vertexData = new VertexData();\r\n\r\n    vertexData.indices = indices;\r\n    vertexData.positions = positions;\r\n    vertexData.normals = normals;\r\n    vertexData.uvs = uvs;\r\n\r\n    return vertexData;\r\n}\r\n\r\n/**\r\n * Creates a sphere mesh\r\n * * The parameter `diameter` sets the diameter size (float) of the sphere (default 1)\r\n * * You can set some different sphere dimensions, for instance to build an ellipsoid, by using the parameters `diameterX`, `diameterY` and `diameterZ` (all by default have the same value of `diameter`)\r\n * * The parameter `segments` sets the sphere number of horizontal stripes (positive integer, default 32)\r\n * * You can create an unclosed sphere with the parameter `arc` (positive float, default 1), valued between 0 and 1, what is the ratio of the circumference (latitude) : 2 x PI x ratio\r\n * * You can create an unclosed sphere on its height with the parameter `slice` (positive float, default1), valued between 0 and 1, what is the height ratio (longitude)\r\n * * You can also set the mesh side orientation with the values : BABYLON.Mesh.FRONTSIDE (default), BABYLON.Mesh.BACKSIDE or BABYLON.Mesh.DOUBLESIDE\r\n * * If you create a double-sided mesh, you can choose what parts of the texture image to crop and stick respectively on the front and the back sides with the parameters `frontUVs` and `backUVs` (Vector4). Detail here : https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation\r\n * * The mesh can be set to updatable with the boolean parameter `updatable` (default false) if its internal geometry is supposed to change once created\r\n * @param name defines the name of the mesh\r\n * @param options defines the options used to create the mesh\r\n * @param scene defines the hosting scene\r\n * @returns the sphere mesh\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#sphere\r\n */\r\nexport function CreateSphere(\r\n    name: string,\r\n    options: {\r\n        segments?: number;\r\n        diameter?: number;\r\n        diameterX?: number;\r\n        diameterY?: number;\r\n        diameterZ?: number;\r\n        arc?: number;\r\n        slice?: number;\r\n        sideOrientation?: number;\r\n        frontUVs?: Vector4;\r\n        backUVs?: Vector4;\r\n        updatable?: boolean;\r\n    } = {},\r\n    scene: Nullable<Scene> = null\r\n): Mesh {\r\n    const sphere = new Mesh(name, scene);\r\n\r\n    options.sideOrientation = Mesh._GetDefaultSideOrientation(options.sideOrientation);\r\n    sphere._originalBuilderSideOrientation = options.sideOrientation;\r\n\r\n    const vertexData = CreateSphereVertexData(options);\r\n\r\n    vertexData.applyToMesh(sphere, options.updatable);\r\n\r\n    return sphere;\r\n}\r\n\r\n/**\r\n * Class containing static functions to help procedurally build meshes\r\n * @deprecated use CreateSphere directly\r\n */\r\nexport const SphereBuilder = {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    CreateSphere,\r\n};\r\n\r\nVertexData.CreateSphere = CreateSphereVertexData;\r\n\r\nMesh.CreateSphere = (name: string, segments: number, diameter: number, scene?: Scene, updatable?: boolean, sideOrientation?: number): Mesh => {\r\n    const options = {\r\n        segments: segments,\r\n        diameterX: diameter,\r\n        diameterY: diameter,\r\n        diameterZ: diameter,\r\n        sideOrientation: sideOrientation,\r\n        updatable: updatable,\r\n    };\r\n\r\n    return CreateSphere(name, options, scene);\r\n};\r\n"]}