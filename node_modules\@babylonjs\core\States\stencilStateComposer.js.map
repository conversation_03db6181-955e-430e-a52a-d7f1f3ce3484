{"version": 3, "file": "stencilStateComposer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/States/stencilStateComposer.ts"], "names": [], "mappings": "AAEA;;IAEI;AACJ,MAAM,OAAO,oBAAoB;IAuB7B,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,iBAAiB,CAAC;IACtH,CAAC;IAED,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAa;QACzB,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;YACtB,OAAO;SACV;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,KAAa;QAC5B,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;YACzB,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa;QAC7B,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;YAC1B,OAAO;SACV;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,KAAa;QAClC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE;YAC7B,OAAO;SACV;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAa;QACvC,IAAI,IAAI,CAAC,mBAAmB,KAAK,KAAK,EAAE;YACpC,OAAO;SACV;QAED,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAa;QACzB,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;YACtB,OAAO;SACV;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,KAAc;QAC7B,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;YACzB,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED,YAAmB,KAAK,GAAG,IAAI;QAlIrB,wBAAmB,GAAG,KAAK,CAAC;QAC5B,wBAAmB,GAAG,KAAK,CAAC;QAC5B,wBAAmB,GAAG,KAAK,CAAC;QAC5B,sBAAiB,GAAG,KAAK,CAAC;QAiB7B,yBAAoB,GAAG,KAAK,CAAC;QA+GhC,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;IACL,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QAEjC,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC;QAE5B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAClC,CAAC;IAEM,KAAK,CAAC,EAA0B;QACnC,IAAI,CAAC,EAAE,EAAE;YACL,OAAO;SACV;QAED,MAAM,sBAAsB,GAAG,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC;QAE7F,IAAI,CAAC,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QACnG,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QAC1F,IAAI,CAAC,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QACnG,IAAI,CAAC,QAAQ,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;QACtG,IAAI,CAAC,aAAa,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;QACrH,IAAI,CAAC,WAAW,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;QAC/G,IAAI,CAAC,kBAAkB,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;QACpI,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QAE1F,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QAED,eAAe;QACf,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;aAC9B;iBAAM;gBACH,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;aAC/B;YACD,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;SACpC;QAED,eAAe;QACf,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;SACpC;QAED,eAAe;QACf,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvD,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;SACpC;QAED,aAAa;QACb,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC5E,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;SAClC;IACL,CAAC;CACJ", "sourcesContent": ["import type { IStencilState } from \"./IStencilState\";\r\n\r\n/**\r\n * @internal\r\n **/\r\nexport class StencilStateComposer {\r\n    protected _isStencilTestDirty = false;\r\n    protected _isStencilMaskDirty = false;\r\n    protected _isStencilFuncDirty = false;\r\n    protected _isStencilOpDirty = false;\r\n\r\n    protected _enabled: boolean;\r\n\r\n    protected _mask: number;\r\n\r\n    protected _func: number;\r\n    protected _funcRef: number;\r\n    protected _funcMask: number;\r\n\r\n    protected _opStencilFail: number;\r\n    protected _opDepthFail: number;\r\n    protected _opStencilDepthPass: number;\r\n\r\n    public stencilGlobal: IStencilState;\r\n    public stencilMaterial: IStencilState | undefined;\r\n\r\n    public useStencilGlobalOnly = false;\r\n\r\n    public get isDirty(): boolean {\r\n        return this._isStencilTestDirty || this._isStencilMaskDirty || this._isStencilFuncDirty || this._isStencilOpDirty;\r\n    }\r\n\r\n    public get func(): number {\r\n        return this._func;\r\n    }\r\n\r\n    public set func(value: number) {\r\n        if (this._func === value) {\r\n            return;\r\n        }\r\n\r\n        this._func = value;\r\n        this._isStencilFuncDirty = true;\r\n    }\r\n\r\n    public get funcRef(): number {\r\n        return this._funcRef;\r\n    }\r\n\r\n    public set funcRef(value: number) {\r\n        if (this._funcRef === value) {\r\n            return;\r\n        }\r\n\r\n        this._funcRef = value;\r\n        this._isStencilFuncDirty = true;\r\n    }\r\n\r\n    public get funcMask(): number {\r\n        return this._funcMask;\r\n    }\r\n\r\n    public set funcMask(value: number) {\r\n        if (this._funcMask === value) {\r\n            return;\r\n        }\r\n\r\n        this._funcMask = value;\r\n        this._isStencilFuncDirty = true;\r\n    }\r\n\r\n    public get opStencilFail(): number {\r\n        return this._opStencilFail;\r\n    }\r\n\r\n    public set opStencilFail(value: number) {\r\n        if (this._opStencilFail === value) {\r\n            return;\r\n        }\r\n\r\n        this._opStencilFail = value;\r\n        this._isStencilOpDirty = true;\r\n    }\r\n\r\n    public get opDepthFail(): number {\r\n        return this._opDepthFail;\r\n    }\r\n\r\n    public set opDepthFail(value: number) {\r\n        if (this._opDepthFail === value) {\r\n            return;\r\n        }\r\n\r\n        this._opDepthFail = value;\r\n        this._isStencilOpDirty = true;\r\n    }\r\n\r\n    public get opStencilDepthPass(): number {\r\n        return this._opStencilDepthPass;\r\n    }\r\n\r\n    public set opStencilDepthPass(value: number) {\r\n        if (this._opStencilDepthPass === value) {\r\n            return;\r\n        }\r\n\r\n        this._opStencilDepthPass = value;\r\n        this._isStencilOpDirty = true;\r\n    }\r\n\r\n    public get mask(): number {\r\n        return this._mask;\r\n    }\r\n\r\n    public set mask(value: number) {\r\n        if (this._mask === value) {\r\n            return;\r\n        }\r\n\r\n        this._mask = value;\r\n        this._isStencilMaskDirty = true;\r\n    }\r\n\r\n    public get enabled(): boolean {\r\n        return this._enabled;\r\n    }\r\n\r\n    public set enabled(value: boolean) {\r\n        if (this._enabled === value) {\r\n            return;\r\n        }\r\n\r\n        this._enabled = value;\r\n        this._isStencilTestDirty = true;\r\n    }\r\n\r\n    public constructor(reset = true) {\r\n        if (reset) {\r\n            this.reset();\r\n        }\r\n    }\r\n\r\n    public reset() {\r\n        this.stencilMaterial = undefined;\r\n\r\n        this.stencilGlobal?.reset();\r\n\r\n        this._isStencilTestDirty = true;\r\n        this._isStencilMaskDirty = true;\r\n        this._isStencilFuncDirty = true;\r\n        this._isStencilOpDirty = true;\r\n    }\r\n\r\n    public apply(gl?: WebGLRenderingContext) {\r\n        if (!gl) {\r\n            return;\r\n        }\r\n\r\n        const stencilMaterialEnabled = !this.useStencilGlobalOnly && !!this.stencilMaterial?.enabled;\r\n\r\n        this.enabled = stencilMaterialEnabled ? this.stencilMaterial!.enabled : this.stencilGlobal.enabled;\r\n        this.func = stencilMaterialEnabled ? this.stencilMaterial!.func : this.stencilGlobal.func;\r\n        this.funcRef = stencilMaterialEnabled ? this.stencilMaterial!.funcRef : this.stencilGlobal.funcRef;\r\n        this.funcMask = stencilMaterialEnabled ? this.stencilMaterial!.funcMask : this.stencilGlobal.funcMask;\r\n        this.opStencilFail = stencilMaterialEnabled ? this.stencilMaterial!.opStencilFail : this.stencilGlobal.opStencilFail;\r\n        this.opDepthFail = stencilMaterialEnabled ? this.stencilMaterial!.opDepthFail : this.stencilGlobal.opDepthFail;\r\n        this.opStencilDepthPass = stencilMaterialEnabled ? this.stencilMaterial!.opStencilDepthPass : this.stencilGlobal.opStencilDepthPass;\r\n        this.mask = stencilMaterialEnabled ? this.stencilMaterial!.mask : this.stencilGlobal.mask;\r\n\r\n        if (!this.isDirty) {\r\n            return;\r\n        }\r\n\r\n        // Stencil test\r\n        if (this._isStencilTestDirty) {\r\n            if (this.enabled) {\r\n                gl.enable(gl.STENCIL_TEST);\r\n            } else {\r\n                gl.disable(gl.STENCIL_TEST);\r\n            }\r\n            this._isStencilTestDirty = false;\r\n        }\r\n\r\n        // Stencil mask\r\n        if (this._isStencilMaskDirty) {\r\n            gl.stencilMask(this.mask);\r\n            this._isStencilMaskDirty = false;\r\n        }\r\n\r\n        // Stencil func\r\n        if (this._isStencilFuncDirty) {\r\n            gl.stencilFunc(this.func, this.funcRef, this.funcMask);\r\n            this._isStencilFuncDirty = false;\r\n        }\r\n\r\n        // Stencil op\r\n        if (this._isStencilOpDirty) {\r\n            gl.stencilOp(this.opStencilFail, this.opDepthFail, this.opStencilDepthPass);\r\n            this._isStencilOpDirty = false;\r\n        }\r\n    }\r\n}\r\n"]}