/**
 * Wrapper class for promise with external resolve and reject.
 */
export declare class Deferred<T> {
    /**
     * The promise associated with this deferred object.
     */
    readonly promise: Promise<T>;
    private _resolve;
    private _reject;
    /**
     * The resolve method of the promise associated with this deferred object.
     */
    get resolve(): (value: T | PromiseLike<T>) => void;
    /**
     * The reject method of the promise associated with this deferred object.
     */
    get reject(): (reason?: any) => void;
    /**
     * Constructor for this deferred object.
     */
    constructor();
}
