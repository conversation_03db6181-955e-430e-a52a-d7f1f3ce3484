{"version": 3, "file": "physicsEngineComponent.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Physics/v1/physicsEngineComponent.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAGzD,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AA+C9C,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,iBAAiB,EAAE;IAC7D,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IACD,GAAG,EAAE,UAA8B,KAAgC;QAC/D,IAAI,IAAI,CAAC,gBAAgB,KAAK,KAAK,EAAE;YACjC,OAAO;SACV;QACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACjE;QAED,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAE9B,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC7D,UAAU;gBACV,IAAI,IAAI,CAAC,eAAe,EAAE;oBACtB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAC,iBAAiB,CAAC,CAAC;oBAChD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;iBAC/B;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH;;;;GAIG;AACH,YAAY,CAAC,SAAS,CAAC,kBAAkB,GAAG;IACxC,OAAO,IAAI,CAAC,eAAe,CAAC;AAChC,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,YAAY,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,KAAc,EAAE,YAAqB;IACjF,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;QACvB,OAAO,IAAI,CAAC;KACf;IACD,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;IACvD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACH,YAAY,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,SAAe,EAAE,MAAe,EAAE,MAAe,EAAE,OAAa;IAClH,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE;QACrD,OAAO,IAAI,CAAC;KACf;IACD,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,eAAe,EAAE,YAAY,CAAC,UAAU,EAAE;QACjF,SAAS,EAAE,MAAM;QACjB,cAAc,EAAE,MAAM;QACtB,YAAY,EAAE,OAAO;KACxB,CAAC,CAAC;IACH,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Node } from \"../../node\";\r\nimport type { PhysicsImpostor } from \"./physicsImpostor\";\r\nimport { PhysicsJoint } from \"./physicsJoint\";\r\n\r\ndeclare module \"../../Meshes/abstractMesh\" {\r\n    /**\r\n     *\r\n     */\r\n    export interface AbstractMesh {\r\n        /** @internal */\r\n        _physicsImpostor: Nullable<PhysicsImpostor>;\r\n\r\n        /**\r\n         * Gets or sets impostor used for physic simulation\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/physics\r\n         */\r\n        physicsImpostor: Nullable<PhysicsImpostor>;\r\n\r\n        /**\r\n         * Gets the current physics impostor\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/physics\r\n         * @returns a physics impostor or null\r\n         */\r\n        getPhysicsImpostor(): Nullable<PhysicsImpostor>;\r\n\r\n        /** Apply a physic impulse to the mesh\r\n         * @param force defines the force to apply\r\n         * @param contactPoint defines where to apply the force\r\n         * @returns the current mesh\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n         */\r\n        applyImpulse(force: Vector3, contactPoint: Vector3): AbstractMesh;\r\n\r\n        /**\r\n         * Creates a physic joint between two meshes\r\n         * @param otherMesh defines the other mesh to use\r\n         * @param pivot1 defines the pivot to use on this mesh\r\n         * @param pivot2 defines the pivot to use on the other mesh\r\n         * @param options defines additional options (can be plugin dependent)\r\n         * @returns the current mesh\r\n         * @see https://www.babylonjs-playground.com/#0BS5U0#0\r\n         */\r\n        setPhysicsLinkWith(otherMesh: Mesh, pivot1: Vector3, pivot2: Vector3, options?: any): AbstractMesh;\r\n\r\n        /** @internal */\r\n        _disposePhysicsObserver: Nullable<Observer<Node>>;\r\n    }\r\n}\r\n\r\nObject.defineProperty(AbstractMesh.prototype, \"physicsImpostor\", {\r\n    get: function (this: AbstractMesh) {\r\n        return this._physicsImpostor;\r\n    },\r\n    set: function (this: AbstractMesh, value: Nullable<PhysicsImpostor>) {\r\n        if (this._physicsImpostor === value) {\r\n            return;\r\n        }\r\n        if (this._disposePhysicsObserver) {\r\n            this.onDisposeObservable.remove(this._disposePhysicsObserver);\r\n        }\r\n\r\n        this._physicsImpostor = value;\r\n\r\n        if (value) {\r\n            this._disposePhysicsObserver = this.onDisposeObservable.add(() => {\r\n                // Physics\r\n                if (this.physicsImpostor) {\r\n                    this.physicsImpostor.dispose(/*!doNotRecurse*/);\r\n                    this.physicsImpostor = null;\r\n                }\r\n            });\r\n        }\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\n/**\r\n * Gets the current physics impostor\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics\r\n * @returns a physics impostor or null\r\n */\r\nAbstractMesh.prototype.getPhysicsImpostor = function (): Nullable<PhysicsImpostor> {\r\n    return this.physicsImpostor;\r\n};\r\n\r\n/**\r\n * Apply a physic impulse to the mesh\r\n * @param force defines the force to apply\r\n * @param contactPoint defines where to apply the force\r\n * @returns the current mesh\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nAbstractMesh.prototype.applyImpulse = function (force: Vector3, contactPoint: Vector3): AbstractMesh {\r\n    if (!this.physicsImpostor) {\r\n        return this;\r\n    }\r\n    this.physicsImpostor.applyImpulse(force, contactPoint);\r\n    return this;\r\n};\r\n\r\n/**\r\n * Creates a physic joint between two meshes\r\n * @param otherMesh defines the other mesh to use\r\n * @param pivot1 defines the pivot to use on this mesh\r\n * @param pivot2 defines the pivot to use on the other mesh\r\n * @param options defines additional options (can be plugin dependent)\r\n * @returns the current mesh\r\n * @see https://www.babylonjs-playground.com/#0BS5U0#0\r\n */\r\nAbstractMesh.prototype.setPhysicsLinkWith = function (otherMesh: Mesh, pivot1: Vector3, pivot2: Vector3, options?: any): AbstractMesh {\r\n    if (!this.physicsImpostor || !otherMesh.physicsImpostor) {\r\n        return this;\r\n    }\r\n    this.physicsImpostor.createJoint(otherMesh.physicsImpostor, PhysicsJoint.HingeJoint, {\r\n        mainPivot: pivot1,\r\n        connectedPivot: pivot2,\r\n        nativeParams: options,\r\n    });\r\n    return this;\r\n};\r\n"]}