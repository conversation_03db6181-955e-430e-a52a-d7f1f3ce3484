{"version": 3, "file": "postProcess.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/postProcess.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAG/C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEjD,OAAO,+BAA+B,CAAC;AAEvC,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C,OAAO,2CAA2C,CAAC;AAEnD,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,oBAAoB,CAAC;AAClE,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AACvE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAGvD,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAkH7D;;;GAGG;AACH,MAAM,OAAO,WAAW;IAMpB;;;;OAIG;IACI,MAAM,CAAC,4BAA4B,CAAC,eAAiC,EAAE,0BAAkE;QAC5I,IAAI,CAAC,0BAA0B,EAAE;YAC7B,OAAO,WAAW,CAAC,2BAA2B,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC;YACtE,OAAO;SACV;QAED,WAAW,CAAC,2BAA2B,CAAC,eAAe,IAAI,EAAE,CAAC,GAAG,0BAA0B,CAAC;IAChG,CAAC;IAEO,MAAM,CAAC,wBAAwB,CAAC,eAAuB;QAC3D,OAAO,WAAW,CAAC,2BAA2B,CAAC,eAAe,CAAC,IAAI,WAAW,CAAC,2BAA2B,CAAC,EAAE,CAAC,CAAC;IACnH,CAAC;IAgHD;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,CAAS;QACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,cAAc,CAAC,CAAC;QAEnE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC/B,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACP,CAAC;IA8DD;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAUD;;OAEG;IACH,IAAW,UAAU,CAAC,QAA4C;QAC9D,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC9D;QACD,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SACtE;IACL,CAAC;IAQD;;OAEG;IACH,IAAW,aAAa,CAAC,QAA4C;QACjE,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;SACpE;QACD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC7E,CAAC;IAQD;;OAEG;IACH,IAAW,OAAO,CAAC,QAAkC;QACjD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACxD;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACjE,CAAC;IAQD;;OAEG;IACH,IAAW,cAAc,CAAC,QAAkC;QACxD,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACtE;QACD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/E,CAAC;IAQD;;OAEG;IACH,IAAW,aAAa,CAAC,QAAiC;QACtD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;SACpE;QACD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC7E,CAAC;IAED;;;OAGG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC;IAED,IAAW,YAAY,CAAC,KAA0B;QAC9C,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,0BAA0B;QAC7B,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;IACL,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,IAAW,SAAS;QAChB,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClC,OAAO,IAAI,CAAC,2BAA2B,CAAC,SAAS,CAAC;SACrD;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;SACjH;QAED,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAgDD,gBAAgB;IAChB,YACI,IAAY,EACZ,WAAmB,EACnB,UAAoD,EACpD,QAA6B,EAC7B,KAAmC,EACnC,MAAyB,EACzB,eAAuB,SAAS,CAAC,4BAA4B,EAC7D,MAAe,EACf,QAAkB,EAClB,UAA4B,IAAI,EAChC,cAAsB,SAAS,CAAC,wBAAwB,EACxD,YAAoB,aAAa,EACjC,eAAqB,EACrB,gBAAgB,GAAG,KAAK,EACxB,aAAa,GAAG,SAAS,CAAC,kBAAkB,EAC5C,cAAc,GAAG,cAAc,CAAC,IAAI;QA1ZxC,gBAAgB;QACT,qBAAgB,GAA4B,IAAI,CAAC;QAgCxD;;WAEG;QAEI,UAAK,GAAG,CAAC,CAAC,CAAC;QAElB;;WAEG;QAEI,WAAM,GAAG,CAAC,CAAC,CAAC;QAEnB;;WAEG;QACI,uBAAkB,GAA2B,IAAI,CAAC;QAEzD;;;WAGG;QACI,mBAAc,GAAkC,IAAI,CAAC;QAY5D;;;WAGG;QAEI,cAAS,GAAG,IAAI,CAAC;QACxB;;;WAGG;QAEI,8BAAyB,GAAG,KAAK,CAAC;QACzC;;WAEG;QAEI,cAAS,GAAG,SAAS,CAAC,aAAa,CAAC;QAM3C;;WAEG;QACI,eAAU,GAAgB,EAAE,CAAC;QAEpC;;;WAGG;QAEI,2BAAsB,GAAG,KAAK,CAAC;QAEtC;;WAEG;QAEI,4BAAuB,GAAG,IAAI,CAAC;QAQtC;;;;;;;;;WASG;QAEI,cAAS,GAAG,SAAS,CAAC,eAAe,CAAC;QAC7C;;WAEG;QAEI,mBAAc,GAAG,KAAK,CAAC;QAGtB,aAAQ,GAAG,CAAC,CAAC;QAiBrB;;WAEG;QAEI,gCAA2B,GAAG,KAAK,CAAC;QAOnC,cAAS,GAAG,KAAK,CAAC;QAClB,cAAS,GAAG,CAAC,CAAC;QAKtB;;;;WAIG;QACI,kCAA6B,GAAG,KAAK,CAAC;QAE7C;;;WAGG;QACI,cAAS,GAAG,IAAI,UAAU,CAAsB,CAAC,CAAC,CAAC;QAC1D;;;WAGG;QACK,kBAAa,GAAmB,EAAE,CAAC;QAC3C;;;WAGG;QACI,6BAAwB,GAAG,CAAC,CAAC;QAQ5B,gBAAW,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAGhC,eAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAmBpC,SAAS;QAET;;WAEG;QACI,yBAAoB,GAAG,IAAI,UAAU,EAAU,CAAC;QAevD;;WAEG;QACI,4BAAuB,GAAG,IAAI,UAAU,EAAe,CAAC;QAa/D;;WAEG;QACI,sBAAiB,GAAG,IAAI,UAAU,EAAU,CAAC;QAapD;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAU,CAAC;QAa3D;;WAEG;QACI,4BAAuB,GAAG,IAAI,UAAU,EAAU,CAAC;QA6HtD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,IAAI,GAA+C,CAAC,CAAC;QACzD,IAAI,cAAc,GAAuB,IAAI,CAAC;QAC9C,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAC1C,MAAM,OAAO,GAAG,UAAU,CAAC;YAC3B,UAAU,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC;YACtC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC;YACpC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;YACzB,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC;YAChC,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,SAAS,CAAC,4BAA4B,CAAC;YAC9E,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YACxB,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAC5B,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC;YAClC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,SAAS,CAAC,wBAAwB,CAAC;YACxE,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,aAAa,CAAC;YAC/C,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;YAC1C,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,IAAI,KAAK,CAAC;YACrD,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,SAAS,CAAC,kBAAkB,CAAC;YACtE,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC,IAAI,CAAC;YAC/D,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC;SACnD;aAAM,IAAI,KAAK,EAAE;YACd,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC3B,IAAI,GAAG,KAAK,CAAC;aAChB;iBAAM;gBACH,IAAI,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,KAAM,EAAE,MAAM,EAAE,KAAK,CAAC,MAAO,EAAE,CAAC;aACzD;SACJ;QAED,IAAI,MAAM,IAAI,IAAI,EAAE;YAChB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAEvC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;SAC7C;aAAM,IAAI,MAAM,EAAE;YACf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,wBAAwB,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC;QACrG,IAAI,CAAC,SAAS,GAAG,QAAQ,IAAI,KAAK,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QAEtC,IAAI,CAAC,SAAS,GAAG,QAAQ,IAAI,EAAE,CAAC;QAChC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEtC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI,EAAE,CAAC;QAEpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,cAAc,IAAI,EAAE,CAAC;QAE5C,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAElD,IAAI,CAAC,gBAAgB,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;SAC9B;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,WAAwB;QAC3C,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,IAAI,CAAC,2BAA2B,GAAG,WAAW,CAAC;QAE/C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE;YAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,CAAsB,CAAC,CAAC,CAAC;SAC3D;QAED,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;IAC5C,CAAC;IAED;;;;;;;;;;OAUG;IACI,YAAY,CACf,UAA4B,IAAI,EAChC,WAA+B,IAAI,EACnC,WAA+B,IAAI,EACnC,eAAqB,EACrB,UAAqC,EACrC,OAAkD,EAClD,SAAkB,EAClB,WAAoB;QAEpB,MAAM,0BAA0B,GAAG,WAAW,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnF,IAAI,0BAA0B,EAAE,oBAAoB,EAAE;YAClD,MAAM,WAAW,GAAG,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;YAC5C,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;YAEtC,MAAM,WAAW,GAAG,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;YAC5C,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;YAEpC,OAAO,GAAG,0BAA0B,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;YACxG,QAAQ,GAAG,WAAW,CAAC;YACvB,QAAQ,GAAG,WAAW,CAAC;SAC1B;QACD,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC;QACnC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAChD,EAAE,MAAM,EAAE,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,IAAI,IAAI,CAAC,YAAY,EAAE,EACpF;YACI,UAAU,EAAE,CAAC,UAAU,CAAC;YACxB,aAAa,EAAE,QAAQ,IAAI,IAAI,CAAC,WAAW;YAC3C,mBAAmB,EAAE,IAAI,CAAC,eAAe;YACzC,QAAQ,EAAE,QAAQ,IAAI,IAAI,CAAC,SAAS;YACpC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACxC,SAAS,EAAE,IAAI;YACf,UAAU,EAAE,UAAU,IAAI,IAAI;YAC9B,OAAO,EAAE,OAAO,IAAI,IAAI;YACxB,eAAe,EAAE,eAAe,IAAI,IAAI,CAAC,gBAAgB;YACzD,wBAAwB,EAAE,0BAA0B,EAAE,wBAAwB;gBAC1E,CAAC,CAAC,CAAC,UAAkB,EAAE,IAAY,EAAE,EAAE,CAAC,0BAA2B,CAAC,wBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC;gBAC1H,CAAC,CAAC,IAAI;YACV,gBAAgB,EAAE,0BAA0B,EAAE,gBAAgB;gBAC1D,CAAC,CAAC,CAAC,UAAkB,EAAE,IAAY,EAAE,EAAE,CAAC,0BAA2B,CAAC,gBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC;gBAClH,CAAC,CAAC,IAAI;YACV,cAAc,EAAE,IAAI,CAAC,eAAe;SACvC,EACD,IAAI,CAAC,OAAO,CACf,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,UAAU;QACb,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,8EAA8E;IACvE,gBAAgB;QACnB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IACpB,CAAC;IAEO,0BAA0B,CAAC,WAA8C,EAAE,cAA2C,EAAE,OAAO,GAAG,CAAC;QACvI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,IACI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW,CAAC,KAAK;gBACzD,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM;gBAC3D,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,kBAAkB,KAAK,OAAO;gBACpD,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,KAAK,cAAc,CAAC,mBAAmB;gBACzF,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,KAAK,cAAc,CAAC,OAAO,EAClE;gBACE,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;aACxC;SACJ;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAChF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,kBAAkB,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAE7F,OAAO,GAAG,CAAC;IACf,CAAC;IAEO,kBAAkB;QACtB,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC;QAEvC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACrD,IAAI,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,GAAG,EAAE;gBAChE,IAAI,aAAa,GAAG,KAAK,CAAC;gBAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC5C,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE;wBAC1D,aAAa,GAAG,IAAI,CAAC;wBACrB,MAAM;qBACT;iBACJ;gBAED,IAAI,CAAC,aAAa,EAAE;oBAChB,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;oBACxC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACnC;aACJ;SACJ;IACL,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,KAAa,EAAE,MAAc,EAAE,SAA2B,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,iBAAiB,GAAG,KAAK;QACxH,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;SAC1B;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,OAAO,GAAG,IAAI,CAAC;QACnB,IAAI,MAAM,EAAE;YACR,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnD,IAAI,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;oBACnC,OAAO,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;oBACnC,MAAM;iBACT;aACJ;SACJ;QAED,MAAM,WAAW,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;QAC/D,MAAM,cAAc,GAAG;YACnB,eAAe,EAAE,WAAW;YAC5B,mBAAmB,EAAE,iBAAiB,IAAI,OAAO,KAAK,IAAI;YAC1D,qBAAqB,EAAE,CAAC,iBAAiB,IAAI,OAAO,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe;YAC9F,YAAY,EAAE,IAAI,CAAC,wBAAwB;YAC3C,IAAI,EAAE,IAAI,CAAC,YAAY;YACvB,MAAM,EAAE,IAAI,CAAC,cAAc;YAC3B,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,KAAK,EAAE,iBAAiB,GAAG,IAAI,CAAC,IAAI;SACvC,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;QAErF,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;SACxF;QAED,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAEpE,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAEO,UAAU;QACd,IAAI,MAA2B,CAAC;QAEhC,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClC,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,YAAY,CAAC;SAC1D;aAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAClC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAEnC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;YAC7C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;SAClD;aAAM;YACH,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;YAE3B,IAAI,KAAK,CAAC;YACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChD,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,MAAM,EAAE;oBAC1C,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAC9B,MAAM;iBACT;aACJ;YAED,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC;aAC3C;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;;OAOG;IACI,QAAQ,CAAC,MAAwB,EAAE,gBAA2C,IAAI,EAAE,iBAA2B;QAClH,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;QAEhC,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,cAAc,CAAC;QAEhD,MAAM,aAAa,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,GAAW,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC9H,MAAM,cAAc,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,GAAW,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAEjI,IAAI,YAAY,GAAwB,IAAI,CAAC,QAAS,CAAC,KAAK,IAAI,aAAa,CAAC;QAC9E,IAAI,aAAa,GAAwB,IAAI,CAAC,QAAS,CAAC,MAAM,IAAI,cAAc,CAAC;QAEjF,MAAM,WAAW,GACb,IAAI,CAAC,wBAAwB,KAAK,SAAS,CAAC,sBAAsB;YAClE,IAAI,CAAC,wBAAwB,KAAK,SAAS,CAAC,uBAAuB;YACnE,IAAI,CAAC,wBAAwB,KAAK,SAAS,CAAC,qBAAqB,CAAC;QAEtE,IAAI,MAAM,GAAkC,IAAI,CAAC;QAEjD,IAAI,CAAC,IAAI,CAAC,2BAA2B,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACjE,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBAClC,MAAM,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;gBAE/C,IAAI,eAAe,EAAE;oBACjB,YAAY,IAAI,eAAe,CAAC,KAAK,CAAC;oBACtC,aAAa,IAAI,eAAe,CAAC,MAAM,CAAC;iBAC3C;aACJ;YAED,IAAI,WAAW,IAAI,IAAI,CAAC,cAAc,EAAE;gBACpC,IAAI,CAAsB,IAAI,CAAC,QAAS,CAAC,KAAK,EAAE;oBAC5C,YAAY,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;iBACzH;gBAED,IAAI,CAAsB,IAAI,CAAC,QAAS,CAAC,MAAM,EAAE;oBAC7C,aAAa,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;iBAC5H;aACJ;YAED,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,IAAI,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE;gBAC/F,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;aACpF;YAED,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC/B,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;oBAClC,IAAI,CAAC,OAAO,CAAC,oCAAoC,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;iBAC5E;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,EAAE,CAAC;SACpB;QAED,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;SAC9B;QAED,6FAA6F;QAC7F,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,aAAa,GAAG,YAAY,EAAE,cAAc,GAAG,aAAa,CAAC,CAAC;YAC9F,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACxG;aAAM;YACH,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;SAC/F;QAED,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,gBAAgB,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC;QAErE,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAElD,QAAQ;QACR,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,aAAa,IAAI,IAAI,CAAC,yBAAyB,CAAC,EAAE;YAClG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,2BAA2B,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;SAC3H;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;SAC3E;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,WAAW,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClC,OAAO,IAAI,CAAC,2BAA2B,CAAC,WAAW,CAAC;SACvD;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;SAC7E;QACD,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,KAAK,CAAC;IACxD,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,QAAQ;QACR,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;YACtC,OAAO,IAAI,CAAC;SACf;QAED,SAAS;QACT,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAElC,QAAQ;QACR,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,SAAS,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;SAClI;QAED,0FAA0F;QAC1F,IAAI,MAA2B,CAAC;QAChC,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClC,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,YAAY,CAAC;SAC1D;aAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAClC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;SACtC;aAAM;YACH,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;SAC9B;QAED,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE;YACrC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,gBAAgB,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;SAC5E;QAED,aAAa;QACb,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/D,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAEjE,WAAW,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,kBAAkB,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAE3G,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;IACpC,CAAC;IAEO,gBAAgB;QACpB,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC/D,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,OAAO;SACV;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAEO,oBAAoB;QACxB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACrD,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;SAC3C;QAED,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,eAAgC;QACtD,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClC,IAAI,CAAC,2BAA2B,GAAG,eAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC5G,IAAI,CAAC,2BAA2B,CAAC,OAAO,GAAG,IAAI,CAAC;YAChD,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,MAAe;QAC1B,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;QAEhC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,IAAI,KAAK,CAAC;QACV,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBACd,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAC9C;SACJ;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAChE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACZ,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACxD;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;QAED,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC/C;QAED,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QACD,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAE/B,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,KAAK,KAAK,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;YACjD,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;YAC7D,IAAI,gBAAgB,EAAE;gBAClB,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;aACvC;SACJ;QAED,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;IACzC,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAC7E,mBAAmB,CAAC,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAClE,mBAAmB,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QACzD,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAC9C,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;QACpD,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;QACpD,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAClD,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAC9C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACvD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QACxD,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAChD,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE5D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC7C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC;QAEpC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAEvE,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,IAAI,CAAC;SACf;QAED,MAAM,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAChE,MAAM,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACtE,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC1D,MAAM,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACxE,MAAM,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QAEtE,MAAM,CAAC,2BAA2B,GAAG,IAAI,CAAC,2BAA2B,CAAC;QAEtE,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,iBAAsB,EAAE,KAAY,EAAE,OAAe;QACrE,MAAM,eAAe,GAAG,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAE/D,IAAI,CAAC,eAAe,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;YAC7C,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9E,OAAO,eAAe,CAAC,MAAM,CAAC,iBAAiB,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,iBAAsB,EAAE,YAAoB,EAAE,KAAY,EAAE,OAAe;QAC5F,OAAO,mBAAmB,CAAC,KAAK,CAC5B,GAAG,EAAE;YACD,OAAO,IAAI,WAAW,CAClB,iBAAiB,CAAC,IAAI,EACtB,iBAAiB,CAAC,WAAW,EAC7B,iBAAiB,CAAC,UAAU,EAC5B,iBAAiB,CAAC,QAAQ,EAC1B,iBAAiB,CAAC,OAAO,EACzB,YAAY,EACZ,iBAAiB,CAAC,wBAAwB,EAC1C,iBAAiB,CAAC,OAAO,EACzB,iBAAiB,CAAC,QAAQ,EAC1B,iBAAiB,CAAC,OAAO,EACzB,iBAAiB,CAAC,WAAW,EAC7B,iBAAiB,CAAC,SAAS,EAC3B,iBAAiB,CAAC,eAAe,EACjC,KAAK,EACL,iBAAiB,CAAC,aAAa,CAClC,CAAC;QACN,CAAC,EACD,iBAAiB,EACjB,KAAK,EACL,OAAO,CACV,CAAC;IACN,CAAC;;AA/hCc,uCAA2B,GAAyE,EAAE,AAA3E,CAA4E;AAwB/G;IADN,SAAS,EAAE;6CACY;AAIjB;IADN,SAAS,EAAE;yCACQ;AAMb;IADN,SAAS,EAAE;0CACM;AAMX;IADN,SAAS,EAAE;2CACO;AAiBZ;IADN,SAAS,EAAE;6DAC4B;AAKjC;IADN,iBAAiB,EAAE;+CACM;AAMnB;IADN,SAAS,EAAE;8CACY;AAMjB;IADN,SAAS,EAAE;8DAC6B;AAKlC;IADN,SAAS,EAAE;8CAC+B;AAKpC;IADN,SAAS,EAAE;mDACkB;AAWvB;IADN,SAAS,EAAE;2DAC0B;AAM/B;IADN,SAAS,EAAE;4DAC0B;AAmB/B;IADN,SAAS,EAAE;8CACiC;AAKtC;IADN,SAAS,EAAE;mDACkB;AAGtB;IADP,SAAS,CAAC,SAAS,CAAC;6CACA;AAqBd;IADN,SAAS,EAAE;gEAC+B;AA64B/C,aAAa,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { SmartArray } from \"../Misc/smartArray\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Vector2 } from \"../Maths/math.vector\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { RenderTargetCreationOptions } from \"../Materials/Textures/textureCreationOptions\";\r\nimport \"../Shaders/postprocess.vertex\";\r\nimport type { IInspectable } from \"../Misc/iInspectable\";\r\nimport { Engine } from \"../Engines/engine\";\r\nimport type { Color4 } from \"../Maths/math.color\";\r\n\r\nimport \"../Engines/Extensions/engine.renderTarget\";\r\nimport type { NodeMaterial } from \"../Materials/Node/nodeMaterial\";\r\nimport { serialize, serializeAsColor4 } from \"../Misc/decorators\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\nimport { GetClass, RegisterClass } from \"../Misc/typeStore\";\r\nimport { DrawWrapper } from \"../Materials/drawWrapper\";\r\nimport type { AbstractScene } from \"../abstractScene\";\r\nimport type { RenderTargetWrapper } from \"../Engines/renderTargetWrapper\";\r\nimport { ShaderLanguage } from \"../Materials/shaderLanguage\";\r\n\r\nimport type { Scene } from \"../scene\";\r\nimport type { InternalTexture } from \"../Materials/Textures/internalTexture\";\r\nimport type { Animation } from \"../Animations/animation\";\r\nimport type { PrePassRenderer } from \"../Rendering/prePassRenderer\";\r\nimport type { PrePassEffectConfiguration } from \"../Rendering/prePassEffectConfiguration\";\r\n\r\n/**\r\n * Allows for custom processing of the shader code used by a post process\r\n */\r\nexport type PostProcessCustomShaderCodeProcessing = {\r\n    /**\r\n     * If provided, will be called two times with the vertex and fragment code so that this code can be updated after the #include have been processed\r\n     */\r\n    processCodeAfterIncludes?: (postProcessName: string, shaderType: string, code: string) => string;\r\n    /**\r\n     * If provided, will be called two times with the vertex and fragment code so that this code can be updated before it is compiled by the GPU\r\n     */\r\n    processFinalCode?: (postProcessName: string, shaderType: string, code: string) => string;\r\n    /**\r\n     * If provided, will be called before creating the effect to collect additional custom bindings (defines, uniforms, samplers)\r\n     */\r\n    defineCustomBindings?: (postProcessName: string, defines: Nullable<string>, uniforms: string[], samplers: string[]) => Nullable<string>;\r\n    /**\r\n     * If provided, will be called when binding inputs to the shader code to allow the user to add custom bindings\r\n     */\r\n    bindCustomBindings?: (postProcessName: string, effect: Effect) => void;\r\n};\r\n\r\n/**\r\n * Options for the PostProcess constructor\r\n */\r\nexport type PostProcessOptions = {\r\n    /**\r\n     * The width of the texture created for this post process.\r\n     * This parameter (and height) is only used when passing a value for the 5th parameter (options) to the PostProcess constructor function.\r\n     * If you use a PostProcessOptions for the 3rd parameter of the constructor, size is used instead of width and height.\r\n     */\r\n    width?: number;\r\n    /**\r\n     * The height of the texture created for this post process.\r\n     * This parameter (and width) is only used when passing a value for the 5th parameter (options) to the PostProcess constructor function.\r\n     * If you use a PostProcessOptions for the 3rd parameter of the constructor, size is used instead of width and height.\r\n     */\r\n    height?: number;\r\n\r\n    /**\r\n     * The list of uniforms used in the shader (if any)\r\n     */\r\n    uniforms?: Nullable<string[]>;\r\n    /**\r\n     * The list of samplers used in the shader (if any)\r\n     */\r\n    samplers?: Nullable<string[]>;\r\n    /**\r\n     * The list of uniform buffers used in the shader (if any)\r\n     */\r\n    uniformBuffers?: Nullable<string[]>;\r\n    /**\r\n     * String of defines that will be set when running the fragment shader. (default: null)\r\n     */\r\n    defines?: Nullable<string>;\r\n    /**\r\n     * The size of the post process texture.\r\n     * It is either a ratio to downscale or upscale the texture create for this post process, or an object containing width and height values.\r\n     * Default: 1\r\n     */\r\n    size?: number | { width: number; height: number };\r\n    /**\r\n     * The camera that the post process will be attached to (default: null)\r\n     */\r\n    camera?: Nullable<Camera>;\r\n    /**\r\n     * The sampling mode to be used by the shader (default: Constants.TEXTURE_NEAREST_SAMPLINGMODE)\r\n     */\r\n    samplingMode?: number;\r\n    /**\r\n     * The engine to be used to render the post process (default: engine from scene)\r\n     */\r\n    engine?: Engine;\r\n    /**\r\n     * If the post process can be reused on the same frame. (default: false)\r\n     */\r\n    reusable?: boolean;\r\n    /**\r\n     * Type of the texture created for this post process (default: Constants.TEXTURETYPE_UNSIGNED_INT)\r\n     */\r\n    textureType?: number;\r\n    /**\r\n     * The url of the vertex shader to be used. (default: \"postprocess\")\r\n     */\r\n    vertexUrl?: string;\r\n    /**\r\n     * The index parameters to be used for babylons include syntax \"#include<kernelBlurVaryingDeclaration>[0..varyingCount]\". (default: undefined)\r\n     * See usage in babylon.blurPostProcess.ts and kernelBlur.vertex.fx\r\n     */\r\n    indexParameters?: any;\r\n    /**\r\n     * If the shader should not be compiled immediately. (default: false)\r\n     */\r\n    blockCompilation?: boolean;\r\n    /**\r\n     * Format of the texture created for this post process (default: TEXTUREFORMAT_RGBA)\r\n     */\r\n    textureFormat?: number;\r\n    /**\r\n     * The shader language of the shader. (default: GLSL)\r\n     */\r\n    shaderLanguage?: ShaderLanguage;\r\n};\r\n\r\ntype TextureCache = { texture: RenderTargetWrapper; postProcessChannel: number; lastUsedRenderId: number };\r\n\r\n/**\r\n * PostProcess can be used to apply a shader to a texture after it has been rendered\r\n * See https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/usePostProcesses\r\n */\r\nexport class PostProcess {\r\n    /** @internal */\r\n    public _parentContainer: Nullable<AbstractScene> = null;\r\n\r\n    private static _CustomShaderCodeProcessing: { [postProcessName: string]: PostProcessCustomShaderCodeProcessing } = {};\r\n\r\n    /**\r\n     * Registers a shader code processing with a post process name.\r\n     * @param postProcessName name of the post process. Use null for the fallback shader code processing. This is the shader code processing that will be used in case no specific shader code processing has been associated to a post process name\r\n     * @param customShaderCodeProcessing shader code processing to associate to the post process name\r\n     */\r\n    public static RegisterShaderCodeProcessing(postProcessName: Nullable<string>, customShaderCodeProcessing?: PostProcessCustomShaderCodeProcessing) {\r\n        if (!customShaderCodeProcessing) {\r\n            delete PostProcess._CustomShaderCodeProcessing[postProcessName ?? \"\"];\r\n            return;\r\n        }\r\n\r\n        PostProcess._CustomShaderCodeProcessing[postProcessName ?? \"\"] = customShaderCodeProcessing;\r\n    }\r\n\r\n    private static _GetShaderCodeProcessing(postProcessName: string) {\r\n        return PostProcess._CustomShaderCodeProcessing[postProcessName] ?? PostProcess._CustomShaderCodeProcessing[\"\"];\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the unique id of the post process\r\n     */\r\n    @serialize()\r\n    public uniqueId: number;\r\n\r\n    /** Name of the PostProcess. */\r\n    @serialize()\r\n    public name: string;\r\n\r\n    /**\r\n     * Width of the texture to apply the post process on\r\n     */\r\n    @serialize()\r\n    public width = -1;\r\n\r\n    /**\r\n     * Height of the texture to apply the post process on\r\n     */\r\n    @serialize()\r\n    public height = -1;\r\n\r\n    /**\r\n     * Gets the node material used to create this postprocess (null if the postprocess was manually created)\r\n     */\r\n    public nodeMaterialSource: Nullable<NodeMaterial> = null;\r\n\r\n    /**\r\n     * Internal, reference to the location where this postprocess was output to. (Typically the texture on the next postprocess in the chain)\r\n     * @internal\r\n     */\r\n    public _outputTexture: Nullable<RenderTargetWrapper> = null;\r\n    /**\r\n     * Sampling mode used by the shader\r\n     * See https://doc.babylonjs.com/classes/3.1/texture\r\n     */\r\n    @serialize()\r\n    public renderTargetSamplingMode: number;\r\n    /**\r\n     * Clear color to use when screen clearing\r\n     */\r\n    @serializeAsColor4()\r\n    public clearColor: Color4;\r\n    /**\r\n     * If the buffer needs to be cleared before applying the post process. (default: true)\r\n     * Should be set to false if shader will overwrite all previous pixels.\r\n     */\r\n    @serialize()\r\n    public autoClear = true;\r\n    /**\r\n     * If clearing the buffer should be forced in autoClear mode, even when alpha mode is enabled (default: false).\r\n     * By default, the buffer will only be cleared if alpha mode is disabled (and autoClear is true).\r\n     */\r\n    @serialize()\r\n    public forceAutoClearInAlphaMode = false;\r\n    /**\r\n     * Type of alpha mode to use when performing the post process (default: Engine.ALPHA_DISABLE)\r\n     */\r\n    @serialize()\r\n    public alphaMode = Constants.ALPHA_DISABLE;\r\n    /**\r\n     * Sets the setAlphaBlendConstants of the babylon engine\r\n     */\r\n    @serialize()\r\n    public alphaConstants: Color4;\r\n    /**\r\n     * Animations to be used for the post processing\r\n     */\r\n    public animations: Animation[] = [];\r\n\r\n    /**\r\n     * Enable Pixel Perfect mode where texture is not scaled to be power of 2.\r\n     * Can only be used on a single postprocess or on the last one of a chain. (default: false)\r\n     */\r\n    @serialize()\r\n    public enablePixelPerfectMode = false;\r\n\r\n    /**\r\n     * Force the postprocess to be applied without taking in account viewport\r\n     */\r\n    @serialize()\r\n    public forceFullscreenViewport = true;\r\n\r\n    /**\r\n     * List of inspectable custom properties (used by the Inspector)\r\n     * @see https://doc.babylonjs.com/toolsAndResources/inspector#extensibility\r\n     */\r\n    public inspectableCustomProperties: IInspectable[];\r\n\r\n    /**\r\n     * Scale mode for the post process (default: Engine.SCALEMODE_FLOOR)\r\n     *\r\n     * | Value | Type                                | Description |\r\n     * | ----- | ----------------------------------- | ----------- |\r\n     * | 1     | SCALEMODE_FLOOR                     | [engine.scalemode_floor](https://doc.babylonjs.com/api/classes/babylon.engine#scalemode_floor) |\r\n     * | 2     | SCALEMODE_NEAREST                   | [engine.scalemode_nearest](https://doc.babylonjs.com/api/classes/babylon.engine#scalemode_nearest) |\r\n     * | 3     | SCALEMODE_CEILING                   | [engine.scalemode_ceiling](https://doc.babylonjs.com/api/classes/babylon.engine#scalemode_ceiling) |\r\n     *\r\n     */\r\n    @serialize()\r\n    public scaleMode = Constants.SCALEMODE_FLOOR;\r\n    /**\r\n     * Force textures to be a power of two (default: false)\r\n     */\r\n    @serialize()\r\n    public alwaysForcePOT = false;\r\n\r\n    @serialize(\"samples\")\r\n    private _samples = 1;\r\n\r\n    /**\r\n     * Number of sample textures (default: 1)\r\n     */\r\n    public get samples() {\r\n        return this._samples;\r\n    }\r\n\r\n    public set samples(n: number) {\r\n        this._samples = Math.min(n, this._engine.getCaps().maxMSAASamples);\r\n\r\n        this._textures.forEach((texture) => {\r\n            texture.setSamples(this._samples);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Modify the scale of the post process to be the same as the viewport (default: false)\r\n     */\r\n    @serialize()\r\n    public adaptScaleToCurrentViewport = false;\r\n\r\n    private _camera: Camera;\r\n    protected _scene: Scene;\r\n    private _engine: Engine;\r\n\r\n    private _options: number | { width: number; height: number };\r\n    private _reusable = false;\r\n    private _renderId = 0;\r\n    private _textureType: number;\r\n    private _textureFormat: number;\r\n    private _shaderLanguage: ShaderLanguage;\r\n\r\n    /**\r\n     * if externalTextureSamplerBinding is true, the \"apply\" method won't bind the textureSampler texture, it is expected to be done by the \"outside\" (by the onApplyObservable observer most probably).\r\n     * counter-productive in some cases because if the texture bound by \"apply\" is different from the currently texture bound, (the one set by the onApplyObservable observer, for eg) some\r\n     * internal structures (materialContext) will be dirtified, which may impact performances\r\n     */\r\n    public externalTextureSamplerBinding = false;\r\n\r\n    /**\r\n     * Smart array of input and output textures for the post process.\r\n     * @internal\r\n     */\r\n    public _textures = new SmartArray<RenderTargetWrapper>(2);\r\n    /**\r\n     * Smart array of input and output textures for the post process.\r\n     * @internal\r\n     */\r\n    private _textureCache: TextureCache[] = [];\r\n    /**\r\n     * The index in _textures that corresponds to the output texture.\r\n     * @internal\r\n     */\r\n    public _currentRenderTextureInd = 0;\r\n    private _drawWrapper: DrawWrapper;\r\n    private _samplers: string[];\r\n    private _fragmentUrl: string;\r\n    private _vertexUrl: string;\r\n    private _parameters: string[];\r\n    private _uniformBuffers: string[];\r\n    protected _postProcessDefines: Nullable<string>;\r\n    private _scaleRatio = new Vector2(1, 1);\r\n    protected _indexParameters: any;\r\n    private _shareOutputWithPostProcess: Nullable<PostProcess>;\r\n    private _texelSize = Vector2.Zero();\r\n\r\n    /** @internal */\r\n    public _forcedOutputTexture: Nullable<RenderTargetWrapper>;\r\n\r\n    /**\r\n     * Prepass configuration in case this post process needs a texture from prepass\r\n     * @internal\r\n     */\r\n    public _prePassEffectConfiguration: PrePassEffectConfiguration;\r\n\r\n    /**\r\n     * Returns the fragment url or shader name used in the post process.\r\n     * @returns the fragment url or name in the shader store.\r\n     */\r\n    public getEffectName(): string {\r\n        return this._fragmentUrl;\r\n    }\r\n\r\n    // Events\r\n\r\n    /**\r\n     * An event triggered when the postprocess is activated.\r\n     */\r\n    public onActivateObservable = new Observable<Camera>();\r\n\r\n    private _onActivateObserver: Nullable<Observer<Camera>>;\r\n    /**\r\n     * A function that is added to the onActivateObservable\r\n     */\r\n    public set onActivate(callback: Nullable<(camera: Camera) => void>) {\r\n        if (this._onActivateObserver) {\r\n            this.onActivateObservable.remove(this._onActivateObserver);\r\n        }\r\n        if (callback) {\r\n            this._onActivateObserver = this.onActivateObservable.add(callback);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * An event triggered when the postprocess changes its size.\r\n     */\r\n    public onSizeChangedObservable = new Observable<PostProcess>();\r\n\r\n    private _onSizeChangedObserver: Nullable<Observer<PostProcess>>;\r\n    /**\r\n     * A function that is added to the onSizeChangedObservable\r\n     */\r\n    public set onSizeChanged(callback: (postProcess: PostProcess) => void) {\r\n        if (this._onSizeChangedObserver) {\r\n            this.onSizeChangedObservable.remove(this._onSizeChangedObserver);\r\n        }\r\n        this._onSizeChangedObserver = this.onSizeChangedObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered when the postprocess applies its effect.\r\n     */\r\n    public onApplyObservable = new Observable<Effect>();\r\n\r\n    private _onApplyObserver: Nullable<Observer<Effect>>;\r\n    /**\r\n     * A function that is added to the onApplyObservable\r\n     */\r\n    public set onApply(callback: (effect: Effect) => void) {\r\n        if (this._onApplyObserver) {\r\n            this.onApplyObservable.remove(this._onApplyObserver);\r\n        }\r\n        this._onApplyObserver = this.onApplyObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered before rendering the postprocess\r\n     */\r\n    public onBeforeRenderObservable = new Observable<Effect>();\r\n\r\n    private _onBeforeRenderObserver: Nullable<Observer<Effect>>;\r\n    /**\r\n     * A function that is added to the onBeforeRenderObservable\r\n     */\r\n    public set onBeforeRender(callback: (effect: Effect) => void) {\r\n        if (this._onBeforeRenderObserver) {\r\n            this.onBeforeRenderObservable.remove(this._onBeforeRenderObserver);\r\n        }\r\n        this._onBeforeRenderObserver = this.onBeforeRenderObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered after rendering the postprocess\r\n     */\r\n    public onAfterRenderObservable = new Observable<Effect>();\r\n\r\n    private _onAfterRenderObserver: Nullable<Observer<Effect>>;\r\n    /**\r\n     * A function that is added to the onAfterRenderObservable\r\n     */\r\n    public set onAfterRender(callback: (efect: Effect) => void) {\r\n        if (this._onAfterRenderObserver) {\r\n            this.onAfterRenderObservable.remove(this._onAfterRenderObserver);\r\n        }\r\n        this._onAfterRenderObserver = this.onAfterRenderObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * The input texture for this post process and the output texture of the previous post process. When added to a pipeline the previous post process will\r\n     * render it's output into this texture and this texture will be used as textureSampler in the fragment shader of this post process.\r\n     */\r\n    public get inputTexture(): RenderTargetWrapper {\r\n        return this._textures.data[this._currentRenderTextureInd];\r\n    }\r\n\r\n    public set inputTexture(value: RenderTargetWrapper) {\r\n        this._forcedOutputTexture = value;\r\n    }\r\n\r\n    /**\r\n     * Since inputTexture should always be defined, if we previously manually set `inputTexture`,\r\n     * the only way to unset it is to use this function to restore its internal state\r\n     */\r\n    public restoreDefaultInputTexture() {\r\n        if (this._forcedOutputTexture) {\r\n            this._forcedOutputTexture = null;\r\n            this.markTextureDirty();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the camera which post process is applied to.\r\n     * @returns The camera the post process is applied to.\r\n     */\r\n    public getCamera(): Camera {\r\n        return this._camera;\r\n    }\r\n\r\n    /**\r\n     * Gets the texel size of the postprocess.\r\n     * See https://en.wikipedia.org/wiki/Texel_(graphics)\r\n     */\r\n    public get texelSize(): Vector2 {\r\n        if (this._shareOutputWithPostProcess) {\r\n            return this._shareOutputWithPostProcess.texelSize;\r\n        }\r\n\r\n        if (this._forcedOutputTexture) {\r\n            this._texelSize.copyFromFloats(1.0 / this._forcedOutputTexture.width, 1.0 / this._forcedOutputTexture.height);\r\n        }\r\n\r\n        return this._texelSize;\r\n    }\r\n\r\n    /**\r\n     * Creates a new instance PostProcess\r\n     * @param name The name of the PostProcess.\r\n     * @param fragmentUrl The url of the fragment shader to be used.\r\n     * @param options The options to be used when constructing the post process.\r\n     */\r\n    constructor(name: string, fragmentUrl: string, options?: PostProcessOptions);\r\n\r\n    /**\r\n     * Creates a new instance PostProcess\r\n     * @param name The name of the PostProcess.\r\n     * @param fragmentUrl The url of the fragment shader to be used.\r\n     * @param parameters Array of the names of uniform non-sampler2D variables that will be passed to the shader.\r\n     * @param samplers Array of the names of uniform sampler2D variables that will be passed to the shader.\r\n     * @param options The required width/height ratio to downsize to before computing the render pass. (Use 1.0 for full size)\r\n     * @param camera The camera to apply the render pass to.\r\n     * @param samplingMode The sampling mode to be used when computing the pass. (default: 0)\r\n     * @param engine The engine which the post process will be applied. (default: current engine)\r\n     * @param reusable If the post process can be reused on the same frame. (default: false)\r\n     * @param defines String of defines that will be set when running the fragment shader. (default: null)\r\n     * @param textureType Type of textures used when performing the post process. (default: 0)\r\n     * @param vertexUrl The url of the vertex shader to be used. (default: \"postprocess\")\r\n     * @param indexParameters The index parameters to be used for babylons include syntax \"#include<kernelBlurVaryingDeclaration>[0..varyingCount]\". (default: undefined) See usage in babylon.blurPostProcess.ts and kernelBlur.vertex.fx\r\n     * @param blockCompilation If the shader should not be compiled immediatly. (default: false)\r\n     * @param textureFormat Format of textures used when performing the post process. (default: TEXTUREFORMAT_RGBA)\r\n     * @param shaderLanguage The shader language of the shader. (default: GLSL)\r\n     */\r\n    constructor(\r\n        name: string,\r\n        fragmentUrl: string,\r\n        parameters: Nullable<string[]>,\r\n        samplers: Nullable<string[]>,\r\n        options: number | PostProcessOptions,\r\n        camera: Nullable<Camera>,\r\n        samplingMode?: number,\r\n        engine?: Engine,\r\n        reusable?: boolean,\r\n        defines?: Nullable<string>,\r\n        textureType?: number,\r\n        vertexUrl?: string,\r\n        indexParameters?: any,\r\n        blockCompilation?: boolean,\r\n        textureFormat?: number,\r\n        shaderLanguage?: ShaderLanguage\r\n    );\r\n\r\n    /** @internal */\r\n    constructor(\r\n        name: string,\r\n        fragmentUrl: string,\r\n        parameters?: Nullable<string[]> | PostProcessOptions,\r\n        samplers?: Nullable<string[]>,\r\n        _size?: number | PostProcessOptions,\r\n        camera?: Nullable<Camera>,\r\n        samplingMode: number = Constants.TEXTURE_NEAREST_SAMPLINGMODE,\r\n        engine?: Engine,\r\n        reusable?: boolean,\r\n        defines: Nullable<string> = null,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        vertexUrl: string = \"postprocess\",\r\n        indexParameters?: any,\r\n        blockCompilation = false,\r\n        textureFormat = Constants.TEXTUREFORMAT_RGBA,\r\n        shaderLanguage = ShaderLanguage.GLSL\r\n    ) {\r\n        this.name = name;\r\n        let size: number | { width: number; height: number } = 1;\r\n        let uniformBuffers: Nullable<string[]> = null;\r\n        if (parameters && !Array.isArray(parameters)) {\r\n            const options = parameters;\r\n            parameters = options.uniforms ?? null;\r\n            samplers = options.samplers ?? null;\r\n            size = options.size ?? 1;\r\n            camera = options.camera ?? null;\r\n            samplingMode = options.samplingMode ?? Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n            engine = options.engine;\r\n            reusable = options.reusable;\r\n            defines = options.defines ?? null;\r\n            textureType = options.textureType ?? Constants.TEXTURETYPE_UNSIGNED_INT;\r\n            vertexUrl = options.vertexUrl ?? \"postprocess\";\r\n            indexParameters = options.indexParameters;\r\n            blockCompilation = options.blockCompilation ?? false;\r\n            textureFormat = options.textureFormat ?? Constants.TEXTUREFORMAT_RGBA;\r\n            shaderLanguage = options.shaderLanguage ?? ShaderLanguage.GLSL;\r\n            uniformBuffers = options.uniformBuffers ?? null;\r\n        } else if (_size) {\r\n            if (typeof _size === \"number\") {\r\n                size = _size;\r\n            } else {\r\n                size = { width: _size.width!, height: _size.height! };\r\n            }\r\n        }\r\n\r\n        if (camera != null) {\r\n            this._camera = camera;\r\n            this._scene = camera.getScene();\r\n            camera.attachPostProcess(this);\r\n            this._engine = this._scene.getEngine();\r\n\r\n            this._scene.postProcesses.push(this);\r\n            this.uniqueId = this._scene.getUniqueId();\r\n        } else if (engine) {\r\n            this._engine = engine;\r\n            this._engine.postProcesses.push(this);\r\n        }\r\n\r\n        this._options = size;\r\n        this.renderTargetSamplingMode = samplingMode ? samplingMode : Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        this._reusable = reusable || false;\r\n        this._textureType = textureType;\r\n        this._textureFormat = textureFormat;\r\n        this._shaderLanguage = shaderLanguage;\r\n\r\n        this._samplers = samplers || [];\r\n        this._samplers.push(\"textureSampler\");\r\n\r\n        this._fragmentUrl = fragmentUrl;\r\n        this._vertexUrl = vertexUrl;\r\n        this._parameters = parameters || [];\r\n\r\n        this._parameters.push(\"scale\");\r\n        this._uniformBuffers = uniformBuffers || [];\r\n\r\n        this._indexParameters = indexParameters;\r\n        this._drawWrapper = new DrawWrapper(this._engine);\r\n\r\n        if (!blockCompilation) {\r\n            this.updateEffect(defines);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"PostProcess\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"PostProcess\";\r\n    }\r\n\r\n    /**\r\n     * Gets the engine which this post process belongs to.\r\n     * @returns The engine the post process was enabled with.\r\n     */\r\n    public getEngine(): Engine {\r\n        return this._engine;\r\n    }\r\n\r\n    /**\r\n     * The effect that is created when initializing the post process.\r\n     * @returns The created effect corresponding the postprocess.\r\n     */\r\n    public getEffect(): Effect {\r\n        return this._drawWrapper.effect!;\r\n    }\r\n\r\n    /**\r\n     * To avoid multiple redundant textures for multiple post process, the output the output texture for this post process can be shared with another.\r\n     * @param postProcess The post process to share the output with.\r\n     * @returns This post process.\r\n     */\r\n    public shareOutputWith(postProcess: PostProcess): PostProcess {\r\n        this._disposeTextures();\r\n\r\n        this._shareOutputWithPostProcess = postProcess;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Reverses the effect of calling shareOutputWith and returns the post process back to its original state.\r\n     * This should be called if the post process that shares output with this post process is disabled/disposed.\r\n     */\r\n    public useOwnOutput() {\r\n        if (this._textures.length == 0) {\r\n            this._textures = new SmartArray<RenderTargetWrapper>(2);\r\n        }\r\n\r\n        this._shareOutputWithPostProcess = null;\r\n    }\r\n\r\n    /**\r\n     * Updates the effect with the current post process compile time values and recompiles the shader.\r\n     * @param defines Define statements that should be added at the beginning of the shader. (default: null)\r\n     * @param uniforms Set of uniform variables that will be passed to the shader. (default: null)\r\n     * @param samplers Set of Texture2D variables that will be passed to the shader. (default: null)\r\n     * @param indexParameters The index parameters to be used for babylons include syntax \"#include<kernelBlurVaryingDeclaration>[0..varyingCount]\". (default: undefined) See usage in babylon.blurPostProcess.ts and kernelBlur.vertex.fx\r\n     * @param onCompiled Called when the shader has been compiled.\r\n     * @param onError Called if there is an error when compiling a shader.\r\n     * @param vertexUrl The url of the vertex shader to be used (default: the one given at construction time)\r\n     * @param fragmentUrl The url of the fragment shader to be used (default: the one given at construction time)\r\n     */\r\n    public updateEffect(\r\n        defines: Nullable<string> = null,\r\n        uniforms: Nullable<string[]> = null,\r\n        samplers: Nullable<string[]> = null,\r\n        indexParameters?: any,\r\n        onCompiled?: (effect: Effect) => void,\r\n        onError?: (effect: Effect, errors: string) => void,\r\n        vertexUrl?: string,\r\n        fragmentUrl?: string\r\n    ) {\r\n        const customShaderCodeProcessing = PostProcess._GetShaderCodeProcessing(this.name);\r\n        if (customShaderCodeProcessing?.defineCustomBindings) {\r\n            const newUniforms = uniforms?.slice() ?? [];\r\n            newUniforms.push(...this._parameters);\r\n\r\n            const newSamplers = samplers?.slice() ?? [];\r\n            newSamplers.push(...this._samplers);\r\n\r\n            defines = customShaderCodeProcessing.defineCustomBindings(this.name, defines, newUniforms, newSamplers);\r\n            uniforms = newUniforms;\r\n            samplers = newSamplers;\r\n        }\r\n        this._postProcessDefines = defines;\r\n        this._drawWrapper.effect = this._engine.createEffect(\r\n            { vertex: vertexUrl ?? this._vertexUrl, fragment: fragmentUrl ?? this._fragmentUrl },\r\n            {\r\n                attributes: [\"position\"],\r\n                uniformsNames: uniforms || this._parameters,\r\n                uniformBuffersNames: this._uniformBuffers,\r\n                samplers: samplers || this._samplers,\r\n                defines: defines !== null ? defines : \"\",\r\n                fallbacks: null,\r\n                onCompiled: onCompiled ?? null,\r\n                onError: onError ?? null,\r\n                indexParameters: indexParameters || this._indexParameters,\r\n                processCodeAfterIncludes: customShaderCodeProcessing?.processCodeAfterIncludes\r\n                    ? (shaderType: string, code: string) => customShaderCodeProcessing!.processCodeAfterIncludes!(this.name, shaderType, code)\r\n                    : null,\r\n                processFinalCode: customShaderCodeProcessing?.processFinalCode\r\n                    ? (shaderType: string, code: string) => customShaderCodeProcessing!.processFinalCode!(this.name, shaderType, code)\r\n                    : null,\r\n                shaderLanguage: this._shaderLanguage,\r\n            },\r\n            this._engine\r\n        );\r\n    }\r\n\r\n    /**\r\n     * The post process is reusable if it can be used multiple times within one frame.\r\n     * @returns If the post process is reusable\r\n     */\r\n    public isReusable(): boolean {\r\n        return this._reusable;\r\n    }\r\n\r\n    /** invalidate frameBuffer to hint the postprocess to create a depth buffer */\r\n    public markTextureDirty(): void {\r\n        this.width = -1;\r\n    }\r\n\r\n    private _createRenderTargetTexture(textureSize: { width: number; height: number }, textureOptions: RenderTargetCreationOptions, channel = 0) {\r\n        for (let i = 0; i < this._textureCache.length; i++) {\r\n            if (\r\n                this._textureCache[i].texture.width === textureSize.width &&\r\n                this._textureCache[i].texture.height === textureSize.height &&\r\n                this._textureCache[i].postProcessChannel === channel &&\r\n                this._textureCache[i].texture._generateDepthBuffer === textureOptions.generateDepthBuffer &&\r\n                this._textureCache[i].texture.samples === textureOptions.samples\r\n            ) {\r\n                return this._textureCache[i].texture;\r\n            }\r\n        }\r\n\r\n        const tex = this._engine.createRenderTargetTexture(textureSize, textureOptions);\r\n        this._textureCache.push({ texture: tex, postProcessChannel: channel, lastUsedRenderId: -1 });\r\n\r\n        return tex;\r\n    }\r\n\r\n    private _flushTextureCache() {\r\n        const currentRenderId = this._renderId;\r\n\r\n        for (let i = this._textureCache.length - 1; i >= 0; i--) {\r\n            if (currentRenderId - this._textureCache[i].lastUsedRenderId > 100) {\r\n                let currentlyUsed = false;\r\n                for (let j = 0; j < this._textures.length; j++) {\r\n                    if (this._textures.data[j] === this._textureCache[i].texture) {\r\n                        currentlyUsed = true;\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                if (!currentlyUsed) {\r\n                    this._textureCache[i].texture.dispose();\r\n                    this._textureCache.splice(i, 1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Resizes the post-process texture\r\n     * @param width Width of the texture\r\n     * @param height Height of the texture\r\n     * @param camera The camera this post-process is applied to. Pass null if the post-process is used outside the context of a camera post-process chain (default: null)\r\n     * @param needMipMaps True if mip maps need to be generated after render (default: false)\r\n     * @param forceDepthStencil True to force post-process texture creation with stencil depth and buffer (default: false)\r\n     */\r\n    public resize(width: number, height: number, camera: Nullable<Camera> = null, needMipMaps = false, forceDepthStencil = false) {\r\n        if (this._textures.length > 0) {\r\n            this._textures.reset();\r\n        }\r\n\r\n        this.width = width;\r\n        this.height = height;\r\n\r\n        let firstPP = null;\r\n        if (camera) {\r\n            for (let i = 0; i < camera._postProcesses.length; i++) {\r\n                if (camera._postProcesses[i] !== null) {\r\n                    firstPP = camera._postProcesses[i];\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n\r\n        const textureSize = { width: this.width, height: this.height };\r\n        const textureOptions = {\r\n            generateMipMaps: needMipMaps,\r\n            generateDepthBuffer: forceDepthStencil || firstPP === this,\r\n            generateStencilBuffer: (forceDepthStencil || firstPP === this) && this._engine.isStencilEnable,\r\n            samplingMode: this.renderTargetSamplingMode,\r\n            type: this._textureType,\r\n            format: this._textureFormat,\r\n            samples: this._samples,\r\n            label: \"PostProcessRTT-\" + this.name,\r\n        };\r\n\r\n        this._textures.push(this._createRenderTargetTexture(textureSize, textureOptions, 0));\r\n\r\n        if (this._reusable) {\r\n            this._textures.push(this._createRenderTargetTexture(textureSize, textureOptions, 1));\r\n        }\r\n\r\n        this._texelSize.copyFromFloats(1.0 / this.width, 1.0 / this.height);\r\n\r\n        this.onSizeChangedObservable.notifyObservers(this);\r\n    }\r\n\r\n    private _getTarget() {\r\n        let target: RenderTargetWrapper;\r\n\r\n        if (this._shareOutputWithPostProcess) {\r\n            target = this._shareOutputWithPostProcess.inputTexture;\r\n        } else if (this._forcedOutputTexture) {\r\n            target = this._forcedOutputTexture;\r\n\r\n            this.width = this._forcedOutputTexture.width;\r\n            this.height = this._forcedOutputTexture.height;\r\n        } else {\r\n            target = this.inputTexture;\r\n\r\n            let cache;\r\n            for (let i = 0; i < this._textureCache.length; i++) {\r\n                if (this._textureCache[i].texture === target) {\r\n                    cache = this._textureCache[i];\r\n                    break;\r\n                }\r\n            }\r\n\r\n            if (cache) {\r\n                cache.lastUsedRenderId = this._renderId;\r\n            }\r\n        }\r\n\r\n        return target;\r\n    }\r\n\r\n    /**\r\n     * Activates the post process by intializing the textures to be used when executed. Notifies onActivateObservable.\r\n     * When this post process is used in a pipeline, this is call will bind the input texture of this post process to the output of the previous.\r\n     * @param camera The camera that will be used in the post process. This camera will be used when calling onActivateObservable.\r\n     * @param sourceTexture The source texture to be inspected to get the width and height if not specified in the post process constructor. (default: null)\r\n     * @param forceDepthStencil If true, a depth and stencil buffer will be generated. (default: false)\r\n     * @returns The render target wrapper that was bound to be written to.\r\n     */\r\n    public activate(camera: Nullable<Camera>, sourceTexture: Nullable<InternalTexture> = null, forceDepthStencil?: boolean): RenderTargetWrapper {\r\n        camera = camera || this._camera;\r\n\r\n        const scene = camera.getScene();\r\n        const engine = scene.getEngine();\r\n        const maxSize = engine.getCaps().maxTextureSize;\r\n\r\n        const requiredWidth = ((sourceTexture ? sourceTexture.width : this._engine.getRenderWidth(true)) * <number>this._options) | 0;\r\n        const requiredHeight = ((sourceTexture ? sourceTexture.height : this._engine.getRenderHeight(true)) * <number>this._options) | 0;\r\n\r\n        let desiredWidth = (<PostProcessOptions>this._options).width || requiredWidth;\r\n        let desiredHeight = (<PostProcessOptions>this._options).height || requiredHeight;\r\n\r\n        const needMipMaps =\r\n            this.renderTargetSamplingMode !== Constants.TEXTURE_NEAREST_LINEAR &&\r\n            this.renderTargetSamplingMode !== Constants.TEXTURE_NEAREST_NEAREST &&\r\n            this.renderTargetSamplingMode !== Constants.TEXTURE_LINEAR_LINEAR;\r\n\r\n        let target: Nullable<RenderTargetWrapper> = null;\r\n\r\n        if (!this._shareOutputWithPostProcess && !this._forcedOutputTexture) {\r\n            if (this.adaptScaleToCurrentViewport) {\r\n                const currentViewport = engine.currentViewport;\r\n\r\n                if (currentViewport) {\r\n                    desiredWidth *= currentViewport.width;\r\n                    desiredHeight *= currentViewport.height;\r\n                }\r\n            }\r\n\r\n            if (needMipMaps || this.alwaysForcePOT) {\r\n                if (!(<PostProcessOptions>this._options).width) {\r\n                    desiredWidth = engine.needPOTTextures ? Engine.GetExponentOfTwo(desiredWidth, maxSize, this.scaleMode) : desiredWidth;\r\n                }\r\n\r\n                if (!(<PostProcessOptions>this._options).height) {\r\n                    desiredHeight = engine.needPOTTextures ? Engine.GetExponentOfTwo(desiredHeight, maxSize, this.scaleMode) : desiredHeight;\r\n                }\r\n            }\r\n\r\n            if (this.width !== desiredWidth || this.height !== desiredHeight || !(target = this._getTarget())) {\r\n                this.resize(desiredWidth, desiredHeight, camera, needMipMaps, forceDepthStencil);\r\n            }\r\n\r\n            this._textures.forEach((texture) => {\r\n                if (texture.samples !== this.samples) {\r\n                    this._engine.updateRenderTargetTextureSampleCount(texture, this.samples);\r\n                }\r\n            });\r\n\r\n            this._flushTextureCache();\r\n            this._renderId++;\r\n        }\r\n\r\n        if (!target) {\r\n            target = this._getTarget();\r\n        }\r\n\r\n        // Bind the input of this post process to be used as the output of the previous post process.\r\n        if (this.enablePixelPerfectMode) {\r\n            this._scaleRatio.copyFromFloats(requiredWidth / desiredWidth, requiredHeight / desiredHeight);\r\n            this._engine.bindFramebuffer(target, 0, requiredWidth, requiredHeight, this.forceFullscreenViewport);\r\n        } else {\r\n            this._scaleRatio.copyFromFloats(1, 1);\r\n            this._engine.bindFramebuffer(target, 0, undefined, undefined, this.forceFullscreenViewport);\r\n        }\r\n\r\n        this._engine._debugInsertMarker?.(`post process ${this.name} input`);\r\n\r\n        this.onActivateObservable.notifyObservers(camera);\r\n\r\n        // Clear\r\n        if (this.autoClear && (this.alphaMode === Constants.ALPHA_DISABLE || this.forceAutoClearInAlphaMode)) {\r\n            this._engine.clear(this.clearColor ? this.clearColor : scene.clearColor, scene._allowPostProcessClearColor, true, true);\r\n        }\r\n\r\n        if (this._reusable) {\r\n            this._currentRenderTextureInd = (this._currentRenderTextureInd + 1) % 2;\r\n        }\r\n        return target;\r\n    }\r\n\r\n    /**\r\n     * If the post process is supported.\r\n     */\r\n    public get isSupported(): boolean {\r\n        return this._drawWrapper.effect!.isSupported;\r\n    }\r\n\r\n    /**\r\n     * The aspect ratio of the output texture.\r\n     */\r\n    public get aspectRatio(): number {\r\n        if (this._shareOutputWithPostProcess) {\r\n            return this._shareOutputWithPostProcess.aspectRatio;\r\n        }\r\n\r\n        if (this._forcedOutputTexture) {\r\n            return this._forcedOutputTexture.width / this._forcedOutputTexture.height;\r\n        }\r\n        return this.width / this.height;\r\n    }\r\n\r\n    /**\r\n     * Get a value indicating if the post-process is ready to be used\r\n     * @returns true if the post-process is ready (shader is compiled)\r\n     */\r\n    public isReady(): boolean {\r\n        return this._drawWrapper.effect?.isReady() ?? false;\r\n    }\r\n\r\n    /**\r\n     * Binds all textures and uniforms to the shader, this will be run on every pass.\r\n     * @returns the effect corresponding to this post process. Null if not compiled or not ready.\r\n     */\r\n    public apply(): Nullable<Effect> {\r\n        // Check\r\n        if (!this._drawWrapper.effect?.isReady()) {\r\n            return null;\r\n        }\r\n\r\n        // States\r\n        this._engine.enableEffect(this._drawWrapper);\r\n        this._engine.setState(false);\r\n        this._engine.setDepthBuffer(false);\r\n        this._engine.setDepthWrite(false);\r\n\r\n        // Alpha\r\n        this._engine.setAlphaMode(this.alphaMode);\r\n        if (this.alphaConstants) {\r\n            this.getEngine().setAlphaConstants(this.alphaConstants.r, this.alphaConstants.g, this.alphaConstants.b, this.alphaConstants.a);\r\n        }\r\n\r\n        // Bind the output texture of the preivous post process as the input to this post process.\r\n        let source: RenderTargetWrapper;\r\n        if (this._shareOutputWithPostProcess) {\r\n            source = this._shareOutputWithPostProcess.inputTexture;\r\n        } else if (this._forcedOutputTexture) {\r\n            source = this._forcedOutputTexture;\r\n        } else {\r\n            source = this.inputTexture;\r\n        }\r\n\r\n        if (!this.externalTextureSamplerBinding) {\r\n            this._drawWrapper.effect._bindTexture(\"textureSampler\", source?.texture);\r\n        }\r\n\r\n        // Parameters\r\n        this._drawWrapper.effect.setVector2(\"scale\", this._scaleRatio);\r\n        this.onApplyObservable.notifyObservers(this._drawWrapper.effect);\r\n\r\n        PostProcess._GetShaderCodeProcessing(this.name)?.bindCustomBindings?.(this.name, this._drawWrapper.effect);\r\n\r\n        return this._drawWrapper.effect;\r\n    }\r\n\r\n    private _disposeTextures() {\r\n        if (this._shareOutputWithPostProcess || this._forcedOutputTexture) {\r\n            this._disposeTextureCache();\r\n            return;\r\n        }\r\n\r\n        this._disposeTextureCache();\r\n        this._textures.dispose();\r\n    }\r\n\r\n    private _disposeTextureCache() {\r\n        for (let i = this._textureCache.length - 1; i >= 0; i--) {\r\n            this._textureCache[i].texture.dispose();\r\n        }\r\n\r\n        this._textureCache.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Sets the required values to the prepass renderer.\r\n     * @param prePassRenderer defines the prepass renderer to setup.\r\n     * @returns true if the pre pass is needed.\r\n     */\r\n    public setPrePassRenderer(prePassRenderer: PrePassRenderer): boolean {\r\n        if (this._prePassEffectConfiguration) {\r\n            this._prePassEffectConfiguration = prePassRenderer.addEffectConfiguration(this._prePassEffectConfiguration);\r\n            this._prePassEffectConfiguration.enabled = true;\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Disposes the post process.\r\n     * @param camera The camera to dispose the post process on.\r\n     */\r\n    public dispose(camera?: Camera): void {\r\n        camera = camera || this._camera;\r\n\r\n        this._disposeTextures();\r\n\r\n        let index;\r\n        if (this._scene) {\r\n            index = this._scene.postProcesses.indexOf(this);\r\n            if (index !== -1) {\r\n                this._scene.postProcesses.splice(index, 1);\r\n            }\r\n        }\r\n\r\n        if (this._parentContainer) {\r\n            const index = this._parentContainer.postProcesses.indexOf(this);\r\n            if (index > -1) {\r\n                this._parentContainer.postProcesses.splice(index, 1);\r\n            }\r\n            this._parentContainer = null;\r\n        }\r\n\r\n        index = this._engine.postProcesses.indexOf(this);\r\n        if (index !== -1) {\r\n            this._engine.postProcesses.splice(index, 1);\r\n        }\r\n\r\n        if (!camera) {\r\n            return;\r\n        }\r\n        camera.detachPostProcess(this);\r\n\r\n        index = camera._postProcesses.indexOf(this);\r\n        if (index === 0 && camera._postProcesses.length > 0) {\r\n            const firstPostProcess = this._camera._getFirstPostProcess();\r\n            if (firstPostProcess) {\r\n                firstPostProcess.markTextureDirty();\r\n            }\r\n        }\r\n\r\n        this.onActivateObservable.clear();\r\n        this.onAfterRenderObservable.clear();\r\n        this.onApplyObservable.clear();\r\n        this.onBeforeRenderObservable.clear();\r\n        this.onSizeChangedObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Serializes the post process to a JSON object\r\n     * @returns the JSON object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n        const camera = this.getCamera() || (this._scene && this._scene.activeCamera);\r\n        serializationObject.customType = \"BABYLON.\" + this.getClassName();\r\n        serializationObject.cameraId = camera ? camera.id : null;\r\n        serializationObject.reusable = this._reusable;\r\n        serializationObject.textureType = this._textureType;\r\n        serializationObject.fragmentUrl = this._fragmentUrl;\r\n        serializationObject.parameters = this._parameters;\r\n        serializationObject.samplers = this._samplers;\r\n        serializationObject.options = this._options;\r\n        serializationObject.defines = this._postProcessDefines;\r\n        serializationObject.textureFormat = this._textureFormat;\r\n        serializationObject.vertexUrl = this._vertexUrl;\r\n        serializationObject.indexParameters = this._indexParameters;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Clones this post process\r\n     * @returns a new post process similar to this one\r\n     */\r\n    public clone(): Nullable<PostProcess> {\r\n        const serializationObject = this.serialize();\r\n        serializationObject._engine = this._engine;\r\n        serializationObject.cameraId = null;\r\n\r\n        const result = PostProcess.Parse(serializationObject, this._scene, \"\");\r\n\r\n        if (!result) {\r\n            return null;\r\n        }\r\n\r\n        result.onActivateObservable = this.onActivateObservable.clone();\r\n        result.onSizeChangedObservable = this.onSizeChangedObservable.clone();\r\n        result.onApplyObservable = this.onApplyObservable.clone();\r\n        result.onBeforeRenderObservable = this.onBeforeRenderObservable.clone();\r\n        result.onAfterRenderObservable = this.onAfterRenderObservable.clone();\r\n\r\n        result._prePassEffectConfiguration = this._prePassEffectConfiguration;\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Creates a material from parsed material data\r\n     * @param parsedPostProcess defines parsed post process data\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures\r\n     * @returns a new post process\r\n     */\r\n    public static Parse(parsedPostProcess: any, scene: Scene, rootUrl: string): Nullable<PostProcess> {\r\n        const postProcessType = GetClass(parsedPostProcess.customType);\r\n\r\n        if (!postProcessType || !postProcessType._Parse) {\r\n            return null;\r\n        }\r\n\r\n        const camera = scene ? scene.getCameraById(parsedPostProcess.cameraId) : null;\r\n        return postProcessType._Parse(parsedPostProcess, camera, scene, rootUrl);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _Parse(parsedPostProcess: any, targetCamera: Camera, scene: Scene, rootUrl: string): Nullable<PostProcess> {\r\n        return SerializationHelper.Parse(\r\n            () => {\r\n                return new PostProcess(\r\n                    parsedPostProcess.name,\r\n                    parsedPostProcess.fragmentUrl,\r\n                    parsedPostProcess.parameters,\r\n                    parsedPostProcess.samplers,\r\n                    parsedPostProcess.options,\r\n                    targetCamera,\r\n                    parsedPostProcess.renderTargetSamplingMode,\r\n                    parsedPostProcess._engine,\r\n                    parsedPostProcess.reusable,\r\n                    parsedPostProcess.defines,\r\n                    parsedPostProcess.textureType,\r\n                    parsedPostProcess.vertexUrl,\r\n                    parsedPostProcess.indexParameters,\r\n                    false,\r\n                    parsedPostProcess.textureFormat\r\n                );\r\n            },\r\n            parsedPostProcess,\r\n            scene,\r\n            rootUrl\r\n        );\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PostProcess\", PostProcess);\r\n"]}