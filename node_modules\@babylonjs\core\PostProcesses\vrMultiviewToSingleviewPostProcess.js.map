{"version": 3, "file": "vrMultiviewToSingleviewPostProcess.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/vrMultiviewToSingleviewPostProcess.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,OAAO,6CAA6C,CAAC;AACrD,OAAO,wCAAwC,CAAC;AAGhD;;;GAGG;AACH,MAAM,OAAO,kCAAmC,SAAQ,WAAW;IAC/D;;;OAGG;IACI,YAAY;QACf,OAAO,oCAAoC,CAAC;IAChD,CAAC;IAED;;;;;OAKG;IACH,YAAY,IAAY,EAAE,MAAwB,EAAE,WAAmB;QACnE,KAAK,CAAC,IAAI,EAAE,yBAAyB,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,kBAAkB,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAEjI,MAAM,GAAG,GAAG,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;QACvC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAC3C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAc,EAAE,EAAE;YAC1C,IAAI,GAAG,CAAC,MAAM,CAAC,YAAY,IAAI,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,EAAE;gBACjE,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;aAClC;iBAAM;gBACH,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;aAClC;YACD,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;IACP,CAAC;CACJ", "sourcesContent": ["import type { Camera } from \"../Cameras/camera\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { PostProcess } from \"./postProcess\";\r\n\r\nimport \"../Shaders/vrMultiviewToSingleview.fragment\";\r\nimport \"../Engines/Extensions/engine.multiview\";\r\nimport type { Nullable } from \"../types\";\r\n\r\n/**\r\n * VRMultiviewToSingleview used to convert multiview texture arrays to standard textures for scenarios such as webVR\r\n * This will not be used for webXR as it supports displaying texture arrays directly\r\n */\r\nexport class VRMultiviewToSingleviewPostProcess extends PostProcess {\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"VRMultiviewToSingleviewPostProcess\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"VRMultiviewToSingleviewPostProcess\";\r\n    }\r\n\r\n    /**\r\n     * Initializes a VRMultiviewToSingleview\r\n     * @param name name of the post process\r\n     * @param camera camera to be applied to\r\n     * @param scaleFactor scaling factor to the size of the output texture\r\n     */\r\n    constructor(name: string, camera: Nullable<Camera>, scaleFactor: number) {\r\n        super(name, \"vrMultiviewToSingleview\", [\"imageIndex\"], [\"multiviewSampler\"], scaleFactor, camera, Texture.BILINEAR_SAMPLINGMODE);\r\n\r\n        const cam = camera ?? this.getCamera();\r\n        this.onSizeChangedObservable.add(() => {});\r\n        this.onApplyObservable.add((effect: Effect) => {\r\n            if (cam._scene.activeCamera && cam._scene.activeCamera.isLeftCamera) {\r\n                effect.setInt(\"imageIndex\", 0);\r\n            } else {\r\n                effect.setInt(\"imageIndex\", 1);\r\n            }\r\n            effect.setTexture(\"multiviewSampler\", cam._multiviewTexture);\r\n        });\r\n    }\r\n}\r\n"]}