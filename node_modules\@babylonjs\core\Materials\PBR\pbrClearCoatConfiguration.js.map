{"version": 3, "file": "pbrClearCoatConfiguration.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/PBR/pbrClearCoatConfiguration.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AAC3G,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAEhD,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAKjD,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAKrD,OAAO,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAE3F;;GAEG;AACH,MAAM,OAAO,wBAAyB,SAAQ,eAAe;IAA7D;;QACW,cAAS,GAAG,KAAK,CAAC;QAClB,yBAAoB,GAAG,KAAK,CAAC;QAC7B,sBAAiB,GAAG,KAAK,CAAC;QAC1B,gCAA2B,GAAG,KAAK,CAAC;QACpC,8BAAyB,GAAG,CAAC,CAAC;QAC9B,wCAAmC,GAAG,CAAC,CAAC;QACxC,mBAAc,GAAG,KAAK,CAAC;QACvB,2BAAsB,GAAG,CAAC,CAAC;QAC3B,6CAAwC,GAAG,KAAK,CAAC;QACjD,0CAAqC,GAAG,KAAK,CAAC;QAC9C,uBAAkB,GAAG,KAAK,CAAC;QAE3B,mBAAc,GAAG,KAAK,CAAC;QACvB,2BAAsB,GAAG,KAAK,CAAC;QAC/B,mCAA8B,GAAG,CAAC,CAAC;QACnC,gCAA2B,GAAG,KAAK,CAAC;IAC/C,CAAC;CAAA;AAED;;GAEG;AACH,MAAM,OAAO,yBAA0B,SAAQ,kBAAkB;IAgI7D,gBAAgB;IACT,gCAAgC;QACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,IAAI,CAAC,wCAAwC,EAAE,CAAC;IACpD,CAAC;IAED,YAAY,QAAyB,EAAE,eAAe,GAAG,IAAI;QACzD,KAAK,CAAC,QAAQ,EAAE,cAAc,EAAE,GAAG,EAAE,IAAI,wBAAwB,EAAE,EAAE,eAAe,CAAC,CAAC;QA7HlF,eAAU,GAAG,KAAK,CAAC;QAC3B;;WAEG;QAGI,cAAS,GAAG,KAAK,CAAC;QAEzB;;WAEG;QAEI,cAAS,GAAW,CAAC,CAAC;QAE7B;;WAEG;QAEI,cAAS,GAAW,CAAC,CAAC;QAErB,uBAAkB,GAAG,yBAAyB,CAAC,yBAAyB,CAAC;QACjF;;;;;WAKG;QAGI,sBAAiB,GAAG,yBAAyB,CAAC,yBAAyB,CAAC;QAEvE,aAAQ,GAA0B,IAAI,CAAC;QAC/C;;;;WAIG;QAGI,YAAO,GAA0B,IAAI,CAAC;QAErC,iCAA4B,GAAG,IAAI,CAAC;QAC5C;;;WAGG;QAGI,gCAA2B,GAAG,IAAI,CAAC;QAElC,sBAAiB,GAA0B,IAAI,CAAC;QACxD;;;WAGG;QAGI,qBAAgB,GAA0B,IAAI,CAAC;QAE9C,8BAAyB,GAAG,IAAI,CAAC;QACzC;;WAEG;QAGI,6BAAwB,GAAG,IAAI,CAAC;QAE/B,iBAAY,GAA0B,IAAI,CAAC;QACnD;;WAEG;QAGI,gBAAW,GAA0B,IAAI,CAAC;QAEzC,mBAAc,GAAG,KAAK,CAAC;QAC/B;;WAEG;QAGI,kBAAa,GAAG,KAAK,CAAC;QAE7B;;;WAGG;QAEI,cAAS,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAElC;;;;WAIG;QAEI,wBAAmB,GAAG,CAAC,CAAC;QAE/B;;;WAGG;QAEI,kBAAa,GAAW,CAAC,CAAC;QAEzB,iBAAY,GAA0B,IAAI,CAAC;QACnD;;;;WAIG;QAGI,gBAAW,GAA0B,IAAI,CAAC;QAc7C,IAAI,CAAC,wCAAwC,GAAG,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IAClH,CAAC;IAEM,iBAAiB,CAAC,OAAiC,EAAE,KAAY,EAAE,MAAc;QACpF,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;QACtD,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,IAAI,KAAK,CAAC,eAAe,EAAE;gBACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,uBAAuB,EAAE;oBACxD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE;wBACvC,OAAO,KAAK,CAAC;qBAChB;iBACJ;gBAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,aAAa,CAAC,uBAAuB,EAAE;oBACjE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE;wBAChD,OAAO,KAAK,CAAC;qBAChB;iBACJ;gBAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,IAAI,CAAC,YAAY,IAAI,aAAa,CAAC,2BAA2B,IAAI,CAAC,cAAc,EAAE;oBAC3H,uCAAuC;oBACvC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE;wBAC9B,OAAO,KAAK,CAAC;qBAChB;iBACJ;gBAED,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,IAAI,aAAa,CAAC,2BAA2B,EAAE;oBACvF,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,oBAAoB,EAAE,EAAE;wBAC3C,OAAO,KAAK,CAAC;qBAChB;iBACJ;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,8BAA8B,CAAC,OAAiC,EAAE,KAAY;QACjF,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YACzB,OAAO,CAAC,wCAAwC,GAAG,IAAI,CAAC,4BAA4B,CAAC;YACrF,OAAO,CAAC,qCAAqC;gBACzC,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,iBAAiB,EAAE,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,2BAA2B,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC/J,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,CAAC;YAE5D,IAAI,OAAO,CAAC,iBAAiB,EAAE;gBAC3B,IAAI,KAAK,CAAC,eAAe,EAAE;oBACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,uBAAuB,EAAE;wBACxD,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,mBAAmB,CAAC,CAAC;qBAC1E;yBAAM;wBACH,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;qBACrC;oBAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,aAAa,CAAC,uBAAuB,EAAE;wBACjE,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,6BAA6B,CAAC,CAAC;qBAC7F;yBAAM;wBACH,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;qBAC/C;oBAED,IAAI,IAAI,CAAC,YAAY,IAAI,aAAa,CAAC,2BAA2B,EAAE;wBAChE,yBAAyB,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;qBAC3E;yBAAM;wBACH,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;qBAClC;oBAED,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,kBAAkB,KAAK,yBAAyB,CAAC,yBAAyB,CAAC;oBAE/G,IAAI,IAAI,CAAC,cAAc,EAAE;wBACrB,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC;wBAC9B,IAAI,IAAI,CAAC,YAAY,IAAI,aAAa,CAAC,2BAA2B,EAAE;4BAChE,yBAAyB,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,wBAAwB,CAAC,CAAC;4BAChF,OAAO,CAAC,2BAA2B,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;yBACtE;6BAAM;4BACH,OAAO,CAAC,sBAAsB,GAAG,KAAK,CAAC;yBAC1C;qBACJ;yBAAM;wBACH,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;wBAC/B,OAAO,CAAC,sBAAsB,GAAG,KAAK,CAAC;qBAC1C;iBACJ;aACJ;SACJ;aAAM;YACH,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;YAC1B,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAClC,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;YAC5C,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;YAC/B,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;YAC/B,OAAO,CAAC,sBAAsB,GAAG,KAAK,CAAC;YACvC,OAAO,CAAC,wCAAwC,GAAG,KAAK,CAAC;YACzD,OAAO,CAAC,qCAAqC,GAAG,KAAK,CAAC;YACtD,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;YACrC,OAAO,CAAC,yBAAyB,GAAG,CAAC,CAAC;YACtC,OAAO,CAAC,mCAAmC,GAAG,CAAC,CAAC;YAChD,OAAO,CAAC,sBAAsB,GAAG,CAAC,CAAC;YACnC,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;YACnC,OAAO,CAAC,8BAA8B,GAAG,CAAC,CAAC;YAC3C,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;SAC/C;IACL,CAAC;IAEM,cAAc,CAAC,aAA4B,EAAE,KAAY,EAAE,MAAc,EAAE,OAAgB;QAC9F,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO;SACV;QAED,MAAM,OAAO,GAAG,OAAQ,CAAC,eAAsD,CAAC;QAEhF,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAEzC,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;QACtD,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;QAC1D,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;QAE1D,MAAM,iBAAiB,GAAG,OAAO,CAAC,qCAAqC,CAAC;QAExE,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YAC7D,IAAI,iBAAiB,IAAI,aAAa,CAAC,uBAAuB,EAAE;gBAC5D,aAAa,CAAC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,QAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,QAAS,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC7G,iBAAiB,CAAC,IAAI,CAAC,QAAS,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;aACjE;iBAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,aAAa,CAAC,uBAAuB,EAAE;gBAC3F,aAAa,CAAC,YAAY,CACtB,iBAAiB,EACjB,IAAI,CAAC,QAAQ,EAAE,gBAAgB,IAAI,CAAC,EACpC,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,EACzB,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,IAAI,CAAC,EAC7C,IAAI,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,CACrC,CAAC;gBACF,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACf,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;iBAChE;gBACD,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC,wCAAwC,EAAE;oBACnG,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,EAAE,oBAAoB,CAAC,CAAC;iBAClF;aACJ;YAED,IAAI,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,aAAa,CAAC,uBAAuB,IAAI,CAAC,cAAc,EAAE;gBACvH,aAAa,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAC/G,iBAAiB,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC;gBAErE,IAAI,KAAK,CAAC,uBAAuB,EAAE;oBAC/B,aAAa,CAAC,YAAY,CAAC,8BAA8B,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;iBAC5H;qBAAM;oBACH,aAAa,CAAC,YAAY,CAAC,8BAA8B,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;iBAC5H;aACJ;YAED,IAAI,IAAI,CAAC,YAAY,IAAI,aAAa,CAAC,2BAA2B,EAAE;gBAChE,aAAa,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAC/G,iBAAiB,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC;aACxE;YAED,4BAA4B;YAC5B,aAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAE/E,+BAA+B;YAC/B,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACtC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACtC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,4FAA4F;YAC5H,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACxC,aAAa,CAAC,YAAY,CAAC,4BAA4B,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAExE,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,aAAa,CAAC,YAAY,CAAC,sBAAsB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;gBAChJ,aAAa,CAAC,WAAW,CAAC,0BAA0B,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;aACtG;SACJ;QAED,WAAW;QACX,IAAI,KAAK,CAAC,eAAe,EAAE;YACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,CAAC,uBAAuB,EAAE;gBACxD,aAAa,CAAC,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC/D;YAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC,wCAAwC,IAAI,aAAa,CAAC,uBAAuB,EAAE;gBAC5I,aAAa,CAAC,UAAU,CAAC,2BAA2B,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;aACjF;YAED,IAAI,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,aAAa,CAAC,2BAA2B,IAAI,CAAC,cAAc,EAAE;gBAC3H,aAAa,CAAC,UAAU,CAAC,sBAAsB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;aACvE;YAED,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,IAAI,aAAa,CAAC,2BAA2B,EAAE;gBACvF,aAAa,CAAC,UAAU,CAAC,sBAAsB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;aACvE;SACJ;IACL,CAAC;IAEM,UAAU,CAAC,OAAoB;QAClC,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,OAAO,EAAE;YACpC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE;YAC/B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE;YAC/B,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,iBAAiB,CAAC,cAA6B;QAClD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACtC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC/C;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC1C;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC1C;IACL,CAAC;IAEM,cAAc,CAAC,WAA0B;QAC5C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAClF,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7G,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC5C;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9F,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACvC;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9F,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACvC;IACL,CAAC;IAEM,OAAO,CAAC,oBAA8B;QACzC,IAAI,oBAAoB,EAAE;YACtB,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC;SAChC;IACL,CAAC;IAEM,YAAY;QACf,OAAO,2BAA2B,CAAC;IACvC,CAAC;IAEM,YAAY,CAAC,OAAiC,EAAE,SAA0B,EAAE,WAAmB;QAClG,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,gBAAgB,CAAC,CAAC;SAC1D;QACD,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,gBAAgB,CAAC,CAAC;SAC1D;QACD,IAAI,OAAO,CAAC,SAAS,EAAE;YACnB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,WAAW,CAAC,CAAC;SACrD;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAEM,WAAW,CAAC,QAAkB;QACjC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,2BAA2B,EAAE,sBAAsB,EAAE,sBAAsB,CAAC,CAAC;IACnH,CAAC;IAEM,WAAW;QACd,OAAO;YACH,GAAG,EAAE;gBACD,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBACnD,EAAE,IAAI,EAAE,4BAA4B,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC7D,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAClD,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBACnD,EAAE,IAAI,EAAE,0BAA0B,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC5D,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBACtD,EAAE,IAAI,EAAE,8BAA8B,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBAC/D,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBACvD,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBACvD,EAAE,IAAI,EAAE,0BAA0B,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;gBAC5D,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;gBACtD,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;aAC1D;SACJ,CAAC;IACN,CAAC;;AA5aD;;;;GAIG;AACoB,mDAAyB,GAAG,GAAG,AAAN,CAAO;AAQhD;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;4DAC5B;AAMlB;IADN,SAAS,EAAE;4DACiB;AAMtB;IADN,SAAS,EAAE;4DACiB;AAWtB;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;oEAC0B;AAUxE;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;0DACR;AAStC;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;8EACX;AASnC;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;mEACC;AAQ/C;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;2EACd;AAQhC;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;8DACJ;AAQ1C;IAFN,SAAS,EAAE;IACX,gBAAgB,CAAC,kCAAkC,CAAC;gEACxB;AAOtB;IADN,iBAAiB,EAAE;4DACc;AAQ3B;IADN,SAAS,EAAE;sEACmB;AAOxB;IADN,SAAS,EAAE;gEACqB;AAU1B;IAFN,kBAAkB,EAAE;IACpB,gBAAgB,CAAC,kCAAkC,CAAC;8DACJ", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"../../types\";\r\nimport { serialize, serializeAsTexture, expandToProperty, serializeAsColor3 } from \"../../Misc/decorators\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { MaterialFlags } from \"../materialFlags\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport type { IAnimatable } from \"../../Animations/animatable.interface\";\r\nimport type { EffectFallbacks } from \"../effectFallbacks\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { MaterialPluginBase } from \"../materialPluginBase\";\r\nimport { MaterialDefines } from \"../materialDefines\";\r\n\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport { BindTextureMatrix, PrepareDefinesForMergedUV } from \"../materialHelper.functions\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class MaterialClearCoatDefines extends MaterialDefines {\r\n    public CLEARCOAT = false;\r\n    public CLEARCOAT_DEFAULTIOR = false;\r\n    public CLEARCOAT_TEXTURE = false;\r\n    public CLEARCOAT_TEXTURE_ROUGHNESS = false;\r\n    public CLEARCOAT_TEXTUREDIRECTUV = 0;\r\n    public CLEARCOAT_TEXTURE_ROUGHNESSDIRECTUV = 0;\r\n    public CLEARCOAT_BUMP = false;\r\n    public CLEARCOAT_BUMPDIRECTUV = 0;\r\n    public CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE = false;\r\n    public CLEARCOAT_TEXTURE_ROUGHNESS_IDENTICAL = false;\r\n    public CLEARCOAT_REMAP_F0 = false;\r\n\r\n    public CLEARCOAT_TINT = false;\r\n    public CLEARCOAT_TINT_TEXTURE = false;\r\n    public CLEARCOAT_TINT_TEXTUREDIRECTUV = 0;\r\n    public CLEARCOAT_TINT_GAMMATEXTURE = false;\r\n}\r\n\r\n/**\r\n * Plugin that implements the clear coat component of the PBR material\r\n */\r\nexport class PBRClearCoatConfiguration extends MaterialPluginBase {\r\n    protected _material: PBRBaseMaterial;\r\n\r\n    /**\r\n     * This defaults to 1.5 corresponding to a 0.04 f0 or a 4% reflectance at normal incidence\r\n     * The default fits with a polyurethane material.\r\n     * @internal\r\n     */\r\n    public static readonly _DefaultIndexOfRefraction = 1.5;\r\n\r\n    private _isEnabled = false;\r\n    /**\r\n     * Defines if the clear coat is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isEnabled = false;\r\n\r\n    /**\r\n     * Defines the clear coat layer strength (between 0 and 1) it defaults to 1.\r\n     */\r\n    @serialize()\r\n    public intensity: number = 1;\r\n\r\n    /**\r\n     * Defines the clear coat layer roughness.\r\n     */\r\n    @serialize()\r\n    public roughness: number = 0;\r\n\r\n    private _indexOfRefraction = PBRClearCoatConfiguration._DefaultIndexOfRefraction;\r\n    /**\r\n     * Defines the index of refraction of the clear coat.\r\n     * This defaults to 1.5 corresponding to a 0.04 f0 or a 4% reflectance at normal incidence\r\n     * The default fits with a polyurethane material.\r\n     * Changing the default value is more performance intensive.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public indexOfRefraction = PBRClearCoatConfiguration._DefaultIndexOfRefraction;\r\n\r\n    private _texture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the clear coat values in a texture (red channel is intensity and green channel is roughness)\r\n     * If useRoughnessFromMainTexture is false, the green channel of texture is not used and the green channel of textureRoughness is used instead\r\n     * if textureRoughness is not empty, else no texture roughness is used\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public texture: Nullable<BaseTexture> = null;\r\n\r\n    private _useRoughnessFromMainTexture = true;\r\n    /**\r\n     * Indicates that the green channel of the texture property will be used for roughness (default: true)\r\n     * If false, the green channel from textureRoughness is used for roughness\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useRoughnessFromMainTexture = true;\r\n\r\n    private _textureRoughness: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the clear coat roughness in a texture (green channel)\r\n     * Not used if useRoughnessFromMainTexture is true\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public textureRoughness: Nullable<BaseTexture> = null;\r\n\r\n    private _remapF0OnInterfaceChange = true;\r\n    /**\r\n     * Defines if the F0 value should be remapped to account for the interface change in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public remapF0OnInterfaceChange = true;\r\n\r\n    private _bumpTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Define the clear coat specific bump texture.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public bumpTexture: Nullable<BaseTexture> = null;\r\n\r\n    private _isTintEnabled = false;\r\n    /**\r\n     * Defines if the clear coat tint is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isTintEnabled = false;\r\n\r\n    /**\r\n     * Defines the clear coat tint of the material.\r\n     * This is only use if tint is enabled\r\n     */\r\n    @serializeAsColor3()\r\n    public tintColor = Color3.White();\r\n\r\n    /**\r\n     * Defines the distance at which the tint color should be found in the\r\n     * clear coat media.\r\n     * This is only use if tint is enabled\r\n     */\r\n    @serialize()\r\n    public tintColorAtDistance = 1;\r\n\r\n    /**\r\n     * Defines the clear coat layer thickness.\r\n     * This is only use if tint is enabled\r\n     */\r\n    @serialize()\r\n    public tintThickness: number = 1;\r\n\r\n    private _tintTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the clear tint values in a texture.\r\n     * rgb is tint\r\n     * a is a thickness factor\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public tintTexture: Nullable<BaseTexture> = null;\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsTexturesDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsTexturesDirty(): void {\r\n        this._enable(this._isEnabled);\r\n        this._internalMarkAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    constructor(material: PBRBaseMaterial, addToPluginList = true) {\r\n        super(material, \"PBRClearCoat\", 100, new MaterialClearCoatDefines(), addToPluginList);\r\n\r\n        this._internalMarkAllSubMeshesAsTexturesDirty = material._dirtyCallbacks[Constants.MATERIAL_TextureDirtyFlag];\r\n    }\r\n\r\n    public isReadyForSubMesh(defines: MaterialClearCoatDefines, scene: Scene, engine: Engine): boolean {\r\n        if (!this._isEnabled) {\r\n            return true;\r\n        }\r\n\r\n        const disableBumpMap = this._material._disableBumpMap;\r\n        if (defines._areTexturesDirty) {\r\n            if (scene.texturesEnabled) {\r\n                if (this._texture && MaterialFlags.ClearCoatTextureEnabled) {\r\n                    if (!this._texture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._textureRoughness && MaterialFlags.ClearCoatTextureEnabled) {\r\n                    if (!this._textureRoughness.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (engine.getCaps().standardDerivatives && this._bumpTexture && MaterialFlags.ClearCoatBumpTextureEnabled && !disableBumpMap) {\r\n                    // Bump texture cannot be not blocking.\r\n                    if (!this._bumpTexture.isReady()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._isTintEnabled && this._tintTexture && MaterialFlags.ClearCoatTintTextureEnabled) {\r\n                    if (!this._tintTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public prepareDefinesBeforeAttributes(defines: MaterialClearCoatDefines, scene: Scene): void {\r\n        if (this._isEnabled) {\r\n            defines.CLEARCOAT = true;\r\n            defines.CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE = this._useRoughnessFromMainTexture;\r\n            defines.CLEARCOAT_TEXTURE_ROUGHNESS_IDENTICAL =\r\n                this._texture !== null && this._texture._texture === this._textureRoughness?._texture && this._texture.checkTransformsAreIdentical(this._textureRoughness);\r\n            defines.CLEARCOAT_REMAP_F0 = this._remapF0OnInterfaceChange;\r\n\r\n            if (defines._areTexturesDirty) {\r\n                if (scene.texturesEnabled) {\r\n                    if (this._texture && MaterialFlags.ClearCoatTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._texture, defines, \"CLEARCOAT_TEXTURE\");\r\n                    } else {\r\n                        defines.CLEARCOAT_TEXTURE = false;\r\n                    }\r\n\r\n                    if (this._textureRoughness && MaterialFlags.ClearCoatTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._textureRoughness, defines, \"CLEARCOAT_TEXTURE_ROUGHNESS\");\r\n                    } else {\r\n                        defines.CLEARCOAT_TEXTURE_ROUGHNESS = false;\r\n                    }\r\n\r\n                    if (this._bumpTexture && MaterialFlags.ClearCoatBumpTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._bumpTexture, defines, \"CLEARCOAT_BUMP\");\r\n                    } else {\r\n                        defines.CLEARCOAT_BUMP = false;\r\n                    }\r\n\r\n                    defines.CLEARCOAT_DEFAULTIOR = this._indexOfRefraction === PBRClearCoatConfiguration._DefaultIndexOfRefraction;\r\n\r\n                    if (this._isTintEnabled) {\r\n                        defines.CLEARCOAT_TINT = true;\r\n                        if (this._tintTexture && MaterialFlags.ClearCoatTintTextureEnabled) {\r\n                            PrepareDefinesForMergedUV(this._tintTexture, defines, \"CLEARCOAT_TINT_TEXTURE\");\r\n                            defines.CLEARCOAT_TINT_GAMMATEXTURE = this._tintTexture.gammaSpace;\r\n                        } else {\r\n                            defines.CLEARCOAT_TINT_TEXTURE = false;\r\n                        }\r\n                    } else {\r\n                        defines.CLEARCOAT_TINT = false;\r\n                        defines.CLEARCOAT_TINT_TEXTURE = false;\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            defines.CLEARCOAT = false;\r\n            defines.CLEARCOAT_TEXTURE = false;\r\n            defines.CLEARCOAT_TEXTURE_ROUGHNESS = false;\r\n            defines.CLEARCOAT_BUMP = false;\r\n            defines.CLEARCOAT_TINT = false;\r\n            defines.CLEARCOAT_TINT_TEXTURE = false;\r\n            defines.CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE = false;\r\n            defines.CLEARCOAT_TEXTURE_ROUGHNESS_IDENTICAL = false;\r\n            defines.CLEARCOAT_DEFAULTIOR = false;\r\n            defines.CLEARCOAT_TEXTUREDIRECTUV = 0;\r\n            defines.CLEARCOAT_TEXTURE_ROUGHNESSDIRECTUV = 0;\r\n            defines.CLEARCOAT_BUMPDIRECTUV = 0;\r\n            defines.CLEARCOAT_REMAP_F0 = false;\r\n            defines.CLEARCOAT_TINT_TEXTUREDIRECTUV = 0;\r\n            defines.CLEARCOAT_TINT_GAMMATEXTURE = false;\r\n        }\r\n    }\r\n\r\n    public bindForSubMesh(uniformBuffer: UniformBuffer, scene: Scene, engine: Engine, subMesh: SubMesh): void {\r\n        if (!this._isEnabled) {\r\n            return;\r\n        }\r\n\r\n        const defines = subMesh!.materialDefines as unknown as MaterialClearCoatDefines;\r\n\r\n        const isFrozen = this._material.isFrozen;\r\n\r\n        const disableBumpMap = this._material._disableBumpMap;\r\n        const invertNormalMapX = this._material._invertNormalMapX;\r\n        const invertNormalMapY = this._material._invertNormalMapY;\r\n\r\n        const identicalTextures = defines.CLEARCOAT_TEXTURE_ROUGHNESS_IDENTICAL;\r\n\r\n        if (!uniformBuffer.useUbo || !isFrozen || !uniformBuffer.isSync) {\r\n            if (identicalTextures && MaterialFlags.ClearCoatTextureEnabled) {\r\n                uniformBuffer.updateFloat4(\"vClearCoatInfos\", this._texture!.coordinatesIndex, this._texture!.level, -1, -1);\r\n                BindTextureMatrix(this._texture!, uniformBuffer, \"clearCoat\");\r\n            } else if ((this._texture || this._textureRoughness) && MaterialFlags.ClearCoatTextureEnabled) {\r\n                uniformBuffer.updateFloat4(\r\n                    \"vClearCoatInfos\",\r\n                    this._texture?.coordinatesIndex ?? 0,\r\n                    this._texture?.level ?? 0,\r\n                    this._textureRoughness?.coordinatesIndex ?? 0,\r\n                    this._textureRoughness?.level ?? 0\r\n                );\r\n                if (this._texture) {\r\n                    BindTextureMatrix(this._texture, uniformBuffer, \"clearCoat\");\r\n                }\r\n                if (this._textureRoughness && !identicalTextures && !defines.CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE) {\r\n                    BindTextureMatrix(this._textureRoughness, uniformBuffer, \"clearCoatRoughness\");\r\n                }\r\n            }\r\n\r\n            if (this._bumpTexture && engine.getCaps().standardDerivatives && MaterialFlags.ClearCoatTextureEnabled && !disableBumpMap) {\r\n                uniformBuffer.updateFloat2(\"vClearCoatBumpInfos\", this._bumpTexture.coordinatesIndex, this._bumpTexture.level);\r\n                BindTextureMatrix(this._bumpTexture, uniformBuffer, \"clearCoatBump\");\r\n\r\n                if (scene._mirroredCameraPosition) {\r\n                    uniformBuffer.updateFloat2(\"vClearCoatTangentSpaceParams\", invertNormalMapX ? 1.0 : -1.0, invertNormalMapY ? 1.0 : -1.0);\r\n                } else {\r\n                    uniformBuffer.updateFloat2(\"vClearCoatTangentSpaceParams\", invertNormalMapX ? -1.0 : 1.0, invertNormalMapY ? -1.0 : 1.0);\r\n                }\r\n            }\r\n\r\n            if (this._tintTexture && MaterialFlags.ClearCoatTintTextureEnabled) {\r\n                uniformBuffer.updateFloat2(\"vClearCoatTintInfos\", this._tintTexture.coordinatesIndex, this._tintTexture.level);\r\n                BindTextureMatrix(this._tintTexture, uniformBuffer, \"clearCoatTint\");\r\n            }\r\n\r\n            // Clear Coat General params\r\n            uniformBuffer.updateFloat2(\"vClearCoatParams\", this.intensity, this.roughness);\r\n\r\n            // Clear Coat Refraction params\r\n            const a = 1 - this._indexOfRefraction;\r\n            const b = 1 + this._indexOfRefraction;\r\n            const f0 = Math.pow(-a / b, 2); // Schlicks approx: (ior1 - ior2) / (ior1 + ior2) where ior2 for air is close to vacuum = 1.\r\n            const eta = 1 / this._indexOfRefraction;\r\n            uniformBuffer.updateFloat4(\"vClearCoatRefractionParams\", f0, eta, a, b);\r\n\r\n            if (this._isTintEnabled) {\r\n                uniformBuffer.updateFloat4(\"vClearCoatTintParams\", this.tintColor.r, this.tintColor.g, this.tintColor.b, Math.max(0.00001, this.tintThickness));\r\n                uniformBuffer.updateFloat(\"clearCoatColorAtDistance\", Math.max(0.00001, this.tintColorAtDistance));\r\n            }\r\n        }\r\n\r\n        // Textures\r\n        if (scene.texturesEnabled) {\r\n            if (this._texture && MaterialFlags.ClearCoatTextureEnabled) {\r\n                uniformBuffer.setTexture(\"clearCoatSampler\", this._texture);\r\n            }\r\n\r\n            if (this._textureRoughness && !identicalTextures && !defines.CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE && MaterialFlags.ClearCoatTextureEnabled) {\r\n                uniformBuffer.setTexture(\"clearCoatRoughnessSampler\", this._textureRoughness);\r\n            }\r\n\r\n            if (this._bumpTexture && engine.getCaps().standardDerivatives && MaterialFlags.ClearCoatBumpTextureEnabled && !disableBumpMap) {\r\n                uniformBuffer.setTexture(\"clearCoatBumpSampler\", this._bumpTexture);\r\n            }\r\n\r\n            if (this._isTintEnabled && this._tintTexture && MaterialFlags.ClearCoatTintTextureEnabled) {\r\n                uniformBuffer.setTexture(\"clearCoatTintSampler\", this._tintTexture);\r\n            }\r\n        }\r\n    }\r\n\r\n    public hasTexture(texture: BaseTexture): boolean {\r\n        if (this._texture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._textureRoughness === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._bumpTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._tintTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public getActiveTextures(activeTextures: BaseTexture[]): void {\r\n        if (this._texture) {\r\n            activeTextures.push(this._texture);\r\n        }\r\n\r\n        if (this._textureRoughness) {\r\n            activeTextures.push(this._textureRoughness);\r\n        }\r\n\r\n        if (this._bumpTexture) {\r\n            activeTextures.push(this._bumpTexture);\r\n        }\r\n\r\n        if (this._tintTexture) {\r\n            activeTextures.push(this._tintTexture);\r\n        }\r\n    }\r\n\r\n    public getAnimatables(animatables: IAnimatable[]): void {\r\n        if (this._texture && this._texture.animations && this._texture.animations.length > 0) {\r\n            animatables.push(this._texture);\r\n        }\r\n\r\n        if (this._textureRoughness && this._textureRoughness.animations && this._textureRoughness.animations.length > 0) {\r\n            animatables.push(this._textureRoughness);\r\n        }\r\n\r\n        if (this._bumpTexture && this._bumpTexture.animations && this._bumpTexture.animations.length > 0) {\r\n            animatables.push(this._bumpTexture);\r\n        }\r\n\r\n        if (this._tintTexture && this._tintTexture.animations && this._tintTexture.animations.length > 0) {\r\n            animatables.push(this._tintTexture);\r\n        }\r\n    }\r\n\r\n    public dispose(forceDisposeTextures?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            this._texture?.dispose();\r\n            this._textureRoughness?.dispose();\r\n            this._bumpTexture?.dispose();\r\n            this._tintTexture?.dispose();\r\n        }\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"PBRClearCoatConfiguration\";\r\n    }\r\n\r\n    public addFallbacks(defines: MaterialClearCoatDefines, fallbacks: EffectFallbacks, currentRank: number): number {\r\n        if (defines.CLEARCOAT_BUMP) {\r\n            fallbacks.addFallback(currentRank++, \"CLEARCOAT_BUMP\");\r\n        }\r\n        if (defines.CLEARCOAT_TINT) {\r\n            fallbacks.addFallback(currentRank++, \"CLEARCOAT_TINT\");\r\n        }\r\n        if (defines.CLEARCOAT) {\r\n            fallbacks.addFallback(currentRank++, \"CLEARCOAT\");\r\n        }\r\n        return currentRank;\r\n    }\r\n\r\n    public getSamplers(samplers: string[]): void {\r\n        samplers.push(\"clearCoatSampler\", \"clearCoatRoughnessSampler\", \"clearCoatBumpSampler\", \"clearCoatTintSampler\");\r\n    }\r\n\r\n    public getUniforms(): { ubo?: Array<{ name: string; size: number; type: string }>; vertex?: string; fragment?: string } {\r\n        return {\r\n            ubo: [\r\n                { name: \"vClearCoatParams\", size: 2, type: \"vec2\" },\r\n                { name: \"vClearCoatRefractionParams\", size: 4, type: \"vec4\" },\r\n                { name: \"vClearCoatInfos\", size: 4, type: \"vec4\" },\r\n                { name: \"clearCoatMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"clearCoatRoughnessMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"vClearCoatBumpInfos\", size: 2, type: \"vec2\" },\r\n                { name: \"vClearCoatTangentSpaceParams\", size: 2, type: \"vec2\" },\r\n                { name: \"clearCoatBumpMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"vClearCoatTintParams\", size: 4, type: \"vec4\" },\r\n                { name: \"clearCoatColorAtDistance\", size: 1, type: \"float\" },\r\n                { name: \"vClearCoatTintInfos\", size: 2, type: \"vec2\" },\r\n                { name: \"clearCoatTintMatrix\", size: 16, type: \"mat4\" },\r\n            ],\r\n        };\r\n    }\r\n}\r\n"]}