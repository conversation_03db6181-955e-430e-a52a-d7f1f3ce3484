{"version": 3, "file": "shadowGenerator.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Lights/Shadows/shadowGenerator.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AACnE,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAMpD,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAG3C,OAAO,EAAE,OAAO,EAAE,MAAM,kCAAkC,CAAC;AAC3D,OAAO,EAAE,mBAAmB,EAAE,MAAM,8CAA8C,CAAC;AAEnF,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,qCAAqC,CAAC;AACtE,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAClE,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AACpE,OAAO,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAC;AAI1D,OAAO,kCAAkC,CAAC;AAC1C,OAAO,gCAAgC,CAAC;AACxC,OAAO,qCAAqC,CAAC;AAC7C,OAAO,qEAAqE,CAAC;AAC7E,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,iCAAiC,EAAE,MAAM,yCAAyC,CAAC;AAEjI,OAAO,EACH,yBAAyB,EACzB,sBAAsB,EACtB,2CAA2C,EAC3C,0BAA0B,GAC7B,MAAM,0CAA0C,CAAC;AA0GlD;;;;GAIG;AACH,MAAM,OAAO,eAAe;IAmHxB;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IACD;;OAEG;IACH,IAAW,IAAI,CAAC,IAAY;QACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACtB,CAAC;IAGD;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;OAEG;IACH,IAAW,UAAU,CAAC,UAAkB;QACpC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAClC,CAAC;IAGD;;;OAGG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IACD;;;OAGG;IACH,IAAW,aAAa,CAAC,KAAa;QAClC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAGD;;;OAGG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD;;;OAGG;IACH,IAAW,SAAS,CAAC,KAAa;QAC9B,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE;YAC3B,OAAO;SACV;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAGD;;;OAGG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;;OAGG;IACH,IAAW,UAAU,CAAC,KAAa;QAC/B,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;YAC5B,OAAO;SACV;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAGD;;;OAGG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IACD;;;OAGG;IACH,IAAW,aAAa,CAAC,KAAc;QACnC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAGD;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;IAC3F,CAAC;IACD;;;OAGG;IACH,IAAW,UAAU,CAAC,KAAa;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAES,eAAe,CAAC,MAAc;QACpC,OAAO,MAAM,CAAC;IAClB,CAAC;IAGD;;;OAGG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IACD;;;OAGG;IACH,IAAW,MAAM,CAAC,KAAa;QAC3B,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAEpC,oFAAoF;QACpF,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE;YACxB,IAAI,KAAK,KAAK,eAAe,CAAC,+BAA+B,EAAE;gBAC3D,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;gBACpC,OAAO;aACV;iBAAM,IAAI,KAAK,KAAK,eAAe,CAAC,oCAAoC,EAAE;gBACvE,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;gBACzC,OAAO;aACV;YACD,yCAAyC;iBACpC,IAAI,KAAK,KAAK,eAAe,CAAC,UAAU,IAAI,KAAK,KAAK,eAAe,CAAC,WAAW,EAAE;gBACpF,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAC/B,OAAO;aACV;SACJ;QAED,2BAA2B;QAC3B,IAAI,KAAK,KAAK,eAAe,CAAC,UAAU,IAAI,KAAK,KAAK,eAAe,CAAC,WAAW,EAAE;YAC/E,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,qBAAqB,EAAE;gBAC1D,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAC/B,OAAO;aACV;SACJ;QAED,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;YACxB,OAAO;SACV;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,sBAAsB,CAAC;IAClE,CAAC;IACD;;OAEG;IACH,IAAW,kBAAkB,CAAC,KAAc;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC;QAE5E,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,sBAAsB,EAAE;YAClE,OAAO;SACV;QAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,2BAA2B,CAAC;IACvE,CAAC;IACD;;OAEG;IACH,IAAW,uBAAuB,CAAC,KAAc;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,2BAA2B,CAAC,CAAC;QAEjF,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,2BAA2B,EAAE;YACvE,OAAO;SACV;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,IAAW,2BAA2B;QAClC,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,+BAA+B,CAAC;IAC3E,CAAC;IACD;;OAEG;IACH,IAAW,2BAA2B,CAAC,KAAc;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,+BAA+B,CAAC,CAAC;QAErF,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,+BAA+B,EAAE;YAC3E,OAAO;SACV;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAED;;;OAGG;IACH,IAAW,4BAA4B;QACnC,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,gCAAgC,CAAC;IAC5E,CAAC;IACD;;;OAGG;IACH,IAAW,4BAA4B,CAAC,KAAc;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,gCAAgC,CAAC,CAAC;QAEtF,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,gCAAgC,EAAE;YAC5E,OAAO;SACV;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAED;;;OAGG;IACH,IAAW,gCAAgC;QACvC,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,oCAAoC,CAAC;IAChF,CAAC;IACD;;;OAGG;IACH,IAAW,gCAAgC,CAAC,KAAc;QACtD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,oCAAoC,CAAC,CAAC;QAE1F,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,oCAAoC,EAAE;YAChF,OAAO;SACV;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,IAAW,4BAA4B;QACnC,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,UAAU,CAAC;IACtD,CAAC;IACD;;OAEG;IACH,IAAW,4BAA4B,CAAC,KAAc;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAEhE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,UAAU,EAAE;YACtD,OAAO;SACV;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAGD;;;OAGG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IACD;;;OAGG;IACH,IAAW,gBAAgB,CAAC,gBAAwB;QAChD,IAAI,IAAI,CAAC,iBAAiB,KAAK,gBAAgB,EAAE;YAC7C,OAAO;SACV;QAED,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAE1C,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,WAAW,CAAC;IACvD,CAAC;IACD;;OAEG;IACH,IAAW,yBAAyB,CAAC,KAAc;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAEjE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,WAAW,EAAE;YACvD,OAAO;SACV;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAGD;;;;;;;;OAQG;IACH,IAAW,gCAAgC;QACvC,OAAO,IAAI,CAAC,iCAAiC,CAAC;IAClD,CAAC;IACD;;;;;;;;OAQG;IACH,IAAW,gCAAgC,CAAC,gCAAwC;QAChF,IAAI,CAAC,iCAAiC,GAAG,gCAAgC,CAAC;IAC9E,CAAC;IAID,mDAAmD;IACnD,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa;QAC7B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IACD;;;;OAIG;IACI,WAAW,CAAC,QAAgB;QAC/B,IAAI,QAAQ,IAAI,GAAG,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;SACxB;aAAM,IAAI,QAAQ,IAAI,GAAG,EAAE;YACxB,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;SACxB;aAAM;YACH,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;SAC7B;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAID,2DAA2D;IAC3D,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAc;QACxC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,WAAoB;QAC7C,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC;QACvC,OAAO,IAAI,CAAC;IAChB,CAAC;IAoBD;;;OAGG;IACI,YAAY;QACf,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,wBAAwB;QAC3B,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,OAAO,IAAI,CAAC,WAAW,CAAC;SAC3B;QAED,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,eAAe,CAAC,SAAS,CAAC;IACrC,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,IAAkB,EAAE,kBAAkB,GAAG,IAAI;QAChE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;YAC7B,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACjD,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzC;QAED,IAAI,kBAAkB,EAAE;YACpB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;gBAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;oBACtD,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBAC9C;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,IAAkB,EAAE,kBAAkB,GAAG,IAAI;QACnE,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;YACjD,OAAO,IAAI,CAAC;SACf;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEvD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC/C;QAED,IAAI,kBAAkB,EAAE;YACpB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;gBACpC,IAAI,CAAC,kBAAkB,CAAM,KAAK,CAAC,CAAC;aACvC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAQD;;;OAGG;IACI,QAAQ;QACX,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAWS,UAAU;QAChB,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IACpD,CAAC;IAmCD;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,IAAY;QAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;QACtC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;;;;;;OAUG;IACH,YAAY,OAAe,EAAE,KAAmB,EAAE,iBAA2B,EAAE,MAAyB,EAAE,iBAA2B;QAjnBrI;;WAEG;QACI,sCAAiC,GAAG,IAAI,UAAU,EAAU,CAAC;QAEpE;;WAEG;QACI,qCAAgC,GAAG,IAAI,UAAU,EAAU,CAAC;QAEnE;;;WAGG;QACI,0CAAqC,GAAG,IAAI,UAAU,EAAQ,CAAC;QAEtE;;;WAGG;QACI,yCAAoC,GAAG,IAAI,UAAU,EAAQ,CAAC;QAE3D,UAAK,GAAG,OAAO,CAAC;QAchB,gBAAW,GAAG,CAAC,CAAC;QAchB,mBAAc,GAAG,CAAC,CAAC;QAqBnB,eAAU,GAAG,CAAC,CAAC;QAqBf,gBAAW,GAAG,CAAC,CAAC;QAqBhB,mBAAc,GAAG,KAAK,CAAC;QAwCvB,YAAO,GAAG,eAAe,CAAC,WAAW,CAAC;QAkKtC,sBAAiB,GAAG,eAAe,CAAC,YAAY,CAAC;QA0CjD,sCAAiC,GAAG,GAAG,CAAC;QA0BxC,cAAS,GAAG,CAAC,CAAC;QAmCd,wBAAmB,GAAG,KAAK,CAAC;QAqBtC;;;;;;;WAOG;QACI,gCAA2B,GAAY,KAAK,CAAC;QAEpD;;WAEG;QACI,0CAAqC,GAAY,KAAK,CAAC;QAyF9D;;WAEG;QACI,uBAAkB,GAAG,CAAC,CAAC;QAW9B;;;;WAIG;QACI,uBAAkB,GAAG,KAAK,CAAC;QAUxB,oBAAe,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEjC,gBAAW,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAC5B,sBAAiB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAClC,qBAAgB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QACjC,oBAAe,GAAY,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC7F,qBAAgB,GAAY,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAQ9F,sBAAiB,GAAG,CAAC,CAAC;QACtB,2BAAsB,GAAG,CAAC,CAAC;QAE3B,0BAAqB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAuChD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,iBAAiB,CAAC;QAE9C,IAAI,gBAAgB,GAAG,KAAK,CAAC,iBAAiB,CAAC;QAC/C,IAAI,CAAC,gBAAgB,EAAE;YACnB,gBAAgB,GAAG,KAAK,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;SAC1D;QACD,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,sBAAsB,CAAC;QAE9D,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;YACrB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,sCAAsC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;SAC1H;QAED,eAAe,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE3D,4DAA4D;QAC5D,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC;QAE/C,IAAI,CAAC,iBAAiB,EAAE;YACpB,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,+BAA+B,EAAE;gBACrE,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,sBAAsB,CAAC;aACxD;iBAAM,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBACpE,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,iBAAiB,CAAC;aACnD;iBAAM;gBACH,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,wBAAwB,CAAC;aAC1D;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBAC7D,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,iBAAiB,CAAC;aACnD;iBAAM,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,+BAA+B,EAAE;gBAC5E,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,sBAAsB,CAAC;aACxD;iBAAM;gBACH,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,wBAAwB,CAAC;aAC1D;SACJ;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAES,oBAAoB;QAC1B,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;QACtC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAES,0BAA0B;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,IAAI,MAAM,CAAC,SAAS,CAAC,0BAA0B,EAAE;YAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,mBAAmB,CACrC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,YAAY,EAC/B,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,MAAM,EACX,KAAK,EACL,IAAI,EACJ,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EACtB,SAAS,EACT,KAAK,EACL,KAAK,EACL,SAAS,EACT,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CACvF,CAAC;YACF,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACtH;aAAM;YACH,IAAI,CAAC,UAAU,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;SAClK;QACD,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAC7C,CAAC;IAES,oBAAoB;QAC1B,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;YAC1B,OAAO;SACV;QAED,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;QAClD,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;QAClD,IAAI,CAAC,UAAU,CAAC,yBAAyB,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAClE,IAAI,CAAC,UAAU,CAAC,eAAe,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,UAAU,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAC5C,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC;SACnD;QAED,0BAA0B;QAC1B,IAAI,CAAC,UAAU,CAAC,oBAAoB,GAAG,CACnC,eAAoC,EACpC,kBAAuC,EACvC,oBAAyC,EACzC,kBAAuC,EACzC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;QAE7G,wEAAwE;QACxE,2EAA2E;QAC3E,0CAA0C;QAC1C,IAAI,CAAC,UAAU,CAAC,qBAAqB,GAAG,GAAG,EAAE;YACzC,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;YAC5D,MAAM,CAAC,eAAe,EAAE,CAAC,qCAAqC,MAAM,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,SAAiB,EAAE,EAAE;YAC/D,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aACzD;YACD,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;YACnC,IAAI,IAAI,CAAC,OAAO,KAAK,eAAe,CAAC,UAAU,EAAE;gBAC7C,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aAC/B;YACD,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,sCAAsC;YACjE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACzE,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,YAAY,EAAE,CAAC;gBACnD,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;aAClC;QACL,CAAC,CAAC,CAAC;QAEH,iCAAiC;QACjC,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC7C,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAC5D;YACD,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,CAAC,4DAA4D;YAEjG,IAAI,IAAI,CAAC,OAAO,KAAK,eAAe,CAAC,UAAU,EAAE;gBAC7C,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;aAC9B;YACD,IAAI,CAAC,IAAI,CAAC,2BAA2B,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBAC7E,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC3B,OAAO;aACV;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAElD,IAAI,SAAS,EAAE;gBACX,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;gBACnG,MAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,YAAa,EAAE,IAAI,CAAC,CAAC;gBACxD,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;aAC9B;QACL,CAAC,CAAC,CAAC;QAEH,wCAAwC;QACxC,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,OAAO,KAAK,eAAe,CAAC,UAAU,EAAE;gBAC7C,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;aAC9C;iBAAM,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBACzE,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;aAC9C;iBAAM;gBACH,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;aAC7C;QACL,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YAC3C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAW,CAAC,QAAQ,CAAC;YACjD,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,aAAa,EAAE,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;YACtC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,2DAA2D;QAC3D,4CAA4C;QAC5C,KAAK,IAAI,CAAC,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,EAAE,EAAE;YAC9F,IAAI,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;SAC/D;IACL,CAAC;IAES,kCAAkC;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAElD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,SAAS,KAAK,GAAG,EAAE;YAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,aAAa,EAAE,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YACnK,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;YACnD,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;YACnD,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;SACtE;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,uBAAuB,GAAG,IAAI,eAAe,CAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,aAAa,EAChC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EACjB,IAAI,CAAC,UAAU,EACf,GAAG,EACH,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,MAAM,EACN,KAAK,EACL,IAAI,CAAC,YAAY,CACpB,CAAC;YACF,IAAI,CAAC,uBAAuB,CAAC,KAAK,GAAG,UAAU,CAAC;YAChD,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,UAAU,CAAC;YACjD,IAAI,CAAC,uBAAuB,CAAC,6BAA6B,GAAG,IAAI,CAAC;YAClE,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC1D,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,uBAAuB,GAAG,IAAI,eAAe,CAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,aAAa,EAChC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EACjB,IAAI,CAAC,UAAU,EACf,GAAG,EACH,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,MAAM,EACN,KAAK,EACL,IAAI,CAAC,YAAY,CACpB,CAAC;YAEF,IAAI,CAAC,uBAAuB,CAAC,SAAS,GAAG,KAAK,CAAC;YAC/C,IAAI,CAAC,uBAAuB,CAAC,SAAS,GAAG,KAAK,CAAC;YAE/C,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,CAAC,wBAAwB,EAAE;gBACxC,IAAI,CAAC,uBAAwB,CAAC,WAAW,GAAG,IAAI,CAAC;gBACjD,IAAI,CAAC,uBAAwB,CAAC,WAAW,GAAG,IAAI,CAAC;aACtE;YAED,IAAI,CAAC,kBAAkB,GAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;SAC1F;aAAM;YACH,IAAI,CAAC,mBAAmB,GAAG,IAAI,WAAW,CACtC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,cAAc,EACjC,cAAc,EACd,CAAC,YAAY,EAAE,WAAW,CAAC,EAC3B,EAAE,EACF,GAAG,EACH,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,MAAM,EACN,KAAK,EACL,iBAAiB,GAAG,IAAI,CAAC,cAAc,EACvC,IAAI,CAAC,YAAY,CACpB,CAAC;YACF,IAAI,CAAC,mBAAmB,CAAC,6BAA6B,GAAG,IAAI,CAAC;YAC9D,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACtD,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;gBACvD,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,mBAAmB,CAAC,SAAS,GAAG,KAAK,CAAC;YAE3C,IAAI,CAAC,kBAAkB,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACxD;IACL,CAAC;IAES,mBAAmB,CACzB,eAAoC,EACpC,kBAAuC,EACvC,oBAAyC,EACzC,kBAAuC;QAEvC,IAAI,KAAa,CAAC;QAElB,IAAI,kBAAkB,CAAC,MAAM,EAAE;YAC3B,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACxD,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aACnE;SACJ;QAED,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;SAChE;QAED,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACxD,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;SACnE;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC1D,IAAI,CAAC,0BAA0B,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;aAC3E;SACJ;aAAM;YACH,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC1D,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC,6BAA6B,CAAC,qBAAqB,GAAG,KAAK,CAAC;aACnH;SACJ;IACL,CAAC;IAED,6DAA6D;IACnD,6CAA6C,CAAC,OAAgB,EAAE,MAAc,EAAE,IAAkB;QACxG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;IAClE,CAAC;IAES,0BAA0B,CAAC,OAAgB,EAAE,gBAAyB,KAAK;QACjF,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACjD,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAEvC,aAAa,CAAC,6BAA6B,CAAC,qBAAqB,GAAG,KAAK,CAAC;QAE1E,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,aAAa,KAAK,CAAC,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,WAAW,EAAE,EAAE;YACvF,OAAO;SACV;QAED,UAAU;QACV,MAAM,MAAM,GAAG,aAAa,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;QAC9D,IAAI,eAAe,GAAG,aAAa,CAAC,+BAA+B,IAAI,QAAQ,CAAC,eAAe,CAAC;QAChG,IAAI,MAAM,EAAE;YACR,eAAe;gBACX,eAAe,KAAK,SAAS,CAAC,iCAAiC,CAAC,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC;SAC1K;QACD,MAAM,sBAAsB,GAAG,eAAe,KAAK,SAAS,CAAC,iCAAiC,CAAC;QAE/F,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,sBAAsB,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;QAEhH,qBAAqB;QACrB,MAAM,KAAK,GAAG,aAAa,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC;QACjG,IAAI,KAAK,CAAC,UAAU,EAAE;YAClB,OAAO;SACV;QAED,MAAM,0BAA0B,GAC5B,MAAM,CAAC,OAAO,EAAE,CAAC,eAAe;YAChC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,IAAI,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAE5I,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE;YAClE,OAAO;SACV;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,0BAA0B,EAAE,aAAa,CAAC,EAAE;YAClE,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;YAExC,MAAM,kBAAkB,GAAG,QAAQ,CAAC,kBAAkB,CAAC;YAEvD,MAAM,WAAW,GAAG,kBAAkB,EAAE,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,mBAAmB,CAAC,IAAI,OAAO,CAAC,eAAe,EAAG,CAAC;YAC3H,MAAM,MAAM,GAAG,WAAW,CAAC,SAAS,CAAC,WAAW,CAAE,CAAC;YAEnD,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAEjC,IAAI,CAAC,0BAA0B,EAAE;gBAC7B,aAAa,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;aAC3D;YAED,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,+DAA+D;YAE1F,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAEhF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC,4BAA4B,EAAE;gBACpE,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAC3D;iBAAM;gBACH,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;aAC1D;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACjC,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;aACxJ;YAED,IAAI,aAAa,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBACnD,MAAM,CAAC,QAAQ,CAAC,yBAAyB,EAAE,aAAa,CAAC,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;aACzF;YAED,IAAI,kBAAkB,EAAE;gBACpB,OAAO,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;gBACjD,IAAI,kBAAkB,CAAC,UAAU,EAAE;oBAC/B,kBAAkB,CAAC,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,cAAc,EAAE,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;iBAC1G;qBAAM;oBACH,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,cAAc,EAAE,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;iBACnF;gBACD,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;aAC7C;iBAAM;gBACH,aAAa;gBACb,IAAI,IAAI,CAAC,eAAe,EAAE;oBACtB,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBAC1D,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC;iBAC5G;gBAED,QAAQ;gBACR,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,wBAAwB,IAAI,aAAa,CAAC,QAAQ,EAAE;oBAC5F,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;oBAExC,IAAI,QAAQ,CAAC,yBAAyB,EAAE;wBACpC,MAAM,WAAW,GAAG,QAAQ,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAC;wBAEtE,IAAI,CAAC,WAAW,EAAE;4BACd,OAAO;yBACV;wBAED,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;wBAC9C,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;qBAC1E;yBAAM;wBACH,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC;qBAC9E;iBACJ;gBAED,gBAAgB;gBAChB,yBAAyB,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;gBAEjD,IAAI,aAAa,CAAC,kBAAkB,IAAI,aAAa,CAAC,kBAAkB,CAAC,wBAAwB,EAAE;oBAC/F,aAAa,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;iBAClD;gBAED,cAAc;gBACd,aAAa,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;aAC1C;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,kBAAkB,EAAE;gBACtC,IAAI,CAAC,6CAA6C,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;aACtF;YAED,sBAAsB,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,CAAC;YACpE,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,iBAAiB,EAAE,CAAC;YAExD,MAAM,KAAK,GAAG,aAAa,CAAC,cAAc,EAAE,CAAC;YAE7C,wIAAwI;YACxI,IAAI,0BAA0B,EAAE;gBAC5B,aAAa,CAAC,oBAAoB,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAClE,aAAa,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;aACzC;YAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;aACjE;YAED,cAAc;YACd,IAAI,CAAC,qCAAqC,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAC1E,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAE/D,OAAO;YACP,aAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,EAAE;gBAChJ,IAAI,aAAa,KAAK,aAAa,IAAI,CAAC,UAAU,EAAE;oBAChD,aAAa,CAAC,oBAAoB,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;oBAClE,aAAa,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;iBACjD;qBAAM;oBACH,aAAa,CAAC,oBAAoB,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;oBAClE,aAAa,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;iBACtE;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;aAClE;YAED,cAAc;YACd,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC9D,IAAI,CAAC,oCAAoC,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;SAC5E;aAAM;YACH,8CAA8C;YAC9C,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC;aACzC;SACJ;IACL,CAAC;IAES,kBAAkB;QACxB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO;SACV;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,WAAW,EAAE;YAC5F,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;SACpE;aAAM;YACH,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;SACrE;IACL,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,UAAkD,EAAE,OAA4C;QACpH,MAAM,YAAY,GAAG;YACjB,YAAY,EAAE,KAAK;YACnB,GAAG,OAAO;SACb,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,IAAI,CAAC,SAAS,EAAE;YACZ,IAAI,UAAU,EAAE;gBACZ,UAAU,CAAC,IAAI,CAAC,CAAC;aACpB;YACD,OAAO;SACV;QAED,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;QACxC,IAAI,CAAC,UAAU,EAAE;YACb,IAAI,UAAU,EAAE;gBACZ,UAAU,CAAC,IAAI,CAAC,CAAC;aACpB;YACD,OAAO;SACV;QAED,MAAM,SAAS,GAAc,EAAE,CAAC;QAChC,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE;YAC3B,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;SACrC;QACD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,IAAI,UAAU,EAAE;gBACZ,UAAU,CAAC,IAAI,CAAC,CAAC;aACpB;YACD,OAAO;SACV;QAED,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,MAAM,UAAU,GAAG,GAAG,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE;gBAC1C,OAAO;aACV;YAED,OACI,IAAI,CAAC,OAAO,CACR,SAAS,CAAC,YAAY,CAAC,EACvB,YAAY,CAAC,YAAY,EACzB,SAAS,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,EAAE,wBAAwB,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,KAAK,CAC9G,EACH;gBACE,YAAY,EAAE,CAAC;gBACf,IAAI,YAAY,IAAI,SAAS,CAAC,MAAM,EAAE;oBAClC,IAAI,UAAU,EAAE;wBACZ,UAAU,CAAC,IAAI,CAAC,CAAC;qBACpB;oBACD,OAAO;iBACV;aACJ;YACD,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QAC/B,CAAC,CAAC;QAEF,UAAU,EAAE,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,OAA4C;QACrE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;gBACvB,OAAO,EAAE,CAAC;YACd,CAAC,EAAE,OAAO,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,6DAA6D;IACnD,qBAAqB,CAAC,OAAY,EAAE,OAAgB,EAAE,YAAqB,IAAS,CAAC;IAEvF,qBAAqB,CAAC,OAAgB,EAAE,YAAqB,EAAE,OAAiB,EAAE,aAAsB;QAC5G,OAAO,CAAC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;QAEjF,OAAO,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,YAAY,KAAK,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE3G,OAAO,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEjH,OAAO,CAAC,IAAI,CAAC,0BAA0B,GAAG,CAAC,IAAI,CAAC,4BAA4B,IAAI,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE7H,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QAE/B,eAAe;QACf,OAAO,CAAC,IAAI,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9H,OAAO,CAAC,IAAI,CAAC,kCAAkC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEpI,cAAc;QACd,OAAO,CAAC,IAAI,CAAC,yBAAyB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE/E,2BAA2B;QAC3B,OAAO,CAAC,IAAI,CAAC,mCAAmC,GAAG,CAAC,IAAI,CAAC,2BAA2B,IAAI,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEpH,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAE3D,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;OAMG;IACI,OAAO,CAAC,OAAgB,EAAE,YAAqB,EAAE,aAAsB;QAC1E,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,EAClC,kBAAkB,GAAG,QAAQ,EAAE,kBAAkB,CAAC;QAEtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;QAE1E,IAAI,kBAAkB,EAAE;YACpB,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,EAAE;gBAC1H,OAAO,KAAK,CAAC;aAChB;SACJ;aAAM;YACH,MAAM,aAAa,GAAG,OAAO,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAE,CAAC;YAEhE,IAAI,MAAM,GAAG,aAAa,CAAC,MAAO,CAAC;YACnC,IAAI,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC;YAE1C,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAE5C,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;YAE/B,eAAe;YACf,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;gBACxE,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBACtC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC/B,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBACxB,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;iBAC7C;aACJ;YAED,aAAa;YACb,MAAM,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,EAAE,CAAC;YAErD,IAAI,gBAAgB,IAAI,QAAQ,CAAC,iBAAiB,EAAE,EAAE;gBAClD,IAAI,IAAI,CAAC,qCAAqC,EAAE;oBAC5C,IAAI,CAAC,eAAe,GAAI,QAAgB,CAAC,cAAc,CAAC;iBAC3D;qBAAM;oBACH,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,mBAAmB,EAAE,CAAC;iBACzD;gBACD,IAAI,IAAI,CAAC,eAAe,EAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE;wBACjC,OAAO,KAAK,CAAC;qBAChB;oBAED,MAAM,WAAW,GAAI,QAAgB,CAAC,WAAW,IAAI,eAAe,CAAC,oBAAoB,CAAC;oBAE1F,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;oBACrC,IAAI,gBAAgB,EAAE;wBAClB,OAAO,CAAC,IAAI,CAAC,0BAA0B,WAAW,GAAG,WAAW,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;qBAC5F;oBACD,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;wBACjD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;wBAClC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;qBAC/B;oBACD,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE;wBAClD,IAAI,IAAI,CAAC,eAAe,CAAC,gBAAgB,KAAK,CAAC,EAAE;4BAC7C,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;4BACnC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;yBAC/B;qBACJ;iBACJ;aACJ;YAED,QAAQ;YACR,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;YACxC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjE,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;gBAC/C,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;gBAC/C,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;oBAC7B,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;oBACpD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;iBACvD;gBACD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,OAAO,CAAC,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACxE,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;oBAC7B,SAAS,CAAC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;iBAC7C;gBAED,IAAI,QAAQ,CAAC,yBAAyB,EAAE;oBACpC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;iBACvC;qBAAM;oBACH,OAAO,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;iBACvE;aACJ;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;aAClD;YAED,gBAAgB;YAChB,MAAM,OAAO,GAAU,IAAK,CAAC,kBAAkB,CAAC;YAChD,IAAI,gBAAgB,GAAG,CAAC,CAAC;YACzB,IAAI,OAAO,EAAE;gBACT,gBAAgB,GAAG,OAAO,CAAC,iBAAiB,IAAI,OAAO,CAAC,cAAc,CAAC;gBACvE,IAAI,gBAAgB,GAAG,CAAC,EAAE;oBACtB,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;oBACrC,OAAO,CAAC,IAAI,CAAC,gCAAgC,GAAG,gBAAgB,CAAC,CAAC;oBAClE,IAAI,OAAO,CAAC,wBAAwB,EAAE;wBAClC,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;qBAChD;oBACD,2CAA2C,CAAC,OAAO,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;iBAChF;aACJ;YAED,aAAa;YACb,iCAAiC,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAElE,YAAY;YACZ,IAAI,YAAY,EAAE;gBACd,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAClC,0BAA0B,CAAC,OAAO,CAAC,CAAC;gBACpC,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE;oBAC7C,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;iBAC1C;aACJ;YAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE;oBAClC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE;wBACnD,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;4BAChC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;yBACxB;qBACJ;iBACJ;aACJ;YAED,qBAAqB;YACrB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,aAAa,KAAK,IAAI,EAAE;gBACxB,aAAa,GAAG,IAAI,CAAC;gBAErB,IAAI,UAAU,GAAG,WAAW,CAAC;gBAC7B,MAAM,QAAQ,GAAG;oBACb,OAAO;oBACP,QAAQ;oBACR,gBAAgB;oBAChB,eAAe;oBACf,aAAa;oBACb,eAAe;oBACf,gBAAgB;oBAChB,uBAAuB;oBACvB,kBAAkB;oBAClB,kBAAkB;oBAClB,yBAAyB;oBACzB,wBAAwB;oBACxB,2BAA2B;iBAC9B,CAAC;gBACF,MAAM,QAAQ,GAAG,CAAC,gBAAgB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;gBACnE,MAAM,cAAc,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAEzC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;gBAE/B,iBAAiB;gBACjB,IAAI,IAAI,CAAC,mBAAmB,EAAE;oBAC1B,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC;oBAEjD,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE;wBACrC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE;4BACtD,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gCAChC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;6BACxB;yBACJ;qBACJ;oBAED,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE;wBACnC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE;4BACrD,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;gCAClC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;6BAC1B;yBACJ;qBACJ;oBAED,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE;wBACnC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE;4BACrD,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;gCAClC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;6BAC1B;yBACJ;qBACJ;iBACJ;gBAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBAEvC,MAAM,GAAG,MAAM,CAAC,YAAY,CACxB,UAAU,EACc;oBACpB,UAAU,EAAE,OAAO;oBACnB,aAAa,EAAE,QAAQ;oBACvB,mBAAmB,EAAE,cAAc;oBACnC,QAAQ,EAAE,QAAQ;oBAClB,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,SAAS;oBACpB,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,IAAI;oBACb,eAAe,EAAE,EAAE,2BAA2B,EAAE,gBAAgB,EAAE;iBACrE,EACD,MAAM,CACT,CAAC;gBAEF,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;aAClD;YAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;gBACnB,OAAO,KAAK,CAAC;aAChB;SACJ;QAED,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,gCAAgC,EAAE;YAC3E,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;gBAC7D,IAAI,CAAC,kCAAkC,EAAE,CAAC;aAC7C;SACJ;QAED,IAAI,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,EAAE;YACzE,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,EAAE;YACzE,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE;YACjE,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,OAAY,EAAE,UAAkB;QAClD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;YAC/C,OAAO;SACV;QAED,OAAO,CAAC,QAAQ,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;QAEtC,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;YAC1C,IAAI,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,WAAW,EAAE;gBACxD,OAAO,CAAC,kBAAkB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;aACnD;iBAAM,IAAI,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,cAAc,EAAE;gBAClE,OAAO,CAAC,qBAAqB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;aACtD;YACD,wBAAwB;SAC3B;aAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE;YAC1C,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;YACzC,IAAI,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,WAAW,EAAE;gBACxD,OAAO,CAAC,kBAAkB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;aACnD;iBAAM,IAAI,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,cAAc,EAAE;gBAClE,OAAO,CAAC,qBAAqB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;aACtD;YACD,wBAAwB;SAC3B;aAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAChC,OAAO,CAAC,eAAe,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;SAChD;aAAM,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,2BAA2B,EAAE;YACzE,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;SAC5C;aAAM,IAAI,IAAI,CAAC,4BAA4B,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACnF,OAAO,CAAC,gBAAgB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;SACjD;QAED,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAE;YAClB,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;SAC7C;IACL,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,UAAkB,EAAE,MAAc;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;YAC/C,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEtC,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO;SACV;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE;YACnB,MAAM,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;SAC3E;QAED,uCAAuC;QACvC,IAAI,IAAI,CAAC,OAAO,KAAK,eAAe,CAAC,UAAU,EAAE;YAC7C,MAAM,CAAC,sBAAsB,CAAC,eAAe,GAAG,UAAU,EAAE,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAC;YAC7F,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;SACvK;aAAM,IAAI,IAAI,CAAC,OAAO,KAAK,eAAe,CAAC,WAAW,EAAE;YACrD,MAAM,CAAC,sBAAsB,CAAC,eAAe,GAAG,UAAU,EAAE,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAC;YAC7F,MAAM,CAAC,UAAU,CAAC,cAAc,GAAG,UAAU,EAAE,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAC;YAChF,KAAK,CAAC,cAAc,CAAC,YAAY,CAC7B,aAAa,EACb,IAAI,CAAC,WAAW,EAAE,EAClB,CAAC,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,KAAK,EAC7B,IAAI,CAAC,iCAAiC,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,KAAK,EAClE,IAAI,CAAC,kBAAkB,EACvB,UAAU,CACb,CAAC;SACL;aAAM;YACH,MAAM,CAAC,UAAU,CAAC,eAAe,GAAG,UAAU,EAAE,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAC;YACjF,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;SAC1K;QAED,KAAK,CAAC,cAAc,CAAC,YAAY,CAC7B,aAAa,EACb,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,EACpC,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,EAC3E,UAAU,CACb,CAAC;IACN,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACI,kBAAkB;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,IAAI,CAAC,gBAAgB,KAAK,KAAK,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,iBAAiB,EAAE;YACzG,OAAO,IAAI,CAAC,gBAAgB,CAAC;SAChC;QAED,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAC5C,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAErD,IAAI,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACzC,IAAI,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE,EAAE;YAC7C,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;SACnD;QAED,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACrG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE;YACnE,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,kDAAkD;SAC/F;QAED,IACI,IAAI,CAAC,MAAM,CAAC,2BAA2B,EAAE;YACzC,CAAC,IAAI,CAAC,eAAe;YACrB,CAAC,IAAI,CAAC,gBAAgB;YACtB,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC;YAC3C,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,EACrD;YACE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC7C,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAErD,MAAM,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAE7G,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAEtC,IAAI,SAAS,EAAE;gBACX,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;gBAExC,IAAI,UAAU,EAAE;oBACZ,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;iBAC/F;aACJ;YAED,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACjF;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO;SACV;QAED,qBAAqB;QACrB,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;QACxC,0BAA0B;QAC1B,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,iBAAiB;QACjB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,iEAAiE;QACjE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3B,uBAAuB;QACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,wBAAwB;QACxB,IAAI,UAAU,EAAE;YACZ,2DAA2D;YAC3D,0LAA0L;YAC1L,IAAI,CAAC,IAAI,CAAC,UAAW,CAAC,UAAU,EAAE;gBAC9B,IAAI,CAAC,UAAW,CAAC,UAAU,GAAG,EAAE,CAAC;aACpC;YACD,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE;gBAC3B,IAAI,CAAC,UAAW,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC1C;SACJ;aAAM;YACH,IAAI,CAAC,UAAW,CAAC,UAAU,GAAG,IAAI,CAAC;SACtC;IACL,CAAC;IAES,yBAAyB;QAC/B,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SAC3B;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;YACnC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;YACvC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;SACvC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;YACvC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;SACvC;QAED,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;IACjC,CAAC;IAES,2BAA2B;QACjC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SAC1B;QAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAES,iBAAiB;QACvB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;gBAC/B,GAAG,CAAC,OAAO,EAAE,CAAC;aACjB;YACD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACxB;IACL,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,CAAC,2BAA2B,EAAE,CAAC;QAEnC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;gBAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;gBACzD,KAAK,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE;oBAC5E,MAAM,CAAC,MAAM,EAAE,eAAe,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;oBAC9C,IAAI,eAAe,KAAK,IAAI,EAAE;wBAC1B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;qBAChD;iBACJ;gBACD,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,EAAE;oBAC1C,IAAI,CAAC,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC;iBACxC;aACJ;YACD,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;SACzC;QAED,IAAI,CAAC,qCAAqC,CAAC,KAAK,EAAE,CAAC;QACnD,IAAI,CAAC,iCAAiC,CAAC,KAAK,EAAE,CAAC;QAC/C,IAAI,CAAC,oCAAoC,CAAC,KAAK,EAAE,CAAC;QAClD,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;IAClD,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEtC,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,mBAAmB,CAAC;SAC9B;QAED,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACpD,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;QAC7C,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;QAChD,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACjC,mBAAmB,CAAC,OAAO,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;QACxD,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACjE,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAClD,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAClE,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACjE,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,4BAA4B,GAAG,IAAI,CAAC,4BAA4B,CAAC;QACrF,mBAAmB,CAAC,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAC;QAC/E,mBAAmB,CAAC,gCAAgC,GAAG,IAAI,CAAC,gCAAgC,CAAC;QAC7F,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC7D,mBAAmB,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAC3E,mBAAmB,CAAC,2BAA2B,GAAG,IAAI,CAAC,2BAA2B,CAAC;QACnF,mBAAmB,CAAC,4BAA4B,GAAG,IAAI,CAAC,2BAA2B,CAAC;QACpF,mBAAmB,CAAC,gCAAgC,GAAG,IAAI,CAAC,2BAA2B,CAAC;QACxF,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACjE,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvD,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAEvD,mBAAmB,CAAC,UAAU,GAAG,EAAE,CAAC;QACpC,IAAI,SAAS,CAAC,UAAU,EAAE;YACtB,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;gBAC1E,MAAM,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;gBAE7C,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aAChD;SACJ;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,qBAA0B,EAAE,KAAY,EAAE,MAA4F;QACtJ,MAAM,KAAK,GAAiB,KAAK,CAAC,YAAY,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAC9E,MAAM,MAAM,GAAqB,qBAAqB,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3I,MAAM,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QACrK,MAAM,SAAS,GAAG,eAAe,CAAC,YAAY,EAAE,CAAC;QAEjD,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,qBAAqB,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;YACtF,MAAM,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,qBAAqB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;YAChF,MAAM,CAAC,OAAO,CAAC,UAAU,IAAI;gBACzB,IAAI,CAAC,SAAS,EAAE;oBACZ,OAAO;iBACV;gBACD,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;oBACvB,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC;iBAC7B;gBACD,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;SACN;QAED,IAAI,qBAAqB,CAAC,EAAE,KAAK,SAAS,EAAE;YACxC,eAAe,CAAC,EAAE,GAAG,qBAAqB,CAAC,EAAE,CAAC;SACjD;QAED,eAAe,CAAC,kBAAkB,GAAG,CAAC,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;QAEhF,IAAI,qBAAqB,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC9C,eAAe,CAAC,WAAW,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;SAC/D;QAED,IAAI,qBAAqB,CAAC,kBAAkB,EAAE;YAC1C,eAAe,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;SAC/C;QAED,IAAI,qBAAqB,CAAC,kBAAkB,KAAK,SAAS,EAAE;YACxD,eAAe,CAAC,kBAAkB,GAAG,qBAAqB,CAAC,kBAAkB,CAAC;SACjF;QAED,IAAI,qBAAqB,CAAC,IAAI,KAAK,SAAS,EAAE;YAC1C,eAAe,CAAC,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC;SACrD;QAED,IAAI,qBAAqB,CAAC,UAAU,KAAK,SAAS,EAAE;YAChD,eAAe,CAAC,UAAU,GAAG,qBAAqB,CAAC,UAAU,CAAC;SACjE;QAED,IAAI,qBAAqB,CAAC,4BAA4B,EAAE;YACpD,eAAe,CAAC,4BAA4B,GAAG,IAAI,CAAC;SACvD;aAAM,IAAI,qBAAqB,CAAC,yBAAyB,EAAE;YACxD,eAAe,CAAC,yBAAyB,GAAG,IAAI,CAAC;SACpD;aAAM,IAAI,qBAAqB,CAAC,kBAAkB,EAAE;YACjD,eAAe,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAC7C;aAAM,IAAI,qBAAqB,CAAC,uBAAuB,EAAE;YACtD,eAAe,CAAC,uBAAuB,GAAG,IAAI,CAAC;SAClD;aAAM,IAAI,qBAAqB,CAAC,2BAA2B,EAAE;YAC1D,eAAe,CAAC,2BAA2B,GAAG,IAAI,CAAC;SACtD;aAAM,IAAI,qBAAqB,CAAC,4BAA4B,EAAE;YAC3D,eAAe,CAAC,4BAA4B,GAAG,IAAI,CAAC;SACvD;aAAM,IAAI,qBAAqB,CAAC,gCAAgC,EAAE;YAC/D,eAAe,CAAC,gCAAgC,GAAG,IAAI,CAAC;SAC3D;QACD,kBAAkB;aACb,IAAI,qBAAqB,CAAC,oBAAoB,EAAE;YACjD,eAAe,CAAC,uBAAuB,GAAG,IAAI,CAAC;SAClD;aAAM,IAAI,qBAAqB,CAAC,wBAAwB,EAAE;YACvD,eAAe,CAAC,2BAA2B,GAAG,IAAI,CAAC;SACtD;QAED,IAAI,qBAAqB,CAAC,gCAAgC,KAAK,SAAS,EAAE;YACtE,eAAe,CAAC,gCAAgC,GAAG,qBAAqB,CAAC,gCAAgC,CAAC;SAC7G;QAED,IAAI,qBAAqB,CAAC,gBAAgB,KAAK,SAAS,EAAE;YACtD,eAAe,CAAC,gBAAgB,GAAG,qBAAqB,CAAC,gBAAgB,CAAC;SAC7E;QAED,IAAI,qBAAqB,CAAC,UAAU,EAAE;YAClC,eAAe,CAAC,UAAU,GAAG,qBAAqB,CAAC,UAAU,CAAC;SACjE;QAED,IAAI,qBAAqB,CAAC,SAAS,EAAE;YACjC,eAAe,CAAC,SAAS,GAAG,qBAAqB,CAAC,SAAS,CAAC;SAC/D;QAED,IAAI,qBAAqB,CAAC,aAAa,EAAE;YACrC,eAAe,CAAC,aAAa,GAAG,qBAAqB,CAAC,aAAa,CAAC;SACvE;QAED,IAAI,qBAAqB,CAAC,aAAa,EAAE;YACrC,eAAe,CAAC,aAAa,GAAG,qBAAqB,CAAC,aAAa,CAAC;SACvE;QAED,IAAI,qBAAqB,CAAC,UAAU,EAAE;YAClC,eAAe,CAAC,UAAU,GAAG,qBAAqB,CAAC,UAAU,CAAC;SACjE;QAED,OAAO,eAAe,CAAC;IAC3B,CAAC;;AA56DD;;GAEG;AACW,yBAAS,GAAG,iBAAiB,AAApB,CAAqB;AAE5C;;GAEG;AACoB,2BAAW,GAAG,CAAC,AAAJ,CAAK;AACvC;;;GAGG;AACoB,2CAA2B,GAAG,CAAC,AAAJ,CAAK;AACvD;;;GAGG;AACoB,sCAAsB,GAAG,CAAC,AAAJ,CAAK;AAClD;;;GAGG;AACoB,+CAA+B,GAAG,CAAC,AAAJ,CAAK;AAC3D;;;;GAIG;AACoB,gDAAgC,GAAG,CAAC,AAAJ,CAAK;AAC5D;;;;GAIG;AACoB,oDAAoC,GAAG,CAAC,AAAJ,CAAK;AAChE;;;;GAIG;AACoB,0BAAU,GAAG,CAAC,AAAJ,CAAK;AACtC;;;;GAIG;AACoB,2BAAW,GAAG,CAAC,AAAJ,CAAK;AAEvC;;;;;;;GAOG;AACoB,4BAAY,GAAG,CAAC,AAAJ,CAAK;AACxC;;;;;;;GAOG;AACoB,8BAAc,GAAG,CAAC,AAAJ,CAAK;AAC1C;;;;;;;GAOG;AACoB,2BAAW,GAAG,CAAC,AAAJ,CAAK;AAEvC;;GAEG;AACW,oCAAoB,GAAG,GAAG,AAAN,CAAO;AA6lBzC;;GAEG;AACW,6CAA6B,GAA2B,CAAC,CAAC,EAAE,EAAE;IACxE,MAAM,WAAW,CAAC,+BAA+B,CAAC,CAAC;AACvD,CAAC,AAF0C,CAEzC", "sourcesContent": ["import type { SmartArray } from \"../../Misc/smartArray\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Matrix, Vector3, Vector2 } from \"../../Maths/math.vector\";\r\nimport { Color4 } from \"../../Maths/math.color\";\r\nimport { VertexBuffer } from \"../../Buffers/buffer\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\n\r\nimport type { IShadowLight } from \"../../Lights/shadowLight\";\r\nimport { Light } from \"../../Lights/light\";\r\nimport type { MaterialDefines } from \"../../Materials/materialDefines\";\r\nimport type { Effect, IEffectCreationOptions } from \"../../Materials/effect\";\r\nimport { Texture } from \"../../Materials/Textures/texture\";\r\nimport { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\n\r\nimport { PostProcess } from \"../../PostProcesses/postProcess\";\r\nimport { BlurPostProcess } from \"../../PostProcesses/blurPostProcess\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { _WarnImport } from \"../../Misc/devTools\";\r\nimport { EffectFallbacks } from \"../../Materials/effectFallbacks\";\r\nimport { RenderingManager } from \"../../Rendering/renderingManager\";\r\nimport { DrawWrapper } from \"../../Materials/drawWrapper\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\n\r\nimport \"../../Shaders/shadowMap.fragment\";\r\nimport \"../../Shaders/shadowMap.vertex\";\r\nimport \"../../Shaders/depthBoxBlur.fragment\";\r\nimport \"../../Shaders/ShadersInclude/shadowMapFragmentSoftTransparentShadow\";\r\nimport { addClipPlaneUniforms, bindClipPlane, prepareStringDefinesForClipPlanes } from \"../../Materials/clipPlaneMaterialHelper\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport {\r\n    BindMorphTargetParameters,\r\n    BindSceneUniformBuffer,\r\n    PrepareAttributesForMorphTargetsInfluencers,\r\n    PushAttributesForInstances,\r\n} from \"../../Materials/materialHelper.functions\";\r\n\r\n/**\r\n * Defines the options associated with the creation of a custom shader for a shadow generator.\r\n */\r\nexport interface ICustomShaderOptions {\r\n    /**\r\n     * Gets or sets the custom shader name to use\r\n     */\r\n    shaderName: string;\r\n\r\n    /**\r\n     * The list of attribute names used in the shader\r\n     */\r\n    attributes?: string[];\r\n\r\n    /**\r\n     * The list of uniform names used in the shader\r\n     */\r\n    uniforms?: string[];\r\n\r\n    /**\r\n     * The list of sampler names used in the shader\r\n     */\r\n    samplers?: string[];\r\n\r\n    /**\r\n     * The list of defines used in the shader\r\n     */\r\n    defines?: string[];\r\n}\r\n\r\n/**\r\n * Interface to implement to create a shadow generator compatible with BJS.\r\n */\r\nexport interface IShadowGenerator {\r\n    /** Gets or set the id of the shadow generator. It will be the one from the light if not defined */\r\n    id: string;\r\n    /**\r\n     * Gets the main RTT containing the shadow map (usually storing depth from the light point of view).\r\n     * @returns The render target texture if present otherwise, null\r\n     */\r\n    getShadowMap(): Nullable<RenderTargetTexture>;\r\n\r\n    /**\r\n     * Determine whether the shadow generator is ready or not (mainly all effects and related post processes needs to be ready).\r\n     * @param subMesh The submesh we want to render in the shadow map\r\n     * @param useInstances Defines whether will draw in the map using instances\r\n     * @param isTransparent Indicates that isReady is called for a transparent subMesh\r\n     * @returns true if ready otherwise, false\r\n     */\r\n    isReady(subMesh: SubMesh, useInstances: boolean, isTransparent: boolean): boolean;\r\n\r\n    /**\r\n     * Prepare all the defines in a material relying on a shadow map at the specified light index.\r\n     * @param defines Defines of the material we want to update\r\n     * @param lightIndex Index of the light in the enabled light list of the material\r\n     */\r\n    prepareDefines(defines: MaterialDefines, lightIndex: number): void;\r\n    /**\r\n     * Binds the shadow related information inside of an effect (information like near, far, darkness...\r\n     * defined in the generator but impacting the effect).\r\n     * It implies the uniforms available on the materials are the standard BJS ones.\r\n     * @param lightIndex Index of the light in the enabled light list of the material owning the effect\r\n     * @param effect The effect we are binding the information for\r\n     */\r\n    bindShadowLight(lightIndex: string, effect: Effect): void;\r\n    /**\r\n     * Gets the transformation matrix used to project the meshes into the map from the light point of view.\r\n     * (eq to shadow projection matrix * light transform matrix)\r\n     * @returns The transform matrix used to create the shadow map\r\n     */\r\n    getTransformMatrix(): Matrix;\r\n\r\n    /**\r\n     * Recreates the shadow map dependencies like RTT and post processes. This can be used during the switch between\r\n     * Cube and 2D textures for instance.\r\n     */\r\n    recreateShadowMap(): void;\r\n\r\n    /**\r\n     * Forces all the attached effect to compile to enable rendering only once ready vs. lazily compiling effects.\r\n     * @param onCompiled Callback triggered at the and of the effects compilation\r\n     * @param options Sets of optional options forcing the compilation with different modes\r\n     */\r\n    forceCompilation(onCompiled?: (generator: IShadowGenerator) => void, options?: Partial<{ useInstances: boolean }>): void;\r\n\r\n    /**\r\n     * Forces all the attached effect to compile to enable rendering only once ready vs. lazily compiling effects.\r\n     * @param options Sets of optional options forcing the compilation with different modes\r\n     * @returns A promise that resolves when the compilation completes\r\n     */\r\n    forceCompilationAsync(options?: Partial<{ useInstances: boolean }>): Promise<void>;\r\n\r\n    /**\r\n     * Serializes the shadow generator setup to a json object.\r\n     * @returns The serialized JSON object\r\n     */\r\n    serialize(): any;\r\n\r\n    /**\r\n     * Disposes the Shadow map and related Textures and effects.\r\n     */\r\n    dispose(): void;\r\n}\r\n\r\n/**\r\n * Default implementation IShadowGenerator.\r\n * This is the main object responsible of generating shadows in the framework.\r\n * Documentation: https://doc.babylonjs.com/features/featuresDeepDive/lights/shadows\r\n */\r\nexport class ShadowGenerator implements IShadowGenerator {\r\n    /**\r\n     * Name of the shadow generator class\r\n     */\r\n    public static CLASSNAME = \"ShadowGenerator\";\r\n\r\n    /**\r\n     * Shadow generator mode None: no filtering applied.\r\n     */\r\n    public static readonly FILTER_NONE = 0;\r\n    /**\r\n     * Shadow generator mode ESM: Exponential Shadow Mapping.\r\n     * (http://developer.download.nvidia.com/presentations/2008/GDC/GDC08_SoftShadowMapping.pdf)\r\n     */\r\n    public static readonly FILTER_EXPONENTIALSHADOWMAP = 1;\r\n    /**\r\n     * Shadow generator mode Poisson Sampling: Percentage Closer Filtering.\r\n     * (Multiple Tap around evenly distributed around the pixel are used to evaluate the shadow strength)\r\n     */\r\n    public static readonly FILTER_POISSONSAMPLING = 2;\r\n    /**\r\n     * Shadow generator mode ESM: Blurred Exponential Shadow Mapping.\r\n     * (http://developer.download.nvidia.com/presentations/2008/GDC/GDC08_SoftShadowMapping.pdf)\r\n     */\r\n    public static readonly FILTER_BLUREXPONENTIALSHADOWMAP = 3;\r\n    /**\r\n     * Shadow generator mode ESM: Exponential Shadow Mapping using the inverse of the exponential preventing\r\n     * edge artifacts on steep falloff.\r\n     * (http://developer.download.nvidia.com/presentations/2008/GDC/GDC08_SoftShadowMapping.pdf)\r\n     */\r\n    public static readonly FILTER_CLOSEEXPONENTIALSHADOWMAP = 4;\r\n    /**\r\n     * Shadow generator mode ESM: Blurred Exponential Shadow Mapping using the inverse of the exponential preventing\r\n     * edge artifacts on steep falloff.\r\n     * (http://developer.download.nvidia.com/presentations/2008/GDC/GDC08_SoftShadowMapping.pdf)\r\n     */\r\n    public static readonly FILTER_BLURCLOSEEXPONENTIALSHADOWMAP = 5;\r\n    /**\r\n     * Shadow generator mode PCF: Percentage Closer Filtering\r\n     * benefits from Webgl 2 shadow samplers. Fallback to Poisson Sampling in Webgl 1\r\n     * (https://developer.nvidia.com/gpugems/GPUGems/gpugems_ch11.html)\r\n     */\r\n    public static readonly FILTER_PCF = 6;\r\n    /**\r\n     * Shadow generator mode PCSS: Percentage Closering Soft Shadow.\r\n     * benefits from Webgl 2 shadow samplers. Fallback to Poisson Sampling in Webgl 1\r\n     * Contact Hardening\r\n     */\r\n    public static readonly FILTER_PCSS = 7;\r\n\r\n    /**\r\n     * Reserved for PCF and PCSS\r\n     * Highest Quality.\r\n     *\r\n     * Execute PCF on a 5*5 kernel improving a lot the shadow aliasing artifacts.\r\n     *\r\n     * Execute PCSS with 32 taps blocker search and 64 taps PCF.\r\n     */\r\n    public static readonly QUALITY_HIGH = 0;\r\n    /**\r\n     * Reserved for PCF and PCSS\r\n     * Good tradeoff for quality/perf cross devices\r\n     *\r\n     * Execute PCF on a 3*3 kernel.\r\n     *\r\n     * Execute PCSS with 16 taps blocker search and 32 taps PCF.\r\n     */\r\n    public static readonly QUALITY_MEDIUM = 1;\r\n    /**\r\n     * Reserved for PCF and PCSS\r\n     * The lowest quality but the fastest.\r\n     *\r\n     * Execute PCF on a 1*1 kernel.\r\n     *\r\n     * Execute PCSS with 16 taps blocker search and 16 taps PCF.\r\n     */\r\n    public static readonly QUALITY_LOW = 2;\r\n\r\n    /**\r\n     * Defines the default alpha cutoff value used for transparent alpha tested materials.\r\n     */\r\n    public static DEFAULT_ALPHA_CUTOFF = 0.5;\r\n\r\n    /** Gets or set the id of the shadow generator. It will be the one from the light if not defined */\r\n    public id: string;\r\n\r\n    /** Gets or sets the custom shader name to use */\r\n    public customShaderOptions: ICustomShaderOptions;\r\n\r\n    /** Gets or sets a custom function to allow/disallow rendering a sub mesh in the shadow map */\r\n    public customAllowRendering: (subMesh: SubMesh) => boolean;\r\n\r\n    /**\r\n     * Observable triggered before the shadow is rendered. Can be used to update internal effect state\r\n     */\r\n    public onBeforeShadowMapRenderObservable = new Observable<Effect>();\r\n\r\n    /**\r\n     * Observable triggered after the shadow is rendered. Can be used to restore internal effect state\r\n     */\r\n    public onAfterShadowMapRenderObservable = new Observable<Effect>();\r\n\r\n    /**\r\n     * Observable triggered before a mesh is rendered in the shadow map.\r\n     * Can be used to update internal effect state (that you can get from the onBeforeShadowMapRenderObservable)\r\n     */\r\n    public onBeforeShadowMapRenderMeshObservable = new Observable<Mesh>();\r\n\r\n    /**\r\n     * Observable triggered after a mesh is rendered in the shadow map.\r\n     * Can be used to update internal effect state (that you can get from the onAfterShadowMapRenderObservable)\r\n     */\r\n    public onAfterShadowMapRenderMeshObservable = new Observable<Mesh>();\r\n\r\n    protected _bias = 0.00005;\r\n    /**\r\n     * Gets the bias: offset applied on the depth preventing acnea (in light direction).\r\n     */\r\n    public get bias(): number {\r\n        return this._bias;\r\n    }\r\n    /**\r\n     * Sets the bias: offset applied on the depth preventing acnea (in light direction).\r\n     */\r\n    public set bias(bias: number) {\r\n        this._bias = bias;\r\n    }\r\n\r\n    protected _normalBias = 0;\r\n    /**\r\n     * Gets the normalBias: offset applied on the depth preventing acnea (along side the normal direction and proportional to the light/normal angle).\r\n     */\r\n    public get normalBias(): number {\r\n        return this._normalBias;\r\n    }\r\n    /**\r\n     * Sets the normalBias: offset applied on the depth preventing acnea (along side the normal direction and proportional to the light/normal angle).\r\n     */\r\n    public set normalBias(normalBias: number) {\r\n        this._normalBias = normalBias;\r\n    }\r\n\r\n    protected _blurBoxOffset = 1;\r\n    /**\r\n     * Gets the blur box offset: offset applied during the blur pass.\r\n     * Only useful if useKernelBlur = false\r\n     */\r\n    public get blurBoxOffset(): number {\r\n        return this._blurBoxOffset;\r\n    }\r\n    /**\r\n     * Sets the blur box offset: offset applied during the blur pass.\r\n     * Only useful if useKernelBlur = false\r\n     */\r\n    public set blurBoxOffset(value: number) {\r\n        if (this._blurBoxOffset === value) {\r\n            return;\r\n        }\r\n\r\n        this._blurBoxOffset = value;\r\n        this._disposeBlurPostProcesses();\r\n    }\r\n\r\n    protected _blurScale = 2;\r\n    /**\r\n     * Gets the blur scale: scale of the blurred texture compared to the main shadow map.\r\n     * 2 means half of the size.\r\n     */\r\n    public get blurScale(): number {\r\n        return this._blurScale;\r\n    }\r\n    /**\r\n     * Sets the blur scale: scale of the blurred texture compared to the main shadow map.\r\n     * 2 means half of the size.\r\n     */\r\n    public set blurScale(value: number) {\r\n        if (this._blurScale === value) {\r\n            return;\r\n        }\r\n\r\n        this._blurScale = value;\r\n        this._disposeBlurPostProcesses();\r\n    }\r\n\r\n    protected _blurKernel = 1;\r\n    /**\r\n     * Gets the blur kernel: kernel size of the blur pass.\r\n     * Only useful if useKernelBlur = true\r\n     */\r\n    public get blurKernel(): number {\r\n        return this._blurKernel;\r\n    }\r\n    /**\r\n     * Sets the blur kernel: kernel size of the blur pass.\r\n     * Only useful if useKernelBlur = true\r\n     */\r\n    public set blurKernel(value: number) {\r\n        if (this._blurKernel === value) {\r\n            return;\r\n        }\r\n\r\n        this._blurKernel = value;\r\n        this._disposeBlurPostProcesses();\r\n    }\r\n\r\n    protected _useKernelBlur = false;\r\n    /**\r\n     * Gets whether the blur pass is a kernel blur (if true) or box blur.\r\n     * Only useful in filtered mode (useBlurExponentialShadowMap...)\r\n     */\r\n    public get useKernelBlur(): boolean {\r\n        return this._useKernelBlur;\r\n    }\r\n    /**\r\n     * Sets whether the blur pass is a kernel blur (if true) or box blur.\r\n     * Only useful in filtered mode (useBlurExponentialShadowMap...)\r\n     */\r\n    public set useKernelBlur(value: boolean) {\r\n        if (this._useKernelBlur === value) {\r\n            return;\r\n        }\r\n\r\n        this._useKernelBlur = value;\r\n        this._disposeBlurPostProcesses();\r\n    }\r\n\r\n    protected _depthScale: number;\r\n    /**\r\n     * Gets the depth scale used in ESM mode.\r\n     */\r\n    public get depthScale(): number {\r\n        return this._depthScale !== undefined ? this._depthScale : this._light.getDepthScale();\r\n    }\r\n    /**\r\n     * Sets the depth scale used in ESM mode.\r\n     * This can override the scale stored on the light.\r\n     */\r\n    public set depthScale(value: number) {\r\n        this._depthScale = value;\r\n    }\r\n\r\n    protected _validateFilter(filter: number): number {\r\n        return filter;\r\n    }\r\n\r\n    protected _filter = ShadowGenerator.FILTER_NONE;\r\n    /**\r\n     * Gets the current mode of the shadow generator (normal, PCF, ESM...).\r\n     * The returned value is a number equal to one of the available mode defined in ShadowMap.FILTER_x like _FILTER_NONE\r\n     */\r\n    public get filter(): number {\r\n        return this._filter;\r\n    }\r\n    /**\r\n     * Sets the current mode of the shadow generator (normal, PCF, ESM...).\r\n     * The returned value is a number equal to one of the available mode defined in ShadowMap.FILTER_x like _FILTER_NONE\r\n     */\r\n    public set filter(value: number) {\r\n        value = this._validateFilter(value);\r\n\r\n        // Blurring the cubemap is going to be too expensive. Reverting to unblurred version\r\n        if (this._light.needCube()) {\r\n            if (value === ShadowGenerator.FILTER_BLUREXPONENTIALSHADOWMAP) {\r\n                this.useExponentialShadowMap = true;\r\n                return;\r\n            } else if (value === ShadowGenerator.FILTER_BLURCLOSEEXPONENTIALSHADOWMAP) {\r\n                this.useCloseExponentialShadowMap = true;\r\n                return;\r\n            }\r\n            // PCF on cubemap would also be expensive\r\n            else if (value === ShadowGenerator.FILTER_PCF || value === ShadowGenerator.FILTER_PCSS) {\r\n                this.usePoissonSampling = true;\r\n                return;\r\n            }\r\n        }\r\n\r\n        // Weblg1 fallback for PCF.\r\n        if (value === ShadowGenerator.FILTER_PCF || value === ShadowGenerator.FILTER_PCSS) {\r\n            if (!this._scene.getEngine()._features.supportShadowSamplers) {\r\n                this.usePoissonSampling = true;\r\n                return;\r\n            }\r\n        }\r\n\r\n        if (this._filter === value) {\r\n            return;\r\n        }\r\n\r\n        this._filter = value;\r\n        this._disposeBlurPostProcesses();\r\n        this._applyFilterValues();\r\n        this._light._markMeshesAsLightDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to Poisson Sampling.\r\n     */\r\n    public get usePoissonSampling(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_POISSONSAMPLING;\r\n    }\r\n    /**\r\n     * Sets the current filter to Poisson Sampling.\r\n     */\r\n    public set usePoissonSampling(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_POISSONSAMPLING);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_POISSONSAMPLING) {\r\n            return;\r\n        }\r\n\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to ESM.\r\n     */\r\n    public get useExponentialShadowMap(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_EXPONENTIALSHADOWMAP;\r\n    }\r\n    /**\r\n     * Sets the current filter is to ESM.\r\n     */\r\n    public set useExponentialShadowMap(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_EXPONENTIALSHADOWMAP);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_EXPONENTIALSHADOWMAP) {\r\n            return;\r\n        }\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to filtered ESM.\r\n     */\r\n    public get useBlurExponentialShadowMap(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_BLUREXPONENTIALSHADOWMAP;\r\n    }\r\n    /**\r\n     * Gets if the current filter is set to filtered  ESM.\r\n     */\r\n    public set useBlurExponentialShadowMap(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_BLUREXPONENTIALSHADOWMAP);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_BLUREXPONENTIALSHADOWMAP) {\r\n            return;\r\n        }\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to \"close ESM\" (using the inverse of the\r\n     * exponential to prevent steep falloff artifacts).\r\n     */\r\n    public get useCloseExponentialShadowMap(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_CLOSEEXPONENTIALSHADOWMAP;\r\n    }\r\n    /**\r\n     * Sets the current filter to \"close ESM\" (using the inverse of the\r\n     * exponential to prevent steep falloff artifacts).\r\n     */\r\n    public set useCloseExponentialShadowMap(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_CLOSEEXPONENTIALSHADOWMAP);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_CLOSEEXPONENTIALSHADOWMAP) {\r\n            return;\r\n        }\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to filtered \"close ESM\" (using the inverse of the\r\n     * exponential to prevent steep falloff artifacts).\r\n     */\r\n    public get useBlurCloseExponentialShadowMap(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_BLURCLOSEEXPONENTIALSHADOWMAP;\r\n    }\r\n    /**\r\n     * Sets the current filter to filtered \"close ESM\" (using the inverse of the\r\n     * exponential to prevent steep falloff artifacts).\r\n     */\r\n    public set useBlurCloseExponentialShadowMap(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_BLURCLOSEEXPONENTIALSHADOWMAP);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_BLURCLOSEEXPONENTIALSHADOWMAP) {\r\n            return;\r\n        }\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to \"PCF\" (percentage closer filtering).\r\n     */\r\n    public get usePercentageCloserFiltering(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_PCF;\r\n    }\r\n    /**\r\n     * Sets the current filter to \"PCF\" (percentage closer filtering).\r\n     */\r\n    public set usePercentageCloserFiltering(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_PCF);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_PCF) {\r\n            return;\r\n        }\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    protected _filteringQuality = ShadowGenerator.QUALITY_HIGH;\r\n    /**\r\n     * Gets the PCF or PCSS Quality.\r\n     * Only valid if usePercentageCloserFiltering or usePercentageCloserFiltering is true.\r\n     */\r\n    public get filteringQuality(): number {\r\n        return this._filteringQuality;\r\n    }\r\n    /**\r\n     * Sets the PCF or PCSS Quality.\r\n     * Only valid if usePercentageCloserFiltering or usePercentageCloserFiltering is true.\r\n     */\r\n    public set filteringQuality(filteringQuality: number) {\r\n        if (this._filteringQuality === filteringQuality) {\r\n            return;\r\n        }\r\n\r\n        this._filteringQuality = filteringQuality;\r\n\r\n        this._disposeBlurPostProcesses();\r\n        this._applyFilterValues();\r\n        this._light._markMeshesAsLightDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to \"PCSS\" (contact hardening).\r\n     */\r\n    public get useContactHardeningShadow(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_PCSS;\r\n    }\r\n    /**\r\n     * Sets the current filter to \"PCSS\" (contact hardening).\r\n     */\r\n    public set useContactHardeningShadow(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_PCSS);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_PCSS) {\r\n            return;\r\n        }\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    protected _contactHardeningLightSizeUVRatio = 0.1;\r\n    /**\r\n     * Gets the Light Size (in shadow map uv unit) used in PCSS to determine the blocker search area and the penumbra size.\r\n     * Using a ratio helps keeping shape stability independently of the map size.\r\n     *\r\n     * It does not account for the light projection as it was having too much\r\n     * instability during the light setup or during light position changes.\r\n     *\r\n     * Only valid if useContactHardeningShadow is true.\r\n     */\r\n    public get contactHardeningLightSizeUVRatio(): number {\r\n        return this._contactHardeningLightSizeUVRatio;\r\n    }\r\n    /**\r\n     * Sets the Light Size (in shadow map uv unit) used in PCSS to determine the blocker search area and the penumbra size.\r\n     * Using a ratio helps keeping shape stability independently of the map size.\r\n     *\r\n     * It does not account for the light projection as it was having too much\r\n     * instability during the light setup or during light position changes.\r\n     *\r\n     * Only valid if useContactHardeningShadow is true.\r\n     */\r\n    public set contactHardeningLightSizeUVRatio(contactHardeningLightSizeUVRatio: number) {\r\n        this._contactHardeningLightSizeUVRatio = contactHardeningLightSizeUVRatio;\r\n    }\r\n\r\n    protected _darkness = 0;\r\n\r\n    /** Gets or sets the actual darkness of a shadow */\r\n    public get darkness() {\r\n        return this._darkness;\r\n    }\r\n\r\n    public set darkness(value: number) {\r\n        this.setDarkness(value);\r\n    }\r\n\r\n    /**\r\n     * Returns the darkness value (float). This can only decrease the actual darkness of a shadow.\r\n     * 0 means strongest and 1 would means no shadow.\r\n     * @returns the darkness.\r\n     */\r\n    public getDarkness(): number {\r\n        return this._darkness;\r\n    }\r\n    /**\r\n     * Sets the darkness value (float). This can only decrease the actual darkness of a shadow.\r\n     * @param darkness The darkness value 0 means strongest and 1 would means no shadow.\r\n     * @returns the shadow generator allowing fluent coding.\r\n     */\r\n    public setDarkness(darkness: number): ShadowGenerator {\r\n        if (darkness >= 1.0) {\r\n            this._darkness = 1.0;\r\n        } else if (darkness <= 0.0) {\r\n            this._darkness = 0.0;\r\n        } else {\r\n            this._darkness = darkness;\r\n        }\r\n        return this;\r\n    }\r\n\r\n    protected _transparencyShadow = false;\r\n\r\n    /** Gets or sets the ability to have transparent shadow  */\r\n    public get transparencyShadow() {\r\n        return this._transparencyShadow;\r\n    }\r\n\r\n    public set transparencyShadow(value: boolean) {\r\n        this.setTransparencyShadow(value);\r\n    }\r\n\r\n    /**\r\n     * Sets the ability to have transparent shadow (boolean).\r\n     * @param transparent True if transparent else False\r\n     * @returns the shadow generator allowing fluent coding\r\n     */\r\n    public setTransparencyShadow(transparent: boolean): ShadowGenerator {\r\n        this._transparencyShadow = transparent;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Enables or disables shadows with varying strength based on the transparency\r\n     * When it is enabled, the strength of the shadow is taken equal to mesh.visibility\r\n     * If you enabled an alpha texture on your material, the alpha value red from the texture is also combined to compute the strength:\r\n     *          mesh.visibility * alphaTexture.a\r\n     * The texture used is the diffuse by default, but it can be set to the opacity by setting useOpacityTextureForTransparentShadow\r\n     * Note that by definition transparencyShadow must be set to true for enableSoftTransparentShadow to work!\r\n     */\r\n    public enableSoftTransparentShadow: boolean = false;\r\n\r\n    /**\r\n     * If this is true, use the opacity texture's alpha channel for transparent shadows instead of the diffuse one\r\n     */\r\n    public useOpacityTextureForTransparentShadow: boolean = false;\r\n\r\n    protected _shadowMap: Nullable<RenderTargetTexture>;\r\n    protected _shadowMap2: Nullable<RenderTargetTexture>;\r\n\r\n    /**\r\n     * Gets the main RTT containing the shadow map (usually storing depth from the light point of view).\r\n     * @returns The render target texture if present otherwise, null\r\n     */\r\n    public getShadowMap(): Nullable<RenderTargetTexture> {\r\n        return this._shadowMap;\r\n    }\r\n\r\n    /**\r\n     * Gets the RTT used during rendering (can be a blurred version of the shadow map or the shadow map itself).\r\n     * @returns The render target texture if the shadow map is present otherwise, null\r\n     */\r\n    public getShadowMapForRendering(): Nullable<RenderTargetTexture> {\r\n        if (this._shadowMap2) {\r\n            return this._shadowMap2;\r\n        }\r\n\r\n        return this._shadowMap;\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of that object\r\n     * @returns \"ShadowGenerator\"\r\n     */\r\n    public getClassName(): string {\r\n        return ShadowGenerator.CLASSNAME;\r\n    }\r\n\r\n    /**\r\n     * Helper function to add a mesh and its descendants to the list of shadow casters.\r\n     * @param mesh Mesh to add\r\n     * @param includeDescendants boolean indicating if the descendants should be added. Default to true\r\n     * @returns the Shadow Generator itself\r\n     */\r\n    public addShadowCaster(mesh: AbstractMesh, includeDescendants = true): ShadowGenerator {\r\n        if (!this._shadowMap) {\r\n            return this;\r\n        }\r\n\r\n        if (!this._shadowMap.renderList) {\r\n            this._shadowMap.renderList = [];\r\n        }\r\n\r\n        if (this._shadowMap.renderList.indexOf(mesh) === -1) {\r\n            this._shadowMap.renderList.push(mesh);\r\n        }\r\n\r\n        if (includeDescendants) {\r\n            for (const childMesh of mesh.getChildMeshes()) {\r\n                if (this._shadowMap.renderList.indexOf(childMesh) === -1) {\r\n                    this._shadowMap.renderList.push(childMesh);\r\n                }\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Helper function to remove a mesh and its descendants from the list of shadow casters\r\n     * @param mesh Mesh to remove\r\n     * @param includeDescendants boolean indicating if the descendants should be removed. Default to true\r\n     * @returns the Shadow Generator itself\r\n     */\r\n    public removeShadowCaster(mesh: AbstractMesh, includeDescendants = true): ShadowGenerator {\r\n        if (!this._shadowMap || !this._shadowMap.renderList) {\r\n            return this;\r\n        }\r\n\r\n        const index = this._shadowMap.renderList.indexOf(mesh);\r\n\r\n        if (index !== -1) {\r\n            this._shadowMap.renderList.splice(index, 1);\r\n        }\r\n\r\n        if (includeDescendants) {\r\n            for (const child of mesh.getChildren()) {\r\n                this.removeShadowCaster(<any>child);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Controls the extent to which the shadows fade out at the edge of the frustum\r\n     */\r\n    public frustumEdgeFalloff = 0;\r\n\r\n    protected _light: IShadowLight;\r\n    /**\r\n     * Returns the associated light object.\r\n     * @returns the light generating the shadow\r\n     */\r\n    public getLight(): IShadowLight {\r\n        return this._light;\r\n    }\r\n\r\n    /**\r\n     * If true the shadow map is generated by rendering the back face of the mesh instead of the front face.\r\n     * This can help with self-shadowing as the geometry making up the back of objects is slightly offset.\r\n     * It might on the other hand introduce peter panning.\r\n     */\r\n    public forceBackFacesOnly = false;\r\n\r\n    protected _camera: Nullable<Camera>;\r\n\r\n    protected _getCamera() {\r\n        return this._camera ?? this._scene.activeCamera;\r\n    }\r\n\r\n    protected _scene: Scene;\r\n    protected _useRedTextureType: boolean;\r\n    protected _lightDirection = Vector3.Zero();\r\n\r\n    protected _viewMatrix = Matrix.Zero();\r\n    protected _projectionMatrix = Matrix.Zero();\r\n    protected _transformMatrix = Matrix.Zero();\r\n    protected _cachedPosition: Vector3 = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n    protected _cachedDirection: Vector3 = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n    protected _cachedDefines: string;\r\n    protected _currentRenderId: number;\r\n    protected _boxBlurPostprocess: Nullable<PostProcess>;\r\n    protected _kernelBlurXPostprocess: Nullable<PostProcess>;\r\n    protected _kernelBlurYPostprocess: Nullable<PostProcess>;\r\n    protected _blurPostProcesses: PostProcess[];\r\n    protected _mapSize: number;\r\n    protected _currentFaceIndex = 0;\r\n    protected _currentFaceIndexCache = 0;\r\n    protected _textureType: number;\r\n    protected _defaultTextureMatrix = Matrix.Identity();\r\n    protected _storedUniqueId: Nullable<number>;\r\n    protected _useUBO: boolean;\r\n    protected _sceneUBOs: UniformBuffer[];\r\n    protected _currentSceneUBO: UniformBuffer;\r\n    protected _opacityTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _SceneComponentInitialization: (scene: Scene) => void = (_) => {\r\n        throw _WarnImport(\"ShadowGeneratorSceneComponent\");\r\n    };\r\n\r\n    /**\r\n     * Gets or sets the size of the texture what stores the shadows\r\n     */\r\n    public get mapSize(): number {\r\n        return this._mapSize;\r\n    }\r\n\r\n    public set mapSize(size: number) {\r\n        this._mapSize = size;\r\n        this._light._markMeshesAsLightDirty();\r\n        this.recreateShadowMap();\r\n    }\r\n\r\n    /**\r\n     * Creates a ShadowGenerator object.\r\n     * A ShadowGenerator is the required tool to use the shadows.\r\n     * Each light casting shadows needs to use its own ShadowGenerator.\r\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/shadows\r\n     * @param mapSize The size of the texture what stores the shadows. Example : 1024.\r\n     * @param light The light object generating the shadows.\r\n     * @param usefullFloatFirst By default the generator will try to use half float textures but if you need precision (for self shadowing for instance), you can use this option to enforce full float texture.\r\n     * @param camera Camera associated with this shadow generator (default: null). If null, takes the scene active camera at the time we need to access it\r\n     * @param useRedTextureType Forces the generator to use a Red instead of a RGBA type for the shadow map texture format (default: false)\r\n     */\r\n    constructor(mapSize: number, light: IShadowLight, usefullFloatFirst?: boolean, camera?: Nullable<Camera>, useRedTextureType?: boolean) {\r\n        this._mapSize = mapSize;\r\n        this._light = light;\r\n        this._scene = light.getScene();\r\n        this._camera = camera ?? null;\r\n        this._useRedTextureType = !!useRedTextureType;\r\n\r\n        let shadowGenerators = light._shadowGenerators;\r\n        if (!shadowGenerators) {\r\n            shadowGenerators = light._shadowGenerators = new Map();\r\n        }\r\n        shadowGenerators.set(this._camera, this);\r\n        this.id = light.id;\r\n        this._useUBO = this._scene.getEngine().supportsUniformBuffers;\r\n\r\n        if (this._useUBO) {\r\n            this._sceneUBOs = [];\r\n            this._sceneUBOs.push(this._scene.createSceneUniformBuffer(`Scene for Shadow Generator (light \"${this._light.name}\")`));\r\n        }\r\n\r\n        ShadowGenerator._SceneComponentInitialization(this._scene);\r\n\r\n        // Texture type fallback from float to int if not supported.\r\n        const caps = this._scene.getEngine().getCaps();\r\n\r\n        if (!usefullFloatFirst) {\r\n            if (caps.textureHalfFloatRender && caps.textureHalfFloatLinearFiltering) {\r\n                this._textureType = Constants.TEXTURETYPE_HALF_FLOAT;\r\n            } else if (caps.textureFloatRender && caps.textureFloatLinearFiltering) {\r\n                this._textureType = Constants.TEXTURETYPE_FLOAT;\r\n            } else {\r\n                this._textureType = Constants.TEXTURETYPE_UNSIGNED_INT;\r\n            }\r\n        } else {\r\n            if (caps.textureFloatRender && caps.textureFloatLinearFiltering) {\r\n                this._textureType = Constants.TEXTURETYPE_FLOAT;\r\n            } else if (caps.textureHalfFloatRender && caps.textureHalfFloatLinearFiltering) {\r\n                this._textureType = Constants.TEXTURETYPE_HALF_FLOAT;\r\n            } else {\r\n                this._textureType = Constants.TEXTURETYPE_UNSIGNED_INT;\r\n            }\r\n        }\r\n\r\n        this._initializeGenerator();\r\n        this._applyFilterValues();\r\n    }\r\n\r\n    protected _initializeGenerator(): void {\r\n        this._light._markMeshesAsLightDirty();\r\n        this._initializeShadowMap();\r\n    }\r\n\r\n    protected _createTargetRenderTexture(): void {\r\n        const engine = this._scene.getEngine();\r\n        if (engine._features.supportDepthStencilTexture) {\r\n            this._shadowMap = new RenderTargetTexture(\r\n                this._light.name + \"_shadowMap\",\r\n                this._mapSize,\r\n                this._scene,\r\n                false,\r\n                true,\r\n                this._textureType,\r\n                this._light.needCube(),\r\n                undefined,\r\n                false,\r\n                false,\r\n                undefined,\r\n                this._useRedTextureType ? Constants.TEXTUREFORMAT_RED : Constants.TEXTUREFORMAT_RGBA\r\n            );\r\n            this._shadowMap.createDepthStencilTexture(engine.useReverseDepthBuffer ? Constants.GREATER : Constants.LESS, true);\r\n        } else {\r\n            this._shadowMap = new RenderTargetTexture(this._light.name + \"_shadowMap\", this._mapSize, this._scene, false, true, this._textureType, this._light.needCube());\r\n        }\r\n        this._shadowMap.noPrePassRenderer = true;\r\n    }\r\n\r\n    protected _initializeShadowMap(): void {\r\n        this._createTargetRenderTexture();\r\n\r\n        if (this._shadowMap === null) {\r\n            return;\r\n        }\r\n\r\n        this._shadowMap.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n        this._shadowMap.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n        this._shadowMap.anisotropicFilteringLevel = 1;\r\n        this._shadowMap.updateSamplingMode(Texture.BILINEAR_SAMPLINGMODE);\r\n        this._shadowMap.renderParticles = false;\r\n        this._shadowMap.ignoreCameraViewport = true;\r\n        if (this._storedUniqueId) {\r\n            this._shadowMap.uniqueId = this._storedUniqueId;\r\n        }\r\n\r\n        // Custom render function.\r\n        this._shadowMap.customRenderFunction = (\r\n            opaqueSubMeshes: SmartArray<SubMesh>,\r\n            alphaTestSubMeshes: SmartArray<SubMesh>,\r\n            transparentSubMeshes: SmartArray<SubMesh>,\r\n            depthOnlySubMeshes: SmartArray<SubMesh>\r\n        ) => this._renderForShadowMap(opaqueSubMeshes, alphaTestSubMeshes, transparentSubMeshes, depthOnlySubMeshes);\r\n\r\n        // Force the mesh is ready function to true as we are double checking it\r\n        // in the custom render function. Also it prevents side effects and useless\r\n        // shader variations in DEPTHPREPASS mode.\r\n        this._shadowMap.customIsReadyFunction = () => {\r\n            return true;\r\n        };\r\n\r\n        const engine = this._scene.getEngine();\r\n\r\n        this._shadowMap.onBeforeBindObservable.add(() => {\r\n            this._currentSceneUBO = this._scene.getSceneUniformBuffer();\r\n            engine._debugPushGroup?.(`shadow map generation for pass id ${engine.currentRenderPassId}`, 1);\r\n        });\r\n\r\n        // Record Face Index before render.\r\n        this._shadowMap.onBeforeRenderObservable.add((faceIndex: number) => {\r\n            if (this._sceneUBOs) {\r\n                this._scene.setSceneUniformBuffer(this._sceneUBOs[0]);\r\n            }\r\n            this._currentFaceIndex = faceIndex;\r\n            if (this._filter === ShadowGenerator.FILTER_PCF) {\r\n                engine.setColorWrite(false);\r\n            }\r\n            this.getTransformMatrix(); // generate the view/projection matrix\r\n            this._scene.setTransformMatrix(this._viewMatrix, this._projectionMatrix);\r\n            if (this._useUBO) {\r\n                this._scene.getSceneUniformBuffer().unbindEffect();\r\n                this._scene.finalizeSceneUbo();\r\n            }\r\n        });\r\n\r\n        // Blur if required after render.\r\n        this._shadowMap.onAfterUnbindObservable.add(() => {\r\n            if (this._sceneUBOs) {\r\n                this._scene.setSceneUniformBuffer(this._currentSceneUBO);\r\n            }\r\n            this._scene.updateTransformMatrix(); // restore the view/projection matrices of the active camera\r\n\r\n            if (this._filter === ShadowGenerator.FILTER_PCF) {\r\n                engine.setColorWrite(true);\r\n            }\r\n            if (!this.useBlurExponentialShadowMap && !this.useBlurCloseExponentialShadowMap) {\r\n                engine._debugPopGroup?.(1);\r\n                return;\r\n            }\r\n            const shadowMap = this.getShadowMapForRendering();\r\n\r\n            if (shadowMap) {\r\n                this._scene.postProcessManager.directRender(this._blurPostProcesses, shadowMap.renderTarget, true);\r\n                engine.unBindFramebuffer(shadowMap.renderTarget!, true);\r\n                engine._debugPopGroup?.(1);\r\n            }\r\n        });\r\n\r\n        // Clear according to the chosen filter.\r\n        const clearZero = new Color4(0, 0, 0, 0);\r\n        const clearOne = new Color4(1.0, 1.0, 1.0, 1.0);\r\n        this._shadowMap.onClearObservable.add((engine) => {\r\n            if (this._filter === ShadowGenerator.FILTER_PCF) {\r\n                engine.clear(clearOne, false, true, false);\r\n            } else if (this.useExponentialShadowMap || this.useBlurExponentialShadowMap) {\r\n                engine.clear(clearZero, true, true, false);\r\n            } else {\r\n                engine.clear(clearOne, true, true, false);\r\n            }\r\n        });\r\n\r\n        // Recreate on resize.\r\n        this._shadowMap.onResizeObservable.add((rtt) => {\r\n            this._storedUniqueId = this._shadowMap!.uniqueId;\r\n            this._mapSize = rtt.getRenderSize();\r\n            this._light._markMeshesAsLightDirty();\r\n            this.recreateShadowMap();\r\n        });\r\n\r\n        // Ensures rendering groupids do not erase the depth buffer\r\n        // or we would lose the shadows information.\r\n        for (let i = RenderingManager.MIN_RENDERINGGROUPS; i < RenderingManager.MAX_RENDERINGGROUPS; i++) {\r\n            this._shadowMap.setRenderingAutoClearDepthStencil(i, false);\r\n        }\r\n    }\r\n\r\n    protected _initializeBlurRTTAndPostProcesses(): void {\r\n        const engine = this._scene.getEngine();\r\n        const targetSize = this._mapSize / this.blurScale;\r\n\r\n        if (!this.useKernelBlur || this.blurScale !== 1.0) {\r\n            this._shadowMap2 = new RenderTargetTexture(this._light.name + \"_shadowMap2\", targetSize, this._scene, false, true, this._textureType, undefined, undefined, false);\r\n            this._shadowMap2.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n            this._shadowMap2.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n            this._shadowMap2.updateSamplingMode(Texture.BILINEAR_SAMPLINGMODE);\r\n        }\r\n\r\n        if (this.useKernelBlur) {\r\n            this._kernelBlurXPostprocess = new BlurPostProcess(\r\n                this._light.name + \"KernelBlurX\",\r\n                new Vector2(1, 0),\r\n                this.blurKernel,\r\n                1.0,\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                engine,\r\n                false,\r\n                this._textureType\r\n            );\r\n            this._kernelBlurXPostprocess.width = targetSize;\r\n            this._kernelBlurXPostprocess.height = targetSize;\r\n            this._kernelBlurXPostprocess.externalTextureSamplerBinding = true;\r\n            this._kernelBlurXPostprocess.onApplyObservable.add((effect) => {\r\n                effect.setTexture(\"textureSampler\", this._shadowMap);\r\n            });\r\n\r\n            this._kernelBlurYPostprocess = new BlurPostProcess(\r\n                this._light.name + \"KernelBlurY\",\r\n                new Vector2(0, 1),\r\n                this.blurKernel,\r\n                1.0,\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                engine,\r\n                false,\r\n                this._textureType\r\n            );\r\n\r\n            this._kernelBlurXPostprocess.autoClear = false;\r\n            this._kernelBlurYPostprocess.autoClear = false;\r\n\r\n            if (this._textureType === Constants.TEXTURETYPE_UNSIGNED_INT) {\r\n                (<BlurPostProcess>this._kernelBlurXPostprocess).packedFloat = true;\r\n                (<BlurPostProcess>this._kernelBlurYPostprocess).packedFloat = true;\r\n            }\r\n\r\n            this._blurPostProcesses = [this._kernelBlurXPostprocess, this._kernelBlurYPostprocess];\r\n        } else {\r\n            this._boxBlurPostprocess = new PostProcess(\r\n                this._light.name + \"DepthBoxBlur\",\r\n                \"depthBoxBlur\",\r\n                [\"screenSize\", \"boxOffset\"],\r\n                [],\r\n                1.0,\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                engine,\r\n                false,\r\n                \"#define OFFSET \" + this._blurBoxOffset,\r\n                this._textureType\r\n            );\r\n            this._boxBlurPostprocess.externalTextureSamplerBinding = true;\r\n            this._boxBlurPostprocess.onApplyObservable.add((effect) => {\r\n                effect.setFloat2(\"screenSize\", targetSize, targetSize);\r\n                effect.setTexture(\"textureSampler\", this._shadowMap);\r\n            });\r\n\r\n            this._boxBlurPostprocess.autoClear = false;\r\n\r\n            this._blurPostProcesses = [this._boxBlurPostprocess];\r\n        }\r\n    }\r\n\r\n    protected _renderForShadowMap(\r\n        opaqueSubMeshes: SmartArray<SubMesh>,\r\n        alphaTestSubMeshes: SmartArray<SubMesh>,\r\n        transparentSubMeshes: SmartArray<SubMesh>,\r\n        depthOnlySubMeshes: SmartArray<SubMesh>\r\n    ): void {\r\n        let index: number;\r\n\r\n        if (depthOnlySubMeshes.length) {\r\n            for (index = 0; index < depthOnlySubMeshes.length; index++) {\r\n                this._renderSubMeshForShadowMap(depthOnlySubMeshes.data[index]);\r\n            }\r\n        }\r\n\r\n        for (index = 0; index < opaqueSubMeshes.length; index++) {\r\n            this._renderSubMeshForShadowMap(opaqueSubMeshes.data[index]);\r\n        }\r\n\r\n        for (index = 0; index < alphaTestSubMeshes.length; index++) {\r\n            this._renderSubMeshForShadowMap(alphaTestSubMeshes.data[index]);\r\n        }\r\n\r\n        if (this._transparencyShadow) {\r\n            for (index = 0; index < transparentSubMeshes.length; index++) {\r\n                this._renderSubMeshForShadowMap(transparentSubMeshes.data[index], true);\r\n            }\r\n        } else {\r\n            for (index = 0; index < transparentSubMeshes.length; index++) {\r\n                transparentSubMeshes.data[index].getEffectiveMesh()._internalAbstractMeshDataInfo._isActiveIntermediate = false;\r\n            }\r\n        }\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _bindCustomEffectForRenderSubMeshForShadowMap(subMesh: SubMesh, effect: Effect, mesh: AbstractMesh): void {\r\n        effect.setMatrix(\"viewProjection\", this.getTransformMatrix());\r\n    }\r\n\r\n    protected _renderSubMeshForShadowMap(subMesh: SubMesh, isTransparent: boolean = false): void {\r\n        const renderingMesh = subMesh.getRenderingMesh();\r\n        const effectiveMesh = subMesh.getEffectiveMesh();\r\n        const scene = this._scene;\r\n        const engine = scene.getEngine();\r\n        const material = subMesh.getMaterial();\r\n\r\n        effectiveMesh._internalAbstractMeshDataInfo._isActiveIntermediate = false;\r\n\r\n        if (!material || subMesh.verticesCount === 0 || subMesh._renderId === scene.getRenderId()) {\r\n            return;\r\n        }\r\n\r\n        // Culling\r\n        const detNeg = effectiveMesh._getWorldMatrixDeterminant() < 0;\r\n        let sideOrientation = renderingMesh.overrideMaterialSideOrientation ?? material.sideOrientation;\r\n        if (detNeg) {\r\n            sideOrientation =\r\n                sideOrientation === Constants.MATERIAL_ClockWiseSideOrientation ? Constants.MATERIAL_CounterClockWiseSideOrientation : Constants.MATERIAL_ClockWiseSideOrientation;\r\n        }\r\n        const reverseSideOrientation = sideOrientation === Constants.MATERIAL_ClockWiseSideOrientation;\r\n\r\n        engine.setState(material.backFaceCulling, undefined, undefined, reverseSideOrientation, material.cullBackFaces);\r\n\r\n        // Managing instances\r\n        const batch = renderingMesh._getInstancesRenderList(subMesh._id, !!subMesh.getReplacementMesh());\r\n        if (batch.mustReturn) {\r\n            return;\r\n        }\r\n\r\n        const hardwareInstancedRendering =\r\n            engine.getCaps().instancedArrays &&\r\n            ((batch.visibleInstances[subMesh._id] !== null && batch.visibleInstances[subMesh._id] !== undefined) || renderingMesh.hasThinInstances);\r\n\r\n        if (this.customAllowRendering && !this.customAllowRendering(subMesh)) {\r\n            return;\r\n        }\r\n\r\n        if (this.isReady(subMesh, hardwareInstancedRendering, isTransparent)) {\r\n            subMesh._renderId = scene.getRenderId();\r\n\r\n            const shadowDepthWrapper = material.shadowDepthWrapper;\r\n\r\n            const drawWrapper = shadowDepthWrapper?.getEffect(subMesh, this, engine.currentRenderPassId) ?? subMesh._getDrawWrapper()!;\r\n            const effect = DrawWrapper.GetEffect(drawWrapper)!;\r\n\r\n            engine.enableEffect(drawWrapper);\r\n\r\n            if (!hardwareInstancedRendering) {\r\n                renderingMesh._bind(subMesh, effect, material.fillMode);\r\n            }\r\n\r\n            this.getTransformMatrix(); // make sure _cachedDirection et _cachedPosition are up to date\r\n\r\n            effect.setFloat3(\"biasAndScaleSM\", this.bias, this.normalBias, this.depthScale);\r\n\r\n            if (this.getLight().getTypeID() === Light.LIGHTTYPEID_DIRECTIONALLIGHT) {\r\n                effect.setVector3(\"lightDataSM\", this._cachedDirection);\r\n            } else {\r\n                effect.setVector3(\"lightDataSM\", this._cachedPosition);\r\n            }\r\n\r\n            const camera = this._getCamera();\r\n            if (camera) {\r\n                effect.setFloat2(\"depthValuesSM\", this.getLight().getDepthMinZ(camera), this.getLight().getDepthMinZ(camera) + this.getLight().getDepthMaxZ(camera));\r\n            }\r\n\r\n            if (isTransparent && this.enableSoftTransparentShadow) {\r\n                effect.setFloat(\"softTransparentShadowSM\", effectiveMesh.visibility * material.alpha);\r\n            }\r\n\r\n            if (shadowDepthWrapper) {\r\n                subMesh._setMainDrawWrapperOverride(drawWrapper);\r\n                if (shadowDepthWrapper.standalone) {\r\n                    shadowDepthWrapper.baseMaterial.bindForSubMesh(effectiveMesh.getWorldMatrix(), renderingMesh, subMesh);\r\n                } else {\r\n                    material.bindForSubMesh(effectiveMesh.getWorldMatrix(), renderingMesh, subMesh);\r\n                }\r\n                subMesh._setMainDrawWrapperOverride(null);\r\n            } else {\r\n                // Alpha test\r\n                if (this._opacityTexture) {\r\n                    effect.setTexture(\"diffuseSampler\", this._opacityTexture);\r\n                    effect.setMatrix(\"diffuseMatrix\", this._opacityTexture.getTextureMatrix() || this._defaultTextureMatrix);\r\n                }\r\n\r\n                // Bones\r\n                if (renderingMesh.useBones && renderingMesh.computeBonesUsingShaders && renderingMesh.skeleton) {\r\n                    const skeleton = renderingMesh.skeleton;\r\n\r\n                    if (skeleton.isUsingTextureForMatrices) {\r\n                        const boneTexture = skeleton.getTransformMatrixTexture(renderingMesh);\r\n\r\n                        if (!boneTexture) {\r\n                            return;\r\n                        }\r\n\r\n                        effect.setTexture(\"boneSampler\", boneTexture);\r\n                        effect.setFloat(\"boneTextureWidth\", 4.0 * (skeleton.bones.length + 1));\r\n                    } else {\r\n                        effect.setMatrices(\"mBones\", skeleton.getTransformMatrices(renderingMesh));\r\n                    }\r\n                }\r\n\r\n                // Morph targets\r\n                BindMorphTargetParameters(renderingMesh, effect);\r\n\r\n                if (renderingMesh.morphTargetManager && renderingMesh.morphTargetManager.isUsingTextureForTargets) {\r\n                    renderingMesh.morphTargetManager._bind(effect);\r\n                }\r\n\r\n                // Clip planes\r\n                bindClipPlane(effect, material, scene);\r\n            }\r\n\r\n            if (!this._useUBO && !shadowDepthWrapper) {\r\n                this._bindCustomEffectForRenderSubMeshForShadowMap(subMesh, effect, effectiveMesh);\r\n            }\r\n\r\n            BindSceneUniformBuffer(effect, this._scene.getSceneUniformBuffer());\r\n            this._scene.getSceneUniformBuffer().bindUniformBuffer();\r\n\r\n            const world = effectiveMesh.getWorldMatrix();\r\n\r\n            // In the non hardware instanced mode, the Mesh ubo update is done by the callback passed to renderingMesh._processRendering (see below)\r\n            if (hardwareInstancedRendering) {\r\n                effectiveMesh.getMeshUniformBuffer().bindToEffect(effect, \"Mesh\");\r\n                effectiveMesh.transferToEffect(world);\r\n            }\r\n\r\n            if (this.forceBackFacesOnly) {\r\n                engine.setState(true, 0, false, true, material.cullBackFaces);\r\n            }\r\n\r\n            // Observables\r\n            this.onBeforeShadowMapRenderMeshObservable.notifyObservers(renderingMesh);\r\n            this.onBeforeShadowMapRenderObservable.notifyObservers(effect);\r\n\r\n            // Draw\r\n            renderingMesh._processRendering(effectiveMesh, subMesh, effect, material.fillMode, batch, hardwareInstancedRendering, (isInstance, worldOverride) => {\r\n                if (effectiveMesh !== renderingMesh && !isInstance) {\r\n                    renderingMesh.getMeshUniformBuffer().bindToEffect(effect, \"Mesh\");\r\n                    renderingMesh.transferToEffect(worldOverride);\r\n                } else {\r\n                    effectiveMesh.getMeshUniformBuffer().bindToEffect(effect, \"Mesh\");\r\n                    effectiveMesh.transferToEffect(isInstance ? worldOverride : world);\r\n                }\r\n            });\r\n\r\n            if (this.forceBackFacesOnly) {\r\n                engine.setState(true, 0, false, false, material.cullBackFaces);\r\n            }\r\n\r\n            // Observables\r\n            this.onAfterShadowMapRenderObservable.notifyObservers(effect);\r\n            this.onAfterShadowMapRenderMeshObservable.notifyObservers(renderingMesh);\r\n        } else {\r\n            // Need to reset refresh rate of the shadowMap\r\n            if (this._shadowMap) {\r\n                this._shadowMap.resetRefreshCounter();\r\n            }\r\n        }\r\n    }\r\n\r\n    protected _applyFilterValues(): void {\r\n        if (!this._shadowMap) {\r\n            return;\r\n        }\r\n\r\n        if (this.filter === ShadowGenerator.FILTER_NONE || this.filter === ShadowGenerator.FILTER_PCSS) {\r\n            this._shadowMap.updateSamplingMode(Texture.NEAREST_SAMPLINGMODE);\r\n        } else {\r\n            this._shadowMap.updateSamplingMode(Texture.BILINEAR_SAMPLINGMODE);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Forces all the attached effect to compile to enable rendering only once ready vs. lazily compiling effects.\r\n     * @param onCompiled Callback triggered at the and of the effects compilation\r\n     * @param options Sets of optional options forcing the compilation with different modes\r\n     */\r\n    public forceCompilation(onCompiled?: (generator: IShadowGenerator) => void, options?: Partial<{ useInstances: boolean }>): void {\r\n        const localOptions = {\r\n            useInstances: false,\r\n            ...options,\r\n        };\r\n\r\n        const shadowMap = this.getShadowMap();\r\n        if (!shadowMap) {\r\n            if (onCompiled) {\r\n                onCompiled(this);\r\n            }\r\n            return;\r\n        }\r\n\r\n        const renderList = shadowMap.renderList;\r\n        if (!renderList) {\r\n            if (onCompiled) {\r\n                onCompiled(this);\r\n            }\r\n            return;\r\n        }\r\n\r\n        const subMeshes: SubMesh[] = [];\r\n        for (const mesh of renderList) {\r\n            subMeshes.push(...mesh.subMeshes);\r\n        }\r\n        if (subMeshes.length === 0) {\r\n            if (onCompiled) {\r\n                onCompiled(this);\r\n            }\r\n            return;\r\n        }\r\n\r\n        let currentIndex = 0;\r\n\r\n        const checkReady = () => {\r\n            if (!this._scene || !this._scene.getEngine()) {\r\n                return;\r\n            }\r\n\r\n            while (\r\n                this.isReady(\r\n                    subMeshes[currentIndex],\r\n                    localOptions.useInstances,\r\n                    subMeshes[currentIndex].getMaterial()?.needAlphaBlendingForMesh(subMeshes[currentIndex].getMesh()) ?? false\r\n                )\r\n            ) {\r\n                currentIndex++;\r\n                if (currentIndex >= subMeshes.length) {\r\n                    if (onCompiled) {\r\n                        onCompiled(this);\r\n                    }\r\n                    return;\r\n                }\r\n            }\r\n            setTimeout(checkReady, 16);\r\n        };\r\n\r\n        checkReady();\r\n    }\r\n\r\n    /**\r\n     * Forces all the attached effect to compile to enable rendering only once ready vs. lazily compiling effects.\r\n     * @param options Sets of optional options forcing the compilation with different modes\r\n     * @returns A promise that resolves when the compilation completes\r\n     */\r\n    public forceCompilationAsync(options?: Partial<{ useInstances: boolean }>): Promise<void> {\r\n        return new Promise((resolve) => {\r\n            this.forceCompilation(() => {\r\n                resolve();\r\n            }, options);\r\n        });\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _isReadyCustomDefines(defines: any, subMesh: SubMesh, useInstances: boolean): void {}\r\n\r\n    private _prepareShadowDefines(subMesh: SubMesh, useInstances: boolean, defines: string[], isTransparent: boolean): string[] {\r\n        defines.push(\"#define SM_LIGHTTYPE_\" + this._light.getClassName().toUpperCase());\r\n\r\n        defines.push(\"#define SM_FLOAT \" + (this._textureType !== Constants.TEXTURETYPE_UNSIGNED_INT ? \"1\" : \"0\"));\r\n\r\n        defines.push(\"#define SM_ESM \" + (this.useExponentialShadowMap || this.useBlurExponentialShadowMap ? \"1\" : \"0\"));\r\n\r\n        defines.push(\"#define SM_DEPTHTEXTURE \" + (this.usePercentageCloserFiltering || this.useContactHardeningShadow ? \"1\" : \"0\"));\r\n\r\n        const mesh = subMesh.getMesh();\r\n\r\n        // Normal bias.\r\n        defines.push(\"#define SM_NORMALBIAS \" + (this.normalBias && mesh.isVerticesDataPresent(VertexBuffer.NormalKind) ? \"1\" : \"0\"));\r\n        defines.push(\"#define SM_DIRECTIONINLIGHTDATA \" + (this.getLight().getTypeID() === Light.LIGHTTYPEID_DIRECTIONALLIGHT ? \"1\" : \"0\"));\r\n\r\n        // Point light\r\n        defines.push(\"#define SM_USEDISTANCE \" + (this._light.needCube() ? \"1\" : \"0\"));\r\n\r\n        // Soft transparent shadows\r\n        defines.push(\"#define SM_SOFTTRANSPARENTSHADOW \" + (this.enableSoftTransparentShadow && isTransparent ? \"1\" : \"0\"));\r\n\r\n        this._isReadyCustomDefines(defines, subMesh, useInstances);\r\n\r\n        return defines;\r\n    }\r\n\r\n    /**\r\n     * Determine whether the shadow generator is ready or not (mainly all effects and related post processes needs to be ready).\r\n     * @param subMesh The submesh we want to render in the shadow map\r\n     * @param useInstances Defines whether will draw in the map using instances\r\n     * @param isTransparent Indicates that isReady is called for a transparent subMesh\r\n     * @returns true if ready otherwise, false\r\n     */\r\n    public isReady(subMesh: SubMesh, useInstances: boolean, isTransparent: boolean): boolean {\r\n        const material = subMesh.getMaterial(),\r\n            shadowDepthWrapper = material?.shadowDepthWrapper;\r\n\r\n        this._opacityTexture = null;\r\n\r\n        if (!material) {\r\n            return false;\r\n        }\r\n\r\n        const defines: string[] = [];\r\n\r\n        this._prepareShadowDefines(subMesh, useInstances, defines, isTransparent);\r\n\r\n        if (shadowDepthWrapper) {\r\n            if (!shadowDepthWrapper.isReadyForSubMesh(subMesh, defines, this, useInstances, this._scene.getEngine().currentRenderPassId)) {\r\n                return false;\r\n            }\r\n        } else {\r\n            const subMeshEffect = subMesh._getDrawWrapper(undefined, true)!;\r\n\r\n            let effect = subMeshEffect.effect!;\r\n            let cachedDefines = subMeshEffect.defines;\r\n\r\n            const attribs = [VertexBuffer.PositionKind];\r\n\r\n            const mesh = subMesh.getMesh();\r\n\r\n            // Normal bias.\r\n            if (this.normalBias && mesh.isVerticesDataPresent(VertexBuffer.NormalKind)) {\r\n                attribs.push(VertexBuffer.NormalKind);\r\n                defines.push(\"#define NORMAL\");\r\n                if (mesh.nonUniformScaling) {\r\n                    defines.push(\"#define NONUNIFORMSCALING\");\r\n                }\r\n            }\r\n\r\n            // Alpha test\r\n            const needAlphaTesting = material.needAlphaTesting();\r\n\r\n            if (needAlphaTesting || material.needAlphaBlending()) {\r\n                if (this.useOpacityTextureForTransparentShadow) {\r\n                    this._opacityTexture = (material as any).opacityTexture;\r\n                } else {\r\n                    this._opacityTexture = material.getAlphaTestTexture();\r\n                }\r\n                if (this._opacityTexture) {\r\n                    if (!this._opacityTexture.isReady()) {\r\n                        return false;\r\n                    }\r\n\r\n                    const alphaCutOff = (material as any).alphaCutOff ?? ShadowGenerator.DEFAULT_ALPHA_CUTOFF;\r\n\r\n                    defines.push(\"#define ALPHATEXTURE\");\r\n                    if (needAlphaTesting) {\r\n                        defines.push(`#define ALPHATESTVALUE ${alphaCutOff}${alphaCutOff % 1 === 0 ? \".\" : \"\"}`);\r\n                    }\r\n                    if (mesh.isVerticesDataPresent(VertexBuffer.UVKind)) {\r\n                        attribs.push(VertexBuffer.UVKind);\r\n                        defines.push(\"#define UV1\");\r\n                    }\r\n                    if (mesh.isVerticesDataPresent(VertexBuffer.UV2Kind)) {\r\n                        if (this._opacityTexture.coordinatesIndex === 1) {\r\n                            attribs.push(VertexBuffer.UV2Kind);\r\n                            defines.push(\"#define UV2\");\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Bones\r\n            const fallbacks = new EffectFallbacks();\r\n            if (mesh.useBones && mesh.computeBonesUsingShaders && mesh.skeleton) {\r\n                attribs.push(VertexBuffer.MatricesIndicesKind);\r\n                attribs.push(VertexBuffer.MatricesWeightsKind);\r\n                if (mesh.numBoneInfluencers > 4) {\r\n                    attribs.push(VertexBuffer.MatricesIndicesExtraKind);\r\n                    attribs.push(VertexBuffer.MatricesWeightsExtraKind);\r\n                }\r\n                const skeleton = mesh.skeleton;\r\n                defines.push(\"#define NUM_BONE_INFLUENCERS \" + mesh.numBoneInfluencers);\r\n                if (mesh.numBoneInfluencers > 0) {\r\n                    fallbacks.addCPUSkinningFallback(0, mesh);\r\n                }\r\n\r\n                if (skeleton.isUsingTextureForMatrices) {\r\n                    defines.push(\"#define BONETEXTURE\");\r\n                } else {\r\n                    defines.push(\"#define BonesPerMesh \" + (skeleton.bones.length + 1));\r\n                }\r\n            } else {\r\n                defines.push(\"#define NUM_BONE_INFLUENCERS 0\");\r\n            }\r\n\r\n            // Morph targets\r\n            const manager = (<Mesh>mesh).morphTargetManager;\r\n            let morphInfluencers = 0;\r\n            if (manager) {\r\n                morphInfluencers = manager.numMaxInfluencers || manager.numInfluencers;\r\n                if (morphInfluencers > 0) {\r\n                    defines.push(\"#define MORPHTARGETS\");\r\n                    defines.push(\"#define NUM_MORPH_INFLUENCERS \" + morphInfluencers);\r\n                    if (manager.isUsingTextureForTargets) {\r\n                        defines.push(\"#define MORPHTARGETS_TEXTURE\");\r\n                    }\r\n                    PrepareAttributesForMorphTargetsInfluencers(attribs, mesh, morphInfluencers);\r\n                }\r\n            }\r\n\r\n            // ClipPlanes\r\n            prepareStringDefinesForClipPlanes(material, this._scene, defines);\r\n\r\n            // Instances\r\n            if (useInstances) {\r\n                defines.push(\"#define INSTANCES\");\r\n                PushAttributesForInstances(attribs);\r\n                if (subMesh.getRenderingMesh().hasThinInstances) {\r\n                    defines.push(\"#define THIN_INSTANCES\");\r\n                }\r\n            }\r\n\r\n            if (this.customShaderOptions) {\r\n                if (this.customShaderOptions.defines) {\r\n                    for (const define of this.customShaderOptions.defines) {\r\n                        if (defines.indexOf(define) === -1) {\r\n                            defines.push(define);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Get correct effect\r\n            const join = defines.join(\"\\n\");\r\n            if (cachedDefines !== join) {\r\n                cachedDefines = join;\r\n\r\n                let shaderName = \"shadowMap\";\r\n                const uniforms = [\r\n                    \"world\",\r\n                    \"mBones\",\r\n                    \"viewProjection\",\r\n                    \"diffuseMatrix\",\r\n                    \"lightDataSM\",\r\n                    \"depthValuesSM\",\r\n                    \"biasAndScaleSM\",\r\n                    \"morphTargetInfluences\",\r\n                    \"morphTargetCount\",\r\n                    \"boneTextureWidth\",\r\n                    \"softTransparentShadowSM\",\r\n                    \"morphTargetTextureInfo\",\r\n                    \"morphTargetTextureIndices\",\r\n                ];\r\n                const samplers = [\"diffuseSampler\", \"boneSampler\", \"morphTargets\"];\r\n                const uniformBuffers = [\"Scene\", \"Mesh\"];\r\n\r\n                addClipPlaneUniforms(uniforms);\r\n\r\n                // Custom shader?\r\n                if (this.customShaderOptions) {\r\n                    shaderName = this.customShaderOptions.shaderName;\r\n\r\n                    if (this.customShaderOptions.attributes) {\r\n                        for (const attrib of this.customShaderOptions.attributes) {\r\n                            if (attribs.indexOf(attrib) === -1) {\r\n                                attribs.push(attrib);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    if (this.customShaderOptions.uniforms) {\r\n                        for (const uniform of this.customShaderOptions.uniforms) {\r\n                            if (uniforms.indexOf(uniform) === -1) {\r\n                                uniforms.push(uniform);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    if (this.customShaderOptions.samplers) {\r\n                        for (const sampler of this.customShaderOptions.samplers) {\r\n                            if (samplers.indexOf(sampler) === -1) {\r\n                                samplers.push(sampler);\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                const engine = this._scene.getEngine();\r\n\r\n                effect = engine.createEffect(\r\n                    shaderName,\r\n                    <IEffectCreationOptions>{\r\n                        attributes: attribs,\r\n                        uniformsNames: uniforms,\r\n                        uniformBuffersNames: uniformBuffers,\r\n                        samplers: samplers,\r\n                        defines: join,\r\n                        fallbacks: fallbacks,\r\n                        onCompiled: null,\r\n                        onError: null,\r\n                        indexParameters: { maxSimultaneousMorphTargets: morphInfluencers },\r\n                    },\r\n                    engine\r\n                );\r\n\r\n                subMeshEffect.setEffect(effect, cachedDefines);\r\n            }\r\n\r\n            if (!effect.isReady()) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        if (this.useBlurExponentialShadowMap || this.useBlurCloseExponentialShadowMap) {\r\n            if (!this._blurPostProcesses || !this._blurPostProcesses.length) {\r\n                this._initializeBlurRTTAndPostProcesses();\r\n            }\r\n        }\r\n\r\n        if (this._kernelBlurXPostprocess && !this._kernelBlurXPostprocess.isReady()) {\r\n            return false;\r\n        }\r\n        if (this._kernelBlurYPostprocess && !this._kernelBlurYPostprocess.isReady()) {\r\n            return false;\r\n        }\r\n        if (this._boxBlurPostprocess && !this._boxBlurPostprocess.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Prepare all the defines in a material relying on a shadow map at the specified light index.\r\n     * @param defines Defines of the material we want to update\r\n     * @param lightIndex Index of the light in the enabled light list of the material\r\n     */\r\n    public prepareDefines(defines: any, lightIndex: number): void {\r\n        const scene = this._scene;\r\n        const light = this._light;\r\n\r\n        if (!scene.shadowsEnabled || !light.shadowEnabled) {\r\n            return;\r\n        }\r\n\r\n        defines[\"SHADOW\" + lightIndex] = true;\r\n\r\n        if (this.useContactHardeningShadow) {\r\n            defines[\"SHADOWPCSS\" + lightIndex] = true;\r\n            if (this._filteringQuality === ShadowGenerator.QUALITY_LOW) {\r\n                defines[\"SHADOWLOWQUALITY\" + lightIndex] = true;\r\n            } else if (this._filteringQuality === ShadowGenerator.QUALITY_MEDIUM) {\r\n                defines[\"SHADOWMEDIUMQUALITY\" + lightIndex] = true;\r\n            }\r\n            // else default to high.\r\n        } else if (this.usePercentageCloserFiltering) {\r\n            defines[\"SHADOWPCF\" + lightIndex] = true;\r\n            if (this._filteringQuality === ShadowGenerator.QUALITY_LOW) {\r\n                defines[\"SHADOWLOWQUALITY\" + lightIndex] = true;\r\n            } else if (this._filteringQuality === ShadowGenerator.QUALITY_MEDIUM) {\r\n                defines[\"SHADOWMEDIUMQUALITY\" + lightIndex] = true;\r\n            }\r\n            // else default to high.\r\n        } else if (this.usePoissonSampling) {\r\n            defines[\"SHADOWPOISSON\" + lightIndex] = true;\r\n        } else if (this.useExponentialShadowMap || this.useBlurExponentialShadowMap) {\r\n            defines[\"SHADOWESM\" + lightIndex] = true;\r\n        } else if (this.useCloseExponentialShadowMap || this.useBlurCloseExponentialShadowMap) {\r\n            defines[\"SHADOWCLOSEESM\" + lightIndex] = true;\r\n        }\r\n\r\n        if (light.needCube()) {\r\n            defines[\"SHADOWCUBE\" + lightIndex] = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Binds the shadow related information inside of an effect (information like near, far, darkness...\r\n     * defined in the generator but impacting the effect).\r\n     * @param lightIndex Index of the light in the enabled light list of the material owning the effect\r\n     * @param effect The effect we are binding the information for\r\n     */\r\n    public bindShadowLight(lightIndex: string, effect: Effect): void {\r\n        const light = this._light;\r\n        const scene = this._scene;\r\n\r\n        if (!scene.shadowsEnabled || !light.shadowEnabled) {\r\n            return;\r\n        }\r\n\r\n        const camera = this._getCamera();\r\n        if (!camera) {\r\n            return;\r\n        }\r\n\r\n        const shadowMap = this.getShadowMap();\r\n\r\n        if (!shadowMap) {\r\n            return;\r\n        }\r\n\r\n        if (!light.needCube()) {\r\n            effect.setMatrix(\"lightMatrix\" + lightIndex, this.getTransformMatrix());\r\n        }\r\n\r\n        // Only PCF uses depth stencil texture.\r\n        if (this._filter === ShadowGenerator.FILTER_PCF) {\r\n            effect.setDepthStencilTexture(\"shadowSampler\" + lightIndex, this.getShadowMapForRendering());\r\n            light._uniformBuffer.updateFloat4(\"shadowsInfo\", this.getDarkness(), shadowMap.getSize().width, 1 / shadowMap.getSize().width, this.frustumEdgeFalloff, lightIndex);\r\n        } else if (this._filter === ShadowGenerator.FILTER_PCSS) {\r\n            effect.setDepthStencilTexture(\"shadowSampler\" + lightIndex, this.getShadowMapForRendering());\r\n            effect.setTexture(\"depthSampler\" + lightIndex, this.getShadowMapForRendering());\r\n            light._uniformBuffer.updateFloat4(\r\n                \"shadowsInfo\",\r\n                this.getDarkness(),\r\n                1 / shadowMap.getSize().width,\r\n                this._contactHardeningLightSizeUVRatio * shadowMap.getSize().width,\r\n                this.frustumEdgeFalloff,\r\n                lightIndex\r\n            );\r\n        } else {\r\n            effect.setTexture(\"shadowSampler\" + lightIndex, this.getShadowMapForRendering());\r\n            light._uniformBuffer.updateFloat4(\"shadowsInfo\", this.getDarkness(), this.blurScale / shadowMap.getSize().width, this.depthScale, this.frustumEdgeFalloff, lightIndex);\r\n        }\r\n\r\n        light._uniformBuffer.updateFloat2(\r\n            \"depthValues\",\r\n            this.getLight().getDepthMinZ(camera),\r\n            this.getLight().getDepthMinZ(camera) + this.getLight().getDepthMaxZ(camera),\r\n            lightIndex\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the view matrix used to render the shadow map.\r\n     */\r\n    public get viewMatrix() {\r\n        return this._viewMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the projection matrix used to render the shadow map.\r\n     */\r\n    public get projectionMatrix() {\r\n        return this._projectionMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the transformation matrix used to project the meshes into the map from the light point of view.\r\n     * (eq to shadow projection matrix * light transform matrix)\r\n     * @returns The transform matrix used to create the shadow map\r\n     */\r\n    public getTransformMatrix(): Matrix {\r\n        const scene = this._scene;\r\n        if (this._currentRenderId === scene.getRenderId() && this._currentFaceIndexCache === this._currentFaceIndex) {\r\n            return this._transformMatrix;\r\n        }\r\n\r\n        this._currentRenderId = scene.getRenderId();\r\n        this._currentFaceIndexCache = this._currentFaceIndex;\r\n\r\n        let lightPosition = this._light.position;\r\n        if (this._light.computeTransformedInformation()) {\r\n            lightPosition = this._light.transformedPosition;\r\n        }\r\n\r\n        Vector3.NormalizeToRef(this._light.getShadowDirection(this._currentFaceIndex), this._lightDirection);\r\n        if (Math.abs(Vector3.Dot(this._lightDirection, Vector3.Up())) === 1.0) {\r\n            this._lightDirection.z = 0.0000000000001; // Required to avoid perfectly perpendicular light\r\n        }\r\n\r\n        if (\r\n            this._light.needProjectionMatrixCompute() ||\r\n            !this._cachedPosition ||\r\n            !this._cachedDirection ||\r\n            !lightPosition.equals(this._cachedPosition) ||\r\n            !this._lightDirection.equals(this._cachedDirection)\r\n        ) {\r\n            this._cachedPosition.copyFrom(lightPosition);\r\n            this._cachedDirection.copyFrom(this._lightDirection);\r\n\r\n            Matrix.LookAtLHToRef(lightPosition, lightPosition.add(this._lightDirection), Vector3.Up(), this._viewMatrix);\r\n\r\n            const shadowMap = this.getShadowMap();\r\n\r\n            if (shadowMap) {\r\n                const renderList = shadowMap.renderList;\r\n\r\n                if (renderList) {\r\n                    this._light.setShadowProjectionMatrix(this._projectionMatrix, this._viewMatrix, renderList);\r\n                }\r\n            }\r\n\r\n            this._viewMatrix.multiplyToRef(this._projectionMatrix, this._transformMatrix);\r\n        }\r\n\r\n        return this._transformMatrix;\r\n    }\r\n\r\n    /**\r\n     * Recreates the shadow map dependencies like RTT and post processes. This can be used during the switch between\r\n     * Cube and 2D textures for instance.\r\n     */\r\n    public recreateShadowMap(): void {\r\n        const shadowMap = this._shadowMap;\r\n        if (!shadowMap) {\r\n            return;\r\n        }\r\n\r\n        // Track render list.\r\n        const renderList = shadowMap.renderList;\r\n        // Clean up existing data.\r\n        this._disposeRTTandPostProcesses();\r\n        // Reinitializes.\r\n        this._initializeGenerator();\r\n        // Reaffect the filter to ensure a correct fallback if necessary.\r\n        this.filter = this._filter;\r\n        // Reaffect the filter.\r\n        this._applyFilterValues();\r\n        // Reaffect Render List.\r\n        if (renderList) {\r\n            // Note: don't do this._shadowMap!.renderList = renderList;\r\n            // The renderList hooked array is accessing the old RenderTargetTexture (see RenderTargetTexture._hookArray), which is disposed at this point (by the call to _disposeRTTandPostProcesses)\r\n            if (!this._shadowMap!.renderList) {\r\n                this._shadowMap!.renderList = [];\r\n            }\r\n            for (const mesh of renderList) {\r\n                this._shadowMap!.renderList.push(mesh);\r\n            }\r\n        } else {\r\n            this._shadowMap!.renderList = null;\r\n        }\r\n    }\r\n\r\n    protected _disposeBlurPostProcesses(): void {\r\n        if (this._shadowMap2) {\r\n            this._shadowMap2.dispose();\r\n            this._shadowMap2 = null;\r\n        }\r\n\r\n        if (this._boxBlurPostprocess) {\r\n            this._boxBlurPostprocess.dispose();\r\n            this._boxBlurPostprocess = null;\r\n        }\r\n\r\n        if (this._kernelBlurXPostprocess) {\r\n            this._kernelBlurXPostprocess.dispose();\r\n            this._kernelBlurXPostprocess = null;\r\n        }\r\n\r\n        if (this._kernelBlurYPostprocess) {\r\n            this._kernelBlurYPostprocess.dispose();\r\n            this._kernelBlurYPostprocess = null;\r\n        }\r\n\r\n        this._blurPostProcesses = [];\r\n    }\r\n\r\n    protected _disposeRTTandPostProcesses(): void {\r\n        if (this._shadowMap) {\r\n            this._shadowMap.dispose();\r\n            this._shadowMap = null;\r\n        }\r\n\r\n        this._disposeBlurPostProcesses();\r\n    }\r\n\r\n    protected _disposeSceneUBOs(): void {\r\n        if (this._sceneUBOs) {\r\n            for (const ubo of this._sceneUBOs) {\r\n                ubo.dispose();\r\n            }\r\n            this._sceneUBOs = [];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disposes the ShadowGenerator.\r\n     * Returns nothing.\r\n     */\r\n    public dispose(): void {\r\n        this._disposeRTTandPostProcesses();\r\n\r\n        this._disposeSceneUBOs();\r\n\r\n        if (this._light) {\r\n            if (this._light._shadowGenerators) {\r\n                const iterator = this._light._shadowGenerators.entries();\r\n                for (let entry = iterator.next(); entry.done !== true; entry = iterator.next()) {\r\n                    const [camera, shadowGenerator] = entry.value;\r\n                    if (shadowGenerator === this) {\r\n                        this._light._shadowGenerators.delete(camera);\r\n                    }\r\n                }\r\n                if (this._light._shadowGenerators.size === 0) {\r\n                    this._light._shadowGenerators = null;\r\n                }\r\n            }\r\n            this._light._markMeshesAsLightDirty();\r\n        }\r\n\r\n        this.onBeforeShadowMapRenderMeshObservable.clear();\r\n        this.onBeforeShadowMapRenderObservable.clear();\r\n        this.onAfterShadowMapRenderMeshObservable.clear();\r\n        this.onAfterShadowMapRenderObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Serializes the shadow generator setup to a json object.\r\n     * @returns The serialized JSON object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n        const shadowMap = this.getShadowMap();\r\n\r\n        if (!shadowMap) {\r\n            return serializationObject;\r\n        }\r\n\r\n        serializationObject.className = this.getClassName();\r\n        serializationObject.lightId = this._light.id;\r\n        serializationObject.cameraId = this._camera?.id;\r\n        serializationObject.id = this.id;\r\n        serializationObject.mapSize = shadowMap.getRenderSize();\r\n        serializationObject.forceBackFacesOnly = this.forceBackFacesOnly;\r\n        serializationObject.darkness = this.getDarkness();\r\n        serializationObject.transparencyShadow = this._transparencyShadow;\r\n        serializationObject.frustumEdgeFalloff = this.frustumEdgeFalloff;\r\n        serializationObject.bias = this.bias;\r\n        serializationObject.normalBias = this.normalBias;\r\n        serializationObject.usePercentageCloserFiltering = this.usePercentageCloserFiltering;\r\n        serializationObject.useContactHardeningShadow = this.useContactHardeningShadow;\r\n        serializationObject.contactHardeningLightSizeUVRatio = this.contactHardeningLightSizeUVRatio;\r\n        serializationObject.filteringQuality = this.filteringQuality;\r\n        serializationObject.useExponentialShadowMap = this.useExponentialShadowMap;\r\n        serializationObject.useBlurExponentialShadowMap = this.useBlurExponentialShadowMap;\r\n        serializationObject.useCloseExponentialShadowMap = this.useBlurExponentialShadowMap;\r\n        serializationObject.useBlurCloseExponentialShadowMap = this.useBlurExponentialShadowMap;\r\n        serializationObject.usePoissonSampling = this.usePoissonSampling;\r\n        serializationObject.depthScale = this.depthScale;\r\n        serializationObject.blurBoxOffset = this.blurBoxOffset;\r\n        serializationObject.blurKernel = this.blurKernel;\r\n        serializationObject.blurScale = this.blurScale;\r\n        serializationObject.useKernelBlur = this.useKernelBlur;\r\n\r\n        serializationObject.renderList = [];\r\n        if (shadowMap.renderList) {\r\n            for (let meshIndex = 0; meshIndex < shadowMap.renderList.length; meshIndex++) {\r\n                const mesh = shadowMap.renderList[meshIndex];\r\n\r\n                serializationObject.renderList.push(mesh.id);\r\n            }\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parses a serialized ShadowGenerator and returns a new ShadowGenerator.\r\n     * @param parsedShadowGenerator The JSON object to parse\r\n     * @param scene The scene to create the shadow map for\r\n     * @param constr A function that builds a shadow generator or undefined to create an instance of the default shadow generator\r\n     * @returns The parsed shadow generator\r\n     */\r\n    public static Parse(parsedShadowGenerator: any, scene: Scene, constr?: (mapSize: number, light: IShadowLight, camera: Nullable<Camera>) => ShadowGenerator): ShadowGenerator {\r\n        const light = <IShadowLight>scene.getLightById(parsedShadowGenerator.lightId);\r\n        const camera: Nullable<Camera> = parsedShadowGenerator.cameraId !== undefined ? scene.getCameraById(parsedShadowGenerator.cameraId) : null;\r\n        const shadowGenerator = constr ? constr(parsedShadowGenerator.mapSize, light, camera) : new ShadowGenerator(parsedShadowGenerator.mapSize, light, undefined, camera);\r\n        const shadowMap = shadowGenerator.getShadowMap();\r\n\r\n        for (let meshIndex = 0; meshIndex < parsedShadowGenerator.renderList.length; meshIndex++) {\r\n            const meshes = scene.getMeshesById(parsedShadowGenerator.renderList[meshIndex]);\r\n            meshes.forEach(function (mesh) {\r\n                if (!shadowMap) {\r\n                    return;\r\n                }\r\n                if (!shadowMap.renderList) {\r\n                    shadowMap.renderList = [];\r\n                }\r\n                shadowMap.renderList.push(mesh);\r\n            });\r\n        }\r\n\r\n        if (parsedShadowGenerator.id !== undefined) {\r\n            shadowGenerator.id = parsedShadowGenerator.id;\r\n        }\r\n\r\n        shadowGenerator.forceBackFacesOnly = !!parsedShadowGenerator.forceBackFacesOnly;\r\n\r\n        if (parsedShadowGenerator.darkness !== undefined) {\r\n            shadowGenerator.setDarkness(parsedShadowGenerator.darkness);\r\n        }\r\n\r\n        if (parsedShadowGenerator.transparencyShadow) {\r\n            shadowGenerator.setTransparencyShadow(true);\r\n        }\r\n\r\n        if (parsedShadowGenerator.frustumEdgeFalloff !== undefined) {\r\n            shadowGenerator.frustumEdgeFalloff = parsedShadowGenerator.frustumEdgeFalloff;\r\n        }\r\n\r\n        if (parsedShadowGenerator.bias !== undefined) {\r\n            shadowGenerator.bias = parsedShadowGenerator.bias;\r\n        }\r\n\r\n        if (parsedShadowGenerator.normalBias !== undefined) {\r\n            shadowGenerator.normalBias = parsedShadowGenerator.normalBias;\r\n        }\r\n\r\n        if (parsedShadowGenerator.usePercentageCloserFiltering) {\r\n            shadowGenerator.usePercentageCloserFiltering = true;\r\n        } else if (parsedShadowGenerator.useContactHardeningShadow) {\r\n            shadowGenerator.useContactHardeningShadow = true;\r\n        } else if (parsedShadowGenerator.usePoissonSampling) {\r\n            shadowGenerator.usePoissonSampling = true;\r\n        } else if (parsedShadowGenerator.useExponentialShadowMap) {\r\n            shadowGenerator.useExponentialShadowMap = true;\r\n        } else if (parsedShadowGenerator.useBlurExponentialShadowMap) {\r\n            shadowGenerator.useBlurExponentialShadowMap = true;\r\n        } else if (parsedShadowGenerator.useCloseExponentialShadowMap) {\r\n            shadowGenerator.useCloseExponentialShadowMap = true;\r\n        } else if (parsedShadowGenerator.useBlurCloseExponentialShadowMap) {\r\n            shadowGenerator.useBlurCloseExponentialShadowMap = true;\r\n        }\r\n        // Backward compat\r\n        else if (parsedShadowGenerator.useVarianceShadowMap) {\r\n            shadowGenerator.useExponentialShadowMap = true;\r\n        } else if (parsedShadowGenerator.useBlurVarianceShadowMap) {\r\n            shadowGenerator.useBlurExponentialShadowMap = true;\r\n        }\r\n\r\n        if (parsedShadowGenerator.contactHardeningLightSizeUVRatio !== undefined) {\r\n            shadowGenerator.contactHardeningLightSizeUVRatio = parsedShadowGenerator.contactHardeningLightSizeUVRatio;\r\n        }\r\n\r\n        if (parsedShadowGenerator.filteringQuality !== undefined) {\r\n            shadowGenerator.filteringQuality = parsedShadowGenerator.filteringQuality;\r\n        }\r\n\r\n        if (parsedShadowGenerator.depthScale) {\r\n            shadowGenerator.depthScale = parsedShadowGenerator.depthScale;\r\n        }\r\n\r\n        if (parsedShadowGenerator.blurScale) {\r\n            shadowGenerator.blurScale = parsedShadowGenerator.blurScale;\r\n        }\r\n\r\n        if (parsedShadowGenerator.blurBoxOffset) {\r\n            shadowGenerator.blurBoxOffset = parsedShadowGenerator.blurBoxOffset;\r\n        }\r\n\r\n        if (parsedShadowGenerator.useKernelBlur) {\r\n            shadowGenerator.useKernelBlur = parsedShadowGenerator.useKernelBlur;\r\n        }\r\n\r\n        if (parsedShadowGenerator.blurKernel) {\r\n            shadowGenerator.blurKernel = parsedShadowGenerator.blurKernel;\r\n        }\r\n\r\n        return shadowGenerator;\r\n    }\r\n}\r\n"]}