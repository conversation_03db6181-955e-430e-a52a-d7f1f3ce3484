{"version": 3, "file": "shaderProcessingOptions.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Processors/shaderProcessingOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { IShaderProcessor } from \"./iShaderProcessor\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * Function for custom code generation\r\n */\r\nexport type ShaderCustomProcessingFunction = (shaderType: string, code: string) => string;\r\n\r\n/** @internal */\r\nexport interface ShaderProcessingContext {}\r\n\r\n/** @internal */\r\nexport interface ProcessingOptions {\r\n    defines: string[];\r\n    indexParameters: any;\r\n    isFragment: boolean;\r\n    shouldUseHighPrecisionShader: boolean;\r\n    supportsUniformBuffers: boolean;\r\n    shadersRepository: string;\r\n    includesShadersStore: { [key: string]: string };\r\n    processor: Nullable<IShaderProcessor>;\r\n    version: string;\r\n    platformName: string;\r\n    lookForClosingBracketForUniformBuffer?: boolean;\r\n    processingContext: Nullable<ShaderProcessingContext>;\r\n    isNDCHalfZRange: boolean;\r\n    useReverseDepthBuffer: boolean;\r\n    processCodeAfterIncludes?: ShaderCustomProcessingFunction;\r\n}\r\n"]}