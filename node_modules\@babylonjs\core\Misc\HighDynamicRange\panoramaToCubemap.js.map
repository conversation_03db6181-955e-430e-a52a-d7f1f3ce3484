{"version": 3, "file": "panoramaToCubemap.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Misc/HighDynamicRange/panoramaToCubemap.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAqEpD;;GAEG;AACH,MAAM,OAAO,sBAAsB;IAQ/B;;;;;;;;;OASG;IACI,MAAM,CAAC,wBAAwB,CAAC,YAA0B,EAAE,UAAkB,EAAE,WAAmB,EAAE,IAAY,EAAE,WAAW,GAAG,KAAK;QACzI,IAAI,CAAC,YAAY,EAAE;YACf,4CAA4C;YAC5C,MAAM,gDAAgD,CAAC;SAC1D;QAED,IAAI,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG,WAAW,GAAG,CAAC,EAAE;YACrD,4CAA4C;YAC5C,MAAM,+CAA+C,CAAC;SACzD;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QAC1H,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QACxH,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QACxH,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QAC1H,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QACpH,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QAExH,OAAO;YACH,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,WAAW;YACjB,KAAK,EAAE,YAAY;YACnB,EAAE,EAAE,SAAS;YACb,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,SAAS,CAAC,iBAAiB;YACjC,MAAM,EAAE,SAAS,CAAC,iBAAiB;YACnC,UAAU,EAAE,KAAK;SACpB,CAAC;IACN,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,OAAe,EAAE,QAAmB,EAAE,YAA0B,EAAE,UAAkB,EAAE,WAAmB,EAAE,WAAW,GAAG,KAAK;QAC9J,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,OAAO,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1D,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;QAE9C,6GAA6G;QAC7G,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpF,MAAM,YAAY,GAAG,CAAC,GAAG,OAAO,CAAC;QACjC,MAAM,eAAe,GAAG,YAAY,GAAG,YAAY,CAAC;QAEpD,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC;QAC/E,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC;QAE/E,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC;QACvB,IAAI,EAAE,GAAG,CAAC,CAAC;QAEX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;YAC9B,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,OAAO,EAAE,EAAE,EAAE,EAAE;gBACjC,IAAI,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACtB,IAAI,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAEtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;oBAC9B,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,OAAO,EAAE,EAAE,EAAE,EAAE;wBACjC,MAAM,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;wBAC/C,CAAC,CAAC,SAAS,EAAE,CAAC;wBAEd,MAAM,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;wBAErF,wBAAwB;wBACxB,YAAY,CAAC,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,eAAe,CAAC;wBACvE,YAAY,CAAC,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,eAAe,CAAC;wBACvE,YAAY,CAAC,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,eAAe,CAAC;wBAEvE,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;wBACtB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;qBACzB;iBACJ;gBAED,EAAE,IAAI,EAAE,GAAG,YAAY,CAAC;aAC3B;SACJ;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAEO,MAAM,CAAC,uBAAuB,CAAC,IAAa,EAAE,YAA0B,EAAE,UAAkB,EAAE,WAAmB;QACrH,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE9B,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;YACrB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;SACxB;QACD,OAAO,KAAK,GAAG,IAAI,CAAC,EAAE,EAAE;YACpB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;SACxB;QAED,IAAI,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC;QACzB,MAAM,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QAEzB,YAAY;QACZ,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;QAEpB,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,UAAU,CAAC,CAAC;QACrC,IAAI,EAAE,GAAG,CAAC,EAAE;YACR,EAAE,GAAG,CAAC,CAAC;SACV;aAAM,IAAI,EAAE,IAAI,UAAU,EAAE;YACzB,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;SACvB;QAED,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,WAAW,CAAC,CAAC;QACtC,IAAI,EAAE,GAAG,CAAC,EAAE;YACR,EAAE,GAAG,CAAC,CAAC;SACV;aAAM,IAAI,EAAE,IAAI,WAAW,EAAE;YAC1B,EAAE,GAAG,WAAW,GAAG,CAAC,CAAC;SACxB;QAED,MAAM,MAAM,GAAG,WAAW,GAAG,EAAE,GAAG,CAAC,CAAC;QACpC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7D,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7D,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAE7D,OAAO;YACH,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;SACP,CAAC;IACN,CAAC;;AAtIc,gCAAS,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACrI,iCAAU,GAAG,CAAC,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAClI,iCAAU,GAAG,CAAC,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAClI,gCAAS,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACrI,gCAAS,GAAG,CAAC,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AACjI,8BAAO,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"../../types\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { Constants } from \"../../Engines/constants\";\r\n\r\n/**\r\n * CubeMap information grouping all the data for each faces as well as the cubemap size.\r\n */\r\nexport interface CubeMapInfo {\r\n    /**\r\n     * The pixel array for the front face.\r\n     * This is stored in format, left to right, up to down format.\r\n     */\r\n    front: Nullable<ArrayBufferView>;\r\n\r\n    /**\r\n     * The pixel array for the back face.\r\n     * This is stored in format, left to right, up to down format.\r\n     */\r\n    back: Nullable<ArrayBufferView>;\r\n\r\n    /**\r\n     * The pixel array for the left face.\r\n     * This is stored in format, left to right, up to down format.\r\n     */\r\n    left: Nullable<ArrayBufferView>;\r\n\r\n    /**\r\n     * The pixel array for the right face.\r\n     * This is stored in format, left to right, up to down format.\r\n     */\r\n    right: Nullable<ArrayBufferView>;\r\n\r\n    /**\r\n     * The pixel array for the up face.\r\n     * This is stored in format, left to right, up to down format.\r\n     */\r\n    up: Nullable<ArrayBufferView>;\r\n\r\n    /**\r\n     * The pixel array for the down face.\r\n     * This is stored in format, left to right, up to down format.\r\n     */\r\n    down: Nullable<ArrayBufferView>;\r\n\r\n    /**\r\n     * The size of the cubemap stored.\r\n     *\r\n     * Each faces will be size * size pixels.\r\n     */\r\n    size: number;\r\n\r\n    /**\r\n     * The format of the texture.\r\n     *\r\n     * RGBA, RGB.\r\n     */\r\n    format: number;\r\n\r\n    /**\r\n     * The type of the texture data.\r\n     *\r\n     * UNSIGNED_INT, FLOAT.\r\n     */\r\n    type: number;\r\n\r\n    /**\r\n     * Specifies whether the texture is in gamma space.\r\n     */\r\n    gammaSpace: boolean;\r\n}\r\n\r\n/**\r\n * Helper class useful to convert panorama picture to their cubemap representation in 6 faces.\r\n */\r\nexport class PanoramaToCubeMapTools {\r\n    private static FACE_LEFT = [new Vector3(-1.0, -1.0, -1.0), new Vector3(1.0, -1.0, -1.0), new Vector3(-1.0, 1.0, -1.0), new Vector3(1.0, 1.0, -1.0)];\r\n    private static FACE_RIGHT = [new Vector3(1.0, -1.0, 1.0), new Vector3(-1.0, -1.0, 1.0), new Vector3(1.0, 1.0, 1.0), new Vector3(-1.0, 1.0, 1.0)];\r\n    private static FACE_FRONT = [new Vector3(1.0, -1.0, -1.0), new Vector3(1.0, -1.0, 1.0), new Vector3(1.0, 1.0, -1.0), new Vector3(1.0, 1.0, 1.0)];\r\n    private static FACE_BACK = [new Vector3(-1.0, -1.0, 1.0), new Vector3(-1.0, -1.0, -1.0), new Vector3(-1.0, 1.0, 1.0), new Vector3(-1.0, 1.0, -1.0)];\r\n    private static FACE_DOWN = [new Vector3(1.0, 1.0, -1.0), new Vector3(1.0, 1.0, 1.0), new Vector3(-1.0, 1.0, -1.0), new Vector3(-1.0, 1.0, 1.0)];\r\n    private static FACE_UP = [new Vector3(-1.0, -1.0, -1.0), new Vector3(-1.0, -1.0, 1.0), new Vector3(1.0, -1.0, -1.0), new Vector3(1.0, -1.0, 1.0)];\r\n\r\n    /**\r\n     * Converts a panorama stored in RGB right to left up to down format into a cubemap (6 faces).\r\n     *\r\n     * @param float32Array The source data.\r\n     * @param inputWidth The width of the input panorama.\r\n     * @param inputHeight The height of the input panorama.\r\n     * @param size The willing size of the generated cubemap (each faces will be size * size pixels)\r\n     * @param supersample enable supersampling the cubemap\r\n     * @returns The cubemap data\r\n     */\r\n    public static ConvertPanoramaToCubemap(float32Array: Float32Array, inputWidth: number, inputHeight: number, size: number, supersample = false): CubeMapInfo {\r\n        if (!float32Array) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"ConvertPanoramaToCubemap: input cannot be null\";\r\n        }\r\n\r\n        if (float32Array.length != inputWidth * inputHeight * 3) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"ConvertPanoramaToCubemap: input size is wrong\";\r\n        }\r\n\r\n        const textureFront = this.CreateCubemapTexture(size, this.FACE_FRONT, float32Array, inputWidth, inputHeight, supersample);\r\n        const textureBack = this.CreateCubemapTexture(size, this.FACE_BACK, float32Array, inputWidth, inputHeight, supersample);\r\n        const textureLeft = this.CreateCubemapTexture(size, this.FACE_LEFT, float32Array, inputWidth, inputHeight, supersample);\r\n        const textureRight = this.CreateCubemapTexture(size, this.FACE_RIGHT, float32Array, inputWidth, inputHeight, supersample);\r\n        const textureUp = this.CreateCubemapTexture(size, this.FACE_UP, float32Array, inputWidth, inputHeight, supersample);\r\n        const textureDown = this.CreateCubemapTexture(size, this.FACE_DOWN, float32Array, inputWidth, inputHeight, supersample);\r\n\r\n        return {\r\n            front: textureFront,\r\n            back: textureBack,\r\n            left: textureLeft,\r\n            right: textureRight,\r\n            up: textureUp,\r\n            down: textureDown,\r\n            size: size,\r\n            type: Constants.TEXTURETYPE_FLOAT,\r\n            format: Constants.TEXTUREFORMAT_RGB,\r\n            gammaSpace: false,\r\n        };\r\n    }\r\n\r\n    private static CreateCubemapTexture(texSize: number, faceData: Vector3[], float32Array: Float32Array, inputWidth: number, inputHeight: number, supersample = false) {\r\n        const buffer = new ArrayBuffer(texSize * texSize * 4 * 3);\r\n        const textureArray = new Float32Array(buffer);\r\n\r\n        // If supersampling, determine number of samples needed when source texture width is divided for 4 cube faces\r\n        const samples = supersample ? Math.max(1, Math.round(inputWidth / 4 / texSize)) : 1;\r\n        const sampleFactor = 1 / samples;\r\n        const sampleFactorSqr = sampleFactor * sampleFactor;\r\n\r\n        const rotDX1 = faceData[1].subtract(faceData[0]).scale(sampleFactor / texSize);\r\n        const rotDX2 = faceData[3].subtract(faceData[2]).scale(sampleFactor / texSize);\r\n\r\n        const dy = 1 / texSize;\r\n        let fy = 0;\r\n\r\n        for (let y = 0; y < texSize; y++) {\r\n            for (let sy = 0; sy < samples; sy++) {\r\n                let xv1 = faceData[0];\r\n                let xv2 = faceData[2];\r\n\r\n                for (let x = 0; x < texSize; x++) {\r\n                    for (let sx = 0; sx < samples; sx++) {\r\n                        const v = xv2.subtract(xv1).scale(fy).add(xv1);\r\n                        v.normalize();\r\n\r\n                        const color = this.CalcProjectionSpherical(v, float32Array, inputWidth, inputHeight);\r\n\r\n                        // 3 channels per pixels\r\n                        textureArray[y * texSize * 3 + x * 3 + 0] += color.r * sampleFactorSqr;\r\n                        textureArray[y * texSize * 3 + x * 3 + 1] += color.g * sampleFactorSqr;\r\n                        textureArray[y * texSize * 3 + x * 3 + 2] += color.b * sampleFactorSqr;\r\n\r\n                        xv1 = xv1.add(rotDX1);\r\n                        xv2 = xv2.add(rotDX2);\r\n                    }\r\n                }\r\n\r\n                fy += dy * sampleFactor;\r\n            }\r\n        }\r\n\r\n        return textureArray;\r\n    }\r\n\r\n    private static CalcProjectionSpherical(vDir: Vector3, float32Array: Float32Array, inputWidth: number, inputHeight: number): any {\r\n        let theta = Math.atan2(vDir.z, vDir.x);\r\n        const phi = Math.acos(vDir.y);\r\n\r\n        while (theta < -Math.PI) {\r\n            theta += 2 * Math.PI;\r\n        }\r\n        while (theta > Math.PI) {\r\n            theta -= 2 * Math.PI;\r\n        }\r\n\r\n        let dx = theta / Math.PI;\r\n        const dy = phi / Math.PI;\r\n\r\n        // recenter.\r\n        dx = dx * 0.5 + 0.5;\r\n\r\n        let px = Math.round(dx * inputWidth);\r\n        if (px < 0) {\r\n            px = 0;\r\n        } else if (px >= inputWidth) {\r\n            px = inputWidth - 1;\r\n        }\r\n\r\n        let py = Math.round(dy * inputHeight);\r\n        if (py < 0) {\r\n            py = 0;\r\n        } else if (py >= inputHeight) {\r\n            py = inputHeight - 1;\r\n        }\r\n\r\n        const inputY = inputHeight - py - 1;\r\n        const r = float32Array[inputY * inputWidth * 3 + px * 3 + 0];\r\n        const g = float32Array[inputY * inputWidth * 3 + px * 3 + 1];\r\n        const b = float32Array[inputY * inputWidth * 3 + px * 3 + 2];\r\n\r\n        return {\r\n            r: r,\r\n            g: g,\r\n            b: b,\r\n        };\r\n    }\r\n}\r\n"]}