{"version": 3, "file": "prePassTextureBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Input/prePassTextureBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,oCAAoC,EAAE,MAAM,wCAAwC,CAAC;AAG9F,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AACxG,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAC1D,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAE5D;;GAEG;AACH,MAAM,OAAO,mBAAoB,SAAQ,iBAAiB;IAKtD;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAW,OAAO,CAAC,KAAU;QACzB,OAAO;IACX,CAAC;IAED;;;;OAIG;IACH,YAAmB,IAAY,EAAE,MAAM,GAAG,wBAAwB,CAAC,iBAAiB;QAChF,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAE3B,IAAI,CAAC,cAAc,CACf,UAAU,EACV,qCAAqC,CAAC,MAAM,EAC5C,wBAAwB,CAAC,iBAAiB,EAC1C,IAAI,uCAAuC,CAAC,UAAU,EAAE,IAAI,EAAE,oCAAoC,CAAC,MAAM,EAAE,gBAAgB,EAAE,kBAAkB,CAAC,CACnJ,CAAC;QACF,IAAI,CAAC,cAAc,CACf,OAAO,EACP,qCAAqC,CAAC,MAAM,EAC5C,wBAAwB,CAAC,iBAAiB,EAC1C,IAAI,uCAAuC,CAAC,OAAO,EAAE,IAAI,EAAE,oCAAoC,CAAC,MAAM,EAAE,gBAAgB,EAAE,kBAAkB,CAAC,CAChJ,CAAC;QACF,IAAI,CAAC,cAAc,CACf,QAAQ,EACR,qCAAqC,CAAC,MAAM,EAC5C,wBAAwB,CAAC,iBAAiB,EAC1C,IAAI,uCAAuC,CAAC,QAAQ,EAAE,IAAI,EAAE,oCAAoC,CAAC,MAAM,EAAE,gBAAgB,EAAE,kBAAkB,CAAC,CACjJ,CAAC;IACN,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,MAAmC;QACrD,IAAI,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YAC7B,OAAO,IAAI,CAAC,oBAAoB,CAAC;SACpC;QAED,IAAI,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YAC7B,OAAO,IAAI,CAAC,iBAAiB,CAAC;SACjC;QAED,IAAI,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YAC7B,OAAO,IAAI,CAAC,kBAAkB,CAAC;SAClC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IACD;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,qBAAqB,CAAC;IACjC,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,EAAE;YAClD,OAAO;SACV;QAED,IAAI,CAAC,oBAAoB,GAAG,wBAAwB,CAAC;QACrD,IAAI,CAAC,iBAAiB,GAAG,qBAAqB,CAAC;QAC/C,IAAI,CAAC,kBAAkB,GAAG,sBAAsB,CAAC;QAEjD,sDAAsD;QACtD,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,sBAAsB,GAAG,CAAC,CAAC;QAC1D,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,mBAAmB,GAAG,CAAC,CAAC;QACvD,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAExD,eAAe;QACf,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAChD,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7C,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAE9C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,IAAI,CAAC,MAAc,EAAE,YAA0B;QAClD,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QACtC,MAAM,eAAe,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC;QACtD,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO;SACV;QAED,MAAM,OAAO,GAAG,eAAe,CAAC,SAAS,CAAC;QAC1C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACnB,OAAO;SACV;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC3B,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC;SACrI;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YACxB,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC;SAC/H;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACzB,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC;SACjI;IACL,CAAC;CACJ;AAED,aAAa,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { Effect } from \"../../../../Materials/effect\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { NodeMaterial } from \"../../nodeMaterial\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport { Constants } from \"../../../../Engines/constants\";\r\nimport { ImageSourceBlock } from \"../Dual/imageSourceBlock\";\r\n\r\n/**\r\n * Block used to read from prepass textures\r\n */\r\nexport class PrePassTextureBlock extends NodeMaterialBlock {\r\n    private _positionSamplerName: string;\r\n    private _depthSamplerName: string;\r\n    private _normalSamplerName: string;\r\n\r\n    /**\r\n     * The texture associated with the node is the prepass texture\r\n     */\r\n    public get texture() {\r\n        return null;\r\n    }\r\n\r\n    public set texture(value: any) {\r\n        return;\r\n    }\r\n\r\n    /**\r\n     * Creates a new PrePassTextureBlock\r\n     * @param name defines the block name\r\n     * @param target defines the target of that block (VertexAndFragment by default)\r\n     */\r\n    public constructor(name: string, target = NodeMaterialBlockTargets.VertexAndFragment) {\r\n        super(name, target, false);\r\n\r\n        this.registerOutput(\r\n            \"position\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.VertexAndFragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"position\", this, NodeMaterialConnectionPointDirection.Output, ImageSourceBlock, \"ImageSourceBlock\")\r\n        );\r\n        this.registerOutput(\r\n            \"depth\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.VertexAndFragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"depth\", this, NodeMaterialConnectionPointDirection.Output, ImageSourceBlock, \"ImageSourceBlock\")\r\n        );\r\n        this.registerOutput(\r\n            \"normal\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.VertexAndFragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"normal\", this, NodeMaterialConnectionPointDirection.Output, ImageSourceBlock, \"ImageSourceBlock\")\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Returns the sampler name associated with the node connection point\r\n     * @param output defines the connection point to get the associated sampler name\r\n     * @returns\r\n     */\r\n    public getSamplerName(output: NodeMaterialConnectionPoint): string {\r\n        if (output === this._outputs[0]) {\r\n            return this._positionSamplerName;\r\n        }\r\n\r\n        if (output === this._outputs[1]) {\r\n            return this._depthSamplerName;\r\n        }\r\n\r\n        if (output === this._outputs[2]) {\r\n            return this._normalSamplerName;\r\n        }\r\n\r\n        return \"\";\r\n    }\r\n\r\n    /**\r\n     * Gets the position texture\r\n     */\r\n    public get position(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the depth texture\r\n     */\r\n    public get depth(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the normal texture\r\n     */\r\n    public get normal(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the sampler name associated with this image source\r\n     */\r\n    public get positionSamplerName(): string {\r\n        return this._positionSamplerName;\r\n    }\r\n\r\n    /**\r\n     * Gets the sampler name associated with this image source\r\n     */\r\n    public get normalSamplerName(): string {\r\n        return this._normalSamplerName;\r\n    }\r\n    /**\r\n     * Gets the sampler name associated with this image source\r\n     */\r\n    public get depthSamplerName(): string {\r\n        return this._depthSamplerName;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"PrePassTextureBlock\";\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Vertex) {\r\n            return;\r\n        }\r\n\r\n        this._positionSamplerName = \"prepassPositionSampler\";\r\n        this._depthSamplerName = \"prepassDepthSampler\";\r\n        this._normalSamplerName = \"prepassNormalSampler\";\r\n\r\n        // Unique sampler names for every prepasstexture block\r\n        state.sharedData.variableNames.prepassPositionSampler = 0;\r\n        state.sharedData.variableNames.prepassDepthSampler = 0;\r\n        state.sharedData.variableNames.prepassNormalSampler = 0;\r\n\r\n        // Declarations\r\n        state.sharedData.textureBlocks.push(this);\r\n        state.sharedData.bindableBlocks.push(this);\r\n\r\n        state._emit2DSampler(this._positionSamplerName);\r\n        state._emit2DSampler(this._depthSamplerName);\r\n        state._emit2DSampler(this._normalSamplerName);\r\n\r\n        return this;\r\n    }\r\n\r\n    public bind(effect: Effect, nodeMaterial: NodeMaterial) {\r\n        const scene = nodeMaterial.getScene();\r\n        const prePassRenderer = scene.enablePrePassRenderer();\r\n        if (!prePassRenderer) {\r\n            return;\r\n        }\r\n\r\n        const sceneRT = prePassRenderer.defaultRT;\r\n        if (!sceneRT.textures) {\r\n            return;\r\n        }\r\n\r\n        if (this.position.isConnected) {\r\n            effect.setTexture(this._positionSamplerName, sceneRT.textures[prePassRenderer.getIndex(Constants.PREPASS_POSITION_TEXTURE_TYPE)]);\r\n        }\r\n        if (this.depth.isConnected) {\r\n            effect.setTexture(this._depthSamplerName, sceneRT.textures[prePassRenderer.getIndex(Constants.PREPASS_DEPTH_TEXTURE_TYPE)]);\r\n        }\r\n        if (this.normal.isConnected) {\r\n            effect.setTexture(this._normalSamplerName, sceneRT.textures[prePassRenderer.getIndex(Constants.PREPASS_NORMAL_TEXTURE_TYPE)]);\r\n        }\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PrePassTextureBlock\", PrePassTextureBlock);\r\n"]}