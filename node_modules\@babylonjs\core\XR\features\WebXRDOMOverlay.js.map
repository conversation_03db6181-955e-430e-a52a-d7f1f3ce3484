{"version": 3, "file": "WebXRDOMOverlay.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRDOMOverlay.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAEjF,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAmC9D;;;;GAIG;AACH,MAAM,OAAO,eAAgB,SAAQ,oBAAoB;IA2BrD;;;;OAIG;IACH,YACI,iBAAsC;IACtC;;OAEG;IACa,OAAgC;QAEhD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFT,YAAO,GAAP,OAAO,CAAyB;QApCpD;;WAEG;QACK,oBAAe,GAAkC,IAAI,CAAC;QAE9D;;WAEG;QACK,4BAAuB,GAAiD,IAAI,CAAC;QAErF;;WAEG;QACK,aAAQ,GAAsB,IAAI,CAAC;QA0BvC,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC;QAEzC,gDAAgD;QAChD,KAAK,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;IACvE,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,wBAAwB;QACxB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,KAAK,IAAI,EAAE;YACjH,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC;QAE3E,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,KAAK,IAAI,EAAE;YACvE,IAAI,CAAC,uBAAuB,GAAG,CAAC,EAAE,EAAE,EAAE;gBAClC,EAAE,CAAC,cAAc,EAAE,CAAC;YACxB,CAAC,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;SAClF;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,uBAAuB,EAAE;YACxD,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACrF;IACL,CAAC;IAES,UAAU,CAAC,QAAiB;QAClC,WAAW;IACf,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,yBAAyB;QAClC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;YACpC,KAAK,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;YAClF,OAAO,EAAE,CAAC;SACb;aAAM,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;YACjD,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrE,IAAI,eAAe,KAAK,IAAI,EAAE;gBAC1B,KAAK,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,OAAO,CAAC,OAAO,mCAAmC,CAAC,CAAC;gBAC1F,OAAO,EAAE,CAAC;aACb;YACD,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC;SACnC;aAAM;YACH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;SACxC;QAED,OAAO;YACH,UAAU,EAAE;gBACR,IAAI,EAAE,IAAI,CAAC,QAAQ;aACtB;SACJ,CAAC;IACN,CAAC;;AAvGD;;GAEG;AACoB,oBAAI,GAAG,gBAAgB,CAAC,WAAW,AAA/B,CAAgC;AAC3D;;;;GAIG;AACoB,uBAAO,GAAG,CAAC,AAAJ,CAAK;AAiGvC,qBAAqB;AACrB,oBAAoB,CAAC,eAAe,CAChC,eAAe,CAAC,IAAI,EACpB,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,eAAe,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AAChE,CAAC,EACD,eAAe,CAAC,OAAO,EACvB,KAAK,CACR,CAAC", "sourcesContent": ["import { Tools } from \"../../Misc/tools\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { WebXRFeatureName, WebXRFeaturesManager } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\n\r\n/**\r\n * Options for DOM Overlay feature\r\n */\r\nexport interface IWebXRDomOverlayOptions {\r\n    /**\r\n     * DOM Element or document query selector string for overlay.\r\n     *\r\n     * NOTE: UA may make this element background transparent in XR.\r\n     */\r\n    element: Element | string;\r\n    /**\r\n     * Supress XR Select events on container element (DOM blocks interaction to scene).\r\n     */\r\n    supressXRSelectEvents?: boolean;\r\n}\r\n\r\n/**\r\n * Type of DOM overlay provided by UA.\r\n */\r\ntype WebXRDomOverlayType =\r\n    /**\r\n     * Covers the entire physical screen for a screen-based device, for example handheld AR\r\n     */\r\n    | \"screen\"\r\n    /**\r\n     * Appears as a floating rectangle in space\r\n     */\r\n    | \"floating\"\r\n    /**\r\n     * Follows the user’s head movement consistently, appearing similar to a HUD\r\n     */\r\n    | \"head-locked\";\r\n\r\n/**\r\n * DOM Overlay Feature\r\n *\r\n * @since 5.0.0\r\n */\r\nexport class WebXRDomOverlay extends WebXRAbstractFeature {\r\n    /**\r\n     * Type of overlay - non-null when available\r\n     */\r\n    private _domOverlayType: Nullable<WebXRDomOverlayType> = null;\r\n\r\n    /**\r\n     * Event Listener to supress \"beforexrselect\" events.\r\n     */\r\n    private _beforeXRSelectListener: Nullable<EventListenerOrEventListenerObject> = null;\r\n\r\n    /**\r\n     * Element used for overlay\r\n     */\r\n    private _element: Nullable<Element> = null;\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.DOM_OVERLAY;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * Creates a new instance of the dom-overlay feature\r\n     * @param _xrSessionManager an instance of WebXRSessionManager\r\n     * @param options options to use when constructing this feature\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        /**\r\n         * options to use when constructing this feature\r\n         */\r\n        public readonly options: IWebXRDomOverlayOptions\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"dom-overlay\";\r\n\r\n        // https://immersive-web.github.io/dom-overlays/\r\n        Tools.Warn(\"dom-overlay is an experimental and unstable feature.\");\r\n    }\r\n\r\n    /**\r\n     * attach this feature\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public attach(): boolean {\r\n        if (!super.attach()) {\r\n            return false;\r\n        }\r\n\r\n        // Feature not available\r\n        if (!this._xrSessionManager.session.domOverlayState || this._xrSessionManager.session.domOverlayState.type === null) {\r\n            return false;\r\n        }\r\n\r\n        this._domOverlayType = this._xrSessionManager.session.domOverlayState.type;\r\n\r\n        if (this._element !== null && this.options.supressXRSelectEvents === true) {\r\n            this._beforeXRSelectListener = (ev) => {\r\n                ev.preventDefault();\r\n            };\r\n            this._element.addEventListener(\"beforexrselect\", this._beforeXRSelectListener);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * The type of DOM overlay (null when not supported).  Provided by UA and remains unchanged for duration of session.\r\n     */\r\n    public get domOverlayType(): Nullable<WebXRDomOverlayType> {\r\n        return this._domOverlayType;\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n        if (this._element !== null && this._beforeXRSelectListener) {\r\n            this._element.removeEventListener(\"beforexrselect\", this._beforeXRSelectListener);\r\n        }\r\n    }\r\n\r\n    protected _onXRFrame(_xrFrame: XRFrame): void {\r\n        /* empty */\r\n    }\r\n\r\n    /**\r\n     * Extends the session init object if needed\r\n     * @returns augmentation object for the xr session init object.\r\n     */\r\n    public async getXRSessionInitExtension(): Promise<Partial<XRSessionInit>> {\r\n        if (this.options.element === undefined) {\r\n            Tools.Warn('\"element\" option must be provided to attach xr-dom-overlay feature.');\r\n            return {};\r\n        } else if (typeof this.options.element === \"string\") {\r\n            const selectedElement = document.querySelector(this.options.element);\r\n            if (selectedElement === null) {\r\n                Tools.Warn(`element not found '${this.options.element}' (not requesting xr-dom-overlay)`);\r\n                return {};\r\n            }\r\n            this._element = selectedElement;\r\n        } else {\r\n            this._element = this.options.element;\r\n        }\r\n\r\n        return {\r\n            domOverlay: {\r\n                root: this._element,\r\n            },\r\n        };\r\n    }\r\n}\r\n\r\n//register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRDomOverlay.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRDomOverlay(xrSessionManager, options);\r\n    },\r\n    WebXRDomOverlay.Version,\r\n    false\r\n);\r\n"]}