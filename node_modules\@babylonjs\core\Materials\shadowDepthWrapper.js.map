{"version": 3, "file": "shadowDepthWrapper.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Materials/shadowDepthWrapper.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAIlC,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAsBlD,MAAM,MAAM;IAAZ;QACa,OAAE,GAAG,IAAI,GAAG,EAAkB,CAAC;IAiB5C,CAAC;IAfG,GAAG,CAAC,CAAK,EAAE,CAAK;QACZ,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,KAAK,SAAS,EAAE;YACjB,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SACnB;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,GAAG,CAAC,CAAK,EAAE,CAAK,EAAE,CAAI;QAClB,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,KAAK,SAAS,EAAE;YACjB,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;SACnC;QACD,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,kBAAkB;IAa3B,gDAAgD;IAChD,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,QAAQ,EAAE,UAAU,IAAI,KAAK,CAAC;IAC9C,CAAC;IAED,uDAAuD;IACvD,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,qDAAqD;IACrD,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,QAAQ,EAAE,eAAe,IAAI,KAAK,CAAC;IACnD,CAAC;IAED;;;;;;;;OAQG;IACH,YAAY,YAAsB,EAAE,KAAa,EAAE,OAAqC;QACpF,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,MAAM,GAAG,KAAK,IAAW,WAAW,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,sBAAsB,GAAG,IAAI,MAAM,EAAE,CAAC;QAC3C,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;QAEzB,2HAA2H;QAC3H,sCAAsC;QACtC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,aAAa,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,MAAsD,EAAE,EAAE;YACxI,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;YAEvC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACjC,oFAAoF;gBACpF,IAAI,CAAC,OAAO,CAAC,GAAG,CACZ,IAAI,EACJ,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,IAAU,EAAE,EAAE;oBACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;oBAC9C,KAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE;wBACtE,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC;wBAC1B,IAAI,OAAO,EAAE,OAAO,EAAE,KAAM,IAAqB,EAAE;4BAC/C,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;4BACtC,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;yBAC3C;qBACJ;gBACL,CAAC,CAAC,CACL,CAAC;aACL;YAED,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,EAAE;gBAClE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC;gBACxG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aAClD;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,yBAAyB,CAAC,OAA0B;QACxD,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxE,IAAI,mBAAmB,EAAE;YACrB,6CAA6C;YAC7C,mBAAmB,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;gBACzC,YAAY,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;YACnD,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,oCAAoC;SACvF;IACL,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CAAC,OAA0B,EAAE,eAAgC,EAAE,oBAA4B;QACvG,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC;QAChF,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC;SACf;QACD,IAAI,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;QAC1D,IAAI,CAAC,WAAW,EAAE;YACd,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YACjG,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;SACtF;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;;;;;;OAQG;IACI,iBAAiB,CAAC,OAAgB,EAAE,OAAiB,EAAE,eAAgC,EAAE,YAAqB,EAAE,oBAA4B;QAC/I,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,8DAA8D;YAC9D,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE;gBACjF,OAAO,KAAK,CAAC;aAChB;SACJ;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,oBAAoB,CAAC,EAAE,OAAO,EAAE,IAAI,KAAK,CAAC;IACzG,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,aAAa,CAAC,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACnF,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAErC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACxC,KAAK,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE;YAC5E,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;YAErC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;SAC7C;IACL,CAAC;IAEO,WAAW,CAAC,OAAgB,EAAE,OAAiB,EAAE,eAAgC,EAAE,oBAA4B;QACnH,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,yBAAyB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAErE,IAAI,CAAC,yBAAyB,EAAE;YAC5B,OAAO,IAAI,CAAC;SACf;QAED,MAAM,CAAC,UAAU,EAAE,gBAAgB,CAAC,GAAG,yBAAyB,CAAC;QAEjE,IAAI,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QACvE,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,eAAe,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;YAChD,eAAe,CAAC,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC,gBAAgB,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC;YAErF,MAAM,GAAG;gBACL,WAAW,EAAE,EAAE;gBACf,eAAe;gBACf,YAAY,EAAE,EAAE;gBAChB,KAAK,EAAE,UAAU,EAAE;aACtB,CAAC;YACF,MAAM,CAAC,WAAW,CAAC,oBAAoB,CAAC,GAAG,eAAe,CAAC;YAC3D,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;SACrE;QAED,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,MAAM,CAAC,eAAe,CAAC,MAAM,EAAE;YAC/B,IAAI,IAAI,KAAK,MAAM,CAAC,YAAY,EAAE;gBAC9B,qGAAqG;gBACrG,OAAO,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC;aACxC;SACJ;QAED,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;QAE3B,MAAM,QAAQ,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC,KAAK,EAAE,CAAC;QAEtD,qEAAqE;QACrE,IAAI,UAAU,GAAG,UAAU,CAAC,+BAA+B,EACvD,YAAY,GAAG,UAAU,CAAC,iCAAiC,CAAC;QAEhE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,kCAAkC;YAClC,MAAM,oBAAoB,GAClB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB;gBAC5C,CAAC,CAAC,uCAAuC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG;gBACrF,CAAC,CAAC,qCAAqC,EAC/C,gBAAgB,GACZ,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB;gBAC5C,CAAC,CAAC,mCAAmC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG;gBACjF,CAAC,CAAC,iCAAiC,EAC3C,6BAA6B,GACzB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB;gBAC5C,CAAC,CAAC,oDAAoD,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG;gBAClG,CAAC,CAAC,kDAAkD,EAC5D,iBAAiB,GAAG,6BAA6B,EACjD,qBAAqB,GAAG,2CAA2C,CAAC;YAExE,cAAc;YACd,IAAI,UAAU,CAAC,cAAc,KAAK,cAAc,CAAC,IAAI,EAAE;gBACnD,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,eAAe,EAAE,KAAK,qBAAqB,aAAa,CAAC,CAAC;aAC7F;iBAAM;gBACH,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,qBAAqB,WAAW,CAAC,CAAC;aACtF;YACD,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,uEAAuE,EAAE,oBAAoB,CAAC,CAAC;YAE/H,IAAI,UAAU,CAAC,OAAO,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAC,EAAE;gBACzD,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,6BAA6B,EAAE,gBAAgB,CAAC,CAAC;aACpF;iBAAM;gBACH,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,gBAAgB,GAAG,KAAK,CAAC,CAAC;aACvE;YACD,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,mDAAmD,EAAE,EAAE,CAAC,CAAC;YAEzF,gBAAgB;YAChB,MAAM,mCAAmC,GACrC,YAAY,CAAC,OAAO,CAAC,2CAA2C,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,oCAAoC,CAAC,IAAI,CAAC,CAAC;YAC9I,MAAM,sBAAsB,GAAG,YAAY,CAAC,OAAO,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC,CAAC;YAE3F,IAAI,yBAAyB,GAAG,EAAE,CAAC;YAEnC,IAAI,CAAC,mCAAmC,EAAE;gBACtC,yBAAyB,GAAG,6BAA6B,GAAG,IAAI,CAAC;aACpE;iBAAM;gBACH,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,+EAA+E,EAAE,6BAA6B,CAAC,CAAC;aACvJ;YAED,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,oBAAoB,CAAC,mCAAmC,CAAC,GAAG,aAAa,CAAC,CAAC;YAEvI,IAAI,sBAAsB,EAAE;gBACxB,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,+BAA+B,EAAE,iBAAiB,CAAC,CAAC;aAC3F;iBAAM;gBACH,yBAAyB,IAAI,iBAAiB,GAAG,IAAI,CAAC;aACzD;YACD,IAAI,yBAAyB,EAAE;gBAC3B,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,QAAQ,EAAE,yBAAyB,GAAG,GAAG,CAAC,CAAC;aAClF;YAED,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,eAAe,EAAE,aAAa,EAAE,yBAAyB,CAAC,CAAC;SAC9F;QAED,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC,YAAY,CAC/C;YACI,YAAY,EAAE,UAAU;YACxB,cAAc,EAAE,YAAY;YAC5B,WAAW,EAAE,MAAM,CAAC,KAAK;YACzB,aAAa,EAAE,MAAM,CAAC,KAAK;SAC9B,EACuB;YACpB,UAAU,EAAE,UAAU,CAAC,kBAAkB,EAAE;YAC3C,aAAa,EAAE,QAAQ;YACvB,mBAAmB,EAAE,UAAU,CAAC,sBAAsB,EAAE;YACxD,QAAQ,EAAE,UAAU,CAAC,WAAW,EAAE;YAClC,OAAO,EAAE,IAAI,GAAG,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC;YACzG,eAAe,EAAE,UAAU,CAAC,kBAAkB,EAAE;YAChD,cAAc,EAAE,UAAU,CAAC,cAAc;SAC5C,EACD,MAAM,CACT,CAAC;QAEF,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;YACnD,IAAI,EAAE,KAAK,oBAAoB,EAAE;gBAC7B,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;aACpG;SACJ;QACD,OAAO,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC;IACzC,CAAC;CACJ", "sourcesContent": ["import type { Observer } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { Material } from \"./material\";\r\nimport type { IEffectCreationOptions } from \"./effect\";\r\nimport { Effect } from \"./effect\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Node } from \"../node\";\r\nimport type { ShadowGenerator } from \"../Lights/Shadows/shadowGenerator\";\r\nimport { RandomGUID } from \"../Misc/guid\";\r\nimport { DrawWrapper } from \"./drawWrapper\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { ShaderLanguage } from \"./shaderLanguage\";\r\n\r\n/**\r\n * Options to be used when creating a shadow depth material\r\n */\r\nexport interface IIOptionShadowDepthMaterial {\r\n    /** Variables in the vertex shader code that need to have their names remapped.\r\n     * The format is: [\"var_name\", \"var_remapped_name\", \"var_name\", \"var_remapped_name\", ...]\r\n     * \"var_name\" should be either: worldPos or vNormalW\r\n     * So, if the variable holding the world position in your vertex shader is not named worldPos, you must tell the system\r\n     * the name to use instead by using: [\"worldPos\", \"myWorldPosVar\"] assuming the variable is named myWorldPosVar in your code.\r\n     * If the normal must also be remapped: [\"worldPos\", \"myWorldPosVar\", \"vNormalW\", \"myWorldNormal\"]\r\n     */\r\n    remappedVariables?: string[];\r\n\r\n    /** Set standalone to true if the base material wrapped by ShadowDepthMaterial is not used for a regular object but for depth shadow generation only */\r\n    standalone?: boolean;\r\n\r\n    /** Set doNotInjectCode if the specific shadow map generation code is already implemented by the material. That will prevent this code to be injected twice by ShadowDepthWrapper */\r\n    doNotInjectCode?: boolean;\r\n}\r\n\r\nclass MapMap<Ka, Kb, V> {\r\n    readonly mm = new Map<Ka, Map<Kb, V>>();\r\n\r\n    get(a: Ka, b: Kb): V | undefined {\r\n        const m = this.mm.get(a);\r\n        if (m !== undefined) {\r\n            return m.get(b);\r\n        }\r\n        return undefined;\r\n    }\r\n\r\n    set(a: Ka, b: Kb, v: V): void {\r\n        let m = this.mm.get(a);\r\n        if (m === undefined) {\r\n            this.mm.set(a, (m = new Map()));\r\n        }\r\n        m.set(b, v);\r\n    }\r\n}\r\n\r\n/**\r\n * Class that can be used to wrap a base material to generate accurate shadows when using custom vertex/fragment code in the base material\r\n */\r\nexport class ShadowDepthWrapper {\r\n    private _scene: Scene;\r\n    private _options?: IIOptionShadowDepthMaterial;\r\n    private _baseMaterial: Material;\r\n    private _onEffectCreatedObserver: Nullable<Observer<{ effect: Effect; subMesh: Nullable<SubMesh> }>>;\r\n    private _subMeshToEffect: Map<Nullable<SubMesh>, [Effect, number]>;\r\n    private _subMeshToDepthWrapper: MapMap<\r\n        Nullable<SubMesh>,\r\n        ShadowGenerator,\r\n        { drawWrapper: Array<Nullable<DrawWrapper>>; mainDrawWrapper: DrawWrapper; depthDefines: string; token: string }\r\n    >; // key is (subMesh + shadowGenerator)\r\n    private _meshes: Map<AbstractMesh, Nullable<Observer<Node>>>;\r\n\r\n    /** Gets the standalone status of the wrapper */\r\n    public get standalone(): boolean {\r\n        return this._options?.standalone ?? false;\r\n    }\r\n\r\n    /** Gets the base material the wrapper is built upon */\r\n    public get baseMaterial(): Material {\r\n        return this._baseMaterial;\r\n    }\r\n\r\n    /** Gets the doNotInjectCode status of the wrapper */\r\n    public get doNotInjectCode(): boolean {\r\n        return this._options?.doNotInjectCode ?? false;\r\n    }\r\n\r\n    /**\r\n     * Instantiate a new shadow depth wrapper.\r\n     * It works by injecting some specific code in the vertex/fragment shaders of the base material and is used by a shadow generator to\r\n     * generate the shadow depth map. For more information, please refer to the documentation:\r\n     * https://doc.babylonjs.com/features/featuresDeepDive/lights/shadows\r\n     * @param baseMaterial Material to wrap\r\n     * @param scene Define the scene the material belongs to\r\n     * @param options Options used to create the wrapper\r\n     */\r\n    constructor(baseMaterial: Material, scene?: Scene, options?: IIOptionShadowDepthMaterial) {\r\n        this._baseMaterial = baseMaterial;\r\n        this._scene = scene ?? <Scene>EngineStore.LastCreatedScene;\r\n        this._options = options;\r\n\r\n        this._subMeshToEffect = new Map();\r\n        this._subMeshToDepthWrapper = new MapMap();\r\n        this._meshes = new Map();\r\n\r\n        // Register for onEffectCreated to store the effect of the base material when it is (re)generated. This effect will be used\r\n        // to create the depth effect later on\r\n        this._onEffectCreatedObserver = this._baseMaterial.onEffectCreatedObservable.add((params: { effect: Effect; subMesh: Nullable<SubMesh> }) => {\r\n            const mesh = params.subMesh?.getMesh();\r\n\r\n            if (mesh && !this._meshes.has(mesh)) {\r\n                // Register for mesh onDispose to clean up our internal maps when a mesh is disposed\r\n                this._meshes.set(\r\n                    mesh,\r\n                    mesh.onDisposeObservable.add((mesh: Node) => {\r\n                        const iterator = this._subMeshToEffect.keys();\r\n                        for (let key = iterator.next(); key.done !== true; key = iterator.next()) {\r\n                            const subMesh = key.value;\r\n                            if (subMesh?.getMesh() === (mesh as AbstractMesh)) {\r\n                                this._subMeshToEffect.delete(subMesh);\r\n                                this._deleteDepthWrapperEffect(subMesh);\r\n                            }\r\n                        }\r\n                    })\r\n                );\r\n            }\r\n\r\n            if (this._subMeshToEffect.get(params.subMesh)?.[0] !== params.effect) {\r\n                this._subMeshToEffect.set(params.subMesh, [params.effect, this._scene.getEngine().currentRenderPassId]);\r\n                this._deleteDepthWrapperEffect(params.subMesh);\r\n            }\r\n        });\r\n    }\r\n\r\n    private _deleteDepthWrapperEffect(subMesh: Nullable<SubMesh>): void {\r\n        const depthWrapperEntries = this._subMeshToDepthWrapper.mm.get(subMesh);\r\n        if (depthWrapperEntries) {\r\n            // find and release the previous depth effect\r\n            depthWrapperEntries.forEach((depthWrapper) => {\r\n                depthWrapper.mainDrawWrapper.effect?.dispose();\r\n            });\r\n            this._subMeshToDepthWrapper.mm.delete(subMesh); // trigger a depth effect recreation\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the effect to use to generate the depth map\r\n     * @param subMesh subMesh to get the effect for\r\n     * @param shadowGenerator shadow generator to get the effect for\r\n     * @param passIdForDrawWrapper Id of the pass for which the effect from the draw wrapper must be retrieved from\r\n     * @returns the effect to use to generate the depth map for the subMesh + shadow generator specified\r\n     */\r\n    public getEffect(subMesh: Nullable<SubMesh>, shadowGenerator: ShadowGenerator, passIdForDrawWrapper: number): Nullable<DrawWrapper> {\r\n        const entry = this._subMeshToDepthWrapper.mm.get(subMesh)?.get(shadowGenerator);\r\n        if (!entry) {\r\n            return null;\r\n        }\r\n        let drawWrapper = entry.drawWrapper[passIdForDrawWrapper];\r\n        if (!drawWrapper) {\r\n            drawWrapper = entry.drawWrapper[passIdForDrawWrapper] = new DrawWrapper(this._scene.getEngine());\r\n            drawWrapper.setEffect(entry.mainDrawWrapper.effect, entry.mainDrawWrapper.defines);\r\n        }\r\n\r\n        return drawWrapper;\r\n    }\r\n\r\n    /**\r\n     * Specifies that the submesh is ready to be used for depth rendering\r\n     * @param subMesh submesh to check\r\n     * @param defines the list of defines to take into account when checking the effect\r\n     * @param shadowGenerator combined with subMesh, it defines the effect to check\r\n     * @param useInstances specifies that instances should be used\r\n     * @param passIdForDrawWrapper Id of the pass for which the draw wrapper should be created\r\n     * @returns a boolean indicating that the submesh is ready or not\r\n     */\r\n    public isReadyForSubMesh(subMesh: SubMesh, defines: string[], shadowGenerator: ShadowGenerator, useInstances: boolean, passIdForDrawWrapper: number): boolean {\r\n        if (this.standalone) {\r\n            // will ensure the effect is (re)created for the base material\r\n            if (!this._baseMaterial.isReadyForSubMesh(subMesh.getMesh(), subMesh, useInstances)) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        return this._makeEffect(subMesh, defines, shadowGenerator, passIdForDrawWrapper)?.isReady() ?? false;\r\n    }\r\n\r\n    /**\r\n     * Disposes the resources\r\n     */\r\n    public dispose(): void {\r\n        this._baseMaterial.onEffectCreatedObservable.remove(this._onEffectCreatedObserver);\r\n        this._onEffectCreatedObserver = null;\r\n\r\n        const iterator = this._meshes.entries();\r\n        for (let entry = iterator.next(); entry.done !== true; entry = iterator.next()) {\r\n            const [mesh, observer] = entry.value;\r\n\r\n            mesh.onDisposeObservable.remove(observer);\r\n        }\r\n    }\r\n\r\n    private _makeEffect(subMesh: SubMesh, defines: string[], shadowGenerator: ShadowGenerator, passIdForDrawWrapper: number): Nullable<Effect> {\r\n        const engine = this._scene.getEngine();\r\n        const origEffectAndRenderPassId = this._subMeshToEffect.get(subMesh);\r\n\r\n        if (!origEffectAndRenderPassId) {\r\n            return null;\r\n        }\r\n\r\n        const [origEffect, origRenderPassId] = origEffectAndRenderPassId;\r\n\r\n        let params = this._subMeshToDepthWrapper.get(subMesh, shadowGenerator);\r\n        if (!params) {\r\n            const mainDrawWrapper = new DrawWrapper(engine);\r\n            mainDrawWrapper.defines = subMesh._getDrawWrapper(origRenderPassId)?.defines ?? null;\r\n\r\n            params = {\r\n                drawWrapper: [],\r\n                mainDrawWrapper,\r\n                depthDefines: \"\",\r\n                token: RandomGUID(),\r\n            };\r\n            params.drawWrapper[passIdForDrawWrapper] = mainDrawWrapper;\r\n            this._subMeshToDepthWrapper.set(subMesh, shadowGenerator, params);\r\n        }\r\n\r\n        const join = defines.join(\"\\n\");\r\n\r\n        if (params.mainDrawWrapper.effect) {\r\n            if (join === params.depthDefines) {\r\n                // we already created the depth effect and it is still up to date for this submesh + shadow generator\r\n                return params.mainDrawWrapper.effect;\r\n            }\r\n        }\r\n\r\n        params.depthDefines = join;\r\n\r\n        const uniforms = origEffect.getUniformNames().slice();\r\n\r\n        // the depth effect is either out of date or has not been created yet\r\n        let vertexCode = origEffect.vertexSourceCodeBeforeMigration,\r\n            fragmentCode = origEffect.fragmentSourceCodeBeforeMigration;\r\n\r\n        if (!this.doNotInjectCode) {\r\n            // Declare the shadow map includes\r\n            const vertexNormalBiasCode =\r\n                    this._options && this._options.remappedVariables\r\n                        ? `#include<shadowMapVertexNormalBias>(${this._options.remappedVariables.join(\",\")})`\r\n                        : `#include<shadowMapVertexNormalBias>`,\r\n                vertexMetricCode =\r\n                    this._options && this._options.remappedVariables\r\n                        ? `#include<shadowMapVertexMetric>(${this._options.remappedVariables.join(\",\")})`\r\n                        : `#include<shadowMapVertexMetric>`,\r\n                fragmentSoftTransparentShadow =\r\n                    this._options && this._options.remappedVariables\r\n                        ? `#include<shadowMapFragmentSoftTransparentShadow>(${this._options.remappedVariables.join(\",\")})`\r\n                        : `#include<shadowMapFragmentSoftTransparentShadow>`,\r\n                fragmentBlockCode = `#include<shadowMapFragment>`,\r\n                vertexExtraDeclartion = `#include<shadowMapVertexExtraDeclaration>`;\r\n\r\n            // vertex code\r\n            if (origEffect.shaderLanguage === ShaderLanguage.GLSL) {\r\n                vertexCode = vertexCode.replace(/void\\s+?main/g, `\\n${vertexExtraDeclartion}\\nvoid main`);\r\n            } else {\r\n                vertexCode = vertexCode.replace(/@vertex/g, `\\n${vertexExtraDeclartion}\\n@vertex`);\r\n            }\r\n            vertexCode = vertexCode.replace(/#define SHADOWDEPTH_NORMALBIAS|#define CUSTOM_VERTEX_UPDATE_WORLDPOS/g, vertexNormalBiasCode);\r\n\r\n            if (vertexCode.indexOf(\"#define SHADOWDEPTH_METRIC\") !== -1) {\r\n                vertexCode = vertexCode.replace(/#define SHADOWDEPTH_METRIC/g, vertexMetricCode);\r\n            } else {\r\n                vertexCode = vertexCode.replace(/}\\s*$/g, vertexMetricCode + \"\\n}\");\r\n            }\r\n            vertexCode = vertexCode.replace(/#define SHADER_NAME.*?\\n|out vec4 glFragColor;\\n/g, \"\");\r\n\r\n            // fragment code\r\n            const hasLocationForSoftTransparentShadow =\r\n                fragmentCode.indexOf(\"#define SHADOWDEPTH_SOFTTRANSPARENTSHADOW\") >= 0 || fragmentCode.indexOf(\"#define CUSTOM_FRAGMENT_BEFORE_FOG\") >= 0;\r\n            const hasLocationForFragment = fragmentCode.indexOf(\"#define SHADOWDEPTH_FRAGMENT\") !== -1;\r\n\r\n            let fragmentCodeToInjectAtEnd = \"\";\r\n\r\n            if (!hasLocationForSoftTransparentShadow) {\r\n                fragmentCodeToInjectAtEnd = fragmentSoftTransparentShadow + \"\\n\";\r\n            } else {\r\n                fragmentCode = fragmentCode.replace(/#define SHADOWDEPTH_SOFTTRANSPARENTSHADOW|#define CUSTOM_FRAGMENT_BEFORE_FOG/g, fragmentSoftTransparentShadow);\r\n            }\r\n\r\n            fragmentCode = fragmentCode.replace(/void\\s+?main/g, Effect.IncludesShadersStore[\"shadowMapFragmentExtraDeclaration\"] + \"\\nvoid main\");\r\n\r\n            if (hasLocationForFragment) {\r\n                fragmentCode = fragmentCode.replace(/#define SHADOWDEPTH_FRAGMENT/g, fragmentBlockCode);\r\n            } else {\r\n                fragmentCodeToInjectAtEnd += fragmentBlockCode + \"\\n\";\r\n            }\r\n            if (fragmentCodeToInjectAtEnd) {\r\n                fragmentCode = fragmentCode.replace(/}\\s*$/g, fragmentCodeToInjectAtEnd + \"}\");\r\n            }\r\n\r\n            uniforms.push(\"biasAndScaleSM\", \"depthValuesSM\", \"lightDataSM\", \"softTransparentShadowSM\");\r\n        }\r\n\r\n        params.mainDrawWrapper.effect = engine.createEffect(\r\n            {\r\n                vertexSource: vertexCode,\r\n                fragmentSource: fragmentCode,\r\n                vertexToken: params.token,\r\n                fragmentToken: params.token,\r\n            },\r\n            <IEffectCreationOptions>{\r\n                attributes: origEffect.getAttributesNames(),\r\n                uniformsNames: uniforms,\r\n                uniformBuffersNames: origEffect.getUniformBuffersNames(),\r\n                samplers: origEffect.getSamplers(),\r\n                defines: join + \"\\n\" + origEffect.defines.replace(\"#define SHADOWS\", \"\").replace(/#define SHADOW\\d/g, \"\"),\r\n                indexParameters: origEffect.getIndexParameters(),\r\n                shaderLanguage: origEffect.shaderLanguage,\r\n            },\r\n            engine\r\n        );\r\n\r\n        for (let id = 0; id < params.drawWrapper.length; ++id) {\r\n            if (id !== passIdForDrawWrapper) {\r\n                params.drawWrapper[id]?.setEffect(params.mainDrawWrapper.effect, params.mainDrawWrapper.defines);\r\n            }\r\n        }\r\n        return params.mainDrawWrapper.effect;\r\n    }\r\n}\r\n"]}