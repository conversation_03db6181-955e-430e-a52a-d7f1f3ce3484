{"version": 3, "file": "normalBlendBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/normalBlendBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAGvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,iBAAiB;IACnD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAE9C,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QACnF,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QACnF,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAE7E,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,CACtD,qCAAqC,CAAC,MAAM;YACxC,qCAAqC,CAAC,MAAM;YAC5C,qCAAqC,CAAC,OAAO;YAC7C,qCAAqC,CAAC,OAAO,CACpD,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,CACtD,qCAAqC,CAAC,MAAM;YACxC,qCAAqC,CAAC,MAAM;YAC5C,qCAAqC,CAAC,OAAO;YAC7C,qCAAqC,CAAC,OAAO,CACpD,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,KAAK,GAAG,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,KAAK,GAAG,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAElD,KAAK,CAAC,iBAAiB,IAAI,SAAS,KAAK,gBAAgB,MAAM,CAAC,sBAAsB,QAAQ,CAAC;QAC/F,KAAK,CAAC,iBAAiB,IAAI,SAAS,KAAK,gBAAgB,MAAM,CAAC,sBAAsB,QAAQ,CAAC;QAC/F,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;QACtE,KAAK,CAAC,iBAAiB,IAAI,GAAG,MAAM,CAAC,sBAAsB,eAAe,KAAK,OAAO,MAAM,CAAC,sBAAsB,QAAQ,MAAM,CAAC,sBAAsB,cAAc,KAAK,oBAAoB,MAAM,CAAC,sBAAsB,gBAAgB,MAAM,CAAC,sBAAsB,eAAe,CAAC;QACzR,KAAK,CAAC,iBAAiB,IAAI,GAAG,MAAM,CAAC,sBAAsB,eAAe,KAAK,OAAO,MAAM,CAAC,sBAAsB,QAAQ,MAAM,CAAC,sBAAsB,cAAc,KAAK,oBAAoB,MAAM,CAAC,sBAAsB,gBAAgB,MAAM,CAAC,sBAAsB,eAAe,CAAC;QACzR,KAAK,CAAC,iBAAiB,IAAI,GAAG,MAAM,CAAC,sBAAsB,QAAQ,MAAM,CAAC,sBAAsB,QAAQ,MAAM,CAAC,sBAAsB,OAAO,CAAC;QAE7I,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\n/**\r\n * Block used to blend normals\r\n */\r\nexport class NormalBlendBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new NormalBlendBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerInput(\"normalMap0\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerInput(\"normalMap1\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n\r\n        this._inputs[0].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 |\r\n                NodeMaterialBlockConnectionPointTypes.Color4 |\r\n                NodeMaterialBlockConnectionPointTypes.Vector3 |\r\n                NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n\r\n        this._inputs[1].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 |\r\n                NodeMaterialBlockConnectionPointTypes.Color4 |\r\n                NodeMaterialBlockConnectionPointTypes.Vector3 |\r\n                NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"NormalBlendBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the first input component\r\n     */\r\n    public get normalMap0(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the second input component\r\n     */\r\n    public get normalMap1(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const output = this._outputs[0];\r\n        const input0 = this._inputs[0];\r\n        const input1 = this._inputs[1];\r\n        const stepR = state._getFreeVariableName(\"stepR\");\r\n        const stepG = state._getFreeVariableName(\"stepG\");\r\n\r\n        state.compilationString += `float ${stepR} = step(0.5, ${input0.associatedVariableName}.r);\\n`;\r\n        state.compilationString += `float ${stepG} = step(0.5, ${input0.associatedVariableName}.g);\\n`;\r\n        state.compilationString += this._declareOutput(output, state) + `;\\n`;\r\n        state.compilationString += `${output.associatedVariableName}.r = (1.0 - ${stepR}) * ${input0.associatedVariableName}.r * ${input1.associatedVariableName}.r * 2.0 + ${stepR} * (1.0 - (1.0 - ${input0.associatedVariableName}.r) * (1.0 - ${input1.associatedVariableName}.r) * 2.0);\\n`;\r\n        state.compilationString += `${output.associatedVariableName}.g = (1.0 - ${stepG}) * ${input0.associatedVariableName}.g * ${input1.associatedVariableName}.g * 2.0 + ${stepG} * (1.0 - (1.0 - ${input0.associatedVariableName}.g) * (1.0 - ${input1.associatedVariableName}.g) * 2.0);\\n`;\r\n        state.compilationString += `${output.associatedVariableName}.b = ${input0.associatedVariableName}.b * ${input1.associatedVariableName}.b;\\n`;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.NormalBlendBlock\", NormalBlendBlock);\r\n"]}