{"version": 3, "file": "shadowsVertex.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Shaders/ShadersInclude/shadowsVertex.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAExD,MAAM,IAAI,GAAG,eAAe,CAAC;AAC7B,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;CAkBd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAChD,gBAAgB;AAChB,MAAM,CAAC,MAAM,aAAa,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore\";\n\nconst name = \"shadowsVertex\";\nconst shader = `#ifdef SHADOWS\n#if defined(SHADOWCSM{X})\nvPositionFromCamera{X}=view*worldPos;for (int i=0; i<SHADOWCSMNUM_CASCADES{X}; i++) {vPositionFromLight{X}[i]=lightMatrix{X}[i]*worldPos;\n#ifdef USE_REVERSE_DEPTHBUFFER\nvDepthMetric{X}[i]=(-vPositionFromLight{X}[i].z+light{X}.depthValues.x)/light{X}.depthValues.y;\n#else\nvDepthMetric{X}[i]=(vPositionFromLight{X}[i].z+light{X}.depthValues.x)/light{X}.depthValues.y;\n#endif\n}\n#elif defined(SHADOW{X}) && !defined(SHADOWCUBE{X})\nvPositionFromLight{X}=lightMatrix{X}*worldPos;\n#ifdef USE_REVERSE_DEPTHBUFFER\nvDepthMetric{X}=(-vPositionFromLight{X}.z+light{X}.depthValues.x)/light{X}.depthValues.y;\n#else\nvDepthMetric{X}=(vPositionFromLight{X}.z+light{X}.depthValues.x)/light{X}.depthValues.y;\n#endif\n#endif\n#endif\n`;\n// Sideeffect\nShaderStore.IncludesShadersStore[name] = shader;\n/** @internal */\nexport const shadowsVertex = { name, shader };\n"]}