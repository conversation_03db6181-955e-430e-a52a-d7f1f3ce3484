{"version": 3, "file": "standardRenderingPipeline.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/PostProcesses/RenderPipeline/Pipelines/standardRenderingPipeline.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AACzE,OAAO,EAAE,mBAAmB,EAAE,MAAM,wCAAwC,CAAC;AAE7E,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AAC/E,OAAO,EAAE,MAAM,EAAE,MAAM,4BAA4B,CAAC;AAGpD,OAAO,EAAE,OAAO,EAAE,MAAM,qCAAqC,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,oCAAoC,CAAC;AACjE,OAAO,EAAE,yBAAyB,EAAE,MAAM,iEAAiE,CAAC;AAC5G,OAAO,EAAE,uBAAuB,EAAE,MAAM,+DAA+D,CAAC;AACxG,OAAO,EAAE,eAAe,EAAE,MAAM,wCAAwC,CAAC;AACzE,OAAO,EAAE,eAAe,EAAE,MAAM,wCAAwC,CAAC;AAMzE,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,qBAAqB,EAAE,MAAM,6BAA6B,CAAC;AACpE,OAAO,EAAE,gCAAgC,EAAE,MAAM,wCAAwC,CAAC;AAI1F,OAAO,sFAAsF,CAAC;AAE9F,OAAO,oCAAoC,CAAC;AAC5C;;;;GAIG;AACH,MAAM,OAAO,yBAA0B,SAAQ,yBAAyB;IAyHpE;;OAEG;IAEH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IACD;;OAEG;IACH,IAAW,QAAQ,CAAC,KAAa;QAC7B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAClC,CAAC;IA6CD;;OAEG;IAEH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IACD;;OAEG;IACH,IAAW,eAAe,CAAC,KAAc;QACrC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,MAAM,OAAO,GAAG,CAAC,aAAa,CAAC,CAAC;YAChC,IAAI,KAAK,EAAE;gBACP,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;aACzC;YACD,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACxD;IACL,CAAC;IAyDD;;OAEG;IAEH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IACD;;OAEG;IACH,IAAW,cAAc,CAAC,QAAgB;QACtC,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAEhC,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5D,IAAI,CAAC,qBAA+C,CAAC,cAAc,GAAG,QAAQ,CAAC;SACnF;IACL,CAAC;IAED;;OAEG;IAEH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IACD;;OAEG;IACH,IAAW,qBAAqB,CAAC,KAAc;QAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,wBAAwB,KAAK,KAAK,CAAC;QAC9D,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;QAEtC,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;IACL,CAAC;IA0CD;;;OAGG;IAEH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,YAAY,CAAC,OAAgB;QACpC,IAAI,IAAI,CAAC,aAAa,KAAK,OAAO,EAAE;YAChC,OAAO;SACV;QAED,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;QAC7B,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IAEH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED,IAAW,mBAAmB,CAAC,OAAgB;QAC3C,IAAI,IAAI,CAAC,oBAAoB,KAAK,OAAO,EAAE;YACvC,OAAO;SACV;QAED,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QACpC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IAEH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED,IAAW,gBAAgB,CAAC,OAAgB;QACxC,IAAI,IAAI,CAAC,iBAAiB,KAAK,OAAO,EAAE;YACpC,OAAO;SACV;QAED,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC;QACjC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IAEH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,OAAgB;QAClC,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,EAAE;YAC9B,OAAO;SACV;QAED,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IAGH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,gEAAgE;IAChE,IAAW,UAAU,CAAC,OAAO;QACzB,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO,EAAE;YAC9B,OAAO;SACV;QAED,IAAI,OAAO,EAAE;YACT,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,4BAA4B,EAAE,CAAC;YAC5D,IAAI,CAAC,QAAQ,EAAE;gBACX,MAAM,CAAC,IAAI,CAAC,oGAAoG,CAAC,CAAC;gBAClH,OAAO;aACV;SACJ;QAED,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IAEH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED,IAAW,iBAAiB,CAAC,OAAgB;QACzC,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,EAAE;YACrC,OAAO;SACV;QAED,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IAEH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,OAAgB;QACnC,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAC5B,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IAEH,IAAW,6BAA6B;QACpC,OAAO,IAAI,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IAED,IAAW,6BAA6B,CAAC,OAAgB;QACrD,IAAI,IAAI,CAAC,8BAA8B,KAAK,OAAO,EAAE;YACjD,OAAO;SACV;QAED,IAAI,CAAC,8BAA8B,GAAG,OAAO,CAAC;QAC9C,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IAEH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IAED,IAAW,yBAAyB,CAAC,KAAa;QAC9C,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,gCAAgC,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SACrG;QAED,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED;;;OAGG;IAEH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED,IAAW,iBAAiB,CAAC,OAAe;QACxC,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,IAAI,CAAC,wBAAwB,EAAE;gBAC9B,IAAI,CAAC,qBAA+C,CAAC,iBAAiB,GAAG,OAAO,CAAC;aACrF;iBAAM;gBACH,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,kDAAkD,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aACpH;SACJ;QAED,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;IACtC,CAAC;IAED;;OAEG;IAEH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,WAAmB;QAClC,IAAI,IAAI,CAAC,QAAQ,KAAK,WAAW,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;QAC5B,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;;;;OAQG;IACH,YAAY,IAAY,EAAE,KAAY,EAAE,KAAa,EAAE,sBAA6C,IAAI,EAAE,OAAkB;QACxH,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;QA5hBnC;;WAEG;QACI,4BAAuB,GAA0B,IAAI,CAAC;QAC7D;;WAEG;QACI,0BAAqB,GAA0B,IAAI,CAAC;QAC3D;;WAEG;QACI,uBAAkB,GAAkB,EAAE,CAAC;QAC9C;;WAEG;QACI,uBAAkB,GAAkB,EAAE,CAAC;QAC9C;;WAEG;QACI,4BAAuB,GAA0B,IAAI,CAAC;QAE7D;;WAEG;QACI,+BAA0B,GAA0B,IAAI,CAAC;QAChE;;WAEG;QACI,sCAAiC,GAA8B,IAAI,CAAC;QAC3E;;WAEG;QACI,sCAAiC,GAA8B,IAAI,CAAC;QAC3E;;WAEG;QACI,mCAA8B,GAA0B,IAAI,CAAC;QACpE;;WAEG;QACI,oCAA+B,GAA0B,IAAI,CAAC;QAErE;;WAEG;QACI,yBAAoB,GAA0B,IAAI,CAAC;QAC1D;;;;WAIG;QACI,qCAAgC,GAAkB,EAAE,CAAC;QAC5D;;WAEG;QACI,mBAAc,GAA0B,IAAI,CAAC;QACpD;;WAEG;QACI,iCAA4B,GAA0B,IAAI,CAAC;QAClE;;WAEG;QACI,8BAAyB,GAA0B,IAAI,CAAC;QAC/D;;WAEG;QACI,wBAAmB,GAA0B,IAAI,CAAC;QACzD;;WAEG;QACI,yBAAoB,GAA0B,IAAI,CAAC;QAC1D;;WAEG;QACI,gCAA2B,GAA0B,IAAI,CAAC;QACjE;;WAEG;QACI,0BAAqB,GAA0B,IAAI,CAAC;QAC3D;;WAEG;QACI,4BAAuB,GAA0B,IAAI,CAAC;QAC7D;;WAEG;QACI,oBAAe,GAA8B,IAAI,CAAC;QACzD;;WAEG;QACI,qCAAgC,GAA+C,IAAI,CAAC;QAE3F,SAAS;QAET;;WAEG;QAEI,oBAAe,GAAW,GAAG,CAAC;QAErC;;WAEG;QAEI,cAAS,GAAW,KAAK,CAAC;QACjC;;WAEG;QAEI,mBAAc,GAAY,KAAK,CAAC;QAiBvC;;WAEG;QAEI,gBAAW,GAAsB,IAAI,CAAC;QAE7C;;WAEG;QAEI,+BAA0B,GAAW,GAAG,CAAC;QAChD;;WAEG;QAEI,yBAAoB,GAAW,GAAG,CAAC;QAC1C;;WAEG;QAEI,6BAAwB,GAAW,IAAI,CAAC;QAC/C;;;;WAIG;QACI,gBAAW,GAA2C,IAAI,CAAC;QAElE;;WAEG;QAEI,wBAAmB,GAAW,GAAG,CAAC;QACzC;;WAEG;QAEI,oBAAe,GAAW,GAAG,CAAC;QACrC;;WAEG;QAEI,oBAAe,GAAW,GAAG,CAAC;QAsBrC;;WAEG;QAEI,qBAAgB,GAAsB,IAAI,CAAC;QAClD;;WAEG;QAEI,sBAAiB,GAAW,IAAI,CAAC;QACxC;;WAEG;QAEI,4BAAuB,GAAW,GAAG,CAAC;QAC7C;;WAEG;QAEI,uBAAkB,GAAW,GAAG,CAAC;QACxC;;;WAGG;QAEI,gCAA2B,GAAW,IAAI,CAAC;QAClD;;WAEG;QAEI,uBAAkB,GAAW,KAAK,CAAC;QAC1C;;;WAGG;QAEI,oBAAe,GAAsB,IAAI,CAAC;QACjD;;;WAGG;QAEI,yBAAoB,GAAsB,IAAI,CAAC;QAEtD;;WAEG;QAEI,yBAAoB,GAAW,IAAI,CAAC;QAC3C;;WAEG;QAEI,0BAAqB,GAAW,IAAI,CAAC;QAuC5C;;WAEG;QACI,eAAU,GAAgB,EAAE,CAAC;QAM5B,+BAA0B,GAA0B,IAAI,CAAC;QAGzD,mBAAc,GAAW,GAAG,CAAC;QAC7B,qBAAgB,GAAW,GAAG,CAAC;QAC/B,qBAAgB,GAAY,KAAK,CAAC;QAClC,yBAAoB,GAAW,GAAG,CAAC;QACnC,oBAAe,GAAW,GAAG,CAAC;QAC9B,6BAAwB,GAAY,KAAK,CAAC;QAI1C,yBAAoB,GAAkB,EAAE,CAAC;QAKjD,sBAAsB;QACd,kBAAa,GAAY,KAAK,CAAC;QAC/B,yBAAoB,GAAY,KAAK,CAAC;QACtC,gBAAW,GAAY,KAAK,CAAC;QAC7B,sBAAiB,GAAY,KAAK,CAAC;QACnC,gBAAW,GAAY,KAAK,CAAC;QAC7B,uBAAkB,GAAY,KAAK,CAAC;QACpC,iBAAY,GAAY,KAAK,CAAC;QAC9B,mCAA8B,GAAY,KAAK,CAAC;QAEhD,uBAAkB,GAAW,IAAI,CAAC;QAClC,+BAA0B,GAAW,IAAI,CAAC;QAC1C,aAAQ,GAAW,CAAC,CAAC;QA4NzB,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAElD,aAAa;QACb,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,gBAAgB,GAAG,mBAAmB,CAAC;QAC5C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,OAAO;QACP,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,sBAAsB,CAAC;QAEzI,SAAS;QACT,KAAK,CAAC,gCAAgC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAEO,cAAc;QAClB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YACxB,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxG,mDAAmD;YACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;SACrD;QACD,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,2BAA2B;QAC3B,IAAI,IAAI,CAAC,8BAA8B,EAAE;YACrC,IAAI,CAAC,gCAAgC,GAAG,IAAI,gCAAgC,CACxE,SAAS,EACT,KAAK,EACL,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,IAAI,CAAC,iBAAiB,CACzB,CAAC;YACF,IAAI,CAAC,gCAAgC,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC7D,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,gCAAgC,CAAC;YAC5E,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,SAAS,CAAC,IAAI,uBAAuB,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,2BAA2B,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,gCAAgC,EAAE,IAAI,CAAC,CAAC,CAAC;SAClJ;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,IAAI,CAAC,mBAAmB,GAAG,IAAI,WAAW,CACtC,SAAS,EACT,UAAU,EACV,EAAE,EACF,EAAE,EACF,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,2BAA2B,EAC3B,IAAI,CAAC,iBAAiB,CACzB,CAAC;SACL;aAAM;YACH,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,CAAC;SACpD;QAED,IAAI,CAAC,mBAAmB,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,gCAAgC,CAAC;QAC5E,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE;YAChD,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,IAAI,uBAAuB,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,oBAAoB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,CAAC;QAE3H,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,qCAAqC;YACrC,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAEtD,kCAAkC;YAClC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAEpD,4DAA4D;YAC5D,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YAEnD,oCAAoC;YACpC,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAElD,4CAA4C;YAC5C,IAAI,CAAC,4BAA4B,GAAG,IAAI,WAAW,CAC/C,uBAAuB,EACvB,UAAU,EACV,EAAE,EACF,EAAE,EACF,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,2BAA2B,EAC3B,SAAS,CAAC,wBAAwB,CACrC,CAAC;YACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,2BAA2B,EAC3B,GAAG,EAAE;gBACD,OAAO,IAAI,CAAC,4BAA4B,CAAC;YAC7C,CAAC,EACD,IAAI,CACP,CACJ,CAAC;SACL;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,0BAA0B;YAC1B,IAAI,CAAC,iCAAiC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAErD,6CAA6C;YAC7C,IAAI,CAAC,+BAA+B,GAAG,IAAI,WAAW,CAClD,aAAa,EACb,UAAU,EACV,EAAE,EACF,EAAE,EACF,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,2BAA2B,EAC3B,SAAS,CAAC,wBAAwB,CACrC,CAAC;YACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,aAAa,EACb,GAAG,EAAE;gBACD,OAAO,IAAI,CAAC,+BAA+B,CAAC;YAChD,CAAC,EACD,IAAI,CACP,CACJ,CAAC;SACL;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,iCAAiC;YACjC,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAE/C,+EAA+E;YAC/E,IAAI,CAAC,yBAAyB,GAAG,IAAI,WAAW,CAC5C,oCAAoC,EACpC,UAAU,EACV,EAAE,EACF,EAAE,EACF,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,2BAA2B,EAC3B,SAAS,CAAC,wBAAwB,CACrC,CAAC;YACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,oCAAoC,EACpC,GAAG,EAAE;gBACD,OAAO,IAAI,CAAC,yBAAyB,CAAC;YAC1C,CAAC,EACD,IAAI,CACP,CACJ,CAAC;SACL;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,mBAAmB;YACnB,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAElE,aAAa;YACb,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAEzC,wEAAwE;YACxE,IAAI,CAAC,mBAAmB,GAAG,IAAI,WAAW,CACtC,+BAA+B,EAC/B,UAAU,EACV,EAAE,EACF,EAAE,EACF,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,2BAA2B,EAC3B,SAAS,CAAC,wBAAwB,CACrC,CAAC;YACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,+BAA+B,EAC/B,GAAG,EAAE;gBACD,OAAO,IAAI,CAAC,mBAAmB,CAAC;YACpC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;SACL;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,8CAA8C;YAC9C,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,uBAAuB,CAAC,CAAC;YAE5E,qCAAqC;YACrC,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACrD;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,kCAAkC;YAClC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACnD;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,2BAA2B;YAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,qBAAqB,EAAE,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,wBAAwB,CAAC,CAAC;YAC3J,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,SAAS,EACT,GAAG,EAAE;gBACD,OAAO,IAAI,CAAC,eAAe,CAAC;YAChC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;SACL;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YACxB,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,6BAA6B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SACzG;QAED,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE;YACzE,MAAM,CAAC,IAAI,CAAC,qFAAqF,CAAC,CAAC;SACtG;IACL,CAAC;IAED,8BAA8B;IACtB,8BAA8B,CAAC,KAAY,EAAE,KAAa;QAC9D,MAAM,mBAAmB,GAAG,IAAI,KAAK,CAAS,EAAE,CAAC,CAAC;QAClD,IAAI,CAAC,uBAAuB,GAAG,IAAI,WAAW,CAC1C,iBAAiB,EACjB,UAAU,EACV,CAAC,WAAW,CAAC,EACb,EAAE,EACF,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,wBAAwB,EACxB,IAAI,CAAC,iBAAiB,CACzB,CAAC;QAEF,IAAI,CAAC,uBAAuB,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YACtD,IAAI,EAAE,GAAG,CAAC,CAAC;YACX,MAAM,KAAK,GAAiB,IAAI,CAAC,uBAAwB,CAAC,KAAK,CAAC;YAChE,MAAM,MAAM,GAAiB,IAAI,CAAC,uBAAwB,CAAC,MAAM,CAAC;YAElE,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACzB,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBACzB,mBAAmB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;oBACpD,mBAAmB,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC;oBACzD,EAAE,IAAI,CAAC,CAAC;iBACX;aACJ;YAED,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;QACvD,CAAC,CAAC;QAEF,kBAAkB;QAClB,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,iBAAiB,EACjB,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACxC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;IACN,CAAC;IAED,0BAA0B;IAClB,4BAA4B,CAAC,KAAY,EAAE,KAAa;QAC5D,MAAM,aAAa,GAAG,IAAI,KAAK,CAAS,CAAC,CAAC,CAAC;QAC3C,IAAI,CAAC,qBAAqB,GAAG,IAAI,WAAW,CACxC,eAAe,EACf,UAAU,EACV,CAAC,WAAW,EAAE,iBAAiB,CAAC,EAChC,EAAE,EACF,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,qBAAqB,EACrB,IAAI,CAAC,iBAAiB,CACzB,CAAC;QAEF,IAAI,CAAC,qBAAqB,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YACpD,MAAM,EAAE,GAAG,GAAG,GAAiB,IAAI,CAAC,qBAAsB,CAAC,KAAK,CAAC;YACjE,MAAM,EAAE,GAAG,GAAG,GAAiB,IAAI,CAAC,qBAAsB,CAAC,MAAM,CAAC;YAElE,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;YAC7B,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;YAC5B,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;YAC5B,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;YAC5B,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;YAC7B,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;YAC7B,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;YAC5B,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;YAE7B,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7D,CAAC,CAAC;QAEF,kBAAkB;QAClB,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,eAAe,EACf,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACtC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;IACN,CAAC;IAED,iCAAiC;IACzB,wBAAwB,CAAC,KAAY,EAAE,KAAa,EAAE,MAAc,EAAE,eAAuB,WAAW;QAC5G,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,MAAM,KAAK,GAAG,IAAI,eAAe,CAC7B,UAAU,GAAG,GAAG,GAAG,MAAM,EACzB,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EACX,IAAK,CAAC,YAAY,CAAC,EACzB,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,IAAI,CAAC,iBAAiB,CACzB,CAAC;QACF,MAAM,KAAK,GAAG,IAAI,eAAe,CAC7B,UAAU,GAAG,GAAG,GAAG,MAAM,EACzB,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EACX,IAAK,CAAC,YAAY,CAAC,EACzB,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,IAAI,CAAC,iBAAiB,CACzB,CAAC;QAEF,KAAK,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,EAAE;YAChC,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;YACjD,KAAK,CAAC,MAAM,GAAS,IAAK,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,KAAK,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,EAAE;YAChC,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;YACnD,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAO,IAAK,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,UAAU,GAAG,MAAM,EACnB,GAAG,EAAE;YACD,OAAO,KAAK,CAAC;QACjB,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,UAAU,GAAG,MAAM,EACnB,GAAG,EAAE;YACD,OAAO,KAAK,CAAC;QACjB,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QAEF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,oCAAoC;IAC5B,8BAA8B,CAAC,KAAY,EAAE,KAAa;QAC9D,IAAI,CAAC,uBAAuB,GAAG,IAAI,WAAW,CAC1C,iBAAiB,EACjB,UAAU,EACV,CAAC,UAAU,CAAC,EACZ,CAAC,cAAc,EAAE,aAAa,CAAC,EAC/B,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,uBAAuB,EACvB,IAAI,CAAC,iBAAiB,CACzB,CAAC;QACF,IAAI,CAAC,uBAAuB,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YACtD,MAAM,CAAC,yBAAyB,CAAC,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAChI,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAEnD,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAEnD,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,4BAA4B,CAAC;QACxE,CAAC,CAAC;QAEF,kBAAkB;QAClB,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,iBAAiB,EACjB,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACxC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;IACN,CAAC;IAEO,iCAAiC,CAAC,KAAY,EAAE,KAAa;QACjE,MAAM,gBAAgB,GAA2B,KAAK,CAAC,4BAA4B,EAAE,CAAC;QACtF,gBAAgB,CAAC,cAAc,GAAG,IAAI,CAAC;QAEvC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,UAAU,EAAE,CAAC;QAE/C,oBAAoB;QACpB,IAAI,CAAC,0BAA0B,GAAG,IAAI,WAAW,CAC7C,QAAQ,EACR,UAAU,EACV,CAAC,sBAAsB,EAAE,gBAAgB,EAAE,cAAc,EAAE,UAAU,EAAE,uBAAuB,EAAE,iBAAiB,EAAE,aAAa,CAAC,EACjI,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,EACvC,KAAK,GAAG,CAAC,EACT,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,gCAAgC,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAC,CAChF,CAAC;QAEF,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEnC,IAAI,CAAC,0BAA0B,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YACzD,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACvF,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAG,CAAC;gBAEzD,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC;gBAChE,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE3D,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACvD,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC;gBAEzE,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;gBAC7E,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE,SAAS,CAAC,kBAAkB,EAAE,CAAC,CAAC;gBAEzE,MAAM,CAAC,QAAQ,CAAC,uBAAuB,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBAC1E,MAAM,CAAC,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAE9D,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBACxE,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBACxE,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;aACjD;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,QAAQ,EACR,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,0BAA0B,CAAC;QAC3C,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QAEF,SAAS;QACT,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,0BAA0B,CAAC,CAAC;QAE/E,QAAQ;QACR,IAAI,CAAC,8BAA8B,GAAG,IAAI,WAAW,CACjD,aAAa,EACb,UAAU,EACV,EAAE,EACF,CAAC,iBAAiB,CAAC,EACnB,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,kBAAkB,CACrB,CAAC;QAEF,IAAI,CAAC,8BAA8B,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YAC7D,MAAM,CAAC,yBAAyB,CAAC,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEvI,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,+BAA+B,CAAC;QAC3E,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,aAAa,EACb,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,8BAA8B,CAAC;QAC/C,CAAC,EACD,IAAI,CACP,CACJ,CAAC;IACN,CAAC;IAED,mBAAmB;IACX,6BAA6B,CAAC,KAAY,EAAE,WAAmB;QACnE,mBAAmB;QACnB,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,yBAAyB,CAAC,cAAc,CAAC,CAAC;QACjE,IAAI,CAAC,oBAAoB,GAAG,IAAI,WAAW,CACvC,cAAc,EACd,UAAU,EACV,CAAC,YAAY,CAAC,EACd,EAAE,EACF,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAC7B,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,mBAAmB,EACnB,WAAW,CACd,CAAC;QAEF,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,IAAI,CAAC,oBAAoB,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YACnD,MAAM,EAAE,GAAG,GAAG,GAAiB,IAAI,CAAC,oBAAqB,CAAC,KAAK,CAAC;YAChE,MAAM,EAAE,GAAG,GAAG,GAAiB,IAAI,CAAC,oBAAqB,CAAC,MAAM,CAAC;YAEjE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;YACvB,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;YACtB,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;YACtB,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;YACtB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;YACvB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;YACvB,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;YACtB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;YAEvB,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC;QAEF,kBAAkB;QAClB,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,cAAc,EACd,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACrC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QAEF,+BAA+B;QAC/B,KAAK,IAAI,CAAC,GAAG,yBAAyB,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACpE,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEtB,IAAI,OAAO,GAAG,iCAAiC,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,EAAE;gBACT,OAAO,IAAI,4BAA4B,CAAC;aAC3C;YAED,MAAM,WAAW,GAAG,IAAI,WAAW,CAC/B,wBAAwB,GAAG,CAAC,EAC5B,UAAU,EACV,CAAC,WAAW,EAAE,mBAAmB,CAAC,EAClC,EAAE,EACF,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAC7B,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,OAAO,EACP,WAAW,CACd,CAAC;YACF,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC3D;QAED,mCAAmC;QACnC,IAAI,aAAa,GAA0B,IAAI,CAAC,oBAAoB,CAAC;QAErE,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;YACxD,MAAM,iBAAiB,GAAG,IAAI,KAAK,CAAS,EAAE,CAAC,CAAC;YAEhD,EAAE,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;gBAC5B,IAAI,CAAC,aAAa,EAAE;oBAChB,OAAO;iBACV;gBAED,IAAI,EAAE,GAAG,CAAC,CAAC;gBACX,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBACzB,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;wBACzB,iBAAiB,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC;wBAChD,iBAAiB,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC;wBACrD,EAAE,IAAI,CAAC,CAAC;qBACX;iBACJ;gBAED,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;gBACjD,MAAM,CAAC,QAAQ,CAAC,mBAAmB,EAAE,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;gBAEhE,IAAI,KAAK,KAAK,IAAI,CAAC,gCAAgC,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC5D,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC;iBAC7C;qBAAM;oBACH,aAAa,GAAG,EAAE,CAAC;iBACtB;YACL,CAAC,CAAC;YAEF,IAAI,KAAK,KAAK,IAAI,CAAC,gCAAgC,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC5D,EAAE,CAAC,aAAa,GAAG,GAAG,EAAE;oBACpB,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACvD,MAAM,SAAS,GAAG,IAAI,OAAO,CAAC,GAAG,GAAG,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,CAAC;oBACtG,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;wBACjB,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;wBAC1C,IAAI,CAAC,oBAAoB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;oBACxI,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC;aACL;YAED,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,wBAAwB,GAAG,KAAK,EAChC,GAAG,EAAE;gBACD,OAAO,EAAE,CAAC;YACd,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED,0BAA0B;IAClB,qBAAqB,CAAC,KAAY,EAAE,KAAa;QACrD,MAAM,OAAO,GAAG,CAAC,aAAa,CAAC,CAAC;QAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACzC;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,WAAW,CACjC,KAAK,EACL,UAAU,EACV,CAAC,kBAAkB,CAAC,EACpB,CAAC,qBAAqB,CAAC,EACvB,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAClB,SAAS,CAAC,wBAAwB,CACrC,CAAC;QAEF,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YAC7C,MAAM,CAAC,yBAAyB,CAAC,qBAAqB,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAEzF,IAAI,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,CAAC;YAEzC,IAAI,eAAe,GAAG,CAAC,EAAE;gBACrB,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC;aAC/C;iBAAM;gBACH,MAAM,EAAE,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC;gBAEtC,IAAI,IAAI,CAAC,oBAAoB,GAAG,eAAe,GAAG,IAAI,CAAC,eAAe,GAAG,EAAE,EAAE;oBACzE,eAAe,IAAI,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;iBAChD;qBAAM,IAAI,IAAI,CAAC,oBAAoB,GAAG,eAAe,GAAG,IAAI,CAAC,eAAe,GAAG,EAAE,EAAE;oBAChF,eAAe,IAAI,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;iBAChD;qBAAM;oBACH,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC;iBAC/C;aACJ;YAED,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,GAAG,eAAe,CAAC;aACjE;iBAAM;gBACH,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;gBAChF,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;aACxD;YAED,QAAQ,GAAG,IAAI,CAAC;YAEhB,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAC/D,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B,CAAC,EACD,IAAI,CACP,CACJ,CAAC;IACN,CAAC;IAED,iCAAiC;IACzB,2BAA2B,CAAC,KAAY,EAAE,KAAa;QAC3D,IAAI,CAAC,oBAAoB,GAAG,IAAI,WAAW,CACvC,cAAc,EACd,UAAU,EACV,CAAC,UAAU,EAAE,gBAAgB,EAAE,WAAW,EAAE,YAAY,EAAE,oBAAoB,CAAC,EAC/E,CAAC,kBAAkB,CAAC,EACpB,KAAK,GAAG,CAAC,EACT,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,oBAAoB,EACpB,SAAS,CAAC,wBAAwB,CACrC,CAAC;QACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,cAAc,EACd,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACrC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QAEF,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,oBAAoB,CAAC,CAAC;QAEzE,IAAI,CAAC,2BAA2B,GAAG,IAAI,WAAW,CAC9C,qBAAqB,EACrB,UAAU,EACV,CAAC,gBAAgB,CAAC,EAClB,CAAC,cAAc,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,EACtD,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,4BAA4B,EAC5B,SAAS,CAAC,wBAAwB,CACrC,CAAC;QACF,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,qBAAqB,EACrB,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,2BAA2B,CAAC;QAC5C,CAAC,EACD,IAAI,CACP,CACJ,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAErC,aAAa;QACb,IAAI,CAAC,oBAAoB,CAAC,6BAA6B,GAAG,IAAI,CAAC;QAC/D,IAAI,CAAC,oBAAoB,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YACnD,MAAM,CAAC,yBAAyB,CAAC,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC/H,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC7D,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACpD,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAChE,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAEtD,QAAQ;YACR,UAAU,CAAC,CAAC,GAAiB,IAAI,CAAC,oBAAqB,CAAC,KAAK,CAAC;YAC9D,UAAU,CAAC,CAAC,GAAiB,IAAI,CAAC,oBAAqB,CAAC,MAAM,CAAC;YAC/D,MAAM,CAAC,UAAU,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YAE5C,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC5E,CAAC,CAAC;QAEF,UAAU;QACV,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAEvH,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAErH,IAAI,CAAC,2BAA2B,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YAC1D,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBAC3B,OAAO;aACV;YAED,MAAM,CAAC,yBAAyB,CAAC,cAAc,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC5E,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAChE,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAE3D,6BAA6B;YAC7B,MAAM,OAAO,GAAY,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5E,MAAM,OAAO,GAAY,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5E,IAAI,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YACzI,MAAM,IAAI,GAAG,CAAC;YAEd,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAClC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,EACtB,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EACjB,GAAG,EACH,GAAG,EACH,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAChB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,EACtB,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACN,CAAC;YAEF,MAAM,cAAc,GAAG,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAE9E,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;YAEnD,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,yBAAyB,CAAC;QACrE,CAAC,CAAC;IACN,CAAC;IAED,qCAAqC;IAC7B,8BAA8B,CAAC,KAAY,EAAE,KAAa;QAC9D,IAAI,CAAC,uBAAuB,GAAG,IAAI,WAAW,CAC1C,iBAAiB,EACjB,UAAU,EACV,CAAC,UAAU,CAAC,EACZ,CAAC,cAAc,EAAE,cAAc,CAAC,EAChC,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,wBAAwB,EACxB,SAAS,CAAC,wBAAwB,CACrC,CAAC;QACF,IAAI,CAAC,uBAAuB,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YACtD,MAAM,CAAC,yBAAyB,CAAC,cAAc,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAClF,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YAE3D,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC3D,CAAC,CAAC;QAEF,kBAAkB;QAClB,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,iBAAiB,EACjB,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACxC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;IACN,CAAC;IAED,kCAAkC;IAC1B,4BAA4B,CAAC,KAAY,EAAE,KAAa;QAC5D,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,MAAM,EAAE,GAAG,IAAI,qBAAqB,CAAC,eAAe,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,qBAAqB,EAAE,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,wBAAwB,CAAC,CAAC;YACvK,EAAE,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;YACxC,EAAE,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;YAC9C,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;SACnC;aAAM;YACH,IAAI,CAAC,qBAAqB,GAAG,IAAI,WAAW,CACxC,eAAe,EACf,UAAU,EACV,CAAC,uBAAuB,EAAE,oBAAoB,EAAE,YAAY,EAAE,aAAa,EAAE,gBAAgB,CAAC,EAC9F,CAAC,cAAc,CAAC,EAChB,KAAK,EACL,IAAI,EACJ,OAAO,CAAC,qBAAqB,EAC7B,KAAK,CAAC,SAAS,EAAE,EACjB,KAAK,EACL,kDAAkD,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,EACtF,SAAS,CAAC,wBAAwB,CACrC,CAAC;YAEF,IAAI,WAAW,GAAW,CAAC,CAAC;YAC5B,IAAI,kBAAkB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC5C,IAAI,cAAc,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YACvC,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YAElC,IAAI,CAAC,qBAAqB,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;gBACpD,cAAc,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;gBAE7E,cAAc,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;gBAC9C,MAAM,CAAC,SAAS,CAAC,uBAAuB,EAAE,iBAAiB,CAAC,CAAC;gBAE7D,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;gBAC3D,kBAAkB,GAAG,cAAc,CAAC;gBAEpC,UAAU,CAAC,CAAC,GAAiB,IAAI,CAAC,qBAAsB,CAAC,KAAK,CAAC;gBAC/D,UAAU,CAAC,CAAC,GAAiB,IAAI,CAAC,qBAAsB,CAAC,MAAM,CAAC;gBAChE,MAAM,CAAC,UAAU,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;gBAE5C,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;gBAChD,MAAM,CAAC,QAAQ,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;gBAC5C,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAEvD,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YAC/D,CAAC,CAAC;SACL;QAED,IAAI,CAAC,SAAS,CACV,IAAI,uBAAuB,CACvB,KAAK,CAAC,SAAS,EAAE,EACjB,eAAe,EACf,GAAG,EAAE;YACD,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACtC,CAAC,EACD,IAAI,CACP,CACJ,CAAC;IACN,CAAC;IAEO,gBAAgB;QACpB,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,oBAAoB,EAAE;YACxD,MAAM,QAAQ,GAA2B,IAAI,CAAC,MAAM,CAAC,4BAA4B,EAAE,CAAC;YACpF,OAAO,QAAQ,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SAC5C;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,WAAW,EAAE,CAAC;IAC3D,CAAC;IAEO,qBAAqB;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEhC,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC5C;YACD,IAAI,IAAI,CAAC,gCAAgC,EAAE;gBACvC,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACzD;YAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAChD;YACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC9C;YACD,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAChD;YAED,IAAI,IAAI,CAAC,0BAA0B,EAAE;gBACjC,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACnD;YACD,IAAI,IAAI,CAAC,iCAAiC,EAAE;gBACxC,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC1D;YACD,IAAI,IAAI,CAAC,iCAAiC,EAAE;gBACxC,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC1D;YACD,IAAI,IAAI,CAAC,8BAA8B,EAAE;gBACrC,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACvD;YACD,IAAI,IAAI,CAAC,+BAA+B,EAAE;gBACtC,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACxD;YAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC7C;YACD,IAAI,IAAI,CAAC,2BAA2B,EAAE;gBAClC,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACpD;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gCAAgC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnE,IAAI,CAAC,gCAAgC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC5D;YAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC7C;YACD,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACvC;YACD,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC5C;YAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAChD;YAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC9C;YAED,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACxC;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrD,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC9C;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrD,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC9C;SACJ;QAED,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;QACzC,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;QAC9C,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;QAC9C,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC;QAC3C,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC;QAC5C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;QACxC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;QAE7C,IAAI,CAAC,gCAAgC,CAAC,MAAM,GAAG,CAAC,CAAC;QACjD,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAExG,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAEhE,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;SAC3D;QAED,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACvC,mBAAmB,CAAC,gCAAgC,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;SAC/H;QAED,mBAAmB,CAAC,UAAU,GAAG,2BAA2B,CAAC;QAE7D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,MAAM,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,yBAAyB,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAErI,IAAI,MAAM,CAAC,aAAa,EAAE;YACtB,CAAC,CAAC,WAAW,GAAiC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;SAC1F;QAED,IAAI,MAAM,CAAC,gCAAgC,EAAE;YACzC,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,gCAAgC,EAAE,MAAM,CAAC,gCAAgC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAChI;QAED,OAAO,CAAC,CAAC;IACb,CAAC;;AAED;;GAEG;AACW,wCAAc,GAAW,CAAC,AAAZ,CAAa;AApgDlC;IADN,SAAS,EAAE;kEACyB;AAM9B;IADN,SAAS,EAAE;4DACqB;AAK1B;IADN,SAAS,EAAE;iEAC2B;AAMvC;IADC,SAAS,EAAE;yDAGX;AAaM;IADN,kBAAkB,CAAC,aAAa,CAAC;8DACW;AAMtC;IADN,SAAS,EAAE;6EACoC;AAKzC;IADN,SAAS,EAAE;uEAC8B;AAKnC;IADN,SAAS,EAAE;2EACmC;AAYxC;IADN,SAAS,EAAE;sEAC6B;AAKlC;IADN,SAAS,EAAE;kEACyB;AAK9B;IADN,SAAS,EAAE;kEACyB;AAKrC;IADC,SAAS,EAAE;gEAGX;AAmBM;IADN,kBAAkB,CAAC,kBAAkB,CAAC;mEACW;AAK3C;IADN,SAAS,EAAE;oEAC4B;AAKjC;IADN,SAAS,EAAE;0EACiC;AAKtC;IADN,SAAS,EAAE;qEAC4B;AAMjC;IADN,SAAS,EAAE;8EACsC;AAK3C;IADN,SAAS,EAAE;qEAC8B;AAMnC;IADN,kBAAkB,CAAC,iBAAiB,CAAC;kEACW;AAM1C;IADN,kBAAkB,CAAC,sBAAsB,CAAC;uEACW;AAM/C;IADN,SAAS,EAAE;uEAC+B;AAKpC;IADN,SAAS,EAAE;wEACgC;AAM5C;IADC,SAAS,EAAE;+DAGX;AAgBD;IADC,SAAS,EAAE;sEAGX;AAqCO;IADP,SAAS,EAAE;yDACW;AAqBvB;IADC,SAAS,EAAE;6DAGX;AAgBD;IADC,SAAS,EAAE;oEAGX;AAgBD;IADC,SAAS,EAAE;iEAGX;AAgBD;IADC,SAAS,EAAE;2DAGX;AAiBD;IAFC,SAAS,EAAE;IACZ,gEAAgE;2DAG/D;AAyBD;IADC,SAAS,EAAE;kEAGX;AAeD;IADC,SAAS,EAAE;4DAGX;AAeD;IADC,SAAS,EAAE;8EAGX;AAgBD;IADC,SAAS,EAAE;0EAGX;AAeD;IADC,SAAS,EAAE;kEAGX;AAkBD;IADC,SAAS,EAAE;wDAGX;AAmmCL,aAAa,CAAC,mCAAmC,EAAE,yBAAyB,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"../../../types\";\r\nimport { serialize, serializeAsTexture } from \"../../../Misc/decorators\";\r\nimport { SerializationHelper } from \"../../../Misc/decorators.serialization\";\r\nimport type { IAnimatable } from \"../../../Animations/animatable.interface\";\r\nimport { Logger } from \"../../../Misc/logger\";\r\nimport { Vector2, Vector3, Matrix, Vector4 } from \"../../../Maths/math.vector\";\r\nimport { Scalar } from \"../../../Maths/math.scalar\";\r\nimport type { Camera } from \"../../../Cameras/camera\";\r\nimport type { Effect } from \"../../../Materials/effect\";\r\nimport { Texture } from \"../../../Materials/Textures/texture\";\r\nimport { PostProcess } from \"../../../PostProcesses/postProcess\";\r\nimport { PostProcessRenderPipeline } from \"../../../PostProcesses/RenderPipeline/postProcessRenderPipeline\";\r\nimport { PostProcessRenderEffect } from \"../../../PostProcesses/RenderPipeline/postProcessRenderEffect\";\r\nimport { BlurPostProcess } from \"../../../PostProcesses/blurPostProcess\";\r\nimport { FxaaPostProcess } from \"../../../PostProcesses/fxaaPostProcess\";\r\nimport type { IDisposable, Scene } from \"../../../scene\";\r\nimport type { SpotLight } from \"../../../Lights/spotLight\";\r\nimport type { DirectionalLight } from \"../../../Lights/directionalLight\";\r\nimport type { GeometryBufferRenderer } from \"../../../Rendering/geometryBufferRenderer\";\r\n\r\nimport { Constants } from \"../../../Engines/constants\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport { MotionBlurPostProcess } from \"../../motionBlurPostProcess\";\r\nimport { ScreenSpaceReflectionPostProcess } from \"../../screenSpaceReflectionPostProcess\";\r\n\r\nimport type { Animation } from \"../../../Animations/animation\";\r\n\r\nimport \"../../../PostProcesses/RenderPipeline/postProcessRenderPipelineManagerSceneComponent\";\r\n\r\nimport \"../../../Shaders/standard.fragment\";\r\n/**\r\n * Standard rendering pipeline\r\n * Default pipeline should be used going forward but the standard pipeline will be kept for backwards compatibility.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/standardRenderingPipeline\r\n */\r\nexport class StandardRenderingPipeline extends PostProcessRenderPipeline implements IDisposable, IAnimatable {\r\n    /**\r\n     * Public members\r\n     */\r\n    // Post-processes\r\n    /**\r\n     * Post-process which contains the original scene color before the pipeline applies all the effects\r\n     */\r\n    public originalPostProcess: Nullable<PostProcess>;\r\n    /**\r\n     * Post-process used to down scale an image x4\r\n     */\r\n    public downSampleX4PostProcess: Nullable<PostProcess> = null;\r\n    /**\r\n     * Post-process used to calculate the illuminated surfaces controlled by a threshold\r\n     */\r\n    public brightPassPostProcess: Nullable<PostProcess> = null;\r\n    /**\r\n     * Post-process array storing all the horizontal blur post-processes used by the pipeline\r\n     */\r\n    public blurHPostProcesses: PostProcess[] = [];\r\n    /**\r\n     * Post-process array storing all the vertical blur post-processes used by the pipeline\r\n     */\r\n    public blurVPostProcesses: PostProcess[] = [];\r\n    /**\r\n     * Post-process used to add colors of 2 textures (typically brightness + real scene color)\r\n     */\r\n    public textureAdderPostProcess: Nullable<PostProcess> = null;\r\n\r\n    /**\r\n     * Post-process used to create volumetric lighting effect\r\n     */\r\n    public volumetricLightPostProcess: Nullable<PostProcess> = null;\r\n    /**\r\n     * Post-process used to smooth the previous volumetric light post-process on the X axis\r\n     */\r\n    public volumetricLightSmoothXPostProcess: Nullable<BlurPostProcess> = null;\r\n    /**\r\n     * Post-process used to smooth the previous volumetric light post-process on the Y axis\r\n     */\r\n    public volumetricLightSmoothYPostProcess: Nullable<BlurPostProcess> = null;\r\n    /**\r\n     * Post-process used to merge the volumetric light effect and the real scene color\r\n     */\r\n    public volumetricLightMergePostProces: Nullable<PostProcess> = null;\r\n    /**\r\n     * Post-process used to store the final volumetric light post-process (attach/detach for debug purpose)\r\n     */\r\n    public volumetricLightFinalPostProcess: Nullable<PostProcess> = null;\r\n\r\n    /**\r\n     * Base post-process used to calculate the average luminance of the final image for HDR\r\n     */\r\n    public luminancePostProcess: Nullable<PostProcess> = null;\r\n    /**\r\n     * Post-processes used to create down sample post-processes in order to get\r\n     * the average luminance of the final image for HDR\r\n     * Array of length \"StandardRenderingPipeline.LuminanceSteps\"\r\n     */\r\n    public luminanceDownSamplePostProcesses: PostProcess[] = [];\r\n    /**\r\n     * Post-process used to create a HDR effect (light adaptation)\r\n     */\r\n    public hdrPostProcess: Nullable<PostProcess> = null;\r\n    /**\r\n     * Post-process used to store the final texture adder post-process (attach/detach for debug purpose)\r\n     */\r\n    public textureAdderFinalPostProcess: Nullable<PostProcess> = null;\r\n    /**\r\n     * Post-process used to store the final lens flare post-process (attach/detach for debug purpose)\r\n     */\r\n    public lensFlareFinalPostProcess: Nullable<PostProcess> = null;\r\n    /**\r\n     * Post-process used to merge the final HDR post-process and the real scene color\r\n     */\r\n    public hdrFinalPostProcess: Nullable<PostProcess> = null;\r\n    /**\r\n     * Post-process used to create a lens flare effect\r\n     */\r\n    public lensFlarePostProcess: Nullable<PostProcess> = null;\r\n    /**\r\n     * Post-process that merges the result of the lens flare post-process and the real scene color\r\n     */\r\n    public lensFlareComposePostProcess: Nullable<PostProcess> = null;\r\n    /**\r\n     * Post-process used to create a motion blur effect\r\n     */\r\n    public motionBlurPostProcess: Nullable<PostProcess> = null;\r\n    /**\r\n     * Post-process used to create a depth of field effect\r\n     */\r\n    public depthOfFieldPostProcess: Nullable<PostProcess> = null;\r\n    /**\r\n     * The Fast Approximate Anti-Aliasing post process which attempts to remove aliasing from an image.\r\n     */\r\n    public fxaaPostProcess: Nullable<FxaaPostProcess> = null;\r\n    /**\r\n     * Post-process used to simulate realtime reflections using the screen space and geometry renderer.\r\n     */\r\n    public screenSpaceReflectionPostProcess: Nullable<ScreenSpaceReflectionPostProcess> = null;\r\n\r\n    // Values\r\n\r\n    /**\r\n     * Represents the brightness threshold in order to configure the illuminated surfaces\r\n     */\r\n    @serialize()\r\n    public brightThreshold: number = 1.0;\r\n\r\n    /**\r\n     * Configures the blur intensity used for surexposed surfaces are highlighted surfaces (light halo)\r\n     */\r\n    @serialize()\r\n    public blurWidth: number = 512.0;\r\n    /**\r\n     * Sets if the blur for highlighted surfaces must be only horizontal\r\n     */\r\n    @serialize()\r\n    public horizontalBlur: boolean = false;\r\n\r\n    /**\r\n     * Gets the overall exposure used by the pipeline\r\n     */\r\n    @serialize()\r\n    public get exposure(): number {\r\n        return this._fixedExposure;\r\n    }\r\n    /**\r\n     * Sets the overall exposure used by the pipeline\r\n     */\r\n    public set exposure(value: number) {\r\n        this._fixedExposure = value;\r\n        this._currentExposure = value;\r\n    }\r\n\r\n    /**\r\n     * Texture used typically to simulate \"dirty\" on camera lens\r\n     */\r\n    @serializeAsTexture(\"lensTexture\")\r\n    public lensTexture: Nullable<Texture> = null;\r\n\r\n    /**\r\n     * Represents the offset coefficient based on Rayleigh principle. Typically in interval [-0.2, 0.2]\r\n     */\r\n    @serialize()\r\n    public volumetricLightCoefficient: number = 0.2;\r\n    /**\r\n     * The overall power of volumetric lights, typically in interval [0, 10] maximum\r\n     */\r\n    @serialize()\r\n    public volumetricLightPower: number = 4.0;\r\n    /**\r\n     * Used the set the blur intensity to smooth the volumetric lights\r\n     */\r\n    @serialize()\r\n    public volumetricLightBlurScale: number = 64.0;\r\n    /**\r\n     * Light (spot or directional) used to generate the volumetric lights rays\r\n     * The source light must have a shadow generate so the pipeline can get its\r\n     * depth map\r\n     */\r\n    public sourceLight: Nullable<SpotLight | DirectionalLight> = null;\r\n\r\n    /**\r\n     * For eye adaptation, represents the minimum luminance the eye can see\r\n     */\r\n    @serialize()\r\n    public hdrMinimumLuminance: number = 1.0;\r\n    /**\r\n     * For eye adaptation, represents the decrease luminance speed\r\n     */\r\n    @serialize()\r\n    public hdrDecreaseRate: number = 0.5;\r\n    /**\r\n     * For eye adaptation, represents the increase luminance speed\r\n     */\r\n    @serialize()\r\n    public hdrIncreaseRate: number = 0.5;\r\n    /**\r\n     * Gets whether or not the exposure of the overall pipeline should be automatically adjusted by the HDR post-process\r\n     */\r\n    @serialize()\r\n    public get hdrAutoExposure(): boolean {\r\n        return this._hdrAutoExposure;\r\n    }\r\n    /**\r\n     * Sets whether or not the exposure of the overall pipeline should be automatically adjusted by the HDR post-process\r\n     */\r\n    public set hdrAutoExposure(value: boolean) {\r\n        this._hdrAutoExposure = value;\r\n        if (this.hdrPostProcess) {\r\n            const defines = [\"#define HDR\"];\r\n            if (value) {\r\n                defines.push(\"#define AUTO_EXPOSURE\");\r\n            }\r\n            this.hdrPostProcess.updateEffect(defines.join(\"\\n\"));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Lens color texture used by the lens flare effect. Mandatory if lens flare effect enabled\r\n     */\r\n    @serializeAsTexture(\"lensColorTexture\")\r\n    public lensColorTexture: Nullable<Texture> = null;\r\n    /**\r\n     * The overall strength for the lens flare effect\r\n     */\r\n    @serialize()\r\n    public lensFlareStrength: number = 20.0;\r\n    /**\r\n     * Dispersion coefficient for lens flare ghosts\r\n     */\r\n    @serialize()\r\n    public lensFlareGhostDispersal: number = 1.4;\r\n    /**\r\n     * Main lens flare halo width\r\n     */\r\n    @serialize()\r\n    public lensFlareHaloWidth: number = 0.7;\r\n    /**\r\n     * Based on the lens distortion effect, defines how much the lens flare result\r\n     * is distorted\r\n     */\r\n    @serialize()\r\n    public lensFlareDistortionStrength: number = 16.0;\r\n    /**\r\n     * Configures the blur intensity used for for lens flare (halo)\r\n     */\r\n    @serialize()\r\n    public lensFlareBlurWidth: number = 512.0;\r\n    /**\r\n     * Lens star texture must be used to simulate rays on the flares and is available\r\n     * in the documentation\r\n     */\r\n    @serializeAsTexture(\"lensStarTexture\")\r\n    public lensStarTexture: Nullable<Texture> = null;\r\n    /**\r\n     * As the \"lensTexture\" (can be the same texture or different), it is used to apply the lens\r\n     * flare effect by taking account of the dirt texture\r\n     */\r\n    @serializeAsTexture(\"lensFlareDirtTexture\")\r\n    public lensFlareDirtTexture: Nullable<Texture> = null;\r\n\r\n    /**\r\n     * Represents the focal length for the depth of field effect\r\n     */\r\n    @serialize()\r\n    public depthOfFieldDistance: number = 10.0;\r\n    /**\r\n     * Represents the blur intensity for the blurred part of the depth of field effect\r\n     */\r\n    @serialize()\r\n    public depthOfFieldBlurWidth: number = 64.0;\r\n\r\n    /**\r\n     * Gets how much the image is blurred by the movement while using the motion blur post-process\r\n     */\r\n    @serialize()\r\n    public get motionStrength(): number {\r\n        return this._motionStrength;\r\n    }\r\n    /**\r\n     * Sets how much the image is blurred by the movement while using the motion blur post-process\r\n     */\r\n    public set motionStrength(strength: number) {\r\n        this._motionStrength = strength;\r\n\r\n        if (this._isObjectBasedMotionBlur && this.motionBlurPostProcess) {\r\n            (this.motionBlurPostProcess as MotionBlurPostProcess).motionStrength = strength;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets whether or not the motion blur post-process is object based or screen based.\r\n     */\r\n    @serialize()\r\n    public get objectBasedMotionBlur(): boolean {\r\n        return this._isObjectBasedMotionBlur;\r\n    }\r\n    /**\r\n     * Sets whether or not the motion blur post-process should be object based or screen based\r\n     */\r\n    public set objectBasedMotionBlur(value: boolean) {\r\n        const shouldRebuild = this._isObjectBasedMotionBlur !== value;\r\n        this._isObjectBasedMotionBlur = value;\r\n\r\n        if (shouldRebuild) {\r\n            this._buildPipeline();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * List of animations for the pipeline (IAnimatable implementation)\r\n     */\r\n    public animations: Animation[] = [];\r\n\r\n    /**\r\n     * Private members\r\n     */\r\n    private _scene: Scene;\r\n    private _currentDepthOfFieldSource: Nullable<PostProcess> = null;\r\n    private _basePostProcess: Nullable<PostProcess>;\r\n\r\n    private _fixedExposure: number = 1.0;\r\n    private _currentExposure: number = 1.0;\r\n    private _hdrAutoExposure: boolean = false;\r\n    private _hdrCurrentLuminance: number = 1.0;\r\n    private _motionStrength: number = 1.0;\r\n    private _isObjectBasedMotionBlur: boolean = false;\r\n\r\n    private _floatTextureType: number;\r\n\r\n    private _camerasToBeAttached: Array<Camera> = [];\r\n\r\n    @serialize()\r\n    private _ratio: number;\r\n\r\n    // Getters and setters\r\n    private _bloomEnabled: boolean = false;\r\n    private _depthOfFieldEnabled: boolean = false;\r\n    private _vlsEnabled: boolean = false;\r\n    private _lensFlareEnabled: boolean = false;\r\n    private _hdrEnabled: boolean = false;\r\n    private _motionBlurEnabled: boolean = false;\r\n    private _fxaaEnabled: boolean = false;\r\n    private _screenSpaceReflectionsEnabled: boolean = false;\r\n\r\n    private _motionBlurSamples: number = 64.0;\r\n    private _volumetricLightStepsCount: number = 50.0;\r\n    private _samples: number = 1;\r\n\r\n    /**\r\n     * @ignore\r\n     * Specifies if the bloom pipeline is enabled\r\n     */\r\n    @serialize()\r\n    public get BloomEnabled(): boolean {\r\n        return this._bloomEnabled;\r\n    }\r\n\r\n    public set BloomEnabled(enabled: boolean) {\r\n        if (this._bloomEnabled === enabled) {\r\n            return;\r\n        }\r\n\r\n        this._bloomEnabled = enabled;\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * @ignore\r\n     * Specifies if the depth of field pipeline is enabled\r\n     */\r\n    @serialize()\r\n    public get DepthOfFieldEnabled(): boolean {\r\n        return this._depthOfFieldEnabled;\r\n    }\r\n\r\n    public set DepthOfFieldEnabled(enabled: boolean) {\r\n        if (this._depthOfFieldEnabled === enabled) {\r\n            return;\r\n        }\r\n\r\n        this._depthOfFieldEnabled = enabled;\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * @ignore\r\n     * Specifies if the lens flare pipeline is enabled\r\n     */\r\n    @serialize()\r\n    public get LensFlareEnabled(): boolean {\r\n        return this._lensFlareEnabled;\r\n    }\r\n\r\n    public set LensFlareEnabled(enabled: boolean) {\r\n        if (this._lensFlareEnabled === enabled) {\r\n            return;\r\n        }\r\n\r\n        this._lensFlareEnabled = enabled;\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * @ignore\r\n     * Specifies if the HDR pipeline is enabled\r\n     */\r\n    @serialize()\r\n    public get HDREnabled(): boolean {\r\n        return this._hdrEnabled;\r\n    }\r\n\r\n    public set HDREnabled(enabled: boolean) {\r\n        if (this._hdrEnabled === enabled) {\r\n            return;\r\n        }\r\n\r\n        this._hdrEnabled = enabled;\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * @ignore\r\n     * Specifies if the volumetric lights scattering effect is enabled\r\n     */\r\n    @serialize()\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get VLSEnabled(): boolean {\r\n        return this._vlsEnabled;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public set VLSEnabled(enabled) {\r\n        if (this._vlsEnabled === enabled) {\r\n            return;\r\n        }\r\n\r\n        if (enabled) {\r\n            const geometry = this._scene.enableGeometryBufferRenderer();\r\n            if (!geometry) {\r\n                Logger.Warn(\"Geometry renderer is not supported, cannot create volumetric lights in Standard Rendering Pipeline\");\r\n                return;\r\n            }\r\n        }\r\n\r\n        this._vlsEnabled = enabled;\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * @ignore\r\n     * Specifies if the motion blur effect is enabled\r\n     */\r\n    @serialize()\r\n    public get MotionBlurEnabled(): boolean {\r\n        return this._motionBlurEnabled;\r\n    }\r\n\r\n    public set MotionBlurEnabled(enabled: boolean) {\r\n        if (this._motionBlurEnabled === enabled) {\r\n            return;\r\n        }\r\n\r\n        this._motionBlurEnabled = enabled;\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * Specifies if anti-aliasing is enabled\r\n     */\r\n    @serialize()\r\n    public get fxaaEnabled(): boolean {\r\n        return this._fxaaEnabled;\r\n    }\r\n\r\n    public set fxaaEnabled(enabled: boolean) {\r\n        if (this._fxaaEnabled === enabled) {\r\n            return;\r\n        }\r\n\r\n        this._fxaaEnabled = enabled;\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * Specifies if screen space reflections are enabled.\r\n     */\r\n    @serialize()\r\n    public get screenSpaceReflectionsEnabled(): boolean {\r\n        return this._screenSpaceReflectionsEnabled;\r\n    }\r\n\r\n    public set screenSpaceReflectionsEnabled(enabled: boolean) {\r\n        if (this._screenSpaceReflectionsEnabled === enabled) {\r\n            return;\r\n        }\r\n\r\n        this._screenSpaceReflectionsEnabled = enabled;\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * Specifies the number of steps used to calculate the volumetric lights\r\n     * Typically in interval [50, 200]\r\n     */\r\n    @serialize()\r\n    public get volumetricLightStepsCount(): number {\r\n        return this._volumetricLightStepsCount;\r\n    }\r\n\r\n    public set volumetricLightStepsCount(count: number) {\r\n        if (this.volumetricLightPostProcess) {\r\n            this.volumetricLightPostProcess.updateEffect(\"#define VLS\\n#define NB_STEPS \" + count.toFixed(1));\r\n        }\r\n\r\n        this._volumetricLightStepsCount = count;\r\n    }\r\n\r\n    /**\r\n     * Specifies the number of samples used for the motion blur effect\r\n     * Typically in interval [16, 64]\r\n     */\r\n    @serialize()\r\n    public get motionBlurSamples(): number {\r\n        return this._motionBlurSamples;\r\n    }\r\n\r\n    public set motionBlurSamples(samples: number) {\r\n        if (this.motionBlurPostProcess) {\r\n            if (this._isObjectBasedMotionBlur) {\r\n                (this.motionBlurPostProcess as MotionBlurPostProcess).motionBlurSamples = samples;\r\n            } else {\r\n                this.motionBlurPostProcess.updateEffect(\"#define MOTION_BLUR\\n#define MAX_MOTION_SAMPLES \" + samples.toFixed(1));\r\n            }\r\n        }\r\n\r\n        this._motionBlurSamples = samples;\r\n    }\r\n\r\n    /**\r\n     * Specifies MSAA sample count, setting this to 4 will provide 4x anti aliasing. (default: 1)\r\n     */\r\n    @serialize()\r\n    public get samples(): number {\r\n        return this._samples;\r\n    }\r\n\r\n    public set samples(sampleCount: number) {\r\n        if (this._samples === sampleCount) {\r\n            return;\r\n        }\r\n\r\n        this._samples = sampleCount;\r\n        this._buildPipeline();\r\n    }\r\n\r\n    /**\r\n     * Default pipeline should be used going forward but the standard pipeline will be kept for backwards compatibility.\r\n     * @constructor\r\n     * @param name The rendering pipeline name\r\n     * @param scene The scene linked to this pipeline\r\n     * @param ratio The size of the postprocesses (0.5 means that your postprocess will have a width = canvas.width 0.5 and a height = canvas.height 0.5)\r\n     * @param originalPostProcess the custom original color post-process. Must be \"reusable\". Can be null.\r\n     * @param cameras The array of cameras that the rendering pipeline will be attached to\r\n     */\r\n    constructor(name: string, scene: Scene, ratio: number, originalPostProcess: Nullable<PostProcess> = null, cameras?: Camera[]) {\r\n        super(scene.getEngine(), name);\r\n        this._cameras = cameras || scene.cameras;\r\n        this._cameras = this._cameras.slice();\r\n        this._camerasToBeAttached = this._cameras.slice();\r\n\r\n        // Initialize\r\n        this._scene = scene;\r\n        this._basePostProcess = originalPostProcess;\r\n        this._ratio = ratio;\r\n\r\n        // Misc\r\n        this._floatTextureType = scene.getEngine().getCaps().textureFloatRender ? Constants.TEXTURETYPE_FLOAT : Constants.TEXTURETYPE_HALF_FLOAT;\r\n\r\n        // Finish\r\n        scene.postProcessRenderPipelineManager.addPipeline(this);\r\n        this._buildPipeline();\r\n    }\r\n\r\n    private _buildPipeline(): void {\r\n        const ratio = this._ratio;\r\n        const scene = this._scene;\r\n\r\n        this._disposePostProcesses();\r\n        if (this._cameras !== null) {\r\n            this._scene.postProcessRenderPipelineManager.detachCamerasFromRenderPipeline(this._name, this._cameras);\r\n            // get back cameras to be used to reattach pipeline\r\n            this._cameras = this._camerasToBeAttached.slice();\r\n        }\r\n        this._reset();\r\n\r\n        // Create pass post-process\r\n        if (this._screenSpaceReflectionsEnabled) {\r\n            this.screenSpaceReflectionPostProcess = new ScreenSpaceReflectionPostProcess(\r\n                \"HDRPass\",\r\n                scene,\r\n                ratio,\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                scene.getEngine(),\r\n                false,\r\n                this._floatTextureType\r\n            );\r\n            this.screenSpaceReflectionPostProcess.onApplyObservable.add(() => {\r\n                this._currentDepthOfFieldSource = this.screenSpaceReflectionPostProcess;\r\n            });\r\n            this.addEffect(new PostProcessRenderEffect(scene.getEngine(), \"HDRScreenSpaceReflections\", () => this.screenSpaceReflectionPostProcess, true));\r\n        }\r\n\r\n        if (!this._basePostProcess) {\r\n            this.originalPostProcess = new PostProcess(\r\n                \"HDRPass\",\r\n                \"standard\",\r\n                [],\r\n                [],\r\n                ratio,\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                scene.getEngine(),\r\n                false,\r\n                \"#define PASS_POST_PROCESS\",\r\n                this._floatTextureType\r\n            );\r\n        } else {\r\n            this.originalPostProcess = this._basePostProcess;\r\n        }\r\n\r\n        this.originalPostProcess.autoClear = !this.screenSpaceReflectionPostProcess;\r\n        this.originalPostProcess.onApplyObservable.add(() => {\r\n            this._currentDepthOfFieldSource = this.originalPostProcess;\r\n        });\r\n\r\n        this.addEffect(new PostProcessRenderEffect(scene.getEngine(), \"HDRPassPostProcess\", () => this.originalPostProcess, true));\r\n\r\n        if (this._bloomEnabled) {\r\n            // Create down sample X4 post-process\r\n            this._createDownSampleX4PostProcess(scene, ratio / 4);\r\n\r\n            // Create bright pass post-process\r\n            this._createBrightPassPostProcess(scene, ratio / 4);\r\n\r\n            // Create gaussian blur post-processes (down sampling blurs)\r\n            this._createBlurPostProcesses(scene, ratio / 4, 1);\r\n\r\n            // Create texture adder post-process\r\n            this._createTextureAdderPostProcess(scene, ratio);\r\n\r\n            // Create depth-of-field source post-process\r\n            this.textureAdderFinalPostProcess = new PostProcess(\r\n                \"HDRDepthOfFieldSource\",\r\n                \"standard\",\r\n                [],\r\n                [],\r\n                ratio,\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                scene.getEngine(),\r\n                false,\r\n                \"#define PASS_POST_PROCESS\",\r\n                Constants.TEXTURETYPE_UNSIGNED_INT\r\n            );\r\n            this.addEffect(\r\n                new PostProcessRenderEffect(\r\n                    scene.getEngine(),\r\n                    \"HDRBaseDepthOfFieldSource\",\r\n                    () => {\r\n                        return this.textureAdderFinalPostProcess;\r\n                    },\r\n                    true\r\n                )\r\n            );\r\n        }\r\n\r\n        if (this._vlsEnabled) {\r\n            // Create volumetric light\r\n            this._createVolumetricLightPostProcess(scene, ratio);\r\n\r\n            // Create volumetric light final post-process\r\n            this.volumetricLightFinalPostProcess = new PostProcess(\r\n                \"HDRVLSFinal\",\r\n                \"standard\",\r\n                [],\r\n                [],\r\n                ratio,\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                scene.getEngine(),\r\n                false,\r\n                \"#define PASS_POST_PROCESS\",\r\n                Constants.TEXTURETYPE_UNSIGNED_INT\r\n            );\r\n            this.addEffect(\r\n                new PostProcessRenderEffect(\r\n                    scene.getEngine(),\r\n                    \"HDRVLSFinal\",\r\n                    () => {\r\n                        return this.volumetricLightFinalPostProcess;\r\n                    },\r\n                    true\r\n                )\r\n            );\r\n        }\r\n\r\n        if (this._lensFlareEnabled) {\r\n            // Create lens flare post-process\r\n            this._createLensFlarePostProcess(scene, ratio);\r\n\r\n            // Create depth-of-field source post-process post lens-flare and disable it now\r\n            this.lensFlareFinalPostProcess = new PostProcess(\r\n                \"HDRPostLensFlareDepthOfFieldSource\",\r\n                \"standard\",\r\n                [],\r\n                [],\r\n                ratio,\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                scene.getEngine(),\r\n                false,\r\n                \"#define PASS_POST_PROCESS\",\r\n                Constants.TEXTURETYPE_UNSIGNED_INT\r\n            );\r\n            this.addEffect(\r\n                new PostProcessRenderEffect(\r\n                    scene.getEngine(),\r\n                    \"HDRPostLensFlareDepthOfFieldSource\",\r\n                    () => {\r\n                        return this.lensFlareFinalPostProcess;\r\n                    },\r\n                    true\r\n                )\r\n            );\r\n        }\r\n\r\n        if (this._hdrEnabled) {\r\n            // Create luminance\r\n            this._createLuminancePostProcesses(scene, this._floatTextureType);\r\n\r\n            // Create HDR\r\n            this._createHdrPostProcess(scene, ratio);\r\n\r\n            // Create depth-of-field source post-process post hdr and disable it now\r\n            this.hdrFinalPostProcess = new PostProcess(\r\n                \"HDRPostHDReDepthOfFieldSource\",\r\n                \"standard\",\r\n                [],\r\n                [],\r\n                ratio,\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                scene.getEngine(),\r\n                false,\r\n                \"#define PASS_POST_PROCESS\",\r\n                Constants.TEXTURETYPE_UNSIGNED_INT\r\n            );\r\n            this.addEffect(\r\n                new PostProcessRenderEffect(\r\n                    scene.getEngine(),\r\n                    \"HDRPostHDReDepthOfFieldSource\",\r\n                    () => {\r\n                        return this.hdrFinalPostProcess;\r\n                    },\r\n                    true\r\n                )\r\n            );\r\n        }\r\n\r\n        if (this._depthOfFieldEnabled) {\r\n            // Create gaussian blur used by depth-of-field\r\n            this._createBlurPostProcesses(scene, ratio / 2, 3, \"depthOfFieldBlurWidth\");\r\n\r\n            // Create depth-of-field post-process\r\n            this._createDepthOfFieldPostProcess(scene, ratio);\r\n        }\r\n\r\n        if (this._motionBlurEnabled) {\r\n            // Create motion blur post-process\r\n            this._createMotionBlurPostProcess(scene, ratio);\r\n        }\r\n\r\n        if (this._fxaaEnabled) {\r\n            // Create fxaa post-process\r\n            this.fxaaPostProcess = new FxaaPostProcess(\"fxaa\", 1.0, null, Texture.BILINEAR_SAMPLINGMODE, scene.getEngine(), false, Constants.TEXTURETYPE_UNSIGNED_INT);\r\n            this.addEffect(\r\n                new PostProcessRenderEffect(\r\n                    scene.getEngine(),\r\n                    \"HDRFxaa\",\r\n                    () => {\r\n                        return this.fxaaPostProcess;\r\n                    },\r\n                    true\r\n                )\r\n            );\r\n        }\r\n\r\n        if (this._cameras !== null) {\r\n            this._scene.postProcessRenderPipelineManager.attachCamerasToRenderPipeline(this._name, this._cameras);\r\n        }\r\n\r\n        if (!this._enableMSAAOnFirstPostProcess(this._samples) && this._samples > 1) {\r\n            Logger.Warn(\"MSAA failed to enable, MSAA is only supported in browsers that support webGL >= 2.0\");\r\n        }\r\n    }\r\n\r\n    // Down Sample X4 Post-Process\r\n    private _createDownSampleX4PostProcess(scene: Scene, ratio: number): void {\r\n        const downSampleX4Offsets = new Array<number>(32);\r\n        this.downSampleX4PostProcess = new PostProcess(\r\n            \"HDRDownSampleX4\",\r\n            \"standard\",\r\n            [\"dsOffsets\"],\r\n            [],\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            scene.getEngine(),\r\n            false,\r\n            \"#define DOWN_SAMPLE_X4\",\r\n            this._floatTextureType\r\n        );\r\n\r\n        this.downSampleX4PostProcess.onApply = (effect: Effect) => {\r\n            let id = 0;\r\n            const width = (<PostProcess>this.downSampleX4PostProcess).width;\r\n            const height = (<PostProcess>this.downSampleX4PostProcess).height;\r\n\r\n            for (let i = -2; i < 2; i++) {\r\n                for (let j = -2; j < 2; j++) {\r\n                    downSampleX4Offsets[id] = (i + 0.5) * (1.0 / width);\r\n                    downSampleX4Offsets[id + 1] = (j + 0.5) * (1.0 / height);\r\n                    id += 2;\r\n                }\r\n            }\r\n\r\n            effect.setArray2(\"dsOffsets\", downSampleX4Offsets);\r\n        };\r\n\r\n        // Add to pipeline\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                \"HDRDownSampleX4\",\r\n                () => {\r\n                    return this.downSampleX4PostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n    }\r\n\r\n    // Brightpass Post-Process\r\n    private _createBrightPassPostProcess(scene: Scene, ratio: number): void {\r\n        const brightOffsets = new Array<number>(8);\r\n        this.brightPassPostProcess = new PostProcess(\r\n            \"HDRBrightPass\",\r\n            \"standard\",\r\n            [\"dsOffsets\", \"brightThreshold\"],\r\n            [],\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            scene.getEngine(),\r\n            false,\r\n            \"#define BRIGHT_PASS\",\r\n            this._floatTextureType\r\n        );\r\n\r\n        this.brightPassPostProcess.onApply = (effect: Effect) => {\r\n            const sU = 1.0 / (<PostProcess>this.brightPassPostProcess).width;\r\n            const sV = 1.0 / (<PostProcess>this.brightPassPostProcess).height;\r\n\r\n            brightOffsets[0] = -0.5 * sU;\r\n            brightOffsets[1] = 0.5 * sV;\r\n            brightOffsets[2] = 0.5 * sU;\r\n            brightOffsets[3] = 0.5 * sV;\r\n            brightOffsets[4] = -0.5 * sU;\r\n            brightOffsets[5] = -0.5 * sV;\r\n            brightOffsets[6] = 0.5 * sU;\r\n            brightOffsets[7] = -0.5 * sV;\r\n\r\n            effect.setArray2(\"dsOffsets\", brightOffsets);\r\n            effect.setFloat(\"brightThreshold\", this.brightThreshold);\r\n        };\r\n\r\n        // Add to pipeline\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                \"HDRBrightPass\",\r\n                () => {\r\n                    return this.brightPassPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n    }\r\n\r\n    // Create blur H&V post-processes\r\n    private _createBlurPostProcesses(scene: Scene, ratio: number, indice: number, blurWidthKey: string = \"blurWidth\"): void {\r\n        const engine = scene.getEngine();\r\n\r\n        const blurX = new BlurPostProcess(\r\n            \"HDRBlurH\" + \"_\" + indice,\r\n            new Vector2(1, 0),\r\n            (<any>this)[blurWidthKey],\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            scene.getEngine(),\r\n            false,\r\n            this._floatTextureType\r\n        );\r\n        const blurY = new BlurPostProcess(\r\n            \"HDRBlurV\" + \"_\" + indice,\r\n            new Vector2(0, 1),\r\n            (<any>this)[blurWidthKey],\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            scene.getEngine(),\r\n            false,\r\n            this._floatTextureType\r\n        );\r\n\r\n        blurX.onActivateObservable.add(() => {\r\n            const dw = blurX.width / engine.getRenderWidth();\r\n            blurX.kernel = (<any>this)[blurWidthKey] * dw;\r\n        });\r\n\r\n        blurY.onActivateObservable.add(() => {\r\n            const dw = blurY.height / engine.getRenderHeight();\r\n            blurY.kernel = this.horizontalBlur ? 64 * dw : (<any>this)[blurWidthKey] * dw;\r\n        });\r\n\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                \"HDRBlurH\" + indice,\r\n                () => {\r\n                    return blurX;\r\n                },\r\n                true\r\n            )\r\n        );\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                \"HDRBlurV\" + indice,\r\n                () => {\r\n                    return blurY;\r\n                },\r\n                true\r\n            )\r\n        );\r\n\r\n        this.blurHPostProcesses.push(blurX);\r\n        this.blurVPostProcesses.push(blurY);\r\n    }\r\n\r\n    // Create texture adder post-process\r\n    private _createTextureAdderPostProcess(scene: Scene, ratio: number): void {\r\n        this.textureAdderPostProcess = new PostProcess(\r\n            \"HDRTextureAdder\",\r\n            \"standard\",\r\n            [\"exposure\"],\r\n            [\"otherSampler\", \"lensSampler\"],\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            scene.getEngine(),\r\n            false,\r\n            \"#define TEXTURE_ADDER\",\r\n            this._floatTextureType\r\n        );\r\n        this.textureAdderPostProcess.onApply = (effect: Effect) => {\r\n            effect.setTextureFromPostProcess(\"otherSampler\", this._vlsEnabled ? this._currentDepthOfFieldSource : this.originalPostProcess);\r\n            effect.setTexture(\"lensSampler\", this.lensTexture);\r\n\r\n            effect.setFloat(\"exposure\", this._currentExposure);\r\n\r\n            this._currentDepthOfFieldSource = this.textureAdderFinalPostProcess;\r\n        };\r\n\r\n        // Add to pipeline\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                \"HDRTextureAdder\",\r\n                () => {\r\n                    return this.textureAdderPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n    }\r\n\r\n    private _createVolumetricLightPostProcess(scene: Scene, ratio: number): void {\r\n        const geometryRenderer = <GeometryBufferRenderer>scene.enableGeometryBufferRenderer();\r\n        geometryRenderer.enablePosition = true;\r\n\r\n        const geometry = geometryRenderer.getGBuffer();\r\n\r\n        // Base post-process\r\n        this.volumetricLightPostProcess = new PostProcess(\r\n            \"HDRVLS\",\r\n            \"standard\",\r\n            [\"shadowViewProjection\", \"cameraPosition\", \"sunDirection\", \"sunColor\", \"scatteringCoefficient\", \"scatteringPower\", \"depthValues\"],\r\n            [\"shadowMapSampler\", \"positionSampler\"],\r\n            ratio / 8,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            scene.getEngine(),\r\n            false,\r\n            \"#define VLS\\n#define NB_STEPS \" + this._volumetricLightStepsCount.toFixed(1)\r\n        );\r\n\r\n        const depthValues = Vector2.Zero();\r\n\r\n        this.volumetricLightPostProcess.onApply = (effect: Effect) => {\r\n            if (this.sourceLight && this.sourceLight.getShadowGenerator() && this._scene.activeCamera) {\r\n                const generator = this.sourceLight.getShadowGenerator()!;\r\n\r\n                effect.setTexture(\"shadowMapSampler\", generator.getShadowMap());\r\n                effect.setTexture(\"positionSampler\", geometry.textures[2]);\r\n\r\n                effect.setColor3(\"sunColor\", this.sourceLight.diffuse);\r\n                effect.setVector3(\"sunDirection\", this.sourceLight.getShadowDirection());\r\n\r\n                effect.setVector3(\"cameraPosition\", this._scene.activeCamera.globalPosition);\r\n                effect.setMatrix(\"shadowViewProjection\", generator.getTransformMatrix());\r\n\r\n                effect.setFloat(\"scatteringCoefficient\", this.volumetricLightCoefficient);\r\n                effect.setFloat(\"scatteringPower\", this.volumetricLightPower);\r\n\r\n                depthValues.x = this.sourceLight.getDepthMinZ(this._scene.activeCamera);\r\n                depthValues.y = this.sourceLight.getDepthMaxZ(this._scene.activeCamera);\r\n                effect.setVector2(\"depthValues\", depthValues);\r\n            }\r\n        };\r\n\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                \"HDRVLS\",\r\n                () => {\r\n                    return this.volumetricLightPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n\r\n        // Smooth\r\n        this._createBlurPostProcesses(scene, ratio / 4, 0, \"volumetricLightBlurScale\");\r\n\r\n        // Merge\r\n        this.volumetricLightMergePostProces = new PostProcess(\r\n            \"HDRVLSMerge\",\r\n            \"standard\",\r\n            [],\r\n            [\"originalSampler\"],\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            scene.getEngine(),\r\n            false,\r\n            \"#define VLSMERGE\"\r\n        );\r\n\r\n        this.volumetricLightMergePostProces.onApply = (effect: Effect) => {\r\n            effect.setTextureFromPostProcess(\"originalSampler\", this._bloomEnabled ? this.textureAdderFinalPostProcess : this.originalPostProcess);\r\n\r\n            this._currentDepthOfFieldSource = this.volumetricLightFinalPostProcess;\r\n        };\r\n\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                \"HDRVLSMerge\",\r\n                () => {\r\n                    return this.volumetricLightMergePostProces;\r\n                },\r\n                true\r\n            )\r\n        );\r\n    }\r\n\r\n    // Create luminance\r\n    private _createLuminancePostProcesses(scene: Scene, textureType: number): void {\r\n        // Create luminance\r\n        let size = Math.pow(3, StandardRenderingPipeline.LuminanceSteps);\r\n        this.luminancePostProcess = new PostProcess(\r\n            \"HDRLuminance\",\r\n            \"standard\",\r\n            [\"lumOffsets\"],\r\n            [],\r\n            { width: size, height: size },\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            scene.getEngine(),\r\n            false,\r\n            \"#define LUMINANCE\",\r\n            textureType\r\n        );\r\n\r\n        const offsets: number[] = [];\r\n        this.luminancePostProcess.onApply = (effect: Effect) => {\r\n            const sU = 1.0 / (<PostProcess>this.luminancePostProcess).width;\r\n            const sV = 1.0 / (<PostProcess>this.luminancePostProcess).height;\r\n\r\n            offsets[0] = -0.5 * sU;\r\n            offsets[1] = 0.5 * sV;\r\n            offsets[2] = 0.5 * sU;\r\n            offsets[3] = 0.5 * sV;\r\n            offsets[4] = -0.5 * sU;\r\n            offsets[5] = -0.5 * sV;\r\n            offsets[6] = 0.5 * sU;\r\n            offsets[7] = -0.5 * sV;\r\n\r\n            effect.setArray2(\"lumOffsets\", offsets);\r\n        };\r\n\r\n        // Add to pipeline\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                \"HDRLuminance\",\r\n                () => {\r\n                    return this.luminancePostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n\r\n        // Create down sample luminance\r\n        for (let i = StandardRenderingPipeline.LuminanceSteps - 1; i >= 0; i--) {\r\n            size = Math.pow(3, i);\r\n\r\n            let defines = \"#define LUMINANCE_DOWN_SAMPLE\\n\";\r\n            if (i === 0) {\r\n                defines += \"#define FINAL_DOWN_SAMPLER\";\r\n            }\r\n\r\n            const postProcess = new PostProcess(\r\n                \"HDRLuminanceDownSample\" + i,\r\n                \"standard\",\r\n                [\"dsOffsets\", \"halfDestPixelSize\"],\r\n                [],\r\n                { width: size, height: size },\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                scene.getEngine(),\r\n                false,\r\n                defines,\r\n                textureType\r\n            );\r\n            this.luminanceDownSamplePostProcesses.push(postProcess);\r\n        }\r\n\r\n        // Create callbacks and add effects\r\n        let lastLuminance: Nullable<PostProcess> = this.luminancePostProcess;\r\n\r\n        this.luminanceDownSamplePostProcesses.forEach((pp, index) => {\r\n            const downSampleOffsets = new Array<number>(18);\r\n\r\n            pp.onApply = (effect: Effect) => {\r\n                if (!lastLuminance) {\r\n                    return;\r\n                }\r\n\r\n                let id = 0;\r\n                for (let x = -1; x < 2; x++) {\r\n                    for (let y = -1; y < 2; y++) {\r\n                        downSampleOffsets[id] = x / lastLuminance.width;\r\n                        downSampleOffsets[id + 1] = y / lastLuminance.height;\r\n                        id += 2;\r\n                    }\r\n                }\r\n\r\n                effect.setArray2(\"dsOffsets\", downSampleOffsets);\r\n                effect.setFloat(\"halfDestPixelSize\", 0.5 / lastLuminance.width);\r\n\r\n                if (index === this.luminanceDownSamplePostProcesses.length - 1) {\r\n                    lastLuminance = this.luminancePostProcess;\r\n                } else {\r\n                    lastLuminance = pp;\r\n                }\r\n            };\r\n\r\n            if (index === this.luminanceDownSamplePostProcesses.length - 1) {\r\n                pp.onAfterRender = () => {\r\n                    const pixel = scene.getEngine().readPixels(0, 0, 1, 1);\r\n                    const bit_shift = new Vector4(1.0 / (255.0 * 255.0 * 255.0), 1.0 / (255.0 * 255.0), 1.0 / 255.0, 1.0);\r\n                    pixel.then((pixel) => {\r\n                        const data = new Uint8Array(pixel.buffer);\r\n                        this._hdrCurrentLuminance = (data[0] * bit_shift.x + data[1] * bit_shift.y + data[2] * bit_shift.z + data[3] * bit_shift.w) / 100.0;\r\n                    });\r\n                };\r\n            }\r\n\r\n            this.addEffect(\r\n                new PostProcessRenderEffect(\r\n                    scene.getEngine(),\r\n                    \"HDRLuminanceDownSample\" + index,\r\n                    () => {\r\n                        return pp;\r\n                    },\r\n                    true\r\n                )\r\n            );\r\n        });\r\n    }\r\n\r\n    // Create HDR post-process\r\n    private _createHdrPostProcess(scene: Scene, ratio: number): void {\r\n        const defines = [\"#define HDR\"];\r\n        if (this._hdrAutoExposure) {\r\n            defines.push(\"#define AUTO_EXPOSURE\");\r\n        }\r\n        this.hdrPostProcess = new PostProcess(\r\n            \"HDR\",\r\n            \"standard\",\r\n            [\"averageLuminance\"],\r\n            [\"textureAdderSampler\"],\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            scene.getEngine(),\r\n            false,\r\n            defines.join(\"\\n\"),\r\n            Constants.TEXTURETYPE_UNSIGNED_INT\r\n        );\r\n\r\n        let outputLiminance = 1;\r\n        let time = 0;\r\n        let lastTime = 0;\r\n\r\n        this.hdrPostProcess.onApply = (effect: Effect) => {\r\n            effect.setTextureFromPostProcess(\"textureAdderSampler\", this._currentDepthOfFieldSource);\r\n\r\n            time += scene.getEngine().getDeltaTime();\r\n\r\n            if (outputLiminance < 0) {\r\n                outputLiminance = this._hdrCurrentLuminance;\r\n            } else {\r\n                const dt = (lastTime - time) / 1000.0;\r\n\r\n                if (this._hdrCurrentLuminance < outputLiminance + this.hdrDecreaseRate * dt) {\r\n                    outputLiminance += this.hdrDecreaseRate * dt;\r\n                } else if (this._hdrCurrentLuminance > outputLiminance - this.hdrIncreaseRate * dt) {\r\n                    outputLiminance -= this.hdrIncreaseRate * dt;\r\n                } else {\r\n                    outputLiminance = this._hdrCurrentLuminance;\r\n                }\r\n            }\r\n\r\n            if (this.hdrAutoExposure) {\r\n                this._currentExposure = this._fixedExposure / outputLiminance;\r\n            } else {\r\n                outputLiminance = Scalar.Clamp(outputLiminance, this.hdrMinimumLuminance, 1e20);\r\n                effect.setFloat(\"averageLuminance\", outputLiminance);\r\n            }\r\n\r\n            lastTime = time;\r\n\r\n            this._currentDepthOfFieldSource = this.hdrFinalPostProcess;\r\n        };\r\n\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                \"HDR\",\r\n                () => {\r\n                    return this.hdrPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n    }\r\n\r\n    // Create lens flare post-process\r\n    private _createLensFlarePostProcess(scene: Scene, ratio: number): void {\r\n        this.lensFlarePostProcess = new PostProcess(\r\n            \"HDRLensFlare\",\r\n            \"standard\",\r\n            [\"strength\", \"ghostDispersal\", \"haloWidth\", \"resolution\", \"distortionStrength\"],\r\n            [\"lensColorSampler\"],\r\n            ratio / 2,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            scene.getEngine(),\r\n            false,\r\n            \"#define LENS_FLARE\",\r\n            Constants.TEXTURETYPE_UNSIGNED_INT\r\n        );\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                \"HDRLensFlare\",\r\n                () => {\r\n                    return this.lensFlarePostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n\r\n        this._createBlurPostProcesses(scene, ratio / 4, 2, \"lensFlareBlurWidth\");\r\n\r\n        this.lensFlareComposePostProcess = new PostProcess(\r\n            \"HDRLensFlareCompose\",\r\n            \"standard\",\r\n            [\"lensStarMatrix\"],\r\n            [\"otherSampler\", \"lensDirtSampler\", \"lensStarSampler\"],\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            scene.getEngine(),\r\n            false,\r\n            \"#define LENS_FLARE_COMPOSE\",\r\n            Constants.TEXTURETYPE_UNSIGNED_INT\r\n        );\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                \"HDRLensFlareCompose\",\r\n                () => {\r\n                    return this.lensFlareComposePostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n\r\n        const resolution = new Vector2(0, 0);\r\n\r\n        // Lens flare\r\n        this.lensFlarePostProcess.externalTextureSamplerBinding = true;\r\n        this.lensFlarePostProcess.onApply = (effect: Effect) => {\r\n            effect.setTextureFromPostProcess(\"textureSampler\", this._bloomEnabled ? this.blurHPostProcesses[0] : this.originalPostProcess);\r\n            effect.setTexture(\"lensColorSampler\", this.lensColorTexture);\r\n            effect.setFloat(\"strength\", this.lensFlareStrength);\r\n            effect.setFloat(\"ghostDispersal\", this.lensFlareGhostDispersal);\r\n            effect.setFloat(\"haloWidth\", this.lensFlareHaloWidth);\r\n\r\n            // Shift\r\n            resolution.x = (<PostProcess>this.lensFlarePostProcess).width;\r\n            resolution.y = (<PostProcess>this.lensFlarePostProcess).height;\r\n            effect.setVector2(\"resolution\", resolution);\r\n\r\n            effect.setFloat(\"distortionStrength\", this.lensFlareDistortionStrength);\r\n        };\r\n\r\n        // Compose\r\n        const scaleBias1 = Matrix.FromValues(2.0, 0.0, -1.0, 0.0, 0.0, 2.0, -1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0);\r\n\r\n        const scaleBias2 = Matrix.FromValues(0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0);\r\n\r\n        this.lensFlareComposePostProcess.onApply = (effect: Effect) => {\r\n            if (!this._scene.activeCamera) {\r\n                return;\r\n            }\r\n\r\n            effect.setTextureFromPostProcess(\"otherSampler\", this.lensFlarePostProcess);\r\n            effect.setTexture(\"lensDirtSampler\", this.lensFlareDirtTexture);\r\n            effect.setTexture(\"lensStarSampler\", this.lensStarTexture);\r\n\r\n            // Lens start rotation matrix\r\n            const camerax = <Vector4>this._scene.activeCamera.getViewMatrix().getRow(0);\r\n            const cameraz = <Vector4>this._scene.activeCamera.getViewMatrix().getRow(2);\r\n            let camRot = Vector3.Dot(camerax.toVector3(), new Vector3(1.0, 0.0, 0.0)) + Vector3.Dot(cameraz.toVector3(), new Vector3(0.0, 0.0, 1.0));\r\n            camRot *= 4.0;\r\n\r\n            const starRotation = Matrix.FromValues(\r\n                Math.cos(camRot) * 0.5,\r\n                -Math.sin(camRot),\r\n                0.0,\r\n                0.0,\r\n                Math.sin(camRot),\r\n                Math.cos(camRot) * 0.5,\r\n                0.0,\r\n                0.0,\r\n                0.0,\r\n                0.0,\r\n                1.0,\r\n                0.0,\r\n                0.0,\r\n                0.0,\r\n                0.0,\r\n                1.0\r\n            );\r\n\r\n            const lensStarMatrix = scaleBias2.multiply(starRotation).multiply(scaleBias1);\r\n\r\n            effect.setMatrix(\"lensStarMatrix\", lensStarMatrix);\r\n\r\n            this._currentDepthOfFieldSource = this.lensFlareFinalPostProcess;\r\n        };\r\n    }\r\n\r\n    // Create depth-of-field post-process\r\n    private _createDepthOfFieldPostProcess(scene: Scene, ratio: number): void {\r\n        this.depthOfFieldPostProcess = new PostProcess(\r\n            \"HDRDepthOfField\",\r\n            \"standard\",\r\n            [\"distance\"],\r\n            [\"otherSampler\", \"depthSampler\"],\r\n            ratio,\r\n            null,\r\n            Texture.BILINEAR_SAMPLINGMODE,\r\n            scene.getEngine(),\r\n            false,\r\n            \"#define DEPTH_OF_FIELD\",\r\n            Constants.TEXTURETYPE_UNSIGNED_INT\r\n        );\r\n        this.depthOfFieldPostProcess.onApply = (effect: Effect) => {\r\n            effect.setTextureFromPostProcess(\"otherSampler\", this._currentDepthOfFieldSource);\r\n            effect.setTexture(\"depthSampler\", this._getDepthTexture());\r\n\r\n            effect.setFloat(\"distance\", this.depthOfFieldDistance);\r\n        };\r\n\r\n        // Add to pipeline\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                \"HDRDepthOfField\",\r\n                () => {\r\n                    return this.depthOfFieldPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n    }\r\n\r\n    // Create motion blur post-process\r\n    private _createMotionBlurPostProcess(scene: Scene, ratio: number): void {\r\n        if (this._isObjectBasedMotionBlur) {\r\n            const mb = new MotionBlurPostProcess(\"HDRMotionBlur\", scene, ratio, null, Texture.BILINEAR_SAMPLINGMODE, scene.getEngine(), false, Constants.TEXTURETYPE_UNSIGNED_INT);\r\n            mb.motionStrength = this.motionStrength;\r\n            mb.motionBlurSamples = this.motionBlurSamples;\r\n            this.motionBlurPostProcess = mb;\r\n        } else {\r\n            this.motionBlurPostProcess = new PostProcess(\r\n                \"HDRMotionBlur\",\r\n                \"standard\",\r\n                [\"inverseViewProjection\", \"prevViewProjection\", \"screenSize\", \"motionScale\", \"motionStrength\"],\r\n                [\"depthSampler\"],\r\n                ratio,\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                scene.getEngine(),\r\n                false,\r\n                \"#define MOTION_BLUR\\n#define MAX_MOTION_SAMPLES \" + this.motionBlurSamples.toFixed(1),\r\n                Constants.TEXTURETYPE_UNSIGNED_INT\r\n            );\r\n\r\n            let motionScale: number = 0;\r\n            let prevViewProjection = Matrix.Identity();\r\n            const invViewProjection = Matrix.Identity();\r\n            let viewProjection = Matrix.Identity();\r\n            const screenSize = Vector2.Zero();\r\n\r\n            this.motionBlurPostProcess.onApply = (effect: Effect) => {\r\n                viewProjection = scene.getProjectionMatrix().multiply(scene.getViewMatrix());\r\n\r\n                viewProjection.invertToRef(invViewProjection);\r\n                effect.setMatrix(\"inverseViewProjection\", invViewProjection);\r\n\r\n                effect.setMatrix(\"prevViewProjection\", prevViewProjection);\r\n                prevViewProjection = viewProjection;\r\n\r\n                screenSize.x = (<PostProcess>this.motionBlurPostProcess).width;\r\n                screenSize.y = (<PostProcess>this.motionBlurPostProcess).height;\r\n                effect.setVector2(\"screenSize\", screenSize);\r\n\r\n                motionScale = scene.getEngine().getFps() / 60.0;\r\n                effect.setFloat(\"motionScale\", motionScale);\r\n                effect.setFloat(\"motionStrength\", this.motionStrength);\r\n\r\n                effect.setTexture(\"depthSampler\", this._getDepthTexture());\r\n            };\r\n        }\r\n\r\n        this.addEffect(\r\n            new PostProcessRenderEffect(\r\n                scene.getEngine(),\r\n                \"HDRMotionBlur\",\r\n                () => {\r\n                    return this.motionBlurPostProcess;\r\n                },\r\n                true\r\n            )\r\n        );\r\n    }\r\n\r\n    private _getDepthTexture(): Texture {\r\n        if (this._scene.getEngine().getCaps().drawBuffersExtension) {\r\n            const renderer = <GeometryBufferRenderer>this._scene.enableGeometryBufferRenderer();\r\n            return renderer.getGBuffer().textures[0];\r\n        }\r\n\r\n        return this._scene.enableDepthRenderer().getDepthMap();\r\n    }\r\n\r\n    private _disposePostProcesses(): void {\r\n        for (let i = 0; i < this._cameras.length; i++) {\r\n            const camera = this._cameras[i];\r\n\r\n            if (this.originalPostProcess) {\r\n                this.originalPostProcess.dispose(camera);\r\n            }\r\n            if (this.screenSpaceReflectionPostProcess) {\r\n                this.screenSpaceReflectionPostProcess.dispose(camera);\r\n            }\r\n\r\n            if (this.downSampleX4PostProcess) {\r\n                this.downSampleX4PostProcess.dispose(camera);\r\n            }\r\n            if (this.brightPassPostProcess) {\r\n                this.brightPassPostProcess.dispose(camera);\r\n            }\r\n            if (this.textureAdderPostProcess) {\r\n                this.textureAdderPostProcess.dispose(camera);\r\n            }\r\n\r\n            if (this.volumetricLightPostProcess) {\r\n                this.volumetricLightPostProcess.dispose(camera);\r\n            }\r\n            if (this.volumetricLightSmoothXPostProcess) {\r\n                this.volumetricLightSmoothXPostProcess.dispose(camera);\r\n            }\r\n            if (this.volumetricLightSmoothYPostProcess) {\r\n                this.volumetricLightSmoothYPostProcess.dispose(camera);\r\n            }\r\n            if (this.volumetricLightMergePostProces) {\r\n                this.volumetricLightMergePostProces.dispose(camera);\r\n            }\r\n            if (this.volumetricLightFinalPostProcess) {\r\n                this.volumetricLightFinalPostProcess.dispose(camera);\r\n            }\r\n\r\n            if (this.lensFlarePostProcess) {\r\n                this.lensFlarePostProcess.dispose(camera);\r\n            }\r\n            if (this.lensFlareComposePostProcess) {\r\n                this.lensFlareComposePostProcess.dispose(camera);\r\n            }\r\n\r\n            for (let j = 0; j < this.luminanceDownSamplePostProcesses.length; j++) {\r\n                this.luminanceDownSamplePostProcesses[j].dispose(camera);\r\n            }\r\n\r\n            if (this.luminancePostProcess) {\r\n                this.luminancePostProcess.dispose(camera);\r\n            }\r\n            if (this.hdrPostProcess) {\r\n                this.hdrPostProcess.dispose(camera);\r\n            }\r\n            if (this.hdrFinalPostProcess) {\r\n                this.hdrFinalPostProcess.dispose(camera);\r\n            }\r\n\r\n            if (this.depthOfFieldPostProcess) {\r\n                this.depthOfFieldPostProcess.dispose(camera);\r\n            }\r\n\r\n            if (this.motionBlurPostProcess) {\r\n                this.motionBlurPostProcess.dispose(camera);\r\n            }\r\n\r\n            if (this.fxaaPostProcess) {\r\n                this.fxaaPostProcess.dispose(camera);\r\n            }\r\n\r\n            for (let j = 0; j < this.blurHPostProcesses.length; j++) {\r\n                this.blurHPostProcesses[j].dispose(camera);\r\n            }\r\n\r\n            for (let j = 0; j < this.blurVPostProcesses.length; j++) {\r\n                this.blurVPostProcesses[j].dispose(camera);\r\n            }\r\n        }\r\n\r\n        this.originalPostProcess = null;\r\n        this.downSampleX4PostProcess = null;\r\n        this.brightPassPostProcess = null;\r\n        this.textureAdderPostProcess = null;\r\n        this.textureAdderFinalPostProcess = null;\r\n        this.volumetricLightPostProcess = null;\r\n        this.volumetricLightSmoothXPostProcess = null;\r\n        this.volumetricLightSmoothYPostProcess = null;\r\n        this.volumetricLightMergePostProces = null;\r\n        this.volumetricLightFinalPostProcess = null;\r\n        this.lensFlarePostProcess = null;\r\n        this.lensFlareComposePostProcess = null;\r\n        this.luminancePostProcess = null;\r\n        this.hdrPostProcess = null;\r\n        this.hdrFinalPostProcess = null;\r\n        this.depthOfFieldPostProcess = null;\r\n        this.motionBlurPostProcess = null;\r\n        this.fxaaPostProcess = null;\r\n        this.screenSpaceReflectionPostProcess = null;\r\n\r\n        this.luminanceDownSamplePostProcesses.length = 0;\r\n        this.blurHPostProcesses.length = 0;\r\n        this.blurVPostProcesses.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Dispose of the pipeline and stop all post processes\r\n     */\r\n    public dispose(): void {\r\n        this._disposePostProcesses();\r\n\r\n        this._scene.postProcessRenderPipelineManager.detachCamerasFromRenderPipeline(this._name, this._cameras);\r\n\r\n        super.dispose();\r\n    }\r\n\r\n    /**\r\n     * Serialize the rendering pipeline (Used when exporting)\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n\r\n        if (this.sourceLight) {\r\n            serializationObject.sourceLightId = this.sourceLight.id;\r\n        }\r\n\r\n        if (this.screenSpaceReflectionPostProcess) {\r\n            serializationObject.screenSpaceReflectionPostProcess = SerializationHelper.Serialize(this.screenSpaceReflectionPostProcess);\r\n        }\r\n\r\n        serializationObject.customType = \"StandardRenderingPipeline\";\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parse the serialized pipeline\r\n     * @param source Source pipeline.\r\n     * @param scene The scene to load the pipeline to.\r\n     * @param rootUrl The URL of the serialized pipeline.\r\n     * @returns An instantiated pipeline from the serialized object.\r\n     */\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): StandardRenderingPipeline {\r\n        const p = SerializationHelper.Parse(() => new StandardRenderingPipeline(source._name, scene, source._ratio), source, scene, rootUrl);\r\n\r\n        if (source.sourceLightId) {\r\n            p.sourceLight = <SpotLight | DirectionalLight>scene.getLightById(source.sourceLightId);\r\n        }\r\n\r\n        if (source.screenSpaceReflectionPostProcess) {\r\n            SerializationHelper.Parse(() => p.screenSpaceReflectionPostProcess, source.screenSpaceReflectionPostProcess, scene, rootUrl);\r\n        }\r\n\r\n        return p;\r\n    }\r\n\r\n    /**\r\n     * Luminance steps\r\n     */\r\n    public static LuminanceSteps: number = 6;\r\n}\r\n\r\nRegisterClass(\"BABYLON.StandardRenderingPipeline\", StandardRenderingPipeline);\r\n"]}