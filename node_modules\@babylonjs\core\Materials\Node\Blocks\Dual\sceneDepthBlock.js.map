{"version": 3, "file": "sceneDepthBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Dual/sceneDepthBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAGhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAG3D,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAKtG;;;GAGG;AACH,MAAM,OAAO,eAAgB,SAAQ,iBAAiB;IAyDlD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QAzD5D;;WAEG;QAkBI,sBAAiB,GAAG,KAAK,CAAC;QAEjC;;WAEG;QAkBI,sBAAiB,GAAG,KAAK,CAAC;QAEjC;;WAEG;QAII,oBAAe,GAAG,KAAK,CAAC;QAS3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,qCAAqC,CAAC,UAAU,EAAE,KAAK,EAAE,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QAE9H,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAE5G,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,CACtD,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,GAAG,qCAAqC,CAAC,OAAO,CAChJ,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,EAAE;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,KAA6B;QAC3C,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;IACjD,CAAC;IAED,IAAW,MAAM;QACb,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE;YACtB,OAAO,wBAAwB,CAAC,iBAAiB,CAAC;SACrD;QAED,IAAI,IAAI,CAAC,EAAE,CAAC,WAAY,CAAC,OAAO,EAAE;YAC9B,OAAO,wBAAwB,CAAC,iBAAiB,CAAC;SACrD;QAED,OAAO,wBAAwB,CAAC,QAAQ,CAAC;IAC7C,CAAC;IAEO,WAAW,CAAC,KAAY;QAC5B,MAAM,aAAa,GAAG,KAAK,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,EAAE,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAE5I,OAAO,aAAa,CAAC,WAAW,EAAE,CAAC;IACvC,CAAC;IAEM,IAAI,CAAC,MAAc,EAAE,YAA0B;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE1D,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAEO,iBAAiB,CAAC,KAA6B;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC;QAExB,IAAI,OAAO,CAAC,cAAe,CAAC,UAAU,CAAC,OAAO,EAAE;YAC5C,MAAM,iBAAiB,GAAG,OAAO,CAAC,cAAe,CAAC,UAAwB,CAAC;YAE3E,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;gBAChC,KAAK,CAAC,sBAAsB,CACxB,OAAO,CAAC,sBAAsB,EAC9B,KAAK,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK,qCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,qCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAC9J,CAAC;aACL;SACJ;QAED,IAAI,CAAC,WAAW,GAAG,OAAO,GAAG,OAAO,CAAC,sBAAsB,CAAC;QAE5D,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAEvD,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,WAAW,MAAM,OAAO,CAAC,sBAAsB,QAAQ,CAAC;QAE3F,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,yBAAyB,CAAC,EAAE;YACzD,OAAO;SACV;QAED,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAEpC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,MAAM,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;aAC/C;SACJ;IACL,CAAC;IAEO,iBAAiB,CAAC,KAA6B,EAAE,UAAU,GAAG,KAAK;QACvE,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC;QAExB,IAAI,UAAU,EAAE;YACZ,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;gBACpD,OAAO;aACV;YAED,KAAK,CAAC,iBAAiB,IAAI,QAAQ,IAAI,CAAC,gBAAgB,gBAAgB,IAAI,CAAC,YAAY,KAAK,OAAO,CAAC,sBAAsB,SAAS,CAAC;YACtI,OAAO;SACV;QAED,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACjE,KAAK,CAAC,iBAAiB,IAAI,QAAQ,IAAI,CAAC,gBAAgB,gBAAgB,IAAI,CAAC,YAAY,KAAK,OAAO,CAAC,sBAAsB,SAAS,CAAC;YACtI,OAAO;SACV;QAED,KAAK,CAAC,iBAAiB,IAAI,QAAQ,IAAI,CAAC,gBAAgB,gBAAgB,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,WAAW,MAAM,CAAC;IACzH,CAAC;IAEO,YAAY,CAAC,KAA6B,EAAE,MAAmC,EAAE,OAAe,EAAE,UAAU,GAAG,KAAK;QACxH,IAAI,UAAU,EAAE;YACZ,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;gBACpD,OAAO;aACV;YAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC,gBAAgB,IAAI,OAAO,KAAK,CAAC;YAC5G,OAAO;SACV;QAED,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACjE,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC,gBAAgB,IAAI,OAAO,KAAK,CAAC;YAC5G,OAAO;SACV;QAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC,gBAAgB,IAAI,OAAO,KAAK,CAAC;IAChH,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;QACtE,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAEtE,IAAI,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACnD,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC9C;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACpD,SAAS;YACT,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACxC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9B,OAAO;SACV;QAED,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,2BAA2B,CAAC,EAAE;YAC3D,OAAO;SACV;QAED,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAExC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAE9B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,MAAM,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;aACzC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC/D,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC/D,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAE3D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,iBAAiB,GAAG,mBAAmB,CAAC,iBAAiB,CAAC;QAC/D,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;QACjE,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC,eAAe,CAAC;IAC/D,CAAC;CACJ;AArOU;IAjBN,sBAAsB,CAAC,sBAAsB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE;QACxF,SAAS,EAAE;YACP,sBAAsB,EAAE,IAAI;YAC5B,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,eAAe,GAAG,KAAwB,CAAC;gBACjD,IAAI,MAAM,GAAG,KAAK,CAAC;gBACnB,IAAI,eAAe,CAAC,iBAAiB,EAAE;oBACnC,eAAe,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBAC1C,MAAM,GAAG,IAAI,CAAC;iBACjB;gBACD,IAAI,KAAK,EAAE;oBACP,KAAK,CAAC,oBAAoB,EAAE,CAAC;iBAChC;gBACD,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ;KACJ,CAAC;0DAC+B;AAsB1B;IAjBN,sBAAsB,CAAC,sBAAsB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE;QACxF,SAAS,EAAE;YACP,sBAAsB,EAAE,IAAI;YAC5B,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,eAAe,GAAG,KAAwB,CAAC;gBACjD,IAAI,MAAM,GAAG,KAAK,CAAC;gBACnB,IAAI,eAAe,CAAC,iBAAiB,EAAE;oBACnC,eAAe,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBAC1C,MAAM,GAAG,IAAI,CAAC;iBACjB;gBACD,IAAI,KAAK,EAAE;oBACP,KAAK,CAAC,oBAAoB,EAAE,CAAC;iBAChC;gBACD,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ;KACJ,CAAC;0DAC+B;AAQ1B;IAHN,sBAAsB,CAAC,qBAAqB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE;QACvF,SAAS,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,EAAE,oBAAoB,EAAE,EAAE;KAClG,CAAC;wDAC6B;AAyMnC,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { BaseTexture } from \"../../../Textures/baseTexture\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport type { InputBlock } from \"../Input/inputBlock\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport type { Effect } from \"../../../effect\";\r\n\r\nimport type { NodeMaterial } from \"../../nodeMaterial\";\r\n\r\n/**\r\n * Block used to retrieve the depth (zbuffer) of the scene\r\n * @since 5.0.0\r\n */\r\nexport class SceneDepthBlock extends NodeMaterialBlock {\r\n    private _samplerName: string;\r\n    private _mainUVName: string;\r\n    private _tempTextureRead: string;\r\n\r\n    /**\r\n     * Defines if the depth renderer should be setup in non linear mode\r\n     */\r\n    @editableInPropertyPage(\"Use non linear depth\", PropertyTypeForEdition.Boolean, \"ADVANCED\", {\r\n        notifiers: {\r\n            activatePreviewCommand: true,\r\n            callback: (scene, block) => {\r\n                const sceneDepthBlock = block as SceneDepthBlock;\r\n                let retVal = false;\r\n                if (sceneDepthBlock.useNonLinearDepth) {\r\n                    sceneDepthBlock.storeCameraSpaceZ = false;\r\n                    retVal = true;\r\n                }\r\n                if (scene) {\r\n                    scene.disableDepthRenderer();\r\n                }\r\n                return retVal;\r\n            },\r\n        },\r\n    })\r\n    public useNonLinearDepth = false;\r\n\r\n    /**\r\n     * Defines if the depth renderer should be setup in camera space Z mode (if set, useNonLinearDepth has no effect)\r\n     */\r\n    @editableInPropertyPage(\"Store Camera space Z\", PropertyTypeForEdition.Boolean, \"ADVANCED\", {\r\n        notifiers: {\r\n            activatePreviewCommand: true,\r\n            callback: (scene, block) => {\r\n                const sceneDepthBlock = block as SceneDepthBlock;\r\n                let retVal = false;\r\n                if (sceneDepthBlock.storeCameraSpaceZ) {\r\n                    sceneDepthBlock.useNonLinearDepth = false;\r\n                    retVal = true;\r\n                }\r\n                if (scene) {\r\n                    scene.disableDepthRenderer();\r\n                }\r\n                return retVal;\r\n            },\r\n        },\r\n    })\r\n    public storeCameraSpaceZ = false;\r\n\r\n    /**\r\n     * Defines if the depth renderer should be setup in full 32 bits float mode\r\n     */\r\n    @editableInPropertyPage(\"Force 32 bits float\", PropertyTypeForEdition.Boolean, \"ADVANCED\", {\r\n        notifiers: { activatePreviewCommand: true, callback: (scene) => scene?.disableDepthRenderer() },\r\n    })\r\n    public force32itsFloat = false;\r\n\r\n    /**\r\n     * Create a new SceneDepthBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.VertexAndFragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"uv\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false, NodeMaterialBlockTargets.VertexAndFragment);\r\n\r\n        this.registerOutput(\"depth\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this._inputs[0].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Vector2 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n\r\n        this._inputs[0]._prioritizeVertex = false;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"SceneDepthBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the uv input component\r\n     */\r\n    public get uv(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the depth output component\r\n     */\r\n    public get depth(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"textureSampler\");\r\n    }\r\n\r\n    public get target() {\r\n        if (!this.uv.isConnected) {\r\n            return NodeMaterialBlockTargets.VertexAndFragment;\r\n        }\r\n\r\n        if (this.uv.sourceBlock!.isInput) {\r\n            return NodeMaterialBlockTargets.VertexAndFragment;\r\n        }\r\n\r\n        return NodeMaterialBlockTargets.Fragment;\r\n    }\r\n\r\n    private _getTexture(scene: Scene): BaseTexture {\r\n        const depthRenderer = scene.enableDepthRenderer(undefined, this.useNonLinearDepth, this.force32itsFloat, undefined, this.storeCameraSpaceZ);\r\n\r\n        return depthRenderer.getDepthMap();\r\n    }\r\n\r\n    public bind(effect: Effect, nodeMaterial: NodeMaterial) {\r\n        const texture = this._getTexture(nodeMaterial.getScene());\r\n\r\n        effect.setTexture(this._samplerName, texture);\r\n    }\r\n\r\n    private _injectVertexCode(state: NodeMaterialBuildState) {\r\n        const uvInput = this.uv;\r\n\r\n        if (uvInput.connectedPoint!.ownerBlock.isInput) {\r\n            const uvInputOwnerBlock = uvInput.connectedPoint!.ownerBlock as InputBlock;\r\n\r\n            if (!uvInputOwnerBlock.isAttribute) {\r\n                state._emitUniformFromString(\r\n                    uvInput.associatedVariableName,\r\n                    \"vec\" + (uvInput.type === NodeMaterialBlockConnectionPointTypes.Vector3 ? \"3\" : uvInput.type === NodeMaterialBlockConnectionPointTypes.Vector4 ? \"4\" : \"2\")\r\n                );\r\n            }\r\n        }\r\n\r\n        this._mainUVName = \"vMain\" + uvInput.associatedVariableName;\r\n\r\n        state._emitVaryingFromString(this._mainUVName, \"vec2\");\r\n\r\n        state.compilationString += `${this._mainUVName} = ${uvInput.associatedVariableName}.xy;\\n`;\r\n\r\n        if (!this._outputs.some((o) => o.isConnectedInVertexShader)) {\r\n            return;\r\n        }\r\n\r\n        this._writeTextureRead(state, true);\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints) {\r\n                this._writeOutput(state, output, \"r\", true);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _writeTextureRead(state: NodeMaterialBuildState, vertexMode = false) {\r\n        const uvInput = this.uv;\r\n\r\n        if (vertexMode) {\r\n            if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n                return;\r\n            }\r\n\r\n            state.compilationString += `vec4 ${this._tempTextureRead} = texture2D(${this._samplerName}, ${uvInput.associatedVariableName}.xy);\\n`;\r\n            return;\r\n        }\r\n\r\n        if (this.uv.ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += `vec4 ${this._tempTextureRead} = texture2D(${this._samplerName}, ${uvInput.associatedVariableName}.xy);\\n`;\r\n            return;\r\n        }\r\n\r\n        state.compilationString += `vec4 ${this._tempTextureRead} = texture2D(${this._samplerName}, ${this._mainUVName});\\n`;\r\n    }\r\n\r\n    private _writeOutput(state: NodeMaterialBuildState, output: NodeMaterialConnectionPoint, swizzle: string, vertexMode = false) {\r\n        if (vertexMode) {\r\n            if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n                return;\r\n            }\r\n\r\n            state.compilationString += `${this._declareOutput(output, state)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n            return;\r\n        }\r\n\r\n        if (this.uv.ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += `${this._declareOutput(output, state)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n            return;\r\n        }\r\n\r\n        state.compilationString += `${this._declareOutput(output, state)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        this._samplerName = state._getFreeVariableName(this.name + \"Sampler\");\r\n        this._tempTextureRead = state._getFreeVariableName(\"tempTextureRead\");\r\n\r\n        if (state.sharedData.bindableBlocks.indexOf(this) < 0) {\r\n            state.sharedData.bindableBlocks.push(this);\r\n        }\r\n\r\n        if (state.target !== NodeMaterialBlockTargets.Fragment) {\r\n            // Vertex\r\n            state._emit2DSampler(this._samplerName);\r\n            this._injectVertexCode(state);\r\n            return;\r\n        }\r\n\r\n        // Fragment\r\n        if (!this._outputs.some((o) => o.isConnectedInFragmentShader)) {\r\n            return;\r\n        }\r\n\r\n        state._emit2DSampler(this._samplerName);\r\n\r\n        this._writeTextureRead(state);\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints) {\r\n                this._writeOutput(state, output, \"r\");\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.useNonLinearDepth = this.useNonLinearDepth;\r\n        serializationObject.storeCameraSpaceZ = this.storeCameraSpaceZ;\r\n        serializationObject.force32itsFloat = this.force32itsFloat;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.useNonLinearDepth = serializationObject.useNonLinearDepth;\r\n        this.storeCameraSpaceZ = !!serializationObject.storeCameraSpaceZ;\r\n        this.force32itsFloat = serializationObject.force32itsFloat;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SceneDepthBlock\", SceneDepthBlock);\r\n"]}