{"version": 3, "file": "virtualJoystick.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/virtualJoystick.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACxD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEtD,qCAAqC;AACrC,oPAAoP;AACpP,qHAAqH;AAErH;;GAEG;AACH,MAAM,CAAN,IAAY,YAOX;AAPD,WAAY,YAAY;IACpB,aAAa;IACb,yCAAC,CAAA;IACD,aAAa;IACb,yCAAC,CAAA;IACD,aAAa;IACb,yCAAC,CAAA;AACL,CAAC,EAPW,YAAY,KAAZ,YAAY,QAOvB;AAyCD;;GAEG;AACH,MAAM,OAAO,eAAe;IAkChB,MAAM,CAAC,kBAAkB;QAC7B,OAAO;YACH,QAAQ,EAAE,EAAE;YACZ,aAAa,EAAE,EAAE;YACjB,KAAK,EAAE,MAAM;YACb,SAAS,EAAE,SAAS;YACpB,cAAc,EAAE,SAAS;YACzB,QAAQ,EAAE,SAAS;YACnB,aAAa,EAAE,KAAK;YACpB,gBAAgB,EAAE,KAAK;SAC1B,CAAC;IACN,CAAC;IAkCD;;;;OAIG;IACH,YAAY,YAAsB,EAAE,cAAuD;QApBnF,cAAS,GAAG,KAAK,CAAC;QAqBtB,MAAM,OAAO,GAAG;YACZ,GAAG,eAAe,CAAC,kBAAkB,EAAE;YACvC,GAAG,cAAc;SACpB,CAAC;QAEF,IAAI,YAAY,EAAE;YACd,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B;aAAM;YACH,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;SAC9B;QAED,eAAe,CAAC,oBAAoB,EAAE,CAAC;QAEvC,sDAAsD;QACtD,sCAAsC;QACtC,IAAI,CAAC,2BAA2B,GAAG,YAAY,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,wBAAwB,GAAG,YAAY,CAAC,CAAC,CAAC;QAE/C,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,0BAA0B;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,gBAAgB,EAAyE,CAAC;QAC9G,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpC,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,oBAAoB,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;QAEnE,IAAI,CAAC,SAAS,GAAG,GAAG,EAAE;YAClB,eAAe,CAAC,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC;YACnD,eAAe,CAAC,eAAe,GAAG,MAAM,CAAC,WAAW,CAAC;YACrD,IAAI,eAAe,CAAC,MAAM,EAAE;gBACxB,eAAe,CAAC,MAAM,CAAC,KAAK,GAAG,eAAe,CAAC,cAAc,CAAC;gBAC9D,eAAe,CAAC,MAAM,CAAC,MAAM,GAAG,eAAe,CAAC,eAAe,CAAC;aACnE;YACD,eAAe,CAAC,UAAU,GAAG,eAAe,CAAC,cAAc,GAAG,CAAC,CAAC;QACpE,CAAC,CAAC;QAEF,0DAA0D;QAC1D,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;YACzB,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACzD,eAAe,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC1D,eAAe,CAAC,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC;YACnD,eAAe,CAAC,eAAe,GAAG,MAAM,CAAC,WAAW,CAAC;YACrD,eAAe,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC;YACjD,eAAe,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC;YACnD,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;YAC5C,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;YAC7C,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;YACnD,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG,aAAa,CAAC;YAC7D,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;YACzC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;YAC1C,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;YAC1C,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC,yGAAyG;YAC5J,kCAAkC;YAClC,eAAe,CAAC,MAAM,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAC5D,MAAM,OAAO,GAAG,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAExD,IAAI,CAAC,OAAO,EAAE;gBACV,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;aACnE;YAED,eAAe,CAAC,gBAAgB,GAAG,OAAO,CAAC;YAC3C,eAAe,CAAC,gBAAgB,CAAC,WAAW,GAAG,SAAS,CAAC;YACzD,eAAe,CAAC,gBAAgB,CAAC,SAAS,GAAG,CAAC,CAAC;YAC/C,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SACrD;QACD,eAAe,CAAC,UAAU,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;QAC9D,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QAEjD,yBAAyB;QACzB,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC;QAEpC,wBAAwB;QACxB,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAEjC,IAAI,OAAO,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SAC5D;QACD,IAAI,OAAO,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SACxC;QACD,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SAClD;QACD,IAAI,OAAO,CAAC,aAAa,EAAE;YACvB,eAAe,CAAC,oBAAoB,EAAE,CAAC;SAC1C;QAED,2CAA2C;QAC3C,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAE3C,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC;QAC7B,4BAA4B;QAC5B,IAAI,CAAC,mBAAmB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,2BAA2B,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,2BAA2B;QAC3B,IAAI,CAAC,wBAAwB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,oBAAoB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE9C,IAAI,CAAC,wBAAwB,GAAG,CAAC,GAAG,EAAE,EAAE;YACpC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC;QACF,IAAI,CAAC,wBAAwB,GAAG,CAAC,GAAG,EAAE,EAAE;YACpC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC;QACF,IAAI,CAAC,sBAAsB,GAAG,CAAC,GAAG,EAAE,EAAE;YAClC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAC3B,CAAC,CAAC;QAEF,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC7F,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC7F,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QACzF,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC1F,eAAe,CAAC,MAAM,CAAC,gBAAgB,CACnC,aAAa,EACb,CAAC,GAAG,EAAE,EAAE;YACJ,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC,uBAAuB;QACjD,CAAC,EACD,KAAK,CACR,CAAC;QACF,qBAAqB,CAAC,GAAG,EAAE;YACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,sBAAsB,CAAC,sBAA8B;QACxD,IAAI,CAAC,oBAAoB,GAAG,sBAAsB,CAAC;QACnD,IAAI,CAAC,oBAAoB,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IACvE,CAAC;IAEO,cAAc,CAAC,CAAe;QAClC,IAAI,yBAAkC,CAAC;QAEvC,CAAC,CAAC,cAAc,EAAE,CAAC;QAEnB,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;YAC7B,yBAAyB,GAAG,CAAC,CAAC,OAAO,GAAG,eAAe,CAAC,UAAU,CAAC;SACtE;aAAM;YACH,yBAAyB,GAAG,CAAC,CAAC,OAAO,GAAG,eAAe,CAAC,UAAU,CAAC;SACtE;QAED,IAAI,yBAAyB,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;YAC1D,0DAA0D;YAC1D,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,SAAS,CAAC;YAEtC,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxB,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;gBAC/D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;gBAC1D,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;gBAElE,qDAAqD;gBACrD,yCAAyC;gBACzC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;aAC1B;iBAAM;gBACH,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;gBAC5C,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;gBAC5C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;gBACjE,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;aAC5E;YAED,IAAI,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC;YAChC,IAAI,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC;YAChC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;SAChD;aAAM;YACH,mEAAmE;YACnE,IAAI,eAAe,CAAC,oBAAoB,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC1D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;aACjH;SACJ;IACL,CAAC;IAEO,cAAc,CAAC,CAAe;QAClC,qFAAqF;QACrF,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC,SAAS,EAAE;YACxC,gCAAgC;YAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;gBACrH,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;gBAEjC,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,EAAE;oBAC/B,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,CAAC;iBACtD;gBAED,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;gBACxE,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;aAC3E;iBAAM;gBACH,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;gBACvC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;aAC1C;YAED,sBAAsB;YACtB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;YAC7D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAE9F,oEAAoE;YACpE,6DAA6D;YAC7D,IAAI,CAAC,GAAG,eAAe,CAAC,oBAAoB,EAAE;gBAC1C,IAAI,IAAI,CAAC,aAAa,EAAE;oBACpB,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;iBACjG;qBAAM;oBACH,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;iBACjG;aACJ;YAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,cAAc,GAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACtG,QAAQ,IAAI,CAAC,2BAA2B,EAAE;gBACtC,KAAK,YAAY,CAAC,CAAC;oBACf,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;oBACjE,MAAM;gBACV,KAAK,YAAY,CAAC,CAAC;oBACf,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;oBACjE,MAAM;gBACV,KAAK,YAAY,CAAC,CAAC;oBACf,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;oBACjE,MAAM;aACb;YACD,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,cAAc,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACnG,QAAQ,IAAI,CAAC,wBAAwB,EAAE;gBACnC,KAAK,YAAY,CAAC,CAAC;oBACf,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;oBACjE,MAAM;gBACV,KAAK,YAAY,CAAC,CAAC;oBACf,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;oBACjE,MAAM;gBACV,KAAK,YAAY,CAAC,CAAC;oBACf,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;oBACjE,MAAM;aACb;SACJ;aAAM;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;YACvD,IAAI,IAAI,EAAE;gBACL,IAAY,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;gBAC3B,IAAY,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;aAC/B;SACJ;IACL,CAAC;IAEO,YAAY,CAAC,CAAe;QAChC,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC,SAAS,EAAE;YACxC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;SACxB;aAAM;YACH,MAAM,KAAK,GAA2D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;YAChH,IAAI,KAAK,EAAE;gBACP,eAAe,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,EAAE,KAAK,CAAC,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;aAC1F;SACJ;QACD,IAAI,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC;QAEhC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,QAAgB;QACpC,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,aAAa,CAAC,OAAe;QACpC,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC;QACtC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC,CAAC;QACjE,IAAI,CAAC,yBAAyB,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;IACtE,CAAC;IACD,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ,CAAC,OAAe;QAC/B,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC;QACjC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAC,CAAC;QACvD,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC;IACD,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,aAAa,CAAC,KAAc;QACnC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,KAAK,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACjC,eAAe,CAAC,oBAAoB,EAAE,CAAC;YAEvC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC9B;aAAM;YACH,eAAe,CAAC,oBAAoB,EAAE,CAAC;YAEvC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;SAC/B;IACL,CAAC;IACD,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,CAAS,EAAE,CAAS;QACnC,gEAAgE;QAChE,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;QAED,IAAI,CAAC,iBAAiB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,MAAiB;QACrC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,mBAAmB,CAAC,IAAkB;QACzC,QAAQ,IAAI,EAAE;YACV,KAAK,YAAY,CAAC,CAAC,CAAC;YACpB,KAAK,YAAY,CAAC,CAAC,CAAC;YACpB,KAAK,YAAY,CAAC,CAAC;gBACf,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;gBACxC,MAAM;YACV;gBACI,IAAI,CAAC,2BAA2B,GAAG,YAAY,CAAC,CAAC,CAAC;gBAClD,MAAM;SACb;IACL,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,IAAkB;QACtC,QAAQ,IAAI,EAAE;YACV,KAAK,YAAY,CAAC,CAAC,CAAC;YACpB,KAAK,YAAY,CAAC,CAAC,CAAC;YACpB,KAAK,YAAY,CAAC,CAAC;gBACf,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;gBACrC,MAAM;YACV;gBACI,IAAI,CAAC,wBAAwB,GAAG,YAAY,CAAC,CAAC,CAAC;gBAC/C,MAAM;SACb;IACL,CAAC;IAED;;OAEG;IACK,kBAAkB;QACtB,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,wBAAwB,CAAC;QAEnE,yBAAyB;QACzB,eAAe,CAAC,gBAAgB,CAAC,SAAS,CACtC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,yBAAyB,EACrC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,yBAAyB,EACrC,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,mBAAmB,CAC3B,CAAC;QAEF,kEAAkE;QAClE,eAAe,CAAC,gBAAgB,CAAC,SAAS,CACtC,IAAI,CAAC,2BAA2B,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,GAAG,CAAC,EAClE,IAAI,CAAC,2BAA2B,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,GAAG,CAAC,EAClE,IAAI,CAAC,cAAc,GAAG,CAAC,EACvB,IAAI,CAAC,cAAc,GAAG,CAAC,CAC1B,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,OAAe;QACpC,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QAC1B,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC;QAEpB,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,CAAC;IACxD,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,OAAe;QAC/B,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QAC1B,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC;QAEpB,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACK,cAAc;QAClB,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,wBAAwB,CAAC;QAEnE,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,eAAe,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;SAC1K;aAAM;YACH,kBAAkB;YAClB,eAAe,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;YAC7C,eAAe,CAAC,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC;YACnE,eAAe,CAAC,gBAAgB,CAAC,SAAS,GAAG,CAAC,CAAC;YAC/C,eAAe,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;YAC3F,eAAe,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC1C,eAAe,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;YAE7C,kBAAkB;YAClB,eAAe,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;YAC7C,eAAe,CAAC,gBAAgB,CAAC,SAAS,GAAG,CAAC,CAAC;YAC/C,eAAe,CAAC,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC;YACnE,eAAe,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;YACtF,eAAe,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC1C,eAAe,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;SAChD;IACL,CAAC;IAED;;OAEG;IACK,SAAS;QACb,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,eAAe,CAAC,gBAAgB,CAAC,SAAS,CACtC,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,EAC1C,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,EAC1C,IAAI,CAAC,QAAQ,GAAG,CAAC,EACjB,IAAI,CAAC,QAAQ,GAAG,CAAC,CACpB,CAAC;SACL;aAAM;YACH,eAAe,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;YAC7C,eAAe,CAAC,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC;YACnE,eAAe,CAAC,gBAAgB,CAAC,SAAS,GAAG,CAAC,CAAC;YAC/C,eAAe,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;YAClI,eAAe,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC1C,eAAe,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;SAChD;IACL,CAAC;IAEO,oBAAoB;QACxB,4CAA4C;QAC5C,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO;SACV;QACD,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBACjC,IAAmB,KAAM,CAAC,SAAS,KAAK,IAAI,CAAC,kBAAkB,EAAE;oBAC7D,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;wBACrB,IAAI,CAAC,cAAc,EAAE,CAAC;qBACzB;oBAED,IAAI,CAAC,SAAS,EAAE,CAAC;oBAEjB,uCAAuC;oBACvC,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;iBACvE;qBAAM;oBACH,eAAe,CAAC,gBAAgB,CAAC,SAAS,CAAO,KAAM,CAAC,KAAK,GAAG,EAAE,EAAQ,KAAM,CAAC,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;oBACrG,eAAe,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;oBAC7C,eAAe,CAAC,gBAAgB,CAAC,SAAS,GAAG,OAAO,CAAC;oBACrD,eAAe,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;oBAC7C,eAAe,CAAC,gBAAgB,CAAC,WAAW,GAAG,KAAK,CAAC;oBACrD,eAAe,CAAC,gBAAgB,CAAC,SAAS,GAAG,CAAC,CAAC;oBAC/C,eAAe,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;oBACjF,eAAe,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;oBAC1C,eAAe,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;oBACvC,KAAM,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;oBACvB,KAAM,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;iBAChC;YACL,CAAC,CAAC,CAAC;SACN;QACD,qBAAqB,CAAC,GAAG,EAAE;YACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,eAAe,CAAC,MAAM,EAAE;YACxB,eAAe,CAAC,MAAM,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACzF,eAAe,CAAC,MAAM,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACzF,eAAe,CAAC,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACrF,eAAe,CAAC,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACtF,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACrD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAClD,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC;SACjC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;;AAjlBD,4FAA4F;AAC7E,oCAAoB,GAAW,CAAC,AAAZ,CAAa;AACjC,oCAAoB,GAAW,CAAC,AAAZ,CAAa", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Vector3, Vector2 } from \"../Maths/math.vector\";\r\nimport { StringDictionary } from \"./stringDictionary\";\r\n\r\n// Mainly based on these 2 articles :\r\n// Creating an universal virtual touch joystick working for all Touch models thanks to Hand.JS : http://blogs.msdn.com/b/davrous/archive/2013/02/22/creating-an-universal-virtual-touch-joystick-working-for-all-touch-models-thanks-to-hand-js.aspx\r\n// & on Seb <PERSON><PERSON> original work: http://seb.ly/2011/04/multi-touch-game-controller-in-javascripthtml5-for-ipad/\r\n\r\n/**\r\n * Defines the potential axis of a Joystick\r\n */\r\nexport enum JoystickAxis {\r\n    /** X axis */\r\n    X,\r\n    /** Y axis */\r\n    Y,\r\n    /** Z axis */\r\n    Z,\r\n}\r\n\r\n/**\r\n * Represents the different customization options available\r\n * for VirtualJoystick\r\n */\r\ninterface VirtualJoystickCustomizations {\r\n    /**\r\n     * Size of the joystick's puck\r\n     */\r\n    puckSize: number;\r\n    /**\r\n     * Size of the joystick's container\r\n     */\r\n    containerSize: number;\r\n    /**\r\n     * Color of the joystick && puck\r\n     */\r\n    color: string;\r\n    /**\r\n     * Image URL for the joystick's puck\r\n     */\r\n    puckImage?: string;\r\n    /**\r\n     * Image URL for the joystick's container\r\n     */\r\n    containerImage?: string;\r\n    /**\r\n     * Defines the unmoving position of the joystick container\r\n     */\r\n    position?: { x: number; y: number };\r\n    /**\r\n     * Defines whether or not the joystick container is always visible\r\n     */\r\n    alwaysVisible: boolean;\r\n    /**\r\n     * Defines whether or not to limit the movement of the puck to the joystick's container\r\n     */\r\n    limitToContainer: boolean;\r\n}\r\n\r\n/**\r\n * Class used to define virtual joystick (used in touch mode)\r\n */\r\nexport class VirtualJoystick {\r\n    /**\r\n     * Gets or sets a boolean indicating that left and right values must be inverted\r\n     */\r\n    public reverseLeftRight: boolean;\r\n    /**\r\n     * Gets or sets a boolean indicating that up and down values must be inverted\r\n     */\r\n    public reverseUpDown: boolean;\r\n    /**\r\n     * Gets the offset value for the position (ie. the change of the position value)\r\n     */\r\n    public deltaPosition: Vector3;\r\n    /**\r\n     * Gets a boolean indicating if the virtual joystick was pressed\r\n     */\r\n    public pressed: boolean;\r\n    /**\r\n     * Canvas the virtual joystick will render onto, default z-index of this is 5\r\n     */\r\n    public static Canvas: Nullable<HTMLCanvasElement>;\r\n\r\n    /**\r\n     * boolean indicating whether or not the joystick's puck's movement should be limited to the joystick's container area\r\n     */\r\n    public limitToContainer: boolean;\r\n\r\n    // Used to draw the virtual joystick inside a 2D canvas on top of the WebGL rendering canvas\r\n    private static _GlobalJoystickIndex: number = 0;\r\n    private static _AlwaysVisibleSticks: number = 0;\r\n    private static _VJCanvasContext: CanvasRenderingContext2D;\r\n    private static _VJCanvasWidth: number;\r\n    private static _VJCanvasHeight: number;\r\n    private static _HalfWidth: number;\r\n    private static _GetDefaultOptions(): VirtualJoystickCustomizations {\r\n        return {\r\n            puckSize: 40,\r\n            containerSize: 60,\r\n            color: \"cyan\",\r\n            puckImage: undefined,\r\n            containerImage: undefined,\r\n            position: undefined,\r\n            alwaysVisible: false,\r\n            limitToContainer: false,\r\n        };\r\n    }\r\n\r\n    private _action: () => any;\r\n    private _axisTargetedByLeftAndRight: JoystickAxis;\r\n    private _axisTargetedByUpAndDown: JoystickAxis;\r\n    private _joystickSensibility: number;\r\n    private _inversedSensibility: number;\r\n    private _joystickPointerId: number;\r\n    private _joystickColor: string;\r\n    private _joystickPointerPos: Vector2;\r\n    private _joystickPreviousPointerPos: Vector2;\r\n    private _joystickPointerStartPos: Vector2;\r\n    private _deltaJoystickVector: Vector2;\r\n    private _leftJoystick: boolean;\r\n    private _touches: StringDictionary<{ x: number; y: number; prevX: number; prevY: number } | PointerEvent>;\r\n    private _joystickPosition: Nullable<Vector2>;\r\n    private _alwaysVisible: boolean;\r\n    private _puckImage: HTMLImageElement;\r\n    private _containerImage: HTMLImageElement;\r\n    private _released = false;\r\n\r\n    // size properties\r\n    private _joystickPuckSize: number;\r\n    private _joystickContainerSize: number;\r\n    private _clearPuckSize: number;\r\n    private _clearContainerSize: number;\r\n    private _clearPuckSizeOffset: number;\r\n    private _clearContainerSizeOffset: number;\r\n\r\n    private _onPointerDownHandlerRef: (e: PointerEvent) => any;\r\n    private _onPointerMoveHandlerRef: (e: PointerEvent) => any;\r\n    private _onPointerUpHandlerRef: (e: PointerEvent) => any;\r\n    private _onResize: (e: any) => any;\r\n\r\n    /**\r\n     * Creates a new virtual joystick\r\n     * @param leftJoystick defines that the joystick is for left hand (false by default)\r\n     * @param customizations Defines the options we want to customize the VirtualJoystick\r\n     */\r\n    constructor(leftJoystick?: boolean, customizations?: Partial<VirtualJoystickCustomizations>) {\r\n        const options = {\r\n            ...VirtualJoystick._GetDefaultOptions(),\r\n            ...customizations,\r\n        };\r\n\r\n        if (leftJoystick) {\r\n            this._leftJoystick = true;\r\n        } else {\r\n            this._leftJoystick = false;\r\n        }\r\n\r\n        VirtualJoystick._GlobalJoystickIndex++;\r\n\r\n        // By default left & right arrow keys are moving the X\r\n        // and up & down keys are moving the Y\r\n        this._axisTargetedByLeftAndRight = JoystickAxis.X;\r\n        this._axisTargetedByUpAndDown = JoystickAxis.Y;\r\n\r\n        this.reverseLeftRight = false;\r\n        this.reverseUpDown = false;\r\n\r\n        // collections of pointers\r\n        this._touches = new StringDictionary<{ x: number; y: number; prevX: number; prevY: number } | PointerEvent>();\r\n        this.deltaPosition = Vector3.Zero();\r\n\r\n        this._joystickSensibility = 25;\r\n        this._inversedSensibility = 1 / (this._joystickSensibility / 1000);\r\n\r\n        this._onResize = () => {\r\n            VirtualJoystick._VJCanvasWidth = window.innerWidth;\r\n            VirtualJoystick._VJCanvasHeight = window.innerHeight;\r\n            if (VirtualJoystick.Canvas) {\r\n                VirtualJoystick.Canvas.width = VirtualJoystick._VJCanvasWidth;\r\n                VirtualJoystick.Canvas.height = VirtualJoystick._VJCanvasHeight;\r\n            }\r\n            VirtualJoystick._HalfWidth = VirtualJoystick._VJCanvasWidth / 2;\r\n        };\r\n\r\n        // injecting a canvas element on top of the canvas 3D game\r\n        if (!VirtualJoystick.Canvas) {\r\n            window.addEventListener(\"resize\", this._onResize, false);\r\n            VirtualJoystick.Canvas = document.createElement(\"canvas\");\r\n            VirtualJoystick._VJCanvasWidth = window.innerWidth;\r\n            VirtualJoystick._VJCanvasHeight = window.innerHeight;\r\n            VirtualJoystick.Canvas.width = window.innerWidth;\r\n            VirtualJoystick.Canvas.height = window.innerHeight;\r\n            VirtualJoystick.Canvas.style.width = \"100%\";\r\n            VirtualJoystick.Canvas.style.height = \"100%\";\r\n            VirtualJoystick.Canvas.style.position = \"absolute\";\r\n            VirtualJoystick.Canvas.style.backgroundColor = \"transparent\";\r\n            VirtualJoystick.Canvas.style.top = \"0px\";\r\n            VirtualJoystick.Canvas.style.left = \"0px\";\r\n            VirtualJoystick.Canvas.style.zIndex = \"5\";\r\n            VirtualJoystick.Canvas.style.touchAction = \"none\"; // fix https://forum.babylonjs.com/t/virtualjoystick-needs-to-set-style-touch-action-none-explicitly/9562\r\n            // Support for jQuery PEP polyfill\r\n            VirtualJoystick.Canvas.setAttribute(\"touch-action\", \"none\");\r\n            const context = VirtualJoystick.Canvas.getContext(\"2d\");\r\n\r\n            if (!context) {\r\n                throw new Error(\"Unable to create canvas for virtual joystick\");\r\n            }\r\n\r\n            VirtualJoystick._VJCanvasContext = context;\r\n            VirtualJoystick._VJCanvasContext.strokeStyle = \"#ffffff\";\r\n            VirtualJoystick._VJCanvasContext.lineWidth = 2;\r\n            document.body.appendChild(VirtualJoystick.Canvas);\r\n        }\r\n        VirtualJoystick._HalfWidth = VirtualJoystick.Canvas.width / 2;\r\n        this.pressed = false;\r\n        this.limitToContainer = options.limitToContainer;\r\n\r\n        // default joystick color\r\n        this._joystickColor = options.color;\r\n\r\n        // default joystick size\r\n        this.containerSize = options.containerSize;\r\n        this.puckSize = options.puckSize;\r\n\r\n        if (options.position) {\r\n            this.setPosition(options.position.x, options.position.y);\r\n        }\r\n        if (options.puckImage) {\r\n            this.setPuckImage(options.puckImage);\r\n        }\r\n        if (options.containerImage) {\r\n            this.setContainerImage(options.containerImage);\r\n        }\r\n        if (options.alwaysVisible) {\r\n            VirtualJoystick._AlwaysVisibleSticks++;\r\n        }\r\n\r\n        // must come after position potentially set\r\n        this.alwaysVisible = options.alwaysVisible;\r\n\r\n        this._joystickPointerId = -1;\r\n        // current joystick position\r\n        this._joystickPointerPos = new Vector2(0, 0);\r\n        this._joystickPreviousPointerPos = new Vector2(0, 0);\r\n        // origin joystick position\r\n        this._joystickPointerStartPos = new Vector2(0, 0);\r\n        this._deltaJoystickVector = new Vector2(0, 0);\r\n\r\n        this._onPointerDownHandlerRef = (evt) => {\r\n            this._onPointerDown(evt);\r\n        };\r\n        this._onPointerMoveHandlerRef = (evt) => {\r\n            this._onPointerMove(evt);\r\n        };\r\n        this._onPointerUpHandlerRef = (evt) => {\r\n            this._onPointerUp(evt);\r\n        };\r\n\r\n        VirtualJoystick.Canvas.addEventListener(\"pointerdown\", this._onPointerDownHandlerRef, false);\r\n        VirtualJoystick.Canvas.addEventListener(\"pointermove\", this._onPointerMoveHandlerRef, false);\r\n        VirtualJoystick.Canvas.addEventListener(\"pointerup\", this._onPointerUpHandlerRef, false);\r\n        VirtualJoystick.Canvas.addEventListener(\"pointerout\", this._onPointerUpHandlerRef, false);\r\n        VirtualJoystick.Canvas.addEventListener(\r\n            \"contextmenu\",\r\n            (evt) => {\r\n                evt.preventDefault(); // Disables system menu\r\n            },\r\n            false\r\n        );\r\n        requestAnimationFrame(() => {\r\n            this._drawVirtualJoystick();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Defines joystick sensibility (ie. the ratio between a physical move and virtual joystick position change)\r\n     * @param newJoystickSensibility defines the new sensibility\r\n     */\r\n    public setJoystickSensibility(newJoystickSensibility: number) {\r\n        this._joystickSensibility = newJoystickSensibility;\r\n        this._inversedSensibility = 1 / (this._joystickSensibility / 1000);\r\n    }\r\n\r\n    private _onPointerDown(e: PointerEvent) {\r\n        let positionOnScreenCondition: boolean;\r\n\r\n        e.preventDefault();\r\n\r\n        if (this._leftJoystick === true) {\r\n            positionOnScreenCondition = e.clientX < VirtualJoystick._HalfWidth;\r\n        } else {\r\n            positionOnScreenCondition = e.clientX > VirtualJoystick._HalfWidth;\r\n        }\r\n\r\n        if (positionOnScreenCondition && this._joystickPointerId < 0) {\r\n            // First contact will be dedicated to the virtual joystick\r\n            this._joystickPointerId = e.pointerId;\r\n\r\n            if (this._joystickPosition) {\r\n                this._joystickPointerStartPos = this._joystickPosition.clone();\r\n                this._joystickPointerPos = this._joystickPosition.clone();\r\n                this._joystickPreviousPointerPos = this._joystickPosition.clone();\r\n\r\n                // in case the user only clicks down && doesn't move:\r\n                // this ensures the delta is properly set\r\n                this._onPointerMove(e);\r\n            } else {\r\n                this._joystickPointerStartPos.x = e.clientX;\r\n                this._joystickPointerStartPos.y = e.clientY;\r\n                this._joystickPointerPos = this._joystickPointerStartPos.clone();\r\n                this._joystickPreviousPointerPos = this._joystickPointerStartPos.clone();\r\n            }\r\n\r\n            this._deltaJoystickVector.x = 0;\r\n            this._deltaJoystickVector.y = 0;\r\n            this.pressed = true;\r\n            this._touches.add(e.pointerId.toString(), e);\r\n        } else {\r\n            // You can only trigger the action buttons with a joystick declared\r\n            if (VirtualJoystick._GlobalJoystickIndex < 2 && this._action) {\r\n                this._action();\r\n                this._touches.add(e.pointerId.toString(), { x: e.clientX, y: e.clientY, prevX: e.clientX, prevY: e.clientY });\r\n            }\r\n        }\r\n    }\r\n\r\n    private _onPointerMove(e: PointerEvent) {\r\n        // If the current pointer is the one associated to the joystick (first touch contact)\r\n        if (this._joystickPointerId == e.pointerId) {\r\n            // limit to container if need be\r\n            if (this.limitToContainer) {\r\n                const vector = new Vector2(e.clientX - this._joystickPointerStartPos.x, e.clientY - this._joystickPointerStartPos.y);\r\n                const distance = vector.length();\r\n\r\n                if (distance > this.containerSize) {\r\n                    vector.scaleInPlace(this.containerSize / distance);\r\n                }\r\n\r\n                this._joystickPointerPos.x = this._joystickPointerStartPos.x + vector.x;\r\n                this._joystickPointerPos.y = this._joystickPointerStartPos.y + vector.y;\r\n            } else {\r\n                this._joystickPointerPos.x = e.clientX;\r\n                this._joystickPointerPos.y = e.clientY;\r\n            }\r\n\r\n            // create delta vector\r\n            this._deltaJoystickVector = this._joystickPointerPos.clone();\r\n            this._deltaJoystickVector = this._deltaJoystickVector.subtract(this._joystickPointerStartPos);\r\n\r\n            // if a joystick is always visible, there will be clipping issues if\r\n            // you drag the puck from one over the container of the other\r\n            if (0 < VirtualJoystick._AlwaysVisibleSticks) {\r\n                if (this._leftJoystick) {\r\n                    this._joystickPointerPos.x = Math.min(VirtualJoystick._HalfWidth, this._joystickPointerPos.x);\r\n                } else {\r\n                    this._joystickPointerPos.x = Math.max(VirtualJoystick._HalfWidth, this._joystickPointerPos.x);\r\n                }\r\n            }\r\n\r\n            const directionLeftRight = this.reverseLeftRight ? -1 : 1;\r\n            const deltaJoystickX = (directionLeftRight * this._deltaJoystickVector.x) / this._inversedSensibility;\r\n            switch (this._axisTargetedByLeftAndRight) {\r\n                case JoystickAxis.X:\r\n                    this.deltaPosition.x = Math.min(1, Math.max(-1, deltaJoystickX));\r\n                    break;\r\n                case JoystickAxis.Y:\r\n                    this.deltaPosition.y = Math.min(1, Math.max(-1, deltaJoystickX));\r\n                    break;\r\n                case JoystickAxis.Z:\r\n                    this.deltaPosition.z = Math.min(1, Math.max(-1, deltaJoystickX));\r\n                    break;\r\n            }\r\n            const directionUpDown = this.reverseUpDown ? 1 : -1;\r\n            const deltaJoystickY = (directionUpDown * this._deltaJoystickVector.y) / this._inversedSensibility;\r\n            switch (this._axisTargetedByUpAndDown) {\r\n                case JoystickAxis.X:\r\n                    this.deltaPosition.x = Math.min(1, Math.max(-1, deltaJoystickY));\r\n                    break;\r\n                case JoystickAxis.Y:\r\n                    this.deltaPosition.y = Math.min(1, Math.max(-1, deltaJoystickY));\r\n                    break;\r\n                case JoystickAxis.Z:\r\n                    this.deltaPosition.z = Math.min(1, Math.max(-1, deltaJoystickY));\r\n                    break;\r\n            }\r\n        } else {\r\n            const data = this._touches.get(e.pointerId.toString());\r\n            if (data) {\r\n                (data as any).x = e.clientX;\r\n                (data as any).y = e.clientY;\r\n            }\r\n        }\r\n    }\r\n\r\n    private _onPointerUp(e: PointerEvent) {\r\n        if (this._joystickPointerId == e.pointerId) {\r\n            this._clearPreviousDraw();\r\n\r\n            this._joystickPointerId = -1;\r\n            this.pressed = false;\r\n        } else {\r\n            const touch = <{ x: number; y: number; prevX: number; prevY: number }>this._touches.get(e.pointerId.toString());\r\n            if (touch) {\r\n                VirtualJoystick._VJCanvasContext.clearRect(touch.prevX - 44, touch.prevY - 44, 88, 88);\r\n            }\r\n        }\r\n        this._deltaJoystickVector.x = 0;\r\n        this._deltaJoystickVector.y = 0;\r\n\r\n        this._touches.remove(e.pointerId.toString());\r\n    }\r\n\r\n    /**\r\n     * Change the color of the virtual joystick\r\n     * @param newColor a string that must be a CSS color value (like \"red\") or the hexa value (like \"#FF0000\")\r\n     */\r\n    public setJoystickColor(newColor: string) {\r\n        this._joystickColor = newColor;\r\n    }\r\n\r\n    /**\r\n     * Size of the joystick's container\r\n     */\r\n    public set containerSize(newSize: number) {\r\n        this._joystickContainerSize = newSize;\r\n        this._clearContainerSize = ~~(this._joystickContainerSize * 2.1);\r\n        this._clearContainerSizeOffset = ~~(this._clearContainerSize / 2);\r\n    }\r\n    public get containerSize() {\r\n        return this._joystickContainerSize;\r\n    }\r\n\r\n    /**\r\n     * Size of the joystick's puck\r\n     */\r\n    public set puckSize(newSize: number) {\r\n        this._joystickPuckSize = newSize;\r\n        this._clearPuckSize = ~~(this._joystickPuckSize * 2.1);\r\n        this._clearPuckSizeOffset = ~~(this._clearPuckSize / 2);\r\n    }\r\n    public get puckSize() {\r\n        return this._joystickPuckSize;\r\n    }\r\n\r\n    /**\r\n     * Clears the set position of the joystick\r\n     */\r\n    public clearPosition() {\r\n        this.alwaysVisible = false;\r\n\r\n        this._joystickPosition = null;\r\n    }\r\n\r\n    /**\r\n     * Defines whether or not the joystick container is always visible\r\n     */\r\n    public set alwaysVisible(value: boolean) {\r\n        if (this._alwaysVisible === value) {\r\n            return;\r\n        }\r\n\r\n        if (value && this._joystickPosition) {\r\n            VirtualJoystick._AlwaysVisibleSticks++;\r\n\r\n            this._alwaysVisible = true;\r\n        } else {\r\n            VirtualJoystick._AlwaysVisibleSticks--;\r\n\r\n            this._alwaysVisible = false;\r\n        }\r\n    }\r\n    public get alwaysVisible() {\r\n        return this._alwaysVisible;\r\n    }\r\n\r\n    /**\r\n     * Sets the constant position of the Joystick container\r\n     * @param x X axis coordinate\r\n     * @param y Y axis coordinate\r\n     */\r\n    public setPosition(x: number, y: number) {\r\n        // just in case position is moved while the container is visible\r\n        if (this._joystickPointerStartPos) {\r\n            this._clearPreviousDraw();\r\n        }\r\n\r\n        this._joystickPosition = new Vector2(x, y);\r\n    }\r\n\r\n    /**\r\n     * Defines a callback to call when the joystick is touched\r\n     * @param action defines the callback\r\n     */\r\n    public setActionOnTouch(action: () => any) {\r\n        this._action = action;\r\n    }\r\n\r\n    /**\r\n     * Defines which axis you'd like to control for left & right\r\n     * @param axis defines the axis to use\r\n     */\r\n    public setAxisForLeftRight(axis: JoystickAxis) {\r\n        switch (axis) {\r\n            case JoystickAxis.X:\r\n            case JoystickAxis.Y:\r\n            case JoystickAxis.Z:\r\n                this._axisTargetedByLeftAndRight = axis;\r\n                break;\r\n            default:\r\n                this._axisTargetedByLeftAndRight = JoystickAxis.X;\r\n                break;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Defines which axis you'd like to control for up & down\r\n     * @param axis defines the axis to use\r\n     */\r\n    public setAxisForUpDown(axis: JoystickAxis) {\r\n        switch (axis) {\r\n            case JoystickAxis.X:\r\n            case JoystickAxis.Y:\r\n            case JoystickAxis.Z:\r\n                this._axisTargetedByUpAndDown = axis;\r\n                break;\r\n            default:\r\n                this._axisTargetedByUpAndDown = JoystickAxis.Y;\r\n                break;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clears the canvas from the previous puck / container draw\r\n     */\r\n    private _clearPreviousDraw() {\r\n        const jp = this._joystickPosition || this._joystickPointerStartPos;\r\n\r\n        // clear container pixels\r\n        VirtualJoystick._VJCanvasContext.clearRect(\r\n            jp.x - this._clearContainerSizeOffset,\r\n            jp.y - this._clearContainerSizeOffset,\r\n            this._clearContainerSize,\r\n            this._clearContainerSize\r\n        );\r\n\r\n        // clear puck pixels + 1 pixel for the change made before it moved\r\n        VirtualJoystick._VJCanvasContext.clearRect(\r\n            this._joystickPreviousPointerPos.x - this._clearPuckSizeOffset - 1,\r\n            this._joystickPreviousPointerPos.y - this._clearPuckSizeOffset - 1,\r\n            this._clearPuckSize + 2,\r\n            this._clearPuckSize + 2\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Loads `urlPath` to be used for the container's image\r\n     * @param urlPath defines the urlPath of an image to use\r\n     */\r\n    public setContainerImage(urlPath: string) {\r\n        const image = new Image();\r\n        image.src = urlPath;\r\n\r\n        image.onload = () => (this._containerImage = image);\r\n    }\r\n\r\n    /**\r\n     * Loads `urlPath` to be used for the puck's image\r\n     * @param urlPath defines the urlPath of an image to use\r\n     */\r\n    public setPuckImage(urlPath: string) {\r\n        const image = new Image();\r\n        image.src = urlPath;\r\n\r\n        image.onload = () => (this._puckImage = image);\r\n    }\r\n\r\n    /**\r\n     * Draws the Virtual Joystick's container\r\n     */\r\n    private _drawContainer() {\r\n        const jp = this._joystickPosition || this._joystickPointerStartPos;\r\n\r\n        this._clearPreviousDraw();\r\n\r\n        if (this._containerImage) {\r\n            VirtualJoystick._VJCanvasContext.drawImage(this._containerImage, jp.x - this.containerSize, jp.y - this.containerSize, this.containerSize * 2, this.containerSize * 2);\r\n        } else {\r\n            // outer container\r\n            VirtualJoystick._VJCanvasContext.beginPath();\r\n            VirtualJoystick._VJCanvasContext.strokeStyle = this._joystickColor;\r\n            VirtualJoystick._VJCanvasContext.lineWidth = 2;\r\n            VirtualJoystick._VJCanvasContext.arc(jp.x, jp.y, this.containerSize, 0, Math.PI * 2, true);\r\n            VirtualJoystick._VJCanvasContext.stroke();\r\n            VirtualJoystick._VJCanvasContext.closePath();\r\n\r\n            // inner container\r\n            VirtualJoystick._VJCanvasContext.beginPath();\r\n            VirtualJoystick._VJCanvasContext.lineWidth = 6;\r\n            VirtualJoystick._VJCanvasContext.strokeStyle = this._joystickColor;\r\n            VirtualJoystick._VJCanvasContext.arc(jp.x, jp.y, this.puckSize, 0, Math.PI * 2, true);\r\n            VirtualJoystick._VJCanvasContext.stroke();\r\n            VirtualJoystick._VJCanvasContext.closePath();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Draws the Virtual Joystick's puck\r\n     */\r\n    private _drawPuck() {\r\n        if (this._puckImage) {\r\n            VirtualJoystick._VJCanvasContext.drawImage(\r\n                this._puckImage,\r\n                this._joystickPointerPos.x - this.puckSize,\r\n                this._joystickPointerPos.y - this.puckSize,\r\n                this.puckSize * 2,\r\n                this.puckSize * 2\r\n            );\r\n        } else {\r\n            VirtualJoystick._VJCanvasContext.beginPath();\r\n            VirtualJoystick._VJCanvasContext.strokeStyle = this._joystickColor;\r\n            VirtualJoystick._VJCanvasContext.lineWidth = 2;\r\n            VirtualJoystick._VJCanvasContext.arc(this._joystickPointerPos.x, this._joystickPointerPos.y, this.puckSize, 0, Math.PI * 2, true);\r\n            VirtualJoystick._VJCanvasContext.stroke();\r\n            VirtualJoystick._VJCanvasContext.closePath();\r\n        }\r\n    }\r\n\r\n    private _drawVirtualJoystick() {\r\n        // canvas released? don't continue iterating\r\n        if (this._released) {\r\n            return;\r\n        }\r\n        if (this.alwaysVisible) {\r\n            this._drawContainer();\r\n        }\r\n\r\n        if (this.pressed) {\r\n            this._touches.forEach((key, touch) => {\r\n                if ((<PointerEvent>touch).pointerId === this._joystickPointerId) {\r\n                    if (!this.alwaysVisible) {\r\n                        this._drawContainer();\r\n                    }\r\n\r\n                    this._drawPuck();\r\n\r\n                    // store current pointer for next clear\r\n                    this._joystickPreviousPointerPos = this._joystickPointerPos.clone();\r\n                } else {\r\n                    VirtualJoystick._VJCanvasContext.clearRect((<any>touch).prevX - 44, (<any>touch).prevY - 44, 88, 88);\r\n                    VirtualJoystick._VJCanvasContext.beginPath();\r\n                    VirtualJoystick._VJCanvasContext.fillStyle = \"white\";\r\n                    VirtualJoystick._VJCanvasContext.beginPath();\r\n                    VirtualJoystick._VJCanvasContext.strokeStyle = \"red\";\r\n                    VirtualJoystick._VJCanvasContext.lineWidth = 6;\r\n                    VirtualJoystick._VJCanvasContext.arc(touch.x, touch.y, 40, 0, Math.PI * 2, true);\r\n                    VirtualJoystick._VJCanvasContext.stroke();\r\n                    VirtualJoystick._VJCanvasContext.closePath();\r\n                    (<any>touch).prevX = touch.x;\r\n                    (<any>touch).prevY = touch.y;\r\n                }\r\n            });\r\n        }\r\n        requestAnimationFrame(() => {\r\n            this._drawVirtualJoystick();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Release internal HTML canvas\r\n     */\r\n    public releaseCanvas() {\r\n        if (VirtualJoystick.Canvas) {\r\n            VirtualJoystick.Canvas.removeEventListener(\"pointerdown\", this._onPointerDownHandlerRef);\r\n            VirtualJoystick.Canvas.removeEventListener(\"pointermove\", this._onPointerMoveHandlerRef);\r\n            VirtualJoystick.Canvas.removeEventListener(\"pointerup\", this._onPointerUpHandlerRef);\r\n            VirtualJoystick.Canvas.removeEventListener(\"pointerout\", this._onPointerUpHandlerRef);\r\n            window.removeEventListener(\"resize\", this._onResize);\r\n            document.body.removeChild(VirtualJoystick.Canvas);\r\n            VirtualJoystick.Canvas = null;\r\n        }\r\n        this._released = true;\r\n    }\r\n}\r\n"]}