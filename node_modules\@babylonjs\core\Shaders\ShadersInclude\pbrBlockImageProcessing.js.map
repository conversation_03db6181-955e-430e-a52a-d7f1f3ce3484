{"version": 3, "file": "pbrBlockImageProcessing.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Shaders/ShadersInclude/pbrBlockImageProcessing.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAExD,MAAM,IAAI,GAAG,yBAAyB,CAAC;AACvC,MAAM,MAAM,GAAG;;;;;;;;;;;CAWd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAChD,gBAAgB;AAChB,MAAM,CAAC,MAAM,uBAAuB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore\";\n\nconst name = \"pbrBlockImageProcessing\";\nconst shader = `#if defined(IMAGEPROCESSINGPOSTPROCESS) || defined(SS_SCATTERING)\n#if !defined(SKIPFINALCOLORCLAMP)\nfinalColor.rgb=clamp(finalColor.rgb,0.,30.0);\n#endif\n#else\nfinalColor=applyImageProcessing(finalColor);\n#endif\nfinalColor.a*=visibility;\n#ifdef PREMULTIPLYALPHA\nfinalColor.rgb*=finalColor.a;\n#endif\n`;\n// Sideeffect\nShaderStore.IncludesShadersStore[name] = shader;\n/** @internal */\nexport const pbrBlockImageProcessing = { name, shader };\n"]}