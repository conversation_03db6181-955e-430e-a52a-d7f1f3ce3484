{"version": 3, "file": "simplexPerlin3DBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/simplexPerlin3DBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAGvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD;;GAEG;AACH,EAAE;AACF,UAAU;AACV,2DAA2D;AAC3D,iDAAiD;AACjD,8DAA8D;AAC9D,EAAE;AACF,+DAA+D;AAC/D,kFAAkF;AAClF,0HAA0H;AAC1H,WAAW;AACX,EAAE;AACF,gBAAgB;AAChB,oCAAoC;AACpC,oCAAoC;AACpC,kCAAkC;AAClC,EAAE;AACF,EAAE;AACF,kHAAkH;AAClH,wBAAwB;AACxB,qCAAqC;AACrC,oCAAoC;AACpC,yDAAyD;AACzD,4EAA4E;AAC5E,EAAE;AACF,8BAA8B;AAC9B,EAAE;AACF,2BAA2B;AAC3B,mCAAmC;AACnC,EAAE;AACF,MAAM,OAAO,oBAAqB,SAAQ,iBAAiB;IACvD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC1E,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;IAC/E,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACxB,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE;YAChC,OAAO;SACV;QAED,IAAI,cAAc,GAAG,qCAAqC,CAAC;QAC3D,cAAc,IAAI,uCAAuC,CAAC;QAC1D,cAAc,IAAI,yCAAyC,CAAC;QAC5D,cAAc,IAAI,gFAAgF,CAAC;QACnG,cAAc,IAAI,oCAAoC,CAAC;QACvD,cAAc,IAAI,oDAAoD,CAAC;QACvE,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,yDAAyD,CAAC;QAC5E,cAAc,IAAI,0DAA0D,CAAC;QAC7E,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,yBAAyB,CAAC;QAC5C,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,2CAA2C,CAAC;QAC9D,cAAc,IAAI,yCAAyC,CAAC;QAC5D,cAAc,IAAI,0CAA0C,CAAC;QAC7D,cAAc,IAAI,sDAAsD,CAAC;QACzE,cAAc,IAAI,sDAAsD,CAAC;QACzE,cAAc,IAAI,sDAAsD,CAAC;QACzE,cAAc,IAAI,gEAAgE,CAAC;QACnF,cAAc,IAAI,qEAAqE,CAAC;QACxF,cAAc,IAAI,uEAAuE,CAAC;QAC1F,cAAc,IAAI,iBAAiB,CAAC;QACpC,cAAc,IAAI,2EAA2E,CAAC;QAC9F,cAAc,IAAI,iFAAiF,CAAC;QACpG,cAAc,IAAI,gFAAgF,CAAC;QACnG,cAAc,IAAI,kEAAkE,CAAC;QACrF,cAAc,IAAI,mFAAmF,CAAC;QACtG,cAAc,IAAI,yFAAyF,CAAC;QAC5G,cAAc,IAAI,yDAAyD,CAAC;QAC5E,cAAc,IAAI,yDAAyD,CAAC;QAC5E,cAAc,IAAI,gGAAgG,CAAC;QACnH,cAAc,IAAI,gGAAgG,CAAC;QACnH,cAAc,IAAI,gGAAgG,CAAC;QACnH,cAAc,IAAI,4JAA4J,CAAC;QAC/K,cAAc,IAAI,4EAA4E,CAAC;QAC/F,cAAc,IAAI,wFAAwF,CAAC;QAC3G,cAAc,IAAI,wDAAwD,CAAC;QAC3E,cAAc,IAAI,sEAAsE,CAAC;QACzF,cAAc,IAAI,yEAAyE,CAAC;QAC5F,cAAc,IAAI,KAAK,CAAC;QAExB,KAAK,CAAC,aAAa,CAAC,iBAAiB,EAAE,cAAc,EAAE,oBAAoB,CAAC,CAAC;QAC7E,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,sBAAsB,IAAI,CAAC,IAAI,CAAC,sBAAsB,MAAM,CAAC;QAEvI,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,8BAA8B,EAAE,oBAAoB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\n/**\r\n * block used to Generate a Simplex Perlin 3d Noise Pattern\r\n */\r\n//\r\n//  Wombat\r\n//  An efficient texture-free GLSL procedural noise library\r\n//  Source: https://github.com/BrianSharpe/Wombat\r\n//  Derived from: https://github.com/BrianSharpe/GPU-Noise-Lib\r\n//\r\n//  I'm not one for copyrights.  Use the code however you wish.\r\n//  All I ask is that credit be given back to the blog or myself when appropriate.\r\n//  And also to let me know if you come up with any changes, improvements, thoughts or interesting uses for this stuff. :)\r\n//  Thanks!\r\n//\r\n//  <PERSON>\r\n//  brisharpe CIRCLE_A yahoo DOT com\r\n//  http://briansharpe.wordpress.com\r\n//  https://github.com/BrianSharpe\r\n//\r\n//\r\n//  This is a modified version of Stefan Gustavson's and Ian McEwan's work at http://github.com/ashima/webgl-noise\r\n//  Modifications are...\r\n//  - faster random number generation\r\n//  - analytical final normalization\r\n//  - space scaled can have an approx feature size of 1.0\r\n//  - filter kernel changed to fix discontinuities at tetrahedron boundaries\r\n//\r\n//  Converted to BJS by Pryme8\r\n//\r\n//  Simplex Perlin Noise 3D\r\n//  Return value range of -1.0->1.0\r\n//\r\nexport class SimplexPerlin3DBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new SimplexPerlin3DBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n        this.registerInput(\"seed\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Float);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"SimplexPerlin3DBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the seed operand input component\r\n     */\r\n    public get seed(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (!this.seed.isConnected) {\r\n            return;\r\n        }\r\n\r\n        if (!this._outputs[0].hasEndpoints) {\r\n            return;\r\n        }\r\n\r\n        let functionString = `const float SKEWFACTOR = 1.0/3.0;\\n`;\r\n        functionString += `const float UNSKEWFACTOR = 1.0/6.0;\\n`;\r\n        functionString += `const float SIMPLEX_CORNER_POS = 0.5;\\n`;\r\n        functionString += `const float SIMPLEX_TETRAHADRON_HEIGHT = 0.70710678118654752440084436210485;\\n`;\r\n        functionString += `float SimplexPerlin3D( vec3 P ){\\n`;\r\n        functionString += `    P.x = P == vec3(0., 0., 0.) ? 0.00001 : P.x;\\n`;\r\n        functionString += `    P *= SIMPLEX_TETRAHADRON_HEIGHT;\\n`;\r\n        functionString += `    vec3 Pi = floor( P + dot( P, vec3( SKEWFACTOR) ) );`;\r\n        functionString += `    vec3 x0 = P - Pi + dot(Pi, vec3( UNSKEWFACTOR ) );\\n`;\r\n        functionString += `    vec3 g = step(x0.yzx, x0.xyz);\\n`;\r\n        functionString += `    vec3 l = 1.0 - g;\\n`;\r\n        functionString += `    vec3 Pi_1 = min( g.xyz, l.zxy );\\n`;\r\n        functionString += `    vec3 Pi_2 = max( g.xyz, l.zxy );\\n`;\r\n        functionString += `    vec3 x1 = x0 - Pi_1 + UNSKEWFACTOR;\\n`;\r\n        functionString += `    vec3 x2 = x0 - Pi_2 + SKEWFACTOR;\\n`;\r\n        functionString += `    vec3 x3 = x0 - SIMPLEX_CORNER_POS;\\n`;\r\n        functionString += `    vec4 v1234_x = vec4( x0.x, x1.x, x2.x, x3.x );\\n`;\r\n        functionString += `    vec4 v1234_y = vec4( x0.y, x1.y, x2.y, x3.y );\\n`;\r\n        functionString += `    vec4 v1234_z = vec4( x0.z, x1.z, x2.z, x3.z );\\n`;\r\n        functionString += `    Pi.xyz = Pi.xyz - floor(Pi.xyz * ( 1.0 / 69.0 )) * 69.0;\\n`;\r\n        functionString += `    vec3 Pi_inc1 = step( Pi, vec3( 69.0 - 1.5 ) ) * ( Pi + 1.0 );\\n`;\r\n        functionString += `    vec4 Pt = vec4( Pi.xy, Pi_inc1.xy ) + vec2( 50.0, 161.0 ).xyxy;\\n`;\r\n        functionString += `    Pt *= Pt;\\n`;\r\n        functionString += `    vec4 V1xy_V2xy = mix( Pt.xyxy, Pt.zwzw, vec4( Pi_1.xy, Pi_2.xy ) );\\n`;\r\n        functionString += `    Pt = vec4( Pt.x, V1xy_V2xy.xz, Pt.z ) * vec4( Pt.y, V1xy_V2xy.yw, Pt.w );\\n`;\r\n        functionString += `    const vec3 SOMELARGEFLOATS = vec3( 635.298681, 682.357502, 668.926525 );\\n`;\r\n        functionString += `    const vec3 ZINC = vec3( 48.500388, 65.294118, 63.934599 );\\n`;\r\n        functionString += `    vec3 lowz_mods = vec3( 1.0 / ( SOMELARGEFLOATS.xyz + Pi.zzz * ZINC.xyz ) );\\n`;\r\n        functionString += `    vec3 highz_mods = vec3( 1.0 / ( SOMELARGEFLOATS.xyz + Pi_inc1.zzz * ZINC.xyz ) );\\n`;\r\n        functionString += `    Pi_1 = ( Pi_1.z < 0.5 ) ? lowz_mods : highz_mods;\\n`;\r\n        functionString += `    Pi_2 = ( Pi_2.z < 0.5 ) ? lowz_mods : highz_mods;\\n`;\r\n        functionString += `    vec4 hash_0 = fract( Pt * vec4( lowz_mods.x, Pi_1.x, Pi_2.x, highz_mods.x ) ) - 0.49999;\\n`;\r\n        functionString += `    vec4 hash_1 = fract( Pt * vec4( lowz_mods.y, Pi_1.y, Pi_2.y, highz_mods.y ) ) - 0.49999;\\n`;\r\n        functionString += `    vec4 hash_2 = fract( Pt * vec4( lowz_mods.z, Pi_1.z, Pi_2.z, highz_mods.z ) ) - 0.49999;\\n`;\r\n        functionString += `    vec4 grad_results = inversesqrt( hash_0 * hash_0 + hash_1 * hash_1 + hash_2 * hash_2 ) * ( hash_0 * v1234_x + hash_1 * v1234_y + hash_2 * v1234_z );\\n`;\r\n        functionString += `    const float FINAL_NORMALIZATION = 37.837227241611314102871574478976;\\n`;\r\n        functionString += `    vec4 kernel_weights = v1234_x * v1234_x + v1234_y * v1234_y + v1234_z * v1234_z;\\n`;\r\n        functionString += `    kernel_weights = max(0.5 - kernel_weights, 0.0);\\n`;\r\n        functionString += `    kernel_weights = kernel_weights*kernel_weights*kernel_weights;\\n`;\r\n        functionString += `    return dot( kernel_weights, grad_results ) * FINAL_NORMALIZATION;\\n`;\r\n        functionString += `}\\n`;\r\n\r\n        state._emitFunction(\"SimplexPerlin3D\", functionString, \"// SimplexPerlin3D\");\r\n        state.compilationString += this._declareOutput(this._outputs[0], state) + ` = SimplexPerlin3D(${this.seed.associatedVariableName});\\n`;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SimplexPerlin3DBlock\", SimplexPerlin3DBlock);\r\n"]}