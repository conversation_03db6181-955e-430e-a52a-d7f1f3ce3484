{"version": 3, "file": "nullBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Meshes/Node/Blocks/Sources/nullBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,8CAA8C,CAAC;AACrG,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D;;GAEG;AACH,MAAM,OAAO,SAAU,SAAQ,iBAAiB;IAC5C;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,qCAAqC,CAAC,QAAQ,CAAC,CAAC;IACpF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW;QACjB,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;IACtC,CAAC;CACJ;AAED,aAAa,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC", "sourcesContent": ["import { NodeGeometryBlockConnectionPointTypes } from \"../../Enums/nodeGeometryConnectionPointTypes\";\r\nimport { NodeGeometryBlock } from \"../../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../../nodeGeometryBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\n\r\n/**\r\n * Defines a block used to generate a null geometry data\r\n */\r\nexport class Null<PERSON>lock extends NodeGeometryBlock {\r\n    /**\r\n     * Create a new NullBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n        this.registerOutput(\"geometry\", NodeGeometryBlockConnectionPointTypes.Geometry);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"NullBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the geometry output component\r\n     */\r\n    public get geometry(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock() {\r\n        this.geometry._storedValue = null;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.NullBlock\", NullBlock);\r\n"]}