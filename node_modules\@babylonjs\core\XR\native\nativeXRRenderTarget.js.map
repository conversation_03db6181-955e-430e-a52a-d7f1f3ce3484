{"version": 3, "file": "nativeXRRenderTarget.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/native/nativeXRRenderTarget.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,qCAAqC,CAAC;AAI5F;;;GAGG;AACH,MAAM,OAAO,oBAAqB,SAAQ,iBAAiB;IACvD,YAA4B,KAAmB;QAC3C,KAAK,CACD,GAAG,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAC5B,GAAG,EAAE,CAAC,KAAK,CAAC,iBAAiB,EAC7B,KAAK,EACL,cAAc,EACd,CAAC,cAAc,EAAE,EAAE,CAAC,IAAI,wCAAwC,CAAC,cAAc,EAAE,IAAI,CAAC,CACzF,CAAC;QAPsB,UAAK,GAAL,KAAK,CAAc;IAQ/C,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,wCAAyC,SAAQ,qCAAqC;IAI/F,YACI,cAAmC,EACnB,YAAkC;QAElD,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAF1B,iBAAY,GAAZ,YAAY,CAAsB;QAGlD,IAAI,CAAC,kBAAkB,GAAI,SAAiB,CAAC,EAAE,CAAC,6BAA6B,CACzE,cAAc,CAAC,OAAO,EACtB,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,EAC1C,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9C,CAAC;QACF,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC;IAC3C,CAAC;IAEM,qBAAqB,CAAC,QAAkB;QAC3C,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QACf,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QACf,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;QACnB,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACpB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,4BAA4B,CAAC,GAAU;QAC1C,0GAA0G;QAC1G,OAAQ,IAAI,CAAC,kBAA0B,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;IACvE,CAAC;IAEM,6BAA6B,CAAC,IAAY;QAC7C,OAAQ,IAAI,CAAC,kBAA0B,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5E,CAAC;IAEM,wBAAwB;QAC3B,OAAO;YACH,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB;YACpD,iBAAiB,EAAE,IAAI,CAAC,YAAY,CAAC,iBAAiB;SACzD,CAAC;IACN,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IAM7B,YAAY,iBAAsC;QAC9C,IAAI,CAAC,mBAAmB,GAAI,SAAiB,CAAC,EAAE,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;IAC/G,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,SAAoB;QACpD,MAAM,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QACjE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAQ,CAAC;QACjD,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,OAAO;QACH,WAAW;IACf,CAAC;CACJ", "sourcesContent": ["import type { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\nimport type { Viewport } from \"../../Maths/math.viewport\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { WebXRLayerWrapper } from \"../webXRLayerWrapper\";\r\nimport { WebXRLayerRenderTargetTextureProvider } from \"../webXRRenderTargetTextureProvider\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport type { WebXRRenderTarget } from \"../webXRTypes\";\r\n\r\n/**\r\n * Wraps XRWebGLLayer's created by Babylon Native.\r\n * @internal\r\n */\r\nexport class NativeXRLayerWrapper extends WebXRLayerWrapper {\r\n    constructor(public readonly layer: XRWebGLLayer) {\r\n        super(\r\n            () => layer.framebufferWidth,\r\n            () => layer.framebufferHeight,\r\n            layer,\r\n            \"XRWebGLLayer\",\r\n            (sessionManager) => new NativeXRLayerRenderTargetTextureProvider(sessionManager, this)\r\n        );\r\n    }\r\n}\r\n\r\n/**\r\n * Provides render target textures for layers created by Babylon Native.\r\n * @internal\r\n */\r\nexport class NativeXRLayerRenderTargetTextureProvider extends WebXRLayerRenderTargetTextureProvider {\r\n    private _nativeRTTProvider: WebXRLayerRenderTargetTextureProvider;\r\n    private _nativeLayer: XRWebGLLayer;\r\n\r\n    constructor(\r\n        sessionManager: WebXRSessionManager,\r\n        public readonly layerWrapper: NativeXRLayerWrapper\r\n    ) {\r\n        super(sessionManager.scene, layerWrapper);\r\n        this._nativeRTTProvider = (navigator as any).xr.getNativeRenderTargetProvider(\r\n            sessionManager.session,\r\n            this._createRenderTargetTexture.bind(this),\r\n            this._destroyRenderTargetTexture.bind(this)\r\n        );\r\n        this._nativeLayer = layerWrapper.layer;\r\n    }\r\n\r\n    public trySetViewportForView(viewport: Viewport): boolean {\r\n        viewport.x = 0;\r\n        viewport.y = 0;\r\n        viewport.width = 1;\r\n        viewport.height = 1;\r\n        return true;\r\n    }\r\n\r\n    public getRenderTargetTextureForEye(eye: XREye): Nullable<RenderTargetTexture> {\r\n        // TODO (rgerd): Update the contract on the BabylonNative side to call this \"getRenderTargetTextureForEye\"\r\n        return (this._nativeRTTProvider as any).getRenderTargetForEye(eye);\r\n    }\r\n\r\n    public getRenderTargetTextureForView(view: XRView): Nullable<RenderTargetTexture> {\r\n        return (this._nativeRTTProvider as any).getRenderTargetForEye(view.eye);\r\n    }\r\n\r\n    public getFramebufferDimensions(): Nullable<{ framebufferWidth: number; framebufferHeight: number }> {\r\n        return {\r\n            framebufferWidth: this._nativeLayer.framebufferWidth,\r\n            framebufferHeight: this._nativeLayer.framebufferHeight,\r\n        };\r\n    }\r\n}\r\n\r\n/**\r\n * Creates the xr layer that will be used as the xr session's base layer.\r\n * @internal\r\n */\r\nexport class NativeXRRenderTarget implements WebXRRenderTarget {\r\n    public canvasContext: WebGLRenderingContext;\r\n    public xrLayer: Nullable<XRWebGLLayer>;\r\n\r\n    private _nativeRenderTarget: WebXRRenderTarget;\r\n\r\n    constructor(_xrSessionManager: WebXRSessionManager) {\r\n        this._nativeRenderTarget = (navigator as any).xr.getWebXRRenderTarget(_xrSessionManager.scene.getEngine());\r\n    }\r\n\r\n    public async initializeXRLayerAsync(xrSession: XRSession): Promise<XRWebGLLayer> {\r\n        await this._nativeRenderTarget.initializeXRLayerAsync(xrSession);\r\n        this.xrLayer = this._nativeRenderTarget.xrLayer!;\r\n        return this.xrLayer;\r\n    }\r\n\r\n    dispose(): void {\r\n        /* empty */\r\n    }\r\n}\r\n"]}