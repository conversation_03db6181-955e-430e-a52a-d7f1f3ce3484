{"version": 3, "file": "octree.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Culling/Octrees/octree.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,qBAAqB,EAAE,MAAM,uBAAuB,CAAC;AAK9D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAG5C;;;GAGG;AACH,MAAM,OAAO,MAAM;IAcf;;;;;;OAMG;IACH,YACI,YAAuD,EACvD,gBAAyB;IACzB,qKAAqK;IAC9J,WAAW,CAAC;QAAZ,aAAQ,GAAR,QAAQ,CAAI;QApBvB;;WAEG;QACI,mBAAc,GAAQ,EAAE,CAAC;QAmB5B,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,IAAI,EAAE,CAAC;QAChD,IAAI,CAAC,iBAAiB,GAAG,IAAI,qBAAqB,CAAI,IAAI,CAAC,CAAC;QAC5D,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;IACtC,CAAC;IAED,UAAU;IACV;;;;;OAKG;IACI,MAAM,CAAC,QAAiB,EAAE,QAAiB,EAAE,OAAY;QAC5D,WAAW,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC/H,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,KAAQ;QACnB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACzB;IACL,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,KAAQ;QACtB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAC5B;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,aAAsB,EAAE,cAAwB;QAC1D,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAE/B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;SACvE;QAED,IAAI,cAAc,EAAE;YAChB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACtD;aAAM;YACH,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACrE;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;;;;OAMG;IACI,UAAU,CAAC,YAAqB,EAAE,YAAoB,EAAE,cAAwB;QACnF,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAE/B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,KAAK,CAAC,UAAU,CAAC,YAAY,EAAE,YAAY,EAAE,IAAI,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;SACxF;QAED,IAAI,cAAc,EAAE;YAChB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACtD;aAAM;YACH,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACrE;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,GAAQ;QACzB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAE/B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAElE,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;;AAED;;;;GAIG;AACW,4BAAqB,GAAG,CAAC,KAAmB,EAAE,KAAgC,EAAQ,EAAE;IAClG,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;IAC7C,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,YAAY,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE;QAC/F,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC7B;AACL,CAAC,AALkC,CAKjC;AAEF;;;;GAIG;AACW,+BAAwB,GAAG,CAAC,KAAc,EAAE,KAA2B,EAAQ,EAAE;IAC3F,MAAM,YAAY,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;IAC7C,IAAI,YAAY,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE;QAC3E,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC7B;AACL,CAAC,AALqC,CAKpC", "sourcesContent": ["import type { SmartArray } from \"../../Misc/smartArray\";\r\nimport { SmartArrayNoDuplicate } from \"../../Misc/smartArray\";\r\nimport type { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Ray } from \"../../Culling/ray\";\r\nimport { OctreeBlock } from \"./octreeBlock\";\r\nimport type { Plane } from \"../../Maths/math.plane\";\r\n\r\n/**\r\n * Octrees are a really powerful data structure that can quickly select entities based on space coordinates.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n */\r\nexport class Octree<T> {\r\n    /**\r\n     * Blocks within the octree containing objects\r\n     */\r\n    public blocks: Array<OctreeBlock<T>>;\r\n    /**\r\n     * Content stored in the octree\r\n     */\r\n    public dynamicContent: T[] = [];\r\n\r\n    private _maxBlockCapacity: number;\r\n    private _selectionContent: SmartArrayNoDuplicate<T>;\r\n    private _creationFunc: (entry: T, block: OctreeBlock<T>) => void;\r\n\r\n    /**\r\n     * Creates a octree\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees\r\n     * @param creationFunc function to be used to instantiate the octree\r\n     * @param maxBlockCapacity defines the maximum number of meshes you want on your octree's leaves (default: 64)\r\n     * @param maxDepth defines the maximum depth (sub-levels) for your octree. Default value is 2, which means 8 8 8 = 512 blocks :) (This parameter takes precedence over capacity.)\r\n     */\r\n    constructor(\r\n        creationFunc: (entry: T, block: OctreeBlock<T>) => void,\r\n        maxBlockCapacity?: number,\r\n        /** Defines the maximum depth (sub-levels) for your octree. Default value is 2, which means 8 8 8 = 512 blocks :) (This parameter takes precedence over capacity.) */\r\n        public maxDepth = 2\r\n    ) {\r\n        this._maxBlockCapacity = maxBlockCapacity || 64;\r\n        this._selectionContent = new SmartArrayNoDuplicate<T>(1024);\r\n        this._creationFunc = creationFunc;\r\n    }\r\n\r\n    // Methods\r\n    /**\r\n     * Updates the octree by adding blocks for the passed in meshes within the min and max world parameters\r\n     * @param worldMin worldMin for the octree blocks var blockSize = new Vector3((worldMax.x - worldMin.x) / 2, (worldMax.y - worldMin.y) / 2, (worldMax.z - worldMin.z) / 2);\r\n     * @param worldMax worldMax for the octree blocks var blockSize = new Vector3((worldMax.x - worldMin.x) / 2, (worldMax.y - worldMin.y) / 2, (worldMax.z - worldMin.z) / 2);\r\n     * @param entries meshes to be added to the octree blocks\r\n     */\r\n    public update(worldMin: Vector3, worldMax: Vector3, entries: T[]): void {\r\n        OctreeBlock._CreateBlocks(worldMin, worldMax, entries, this._maxBlockCapacity, 0, this.maxDepth, this, this._creationFunc);\r\n    }\r\n\r\n    /**\r\n     * Adds a mesh to the octree\r\n     * @param entry Mesh to add to the octree\r\n     */\r\n    public addMesh(entry: T): void {\r\n        for (let index = 0; index < this.blocks.length; index++) {\r\n            const block = this.blocks[index];\r\n            block.addEntry(entry);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove an element from the octree\r\n     * @param entry defines the element to remove\r\n     */\r\n    public removeMesh(entry: T): void {\r\n        for (let index = 0; index < this.blocks.length; index++) {\r\n            const block = this.blocks[index];\r\n            block.removeEntry(entry);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Selects an array of meshes within the frustum\r\n     * @param frustumPlanes The frustum planes to use which will select all meshes within it\r\n     * @param allowDuplicate If duplicate objects are allowed in the resulting object array\r\n     * @returns array of meshes within the frustum\r\n     */\r\n    public select(frustumPlanes: Plane[], allowDuplicate?: boolean): SmartArray<T> {\r\n        this._selectionContent.reset();\r\n\r\n        for (let index = 0; index < this.blocks.length; index++) {\r\n            const block = this.blocks[index];\r\n            block.select(frustumPlanes, this._selectionContent, allowDuplicate);\r\n        }\r\n\r\n        if (allowDuplicate) {\r\n            this._selectionContent.concat(this.dynamicContent);\r\n        } else {\r\n            this._selectionContent.concatWithNoDuplicate(this.dynamicContent);\r\n        }\r\n\r\n        return this._selectionContent;\r\n    }\r\n\r\n    /**\r\n     * Test if the octree intersect with the given bounding sphere and if yes, then add its content to the selection array\r\n     * @param sphereCenter defines the bounding sphere center\r\n     * @param sphereRadius defines the bounding sphere radius\r\n     * @param allowDuplicate defines if the selection array can contains duplicated entries\r\n     * @returns an array of objects that intersect the sphere\r\n     */\r\n    public intersects(sphereCenter: Vector3, sphereRadius: number, allowDuplicate?: boolean): SmartArray<T> {\r\n        this._selectionContent.reset();\r\n\r\n        for (let index = 0; index < this.blocks.length; index++) {\r\n            const block = this.blocks[index];\r\n            block.intersects(sphereCenter, sphereRadius, this._selectionContent, allowDuplicate);\r\n        }\r\n\r\n        if (allowDuplicate) {\r\n            this._selectionContent.concat(this.dynamicContent);\r\n        } else {\r\n            this._selectionContent.concatWithNoDuplicate(this.dynamicContent);\r\n        }\r\n\r\n        return this._selectionContent;\r\n    }\r\n\r\n    /**\r\n     * Test if the octree intersect with the given ray and if yes, then add its content to resulting array\r\n     * @param ray defines the ray to test with\r\n     * @returns array of intersected objects\r\n     */\r\n    public intersectsRay(ray: Ray): SmartArray<T> {\r\n        this._selectionContent.reset();\r\n\r\n        for (let index = 0; index < this.blocks.length; index++) {\r\n            const block = this.blocks[index];\r\n            block.intersectsRay(ray, this._selectionContent);\r\n        }\r\n\r\n        this._selectionContent.concatWithNoDuplicate(this.dynamicContent);\r\n\r\n        return this._selectionContent;\r\n    }\r\n\r\n    /**\r\n     * Adds a mesh into the octree block if it intersects the block\r\n     * @param entry defines the mesh to try to add to the block\r\n     * @param block defines the block where the mesh should be added\r\n     */\r\n    public static CreationFuncForMeshes = (entry: AbstractMesh, block: OctreeBlock<AbstractMesh>): void => {\r\n        const boundingInfo = entry.getBoundingInfo();\r\n        if (!entry.isBlocked && boundingInfo.boundingBox.intersectsMinMax(block.minPoint, block.maxPoint)) {\r\n            block.entries.push(entry);\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Adds a submesh into the octree block if it intersects the block\r\n     * @param entry defines the submesh to try to add to the block\r\n     * @param block defines the block where the submesh should be added\r\n     */\r\n    public static CreationFuncForSubMeshes = (entry: SubMesh, block: OctreeBlock<SubMesh>): void => {\r\n        const boundingInfo = entry.getBoundingInfo();\r\n        if (boundingInfo.boundingBox.intersectsMinMax(block.minPoint, block.maxPoint)) {\r\n            block.entries.push(entry);\r\n        }\r\n    };\r\n}\r\n"]}