{"version": 3, "file": "pbrMetallicRoughnessBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/PBR/pbrMetallicRoughnessBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,oCAAoC,EAAE,MAAM,wCAAwC,CAAC;AAC9F,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAGjD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAI3D,OAAO,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAC;AAE/D,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AACtG,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AACxG,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,EAAE,yBAAyB,EAAE,MAAM,mCAAmC,CAAC;AAC9E,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAGpD,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAC1D,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,8BAA8B,CAAC;AACjE,OAAO,EAAE,MAAM,EAAE,mCAAyB;AAC1C,OAAO,EACH,SAAS,EACT,UAAU,EACV,sBAAsB,EACtB,uBAAuB,EACvB,0BAA0B,EAC1B,kCAAkC,GACrC,MAAM,mCAAmC,CAAC;AAE3C,MAAM,mBAAmB,GAAyC;IAC9D,UAAU,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;IAChC,UAAU,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;IAChC,WAAW,EAAE,CAAC,qBAAqB,EAAE,0CAA0C,CAAC;IAChF,YAAY,EAAE,CAAC,sBAAsB,EAAE,uCAAuC,CAAC;IAC/E,QAAQ,EAAE,CAAC,kBAAkB,EAAE,mCAAmC,CAAC;IACnE,UAAU,EAAE,CAAC,iBAAiB,EAAE,wCAAwC,CAAC;IACzE,WAAW,EAAE,CAAC,qBAAqB,EAAE,wCAAwC,CAAC;IAC9E,YAAY,EAAE,CAAC,2CAA2C,EAAE,8DAA8D,CAAC;IAC3H,QAAQ,EAAE,CAAC,mCAAmC,EAAE,sFAAsF,CAAC;IACvI,UAAU,EAAE,CAAC,+BAA+B,EAAE,2CAA2C,CAAC;IAC1F,QAAQ,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC;IAChC,MAAM,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC;IACzB,KAAK,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC;CACvB,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,yBAA0B,SAAQ,iBAAiB;IAMpD,MAAM,CAAC,kCAAkC,CAAC,KAAwB,EAAE,aAAqB;QAC7F,MAAM,IAAI,GAAG,KAAkC,CAAC;QAEhD,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;YAChC,IAAI,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC/D,MAAM,CAAC,KAAK,CAAC,qEAAqE,CAAC,CAAC;YACpF,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,UAAU;QACd,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QACvI,IAAI,CAAC,cAAc,CAAC,eAAe,CAAE,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB,CAAC,MAAM,CAAC;IACvJ,CAAC;IAYD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QAbpD,4BAAuB,GAA0B,IAAI,CAAC;QAItD,8BAAyB,GAAW,MAAM,CAAC,KAAK,EAAE,CAAC;QACnD,sBAAiB,GAAG,CAAC,CAAC;QAkF9B;;;WAGG;QAEI,oBAAe,GAAW,GAAG,CAAC;QAErC;;;WAGG;QAEI,yBAAoB,GAAW,GAAG,CAAC;QAE1C;;;WAGG;QAEI,sBAAiB,GAAW,GAAG,CAAC;QAEvC;;;WAGG;QASI,iBAAY,GAAG,CAAC,CAAC;QAExB;;WAEG;QAEI,iBAAY,GAAY,KAAK,CAAC;QAErC;;WAEG;QAEI,oBAAe,GAAW,GAAG,CAAC;QAErC;;WAEG;QAEI,qBAAgB,GAAY,KAAK,CAAC;QAEzC;;;WAGG;QAEI,yBAAoB,GAAY,IAAI,CAAC;QAE5C;;;WAGG;QAEI,yBAAoB,GAAY,IAAI,CAAC;QAE5C;;;;WAIG;QAEI,+BAA0B,GAAY,KAAK,CAAC;QAEnD;;WAEG;QAEI,sBAAiB,GAAY,KAAK,CAAC;QAE1C;;WAEG;QASI,6BAAwB,GAAG,SAAS,CAAC,6BAA6B,CAAC;QAE1E;;WAEG;QAEI,0BAAqB,GAAY,IAAI,CAAC;QAE7C;;;WAGG;QAEI,yBAAoB,GAAY,IAAI,CAAC;QAE5C;;;WAGG;QAEI,wBAAmB,GAAY,IAAI,CAAC;QAE3C;;WAEG;QAEI,UAAK,GAAY,KAAK,CAAC;QAE9B;;WAEG;QAEI,uBAAkB,GAAY,KAAK,CAAC;QAE3C,0JAA0J;QAInJ,6BAAwB,GAAG,KAAK,CAAC;QAExC;;;WAGG;QAwDI,cAAS,GAAG,CAAC,CAAC;QAErB;;;;;WAKG;QAEI,eAAU,GAAG,CAAC,CAAC;QAEtB;;;WAGG;QAEI,gBAAW,GAAG,CAAC,CAAC;QAvRnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,qCAAqC,CAAC,OAAO,EAAE,KAAK,EAAE,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAC3H,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,qCAAqC,CAAC,OAAO,EAAE,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC3H,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,qCAAqC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAChF,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,qCAAqC,CAAC,OAAO,EAAE,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9H,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,qCAAqC,CAAC,OAAO,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9H,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,MAAM,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,qCAAqC,CAAC,KAAK,EAAE,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACtH,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACpH,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9H,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,qCAAqC,CAAC,MAAM,EAAE,IAAI,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC1H,IAAI,CAAC,aAAa,CACd,YAAY,EACZ,qCAAqC,CAAC,MAAM,EAC5C,IAAI,EACJ,wBAAwB,CAAC,QAAQ,EACjC,IAAI,uCAAuC,CAAC,YAAY,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAClJ,CAAC;QACF,IAAI,CAAC,aAAa,CACd,WAAW,EACX,qCAAqC,CAAC,MAAM,EAC5C,IAAI,EACJ,wBAAwB,CAAC,QAAQ,EACjC,IAAI,uCAAuC,CAAC,WAAW,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAC/I,CAAC;QACF,IAAI,CAAC,aAAa,CACd,OAAO,EACP,qCAAqC,CAAC,MAAM,EAC5C,IAAI,EACJ,wBAAwB,CAAC,QAAQ,EACjC,IAAI,uCAAuC,CAAC,OAAO,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,EAAE,UAAU,EAAE,YAAY,CAAC,CACnI,CAAC;QACF,IAAI,CAAC,aAAa,CACd,YAAY,EACZ,qCAAqC,CAAC,MAAM,EAC5C,IAAI,EACJ,wBAAwB,CAAC,QAAQ,EACjC,IAAI,uCAAuC,CAAC,YAAY,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAClJ,CAAC;QACF,IAAI,CAAC,aAAa,CACd,YAAY,EACZ,qCAAqC,CAAC,MAAM,EAC5C,IAAI,EACJ,wBAAwB,CAAC,QAAQ,EACjC,IAAI,uCAAuC,CAAC,YAAY,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAClJ,CAAC;QACF,IAAI,CAAC,aAAa,CACd,aAAa,EACb,qCAAqC,CAAC,MAAM,EAC5C,IAAI,EACJ,wBAAwB,CAAC,QAAQ,EACjC,IAAI,uCAAuC,CAAC,aAAa,EAAE,IAAI,EAAE,oCAAoC,CAAC,KAAK,EAAE,gBAAgB,EAAE,kBAAkB,CAAC,CACrJ,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACnH,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACnH,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACpH,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACrH,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACjH,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACnH,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACpH,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACrH,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACjH,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACnH,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,qCAAqC,CAAC,MAAM,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACjH,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9G,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,qCAAqC,CAAC,KAAK,EAAE,wBAAwB,CAAC,QAAQ,CAAC,CAAC;IACjH,CAAC;IAmND;;;OAGG;IACI,UAAU,CAAC,KAA6B;QAC3C,KAAK,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;QAEjD,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAC/C,KAAK,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACtC,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAEzC,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAC/C,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAC5C,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAEpC,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAEpC,KAAK,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACxC,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAC9C,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAExC,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAC7C,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACpC,KAAK,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACrC,KAAK,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;QACjD,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAC9C,KAAK,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;QAChD,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAClC,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAElC,KAAK,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;QAClD,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAC/C,KAAK,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,CAAC;QAEpD,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACtC,KAAK,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QACnC,KAAK,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAErC,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,kCAAkC,CAAC,CAAC;QAE/D,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAEzC,KAAK,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;QACjD,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,2BAA2B,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IAEM,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,EAAE,CAAC,IAAI;QACnH,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE;YAClC,IAAI,mBAAmB,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,wBAAwB,CAAC,cAAc,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5J,IAAI,CAAC,mBAAmB,EAAE;gBACtB,mBAAmB,GAAG,IAAI,UAAU,CAAC,gBAAgB,CAAC,CAAC;gBACvD,mBAAmB,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;aACjF;YACD,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC7D;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACxB,IAAI,SAAS,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,wBAAwB,CAAC,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAExI,IAAI,CAAC,SAAS,EAAE;gBACZ,SAAS,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;gBACnC,SAAS,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;aAC7D;YACD,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzC;IACL,CAAC;IAEM,cAAc,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QAC9F,UAAU;QACV,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC9B,OAAO,CAAC,QAAQ,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAC3C,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACpD,OAAO,CAAC,QAAQ,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;QAChD,OAAO,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QACxC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACjE,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,CAAC;QAEvF,mBAAmB;QACnB,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACxC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAE5D,oBAAoB;QACpB,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACxC,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEpD,eAAe;QACf,OAAO,CAAC,QAAQ,CAAC,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC9C,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACtD,OAAO,CAAC,QAAQ,CAAC,+BAA+B,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC/D,OAAO,CAAC,QAAQ,CAAC,+BAA+B,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC/D,OAAO,CAAC,QAAQ,CAAC,+BAA+B,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAE/D,oBAAoB;QACpB,IAAI,IAAI,CAAC,YAAY,KAAK,eAAe,CAAC,qBAAqB,EAAE;YAC7D,OAAO,CAAC,QAAQ,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,CAAC,QAAQ,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;SAClD;aAAM,IAAI,IAAI,CAAC,YAAY,KAAK,eAAe,CAAC,iBAAiB,EAAE;YAChE,OAAO,CAAC,QAAQ,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,CAAC,QAAQ,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;SACjD;aAAM;YACH,OAAO,CAAC,QAAQ,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;YAClD,OAAO,CAAC,QAAQ,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;SAClD;QAED,eAAe;QACf,MAAM,qBAAqB,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;QAE9D,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAC5D,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACjD,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QACvD,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,GAAG,CAAC,CAAC,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACvI,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAE5C,YAAY;QACZ,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QACvE,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QACvE,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC;QAC/H,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAErE,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,MAAM,CAAC,SAAS,CAAC,+BAA+B,EAAE;YAClD,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,wBAAwB,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC;SAC9E;aAAM;YACH,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE,GAAG,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;SAC7E;QAED,WAAW;QACX,OAAO,CAAC,QAAQ,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC;QACnD,OAAO,CAAC,QAAQ,CAAC,6BAA6B,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAClF,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QACvE,OAAO,CAAC,QAAQ,CAAC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QACrE,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC5C,OAAO,CAAC,QAAQ,CAAC,oBAAoB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAEtE,IAAI,IAAI,CAAC,uBAAuB,IAAI,aAAa,CAAC,wBAAwB,EAAE;YACxE,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;YAC1C,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SACvF;aAAM;YACH,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;YAC3C,OAAO,CAAC,QAAQ,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;SACnD;QAED,IAAI,OAAO,CAAC,wBAAwB,IAAI,YAAY,CAAC,4BAA4B,EAAE;YAC/E,YAAY,CAAC,4BAA4B,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;SACrE;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC1B,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,SAAS;YACT,uBAAuB,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,CAAC,qBAAqB,CAAC,CAAC;YACxF,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;YAE5B,YAAY;YACZ,0BAA0B,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;SAC9C;aAAM;YACH,MAAM,KAAK,GAAG;gBACV,WAAW,EAAE,KAAK;gBAClB,WAAW,EAAE,KAAK;gBAClB,YAAY,EAAE,KAAK;gBACnB,aAAa,EAAE,KAAK;gBACpB,eAAe,EAAE,KAAK;aACzB,CAAC;YAEF,sBAAsB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAErF,IAAI,KAAK,CAAC,WAAW,EAAE;gBACnB,OAAO,CAAC,OAAO,EAAE,CAAC;aACrB;SACJ;IACL,CAAC;IAEM,wBAAwB,CAAC,KAA6B,EAAE,YAA0B,EAAE,OAA4B,EAAE,cAAwB;QAC7I,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,YAAY,CAAC,qBAAqB,EAAE,UAAU,EAAE,EAAE;YACpF,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE;gBAChC,MAAM;aACT;YACD,MAAM,qBAAqB,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;YACrF,kCAAkC,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,uBAAuB,GAAG,UAAU,CAAC,EAAE,cAAc,EAAE,qBAAqB,CAAC,CAAC;SACxK;IACL,CAAC;IAEM,OAAO,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QACvF,IAAI,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,EAAE;YACzE,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,OAAO,CAAC,wBAAwB,IAAI,YAAY,CAAC,4BAA4B,EAAE;YAC/E,IAAI,CAAC,YAAY,CAAC,4BAA4B,CAAC,OAAO,EAAE,EAAE;gBACtD,OAAO,KAAK,CAAC;aAChB;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW;QAC/D,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,CAAC,qBAAqB,CAAC,CAAC;SAC7E;aAAM;YACH,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;SAC7D;QAED,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,2BAA2B,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAElF,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAElE,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAE9C,IAAI,YAAY,EAAE;YACd,MAAM,CAAC,SAAS,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;SACtD;QAED,MAAM,YAAY,GAAG,KAAK,CAAC,oBAAoB,KAAK,CAAC,KAAK,CAAC,uBAAuB,IAAI,IAAI,CAAC,CAAC;QAE5F,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/D,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEtJ,wBAAwB;QACxB,MAAM,UAAU,GAAG,CAAC,CAAC,CAAC,yEAAyE;QAC/F,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,KAAK,IAAI,GAAG,CAAC;QAEnE,8FAA8F;QAC9F,mDAAmD;QACnD,mBAAmB;QACnB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;QAEhE,wDAAwD;QACxD,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5F,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAE3C,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,gCAAgC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QAE1F,IAAI,YAAY,CAAC,4BAA4B,EAAE;YAC3C,YAAY,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC1D;IACL,CAAC;IAEO,iBAAiB,CAAC,KAA6B;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;QACpC,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAElC,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,sBAAsB;YACtB,KAAK,CAAC,wBAAwB,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,4BAA4B,EAAE,QAAQ,EAAE;gBAC3H,SAAS,EAAE,uBAAuB;aACrC,CAAC,CAAC;YACH,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;YAElB,KAAK,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACpD;aAAM;YACH,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACzG,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YAE/C,KAAK,CAAC,wBAAwB,CAC1B,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,4BAA4B,EACpF,QAAQ,EACR;gBACI,cAAc,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC;aAC1E,EACD,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAC3B,CAAC;SACL;QAED,wBAAwB;QACxB,MAAM,mBAAmB,GAAG,IAAI,GAAG,QAAQ,CAAC,sBAAsB,CAAC;QACnE,IAAI,KAAK,CAAC,sBAAsB,CAAC,mBAAmB,EAAE,MAAM,CAAC,EAAE;YAC3D,KAAK,CAAC,iBAAiB,IAAI,GAAG,mBAAmB,MAAM,QAAQ,CAAC,sBAAsB,KAAK,CAAC;SAC/F;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,UAA8B,CAAC,CAAC,CAAC,IAAI,CAAC;QAE7H,IAAI,eAAe,EAAE;YACjB,eAAe,CAAC,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC;SACnD;QAED,KAAK,CAAC,iBAAiB,IAAI,eAAe,EAAE,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAE1E,IAAI,KAAK,CAAC,sBAAsB,CAAC,oBAAoB,EAAE,MAAM,EAAE,kCAAkC,CAAC,EAAE;YAChG,KAAK,CAAC,YAAY,IAAI,qBAAqB,CAAC;YAC5C,KAAK,CAAC,YAAY,IAAI,qCAAqC,CAAC;YAC5D,KAAK,CAAC,YAAY,IAAI,UAAU,CAAC;SACpC;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,cAAc,EAAE;oBACZ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE;oBACrD,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC,sBAAsB,EAAE;iBACpE;aACJ,CAAC,CAAC;SACN;aAAM;YACH,KAAK,CAAC,iBAAiB,IAAI,mBAAmB,QAAQ,CAAC,sBAAsB,KAAK,CAAC;YACnF,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACvB,KAAK,CAAC,iBAAiB,IAAI,eAAe,IAAI,CAAC,IAAI,CAAC,sBAAsB,KAAK,CAAC;aACnF;YACD,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,SAAS,EAAE,uBAAuB;aACrC,CAAC,CAAC;SACN;IACL,CAAC;IAEO,qBAAqB;QACzB,IAAI,IAAI,GAAG,4CAA4C,CAAC;QAExD,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC;QACpG,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAEtF,IAAI,IAAI;uBACO,WAAW;;;;;;uBAMX,OAAO;;;;;;;oDAOsB,CAAC;QAE7C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,wBAAwB;QAC5B,IAAI,IAAI,GAAG,oCAAoC,CAAC;QAEhD,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC;QAEvF,IAAI,IAAI;;uBAEO,EAAE;;;;iBAIR,CAAC;QAEV,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB,CAAC,KAA6B;QACtD,IAAI,IAAI,GAAG,0CAA0C,CAAC;QAEtD,MAAM,WAAW,GAAG,IAAI,CAAC;QAEzB,IAAI,CAAC,gCAAgC,GAAG,KAAK,CAAC,oBAAoB,CAAC,6BAA6B,CAAC,CAAC;QAClG,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,gCAAgC,EAAE,MAAM,CAAC,CAAC;QAE5E,IAAI,IAAI;;;uBAGO,IAAI,CAAC,QAAQ,CAAC,sBAAsB,KAAK,IAAI,CAAC,SAAS,CAAC,sBAAsB;;;kBAGnF,IAAI,CAAC,gCAAgC;;;+BAGxB,WAAW;;;;;;;;;;;;;;;;;;;;qBAoBrB,CAAC;QAEd,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;QAErC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,IAAI,CAAC,uBAAuB,GAAG,yBAAyB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACzE;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,UAA8B,CAAC,CAAC,CAAC,IAAI,CAAC;QAE7H,IAAI,eAAe,EAAE;YACjB,kEAAkE;YAClE,eAAe,CAAC,4BAA4B,GAAG,IAAI,CAAC,aAAa,CAAC;YAClE,eAAe,CAAC,6BAA6B,GAAG,IAAI,CAAC,cAAc,CAAC;YACpE,eAAe,CAAC,0BAA0B,GAAG,IAAI,CAAC,WAAW,CAAC;YAC9D,eAAe,CAAC,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC;SACnD;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACpD,SAAS;YACT,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAE9B,OAAO,IAAI,CAAC;SACf;QAED,WAAW;QACX,KAAK,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,KAAK,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACpD;QAED,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC;QAE3C,IAAI,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC;QAChE,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,eAAe,GAAG,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;YAC/D,KAAK,CAAC,aAAa,CAAC,oBAAoB,EAAE,QAAQ,eAAe,KAAK,EAAE,QAAQ,CAAC,CAAC;YAClF,KAAK,CAAC,iBAAiB,IAAI,GAAG,eAAe,MAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,SAAS,CAAC;YAEtG,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,SAAS,EAAE,uBAAuB;gBAClC,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC,SAAS;aACxH,CAAC,CAAC;YAEH,KAAK,CAAC,iBAAiB,IAAI,qBAAqB,CAAC;YACjD,KAAK,CAAC,iBAAiB,IAAI,8FAA8F,CAAC;YAC1H,KAAK,CAAC,iBAAiB,IAAI,UAAU,CAAC;SACzC;aAAM;YACH,eAAe,GAAG,IAAI,GAAG,eAAe,CAAC;SAC5C;QAED,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,CAAC;QAExF,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAEvD,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC;QAC7G,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC,YAAY,CAAC;QAEvG,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,+CAA+C,EAAE,+BAA+B,CAAC,CAAC;QAC9G,KAAK,CAAC,cAAc,CAAC,aAAa,EAAE,iDAAiD,CAAC,CAAC;QAEvF,KAAK,CAAC,sBAAsB,CAAC,YAAY,EAAE,MAAM,EAAE,kCAAkC,CAAC,CAAC;QACvF,KAAK,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;QAEzD,4BAA4B;QAC5B,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACtC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAChC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC1C,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACzC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACzC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACjD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACjD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACxC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC9C,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEvC,EAAE;QACF,WAAW;QACX,EAAE;QACF,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,sBAAsB;YACtB,KAAK,CAAC,wBAAwB,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,0BAA0B,EAAE,QAAQ,EAAE;gBACvH,SAAS,EAAE,uBAAuB;gBAClC,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;aAC3E,CAAC,CAAC;SACN;aAAM;YACH,KAAK,CAAC,wBAAwB,CAC1B,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,0BAA0B,EAChF,QAAQ,EACR;gBACI,cAAc,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC;aAC1E,EACD,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAC3B,CAAC;SACL;QAED,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAC5D,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;QAC/D,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;QAC/D,KAAK,CAAC,wBAAwB,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC;QACvE,KAAK,CAAC,wBAAwB,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC;QAErE,KAAK,CAAC,wBAAwB,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC;QAErE,KAAK,CAAC,wBAAwB,CAAC,iCAAiC,EAAE,QAAQ,EAAE;YACxE,cAAc,EAAE,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,eAAe,GAAG,MAAM,EAAE,CAAC;SACjF,CAAC,CAAC;QAEH,KAAK,CAAC,wBAAwB,CAAC,mCAAmC,EAAE,QAAQ,CAAC,CAAC;QAC9E,KAAK,CAAC,wBAAwB,CAAC,kBAAkB,EAAE,QAAQ,EAAE;YACzD,cAAc,EAAE,CAAC,EAAE,MAAM,EAAE,uBAAuB,EAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,IAAI,sBAAsB,EAAE,CAAC;SAC/H,CAAC,CAAC;QACH,KAAK,CAAC,wBAAwB,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;QAElE,KAAK,CAAC,wBAAwB,CAAC,4BAA4B,EAAE,QAAQ,EAAE;YACnE,cAAc,EAAE,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,eAAe,GAAG,MAAM,EAAE,CAAC;SACjF,CAAC,CAAC;QAEH,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAE5D,KAAK,CAAC,wBAAwB,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;QAClE,KAAK,CAAC,wBAAwB,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;QACjE,KAAK,CAAC,wBAAwB,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC;QACrE,KAAK,CAAC,wBAAwB,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;QACjE,KAAK,CAAC,wBAAwB,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;QAEhE,EAAE;QACF,OAAO;QACP,EAAE;QAEF,KAAK,CAAC,sBAAsB,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;QAE3D,IAAI,eAAe,EAAE,wBAAwB,EAAE;YAC3C,KAAK,CAAC,iBAAiB,IAAI,eAAe,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;SACtE;QAED,kFAAkF;QAClF,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAE5D,KAAK,CAAC,iBAAiB,IAAI,QAAQ,IAAI,CAAC,aAAa,gBAAgB,IAAI,CAAC,WAAW,CAAC,sBAAsB,MAAM,CAAC;QAEnH,IAAI,KAAK,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,EAAE;YAC/C,KAAK,CAAC,iBAAiB,IAAI,mCAAmC,IAAI,CAAC,cAAc,CAAC,sBAAsB,MAAM,eAAe,UAAU,CAAC;SAC3I;QAED,KAAK,CAAC,iBAAiB,IAAI,2BAA2B,IAAI,CAAC,aAAa,SAAS,CAAC;QAElF,KAAK,CAAC,iBAAiB,IAAI,kBAAkB,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,GAAG,aAAa,CAAC,sBAAsB,GAAG,OAAO,CAAC,CAAC,CAAC,kBAAkB,KAAK,CAAC;QAEjK,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAEpE,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;QAE9D,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,qBAAqB,EAAE,QAAQ,EAAE;YACnF,cAAc,EAAE;gBACZ,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,eAAe,GAAG,MAAM,EAAE;gBAC5D,EAAE,MAAM,EAAE,iBAAiB,EAAE,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAAE;aACjE;SACJ,CAAC,CAAC;QAEH,gFAAgF;QAChF,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAExD,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAEhF,oEAAoE;QACpE,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAE3D,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;QAExF,uEAAuE;QACvE,KAAK,CAAC,iBAAiB,IAAI;;oBAEf,CAAC;QAEb,6EAA6E;QAC7E,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAE5D,gFAAgF;QAChF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,QAAQ,EAAE;YACpF,cAAc,EAAE;gBACZ,EAAE,MAAM,EAAE,uBAAuB,EAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,IAAI,sBAAsB,EAAE;gBAC1G,EAAE,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,kBAAkB,EAAE;aACjG;SACJ,CAAC,CAAC;QAEH,mFAAmF;QACnF,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,UAA8B,CAAC,CAAC,CAAC,IAAI,CAAC;QAE7H,IAAI,eAAe,EAAE;YACjB,eAAe,CAAC,4BAA4B,GAAG,IAAI,CAAC,aAAa,CAAC;YAClE,eAAe,CAAC,0BAA0B,GAAG,IAAI,CAAC,WAAW,CAAC;YAE9D,KAAK,CAAC,iBAAiB,IAAI,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;SAChG;QAED,mFAAmF;QACnF,IAAI,eAAe,IAAI,eAAe,CAAC,UAAU,EAAE;YAC/C,KAAK,CAAC,iBAAiB,IAAI,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;SAC/H;QAED,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,QAAQ,EAAE;YAC3D,cAAc,EAAE;gBACZ,EAAE,MAAM,EAAE,0BAA0B,EAAE,OAAO,EAAE,4BAA4B,EAAE;gBAC7E,EAAE,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,kBAAkB,EAAE;gBAC9F,EAAE,MAAM,EAAE,0BAA0B,EAAE,OAAO,EAAE,eAAe,EAAE,gBAAgB,IAAI,yBAAyB,EAAE;gBAC/G,EAAE,MAAM,EAAE,2BAA2B,EAAE,OAAO,EAAE,eAAe,EAAE,qBAAqB,IAAI,0BAA0B,EAAE;gBACtH,EAAE,MAAM,EAAE,uBAAuB,EAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,IAAI,sBAAsB,EAAE;gBAC1G,EAAE,MAAM,EAAE,uBAAuB,EAAE,OAAO,EAAE,eAAe,EAAE,yBAAyB,IAAI,sBAAsB,EAAE;gBAClH,EAAE,MAAM,EAAE,2BAA2B,EAAE,OAAO,EAAE,eAAe,EAAE,+BAA+B,IAAI,0BAA0B,EAAE;gBAChI,EAAE,MAAM,EAAE,2BAA2B,EAAE,OAAO,EAAE,eAAe,EAAE,6BAA6B,IAAI,0BAA0B,EAAE;aACjI;SACJ,CAAC,CAAC;QAEH,mFAAmF;QACnF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,QAAQ,EAAE;YACpF,cAAc,EAAE,CAAC,EAAE,MAAM,EAAE,6BAA6B,EAAE,OAAO,EAAE,IAAI,CAAC,gCAAgC,EAAE,CAAC;SAC9G,CAAC,CAAC;QACH,wEAAwE;QACxE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,UAAyB,CAAC,CAAC,CAAC,IAAI,CAAC;QAEzG,IAAI,UAAU,EAAE;YACZ,KAAK,CAAC,iBAAiB,IAAI,UAAU,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;SAClE;QAED,KAAK,CAAC,wBAAwB,CAAC,eAAe,EAAE,QAAQ,EAAE;YACtD,cAAc,EAAE;gBACZ,EAAE,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,kBAAkB,EAAE;gBAC9F,EAAE,MAAM,EAAE,uBAAuB,EAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,IAAI,sBAAsB,EAAE;gBAC1G,EAAE,MAAM,EAAE,uBAAuB,EAAE,OAAO,EAAE,eAAe,EAAE,yBAAyB,IAAI,sBAAsB,EAAE;gBAClH,EAAE,MAAM,EAAE,2BAA2B,EAAE,OAAO,EAAE,eAAe,EAAE,+BAA+B,IAAI,0BAA0B,EAAE;aACnI;SACJ,CAAC,CAAC;QAEH,4EAA4E;QAC5E,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,UAA+B,CAAC,CAAC,CAAC,IAAI,CAAC;QACjI,KAAK,CAAC,iBAAiB,IAAI,gBAAgB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAEtE,KAAK,CAAC,wBAAwB,CAAC,qBAAqB,EAAE,QAAQ,EAAE;YAC5D,cAAc,EAAE,EAAE;SACrB,CAAC,CAAC;QAEH,wEAAwE;QACxE,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,UAA6B,CAAC,CAAC,CAAC,IAAI,CAAC;QACzH,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;QAC3F,MAAM,iCAAiC,GACnC,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,UAAiC,CAAA,CAAC,YAAY,EAAE,WAAW,CAAC;QAC1I,MAAM,8BAA8B,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,UAA8B,CAAA,CAAC,YAAY,CAAC,WAAW,CAAC;QAC/J,IAAI,aAAa,GAAG,iCAAiC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,8BAA8B,CAAC,CAAC;QAE/H,KAAK,CAAC,iBAAiB,IAAI,cAAc,CAAC,OAAO,CAC7C,KAAK,EACL,cAAc,EACd,eAAe,EACf,eAAe,EACf,gBAAgB,EAChB,aAAa,EACb,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAC1C,CAAC;QAEF,IAAI,gBAAgB,EAAE;YAClB,aAAa,GAAG,cAAc,EAAE,YAAY,CAAC,WAAW,IAAI,KAAK,CAAC;SACrE;QAED,KAAK,CAAC,wBAAwB,CAAC,mBAAmB,EAAE,QAAQ,EAAE;YAC1D,cAAc,EAAE;gBACZ,EAAE,MAAM,EAAE,0BAA0B,EAAE,OAAO,EAAE,4BAA4B,EAAE;gBAC7E,EAAE,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,kBAAkB,EAAE;gBAC9F,EAAE,MAAM,EAAE,0BAA0B,EAAE,OAAO,EAAE,eAAe,EAAE,gBAAgB,IAAI,yBAAyB,EAAE;gBAC/G,EAAE,MAAM,EAAE,2BAA2B,EAAE,OAAO,EAAE,eAAe,EAAE,qBAAqB,IAAI,0BAA0B,EAAE;gBACtH,EAAE,MAAM,EAAE,uBAAuB,EAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,IAAI,sBAAsB,EAAE;gBAC1G,EAAE,MAAM,EAAE,uBAAuB,EAAE,OAAO,EAAE,eAAe,EAAE,yBAAyB,IAAI,sBAAsB,EAAE;gBAClH,EAAE,MAAM,EAAE,2BAA2B,EAAE,OAAO,EAAE,eAAe,EAAE,+BAA+B,IAAI,0BAA0B,EAAE;gBAChI,EAAE,MAAM,EAAE,qBAAqB,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,EAAE;aACrG;SACJ,CAAC,CAAC;QAEH,wFAAwF;QACxF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,qBAAqB,EAAE,QAAQ,EAAE;YACnF,cAAc,EAAE;gBACZ,EAAE,MAAM,EAAE,uBAAuB,EAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,IAAI,sBAAsB,EAAE;gBAC1G,EAAE,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,kBAAkB,EAAE;aACjG;SACJ,CAAC,CAAC;QAEH,wFAAwF;QACxF,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,UAA8B,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7H,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW;YAC/C,CAAC,CAAE,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,UAA8B,CAAA,CAAC,UAAU,CAAC,cAAc,EAAE,UAA8B;YAC5H,CAAC,CAAC,IAAI,CAAC;QAEX,IAAI,eAAe,EAAE;YACjB,eAAe,CAAC,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC;YAChD,eAAe,CAAC,gCAAgC,GAAG,IAAI,CAAC,iBAAiB,CAAC;SAC7E;QAED,KAAK,CAAC,iBAAiB,IAAI,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;QAE7G,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,QAAQ,EAAE;YAC3D,cAAc,EAAE;gBACZ,EAAE,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,kBAAkB,EAAE;gBAC9F,EAAE,MAAM,EAAE,0BAA0B,EAAE,OAAO,EAAE,eAAe,EAAE,gBAAgB,IAAI,yBAAyB,EAAE;gBAC/G,EAAE,MAAM,EAAE,2BAA2B,EAAE,OAAO,EAAE,eAAe,EAAE,qBAAqB,IAAI,0BAA0B,EAAE;gBACtH,EAAE,MAAM,EAAE,sBAAsB,EAAE,OAAO,EAAE,eAAe,EAAE,aAAa,IAAI,qBAAqB,EAAE;gBACpG,EAAE,MAAM,EAAE,0BAA0B,EAAE,OAAO,EAAE,eAAe,EAAE,yBAAyB,IAAI,yBAAyB,EAAE;gBACxH,EAAE,MAAM,EAAE,8BAA8B,EAAE,OAAO,EAAE,eAAe,EAAE,+BAA+B,IAAI,6BAA6B,EAAE;gBACtI,EAAE,MAAM,EAAE,6BAA6B,EAAE,OAAO,EAAE,eAAe,EAAE,gBAAgB,IAAI,4BAA4B,EAAE;aACxH;SACJ,CAAC,CAAC;QAEH,wFAAwF;QACxF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC;QAE1F,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,cAAc,EAAE;oBACZ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE;oBACrD,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,eAAe,GAAG,MAAM,EAAE;iBAC/D;aACJ,CAAC,CAAC;SACN;aAAM;YACH,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,SAAS,EAAE,uBAAuB;gBAClC,gBAAgB,EAAE,cAAc,eAAe,MAAM;aACxD,CAAC,CAAC;SACN;QAED,sFAAsF;QACtF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC;QAE9F,mEAAmE;QACnE,KAAK,CAAC,iBAAiB,IAAI,UAAU,CAAC,CAAC,QAAQ;QAE/C,wFAAwF;QACxF,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,kBAAkB,CAAC;QAE9G,IAAI,sBAAsB,GAAG,eAAe,CAAC,+BAA+B,CAAC,QAAQ,EAAE,CAAC;QAExF,IAAI,sBAAsB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5C,sBAAsB,IAAI,GAAG,CAAC;SACjC;QAED,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,8BAA8B,EAAE,QAAQ,EAAE;YAC5F,cAAc,EAAE;gBACZ,EAAE,MAAM,EAAE,mEAAmE,EAAE,OAAO,EAAE,EAAE,EAAE;gBAC5F,EAAE,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,GAAG,qBAAqB,EAAE;gBACtE,EAAE,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,sBAAsB,EAAE;aACnE;SACJ,CAAC,CAAC;QAEH,wFAAwF;QACxF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,+BAA+B,EAAE,QAAQ,EAAE;YAC7F,cAAc,EAAE,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;SACtE,CAAC,CAAC;QAEH,gFAAgF;QAChF,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,yBAAyB,EAAE,QAAQ,EAAE;YACvF,cAAc,EAAE,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;SAC7D,CAAC,CAAC;QAEH,6EAA6E;QAC7E,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,UAAU,EAAE,QAAQ,EAAE;YACxE,cAAc,EAAE;gBACZ,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE;gBACpD,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,eAAe,EAAE;gBACnD,EAAE,MAAM,EAAE,sBAAsB,EAAE,OAAO,EAAE,iEAAiE,EAAE;aACjH;SACJ,CAAC,CAAC;QAEH,6EAA6E;QAC7E,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,MAAM,CAAC,YAAY,EAAE;gBACrB,MAAM,KAAK,GAAG,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC/C,IAAI,KAAK,EAAE;oBACP,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,KAAK,CAAC;oBACpC,IAAI,UAAU,EAAE;wBACZ,KAAK,CAAC,iBAAiB,IAAI,OAAO,UAAU,IAAI,CAAC;qBACpD;oBACD,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC;oBACnF,IAAI,UAAU,EAAE;wBACZ,KAAK,CAAC,iBAAiB,IAAI,SAAS,CAAC;wBACrC,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,gBAAgB,CAAC;wBACjF,KAAK,CAAC,iBAAiB,IAAI,UAAU,CAAC;qBACzC;iBACJ;qBAAM;oBACH,MAAM,CAAC,KAAK,CAAC,gCAAgC,MAAM,CAAC,IAAI,+BAA+B,CAAC,CAAC;iBAC5F;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,mBAAmB,IAAI,CAAC,YAAY,KAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,mBAAmB,IAAI,CAAC,YAAY,KAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,sBAAsB,IAAI,CAAC,eAAe,KAAK,CAAC;QACvF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,uBAAuB,IAAI,CAAC,gBAAgB,KAAK,CAAC;QACzF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,2BAA2B,IAAI,CAAC,oBAAoB,KAAK,CAAC;QACjG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,2BAA2B,IAAI,CAAC,oBAAoB,KAAK,CAAC;QACjG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,iCAAiC,IAAI,CAAC,0BAA0B,KAAK,CAAC;QAC7G,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,wBAAwB,IAAI,CAAC,iBAAiB,KAAK,CAAC;QAC3F,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,+BAA+B,IAAI,CAAC,wBAAwB,KAAK,CAAC;QACzG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,4BAA4B,IAAI,CAAC,qBAAqB,KAAK,CAAC;QACnG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,2BAA2B,IAAI,CAAC,oBAAoB,KAAK,CAAC;QACjG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,0BAA0B,IAAI,CAAC,mBAAmB,KAAK,CAAC;QAC/F,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,YAAY,IAAI,CAAC,KAAK,KAAK,CAAC;QACnE,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,yBAAyB,IAAI,CAAC,kBAAkB,KAAK,CAAC;QAC7F,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,gBAAgB,IAAI,CAAC,SAAS,KAAK,CAAC;QAC3E,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,iBAAiB,IAAI,CAAC,UAAU,KAAK,CAAC;QAC7E,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,kBAAkB,IAAI,CAAC,WAAW,KAAK,CAAC;QAE/E,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;SAC/C;QAED,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACrD,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACrD,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC3D,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC7D,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,0BAA0B,GAAG,IAAI,CAAC,0BAA0B,CAAC;QACjF,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC/D,mBAAmB,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAC7E,mBAAmB,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACvE,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACnE,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvC,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACjE,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACnD,mBAAmB,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAE7E,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,mBAAmB,CAAC,OAAO,EAAE;YAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;SAChE;QAED,IAAI,CAAC,YAAY,GAAG,mBAAmB,CAAC,YAAY,IAAI,CAAC,CAAC;QAC1D,IAAI,CAAC,YAAY,GAAG,mBAAmB,CAAC,YAAY,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC,eAAe,CAAC;QAC3D,IAAI,CAAC,gBAAgB,GAAG,mBAAmB,CAAC,gBAAgB,CAAC;QAC7D,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC,oBAAoB,CAAC;QACrE,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC,oBAAoB,CAAC;QACrE,IAAI,CAAC,0BAA0B,GAAG,mBAAmB,CAAC,0BAA0B,CAAC;QACjF,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;QACjE,IAAI,CAAC,wBAAwB,GAAG,mBAAmB,CAAC,wBAAwB,IAAI,SAAS,CAAC,6BAA6B,CAAC;QACxH,IAAI,CAAC,qBAAqB,GAAG,mBAAmB,CAAC,qBAAqB,CAAC;QACvE,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC,oBAAoB,CAAC;QACrE,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,mBAAmB,CAAC;QACnE,IAAI,CAAC,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC;QACvC,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,mBAAmB,CAAC,kBAAkB,CAAC;QACnE,IAAI,CAAC,SAAS,GAAG,mBAAmB,CAAC,SAAS,CAAC;QAC/C,IAAI,CAAC,UAAU,GAAG,mBAAmB,CAAC,UAAU,CAAC;QACjD,IAAI,CAAC,WAAW,GAAG,mBAAmB,CAAC,WAAW,CAAC;QACnD,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC,mBAAmB,CAAC,wBAAwB,CAAC;QAE/E,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;CACJ;AA/xCU;IADN,sBAAsB,CAAC,eAAe,EAAE,sBAAsB,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;kEAC/F;AAO9B;IADN,sBAAsB,CAAC,oBAAoB,EAAE,sBAAsB,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;uEAC/F;AAOnC;IADN,sBAAsB,CAAC,qBAAqB,EAAE,sBAAsB,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;oEACnG;AAchC;IARN,sBAAsB,CAAC,eAAe,EAAE,sBAAsB,CAAC,IAAI,EAAE,mBAAmB,EAAE;QACvF,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;QAC3B,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,eAAe,CAAC,qBAAqB,EAAE;YACnE,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,eAAe,CAAC,iBAAiB,EAAE;YAC3D,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,eAAe,CAAC,qBAAqB,EAAE;SACtE;KACJ,CAAC;+DACsB;AAMjB;IADN,sBAAsB,CAAC,eAAe,EAAE,sBAAsB,CAAC,OAAO,EAAE,SAAS,CAAC;+DAC9C;AAM9B;IADN,sBAAsB,CAAC,cAAc,EAAE,sBAAsB,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;kEAC5F;AAM9B;IADN,sBAAsB,CAAC,gBAAgB,EAAE,sBAAsB,CAAC,OAAO,EAAE,SAAS,CAAC;mEAC3C;AAOlC;IADN,sBAAsB,CAAC,qBAAqB,EAAE,sBAAsB,CAAC,OAAO,EAAE,WAAW,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;uEAChF;AAOrC;IADN,sBAAsB,CAAC,qBAAqB,EAAE,sBAAsB,CAAC,OAAO,EAAE,WAAW,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;uEAChF;AAQrC;IADN,sBAAsB,CAAC,wBAAwB,EAAE,sBAAsB,CAAC,OAAO,EAAE,WAAW,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;6EAC5E;AAM5C;IADN,sBAAsB,CAAC,oBAAoB,EAAE,sBAAsB,CAAC,OAAO,EAAE,WAAW,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;oEACjF;AAanC;IARN,sBAAsB,CAAC,4BAA4B,EAAE,sBAAsB,CAAC,IAAI,EAAE,WAAW,EAAE;QAC5F,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;QAC3B,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,6BAA6B,EAAE;YAChE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,gCAAgC,EAAE;YACtE,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,8BAA8B,EAAE;SACrE;KACJ,CAAC;2EACwE;AAMnE;IADN,sBAAsB,CAAC,qBAAqB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;wEAC9E;AAOtC;IADN,sBAAsB,CAAC,oBAAoB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;uEAC9E;AAOrC;IADN,sBAAsB,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;sEAC9E;AAMpC;IADN,sBAAsB,CAAC,OAAO,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;wDAC/E;AAMvB;IADN,sBAAsB,CAAC,sBAAsB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;qEACjF;AAMpC;IAHN,sBAAsB,CAAC,6BAA6B,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE;QAC/F,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,yBAAyB,CAAC,kCAAkC,EAAE;KACzH,CAAC;2EACsC;AA6DjC;IAvDN,sBAAsB,CAAC,YAAY,EAAE,sBAAsB,CAAC,IAAI,EAAE,OAAO,EAAE;QACxE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;QAC3B,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE;YAC3B,WAAW;YACX,EAAE,KAAK,EAAE,qBAAqB,EAAE,KAAK,EAAE,CAAC,EAAE;YAC1C,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE;YAC9B,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,EAAE;YAC/B,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE;YACjC,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC,EAAE;YACnC,6BAA6B;YAC7B,6BAA6B;YAC7B,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,EAAE,CAAC,EAAE;YACxC,EAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC,EAAE;YACzC,EAAE,KAAK,EAAE,sBAAsB,EAAE,KAAK,EAAE,EAAE,EAAE;YAC5C,EAAE,KAAK,EAAE,qBAAqB,EAAE,KAAK,EAAE,EAAE,EAAE;YAC3C,EAAE,KAAK,EAAE,sBAAsB,EAAE,KAAK,EAAE,EAAE,EAAE;YAC5C,EAAE,KAAK,EAAE,wBAAwB,EAAE,KAAK,EAAE,EAAE,EAAE;YAC9C,OAAO;YACP,uCAAuC;YACvC,oCAAoC;YACpC,MAAM;YACN,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,EAAE,EAAE;YACtC,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,EAAE,EAAE;YACtC,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,EAAE,EAAE;YACtC,WAAW;YACX,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,EAAE,EAAE;YACtC,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,EAAE,EAAE;YACvC,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,EAAE,EAAE,EAAE;YACzC,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,EAAE,EAAE;YACpC,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,EAAE,EAAE;YACtC,kBAAkB;YAClB,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,EAAE,EAAE;YACtC,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,EAAE,EAAE;YACrC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE;YAChC,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,EAAE,EAAE;YACnC,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,EAAE,EAAE;YACjC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE;YAC9B,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE;YAC7B,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,EAAE,EAAE;YACvC,EAAE,KAAK,EAAE,qBAAqB,EAAE,KAAK,EAAE,EAAE,EAAE;YAC3C,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,EAAE,EAAE;YACvC,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,EAAE,EAAE;YACrC,EAAE,KAAK,EAAE,0BAA0B,EAAE,KAAK,EAAE,EAAE,EAAE;YAChD,OAAO;YACP,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE;YAC3B,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE;YAC3B,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,EAAE,EAAE;YACrC,EAAE,KAAK,EAAE,sBAAsB,EAAE,KAAK,EAAE,EAAE,EAAE;YAC5C,EAAE,KAAK,EAAE,wBAAwB,EAAE,KAAK,EAAE,EAAE,EAAE;YAC9C,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,EAAE,EAAE,EAAE;YACzC,EAAE,KAAK,EAAE,sBAAsB,EAAE,KAAK,EAAE,EAAE,EAAE;YAC5C,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE;SAChC;KACJ,CAAC;4DACmB;AASd;IADN,sBAAsB,CAAC,gBAAgB,EAAE,sBAAsB,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;6DAC5G;AAOf;IADN,sBAAsB,CAAC,eAAe,EAAE,sBAAsB,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;8DACzG;AAulC3B,aAAa,CAAC,mCAAmC,EAAE,yBAAyB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport type { Light } from \"../../../../Lights/light\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport { PBRBaseMaterial } from \"../../../PBR/pbrBaseMaterial\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport { SheenBlock } from \"./sheenBlock\";\r\nimport type { BaseTexture } from \"../../../Textures/baseTexture\";\r\nimport { GetEnvironmentBRDFTexture } from \"../../../../Misc/brdfTextureTools\";\r\nimport { MaterialFlags } from \"../../../materialFlags\";\r\nimport { AnisotropyBlock } from \"./anisotropyBlock\";\r\nimport { ReflectionBlock } from \"./reflectionBlock\";\r\nimport { ClearCoatBlock } from \"./clearCoatBlock\";\r\nimport { IridescenceBlock } from \"./iridescenceBlock\";\r\nimport { SubSurfaceBlock } from \"./subSurfaceBlock\";\r\nimport type { RefractionBlock } from \"./refractionBlock\";\r\nimport type { PerturbNormalBlock } from \"../Fragment/perturbNormalBlock\";\r\nimport { Constants } from \"../../../../Engines/constants\";\r\nimport { Color3, TmpColors } from \"../../../../Maths/math.color\";\r\nimport { Logger } from \"core/Misc/logger\";\r\nimport {\r\n    BindLight,\r\n    BindLights,\r\n    PrepareDefinesForLight,\r\n    PrepareDefinesForLights,\r\n    PrepareDefinesForMultiview,\r\n    PrepareUniformsAndSamplersForLight,\r\n} from \"../../../materialHelper.functions\";\r\n\r\nconst mapOutputToVariable: { [name: string]: [string, string] } = {\r\n    ambientClr: [\"finalAmbient\", \"\"],\r\n    diffuseDir: [\"finalDiffuse\", \"\"],\r\n    specularDir: [\"finalSpecularScaled\", \"!defined(UNLIT) && defined(SPECULARTERM)\"],\r\n    clearcoatDir: [\"finalClearCoatScaled\", \"!defined(UNLIT) && defined(CLEARCOAT)\"],\r\n    sheenDir: [\"finalSheenScaled\", \"!defined(UNLIT) && defined(SHEEN)\"],\r\n    diffuseInd: [\"finalIrradiance\", \"!defined(UNLIT) && defined(REFLECTION)\"],\r\n    specularInd: [\"finalRadianceScaled\", \"!defined(UNLIT) && defined(REFLECTION)\"],\r\n    clearcoatInd: [\"clearcoatOut.finalClearCoatRadianceScaled\", \"!defined(UNLIT) && defined(REFLECTION) && defined(CLEARCOAT)\"],\r\n    sheenInd: [\"sheenOut.finalSheenRadianceScaled\", \"!defined(UNLIT) && defined(REFLECTION) && defined(SHEEN) && defined(ENVIRONMENTBRDF)\"],\r\n    refraction: [\"subSurfaceOut.finalRefraction\", \"!defined(UNLIT) && defined(SS_REFRACTION)\"],\r\n    lighting: [\"finalColor.rgb\", \"\"],\r\n    shadow: [\"aggShadow\", \"\"],\r\n    alpha: [\"alpha\", \"\"],\r\n};\r\n\r\n/**\r\n * Block used to implement the PBR metallic/roughness model\r\n */\r\nexport class PBRMetallicRoughnessBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Gets or sets the light associated with this block\r\n     */\r\n    public light: Nullable<Light>;\r\n\r\n    private static _OnGenerateOnlyFragmentCodeChanged(block: NodeMaterialBlock, _propertyName: string): boolean {\r\n        const that = block as PBRMetallicRoughnessBlock;\r\n\r\n        if (that.worldPosition.isConnected) {\r\n            that.generateOnlyFragmentCode = !that.generateOnlyFragmentCode;\r\n            Logger.Error(\"The worldPosition input must not be connected to be able to switch!\");\r\n            return false;\r\n        }\r\n\r\n        that._setTarget();\r\n\r\n        return true;\r\n    }\r\n\r\n    private _setTarget(): void {\r\n        this._setInitialTarget(this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.VertexAndFragment);\r\n        this.getInputByName(\"worldPosition\")!.target = this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.Vertex;\r\n    }\r\n\r\n    private _lightId: number;\r\n    private _scene: Scene;\r\n    private _environmentBRDFTexture: Nullable<BaseTexture> = null;\r\n    private _environmentBrdfSamplerName: string;\r\n    private _vNormalWName: string;\r\n    private _invertNormalName: string;\r\n    private _metallicReflectanceColor: Color3 = Color3.White();\r\n    private _metallicF0Factor = 1;\r\n    private _vMetallicReflectanceFactorsName: string;\r\n\r\n    /**\r\n     * Create a new ReflectionBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.VertexAndFragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.Vector4, false, NodeMaterialBlockTargets.Vertex);\r\n        this.registerInput(\"worldNormal\", NodeMaterialBlockConnectionPointTypes.Vector4, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"view\", NodeMaterialBlockConnectionPointTypes.Matrix, false);\r\n        this.registerInput(\"cameraPosition\", NodeMaterialBlockConnectionPointTypes.Vector3, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"perturbedNormal\", NodeMaterialBlockConnectionPointTypes.Vector4, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"baseColor\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"metallic\", NodeMaterialBlockConnectionPointTypes.Float, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"roughness\", NodeMaterialBlockConnectionPointTypes.Float, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"ambientOcc\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"opacity\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"indexOfRefraction\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"ambientColor\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\r\n            \"reflection\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"reflection\", this, NodeMaterialConnectionPointDirection.Input, ReflectionBlock, \"ReflectionBlock\")\r\n        );\r\n        this.registerInput(\r\n            \"clearcoat\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"clearcoat\", this, NodeMaterialConnectionPointDirection.Input, ClearCoatBlock, \"ClearCoatBlock\")\r\n        );\r\n        this.registerInput(\r\n            \"sheen\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"sheen\", this, NodeMaterialConnectionPointDirection.Input, SheenBlock, \"SheenBlock\")\r\n        );\r\n        this.registerInput(\r\n            \"subsurface\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"subsurface\", this, NodeMaterialConnectionPointDirection.Input, SubSurfaceBlock, \"SubSurfaceBlock\")\r\n        );\r\n        this.registerInput(\r\n            \"anisotropy\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"anisotropy\", this, NodeMaterialConnectionPointDirection.Input, AnisotropyBlock, \"AnisotropyBlock\")\r\n        );\r\n        this.registerInput(\r\n            \"iridescence\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"iridescence\", this, NodeMaterialConnectionPointDirection.Input, IridescenceBlock, \"IridescenceBlock\")\r\n        );\r\n\r\n        this.registerOutput(\"ambientClr\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"diffuseDir\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"specularDir\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"clearcoatDir\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"sheenDir\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"diffuseInd\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"specularInd\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"clearcoatInd\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"sheenInd\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"refraction\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"lighting\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"shadow\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"alpha\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n    }\r\n\r\n    /**\r\n     * Intensity of the direct lights e.g. the four lights available in your scene.\r\n     * This impacts both the direct diffuse and specular highlights.\r\n     */\r\n    @editableInPropertyPage(\"Direct lights\", PropertyTypeForEdition.Float, \"INTENSITY\", { min: 0, max: 1, notifiers: { update: true } })\r\n    public directIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Intensity of the environment e.g. how much the environment will light the object\r\n     * either through harmonics for rough material or through the reflection for shiny ones.\r\n     */\r\n    @editableInPropertyPage(\"Environment lights\", PropertyTypeForEdition.Float, \"INTENSITY\", { min: 0, max: 1, notifiers: { update: true } })\r\n    public environmentIntensity: number = 1.0;\r\n\r\n    /**\r\n     * This is a special control allowing the reduction of the specular highlights coming from the\r\n     * four lights of the scene. Those highlights may not be needed in full environment lighting.\r\n     */\r\n    @editableInPropertyPage(\"Specular highlights\", PropertyTypeForEdition.Float, \"INTENSITY\", { min: 0, max: 1, notifiers: { update: true } })\r\n    public specularIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Defines the  falloff type used in this material.\r\n     * It by default is Physical.\r\n     */\r\n    @editableInPropertyPage(\"Light falloff\", PropertyTypeForEdition.List, \"LIGHTING & COLORS\", {\r\n        notifiers: { update: true },\r\n        options: [\r\n            { label: \"Physical\", value: PBRBaseMaterial.LIGHTFALLOFF_PHYSICAL },\r\n            { label: \"GLTF\", value: PBRBaseMaterial.LIGHTFALLOFF_GLTF },\r\n            { label: \"Standard\", value: PBRBaseMaterial.LIGHTFALLOFF_STANDARD },\r\n        ],\r\n    })\r\n    public lightFalloff = 0;\r\n\r\n    /**\r\n     * Specifies that alpha test should be used\r\n     */\r\n    @editableInPropertyPage(\"Alpha Testing\", PropertyTypeForEdition.Boolean, \"OPACITY\")\r\n    public useAlphaTest: boolean = false;\r\n\r\n    /**\r\n     * Defines the alpha limits in alpha test mode.\r\n     */\r\n    @editableInPropertyPage(\"Alpha CutOff\", PropertyTypeForEdition.Float, \"OPACITY\", { min: 0, max: 1, notifiers: { update: true } })\r\n    public alphaTestCutoff: number = 0.5;\r\n\r\n    /**\r\n     * Specifies that alpha blending should be used\r\n     */\r\n    @editableInPropertyPage(\"Alpha blending\", PropertyTypeForEdition.Boolean, \"OPACITY\")\r\n    public useAlphaBlending: boolean = false;\r\n\r\n    /**\r\n     * Specifies that the material will keeps the reflection highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good example of that. When the street lights reflects on it you can not see what is behind.\r\n     */\r\n    @editableInPropertyPage(\"Radiance over alpha\", PropertyTypeForEdition.Boolean, \"RENDERING\", { notifiers: { update: true } })\r\n    public useRadianceOverAlpha: boolean = true;\r\n\r\n    /**\r\n     * Specifies that the material will keeps the specular highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good example of that. When sun reflects on it you can not see what is behind.\r\n     */\r\n    @editableInPropertyPage(\"Specular over alpha\", PropertyTypeForEdition.Boolean, \"RENDERING\", { notifiers: { update: true } })\r\n    public useSpecularOverAlpha: boolean = true;\r\n\r\n    /**\r\n     * Enables specular anti aliasing in the PBR shader.\r\n     * It will both interacts on the Geometry for analytical and IBL lighting.\r\n     * It also prefilter the roughness map based on the bump values.\r\n     */\r\n    @editableInPropertyPage(\"Specular anti-aliasing\", PropertyTypeForEdition.Boolean, \"RENDERING\", { notifiers: { update: true } })\r\n    public enableSpecularAntiAliasing: boolean = false;\r\n\r\n    /**\r\n     * Enables realtime filtering on the texture.\r\n     */\r\n    @editableInPropertyPage(\"Realtime filtering\", PropertyTypeForEdition.Boolean, \"RENDERING\", { notifiers: { update: true } })\r\n    public realTimeFiltering: boolean = false;\r\n\r\n    /**\r\n     * Quality switch for realtime filtering\r\n     */\r\n    @editableInPropertyPage(\"Realtime filtering quality\", PropertyTypeForEdition.List, \"RENDERING\", {\r\n        notifiers: { update: true },\r\n        options: [\r\n            { label: \"Low\", value: Constants.TEXTURE_FILTERING_QUALITY_LOW },\r\n            { label: \"Medium\", value: Constants.TEXTURE_FILTERING_QUALITY_MEDIUM },\r\n            { label: \"High\", value: Constants.TEXTURE_FILTERING_QUALITY_HIGH },\r\n        ],\r\n    })\r\n    public realTimeFilteringQuality = Constants.TEXTURE_FILTERING_QUALITY_LOW;\r\n\r\n    /**\r\n     * Defines if the material uses energy conservation.\r\n     */\r\n    @editableInPropertyPage(\"Energy Conservation\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public useEnergyConservation: boolean = true;\r\n\r\n    /**\r\n     * This parameters will enable/disable radiance occlusion by preventing the radiance to lit\r\n     * too much the area relying on ambient texture to define their ambient occlusion.\r\n     */\r\n    @editableInPropertyPage(\"Radiance occlusion\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public useRadianceOcclusion: boolean = true;\r\n\r\n    /**\r\n     * This parameters will enable/disable Horizon occlusion to prevent normal maps to look shiny when the normal\r\n     * makes the reflect vector face the model (under horizon).\r\n     */\r\n    @editableInPropertyPage(\"Horizon occlusion\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public useHorizonOcclusion: boolean = true;\r\n\r\n    /**\r\n     * If set to true, no lighting calculations will be applied.\r\n     */\r\n    @editableInPropertyPage(\"Unlit\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public unlit: boolean = false;\r\n\r\n    /**\r\n     * Force normal to face away from face.\r\n     */\r\n    @editableInPropertyPage(\"Force normal forward\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { update: true } })\r\n    public forceNormalForward: boolean = false;\r\n\r\n    /** Indicates that no code should be generated in the vertex shader. Can be useful in some specific circumstances (like when doing ray marching for eg) */\r\n    @editableInPropertyPage(\"Generate only fragment code\", PropertyTypeForEdition.Boolean, \"ADVANCED\", {\r\n        notifiers: { rebuild: true, update: true, onValidation: PBRMetallicRoughnessBlock._OnGenerateOnlyFragmentCodeChanged },\r\n    })\r\n    public generateOnlyFragmentCode = false;\r\n\r\n    /**\r\n     * Defines the material debug mode.\r\n     * It helps seeing only some components of the material while troubleshooting.\r\n     */\r\n    @editableInPropertyPage(\"Debug mode\", PropertyTypeForEdition.List, \"DEBUG\", {\r\n        notifiers: { update: true },\r\n        options: [\r\n            { label: \"None\", value: 0 },\r\n            // Geometry\r\n            { label: \"Normalized position\", value: 1 },\r\n            { label: \"Normals\", value: 2 },\r\n            { label: \"Tangents\", value: 3 },\r\n            { label: \"Bitangents\", value: 4 },\r\n            { label: \"Bump Normals\", value: 5 },\r\n            //{ label: \"UV1\", value: 6 },\r\n            //{ label: \"UV2\", value: 7 },\r\n            { label: \"ClearCoat Normals\", value: 8 },\r\n            { label: \"ClearCoat Tangents\", value: 9 },\r\n            { label: \"ClearCoat Bitangents\", value: 10 },\r\n            { label: \"Anisotropic Normals\", value: 11 },\r\n            { label: \"Anisotropic Tangents\", value: 12 },\r\n            { label: \"Anisotropic Bitangents\", value: 13 },\r\n            // Maps\r\n            //{ label: \"Emissive Map\", value: 23 },\r\n            //{ label: \"Light Map\", value: 24 },\r\n            // Env\r\n            { label: \"Env Refraction\", value: 40 },\r\n            { label: \"Env Reflection\", value: 41 },\r\n            { label: \"Env Clear Coat\", value: 42 },\r\n            // Lighting\r\n            { label: \"Direct Diffuse\", value: 50 },\r\n            { label: \"Direct Specular\", value: 51 },\r\n            { label: \"Direct Clear Coat\", value: 52 },\r\n            { label: \"Direct Sheen\", value: 53 },\r\n            { label: \"Env Irradiance\", value: 54 },\r\n            // Lighting Params\r\n            { label: \"Surface Albedo\", value: 60 },\r\n            { label: \"Reflectance 0\", value: 61 },\r\n            { label: \"Metallic\", value: 62 },\r\n            { label: \"Metallic F0\", value: 71 },\r\n            { label: \"Roughness\", value: 63 },\r\n            { label: \"AlphaG\", value: 64 },\r\n            { label: \"NdotV\", value: 65 },\r\n            { label: \"ClearCoat Color\", value: 66 },\r\n            { label: \"ClearCoat Roughness\", value: 67 },\r\n            { label: \"ClearCoat NdotV\", value: 68 },\r\n            { label: \"Transmittance\", value: 69 },\r\n            { label: \"Refraction Transmittance\", value: 70 },\r\n            // Misc\r\n            { label: \"SEO\", value: 80 },\r\n            { label: \"EHO\", value: 81 },\r\n            { label: \"Energy Factor\", value: 82 },\r\n            { label: \"Specular Reflectance\", value: 83 },\r\n            { label: \"Clear Coat Reflectance\", value: 84 },\r\n            { label: \"Sheen Reflectance\", value: 85 },\r\n            { label: \"Luminance Over Alpha\", value: 86 },\r\n            { label: \"Alpha\", value: 87 },\r\n        ],\r\n    })\r\n    public debugMode = 0;\r\n\r\n    /**\r\n     * Specify from where on screen the debug mode should start.\r\n     * The value goes from -1 (full screen) to 1 (not visible)\r\n     * It helps with side by side comparison against the final render\r\n     * This defaults to 0\r\n     */\r\n    @editableInPropertyPage(\"Split position\", PropertyTypeForEdition.Float, \"DEBUG\", { min: -1, max: 1, notifiers: { update: true } })\r\n    public debugLimit = 0;\r\n\r\n    /**\r\n     * As the default viewing range might not be enough (if the ambient is really small for instance)\r\n     * You can use the factor to better multiply the final value.\r\n     */\r\n    @editableInPropertyPage(\"Output factor\", PropertyTypeForEdition.Float, \"DEBUG\", { min: 0, max: 5, notifiers: { update: true } })\r\n    public debugFactor = 1;\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"vLightingIntensity\");\r\n\r\n        state._excludeVariableName(\"geometricNormalW\");\r\n        state._excludeVariableName(\"normalW\");\r\n        state._excludeVariableName(\"faceNormal\");\r\n\r\n        state._excludeVariableName(\"albedoOpacityOut\");\r\n        state._excludeVariableName(\"surfaceAlbedo\");\r\n        state._excludeVariableName(\"alpha\");\r\n\r\n        state._excludeVariableName(\"aoOut\");\r\n\r\n        state._excludeVariableName(\"baseColor\");\r\n        state._excludeVariableName(\"reflectivityOut\");\r\n        state._excludeVariableName(\"microSurface\");\r\n        state._excludeVariableName(\"roughness\");\r\n\r\n        state._excludeVariableName(\"NdotVUnclamped\");\r\n        state._excludeVariableName(\"NdotV\");\r\n        state._excludeVariableName(\"alphaG\");\r\n        state._excludeVariableName(\"AARoughnessFactors\");\r\n        state._excludeVariableName(\"environmentBrdf\");\r\n        state._excludeVariableName(\"ambientMonochrome\");\r\n        state._excludeVariableName(\"seo\");\r\n        state._excludeVariableName(\"eho\");\r\n\r\n        state._excludeVariableName(\"environmentRadiance\");\r\n        state._excludeVariableName(\"irradianceVector\");\r\n        state._excludeVariableName(\"environmentIrradiance\");\r\n\r\n        state._excludeVariableName(\"diffuseBase\");\r\n        state._excludeVariableName(\"specularBase\");\r\n        state._excludeVariableName(\"preInfo\");\r\n        state._excludeVariableName(\"info\");\r\n        state._excludeVariableName(\"shadow\");\r\n\r\n        state._excludeVariableName(\"finalDiffuse\");\r\n        state._excludeVariableName(\"finalAmbient\");\r\n        state._excludeVariableName(\"ambientOcclusionForDirectDiffuse\");\r\n\r\n        state._excludeVariableName(\"finalColor\");\r\n\r\n        state._excludeVariableName(\"vClipSpacePosition\");\r\n        state._excludeVariableName(\"vDebugMode\");\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"PBRMetallicRoughnessBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the view matrix parameter\r\n     */\r\n    public get view(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the camera position input component\r\n     */\r\n    public get cameraPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the perturbed normal input component\r\n     */\r\n    public get perturbedNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the base color input component\r\n     */\r\n    public get baseColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the metallic input component\r\n     */\r\n    public get metallic(): NodeMaterialConnectionPoint {\r\n        return this._inputs[6];\r\n    }\r\n\r\n    /**\r\n     * Gets the roughness input component\r\n     */\r\n    public get roughness(): NodeMaterialConnectionPoint {\r\n        return this._inputs[7];\r\n    }\r\n\r\n    /**\r\n     * Gets the ambient occlusion input component\r\n     */\r\n    public get ambientOcc(): NodeMaterialConnectionPoint {\r\n        return this._inputs[8];\r\n    }\r\n\r\n    /**\r\n     * Gets the opacity input component\r\n     */\r\n    public get opacity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[9];\r\n    }\r\n\r\n    /**\r\n     * Gets the index of refraction input component\r\n     */\r\n    public get indexOfRefraction(): NodeMaterialConnectionPoint {\r\n        return this._inputs[10];\r\n    }\r\n\r\n    /**\r\n     * Gets the ambient color input component\r\n     */\r\n    public get ambientColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[11];\r\n    }\r\n\r\n    /**\r\n     * Gets the reflection object parameters\r\n     */\r\n    public get reflection(): NodeMaterialConnectionPoint {\r\n        return this._inputs[12];\r\n    }\r\n\r\n    /**\r\n     * Gets the clear coat object parameters\r\n     */\r\n    public get clearcoat(): NodeMaterialConnectionPoint {\r\n        return this._inputs[13];\r\n    }\r\n\r\n    /**\r\n     * Gets the sheen object parameters\r\n     */\r\n    public get sheen(): NodeMaterialConnectionPoint {\r\n        return this._inputs[14];\r\n    }\r\n\r\n    /**\r\n     * Gets the sub surface object parameters\r\n     */\r\n    public get subsurface(): NodeMaterialConnectionPoint {\r\n        return this._inputs[15];\r\n    }\r\n\r\n    /**\r\n     * Gets the anisotropy object parameters\r\n     */\r\n    public get anisotropy(): NodeMaterialConnectionPoint {\r\n        return this._inputs[16];\r\n    }\r\n\r\n    /**\r\n     * Gets the iridescence object parameters\r\n     */\r\n    public get iridescence(): NodeMaterialConnectionPoint {\r\n        return this._inputs[17];\r\n    }\r\n\r\n    /**\r\n     * Gets the ambient output component\r\n     */\r\n    public get ambientClr(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the diffuse output component\r\n     */\r\n    public get diffuseDir(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the specular output component\r\n     */\r\n    public get specularDir(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the clear coat output component\r\n     */\r\n    public get clearcoatDir(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the sheen output component\r\n     */\r\n    public get sheenDir(): NodeMaterialConnectionPoint {\r\n        return this._outputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the indirect diffuse output component\r\n     */\r\n    public get diffuseInd(): NodeMaterialConnectionPoint {\r\n        return this._outputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the indirect specular output component\r\n     */\r\n    public get specularInd(): NodeMaterialConnectionPoint {\r\n        return this._outputs[6];\r\n    }\r\n\r\n    /**\r\n     * Gets the indirect clear coat output component\r\n     */\r\n    public get clearcoatInd(): NodeMaterialConnectionPoint {\r\n        return this._outputs[7];\r\n    }\r\n\r\n    /**\r\n     * Gets the indirect sheen output component\r\n     */\r\n    public get sheenInd(): NodeMaterialConnectionPoint {\r\n        return this._outputs[8];\r\n    }\r\n\r\n    /**\r\n     * Gets the refraction output component\r\n     */\r\n    public get refraction(): NodeMaterialConnectionPoint {\r\n        return this._outputs[9];\r\n    }\r\n\r\n    /**\r\n     * Gets the global lighting output component\r\n     */\r\n    public get lighting(): NodeMaterialConnectionPoint {\r\n        return this._outputs[10];\r\n    }\r\n\r\n    /**\r\n     * Gets the shadow output component\r\n     */\r\n    public get shadow(): NodeMaterialConnectionPoint {\r\n        return this._outputs[11];\r\n    }\r\n\r\n    /**\r\n     * Gets the alpha output component\r\n     */\r\n    public get alpha(): NodeMaterialConnectionPoint {\r\n        return this._outputs[12];\r\n    }\r\n\r\n    public autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.cameraPosition.isConnected) {\r\n            let cameraPositionInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.CameraPosition && additionalFilteringInfo(b));\r\n\r\n            if (!cameraPositionInput) {\r\n                cameraPositionInput = new InputBlock(\"cameraPosition\");\r\n                cameraPositionInput.setAsSystemValue(NodeMaterialSystemValues.CameraPosition);\r\n            }\r\n            cameraPositionInput.output.connectTo(this.cameraPosition);\r\n        }\r\n\r\n        if (!this.view.isConnected) {\r\n            let viewInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.View && additionalFilteringInfo(b));\r\n\r\n            if (!viewInput) {\r\n                viewInput = new InputBlock(\"view\");\r\n                viewInput.setAsSystemValue(NodeMaterialSystemValues.View);\r\n            }\r\n            viewInput.output.connectTo(this.view);\r\n        }\r\n    }\r\n\r\n    public prepareDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        // General\r\n        defines.setValue(\"PBR\", true);\r\n        defines.setValue(\"METALLICWORKFLOW\", true);\r\n        defines.setValue(\"DEBUGMODE\", this.debugMode, true);\r\n        defines.setValue(\"DEBUGMODE_FORCERETURN\", true);\r\n        defines.setValue(\"NORMALXYSCALE\", true);\r\n        defines.setValue(\"BUMP\", this.perturbedNormal.isConnected, true);\r\n        defines.setValue(\"LODBASEDMICROSFURACE\", this._scene.getEngine().getCaps().textureLOD);\r\n\r\n        // Albedo & Opacity\r\n        defines.setValue(\"ALBEDO\", false, true);\r\n        defines.setValue(\"OPACITY\", this.opacity.isConnected, true);\r\n\r\n        // Ambient occlusion\r\n        defines.setValue(\"AMBIENT\", true, true);\r\n        defines.setValue(\"AMBIENTINGRAYSCALE\", false, true);\r\n\r\n        // Reflectivity\r\n        defines.setValue(\"REFLECTIVITY\", false, true);\r\n        defines.setValue(\"AOSTOREINMETALMAPRED\", false, true);\r\n        defines.setValue(\"METALLNESSSTOREINMETALMAPBLUE\", false, true);\r\n        defines.setValue(\"ROUGHNESSSTOREINMETALMAPALPHA\", false, true);\r\n        defines.setValue(\"ROUGHNESSSTOREINMETALMAPGREEN\", false, true);\r\n\r\n        // Lighting & colors\r\n        if (this.lightFalloff === PBRBaseMaterial.LIGHTFALLOFF_STANDARD) {\r\n            defines.setValue(\"USEPHYSICALLIGHTFALLOFF\", false);\r\n            defines.setValue(\"USEGLTFLIGHTFALLOFF\", false);\r\n        } else if (this.lightFalloff === PBRBaseMaterial.LIGHTFALLOFF_GLTF) {\r\n            defines.setValue(\"USEPHYSICALLIGHTFALLOFF\", false);\r\n            defines.setValue(\"USEGLTFLIGHTFALLOFF\", true);\r\n        } else {\r\n            defines.setValue(\"USEPHYSICALLIGHTFALLOFF\", true);\r\n            defines.setValue(\"USEGLTFLIGHTFALLOFF\", false);\r\n        }\r\n\r\n        // Transparency\r\n        const alphaTestCutOffString = this.alphaTestCutoff.toString();\r\n\r\n        defines.setValue(\"ALPHABLEND\", this.useAlphaBlending, true);\r\n        defines.setValue(\"ALPHAFROMALBEDO\", false, true);\r\n        defines.setValue(\"ALPHATEST\", this.useAlphaTest, true);\r\n        defines.setValue(\"ALPHATESTVALUE\", alphaTestCutOffString.indexOf(\".\") < 0 ? alphaTestCutOffString + \".\" : alphaTestCutOffString, true);\r\n        defines.setValue(\"OPACITYRGB\", false, true);\r\n\r\n        // Rendering\r\n        defines.setValue(\"RADIANCEOVERALPHA\", this.useRadianceOverAlpha, true);\r\n        defines.setValue(\"SPECULAROVERALPHA\", this.useSpecularOverAlpha, true);\r\n        defines.setValue(\"SPECULARAA\", this._scene.getEngine().getCaps().standardDerivatives && this.enableSpecularAntiAliasing, true);\r\n        defines.setValue(\"REALTIME_FILTERING\", this.realTimeFiltering, true);\r\n\r\n        const scene = mesh.getScene();\r\n        const engine = scene.getEngine();\r\n\r\n        if (engine._features.needTypeSuffixInShaderConstants) {\r\n            defines.setValue(\"NUM_SAMPLES\", this.realTimeFilteringQuality + \"u\", true);\r\n        } else {\r\n            defines.setValue(\"NUM_SAMPLES\", \"\" + this.realTimeFilteringQuality, true);\r\n        }\r\n\r\n        // Advanced\r\n        defines.setValue(\"BRDF_V_HEIGHT_CORRELATED\", true);\r\n        defines.setValue(\"MS_BRDF_ENERGY_CONSERVATION\", this.useEnergyConservation, true);\r\n        defines.setValue(\"RADIANCEOCCLUSION\", this.useRadianceOcclusion, true);\r\n        defines.setValue(\"HORIZONOCCLUSION\", this.useHorizonOcclusion, true);\r\n        defines.setValue(\"UNLIT\", this.unlit, true);\r\n        defines.setValue(\"FORCENORMALFORWARD\", this.forceNormalForward, true);\r\n\r\n        if (this._environmentBRDFTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n            defines.setValue(\"ENVIRONMENTBRDF\", true);\r\n            defines.setValue(\"ENVIRONMENTBRDF_RGBD\", this._environmentBRDFTexture.isRGBD, true);\r\n        } else {\r\n            defines.setValue(\"ENVIRONMENTBRDF\", false);\r\n            defines.setValue(\"ENVIRONMENTBRDF_RGBD\", false);\r\n        }\r\n\r\n        if (defines._areImageProcessingDirty && nodeMaterial.imageProcessingConfiguration) {\r\n            nodeMaterial.imageProcessingConfiguration.prepareDefines(defines);\r\n        }\r\n\r\n        if (!defines._areLightsDirty) {\r\n            return;\r\n        }\r\n\r\n        if (!this.light) {\r\n            // Lights\r\n            PrepareDefinesForLights(scene, mesh, defines, true, nodeMaterial.maxSimultaneousLights);\r\n            defines._needNormals = true;\r\n\r\n            // Multiview\r\n            PrepareDefinesForMultiview(scene, defines);\r\n        } else {\r\n            const state = {\r\n                needNormals: false,\r\n                needRebuild: false,\r\n                lightmapMode: false,\r\n                shadowEnabled: false,\r\n                specularEnabled: false,\r\n            };\r\n\r\n            PrepareDefinesForLight(scene, mesh, this.light, this._lightId, defines, true, state);\r\n\r\n            if (state.needRebuild) {\r\n                defines.rebuild();\r\n            }\r\n        }\r\n    }\r\n\r\n    public updateUniformsAndSamples(state: NodeMaterialBuildState, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines, uniformBuffers: string[]) {\r\n        for (let lightIndex = 0; lightIndex < nodeMaterial.maxSimultaneousLights; lightIndex++) {\r\n            if (!defines[\"LIGHT\" + lightIndex]) {\r\n                break;\r\n            }\r\n            const onlyUpdateBuffersList = state.uniforms.indexOf(\"vLightData\" + lightIndex) >= 0;\r\n            PrepareUniformsAndSamplersForLight(lightIndex, state.uniforms, state.samplers, defines[\"PROJECTEDLIGHTTEXTURE\" + lightIndex], uniformBuffers, onlyUpdateBuffersList);\r\n        }\r\n    }\r\n\r\n    public isReady(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        if (this._environmentBRDFTexture && !this._environmentBRDFTexture.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        if (defines._areImageProcessingDirty && nodeMaterial.imageProcessingConfiguration) {\r\n            if (!nodeMaterial.imageProcessingConfiguration.isReady()) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh) {\r\n        if (!mesh) {\r\n            return;\r\n        }\r\n\r\n        const scene = mesh.getScene();\r\n\r\n        if (!this.light) {\r\n            BindLights(scene, mesh, effect, true, nodeMaterial.maxSimultaneousLights);\r\n        } else {\r\n            BindLight(this.light, this._lightId, scene, effect, true);\r\n        }\r\n\r\n        effect.setTexture(this._environmentBrdfSamplerName, this._environmentBRDFTexture);\r\n\r\n        effect.setFloat2(\"vDebugMode\", this.debugLimit, this.debugFactor);\r\n\r\n        const ambientScene = this._scene.ambientColor;\r\n\r\n        if (ambientScene) {\r\n            effect.setColor3(\"ambientFromScene\", ambientScene);\r\n        }\r\n\r\n        const invertNormal = scene.useRightHandedSystem === (scene._mirroredCameraPosition != null);\r\n\r\n        effect.setFloat(this._invertNormalName, invertNormal ? -1 : 1);\r\n\r\n        effect.setFloat4(\"vLightingIntensity\", this.directIntensity, 1, this.environmentIntensity * this._scene.environmentIntensity, this.specularIntensity);\r\n\r\n        // reflectivity bindings\r\n        const outsideIOR = 1; // consider air as clear coat and other layers would remap in the shader.\r\n        const ior = this.indexOfRefraction.connectInputBlock?.value ?? 1.5;\r\n\r\n        // We are here deriving our default reflectance from a common value for none metallic surface.\r\n        // Based of the schlick fresnel approximation model\r\n        // for dielectrics.\r\n        const f0 = Math.pow((ior - outsideIOR) / (ior + outsideIOR), 2);\r\n\r\n        // Tweak the default F0 and F90 based on our given setup\r\n        this._metallicReflectanceColor.scaleToRef(f0 * this._metallicF0Factor, TmpColors.Color3[0]);\r\n        const metallicF90 = this._metallicF0Factor;\r\n\r\n        effect.setColor4(this._vMetallicReflectanceFactorsName, TmpColors.Color3[0], metallicF90);\r\n\r\n        if (nodeMaterial.imageProcessingConfiguration) {\r\n            nodeMaterial.imageProcessingConfiguration.bind(effect);\r\n        }\r\n    }\r\n\r\n    private _injectVertexCode(state: NodeMaterialBuildState) {\r\n        const worldPos = this.worldPosition;\r\n        const comments = `//${this.name}`;\r\n\r\n        // Declaration\r\n        if (!this.light) {\r\n            // Emit for all lights\r\n            state._emitFunctionFromInclude(state.supportUniformBuffers ? \"lightVxUboDeclaration\" : \"lightVxFragmentDeclaration\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n            });\r\n            this._lightId = 0;\r\n\r\n            state.sharedData.dynamicUniformBlocks.push(this);\r\n        } else {\r\n            this._lightId = (state.counters[\"lightCounter\"] !== undefined ? state.counters[\"lightCounter\"] : -1) + 1;\r\n            state.counters[\"lightCounter\"] = this._lightId;\r\n\r\n            state._emitFunctionFromInclude(\r\n                state.supportUniformBuffers ? \"lightVxUboDeclaration\" : \"lightVxFragmentDeclaration\",\r\n                comments,\r\n                {\r\n                    replaceStrings: [{ search: /{X}/g, replace: this._lightId.toString() }],\r\n                },\r\n                this._lightId.toString()\r\n            );\r\n        }\r\n\r\n        // Inject code in vertex\r\n        const worldPosVaryingName = \"v_\" + worldPos.associatedVariableName;\r\n        if (state._emitVaryingFromString(worldPosVaryingName, \"vec4\")) {\r\n            state.compilationString += `${worldPosVaryingName} = ${worldPos.associatedVariableName};\\n`;\r\n        }\r\n\r\n        const reflectionBlock = this.reflection.isConnected ? (this.reflection.connectedPoint?.ownerBlock as ReflectionBlock) : null;\r\n\r\n        if (reflectionBlock) {\r\n            reflectionBlock.viewConnectionPoint = this.view;\r\n        }\r\n\r\n        state.compilationString += reflectionBlock?.handleVertexSide(state) ?? \"\";\r\n\r\n        if (state._emitVaryingFromString(\"vClipSpacePosition\", \"vec4\", \"defined(IGNORE) || DEBUGMODE > 0\")) {\r\n            state._injectAtEnd += `#if DEBUGMODE > 0\\n`;\r\n            state._injectAtEnd += `vClipSpacePosition = gl_Position;\\n`;\r\n            state._injectAtEnd += `#endif\\n`;\r\n        }\r\n\r\n        if (this.light) {\r\n            state.compilationString += state._emitCodeFromInclude(\"shadowsVertex\", comments, {\r\n                replaceStrings: [\r\n                    { search: /{X}/g, replace: this._lightId.toString() },\r\n                    { search: /worldPos/g, replace: worldPos.associatedVariableName },\r\n                ],\r\n            });\r\n        } else {\r\n            state.compilationString += `vec4 worldPos = ${worldPos.associatedVariableName};\\n`;\r\n            if (this.view.isConnected) {\r\n                state.compilationString += `mat4 view = ${this.view.associatedVariableName};\\n`;\r\n            }\r\n            state.compilationString += state._emitCodeFromInclude(\"shadowsVertex\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n            });\r\n        }\r\n    }\r\n\r\n    private _getAlbedoOpacityCode(): string {\r\n        let code = `albedoOpacityOutParams albedoOpacityOut;\\n`;\r\n\r\n        const albedoColor = this.baseColor.isConnected ? this.baseColor.associatedVariableName : \"vec3(1.)\";\r\n        const opacity = this.opacity.isConnected ? this.opacity.associatedVariableName : \"1.\";\r\n\r\n        code += `albedoOpacityBlock(\r\n                vec4(${albedoColor}, 1.),\r\n            #ifdef ALBEDO\r\n                vec4(1.),\r\n                vec2(1., 1.),\r\n            #endif\r\n            #ifdef OPACITY\r\n                vec4(${opacity}),\r\n                vec2(1., 1.),\r\n            #endif\r\n                albedoOpacityOut\r\n            );\r\n\r\n            vec3 surfaceAlbedo = albedoOpacityOut.surfaceAlbedo;\r\n            float alpha = albedoOpacityOut.alpha;\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    private _getAmbientOcclusionCode(): string {\r\n        let code = `ambientOcclusionOutParams aoOut;\\n`;\r\n\r\n        const ao = this.ambientOcc.isConnected ? this.ambientOcc.associatedVariableName : \"1.\";\r\n\r\n        code += `ambientOcclusionBlock(\r\n            #ifdef AMBIENT\r\n                vec3(${ao}),\r\n                vec4(0., 1.0, 1.0, 0.),\r\n            #endif\r\n                aoOut\r\n            );\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    private _getReflectivityCode(state: NodeMaterialBuildState): string {\r\n        let code = `reflectivityOutParams reflectivityOut;\\n`;\r\n\r\n        const aoIntensity = \"1.\";\r\n\r\n        this._vMetallicReflectanceFactorsName = state._getFreeVariableName(\"vMetallicReflectanceFactors\");\r\n        state._emitUniformFromString(this._vMetallicReflectanceFactorsName, \"vec4\");\r\n\r\n        code += `vec3 baseColor = surfaceAlbedo;\r\n\r\n            reflectivityBlock(\r\n                vec4(${this.metallic.associatedVariableName}, ${this.roughness.associatedVariableName}, 0., 0.),\r\n            #ifdef METALLICWORKFLOW\r\n                surfaceAlbedo,\r\n                ${this._vMetallicReflectanceFactorsName},\r\n            #endif\r\n            #ifdef REFLECTIVITY\r\n                vec3(0., 0., ${aoIntensity}),\r\n                vec4(1.),\r\n            #endif\r\n            #if defined(METALLICWORKFLOW) && defined(REFLECTIVITY)  && defined(AOSTOREINMETALMAPRED)\r\n                aoOut.ambientOcclusionColor,\r\n            #endif\r\n            #ifdef MICROSURFACEMAP\r\n                microSurfaceTexel, <== not handled!\r\n            #endif\r\n                reflectivityOut\r\n            );\r\n\r\n            float microSurface = reflectivityOut.microSurface;\r\n            float roughness = reflectivityOut.roughness;\r\n\r\n            #ifdef METALLICWORKFLOW\r\n                surfaceAlbedo = reflectivityOut.surfaceAlbedo;\r\n            #endif\r\n            #if defined(METALLICWORKFLOW) && defined(REFLECTIVITY) && defined(AOSTOREINMETALMAPRED)\r\n                aoOut.ambientOcclusionColor = reflectivityOut.ambientOcclusionColor;\r\n            #endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        this._scene = state.sharedData.scene;\r\n\r\n        if (!this._environmentBRDFTexture) {\r\n            this._environmentBRDFTexture = GetEnvironmentBRDFTexture(this._scene);\r\n        }\r\n\r\n        const reflectionBlock = this.reflection.isConnected ? (this.reflection.connectedPoint?.ownerBlock as ReflectionBlock) : null;\r\n\r\n        if (reflectionBlock) {\r\n            // Need those variables to be setup when calling _injectVertexCode\r\n            reflectionBlock.worldPositionConnectionPoint = this.worldPosition;\r\n            reflectionBlock.cameraPositionConnectionPoint = this.cameraPosition;\r\n            reflectionBlock.worldNormalConnectionPoint = this.worldNormal;\r\n            reflectionBlock.viewConnectionPoint = this.view;\r\n        }\r\n\r\n        if (state.target !== NodeMaterialBlockTargets.Fragment) {\r\n            // Vertex\r\n            this._injectVertexCode(state);\r\n\r\n            return this;\r\n        }\r\n\r\n        // Fragment\r\n        state.sharedData.forcedBindableBlocks.push(this);\r\n        state.sharedData.blocksWithDefines.push(this);\r\n        state.sharedData.blockingBlocks.push(this);\r\n        if (this.generateOnlyFragmentCode) {\r\n            state.sharedData.dynamicUniformBlocks.push(this);\r\n        }\r\n\r\n        const comments = `//${this.name}`;\r\n        const normalShading = this.perturbedNormal;\r\n\r\n        let worldPosVarName = this.worldPosition.associatedVariableName;\r\n        if (this.generateOnlyFragmentCode) {\r\n            worldPosVarName = state._getFreeVariableName(\"globalWorldPos\");\r\n            state._emitFunction(\"pbr_globalworldpos\", `vec3 ${worldPosVarName};\\n`, comments);\r\n            state.compilationString += `${worldPosVarName} = ${this.worldPosition.associatedVariableName}.xyz;\\n`;\r\n\r\n            state.compilationString += state._emitCodeFromInclude(\"shadowsVertex\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n                substitutionVars: this.generateOnlyFragmentCode ? `worldPos,${this.worldPosition.associatedVariableName}` : undefined,\r\n            });\r\n\r\n            state.compilationString += `#if DEBUGMODE > 0\\n`;\r\n            state.compilationString += `vec4 vClipSpacePosition = vec4((vec2(gl_FragCoord.xy) / vec2(1.0)) * 2.0 - 1.0, 0.0, 1.0);\\n`;\r\n            state.compilationString += `#endif\\n`;\r\n        } else {\r\n            worldPosVarName = \"v_\" + worldPosVarName;\r\n        }\r\n\r\n        this._environmentBrdfSamplerName = state._getFreeVariableName(\"environmentBrdfSampler\");\r\n\r\n        state._emit2DSampler(this._environmentBrdfSamplerName);\r\n\r\n        state.sharedData.hints.needAlphaBlending = state.sharedData.hints.needAlphaBlending || this.useAlphaBlending;\r\n        state.sharedData.hints.needAlphaTesting = state.sharedData.hints.needAlphaTesting || this.useAlphaTest;\r\n\r\n        state._emitExtension(\"lod\", \"#extension GL_EXT_shader_texture_lod : enable\", \"defined(LODBASEDMICROSFURACE)\");\r\n        state._emitExtension(\"derivatives\", \"#extension GL_OES_standard_derivatives : enable\");\r\n\r\n        state._emitUniformFromString(\"vDebugMode\", \"vec2\", \"defined(IGNORE) || DEBUGMODE > 0\");\r\n        state._emitUniformFromString(\"ambientFromScene\", \"vec3\");\r\n\r\n        // Image processing uniforms\r\n        state.uniforms.push(\"exposureLinear\");\r\n        state.uniforms.push(\"contrast\");\r\n        state.uniforms.push(\"vInverseScreenSize\");\r\n        state.uniforms.push(\"vignetteSettings1\");\r\n        state.uniforms.push(\"vignetteSettings2\");\r\n        state.uniforms.push(\"vCameraColorCurveNegative\");\r\n        state.uniforms.push(\"vCameraColorCurveNeutral\");\r\n        state.uniforms.push(\"vCameraColorCurvePositive\");\r\n        state.uniforms.push(\"txColorTransform\");\r\n        state.uniforms.push(\"colorTransformSettings\");\r\n        state.uniforms.push(\"ditherIntensity\");\r\n\r\n        //\r\n        // Includes\r\n        //\r\n        if (!this.light) {\r\n            // Emit for all lights\r\n            state._emitFunctionFromInclude(state.supportUniformBuffers ? \"lightUboDeclaration\" : \"lightFragmentDeclaration\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n                substitutionVars: this.generateOnlyFragmentCode ? \"varying,\" : undefined,\r\n            });\r\n        } else {\r\n            state._emitFunctionFromInclude(\r\n                state.supportUniformBuffers ? \"lightUboDeclaration\" : \"lightFragmentDeclaration\",\r\n                comments,\r\n                {\r\n                    replaceStrings: [{ search: /{X}/g, replace: this._lightId.toString() }],\r\n                },\r\n                this._lightId.toString()\r\n            );\r\n        }\r\n\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n        state._emitFunctionFromInclude(\"importanceSampling\", comments);\r\n        state._emitFunctionFromInclude(\"pbrHelperFunctions\", comments);\r\n        state._emitFunctionFromInclude(\"imageProcessingDeclaration\", comments);\r\n        state._emitFunctionFromInclude(\"imageProcessingFunctions\", comments);\r\n\r\n        state._emitFunctionFromInclude(\"shadowsFragmentFunctions\", comments);\r\n\r\n        state._emitFunctionFromInclude(\"pbrDirectLightingSetupFunctions\", comments, {\r\n            replaceStrings: [{ search: /vPositionW/g, replace: worldPosVarName + \".xyz\" }],\r\n        });\r\n\r\n        state._emitFunctionFromInclude(\"pbrDirectLightingFalloffFunctions\", comments);\r\n        state._emitFunctionFromInclude(\"pbrBRDFFunctions\", comments, {\r\n            replaceStrings: [{ search: /REFLECTIONMAP_SKYBOX/g, replace: reflectionBlock?._defineSkyboxName ?? \"REFLECTIONMAP_SKYBOX\" }],\r\n        });\r\n        state._emitFunctionFromInclude(\"hdrFilteringFunctions\", comments);\r\n\r\n        state._emitFunctionFromInclude(\"pbrDirectLightingFunctions\", comments, {\r\n            replaceStrings: [{ search: /vPositionW/g, replace: worldPosVarName + \".xyz\" }],\r\n        });\r\n\r\n        state._emitFunctionFromInclude(\"pbrIBLFunctions\", comments);\r\n\r\n        state._emitFunctionFromInclude(\"pbrBlockAlbedoOpacity\", comments);\r\n        state._emitFunctionFromInclude(\"pbrBlockReflectivity\", comments);\r\n        state._emitFunctionFromInclude(\"pbrBlockAmbientOcclusion\", comments);\r\n        state._emitFunctionFromInclude(\"pbrBlockAlphaFresnel\", comments);\r\n        state._emitFunctionFromInclude(\"pbrBlockAnisotropic\", comments);\r\n\r\n        //\r\n        // code\r\n        //\r\n\r\n        state._emitUniformFromString(\"vLightingIntensity\", \"vec4\");\r\n\r\n        if (reflectionBlock?.generateOnlyFragmentCode) {\r\n            state.compilationString += reflectionBlock.handleVertexSide(state);\r\n        }\r\n\r\n        // _____________________________ Geometry Information ____________________________\r\n        this._vNormalWName = state._getFreeVariableName(\"vNormalW\");\r\n\r\n        state.compilationString += `vec4 ${this._vNormalWName} = normalize(${this.worldNormal.associatedVariableName});\\n`;\r\n\r\n        if (state._registerTempVariable(\"viewDirectionW\")) {\r\n            state.compilationString += `vec3 viewDirectionW = normalize(${this.cameraPosition.associatedVariableName} - ${worldPosVarName}.xyz);\\n`;\r\n        }\r\n\r\n        state.compilationString += `vec3 geometricNormalW = ${this._vNormalWName}.xyz;\\n`;\r\n\r\n        state.compilationString += `vec3 normalW = ${normalShading.isConnected ? \"normalize(\" + normalShading.associatedVariableName + \".xyz)\" : \"geometricNormalW\"};\\n`;\r\n\r\n        this._invertNormalName = state._getFreeVariableName(\"invertNormal\");\r\n\r\n        state._emitUniformFromString(this._invertNormalName, \"float\");\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockNormalFinal\", comments, {\r\n            replaceStrings: [\r\n                { search: /vPositionW/g, replace: worldPosVarName + \".xyz\" },\r\n                { search: /vEyePosition.w/g, replace: this._invertNormalName },\r\n            ],\r\n        });\r\n\r\n        // _____________________________ Albedo & Opacity ______________________________\r\n        state.compilationString += this._getAlbedoOpacityCode();\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"depthPrePass\", comments);\r\n\r\n        // _____________________________ AO  _______________________________\r\n        state.compilationString += this._getAmbientOcclusionCode();\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockLightmapInit\", comments);\r\n\r\n        // _____________________________ UNLIT  _______________________________\r\n        state.compilationString += `#ifdef UNLIT\r\n                vec3 diffuseBase = vec3(1., 1., 1.);\r\n            #else\\n`;\r\n\r\n        // _____________________________ Reflectivity _______________________________\r\n        state.compilationString += this._getReflectivityCode(state);\r\n\r\n        // _____________________________ Geometry info _________________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockGeometryInfo\", comments, {\r\n            replaceStrings: [\r\n                { search: /REFLECTIONMAP_SKYBOX/g, replace: reflectionBlock?._defineSkyboxName ?? \"REFLECTIONMAP_SKYBOX\" },\r\n                { search: /REFLECTIONMAP_3D/g, replace: reflectionBlock?._define3DName ?? \"REFLECTIONMAP_3D\" },\r\n            ],\r\n        });\r\n\r\n        // _____________________________ Anisotropy _______________________________________\r\n        const anisotropyBlock = this.anisotropy.isConnected ? (this.anisotropy.connectedPoint?.ownerBlock as AnisotropyBlock) : null;\r\n\r\n        if (anisotropyBlock) {\r\n            anisotropyBlock.worldPositionConnectionPoint = this.worldPosition;\r\n            anisotropyBlock.worldNormalConnectionPoint = this.worldNormal;\r\n\r\n            state.compilationString += anisotropyBlock.getCode(state, !this.perturbedNormal.isConnected);\r\n        }\r\n\r\n        // _____________________________ Reflection _______________________________________\r\n        if (reflectionBlock && reflectionBlock.hasTexture) {\r\n            state.compilationString += reflectionBlock.getCode(state, anisotropyBlock ? \"anisotropicOut.anisotropicNormal\" : \"normalW\");\r\n        }\r\n\r\n        state._emitFunctionFromInclude(\"pbrBlockReflection\", comments, {\r\n            replaceStrings: [\r\n                { search: /computeReflectionCoords/g, replace: \"computeReflectionCoordsPBR\" },\r\n                { search: /REFLECTIONMAP_3D/g, replace: reflectionBlock?._define3DName ?? \"REFLECTIONMAP_3D\" },\r\n                { search: /REFLECTIONMAP_OPPOSITEZ/g, replace: reflectionBlock?._defineOppositeZ ?? \"REFLECTIONMAP_OPPOSITEZ\" },\r\n                { search: /REFLECTIONMAP_PROJECTION/g, replace: reflectionBlock?._defineProjectionName ?? \"REFLECTIONMAP_PROJECTION\" },\r\n                { search: /REFLECTIONMAP_SKYBOX/g, replace: reflectionBlock?._defineSkyboxName ?? \"REFLECTIONMAP_SKYBOX\" },\r\n                { search: /LODINREFLECTIONALPHA/g, replace: reflectionBlock?._defineLODReflectionAlpha ?? \"LODINREFLECTIONALPHA\" },\r\n                { search: /LINEARSPECULARREFLECTION/g, replace: reflectionBlock?._defineLinearSpecularReflection ?? \"LINEARSPECULARREFLECTION\" },\r\n                { search: /vReflectionFilteringInfo/g, replace: reflectionBlock?._vReflectionFilteringInfoName ?? \"vReflectionFilteringInfo\" },\r\n            ],\r\n        });\r\n\r\n        // ___________________ Compute Reflectance aka R0 F0 info _________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockReflectance0\", comments, {\r\n            replaceStrings: [{ search: /metallicReflectanceFactors/g, replace: this._vMetallicReflectanceFactorsName }],\r\n        });\r\n        // ________________________________ Sheen ______________________________\r\n        const sheenBlock = this.sheen.isConnected ? (this.sheen.connectedPoint?.ownerBlock as SheenBlock) : null;\r\n\r\n        if (sheenBlock) {\r\n            state.compilationString += sheenBlock.getCode(reflectionBlock);\r\n        }\r\n\r\n        state._emitFunctionFromInclude(\"pbrBlockSheen\", comments, {\r\n            replaceStrings: [\r\n                { search: /REFLECTIONMAP_3D/g, replace: reflectionBlock?._define3DName ?? \"REFLECTIONMAP_3D\" },\r\n                { search: /REFLECTIONMAP_SKYBOX/g, replace: reflectionBlock?._defineSkyboxName ?? \"REFLECTIONMAP_SKYBOX\" },\r\n                { search: /LODINREFLECTIONALPHA/g, replace: reflectionBlock?._defineLODReflectionAlpha ?? \"LODINREFLECTIONALPHA\" },\r\n                { search: /LINEARSPECULARREFLECTION/g, replace: reflectionBlock?._defineLinearSpecularReflection ?? \"LINEARSPECULARREFLECTION\" },\r\n            ],\r\n        });\r\n\r\n        // _____________________________ Iridescence _______________________________\r\n        const iridescenceBlock = this.iridescence.isConnected ? (this.iridescence.connectedPoint?.ownerBlock as IridescenceBlock) : null;\r\n        state.compilationString += IridescenceBlock.GetCode(iridescenceBlock);\r\n\r\n        state._emitFunctionFromInclude(\"pbrBlockIridescence\", comments, {\r\n            replaceStrings: [],\r\n        });\r\n\r\n        // _____________________________ Clear Coat ____________________________\r\n        const clearcoatBlock = this.clearcoat.isConnected ? (this.clearcoat.connectedPoint?.ownerBlock as ClearCoatBlock) : null;\r\n        const generateTBNSpace = !this.perturbedNormal.isConnected && !this.anisotropy.isConnected;\r\n        const isTangentConnectedToPerturbNormal =\r\n            this.perturbedNormal.isConnected && (this.perturbedNormal.connectedPoint?.ownerBlock as PerturbNormalBlock).worldTangent?.isConnected;\r\n        const isTangentConnectedToAnisotropy = this.anisotropy.isConnected && (this.anisotropy.connectedPoint?.ownerBlock as AnisotropyBlock).worldTangent.isConnected;\r\n        let vTBNAvailable = isTangentConnectedToPerturbNormal || (!this.perturbedNormal.isConnected && isTangentConnectedToAnisotropy);\r\n\r\n        state.compilationString += ClearCoatBlock.GetCode(\r\n            state,\r\n            clearcoatBlock,\r\n            reflectionBlock,\r\n            worldPosVarName,\r\n            generateTBNSpace,\r\n            vTBNAvailable,\r\n            this.worldNormal.associatedVariableName\r\n        );\r\n\r\n        if (generateTBNSpace) {\r\n            vTBNAvailable = clearcoatBlock?.worldTangent.isConnected ?? false;\r\n        }\r\n\r\n        state._emitFunctionFromInclude(\"pbrBlockClearcoat\", comments, {\r\n            replaceStrings: [\r\n                { search: /computeReflectionCoords/g, replace: \"computeReflectionCoordsPBR\" },\r\n                { search: /REFLECTIONMAP_3D/g, replace: reflectionBlock?._define3DName ?? \"REFLECTIONMAP_3D\" },\r\n                { search: /REFLECTIONMAP_OPPOSITEZ/g, replace: reflectionBlock?._defineOppositeZ ?? \"REFLECTIONMAP_OPPOSITEZ\" },\r\n                { search: /REFLECTIONMAP_PROJECTION/g, replace: reflectionBlock?._defineProjectionName ?? \"REFLECTIONMAP_PROJECTION\" },\r\n                { search: /REFLECTIONMAP_SKYBOX/g, replace: reflectionBlock?._defineSkyboxName ?? \"REFLECTIONMAP_SKYBOX\" },\r\n                { search: /LODINREFLECTIONALPHA/g, replace: reflectionBlock?._defineLODReflectionAlpha ?? \"LODINREFLECTIONALPHA\" },\r\n                { search: /LINEARSPECULARREFLECTION/g, replace: reflectionBlock?._defineLinearSpecularReflection ?? \"LINEARSPECULARREFLECTION\" },\r\n                { search: /defined\\(TANGENT\\)/g, replace: vTBNAvailable ? \"defined(TANGENT)\" : \"defined(IGNORE)\" },\r\n            ],\r\n        });\r\n\r\n        // _________________________ Specular Environment Reflectance __________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockReflectance\", comments, {\r\n            replaceStrings: [\r\n                { search: /REFLECTIONMAP_SKYBOX/g, replace: reflectionBlock?._defineSkyboxName ?? \"REFLECTIONMAP_SKYBOX\" },\r\n                { search: /REFLECTIONMAP_3D/g, replace: reflectionBlock?._define3DName ?? \"REFLECTIONMAP_3D\" },\r\n            ],\r\n        });\r\n\r\n        // ___________________________________ SubSurface ______________________________________\r\n        const subsurfaceBlock = this.subsurface.isConnected ? (this.subsurface.connectedPoint?.ownerBlock as SubSurfaceBlock) : null;\r\n        const refractionBlock = this.subsurface.isConnected\r\n            ? ((this.subsurface.connectedPoint?.ownerBlock as SubSurfaceBlock).refraction.connectedPoint?.ownerBlock as RefractionBlock)\r\n            : null;\r\n\r\n        if (refractionBlock) {\r\n            refractionBlock.viewConnectionPoint = this.view;\r\n            refractionBlock.indexOfRefractionConnectionPoint = this.indexOfRefraction;\r\n        }\r\n\r\n        state.compilationString += SubSurfaceBlock.GetCode(state, subsurfaceBlock, reflectionBlock, worldPosVarName);\r\n\r\n        state._emitFunctionFromInclude(\"pbrBlockSubSurface\", comments, {\r\n            replaceStrings: [\r\n                { search: /REFLECTIONMAP_3D/g, replace: reflectionBlock?._define3DName ?? \"REFLECTIONMAP_3D\" },\r\n                { search: /REFLECTIONMAP_OPPOSITEZ/g, replace: reflectionBlock?._defineOppositeZ ?? \"REFLECTIONMAP_OPPOSITEZ\" },\r\n                { search: /REFLECTIONMAP_PROJECTION/g, replace: reflectionBlock?._defineProjectionName ?? \"REFLECTIONMAP_PROJECTION\" },\r\n                { search: /SS_REFRACTIONMAP_3D/g, replace: refractionBlock?._define3DName ?? \"SS_REFRACTIONMAP_3D\" },\r\n                { search: /SS_LODINREFRACTIONALPHA/g, replace: refractionBlock?._defineLODRefractionAlpha ?? \"SS_LODINREFRACTIONALPHA\" },\r\n                { search: /SS_LINEARSPECULARREFRACTION/g, replace: refractionBlock?._defineLinearSpecularRefraction ?? \"SS_LINEARSPECULARREFRACTION\" },\r\n                { search: /SS_REFRACTIONMAP_OPPOSITEZ/g, replace: refractionBlock?._defineOppositeZ ?? \"SS_REFRACTIONMAP_OPPOSITEZ\" },\r\n            ],\r\n        });\r\n\r\n        // _____________________________ Direct Lighting Info __________________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockDirectLighting\", comments);\r\n\r\n        if (this.light) {\r\n            state.compilationString += state._emitCodeFromInclude(\"lightFragment\", comments, {\r\n                replaceStrings: [\r\n                    { search: /{X}/g, replace: this._lightId.toString() },\r\n                    { search: /vPositionW/g, replace: worldPosVarName + \".xyz\" },\r\n                ],\r\n            });\r\n        } else {\r\n            state.compilationString += state._emitCodeFromInclude(\"lightFragment\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n                substitutionVars: `vPositionW,${worldPosVarName}.xyz`,\r\n            });\r\n        }\r\n\r\n        // _____________________________ Compute Final Lit Components ________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockFinalLitComponents\", comments);\r\n\r\n        // _____________________________ UNLIT (2) ________________________\r\n        state.compilationString += `#endif\\n`; // UNLIT\r\n\r\n        // _____________________________ Compute Final Unlit Components ________________________\r\n        const aoColor = this.ambientColor.isConnected ? this.ambientColor.associatedVariableName : \"vec3(0., 0., 0.)\";\r\n\r\n        let aoDirectLightIntensity = PBRBaseMaterial.DEFAULT_AO_ON_ANALYTICAL_LIGHTS.toString();\r\n\r\n        if (aoDirectLightIntensity.indexOf(\".\") === -1) {\r\n            aoDirectLightIntensity += \".\";\r\n        }\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockFinalUnlitComponents\", comments, {\r\n            replaceStrings: [\r\n                { search: /vec3 finalEmissive[\\s\\S]*?finalEmissive\\*=vLightingIntensity\\.y;/g, replace: \"\" },\r\n                { search: /vAmbientColor/g, replace: aoColor + \" * ambientFromScene\" },\r\n                { search: /vAmbientInfos\\.w/g, replace: aoDirectLightIntensity },\r\n            ],\r\n        });\r\n\r\n        // _____________________________ Output Final Color Composition ________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockFinalColorComposition\", comments, {\r\n            replaceStrings: [{ search: /finalEmissive/g, replace: \"vec3(0.)\" }],\r\n        });\r\n\r\n        // _____________________________ Apply image processing ________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrBlockImageProcessing\", comments, {\r\n            replaceStrings: [{ search: /visibility/g, replace: \"1.\" }],\r\n        });\r\n\r\n        // _____________________________ Generate debug code ________________________\r\n        state.compilationString += state._emitCodeFromInclude(\"pbrDebug\", comments, {\r\n            replaceStrings: [\r\n                { search: /vNormalW/g, replace: this._vNormalWName },\r\n                { search: /vPositionW/g, replace: worldPosVarName },\r\n                { search: /albedoTexture\\.rgb;/g, replace: \"vec3(1.);\\ngl_FragColor.rgb = toGammaSpace(gl_FragColor.rgb);\\n\" },\r\n            ],\r\n        });\r\n\r\n        // _____________________________ Generate end points ________________________\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints) {\r\n                const remap = mapOutputToVariable[output.name];\r\n                if (remap) {\r\n                    const [varName, conditions] = remap;\r\n                    if (conditions) {\r\n                        state.compilationString += `#if ${conditions}\\n`;\r\n                    }\r\n                    state.compilationString += `${this._declareOutput(output, state)} = ${varName};\\n`;\r\n                    if (conditions) {\r\n                        state.compilationString += `#else\\n`;\r\n                        state.compilationString += `${this._declareOutput(output, state)} = vec3(0.);\\n`;\r\n                        state.compilationString += `#endif\\n`;\r\n                    }\r\n                } else {\r\n                    Logger.Error(`There's no remapping for the ${output.name} end point! No code generated`);\r\n                }\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        codeString += `${this._codeVariableName}.lightFalloff = ${this.lightFalloff};\\n`;\r\n        codeString += `${this._codeVariableName}.useAlphaTest = ${this.useAlphaTest};\\n`;\r\n        codeString += `${this._codeVariableName}.alphaTestCutoff = ${this.alphaTestCutoff};\\n`;\r\n        codeString += `${this._codeVariableName}.useAlphaBlending = ${this.useAlphaBlending};\\n`;\r\n        codeString += `${this._codeVariableName}.useRadianceOverAlpha = ${this.useRadianceOverAlpha};\\n`;\r\n        codeString += `${this._codeVariableName}.useSpecularOverAlpha = ${this.useSpecularOverAlpha};\\n`;\r\n        codeString += `${this._codeVariableName}.enableSpecularAntiAliasing = ${this.enableSpecularAntiAliasing};\\n`;\r\n        codeString += `${this._codeVariableName}.realTimeFiltering = ${this.realTimeFiltering};\\n`;\r\n        codeString += `${this._codeVariableName}.realTimeFilteringQuality = ${this.realTimeFilteringQuality};\\n`;\r\n        codeString += `${this._codeVariableName}.useEnergyConservation = ${this.useEnergyConservation};\\n`;\r\n        codeString += `${this._codeVariableName}.useRadianceOcclusion = ${this.useRadianceOcclusion};\\n`;\r\n        codeString += `${this._codeVariableName}.useHorizonOcclusion = ${this.useHorizonOcclusion};\\n`;\r\n        codeString += `${this._codeVariableName}.unlit = ${this.unlit};\\n`;\r\n        codeString += `${this._codeVariableName}.forceNormalForward = ${this.forceNormalForward};\\n`;\r\n        codeString += `${this._codeVariableName}.debugMode = ${this.debugMode};\\n`;\r\n        codeString += `${this._codeVariableName}.debugLimit = ${this.debugLimit};\\n`;\r\n        codeString += `${this._codeVariableName}.debugFactor = ${this.debugFactor};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        if (this.light) {\r\n            serializationObject.lightId = this.light.id;\r\n        }\r\n\r\n        serializationObject.lightFalloff = this.lightFalloff;\r\n        serializationObject.useAlphaTest = this.useAlphaTest;\r\n        serializationObject.alphaTestCutoff = this.alphaTestCutoff;\r\n        serializationObject.useAlphaBlending = this.useAlphaBlending;\r\n        serializationObject.useRadianceOverAlpha = this.useRadianceOverAlpha;\r\n        serializationObject.useSpecularOverAlpha = this.useSpecularOverAlpha;\r\n        serializationObject.enableSpecularAntiAliasing = this.enableSpecularAntiAliasing;\r\n        serializationObject.realTimeFiltering = this.realTimeFiltering;\r\n        serializationObject.realTimeFilteringQuality = this.realTimeFilteringQuality;\r\n        serializationObject.useEnergyConservation = this.useEnergyConservation;\r\n        serializationObject.useRadianceOcclusion = this.useRadianceOcclusion;\r\n        serializationObject.useHorizonOcclusion = this.useHorizonOcclusion;\r\n        serializationObject.unlit = this.unlit;\r\n        serializationObject.forceNormalForward = this.forceNormalForward;\r\n        serializationObject.debugMode = this.debugMode;\r\n        serializationObject.debugLimit = this.debugLimit;\r\n        serializationObject.debugFactor = this.debugFactor;\r\n        serializationObject.generateOnlyFragmentCode = this.generateOnlyFragmentCode;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        if (serializationObject.lightId) {\r\n            this.light = scene.getLightById(serializationObject.lightId);\r\n        }\r\n\r\n        this.lightFalloff = serializationObject.lightFalloff ?? 0;\r\n        this.useAlphaTest = serializationObject.useAlphaTest;\r\n        this.alphaTestCutoff = serializationObject.alphaTestCutoff;\r\n        this.useAlphaBlending = serializationObject.useAlphaBlending;\r\n        this.useRadianceOverAlpha = serializationObject.useRadianceOverAlpha;\r\n        this.useSpecularOverAlpha = serializationObject.useSpecularOverAlpha;\r\n        this.enableSpecularAntiAliasing = serializationObject.enableSpecularAntiAliasing;\r\n        this.realTimeFiltering = !!serializationObject.realTimeFiltering;\r\n        this.realTimeFilteringQuality = serializationObject.realTimeFilteringQuality ?? Constants.TEXTURE_FILTERING_QUALITY_LOW;\r\n        this.useEnergyConservation = serializationObject.useEnergyConservation;\r\n        this.useRadianceOcclusion = serializationObject.useRadianceOcclusion;\r\n        this.useHorizonOcclusion = serializationObject.useHorizonOcclusion;\r\n        this.unlit = serializationObject.unlit;\r\n        this.forceNormalForward = !!serializationObject.forceNormalForward;\r\n        this.debugMode = serializationObject.debugMode;\r\n        this.debugLimit = serializationObject.debugLimit;\r\n        this.debugFactor = serializationObject.debugFactor;\r\n        this.generateOnlyFragmentCode = !!serializationObject.generateOnlyFragmentCode;\r\n\r\n        this._setTarget();\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PBRMetallicRoughnessBlock\", PBRMetallicRoughnessBlock);\r\n"]}