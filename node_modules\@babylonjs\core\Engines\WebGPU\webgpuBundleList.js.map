{"version": 3, "file": "webgpuBundleList.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuBundleList.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAQ5D,gBAAgB;AAChB,MAAM,OAAO,wBAAwB;IAMjC,YAAmB,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACzD,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAEM,GAAG,CAAC,UAAgC;QACvC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACjE,CAAC;IAEM,KAAK;QACR,OAAO,IAAI,wBAAwB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,OAAO,uBAAuB;IAChC,YACW,CAAS,EACT,CAAS,EACT,CAAS,EACT,CAAS;QAHT,MAAC,GAAD,CAAC,CAAQ;QACT,MAAC,GAAD,CAAC,CAAQ;QACT,MAAC,GAAD,CAAC,CAAQ;QACT,MAAC,GAAD,CAAC,CAAQ;IACjB,CAAC;IAEG,GAAG,CAAC,UAAgC;QACvC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC;IAEM,KAAK;QACR,OAAO,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,OAAO,0BAA0B;IACnC,YAA0B,GAAW;QAAX,QAAG,GAAH,GAAG,CAAQ;IAAG,CAAC;IAElC,GAAG,CAAC,UAAgC;QACvC,UAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IAEM,KAAK;QACR,OAAO,IAAI,0BAA0B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,OAAO,0BAA0B;IACnC,YAA0B,KAAyB;QAAzB,UAAK,GAAL,KAAK,CAAoB;IAAG,CAAC;IAEhD,GAAG,CAAC,UAAgC;QACvC,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAiB,CAAC,CAAC;IACxD,CAAC;IAEM,KAAK;QACR,OAAO,IAAI,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACtD,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,OAAO,mCAAmC;IAC5C,YAA0B,KAAa;QAAb,UAAK,GAAL,KAAK,CAAQ;IAAG,CAAC;IAEpC,GAAG,CAAC,UAAgC;QACvC,UAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC;IAEM,KAAK;QACR,OAAO,IAAI,mCAAmC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/D,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,OAAO,iCAAiC;IAC1C,gBAAsB,CAAC;IAEhB,GAAG,CAAC,UAAgC;QACvC,UAAU,CAAC,iBAAiB,EAAE,CAAC;IACnC,CAAC;IAEM,KAAK;QACR,OAAO,IAAI,iCAAiC,EAAE,CAAC;IACnD,CAAC;CACJ;AAED,MAAM,uBAAuB;IAGzB;QACI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IACtB,CAAC;IAEM,GAAG,CAAC,UAAgC;QACvC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC;IAEM,KAAK;QACR,MAAM,MAAM,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAC7C,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC9B,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,OAAO,gBAAgB;IAYzB,YAAmB,MAAiB;QAF7B,iBAAY,GAAG,CAAC,CAAC;QAGpB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IACzB,CAAC;IAEM,SAAS,CAAC,MAAwB;QACrC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,MAAM,IAAI,GAAG,IAAI,uBAAuB,EAAE,CAAC;YAE3C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC;YACtC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC;YACvC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SACpC;QACD,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACxC;IACL,CAAC;IAEO,aAAa;QACjB,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,EAAE;YAClD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;YAC3D,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;YAChC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;SACrC;IACL,CAAC;IAEM,OAAO,CAAC,IAAuB;QAClC,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC;QACtC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACtC,CAAC;IAEM,gBAAgB,CAAC,YAAyC,EAAE,kBAAgD,EAAE,WAAmB;QACpI,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC;gBACzD,YAAY;gBACZ,kBAAkB;gBAClB,WAAW,EAAE,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC;aAC1D,CAAC,CAAC;SACN;QACD,OAAO,IAAI,CAAC,cAAe,CAAC;IAChC,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEM,GAAG,CAAC,UAAgC;QACvC,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE;YACvC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;SACjC;IACL,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;IAC1B,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,MAAM,MAAM,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAElD,MAAM,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3C,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACtC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE;YACvC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;SAC3C;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { WebGPUTextureHelper } from \"./webgpuTextureHelper\";\r\n\r\n/** @internal */\r\ninterface IWebGPURenderItem {\r\n    run(renderPass: GPURenderPassEncoder): void;\r\n    clone(): IWebGPURenderItem;\r\n}\r\n\r\n/** @internal */\r\nexport class WebGPURenderItemViewport implements IWebGPURenderItem {\r\n    public x: number;\r\n    public y: number;\r\n    public w: number;\r\n    public h: number;\r\n\r\n    public constructor(x: number, y: number, w: number, h: number) {\r\n        this.x = Math.floor(x);\r\n        this.y = Math.floor(y);\r\n        this.w = Math.floor(w);\r\n        this.h = Math.floor(h);\r\n    }\r\n\r\n    public run(renderPass: GPURenderPassEncoder) {\r\n        renderPass.setViewport(this.x, this.y, this.w, this.h, 0, 1);\r\n    }\r\n\r\n    public clone(): WebGPURenderItemViewport {\r\n        return new WebGPURenderItemViewport(this.x, this.y, this.w, this.h);\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport class WebGPURenderItemScissor implements IWebGPURenderItem {\r\n    public constructor(\r\n        public x: number,\r\n        public y: number,\r\n        public w: number,\r\n        public h: number\r\n    ) {}\r\n\r\n    public run(renderPass: GPURenderPassEncoder) {\r\n        renderPass.setScissorRect(this.x, this.y, this.w, this.h);\r\n    }\r\n\r\n    public clone(): WebGPURenderItemScissor {\r\n        return new WebGPURenderItemScissor(this.x, this.y, this.w, this.h);\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport class WebGPURenderItemStencilRef implements IWebGPURenderItem {\r\n    public constructor(public ref: number) {}\r\n\r\n    public run(renderPass: GPURenderPassEncoder) {\r\n        renderPass.setStencilReference(this.ref);\r\n    }\r\n\r\n    public clone(): WebGPURenderItemStencilRef {\r\n        return new WebGPURenderItemStencilRef(this.ref);\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport class WebGPURenderItemBlendColor implements IWebGPURenderItem {\r\n    public constructor(public color: Nullable<number>[]) {}\r\n\r\n    public run(renderPass: GPURenderPassEncoder) {\r\n        renderPass.setBlendConstant(this.color as GPUColor);\r\n    }\r\n\r\n    public clone(): WebGPURenderItemBlendColor {\r\n        return new WebGPURenderItemBlendColor(this.color);\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport class WebGPURenderItemBeginOcclusionQuery implements IWebGPURenderItem {\r\n    public constructor(public query: number) {}\r\n\r\n    public run(renderPass: GPURenderPassEncoder) {\r\n        renderPass.beginOcclusionQuery(this.query);\r\n    }\r\n\r\n    public clone(): WebGPURenderItemBeginOcclusionQuery {\r\n        return new WebGPURenderItemBeginOcclusionQuery(this.query);\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport class WebGPURenderItemEndOcclusionQuery implements IWebGPURenderItem {\r\n    public constructor() {}\r\n\r\n    public run(renderPass: GPURenderPassEncoder) {\r\n        renderPass.endOcclusionQuery();\r\n    }\r\n\r\n    public clone(): WebGPURenderItemEndOcclusionQuery {\r\n        return new WebGPURenderItemEndOcclusionQuery();\r\n    }\r\n}\r\n\r\nclass WebGPURenderItemBundles implements IWebGPURenderItem {\r\n    public bundles: GPURenderBundle[];\r\n\r\n    public constructor() {\r\n        this.bundles = [];\r\n    }\r\n\r\n    public run(renderPass: GPURenderPassEncoder) {\r\n        renderPass.executeBundles(this.bundles);\r\n    }\r\n\r\n    public clone(): WebGPURenderItemBundles {\r\n        const cloned = new WebGPURenderItemBundles();\r\n        cloned.bundles = this.bundles;\r\n        return cloned;\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport class WebGPUBundleList {\r\n    private _device: GPUDevice;\r\n    private _bundleEncoder: GPURenderBundleEncoder | undefined;\r\n\r\n    private _list: IWebGPURenderItem[];\r\n    private _listLength: number;\r\n\r\n    private _currentItemIsBundle: boolean;\r\n    private _currentBundleList: GPURenderBundle[];\r\n\r\n    public numDrawCalls = 0;\r\n\r\n    public constructor(device: GPUDevice) {\r\n        this._device = device;\r\n        this._list = new Array(10);\r\n        this._listLength = 0;\r\n    }\r\n\r\n    public addBundle(bundle?: GPURenderBundle): void {\r\n        if (!this._currentItemIsBundle) {\r\n            const item = new WebGPURenderItemBundles();\r\n\r\n            this._list[this._listLength++] = item;\r\n            this._currentBundleList = item.bundles;\r\n            this._currentItemIsBundle = true;\r\n        }\r\n        if (bundle) {\r\n            this._currentBundleList.push(bundle);\r\n        }\r\n    }\r\n\r\n    private _finishBundle(): void {\r\n        if (this._currentItemIsBundle && this._bundleEncoder) {\r\n            this._currentBundleList.push(this._bundleEncoder.finish());\r\n            this._bundleEncoder = undefined;\r\n            this._currentItemIsBundle = false;\r\n        }\r\n    }\r\n\r\n    public addItem(item: IWebGPURenderItem) {\r\n        this._finishBundle();\r\n        this._list[this._listLength++] = item;\r\n        this._currentItemIsBundle = false;\r\n    }\r\n\r\n    public getBundleEncoder(colorFormats: (GPUTextureFormat | null)[], depthStencilFormat: GPUTextureFormat | undefined, sampleCount: number): GPURenderBundleEncoder {\r\n        if (!this._currentItemIsBundle) {\r\n            this.addBundle();\r\n            this._bundleEncoder = this._device.createRenderBundleEncoder({\r\n                colorFormats,\r\n                depthStencilFormat,\r\n                sampleCount: WebGPUTextureHelper.GetSample(sampleCount),\r\n            });\r\n        }\r\n        return this._bundleEncoder!;\r\n    }\r\n\r\n    public close(): void {\r\n        this._finishBundle();\r\n    }\r\n\r\n    public run(renderPass: GPURenderPassEncoder) {\r\n        this.close();\r\n        for (let i = 0; i < this._listLength; ++i) {\r\n            this._list[i].run(renderPass);\r\n        }\r\n    }\r\n\r\n    public reset() {\r\n        this._listLength = 0;\r\n        this._currentItemIsBundle = false;\r\n        this.numDrawCalls = 0;\r\n    }\r\n\r\n    public clone(): WebGPUBundleList {\r\n        this.close();\r\n\r\n        const cloned = new WebGPUBundleList(this._device);\r\n\r\n        cloned._list = new Array(this._listLength);\r\n        cloned._listLength = this._listLength;\r\n        cloned.numDrawCalls = this.numDrawCalls;\r\n\r\n        for (let i = 0; i < this._listLength; ++i) {\r\n            cloned._list[i] = this._list[i].clone();\r\n        }\r\n\r\n        return cloned;\r\n    }\r\n}\r\n"]}