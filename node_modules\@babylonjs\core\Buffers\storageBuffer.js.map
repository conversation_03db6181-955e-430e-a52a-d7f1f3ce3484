{"version": 3, "file": "storageBuffer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Buffers/storageBuffer.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEjD;;GAEG;AACH,MAAM,OAAO,aAAa;IAOtB;;;;;;OAMG;IACH,YAAY,MAAkB,EAAE,IAAY,EAAE,aAAa,GAAG,SAAS,CAAC,6BAA6B,EAAE,KAAc;QACjH,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IACtC,CAAC;IAEO,OAAO,CAAC,IAAY,EAAE,aAAqB;QAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACtF,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IACxD,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,IAAe,EAAE,UAAmB,EAAE,UAAmB;QACnE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QAED,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IACjF,CAAC;IAED;;;;;;;OAOG;IACI,IAAI,CAAC,MAAe,EAAE,IAAa,EAAE,MAAwB,EAAE,OAAiB;QACnF,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IAC3F,CAAC;IAED;;OAEG;IACI,OAAO;QACV,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;QACpD,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,cAAc,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAClE,cAAc,CAAC,GAAG,EAAE,CAAC;SACxB;QAED,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,GAAG,IAAW,CAAC;IAC/B,CAAC;CACJ", "sourcesContent": ["import type { ThinEngine } from \"../Engines/thinEngine\";\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport type { DataArray } from \"../types\";\r\nimport { Constants } from \"../Engines/constants\";\r\n\r\n/**\r\n * This class is a small wrapper around a native buffer that can be read and/or written\r\n */\r\nexport class StorageBuffer {\r\n    private _engine: ThinEngine;\r\n    private _buffer: DataBuffer;\r\n    private _bufferSize: number;\r\n    private _creationFlags: number;\r\n    private _label?: string;\r\n\r\n    /**\r\n     * Creates a new storage buffer instance\r\n     * @param engine The engine the buffer will be created inside\r\n     * @param size The size of the buffer in bytes\r\n     * @param creationFlags flags to use when creating the buffer (see Constants.BUFFER_CREATIONFLAG_XXX). The BUFFER_CREATIONFLAG_STORAGE flag will be automatically added.\r\n     * @param label defines the label of the buffer (for debug purpose)\r\n     */\r\n    constructor(engine: ThinEngine, size: number, creationFlags = Constants.BUFFER_CREATIONFLAG_READWRITE, label?: string) {\r\n        this._engine = engine;\r\n        this._label = label;\r\n        this._engine._storageBuffers.push(this);\r\n        this._create(size, creationFlags);\r\n    }\r\n\r\n    private _create(size: number, creationFlags: number): void {\r\n        this._bufferSize = size;\r\n        this._creationFlags = creationFlags;\r\n        this._buffer = this._engine.createStorageBuffer(size, creationFlags, this._label);\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuild(): void {\r\n        this._create(this._bufferSize, this._creationFlags);\r\n    }\r\n\r\n    /**\r\n     * Gets underlying native buffer\r\n     * @returns underlying native buffer\r\n     */\r\n    public getBuffer(): DataBuffer {\r\n        return this._buffer;\r\n    }\r\n\r\n    /**\r\n     * Updates the storage buffer\r\n     * @param data the data used to update the storage buffer\r\n     * @param byteOffset the byte offset of the data (optional)\r\n     * @param byteLength the byte length of the data (optional)\r\n     */\r\n    public update(data: DataArray, byteOffset?: number, byteLength?: number): void {\r\n        if (!this._buffer) {\r\n            return;\r\n        }\r\n\r\n        this._engine.updateStorageBuffer(this._buffer, data, byteOffset, byteLength);\r\n    }\r\n\r\n    /**\r\n     * Reads data from the storage buffer\r\n     * @param offset The offset in the storage buffer to start reading from (default: 0)\r\n     * @param size  The number of bytes to read from the storage buffer (default: capacity of the buffer)\r\n     * @param buffer The buffer to write the data we have read from the storage buffer to (optional)\r\n     * @param noDelay If true, a call to flushFramebuffer will be issued so that the data can be read back immediately. This can speed up data retrieval, at the cost of a small perf penalty (default: false).\r\n     * @returns If not undefined, returns the (promise) buffer (as provided by the 4th parameter) filled with the data, else it returns a (promise) Uint8Array with the data read from the storage buffer\r\n     */\r\n    public read(offset?: number, size?: number, buffer?: ArrayBufferView, noDelay?: boolean): Promise<ArrayBufferView> {\r\n        return this._engine.readFromStorageBuffer(this._buffer, offset, size, buffer, noDelay);\r\n    }\r\n\r\n    /**\r\n     * Disposes the storage buffer\r\n     */\r\n    public dispose(): void {\r\n        const storageBuffers = this._engine._storageBuffers;\r\n        const index = storageBuffers.indexOf(this);\r\n\r\n        if (index !== -1) {\r\n            storageBuffers[index] = storageBuffers[storageBuffers.length - 1];\r\n            storageBuffers.pop();\r\n        }\r\n\r\n        this._engine._releaseBuffer(this._buffer);\r\n        this._buffer = null as any;\r\n    }\r\n}\r\n"]}