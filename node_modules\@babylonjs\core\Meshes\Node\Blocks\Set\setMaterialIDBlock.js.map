{"version": 3, "file": "setMaterialIDBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Meshes/Node/Blocks/Set/setMaterialIDBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,qCAAqC,EAAE,MAAM,8CAA8C,CAAC;AACrG,OAAO,EAAE,sBAAsB,EAAmB,MAAM,oCAAoC,CAAC;AAC7F,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAGtG;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,iBAAiB;IAQrD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAZhB;;;WAGG;QAEI,oBAAe,GAAG,IAAI,CAAC;QAS1B,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,qCAAqC,CAAC,QAAQ,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,qCAAqC,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAE7E,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,QAAQ,CAAC,CAAC;QAC9E,IAAI,CAAC,EAAE,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,KAAK,CAAC,CAAC;IAC3F,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,EAAE;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;YAChC,OAAO;SACV;QAED,MAAM,IAAI,GAAG,CAAC,KAA6B,EAAE,EAAE;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAe,CAAC;YACxE,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;gBAC7D,OAAO,UAAU,CAAC;aACrB;YAED,MAAM,YAAY,GAAG,IAAI,sBAAsB,EAAE,CAAC;YAClD,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClE,YAAY,CAAC,UAAU,GAAG,CAAC,CAAC;YAC5B,YAAY,CAAC,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;YACpD,YAAY,CAAC,aAAa,GAAG,CAAC,CAAC;YAC/B,YAAY,CAAC,aAAa,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YAE7D,UAAU,CAAC,aAAa,GAAG,CAAC,YAAY,CAAC,CAAC;YAE1C,OAAO,UAAU,CAAC;QACtB,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;SACtC;aAAM;YACH,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1C;IACL,CAAC;IAES,mBAAmB;QACzB,MAAM,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,GAAG,GAAG,IAAI,CAAC,iBAAiB,sBAAsB,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC;QAC7I,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAE3D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB;QACxC,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAExC,IAAI,mBAAmB,CAAC,eAAe,KAAK,SAAS,EAAE;YACnD,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC,eAAe,CAAC;SAC9D;IACL,CAAC;CACJ;AAtGU;IADN,sBAAsB,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC;2DAC3F;AAwGlC,aAAa,CAAC,4BAA4B,EAAE,kBAAkB,CAAC,CAAC", "sourcesContent": ["import { NodeGeometry<PERSON>lock } from \"../../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../../nodeGeometryBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"../../Enums/nodeGeometryConnectionPointTypes\";\r\nimport { VertexDataMaterialInfo, type VertexData } from \"../../../../Meshes/mesh.vertexData\";\r\nimport { PropertyTypeForEdition, editableInPropertyPage } from \"../../../../Decorators/nodeDecorator\";\r\nimport type { NodeGeometryBuildState } from \"../../nodeGeometryBuildState\";\r\n\r\n/**\r\n * Block used to affect a material ID to a geometry\r\n */\r\nexport class SetMaterialIDBlock extends NodeGeometryBlock {\r\n    /**\r\n     * Gets or sets a boolean indicating that this block can evaluate context\r\n     * Build performance is improved when this value is set to false as the system will cache values instead of reevaluating everything per context change\r\n     */\r\n    @editableInPropertyPage(\"Evaluate context\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { rebuild: true } })\r\n    public evaluateContext = true;\r\n\r\n    /**\r\n     * Create a new SetMaterialIDBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"geometry\", NodeGeometryBlockConnectionPointTypes.Geometry);\r\n        this.registerInput(\"id\", NodeGeometryBlockConnectionPointTypes.Int, true, 0);\r\n\r\n        this.registerOutput(\"output\", NodeGeometryBlockConnectionPointTypes.Geometry);\r\n        this.id.acceptedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Float);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"SetMaterialIDBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the geometry input component\r\n     */\r\n    public get geometry(): NodeGeometryConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the id input component\r\n     */\r\n    public get id(): NodeGeometryConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the geometry output component\r\n     */\r\n    public get output(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeGeometryBuildState) {\r\n        if (!this.geometry.isConnected) {\r\n            this.output._storedFunction = null;\r\n            this.output._storedValue = null;\r\n            return;\r\n        }\r\n\r\n        const func = (state: NodeGeometryBuildState) => {\r\n            const vertexData = this.geometry.getConnectedValue(state) as VertexData;\r\n            if (!vertexData || !vertexData.indices || !vertexData.positions) {\r\n                return vertexData;\r\n            }\r\n\r\n            const materialInfo = new VertexDataMaterialInfo();\r\n            materialInfo.materialIndex = this.id.getConnectedValue(state) | 0;\r\n            materialInfo.indexStart = 0;\r\n            materialInfo.indexCount = vertexData.indices.length;\r\n            materialInfo.verticesStart = 0;\r\n            materialInfo.verticesCount = vertexData.positions.length / 3;\r\n\r\n            vertexData.materialInfos = [materialInfo];\r\n\r\n            return vertexData;\r\n        };\r\n\r\n        if (this.evaluateContext) {\r\n            this.output._storedFunction = func;\r\n        } else {\r\n            this.output._storedFunction = null;\r\n            this.output._storedValue = func(state);\r\n        }\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        const codeString = super._dumpPropertiesCode() + `${this._codeVariableName}.evaluateContext = ${this.evaluateContext ? \"true\" : \"false\"};\\n`;\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Serializes this block in a JSON representation\r\n     * @returns the serialized block object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.evaluateContext = this.evaluateContext;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any) {\r\n        super._deserialize(serializationObject);\r\n\r\n        if (serializationObject.evaluateContext !== undefined) {\r\n            this.evaluateContext = serializationObject.evaluateContext;\r\n        }\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SetMaterialIDBlock\", SetMaterialIDBlock);\r\n"]}