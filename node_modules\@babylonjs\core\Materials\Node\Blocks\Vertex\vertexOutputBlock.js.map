{"version": 3, "file": "vertexOutputBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Vertex/vertexOutputBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAK3D;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,iBAAiB;IACpD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEnD,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;IAChF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAEO,0BAA0B,CAAC,QAAwC,EAAE,mBAA4B;QACrG,IAAI,mBAAmB,EAAE;YACrB,OAAO,IAAI,CAAC;SACf;QAED,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;YACzB,IAAK,IAA4B,CAAC,mBAAmB,EAAE;gBACnD,OAAO,IAAI,CAAC;aACf;SACJ;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,KAAK,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,CAAC,sBAAsB,KAAK,CAAC;QAE9E,IAAI,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,UAAU,CAAC,mBAAmB,EAAE,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE;YAC1H,KAAK,CAAC,sBAAsB,CAAC,0BAA0B,EAAE,OAAO,CAAC,CAAC;YAClE,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;YAExD,KAAK,CAAC,iBAAiB,IAAI,yCAAyC,CAAC;YACrE,KAAK,CAAC,iBAAiB,IAAI,mFAAmF,CAAC;SAClH;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,2BAA2B,EAAE,iBAAiB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Immutable } from \"../../../../types\";\r\n\r\nimport type { FragmentOutputBlock } from \"../Fragment/fragmentOutputBlock\";\r\n\r\n/**\r\n * Block used to output the vertex position\r\n */\r\nexport class VertexOutputBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new VertexOutputBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Vertex, true);\r\n\r\n        this.registerInput(\"vector\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"VertexOutputBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the vector input component\r\n     */\r\n    public get vector(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    private _isLogarithmicDepthEnabled(nodeList: Immutable<NodeMaterialBlock[]>, useLogarithmicDepth: boolean): boolean {\r\n        if (useLogarithmicDepth) {\r\n            return true;\r\n        }\r\n\r\n        for (const node of nodeList) {\r\n            if ((node as FragmentOutputBlock).useLogarithmicDepth) {\r\n                return true;\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const input = this.vector;\r\n\r\n        state.compilationString += `gl_Position = ${input.associatedVariableName};\\n`;\r\n\r\n        if (this._isLogarithmicDepthEnabled(state.sharedData.fragmentOutputNodes, state.sharedData.nodeMaterial.useLogarithmicDepth)) {\r\n            state._emitUniformFromString(\"logarithmicDepthConstant\", \"float\");\r\n            state._emitVaryingFromString(\"vFragmentDepth\", \"float\");\r\n\r\n            state.compilationString += `vFragmentDepth = 1.0 + gl_Position.w;\\n`;\r\n            state.compilationString += `gl_Position.z = log2(max(0.000001, vFragmentDepth)) * logarithmicDepthConstant;\\n`;\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.VertexOutputBlock\", VertexOutputBlock);\r\n"]}