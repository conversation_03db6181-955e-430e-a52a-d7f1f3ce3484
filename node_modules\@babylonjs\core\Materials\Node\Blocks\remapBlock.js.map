{"version": 3, "file": "remapBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/remapBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAGvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AAErD,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,mCAAmC,CAAC;AACnG;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,iBAAiB;IAa7C;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAjBlD;;WAEG;QAEI,gBAAW,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAExC;;WAEG;QAEI,gBAAW,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QASnC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAC9E,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACnF,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACnF,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACnF,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAEnF,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,YAAY,CAAC,CAAC;QAElF,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEhC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC5H,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAE5H,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC5H,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAE5H,KAAK,CAAC,iBAAiB;YACnB,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC;gBAClC,MAAM,SAAS,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,sBAAsB,MAAM,SAAS,QAAQ,SAAS,MAAM,SAAS,QAAQ,SAAS,MAAM,SAAS,MAAM,CAAC;QAEtJ,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,GAAG,GAAG,IAAI,CAAC,iBAAiB,sCAAsC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC;QAE9J,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,sCAAsC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC;QAE7H,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC7D,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAE7D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACtE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;IAC1E,CAAC;CACJ;AArHU;IADN,sBAAsB,CAAC,MAAM,EAAE,sBAAsB,CAAC,OAAO,CAAC;+CACvB;AAMjC;IADN,sBAAsB,CAAC,IAAI,EAAE,sBAAsB,CAAC,OAAO,CAAC;+CACtB;AAiH3C,aAAa,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport { Vector2 } from \"../../../Maths/math.vector\";\r\nimport type { Scene } from \"../../../scene\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../Decorators/nodeDecorator\";\r\n/**\r\n * Block used to remap a float from a range to a new one\r\n */\r\nexport class RemapBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Gets or sets the source range\r\n     */\r\n    @editableInPropertyPage(\"From\", PropertyTypeForEdition.Vector2)\r\n    public sourceRange = new Vector2(-1, 1);\r\n\r\n    /**\r\n     * Gets or sets the target range\r\n     */\r\n    @editableInPropertyPage(\"To\", PropertyTypeForEdition.Vector2)\r\n    public targetRange = new Vector2(0, 1);\r\n\r\n    /**\r\n     * Creates a new RemapBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerInput(\"input\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerInput(\"sourceMin\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"sourceMax\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"targetMin\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"targetMax\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.BasedOnInput);\r\n\r\n        this._outputs[0]._typeConnectionSource = this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"RemapBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the input component\r\n     */\r\n    public get input(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the source min input component\r\n     */\r\n    public get sourceMin(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the source max input component\r\n     */\r\n    public get sourceMax(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the target min input component\r\n     */\r\n    public get targetMin(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the target max input component\r\n     */\r\n    public get targetMax(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const output = this._outputs[0];\r\n\r\n        const sourceMin = this.sourceMin.isConnected ? this.sourceMin.associatedVariableName : this._writeFloat(this.sourceRange.x);\r\n        const sourceMax = this.sourceMax.isConnected ? this.sourceMax.associatedVariableName : this._writeFloat(this.sourceRange.y);\r\n\r\n        const targetMin = this.targetMin.isConnected ? this.targetMin.associatedVariableName : this._writeFloat(this.targetRange.x);\r\n        const targetMax = this.targetMax.isConnected ? this.targetMax.associatedVariableName : this._writeFloat(this.targetRange.y);\r\n\r\n        state.compilationString +=\r\n            this._declareOutput(output, state) +\r\n            ` = ${targetMin} + (${this._inputs[0].associatedVariableName} - ${sourceMin}) * (${targetMax} - ${targetMin}) / (${sourceMax} - ${sourceMin});\\n`;\r\n\r\n        return this;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode() + `${this._codeVariableName}.sourceRange = new BABYLON.Vector2(${this.sourceRange.x}, ${this.sourceRange.y});\\n`;\r\n\r\n        codeString += `${this._codeVariableName}.targetRange = new BABYLON.Vector2(${this.targetRange.x}, ${this.targetRange.y});\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.sourceRange = this.sourceRange.asArray();\r\n        serializationObject.targetRange = this.targetRange.asArray();\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.sourceRange = Vector2.FromArray(serializationObject.sourceRange);\r\n        this.targetRange = Vector2.FromArray(serializationObject.targetRange);\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.RemapBlock\", RemapBlock);\r\n"]}