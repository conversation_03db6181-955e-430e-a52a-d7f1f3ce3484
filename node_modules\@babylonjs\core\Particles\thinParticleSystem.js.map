{"version": 3, "file": "thinParticleSystem.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Particles/thinParticleSystem.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAElG,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAC5E,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAGzD,OAAO,EAAE,UAAU,EAAE,MAAM,kCAAkC,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAIrD,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAEjD,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAEvD,OAAO,+BAA+B,CAAC;AACvC,OAAO,6BAA6B,CAAC;AAErC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAIhE,OAAO,oCAAoC,CAAC;AAC5C,OAAO,EAAE,oBAAoB,EAAE,iCAAiC,EAAE,aAAa,EAAE,MAAM,sCAAsC,CAAC;AAK9H,OAAO,EAAE,iBAAiB,EAAE,YAAY,EAAE,MAAM,uCAAuC,CAAC;AACxF,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AACvE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,gCAAgC,CAAC;AAC1E,OAAO,EAAE,iCAAiC,EAAE,iCAAiC,EAAE,MAAM,qDAAqD,CAAC;AAE3I;;;;;;GAMG;AACH,MAAM,OAAO,kBAAmB,SAAQ,kBAAkB;IAoCtD;;OAEG;IACH,IAAW,SAAS,CAAC,QAAoB;QACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC5D;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrE,CAAC;IA2DD;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED,IAAW,gBAAgB,CAAC,KAAc;QACtC,IAAI,IAAI,CAAC,iBAAiB,KAAK,KAAK,EAAE;YAClC,OAAO;SACV;QAED,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAE/B,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAUD;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,UAAU;QACb,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,YAAoB,CAAC;QACxC,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAE,CAAC,MAAM,CAAC;IACtF,CAAC;IAEO,qBAAqB,CAAC,YAAoB,CAAC;QAC/C,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IACtE,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,MAAwB,EAAE,YAAoB,CAAC;QAClE,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChE,IAAI,CAAC,eAAe,CAAC,SAAS,CAAE,CAAC,MAAM,GAAG,MAAM,CAAC;QACjD,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAE,CAAC,WAAW,EAAE;YAC9C,IAAI,CAAC,eAAe,CAAC,SAAS,CAAE,CAAC,WAAY,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;SACrF;IACL,CAAC;IAKD;;OAEG;IACH,IAAW,+BAA+B;QACtC,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE;YACxC,IAAI,CAAC,gCAAgC,GAAG,IAAI,UAAU,EAAoB,CAAC;SAC9E;QAED,OAAO,IAAI,CAAC,gCAAgC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;;;;;;OASG;IACH,YACI,IAAY,EACZ,QAAgB,EAChB,aAAiC,EACjC,eAAiC,IAAI,EACrC,0BAAmC,KAAK,EACxC,UAAkB,IAAI;QAEtB,KAAK,CAAC,IAAI,CAAC,CAAC;QArOR,+BAA0B,GAAW,MAAM,CAAC,QAAQ,EAAE,CAAC;QAa/D;;WAEG;QACI,6BAAwB,GAAG,IAAI,OAAO,EAAE,CAAC;QAChD;;WAEG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAmB,CAAC;QAC/D;;WAEG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAmB,CAAC;QAavD,eAAU,GAAG,IAAI,KAAK,EAAY,CAAC;QAGnC,oBAAe,GAAG,IAAI,KAAK,EAAY,CAAC;QACxC,oBAAe,GAAG,CAAC,CAAC;QAGpB,mBAAc,GAAoC,EAAE,CAAC;QAQrD,qBAAgB,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1C,eAAU,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,qBAAgB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,mBAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAChC,qBAAgB,GAAG,CAAC,CAAC,CAAC;QAEtB,mBAAc,GAAG,KAAK,CAAC;QAGvB,aAAQ,GAAG,KAAK,CAAC;QACjB,aAAQ,GAAG,KAAK,CAAC;QACjB,iBAAY,GAAG,CAAC,CAAC;QAMzB,gBAAgB;QACT,sBAAiB,GAAG,CAAC,CAAC;QAC7B,gBAAgB;QACT,sBAAiB,GAAG,CAAC,CAAC;QAI7B,gBAAgB;QACT,uBAAkB,GAAG,CAAC,CAAC;QAC9B,gBAAgB;QACT,uBAAkB,GAAG,CAAC,CAAC;QAE9B,6EAA6E;QAC7D,oBAAe,GAAG,IAAI,CAAC;QAEtB,qBAAgB,GAAG,GAAG,CAAC;QAEhC,sBAAiB,GAAG,KAAK,CAAC;QAyBlC;;WAEG;QACI,YAAO,GAAG,KAAK,CAAC;QAEvB,sDAAsD;QACtC,UAAK,GAAG,KAAK,CAAC;QA2D9B,gBAAgB;QACR,qCAAgC,GAA2C,IAAI,CAAC;QA2TxF,gBAAgB;QACT,sBAAiB,GAAiC,CAAC,QAAQ,EAAE,EAAE;YAClE,aAAa;QACjB,CAAC,CAAC;QAi0BF,8BAA8B;QAE9B;;;;WAIG;QACI,oBAAe,GAAiC,CAAC,QAAQ,EAAE,EAAE;YAChE,4DAA4D;YAC5D,MAAM,YAAY,GAAa,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YACrD,IAAI,YAAY,KAAK,QAAQ,EAAE;gBAC3B,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;aACjC;YACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5C,CAAC,CAAC;QAEM,oBAAe,GAAmB,GAAG,EAAE;YAC3C,IAAI,QAAkB,CAAC;YACvB,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;gBACnC,QAAQ,GAAa,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC;gBAChD,QAAQ,CAAC,MAAM,EAAE,CAAC;aACrB;iBAAM;gBACH,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;aACjC;YAED,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAChC,OAAO,QAAQ,CAAC;QACpB,CAAC,CAAC;QApmCE,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,wBAAwB,GAAG,uBAAuB,CAAC;QAExD,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,YAAY,EAAE,KAAK,OAAO,EAAE;YAC5D,IAAI,CAAC,MAAM,GAAI,aAAuB,IAAI,WAAW,CAAC,gBAAgB,CAAC;YACvE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC1C;aAAM;YACH,IAAI,CAAC,OAAO,GAAG,aAA2B,CAAC;YAC3C,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;SAC1G;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,iBAAiB,EAAE;YAC1C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAClC;QAED,2DAA2D;QAC3D,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,CAAC;QAE/C,gEAAgE;QAChE,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC,EAAE,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5D,IAAI,CAAC,eAAe,CAAC,CAAC,CAAE,CAAC,MAAM,GAAG,YAAY,CAAC;QAE/C,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,eAAe,CAAC;QAE7D,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,uBAAuB;QACvB,IAAI,CAAC,mBAAmB,GAAG,IAAI,kBAAkB,EAAE,CAAC;QACpD,IAAI,gBAAgB,GAAyB,IAAI,CAAC;QAElD,SAAS;QACT,IAAI,CAAC,cAAc,GAAG,CAAC,SAAqB,EAAQ,EAAE;YAClD,IAAI,gBAAgB,GAAoB,IAAI,CAAC;YAE7C,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,0CAA0C;gBAC1C,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC/C,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC1C,gBAAgB,GAAG,IAAkB,CAAC;gBAC1C,CAAC,CAAC,CAAC;aACN;YAED,MAAM,iBAAiB,GAAG,SAAS,KAAK,IAAI,CAAC,UAAU,CAAC;YAExD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACnD,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;gBAElC,IAAI,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAChD,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC;gBACjC,QAAQ,CAAC,GAAG,IAAI,iBAAiB,CAAC;gBAElC,yBAAyB;gBACzB,IAAI,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,QAAQ,EAAE;oBAClC,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,GAAG,WAAW,CAAC;oBACxC,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC;oBAEhD,iBAAiB,GAAG,CAAC,OAAO,GAAG,iBAAiB,CAAC,GAAG,IAAI,CAAC;oBAEzD,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC;iBACpC;gBAED,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC;gBAE/C,QAAQ;gBACR,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;oBACzD,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;wBACpG,IAAI,eAAe,KAAK,QAAQ,CAAC,qBAAqB,EAAE;4BACpD,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;4BAC1C,YAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;4BACrE,QAAQ,CAAC,qBAAqB,GAAkB,eAAe,CAAC;yBACnE;wBACD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,cAAc,EAAE,QAAQ,CAAC,cAAc,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAC9F,CAAC,CAAC,CAAC;iBACN;qBAAM;oBACH,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBACxE,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBAEjD,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE;wBACtB,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;qBACxB;iBACJ;gBAED,gBAAgB;gBAChB,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE;oBACvE,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,sBAAsB,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;wBAC3G,IAAI,eAAe,KAAK,QAAQ,CAAC,4BAA4B,EAAE;4BAC3D,QAAQ,CAAC,qBAAqB,GAAG,QAAQ,CAAC,qBAAqB,CAAC;4BAChE,QAAQ,CAAC,qBAAqB,GAAoB,YAAa,CAAC,SAAS,EAAE,CAAC;4BAC5E,QAAQ,CAAC,4BAA4B,GAAmB,eAAe,CAAC;yBAC3E;wBACD,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,QAAQ,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;oBACxG,CAAC,CAAC,CAAC;iBACN;gBACD,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,YAAY,GAAG,iBAAiB,CAAC;gBAE5D,YAAY;gBACZ,IAAI,cAAc,GAAG,iBAAiB,CAAC;gBAEvC,YAAY;gBACZ,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC/D,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;wBACvG,IAAI,eAAe,KAAK,QAAQ,CAAC,wBAAwB,EAAE;4BACvD,QAAQ,CAAC,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,CAAC;4BACxD,QAAQ,CAAC,iBAAiB,GAAoB,YAAa,CAAC,SAAS,EAAE,CAAC;4BACxE,QAAQ,CAAC,wBAAwB,GAAmB,eAAe,CAAC;yBACvE;wBACD,cAAc,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;oBAC1F,CAAC,CAAC,CAAC;iBACN;gBAED,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAErE,kBAAkB;gBAClB,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE;oBACzE,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,uBAAuB,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;wBAC5G,IAAI,eAAe,KAAK,QAAQ,CAAC,6BAA6B,EAAE;4BAC5D,QAAQ,CAAC,sBAAsB,GAAG,QAAQ,CAAC,sBAAsB,CAAC;4BAClE,QAAQ,CAAC,sBAAsB,GAAoB,YAAa,CAAC,SAAS,EAAE,CAAC;4BAC7E,QAAQ,CAAC,6BAA6B,GAAmB,eAAe,CAAC;yBAC5E;wBAED,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE,QAAQ,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;wBACpG,MAAM,eAAe,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;wBAEpD,IAAI,eAAe,GAAG,aAAa,EAAE;4BACjC,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;yBAC9D;oBACL,CAAC,CAAC,CAAC;iBACN;gBAED,QAAQ;gBACR,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;oBACvD,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;wBACnG,IAAI,eAAe,KAAK,QAAQ,CAAC,oBAAoB,EAAE;4BACnD,QAAQ,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;4BAChD,QAAQ,CAAC,aAAa,GAAoB,YAAa,CAAC,SAAS,EAAE,CAAC;4BACpE,QAAQ,CAAC,oBAAoB,GAAmB,eAAe,CAAC;yBACnE;wBAED,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;wBAEzE,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;oBACnD,CAAC,CAAC,CAAC;iBACN;gBAED,IAAI,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE;oBACzC,QAAQ,CAAC,cAAe,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBAC3D,OAAO,CAAC,yBAAyB,CAAC,QAAQ,CAAC,cAAe,EAAE,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;iBAC5G;qBAAM;oBACH,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;iBACvD;gBAED,QAAQ;gBACR,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,QAAQ,CAAC,wBAAwB,EAAE;oBAC3E,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAC9B,QAAQ,CAAC,wBAAwB,CAAC,CAAC,EACnC,QAAQ,CAAC,wBAAwB,CAAC,CAAC,EACnC,gBAAgB,CAAC,KAAK,EACtB,gBAAgB,CAAC,MAAM,EACvB,gBAAgB,CACnB,CAAC;oBACF,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAC9B,QAAQ,CAAC,wBAAwB,CAAC,CAAC,EACnC,QAAQ,CAAC,wBAAwB,CAAC,CAAC,EACnC,gBAAgB,CAAC,KAAK,EACtB,gBAAgB,CAAC,MAAM,EACvB,gBAAgB,CACnB,CAAC;oBACF,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAC9B,QAAQ,CAAC,wBAAwB,CAAC,CAAC,EACnC,QAAQ,CAAC,wBAAwB,CAAC,CAAC,EACnC,gBAAgB,CAAC,KAAK,EACtB,gBAAgB,CAAC,MAAM,EACvB,gBAAgB,CACnB,CAAC;oBAEF,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBACpC,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBAE1C,KAAK,CAAC,cAAc,CAChB,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,EAC9C,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,EAC9C,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CACjD,CAAC;oBAEF,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;oBACjD,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;iBAC9C;gBAED,UAAU;gBACV,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAChE,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAEnD,OAAO;gBACP,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;oBACvD,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;wBACnG,IAAI,eAAe,KAAK,QAAQ,CAAC,oBAAoB,EAAE;4BACnD,QAAQ,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;4BAChD,QAAQ,CAAC,aAAa,GAAoB,YAAa,CAAC,SAAS,EAAE,CAAC;4BACpE,QAAQ,CAAC,oBAAoB,GAAmB,eAAe,CAAC;yBACnE;wBACD,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;oBAChF,CAAC,CAAC,CAAC;iBACN;gBAED,aAAa;gBACb,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBACxB,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;wBACnE,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;4BACzG,MAAM,GAAG,GAAG,IAAI,CAAkB,eAAgB,CAAC,OAAO,EAAmB,YAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;4BAC3G,MAAM,GAAG,GAAG,IAAI,CAAkB,eAAgB,CAAC,OAAQ,EAAmB,YAAa,CAAC,OAAQ,EAAE,KAAK,CAAC,CAAC;4BAE7G,QAAQ,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC;4BAC3B,QAAQ,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;wBACrC,CAAC,CAAC,CAAC;qBACN;oBAED,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;wBACnE,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;4BACzG,MAAM,GAAG,GAAG,IAAI,CAAkB,eAAgB,CAAC,OAAO,EAAmB,YAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;4BAC3G,MAAM,GAAG,GAAG,IAAI,CAAkB,eAAgB,CAAC,OAAQ,EAAmB,YAAa,CAAC,OAAQ,EAAE,KAAK,CAAC,CAAC;4BAE7G,QAAQ,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,CAAC;4BAC3B,QAAQ,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;wBACrC,CAAC,CAAC,CAAC;qBACN;iBACJ;gBAED,IAAI,IAAI,CAAC,wBAAwB,EAAE;oBAC/B,QAAQ,CAAC,eAAe,EAAE,CAAC;iBAC9B;gBAED,oFAAoF;gBACpF,QAAQ,CAAC,iCAAiC,EAAE,CAAC;gBAE7C,IAAI,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE;oBACnC,yCAAyC;oBACzC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBACjC,IAAI,QAAQ,CAAC,oBAAoB,EAAE;wBAC/B,QAAQ,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;4BACjD,UAAU,CAAC,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC;4BAC/C,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;wBACrC,CAAC,CAAC,CAAC;wBACH,QAAQ,CAAC,oBAAoB,GAAG,IAAI,CAAC;qBACxC;oBACD,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;oBAC/B,IAAI,iBAAiB,EAAE;wBACnB,KAAK,EAAE,CAAC;qBACX;oBACD,SAAS;iBACZ;aACJ;QACL,CAAC,CAAC;IACN,CAAC;IAOD,SAAS,CAAC,gBAAyB;QAC/B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,IAAY,EAAE,UAAe,EAAE,YAAY,GAAG,KAAK;QAC5D,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/C,CAAC;IAEO,kBAAkB,CAAC,eAAiC,EAAE,QAAgB,EAAE,MAAc,EAAE,OAAgB;QAC5G,MAAM,WAAW,GAAG,IAAI,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAClE,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAElC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE;gBACzB,OAAO,CAAC,CAAC,CAAC;aACb;iBAAM,IAAI,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE;gBAChC,OAAO,CAAC,CAAC;aACZ;YAED,OAAO,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,qBAAqB,CAAC,eAA2C,EAAE,QAAgB;QACvF,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO;SACV;QAED,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE;YAC1C,IAAI,cAAc,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBACtC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACjC,MAAM;aACT;YACD,KAAK,EAAE,CAAC;SACX;IACL,CAAC;IAED;;;;;;OAMG;IACI,mBAAmB,CAAC,QAAgB,EAAE,MAAc,EAAE,OAAgB;QACzE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;SAChC;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAE5E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,QAAgB;QAC1C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QAE9D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,eAAe,CAAC,QAAgB,EAAE,MAAc,EAAE,OAAgB;QACrE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;SAC5B;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAExE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,QAAgB;QACtC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAE1D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,qBAAqB,CAAC,QAAgB,EAAE,GAAW,EAAE,GAAW;QACnE,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;SAClC;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,oBAAoB,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAEvE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAAC,QAAgB;QAC5C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;QAEhE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,qBAAqB,CAAC,QAAgB,EAAE,GAAW,EAAE,GAAW;QACnE,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;SAClC;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,oBAAoB,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAEvE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAAC,QAAgB;QAC5C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;QAEhE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,uBAAuB,CAAC,QAAgB,EAAE,MAAc,EAAE,OAAgB;QAC7E,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;SACpC;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAEhF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,0BAA0B,CAAC,QAAgB;QAC9C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;QAElE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,mBAAmB,CAAC,QAAgB,EAAE,MAAc,EAAE,OAAgB;QACzE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;SAChC;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAE5E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,QAAgB;QAC1C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QAE9D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,wBAAwB,CAAC,QAAgB,EAAE,MAAc,EAAE,OAAgB;QAC9E,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAC;SACrC;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,uBAAuB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAEjF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,2BAA2B,CAAC,QAAgB;QAC/C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;QAEnE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,eAAe,CAAC,QAAgB,EAAE,MAAc,EAAE,OAAgB;QACrE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;SAC5B;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAExE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,QAAgB;QACtC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAE1D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,mBAAmB,CAAC,QAAgB,EAAE,MAAc,EAAE,OAAgB;QACzE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;SAChC;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAC5E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,QAAgB;QAC1C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QAE9D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,oBAAoB,CAAC,QAAgB,EAAE,MAAc,EAAE,OAAgB;QAC1E,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;SACjC;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAC7E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,QAAgB;QAC3C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC;QAE/D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,0BAA0B;QAC9B,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,qBAAqB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACnG,OAAO;SACV;QAED,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;QACvD,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE;YAC5C,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAExC,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;gBACnG,MAAM,CAAC,SAAS,CAAkB,eAAgB,CAAC,KAAK,EAAmB,YAAa,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;gBACjH,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;gBAC/B,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;gBACnC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;gBACnC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;YAC1B,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,4BAA4B,CAAC,CAAC;IACjK,CAAC;IAED;;;;OAIG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,uEAAuE;IAChE,qBAAqB;QACxB,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAEO,wBAAwB;QAC5B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9B,IAAI,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE;gBACzB,OAAO,CAAC,CAAC,CAAC;aACb;iBAAM,IAAI,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE;gBAChC,OAAO,CAAC,CAAC;aACZ;YAED,OAAO,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;YACrC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;SACrC;QAED,IAAI,CAAC,0BAA0B,EAAE,CAAC;IACtC,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,QAAgB,EAAE,KAAa;QAClD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;SAC5B;QAED,MAAM,YAAY,GAAG,IAAI,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QACzD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEvC,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEhC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,QAAgB;QACtC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC1F,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAElC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;YACvD,IAAI,CAAC,0BAA0B,EAAE,CAAC;SACrC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,gBAAgB,CAAC,QAAgB,EAAE,MAAc,EAAE,MAAe;QACrE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;SAC7B;QAED,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAClE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEzC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAI,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE;gBACzB,OAAO,CAAC,CAAC,CAAC;aACb;iBAAM,IAAI,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE;gBAChC,OAAO,CAAC,CAAC;aACZ;YAED,OAAO,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,QAAgB;QACvC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,eAAe,EAAE;YAC9C,IAAI,aAAa,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBACrC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACtC,MAAM;aACT;YACD,KAAK,EAAE,CAAC;SACX;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,cAAc;QACjB,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,aAAa,EAAE;YAC3C,IAAI,YAAY,EAAE;gBACd,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;oBACpC,WAAW,EAAE,OAAO,EAAE,CAAC;iBAC1B;aACJ;SACJ;QAED,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC5B,CAAC;IAEO,OAAO,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAE,MAAkB;QACnF,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;QAC5B,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;QAE5B,MAAM,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;QACzC,MAAM,QAAQ,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;QAE3C,MAAM,QAAQ,GAAG,CAAC,QAAQ,GAAG,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QACnD,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;IAClC,CAAC;IAES,MAAM;QACZ,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAEO,YAAY;QAChB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC/D,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAClC;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAEO,oBAAoB;QACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACvD,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;SAC/B;QAED,IACI,CAAC,IAAI,CAAC,iBAAiB;YACvB,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,iCAAiC;YAClE,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,uCAAuC,EAC1E;YACE,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;SAC/B;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;SAC/B;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,CAAC;QACjE,IAAI,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QAE5E,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC/I,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC;QAC3D,UAAU,IAAI,CAAC,CAAC;QAEhB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,YAAY,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACzI,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;QACrD,UAAU,IAAI,CAAC,CAAC;QAEhB,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC3H,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;QACvC,UAAU,IAAI,CAAC,CAAC;QAEhB,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACvH,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QACnC,UAAU,IAAI,CAAC,CAAC;QAEhB,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,WAAW,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACvI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,eAAe,CAAC;YACnD,UAAU,IAAI,CAAC,CAAC;SACnB;QAED,IACI,CAAC,IAAI,CAAC,iBAAiB;YACvB,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,iCAAiC;YAClE,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,uCAAuC,EAC1E;YACE,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,WAAW,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACvI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,eAAe,CAAC;YACnD,UAAU,IAAI,CAAC,CAAC;SACnB;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,WAAW,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACtI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,cAAc,CAAC;YAClD,UAAU,IAAI,CAAC,CAAC;SACnB;QAED,IAAI,OAAqB,CAAC;QAC1B,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC9D,IAAI,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YAC9D,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACnE;aAAM;YACH,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACtH,UAAU,IAAI,CAAC,CAAC;SACnB;QACD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC;QAExC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAEO,kBAAkB;QACtB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACtH,OAAO;SACV;QACD,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAC5B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;YACjD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxB,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YACxH,KAAK,IAAI,CAAC,CAAC;SACd;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC5D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;IAC9E,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,gBAAgB;IACT,SAAS;QACZ,aAAa;IACjB,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU;QAChC,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,uCAAuC,EAAE,EAAE;YAC5E,4CAA4C;YAC5C,MAAM,6HAA6H,CAAC;SACvI;QACD,IAAI,KAAK,EAAE;YACP,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,EAAE,KAAK,CAAC,CAAC;YACV,OAAO;SACV;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QAEtB,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,yDAAyD;QACzD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;gBACpC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;gBAC3D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,CAAC;gBACnE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;aACnD;YACD,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;gBACpC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;aACnE;SACJ;QACD,+DAA+D;QAC/D,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrC,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;gBAC7D,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,CAAC;gBACrE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;aACrD;YACD,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;aACrE;SACJ;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBACpD,IAAI,CAAC,OAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAClD;YAED,MAAM,wBAAwB,GAAG,IAAI,CAAC,YAAiC,CAAC;YAExE,IAAI,wBAAwB,IAAI,wBAAwB,CAAC,qBAAqB,EAAE;gBAC5E,wBAAwB,CAAC,qBAAqB,CAAC,OAAO,CAAC,GAAG,EAAE;oBACxD,UAAU,CAAC,GAAG,EAAE;wBACZ,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;4BACrD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;4BACnB,wBAAwB,CAAC,MAAM,EAAE,CAAC;yBACrC;oBACL,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;aACN;iBAAM;gBACH,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;oBACrD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBACtB;aACJ;SACJ;QAED,aAAa;QACb,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YAC5F,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC7G;IACL,CAAC;IAED;;;OAGG;IACI,IAAI,CAAC,eAAe,GAAG,IAAI;QAC9B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;SACV;QAED,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;IACpC,CAAC;IAED,gBAAgB;IACT,SAAS,CAAC,eAAwB;QACrC,aAAa;IACjB,CAAC;IAED,kBAAkB;IAElB;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,KAAa,EAAE,QAAkB,EAAE,OAAe,EAAE,OAAe;QAC5F,IAAI,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAE5C,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QACtE,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QACtE,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QACtE,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC;QAE5C,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC;QAC9D,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC;QAE9D,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC;SACnD;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,IAAI,QAAQ,CAAC,iBAAiB,EAAE;gBAC5B,IAAI,gBAAgB,GAAG,QAAQ,CAAC,iBAAiB,CAAC;gBAClD,IAAI,IAAI,CAAC,OAAO,EAAE;oBACd,OAAO,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChG,gBAAgB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;iBAC5C;gBACD,IAAI,gBAAgB,CAAC,CAAC,KAAK,CAAC,IAAI,gBAAgB,CAAC,CAAC,KAAK,CAAC,EAAE;oBACtD,gBAAgB,CAAC,CAAC,GAAG,KAAK,CAAC;iBAC9B;gBAED,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC;gBAChD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC;gBAChD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC;aACnD;iBAAM;gBACH,IAAI,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;gBACnC,IAAI,IAAI,CAAC,OAAO,EAAE;oBACd,OAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,CAAC,mBAAmB,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzF,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;iBACrC;gBAED,IAAI,SAAS,CAAC,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE;oBACxC,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC;iBACvB;gBACD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;gBACzC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;gBACzC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;aAC5C;SACJ;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,iCAAiC,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,uCAAuC,EAAE;YACvJ,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;SACrD;QAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,QAAQ,CAAC,SAAS,EAAE;YAC9C,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;SACrD;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,IAAI,OAAO,KAAK,CAAC,EAAE;oBACf,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;iBAC3B;qBAAM,IAAI,OAAO,KAAK,CAAC,EAAE;oBACtB,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;iBAC/B;gBAED,IAAI,OAAO,KAAK,CAAC,EAAE;oBACf,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;iBAC3B;qBAAM,IAAI,OAAO,KAAK,CAAC,EAAE;oBACtB,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;iBAC/B;aACJ;YAED,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,OAAO,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,OAAO,CAAC;SACxC;IACL,CAAC;IA+BD,gBAAgB;IACT,gBAAgB,CAAC,QAAkB;QACtC,YAAY;IAChB,CAAC;IAEO,OAAO,CAAC,YAAoB;QAChC,iBAAiB;QACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QAEzC,IAAmB,IAAI,CAAC,OAAQ,CAAC,QAAQ,EAAE;YACvC,MAAM,WAAW,GAAiB,IAAI,CAAC,OAAO,CAAC;YAC/C,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;SAC3D;aAAM;YACH,MAAM,eAAe,GAAY,IAAI,CAAC,OAAO,CAAC;YAC9C,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;SAC1G;QAED,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAErC,eAAe;QACf,IAAI,QAAkB,CAAC;QACvB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,EAAE,KAAK,EAAE,EAAE;YAC/C,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,EAAE;gBAC3C,MAAM;aACT;YAED,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAElC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE/B,YAAY;YACZ,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1F,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACjE,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,EAAE;oBAChG,MAAM,eAAe,GAAmB,eAAe,CAAC;oBACxD,MAAM,eAAe,GAAmB,YAAY,CAAC;oBACrD,MAAM,SAAS,GAAG,eAAe,CAAC,SAAS,EAAE,CAAC;oBAC9C,MAAM,SAAS,GAAG,eAAe,CAAC,SAAS,EAAE,CAAC;oBAC9C,MAAM,QAAQ,GAAG,CAAC,KAAK,GAAG,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;oBAC5G,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAC7D,CAAC,CAAC,CAAC;aACN;iBAAM;gBACH,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;aACvE;YAED,UAAU;YACV,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAEpE,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aACnG;iBAAM;gBACH,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aACvH;YAED,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;oBAC1B,QAAQ,CAAC,cAAc,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;iBACvD;qBAAM;oBACH,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;iBACvD;gBACD,OAAO,CAAC,yBAAyB,CAAC,QAAQ,CAAC,cAAe,EAAE,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;aAC5G;YAED,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aACrG;iBAAM;gBACH,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;aAC1J;YAED,IAAI,SAAS,KAAK,CAAC,EAAE;gBACjB,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE;oBAC7B,QAAQ,CAAC,iBAAiB,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;iBAC3D;qBAAM;oBACH,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;iBAC3D;aACJ;iBAAM;gBACH,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC;aACrC;YAED,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAE3C,OAAO;YACP,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1D,QAAQ,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aAC3D;iBAAM;gBACH,QAAQ,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBACvD,QAAQ,CAAC,aAAa,GAAG,QAAQ,CAAC,oBAAoB,CAAC,SAAS,EAAE,CAAC;gBACnE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC;gBAEvC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;oBAChC,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;iBAC/D;qBAAM;oBACH,QAAQ,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;iBACnD;aACJ;YACD,iBAAiB;YACjB,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAExH,6BAA6B;YAC7B,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACpF,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAC1D,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;oBACxG,IAAI,eAAe,KAAK,IAAI,CAAC,yBAAyB,EAAE;wBACpD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;wBAClD,IAAI,CAAC,kBAAkB,GAAoB,YAAa,CAAC,SAAS,EAAE,CAAC;wBACrE,IAAI,CAAC,yBAAyB,GAAmB,eAAe,CAAC;qBACpE;oBAED,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;oBAC5E,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACvC,CAAC,CAAC,CAAC;aACN;YAED,QAAQ;YACR,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1E,QAAQ,CAAC,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;aACnF;iBAAM;gBACH,QAAQ,CAAC,4BAA4B,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;gBACvE,QAAQ,CAAC,YAAY,GAAG,QAAQ,CAAC,4BAA4B,CAAC,SAAS,EAAE,CAAC;gBAC1E,QAAQ,CAAC,qBAAqB,GAAG,QAAQ,CAAC,YAAY,CAAC;gBAEvD,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE;oBACxC,QAAQ,CAAC,qBAAqB,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;iBAC/E;qBAAM;oBACH,QAAQ,CAAC,qBAAqB,GAAG,QAAQ,CAAC,qBAAqB,CAAC;iBACnE;aACJ;YACD,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAE/E,WAAW;YACX,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC/D,QAAQ,CAAC,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;gBAC/D,QAAQ,CAAC,iBAAiB,GAAG,QAAQ,CAAC,wBAAwB,CAAC,SAAS,EAAE,CAAC;gBAE3E,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;oBACpC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;iBACvE;qBAAM;oBACH,QAAQ,CAAC,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,CAAC;iBAC3D;aACJ;YAED,iBAAiB;YACjB,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzE,QAAQ,CAAC,6BAA6B,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;gBACzE,QAAQ,CAAC,sBAAsB,GAAG,QAAQ,CAAC,6BAA6B,CAAC,SAAS,EAAE,CAAC;gBAErF,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE;oBACzC,QAAQ,CAAC,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;iBACjF;qBAAM;oBACH,QAAQ,CAAC,sBAAsB,GAAG,QAAQ,CAAC,sBAAsB,CAAC;iBACrE;aACJ;YAED,OAAO;YACP,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvD,QAAQ,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBACvD,QAAQ,CAAC,aAAa,GAAG,QAAQ,CAAC,oBAAoB,CAAC,SAAS,EAAE,CAAC;gBAEnE,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;oBAChC,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;iBAC/D;qBAAM;oBACH,QAAQ,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;iBACnD;aACJ;YAED,QAAQ;YACR,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC5D,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBAEjC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAEjE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC9D,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,GAAG,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;aAC3E;iBAAM;gBACH,QAAQ,CAAC,qBAAqB,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBACzD,QAAQ,CAAC,qBAAqB,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC7D,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAEjD,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;oBACjC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;iBAClE;qBAAM;oBACH,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;iBACpD;aACJ;YAED,QAAQ;YACR,IAAI,IAAI,CAAC,wBAAwB,EAAE;gBAC/B,QAAQ,CAAC,yBAAyB,GAAG,IAAI,CAAC,iBAAiB,CAAC;gBAC5D,QAAQ,CAAC,uBAAuB,GAAG,IAAI,CAAC,eAAe,CAAC;gBACxD,QAAQ,CAAC,sBAAsB,GAAG,IAAI,CAAC,cAAc,CAAC;aACzD;YAED,qBAAqB;YACrB,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAE7D,OAAO;YACP,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxB,QAAQ,CAAC,SAAS,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAChD;YAED,4BAA4B;YAC5B,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,IAAI,QAAQ,CAAC,wBAAwB,EAAE;oBACnC,QAAQ,CAAC,wBAAwB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC9F,QAAQ,CAAC,wBAAwB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;iBACjG;qBAAM;oBACH,QAAQ,CAAC,wBAAwB,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC7F,QAAQ,CAAC,wBAAwB,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;iBAChG;aACJ;YAED,oFAAoF;YACpF,QAAQ,CAAC,iCAAiC,EAAE,CAAC;SAChD;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,2BAA2B,CAAC,uBAAuB,GAAG,KAAK,EAAE,gBAAgB,GAAG,KAAK,EAAE,gBAAgB,GAAG,KAAK;QACzH,MAAM,uBAAuB,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE/G,IAAI,uBAAuB,EAAE;YACzB,uBAAuB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,gBAAgB,EAAE;YACnB,uBAAuB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC7C;QAED,IAAI,gBAAgB,EAAE;YAClB,uBAAuB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC7C;QAED,OAAO,uBAAuB,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,yBAAyB,CAAC,uBAAuB,GAAG,KAAK,EAAE,mBAAmB,GAAG,KAAK,EAAE,QAAQ,GAAG,KAAK;QAClH,MAAM,oBAAoB,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,kBAAkB,EAAE,aAAa,CAAC,CAAC;QAEjH,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;QAE3C,IAAI,uBAAuB,EAAE;YACzB,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC/C;QACD,IAAI,mBAAmB,EAAE;YACrB,oBAAoB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;SACzD;QAED,IAAI,QAAQ,EAAE;YACV,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACvC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC1C;QAED,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,OAAsB,EAAE,SAAiB;QACxD,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,iCAAiC,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC9D,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,YAAY,EAAE;gBAC3F,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aAC/B;SACJ;QAED,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;SACxC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;SAC5C;QAED,IAAI,SAAS,KAAK,kBAAkB,CAAC,kBAAkB,EAAE;YACrD,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;SAC7C;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;SACxC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAElC,QAAQ,IAAI,CAAC,aAAa,EAAE;gBACxB,KAAK,SAAS,CAAC,yBAAyB;oBACpC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBACnC,MAAM;gBACV,KAAK,SAAS,CAAC,iCAAiC,CAAC;gBACjD,KAAK,SAAS,CAAC,uCAAuC;oBAClD,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;oBAC3C,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,uCAAuC,EAAE;wBAC1E,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;qBACpD;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,2BAA2B;oBACtC,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;oBAC1C,MAAM;gBACV;oBACI,MAAM;aACb;SACJ;QAED,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACpC,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAC7F,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,oCAAoC,CAAC,QAAQ,EAAE,CAAC,CAAC;SACtE;IACL,CAAC;IAED;;;;;OAKG;IACI,qCAAqC,CAAC,QAAuB,EAAE,UAAyB,EAAE,QAAuB;QACpH,UAAU,CAAC,IAAI,CACX,GAAG,kBAAkB,CAAC,2BAA2B,CAC7C,IAAI,CAAC,wBAAwB,EAC7B,IAAI,CAAC,iBAAiB;YAClB,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,iCAAiC;YAClE,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,uCAAuC,EAC5E,IAAI,CAAC,iBAAiB,CACzB,CACJ,CAAC;QAEF,QAAQ,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,yBAAyB,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEvI,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QAE/C,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACpC,iCAAiC,CAAC,QAAQ,EAAE,IAAI,CAAC,oCAAoC,CAAC,CAAC;YACvF,iCAAiC,CAAC,QAAQ,EAAE,IAAI,CAAC,oCAAoC,CAAC,CAAC;SAC1F;IACL,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,SAAiB;QACjC,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAE5D,IAAI,aAAa,EAAE,MAAM,EAAE;YACvB,OAAO,aAAa,CAAC;SACxB;QAED,MAAM,OAAO,GAAkB,EAAE,CAAC;QAElC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAErC,SAAS;QACT,MAAM,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAE,IAAI,CAAC,OAAkB,CAAC,mBAAmB,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC;QAClJ,IAAI,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC3D,IAAI,CAAC,YAAY,EAAE;YACf,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,GAAG,EAAE,CAAC;SAC/D;QACD,IAAI,WAAW,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,CAAC,WAAW,EAAE;YACd,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,WAAW,CAAC,WAAW,EAAE;gBACzB,WAAW,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;aAC/D;YACD,YAAY,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;SACzC;QAED,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,WAAW,CAAC,OAAO,KAAK,IAAI,EAAE;YAC9B,MAAM,wBAAwB,GAAkB,EAAE,CAAC;YACnD,MAAM,oBAAoB,GAAkB,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAkB,EAAE,CAAC;YAEnC,IAAI,CAAC,qCAAqC,CAAC,oBAAoB,EAAE,wBAAwB,EAAE,QAAQ,CAAC,CAAC;YAErG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;SACvI;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,WAAW,GAAG,KAAK;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QAED,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,EAAE;YAC7B,QAAQ;YACR,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;gBACjB,OAAO;aACV;YAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE;gBACpD,OAAO;aACV;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;SACpD;QAED,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAAC;QAE5H,sDAAsD;QACtD,IAAI,YAAY,CAAC;QAEjB,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,EAAE;YAC3B,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC;YACpC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;YACzB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;SAC5B;aAAM;YACH,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;YAEzB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC1F,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAC1D,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;oBACvG,IAAI,eAAe,KAAK,IAAI,CAAC,wBAAwB,EAAE;wBACnD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;wBAChD,IAAI,CAAC,iBAAiB,GAAoB,YAAa,CAAC,SAAS,EAAE,CAAC;wBACpE,IAAI,CAAC,wBAAwB,GAAmB,eAAe,CAAC;qBACnE;oBAED,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;gBACvE,CAAC,CAAC,CAAC;aACN;YAED,YAAY,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACrD,IAAI,CAAC,eAAe,IAAI,IAAI,GAAG,IAAI,CAAC,kBAAkB,GAAG,YAAY,CAAC;SACzE;QAED,IAAI,IAAI,CAAC,eAAe,GAAG,GAAG,EAAE;YAC5B,YAAY,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC;YAC1C,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC;SACrD;QAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,kBAAkB,CAAC;YAE7C,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzE,IAAI,CAAC,IAAI,EAAE,CAAC;aACf;SACJ;aAAM;YACH,YAAY,GAAG,CAAC,CAAC;SACpB;QACD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAE3B,WAAW;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACd,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACtB,IAAI,IAAI,CAAC,cAAc,EAAE;oBACrB,IAAI,CAAC,cAAc,EAAE,CAAC;iBACzB;gBACD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,EAAE;oBACnC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACxC;aACJ;SACJ;QAED,IAAI,CAAC,WAAW,EAAE;YACd,aAAa;YACb,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACxC,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAC/C,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACzC;YAED,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;aAClF;SACJ;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE;YAClD,IAAI,CAAC,IAAI,EAAE,CAAC;SACf;IACL,CAAC;IAEO,uBAAuB,CAAC,MAAc,EAAE,QAAkB;QAC9D,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACxD;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,iBAAiB,EAAE;YAC1C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAClC;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,CAAC;QAE/B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,6BAA6B,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE;YACpK,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,SAAS,KAAK,kBAAkB,CAAC,qBAAqB,EAAE;YAC7D,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAO,CAAC,OAAO,EAAE,EAAE;gBACrD,OAAO,KAAK,CAAC;aAChB;SACJ;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC,MAAO,CAAC,OAAO,EAAE,EAAE;gBAC5E,OAAO,KAAK,CAAC;aAChB;YACD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,MAAO,CAAC,OAAO,EAAE,EAAE;gBACvE,OAAO,KAAK,CAAC;aAChB;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,OAAO,CAAC,SAAiB;QAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAChD,MAAM,MAAM,GAAG,WAAW,CAAC,MAAO,CAAC;QAEnC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5B,SAAS;QACT,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAEjC,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAO,CAAC,aAAa,EAAE,CAAC;QAC1E,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC1D,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QACrC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,MAAO,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAEnG,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,eAAe,EAAE;YACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;YACpD,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;SAC7J;QAED,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC7D,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAEhH,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,EAAE;YACvC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAa,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;SAC3D;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;gBACrD,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;gBACrC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;aACrC;YACD,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;SAChE;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAE/B,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAEzC,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;aACrD;SACJ;QAED,IAAI,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE;YACnD,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACrD;QAED,IAAI,IAAI,CAAC,kBAAkB,KAAK,SAAS,EAAE;YACvC,IAAI,IAAI,CAAC,MAAM,EAAE,cAAc,EAAE;gBAC7B,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,8BAA8B,EAAE,MAAM,CAAC,CAAC;aACxF;iBAAM;gBACH,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;oBAC1B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;iBACrG;gBAED,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aACtJ;SACJ;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACpB,sBAAsB;gBACtB,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;aAC7H;iBAAM;gBACH,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;aAC7H;SACJ;QAED,aAAa;QACb,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,MAAM,EAAE;YACzC,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SAC9C;QAED,mBAAmB;QACnB,IAAI,IAAI,CAAC,6BAA6B,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,EAAE;YAC9F,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACnD;QAED,aAAa;QACb,QAAQ,SAAS,EAAE;YACf,KAAK,kBAAkB,CAAC,aAAa;gBACjC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBACzC,MAAM;YACV,KAAK,kBAAkB,CAAC,gBAAgB;gBACpC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBAC5C,MAAM;YACV,KAAK,kBAAkB,CAAC,kBAAkB;gBACtC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;gBAC7C,MAAM;YACV,KAAK,kBAAkB,CAAC,kBAAkB;gBACtC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;gBAC9C,MAAM;SACb;QAED,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACvC,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SACjE;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,IAAI,CAAC,MAAM,EAAE,cAAc,EAAE;gBAC7B,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,0BAA0B,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;aAChG;iBAAM;gBACH,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,8BAA8B,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;aACjG;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,MAAM,EAAE,cAAc,EAAE;gBAC7B,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,0BAA0B,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;aACjG;iBAAM;gBACH,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;aAC/F;SACJ;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,MAAM;QACT,QAAQ;QACR,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YAC5C,OAAO,CAAC,CAAC;SACZ;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAc,CAAC;QACnC,IAAI,MAAM,CAAC,QAAQ,EAAE;YACjB,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAEvB,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;aAC9B;SACJ;QAED,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,IAAI,IAAI,CAAC,SAAS,KAAK,kBAAkB,CAAC,qBAAqB,EAAE;YAC7D,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;SACvH;aAAM;YACH,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC/C;QAED,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEnD,OAAO,YAAY,CAAC;IACxB,CAAC;IAED,gBAAgB;IACT,UAAU,CAAC,0BAA0B,GAAG,KAAK,EAAE,qBAAqB,GAAG,KAAK;QAC/E,aAAa;IACjB,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,cAAc,GAAG,IAAI,EAAE,0BAA0B,GAAG,KAAK,EAAE,qBAAqB,GAAG,KAAK;QACnG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC/C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACpD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACjC;QAED,IAAI,IAAI,CAAC,8BAA8B,EAAE;YACrC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YACjE,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC;SAC9C;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC/D,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAClC;QAED,IAAI,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE;YACxC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC/B;QAED,IAAI,cAAc,IAAI,IAAI,CAAC,YAAY,EAAE;YACrC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;YACrC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;SACrC;QAED,IAAI,CAAC,UAAU,CAAC,0BAA0B,EAAE,qBAAqB,CAAC,CAAC;QAEnE,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACvC,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;SACjD;QAED,oBAAoB;QACpB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACxD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACZ,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAChD;YAED,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE,CAAC;SAChD;QAED,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QAEjC,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;CACJ", "sourcesContent": ["/* eslint-disable import/no-internal-modules */\r\nimport type { Immutable, Nullable } from \"../types\";\r\nimport { FactorGradient, ColorGradient, Color3Gradient, GradientHelper } from \"../Misc/gradients\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Vector3, Matrix, Vector4, TmpVectors } from \"../Maths/math.vector\";\r\nimport { Vertex<PERSON>uffer, Buffer } from \"../Buffers/buffer\";\r\n\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { RawTexture } from \"../Materials/Textures/rawTexture\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { IDisposable, Scene } from \"../scene\";\r\n\r\nimport type { IParticleSystem } from \"./IParticleSystem\";\r\nimport { BaseParticleSystem } from \"./baseParticleSystem\";\r\nimport { Particle } from \"./particle\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { IAnimatable } from \"../Animations/animatable.interface\";\r\nimport { DrawWrapper } from \"../Materials/drawWrapper\";\r\n\r\nimport \"../Shaders/particles.fragment\";\r\nimport \"../Shaders/particles.vertex\";\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport { Color4, Color3, TmpColors } from \"../Maths/math.color\";\r\nimport type { ISize } from \"../Maths/math.size\";\r\nimport type { ThinEngine } from \"../Engines/thinEngine\";\r\n\r\nimport \"../Engines/Extensions/engine.alpha\";\r\nimport { addClipPlaneUniforms, prepareStringDefinesForClipPlanes, bindClipPlane } from \"../Materials/clipPlaneMaterialHelper\";\r\n\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { ProceduralTexture } from \"../Materials/Textures/Procedurals/proceduralTexture\";\r\nimport type { Engine } from \"../Engines/engine\";\r\nimport { BindFogParameters, BindLogDepth } from \"../Materials/materialHelper.functions\";\r\nimport { BoxParticleEmitter } from \"./EmitterTypes/boxParticleEmitter\";\r\nimport { Clamp, Lerp, RandomRange } from \"../Maths/math.scalar.functions\";\r\nimport { PrepareSamplersForImageProcessing, PrepareUniformsForImageProcessing } from \"../Materials/imageProcessingConfiguration.functions\";\r\n\r\n/**\r\n * This represents a thin particle system in Babylon.\r\n * Particles are often small sprites used to simulate hard-to-reproduce phenomena like fire, smoke, water, or abstract visual effects like magic glitter and faery dust.\r\n * Particles can take different shapes while emitted like box, sphere, cone or you can write your custom function.\r\n * This thin version contains a limited subset of the total features in order to provide users with a way to get particles but with a smaller footprint\r\n * @example https://doc.babylonjs.com/features/featuresDeepDive/particles/particle_system/particle_system_intro\r\n */\r\nexport class ThinParticleSystem extends BaseParticleSystem implements IDisposable, IAnimatable, IParticleSystem {\r\n    /**\r\n     * This function can be defined to provide custom update for active particles.\r\n     * This function will be called instead of regular update (age, position, color, etc.).\r\n     * Do not forget that this function will be called on every frame so try to keep it simple and fast :)\r\n     */\r\n    public updateFunction: (particles: Particle[]) => void;\r\n\r\n    private _emitterWorldMatrix: Matrix;\r\n    private _emitterInverseWorldMatrix: Matrix = Matrix.Identity();\r\n\r\n    /**\r\n     * This function can be defined to specify initial direction for every new particle.\r\n     * It by default use the emitterType defined function\r\n     */\r\n    public startDirectionFunction: (worldMatrix: Matrix, directionToUpdate: Vector3, particle: Particle, isLocal: boolean) => void;\r\n    /**\r\n     * This function can be defined to specify initial position for every new particle.\r\n     * It by default use the emitterType defined function\r\n     */\r\n    public startPositionFunction: (worldMatrix: Matrix, positionToUpdate: Vector3, particle: Particle, isLocal: boolean) => void;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _inheritedVelocityOffset = new Vector3();\r\n    /**\r\n     * An event triggered when the system is disposed\r\n     */\r\n    public onDisposeObservable = new Observable<IParticleSystem>();\r\n    /**\r\n     * An event triggered when the system is stopped\r\n     */\r\n    public onStoppedObservable = new Observable<IParticleSystem>();\r\n\r\n    private _onDisposeObserver: Nullable<Observer<IParticleSystem>>;\r\n    /**\r\n     * Sets a callback that will be triggered when the system is disposed\r\n     */\r\n    public set onDispose(callback: () => void) {\r\n        if (this._onDisposeObserver) {\r\n            this.onDisposeObservable.remove(this._onDisposeObserver);\r\n        }\r\n        this._onDisposeObserver = this.onDisposeObservable.add(callback);\r\n    }\r\n\r\n    private _particles = new Array<Particle>();\r\n    private _epsilon: number;\r\n    private _capacity: number;\r\n    private _stockParticles = new Array<Particle>();\r\n    private _newPartsExcess = 0;\r\n    private _vertexData: Float32Array;\r\n    private _vertexBuffer: Nullable<Buffer>;\r\n    private _vertexBuffers: { [key: string]: VertexBuffer } = {};\r\n    private _spriteBuffer: Nullable<Buffer>;\r\n    private _indexBuffer: Nullable<DataBuffer>;\r\n    private _linesIndexBuffer: Nullable<DataBuffer>;\r\n    private _linesIndexBufferUseInstancing: Nullable<DataBuffer>;\r\n    private _drawWrappers: DrawWrapper[][]; // first index is render pass id, second index is blend mode\r\n    /** @internal */\r\n    public _customWrappers: { [blendMode: number]: Nullable<DrawWrapper> };\r\n    private _scaledColorStep = new Color4(0, 0, 0, 0);\r\n    private _colorDiff = new Color4(0, 0, 0, 0);\r\n    private _scaledDirection = Vector3.Zero();\r\n    private _scaledGravity = Vector3.Zero();\r\n    private _currentRenderId = -1;\r\n    private _alive: boolean;\r\n    private _useInstancing = false;\r\n    private _vertexArrayObject: Nullable<WebGLVertexArrayObject>;\r\n\r\n    private _started = false;\r\n    private _stopped = false;\r\n    private _actualFrame = 0;\r\n    private _scaledUpdateSpeed: number;\r\n    private _vertexBufferSize: number;\r\n\r\n    /** @internal */\r\n    public _currentEmitRateGradient: Nullable<FactorGradient>;\r\n    /** @internal */\r\n    public _currentEmitRate1 = 0;\r\n    /** @internal */\r\n    public _currentEmitRate2 = 0;\r\n\r\n    /** @internal */\r\n    public _currentStartSizeGradient: Nullable<FactorGradient>;\r\n    /** @internal */\r\n    public _currentStartSize1 = 0;\r\n    /** @internal */\r\n    public _currentStartSize2 = 0;\r\n\r\n    /** Indicates that the update of particles is done in the animate function */\r\n    public readonly updateInAnimate = true;\r\n\r\n    private readonly _rawTextureWidth = 256;\r\n    private _rampGradientsTexture: Nullable<RawTexture>;\r\n    private _useRampGradients = false;\r\n\r\n    /** Gets or sets a matrix to use to compute projection */\r\n    public defaultProjectionMatrix: Matrix;\r\n\r\n    /** Gets or sets a matrix to use to compute view */\r\n    public defaultViewMatrix: Matrix;\r\n\r\n    /** Gets or sets a boolean indicating that ramp gradients must be used\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/particles/particle_system/particle_system_intro#ramp-gradients\r\n     */\r\n    public get useRampGradients(): boolean {\r\n        return this._useRampGradients;\r\n    }\r\n\r\n    public set useRampGradients(value: boolean) {\r\n        if (this._useRampGradients === value) {\r\n            return;\r\n        }\r\n\r\n        this._useRampGradients = value;\r\n\r\n        this._resetEffect();\r\n    }\r\n\r\n    /**\r\n     * Specifies if the particles are updated in emitter local space or world space\r\n     */\r\n    public isLocal = false;\r\n\r\n    /** Indicates that the particle system is CPU based */\r\n    public readonly isGPU = false;\r\n\r\n    /**\r\n     * Gets the current list of active particles\r\n     */\r\n    public get particles(): Particle[] {\r\n        return this._particles;\r\n    }\r\n\r\n    /**\r\n     * Gets the number of particles active at the same time.\r\n     * @returns The number of active particles.\r\n     */\r\n    public getActiveCount() {\r\n        return this._particles.length;\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"ParticleSystem\"\r\n     * @returns a string containing the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"ParticleSystem\";\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the system is stopping\r\n     * @returns true if the system is currently stopping\r\n     */\r\n    public isStopping() {\r\n        return this._stopped && this.isAlive();\r\n    }\r\n\r\n    /**\r\n     * Gets the custom effect used to render the particles\r\n     * @param blendMode Blend mode for which the effect should be retrieved\r\n     * @returns The effect\r\n     */\r\n    public getCustomEffect(blendMode: number = 0): Nullable<Effect> {\r\n        return this._customWrappers[blendMode]?.effect ?? this._customWrappers[0]!.effect;\r\n    }\r\n\r\n    private _getCustomDrawWrapper(blendMode: number = 0): Nullable<DrawWrapper> {\r\n        return this._customWrappers[blendMode] ?? this._customWrappers[0];\r\n    }\r\n\r\n    /**\r\n     * Sets the custom effect used to render the particles\r\n     * @param effect The effect to set\r\n     * @param blendMode Blend mode for which the effect should be set\r\n     */\r\n    public setCustomEffect(effect: Nullable<Effect>, blendMode: number = 0) {\r\n        this._customWrappers[blendMode] = new DrawWrapper(this._engine);\r\n        this._customWrappers[blendMode]!.effect = effect;\r\n        if (this._customWrappers[blendMode]!.drawContext) {\r\n            this._customWrappers[blendMode]!.drawContext!.useInstancing = this._useInstancing;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    private _onBeforeDrawParticlesObservable: Nullable<Observable<Nullable<Effect>>> = null;\r\n\r\n    /**\r\n     * Observable that will be called just before the particles are drawn\r\n     */\r\n    public get onBeforeDrawParticlesObservable(): Observable<Nullable<Effect>> {\r\n        if (!this._onBeforeDrawParticlesObservable) {\r\n            this._onBeforeDrawParticlesObservable = new Observable<Nullable<Effect>>();\r\n        }\r\n\r\n        return this._onBeforeDrawParticlesObservable;\r\n    }\r\n\r\n    /**\r\n     * Gets the name of the particle vertex shader\r\n     */\r\n    public get vertexShaderName(): string {\r\n        return \"particles\";\r\n    }\r\n\r\n    /**\r\n     * Gets the vertex buffers used by the particle system\r\n     */\r\n    public get vertexBuffers(): Immutable<{ [key: string]: VertexBuffer }> {\r\n        return this._vertexBuffers;\r\n    }\r\n\r\n    /**\r\n     * Gets the index buffer used by the particle system (or null if no index buffer is used (if _useInstancing=true))\r\n     */\r\n    public get indexBuffer(): Nullable<DataBuffer> {\r\n        return this._indexBuffer;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a particle system.\r\n     * Particles are often small sprites used to simulate hard-to-reproduce phenomena like fire, smoke, water, or abstract visual effects like magic glitter and faery dust.\r\n     * @param name The name of the particle system\r\n     * @param capacity The max number of particles alive at the same time\r\n     * @param sceneOrEngine The scene the particle system belongs to or the engine to use if no scene\r\n     * @param customEffect a custom effect used to change the way particles are rendered by default\r\n     * @param isAnimationSheetEnabled Must be true if using a spritesheet to animate the particles texture\r\n     * @param epsilon Offset used to render the particles\r\n     */\r\n    constructor(\r\n        name: string,\r\n        capacity: number,\r\n        sceneOrEngine: Scene | ThinEngine,\r\n        customEffect: Nullable<Effect> = null,\r\n        isAnimationSheetEnabled: boolean = false,\r\n        epsilon: number = 0.01\r\n    ) {\r\n        super(name);\r\n\r\n        this._capacity = capacity;\r\n\r\n        this._epsilon = epsilon;\r\n        this._isAnimationSheetEnabled = isAnimationSheetEnabled;\r\n\r\n        if (!sceneOrEngine || sceneOrEngine.getClassName() === \"Scene\") {\r\n            this._scene = (sceneOrEngine as Scene) || EngineStore.LastCreatedScene;\r\n            this._engine = this._scene.getEngine();\r\n            this.uniqueId = this._scene.getUniqueId();\r\n            this._scene.particleSystems.push(this);\r\n        } else {\r\n            this._engine = sceneOrEngine as ThinEngine;\r\n            this.defaultProjectionMatrix = Matrix.PerspectiveFovLH(0.8, 1, 0.1, 100, this._engine.isNDCHalfZRange);\r\n        }\r\n\r\n        if (this._engine.getCaps().vertexArrayObject) {\r\n            this._vertexArrayObject = null;\r\n        }\r\n\r\n        // Setup the default processing configuration to the scene.\r\n        this._attachImageProcessingConfiguration(null);\r\n\r\n        // eslint-disable-next-line @typescript-eslint/naming-convention\r\n        this._customWrappers = { 0: new DrawWrapper(this._engine) };\r\n        this._customWrappers[0]!.effect = customEffect;\r\n\r\n        this._drawWrappers = [];\r\n        this._useInstancing = this._engine.getCaps().instancedArrays;\r\n\r\n        this._createIndexBuffer();\r\n        this._createVertexBuffers();\r\n\r\n        // Default emitter type\r\n        this.particleEmitterType = new BoxParticleEmitter();\r\n        let noiseTextureData: Nullable<Uint8Array> = null;\r\n\r\n        // Update\r\n        this.updateFunction = (particles: Particle[]): void => {\r\n            let noiseTextureSize: Nullable<ISize> = null;\r\n\r\n            if (this.noiseTexture) {\r\n                // We need to get texture data back to CPU\r\n                noiseTextureSize = this.noiseTexture.getSize();\r\n                this.noiseTexture.getContent()?.then((data) => {\r\n                    noiseTextureData = data as Uint8Array;\r\n                });\r\n            }\r\n\r\n            const sameParticleArray = particles === this._particles;\r\n\r\n            for (let index = 0; index < particles.length; index++) {\r\n                const particle = particles[index];\r\n\r\n                let scaledUpdateSpeed = this._scaledUpdateSpeed;\r\n                const previousAge = particle.age;\r\n                particle.age += scaledUpdateSpeed;\r\n\r\n                // Evaluate step to death\r\n                if (particle.age > particle.lifeTime) {\r\n                    const diff = particle.age - previousAge;\r\n                    const oldDiff = particle.lifeTime - previousAge;\r\n\r\n                    scaledUpdateSpeed = (oldDiff * scaledUpdateSpeed) / diff;\r\n\r\n                    particle.age = particle.lifeTime;\r\n                }\r\n\r\n                const ratio = particle.age / particle.lifeTime;\r\n\r\n                // Color\r\n                if (this._colorGradients && this._colorGradients.length > 0) {\r\n                    GradientHelper.GetCurrentGradient(ratio, this._colorGradients, (currentGradient, nextGradient, scale) => {\r\n                        if (currentGradient !== particle._currentColorGradient) {\r\n                            particle._currentColor1.copyFrom(particle._currentColor2);\r\n                            (<ColorGradient>nextGradient).getColorToRef(particle._currentColor2);\r\n                            particle._currentColorGradient = <ColorGradient>currentGradient;\r\n                        }\r\n                        Color4.LerpToRef(particle._currentColor1, particle._currentColor2, scale, particle.color);\r\n                    });\r\n                } else {\r\n                    particle.colorStep.scaleToRef(scaledUpdateSpeed, this._scaledColorStep);\r\n                    particle.color.addInPlace(this._scaledColorStep);\r\n\r\n                    if (particle.color.a < 0) {\r\n                        particle.color.a = 0;\r\n                    }\r\n                }\r\n\r\n                // Angular speed\r\n                if (this._angularSpeedGradients && this._angularSpeedGradients.length > 0) {\r\n                    GradientHelper.GetCurrentGradient(ratio, this._angularSpeedGradients, (currentGradient, nextGradient, scale) => {\r\n                        if (currentGradient !== particle._currentAngularSpeedGradient) {\r\n                            particle._currentAngularSpeed1 = particle._currentAngularSpeed2;\r\n                            particle._currentAngularSpeed2 = (<FactorGradient>nextGradient).getFactor();\r\n                            particle._currentAngularSpeedGradient = <FactorGradient>currentGradient;\r\n                        }\r\n                        particle.angularSpeed = Lerp(particle._currentAngularSpeed1, particle._currentAngularSpeed2, scale);\r\n                    });\r\n                }\r\n                particle.angle += particle.angularSpeed * scaledUpdateSpeed;\r\n\r\n                // Direction\r\n                let directionScale = scaledUpdateSpeed;\r\n\r\n                /// Velocity\r\n                if (this._velocityGradients && this._velocityGradients.length > 0) {\r\n                    GradientHelper.GetCurrentGradient(ratio, this._velocityGradients, (currentGradient, nextGradient, scale) => {\r\n                        if (currentGradient !== particle._currentVelocityGradient) {\r\n                            particle._currentVelocity1 = particle._currentVelocity2;\r\n                            particle._currentVelocity2 = (<FactorGradient>nextGradient).getFactor();\r\n                            particle._currentVelocityGradient = <FactorGradient>currentGradient;\r\n                        }\r\n                        directionScale *= Lerp(particle._currentVelocity1, particle._currentVelocity2, scale);\r\n                    });\r\n                }\r\n\r\n                particle.direction.scaleToRef(directionScale, this._scaledDirection);\r\n\r\n                /// Limit velocity\r\n                if (this._limitVelocityGradients && this._limitVelocityGradients.length > 0) {\r\n                    GradientHelper.GetCurrentGradient(ratio, this._limitVelocityGradients, (currentGradient, nextGradient, scale) => {\r\n                        if (currentGradient !== particle._currentLimitVelocityGradient) {\r\n                            particle._currentLimitVelocity1 = particle._currentLimitVelocity2;\r\n                            particle._currentLimitVelocity2 = (<FactorGradient>nextGradient).getFactor();\r\n                            particle._currentLimitVelocityGradient = <FactorGradient>currentGradient;\r\n                        }\r\n\r\n                        const limitVelocity = Lerp(particle._currentLimitVelocity1, particle._currentLimitVelocity2, scale);\r\n                        const currentVelocity = particle.direction.length();\r\n\r\n                        if (currentVelocity > limitVelocity) {\r\n                            particle.direction.scaleInPlace(this.limitVelocityDamping);\r\n                        }\r\n                    });\r\n                }\r\n\r\n                /// Drag\r\n                if (this._dragGradients && this._dragGradients.length > 0) {\r\n                    GradientHelper.GetCurrentGradient(ratio, this._dragGradients, (currentGradient, nextGradient, scale) => {\r\n                        if (currentGradient !== particle._currentDragGradient) {\r\n                            particle._currentDrag1 = particle._currentDrag2;\r\n                            particle._currentDrag2 = (<FactorGradient>nextGradient).getFactor();\r\n                            particle._currentDragGradient = <FactorGradient>currentGradient;\r\n                        }\r\n\r\n                        const drag = Lerp(particle._currentDrag1, particle._currentDrag2, scale);\r\n\r\n                        this._scaledDirection.scaleInPlace(1.0 - drag);\r\n                    });\r\n                }\r\n\r\n                if (this.isLocal && particle._localPosition) {\r\n                    particle._localPosition!.addInPlace(this._scaledDirection);\r\n                    Vector3.TransformCoordinatesToRef(particle._localPosition!, this._emitterWorldMatrix, particle.position);\r\n                } else {\r\n                    particle.position.addInPlace(this._scaledDirection);\r\n                }\r\n\r\n                // Noise\r\n                if (noiseTextureData && noiseTextureSize && particle._randomNoiseCoordinates1) {\r\n                    const fetchedColorR = this._fetchR(\r\n                        particle._randomNoiseCoordinates1.x,\r\n                        particle._randomNoiseCoordinates1.y,\r\n                        noiseTextureSize.width,\r\n                        noiseTextureSize.height,\r\n                        noiseTextureData\r\n                    );\r\n                    const fetchedColorG = this._fetchR(\r\n                        particle._randomNoiseCoordinates1.z,\r\n                        particle._randomNoiseCoordinates2.x,\r\n                        noiseTextureSize.width,\r\n                        noiseTextureSize.height,\r\n                        noiseTextureData\r\n                    );\r\n                    const fetchedColorB = this._fetchR(\r\n                        particle._randomNoiseCoordinates2.y,\r\n                        particle._randomNoiseCoordinates2.z,\r\n                        noiseTextureSize.width,\r\n                        noiseTextureSize.height,\r\n                        noiseTextureData\r\n                    );\r\n\r\n                    const force = TmpVectors.Vector3[0];\r\n                    const scaledForce = TmpVectors.Vector3[1];\r\n\r\n                    force.copyFromFloats(\r\n                        (2 * fetchedColorR - 1) * this.noiseStrength.x,\r\n                        (2 * fetchedColorG - 1) * this.noiseStrength.y,\r\n                        (2 * fetchedColorB - 1) * this.noiseStrength.z\r\n                    );\r\n\r\n                    force.scaleToRef(scaledUpdateSpeed, scaledForce);\r\n                    particle.direction.addInPlace(scaledForce);\r\n                }\r\n\r\n                // Gravity\r\n                this.gravity.scaleToRef(scaledUpdateSpeed, this._scaledGravity);\r\n                particle.direction.addInPlace(this._scaledGravity);\r\n\r\n                // Size\r\n                if (this._sizeGradients && this._sizeGradients.length > 0) {\r\n                    GradientHelper.GetCurrentGradient(ratio, this._sizeGradients, (currentGradient, nextGradient, scale) => {\r\n                        if (currentGradient !== particle._currentSizeGradient) {\r\n                            particle._currentSize1 = particle._currentSize2;\r\n                            particle._currentSize2 = (<FactorGradient>nextGradient).getFactor();\r\n                            particle._currentSizeGradient = <FactorGradient>currentGradient;\r\n                        }\r\n                        particle.size = Lerp(particle._currentSize1, particle._currentSize2, scale);\r\n                    });\r\n                }\r\n\r\n                // Remap data\r\n                if (this._useRampGradients) {\r\n                    if (this._colorRemapGradients && this._colorRemapGradients.length > 0) {\r\n                        GradientHelper.GetCurrentGradient(ratio, this._colorRemapGradients, (currentGradient, nextGradient, scale) => {\r\n                            const min = Lerp((<FactorGradient>currentGradient).factor1, (<FactorGradient>nextGradient).factor1, scale);\r\n                            const max = Lerp((<FactorGradient>currentGradient).factor2!, (<FactorGradient>nextGradient).factor2!, scale);\r\n\r\n                            particle.remapData.x = min;\r\n                            particle.remapData.y = max - min;\r\n                        });\r\n                    }\r\n\r\n                    if (this._alphaRemapGradients && this._alphaRemapGradients.length > 0) {\r\n                        GradientHelper.GetCurrentGradient(ratio, this._alphaRemapGradients, (currentGradient, nextGradient, scale) => {\r\n                            const min = Lerp((<FactorGradient>currentGradient).factor1, (<FactorGradient>nextGradient).factor1, scale);\r\n                            const max = Lerp((<FactorGradient>currentGradient).factor2!, (<FactorGradient>nextGradient).factor2!, scale);\r\n\r\n                            particle.remapData.z = min;\r\n                            particle.remapData.w = max - min;\r\n                        });\r\n                    }\r\n                }\r\n\r\n                if (this._isAnimationSheetEnabled) {\r\n                    particle.updateCellIndex();\r\n                }\r\n\r\n                // Update the position of the attached sub-emitters to match their attached particle\r\n                particle._inheritParticleInfoToSubEmitters();\r\n\r\n                if (particle.age >= particle.lifeTime) {\r\n                    // Recycle by swapping with last particle\r\n                    this._emitFromParticle(particle);\r\n                    if (particle._attachedSubEmitters) {\r\n                        particle._attachedSubEmitters.forEach((subEmitter) => {\r\n                            subEmitter.particleSystem.disposeOnStop = true;\r\n                            subEmitter.particleSystem.stop();\r\n                        });\r\n                        particle._attachedSubEmitters = null;\r\n                    }\r\n                    this.recycleParticle(particle);\r\n                    if (sameParticleArray) {\r\n                        index--;\r\n                    }\r\n                    continue;\r\n                }\r\n            }\r\n        };\r\n    }\r\n\r\n    /** @internal */\r\n    public _emitFromParticle: (particle: Particle) => void = (particle) => {\r\n        // Do nothing\r\n    };\r\n\r\n    serialize(serializeTexture: boolean) {\r\n        throw new Error(\"Method not implemented.\");\r\n    }\r\n\r\n    /**\r\n     * Clones the particle system.\r\n     * @param name The name of the cloned object\r\n     * @param newEmitter The new emitter to use\r\n     * @param cloneTexture Also clone the textures if true\r\n     */\r\n    public clone(name: string, newEmitter: any, cloneTexture = false): ThinParticleSystem {\r\n        throw new Error(\"Method not implemented.\");\r\n    }\r\n\r\n    private _addFactorGradient(factorGradients: FactorGradient[], gradient: number, factor: number, factor2?: number) {\r\n        const newGradient = new FactorGradient(gradient, factor, factor2);\r\n        factorGradients.push(newGradient);\r\n\r\n        factorGradients.sort((a, b) => {\r\n            if (a.gradient < b.gradient) {\r\n                return -1;\r\n            } else if (a.gradient > b.gradient) {\r\n                return 1;\r\n            }\r\n\r\n            return 0;\r\n        });\r\n    }\r\n\r\n    private _removeFactorGradient(factorGradients: Nullable<FactorGradient[]>, gradient: number) {\r\n        if (!factorGradients) {\r\n            return;\r\n        }\r\n\r\n        let index = 0;\r\n        for (const factorGradient of factorGradients) {\r\n            if (factorGradient.gradient === gradient) {\r\n                factorGradients.splice(index, 1);\r\n                break;\r\n            }\r\n            index++;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds a new life time gradient\r\n     * @param gradient defines the gradient to use (between 0 and 1)\r\n     * @param factor defines the life time factor to affect to the specified gradient\r\n     * @param factor2 defines an additional factor used to define a range ([factor, factor2]) with main value to pick the final value from\r\n     * @returns the current particle system\r\n     */\r\n    public addLifeTimeGradient(gradient: number, factor: number, factor2?: number): IParticleSystem {\r\n        if (!this._lifeTimeGradients) {\r\n            this._lifeTimeGradients = [];\r\n        }\r\n\r\n        this._addFactorGradient(this._lifeTimeGradients, gradient, factor, factor2);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a specific life time gradient\r\n     * @param gradient defines the gradient to remove\r\n     * @returns the current particle system\r\n     */\r\n    public removeLifeTimeGradient(gradient: number): IParticleSystem {\r\n        this._removeFactorGradient(this._lifeTimeGradients, gradient);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds a new size gradient\r\n     * @param gradient defines the gradient to use (between 0 and 1)\r\n     * @param factor defines the size factor to affect to the specified gradient\r\n     * @param factor2 defines an additional factor used to define a range ([factor, factor2]) with main value to pick the final value from\r\n     * @returns the current particle system\r\n     */\r\n    public addSizeGradient(gradient: number, factor: number, factor2?: number): IParticleSystem {\r\n        if (!this._sizeGradients) {\r\n            this._sizeGradients = [];\r\n        }\r\n\r\n        this._addFactorGradient(this._sizeGradients, gradient, factor, factor2);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a specific size gradient\r\n     * @param gradient defines the gradient to remove\r\n     * @returns the current particle system\r\n     */\r\n    public removeSizeGradient(gradient: number): IParticleSystem {\r\n        this._removeFactorGradient(this._sizeGradients, gradient);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds a new color remap gradient\r\n     * @param gradient defines the gradient to use (between 0 and 1)\r\n     * @param min defines the color remap minimal range\r\n     * @param max defines the color remap maximal range\r\n     * @returns the current particle system\r\n     */\r\n    public addColorRemapGradient(gradient: number, min: number, max: number): IParticleSystem {\r\n        if (!this._colorRemapGradients) {\r\n            this._colorRemapGradients = [];\r\n        }\r\n\r\n        this._addFactorGradient(this._colorRemapGradients, gradient, min, max);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a specific color remap gradient\r\n     * @param gradient defines the gradient to remove\r\n     * @returns the current particle system\r\n     */\r\n    public removeColorRemapGradient(gradient: number): IParticleSystem {\r\n        this._removeFactorGradient(this._colorRemapGradients, gradient);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds a new alpha remap gradient\r\n     * @param gradient defines the gradient to use (between 0 and 1)\r\n     * @param min defines the alpha remap minimal range\r\n     * @param max defines the alpha remap maximal range\r\n     * @returns the current particle system\r\n     */\r\n    public addAlphaRemapGradient(gradient: number, min: number, max: number): IParticleSystem {\r\n        if (!this._alphaRemapGradients) {\r\n            this._alphaRemapGradients = [];\r\n        }\r\n\r\n        this._addFactorGradient(this._alphaRemapGradients, gradient, min, max);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a specific alpha remap gradient\r\n     * @param gradient defines the gradient to remove\r\n     * @returns the current particle system\r\n     */\r\n    public removeAlphaRemapGradient(gradient: number): IParticleSystem {\r\n        this._removeFactorGradient(this._alphaRemapGradients, gradient);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds a new angular speed gradient\r\n     * @param gradient defines the gradient to use (between 0 and 1)\r\n     * @param factor defines the angular speed  to affect to the specified gradient\r\n     * @param factor2 defines an additional factor used to define a range ([factor, factor2]) with main value to pick the final value from\r\n     * @returns the current particle system\r\n     */\r\n    public addAngularSpeedGradient(gradient: number, factor: number, factor2?: number): IParticleSystem {\r\n        if (!this._angularSpeedGradients) {\r\n            this._angularSpeedGradients = [];\r\n        }\r\n\r\n        this._addFactorGradient(this._angularSpeedGradients, gradient, factor, factor2);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a specific angular speed gradient\r\n     * @param gradient defines the gradient to remove\r\n     * @returns the current particle system\r\n     */\r\n    public removeAngularSpeedGradient(gradient: number): IParticleSystem {\r\n        this._removeFactorGradient(this._angularSpeedGradients, gradient);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds a new velocity gradient\r\n     * @param gradient defines the gradient to use (between 0 and 1)\r\n     * @param factor defines the velocity to affect to the specified gradient\r\n     * @param factor2 defines an additional factor used to define a range ([factor, factor2]) with main value to pick the final value from\r\n     * @returns the current particle system\r\n     */\r\n    public addVelocityGradient(gradient: number, factor: number, factor2?: number): IParticleSystem {\r\n        if (!this._velocityGradients) {\r\n            this._velocityGradients = [];\r\n        }\r\n\r\n        this._addFactorGradient(this._velocityGradients, gradient, factor, factor2);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a specific velocity gradient\r\n     * @param gradient defines the gradient to remove\r\n     * @returns the current particle system\r\n     */\r\n    public removeVelocityGradient(gradient: number): IParticleSystem {\r\n        this._removeFactorGradient(this._velocityGradients, gradient);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds a new limit velocity gradient\r\n     * @param gradient defines the gradient to use (between 0 and 1)\r\n     * @param factor defines the limit velocity value to affect to the specified gradient\r\n     * @param factor2 defines an additional factor used to define a range ([factor, factor2]) with main value to pick the final value from\r\n     * @returns the current particle system\r\n     */\r\n    public addLimitVelocityGradient(gradient: number, factor: number, factor2?: number): IParticleSystem {\r\n        if (!this._limitVelocityGradients) {\r\n            this._limitVelocityGradients = [];\r\n        }\r\n\r\n        this._addFactorGradient(this._limitVelocityGradients, gradient, factor, factor2);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a specific limit velocity gradient\r\n     * @param gradient defines the gradient to remove\r\n     * @returns the current particle system\r\n     */\r\n    public removeLimitVelocityGradient(gradient: number): IParticleSystem {\r\n        this._removeFactorGradient(this._limitVelocityGradients, gradient);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds a new drag gradient\r\n     * @param gradient defines the gradient to use (between 0 and 1)\r\n     * @param factor defines the drag value to affect to the specified gradient\r\n     * @param factor2 defines an additional factor used to define a range ([factor, factor2]) with main value to pick the final value from\r\n     * @returns the current particle system\r\n     */\r\n    public addDragGradient(gradient: number, factor: number, factor2?: number): IParticleSystem {\r\n        if (!this._dragGradients) {\r\n            this._dragGradients = [];\r\n        }\r\n\r\n        this._addFactorGradient(this._dragGradients, gradient, factor, factor2);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a specific drag gradient\r\n     * @param gradient defines the gradient to remove\r\n     * @returns the current particle system\r\n     */\r\n    public removeDragGradient(gradient: number): IParticleSystem {\r\n        this._removeFactorGradient(this._dragGradients, gradient);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds a new emit rate gradient (please note that this will only work if you set the targetStopDuration property)\r\n     * @param gradient defines the gradient to use (between 0 and 1)\r\n     * @param factor defines the emit rate value to affect to the specified gradient\r\n     * @param factor2 defines an additional factor used to define a range ([factor, factor2]) with main value to pick the final value from\r\n     * @returns the current particle system\r\n     */\r\n    public addEmitRateGradient(gradient: number, factor: number, factor2?: number): IParticleSystem {\r\n        if (!this._emitRateGradients) {\r\n            this._emitRateGradients = [];\r\n        }\r\n\r\n        this._addFactorGradient(this._emitRateGradients, gradient, factor, factor2);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a specific emit rate gradient\r\n     * @param gradient defines the gradient to remove\r\n     * @returns the current particle system\r\n     */\r\n    public removeEmitRateGradient(gradient: number): IParticleSystem {\r\n        this._removeFactorGradient(this._emitRateGradients, gradient);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds a new start size gradient (please note that this will only work if you set the targetStopDuration property)\r\n     * @param gradient defines the gradient to use (between 0 and 1)\r\n     * @param factor defines the start size value to affect to the specified gradient\r\n     * @param factor2 defines an additional factor used to define a range ([factor, factor2]) with main value to pick the final value from\r\n     * @returns the current particle system\r\n     */\r\n    public addStartSizeGradient(gradient: number, factor: number, factor2?: number): IParticleSystem {\r\n        if (!this._startSizeGradients) {\r\n            this._startSizeGradients = [];\r\n        }\r\n\r\n        this._addFactorGradient(this._startSizeGradients, gradient, factor, factor2);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a specific start size gradient\r\n     * @param gradient defines the gradient to remove\r\n     * @returns the current particle system\r\n     */\r\n    public removeStartSizeGradient(gradient: number): IParticleSystem {\r\n        this._removeFactorGradient(this._startSizeGradients, gradient);\r\n\r\n        return this;\r\n    }\r\n\r\n    private _createRampGradientTexture() {\r\n        if (!this._rampGradients || !this._rampGradients.length || this._rampGradientsTexture || !this._scene) {\r\n            return;\r\n        }\r\n\r\n        const data = new Uint8Array(this._rawTextureWidth * 4);\r\n        const tmpColor = TmpColors.Color3[0];\r\n\r\n        for (let x = 0; x < this._rawTextureWidth; x++) {\r\n            const ratio = x / this._rawTextureWidth;\r\n\r\n            GradientHelper.GetCurrentGradient(ratio, this._rampGradients, (currentGradient, nextGradient, scale) => {\r\n                Color3.LerpToRef((<Color3Gradient>currentGradient).color, (<Color3Gradient>nextGradient).color, scale, tmpColor);\r\n                data[x * 4] = tmpColor.r * 255;\r\n                data[x * 4 + 1] = tmpColor.g * 255;\r\n                data[x * 4 + 2] = tmpColor.b * 255;\r\n                data[x * 4 + 3] = 255;\r\n            });\r\n        }\r\n\r\n        this._rampGradientsTexture = RawTexture.CreateRGBATexture(data, this._rawTextureWidth, 1, this._scene, false, false, Constants.TEXTURE_NEAREST_SAMPLINGMODE);\r\n    }\r\n\r\n    /**\r\n     * Gets the current list of ramp gradients.\r\n     * You must use addRampGradient and removeRampGradient to update this list\r\n     * @returns the list of ramp gradients\r\n     */\r\n    public getRampGradients(): Nullable<Array<Color3Gradient>> {\r\n        return this._rampGradients;\r\n    }\r\n\r\n    /** Force the system to rebuild all gradients that need to be resync */\r\n    public forceRefreshGradients() {\r\n        this._syncRampGradientTexture();\r\n    }\r\n\r\n    private _syncRampGradientTexture() {\r\n        if (!this._rampGradients) {\r\n            return;\r\n        }\r\n\r\n        this._rampGradients.sort((a, b) => {\r\n            if (a.gradient < b.gradient) {\r\n                return -1;\r\n            } else if (a.gradient > b.gradient) {\r\n                return 1;\r\n            }\r\n\r\n            return 0;\r\n        });\r\n\r\n        if (this._rampGradientsTexture) {\r\n            this._rampGradientsTexture.dispose();\r\n            this._rampGradientsTexture = null;\r\n        }\r\n\r\n        this._createRampGradientTexture();\r\n    }\r\n\r\n    /**\r\n     * Adds a new ramp gradient used to remap particle colors\r\n     * @param gradient defines the gradient to use (between 0 and 1)\r\n     * @param color defines the color to affect to the specified gradient\r\n     * @returns the current particle system\r\n     */\r\n    public addRampGradient(gradient: number, color: Color3): ThinParticleSystem {\r\n        if (!this._rampGradients) {\r\n            this._rampGradients = [];\r\n        }\r\n\r\n        const rampGradient = new Color3Gradient(gradient, color);\r\n        this._rampGradients.push(rampGradient);\r\n\r\n        this._syncRampGradientTexture();\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a specific ramp gradient\r\n     * @param gradient defines the gradient to remove\r\n     * @returns the current particle system\r\n     */\r\n    public removeRampGradient(gradient: number): ThinParticleSystem {\r\n        this._removeGradientAndTexture(gradient, this._rampGradients, this._rampGradientsTexture);\r\n        this._rampGradientsTexture = null;\r\n\r\n        if (this._rampGradients && this._rampGradients.length > 0) {\r\n            this._createRampGradientTexture();\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds a new color gradient\r\n     * @param gradient defines the gradient to use (between 0 and 1)\r\n     * @param color1 defines the color to affect to the specified gradient\r\n     * @param color2 defines an additional color used to define a range ([color, color2]) with main color to pick the final color from\r\n     * @returns this particle system\r\n     */\r\n    public addColorGradient(gradient: number, color1: Color4, color2?: Color4): IParticleSystem {\r\n        if (!this._colorGradients) {\r\n            this._colorGradients = [];\r\n        }\r\n\r\n        const colorGradient = new ColorGradient(gradient, color1, color2);\r\n        this._colorGradients.push(colorGradient);\r\n\r\n        this._colorGradients.sort((a, b) => {\r\n            if (a.gradient < b.gradient) {\r\n                return -1;\r\n            } else if (a.gradient > b.gradient) {\r\n                return 1;\r\n            }\r\n\r\n            return 0;\r\n        });\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Remove a specific color gradient\r\n     * @param gradient defines the gradient to remove\r\n     * @returns this particle system\r\n     */\r\n    public removeColorGradient(gradient: number): IParticleSystem {\r\n        if (!this._colorGradients) {\r\n            return this;\r\n        }\r\n\r\n        let index = 0;\r\n        for (const colorGradient of this._colorGradients) {\r\n            if (colorGradient.gradient === gradient) {\r\n                this._colorGradients.splice(index, 1);\r\n                break;\r\n            }\r\n            index++;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Resets the draw wrappers cache\r\n     */\r\n    public resetDrawCache(): void {\r\n        for (const drawWrappers of this._drawWrappers) {\r\n            if (drawWrappers) {\r\n                for (const drawWrapper of drawWrappers) {\r\n                    drawWrapper?.dispose();\r\n                }\r\n            }\r\n        }\r\n\r\n        this._drawWrappers = [];\r\n    }\r\n\r\n    private _fetchR(u: number, v: number, width: number, height: number, pixels: Uint8Array): number {\r\n        u = Math.abs(u) * 0.5 + 0.5;\r\n        v = Math.abs(v) * 0.5 + 0.5;\r\n\r\n        const wrappedU = (u * width) % width | 0;\r\n        const wrappedV = (v * height) % height | 0;\r\n\r\n        const position = (wrappedU + wrappedV * width) * 4;\r\n        return pixels[position] / 255;\r\n    }\r\n\r\n    protected _reset() {\r\n        this._resetEffect();\r\n    }\r\n\r\n    private _resetEffect() {\r\n        if (this._vertexBuffer) {\r\n            this._vertexBuffer.dispose();\r\n            this._vertexBuffer = null;\r\n        }\r\n\r\n        if (this._spriteBuffer) {\r\n            this._spriteBuffer.dispose();\r\n            this._spriteBuffer = null;\r\n        }\r\n\r\n        if (this._vertexArrayObject) {\r\n            this._engine.releaseVertexArrayObject(this._vertexArrayObject);\r\n            this._vertexArrayObject = null;\r\n        }\r\n\r\n        this._createVertexBuffers();\r\n    }\r\n\r\n    private _createVertexBuffers() {\r\n        this._vertexBufferSize = this._useInstancing ? 10 : 12;\r\n        if (this._isAnimationSheetEnabled) {\r\n            this._vertexBufferSize += 1;\r\n        }\r\n\r\n        if (\r\n            !this._isBillboardBased ||\r\n            this.billboardMode === Constants.PARTICLES_BILLBOARDMODE_STRETCHED ||\r\n            this.billboardMode === Constants.PARTICLES_BILLBOARDMODE_STRETCHED_LOCAL\r\n        ) {\r\n            this._vertexBufferSize += 3;\r\n        }\r\n\r\n        if (this._useRampGradients) {\r\n            this._vertexBufferSize += 4;\r\n        }\r\n\r\n        const engine = this._engine;\r\n        const vertexSize = this._vertexBufferSize * (this._useInstancing ? 1 : 4);\r\n        this._vertexData = new Float32Array(this._capacity * vertexSize);\r\n        this._vertexBuffer = new Buffer(engine, this._vertexData, true, vertexSize);\r\n\r\n        let dataOffset = 0;\r\n        const positions = this._vertexBuffer.createVertexBuffer(VertexBuffer.PositionKind, dataOffset, 3, this._vertexBufferSize, this._useInstancing);\r\n        this._vertexBuffers[VertexBuffer.PositionKind] = positions;\r\n        dataOffset += 3;\r\n\r\n        const colors = this._vertexBuffer.createVertexBuffer(VertexBuffer.ColorKind, dataOffset, 4, this._vertexBufferSize, this._useInstancing);\r\n        this._vertexBuffers[VertexBuffer.ColorKind] = colors;\r\n        dataOffset += 4;\r\n\r\n        const options = this._vertexBuffer.createVertexBuffer(\"angle\", dataOffset, 1, this._vertexBufferSize, this._useInstancing);\r\n        this._vertexBuffers[\"angle\"] = options;\r\n        dataOffset += 1;\r\n\r\n        const size = this._vertexBuffer.createVertexBuffer(\"size\", dataOffset, 2, this._vertexBufferSize, this._useInstancing);\r\n        this._vertexBuffers[\"size\"] = size;\r\n        dataOffset += 2;\r\n\r\n        if (this._isAnimationSheetEnabled) {\r\n            const cellIndexBuffer = this._vertexBuffer.createVertexBuffer(\"cellIndex\", dataOffset, 1, this._vertexBufferSize, this._useInstancing);\r\n            this._vertexBuffers[\"cellIndex\"] = cellIndexBuffer;\r\n            dataOffset += 1;\r\n        }\r\n\r\n        if (\r\n            !this._isBillboardBased ||\r\n            this.billboardMode === Constants.PARTICLES_BILLBOARDMODE_STRETCHED ||\r\n            this.billboardMode === Constants.PARTICLES_BILLBOARDMODE_STRETCHED_LOCAL\r\n        ) {\r\n            const directionBuffer = this._vertexBuffer.createVertexBuffer(\"direction\", dataOffset, 3, this._vertexBufferSize, this._useInstancing);\r\n            this._vertexBuffers[\"direction\"] = directionBuffer;\r\n            dataOffset += 3;\r\n        }\r\n\r\n        if (this._useRampGradients) {\r\n            const rampDataBuffer = this._vertexBuffer.createVertexBuffer(\"remapData\", dataOffset, 4, this._vertexBufferSize, this._useInstancing);\r\n            this._vertexBuffers[\"remapData\"] = rampDataBuffer;\r\n            dataOffset += 4;\r\n        }\r\n\r\n        let offsets: VertexBuffer;\r\n        if (this._useInstancing) {\r\n            const spriteData = new Float32Array([0, 0, 1, 0, 0, 1, 1, 1]);\r\n            this._spriteBuffer = new Buffer(engine, spriteData, false, 2);\r\n            offsets = this._spriteBuffer.createVertexBuffer(\"offset\", 0, 2);\r\n        } else {\r\n            offsets = this._vertexBuffer.createVertexBuffer(\"offset\", dataOffset, 2, this._vertexBufferSize, this._useInstancing);\r\n            dataOffset += 2;\r\n        }\r\n        this._vertexBuffers[\"offset\"] = offsets;\r\n\r\n        this.resetDrawCache();\r\n    }\r\n\r\n    private _createIndexBuffer() {\r\n        if (this._useInstancing) {\r\n            this._linesIndexBufferUseInstancing = this._engine.createIndexBuffer(new Uint32Array([0, 1, 1, 3, 3, 2, 2, 0, 0, 3]));\r\n            return;\r\n        }\r\n        const indices = [];\r\n        const indicesWireframe = [];\r\n        let index = 0;\r\n        for (let count = 0; count < this._capacity; count++) {\r\n            indices.push(index);\r\n            indices.push(index + 1);\r\n            indices.push(index + 2);\r\n            indices.push(index);\r\n            indices.push(index + 2);\r\n            indices.push(index + 3);\r\n            indicesWireframe.push(index, index + 1, index + 1, index + 2, index + 2, index + 3, index + 3, index, index, index + 3);\r\n            index += 4;\r\n        }\r\n\r\n        this._indexBuffer = this._engine.createIndexBuffer(indices);\r\n        this._linesIndexBuffer = this._engine.createIndexBuffer(indicesWireframe);\r\n    }\r\n\r\n    /**\r\n     * Gets the maximum number of particles active at the same time.\r\n     * @returns The max number of active particles.\r\n     */\r\n    public getCapacity(): number {\r\n        return this._capacity;\r\n    }\r\n\r\n    /**\r\n     * Gets whether there are still active particles in the system.\r\n     * @returns True if it is alive, otherwise false.\r\n     */\r\n    public isAlive(): boolean {\r\n        return this._alive;\r\n    }\r\n\r\n    /**\r\n     * Gets if the system has been started. (Note: this will still be true after stop is called)\r\n     * @returns True if it has been started, otherwise false.\r\n     */\r\n    public isStarted(): boolean {\r\n        return this._started;\r\n    }\r\n\r\n    /** @internal */\r\n    public _preStart() {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Starts the particle system and begins to emit\r\n     * @param delay defines the delay in milliseconds before starting the system (this.startDelay by default)\r\n     */\r\n    public start(delay = this.startDelay): void {\r\n        if (!this.targetStopDuration && this._hasTargetStopDurationDependantGradient()) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"Particle system started with a targetStopDuration dependant gradient (eg. startSizeGradients) but no targetStopDuration set\";\r\n        }\r\n        if (delay) {\r\n            setTimeout(() => {\r\n                this.start(0);\r\n            }, delay);\r\n            return;\r\n        }\r\n        this._started = true;\r\n        this._stopped = false;\r\n        this._actualFrame = 0;\r\n\r\n        this._preStart();\r\n\r\n        // Reset emit gradient so it acts the same on every start\r\n        if (this._emitRateGradients) {\r\n            if (this._emitRateGradients.length > 0) {\r\n                this._currentEmitRateGradient = this._emitRateGradients[0];\r\n                this._currentEmitRate1 = this._currentEmitRateGradient.getFactor();\r\n                this._currentEmitRate2 = this._currentEmitRate1;\r\n            }\r\n            if (this._emitRateGradients.length > 1) {\r\n                this._currentEmitRate2 = this._emitRateGradients[1].getFactor();\r\n            }\r\n        }\r\n        // Reset start size gradient so it acts the same on every start\r\n        if (this._startSizeGradients) {\r\n            if (this._startSizeGradients.length > 0) {\r\n                this._currentStartSizeGradient = this._startSizeGradients[0];\r\n                this._currentStartSize1 = this._currentStartSizeGradient.getFactor();\r\n                this._currentStartSize2 = this._currentStartSize1;\r\n            }\r\n            if (this._startSizeGradients.length > 1) {\r\n                this._currentStartSize2 = this._startSizeGradients[1].getFactor();\r\n            }\r\n        }\r\n\r\n        if (this.preWarmCycles) {\r\n            if (this.emitter?.getClassName().indexOf(\"Mesh\") !== -1) {\r\n                (this.emitter as any).computeWorldMatrix(true);\r\n            }\r\n\r\n            const noiseTextureAsProcedural = this.noiseTexture as ProceduralTexture;\r\n\r\n            if (noiseTextureAsProcedural && noiseTextureAsProcedural.onGeneratedObservable) {\r\n                noiseTextureAsProcedural.onGeneratedObservable.addOnce(() => {\r\n                    setTimeout(() => {\r\n                        for (let index = 0; index < this.preWarmCycles; index++) {\r\n                            this.animate(true);\r\n                            noiseTextureAsProcedural.render();\r\n                        }\r\n                    });\r\n                });\r\n            } else {\r\n                for (let index = 0; index < this.preWarmCycles; index++) {\r\n                    this.animate(true);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Animations\r\n        if (this.beginAnimationOnStart && this.animations && this.animations.length > 0 && this._scene) {\r\n            this._scene.beginAnimation(this, this.beginAnimationFrom, this.beginAnimationTo, this.beginAnimationLoop);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Stops the particle system.\r\n     * @param stopSubEmitters if true it will stop the current system and all created sub-Systems if false it will stop the current root system only, this param is used by the root particle system only. The default value is true.\r\n     */\r\n    public stop(stopSubEmitters = true): void {\r\n        if (this._stopped) {\r\n            return;\r\n        }\r\n\r\n        this.onStoppedObservable.notifyObservers(this);\r\n\r\n        this._stopped = true;\r\n\r\n        this._postStop(stopSubEmitters);\r\n    }\r\n\r\n    /** @internal */\r\n    public _postStop(stopSubEmitters: boolean) {\r\n        // Do nothing\r\n    }\r\n\r\n    // Animation sheet\r\n\r\n    /**\r\n     * Remove all active particles\r\n     */\r\n    public reset(): void {\r\n        this._stockParticles.length = 0;\r\n        this._particles.length = 0;\r\n    }\r\n\r\n    /**\r\n     * @internal (for internal use only)\r\n     */\r\n    public _appendParticleVertex(index: number, particle: Particle, offsetX: number, offsetY: number): void {\r\n        let offset = index * this._vertexBufferSize;\r\n\r\n        this._vertexData[offset++] = particle.position.x + this.worldOffset.x;\r\n        this._vertexData[offset++] = particle.position.y + this.worldOffset.y;\r\n        this._vertexData[offset++] = particle.position.z + this.worldOffset.z;\r\n        this._vertexData[offset++] = particle.color.r;\r\n        this._vertexData[offset++] = particle.color.g;\r\n        this._vertexData[offset++] = particle.color.b;\r\n        this._vertexData[offset++] = particle.color.a;\r\n        this._vertexData[offset++] = particle.angle;\r\n\r\n        this._vertexData[offset++] = particle.scale.x * particle.size;\r\n        this._vertexData[offset++] = particle.scale.y * particle.size;\r\n\r\n        if (this._isAnimationSheetEnabled) {\r\n            this._vertexData[offset++] = particle.cellIndex;\r\n        }\r\n\r\n        if (!this._isBillboardBased) {\r\n            if (particle._initialDirection) {\r\n                let initialDirection = particle._initialDirection;\r\n                if (this.isLocal) {\r\n                    Vector3.TransformNormalToRef(initialDirection, this._emitterWorldMatrix, TmpVectors.Vector3[0]);\r\n                    initialDirection = TmpVectors.Vector3[0];\r\n                }\r\n                if (initialDirection.x === 0 && initialDirection.z === 0) {\r\n                    initialDirection.x = 0.001;\r\n                }\r\n\r\n                this._vertexData[offset++] = initialDirection.x;\r\n                this._vertexData[offset++] = initialDirection.y;\r\n                this._vertexData[offset++] = initialDirection.z;\r\n            } else {\r\n                let direction = particle.direction;\r\n                if (this.isLocal) {\r\n                    Vector3.TransformNormalToRef(direction, this._emitterWorldMatrix, TmpVectors.Vector3[0]);\r\n                    direction = TmpVectors.Vector3[0];\r\n                }\r\n\r\n                if (direction.x === 0 && direction.z === 0) {\r\n                    direction.x = 0.001;\r\n                }\r\n                this._vertexData[offset++] = direction.x;\r\n                this._vertexData[offset++] = direction.y;\r\n                this._vertexData[offset++] = direction.z;\r\n            }\r\n        } else if (this.billboardMode === Constants.PARTICLES_BILLBOARDMODE_STRETCHED || this.billboardMode === Constants.PARTICLES_BILLBOARDMODE_STRETCHED_LOCAL) {\r\n            this._vertexData[offset++] = particle.direction.x;\r\n            this._vertexData[offset++] = particle.direction.y;\r\n            this._vertexData[offset++] = particle.direction.z;\r\n        }\r\n\r\n        if (this._useRampGradients && particle.remapData) {\r\n            this._vertexData[offset++] = particle.remapData.x;\r\n            this._vertexData[offset++] = particle.remapData.y;\r\n            this._vertexData[offset++] = particle.remapData.z;\r\n            this._vertexData[offset++] = particle.remapData.w;\r\n        }\r\n\r\n        if (!this._useInstancing) {\r\n            if (this._isAnimationSheetEnabled) {\r\n                if (offsetX === 0) {\r\n                    offsetX = this._epsilon;\r\n                } else if (offsetX === 1) {\r\n                    offsetX = 1 - this._epsilon;\r\n                }\r\n\r\n                if (offsetY === 0) {\r\n                    offsetY = this._epsilon;\r\n                } else if (offsetY === 1) {\r\n                    offsetY = 1 - this._epsilon;\r\n                }\r\n            }\r\n\r\n            this._vertexData[offset++] = offsetX;\r\n            this._vertexData[offset++] = offsetY;\r\n        }\r\n    }\r\n\r\n    // start of sub system methods\r\n\r\n    /**\r\n     * \"Recycles\" one of the particle by copying it back to the \"stock\" of particles and removing it from the active list.\r\n     * Its lifetime will start back at 0.\r\n     * @param particle\r\n     */\r\n    public recycleParticle: (particle: Particle) => void = (particle) => {\r\n        // move particle from activeParticle list to stock particles\r\n        const lastParticle = <Particle>this._particles.pop();\r\n        if (lastParticle !== particle) {\r\n            lastParticle.copyTo(particle);\r\n        }\r\n        this._stockParticles.push(lastParticle);\r\n    };\r\n\r\n    private _createParticle: () => Particle = () => {\r\n        let particle: Particle;\r\n        if (this._stockParticles.length !== 0) {\r\n            particle = <Particle>this._stockParticles.pop();\r\n            particle._reset();\r\n        } else {\r\n            particle = new Particle(this);\r\n        }\r\n\r\n        this._prepareParticle(particle);\r\n        return particle;\r\n    };\r\n\r\n    /** @internal */\r\n    public _prepareParticle(particle: Particle) {\r\n        //Do nothing\r\n    }\r\n\r\n    private _update(newParticles: number): void {\r\n        // Update current\r\n        this._alive = this._particles.length > 0;\r\n\r\n        if ((<AbstractMesh>this.emitter).position) {\r\n            const emitterMesh = <AbstractMesh>this.emitter;\r\n            this._emitterWorldMatrix = emitterMesh.getWorldMatrix();\r\n        } else {\r\n            const emitterPosition = <Vector3>this.emitter;\r\n            this._emitterWorldMatrix = Matrix.Translation(emitterPosition.x, emitterPosition.y, emitterPosition.z);\r\n        }\r\n\r\n        this._emitterWorldMatrix.invertToRef(this._emitterInverseWorldMatrix);\r\n        this.updateFunction(this._particles);\r\n\r\n        // Add new ones\r\n        let particle: Particle;\r\n        for (let index = 0; index < newParticles; index++) {\r\n            if (this._particles.length === this._capacity) {\r\n                break;\r\n            }\r\n\r\n            particle = this._createParticle();\r\n\r\n            this._particles.push(particle);\r\n\r\n            // Life time\r\n            if (this.targetStopDuration && this._lifeTimeGradients && this._lifeTimeGradients.length > 0) {\r\n                const ratio = Clamp(this._actualFrame / this.targetStopDuration);\r\n                GradientHelper.GetCurrentGradient(ratio, this._lifeTimeGradients, (currentGradient, nextGradient) => {\r\n                    const factorGradient1 = <FactorGradient>currentGradient;\r\n                    const factorGradient2 = <FactorGradient>nextGradient;\r\n                    const lifeTime1 = factorGradient1.getFactor();\r\n                    const lifeTime2 = factorGradient2.getFactor();\r\n                    const gradient = (ratio - factorGradient1.gradient) / (factorGradient2.gradient - factorGradient1.gradient);\r\n                    particle.lifeTime = Lerp(lifeTime1, lifeTime2, gradient);\r\n                });\r\n            } else {\r\n                particle.lifeTime = RandomRange(this.minLifeTime, this.maxLifeTime);\r\n            }\r\n\r\n            // Emitter\r\n            const emitPower = RandomRange(this.minEmitPower, this.maxEmitPower);\r\n\r\n            if (this.startPositionFunction) {\r\n                this.startPositionFunction(this._emitterWorldMatrix, particle.position, particle, this.isLocal);\r\n            } else {\r\n                this.particleEmitterType.startPositionFunction(this._emitterWorldMatrix, particle.position, particle, this.isLocal);\r\n            }\r\n\r\n            if (this.isLocal) {\r\n                if (!particle._localPosition) {\r\n                    particle._localPosition = particle.position.clone();\r\n                } else {\r\n                    particle._localPosition.copyFrom(particle.position);\r\n                }\r\n                Vector3.TransformCoordinatesToRef(particle._localPosition!, this._emitterWorldMatrix, particle.position);\r\n            }\r\n\r\n            if (this.startDirectionFunction) {\r\n                this.startDirectionFunction(this._emitterWorldMatrix, particle.direction, particle, this.isLocal);\r\n            } else {\r\n                this.particleEmitterType.startDirectionFunction(this._emitterWorldMatrix, particle.direction, particle, this.isLocal, this._emitterInverseWorldMatrix);\r\n            }\r\n\r\n            if (emitPower === 0) {\r\n                if (!particle._initialDirection) {\r\n                    particle._initialDirection = particle.direction.clone();\r\n                } else {\r\n                    particle._initialDirection.copyFrom(particle.direction);\r\n                }\r\n            } else {\r\n                particle._initialDirection = null;\r\n            }\r\n\r\n            particle.direction.scaleInPlace(emitPower);\r\n\r\n            // Size\r\n            if (!this._sizeGradients || this._sizeGradients.length === 0) {\r\n                particle.size = RandomRange(this.minSize, this.maxSize);\r\n            } else {\r\n                particle._currentSizeGradient = this._sizeGradients[0];\r\n                particle._currentSize1 = particle._currentSizeGradient.getFactor();\r\n                particle.size = particle._currentSize1;\r\n\r\n                if (this._sizeGradients.length > 1) {\r\n                    particle._currentSize2 = this._sizeGradients[1].getFactor();\r\n                } else {\r\n                    particle._currentSize2 = particle._currentSize1;\r\n                }\r\n            }\r\n            // Size and scale\r\n            particle.scale.copyFromFloats(RandomRange(this.minScaleX, this.maxScaleX), RandomRange(this.minScaleY, this.maxScaleY));\r\n\r\n            // Adjust scale by start size\r\n            if (this._startSizeGradients && this._startSizeGradients[0] && this.targetStopDuration) {\r\n                const ratio = this._actualFrame / this.targetStopDuration;\r\n                GradientHelper.GetCurrentGradient(ratio, this._startSizeGradients, (currentGradient, nextGradient, scale) => {\r\n                    if (currentGradient !== this._currentStartSizeGradient) {\r\n                        this._currentStartSize1 = this._currentStartSize2;\r\n                        this._currentStartSize2 = (<FactorGradient>nextGradient).getFactor();\r\n                        this._currentStartSizeGradient = <FactorGradient>currentGradient;\r\n                    }\r\n\r\n                    const value = Lerp(this._currentStartSize1, this._currentStartSize2, scale);\r\n                    particle.scale.scaleInPlace(value);\r\n                });\r\n            }\r\n\r\n            // Angle\r\n            if (!this._angularSpeedGradients || this._angularSpeedGradients.length === 0) {\r\n                particle.angularSpeed = RandomRange(this.minAngularSpeed, this.maxAngularSpeed);\r\n            } else {\r\n                particle._currentAngularSpeedGradient = this._angularSpeedGradients[0];\r\n                particle.angularSpeed = particle._currentAngularSpeedGradient.getFactor();\r\n                particle._currentAngularSpeed1 = particle.angularSpeed;\r\n\r\n                if (this._angularSpeedGradients.length > 1) {\r\n                    particle._currentAngularSpeed2 = this._angularSpeedGradients[1].getFactor();\r\n                } else {\r\n                    particle._currentAngularSpeed2 = particle._currentAngularSpeed1;\r\n                }\r\n            }\r\n            particle.angle = RandomRange(this.minInitialRotation, this.maxInitialRotation);\r\n\r\n            // Velocity\r\n            if (this._velocityGradients && this._velocityGradients.length > 0) {\r\n                particle._currentVelocityGradient = this._velocityGradients[0];\r\n                particle._currentVelocity1 = particle._currentVelocityGradient.getFactor();\r\n\r\n                if (this._velocityGradients.length > 1) {\r\n                    particle._currentVelocity2 = this._velocityGradients[1].getFactor();\r\n                } else {\r\n                    particle._currentVelocity2 = particle._currentVelocity1;\r\n                }\r\n            }\r\n\r\n            // Limit velocity\r\n            if (this._limitVelocityGradients && this._limitVelocityGradients.length > 0) {\r\n                particle._currentLimitVelocityGradient = this._limitVelocityGradients[0];\r\n                particle._currentLimitVelocity1 = particle._currentLimitVelocityGradient.getFactor();\r\n\r\n                if (this._limitVelocityGradients.length > 1) {\r\n                    particle._currentLimitVelocity2 = this._limitVelocityGradients[1].getFactor();\r\n                } else {\r\n                    particle._currentLimitVelocity2 = particle._currentLimitVelocity1;\r\n                }\r\n            }\r\n\r\n            // Drag\r\n            if (this._dragGradients && this._dragGradients.length > 0) {\r\n                particle._currentDragGradient = this._dragGradients[0];\r\n                particle._currentDrag1 = particle._currentDragGradient.getFactor();\r\n\r\n                if (this._dragGradients.length > 1) {\r\n                    particle._currentDrag2 = this._dragGradients[1].getFactor();\r\n                } else {\r\n                    particle._currentDrag2 = particle._currentDrag1;\r\n                }\r\n            }\r\n\r\n            // Color\r\n            if (!this._colorGradients || this._colorGradients.length === 0) {\r\n                const step = RandomRange(0, 1.0);\r\n\r\n                Color4.LerpToRef(this.color1, this.color2, step, particle.color);\r\n\r\n                this.colorDead.subtractToRef(particle.color, this._colorDiff);\r\n                this._colorDiff.scaleToRef(1.0 / particle.lifeTime, particle.colorStep);\r\n            } else {\r\n                particle._currentColorGradient = this._colorGradients[0];\r\n                particle._currentColorGradient.getColorToRef(particle.color);\r\n                particle._currentColor1.copyFrom(particle.color);\r\n\r\n                if (this._colorGradients.length > 1) {\r\n                    this._colorGradients[1].getColorToRef(particle._currentColor2);\r\n                } else {\r\n                    particle._currentColor2.copyFrom(particle.color);\r\n                }\r\n            }\r\n\r\n            // Sheet\r\n            if (this._isAnimationSheetEnabled) {\r\n                particle._initialStartSpriteCellID = this.startSpriteCellID;\r\n                particle._initialEndSpriteCellID = this.endSpriteCellID;\r\n                particle._initialSpriteCellLoop = this.spriteCellLoop;\r\n            }\r\n\r\n            // Inherited Velocity\r\n            particle.direction.addInPlace(this._inheritedVelocityOffset);\r\n\r\n            // Ramp\r\n            if (this._useRampGradients) {\r\n                particle.remapData = new Vector4(0, 1, 0, 1);\r\n            }\r\n\r\n            // Noise texture coordinates\r\n            if (this.noiseTexture) {\r\n                if (particle._randomNoiseCoordinates1) {\r\n                    particle._randomNoiseCoordinates1.copyFromFloats(Math.random(), Math.random(), Math.random());\r\n                    particle._randomNoiseCoordinates2.copyFromFloats(Math.random(), Math.random(), Math.random());\r\n                } else {\r\n                    particle._randomNoiseCoordinates1 = new Vector3(Math.random(), Math.random(), Math.random());\r\n                    particle._randomNoiseCoordinates2 = new Vector3(Math.random(), Math.random(), Math.random());\r\n                }\r\n            }\r\n\r\n            // Update the position of the attached sub-emitters to match their attached particle\r\n            particle._inheritParticleInfoToSubEmitters();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _GetAttributeNamesOrOptions(isAnimationSheetEnabled = false, isBillboardBased = false, useRampGradients = false): string[] {\r\n        const attributeNamesOrOptions = [VertexBuffer.PositionKind, VertexBuffer.ColorKind, \"angle\", \"offset\", \"size\"];\r\n\r\n        if (isAnimationSheetEnabled) {\r\n            attributeNamesOrOptions.push(\"cellIndex\");\r\n        }\r\n\r\n        if (!isBillboardBased) {\r\n            attributeNamesOrOptions.push(\"direction\");\r\n        }\r\n\r\n        if (useRampGradients) {\r\n            attributeNamesOrOptions.push(\"remapData\");\r\n        }\r\n\r\n        return attributeNamesOrOptions;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _GetEffectCreationOptions(isAnimationSheetEnabled = false, useLogarithmicDepth = false, applyFog = false): string[] {\r\n        const effectCreationOption = [\"invView\", \"view\", \"projection\", \"textureMask\", \"translationPivot\", \"eyePosition\"];\r\n\r\n        addClipPlaneUniforms(effectCreationOption);\r\n\r\n        if (isAnimationSheetEnabled) {\r\n            effectCreationOption.push(\"particlesInfos\");\r\n        }\r\n        if (useLogarithmicDepth) {\r\n            effectCreationOption.push(\"logarithmicDepthConstant\");\r\n        }\r\n\r\n        if (applyFog) {\r\n            effectCreationOption.push(\"vFogInfos\");\r\n            effectCreationOption.push(\"vFogColor\");\r\n        }\r\n\r\n        return effectCreationOption;\r\n    }\r\n\r\n    /**\r\n     * Fill the defines array according to the current settings of the particle system\r\n     * @param defines Array to be updated\r\n     * @param blendMode blend mode to take into account when updating the array\r\n     */\r\n    public fillDefines(defines: Array<string>, blendMode: number) {\r\n        if (this._scene) {\r\n            prepareStringDefinesForClipPlanes(this, this._scene, defines);\r\n            if (this.applyFog && this._scene.fogEnabled && this._scene.fogMode !== Constants.FOGMODE_NONE) {\r\n                defines.push(\"#define FOG\");\r\n            }\r\n        }\r\n\r\n        if (this._isAnimationSheetEnabled) {\r\n            defines.push(\"#define ANIMATESHEET\");\r\n        }\r\n\r\n        if (this.useLogarithmicDepth) {\r\n            defines.push(\"#define LOGARITHMICDEPTH\");\r\n        }\r\n\r\n        if (blendMode === BaseParticleSystem.BLENDMODE_MULTIPLY) {\r\n            defines.push(\"#define BLENDMULTIPLYMODE\");\r\n        }\r\n\r\n        if (this._useRampGradients) {\r\n            defines.push(\"#define RAMPGRADIENT\");\r\n        }\r\n\r\n        if (this._isBillboardBased) {\r\n            defines.push(\"#define BILLBOARD\");\r\n\r\n            switch (this.billboardMode) {\r\n                case Constants.PARTICLES_BILLBOARDMODE_Y:\r\n                    defines.push(\"#define BILLBOARDY\");\r\n                    break;\r\n                case Constants.PARTICLES_BILLBOARDMODE_STRETCHED:\r\n                case Constants.PARTICLES_BILLBOARDMODE_STRETCHED_LOCAL:\r\n                    defines.push(\"#define BILLBOARDSTRETCHED\");\r\n                    if (this.billboardMode === Constants.PARTICLES_BILLBOARDMODE_STRETCHED_LOCAL) {\r\n                        defines.push(\"#define BILLBOARDSTRETCHED_LOCAL\");\r\n                    }\r\n                    break;\r\n                case Constants.PARTICLES_BILLBOARDMODE_ALL:\r\n                    defines.push(\"#define BILLBOARDMODE_ALL\");\r\n                    break;\r\n                default:\r\n                    break;\r\n            }\r\n        }\r\n\r\n        if (this._imageProcessingConfiguration) {\r\n            this._imageProcessingConfiguration.prepareDefines(this._imageProcessingConfigurationDefines);\r\n            defines.push(this._imageProcessingConfigurationDefines.toString());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Fill the uniforms, attributes and samplers arrays according to the current settings of the particle system\r\n     * @param uniforms Uniforms array to fill\r\n     * @param attributes Attributes array to fill\r\n     * @param samplers Samplers array to fill\r\n     */\r\n    public fillUniformsAttributesAndSamplerNames(uniforms: Array<string>, attributes: Array<string>, samplers: Array<string>) {\r\n        attributes.push(\r\n            ...ThinParticleSystem._GetAttributeNamesOrOptions(\r\n                this._isAnimationSheetEnabled,\r\n                this._isBillboardBased &&\r\n                    this.billboardMode !== Constants.PARTICLES_BILLBOARDMODE_STRETCHED &&\r\n                    this.billboardMode !== Constants.PARTICLES_BILLBOARDMODE_STRETCHED_LOCAL,\r\n                this._useRampGradients\r\n            )\r\n        );\r\n\r\n        uniforms.push(...ThinParticleSystem._GetEffectCreationOptions(this._isAnimationSheetEnabled, this.useLogarithmicDepth, this.applyFog));\r\n\r\n        samplers.push(\"diffuseSampler\", \"rampSampler\");\r\n\r\n        if (this._imageProcessingConfiguration) {\r\n            PrepareUniformsForImageProcessing(uniforms, this._imageProcessingConfigurationDefines);\r\n            PrepareSamplersForImageProcessing(samplers, this._imageProcessingConfigurationDefines);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    private _getWrapper(blendMode: number): DrawWrapper {\r\n        const customWrapper = this._getCustomDrawWrapper(blendMode);\r\n\r\n        if (customWrapper?.effect) {\r\n            return customWrapper;\r\n        }\r\n\r\n        const defines: Array<string> = [];\r\n\r\n        this.fillDefines(defines, blendMode);\r\n\r\n        // Effect\r\n        const currentRenderPassId = this._engine._features.supportRenderPasses ? (this._engine as Engine).currentRenderPassId : Constants.RENDERPASS_MAIN;\r\n        let drawWrappers = this._drawWrappers[currentRenderPassId];\r\n        if (!drawWrappers) {\r\n            drawWrappers = this._drawWrappers[currentRenderPassId] = [];\r\n        }\r\n        let drawWrapper = drawWrappers[blendMode];\r\n        if (!drawWrapper) {\r\n            drawWrapper = new DrawWrapper(this._engine);\r\n            if (drawWrapper.drawContext) {\r\n                drawWrapper.drawContext.useInstancing = this._useInstancing;\r\n            }\r\n            drawWrappers[blendMode] = drawWrapper;\r\n        }\r\n\r\n        const join = defines.join(\"\\n\");\r\n        if (drawWrapper.defines !== join) {\r\n            const attributesNamesOrOptions: Array<string> = [];\r\n            const effectCreationOption: Array<string> = [];\r\n            const samplers: Array<string> = [];\r\n\r\n            this.fillUniformsAttributesAndSamplerNames(effectCreationOption, attributesNamesOrOptions, samplers);\r\n\r\n            drawWrapper.setEffect(this._engine.createEffect(\"particles\", attributesNamesOrOptions, effectCreationOption, samplers, join), join);\r\n        }\r\n\r\n        return drawWrapper;\r\n    }\r\n\r\n    /**\r\n     * Animates the particle system for the current frame by emitting new particles and or animating the living ones.\r\n     * @param preWarmOnly will prevent the system from updating the vertex buffer (default is false)\r\n     */\r\n    public animate(preWarmOnly = false): void {\r\n        if (!this._started) {\r\n            return;\r\n        }\r\n\r\n        if (!preWarmOnly && this._scene) {\r\n            // Check\r\n            if (!this.isReady()) {\r\n                return;\r\n            }\r\n\r\n            if (this._currentRenderId === this._scene.getFrameId()) {\r\n                return;\r\n            }\r\n            this._currentRenderId = this._scene.getFrameId();\r\n        }\r\n\r\n        this._scaledUpdateSpeed = this.updateSpeed * (preWarmOnly ? this.preWarmStepOffset : this._scene?.getAnimationRatio() || 1);\r\n\r\n        // Determine the number of particles we need to create\r\n        let newParticles;\r\n\r\n        if (this.manualEmitCount > -1) {\r\n            newParticles = this.manualEmitCount;\r\n            this._newPartsExcess = 0;\r\n            this.manualEmitCount = 0;\r\n        } else {\r\n            let rate = this.emitRate;\r\n\r\n            if (this._emitRateGradients && this._emitRateGradients.length > 0 && this.targetStopDuration) {\r\n                const ratio = this._actualFrame / this.targetStopDuration;\r\n                GradientHelper.GetCurrentGradient(ratio, this._emitRateGradients, (currentGradient, nextGradient, scale) => {\r\n                    if (currentGradient !== this._currentEmitRateGradient) {\r\n                        this._currentEmitRate1 = this._currentEmitRate2;\r\n                        this._currentEmitRate2 = (<FactorGradient>nextGradient).getFactor();\r\n                        this._currentEmitRateGradient = <FactorGradient>currentGradient;\r\n                    }\r\n\r\n                    rate = Lerp(this._currentEmitRate1, this._currentEmitRate2, scale);\r\n                });\r\n            }\r\n\r\n            newParticles = (rate * this._scaledUpdateSpeed) >> 0;\r\n            this._newPartsExcess += rate * this._scaledUpdateSpeed - newParticles;\r\n        }\r\n\r\n        if (this._newPartsExcess > 1.0) {\r\n            newParticles += this._newPartsExcess >> 0;\r\n            this._newPartsExcess -= this._newPartsExcess >> 0;\r\n        }\r\n\r\n        this._alive = false;\r\n\r\n        if (!this._stopped) {\r\n            this._actualFrame += this._scaledUpdateSpeed;\r\n\r\n            if (this.targetStopDuration && this._actualFrame >= this.targetStopDuration) {\r\n                this.stop();\r\n            }\r\n        } else {\r\n            newParticles = 0;\r\n        }\r\n        this._update(newParticles);\r\n\r\n        // Stopped?\r\n        if (this._stopped) {\r\n            if (!this._alive) {\r\n                this._started = false;\r\n                if (this.onAnimationEnd) {\r\n                    this.onAnimationEnd();\r\n                }\r\n                if (this.disposeOnStop && this._scene) {\r\n                    this._scene._toBeDisposed.push(this);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (!preWarmOnly) {\r\n            // Update VBO\r\n            let offset = 0;\r\n            for (let index = 0; index < this._particles.length; index++) {\r\n                const particle = this._particles[index];\r\n                this._appendParticleVertices(offset, particle);\r\n                offset += this._useInstancing ? 1 : 4;\r\n            }\r\n\r\n            if (this._vertexBuffer) {\r\n                this._vertexBuffer.updateDirectly(this._vertexData, 0, this._particles.length);\r\n            }\r\n        }\r\n\r\n        if (this.manualEmitCount === 0 && this.disposeOnStop) {\r\n            this.stop();\r\n        }\r\n    }\r\n\r\n    private _appendParticleVertices(offset: number, particle: Particle) {\r\n        this._appendParticleVertex(offset++, particle, 0, 0);\r\n        if (!this._useInstancing) {\r\n            this._appendParticleVertex(offset++, particle, 1, 0);\r\n            this._appendParticleVertex(offset++, particle, 1, 1);\r\n            this._appendParticleVertex(offset++, particle, 0, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the particle system.\r\n     */\r\n    public rebuild(): void {\r\n        if (this._engine.getCaps().vertexArrayObject) {\r\n            this._vertexArrayObject = null;\r\n        }\r\n\r\n        this._createIndexBuffer();\r\n\r\n        this._spriteBuffer?._rebuild();\r\n\r\n        this._createVertexBuffers();\r\n\r\n        this.resetDrawCache();\r\n    }\r\n\r\n    /**\r\n     * Is this system ready to be used/rendered\r\n     * @returns true if the system is ready\r\n     */\r\n    public isReady(): boolean {\r\n        if (!this.emitter || (this._imageProcessingConfiguration && !this._imageProcessingConfiguration.isReady()) || !this.particleTexture || !this.particleTexture.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        if (this.blendMode !== BaseParticleSystem.BLENDMODE_MULTIPLYADD) {\r\n            if (!this._getWrapper(this.blendMode).effect!.isReady()) {\r\n                return false;\r\n            }\r\n        } else {\r\n            if (!this._getWrapper(BaseParticleSystem.BLENDMODE_MULTIPLY).effect!.isReady()) {\r\n                return false;\r\n            }\r\n            if (!this._getWrapper(BaseParticleSystem.BLENDMODE_ADD).effect!.isReady()) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    private _render(blendMode: number) {\r\n        const drawWrapper = this._getWrapper(blendMode);\r\n        const effect = drawWrapper.effect!;\r\n\r\n        const engine = this._engine;\r\n\r\n        // Render\r\n        engine.enableEffect(drawWrapper);\r\n\r\n        const viewMatrix = this.defaultViewMatrix ?? this._scene!.getViewMatrix();\r\n        effect.setTexture(\"diffuseSampler\", this.particleTexture);\r\n        effect.setMatrix(\"view\", viewMatrix);\r\n        effect.setMatrix(\"projection\", this.defaultProjectionMatrix ?? this._scene!.getProjectionMatrix());\r\n\r\n        if (this._isAnimationSheetEnabled && this.particleTexture) {\r\n            const baseSize = this.particleTexture.getBaseSize();\r\n            effect.setFloat3(\"particlesInfos\", this.spriteCellWidth / baseSize.width, this.spriteCellHeight / baseSize.height, this.spriteCellWidth / baseSize.width);\r\n        }\r\n\r\n        effect.setVector2(\"translationPivot\", this.translationPivot);\r\n        effect.setFloat4(\"textureMask\", this.textureMask.r, this.textureMask.g, this.textureMask.b, this.textureMask.a);\r\n\r\n        if (this._isBillboardBased && this._scene) {\r\n            const camera = this._scene.activeCamera!;\r\n            effect.setVector3(\"eyePosition\", camera.globalPosition);\r\n        }\r\n\r\n        if (this._rampGradientsTexture) {\r\n            if (!this._rampGradients || !this._rampGradients.length) {\r\n                this._rampGradientsTexture.dispose();\r\n                this._rampGradientsTexture = null;\r\n            }\r\n            effect.setTexture(\"rampSampler\", this._rampGradientsTexture);\r\n        }\r\n\r\n        const defines = effect.defines;\r\n\r\n        if (this._scene) {\r\n            bindClipPlane(effect, this, this._scene);\r\n\r\n            if (this.applyFog) {\r\n                BindFogParameters(this._scene, undefined, effect);\r\n            }\r\n        }\r\n\r\n        if (defines.indexOf(\"#define BILLBOARDMODE_ALL\") >= 0) {\r\n            viewMatrix.invertToRef(TmpVectors.Matrix[0]);\r\n            effect.setMatrix(\"invView\", TmpVectors.Matrix[0]);\r\n        }\r\n\r\n        if (this._vertexArrayObject !== undefined) {\r\n            if (this._scene?.forceWireframe) {\r\n                engine.bindBuffers(this._vertexBuffers, this._linesIndexBufferUseInstancing, effect);\r\n            } else {\r\n                if (!this._vertexArrayObject) {\r\n                    this._vertexArrayObject = this._engine.recordVertexArrayObject(this._vertexBuffers, null, effect);\r\n                }\r\n\r\n                this._engine.bindVertexArrayObject(this._vertexArrayObject, this._scene?.forceWireframe ? this._linesIndexBufferUseInstancing : this._indexBuffer);\r\n            }\r\n        } else {\r\n            if (!this._indexBuffer) {\r\n                // Use instancing mode\r\n                engine.bindBuffers(this._vertexBuffers, this._scene?.forceWireframe ? this._linesIndexBufferUseInstancing : null, effect);\r\n            } else {\r\n                engine.bindBuffers(this._vertexBuffers, this._scene?.forceWireframe ? this._linesIndexBuffer : this._indexBuffer, effect);\r\n            }\r\n        }\r\n\r\n        // Log. depth\r\n        if (this.useLogarithmicDepth && this._scene) {\r\n            BindLogDepth(defines, effect, this._scene);\r\n        }\r\n\r\n        // image processing\r\n        if (this._imageProcessingConfiguration && !this._imageProcessingConfiguration.applyByPostProcess) {\r\n            this._imageProcessingConfiguration.bind(effect);\r\n        }\r\n\r\n        // Draw order\r\n        switch (blendMode) {\r\n            case BaseParticleSystem.BLENDMODE_ADD:\r\n                engine.setAlphaMode(Constants.ALPHA_ADD);\r\n                break;\r\n            case BaseParticleSystem.BLENDMODE_ONEONE:\r\n                engine.setAlphaMode(Constants.ALPHA_ONEONE);\r\n                break;\r\n            case BaseParticleSystem.BLENDMODE_STANDARD:\r\n                engine.setAlphaMode(Constants.ALPHA_COMBINE);\r\n                break;\r\n            case BaseParticleSystem.BLENDMODE_MULTIPLY:\r\n                engine.setAlphaMode(Constants.ALPHA_MULTIPLY);\r\n                break;\r\n        }\r\n\r\n        if (this._onBeforeDrawParticlesObservable) {\r\n            this._onBeforeDrawParticlesObservable.notifyObservers(effect);\r\n        }\r\n\r\n        if (this._useInstancing) {\r\n            if (this._scene?.forceWireframe) {\r\n                engine.drawElementsType(Constants.MATERIAL_LineStripDrawMode, 0, 10, this._particles.length);\r\n            } else {\r\n                engine.drawArraysType(Constants.MATERIAL_TriangleStripDrawMode, 0, 4, this._particles.length);\r\n            }\r\n        } else {\r\n            if (this._scene?.forceWireframe) {\r\n                engine.drawElementsType(Constants.MATERIAL_WireFrameFillMode, 0, this._particles.length * 10);\r\n            } else {\r\n                engine.drawElementsType(Constants.MATERIAL_TriangleFillMode, 0, this._particles.length * 6);\r\n            }\r\n        }\r\n\r\n        return this._particles.length;\r\n    }\r\n\r\n    /**\r\n     * Renders the particle system in its current state.\r\n     * @returns the current number of particles\r\n     */\r\n    public render(): number {\r\n        // Check\r\n        if (!this.isReady() || !this._particles.length) {\r\n            return 0;\r\n        }\r\n\r\n        const engine = this._engine as any;\r\n        if (engine.setState) {\r\n            engine.setState(false);\r\n\r\n            if (this.forceDepthWrite) {\r\n                engine.setDepthWrite(true);\r\n            }\r\n        }\r\n\r\n        let outparticles = 0;\r\n\r\n        if (this.blendMode === BaseParticleSystem.BLENDMODE_MULTIPLYADD) {\r\n            outparticles = this._render(BaseParticleSystem.BLENDMODE_MULTIPLY) + this._render(BaseParticleSystem.BLENDMODE_ADD);\r\n        } else {\r\n            outparticles = this._render(this.blendMode);\r\n        }\r\n\r\n        this._engine.unbindInstanceAttributes();\r\n        this._engine.setAlphaMode(Constants.ALPHA_DISABLE);\r\n\r\n        return outparticles;\r\n    }\r\n\r\n    /** @internal */\r\n    public _onDispose(disposeAttachedSubEmitters = false, disposeEndSubEmitters = false) {\r\n        // Do Nothing\r\n    }\r\n\r\n    /**\r\n     * Disposes the particle system and free the associated resources\r\n     * @param disposeTexture defines if the particle texture must be disposed as well (true by default)\r\n     * @param disposeAttachedSubEmitters defines if the attached sub-emitters must be disposed as well (false by default)\r\n     * @param disposeEndSubEmitters defines if the end type sub-emitters must be disposed as well (false by default)\r\n     */\r\n    public dispose(disposeTexture = true, disposeAttachedSubEmitters = false, disposeEndSubEmitters = false): void {\r\n        this.resetDrawCache();\r\n\r\n        if (this._vertexBuffer) {\r\n            this._vertexBuffer.dispose();\r\n            this._vertexBuffer = null;\r\n        }\r\n\r\n        if (this._spriteBuffer) {\r\n            this._spriteBuffer.dispose();\r\n            this._spriteBuffer = null;\r\n        }\r\n\r\n        if (this._indexBuffer) {\r\n            this._engine._releaseBuffer(this._indexBuffer);\r\n            this._indexBuffer = null;\r\n        }\r\n\r\n        if (this._linesIndexBuffer) {\r\n            this._engine._releaseBuffer(this._linesIndexBuffer);\r\n            this._linesIndexBuffer = null;\r\n        }\r\n\r\n        if (this._linesIndexBufferUseInstancing) {\r\n            this._engine._releaseBuffer(this._linesIndexBufferUseInstancing);\r\n            this._linesIndexBufferUseInstancing = null;\r\n        }\r\n\r\n        if (this._vertexArrayObject) {\r\n            this._engine.releaseVertexArrayObject(this._vertexArrayObject);\r\n            this._vertexArrayObject = null;\r\n        }\r\n\r\n        if (disposeTexture && this.particleTexture) {\r\n            this.particleTexture.dispose();\r\n            this.particleTexture = null;\r\n        }\r\n\r\n        if (disposeTexture && this.noiseTexture) {\r\n            this.noiseTexture.dispose();\r\n            this.noiseTexture = null;\r\n        }\r\n\r\n        if (this._rampGradientsTexture) {\r\n            this._rampGradientsTexture.dispose();\r\n            this._rampGradientsTexture = null;\r\n        }\r\n\r\n        this._onDispose(disposeAttachedSubEmitters, disposeEndSubEmitters);\r\n\r\n        if (this._onBeforeDrawParticlesObservable) {\r\n            this._onBeforeDrawParticlesObservable.clear();\r\n        }\r\n\r\n        // Remove from scene\r\n        if (this._scene) {\r\n            const index = this._scene.particleSystems.indexOf(this);\r\n            if (index > -1) {\r\n                this._scene.particleSystems.splice(index, 1);\r\n            }\r\n\r\n            this._scene._activeParticleSystems.dispose();\r\n        }\r\n\r\n        // Callback\r\n        this.onDisposeObservable.notifyObservers(this);\r\n        this.onDisposeObservable.clear();\r\n        this.onStoppedObservable.clear();\r\n\r\n        this.reset();\r\n    }\r\n}\r\n"]}