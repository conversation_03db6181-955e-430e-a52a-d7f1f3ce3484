{"version": 3, "file": "proceduralTexture.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Textures/Procedurals/proceduralTexture.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAMtD,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AACvD,OAAO,EAAE,uBAAuB,EAAE,MAAM,yBAAyB,CAAC;AAElE,OAAO,EAAE,QAAQ,EAAE,MAAM,6BAA6B,CAAC;AAEvD,OAAO,EAAE,OAAO,EAAE,MAAM,qCAAqC,CAAC;AAE9D,OAAO,EAAE,mBAAmB,EAAE,MAAM,iDAAiD,CAAC;AACtF,OAAO,EAAE,+BAA+B,EAAE,MAAM,mCAAmC,CAAC;AAEpF,OAAO,iDAAiD,CAAC;AACzD,OAAO,qDAAqD,CAAC;AAC7D,OAAO,oCAAoC,CAAC;AAE5C,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAGxD,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAC3D,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AACvD,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAahD;;;;GAIG;AACH,MAAM,OAAO,iBAAkB,SAAQ,OAAO;IA6E1C;;;;;;;;;;;;;;;;OAgBG;IACH,YACI,IAAY,EACZ,IAAiB,EACjB,QAAa,EACb,KAAsB,EACtB,kBAAyE,IAAI,EAC7E,eAAe,GAAG,IAAI,EACtB,MAAM,GAAG,KAAK,EACd,WAAW,GAAG,SAAS,CAAC,wBAAwB;QAEhD,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,eAAe,CAAC,CAAC;QAvGzC;;WAEG;QAEI,cAAS,GAAG,IAAI,CAAC;QAExB;;WAEG;QAEI,cAAS,GAAG,IAAI,CAAC;QAOxB;;WAEG;QACI,0BAAqB,GAAG,IAAI,UAAU,EAAqB,CAAC;QAEnE;;WAEG;QACI,iCAA4B,GAAG,IAAI,UAAU,EAAqB,CAAC;QAE1E;;WAEG;QACI,uBAAkB,GAA2B,IAAI,CAAC;QAQzD,gBAAgB;QACT,cAAS,GAA+B,EAAE,CAAC;QAQ1C,sBAAiB,GAAG,CAAC,CAAC,CAAC;QACvB,aAAQ,GAAG,CAAC,CAAC,CAAC;QACd,iBAAY,GAAG,CAAC,CAAC;QACjB,mBAAc,GAA8C,EAAE,CAAC;QAE/D,cAAS,GAAG,IAAI,KAAK,EAAU,CAAC;QAChC,cAAS,GAAG,IAAI,KAAK,EAAU,CAAC;QAGhC,YAAO,GAA8B,EAAE,CAAC;QACxC,UAAK,GAA8B,EAAE,CAAC;QACtC,kBAAa,GAAgC,EAAE,CAAC;QAChD,aAAQ,GAA8B,EAAE,CAAC;QACzC,aAAQ,GAA8B,EAAE,CAAC;QACzC,cAAS,GAA+B,EAAE,CAAC;QAC3C,cAAS,GAA+B,EAAE,CAAC;QAC3C,cAAS,GAA8B,EAAE,CAAC;QAE1C,yBAAoB,GAAG,KAAK,CAAC;QAG7B,mBAAc,GAAqB,IAAI,CAAC;QAExC,qBAAgB,GAAG,CAAC,CAAC,CAAC;QAGtB,eAAU,GAAkC,IAAI,CAAC;QAgCrD,IAAI,eAAe,KAAK,IAAI,IAAI,CAAC,CAAC,eAAe,YAAY,OAAO,CAAC,EAAE;YACnE,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC;YAChC,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC,eAAe,IAAI,IAAI,CAAC;SACnE;aAAM;YACH,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;YACnB,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;SAC3C;QAED,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,WAAW,CAAC,gBAAiB,CAAC;QACzD,IAAI,SAAS,GAAG,KAAK,CAAC,aAAa,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,CAAC;QACpF,IAAI,CAAC,SAAS,EAAE;YACZ,SAAS,GAAG,IAAI,+BAA+B,CAAC,KAAK,CAAC,CAAC;YACvD,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;SAClC;QACD,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEpC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAErC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEtD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE3B,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,CAAC,CAAC;QACpF,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC;QAElC,MAAM;QACN,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAErB,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,YAAY,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAE1I,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAEO,gBAAgB,CAAC,MAAe,EAAE,IAAiB,EAAE,eAAwB,EAAE,WAAmB;QACtG,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,6BAA6B,CAAC,IAAc,EAAE;gBAC7E,eAAe,EAAE,eAAe;gBAChC,mBAAmB,EAAE,KAAK;gBAC1B,qBAAqB,EAAE,KAAK;gBAC5B,IAAI,EAAE,WAAW;gBACjB,GAAG,IAAI,CAAC,QAAQ;aACnB,CAAC,CAAC;YACH,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SAC5B;aAAM;YACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,IAAI,EAAE;gBAC/D,eAAe,EAAE,eAAe;gBAChC,mBAAmB,EAAE,KAAK;gBAC1B,qBAAqB,EAAE,KAAK;gBAC5B,IAAI,EAAE,WAAW;gBACjB,GAAG,IAAI,CAAC,QAAQ;aACnB,CAAC,CAAC;SACN;QACD,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,MAAc;QAC5B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,UAAU;QACb,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,gBAAgB,EAAE;YAC9D,OAAO,IAAI,CAAC,YAAY,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;gBAClD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC1C,CAAC,CAAC,CAAC;SACN;aAAM;YACH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;SACzC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAEO,kBAAkB;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;QAEhC,UAAU;QACV,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAE1D,IAAI,EAAE,EAAE;YACJ,EAAE,CAAC,QAAQ,EAAE,CAAC;SACjB;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,IAAI,CAAC,WAAW,KAAK,mBAAmB,CAAC,uBAAuB,EAAE;YAClE,IAAI,CAAC,WAAW,GAAG,mBAAmB,CAAC,uBAAuB,CAAC;SAClE;IACL,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;QACpC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAES,WAAW;QACjB,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,IAA0C;QAC9D,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YAChB,IAAI,CAAC,IAAI,CAAC,CAAC;YACX,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,mBAAmB,CAAC,GAAG,EAAE;gBAC5B,IAAI,CAAC,IAAI,CAAC,CAAC;YACf,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;QAEhC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,OAAO,EAAE,CAAC;SAC9C;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,OAAO,KAAK,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;YACnG,OAAO,IAAI,CAAC;SACf;QAED,MAAM,OAAO,GAAG;YACZ,MAAM,EAAE,YAAY;YACpB,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe;YAC/C,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc;YAC7C,QAAQ,EAAE,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;SAC5E,CAAC;QAEF,IAAI,IAAI,CAAC,cAAc,KAAK,OAAO,EAAE;YACjC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;YAE9B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE;gBACrJ,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC;gBAC3B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBAEvC,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;oBAE/C,IAAI,IAAI,CAAC,QAAQ,EAAE;wBACf,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC;qBACvC;iBACJ;gBAED,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACrC,CAAC,CAAC,CAAC;SACN;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,OAAO,EAAE,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACtB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,QAAa;QAC5B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;IAED;;;OAGG;IAEH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED,gBAAgB;IACT,aAAa;QAChB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACtD,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;aACjC;YACD,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,EAAE;YAC/B,uBAAuB;YACvB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,iBAAiB,EAAE;YAC7C,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,IAAiB,EAAE,eAAwB;QACrD,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACjE,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAE1B,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1F,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC;QAElC,oBAAoB;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;IAC5C,CAAC;IAEO,aAAa,CAAC,WAAmB;QACrC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACpC;IACL,CAAC;IAED;;;;;OAKG;IACI,UAAU,CAAC,IAAY,EAAE,OAAgB;QAC5C,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACrC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7B;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;QAE/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,IAAY,EAAE,KAAa;QACvC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE3B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,IAAY,EAAE,KAAa;QACrC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAEzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY,EAAE,KAAe;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAEjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY,EAAE,KAAa;QACxC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE5B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY,EAAE,KAAa;QACxC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE5B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,UAAU,CAAC,IAAY,EAAE,KAAc;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,UAAU,CAAC,IAAY,EAAE,KAAc;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY,EAAE,KAAa;QACxC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAE7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,6DAA6D;IACtD,MAAM,CAAC,oBAA8B;QACxC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;QAEhC,SAAS;QACT,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACvC,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACxD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,UAAU;YACV,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;gBAC/B,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;aACpE;YAED,QAAQ;YACR,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;gBAC3B,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;aAC5D;YAED,QAAQ;YACR,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC7B,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;aAChE;YAED,SAAS;YACT,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;gBACnC,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;aACtE;YAED,SAAS;YACT,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAC9B,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;aAClE;YAED,SAAS;YACT,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAClC,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;aACjF;YAED,UAAU;YACV,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;gBAC/B,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;aACpE;YAED,UAAU;YACV,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;gBAC/B,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;aACpE;YAED,SAAS;YACT,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;gBAC/B,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;aACnE;SACJ;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpC,OAAO;SACV;QAED,MAAM,CAAC,eAAe,EAAE,CAAC,qCAAqC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;QAE9E,MAAM,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC;QACxC,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE;gBACjC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;gBAE1E,OAAO;gBACP,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,CAAC;gBAEtF,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAEjD,QAAQ;gBACR,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;iBACtD;gBAED,aAAa;gBACb,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAC5D;SACJ;aAAM;YACH,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAEvE,OAAO;YACP,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,CAAC;YAEtF,QAAQ;YACR,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACtD;YAED,aAAa;YACb,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC5D;QAED,8BAA8B;QAC9B,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACvD,IAAI,QAAQ,EAAE;YACV,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;SAChC;QAED,UAAU;QACV,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACnD;QAED,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;QAE3B,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;QAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,UAAU,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAS,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE7J,eAAe;QACf,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAE9B,uBAAuB;QACvB,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAElD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,MAAM,KAAK,GAAG,KAAK,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAErD,IAAI,KAAK,IAAI,CAAC,EAAE;YACZ,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC7C;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QACpE,IAAI,YAAY,EAAE;YACd,YAAY,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;SACzD;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;YACzE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;QAED,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAE1C,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;CACJ;AAvsBU;IADN,SAAS,EAAE;oDACY;AAMjB;IADN,SAAS,EAAE;oDACY;AAwBjB;IADN,SAAS,EAAE;2DACqB;AAWzB;IADP,SAAS,EAAE;gDACe;AA8S3B;IADC,SAAS,EAAE;oDAGX;AAgXL,aAAa,CAAC,2BAA2B,EAAE,iBAAiB,CAAC,CAAC", "sourcesContent": ["import { serialize } from \"../../../Misc/decorators\";\r\nimport { Observable } from \"../../../Misc/observable\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport type { Scene } from \"../../../scene\";\r\nimport type { Matrix, Vector3, Vector2 } from \"../../../Maths/math.vector\";\r\nimport type { Color4, Color3 } from \"../../../Maths/math.color\";\r\nimport type { Engine } from \"../../../Engines/engine\";\r\nimport { VertexBuffer } from \"../../../Buffers/buffer\";\r\nimport { SceneComponentConstants } from \"../../../sceneComponent\";\r\n\r\nimport { Material } from \"../../../Materials/material\";\r\nimport type { Effect } from \"../../../Materials/effect\";\r\nimport { Texture } from \"../../../Materials/Textures/texture\";\r\nimport type { RenderTargetTextureOptions } from \"../../../Materials/Textures/renderTargetTexture\";\r\nimport { RenderTargetTexture } from \"../../../Materials/Textures/renderTargetTexture\";\r\nimport { ProceduralTextureSceneComponent } from \"./proceduralTextureSceneComponent\";\r\n\r\nimport \"../../../Engines/Extensions/engine.renderTarget\";\r\nimport \"../../../Engines/Extensions/engine.renderTargetCube\";\r\nimport \"../../../Shaders/procedural.vertex\";\r\nimport type { DataBuffer } from \"../../../Buffers/dataBuffer\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport type { NodeMaterial } from \"../../Node/nodeMaterial\";\r\nimport type { TextureSize } from \"../../../Materials/Textures/textureCreationOptions\";\r\nimport { EngineStore } from \"../../../Engines/engineStore\";\r\nimport { Constants } from \"../../../Engines/constants\";\r\nimport { DrawWrapper } from \"../../drawWrapper\";\r\nimport type { RenderTargetWrapper } from \"../../../Engines/renderTargetWrapper\";\r\n\r\n/**\r\n * Options to create a procedural texture\r\n */\r\nexport interface IProceduralTextureCreationOptions extends RenderTargetTextureOptions {\r\n    /**\r\n     * Defines a fallback texture in case there were issues to create the custom texture\r\n     */\r\n    fallbackTexture?: Nullable<Texture>;\r\n}\r\n\r\n/**\r\n * Procedural texturing is a way to programmatically create a texture. There are 2 types of procedural textures: code-only, and code that references some classic 2D images, sometimes calmpler' images.\r\n * This is the base class of any Procedural texture and contains most of the shareable code.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/proceduralTextures\r\n */\r\nexport class ProceduralTexture extends Texture {\r\n    /**\r\n     * Define if the texture is enabled or not (disabled texture will not render)\r\n     */\r\n    @serialize()\r\n    public isEnabled = true;\r\n\r\n    /**\r\n     * Define if the texture must be cleared before rendering (default is true)\r\n     */\r\n    @serialize()\r\n    public autoClear = true;\r\n\r\n    /**\r\n     * Callback called when the texture is generated\r\n     */\r\n    public onGenerated: () => void;\r\n\r\n    /**\r\n     * Event raised when the texture is generated\r\n     */\r\n    public onGeneratedObservable = new Observable<ProceduralTexture>();\r\n\r\n    /**\r\n     * Event raised before the texture is generated\r\n     */\r\n    public onBeforeGenerationObservable = new Observable<ProceduralTexture>();\r\n\r\n    /**\r\n     * Gets or sets the node material used to create this texture (null if the texture was manually created)\r\n     */\r\n    public nodeMaterialSource: Nullable<NodeMaterial> = null;\r\n\r\n    /** @internal */\r\n    @serialize()\r\n    public _generateMipMaps: boolean;\r\n\r\n    private _drawWrapper: DrawWrapper;\r\n\r\n    /** @internal */\r\n    public _textures: { [key: string]: Texture } = {};\r\n\r\n    /** @internal */\r\n    protected _fallbackTexture: Nullable<Texture>;\r\n\r\n    @serialize()\r\n    private _size: TextureSize;\r\n    private _textureType: number;\r\n    private _currentRefreshId = -1;\r\n    private _frameId = -1;\r\n    private _refreshRate = 1;\r\n    private _vertexBuffers: { [key: string]: Nullable<VertexBuffer> } = {};\r\n    private _indexBuffer: Nullable<DataBuffer>;\r\n    private _uniforms = new Array<string>();\r\n    private _samplers = new Array<string>();\r\n    private _fragment: any;\r\n\r\n    private _floats: { [key: string]: number } = {};\r\n    private _ints: { [key: string]: number } = {};\r\n    private _floatsArrays: { [key: string]: number[] } = {};\r\n    private _colors3: { [key: string]: Color3 } = {};\r\n    private _colors4: { [key: string]: Color4 } = {};\r\n    private _vectors2: { [key: string]: Vector2 } = {};\r\n    private _vectors3: { [key: string]: Vector3 } = {};\r\n    private _matrices: { [key: string]: Matrix } = {};\r\n\r\n    private _fallbackTextureUsed = false;\r\n    private _fullEngine: Engine;\r\n\r\n    private _cachedDefines: Nullable<string> = null;\r\n\r\n    private _contentUpdateId = -1;\r\n    private _contentData: Nullable<Promise<ArrayBufferView>>;\r\n\r\n    private _rtWrapper: Nullable<RenderTargetWrapper> = null;\r\n    private _options: IProceduralTextureCreationOptions;\r\n\r\n    /**\r\n     * Instantiates a new procedural texture.\r\n     * Procedural texturing is a way to programmatically create a texture. There are 2 types of procedural textures: code-only, and code that references some classic 2D images, sometimes called 'refMaps' or 'sampler' images.\r\n     * This is the base class of any Procedural texture and contains most of the shareable code.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/proceduralTextures\r\n     * @param name  Define the name of the texture\r\n     * @param size Define the size of the texture to create\r\n     * @param fragment Define the fragment shader to use to generate the texture or null if it is defined later:\r\n     *  * object: \\{ fragmentElement: \"fragmentShaderCode\" \\}, used with shader code in script tags\r\n     *  * object: \\{ fragmentSource: \"fragment shader code string\" \\}, the string contains the shader code\r\n     *  * string: the string contains a name \"XXX\" to lookup in Effect.ShadersStore[\"XXXFragmentShader\"]\r\n     * @param scene Define the scene the texture belongs to\r\n     * @param fallbackTexture Define a fallback texture in case there were issues to create the custom texture\r\n     * @param generateMipMaps Define if the texture should creates mip maps or not\r\n     * @param isCube Define if the texture is a cube texture or not (this will render each faces of the cube)\r\n     * @param textureType The FBO internal texture type\r\n     */\r\n    constructor(\r\n        name: string,\r\n        size: TextureSize,\r\n        fragment: any,\r\n        scene: Nullable<Scene>,\r\n        fallbackTexture: Nullable<Texture> | IProceduralTextureCreationOptions = null,\r\n        generateMipMaps = true,\r\n        isCube = false,\r\n        textureType = Constants.TEXTURETYPE_UNSIGNED_INT\r\n    ) {\r\n        super(null, scene, !generateMipMaps);\r\n\r\n        if (fallbackTexture !== null && !(fallbackTexture instanceof Texture)) {\r\n            this._options = fallbackTexture;\r\n            this._fallbackTexture = fallbackTexture.fallbackTexture ?? null;\r\n        } else {\r\n            this._options = {};\r\n            this._fallbackTexture = fallbackTexture;\r\n        }\r\n\r\n        scene = this.getScene() || EngineStore.LastCreatedScene!;\r\n        let component = scene._getComponent(SceneComponentConstants.NAME_PROCEDURALTEXTURE);\r\n        if (!component) {\r\n            component = new ProceduralTextureSceneComponent(scene);\r\n            scene._addComponent(component);\r\n        }\r\n        scene.proceduralTextures.push(this);\r\n\r\n        this._fullEngine = scene.getEngine();\r\n\r\n        this.name = name;\r\n        this.isRenderTarget = true;\r\n        this._size = size;\r\n        this._textureType = textureType;\r\n        this._generateMipMaps = generateMipMaps;\r\n        this._drawWrapper = new DrawWrapper(this._fullEngine);\r\n\r\n        this.setFragment(fragment);\r\n\r\n        const rtWrapper = this._createRtWrapper(isCube, size, generateMipMaps, textureType);\r\n        this._texture = rtWrapper.texture;\r\n\r\n        // VBO\r\n        const vertices = [];\r\n        vertices.push(1, 1);\r\n        vertices.push(-1, 1);\r\n        vertices.push(-1, -1);\r\n        vertices.push(1, -1);\r\n\r\n        this._vertexBuffers[VertexBuffer.PositionKind] = new VertexBuffer(this._fullEngine, vertices, VertexBuffer.PositionKind, false, false, 2);\r\n\r\n        this._createIndexBuffer();\r\n    }\r\n\r\n    private _createRtWrapper(isCube: boolean, size: TextureSize, generateMipMaps: boolean, textureType: number) {\r\n        if (isCube) {\r\n            this._rtWrapper = this._fullEngine.createRenderTargetCubeTexture(size as number, {\r\n                generateMipMaps: generateMipMaps,\r\n                generateDepthBuffer: false,\r\n                generateStencilBuffer: false,\r\n                type: textureType,\r\n                ...this._options,\r\n            });\r\n            this.setFloat(\"face\", 0);\r\n        } else {\r\n            this._rtWrapper = this._fullEngine.createRenderTargetTexture(size, {\r\n                generateMipMaps: generateMipMaps,\r\n                generateDepthBuffer: false,\r\n                generateStencilBuffer: false,\r\n                type: textureType,\r\n                ...this._options,\r\n            });\r\n        }\r\n        return this._rtWrapper;\r\n    }\r\n\r\n    /**\r\n     * The effect that is created when initializing the post process.\r\n     * @returns The created effect corresponding the postprocess.\r\n     */\r\n    public getEffect(): Effect {\r\n        return this._drawWrapper.effect!;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setEffect(effect: Effect) {\r\n        this._drawWrapper.effect = effect;\r\n    }\r\n\r\n    /**\r\n     * Gets texture content (Use this function wisely as reading from a texture can be slow)\r\n     * @returns an ArrayBufferView promise (Uint8Array or Float32Array)\r\n     */\r\n    public getContent(): Nullable<Promise<ArrayBufferView>> {\r\n        if (this._contentData && this._frameId === this._contentUpdateId) {\r\n            return this._contentData;\r\n        }\r\n\r\n        if (this._contentData) {\r\n            this._contentData.then((buffer) => {\r\n                this._contentData = this.readPixels(0, 0, buffer);\r\n                this._contentUpdateId = this._frameId;\r\n            });\r\n        } else {\r\n            this._contentData = this.readPixels(0, 0);\r\n            this._contentUpdateId = this._frameId;\r\n        }\r\n\r\n        return this._contentData;\r\n    }\r\n\r\n    private _createIndexBuffer(): void {\r\n        const engine = this._fullEngine;\r\n\r\n        // Indices\r\n        const indices = [];\r\n        indices.push(0);\r\n        indices.push(1);\r\n        indices.push(2);\r\n\r\n        indices.push(0);\r\n        indices.push(2);\r\n        indices.push(3);\r\n\r\n        this._indexBuffer = engine.createIndexBuffer(indices);\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuild(): void {\r\n        const vb = this._vertexBuffers[VertexBuffer.PositionKind];\r\n\r\n        if (vb) {\r\n            vb._rebuild();\r\n        }\r\n\r\n        this._createIndexBuffer();\r\n\r\n        if (this.refreshRate === RenderTargetTexture.REFRESHRATE_RENDER_ONCE) {\r\n            this.refreshRate = RenderTargetTexture.REFRESHRATE_RENDER_ONCE;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Resets the texture in order to recreate its associated resources.\r\n     * This can be called in case of context loss or if you change the shader code and need to regenerate the texture with the new code\r\n     */\r\n    public reset(): void {\r\n        this._drawWrapper.effect?.dispose();\r\n        this._drawWrapper.effect = null;\r\n        this._cachedDefines = null;\r\n    }\r\n\r\n    protected _getDefines(): string {\r\n        return \"\";\r\n    }\r\n\r\n    /**\r\n     * Executes a function when the texture will be ready to be drawn.\r\n     * @param func The callback to be used.\r\n     */\r\n    public executeWhenReady(func: (texture: ProceduralTexture) => void): void {\r\n        if (this.isReady()) {\r\n            func(this);\r\n            return;\r\n        }\r\n\r\n        const effect = this.getEffect();\r\n        if (effect) {\r\n            effect.executeWhenCompiled(() => {\r\n                func(this);\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Is the texture ready to be used ? (rendered at least once)\r\n     * @returns true if ready, otherwise, false.\r\n     */\r\n    public isReady(): boolean {\r\n        const engine = this._fullEngine;\r\n\r\n        if (this.nodeMaterialSource) {\r\n            return this._drawWrapper.effect!.isReady();\r\n        }\r\n\r\n        if (!this._fragment) {\r\n            return false;\r\n        }\r\n\r\n        if (this._fallbackTextureUsed) {\r\n            return true;\r\n        }\r\n\r\n        if (!this._texture) {\r\n            return false;\r\n        }\r\n\r\n        const defines = this._getDefines();\r\n        if (this._drawWrapper.effect && defines === this._cachedDefines && this._drawWrapper.effect.isReady()) {\r\n            return true;\r\n        }\r\n\r\n        const shaders = {\r\n            vertex: \"procedural\",\r\n            fragmentElement: this._fragment.fragmentElement,\r\n            fragmentSource: this._fragment.fragmentSource,\r\n            fragment: typeof this._fragment === \"string\" ? this._fragment : undefined,\r\n        };\r\n\r\n        if (this._cachedDefines !== defines) {\r\n            this._cachedDefines = defines;\r\n\r\n            this._drawWrapper.effect = engine.createEffect(shaders, [VertexBuffer.PositionKind], this._uniforms, this._samplers, defines, undefined, undefined, () => {\r\n                this._rtWrapper?.dispose();\r\n                this._rtWrapper = this._texture = null;\r\n\r\n                if (this._fallbackTexture) {\r\n                    this._texture = this._fallbackTexture._texture;\r\n\r\n                    if (this._texture) {\r\n                        this._texture.incrementReferences();\r\n                    }\r\n                }\r\n\r\n                this._fallbackTextureUsed = true;\r\n            });\r\n        }\r\n\r\n        return this._drawWrapper.effect!.isReady();\r\n    }\r\n\r\n    /**\r\n     * Resets the refresh counter of the texture and start bak from scratch.\r\n     * Could be useful to regenerate the texture if it is setup to render only once.\r\n     */\r\n    public resetRefreshCounter(): void {\r\n        this._currentRefreshId = -1;\r\n    }\r\n\r\n    /**\r\n     * Set the fragment shader to use in order to render the texture.\r\n     * @param fragment This can be set to a path (into the shader store) or to a json object containing a fragmentElement property.\r\n     */\r\n    public setFragment(fragment: any) {\r\n        this._fragment = fragment;\r\n    }\r\n\r\n    /**\r\n     * Define the refresh rate of the texture or the rendering frequency.\r\n     * Use 0 to render just once, 1 to render on every frame, 2 to render every two frames and so on...\r\n     */\r\n    @serialize()\r\n    public get refreshRate(): number {\r\n        return this._refreshRate;\r\n    }\r\n\r\n    public set refreshRate(value: number) {\r\n        this._refreshRate = value;\r\n        this.resetRefreshCounter();\r\n    }\r\n\r\n    /** @internal */\r\n    public _shouldRender(): boolean {\r\n        if (!this.isEnabled || !this.isReady() || !this._texture) {\r\n            if (this._texture) {\r\n                this._texture.isReady = false;\r\n            }\r\n            return false;\r\n        }\r\n\r\n        if (this._fallbackTextureUsed) {\r\n            return false;\r\n        }\r\n\r\n        if (this._currentRefreshId === -1) {\r\n            // At least render once\r\n            this._currentRefreshId = 1;\r\n            this._frameId++;\r\n            return true;\r\n        }\r\n\r\n        if (this.refreshRate === this._currentRefreshId) {\r\n            this._currentRefreshId = 1;\r\n            this._frameId++;\r\n            return true;\r\n        }\r\n\r\n        this._currentRefreshId++;\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Get the size the texture is rendering at.\r\n     * @returns the size (on cube texture it is always squared)\r\n     */\r\n    public getRenderSize(): TextureSize {\r\n        return this._size;\r\n    }\r\n\r\n    /**\r\n     * Resize the texture to new value.\r\n     * @param size Define the new size the texture should have\r\n     * @param generateMipMaps Define whether the new texture should create mip maps\r\n     */\r\n    public resize(size: TextureSize, generateMipMaps: boolean): void {\r\n        if (this._fallbackTextureUsed || !this._rtWrapper || !this._texture) {\r\n            return;\r\n        }\r\n\r\n        const isCube = this._texture.isCube;\r\n        this._rtWrapper.dispose();\r\n\r\n        const rtWrapper = this._createRtWrapper(isCube, size, generateMipMaps, this._textureType);\r\n        this._texture = rtWrapper.texture;\r\n\r\n        // Update properties\r\n        this._size = size;\r\n        this._generateMipMaps = generateMipMaps;\r\n    }\r\n\r\n    private _checkUniform(uniformName: string): void {\r\n        if (this._uniforms.indexOf(uniformName) === -1) {\r\n            this._uniforms.push(uniformName);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set a texture in the shader program used to render.\r\n     * @param name Define the name of the uniform samplers as defined in the shader\r\n     * @param texture Define the texture to bind to this sampler\r\n     * @returns the texture itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setTexture(name: string, texture: Texture): ProceduralTexture {\r\n        if (this._samplers.indexOf(name) === -1) {\r\n            this._samplers.push(name);\r\n        }\r\n        this._textures[name] = texture;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a float in the shader.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the texture itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setFloat(name: string, value: number): ProceduralTexture {\r\n        this._checkUniform(name);\r\n        this._floats[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a int in the shader.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the texture itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setInt(name: string, value: number): ProceduralTexture {\r\n        this._checkUniform(name);\r\n        this._ints[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set an array of floats in the shader.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the texture itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setFloats(name: string, value: number[]): ProceduralTexture {\r\n        this._checkUniform(name);\r\n        this._floatsArrays[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec3 in the shader from a Color3.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the texture itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setColor3(name: string, value: Color3): ProceduralTexture {\r\n        this._checkUniform(name);\r\n        this._colors3[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec4 in the shader from a Color4.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the texture itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setColor4(name: string, value: Color4): ProceduralTexture {\r\n        this._checkUniform(name);\r\n        this._colors4[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec2 in the shader from a Vector2.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the texture itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setVector2(name: string, value: Vector2): ProceduralTexture {\r\n        this._checkUniform(name);\r\n        this._vectors2[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a vec3 in the shader from a Vector3.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the texture itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setVector3(name: string, value: Vector3): ProceduralTexture {\r\n        this._checkUniform(name);\r\n        this._vectors3[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set a mat4 in the shader from a MAtrix.\r\n     * @param name Define the name of the uniform as defined in the shader\r\n     * @param value Define the value to give to the uniform\r\n     * @returns the texture itself allowing \"fluent\" like uniform updates\r\n     */\r\n    public setMatrix(name: string, value: Matrix): ProceduralTexture {\r\n        this._checkUniform(name);\r\n        this._matrices[name] = value;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Render the texture to its associated render target.\r\n     * @param useCameraPostProcess Define if camera post process should be applied to the texture\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public render(useCameraPostProcess?: boolean): void {\r\n        const scene = this.getScene();\r\n\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        const engine = this._fullEngine;\r\n\r\n        // Render\r\n        engine.enableEffect(this._drawWrapper);\r\n        this.onBeforeGenerationObservable.notifyObservers(this);\r\n        engine.setState(false);\r\n\r\n        if (!this.nodeMaterialSource) {\r\n            // Texture\r\n            for (const name in this._textures) {\r\n                this._drawWrapper.effect!.setTexture(name, this._textures[name]);\r\n            }\r\n\r\n            // Float\r\n            for (const name in this._ints) {\r\n                this._drawWrapper.effect!.setInt(name, this._ints[name]);\r\n            }\r\n\r\n            // Float\r\n            for (const name in this._floats) {\r\n                this._drawWrapper.effect!.setFloat(name, this._floats[name]);\r\n            }\r\n\r\n            // Floats\r\n            for (const name in this._floatsArrays) {\r\n                this._drawWrapper.effect!.setArray(name, this._floatsArrays[name]);\r\n            }\r\n\r\n            // Color3\r\n            for (const name in this._colors3) {\r\n                this._drawWrapper.effect!.setColor3(name, this._colors3[name]);\r\n            }\r\n\r\n            // Color4\r\n            for (const name in this._colors4) {\r\n                const color = this._colors4[name];\r\n                this._drawWrapper.effect!.setFloat4(name, color.r, color.g, color.b, color.a);\r\n            }\r\n\r\n            // Vector2\r\n            for (const name in this._vectors2) {\r\n                this._drawWrapper.effect!.setVector2(name, this._vectors2[name]);\r\n            }\r\n\r\n            // Vector3\r\n            for (const name in this._vectors3) {\r\n                this._drawWrapper.effect!.setVector3(name, this._vectors3[name]);\r\n            }\r\n\r\n            // Matrix\r\n            for (const name in this._matrices) {\r\n                this._drawWrapper.effect!.setMatrix(name, this._matrices[name]);\r\n            }\r\n        }\r\n\r\n        if (!this._texture || !this._rtWrapper) {\r\n            return;\r\n        }\r\n\r\n        engine._debugPushGroup?.(`procedural texture generation for ${this.name}`, 1);\r\n\r\n        const viewPort = engine.currentViewport;\r\n        if (this.isCube) {\r\n            for (let face = 0; face < 6; face++) {\r\n                engine.bindFramebuffer(this._rtWrapper, face, undefined, undefined, true);\r\n\r\n                // VBOs\r\n                engine.bindBuffers(this._vertexBuffers, this._indexBuffer, this._drawWrapper.effect!);\r\n\r\n                this._drawWrapper.effect!.setFloat(\"face\", face);\r\n\r\n                // Clear\r\n                if (this.autoClear) {\r\n                    engine.clear(scene.clearColor, true, false, false);\r\n                }\r\n\r\n                // Draw order\r\n                engine.drawElementsType(Material.TriangleFillMode, 0, 6);\r\n            }\r\n        } else {\r\n            engine.bindFramebuffer(this._rtWrapper, 0, undefined, undefined, true);\r\n\r\n            // VBOs\r\n            engine.bindBuffers(this._vertexBuffers, this._indexBuffer, this._drawWrapper.effect!);\r\n\r\n            // Clear\r\n            if (this.autoClear) {\r\n                engine.clear(scene.clearColor, true, false, false);\r\n            }\r\n\r\n            // Draw order\r\n            engine.drawElementsType(Material.TriangleFillMode, 0, 6);\r\n        }\r\n\r\n        // Unbind and restore viewport\r\n        engine.unBindFramebuffer(this._rtWrapper, this.isCube);\r\n        if (viewPort) {\r\n            engine.setViewport(viewPort);\r\n        }\r\n\r\n        // Mipmaps\r\n        if (this.isCube) {\r\n            engine.generateMipMapsForCubemap(this._texture);\r\n        }\r\n\r\n        engine._debugPopGroup?.(1);\r\n\r\n        if (this.onGenerated) {\r\n            this.onGenerated();\r\n        }\r\n\r\n        this.onGeneratedObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Clone the texture.\r\n     * @returns the cloned texture\r\n     */\r\n    public clone(): ProceduralTexture {\r\n        const textureSize = this.getSize();\r\n        const newTexture = new ProceduralTexture(this.name, textureSize.width, this._fragment, <Scene>this.getScene(), this._fallbackTexture, this._generateMipMaps);\r\n\r\n        // Base texture\r\n        newTexture.hasAlpha = this.hasAlpha;\r\n        newTexture.level = this.level;\r\n\r\n        // RenderTarget Texture\r\n        newTexture.coordinatesMode = this.coordinatesMode;\r\n\r\n        return newTexture;\r\n    }\r\n\r\n    /**\r\n     * Dispose the texture and release its associated resources.\r\n     */\r\n    public dispose(): void {\r\n        const scene = this.getScene();\r\n\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        const index = scene.proceduralTextures.indexOf(this);\r\n\r\n        if (index >= 0) {\r\n            scene.proceduralTextures.splice(index, 1);\r\n        }\r\n\r\n        const vertexBuffer = this._vertexBuffers[VertexBuffer.PositionKind];\r\n        if (vertexBuffer) {\r\n            vertexBuffer.dispose();\r\n            this._vertexBuffers[VertexBuffer.PositionKind] = null;\r\n        }\r\n\r\n        if (this._indexBuffer && this._fullEngine._releaseBuffer(this._indexBuffer)) {\r\n            this._indexBuffer = null;\r\n        }\r\n\r\n        this.onGeneratedObservable.clear();\r\n        this.onBeforeGenerationObservable.clear();\r\n\r\n        super.dispose();\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ProceduralTexture\", ProceduralTexture);\r\n"]}