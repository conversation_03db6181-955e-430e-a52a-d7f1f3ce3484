{"version": 3, "file": "reflectionTextureBaseBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Materials/Node/Blocks/Dual/reflectionTextureBaseBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAKhF,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAIlD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAE1D,OAAO,uDAAuD,CAAC;AAC/D,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAC;AACpD,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAGtG;;GAEG;AACH,MAAM,OAAgB,0BAA2B,SAAQ,iBAAiB;IA6CtE;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,OAA8B;QAC7C,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;YAC3B,OAAO;SACV;QAED,MAAM,KAAK,GAAG,OAAO,EAAE,QAAQ,EAAE,IAAI,WAAW,CAAC,gBAAgB,CAAC;QAElE,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE;YACnB,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,QAAS,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,OAAO,IAAI,KAAK,EAAE;YAClB,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAQS,MAAM,CAAC,kCAAkC,CAAC,KAAwB,EAAE,aAAqB;QAC/F,MAAM,IAAI,GAAG,KAAmC,CAAC;QACjD,OAAO,IAAI,CAAC,kCAAkC,EAAE,CAAC;IACrD,CAAC;IAES,kCAAkC;QACxC,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,UAAU;QAChB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;IAC3I,CAAC;IAED;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QAzB5D,0JAA0J;QAInJ,6BAAwB,GAAG,KAAK,CAAC;IAsBxC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,4BAA4B,CAAC;IACxC,CAAC;IAgCS,WAAW;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,EAAE,CAAC,IAAI;QACnH,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC5B,IAAI,aAAa,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAEnI,IAAI,CAAC,aAAa,EAAE;gBAChB,aAAa,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;gBAC3C,aAAa,CAAC,cAAc,EAAE,CAAC;aAClC;YACD,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACjD;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YACzB,IAAI,UAAU,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,wBAAwB,CAAC,KAAK,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE1I,IAAI,CAAC,UAAU,EAAE;gBACb,UAAU,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;gBACrC,UAAU,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;aAC/D;YACD,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC3C;QAED,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrC,IAAI,SAAS,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,wBAAwB,CAAC,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAExI,IAAI,CAAC,SAAS,EAAE;gBACZ,SAAS,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;gBACnC,SAAS,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;aAC7D;YACD,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzC;IACL,CAAC;IAEM,cAAc,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B;QAC9F,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAC5B,OAAO;SACV;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YACvC,OAAO;SACV;QAED,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC3D,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,EAAQ,OAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAClG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAC9G,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QAC1G,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,kBAAkB,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACvK,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACtG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;QAChH,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QAC1G,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;QAClH,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,4BAA4B,EAAE,IAAI,CAAC,CAAC;QAC5H,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,EAAE,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,kCAAkC,EAAE,IAAI,CAAC,CAAC;QACvI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,uCAAuC,EAAE,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,2CAA2C,EAAE,IAAI,CAAC,CAAC;IAC5J,CAAC;IAEM,OAAO;QACV,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE;YAC5C,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAE,QAAkB;QACnF,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;YACnB,OAAO;SACV;QAED,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC,0BAA0B,EAAE,CAAC,CAAC;QAEnF,IAAI,OAAO,CAAC,MAAM,EAAE;YAChB,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;SACrD;aAAM;YACH,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;SACnD;QAED,IAAU,OAAQ,CAAC,eAAe,EAAE;YAChC,MAAM,WAAW,GAAgB,OAAO,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE,WAAW,CAAC,mBAAmB,CAAC,CAAC;YACjF,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;SAC5E;IACL,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,KAA6B;QACjD,IAAI,IAAI,CAAC,wBAAwB,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,MAAM,EAAE;YACnF,OAAO,EAAE,CAAC;SACb;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;QAClE,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAC;QACxE,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;QAChF,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;QAC1E,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;QAClF,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,CAAC;QAC9E,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC,kBAAkB,CAAC,+BAA+B,CAAC,CAAC;QAC5F,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,kBAAkB,CAAC,+BAA+B,CAAC,CAAC;QACvF,IAAI,CAAC,uCAAuC,GAAG,KAAK,CAAC,kBAAkB,CAAC,6CAA6C,CAAC,CAAC;QACvH,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC,kBAAkB,CAAC,qCAAqC,CAAC,CAAC;QACvG,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;QAC1E,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;QAE5E,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAE5E,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;QAEjE,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,IAAI,CAAC,oCAAoC,GAAG,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAExF,MAAM,mBAAmB,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC;QACzJ,IAAI,IAAI,CAAC,wBAAwB,IAAI,KAAK,CAAC,sBAAsB,CAAC,mBAAmB,EAAE,MAAM,CAAC,EAAE;YAC5F,IAAI,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,mBAAmB,MAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,KAAK,CAAC;SACrI;QAED,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAClE,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAEhE,IAAI,IAAI,CAAC,wBAAwB,IAAI,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,EAAE;YACtH,IAAI,IAAI,UAAU,IAAI,CAAC,iBAAiB,IAAI,CAAC;YAC7C,IAAI,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,gBAAgB,MAAM,IAAI,CAAC,QAAQ,CAAC,sBAAsB,SAAS,CAAC;YACnI,IAAI,IAAI,UAAU,CAAC;SACtB;QAED,IACI,IAAI,CAAC,wBAAwB;YAC7B,KAAK,CAAC,sBAAsB,CACxB,IAAI,CAAC,eAAe,EACpB,MAAM,EACN,WAAW,IAAI,CAAC,+BAA+B,gBAAgB,IAAI,CAAC,uCAAuC,GAAG,CACjH,EACH;YACE,IAAI,IAAI,eAAe,IAAI,CAAC,+BAA+B,gBAAgB,IAAI,CAAC,uCAAuC,KAAK,CAAC;YAC7H,IAAI,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,eAAe,qBAAqB,IAAI,CAAC,KAAK,CAAC,sBAAsB,WAChI,IAAI,CAAC,QAAQ,CAAC,sBAClB,iBAAiB,CAAC;YAClB,IAAI,IAAI,UAAU,CAAC;SACtB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,KAA6B;QACxD,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE1C,WAAW;QACX,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC;QAC9E,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE3C,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC;QAC1E,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEzC,KAAK,CAAC,mBAAmB,IAAI,UAAU,IAAI,CAAC,aAAa,IAAI,CAAC;QAC9D,KAAK,CAAC,mBAAmB,IAAI,uBAAuB,IAAI,CAAC,gBAAgB,KAAK,CAAC;QAC/E,KAAK,CAAC,mBAAmB,IAAI,SAAS,CAAC;QACvC,KAAK,CAAC,mBAAmB,IAAI,qBAAqB,IAAI,CAAC,cAAc,KAAK,CAAC;QAC3E,KAAK,CAAC,mBAAmB,IAAI,UAAU,CAAC;QAExC,WAAW;QACX,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAC5D,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,QAAQ,EAAE;YAC3D,cAAc,EAAE,CAAC,EAAE,MAAM,EAAE,+BAA+B,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC;SAC3F,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAC1E,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QACzE,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAE5E,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;QACjF,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC;QAEnE,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;QAC7E,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;IACnE,CAAC;IAED;;;;;;;OAOG;IACI,sCAAsC,CAAC,kBAA0B,EAAE,QAAiB,EAAE,oBAAoB,GAAG,KAAK,EAAE,gBAAgB,GAAG,KAAK;QAC/I,IAAI,CAAC,QAAQ,EAAE;YACX,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC;SAC3I;QACD,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACpD,MAAM,SAAS,GAAG,aAAa,IAAI,CAAC,eAAe,GAAG,CAAC;QACvD,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC/C,MAAM,YAAY,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC;QACrE,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEnD,kBAAkB,IAAI,MAAM,CAAC;QAE7B,IAAI,IAAI,GAAG;qBACE,IAAI,CAAC,uCAAuC;uBAC1C,IAAI,CAAC,qBAAqB,gDAAgD,QAAQ,KAAK,kBAAkB,KAAK,SAAS;;;qBAGzH,IAAI,CAAC,+BAA+B;uBAClC,IAAI,CAAC,qBAAqB,wCAAwC,QAAQ,KAAK,kBAAkB,KAAK,SAAS;;;qBAGjH,IAAI,CAAC,0BAA0B;uBAC7B,IAAI,CAAC,qBAAqB,mCAAmC,QAAQ,KAAK,kBAAkB,KAAK,YAAY,SAAS,gBAAgB;;;qBAGxI,IAAI,CAAC,oBAAoB;uBACvB,IAAI,CAAC,qBAAqB,6BAA6B,QAAQ,KAAK,kBAAkB,KAAK,IAAI,KAAK,gBAAgB;;;qBAGtH,IAAI,CAAC,iBAAiB;uBACpB,IAAI,CAAC,qBAAqB,0BAA0B,QAAQ,KAAK,kBAAkB,KAAK,YAAY,SAAS,gBAAgB;;;qBAG/H,IAAI,CAAC,gBAAgB;yBACjB,IAAI,CAAC,qBAAqB;2BACxB,IAAI,CAAC,qBAAqB,8BAA8B,QAAQ,KAAK,kBAAkB,KAAK,YAAY,SAAS,gBAAgB,KAAK,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC,uBAAuB;;uBAEnM,IAAI,CAAC,qBAAqB,yBAAyB,QAAQ,KAAK,kBAAkB,KAAK,YAAY,SAAS,gBAAgB;;;;qBAI9H,IAAI,CAAC,qBAAqB;uBACxB,IAAI,CAAC,qBAAqB,8BAA8B,QAAQ,KAAK,IAAI,KAAK,gBAAgB;;;qBAGhG,IAAI,CAAC,iBAAiB;uBACpB,IAAI,CAAC,qBAAqB,0BAA0B,WAAW,KAAK,gBAAgB;;;qBAGtF,IAAI,CAAC,mBAAmB;uBACtB,IAAI,CAAC,qBAAqB;qBAC5B,CAAC;QAEd,IAAI,CAAC,gBAAgB,EAAE;YACnB,IAAI,IAAI,UAAU,IAAI,CAAC,gBAAgB;kBACjC,IAAI,CAAC,qBAAqB;qBACvB,CAAC;SACb;QAED,IAAI,CAAC,oBAAoB,EAAE;YACvB,IAAI,IAAI;yBACK,IAAI,CAAC,aAAa;2BAChB,IAAI,CAAC,qBAAqB,MAAM,IAAI,CAAC,qBAAqB;;2BAE1D,IAAI,CAAC,qBAAqB,MAAM,IAAI,CAAC,qBAAqB;6BACxD,IAAI,CAAC,qBAAqB;0BAC7B,IAAI,CAAC,qBAAqB,OAAO,IAAI,CAAC,qBAAqB;;sBAE/D,IAAI,CAAC,qBAAqB,cAAc,IAAI,CAAC,qBAAqB;yBAC/D,CAAC;SACjB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,qCAAqC,CAAC,UAAmB,EAAE,oBAAoB,GAAG,MAAM;QAC3F,MAAM,SAAS,GAAG,KAAK,GAAG,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEtG,IAAI,IAAI,GAAG,GAAG,SAAS,IAAI,IAAI,CAAC,oBAAoB;qBACvC,IAAI,CAAC,aAAa,IAAI,CAAC;QAEpC,IAAI,UAAU,EAAE;YACZ,IAAI,IAAI,GAAG,IAAI,CAAC,oBAAoB,wBAAwB,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,qBAAqB,KAAK,UAAU,IAAI,oBAAoB,KAAK,CAAC;SAChK;aAAM;YACH,IAAI,IAAI,GAAG,IAAI,CAAC,oBAAoB,kBAAkB,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,qBAAqB,IAAI,oBAAoB,KAAK,CAAC;SAC3I;QAED,IAAI,IAAI;oBACI,CAAC;QAEb,IAAI,UAAU,EAAE;YACZ,IAAI,IAAI,GAAG,IAAI,CAAC,oBAAoB,sBAAsB,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,qBAAqB,KAAK,UAAU,IAAI,oBAAoB,KAAK,CAAC;SAC5J;aAAM;YACH,IAAI,IAAI,GAAG,IAAI,CAAC,oBAAoB,gBAAgB,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,qBAAqB,IAAI,oBAAoB,KAAK,CAAC;SACvI;QAED,IAAI,IAAI,UAAU,CAAC;QAEnB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,KAA6B,EAAE,OAAe;QAC9D,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,IAAI,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,QAAQ,EAAE;YACpD,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAChC,IAAI,MAAM,CAAC,YAAY,EAAE;oBACrB,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC;iBAClF;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,mBAAmB;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO,UAAU,CAAC;SACrB;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACrB,MAAM,eAAe,GAAI,IAAI,CAAC,OAAuB,CAAC,eAAe,CAAC;YACtE,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,uCAAuC,IAAI,CAAC,OAAO,CAAC,IAAI,4BAC3F,IAAI,CAAC,OAAO,CAAC,QACjB,4CAA4C,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,eAAe,CAAC,CAAC,CAAC,GAAG,GAAG,eAAe,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,CAAC;SAC1I;aAAM;YACH,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,mCAAmC,IAAI,CAAC,OAAO,CAAC,IAAI,aAAa,CAAC;SAC5G;QACD,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,8BAA8B,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,CAAC;QAEvG,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC9C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;SAC1D;QAED,mBAAmB,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAE7E,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,mBAAmB,CAAC,OAAO,IAAI,CAAC,YAAY,CAAC,wBAAwB,EAAE;YACvE,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YAChF,IAAI,mBAAmB,CAAC,OAAO,CAAC,MAAM,EAAE;gBACpC,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;aACjF;iBAAM;gBACH,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;aAC7E;SACJ;QAED,IAAI,CAAC,wBAAwB,GAAG,mBAAmB,CAAC,wBAAwB,CAAC;QAE7E,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;CACJ;AArcU;IAHN,sBAAsB,CAAC,6BAA6B,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE;QAC/F,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,0BAA0B,CAAC,kCAAkC,EAAE;KAC1H,CAAC;4EACsC;AAuc5C,aAAa,CAAC,oCAAoC,EAAE,0BAA0B,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { BaseTexture } from \"../../../Textures/baseTexture\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport type { NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { NodeMaterial } from \"../../nodeMaterial\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { Constants } from \"../../../../Engines/constants\";\r\n\r\nimport \"../../../../Shaders/ShadersInclude/reflectionFunction\";\r\nimport { CubeTexture } from \"../../../Textures/cubeTexture\";\r\nimport { Texture } from \"../../../Textures/texture\";\r\nimport { EngineStore } from \"../../../../Engines/engineStore\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport type { SubMesh } from \"../../../..//Meshes/subMesh\";\r\n\r\n/**\r\n * Base block used to read a reflection texture from a sampler\r\n */\r\nexport abstract class ReflectionTextureBaseBlock extends NodeMaterialBlock {\r\n    /** @internal */\r\n    public _define3DName: string;\r\n    /** @internal */\r\n    public _defineCubicName: string;\r\n    /** @internal */\r\n    public _defineExplicitName: string;\r\n    /** @internal */\r\n    public _defineProjectionName: string;\r\n    /** @internal */\r\n    public _defineLocalCubicName: string;\r\n    /** @internal */\r\n    public _defineSphericalName: string;\r\n    /** @internal */\r\n    public _definePlanarName: string;\r\n    /** @internal */\r\n    public _defineEquirectangularName: string;\r\n    /** @internal */\r\n    public _defineMirroredEquirectangularFixedName: string;\r\n    /** @internal */\r\n    public _defineEquirectangularFixedName: string;\r\n    /** @internal */\r\n    public _defineSkyboxName: string;\r\n    /** @internal */\r\n    public _defineOppositeZ: string;\r\n    /** @internal */\r\n    public _cubeSamplerName: string;\r\n    /** @internal */\r\n    public _2DSamplerName: string;\r\n    /** @internal */\r\n    public _reflectionPositionName: string;\r\n    /** @internal */\r\n    public _reflectionSizeName: string;\r\n\r\n    protected _positionUVWName: string;\r\n    protected _directionWName: string;\r\n    protected _reflectionVectorName: string;\r\n    /** @internal */\r\n    public _reflectionCoordsName: string;\r\n    /** @internal */\r\n    public _reflectionMatrixName: string;\r\n    protected _reflectionColorName: string;\r\n    protected _worldPositionNameInFragmentOnlyMode: string;\r\n\r\n    protected _texture: Nullable<BaseTexture>;\r\n    /**\r\n     * Gets or sets the texture associated with the node\r\n     */\r\n    public get texture(): Nullable<BaseTexture> {\r\n        return this._texture;\r\n    }\r\n\r\n    public set texture(texture: Nullable<BaseTexture>) {\r\n        if (this._texture === texture) {\r\n            return;\r\n        }\r\n\r\n        const scene = texture?.getScene() ?? EngineStore.LastCreatedScene;\r\n\r\n        if (!texture && scene) {\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this._texture!);\r\n            });\r\n        }\r\n\r\n        this._texture = texture;\r\n\r\n        if (texture && scene) {\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(texture);\r\n            });\r\n        }\r\n    }\r\n\r\n    /** Indicates that no code should be generated in the vertex shader. Can be useful in some specific circumstances (like when doing ray marching for eg) */\r\n    @editableInPropertyPage(\"Generate only fragment code\", PropertyTypeForEdition.Boolean, \"ADVANCED\", {\r\n        notifiers: { rebuild: true, update: true, onValidation: ReflectionTextureBaseBlock._OnGenerateOnlyFragmentCodeChanged },\r\n    })\r\n    public generateOnlyFragmentCode = false;\r\n\r\n    protected static _OnGenerateOnlyFragmentCodeChanged(block: NodeMaterialBlock, _propertyName: string): boolean {\r\n        const that = block as ReflectionTextureBaseBlock;\r\n        return that._onGenerateOnlyFragmentCodeChanged();\r\n    }\r\n\r\n    protected _onGenerateOnlyFragmentCodeChanged(): boolean {\r\n        this._setTarget();\r\n        return true;\r\n    }\r\n\r\n    protected _setTarget(): void {\r\n        this._setInitialTarget(this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.VertexAndFragment);\r\n    }\r\n\r\n    /**\r\n     * Create a new ReflectionTextureBaseBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.VertexAndFragment);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"ReflectionTextureBaseBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public abstract get position(): NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public abstract get worldPosition(): NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public abstract get worldNormal(): NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * Gets the world input component\r\n     */\r\n    public abstract get world(): NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * Gets the camera (or eye) position component\r\n     */\r\n    public abstract get cameraPosition(): NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * Gets the view input component\r\n     */\r\n    public abstract get view(): NodeMaterialConnectionPoint;\r\n\r\n    protected _getTexture(): Nullable<BaseTexture> {\r\n        return this.texture;\r\n    }\r\n\r\n    /**\r\n     * Auto configure the node based on the existing material\r\n     * @param material defines the material to configure\r\n     * @param additionalFilteringInfo defines additional info to be used when filtering inputs (we might want to skip some non relevant blocks)\r\n     */\r\n    public autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.position.isConnected) {\r\n            let positionInput = material.getInputBlockByPredicate((b) => b.isAttribute && b.name === \"position\" && additionalFilteringInfo(b));\r\n\r\n            if (!positionInput) {\r\n                positionInput = new InputBlock(\"position\");\r\n                positionInput.setAsAttribute();\r\n            }\r\n            positionInput.output.connectTo(this.position);\r\n        }\r\n\r\n        if (!this.world.isConnected) {\r\n            let worldInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.World && additionalFilteringInfo(b));\r\n\r\n            if (!worldInput) {\r\n                worldInput = new InputBlock(\"world\");\r\n                worldInput.setAsSystemValue(NodeMaterialSystemValues.World);\r\n            }\r\n            worldInput.output.connectTo(this.world);\r\n        }\r\n\r\n        if (this.view && !this.view.isConnected) {\r\n            let viewInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.View && additionalFilteringInfo(b));\r\n\r\n            if (!viewInput) {\r\n                viewInput = new InputBlock(\"view\");\r\n                viewInput.setAsSystemValue(NodeMaterialSystemValues.View);\r\n            }\r\n            viewInput.output.connectTo(this.view);\r\n        }\r\n    }\r\n\r\n    public prepareDefines(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        if (!defines._areTexturesDirty) {\r\n            return;\r\n        }\r\n\r\n        const texture = this._getTexture();\r\n\r\n        if (!texture || !texture.getTextureMatrix) {\r\n            return;\r\n        }\r\n\r\n        defines.setValue(this._define3DName, texture.isCube, true);\r\n        defines.setValue(this._defineLocalCubicName, (<any>texture).boundingBoxSize ? true : false, true);\r\n        defines.setValue(this._defineExplicitName, texture.coordinatesMode === Constants.TEXTURE_EXPLICIT_MODE, true);\r\n        defines.setValue(this._defineSkyboxName, texture.coordinatesMode === Constants.TEXTURE_SKYBOX_MODE, true);\r\n        defines.setValue(this._defineCubicName, texture.coordinatesMode === Constants.TEXTURE_CUBIC_MODE || texture.coordinatesMode === Constants.TEXTURE_INVCUBIC_MODE, true);\r\n        defines.setValue(\"INVERTCUBICMAP\", texture.coordinatesMode === Constants.TEXTURE_INVCUBIC_MODE, true);\r\n        defines.setValue(this._defineSphericalName, texture.coordinatesMode === Constants.TEXTURE_SPHERICAL_MODE, true);\r\n        defines.setValue(this._definePlanarName, texture.coordinatesMode === Constants.TEXTURE_PLANAR_MODE, true);\r\n        defines.setValue(this._defineProjectionName, texture.coordinatesMode === Constants.TEXTURE_PROJECTION_MODE, true);\r\n        defines.setValue(this._defineEquirectangularName, texture.coordinatesMode === Constants.TEXTURE_EQUIRECTANGULAR_MODE, true);\r\n        defines.setValue(this._defineEquirectangularFixedName, texture.coordinatesMode === Constants.TEXTURE_FIXED_EQUIRECTANGULAR_MODE, true);\r\n        defines.setValue(this._defineMirroredEquirectangularFixedName, texture.coordinatesMode === Constants.TEXTURE_FIXED_EQUIRECTANGULAR_MIRRORED_MODE, true);\r\n    }\r\n\r\n    public isReady() {\r\n        const texture = this._getTexture();\r\n\r\n        if (texture && !texture.isReadyOrNotBlocking()) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh, _subMesh?: SubMesh) {\r\n        const texture = this._getTexture();\r\n\r\n        if (!mesh || !texture) {\r\n            return;\r\n        }\r\n\r\n        effect.setMatrix(this._reflectionMatrixName, texture.getReflectionTextureMatrix());\r\n\r\n        if (texture.isCube) {\r\n            effect.setTexture(this._cubeSamplerName, texture);\r\n        } else {\r\n            effect.setTexture(this._2DSamplerName, texture);\r\n        }\r\n\r\n        if ((<any>texture).boundingBoxSize) {\r\n            const cubeTexture = <CubeTexture>texture;\r\n            effect.setVector3(this._reflectionPositionName, cubeTexture.boundingBoxPosition);\r\n            effect.setVector3(this._reflectionSizeName, cubeTexture.boundingBoxSize);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the code to inject in the vertex shader\r\n     * @param state current state of the node material building\r\n     * @returns the shader code\r\n     */\r\n    public handleVertexSide(state: NodeMaterialBuildState): string {\r\n        if (this.generateOnlyFragmentCode && state.target === NodeMaterialBlockTargets.Vertex) {\r\n            return \"\";\r\n        }\r\n\r\n        this._define3DName = state._getFreeDefineName(\"REFLECTIONMAP_3D\");\r\n        this._defineCubicName = state._getFreeDefineName(\"REFLECTIONMAP_CUBIC\");\r\n        this._defineSphericalName = state._getFreeDefineName(\"REFLECTIONMAP_SPHERICAL\");\r\n        this._definePlanarName = state._getFreeDefineName(\"REFLECTIONMAP_PLANAR\");\r\n        this._defineProjectionName = state._getFreeDefineName(\"REFLECTIONMAP_PROJECTION\");\r\n        this._defineExplicitName = state._getFreeDefineName(\"REFLECTIONMAP_EXPLICIT\");\r\n        this._defineEquirectangularName = state._getFreeDefineName(\"REFLECTIONMAP_EQUIRECTANGULAR\");\r\n        this._defineLocalCubicName = state._getFreeDefineName(\"USE_LOCAL_REFLECTIONMAP_CUBIC\");\r\n        this._defineMirroredEquirectangularFixedName = state._getFreeDefineName(\"REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED\");\r\n        this._defineEquirectangularFixedName = state._getFreeDefineName(\"REFLECTIONMAP_EQUIRECTANGULAR_FIXED\");\r\n        this._defineSkyboxName = state._getFreeDefineName(\"REFLECTIONMAP_SKYBOX\");\r\n        this._defineOppositeZ = state._getFreeDefineName(\"REFLECTIONMAP_OPPOSITEZ\");\r\n\r\n        this._reflectionMatrixName = state._getFreeVariableName(\"reflectionMatrix\");\r\n\r\n        state._emitUniformFromString(this._reflectionMatrixName, \"mat4\");\r\n\r\n        let code = \"\";\r\n\r\n        this._worldPositionNameInFragmentOnlyMode = state._getFreeVariableName(\"worldPosition\");\r\n\r\n        const worldPosVaryingName = this.generateOnlyFragmentCode ? this._worldPositionNameInFragmentOnlyMode : \"v_\" + this.worldPosition.associatedVariableName;\r\n        if (this.generateOnlyFragmentCode || state._emitVaryingFromString(worldPosVaryingName, \"vec4\")) {\r\n            code += `${this.generateOnlyFragmentCode ? \"vec4 \" : \"\"}${worldPosVaryingName} = ${this.worldPosition.associatedVariableName};\\n`;\r\n        }\r\n\r\n        this._positionUVWName = state._getFreeVariableName(\"positionUVW\");\r\n        this._directionWName = state._getFreeVariableName(\"directionW\");\r\n\r\n        if (this.generateOnlyFragmentCode || state._emitVaryingFromString(this._positionUVWName, \"vec3\", this._defineSkyboxName)) {\r\n            code += `#ifdef ${this._defineSkyboxName}\\n`;\r\n            code += `${this.generateOnlyFragmentCode ? \"vec3 \" : \"\"}${this._positionUVWName} = ${this.position.associatedVariableName}.xyz;\\n`;\r\n            code += `#endif\\n`;\r\n        }\r\n\r\n        if (\r\n            this.generateOnlyFragmentCode ||\r\n            state._emitVaryingFromString(\r\n                this._directionWName,\r\n                \"vec3\",\r\n                `defined(${this._defineEquirectangularFixedName}) || defined(${this._defineMirroredEquirectangularFixedName})`\r\n            )\r\n        ) {\r\n            code += `#if defined(${this._defineEquirectangularFixedName}) || defined(${this._defineMirroredEquirectangularFixedName})\\n`;\r\n            code += `${this.generateOnlyFragmentCode ? \"vec3 \" : \"\"}${this._directionWName} = normalize(vec3(${this.world.associatedVariableName} * vec4(${\r\n                this.position.associatedVariableName\r\n            }.xyz, 0.0)));\\n`;\r\n            code += `#endif\\n`;\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * Handles the inits for the fragment code path\r\n     * @param state node material build state\r\n     */\r\n    public handleFragmentSideInits(state: NodeMaterialBuildState) {\r\n        state.sharedData.blockingBlocks.push(this);\r\n        state.sharedData.textureBlocks.push(this);\r\n\r\n        // Samplers\r\n        this._cubeSamplerName = state._getFreeVariableName(this.name + \"CubeSampler\");\r\n        state.samplers.push(this._cubeSamplerName);\r\n\r\n        this._2DSamplerName = state._getFreeVariableName(this.name + \"2DSampler\");\r\n        state.samplers.push(this._2DSamplerName);\r\n\r\n        state._samplerDeclaration += `#ifdef ${this._define3DName}\\n`;\r\n        state._samplerDeclaration += `uniform samplerCube ${this._cubeSamplerName};\\n`;\r\n        state._samplerDeclaration += `#else\\n`;\r\n        state._samplerDeclaration += `uniform sampler2D ${this._2DSamplerName};\\n`;\r\n        state._samplerDeclaration += `#endif\\n`;\r\n\r\n        // Fragment\r\n        state.sharedData.blocksWithDefines.push(this);\r\n        state.sharedData.bindableBlocks.push(this);\r\n\r\n        const comments = `//${this.name}`;\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n        state._emitFunctionFromInclude(\"reflectionFunction\", comments, {\r\n            replaceStrings: [{ search: /vec3 computeReflectionCoords/g, replace: \"void DUMMYFUNC\" }],\r\n        });\r\n\r\n        this._reflectionColorName = state._getFreeVariableName(\"reflectionColor\");\r\n        this._reflectionVectorName = state._getFreeVariableName(\"reflectionUVW\");\r\n        this._reflectionCoordsName = state._getFreeVariableName(\"reflectionCoords\");\r\n\r\n        this._reflectionPositionName = state._getFreeVariableName(\"vReflectionPosition\");\r\n        state._emitUniformFromString(this._reflectionPositionName, \"vec3\");\r\n\r\n        this._reflectionSizeName = state._getFreeVariableName(\"vReflectionPosition\");\r\n        state._emitUniformFromString(this._reflectionSizeName, \"vec3\");\r\n    }\r\n\r\n    /**\r\n     * Generates the reflection coords code for the fragment code path\r\n     * @param worldNormalVarName name of the world normal variable\r\n     * @param worldPos name of the world position variable. If not provided, will use the world position connected to this block\r\n     * @param onlyReflectionVector if true, generates code only for the reflection vector computation, not for the reflection coordinates\r\n     * @param doNotEmitInvertZ if true, does not emit the invertZ code\r\n     * @returns the shader code\r\n     */\r\n    public handleFragmentSideCodeReflectionCoords(worldNormalVarName: string, worldPos?: string, onlyReflectionVector = false, doNotEmitInvertZ = false): string {\r\n        if (!worldPos) {\r\n            worldPos = this.generateOnlyFragmentCode ? this._worldPositionNameInFragmentOnlyMode : `v_${this.worldPosition.associatedVariableName}`;\r\n        }\r\n        const reflectionMatrix = this._reflectionMatrixName;\r\n        const direction = `normalize(${this._directionWName})`;\r\n        const positionUVW = `${this._positionUVWName}`;\r\n        const vEyePosition = `${this.cameraPosition.associatedVariableName}`;\r\n        const view = `${this.view.associatedVariableName}`;\r\n\r\n        worldNormalVarName += \".xyz\";\r\n\r\n        let code = `\r\n            #ifdef ${this._defineMirroredEquirectangularFixedName}\r\n                vec3 ${this._reflectionVectorName} = computeMirroredFixedEquirectangularCoords(${worldPos}, ${worldNormalVarName}, ${direction});\r\n            #endif\r\n\r\n            #ifdef ${this._defineEquirectangularFixedName}\r\n                vec3 ${this._reflectionVectorName} = computeFixedEquirectangularCoords(${worldPos}, ${worldNormalVarName}, ${direction});\r\n            #endif\r\n\r\n            #ifdef ${this._defineEquirectangularName}\r\n                vec3 ${this._reflectionVectorName} = computeEquirectangularCoords(${worldPos}, ${worldNormalVarName}, ${vEyePosition}.xyz, ${reflectionMatrix});\r\n            #endif\r\n\r\n            #ifdef ${this._defineSphericalName}\r\n                vec3 ${this._reflectionVectorName} = computeSphericalCoords(${worldPos}, ${worldNormalVarName}, ${view}, ${reflectionMatrix});\r\n            #endif\r\n\r\n            #ifdef ${this._definePlanarName}\r\n                vec3 ${this._reflectionVectorName} = computePlanarCoords(${worldPos}, ${worldNormalVarName}, ${vEyePosition}.xyz, ${reflectionMatrix});\r\n            #endif\r\n\r\n            #ifdef ${this._defineCubicName}\r\n                #ifdef ${this._defineLocalCubicName}\r\n                    vec3 ${this._reflectionVectorName} = computeCubicLocalCoords(${worldPos}, ${worldNormalVarName}, ${vEyePosition}.xyz, ${reflectionMatrix}, ${this._reflectionSizeName}, ${this._reflectionPositionName});\r\n                #else\r\n                vec3 ${this._reflectionVectorName} = computeCubicCoords(${worldPos}, ${worldNormalVarName}, ${vEyePosition}.xyz, ${reflectionMatrix});\r\n                #endif\r\n            #endif\r\n\r\n            #ifdef ${this._defineProjectionName}\r\n                vec3 ${this._reflectionVectorName} = computeProjectionCoords(${worldPos}, ${view}, ${reflectionMatrix});\r\n            #endif\r\n\r\n            #ifdef ${this._defineSkyboxName}\r\n                vec3 ${this._reflectionVectorName} = computeSkyBoxCoords(${positionUVW}, ${reflectionMatrix});\r\n            #endif\r\n\r\n            #ifdef ${this._defineExplicitName}\r\n                vec3 ${this._reflectionVectorName} = vec3(0, 0, 0);\r\n            #endif\\n`;\r\n\r\n        if (!doNotEmitInvertZ) {\r\n            code += `#ifdef ${this._defineOppositeZ}\r\n                ${this._reflectionVectorName}.z *= -1.0;\r\n            #endif\\n`;\r\n        }\r\n\r\n        if (!onlyReflectionVector) {\r\n            code += `\r\n                #ifdef ${this._define3DName}\r\n                    vec3 ${this._reflectionCoordsName} = ${this._reflectionVectorName};\r\n                #else\r\n                    vec2 ${this._reflectionCoordsName} = ${this._reflectionVectorName}.xy;\r\n                    #ifdef ${this._defineProjectionName}\r\n                        ${this._reflectionCoordsName} /= ${this._reflectionVectorName}.z;\r\n                    #endif\r\n                    ${this._reflectionCoordsName}.y = 1.0 - ${this._reflectionCoordsName}.y;\r\n                #endif\\n`;\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * Generates the reflection color code for the fragment code path\r\n     * @param lodVarName name of the lod variable\r\n     * @param swizzleLookupTexture swizzle to use for the final color variable\r\n     * @returns the shader code\r\n     */\r\n    public handleFragmentSideCodeReflectionColor(lodVarName?: string, swizzleLookupTexture = \".rgb\"): string {\r\n        const colorType = \"vec\" + (swizzleLookupTexture.length === 0 ? \"4\" : swizzleLookupTexture.length - 1);\r\n\r\n        let code = `${colorType} ${this._reflectionColorName};\r\n            #ifdef ${this._define3DName}\\n`;\r\n\r\n        if (lodVarName) {\r\n            code += `${this._reflectionColorName} = textureCubeLodEXT(${this._cubeSamplerName}, ${this._reflectionVectorName}, ${lodVarName})${swizzleLookupTexture};\\n`;\r\n        } else {\r\n            code += `${this._reflectionColorName} = textureCube(${this._cubeSamplerName}, ${this._reflectionVectorName})${swizzleLookupTexture};\\n`;\r\n        }\r\n\r\n        code += `\r\n            #else\\n`;\r\n\r\n        if (lodVarName) {\r\n            code += `${this._reflectionColorName} = texture2DLodEXT(${this._2DSamplerName}, ${this._reflectionCoordsName}, ${lodVarName})${swizzleLookupTexture};\\n`;\r\n        } else {\r\n            code += `${this._reflectionColorName} = texture2D(${this._2DSamplerName}, ${this._reflectionCoordsName})${swizzleLookupTexture};\\n`;\r\n        }\r\n\r\n        code += `#endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * Generates the code corresponding to the connected output points\r\n     * @param state node material build state\r\n     * @param varName name of the variable to output\r\n     * @returns the shader code\r\n     */\r\n    public writeOutputs(state: NodeMaterialBuildState, varName: string): string {\r\n        let code = \"\";\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n            for (const output of this._outputs) {\r\n                if (output.hasEndpoints) {\r\n                    code += `${this._declareOutput(output, state)} = ${varName}.${output.name};\\n`;\r\n                }\r\n            }\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n        return this;\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        if (!this.texture) {\r\n            return codeString;\r\n        }\r\n\r\n        if (this.texture.isCube) {\r\n            const forcedExtension = (this.texture as CubeTexture).forcedExtension;\r\n            codeString += `${this._codeVariableName}.texture = new BABYLON.CubeTexture(\"${this.texture.name}\", undefined, undefined, ${\r\n                this.texture.noMipmap\r\n            }, null, undefined, undefined, undefined, ${this.texture._prefiltered}, ${forcedExtension ? '\"' + forcedExtension + '\"' : \"null\"});\\n`;\r\n        } else {\r\n            codeString += `${this._codeVariableName}.texture = new BABYLON.Texture(\"${this.texture.name}\", null);\\n`;\r\n        }\r\n        codeString += `${this._codeVariableName}.texture.coordinatesMode = ${this.texture.coordinatesMode};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        if (this.texture && !this.texture.isRenderTarget) {\r\n            serializationObject.texture = this.texture.serialize();\r\n        }\r\n\r\n        serializationObject.generateOnlyFragmentCode = this.generateOnlyFragmentCode;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        if (serializationObject.texture && !NodeMaterial.IgnoreTexturesAtLoadTime) {\r\n            rootUrl = serializationObject.texture.url.indexOf(\"data:\") === 0 ? \"\" : rootUrl;\r\n            if (serializationObject.texture.isCube) {\r\n                this.texture = CubeTexture.Parse(serializationObject.texture, scene, rootUrl);\r\n            } else {\r\n                this.texture = Texture.Parse(serializationObject.texture, scene, rootUrl);\r\n            }\r\n        }\r\n\r\n        this.generateOnlyFragmentCode = serializationObject.generateOnlyFragmentCode;\r\n\r\n        this._setTarget();\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ReflectionTextureBaseBlock\", ReflectionTextureBaseBlock);\r\n"]}