import type { Observer } from "../Misc/observable";
import { Observable } from "../Misc/observable";
import type { Nullable } from "../types";
import type { PointerInfo } from "../Events/pointerEvents";
import { Vector3 } from "../Maths/math.vector";
import type { AbstractMesh } from "../Meshes/abstractMesh";
import type { Node } from "../node";
import { Mesh } from "../Meshes/mesh";
import { StandardMaterial } from "../Materials/standardMaterial";
import { PointerDragBehavior } from "../Behaviors/Meshes/pointerDragBehavior";
import type { IGizmo } from "./gizmo";
import { Gizmo } from "./gizmo";
import { UtilityLayerRenderer } from "../Rendering/utilityLayerRenderer";
import type { ScaleGizmo } from "./scaleGizmo";
import { Color3 } from "../Maths/math.color";
/**
 * Interface for axis scale gizmo
 */
export interface IAxisScaleGizmo extends IGizmo {
    /** Drag behavior responsible for the gizmos dragging interactions */
    dragBehavior: PointerDragBehavior;
    /** Drag distance in babylon units that the gizmo will snap to when dragged */
    snapDistance: number;
    /** Incremental snap scaling. When true, with a snapDistance of 0.1, scaling will be 1.1,1.2,1.3 instead of, when false: 1.1,1.21,1.33,... */
    incrementalSnap: boolean;
    /**
     * Event that fires each time the gizmo snaps to a new location.
     * * snapDistance is the change in distance
     */
    onSnapObservable: Observable<{
        snapDistance: number;
    }>;
    /** If the scaling operation should be done on all axis */
    uniformScaling: boolean;
    /** Custom sensitivity value for the drag strength */
    sensitivity: number;
    /** The magnitude of the drag strength (scaling factor) */
    dragScale: number;
    /** If the gizmo is enabled */
    isEnabled: boolean;
    /** Default material used to render when gizmo is not disabled or hovered */
    coloredMaterial: StandardMaterial;
    /** Material used to render when gizmo is hovered with mouse*/
    hoverMaterial: StandardMaterial;
    /** Material used to render when gizmo is disabled. typically grey.*/
    disableMaterial: StandardMaterial;
}
/**
 * Single axis scale gizmo
 */
export declare class AxisScaleGizmo extends Gizmo implements IAxisScaleGizmo {
    /**
     * Drag behavior responsible for the gizmos dragging interactions
     */
    dragBehavior: PointerDragBehavior;
    protected _pointerObserver: Nullable<Observer<PointerInfo>>;
    /**
     * Scale distance in babylon units that the gizmo will snap to when dragged (Default: 0)
     */
    snapDistance: number;
    /**
     * Event that fires each time the gizmo snaps to a new location.
     * * snapDistance is the change in distance
     */
    onSnapObservable: Observable<{
        snapDistance: number;
    }>;
    /**
     * If the scaling operation should be done on all axis (default: false)
     */
    uniformScaling: boolean;
    /**
     * Custom sensitivity value for the drag strength
     */
    sensitivity: number;
    /**
     * The magnitude of the drag strength (scaling factor)
     */
    dragScale: number;
    /**
     * The minimal absolute scale per component. can be positive or negative but never smaller.
     */
    static MinimumAbsoluteScale: number;
    /**
     * Incremental snap scaling (default is false). When true, with a snapDistance of 0.1, scaling will be 1.1,1.2,1.3 instead of, when false: 1.1,1.21,1.33,...
     */
    incrementalSnap: boolean;
    protected _isEnabled: boolean;
    protected _parent: Nullable<ScaleGizmo>;
    protected _gizmoMesh: Mesh;
    protected _coloredMaterial: StandardMaterial;
    protected _hoverMaterial: StandardMaterial;
    protected _disableMaterial: StandardMaterial;
    protected _dragging: boolean;
    private _tmpVector;
    private _incrementalStartupValue;
    /** Default material used to render when gizmo is not disabled or hovered */
    get coloredMaterial(): StandardMaterial;
    /** Material used to render when gizmo is hovered with mouse*/
    get hoverMaterial(): StandardMaterial;
    /** Material used to render when gizmo is disabled. typically grey.*/
    get disableMaterial(): StandardMaterial;
    /**
     * Creates an AxisScaleGizmo
     * @param dragAxis The axis which the gizmo will be able to scale on
     * @param color The color of the gizmo
     * @param gizmoLayer The utility layer the gizmo will be added to
     * @param parent
     * @param thickness display gizmo axis thickness
     * @param hoverColor The color of the gizmo when hovering over and dragging
     * @param disableColor The Color of the gizmo when its disabled
     */
    constructor(dragAxis: Vector3, color?: Color3, gizmoLayer?: UtilityLayerRenderer, parent?: Nullable<ScaleGizmo>, thickness?: number, hoverColor?: Color3, disableColor?: Color3);
    /**
     * @internal
     * Create Geometry for Gizmo
     * @param parentMesh
     * @param thickness
     * @param isCollider
     * @returns the gizmo mesh
     */
    protected _createGizmoMesh(parentMesh: AbstractMesh, thickness: number, isCollider?: boolean): {
        arrowMesh: Mesh;
        arrowTail: Mesh;
    };
    protected _attachedNodeChanged(value: Nullable<Node>): void;
    /**
     * If the gizmo is enabled
     */
    set isEnabled(value: boolean);
    get isEnabled(): boolean;
    /**
     * Disposes of the gizmo
     */
    dispose(): void;
    /**
     * Disposes and replaces the current meshes in the gizmo with the specified mesh
     * @param mesh The mesh to replace the default mesh of the gizmo
     * @param useGizmoMaterial If the gizmo's default material should be used (default: false)
     */
    setCustomMesh(mesh: Mesh, useGizmoMaterial?: boolean): void;
}
