{"version": 3, "file": "worleyNoise3DBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/worleyNoise3DBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAGvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAExD,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,mCAAmC,CAAC;AAEnG;;GAEG;AAEH,kDAAkD;AAClD,8BAA8B;AAC9B,EAAE;AACF,mBAAmB;AACnB,2DAA2D;AAE3D,MAAM,OAAO,kBAAmB,SAAQ,iBAAiB;IAKrD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QATlD,iFAAiF;QAE1E,sBAAiB,GAAG,KAAK,CAAC;QAQ7B,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC1E,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAE1E,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;IAC1E,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,CAAC;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACxB,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE;YAC3E,OAAO;SACV;QAED,IAAI,cAAc,GAAG,yBAAyB,CAAC;QAC/C,cAAc,IAAI,gDAAgD,CAAC;QACnE,cAAc,IAAI,OAAO,CAAC;QAE1B,cAAc,IAAI,+DAA+D,CAAC;QAClF,cAAc,IAAI,wFAAwF,CAAC;QAC3G,cAAc,IAAI,OAAO,CAAC;QAE1B,cAAc,IAAI,8DAA8D,CAAC;QACjF,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,6CAA6C,CAAC;QAChE,cAAc,IAAI,iDAAiD,CAAC;QACpE,cAAc,IAAI,yCAAyC,CAAC;QAC5D,cAAc,IAAI,gDAAgD,CAAC;QACnE,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,uCAAuC,CAAC;QAC1D,cAAc,IAAI,iCAAiC,CAAC;QACpD,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,+CAA+C,CAAC;QAClE,cAAc,IAAI,+CAA+C,CAAC;QAClE,cAAc,IAAI,+CAA+C,CAAC;QAClE,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sDAAsD,CAAC;QACzE,cAAc,IAAI,0CAA0C,CAAC;QAC7D,cAAc,IAAI,oCAAoC,CAAC;QACvD,cAAc,IAAI,0CAA0C,CAAC;QAC7D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,4CAA4C,CAAC;QAC/D,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,4CAA4C,CAAC;QAC/D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,4CAA4C,CAAC;QAC/D,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,4CAA4C,CAAC;QAC/D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,4CAA4C,CAAC;QAC/D,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,4CAA4C,CAAC;QAC/D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,kDAAkD,CAAC;QACrE,cAAc,IAAI,mEAAmE,CAAC;QACtF,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,kDAAkD,CAAC;QACrE,cAAc,IAAI,2CAA2C,CAAC;QAC9D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,kDAAkD,CAAC;QACrE,cAAc,IAAI,2CAA2C,CAAC;QAC9D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,kDAAkD,CAAC;QACrE,cAAc,IAAI,2CAA2C,CAAC;QAC9D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,kDAAkD,CAAC;QACrE,cAAc,IAAI,2CAA2C,CAAC;QAC9D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,kDAAkD,CAAC;QACrE,cAAc,IAAI,2CAA2C,CAAC;QAC9D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,kDAAkD,CAAC;QACrE,cAAc,IAAI,2CAA2C,CAAC;QAC9D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,kDAAkD,CAAC;QACrE,cAAc,IAAI,2CAA2C,CAAC;QAC9D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,kDAAkD,CAAC;QACrE,cAAc,IAAI,2CAA2C,CAAC;QAC9D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,wCAAwC,CAAC;QAC3D,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,6DAA6D,CAAC;QAChF,cAAc,IAAI,4DAA4D,CAAC;QAC/E,cAAc,IAAI,6DAA6D,CAAC;QAChF,cAAc,IAAI,6DAA6D,CAAC;QAChF,cAAc,IAAI,6DAA6D,CAAC;QAChF,cAAc,IAAI,6DAA6D,CAAC;QAChF,cAAc,IAAI,6DAA6D,CAAC;QAChF,cAAc,IAAI,6DAA6D,CAAC;QAChF,cAAc,IAAI,6DAA6D,CAAC;QAChF,cAAc,IAAI,IAAI,CAAC;QACvB,cAAc,IAAI,iCAAiC,CAAC;QACpD,cAAc,IAAI,4BAA4B,CAAC;QAC/C,cAAc,IAAI,8DAA8D,CAAC;QACjF,cAAc,IAAI,4BAA4B,CAAC;QAC/C,cAAc,IAAI,2DAA2D,CAAC;QAC9E,cAAc,IAAI,iCAAiC,CAAC;QACpD,cAAc,IAAI,4BAA4B,CAAC;QAC/C,cAAc,IAAI,8DAA8D,CAAC;QACjF,cAAc,IAAI,4BAA4B,CAAC;QAC/C,cAAc,IAAI,2DAA2D,CAAC;QAC9E,cAAc,IAAI,iCAAiC,CAAC;QACpD,cAAc,IAAI,4BAA4B,CAAC;QAC/C,cAAc,IAAI,8DAA8D,CAAC;QACjF,cAAc,IAAI,4BAA4B,CAAC;QAC/C,cAAc,IAAI,2DAA2D,CAAC;QAC9E,cAAc,IAAI,gCAAgC,CAAC;QACnD,cAAc,IAAI,4BAA4B,CAAC;QAC/C,cAAc,IAAI,kDAAkD,CAAC;QACrE,cAAc,IAAI,0DAA0D,CAAC;QAC7E,cAAc,IAAI,mDAAmD,CAAC;QACtE,cAAc,IAAI,yEAAyE,CAAC;QAC5F,cAAc,IAAI,2DAA2D,CAAC;QAC9E,cAAc,IAAI,0CAA0C,CAAC;QAC7D,cAAc,IAAI,0CAA0C,CAAC;QAC7D,cAAc,IAAI,0CAA0C,CAAC;QAC7D,cAAc,IAAI,qDAAqD,CAAC;QACxE,cAAc,IAAI,wDAAwD,CAAC;QAC3E,cAAc,IAAI,kDAAkD,CAAC;QACrE,cAAc,IAAI,sCAAsC,CAAC;QACzD,cAAc,IAAI,OAAO,CAAC;QAE1B,KAAK,CAAC,aAAa,CAAC,UAAU,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;QAE/D,MAAM,YAAY,GAAG,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAE9D,KAAK,CAAC,iBAAiB,IAAI,QAAQ,YAAY,aAAa,IAAI,CAAC,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,MAAM,CAAC,sBAAsB,KAAK,IAAI,CAAC,iBAAiB,MAAM,CAAC;QAErK,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC1B,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,KAAK,CAAC;SAChG;QAED,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE;YACrB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,OAAO,CAAC;SAC7F;QAED,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE;YACrB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,OAAO,CAAC;SAC7F;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IACD;;;OAGG;IACO,mBAAmB;QACzB,MAAM,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,GAAG,GAAG,IAAI,CAAC,iBAAiB,wBAAwB,IAAI,CAAC,iBAAiB,KAAK,CAAC;QAE9H,OAAO,UAAU,CAAC;IACtB,CAAC;IACD;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAE/D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IACD;;;;;OAKG;IACI,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,iBAAiB,GAAG,mBAAmB,CAAC,iBAAiB,CAAC;IACnE,CAAC;CACJ;AAhRU;IADN,sBAAsB,CAAC,wBAAwB,EAAE,sBAAsB,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;6DAChG;AAkRrC,aAAa,CAAC,4BAA4B,EAAE,kBAAkB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport type { Scene } from \"../../../scene\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../Decorators/nodeDecorator\";\r\n\r\n/**\r\n * block used to Generate a Worley Noise 3D Noise Pattern\r\n */\r\n\r\n//  Source: https://github.com/Erkaman/glsl-worley\r\n//  Converted to BJS by Pryme8\r\n//\r\n//  Worley Noise 3D\r\n//  Return vec2 value range of -1.0->1.0, F1-F2 respectivly\r\n\r\nexport class WorleyNoise3DBlock extends NodeMaterialBlock {\r\n    /** Gets or sets a boolean indicating that normal should be inverted on X axis */\r\n    @editableInPropertyPage(\"Use Manhattan Distance\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { notifiers: { update: false } })\r\n    public manhattanDistance = false;\r\n\r\n    /**\r\n     * Creates a new WorleyNoise3DBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n        this.registerInput(\"seed\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n        this.registerInput(\"jitter\", NodeMaterialBlockConnectionPointTypes.Float);\r\n\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerOutput(\"x\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"y\", NodeMaterialBlockConnectionPointTypes.Float);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"WorleyNoise3DBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the seed input component\r\n     */\r\n    public get seed(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the jitter input component\r\n     */\r\n    public get jitter(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the x component\r\n     */\r\n    public get x(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the y component\r\n     */\r\n    public get y(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (!this.seed.isConnected) {\r\n            return;\r\n        }\r\n\r\n        if (!this.output.hasEndpoints && !this.x.hasEndpoints && !this.y.hasEndpoints) {\r\n            return;\r\n        }\r\n\r\n        let functionString = `vec3 permute(vec3 x){\\n`;\r\n        functionString += `    return mod((34.0 * x + 1.0) * x, 289.0);\\n`;\r\n        functionString += `}\\n\\n`;\r\n\r\n        functionString += `vec3 dist(vec3 x, vec3 y, vec3 z,  bool manhattanDistance){\\n`;\r\n        functionString += `    return manhattanDistance ?  abs(x) + abs(y) + abs(z) :  (x * x + y * y + z * z);\\n`;\r\n        functionString += `}\\n\\n`;\r\n\r\n        functionString += `vec2 worley(vec3 P, float jitter, bool manhattanDistance){\\n`;\r\n        functionString += `    float K = 0.142857142857; // 1/7\\n`;\r\n        functionString += `    float Ko = 0.428571428571; // 1/2-K/2\\n`;\r\n        functionString += `    float  K2 = 0.020408163265306; // 1/(7*7)\\n`;\r\n        functionString += `    float Kz = 0.166666666667; // 1/6\\n`;\r\n        functionString += `    float Kzo = 0.416666666667; // 1/2-1/6*2\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 Pi = mod(floor(P), 289.0);\\n`;\r\n        functionString += `    vec3 Pf = fract(P) - 0.5;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 Pfx = Pf.x + vec3(1.0, 0.0, -1.0);\\n`;\r\n        functionString += `    vec3 Pfy = Pf.y + vec3(1.0, 0.0, -1.0);\\n`;\r\n        functionString += `    vec3 Pfz = Pf.z + vec3(1.0, 0.0, -1.0);\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 p = permute(Pi.x + vec3(-1.0, 0.0, 1.0));\\n`;\r\n        functionString += `    vec3 p1 = permute(p + Pi.y - 1.0);\\n`;\r\n        functionString += `    vec3 p2 = permute(p + Pi.y);\\n`;\r\n        functionString += `    vec3 p3 = permute(p + Pi.y + 1.0);\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 p11 = permute(p1 + Pi.z - 1.0);\\n`;\r\n        functionString += `    vec3 p12 = permute(p1 + Pi.z);\\n`;\r\n        functionString += `    vec3 p13 = permute(p1 + Pi.z + 1.0);\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 p21 = permute(p2 + Pi.z - 1.0);\\n`;\r\n        functionString += `    vec3 p22 = permute(p2 + Pi.z);\\n`;\r\n        functionString += `    vec3 p23 = permute(p2 + Pi.z + 1.0);\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 p31 = permute(p3 + Pi.z - 1.0);\\n`;\r\n        functionString += `    vec3 p32 = permute(p3 + Pi.z);\\n`;\r\n        functionString += `    vec3 p33 = permute(p3 + Pi.z + 1.0);\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 ox11 = fract(p11*K) - Ko;\\n`;\r\n        functionString += `    vec3 oy11 = mod(floor(p11*K), 7.0)*K - Ko;\\n`;\r\n        functionString += `    vec3 oz11 = floor(p11*K2)*Kz - Kzo; // p11 < 289 guaranteed\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 ox12 = fract(p12*K) - Ko;\\n`;\r\n        functionString += `    vec3 oy12 = mod(floor(p12*K), 7.0)*K - Ko;\\n`;\r\n        functionString += `    vec3 oz12 = floor(p12*K2)*Kz - Kzo;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 ox13 = fract(p13*K) - Ko;\\n`;\r\n        functionString += `    vec3 oy13 = mod(floor(p13*K), 7.0)*K - Ko;\\n`;\r\n        functionString += `    vec3 oz13 = floor(p13*K2)*Kz - Kzo;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 ox21 = fract(p21*K) - Ko;\\n`;\r\n        functionString += `    vec3 oy21 = mod(floor(p21*K), 7.0)*K - Ko;\\n`;\r\n        functionString += `    vec3 oz21 = floor(p21*K2)*Kz - Kzo;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 ox22 = fract(p22*K) - Ko;\\n`;\r\n        functionString += `    vec3 oy22 = mod(floor(p22*K), 7.0)*K - Ko;\\n`;\r\n        functionString += `    vec3 oz22 = floor(p22*K2)*Kz - Kzo;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 ox23 = fract(p23*K) - Ko;\\n`;\r\n        functionString += `    vec3 oy23 = mod(floor(p23*K), 7.0)*K - Ko;\\n`;\r\n        functionString += `    vec3 oz23 = floor(p23*K2)*Kz - Kzo;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 ox31 = fract(p31*K) - Ko;\\n`;\r\n        functionString += `    vec3 oy31 = mod(floor(p31*K), 7.0)*K - Ko;\\n`;\r\n        functionString += `    vec3 oz31 = floor(p31*K2)*Kz - Kzo;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 ox32 = fract(p32*K) - Ko;\\n`;\r\n        functionString += `    vec3 oy32 = mod(floor(p32*K), 7.0)*K - Ko;\\n`;\r\n        functionString += `    vec3 oz32 = floor(p32*K2)*Kz - Kzo;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 ox33 = fract(p33*K) - Ko;\\n`;\r\n        functionString += `    vec3 oy33 = mod(floor(p33*K), 7.0)*K - Ko;\\n`;\r\n        functionString += `    vec3 oz33 = floor(p33*K2)*Kz - Kzo;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 dx11 = Pfx + jitter*ox11;\\n`;\r\n        functionString += `    vec3 dy11 = Pfy.x + jitter*oy11;\\n`;\r\n        functionString += `    vec3 dz11 = Pfz.x + jitter*oz11;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 dx12 = Pfx + jitter*ox12;\\n`;\r\n        functionString += `    vec3 dy12 = Pfy.x + jitter*oy12;\\n`;\r\n        functionString += `    vec3 dz12 = Pfz.y + jitter*oz12;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 dx13 = Pfx + jitter*ox13;\\n`;\r\n        functionString += `    vec3 dy13 = Pfy.x + jitter*oy13;\\n`;\r\n        functionString += `    vec3 dz13 = Pfz.z + jitter*oz13;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 dx21 = Pfx + jitter*ox21;\\n`;\r\n        functionString += `    vec3 dy21 = Pfy.y + jitter*oy21;\\n`;\r\n        functionString += `    vec3 dz21 = Pfz.x + jitter*oz21;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 dx22 = Pfx + jitter*ox22;\\n`;\r\n        functionString += `    vec3 dy22 = Pfy.y + jitter*oy22;\\n`;\r\n        functionString += `    vec3 dz22 = Pfz.y + jitter*oz22;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 dx23 = Pfx + jitter*ox23;\\n`;\r\n        functionString += `    vec3 dy23 = Pfy.y + jitter*oy23;\\n`;\r\n        functionString += `    vec3 dz23 = Pfz.z + jitter*oz23;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 dx31 = Pfx + jitter*ox31;\\n`;\r\n        functionString += `    vec3 dy31 = Pfy.z + jitter*oy31;\\n`;\r\n        functionString += `    vec3 dz31 = Pfz.x + jitter*oz31;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 dx32 = Pfx + jitter*ox32;\\n`;\r\n        functionString += `    vec3 dy32 = Pfy.z + jitter*oy32;\\n`;\r\n        functionString += `    vec3 dz32 = Pfz.y + jitter*oz32;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 dx33 = Pfx + jitter*ox33;\\n`;\r\n        functionString += `    vec3 dy33 = Pfy.z + jitter*oy33;\\n`;\r\n        functionString += `    vec3 dz33 = Pfz.z + jitter*oz33;\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 d11 = dist(dx11, dy11, dz11, manhattanDistance);\\n`;\r\n        functionString += `    vec3 d12 =dist(dx12, dy12, dz12, manhattanDistance);\\n`;\r\n        functionString += `    vec3 d13 = dist(dx13, dy13, dz13, manhattanDistance);\\n`;\r\n        functionString += `    vec3 d21 = dist(dx21, dy21, dz21, manhattanDistance);\\n`;\r\n        functionString += `    vec3 d22 = dist(dx22, dy22, dz22, manhattanDistance);\\n`;\r\n        functionString += `    vec3 d23 = dist(dx23, dy23, dz23, manhattanDistance);\\n`;\r\n        functionString += `    vec3 d31 = dist(dx31, dy31, dz31, manhattanDistance);\\n`;\r\n        functionString += `    vec3 d32 = dist(dx32, dy32, dz32, manhattanDistance);\\n`;\r\n        functionString += `    vec3 d33 = dist(dx33, dy33, dz33, manhattanDistance);\\n`;\r\n        functionString += `\\n`;\r\n        functionString += `    vec3 d1a = min(d11, d12);\\n`;\r\n        functionString += `    d12 = max(d11, d12);\\n`;\r\n        functionString += `    d11 = min(d1a, d13); // Smallest now not in d12 or d13\\n`;\r\n        functionString += `    d13 = max(d1a, d13);\\n`;\r\n        functionString += `    d12 = min(d12, d13); // 2nd smallest now not in d13\\n`;\r\n        functionString += `    vec3 d2a = min(d21, d22);\\n`;\r\n        functionString += `    d22 = max(d21, d22);\\n`;\r\n        functionString += `    d21 = min(d2a, d23); // Smallest now not in d22 or d23\\n`;\r\n        functionString += `    d23 = max(d2a, d23);\\n`;\r\n        functionString += `    d22 = min(d22, d23); // 2nd smallest now not in d23\\n`;\r\n        functionString += `    vec3 d3a = min(d31, d32);\\n`;\r\n        functionString += `    d32 = max(d31, d32);\\n`;\r\n        functionString += `    d31 = min(d3a, d33); // Smallest now not in d32 or d33\\n`;\r\n        functionString += `    d33 = max(d3a, d33);\\n`;\r\n        functionString += `    d32 = min(d32, d33); // 2nd smallest now not in d33\\n`;\r\n        functionString += `    vec3 da = min(d11, d21);\\n`;\r\n        functionString += `    d21 = max(d11, d21);\\n`;\r\n        functionString += `    d11 = min(da, d31); // Smallest now in d11\\n`;\r\n        functionString += `    d31 = max(da, d31); // 2nd smallest now not in d31\\n`;\r\n        functionString += `    d11.xy = (d11.x < d11.y) ? d11.xy : d11.yx;\\n`;\r\n        functionString += `    d11.xz = (d11.x < d11.z) ? d11.xz : d11.zx; // d11.x now smallest\\n`;\r\n        functionString += `    d12 = min(d12, d21); // 2nd smallest now not in d21\\n`;\r\n        functionString += `    d12 = min(d12, d22); // nor in d22\\n`;\r\n        functionString += `    d12 = min(d12, d31); // nor in d31\\n`;\r\n        functionString += `    d12 = min(d12, d32); // nor in d32\\n`;\r\n        functionString += `    d11.yz = min(d11.yz,d12.xy); // nor in d12.yz\\n`;\r\n        functionString += `    d11.y = min(d11.y,d12.z); // Only two more to go\\n`;\r\n        functionString += `    d11.y = min(d11.y,d11.z); // Done! (Phew!)\\n`;\r\n        functionString += `    return sqrt(d11.xy); // F1, F2\\n`;\r\n        functionString += `}\\n\\n`;\r\n\r\n        state._emitFunction(\"worley3D\", functionString, \"// Worley3D\");\r\n\r\n        const tempVariable = state._getFreeVariableName(\"worleyTemp\");\r\n\r\n        state.compilationString += `vec2 ${tempVariable} = worley(${this.seed.associatedVariableName}, ${this.jitter.associatedVariableName}, ${this.manhattanDistance});\\n`;\r\n\r\n        if (this.output.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(this.output, state) + ` = ${tempVariable};\\n`;\r\n        }\r\n\r\n        if (this.x.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(this.x, state) + ` = ${tempVariable}.x;\\n`;\r\n        }\r\n\r\n        if (this.y.hasEndpoints) {\r\n            state.compilationString += this._declareOutput(this.y, state) + ` = ${tempVariable}.y;\\n`;\r\n        }\r\n        return this;\r\n    }\r\n    /**\r\n     * Exposes the properties to the UI?\r\n     * @returns - boolean indicating if the block has properties or not\r\n     */\r\n    protected _dumpPropertiesCode() {\r\n        const codeString = super._dumpPropertiesCode() + `${this._codeVariableName}.manhattanDistance = ${this.manhattanDistance};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n    /**\r\n     * Exposes the properties to the Serialize?\r\n     * @returns - a serialized object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.manhattanDistance = this.manhattanDistance;\r\n\r\n        return serializationObject;\r\n    }\r\n    /**\r\n     * Exposes the properties to the deserialize?\r\n     * @param serializationObject\r\n     * @param scene\r\n     * @param rootUrl\r\n     */\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.manhattanDistance = serializationObject.manhattanDistance;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.WorleyNoise3DBlock\", WorleyNoise3DBlock);\r\n"]}