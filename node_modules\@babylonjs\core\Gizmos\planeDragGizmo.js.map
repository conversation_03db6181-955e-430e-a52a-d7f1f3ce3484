{"version": 3, "file": "planeDragGizmo.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Gizmos/planeDragGizmo.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC3D,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAGxD,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,mBAAmB,EAAE,MAAM,yCAAyC,CAAC;AAE9E,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AA4BjE;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,KAAK;IAyBrC,4EAA4E;IAC5E,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,8DAA8D;IAC9D,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,qEAAqE;IACrE,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,YAAY,CAAC,KAAY,EAAE,QAA0B;QAC/D,MAAM,KAAK,GAAG,IAAI,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEhD,iCAAiC;QACjC,MAAM,SAAS,GAAG,WAAW,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QACzG,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC9B,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;QACzB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;OAQG;IACH,YACI,eAAwB,EACxB,QAAgB,MAAM,CAAC,IAAI,EAAE,EAC7B,aAAmC,oBAAoB,CAAC,mBAAmB,EAC3E,SAAkC,IAAI,EACtC,aAAqB,MAAM,CAAC,MAAM,EAAE,EACpC,eAAuB,MAAM,CAAC,IAAI,EAAE;QAEpC,KAAK,CAAC,UAAU,CAAC,CAAC;QAjEZ,qBAAgB,GAAoC,IAAI,CAAC;QACnE;;WAEG;QACI,iBAAY,GAAG,CAAC,CAAC;QACxB;;;WAGG;QACI,qBAAgB,GAAG,IAAI,UAAU,EAA4B,CAAC;QAO3D,eAAU,GAAY,KAAK,CAAC;QAC5B,YAAO,GAA4B,IAAI,CAAC;QACxC,cAAS,GAAY,KAAK,CAAC;QAgDjC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,kBAAkB;QAClB,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAEhF,IAAI,CAAC,cAAc,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,UAAU,CAAC;QAE9C,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,YAAY,CAAC;QAClD,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,GAAG,CAAC;QAElC,gCAAgC;QAChC,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,YAAY,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEnG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAExC,IAAI,uBAAuB,GAAG,CAAC,CAAC;QAChC,MAAM,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;QAChC,MAAM,YAAY,GAAG,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC;QACzC,+EAA+E;QAC/E,IAAI,CAAC,YAAY,GAAG,IAAI,mBAAmB,CAAC,EAAE,eAAe,EAAE,eAAe,EAAE,CAAC,CAAC;QAClF,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC;QACvC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE9C,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,8DAA8D;gBAC9D,qFAAqF;gBACrF,gDAAgD;gBAEhD,iBAAiB;gBACjB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,EAAE;oBACxB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,mBAAmB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9E,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnE,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;wBACvD,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,wBAAwB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;qBAC5G;iBACJ;qBAAM;oBACH,uBAAuB,IAAI,KAAK,CAAC,YAAY,CAAC;oBAC9C,IAAI,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE;wBACvD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;wBACpF,uBAAuB,GAAG,uBAAuB,GAAG,IAAI,CAAC,YAAY,CAAC;wBACtE,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;wBACtC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,CAAC;wBACtD,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,mBAAmB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9E,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjE,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;4BACvD,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;4BACnG,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;4BAC1D,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;yBACvD;qBACJ;iBACJ;gBACD,IAAI,CAAC,cAAc,EAAE,CAAC;aACzB;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC7C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC3C,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC;QAChD,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;QAEjG,MAAM,KAAK,GAAmB;YAC1B,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAY;YACvD,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAY;YAC1D,QAAQ,EAAE,IAAI,CAAC,gBAAgB;YAC/B,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,MAAM,EAAE,KAAK;YACb,YAAY,EAAE,IAAI,CAAC,YAAY;SAClC,CAAC;QACF,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,IAAI,CAAC,UAAkB,EAAE,KAAK,CAAC,CAAC;QAE7D,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACzF,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,OAAO;aACV;YACD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAO,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,MAAM,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;gBACxJ,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;aAC3D;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YACnD,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5G,CAAC,CAAC,CAAC;IACP,CAAC;IAES,oBAAoB,CAAC,KAAqB;QAChD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;SACpD;IACL,CAAC;IAED;;OAEG;IACH,IAAW,SAAS,CAAC,KAAc;QAC/B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,KAAK,EAAE;YACR,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;aAAM;YACH,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;aACjD;SACJ;IACL,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpF,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QAC3B,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;SAC7B;QACD,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACjF,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,OAAO,EAAE,CAAC;aAClB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;CACJ", "sourcesContent": ["import type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport { TmpVectors, Vector3 } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { Node } from \"../node\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport { CreatePlane } from \"../Meshes/Builders/planeBuilder\";\r\nimport { PointerDragBehavior } from \"../Behaviors/Meshes/pointerDragBehavior\";\r\nimport type { GizmoAxisCache, IGizmo } from \"./gizmo\";\r\nimport { Gizmo } from \"./gizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { PositionGizmo } from \"./positionGizmo\";\r\n\r\n/**\r\n * Interface for plane drag gizmo\r\n */\r\nexport interface IPlaneDragGizmo extends IGizmo {\r\n    /** Drag behavior responsible for the gizmos dragging interactions */\r\n    dragBehavior: PointerDragBehavior;\r\n    /** Drag distance in babylon units that the gizmo will snap to when dragged */\r\n    snapDistance: number;\r\n    /**\r\n     * Event that fires each time the gizmo snaps to a new location.\r\n     * * snapDistance is the change in distance\r\n     */\r\n    onSnapObservable: Observable<{ snapDistance: number }>;\r\n    /** If the gizmo is enabled */\r\n    isEnabled: boolean;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    coloredMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is hovered with mouse */\r\n    hoverMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is disabled. typically grey. */\r\n    disableMaterial: StandardMaterial;\r\n}\r\n\r\n/**\r\n * Single plane drag gizmo\r\n */\r\nexport class PlaneDragGizmo extends Gizmo implements IPlaneDragGizmo {\r\n    /**\r\n     * Drag behavior responsible for the gizmos dragging interactions\r\n     */\r\n    public dragBehavior: PointerDragBehavior;\r\n    protected _pointerObserver: Nullable<Observer<PointerInfo>> = null;\r\n    /**\r\n     * Drag distance in babylon units that the gizmo will snap to when dragged (Default: 0)\r\n     */\r\n    public snapDistance = 0;\r\n    /**\r\n     * Event that fires each time the gizmo snaps to a new location.\r\n     * * snapDistance is the change in distance\r\n     */\r\n    public onSnapObservable = new Observable<{ snapDistance: number }>();\r\n\r\n    protected _gizmoMesh: TransformNode;\r\n    protected _coloredMaterial: StandardMaterial;\r\n    protected _hoverMaterial: StandardMaterial;\r\n    protected _disableMaterial: StandardMaterial;\r\n\r\n    protected _isEnabled: boolean = false;\r\n    protected _parent: Nullable<PositionGizmo> = null;\r\n    protected _dragging: boolean = false;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    public get coloredMaterial() {\r\n        return this._coloredMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is hovered with mouse*/\r\n    public get hoverMaterial() {\r\n        return this._hoverMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is disabled. typically grey.*/\r\n    public get disableMaterial() {\r\n        return this._disableMaterial;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _CreatePlane(scene: Scene, material: StandardMaterial): TransformNode {\r\n        const plane = new TransformNode(\"plane\", scene);\r\n\r\n        //make sure plane is double sided\r\n        const dragPlane = CreatePlane(\"dragPlane\", { width: 0.1375, height: 0.1375, sideOrientation: 2 }, scene);\r\n        dragPlane.material = material;\r\n        dragPlane.parent = plane;\r\n        return plane;\r\n    }\r\n\r\n    /**\r\n     * Creates a PlaneDragGizmo\r\n     * @param dragPlaneNormal The axis normal to which the gizmo will be able to drag on\r\n     * @param color The color of the gizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param parent\r\n     * @param hoverColor The color of the gizmo when hovering over and dragging\r\n     * @param disableColor The Color of the gizmo when its disabled\r\n     */\r\n    constructor(\r\n        dragPlaneNormal: Vector3,\r\n        color: Color3 = Color3.Gray(),\r\n        gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer,\r\n        parent: Nullable<PositionGizmo> = null,\r\n        hoverColor: Color3 = Color3.Yellow(),\r\n        disableColor: Color3 = Color3.Gray()\r\n    ) {\r\n        super(gizmoLayer);\r\n        this._parent = parent;\r\n        // Create Material\r\n        this._coloredMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._coloredMaterial.diffuseColor = color;\r\n        this._coloredMaterial.specularColor = color.subtract(new Color3(0.1, 0.1, 0.1));\r\n\r\n        this._hoverMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._hoverMaterial.diffuseColor = hoverColor;\r\n\r\n        this._disableMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._disableMaterial.diffuseColor = disableColor;\r\n        this._disableMaterial.alpha = 0.4;\r\n\r\n        // Build plane mesh on root node\r\n        this._gizmoMesh = PlaneDragGizmo._CreatePlane(gizmoLayer.utilityLayerScene, this._coloredMaterial);\r\n\r\n        this._gizmoMesh.lookAt(this._rootMesh.position.add(dragPlaneNormal));\r\n        this._gizmoMesh.scaling.scaleInPlace(1 / 3);\r\n        this._gizmoMesh.parent = this._rootMesh;\r\n\r\n        let currentSnapDragDistance = 0;\r\n        const tmpVector = new Vector3();\r\n        const tmpSnapEvent = { snapDistance: 0 };\r\n        // Add dragPlaneNormal drag behavior to handle events when the gizmo is dragged\r\n        this.dragBehavior = new PointerDragBehavior({ dragPlaneNormal: dragPlaneNormal });\r\n        this.dragBehavior.moveAttached = false;\r\n        this._rootMesh.addBehavior(this.dragBehavior);\r\n\r\n        this.dragBehavior.onDragObservable.add((event) => {\r\n            if (this.attachedNode) {\r\n                // Keep world translation and use it to update world transform\r\n                // if the node has parent, the local transform properties (position, rotation, scale)\r\n                // will be recomputed in _matrixChanged function\r\n\r\n                // Snapping logic\r\n                if (this.snapDistance == 0) {\r\n                    this.attachedNode.getWorldMatrix().getTranslationToRef(TmpVectors.Vector3[0]);\r\n                    TmpVectors.Vector3[0].addToRef(event.delta, TmpVectors.Vector3[0]);\r\n                    if (this.dragBehavior.validateDrag(TmpVectors.Vector3[0])) {\r\n                        this.attachedNode.getWorldMatrix().addTranslationFromFloats(event.delta.x, event.delta.y, event.delta.z);\r\n                    }\r\n                } else {\r\n                    currentSnapDragDistance += event.dragDistance;\r\n                    if (Math.abs(currentSnapDragDistance) > this.snapDistance) {\r\n                        const dragSteps = Math.floor(Math.abs(currentSnapDragDistance) / this.snapDistance);\r\n                        currentSnapDragDistance = currentSnapDragDistance % this.snapDistance;\r\n                        event.delta.normalizeToRef(tmpVector);\r\n                        tmpVector.scaleInPlace(this.snapDistance * dragSteps);\r\n                        this.attachedNode.getWorldMatrix().getTranslationToRef(TmpVectors.Vector3[0]);\r\n                        TmpVectors.Vector3[0].addToRef(tmpVector, TmpVectors.Vector3[0]);\r\n                        if (this.dragBehavior.validateDrag(TmpVectors.Vector3[0])) {\r\n                            this.attachedNode.getWorldMatrix().addTranslationFromFloats(tmpVector.x, tmpVector.y, tmpVector.z);\r\n                            tmpSnapEvent.snapDistance = this.snapDistance * dragSteps;\r\n                            this.onSnapObservable.notifyObservers(tmpSnapEvent);\r\n                        }\r\n                    }\r\n                }\r\n                this._matrixChanged();\r\n            }\r\n        });\r\n        this.dragBehavior.onDragStartObservable.add(() => {\r\n            this._dragging = true;\r\n        });\r\n        this.dragBehavior.onDragEndObservable.add(() => {\r\n            this._dragging = false;\r\n        });\r\n\r\n        const light = gizmoLayer._getSharedGizmoLight();\r\n        light.includedOnlyMeshes = light.includedOnlyMeshes.concat(this._rootMesh.getChildMeshes(false));\r\n\r\n        const cache: GizmoAxisCache = {\r\n            gizmoMeshes: this._gizmoMesh.getChildMeshes() as Mesh[],\r\n            colliderMeshes: this._gizmoMesh.getChildMeshes() as Mesh[],\r\n            material: this._coloredMaterial,\r\n            hoverMaterial: this._hoverMaterial,\r\n            disableMaterial: this._disableMaterial,\r\n            active: false,\r\n            dragBehavior: this.dragBehavior,\r\n        };\r\n        this._parent?.addToAxisCache(this._gizmoMesh as Mesh, cache);\r\n\r\n        this._pointerObserver = gizmoLayer.utilityLayerScene.onPointerObservable.add((pointerInfo) => {\r\n            if (this._customMeshSet) {\r\n                return;\r\n            }\r\n            this._isHovered = !!(cache.colliderMeshes.indexOf(<Mesh>pointerInfo?.pickInfo?.pickedMesh) != -1);\r\n            if (!this._parent) {\r\n                const material = cache.dragBehavior.enabled ? (this._isHovered || this._dragging ? this._hoverMaterial : this._coloredMaterial) : this._disableMaterial;\r\n                this._setGizmoMeshMaterial(cache.gizmoMeshes, material);\r\n            }\r\n        });\r\n\r\n        this.dragBehavior.onEnabledObservable.add((newState) => {\r\n            this._setGizmoMeshMaterial(cache.gizmoMeshes, newState ? this._coloredMaterial : this._disableMaterial);\r\n        });\r\n    }\r\n\r\n    protected _attachedNodeChanged(value: Nullable<Node>) {\r\n        if (this.dragBehavior) {\r\n            this.dragBehavior.enabled = value ? true : false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If the gizmo is enabled\r\n     */\r\n    public set isEnabled(value: boolean) {\r\n        this._isEnabled = value;\r\n        if (!value) {\r\n            this.attachedNode = null;\r\n        } else {\r\n            if (this._parent) {\r\n                this.attachedNode = this._parent.attachedNode;\r\n            }\r\n        }\r\n    }\r\n\r\n    public get isEnabled(): boolean {\r\n        return this._isEnabled;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public dispose() {\r\n        this.onSnapObservable.clear();\r\n        this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(this._pointerObserver);\r\n        this.dragBehavior.detach();\r\n        super.dispose();\r\n        if (this._gizmoMesh) {\r\n            this._gizmoMesh.dispose();\r\n        }\r\n        [this._coloredMaterial, this._hoverMaterial, this._disableMaterial].forEach((matl) => {\r\n            if (matl) {\r\n                matl.dispose();\r\n            }\r\n        });\r\n    }\r\n}\r\n"]}