{"version": 3, "file": "WebXRLightEstimation.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRLightEstimation.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,0CAA0C,CAAC;AAChF,OAAO,EAAE,eAAe,EAAE,qBAAqB,EAAE,MAAM,0CAA0C,CAAC;AAClG,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAEjF,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,WAAW,EAAE,MAAM,sCAAsC,CAAC;AACnE,OAAO,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,MAAM,iCAAiC,CAAC;AAC1F,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAC7D,OAAO,EAAE,YAAY,EAAE,2DAAuD;AAyE9E;;;;GAIG;AACH,MAAM,OAAO,oBAAqB,SAAQ,oBAAoB;IA6C1D;;;;OAIG;IACH,YACI,iBAAsC;IACtC;;OAEG;IACa,OAAqC;QAErD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFT,YAAO,GAAP,OAAO,CAA8B;QAtDjD,mBAAc,GAA6D,IAAI,CAAC;QAChF,uBAAkB,GAA0B,IAAI,CAAC;QACjD,qBAAgB,GAA8B,IAAI,CAAC;QACnD,kBAAa,GAA2B,IAAI,CAAC;QAC7C,oBAAe,GAA6B,IAAI,CAAC;QACjD,oBAAe,GAAY,OAAO,CAAC,EAAE,EAAE,CAAC,aAAa,EAAE,CAAC;QACxD,gBAAW,GAAW,MAAM,CAAC,KAAK,EAAE,CAAC;QACrC,eAAU,GAAW,CAAC,CAAC;QACvB,wBAAmB,GAAuB,IAAI,kBAAkB,EAAE,CAAC;QACnE,qBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC9B,6BAAwB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAa9C;;;;WAIG;QACK,kCAA6B,GAAW,EAAE,CAAC;QAInD;;;;WAIG;QACI,qBAAgB,GAA+B,IAAI,CAAC;QAE3D;;WAEG;QACI,yCAAoC,GAA4B,IAAI,UAAU,EAAE,CAAC;QAsExF;;WAEG;QACK,6BAAwB,GAAG,GAAS,EAAE;YAC1C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACrB,OAAO;aACV;YACD,gEAAgE;YAChE,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;gBAClC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACvB,IAAI,GAAG,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;oBAChE,OAAO;iBACV;gBACD,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC;aAC/B;YACD,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3E,IAAI,EAAE,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC/B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE;oBACnC,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,qBAAqB,CAAC,OAAO,CAAC,CAAC;oBACrH,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC;oBAC9B,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC;oBAChC,eAAe,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,KAAK,QAAQ,CAAC;oBAC5E,eAAe,CAAC,MAAM,GAAG,SAAS,CAAC,kBAAkB,CAAC;oBACtD,eAAe,CAAC,eAAe,GAAG,IAAI,CAAC;oBACvC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC;oBAC3I,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC,+BAA+B,CAAC;oBACzE,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,6BAA6B,CAAC;oBAC3D,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,6BAA6B,CAAC;oBAC5D,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC,wBAAwB,CAAC;oBAClE,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC,wBAAwB,CAAC;oBAClE,eAAe,CAAC,gBAAgB,GAAG,IAAI,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC,iBAAiB,EAA2B,CAAC,CAAC;oBACnH,IAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,eAAe,CAAC;iBACtD;qBAAM;oBACH,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC3D,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,iBAAiB,EAAE,CAAC;iBACpE;gBACD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;gBAChD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;oBACnC,IAAI,CAAC,aAAc,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;oBAC3F,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;wBACzD,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;wBAC1F,IAAI,CAAC,oCAAoC,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAmB,CAAC,CAAC;wBACpF,IAAI,CAAC,aAAc,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;oBAC5F,CAAC,CAAC,CAAC;iBACN;qBAAM;oBACH,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;oBAE1F,IAAI,CAAC,oCAAoC,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;iBACtF;aACJ;QACL,CAAC,CAAC;QAzGE,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAE9C,IAAI,IAAI,CAAC,OAAO,CAAC,4BAA4B,EAAE;YAC3C,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,8BAA8B,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACjI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACtD,8BAA8B;YAC9B,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG,CAAC,CAAC;YACpC,IAAI,CAAC,gBAAgB,CAAC,WAAW,GAAG,cAAc,CAAC,YAAY,CAAC;SACnE;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;QAE7E,uDAAuD;QACvD,KAAK,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;IAC5E,CAAC;IAED;;;;OAIG;IACH,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO;gBACH,UAAU,EAAE,IAAI,CAAC,WAAW;gBAC5B,cAAc,EAAE,IAAI,CAAC,eAAe;gBACpC,cAAc,EAAE,IAAI,CAAC,UAAU;gBAC/B,kBAAkB,EAAE,IAAI,CAAC,mBAAmB;aAC/C,CAAC;SACL;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAEO,iBAAiB;QACrB,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;YAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC;SACtE;QACD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAEO,eAAe;QACnB,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;YAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzC,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SACtF;QACD,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAsDD;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,yBAAyB,IAAI,QAAQ,CAAC,CAAC;QACjI,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACjD,IAAI,CAAC,iBAAiB,CAAC,OAAO;aACzB,iBAAiB,CAAC;YACf,gBAAgB;SACnB,CAAC;aACD,IAAI,CAAC,CAAC,YAA0B,EAAE,EAAE;YACjC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE;gBACxC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;oBAC1B,IAAI,CAAC,kBAAkB,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;oBACxE,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,IAAI,CAAC;oBACvC,IAAI,CAAC,kBAAkB,CAAC,eAAe,GAAG,SAAS,CAAC,kBAAkB,CAAC;oBACvE,IAAI,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE;wBACzC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;qBAC7E;iBACJ;gBACD,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;aAC1F;QACL,CAAC,CAAC,CAAC;QAEP,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE;YACvE,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC1F,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,mHAAmH;QACnH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,oCAAoC,CAAC,KAAK,EAAE,CAAC;QAElD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;QAED,IAAI,IAAI,CAAC,kBAAkB,KAAK,IAAI,EAAE;YAClC,IAAI,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE;gBAClC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;aAC9C;YACD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAClC;IACL,CAAC;IAES,UAAU,CAAC,QAAiB;QAClC,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;YAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,2BAA2B,EAAE;gBAC1C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACvB,IAAI,GAAG,GAAG,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC,2BAA2B,EAAE;oBAChF,OAAO;iBACV;gBACD,IAAI,CAAC,wBAAwB,GAAG,GAAG,CAAC;aACvC;YACD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtE,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CACtB,GAAG,EACH,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,EAC7C,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,EAC7C,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,CAChD,CAAC;gBAEF,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;gBAEjF,qFAAqF;gBACrF,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;oBACjC,IAAI,CAAC,eAAe,GAAG,IAAI,OAAO,EAAE,CAAC;oBACrC,IAAI,CAAC,WAAW,GAAG,IAAI,MAAM,EAAE,CAAC;oBAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACvB,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC;wBACvD,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;qBACpD;iBACJ;gBAED,IAAI,CAAC,eAAe,CAAC,cAAc,CAC/B,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,EAC7C,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,EAC7C,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,GAAG,SAAS,CAC5D,CAAC;gBACF,IAAI,CAAC,WAAW,CAAC,cAAc,CAC3B,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EAC/D,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EAC/D,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAClE,CAAC;gBACF,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,CAAC;gBACrG,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE;oBACrE,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,IAAI,IAAI,mBAAmB,EAAE,CAAC;oBACvH,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;iBAC9F;gBAED,yCAAyC;gBACzC,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;gBACrC,wCAAwC;gBACxC,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;oBAC/D,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;oBACjE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBAC5D;aACJ;SACJ;IACL,CAAC;;AA/RD;;GAEG;AACoB,yBAAI,GAAG,gBAAgB,CAAC,gBAAgB,AAApC,CAAqC;AAChE;;;;GAIG;AACoB,4BAAO,GAAG,CAAC,AAAJ,CAAK;AAyRvC,sBAAsB;AACtB,oBAAoB,CAAC,eAAe,CAChC,oBAAoB,CAAC,IAAI,EACzB,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,oBAAoB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AACrE,CAAC,EACD,oBAAoB,CAAC,OAAO,EAC5B,KAAK,CACR,CAAC", "sourcesContent": ["import { WebGLHardwareTexture } from \"../../Engines/WebGL/webGLHardwareTexture\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { WebXRFeatureName, WebXRFeaturesManager } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { DirectionalLight } from \"../../Lights/directionalLight\";\r\nimport { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { SphericalHarmonics, SphericalPolynomial } from \"../../Maths/sphericalPolynomial\";\r\nimport { LightConstants } from \"../../Lights/lightConstants\";\r\nimport { HDRFiltering } from \"core/Materials/Textures/Filtering/hdrFiltering\";\r\n\r\n/**\r\n * Options for Light Estimation feature\r\n */\r\nexport interface IWebXRLightEstimationOptions {\r\n    /**\r\n     * Disable the cube map reflection feature. In this case only light direction and color will be updated\r\n     */\r\n    disableCubeMapReflection?: boolean;\r\n    /**\r\n     * Should the scene's env texture be set to the cube map reflection texture\r\n     * Note that this doesn't work is disableCubeMapReflection if set to false\r\n     */\r\n    setSceneEnvironmentTexture?: boolean;\r\n    /**\r\n     * How often should the cubemap update in ms.\r\n     * If not set the cubemap will be updated every time the underlying system updates the environment texture.\r\n     */\r\n    cubeMapPollInterval?: number;\r\n    /**\r\n     * How often should the light estimation properties update in ms.\r\n     * If not set the light estimation properties will be updated on every frame (depending on the underlying system)\r\n     */\r\n    lightEstimationPollInterval?: number;\r\n    /**\r\n     * Should a directional light source be created.\r\n     * If created, this light source will be updated whenever the light estimation values change\r\n     */\r\n    createDirectionalLightSource?: boolean;\r\n    /**\r\n     * Define the format to be used for the light estimation texture.\r\n     */\r\n    reflectionFormat?: XRReflectionFormat;\r\n    /**\r\n     * Should the light estimation's needed vectors be constructed on each frame.\r\n     * Use this when you use those vectors and don't want their values to change outside of the light estimation feature\r\n     */\r\n    disableVectorReuse?: boolean;\r\n\r\n    /**\r\n     * disable applying the spherical polynomial to the cube map texture\r\n     */\r\n    disableSphericalPolynomial?: boolean;\r\n\r\n    /**\r\n     * disable prefiltering the cube map texture\r\n     */\r\n    disablePreFiltering?: boolean;\r\n}\r\n\r\n/**\r\n * An interface describing the result of a light estimation\r\n */\r\nexport interface IWebXRLightEstimation {\r\n    /**\r\n     * The intensity of the light source\r\n     */\r\n    lightIntensity: number;\r\n    /**\r\n     * Color of light source\r\n     */\r\n    lightColor: Color3;\r\n    /**\r\n     * The direction from the light source\r\n     */\r\n    lightDirection: Vector3;\r\n    /**\r\n     * Spherical harmonics coefficients of the light source\r\n     */\r\n    sphericalHarmonics: SphericalHarmonics;\r\n}\r\n\r\n/**\r\n * Light Estimation Feature\r\n *\r\n * @since 5.0.0\r\n */\r\nexport class WebXRLightEstimation extends WebXRAbstractFeature {\r\n    private _canvasContext: Nullable<WebGLRenderingContext | WebGL2RenderingContext> = null;\r\n    private _reflectionCubeMap: Nullable<BaseTexture> = null;\r\n    private _xrLightEstimate: Nullable<XRLightEstimate> = null;\r\n    private _xrLightProbe: Nullable<XRLightProbe> = null;\r\n    private _xrWebGLBinding: Nullable<XRWebGLBinding> = null;\r\n    private _lightDirection: Vector3 = Vector3.Up().negateInPlace();\r\n    private _lightColor: Color3 = Color3.White();\r\n    private _intensity: number = 1;\r\n    private _sphericalHarmonics: SphericalHarmonics = new SphericalHarmonics();\r\n    private _cubeMapPollTime = Date.now();\r\n    private _lightEstimationPollTime = Date.now();\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.LIGHT_ESTIMATION;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * ARCore's reflection cube map size is 16x16.\r\n     * Once other systems support this feature we will need to change this to be dynamic.\r\n     * see https://github.com/immersive-web/lighting-estimation/blob/main/lighting-estimation-explainer.md#cube-map-open-questions\r\n     */\r\n    private _reflectionCubeMapTextureSize: number = 16;\r\n\r\n    private _hdrFilter: HDRFiltering;\r\n\r\n    /**\r\n     * If createDirectionalLightSource is set to true this light source will be created automatically.\r\n     * Otherwise this can be set with an external directional light source.\r\n     * This light will be updated whenever the light estimation values change.\r\n     */\r\n    public directionalLight: Nullable<DirectionalLight> = null;\r\n\r\n    /**\r\n     * This observable will notify when the reflection cube map is updated.\r\n     */\r\n    public onReflectionCubeMapUpdatedObservable: Observable<BaseTexture> = new Observable();\r\n\r\n    /**\r\n     * Creates a new instance of the light estimation feature\r\n     * @param _xrSessionManager an instance of WebXRSessionManager\r\n     * @param options options to use when constructing this feature\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        /**\r\n         * options to use when constructing this feature\r\n         */\r\n        public readonly options: IWebXRLightEstimationOptions\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"light-estimation\";\r\n\r\n        if (this.options.createDirectionalLightSource) {\r\n            this.directionalLight = new DirectionalLight(\"light estimation directional\", this._lightDirection, this._xrSessionManager.scene);\r\n            this.directionalLight.position = new Vector3(0, 8, 0);\r\n            // intensity will be set later\r\n            this.directionalLight.intensity = 0;\r\n            this.directionalLight.falloffType = LightConstants.FALLOFF_GLTF;\r\n        }\r\n\r\n        this._hdrFilter = new HDRFiltering(this._xrSessionManager.scene.getEngine());\r\n\r\n        // https://immersive-web.github.io/lighting-estimation/\r\n        Tools.Warn(\"light-estimation is an experimental and unstable feature.\");\r\n    }\r\n\r\n    /**\r\n     * While the estimated cube map is expected to update over time to better reflect the user's environment as they move around those changes are unlikely to happen with every XRFrame.\r\n     * Since creating and processing the cube map is potentially expensive, especially if mip maps are needed, you can listen to the onReflectionCubeMapUpdatedObservable to determine\r\n     * when it has been updated.\r\n     */\r\n    public get reflectionCubeMapTexture(): Nullable<BaseTexture> {\r\n        return this._reflectionCubeMap;\r\n    }\r\n\r\n    /**\r\n     * The most recent light estimate.  Available starting on the first frame where the device provides a light probe.\r\n     */\r\n    public get xrLightingEstimate(): Nullable<IWebXRLightEstimation> {\r\n        if (this._xrLightEstimate) {\r\n            return {\r\n                lightColor: this._lightColor,\r\n                lightDirection: this._lightDirection,\r\n                lightIntensity: this._intensity,\r\n                sphericalHarmonics: this._sphericalHarmonics,\r\n            };\r\n        }\r\n        return this._xrLightEstimate;\r\n    }\r\n\r\n    private _getCanvasContext(): WebGLRenderingContext | WebGL2RenderingContext {\r\n        if (this._canvasContext === null) {\r\n            this._canvasContext = this._xrSessionManager.scene.getEngine()._gl;\r\n        }\r\n        return this._canvasContext;\r\n    }\r\n\r\n    private _getXRGLBinding(): XRWebGLBinding {\r\n        if (this._xrWebGLBinding === null) {\r\n            const context = this._getCanvasContext();\r\n            this._xrWebGLBinding = new XRWebGLBinding(this._xrSessionManager.session, context);\r\n        }\r\n        return this._xrWebGLBinding;\r\n    }\r\n\r\n    /**\r\n     * Event Listener for \"reflectionchange\" events.\r\n     */\r\n    private _updateReflectionCubeMap = (): void => {\r\n        if (!this._xrLightProbe) {\r\n            return;\r\n        }\r\n        // check poll time, do not update if it has not been long enough\r\n        if (this.options.cubeMapPollInterval) {\r\n            const now = Date.now();\r\n            if (now - this._cubeMapPollTime < this.options.cubeMapPollInterval) {\r\n                return;\r\n            }\r\n            this._cubeMapPollTime = now;\r\n        }\r\n        const lp = this._getXRGLBinding().getReflectionCubeMap(this._xrLightProbe);\r\n        if (lp && this._reflectionCubeMap) {\r\n            if (!this._reflectionCubeMap._texture) {\r\n                const internalTexture = new InternalTexture(this._xrSessionManager.scene.getEngine(), InternalTextureSource.Unknown);\r\n                internalTexture.isCube = true;\r\n                internalTexture.invertY = false;\r\n                internalTexture._useSRGBBuffer = this.options.reflectionFormat === \"srgba8\";\r\n                internalTexture.format = Constants.TEXTUREFORMAT_RGBA;\r\n                internalTexture.generateMipMaps = true;\r\n                internalTexture.type = this.options.reflectionFormat !== \"srgba8\" ? Constants.TEXTURETYPE_HALF_FLOAT : Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n                internalTexture.samplingMode = Constants.TEXTURE_LINEAR_LINEAR_MIPLINEAR;\r\n                internalTexture.width = this._reflectionCubeMapTextureSize;\r\n                internalTexture.height = this._reflectionCubeMapTextureSize;\r\n                internalTexture._cachedWrapU = Constants.TEXTURE_WRAP_ADDRESSMODE;\r\n                internalTexture._cachedWrapV = Constants.TEXTURE_WRAP_ADDRESSMODE;\r\n                internalTexture._hardwareTexture = new WebGLHardwareTexture(lp, this._getCanvasContext() as WebGLRenderingContext);\r\n                this._reflectionCubeMap._texture = internalTexture;\r\n            } else {\r\n                this._reflectionCubeMap._texture._hardwareTexture?.set(lp);\r\n                this._reflectionCubeMap._texture.getEngine().resetTextureCache();\r\n            }\r\n            this._reflectionCubeMap._texture.isReady = true;\r\n            if (!this.options.disablePreFiltering) {\r\n                this._xrLightProbe!.removeEventListener(\"reflectionchange\", this._updateReflectionCubeMap);\r\n                this._hdrFilter.prefilter(this._reflectionCubeMap).then(() => {\r\n                    this._xrSessionManager.scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag);\r\n                    this.onReflectionCubeMapUpdatedObservable.notifyObservers(this._reflectionCubeMap!);\r\n                    this._xrLightProbe!.addEventListener(\"reflectionchange\", this._updateReflectionCubeMap);\r\n                });\r\n            } else {\r\n                this._xrSessionManager.scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag);\r\n\r\n                this.onReflectionCubeMapUpdatedObservable.notifyObservers(this._reflectionCubeMap);\r\n            }\r\n        }\r\n    };\r\n\r\n    /**\r\n     * attach this feature\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public attach(): boolean {\r\n        if (!super.attach()) {\r\n            return false;\r\n        }\r\n\r\n        const reflectionFormat = this.options.reflectionFormat ?? (this._xrSessionManager.session.preferredReflectionFormat || \"srgba8\");\r\n        this.options.reflectionFormat = reflectionFormat;\r\n        this._xrSessionManager.session\r\n            .requestLightProbe({\r\n                reflectionFormat,\r\n            })\r\n            .then((xrLightProbe: XRLightProbe) => {\r\n                this._xrLightProbe = xrLightProbe;\r\n                if (!this.options.disableCubeMapReflection) {\r\n                    if (!this._reflectionCubeMap) {\r\n                        this._reflectionCubeMap = new BaseTexture(this._xrSessionManager.scene);\r\n                        this._reflectionCubeMap._isCube = true;\r\n                        this._reflectionCubeMap.coordinatesMode = Constants.TEXTURE_CUBIC_MODE;\r\n                        if (this.options.setSceneEnvironmentTexture) {\r\n                            this._xrSessionManager.scene.environmentTexture = this._reflectionCubeMap;\r\n                        }\r\n                    }\r\n                    this._xrLightProbe.addEventListener(\"reflectionchange\", this._updateReflectionCubeMap);\r\n                }\r\n            });\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * detach this feature.\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public detach(): boolean {\r\n        const detached = super.detach();\r\n\r\n        if (this._xrLightProbe !== null && !this.options.disableCubeMapReflection) {\r\n            this._xrLightProbe.removeEventListener(\"reflectionchange\", this._updateReflectionCubeMap);\r\n            this._xrLightProbe = null;\r\n        }\r\n\r\n        this._canvasContext = null;\r\n        this._xrLightEstimate = null;\r\n        // When the session ends (on detach) we must clear our XRWebGLBinging instance, which references the ended session.\r\n        this._xrWebGLBinding = null;\r\n\r\n        return detached;\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n\r\n        this.onReflectionCubeMapUpdatedObservable.clear();\r\n\r\n        if (this.directionalLight) {\r\n            this.directionalLight.dispose();\r\n            this.directionalLight = null;\r\n        }\r\n\r\n        if (this._reflectionCubeMap !== null) {\r\n            if (this._reflectionCubeMap._texture) {\r\n                this._reflectionCubeMap._texture.dispose();\r\n            }\r\n            this._reflectionCubeMap.dispose();\r\n            this._reflectionCubeMap = null;\r\n        }\r\n    }\r\n\r\n    protected _onXRFrame(_xrFrame: XRFrame): void {\r\n        if (this._xrLightProbe !== null) {\r\n            if (this.options.lightEstimationPollInterval) {\r\n                const now = Date.now();\r\n                if (now - this._lightEstimationPollTime < this.options.lightEstimationPollInterval) {\r\n                    return;\r\n                }\r\n                this._lightEstimationPollTime = now;\r\n            }\r\n            this._xrLightEstimate = _xrFrame.getLightEstimate(this._xrLightProbe);\r\n            if (this._xrLightEstimate) {\r\n                this._intensity = Math.max(\r\n                    1.0,\r\n                    this._xrLightEstimate.primaryLightIntensity.x,\r\n                    this._xrLightEstimate.primaryLightIntensity.y,\r\n                    this._xrLightEstimate.primaryLightIntensity.z\r\n                );\r\n\r\n                const rhsFactor = this._xrSessionManager.scene.useRightHandedSystem ? 1.0 : -1.0;\r\n\r\n                // recreate the vector caches, so that the last one provided to the user will persist\r\n                if (this.options.disableVectorReuse) {\r\n                    this._lightDirection = new Vector3();\r\n                    this._lightColor = new Color3();\r\n                    if (this.directionalLight) {\r\n                        this.directionalLight.direction = this._lightDirection;\r\n                        this.directionalLight.diffuse = this._lightColor;\r\n                    }\r\n                }\r\n\r\n                this._lightDirection.copyFromFloats(\r\n                    this._xrLightEstimate.primaryLightDirection.x,\r\n                    this._xrLightEstimate.primaryLightDirection.y,\r\n                    this._xrLightEstimate.primaryLightDirection.z * rhsFactor\r\n                );\r\n                this._lightColor.copyFromFloats(\r\n                    this._xrLightEstimate.primaryLightIntensity.x / this._intensity,\r\n                    this._xrLightEstimate.primaryLightIntensity.y / this._intensity,\r\n                    this._xrLightEstimate.primaryLightIntensity.z / this._intensity\r\n                );\r\n                this._sphericalHarmonics.updateFromFloatsArray(this._xrLightEstimate.sphericalHarmonicsCoefficients);\r\n                if (this._reflectionCubeMap && !this.options.disableSphericalPolynomial) {\r\n                    this._reflectionCubeMap.sphericalPolynomial = this._reflectionCubeMap.sphericalPolynomial || new SphericalPolynomial();\r\n                    this._reflectionCubeMap.sphericalPolynomial?.updateFromHarmonics(this._sphericalHarmonics);\r\n                }\r\n\r\n                // direction from instead of direction to\r\n                this._lightDirection.negateInPlace();\r\n                // set the values after calculating them\r\n                if (this.directionalLight) {\r\n                    this.directionalLight.direction.copyFrom(this._lightDirection);\r\n                    this.directionalLight.intensity = Math.min(this._intensity, 1.0);\r\n                    this.directionalLight.diffuse.copyFrom(this._lightColor);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRLightEstimation.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRLightEstimation(xrSessionManager, options);\r\n    },\r\n    WebXRLightEstimation.Version,\r\n    false\r\n);\r\n"]}