{"version": 3, "file": "observable.extensions.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/observable.extensions.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C;;GAEG;AACH,MAAM,OAAO,aAAa;IAItB;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,EAAE;YACtC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACzD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;aAC3D;SACJ;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,KAAK,CAAI,WAA4B,EAAE,QAAwD,EAAE,OAAe,CAAC,CAAC,EAAE,QAAa,IAAI;QAC/I,MAAM,MAAM,GAAG,IAAI,aAAa,EAAK,CAAC;QAEtC,MAAM,CAAC,UAAU,GAAG,IAAI,KAAK,EAAe,CAAC;QAC7C,MAAM,CAAC,YAAY,GAAG,WAAW,CAAC;QAElC,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;YAClC,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAC9D,IAAI,QAAQ,EAAE;gBACV,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACpC;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;AAsBD,UAAU,CAAC,SAAS,CAAC,0BAA0B,GAAG,KAAK,WAAc,SAAY,EAAE,OAAe,CAAC,CAAC,EAAE,MAAY,EAAE,aAAmB,EAAE,QAAc;IACnJ,0BAA0B;IAC1B,IAAI,CAAC,GAAiB,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAEjD,qCAAqC;IACrC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;QACxB,OAAO,CAAC,CAAC;KACZ;IAED,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;IAC/B,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IAClB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;IACtB,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;IACpC,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC;IAChC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAE1B,qFAAqF;IACrF,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;QAC3B,IAAI,KAAK,CAAC,iBAAiB,EAAE;YACzB,OAAO;SACV;QACD,IAAI,GAAG,CAAC,mBAAmB,EAAE;YACzB,OAAO;SACV;QACD,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,EAAE;YACjB,IAAI,GAAG,CAAC,KAAK,EAAE;gBACX,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,EAAE;oBAC7B,KAAK,CAAC,eAAe,GAAG,iBAAiB,CAAC;oBAC1C,OAAO,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;gBAC7D,CAAC,CAAC,CAAC;aACN;iBAAM;gBACH,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,EAAE;oBAC7B,KAAK,CAAC,eAAe,GAAG,iBAAiB,CAAC;oBAC1C,OAAO,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gBAC1C,CAAC,CAAC,CAAC;aACN;YACD,IAAI,GAAG,CAAC,oBAAoB,EAAE;gBAC1B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;aAC9B;SACJ;IACL,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,MAAM,CAAC,CAAC;IACR,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Observer, EventState } from \"./observable\";\r\nimport { Observable } from \"./observable\";\r\n\r\n/**\r\n * Represent a list of observers registered to multiple Observables object.\r\n */\r\nexport class MultiObserver<T> {\r\n    private _observers: Nullable<Observer<T>[]>;\r\n    private _observables: Nullable<Observable<T>[]>;\r\n\r\n    /**\r\n     * Release associated resources\r\n     */\r\n    public dispose(): void {\r\n        if (this._observers && this._observables) {\r\n            for (let index = 0; index < this._observers.length; index++) {\r\n                this._observables[index].remove(this._observers[index]);\r\n            }\r\n        }\r\n\r\n        this._observers = null;\r\n        this._observables = null;\r\n    }\r\n\r\n    /**\r\n     * Raise a callback when one of the observable will notify\r\n     * @param observables defines a list of observables to watch\r\n     * @param callback defines the callback to call on notification\r\n     * @param mask defines the mask used to filter notifications\r\n     * @param scope defines the current scope used to restore the JS context\r\n     * @returns the new MultiObserver\r\n     */\r\n    public static Watch<T>(observables: Observable<T>[], callback: (eventData: T, eventState: EventState) => void, mask: number = -1, scope: any = null): MultiObserver<T> {\r\n        const result = new MultiObserver<T>();\r\n\r\n        result._observers = new Array<Observer<T>>();\r\n        result._observables = observables;\r\n\r\n        for (const observable of observables) {\r\n            const observer = observable.add(callback, mask, false, scope);\r\n            if (observer) {\r\n                result._observers.push(observer);\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n}\r\n\r\ndeclare module \"./observable\" {\r\n    export interface Observable<T> {\r\n        /**\r\n         * Calling this will execute each callback, expecting it to be a promise or return a value.\r\n         * If at any point in the chain one function fails, the promise will fail and the execution will not continue.\r\n         * This is useful when a chain of events (sometimes async events) is needed to initialize a certain object\r\n         * and it is crucial that all callbacks will be executed.\r\n         * The order of the callbacks is kept, callbacks are not executed parallel.\r\n         *\r\n         * @param eventData The data to be sent to each callback\r\n         * @param mask is used to filter observers defaults to -1\r\n         * @param target defines the callback target (see EventState)\r\n         * @param currentTarget defines he current object in the bubbling phase\r\n         * @param userInfo defines any user info to send to observers\r\n         * @returns {Promise<T>} will return a Promise than resolves when all callbacks executed successfully.\r\n         */\r\n        notifyObserversWithPromise(eventData: T, mask?: number, target?: any, currentTarget?: any, userInfo?: any): Promise<T>;\r\n    }\r\n}\r\n\r\nObservable.prototype.notifyObserversWithPromise = async function <T>(eventData: T, mask: number = -1, target?: any, currentTarget?: any, userInfo?: any): Promise<T> {\r\n    // create an empty promise\r\n    let p: Promise<any> = Promise.resolve(eventData);\r\n\r\n    // no observers? return this promise.\r\n    if (!this.observers.length) {\r\n        return p;\r\n    }\r\n\r\n    const state = this._eventState;\r\n    state.mask = mask;\r\n    state.target = target;\r\n    state.currentTarget = currentTarget;\r\n    state.skipNextObservers = false;\r\n    state.userInfo = userInfo;\r\n\r\n    // execute one callback after another (not using Promise.all, the order is important)\r\n    this.observers.forEach((obs) => {\r\n        if (state.skipNextObservers) {\r\n            return;\r\n        }\r\n        if (obs._willBeUnregistered) {\r\n            return;\r\n        }\r\n        if (obs.mask & mask) {\r\n            if (obs.scope) {\r\n                p = p.then((lastReturnedValue) => {\r\n                    state.lastReturnValue = lastReturnedValue;\r\n                    return obs.callback.apply(obs.scope, [eventData, state]);\r\n                });\r\n            } else {\r\n                p = p.then((lastReturnedValue) => {\r\n                    state.lastReturnValue = lastReturnedValue;\r\n                    return obs.callback(eventData, state);\r\n                });\r\n            }\r\n            if (obs.unregisterOnNextCall) {\r\n                this._deferUnregister(obs);\r\n            }\r\n        }\r\n    });\r\n\r\n    // return the eventData\r\n    await p;\r\n    return eventData;\r\n};\r\n"]}