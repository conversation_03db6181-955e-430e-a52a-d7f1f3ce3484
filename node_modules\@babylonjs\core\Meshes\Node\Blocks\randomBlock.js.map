{"version": 3, "file": "randomBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Meshes/Node/Blocks/randomBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AAEzD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,qCAAqC,EAAE,MAAM,2CAA2C,CAAC;AAElG,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AAEvE,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,6CAAsC;AAC/F,OAAO,EAAE,6BAA6B,EAAE,MAAM,wCAAwC,CAAC;AAEvF;;GAEG;AACH,MAAM,CAAN,IAAY,gBAOX;AAPD,WAAY,gBAAgB;IACxB,WAAW;IACX,uDAAI,CAAA;IACJ,aAAa;IACb,2DAAM,CAAA;IACN,iBAAiB;IACjB,mEAAU,CAAA;AACd,CAAC,EAPW,gBAAgB,KAAhB,gBAAgB,QAO3B;AAED;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,iBAAiB;IAe9C;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAnBR,mBAAc,GAAG,CAAC,CAAC,CAAC;QAC5B;;WAEG;QASI,aAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC;QASpC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAE5E,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,YAAY,CAAC,CAAC;QAElF,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,MAAM,CAAC,CAAC;QAChG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,QAAQ,CAAC,CAAC;QAClG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,CAAC;QACjG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,MAAM,CAAC,CAAC;QAChG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,QAAQ,CAAC,CAAC;QAClG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAEjG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa;QAChB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE;YACvB,MAAM,QAAQ,GAAG,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC/C,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;YACnB,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACvC;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE;YACvB,MAAM,QAAQ,GAAG,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC/C,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;YACnB,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACvC;IACL,CAAC;IAES,WAAW;QACjB,IAAI,IAAI,GAAqD,IAAI,CAAC;QAClE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;QAEzB,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;YACnB,KAAK,qCAAqC,CAAC,GAAG,CAAC;YAC/C,KAAK,qCAAqC,CAAC,KAAK,CAAC,CAAC;gBAC9C,IAAI,GAAG,CAAC,KAAK,EAAE,EAAE;oBACb,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACnD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACnD,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;gBAC7C,CAAC,CAAC;gBACF,MAAM;aACT;YACD,KAAK,qCAAqC,CAAC,OAAO,CAAC,CAAC;gBAChD,IAAI,GAAG,CAAC,KAAK,EAAE,EAAE;oBACb,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;oBAChE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;oBAChE,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzG,CAAC,CAAC;gBACF,MAAM;aACT;YACD,KAAK,qCAAqC,CAAC,OAAO,CAAC,CAAC;gBAChD,IAAI,GAAG,CAAC,KAAK,EAAE,EAAE;oBACb,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;oBAChE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;oBAChE,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClJ,CAAC,CAAC;gBACF,MAAM;aACT;YACD,KAAK,qCAAqC,CAAC,OAAO,CAAC,CAAC;gBAChD,IAAI,GAAG,CAAC,KAAK,EAAE,EAAE;oBACb,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;oBAChE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;oBAChE,OAAO,IAAI,OAAO,CACd,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EACvC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EACvC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EACvC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAC1C,CAAC;gBACN,CAAC,CAAC;gBACF,MAAM;aACT;SACJ;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,gBAAgB,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;YAClD,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;SACtC;aAAM;YACH,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;gBACpC,IAAI,MAAM,GAAG,CAAC,CAAC;gBAEf,QAAQ,IAAI,CAAC,QAAQ,EAAE;oBACnB,KAAK,gBAAgB,CAAC,UAAU;wBAC5B,MAAM,GAAG,KAAK,CAAC,kBAAkB,CAAC,6BAA6B,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;wBACvF,MAAM;oBACV,KAAK,gBAAgB,CAAC,MAAM;wBACxB,MAAM,GAAG,KAAK,CAAC,kBAAkB,CAAC,6BAA6B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;wBACnF,MAAM;iBACb;gBAED,IAAI,IAAI,CAAC,cAAc,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,gBAAgB,CAAC,IAAI,EAAE;oBAC3E,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;oBAC7B,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAK,CAAC,KAAK,CAAC,CAAC;iBAC3C;gBACD,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YACpC,CAAC,CAAC;SACL;IACL,CAAC;IAES,mBAAmB;QACzB,MAAM,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,GAAG,GAAG,IAAI,CAAC,iBAAiB,wCAAwC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QACvJ,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE7C,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB;QACxC,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAExC,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC;IACjD,CAAC;CACJ;AA/JU;IARN,sBAAsB,CAAC,UAAU,EAAE,sBAAsB,CAAC,IAAI,EAAE,UAAU,EAAE;QACzE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;QAC5B,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,gBAAgB,CAAC,IAAI,EAAE;YAC/C,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,gBAAgB,CAAC,MAAM,EAAE;YACnD,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,gBAAgB,CAAC,UAAU,EAAE;SAC9D;KACJ,CAAC;6CACsC;AAiK5C,aAAa,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC", "sourcesContent": ["import { NodeGeometryBlock } from \"../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../nodeGeometryBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"../Enums/nodeGeometryConnectionPointTypes\";\r\nimport type { NodeGeometryBuildState } from \"../nodeGeometryBuildState\";\r\nimport { GeometryInputBlock } from \"./geometryInputBlock\";\r\nimport { Vector2, Vector3, Vector4 } from \"../../../Maths/math.vector\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport { PropertyTypeForEdition, editableInPropertyPage } from \"core/Decorators/nodeDecorator\";\r\nimport { NodeGeometryContextualSources } from \"../Enums/nodeGeometryContextualSources\";\r\n\r\n/**\r\n * Locks supported by the random block\r\n */\r\nexport enum RandomBlockLocks {\r\n    /** None */\r\n    None,\r\n    /** LoopID */\r\n    LoopID,\r\n    /** InstanceID */\r\n    InstanceID,\r\n}\r\n\r\n/**\r\n * Block used to get a random number\r\n */\r\nexport class RandomBlock extends NodeGeometryBlock {\r\n    private _currentLockId = -1;\r\n    /**\r\n     * Gets or sets a value indicating if that block will lock its value for a specific duration\r\n     */\r\n    @editableInPropertyPage(\"LockMode\", PropertyTypeForEdition.List, \"ADVANCED\", {\r\n        notifiers: { rebuild: true },\r\n        options: [\r\n            { label: \"None\", value: RandomBlockLocks.None },\r\n            { label: \"LoopID\", value: RandomBlockLocks.LoopID },\r\n            { label: \"InstanceID\", value: RandomBlockLocks.InstanceID },\r\n        ],\r\n    })\r\n    public lockMode = RandomBlockLocks.None;\r\n\r\n    /**\r\n     * Create a new RandomBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"min\", NodeGeometryBlockConnectionPointTypes.AutoDetect);\r\n        this.registerInput(\"max\", NodeGeometryBlockConnectionPointTypes.AutoDetect);\r\n\r\n        this.registerOutput(\"output\", NodeGeometryBlockConnectionPointTypes.BasedOnInput);\r\n\r\n        this._inputs[0].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Matrix);\r\n        this._inputs[0].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Geometry);\r\n        this._inputs[0].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Texture);\r\n        this._inputs[1].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Matrix);\r\n        this._inputs[1].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Geometry);\r\n        this._inputs[1].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Texture);\r\n\r\n        this._outputs[0]._typeConnectionSource = this._inputs[0];\r\n        this._linkConnectionTypes(0, 1);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"RandomBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the min input component\r\n     */\r\n    public get min(): NodeGeometryConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the max input component\r\n     */\r\n    public get max(): NodeGeometryConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the geometry output component\r\n     */\r\n    public get output(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public autoConfigure() {\r\n        if (!this.min.isConnected) {\r\n            const minInput = new GeometryInputBlock(\"Min\");\r\n            minInput.value = 0;\r\n            minInput.output.connectTo(this.min);\r\n        }\r\n\r\n        if (!this.max.isConnected) {\r\n            const maxInput = new GeometryInputBlock(\"Max\");\r\n            maxInput.value = 1;\r\n            maxInput.output.connectTo(this.max);\r\n        }\r\n    }\r\n\r\n    protected _buildBlock() {\r\n        let func: Nullable<(state: NodeGeometryBuildState) => any> = null;\r\n        this._currentLockId = -1;\r\n\r\n        switch (this.min.type) {\r\n            case NodeGeometryBlockConnectionPointTypes.Int:\r\n            case NodeGeometryBlockConnectionPointTypes.Float: {\r\n                func = (state) => {\r\n                    const min = this.min.getConnectedValue(state) || 0;\r\n                    const max = this.max.getConnectedValue(state) || 0;\r\n                    return min + Math.random() * (max - min);\r\n                };\r\n                break;\r\n            }\r\n            case NodeGeometryBlockConnectionPointTypes.Vector2: {\r\n                func = (state) => {\r\n                    const min = this.min.getConnectedValue(state) || Vector2.Zero();\r\n                    const max = this.max.getConnectedValue(state) || Vector2.Zero();\r\n                    return new Vector2(min.x + Math.random() * (max.x - min.x), min.y + Math.random() * (max.y - min.y));\r\n                };\r\n                break;\r\n            }\r\n            case NodeGeometryBlockConnectionPointTypes.Vector3: {\r\n                func = (state) => {\r\n                    const min = this.min.getConnectedValue(state) || Vector3.Zero();\r\n                    const max = this.max.getConnectedValue(state) || Vector3.Zero();\r\n                    return new Vector3(min.x + Math.random() * (max.x - min.x), min.y + Math.random() * (max.y - min.y), min.z + Math.random() * (max.z - min.z));\r\n                };\r\n                break;\r\n            }\r\n            case NodeGeometryBlockConnectionPointTypes.Vector4: {\r\n                func = (state) => {\r\n                    const min = this.min.getConnectedValue(state) || Vector4.Zero();\r\n                    const max = this.max.getConnectedValue(state) || Vector4.Zero();\r\n                    return new Vector4(\r\n                        min.x + Math.random() * (max.x - min.x),\r\n                        min.y + Math.random() * (max.y - min.y),\r\n                        min.z + Math.random() * (max.z - min.z),\r\n                        min.w + Math.random() * (max.w - min.w)\r\n                    );\r\n                };\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (this.lockMode === RandomBlockLocks.None || !func) {\r\n            this.output._storedFunction = func;\r\n        } else {\r\n            this.output._storedFunction = (state) => {\r\n                let lockId = 0;\r\n\r\n                switch (this.lockMode) {\r\n                    case RandomBlockLocks.InstanceID:\r\n                        lockId = state.getContextualValue(NodeGeometryContextualSources.InstanceID, true) || 0;\r\n                        break;\r\n                    case RandomBlockLocks.LoopID:\r\n                        lockId = state.getContextualValue(NodeGeometryContextualSources.LoopID, true) || 0;\r\n                        break;\r\n                }\r\n\r\n                if (this._currentLockId !== lockId || this.lockMode === RandomBlockLocks.None) {\r\n                    this._currentLockId = lockId;\r\n                    this.output._storedValue = func!(state);\r\n                }\r\n                return this.output._storedValue;\r\n            };\r\n        }\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        const codeString = super._dumpPropertiesCode() + `${this._codeVariableName}.lockMode = BABYLON.RandomBlockLocks.${RandomBlockLocks[this.lockMode]};\\n`;\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Serializes this block in a JSON representation\r\n     * @returns the serialized block object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.lockMode = this.lockMode;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any) {\r\n        super._deserialize(serializationObject);\r\n\r\n        this.lockMode = serializationObject.lockMode;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.RandomBlock\", RandomBlock);\r\n"]}