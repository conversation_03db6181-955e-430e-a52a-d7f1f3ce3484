{"version": 3, "file": "shadowMap.vertex.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Shaders/shadowMap.vertex.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,mCAAmC,CAAC;AAC3C,OAAO,kDAAkD,CAAC;AAC1D,OAAO,sDAAsD,CAAC;AAC9D,OAAO,gDAAgD,CAAC;AACxD,OAAO,kCAAkC,CAAC;AAC1C,OAAO,6CAA6C,CAAC;AACrD,OAAO,0CAA0C,CAAC;AAClD,OAAO,kDAAkD,CAAC;AAC1D,OAAO,6CAA6C,CAAC;AACrD,OAAO,2CAA2C,CAAC;AACnD,OAAO,qCAAqC,CAAC;AAC7C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,8BAA8B,CAAC;AACtC,OAAO,uCAAuC,CAAC;AAC/C,OAAO,4CAA4C,CAAC;AACpD,OAAO,wCAAwC,CAAC;AAChD,OAAO,kCAAkC,CAAC;AAE1C,MAAM,IAAI,GAAG,uBAAuB,CAAC;AACrC,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8Db,CAAC;AACH,aAAa;AACb,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACxC,gBAAgB;AAChB,MAAM,CAAC,MAAM,qBAAqB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore\";\nimport \"./ShadersInclude/bonesDeclaration\";\nimport \"./ShadersInclude/bakedVertexAnimationDeclaration\";\nimport \"./ShadersInclude/morphTargetsVertexGlobalDeclaration\";\nimport \"./ShadersInclude/morphTargetsVertexDeclaration\";\nimport \"./ShadersInclude/helperFunctions\";\nimport \"./ShadersInclude/shadowMapVertexDeclaration\";\nimport \"./ShadersInclude/shadowMapUboDeclaration\";\nimport \"./ShadersInclude/shadowMapVertexExtraDeclaration\";\nimport \"./ShadersInclude/clipPlaneVertexDeclaration\";\nimport \"./ShadersInclude/morphTargetsVertexGlobal\";\nimport \"./ShadersInclude/morphTargetsVertex\";\nimport \"./ShadersInclude/instancesVertex\";\nimport \"./ShadersInclude/bonesVertex\";\nimport \"./ShadersInclude/bakedVertexAnimation\";\nimport \"./ShadersInclude/shadowMapVertexNormalBias\";\nimport \"./ShadersInclude/shadowMapVertexMetric\";\nimport \"./ShadersInclude/clipPlaneVertex\";\n\nconst name = \"shadowMapVertexShader\";\nconst shader = `attribute vec3 position;\n#ifdef NORMAL\nattribute vec3 normal;\n#endif\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<morphTargetsVertexGlobalDeclaration>\n#include<morphTargetsVertexDeclaration>[0..maxSimultaneousMorphTargets]\n#ifdef INSTANCES\nattribute vec4 world0;attribute vec4 world1;attribute vec4 world2;attribute vec4 world3;\n#endif\n#include<helperFunctions>\n#include<__decl__shadowMapVertex>\n#ifdef ALPHATEXTURE\nvarying vec2 vUV;uniform mat4 diffuseMatrix;\n#ifdef UV1\nattribute vec2 uv;\n#endif\n#ifdef UV2\nattribute vec2 uv2;\n#endif\n#endif\n#include<shadowMapVertexExtraDeclaration>\n#include<clipPlaneVertexDeclaration>\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void)\n{vec3 positionUpdated=position;\n#ifdef UV1\nvec2 uvUpdated=uv;\n#endif\n#ifdef NORMAL\nvec3 normalUpdated=normal;\n#endif\n#include<morphTargetsVertexGlobal>\n#include<morphTargetsVertex>[0..maxSimultaneousMorphTargets]\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nvec4 worldPos=finalWorld*vec4(positionUpdated,1.0);\n#ifdef NORMAL\nmat3 normWorldSM=mat3(finalWorld);\n#if defined(INSTANCES) && defined(THIN_INSTANCES)\nvec3 vNormalW=normalUpdated/vec3(dot(normWorldSM[0],normWorldSM[0]),dot(normWorldSM[1],normWorldSM[1]),dot(normWorldSM[2],normWorldSM[2]));vNormalW=normalize(normWorldSM*vNormalW);\n#else\n#ifdef NONUNIFORMSCALING\nnormWorldSM=transposeMat3(inverseMat3(normWorldSM));\n#endif\nvec3 vNormalW=normalize(normWorldSM*normalUpdated);\n#endif\n#endif\n#include<shadowMapVertexNormalBias>\ngl_Position=viewProjection*worldPos;\n#include<shadowMapVertexMetric>\n#ifdef ALPHATEXTURE\n#ifdef UV1\nvUV=vec2(diffuseMatrix*vec4(uvUpdated,1.0,0.0));\n#endif\n#ifdef UV2\nvUV=vec2(diffuseMatrix*vec4(uv2,1.0,0.0));\n#endif\n#endif\n#include<clipPlaneVertex>\n}`;\n// Sideeffect\nShaderStore.ShadersStore[name] = shader;\n/** @internal */\nexport const shadowMapVertexShader = { name, shader };\n"]}