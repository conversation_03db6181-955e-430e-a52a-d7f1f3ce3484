{"version": 3, "file": "webgpuCacheRenderPipelineString.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuCacheRenderPipelineString.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAExE;;;GAGG;AACH,MAAM,OAAO,+BAAgC,SAAQ,yBAAyB;IAGhE,kBAAkB,CAAC,KAA4D;QACrF,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACjC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;QACnB,KAAK,CAAC,QAAQ,GAAG,+BAA+B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAClE,CAAC;IAES,kBAAkB,CAAC,KAA4D;QACrF,+BAA+B,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,QAAS,CAAC;IAC1E,CAAC;;AAVc,sCAAM,GAA0C,EAAE,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { WebGPUCacheRenderPipeline } from \"./webgpuCacheRenderPipeline\";\r\n\r\n/**\r\n * Class not used, WebGPUCacheRenderPipelineTree is faster\r\n * @internal\r\n */\r\nexport class WebGPUCacheRenderPipelineString extends WebGPUCacheRenderPipeline {\r\n    private static _Cache: { [hash: string]: GPURenderPipeline } = {};\r\n\r\n    protected _getRenderPipeline(param: { token: any; pipeline: Nullable<GPURenderPipeline> }): void {\r\n        const hash = this._states.join();\r\n        param.token = hash;\r\n        param.pipeline = WebGPUCacheRenderPipelineString._Cache[hash];\r\n    }\r\n\r\n    protected _setRenderPipeline(param: { token: any; pipeline: Nullable<GPURenderPipeline> }): void {\r\n        WebGPUCacheRenderPipelineString._Cache[param.token] = param.pipeline!;\r\n    }\r\n}\r\n"]}