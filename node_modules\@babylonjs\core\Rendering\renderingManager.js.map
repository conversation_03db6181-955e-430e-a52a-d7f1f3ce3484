{"version": 3, "file": "renderingManager.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Rendering/renderingManager.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AA2BlD;;GAEG;AACH,MAAM,OAAO,kBAAkB;CAe9B;AAED;;;;GAIG;AACH,MAAM,OAAO,gBAAgB;IAgCzB;;;;;OAKG;IACH,IAAW,0BAA0B;QACjC,OAAO,IAAI,CAAC,2BAA2B,CAAC;IAC5C,CAAC;IAED,IAAW,0BAA0B,CAAC,KAAc;QAChD,IAAI,KAAK,KAAK,IAAI,CAAC,2BAA2B,EAAE;YAC5C,OAAO;SACV;QAED,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE;YACnC,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAChC;IACL,CAAC;IAED;;OAEG;IACI,qBAAqB;QACxB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACnC,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;iBAClC;aACJ;SACJ;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;YAC5B,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;gBACpD,aAAa,CAAC,cAAc,GAAG,KAAK,CAAC;aACxC;SACJ;QAED,KAAK,MAAM,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;YACtD,cAAc,CAAC,cAAc,GAAG,KAAK,CAAC;SACzC;IACL,CAAC;IAED;;;OAGG;IACH,YAAY,KAAY;QAhExB;;WAEG;QACI,4BAAuB,GAAG,KAAK,CAAC;QAG/B,qBAAgB,GAAG,IAAI,KAAK,EAAkB,CAAC;QAG/C,2BAAsB,GAAsD,EAAE,CAAC;QAC/E,+BAA0B,GAAmE,EAAE,CAAC;QAChG,kCAA6B,GAAmE,EAAE,CAAC;QACnG,oCAA+B,GAAmE,EAAE,CAAC;QACrG,wBAAmB,GAAiC,IAAI,kBAAkB,EAAE,CAAC;QAE7E,gCAA2B,GAAG,KAAK,CAAC;QAkDxC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,KAAK,IAAI,CAAC,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,EAAE,EAAE;YAC9F,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;SACpF;IACL,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,EAAU;QAC/B,MAAM,gBAAgB,GAAG,EAAE,IAAI,CAAC,CAAC;QAEjC,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;QAE9C,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;IACnD,CAAC;IAEO,wBAAwB,CAAC,KAAK,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI;QACzD,IAAI,IAAI,CAAC,iCAAiC,EAAE;YACxC,OAAO;SACV;QAED,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC3D,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;IAClD,CAAC;IAED;;;OAGG;IACI,MAAM,CACT,oBAOC,EACD,YAAsC,EACtC,eAAwB,EACxB,aAAsB;QAEtB,2EAA2E;QAC3E,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAoB,CAAC;QACvC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAEvC,mBAAmB;QACnB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,aAAa,EAAE;YAC7C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACpE,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAClD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;aACjC;SACJ;QAED,SAAS;QACT,KAAK,IAAI,KAAK,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,KAAK,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,KAAK,EAAE,EAAE;YAC1G,IAAI,CAAC,iCAAiC,GAAG,KAAK,KAAK,gBAAgB,CAAC,mBAAmB,CAAC;YACxF,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACpD,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE;gBAC1C,SAAS;aACZ;YAED,MAAM,kBAAkB,GAAG,CAAC,IAAI,KAAK,CAAC;YACtC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAE9B,oBAAoB;YACpB,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,eAAe,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;YAEvF,gCAAgC;YAChC,IAAI,gBAAgB,CAAC,SAAS,EAAE;gBAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;gBAEvI,IAAI,SAAS,IAAI,SAAS,CAAC,SAAS,EAAE;oBAClC,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;iBACrE;aACJ;YAED,SAAS;YACT,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,8BAA8B,EAAE;gBAC3D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACtB;YACD,cAAc,CAAC,MAAM,CAAC,oBAAoB,EAAE,aAAa,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;YAC1F,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE;gBAC1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACtB;YAED,mBAAmB;YACnB,IAAI,CAAC,MAAM,CAAC,+BAA+B,CAAC,eAAe,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;SACzF;IACL,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,OAAO;SACV;QAED,KAAK,IAAI,KAAK,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,KAAK,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,KAAK,EAAE,EAAE;YAC1G,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACpD,IAAI,cAAc,EAAE;gBAChB,cAAc,CAAC,OAAO,EAAE,CAAC;aAC5B;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,OAAO;SACV;QAED,KAAK,IAAI,KAAK,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,KAAK,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,KAAK,EAAE,EAAE;YAC1G,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACpD,IAAI,cAAc,EAAE;gBAChB,cAAc,CAAC,cAAc,EAAE,CAAC;aACnC;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,KAAK,IAAI,KAAK,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,KAAK,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,KAAK,EAAE,EAAE;YAC1G,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACpD,IAAI,cAAc,EAAE;gBAChB,cAAc,CAAC,OAAO,EAAE,CAAC;aAC5B;SACJ;IACL,CAAC;IAEO,sBAAsB,CAAC,gBAAwB;QACnD,IAAI,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,SAAS,EAAE;YACvD,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,IAAI,cAAc,CACxD,gBAAgB,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,EACjD,IAAI,CAAC,6BAA6B,CAAC,gBAAgB,CAAC,EACpD,IAAI,CAAC,+BAA+B,CAAC,gBAAgB,CAAC,CACzD,CAAC;SACL;IACL,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,aAA6B;QAChD,IAAI,IAAI,CAAC,0BAA0B,IAAI,aAAa,CAAC,cAAc,EAAE;YACjE,OAAO;SACV;QACD,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;IAC1F,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,cAA+B;QACpD,IAAI,IAAI,CAAC,0BAA0B,IAAI,cAAc,CAAC,cAAc,EAAE;YAClE,OAAO;SACV;QACD,cAAc,CAAC,cAAc,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAC9F,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,OAAgB,EAAE,IAAmB,EAAE,QAA6B;QAChF,IAAI,IAAI,KAAK,SAAS,EAAE;YACpB,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QACD,IAAI,IAAI,CAAC,0BAA0B,IAAI,OAAO,CAAC,cAAc,EAAE;YAC3D,OAAO;SACV;QACD,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IACpF,CAAC;IAED;;;;;;;;OAQG;IACI,iBAAiB,CACpB,gBAAwB,EACxB,sBAAoE,IAAI,EACxE,yBAAuE,IAAI,EAC3E,2BAAyE,IAAI;QAE7E,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,GAAG,mBAAmB,CAAC;QACxE,IAAI,CAAC,6BAA6B,CAAC,gBAAgB,CAAC,GAAG,sBAAsB,CAAC;QAC9E,IAAI,CAAC,+BAA+B,CAAC,gBAAgB,CAAC,GAAG,wBAAwB,CAAC;QAElF,IAAI,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,EAAE;YACzC,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YACtD,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,CAAC;YAC9E,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,6BAA6B,CAAC,gBAAgB,CAAC,CAAC;YACpF,KAAK,CAAC,wBAAwB,GAAG,IAAI,CAAC,+BAA+B,CAAC,gBAAgB,CAAC,CAAC;SAC3F;IACL,CAAC;IAED;;;;;;;OAOG;IACI,iCAAiC,CAAC,gBAAwB,EAAE,qBAA8B,EAAE,KAAK,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI;QAC3H,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,GAAG;YAC5C,SAAS,EAAE,qBAAqB;YAChC,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,OAAO;SACnB,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACI,6BAA6B,CAAC,KAAa;QAC9C,OAAO,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC;;AA/UD;;GAEG;AACW,oCAAmB,GAAG,CAAC,AAAJ,CAAK;AAEtC;;GAEG;AACW,oCAAmB,GAAG,CAAC,AAAJ,CAAK;AAEtC;;GAEG;AACW,0BAAS,GAAG,IAAI,AAAP,CAAQ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { SmartArray } from \"../Misc/smartArray\";\r\nimport type { ISpriteManager } from \"../Sprites/spriteManager\";\r\nimport type { IParticleSystem } from \"../Particles/IParticleSystem\";\r\nimport { RenderingGroup } from \"./renderingGroup\";\r\n\r\nimport type { Scene } from \"../scene\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Material } from \"../Materials/material\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\n\r\n/**\r\n * Interface describing the different options available in the rendering manager\r\n * regarding Auto Clear between groups.\r\n */\r\nexport interface IRenderingManagerAutoClearSetup {\r\n    /**\r\n     * Defines whether or not autoclear is enable.\r\n     */\r\n    autoClear: boolean;\r\n    /**\r\n     * Defines whether or not to autoclear the depth buffer.\r\n     */\r\n    depth: boolean;\r\n    /**\r\n     * Defines whether or not to autoclear the stencil buffer.\r\n     */\r\n    stencil: boolean;\r\n}\r\n\r\n/**\r\n * This class is used by the onRenderingGroupObservable\r\n */\r\nexport class RenderingGroupInfo {\r\n    /**\r\n     * The Scene that being rendered\r\n     */\r\n    scene: Scene;\r\n\r\n    /**\r\n     * The camera currently used for the rendering pass\r\n     */\r\n    camera: Nullable<Camera>;\r\n\r\n    /**\r\n     * The ID of the renderingGroup being processed\r\n     */\r\n    renderingGroupId: number;\r\n}\r\n\r\n/**\r\n * This is the manager responsible of all the rendering for meshes sprites and particles.\r\n * It is enable to manage the different groups as well as the different necessary sort functions.\r\n * This should not be used directly aside of the few static configurations\r\n */\r\nexport class RenderingManager {\r\n    /**\r\n     * The max id used for rendering groups (not included)\r\n     */\r\n    public static MAX_RENDERINGGROUPS = 4;\r\n\r\n    /**\r\n     * The min id used for rendering groups (included)\r\n     */\r\n    public static MIN_RENDERINGGROUPS = 0;\r\n\r\n    /**\r\n     * Used to globally prevent autoclearing scenes.\r\n     */\r\n    public static AUTOCLEAR = true;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _useSceneAutoClearSetup = false;\r\n\r\n    private _scene: Scene;\r\n    private _renderingGroups = new Array<RenderingGroup>();\r\n    private _depthStencilBufferAlreadyCleaned: boolean;\r\n\r\n    private _autoClearDepthStencil: { [id: number]: IRenderingManagerAutoClearSetup } = {};\r\n    private _customOpaqueSortCompareFn: { [id: number]: Nullable<(a: SubMesh, b: SubMesh) => number> } = {};\r\n    private _customAlphaTestSortCompareFn: { [id: number]: Nullable<(a: SubMesh, b: SubMesh) => number> } = {};\r\n    private _customTransparentSortCompareFn: { [id: number]: Nullable<(a: SubMesh, b: SubMesh) => number> } = {};\r\n    private _renderingGroupInfo: Nullable<RenderingGroupInfo> = new RenderingGroupInfo();\r\n\r\n    private _maintainStateBetweenFrames = false;\r\n    /**\r\n     * Gets or sets a boolean indicating that the manager will not reset between frames.\r\n     * This means that if a mesh becomes invisible or transparent it will not be visible until this boolean is set to false again.\r\n     * By default, the rendering manager will dispatch all active meshes per frame (moving them to the transparent, opaque or alpha testing lists).\r\n     * By turning this property on, you will accelerate the rendering by keeping all these lists unchanged between frames.\r\n     */\r\n    public get maintainStateBetweenFrames() {\r\n        return this._maintainStateBetweenFrames;\r\n    }\r\n\r\n    public set maintainStateBetweenFrames(value: boolean) {\r\n        if (value === this._maintainStateBetweenFrames) {\r\n            return;\r\n        }\r\n\r\n        this._maintainStateBetweenFrames = value;\r\n        if (!this._maintainStateBetweenFrames) {\r\n            this.restoreDispachedFlags();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Restore wasDispatched flags on the lists of elements to render.\r\n     */\r\n    public restoreDispachedFlags() {\r\n        for (const mesh of this._scene.meshes) {\r\n            if (mesh.subMeshes) {\r\n                for (const subMesh of mesh.subMeshes) {\r\n                    subMesh._wasDispatched = false;\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._scene.spriteManagers) {\r\n            for (const spriteManager of this._scene.spriteManagers) {\r\n                spriteManager._wasDispatched = false;\r\n            }\r\n        }\r\n\r\n        for (const particleSystem of this._scene.particleSystems) {\r\n            particleSystem._wasDispatched = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new rendering group for a particular scene\r\n     * @param scene Defines the scene the groups belongs to\r\n     */\r\n    constructor(scene: Scene) {\r\n        this._scene = scene;\r\n\r\n        for (let i = RenderingManager.MIN_RENDERINGGROUPS; i < RenderingManager.MAX_RENDERINGGROUPS; i++) {\r\n            this._autoClearDepthStencil[i] = { autoClear: true, depth: true, stencil: true };\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @returns the rendering group with the specified id.\r\n     * @param id the id of the rendering group (0 by default)\r\n     */\r\n    public getRenderingGroup(id: number): RenderingGroup {\r\n        const renderingGroupId = id || 0;\r\n\r\n        this._prepareRenderingGroup(renderingGroupId);\r\n\r\n        return this._renderingGroups[renderingGroupId];\r\n    }\r\n\r\n    private _clearDepthStencilBuffer(depth = true, stencil = true): void {\r\n        if (this._depthStencilBufferAlreadyCleaned) {\r\n            return;\r\n        }\r\n\r\n        this._scene.getEngine().clear(null, false, depth, stencil);\r\n        this._depthStencilBufferAlreadyCleaned = true;\r\n    }\r\n\r\n    /**\r\n     * Renders the entire managed groups. This is used by the scene or the different render targets.\r\n     * @internal\r\n     */\r\n    public render(\r\n        customRenderFunction: Nullable<\r\n            (\r\n                opaqueSubMeshes: SmartArray<SubMesh>,\r\n                transparentSubMeshes: SmartArray<SubMesh>,\r\n                alphaTestSubMeshes: SmartArray<SubMesh>,\r\n                depthOnlySubMeshes: SmartArray<SubMesh>\r\n            ) => void\r\n        >,\r\n        activeMeshes: Nullable<AbstractMesh[]>,\r\n        renderParticles: boolean,\r\n        renderSprites: boolean\r\n    ): void {\r\n        // Update the observable context (not null as it only goes away on dispose)\r\n        const info = this._renderingGroupInfo!;\r\n        info.scene = this._scene;\r\n        info.camera = this._scene.activeCamera;\r\n\r\n        // Dispatch sprites\r\n        if (this._scene.spriteManagers && renderSprites) {\r\n            for (let index = 0; index < this._scene.spriteManagers.length; index++) {\r\n                const manager = this._scene.spriteManagers[index];\r\n                this.dispatchSprites(manager);\r\n            }\r\n        }\r\n\r\n        // Render\r\n        for (let index = RenderingManager.MIN_RENDERINGGROUPS; index < RenderingManager.MAX_RENDERINGGROUPS; index++) {\r\n            this._depthStencilBufferAlreadyCleaned = index === RenderingManager.MIN_RENDERINGGROUPS;\r\n            const renderingGroup = this._renderingGroups[index];\r\n            if (!renderingGroup || renderingGroup._empty) {\r\n                continue;\r\n            }\r\n\r\n            const renderingGroupMask = 1 << index;\r\n            info.renderingGroupId = index;\r\n\r\n            // Before Observable\r\n            this._scene.onBeforeRenderingGroupObservable.notifyObservers(info, renderingGroupMask);\r\n\r\n            // Clear depth/stencil if needed\r\n            if (RenderingManager.AUTOCLEAR) {\r\n                const autoClear = this._useSceneAutoClearSetup ? this._scene.getAutoClearDepthStencilSetup(index) : this._autoClearDepthStencil[index];\r\n\r\n                if (autoClear && autoClear.autoClear) {\r\n                    this._clearDepthStencilBuffer(autoClear.depth, autoClear.stencil);\r\n                }\r\n            }\r\n\r\n            // Render\r\n            for (const step of this._scene._beforeRenderingGroupDrawStage) {\r\n                step.action(index);\r\n            }\r\n            renderingGroup.render(customRenderFunction, renderSprites, renderParticles, activeMeshes);\r\n            for (const step of this._scene._afterRenderingGroupDrawStage) {\r\n                step.action(index);\r\n            }\r\n\r\n            // After Observable\r\n            this._scene.onAfterRenderingGroupObservable.notifyObservers(info, renderingGroupMask);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Resets the different information of the group to prepare a new frame\r\n     * @internal\r\n     */\r\n    public reset(): void {\r\n        if (this.maintainStateBetweenFrames) {\r\n            return;\r\n        }\r\n\r\n        for (let index = RenderingManager.MIN_RENDERINGGROUPS; index < RenderingManager.MAX_RENDERINGGROUPS; index++) {\r\n            const renderingGroup = this._renderingGroups[index];\r\n            if (renderingGroup) {\r\n                renderingGroup.prepare();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Resets the sprites information of the group to prepare a new frame\r\n     * @internal\r\n     */\r\n    public resetSprites(): void {\r\n        if (this.maintainStateBetweenFrames) {\r\n            return;\r\n        }\r\n\r\n        for (let index = RenderingManager.MIN_RENDERINGGROUPS; index < RenderingManager.MAX_RENDERINGGROUPS; index++) {\r\n            const renderingGroup = this._renderingGroups[index];\r\n            if (renderingGroup) {\r\n                renderingGroup.prepareSprites();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Dispose and release the group and its associated resources.\r\n     * @internal\r\n     */\r\n    public dispose(): void {\r\n        this.freeRenderingGroups();\r\n        this._renderingGroups.length = 0;\r\n        this._renderingGroupInfo = null;\r\n    }\r\n\r\n    /**\r\n     * Clear the info related to rendering groups preventing retention points during dispose.\r\n     */\r\n    public freeRenderingGroups(): void {\r\n        for (let index = RenderingManager.MIN_RENDERINGGROUPS; index < RenderingManager.MAX_RENDERINGGROUPS; index++) {\r\n            const renderingGroup = this._renderingGroups[index];\r\n            if (renderingGroup) {\r\n                renderingGroup.dispose();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _prepareRenderingGroup(renderingGroupId: number): void {\r\n        if (this._renderingGroups[renderingGroupId] === undefined) {\r\n            this._renderingGroups[renderingGroupId] = new RenderingGroup(\r\n                renderingGroupId,\r\n                this._scene,\r\n                this._customOpaqueSortCompareFn[renderingGroupId],\r\n                this._customAlphaTestSortCompareFn[renderingGroupId],\r\n                this._customTransparentSortCompareFn[renderingGroupId]\r\n            );\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Add a sprite manager to the rendering manager in order to render it this frame.\r\n     * @param spriteManager Define the sprite manager to render\r\n     */\r\n    public dispatchSprites(spriteManager: ISpriteManager) {\r\n        if (this.maintainStateBetweenFrames && spriteManager._wasDispatched) {\r\n            return;\r\n        }\r\n        spriteManager._wasDispatched = true;\r\n        this.getRenderingGroup(spriteManager.renderingGroupId).dispatchSprites(spriteManager);\r\n    }\r\n\r\n    /**\r\n     * Add a particle system to the rendering manager in order to render it this frame.\r\n     * @param particleSystem Define the particle system to render\r\n     */\r\n    public dispatchParticles(particleSystem: IParticleSystem) {\r\n        if (this.maintainStateBetweenFrames && particleSystem._wasDispatched) {\r\n            return;\r\n        }\r\n        particleSystem._wasDispatched = true;\r\n        this.getRenderingGroup(particleSystem.renderingGroupId).dispatchParticles(particleSystem);\r\n    }\r\n\r\n    /**\r\n     * Add a submesh to the manager in order to render it this frame\r\n     * @param subMesh The submesh to dispatch\r\n     * @param mesh Optional reference to the submeshes's mesh. Provide if you have an exiting reference to improve performance.\r\n     * @param material Optional reference to the submeshes's material. Provide if you have an exiting reference to improve performance.\r\n     */\r\n    public dispatch(subMesh: SubMesh, mesh?: AbstractMesh, material?: Nullable<Material>): void {\r\n        if (mesh === undefined) {\r\n            mesh = subMesh.getMesh();\r\n        }\r\n        if (this.maintainStateBetweenFrames && subMesh._wasDispatched) {\r\n            return;\r\n        }\r\n        subMesh._wasDispatched = true;\r\n        this.getRenderingGroup(mesh.renderingGroupId).dispatch(subMesh, mesh, material);\r\n    }\r\n\r\n    /**\r\n     * Overrides the default sort function applied in the rendering group to prepare the meshes.\r\n     * This allowed control for front to back rendering or reversely depending of the special needs.\r\n     *\r\n     * @param renderingGroupId The rendering group id corresponding to its index\r\n     * @param opaqueSortCompareFn The opaque queue comparison function use to sort.\r\n     * @param alphaTestSortCompareFn The alpha test queue comparison function use to sort.\r\n     * @param transparentSortCompareFn The transparent queue comparison function use to sort.\r\n     */\r\n    public setRenderingOrder(\r\n        renderingGroupId: number,\r\n        opaqueSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null,\r\n        alphaTestSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null,\r\n        transparentSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null\r\n    ) {\r\n        this._customOpaqueSortCompareFn[renderingGroupId] = opaqueSortCompareFn;\r\n        this._customAlphaTestSortCompareFn[renderingGroupId] = alphaTestSortCompareFn;\r\n        this._customTransparentSortCompareFn[renderingGroupId] = transparentSortCompareFn;\r\n\r\n        if (this._renderingGroups[renderingGroupId]) {\r\n            const group = this._renderingGroups[renderingGroupId];\r\n            group.opaqueSortCompareFn = this._customOpaqueSortCompareFn[renderingGroupId];\r\n            group.alphaTestSortCompareFn = this._customAlphaTestSortCompareFn[renderingGroupId];\r\n            group.transparentSortCompareFn = this._customTransparentSortCompareFn[renderingGroupId];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Specifies whether or not the stencil and depth buffer are cleared between two rendering groups.\r\n     *\r\n     * @param renderingGroupId The rendering group id corresponding to its index\r\n     * @param autoClearDepthStencil Automatically clears depth and stencil between groups if true.\r\n     * @param depth Automatically clears depth between groups if true and autoClear is true.\r\n     * @param stencil Automatically clears stencil between groups if true and autoClear is true.\r\n     */\r\n    public setRenderingAutoClearDepthStencil(renderingGroupId: number, autoClearDepthStencil: boolean, depth = true, stencil = true): void {\r\n        this._autoClearDepthStencil[renderingGroupId] = {\r\n            autoClear: autoClearDepthStencil,\r\n            depth: depth,\r\n            stencil: stencil,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the current auto clear configuration for one rendering group of the rendering\r\n     * manager.\r\n     * @param index the rendering group index to get the information for\r\n     * @returns The auto clear setup for the requested rendering group\r\n     */\r\n    public getAutoClearDepthStencilSetup(index: number): IRenderingManagerAutoClearSetup {\r\n        return this._autoClearDepthStencil[index];\r\n    }\r\n}\r\n"]}