{"version": 3, "file": "subSurfaceScatteringPostProcess.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/PostProcesses/subSurfaceScatteringPostProcess.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAExD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAG5C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,qCAAqC,CAAC;AAC7C,OAAO,0CAA0C,CAAC;AAClD,OAAO,+BAA+B,CAAC;AAEvC;;GAEG;AACH,MAAM,OAAO,+BAAgC,SAAQ,WAAW;IAC5D;;;OAGG;IACI,YAAY;QACf,OAAO,iCAAiC,CAAC;IAC7C,CAAC;IAED,YACI,IAAY,EACZ,KAAY,EACZ,OAAoC,EACpC,SAA2B,IAAI,EAC/B,YAAqB,EACrB,MAAe,EACf,QAAkB,EAClB,cAAsB,SAAS,CAAC,wBAAwB;QAExD,KAAK,CACD,IAAI,EACJ,sBAAsB,EACtB,CAAC,WAAW,EAAE,cAAc,EAAE,eAAe,CAAC,EAC9C,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,mBAAmB,EAAE,cAAc,EAAE,eAAe,CAAC,EACjG,OAAO,EACP,MAAM,EACN,YAAY,IAAI,OAAO,CAAC,qBAAqB,EAC7C,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,WAAW,EACX,aAAa,EACb,SAAS,EACT,IAAI,CACP,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAc,EAAE,EAAE;YAC1C,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBAC1D,MAAM,CAAC,KAAK,CAAC,qFAAqF,CAAC,CAAC;gBACpG,OAAO;aACV;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACjC,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,KAAK,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;YAC9E,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;YACxD,MAAM,CAAC,UAAU,CAAC,mBAAmB,EAAE,KAAK,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC;YACpK,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,KAAK,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC;YAC1J,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,KAAK,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC,CAAC,CAAC;YACjK,MAAM,CAAC,SAAS,CACZ,cAAc,EACd,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,YAAa,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,YAAa,EAAE,IAAI,CAAC,EACnG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,YAAa,CAAC,GAAG,GAAG,CAAC,CAAC,CACxC,CAAC;YACF,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;YAC3E,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE,KAAK,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;YAC1E,MAAM,CAAC,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IACP,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport type { PostProcessOptions } from \"./postProcess\";\r\nimport { PostProcess } from \"./postProcess\";\r\nimport type { Engine } from \"../Engines/engine\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { Logger } from \"../Misc/logger\";\r\n\r\nimport \"../Shaders/imageProcessing.fragment\";\r\nimport \"../Shaders/subSurfaceScattering.fragment\";\r\nimport \"../Shaders/postprocess.vertex\";\r\n\r\n/**\r\n * Sub surface scattering post process\r\n */\r\nexport class SubSurfaceScatteringPostProcess extends PostProcess {\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"SubSurfaceScatteringPostProcess\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"SubSurfaceScatteringPostProcess\";\r\n    }\r\n\r\n    constructor(\r\n        name: string,\r\n        scene: Scene,\r\n        options: number | PostProcessOptions,\r\n        camera: Nullable<Camera> = null,\r\n        samplingMode?: number,\r\n        engine?: Engine,\r\n        reusable?: boolean,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_INT\r\n    ) {\r\n        super(\r\n            name,\r\n            \"subSurfaceScattering\",\r\n            [\"texelSize\", \"viewportSize\", \"metersPerUnit\"],\r\n            [\"diffusionS\", \"diffusionD\", \"filterRadii\", \"irradianceSampler\", \"depthSampler\", \"albedoSampler\"],\r\n            options,\r\n            camera,\r\n            samplingMode || Texture.BILINEAR_SAMPLINGMODE,\r\n            engine,\r\n            reusable,\r\n            null,\r\n            textureType,\r\n            \"postprocess\",\r\n            undefined,\r\n            true\r\n        );\r\n        this._scene = scene;\r\n\r\n        this.updateEffect();\r\n\r\n        this.onApplyObservable.add((effect: Effect) => {\r\n            if (!scene.prePassRenderer || !scene.subSurfaceConfiguration) {\r\n                Logger.Error(\"PrePass and subsurface configuration needs to be enabled for subsurface scattering.\");\r\n                return;\r\n            }\r\n            const texelSize = this.texelSize;\r\n            effect.setFloat(\"metersPerUnit\", scene.subSurfaceConfiguration.metersPerUnit);\r\n            effect.setFloat2(\"texelSize\", texelSize.x, texelSize.y);\r\n            effect.setTexture(\"irradianceSampler\", scene.prePassRenderer.getRenderTarget().textures[scene.prePassRenderer.getIndex(Constants.PREPASS_IRRADIANCE_TEXTURE_TYPE)]);\r\n            effect.setTexture(\"depthSampler\", scene.prePassRenderer.getRenderTarget().textures[scene.prePassRenderer.getIndex(Constants.PREPASS_DEPTH_TEXTURE_TYPE)]);\r\n            effect.setTexture(\"albedoSampler\", scene.prePassRenderer.getRenderTarget().textures[scene.prePassRenderer.getIndex(Constants.PREPASS_ALBEDO_SQRT_TEXTURE_TYPE)]);\r\n            effect.setFloat2(\r\n                \"viewportSize\",\r\n                Math.tan(scene.activeCamera!.fov / 2) * scene.getEngine().getAspectRatio(scene.activeCamera!, true),\r\n                Math.tan(scene.activeCamera!.fov / 2)\r\n            );\r\n            effect.setArray3(\"diffusionS\", scene.subSurfaceConfiguration.ssDiffusionS);\r\n            effect.setArray(\"diffusionD\", scene.subSurfaceConfiguration.ssDiffusionD);\r\n            effect.setArray(\"filterRadii\", scene.subSurfaceConfiguration.ssFilterRadii);\r\n        });\r\n    }\r\n}\r\n"]}