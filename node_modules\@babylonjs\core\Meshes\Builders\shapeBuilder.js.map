{"version": 3, "file": "shapeBuilder.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Builders/shapeBuilder.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AACtE,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAE/C;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,MAAM,UAAU,YAAY,CACxB,IAAY,EACZ,OAgBC,EACD,QAAyB,IAAI;IAE7B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAC1B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC5B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC;IACjC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;IACvC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC;IAC/D,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;IACpC,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACjF,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC;IAC1C,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC;IAC3C,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC;IAC/C,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC;IAE7C,OAAO,oBAAoB,CACvB,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,UAAU,EACV,GAAG,EACH,KAAK,EACL,KAAK,EACL,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EACxB,eAAe,EACf,QAAQ,EACR,QAAQ,EACR,OAAO,CAAC,QAAQ,IAAI,IAAI,EACxB,OAAO,CAAC,OAAO,IAAI,IAAI,EACvB,OAAO,CAAC,WAAW,IAAI,IAAI,EAC3B,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CACrC,CAAC;AACN,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,MAAM,UAAU,kBAAkB,CAC9B,IAAY,EACZ,OAkBC,EACD,QAAyB,IAAI;IAE7B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAC1B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC5B,MAAM,aAAa,GACf,OAAO,CAAC,aAAa;QACrB,CAAC,GAAG,EAAE;YACF,OAAO,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACP,MAAM,gBAAgB,GAClB,OAAO,CAAC,gBAAgB;QACxB,CAAC,GAAG,EAAE;YACF,OAAO,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACP,MAAM,gBAAgB,GAAG,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,gBAAgB,IAAI,KAAK,CAAC;IAChF,MAAM,eAAe,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,eAAe,IAAI,KAAK,CAAC;IAC/E,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC;IAC/D,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;IACpC,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC;IAChD,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,KAAK,CAAC;IACjD,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACjF,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IAClC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC;IAC3C,OAAO,oBAAoB,CACvB,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,aAAa,EACb,gBAAgB,EAChB,gBAAgB,EAChB,eAAe,EACf,GAAG,EACH,IAAI,EACJ,KAAK,EACL,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EACxB,eAAe,EACf,QAAQ,IAAI,IAAI,EAChB,QAAQ,EACR,OAAO,CAAC,QAAQ,IAAI,IAAI,EACxB,OAAO,CAAC,OAAO,IAAI,IAAI,EACvB,WAAW,EACX,WAAW,CACd,CAAC;AACN,CAAC;AAED,SAAS,oBAAoB,CACzB,IAAY,EACZ,KAAgB,EAChB,KAAgB,EAChB,KAAuB,EACvB,QAA0B,EAC1B,aAAkE,EAClE,cAAmE,EACnE,IAAa,EACb,IAAa,EACb,GAAW,EACX,MAAe,EACf,KAAsB,EACtB,MAAe,EACf,IAAY,EACZ,QAAwB,EACxB,QAAiB,EACjB,QAA2B,EAC3B,OAA0B,EAC1B,WAA8B,EAC9B,WAAoB;IAEpB,qBAAqB;IACrB,MAAM,kBAAkB,GAAG,CACvB,KAAgB,EAChB,KAAgB,EAChB,MAAc,EACd,UAAuB,EACvB,KAAuB,EACvB,QAA0B,EAC1B,aAAkE,EAClE,cAAmE,EACnE,GAAW,EACX,MAAe,EACf,WAAoB,EACtB,EAAE;QACA,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACtC,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QACpC,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QACxC,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QACxC,IAAI,WAAW,EAAE;YACb,qCAAqC;YACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;oBAChE,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBACzC;gBACD,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;oBAC7D,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBACvC;gBACD,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;oBACnE,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBAC3C;gBACD,IAAI,CAAC,GAAG,CAAC,EAAE;oBACP,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACxB,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;wBACjC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;qBAChC;oBACD,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACnB,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;wBAChC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC/B;oBACD,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACrB,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;wBAClC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;qBACjC;iBACJ;aACJ;SACJ;QACD,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,WAAW,GAAG,GAAG,EAAE;YACrB,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC;QACF,MAAM,cAAc,GAAG,GAAG,EAAE;YACxB,OAAO,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC;QACF,MAAM,MAAM,GAA8C,MAAM,IAAI,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC;QACrH,MAAM,GAAG,GAA8C,MAAM,IAAI,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC;QAC7G,IAAI,KAAK,GAAG,GAAG,KAAK,IAAI,CAAC,MAAM,IAAI,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,MAAM,cAAc,GAAW,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,MAAM,SAAS,GAAc,EAAE,CAAC;YAChC,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,UAAU,GAAG,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;YAC7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnH,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;gBAC/B,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;gBACnE,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtD,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;aAC1B;YACD,UAAU,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;YAC9B,KAAK,IAAI,SAAS,CAAC;YACnB,KAAK,EAAE,CAAC;SACX;QACD,MAAM;QACN,MAAM,OAAO,GAAG,CAAC,SAAoB,EAAE,EAAE;YACrC,MAAM,QAAQ,GAAG,KAAK,EAAW,CAAC;YAClC,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YAClC,IAAI,CAAS,CAAC;YACd,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;aACvC;YACD,UAAU,CAAC,YAAY,CAAC,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;YAChD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAC7B;YACD,OAAO,QAAQ,CAAC;QACpB,CAAC,CAAC;QACF,QAAQ,GAAG,EAAE;YACT,KAAK,IAAI,CAAC,MAAM;gBACZ,MAAM;YACV,KAAK,IAAI,CAAC,SAAS;gBACf,UAAU,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvC,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC9B,MAAM;YACV,KAAK,IAAI,CAAC,OAAO;gBACb,UAAU,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC1C,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBACvD,MAAM;YACV,KAAK,IAAI,CAAC,OAAO;gBACb,UAAU,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvC,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC9B,UAAU,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC1C,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBACvD,MAAM;YACV;gBACI,MAAM;SACb;QACD,OAAO,UAAU,CAAC;IACtB,CAAC,CAAC;IACF,IAAI,MAAM,CAAC;IACX,IAAI,SAAS,CAAC;IACd,IAAI,QAAQ,EAAE;QACV,kBAAkB;QAClB,MAAM,OAAO,GAAG,QAAQ,CAAC,oBAAqB,CAAC;QAC/C,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAChG,SAAS,GAAG,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,cAAc,EAAE,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;QAClK,QAAQ,GAAG,YAAY,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,KAAK,IAAI,SAAS,CAAC,CAAC;QAE/J,OAAO,QAAQ,CAAC;KACnB;IACD,0BAA0B;IAC1B,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;IAC1E,MAAM,aAAa,GAAG,IAAI,KAAK,EAAkB,CAAC;IAClD,GAAG,GAAG,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IACnC,SAAS,GAAG,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;IAC9I,MAAM,eAAe,GAAG,YAAY,CAChC,IAAI,EACJ;QACI,SAAS,EAAE,SAAS;QACpB,UAAU,EAAE,IAAI;QAChB,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,MAAM;QACjB,eAAe,EAAE,IAAI;QACrB,QAAQ,EAAE,QAAQ;QAClB,QAAQ,EAAE,QAAQ,IAAI,SAAS;QAC/B,OAAO,EAAE,OAAO,IAAI,SAAS;KAChC,EACD,KAAK,CACR,CAAC;IACF,eAAe,CAAC,oBAAqB,CAAC,SAAS,GAAG,SAAS,CAAC;IAC5D,eAAe,CAAC,oBAAqB,CAAC,MAAM,GAAG,MAAM,CAAC;IACtD,eAAe,CAAC,oBAAqB,CAAC,GAAG,GAAG,GAAG,CAAC;IAEhD,OAAO,eAAe,CAAC;AAC3B,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG;IACxB,gEAAgE;IAChE,YAAY;IACZ,gEAAgE;IAChE,kBAAkB;CACrB,CAAC;AAEF,IAAI,CAAC,YAAY,GAAG,CAChB,IAAY,EACZ,KAAgB,EAChB,IAAe,EACf,KAAa,EACb,QAAgB,EAChB,GAAW,EACX,QAAyB,IAAI,EAC7B,SAAmB,EACnB,eAAwB,EACxB,QAAe,EACX,EAAE;IACN,MAAM,OAAO,GAAG;QACZ,KAAK,EAAE,KAAK;QACZ,IAAI,EAAE,IAAI;QACV,KAAK,EAAE,KAAK;QACZ,QAAQ,EAAE,QAAQ;QAClB,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM;QACvC,eAAe,EAAE,eAAe;QAChC,QAAQ,EAAE,QAAQ;QAClB,SAAS,EAAE,SAAS;KACvB,CAAC;IAEF,OAAO,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC9C,CAAC,CAAC;AAEF,IAAI,CAAC,kBAAkB,GAAG,CACtB,IAAY,EACZ,KAAgB,EAChB,IAAe,EACf,aAAkE,EAClE,gBAAqE,EACrE,gBAAyB,EACzB,eAAwB,EACxB,GAAW,EACX,KAAY,EACZ,SAAmB,EACnB,eAAwB,EACxB,QAAe,EACX,EAAE;IACN,MAAM,OAAO,GAAG;QACZ,KAAK,EAAE,KAAK;QACZ,IAAI,EAAE,IAAI;QACV,aAAa,EAAE,aAAa;QAC5B,gBAAgB,EAAE,gBAAgB;QAClC,gBAAgB,EAAE,gBAAgB;QAClC,eAAe,EAAE,eAAe;QAChC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM;QACvC,eAAe,EAAE,eAAe;QAChC,QAAQ,EAAE,QAAQ;QAClB,SAAS,EAAE,SAAS;KACvB,CAAC;IAEF,OAAO,kBAAkB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AACpD,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Vector4 } from \"../../Maths/math.vector\";\r\nimport { Vector3, TmpVectors, Matrix } from \"../../Maths/math.vector\";\r\nimport { Mesh } from \"../mesh\";\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON> } from \"./ribbonBuilder\";\r\nimport { Path3D } from \"../../Maths/math.path\";\r\n\r\n/**\r\n * Creates an extruded shape mesh. The extrusion is a parametric shape. It has no predefined shape. Its final shape will depend on the input parameters.\r\n * * The parameter `shape` is a required array of successive Vector3. This array depicts the shape to be extruded in its local space : the shape must be designed in the xOy plane and will be extruded along the Z axis.\r\n * * The parameter `path` is a required array of successive Vector3. This is the axis curve the shape is extruded along.\r\n * * The parameter `rotation` (float, default 0 radians) is the angle value to rotate the shape each step (each path point), from the former step (so rotation added each step) along the curve.\r\n * * The parameter `scale` (float, default 1) is the value to scale the shape.\r\n * * The parameter `closeShape` (boolean, default false) closes the shape when true, since v5.0.0.\r\n * * The parameter `closePath` (boolean, default false) closes the path when true and no caps, since v5.0.0.\r\n * * The parameter `cap` sets the way the extruded shape is capped. Possible values : BABYLON.Mesh.NO_CAP (default), BABYLON.Mesh.CAP_START, BABYLON.Mesh.CAP_END, BABYLON.Mesh.CAP_ALL\r\n * * The optional parameter `instance` is an instance of an existing ExtrudedShape object to be updated with the passed `shape`, `path`, `scale` or `rotation` parameters : https://doc.babylonjs.com/features/featuresDeepDive/mesh/dynamicMeshMorph#extruded-shape\r\n * * Remember you can only change the shape or path point positions, not their number when updating an extruded shape.\r\n * * You can also set the mesh side orientation with the values : BABYLON.Mesh.FRONTSIDE (default), BABYLON.Mesh.BACKSIDE or BABYLON.Mesh.DOUBLESIDE\r\n * * If you create a double-sided mesh, you can choose what parts of the texture image to crop and stick respectively on the front and the back sides with the parameters `frontUVs` and `backUVs` (Vector4). Detail here : https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation\r\n * * The optional parameter `invertUV` (boolean, default false) swaps in the geometry the U and V coordinates to apply a texture.\r\n * * The mesh can be set to updatable with the boolean parameter `updatable` (default false) if its internal geometry is supposed to change once created.\r\n * * The optional parameter `firstNormal` (Vector3) defines the direction of the first normal of the supplied path. Consider using this for any path that is straight, and particular for paths in the xy plane.\r\n * * The optional `adjustFrame` (boolean, default false) will cause the internally generated Path3D tangents, normals, and binormals to be adjusted so that a) they are always well-defined, and b) they do not reverse from one path point to the next. This prevents the extruded shape from being flipped and/or rotated with resulting mesh self-intersections. This is primarily useful for straight paths that can reverse direction.\r\n * @param name defines the name of the mesh\r\n * @param options defines the options used to create the mesh\r\n * @param scene defines the hosting scene\r\n * @returns the extruded shape mesh\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param#extruded-shapes\r\n */\r\nexport function ExtrudeShape(\r\n    name: string,\r\n    options: {\r\n        shape: Vector3[];\r\n        path: Vector3[];\r\n        scale?: number;\r\n        rotation?: number;\r\n        closeShape?: boolean;\r\n        closePath?: boolean;\r\n        cap?: number;\r\n        updatable?: boolean;\r\n        sideOrientation?: number;\r\n        frontUVs?: Vector4;\r\n        backUVs?: Vector4;\r\n        instance?: Mesh;\r\n        invertUV?: boolean;\r\n        firstNormal?: Vector3;\r\n        adjustFrame?: boolean;\r\n    },\r\n    scene: Nullable<Scene> = null\r\n): Mesh {\r\n    const path = options.path;\r\n    const shape = options.shape;\r\n    const scale = options.scale || 1;\r\n    const rotation = options.rotation || 0;\r\n    const cap = options.cap === 0 ? 0 : options.cap || Mesh.NO_CAP;\r\n    const updatable = options.updatable;\r\n    const sideOrientation = Mesh._GetDefaultSideOrientation(options.sideOrientation);\r\n    const instance = options.instance || null;\r\n    const invertUV = options.invertUV || false;\r\n    const closeShape = options.closeShape || false;\r\n    const closePath = options.closePath || false;\r\n\r\n    return _ExtrudeShapeGeneric(\r\n        name,\r\n        shape,\r\n        path,\r\n        scale,\r\n        rotation,\r\n        null,\r\n        null,\r\n        closePath,\r\n        closeShape,\r\n        cap,\r\n        false,\r\n        scene,\r\n        updatable ? true : false,\r\n        sideOrientation,\r\n        instance,\r\n        invertUV,\r\n        options.frontUVs || null,\r\n        options.backUVs || null,\r\n        options.firstNormal || null,\r\n        options.adjustFrame ? true : false\r\n    );\r\n}\r\n\r\n/**\r\n * Creates an custom extruded shape mesh.\r\n * The custom extrusion is a parametric shape. It has no predefined shape. Its final shape will depend on the input parameters.\r\n * * The parameter `shape` is a required array of successive Vector3. This array depicts the shape to be extruded in its local space : the shape must be designed in the xOy plane and will be extruded along the Z axis.\r\n * * The parameter `path` is a required array of successive Vector3. This is the axis curve the shape is extruded along.\r\n * * The parameter `rotationFunction` (JS function) is a custom Javascript function called on each path point. This function is passed the position i of the point in the path and the distance of this point from the beginning of the path\r\n * * It must returns a float value that will be the rotation in radians applied to the shape on each path point.\r\n * * The parameter `scaleFunction` (JS function) is a custom Javascript function called on each path point. This function is passed the position i of the point in the path and the distance of this point from the beginning of the path\r\n * * It must returns a float value that will be the scale value applied to the shape on each path point\r\n * * The parameter `closeShape` (boolean, default false) closes the shape when true, since v5.0.0.\r\n * * The parameter `closePath` (boolean, default false) closes the path when true and no caps, since v5.0.0.\r\n * * The parameter `ribbonClosePath` (boolean, default false) forces the extrusion underlying ribbon to close all the paths in its `pathArray` - depreciated in favor of closeShape\r\n * * The parameter `ribbonCloseArray` (boolean, default false) forces the extrusion underlying ribbon to close its `pathArray` - depreciated in favor of closePath\r\n * * The parameter `cap` sets the way the extruded shape is capped. Possible values : BABYLON.Mesh.NO_CAP (default), BABYLON.Mesh.CAP_START, BABYLON.Mesh.CAP_END, BABYLON.Mesh.CAP_ALL\r\n * * The optional parameter `instance` is an instance of an existing ExtrudedShape object to be updated with the passed `shape`, `path`, `scale` or `rotation` parameters : https://doc.babylonjs.com/features/featuresDeepDive/mesh/dynamicMeshMorph#extruded-shape\r\n * * Remember you can only change the shape or path point positions, not their number when updating an extruded shape\r\n * * You can also set the mesh side orientation with the values : BABYLON.Mesh.FRONTSIDE (default), BABYLON.Mesh.BACKSIDE or BABYLON.Mesh.DOUBLESIDE\r\n * * If you create a double-sided mesh, you can choose what parts of the texture image to crop and stick respectively on the front and the back sides with the parameters `frontUVs` and `backUVs` (Vector4). Detail here : https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation\r\n * * The optional parameter `invertUV` (boolean, default false) swaps in the geometry the U and V coordinates to apply a texture\r\n * * The mesh can be set to updatable with the boolean parameter `updatable` (default false) if its internal geometry is supposed to change once created\r\n * * The optional parameter `firstNormal` (Vector3) defines the direction of the first normal of the supplied path. It should be supplied when the path is in the xy plane, and particularly if these sections are straight, because the underlying Path3D object will pick a normal in the xy plane that causes the extrusion to be collapsed into the plane. This should be used for any path that is straight.\r\n * * The optional `adjustFrame` (boolean, default false) will cause the internally generated Path3D tangents, normals, and binormals to be adjusted so that a) they are always well-defined, and b) they do not reverse from one path point to the next. This prevents the extruded shape from being flipped and/or rotated with resulting mesh self-intersections. This is primarily useful for straight paths that can reverse direction.\r\n * @param name defines the name of the mesh\r\n * @param options defines the options used to create the mesh\r\n * @param scene defines the hosting scene\r\n * @returns the custom extruded shape mesh\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param#custom-extruded-shapes\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/param#extruded-shapes\r\n */\r\nexport function ExtrudeShapeCustom(\r\n    name: string,\r\n    options: {\r\n        shape: Vector3[];\r\n        path: Vector3[];\r\n        scaleFunction?: Nullable<{ (i: number, distance: number): number }>;\r\n        rotationFunction?: Nullable<{ (i: number, distance: number): number }>;\r\n        ribbonCloseArray?: boolean;\r\n        ribbonClosePath?: boolean;\r\n        closeShape?: boolean;\r\n        closePath?: boolean;\r\n        cap?: number;\r\n        updatable?: boolean;\r\n        sideOrientation?: number;\r\n        frontUVs?: Vector4;\r\n        backUVs?: Vector4;\r\n        instance?: Mesh;\r\n        invertUV?: boolean;\r\n        firstNormal?: Vector3;\r\n        adjustFrame?: boolean;\r\n    },\r\n    scene: Nullable<Scene> = null\r\n): Mesh {\r\n    const path = options.path;\r\n    const shape = options.shape;\r\n    const scaleFunction =\r\n        options.scaleFunction ||\r\n        (() => {\r\n            return 1;\r\n        });\r\n    const rotationFunction =\r\n        options.rotationFunction ||\r\n        (() => {\r\n            return 0;\r\n        });\r\n    const ribbonCloseArray = options.closePath || options.ribbonCloseArray || false;\r\n    const ribbonClosePath = options.closeShape || options.ribbonClosePath || false;\r\n    const cap = options.cap === 0 ? 0 : options.cap || Mesh.NO_CAP;\r\n    const updatable = options.updatable;\r\n    const firstNormal = options.firstNormal || null;\r\n    const adjustFrame = options.adjustFrame || false;\r\n    const sideOrientation = Mesh._GetDefaultSideOrientation(options.sideOrientation);\r\n    const instance = options.instance;\r\n    const invertUV = options.invertUV || false;\r\n    return _ExtrudeShapeGeneric(\r\n        name,\r\n        shape,\r\n        path,\r\n        null,\r\n        null,\r\n        scaleFunction,\r\n        rotationFunction,\r\n        ribbonCloseArray,\r\n        ribbonClosePath,\r\n        cap,\r\n        true,\r\n        scene,\r\n        updatable ? true : false,\r\n        sideOrientation,\r\n        instance || null,\r\n        invertUV,\r\n        options.frontUVs || null,\r\n        options.backUVs || null,\r\n        firstNormal,\r\n        adjustFrame\r\n    );\r\n}\r\n\r\nfunction _ExtrudeShapeGeneric(\r\n    name: string,\r\n    shape: Vector3[],\r\n    curve: Vector3[],\r\n    scale: Nullable<number>,\r\n    rotation: Nullable<number>,\r\n    scaleFunction: Nullable<{ (i: number, distance: number): number }>,\r\n    rotateFunction: Nullable<{ (i: number, distance: number): number }>,\r\n    rbCA: boolean,\r\n    rbCP: boolean,\r\n    cap: number,\r\n    custom: boolean,\r\n    scene: Nullable<Scene>,\r\n    updtbl: boolean,\r\n    side: number,\r\n    instance: Nullable<Mesh>,\r\n    invertUV: boolean,\r\n    frontUVs: Nullable<Vector4>,\r\n    backUVs: Nullable<Vector4>,\r\n    firstNormal: Nullable<Vector3>,\r\n    adjustFrame: boolean\r\n): Mesh {\r\n    // extrusion geometry\r\n    const extrusionPathArray = (\r\n        shape: Vector3[],\r\n        curve: Vector3[],\r\n        path3D: Path3D,\r\n        shapePaths: Vector3[][],\r\n        scale: Nullable<number>,\r\n        rotation: Nullable<number>,\r\n        scaleFunction: Nullable<{ (i: number, distance: number): number }>,\r\n        rotateFunction: Nullable<{ (i: number, distance: number): number }>,\r\n        cap: number,\r\n        custom: boolean,\r\n        adjustFrame: boolean\r\n    ) => {\r\n        const tangents = path3D.getTangents();\r\n        const normals = path3D.getNormals();\r\n        const binormals = path3D.getBinormals();\r\n        const distances = path3D.getDistances();\r\n        if (adjustFrame) {\r\n            /* fix tangents,normals, binormals */\r\n            for (let i = 0; i < tangents.length; i++) {\r\n                if (tangents[i].x == 0 && tangents[i].y == 0 && tangents[i].z == 0) {\r\n                    tangents[i].copyFrom(tangents[i - 1]);\r\n                }\r\n                if (normals[i].x == 0 && normals[i].y == 0 && normals[i].z == 0) {\r\n                    normals[i].copyFrom(normals[i - 1]);\r\n                }\r\n                if (binormals[i].x == 0 && binormals[i].y == 0 && binormals[i].z == 0) {\r\n                    binormals[i].copyFrom(binormals[i - 1]);\r\n                }\r\n                if (i > 0) {\r\n                    let v = tangents[i - 1];\r\n                    if (Vector3.Dot(v, tangents[i]) < 0) {\r\n                        tangents[i].scaleInPlace(-1);\r\n                    }\r\n                    v = normals[i - 1];\r\n                    if (Vector3.Dot(v, normals[i]) < 0) {\r\n                        normals[i].scaleInPlace(-1);\r\n                    }\r\n                    v = binormals[i - 1];\r\n                    if (Vector3.Dot(v, binormals[i]) < 0) {\r\n                        binormals[i].scaleInPlace(-1);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        let angle = 0;\r\n        const returnScale = () => {\r\n            return scale !== null ? scale : 1;\r\n        };\r\n        const returnRotation = () => {\r\n            return rotation !== null ? rotation : 0;\r\n        };\r\n        const rotate: { (i: number, distance: number): number } = custom && rotateFunction ? rotateFunction : returnRotation;\r\n        const scl: { (i: number, distance: number): number } = custom && scaleFunction ? scaleFunction : returnScale;\r\n        let index = cap === Mesh.NO_CAP || cap === Mesh.CAP_END ? 0 : 2;\r\n        const rotationMatrix: Matrix = TmpVectors.Matrix[0];\r\n\r\n        for (let i = 0; i < curve.length; i++) {\r\n            const shapePath: Vector3[] = [];\r\n            const angleStep = rotate(i, distances[i]);\r\n            const scaleRatio = scl(i, distances[i]);\r\n            Matrix.RotationAxisToRef(tangents[i], angle, rotationMatrix);\r\n            for (let p = 0; p < shape.length; p++) {\r\n                const planed = tangents[i].scale(shape[p].z).add(normals[i].scale(shape[p].x)).add(binormals[i].scale(shape[p].y));\r\n                const rotated = Vector3.Zero();\r\n                Vector3.TransformCoordinatesToRef(planed, rotationMatrix, rotated);\r\n                rotated.scaleInPlace(scaleRatio).addInPlace(curve[i]);\r\n                shapePath[p] = rotated;\r\n            }\r\n            shapePaths[index] = shapePath;\r\n            angle += angleStep;\r\n            index++;\r\n        }\r\n        // cap\r\n        const capPath = (shapePath: Vector3[]) => {\r\n            const pointCap = Array<Vector3>();\r\n            const barycenter = Vector3.Zero();\r\n            let i: number;\r\n            for (i = 0; i < shapePath.length; i++) {\r\n                barycenter.addInPlace(shapePath[i]);\r\n            }\r\n            barycenter.scaleInPlace(1.0 / shapePath.length);\r\n            for (i = 0; i < shapePath.length; i++) {\r\n                pointCap.push(barycenter);\r\n            }\r\n            return pointCap;\r\n        };\r\n        switch (cap) {\r\n            case Mesh.NO_CAP:\r\n                break;\r\n            case Mesh.CAP_START:\r\n                shapePaths[0] = capPath(shapePaths[2]);\r\n                shapePaths[1] = shapePaths[2];\r\n                break;\r\n            case Mesh.CAP_END:\r\n                shapePaths[index] = shapePaths[index - 1];\r\n                shapePaths[index + 1] = capPath(shapePaths[index - 1]);\r\n                break;\r\n            case Mesh.CAP_ALL:\r\n                shapePaths[0] = capPath(shapePaths[2]);\r\n                shapePaths[1] = shapePaths[2];\r\n                shapePaths[index] = shapePaths[index - 1];\r\n                shapePaths[index + 1] = capPath(shapePaths[index - 1]);\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n        return shapePaths;\r\n    };\r\n    let path3D;\r\n    let pathArray;\r\n    if (instance) {\r\n        // instance update\r\n        const storage = instance._creationDataStorage!;\r\n        path3D = firstNormal ? storage.path3D.update(curve, firstNormal) : storage.path3D.update(curve);\r\n        pathArray = extrusionPathArray(shape, curve, storage.path3D, storage.pathArray, scale, rotation, scaleFunction, rotateFunction, storage.cap, custom, adjustFrame);\r\n        instance = CreateRibbon(\"\", { pathArray, closeArray: false, closePath: false, offset: 0, updatable: false, sideOrientation: 0, instance }, scene || undefined);\r\n\r\n        return instance;\r\n    }\r\n    // extruded shape creation\r\n    path3D = firstNormal ? new Path3D(curve, firstNormal) : new Path3D(curve);\r\n    const newShapePaths = new Array<Array<Vector3>>();\r\n    cap = cap < 0 || cap > 3 ? 0 : cap;\r\n    pathArray = extrusionPathArray(shape, curve, path3D, newShapePaths, scale, rotation, scaleFunction, rotateFunction, cap, custom, adjustFrame);\r\n    const extrudedGeneric = CreateRibbon(\r\n        name,\r\n        {\r\n            pathArray: pathArray,\r\n            closeArray: rbCA,\r\n            closePath: rbCP,\r\n            updatable: updtbl,\r\n            sideOrientation: side,\r\n            invertUV: invertUV,\r\n            frontUVs: frontUVs || undefined,\r\n            backUVs: backUVs || undefined,\r\n        },\r\n        scene\r\n    );\r\n    extrudedGeneric._creationDataStorage!.pathArray = pathArray;\r\n    extrudedGeneric._creationDataStorage!.path3D = path3D;\r\n    extrudedGeneric._creationDataStorage!.cap = cap;\r\n\r\n    return extrudedGeneric;\r\n}\r\n\r\n/**\r\n * Class containing static functions to help procedurally build meshes\r\n * @deprecated please use the functions directly from the module\r\n */\r\nexport const ShapeBuilder = {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    ExtrudeShape,\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    ExtrudeShapeCustom,\r\n};\r\n\r\nMesh.ExtrudeShape = (\r\n    name: string,\r\n    shape: Vector3[],\r\n    path: Vector3[],\r\n    scale: number,\r\n    rotation: number,\r\n    cap: number,\r\n    scene: Nullable<Scene> = null,\r\n    updatable?: boolean,\r\n    sideOrientation?: number,\r\n    instance?: Mesh\r\n): Mesh => {\r\n    const options = {\r\n        shape: shape,\r\n        path: path,\r\n        scale: scale,\r\n        rotation: rotation,\r\n        cap: cap === 0 ? 0 : cap || Mesh.NO_CAP,\r\n        sideOrientation: sideOrientation,\r\n        instance: instance,\r\n        updatable: updatable,\r\n    };\r\n\r\n    return ExtrudeShape(name, options, scene);\r\n};\r\n\r\nMesh.ExtrudeShapeCustom = (\r\n    name: string,\r\n    shape: Vector3[],\r\n    path: Vector3[],\r\n    scaleFunction: Nullable<{ (i: number, distance: number): number }>,\r\n    rotationFunction: Nullable<{ (i: number, distance: number): number }>,\r\n    ribbonCloseArray: boolean,\r\n    ribbonClosePath: boolean,\r\n    cap: number,\r\n    scene: Scene,\r\n    updatable?: boolean,\r\n    sideOrientation?: number,\r\n    instance?: Mesh\r\n): Mesh => {\r\n    const options = {\r\n        shape: shape,\r\n        path: path,\r\n        scaleFunction: scaleFunction,\r\n        rotationFunction: rotationFunction,\r\n        ribbonCloseArray: ribbonCloseArray,\r\n        ribbonClosePath: ribbonClosePath,\r\n        cap: cap === 0 ? 0 : cap || Mesh.NO_CAP,\r\n        sideOrientation: sideOrientation,\r\n        instance: instance,\r\n        updatable: updatable,\r\n    };\r\n\r\n    return ExtrudeShapeCustom(name, options, scene);\r\n};\r\n"]}