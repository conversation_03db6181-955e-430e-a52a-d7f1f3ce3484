{"version": 3, "file": "physicsEngine.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Physics/v1/physicsEngine.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAMlD,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAElD;;;GAGG;AACH,MAAM,OAAO,aAAa;IActB;;;OAGG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;IAClD,CAAC;IACD;;;;OAIG;IACI,MAAM,CAAC,oBAAoB;QAC9B,MAAM,WAAW,CAAC,gBAAgB,CAAC,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,YACI,OAA0B,EAClB,iBAAuC,aAAa,CAAC,oBAAoB,EAAE;QAA3E,mBAAc,GAAd,cAAc,CAA6D;QApCvF;;WAEG;QACK,eAAU,GAA2B,EAAE,CAAC;QACxC,YAAO,GAAgC,EAAE,CAAC;QAC1C,iBAAY,GAAW,CAAC,CAAC;QACzB,qBAAgB,GAAG,CAAC,CAAC;QAgCzB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,oBAAoB,GAAG,kCAAkC,CAAC,CAAC;SAC7H;QACD,OAAO,GAAG,OAAO,IAAI,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,OAAgB;QAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;OAMG;IACI,WAAW,CAAC,cAAsB,CAAC,GAAG,EAAE;QAC3C,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;IAC7C,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,cAAsB,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,QAAQ;YACtC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,oBAAoB;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,QAAyB;QACxC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/B,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5C,iCAAiC;QACjC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YAClB,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;SACrD;IACL,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,QAAyB;QAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACjD,eAAe;YACf,IAAI,OAAO,CAAC,MAAM,EAAE;gBAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;aACvD;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,YAA6B,EAAE,iBAAkC,EAAE,KAAmB;QAClG,MAAM,aAAa,GAAG;YAClB,YAAY,EAAE,YAAY;YAC1B,iBAAiB,EAAE,iBAAiB;YACpC,KAAK,EAAE,KAAK;SACf,CAAC;QACF,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAC1C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACjC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;IACrD,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,YAA6B,EAAE,iBAAkC,EAAE,KAAmB;QACrG,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,aAAa;YAC9D,OAAO,aAAa,CAAC,iBAAiB,KAAK,iBAAiB,IAAI,aAAa,CAAC,KAAK,KAAK,KAAK,IAAI,aAAa,CAAC,YAAY,KAAK,YAAY,CAAC;QACjJ,CAAC,CAAC,CAAC;QACH,IAAI,cAAc,CAAC,MAAM,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,sCAAsC;SACzC;IACL,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,KAAa;QACtB,oDAAoD;QACpD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACjC,IAAI,QAAQ,CAAC,kBAAkB,EAAE,EAAE;gBAC/B,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;aACrD;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,KAAK,GAAG,GAAG,EAAE;YACb,KAAK,GAAG,GAAG,CAAC;SACf;aAAM,IAAI,KAAK,IAAI,CAAC,EAAE;YACnB,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC;SACtB;QAED,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAC5D,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACI,2BAA2B,CAAC,MAA6B;QAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAC7C,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,EAAE;gBACtC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;aAC7B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,0BAA0B,CAAC,IAAS;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAC7C,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,KAAK,IAAI,EAAE;gBACzC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;aAC7B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,IAAa,EAAE,EAAW;QACrC,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;OAMG;IACI,YAAY,CAAC,IAAa,EAAE,EAAW,EAAE,MAA4B;QACxE,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { PhysicsImpostorJoint, IPhysicsEnginePlugin } from \"./IPhysicsEnginePlugin\";\r\nimport type { IPhysicsEngine } from \"../IPhysicsEngine\";\r\nimport type { PhysicsImpostor, IPhysicsEnabledObject } from \"./physicsImpostor\";\r\nimport type { PhysicsJoint } from \"./physicsJoint\";\r\nimport type { PhysicsRaycastResult } from \"../physicsRaycastResult\";\r\nimport { _WarnImport } from \"../../Misc/devTools\";\r\n\r\n/**\r\n * Class used to control physics engine\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport class PhysicsEngine implements IPhysicsEngine {\r\n    /**\r\n     * Global value used to control the smallest number supported by the simulation\r\n     */\r\n    private _impostors: Array<PhysicsImpostor> = [];\r\n    private _joints: Array<PhysicsImpostorJoint> = [];\r\n    private _subTimeStep: number = 0;\r\n    private _uniqueIdCounter = 0;\r\n\r\n    /**\r\n     * Gets the gravity vector used by the simulation\r\n     */\r\n    public gravity: Vector3;\r\n\r\n    /**\r\n     *\r\n     * @returns version\r\n     */\r\n    public getPluginVersion(): number {\r\n        return this._physicsPlugin.getPluginVersion();\r\n    }\r\n    /**\r\n     * @virtual\r\n     * Factory used to create the default physics plugin.\r\n     * @returns The default physics plugin\r\n     */\r\n    public static DefaultPluginFactory(): IPhysicsEnginePlugin {\r\n        throw _WarnImport(\"CannonJSPlugin\");\r\n    }\r\n\r\n    /**\r\n     * Creates a new Physics Engine\r\n     * @param gravity defines the gravity vector used by the simulation\r\n     * @param _physicsPlugin defines the plugin to use (CannonJS by default)\r\n     */\r\n    constructor(\r\n        gravity: Nullable<Vector3>,\r\n        private _physicsPlugin: IPhysicsEnginePlugin = PhysicsEngine.DefaultPluginFactory()\r\n    ) {\r\n        if (!this._physicsPlugin.isSupported()) {\r\n            throw new Error(\"Physics Engine \" + this._physicsPlugin.name + \" cannot be found. \" + \"Please make sure it is included.\");\r\n        }\r\n        gravity = gravity || new Vector3(0, -9.807, 0);\r\n        this.setGravity(gravity);\r\n        this.setTimeStep();\r\n    }\r\n\r\n    /**\r\n     * Sets the gravity vector used by the simulation\r\n     * @param gravity defines the gravity vector to use\r\n     */\r\n    public setGravity(gravity: Vector3): void {\r\n        this.gravity = gravity;\r\n        this._physicsPlugin.setGravity(this.gravity);\r\n    }\r\n\r\n    /**\r\n     * Set the time step of the physics engine.\r\n     * Default is 1/60.\r\n     * To slow it down, enter 1/600 for example.\r\n     * To speed it up, 1/30\r\n     * @param newTimeStep defines the new timestep to apply to this world.\r\n     */\r\n    public setTimeStep(newTimeStep: number = 1 / 60) {\r\n        this._physicsPlugin.setTimeStep(newTimeStep);\r\n    }\r\n\r\n    /**\r\n     * Get the time step of the physics engine.\r\n     * @returns the current time step\r\n     */\r\n    public getTimeStep(): number {\r\n        return this._physicsPlugin.getTimeStep();\r\n    }\r\n\r\n    /**\r\n     * Set the sub time step of the physics engine.\r\n     * Default is 0 meaning there is no sub steps\r\n     * To increase physics resolution precision, set a small value (like 1 ms)\r\n     * @param subTimeStep defines the new sub timestep used for physics resolution.\r\n     */\r\n    public setSubTimeStep(subTimeStep: number = 0) {\r\n        this._subTimeStep = subTimeStep;\r\n    }\r\n\r\n    /**\r\n     * Get the sub time step of the physics engine.\r\n     * @returns the current sub time step\r\n     */\r\n    public getSubTimeStep() {\r\n        return this._subTimeStep;\r\n    }\r\n\r\n    /**\r\n     * Release all resources\r\n     */\r\n    public dispose(): void {\r\n        this._impostors.forEach(function (impostor) {\r\n            impostor.dispose();\r\n        });\r\n        this._physicsPlugin.dispose();\r\n    }\r\n\r\n    /**\r\n     * Gets the name of the current physics plugin\r\n     * @returns the name of the plugin\r\n     */\r\n    public getPhysicsPluginName(): string {\r\n        return this._physicsPlugin.name;\r\n    }\r\n\r\n    /**\r\n     * Adding a new impostor for the impostor tracking.\r\n     * This will be done by the impostor itself.\r\n     * @param impostor the impostor to add\r\n     */\r\n    public addImpostor(impostor: PhysicsImpostor) {\r\n        this._impostors.push(impostor);\r\n        impostor.uniqueId = this._uniqueIdCounter++;\r\n        //if no parent, generate the body\r\n        if (!impostor.parent) {\r\n            this._physicsPlugin.generatePhysicsBody(impostor);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove an impostor from the engine.\r\n     * This impostor and its mesh will not longer be updated by the physics engine.\r\n     * @param impostor the impostor to remove\r\n     */\r\n    public removeImpostor(impostor: PhysicsImpostor) {\r\n        const index = this._impostors.indexOf(impostor);\r\n        if (index > -1) {\r\n            const removed = this._impostors.splice(index, 1);\r\n            //Is it needed?\r\n            if (removed.length) {\r\n                this.getPhysicsPlugin().removePhysicsBody(impostor);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Add a joint to the physics engine\r\n     * @param mainImpostor defines the main impostor to which the joint is added.\r\n     * @param connectedImpostor defines the impostor that is connected to the main impostor using this joint\r\n     * @param joint defines the joint that will connect both impostors.\r\n     */\r\n    public addJoint(mainImpostor: PhysicsImpostor, connectedImpostor: PhysicsImpostor, joint: PhysicsJoint) {\r\n        const impostorJoint = {\r\n            mainImpostor: mainImpostor,\r\n            connectedImpostor: connectedImpostor,\r\n            joint: joint,\r\n        };\r\n        joint.physicsPlugin = this._physicsPlugin;\r\n        this._joints.push(impostorJoint);\r\n        this._physicsPlugin.generateJoint(impostorJoint);\r\n    }\r\n\r\n    /**\r\n     * Removes a joint from the simulation\r\n     * @param mainImpostor defines the impostor used with the joint\r\n     * @param connectedImpostor defines the other impostor connected to the main one by the joint\r\n     * @param joint defines the joint to remove\r\n     */\r\n    public removeJoint(mainImpostor: PhysicsImpostor, connectedImpostor: PhysicsImpostor, joint: PhysicsJoint) {\r\n        const matchingJoints = this._joints.filter(function (impostorJoint) {\r\n            return impostorJoint.connectedImpostor === connectedImpostor && impostorJoint.joint === joint && impostorJoint.mainImpostor === mainImpostor;\r\n        });\r\n        if (matchingJoints.length) {\r\n            this._physicsPlugin.removeJoint(matchingJoints[0]);\r\n            //TODO remove it from the list as well\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Called by the scene. No need to call it.\r\n     * @param delta defines the timespan between frames\r\n     */\r\n    public _step(delta: number) {\r\n        //check if any mesh has no body / requires an update\r\n        this._impostors.forEach((impostor) => {\r\n            if (impostor.isBodyInitRequired()) {\r\n                this._physicsPlugin.generatePhysicsBody(impostor);\r\n            }\r\n        });\r\n\r\n        if (delta > 0.1) {\r\n            delta = 0.1;\r\n        } else if (delta <= 0) {\r\n            delta = 1.0 / 60.0;\r\n        }\r\n\r\n        this._physicsPlugin.executeStep(delta, this._impostors);\r\n    }\r\n\r\n    /**\r\n     * Gets the current plugin used to run the simulation\r\n     * @returns current plugin\r\n     */\r\n    public getPhysicsPlugin(): IPhysicsEnginePlugin {\r\n        return this._physicsPlugin;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of physic impostors\r\n     * @returns an array of PhysicsImpostor\r\n     */\r\n    public getImpostors(): Array<PhysicsImpostor> {\r\n        return this._impostors;\r\n    }\r\n\r\n    /**\r\n     * Gets the impostor for a physics enabled object\r\n     * @param object defines the object impersonated by the impostor\r\n     * @returns the PhysicsImpostor or null if not found\r\n     */\r\n    public getImpostorForPhysicsObject(object: IPhysicsEnabledObject): Nullable<PhysicsImpostor> {\r\n        for (let i = 0; i < this._impostors.length; ++i) {\r\n            if (this._impostors[i].object === object) {\r\n                return this._impostors[i];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets the impostor for a physics body object\r\n     * @param body defines physics body used by the impostor\r\n     * @returns the PhysicsImpostor or null if not found\r\n     */\r\n    public getImpostorWithPhysicsBody(body: any): Nullable<PhysicsImpostor> {\r\n        for (let i = 0; i < this._impostors.length; ++i) {\r\n            if (this._impostors[i].physicsBody === body) {\r\n                return this._impostors[i];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Does a raycast in the physics world\r\n     * @param from when should the ray start?\r\n     * @param to when should the ray end?\r\n     * @returns PhysicsRaycastResult\r\n     */\r\n    public raycast(from: Vector3, to: Vector3): PhysicsRaycastResult {\r\n        return this._physicsPlugin.raycast(from, to);\r\n    }\r\n\r\n    /**\r\n     * Does a raycast in the physics world\r\n     * @param from when should the ray start?\r\n     * @param to when should the ray end?\r\n     * @param result resulting PhysicsRaycastResult\r\n     * @returns true if the ray hits an impostor, else false\r\n     */\r\n    public raycastToRef(from: Vector3, to: Vector3, result: PhysicsRaycastResult) {\r\n        return this._physicsPlugin.raycastToRef(from, to, result);\r\n    }\r\n}\r\n"]}