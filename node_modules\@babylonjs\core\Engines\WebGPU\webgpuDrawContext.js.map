{"version": 3, "file": "webgpuDrawContext.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuDrawContext.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,eAAe,MAAM,mBAAmB,CAAC;AAErD,gBAAgB;AAChB,MAAM,OAAO,iBAAiB;IAmBnB,OAAO,CAAC,uBAA+B;QAC1C,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,KAAK,uBAAuB,CAAC;IACtF,CAAC;IAEM,YAAY,CAAC,uBAA+B;QAC/C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,wBAAwB,GAAG,uBAAuB,CAAC;IAC5D,CAAC;IAED,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,GAAY;QACjC,IAAI,IAAI,CAAC,cAAc,KAAK,GAAG,EAAE;YAC7B,OAAO;SACV;QAED,IAAI,CAAC,GAAG,EAAE;YACN,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC9D;YACD,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;YACpC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;SACtC;aAAM;YACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CACzD,EAAE,EACF,eAAe,CAAC,WAAW,CAAC,OAAO,GAAG,eAAe,CAAC,WAAW,CAAC,QAAQ,GAAG,eAAe,CAAC,WAAW,CAAC,OAAO,EAChH,SAAS,EACT,oBAAoB,CACvB,CAAC;YACF,IAAI,CAAC,iBAAiB,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC9B,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACjC;QAED,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;QAC1B,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IAED,YAAY,aAAkC;QAC1C,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,EAAE,CAAC;QAC7C,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAChC,CAAC;IAEM,SAAS,CAAC,IAAY,EAAE,MAAkC;QAC7D,IAAI,CAAC,QAAQ,KAAb,IAAI,CAAC,QAAQ,GAAK,MAAM,EAAE,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAC;QAEpE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;IAChC,CAAC;IAEM,eAAe,CAAC,kBAA0B,EAAE,aAAqB,EAAE,kBAA0B;QAChG,IAAI,aAAa,KAAK,IAAI,CAAC,qBAAqB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACrG,yDAAyD;YACzD,mHAAmH;YACnH,mEAAmE;YACnE,OAAO;SACV;QACD,IAAI,CAAC,qBAAqB,GAAG,aAAa,CAAC;QAE3C,IAAI,CAAC,iBAAkB,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC;QAChD,IAAI,CAAC,iBAAkB,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC;QAC3C,IAAI,CAAC,iBAAkB,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC;QAEhD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IAC9F,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC3D,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;YACpC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;SACtC;QACD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,SAAgB,CAAC;IACpC,CAAC;;AAzGc,0BAAQ,GAAG,CAAC,CAAC", "sourcesContent": ["import type { WebGPUDataBuffer } from \"../../Meshes/WebGPU/webgpuDataBuffer\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { IDrawContext } from \"../IDrawContext\";\r\nimport type { WebGPUBufferManager } from \"./webgpuBufferManager\";\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\n\r\n/** @internal */\r\nexport class WebGPUDrawContext implements IDrawContext {\r\n    private static _Counter = 0;\r\n\r\n    public fastBundle?: GPURenderBundle; // used only when compatibilityMode==false (fast mode)\r\n    public bindGroups?: GPUBindGroup[]; // cache of the bind groups. Will be reused for the next draw if isDirty==false (and materialContext.isDirty==false)\r\n\r\n    public uniqueId: number;\r\n\r\n    public buffers: { [name: string]: Nullable<WebGPUDataBuffer> };\r\n\r\n    public indirectDrawBuffer?: GPUBuffer;\r\n\r\n    private _materialContextUpdateId: number;\r\n    private _bufferManager: WebGPUBufferManager;\r\n    private _useInstancing: boolean;\r\n    private _indirectDrawData?: Uint32Array;\r\n    private _currentInstanceCount: number;\r\n    private _isDirty: boolean;\r\n\r\n    public isDirty(materialContextUpdateId: number): boolean {\r\n        return this._isDirty || this._materialContextUpdateId !== materialContextUpdateId;\r\n    }\r\n\r\n    public resetIsDirty(materialContextUpdateId: number): void {\r\n        this._isDirty = false;\r\n        this._materialContextUpdateId = materialContextUpdateId;\r\n    }\r\n\r\n    public get useInstancing() {\r\n        return this._useInstancing;\r\n    }\r\n\r\n    public set useInstancing(use: boolean) {\r\n        if (this._useInstancing === use) {\r\n            return;\r\n        }\r\n\r\n        if (!use) {\r\n            if (this.indirectDrawBuffer) {\r\n                this._bufferManager.releaseBuffer(this.indirectDrawBuffer);\r\n            }\r\n            this.indirectDrawBuffer = undefined;\r\n            this._indirectDrawData = undefined;\r\n        } else {\r\n            this.indirectDrawBuffer = this._bufferManager.createRawBuffer(\r\n                20,\r\n                WebGPUConstants.BufferUsage.CopyDst | WebGPUConstants.BufferUsage.Indirect | WebGPUConstants.BufferUsage.Storage,\r\n                undefined,\r\n                \"IndirectDrawBuffer\"\r\n            );\r\n            this._indirectDrawData = new Uint32Array(5);\r\n            this._indirectDrawData[3] = 0;\r\n            this._indirectDrawData[4] = 0;\r\n        }\r\n\r\n        this._useInstancing = use;\r\n        this._currentInstanceCount = -1;\r\n    }\r\n\r\n    constructor(bufferManager: WebGPUBufferManager) {\r\n        this._bufferManager = bufferManager;\r\n        this.uniqueId = WebGPUDrawContext._Counter++;\r\n        this._useInstancing = false;\r\n        this._currentInstanceCount = 0;\r\n        this.reset();\r\n    }\r\n\r\n    public reset(): void {\r\n        this.buffers = {};\r\n        this._isDirty = true;\r\n        this._materialContextUpdateId = 0;\r\n        this.fastBundle = undefined;\r\n        this.bindGroups = undefined;\r\n    }\r\n\r\n    public setBuffer(name: string, buffer: Nullable<WebGPUDataBuffer>): void {\r\n        this._isDirty ||= buffer?.uniqueId !== this.buffers[name]?.uniqueId;\r\n\r\n        this.buffers[name] = buffer;\r\n    }\r\n\r\n    public setIndirectData(indexOrVertexCount: number, instanceCount: number, firstIndexOrVertex: number): void {\r\n        if (instanceCount === this._currentInstanceCount || !this.indirectDrawBuffer || !this._indirectDrawData) {\r\n            // The current buffer is already up to date so do nothing\r\n            // Note that we only check for instanceCount and not indexOrVertexCount nor firstIndexOrVertex because those values\r\n            // are supposed to not change during the lifetime of a draw context\r\n            return;\r\n        }\r\n        this._currentInstanceCount = instanceCount;\r\n\r\n        this._indirectDrawData![0] = indexOrVertexCount;\r\n        this._indirectDrawData![1] = instanceCount;\r\n        this._indirectDrawData![2] = firstIndexOrVertex;\r\n\r\n        this._bufferManager.setRawData(this.indirectDrawBuffer, 0, this._indirectDrawData, 0, 20);\r\n    }\r\n\r\n    public dispose(): void {\r\n        if (this.indirectDrawBuffer) {\r\n            this._bufferManager.releaseBuffer(this.indirectDrawBuffer);\r\n            this.indirectDrawBuffer = undefined;\r\n            this._indirectDrawData = undefined;\r\n        }\r\n        this.fastBundle = undefined;\r\n        this.bindGroups = undefined;\r\n        this.buffers = undefined as any;\r\n    }\r\n}\r\n"]}