{"version": 3, "file": "nodeGeometryTextureData.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Meshes/Node/Interfaces/nodeGeometryTextureData.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\n\r\n/**\r\n * Interface used to define texture data\r\n */\r\nexport interface INodeGeometryTextureData {\r\n    data: Nullable<Float32Array>;\r\n    width: number;\r\n    height: number;\r\n}\r\n"]}