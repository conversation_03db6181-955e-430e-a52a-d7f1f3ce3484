{"version": 3, "file": "trajectoryClassifier.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/trajectoryClassifier.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAEvD,8EAA8E;AAC9E,0CAA0C;AAE1C;;GAEG;AACH,IAAU,WAAW,CA+NpB;AA/ND,WAAU,WAAW;IACjB;;;OAGG;IACH,MAAa,QAAQ;QAMjB;;;WAGG;QACI,SAAS;YACZ,MAAM,UAAU,GAAQ,EAAE,CAAC;YAE3B,MAAM,UAAU,GAAG,IAAI,KAAK,CAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC3D,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAClC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC;YACH,UAAU,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC;YAEtC,UAAU,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;YACpD,UAAU,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;YAClD,UAAU,CAAC,mBAAmB,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;YAE1D,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACtC,CAAC;QAED;;;;WAIG;QACI,MAAM,CAAC,WAAW,CAAI,IAAY;YACrC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAQ,CAAC,CAAC;YAC/D,QAAQ,CAAC,eAAe,GAAG,UAAU,CAAC,gBAAgB,CAAC,CAAC;YACxD,QAAQ,CAAC,cAAc,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC;YACtD,QAAQ,CAAC,kBAAkB,GAAG,UAAU,CAAC,mBAAmB,CAAC,CAAC;YAC9D,OAAO,QAAQ,CAAC;QACpB,CAAC;QAED;;;;;;WAMG;QACH,YACI,UAAoB,EACpB,sBAAqD,IAAI,EACzD,qBAAoD,IAAI,EACxD,0BAAuE,IAAI;YAE3E,mBAAmB,GAAG,mBAAmB,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACvD,kBAAkB,GAAG,kBAAkB,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACrD,uBAAuB,GAAG,uBAAuB,IAAI,CAAC,CAAC,CAAI,EAAE,CAAI,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAEzF,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAa,CAAC;YAC5C,IAAI,CAAC,eAAe,GAAG,IAAI,KAAK,CAAS,UAAU,CAAC,MAAM,CAAC,CAAC;YAC5D,IAAI,CAAC,cAAc,GAAG,IAAI,KAAK,CAAS,UAAU,CAAC,MAAM,CAAC,CAAC;YAC3D,IAAI,CAAC,kBAAkB,GAAG,IAAI,KAAK,CAAgB,UAAU,CAAC,MAAM,CAAC,CAAC;YAEtE,IAAI,CAAI,CAAC;YACT,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE;gBAC7D,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACzB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;gBACtC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;gBACxD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;gBAEtD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG,IAAI,KAAK,CAAS,UAAU,CAAC,MAAM,CAAC,CAAC;gBACzE,KAAK,IAAI,QAAQ,GAAG,QAAQ,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE;oBACpE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,GAAG,uBAAuB,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;iBAClG;aACJ;QACL,CAAC;QAED;;;;WAIG;QACI,eAAe,CAAC,IAAO;YAC1B,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;QAC3C,CAAC;QAED;;;;WAIG;QACI,gBAAgB,CAAC,GAAW;YAC/B,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC;QAED;;;;WAIG;QACI,eAAe,CAAC,GAAW;YAC9B,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QACpC,CAAC;QAED;;;;;;;WAOG;QACI,mBAAmB,CAAC,IAAY,EAAE,IAAY;YACjD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACjC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAEjC,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7C,CAAC;KACJ;IArHY,oBAAQ,WAqHpB,CAAA;IAED;;;OAGG;IACH,MAAa,QAAQ;QAWjB;;;;;WAKG;QACI,SAAS;YACZ,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5C,CAAC;QAED;;;;;;;WAOG;QACI,MAAM,CAAC,WAAW,CAAI,IAAY,EAAE,QAAqB;YAC5D,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;YAC5C,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACxC,OAAO,QAAQ,CAAC;QACpB,CAAC;QAED;;;;WAIG;QACH,YAAmB,UAAe,EAAE,QAAqB;YACrD,IAAI,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC,oBAAoB,EAAE;gBACnD,MAAM,IAAI,KAAK,CAAC,wBAAwB,GAAG,QAAQ,CAAC,oBAAoB,GAAG,iBAAiB,CAAC,CAAC;aACjG;YACD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC1B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;QAChF,CAAC;QAED;;;;WAIG;QACI,QAAQ,CAAC,KAAkB;YAC9B,OAAO,QAAQ,CAAC,SAAS,CAAI,IAAI,EAAE,KAAK,CAAC,CAAC;QAC9C,CAAC;QAED;;;;;WAKG;QACK,MAAM,CAAC,SAAS,CAAI,CAAc,EAAE,CAAc;YACtD,MAAM,QAAQ,GAAG,CAAC,CAAC,SAAS,CAAC;YAC7B,IAAI,QAAQ,KAAK,CAAC,CAAC,SAAS,EAAE;gBAC1B,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;aAC3F;YACD,MAAM,MAAM,GAAG,CAAC,CAAC,WAAW,CAAC;YAC7B,MAAM,MAAM,GAAG,CAAC,CAAC,WAAW,CAAC;YAC7B,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YAC9B,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YAE9B,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC;YACxC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACrB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,EAAE,EAAE,GAAG,EAAE;gBACpC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;aACxF;YACD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,EAAE,EAAE,GAAG,EAAE;gBACpC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;aACxF;YAED,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,OAAO,EAAE,EAAE,IAAI,EAAE;gBACvC,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,OAAO,EAAE,EAAE,IAAI,EAAE;oBACvC,QAAQ,CAAC,cAAc,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC/F,QAAQ,CAAC,aAAa,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;oBAC7F,QAAQ,CAAC,iBAAiB,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;oBAE/G,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAc,EAAE,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC,iBAAiB,CAAC,CAAC;iBAC1H;aACJ;YAED,OAAO,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC;;IAzFD,iBAAiB;IACO,6BAAoB,GAAG,GAAG,CAAC;IACpC,oBAAW,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,CAAS,QAAQ,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;IANlI,oBAAQ,WA8FpB,CAAA;AACL,CAAC,EA/NS,WAAW,KAAX,WAAW,QA+NpB;AAED;;;GAGG;AACH,MAAM,OAAO,UAAU;IAInB;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,WAAW,CAAC,IAAY;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAChE,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAO,EAAE,EAAE;YACvD,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QACH,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACH,YAAmB,gBAAwB,IAAI;QAC3C,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;IACrD,CAAC;IAED;;;;OAIG;IACI,GAAG,CAAC,KAA6B;QACpC,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACpC,IAAI,SAAS,KAAK,CAAC,EAAE;YACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;SACpC;aAAM;YACH,MAAM,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YAC9F,KAAK,IAAI,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE;gBACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC5D,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;gBACpC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC5B,EAAE,SAAS,CAAC;aACf;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,0BAA0B,CAAC,gBAAwB;QACtD,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,gBAAgB,CAAC,CAAC;QACtE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YACxB,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC;QACH,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;OAOG;IACI,QAAQ,CAAC,MAAgC;QAC5C,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,MAAM,UAAU,GAAG,IAAI,OAAO,EAAE,CAAC;QACjC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE;YAChD,IAAI,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,EAAE;gBACnH,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;aACtE;SACJ;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAQD;;;;;;;;;;OAUG;IACK,MAAM,CAAC,yBAAyB,CAAC,QAAgC,EAAE,OAA+B,EAAE,KAA6B,EAAE,MAAe;QACtJ,MAAM,sCAAsC,GAAG,IAAI,CAAC;QAEpD,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC;QACxD,UAAU,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC;QACnD,UAAU,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC,GAAG,sCAAsC,EAAE;YACpH,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,eAAe,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;QAC1F,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAC9B,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC;QACnF,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;QACpD,UAAU,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QAClC,OAAO,CAAC,oBAAoB,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QACpF,OAAO,IAAI,CAAC;IAChB,CAAC;IAMD;;;;;;OAMG;IACK,MAAM,CAAC,gBAAgB,CAAC,OAA+B,EAAE,MAAgC;QAC7F,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC;QAC1B,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;QAC1C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE;YAC1C,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YACtD,IAAI,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,UAAU,EAAE;gBAC3C,UAAU,CAAC,UAAU,GAAG,GAAG,CAAC;gBAC5B,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;aAC7C;SACJ;QAED,OAAO,UAAU,CAAC,UAAU,CAAC;IACjC,CAAC;;AA9Dc,sBAAW,GAAG,IAAI,OAAO,EAAE,CAAC;AAC5B,0BAAe,GAAG,IAAI,OAAO,EAAE,CAAC;AAChC,iBAAM,GAAG,IAAI,OAAO,EAAE,CAAC;AACvB,qBAAU,GAAG,IAAI,OAAO,EAAE,CAAC;AAC3B,sBAAW,GAAG,IAAI,MAAM,EAAE,CAAC;AA6D9C;;;;;GAKG;AACH,MAAM,eAAe;IASjB;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,QAAQ,CAClB,eAAuB,EAAE,EACzB,aAAqB,GAAG,EACxB,mBAA2B,GAAG,EAC9B,iBAAyB,KAAK,EAC9B,cAAwC,EAAE;QAE1C,MAAM,OAAO,GAAG,KAAK,CAAC;QACtB,MAAM,eAAe,GAAG,OAAO,GAAG,OAAO,CAAC;QAE1C,MAAM,QAAQ,GAAG,IAAI,eAAe,CAAC,YAAY,CAAC,CAAC;QACnD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,YAAY,EAAE,EAAE,GAAG,EAAE;YACzC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;YACjG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC;SACnC;QAED,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE;YAC/C,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;SAClD;QAED,IAAI,QAAgB,CAAC;QACrB,IAAI,MAAc,CAAC;QACnB,MAAM,KAAK,GAAG,IAAI,OAAO,EAAE,CAAC;QAC5B,MAAM,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;QAC9B,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACxE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,UAAU,EAAE,EAAE,SAAS,EAAE;YACzD,QAAQ,GAAG,IAAI,CAAC,gBAAgB,EAAE,cAAc,EAAE,SAAS,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;YAChF,KAAK,IAAI,GAAG,GAAG,WAAW,CAAC,MAAM,EAAE,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE;gBACnE,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC9B,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;oBAC1B,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;oBAC/C,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;oBACjC,IAAI,MAAM,GAAG,eAAe,EAAE;wBAC1B,OAAO,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;qBAC3E;gBACL,CAAC,CAAC,CAAC;gBACH,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAC7B,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACtC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC;aACnC;SACJ;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,WAAW,CAAC,IAAY;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,QAAQ,GAAG,IAAI,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACxD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE;YAC9C,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;SAC1G;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,YAAoB,IAAY;QAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,oBAAoB;IAKtB;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IACnF,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,WAAW,CAAC,IAAY,EAAE,QAAsC;QAC1E,MAAM,UAAU,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC9C,UAAU,CAAC,UAAU,GAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;QACjH,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,oBAAoB,CAAC,UAAsB,EAAE,eAAgC,EAAE,mBAAiD;QAC1I,OAAO,oBAAoB,CAAC,6BAA6B,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,UAAU,EAAE,eAAe,CAAC,EAAE,mBAAmB,CAAC,CAAC;IAC9J,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,6BAA6B,CAAC,OAAmB,EAAE,mBAAiD;QAC9G,MAAM,UAAU,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC9C,UAAU,CAAC,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAS,MAAM,EAAE,mBAAmB,CAAC,CAAC,CAAC;QAC/G,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;QACI,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACzB,CAAC;IAED;;;;;;;OAOG;IACK,MAAM,CAAC,uBAAuB,CAClC,UAAsB,EACtB,QAAyB,EACzB,mBAA2B,oBAAoB,CAAC,6BAA6B;QAE7E,MAAM,OAAO,GAAe,EAAE,CAAC;QAC/B,KAAK,IAAI,GAAG,GAAG,gBAAgB,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE;YACjE,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;SACrF;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;OAMG;IACI,QAAQ,CAAC,KAA2B;QACvC,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,MAAc,CAAC;QACnB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE;YACnD,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAC1B,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;SAClF;QACD,OAAO,aAAa,CAAC;IACzB,CAAC;;AA3FuB,kDAA6B,GAAG,EAAE,CAAC;AA8F/D;;;GAGG;AACH,MAAM,eAAe;IAOjB;;;OAGG;IACI,SAAS;QACZ,MAAM,UAAU,GAAQ,EAAE,CAAC;QAC3B,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAC3E,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;QAC3C,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACnD,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,WAAW,CAAC,IAAY,EAAE,QAAsC;QAC1E,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;QACxC,SAAS,CAAC,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;QAClH,SAAS,CAAC,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC;QAChD,SAAS,CAAC,gBAAgB,GAAG,UAAU,CAAC,eAAe,CAAC;QACxD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;OAGG;IACH,YAAmB,cAAsC,EAAE;QACvD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACI,GAAG,CAAC,UAAgC;QACvC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACnC,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,YAAY,CAAC,UAAgC;QAChD,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;IAC7F,CAAC;IAED;;;;;;OAMG;IACI,uBAAuB,CAAC,UAAgC;QAC3D,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACK,mBAAmB;QACvB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QACvB,IAAI,GAAW,CAAC;QAChB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YAC1C,GAAG,GAAG,CAAC,CAAC;YACR,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBAC5B,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;YACH,OAAO,GAAG,CAAC;QACf,CAAC,CAAC,CAAC;QACH,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE;YAC7C,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;gBACxE,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;aAC3B;SACJ;QAED,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC/B,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,eAAe,CAAC,qBAAqB,CAAC,CAAC;SAC7H;IACL,CAAC;;AAzGuB,qCAAqB,GAAG,CAAC,CAAC;AA4GtD;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IAM7B;;;OAGG;IACI,SAAS;QACZ,MAAM,UAAU,GAAQ,EAAE,CAAC;QAC3B,UAAU,CAAC,yBAAyB,GAAG,IAAI,CAAC,0BAA0B,CAAC;QACvE,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;QAC/D,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,CAAC;QACvE,UAAU,CAAC,yBAAyB,GAAG,EAAE,CAAC;QAC1C,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE;YACxD,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,WAAW,CAAC,IAAY;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,UAAU,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC9C,UAAU,CAAC,0BAA0B,GAAG,UAAU,CAAC,yBAAyB,CAAC;QAC7E,UAAU,CAAC,gBAAgB,GAAG,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;QACtF,UAAU,CAAC,oBAAoB,GAAG,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAS,UAAU,CAAC,mBAAmB,CAAC,CAAC;QAC3G,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,UAAU,CAAC,yBAAyB,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;YAC3E,UAAU,CAAC,0BAA0B,CAAC,GAAG,CACrC,UAAU,CAAC,yBAAyB,CAAC,GAAG,CAAC,EACzC,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,yBAAyB,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,oBAAoB,CAAC,CAC9G,CAAC;SACL;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,QAAQ;QAClB,MAAM,IAAI,GAAG,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAEhF,MAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC9C,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE;YAC5C,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;SACvB;QAED,MAAM,QAAQ,GAAG,IAAI,WAAW,CAAC,QAAQ,CACrC,QAAQ,EACR,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5B,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5B,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CACvE,CAAC;QAEF,MAAM,aAAa,GAAG,IAAI,oBAAoB,EAAE,CAAC;QACjD,aAAa,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACtC,aAAa,CAAC,oBAAoB,GAAG,QAAQ,CAAC;QAC9C,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;QArEQ,+BAA0B,GAAW,CAAC,CAAC;QAsE3C,IAAI,CAAC,0BAA0B,GAAG,IAAI,GAAG,EAA2B,CAAC;IACzE,CAAC;IAED;;;;OAIG;IACI,6BAA6B,CAAC,UAAsB,EAAE,cAAsB;QAC/E,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;YACtD,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,eAAe,EAAE,CAAC,CAAC;SAC9E;QAED,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,cAAc,CAAE,CAAC,GAAG,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;IACtK,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,cAAsB;QAC9C,OAAO,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IAClE,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,UAAsB;QAC5C,MAAM,UAAU,GAAG,oBAAoB,CAAC,oBAAoB,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAE3H,MAAM,gBAAgB,GAAa,EAAE,CAAC;QACtC,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,cAAc,EAAE,EAAE;YACxE,IAAI,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,0BAA0B,EAAE;gBAC5E,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;aACzC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,SAAS,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAE,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;QACpH,IAAI,KAAa,CAAC;QAClB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,gBAAgB,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE;YACpD,KAAK,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAE,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YACxG,IAAI,KAAK,GAAG,SAAS,EAAE;gBACnB,SAAS,GAAG,KAAK,CAAC;gBAClB,OAAO,GAAG,GAAG,CAAC;aACjB;SACJ;QACD,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;CACJ", "sourcesContent": ["import type { DeepImmutable, Nullable } from \"../types\";\r\nimport { Matrix, Vector3 } from \"../Maths/math.vector\";\r\n\r\n// This implementation was based on the original MIT-licensed TRACE repository\r\n// from https://github.com/septagon/TRACE.\r\n\r\n/**\r\n * Generic implementation of Levenshtein distance.\r\n */\r\nnamespace Levenshtein {\r\n    /**\r\n     * Alphabet from which to construct sequences to be compared using Levenshtein\r\n     * distance.\r\n     */\r\n    export class Alphabet<T> {\r\n        private _characterToIdx: Map<T, number>;\r\n        private _insertionCosts: number[];\r\n        private _deletionCosts: number[];\r\n        private _substitutionCosts: number[][];\r\n\r\n        /**\r\n         * Serialize the Alphabet to JSON string.\r\n         * @returns JSON serialization\r\n         */\r\n        public serialize(): string {\r\n            const jsonObject: any = {};\r\n\r\n            const characters = new Array<T>(this._characterToIdx.size);\r\n            this._characterToIdx.forEach((v, k) => {\r\n                characters[v] = k;\r\n            });\r\n            jsonObject[\"characters\"] = characters;\r\n\r\n            jsonObject[\"insertionCosts\"] = this._insertionCosts;\r\n            jsonObject[\"deletionCosts\"] = this._deletionCosts;\r\n            jsonObject[\"substitutionCosts\"] = this._substitutionCosts;\r\n\r\n            return JSON.stringify(jsonObject);\r\n        }\r\n\r\n        /**\r\n         * Parse an Alphabet from a JSON serialization.\r\n         * @param json JSON string to deserialize\r\n         * @returns deserialized Alphabet\r\n         */\r\n        public static Deserialize<T>(json: string): Alphabet<T> {\r\n            const jsonObject = JSON.parse(json);\r\n            const alphabet = new Alphabet(jsonObject[\"characters\"] as T[]);\r\n            alphabet._insertionCosts = jsonObject[\"insertionCosts\"];\r\n            alphabet._deletionCosts = jsonObject[\"deletionCosts\"];\r\n            alphabet._substitutionCosts = jsonObject[\"substitutionCosts\"];\r\n            return alphabet;\r\n        }\r\n\r\n        /**\r\n         * Create a new Alphabet.\r\n         * @param characters characters of the alphabet\r\n         * @param charToInsertionCost function mapping characters to insertion costs\r\n         * @param charToDeletionCost function mapping characters to deletion costs\r\n         * @param charsToSubstitutionCost function mapping character pairs to substitution costs\r\n         */\r\n        public constructor(\r\n            characters: Array<T>,\r\n            charToInsertionCost: Nullable<(char: T) => number> = null,\r\n            charToDeletionCost: Nullable<(char: T) => number> = null,\r\n            charsToSubstitutionCost: Nullable<(outChar: T, inChar: T) => number> = null\r\n        ) {\r\n            charToInsertionCost = charToInsertionCost ?? (() => 1);\r\n            charToDeletionCost = charToDeletionCost ?? (() => 1);\r\n            charsToSubstitutionCost = charsToSubstitutionCost ?? ((a: T, b: T) => (a === b ? 0 : 1));\r\n\r\n            this._characterToIdx = new Map<T, number>();\r\n            this._insertionCosts = new Array<number>(characters.length);\r\n            this._deletionCosts = new Array<number>(characters.length);\r\n            this._substitutionCosts = new Array<Array<number>>(characters.length);\r\n\r\n            let c: T;\r\n            for (let outerIdx = 0; outerIdx < characters.length; ++outerIdx) {\r\n                c = characters[outerIdx];\r\n                this._characterToIdx.set(c, outerIdx);\r\n                this._insertionCosts[outerIdx] = charToInsertionCost(c);\r\n                this._deletionCosts[outerIdx] = charToDeletionCost(c);\r\n\r\n                this._substitutionCosts[outerIdx] = new Array<number>(characters.length);\r\n                for (let innerIdx = outerIdx; innerIdx < characters.length; ++innerIdx) {\r\n                    this._substitutionCosts[outerIdx][innerIdx] = charsToSubstitutionCost(c, characters[innerIdx]);\r\n                }\r\n            }\r\n        }\r\n\r\n        /**\r\n         * Get the index (internally-assigned number) for a character.\r\n         * @param char character\r\n         * @returns index\r\n         */\r\n        public getCharacterIdx(char: T): number {\r\n            return this._characterToIdx.get(char)!;\r\n        }\r\n\r\n        /**\r\n         * Get the insertion cost of a character from its index.\r\n         * @param idx character index\r\n         * @returns insertion cost\r\n         */\r\n        public getInsertionCost(idx: number): number {\r\n            return this._insertionCosts[idx];\r\n        }\r\n\r\n        /**\r\n         * Get the deletion cost of a character from its index.\r\n         * @param idx character index\r\n         * @returns deletion cost\r\n         */\r\n        public getDeletionCost(idx: number): number {\r\n            return this._deletionCosts[idx];\r\n        }\r\n\r\n        /**\r\n         * Gets the cost to substitute two characters. NOTE: this cost is\r\n         * required to be bi-directional, meaning it cannot matter which of\r\n         * the provided characters is being removed and which is being inserted.\r\n         * @param idx1 the first character index\r\n         * @param idx2 the second character index\r\n         * @returns substitution cost\r\n         */\r\n        public getSubstitutionCost(idx1: number, idx2: number): number {\r\n            const min = Math.min(idx1, idx2);\r\n            const max = Math.max(idx1, idx2);\r\n\r\n            return this._substitutionCosts[min][max];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Character sequence intended to be compared against other Sequences created\r\n     * with the same Alphabet in order to compute Levenshtein distance.\r\n     */\r\n    export class Sequence<T> {\r\n        private _alphabet: Alphabet<T>;\r\n        private _characters: number[];\r\n\r\n        // Scratch values\r\n        private static readonly _MAX_SEQUENCE_LENGTH = 256;\r\n        private static _CostMatrix = [...Array(Sequence._MAX_SEQUENCE_LENGTH + 1)].map(() => new Array<number>(Sequence._MAX_SEQUENCE_LENGTH + 1));\r\n        private static _InsertionCost: number;\r\n        private static _DeletionCost: number;\r\n        private static _SubstitutionCost: number;\r\n\r\n        /**\r\n         * Serialize to JSON string. JSON representation does NOT include the Alphabet\r\n         * from which this Sequence was created; Alphabet must be independently\r\n         * serialized.\r\n         * @returns JSON string\r\n         */\r\n        public serialize(): string {\r\n            return JSON.stringify(this._characters);\r\n        }\r\n\r\n        /**\r\n         * Deserialize from JSON string and Alphabet. This should be the same Alphabet\r\n         * from which the Sequence was originally created, which must be serialized and\r\n         * deserialized independently so that it can be passed in here.\r\n         * @param json JSON string representation of Sequence\r\n         * @param alphabet Alphabet from which Sequence was originally created\r\n         * @returns Sequence\r\n         */\r\n        public static Deserialize<T>(json: string, alphabet: Alphabet<T>): Sequence<T> {\r\n            const sequence = new Sequence([], alphabet);\r\n            sequence._characters = JSON.parse(json);\r\n            return sequence;\r\n        }\r\n\r\n        /**\r\n         * Create a new Sequence.\r\n         * @param characters characters in the new Sequence\r\n         * @param alphabet Alphabet, which must include all used characters\r\n         */\r\n        public constructor(characters: T[], alphabet: Alphabet<T>) {\r\n            if (characters.length > Sequence._MAX_SEQUENCE_LENGTH) {\r\n                throw new Error(\"Sequences longer than \" + Sequence._MAX_SEQUENCE_LENGTH + \" not supported.\");\r\n            }\r\n            this._alphabet = alphabet;\r\n            this._characters = characters.map((c) => this._alphabet.getCharacterIdx(c));\r\n        }\r\n\r\n        /**\r\n         * Get the distance between this Sequence and another.\r\n         * @param other sequence to compare to\r\n         * @returns Levenshtein distance\r\n         */\r\n        public distance(other: Sequence<T>): number {\r\n            return Sequence._Distance<T>(this, other);\r\n        }\r\n\r\n        /**\r\n         * Compute the Levenshtein distance between two Sequences.\r\n         * @param a first Sequence\r\n         * @param b second Sequence\r\n         * @returns Levenshtein distance\r\n         */\r\n        private static _Distance<T>(a: Sequence<T>, b: Sequence<T>): number {\r\n            const alphabet = a._alphabet;\r\n            if (alphabet !== b._alphabet) {\r\n                throw new Error(\"Cannot Levenshtein compare Sequences built from different alphabets.\");\r\n            }\r\n            const aChars = a._characters;\r\n            const bChars = b._characters;\r\n            const aLength = aChars.length;\r\n            const bLength = bChars.length;\r\n\r\n            const costMatrix = Sequence._CostMatrix;\r\n            costMatrix[0][0] = 0;\r\n            for (let idx = 0; idx < aLength; ++idx) {\r\n                costMatrix[idx + 1][0] = costMatrix[idx][0] + alphabet.getInsertionCost(aChars[idx]);\r\n            }\r\n            for (let idx = 0; idx < bLength; ++idx) {\r\n                costMatrix[0][idx + 1] = costMatrix[0][idx] + alphabet.getInsertionCost(bChars[idx]);\r\n            }\r\n\r\n            for (let aIdx = 0; aIdx < aLength; ++aIdx) {\r\n                for (let bIdx = 0; bIdx < bLength; ++bIdx) {\r\n                    Sequence._InsertionCost = costMatrix[aIdx + 1][bIdx] + alphabet.getInsertionCost(bChars[bIdx]);\r\n                    Sequence._DeletionCost = costMatrix[aIdx][bIdx + 1] + alphabet.getDeletionCost(aChars[aIdx]);\r\n                    Sequence._SubstitutionCost = costMatrix[aIdx][bIdx] + alphabet.getSubstitutionCost(aChars[aIdx], bChars[bIdx]);\r\n\r\n                    costMatrix[aIdx + 1][bIdx + 1] = Math.min(Sequence._InsertionCost, Sequence._DeletionCost, Sequence._SubstitutionCost);\r\n                }\r\n            }\r\n\r\n            return costMatrix[aLength][bLength];\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * A 3D trajectory consisting of an order list of vectors describing a\r\n * path of motion through 3D space.\r\n */\r\nexport class Trajectory {\r\n    private _points: Vector3[];\r\n    private readonly _segmentLength: number;\r\n\r\n    /**\r\n     * Serialize to JSON.\r\n     * @returns serialized JSON string\r\n     */\r\n    public serialize(): string {\r\n        return JSON.stringify(this);\r\n    }\r\n\r\n    /**\r\n     * Deserialize from JSON.\r\n     * @param json serialized JSON string\r\n     * @returns deserialized Trajectory\r\n     */\r\n    public static Deserialize(json: string): Trajectory {\r\n        const jsonObject = JSON.parse(json);\r\n        const trajectory = new Trajectory(jsonObject[\"_segmentLength\"]);\r\n        trajectory._points = jsonObject[\"_points\"].map((pt: any) => {\r\n            return new Vector3(pt[\"_x\"], pt[\"_y\"], pt[\"_z\"]);\r\n        });\r\n        return trajectory;\r\n    }\r\n\r\n    /**\r\n     * Create a new empty Trajectory.\r\n     * @param segmentLength radius of discretization for Trajectory points\r\n     */\r\n    public constructor(segmentLength: number = 0.01) {\r\n        this._points = [];\r\n        this._segmentLength = segmentLength;\r\n    }\r\n\r\n    /**\r\n     * Get the length of the Trajectory.\r\n     * @returns length of the Trajectory\r\n     */\r\n    public getLength(): number {\r\n        return this._points.length * this._segmentLength;\r\n    }\r\n\r\n    /**\r\n     * Append a new point to the Trajectory.\r\n     * NOTE: This implementation has many allocations.\r\n     * @param point point to append to the Trajectory\r\n     */\r\n    public add(point: DeepImmutable<Vector3>): void {\r\n        let numPoints = this._points.length;\r\n        if (numPoints === 0) {\r\n            this._points.push(point.clone());\r\n        } else {\r\n            const getT = () => this._segmentLength / Vector3.Distance(this._points[numPoints - 1], point);\r\n            for (let t = getT(); t <= 1.0; t = getT()) {\r\n                const newPoint = this._points[numPoints - 1].scale(1.0 - t);\r\n                point.scaleAndAddToRef(t, newPoint);\r\n                this._points.push(newPoint);\r\n                ++numPoints;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create a new Trajectory with a segment length chosen to make it\r\n     * probable that the new Trajectory will have a specified number of\r\n     * segments. This operation is imprecise.\r\n     * @param targetResolution number of segments desired\r\n     * @returns new Trajectory with approximately the requested number of segments\r\n     */\r\n    public resampleAtTargetResolution(targetResolution: number): Trajectory {\r\n        const resampled = new Trajectory(this.getLength() / targetResolution);\r\n        this._points.forEach((pt) => {\r\n            resampled.add(pt);\r\n        });\r\n        return resampled;\r\n    }\r\n\r\n    /**\r\n     * Convert Trajectory segments into tokenized representation. This\r\n     * representation is an array of numbers where each nth number is the\r\n     * index of the token which is most similar to the nth segment of the\r\n     * Trajectory.\r\n     * @param tokens list of vectors which serve as discrete tokens\r\n     * @returns list of indices of most similar token per segment\r\n     */\r\n    public tokenize(tokens: DeepImmutable<Vector3[]>): number[] {\r\n        const tokenization: number[] = [];\r\n\r\n        const segmentDir = new Vector3();\r\n        for (let idx = 2; idx < this._points.length; ++idx) {\r\n            if (Trajectory._TransformSegmentDirToRef(this._points[idx - 2], this._points[idx - 1], this._points[idx], segmentDir)) {\r\n                tokenization.push(Trajectory._TokenizeSegment(segmentDir, tokens));\r\n            }\r\n        }\r\n\r\n        return tokenization;\r\n    }\r\n\r\n    private static _ForwardDir = new Vector3();\r\n    private static _InverseFromVec = new Vector3();\r\n    private static _UpDir = new Vector3();\r\n    private static _FromToVec = new Vector3();\r\n    private static _LookMatrix = new Matrix();\r\n\r\n    /**\r\n     * Transform the rotation (i.e., direction) of a segment to isolate\r\n     * the relative transformation represented by the segment. This operation\r\n     * may or may not succeed due to singularities in the equations that define\r\n     * motion relativity in this context.\r\n     * @param priorVec the origin of the prior segment\r\n     * @param fromVec the origin of the current segment\r\n     * @param toVec the destination of the current segment\r\n     * @param result reference to output variable\r\n     * @returns whether or not transformation was successful\r\n     */\r\n    private static _TransformSegmentDirToRef(priorVec: DeepImmutable<Vector3>, fromVec: DeepImmutable<Vector3>, toVec: DeepImmutable<Vector3>, result: Vector3): boolean {\r\n        const DOT_PRODUCT_SAMPLE_REJECTION_THRESHOLD = 0.98;\r\n\r\n        fromVec.subtractToRef(priorVec, Trajectory._ForwardDir);\r\n        Trajectory._ForwardDir.normalize();\r\n        fromVec.scaleToRef(-1, Trajectory._InverseFromVec);\r\n        Trajectory._InverseFromVec.normalize();\r\n\r\n        if (Math.abs(Vector3.Dot(Trajectory._ForwardDir, Trajectory._InverseFromVec)) > DOT_PRODUCT_SAMPLE_REJECTION_THRESHOLD) {\r\n            return false;\r\n        }\r\n\r\n        Vector3.CrossToRef(Trajectory._ForwardDir, Trajectory._InverseFromVec, Trajectory._UpDir);\r\n        Trajectory._UpDir.normalize();\r\n        Matrix.LookAtLHToRef(priorVec, fromVec, Trajectory._UpDir, Trajectory._LookMatrix);\r\n        toVec.subtractToRef(fromVec, Trajectory._FromToVec);\r\n        Trajectory._FromToVec.normalize();\r\n        Vector3.TransformNormalToRef(Trajectory._FromToVec, Trajectory._LookMatrix, result);\r\n        return true;\r\n    }\r\n\r\n    private static _BestMatch: number;\r\n    private static _Score: number;\r\n    private static _BestScore: number;\r\n\r\n    /**\r\n     * Determine which token vector is most similar to the\r\n     * segment vector.\r\n     * @param segment segment vector\r\n     * @param tokens token vector list\r\n     * @returns index of the most similar token to the segment\r\n     */\r\n    private static _TokenizeSegment(segment: DeepImmutable<Vector3>, tokens: DeepImmutable<Vector3[]>): number {\r\n        Trajectory._BestMatch = 0;\r\n        Trajectory._Score = Vector3.Dot(segment, tokens[0]);\r\n        Trajectory._BestScore = Trajectory._Score;\r\n        for (let idx = 1; idx < tokens.length; ++idx) {\r\n            Trajectory._Score = Vector3.Dot(segment, tokens[idx]);\r\n            if (Trajectory._Score > Trajectory._BestScore) {\r\n                Trajectory._BestMatch = idx;\r\n                Trajectory._BestScore = Trajectory._Score;\r\n            }\r\n        }\r\n\r\n        return Trajectory._BestMatch;\r\n    }\r\n}\r\n\r\n/**\r\n * Collection of vectors intended to be used as the basis of Trajectory\r\n * tokenization for Levenshtein distance comparison. Canonically, a\r\n * Vector3Alphabet will resemble a \"spikeball\" of vectors distributed\r\n * roughly evenly over the surface of the unit sphere.\r\n */\r\nclass Vector3Alphabet {\r\n    /**\r\n     * Characters in the alphabet.\r\n     * NOTE: There is no reason for this property to exist and this class should just extend\r\n     * Array<Vector3>, except that doing so produces bizarre build-time errors indicating that\r\n     * the ES5 library itself fails its own TypeDoc validation.\r\n     */\r\n    public chars: Vector3[];\r\n\r\n    /**\r\n     * Helper method to create new \"spikeball\" Vector3Alphabets. Uses a naive\r\n     * optimize-from-random strategy to space points around the unit sphere\r\n     * surface as a simple alternative to really doing the math to tile the\r\n     * sphere.\r\n     * @param alphabetSize size of the desired alphabet\r\n     * @param iterations number of iterations over which to optimize the \"spikeball\"\r\n     * @param startingStepSize distance factor to move points in early optimization iterations\r\n     * @param endingStepSize distance factor to move points in late optimization iterations\r\n     * @param fixedValues alphabet \"characters\" that are required and cannot be moved by optimization\r\n     * @returns a new randomly generated and optimized Vector3Alphabet of the specified size\r\n     */\r\n    public static Generate(\r\n        alphabetSize: number = 64,\r\n        iterations: number = 256,\r\n        startingStepSize: number = 0.1,\r\n        endingStepSize: number = 0.001,\r\n        fixedValues: DeepImmutable<Vector3[]> = []\r\n    ): Vector3Alphabet {\r\n        const EPSILON = 0.001;\r\n        const EPSILON_SQUARED = EPSILON * EPSILON;\r\n\r\n        const alphabet = new Vector3Alphabet(alphabetSize);\r\n        for (let idx = 0; idx < alphabetSize; ++idx) {\r\n            alphabet.chars[idx] = new Vector3(Math.random() - 0.5, Math.random() - 0.5, Math.random() - 0.5);\r\n            alphabet.chars[idx].normalize();\r\n        }\r\n\r\n        for (let idx = 0; idx < fixedValues.length; ++idx) {\r\n            alphabet.chars[idx].copyFrom(fixedValues[idx]);\r\n        }\r\n\r\n        let stepSize: number;\r\n        let distSq: number;\r\n        const force = new Vector3();\r\n        const scratch = new Vector3();\r\n        const lerp = (l: number, r: number, t: number) => (1.0 - t) * l + t * r;\r\n        for (let iteration = 0; iteration < iterations; ++iteration) {\r\n            stepSize = lerp(startingStepSize, endingStepSize, iteration / (iterations - 1));\r\n            for (let idx = fixedValues.length; idx < alphabet.chars.length; ++idx) {\r\n                force.copyFromFloats(0, 0, 0);\r\n                alphabet.chars.forEach((pt) => {\r\n                    alphabet.chars[idx].subtractToRef(pt, scratch);\r\n                    distSq = scratch.lengthSquared();\r\n                    if (distSq > EPSILON_SQUARED) {\r\n                        scratch.scaleAndAddToRef(1 / (scratch.lengthSquared() * distSq), force);\r\n                    }\r\n                });\r\n                force.scaleInPlace(stepSize);\r\n                alphabet.chars[idx].addInPlace(force);\r\n                alphabet.chars[idx].normalize();\r\n            }\r\n        }\r\n\r\n        return alphabet;\r\n    }\r\n\r\n    /**\r\n     * Serialize to JSON.\r\n     * @returns JSON serialization\r\n     */\r\n    public serialize(): string {\r\n        return JSON.stringify(this.chars);\r\n    }\r\n\r\n    /**\r\n     * Deserialize from JSON.\r\n     * @param json JSON serialization\r\n     * @returns deserialized Vector3Alphabet\r\n     */\r\n    public static Deserialize(json: string): Vector3Alphabet {\r\n        const jsonObject = JSON.parse(json);\r\n        const alphabet = new Vector3Alphabet(jsonObject.length);\r\n        for (let idx = 0; idx < jsonObject.length; ++idx) {\r\n            alphabet.chars[idx] = new Vector3(jsonObject[idx][\"_x\"], jsonObject[idx][\"_y\"], jsonObject[idx][\"_z\"]);\r\n        }\r\n        return alphabet;\r\n    }\r\n\r\n    private constructor(size: number) {\r\n        this.chars = new Array(size);\r\n    }\r\n}\r\n\r\n/**\r\n * Class which formalizes the manner in which a Vector3Alphabet is used to tokenize and\r\n * describe a Trajectory. This class houses the functionality which determines what\r\n * attributes of Trajectories are and are not considered important, such as scale.\r\n */\r\nclass TrajectoryDescriptor {\r\n    private static readonly _FINEST_DESCRIPTOR_RESOLUTION = 32;\r\n\r\n    private _sequences: Levenshtein.Sequence<number>[];\r\n\r\n    /**\r\n     * Serialize to JSON.\r\n     * @returns JSON serialization\r\n     */\r\n    public serialize(): string {\r\n        return JSON.stringify(this._sequences.map((sequence) => sequence.serialize()));\r\n    }\r\n\r\n    /**\r\n     * Deserialize from JSON string and Alphabet. This should be the same Alphabet\r\n     * from which the descriptor was originally created, which must be serialized and\r\n     * deserialized independently so that it can be passed in here.\r\n     * @param json JSON serialization\r\n     * @param alphabet Alphabet from which descriptor was originally created\r\n     * @returns deserialized TrajectoryDescriptor\r\n     */\r\n    public static Deserialize(json: string, alphabet: Levenshtein.Alphabet<number>): TrajectoryDescriptor {\r\n        const descriptor = new TrajectoryDescriptor();\r\n        descriptor._sequences = (JSON.parse(json) as string[]).map((s) => Levenshtein.Sequence.Deserialize(s, alphabet));\r\n        return descriptor;\r\n    }\r\n\r\n    /**\r\n     * Create a new TrajectoryDescriptor to describe a provided Trajectory according\r\n     * to the provided alphabets.\r\n     * @param trajectory Trajectory to be described\r\n     * @param vector3Alphabet Vector3Alphabet to be used to tokenize the Trajectory\r\n     * @param levenshteinAlphabet Levenshtein.Alphabet to be used as basis for comparison with other descriptors\r\n     * @returns TrajectoryDescriptor describing provided Trajectory\r\n     */\r\n    public static CreateFromTrajectory(trajectory: Trajectory, vector3Alphabet: Vector3Alphabet, levenshteinAlphabet: Levenshtein.Alphabet<number>): TrajectoryDescriptor {\r\n        return TrajectoryDescriptor.CreateFromTokenizationPyramid(TrajectoryDescriptor._GetTokenizationPyramid(trajectory, vector3Alphabet), levenshteinAlphabet);\r\n    }\r\n\r\n    /**\r\n     * Create a new TrajectoryDescriptor from a pre-existing pyramid of tokens.\r\n     * NOTE: This function exists to support an outdated serialization mechanism and should\r\n     * be deleted if it is no longer useful.\r\n     * @param pyramid tokenization pyramid\r\n     * @param levenshteinAlphabet Levenshtein.Alphabet to be uses as basis for comparison with other descriptors\r\n     * @returns TrajectoryDescriptor describing the Trajectory from which the pyramid was built\r\n     */\r\n    public static CreateFromTokenizationPyramid(pyramid: number[][], levenshteinAlphabet: Levenshtein.Alphabet<number>): TrajectoryDescriptor {\r\n        const descriptor = new TrajectoryDescriptor();\r\n        descriptor._sequences = pyramid.map((tokens) => new Levenshtein.Sequence<number>(tokens, levenshteinAlphabet));\r\n        return descriptor;\r\n    }\r\n\r\n    private constructor() {\r\n        this._sequences = [];\r\n    }\r\n\r\n    /**\r\n     * Create the tokenization pyramid for the provided Trajectory according to the given\r\n     * Vector3Alphabet.\r\n     * @param trajectory Trajectory to be tokenized\r\n     * @param alphabet Vector3Alphabet containing tokens\r\n     * @param targetResolution finest resolution of descriptor\r\n     * @returns tokenization pyramid for Trajectory\r\n     */\r\n    private static _GetTokenizationPyramid(\r\n        trajectory: Trajectory,\r\n        alphabet: Vector3Alphabet,\r\n        targetResolution: number = TrajectoryDescriptor._FINEST_DESCRIPTOR_RESOLUTION\r\n    ): number[][] {\r\n        const pyramid: number[][] = [];\r\n        for (let res = targetResolution; res > 4; res = Math.floor(res / 2)) {\r\n            pyramid.push(trajectory.resampleAtTargetResolution(res).tokenize(alphabet.chars));\r\n        }\r\n        return pyramid;\r\n    }\r\n\r\n    /**\r\n     * Calculate a distance metric between this TrajectoryDescriptor and another. This is\r\n     * essentially a similarity score and does not directly represent Euclidean distance,\r\n     * edit distance, or any other formal distance metric.\r\n     * @param other TrajectoryDescriptor from which to determine distance\r\n     * @returns distance, a nonnegative similarity score where larger values indicate dissimilarity\r\n     */\r\n    public distance(other: TrajectoryDescriptor): number {\r\n        let totalDistance = 0;\r\n        let weight: number;\r\n        for (let idx = 0; idx < this._sequences.length; ++idx) {\r\n            weight = Math.pow(2, idx);\r\n            totalDistance += weight * this._sequences[idx].distance(other._sequences[idx]);\r\n        }\r\n        return totalDistance;\r\n    }\r\n}\r\n\r\n/**\r\n * A set of TrajectoryDescriptors defined to be \"the same.\" This is essentially a helper\r\n * class to facilitate methods of Trajectory clustering.\r\n */\r\nclass TrajectoryClass {\r\n    private static readonly _MIN_AVERAGE_DISTANCE = 1;\r\n\r\n    private _descriptors: TrajectoryDescriptor[];\r\n    private _centroidIdx: number;\r\n    private _averageDistance: number;\r\n\r\n    /**\r\n     * Serialize to JSON.\r\n     * @returns JSON serialization\r\n     */\r\n    public serialize(): string {\r\n        const jsonObject: any = {};\r\n        jsonObject.descriptors = this._descriptors.map((desc) => desc.serialize());\r\n        jsonObject.centroidIdx = this._centroidIdx;\r\n        jsonObject.averageDistance = this._averageDistance;\r\n        return JSON.stringify(jsonObject);\r\n    }\r\n\r\n    /**\r\n     * Deserialize from JSON string and Alphabet. This should be the same Alphabet\r\n     * from which the descriptors were originally created, which must be serialized and\r\n     * deserialized independently so that it can be passed in here.\r\n     * @param json JSON string representation\r\n     * @param alphabet Alphabet from which TrajectoryDescriptors were originally created\r\n     * @returns deserialized TrajectoryDescriptor\r\n     */\r\n    public static Deserialize(json: string, alphabet: Levenshtein.Alphabet<number>): TrajectoryClass {\r\n        const jsonObject = JSON.parse(json);\r\n        const described = new TrajectoryClass();\r\n        described._descriptors = jsonObject.descriptors.map((s: string) => TrajectoryDescriptor.Deserialize(s, alphabet));\r\n        described._centroidIdx = jsonObject.centroidIdx;\r\n        described._averageDistance = jsonObject.averageDistance;\r\n        return described;\r\n    }\r\n\r\n    /**\r\n     * Create a new DescribedTrajectory.\r\n     * @param descriptors currently-known TrajectoryDescriptors, if any\r\n     */\r\n    public constructor(descriptors: TrajectoryDescriptor[] = []) {\r\n        this._descriptors = descriptors;\r\n        this._centroidIdx = -1;\r\n        this._averageDistance = 0;\r\n\r\n        this._refreshDescription();\r\n    }\r\n\r\n    /**\r\n     * Add a new TrajectoryDescriptor to the list of descriptors known to describe\r\n     * this same DescribedTrajectory.\r\n     * @param descriptor descriptor to be added\r\n     */\r\n    public add(descriptor: TrajectoryDescriptor): void {\r\n        this._descriptors.push(descriptor);\r\n        this._refreshDescription();\r\n    }\r\n\r\n    /**\r\n     * Compute the cost, which is inversely related to the likelihood that the provided\r\n     * TrajectoryDescriptor describes a Trajectory that is considered to be the same as\r\n     * the class represented by this DescribedTrajectory.\r\n     * @param descriptor the descriptor to be costed\r\n     * @returns cost of the match, which is a nonnegative similarity metric where larger values indicate dissimilarity\r\n     */\r\n    public getMatchCost(descriptor: TrajectoryDescriptor): number {\r\n        return descriptor.distance(this._descriptors[this._centroidIdx]) / this._averageDistance;\r\n    }\r\n\r\n    /**\r\n     * Compute the minimum distance between the queried TrajectoryDescriptor and a\r\n     * descriptor which is a member of this collection. This is an alternative way of\r\n     * conceptualizing match cost from getMatchCost(), and it serves a different function.\r\n     * @param descriptor the descriptor to find the minimum distance to\r\n     * @returns minimum descriptor distance to a member descriptor of this DescribedTrajectory\r\n     */\r\n    public getMatchMinimumDistance(descriptor: TrajectoryDescriptor): number {\r\n        return Math.min(...this._descriptors.map((desc) => desc.distance(descriptor)));\r\n    }\r\n\r\n    /**\r\n     * Refreshes the internal representation of this DescribedTrajectory.\r\n     */\r\n    private _refreshDescription(): void {\r\n        this._centroidIdx = -1;\r\n        let sum: number;\r\n        const distances = this._descriptors.map((a) => {\r\n            sum = 0;\r\n            this._descriptors.forEach((b) => {\r\n                sum += a.distance(b);\r\n            });\r\n            return sum;\r\n        });\r\n        for (let idx = 0; idx < distances.length; ++idx) {\r\n            if (this._centroidIdx < 0 || distances[idx] < distances[this._centroidIdx]) {\r\n                this._centroidIdx = idx;\r\n            }\r\n        }\r\n\r\n        this._averageDistance = 0;\r\n        this._descriptors.forEach((desc) => {\r\n            this._averageDistance += desc.distance(this._descriptors[this._centroidIdx]);\r\n        });\r\n        if (this._descriptors.length > 0) {\r\n            this._averageDistance = Math.max(this._averageDistance / this._descriptors.length, TrajectoryClass._MIN_AVERAGE_DISTANCE);\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * Class representing a set of known, named trajectories to which Trajectories can be\r\n * added and using which Trajectories can be recognized.\r\n */\r\nexport class TrajectoryClassifier {\r\n    private _maximumAllowableMatchCost: number = 4;\r\n    private _vector3Alphabet: Vector3Alphabet;\r\n    private _levenshteinAlphabet: Levenshtein.Alphabet<number>;\r\n    private _nameToDescribedTrajectory: Map<string, TrajectoryClass>;\r\n\r\n    /**\r\n     * Serialize to JSON.\r\n     * @returns JSON serialization\r\n     */\r\n    public serialize(): string {\r\n        const jsonObject: any = {};\r\n        jsonObject.maximumAllowableMatchCost = this._maximumAllowableMatchCost;\r\n        jsonObject.vector3Alphabet = this._vector3Alphabet.serialize();\r\n        jsonObject.levenshteinAlphabet = this._levenshteinAlphabet.serialize();\r\n        jsonObject.nameToDescribedTrajectory = [];\r\n        this._nameToDescribedTrajectory.forEach((described, name) => {\r\n            jsonObject.nameToDescribedTrajectory.push(name);\r\n            jsonObject.nameToDescribedTrajectory.push(described.serialize());\r\n        });\r\n        return JSON.stringify(jsonObject);\r\n    }\r\n\r\n    /**\r\n     * Deserialize from JSON.\r\n     * @param json JSON serialization\r\n     * @returns deserialized TrajectorySet\r\n     */\r\n    public static Deserialize(json: string): TrajectoryClassifier {\r\n        const jsonObject = JSON.parse(json);\r\n        const classifier = new TrajectoryClassifier();\r\n        classifier._maximumAllowableMatchCost = jsonObject.maximumAllowableMatchCost;\r\n        classifier._vector3Alphabet = Vector3Alphabet.Deserialize(jsonObject.vector3Alphabet);\r\n        classifier._levenshteinAlphabet = Levenshtein.Alphabet.Deserialize<number>(jsonObject.levenshteinAlphabet);\r\n        for (let idx = 0; idx < jsonObject.nameToDescribedTrajectory.length; idx += 2) {\r\n            classifier._nameToDescribedTrajectory.set(\r\n                jsonObject.nameToDescribedTrajectory[idx],\r\n                TrajectoryClass.Deserialize(jsonObject.nameToDescribedTrajectory[idx + 1], classifier._levenshteinAlphabet)\r\n            );\r\n        }\r\n        return classifier;\r\n    }\r\n\r\n    /**\r\n     * Initialize a new empty TrajectorySet with auto-generated Alphabets.\r\n     * VERY naive, need to be generating these things from known\r\n     * sets. Better version later, probably eliminating this one.\r\n     * @returns auto-generated TrajectorySet\r\n     */\r\n    public static Generate(): TrajectoryClassifier {\r\n        const vecs = Vector3Alphabet.Generate(64, 256, 0.1, 0.001, [Vector3.Forward()]);\r\n\r\n        const charIdxs = new Array(vecs.chars.length);\r\n        for (let idx = 0; idx < charIdxs.length; ++idx) {\r\n            charIdxs[idx] = idx;\r\n        }\r\n\r\n        const alphabet = new Levenshtein.Alphabet<number>(\r\n            charIdxs,\r\n            (idx) => (idx === 0 ? 0 : 1),\r\n            (idx) => (idx === 0 ? 0 : 1),\r\n            (a, b) => Math.min(1 - Vector3.Dot(vecs.chars[a], vecs.chars[b]), 1)\r\n        );\r\n\r\n        const trajectorySet = new TrajectoryClassifier();\r\n        trajectorySet._vector3Alphabet = vecs;\r\n        trajectorySet._levenshteinAlphabet = alphabet;\r\n        return trajectorySet;\r\n    }\r\n\r\n    private constructor() {\r\n        this._nameToDescribedTrajectory = new Map<string, TrajectoryClass>();\r\n    }\r\n\r\n    /**\r\n     * Add a new Trajectory to the set with a given name.\r\n     * @param trajectory new Trajectory to be added\r\n     * @param classification name to which to add the Trajectory\r\n     */\r\n    public addTrajectoryToClassification(trajectory: Trajectory, classification: string): void {\r\n        if (!this._nameToDescribedTrajectory.has(classification)) {\r\n            this._nameToDescribedTrajectory.set(classification, new TrajectoryClass());\r\n        }\r\n\r\n        this._nameToDescribedTrajectory.get(classification)!.add(TrajectoryDescriptor.CreateFromTrajectory(trajectory, this._vector3Alphabet, this._levenshteinAlphabet));\r\n    }\r\n\r\n    /**\r\n     * Remove a known named trajectory and all Trajectories associated with it.\r\n     * @param classification name to remove\r\n     * @returns whether anything was removed\r\n     */\r\n    public deleteClassification(classification: string): boolean {\r\n        return this._nameToDescribedTrajectory.delete(classification);\r\n    }\r\n\r\n    /**\r\n     * Attempt to recognize a Trajectory from among all the classifications\r\n     * already known to the classifier.\r\n     * @param trajectory Trajectory to be recognized\r\n     * @returns classification of Trajectory if recognized, null otherwise\r\n     */\r\n    public classifyTrajectory(trajectory: Trajectory): Nullable<string> {\r\n        const descriptor = TrajectoryDescriptor.CreateFromTrajectory(trajectory, this._vector3Alphabet, this._levenshteinAlphabet);\r\n\r\n        const allowableMatches: string[] = [];\r\n        this._nameToDescribedTrajectory.forEach((trajectoryClass, classification) => {\r\n            if (trajectoryClass.getMatchCost(descriptor) < this._maximumAllowableMatchCost) {\r\n                allowableMatches.push(classification);\r\n            }\r\n        });\r\n\r\n        if (allowableMatches.length === 0) {\r\n            return null;\r\n        }\r\n\r\n        let bestIdx = 0;\r\n        let bestMatch = this._nameToDescribedTrajectory.get(allowableMatches[bestIdx])!.getMatchMinimumDistance(descriptor);\r\n        let match: number;\r\n        for (let idx = 0; idx < allowableMatches.length; ++idx) {\r\n            match = this._nameToDescribedTrajectory.get(allowableMatches[idx])!.getMatchMinimumDistance(descriptor);\r\n            if (match < bestMatch) {\r\n                bestMatch = match;\r\n                bestIdx = idx;\r\n            }\r\n        }\r\n        return allowableMatches[bestIdx];\r\n    }\r\n}\r\n"]}