import type { Behavior } from "../../Behaviors/behavior";
import type { ArcRotateCamera } from "../../Cameras/arcRotateCamera";
import { BackEase } from "../../Animations/easing";
/**
 * Add a bouncing effect to an ArcRotateCamera when reaching a specified minimum and maximum radius
 * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#bouncing-behavior
 */
export declare class BouncingBehavior implements Behavior<ArcRotateCamera> {
    /**
     * Gets the name of the behavior.
     */
    get name(): string;
    /**
     * The easing function used by animations
     */
    static EasingFunction: BackEase;
    /**
     * The easing mode used by animations
     */
    static EasingMode: number;
    /**
     * The duration of the animation, in milliseconds
     */
    transitionDuration: number;
    /**
     * Length of the distance animated by the transition when lower radius is reached
     */
    lowerRadiusTransitionRange: number;
    /**
     * Length of the distance animated by the transition when upper radius is reached
     */
    upperRadiusTransitionRange: number;
    private _autoTransitionRange;
    /**
     * Gets a value indicating if the lowerRadiusTransitionRange and upperRadiusTransitionRange are defined automatically
     */
    get autoTransitionRange(): boolean;
    /**
     * Sets a value indicating if the lowerRadiusTransitionRange and upperRadiusTransitionRange are defined automatically
     * Transition ranges will be set to 5% of the bounding box diagonal in world space
     */
    set autoTransitionRange(value: boolean);
    private _attachedCamera;
    private _onAfterCheckInputsObserver;
    private _onMeshTargetChangedObserver;
    /**
     * Initializes the behavior.
     */
    init(): void;
    /**
     * Attaches the behavior to its arc rotate camera.
     * @param camera Defines the camera to attach the behavior to
     */
    attach(camera: ArcRotateCamera): void;
    /**
     * Detaches the behavior from its current arc rotate camera.
     */
    detach(): void;
    private _radiusIsAnimating;
    private _radiusBounceTransition;
    private _animatables;
    private _cachedWheelPrecision;
    /**
     * Checks if the camera radius is at the specified limit. Takes into account animation locks.
     * @param radiusLimit The limit to check against.
     * @returns Bool to indicate if at limit.
     */
    private _isRadiusAtLimit;
    /**
     * Applies an animation to the radius of the camera, extending by the radiusDelta.
     * @param radiusDelta The delta by which to animate to. Can be negative.
     */
    private _applyBoundRadiusAnimation;
    /**
     * Removes all animation locks. Allows new animations to be added to any of the camera properties.
     */
    protected _clearAnimationLocks(): void;
    /**
     * Stops and removes all animations that have been applied to the camera
     */
    stopAllAnimations(): void;
}
