{"version": 3, "file": "WebXRSpaceWarp.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRSpaceWarp.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAEjF,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAK9D,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AACjD,OAAO,EAAE,mBAAmB,EAAE,MAAM,8CAA8C,CAAC;AACnF,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAIhE,OAAO,iCAAiC,CAAC;AACzC,OAAO,+BAA+B,CAAC;AAGvC;;GAEG;AACH,MAAM,OAAO,uBAAwB,SAAQ,mBAAmB;IAM5D;;;;;;OAMG;IACH,YAAY,mBAAiC,EAAE,mBAAiC,EAAE,KAAa,EAAE,OAAuE,GAAG;QACvK,KAAK,CAAC,eAAe,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC,sBAAsB,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAZtI,qBAAgB,GAA8C,EAAE,CAAC;QACjE,2BAAsB,GAAkB,EAAE,CAAC;QAC3C,wBAAmB,GAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QAW3E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,EAAG;aAChC,SAAS,EAAE;aACX,kCAAkC,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;QAChI,IAAI,CAAC,aAA0C,CAAC,wBAAwB,GAAG,IAAI,CAAC;QACjF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,OAAQ,CAAC;QAC5C,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC,kBAAkB,CAAC;QAEpD,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,iBAAiB,GAAG,IAAI,cAAc,CACvC,0BAA0B,EAC1B,KAAK,EACL;gBACI,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,UAAU;aACvB,EACD;gBACI,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,wBAAwB,EAAE,yBAAyB,CAAC;aACjI,CACJ,CAAC;YACF,IAAI,CAAC,iBAAiB,CAAC,oCAAoC,GAAG,IAAI,CAAC;YACnE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACjD,yHAAyH;gBACzH,gKAAgK;gBAChK,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACjH,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC1G,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBACnE,sCAAsC;gBACtC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,wBAAwB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpG,qBAAqB;gBACrB,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,yBAAyB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;gBAErG,uDAAuD;gBACvD,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;gBACjE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;SACnC;IACL,CAAC;IAEM,MAAM,CAAC,uBAAgC,KAAK,EAAE,eAAwB,KAAK;QAC9E,gCAAgC;QAChC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;QACjC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,uDAAuD;QACvD,IAAI,KAAK,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACjC,KAAK,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBACrC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAClD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC;YAC3C,CAAC,CAAC,CAAC;SACN;QAED,KAAK,CAAC,MAAM,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;QAEjD,6BAA6B;QAC7B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO;SACV;QACD,IAAI,CAAC,QAAQ,EAAG,CAAC,SAAS,EAAE,CAAC,wBAAwB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9E,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,CAAC,CAAC;IACb,CAAC;IAEM,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;IACrC,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,yCAAyC;IAMlD,YACuB,MAAa,EACb,iBAAsC,EACtC,eAA+B;QAF/B,WAAM,GAAN,MAAM,CAAO;QACb,sBAAiB,GAAjB,iBAAiB,CAAqB;QACtC,oBAAe,GAAf,eAAe,CAAgB;QAR5C,mBAAc,GAAG,IAAI,GAAG,EAA2B,CAAC;QACpD,0BAAqB,GAAG,IAAI,GAAG,EAA8B,CAAC;QASpE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;IACtC,CAAC;IAEO,mBAAmB,CAAC,IAAY;QACpC,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,CAAC;QACnE,IAAI,CAAC,YAAY,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;SACzF;QACD,IAAI,YAAY,CAAC,SAAS,KAAK,mBAAmB,EAAE;YAChD,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;SACtF;QACD,MAAM,KAAK,GAAG,YAAY,CAAC,KAA0B,CAAC;QACtD,OAAO,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC7D,CAAC;IAES,uBAAuB,CAAC,QAAkB,EAAE,QAAyB;QAC3E,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QACf,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QACf,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,wBAAyB,CAAC;QACpD,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,yBAA0B,CAAC;IAC1D,CAAC;IAES,0BAA0B,CAChC,KAAa,EACb,MAAc,EACd,WAAuC,EACvC,mBAAiC,EACjC,mBAAiC;QAEjC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;SACzC;QAED,MAAM,WAAW,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;QAEtC,yDAAyD;QACzD,MAAM,mBAAmB,GAAG,IAAI,uBAAuB,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAC5H,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,YAAwC,CAAC;QACzF,IAAI,WAAW,EAAE;YACb,mBAAmB,CAAC,YAAY,GAAG,WAAW,CAAC;SAClD;QAED,0BAA0B;QAC1B,mBAAmB,CAAC,kBAAkB,GAAG,mBAAmB,CAAC;QAC7D,mBAAmB,CAAC,yBAAyB,GAAG,mBAAmB,CAAC;QAEpE,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;QACvC,mBAAmB,CAAC,mBAAmB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;QAErD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAES,2BAA2B,CAAC,QAAyB,EAAE,IAAY;QACzE,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,mBAAmB,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEnE,MAAM,KAAK,GAAG,QAAQ,CAAC,wBAAyB,CAAC;QACjD,MAAM,MAAM,GAAG,QAAQ,CAAC,yBAA0B,CAAC;QAEnD,IAAI,CAAC,mBAAmB,IAAI,YAAY,EAAE,YAAY,KAAK,KAAK,IAAI,YAAY,EAAE,aAAa,IAAI,MAAM,EAAE;YACvG,mBAAmB,GAAG,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,mBAAoB,EAAE,QAAQ,CAAC,mBAAoB,CAAC,CAAC;YACzI,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;YAE9D,IAAI,CAAC,sBAAsB,GAAG;gBAC1B,gBAAgB,EAAE,KAAK;gBACvB,iBAAiB,EAAE,MAAM;aAC5B,CAAC;SACL;QAED,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAExC,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,qBAAqB,CAAC,QAAkB,EAAE,IAAY;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QACjF,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,kBAAkB,CAAC,IAAY;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,QAAQ,EAAE;YACV,uFAAuF;YACvF,QAAQ,CAAC,mBAAmB,CAAC;YAC7B,QAAQ,CAAC,mBAAmB,CAAC;SAChC;IACL,CAAC;IAEM,4BAA4B,CAAC,IAAW;QAC3C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,6BAA6B,CAAC,IAAY;QAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,QAAQ,EAAE;YACV,OAAO,IAAI,CAAC,2BAA2B,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SAC3D;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;IACvC,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,oBAAoB;IAqBpD;;;OAGG;IACH,YAAY,iBAAsC;QAC9C,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAPrB,2BAAsB,GAA8B,IAAI,CAAC;QA6C1D,cAAS,GAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QArCnD,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC;QACxC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,0BAA0B,GAAG,IAAI,CAAC;IACnE,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QACxD,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAE3F,IAAI,CAAC,oBAAoB,GAAG,IAAI,yCAAyC,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAEtJ,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QAEpH,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,MAAM;QACT,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACzF,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC;IAC1B,CAAC;IAEO,cAAc;QAClB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC5C,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SAClD;IACL,CAAC;IAIM,YAAY;QACf,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,oBAAoB,IAAI,KAAK,CAAC;IAC5F,CAAC;IAEM,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAES,UAAU,CAAC,QAAiB;QAClC,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAC3E,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QAED,sEAAsE;QACtE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAqB,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC;QACxH,IAAI,CAAC,oBAAqB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;;AAnFD;;GAEG;AACoB,mBAAI,GAAG,gBAAgB,CAAC,UAAU,AAA9B,CAA+B;AAC1D;;;;GAIG;AACoB,sBAAO,GAAG,CAAC,AAAJ,CAAK;AA6EvC,qBAAqB;AACrB,oBAAoB,CAAC,eAAe,CAChC,cAAc,CAAC,IAAI,EACnB,CAAC,gBAAgB,EAAE,EAAE;IACjB,OAAO,GAAG,EAAE,CAAC,IAAI,cAAc,CAAC,gBAAgB,CAAC,CAAC;AACtD,CAAC,EACD,cAAc,CAAC,OAAO,EACtB,KAAK,CACR,CAAC", "sourcesContent": ["import type { Engine } from \"../../Engines/engine\";\r\nimport type { WebGLRenderTargetWrapper } from \"../../Engines/WebGL/webGLRenderTargetWrapper\";\r\nimport { WebXRFeatureName, WebXRFeaturesManager } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { IWebXRRenderTargetTextureProvider } from \"../webXRRenderTargetTextureProvider\";\r\nimport type { Viewport } from \"../../Maths/math.viewport\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Matrix } from \"../../Maths/math.vector\";\r\nimport { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { ShaderMaterial } from \"../../Materials/shaderMaterial\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Material } from \"../../Materials/material\";\r\n\r\nimport \"../../Shaders/velocity.fragment\";\r\nimport \"../../Shaders/velocity.vertex\";\r\nimport type { Observer } from \"core/Misc/observable\";\r\n\r\n/**\r\n * Used for Space Warp render process\r\n */\r\nexport class XRSpaceWarpRenderTarget extends RenderTargetTexture {\r\n    private _velocityMaterial: ShaderMaterial;\r\n    private _originalPairing: Array<[AbstractMesh, Nullable<Material>]> = [];\r\n    private _previousWorldMatrices: Array<Matrix> = [];\r\n    private _previousTransforms: Matrix[] = [Matrix.Identity(), Matrix.Identity()];\r\n\r\n    /**\r\n     * Creates a Space Warp render target\r\n     * @param motionVectorTexture WebGLTexture provided by WebGLSubImage\r\n     * @param depthStencilTexture WebGLTexture provided by WebGLSubImage\r\n     * @param scene scene used with the render target\r\n     * @param size the size of the render target (used for each view)\r\n     */\r\n    constructor(motionVectorTexture: WebGLTexture, depthStencilTexture: WebGLTexture, scene?: Scene, size: number | { width: number; height: number } | { ratio: number } = 512) {\r\n        super(\"spacewarp rtt\", size, scene, false, true, Constants.TEXTURETYPE_HALF_FLOAT, false, undefined, false, false, true, undefined, true);\r\n        this._renderTarget = this.getScene()!\r\n            .getEngine()\r\n            .createMultiviewRenderTargetTexture(this.getRenderWidth(), this.getRenderHeight(), motionVectorTexture, depthStencilTexture);\r\n        (this._renderTarget as WebGLRenderTargetWrapper)._disposeOnlyFramebuffers = true;\r\n        this._texture = this._renderTarget.texture!;\r\n        this._texture.isMultiview = true;\r\n        this._texture.format = Constants.TEXTUREFORMAT_RGBA;\r\n\r\n        if (scene) {\r\n            this._velocityMaterial = new ShaderMaterial(\r\n                \"velocity shader material\",\r\n                scene,\r\n                {\r\n                    vertex: \"velocity\",\r\n                    fragment: \"velocity\",\r\n                },\r\n                {\r\n                    uniforms: [\"world\", \"previousWorld\", \"viewProjection\", \"viewProjectionR\", \"previousViewProjection\", \"previousViewProjectionR\"],\r\n                }\r\n            );\r\n            this._velocityMaterial._materialHelperNeedsPreviousMatrices = true;\r\n            this._velocityMaterial.onBindObservable.add((mesh) => {\r\n                // mesh. getWorldMatrix can be incorrect under rare conditions (e.g. when using a effective mesh in the render function).\r\n                // If the case arise that will require changing it we will need to change the bind process in the material class to also provide the world matrix as a parameter\r\n                this._previousWorldMatrices[mesh.uniqueId] = this._previousWorldMatrices[mesh.uniqueId] || mesh.getWorldMatrix();\r\n                this._velocityMaterial.getEffect().setMatrix(\"previousWorld\", this._previousWorldMatrices[mesh.uniqueId]);\r\n                this._previousWorldMatrices[mesh.uniqueId] = mesh.getWorldMatrix();\r\n                // now set the scene's previous matrix\r\n                this._velocityMaterial.getEffect().setMatrix(\"previousViewProjection\", this._previousTransforms[0]);\r\n                // multiview for sure\r\n                this._velocityMaterial.getEffect().setMatrix(\"previousViewProjectionR\", this._previousTransforms[1]);\r\n\r\n                // store the previous (current, to be exact) transforms\r\n                this._previousTransforms[0].copyFrom(scene.getTransformMatrix());\r\n                this._previousTransforms[1].copyFrom(scene._transformMatrixR);\r\n            });\r\n            this._velocityMaterial.freeze();\r\n        }\r\n    }\r\n\r\n    public render(useCameraPostProcess: boolean = false, dumpForDebug: boolean = false): void {\r\n        // Swap to use velocity material\r\n        this._originalPairing.length = 0;\r\n        const scene = this.getScene();\r\n        // set the velocity material to render the velocity RTT\r\n        if (scene && this._velocityMaterial) {\r\n            scene.getActiveMeshes().forEach((mesh) => {\r\n                this._originalPairing.push([mesh, mesh.material]);\r\n                mesh.material = this._velocityMaterial;\r\n            });\r\n        }\r\n\r\n        super.render(useCameraPostProcess, dumpForDebug);\r\n\r\n        // Restore original materials\r\n        this._originalPairing.forEach((tuple) => {\r\n            tuple[0].material = tuple[1];\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindFrameBuffer() {\r\n        if (!this._renderTarget) {\r\n            return;\r\n        }\r\n        this.getScene()!.getEngine().bindSpaceWarpFramebuffer(this._renderTarget);\r\n    }\r\n\r\n    /**\r\n     * Gets the number of views the corresponding to the texture (eg. a SpaceWarpRenderTarget will have > 1)\r\n     * @returns the view count\r\n     */\r\n    public getViewCount() {\r\n        return 2;\r\n    }\r\n\r\n    public dispose(): void {\r\n        super.dispose();\r\n        this._velocityMaterial.dispose();\r\n        this._previousTransforms.length = 0;\r\n        this._previousWorldMatrices.length = 0;\r\n        this._originalPairing.length = 0;\r\n    }\r\n}\r\n\r\n/**\r\n * WebXR Space Warp Render Target Texture Provider\r\n */\r\nexport class WebXRSpaceWarpRenderTargetTextureProvider implements IWebXRRenderTargetTextureProvider {\r\n    protected _lastSubImages = new Map<XRView, XRWebGLSubImage>();\r\n    protected _renderTargetTextures = new Map<XREye, RenderTargetTexture>();\r\n    protected _framebufferDimensions: Nullable<{ framebufferWidth: number; framebufferHeight: number }>;\r\n    protected _engine: Engine;\r\n\r\n    constructor(\r\n        protected readonly _scene: Scene,\r\n        protected readonly _xrSessionManager: WebXRSessionManager,\r\n        protected readonly _xrWebGLBinding: XRWebGLBinding\r\n    ) {\r\n        this._engine = _scene.getEngine();\r\n    }\r\n\r\n    private _getSubImageForView(view: XRView): XRWebGLSubImage {\r\n        const layerWrapper = this._xrSessionManager._getBaseLayerWrapper();\r\n        if (!layerWrapper) {\r\n            throw new Error(\"For Space Warp, the base layer should be a WebXR Projection Layer.\");\r\n        }\r\n        if (layerWrapper.layerType !== \"XRProjectionLayer\") {\r\n            throw new Error('For Space Warp, the base layer type should \"XRProjectionLayer\".');\r\n        }\r\n        const layer = layerWrapper.layer as XRProjectionLayer;\r\n        return this._xrWebGLBinding.getViewSubImage(layer, view);\r\n    }\r\n\r\n    protected _setViewportForSubImage(viewport: Viewport, subImage: XRWebGLSubImage) {\r\n        viewport.x = 0;\r\n        viewport.y = 0;\r\n        viewport.width = subImage.motionVectorTextureWidth!;\r\n        viewport.height = subImage.motionVectorTextureHeight!;\r\n    }\r\n\r\n    protected _createRenderTargetTexture(\r\n        width: number,\r\n        height: number,\r\n        framebuffer: Nullable<WebGLFramebuffer>,\r\n        motionVectorTexture: WebGLTexture,\r\n        depthStencilTexture: WebGLTexture\r\n    ): RenderTargetTexture {\r\n        if (!this._engine) {\r\n            throw new Error(\"Engine is disposed\");\r\n        }\r\n\r\n        const textureSize = { width, height };\r\n\r\n        // Create render target texture from the internal texture\r\n        const renderTargetTexture = new XRSpaceWarpRenderTarget(motionVectorTexture, depthStencilTexture, this._scene, textureSize);\r\n        const renderTargetWrapper = renderTargetTexture.renderTarget as WebGLRenderTargetWrapper;\r\n        if (framebuffer) {\r\n            renderTargetWrapper._framebuffer = framebuffer;\r\n        }\r\n\r\n        // Create internal texture\r\n        renderTargetWrapper._colorTextureArray = motionVectorTexture;\r\n        renderTargetWrapper._depthStencilTextureArray = depthStencilTexture;\r\n\r\n        renderTargetTexture.disableRescaling();\r\n        renderTargetTexture.renderListPredicate = () => true;\r\n\r\n        return renderTargetTexture;\r\n    }\r\n\r\n    protected _getRenderTargetForSubImage(subImage: XRWebGLSubImage, view: XRView) {\r\n        const lastSubImage = this._lastSubImages.get(view);\r\n        let renderTargetTexture = this._renderTargetTextures.get(view.eye);\r\n\r\n        const width = subImage.motionVectorTextureWidth!;\r\n        const height = subImage.motionVectorTextureHeight!;\r\n\r\n        if (!renderTargetTexture || lastSubImage?.textureWidth !== width || lastSubImage?.textureHeight != height) {\r\n            renderTargetTexture = this._createRenderTargetTexture(width, height, null, subImage.motionVectorTexture!, subImage.depthStencilTexture!);\r\n            this._renderTargetTextures.set(view.eye, renderTargetTexture);\r\n\r\n            this._framebufferDimensions = {\r\n                framebufferWidth: width,\r\n                framebufferHeight: height,\r\n            };\r\n        }\r\n\r\n        this._lastSubImages.set(view, subImage);\r\n\r\n        return renderTargetTexture;\r\n    }\r\n\r\n    public trySetViewportForView(viewport: Viewport, view: XRView): boolean {\r\n        const subImage = this._lastSubImages.get(view) || this._getSubImageForView(view);\r\n        if (subImage) {\r\n            this._setViewportForSubImage(viewport, subImage);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Access the motion vector (which will turn on Space Warp)\r\n     * @param view the view to access the motion vector texture for\r\n     */\r\n    public accessMotionVector(view: XRView): void {\r\n        const subImage = this._getSubImageForView(view);\r\n        if (subImage) {\r\n            // Meta Quest Browser uses accessing these textures as a sign for turning on Space Warp\r\n            subImage.motionVectorTexture;\r\n            subImage.depthStencilTexture;\r\n        }\r\n    }\r\n\r\n    public getRenderTargetTextureForEye(_eye: XREye): Nullable<RenderTargetTexture> {\r\n        return null;\r\n    }\r\n\r\n    public getRenderTargetTextureForView(view: XRView): Nullable<RenderTargetTexture> {\r\n        const subImage = this._getSubImageForView(view);\r\n        if (subImage) {\r\n            return this._getRenderTargetForSubImage(subImage, view);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    public dispose() {\r\n        this._renderTargetTextures.forEach((rtt) => rtt.dispose());\r\n        this._renderTargetTextures.clear();\r\n    }\r\n}\r\n\r\n/**\r\n * the WebXR Space Warp feature.\r\n */\r\nexport class WebXRSpaceWarp extends WebXRAbstractFeature {\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.SPACE_WARP;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * The space warp provider\r\n     */\r\n    public spaceWarpRTTProvider: Nullable<WebXRSpaceWarpRenderTargetTextureProvider>;\r\n    private _glContext: WebGLRenderingContext | WebGL2RenderingContext;\r\n    private _xrWebGLBinding: XRWebGLBinding;\r\n    private _renderTargetTexture: Nullable<RenderTargetTexture>;\r\n    private _onAfterRenderObserver: Nullable<Observer<Scene>> = null;\r\n\r\n    /**\r\n     * constructor for the space warp feature\r\n     * @param _xrSessionManager the xr session manager for this feature\r\n     */\r\n    constructor(_xrSessionManager: WebXRSessionManager) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"space-warp\";\r\n        this._xrSessionManager.scene.needsPreviousWorldMatrices = true;\r\n    }\r\n\r\n    /**\r\n     * Attach this feature.\r\n     * Will usually be called by the features manager.\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public attach(): boolean {\r\n        if (!super.attach()) {\r\n            return false;\r\n        }\r\n\r\n        const engine = this._xrSessionManager.scene.getEngine();\r\n        this._glContext = engine._gl;\r\n        this._xrWebGLBinding = new XRWebGLBinding(this._xrSessionManager.session, this._glContext);\r\n\r\n        this.spaceWarpRTTProvider = new WebXRSpaceWarpRenderTargetTextureProvider(this._xrSessionManager.scene, this._xrSessionManager, this._xrWebGLBinding);\r\n\r\n        this._onAfterRenderObserver = this._xrSessionManager.scene.onAfterRenderObservable.add(() => this._onAfterRender());\r\n\r\n        return true;\r\n    }\r\n\r\n    public detach(): boolean {\r\n        this._xrSessionManager.scene.onAfterRenderObservable.remove(this._onAfterRenderObserver);\r\n        return super.detach();\r\n    }\r\n\r\n    private _onAfterRender(): void {\r\n        if (this.attached && this._renderTargetTexture) {\r\n            this._renderTargetTexture.render(false, false);\r\n        }\r\n    }\r\n\r\n    public dependsOn: string[] = [WebXRFeatureName.LAYERS];\r\n\r\n    public isCompatible(): boolean {\r\n        return this._xrSessionManager.scene.getEngine().getCaps().colorBufferHalfFloat || false;\r\n    }\r\n\r\n    public dispose(): void {\r\n        super.dispose();\r\n    }\r\n\r\n    protected _onXRFrame(_xrFrame: XRFrame): void {\r\n        const pose = _xrFrame.getViewerPose(this._xrSessionManager.referenceSpace);\r\n        if (!pose) {\r\n            return;\r\n        }\r\n\r\n        // get the first view to which we will create a texture (or update it)\r\n        const view = pose.views[0];\r\n        this._renderTargetTexture = this._renderTargetTexture || this.spaceWarpRTTProvider!.getRenderTargetTextureForView(view);\r\n        this.spaceWarpRTTProvider!.accessMotionVector(view);\r\n    }\r\n}\r\n\r\n//register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRSpaceWarp.Name,\r\n    (xrSessionManager) => {\r\n        return () => new WebXRSpaceWarp(xrSessionManager);\r\n    },\r\n    WebXRSpaceWarp.Version,\r\n    false\r\n);\r\n"]}