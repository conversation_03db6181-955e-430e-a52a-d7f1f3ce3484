{"version": 3, "file": "physicsRaycastResult.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Physics/physicsRaycastResult.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAahD;;;GAGG;AACH,MAAM,OAAO,oBAAqB,SAAQ,aAAa;IAAvD;;QACY,iBAAY,GAAW,CAAC,CAAC;QACzB,kBAAa,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QACxC,gBAAW,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;IAgElD,CAAC;IA9DG;;OAEG;IACH,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,cAAc;QACd,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,QAAgB;QAClC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,oBAAoB;QACvB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7E,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,OAAgB,OAAO,CAAC,IAAI,EAAE,EAAE,KAAc,OAAO,CAAC,IAAI,EAAE;QACrE,KAAK,CAAC,KAAK,EAAE,CAAC;QACd,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAE9B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;IAC1B,CAAC;CACJ", "sourcesContent": ["import { Vector3 } from \"../Maths/math.vector\";\r\nimport { CastingResult } from \"./castingResult\";\r\n\r\n/**\r\n * Interface for query parameters in the raycast function.\r\n * @see the \"Collision Filtering\" section in https://github.com/eoineoineoin/glTF/tree/MSFT_RigidBodies/extensions/2.0/Vendor/MSFT_collision_primitives\r\n */\r\nexport interface IRaycastQuery {\r\n    /** Membership mask */\r\n    membership?: number;\r\n    /** CollideWith mask */\r\n    collideWith?: number;\r\n}\r\n\r\n/**\r\n * Holds the data for the raycast result\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport class PhysicsRaycastResult extends CastingResult {\r\n    private _hitDistance: number = 0;\r\n    private _rayFromWorld: Vector3 = Vector3.Zero();\r\n    private _rayToWorld: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * Gets the distance from the hit\r\n     */\r\n    get hitDistance(): number {\r\n        return this._hitDistance;\r\n    }\r\n\r\n    /**\r\n     * Gets the hit normal/direction in the world\r\n     */\r\n    get hitNormalWorld(): Vector3 {\r\n        return this._hitNormal;\r\n    }\r\n\r\n    /**\r\n     * Gets the hit point in the world\r\n     */\r\n    get hitPointWorld(): Vector3 {\r\n        return this._hitPoint;\r\n    }\r\n\r\n    /**\r\n     * Gets the ray \"start point\" of the ray in the world\r\n     */\r\n    get rayFromWorld(): Vector3 {\r\n        return this._rayFromWorld;\r\n    }\r\n\r\n    /**\r\n     * Gets the ray \"end point\" of the ray in the world\r\n     */\r\n    get rayToWorld(): Vector3 {\r\n        return this._rayToWorld;\r\n    }\r\n\r\n    /**\r\n     * Sets the distance from the start point to the hit point\r\n     * @param distance defines the distance to set\r\n     */\r\n    public setHitDistance(distance: number) {\r\n        this._hitDistance = distance;\r\n    }\r\n\r\n    /**\r\n     * Calculates the distance manually\r\n     */\r\n    public calculateHitDistance() {\r\n        this._hitDistance = Vector3.Distance(this._rayFromWorld, this._hitPoint);\r\n    }\r\n\r\n    /**\r\n     * Resets all the values to default\r\n     * @param from The from point on world space\r\n     * @param to The to point on world space\r\n     */\r\n    public reset(from: Vector3 = Vector3.Zero(), to: Vector3 = Vector3.Zero()) {\r\n        super.reset();\r\n        this._rayFromWorld.copyFrom(from);\r\n        this._rayToWorld.copyFrom(to);\r\n\r\n        this._hitDistance = 0;\r\n    }\r\n}\r\n"]}