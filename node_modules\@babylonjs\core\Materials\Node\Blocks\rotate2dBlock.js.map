{"version": 3, "file": "rotate2dBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/rotate2dBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAEvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAE7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD;;GAEG;AACH,MAAM,OAAO,aAAc,SAAQ,iBAAiB;IAChD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAE9C,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAC3E,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,KAAK,CAAC,CAAC;QACzE,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;IACjF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa;QAChB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YACzB,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;YAC3C,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;YACrB,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC3C;IACL,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAEzB,KAAK,CAAC,iBAAiB;YACnB,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC;gBAClC,eAAe,KAAK,CAAC,sBAAsB,OAAO,KAAK,CAAC,sBAAsB,YAAY,KAAK,CAAC,sBAAsB,OAAO,KAAK,CAAC,sBAAsB,WAAW,KAAK,CAAC,sBAAsB,OAAO,KAAK,CAAC,sBAAsB,YAAY,KAAK,CAAC,sBAAsB,OAAO,KAAK,CAAC,sBAAsB,QAAQ,CAAC;QAE3T,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport { InputBlock } from \"./Input/inputBlock\";\r\n\r\n/**\r\n * Block used to rotate a 2d vector by a given angle\r\n */\r\nexport class Rotate2dBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new Rotate2dBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerInput(\"input\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerInput(\"angle\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"Rotate2dBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the input vector\r\n     */\r\n    public get input(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the input angle\r\n     */\r\n    public get angle(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public autoConfigure() {\r\n        if (!this.angle.isConnected) {\r\n            const angleInput = new InputBlock(\"angle\");\r\n            angleInput.value = 0;\r\n            angleInput.output.connectTo(this.angle);\r\n        }\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const output = this._outputs[0];\r\n        const angle = this.angle;\r\n        const input = this.input;\r\n\r\n        state.compilationString +=\r\n            this._declareOutput(output, state) +\r\n            ` = vec2(cos(${angle.associatedVariableName}) * ${input.associatedVariableName}.x - sin(${angle.associatedVariableName}) * ${input.associatedVariableName}.y, sin(${angle.associatedVariableName}) * ${input.associatedVariableName}.x + cos(${angle.associatedVariableName}) * ${input.associatedVariableName}.y);\\n`;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.Rotate2dBlock\", Rotate2dBlock);\r\n"]}