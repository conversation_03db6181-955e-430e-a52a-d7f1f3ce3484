import type { DeepImmutable } from "../types";
import type { Matrix } from "../Maths/math.vector";
import { Vector3 } from "../Maths/math.vector";
import { BoundingBox } from "./boundingBox";
import { BoundingSphere } from "./boundingSphere";
import type { Plane } from "../Maths/math.plane";
import type { Collider } from "../Collisions/collider";
/**
 * Interface for cullable objects
 * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction#back-face-culling
 */
export interface ICullable {
    /**
     * Checks if the object or part of the object is in the frustum
     * @param frustumPlanes Camera near/planes
     * @returns true if the object is in frustum otherwise false
     */
    isInFrustum(frustumPlanes: Plane[]): boolean;
    /**
     * Checks if a cullable object (mesh...) is in the camera frustum
     * Unlike isInFrustum this checks the full bounding box
     * @param frustumPlanes Camera near/planes
     * @returns true if the object is in frustum otherwise false
     */
    isCompletelyInFrustum(frustumPlanes: Plane[]): boolean;
}
/**
 * Info for a bounding data of a mesh
 */
export declare class BoundingInfo implements ICullable {
    /**
     * Bounding box for the mesh
     */
    readonly boundingBox: BoundingBox;
    /**
     * Bounding sphere for the mesh
     */
    readonly boundingSphere: BoundingSphere;
    private _isLocked;
    private static readonly _TmpVector3;
    /**
     * Constructs bounding info
     * @param minimum min vector of the bounding box/sphere
     * @param maximum max vector of the bounding box/sphere
     * @param worldMatrix defines the new world matrix
     */
    constructor(minimum: DeepImmutable<Vector3>, maximum: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>);
    /**
     * Recreates the entire bounding info from scratch as if we call the constructor in place
     * @param min defines the new minimum vector (in local space)
     * @param max defines the new maximum vector (in local space)
     * @param worldMatrix defines the new world matrix
     */
    reConstruct(min: DeepImmutable<Vector3>, max: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>): void;
    /**
     * min vector of the bounding box/sphere
     */
    get minimum(): Vector3;
    /**
     * max vector of the bounding box/sphere
     */
    get maximum(): Vector3;
    /**
     * If the info is locked and won't be updated to avoid perf overhead
     */
    get isLocked(): boolean;
    set isLocked(value: boolean);
    /**
     * Updates the bounding sphere and box
     * @param world world matrix to be used to update
     */
    update(world: DeepImmutable<Matrix>): void;
    /**
     * Recreate the bounding info to be centered around a specific point given a specific extend.
     * @param center New center of the bounding info
     * @param extend New extend of the bounding info
     * @returns the current bounding info
     */
    centerOn(center: DeepImmutable<Vector3>, extend: DeepImmutable<Vector3>): BoundingInfo;
    /**
     * Grows the bounding info to include the given point.
     * @param point The point that will be included in the current bounding info (in local space)
     * @returns the current bounding info
     */
    encapsulate(point: Vector3): BoundingInfo;
    /**
     * Grows the bounding info to encapsulate the given bounding info.
     * @param toEncapsulate The bounding info that will be encapsulated in the current bounding info
     * @returns the current bounding info
     */
    encapsulateBoundingInfo(toEncapsulate: BoundingInfo): BoundingInfo;
    /**
     * Scale the current bounding info by applying a scale factor
     * @param factor defines the scale factor to apply
     * @returns the current bounding info
     */
    scale(factor: number): BoundingInfo;
    /**
     * Returns `true` if the bounding info is within the frustum defined by the passed array of planes.
     * @param frustumPlanes defines the frustum to test
     * @param strategy defines the strategy to use for the culling (default is BABYLON.AbstractMesh.CULLINGSTRATEGY_STANDARD)
     * The different strategies available are:
     * * BABYLON.AbstractMesh.CULLINGSTRATEGY_STANDARD most accurate but slower @see https://doc.babylonjs.com/typedoc/classes/BABYLON.AbstractMesh#CULLINGSTRATEGY_STANDARD
     * * BABYLON.AbstractMesh.CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY faster but less accurate @see https://doc.babylonjs.com/typedoc/classes/BABYLON.AbstractMesh#CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY
     * * BABYLON.AbstractMesh.CULLINGSTRATEGY_OPTIMISTIC_INCLUSION can be faster if always visible @see https://doc.babylonjs.com/typedoc/classes/BABYLON.AbstractMesh#CULLINGSTRATEGY_OPTIMISTIC_INCLUSION
     * * BABYLON.AbstractMesh.CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY can be faster if always visible @see https://doc.babylonjs.com/typedoc/classes/BABYLON.AbstractMesh#CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY
     * @returns true if the bounding info is in the frustum planes
     */
    isInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>, strategy?: number): boolean;
    /**
     * Gets the world distance between the min and max points of the bounding box
     */
    get diagonalLength(): number;
    /**
     * Checks if a cullable object (mesh...) is in the camera frustum
     * Unlike isInFrustum this checks the full bounding box
     * @param frustumPlanes Camera near/planes
     * @returns true if the object is in frustum otherwise false
     */
    isCompletelyInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>): boolean;
    /**
     * @internal
     */
    _checkCollision(collider: Collider): boolean;
    /**
     * Checks if a point is inside the bounding box and bounding sphere or the mesh
     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/interactions/mesh_intersect
     * @param point the point to check intersection with
     * @returns if the point intersects
     */
    intersectsPoint(point: DeepImmutable<Vector3>): boolean;
    /**
     * Checks if another bounding info intersects the bounding box and bounding sphere or the mesh
     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/interactions/mesh_intersect
     * @param boundingInfo the bounding info to check intersection with
     * @param precise if the intersection should be done using OBB
     * @returns if the bounding info intersects
     */
    intersects(boundingInfo: DeepImmutable<BoundingInfo>, precise: boolean): boolean;
}
