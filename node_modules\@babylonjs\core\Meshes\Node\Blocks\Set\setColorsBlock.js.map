{"version": 3, "file": "setColorsBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Meshes/Node/Blocks/Set/setColorsBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,qCAAqC,EAAE,MAAM,8CAA8C,CAAC;AAKrG,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAEtG;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,iBAAiB;IAWjD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAZhB;;;WAGG;QAEI,oBAAe,GAAG,IAAI,CAAC;QAS1B,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,qCAAqC,CAAC,QAAQ,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAE/E,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,QAAQ,CAAC,CAAC;QAE9E,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,GAAG,CAAC,CAAC;QAC7F,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,KAAK,CAAC,CAAC;QAC/F,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,CAAC;QACjG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,CAAC;QACjG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,OAAO,CAAC,CAAC;IACrG,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,qBAAqB;QACxB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,qBAAqB;QACxB,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,MAAM,IAAI,GAAG,CAAC,KAA6B,EAAE,EAAE;YAC3C,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAEjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,uBAAuB;aACvE;YAED,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE5C,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;gBAClD,KAAK,CAAC,sBAAsB,EAAE,CAAC;gBAC/B,KAAK,CAAC,uBAAuB,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;gBAChC,OAAO;aACV;YAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;gBAC1B,KAAK,CAAC,sBAAsB,EAAE,CAAC;gBAC/B,KAAK,CAAC,uBAAuB,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC;gBAC5C,OAAO;aACV;YAED,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;gBAC1B,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE,CAAC;aAChC;YAED,aAAa;YACb,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YAC1D,KAAK,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,IAAI,CAAC,aAAa,GAAG,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE;gBACjF,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,KAAK,qCAAqC,CAAC,OAAO,EAAE;oBACpF,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAY,CAAC;oBACpE,IAAI,WAAW,EAAE;wBACb,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;wBACrE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ;wBACjE,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,KAAK,CAAC;qBAC3C;iBACJ;qBAAM;oBACH,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAY,CAAC;oBACpE,IAAI,WAAW,EAAE;wBACb,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;wBACrE,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,IAAI,CAAC;qBAC1C;iBACJ;aACJ;YAED,UAAU;YACV,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC/B,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,WAAW,CAAC;QAC5B,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;SACtC;aAAM;YACH,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1C;IACL,CAAC;IAES,mBAAmB;QACzB,MAAM,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,GAAG,GAAG,IAAI,CAAC,iBAAiB,sBAAsB,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC;QAC7I,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAE3D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB;QACxC,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAExC,IAAI,mBAAmB,CAAC,eAAe,KAAK,SAAS,EAAE;YACnD,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC,eAAe,CAAC;SAC9D;IACL,CAAC;CACJ;AAjKU;IADN,sBAAsB,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC;uDAC3F;AAmKlC,aAAa,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "sourcesContent": ["import { NodeGeometry<PERSON>lock } from \"../../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../../nodeGeometryBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"../../Enums/nodeGeometryConnectionPointTypes\";\r\nimport type { NodeGeometryBuildState } from \"../../nodeGeometryBuildState\";\r\nimport type { INodeGeometryExecutionContext } from \"../../Interfaces/nodeGeometryExecutionContext\";\r\nimport type { VertexData } from \"../../../mesh.vertexData\";\r\nimport type { Vector3, Vector4 } from \"../../../../Maths/math.vector\";\r\nimport { PropertyTypeForEdition, editableInPropertyPage } from \"../../../../Decorators/nodeDecorator\";\r\n\r\n/**\r\n * Block used to set colors for a geometry\r\n */\r\nexport class SetColorsBlock extends NodeGeometryBlock implements INodeGeometryExecutionContext {\r\n    private _vertexData: VertexData;\r\n    private _currentIndex: number;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that this block can evaluate context\r\n     * Build performance is improved when this value is set to false as the system will cache values instead of reevaluating everything per context change\r\n     */\r\n    @editableInPropertyPage(\"Evaluate context\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { rebuild: true } })\r\n    public evaluateContext = true;\r\n\r\n    /**\r\n     * Create a new SetColorsBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"geometry\", NodeGeometryBlockConnectionPointTypes.Geometry);\r\n        this.registerInput(\"colors\", NodeGeometryBlockConnectionPointTypes.AutoDetect);\r\n\r\n        this.registerOutput(\"output\", NodeGeometryBlockConnectionPointTypes.Geometry);\r\n\r\n        this._inputs[1].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Int);\r\n        this._inputs[1].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Float);\r\n        this._inputs[1].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Vector2);\r\n        this._inputs[1].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Texture);\r\n        this._inputs[1].excludedConnectionPointTypes.push(NodeGeometryBlockConnectionPointTypes.Texture);\r\n    }\r\n\r\n    /**\r\n     * Gets the current index in the current flow\r\n     * @returns the current index\r\n     */\r\n    public getExecutionIndex(): number {\r\n        return this._currentIndex;\r\n    }\r\n\r\n    /**\r\n     * Gets the current loop index in the current flow\r\n     * @returns the current loop index\r\n     */\r\n    public getExecutionLoopIndex(): number {\r\n        return this._currentIndex;\r\n    }\r\n\r\n    /**\r\n     * Gets the current face index in the current flow\r\n     * @returns the current face index\r\n     */\r\n    public getExecutionFaceIndex(): number {\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"SetColorsBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the geometry input component\r\n     */\r\n    public get geometry(): NodeGeometryConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the colors input component\r\n     */\r\n    public get colors(): NodeGeometryConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the geometry output component\r\n     */\r\n    public get output(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeGeometryBuildState) {\r\n        const func = (state: NodeGeometryBuildState) => {\r\n            state.pushExecutionContext(this);\r\n\r\n            this._vertexData = this.geometry.getConnectedValue(state);\r\n\r\n            if (this._vertexData) {\r\n                this._vertexData = this._vertexData.clone(); // Preserve source data\r\n            }\r\n\r\n            state.pushGeometryContext(this._vertexData);\r\n\r\n            if (!this._vertexData || !this._vertexData.positions) {\r\n                state.restoreGeometryContext();\r\n                state.restoreExecutionContext();\r\n                this.output._storedValue = null;\r\n                return;\r\n            }\r\n\r\n            if (!this.colors.isConnected) {\r\n                state.restoreGeometryContext();\r\n                state.restoreExecutionContext();\r\n                this.output._storedValue = this._vertexData;\r\n                return;\r\n            }\r\n\r\n            if (!this._vertexData.colors) {\r\n                this._vertexData.colors = [];\r\n            }\r\n\r\n            // Processing\r\n            const vertexCount = this._vertexData.positions.length / 3;\r\n            for (this._currentIndex = 0; this._currentIndex < vertexCount; this._currentIndex++) {\r\n                if (this.colors.connectedPoint?.type === NodeGeometryBlockConnectionPointTypes.Vector3) {\r\n                    const tempVector3 = this.colors.getConnectedValue(state) as Vector3;\r\n                    if (tempVector3) {\r\n                        tempVector3.toArray(this._vertexData.colors, this._currentIndex * 4);\r\n                        this._vertexData.colors[this._currentIndex * 4 + 3] = 1; // Alpha\r\n                        this._vertexData.hasVertexAlpha = false;\r\n                    }\r\n                } else {\r\n                    const tempVector4 = this.colors.getConnectedValue(state) as Vector4;\r\n                    if (tempVector4) {\r\n                        tempVector4.toArray(this._vertexData.colors, this._currentIndex * 4);\r\n                        this._vertexData.hasVertexAlpha = true;\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Storage\r\n            state.restoreGeometryContext();\r\n            state.restoreExecutionContext();\r\n            return this._vertexData;\r\n        };\r\n\r\n        if (this.evaluateContext) {\r\n            this.output._storedFunction = func;\r\n        } else {\r\n            this.output._storedFunction = null;\r\n            this.output._storedValue = func(state);\r\n        }\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        const codeString = super._dumpPropertiesCode() + `${this._codeVariableName}.evaluateContext = ${this.evaluateContext ? \"true\" : \"false\"};\\n`;\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Serializes this block in a JSON representation\r\n     * @returns the serialized block object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.evaluateContext = this.evaluateContext;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any) {\r\n        super._deserialize(serializationObject);\r\n\r\n        if (serializationObject.evaluateContext !== undefined) {\r\n            this.evaluateContext = serializationObject.evaluateContext;\r\n        }\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SetColorsBlock\", SetColorsBlock);\r\n"]}