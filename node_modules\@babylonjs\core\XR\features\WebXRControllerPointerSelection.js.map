{"version": 3, "file": "WebXRControllerPointerSelection.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRControllerPointerSelection.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AASjF,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,IAAI,EAAE,MAAM,uBAAuB,CAAC;AAC7C,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AACpE,OAAO,EAAE,cAAc,EAAE,MAAM,uCAAuC,CAAC;AACvE,OAAO,EAAE,WAAW,EAAE,MAAM,oCAAoC,CAAC;AACjE,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;AACxC,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAC3D,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,oBAAoB,EAAE,MAAM,sCAAsC,CAAC;AAI5E,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAErD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AA+GzC;;GAEG;AACH,MAAM,OAAO,+BAAgC,SAAQ,oBAAoB;IA4HrE;;;;OAIG;IACH,YACI,iBAAsC,EACrB,QAAiD;QAElE,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFR,aAAQ,GAAR,QAAQ,CAAyC;QAhI9D,sBAAiB,GAAG,CAAC,YAA8B,EAAE,EAAE;YAC3D,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;gBAC1C,mBAAmB;gBACnB,OAAO;aACV;YAED,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAEtK,qBAAqB;YACrB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG;gBACvC,YAAY;gBACZ,YAAY;gBACZ,aAAa;gBACb,gBAAgB,EAAE,IAAI;gBACtB,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,IAAI,GAAG,CAAC,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,CAAC;gBAC7C,yBAAyB,EAAE,KAAK;gBAChC,EAAE,EAAE,+BAA+B,CAAC,UAAU,EAAE;aACnD,CAAC;YAEF,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IACI,CAAC,IAAI,CAAC,QAAQ,CAAC,sCAAsC;oBACrD,IAAI,CAAC,QAAQ,CAAC,mBAAmB;oBACjC,YAAY,CAAC,WAAW,CAAC,UAAU,KAAK,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAC3E;oBACE,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC,QAAQ,CAAC;iBACpD;aACJ;iBAAM;gBACH,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,sCAAsC,EAAE;oBACvD,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC,QAAQ,CAAC;iBACpD;aACJ;YAED,QAAQ,YAAY,CAAC,WAAW,CAAC,aAAa,EAAE;gBAC5C,KAAK,iBAAiB;oBAClB,OAAO,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAC;gBAC3D,KAAK,MAAM;oBACP,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;gBAC9C,KAAK,QAAQ,CAAC;gBACd,KAAK,mBAAmB;oBACpB,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;aACtD;QACL,CAAC,CAAC;QAEM,iBAAY,GAoBhB,EAAE,CAAC;QAEC,6BAAwB,GAAG,IAAI,OAAO,EAAE,CAAC;QAejD;;WAEG;QACI,2BAAsB,GAAY,IAAI,CAAC;QAC9C;;WAEG;QACI,iCAA4B,GAAY,IAAI,CAAC;QACpD;;WAEG;QACI,wBAAmB,GAAY,IAAI,CAAC;QAC3C;;WAEG;QACI,yBAAoB,GAAY,IAAI,CAAC;QAC5C;;WAEG;QACI,4BAAuB,GAAW,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACnE;;WAEG;QACI,6BAAwB,GAAW,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACpE;;WAEG;QACI,8BAAyB,GAAW,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACrE;;WAEG;QACI,6BAAwB,GAAW,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QA2J5D,oBAAe,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QACpC,0BAAqB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACvC,iBAAY,GAAG,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QA3I5C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;QAE3C,8EAA8E;QAC9E,qEAAqE;QACrE,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,EAAE;YAC1H,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;SACxC;QAED,yCAAyC;QACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE;YAC/B,IAAI,CAAC,QAAQ,CAAC,sCAAsC,GAAG,IAAI,CAAC;YAC5D,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;SACpC;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,2BAA2B,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAC5G,IAAI,CAAC,qBAAqB,CACtB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,6BAA6B,EACnD,CAAC,UAAU,EAAE,EAAE;YACX,wBAAwB;YACxB,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC,EACD,IAAI,CACP,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,gCAAgC,GAAG,IAAI,CAAC;QAEpD,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;YAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YAE7C,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAE/E,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG;gBAC1B,WAAW;gBACX,YAAY;gBACZ,aAAa;gBACb,gBAAgB,EAAE,IAAI;gBACtB,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,IAAI,GAAG,CAAC,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,CAAC;gBAC7C,yBAAyB,EAAE,KAAK;gBAChC,EAAE,EAAE,+BAA+B,CAAC,UAAU,EAAE;aACnD,CAAC;YACF,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YACpD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,YAAoB;QAC3C,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE;YACjC,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,gBAAgB,CAAC;SAC3D;aAAM;YACH,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAED;;;;;OAKG;IACI,0BAA0B,CAAC,EAAU;QACxC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAClC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,IAAI,IAAI,CAAC;aAC1D;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,uCAAuC,CAAC,EAAU;QACrD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAClC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,yBAAyB,CAAC;aAC/D;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,uCAAuC,CAAC,EAAU,EAAE,KAAc;QACrE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAClC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBACtC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,yBAAyB,GAAG,KAAK,CAAC;gBAC7D,OAAO;aACV;SACJ;IACL,CAAC;IAMS,UAAU,CAAC,QAAiB;QAClC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YAC1C,qBAAqB;YACrB,wCAAwC;YACxC,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAC7C,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,cAAc,CAAC,YAAY,EAAE,WAAW,CAAC,aAAa,KAAK,mBAAmB,EAAE;gBACjH,OAAO;aACV;YACD,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,sCAAsC,IAAI,EAAE,KAAK,IAAI,CAAC,mBAAmB,CAAC,IAAI,cAAc,CAAC,yBAAyB,EAAE;gBACxI,cAAc,CAAC,aAAa,CAAC,SAAS,GAAG,KAAK,CAAC;gBAC/C,cAAc,CAAC,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC;gBAC9C,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC;gBAC3B,OAAO;aACV;YAED,cAAc,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAEjE,IAAI,wBAAiC,CAAC;YAEtC,qCAAqC;YACrC,IAAI,cAAc,CAAC,YAAY,EAAE;gBAC7B,wBAAwB;oBACpB,IAAI,CAAC,QAAQ,CAAC,oBAAoB,IAAI,cAAc,CAAC,YAAY,CAAC,IAAI;wBAClE,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ;wBAC3C,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACvD,cAAc,CAAC,YAAY,CAAC,uBAAuB,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;aAClH;iBAAM,IAAI,cAAc,CAAC,WAAW,EAAE;gBACnC,wBAAwB,GAAG,cAAc,CAAC,WAAW,CAAC,QAAQ,CAAC;gBAC/D,cAAc,CAAC,WAAW,CAAC,kBAAkB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;aACxE;iBAAM;gBACH,OAAO;aACV;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE;gBAClC,cAAc,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;aACnE;YACD,8EAA8E;YAC9E,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,+BAA+B,IAAI,wBAAwB,EAAE;gBAC5E,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;gBAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAC9C,IAAI,MAAM,EAAE;oBACR,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,cAAc,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;oBACrJ,OAAO,CAAC,YAAY,CAAC,wBAAwB,EAAE,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,uBAAuB,EAAE,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;oBACtJ,YAAY;oBACZ,IACI,OAAO,IAAI,CAAC,qBAAqB,CAAC,CAAC,KAAK,QAAQ;wBAChD,OAAO,IAAI,CAAC,qBAAqB,CAAC,CAAC,KAAK,QAAQ;wBAChD,CAAC,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;wBACpC,CAAC,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;wBACpC,IAAI,CAAC,qBAAqB,CAAC,CAAC,KAAK,QAAQ;wBACzC,IAAI,CAAC,qBAAqB,CAAC,CAAC,KAAK,QAAQ,EAC3C;wBACE,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;wBAC9C,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;wBAE9C,cAAc,CAAC,iBAAiB,GAAG;4BAC/B,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;4BAC/B,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;yBAClC,CAAC;qBACL;iBACJ;aACJ;YAED,IAAI,gBAAgB,GAAG,IAAI,CAAC;YAC5B,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC;aAC7J;YAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACzI,IAAI,CAAC,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;gBAC5C,0BAA0B;gBAC1B,cAAc,CAAC,IAAI,GAAG,iBAAiB,CAAC;aAC3C;iBAAM,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE;gBACrD,2BAA2B;gBAC3B,cAAc,CAAC,IAAI,GAAG,gBAAgB,CAAC;aAC1C;iBAAM,IAAI,gBAAgB,CAAC,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,EAAE;gBAC/D,iCAAiC;gBACjC,cAAc,CAAC,IAAI,GAAG,gBAAgB,CAAC;aAC1C;iBAAM;gBACH,kCAAkC;gBAClC,cAAc,CAAC,IAAI,GAAG,iBAAiB,CAAC;aAC3C;YAED,IAAI,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,YAAY,EAAE;gBACpD,cAAc,CAAC,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC;gBACvE,cAAc,CAAC,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC,YAAY,CAAC,IAAI,IAAI,IAAI,CAAC;gBAC7E,cAAc,CAAC,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC;aACxE;YAED,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC;YAEjC,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,GAAG,EAAE;gBACtC,qBAAqB;gBACrB,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAExE,sBAAsB;gBACtB,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACjE,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClE,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClE,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAElE,sBAAsB;gBACtB,MAAM,UAAU,GAAG,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;gBACpG,MAAM,aAAa,GAAG,KAAK,CAAC;gBAC5B,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACjE,IAAI,UAAU,EAAE;oBACZ,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;oBAChD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;oBAC/C,OAAO,CAAC,qBAAqB,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;oBAC/F,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;iBACrF;gBACD,cAAc,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC;gBAC3E,cAAc,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC;aACrD;iBAAM;gBACH,cAAc,CAAC,aAAa,CAAC,SAAS,GAAG,KAAK,CAAC;gBAC/C,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;gBAC5D,cAAc,CAAC,gBAAgB,GAAG,IAAI,CAAC;aAC1C;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,IAAY,kBAAkB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,oBAAoB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;IAC/G,CAAC;IAEO,eAAe,CAAC,YAA+B;QACnD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,YAAY,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC;QAC9F,8CAA8C;QAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,IAAI,CAAC;QACxD,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QAC9F,IAAI,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,WAAW,CACxB,WAAW,EACX;YACI,QAAQ,EAAE,MAAM,GAAG,EAAE;YACrB,SAAS,EAAE,MAAM,GAAG,CAAC;YACrB,YAAY,EAAE,EAAE;SACnB,EACD,eAAe,CAClB,CAAC;QACF,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;QAC3B,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC;QAC5B,QAAQ,CAAC,MAAM,GAAG,cAAc,CAAC,aAAa,CAAC;QAC/C,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,MAAM,gBAAgB,GAAqB;YACvC,SAAS,EAAE,cAAc,CAAC,EAAE;YAC5B,WAAW,EAAE,IAAI;SACpB,CAAC;QACF,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YACjF,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;gBACtB,OAAO;aACV;YACD,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,iBAAiB,CAAC,CAAC;YAChG,cAAc,CAAC,YAAY,CAAC,QAAS,CAAC,KAAK,GAAG,CAAC,CAAC;YAChD,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;YAC3B,IAAI,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE;gBACzB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,cAAc,CAAC,IAAI,CAAC,EAAE;oBACnD,IAAI,KAAK,GAAG,YAAY,GAAG,EAAE,EAAE;wBAC3B,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;qBAC7B;oBAED,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,CAAC;oBAChD,IAAI,KAAK,IAAI,YAAY,EAAE;wBACvB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;wBACvE,yIAAyI;wBACzI,aAAa,GAAG,IAAI,CAAC;wBACrB,uDAAuD;wBACvD,IAAI,IAAI,CAAC,QAAQ,CAAC,0BAA0B,EAAE;4BAC1C,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;yBACxE;wBACD,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;qBAC9B;yBAAM;wBACH,MAAM,WAAW,GAAG,CAAC,GAAG,KAAK,GAAG,YAAY,CAAC;wBAC7C,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;qBAC/D;iBACJ;qBAAM;oBACH,IAAI,aAAa,EAAE;wBACf,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,0BAA0B,EAAE;4BAC3C,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;yBACxE;qBACJ;oBACD,aAAa,GAAG,KAAK,CAAC;oBACtB,KAAK,GAAG,CAAC,CAAC;iBACb;aACJ;iBAAM;gBACH,aAAa,GAAG,KAAK,CAAC;gBACtB,KAAK,GAAG,CAAC,CAAC;aACb;YAED,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;YAEvE,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,KAAK,SAAS,EAAE;YAC9C,QAAQ,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;SAC9D;QACD,IAAI,YAAY,EAAE;YACd,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;gBAC1C,IAAI,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,0BAA0B,IAAI,aAAa,EAAE;oBACnF,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;oBACrE,cAAc,CAAC,uBAAuB,GAAG,IAAI,CAAC;iBACjD;gBACD,QAAQ,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAEO,oBAAoB,CAAC,YAA8B;QACvD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAChE,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,MAAM,gBAAgB,GAAqB;YACvC,SAAS,EAAE,cAAc,CAAC,EAAE;YAC5B,WAAW,EAAE,IAAI;SACpB,CAAC;QACF,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YACjF,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,iBAAiB,CAAC,CAAC;YAChG,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,0BAA0B,IAAI,aAAa,CAAC,EAAE;gBACrF,OAAO;aACV;YACD,IAAI,CAAC,aAAa,EAAE;gBAChB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;gBACvE,cAAc,CAAC,oBAAoB,GAAG,IAAI,CAAC;gBAC3C,aAAa,GAAG,IAAI,CAAC;gBACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,0BAA0B,EAAE;oBAC1C,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;iBACxE;aACJ;iBAAM;gBACH,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;aAC1E;QACL,CAAC,CAAC,CAAC;QACH,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;YAC1C,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,iBAAiB,CAAC,CAAC;YAChG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,GAAG,EAAE;gBACrC,IAAI,cAAc,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,uBAAuB,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,0BAA0B,EAAE;oBAC9H,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;oBACrE,cAAc,CAAC,uBAAuB,GAAG,IAAI,CAAC;iBACjD;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,4BAA4B,CAAC,YAA8B;QAC/D,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAChE,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;YAC7B,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;SAC7C;QACD,MAAM,gBAAgB,GAAqB;YACvC,SAAS,EAAE,cAAc,CAAC,EAAE;YAC5B,WAAW,EAAE,IAAI;SACpB,CAAC;QACF,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC9D,cAAc,CAAC,YAAY,CAAC,QAAS,CAAC,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC;YACpF,cAAc,CAAC,aAAa,CAAC,QAAS,CAAC,eAAe,GAAG,IAAI,CAAC,4BAA4B,CAAC;YAE9G,IAAI,cAAc,CAAC,IAAI,EAAE;gBACrB,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,iBAAiB,CAAC,CAAC;gBAChG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;aAC1E;QACL,CAAC,CAAC,CAAC;QACH,IAAI,YAAY,CAAC,WAAW,CAAC,OAAO,EAAE;YAClC,MAAM,IAAI,GAAG,CAAC,gBAA+C,EAAE,EAAE;gBAC7D,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE;oBAChC,cAAc,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;iBACrG;gBACD,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE;oBACpC,cAAc,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;iBAC3E;gBAED,cAAc,CAAC,uBAAuB,GAAG,cAAc,CAAC,kBAAkB,CAAC,8BAA8B,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;oBACxH,IAAI,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE;wBAC3B,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;wBAClD,IAAI,cAAc,CAAC,IAAI,EAAE;4BACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,sCAAsC,IAAI,YAAY,CAAC,QAAQ,KAAK,IAAI,CAAC,mBAAmB,EAAE;gCAC5G,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,iBAAiB,CAAC,CAAC;gCAChG,IAAI,OAAO,EAAE;oCACT,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;oCACvE,cAAc,CAAC,oBAAoB,GAAG,IAAI,CAAC;oCACxB,cAAc,CAAC,aAAa,CAAC,QAAS,CAAC,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC;oCACrF,cAAc,CAAC,YAAY,CAAC,QAAS,CAAC,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC;iCACzG;qCAAM;oCACH,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;oCAClD,cAAc,CAAC,aAAa,CAAC,QAAS,CAAC,aAAa,GAAG,IAAI,CAAC,yBAAyB,CAAC;oCACtF,cAAc,CAAC,YAAY,CAAC,QAAS,CAAC,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC;iCAC1G;6BACJ;yBACJ;6BAAM;4BACH,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,sCAAsC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE;gCACzG,8CAA8C;gCAC9C,8CAA8C;gCAC9C,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gCACnE,IAAI,cAAc,IAAI,cAAc,CAAC,oBAAoB,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE;oCAClG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,iBAAiB,CAAC,CAAC;oCAChG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,WAAW,EAAE,EAAE;wCAC7C,SAAS,EAAE,cAAc,CAAC,EAAE;wCAC5B,WAAW,EAAE,IAAI;qCACpB,CAAC,CAAC;oCACH,cAAc,CAAC,uBAAuB,GAAG,IAAI,CAAC;iCACjD;gCACD,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC,QAAQ,CAAC;6BACpD;yBACJ;qBACJ;gBACL,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;YACF,IAAI,YAAY,CAAC,gBAAgB,EAAE;gBAC/B,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;aACvC;iBAAM;gBACH,YAAY,CAAC,gCAAgC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aAC3D;SACJ;aAAM;YACH,oCAAoC;YACpC,MAAM,mBAAmB,GAAG,CAAC,KAAyB,EAAE,EAAE;gBACtD,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;oBACpD,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,iBAAiB,CAAC,CAAC;oBAChG,IAAI,cAAc,CAAC,YAAY,IAAI,KAAK,CAAC,WAAW,KAAK,cAAc,CAAC,YAAY,CAAC,WAAW,IAAI,cAAc,CAAC,IAAI,EAAE;wBACrH,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;wBACvE,cAAc,CAAC,oBAAoB,GAAG,IAAI,CAAC;wBACxB,cAAc,CAAC,aAAa,CAAC,QAAS,CAAC,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC;wBACrF,cAAc,CAAC,YAAY,CAAC,QAAS,CAAC,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC;qBACzG;gBACL,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;YAEF,MAAM,iBAAiB,GAAG,CAAC,KAAyB,EAAE,EAAE;gBACpD,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;oBACpD,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,iBAAiB,CAAC,CAAC;oBAChG,IAAI,cAAc,CAAC,YAAY,IAAI,KAAK,CAAC,WAAW,KAAK,cAAc,CAAC,YAAY,CAAC,WAAW,IAAI,cAAc,CAAC,IAAI,EAAE;wBACrH,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;wBAClD,cAAc,CAAC,aAAa,CAAC,QAAS,CAAC,aAAa,GAAG,IAAI,CAAC,yBAAyB,CAAC;wBACtF,cAAc,CAAC,YAAY,CAAC,QAAS,CAAC,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC;qBAC1G;gBACL,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;YAEF,cAAc,CAAC,cAAc,GAAG;gBAC5B,SAAS,EAAE,iBAAiB;gBAC5B,WAAW,EAAE,mBAAmB;aACnC,CAAC;YAEF,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,gBAAgB,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;YACpF,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;SACnF;IACL,CAAC;IAEO,8BAA8B,CAAC,MAAyB,EAAE,GAAQ;QACtE,IAAI,MAAM,EAAE;YACR,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;YAC5D,IAAI,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE;gBACrB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3B;SACJ;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,iBAAiB,CAAC,oBAA4B;QAClD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QAC/D,IAAI,CAAC,cAAc,EAAE;YACjB,OAAO;SACV;QACD,IAAI,cAAc,CAAC,kBAAkB,EAAE;YACnC,IAAI,cAAc,CAAC,uBAAuB,EAAE;gBACxC,cAAc,CAAC,kBAAkB,CAAC,8BAA8B,CAAC,MAAM,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;aACnH;SACJ;QACD,IAAI,cAAc,CAAC,eAAe,EAAE;YAChC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;SACrF;QACD,IAAI,cAAc,CAAC,cAAc,EAAE;YAC/B,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,SAAiB,EAAE,EAAE;gBACrE,MAAM,IAAI,GAAG,cAAc,CAAC,cAAc,IAAI,cAAc,CAAC,cAAc,CAAC,SAAwB,CAAC,CAAC;gBACtG,IAAI,IAAI,EAAE;oBACN,gEAAgE;oBAChE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,mBAAmB,CAAC,SAAwB,EAAE,IAAW,CAAC,CAAC;iBAC7F;YACL,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,cAAc,CAAC,uBAAuB,IAAI,cAAc,CAAC,oBAAoB,EAAE;YAChF,sEAAsE;YACtE,MAAM,gBAAgB,GAAqB;gBACvC,SAAS,EAAE,cAAc,CAAC,EAAE;gBAC5B,WAAW,EAAE,IAAI;aACpB,CAAC;YACF,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,GAAG,EAAE;gBACrC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,iBAAiB,CAAC,CAAC;gBAChG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,IAAI,IAAI,WAAW,EAAE,EAAE,gBAAgB,CAAC,CAAC;gBAC1F,cAAc,CAAC,uBAAuB,GAAG,IAAI,CAAC;YAClD,CAAC,CAAC,CAAC;SACN;QACD,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,wBAAwB,CAAC,OAAO,CAAC,GAAG,EAAE;YAC/D,IAAI;gBACA,cAAc,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;gBACvC,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBACtC,sBAAsB;gBACtB,OAAO,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;gBAC/C,IAAI,IAAI,CAAC,mBAAmB,KAAK,oBAAoB,EAAE;oBACnD,8BAA8B;oBAC9B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAC5C,IAAI,IAAI,CAAC,MAAM,EAAE;wBACb,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;qBACtC;yBAAM;wBACH,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;qBACjC;iBACJ;aACJ;YAAC,OAAO,CAAC,EAAE;gBACR,KAAK,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;aAC9C;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,oBAAoB,CAAC,UAAgB;QACzC,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,oBAAoB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QAC1K,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,gCAAgC;YAC/D,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,gCAAgC,EAAE;YAClD,CAAC,CAAC,cAAc,CACV,cAAc,EACd;gBACI,MAAM,EAAE,CAAC;gBACT,WAAW,EAAE,MAAM;gBACnB,cAAc,EAAE,KAAK;gBACrB,YAAY,EAAE,EAAE;gBAChB,YAAY,EAAE,CAAC;aAClB,EACD,eAAe,CAClB,CAAC;QACR,YAAY,CAAC,MAAM,GAAG,UAAU,CAAC;QACjC,MAAM,oBAAoB,GAAG,IAAI,gBAAgB,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;QACtF,oBAAoB,CAAC,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC;QACnE,oBAAoB,CAAC,KAAK,GAAG,GAAG,CAAC;QACjC,YAAY,CAAC,QAAQ,GAAG,oBAAoB,CAAC;QAC7C,YAAY,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACtC,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAC7C,YAAY,CAAC,UAAU,GAAG,KAAK,CAAC;QAChC,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC;QAE/B,+CAA+C;QAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,4BAA4B;YAC5D,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,4BAA4B,EAAE;YAC9C,CAAC,CAAC,WAAW,CACP,aAAa,EACb;gBACI,QAAQ,EAAE,MAAM,GAAG,CAAC;gBACpB,SAAS,EAAE,MAAM,GAAG,CAAC;gBACrB,YAAY,EAAE,EAAE;aACnB,EACD,eAAe,CAClB,CAAC;QACR,aAAa,CAAC,gCAAgC,EAAE,CAAC;QACjD,aAAa,CAAC,UAAU,GAAG,KAAK,CAAC;QACjC,aAAa,CAAC,SAAS,GAAG,KAAK,CAAC;QAChC,MAAM,SAAS,GAAG,IAAI,gBAAgB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QACrE,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QACzC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,yBAAyB,CAAC;QACzD,SAAS,CAAC,eAAe,GAAG,KAAK,CAAC;QAClC,aAAa,CAAC,QAAQ,GAAG,SAAS,CAAC;QAEnC,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,KAAK,SAAS,EAAE;YAC9C,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;YAC/D,aAAa,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;SACnE;QAED,OAAO;YACH,YAAY;YACZ,aAAa;SAChB,CAAC;IACN,CAAC;IAEO,aAAa,CAAC,OAAoB,EAAE,OAAoB;QAC5D,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YAC9B,OAAO,IAAI,CAAC;SACf;QACD,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC5F,OAAO,IAAI,CAAC;SACf;QACD,IAAI,OAAO,CAAC,UAAU,KAAK,OAAO,CAAC,UAAU,EAAE;YAC3C,OAAO,IAAI,CAAC;SACf;QACD,OAAO,CAAC,WAAW,EAAE,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACvF,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;QACnK,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,0BAA0B,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC;QACxF,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,CAAC;QACtD,IAAI,MAAM,GAAG,KAAK,EAAE;YAChB,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,sBAAsB,CAAC,aAA2B,EAAE,WAAmB,GAAG;QAC9E,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,QAAQ,CAAC;QACnC,wCAAwC;QACxC,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;YAClC,QAAQ,IAAI,CAAC,CAAC,CAAC;SAClB;QACD,aAAa,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;IACnD,CAAC;IAEO,mBAAmB,CAAC,gBAAkC,EAAE,EAAU,EAAE,iBAA4C;QACpH,gBAAgB,CAAC,SAAS,GAAG,EAAE,CAAC;QAChC,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC;QACpC,IAAI,iBAAiB,EAAE;YACnB,gBAAgB,CAAC,OAAO,GAAG,iBAAiB,CAAC,CAAC,CAAC;YAC/C,gBAAgB,CAAC,OAAO,GAAG,iBAAiB,CAAC,CAAC,CAAC;SAClD;IACL,CAAC;IAED,gBAAgB;IAChB,IAAW,yBAAyB;QAChC,qBAAqB;QACrB,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;;AAjxBc,0CAAU,GAAG,GAAG,AAAN,CAAO;AAyEhC;;GAEG;AACoB,oCAAI,GAAG,gBAAgB,CAAC,iBAAiB,AAArC,CAAsC;AACjE;;;;GAIG;AACoB,uCAAO,GAAG,CAAC,AAAJ,CAAK;AAksBvC,qBAAqB;AACrB,oBAAoB,CAAC,eAAe,CAChC,+BAA+B,CAAC,IAAI,EACpC,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,+BAA+B,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AAChF,CAAC,EACD,+BAA+B,CAAC,OAAO,EACvC,IAAI,CACP,CAAC", "sourcesContent": ["import { WebXRFeaturesManager, WebXRFeatureName } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { WebXRInput } from \"../webXRInput\";\r\nimport type { WebXRInputSource } from \"../webXRInputSource\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { WebXRControllerComponent } from \"../motionController/webXRControllerComponent\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Matrix, Vector3 } from \"../../Maths/math.vector\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport { Axis } from \"../../Maths/math.axis\";\r\nimport { StandardMaterial } from \"../../Materials/standardMaterial\";\r\nimport { CreateCylinder } from \"../../Meshes/Builders/cylinderBuilder\";\r\nimport { CreateTorus } from \"../../Meshes/Builders/torusBuilder\";\r\nimport { Ray } from \"../../Culling/ray\";\r\nimport { PickingInfo } from \"../../Collisions/pickingInfo\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport { UtilityLayerRenderer } from \"../../Rendering/utilityLayerRenderer\";\r\nimport type { WebXRAbstractMotionController } from \"../motionController/webXRAbstractMotionController\";\r\nimport type { WebXRCamera } from \"../webXRCamera\";\r\nimport type { Node } from \"../../node\";\r\nimport { Viewport } from \"../../Maths/math.viewport\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n\r\n/**\r\n * Options interface for the pointer selection module\r\n */\r\nexport interface IWebXRControllerPointerSelectionOptions {\r\n    /**\r\n     * if provided, this scene will be used to render meshes.\r\n     */\r\n    customUtilityLayerScene?: Scene;\r\n    /**\r\n     * Disable the pointer up event when the xr controller in screen and gaze mode is disposed (meaning - when the user removed the finger from the screen)\r\n     * If not disabled, the last picked point will be used to execute a pointer up event\r\n     * If disabled, pointer up event will be triggered right after the pointer down event.\r\n     * Used in screen and gaze target ray mode only\r\n     */\r\n    disablePointerUpOnTouchOut: boolean;\r\n    /**\r\n     * For gaze mode for tracked-pointer / controllers (time to select instead of button press)\r\n     */\r\n    forceGazeMode: boolean;\r\n    /**\r\n     * Factor to be applied to the pointer-moved function in the gaze mode. How sensitive should the gaze mode be when checking if the pointer moved\r\n     * to start a new countdown to the pointer down event.\r\n     * Defaults to 1.\r\n     */\r\n    gazeModePointerMovedFactor?: number;\r\n    /**\r\n     * Different button type to use instead of the main component\r\n     */\r\n    overrideButtonId?: string;\r\n    /**\r\n     *  use this rendering group id for the meshes (optional)\r\n     */\r\n    renderingGroupId?: number;\r\n    /**\r\n     * The amount of time in milliseconds it takes between pick found something to a pointer down event.\r\n     * Used in gaze modes. Tracked pointer uses the trigger, screen uses touch events\r\n     * 3000 means 3 seconds between pointing at something and selecting it\r\n     */\r\n    timeToSelect?: number;\r\n    /**\r\n     * Should meshes created here be added to a utility layer or the main scene\r\n     */\r\n    useUtilityLayer?: boolean;\r\n    /**\r\n     * Optional WebXR camera to be used for gaze selection\r\n     */\r\n    gazeCamera?: WebXRCamera;\r\n    /**\r\n     * the xr input to use with this pointer selection\r\n     */\r\n    xrInput: WebXRInput;\r\n\r\n    /**\r\n     * Should the scene pointerX and pointerY update be disabled\r\n     * This is required for fullscreen AR GUI, but might slow down other experiences.\r\n     * Disable in VR, if not needed.\r\n     * The first rig camera (left eye) will be used to calculate the projection\r\n     */\r\n    disableScenePointerVectorUpdate: boolean;\r\n\r\n    /**\r\n     * Enable pointer selection on all controllers instead of switching between them\r\n     */\r\n    enablePointerSelectionOnAllControllers?: boolean;\r\n\r\n    /**\r\n     * The preferred hand to give the pointer selection to. This will be prioritized when the controller initialize.\r\n     * If switch is enabled, it will still allow the user to switch between the different controllers\r\n     */\r\n    preferredHandedness?: XRHandedness;\r\n\r\n    /**\r\n     * Disable switching the pointer selection from one controller to the other.\r\n     * If the preferred hand is set it will be fixed on this hand, and if not it will be fixed on the first controller added to the scene\r\n     */\r\n    disableSwitchOnClick?: boolean;\r\n\r\n    /**\r\n     * The maximum distance of the pointer selection feature. Defaults to 100.\r\n     */\r\n    maxPointerDistance?: number;\r\n\r\n    /**\r\n     * A function that will be called when a new selection mesh is generated.\r\n     * This function should return a mesh that will be used as the selection mesh.\r\n     * The default is a torus with a 0.01 diameter and 0.0075 thickness .\r\n     */\r\n    customSelectionMeshGenerator?: () => Mesh;\r\n\r\n    /**\r\n     * A function that will be called when a new laser pointer mesh is generated.\r\n     * This function should return a mesh that will be used as the laser pointer mesh.\r\n     * The height (y) of the mesh must be 1.\r\n     */\r\n    customLasterPointerMeshGenerator?: () => AbstractMesh;\r\n\r\n    /**\r\n     * Use the grip space instead of the pointer space for selection, if available.\r\n     */\r\n    forceGripIfAvailable?: boolean;\r\n\r\n    /**\r\n     * If set to true, the hand rays will be disabled and the user will be able to look and pick objects.\r\n     * This requires system support (like in the vision OS) and will not work in all systems.\r\n     * @experimental - this is an experimental feature and might change int he future\r\n     */\r\n    lookAndPickMode?: boolean;\r\n}\r\n\r\n/**\r\n * A module that will enable pointer selection for motion controllers of XR Input Sources\r\n */\r\nexport class WebXRControllerPointerSelection extends WebXRAbstractFeature {\r\n    private static _IdCounter = 200;\r\n\r\n    private _attachController = (xrController: WebXRInputSource) => {\r\n        if (this._controllers[xrController.uniqueId]) {\r\n            // already attached\r\n            return;\r\n        }\r\n\r\n        const { laserPointer, selectionMesh } = this._generateNewMeshPair(this._options.forceGripIfAvailable && xrController.grip ? xrController.grip : xrController.pointer);\r\n\r\n        // get two new meshes\r\n        this._controllers[xrController.uniqueId] = {\r\n            xrController,\r\n            laserPointer,\r\n            selectionMesh,\r\n            meshUnderPointer: null,\r\n            pick: null,\r\n            tmpRay: new Ray(new Vector3(), new Vector3()),\r\n            disabledByNearInteraction: false,\r\n            id: WebXRControllerPointerSelection._IdCounter++,\r\n        };\r\n\r\n        if (this._attachedController) {\r\n            if (\r\n                !this._options.enablePointerSelectionOnAllControllers &&\r\n                this._options.preferredHandedness &&\r\n                xrController.inputSource.handedness === this._options.preferredHandedness\r\n            ) {\r\n                this._attachedController = xrController.uniqueId;\r\n            }\r\n        } else {\r\n            if (!this._options.enablePointerSelectionOnAllControllers) {\r\n                this._attachedController = xrController.uniqueId;\r\n            }\r\n        }\r\n\r\n        switch (xrController.inputSource.targetRayMode) {\r\n            case \"tracked-pointer\":\r\n                return this._attachTrackedPointerRayMode(xrController);\r\n            case \"gaze\":\r\n                return this._attachGazeMode(xrController);\r\n            case \"screen\":\r\n            case \"transient-pointer\":\r\n                return this._attachScreenRayMode(xrController);\r\n        }\r\n    };\r\n\r\n    private _controllers: {\r\n        [controllerUniqueId: string]: {\r\n            xrController?: WebXRInputSource;\r\n            webXRCamera?: WebXRCamera;\r\n            selectionComponent?: WebXRControllerComponent;\r\n            onButtonChangedObserver?: Nullable<Observer<WebXRControllerComponent>>;\r\n            onFrameObserver?: Nullable<Observer<XRFrame>>;\r\n            laserPointer: AbstractMesh;\r\n            selectionMesh: AbstractMesh;\r\n            meshUnderPointer: Nullable<AbstractMesh>;\r\n            pick: Nullable<PickingInfo>;\r\n            id: number;\r\n            tmpRay: Ray;\r\n            disabledByNearInteraction: boolean;\r\n            // event support\r\n            eventListeners?: { [event in XREventType]?: (event: XRInputSourceEvent) => void };\r\n            screenCoordinates?: { x: number; y: number };\r\n            pointerDownTriggered?: boolean;\r\n            finalPointerUpTriggered?: boolean;\r\n        };\r\n    } = {};\r\n    private _scene: Scene;\r\n    private _tmpVectorForPickCompare = new Vector3();\r\n\r\n    private _attachedController: string;\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.POINTER_SELECTION;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * Disable lighting on the laser pointer (so it will always be visible)\r\n     */\r\n    public disablePointerLighting: boolean = true;\r\n    /**\r\n     * Disable lighting on the selection mesh (so it will always be visible)\r\n     */\r\n    public disableSelectionMeshLighting: boolean = true;\r\n    /**\r\n     * Should the laser pointer be displayed\r\n     */\r\n    public displayLaserPointer: boolean = true;\r\n    /**\r\n     * Should the selection mesh be displayed (The ring at the end of the laser pointer)\r\n     */\r\n    public displaySelectionMesh: boolean = true;\r\n    /**\r\n     * This color will be set to the laser pointer when selection is triggered\r\n     */\r\n    public laserPointerPickedColor: Color3 = new Color3(0.9, 0.9, 0.9);\r\n    /**\r\n     * Default color of the laser pointer\r\n     */\r\n    public laserPointerDefaultColor: Color3 = new Color3(0.7, 0.7, 0.7);\r\n    /**\r\n     * default color of the selection ring\r\n     */\r\n    public selectionMeshDefaultColor: Color3 = new Color3(0.8, 0.8, 0.8);\r\n    /**\r\n     * This color will be applied to the selection ring when selection is triggered\r\n     */\r\n    public selectionMeshPickedColor: Color3 = new Color3(0.3, 0.3, 1.0);\r\n\r\n    /**\r\n     * Optional filter to be used for ray selection.  This predicate shares behavior with\r\n     * scene.pointerMovePredicate which takes priority if it is also assigned.\r\n     */\r\n    public raySelectionPredicate: (mesh: AbstractMesh) => boolean;\r\n\r\n    /**\r\n     * constructs a new background remover module\r\n     * @param _xrSessionManager the session manager for this module\r\n     * @param _options read-only options to be used in this module\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        private readonly _options: IWebXRControllerPointerSelectionOptions\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this._scene = this._xrSessionManager.scene;\r\n\r\n        // force look and pick mode if using WebXR on safari, assuming it is vision OS\r\n        // Only if not explicitly set. If set to false, it will not be forced\r\n        if (this._options.lookAndPickMode === undefined && (this._scene.getEngine()._badDesktopOS || this._scene.getEngine()._badOS)) {\r\n            this._options.lookAndPickMode = true;\r\n        }\r\n\r\n        // look and pick mode extra state changes\r\n        if (this._options.lookAndPickMode) {\r\n            this._options.enablePointerSelectionOnAllControllers = true;\r\n            this.displayLaserPointer = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * attach this feature\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public attach(): boolean {\r\n        if (!super.attach()) {\r\n            return false;\r\n        }\r\n\r\n        this._options.xrInput.controllers.forEach(this._attachController);\r\n        this._addNewAttachObserver(this._options.xrInput.onControllerAddedObservable, this._attachController, true);\r\n        this._addNewAttachObserver(\r\n            this._options.xrInput.onControllerRemovedObservable,\r\n            (controller) => {\r\n                // REMOVE the controller\r\n                this._detachController(controller.uniqueId);\r\n            },\r\n            true\r\n        );\r\n\r\n        this._scene.constantlyUpdateMeshUnderPointer = true;\r\n\r\n        if (this._options.gazeCamera) {\r\n            const webXRCamera = this._options.gazeCamera;\r\n\r\n            const { laserPointer, selectionMesh } = this._generateNewMeshPair(webXRCamera);\r\n\r\n            this._controllers[\"camera\"] = {\r\n                webXRCamera,\r\n                laserPointer,\r\n                selectionMesh,\r\n                meshUnderPointer: null,\r\n                pick: null,\r\n                tmpRay: new Ray(new Vector3(), new Vector3()),\r\n                disabledByNearInteraction: false,\r\n                id: WebXRControllerPointerSelection._IdCounter++,\r\n            };\r\n            this._attachGazeMode();\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * detach this feature.\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n\r\n        Object.keys(this._controllers).forEach((controllerId) => {\r\n            this._detachController(controllerId);\r\n        });\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Will get the mesh under a specific pointer.\r\n     * `scene.meshUnderPointer` will only return one mesh - either left or right.\r\n     * @param controllerId the controllerId to check\r\n     * @returns The mesh under pointer or null if no mesh is under the pointer\r\n     */\r\n    public getMeshUnderPointer(controllerId: string): Nullable<AbstractMesh> {\r\n        if (this._controllers[controllerId]) {\r\n            return this._controllers[controllerId].meshUnderPointer;\r\n        } else {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the xr controller that correlates to the pointer id in the pointer event\r\n     *\r\n     * @param id the pointer id to search for\r\n     * @returns the controller that correlates to this id or null if not found\r\n     */\r\n    public getXRControllerByPointerId(id: number): Nullable<WebXRInputSource> {\r\n        const keys = Object.keys(this._controllers);\r\n\r\n        for (let i = 0; i < keys.length; ++i) {\r\n            if (this._controllers[keys[i]].id === id) {\r\n                return this._controllers[keys[i]].xrController || null;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getPointerSelectionDisabledByPointerId(id: number): boolean {\r\n        const keys = Object.keys(this._controllers);\r\n\r\n        for (let i = 0; i < keys.length; ++i) {\r\n            if (this._controllers[keys[i]].id === id) {\r\n                return this._controllers[keys[i]].disabledByNearInteraction;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setPointerSelectionDisabledByPointerId(id: number, state: boolean) {\r\n        const keys = Object.keys(this._controllers);\r\n\r\n        for (let i = 0; i < keys.length; ++i) {\r\n            if (this._controllers[keys[i]].id === id) {\r\n                this._controllers[keys[i]].disabledByNearInteraction = state;\r\n                return;\r\n            }\r\n        }\r\n    }\r\n\r\n    private _identityMatrix = Matrix.Identity();\r\n    private _screenCoordinatesRef = Vector3.Zero();\r\n    private _viewportRef = new Viewport(0, 0, 0, 0);\r\n\r\n    protected _onXRFrame(_xrFrame: XRFrame) {\r\n        Object.keys(this._controllers).forEach((id) => {\r\n            // look and pick mode\r\n            // only do this for the selected pointer\r\n            const controllerData = this._controllers[id];\r\n            if (this._options.lookAndPickMode && controllerData.xrController?.inputSource.targetRayMode !== \"transient-pointer\") {\r\n                return;\r\n            }\r\n            if ((!this._options.enablePointerSelectionOnAllControllers && id !== this._attachedController) || controllerData.disabledByNearInteraction) {\r\n                controllerData.selectionMesh.isVisible = false;\r\n                controllerData.laserPointer.isVisible = false;\r\n                controllerData.pick = null;\r\n                return;\r\n            }\r\n\r\n            controllerData.laserPointer.isVisible = this.displayLaserPointer;\r\n\r\n            let controllerGlobalPosition: Vector3;\r\n\r\n            // Every frame check collisions/input\r\n            if (controllerData.xrController) {\r\n                controllerGlobalPosition =\r\n                    this._options.forceGripIfAvailable && controllerData.xrController.grip\r\n                        ? controllerData.xrController.grip.position\r\n                        : controllerData.xrController.pointer.position;\r\n                controllerData.xrController.getWorldPointerRayToRef(controllerData.tmpRay, this._options.forceGripIfAvailable);\r\n            } else if (controllerData.webXRCamera) {\r\n                controllerGlobalPosition = controllerData.webXRCamera.position;\r\n                controllerData.webXRCamera.getForwardRayToRef(controllerData.tmpRay);\r\n            } else {\r\n                return;\r\n            }\r\n\r\n            if (this._options.maxPointerDistance) {\r\n                controllerData.tmpRay.length = this._options.maxPointerDistance;\r\n            }\r\n            // update pointerX and pointerY of the scene. Only if the flag is set to true!\r\n            if (!this._options.disableScenePointerVectorUpdate && controllerGlobalPosition) {\r\n                const scene = this._xrSessionManager.scene;\r\n                const camera = this._options.xrInput.xrCamera;\r\n                if (camera) {\r\n                    camera.viewport.toGlobalToRef(scene.getEngine().getRenderWidth() / camera.rigCameras.length, scene.getEngine().getRenderHeight(), this._viewportRef);\r\n                    Vector3.ProjectToRef(controllerGlobalPosition, this._identityMatrix, camera.getTransformationMatrix(), this._viewportRef, this._screenCoordinatesRef);\r\n                    // stay safe\r\n                    if (\r\n                        typeof this._screenCoordinatesRef.x === \"number\" &&\r\n                        typeof this._screenCoordinatesRef.y === \"number\" &&\r\n                        !isNaN(this._screenCoordinatesRef.x) &&\r\n                        !isNaN(this._screenCoordinatesRef.y) &&\r\n                        this._screenCoordinatesRef.x !== Infinity &&\r\n                        this._screenCoordinatesRef.y !== Infinity\r\n                    ) {\r\n                        scene.pointerX = this._screenCoordinatesRef.x;\r\n                        scene.pointerY = this._screenCoordinatesRef.y;\r\n\r\n                        controllerData.screenCoordinates = {\r\n                            x: this._screenCoordinatesRef.x,\r\n                            y: this._screenCoordinatesRef.y,\r\n                        };\r\n                    }\r\n                }\r\n            }\r\n\r\n            let utilityScenePick = null;\r\n            if (this._utilityLayerScene) {\r\n                utilityScenePick = this._utilityLayerScene.pickWithRay(controllerData.tmpRay, this._utilityLayerScene.pointerMovePredicate || this.raySelectionPredicate);\r\n            }\r\n\r\n            const originalScenePick = this._scene.pickWithRay(controllerData.tmpRay, this._scene.pointerMovePredicate || this.raySelectionPredicate);\r\n            if (!utilityScenePick || !utilityScenePick.hit) {\r\n                // No hit in utility scene\r\n                controllerData.pick = originalScenePick;\r\n            } else if (!originalScenePick || !originalScenePick.hit) {\r\n                // No hit in original scene\r\n                controllerData.pick = utilityScenePick;\r\n            } else if (utilityScenePick.distance < originalScenePick.distance) {\r\n                // Hit is closer in utility scene\r\n                controllerData.pick = utilityScenePick;\r\n            } else {\r\n                // Hit is closer in original scene\r\n                controllerData.pick = originalScenePick;\r\n            }\r\n\r\n            if (controllerData.pick && controllerData.xrController) {\r\n                controllerData.pick.aimTransform = controllerData.xrController.pointer;\r\n                controllerData.pick.gripTransform = controllerData.xrController.grip || null;\r\n                controllerData.pick.originMesh = controllerData.xrController.pointer;\r\n            }\r\n\r\n            const pick = controllerData.pick;\r\n\r\n            if (pick && pick.pickedPoint && pick.hit) {\r\n                // Update laser state\r\n                this._updatePointerDistance(controllerData.laserPointer, pick.distance);\r\n\r\n                // Update cursor state\r\n                controllerData.selectionMesh.position.copyFrom(pick.pickedPoint);\r\n                controllerData.selectionMesh.scaling.x = Math.sqrt(pick.distance);\r\n                controllerData.selectionMesh.scaling.y = Math.sqrt(pick.distance);\r\n                controllerData.selectionMesh.scaling.z = Math.sqrt(pick.distance);\r\n\r\n                // To avoid z-fighting\r\n                const pickNormal = this._convertNormalToDirectionOfRay(pick.getNormal(true), controllerData.tmpRay);\r\n                const deltaFighting = 0.001;\r\n                controllerData.selectionMesh.position.copyFrom(pick.pickedPoint);\r\n                if (pickNormal) {\r\n                    const axis1 = Vector3.Cross(Axis.Y, pickNormal);\r\n                    const axis2 = Vector3.Cross(pickNormal, axis1);\r\n                    Vector3.RotationFromAxisToRef(axis2, pickNormal, axis1, controllerData.selectionMesh.rotation);\r\n                    controllerData.selectionMesh.position.addInPlace(pickNormal.scale(deltaFighting));\r\n                }\r\n                controllerData.selectionMesh.isVisible = true && this.displaySelectionMesh;\r\n                controllerData.meshUnderPointer = pick.pickedMesh;\r\n            } else {\r\n                controllerData.selectionMesh.isVisible = false;\r\n                this._updatePointerDistance(controllerData.laserPointer, 1);\r\n                controllerData.meshUnderPointer = null;\r\n            }\r\n        });\r\n    }\r\n\r\n    private get _utilityLayerScene() {\r\n        return this._options.customUtilityLayerScene || UtilityLayerRenderer.DefaultUtilityLayer.utilityLayerScene;\r\n    }\r\n\r\n    private _attachGazeMode(xrController?: WebXRInputSource) {\r\n        const controllerData = this._controllers[(xrController && xrController.uniqueId) || \"camera\"];\r\n        // attached when touched, detaches when raised\r\n        const timeToSelect = this._options.timeToSelect || 3000;\r\n        const sceneToRenderTo = this._options.useUtilityLayer ? this._utilityLayerScene : this._scene;\r\n        let oldPick = new PickingInfo();\r\n        const discMesh = CreateTorus(\r\n            \"selection\",\r\n            {\r\n                diameter: 0.0035 * 15,\r\n                thickness: 0.0025 * 6,\r\n                tessellation: 20,\r\n            },\r\n            sceneToRenderTo\r\n        );\r\n        discMesh.isVisible = false;\r\n        discMesh.isPickable = false;\r\n        discMesh.parent = controllerData.selectionMesh;\r\n        let timer = 0;\r\n        let downTriggered = false;\r\n        const pointerEventInit: PointerEventInit = {\r\n            pointerId: controllerData.id,\r\n            pointerType: \"xr\",\r\n        };\r\n        controllerData.onFrameObserver = this._xrSessionManager.onXRFrameObservable.add(() => {\r\n            if (!controllerData.pick) {\r\n                return;\r\n            }\r\n            this._augmentPointerInit(pointerEventInit, controllerData.id, controllerData.screenCoordinates);\r\n            controllerData.laserPointer.material!.alpha = 0;\r\n            discMesh.isVisible = false;\r\n            if (controllerData.pick.hit) {\r\n                if (!this._pickingMoved(oldPick, controllerData.pick)) {\r\n                    if (timer > timeToSelect / 10) {\r\n                        discMesh.isVisible = true;\r\n                    }\r\n\r\n                    timer += this._scene.getEngine().getDeltaTime();\r\n                    if (timer >= timeToSelect) {\r\n                        this._scene.simulatePointerDown(controllerData.pick, pointerEventInit);\r\n                        // this pointerdown event is not setting the controllerData.pointerDownTriggered to avoid a pointerUp event when this feature is detached\r\n                        downTriggered = true;\r\n                        // pointer up right after down, if disable on touch out\r\n                        if (this._options.disablePointerUpOnTouchOut) {\r\n                            this._scene.simulatePointerUp(controllerData.pick, pointerEventInit);\r\n                        }\r\n                        discMesh.isVisible = false;\r\n                    } else {\r\n                        const scaleFactor = 1 - timer / timeToSelect;\r\n                        discMesh.scaling.set(scaleFactor, scaleFactor, scaleFactor);\r\n                    }\r\n                } else {\r\n                    if (downTriggered) {\r\n                        if (!this._options.disablePointerUpOnTouchOut) {\r\n                            this._scene.simulatePointerUp(controllerData.pick, pointerEventInit);\r\n                        }\r\n                    }\r\n                    downTriggered = false;\r\n                    timer = 0;\r\n                }\r\n            } else {\r\n                downTriggered = false;\r\n                timer = 0;\r\n            }\r\n\r\n            this._scene.simulatePointerMove(controllerData.pick, pointerEventInit);\r\n\r\n            oldPick = controllerData.pick;\r\n        });\r\n\r\n        if (this._options.renderingGroupId !== undefined) {\r\n            discMesh.renderingGroupId = this._options.renderingGroupId;\r\n        }\r\n        if (xrController) {\r\n            xrController.onDisposeObservable.addOnce(() => {\r\n                if (controllerData.pick && !this._options.disablePointerUpOnTouchOut && downTriggered) {\r\n                    this._scene.simulatePointerUp(controllerData.pick, pointerEventInit);\r\n                    controllerData.finalPointerUpTriggered = true;\r\n                }\r\n                discMesh.dispose();\r\n            });\r\n        }\r\n    }\r\n\r\n    private _attachScreenRayMode(xrController: WebXRInputSource) {\r\n        const controllerData = this._controllers[xrController.uniqueId];\r\n        let downTriggered = false;\r\n        const pointerEventInit: PointerEventInit = {\r\n            pointerId: controllerData.id,\r\n            pointerType: \"xr\",\r\n        };\r\n        controllerData.onFrameObserver = this._xrSessionManager.onXRFrameObservable.add(() => {\r\n            this._augmentPointerInit(pointerEventInit, controllerData.id, controllerData.screenCoordinates);\r\n            if (!controllerData.pick || (this._options.disablePointerUpOnTouchOut && downTriggered)) {\r\n                return;\r\n            }\r\n            if (!downTriggered) {\r\n                this._scene.simulatePointerDown(controllerData.pick, pointerEventInit);\r\n                controllerData.pointerDownTriggered = true;\r\n                downTriggered = true;\r\n                if (this._options.disablePointerUpOnTouchOut) {\r\n                    this._scene.simulatePointerUp(controllerData.pick, pointerEventInit);\r\n                }\r\n            } else {\r\n                this._scene.simulatePointerMove(controllerData.pick, pointerEventInit);\r\n            }\r\n        });\r\n        xrController.onDisposeObservable.addOnce(() => {\r\n            this._augmentPointerInit(pointerEventInit, controllerData.id, controllerData.screenCoordinates);\r\n            this._xrSessionManager.runInXRFrame(() => {\r\n                if (controllerData.pick && !controllerData.finalPointerUpTriggered && downTriggered && !this._options.disablePointerUpOnTouchOut) {\r\n                    this._scene.simulatePointerUp(controllerData.pick, pointerEventInit);\r\n                    controllerData.finalPointerUpTriggered = true;\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    private _attachTrackedPointerRayMode(xrController: WebXRInputSource) {\r\n        const controllerData = this._controllers[xrController.uniqueId];\r\n        if (this._options.forceGazeMode) {\r\n            return this._attachGazeMode(xrController);\r\n        }\r\n        const pointerEventInit: PointerEventInit = {\r\n            pointerId: controllerData.id,\r\n            pointerType: \"xr\",\r\n        };\r\n        controllerData.onFrameObserver = this._xrSessionManager.onXRFrameObservable.add(() => {\r\n            (<StandardMaterial>controllerData.laserPointer.material).disableLighting = this.disablePointerLighting;\r\n            (<StandardMaterial>controllerData.selectionMesh.material).disableLighting = this.disableSelectionMeshLighting;\r\n\r\n            if (controllerData.pick) {\r\n                this._augmentPointerInit(pointerEventInit, controllerData.id, controllerData.screenCoordinates);\r\n                this._scene.simulatePointerMove(controllerData.pick, pointerEventInit);\r\n            }\r\n        });\r\n        if (xrController.inputSource.gamepad) {\r\n            const init = (motionController: WebXRAbstractMotionController) => {\r\n                if (this._options.overrideButtonId) {\r\n                    controllerData.selectionComponent = motionController.getComponent(this._options.overrideButtonId);\r\n                }\r\n                if (!controllerData.selectionComponent) {\r\n                    controllerData.selectionComponent = motionController.getMainComponent();\r\n                }\r\n\r\n                controllerData.onButtonChangedObserver = controllerData.selectionComponent.onButtonStateChangedObservable.add((component) => {\r\n                    if (component.changes.pressed) {\r\n                        const pressed = component.changes.pressed.current;\r\n                        if (controllerData.pick) {\r\n                            if (this._options.enablePointerSelectionOnAllControllers || xrController.uniqueId === this._attachedController) {\r\n                                this._augmentPointerInit(pointerEventInit, controllerData.id, controllerData.screenCoordinates);\r\n                                if (pressed) {\r\n                                    this._scene.simulatePointerDown(controllerData.pick, pointerEventInit);\r\n                                    controllerData.pointerDownTriggered = true;\r\n                                    (<StandardMaterial>controllerData.selectionMesh.material).emissiveColor = this.selectionMeshPickedColor;\r\n                                    (<StandardMaterial>controllerData.laserPointer.material).emissiveColor = this.laserPointerPickedColor;\r\n                                } else {\r\n                                    this._scene.simulatePointerUp(controllerData.pick, pointerEventInit);\r\n                                    (<StandardMaterial>controllerData.selectionMesh.material).emissiveColor = this.selectionMeshDefaultColor;\r\n                                    (<StandardMaterial>controllerData.laserPointer.material).emissiveColor = this.laserPointerDefaultColor;\r\n                                }\r\n                            }\r\n                        } else {\r\n                            if (pressed && !this._options.enablePointerSelectionOnAllControllers && !this._options.disableSwitchOnClick) {\r\n                                // force a pointer up if switching controllers\r\n                                // get the controller that was attached before\r\n                                const prevController = this._controllers[this._attachedController];\r\n                                if (prevController && prevController.pointerDownTriggered && !prevController.finalPointerUpTriggered) {\r\n                                    this._augmentPointerInit(pointerEventInit, prevController.id, prevController.screenCoordinates);\r\n                                    this._scene.simulatePointerUp(new PickingInfo(), {\r\n                                        pointerId: prevController.id,\r\n                                        pointerType: \"xr\",\r\n                                    });\r\n                                    prevController.finalPointerUpTriggered = true;\r\n                                }\r\n                                this._attachedController = xrController.uniqueId;\r\n                            }\r\n                        }\r\n                    }\r\n                });\r\n            };\r\n            if (xrController.motionController) {\r\n                init(xrController.motionController);\r\n            } else {\r\n                xrController.onMotionControllerInitObservable.add(init);\r\n            }\r\n        } else {\r\n            // use the select and squeeze events\r\n            const selectStartListener = (event: XRInputSourceEvent) => {\r\n                this._xrSessionManager.onXRFrameObservable.addOnce(() => {\r\n                    this._augmentPointerInit(pointerEventInit, controllerData.id, controllerData.screenCoordinates);\r\n                    if (controllerData.xrController && event.inputSource === controllerData.xrController.inputSource && controllerData.pick) {\r\n                        this._scene.simulatePointerDown(controllerData.pick, pointerEventInit);\r\n                        controllerData.pointerDownTriggered = true;\r\n                        (<StandardMaterial>controllerData.selectionMesh.material).emissiveColor = this.selectionMeshPickedColor;\r\n                        (<StandardMaterial>controllerData.laserPointer.material).emissiveColor = this.laserPointerPickedColor;\r\n                    }\r\n                });\r\n            };\r\n\r\n            const selectEndListener = (event: XRInputSourceEvent) => {\r\n                this._xrSessionManager.onXRFrameObservable.addOnce(() => {\r\n                    this._augmentPointerInit(pointerEventInit, controllerData.id, controllerData.screenCoordinates);\r\n                    if (controllerData.xrController && event.inputSource === controllerData.xrController.inputSource && controllerData.pick) {\r\n                        this._scene.simulatePointerUp(controllerData.pick, pointerEventInit);\r\n                        (<StandardMaterial>controllerData.selectionMesh.material).emissiveColor = this.selectionMeshDefaultColor;\r\n                        (<StandardMaterial>controllerData.laserPointer.material).emissiveColor = this.laserPointerDefaultColor;\r\n                    }\r\n                });\r\n            };\r\n\r\n            controllerData.eventListeners = {\r\n                selectend: selectEndListener,\r\n                selectstart: selectStartListener,\r\n            };\r\n\r\n            this._xrSessionManager.session.addEventListener(\"selectstart\", selectStartListener);\r\n            this._xrSessionManager.session.addEventListener(\"selectend\", selectEndListener);\r\n        }\r\n    }\r\n\r\n    private _convertNormalToDirectionOfRay(normal: Nullable<Vector3>, ray: Ray) {\r\n        if (normal) {\r\n            const angle = Math.acos(Vector3.Dot(normal, ray.direction));\r\n            if (angle < Math.PI / 2) {\r\n                normal.scaleInPlace(-1);\r\n            }\r\n        }\r\n        return normal;\r\n    }\r\n\r\n    private _detachController(xrControllerUniqueId: string) {\r\n        const controllerData = this._controllers[xrControllerUniqueId];\r\n        if (!controllerData) {\r\n            return;\r\n        }\r\n        if (controllerData.selectionComponent) {\r\n            if (controllerData.onButtonChangedObserver) {\r\n                controllerData.selectionComponent.onButtonStateChangedObservable.remove(controllerData.onButtonChangedObserver);\r\n            }\r\n        }\r\n        if (controllerData.onFrameObserver) {\r\n            this._xrSessionManager.onXRFrameObservable.remove(controllerData.onFrameObserver);\r\n        }\r\n        if (controllerData.eventListeners) {\r\n            Object.keys(controllerData.eventListeners).forEach((eventName: string) => {\r\n                const func = controllerData.eventListeners && controllerData.eventListeners[eventName as XREventType];\r\n                if (func) {\r\n                    // For future reference - this is an issue in the WebXR typings.\r\n                    this._xrSessionManager.session.removeEventListener(eventName as XREventType, func as any);\r\n                }\r\n            });\r\n        }\r\n\r\n        if (!controllerData.finalPointerUpTriggered && controllerData.pointerDownTriggered) {\r\n            // Stay safe and fire a pointerup, in case it wasn't already triggered\r\n            const pointerEventInit: PointerEventInit = {\r\n                pointerId: controllerData.id,\r\n                pointerType: \"xr\",\r\n            };\r\n            this._xrSessionManager.runInXRFrame(() => {\r\n                this._augmentPointerInit(pointerEventInit, controllerData.id, controllerData.screenCoordinates);\r\n                this._scene.simulatePointerUp(controllerData.pick || new PickingInfo(), pointerEventInit);\r\n                controllerData.finalPointerUpTriggered = true;\r\n            });\r\n        }\r\n        this._xrSessionManager.scene.onBeforeRenderObservable.addOnce(() => {\r\n            try {\r\n                controllerData.selectionMesh.dispose();\r\n                controllerData.laserPointer.dispose();\r\n                // remove from the map\r\n                delete this._controllers[xrControllerUniqueId];\r\n                if (this._attachedController === xrControllerUniqueId) {\r\n                    // check for other controllers\r\n                    const keys = Object.keys(this._controllers);\r\n                    if (keys.length) {\r\n                        this._attachedController = keys[0];\r\n                    } else {\r\n                        this._attachedController = \"\";\r\n                    }\r\n                }\r\n            } catch (e) {\r\n                Tools.Warn(\"controller already detached.\");\r\n            }\r\n        });\r\n    }\r\n\r\n    private _generateNewMeshPair(meshParent: Node) {\r\n        const sceneToRenderTo = this._options.useUtilityLayer ? this._options.customUtilityLayerScene || UtilityLayerRenderer.DefaultUtilityLayer.utilityLayerScene : this._scene;\r\n        const laserPointer = this._options.customLasterPointerMeshGenerator\r\n            ? this._options.customLasterPointerMeshGenerator()\r\n            : CreateCylinder(\r\n                  \"laserPointer\",\r\n                  {\r\n                      height: 1,\r\n                      diameterTop: 0.0002,\r\n                      diameterBottom: 0.004,\r\n                      tessellation: 20,\r\n                      subdivisions: 1,\r\n                  },\r\n                  sceneToRenderTo\r\n              );\r\n        laserPointer.parent = meshParent;\r\n        const laserPointerMaterial = new StandardMaterial(\"laserPointerMat\", sceneToRenderTo);\r\n        laserPointerMaterial.emissiveColor = this.laserPointerDefaultColor;\r\n        laserPointerMaterial.alpha = 0.7;\r\n        laserPointer.material = laserPointerMaterial;\r\n        laserPointer.rotation.x = Math.PI / 2;\r\n        this._updatePointerDistance(laserPointer, 1);\r\n        laserPointer.isPickable = false;\r\n        laserPointer.isVisible = false;\r\n\r\n        // Create a gaze tracker for the  XR controller\r\n        const selectionMesh = this._options.customSelectionMeshGenerator\r\n            ? this._options.customSelectionMeshGenerator()\r\n            : CreateTorus(\r\n                  \"gazeTracker\",\r\n                  {\r\n                      diameter: 0.0035 * 3,\r\n                      thickness: 0.0025 * 3,\r\n                      tessellation: 20,\r\n                  },\r\n                  sceneToRenderTo\r\n              );\r\n        selectionMesh.bakeCurrentTransformIntoVertices();\r\n        selectionMesh.isPickable = false;\r\n        selectionMesh.isVisible = false;\r\n        const targetMat = new StandardMaterial(\"targetMat\", sceneToRenderTo);\r\n        targetMat.specularColor = Color3.Black();\r\n        targetMat.emissiveColor = this.selectionMeshDefaultColor;\r\n        targetMat.backFaceCulling = false;\r\n        selectionMesh.material = targetMat;\r\n\r\n        if (this._options.renderingGroupId !== undefined) {\r\n            laserPointer.renderingGroupId = this._options.renderingGroupId;\r\n            selectionMesh.renderingGroupId = this._options.renderingGroupId;\r\n        }\r\n\r\n        return {\r\n            laserPointer,\r\n            selectionMesh,\r\n        };\r\n    }\r\n\r\n    private _pickingMoved(oldPick: PickingInfo, newPick: PickingInfo) {\r\n        if (!oldPick.hit || !newPick.hit) {\r\n            return true;\r\n        }\r\n        if (!oldPick.pickedMesh || !oldPick.pickedPoint || !newPick.pickedMesh || !newPick.pickedPoint) {\r\n            return true;\r\n        }\r\n        if (oldPick.pickedMesh !== newPick.pickedMesh) {\r\n            return true;\r\n        }\r\n        oldPick.pickedPoint?.subtractToRef(newPick.pickedPoint, this._tmpVectorForPickCompare);\r\n        this._tmpVectorForPickCompare.set(Math.abs(this._tmpVectorForPickCompare.x), Math.abs(this._tmpVectorForPickCompare.y), Math.abs(this._tmpVectorForPickCompare.z));\r\n        const delta = (this._options.gazeModePointerMovedFactor || 1) * 0.01 * newPick.distance;\r\n        const length = this._tmpVectorForPickCompare.length();\r\n        if (length > delta) {\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    private _updatePointerDistance(_laserPointer: AbstractMesh, distance: number = 100) {\r\n        _laserPointer.scaling.y = distance;\r\n        // a bit of distance from the controller\r\n        if (this._scene.useRightHandedSystem) {\r\n            distance *= -1;\r\n        }\r\n        _laserPointer.position.z = distance / 2 + 0.05;\r\n    }\r\n\r\n    private _augmentPointerInit(pointerEventInit: PointerEventInit, id: number, screenCoordinates?: { x: number; y: number }): void {\r\n        pointerEventInit.pointerId = id;\r\n        pointerEventInit.pointerType = \"xr\";\r\n        if (screenCoordinates) {\r\n            pointerEventInit.screenX = screenCoordinates.x;\r\n            pointerEventInit.screenY = screenCoordinates.y;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public get lasterPointerDefaultColor(): Color3 {\r\n        // here due to a typo\r\n        return this.laserPointerDefaultColor;\r\n    }\r\n}\r\n\r\n//register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRControllerPointerSelection.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRControllerPointerSelection(xrSessionManager, options);\r\n    },\r\n    WebXRControllerPointerSelection.Version,\r\n    true\r\n);\r\n"]}