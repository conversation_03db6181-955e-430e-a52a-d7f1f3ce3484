{"version": 3, "file": "physicsMaterial.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Physics/v2/physicsMaterial.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AACH,MAAM,CAAN,IAAY,0BAwBX;AAxBD,WAAY,0BAA0B;IAClC;;;OAGG;IACH,+FAAc,CAAA;IACd;;;OAGG;IACH,iFAAO,CAAA;IACP;;OAEG;IACH,iFAAO,CAAA;IACP;;OAEG;IACH,iGAAe,CAAA;IACf;;;OAGG;IACH,mFAAQ,CAAA;AACZ,CAAC,EAxBW,0BAA0B,KAA1B,0BAA0B,QAwBrC", "sourcesContent": ["/**\r\n * Determines how values from the PhysicsMaterial are combined when\r\n * two objects are in contact. When each PhysicsMaterial specifies\r\n * a different combine mode for some property, the combine mode which\r\n * is used will be selected based on their order in this enum - i.e.\r\n * a value later in this list will be preferentially used.\r\n */\r\nexport enum PhysicsMaterialCombineMode {\r\n    /**\r\n     * The final value will be the geometric mean of the two values:\r\n     * sqrt( valueA *  valueB )\r\n     */\r\n    GEOMETRIC_MEAN,\r\n    /**\r\n     * The final value will be the smaller of the two:\r\n     * min( valueA , valueB )\r\n     */\r\n    MINIMUM,\r\n    /* The final value will be the larger of the two:\r\n     * max( valueA , valueB )\r\n     */\r\n    MAXIMUM,\r\n    /* The final value will be the arithmetic mean of the two values:\r\n     * (valueA + valueB) / 2\r\n     */\r\n    ARITHMETIC_MEAN,\r\n    /**\r\n     * The final value will be the product of the two values:\r\n     * valueA * valueB\r\n     */\r\n    MULTIPLY,\r\n}\r\n\r\n/**\r\n * Physics material class\r\n * Helps setting friction and restitution that are used to compute responding forces in collision response\r\n */\r\nexport interface PhysicsMaterial {\r\n    /**\r\n     * Sets the friction used by this material\r\n     *\r\n     * The friction determines how much an object will slow down when it is in contact with another object.\r\n     * This is important for simulating realistic physics, such as when an object slides across a surface.\r\n     *\r\n     * If not provided, a default value of 0.5 will be used.\r\n     */\r\n    friction?: number;\r\n\r\n    /**\r\n     * Sets the static friction used by this material.\r\n     *\r\n     * Static friction is the friction that must be overcome before a pair of objects can start sliding\r\n     * relative to each other; for physically-realistic behaviour, it should be at least as high as the\r\n     * normal friction value. If not provided, the friction value will be used\r\n     */\r\n    staticFriction?: number;\r\n\r\n    /**\r\n     * Sets the restitution of the physics material.\r\n     *\r\n     * The restitution is a factor which describes, the amount of energy that is retained after a collision,\r\n     * which should be a number between 0 and 1..\r\n     *\r\n     * A restitution of 0 means that no energy is retained and the objects will not bounce off each other,\r\n     * while a restitution of 1 means that all energy is retained and the objects will bounce.\r\n     *\r\n     * Note, though, due that due to the simulation implementation, an object with a restitution of 1 may\r\n     * still lose energy over time.\r\n     *\r\n     * If not provided, a default value of 0 will be used.\r\n     */\r\n    restitution?: number;\r\n\r\n    /**\r\n     * Describes how two different friction values should be combined. See PhysicsMaterialCombineMode for\r\n     * more details.\r\n     *\r\n     * If not provided, will use PhysicsMaterialCombineMode.MINIMUM\r\n     */\r\n    frictionCombine?: PhysicsMaterialCombineMode;\r\n\r\n    /**\r\n     * Describes how two different restitution values should be combined. See PhysicsMaterialCombineMode for\r\n     * more details.\r\n     *\r\n     * If not provided, will use PhysicsMaterialCombineMode.MAXIMUM\r\n     */\r\n    restitutionCombine?: PhysicsMaterialCombineMode;\r\n}\r\n"]}