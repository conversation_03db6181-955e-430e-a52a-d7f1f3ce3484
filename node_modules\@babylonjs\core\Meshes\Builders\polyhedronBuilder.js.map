{"version": 3, "file": "polyhedronBuilder.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Builders/polyhedronBuilder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAEzE,sEAAsE;AACtE;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,0BAA0B,CAAC,OAa1C;IACG,8BAA8B;IAC9B,wLAAwL;IACxL,0NAA0N;IAC1N,MAAM,SAAS,GAA+C,EAAE,CAAC;IACjE,SAAS,CAAC,CAAC,CAAC,GAAG;QACX,MAAM,EAAE;YACJ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;YAChB,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC;YACzB,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC;YAClC,CAAC,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC;SACtC;QACD,IAAI,EAAE;YACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SACZ;KACJ,CAAC;IACF,SAAS,CAAC,CAAC,CAAC,GAAG;QACX,MAAM,EAAE;YACJ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;YAChB,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;YAChB,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;YAChB,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;YACjB,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC;SACpB;QACD,IAAI,EAAE;YACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SACZ;KACJ,CAAC;IACF,SAAS,CAAC,CAAC,CAAC,GAAG;QACX,MAAM,EAAE;YACJ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;YAChB,CAAC,SAAS,EAAE,CAAC,EAAE,SAAS,CAAC;YACzB,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC;YACjC,CAAC,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;YAClC,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC;YAChC,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;YACjC,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC;YACjC,CAAC,SAAS,EAAE,CAAC,EAAE,SAAS,CAAC;YACzB,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC;YAC1B,CAAC,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;YAClC,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC;YACjC,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC;YAClC,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC;YAClC,CAAC,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC;YAC5B,CAAC,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC;YACnC,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC;YACjC,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC;YAClC,CAAC,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC;SACpB;QACD,IAAI,EAAE;YACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YACjB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YACjB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YACjB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAClB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAClB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAClB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YACpB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YACpB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;SACvB;KACJ,CAAC;IACF,SAAS,CAAC,CAAC,CAAC,GAAG;QACX,MAAM,EAAE;YACJ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;YAChB,CAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,CAAC;YACxB,CAAC,SAAS,EAAE,CAAC,EAAE,SAAS,CAAC;YACzB,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC;YACjC,CAAC,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;YAClC,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC;YAC1B,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC;YACjC,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC;YAClC,CAAC,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC;YAC1B,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC;YAC5B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC;SACpB;QACD,IAAI,EAAE;YACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACV,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACV,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACV,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACV,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACV,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;YACX,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACV,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;SACd;KACJ,CAAC;IACF,SAAS,CAAC,CAAC,CAAC,GAAG;QACX,MAAM,EAAE;YACJ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;YAChB,CAAC,SAAS,EAAE,CAAC,EAAE,SAAS,CAAC;YACzB,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC;YACjC,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YAClC,CAAC,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;YAClC,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACjC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC;YAC/B,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;YAClC,CAAC,CAAC,SAAS,EAAE,CAAC,EAAE,SAAS,CAAC;YAC1B,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC;YAC7B,CAAC,CAAC,SAAS,EAAE,GAAG,EAAE,SAAS,CAAC;YAC5B,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC;YAC3B,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC;YAC1B,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC;YAC7B,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC;YAC5B,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;YACnC,CAAC,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC;YAClC,CAAC,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC;YACpC,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;YACjC,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC;YACnC,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC;YAClC,CAAC,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC;SACpB;QACD,IAAI,EAAE;YACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACV,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;YACX,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;YACX,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YACZ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YACZ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACb,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACb,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACb,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YACd,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YACd,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;YACd,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YACf,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YACf,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YACf,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAChB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAChB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAChB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAChB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAChB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;SACnB;KACJ,CAAC;IACF,SAAS,CAAC,CAAC,CAAC,GAAG;QACX,MAAM,EAAE;YACJ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;YAChB,CAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,CAAC;YACxB,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YAClC,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;YACjC,CAAC,SAAS,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;YAClC,CAAC,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC;SACvC;QACD,IAAI,EAAE;YACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SACf;KACJ,CAAC;IACF,SAAS,CAAC,CAAC,CAAC,GAAG;QACX,MAAM,EAAE;YACJ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;YAChB,CAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,CAAC;YACxB,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YAClC,CAAC,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;YACnC,CAAC,SAAS,EAAE,SAAS,EAAE,CAAC,UAAU,CAAC;YACnC,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC;YACnC,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC,UAAU,CAAC;YACnC,CAAC,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC;YACnC,CAAC,SAAS,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;YAClC,CAAC,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC;SACtC;QACD,IAAI,EAAE;YACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SAClB;KACJ,CAAC;IACF,SAAS,CAAC,CAAC,CAAC,GAAG;QACX,MAAM,EAAE;YACJ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;YAChB,CAAC,SAAS,EAAE,CAAC,EAAE,SAAS,CAAC;YACzB,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YAClC,CAAC,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;YACnC,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACjC,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC;YAClC,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC;YACjC,CAAC,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC;YACpC,CAAC,SAAS,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC;YAClC,CAAC,SAAS,EAAE,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC;YACnC,CAAC,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC;SACpB;QACD,IAAI,EAAE;YACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACb,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACb,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YACd,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAClB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;SACvB;KACJ,CAAC;IACF,SAAS,CAAC,CAAC,CAAC,GAAG;QACX,MAAM,EAAE;YACJ,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC/B,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAChC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC9B,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;SAClC;QACD,IAAI,EAAE;YACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SACf;KACJ,CAAC;IACF,SAAS,CAAC,CAAC,CAAC,GAAG;QACX,MAAM,EAAE;YACJ,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;YAC/B,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC;YAC9B,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAC/B,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAC/B,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;SACnC;QACD,IAAI,EAAE;YACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SAClB;KACJ,CAAC;IACF,SAAS,CAAC,EAAE,CAAC,GAAG;QACZ,MAAM,EAAE;YACJ,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC/B,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC;YAC9B,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;YAC7B,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;SACjC;QACD,IAAI,EAAE;YACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SACZ;KACJ,CAAC;IACF,SAAS,CAAC,EAAE,CAAC,GAAG;QACZ,MAAM,EAAE;YACJ,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAChC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACjC,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAC/B,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC9B,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAC/B,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC;SAClC;QACD,IAAI,EAAE;YACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SACZ;KACJ,CAAC;IACF,SAAS,CAAC,EAAE,CAAC,GAAG;QACZ,MAAM,EAAE;YACJ,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC/B,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;YACtB,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACjC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAChC,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAC/B,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC9B,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtB,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;SAClC;QACD,IAAI,EAAE;YACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;SACf;KACJ,CAAC;IACF,SAAS,CAAC,EAAE,CAAC,GAAG;QACZ,MAAM,EAAE;YACJ,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC/B,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACjC,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;YACxB,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAChC,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC9B,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;YACxB,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAC/B,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAC/B,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;SACnC;QACD,IAAI,EAAE;YACF,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YACX,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACV,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACV,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACd,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACb,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;SAChB;KACJ,CAAC;IACF,SAAS,CAAC,EAAE,CAAC,GAAG;QACZ,MAAM,EAAE;YACJ,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAC/B,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACjC,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC/B,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAChC,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACjC,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;YAC7B,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACjC,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAChC,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC/B,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACjC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC9B,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC7B,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAC/B,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC9B,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAC/B,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;YAC7B,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC9B,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAC/B,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YAChC,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC;YAC7B,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;SACjC;QACD,IAAI,EAAE;YACF,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YACZ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YACZ,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACV,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACV,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACb,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YACf,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAChB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAChB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAChB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAChB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAChB,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACd,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;YACf,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YAChB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;YACf,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;YACjB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;SACxC;KACJ,CAAC;IAEF,MAAM,IAAI,GAAW,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,IAAI,OAAO,CAAC,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;IACpH,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAC1B,MAAM,KAAK,GAAW,OAAO,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC;IACjD,MAAM,KAAK,GAAW,OAAO,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC;IACjD,MAAM,KAAK,GAAW,OAAO,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC;IACjD,MAAM,IAAI,GAA+E,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;IAC3H,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;IACjC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IACpD,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IACtC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;IAC9D,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,IAAI,UAAU,CAAC,WAAW,CAAC;IAE9G,MAAM,SAAS,GAAa,EAAE,CAAC;IAC/B,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,MAAM,GAAG,GAAa,EAAE,CAAC;IACzB,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,qCAAqC;IACtD,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAS,EAAE,CAAS,EAAE,GAAW,EAAE,CAAS,EAAE,CAAS,EAAE,GAAW,CAAC;IAEzE,0CAA0C;IAC1C,IAAI,IAAI,EAAE;QACN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC3C,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAC1C;YACD,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBACnC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACvC;SACJ;KACJ;IAED,IAAI,CAAC,IAAI,EAAE;QACP,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;YAChG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACzE;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;YAC1B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1C,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;aAC3E;SACJ;KACJ;SAAM;QACH,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;YAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,yCAAyC;YACzE,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;YACzB,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAC5B,CAAC,GAAG,GAAG,CAAC;YAER,yBAAyB;YACzB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;gBACrB,YAAY;gBACZ,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;gBAC1I,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpB,KAAK,EAAE,CAAC;gBACR,MAAM;gBACN,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC1D,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBAC1D,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1E,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC5C,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC1C,CAAC,GAAG,GAAG,CAAC;gBACR,SAAS;gBACT,IAAI,UAAU,EAAE;oBACZ,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBACnF;aACJ;YAED,uBAAuB;YACvB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACzB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;aAC1F;YACD,OAAO,IAAI,EAAE,CAAC;SACjB;KACJ;IAED,UAAU,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACvD,UAAU,CAAC,aAAa,CAAC,eAAe,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IAE/G,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IACpC,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;IACjC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;IACrB,IAAI,UAAU,IAAI,IAAI,EAAE;QACpB,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;KAC9B;IACD,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,UAAU,gBAAgB,CAC5B,IAAY,EACZ,UAcI,EAAE,EACN,QAAyB,IAAI;IAE7B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAEzC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACnF,UAAU,CAAC,+BAA+B,GAAG,OAAO,CAAC,eAAe,CAAC;IAErE,MAAM,UAAU,GAAG,0BAA0B,CAAC,OAAO,CAAC,CAAC;IAEvD,UAAU,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAEtD,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG;IAC7B,gEAAgE;IAChE,gBAAgB;CACnB,CAAC;AAEF,UAAU,CAAC,gBAAgB,GAAG,0BAA0B,CAAC;AAEzD,IAAI,CAAC,gBAAgB,GAAG,CACpB,IAAY,EACZ,OAWC,EACD,KAAY,EACR,EAAE;IACN,OAAO,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAClD,CAAC,CAAC", "sourcesContent": ["import type { Scene } from \"../../scene\";\r\nimport { Vector4 } from \"../../Maths/math.vector\";\r\nimport { Color4 } from \"../../Maths/math.color\";\r\nimport { Mesh } from \"../mesh\";\r\nimport { VertexData } from \"../mesh.vertexData\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { CompatibilityOptions } from \"../../Compat/compatibilityOptions\";\r\n\r\n// inspired from // http://stemkoski.github.io/Three.js/Polyhedra.html\r\n/**\r\n * Creates the VertexData for a Polyhedron\r\n * @param options an object used to set the following optional parameters for the polyhedron, required but can be empty\r\n * * type provided types are:\r\n *  * 0 : Tetrahedron, 1 : Octahedron, 2 : Dodecahedron, 3 : Icosahedron, 4 : Rhombicuboctahedron, 5 : Triangular Prism, 6 : Pentagonal Prism, 7 : Hexagonal Prism, 8 : Square Pyramid (J1)\r\n *  * 9 : Pentagonal Pyramid (J2), 10 : Triangular Dipyramid (J12), 11 : Pentagonal Dipyramid (J13), 12 : Elongated Square Dipyramid (J15), 13 : Elongated Pentagonal Dipyramid (J16), 14 : Elongated Pentagonal Cupola (J20)\r\n * * size the size of the IcoSphere, optional default 1\r\n * * sizeX allows stretching in the x direction, optional, default size\r\n * * sizeY allows stretching in the y direction, optional, default size\r\n * * sizeZ allows stretching in the z direction, optional, default size\r\n * * custom a number that overwrites the type to create from an extended set of polyhedron from https://www.babylonjs-playground.com/#21QRSK#15 with minimised editor\r\n * * faceUV an array of Vector4 elements used to set different images to the top, rings and bottom respectively\r\n * * faceColors an array of Color3 elements used to set different colors to the top, rings and bottom respectively\r\n * * flat when true creates a flat shaded mesh, optional, default true\r\n * * subdivisions increasing the subdivisions increases the number of faces, optional, default 4\r\n * * sideOrientation optional and takes the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n * * frontUvs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the front side, optional, default vector4 (0, 0, 1, 1)\r\n * * backUVs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the back side, optional, default vector4 (0, 0, 1, 1)\r\n * @returns the VertexData of the Polyhedron\r\n */\r\nexport function CreatePolyhedronVertexData(options: {\r\n    type?: number;\r\n    size?: number;\r\n    sizeX?: number;\r\n    sizeY?: number;\r\n    sizeZ?: number;\r\n    custom?: any;\r\n    faceUV?: Vector4[];\r\n    faceColors?: Color4[];\r\n    flat?: boolean;\r\n    sideOrientation?: number;\r\n    frontUVs?: Vector4;\r\n    backUVs?: Vector4;\r\n}): VertexData {\r\n    // provided polyhedron types :\r\n    // 0 : Tetrahedron, 1 : Octahedron, 2 : Dodecahedron, 3 : Icosahedron, 4 : Rhombicuboctahedron, 5 : Triangular Prism, 6 : Pentagonal Prism, 7 : Hexagonal Prism, 8 : Square Pyramid (J1)\r\n    // 9 : Pentagonal Pyramid (J2), 10 : Triangular Dipyramid (J12), 11 : Pentagonal Dipyramid (J13), 12 : Elongated Square Dipyramid (J15), 13 : Elongated Pentagonal Dipyramid (J16), 14 : Elongated Pentagonal Cupola (J20)\r\n    const polyhedra: { vertex: number[][]; face: number[][] }[] = [];\r\n    polyhedra[0] = {\r\n        vertex: [\r\n            [0, 0, 1.732051],\r\n            [1.632993, 0, -0.5773503],\r\n            [-0.8164966, 1.414214, -0.5773503],\r\n            [-0.8164966, -1.414214, -0.5773503],\r\n        ],\r\n        face: [\r\n            [0, 1, 2],\r\n            [0, 2, 3],\r\n            [0, 3, 1],\r\n            [1, 3, 2],\r\n        ],\r\n    };\r\n    polyhedra[1] = {\r\n        vertex: [\r\n            [0, 0, 1.414214],\r\n            [1.414214, 0, 0],\r\n            [0, 1.414214, 0],\r\n            [-1.414214, 0, 0],\r\n            [0, -1.414214, 0],\r\n            [0, 0, -1.414214],\r\n        ],\r\n        face: [\r\n            [0, 1, 2],\r\n            [0, 2, 3],\r\n            [0, 3, 4],\r\n            [0, 4, 1],\r\n            [1, 4, 5],\r\n            [1, 5, 2],\r\n            [2, 5, 3],\r\n            [3, 5, 4],\r\n        ],\r\n    };\r\n    polyhedra[2] = {\r\n        vertex: [\r\n            [0, 0, 1.070466],\r\n            [0.7136442, 0, 0.7978784],\r\n            [-0.3568221, 0.618034, 0.7978784],\r\n            [-0.3568221, -0.618034, 0.7978784],\r\n            [0.7978784, 0.618034, 0.3568221],\r\n            [0.7978784, -0.618034, 0.3568221],\r\n            [-0.9341724, 0.381966, 0.3568221],\r\n            [0.1362939, 1, 0.3568221],\r\n            [0.1362939, -1, 0.3568221],\r\n            [-0.9341724, -0.381966, 0.3568221],\r\n            [0.9341724, 0.381966, -0.3568221],\r\n            [0.9341724, -0.381966, -0.3568221],\r\n            [-0.7978784, 0.618034, -0.3568221],\r\n            [-0.1362939, 1, -0.3568221],\r\n            [-0.1362939, -1, -0.3568221],\r\n            [-0.7978784, -0.618034, -0.3568221],\r\n            [0.3568221, 0.618034, -0.7978784],\r\n            [0.3568221, -0.618034, -0.7978784],\r\n            [-0.7136442, 0, -0.7978784],\r\n            [0, 0, -1.070466],\r\n        ],\r\n        face: [\r\n            [0, 1, 4, 7, 2],\r\n            [0, 2, 6, 9, 3],\r\n            [0, 3, 8, 5, 1],\r\n            [1, 5, 11, 10, 4],\r\n            [2, 7, 13, 12, 6],\r\n            [3, 9, 15, 14, 8],\r\n            [4, 10, 16, 13, 7],\r\n            [5, 8, 14, 17, 11],\r\n            [6, 12, 18, 15, 9],\r\n            [10, 11, 17, 19, 16],\r\n            [12, 13, 16, 19, 18],\r\n            [14, 15, 18, 19, 17],\r\n        ],\r\n    };\r\n    polyhedra[3] = {\r\n        vertex: [\r\n            [0, 0, 1.175571],\r\n            [1.051462, 0, 0.5257311],\r\n            [0.3249197, 1, 0.5257311],\r\n            [-0.8506508, 0.618034, 0.5257311],\r\n            [-0.8506508, -0.618034, 0.5257311],\r\n            [0.3249197, -1, 0.5257311],\r\n            [0.8506508, 0.618034, -0.5257311],\r\n            [0.8506508, -0.618034, -0.5257311],\r\n            [-0.3249197, 1, -0.5257311],\r\n            [-1.051462, 0, -0.5257311],\r\n            [-0.3249197, -1, -0.5257311],\r\n            [0, 0, -1.175571],\r\n        ],\r\n        face: [\r\n            [0, 1, 2],\r\n            [0, 2, 3],\r\n            [0, 3, 4],\r\n            [0, 4, 5],\r\n            [0, 5, 1],\r\n            [1, 5, 7],\r\n            [1, 7, 6],\r\n            [1, 6, 2],\r\n            [2, 6, 8],\r\n            [2, 8, 3],\r\n            [3, 8, 9],\r\n            [3, 9, 4],\r\n            [4, 9, 10],\r\n            [4, 10, 5],\r\n            [5, 10, 7],\r\n            [6, 7, 11],\r\n            [6, 11, 8],\r\n            [7, 10, 11],\r\n            [8, 11, 9],\r\n            [9, 11, 10],\r\n        ],\r\n    };\r\n    polyhedra[4] = {\r\n        vertex: [\r\n            [0, 0, 1.070722],\r\n            [0.7148135, 0, 0.7971752],\r\n            [-0.104682, 0.7071068, 0.7971752],\r\n            [-0.6841528, 0.2071068, 0.7971752],\r\n            [-0.104682, -0.7071068, 0.7971752],\r\n            [0.6101315, 0.7071068, 0.5236279],\r\n            [1.04156, 0.2071068, 0.1367736],\r\n            [0.6101315, -0.7071068, 0.5236279],\r\n            [-0.3574067, 1, 0.1367736],\r\n            [-0.7888348, -0.5, 0.5236279],\r\n            [-0.9368776, 0.5, 0.1367736],\r\n            [-0.3574067, -1, 0.1367736],\r\n            [0.3574067, 1, -0.1367736],\r\n            [0.9368776, -0.5, -0.1367736],\r\n            [0.7888348, 0.5, -0.5236279],\r\n            [0.3574067, -1, -0.1367736],\r\n            [-0.6101315, 0.7071068, -0.5236279],\r\n            [-1.04156, -0.2071068, -0.1367736],\r\n            [-0.6101315, -0.7071068, -0.5236279],\r\n            [0.104682, 0.7071068, -0.7971752],\r\n            [0.6841528, -0.2071068, -0.7971752],\r\n            [0.104682, -0.7071068, -0.7971752],\r\n            [-0.7148135, 0, -0.7971752],\r\n            [0, 0, -1.070722],\r\n        ],\r\n        face: [\r\n            [0, 2, 3],\r\n            [1, 6, 5],\r\n            [4, 9, 11],\r\n            [7, 15, 13],\r\n            [8, 16, 10],\r\n            [12, 14, 19],\r\n            [17, 22, 18],\r\n            [20, 21, 23],\r\n            [0, 1, 5, 2],\r\n            [0, 3, 9, 4],\r\n            [0, 4, 7, 1],\r\n            [1, 7, 13, 6],\r\n            [2, 5, 12, 8],\r\n            [2, 8, 10, 3],\r\n            [3, 10, 17, 9],\r\n            [4, 11, 15, 7],\r\n            [5, 6, 14, 12],\r\n            [6, 13, 20, 14],\r\n            [8, 12, 19, 16],\r\n            [9, 17, 18, 11],\r\n            [10, 16, 22, 17],\r\n            [11, 18, 21, 15],\r\n            [13, 15, 21, 20],\r\n            [14, 20, 23, 19],\r\n            [16, 19, 23, 22],\r\n            [18, 22, 23, 21],\r\n        ],\r\n    };\r\n    polyhedra[5] = {\r\n        vertex: [\r\n            [0, 0, 1.322876],\r\n            [1.309307, 0, 0.1889822],\r\n            [-0.9819805, 0.8660254, 0.1889822],\r\n            [0.1636634, -1.299038, 0.1889822],\r\n            [0.3273268, 0.8660254, -0.9449112],\r\n            [-0.8183171, -0.4330127, -0.9449112],\r\n        ],\r\n        face: [\r\n            [0, 3, 1],\r\n            [2, 4, 5],\r\n            [0, 1, 4, 2],\r\n            [0, 2, 5, 3],\r\n            [1, 3, 5, 4],\r\n        ],\r\n    };\r\n    polyhedra[6] = {\r\n        vertex: [\r\n            [0, 0, 1.159953],\r\n            [1.013464, 0, 0.5642542],\r\n            [-0.3501431, 0.9510565, 0.5642542],\r\n            [-0.7715208, -0.6571639, 0.5642542],\r\n            [0.6633206, 0.9510565, -0.03144481],\r\n            [0.8682979, -0.6571639, -0.3996071],\r\n            [-1.121664, 0.2938926, -0.03144481],\r\n            [-0.2348831, -1.063314, -0.3996071],\r\n            [0.5181548, 0.2938926, -0.9953061],\r\n            [-0.5850262, -0.112257, -0.9953061],\r\n        ],\r\n        face: [\r\n            [0, 1, 4, 2],\r\n            [0, 2, 6, 3],\r\n            [1, 5, 8, 4],\r\n            [3, 6, 9, 7],\r\n            [5, 7, 9, 8],\r\n            [0, 3, 7, 5, 1],\r\n            [2, 4, 8, 9, 6],\r\n        ],\r\n    };\r\n    polyhedra[7] = {\r\n        vertex: [\r\n            [0, 0, 1.118034],\r\n            [0.8944272, 0, 0.6708204],\r\n            [-0.2236068, 0.8660254, 0.6708204],\r\n            [-0.7826238, -0.4330127, 0.6708204],\r\n            [0.6708204, 0.8660254, 0.2236068],\r\n            [1.006231, -0.4330127, -0.2236068],\r\n            [-1.006231, 0.4330127, 0.2236068],\r\n            [-0.6708204, -0.8660254, -0.2236068],\r\n            [0.7826238, 0.4330127, -0.6708204],\r\n            [0.2236068, -0.8660254, -0.6708204],\r\n            [-0.8944272, 0, -0.6708204],\r\n            [0, 0, -1.118034],\r\n        ],\r\n        face: [\r\n            [0, 1, 4, 2],\r\n            [0, 2, 6, 3],\r\n            [1, 5, 8, 4],\r\n            [3, 6, 10, 7],\r\n            [5, 9, 11, 8],\r\n            [7, 10, 11, 9],\r\n            [0, 3, 7, 9, 5, 1],\r\n            [2, 4, 8, 11, 10, 6],\r\n        ],\r\n    };\r\n    polyhedra[8] = {\r\n        vertex: [\r\n            [-0.729665, 0.670121, 0.319155],\r\n            [-0.655235, -0.29213, -0.754096],\r\n            [-0.093922, -0.607123, 0.537818],\r\n            [0.702196, 0.595691, 0.485187],\r\n            [0.776626, -0.36656, -0.588064],\r\n        ],\r\n        face: [\r\n            [1, 4, 2],\r\n            [0, 1, 2],\r\n            [3, 0, 2],\r\n            [4, 3, 2],\r\n            [4, 1, 0, 3],\r\n        ],\r\n    };\r\n    polyhedra[9] = {\r\n        vertex: [\r\n            [-0.868849, -0.100041, 0.61257],\r\n            [-0.329458, 0.976099, 0.28078],\r\n            [-0.26629, -0.013796, -0.477654],\r\n            [-0.13392, -1.034115, 0.229829],\r\n            [0.738834, 0.707117, -0.307018],\r\n            [0.859683, -0.535264, -0.338508],\r\n        ],\r\n        face: [\r\n            [3, 0, 2],\r\n            [5, 3, 2],\r\n            [4, 5, 2],\r\n            [1, 4, 2],\r\n            [0, 1, 2],\r\n            [0, 3, 5, 4, 1],\r\n        ],\r\n    };\r\n    polyhedra[10] = {\r\n        vertex: [\r\n            [-0.610389, 0.243975, 0.531213],\r\n            [-0.187812, -0.48795, -0.664016],\r\n            [-0.187812, 0.9759, -0.664016],\r\n            [0.187812, -0.9759, 0.664016],\r\n            [0.798201, 0.243975, 0.132803],\r\n        ],\r\n        face: [\r\n            [1, 3, 0],\r\n            [3, 4, 0],\r\n            [3, 1, 4],\r\n            [0, 2, 1],\r\n            [0, 4, 2],\r\n            [2, 4, 1],\r\n        ],\r\n    };\r\n    polyhedra[11] = {\r\n        vertex: [\r\n            [-1.028778, 0.392027, -0.048786],\r\n            [-0.640503, -0.646161, 0.621837],\r\n            [-0.125162, -0.395663, -0.540059],\r\n            [0.004683, 0.888447, -0.651988],\r\n            [0.125161, 0.395663, 0.540059],\r\n            [0.632925, -0.791376, 0.433102],\r\n            [1.031672, 0.157063, -0.354165],\r\n        ],\r\n        face: [\r\n            [3, 2, 0],\r\n            [2, 1, 0],\r\n            [2, 5, 1],\r\n            [0, 4, 3],\r\n            [0, 1, 4],\r\n            [4, 1, 5],\r\n            [2, 3, 6],\r\n            [3, 4, 6],\r\n            [5, 2, 6],\r\n            [4, 5, 6],\r\n        ],\r\n    };\r\n    polyhedra[12] = {\r\n        vertex: [\r\n            [-0.669867, 0.334933, -0.529576],\r\n            [-0.669867, 0.334933, 0.529577],\r\n            [-0.4043, 1.212901, 0],\r\n            [-0.334933, -0.669867, -0.529576],\r\n            [-0.334933, -0.669867, 0.529577],\r\n            [0.334933, 0.669867, -0.529576],\r\n            [0.334933, 0.669867, 0.529577],\r\n            [0.4043, -1.212901, 0],\r\n            [0.669867, -0.334933, -0.529576],\r\n            [0.669867, -0.334933, 0.529577],\r\n        ],\r\n        face: [\r\n            [8, 9, 7],\r\n            [6, 5, 2],\r\n            [3, 8, 7],\r\n            [5, 0, 2],\r\n            [4, 3, 7],\r\n            [0, 1, 2],\r\n            [9, 4, 7],\r\n            [1, 6, 2],\r\n            [9, 8, 5, 6],\r\n            [8, 3, 0, 5],\r\n            [3, 4, 1, 0],\r\n            [4, 9, 6, 1],\r\n        ],\r\n    };\r\n    polyhedra[13] = {\r\n        vertex: [\r\n            [-0.931836, 0.219976, -0.264632],\r\n            [-0.636706, 0.318353, 0.692816],\r\n            [-0.613483, -0.735083, -0.264632],\r\n            [-0.326545, 0.979634, 0],\r\n            [-0.318353, -0.636706, 0.692816],\r\n            [-0.159176, 0.477529, -0.856368],\r\n            [0.159176, -0.477529, -0.856368],\r\n            [0.318353, 0.636706, 0.692816],\r\n            [0.326545, -0.979634, 0],\r\n            [0.613482, 0.735082, -0.264632],\r\n            [0.636706, -0.318353, 0.692816],\r\n            [0.931835, -0.219977, -0.264632],\r\n        ],\r\n        face: [\r\n            [11, 10, 8],\r\n            [7, 9, 3],\r\n            [6, 11, 8],\r\n            [9, 5, 3],\r\n            [2, 6, 8],\r\n            [5, 0, 3],\r\n            [4, 2, 8],\r\n            [0, 1, 3],\r\n            [10, 4, 8],\r\n            [1, 7, 3],\r\n            [10, 11, 9, 7],\r\n            [11, 6, 5, 9],\r\n            [6, 2, 0, 5],\r\n            [2, 4, 1, 0],\r\n            [4, 10, 7, 1],\r\n        ],\r\n    };\r\n    polyhedra[14] = {\r\n        vertex: [\r\n            [-0.93465, 0.300459, -0.271185],\r\n            [-0.838689, -0.260219, -0.516017],\r\n            [-0.711319, 0.717591, 0.128359],\r\n            [-0.710334, -0.156922, 0.080946],\r\n            [-0.599799, 0.556003, -0.725148],\r\n            [-0.503838, -0.004675, -0.969981],\r\n            [-0.487004, 0.26021, 0.48049],\r\n            [-0.460089, -0.750282, -0.512622],\r\n            [-0.376468, 0.973135, -0.325605],\r\n            [-0.331735, -0.646985, 0.084342],\r\n            [-0.254001, 0.831847, 0.530001],\r\n            [-0.125239, -0.494738, -0.966586],\r\n            [0.029622, 0.027949, 0.730817],\r\n            [0.056536, -0.982543, -0.262295],\r\n            [0.08085, 1.087391, 0.076037],\r\n            [0.125583, -0.532729, 0.485984],\r\n            [0.262625, 0.599586, 0.780328],\r\n            [0.391387, -0.726999, -0.716259],\r\n            [0.513854, -0.868287, 0.139347],\r\n            [0.597475, 0.85513, 0.326364],\r\n            [0.641224, 0.109523, 0.783723],\r\n            [0.737185, -0.451155, 0.538891],\r\n            [0.848705, -0.612742, -0.314616],\r\n            [0.976075, 0.365067, 0.32976],\r\n            [1.072036, -0.19561, 0.084927],\r\n        ],\r\n        face: [\r\n            [15, 18, 21],\r\n            [12, 20, 16],\r\n            [6, 10, 2],\r\n            [3, 0, 1],\r\n            [9, 7, 13],\r\n            [2, 8, 4, 0],\r\n            [0, 4, 5, 1],\r\n            [1, 5, 11, 7],\r\n            [7, 11, 17, 13],\r\n            [13, 17, 22, 18],\r\n            [18, 22, 24, 21],\r\n            [21, 24, 23, 20],\r\n            [20, 23, 19, 16],\r\n            [16, 19, 14, 10],\r\n            [10, 14, 8, 2],\r\n            [15, 9, 13, 18],\r\n            [12, 15, 21, 20],\r\n            [6, 12, 16, 10],\r\n            [3, 6, 2, 0],\r\n            [9, 3, 1, 7],\r\n            [9, 15, 12, 6, 3],\r\n            [22, 17, 11, 5, 4, 8, 14, 19, 23, 24],\r\n        ],\r\n    };\r\n\r\n    const type: number = options.type && (options.type < 0 || options.type >= polyhedra.length) ? 0 : options.type || 0;\r\n    const size = options.size;\r\n    const sizeX: number = options.sizeX || size || 1;\r\n    const sizeY: number = options.sizeY || size || 1;\r\n    const sizeZ: number = options.sizeZ || size || 1;\r\n    const data: { vertex: number[][]; face: number[][]; name?: string; category?: string } = options.custom || polyhedra[type];\r\n    const nbfaces = data.face.length;\r\n    const faceUV = options.faceUV || new Array(nbfaces);\r\n    const faceColors = options.faceColors;\r\n    const flat = options.flat === undefined ? true : options.flat;\r\n    const sideOrientation = options.sideOrientation === 0 ? 0 : options.sideOrientation || VertexData.DEFAULTSIDE;\r\n\r\n    const positions: number[] = [];\r\n    const indices: number[] = [];\r\n    const normals: number[] = [];\r\n    const uvs: number[] = [];\r\n    const colors: number[] = [];\r\n    let index = 0;\r\n    let faceIdx = 0; // face cursor in the array \"indexes\"\r\n    const indexes: number[] = [];\r\n    let i = 0;\r\n    let f = 0;\r\n    let u: number, v: number, ang: number, x: number, y: number, tmp: number;\r\n\r\n    // default face colors and UV if undefined\r\n    if (flat) {\r\n        for (f = 0; f < nbfaces; f++) {\r\n            if (faceColors && faceColors[f] === undefined) {\r\n                faceColors[f] = new Color4(1, 1, 1, 1);\r\n            }\r\n            if (faceUV && faceUV[f] === undefined) {\r\n                faceUV[f] = new Vector4(0, 0, 1, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    if (!flat) {\r\n        for (i = 0; i < data.vertex.length; i++) {\r\n            positions.push(data.vertex[i][0] * sizeX, data.vertex[i][1] * sizeY, data.vertex[i][2] * sizeZ);\r\n            uvs.push(0, CompatibilityOptions.UseOpenGLOrientationForUV ? 1.0 : 0);\r\n        }\r\n        for (f = 0; f < nbfaces; f++) {\r\n            for (i = 0; i < data.face[f].length - 2; i++) {\r\n                indices.push(data.face[f][0], data.face[f][i + 2], data.face[f][i + 1]);\r\n            }\r\n        }\r\n    } else {\r\n        for (f = 0; f < nbfaces; f++) {\r\n            const fl = data.face[f].length; // number of vertices of the current face\r\n            ang = (2 * Math.PI) / fl;\r\n            x = 0.5 * Math.tan(ang / 2);\r\n            y = 0.5;\r\n\r\n            // positions, uvs, colors\r\n            for (i = 0; i < fl; i++) {\r\n                // positions\r\n                positions.push(data.vertex[data.face[f][i]][0] * sizeX, data.vertex[data.face[f][i]][1] * sizeY, data.vertex[data.face[f][i]][2] * sizeZ);\r\n                indexes.push(index);\r\n                index++;\r\n                // uvs\r\n                u = faceUV[f].x + (faceUV[f].z - faceUV[f].x) * (0.5 + x);\r\n                v = faceUV[f].y + (faceUV[f].w - faceUV[f].y) * (y - 0.5);\r\n                uvs.push(u, CompatibilityOptions.UseOpenGLOrientationForUV ? 1.0 - v : v);\r\n                tmp = x * Math.cos(ang) - y * Math.sin(ang);\r\n                y = x * Math.sin(ang) + y * Math.cos(ang);\r\n                x = tmp;\r\n                // colors\r\n                if (faceColors) {\r\n                    colors.push(faceColors[f].r, faceColors[f].g, faceColors[f].b, faceColors[f].a);\r\n                }\r\n            }\r\n\r\n            // indices from indexes\r\n            for (i = 0; i < fl - 2; i++) {\r\n                indices.push(indexes[0 + faceIdx], indexes[i + 2 + faceIdx], indexes[i + 1 + faceIdx]);\r\n            }\r\n            faceIdx += fl;\r\n        }\r\n    }\r\n\r\n    VertexData.ComputeNormals(positions, indices, normals);\r\n    VertexData._ComputeSides(sideOrientation, positions, indices, normals, uvs, options.frontUVs, options.backUVs);\r\n\r\n    const vertexData = new VertexData();\r\n    vertexData.positions = positions;\r\n    vertexData.indices = indices;\r\n    vertexData.normals = normals;\r\n    vertexData.uvs = uvs;\r\n    if (faceColors && flat) {\r\n        vertexData.colors = colors;\r\n    }\r\n    return vertexData;\r\n}\r\n\r\n/**\r\n * Creates a polyhedron mesh\r\n * * The parameter `type` (positive integer, max 14, default 0) sets the polyhedron type to build among the 15 embbeded types. Please refer to the type sheet in the tutorial to choose the wanted type\r\n * * The parameter `size` (positive float, default 1) sets the polygon size\r\n * * You can overwrite the `size` on each dimension bu using the parameters `sizeX`, `sizeY` or `sizeZ` (positive floats, default to `size` value)\r\n * * You can build other polyhedron types than the 15 embbeded ones by setting the parameter `custom` (`polyhedronObject`, default null). If you set the parameter `custom`, this overrides the parameter `type`\r\n * * A `polyhedronObject` is a formatted javascript object. You'll find a full file with pre-set polyhedra here : https://github.com/BabylonJS/Extensions/tree/master/Polyhedron\r\n * * You can set the color and the UV of each side of the polyhedron with the parameters `faceColors` (Color4, default `(1, 1, 1, 1)`) and faceUV (Vector4, default `(0, 0, 1, 1)`)\r\n * * To understand how to set `faceUV` or `faceColors`, please read this by considering the right number of faces of your polyhedron, instead of only 6 for the box : https://doc.babylonjs.com/features/featuresDeepDive/materials/using/texturePerBoxFace\r\n * * The parameter `flat` (boolean, default true). If set to false, it gives the polyhedron a single global face, so less vertices and shared normals. In this case, `faceColors` and `faceUV` are ignored\r\n * * You can also set the mesh side orientation with the values : BABYLON.Mesh.FRONTSIDE (default), BABYLON.Mesh.BACKSIDE or BABYLON.Mesh.DOUBLESIDE\r\n * * If you create a double-sided mesh, you can choose what parts of the texture image to crop and stick respectively on the front and the back sides with the parameters `frontUVs` and `backUVs` (Vector4). Detail here : https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation\r\n * * The mesh can be set to updatable with the boolean parameter `updatable` (default false) if its internal geometry is supposed to change once created\r\n * @param name defines the name of the mesh\r\n * @param options defines the options used to create the mesh\r\n * @param scene defines the hosting scene\r\n * @returns the polyhedron mesh\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/polyhedra\r\n */\r\nexport function CreatePolyhedron(\r\n    name: string,\r\n    options: {\r\n        type?: number;\r\n        size?: number;\r\n        sizeX?: number;\r\n        sizeY?: number;\r\n        sizeZ?: number;\r\n        custom?: any;\r\n        faceUV?: Vector4[];\r\n        faceColors?: Color4[];\r\n        flat?: boolean;\r\n        updatable?: boolean;\r\n        sideOrientation?: number;\r\n        frontUVs?: Vector4;\r\n        backUVs?: Vector4;\r\n    } = {},\r\n    scene: Nullable<Scene> = null\r\n): Mesh {\r\n    const polyhedron = new Mesh(name, scene);\r\n\r\n    options.sideOrientation = Mesh._GetDefaultSideOrientation(options.sideOrientation);\r\n    polyhedron._originalBuilderSideOrientation = options.sideOrientation;\r\n\r\n    const vertexData = CreatePolyhedronVertexData(options);\r\n\r\n    vertexData.applyToMesh(polyhedron, options.updatable);\r\n\r\n    return polyhedron;\r\n}\r\n\r\n/**\r\n * Class containing static functions to help procedurally build meshes\r\n * @deprecated use the function directly from the module\r\n */\r\nexport const PolyhedronBuilder = {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    CreatePolyhedron,\r\n};\r\n\r\nVertexData.CreatePolyhedron = CreatePolyhedronVertexData;\r\n\r\nMesh.CreatePolyhedron = (\r\n    name: string,\r\n    options: {\r\n        type?: number;\r\n        size?: number;\r\n        sizeX?: number;\r\n        sizeY?: number;\r\n        sizeZ?: number;\r\n        custom?: any;\r\n        faceUV?: Vector4[];\r\n        faceColors?: Color4[];\r\n        updatable?: boolean;\r\n        sideOrientation?: number;\r\n    },\r\n    scene: Scene\r\n): Mesh => {\r\n    return CreatePolyhedron(name, options, scene);\r\n};\r\n"]}