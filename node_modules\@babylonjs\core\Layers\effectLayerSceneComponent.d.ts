import type { Scene } from "../scene";
import type { ISceneSerializableComponent } from "../sceneComponent";
import { EffectLayer } from "./effectLayer";
import { AbstractScene } from "../abstractScene";
declare module "../abstractScene" {
    interface AbstractScene {
        /**
         * The list of effect layers (highlights/glow) added to the scene
         * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/highlightLayer
         * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/glowLayer
         */
        effectLayers: Array<EffectLayer>;
        /**
         * Removes the given effect layer from this scene.
         * @param toRemove defines the effect layer to remove
         * @returns the index of the removed effect layer
         */
        removeEffectLayer(toRemove: EffectLayer): number;
        /**
         * Adds the given effect layer to this scene
         * @param newEffectLayer defines the effect layer to add
         */
        addEffectLayer(newEffectLayer: EffectLayer): void;
    }
}
/**
 * Defines the layer scene component responsible to manage any effect layers
 * in a given scene.
 */
export declare class EffectLayerSceneComponent implements ISceneSerializableComponent {
    /**
     * The component name helpful to identify the component in the list of scene components.
     */
    readonly name = "EffectLayer";
    /**
     * The scene the component belongs to.
     */
    scene: Scene;
    private _engine;
    private _renderEffects;
    private _needStencil;
    private _previousStencilState;
    /**
     * Creates a new instance of the component for the given scene
     * @param scene Defines the scene to register the component in
     */
    constructor(scene?: Scene);
    /**
     * Registers the component in a given scene
     */
    register(): void;
    /**
     * Rebuilds the elements related to this component in case of
     * context lost for instance.
     */
    rebuild(): void;
    /**
     * Serializes the component data to the specified json object
     * @param serializationObject The object to serialize to
     */
    serialize(serializationObject: any): void;
    /**
     * Adds all the elements from the container to the scene
     * @param container the container holding the elements
     */
    addFromContainer(container: AbstractScene): void;
    /**
     * Removes all the elements in the container from the scene
     * @param container contains the elements to remove
     * @param dispose if the removed element should be disposed (default: false)
     */
    removeFromContainer(container: AbstractScene, dispose?: boolean): void;
    /**
     * Disposes the component and the associated resources.
     */
    dispose(): void;
    private _isReadyForMesh;
    private _renderMainTexture;
    private _setStencil;
    private _setStencilBack;
    private _draw;
    private _drawCamera;
    private _drawRenderingGroup;
}
