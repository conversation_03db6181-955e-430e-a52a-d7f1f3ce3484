{"version": 3, "file": "proximityCastResult.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Physics/proximityCastResult.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD;;GAEG;AACH,MAAM,OAAO,mBAAoB,SAAQ,aAAa;IAAtD;;QACc,iBAAY,GAAW,CAAC,CAAC;IAwBvC,CAAC;IAtBG;;OAEG;IACH,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,QAAgB;QAClC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,KAAK;QACR,KAAK,CAAC,KAAK,EAAE,CAAC;QACd,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;IAC1B,CAAC;CACJ", "sourcesContent": ["import { CastingResult } from \"./castingResult\";\r\n\r\n/**\r\n * Class representing a contact point produced in a proximity cast\r\n */\r\nexport class ProximityCastResult extends CastingResult {\r\n    protected _hitDistance: number = 0;\r\n\r\n    /**\r\n     * Gets the distance from the hit\r\n     */\r\n    get hitDistance(): number {\r\n        return this._hitDistance;\r\n    }\r\n\r\n    /**\r\n     * Sets the distance from the start point to the hit point\r\n     * @param distance\r\n     */\r\n    public setHitDistance(distance: number) {\r\n        this._hitDistance = distance;\r\n    }\r\n\r\n    /**\r\n     * Resets all the values to default\r\n     */\r\n    public reset() {\r\n        super.reset();\r\n        this._hitDistance = 0;\r\n    }\r\n}\r\n"]}