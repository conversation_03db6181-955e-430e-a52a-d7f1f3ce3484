{"version": 3, "file": "pointerEvents.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Events/pointerEvents.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAO/C;;GAEG;AACH,MAAM,OAAO,iBAAiB;;AAC1B;;GAEG;AACoB,6BAAW,GAAG,IAAI,CAAC;AAC1C;;GAEG;AACoB,2BAAS,GAAG,IAAI,CAAC;AACxC;;GAEG;AACoB,6BAAW,GAAG,IAAI,CAAC;AAC1C;;GAEG;AACoB,8BAAY,GAAG,IAAI,CAAC;AAC3C;;GAEG;AACoB,6BAAW,GAAG,IAAI,CAAC;AAC1C;;GAEG;AACoB,4BAAU,GAAG,IAAI,CAAC;AACzC;;GAEG;AACoB,kCAAgB,GAAG,IAAI,CAAC;AAGnD;;GAEG;AACH,MAAM,OAAO,eAAe;IACxB;;;;OAIG;IACH;IACI;;OAEG;IACI,IAAY;IACnB;;OAEG;IACI,KAAkB;QAJlB,SAAI,GAAJ,IAAI,CAAQ;QAIZ,UAAK,GAAL,KAAK,CAAa;IAC1B,CAAC;CACP;AAED;;;GAGG;AACH,MAAM,OAAO,cAAe,SAAQ,eAAe;IA0B/C;;;;;;OAMG;IACH,YAAY,IAAY,EAAE,KAAkB,EAAE,MAAc,EAAE,MAAc;QACxE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAjCvB;;WAEG;QACI,QAAG,GAAkB,IAAI,CAAC;QAOjC;;WAEG;QACI,wBAAmB,GAA0B,IAAI,CAAC;QAqBrD,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,IAAI,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,WAAY,SAAQ,eAAe;IAI5C;;OAEG;IACH,IAAW,QAAQ;QACf,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IACD;;;;;;OAMG;IACH,YAAY,IAAY,EAAE,KAAkB,EAAE,QAA+B,EAAE,eAAuC,IAAI;QACtH,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,gBAAgB;IACT,iBAAiB;QACpB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,KAAsB,CAAC,CAAC;YAC3E,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACpE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B;IACL,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Vector2 } from \"../Maths/math.vector\";\r\nimport type { PickingInfo } from \"../Collisions/pickingInfo\";\r\nimport type { IMouseEvent, IPointerEvent } from \"./deviceInputEvents\";\r\nimport type { InputManager } from \"../Inputs/scene.inputManager\";\r\n\r\nimport type { Ray } from \"../Culling/ray\";\r\n\r\n/**\r\n * Gather the list of pointer event types as constants.\r\n */\r\nexport class PointerEventTypes {\r\n    /**\r\n     * The pointerdown event is fired when a pointer becomes active. For mouse, it is fired when the device transitions from no buttons depressed to at least one button depressed. For touch, it is fired when physical contact is made with the digitizer. For pen, it is fired when the stylus makes physical contact with the digitizer.\r\n     */\r\n    public static readonly POINTERDOWN = 0x01;\r\n    /**\r\n     * The pointerup event is fired when a pointer is no longer active.\r\n     */\r\n    public static readonly POINTERUP = 0x02;\r\n    /**\r\n     * The pointermove event is fired when a pointer changes coordinates.\r\n     */\r\n    public static readonly POINTERMOVE = 0x04;\r\n    /**\r\n     * The pointerwheel event is fired when a mouse wheel has been rotated.\r\n     */\r\n    public static readonly POINTERWHEEL = 0x08;\r\n    /**\r\n     * The pointerpick event is fired when a mesh or sprite has been picked by the pointer.\r\n     */\r\n    public static readonly POINTERPICK = 0x10;\r\n    /**\r\n     * The pointertap event is fired when a the object has been touched and released without drag.\r\n     */\r\n    public static readonly POINTERTAP = 0x20;\r\n    /**\r\n     * The pointerdoubletap event is fired when a the object has been touched and released twice without drag.\r\n     */\r\n    public static readonly POINTERDOUBLETAP = 0x40;\r\n}\r\n\r\n/**\r\n * Base class of pointer info types.\r\n */\r\nexport class PointerInfoBase {\r\n    /**\r\n     * Instantiates the base class of pointers info.\r\n     * @param type Defines the type of event (PointerEventTypes)\r\n     * @param event Defines the related dom event\r\n     */\r\n    constructor(\r\n        /**\r\n         * Defines the type of event (PointerEventTypes)\r\n         */\r\n        public type: number,\r\n        /**\r\n         * Defines the related dom event\r\n         */\r\n        public event: IMouseEvent\r\n    ) {}\r\n}\r\n\r\n/**\r\n * This class is used to store pointer related info for the onPrePointerObservable event.\r\n * Set the skipOnPointerObservable property to true if you want the engine to stop any process after this event is triggered, even not calling onPointerObservable\r\n */\r\nexport class PointerInfoPre extends PointerInfoBase {\r\n    /**\r\n     * Ray from a pointer if available (eg. 6dof controller)\r\n     */\r\n    public ray: Nullable<Ray> = null;\r\n\r\n    /**\r\n     * Defines picking info coming from a near interaction (proximity instead of ray-based picking)\r\n     */\r\n    public nearInteractionPickingInfo: Nullable<PickingInfo>;\r\n\r\n    /**\r\n     * The original picking info that was used to trigger the pointer event\r\n     */\r\n    public originalPickingInfo: Nullable<PickingInfo> = null;\r\n\r\n    /**\r\n     * Defines the local position of the pointer on the canvas.\r\n     */\r\n    public localPosition: Vector2;\r\n\r\n    /**\r\n     * Defines whether the engine should skip the next OnPointerObservable associated to this pre.\r\n     */\r\n    public skipOnPointerObservable: boolean;\r\n\r\n    /**\r\n     * Instantiates a PointerInfoPre to store pointer related info to the onPrePointerObservable event.\r\n     * @param type Defines the type of event (PointerEventTypes)\r\n     * @param event Defines the related dom event\r\n     * @param localX Defines the local x coordinates of the pointer when the event occured\r\n     * @param localY Defines the local y coordinates of the pointer when the event occured\r\n     */\r\n    constructor(type: number, event: IMouseEvent, localX: number, localY: number) {\r\n        super(type, event);\r\n        this.skipOnPointerObservable = false;\r\n        this.localPosition = new Vector2(localX, localY);\r\n    }\r\n}\r\n\r\n/**\r\n * This type contains all the data related to a pointer event in Babylon.js.\r\n * The event member is an instance of PointerEvent for all types except PointerWheel and is of type MouseWheelEvent when type equals PointerWheel. The different event types can be found in the PointerEventTypes class.\r\n */\r\nexport class PointerInfo extends PointerInfoBase {\r\n    private _pickInfo: Nullable<PickingInfo>;\r\n    private _inputManager: Nullable<InputManager>;\r\n\r\n    /**\r\n     * Defines the picking info associated with this PointerInfo object (if applicable)\r\n     */\r\n    public get pickInfo(): Nullable<PickingInfo> {\r\n        if (!this._pickInfo) {\r\n            this._generatePickInfo();\r\n        }\r\n\r\n        return this._pickInfo;\r\n    }\r\n    /**\r\n     * Instantiates a PointerInfo to store pointer related info to the onPointerObservable event.\r\n     * @param type Defines the type of event (PointerEventTypes)\r\n     * @param event Defines the related dom event\r\n     * @param pickInfo Defines the picking info associated to the info (if any)\r\n     * @param inputManager Defines the InputManager to use if there is no pickInfo\r\n     */\r\n    constructor(type: number, event: IMouseEvent, pickInfo: Nullable<PickingInfo>, inputManager: Nullable<InputManager> = null) {\r\n        super(type, event);\r\n        this._pickInfo = pickInfo;\r\n        this._inputManager = inputManager;\r\n    }\r\n\r\n    /**\r\n     * Generates the picking info if needed\r\n     */\r\n    /** @internal */\r\n    public _generatePickInfo(): void {\r\n        if (this._inputManager) {\r\n            this._pickInfo = this._inputManager._pickMove(this.event as IPointerEvent);\r\n            this._inputManager._setRayOnPointerInfo(this._pickInfo, this.event);\r\n            this._inputManager = null;\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * Data relating to a touch event on the screen.\r\n */\r\nexport interface PointerTouch {\r\n    /**\r\n     * X coordinate of touch.\r\n     */\r\n    x: number;\r\n    /**\r\n     * Y coordinate of touch.\r\n     */\r\n    y: number;\r\n    /**\r\n     * Id of touch. Unique for each finger.\r\n     */\r\n    pointerId: number;\r\n    /**\r\n     * Event type passed from DOM.\r\n     */\r\n    type: any;\r\n}\r\n"]}