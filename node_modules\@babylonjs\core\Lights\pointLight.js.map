{"version": 3, "file": "pointLight.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Lights/pointLight.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAE/C,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAG5C,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACpD,OAAO,GAAG,EAAE,CAAC,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC;AAEH;;;;;GAKG;AACH,MAAM,OAAO,UAAW,SAAQ,WAAW;IAEvC;;;;;OAKG;IAEH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IACD;;;;;OAKG;IACH,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACxC,CAAC;IAED;;;OAGG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS,CAAC,KAAc;QAC/B,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,gBAAgB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAChE,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;YACjD,KAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE;gBACtE,MAAM,eAAe,GAAG,GAAG,CAAC,KAAK,CAAC;gBAClC,eAAe,CAAC,iBAAiB,EAAE,CAAC;aACvC;SACJ;IACL,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa;QACtD,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QA3Df,iBAAY,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QA4D/B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,KAAK,CAAC,sBAAsB,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,SAAkB;QACxC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;SAC9C;aAAM;YACH,QAAQ,SAAS,EAAE;gBACf,KAAK,CAAC;oBACF,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACtC,KAAK,CAAC;oBACF,OAAO,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACvC,KAAK,CAAC;oBACF,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBACvC,KAAK,CAAC;oBACF,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACtC,KAAK,CAAC;oBACF,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACtC,KAAK,CAAC;oBACF,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;aAC1C;SACJ;QAED,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;;;;;OASG;IACH,6DAA6D;IACnD,iCAAiC,CAAC,MAAc,EAAE,UAAkB,EAAE,UAA+B;QAC3G,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC;QAElD,IAAI,CAAC,YAAY,EAAE;YACf,OAAO;SACV;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;QACjF,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;QAEjF,MAAM,qBAAqB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,qBAAqB,CAAC;QAEhF,MAAM,CAAC,qBAAqB,CACxB,IAAI,CAAC,WAAW,EAChB,GAAG,EACH,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACnC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACnC,MAAM,EACN,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,EACvC,SAAS,EACT,qBAAqB,CACxB,CAAC;IACN,CAAC;IAES,mBAAmB;QACzB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;IACjC,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,MAAc,EAAE,UAAkB;QACtD,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE;YACtC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;SACvJ;aAAM;YACH,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;SACpH;QAED,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;QAC3G,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,4BAA4B,CAAC,MAAc,EAAE,oBAA4B;QAC5E,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE;YACtC,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;SAC9H;aAAM;YACH,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SAC7F;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,2BAA2B,CAAC,OAAY,EAAE,UAAkB;QAC/D,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;IAC9C,CAAC;CACJ;AA5LG;IADC,SAAS,EAAE;6CAGX", "sourcesContent": ["import { serialize } from \"../Misc/decorators\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Matrix, Vector3 } from \"../Maths/math.vector\";\r\nimport { Node } from \"../node\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Light } from \"./light\";\r\nimport { ShadowLight } from \"./shadowLight\";\r\nimport type { Effect } from \"../Materials/effect\";\r\n\r\nNode.AddNodeConstructor(\"Light_Type_0\", (name, scene) => {\r\n    return () => new PointLight(name, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * A point light is a light defined by an unique point in world space.\r\n * The light is emitted in every direction from this point.\r\n * A good example of a point light is a standard light bulb.\r\n * Documentation: https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n */\r\nexport class PointLight extends ShadowLight {\r\n    private _shadowAngle = Math.PI / 2;\r\n    /**\r\n     * Getter: In case of direction provided, the shadow will not use a cube texture but simulate a spot shadow as a fallback\r\n     * This specifies what angle the shadow will use to be created.\r\n     *\r\n     * It default to 90 degrees to work nicely with the cube texture generation for point lights shadow maps.\r\n     */\r\n    @serialize()\r\n    public get shadowAngle(): number {\r\n        return this._shadowAngle;\r\n    }\r\n    /**\r\n     * Setter: In case of direction provided, the shadow will not use a cube texture but simulate a spot shadow as a fallback\r\n     * This specifies what angle the shadow will use to be created.\r\n     *\r\n     * It default to 90 degrees to work nicely with the cube texture generation for point lights shadow maps.\r\n     */\r\n    public set shadowAngle(value: number) {\r\n        this._shadowAngle = value;\r\n        this.forceProjectionMatrixCompute();\r\n    }\r\n\r\n    /**\r\n     * Gets the direction if it has been set.\r\n     * In case of direction provided, the shadow will not use a cube texture but simulate a spot shadow as a fallback\r\n     */\r\n    public get direction(): Vector3 {\r\n        return this._direction;\r\n    }\r\n\r\n    /**\r\n     * In case of direction provided, the shadow will not use a cube texture but simulate a spot shadow as a fallback\r\n     */\r\n    public set direction(value: Vector3) {\r\n        const previousNeedCube = this.needCube();\r\n        this._direction = value;\r\n        if (this.needCube() !== previousNeedCube && this._shadowGenerators) {\r\n            const iterator = this._shadowGenerators.values();\r\n            for (let key = iterator.next(); key.done !== true; key = iterator.next()) {\r\n                const shadowGenerator = key.value;\r\n                shadowGenerator.recreateShadowMap();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Creates a PointLight object from the passed name and position (Vector3) and adds it in the scene.\r\n     * A PointLight emits the light in every direction.\r\n     * It can cast shadows.\r\n     * If the scene camera is already defined and you want to set your PointLight at the camera position, just set it :\r\n     * ```javascript\r\n     * var pointLight = new PointLight(\"pl\", camera.position, scene);\r\n     * ```\r\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n     * @param name The light friendly name\r\n     * @param position The position of the point light in the scene\r\n     * @param scene The scene the lights belongs to\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene) {\r\n        super(name, scene);\r\n        this.position = position;\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"PointLight\"\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"PointLight\";\r\n    }\r\n\r\n    /**\r\n     * Returns the integer 0.\r\n     * @returns The light Type id as a constant defines in Light.LIGHTTYPEID_x\r\n     */\r\n    public getTypeID(): number {\r\n        return Light.LIGHTTYPEID_POINTLIGHT;\r\n    }\r\n\r\n    /**\r\n     * Specifies whether or not the shadowmap should be a cube texture.\r\n     * @returns true if the shadowmap needs to be a cube texture.\r\n     */\r\n    public needCube(): boolean {\r\n        return !this.direction;\r\n    }\r\n\r\n    /**\r\n     * Returns a new Vector3 aligned with the PointLight cube system according to the passed cube face index (integer).\r\n     * @param faceIndex The index of the face we are computed the direction to generate shadow\r\n     * @returns The set direction in 2d mode otherwise the direction to the cubemap face if needCube() is true\r\n     */\r\n    public getShadowDirection(faceIndex?: number): Vector3 {\r\n        if (this.direction) {\r\n            return super.getShadowDirection(faceIndex);\r\n        } else {\r\n            switch (faceIndex) {\r\n                case 0:\r\n                    return new Vector3(1.0, 0.0, 0.0);\r\n                case 1:\r\n                    return new Vector3(-1.0, 0.0, 0.0);\r\n                case 2:\r\n                    return new Vector3(0.0, -1.0, 0.0);\r\n                case 3:\r\n                    return new Vector3(0.0, 1.0, 0.0);\r\n                case 4:\r\n                    return new Vector3(0.0, 0.0, 1.0);\r\n                case 5:\r\n                    return new Vector3(0.0, 0.0, -1.0);\r\n            }\r\n        }\r\n\r\n        return Vector3.Zero();\r\n    }\r\n\r\n    /**\r\n     * Sets the passed matrix \"matrix\" as a left-handed perspective projection matrix with the following settings :\r\n     * - fov = PI / 2\r\n     * - aspect ratio : 1.0\r\n     * - z-near and far equal to the active camera minZ and maxZ.\r\n     * Returns the PointLight.\r\n     * @param matrix\r\n     * @param viewMatrix\r\n     * @param renderList\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _setDefaultShadowProjectionMatrix(matrix: Matrix, viewMatrix: Matrix, renderList: Array<AbstractMesh>): void {\r\n        const activeCamera = this.getScene().activeCamera;\r\n\r\n        if (!activeCamera) {\r\n            return;\r\n        }\r\n\r\n        const minZ = this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera.minZ;\r\n        const maxZ = this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera.maxZ;\r\n\r\n        const useReverseDepthBuffer = this.getScene().getEngine().useReverseDepthBuffer;\r\n\r\n        Matrix.PerspectiveFovLHToRef(\r\n            this.shadowAngle,\r\n            1.0,\r\n            useReverseDepthBuffer ? maxZ : minZ,\r\n            useReverseDepthBuffer ? minZ : maxZ,\r\n            matrix,\r\n            true,\r\n            this._scene.getEngine().isNDCHalfZRange,\r\n            undefined,\r\n            useReverseDepthBuffer\r\n        );\r\n    }\r\n\r\n    protected _buildUniformLayout(): void {\r\n        this._uniformBuffer.addUniform(\"vLightData\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightDiffuse\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightSpecular\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightFalloff\", 4);\r\n        this._uniformBuffer.addUniform(\"shadowsInfo\", 3);\r\n        this._uniformBuffer.addUniform(\"depthValues\", 2);\r\n        this._uniformBuffer.create();\r\n    }\r\n\r\n    /**\r\n     * Sets the passed Effect \"effect\" with the PointLight transformed position (or position, if none) and passed name (string).\r\n     * @param effect The effect to update\r\n     * @param lightIndex The index of the light in the effect to update\r\n     * @returns The point light\r\n     */\r\n    public transferToEffect(effect: Effect, lightIndex: string): PointLight {\r\n        if (this.computeTransformedInformation()) {\r\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.transformedPosition.x, this.transformedPosition.y, this.transformedPosition.z, 0.0, lightIndex);\r\n        } else {\r\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.position.x, this.position.y, this.position.z, 0, lightIndex);\r\n        }\r\n\r\n        this._uniformBuffer.updateFloat4(\"vLightFalloff\", this.range, this._inverseSquaredRange, 0, 0, lightIndex);\r\n        return this;\r\n    }\r\n\r\n    public transferToNodeMaterialEffect(effect: Effect, lightDataUniformName: string) {\r\n        if (this.computeTransformedInformation()) {\r\n            effect.setFloat3(lightDataUniformName, this.transformedPosition.x, this.transformedPosition.y, this.transformedPosition.z);\r\n        } else {\r\n            effect.setFloat3(lightDataUniformName, this.position.x, this.position.y, this.position.z);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Prepares the list of defines specific to the light type.\r\n     * @param defines the list of defines\r\n     * @param lightIndex defines the index of the light for the effect\r\n     */\r\n    public prepareLightSpecificDefines(defines: any, lightIndex: number): void {\r\n        defines[\"POINTLIGHT\" + lightIndex] = true;\r\n    }\r\n}\r\n"]}