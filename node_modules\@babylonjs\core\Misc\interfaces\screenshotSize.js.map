{"version": 3, "file": "screenshotSize.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Misc/interfaces/screenshotSize.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\r\n * Interface for screenshot methods with describe argument called `size` as object with options\r\n * @link https://doc.babylonjs.com/api/classes/babylon.screenshottools\r\n */\r\nexport interface IScreenshotSize {\r\n    /**\r\n     * number in pixels for canvas height. It is the height of the texture used to render the scene\r\n     */\r\n    height?: number;\r\n\r\n    /**\r\n     * multiplier allowing render at a higher or lower resolution\r\n     * If value is defined then width and height will be multiplied by this value\r\n     */\r\n    precision?: number;\r\n\r\n    /**\r\n     * number in pixels for canvas width. It is the width of the texture used to render the scene\r\n     */\r\n    width?: number;\r\n\r\n    /**\r\n     * Width of the final screenshot image.\r\n     * If only one of the two values is provided, the other will be calculated based on the camera's aspect ratio.\r\n     * If both finalWidth and finalHeight are not provided, width and height will be used instead.\r\n     * finalWidth and finalHeight are used only by CreateScreenshotUsingRenderTarget, not by CreateScreenshot!\r\n     */\r\n    finalWidth?: number;\r\n\r\n    /**\r\n     * Height of the final screenshot image.\r\n     * If only one of the two values is provided, the other will be calculated based on the camera's aspect ratio.\r\n     * If both finalWidth and finalHeight are not provided, width and height will be used instead\r\n     * finalWidth and finalHeight are used only by CreateScreenshotUsingRenderTarget, not by CreateScreenshot!\r\n     */\r\n    finalHeight?: number;\r\n}\r\n"]}