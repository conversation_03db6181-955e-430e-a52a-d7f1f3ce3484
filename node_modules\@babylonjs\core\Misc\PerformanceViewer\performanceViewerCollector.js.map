{"version": 3, "file": "performanceViewerCollector.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Misc/PerformanceViewer/performanceViewerCollector.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAG5D,8DAA8D;AAC9D,MAAM,gBAAgB,GAAG,IAAI,CAAC;AAE9B,kEAAkE;AAClE,MAAM,qBAAqB,GAAG,EAAE,CAAC;AAEjC,2DAA2D;AAC3D,MAAM,UAAU,GAAG,GAAG,CAAC;AAEvB,kCAAkC;AAClC,MAAM,kBAAkB,GAAG,WAAW,CAAC;AAEvC,kCAAkC;AAClC,MAAM,kBAAkB,GAAG,WAAW,CAAC;AAEvC,uDAAuD;AACvD,MAAM,mBAAmB,GAAG,KAAK,CAAC;AAElC,wFAAwF;AACxF,MAAM,qBAAqB,GAAG,GAAG,CAAC;AAoBlC;;;GAGG;AACH,MAAM,OAAO,0BAA0B;IAuBnC;;OAEG;IACI,MAAM,KAAK,eAAe;QAC7B,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,oBAAoB;QAClC,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;OAIG;IACH,YACY,MAAa,EACrB,yBAAiE;QADzD,WAAM,GAAN,MAAM,CAAO;QAuJzB;;;WAGG;QACK,wBAAmB,GAAG,GAAG,EAAE;YAC/B,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC;YAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;YAE3C,uCAAuC;YACvC,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,UAAU,CAAC;YACjE,IAAI,aAAa,GAAG,CAAC,CAAC;YAEtB,IAAI,eAAe,GAAG,CAAC,EAAE;gBACrB,MAAM,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;gBACpF,aAAa;oBACT,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,qBAAqB,GAAG,0BAA0B,CAAC,oBAAoB,CAAC,GAAG,0BAA0B,CAAC,eAAe,CAAC;aAC3K;YAED,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAElD,sCAAsC;YACtC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACnC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEnC,mCAAmC;YACnC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAU,EAAE,EAAE;gBACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAE1C,IAAI,CAAC,QAAQ,EAAE;oBACX,OAAO;iBACV;gBAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,EAAE;gBACvC,MAAM,KAAK,GAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;oBAChC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,GAAG,0BAA0B,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;iBACrG;gBAED,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;aACjD;QACL,CAAC,CAAC;QAhME,IAAI,CAAC,QAAQ,GAAG;YACZ,GAAG,EAAE,EAAE;YACP,IAAI,EAAE,IAAI,mBAAmB,CAAC,gBAAgB,CAAC;YAC/C,eAAe,EAAE,IAAI,mBAAmB,CAAC,gBAAgB,CAAC;SAC7D,CAAC;QACF,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAyC,CAAC;QACpE,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAyB,CAAC;QACrD,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,sBAAsB,GAAG,IAAI,UAAU,EAAE,CAAC;QAC/C,IAAI,CAAC,iBAAiB,GAAG,IAAI,UAAU,EAAE,CAAC;QAC1C,IAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChH,IAAI,yBAAyB,EAAE;YAC3B,IAAI,CAAC,uBAAuB,CAAC,GAAG,yBAAyB,CAAC,CAAC;SAC9D;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,aAAa,CAAC,IAAY,EAAE,WAAqB,EAAE,QAAiB;QACvE,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YAC5C,OAAO;SACV;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,WAAW,EAAE;YAC3C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC;YACtC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACjC;QAED,MAAM,QAAQ,GAA+B,CAAC,KAAK,EAAE,EAAE;YACnD,IAAI,OAAO,GAAW,CAAC,CAAC;YACxB,IAAI,KAAK,GAAW,CAAC,CAAC;YAEtB,MAAM,mBAAmB,GAAG,KAAK,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC/D,KAAK,GAAG,OAAO,CAAC;gBAChB,OAAO,GAAG,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAChE,IAAI,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE;oBACxB,OAAO;iBACV;gBAED,IAAI,QAAQ,CAAC,KAAK,KAAK,SAAS,EAAE;oBAC9B,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC;iBAC5B;qBAAM;oBACH,OAAO,EAAE,CAAC;iBACb;YACL,CAAC,CAAC,CAAC;YAEH,OAAO;gBACH,EAAE,EAAE,IAAI;gBACR,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK;gBACpB,OAAO,EAAE,GAAG,EAAE;oBACV,KAAK,CAAC,uBAAuB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;oBAC1D,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;gBACvD,CAAC;aACJ,CAAC;QACN,CAAC,CAAC;QACF,MAAM,KAAK,GAAqB;YAC5B,IAAI;SACP,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,uBAAuB,CAAC,EAAE,gBAAgB,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;QAEvE,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,KAAuB;QACpC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACK,oBAAoB;QACxB,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,KAAK,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,MAAM,EAAE;YAC7E,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBACpC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,GAAG,iBAAwD;QACtF,wCAAwC;QACxC,KAAK,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,iBAAiB,EAAE;YAClE,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/C,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;gBACnC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACnB,SAAS;aACZ;YAED,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEpC,IAAI,QAAQ,EAAE;gBACV,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,qBAAqB,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;aAC3E;YAED,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE;gBAC/B,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC3C,QAAQ;gBACR,MAAM;aACT,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;SAC/C;QAED,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/D,CAAC;IAED;;;;OAIG;IACK,kBAAkB,CAAC,EAAU;QACjC,0DAA0D;QAC1D,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChC,8CAA8C;YAC9C,IAAI,GAAG,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;SAClD;QAED,2CAA2C;QAC3C,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,qBAAqB,EAAE,CAAC,IAAI,CAAC,EAAE;YAC/C,MAAM,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;YACjC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACvD;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAgDD;;;;OAIG;IACI,eAAe;QAClB,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;QAC3C,MAAM,KAAK,GAAa,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAE/C,mCAAmC;QACnC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAU,EAAE,EAAE;YACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAE1C,IAAI,CAAC,QAAQ,EAAE;gBACX,OAAO;aACV;YAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,EAAE;gBACvC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;aAClC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,EAAE;YACvC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SACjD;IACL,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAgC,EAAU,EAAE,IAAO,EAAE,KAAuB;QAC7F,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEvC,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAEnB,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC/D,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,2BAAqC;QAC9C,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAC/D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,IAAI,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAC1E,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAEzB,IAAI,CAAC,2BAA2B,EAAE;YAC9B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;SACjC;QACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,gBAAgB,CAAC,IAAY,EAAE,eAAyB;QAC3D,MAAM,KAAK,GAAG,IAAI;aACb,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC;aAChC,KAAK,CAAC,IAAI,CAAC;aACX,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;aAC1D,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACvC,MAAM,cAAc,GAAG,CAAC,CAAC;QACzB,MAAM,cAAc,GAAG,0BAA0B,CAAC,oBAAoB,CAAC;QACvE,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YAClB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,cAAc,GAAkB;YAClC,GAAG,EAAE,EAAE;YACP,IAAI,EAAE,IAAI,mBAAmB,CAAC,gBAAgB,CAAC;YAC/C,eAAe,EAAE,IAAI,mBAAmB,CAAC,gBAAgB,CAAC;SAC7D,CAAC;QAEF,+CAA+C;QAC/C,MAAM,CAAC,SAAS,EAAE,GAAG,SAAS,CAAC,GAAG,KAAK,CAAC;QACxC,kDAAkD;QAClD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,cAAc,CAAC,KAAK,kBAAkB,IAAI,SAAS,CAAC,cAAc,CAAC,KAAK,kBAAkB,EAAE;YAC9H,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,aAAa,GAAwB,IAAI,GAAG,EAAkB,CAAC;QAErE,oBAAoB;QACpB,KAAK,IAAI,CAAC,GAAG,0BAA0B,CAAC,eAAe,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChF,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACjE,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5B,aAAa,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;SACnC;QAED,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;YAC1B,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjB,OAAO,KAAK,CAAC;aAChB;YAED,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YACnD,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YAEjD,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,EAAE;gBACtC,OAAO,KAAK,CAAC;aAChB;YAED,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACpC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEpC,IAAI,SAAS,GAAG,0BAA0B,CAAC,eAAe,KAAK,IAAI,CAAC,MAAM,EAAE;gBACxE,OAAO,KAAK,CAAC;aAChB;YAED,KAAK,IAAI,CAAC,GAAG,0BAA0B,CAAC,eAAe,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3E,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChC,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;oBACZ,OAAO,KAAK,CAAC;iBAChB;gBACD,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACjC;YAED,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACnD,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC;SAChC;QAED,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC;QACzC,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;QAC/D,IAAI,CAAC,eAAe,EAAE;YAClB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;SAC7B;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAEzB,qBAAqB;QACrB,IAAI,CAAC,eAAe,EAAE;YAClB,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;gBAChC,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAEvC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;aAC/E;SACJ;QACD,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,0BAA0B;QAC1B,UAAU,IAAI,GAAG,kBAAkB,IAAI,kBAAkB,EAAE,CAAC;QAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/C,UAAU,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzD,IAAI,IAAI,EAAE,QAAQ,EAAE;oBAChB,UAAU,IAAI,GAAG,qBAAqB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;iBAC5D;aACJ;SACJ;QACD,UAAU,IAAI,IAAI,CAAC;QACnB,wBAAwB;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YAC/D,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;YACvD,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,GAAG,0BAA0B,CAAC,oBAAoB,CAAC,CAAC;YAEzG,UAAU,IAAI,GAAG,SAAS,IAAI,SAAS,EAAE,CAAC;YAE1C,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,EAAE,MAAM,EAAE,EAAE;gBAC/C,UAAU,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,GAAG,0BAA0B,CAAC,eAAe,GAAG,MAAM,CAAC,EAAE,CAAC;aAClH;YAED,oBAAoB;YACpB,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,GAAG,SAAS,EAAE,IAAI,EAAE,EAAE;gBACpE,UAAU,IAAI,GAAG,CAAC;aACrB;YAED,UAAU,IAAI,IAAI,CAAC;SACtB;QAED,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC;QAC5D,KAAK,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;IAC3E,CAAC;IACD;;;OAGG;IACI,KAAK,CAAC,cAAwB;QACjC,IAAI,CAAC,cAAc,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;YAC/D,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,IAAI,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;YAC1E,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,GAAG,CAAC;SAC/C;aAAM,IAAI,IAAI,CAAC,kBAAkB,KAAK,SAAS,EAAE;YAC9C,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,GAAG,CAAC;SAC/C;QACD,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAClE,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,IAAI;QACP,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC7E,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC7E,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAClC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAChC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,QAAS,GAAG,IAAI,CAAC;IAChC,CAAC;CACJ", "sourcesContent": ["import type { Scene } from \"../../scene\";\r\nimport type { IPerfCustomEvent, IPerfDatasets, IPerfMetadata } from \"../interfaces/iPerfViewer\";\r\nimport { EventState, Observable } from \"../observable\";\r\nimport { PrecisionDate } from \"../precisionDate\";\r\nimport { Tools } from \"../tools\";\r\nimport { DynamicFloat32Array } from \"./dynamicFloat32Array\";\r\nimport type { IPerfViewerCollectionStrategy, PerfStrategyInitialization } from \"./performanceViewerCollectionStrategies\";\r\n\r\n// the initial size of our array, should be a multiple of two!\r\nconst InitialArraySize = 1800;\r\n\r\n// three octets in a hexcode. #[AA][BB][CC], i.e. 24 bits of data.\r\nconst NumberOfBitsInHexcode = 24;\r\n\r\n// Allows single numeral hex numbers to be appended by a 0.\r\nconst HexPadding = \"0\";\r\n\r\n// header for the timestamp column\r\nconst TimestampColHeader = \"timestamp\";\r\n\r\n// header for the numPoints column\r\nconst NumPointsColHeader = \"numPoints\";\r\n\r\n// regex to capture all carriage returns in the string.\r\nconst CarriageReturnRegex = /\\r/g;\r\n\r\n// string to use as separator when exporting extra information along with the dataset id\r\nconst ExportedDataSeparator = \"@\";\r\n\r\n/**\r\n * Callback strategy and optional category for data collection\r\n */\r\ninterface IPerformanceViewerStrategyParameter {\r\n    /**\r\n     * The strategy for collecting data. Available strategies are located on the PerfCollectionStrategy class\r\n     */\r\n    strategyCallback: PerfStrategyInitialization;\r\n    /**\r\n     * Category for displaying this strategy on the viewer. Can be undefined or an empty string, in which case the strategy will be displayed on top\r\n     */\r\n    category?: string;\r\n    /**\r\n     * Starts hidden\r\n     */\r\n    hidden?: boolean;\r\n}\r\n\r\n/**\r\n * The collector class handles the collection and storage of data into the appropriate array.\r\n * The collector also handles notifying any observers of any updates.\r\n */\r\nexport class PerformanceViewerCollector {\r\n    private _datasetMeta: Map<string, IPerfMetadata>;\r\n    private _strategies: Map<string, IPerfViewerCollectionStrategy>;\r\n    private _startingTimestamp: number;\r\n    private _hasLoadedData: boolean;\r\n    private _isStarted: boolean;\r\n    private readonly _customEventObservable: Observable<IPerfCustomEvent>;\r\n    private readonly _eventRestoreSet: Set<string>;\r\n\r\n    /**\r\n     * Datastructure containing the collected datasets. Warning: you should not modify the values in here, data will be of the form [timestamp, numberOfPoints, value1, value2..., timestamp, etc...]\r\n     */\r\n    public readonly datasets: IPerfDatasets;\r\n    /**\r\n     * An observable you can attach to get deltas in the dataset. Subscribing to this will increase memory consumption slightly, and may hurt performance due to increased garbage collection needed.\r\n     * Updates of slices will be of the form [timestamp, numberOfPoints, value1, value2...].\r\n     */\r\n    public readonly datasetObservable: Observable<number[]>;\r\n    /**\r\n     * An observable you can attach to get the most updated map of metadatas.\r\n     */\r\n    public readonly metadataObservable: Observable<Map<string, IPerfMetadata>>;\r\n\r\n    /**\r\n     * The offset for when actual data values start appearing inside a slice.\r\n     */\r\n    public static get SliceDataOffset() {\r\n        return 2;\r\n    }\r\n\r\n    /**\r\n     * The offset for the value of the number of points inside a slice.\r\n     */\r\n    public static get NumberOfPointsOffset() {\r\n        return 1;\r\n    }\r\n\r\n    /**\r\n     * Handles the creation of a performance viewer collector.\r\n     * @param _scene the scene to collect on.\r\n     * @param _enabledStrategyCallbacks the list of data to collect with callbacks for initialization purposes.\r\n     */\r\n    constructor(\r\n        private _scene: Scene,\r\n        _enabledStrategyCallbacks?: IPerformanceViewerStrategyParameter[]\r\n    ) {\r\n        this.datasets = {\r\n            ids: [],\r\n            data: new DynamicFloat32Array(InitialArraySize),\r\n            startingIndices: new DynamicFloat32Array(InitialArraySize),\r\n        };\r\n        this._strategies = new Map<string, IPerfViewerCollectionStrategy>();\r\n        this._datasetMeta = new Map<string, IPerfMetadata>();\r\n        this._eventRestoreSet = new Set();\r\n        this._customEventObservable = new Observable();\r\n        this.datasetObservable = new Observable();\r\n        this.metadataObservable = new Observable((observer) => observer.callback(this._datasetMeta, new EventState(0)));\r\n        if (_enabledStrategyCallbacks) {\r\n            this.addCollectionStrategies(..._enabledStrategyCallbacks);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Registers a custom string event which will be callable via sendEvent. This method returns an event object which will contain the id of the event.\r\n     * The user can set a value optionally, which will be used in the sendEvent method. If the value is set, we will record this value at the end of each frame,\r\n     * if not we will increment our counter and record the value of the counter at the end of each frame. The value recorded is 0 if no sendEvent method is called, within a frame.\r\n     * @param name The name of the event to register\r\n     * @param forceUpdate if the code should force add an event, and replace the last one.\r\n     * @param category the category for that event\r\n     * @returns The event registered, used in sendEvent\r\n     */\r\n    public registerEvent(name: string, forceUpdate?: boolean, category?: string): IPerfCustomEvent | undefined {\r\n        if (this._strategies.has(name) && !forceUpdate) {\r\n            return;\r\n        }\r\n\r\n        if (this._strategies.has(name) && forceUpdate) {\r\n            this._strategies.get(name)?.dispose();\r\n            this._strategies.delete(name);\r\n        }\r\n\r\n        const strategy: PerfStrategyInitialization = (scene) => {\r\n            let counter: number = 0;\r\n            let value: number = 0;\r\n\r\n            const afterRenderObserver = scene.onAfterRenderObservable.add(() => {\r\n                value = counter;\r\n                counter = 0;\r\n            });\r\n\r\n            const stringObserver = this._customEventObservable.add((eventVal) => {\r\n                if (name !== eventVal.name) {\r\n                    return;\r\n                }\r\n\r\n                if (eventVal.value !== undefined) {\r\n                    counter = eventVal.value;\r\n                } else {\r\n                    counter++;\r\n                }\r\n            });\r\n\r\n            return {\r\n                id: name,\r\n                getData: () => value,\r\n                dispose: () => {\r\n                    scene.onAfterRenderObservable.remove(afterRenderObserver);\r\n                    this._customEventObservable.remove(stringObserver);\r\n                },\r\n            };\r\n        };\r\n        const event: IPerfCustomEvent = {\r\n            name,\r\n        };\r\n\r\n        this._eventRestoreSet.add(name);\r\n        this.addCollectionStrategies({ strategyCallback: strategy, category });\r\n\r\n        return event;\r\n    }\r\n\r\n    /**\r\n     * Lets the perf collector handle an event, occurences or event value depending on if the event.value params is set.\r\n     * @param event the event to handle an occurence for\r\n     */\r\n    public sendEvent(event: IPerfCustomEvent) {\r\n        this._customEventObservable.notifyObservers(event);\r\n    }\r\n\r\n    /**\r\n     * This event restores all custom string events if necessary.\r\n     */\r\n    private _restoreStringEvents() {\r\n        if (this._eventRestoreSet.size !== this._customEventObservable.observers.length) {\r\n            this._eventRestoreSet.forEach((event) => {\r\n                this.registerEvent(event, true);\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * This method adds additional collection strategies for data collection purposes.\r\n     * @param strategyCallbacks the list of data to collect with callbacks.\r\n     */\r\n    public addCollectionStrategies(...strategyCallbacks: IPerformanceViewerStrategyParameter[]) {\r\n        // eslint-disable-next-line prefer-const\r\n        for (let { strategyCallback, category, hidden } of strategyCallbacks) {\r\n            const strategy = strategyCallback(this._scene);\r\n            if (this._strategies.has(strategy.id)) {\r\n                strategy.dispose();\r\n                continue;\r\n            }\r\n\r\n            this.datasets.ids.push(strategy.id);\r\n\r\n            if (category) {\r\n                category = category.replace(new RegExp(ExportedDataSeparator, \"g\"), \"\");\r\n            }\r\n\r\n            this._datasetMeta.set(strategy.id, {\r\n                color: this._getHexColorFromId(strategy.id),\r\n                category,\r\n                hidden,\r\n            });\r\n\r\n            this._strategies.set(strategy.id, strategy);\r\n        }\r\n\r\n        this.metadataObservable.notifyObservers(this._datasetMeta);\r\n    }\r\n\r\n    /**\r\n     * Gets a 6 character hexcode representing the colour from a passed in string.\r\n     * @param id the string to get a hex code for.\r\n     * @returns a hexcode hashed from the id.\r\n     */\r\n    private _getHexColorFromId(id: string) {\r\n        // this first bit is just a known way of hashing a string.\r\n        let hash = 0;\r\n        for (let i = 0; i < id.length; i++) {\r\n            // (hash << 5) - hash is the same as hash * 31\r\n            hash = id.charCodeAt(i) + ((hash << 5) - hash);\r\n        }\r\n\r\n        // then we build the string octet by octet.\r\n        let hex = \"#\";\r\n        for (let i = 0; i < NumberOfBitsInHexcode; i += 8) {\r\n            const octet = (hash >> i) & 0xff;\r\n            hex += (HexPadding + octet.toString(16)).substr(-2);\r\n        }\r\n\r\n        return hex;\r\n    }\r\n\r\n    /**\r\n     * Collects data for every dataset by using the appropriate strategy. This is called every frame.\r\n     * This method will then notify all observers with the latest slice.\r\n     */\r\n    private _collectDataAtFrame = () => {\r\n        const timestamp = PrecisionDate.Now - this._startingTimestamp;\r\n        const numPoints = this.datasets.ids.length;\r\n\r\n        // add the starting index for the slice\r\n        const numberOfIndices = this.datasets.startingIndices.itemLength;\r\n        let startingIndex = 0;\r\n\r\n        if (numberOfIndices > 0) {\r\n            const previousStartingIndex = this.datasets.startingIndices.at(numberOfIndices - 1);\r\n            startingIndex =\r\n                previousStartingIndex + this.datasets.data.at(previousStartingIndex + PerformanceViewerCollector.NumberOfPointsOffset) + PerformanceViewerCollector.SliceDataOffset;\r\n        }\r\n\r\n        this.datasets.startingIndices.push(startingIndex);\r\n\r\n        // add the first 2 items in our slice.\r\n        this.datasets.data.push(timestamp);\r\n        this.datasets.data.push(numPoints);\r\n\r\n        // add the values inside the slice.\r\n        this.datasets.ids.forEach((id: string) => {\r\n            const strategy = this._strategies.get(id);\r\n\r\n            if (!strategy) {\r\n                return;\r\n            }\r\n\r\n            this.datasets.data.push(strategy.getData());\r\n        });\r\n\r\n        if (this.datasetObservable.hasObservers()) {\r\n            const slice: number[] = [timestamp, numPoints];\r\n\r\n            for (let i = 0; i < numPoints; i++) {\r\n                slice.push(this.datasets.data.at(startingIndex + PerformanceViewerCollector.SliceDataOffset + i));\r\n            }\r\n\r\n            this.datasetObservable.notifyObservers(slice);\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Collects and then sends the latest slice to any observers by using the appropriate strategy when the user wants.\r\n     * The slice will be of the form [timestamp, numberOfPoints, value1, value2...]\r\n     * This method does not add onto the collected data accessible via the datasets variable.\r\n     */\r\n    public getCurrentSlice() {\r\n        const timestamp = PrecisionDate.Now - this._startingTimestamp;\r\n        const numPoints = this.datasets.ids.length;\r\n        const slice: number[] = [timestamp, numPoints];\r\n\r\n        // add the values inside the slice.\r\n        this.datasets.ids.forEach((id: string) => {\r\n            const strategy = this._strategies.get(id);\r\n\r\n            if (!strategy) {\r\n                return;\r\n            }\r\n\r\n            if (this.datasetObservable.hasObservers()) {\r\n                slice.push(strategy.getData());\r\n            }\r\n        });\r\n\r\n        if (this.datasetObservable.hasObservers()) {\r\n            this.datasetObservable.notifyObservers(slice);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates a property for a dataset's metadata with the value provided.\r\n     * @param id the id of the dataset which needs its metadata updated.\r\n     * @param prop the property to update.\r\n     * @param value the value to update the property with.\r\n     */\r\n    public updateMetadata<T extends keyof IPerfMetadata>(id: string, prop: T, value: IPerfMetadata[T]) {\r\n        const meta = this._datasetMeta.get(id);\r\n\r\n        if (!meta) {\r\n            return;\r\n        }\r\n\r\n        meta[prop] = value;\r\n\r\n        this.metadataObservable.notifyObservers(this._datasetMeta);\r\n    }\r\n\r\n    /**\r\n     * Completely clear, data, ids, and strategies saved to this performance collector.\r\n     * @param preserveStringEventsRestore if it should preserve the string events, by default will clear string events registered when called.\r\n     */\r\n    public clear(preserveStringEventsRestore?: boolean) {\r\n        this.datasets.data = new DynamicFloat32Array(InitialArraySize);\r\n        this.datasets.ids.length = 0;\r\n        this.datasets.startingIndices = new DynamicFloat32Array(InitialArraySize);\r\n        this._datasetMeta.clear();\r\n        this._strategies.forEach((strategy) => strategy.dispose());\r\n        this._strategies.clear();\r\n\r\n        if (!preserveStringEventsRestore) {\r\n            this._eventRestoreSet.clear();\r\n        }\r\n        this._hasLoadedData = false;\r\n    }\r\n\r\n    /**\r\n     * Accessor which lets the caller know if the performance collector has data loaded from a file or not!\r\n     * Call clear() to reset this value.\r\n     * @returns true if the data is loaded from a file, false otherwise.\r\n     */\r\n    public get hasLoadedData(): boolean {\r\n        return this._hasLoadedData;\r\n    }\r\n\r\n    /**\r\n     * Given a string containing file data, this function parses the file data into the datasets object.\r\n     * It returns a boolean to indicate if this object was successfully loaded with the data.\r\n     * @param data string content representing the file data.\r\n     * @param keepDatasetMeta if it should use reuse the existing dataset metadata\r\n     * @returns true if the data was successfully loaded, false otherwise.\r\n     */\r\n    public loadFromFileData(data: string, keepDatasetMeta?: boolean): boolean {\r\n        const lines = data\r\n            .replace(CarriageReturnRegex, \"\")\r\n            .split(\"\\n\")\r\n            .map((line) => line.split(\",\").filter((s) => s.length > 0))\r\n            .filter((line) => line.length > 0);\r\n        const timestampIndex = 0;\r\n        const numPointsIndex = PerformanceViewerCollector.NumberOfPointsOffset;\r\n        if (lines.length < 2) {\r\n            return false;\r\n        }\r\n\r\n        const parsedDatasets: IPerfDatasets = {\r\n            ids: [],\r\n            data: new DynamicFloat32Array(InitialArraySize),\r\n            startingIndices: new DynamicFloat32Array(InitialArraySize),\r\n        };\r\n\r\n        // parse first line separately to populate ids!\r\n        const [firstLine, ...dataLines] = lines;\r\n        // make sure we have the correct beginning headers\r\n        if (firstLine.length < 2 || firstLine[timestampIndex] !== TimestampColHeader || firstLine[numPointsIndex] !== NumPointsColHeader) {\r\n            return false;\r\n        }\r\n\r\n        const idCategoryMap: Map<string, string> = new Map<string, string>();\r\n\r\n        // populate the ids.\r\n        for (let i = PerformanceViewerCollector.SliceDataOffset; i < firstLine.length; i++) {\r\n            const [id, category] = firstLine[i].split(ExportedDataSeparator);\r\n            parsedDatasets.ids.push(id);\r\n            idCategoryMap.set(id, category);\r\n        }\r\n\r\n        let startingIndex = 0;\r\n        for (const line of dataLines) {\r\n            if (line.length < 2) {\r\n                return false;\r\n            }\r\n\r\n            const timestamp = parseFloat(line[timestampIndex]);\r\n            const numPoints = parseInt(line[numPointsIndex]);\r\n\r\n            if (isNaN(numPoints) || isNaN(timestamp)) {\r\n                return false;\r\n            }\r\n\r\n            parsedDatasets.data.push(timestamp);\r\n            parsedDatasets.data.push(numPoints);\r\n\r\n            if (numPoints + PerformanceViewerCollector.SliceDataOffset !== line.length) {\r\n                return false;\r\n            }\r\n\r\n            for (let i = PerformanceViewerCollector.SliceDataOffset; i < line.length; i++) {\r\n                const val = parseFloat(line[i]);\r\n                if (isNaN(val)) {\r\n                    return false;\r\n                }\r\n                parsedDatasets.data.push(val);\r\n            }\r\n\r\n            parsedDatasets.startingIndices.push(startingIndex);\r\n            startingIndex += line.length;\r\n        }\r\n\r\n        this.datasets.ids = parsedDatasets.ids;\r\n        this.datasets.data = parsedDatasets.data;\r\n        this.datasets.startingIndices = parsedDatasets.startingIndices;\r\n        if (!keepDatasetMeta) {\r\n            this._datasetMeta.clear();\r\n        }\r\n        this._strategies.forEach((strategy) => strategy.dispose());\r\n        this._strategies.clear();\r\n\r\n        // populate metadata.\r\n        if (!keepDatasetMeta) {\r\n            for (const id of this.datasets.ids) {\r\n                const category = idCategoryMap.get(id);\r\n\r\n                this._datasetMeta.set(id, { category, color: this._getHexColorFromId(id) });\r\n            }\r\n        }\r\n        this.metadataObservable.notifyObservers(this._datasetMeta);\r\n        this._hasLoadedData = true;\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Exports the datasets inside of the collector to a csv.\r\n     */\r\n    public exportDataToCsv() {\r\n        let csvContent = \"\";\r\n        // create the header line.\r\n        csvContent += `${TimestampColHeader},${NumPointsColHeader}`;\r\n        for (let i = 0; i < this.datasets.ids.length; i++) {\r\n            csvContent += `,${this.datasets.ids[i]}`;\r\n            if (this._datasetMeta) {\r\n                const meta = this._datasetMeta.get(this.datasets.ids[i]);\r\n                if (meta?.category) {\r\n                    csvContent += `${ExportedDataSeparator}${meta.category}`;\r\n                }\r\n            }\r\n        }\r\n        csvContent += \"\\n\";\r\n        // create the data lines\r\n        for (let i = 0; i < this.datasets.startingIndices.itemLength; i++) {\r\n            const startingIndex = this.datasets.startingIndices.at(i);\r\n            const timestamp = this.datasets.data.at(startingIndex);\r\n            const numPoints = this.datasets.data.at(startingIndex + PerformanceViewerCollector.NumberOfPointsOffset);\r\n\r\n            csvContent += `${timestamp},${numPoints}`;\r\n\r\n            for (let offset = 0; offset < numPoints; offset++) {\r\n                csvContent += `,${this.datasets.data.at(startingIndex + PerformanceViewerCollector.SliceDataOffset + offset)}`;\r\n            }\r\n\r\n            // add extra commas.\r\n            for (let diff = 0; diff < this.datasets.ids.length - numPoints; diff++) {\r\n                csvContent += \",\";\r\n            }\r\n\r\n            csvContent += \"\\n\";\r\n        }\r\n\r\n        const fileName = `${new Date().toISOString()}-perfdata.csv`;\r\n        Tools.Download(new Blob([csvContent], { type: \"text/csv\" }), fileName);\r\n    }\r\n    /**\r\n     * Starts the realtime collection of data.\r\n     * @param shouldPreserve optional boolean param, if set will preserve the dataset between calls of start.\r\n     */\r\n    public start(shouldPreserve?: boolean) {\r\n        if (!shouldPreserve) {\r\n            this.datasets.data = new DynamicFloat32Array(InitialArraySize);\r\n            this.datasets.startingIndices = new DynamicFloat32Array(InitialArraySize);\r\n            this._startingTimestamp = PrecisionDate.Now;\r\n        } else if (this._startingTimestamp === undefined) {\r\n            this._startingTimestamp = PrecisionDate.Now;\r\n        }\r\n        this._scene.onAfterRenderObservable.add(this._collectDataAtFrame);\r\n        this._restoreStringEvents();\r\n        this._isStarted = true;\r\n    }\r\n\r\n    /**\r\n     * Stops the collection of data.\r\n     */\r\n    public stop() {\r\n        this._scene.onAfterRenderObservable.removeCallback(this._collectDataAtFrame);\r\n        this._isStarted = false;\r\n    }\r\n\r\n    /**\r\n     * Returns if the perf collector has been started or not.\r\n     */\r\n    public get isStarted(): boolean {\r\n        return this._isStarted;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the object\r\n     */\r\n    public dispose() {\r\n        this._scene.onAfterRenderObservable.removeCallback(this._collectDataAtFrame);\r\n        this._datasetMeta.clear();\r\n        this._strategies.forEach((strategy) => {\r\n            strategy.dispose();\r\n        });\r\n        this.datasetObservable.clear();\r\n        this.metadataObservable.clear();\r\n        this._isStarted = false;\r\n        (<any>this.datasets) = null;\r\n    }\r\n}\r\n"]}