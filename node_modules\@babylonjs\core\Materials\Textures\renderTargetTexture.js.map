{"version": 3, "file": "renderTargetTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/renderTargetTexture.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAKnD,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAM1D,OAAO,EAAE,OAAO,EAAE,MAAM,kCAAkC,CAAC;AAC3D,OAAO,EAAE,kBAAkB,EAAE,MAAM,wCAAwC,CAAC;AAE5E,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AACpE,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAGpD,OAAO,8CAA8C,CAAC;AACtD,OAAO,kDAAkD,CAAC;AAC1D,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAyDjD;;;;GAIG;AACH,MAAM,OAAO,mBAAoB,SAAQ,OAAO;IAwB5C;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAoC;QACtD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;SACpC;QAED,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;SAChF;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAiED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAID,IAAY,eAAe;QACvB,OAAO,CAAC,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;IAC5E,CAAC;IAaD;;;OAGG;IACH,IAAW,aAAa,CAAC,QAAoB;QACzC,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;SACpE;QACD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC7E,CAAC;IAQD;;;OAGG;IACH,IAAW,cAAc,CAAC,QAAqC;QAC3D,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACtE;QACD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/E,CAAC;IAQD;;;OAGG;IACH,IAAW,aAAa,CAAC,QAAqC;QAC1D,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;SACpE;QACD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC7E,CAAC;IAQD;;;OAGG;IACH,IAAW,OAAO,CAAC,QAAkC;QACjD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACxD;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACjE,CAAC;IAsCD;;;OAGG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,IAAmC,EAAE,QAAgC;QAChG,IAAI,MAAM,CAAC;QACX,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACtB,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;SACnB;aAAM;YACH,MAAM,GAAG,IAAI,CAAC;SACjB;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACjD,MAAM,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;aACvJ;SACJ;IACL,CAAC;IAID;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,aAAa,EAAE,OAAO,IAAI,KAAK,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAES,eAAe;QACrB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SAC3C;IACL,CAAC;IAUD;;;;;OAKG;IACH,IAAW,eAAe,CAAC,KAAc;QACrC,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAC9D,OAAO;SACV;QACD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;SACtE;IACL,CAAC;IACD,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,aAAa,EAAE,oBAAoB,IAAI,IAAI,CAAC;IAC5D,CAAC;IAqDD,gBAAgB;IAChB,YACI,IAAY,EACZ,IAAqF,EACrF,KAAuB,EACvB,kBAAwD,KAAK,EAC7D,yBAAkC,IAAI,EACtC,OAAe,SAAS,CAAC,wBAAwB,EACjD,MAAM,GAAG,KAAK,EACd,YAAY,GAAG,OAAO,CAAC,sBAAsB,EAC7C,mBAAmB,GAAG,IAAI,EAC1B,qBAAqB,GAAG,KAAK,EAC7B,OAAO,GAAG,KAAK,EACf,MAAM,GAAG,SAAS,CAAC,kBAAkB,EACrC,eAAe,GAAG,KAAK,EACvB,OAAgB,EAChB,aAAsB,EACtB,iBAAiB,GAAG,KAAK,EACzB,aAAa,GAAG,KAAK;QAErB,IAAI,eAAe,GAAgC,SAAS,CAAC;QAC7D,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;YACrC,MAAM,OAAO,GAAG,eAAe,CAAC;YAChC,eAAe,GAAG,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;YAC5C,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,IAAI,IAAI,CAAC;YAChE,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,SAAS,CAAC,yBAAyB,CAAC;YAC3D,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1B,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,sBAAsB,CAAC;YACtE,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,IAAI,IAAI,CAAC;YAC1D,qBAAqB,GAAG,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC;YACxD,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;YAC5B,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,kBAAkB,CAAC;YACxD,eAAe,GAAG,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;YAC5C,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAC1B,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;YACtC,iBAAiB,GAAG,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC;YAChD,aAAa,GAAG,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;YACxC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;YAC1C,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC;SACjD;QAED,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,eAAe,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QAhZ9G,yBAAoB,GAAyB,IAAI,CAAC;QAsBlD,0BAAqB,GAAG,CAAC,aAAqB,EAAE,cAAsB,EAAE,EAAE;YAC9E,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACjE,IAAI,CAAC,cAAc,KAAK,CAAC,IAAI,SAAS,GAAG,CAAC,CAAC,IAAI,SAAS,KAAK,CAAC,EAAE;gBAC5D,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBACrC,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBACtC,CAAC,CAAC,CAAC;aACN;QACL,CAAC,CAAC;QAaF;;WAEG;QACI,oBAAe,GAAG,IAAI,CAAC;QAC9B;;WAEG;QACI,kBAAa,GAAG,KAAK,CAAC;QAE7B;;WAEG;QACI,wBAAmB,GAAG,KAAK,CAAC;QAwBnC;;WAEG;QACI,yBAAoB,GAAY,KAAK,CAAC;QAiB7C;;WAEG;QACI,2BAAsB,GAAG,IAAI,UAAU,EAAuB,CAAC;QAEtE;;WAEG;QACI,4BAAuB,GAAG,IAAI,UAAU,EAAuB,CAAC;QAcvE;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAU,CAAC;QAc3D;;WAEG;QACI,4BAAuB,GAAG,IAAI,UAAU,EAAU,CAAC;QAc1D;;WAEG;QACI,sBAAiB,GAAG,IAAI,UAAU,EAAU,CAAC;QAcpD;;WAEG;QACI,uBAAkB,GAAG,IAAI,UAAU,EAAuB,CAAC;QAWlE,gBAAgB;QACT,aAAQ,GAAG,KAAK,CAAC;QACxB;;WAEG;QACI,qBAAgB,GAAG,KAAK,CAAC;QAKtB,sBAAiB,GAAG,CAAC,CAAC,CAAC;QACvB,iBAAY,GAAG,CAAC,CAAC;QAEjB,aAAQ,GAAG,CAAC,CAAC;QAEf,gBAAW,GAAG,IAAI,CAAC;QACjB,kBAAa,GAAkC,IAAI,CAAC;QAqE9D;;;WAGG;QACI,wBAAmB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAgIxC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACxB,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAG,CAAC,SAAS,EAAE,CAAC;QAE5C,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,eAAe,CAAC;QAChD,IAAI,CAAC,UAAU,GAAG,EAAoB,CAAC;QACvC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;QAE1B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAEjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAE3C,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAE/D,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACvD,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QAEtD,mBAAmB;QACnB,IAAI,CAAC,iBAAiB,GAAG,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,GAAG,IAAI,CAAC;QAEtD,IAAI,OAAO,EAAE;YACT,OAAO;SACV;QAED,IAAI,CAAC,oBAAoB,GAAG;YACxB,eAAe,EAAE,eAAe;YAChC,IAAI,EAAE,IAAI;YACV,MAAM,EAAE,IAAI,CAAC,OAAO,IAAI,SAAS;YACjC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,mBAAmB,EAAE,mBAAmB;YACxC,qBAAqB,EAAE,qBAAqB;YAC5C,OAAO;YACP,aAAa;YACb,iBAAiB,EAAE,iBAAiB;YACpC,aAAa;YACb,eAAe,EAAE,eAAe;YAChC,KAAK,EAAE,IAAI,CAAC,IAAI;SACnB,CAAC;QAEF,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,CAAC,oBAAoB,EAAE;YACpD,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;YACvC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC;SAC1C;QAED,IAAI,CAAC,eAAe,EAAE;YAClB,IAAI,MAAM,EAAE;gBACR,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,6BAA6B,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACtH,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC;gBAC7C,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;aAC3C;iBAAM;gBACH,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,yBAAyB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;aAC3G;YACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YAC3C,IAAI,OAAO,KAAK,SAAS,EAAE;gBACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;aAC1B;SACJ;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,yBAAyB,CAC5B,qBAA6B,CAAC,EAC9B,oBAA6B,IAAI,EACjC,kBAA2B,KAAK,EAChC,UAAkB,CAAC,EACnB,SAAiB,SAAS,CAAC,2BAA2B;QAEtD,IAAI,CAAC,aAAa,EAAE,yBAAyB,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,eAAe,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC3H,CAAC;IAEO,oBAAoB;QACxB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACjD,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;aACtD;SACJ;QACD,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IAC7B,CAAC;IAEO,mBAAmB;QACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAO,CAAC,SAAS,EAAE,CAAC,CAAC,gEAAgE;QACzG,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QAErE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,EAAE,CAAC,EAAE;YAChC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,yBAAyB,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;SACjG;IACL,CAAC;IAES,qBAAqB,CAAC,IAAoE,EAAE,mBAAmB,GAAG,IAAI;QAC5H,IAAwB,IAAK,CAAC,KAAK,EAAE;YACjC,IAAI,CAAC,UAAU,GAAuB,IAAK,CAAC,KAAK,CAAC;YAClD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAG,CAAC;YAClC,IAAI,CAAC,KAAK,GAAG;gBACT,KAAK,EAAE,IAAI,CAAC,oCAAoC,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC;gBAC1F,MAAM,EAAE,IAAI,CAAC,oCAAoC,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC;aAC/F,CAAC;SACL;aAAM;YACH,IAAI,CAAC,KAAK,GAAgE,IAAI,CAAC;SAClF;QAED,IAAI,mBAAmB,EAAE;YACrB,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC9B;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,aAAa,EAAE,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC;IACxD,CAAC;IAED,IAAW,OAAO,CAAC,KAAa;QAC5B,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SACxD;IACL,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACtB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IACD,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,WAAwB;QAC1C,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAE9B,IAAI,CAAC,KAAK,EAAE;gBACR,OAAO;aACV;YACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACzD,IAAI,CAAC,cAAc,GAAG,IAAI,KAAK,EAAe,CAAC;SAClD;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACI,kBAAkB,CAAC,UAAmB,KAAK;QAC9C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QAED,IAAI,OAAO,EAAE;YACT,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,cAAc,EAAE;gBAC3C,WAAW,CAAC,OAAO,EAAE,CAAC;aACzB;SACJ;QAED,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,WAAwB;QAC7C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;SACV;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAEvD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,OAAO;SACV;QAED,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAErC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;YAChC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;SAC5C;IACL,CAAC;IAED,gBAAgB;IACT,aAAa;QAChB,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,EAAE;YAC/B,uBAAuB;YACvB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,iBAAiB,EAAE;YAC7C,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,IAAwC,IAAI,CAAC,KAAM,CAAC,KAAK,EAAE;YACvD,OAA2C,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC;SAChE;QAED,OAAe,IAAI,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,IAAwC,IAAI,CAAC,KAAM,CAAC,KAAK,EAAE;YACvD,OAA2C,IAAI,CAAC,KAAM,CAAC,MAAM,CAAC;SACjE;QAED,OAAe,IAAI,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,MAAM,MAAM,GAAwD,IAAI,CAAC,KAAM,CAAC,MAAM,CAAC;QACvF,IAAI,MAAM,EAAE;YACR,OAAO,MAAM,CAAC;SACjB;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,KAAa;QACtB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,GAAG,KAAK,CAAC,CAAC;QAE1D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,0BAA0B;QAC7B,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO,IAAI,CAAC,cAAc,CAAC;SAC9B;QAED,OAAO,KAAK,CAAC,0BAA0B,EAAE,CAAC;IAC9C,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,IAAoE;QAC9E,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;QAE5B,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAExC,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,6BAA6B,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACzH;aAAM;YACH,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,yBAAyB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAC3G;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QAE3C,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,KAAK,SAAS,EAAE;YACjD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;SACpD;QAED,IAAI,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,EAAE;YACxC,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SACjD;IACL,CAAC;IAID;;;;OAIG;IACI,MAAM,CAAC,uBAAgC,KAAK,EAAE,eAAwB,KAAK;QAC9E,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAEO,OAAO,CAAC,uBAAgC,KAAK,EAAE,eAAwB,KAAK,EAAE,iBAA0B,KAAK;QACjH,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,cAAc,CAAC;SACzB;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,sBAAsB,KAAK,SAAS,EAAE;YAC3C,oBAAoB,GAAG,IAAI,CAAC,sBAAsB,CAAC;SACtD;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3B,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;gBACrB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBACjE,MAAM,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;oBAC1C,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;oBACnC,IAAI,IAAI,EAAE;wBACN,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBAC9B;iBACJ;aACJ;YACD,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;SACvC;QAED,wBAAwB;QACxB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,4BAA4B;aAC3D;iBAAM;gBACH,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;aACxB;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAE9B,IAAI,CAAC,KAAK,EAAE;gBACR,OAAO,cAAc,CAAC;aACzB;YAED,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;YAEjC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACrD,MAAM,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;gBAChC,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;oBAChC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAC9B;aACJ;SACJ;QAED,MAAM,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,CAAC;QAEvD,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAElD,yBAAyB;QACzB,mEAAmE;QACnE,MAAM,MAAM,GAAqB,IAAI,CAAC,YAAY,IAAI,KAAK,CAAC,YAAY,CAAC;QACzE,MAAM,WAAW,GAAG,KAAK,CAAC,YAAY,CAAC;QAEvC,IAAI,MAAM,EAAE;YACR,IAAI,MAAM,KAAK,KAAK,CAAC,YAAY,EAAE;gBAC/B,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;gBACnF,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC;aAC/B;YACD,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;SACrI;QAED,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;QAExC,IAAI,WAAW,GAAG,cAAc,CAAC;QAEjC,IAAI,CAAC,cAAc,EAAE;YACjB,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,EAAE;oBACzD,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,oBAAoB,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;oBAC3E,KAAK,CAAC,iBAAiB,EAAE,CAAC;oBAC1B,KAAK,CAAC,mBAAmB,EAAE,CAAC;iBAC/B;aACJ;iBAAM,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACrC,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE;oBACjC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,oBAAoB,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;oBAClF,KAAK,CAAC,iBAAiB,EAAE,CAAC;oBAC1B,KAAK,CAAC,mBAAmB,EAAE,CAAC;iBAC/B;aACJ;iBAAM;gBACH,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,oBAAoB,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;aAClF;SACJ;aAAM;YACH,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,EAAE;gBACxB,mHAAmH;gBACnH,KAAK,CAAC,qBAAqB,EAAE,CAAC;aACjC;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChF,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,SAAS,IAAI,WAAW,EAAE,KAAK,EAAE,EAAE;gBAC3D,IAAI,iBAAiB,GAAkC,IAAI,CAAC;gBAC5D,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC;gBAC3F,MAAM,uBAAuB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC;gBAE1G,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAExD,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAErD,IAAI,IAAI,CAAC,mBAAmB,EAAE;oBAC1B,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,iBAAiB,EAAE,uBAAuB,CAAC,CAAC;iBACnG;gBAED,IAAI,CAAC,iBAAiB,EAAE;oBACpB,iBAAiB,GAAG,iBAAiB,CAAC;iBACzC;gBAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;oBAC/B,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;iBACrC;gBAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,IAAI,WAAW,EAAE,EAAE,CAAC,EAAE;oBAC9D,MAAM,IAAI,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;oBAElC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;wBAC3E,SAAS;qBACZ;oBAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;wBAC5B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,EAAE;4BACrE,WAAW,GAAG,KAAK,CAAC;4BACpB,SAAS;yBACZ;qBACJ;yBAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBAC5B,WAAW,GAAG,KAAK,CAAC;wBACpB,SAAS;qBACZ;iBACJ;gBAED,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAEpD,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE;oBAC/B,KAAK,CAAC,iBAAiB,EAAE,CAAC;oBAC1B,KAAK,CAAC,mBAAmB,EAAE,CAAC;iBAC/B;aACJ;SACJ;QAED,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEnD,MAAM,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAEjD,IAAI,WAAW,EAAE;YACb,KAAK,CAAC,YAAY,GAAG,WAAW,CAAC;YACjC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,CAAC,YAAY,EAAE;gBAC/D,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,YAAY,CAAC,aAAa,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;aAC9G;YACD,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;SACnD;QAED,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE5B,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,oCAAoC,CAAC,eAAuB,EAAE,KAAa;QAC/E,MAAM,OAAO,GAAG,GAAG,CAAC;QACpB,MAAM,CAAC,GAAG,eAAe,GAAG,KAAK,CAAC;QAClC,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;QAE1E,kEAAkE;QAClE,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC;IAEO,wBAAwB,CAAC,iBAAsC,EAAE,uBAA+B,EAAE,MAAwB,EAAE,cAAuB;QACvJ,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAE/B,MAAM,aAAa,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAC1C,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,uBAAuB,EAAE,SAAS,EAAE,EAAE;YACtE,MAAM,IAAI,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAE1C,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACzB,IAAI,IAAI,CAAC,qBAAqB,EAAE;oBAC5B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE;wBAC5D,IAAI,CAAC,mBAAmB,EAAE,CAAC;wBAC3B,SAAS;qBACZ;iBACJ;qBAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;oBAC9C,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC3B,SAAS;iBACZ;gBAED,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,qBAAqB,IAAI,KAAK,CAAC,YAAY,EAAE;oBACjF,IAAI,CAAC,6BAA6B,CAAC,WAAW,GAAG,KAAK,CAAC,iBAAiB;wBACpE,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,IAAI,KAAK,CAAC,YAAY,CAAC;wBACxE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;oBAC3D,IAAI,CAAC,6BAA6B,CAAC,qBAAqB,GAAG,IAAI,CAAC;iBACnE;gBACD,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,WAAW,EAAE;oBACjD,SAAS;iBACZ;gBAED,IAAI,YAAY,GAAG,IAAI,CAAC,6BAA6B,CAAC,WAAW,CAAC;gBAElE,YAAY,CAAC,oCAAoC,CAAC,aAAa,CAAC,CAAC;gBAEjE,IAAI,QAAQ,CAAC;gBACb,IAAI,cAAc,IAAI,MAAM,EAAE;oBAC1B,QAAQ,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;iBACxD;qBAAM;oBACH,QAAQ,GAAG,KAAK,CAAC;iBACpB;gBAED,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE;oBACnE,IAAI,YAAY,KAAK,IAAI,EAAE;wBACvB,YAAY,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;qBAC/C;oBACD,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;wBAC9D,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;4BACpB,YAAY,CAAC,6BAA6B,CAAC,6BAA6B,GAAG,KAAK,CAAC;yBACpF;6BAAM;4BACH,IAAI,IAAI,CAAC,6BAA6B,CAAC,iBAAiB,EAAE;gCACtD,YAAY,GAAG,IAAI,CAAC;6BACvB;yBACJ;wBACD,YAAY,CAAC,6BAA6B,CAAC,qBAAqB,GAAG,IAAI,CAAC;wBAExE,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;4BACzE,MAAM,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;4BACjD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;yBAC1D;qBACJ;iBACJ;aACJ;SACJ;QAED,KAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,aAAa,EAAE,EAAE;YACvF,MAAM,cAAc,GAAG,KAAK,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAE5D,MAAM,OAAO,GAAQ,cAAc,CAAC,OAAO,CAAC;YAE5C,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,EAAE;gBACvF,SAAS;aACZ;YAED,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;SAC5D;IACL,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,YAAoB,CAAC,EAAE,KAAK,GAAG,CAAC;QACpD,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;SAC9I;IACL,CAAC;IAES,kBAAkB,CAAC,MAAc,EAAE,SAAiB;QAC1D,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO;SACV;QACD,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE;YAC3D,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,KAAY,EAAE,SAAkB,EAAE,KAAc,EAAE,oBAA8B;QACjG,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACvB,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;aAC9E;SACJ;aAAM,IAAI,CAAC,oBAAoB,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACxF,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SAC3C;IACL,CAAC;IAEO,eAAe,CAAC,SAAiB,EAAE,oBAA6B,EAAE,YAAqB,EAAE,KAAK,GAAG,CAAC,EAAE,SAA2B,IAAI;QACvI,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,MAAM,CAAC,eAAe,EAAE,CAAC,mBAAmB,SAAS,WAAW,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;QAE5E,OAAO;QACP,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,oBAAoB,CAAC,CAAC;QAElE,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACxD,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SACxD;aAAM;YACH,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC5D,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;SAC5D;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,iBAAiB,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,CAAC,sBAAsB,CAAC;QAE/G,IAAI,CAAC,QAAQ,EAAE;YACX,mCAAmC;YACnC,IAAI,iBAAiB,GAAkC,IAAI,CAAC;YAC5D,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC;YAC3F,MAAM,uBAAuB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC;YAE1G,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,EAAE,iBAAiB,EAAE,uBAAuB,CAAC,CAAC;aAChI;YAED,IAAI,CAAC,iBAAiB,EAAE;gBACpB,2FAA2F;gBAC3F,wGAAwG;gBACxG,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;oBAClC,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,MAAM,EAAE,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC;oBAChI,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;iBAC1C;gBACD,iBAAiB,GAAG,iBAAiB,CAAC;aACzC;iBAAM;gBACH,4DAA4D;gBAC5D,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;aAChH;YAED,eAAe;YACf,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,6BAA6B,EAAE;gBACpD,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;aACvC;YAED,QAAQ;YACR,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,EAAE;gBACvC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;aAClD;iBAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC/B,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;aACvE;YAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAC/B,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;aACrC;YAED,qBAAqB;YACrB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,4BAA4B,EAAE;gBACnD,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;aACvC;YAED,SAAS;YACT,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAEtH,oBAAoB;YACpB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,2BAA2B,EAAE;gBAClD,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;aACvC;YAED,MAAM,mBAAmB,GAAG,IAAI,CAAC,QAAQ,EAAE,eAAe,IAAI,KAAK,CAAC;YAEpE,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,KAAK,CAAC,CAAC,mLAAmL;gBAC1N,8LAA8L;gBAC9L,8CAA8C;aACjD;YAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,IAAI,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;aAC9I;iBAAM,IAAI,oBAAoB,EAAE;gBAC7B,KAAK,CAAC,kBAAkB,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,IAAI,SAAS,EAAE,SAAS,CAAC,CAAC;aAC9F;YAED,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,kCAAkC,EAAE;gBACzD,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;aACvC;YAED,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,mBAAmB,CAAC;aACvD;YAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAC/B,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;aACrC;YAED,SAAS;YACT,IAAI,YAAY,EAAE;gBACd,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,MAAM,CAAC,CAAC;aACpF;SACJ;aAAM;YACH,QAAQ;YACR,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,EAAE;gBACvC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;aAClD;iBAAM;gBACH,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;oBACxB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;iBACvE;aACJ;SACJ;QAED,SAAS;QACT,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAE3C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS,KAAK,CAAC,EAAE;YACjD,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACnD;QAED,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IAED;;;;;;;;OAQG;IACI,iBAAiB,CACpB,gBAAwB,EACxB,sBAAoE,IAAI,EACxE,yBAAuE,IAAI,EAC3E,2BAAyE,IAAI;QAE7E,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,wBAAwB,CAAC,CAAC;IACtI,CAAC;IAED;;;;;OAKG;IACI,iCAAiC,CAAC,gBAAwB,EAAE,qBAA8B;QAC7F,IAAI,CAAC,iBAAiB,CAAC,iCAAiC,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAAC;QAClG,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,GAAG,KAAK,CAAC;IAC3D,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,UAAU,GAAG,IAAI,mBAAmB,CACtC,IAAI,CAAC,IAAI,EACT,WAAW,EACX,IAAI,CAAC,QAAQ,EAAE,EACf,IAAI,CAAC,oBAAoB,CAAC,eAAe,EACzC,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAC9B,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,oBAAoB,CAAC,YAAY,EACtC,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAC7C,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,EAC/C,SAAS,EACT,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAChC,SAAS,EACT,IAAI,CAAC,oBAAoB,CAAC,OAAO,CACpC,CAAC;QAEF,eAAe;QACf,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAE9B,uBAAuB;QACvB,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAClD,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACpD;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QAED,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAC5D,mBAAmB,CAAC,UAAU,GAAG,EAAE,CAAC;QAEpC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACzD,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;aAClE;SACJ;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,yBAAyB;QAC5B,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,sBAAsB;QACzB,IAAI,CAAC,aAAa,EAAE,eAAe,EAAE,CAAC;QACtC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAChC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QAEtC,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;YACnC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;SACvC;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAE9B,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,QAAQ,EAAG,CAAC,SAAS,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC7E,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC/B;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,oCAAoC;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,IAAI,KAAK,GAAG,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEpD,IAAI,KAAK,IAAI,CAAC,EAAE;YACZ,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC9C;QAED,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;YAChC,KAAK,GAAG,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEjD,IAAI,KAAK,IAAI,CAAC,EAAE;gBACZ,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAC/C;SACJ;QAED,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,IAAI,IAAI,CAAC,WAAW,KAAK,mBAAmB,CAAC,uBAAuB,EAAE;YAClE,IAAI,CAAC,WAAW,GAAG,mBAAmB,CAAC,uBAAuB,CAAC;SAClE;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;SACvC;IACL,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;SAChD;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,CAAC,CAAC;IACb,CAAC;;AAt3CD;;GAEG;AACoB,2CAAuB,GAAW,CAAC,AAAZ,CAAa;AAC3D;;GAEG;AACoB,mDAA+B,GAAW,CAAC,AAAZ,CAAa;AACnE;;;GAGG;AACoB,uDAAmC,GAAW,CAAC,AAAZ,CAAa;AA62C3E,6DAA6D;AAC7D,OAAO,CAAC,0BAA0B,GAAG,CAAC,IAAY,EAAE,gBAAwB,EAAE,KAAY,EAAE,eAAwB,EAAE,aAAsB,EAAE,EAAE;IAC5I,OAAO,IAAI,mBAAmB,CAAC,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;AACnF,CAAC,CAAC", "sourcesContent": ["import type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { SmartArray } from \"../../Misc/smartArray\";\r\nimport type { Nullable, Immutable } from \"../../types\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Matrix, Vector3 } from \"../../Maths/math.vector\";\r\nimport type { Color4 } from \"../../Maths/math.color\";\r\nimport type { RenderTargetCreationOptions, TextureSize } from \"../../Materials/Textures/textureCreationOptions\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport { Texture } from \"../../Materials/Textures/texture\";\r\nimport { PostProcessManager } from \"../../PostProcesses/postProcessManager\";\r\nimport type { PostProcess } from \"../../PostProcesses/postProcess\";\r\nimport { RenderingManager } from \"../../Rendering/renderingManager\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport type { IRenderTargetTexture, RenderTargetWrapper } from \"../../Engines/renderTargetWrapper\";\r\n\r\nimport \"../../Engines/Extensions/engine.renderTarget\";\r\nimport \"../../Engines/Extensions/engine.renderTargetCube\";\r\nimport { Engine } from \"../../Engines/engine\";\r\nimport { _ObserveArray } from \"../../Misc/arrayTools\";\r\nimport { DumpTools } from \"../../Misc/dumpTools\";\r\n\r\nimport type { Material } from \"../material\";\r\n\r\n/**\r\n * Options for the RenderTargetTexture constructor\r\n */\r\nexport interface RenderTargetTextureOptions {\r\n    /** True (default: false) if mipmaps need to be generated after render */\r\n    generateMipMaps?: boolean;\r\n\r\n    /** True (default) to not change the aspect ratio of the scene in the RTT */\r\n    doNotChangeAspectRatio?: boolean;\r\n\r\n    /** The type of the buffer in the RTT (byte (default), half float, float...) */\r\n    type?: number;\r\n\r\n    /** True (default: false) if a cube texture needs to be created */\r\n    isCube?: boolean;\r\n\r\n    /** The sampling mode to be used with the render target (Trilinear (default), Linear, Nearest...) */\r\n    samplingMode?: number;\r\n\r\n    /** True (default) to generate a depth buffer */\r\n    generateDepthBuffer?: boolean;\r\n\r\n    /** True (default: false) to generate a stencil buffer */\r\n    generateStencilBuffer?: boolean;\r\n\r\n    /** True (default: false) if multiple textures need to be created (Draw Buffers) */\r\n    isMulti?: boolean;\r\n\r\n    /** The internal format of the buffer in the RTT (RED, RG, RGB, RGBA (default), ALPHA...) */\r\n    format?: number;\r\n\r\n    /** True (default: false) if the texture allocation should be delayed */\r\n    delayAllocation?: boolean;\r\n\r\n    /** Sample count to use when creating the RTT */\r\n    samples?: number;\r\n\r\n    /** specific flags to use when creating the texture (e.g., Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures) */\r\n    creationFlags?: number;\r\n\r\n    /** True (default: false) to indicate that no color target should be created. (e.g., if you only want to write to the depth buffer) */\r\n    noColorAttachment?: boolean;\r\n\r\n    /** Specifies the internal texture to use directly instead of creating one (ignores `noColorAttachment` flag when set) **/\r\n    colorAttachment?: InternalTexture;\r\n\r\n    /** True (default: false) to create a SRGB texture */\r\n    useSRGBBuffer?: boolean;\r\n\r\n    /** Defines the underlying texture texture space */\r\n    gammaSpace?: boolean;\r\n}\r\n\r\n/**\r\n * This Helps creating a texture that will be created from a camera in your scene.\r\n * It is basically a dynamic texture that could be used to create special effects for instance.\r\n * Actually, It is the base of lot of effects in the framework like post process, shadows, effect layers and rendering pipelines...\r\n */\r\nexport class RenderTargetTexture extends Texture implements IRenderTargetTexture {\r\n    /**\r\n     * The texture will only be rendered once which can be useful to improve performance if everything in your render is static for instance.\r\n     */\r\n    public static readonly REFRESHRATE_RENDER_ONCE: number = 0;\r\n    /**\r\n     * The texture will only be rendered rendered every frame and is recommended for dynamic contents.\r\n     */\r\n    public static readonly REFRESHRATE_RENDER_ONEVERYFRAME: number = 1;\r\n    /**\r\n     * The texture will be rendered every 2 frames which could be enough if your dynamic objects are not\r\n     * the central point of your effect and can save a lot of performances.\r\n     */\r\n    public static readonly REFRESHRATE_RENDER_ONEVERYTWOFRAMES: number = 2;\r\n\r\n    /**\r\n     * Use this predicate to dynamically define the list of mesh you want to render.\r\n     * If set, the renderList property will be overwritten.\r\n     */\r\n    public renderListPredicate: (AbstractMesh: AbstractMesh) => boolean;\r\n\r\n    private _renderList: Nullable<Array<AbstractMesh>>;\r\n    private _unObserveRenderList: Nullable<() => void> = null;\r\n\r\n    /**\r\n     * Use this list to define the list of mesh you want to render.\r\n     */\r\n    public get renderList(): Nullable<Array<AbstractMesh>> {\r\n        return this._renderList;\r\n    }\r\n\r\n    public set renderList(value: Nullable<Array<AbstractMesh>>) {\r\n        if (this._unObserveRenderList) {\r\n            this._unObserveRenderList();\r\n            this._unObserveRenderList = null;\r\n        }\r\n\r\n        if (value) {\r\n            this._unObserveRenderList = _ObserveArray(value, this._renderListHasChanged);\r\n        }\r\n\r\n        this._renderList = value;\r\n    }\r\n\r\n    private _renderListHasChanged = (_functionName: String, previousLength: number) => {\r\n        const newLength = this._renderList ? this._renderList.length : 0;\r\n        if ((previousLength === 0 && newLength > 0) || newLength === 0) {\r\n            this.getScene()?.meshes.forEach((mesh) => {\r\n                mesh._markSubMeshesAsLightDirty();\r\n            });\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Use this function to overload the renderList array at rendering time.\r\n     * Return null to render with the current renderList, else return the list of meshes to use for rendering.\r\n     * For 2DArray RTT, layerOrFace is the index of the layer that is going to be rendered, else it is the faceIndex of\r\n     * the cube (if the RTT is a cube, else layerOrFace=0).\r\n     * The renderList passed to the function is the current render list (the one that will be used if the function returns null).\r\n     * The length of this list is passed through renderListLength: don't use renderList.length directly because the array can\r\n     * hold dummy elements!\r\n     */\r\n    public getCustomRenderList: (layerOrFace: number, renderList: Nullable<Immutable<Array<AbstractMesh>>>, renderListLength: number) => Nullable<Array<AbstractMesh>>;\r\n\r\n    /**\r\n     * Define if particles should be rendered in your texture.\r\n     */\r\n    public renderParticles = true;\r\n    /**\r\n     * Define if sprites should be rendered in your texture.\r\n     */\r\n    public renderSprites = false;\r\n\r\n    /**\r\n     * Force checking the layerMask property even if a custom list of meshes is provided (ie. if renderList is not undefined)\r\n     */\r\n    public forceLayerMaskCheck = false;\r\n\r\n    /**\r\n     * Define the camera used to render the texture.\r\n     */\r\n    public activeCamera: Nullable<Camera>;\r\n    /**\r\n     * Override the mesh isReady function with your own one.\r\n     */\r\n    public customIsReadyFunction: (mesh: AbstractMesh, refreshRate: number, preWarm?: boolean) => boolean;\r\n    /**\r\n     * Override the render function of the texture with your own one.\r\n     */\r\n    public customRenderFunction: (\r\n        opaqueSubMeshes: SmartArray<SubMesh>,\r\n        alphaTestSubMeshes: SmartArray<SubMesh>,\r\n        transparentSubMeshes: SmartArray<SubMesh>,\r\n        depthOnlySubMeshes: SmartArray<SubMesh>,\r\n        beforeTransparents?: () => void\r\n    ) => void;\r\n    /**\r\n     * Define if camera post processes should be use while rendering the texture.\r\n     */\r\n    public useCameraPostProcesses: boolean;\r\n    /**\r\n     * Define if the camera viewport should be respected while rendering the texture or if the render should be done to the entire texture.\r\n     */\r\n    public ignoreCameraViewport: boolean = false;\r\n\r\n    private _postProcessManager: Nullable<PostProcessManager>;\r\n\r\n    /**\r\n     * Post-processes for this render target\r\n     */\r\n    public get postProcesses() {\r\n        return this._postProcesses;\r\n    }\r\n    private _postProcesses: PostProcess[];\r\n    private _resizeObserver: Nullable<Observer<Engine>>;\r\n\r\n    private get _prePassEnabled() {\r\n        return !!this._prePassRenderTarget && this._prePassRenderTarget.enabled;\r\n    }\r\n\r\n    /**\r\n     * An event triggered when the texture is unbind.\r\n     */\r\n    public onBeforeBindObservable = new Observable<RenderTargetTexture>();\r\n\r\n    /**\r\n     * An event triggered when the texture is unbind.\r\n     */\r\n    public onAfterUnbindObservable = new Observable<RenderTargetTexture>();\r\n\r\n    private _onAfterUnbindObserver: Nullable<Observer<RenderTargetTexture>>;\r\n    /**\r\n     * Set a after unbind callback in the texture.\r\n     * This has been kept for backward compatibility and use of onAfterUnbindObservable is recommended.\r\n     */\r\n    public set onAfterUnbind(callback: () => void) {\r\n        if (this._onAfterUnbindObserver) {\r\n            this.onAfterUnbindObservable.remove(this._onAfterUnbindObserver);\r\n        }\r\n        this._onAfterUnbindObserver = this.onAfterUnbindObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered before rendering the texture\r\n     */\r\n    public onBeforeRenderObservable = new Observable<number>();\r\n\r\n    private _onBeforeRenderObserver: Nullable<Observer<number>>;\r\n    /**\r\n     * Set a before render callback in the texture.\r\n     * This has been kept for backward compatibility and use of onBeforeRenderObservable is recommended.\r\n     */\r\n    public set onBeforeRender(callback: (faceIndex: number) => void) {\r\n        if (this._onBeforeRenderObserver) {\r\n            this.onBeforeRenderObservable.remove(this._onBeforeRenderObserver);\r\n        }\r\n        this._onBeforeRenderObserver = this.onBeforeRenderObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered after rendering the texture\r\n     */\r\n    public onAfterRenderObservable = new Observable<number>();\r\n\r\n    private _onAfterRenderObserver: Nullable<Observer<number>>;\r\n    /**\r\n     * Set a after render callback in the texture.\r\n     * This has been kept for backward compatibility and use of onAfterRenderObservable is recommended.\r\n     */\r\n    public set onAfterRender(callback: (faceIndex: number) => void) {\r\n        if (this._onAfterRenderObserver) {\r\n            this.onAfterRenderObservable.remove(this._onAfterRenderObserver);\r\n        }\r\n        this._onAfterRenderObserver = this.onAfterRenderObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered after the texture clear\r\n     */\r\n    public onClearObservable = new Observable<Engine>();\r\n\r\n    private _onClearObserver: Nullable<Observer<Engine>>;\r\n    /**\r\n     * Set a clear callback in the texture.\r\n     * This has been kept for backward compatibility and use of onClearObservable is recommended.\r\n     */\r\n    public set onClear(callback: (Engine: Engine) => void) {\r\n        if (this._onClearObserver) {\r\n            this.onClearObservable.remove(this._onClearObserver);\r\n        }\r\n        this._onClearObserver = this.onClearObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered when the texture is resized.\r\n     */\r\n    public onResizeObservable = new Observable<RenderTargetTexture>();\r\n\r\n    /**\r\n     * Define the clear color of the Render Target if it should be different from the scene.\r\n     */\r\n    public clearColor: Color4;\r\n    protected _size: TextureSize;\r\n    protected _initialSizeParameter: number | { width: number; height: number } | { ratio: number };\r\n    protected _sizeRatio: Nullable<number>;\r\n    /** @internal */\r\n    public _generateMipMaps: boolean;\r\n    /** @internal */\r\n    public _cleared = false;\r\n    /**\r\n     * Skip the initial clear of the rtt at the beginning of the frame render loop\r\n     */\r\n    public skipInitialClear = false;\r\n    protected _renderingManager: RenderingManager;\r\n    /** @internal */\r\n    public _waitingRenderList?: string[];\r\n    protected _doNotChangeAspectRatio: boolean;\r\n    protected _currentRefreshId = -1;\r\n    protected _refreshRate = 1;\r\n    protected _textureMatrix: Matrix;\r\n    protected _samples = 1;\r\n    protected _renderTargetOptions: RenderTargetCreationOptions;\r\n    private _canRescale = true;\r\n    protected _renderTarget: Nullable<RenderTargetWrapper> = null;\r\n    /**\r\n     * Current render pass id of the render target texture. Note it can change over the rendering as there's a separate id for each face of a cube / each layer of an array layer!\r\n     */\r\n    public renderPassId: number;\r\n    private _renderPassIds: number[];\r\n    /**\r\n     * Gets the render pass ids used by the render target texture. For a single render target the array length will be 1, for a cube texture it will be 6 and for\r\n     * a 2D texture array it will return an array of ids the size of the 2D texture array\r\n     */\r\n    public get renderPassIds(): readonly number[] {\r\n        return this._renderPassIds;\r\n    }\r\n\r\n    /**\r\n     * Gets the current value of the refreshId counter\r\n     */\r\n    public get currentRefreshId() {\r\n        return this._currentRefreshId;\r\n    }\r\n\r\n    /**\r\n     * Sets a specific material to be used to render a mesh/a list of meshes in this render target texture\r\n     * @param mesh mesh or array of meshes\r\n     * @param material material or array of materials to use for this render pass. If undefined is passed, no specific material will be used but the regular material instead (mesh.material). It's possible to provide an array of materials to use a different material for each rendering in the case of a cube texture (6 rendering) and a 2D texture array (as many rendering as the length of the array)\r\n     */\r\n    public setMaterialForRendering(mesh: AbstractMesh | AbstractMesh[], material?: Material | Material[]): void {\r\n        let meshes;\r\n        if (!Array.isArray(mesh)) {\r\n            meshes = [mesh];\r\n        } else {\r\n            meshes = mesh;\r\n        }\r\n        for (let j = 0; j < meshes.length; ++j) {\r\n            for (let i = 0; i < this._renderPassIds.length; ++i) {\r\n                meshes[j].setMaterialForRenderPass(this._renderPassIds[i], material !== undefined ? (Array.isArray(material) ? material[i] : material) : undefined);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _isCubeData: boolean;\r\n\r\n    /**\r\n     * Define if the texture has multiple draw buffers or if false a single draw buffer.\r\n     */\r\n    public get isMulti(): boolean {\r\n        return this._renderTarget?.isMulti ?? false;\r\n    }\r\n\r\n    /**\r\n     * Gets render target creation options that were used.\r\n     */\r\n    public get renderTargetOptions(): RenderTargetCreationOptions {\r\n        return this._renderTargetOptions;\r\n    }\r\n\r\n    /**\r\n     * Gets the render target wrapper associated with this render target\r\n     */\r\n    public get renderTarget(): Nullable<RenderTargetWrapper> {\r\n        return this._renderTarget;\r\n    }\r\n\r\n    protected _onRatioRescale(): void {\r\n        if (this._sizeRatio) {\r\n            this.resize(this._initialSizeParameter);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the center of the bounding box associated with the texture (when in cube mode)\r\n     * It must define where the camera used to render the texture is set\r\n     */\r\n    public boundingBoxPosition = Vector3.Zero();\r\n\r\n    private _boundingBoxSize: Vector3;\r\n\r\n    /**\r\n     * Gets or sets the size of the bounding box associated with the texture (when in cube mode)\r\n     * When defined, the cubemap will switch to local mode\r\n     * @see https://community.arm.com/graphics/b/blog/posts/reflections-based-on-local-cubemaps-in-unity\r\n     * @example https://www.babylonjs-playground.com/#RNASML\r\n     */\r\n    public set boundingBoxSize(value: Vector3) {\r\n        if (this._boundingBoxSize && this._boundingBoxSize.equals(value)) {\r\n            return;\r\n        }\r\n        this._boundingBoxSize = value;\r\n        const scene = this.getScene();\r\n        if (scene) {\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag);\r\n        }\r\n    }\r\n    public get boundingBoxSize(): Vector3 {\r\n        return this._boundingBoxSize;\r\n    }\r\n\r\n    /**\r\n     * In case the RTT has been created with a depth texture, get the associated\r\n     * depth texture.\r\n     * Otherwise, return null.\r\n     */\r\n    public get depthStencilTexture(): Nullable<InternalTexture> {\r\n        return this._renderTarget?._depthStencilTexture ?? null;\r\n    }\r\n\r\n    /**\r\n     * Instantiate a render target texture. This is mainly used to render of screen the scene to for instance apply post process\r\n     * or used a shadow, depth texture...\r\n     * @param name The friendly name of the texture\r\n     * @param size The size of the RTT (number if square, or {width: number, height:number} or {ratio:} to define a ratio from the main scene)\r\n     * @param scene The scene the RTT belongs to. Default is the last created scene.\r\n     * @param options The options for creating the render target texture.\r\n     */\r\n    constructor(name: string, size: number | { width: number; height: number; layers?: number } | { ratio: number }, scene?: Nullable<Scene>, options?: RenderTargetTextureOptions);\r\n\r\n    /**\r\n     * Instantiate a render target texture. This is mainly used to render of screen the scene to for instance apply post process\r\n     * or used a shadow, depth texture...\r\n     * @param name The friendly name of the texture\r\n     * @param size The size of the RTT (number if square, or {width: number, height:number} or {ratio:} to define a ratio from the main scene)\r\n     * @param scene The scene the RTT belongs to. Default is the last created scene\r\n     * @param generateMipMaps True (default: false) if mipmaps need to be generated after render\r\n     * @param doNotChangeAspectRatio True (default) to not change the aspect ratio of the scene in the RTT\r\n     * @param type The type of the buffer in the RTT (byte (default), half float, float...)\r\n     * @param isCube True (default: false) if a cube texture needs to be created\r\n     * @param samplingMode The sampling mode to be used with the render target (Trilinear (default), Linear, Nearest...)\r\n     * @param generateDepthBuffer True (default) to generate a depth buffer\r\n     * @param generateStencilBuffer True (default: false) to generate a stencil buffer\r\n     * @param isMulti True (default: false) if multiple textures need to be created (Draw Buffers)\r\n     * @param format The internal format of the buffer in the RTT (RED, RG, RGB, RGBA (default), ALPHA...)\r\n     * @param delayAllocation True (default: false) if the texture allocation should be delayed\r\n     * @param samples Sample count to use when creating the RTT\r\n     * @param creationFlags specific flags to use when creating the texture (e.g., Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures)\r\n     * @param noColorAttachment True (default: false) to indicate that no color target should be created. (e.g., if you only want to write to the depth buffer)\r\n     * @param useSRGBBuffer True (default: false) to create a SRGB texture\r\n     */\r\n    constructor(\r\n        name: string,\r\n        size: number | { width: number; height: number; layers?: number } | { ratio: number },\r\n        scene?: Nullable<Scene>,\r\n        generateMipMaps?: boolean,\r\n        doNotChangeAspectRatio?: boolean,\r\n        type?: number,\r\n        isCube?: boolean,\r\n        samplingMode?: number,\r\n        generateDepthBuffer?: boolean,\r\n        generateStencilBuffer?: boolean,\r\n        isMulti?: boolean,\r\n        format?: number,\r\n        delayAllocation?: boolean,\r\n        samples?: number,\r\n        creationFlags?: number,\r\n        noColorAttachment?: boolean,\r\n        useSRGBBuffer?: boolean\r\n    );\r\n\r\n    /** @internal */\r\n    constructor(\r\n        name: string,\r\n        size: number | { width: number; height: number; layers?: number } | { ratio: number },\r\n        scene?: Nullable<Scene>,\r\n        generateMipMaps: boolean | RenderTargetTextureOptions = false,\r\n        doNotChangeAspectRatio: boolean = true,\r\n        type: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        isCube = false,\r\n        samplingMode = Texture.TRILINEAR_SAMPLINGMODE,\r\n        generateDepthBuffer = true,\r\n        generateStencilBuffer = false,\r\n        isMulti = false,\r\n        format = Constants.TEXTUREFORMAT_RGBA,\r\n        delayAllocation = false,\r\n        samples?: number,\r\n        creationFlags?: number,\r\n        noColorAttachment = false,\r\n        useSRGBBuffer = false\r\n    ) {\r\n        let colorAttachment: InternalTexture | undefined = undefined;\r\n        let gammaSpace = true;\r\n        if (typeof generateMipMaps === \"object\") {\r\n            const options = generateMipMaps;\r\n            generateMipMaps = !!options.generateMipMaps;\r\n            doNotChangeAspectRatio = options.doNotChangeAspectRatio ?? true;\r\n            type = options.type ?? Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n            isCube = !!options.isCube;\r\n            samplingMode = options.samplingMode ?? Texture.TRILINEAR_SAMPLINGMODE;\r\n            generateDepthBuffer = options.generateDepthBuffer ?? true;\r\n            generateStencilBuffer = !!options.generateStencilBuffer;\r\n            isMulti = !!options.isMulti;\r\n            format = options.format ?? Constants.TEXTUREFORMAT_RGBA;\r\n            delayAllocation = !!options.delayAllocation;\r\n            samples = options.samples;\r\n            creationFlags = options.creationFlags;\r\n            noColorAttachment = !!options.noColorAttachment;\r\n            useSRGBBuffer = !!options.useSRGBBuffer;\r\n            colorAttachment = options.colorAttachment;\r\n            gammaSpace = options.gammaSpace ?? gammaSpace;\r\n        }\r\n\r\n        super(null, scene, !generateMipMaps, undefined, samplingMode, undefined, undefined, undefined, undefined, format);\r\n\r\n        scene = this.getScene();\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        const engine = this.getScene()!.getEngine();\r\n\r\n        this._gammaSpace = gammaSpace;\r\n        this._coordinatesMode = Texture.PROJECTION_MODE;\r\n        this.renderList = [] as AbstractMesh[];\r\n        this.name = name;\r\n        this.isRenderTarget = true;\r\n        this._initialSizeParameter = size;\r\n        this._renderPassIds = [];\r\n        this._isCubeData = isCube;\r\n\r\n        this._processSizeParameter(size);\r\n\r\n        this.renderPassId = this._renderPassIds[0];\r\n\r\n        this._resizeObserver = engine.onResizeObservable.add(() => {});\r\n\r\n        this._generateMipMaps = generateMipMaps ? true : false;\r\n        this._doNotChangeAspectRatio = doNotChangeAspectRatio;\r\n\r\n        // Rendering groups\r\n        this._renderingManager = new RenderingManager(scene);\r\n        this._renderingManager._useSceneAutoClearSetup = true;\r\n\r\n        if (isMulti) {\r\n            return;\r\n        }\r\n\r\n        this._renderTargetOptions = {\r\n            generateMipMaps: generateMipMaps,\r\n            type: type,\r\n            format: this._format ?? undefined,\r\n            samplingMode: this.samplingMode,\r\n            generateDepthBuffer: generateDepthBuffer,\r\n            generateStencilBuffer: generateStencilBuffer,\r\n            samples,\r\n            creationFlags,\r\n            noColorAttachment: noColorAttachment,\r\n            useSRGBBuffer,\r\n            colorAttachment: colorAttachment,\r\n            label: this.name,\r\n        };\r\n\r\n        if (this.samplingMode === Texture.NEAREST_SAMPLINGMODE) {\r\n            this.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n            this.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n        }\r\n\r\n        if (!delayAllocation) {\r\n            if (isCube) {\r\n                this._renderTarget = scene.getEngine().createRenderTargetCubeTexture(this.getRenderSize(), this._renderTargetOptions);\r\n                this.coordinatesMode = Texture.INVCUBIC_MODE;\r\n                this._textureMatrix = Matrix.Identity();\r\n            } else {\r\n                this._renderTarget = scene.getEngine().createRenderTargetTexture(this._size, this._renderTargetOptions);\r\n            }\r\n            this._texture = this._renderTarget.texture;\r\n            if (samples !== undefined) {\r\n                this.samples = samples;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Creates a depth stencil texture.\r\n     * This is only available in WebGL 2 or with the depth texture extension available.\r\n     * @param comparisonFunction Specifies the comparison function to set on the texture. If 0 or undefined, the texture is not in comparison mode (default: 0)\r\n     * @param bilinearFiltering Specifies whether or not bilinear filtering is enable on the texture (default: true)\r\n     * @param generateStencil Specifies whether or not a stencil should be allocated in the texture (default: false)\r\n     * @param samples sample count of the depth/stencil texture (default: 1)\r\n     * @param format format of the depth texture (default: Constants.TEXTUREFORMAT_DEPTH32_FLOAT)\r\n     */\r\n    public createDepthStencilTexture(\r\n        comparisonFunction: number = 0,\r\n        bilinearFiltering: boolean = true,\r\n        generateStencil: boolean = false,\r\n        samples: number = 1,\r\n        format: number = Constants.TEXTUREFORMAT_DEPTH32_FLOAT\r\n    ): void {\r\n        this._renderTarget?.createDepthStencilTexture(comparisonFunction, bilinearFiltering, generateStencil, samples, format);\r\n    }\r\n\r\n    private _releaseRenderPassId(): void {\r\n        if (this._scene) {\r\n            const engine = this._scene.getEngine();\r\n            for (let i = 0; i < this._renderPassIds.length; ++i) {\r\n                engine.releaseRenderPassId(this._renderPassIds[i]);\r\n            }\r\n        }\r\n        this._renderPassIds = [];\r\n    }\r\n\r\n    private _createRenderPassId(): void {\r\n        this._releaseRenderPassId();\r\n\r\n        const engine = this._scene!.getEngine(); // scene can't be null in a RenderTargetTexture, see constructor\r\n        const numPasses = this._isCubeData ? 6 : this.getRenderLayers() || 1;\r\n\r\n        for (let i = 0; i < numPasses; ++i) {\r\n            this._renderPassIds[i] = engine.createRenderPassId(`RenderTargetTexture - ${this.name}#${i}`);\r\n        }\r\n    }\r\n\r\n    protected _processSizeParameter(size: number | { width: number; height: number } | { ratio: number }, createRenderPassIds = true): void {\r\n        if ((<{ ratio: number }>size).ratio) {\r\n            this._sizeRatio = (<{ ratio: number }>size).ratio;\r\n            const engine = this._getEngine()!;\r\n            this._size = {\r\n                width: this._bestReflectionRenderTargetDimension(engine.getRenderWidth(), this._sizeRatio),\r\n                height: this._bestReflectionRenderTargetDimension(engine.getRenderHeight(), this._sizeRatio),\r\n            };\r\n        } else {\r\n            this._size = <number | { width: number; height: number; layers?: number }>size;\r\n        }\r\n\r\n        if (createRenderPassIds) {\r\n            this._createRenderPassId();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Define the number of samples to use in case of MSAA.\r\n     * It defaults to one meaning no MSAA has been enabled.\r\n     */\r\n    public get samples(): number {\r\n        return this._renderTarget?.samples ?? this._samples;\r\n    }\r\n\r\n    public set samples(value: number) {\r\n        if (this._renderTarget) {\r\n            this._samples = this._renderTarget.setSamples(value);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Resets the refresh counter of the texture and start bak from scratch.\r\n     * Could be useful to regenerate the texture if it is setup to render only once.\r\n     */\r\n    public resetRefreshCounter(): void {\r\n        this._currentRefreshId = -1;\r\n    }\r\n\r\n    /**\r\n     * Define the refresh rate of the texture or the rendering frequency.\r\n     * Use 0 to render just once, 1 to render on every frame, 2 to render every two frames and so on...\r\n     */\r\n    public get refreshRate(): number {\r\n        return this._refreshRate;\r\n    }\r\n    public set refreshRate(value: number) {\r\n        this._refreshRate = value;\r\n        this.resetRefreshCounter();\r\n    }\r\n\r\n    /**\r\n     * Adds a post process to the render target rendering passes.\r\n     * @param postProcess define the post process to add\r\n     */\r\n    public addPostProcess(postProcess: PostProcess): void {\r\n        if (!this._postProcessManager) {\r\n            const scene = this.getScene();\r\n\r\n            if (!scene) {\r\n                return;\r\n            }\r\n            this._postProcessManager = new PostProcessManager(scene);\r\n            this._postProcesses = new Array<PostProcess>();\r\n        }\r\n\r\n        this._postProcesses.push(postProcess);\r\n        this._postProcesses[0].autoClear = false;\r\n    }\r\n\r\n    /**\r\n     * Clear all the post processes attached to the render target\r\n     * @param dispose define if the cleared post processes should also be disposed (false by default)\r\n     */\r\n    public clearPostProcesses(dispose: boolean = false): void {\r\n        if (!this._postProcesses) {\r\n            return;\r\n        }\r\n\r\n        if (dispose) {\r\n            for (const postProcess of this._postProcesses) {\r\n                postProcess.dispose();\r\n            }\r\n        }\r\n\r\n        this._postProcesses = [];\r\n    }\r\n\r\n    /**\r\n     * Remove one of the post process from the list of attached post processes to the texture\r\n     * @param postProcess define the post process to remove from the list\r\n     */\r\n    public removePostProcess(postProcess: PostProcess): void {\r\n        if (!this._postProcesses) {\r\n            return;\r\n        }\r\n\r\n        const index = this._postProcesses.indexOf(postProcess);\r\n\r\n        if (index === -1) {\r\n            return;\r\n        }\r\n\r\n        this._postProcesses.splice(index, 1);\r\n\r\n        if (this._postProcesses.length > 0) {\r\n            this._postProcesses[0].autoClear = false;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _shouldRender(): boolean {\r\n        if (this._currentRefreshId === -1) {\r\n            // At least render once\r\n            this._currentRefreshId = 1;\r\n            return true;\r\n        }\r\n\r\n        if (this.refreshRate === this._currentRefreshId) {\r\n            this._currentRefreshId = 1;\r\n            return true;\r\n        }\r\n\r\n        this._currentRefreshId++;\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Gets the actual render size of the texture.\r\n     * @returns the width of the render size\r\n     */\r\n    public getRenderSize(): number {\r\n        return this.getRenderWidth();\r\n    }\r\n\r\n    /**\r\n     * Gets the actual render width of the texture.\r\n     * @returns the width of the render size\r\n     */\r\n    public getRenderWidth(): number {\r\n        if ((<{ width: number; height: number }>this._size).width) {\r\n            return (<{ width: number; height: number }>this._size).width;\r\n        }\r\n\r\n        return <number>this._size;\r\n    }\r\n\r\n    /**\r\n     * Gets the actual render height of the texture.\r\n     * @returns the height of the render size\r\n     */\r\n    public getRenderHeight(): number {\r\n        if ((<{ width: number; height: number }>this._size).width) {\r\n            return (<{ width: number; height: number }>this._size).height;\r\n        }\r\n\r\n        return <number>this._size;\r\n    }\r\n\r\n    /**\r\n     * Gets the actual number of layers of the texture.\r\n     * @returns the number of layers\r\n     */\r\n    public getRenderLayers(): number {\r\n        const layers = (<{ width: number; height: number; layers?: number }>this._size).layers;\r\n        if (layers) {\r\n            return layers;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Don't allow this render target texture to rescale. Mainly used to prevent rescaling by the scene optimizer.\r\n     */\r\n    public disableRescaling() {\r\n        this._canRescale = false;\r\n    }\r\n\r\n    /**\r\n     * Get if the texture can be rescaled or not.\r\n     */\r\n    public get canRescale(): boolean {\r\n        return this._canRescale;\r\n    }\r\n\r\n    /**\r\n     * Resize the texture using a ratio.\r\n     * @param ratio the ratio to apply to the texture size in order to compute the new target size\r\n     */\r\n    public scale(ratio: number): void {\r\n        const newSize = Math.max(1, this.getRenderSize() * ratio);\r\n\r\n        this.resize(newSize);\r\n    }\r\n\r\n    /**\r\n     * Get the texture reflection matrix used to rotate/transform the reflection.\r\n     * @returns the reflection matrix\r\n     */\r\n    public getReflectionTextureMatrix(): Matrix {\r\n        if (this.isCube) {\r\n            return this._textureMatrix;\r\n        }\r\n\r\n        return super.getReflectionTextureMatrix();\r\n    }\r\n\r\n    /**\r\n     * Resize the texture to a new desired size.\r\n     * Be careful as it will recreate all the data in the new texture.\r\n     * @param size Define the new size. It can be:\r\n     *   - a number for squared texture,\r\n     *   - an object containing { width: number, height: number }\r\n     *   - or an object containing a ratio { ratio: number }\r\n     */\r\n    public resize(size: number | { width: number; height: number } | { ratio: number }): void {\r\n        const wasCube = this.isCube;\r\n\r\n        this._renderTarget?.dispose();\r\n        this._renderTarget = null;\r\n\r\n        const scene = this.getScene();\r\n\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        this._processSizeParameter(size, false);\r\n\r\n        if (wasCube) {\r\n            this._renderTarget = scene.getEngine().createRenderTargetCubeTexture(this.getRenderSize(), this._renderTargetOptions);\r\n        } else {\r\n            this._renderTarget = scene.getEngine().createRenderTargetTexture(this._size, this._renderTargetOptions);\r\n        }\r\n        this._texture = this._renderTarget.texture;\r\n\r\n        if (this._renderTargetOptions.samples !== undefined) {\r\n            this.samples = this._renderTargetOptions.samples;\r\n        }\r\n\r\n        if (this.onResizeObservable.hasObservers()) {\r\n            this.onResizeObservable.notifyObservers(this);\r\n        }\r\n    }\r\n\r\n    private _defaultRenderListPrepared: boolean;\r\n\r\n    /**\r\n     * Renders all the objects from the render list into the texture.\r\n     * @param useCameraPostProcess Define if camera post processes should be used during the rendering\r\n     * @param dumpForDebug Define if the rendering result should be dumped (copied) for debugging purpose\r\n     */\r\n    public render(useCameraPostProcess: boolean = false, dumpForDebug: boolean = false): void {\r\n        this._render(useCameraPostProcess, dumpForDebug);\r\n    }\r\n\r\n    /**\r\n     * This function will check if the render target texture can be rendered (textures are loaded, shaders are compiled)\r\n     * @returns true if all required resources are ready\r\n     */\r\n    public isReadyForRendering(): boolean {\r\n        return this._render(false, false, true);\r\n    }\r\n\r\n    private _render(useCameraPostProcess: boolean = false, dumpForDebug: boolean = false, checkReadiness: boolean = false): boolean {\r\n        const scene = this.getScene();\r\n\r\n        if (!scene) {\r\n            return checkReadiness;\r\n        }\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        if (this.useCameraPostProcesses !== undefined) {\r\n            useCameraPostProcess = this.useCameraPostProcesses;\r\n        }\r\n\r\n        if (this._waitingRenderList) {\r\n            if (!this.renderListPredicate) {\r\n                this.renderList = [];\r\n                for (let index = 0; index < this._waitingRenderList.length; index++) {\r\n                    const id = this._waitingRenderList[index];\r\n                    const mesh = scene.getMeshById(id);\r\n                    if (mesh) {\r\n                        this.renderList.push(mesh);\r\n                    }\r\n                }\r\n            }\r\n            this._waitingRenderList = undefined;\r\n        }\r\n\r\n        // Is predicate defined?\r\n        if (this.renderListPredicate) {\r\n            if (this.renderList) {\r\n                this.renderList.length = 0; // Clear previous renderList\r\n            } else {\r\n                this.renderList = [];\r\n            }\r\n\r\n            const scene = this.getScene();\r\n\r\n            if (!scene) {\r\n                return checkReadiness;\r\n            }\r\n\r\n            const sceneMeshes = scene.meshes;\r\n\r\n            for (let index = 0; index < sceneMeshes.length; index++) {\r\n                const mesh = sceneMeshes[index];\r\n                if (this.renderListPredicate(mesh)) {\r\n                    this.renderList.push(mesh);\r\n                }\r\n            }\r\n        }\r\n\r\n        const currentRenderPassId = engine.currentRenderPassId;\r\n\r\n        this.onBeforeBindObservable.notifyObservers(this);\r\n\r\n        // Set custom projection.\r\n        // Needs to be before binding to prevent changing the aspect ratio.\r\n        const camera: Nullable<Camera> = this.activeCamera ?? scene.activeCamera;\r\n        const sceneCamera = scene.activeCamera;\r\n\r\n        if (camera) {\r\n            if (camera !== scene.activeCamera) {\r\n                scene.setTransformMatrix(camera.getViewMatrix(), camera.getProjectionMatrix(true));\r\n                scene.activeCamera = camera;\r\n            }\r\n            engine.setViewport(camera.rigParent ? camera.rigParent.viewport : camera.viewport, this.getRenderWidth(), this.getRenderHeight());\r\n        }\r\n\r\n        this._defaultRenderListPrepared = false;\r\n\r\n        let returnValue = checkReadiness;\r\n\r\n        if (!checkReadiness) {\r\n            if (this.is2DArray && !this.isMulti) {\r\n                for (let layer = 0; layer < this.getRenderLayers(); layer++) {\r\n                    this._renderToTarget(0, useCameraPostProcess, dumpForDebug, layer, camera);\r\n                    scene.incrementRenderId();\r\n                    scene.resetCachedMaterial();\r\n                }\r\n            } else if (this.isCube && !this.isMulti) {\r\n                for (let face = 0; face < 6; face++) {\r\n                    this._renderToTarget(face, useCameraPostProcess, dumpForDebug, undefined, camera);\r\n                    scene.incrementRenderId();\r\n                    scene.resetCachedMaterial();\r\n                }\r\n            } else {\r\n                this._renderToTarget(0, useCameraPostProcess, dumpForDebug, undefined, camera);\r\n            }\r\n        } else {\r\n            if (!scene.getViewMatrix()) {\r\n                // We probably didn't execute scene.render() yet, so make sure we have a view/projection matrix setup for the scene\r\n                scene.updateTransformMatrix();\r\n            }\r\n            const numLayers = this.is2DArray ? this.getRenderLayers() : this.isCube ? 6 : 1;\r\n            for (let layer = 0; layer < numLayers && returnValue; layer++) {\r\n                let currentRenderList: Nullable<Array<AbstractMesh>> = null;\r\n                const defaultRenderList = this.renderList ? this.renderList : scene.getActiveMeshes().data;\r\n                const defaultRenderListLength = this.renderList ? this.renderList.length : scene.getActiveMeshes().length;\r\n\r\n                engine.currentRenderPassId = this._renderPassIds[layer];\r\n\r\n                this.onBeforeRenderObservable.notifyObservers(layer);\r\n\r\n                if (this.getCustomRenderList) {\r\n                    currentRenderList = this.getCustomRenderList(layer, defaultRenderList, defaultRenderListLength);\r\n                }\r\n\r\n                if (!currentRenderList) {\r\n                    currentRenderList = defaultRenderList;\r\n                }\r\n\r\n                if (!this._doNotChangeAspectRatio) {\r\n                    scene.updateTransformMatrix(true);\r\n                }\r\n\r\n                for (let i = 0; i < currentRenderList.length && returnValue; ++i) {\r\n                    const mesh = currentRenderList[i];\r\n\r\n                    if (!mesh.isEnabled() || mesh.isBlocked || !mesh.isVisible || !mesh.subMeshes) {\r\n                        continue;\r\n                    }\r\n\r\n                    if (this.customIsReadyFunction) {\r\n                        if (!this.customIsReadyFunction(mesh, this.refreshRate, checkReadiness)) {\r\n                            returnValue = false;\r\n                            continue;\r\n                        }\r\n                    } else if (!mesh.isReady(true)) {\r\n                        returnValue = false;\r\n                        continue;\r\n                    }\r\n                }\r\n\r\n                this.onAfterRenderObservable.notifyObservers(layer);\r\n\r\n                if (this.is2DArray || this.isCube) {\r\n                    scene.incrementRenderId();\r\n                    scene.resetCachedMaterial();\r\n                }\r\n            }\r\n        }\r\n\r\n        this.onAfterUnbindObservable.notifyObservers(this);\r\n\r\n        engine.currentRenderPassId = currentRenderPassId;\r\n\r\n        if (sceneCamera) {\r\n            scene.activeCamera = sceneCamera;\r\n            if (this.activeCamera && this.activeCamera !== scene.activeCamera) {\r\n                scene.setTransformMatrix(scene.activeCamera.getViewMatrix(), scene.activeCamera.getProjectionMatrix(true));\r\n            }\r\n            engine.setViewport(scene.activeCamera.viewport);\r\n        }\r\n\r\n        scene.resetCachedMaterial();\r\n\r\n        return returnValue;\r\n    }\r\n\r\n    private _bestReflectionRenderTargetDimension(renderDimension: number, scale: number): number {\r\n        const minimum = 128;\r\n        const x = renderDimension * scale;\r\n        const curved = Engine.NearestPOT(x + (minimum * minimum) / (minimum + x));\r\n\r\n        // Ensure we don't exceed the render dimension (while staying POT)\r\n        return Math.min(Engine.FloorPOT(renderDimension), curved);\r\n    }\r\n\r\n    private _prepareRenderingManager(currentRenderList: Array<AbstractMesh>, currentRenderListLength: number, camera: Nullable<Camera>, checkLayerMask: boolean): void {\r\n        const scene = this.getScene();\r\n\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        this._renderingManager.reset();\r\n\r\n        const sceneRenderId = scene.getRenderId();\r\n        for (let meshIndex = 0; meshIndex < currentRenderListLength; meshIndex++) {\r\n            const mesh = currentRenderList[meshIndex];\r\n\r\n            if (mesh && !mesh.isBlocked) {\r\n                if (this.customIsReadyFunction) {\r\n                    if (!this.customIsReadyFunction(mesh, this.refreshRate, false)) {\r\n                        this.resetRefreshCounter();\r\n                        continue;\r\n                    }\r\n                } else if (!mesh.isReady(this.refreshRate === 0)) {\r\n                    this.resetRefreshCounter();\r\n                    continue;\r\n                }\r\n\r\n                if (!mesh._internalAbstractMeshDataInfo._currentLODIsUpToDate && scene.activeCamera) {\r\n                    mesh._internalAbstractMeshDataInfo._currentLOD = scene.customLODSelector\r\n                        ? scene.customLODSelector(mesh, this.activeCamera || scene.activeCamera)\r\n                        : mesh.getLOD(this.activeCamera || scene.activeCamera);\r\n                    mesh._internalAbstractMeshDataInfo._currentLODIsUpToDate = true;\r\n                }\r\n                if (!mesh._internalAbstractMeshDataInfo._currentLOD) {\r\n                    continue;\r\n                }\r\n\r\n                let meshToRender = mesh._internalAbstractMeshDataInfo._currentLOD;\r\n\r\n                meshToRender._preActivateForIntermediateRendering(sceneRenderId);\r\n\r\n                let isMasked;\r\n                if (checkLayerMask && camera) {\r\n                    isMasked = (mesh.layerMask & camera.layerMask) === 0;\r\n                } else {\r\n                    isMasked = false;\r\n                }\r\n\r\n                if (mesh.isEnabled() && mesh.isVisible && mesh.subMeshes && !isMasked) {\r\n                    if (meshToRender !== mesh) {\r\n                        meshToRender._activate(sceneRenderId, true);\r\n                    }\r\n                    if (mesh._activate(sceneRenderId, true) && mesh.subMeshes.length) {\r\n                        if (!mesh.isAnInstance) {\r\n                            meshToRender._internalAbstractMeshDataInfo._onlyForInstancesIntermediate = false;\r\n                        } else {\r\n                            if (mesh._internalAbstractMeshDataInfo._actAsRegularMesh) {\r\n                                meshToRender = mesh;\r\n                            }\r\n                        }\r\n                        meshToRender._internalAbstractMeshDataInfo._isActiveIntermediate = true;\r\n\r\n                        for (let subIndex = 0; subIndex < meshToRender.subMeshes.length; subIndex++) {\r\n                            const subMesh = meshToRender.subMeshes[subIndex];\r\n                            this._renderingManager.dispatch(subMesh, meshToRender);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        for (let particleIndex = 0; particleIndex < scene.particleSystems.length; particleIndex++) {\r\n            const particleSystem = scene.particleSystems[particleIndex];\r\n\r\n            const emitter: any = particleSystem.emitter;\r\n\r\n            if (!particleSystem.isStarted() || !emitter || (emitter.position && !emitter.isEnabled())) {\r\n                continue;\r\n            }\r\n\r\n            this._renderingManager.dispatchParticles(particleSystem);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * @param faceIndex face index to bind to if this is a cubetexture\r\n     * @param layer defines the index of the texture to bind in the array\r\n     */\r\n    public _bindFrameBuffer(faceIndex: number = 0, layer = 0) {\r\n        const scene = this.getScene();\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        const engine = scene.getEngine();\r\n        if (this._renderTarget) {\r\n            engine.bindFramebuffer(this._renderTarget, this.isCube ? faceIndex : undefined, undefined, undefined, this.ignoreCameraViewport, 0, layer);\r\n        }\r\n    }\r\n\r\n    protected _unbindFrameBuffer(engine: Engine, faceIndex: number): void {\r\n        if (!this._renderTarget) {\r\n            return;\r\n        }\r\n        engine.unBindFramebuffer(this._renderTarget, this.isCube, () => {\r\n            this.onAfterRenderObservable.notifyObservers(faceIndex);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _prepareFrame(scene: Scene, faceIndex?: number, layer?: number, useCameraPostProcess?: boolean) {\r\n        if (this._postProcessManager) {\r\n            if (!this._prePassEnabled) {\r\n                this._postProcessManager._prepareFrame(this._texture, this._postProcesses);\r\n            }\r\n        } else if (!useCameraPostProcess || !scene.postProcessManager._prepareFrame(this._texture)) {\r\n            this._bindFrameBuffer(faceIndex, layer);\r\n        }\r\n    }\r\n\r\n    private _renderToTarget(faceIndex: number, useCameraPostProcess: boolean, dumpForDebug: boolean, layer = 0, camera: Nullable<Camera> = null): void {\r\n        const scene = this.getScene();\r\n\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        engine._debugPushGroup?.(`render to face #${faceIndex} layer #${layer}`, 1);\r\n\r\n        // Bind\r\n        this._prepareFrame(scene, faceIndex, layer, useCameraPostProcess);\r\n\r\n        if (this.is2DArray) {\r\n            engine.currentRenderPassId = this._renderPassIds[layer];\r\n            this.onBeforeRenderObservable.notifyObservers(layer);\r\n        } else {\r\n            engine.currentRenderPassId = this._renderPassIds[faceIndex];\r\n            this.onBeforeRenderObservable.notifyObservers(faceIndex);\r\n        }\r\n\r\n        const fastPath = engine.snapshotRendering && engine.snapshotRenderingMode === Constants.SNAPSHOTRENDERING_FAST;\r\n\r\n        if (!fastPath) {\r\n            // Get the list of meshes to render\r\n            let currentRenderList: Nullable<Array<AbstractMesh>> = null;\r\n            const defaultRenderList = this.renderList ? this.renderList : scene.getActiveMeshes().data;\r\n            const defaultRenderListLength = this.renderList ? this.renderList.length : scene.getActiveMeshes().length;\r\n\r\n            if (this.getCustomRenderList) {\r\n                currentRenderList = this.getCustomRenderList(this.is2DArray ? layer : faceIndex, defaultRenderList, defaultRenderListLength);\r\n            }\r\n\r\n            if (!currentRenderList) {\r\n                // No custom render list provided, we prepare the rendering for the default list, but check\r\n                // first if we did not already performed the preparation before so as to avoid re-doing it several times\r\n                if (!this._defaultRenderListPrepared) {\r\n                    this._prepareRenderingManager(defaultRenderList, defaultRenderListLength, camera, !this.renderList || this.forceLayerMaskCheck);\r\n                    this._defaultRenderListPrepared = true;\r\n                }\r\n                currentRenderList = defaultRenderList;\r\n            } else {\r\n                // Prepare the rendering for the custom render list provided\r\n                this._prepareRenderingManager(currentRenderList, currentRenderList.length, camera, this.forceLayerMaskCheck);\r\n            }\r\n\r\n            // Before clear\r\n            for (const step of scene._beforeRenderTargetClearStage) {\r\n                step.action(this, faceIndex, layer);\r\n            }\r\n\r\n            // Clear\r\n            if (this.onClearObservable.hasObservers()) {\r\n                this.onClearObservable.notifyObservers(engine);\r\n            } else if (!this.skipInitialClear) {\r\n                engine.clear(this.clearColor || scene.clearColor, true, true, true);\r\n            }\r\n\r\n            if (!this._doNotChangeAspectRatio) {\r\n                scene.updateTransformMatrix(true);\r\n            }\r\n\r\n            // Before Camera Draw\r\n            for (const step of scene._beforeRenderTargetDrawStage) {\r\n                step.action(this, faceIndex, layer);\r\n            }\r\n\r\n            // Render\r\n            this._renderingManager.render(this.customRenderFunction, currentRenderList, this.renderParticles, this.renderSprites);\r\n\r\n            // After Camera Draw\r\n            for (const step of scene._afterRenderTargetDrawStage) {\r\n                step.action(this, faceIndex, layer);\r\n            }\r\n\r\n            const saveGenerateMipMaps = this._texture?.generateMipMaps ?? false;\r\n\r\n            if (this._texture) {\r\n                this._texture.generateMipMaps = false; // if left true, the mipmaps will be generated (if this._texture.generateMipMaps = true) when the first post process binds its own RTT: by doing so it will unbind the current RTT,\r\n                // which will trigger a mipmap generation. We don't want this because it's a wasted work, we will do an unbind of the current RTT at the end of the process (see unbindFrameBuffer) which will\r\n                // trigger the generation of the final mipmaps\r\n            }\r\n\r\n            if (this._postProcessManager) {\r\n                this._postProcessManager._finalizeFrame(false, this._renderTarget ?? undefined, faceIndex, this._postProcesses, this.ignoreCameraViewport);\r\n            } else if (useCameraPostProcess) {\r\n                scene.postProcessManager._finalizeFrame(false, this._renderTarget ?? undefined, faceIndex);\r\n            }\r\n\r\n            for (const step of scene._afterRenderTargetPostProcessStage) {\r\n                step.action(this, faceIndex, layer);\r\n            }\r\n\r\n            if (this._texture) {\r\n                this._texture.generateMipMaps = saveGenerateMipMaps;\r\n            }\r\n\r\n            if (!this._doNotChangeAspectRatio) {\r\n                scene.updateTransformMatrix(true);\r\n            }\r\n\r\n            // Dump ?\r\n            if (dumpForDebug) {\r\n                DumpTools.DumpFramebuffer(this.getRenderWidth(), this.getRenderHeight(), engine);\r\n            }\r\n        } else {\r\n            // Clear\r\n            if (this.onClearObservable.hasObservers()) {\r\n                this.onClearObservable.notifyObservers(engine);\r\n            } else {\r\n                if (!this.skipInitialClear) {\r\n                    engine.clear(this.clearColor || scene.clearColor, true, true, true);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Unbind\r\n        this._unbindFrameBuffer(engine, faceIndex);\r\n\r\n        if (this._texture && this.isCube && faceIndex === 5) {\r\n            engine.generateMipMapsForCubemap(this._texture);\r\n        }\r\n\r\n        engine._debugPopGroup?.(1);\r\n    }\r\n\r\n    /**\r\n     * Overrides the default sort function applied in the rendering group to prepare the meshes.\r\n     * This allowed control for front to back rendering or reversely depending of the special needs.\r\n     *\r\n     * @param renderingGroupId The rendering group id corresponding to its index\r\n     * @param opaqueSortCompareFn The opaque queue comparison function use to sort.\r\n     * @param alphaTestSortCompareFn The alpha test queue comparison function use to sort.\r\n     * @param transparentSortCompareFn The transparent queue comparison function use to sort.\r\n     */\r\n    public setRenderingOrder(\r\n        renderingGroupId: number,\r\n        opaqueSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null,\r\n        alphaTestSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null,\r\n        transparentSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null\r\n    ): void {\r\n        this._renderingManager.setRenderingOrder(renderingGroupId, opaqueSortCompareFn, alphaTestSortCompareFn, transparentSortCompareFn);\r\n    }\r\n\r\n    /**\r\n     * Specifies whether or not the stencil and depth buffer are cleared between two rendering groups.\r\n     *\r\n     * @param renderingGroupId The rendering group id corresponding to its index\r\n     * @param autoClearDepthStencil Automatically clears depth and stencil between groups if true.\r\n     */\r\n    public setRenderingAutoClearDepthStencil(renderingGroupId: number, autoClearDepthStencil: boolean): void {\r\n        this._renderingManager.setRenderingAutoClearDepthStencil(renderingGroupId, autoClearDepthStencil);\r\n        this._renderingManager._useSceneAutoClearSetup = false;\r\n    }\r\n\r\n    /**\r\n     * Clones the texture.\r\n     * @returns the cloned texture\r\n     */\r\n    public clone(): RenderTargetTexture {\r\n        const textureSize = this.getSize();\r\n        const newTexture = new RenderTargetTexture(\r\n            this.name,\r\n            textureSize,\r\n            this.getScene(),\r\n            this._renderTargetOptions.generateMipMaps,\r\n            this._doNotChangeAspectRatio,\r\n            this._renderTargetOptions.type,\r\n            this.isCube,\r\n            this._renderTargetOptions.samplingMode,\r\n            this._renderTargetOptions.generateDepthBuffer,\r\n            this._renderTargetOptions.generateStencilBuffer,\r\n            undefined,\r\n            this._renderTargetOptions.format,\r\n            undefined,\r\n            this._renderTargetOptions.samples\r\n        );\r\n\r\n        // Base texture\r\n        newTexture.hasAlpha = this.hasAlpha;\r\n        newTexture.level = this.level;\r\n\r\n        // RenderTarget Texture\r\n        newTexture.coordinatesMode = this.coordinatesMode;\r\n        if (this.renderList) {\r\n            newTexture.renderList = this.renderList.slice(0);\r\n        }\r\n\r\n        return newTexture;\r\n    }\r\n\r\n    /**\r\n     * Serialize the texture to a JSON representation we can easily use in the respective Parse function.\r\n     * @returns The JSON representation of the texture\r\n     */\r\n    public serialize(): any {\r\n        if (!this.name) {\r\n            return null;\r\n        }\r\n\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.renderTargetSize = this.getRenderSize();\r\n        serializationObject.renderList = [];\r\n\r\n        if (this.renderList) {\r\n            for (let index = 0; index < this.renderList.length; index++) {\r\n                serializationObject.renderList.push(this.renderList[index].id);\r\n            }\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     *  This will remove the attached framebuffer objects. The texture will not be able to be used as render target anymore\r\n     */\r\n    public disposeFramebufferObjects(): void {\r\n        this._renderTarget?.dispose(true);\r\n    }\r\n\r\n    /**\r\n     * Release and destroy the underlying lower level texture aka internalTexture.\r\n     */\r\n    public releaseInternalTexture(): void {\r\n        this._renderTarget?.releaseTextures();\r\n        this._texture = null;\r\n    }\r\n\r\n    /**\r\n     * Dispose the texture and release its associated resources.\r\n     */\r\n    public dispose(): void {\r\n        this.onResizeObservable.clear();\r\n        this.onClearObservable.clear();\r\n        this.onAfterRenderObservable.clear();\r\n        this.onAfterUnbindObservable.clear();\r\n        this.onBeforeBindObservable.clear();\r\n        this.onBeforeRenderObservable.clear();\r\n\r\n        if (this._postProcessManager) {\r\n            this._postProcessManager.dispose();\r\n            this._postProcessManager = null;\r\n        }\r\n\r\n        if (this._prePassRenderTarget) {\r\n            this._prePassRenderTarget.dispose();\r\n        }\r\n\r\n        this._releaseRenderPassId();\r\n        this.clearPostProcesses(true);\r\n\r\n        if (this._resizeObserver) {\r\n            this.getScene()!.getEngine().onResizeObservable.remove(this._resizeObserver);\r\n            this._resizeObserver = null;\r\n        }\r\n\r\n        this.renderList = null;\r\n\r\n        // Remove from custom render targets\r\n        const scene = this.getScene();\r\n\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        let index = scene.customRenderTargets.indexOf(this);\r\n\r\n        if (index >= 0) {\r\n            scene.customRenderTargets.splice(index, 1);\r\n        }\r\n\r\n        for (const camera of scene.cameras) {\r\n            index = camera.customRenderTargets.indexOf(this);\r\n\r\n            if (index >= 0) {\r\n                camera.customRenderTargets.splice(index, 1);\r\n            }\r\n        }\r\n\r\n        this._renderTarget?.dispose();\r\n        this._renderTarget = null;\r\n        this._texture = null;\r\n\r\n        super.dispose();\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuild(): void {\r\n        if (this.refreshRate === RenderTargetTexture.REFRESHRATE_RENDER_ONCE) {\r\n            this.refreshRate = RenderTargetTexture.REFRESHRATE_RENDER_ONCE;\r\n        }\r\n\r\n        if (this._postProcessManager) {\r\n            this._postProcessManager._rebuild();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clear the info related to rendering groups preventing retention point in material dispose.\r\n     */\r\n    public freeRenderingGroups(): void {\r\n        if (this._renderingManager) {\r\n            this._renderingManager.freeRenderingGroups();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the number of views the corresponding to the texture (eg. a MultiviewRenderTarget will have > 1)\r\n     * @returns the view count\r\n     */\r\n    public getViewCount() {\r\n        return 1;\r\n    }\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nTexture._CreateRenderTargetTexture = (name: string, renderTargetSize: number, scene: Scene, generateMipMaps: boolean, creationFlags?: number) => {\r\n    return new RenderTargetTexture(name, renderTargetSize, scene, generateMipMaps);\r\n};\r\n"]}