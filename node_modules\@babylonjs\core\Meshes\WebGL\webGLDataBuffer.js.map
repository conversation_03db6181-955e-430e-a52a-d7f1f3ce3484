{"version": 3, "file": "webGLDataBuffer.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/WebGL/webGLDataBuffer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAGtD,gBAAgB;AAChB,MAAM,OAAO,eAAgB,SAAQ,UAAU;IAG3C,YAAmB,QAAqB;QACpC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;CACJ", "sourcesContent": ["import { DataBuffer } from \"../../Buffers/dataBuffer\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/** @internal */\r\nexport class WebGLDataBuffer extends DataBuffer {\r\n    private _buffer: Nullable<WebGLBuffer>;\r\n\r\n    public constructor(resource: WebGLBuffer) {\r\n        super();\r\n        this._buffer = resource;\r\n    }\r\n\r\n    public get underlyingResource(): any {\r\n        return this._buffer;\r\n    }\r\n}\r\n"]}