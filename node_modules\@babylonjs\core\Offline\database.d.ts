import type { IOfflineProvider } from "./IOfflineProvider";
/**
 * Class used to enable access to IndexedDB
 * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeCached
 */
export declare class Database implements IOfflineProvider {
    private _currentSceneUrl;
    private _db;
    private _enableSceneOffline;
    private _enableTexturesOffline;
    private _manifestVersionFound;
    private _mustUpdateRessources;
    private _hasReachedQuota;
    private _isSupported;
    private _idbFactory;
    /** Gets a boolean indicating if the user agent supports blob storage (this value will be updated after creating the first Database object) */
    private static _IsUASupportingBlobStorage;
    /**
     * Gets a boolean indicating if Database storage is enabled (off by default)
     */
    static IDBStorageEnabled: boolean;
    /**
     * Gets a boolean indicating if scene must be saved in the database
     */
    get enableSceneOffline(): boolean;
    /**
     * Gets a boolean indicating if textures must be saved in the database
     */
    get enableTexturesOffline(): boolean;
    /**
     * Creates a new Database
     * @param urlToScene defines the url to load the scene
     * @param callbackManifestChecked defines the callback to use when manifest is checked
     * @param disableManifestCheck defines a boolean indicating that we want to skip the manifest validation (it will be considered validated and up to date)
     */
    constructor(urlToScene: string, callbackManifestChecked: (checked: boolean) => any, disableManifestCheck?: boolean);
    private static _ParseURL;
    private static _ReturnFullUrlLocation;
    private _checkManifestFile;
    /**
     * Open the database and make it available
     * @param successCallback defines the callback to call on success
     * @param errorCallback defines the callback to call on error
     */
    open(successCallback: () => void, errorCallback: () => void): void;
    /**
     * Loads an image from the database
     * @param url defines the url to load from
     * @param image defines the target DOM image
     */
    loadImage(url: string, image: HTMLImageElement): void;
    private _loadImageFromDBAsync;
    private _saveImageIntoDBAsync;
    private _checkVersionFromDB;
    private _loadVersionFromDBAsync;
    private _saveVersionIntoDBAsync;
    /**
     * Loads a file from database
     * @param url defines the URL to load from
     * @param sceneLoaded defines a callback to call on success
     * @param progressCallBack defines a callback to call when progress changed
     * @param errorCallback defines a callback to call on error
     * @param useArrayBuffer defines a boolean to use array buffer instead of text string
     */
    loadFile(url: string, sceneLoaded: (data: any) => void, progressCallBack?: (data: any) => void, errorCallback?: () => void, useArrayBuffer?: boolean): void;
    private _loadFileAsync;
    private _saveFileAsync;
    /**
     * Validates if xhr data is correct
     * @param xhr defines the request to validate
     * @param dataType defines the expected data type
     * @returns true if data is correct
     */
    private static _ValidateXHRData;
}
