{"version": 3, "file": "planeBlock.js", "sourceRoot": "", "sources": ["../../../../../../../dev/core/src/Meshes/Node/Blocks/Sources/planeBlock.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,qBAAqB,EAAE,MAAM,0CAA0C,CAAC;AACjF,OAAO,EAAE,qCAAqC,EAAE,MAAM,8CAA8C,CAAC;AACrG,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAG5D,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAEtG;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,iBAAiB;IAQ7C;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAZhB;;;WAGG;QAEI,oBAAe,GAAG,KAAK,CAAC;QAS3B,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACjF,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAClF,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,qCAAqC,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAEnF,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,qCAAqC,CAAC,QAAQ,CAAC,CAAC;IACpF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa;QAChB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACvB,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACrD,MAAM,SAAS,GAAG,IAAI,kBAAkB,CAAC,MAAM,CAAC,CAAC;YACjD,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;YACpB,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YACzB,MAAM,UAAU,GAAG,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACnD,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;YACrB,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC1B,MAAM,WAAW,GAAG,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YACrD,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;YACtB,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC7C;IACL,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,MAAM,OAAO,GAAwH,EAAE,CAAC;QACxI,MAAM,IAAI,GAAG,CAAC,KAA6B,EAAE,EAAE;YAC3C,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAClD,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACpD,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAEtD,4CAA4C;YAC5C,OAAO,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;SACxC;aAAM;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1B,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,GAAG,EAAE;gBACjC,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,CAAC,CAAC;gBAClC,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;YACzB,CAAC,CAAC;SACL;IACL,CAAC;IAES,mBAAmB;QACzB,MAAM,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,GAAG,GAAG,IAAI,CAAC,iBAAiB,sBAAsB,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC;QAC7I,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAE3D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB;QACxC,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAExC,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC,eAAe,CAAC;IAC/D,CAAC;CACJ;AAzHU;IADN,sBAAsB,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC;mDAC1F;AA2HnC,aAAa,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC", "sourcesContent": ["import { CreatePlaneVertexData } from \"../../../../Meshes/Builders/planeBuilder\";\r\nimport { NodeGeometryBlockConnectionPointTypes } from \"../../Enums/nodeGeometryConnectionPointTypes\";\r\nimport { NodeGeometryBlock } from \"../../nodeGeometryBlock\";\r\nimport type { NodeGeometryConnectionPoint } from \"../../nodeGeometryBlockConnectionPoint\";\r\nimport type { NodeGeometryBuildState } from \"../../nodeGeometryBuildState\";\r\nimport { GeometryInputBlock } from \"../geometryInputBlock\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Vector4 } from \"../../../../Maths/math.vector\";\r\nimport { PropertyTypeForEdition, editableInPropertyPage } from \"../../../../Decorators/nodeDecorator\";\r\n\r\n/**\r\n * Defines a block used to generate plane geometry data\r\n */\r\nexport class PlaneBlock extends NodeGeometryBlock {\r\n    /**\r\n     * Gets or sets a boolean indicating that this block can evaluate context\r\n     * Build performance is improved when this value is set to false as the system will cache values instead of reevaluating everything per context change\r\n     */\r\n    @editableInPropertyPage(\"Evaluate context\", PropertyTypeForEdition.Boolean, \"ADVANCED\", { notifiers: { rebuild: true } })\r\n    public evaluateContext = false;\r\n\r\n    /**\r\n     * Create a new PlaneBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"size\", NodeGeometryBlockConnectionPointTypes.Float, true, 1);\r\n        this.registerInput(\"width\", NodeGeometryBlockConnectionPointTypes.Float, true, 0);\r\n        this.registerInput(\"height\", NodeGeometryBlockConnectionPointTypes.Float, true, 0);\r\n\r\n        this.registerOutput(\"geometry\", NodeGeometryBlockConnectionPointTypes.Geometry);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"PlaneBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the size input component\r\n     */\r\n    public get size(): NodeGeometryConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the width input component\r\n     */\r\n    public get width(): NodeGeometryConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the height input component\r\n     */\r\n    public get height(): NodeGeometryConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the geometry output component\r\n     */\r\n    public get geometry(): NodeGeometryConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public autoConfigure() {\r\n        if (this.size.isConnected) {\r\n            return;\r\n        }\r\n\r\n        if (!this.width.isConnected && !this.height.isConnected) {\r\n            const sizeInput = new GeometryInputBlock(\"Size\");\r\n            sizeInput.value = 1;\r\n            sizeInput.output.connectTo(this.size);\r\n            return;\r\n        }\r\n\r\n        if (!this.width.isConnected) {\r\n            const widthInput = new GeometryInputBlock(\"Width\");\r\n            widthInput.value = 1;\r\n            widthInput.output.connectTo(this.width);\r\n        }\r\n\r\n        if (!this.height.isConnected) {\r\n            const heightInput = new GeometryInputBlock(\"Height\");\r\n            heightInput.value = 1;\r\n            heightInput.output.connectTo(this.height);\r\n        }\r\n    }\r\n\r\n    protected _buildBlock(state: NodeGeometryBuildState) {\r\n        const options: { size?: number; width?: number; height?: number; sideOrientation?: number; frontUVs?: Vector4; backUVs?: Vector4 } = {};\r\n        const func = (state: NodeGeometryBuildState) => {\r\n            options.size = this.size.getConnectedValue(state);\r\n            options.width = this.width.getConnectedValue(state);\r\n            options.height = this.height.getConnectedValue(state);\r\n\r\n            // Append vertex data from the plane builder\r\n            return CreatePlaneVertexData(options);\r\n        };\r\n\r\n        if (this.evaluateContext) {\r\n            this.geometry._storedFunction = func;\r\n        } else {\r\n            const value = func(state);\r\n            this.geometry._storedFunction = () => {\r\n                this.geometry._executionCount = 1;\r\n                return value.clone();\r\n            };\r\n        }\r\n    }\r\n\r\n    protected _dumpPropertiesCode() {\r\n        const codeString = super._dumpPropertiesCode() + `${this._codeVariableName}.evaluateContext = ${this.evaluateContext ? \"true\" : \"false\"};\\n`;\r\n        return codeString;\r\n    }\r\n\r\n    /**\r\n     * Serializes this block in a JSON representation\r\n     * @returns the serialized block object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.evaluateContext = this.evaluateContext;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any) {\r\n        super._deserialize(serializationObject);\r\n\r\n        this.evaluateContext = serializationObject.evaluateContext;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PlaneBlock\", PlaneBlock);\r\n"]}