{"version": 3, "file": "webXRWebGLLayer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/XR/webXRWebGLLayer.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,qCAAqC,EAAE,MAAM,oCAAoC,CAAC;AAE3F;;;GAGG;AACH,MAAM,OAAO,sBAAuB,SAAQ,iBAAiB;IACzD;;;OAGG;IACH,YAA4B,KAAmB;QAC3C,KAAK,CACD,GAAG,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAC5B,GAAG,EAAE,CAAC,KAAK,CAAC,iBAAiB,EAC7B,KAAK,EACL,cAAc,EACd,CAAC,cAAc,EAAE,EAAE,CAAC,IAAI,0CAA0C,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CACjG,CAAC;QAPsB,UAAK,GAAL,KAAK,CAAc;IAQ/C,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,0CAA2C,SAAQ,qCAAqC;IAOjG,YACI,KAAY,EACI,YAAoC;QAEpD,KAAK,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAFX,iBAAY,GAAZ,YAAY,CAAwB;QAGpD,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,sBAAsB,GAAG;YAC1B,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC9C,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB;SACnD,CAAC;IACN,CAAC;IAEM,qBAAqB,CAAC,QAAkB,EAAE,IAAY;QACzD,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,UAAU,EAAE;YACb,OAAO,KAAK,CAAC;SAChB;QACD,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC;QACtE,MAAM,iBAAiB,GAAG,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC;QACxE,QAAQ,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,gBAAgB,CAAC;QAC7C,QAAQ,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,iBAAiB,CAAC;QAC9C,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG,gBAAgB,CAAC;QACrD,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,iBAAiB,CAAC;QACxD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,6DAA6D;IACtD,4BAA4B,CAAC,GAAU;QAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAChD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;QAClD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QAE5C,IACI,CAAC,IAAI,CAAC,IAAI;YACV,UAAU,KAAK,IAAI,CAAC,sBAAsB,CAAC,gBAAgB;YAC3D,WAAW,KAAK,IAAI,CAAC,sBAAsB,CAAC,iBAAiB;YAC7D,WAAW,KAAK,IAAI,CAAC,YAAY,EACnC;YACE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;YAClF,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,GAAG,UAAU,CAAC;YAC1D,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,GAAG,WAAW,CAAC;YAC5D,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;SACnC;QAED,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAEM,6BAA6B,CAAC,IAAY;QAC7C,OAAO,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvD,CAAC;CACJ", "sourcesContent": ["import type { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport type { Viewport } from \"../Maths/math.viewport\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Nullable } from \"../types\";\r\nimport { WebXRLayerWrapper } from \"./webXRLayerWrapper\";\r\nimport { WebXRLayerRenderTargetTextureProvider } from \"./webXRRenderTargetTextureProvider\";\r\n\r\n/**\r\n * Wraps xr webgl layers.\r\n * @internal\r\n */\r\nexport class WebXRWebGLLayerWrapper extends WebXRLayerWrapper {\r\n    /**\r\n     * @param layer is the layer to be wrapped.\r\n     * @returns a new WebXRLayerWrapper wrapping the provided XRWebGLLayer.\r\n     */\r\n    constructor(public readonly layer: XRWebGLLayer) {\r\n        super(\r\n            () => layer.framebufferWidth,\r\n            () => layer.framebufferHeight,\r\n            layer,\r\n            \"XRWebGLLayer\",\r\n            (sessionManager) => new WebXRWebGLLayerRenderTargetTextureProvider(sessionManager.scene, this)\r\n        );\r\n    }\r\n}\r\n\r\n/**\r\n * Provides render target textures and other important rendering information for a given XRWebGLLayer.\r\n * @internal\r\n */\r\nexport class WebXRWebGLLayerRenderTargetTextureProvider extends WebXRLayerRenderTargetTextureProvider {\r\n    // The dimensions will always be defined in this class.\r\n    protected _framebufferDimensions: { framebufferWidth: number; framebufferHeight: number };\r\n    private _rtt: Nullable<RenderTargetTexture>;\r\n    private _framebuffer: WebGLFramebuffer;\r\n    private _layer: XRWebGLLayer;\r\n\r\n    constructor(\r\n        scene: Scene,\r\n        public readonly layerWrapper: WebXRWebGLLayerWrapper\r\n    ) {\r\n        super(scene, layerWrapper);\r\n        this._layer = layerWrapper.layer;\r\n        this._framebufferDimensions = {\r\n            framebufferWidth: this._layer.framebufferWidth,\r\n            framebufferHeight: this._layer.framebufferHeight,\r\n        };\r\n    }\r\n\r\n    public trySetViewportForView(viewport: Viewport, view: XRView): boolean {\r\n        const xrViewport = this._layer.getViewport(view);\r\n        if (!xrViewport) {\r\n            return false;\r\n        }\r\n        const framebufferWidth = this._framebufferDimensions.framebufferWidth;\r\n        const framebufferHeight = this._framebufferDimensions.framebufferHeight;\r\n        viewport.x = xrViewport.x / framebufferWidth;\r\n        viewport.y = xrViewport.y / framebufferHeight;\r\n        viewport.width = xrViewport.width / framebufferWidth;\r\n        viewport.height = xrViewport.height / framebufferHeight;\r\n        return true;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public getRenderTargetTextureForEye(eye: XREye): Nullable<RenderTargetTexture> {\r\n        const layerWidth = this._layer.framebufferWidth;\r\n        const layerHeight = this._layer.framebufferHeight;\r\n        const framebuffer = this._layer.framebuffer;\r\n\r\n        if (\r\n            !this._rtt ||\r\n            layerWidth !== this._framebufferDimensions.framebufferWidth ||\r\n            layerHeight !== this._framebufferDimensions.framebufferHeight ||\r\n            framebuffer !== this._framebuffer\r\n        ) {\r\n            this._rtt = this._createRenderTargetTexture(layerWidth, layerHeight, framebuffer);\r\n            this._framebufferDimensions.framebufferWidth = layerWidth;\r\n            this._framebufferDimensions.framebufferHeight = layerHeight;\r\n            this._framebuffer = framebuffer;\r\n        }\r\n\r\n        return this._rtt;\r\n    }\r\n\r\n    public getRenderTargetTextureForView(view: XRView): Nullable<RenderTargetTexture> {\r\n        return this.getRenderTargetTextureForEye(view.eye);\r\n    }\r\n}\r\n"]}