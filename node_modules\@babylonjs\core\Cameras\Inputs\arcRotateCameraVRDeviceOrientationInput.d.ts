import type { ArcRotateCamera } from "../../Cameras/arcRotateCamera";
import type { ICameraInput } from "../../Cameras/cameraInputsManager";
declare module "../../Cameras/arcRotateCameraInputsManager" {
    interface ArcRotateCameraInputsManager {
        /**
         * Add orientation input support to the input manager.
         * @returns the current input manager
         */
        addVRDeviceOrientation(): ArcRotateCameraInputsManager;
    }
}
/**
 * Manage the device orientation inputs (gyroscope) to control an arc rotate camera.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs
 */
export declare class ArcRotateCameraVRDeviceOrientationInput implements ICameraInput<ArcRotateCamera> {
    /**
     * Defines the camera the input is attached to.
     */
    camera: ArcRotateCamera;
    /**
     * Defines a correction factor applied on the alpha value retrieved from the orientation events.
     */
    alphaCorrection: number;
    /**
     * Defines a correction factor applied on the gamma value retrieved from the orientation events.
     */
    gammaCorrection: number;
    private _alpha;
    private _gamma;
    private _dirty;
    private _deviceOrientationHandler;
    /**
     * Instantiate a new ArcRotateCameraVRDeviceOrientationInput.
     */
    constructor();
    /**
     * Attach the input controls to a specific dom element to get the input from.
     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)
     */
    attachControl(noPreventDefault?: boolean): void;
    /**
     * @internal
     */
    _onOrientationEvent(evt: DeviceOrientationEvent): void;
    /**
     * Update the current camera state depending on the inputs that have been used this frame.
     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.
     */
    checkInputs(): void;
    /**
     * Detach the current controls from the specified dom element.
     */
    detachControl(): void;
    /**
     * Gets the class name of the current input.
     * @returns the class name
     */
    getClassName(): string;
    /**
     * Get the friendly name associated with the input class.
     * @returns the input friendly name
     */
    getSimpleName(): string;
}
