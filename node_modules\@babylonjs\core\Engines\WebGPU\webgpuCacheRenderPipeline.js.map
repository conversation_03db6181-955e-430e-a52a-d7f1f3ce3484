{"version": 3, "file": "webgpuCacheRenderPipeline.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuCacheRenderPipeline.ts"], "names": [], "mappings": "AAAA,yDAAyD;AACzD,wCAAwC;AACxC,wCAAwC;AACxC,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,KAAK,eAAe,MAAM,mBAAmB,CAAC;AAGrD,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAKpD,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,8BAA8B,EAAE,MAAM,wBAAwB,CAAC;AAExE,IAAK,aAgBJ;AAhBD,WAAK,aAAa;IACd,uEAAmB,CAAA;IACnB,yEAAoB,CAAA;IACpB,+DAA+D;IAC/D,2DAAa,CAAA;IACb,+EAAuB,CAAA;IACvB,2EAAqB,CAAA;IACrB,uEAAmB,CAAA;IACnB,uEAAmB,CAAA;IACnB,6EAAsB,CAAA;IACtB,+DAAe,CAAA;IACf,+DAAe,CAAA;IACf,kEAAiB,CAAA;IACjB,gEAAgB,CAAA;IAEhB,4DAAc,CAAA;AAClB,CAAC,EAhBI,aAAa,KAAb,aAAa,QAgBjB;AAED,MAAM,uBAAuB,GAA+B;IACxD,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE,EAAE,6BAA6B;CAC5C,CAAC;AAEF,MAAM,gBAAgB,GAA+B;IACjD,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC,EAAE,YAAY;CAC1B,CAAC;AAEF,MAAM,qCAAqC,GAAgC;IACvE,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,IAAI;IACjC,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,IAAI;IAC/B,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,IAAI;IAChC,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,IAAI;IAC3B,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,IAAI;IAC5B,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,IAAI;IAC5B,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,IAAI;IAC5B,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,IAAI;IAC5B,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,IAAI;IAC5B,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,IAAI;IAC9B,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE,IAAI;IACtC,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE,IAAI;IACxC,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE,IAAI;IACxC,CAAC,YAAY,CAAC,wBAAwB,CAAC,EAAE,IAAI;IAC7C,CAAC,YAAY,CAAC,wBAAwB,CAAC,EAAE,IAAI;CAChD,CAAC;AAEF,gBAAgB;AAChB,MAAM,OAAgB,yBAAyB;IA6DnC,MAAM,CAAC,aAAa,CAAC,IAAY;QACrC,QAAQ,IAAI,EAAE;YACV,KAAK,YAAY,CAAC,IAAI,CAAC;YACvB,KAAK,YAAY,CAAC,KAAK,CAAC;YACxB,KAAK,YAAY,CAAC,GAAG,CAAC;YACtB,KAAK,YAAY,CAAC,KAAK;gBACnB,OAAO,IAAI,CAAC;YAChB,KAAK,YAAY,CAAC,aAAa,CAAC;YAChC,KAAK,YAAY,CAAC,cAAc,CAAC;YACjC,KAAK,YAAY,CAAC,YAAY;gBAC1B,OAAO,KAAK,CAAC;YACjB;gBACI,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,GAAG,CAAC,CAAC;SACjD;IACL,CAAC;IAED,YAAY,MAAiB,EAAE,iBAA+B;QA+C9C,oBAAe,GAAW,CAAC,CAAC;QA9CxC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAC,0KAA0K;QACxM,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,gFAAgF;QAC9G,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;QACvD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,MAAM,CAAC,0BAA0B,IAAI,IAAI,CAAC;QAChF,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrB,4BAA4B;QAC5B,IAAI,CAAC,kBAAkB,GAAG,CAAC,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACrE,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAC9D,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAChB,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,oBAAoB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAC9E,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IAOD,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC;IAClF,CAAC;IAMM,iBAAiB,CAAC,QAAgB,EAAE,MAAc,EAAE,WAAmB,EAAE,YAAY,GAAG,CAAC;QAC5F,WAAW,GAAG,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAEzD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,MAAM,QAAQ,GAAG,yBAAyB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAElE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,+CAA+C;YAC7E,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YAEpC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;YAErF,yBAAyB,CAAC,YAAY,EAAE,CAAC;YACzC,yBAAyB,CAAC,gCAAgC,EAAE,CAAC;YAE7D,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;SACnC;QAED,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QACnD,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAEpC,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAE7D,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;YAC5C,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC;YACjD,yBAAyB,CAAC,sBAAsB,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;SACnC;QAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEzC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC;QAEjD,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;YAC1B,yBAAyB,CAAC,mBAAmB,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;SACnC;QAED,MAAM,QAAQ,GAAG,yBAAyB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAElE,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QACrF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEzC,yBAAyB,CAAC,YAAY,EAAE,CAAC;QACzC,yBAAyB,CAAC,gCAAgC,EAAE,CAAC;QAE7D,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;IACpC,CAAC;IAEM,QAAQ;QACX,yBAAyB,CAAC,4BAA4B,GAAG,yBAAyB,CAAC,gCAAgC,CAAC;QACpH,yBAAyB,CAAC,gCAAgC,GAAG,CAAC,CAAC;IACnE,CAAC;IAEM,kBAAkB,CAAC,OAAgB;QACtC,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC;IAC3C,CAAC;IAEM,YAAY,CAAC,SAAiB;QACjC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAChC,CAAC;IAEM,cAAc,CAAC,OAAgB;QAClC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;IAChC,CAAC;IAEM,WAAW,CAAC,QAAgB;QAC/B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;IAEM,aAAa,CAAC,UAAmB;QACpC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAClC,CAAC;IAEM,sBAAsB;QACzB,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;IAC/E,CAAC;IAEM,oBAAoB,CACvB,WAAoB,EACpB,SAAiB,EACjB,QAAgB,EAChB,OAAe,EACf,YAAoB,EACpB,gBAAyB,EACzB,iBAA0B,EAC1B,YAA8B;QAE9B,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,aAAa,GAAG,CAAC,YAAY,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;QACjE,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IACpC,CAAC;IAEM,YAAY,CAAC,SAAiB;QACjC,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;YAC/B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;YAC5B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;YAClD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;SAChG;IACL,CAAC;IAED;;;;;;OAMG;IAEI,sBAAsB,CAAC,mBAA2B;QACrD,IAAI,IAAI,CAAC,oBAAoB,KAAK,mBAAmB,EAAE;YACnD,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,mBAAmB,CAAC,GAAG,mBAAmB,CAAC;YACtE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,mBAAmB,CAAC,CAAC;SAC1G;IACL,CAAC;IAEM,cAAc,CAAC,MAA+B;QACjD,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,8BAA8B,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;IACrE,CAAC;IAEM,iBAAiB,CAAC,WAAqB;QACzC,IAAI,CAAC,cAAsB,GAAG,WAAW,CAAC;QAC3C,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACzC,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBACtB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;aAClB;SACJ;QACD,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;YAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,eAAe,CAAC,CAAC;SACtG;IACL,CAAC;IAEM,MAAM,CAAC,YAA+B,EAAE,YAAqB;QAChE,YAAY,GAAG,YAAY,IAAI,YAAY,CAAC,MAAM,CAAC;QACnD,IAAI,YAAY,GAAG,EAAE,EAAE;YACnB,iLAAiL;YACjL,qIAAqI;YACrI,qDAAqD;YACrD,4CAA4C;YAC5C,MAAM,2EAA2E,CAAC;SACrF;QACA,IAAI,CAAC,eAAuB,GAAG,YAAY,CAAC;QAC5C,IAAI,CAAC,eAAuB,GAAG,YAAY,CAAC;QAE7C,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,CAAC,gHAAgH;QAE/I,MAAM,IAAI,GAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9B,IAAI,SAAS,GAAG,CAAC,EACb,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,EAAE,CAAC,EAAE;YACnC,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,UAAU,GAAG,OAAO,EAAE,gBAAmD,CAAC;YAEhF,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,UAAU,EAAE,MAAM,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAE3E,IAAI,CAAC,SAAS,CAAC,IAAI,8BAA8B,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC;YACzF,IAAI,IAAI,CAAC,CAAC;YACV,KAAK,EAAE,CAAC;YAER,IAAI,IAAI,IAAI,EAAE,EAAE;gBACZ,IAAI,GAAG,CAAC,CAAC;gBACT,SAAS,EAAE,CAAC;aACf;SACJ;QACD,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC;QAChC,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;YACxE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACtD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,eAAe,CAAC,CAAC;SACtG;IACL,CAAC;IAEM,oBAAoB,CAAC,OAAgB;QACxC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;IACtC,CAAC;IAEM,oBAAoB,CAAC,OAAgC,EAAE,UAAmC;QAC7F,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC;QACrC,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC;IAC1C,CAAC;IAEM,YAAY,CAAC,IAAY;QAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC3B,CAAC;IAEM,qBAAqB,CAAC,MAAoC;QAC7D,IAAI,CAAC,yBAAyB,GAAG,MAAM,CAAC;QACxC,IAAI,CAAC,mBAAmB,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;IACjG,CAAC;IAEM,mBAAmB,CAAC,OAAgB;QACvC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC;IACrC,CAAC;IAEM,oBAAoB,CAAC,OAAgB;QACxC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;IACtC,CAAC;IAEM,eAAe,CAAC,IAAsB;QACzC,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IAC7D,CAAC;IAEM,iBAAiB,CAAC,OAAgB;QACrC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;IACnC,CAAC;IAEM,iBAAiB,CAAC,IAAsB;QAC3C,IAAI,CAAC,oBAAoB,GAAG,CAAC,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IACpE,CAAC;IAEM,qBAAqB,CAAC,EAAoB;QAC7C,IAAI,CAAC,wBAAwB,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACtF,CAAC;IAEM,gBAAgB,CAAC,EAAoB;QACxC,IAAI,CAAC,mBAAmB,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACpF,CAAC;IAEM,gBAAgB,CAAC,EAAoB;QACxC,IAAI,CAAC,mBAAmB,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACjF,CAAC;IAEM,kBAAkB,CAAC,IAAY;QAClC,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;YAChC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;YACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,eAAe,CAAC,CAAC;SACtG;IACL,CAAC;IAEM,mBAAmB,CAAC,IAAY;QACnC,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE;YACjC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;YACpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,gBAAgB,CAAC,CAAC;SACvG;IACL,CAAC;IAEM,iBAAiB;QACpB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACjH,CAAC;IAEM,eAAe,CAClB,cAAuB,EACvB,OAAyB,EACzB,WAA6B,EAC7B,MAAwB,EACxB,MAAwB,EACxB,QAAgB,EAChB,SAAiB;QAEjB,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,oBAAoB,GAAG,CAAC,OAAO,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;QACnE,IAAI,CAAC,wBAAwB,GAAG,WAAW,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACpG,IAAI,CAAC,mBAAmB,GAAG,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACxF,IAAI,CAAC,mBAAmB,GAAG,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACrF,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAClC,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IAEM,UAAU,CACb,aAAkE,EAClE,WAAiC,EACjC,qBAA0E;QAE1E,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;QACpD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IACpC,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,QAAgB;QACxC,QAAQ,QAAQ,EAAE;YACd,iBAAiB;YACjB,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,eAAe,CAAC,iBAAiB,CAAC,YAAY,CAAC;YAC1D,KAAK,SAAS,CAAC,sBAAsB;gBACjC,OAAO,eAAe,CAAC,iBAAiB,CAAC,SAAS,CAAC;YACvD,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,eAAe,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YACtD,aAAa;YACb,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,eAAe,CAAC,iBAAiB,CAAC,SAAS,CAAC;YACvD,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,eAAe,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YACtD,KAAK,SAAS,CAAC,yBAAyB;gBACpC,6BAA6B;gBAC7B,4DAA4D;gBAC5D,4CAA4C;gBAC5C,MAAM,+CAA+C,CAAC;YAC1D,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,eAAe,CAAC,iBAAiB,CAAC,SAAS,CAAC;YACvD,KAAK,SAAS,CAAC,8BAA8B;gBACzC,OAAO,eAAe,CAAC,iBAAiB,CAAC,aAAa,CAAC;YAC3D,KAAK,SAAS,CAAC,4BAA4B;gBACvC,gCAAgC;gBAChC,+DAA+D;gBAC/D,4CAA4C;gBAC5C,MAAM,kDAAkD,CAAC;YAC7D;gBACI,OAAO,eAAe,CAAC,iBAAiB,CAAC,YAAY,CAAC;SAC7D;IACL,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,SAA2B;QAC7D,QAAQ,SAAS,EAAE;YACf,KAAK,SAAS,CAAC,qBAAqB;gBAChC,OAAO,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC;YAC9C,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,eAAe,CAAC,cAAc,CAAC,QAAQ,CAAC;YACnD,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,OAAO,eAAe,CAAC,cAAc,CAAC,eAAe,CAAC;YAC1D,KAAK,SAAS,CAAC,qBAAqB;gBAChC,OAAO,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC;YAC9C,KAAK,SAAS,CAAC,qBAAqB;gBAChC,OAAO,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC;YAC9C;gBACI,OAAO,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC;SACjD;IACL,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,MAAwB;QACvD,QAAQ,MAAM,EAAE;YACZ,KAAK,CAAC;gBACF,OAAO,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC;YAC5C,KAAK,CAAC;gBACF,OAAO,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC;YAC3C,KAAK,SAAS,CAAC,qBAAqB;gBAChC,OAAO,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC;YAC3C,KAAK,SAAS,CAAC,qCAAqC;gBAChD,OAAO,eAAe,CAAC,WAAW,CAAC,WAAW,CAAC;YACnD,KAAK,SAAS,CAAC,2BAA2B;gBACtC,OAAO,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC;YAChD,KAAK,SAAS,CAAC,qCAAqC;gBAChD,OAAO,eAAe,CAAC,WAAW,CAAC,gBAAgB,CAAC;YACxD,KAAK,SAAS,CAAC,2BAA2B;gBACtC,OAAO,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC;YAChD,KAAK,SAAS,CAAC,qCAAqC;gBAChD,OAAO,eAAe,CAAC,WAAW,CAAC,gBAAgB,CAAC;YACxD,KAAK,SAAS,CAAC,2BAA2B;gBACtC,OAAO,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC;YAC3C,KAAK,SAAS,CAAC,qCAAqC;gBAChD,OAAO,eAAe,CAAC,WAAW,CAAC,WAAW,CAAC;YACnD,KAAK,SAAS,CAAC,qCAAqC;gBAChD,OAAO,eAAe,CAAC,WAAW,CAAC,iBAAiB,CAAC;YACzD,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,OAAO,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC;YAChD,KAAK,SAAS,CAAC,0CAA0C;gBACrD,OAAO,eAAe,CAAC,WAAW,CAAC,gBAAgB,CAAC;YACxD,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,OAAO,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC;YAChD,KAAK,SAAS,CAAC,0CAA0C;gBACrD,OAAO,eAAe,CAAC,WAAW,CAAC,gBAAgB,CAAC;YACxD;gBACI,OAAO,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC;SAC9C;IACL,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,eAAuB;QACtD,QAAQ,eAAe,EAAE;YACrB,KAAK,CAAC,EAAE,QAAQ;gBACZ,OAAO,eAAe,CAAC,eAAe,CAAC,KAAK,CAAC;YACjD,KAAK,CAAC,EAAE,OAAO;gBACX,OAAO,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC;YAChD,KAAK,CAAC,EAAE,QAAQ;gBACZ,OAAO,eAAe,CAAC,eAAe,CAAC,KAAK,CAAC;YACjD,KAAK,CAAC,EAAE,SAAS;gBACb,OAAO,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC;YACrD,KAAK,CAAC,EAAE,UAAU;gBACd,OAAO,eAAe,CAAC,eAAe,CAAC,OAAO,CAAC;YACnD,KAAK,CAAC,EAAE,WAAW;gBACf,OAAO,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC;YACpD,KAAK,CAAC,EAAE,SAAS;gBACb,OAAO,eAAe,CAAC,eAAe,CAAC,YAAY,CAAC;YACxD,KAAK,CAAC,EAAE,SAAS;gBACb,OAAO,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC;SACrD;QACD,OAAO,eAAe,CAAC,eAAe,CAAC,KAAK,CAAC;IACjD,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,SAAiB;QAClD,QAAQ,SAAS,EAAE;YACf,KAAK,CAAC;gBACF,OAAO,eAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC;YACjD,KAAK,CAAC;gBACF,OAAO,eAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC;YACjD,KAAK,CAAC;gBACF,OAAO,eAAe,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACpD,KAAK,CAAC;gBACF,OAAO,eAAe,CAAC,gBAAgB,CAAC,cAAc,CAAC;YAC3D,KAAK,CAAC;gBACF,OAAO,eAAe,CAAC,gBAAgB,CAAC,cAAc,CAAC;YAC3D,KAAK,CAAC;gBACF,OAAO,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC;YACnD,KAAK,CAAC;gBACF,OAAO,eAAe,CAAC,gBAAgB,CAAC,aAAa,CAAC;YAC1D,KAAK,CAAC;gBACF,OAAO,eAAe,CAAC,gBAAgB,CAAC,aAAa,CAAC;SAC7D;QACD,OAAO,eAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC;IACjD,CAAC;IAEO,MAAM,CAAC,+BAA+B,CAAC,YAA0B;QACrE,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;QAC/B,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;QAC3C,MAAM,IAAI,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC;QAEpC,QAAQ,IAAI,EAAE;YACV,KAAK,YAAY,CAAC,IAAI;gBAClB,QAAQ,IAAI,EAAE;oBACV,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC;oBACrG,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC;iBACxG;gBACD,MAAM;YACV,KAAK,YAAY,CAAC,aAAa;gBAC3B,QAAQ,IAAI,EAAE;oBACV,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC;oBACrG,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC;iBACxG;gBACD,MAAM;YACV,KAAK,YAAY,CAAC,KAAK;gBACnB,QAAQ,IAAI,EAAE;oBACV,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC;oBACvG,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC;iBAC1G;gBACD,MAAM;YACV,KAAK,YAAY,CAAC,cAAc;gBAC5B,QAAQ,IAAI,EAAE;oBACV,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC;oBACvG,KAAK,CAAC,CAAC;oBACP,KAAK,CAAC;wBACF,OAAO,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC;iBAC1G;gBACD,MAAM;YACV,KAAK,YAAY,CAAC,GAAG;gBACjB,QAAQ,IAAI,EAAE;oBACV,KAAK,CAAC;wBACF,OAAO,eAAe,CAAC,YAAY,CAAC,MAAM,CAAC;oBAC/C,KAAK,CAAC;wBACF,OAAO,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC;oBACjD,KAAK,CAAC;wBACF,OAAO,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC;oBACjD,KAAK,CAAC;wBACF,OAAO,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC;iBACpD;gBACD,MAAM;YACV,KAAK,YAAY,CAAC,YAAY;gBAC1B,QAAQ,IAAI,EAAE;oBACV,KAAK,CAAC;wBACF,OAAO,eAAe,CAAC,YAAY,CAAC,MAAM,CAAC;oBAC/C,KAAK,CAAC;wBACF,OAAO,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC;oBACjD,KAAK,CAAC;wBACF,OAAO,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC;oBACjD,KAAK,CAAC;wBACF,OAAO,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC;iBACpD;gBACD,MAAM;YACV,KAAK,YAAY,CAAC,KAAK;gBACnB,QAAQ,IAAI,EAAE;oBACV,KAAK,CAAC;wBACF,OAAO,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC;oBAChD,KAAK,CAAC;wBACF,OAAO,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC;oBAClD,KAAK,CAAC;wBACF,OAAO,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC;oBAClD,KAAK,CAAC;wBACF,OAAO,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC;iBACrD;gBACD,MAAM;SACb;QAED,MAAM,IAAI,KAAK,CAAC,mBAAmB,YAAY,CAAC,OAAO,EAAE,YAAY,IAAI,gBAAgB,UAAU,UAAU,IAAI,EAAE,CAAC,CAAC;IACzH,CAAC;IAEO,kBAAkB;QACtB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,OAAO,IAAI,CAAC;SACf;QAED,OAAO;YACH,SAAS,EAAE,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YACvF,SAAS,EAAE,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YACvF,SAAS,EAAE,yBAAyB,CAAC,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;SAC3F,CAAC;IACN,CAAC;IAEO,mBAAmB;QACvB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,OAAO,IAAI,CAAC;SACf;QAED,OAAO;YACH,SAAS,EAAE,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YACvF,SAAS,EAAE,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YACvF,SAAS,EAAE,yBAAyB,CAAC,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;SAC3F,CAAC;IACN,CAAC;IAEO,eAAe,CAAC,EAAU;QAC9B,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,EAAE;YACvB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;YAC7C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC;SAClG;IACL,CAAC;IAEO,sBAAsB,CAAC,QAAgB,EAAE,WAAmB;QAChE,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,MAAM,kBAAkB,GAAG,SAAS,GAAG,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;QAE/I,IAAI,IAAI,CAAC,mBAAmB,KAAK,kBAAkB,EAAE;YACjD,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;YAC9C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAC1E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,kBAAkB,CAAC,CAAC;SACzG;IACL,CAAC;IAEO,eAAe;QACnB,IAAI,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,sKAAsK;QAEnS,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,WAAW;gBACP,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;oBAC5G,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;oBAC5G,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;oBAC5G,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;oBAC7G,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;oBACzF,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;SACjG;QAED,IAAI,WAAW,KAAK,IAAI,CAAC,YAAY,EAAE;YACnC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;YAC5D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC;SAClG;IACL,CAAC;IAEO,qBAAqB;QACzB,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,eAAe;YACtC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC;YAClF,CAAC,CAAC,IAAI,CAAC,oBAAoB,GAAG,CAAC,IAAI,CAAC,wBAAwB,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC,CAAC;QAE3I,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,CAAC,2CAA2C;QAE9L,IAAI,IAAI,CAAC,kBAAkB,KAAK,iBAAiB,EAAE;YAC/C,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACxE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,iBAAiB,CAAC,CAAC;SACxG;IACL,CAAC;IAEO,eAAe,CAAC,MAAc;QAClC,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QACxC,IAAI,YAAY,GAAG,aAAa,CAAC,WAAW,CAAC;QAE7C,MAAM,qBAAqB,GAAG,MAAM,CAAC,gBAAyC,CAAC;QAC/E,MAAM,UAAU,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,wBAAwB,CAAC;QAC1F,MAAM,SAAS,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,4BAA4B,CAAC;QAE7F,IAAI,gBAAgB,CAAC;QACrB,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACpD,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,YAAY,GAAG,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,cAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9I,IAAI,CAAC,YAAY,EAAE;gBACf,gGAAgG;gBAChG,oFAAoF;gBACpF,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC;aAC1C;YAED,MAAM,MAAM,GAAG,YAAY,CAAC,eAAe,EAAE,kBAAkB,CAAC;YAEhE,6KAA6K;YAC7K,+JAA+J;YAC/J,wCAAwC;YACxC,IAAI,YAAY,CAAC,iBAAiB,KAAK,SAAS,EAAE;gBAC9C,MAAM,MAAM,GAAG,YAAY,CAAC,mBAAmB,CAAC;gBAChD,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM,UAAU,GAAG,YAAY,CAAC,mBAAmB,CAAC;gBAEpD,YAAY,CAAC,iBAAiB;oBAC1B,CAAC,MAAM,GAAG,UAAU,IAAI,IAAI,CAAC,uBAAuB,IAAI,UAAU,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,MAAM,GAAG,UAAU,IAAI,UAAU,CAAC,CAAC;aAC5I;YAED,IAAI,CAAC,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,MAAM,IAAI,YAAY,CAAC,iBAAiB,CAAC,EAAE;gBACtF,kEAAkE;gBAClE,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC,GAAG,YAAY,CAAC;gBACtD,gBAAgB,GAAG,YAAY,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;aACrE;YAED,MAAM,GAAG,GAAG,YAAY,CAAC,QAAQ,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;YAEpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,GAAG,CAAC;YACpE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,GAAG,GAAG,CAAC;SACtC;QAED,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,gBAAgB,CAAC;QAE7C,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,YAAY,KAAK,YAAY,CAAC;QAC/D,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC;SAClG;IACL,CAAC;IAEO,gBAAgB,CAAC,YAAoB;QACzC,IAAI,IAAI,CAAC,aAAa,KAAK,YAAY,EAAE;YACrC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;YAC9D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;SACnG;IACL,CAAC;IAEO,qBAAqB,CAAC,qBAA4C;QACtE,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO,IAAI,CAAC,qCAAqC,CAAC,qBAAqB,CAAC,CAAC;SAC5E;QAED,MAAM,gBAAgB,GAAyB,EAAE,CAAC;QAClD,MAAM,sBAAsB,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,sBAAsB,CAAC;QAEpG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpD,MAAM,aAAa,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAEhD,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;gBACrD,OAAO,EAAE,aAAa;aACzB,CAAC,CAAC;SACN;QAED,qBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC;QAE7D,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,gBAAgB,EAAE,CAAC,CAAC;IACnE,CAAC;IAEO,qCAAqC,CAAC,qBAA4C;QACtF,MAAM,uBAAuB,GAAG,qBAAqB,CAAC,uBAAuB,CAAC;QAC9E,MAAM,sBAAsB,GAAG,uBAAuB,CAAC,sBAAsB,CAAC;QAE9E,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpD,MAAM,aAAa,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAEhD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3C,MAAM,KAAK,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE3C,IAAI,KAAK,CAAC,OAAO,EAAE;oBACf,MAAM,IAAI,GAAG,uBAAuB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;oBACrF,MAAM,WAAW,GAAG,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBACpE,MAAM,WAAW,GAAG,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,GAAG,qBAAqB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBAEnJ,IAAI,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;oBACxC,IAAI,WAAW,GAAG,WAAW,EAAE,IAAI,IAAI,eAAe,CAAC,kBAAkB,CAAC,SAAS,CAAC;oBAEpF,IAAI,IAAI,CAAC,aAAa,GAAG,MAAM,IAAI,UAAU,KAAK,eAAe,CAAC,iBAAiB,CAAC,KAAK,EAAE;wBACvF,6JAA6J;wBAC7J,4FAA4F;wBAC5F,IAAI,WAAW,CAAC,eAAe,EAAE;4BAC7B,WAAW,GAAG,eAAe,CAAC,kBAAkB,CAAC,YAAY,CAAC;yBACjE;wBACD,UAAU,GAAG,eAAe,CAAC,iBAAiB,CAAC,iBAAiB,CAAC;qBACpE;oBAED,KAAK,CAAC,OAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;oBAEvC,IAAI,WAAW,EAAE;wBACb,MAAM,OAAO,GAAG,uBAAuB,CAAC,wBAAwB,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC;wBACzI,sBAAsB,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,OAAQ,CAAC,IAAI,GAAG,WAAW,CAAC;qBAC/F;oBAED,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;iBACxB;aACJ;SACJ;QAED,MAAM,gBAAgB,GAAyB,EAAE,CAAC;QAElD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,sBAAsB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACpD,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;gBACrD,OAAO,EAAE,sBAAsB,CAAC,CAAC,CAAC;aACrC,CAAC,CAAC;SACN;QAED,qBAAqB,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,gBAAgB,CAAC;QAE9E,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,gBAAgB,EAAE,CAAC,CAAC;IACnE,CAAC;IAEO,yBAAyB,CAAC,MAAc;QAC5C,MAAM,WAAW,GAA4B,EAAE,CAAC;QAChD,MAAM,qBAAqB,GAAG,MAAM,CAAC,gBAAyC,CAAC;QAC/E,MAAM,UAAU,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,wBAAwB,CAAC;QAC1F,MAAM,SAAS,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,4BAA4B,CAAC;QAE7F,IAAI,gBAAgB,CAAC;QACrB,IAAI,oBAAsD,CAAC;QAC3D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACpD,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,YAAY,GAAG,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,cAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9I,IAAI,CAAC,YAAY,EAAE;gBACf,gGAAgG;gBAChG,oFAAoF;gBACpF,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC;aAC1C;YAED,IAAI,MAAM,GAAG,YAAY,CAAC,eAAe,EAAE,kBAAkB,CAAC;YAE9D,yKAAyK;YACzK,IAAI,MAAM,GAAG,YAAY,CAAC,mBAAmB,CAAC;YAC9C,MAAM,kBAAkB,GAAG,CAAC,YAAY,CAAC,iBAAiB,CAAC;YAC3D,IAAI,CAAC,CAAC,gBAAgB,IAAI,oBAAoB,IAAI,gBAAgB,KAAK,MAAM,CAAC,IAAI,kBAAkB,EAAE;gBAClG,MAAM,sBAAsB,GAA0B;oBAClD,WAAW,EAAE,YAAY,CAAC,mBAAmB;oBAC7C,QAAQ,EAAE,YAAY,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,MAAM;oBACzH,UAAU,EAAE,EAAE;iBACjB,CAAC;gBAEF,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACzC,oBAAoB,GAAG,sBAAsB,CAAC,UAAU,CAAC;gBACzD,IAAI,kBAAkB,EAAE;oBACpB,MAAM,GAAG,CAAC,CAAC,CAAC,8DAA8D;oBAC1E,MAAM,GAAG,IAAI,CAAC,CAAC,yBAAyB;iBAC3C;aACJ;YAED,oBAAoB,CAAC,IAAI,CAAC;gBACtB,cAAc,EAAE,QAAQ;gBACxB,MAAM;gBACN,MAAM,EAAE,yBAAyB,CAAC,+BAA+B,CAAC,YAAY,CAAC;aAClF,CAAC,CAAC;YAEH,gBAAgB,GAAG,MAAM,CAAC;SAC7B;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,6BAA6B,CAAC,qBAA4C,EAAE,MAAc;QAC9F,MAAM,qBAAqB,GAAG,qBAAqB,CAAC,MAAM,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,cAAc,CAA0B,CAAC;QAEtK,IAAI,gBAAgB,GAAG,KAAK,CAAC;QAE7B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YACpC,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAEtD,IAAI,CAAC,mBAAmB,IAAI,CAAC,qCAAqC,CAAC,IAAI,CAAC,EAAE;gBACtE,SAAS;aACZ;YAED,MAAM,uBAAuB,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAC/G,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAE5E,IACI,CAAC,uBAAuB,KAAK,YAAY,CAAC,KAAK,IAAI,gBAAgB,KAAK,SAAS,CAAC;gBAClF,CAAC,gBAAgB,KAAK,SAAS,IAAI,gBAAgB,KAAK,uBAAuB,CAAC,EAClF;gBACE,gBAAgB,GAAG,IAAI,CAAC;gBACxB,qBAAqB,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,uBAAuB,CAAC;gBAC7E,IAAI,uBAAuB,KAAK,YAAY,CAAC,KAAK,EAAE;oBAChD,qBAAqB,CAAC,oCAAoC,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBACnG,IAAI,yBAAyB,CAAC,aAAa,CAAC,uBAAuB,CAAC,EAAE;wBAClE,qBAAqB,CAAC,oCAAoC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;qBAC1E;iBACJ;aACJ;SACJ;QAED,IAAI,gBAAgB,EAAE;YAClB,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;SAC1D;IACL,CAAC;IAEO,qBAAqB,CAAC,MAAc,EAAE,QAA8B,EAAE,WAAmB;QAC7F,MAAM,qBAAqB,GAAG,MAAM,CAAC,gBAAyC,CAAC;QAC/E,MAAM,oBAAoB,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QACpE,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,CAAC;QAEzE,MAAM,WAAW,GAAsC,EAAE,CAAC;QAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE9C,IAAI,CAAC,6BAA6B,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;QAElE,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBACnC,IAAI,MAAM,EAAE;oBACR,MAAM,KAAK,GAAwB;wBAC/B,MAAM;wBACN,SAAS,EAAE,CAAC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;qBAC3E,CAAC;oBACF,IAAI,UAAU,IAAI,UAAU,EAAE;wBAC1B,KAAK,CAAC,KAAK,GAAG;4BACV,KAAK,EAAE,UAAU;4BACjB,KAAK,EAAE,UAAU;yBACpB,CAAC;qBACL;oBACD,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAC3B;qBAAM;oBACH,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAC1B;aACJ;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE;gBAC5B,MAAM,KAAK,GAAwB;oBAC/B,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;oBAClC,SAAS,EAAE,IAAI,CAAC,UAAU;iBAC7B,CAAC;gBACF,IAAI,UAAU,IAAI,UAAU,EAAE;oBAC1B,KAAK,CAAC,KAAK,GAAG;wBACV,KAAK,EAAE,UAAU;wBACjB,KAAK,EAAE,UAAU;qBACpB,CAAC;iBACL;gBACD,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC3B;iBAAM;gBACH,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC1B;SACJ;QAED,MAAM,gBAAgB,GAAwB;YAC1C,OAAO,EAAE,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YACzH,WAAW,EAAE,yBAAyB,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YACjI,MAAM,EAAE,yBAAyB,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YACvH,MAAM,EAAE,yBAAyB,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;SAC1H,CAAC;QAEF,IAAI,gBAAgB,GAA+B,SAAS,CAAC;QAC7D,IAAI,QAAQ,KAAK,eAAe,CAAC,iBAAiB,CAAC,SAAS,IAAI,QAAQ,KAAK,eAAe,CAAC,iBAAiB,CAAC,aAAa,EAAE;YAC1H,gBAAgB,GAAG,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC;SACjJ;QAED,MAAM,4BAA4B,GAAG,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAEnJ,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;YACrC,KAAK,EAAE,kBAAkB,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,UAAU,IAAI,IAAI,CAAC,yBAAyB,IAAI,SAAS,WAAW,WAAW,gBAAgB,IAAI,CAAC,aAAa,EAAE;YACtK,MAAM,EAAE,cAAc;YACtB,MAAM,EAAE;gBACJ,MAAM,EAAE,qBAAqB,CAAC,MAAO,CAAC,WAAW,CAAC,MAAM;gBACxD,UAAU,EAAE,qBAAqB,CAAC,MAAO,CAAC,WAAW,CAAC,UAAU;gBAChE,OAAO,EAAE,oBAAoB;aAChC;YACD,SAAS,EAAE;gBACP,QAAQ;gBACR,gBAAgB;gBAChB,SAAS,EAAE,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE;gBAC/F,QAAQ,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI;aACvJ;YACD,QAAQ,EAAE,CAAC,qBAAqB,CAAC,MAAO,CAAC,aAAa;gBAClD,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC;oBACI,MAAM,EAAE,qBAAqB,CAAC,MAAO,CAAC,aAAa,CAAC,MAAM;oBAC1D,UAAU,EAAE,qBAAqB,CAAC,MAAO,CAAC,aAAa,CAAC,UAAU;oBAClE,OAAO,EAAE,WAAW;iBACvB;YAEP,WAAW,EAAE;gBACT,KAAK,EAAE,WAAW;gBAClB;yCACyB;aAC5B;YACD,YAAY,EACR,IAAI,CAAC,yBAAyB,KAAK,SAAS;gBACxC,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC;oBACI,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;oBAC1C,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM;oBACjJ,MAAM,EAAE,IAAI,CAAC,yBAAyB;oBACtC,YAAY,EAAE,IAAI,CAAC,eAAe,IAAI,4BAA4B,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS;oBACjG,WAAW,EAAE,IAAI,CAAC,eAAe,IAAI,4BAA4B,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS;oBAChG,eAAe,EAAE,IAAI,CAAC,eAAe,IAAI,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS;oBACzG,gBAAgB,EAAE,IAAI,CAAC,eAAe,IAAI,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS;oBAC3G,SAAS,EAAE,IAAI,CAAC,UAAU;oBAC1B,cAAc,EAAE,IAAI,CAAC,eAAe;oBACpC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB;iBACjD;SACd,CAAC,CAAC;IACP,CAAC;;AArhCa,gDAAsB,GAAG,CAAC,AAAJ,CAAK;AAC3B,6CAAmB,GAAG,CAAC,AAAJ,CAAK;AACxB,sCAAY,GAAG,CAAC,AAAJ,CAAK;AACjB,sDAA4B,GAAG,CAAC,AAAJ,CAAK;AAIhC,0DAAgC,GAAG,CAAC,AAAJ,CAAK", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\n/* eslint-disable babylonjs/available */\r\n/* eslint-disable jsdoc/require-jsdoc */\r\nimport { Constants } from \"../constants\";\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport type { Effect } from \"../../Materials/effect\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport { VertexBuffer } from \"../../Buffers/buffer\";\r\nimport type { DataBuffer } from \"../../Buffers/dataBuffer\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { WebGPUHardwareTexture } from \"./webgpuHardwareTexture\";\r\nimport type { WebGPUPipelineContext } from \"./webgpuPipelineContext\";\r\nimport { WebGPUShaderProcessor } from \"./webgpuShaderProcessor\";\r\nimport { WebGPUTextureHelper } from \"./webgpuTextureHelper\";\r\nimport { renderableTextureFormatToIndex } from \"./webgpuTextureManager\";\r\n\r\nenum StatePosition {\r\n    StencilReadMask = 0,\r\n    StencilWriteMask = 1,\r\n    //DepthBiasClamp = 1, // not used, so remove it to improve perf\r\n    DepthBias = 2,\r\n    DepthBiasSlopeScale = 3,\r\n    DepthStencilState = 4,\r\n    MRTAttachments1 = 5,\r\n    MRTAttachments2 = 6,\r\n    RasterizationState = 7,\r\n    ColorStates = 8,\r\n    ShaderStage = 9,\r\n    TextureStage = 10,\r\n    VertexState = 11, // vertex state will consume positions 11, 12, ... depending on the number of vertex inputs\r\n\r\n    NumStates = 12,\r\n}\r\n\r\nconst alphaBlendFactorToIndex: { [name: number]: number } = {\r\n    0: 1, // Zero\r\n    1: 2, // One\r\n    0x0300: 3, // SrcColor\r\n    0x0301: 4, // OneMinusSrcColor\r\n    0x0302: 5, // SrcAlpha\r\n    0x0303: 6, // OneMinusSrcAlpha\r\n    0x0304: 7, // DstAlpha\r\n    0x0305: 8, // OneMinusDstAlpha\r\n    0x0306: 9, // DstColor\r\n    0x0307: 10, // OneMinusDstColor\r\n    0x0308: 11, // SrcAlphaSaturated\r\n    0x8001: 12, // BlendColor\r\n    0x8002: 13, // OneMinusBlendColor\r\n    0x8003: 12, // BlendColor (alpha)\r\n    0x8004: 13, // OneMinusBlendColor (alpha)\r\n};\r\n\r\nconst stencilOpToIndex: { [name: number]: number } = {\r\n    0x0000: 0, // ZERO\r\n    0x1e00: 1, // KEEP\r\n    0x1e01: 2, // REPLACE\r\n    0x1e02: 3, // INCR\r\n    0x1e03: 4, // DECR\r\n    0x150a: 5, // INVERT\r\n    0x8507: 6, // INCR_WRAP\r\n    0x8508: 7, // DECR_WRAP\r\n};\r\n\r\nconst vertexBufferKindForNonFloatProcessing: { [kind: string]: boolean } = {\r\n    [VertexBuffer.PositionKind]: true,\r\n    [VertexBuffer.NormalKind]: true,\r\n    [VertexBuffer.TangentKind]: true,\r\n    [VertexBuffer.UVKind]: true,\r\n    [VertexBuffer.UV2Kind]: true,\r\n    [VertexBuffer.UV3Kind]: true,\r\n    [VertexBuffer.UV4Kind]: true,\r\n    [VertexBuffer.UV5Kind]: true,\r\n    [VertexBuffer.UV6Kind]: true,\r\n    [VertexBuffer.ColorKind]: true,\r\n    [VertexBuffer.ColorInstanceKind]: true,\r\n    [VertexBuffer.MatricesIndicesKind]: true,\r\n    [VertexBuffer.MatricesWeightsKind]: true,\r\n    [VertexBuffer.MatricesIndicesExtraKind]: true,\r\n    [VertexBuffer.MatricesWeightsExtraKind]: true,\r\n};\r\n\r\n/** @internal */\r\nexport abstract class WebGPUCacheRenderPipeline {\r\n    public static NumCacheHitWithoutHash = 0;\r\n    public static NumCacheHitWithHash = 0;\r\n    public static NumCacheMiss = 0;\r\n    public static NumPipelineCreationLastFrame = 0;\r\n\r\n    public disabled: boolean;\r\n\r\n    private static _NumPipelineCreationCurrentFrame = 0;\r\n\r\n    protected _states: number[];\r\n    protected _statesLength: number;\r\n    protected _stateDirtyLowestIndex: number;\r\n    public lastStateDirtyLowestIndex: number; // for stats only\r\n\r\n    private _device: GPUDevice;\r\n    private _isDirty: boolean;\r\n    private _emptyVertexBuffer: VertexBuffer;\r\n    private _parameter: { token: any; pipeline: Nullable<GPURenderPipeline> };\r\n    private _kMaxVertexBufferStride;\r\n\r\n    private _shaderId: number;\r\n    private _alphaToCoverageEnabled: boolean;\r\n    private _frontFace: number;\r\n    private _cullEnabled: boolean;\r\n    private _cullFace: number;\r\n    private _clampDepth: boolean;\r\n    private _rasterizationState: number;\r\n    private _depthBias: number;\r\n    private _depthBiasClamp: number;\r\n    private _depthBiasSlopeScale: number;\r\n    private _colorFormat: number;\r\n    private _webgpuColorFormat: (GPUTextureFormat | null)[];\r\n    private _mrtAttachments1: number;\r\n    private _mrtAttachments2: number;\r\n    private _mrtFormats: (GPUTextureFormat | null)[];\r\n    private _mrtEnabledMask: number;\r\n    private _alphaBlendEnabled: boolean;\r\n    private _alphaBlendFuncParams: Array<Nullable<number>>;\r\n    private _alphaBlendEqParams: Array<Nullable<number>>;\r\n    private _writeMask: number;\r\n    private _colorStates: number;\r\n    private _depthStencilFormat: number;\r\n    private _webgpuDepthStencilFormat: GPUTextureFormat | undefined;\r\n    private _depthTestEnabled: boolean;\r\n    private _depthWriteEnabled: boolean;\r\n    private _depthCompare: number;\r\n    private _stencilEnabled: boolean;\r\n    private _stencilFrontCompare: number;\r\n    private _stencilFrontDepthFailOp: number;\r\n    private _stencilFrontPassOp: number;\r\n    private _stencilFrontFailOp: number;\r\n    private _stencilReadMask: number;\r\n    private _stencilWriteMask: number;\r\n    private _depthStencilState: number;\r\n    private _vertexBuffers: Nullable<{ [key: string]: Nullable<VertexBuffer> }>;\r\n    private _overrideVertexBuffers: Nullable<{ [key: string]: Nullable<VertexBuffer> }>;\r\n    private _indexBuffer: Nullable<DataBuffer>;\r\n    private _textureState: number;\r\n    private _useTextureStage: boolean;\r\n\r\n    private static _IsSignedType(type: number): boolean {\r\n        switch (type) {\r\n            case VertexBuffer.BYTE:\r\n            case VertexBuffer.SHORT:\r\n            case VertexBuffer.INT:\r\n            case VertexBuffer.FLOAT:\r\n                return true;\r\n            case VertexBuffer.UNSIGNED_BYTE:\r\n            case VertexBuffer.UNSIGNED_SHORT:\r\n            case VertexBuffer.UNSIGNED_INT:\r\n                return false;\r\n            default:\r\n                throw new Error(`Invalid type '${type}'`);\r\n        }\r\n    }\r\n\r\n    constructor(device: GPUDevice, emptyVertexBuffer: VertexBuffer) {\r\n        this._device = device;\r\n        this._useTextureStage = true; // we force usage because we must handle depth textures with \"float\" filtering, which can't be fixed by a caps (like \"textureFloatLinearFiltering\" can for float textures)\r\n        this._states = new Array(30); // pre-allocate enough room so that no new allocation will take place afterwards\r\n        this._statesLength = 0;\r\n        this._stateDirtyLowestIndex = 0;\r\n        this._emptyVertexBuffer = emptyVertexBuffer;\r\n        this._mrtFormats = [];\r\n        this._parameter = { token: undefined, pipeline: null };\r\n        this.disabled = false;\r\n        this.vertexBuffers = [];\r\n        this._kMaxVertexBufferStride = device.limits.maxVertexBufferArrayStride || 2048;\r\n        this.reset();\r\n    }\r\n\r\n    public reset(): void {\r\n        this._isDirty = true;\r\n        this.vertexBuffers.length = 0;\r\n        this.setAlphaToCoverage(false);\r\n        this.resetDepthCullingState();\r\n        this.setClampDepth(false);\r\n        this.setDepthBias(0);\r\n        //this.setDepthBiasClamp(0);\r\n        this._webgpuColorFormat = [WebGPUConstants.TextureFormat.BGRA8Unorm];\r\n        this.setColorFormat(WebGPUConstants.TextureFormat.BGRA8Unorm);\r\n        this.setMRT([]);\r\n        this.setAlphaBlendEnabled(false);\r\n        this.setAlphaBlendFactors([null, null, null, null], [null, null]);\r\n        this.setWriteMask(0xf);\r\n        this.setDepthStencilFormat(WebGPUConstants.TextureFormat.Depth24PlusStencil8);\r\n        this.setStencilEnabled(false);\r\n        this.resetStencilState();\r\n        this.setBuffers(null, null, null);\r\n        this._setTextureState(0);\r\n    }\r\n\r\n    protected abstract _getRenderPipeline(param: { token: any; pipeline: Nullable<GPURenderPipeline> }): void;\r\n    protected abstract _setRenderPipeline(param: { token: any; pipeline: Nullable<GPURenderPipeline> }): void;\r\n\r\n    public readonly vertexBuffers: VertexBuffer[];\r\n\r\n    public get colorFormats(): (GPUTextureFormat | null)[] {\r\n        return this._mrtAttachments1 > 0 ? this._mrtFormats : this._webgpuColorFormat;\r\n    }\r\n\r\n    public readonly mrtAttachments: number[];\r\n    public readonly mrtTextureArray: InternalTexture[];\r\n    public readonly mrtTextureCount: number = 0;\r\n\r\n    public getRenderPipeline(fillMode: number, effect: Effect, sampleCount: number, textureState = 0): GPURenderPipeline {\r\n        sampleCount = WebGPUTextureHelper.GetSample(sampleCount);\r\n\r\n        if (this.disabled) {\r\n            const topology = WebGPUCacheRenderPipeline._GetTopology(fillMode);\r\n\r\n            this._setVertexState(effect); // to fill this.vertexBuffers with correct data\r\n            this._setTextureState(textureState);\r\n\r\n            this._parameter.pipeline = this._createRenderPipeline(effect, topology, sampleCount);\r\n\r\n            WebGPUCacheRenderPipeline.NumCacheMiss++;\r\n            WebGPUCacheRenderPipeline._NumPipelineCreationCurrentFrame++;\r\n\r\n            return this._parameter.pipeline;\r\n        }\r\n\r\n        this._setShaderStage(effect.uniqueId);\r\n        this._setRasterizationState(fillMode, sampleCount);\r\n        this._setColorStates();\r\n        this._setDepthStencilState();\r\n        this._setVertexState(effect);\r\n        this._setTextureState(textureState);\r\n\r\n        this.lastStateDirtyLowestIndex = this._stateDirtyLowestIndex;\r\n\r\n        if (!this._isDirty && this._parameter.pipeline) {\r\n            this._stateDirtyLowestIndex = this._statesLength;\r\n            WebGPUCacheRenderPipeline.NumCacheHitWithoutHash++;\r\n            return this._parameter.pipeline;\r\n        }\r\n\r\n        this._getRenderPipeline(this._parameter);\r\n\r\n        this._isDirty = false;\r\n        this._stateDirtyLowestIndex = this._statesLength;\r\n\r\n        if (this._parameter.pipeline) {\r\n            WebGPUCacheRenderPipeline.NumCacheHitWithHash++;\r\n            return this._parameter.pipeline;\r\n        }\r\n\r\n        const topology = WebGPUCacheRenderPipeline._GetTopology(fillMode);\r\n\r\n        this._parameter.pipeline = this._createRenderPipeline(effect, topology, sampleCount);\r\n        this._setRenderPipeline(this._parameter);\r\n\r\n        WebGPUCacheRenderPipeline.NumCacheMiss++;\r\n        WebGPUCacheRenderPipeline._NumPipelineCreationCurrentFrame++;\r\n\r\n        return this._parameter.pipeline;\r\n    }\r\n\r\n    public endFrame(): void {\r\n        WebGPUCacheRenderPipeline.NumPipelineCreationLastFrame = WebGPUCacheRenderPipeline._NumPipelineCreationCurrentFrame;\r\n        WebGPUCacheRenderPipeline._NumPipelineCreationCurrentFrame = 0;\r\n    }\r\n\r\n    public setAlphaToCoverage(enabled: boolean): void {\r\n        this._alphaToCoverageEnabled = enabled;\r\n    }\r\n\r\n    public setFrontFace(frontFace: number): void {\r\n        this._frontFace = frontFace;\r\n    }\r\n\r\n    public setCullEnabled(enabled: boolean): void {\r\n        this._cullEnabled = enabled;\r\n    }\r\n\r\n    public setCullFace(cullFace: number): void {\r\n        this._cullFace = cullFace;\r\n    }\r\n\r\n    public setClampDepth(clampDepth: boolean): void {\r\n        this._clampDepth = clampDepth;\r\n    }\r\n\r\n    public resetDepthCullingState(): void {\r\n        this.setDepthCullingState(false, 2, 1, 0, 0, true, true, Constants.ALWAYS);\r\n    }\r\n\r\n    public setDepthCullingState(\r\n        cullEnabled: boolean,\r\n        frontFace: number,\r\n        cullFace: number,\r\n        zOffset: number,\r\n        zOffsetUnits: number,\r\n        depthTestEnabled: boolean,\r\n        depthWriteEnabled: boolean,\r\n        depthCompare: Nullable<number>\r\n    ): void {\r\n        this._depthWriteEnabled = depthWriteEnabled;\r\n        this._depthTestEnabled = depthTestEnabled;\r\n        this._depthCompare = (depthCompare ?? Constants.ALWAYS) - 0x0200;\r\n        this._cullFace = cullFace;\r\n        this._cullEnabled = cullEnabled;\r\n        this._frontFace = frontFace;\r\n        this.setDepthBiasSlopeScale(zOffset);\r\n        this.setDepthBias(zOffsetUnits);\r\n    }\r\n\r\n    public setDepthBias(depthBias: number): void {\r\n        if (this._depthBias !== depthBias) {\r\n            this._depthBias = depthBias;\r\n            this._states[StatePosition.DepthBias] = depthBias;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.DepthBias);\r\n        }\r\n    }\r\n\r\n    /*public setDepthBiasClamp(depthBiasClamp: number): void {\r\n        if (this._depthBiasClamp !== depthBiasClamp) {\r\n            this._depthBiasClamp = depthBiasClamp;\r\n            this._states[StatePosition.DepthBiasClamp] = depthBiasClamp.toString();\r\n            this._isDirty = true;\r\n        }\r\n    }*/\r\n\r\n    public setDepthBiasSlopeScale(depthBiasSlopeScale: number): void {\r\n        if (this._depthBiasSlopeScale !== depthBiasSlopeScale) {\r\n            this._depthBiasSlopeScale = depthBiasSlopeScale;\r\n            this._states[StatePosition.DepthBiasSlopeScale] = depthBiasSlopeScale;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.DepthBiasSlopeScale);\r\n        }\r\n    }\r\n\r\n    public setColorFormat(format: GPUTextureFormat | null): void {\r\n        this._webgpuColorFormat[0] = format;\r\n        this._colorFormat = renderableTextureFormatToIndex[format ?? \"\"];\r\n    }\r\n\r\n    public setMRTAttachments(attachments: number[]): void {\r\n        (this.mrtAttachments as any) = attachments;\r\n        let mask = 0;\r\n        for (let i = 0; i < attachments.length; ++i) {\r\n            if (attachments[i] !== 0) {\r\n                mask += 1 << i;\r\n            }\r\n        }\r\n        if (this._mrtEnabledMask !== mask) {\r\n            this._mrtEnabledMask = mask;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.MRTAttachments1);\r\n        }\r\n    }\r\n\r\n    public setMRT(textureArray: InternalTexture[], textureCount?: number): void {\r\n        textureCount = textureCount ?? textureArray.length;\r\n        if (textureCount > 10) {\r\n            // If we want more than 10 attachments we need to change this method (and the StatePosition enum) but 10 seems plenty: note that WebGPU only supports 8 at the time (2021/12/13)!\r\n            // As we need ~39 different values we are using 6 bits to encode a texture format, meaning we can encode 5 texture formats in 32 bits\r\n            // We are using 2x32 bit values to handle 10 textures\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"Can't handle more than 10 attachments for a MRT in cache render pipeline!\";\r\n        }\r\n        (this.mrtTextureArray as any) = textureArray;\r\n        (this.mrtTextureCount as any) = textureCount;\r\n\r\n        this._mrtEnabledMask = 0xffff; // all textures are enabled at start (meaning we can write to them). Calls to setMRTAttachments may disable some\r\n\r\n        const bits: number[] = [0, 0];\r\n        let indexBits = 0,\r\n            mask = 0,\r\n            numRT = 0;\r\n        for (let i = 0; i < textureCount; ++i) {\r\n            const texture = textureArray[i];\r\n            const gpuWrapper = texture?._hardwareTexture as Nullable<WebGPUHardwareTexture>;\r\n\r\n            this._mrtFormats[numRT] = gpuWrapper?.format ?? this._webgpuColorFormat[0];\r\n\r\n            bits[indexBits] += renderableTextureFormatToIndex[this._mrtFormats[numRT] ?? \"\"] << mask;\r\n            mask += 6;\r\n            numRT++;\r\n\r\n            if (mask >= 32) {\r\n                mask = 0;\r\n                indexBits++;\r\n            }\r\n        }\r\n        this._mrtFormats.length = numRT;\r\n        if (this._mrtAttachments1 !== bits[0] || this._mrtAttachments2 !== bits[1]) {\r\n            this._mrtAttachments1 = bits[0];\r\n            this._mrtAttachments2 = bits[1];\r\n            this._states[StatePosition.MRTAttachments1] = bits[0];\r\n            this._states[StatePosition.MRTAttachments2] = bits[1];\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.MRTAttachments1);\r\n        }\r\n    }\r\n\r\n    public setAlphaBlendEnabled(enabled: boolean): void {\r\n        this._alphaBlendEnabled = enabled;\r\n    }\r\n\r\n    public setAlphaBlendFactors(factors: Array<Nullable<number>>, operations: Array<Nullable<number>>): void {\r\n        this._alphaBlendFuncParams = factors;\r\n        this._alphaBlendEqParams = operations;\r\n    }\r\n\r\n    public setWriteMask(mask: number): void {\r\n        this._writeMask = mask;\r\n    }\r\n\r\n    public setDepthStencilFormat(format: GPUTextureFormat | undefined): void {\r\n        this._webgpuDepthStencilFormat = format;\r\n        this._depthStencilFormat = format === undefined ? 0 : renderableTextureFormatToIndex[format];\r\n    }\r\n\r\n    public setDepthTestEnabled(enabled: boolean): void {\r\n        this._depthTestEnabled = enabled;\r\n    }\r\n\r\n    public setDepthWriteEnabled(enabled: boolean): void {\r\n        this._depthWriteEnabled = enabled;\r\n    }\r\n\r\n    public setDepthCompare(func: Nullable<number>): void {\r\n        this._depthCompare = (func ?? Constants.ALWAYS) - 0x0200;\r\n    }\r\n\r\n    public setStencilEnabled(enabled: boolean): void {\r\n        this._stencilEnabled = enabled;\r\n    }\r\n\r\n    public setStencilCompare(func: Nullable<number>): void {\r\n        this._stencilFrontCompare = (func ?? Constants.ALWAYS) - 0x0200;\r\n    }\r\n\r\n    public setStencilDepthFailOp(op: Nullable<number>): void {\r\n        this._stencilFrontDepthFailOp = op === null ? 1 /* KEEP */ : stencilOpToIndex[op];\r\n    }\r\n\r\n    public setStencilPassOp(op: Nullable<number>): void {\r\n        this._stencilFrontPassOp = op === null ? 2 /* REPLACE */ : stencilOpToIndex[op];\r\n    }\r\n\r\n    public setStencilFailOp(op: Nullable<number>): void {\r\n        this._stencilFrontFailOp = op === null ? 1 /* KEEP */ : stencilOpToIndex[op];\r\n    }\r\n\r\n    public setStencilReadMask(mask: number): void {\r\n        if (this._stencilReadMask !== mask) {\r\n            this._stencilReadMask = mask;\r\n            this._states[StatePosition.StencilReadMask] = mask;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.StencilReadMask);\r\n        }\r\n    }\r\n\r\n    public setStencilWriteMask(mask: number): void {\r\n        if (this._stencilWriteMask !== mask) {\r\n            this._stencilWriteMask = mask;\r\n            this._states[StatePosition.StencilWriteMask] = mask;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.StencilWriteMask);\r\n        }\r\n    }\r\n\r\n    public resetStencilState(): void {\r\n        this.setStencilState(false, Constants.ALWAYS, Constants.KEEP, Constants.REPLACE, Constants.KEEP, 0xff, 0xff);\r\n    }\r\n\r\n    public setStencilState(\r\n        stencilEnabled: boolean,\r\n        compare: Nullable<number>,\r\n        depthFailOp: Nullable<number>,\r\n        passOp: Nullable<number>,\r\n        failOp: Nullable<number>,\r\n        readMask: number,\r\n        writeMask: number\r\n    ): void {\r\n        this._stencilEnabled = stencilEnabled;\r\n        this._stencilFrontCompare = (compare ?? Constants.ALWAYS) - 0x0200;\r\n        this._stencilFrontDepthFailOp = depthFailOp === null ? 1 /* KEEP */ : stencilOpToIndex[depthFailOp];\r\n        this._stencilFrontPassOp = passOp === null ? 2 /* REPLACE */ : stencilOpToIndex[passOp];\r\n        this._stencilFrontFailOp = failOp === null ? 1 /* KEEP */ : stencilOpToIndex[failOp];\r\n        this.setStencilReadMask(readMask);\r\n        this.setStencilWriteMask(writeMask);\r\n    }\r\n\r\n    public setBuffers(\r\n        vertexBuffers: Nullable<{ [key: string]: Nullable<VertexBuffer> }>,\r\n        indexBuffer: Nullable<DataBuffer>,\r\n        overrideVertexBuffers: Nullable<{ [key: string]: Nullable<VertexBuffer> }>\r\n    ): void {\r\n        this._vertexBuffers = vertexBuffers;\r\n        this._overrideVertexBuffers = overrideVertexBuffers;\r\n        this._indexBuffer = indexBuffer;\r\n    }\r\n\r\n    private static _GetTopology(fillMode: number): GPUPrimitiveTopology {\r\n        switch (fillMode) {\r\n            // Triangle views\r\n            case Constants.MATERIAL_TriangleFillMode:\r\n                return WebGPUConstants.PrimitiveTopology.TriangleList;\r\n            case Constants.MATERIAL_PointFillMode:\r\n                return WebGPUConstants.PrimitiveTopology.PointList;\r\n            case Constants.MATERIAL_WireFrameFillMode:\r\n                return WebGPUConstants.PrimitiveTopology.LineList;\r\n            // Draw modes\r\n            case Constants.MATERIAL_PointListDrawMode:\r\n                return WebGPUConstants.PrimitiveTopology.PointList;\r\n            case Constants.MATERIAL_LineListDrawMode:\r\n                return WebGPUConstants.PrimitiveTopology.LineList;\r\n            case Constants.MATERIAL_LineLoopDrawMode:\r\n                // return this._gl.LINE_LOOP;\r\n                // TODO WEBGPU. Line Loop Mode Fallback at buffer load time.\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"LineLoop is an unsupported fillmode in WebGPU\";\r\n            case Constants.MATERIAL_LineStripDrawMode:\r\n                return WebGPUConstants.PrimitiveTopology.LineStrip;\r\n            case Constants.MATERIAL_TriangleStripDrawMode:\r\n                return WebGPUConstants.PrimitiveTopology.TriangleStrip;\r\n            case Constants.MATERIAL_TriangleFanDrawMode:\r\n                // return this._gl.TRIANGLE_FAN;\r\n                // TODO WEBGPU. Triangle Fan Mode Fallback at buffer load time.\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"TriangleFan is an unsupported fillmode in WebGPU\";\r\n            default:\r\n                return WebGPUConstants.PrimitiveTopology.TriangleList;\r\n        }\r\n    }\r\n\r\n    private static _GetAphaBlendOperation(operation: Nullable<number>): GPUBlendOperation {\r\n        switch (operation) {\r\n            case Constants.GL_ALPHA_EQUATION_ADD:\r\n                return WebGPUConstants.BlendOperation.Add;\r\n            case Constants.GL_ALPHA_EQUATION_SUBTRACT:\r\n                return WebGPUConstants.BlendOperation.Subtract;\r\n            case Constants.GL_ALPHA_EQUATION_REVERSE_SUBTRACT:\r\n                return WebGPUConstants.BlendOperation.ReverseSubtract;\r\n            case Constants.GL_ALPHA_EQUATION_MIN:\r\n                return WebGPUConstants.BlendOperation.Min;\r\n            case Constants.GL_ALPHA_EQUATION_MAX:\r\n                return WebGPUConstants.BlendOperation.Max;\r\n            default:\r\n                return WebGPUConstants.BlendOperation.Add;\r\n        }\r\n    }\r\n\r\n    private static _GetAphaBlendFactor(factor: Nullable<number>): GPUBlendFactor {\r\n        switch (factor) {\r\n            case 0:\r\n                return WebGPUConstants.BlendFactor.Zero;\r\n            case 1:\r\n                return WebGPUConstants.BlendFactor.One;\r\n            case Constants.GL_ALPHA_FUNCTION_SRC:\r\n                return WebGPUConstants.BlendFactor.Src;\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_SRC_COLOR:\r\n                return WebGPUConstants.BlendFactor.OneMinusSrc;\r\n            case Constants.GL_ALPHA_FUNCTION_SRC_ALPHA:\r\n                return WebGPUConstants.BlendFactor.SrcAlpha;\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_SRC_ALPHA:\r\n                return WebGPUConstants.BlendFactor.OneMinusSrcAlpha;\r\n            case Constants.GL_ALPHA_FUNCTION_DST_ALPHA:\r\n                return WebGPUConstants.BlendFactor.DstAlpha;\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_DST_ALPHA:\r\n                return WebGPUConstants.BlendFactor.OneMinusDstAlpha;\r\n            case Constants.GL_ALPHA_FUNCTION_DST_COLOR:\r\n                return WebGPUConstants.BlendFactor.Dst;\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_DST_COLOR:\r\n                return WebGPUConstants.BlendFactor.OneMinusDst;\r\n            case Constants.GL_ALPHA_FUNCTION_SRC_ALPHA_SATURATED:\r\n                return WebGPUConstants.BlendFactor.SrcAlphaSaturated;\r\n            case Constants.GL_ALPHA_FUNCTION_CONSTANT_COLOR:\r\n                return WebGPUConstants.BlendFactor.Constant;\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_CONSTANT_COLOR:\r\n                return WebGPUConstants.BlendFactor.OneMinusConstant;\r\n            case Constants.GL_ALPHA_FUNCTION_CONSTANT_ALPHA:\r\n                return WebGPUConstants.BlendFactor.Constant;\r\n            case Constants.GL_ALPHA_FUNCTION_ONE_MINUS_CONSTANT_ALPHA:\r\n                return WebGPUConstants.BlendFactor.OneMinusConstant;\r\n            default:\r\n                return WebGPUConstants.BlendFactor.One;\r\n        }\r\n    }\r\n\r\n    private static _GetCompareFunction(compareFunction: number): GPUCompareFunction {\r\n        switch (compareFunction) {\r\n            case 0: // NEVER\r\n                return WebGPUConstants.CompareFunction.Never;\r\n            case 1: // LESS\r\n                return WebGPUConstants.CompareFunction.Less;\r\n            case 2: // EQUAL\r\n                return WebGPUConstants.CompareFunction.Equal;\r\n            case 3: // LEQUAL\r\n                return WebGPUConstants.CompareFunction.LessEqual;\r\n            case 4: // GREATER\r\n                return WebGPUConstants.CompareFunction.Greater;\r\n            case 5: // NOTEQUAL\r\n                return WebGPUConstants.CompareFunction.NotEqual;\r\n            case 6: // GEQUAL\r\n                return WebGPUConstants.CompareFunction.GreaterEqual;\r\n            case 7: // ALWAYS\r\n                return WebGPUConstants.CompareFunction.Always;\r\n        }\r\n        return WebGPUConstants.CompareFunction.Never;\r\n    }\r\n\r\n    private static _GetStencilOpFunction(operation: number): GPUStencilOperation {\r\n        switch (operation) {\r\n            case 0:\r\n                return WebGPUConstants.StencilOperation.Zero;\r\n            case 1:\r\n                return WebGPUConstants.StencilOperation.Keep;\r\n            case 2:\r\n                return WebGPUConstants.StencilOperation.Replace;\r\n            case 3:\r\n                return WebGPUConstants.StencilOperation.IncrementClamp;\r\n            case 4:\r\n                return WebGPUConstants.StencilOperation.DecrementClamp;\r\n            case 5:\r\n                return WebGPUConstants.StencilOperation.Invert;\r\n            case 6:\r\n                return WebGPUConstants.StencilOperation.IncrementWrap;\r\n            case 7:\r\n                return WebGPUConstants.StencilOperation.DecrementWrap;\r\n        }\r\n        return WebGPUConstants.StencilOperation.Keep;\r\n    }\r\n\r\n    private static _GetVertexInputDescriptorFormat(vertexBuffer: VertexBuffer): GPUVertexFormat {\r\n        const type = vertexBuffer.type;\r\n        const normalized = vertexBuffer.normalized;\r\n        const size = vertexBuffer.getSize();\r\n\r\n        switch (type) {\r\n            case VertexBuffer.BYTE:\r\n                switch (size) {\r\n                    case 1:\r\n                    case 2:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Snorm8x2 : WebGPUConstants.VertexFormat.Sint8x2;\r\n                    case 3:\r\n                    case 4:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Snorm8x4 : WebGPUConstants.VertexFormat.Sint8x4;\r\n                }\r\n                break;\r\n            case VertexBuffer.UNSIGNED_BYTE:\r\n                switch (size) {\r\n                    case 1:\r\n                    case 2:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Unorm8x2 : WebGPUConstants.VertexFormat.Uint8x2;\r\n                    case 3:\r\n                    case 4:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Unorm8x4 : WebGPUConstants.VertexFormat.Uint8x4;\r\n                }\r\n                break;\r\n            case VertexBuffer.SHORT:\r\n                switch (size) {\r\n                    case 1:\r\n                    case 2:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Snorm16x2 : WebGPUConstants.VertexFormat.Sint16x2;\r\n                    case 3:\r\n                    case 4:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Snorm16x4 : WebGPUConstants.VertexFormat.Sint16x4;\r\n                }\r\n                break;\r\n            case VertexBuffer.UNSIGNED_SHORT:\r\n                switch (size) {\r\n                    case 1:\r\n                    case 2:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Unorm16x2 : WebGPUConstants.VertexFormat.Uint16x2;\r\n                    case 3:\r\n                    case 4:\r\n                        return normalized ? WebGPUConstants.VertexFormat.Unorm16x4 : WebGPUConstants.VertexFormat.Uint16x4;\r\n                }\r\n                break;\r\n            case VertexBuffer.INT:\r\n                switch (size) {\r\n                    case 1:\r\n                        return WebGPUConstants.VertexFormat.Sint32;\r\n                    case 2:\r\n                        return WebGPUConstants.VertexFormat.Sint32x2;\r\n                    case 3:\r\n                        return WebGPUConstants.VertexFormat.Sint32x3;\r\n                    case 4:\r\n                        return WebGPUConstants.VertexFormat.Sint32x4;\r\n                }\r\n                break;\r\n            case VertexBuffer.UNSIGNED_INT:\r\n                switch (size) {\r\n                    case 1:\r\n                        return WebGPUConstants.VertexFormat.Uint32;\r\n                    case 2:\r\n                        return WebGPUConstants.VertexFormat.Uint32x2;\r\n                    case 3:\r\n                        return WebGPUConstants.VertexFormat.Uint32x3;\r\n                    case 4:\r\n                        return WebGPUConstants.VertexFormat.Uint32x4;\r\n                }\r\n                break;\r\n            case VertexBuffer.FLOAT:\r\n                switch (size) {\r\n                    case 1:\r\n                        return WebGPUConstants.VertexFormat.Float32;\r\n                    case 2:\r\n                        return WebGPUConstants.VertexFormat.Float32x2;\r\n                    case 3:\r\n                        return WebGPUConstants.VertexFormat.Float32x3;\r\n                    case 4:\r\n                        return WebGPUConstants.VertexFormat.Float32x4;\r\n                }\r\n                break;\r\n        }\r\n\r\n        throw new Error(`Invalid Format '${vertexBuffer.getKind()}' - type=${type}, normalized=${normalized}, size=${size}`);\r\n    }\r\n\r\n    private _getAphaBlendState(): Nullable<GPUBlendComponent> {\r\n        if (!this._alphaBlendEnabled) {\r\n            return null;\r\n        }\r\n\r\n        return {\r\n            srcFactor: WebGPUCacheRenderPipeline._GetAphaBlendFactor(this._alphaBlendFuncParams[2]),\r\n            dstFactor: WebGPUCacheRenderPipeline._GetAphaBlendFactor(this._alphaBlendFuncParams[3]),\r\n            operation: WebGPUCacheRenderPipeline._GetAphaBlendOperation(this._alphaBlendEqParams[1]),\r\n        };\r\n    }\r\n\r\n    private _getColorBlendState(): Nullable<GPUBlendComponent> {\r\n        if (!this._alphaBlendEnabled) {\r\n            return null;\r\n        }\r\n\r\n        return {\r\n            srcFactor: WebGPUCacheRenderPipeline._GetAphaBlendFactor(this._alphaBlendFuncParams[0]),\r\n            dstFactor: WebGPUCacheRenderPipeline._GetAphaBlendFactor(this._alphaBlendFuncParams[1]),\r\n            operation: WebGPUCacheRenderPipeline._GetAphaBlendOperation(this._alphaBlendEqParams[0]),\r\n        };\r\n    }\r\n\r\n    private _setShaderStage(id: number): void {\r\n        if (this._shaderId !== id) {\r\n            this._shaderId = id;\r\n            this._states[StatePosition.ShaderStage] = id;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.ShaderStage);\r\n        }\r\n    }\r\n\r\n    private _setRasterizationState(topology: number, sampleCount: number): void {\r\n        const frontFace = this._frontFace;\r\n        const cullMode = this._cullEnabled ? this._cullFace : 0;\r\n        const clampDepth = this._clampDepth ? 1 : 0;\r\n        const alphaToCoverage = this._alphaToCoverageEnabled ? 1 : 0;\r\n        const rasterizationState = frontFace - 1 + (cullMode << 1) + (clampDepth << 3) + (alphaToCoverage << 4) + (topology << 5) + (sampleCount << 8);\r\n\r\n        if (this._rasterizationState !== rasterizationState) {\r\n            this._rasterizationState = rasterizationState;\r\n            this._states[StatePosition.RasterizationState] = this._rasterizationState;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.RasterizationState);\r\n        }\r\n    }\r\n\r\n    private _setColorStates(): void {\r\n        let colorStates = ((this._writeMask ? 1 : 0) << 22) + (this._colorFormat << 23) + ((this._depthWriteEnabled ? 1 : 0) << 29); // this state has been moved from depthStencilState here because alpha and depth are related (generally when alpha is on, depth write is off and the other way around)\r\n\r\n        if (this._alphaBlendEnabled) {\r\n            colorStates +=\r\n                ((this._alphaBlendFuncParams[0] === null ? 2 : alphaBlendFactorToIndex[this._alphaBlendFuncParams[0]]) << 0) +\r\n                ((this._alphaBlendFuncParams[1] === null ? 2 : alphaBlendFactorToIndex[this._alphaBlendFuncParams[1]]) << 4) +\r\n                ((this._alphaBlendFuncParams[2] === null ? 2 : alphaBlendFactorToIndex[this._alphaBlendFuncParams[2]]) << 8) +\r\n                ((this._alphaBlendFuncParams[3] === null ? 2 : alphaBlendFactorToIndex[this._alphaBlendFuncParams[3]]) << 12) +\r\n                ((this._alphaBlendEqParams[0] === null ? 1 : this._alphaBlendEqParams[0] - 0x8005) << 16) +\r\n                ((this._alphaBlendEqParams[1] === null ? 1 : this._alphaBlendEqParams[1] - 0x8005) << 19);\r\n        }\r\n\r\n        if (colorStates !== this._colorStates) {\r\n            this._colorStates = colorStates;\r\n            this._states[StatePosition.ColorStates] = this._colorStates;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.ColorStates);\r\n        }\r\n    }\r\n\r\n    private _setDepthStencilState(): void {\r\n        const stencilState = !this._stencilEnabled\r\n            ? 7 /* ALWAYS */ + (1 /* KEEP */ << 3) + (1 /* KEEP */ << 6) + (1 /* KEEP */ << 9)\r\n            : this._stencilFrontCompare + (this._stencilFrontDepthFailOp << 3) + (this._stencilFrontPassOp << 6) + (this._stencilFrontFailOp << 9);\r\n\r\n        const depthStencilState = this._depthStencilFormat + ((this._depthTestEnabled ? this._depthCompare : 7) /* ALWAYS */ << 6) + (stencilState << 10); // stencil front - stencil back is the same\r\n\r\n        if (this._depthStencilState !== depthStencilState) {\r\n            this._depthStencilState = depthStencilState;\r\n            this._states[StatePosition.DepthStencilState] = this._depthStencilState;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.DepthStencilState);\r\n        }\r\n    }\r\n\r\n    private _setVertexState(effect: Effect): void {\r\n        const currStateLen = this._statesLength;\r\n        let newNumStates = StatePosition.VertexState;\r\n\r\n        const webgpuPipelineContext = effect._pipelineContext as WebGPUPipelineContext;\r\n        const attributes = webgpuPipelineContext.shaderProcessingContext.attributeNamesFromEffect;\r\n        const locations = webgpuPipelineContext.shaderProcessingContext.attributeLocationsFromEffect;\r\n\r\n        let currentGPUBuffer;\r\n        let numVertexBuffers = 0;\r\n        for (let index = 0; index < attributes.length; index++) {\r\n            const location = locations[index];\r\n            let vertexBuffer = (this._overrideVertexBuffers && this._overrideVertexBuffers[attributes[index]]) ?? this._vertexBuffers![attributes[index]];\r\n            if (!vertexBuffer) {\r\n                // In WebGL it's valid to not bind a vertex buffer to an attribute, but it's not valid in WebGPU\r\n                // So we must bind a dummy buffer when we are not given one for a specific attribute\r\n                vertexBuffer = this._emptyVertexBuffer;\r\n            }\r\n\r\n            const buffer = vertexBuffer.effectiveBuffer?.underlyingResource;\r\n\r\n            // We optimize usage of GPUVertexBufferLayout: we will create a single GPUVertexBufferLayout for all the attributes which follow each other and which use the same GPU buffer\r\n            // However, there are some constraints in the attribute.offset value range, so we must check for them before being able to reuse the same GPUVertexBufferLayout\r\n            // See _getVertexInputDescriptor() below\r\n            if (vertexBuffer._validOffsetRange === undefined) {\r\n                const offset = vertexBuffer.effectiveByteOffset;\r\n                const formatSize = vertexBuffer.getSize(true);\r\n                const byteStride = vertexBuffer.effectiveByteStride;\r\n\r\n                vertexBuffer._validOffsetRange =\r\n                    (offset + formatSize <= this._kMaxVertexBufferStride && byteStride === 0) || (byteStride !== 0 && offset + formatSize <= byteStride);\r\n            }\r\n\r\n            if (!(currentGPUBuffer && currentGPUBuffer === buffer && vertexBuffer._validOffsetRange)) {\r\n                // we can't combine the previous vertexBuffer with the current one\r\n                this.vertexBuffers[numVertexBuffers++] = vertexBuffer;\r\n                currentGPUBuffer = vertexBuffer._validOffsetRange ? buffer : null;\r\n            }\r\n\r\n            const vid = vertexBuffer.hashCode + (location << 7);\r\n\r\n            this._isDirty = this._isDirty || this._states[newNumStates] !== vid;\r\n            this._states[newNumStates++] = vid;\r\n        }\r\n\r\n        this.vertexBuffers.length = numVertexBuffers;\r\n\r\n        this._statesLength = newNumStates;\r\n        this._isDirty = this._isDirty || newNumStates !== currStateLen;\r\n        if (this._isDirty) {\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.VertexState);\r\n        }\r\n    }\r\n\r\n    private _setTextureState(textureState: number): void {\r\n        if (this._textureState !== textureState) {\r\n            this._textureState = textureState;\r\n            this._states[StatePosition.TextureStage] = this._textureState;\r\n            this._isDirty = true;\r\n            this._stateDirtyLowestIndex = Math.min(this._stateDirtyLowestIndex, StatePosition.TextureStage);\r\n        }\r\n    }\r\n\r\n    private _createPipelineLayout(webgpuPipelineContext: WebGPUPipelineContext): GPUPipelineLayout {\r\n        if (this._useTextureStage) {\r\n            return this._createPipelineLayoutWithTextureStage(webgpuPipelineContext);\r\n        }\r\n\r\n        const bindGroupLayouts: GPUBindGroupLayout[] = [];\r\n        const bindGroupLayoutEntries = webgpuPipelineContext.shaderProcessingContext.bindGroupLayoutEntries;\r\n\r\n        for (let i = 0; i < bindGroupLayoutEntries.length; i++) {\r\n            const setDefinition = bindGroupLayoutEntries[i];\r\n\r\n            bindGroupLayouts[i] = this._device.createBindGroupLayout({\r\n                entries: setDefinition,\r\n            });\r\n        }\r\n\r\n        webgpuPipelineContext.bindGroupLayouts[0] = bindGroupLayouts;\r\n\r\n        return this._device.createPipelineLayout({ bindGroupLayouts });\r\n    }\r\n\r\n    private _createPipelineLayoutWithTextureStage(webgpuPipelineContext: WebGPUPipelineContext): GPUPipelineLayout {\r\n        const shaderProcessingContext = webgpuPipelineContext.shaderProcessingContext;\r\n        const bindGroupLayoutEntries = shaderProcessingContext.bindGroupLayoutEntries;\r\n\r\n        let bitVal = 1;\r\n        for (let i = 0; i < bindGroupLayoutEntries.length; i++) {\r\n            const setDefinition = bindGroupLayoutEntries[i];\r\n\r\n            for (let j = 0; j < setDefinition.length; j++) {\r\n                const entry = bindGroupLayoutEntries[i][j];\r\n\r\n                if (entry.texture) {\r\n                    const name = shaderProcessingContext.bindGroupLayoutEntryInfo[i][entry.binding].name;\r\n                    const textureInfo = shaderProcessingContext.availableTextures[name];\r\n                    const samplerInfo = textureInfo.autoBindSampler ? shaderProcessingContext.availableSamplers[name + WebGPUShaderProcessor.AutoSamplerSuffix] : null;\r\n\r\n                    let sampleType = textureInfo.sampleType;\r\n                    let samplerType = samplerInfo?.type ?? WebGPUConstants.SamplerBindingType.Filtering;\r\n\r\n                    if (this._textureState & bitVal && sampleType !== WebGPUConstants.TextureSampleType.Depth) {\r\n                        // The texture is a 32 bits float texture but the system does not support linear filtering for them OR the texture is a depth texture with \"float\" filtering:\r\n                        // we set the sampler to \"non-filtering\" and the texture sample type to \"unfilterable-float\"\r\n                        if (textureInfo.autoBindSampler) {\r\n                            samplerType = WebGPUConstants.SamplerBindingType.NonFiltering;\r\n                        }\r\n                        sampleType = WebGPUConstants.TextureSampleType.UnfilterableFloat;\r\n                    }\r\n\r\n                    entry.texture!.sampleType = sampleType;\r\n\r\n                    if (samplerInfo) {\r\n                        const binding = shaderProcessingContext.bindGroupLayoutEntryInfo[samplerInfo.binding.groupIndex][samplerInfo.binding.bindingIndex].index;\r\n                        bindGroupLayoutEntries[samplerInfo.binding.groupIndex][binding].sampler!.type = samplerType;\r\n                    }\r\n\r\n                    bitVal = bitVal << 1;\r\n                }\r\n            }\r\n        }\r\n\r\n        const bindGroupLayouts: GPUBindGroupLayout[] = [];\r\n\r\n        for (let i = 0; i < bindGroupLayoutEntries.length; ++i) {\r\n            bindGroupLayouts[i] = this._device.createBindGroupLayout({\r\n                entries: bindGroupLayoutEntries[i],\r\n            });\r\n        }\r\n\r\n        webgpuPipelineContext.bindGroupLayouts[this._textureState] = bindGroupLayouts;\r\n\r\n        return this._device.createPipelineLayout({ bindGroupLayouts });\r\n    }\r\n\r\n    private _getVertexInputDescriptor(effect: Effect): GPUVertexBufferLayout[] {\r\n        const descriptors: GPUVertexBufferLayout[] = [];\r\n        const webgpuPipelineContext = effect._pipelineContext as WebGPUPipelineContext;\r\n        const attributes = webgpuPipelineContext.shaderProcessingContext.attributeNamesFromEffect;\r\n        const locations = webgpuPipelineContext.shaderProcessingContext.attributeLocationsFromEffect;\r\n\r\n        let currentGPUBuffer;\r\n        let currentGPUAttributes: GPUVertexAttribute[] | undefined;\r\n        for (let index = 0; index < attributes.length; index++) {\r\n            const location = locations[index];\r\n            let vertexBuffer = (this._overrideVertexBuffers && this._overrideVertexBuffers[attributes[index]]) ?? this._vertexBuffers![attributes[index]];\r\n            if (!vertexBuffer) {\r\n                // In WebGL it's valid to not bind a vertex buffer to an attribute, but it's not valid in WebGPU\r\n                // So we must bind a dummy buffer when we are not given one for a specific attribute\r\n                vertexBuffer = this._emptyVertexBuffer;\r\n            }\r\n\r\n            let buffer = vertexBuffer.effectiveBuffer?.underlyingResource;\r\n\r\n            // We reuse the same GPUVertexBufferLayout for all attributes that use the same underlying GPU buffer (and for attributes that follow each other in the attributes array)\r\n            let offset = vertexBuffer.effectiveByteOffset;\r\n            const invalidOffsetRange = !vertexBuffer._validOffsetRange;\r\n            if (!(currentGPUBuffer && currentGPUAttributes && currentGPUBuffer === buffer) || invalidOffsetRange) {\r\n                const vertexBufferDescriptor: GPUVertexBufferLayout = {\r\n                    arrayStride: vertexBuffer.effectiveByteStride,\r\n                    stepMode: vertexBuffer.getIsInstanced() ? WebGPUConstants.VertexStepMode.Instance : WebGPUConstants.VertexStepMode.Vertex,\r\n                    attributes: [],\r\n                };\r\n\r\n                descriptors.push(vertexBufferDescriptor);\r\n                currentGPUAttributes = vertexBufferDescriptor.attributes;\r\n                if (invalidOffsetRange) {\r\n                    offset = 0; // the offset will be set directly in the setVertexBuffer call\r\n                    buffer = null; // buffer can't be reused\r\n                }\r\n            }\r\n\r\n            currentGPUAttributes.push({\r\n                shaderLocation: location,\r\n                offset,\r\n                format: WebGPUCacheRenderPipeline._GetVertexInputDescriptorFormat(vertexBuffer),\r\n            });\r\n\r\n            currentGPUBuffer = buffer;\r\n        }\r\n\r\n        return descriptors;\r\n    }\r\n\r\n    private _processNonFloatVertexBuffers(webgpuPipelineContext: WebGPUPipelineContext, effect: Effect) {\r\n        const webgpuShaderProcessor = webgpuPipelineContext.engine._getShaderProcessor(webgpuPipelineContext.shaderProcessingContext.shaderLanguage) as WebGPUShaderProcessor;\r\n\r\n        let reprocessShaders = false;\r\n\r\n        for (const kind in this._vertexBuffers) {\r\n            const currentVertexBuffer = this._vertexBuffers[kind];\r\n\r\n            if (!currentVertexBuffer || !vertexBufferKindForNonFloatProcessing[kind]) {\r\n                continue;\r\n            }\r\n\r\n            const currentVertexBufferType = currentVertexBuffer.normalized ? VertexBuffer.FLOAT : currentVertexBuffer.type;\r\n            const vertexBufferType = webgpuPipelineContext.vertexBufferKindToType[kind];\r\n\r\n            if (\r\n                (currentVertexBufferType !== VertexBuffer.FLOAT && vertexBufferType === undefined) ||\r\n                (vertexBufferType !== undefined && vertexBufferType !== currentVertexBufferType)\r\n            ) {\r\n                reprocessShaders = true;\r\n                webgpuPipelineContext.vertexBufferKindToType[kind] = currentVertexBufferType;\r\n                if (currentVertexBufferType !== VertexBuffer.FLOAT) {\r\n                    webgpuShaderProcessor.vertexBufferKindToNumberOfComponents[kind] = VertexBuffer.DeduceStride(kind);\r\n                    if (WebGPUCacheRenderPipeline._IsSignedType(currentVertexBufferType)) {\r\n                        webgpuShaderProcessor.vertexBufferKindToNumberOfComponents[kind] *= -1;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (reprocessShaders) {\r\n            effect._processShaderCode(webgpuShaderProcessor, true);\r\n        }\r\n    }\r\n\r\n    private _createRenderPipeline(effect: Effect, topology: GPUPrimitiveTopology, sampleCount: number): GPURenderPipeline {\r\n        const webgpuPipelineContext = effect._pipelineContext as WebGPUPipelineContext;\r\n        const inputStateDescriptor = this._getVertexInputDescriptor(effect);\r\n        const pipelineLayout = this._createPipelineLayout(webgpuPipelineContext);\r\n\r\n        const colorStates: Array<GPUColorTargetState | null> = [];\r\n        const alphaBlend = this._getAphaBlendState();\r\n        const colorBlend = this._getColorBlendState();\r\n\r\n        this._processNonFloatVertexBuffers(webgpuPipelineContext, effect);\r\n\r\n        if (this._mrtAttachments1 > 0) {\r\n            for (let i = 0; i < this._mrtFormats.length; ++i) {\r\n                const format = this._mrtFormats[i];\r\n                if (format) {\r\n                    const descr: GPUColorTargetState = {\r\n                        format,\r\n                        writeMask: (this._mrtEnabledMask & (1 << i)) !== 0 ? this._writeMask : 0,\r\n                    };\r\n                    if (alphaBlend && colorBlend) {\r\n                        descr.blend = {\r\n                            alpha: alphaBlend,\r\n                            color: colorBlend,\r\n                        };\r\n                    }\r\n                    colorStates.push(descr);\r\n                } else {\r\n                    colorStates.push(null);\r\n                }\r\n            }\r\n        } else {\r\n            if (this._webgpuColorFormat[0]) {\r\n                const descr: GPUColorTargetState = {\r\n                    format: this._webgpuColorFormat[0],\r\n                    writeMask: this._writeMask,\r\n                };\r\n                if (alphaBlend && colorBlend) {\r\n                    descr.blend = {\r\n                        alpha: alphaBlend,\r\n                        color: colorBlend,\r\n                    };\r\n                }\r\n                colorStates.push(descr);\r\n            } else {\r\n                colorStates.push(null);\r\n            }\r\n        }\r\n\r\n        const stencilFrontBack: GPUStencilFaceState = {\r\n            compare: WebGPUCacheRenderPipeline._GetCompareFunction(this._stencilEnabled ? this._stencilFrontCompare : 7 /* ALWAYS */),\r\n            depthFailOp: WebGPUCacheRenderPipeline._GetStencilOpFunction(this._stencilEnabled ? this._stencilFrontDepthFailOp : 1 /* KEEP */),\r\n            failOp: WebGPUCacheRenderPipeline._GetStencilOpFunction(this._stencilEnabled ? this._stencilFrontFailOp : 1 /* KEEP */),\r\n            passOp: WebGPUCacheRenderPipeline._GetStencilOpFunction(this._stencilEnabled ? this._stencilFrontPassOp : 1 /* KEEP */),\r\n        };\r\n\r\n        let stripIndexFormat: GPUIndexFormat | undefined = undefined;\r\n        if (topology === WebGPUConstants.PrimitiveTopology.LineStrip || topology === WebGPUConstants.PrimitiveTopology.TriangleStrip) {\r\n            stripIndexFormat = !this._indexBuffer || this._indexBuffer.is32Bits ? WebGPUConstants.IndexFormat.Uint32 : WebGPUConstants.IndexFormat.Uint16;\r\n        }\r\n\r\n        const depthStencilFormatHasStencil = this._webgpuDepthStencilFormat ? WebGPUTextureHelper.HasStencilAspect(this._webgpuDepthStencilFormat) : false;\r\n\r\n        return this._device.createRenderPipeline({\r\n            label: `RenderPipeline_${colorStates[0]?.format ?? \"nooutput\"}_${this._webgpuDepthStencilFormat ?? \"nodepth\"}_samples${sampleCount}_textureState${this._textureState}`,\r\n            layout: pipelineLayout,\r\n            vertex: {\r\n                module: webgpuPipelineContext.stages!.vertexStage.module,\r\n                entryPoint: webgpuPipelineContext.stages!.vertexStage.entryPoint,\r\n                buffers: inputStateDescriptor,\r\n            },\r\n            primitive: {\r\n                topology,\r\n                stripIndexFormat,\r\n                frontFace: this._frontFace === 1 ? WebGPUConstants.FrontFace.CCW : WebGPUConstants.FrontFace.CW,\r\n                cullMode: !this._cullEnabled ? WebGPUConstants.CullMode.None : this._cullFace === 2 ? WebGPUConstants.CullMode.Front : WebGPUConstants.CullMode.Back,\r\n            },\r\n            fragment: !webgpuPipelineContext.stages!.fragmentStage\r\n                ? undefined\r\n                : {\r\n                      module: webgpuPipelineContext.stages!.fragmentStage.module,\r\n                      entryPoint: webgpuPipelineContext.stages!.fragmentStage.entryPoint,\r\n                      targets: colorStates,\r\n                  },\r\n\r\n            multisample: {\r\n                count: sampleCount,\r\n                /*mask,\r\n                alphaToCoverageEnabled,*/\r\n            },\r\n            depthStencil:\r\n                this._webgpuDepthStencilFormat === undefined\r\n                    ? undefined\r\n                    : {\r\n                          depthWriteEnabled: this._depthWriteEnabled,\r\n                          depthCompare: this._depthTestEnabled ? WebGPUCacheRenderPipeline._GetCompareFunction(this._depthCompare) : WebGPUConstants.CompareFunction.Always,\r\n                          format: this._webgpuDepthStencilFormat,\r\n                          stencilFront: this._stencilEnabled && depthStencilFormatHasStencil ? stencilFrontBack : undefined,\r\n                          stencilBack: this._stencilEnabled && depthStencilFormatHasStencil ? stencilFrontBack : undefined,\r\n                          stencilReadMask: this._stencilEnabled && depthStencilFormatHasStencil ? this._stencilReadMask : undefined,\r\n                          stencilWriteMask: this._stencilEnabled && depthStencilFormatHasStencil ? this._stencilWriteMask : undefined,\r\n                          depthBias: this._depthBias,\r\n                          depthBiasClamp: this._depthBiasClamp,\r\n                          depthBiasSlopeScale: this._depthBiasSlopeScale,\r\n                      },\r\n        });\r\n    }\r\n}\r\n"]}