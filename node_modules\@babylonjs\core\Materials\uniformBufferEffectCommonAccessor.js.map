{"version": 3, "file": "uniformBufferEffectCommonAccessor.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Materials/uniformBufferEffectCommonAccessor.ts"], "names": [], "mappings": "AAIA,gBAAgB;AAChB,MAAM,OAAO,iCAAiC;IAyClC,MAAM,CAAC,WAAmC;QAC9C,OAAQ,WAA6B,CAAC,UAAU,KAAK,SAAS,CAAC;IACnE,CAAC;IAED,YAAY,WAAmC;QAC3C,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;YAC1B,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClE,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClE,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpE,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAChE,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAChE,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9D,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9D,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxE,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACtD,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxD,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxD,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC3D;aAAM;YACH,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/D,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/D,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACvD,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzD,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzD,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzD,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjE,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACvD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC7D,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC7D,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC3D,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC3D,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzD,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzD,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACrE,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnD,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACrD,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACrD,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACxD;IACL,CAAC;CACJ", "sourcesContent": ["import type { IColor3<PERSON>ike, IColor4Like, IMatrixLike, IVector3Like, IVector4Like } from \"../Maths/math.like\";\r\nimport type { Effect } from \"./effect\";\r\nimport type { UniformBuffer } from \"./uniformBuffer\";\r\n\r\n/** @internal */\r\nexport class UniformBufferEffectCommonAccessor {\r\n    public setMatrix3x3: (name: string, matrix: Float32Array) => void;\r\n\r\n    public setMatrix2x2: (name: string, matrix: Float32Array) => void;\r\n\r\n    public setFloat: (name: string, x: number) => void;\r\n\r\n    public setFloat2: (name: string, x: number, y: number, suffix?: string) => void;\r\n\r\n    public setFloat3: (name: string, x: number, y: number, z: number, suffix?: string) => void;\r\n\r\n    public setFloat4: (name: string, x: number, y: number, z: number, w: number, suffix?: string) => void;\r\n\r\n    public setFloatArray: (name: string, array: Float32Array) => void;\r\n\r\n    public setArray: (name: string, array: number[]) => void;\r\n\r\n    public setIntArray: (name: string, array: Int32Array) => void;\r\n\r\n    public setMatrix: (name: string, mat: IMatrixLike) => void;\r\n\r\n    public setMatrices: (name: string, mat: Float32Array) => void;\r\n\r\n    public setVector3: (name: string, vector: IVector3Like) => void;\r\n\r\n    public setVector4: (name: string, vector: IVector4Like) => void;\r\n\r\n    public setColor3: (name: string, color: IColor3Like, suffix?: string) => void;\r\n\r\n    public setColor4: (name: string, color: IColor3Like, alpha: number, suffix?: string) => void;\r\n\r\n    public setDirectColor4: (name: string, color: IColor4Like) => void;\r\n\r\n    public setInt: (name: string, x: number, suffix?: string) => void;\r\n\r\n    public setInt2: (name: string, x: number, y: number, suffix?: string) => void;\r\n\r\n    public setInt3: (name: string, x: number, y: number, z: number, suffix?: string) => void;\r\n\r\n    public setInt4: (name: string, x: number, y: number, z: number, w: number, suffix?: string) => void;\r\n\r\n    private _isUbo(uboOrEffect: UniformBuffer | Effect): uboOrEffect is UniformBuffer {\r\n        return (uboOrEffect as UniformBuffer).addUniform !== undefined;\r\n    }\r\n\r\n    constructor(uboOrEffect: UniformBuffer | Effect) {\r\n        if (this._isUbo(uboOrEffect)) {\r\n            this.setMatrix3x3 = uboOrEffect.updateMatrix3x3.bind(uboOrEffect);\r\n            this.setMatrix2x2 = uboOrEffect.updateMatrix2x2.bind(uboOrEffect);\r\n            this.setFloat = uboOrEffect.updateFloat.bind(uboOrEffect);\r\n            this.setFloat2 = uboOrEffect.updateFloat2.bind(uboOrEffect);\r\n            this.setFloat3 = uboOrEffect.updateFloat3.bind(uboOrEffect);\r\n            this.setFloat4 = uboOrEffect.updateFloat4.bind(uboOrEffect);\r\n            this.setFloatArray = uboOrEffect.updateFloatArray.bind(uboOrEffect);\r\n            this.setArray = uboOrEffect.updateArray.bind(uboOrEffect);\r\n            this.setIntArray = uboOrEffect.updateIntArray.bind(uboOrEffect);\r\n            this.setMatrix = uboOrEffect.updateMatrix.bind(uboOrEffect);\r\n            this.setMatrices = uboOrEffect.updateMatrices.bind(uboOrEffect);\r\n            this.setVector3 = uboOrEffect.updateVector3.bind(uboOrEffect);\r\n            this.setVector4 = uboOrEffect.updateVector4.bind(uboOrEffect);\r\n            this.setColor3 = uboOrEffect.updateColor3.bind(uboOrEffect);\r\n            this.setColor4 = uboOrEffect.updateColor4.bind(uboOrEffect);\r\n            this.setDirectColor4 = uboOrEffect.updateDirectColor4.bind(uboOrEffect);\r\n            this.setInt = uboOrEffect.updateInt.bind(uboOrEffect);\r\n            this.setInt2 = uboOrEffect.updateInt2.bind(uboOrEffect);\r\n            this.setInt3 = uboOrEffect.updateInt3.bind(uboOrEffect);\r\n            this.setInt4 = uboOrEffect.updateInt4.bind(uboOrEffect);\r\n        } else {\r\n            this.setMatrix3x3 = uboOrEffect.setMatrix3x3.bind(uboOrEffect);\r\n            this.setMatrix2x2 = uboOrEffect.setMatrix2x2.bind(uboOrEffect);\r\n            this.setFloat = uboOrEffect.setFloat.bind(uboOrEffect);\r\n            this.setFloat2 = uboOrEffect.setFloat2.bind(uboOrEffect);\r\n            this.setFloat3 = uboOrEffect.setFloat3.bind(uboOrEffect);\r\n            this.setFloat4 = uboOrEffect.setFloat4.bind(uboOrEffect);\r\n            this.setFloatArray = uboOrEffect.setFloatArray.bind(uboOrEffect);\r\n            this.setArray = uboOrEffect.setArray.bind(uboOrEffect);\r\n            this.setIntArray = uboOrEffect.setIntArray.bind(uboOrEffect);\r\n            this.setMatrix = uboOrEffect.setMatrix.bind(uboOrEffect);\r\n            this.setMatrices = uboOrEffect.setMatrices.bind(uboOrEffect);\r\n            this.setVector3 = uboOrEffect.setVector3.bind(uboOrEffect);\r\n            this.setVector4 = uboOrEffect.setVector4.bind(uboOrEffect);\r\n            this.setColor3 = uboOrEffect.setColor3.bind(uboOrEffect);\r\n            this.setColor4 = uboOrEffect.setColor4.bind(uboOrEffect);\r\n            this.setDirectColor4 = uboOrEffect.setDirectColor4.bind(uboOrEffect);\r\n            this.setInt = uboOrEffect.setInt.bind(uboOrEffect);\r\n            this.setInt2 = uboOrEffect.setInt2.bind(uboOrEffect);\r\n            this.setInt3 = uboOrEffect.setInt3.bind(uboOrEffect);\r\n            this.setInt4 = uboOrEffect.setInt4.bind(uboOrEffect);\r\n        }\r\n    }\r\n}\r\n"]}