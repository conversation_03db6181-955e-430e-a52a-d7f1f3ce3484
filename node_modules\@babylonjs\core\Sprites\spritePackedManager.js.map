{"version": 3, "file": "spritePackedManager.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Sprites/spritePackedManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAExD;;;GAGG;AAEH,MAAM,OAAO,mBAAoB,SAAQ,aAAa;IAClD;;;;;;;;;;OAUG;IAEH;IACI,wCAAwC;IACjC,IAAY,EACnB,MAAc,EACd,QAAgB,EAChB,KAAY,EACZ,aAA4B,IAAI,EAChC,UAAkB,IAAI,EACtB,eAAuB,OAAO,CAAC,sBAAsB;QAErD,gIAAgI;QAChI,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QAT3E,SAAI,GAAJ,IAAI,CAAQ;IAUvB,CAAC;CACJ", "sourcesContent": ["import { Sprite<PERSON>anager } from \"./spriteManager\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\n\r\n/**\r\n * Class used to manage multiple sprites of different sizes on the same spritesheet\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/sprites\r\n */\r\n\r\nexport class SpritePackedManager extends SpriteManager {\r\n    /**\r\n     * Creates a new sprite manager from a packed sprite sheet\r\n     * @param name defines the manager's name\r\n     * @param imgUrl defines the sprite sheet url\r\n     * @param capacity defines the maximum allowed number of sprites\r\n     * @param scene defines the hosting scene\r\n     * @param spriteJSON null otherwise a JSON object defining sprite sheet data\r\n     * @param epsilon defines the epsilon value to align texture (0.01 by default)\r\n     * @param samplingMode defines the sampling mode to use with spritesheet\r\n     * @param fromPacked set to true; do not alter\r\n     */\r\n\r\n    constructor(\r\n        /** defines the packed manager's name */\r\n        public name: string,\r\n        imgUrl: string,\r\n        capacity: number,\r\n        scene: Scene,\r\n        spriteJSON: string | null = null,\r\n        epsilon: number = 0.01,\r\n        samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE\r\n    ) {\r\n        //the cellSize parameter is not used when built from JSON which provides individual cell data, defaults to 64 if JSON load fails\r\n        super(name, imgUrl, capacity, 64, scene, epsilon, samplingMode, true, spriteJSON);\r\n    }\r\n}\r\n"]}