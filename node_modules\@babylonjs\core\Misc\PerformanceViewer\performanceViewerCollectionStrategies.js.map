{"version": 3, "file": "performanceViewerCollectionStrategies.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Misc/PerformanceViewer/performanceViewerCollectionStrategies.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qBAAqB,EAAE,MAAM,6CAA6C,CAAC;AAEpF,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAE,oBAAoB,EAAE,MAAM,4CAA4C,CAAC;AAClF,OAAO,EAAE,uBAAuB,EAAE,MAAM,4BAA4B,CAAC;AAoBrE,8BAA8B;AAC9B,MAAM,kBAAkB,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;AAMpC;;GAEG;AACH,MAAM,OAAO,sBAAsB;IAC/B;;;OAGG;IACI,MAAM,CAAC,WAAW;QACrB,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;YACjC,OAAO;gBACH,EAAE,EAAE,KAAK;gBACT,OAAO,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC9B,OAAO,EAAE,kBAAkB;aAC9B,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,eAAe;QACzB,OAAO,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,mBAAmB;QAC7B,OAAO,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,EAAE,cAAc,CAAC,CAAC;IAC9E,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,gBAAgB;QAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAC9C,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,IAAY,EAAE,SAAmC,IAAI;QAClF,OAAO,GAAG,EAAE;YACR,IAAI,KAAK,GAAG,CAAC,CAAC;YAEd,MAAM,OAAO,GAAG,IAAI,uBAAuB,EAAE,CAAC;YAC9C,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAEvB,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACrC,KAAK,MAAM,MAAM,IAAI,MAAM,EAAE;oBACzB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;wBACjG,4DAA4D;wBAC5D,QAAQ,MAAM,CAAC,KAAK,EAAE;4BAClB,KAAK,SAAS;gCACV,KAAK,GAAG,CAAC,CAAC;gCACV,MAAM;4BACV,KAAK,MAAM;gCACP,KAAK,GAAG,IAAI,CAAC;gCACb,MAAM;4BACV,KAAK,SAAS;gCACV,KAAK,GAAG,GAAG,CAAC;gCACZ,MAAM;4BACV,KAAK,UAAU;gCACX,KAAK,GAAG,CAAC,CAAC;gCACV,MAAM;yBACb;qBACJ;iBACJ;YACL,CAAC,CAAC,CAAC;YACH,OAAO;gBACH,EAAE,EAAE,IAAI;gBACR,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK;gBACpB,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE;aACnC,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,mBAAmB;QAC7B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,OAAO;gBACH,EAAE,EAAE,cAAc;gBAClB,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;gBAClC,OAAO,EAAE,kBAAkB;aAC9B,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,oBAAoB;QAC9B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,OAAO;gBACH,EAAE,EAAE,eAAe;gBACnB,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,MAAM;gBAC7C,OAAO,EAAE,kBAAkB;aAC9B,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,qBAAqB;QAC/B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,OAAO;gBACH,EAAE,EAAE,gBAAgB;gBACpB,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAAE;gBACvC,OAAO,EAAE,kBAAkB;aAC9B,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,mBAAmB;QAC7B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,OAAO;gBACH,EAAE,EAAE,cAAc;gBAClB,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAAE,GAAG,CAAC;gBAC3C,OAAO,EAAE,kBAAkB;aAC9B,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,mBAAmB;QAC7B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,OAAO;gBACH,EAAE,EAAE,cAAc;gBAClB,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,cAAc,EAAE;gBACrC,OAAO,EAAE,kBAAkB;aAC9B,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,uBAAuB;QACjC,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,OAAO;gBACH,EAAE,EAAE,kBAAkB;gBACtB,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,kBAAkB,EAAE;gBACzC,OAAO,EAAE,kBAAkB;aAC9B,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,iBAAiB;QAC3B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,0BAA0B,GAAG,KAAK,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC3E,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;YACjD,CAAC,CAAC,CAAC;YAEH,MAAM,qBAAqB,GAAG,KAAK,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACjE,SAAS,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;YACrD,CAAC,CAAC,CAAC;YAEH,OAAO;gBACH,EAAE,EAAE,YAAY;gBAChB,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS;gBACxB,OAAO,EAAE,GAAG,EAAE;oBACV,KAAK,CAAC,4BAA4B,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;oBACtE,KAAK,CAAC,uBAAuB,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;gBAChE,CAAC;aACJ,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,mBAAmB;QAC7B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,OAAO;gBACH,EAAE,EAAE,cAAc;gBAClB,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;gBAClC,OAAO,EAAE,kBAAkB;aAC9B,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,qBAAqB;QAC/B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,OAAO;gBACH,EAAE,EAAE,gBAAgB;gBACpB,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAAE;gBACvC,OAAO,EAAE,kBAAkB;aAC9B,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,sBAAsB;QAChC,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,OAAO;gBACH,EAAE,EAAE,iBAAiB;gBACrB,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM;gBACrC,OAAO,EAAE,kBAAkB;aAC9B,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,qBAAqB;QAC/B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,OAAO;gBACH,EAAE,EAAE,gBAAgB;gBACpB,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM;gBACpC,OAAO,EAAE,kBAAkB;aAC9B,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,mBAAmB;QAC7B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,MAAM,oBAAoB,GAAG,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAC7D,oBAAoB,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAE7C,OAAO;gBACH,EAAE,EAAE,cAAc;gBAClB,OAAO,EAAE,GAAG,EAAE;oBACV,OAAO,MAAM,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,cAAc,CAAC;gBACzE,CAAC;gBACD,OAAO,EAAE,kBAAkB;aAC9B,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,uBAAuB;QACjC,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,IAAI,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,4BAA4B,GAAG,KAAK,CAAC,wCAAwC,CAAC,GAAG,CAAC,GAAG,EAAE;gBACzF,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,MAAM,2BAA2B,GAAG,KAAK,CAAC,uCAAuC,CAAC,GAAG,CAAC,GAAG,EAAE;gBACvF,SAAS,GAAG,aAAa,CAAC,GAAG,GAAG,SAAS,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,OAAO;gBACH,EAAE,EAAE,kBAAkB;gBACtB,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS;gBACxB,OAAO,EAAE,GAAG,EAAE;oBACV,KAAK,CAAC,wCAAwC,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;oBACpF,KAAK,CAAC,uCAAuC,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC;gBACtF,CAAC;aACJ,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,qBAAqB;QAC/B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,IAAI,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,6BAA6B,GAAG,KAAK,CAAC,qCAAqC,CAAC,GAAG,CAAC,GAAG,EAAE;gBACvF,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,MAAM,4BAA4B,GAAG,KAAK,CAAC,oCAAoC,CAAC,GAAG,CAAC,GAAG,EAAE;gBACrF,SAAS,GAAG,aAAa,CAAC,GAAG,GAAG,SAAS,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,OAAO;gBACH,EAAE,EAAE,gBAAgB;gBACpB,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS;gBACxB,OAAO,EAAE,GAAG,EAAE;oBACV,KAAK,CAAC,qCAAqC,CAAC,MAAM,CAAC,6BAA6B,CAAC,CAAC;oBAClF,KAAK,CAAC,oCAAoC,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;gBACpF,CAAC;aACJ,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,iBAAiB;QAC3B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,IAAI,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,yBAAyB,GAAG,KAAK,CAAC,oCAAoC,CAAC,GAAG,CAAC,GAAG,EAAE;gBAClF,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,MAAM,wBAAwB,GAAG,KAAK,CAAC,mCAAmC,CAAC,GAAG,CAAC,GAAG,EAAE;gBAChF,SAAS,GAAG,aAAa,CAAC,GAAG,GAAG,SAAS,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,OAAO;gBACH,EAAE,EAAE,WAAW;gBACf,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS;gBACxB,OAAO,EAAE,GAAG,EAAE;oBACV,KAAK,CAAC,oCAAoC,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;oBAC7E,KAAK,CAAC,mCAAmC,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;gBAC/E,CAAC;aACJ,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,eAAe;QACzB,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,IAAI,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,uBAAuB,GAAG,KAAK,CAAC,kCAAkC,EAAE,GAAG,CAAC,GAAG,EAAE;gBAC/E,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,MAAM,sBAAsB,GAAG,KAAK,CAAC,iCAAiC,EAAE,GAAG,CAAC,GAAG,EAAE;gBAC7E,SAAS,GAAG,aAAa,CAAC,GAAG,GAAG,SAAS,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,OAAO;gBACH,EAAE,EAAE,SAAS;gBACb,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS;gBACxB,OAAO,EAAE,GAAG,EAAE;oBACV,KAAK,CAAC,kCAAkC,EAAE,MAAM,CAAC,uBAAuB,CAAC,CAAC;oBAC1E,KAAK,CAAC,iCAAiC,EAAE,MAAM,CAAC,sBAAsB,CAAC,CAAC;gBAC5E,CAAC;aACJ,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,kBAAkB;QAC5B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,IAAI,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,0BAA0B,GAAG,KAAK,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC3E,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,MAAM,yBAAyB,GAAG,KAAK,CAAC,2BAA2B,CAAC,GAAG,CAAC,GAAG,EAAE;gBACzE,SAAS,GAAG,aAAa,CAAC,GAAG,GAAG,SAAS,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,OAAO;gBACH,EAAE,EAAE,YAAY;gBAChB,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS;gBACxB,OAAO,EAAE,GAAG,EAAE;oBACV,KAAK,CAAC,4BAA4B,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;oBACtE,KAAK,CAAC,2BAA2B,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;gBACxE,CAAC;aACJ,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,eAAe;QACzB,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,IAAI,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,uBAAuB,GAAG,KAAK,CAAC,yBAAyB,EAAE,GAAG,CAAC,GAAG,EAAE;gBACtE,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,MAAM,sBAAsB,GAAG,KAAK,CAAC,wBAAwB,EAAE,GAAG,CAAC,GAAG,EAAE;gBACpE,SAAS,GAAG,aAAa,CAAC,GAAG,GAAG,SAAS,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,OAAO;gBACH,EAAE,EAAE,SAAS;gBACb,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS;gBACxB,OAAO,EAAE,GAAG,EAAE;oBACV,KAAK,CAAC,yBAAyB,EAAE,MAAM,CAAC,uBAAuB,CAAC,CAAC;oBACjE,KAAK,CAAC,wBAAwB,EAAE,MAAM,CAAC,sBAAsB,CAAC,CAAC;gBACnE,CAAC;aACJ,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,cAAc;QACxB,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,IAAI,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,yBAAyB,GAAG,KAAK,CAAC,2BAA2B,CAAC,GAAG,CAAC,GAAG,EAAE;gBACzE,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,MAAM,wBAAwB,GAAG,KAAK,CAAC,0BAA0B,CAAC,GAAG,CAAC,GAAG,EAAE;gBACvE,SAAS,GAAG,aAAa,CAAC,GAAG,GAAG,SAAS,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,OAAO;gBACH,EAAE,EAAE,QAAQ;gBACZ,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS;gBACxB,OAAO,EAAE,GAAG,EAAE;oBACV,KAAK,CAAC,2BAA2B,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;oBACpE,KAAK,CAAC,0BAA0B,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;gBACtE,CAAC;aACJ,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,kBAAkB;QAC5B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,IAAI,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,MAAM,0BAA0B,GAAG,KAAK,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC3E,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,MAAM,qBAAqB,GAAG,KAAK,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACjE,SAAS,GAAG,aAAa,CAAC,GAAG,GAAG,SAAS,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,OAAO;gBACH,EAAE,EAAE,aAAa;gBACjB,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS;gBACxB,OAAO,EAAE,GAAG,EAAE;oBACV,KAAK,CAAC,4BAA4B,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;oBACtE,KAAK,CAAC,uBAAuB,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;gBAChE,CAAC;aACJ,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,kBAAkB;QAC5B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,IAAI,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,0BAA0B,GAAG,KAAK,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC3E,SAAS,GAAG,aAAa,CAAC,GAAG,GAAG,SAAS,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,MAAM,qBAAqB,GAAG,KAAK,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACjE,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,OAAO;gBACH,EAAE,EAAE,aAAa;gBACjB,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS;gBACxB,OAAO,EAAE,GAAG,EAAE;oBACV,KAAK,CAAC,4BAA4B,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;oBACtE,KAAK,CAAC,uBAAuB,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;gBAChE,CAAC;aACJ,CAAC;QACN,CAAC,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,oBAAoB;QAC9B,OAAO,CAAC,KAAK,EAAE,EAAE;YACb,MAAM,qBAAqB,GAAG,IAAI,qBAAqB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;YAC3E,qBAAqB,CAAC,mBAAmB,GAAG,IAAI,CAAC;YACjD,OAAO;gBACH,EAAE,EAAE,gBAAgB;gBACpB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,OAAO,GAAG,QAAQ,EAAE,CAAC,CAAC;gBACxF,OAAO,EAAE,GAAG,EAAE;oBACV,qBAAqB,CAAC,OAAO,EAAE,CAAC;gBACpC,CAAC;aACJ,CAAC;QACN,CAAC,CAAC;IACN,CAAC;CACJ", "sourcesContent": ["import { EngineInstrumentation } from \"../../Instrumentation/engineInstrumentation\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { PrecisionDate } from \"../precisionDate\";\r\nimport { SceneInstrumentation } from \"../../Instrumentation/sceneInstrumentation\";\r\nimport { PressureObserverWrapper } from \"../pressureObserverWrapper\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * Defines the general structure of what is necessary for a collection strategy.\r\n */\r\nexport interface IPerfViewerCollectionStrategy {\r\n    /**\r\n     * The id of the strategy.\r\n     */\r\n    id: string;\r\n    /**\r\n     * Function which gets the data for the strategy.\r\n     */\r\n    getData: () => number;\r\n    /**\r\n     * Function which does any necessary cleanup. Called when performanceViewerCollector.dispose() is called.\r\n     */\r\n    dispose: () => void;\r\n}\r\n// Dispose which does nothing.\r\nconst defaultDisposeImpl = () => {};\r\n\r\n/**\r\n * Initializer callback for a strategy\r\n */\r\nexport type PerfStrategyInitialization = (scene: Scene) => IPerfViewerCollectionStrategy;\r\n/**\r\n * Defines the predefined strategies used in the performance viewer.\r\n */\r\nexport class PerfCollectionStrategy {\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of fps metrics\r\n     * @returns the initializer for the fps strategy\r\n     */\r\n    public static FpsStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            const engine = scene.getEngine();\r\n            return {\r\n                id: \"FPS\",\r\n                getData: () => engine.getFps(),\r\n                dispose: defaultDisposeImpl,\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of thermal utilization metrics.\r\n     * Needs the experimental pressure API.\r\n     * @returns the initializer for the thermal utilization strategy\r\n     */\r\n    public static ThermalStrategy(): PerfStrategyInitialization {\r\n        return this._PressureStrategy(\"Thermal utilization\", \"thermal\");\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of power supply utilization metrics.\r\n     * Needs the experimental pressure API.\r\n     * @returns the initializer for the power supply utilization strategy\r\n     */\r\n    public static PowerSupplyStrategy(): PerfStrategyInitialization {\r\n        return this._PressureStrategy(\"Power supply utilization\", \"power-supply\");\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of pressure metrics.\r\n     * Needs the experimental pressure API.\r\n     * @returns the initializer for the pressure strategy\r\n     */\r\n    public static PressureStrategy(): PerfStrategyInitialization {\r\n        return this._PressureStrategy(\"Pressure\");\r\n    }\r\n\r\n    private static _PressureStrategy(name: string, factor: Nullable<PressureFactor> = null): PerfStrategyInitialization {\r\n        return () => {\r\n            let value = 0;\r\n\r\n            const wrapper = new PressureObserverWrapper();\r\n            wrapper.observe(\"cpu\");\r\n\r\n            wrapper.onPressureChanged.add((update) => {\r\n                for (const record of update) {\r\n                    if ((factor && record.factors.includes(factor)) || (!factor && (record.factors?.length ?? 0) === 0)) {\r\n                        // Let s consider each step being 25% of the total pressure.\r\n                        switch (record.state) {\r\n                            case \"nominal\":\r\n                                value = 0;\r\n                                break;\r\n                            case \"fair\":\r\n                                value = 0.25;\r\n                                break;\r\n                            case \"serious\":\r\n                                value = 0.5;\r\n                                break;\r\n                            case \"critical\":\r\n                                value = 1;\r\n                                break;\r\n                        }\r\n                    }\r\n                }\r\n            });\r\n            return {\r\n                id: name,\r\n                getData: () => value,\r\n                dispose: () => wrapper.dispose(),\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of total meshes metrics.\r\n     * @returns the initializer for the total meshes strategy\r\n     */\r\n    public static TotalMeshesStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            return {\r\n                id: \"Total meshes\",\r\n                getData: () => scene.meshes.length,\r\n                dispose: defaultDisposeImpl,\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of active meshes metrics.\r\n     * @returns the initializer for the active meshes strategy\r\n     */\r\n    public static ActiveMeshesStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            return {\r\n                id: \"Active meshes\",\r\n                getData: () => scene.getActiveMeshes().length,\r\n                dispose: defaultDisposeImpl,\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of active indices metrics.\r\n     * @returns the initializer for the active indices strategy\r\n     */\r\n    public static ActiveIndicesStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            return {\r\n                id: \"Active indices\",\r\n                getData: () => scene.getActiveIndices(),\r\n                dispose: defaultDisposeImpl,\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of active faces metrics.\r\n     * @returns the initializer for the active faces strategy\r\n     */\r\n    public static ActiveFacesStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            return {\r\n                id: \"Active faces\",\r\n                getData: () => scene.getActiveIndices() / 3,\r\n                dispose: defaultDisposeImpl,\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of active bones metrics.\r\n     * @returns the initializer for the active bones strategy\r\n     */\r\n    public static ActiveBonesStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            return {\r\n                id: \"Active bones\",\r\n                getData: () => scene.getActiveBones(),\r\n                dispose: defaultDisposeImpl,\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of active particles metrics.\r\n     * @returns the initializer for the active particles strategy\r\n     */\r\n    public static ActiveParticlesStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            return {\r\n                id: \"Active particles\",\r\n                getData: () => scene.getActiveParticles(),\r\n                dispose: defaultDisposeImpl,\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of draw calls metrics.\r\n     * @returns the initializer for the draw calls strategy\r\n     */\r\n    public static DrawCallsStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            let drawCalls = 0;\r\n            const onBeforeAnimationsObserver = scene.onBeforeAnimationsObservable.add(() => {\r\n                scene.getEngine()._drawCalls.fetchNewFrame();\r\n            });\r\n\r\n            const onAfterRenderObserver = scene.onAfterRenderObservable.add(() => {\r\n                drawCalls = scene.getEngine()._drawCalls.current;\r\n            });\r\n\r\n            return {\r\n                id: \"Draw calls\",\r\n                getData: () => drawCalls,\r\n                dispose: () => {\r\n                    scene.onBeforeAnimationsObservable.remove(onBeforeAnimationsObserver);\r\n                    scene.onAfterRenderObservable.remove(onAfterRenderObserver);\r\n                },\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of total lights metrics.\r\n     * @returns the initializer for the total lights strategy\r\n     */\r\n    public static TotalLightsStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            return {\r\n                id: \"Total lights\",\r\n                getData: () => scene.lights.length,\r\n                dispose: defaultDisposeImpl,\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of total vertices metrics.\r\n     * @returns the initializer for the total vertices strategy\r\n     */\r\n    public static TotalVerticesStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            return {\r\n                id: \"Total vertices\",\r\n                getData: () => scene.getTotalVertices(),\r\n                dispose: defaultDisposeImpl,\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of total materials metrics.\r\n     * @returns the initializer for the total materials strategy\r\n     */\r\n    public static TotalMaterialsStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            return {\r\n                id: \"Total materials\",\r\n                getData: () => scene.materials.length,\r\n                dispose: defaultDisposeImpl,\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of total textures metrics.\r\n     * @returns the initializer for the total textures strategy\r\n     */\r\n    public static TotalTexturesStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            return {\r\n                id: \"Total textures\",\r\n                getData: () => scene.textures.length,\r\n                dispose: defaultDisposeImpl,\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of absolute fps metrics.\r\n     * @returns the initializer for the absolute fps strategy\r\n     */\r\n    public static AbsoluteFpsStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            const sceneInstrumentation = new SceneInstrumentation(scene);\r\n            sceneInstrumentation.captureFrameTime = true;\r\n\r\n            return {\r\n                id: \"Absolute FPS\",\r\n                getData: () => {\r\n                    return 1000.0 / sceneInstrumentation.frameTimeCounter.lastSecAverage;\r\n                },\r\n                dispose: defaultDisposeImpl,\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of meshes selection time metrics.\r\n     * @returns the initializer for the meshes selection time strategy\r\n     */\r\n    public static MeshesSelectionStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            let startTime = PrecisionDate.Now;\r\n            let timeTaken = 0;\r\n            const onBeforeActiveMeshesObserver = scene.onBeforeActiveMeshesEvaluationObservable.add(() => {\r\n                startTime = PrecisionDate.Now;\r\n            });\r\n\r\n            const onAfterActiveMeshesObserver = scene.onAfterActiveMeshesEvaluationObservable.add(() => {\r\n                timeTaken = PrecisionDate.Now - startTime;\r\n            });\r\n\r\n            return {\r\n                id: \"Meshes Selection\",\r\n                getData: () => timeTaken,\r\n                dispose: () => {\r\n                    scene.onBeforeActiveMeshesEvaluationObservable.remove(onBeforeActiveMeshesObserver);\r\n                    scene.onAfterActiveMeshesEvaluationObservable.remove(onAfterActiveMeshesObserver);\r\n                },\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of render targets time metrics.\r\n     * @returns the initializer for the render targets time strategy\r\n     */\r\n    public static RenderTargetsStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            let startTime = PrecisionDate.Now;\r\n            let timeTaken = 0;\r\n            const onBeforeRenderTargetsObserver = scene.onBeforeRenderTargetsRenderObservable.add(() => {\r\n                startTime = PrecisionDate.Now;\r\n            });\r\n\r\n            const onAfterRenderTargetsObserver = scene.onAfterRenderTargetsRenderObservable.add(() => {\r\n                timeTaken = PrecisionDate.Now - startTime;\r\n            });\r\n\r\n            return {\r\n                id: \"Render Targets\",\r\n                getData: () => timeTaken,\r\n                dispose: () => {\r\n                    scene.onBeforeRenderTargetsRenderObservable.remove(onBeforeRenderTargetsObserver);\r\n                    scene.onAfterRenderTargetsRenderObservable.remove(onAfterRenderTargetsObserver);\r\n                },\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of particles time metrics.\r\n     * @returns the initializer for the particles time strategy\r\n     */\r\n    public static ParticlesStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            let startTime = PrecisionDate.Now;\r\n            let timeTaken = 0;\r\n            const onBeforeParticlesObserver = scene.onBeforeParticlesRenderingObservable.add(() => {\r\n                startTime = PrecisionDate.Now;\r\n            });\r\n\r\n            const onAfterParticlesObserver = scene.onAfterParticlesRenderingObservable.add(() => {\r\n                timeTaken = PrecisionDate.Now - startTime;\r\n            });\r\n\r\n            return {\r\n                id: \"Particles\",\r\n                getData: () => timeTaken,\r\n                dispose: () => {\r\n                    scene.onBeforeParticlesRenderingObservable.remove(onBeforeParticlesObserver);\r\n                    scene.onAfterParticlesRenderingObservable.remove(onAfterParticlesObserver);\r\n                },\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of sprites time metrics.\r\n     * @returns the initializer for the sprites time strategy\r\n     */\r\n    public static SpritesStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            let startTime = PrecisionDate.Now;\r\n            let timeTaken = 0;\r\n            const onBeforeSpritesObserver = scene.onBeforeSpritesRenderingObservable?.add(() => {\r\n                startTime = PrecisionDate.Now;\r\n            });\r\n\r\n            const onAfterSpritesObserver = scene.onAfterSpritesRenderingObservable?.add(() => {\r\n                timeTaken = PrecisionDate.Now - startTime;\r\n            });\r\n\r\n            return {\r\n                id: \"Sprites\",\r\n                getData: () => timeTaken,\r\n                dispose: () => {\r\n                    scene.onBeforeSpritesRenderingObservable?.remove(onBeforeSpritesObserver);\r\n                    scene.onAfterSpritesRenderingObservable?.remove(onAfterSpritesObserver);\r\n                },\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of animations time metrics.\r\n     * @returns the initializer for the animations time strategy\r\n     */\r\n    public static AnimationsStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            let startTime = PrecisionDate.Now;\r\n            let timeTaken = 0;\r\n            const onBeforeAnimationsObserver = scene.onBeforeAnimationsObservable.add(() => {\r\n                startTime = PrecisionDate.Now;\r\n            });\r\n\r\n            const onAfterAnimationsObserver = scene.onAfterAnimationsObservable.add(() => {\r\n                timeTaken = PrecisionDate.Now - startTime;\r\n            });\r\n\r\n            return {\r\n                id: \"Animations\",\r\n                getData: () => timeTaken,\r\n                dispose: () => {\r\n                    scene.onBeforeAnimationsObservable.remove(onBeforeAnimationsObserver);\r\n                    scene.onAfterAnimationsObservable.remove(onAfterAnimationsObserver);\r\n                },\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of physics time metrics.\r\n     * @returns the initializer for the physics time strategy\r\n     */\r\n    public static PhysicsStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            let startTime = PrecisionDate.Now;\r\n            let timeTaken = 0;\r\n            const onBeforePhysicsObserver = scene.onBeforePhysicsObservable?.add(() => {\r\n                startTime = PrecisionDate.Now;\r\n            });\r\n\r\n            const onAfterPhysicsObserver = scene.onAfterPhysicsObservable?.add(() => {\r\n                timeTaken = PrecisionDate.Now - startTime;\r\n            });\r\n\r\n            return {\r\n                id: \"Physics\",\r\n                getData: () => timeTaken,\r\n                dispose: () => {\r\n                    scene.onBeforePhysicsObservable?.remove(onBeforePhysicsObserver);\r\n                    scene.onAfterPhysicsObservable?.remove(onAfterPhysicsObserver);\r\n                },\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of render time metrics.\r\n     * @returns the initializer for the render time strategy\r\n     */\r\n    public static RenderStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            let startTime = PrecisionDate.Now;\r\n            let timeTaken = 0;\r\n            const onBeforeDrawPhaseObserver = scene.onBeforeDrawPhaseObservable.add(() => {\r\n                startTime = PrecisionDate.Now;\r\n            });\r\n\r\n            const onAfterDrawPhaseObserver = scene.onAfterDrawPhaseObservable.add(() => {\r\n                timeTaken = PrecisionDate.Now - startTime;\r\n            });\r\n\r\n            return {\r\n                id: \"Render\",\r\n                getData: () => timeTaken,\r\n                dispose: () => {\r\n                    scene.onBeforeDrawPhaseObservable.remove(onBeforeDrawPhaseObserver);\r\n                    scene.onAfterDrawPhaseObservable.remove(onAfterDrawPhaseObserver);\r\n                },\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of total frame time metrics.\r\n     * @returns the initializer for the total frame time strategy\r\n     */\r\n    public static FrameTotalStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            let startTime = PrecisionDate.Now;\r\n            let timeTaken = 0;\r\n            const onBeforeAnimationsObserver = scene.onBeforeAnimationsObservable.add(() => {\r\n                startTime = PrecisionDate.Now;\r\n            });\r\n\r\n            const onAfterRenderObserver = scene.onAfterRenderObservable.add(() => {\r\n                timeTaken = PrecisionDate.Now - startTime;\r\n            });\r\n\r\n            return {\r\n                id: \"Frame Total\",\r\n                getData: () => timeTaken,\r\n                dispose: () => {\r\n                    scene.onBeforeAnimationsObservable.remove(onBeforeAnimationsObserver);\r\n                    scene.onAfterRenderObservable.remove(onAfterRenderObserver);\r\n                },\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of inter-frame time metrics.\r\n     * @returns the initializer for the inter-frame time strategy\r\n     */\r\n    public static InterFrameStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            let startTime = PrecisionDate.Now;\r\n            let timeTaken = 0;\r\n\r\n            const onBeforeAnimationsObserver = scene.onBeforeAnimationsObservable.add(() => {\r\n                timeTaken = PrecisionDate.Now - startTime;\r\n            });\r\n\r\n            const onAfterRenderObserver = scene.onAfterRenderObservable.add(() => {\r\n                startTime = PrecisionDate.Now;\r\n            });\r\n\r\n            return {\r\n                id: \"Inter-frame\",\r\n                getData: () => timeTaken,\r\n                dispose: () => {\r\n                    scene.onBeforeAnimationsObservable.remove(onBeforeAnimationsObserver);\r\n                    scene.onAfterRenderObservable.remove(onAfterRenderObserver);\r\n                },\r\n            };\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the initializer for the strategy used for collection of gpu frame time metrics.\r\n     * @returns the initializer for the gpu frame time strategy\r\n     */\r\n    public static GpuFrameTimeStrategy(): PerfStrategyInitialization {\r\n        return (scene) => {\r\n            const engineInstrumentation = new EngineInstrumentation(scene.getEngine());\r\n            engineInstrumentation.captureGPUFrameTime = true;\r\n            return {\r\n                id: \"GPU frame time\",\r\n                getData: () => Math.max(engineInstrumentation.gpuFrameTimeCounter.current * 0.000001, 0),\r\n                dispose: () => {\r\n                    engineInstrumentation.dispose();\r\n                },\r\n            };\r\n        };\r\n    }\r\n}\r\n"]}