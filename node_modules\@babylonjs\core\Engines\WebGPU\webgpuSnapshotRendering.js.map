{"version": 3, "file": "webgpuSnapshotRendering.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuSnapshotRendering.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAIzC,gBAAgB;AAChB,MAAM,OAAO,uBAAuB;IAahC,YAAY,MAAoB,EAAE,aAAqB,EAAE,UAA4B;QAV7E,YAAO,GAAG,KAAK,CAAC;QAChB,UAAK,GAAG,KAAK,CAAC;QACd,yBAAoB,GAAG,CAAC,CAAC;QACzB,oBAAe,GAAuB,EAAE,CAAC;QAIzC,aAAQ,GAAG,KAAK,CAAC;QAIrB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAClC,CAAC;IAED,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,OAAO,CAAC,QAAiB;QAChC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACxC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;YAC7B,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,0BAA0B,CAAC,CAAC,qFAAqF;SAC3I;IACL,CAAC;IAED,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,IAAY;QACxB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SAC1B;aAAM;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACrB;IACL,CAAC;IAEM,aAAa,CAAC,iBAAuC;QACxD,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YAC9B,yCAAyC;YACzC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,UAA4B,CAAC;QAEjC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YACtC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;SAC5B;aAAM;YACH,8BAA8B;YAC9B,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;gBAC1D,MAAM,IAAI,KAAK,CACX,2IAA2I,IAAI,CAAC,oBAAoB,2BAA2B,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAChO,CAAC;aACL;YACD,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;SAClE;QAED,UAAU,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAElC,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,CAAC,sBAAsB,EAAE;YACjD,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;SACzD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,QAAQ;QACX,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,kEAAkE;YAClE,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;SAChC;QAED,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;IAClC,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;CACJ", "sourcesContent": ["import { Constants } from \"../constants\";\r\nimport type { WebGPUEngine } from \"../webgpuEngine\";\r\nimport type { WebGPUBundleList } from \"./webgpuBundleList\";\r\n\r\n/** @internal */\r\nexport class WebGPUSnapshotRendering {\r\n    private _engine: WebGPUEngine;\r\n\r\n    private _record = false;\r\n    private _play = false;\r\n    private _playBundleListIndex = 0;\r\n    private _allBundleLists: WebGPUBundleList[] = [];\r\n    private _modeSaved: number;\r\n    private _bundleList: WebGPUBundleList;\r\n\r\n    private _enabled = false;\r\n    private _mode: number;\r\n\r\n    constructor(engine: WebGPUEngine, renderingMode: number, bundleList: WebGPUBundleList) {\r\n        this._engine = engine;\r\n        this._mode = renderingMode;\r\n        this._bundleList = bundleList;\r\n    }\r\n\r\n    public get enabled(): boolean {\r\n        return this._enabled;\r\n    }\r\n\r\n    public get play() {\r\n        return this._play;\r\n    }\r\n\r\n    public get record() {\r\n        return this._record;\r\n    }\r\n\r\n    public set enabled(activate: boolean) {\r\n        this._allBundleLists.length = 0;\r\n        this._record = this._enabled = activate;\r\n        this._play = false;\r\n        if (activate) {\r\n            this._modeSaved = this._mode;\r\n            this._mode = Constants.SNAPSHOTRENDERING_STANDARD; // need to reset to standard for the recording pass to avoid some code being bypassed\r\n        }\r\n    }\r\n\r\n    public get mode(): number {\r\n        return this._mode;\r\n    }\r\n\r\n    public set mode(mode: number) {\r\n        if (this._record) {\r\n            this._modeSaved = mode;\r\n        } else {\r\n            this._mode = mode;\r\n        }\r\n    }\r\n\r\n    public endRenderPass(currentRenderPass: GPURenderPassEncoder): boolean {\r\n        if (!this._record && !this._play) {\r\n            // Snapshot rendering mode is not enabled\r\n            return false;\r\n        }\r\n\r\n        let bundleList: WebGPUBundleList;\r\n\r\n        if (this._record) {\r\n            bundleList = this._bundleList.clone();\r\n            this._allBundleLists.push(bundleList);\r\n            this._bundleList.reset();\r\n        } else {\r\n            // We are playing the snapshot\r\n            if (this._playBundleListIndex >= this._allBundleLists.length) {\r\n                throw new Error(\r\n                    `Invalid playBundleListIndex! Your snapshot is no longer valid for the current frame, you should recreate a new one. playBundleListIndex=${this._playBundleListIndex}, allBundleLists.length=${this._allBundleLists.length}}`\r\n                );\r\n            }\r\n            bundleList = this._allBundleLists[this._playBundleListIndex++];\r\n        }\r\n\r\n        bundleList.run(currentRenderPass);\r\n\r\n        if (this._mode === Constants.SNAPSHOTRENDERING_FAST) {\r\n            this._engine._reportDrawCall(bundleList.numDrawCalls);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public endFrame(): void {\r\n        if (this._record) {\r\n            // We stop recording and switch to replay mode for the next frames\r\n            this._record = false;\r\n            this._play = true;\r\n            this._mode = this._modeSaved;\r\n        }\r\n\r\n        this._playBundleListIndex = 0;\r\n    }\r\n\r\n    public reset(): void {\r\n        this.enabled = false;\r\n        this.enabled = true;\r\n    }\r\n}\r\n"]}