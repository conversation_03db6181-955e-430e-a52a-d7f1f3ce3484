{"version": 3, "file": "nativeXRFrame.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/native/nativeXRFrame.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,uBAAuB,EAAE,MAAM,4BAA4B,CAAC;AASrE,gBAAgB;AAChB,MAAM,OAAO,aAAa;IAStB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;IACpC,CAAC;IAED,YAAoB,WAA2B;QAA3B,gBAAW,GAAX,WAAW,CAAgB;QAZ9B,iBAAY,GAAG,IAAI,gBAAgB,EAAE,CAAC;QACtC,YAAO,GAAW;YAC/B,SAAS,EAAE,IAAI,CAAC,YAAY;YAC5B,gBAAgB,EAAE,KAAK;SAC1B,CAAC;QACF,yCAAyC;QACxB,sBAAiB,GAAG,IAAI,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QA0B7C,cAAS,GAAG,IAAI,CAAC,WAAW,CAAC,SAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE/D,kBAAa,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEtE,sBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE9E,uCAAkC,GAAG,GAAG,EAAE;YACtD,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;QAC3F,CAAC,CAAC;QAMc,iBAAY,GAAG,IAAI,CAAC,WAAW,CAAC,YAAa,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAUrE,iBAAY,GAAG,IAAI,CAAC,WAAW,CAAC,YAAa,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAErE,mBAAc,GAAG,IAAI,CAAC,WAAW,CAAC,cAAe,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEzE,qBAAgB,GAAG,GAAG,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACzE,CAAC,CAAC;QAMc,4BAAuB,GAAG,GAA4B,EAAE;YACpE,OAAO,IAAI,CAAC,WAAW,CAAC,qBAAqB,IAAI,EAAE,CAAC;QACxD,CAAC,CAAC;IA1DgD,CAAC;IAE5C,OAAO,CAAC,KAAc,EAAE,SAA2B;QACtD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YACjH,OAAO,SAAS,CAAC;SACpB;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAoB,CAAC;QACxD,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACvC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACvC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACvC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAEvC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,WAAuB,CAAC;QAC9D,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC1C,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC1C,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC1C,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAYD,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;IAC3C,CAAC;IAID,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC;IAC7C,CAAC;IAED,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;IAC3C,CAAC;IAUD,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC;IAC9C,CAAC;IAMM,mBAAmB,CAAC,IAAY;QACnC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACpE,qDAAqD;IACzD,CAAC;CACJ;AAED,uBAAuB,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC", "sourcesContent": ["import { RegisterNativeTypeAsync } from \"../../Engines/nativeEngine\";\r\n\r\n/** @internal */\r\ninterface INativeXRFrame extends XRFrame {\r\n    // Native-only helper functions\r\n    getPoseData: (space: XRSpace, baseSpace: XRReferenceSpace, vectorBuffer: <PERSON><PERSON><PERSON><PERSON>uffer, matrixBuffer: <PERSON><PERSON>yBuffer) => XRPose;\r\n    _imageTrackingResults?: XRImageTrackingResult[];\r\n}\r\n\r\n/** @internal */\r\nexport class NativeXRFrame implements XRFrame {\r\n    private readonly _xrTransform = new XRRigidTransform();\r\n    private readonly _xrPose: XRPose = {\r\n        transform: this._xrTransform,\r\n        emulatedPosition: false,\r\n    };\r\n    // Enough space for position, orientation\r\n    private readonly _xrPoseVectorData = new Float32Array(4 + 4);\r\n\r\n    public get session(): XRSession {\r\n        return this._nativeImpl.session;\r\n    }\r\n\r\n    constructor(private _nativeImpl: INativeXRFrame) {}\r\n\r\n    public getPose(space: XRSpace, baseSpace: XRReferenceSpace): XRPose | undefined {\r\n        if (!this._nativeImpl.getPoseData(space, baseSpace, this._xrPoseVectorData.buffer, this._xrTransform.matrix.buffer)) {\r\n            return undefined;\r\n        }\r\n        const position = this._xrTransform.position as DOMPoint;\r\n        position.x = this._xrPoseVectorData[0];\r\n        position.y = this._xrPoseVectorData[1];\r\n        position.z = this._xrPoseVectorData[2];\r\n        position.w = this._xrPoseVectorData[3];\r\n\r\n        const orientation = this._xrTransform.orientation as DOMPoint;\r\n        orientation.x = this._xrPoseVectorData[4];\r\n        orientation.y = this._xrPoseVectorData[5];\r\n        orientation.z = this._xrPoseVectorData[6];\r\n        orientation.w = this._xrPoseVectorData[7];\r\n        return this._xrPose;\r\n    }\r\n\r\n    public readonly fillPoses = this._nativeImpl.fillPoses!.bind(this._nativeImpl);\r\n\r\n    public readonly getViewerPose = this._nativeImpl.getViewerPose.bind(this._nativeImpl);\r\n\r\n    public readonly getHitTestResults = this._nativeImpl.getHitTestResults.bind(this._nativeImpl);\r\n\r\n    public readonly getHitTestResultsForTransientInput = () => {\r\n        throw new Error(\"XRFrame.getHitTestResultsForTransientInput not supported on native.\");\r\n    };\r\n\r\n    public get trackedAnchors(): XRAnchorSet | undefined {\r\n        return this._nativeImpl.trackedAnchors;\r\n    }\r\n\r\n    public readonly createAnchor = this._nativeImpl.createAnchor!.bind(this._nativeImpl);\r\n\r\n    public get worldInformation(): XRWorldInformation | undefined {\r\n        return this._nativeImpl.worldInformation;\r\n    }\r\n\r\n    public get detectedPlanes(): XRPlaneSet | undefined {\r\n        return this._nativeImpl.detectedPlanes;\r\n    }\r\n\r\n    public readonly getJointPose = this._nativeImpl.getJointPose!.bind(this._nativeImpl);\r\n\r\n    public readonly fillJointRadii = this._nativeImpl.fillJointRadii!.bind(this._nativeImpl);\r\n\r\n    public readonly getLightEstimate = () => {\r\n        throw new Error(\"XRFrame.getLightEstimate not supported on native.\");\r\n    };\r\n\r\n    public get featurePointCloud(): number[] | undefined {\r\n        return this._nativeImpl.featurePointCloud;\r\n    }\r\n\r\n    public readonly getImageTrackingResults = (): XRImageTrackingResult[] => {\r\n        return this._nativeImpl._imageTrackingResults ?? [];\r\n    };\r\n\r\n    public getDepthInformation(view: XRView): XRCPUDepthInformation | undefined {\r\n        throw new Error(\"This function is not available in Babylon Native\");\r\n        // return this._nativeImpl.getDepthInformation(view);\r\n    }\r\n}\r\n\r\nRegisterNativeTypeAsync(\"NativeXRFrame\", NativeXRFrame);\r\n"]}