{"version": 3, "file": "physicsJoint.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Physics/v1/physicsJoint.ts"], "names": [], "mappings": "AAkCA;;;;GAIG;AACH,MAAM,OAAO,YAAY;IAIrB;;;;OAIG;IACH;IACI;;OAEG;IACI,IAAY;IACnB;;OAEG;IACI,SAA2B;QAJ3B,SAAI,GAAJ,IAAI,CAAQ;QAIZ,cAAS,GAAT,SAAS,CAAkB;QAElC,SAAS,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,IAAI,EAAE,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY,CAAC,QAAa;QACjC,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,uBAAuB;SAC1B;QAED,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,aAAa,CAAC,aAAmC;QACxD,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,IAA6C;QACtE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACxD,CAAC;;AAED,8CAA8C;AAE9C,aAAa;AACb;;GAEG;AACW,0BAAa,GAAG,CAAC,CAAC;AAChC;;GAEG;AACW,uBAAU,GAAG,CAAC,CAAC;AAC7B;;GAEG;AACW,+BAAkB,GAAG,CAAC,CAAC;AACrC;;GAEG;AACW,uBAAU,GAAG,CAAC,CAAC;AAC7B;;GAEG;AACW,wBAAW,GAAG,CAAC,CAAC;AAC9B,MAAM;AACN;;GAEG;AACW,2BAAc,GAAG,CAAC,CAAC;AACjC,EAAE;AACF;;;GAGG;AACW,2BAAc,GAAG,CAAC,CAAC;AACjC;;GAEG;AACW,wBAAW,GAAG,YAAY,CAAC,UAAU,CAAC;AACpD,QAAQ;AACR;;GAEG;AACW,8BAAiB,GAAG,CAAC,CAAC;AACpC,2BAA2B;AAC3B;;GAEG;AACW,wBAAW,GAAG,CAAC,CAAC;AAC9B;;GAEG;AACW,sBAAS,GAAG,EAAE,CAAC;AAGjC;;;GAGG;AACH,MAAM,OAAO,aAAc,SAAQ,YAAY;IAC3C;;;OAGG;IACH,YAAY,SAA4B;QACpC,KAAK,CAAC,YAAY,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;IACjD,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,WAAmB,EAAE,WAAoB;QAC3D,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,IAAI,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;IAC5E,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,iBAAkB,SAAQ,YAAY;IAC/C;;;;OAIG;IACH,YAAY,IAAY,EAAE,SAA2B;QACjD,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC3B,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,KAAc,EAAE,QAAiB;QAC7C,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,UAAkB,EAAE,UAAmB;QACnD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IAC/D,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,UAAW,SAAQ,iBAAiB;IAC7C;;;OAGG;IACH,YAAY,SAA2B;QACnC,KAAK,CAAC,YAAY,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,KAAc,EAAE,QAAiB;QAC7C,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,UAAkB,EAAE,UAAmB;QACnD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IAC/D,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,WAAY,SAAQ,iBAAiB;IAC9C;;;OAGG;IACH,YAAY,SAA2B;QACnC,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;OAMG;IACI,QAAQ,CAAC,WAAoB,EAAE,QAAiB,EAAE,aAAqB,CAAC;QAC3E,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,IAAI,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC/E,CAAC;IAED;;;;;;OAMG;IACI,QAAQ,CAAC,UAAkB,EAAE,UAAmB,EAAE,aAAqB,CAAC;QAC3E,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IAC3E,CAAC;CACJ", "sourcesContent": ["import type { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { IPhysicsEnginePlugin } from \"./IPhysicsEnginePlugin\";\r\n/**\r\n * Interface for Physics-Joint data\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport interface PhysicsJointData {\r\n    //Important for some engines, optional!\r\n    /**\r\n     * The main pivot of the joint\r\n     */\r\n    mainPivot?: Vector3;\r\n    /**\r\n     * The connected pivot of the joint\r\n     */\r\n    connectedPivot?: Vector3;\r\n    /**\r\n     * The main axis of the joint\r\n     */\r\n    mainAxis?: Vector3;\r\n    /**\r\n     * The connected axis of the joint\r\n     */\r\n    connectedAxis?: Vector3;\r\n    /**\r\n     * The collision of the joint\r\n     */\r\n    collision?: boolean;\r\n    /**\r\n     * Native Oimo/Cannon/Energy data\r\n     */\r\n    nativeParams?: any;\r\n}\r\n\r\n/**\r\n * This is a holder class for the physics joint created by the physics plugin\r\n * It holds a set of functions to control the underlying joint\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport class PhysicsJoint {\r\n    private _physicsJoint: any;\r\n    protected _physicsPlugin: IPhysicsEnginePlugin;\r\n\r\n    /**\r\n     * Initializes the physics joint\r\n     * @param type The type of the physics joint\r\n     * @param jointData The data for the physics joint\r\n     */\r\n    constructor(\r\n        /**\r\n         * The type of the physics joint\r\n         */\r\n        public type: number,\r\n        /**\r\n         * The data for the physics joint\r\n         */\r\n        public jointData: PhysicsJointData\r\n    ) {\r\n        jointData.nativeParams = jointData.nativeParams || {};\r\n    }\r\n\r\n    /**\r\n     * Gets the physics joint\r\n     */\r\n    public get physicsJoint(): any {\r\n        return this._physicsJoint;\r\n    }\r\n\r\n    /**\r\n     * Sets the physics joint\r\n     */\r\n    public set physicsJoint(newJoint: any) {\r\n        if (this._physicsJoint) {\r\n            //remove from the world\r\n        }\r\n\r\n        this._physicsJoint = newJoint;\r\n    }\r\n\r\n    /**\r\n     * Sets the physics plugin\r\n     */\r\n    public set physicsPlugin(physicsPlugin: IPhysicsEnginePlugin) {\r\n        this._physicsPlugin = physicsPlugin;\r\n    }\r\n\r\n    /**\r\n     * Execute a function that is physics-plugin specific.\r\n     * @param {Function} func the function that will be executed.\r\n     *                        It accepts two parameters: the physics world and the physics joint\r\n     */\r\n    public executeNativeFunction(func: (world: any, physicsJoint: any) => void) {\r\n        func(this._physicsPlugin.world, this._physicsJoint);\r\n    }\r\n\r\n    //TODO check if the native joints are the same\r\n\r\n    //Joint Types\r\n    /**\r\n     * Distance-Joint type\r\n     */\r\n    public static DistanceJoint = 0;\r\n    /**\r\n     * Hinge-Joint type\r\n     */\r\n    public static HingeJoint = 1;\r\n    /**\r\n     * Ball-and-Socket joint type\r\n     */\r\n    public static BallAndSocketJoint = 2;\r\n    /**\r\n     * Wheel-Joint type\r\n     */\r\n    public static WheelJoint = 3;\r\n    /**\r\n     * Slider-Joint type\r\n     */\r\n    public static SliderJoint = 4;\r\n    //OIMO\r\n    /**\r\n     * Prismatic-Joint type\r\n     */\r\n    public static PrismaticJoint = 5;\r\n    //\r\n    /**\r\n     * Universal-Joint type\r\n     * ENERGY FTW! (compare with this - @see http://ode-wiki.org/wiki/index.php?title=Manual:_Joint_Types_and_Functions)\r\n     */\r\n    public static UniversalJoint = 6;\r\n    /**\r\n     * Hinge-Joint 2 type\r\n     */\r\n    public static Hinge2Joint = PhysicsJoint.WheelJoint;\r\n    //Cannon\r\n    /**\r\n     * Point to Point Joint type.  Similar to a Ball-Joint.  Different in parameters\r\n     */\r\n    public static PointToPointJoint = 8;\r\n    //Cannon only at the moment\r\n    /**\r\n     * Spring-Joint type\r\n     */\r\n    public static SpringJoint = 9;\r\n    /**\r\n     * Lock-Joint type\r\n     */\r\n    public static LockJoint = 10;\r\n}\r\n\r\n/**\r\n * A class representing a physics distance joint\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport class DistanceJoint extends PhysicsJoint {\r\n    /**\r\n     *\r\n     * @param jointData The data for the Distance-Joint\r\n     */\r\n    constructor(jointData: DistanceJointData) {\r\n        super(PhysicsJoint.DistanceJoint, jointData);\r\n    }\r\n\r\n    /**\r\n     * Update the predefined distance.\r\n     * @param maxDistance The maximum preferred distance\r\n     * @param minDistance The minimum preferred distance\r\n     */\r\n    public updateDistance(maxDistance: number, minDistance?: number) {\r\n        this._physicsPlugin.updateDistanceJoint(this, maxDistance, minDistance);\r\n    }\r\n}\r\n\r\n/**\r\n * Represents a Motor-Enabled Joint\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport class MotorEnabledJoint extends PhysicsJoint implements IMotorEnabledJoint {\r\n    /**\r\n     * Initializes the Motor-Enabled Joint\r\n     * @param type The type of the joint\r\n     * @param jointData The physical joint data for the joint\r\n     */\r\n    constructor(type: number, jointData: PhysicsJointData) {\r\n        super(type, jointData);\r\n    }\r\n\r\n    /**\r\n     * Set the motor values.\r\n     * Attention, this function is plugin specific. Engines won't react 100% the same.\r\n     * @param force the force to apply\r\n     * @param maxForce max force for this motor.\r\n     */\r\n    public setMotor(force?: number, maxForce?: number) {\r\n        this._physicsPlugin.setMotor(this, force || 0, maxForce);\r\n    }\r\n\r\n    /**\r\n     * Set the motor's limits.\r\n     * Attention, this function is plugin specific. Engines won't react 100% the same.\r\n     * @param upperLimit The upper limit of the motor\r\n     * @param lowerLimit The lower limit of the motor\r\n     */\r\n    public setLimit(upperLimit: number, lowerLimit?: number) {\r\n        this._physicsPlugin.setLimit(this, upperLimit, lowerLimit);\r\n    }\r\n}\r\n\r\n/**\r\n * This class represents a single physics Hinge-Joint\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport class HingeJoint extends MotorEnabledJoint {\r\n    /**\r\n     * Initializes the Hinge-Joint\r\n     * @param jointData The joint data for the Hinge-Joint\r\n     */\r\n    constructor(jointData: PhysicsJointData) {\r\n        super(PhysicsJoint.HingeJoint, jointData);\r\n    }\r\n\r\n    /**\r\n     * Set the motor values.\r\n     * Attention, this function is plugin specific. Engines won't react 100% the same.\r\n     * @param {number} force the force to apply\r\n     * @param {number} maxForce max force for this motor.\r\n     */\r\n    public setMotor(force?: number, maxForce?: number) {\r\n        this._physicsPlugin.setMotor(this, force || 0, maxForce);\r\n    }\r\n\r\n    /**\r\n     * Set the motor's limits.\r\n     * Attention, this function is plugin specific. Engines won't react 100% the same.\r\n     * @param upperLimit The upper limit of the motor\r\n     * @param lowerLimit The lower limit of the motor\r\n     */\r\n    public setLimit(upperLimit: number, lowerLimit?: number) {\r\n        this._physicsPlugin.setLimit(this, upperLimit, lowerLimit);\r\n    }\r\n}\r\n\r\n/**\r\n * This class represents a dual hinge physics joint (same as wheel joint)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport class Hinge2Joint extends MotorEnabledJoint {\r\n    /**\r\n     * Initializes the Hinge2-Joint\r\n     * @param jointData The joint data for the Hinge2-Joint\r\n     */\r\n    constructor(jointData: PhysicsJointData) {\r\n        super(PhysicsJoint.Hinge2Joint, jointData);\r\n    }\r\n\r\n    /**\r\n     * Set the motor values.\r\n     * Attention, this function is plugin specific. Engines won't react 100% the same.\r\n     * @param targetSpeed the speed the motor is to reach\r\n     * @param maxForce max force for this motor.\r\n     * @param motorIndex motor's index, 0 or 1.\r\n     */\r\n    public setMotor(targetSpeed?: number, maxForce?: number, motorIndex: number = 0) {\r\n        this._physicsPlugin.setMotor(this, targetSpeed || 0, maxForce, motorIndex);\r\n    }\r\n\r\n    /**\r\n     * Set the motor limits.\r\n     * Attention, this function is plugin specific. Engines won't react 100% the same.\r\n     * @param upperLimit the upper limit\r\n     * @param lowerLimit lower limit\r\n     * @param motorIndex the motor's index, 0 or 1.\r\n     */\r\n    public setLimit(upperLimit: number, lowerLimit?: number, motorIndex: number = 0) {\r\n        this._physicsPlugin.setLimit(this, upperLimit, lowerLimit, motorIndex);\r\n    }\r\n}\r\n\r\n/**\r\n * Interface for a motor enabled joint\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport interface IMotorEnabledJoint {\r\n    /**\r\n     * Physics joint\r\n     */\r\n    physicsJoint: any;\r\n    /**\r\n     * Sets the motor of the motor-enabled joint\r\n     * @param force The force of the motor\r\n     * @param maxForce The maximum force of the motor\r\n     * @param motorIndex The index of the motor\r\n     */\r\n    setMotor(force?: number, maxForce?: number, motorIndex?: number): void;\r\n    /**\r\n     * Sets the limit of the motor\r\n     * @param upperLimit The upper limit of the motor\r\n     * @param lowerLimit The lower limit of the motor\r\n     * @param motorIndex The index of the motor\r\n     */\r\n    setLimit(upperLimit: number, lowerLimit?: number, motorIndex?: number): void;\r\n}\r\n\r\n/**\r\n * Joint data for a Distance-Joint\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport interface DistanceJointData extends PhysicsJointData {\r\n    /**\r\n     * Max distance the 2 joint objects can be apart\r\n     */\r\n    maxDistance: number;\r\n    //Oimo - minDistance\r\n    //Cannon - maxForce\r\n}\r\n\r\n/**\r\n * Joint data from a spring joint\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine\r\n */\r\nexport interface SpringJointData extends PhysicsJointData {\r\n    /**\r\n     * Length of the spring\r\n     */\r\n    length: number;\r\n    /**\r\n     * Stiffness of the spring\r\n     */\r\n    stiffness: number;\r\n    /**\r\n     * Damping of the spring\r\n     */\r\n    damping: number;\r\n    /** this callback will be called when applying the force to the impostors. */\r\n    forceApplicationCallback: () => void;\r\n}\r\n"]}