{"version": 3, "file": "uniformBuffer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Materials/uniformBuffer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAQxC,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,4CAA4C,CAAC;AAEpD;;;;;;;;;GASG;AACH,MAAM,OAAO,aAAa;IA4MtB;;;;;;;;;;;;;;OAcG;IACH,YAAY,MAAkB,EAAE,IAAe,EAAE,OAAiB,EAAE,IAAa,EAAE,oBAAoB,GAAG,KAAK;QA2jB/G,eAAe;QACP,gBAAW,GAA8B,EAAE,CAAC;QA3jBhD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,sBAAsB,IAAI,oBAAoB,CAAC;QACrE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,SAAS,CAAC;QAE/B,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QAExB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,gBAAgB,EAAE;YACzC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;YACnB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YACvB,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;YAClC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC;YACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC;YACtD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC9C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAChD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAChD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAChD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,0BAA0B,CAAC;YACxD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC9C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC;YACpD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC;YACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAChD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC;YACpD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC;YAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC;YAClD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAChD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAChD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,4BAA4B,CAAC;YAC5D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAC1C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAC5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAC5C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC;SACjD;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAExC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC;YACvD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC;YACvD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAC/C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC;YACjD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC;YACjD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC;YACjD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,2BAA2B,CAAC;YACzD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAC/C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC;YACrD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC;YACvD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC;YACjD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC;YACrD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC;YACnD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC;YACnD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC;YACjD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC;YACjD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,6BAA6B,CAAC;YAC7D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAC3C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC7C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC;SAClD;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,MAAM;QACb,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,IAAW,MAAM;QACb,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;IAC3B,CAAC;IAED;;;;;OAKG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACK,cAAc,CAAC,IAAY;QAC/B,6FAA6F;QAC7F,mBAAmB;QACnB,wCAAwC;QAExC,IAAI,SAAS,CAAC;QACd,IAAI,IAAI,IAAI,CAAC,EAAE;YACX,SAAS,GAAG,IAAI,CAAC;SACpB;aAAM;YACH,SAAS,GAAG,CAAC,CAAC;SACjB;QAED,IAAI,IAAI,CAAC,uBAAuB,GAAG,SAAS,KAAK,CAAC,EAAE;YAChD,MAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC;YAChD,IAAI,CAAC,uBAAuB,IAAI,SAAS,GAAG,CAAC,IAAI,CAAC,uBAAuB,GAAG,SAAS,CAAC,CAAC;YACvF,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,GAAG,UAAU,CAAC;YAEvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACtB;SACJ;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,UAAU,CAAC,IAAY,EAAE,IAAuB,EAAE,SAAS,GAAG,CAAC;QAClE,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO;SACV;QAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;YAC5C,2BAA2B;YAC3B,OAAO;SACV;QACD,mEAAmE;QACnE,wDAAwD;QACxD,IAAI,IAAI,CAAC;QAET,gBAAgB;QAChB,IAAI,SAAS,GAAG,CAAC,EAAE;YACf,IAAI,IAAI,YAAY,KAAK,EAAE;gBACvB,4CAA4C;gBAC5C,MAAM,kDAAkD,GAAG,IAAI,CAAC;aACnE;YAED,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YAEvB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;YAChE,IAAI,IAAI,IAAI,EAAE,EAAE;gBACZ,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC;aAC3B;iBAAM;gBACH,MAAM,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAAC;gBACnC,MAAM,YAAY,GAAG,iBAAiB,GAAG,SAAS,CAAC;gBACnD,IAAI,GAAG,IAAI,GAAG,SAAS,GAAG,YAAY,CAAC;aAC1C;YAED,IAAI,GAAG,EAAE,CAAC;YACV,kBAAkB;YAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aAChB;SACJ;aAAM;YACH,IAAI,IAAI,YAAY,KAAK,EAAE;gBACvB,IAAI,GAAG,IAAI,CAAC;gBACZ,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;aACtB;iBAAM;gBACH,IAAI,GAAW,IAAI,CAAC;gBACpB,IAAI,GAAG,EAAE,CAAC;gBAEV,kBAAkB;gBAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;oBAC3B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBAChB;aACJ;YACD,IAAI,CAAC,cAAc,CAAS,IAAI,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAW,IAAI,CAAC;QACxC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAC5D,IAAI,CAAC,uBAAuB,IAAY,IAAI,CAAC;QAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5B;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,IAAY,EAAE,GAAgB;QAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IACrE,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS;QAC/C,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC1D,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,IAAY,EAAE,KAAkB;QAC7C,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY,EAAE,KAAkB,EAAE,KAAa;QAC5D,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,IAAY,EAAE,MAAoB;QAChD,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,IAAY;QAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,IAAY;QAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO;SACV;QACD,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,OAAO,CAAC,gBAAgB;SAC3B;QAED,+CAA+C;QAC/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEhD,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,sFAAsF;IACtF,2EAA2E;IAC3E,4GAA4G;IAC5G,0DAA0D;IAClD,SAAS;QACb,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACvC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjB,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;gBACZ,MAAM;aACT;SACJ;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YAClC,OAAO;SACV;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,GAAG,eAAe,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;SAC7H;aAAM;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,GAAG,eAAe,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;SACtH;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,gBAAgB,EAAE;YACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YAC/H,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7C,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;SACrC;IACL,CAAC;IAED,gBAAgB;IACT,wBAAwB;QAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,gBAAgB,EAAE;YACzC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;YACnB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;SAC5B;QACD,IAAI,CAAC,QAAQ,EAAE,CAAC;IACpB,CAAC;IAED,gBAAgB;IAChB,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IAChC,CAAC;IAED,gBAAgB;IAChB,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,mCAAmC;IACnC,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,8BAA8B;IAC9B,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAEO,aAAa,CAAC,IAAkB,EAAE,IAAkB;QACxD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAClC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;gBACrB,OAAO,KAAK,CAAC;aAChB;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,WAAW,CAAC,GAAiB,EAAE,GAAiB;QACpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACjC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;SACnB;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM;QACT,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO;SACV;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC;YACpE,OAAO;SACV;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;YAClF,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAE,CAAC,EAAE;gBAC5E,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC;gBACpE,OAAO;aACV;iBAAM;gBACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;aAC5E;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAEjE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,0BAA0B,EAAE;YACnD,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAChD,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACrD;YACD,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;SACnD;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC;IACxE,CAAC;IAEO,gBAAgB;QACpB,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YAC9C,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;YAClC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACzB;aAAM;YACH,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB;IACL,CAAC;IAEO,cAAc;QAClB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,gBAAgB,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YAC1F,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;YAC5C,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;YAClC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC;gBACzC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;gBACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;aACtD;iBAAM;gBACH,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;aAC1B;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,WAAmB,EAAE,IAAgB,EAAE,IAAY;QACpE,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACnD,IAAI,QAAQ,KAAK,SAAS,EAAE;YACxB,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,yDAAyD;gBACzD,MAAM,CAAC,KAAK,CAAC,gEAAgE,GAAG,WAAW,CAAC,CAAC;gBAC7F,OAAO;aACV;YACD,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACnC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,CAAC,MAAM,EAAE,CAAC;SACjB;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,mCAAmC;YACnC,IAAI,OAAO,GAAG,KAAK,CAAC;YAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC3B,mGAAmG;gBACnG,oEAAoE;gBACpE,IAAI,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,4BAA4B,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;oBAClI,OAAO,GAAG,IAAI,CAAC;oBACf,IAAI,IAAI,CAAC,oBAAoB,EAAE;wBAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;qBAC3B;oBACD,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;iBAC5C;aACJ;YAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC;SAC9C;aAAM;YACH,uBAAuB;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;aAC5C;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,WAAmB,EAAE,IAAgB,EAAE,IAAY;QACzE,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACrD,IAAI,QAAQ,KAAK,SAAS,EAAE;YACxB,MAAM,CAAC,KAAK,CAAC,kJAAkJ,CAAC,CAAC;YACjK,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,CAAC,MAAM,EAAE,CAAC;SACjB;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAExD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,mCAAmC;YACnC,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,UAAU,GAAG,CAAC,GAAG,WAAW,CAAC,KAAK,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;oBACzF,OAAO,GAAG,IAAI,CAAC;oBACf,IAAI,IAAI,CAAC,oBAAoB,EAAE;wBAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;qBAC3B;oBACD,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,UAAU,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;iBACvE;gBACD,WAAW,EAAE,CAAC;gBACd,IAAI,WAAW,KAAK,UAAU,CAAC,UAAU,EAAE;oBACvC,OAAO,WAAW,GAAG,CAAC,EAAE,WAAW,EAAE,EAAE;wBACnC,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,UAAU,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;qBACjE;oBACD,WAAW,GAAG,CAAC,CAAC;oBAChB,UAAU,EAAE,CAAC;iBAChB;aACJ;YAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC;SAC9C;aAAM;YACH,uBAAuB;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;aAC5C;SACJ;IACL,CAAC;IAIO,YAAY,CAAC,IAAY,EAAE,MAAmB;QAClD,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACrC,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC;QAC/B,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;YACvC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAC9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,iBAAiB;IAET,0BAA0B,CAAC,IAAY,EAAE,MAAoB;QACjE,4CAA4C;QAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACxB,aAAa,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACjD,aAAa,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACzD,aAAa,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACzD,aAAa,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;SAC9C;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IAC5D,CAAC;IAEO,yBAAyB,CAAC,IAAY,EAAE,MAAoB;QAChE,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAEO,yBAAyB,CAAC,IAAY,EAAE,MAAoB;QAChE,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAEO,0BAA0B,CAAC,IAAY,EAAE,MAAoB;QACjE,4CAA4C;QAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACxB,aAAa,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACjD,aAAa,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACzD,aAAa,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3C,aAAa,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;SAC9C;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,qBAAqB,CAAC,IAAY,EAAE,CAAS;QACjD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC1C,CAAC;IAEO,sBAAsB,CAAC,IAAY,EAAE,CAAS;QAClD,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,sBAAsB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,MAAM,GAAG,EAAE;QAC1E,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACvD,CAAC;IAEO,uBAAuB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS;QAC9D,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACjC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,sBAAsB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,MAAM,GAAG,EAAE;QACrF,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1D,CAAC;IAEO,uBAAuB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACzE,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACjC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACjC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,sBAAsB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,MAAM,GAAG,EAAE;QAChG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7D,CAAC;IAEO,uBAAuB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACpF,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACjC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACjC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACjC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,0BAA0B,CAAC,IAAY,EAAE,KAAmB;QAChE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC;IAEO,2BAA2B,CAAC,IAAY,EAAE,KAAmB;QACjE,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IACvD,CAAC;IAEO,qBAAqB,CAAC,IAAY,EAAE,KAAe;QACvD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAEO,sBAAsB,CAAC,IAAY,EAAE,KAAe;QACxD,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IACvD,CAAC;IAEO,wBAAwB,CAAC,IAAY,EAAE,KAAiB;QAC5D,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;IAEO,yBAAyB,CAAC,IAAY,EAAE,KAAiB;QAC7D,aAAa,CAAC,oBAAoB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAC3E,CAAC;IAEO,yBAAyB,CAAC,IAAY,EAAE,KAAkB;QAC9D,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAEO,0BAA0B,CAAC,IAAY,EAAE,KAAkB;QAC/D,aAAa,CAAC,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC/C,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAC3E,CAAC;IAEO,sBAAsB,CAAC,IAAY,EAAE,GAAgB;QACzD,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC7C,CAAC;IAEO,uBAAuB,CAAC,IAAY,EAAE,GAAgB;QAC1D,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;YAC9B,IAAI,CAAC,aAAa,CAAC,IAAI,EAAO,GAAG,CAAC,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;SACpD;IACL,CAAC;IAEO,wBAAwB,CAAC,IAAY,EAAE,GAAiB;QAC5D,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC/C,CAAC;IAEO,yBAAyB,CAAC,IAAY,EAAE,GAAiB;QAC7D,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;IAC9C,CAAC;IAEO,uBAAuB,CAAC,IAAY,EAAE,MAAoB;QAC9D,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAEO,wBAAwB,CAAC,IAAY,EAAE,MAAoB;QAC/D,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACxC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACxC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,uBAAuB,CAAC,IAAY,EAAE,MAAoB;QAC9D,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAEO,wBAAwB,CAAC,IAAY,EAAE,MAAoB;QAC/D,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACxC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACxC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACxC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,sBAAsB,CAAC,IAAY,EAAE,KAAkB,EAAE,MAAM,GAAG,EAAE;QACxE,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,EAAE,KAAK,CAAC,CAAC;IACxD,CAAC;IAEO,uBAAuB,CAAC,IAAY,EAAE,KAAkB;QAC5D,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,sBAAsB,CAAC,IAAY,EAAE,KAAkB,EAAE,KAAa,EAAE,MAAM,GAAG,EAAE;QACvF,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,GAAG,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC/D,CAAC;IAEO,4BAA4B,CAAC,IAAY,EAAE,KAAkB,EAAE,MAAM,GAAG,EAAE;QAC9E,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,GAAG,MAAM,EAAE,KAAK,CAAC,CAAC;IAC9D,CAAC;IAEO,uBAAuB,CAAC,IAAY,EAAE,KAAkB,EAAE,KAAa;QAC3E,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;QACrC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,6BAA6B,CAAC,IAAY,EAAE,KAAkB;QAClE,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,mBAAmB,CAAC,IAAY,EAAE,CAAS,EAAE,MAAM,GAAG,EAAE;QAC5D,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;IACjD,CAAC;IAEO,oBAAoB,CAAC,IAAY,EAAE,CAAS;QAChD,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,oBAAoB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,MAAM,GAAG,EAAE;QACxE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IAEO,qBAAqB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS;QAC5D,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,oBAAoB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,MAAM,GAAG,EAAE;QACnF,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACxD,CAAC;IAEO,qBAAqB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACvE,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,oBAAoB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,MAAM,GAAG,EAAE;QAC9F,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,qBAAqB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAClF,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,oBAAoB,CAAC,IAAY,EAAE,CAAS,EAAE,MAAM,GAAG,EAAE;QAC7D,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC;IAEO,qBAAqB,CAAC,IAAY,EAAE,CAAS;QACjD,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,qBAAqB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,MAAM,GAAG,EAAE;QACzE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtD,CAAC;IAEO,sBAAsB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS;QAC7D,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3C,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,qBAAqB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,MAAM,GAAG,EAAE;QACpF,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAEO,sBAAsB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACxE,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3C,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3C,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,qBAAqB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,MAAM,GAAG,EAAE;QAC/F,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEO,sBAAsB,CAAC,IAAY,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACnF,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3C,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3C,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3C,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,IAAY,EAAE,OAA8B;QAC1D,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,IAAY,EAAE,OAAkC;QAC/D,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,WAAmB,EAAE,IAAgB;QAC9D,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEnD,IAAI,CAAC,MAAM,EAAE,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,MAAc,EAAE,IAAY;QAC5C,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;QAC7B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE;YACrD,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAChF;IACL,CAAC;IAED;;OAEG;IACI,YAAY;QACf,IAAI,CAAC,cAAc,GAAG,SAAgB,CAAC;QACvC,IAAI,CAAC,kBAAkB,GAAG,SAAgB,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,UAAsB;QACvC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,CAAC;SACtC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;gBAC1B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;gBACtB,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;gBAC1B,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;gBAClC,IAAI,CAAC,cAAc,GAAG,SAAgB,CAAC;gBACvC,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO;SACV;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;QACpD,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,cAAc,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAClE,cAAc,CAAC,GAAG,EAAE,CAAC;SACxB;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAO,CAAC,CAAC;aACxC;SACJ;aAAM,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAClE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACvB;IACL,CAAC;;AA3pCD,gBAAgB;AACF,iCAAmB,GAA+B,EAAE,AAAjC,CAAkC;AAqBnE,iCAAiC;AAClB,+BAAiB,GAAG,GAAG,AAAN,CAAO;AACxB,yBAAW,GAAG,IAAI,YAAY,CAAC,aAAa,CAAC,iBAAiB,CAAC,AAApD,CAAqD;AAChE,kCAAoB,GAAG,IAAI,UAAU,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,AAAnD,CAAoD;AACxE,mCAAqB,GAAG,IAAI,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,AAApD,CAAqD", "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport type { Nullable, FloatArray } from \"../types\";\r\nimport type { IMatrixLike, IVector3Like, IVector4Like, IColor3Like, IColor4Like } from \"../Maths/math.like\";\r\nimport type { Effect } from \"./effect\";\r\nimport type { ThinTexture } from \"../Materials/Textures/thinTexture\";\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport type { ThinEngine } from \"../Engines/thinEngine\";\r\nimport type { InternalTexture } from \"./Textures/internalTexture\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport \"../Engines/Extensions/engine.uniformBuffer\";\r\n\r\n/**\r\n * Uniform buffer objects.\r\n *\r\n * Handles blocks of uniform on the GPU.\r\n *\r\n * If WebGL 2 is not available, this class falls back on traditional setUniformXXX calls.\r\n *\r\n * For more information, please refer to :\r\n * https://www.khronos.org/opengl/wiki/Uniform_Buffer_Object\r\n */\r\nexport class UniformBuffer {\r\n    /** @internal */\r\n    public static _UpdatedUbosInFrame: { [name: string]: number } = {};\r\n\r\n    private _engine: ThinEngine;\r\n    private _buffer: Nullable<DataBuffer>;\r\n    private _buffers: Array<[DataBuffer, Float32Array | undefined]>;\r\n    private _bufferIndex: number;\r\n    private _createBufferOnWrite: boolean;\r\n    private _data: number[];\r\n    private _bufferData: Float32Array;\r\n    private _dynamic?: boolean;\r\n    private _uniformLocations: { [key: string]: number };\r\n    private _uniformSizes: { [key: string]: number };\r\n    private _uniformArraySizes: { [key: string]: { strideSize: number; arraySize: number } };\r\n    private _uniformLocationPointer: number;\r\n    private _needSync: boolean;\r\n    private _noUBO: boolean;\r\n    private _currentEffect: Effect;\r\n    private _currentEffectName: string;\r\n    private _name: string;\r\n    private _currentFrameId: number;\r\n\r\n    // Pool for avoiding memory leaks\r\n    private static _MAX_UNIFORM_SIZE = 256;\r\n    private static _TempBuffer = new Float32Array(UniformBuffer._MAX_UNIFORM_SIZE);\r\n    private static _TempBufferInt32View = new Int32Array(UniformBuffer._TempBuffer.buffer);\r\n    private static _TempBufferUInt32View = new Uint32Array(UniformBuffer._TempBuffer.buffer);\r\n\r\n    /**\r\n     * Lambda to Update a 3x3 Matrix in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateMatrix3x3: (name: string, matrix: Float32Array) => void;\r\n\r\n    /**\r\n     * Lambda to Update a 2x2 Matrix in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateMatrix2x2: (name: string, matrix: Float32Array) => void;\r\n\r\n    /**\r\n     * Lambda to Update a single float in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateFloat: (name: string, x: number) => void;\r\n\r\n    /**\r\n     * Lambda to Update a vec2 of float in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateFloat2: (name: string, x: number, y: number, suffix?: string) => void;\r\n\r\n    /**\r\n     * Lambda to Update a vec3 of float in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateFloat3: (name: string, x: number, y: number, z: number, suffix?: string) => void;\r\n\r\n    /**\r\n     * Lambda to Update a vec4 of float in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateFloat4: (name: string, x: number, y: number, z: number, w: number, suffix?: string) => void;\r\n\r\n    /**\r\n     * Lambda to Update an array of float in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateFloatArray: (name: string, array: Float32Array) => void;\r\n\r\n    /**\r\n     * Lambda to Update an array of number in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateArray: (name: string, array: number[]) => void;\r\n\r\n    /**\r\n     * Lambda to Update an array of number in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateIntArray: (name: string, array: Int32Array) => void;\r\n\r\n    /**\r\n     * Lambda to Update an array of number in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateUIntArray: (name: string, array: Uint32Array) => void;\r\n\r\n    /**\r\n     * Lambda to Update a 4x4 Matrix in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateMatrix: (name: string, mat: IMatrixLike) => void;\r\n\r\n    /**\r\n     * Lambda to Update an array of 4x4 Matrix in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateMatrices: (name: string, mat: Float32Array) => void;\r\n\r\n    /**\r\n     * Lambda to Update vec3 of float from a Vector in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateVector3: (name: string, vector: IVector3Like) => void;\r\n\r\n    /**\r\n     * Lambda to Update vec4 of float from a Vector in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateVector4: (name: string, vector: IVector4Like) => void;\r\n\r\n    /**\r\n     * Lambda to Update vec3 of float from a Color in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateColor3: (name: string, color: IColor3Like, suffix?: string) => void;\r\n\r\n    /**\r\n     * Lambda to Update vec4 of float from a Color in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateColor4: (name: string, color: IColor3Like, alpha: number, suffix?: string) => void;\r\n\r\n    /**\r\n     * Lambda to Update vec4 of float from a Color in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateDirectColor4: (name: string, color: IColor4Like, suffix?: string) => void;\r\n\r\n    /**\r\n     * Lambda to Update a int a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateInt: (name: string, x: number, suffix?: string) => void;\r\n\r\n    /**\r\n     * Lambda to Update a vec2 of int in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateInt2: (name: string, x: number, y: number, suffix?: string) => void;\r\n\r\n    /**\r\n     * Lambda to Update a vec3 of int in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateInt3: (name: string, x: number, y: number, z: number, suffix?: string) => void;\r\n\r\n    /**\r\n     * Lambda to Update a vec4 of int in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateInt4: (name: string, x: number, y: number, z: number, w: number, suffix?: string) => void;\r\n\r\n    /**\r\n     * Lambda to Update a unsigned int a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateUInt: (name: string, x: number, suffix?: string) => void;\r\n\r\n    /**\r\n     * Lambda to Update a vec2 of unsigned int in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateUInt2: (name: string, x: number, y: number, suffix?: string) => void;\r\n\r\n    /**\r\n     * Lambda to Update a vec3 of unsigned int in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateUInt3: (name: string, x: number, y: number, z: number, suffix?: string) => void;\r\n\r\n    /**\r\n     * Lambda to Update a vec4 of unsigned int in a uniform buffer.\r\n     * This is dynamic to allow compat with webgl 1 and 2.\r\n     * You will need to pass the name of the uniform as well as the value.\r\n     */\r\n    public updateUInt4: (name: string, x: number, y: number, z: number, w: number, suffix?: string) => void;\r\n\r\n    /**\r\n     * Instantiates a new Uniform buffer objects.\r\n     *\r\n     * Handles blocks of uniform on the GPU.\r\n     *\r\n     * If WebGL 2 is not available, this class falls back on traditional setUniformXXX calls.\r\n     *\r\n     * For more information, please refer to :\r\n     * @see https://www.khronos.org/opengl/wiki/Uniform_Buffer_Object\r\n     * @param engine Define the engine the buffer is associated with\r\n     * @param data Define the data contained in the buffer\r\n     * @param dynamic Define if the buffer is updatable\r\n     * @param name to assign to the buffer (debugging purpose)\r\n     * @param forceNoUniformBuffer define that this object must not rely on UBO objects\r\n     */\r\n    constructor(engine: ThinEngine, data?: number[], dynamic?: boolean, name?: string, forceNoUniformBuffer = false) {\r\n        this._engine = engine;\r\n        this._noUBO = !engine.supportsUniformBuffers || forceNoUniformBuffer;\r\n        this._dynamic = dynamic;\r\n        this._name = name ?? \"no-name\";\r\n\r\n        this._data = data || [];\r\n\r\n        this._uniformLocations = {};\r\n        this._uniformSizes = {};\r\n        this._uniformArraySizes = {};\r\n        this._uniformLocationPointer = 0;\r\n        this._needSync = false;\r\n\r\n        if (this._engine._features.trackUbosInFrame) {\r\n            this._buffers = [];\r\n            this._bufferIndex = -1;\r\n            this._createBufferOnWrite = false;\r\n            this._currentFrameId = 0;\r\n        }\r\n\r\n        if (this._noUBO) {\r\n            this.updateMatrix3x3 = this._updateMatrix3x3ForEffect;\r\n            this.updateMatrix2x2 = this._updateMatrix2x2ForEffect;\r\n            this.updateFloat = this._updateFloatForEffect;\r\n            this.updateFloat2 = this._updateFloat2ForEffect;\r\n            this.updateFloat3 = this._updateFloat3ForEffect;\r\n            this.updateFloat4 = this._updateFloat4ForEffect;\r\n            this.updateFloatArray = this._updateFloatArrayForEffect;\r\n            this.updateArray = this._updateArrayForEffect;\r\n            this.updateIntArray = this._updateIntArrayForEffect;\r\n            this.updateUIntArray = this._updateUIntArrayForEffect;\r\n            this.updateMatrix = this._updateMatrixForEffect;\r\n            this.updateMatrices = this._updateMatricesForEffect;\r\n            this.updateVector3 = this._updateVector3ForEffect;\r\n            this.updateVector4 = this._updateVector4ForEffect;\r\n            this.updateColor3 = this._updateColor3ForEffect;\r\n            this.updateColor4 = this._updateColor4ForEffect;\r\n            this.updateDirectColor4 = this._updateDirectColor4ForEffect;\r\n            this.updateInt = this._updateIntForEffect;\r\n            this.updateInt2 = this._updateInt2ForEffect;\r\n            this.updateInt3 = this._updateInt3ForEffect;\r\n            this.updateInt4 = this._updateInt4ForEffect;\r\n            this.updateUInt = this._updateUIntForEffect;\r\n            this.updateUInt2 = this._updateUInt2ForEffect;\r\n            this.updateUInt3 = this._updateUInt3ForEffect;\r\n            this.updateUInt4 = this._updateUInt4ForEffect;\r\n        } else {\r\n            this._engine._uniformBuffers.push(this);\r\n\r\n            this.updateMatrix3x3 = this._updateMatrix3x3ForUniform;\r\n            this.updateMatrix2x2 = this._updateMatrix2x2ForUniform;\r\n            this.updateFloat = this._updateFloatForUniform;\r\n            this.updateFloat2 = this._updateFloat2ForUniform;\r\n            this.updateFloat3 = this._updateFloat3ForUniform;\r\n            this.updateFloat4 = this._updateFloat4ForUniform;\r\n            this.updateFloatArray = this._updateFloatArrayForUniform;\r\n            this.updateArray = this._updateArrayForUniform;\r\n            this.updateIntArray = this._updateIntArrayForUniform;\r\n            this.updateUIntArray = this._updateUIntArrayForUniform;\r\n            this.updateMatrix = this._updateMatrixForUniform;\r\n            this.updateMatrices = this._updateMatricesForUniform;\r\n            this.updateVector3 = this._updateVector3ForUniform;\r\n            this.updateVector4 = this._updateVector4ForUniform;\r\n            this.updateColor3 = this._updateColor3ForUniform;\r\n            this.updateColor4 = this._updateColor4ForUniform;\r\n            this.updateDirectColor4 = this._updateDirectColor4ForUniform;\r\n            this.updateInt = this._updateIntForUniform;\r\n            this.updateInt2 = this._updateInt2ForUniform;\r\n            this.updateInt3 = this._updateInt3ForUniform;\r\n            this.updateInt4 = this._updateInt4ForUniform;\r\n            this.updateUInt = this._updateUIntForUniform;\r\n            this.updateUInt2 = this._updateUInt2ForUniform;\r\n            this.updateUInt3 = this._updateUInt3ForUniform;\r\n            this.updateUInt4 = this._updateUInt4ForUniform;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Indicates if the buffer is using the WebGL2 UBO implementation,\r\n     * or just falling back on setUniformXXX calls.\r\n     */\r\n    public get useUbo(): boolean {\r\n        return !this._noUBO;\r\n    }\r\n\r\n    /**\r\n     * Indicates if the WebGL underlying uniform buffer is in sync\r\n     * with the javascript cache data.\r\n     */\r\n    public get isSync(): boolean {\r\n        return !this._needSync;\r\n    }\r\n\r\n    /**\r\n     * Indicates if the WebGL underlying uniform buffer is dynamic.\r\n     * Also, a dynamic UniformBuffer will disable cache verification and always\r\n     * update the underlying WebGL uniform buffer to the GPU.\r\n     * @returns if Dynamic, otherwise false\r\n     */\r\n    public isDynamic(): boolean {\r\n        return this._dynamic !== undefined;\r\n    }\r\n\r\n    /**\r\n     * The data cache on JS side.\r\n     * @returns the underlying data as a float array\r\n     */\r\n    public getData(): Float32Array {\r\n        return this._bufferData;\r\n    }\r\n\r\n    /**\r\n     * The underlying WebGL Uniform buffer.\r\n     * @returns the webgl buffer\r\n     */\r\n    public getBuffer(): Nullable<DataBuffer> {\r\n        return this._buffer;\r\n    }\r\n\r\n    /**\r\n     * std140 layout specifies how to align data within an UBO structure.\r\n     * See https://khronos.org/registry/OpenGL/specs/gl/glspec45.core.pdf#page=159\r\n     * for specs.\r\n     * @param size\r\n     */\r\n    private _fillAlignment(size: number) {\r\n        // This code has been simplified because we only use floats, vectors of 1, 2, 3, 4 components\r\n        // and 4x4 matrices\r\n        // TODO : change if other types are used\r\n\r\n        let alignment;\r\n        if (size <= 2) {\r\n            alignment = size;\r\n        } else {\r\n            alignment = 4;\r\n        }\r\n\r\n        if (this._uniformLocationPointer % alignment !== 0) {\r\n            const oldPointer = this._uniformLocationPointer;\r\n            this._uniformLocationPointer += alignment - (this._uniformLocationPointer % alignment);\r\n            const diff = this._uniformLocationPointer - oldPointer;\r\n\r\n            for (let i = 0; i < diff; i++) {\r\n                this._data.push(0);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds an uniform in the buffer.\r\n     * Warning : the subsequents calls of this function must be in the same order as declared in the shader\r\n     * for the layout to be correct ! The addUniform function only handles types like float, vec2, vec3, vec4, mat4,\r\n     * meaning size=1,2,3,4 or 16. It does not handle struct types.\r\n     * @param name Name of the uniform, as used in the uniform block in the shader.\r\n     * @param size Data size, or data directly.\r\n     * @param arraySize The number of elements in the array, 0 if not an array.\r\n     */\r\n    public addUniform(name: string, size: number | number[], arraySize = 0) {\r\n        if (this._noUBO) {\r\n            return;\r\n        }\r\n\r\n        if (this._uniformLocations[name] !== undefined) {\r\n            // Already existing uniform\r\n            return;\r\n        }\r\n        // This function must be called in the order of the shader layout !\r\n        // size can be the size of the uniform, or data directly\r\n        let data;\r\n\r\n        // std140 FTW...\r\n        if (arraySize > 0) {\r\n            if (size instanceof Array) {\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"addUniform should not be use with Array in UBO: \" + name;\r\n            }\r\n\r\n            this._fillAlignment(4);\r\n\r\n            this._uniformArraySizes[name] = { strideSize: size, arraySize };\r\n            if (size == 16) {\r\n                size = size * arraySize;\r\n            } else {\r\n                const perElementPadding = 4 - size;\r\n                const totalPadding = perElementPadding * arraySize;\r\n                size = size * arraySize + totalPadding;\r\n            }\r\n\r\n            data = [];\r\n            // Fill with zeros\r\n            for (let i = 0; i < size; i++) {\r\n                data.push(0);\r\n            }\r\n        } else {\r\n            if (size instanceof Array) {\r\n                data = size;\r\n                size = data.length;\r\n            } else {\r\n                size = <number>size;\r\n                data = [];\r\n\r\n                // Fill with zeros\r\n                for (let i = 0; i < size; i++) {\r\n                    data.push(0);\r\n                }\r\n            }\r\n            this._fillAlignment(<number>size);\r\n        }\r\n\r\n        this._uniformSizes[name] = <number>size;\r\n        this._uniformLocations[name] = this._uniformLocationPointer;\r\n        this._uniformLocationPointer += <number>size;\r\n\r\n        for (let i = 0; i < size; i++) {\r\n            this._data.push(data[i]);\r\n        }\r\n\r\n        this._needSync = true;\r\n    }\r\n\r\n    /**\r\n     * Adds a Matrix 4x4 to the uniform buffer.\r\n     * @param name Name of the uniform, as used in the uniform block in the shader.\r\n     * @param mat A 4x4 matrix.\r\n     */\r\n    public addMatrix(name: string, mat: IMatrixLike) {\r\n        this.addUniform(name, Array.prototype.slice.call(mat.asArray()));\r\n    }\r\n\r\n    /**\r\n     * Adds a vec2 to the uniform buffer.\r\n     * @param name Name of the uniform, as used in the uniform block in the shader.\r\n     * @param x Define the x component value of the vec2\r\n     * @param y Define the y component value of the vec2\r\n     */\r\n    public addFloat2(name: string, x: number, y: number) {\r\n        const temp = [x, y];\r\n        this.addUniform(name, temp);\r\n    }\r\n\r\n    /**\r\n     * Adds a vec3 to the uniform buffer.\r\n     * @param name Name of the uniform, as used in the uniform block in the shader.\r\n     * @param x Define the x component value of the vec3\r\n     * @param y Define the y component value of the vec3\r\n     * @param z Define the z component value of the vec3\r\n     */\r\n    public addFloat3(name: string, x: number, y: number, z: number) {\r\n        const temp = [x, y, z];\r\n        this.addUniform(name, temp);\r\n    }\r\n\r\n    /**\r\n     * Adds a vec3 to the uniform buffer.\r\n     * @param name Name of the uniform, as used in the uniform block in the shader.\r\n     * @param color Define the vec3 from a Color\r\n     */\r\n    public addColor3(name: string, color: IColor3Like) {\r\n        const temp = [color.r, color.g, color.b];\r\n        this.addUniform(name, temp);\r\n    }\r\n\r\n    /**\r\n     * Adds a vec4 to the uniform buffer.\r\n     * @param name Name of the uniform, as used in the uniform block in the shader.\r\n     * @param color Define the rgb components from a Color\r\n     * @param alpha Define the a component of the vec4\r\n     */\r\n    public addColor4(name: string, color: IColor3Like, alpha: number) {\r\n        const temp = [color.r, color.g, color.b, alpha];\r\n        this.addUniform(name, temp);\r\n    }\r\n\r\n    /**\r\n     * Adds a vec3 to the uniform buffer.\r\n     * @param name Name of the uniform, as used in the uniform block in the shader.\r\n     * @param vector Define the vec3 components from a Vector\r\n     */\r\n    public addVector3(name: string, vector: IVector3Like) {\r\n        const temp = [vector.x, vector.y, vector.z];\r\n        this.addUniform(name, temp);\r\n    }\r\n\r\n    /**\r\n     * Adds a Matrix 3x3 to the uniform buffer.\r\n     * @param name Name of the uniform, as used in the uniform block in the shader.\r\n     */\r\n    public addMatrix3x3(name: string) {\r\n        this.addUniform(name, 12);\r\n    }\r\n\r\n    /**\r\n     * Adds a Matrix 2x2 to the uniform buffer.\r\n     * @param name Name of the uniform, as used in the uniform block in the shader.\r\n     */\r\n    public addMatrix2x2(name: string) {\r\n        this.addUniform(name, 8);\r\n    }\r\n\r\n    /**\r\n     * Effectively creates the WebGL Uniform Buffer, once layout is completed with `addUniform`.\r\n     */\r\n    public create(): void {\r\n        if (this._noUBO) {\r\n            return;\r\n        }\r\n        if (this._buffer) {\r\n            return; // nothing to do\r\n        }\r\n\r\n        // See spec, alignment must be filled as a vec4\r\n        this._fillAlignment(4);\r\n        this._bufferData = new Float32Array(this._data);\r\n\r\n        this._rebuild();\r\n\r\n        this._needSync = true;\r\n    }\r\n\r\n    // The result of this method is used for debugging purpose, as part of the buffer name\r\n    // It is meant to more easily know what this buffer is about when debugging\r\n    // Some buffers can have a lot of uniforms (several dozens), so the method only returns the first 10 of them\r\n    // (should be enough to understand what the buffer is for)\r\n    private _getNames() {\r\n        const names = [];\r\n        let i = 0;\r\n        for (const name in this._uniformLocations) {\r\n            names.push(name);\r\n            if (++i === 10) {\r\n                break;\r\n            }\r\n        }\r\n        return names.join(\",\");\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuild(): void {\r\n        if (this._noUBO || !this._bufferData) {\r\n            return;\r\n        }\r\n\r\n        if (this._dynamic) {\r\n            this._buffer = this._engine.createDynamicUniformBuffer(this._bufferData, this._name + \"_UniformList:\" + this._getNames());\r\n        } else {\r\n            this._buffer = this._engine.createUniformBuffer(this._bufferData, this._name + \"_UniformList:\" + this._getNames());\r\n        }\r\n\r\n        if (this._engine._features.trackUbosInFrame) {\r\n            this._buffers.push([this._buffer, this._engine._features.checkUbosContentBeforeUpload ? this._bufferData.slice() : undefined]);\r\n            this._bufferIndex = this._buffers.length - 1;\r\n            this._createBufferOnWrite = false;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuildAfterContextLost(): void {\r\n        if (this._engine._features.trackUbosInFrame) {\r\n            this._buffers = [];\r\n            this._currentFrameId = 0;\r\n        }\r\n        this._rebuild();\r\n    }\r\n\r\n    /** @internal */\r\n    public get _numBuffers(): number {\r\n        return this._buffers.length;\r\n    }\r\n\r\n    /** @internal */\r\n    public get _indexBuffer(): number {\r\n        return this._bufferIndex;\r\n    }\r\n\r\n    /** Gets the name of this buffer */\r\n    public get name(): string {\r\n        return this._name;\r\n    }\r\n\r\n    /** Gets the current effect */\r\n    public get currentEffect(): Nullable<Effect> {\r\n        return this._currentEffect;\r\n    }\r\n\r\n    private _buffersEqual(buf1: Float32Array, buf2: Float32Array): boolean {\r\n        for (let i = 0; i < buf1.length; ++i) {\r\n            if (buf1[i] !== buf2[i]) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    private _copyBuffer(src: Float32Array, dst: Float32Array): void {\r\n        for (let i = 0; i < src.length; ++i) {\r\n            dst[i] = src[i];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the WebGL Uniform Buffer on the GPU.\r\n     * If the `dynamic` flag is set to true, no cache comparison is done.\r\n     * Otherwise, the buffer will be updated only if the cache differs.\r\n     */\r\n    public update(): void {\r\n        if (this._noUBO) {\r\n            return;\r\n        }\r\n\r\n        this.bindUniformBuffer();\r\n\r\n        if (!this._buffer) {\r\n            this.create();\r\n            return;\r\n        }\r\n\r\n        if (!this._dynamic && !this._needSync) {\r\n            this._createBufferOnWrite = this._engine._features.trackUbosInFrame;\r\n            return;\r\n        }\r\n\r\n        if (this._buffers && this._buffers.length > 1 && this._buffers[this._bufferIndex][1]) {\r\n            if (this._buffersEqual(this._bufferData, this._buffers[this._bufferIndex][1]!)) {\r\n                this._needSync = false;\r\n                this._createBufferOnWrite = this._engine._features.trackUbosInFrame;\r\n                return;\r\n            } else {\r\n                this._copyBuffer(this._bufferData, this._buffers[this._bufferIndex][1]!);\r\n            }\r\n        }\r\n\r\n        this._engine.updateUniformBuffer(this._buffer, this._bufferData);\r\n\r\n        if (this._engine._features._collectUbosUpdatedInFrame) {\r\n            if (!UniformBuffer._UpdatedUbosInFrame[this._name]) {\r\n                UniformBuffer._UpdatedUbosInFrame[this._name] = 0;\r\n            }\r\n            UniformBuffer._UpdatedUbosInFrame[this._name]++;\r\n        }\r\n\r\n        this._needSync = false;\r\n        this._createBufferOnWrite = this._engine._features.trackUbosInFrame;\r\n    }\r\n\r\n    private _createNewBuffer() {\r\n        if (this._bufferIndex + 1 < this._buffers.length) {\r\n            this._bufferIndex++;\r\n            this._buffer = this._buffers[this._bufferIndex][0];\r\n            this._createBufferOnWrite = false;\r\n            this._needSync = true;\r\n        } else {\r\n            this._rebuild();\r\n        }\r\n    }\r\n\r\n    private _checkNewFrame(): void {\r\n        if (this._engine._features.trackUbosInFrame && this._currentFrameId !== this._engine.frameId) {\r\n            this._currentFrameId = this._engine.frameId;\r\n            this._createBufferOnWrite = false;\r\n            if (this._buffers && this._buffers.length > 0) {\r\n                this._needSync = this._bufferIndex !== 0;\r\n                this._bufferIndex = 0;\r\n                this._buffer = this._buffers[this._bufferIndex][0];\r\n            } else {\r\n                this._bufferIndex = -1;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the value of an uniform. The `update` method must be called afterwards to make it effective in the GPU.\r\n     * @param uniformName Define the name of the uniform, as used in the uniform block in the shader.\r\n     * @param data Define the flattened data\r\n     * @param size Define the size of the data.\r\n     */\r\n    public updateUniform(uniformName: string, data: FloatArray, size: number) {\r\n        this._checkNewFrame();\r\n\r\n        let location = this._uniformLocations[uniformName];\r\n        if (location === undefined) {\r\n            if (this._buffer) {\r\n                // Cannot add an uniform if the buffer is already created\r\n                Logger.Error(\"Cannot add an uniform after UBO has been created. uniformName=\" + uniformName);\r\n                return;\r\n            }\r\n            this.addUniform(uniformName, size);\r\n            location = this._uniformLocations[uniformName];\r\n        }\r\n\r\n        if (!this._buffer) {\r\n            this.create();\r\n        }\r\n\r\n        if (!this._dynamic) {\r\n            // Cache for static uniform buffers\r\n            let changed = false;\r\n\r\n            for (let i = 0; i < size; i++) {\r\n                // We are checking the matrix cache before calling updateUniform so we do not need to check it here\r\n                // Hence the test for size === 16 to simply commit the matrix values\r\n                if ((size === 16 && !this._engine._features.uniformBufferHardCheckMatrix) || this._bufferData[location + i] !== Math.fround(data[i])) {\r\n                    changed = true;\r\n                    if (this._createBufferOnWrite) {\r\n                        this._createNewBuffer();\r\n                    }\r\n                    this._bufferData[location + i] = data[i];\r\n                }\r\n            }\r\n\r\n            this._needSync = this._needSync || changed;\r\n        } else {\r\n            // No cache for dynamic\r\n            for (let i = 0; i < size; i++) {\r\n                this._bufferData[location + i] = data[i];\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the value of an uniform. The `update` method must be called afterwards to make it effective in the GPU.\r\n     * @param uniformName Define the name of the uniform, as used in the uniform block in the shader.\r\n     * @param data Define the flattened data\r\n     * @param size Define the size of the data.\r\n     */\r\n    public updateUniformArray(uniformName: string, data: FloatArray, size: number) {\r\n        this._checkNewFrame();\r\n\r\n        const location = this._uniformLocations[uniformName];\r\n        if (location === undefined) {\r\n            Logger.Error(\"Cannot add an uniform Array dynamically. Please, add it using addUniform and make sure that uniform buffers are supported by the current engine.\");\r\n            return;\r\n        }\r\n\r\n        if (!this._buffer) {\r\n            this.create();\r\n        }\r\n\r\n        const arraySizes = this._uniformArraySizes[uniformName];\r\n\r\n        if (!this._dynamic) {\r\n            // Cache for static uniform buffers\r\n            let changed = false;\r\n            let countToFour = 0;\r\n            let baseStride = 0;\r\n            for (let i = 0; i < size; i++) {\r\n                if (this._bufferData[location + baseStride * 4 + countToFour] !== Tools.FloatRound(data[i])) {\r\n                    changed = true;\r\n                    if (this._createBufferOnWrite) {\r\n                        this._createNewBuffer();\r\n                    }\r\n                    this._bufferData[location + baseStride * 4 + countToFour] = data[i];\r\n                }\r\n                countToFour++;\r\n                if (countToFour === arraySizes.strideSize) {\r\n                    for (; countToFour < 4; countToFour++) {\r\n                        this._bufferData[location + baseStride * 4 + countToFour] = 0;\r\n                    }\r\n                    countToFour = 0;\r\n                    baseStride++;\r\n                }\r\n            }\r\n\r\n            this._needSync = this._needSync || changed;\r\n        } else {\r\n            // No cache for dynamic\r\n            for (let i = 0; i < size; i++) {\r\n                this._bufferData[location + i] = data[i];\r\n            }\r\n        }\r\n    }\r\n\r\n    // Matrix cache\r\n    private _valueCache: { [key: string]: number } = {};\r\n    private _cacheMatrix(name: string, matrix: IMatrixLike): boolean {\r\n        this._checkNewFrame();\r\n\r\n        const cache = this._valueCache[name];\r\n        const flag = matrix.updateFlag;\r\n        if (cache !== undefined && cache === flag) {\r\n            return false;\r\n        }\r\n\r\n        this._valueCache[name] = flag;\r\n        return true;\r\n    }\r\n\r\n    // Update methods\r\n\r\n    private _updateMatrix3x3ForUniform(name: string, matrix: Float32Array): void {\r\n        // To match std140, matrix must be realigned\r\n        for (let i = 0; i < 3; i++) {\r\n            UniformBuffer._TempBuffer[i * 4] = matrix[i * 3];\r\n            UniformBuffer._TempBuffer[i * 4 + 1] = matrix[i * 3 + 1];\r\n            UniformBuffer._TempBuffer[i * 4 + 2] = matrix[i * 3 + 2];\r\n            UniformBuffer._TempBuffer[i * 4 + 3] = 0.0;\r\n        }\r\n\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 12);\r\n    }\r\n\r\n    private _updateMatrix3x3ForEffect(name: string, matrix: Float32Array): void {\r\n        this._currentEffect.setMatrix3x3(name, matrix);\r\n    }\r\n\r\n    private _updateMatrix2x2ForEffect(name: string, matrix: Float32Array): void {\r\n        this._currentEffect.setMatrix2x2(name, matrix);\r\n    }\r\n\r\n    private _updateMatrix2x2ForUniform(name: string, matrix: Float32Array): void {\r\n        // To match std140, matrix must be realigned\r\n        for (let i = 0; i < 2; i++) {\r\n            UniformBuffer._TempBuffer[i * 4] = matrix[i * 2];\r\n            UniformBuffer._TempBuffer[i * 4 + 1] = matrix[i * 2 + 1];\r\n            UniformBuffer._TempBuffer[i * 4 + 2] = 0.0;\r\n            UniformBuffer._TempBuffer[i * 4 + 3] = 0.0;\r\n        }\r\n\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 8);\r\n    }\r\n\r\n    private _updateFloatForEffect(name: string, x: number) {\r\n        this._currentEffect.setFloat(name, x);\r\n    }\r\n\r\n    private _updateFloatForUniform(name: string, x: number) {\r\n        UniformBuffer._TempBuffer[0] = x;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 1);\r\n    }\r\n\r\n    private _updateFloat2ForEffect(name: string, x: number, y: number, suffix = \"\") {\r\n        this._currentEffect.setFloat2(name + suffix, x, y);\r\n    }\r\n\r\n    private _updateFloat2ForUniform(name: string, x: number, y: number) {\r\n        UniformBuffer._TempBuffer[0] = x;\r\n        UniformBuffer._TempBuffer[1] = y;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 2);\r\n    }\r\n\r\n    private _updateFloat3ForEffect(name: string, x: number, y: number, z: number, suffix = \"\") {\r\n        this._currentEffect.setFloat3(name + suffix, x, y, z);\r\n    }\r\n\r\n    private _updateFloat3ForUniform(name: string, x: number, y: number, z: number) {\r\n        UniformBuffer._TempBuffer[0] = x;\r\n        UniformBuffer._TempBuffer[1] = y;\r\n        UniformBuffer._TempBuffer[2] = z;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 3);\r\n    }\r\n\r\n    private _updateFloat4ForEffect(name: string, x: number, y: number, z: number, w: number, suffix = \"\") {\r\n        this._currentEffect.setFloat4(name + suffix, x, y, z, w);\r\n    }\r\n\r\n    private _updateFloat4ForUniform(name: string, x: number, y: number, z: number, w: number) {\r\n        UniformBuffer._TempBuffer[0] = x;\r\n        UniformBuffer._TempBuffer[1] = y;\r\n        UniformBuffer._TempBuffer[2] = z;\r\n        UniformBuffer._TempBuffer[3] = w;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 4);\r\n    }\r\n\r\n    private _updateFloatArrayForEffect(name: string, array: Float32Array) {\r\n        this._currentEffect.setFloatArray(name, array);\r\n    }\r\n\r\n    private _updateFloatArrayForUniform(name: string, array: Float32Array) {\r\n        this.updateUniformArray(name, array, array.length);\r\n    }\r\n\r\n    private _updateArrayForEffect(name: string, array: number[]) {\r\n        this._currentEffect.setArray(name, array);\r\n    }\r\n\r\n    private _updateArrayForUniform(name: string, array: number[]) {\r\n        this.updateUniformArray(name, array, array.length);\r\n    }\r\n\r\n    private _updateIntArrayForEffect(name: string, array: Int32Array) {\r\n        this._currentEffect.setIntArray(name, array);\r\n    }\r\n\r\n    private _updateIntArrayForUniform(name: string, array: Int32Array) {\r\n        UniformBuffer._TempBufferInt32View.set(array);\r\n        this.updateUniformArray(name, UniformBuffer._TempBuffer, array.length);\r\n    }\r\n\r\n    private _updateUIntArrayForEffect(name: string, array: Uint32Array) {\r\n        this._currentEffect.setUIntArray(name, array);\r\n    }\r\n\r\n    private _updateUIntArrayForUniform(name: string, array: Uint32Array) {\r\n        UniformBuffer._TempBufferUInt32View.set(array);\r\n        this.updateUniformArray(name, UniformBuffer._TempBuffer, array.length);\r\n    }\r\n\r\n    private _updateMatrixForEffect(name: string, mat: IMatrixLike) {\r\n        this._currentEffect.setMatrix(name, mat);\r\n    }\r\n\r\n    private _updateMatrixForUniform(name: string, mat: IMatrixLike) {\r\n        if (this._cacheMatrix(name, mat)) {\r\n            this.updateUniform(name, <any>mat.asArray(), 16);\r\n        }\r\n    }\r\n\r\n    private _updateMatricesForEffect(name: string, mat: Float32Array) {\r\n        this._currentEffect.setMatrices(name, mat);\r\n    }\r\n\r\n    private _updateMatricesForUniform(name: string, mat: Float32Array) {\r\n        this.updateUniform(name, mat, mat.length);\r\n    }\r\n\r\n    private _updateVector3ForEffect(name: string, vector: IVector3Like) {\r\n        this._currentEffect.setVector3(name, vector);\r\n    }\r\n\r\n    private _updateVector3ForUniform(name: string, vector: IVector3Like) {\r\n        UniformBuffer._TempBuffer[0] = vector.x;\r\n        UniformBuffer._TempBuffer[1] = vector.y;\r\n        UniformBuffer._TempBuffer[2] = vector.z;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 3);\r\n    }\r\n\r\n    private _updateVector4ForEffect(name: string, vector: IVector4Like) {\r\n        this._currentEffect.setVector4(name, vector);\r\n    }\r\n\r\n    private _updateVector4ForUniform(name: string, vector: IVector4Like) {\r\n        UniformBuffer._TempBuffer[0] = vector.x;\r\n        UniformBuffer._TempBuffer[1] = vector.y;\r\n        UniformBuffer._TempBuffer[2] = vector.z;\r\n        UniformBuffer._TempBuffer[3] = vector.w;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 4);\r\n    }\r\n\r\n    private _updateColor3ForEffect(name: string, color: IColor3Like, suffix = \"\") {\r\n        this._currentEffect.setColor3(name + suffix, color);\r\n    }\r\n\r\n    private _updateColor3ForUniform(name: string, color: IColor3Like) {\r\n        UniformBuffer._TempBuffer[0] = color.r;\r\n        UniformBuffer._TempBuffer[1] = color.g;\r\n        UniformBuffer._TempBuffer[2] = color.b;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 3);\r\n    }\r\n\r\n    private _updateColor4ForEffect(name: string, color: IColor3Like, alpha: number, suffix = \"\") {\r\n        this._currentEffect.setColor4(name + suffix, color, alpha);\r\n    }\r\n\r\n    private _updateDirectColor4ForEffect(name: string, color: IColor4Like, suffix = \"\") {\r\n        this._currentEffect.setDirectColor4(name + suffix, color);\r\n    }\r\n\r\n    private _updateColor4ForUniform(name: string, color: IColor3Like, alpha: number) {\r\n        UniformBuffer._TempBuffer[0] = color.r;\r\n        UniformBuffer._TempBuffer[1] = color.g;\r\n        UniformBuffer._TempBuffer[2] = color.b;\r\n        UniformBuffer._TempBuffer[3] = alpha;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 4);\r\n    }\r\n\r\n    private _updateDirectColor4ForUniform(name: string, color: IColor4Like) {\r\n        UniformBuffer._TempBuffer[0] = color.r;\r\n        UniformBuffer._TempBuffer[1] = color.g;\r\n        UniformBuffer._TempBuffer[2] = color.b;\r\n        UniformBuffer._TempBuffer[3] = color.a;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 4);\r\n    }\r\n\r\n    private _updateIntForEffect(name: string, x: number, suffix = \"\") {\r\n        this._currentEffect.setInt(name + suffix, x);\r\n    }\r\n\r\n    private _updateIntForUniform(name: string, x: number) {\r\n        UniformBuffer._TempBufferInt32View[0] = x;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 1);\r\n    }\r\n\r\n    private _updateInt2ForEffect(name: string, x: number, y: number, suffix = \"\") {\r\n        this._currentEffect.setInt2(name + suffix, x, y);\r\n    }\r\n\r\n    private _updateInt2ForUniform(name: string, x: number, y: number) {\r\n        UniformBuffer._TempBufferInt32View[0] = x;\r\n        UniformBuffer._TempBufferInt32View[1] = y;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 2);\r\n    }\r\n\r\n    private _updateInt3ForEffect(name: string, x: number, y: number, z: number, suffix = \"\") {\r\n        this._currentEffect.setInt3(name + suffix, x, y, z);\r\n    }\r\n\r\n    private _updateInt3ForUniform(name: string, x: number, y: number, z: number) {\r\n        UniformBuffer._TempBufferInt32View[0] = x;\r\n        UniformBuffer._TempBufferInt32View[1] = y;\r\n        UniformBuffer._TempBufferInt32View[2] = z;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 3);\r\n    }\r\n\r\n    private _updateInt4ForEffect(name: string, x: number, y: number, z: number, w: number, suffix = \"\") {\r\n        this._currentEffect.setInt4(name + suffix, x, y, z, w);\r\n    }\r\n\r\n    private _updateInt4ForUniform(name: string, x: number, y: number, z: number, w: number) {\r\n        UniformBuffer._TempBufferInt32View[0] = x;\r\n        UniformBuffer._TempBufferInt32View[1] = y;\r\n        UniformBuffer._TempBufferInt32View[2] = z;\r\n        UniformBuffer._TempBufferInt32View[3] = w;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 4);\r\n    }\r\n\r\n    private _updateUIntForEffect(name: string, x: number, suffix = \"\") {\r\n        this._currentEffect.setUInt(name + suffix, x);\r\n    }\r\n\r\n    private _updateUIntForUniform(name: string, x: number) {\r\n        UniformBuffer._TempBufferUInt32View[0] = x;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 1);\r\n    }\r\n\r\n    private _updateUInt2ForEffect(name: string, x: number, y: number, suffix = \"\") {\r\n        this._currentEffect.setUInt2(name + suffix, x, y);\r\n    }\r\n\r\n    private _updateUInt2ForUniform(name: string, x: number, y: number) {\r\n        UniformBuffer._TempBufferUInt32View[0] = x;\r\n        UniformBuffer._TempBufferUInt32View[1] = y;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 2);\r\n    }\r\n\r\n    private _updateUInt3ForEffect(name: string, x: number, y: number, z: number, suffix = \"\") {\r\n        this._currentEffect.setUInt3(name + suffix, x, y, z);\r\n    }\r\n\r\n    private _updateUInt3ForUniform(name: string, x: number, y: number, z: number) {\r\n        UniformBuffer._TempBufferUInt32View[0] = x;\r\n        UniformBuffer._TempBufferUInt32View[1] = y;\r\n        UniformBuffer._TempBufferUInt32View[2] = z;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 3);\r\n    }\r\n\r\n    private _updateUInt4ForEffect(name: string, x: number, y: number, z: number, w: number, suffix = \"\") {\r\n        this._currentEffect.setUInt4(name + suffix, x, y, z, w);\r\n    }\r\n\r\n    private _updateUInt4ForUniform(name: string, x: number, y: number, z: number, w: number) {\r\n        UniformBuffer._TempBufferUInt32View[0] = x;\r\n        UniformBuffer._TempBufferUInt32View[1] = y;\r\n        UniformBuffer._TempBufferUInt32View[2] = z;\r\n        UniformBuffer._TempBufferUInt32View[3] = w;\r\n        this.updateUniform(name, UniformBuffer._TempBuffer, 4);\r\n    }\r\n\r\n    /**\r\n     * Sets a sampler uniform on the effect.\r\n     * @param name Define the name of the sampler.\r\n     * @param texture Define the texture to set in the sampler\r\n     */\r\n    public setTexture(name: string, texture: Nullable<ThinTexture>) {\r\n        this._currentEffect.setTexture(name, texture);\r\n    }\r\n\r\n    /**\r\n     * Sets a sampler uniform on the effect.\r\n     * @param name Define the name of the sampler.\r\n     * @param texture Define the (internal) texture to set in the sampler\r\n     */\r\n    public bindTexture(name: string, texture: Nullable<InternalTexture>) {\r\n        this._currentEffect._bindTexture(name, texture);\r\n    }\r\n\r\n    /**\r\n     * Directly updates the value of the uniform in the cache AND on the GPU.\r\n     * @param uniformName Define the name of the uniform, as used in the uniform block in the shader.\r\n     * @param data Define the flattened data\r\n     */\r\n    public updateUniformDirectly(uniformName: string, data: FloatArray) {\r\n        this.updateUniform(uniformName, data, data.length);\r\n\r\n        this.update();\r\n    }\r\n\r\n    /**\r\n     * Associates an effect to this uniform buffer\r\n     * @param effect Define the effect to associate the buffer to\r\n     * @param name Name of the uniform block in the shader.\r\n     */\r\n    public bindToEffect(effect: Effect, name: string): void {\r\n        this._currentEffect = effect;\r\n        this._currentEffectName = name;\r\n    }\r\n\r\n    /**\r\n     * Binds the current (GPU) buffer to the effect\r\n     */\r\n    public bindUniformBuffer(): void {\r\n        if (!this._noUBO && this._buffer && this._currentEffect) {\r\n            this._currentEffect.bindUniformBuffer(this._buffer, this._currentEffectName);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Dissociates the current effect from this uniform buffer\r\n     */\r\n    public unbindEffect(): void {\r\n        this._currentEffect = undefined as any;\r\n        this._currentEffectName = undefined as any;\r\n    }\r\n\r\n    /**\r\n     * Sets the current state of the class (_bufferIndex, _buffer) to point to the data buffer passed in parameter if this buffer is one of the buffers handled by the class (meaning if it can be found in the _buffers array)\r\n     * This method is meant to be able to update a buffer at any time: just call setDataBuffer to set the class in the right state, call some updateXXX methods and then call udpate() => that will update the GPU buffer on the graphic card\r\n     * @param dataBuffer buffer to look for\r\n     * @returns true if the buffer has been found and the class internal state points to it, else false\r\n     */\r\n    public setDataBuffer(dataBuffer: DataBuffer): boolean {\r\n        if (!this._buffers) {\r\n            return this._buffer === dataBuffer;\r\n        }\r\n\r\n        for (let b = 0; b < this._buffers.length; ++b) {\r\n            const buffer = this._buffers[b];\r\n            if (buffer[0] === dataBuffer) {\r\n                this._bufferIndex = b;\r\n                this._buffer = dataBuffer;\r\n                this._createBufferOnWrite = false;\r\n                this._currentEffect = undefined as any;\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Disposes the uniform buffer.\r\n     */\r\n    public dispose(): void {\r\n        if (this._noUBO) {\r\n            return;\r\n        }\r\n\r\n        const uniformBuffers = this._engine._uniformBuffers;\r\n        const index = uniformBuffers.indexOf(this);\r\n\r\n        if (index !== -1) {\r\n            uniformBuffers[index] = uniformBuffers[uniformBuffers.length - 1];\r\n            uniformBuffers.pop();\r\n        }\r\n\r\n        if (this._engine._features.trackUbosInFrame && this._buffers) {\r\n            for (let i = 0; i < this._buffers.length; ++i) {\r\n                const buffer = this._buffers[i][0];\r\n                this._engine._releaseBuffer(buffer!);\r\n            }\r\n        } else if (this._buffer && this._engine._releaseBuffer(this._buffer)) {\r\n            this._buffer = null;\r\n        }\r\n    }\r\n}\r\n"]}