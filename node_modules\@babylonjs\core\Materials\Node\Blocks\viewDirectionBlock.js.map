{"version": 3, "file": "viewDirectionBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/viewDirectionBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAGvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAExD,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,iBAAiB;IACrD;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAE9C,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QACnF,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;QAEpF,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,OAAO,CAAC,CAAC;IACjF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEM,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,EAAE,CAAC,IAAI;QACnH,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE;YAClC,IAAI,mBAAmB,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,KAAK,wBAAwB,CAAC,cAAc,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5J,IAAI,CAAC,mBAAmB,EAAE;gBACtB,mBAAmB,GAAG,IAAI,UAAU,CAAC,gBAAgB,CAAC,CAAC;gBACvD,mBAAmB,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;aACjF;YACD,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC7D;IACL,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEhC,KAAK,CAAC,iBAAiB;YACnB,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,gBAAgB,IAAI,CAAC,cAAc,CAAC,sBAAsB,MAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,UAAU,CAAC;QAE7J,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,aAAa,CAAC,4BAA4B,EAAE,kBAAkB,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport type { NodeMaterial } from \"../nodeMaterial\";\r\nimport { NodeMaterialSystemValues } from \"../Enums/nodeMaterialSystemValues\";\r\nimport { InputBlock } from \"./Input/inputBlock\";\r\n/**\r\n * Block used to get the view direction\r\n */\r\nexport class ViewDirectionBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new ViewDirectionBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        this.registerInput(\"cameraPosition\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"ViewDirectionBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the world position component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the camera position component\r\n     */\r\n    public get cameraPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.cameraPosition.isConnected) {\r\n            let cameraPositionInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.CameraPosition && additionalFilteringInfo(b));\r\n\r\n            if (!cameraPositionInput) {\r\n                cameraPositionInput = new InputBlock(\"cameraPosition\");\r\n                cameraPositionInput.setAsSystemValue(NodeMaterialSystemValues.CameraPosition);\r\n            }\r\n            cameraPositionInput.output.connectTo(this.cameraPosition);\r\n        }\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const output = this._outputs[0];\r\n\r\n        state.compilationString +=\r\n            this._declareOutput(output, state) + ` = normalize(${this.cameraPosition.associatedVariableName} - ${this.worldPosition.associatedVariableName}.xyz);\\n`;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ViewDirectionBlock\", ViewDirectionBlock);\r\n"]}