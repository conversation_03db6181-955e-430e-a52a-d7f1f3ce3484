{"version": 3, "file": "WebXRMeshDetector.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRMeshDetector.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AACjF,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAG9D,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AACtD,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAE,YAAY,EAAE,gCAA4B;AACnD,OAAO,EAAE,MAAM,EAAE,6BAAyB;AAmF1C,IAAI,cAAc,GAAG,CAAC,CAAC;AAEvB;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,oBAAoB;IA2BvD,YACI,iBAAsC,EAC9B,WAAsC,EAAE;QAEhD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFjB,aAAQ,GAAR,QAAQ,CAAgC;QA5B5C,oBAAe,GAAkC,IAAI,GAAG,EAA4B,CAAC;QAa7F;;WAEG;QACI,0BAAqB,GAAiC,IAAI,UAAU,EAAE,CAAC;QAC9E;;WAEG;QACI,4BAAuB,GAAiC,IAAI,UAAU,EAAE,CAAC;QAChF;;WAEG;QACI,4BAAuB,GAAiC,IAAI,UAAU,EAAE,CAAC;QAO5E,IAAI,CAAC,mBAAmB,GAAG,gBAAgB,CAAC;QAC5C,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YAC9B,IAAI,CAAC,QAAQ,CAAC,wBAAwB,GAAG,IAAI,CAAC;SACjD;QACD,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE;YAChC,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;aAAM;YACH,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,EAAE;gBAChD,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAEM,MAAM;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,kCAAkC;QAClC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,yBAAyB,EAAE;YACjG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;SACnE;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,+BAA+B,EAAE;YAChD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAClC,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;IACzC,CAAC;IAES,UAAU,CAAC,KAAc;QAC/B,wBAAwB;QACxB,IAAI;YACA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;gBAC1B,OAAO;aACV;YAED,sCAAsC;YACtC,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,gBAAgB,EAAE,cAAc,CAAC;YACtF,IAAI,cAAc,EAAE;gBAChB,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAU,CAAC;gBACnC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,MAAM,EAAE,EAAE;oBAChD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;wBAC7B,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;qBACxB;gBACL,CAAC,CAAC,CAAC;gBACH,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBACxB,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACpD,IAAI,UAAU,EAAE;wBACZ,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;wBACzD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;qBACvC;gBACL,CAAC,CAAC,CAAC;gBAEH,yBAAyB;gBACzB,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBAC9B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;wBACnC,MAAM,iBAAiB,GAA8B;4BACjD,EAAE,EAAE,cAAc,EAAE;4BACpB,MAAM,EAAE,MAAM;yBACjB,CAAC;wBACF,MAAM,UAAU,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;wBACtF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;wBAC7C,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;qBAC1D;yBAAM;wBACH,WAAW;wBACX,IAAI,MAAM,CAAC,eAAe,KAAK,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE;4BACpE,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;4BACpD,IAAI,UAAU,EAAE;gCACZ,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;gCAC5D,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;6BAC5D;yBACJ;qBACJ;gBACL,CAAC,CAAC,CAAC;aACN;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SAC3B;IACL,CAAC;IAEO,KAAK;QACT,kCAAkC;QAClC,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;YACjC,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,yBAAyB,EAAE;gBAC1D,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;aAClE;YAED,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,kCAAkC,EAAE;gBACjH,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,kCAAkC,CAAC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;aAC7G;SACJ;IACL,CAAC;IAEO,2BAA2B,CAAC,MAAc,EAAE,IAA+B,EAAE,OAAgB;QACjG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;QACrD,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC;QAEtD,IAAI,IAAI,CAAC,QAAQ,CAAC,wBAAwB,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBACpD,IAAI,CAAC,SAAS,GAAG,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBACpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;oBAC1C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBACjC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACzC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;iBACjD;gBAED,IAAI,MAAM,CAAC,OAAO,EAAE;oBAChB,IAAI,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;wBAC/C,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;wBACpC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;wBAC5C,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;qBACpD;iBACJ;aACJ;iBAAM;gBACH,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;gBAC3B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;aACjC;YAED,8GAA8G;YAC9G,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;YAE9B,SAAS;YACT,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YACtF,IAAI,IAAI,EAAE;gBACN,MAAM,GAAG,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,MAAM,EAAE,CAAC;gBACtD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;gBACrD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,EAAE;oBACpD,GAAG,CAAC,4BAA4B,EAAE,CAAC;iBACtC;gBACD,IAAI,CAAC,oBAAoB,GAAG,GAAG,CAAC;gBAChC,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE;oBAC/B,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,CAAC;iBAC1E;aACJ;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;gBAC9B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;oBACZ,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;oBACnF,aAAa,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;oBACpD,aAAa,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;oBACzE,IAAI,IAAI,CAAC,OAAO,EAAE;wBACd,aAAa,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;qBACxE;yBAAM;wBACH,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;qBACrC;oBACD,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;oBACxD,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;iBAC7B;qBAAM;oBACH,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC;oBAChC,aAAa,CAAC,kBAAkB,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC5E,IAAI,IAAI,CAAC,OAAO,EAAE;wBACd,aAAa,CAAC,kBAAkB,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;qBAC3E;yBAAM;wBACH,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;qBACrC;oBACD,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBAC7C;gBACD,IAAI,CAAC,oBAAoB,EAAE,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,kBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC9G;SACJ;QAED,OAAyB,IAAI,CAAC;IAClC,CAAC;;AA7MD;;GAEG;AACoB,sBAAI,GAAG,gBAAgB,CAAC,cAAc,AAAlC,CAAmC;AAC9D;;;;GAIG;AACoB,yBAAO,GAAG,CAAC,AAAJ,CAAK;AAuMvC,oBAAoB,CAAC,eAAe,CAChC,iBAAiB,CAAC,IAAI,EACtB,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,iBAAiB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AAClE,CAAC,EACD,iBAAiB,CAAC,OAAO,EACzB,KAAK,CACR,CAAC", "sourcesContent": ["import { WebXRFeaturesManager, WebXRFeatureName } from \"../webXRFeaturesManager\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { Matrix, Quaternion } from \"../../Maths/math\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { Mesh } from \"../../Meshes/mesh\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * Options used in the mesh detector module\r\n */\r\nexport interface IWebXRMeshDetectorOptions {\r\n    /**\r\n     * The node to use to transform the local results to world coordinates\r\n     */\r\n    worldParentNode?: TransformNode;\r\n    /**\r\n     * If set to true a reference of the created meshes will be kept until the next session starts\r\n     * If not defined, meshes will be removed from the array when the feature is detached or the session ended.\r\n     */\r\n    doNotRemoveMeshesOnSessionEnded?: boolean;\r\n    /**\r\n     * Preferred detector configuration, not all preferred options will be supported by all platforms.\r\n     * Babylon native only!\r\n     */\r\n    preferredDetectorOptions?: XRGeometryDetectorOptions;\r\n    /**\r\n     * If set to true, WebXRMeshDetector will convert coordinate systems for meshes.\r\n     * If not defined, mesh conversions from right handed to left handed coordinate systems won't be conducted.\r\n     * Right handed mesh data will be available through IWebXRVertexData.xrMesh.\r\n     */\r\n    convertCoordinateSystems?: boolean;\r\n\r\n    /**\r\n     * If set to true, the feature will generate meshes for the detected data.\r\n     * Note that this might be time consuming, as the mesh's vertex data will be updated on every change.\r\n     * Setting this to true will also set convertCoordinateSystems to true.\r\n     * Note - the meshes will NOT be disposed automatically when the feature is detached or the session ended.\r\n     */\r\n    generateMeshes?: boolean;\r\n}\r\n\r\n/**\r\n * A babylon interface for a XR mesh's vertex data.\r\n */\r\nexport interface IWebXRVertexData {\r\n    /**\r\n     * A babylon-assigned ID for this mesh\r\n     */\r\n    id: number;\r\n    /**\r\n     * Data required for constructing a mesh in Babylon.js.\r\n     */\r\n    xrMesh: XRMesh;\r\n    /**\r\n     * The node to use to transform the local results to world coordinates.\r\n     * WorldParentNode will only exist if it was declared in the IWebXRMeshDetectorOptions.\r\n     */\r\n    worldParentNode?: TransformNode;\r\n    /**\r\n     * An array of vertex positions in babylon space. right/left hand system is taken into account.\r\n     * Positions will only be calculated if convertCoordinateSystems is set to true in the IWebXRMeshDetectorOptions.\r\n     */\r\n    positions?: Float32Array;\r\n    /**\r\n     * An array of indices in babylon space. Indices have a counterclockwise winding order.\r\n     * Indices will only be populated if convertCoordinateSystems is set to true in the IWebXRMeshDetectorOptions.\r\n     */\r\n    indices?: Uint32Array;\r\n    /**\r\n     * An array of vertex normals in babylon space. right/left hand system is taken into account.\r\n     * Normals will not be calculated if convertCoordinateSystems is undefined in the IWebXRMeshDetectorOptions.\r\n     * Different platforms may or may not support mesh normals when convertCoordinateSystems is set to true.\r\n     */\r\n    normals?: Float32Array;\r\n    /**\r\n     * A transformation matrix to apply on the mesh that will be built using the meshDefinition.\r\n     * Local vs. World are decided if worldParentNode was provided or not in the options when constructing the module.\r\n     * TransformationMatrix will only be calculated if convertCoordinateSystems is set to true in the IWebXRMeshDetectorOptions.\r\n     */\r\n    transformationMatrix?: Matrix;\r\n\r\n    /**\r\n     * If generateMeshes is set to true in the IWebXRMeshDetectorOptions, this will be the generated mesh.\r\n     * This mesh will be updated with the vertex data provided and not regenerated every time.\r\n     */\r\n    mesh?: Mesh;\r\n}\r\n\r\nlet meshIdProvider = 0;\r\n\r\n/**\r\n * The mesh detector is used to detect meshes in the real world when in AR\r\n */\r\nexport class WebXRMeshDetector extends WebXRAbstractFeature {\r\n    private _detectedMeshes: Map<XRMesh, IWebXRVertexData> = new Map<XRMesh, IWebXRVertexData>();\r\n\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.MESH_DETECTION;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * Observers registered here will be executed when a new mesh was added to the session\r\n     */\r\n    public onMeshAddedObservable: Observable<IWebXRVertexData> = new Observable();\r\n    /**\r\n     * Observers registered here will be executed when a mesh is no longer detected in the session\r\n     */\r\n    public onMeshRemovedObservable: Observable<IWebXRVertexData> = new Observable();\r\n    /**\r\n     * Observers registered here will be executed when an existing mesh updates\r\n     */\r\n    public onMeshUpdatedObservable: Observable<IWebXRVertexData> = new Observable();\r\n\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        private _options: IWebXRMeshDetectorOptions = {}\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"mesh-detection\";\r\n        if (this._options.generateMeshes) {\r\n            this._options.convertCoordinateSystems = true;\r\n        }\r\n        if (this._xrSessionManager.session) {\r\n            this._init();\r\n        } else {\r\n            this._xrSessionManager.onXRSessionInit.addOnce(() => {\r\n                this._init();\r\n            });\r\n        }\r\n    }\r\n\r\n    public detach(): boolean {\r\n        if (!super.detach()) {\r\n            return false;\r\n        }\r\n\r\n        // Only supported by BabylonNative\r\n        if (!!this._xrSessionManager.isNative && !!this._xrSessionManager.session.trySetMeshDetectorEnabled) {\r\n            this._xrSessionManager.session.trySetMeshDetectorEnabled(false);\r\n        }\r\n\r\n        if (!this._options.doNotRemoveMeshesOnSessionEnded) {\r\n            this._detectedMeshes.forEach((mesh) => {\r\n                this.onMeshRemovedObservable.notifyObservers(mesh);\r\n            });\r\n\r\n            this._detectedMeshes.clear();\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public dispose(): void {\r\n        super.dispose();\r\n        this.onMeshAddedObservable.clear();\r\n        this.onMeshRemovedObservable.clear();\r\n        this.onMeshUpdatedObservable.clear();\r\n    }\r\n\r\n    protected _onXRFrame(frame: XRFrame) {\r\n        // TODO remove try catch\r\n        try {\r\n            if (!this.attached || !frame) {\r\n                return;\r\n            }\r\n\r\n            // babylon native XR and webxr support\r\n            const detectedMeshes = frame.detectedMeshes || frame.worldInformation?.detectedMeshes;\r\n            if (detectedMeshes) {\r\n                const toRemove = new Set<XRMesh>();\r\n                this._detectedMeshes.forEach((vertexData, xrMesh) => {\r\n                    if (!detectedMeshes.has(xrMesh)) {\r\n                        toRemove.add(xrMesh);\r\n                    }\r\n                });\r\n                toRemove.forEach((xrMesh) => {\r\n                    const vertexData = this._detectedMeshes.get(xrMesh);\r\n                    if (vertexData) {\r\n                        this.onMeshRemovedObservable.notifyObservers(vertexData);\r\n                        this._detectedMeshes.delete(xrMesh);\r\n                    }\r\n                });\r\n\r\n                // now check for new ones\r\n                detectedMeshes.forEach((xrMesh) => {\r\n                    if (!this._detectedMeshes.has(xrMesh)) {\r\n                        const partialVertexData: Partial<IWebXRVertexData> = {\r\n                            id: meshIdProvider++,\r\n                            xrMesh: xrMesh,\r\n                        };\r\n                        const vertexData = this._updateVertexDataWithXRMesh(xrMesh, partialVertexData, frame);\r\n                        this._detectedMeshes.set(xrMesh, vertexData);\r\n                        this.onMeshAddedObservable.notifyObservers(vertexData);\r\n                    } else {\r\n                        // updated?\r\n                        if (xrMesh.lastChangedTime === this._xrSessionManager.currentTimestamp) {\r\n                            const vertexData = this._detectedMeshes.get(xrMesh);\r\n                            if (vertexData) {\r\n                                this._updateVertexDataWithXRMesh(xrMesh, vertexData, frame);\r\n                                this.onMeshUpdatedObservable.notifyObservers(vertexData);\r\n                            }\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n        } catch (error) {\r\n            Logger.Log(error.stack);\r\n        }\r\n    }\r\n\r\n    private _init() {\r\n        // Only supported by BabylonNative\r\n        if (this._xrSessionManager.isNative) {\r\n            if (this._xrSessionManager.session.trySetMeshDetectorEnabled) {\r\n                this._xrSessionManager.session.trySetMeshDetectorEnabled(true);\r\n            }\r\n\r\n            if (!!this._options.preferredDetectorOptions && !!this._xrSessionManager.session.trySetPreferredMeshDetectorOptions) {\r\n                this._xrSessionManager.session.trySetPreferredMeshDetectorOptions(this._options.preferredDetectorOptions);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _updateVertexDataWithXRMesh(xrMesh: XRMesh, mesh: Partial<IWebXRVertexData>, xrFrame: XRFrame): IWebXRVertexData {\r\n        mesh.xrMesh = xrMesh;\r\n        mesh.worldParentNode = this._options.worldParentNode;\r\n        const positions = xrMesh.vertices || xrMesh.positions;\r\n\r\n        if (this._options.convertCoordinateSystems) {\r\n            if (!this._xrSessionManager.scene.useRightHandedSystem) {\r\n                mesh.positions = new Float32Array(positions.length);\r\n                for (let i = 0; i < positions.length; i += 3) {\r\n                    mesh.positions[i] = positions[i];\r\n                    mesh.positions[i + 1] = positions[i + 1];\r\n                    mesh.positions[i + 2] = -1 * positions[i + 2];\r\n                }\r\n\r\n                if (xrMesh.normals) {\r\n                    mesh.normals = new Float32Array(xrMesh.normals.length);\r\n                    for (let i = 0; i < xrMesh.normals.length; i += 3) {\r\n                        mesh.normals[i] = xrMesh.normals[i];\r\n                        mesh.normals[i + 1] = xrMesh.normals[i + 1];\r\n                        mesh.normals[i + 2] = -1 * xrMesh.normals[i + 2];\r\n                    }\r\n                }\r\n            } else {\r\n                mesh.positions = positions;\r\n                mesh.normals = xrMesh.normals;\r\n            }\r\n\r\n            // WebXR should provide indices in a counterclockwise winding order regardless of coordinate system handedness\r\n            mesh.indices = xrMesh.indices;\r\n\r\n            // matrix\r\n            const pose = xrFrame.getPose(xrMesh.meshSpace, this._xrSessionManager.referenceSpace);\r\n            if (pose) {\r\n                const mat = mesh.transformationMatrix || new Matrix();\r\n                Matrix.FromArrayToRef(pose.transform.matrix, 0, mat);\r\n                if (!this._xrSessionManager.scene.useRightHandedSystem) {\r\n                    mat.toggleModelMatrixHandInPlace();\r\n                }\r\n                mesh.transformationMatrix = mat;\r\n                if (this._options.worldParentNode) {\r\n                    mat.multiplyToRef(this._options.worldParentNode.getWorldMatrix(), mat);\r\n                }\r\n            }\r\n\r\n            if (this._options.generateMeshes) {\r\n                if (!mesh.mesh) {\r\n                    const generatedMesh = new Mesh(\"xr mesh \" + mesh.id, this._xrSessionManager.scene);\r\n                    generatedMesh.rotationQuaternion = new Quaternion();\r\n                    generatedMesh.setVerticesData(VertexBuffer.PositionKind, mesh.positions);\r\n                    if (mesh.normals) {\r\n                        generatedMesh.setVerticesData(VertexBuffer.NormalKind, mesh.normals);\r\n                    } else {\r\n                        generatedMesh.createNormals(true);\r\n                    }\r\n                    generatedMesh.setIndices(mesh.indices, undefined, true);\r\n                    mesh.mesh = generatedMesh;\r\n                } else {\r\n                    const generatedMesh = mesh.mesh;\r\n                    generatedMesh.updateVerticesData(VertexBuffer.PositionKind, mesh.positions);\r\n                    if (mesh.normals) {\r\n                        generatedMesh.updateVerticesData(VertexBuffer.NormalKind, mesh.normals);\r\n                    } else {\r\n                        generatedMesh.createNormals(true);\r\n                    }\r\n                    generatedMesh.updateIndices(mesh.indices);\r\n                }\r\n                mesh.transformationMatrix?.decompose(mesh.mesh.scaling, mesh.mesh.rotationQuaternion!, mesh.mesh.position);\r\n            }\r\n        }\r\n\r\n        return <IWebXRVertexData>mesh;\r\n    }\r\n}\r\n\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRMeshDetector.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRMeshDetector(xrSessionManager, options);\r\n    },\r\n    WebXRMeshDetector.Version,\r\n    false\r\n);\r\n"]}