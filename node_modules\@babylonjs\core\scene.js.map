{"version": 3, "file": "scene.js", "sourceRoot": "", "sources": ["../../../dev/core/src/scene.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,KAAK,EAAE,MAAM,cAAc,CAAC;AAErC,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAErD,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAE/C,OAAO,EAAE,qBAAqB,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AACtE,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAC3D,OAAO,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAEnC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAElE,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,4BAA4B,EAAE,MAAM,0CAA0C,CAAC;AACxF,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAIvD,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAC;AAGxE,OAAO,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAC;AAiBhE,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAChD,OAAO,EAAE,mBAAmB,EAAE,MAAM,sBAAsB,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AAEpD,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAE9C,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAEjD,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAEpD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAE7D,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAGnE,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AA4BlD,OAAO,EAAE,2BAA2B,EAAE,MAAM,sCAAsC,CAAC;AACnF,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAoCvC;;GAEG;AACH,MAAM,CAAN,IAAY,wBAOX;AAPD,WAAY,wBAAwB;IAChC,yGAAyG;IACzG,mGAAkB,CAAA;IAClB,yGAAyG;IACzG,uFAAY,CAAA;IACZ,uCAAuC;IACvC,mFAAU,CAAA;AACd,CAAC,EAPW,wBAAwB,KAAxB,wBAAwB,QAOnC;AAED;;;GAGG;AACH,MAAM,OAAO,KAAM,SAAQ,aAAa;IAqBpC,uDAAuD;IACvD;;;;OAIG;IACI,MAAM,CAAC,sBAAsB,CAAC,KAAY;QAC7C,MAAM,WAAW,CAAC,kBAAkB,CAAC,CAAC;IAC1C,CAAC;IAED,uDAAuD;IACvD;;;OAGG;IACI,MAAM,CAAC,2BAA2B;QACrC,MAAM,WAAW,CAAC,6BAA6B,CAAC,CAAC;IACrD,CAAC;IA2CD;;;;OAIG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IACD;;;;OAIG;IACH,IAAW,kBAAkB,CAAC,KAA4B;QACtD,IAAI,IAAI,CAAC,mBAAmB,KAAK,KAAK,EAAE;YACpC,OAAO;SACV;QAED,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IACtE,CAAC;IAYD;;;;;;;OAOG;IACH,IAAW,4BAA4B;QACnC,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAQD;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAK;QAChC,IAAI,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE;YACrC,OAAO;SACV;QAED,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAElC,QAAQ,KAAK,EAAE;YACX,KAAK,wBAAwB,CAAC,kBAAkB;gBAC5C,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;gBACjC,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,GAAG,KAAK,CAAC;gBAC1D,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;gBACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,MAAM;YACV,KAAK,wBAAwB,CAAC,YAAY;gBACtC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;gBACjC,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,GAAG,KAAK,CAAC;gBAC1D,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;gBACnC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,MAAM;YACV,KAAK,wBAAwB,CAAC,UAAU;gBACpC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAChC,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,GAAG,IAAI,CAAC;gBACzD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;gBACnC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,MAAM;SACb;QAED,IAAI,CAAC,2CAA2C,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAC5E,CAAC;IAGD;;OAEG;IACH,IAAW,cAAc,CAAC,KAAc;QACpC,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE;YAChC,OAAO;SACV;QACD,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;IACnE,CAAC;IACD,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAGD;;OAEG;IACH,IAAW,mBAAmB,CAAC,KAAc;QACzC,IAAI,IAAI,CAAC,oBAAoB,KAAK,KAAK,EAAE;YACrC,OAAO;SACV;QACD,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACtC,CAAC;IACD,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAGD;;OAEG;IACH,IAAW,gBAAgB,CAAC,KAAc;QACtC,IAAI,IAAI,CAAC,iBAAiB,KAAK,KAAK,EAAE;YAClC,OAAO;SACV;QACD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;IACnE,CAAC;IACD,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAuCD;;OAEG;IACH,IAAW,2BAA2B;QAClC,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED,IAAW,2BAA2B,CAAC,KAA4C;QAC/E,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;IAC9C,CAAC;IAgED,kEAAkE;IAClE,IAAW,SAAS,CAAC,QAAoB;QACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC5D;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrE,CAAC;IAQD,iEAAiE;IACjE,IAAW,YAAY,CAAC,QAA8B;QAClD,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACtE;QACD,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SAC9E;IACL,CAAC;IAcD,gEAAgE;IAChE,IAAW,WAAW,CAAC,QAA8B;QACjD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;SACpE;QAED,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;SAC5E;IACL,CAAC;IAiCD,8DAA8D;IAC9D,IAAW,kBAAkB,CAAC,QAAoB;QAC9C,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACpC,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;SAClF;QAED,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC3F,CAAC;IASD,6DAA6D;IAC7D,IAAW,iBAAiB,CAAC,QAAoB;QAC7C,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAChF;QACD,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACzF,CAAC;IA6LD;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,CAAC;IAClE,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAK;QACjC,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;IAChE,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAK;QAC/B,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,CAAC;IAClE,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAK;QACjC,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,CAAC;IAClE,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAK;QACjC,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;IAChE,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAK;QAC/B,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,CAAC;IAClE,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAK;QACjC,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC,4BAA4B,CAAC,sBAAsB,CAAC;IACpE,CAAC;IAED,IAAW,sBAAsB,CAAC,KAAK;QACnC,IAAI,CAAC,4BAA4B,CAAC,sBAAsB,GAAG,KAAK,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC,4BAA4B,CAAC,sBAAsB,CAAC;IACpE,CAAC;IAED,IAAW,sBAAsB,CAAC,KAAK;QACnC,IAAI,CAAC,4BAA4B,CAAC,sBAAsB,GAAG,KAAK,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,CAAC;IAClE,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAK;QACjC,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACnE,CAAC;IAqCD;;OAEG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,qBAAqB;QACnC,OAAO,YAAY,CAAC,qBAAqB,CAAC;IAC9C,CAAC;IAEM,MAAM,KAAK,qBAAqB,CAAC,KAAa;QACjD,YAAY,CAAC,qBAAqB,GAAG,KAAK,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,cAAc;QAC5B,OAAO,YAAY,CAAC,cAAc,CAAC;IACvC,CAAC;IAEM,MAAM,KAAK,cAAc,CAAC,KAAa;QAC1C,YAAY,CAAC,cAAc,GAAG,KAAK,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,gBAAgB;QAC9B,OAAO,YAAY,CAAC,gBAAgB,CAAC;IACzC,CAAC;IAEM,MAAM,KAAK,gBAAgB,CAAC,KAAa;QAC5C,YAAY,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAC1C,CAAC;IAED,wGAAwG;IACjG,MAAM,KAAK,wBAAwB;QACtC,OAAO,YAAY,CAAC,wBAAwB,CAAC;IACjD,CAAC;IAEM,MAAM,KAAK,wBAAwB,CAAC,KAAc;QACrD,YAAY,CAAC,wBAAwB,GAAG,KAAK,CAAC;IAClD,CAAC;IAED;;;;;;OAMG;IACI,eAAe,CAAC,MAAwB,EAAE,YAAY,GAAG,cAAc,EAAE,SAAS,GAAG,KAAK;QAC7F,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,YAAa,CAAC,cAAc,CAAC;QAE1K,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,KAAK,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,CAAC;QAE1F,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9F,IAAI,MAAM,EAAE;YACR,IAAI,SAAS,EAAE;gBACX,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7G;iBAAM;gBACH,MAAM,CAAC,UAAU,CAAC,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aAC1D;SACJ;QAED,OAAO,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;QAE7F,GAAG,CAAC,MAAM,EAAE,CAAC;QAEb,OAAO,GAAG,CAAC;IACf,CAAC;IAsBD;;OAEG;IACH,IAAW,oBAAoB,CAAC,KAAc;QAC1C,IAAI,IAAI,CAAC,qBAAqB,KAAK,KAAK,EAAE;YACtC,OAAO;SACV;QACD,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;IACnE,CAAC;IACD,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAOD;;;;OAIG;IACI,SAAS,CAAC,SAAiB;QAC9B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAKD;;;;OAIG;IACH,IAAW,UAAU,CAAC,KAAc;QAChC,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;YAC5B,OAAO;SACV;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;IACnE,CAAC;IACD,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAGD;;;;;;;;;OASG;IACH,IAAW,OAAO,CAAC,KAAa;QAC5B,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;YACzB,OAAO;SACV;QACD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;IACnE,CAAC;IACD,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IA2BD;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC;IAC5E,CAAC;IASD;;OAEG;IACH,IAAW,cAAc,CAAC,KAAc;QACpC,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE;YAChC,OAAO;SACV;QACD,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;IACpE,CAAC;IACD,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAGD;;OAEG;IACH,IAAW,aAAa,CAAC,KAAc;QACnC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;YAC/B,OAAO;SACV;QACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;IACpE,CAAC;IAED,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAKD,qDAAqD;IACrD,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,OAA2B;QAChD,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;SACvC;QAED,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,uBAAuB,GAAG,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE;gBACvD,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;IAClC,CAAC;IAID,6CAA6C;IAC7C,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,YAAY,CAAC,KAAuB;QAC3C,IAAI,KAAK,KAAK,IAAI,CAAC,aAAa,EAAE;YAC9B,OAAO;SACV;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAID,uEAAuE;IACvE,IAAW,eAAe;QACtB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;SAC9D;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,uEAAuE;IACvE,IAAW,eAAe,CAAC,KAAe;QACtC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAClC,CAAC;IAID;;OAEG;IACH,IAAW,eAAe,CAAC,KAAc;QACrC,IAAI,IAAI,CAAC,gBAAgB,KAAK,KAAK,EAAE;YACjC,OAAO;SACV;QACD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IACtE,CAAC;IAED,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAsBD;;OAEG;IACH,IAAW,gBAAgB,CAAC,KAAc;QACtC,IAAI,IAAI,CAAC,iBAAiB,KAAK,KAAK,EAAE;YAClC,OAAO;SACV;QACD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;IACzE,CAAC;IAED,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAiBD,gBAAgB;IAChB,IAAW,oBAAoB;QAC3B,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,2BAA2B,EAAE,CAAC;YACjE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzC;QAED,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAyID;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAiBD;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAiCD;;OAEG;IACK,4BAA4B;QAChC,qEAAqE;QACrE,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;YACtC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC/C,SAAS,CAAC,QAAQ,EAAE,CAAC;aACxB;YACD,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;SACxC;IACL,CAAC;IAED;;;;;;OAMG;IACI,aAAa,CAAC,SAA0B;QAC3C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE1C,MAAM,qBAAqB,GAAG,SAAgB,CAAC;QAC/C,IAAI,qBAAqB,CAAC,gBAAgB,IAAI,qBAAqB,CAAC,SAAS,EAAE;YAC3E,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SAC5D;IACL,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,IAAY;QAC7B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACtC,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,EAAE;gBACzB,OAAO,SAAS,CAAC;aACpB;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IA+HD;;;;OAIG;IACH,YAAY,MAAc,EAAE,OAAsB;QAC9C,KAAK,EAAE,CAAC;QAj8CZ,UAAU;QAEV,gBAAgB;QACT,kBAAa,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QAE9C,oIAAoI;QAC7H,2BAAsB,GAAqB,IAAI,CAAC;QAEvD,gBAAgB;QACA,aAAQ,GAAG,IAAI,CAAC;QAEhC,gBAAgB;QACT,2BAAsB,GAAG,KAAK,CAAC;QAEtC;;WAEG;QACI,cAAS,GAAG,IAAI,CAAC;QACxB;;WAEG;QACI,6BAAwB,GAAG,IAAI,CAAC;QACvC;;WAEG;QACI,eAAU,GAAW,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC3D;;WAEG;QACI,iBAAY,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAkC1C;;;;;WAKG;QACI,yBAAoB,GAAW,CAAC,CAAC;QAgBhC,yBAAoB,GAAG,wBAAwB,CAAC,kBAAkB,CAAC;QAE3E;;WAEG;QACI,gDAA2C,GAAG,IAAI,UAAU,EAA4B,CAAC;QAuCxF,oBAAe,GAAG,KAAK,CAAC;QAexB,yBAAoB,GAAG,KAAK,CAAC;QAc7B,sBAAiB,GAAG,KAAK,CAAC;QA6ClC;;WAEG;QACI,sBAAiB,GAAG,IAAI,CAAC;QAExB,iCAA4B,GAA0C,IAAI,CAAC;QAanF;;;WAGG;QACI,kCAA6B,GAAG,KAAK,CAAC;QAC7C;;;WAGG;QACI,qCAAgC,GAAG,KAAK,CAAC;QAEhD;;WAEG;QACI,gBAAW,GAAG,SAAS,CAAC;QAC/B;;WAEG;QACI,kBAAa,GAAW,EAAE,CAAC;QAClC;;WAEG;QACI,uBAAkB,GAAG,KAAK,CAAC;QAClC;;;WAGG;QACI,gCAA2B,GAAG,IAAI,CAAC;QAE1C;;;WAGG;QACI,8BAAyB,GAAG,IAAI,CAAC;QAExC,WAAW;QACX;;WAEG;QACI,aAAQ,GAAQ,IAAI,CAAC;QAE5B;;WAEG;QACI,sBAAiB,GAAQ,IAAI,CAAC;QAOrC;;WAEG;QACI,wCAAmC,GAAa,EAAE,CAAC;QAE1D;;WAEG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAS,CAAC;QAE7C,uBAAkB,GAA8B,IAAI,CAAC;QAS7D;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAS,CAAC;QAElD,4BAAuB,GAA8B,IAAI,CAAC;QAWlE;;WAEG;QACI,4BAAuB,GAAG,IAAI,UAAU,EAAS,CAAC;QAEzD;;;WAGG;QACI,kCAA6B,GAAG,IAAI,UAAU,EAAU,CAAC;QAExD,2BAAsB,GAA8B,IAAI,CAAC;QAYjE;;WAEG;QACI,iCAA4B,GAAG,IAAI,UAAU,EAAS,CAAC;QAE9D;;WAEG;QACI,gCAA2B,GAAG,IAAI,UAAU,EAAS,CAAC;QAE7D;;WAEG;QACI,gCAA2B,GAAG,IAAI,UAAU,EAAS,CAAC;QAE7D;;WAEG;QACI,+BAA0B,GAAG,IAAI,UAAU,EAAS,CAAC;QAE5D;;WAEG;QACI,sBAAiB,GAAG,IAAI,UAAU,EAAS,CAAC;QAEnD;;WAEG;QACI,mCAA8B,GAAG,IAAI,UAAU,EAAU,CAAC;QAEzD,kCAA6B,GAA+B,IAAI,CAAC;QAUzE;;;WAGG;QACI,kCAA6B,GAAG,IAAI,UAAU,EAAU,CAAC;QAExD,iCAA4B,GAA+B,IAAI,CAAC;QASxE;;WAEG;QACI,6CAAwC,GAAG,IAAI,UAAU,EAAS,CAAC;QAE1E;;WAEG;QACI,4CAAuC,GAAG,IAAI,UAAU,EAAS,CAAC;QAEzE;;;WAGG;QACI,yCAAoC,GAAG,IAAI,UAAU,EAAS,CAAC;QAEtE;;;WAGG;QACI,wCAAmC,GAAG,IAAI,UAAU,EAAS,CAAC;QAErE;;WAEG;QACI,2BAAsB,GAAG,IAAI,UAAU,EAAS,CAAC;QAExD;;WAEG;QACI,+BAA0B,GAAG,IAAI,UAAU,EAAU,CAAC;QAE7D;;WAEG;QACI,8BAAyB,GAAG,IAAI,UAAU,EAAU,CAAC;QAE5D;;WAEG;QACI,8BAAyB,GAAG,IAAI,UAAU,EAAS,CAAC;QAE3D;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAS,CAAC;QAE1D;;WAEG;QACI,iCAA4B,GAAG,IAAI,UAAU,EAAY,CAAC;QAEjE;;WAEG;QACI,gCAA2B,GAAG,IAAI,UAAU,EAAY,CAAC;QAEhE;;WAEG;QACI,sCAAiC,GAAG,IAAI,UAAU,EAAiB,CAAC;QAE3E;;WAEG;QACI,qCAAgC,GAAG,IAAI,UAAU,EAAiB,CAAC;QAE1E;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAgB,CAAC;QAEjE;;WAEG;QACI,4BAAuB,GAAG,IAAI,UAAU,EAAgB,CAAC;QAEhE;;WAEG;QACI,iCAA4B,GAAG,IAAI,UAAU,EAAY,CAAC;QAEjE;;WAEG;QACI,gCAA2B,GAAG,IAAI,UAAU,EAAY,CAAC;QAEhE;;WAEG;QACI,iCAA4B,GAAG,IAAI,UAAU,EAAY,CAAC;QAEjE;;WAEG;QACI,sCAAiC,GAAG,IAAI,UAAU,EAAiB,CAAC;QAE3E;;WAEG;QACI,gCAA2B,GAAG,IAAI,UAAU,EAAY,CAAC;QAEhE;;WAEG;QACI,qCAAgC,GAAG,IAAI,UAAU,EAAiB,CAAC;QAE1E;;WAEG;QACI,gCAA2B,GAAG,IAAI,UAAU,EAAe,CAAC;QAEnE;;WAEG;QACI,+BAA0B,GAAG,IAAI,UAAU,EAAe,CAAC;QAElE;;;WAGG;QACI,0CAAqC,GAAG,IAAI,UAAU,EAAS,CAAC;QAEvE;;;WAGG;QACI,yCAAoC,GAAG,IAAI,UAAU,EAAS,CAAC;QAEtE;;WAEG;QACI,2BAAsB,GAAG,IAAI,UAAU,EAAS,CAAC;QAExD;;WAEG;QACI,0BAAqB,GAAG,IAAI,UAAU,EAAS,CAAC;QAEvD;;WAEG;QACI,0BAAqB,GAAG,IAAI,UAAU,EAAS,CAAC;QAEvD;;WAEG;QACI,2BAAsB,GAAG,IAAI,UAAU,EAAS,CAAC;QAExD;;;;WAIG;QACI,qCAAgC,GAAG,IAAI,UAAU,EAAsB,CAAC;QAE/E;;;;WAIG;QACI,oCAA+B,GAAG,IAAI,UAAU,EAAsB,CAAC;QAE9E;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAgB,CAAC;QAEjE;;WAEG;QACI,sCAAiC,GAAG,IAAI,UAAU,EAAS,CAAC;QAQnE,aAAa;QAEb,gBAAgB;QACT,wCAAmC,GAAG,IAAI,qBAAqB,CAAM,GAAG,CAAC,CAAC;QAEjF,WAAW;QACH,iCAA4B,GAAG,IAAI,2BAA2B,EAAE,CAAC;QA6HzE;;;WAGG;QACI,2BAAsB,GAAG,IAAI,UAAU,EAAkB,CAAC;QAEjE;;WAEG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAe,CAAC;QA8F3D,WAAW;QAEX;;;WAGG;QACI,4BAAuB,GAAG,IAAI,UAAU,EAAmB,CAAC;QAEnE;;WAEG;QACI,yBAAoB,GAAG,IAAI,UAAU,EAAgB,CAAC;QAE7D,qBAAqB;QAEb,0BAAqB,GAAG,KAAK,CAAC;QAetC,yBAAyB;QACjB,qBAAgB,GAAW,CAAC,CAAC;QAC7B,mBAAc,GAAW,CAAC,CAAC;QAC3B,yBAAoB,GAAW,CAAC,CAAC;QA6BzC,MAAM;QAEE,gBAAW,GAAG,IAAI,CAAC;QAiBnB,aAAQ,GAAG,KAAK,CAAC,YAAY,CAAC;QAsBtC;;;;WAIG;QACI,aAAQ,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5C;;;;WAIG;QACI,eAAU,GAAG,GAAG,CAAC;QACxB;;;;WAIG;QACI,aAAQ,GAAG,CAAC,CAAC;QACpB;;;;WAIG;QACI,WAAM,GAAG,MAAM,CAAC;QASvB;;WAEG;QACI,+BAA0B,GAAG,KAAK,CAAC;QAE1C,SAAS;QACD,oBAAe,GAAG,IAAI,CAAC;QAevB,mBAAc,GAAG,IAAI,CAAC;QAiBtB,4BAAuB,GAAyB,IAAI,CAAC;QAsD7D,WAAW;QACH,qBAAgB,GAAG,IAAI,CAAC;QAgBhC,UAAU;QACV;;WAEG;QACI,mBAAc,GAAG,IAAI,CAAC;QAE7B,YAAY;QACZ;;WAEG;QACI,qBAAgB,GAAG,IAAI,CAAC;QAE/B,UAAU;QACV;;WAEG;QACI,mBAAc,GAAG,IAAI,CAAC;QAE7B,YAAY;QACJ,sBAAiB,GAAG,IAAI,CAAC;QAgBjC,cAAc;QACd;;WAEG;QACI,sBAAiB,GAAG,IAAI,CAAC;QAEhC,aAAa;QACb;;;WAGG;QACI,sBAAiB,GAAG,IAAI,CAAC;QAchC;;;WAGG;QACI,YAAO,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAE3C,gBAAgB;QAChB;;WAEG;QACI,yBAAoB,GAAG,IAAI,CAAC;QAMnC,yBAAyB;QACzB;;WAEG;QACI,yBAAoB,GAAG,IAAI,CAAC;QACnC;;;WAGG;QACI,0BAAqB,GAAG,KAAK,CAAC;QACrC;;WAEG;QACI,wBAAmB,GAA0B,EAAE,CAAC;QAQvD;;WAEG;QACI,wBAAmB,GAAa,EAAE,CAAC;QAE1C,SAAS;QACT;;WAEG;QACI,kBAAa,GAAG,IAAI,CAAC;QAepB,4BAAuB,GAAG,IAAI,qBAAqB,CAAe,GAAG,CAAC,CAAC;QAE/E,sBAAsB;QACtB;;WAEG;QACI,8BAAyB,GAAG,IAAI,CAAC;QAKxC,uBAAuB;QACf,mBAAc,GAAG,IAAI,WAAW,EAAE,CAAC;QAC3C,gBAAgB;QACT,mBAAc,GAAG,IAAI,WAAW,EAAE,CAAC;QAC1C,gBAAgB;QACT,qBAAgB,GAAG,IAAI,WAAW,EAAE,CAAC;QAC5C,gBAAgB;QACT,iBAAY,GAAG,IAAI,WAAW,EAAE,CAAC;QAOxC,gBAAgB;QACT,mBAAc,GAAW,CAAC,CAAC;QAElC;;;WAGG;QACI,uBAAkB,GAAW,CAAC,CAAC;QAS9B,cAAS,GAAG,CAAC,CAAC;QACd,aAAQ,GAAG,CAAC,CAAC;QACb,+BAA0B,GAA4C,IAAI,CAAC;QAC3E,2BAAsB,GAAG,KAAK,CAAC;QAC/B,+BAA0B,GAAG,KAAK,CAAC;QAEnC,oBAAe,GAAG,CAAC,CAAC,CAAC;QACrB,0BAAqB,GAAG,CAAC,CAAC,CAAC;QAEnC,gBAAgB;QACT,kBAAa,GAAG,IAAI,KAAK,CAAwB,GAAG,CAAC,CAAC;QACrD,oBAAe,GAAG,IAAI,KAAK,EAAgB,CAAC;QAEpD,gBAAgB;QACT,iBAAY,GAAG,IAAI,KAAK,EAAE,CAAC;QAC1B,gBAAW,GAAG,KAAK,CAAC;QAE5B;;;WAGG;QACI,uCAAkC,GAAY,KAAK,CAAC;QACnD,kBAAa,GAAG,IAAI,UAAU,CAAe,GAAG,CAAC,CAAC;QAClD,wBAAmB,GAAG,IAAI,UAAU,CAAW,GAAG,CAAC,CAAC;QACpD,mBAAc,GAAG,IAAI,qBAAqB,CAAsB,GAAG,CAAC,CAAC;QACrE,4BAAuB,GAAG,IAAI,qBAAqB,CAAsB,GAAG,CAAC,CAAC;QACtF,gBAAgB;QACT,2BAAsB,GAAG,IAAI,UAAU,CAAkB,GAAG,CAAC,CAAC;QAC7D,qBAAgB,GAAG,IAAI,qBAAqB,CAAW,EAAE,CAAC,CAAC;QAC3D,2BAAsB,GAAG,IAAI,qBAAqB,CAAO,EAAE,CAAC,CAAC;QAWrE,gBAAgB;QACT,uBAAkB,GAAG,IAAI,KAAK,EAAc,CAAC;QAE5C,qBAAgB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAmBzC;;;WAGG;QACI,wBAAmB,GAAG,KAAK,CAAC;QAUnC;;;WAGG;QACI,gBAAW,GAAsB,EAAE,CAAC;QAE3C;;;WAGG;QACI,4BAAuB,GAAkC,EAAE,CAAC;QAEnE;;WAEG;QACK,yBAAoB,GAAsB,EAAE,CAAC;QA+CrD;;;WAGG;QACI,6BAAwB,GAAG,KAAK,CAAC,MAAM,EAAqB,CAAC;QACpE;;;WAGG;QACI,sBAAiB,GAAG,KAAK,CAAC,MAAM,EAAqB,CAAC;QAC7D;;;WAGG;QACI,kCAA6B,GAAG,KAAK,CAAC,MAAM,EAA2B,CAAC;QAC/E;;;WAGG;QACI,8BAAyB,GAAG,KAAK,CAAC,MAAM,EAA4B,CAAC;QAC5E;;;WAGG;QACI,0CAAqC,GAAG,KAAK,CAAC,MAAM,EAA4B,CAAC;QACxF;;;WAGG;QACI,yBAAoB,GAAG,KAAK,CAAC,MAAM,EAAmB,CAAC;QAC9D;;;WAGG;QACI,mCAA8B,GAAG,KAAK,CAAC,MAAM,EAAqB,CAAC;QAC1E;;;WAGG;QACI,0BAAqB,GAAG,KAAK,CAAC,MAAM,EAA8B,CAAC;QAC1E;;;WAGG;QACI,wBAAmB,GAAG,KAAK,CAAC,MAAM,EAA4B,CAAC;QACtE;;;WAGG;QACI,iCAA4B,GAAG,KAAK,CAAC,MAAM,EAAgC,CAAC;QACnF;;;WAGG;QACI,2BAAsB,GAAG,KAAK,CAAC,MAAM,EAAqB,CAAC;QAClE;;;WAGG;QACI,iCAA4B,GAAG,KAAK,CAAC,MAAM,EAA2B,CAAC;QAC9E;;;WAGG;QACI,mCAA8B,GAAG,KAAK,CAAC,MAAM,EAA6B,CAAC;QAClF;;;WAGG;QACI,8BAAyB,GAAG,KAAK,CAAC,MAAM,EAA4B,CAAC;QAC5E;;;WAGG;QACI,6BAAwB,GAAG,KAAK,CAAC,MAAM,EAA4B,CAAC;QAC3E;;;WAGG;QACI,kCAA6B,GAAG,KAAK,CAAC,MAAM,EAA6B,CAAC;QACjF;;;WAGG;QACI,0BAAqB,GAAG,KAAK,CAAC,MAAM,EAAqB,CAAC;QACjE;;;WAGG;QACI,iCAA4B,GAAG,KAAK,CAAC,MAAM,EAAqB,CAAC;QACxE;;;WAGG;QACI,gCAA2B,GAAG,KAAK,CAAC,MAAM,EAA2B,CAAC;QAC7E;;WAEG;QACI,uCAAkC,GAAG,KAAK,CAAC,MAAM,EAA2B,CAAC;QACpF;;;WAGG;QACI,sBAAiB,GAAG,KAAK,CAAC,MAAM,EAAqB,CAAC;QAC7D;;;WAGG;QACI,sBAAiB,GAAG,KAAK,CAAC,MAAM,EAA0B,CAAC;QAClE;;;WAGG;QACI,sBAAiB,GAAG,KAAK,CAAC,MAAM,EAA4B,CAAC;QACpE;;;WAGG;QACI,oBAAe,GAAG,KAAK,CAAC,MAAM,EAA4B,CAAC;QAElE;;WAEG;QACK,0BAAqB,GAAyD,IAAI,CAAC;QAsEnF,2BAAsB,GAAkC;YAC5D,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,CAAC;SACZ,CAAC;QAWM,8BAAyB,GAA6B;YAC1D,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,CAAC;SACZ,CAAC;QAmhEM,+CAA0C,GAAG,KAAK,CAAC;QA4F3D,gBAAgB;QACT,wBAAmB,GAAG,KAAK,CAAC;QACnC,gBAAgB;QACT,uCAAkC,GAAG,KAAK,CAAC;QAC1C,wCAAmC,GAAG,KAAK,CAAC;QAmTpD,gBAAgB;QACT,gCAA2B,GAAG,IAAI,CAAC;QA6O1C;;;WAGG;QACI,8BAAyB,GAAiB,GAAG,EAAE;YAClD,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QACtC,CAAC,CAAC;QA4oBF,gBAAgB;QACT,uBAAkB,GAAW,CAAC,CAAC;QAiQ9B,iCAA4B,GAAG,KAAK,CAAC;QA8L7C;;;WAGG;QACO,mBAAc,GAAyC,IAAI,CAAC;QAvzHlE,IAAI,CAAC,aAAa,GAAG,EAAc,CAAC;QAEpC,MAAM,WAAW,GAAG;YAChB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE,IAAI;YACxB,gBAAgB,EAAE,IAAI;YACtB,OAAO,EAAE,KAAK;YACd,GAAG,OAAO;SACb,CAAC;QAEF,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,WAAW,CAAC,iBAAiB,CAAC;QAChE,IAAI,WAAW,CAAC,OAAO,EAAE;YACrB,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACpC;aAAM;YACH,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC5B;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,IAAI,CAAC,iBAAiB,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAEpD,IAAI,kBAAkB,EAAE;YACpB,IAAI,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC;SAC1D;QAED,IAAI,mBAAmB,EAAE,EAAE;YACvB,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;QAED,iBAAiB;QACjB,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,sCAAsC;QACtC,IAAI,4BAA4B,EAAE;YAC9B,IAAI,CAAC,6BAA6B,GAAG,IAAI,4BAA4B,EAAE,CAAC;SAC3E;QAED,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpC,IAAI,WAAW,CAAC,uBAAuB,EAAE;YACrC,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;SACnC;QAED,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,CAAC;QACzD,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC;QAErD,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YAC9B,MAAM,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAC1D;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,OAAO,CAAC;IACnB,CAAC;IAOD;;OAEG;IACI,yBAAyB;QAC5B,IAAI,CAAC,sBAAsB,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;QAC/C,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QACxD,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAOD;;OAEG;IACI,4BAA4B,CAAC,IAAkB;QAClD,IAAI,CAAC,yBAAyB,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;QACrD,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAC9D,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,4BAA4B;QAC/B,IAAI,CAAC,uBAAuB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACtE,IAAI,CAAC,0BAA0B,GAAG,CAAC,IAAkB,EAAE,EAAE,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;QAClG,IAAI,CAAC,gCAAgC,GAAG,CAAC,IAAkB,EAAE,QAAa,EAAE,EAAE,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;QACvH,IAAI,CAAC,6BAA6B,GAAG,CAAC,IAAkB,EAAE,QAAkB,EAAE,EAAE,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;IAC7H,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;IACvC,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa;QAC7B,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;IACvC,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa;QAC7B,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;;;;OAMG;IACI,uBAAuB,CAAC,QAAkB,EAAE,MAAc,EAAE,aAAqB,CAAC;QACrF,OAAO,IAAI,CAAC,aAAa,KAAK,MAAM,IAAI,IAAI,CAAC,eAAe,KAAK,QAAQ,IAAI,IAAI,CAAC,iBAAiB,KAAK,UAAU,CAAC;IACvH,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;IACvC,CAAC;IAED;;;OAGG;IACH,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,gBAAgB;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;IACvC,CAAC;IAED;;;OAGG;IACH,IAAW,6BAA6B;QACpC,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;IACzC,CAAC;IAED;;;OAGG;IACH,IAAW,0BAA0B;QACjC,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,OAAO,IAAI,CAAC,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,UAAU;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,wEAAwE;IACjE,iBAAiB;QACpB,IAAI,CAAC,SAAS,EAAE,CAAC;IACrB,CAAC;IAEO,UAAU;QACd,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;OAMG;IACI,mBAAmB,CAAC,UAAuB,EAAE,gBAAmC;QACnF,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,mBAAmB,CAAC,UAAuB,EAAE,gBAAmC;QACnF,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACI,iBAAiB,CAAC,UAAuB,EAAE,gBAAmC,EAAE,SAAmB;QACtG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,UAAU,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,SAAS,GAAG,CAAC;QAClC,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,QAAQ,GAAG,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,UAAU,GAAG,IAAI;QACtE,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IACvE,CAAC;IAED,iCAAiC;IAC1B,aAAa;QAChB,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;IACvC,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,kBAAkB,GAAG,IAAI;QACpC,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,KAAa,CAAC;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEhC,MAAM,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,CAAC;QAEvD,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC,YAAY,EAAE,YAAY,IAAI,mBAAmB,CAAC;QAEpF,IAAI,OAAO,GAAG,IAAI,CAAC;QAEnB,eAAe;QACf,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,OAAO,GAAG,KAAK,CAAC;SACnB;QAED,wEAAwE;QACxE,IAAI,CAAC,eAAe,EAAE,MAAM,EAAE,CAAC;QAE/B,MAAM;QACN,IAAI,IAAI,CAAC,+BAA+B,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACnE,OAAO,KAAP,OAAO,GAAK,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,EAAC;SACnD;QAED,SAAS;QACT,IAAI,kBAAkB,EAAE;YACpB,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;YACjC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;SACxC;QAED,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACjD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEhC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBAChD,SAAS;aACZ;YAED,6EAA6E;YAC7E,gEAAgE;YAChE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACrB,OAAO,GAAG,KAAK,CAAC;gBAChB,SAAS;aACZ;YAED,MAAM,0BAA0B,GAC5B,IAAI,CAAC,gBAAgB;gBACrB,IAAI,CAAC,YAAY,EAAE,KAAK,eAAe;gBACvC,IAAI,CAAC,YAAY,EAAE,KAAK,oBAAoB;gBAC5C,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,eAAe,IAAW,IAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC5E,oBAAoB;YACpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC1C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,0BAA0B,CAAC,EAAE;oBAChD,OAAO,GAAG,KAAK,CAAC;iBACnB;aACJ;YAED,IAAI,CAAC,kBAAkB,EAAE;gBACrB,SAAS;aACZ;YAED,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC;YAClD,IAAI,GAAG,EAAE;gBACL,IAAI,GAAG,CAAC,uBAAuB,EAAE;oBAC7B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE;wBAClC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;wBACvC,IAAI,QAAQ,IAAI,QAAQ,CAAC,uBAAuB,IAAI,QAAQ,CAAC,uBAAuB,IAAI,IAAI,EAAE;4BAC1F,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;gCACnD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gCAExC,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,QAAQ,CAAC,uBAAwB,EAAE,CAAC,CAAC;6BAC3F;yBACJ;qBACJ;iBACJ;qBAAM;oBACH,IAAI,GAAG,CAAC,uBAAuB,IAAI,GAAG,CAAC,uBAAuB,IAAI,IAAI,EAAE;wBACpE,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;4BAC9C,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;4BAEnC,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,GAAG,CAAC,uBAAwB,EAAE,CAAC,CAAC;yBACtF;qBACJ;iBACJ;aACJ;SACJ;QAED,iBAAiB;QACjB,IAAI,kBAAkB,EAAE;YACpB,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE;gBAClE,MAAM,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrD,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,EAAE;oBAC5B,OAAO,GAAG,KAAK,CAAC;iBACnB;aACJ;SACJ;QAED,aAAa;QACb,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAExC,IAAI,QAAQ,CAAC,cAAc,KAAK,SAAS,CAAC,sBAAsB,EAAE;gBAC9D,OAAO,GAAG,KAAK,CAAC;aACnB;SACJ;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YACrD,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE;gBACrC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACvB,OAAO,GAAG,KAAK,CAAC;iBACnB;aACJ;SACJ;aAAM,IAAI,IAAI,CAAC,YAAY,EAAE;YAC1B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAClC,OAAO,GAAG,KAAK,CAAC;aACnB;SACJ;QAED,YAAY;QACZ,KAAK,MAAM,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE;YAC/C,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE;gBAC3B,OAAO,GAAG,KAAK,CAAC;aACnB;SACJ;QAED,SAAS;QACT,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;gBAC7B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE;oBAClB,OAAO,GAAG,KAAK,CAAC;iBACnB;aACJ;SACJ;QAED,UAAU;QACV,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,EAAE;YAC9B,OAAO,GAAG,KAAK,CAAC;SACnB;QAED,MAAM,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAEjD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,2FAA2F;IACpF,mBAAmB;QACtB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,oBAAoB,CAAC,IAAgB;QACxC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACI,sBAAsB,CAAC,IAAgB;QAC1C,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,mBAAmB,CAAC,IAAgB;QACvC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,qBAAqB,CAAC,IAAgB;QACzC,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAEO,wBAAwB,CAAC,IAAgB;QAC7C,MAAM,QAAQ,GAAG,GAAG,EAAE;YAClB,IAAI,EAAE,CAAC;YACP,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACI,uBAAuB,CAAC,IAAgB,EAAE,OAAgB;QAC7D,IAAI,OAAO,KAAK,SAAS,EAAE;YACvB,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC,EAAE,OAAO,CAAC,CAAC;SACf;aAAM;YACH,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;SACvC;IACL,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,IAAS;QAC3B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,IAAS;QAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;QAClC,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACtC;QAED,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAC/B,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SACrD;IACL,CAAC;IAED;;;OAGG;IACI,oBAAoB;QACvB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,IAAgB,EAAE,kBAAkB,GAAG,KAAK;QAChE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,IAAI,CAAC,0BAA0B,KAAK,IAAI,EAAE;YAC1C,OAAO;SACV;QAED,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,kBAAkB,GAAG,KAAK;QAC5C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;gBACvB,OAAO,EAAE,CAAC;YACd,CAAC,EAAE,kBAAkB,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,kBAAkB,GAAG,KAAK;QAC3C,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpC,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;YAClC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAE7C,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;YACvC,OAAO;SACV;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;YACvC,OAAO;SACV;QAED,IAAI,CAAC,0BAA0B,GAAG,UAAU,CAAC,GAAG,EAAE;YAC9C,4DAA4D;YAC5D,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;QAC3C,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,2BAA2B;QAC9B,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC,GAAG,CAAC;IAChD,CAAC;IAED,SAAS;IAET;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,mBAAmB;QACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;;;;OAMG;IACI,kBAAkB,CAAC,KAAa,EAAE,WAAmB,EAAE,KAAc,EAAE,WAAoB;QAC9F,sEAAsE;QACtE,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACnD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAClC;QACD,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,qBAAqB,KAAK,WAAW,CAAC,UAAU,EAAE;YACpG,OAAO;SACV;QAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,UAAU,CAAC;QACxC,IAAI,CAAC,qBAAqB,GAAG,WAAW,CAAC,UAAU,CAAC;QACpD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;QAErC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE9E,iBAAiB;QACjB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAClE;aAAM;YACH,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;SACtE;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;YAC3D,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;SAChD;aAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YAC9B,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACtD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACrE;IACL,CAAC;IAED;;;OAGG;IACI,qBAAqB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;IAC9E,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAAC,IAAa;QACzC,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,IAAI,OAAO,CAAC,CAAC;QACpF,QAAQ,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QAC1C,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAChC,QAAQ,CAAC,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QACtC,QAAQ,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAEvC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,qBAAqB,CAAC,GAAkB;QAC3C,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QACrB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,OAAO,iBAAiB,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,OAAqB,EAAE,SAAS,GAAG,KAAK;QACnD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO;SACV;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1B,OAAO,CAAC,mBAAmB,EAAE,CAAC;QAE9B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,OAAO,CAAC,oBAAoB,EAAE,CAAC;SAClC;QAED,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAEvD,IAAI,SAAS,EAAE;YACX,OAAO,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBACnC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;;;OAKG;IACI,UAAU,CAAC,QAAsB,EAAE,SAAS,GAAG,KAAK;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,sCAAsC;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACzD,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAElB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;gBAClB,QAAQ,CAAC,yBAAyB,EAAE,CAAC;aACxC;SACJ;QAED,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE7C,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAI,SAAS,EAAE;YACX,QAAQ,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBACpC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;SACN;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,gBAA+B;QACnD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO;SACV;QAED,IAAI,gBAAgB,CAAC,QAAQ,EAAE,KAAK,IAAI,IAAI,gBAAgB,CAAC,gCAAgC,KAAK,CAAC,CAAC,EAAE;YAClG,iBAAiB;YACjB,OAAO;SACV;QAED,gBAAgB,CAAC,gCAAgC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QAC/E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE3C,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;YAC1B,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;SAC3C;QAED,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;IAC7E,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,QAAuB;QAC9C,MAAM,KAAK,GAAG,QAAQ,CAAC,gCAAgC,CAAC;QACxD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,KAAK,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACrE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;gBACtC,QAAQ,CAAC,gCAAgC,GAAG,KAAK,CAAC;aACrD;YAED,QAAQ,CAAC,gCAAgC,GAAG,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;YAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;gBAClB,QAAQ,CAAC,yBAAyB,EAAE,CAAC;aACxC;SACJ;QAED,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAEhE,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,QAAkB;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,iCAAiC;YACjC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAE3D,yBAAyB;YACzB,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9D;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAAC,QAA4B;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,iCAAiC;YACjC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC7C;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,QAAe;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,qBAAqB;YACrB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;gBAC5B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;aAC5C;YAED,sCAAsC;YACtC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;gBAClB,QAAQ,CAAC,yBAAyB,EAAE,CAAC;aACxC;SACJ;QACD,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACxD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,QAAgB;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,sCAAsC;YACtC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC9B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;gBAClB,QAAQ,CAAC,yBAAyB,EAAE,CAAC;aACxC;SACJ;QACD,4BAA4B;QAC5B,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE;gBACf,sCAAsC;gBACtC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;aACxC;SACJ;QACD,yBAAyB;QACzB,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE;YAChC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aACvC;iBAAM;gBACH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;aAC5B;SACJ;QACD,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACzD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,QAAyB;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAEtC,yBAAyB;YACzB,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;SACpE;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,QAAmB;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACpC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,MAAW,EAAE,aAAsB,EAAE,UAAqC;QAC3F,6DAA6D;IACjE,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,QAAwB;QAChD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACzC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,QAAuB;QAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACxC;QAED,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAEhE,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,QAAkB;QACpC,MAAM,KAAK,GAAG,QAAQ,CAAC,0BAA0B,CAAC;QAClD,IAAI,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YAC/C,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrC,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC/D,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC;gBACrC,YAAY,CAAC,0BAA0B,GAAG,KAAK,CAAC;aACnD;YAED,QAAQ,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;SACxB;QAED,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE3D,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,QAA+B;QACtD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACxC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,QAAqB;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAClC;QACD,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE1D,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,QAAQ,CAAC,QAAe;QAC3B,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO;SACV;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YAClB,QAAQ,CAAC,oBAAoB,EAAE,CAAC;SACnC;QAED,iFAAiF;QACjF,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;YAC5B,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC5C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjC,IAAI,CAAC,mBAAmB,EAAE,CAAC;aAC9B;SACJ;QAED,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,oBAAoB;QACvB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;SAC1D;IACL,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,SAAiB;QAC9B,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO;SACV;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7B,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAE3D,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACnB,SAAS,CAAC,oBAAoB,EAAE,CAAC;SACpC;IACL,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,WAAqB;QACpC,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO;SACV;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjC,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;IACnE,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,iBAAkC;QACvD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO;SACV;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,YAAuB;QACvC,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO;SACV;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,iBAAiC;QACtD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO;SACV;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,gBAA+B;QACnD,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO;SACV;QACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC3C,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;IAC7E,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,WAAqB;QACpC,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO;SACV;QAED,IAAI,WAAW,CAAC,QAAQ,EAAE,KAAK,IAAI,IAAI,WAAW,CAAC,0BAA0B,KAAK,CAAC,CAAC,EAAE;YAClF,kBAAkB;YAClB,OAAO;SACV;QAED,WAAW,CAAC,0BAA0B,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjC,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;IACnE,CAAC;IAED;;;OAGG;IACI,qBAAqB,CAAC,qBAAyC;QAClE,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO;SACV;QACD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IACzD,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,WAAqB;QACpC,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO;SACV;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;SAC7E;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,gBAAuC;QAC3D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,UAAuB;QACrC,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,OAAO;SACV;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/B,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;IACjE,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,SAAiB,EAAE,aAAa,GAAG,IAAI;QAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QAE9C,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;SACrC;QACD,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAC9B,IAAI,aAAa,EAAE;YACf,SAAS,CAAC,aAAa,EAAE,CAAC;SAC7B;IACL,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,EAAU;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QAEtC,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;YAC3B,OAAO,MAAM,CAAC;SACjB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,IAAY;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE1C,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;YAC3B,OAAO,MAAM,CAAC;SACjB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,IAAY;QACvC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC9D,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;gBAC3C,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;aACtC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,YAAY,CAAC,mBAA4B,EAAE,SAAmC;QAClF,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACvC,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE;gBACrB,OAAO,QAAQ,CAAC;aACnB;SACJ;QACD,IAAI,mBAAmB,EAAE;YACrB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC5C,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE;oBACrB,OAAO,QAAQ,CAAC;iBACnB;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,qBAAqB,CAAC,QAAgB,EAAE,sBAA+B,KAAK;QAC/E,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IAClF,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,EAAU,EAAE,sBAA+B,KAAK;QACnE,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;IACtE,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,IAAY,EAAE,sBAA+B,KAAK;QACvE,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,EAAU,EAAE,sBAA+B,KAAK;QACvE,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7D,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBACjC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAChC;SACJ;QACD,IAAI,mBAAmB,EAAE;YACrB,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;gBAClE,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;oBACtC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;iBACrC;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,QAAgB;QACxC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACvD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBAC5C,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aAC/B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,IAAY;QAChC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACvD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;gBACpC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aAC/B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,EAAU;QAC3B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAC9B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,QAAgB;QACvC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAC9B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,IAAY;QAC/B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;gBACnC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAC9B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,EAAU;QACzB,KAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,EAAE,EAAE;YAChF,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC/C,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;gBACpE,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;oBACrC,OAAO,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;iBACpC;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,IAAY;QAC7B,KAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,EAAE,EAAE;YAChF,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC/C,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;gBACpE,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;oBACzC,OAAO,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;iBACpC;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,IAAY;QAC9B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;gBAClC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC7B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,EAAU;QAC1B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC7B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,QAAgB;QACtC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC7B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,EAAU;QACnC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC9D,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBACvC,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;aACtC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,EAAU;QAC7B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACzD,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBAClC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;aACjC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,sBAAsB,CAAC,QAAgB;QAC3C,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,KAAK,KAAK,SAAS,EAAE;gBACrB,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;aACjC;SACJ;aAAM;YACH,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACzD,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE;oBAC9C,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;iBACjC;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,QAAkB,EAAE,KAAe;QACnD,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YAC1D,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE3B,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE5D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,QAAkB;QACpC,IAAI,KAAK,CAAC;QACV,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACtD,IAAI,KAAK,KAAK,SAAS,EAAE;gBACrB,OAAO,KAAK,CAAC;aAChB;SACJ;aAAM;YACH,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC1C,IAAI,KAAK,GAAG,CAAC,EAAE;gBACX,OAAO,KAAK,CAAC;aAChB;SACJ;QAED,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACtC,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACjE,IAAI,YAAY,EAAE;gBACd,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC;gBACtC,IAAI,IAAI,CAAC,qBAAqB,EAAE;oBAC5B,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;iBAC7D;aACJ;SACJ;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;SAC7D;QAED,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;QAEtB,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,EAAU;QACzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC7B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,EAAU;QAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;YACjC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,EAAU;QAClC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC7D,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;aACrC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,0BAA0B,CAAC,QAAgB;QAC9C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC7D,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBAClD,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;aACrC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,EAAU;QACnC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,UAAU,CAAC;YACzC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,QAAgB;QACrC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC7B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,EAAU;QAC7B,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;YAC1D,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC7B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAAC,EAAU;QACtC,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;YAClE,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;aACrC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,EAAU;QAC9B,IAAI,KAAa,CAAC;QAClB,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;YACtD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC7B;SACJ;QAED,KAAK,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;YAC9D,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;aACrC;SACJ;QAED,KAAK,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;YACvD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAC9B;SACJ;QAED,KAAK,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;YACtD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC7B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,EAAU;QACzB,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAClC,IAAI,IAAI,EAAE;YACN,OAAO,IAAI,CAAC;SACf;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QACpD,IAAI,aAAa,EAAE;YACf,OAAO,aAAa,CAAC;SACxB;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACpC,IAAI,KAAK,EAAE;YACP,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QACtC,IAAI,MAAM,EAAE;YACR,OAAO,MAAM,CAAC;SACjB;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAClC,IAAI,IAAI,EAAE;YACN,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,IAAY;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,IAAI,EAAE;YACN,OAAO,IAAI,CAAC;SACf;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACxD,IAAI,aAAa,EAAE;YACf,OAAO,aAAa,CAAC;SACxB;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,KAAK,EAAE;YACP,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,MAAM,EAAE;YACR,OAAO,MAAM,CAAC;SACjB;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,IAAI,EAAE;YACN,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,IAAY;QAC7B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;gBAClC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAC7B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,IAAY;QACtC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC7D,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;gBAC1C,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;aACrC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,EAAU;QACjC,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;YAC7D,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBACjC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAChC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,qBAAqB,CAAC,QAAgB;QACzC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACxD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBAC7C,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAChC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,EAAU;QAC7B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACxD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBACjC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAChC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,IAAY;QACjC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACxD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;gBACrC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAChC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,yBAAyB,CAAC,EAAU;QACvC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAClE,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,EAAE,EAAE;gBACjD,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;aAC1C;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,EAAU;QAChC,KAAK,IAAI,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,YAAY,EAAE;YACvF,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;YAClE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE;gBAChE,MAAM,MAAM,GAAG,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACnD,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE;oBAClB,OAAO,MAAM,CAAC;iBACjB;aACJ;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,IAAY;QACpC,KAAK,IAAI,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,YAAY,EAAE;YACvF,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;YAClE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE;gBAChE,MAAM,MAAM,GAAG,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACnD,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;oBACtB,OAAO,MAAM,CAAC;iBACjB;aACJ;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,IAAY;QACpC,KAAK,IAAI,gBAAgB,GAAG,CAAC,EAAE,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,gBAAgB,EAAE;YAC7F,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;YACzD,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,EAAE;gBAC3B,OAAO,WAAW,CAAC;aACtB;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,IAAkB;QAClC,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;SAChC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED;;;;;;;OAOG;IACI,eAAe,CAAmB,GAAW,EAAE,IAAO;QACzD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,aAAa,GAAG,IAAI,gBAAgB,EAAU,CAAC;SACvD;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAI,GAAW;QACjC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,IAAI,CAAC;SACf;QACD,OAAU,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;OAKG;IACI,+BAA+B,CAAmB,GAAW,EAAE,OAAyB;QAC3F,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,aAAa,GAAG,IAAI,gBAAgB,EAAU,CAAC;SACvD;QACD,OAAU,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,GAAW;QACjC,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC1C,CAAC;IAEO,gBAAgB,CAAC,OAAgB,EAAE,IAAkB,EAAE,WAAyB,EAAE,SAAkB;QACxG,IAAI,SAAS,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;YACvD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;aAC9B;YAED,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YACvC,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE;gBAC7C,iBAAiB;gBACjB,IAAI,QAAQ,CAAC,uBAAuB,IAAI,QAAQ,CAAC,uBAAuB,IAAI,IAAI,EAAE;oBAC9E,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;wBACnD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAExC,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,QAAQ,CAAC,uBAAwB,EAAE,CAAC,CAAC;qBAC3F;iBACJ;gBAED,WAAW;gBACX,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;aAC5D;SACJ;IACL,CAAC;IAED;;OAEG;IACI,sBAAsB;QACzB,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;IACvC,CAAC;IAID;;;;OAIG;IACH,IAAW,uCAAuC;QAC9C,OAAO,IAAI,CAAC,0CAA0C,CAAC;IAC3D,CAAC;IAED,IAAW,uCAAuC,CAAC,KAAc;QAC7D,IAAI,IAAI,CAAC,0CAA0C,KAAK,KAAK,EAAE;YAC3D,OAAO;SACV;QAED,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC9B;QAED,IAAI,CAAC,0CAA0C,GAAG,KAAK,CAAC;IAC5D,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,IAAI,IAAI,CAAC,uCAAuC,EAAE;YAC9C,OAAO;SACV;QAED,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE;YACtD,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;SAC7C;QACD,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChD,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gBAC3C,IAAI,YAAY,IAAI,YAAY,CAAC,aAAa,EAAE;oBAC5C,YAAY,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;iBACxC;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,IAAI,IAAI,CAAC,uCAAuC,EAAE;YAC9C,OAAO;SACV;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACjC,IAAI,OAAO,IAA0B,OAAQ,CAAC,UAAU,EAAE;oBAChC,OAAQ,CAAC,mBAAmB,EAAE,CAAC;iBACxD;aACJ;SACJ;IACL,CAAC;IAED,gBAAgB;IACT,0BAA0B;QAC7B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IA4BD;;;;;;;;OAQG;IACI,kBAAkB,CACrB,wBAAwB,GAAG,KAAK,EAChC,SAAsB,EACtB,OAAmC,EACnC,YAAY,GAAG,IAAI,EACnB,kBAAkB,GAAG,KAAK;QAE1B,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACpB,OAAO,IAAI,OAAO,CAAC,wBAAwB,CAAC,CAAC;gBAC7C,OAAO;aACV;YAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,qBAAqB,EAAE,CAAC;aAChC;YAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,kCAAkC,GAAG,kBAAkB,CAAC;YAC7D,IAAI,CAAC,mCAAmC,GAAG,wBAAwB,CAAC;YAEpE,IAAI,YAAY,EAAE;gBACd,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBAC5D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;iBAC5C;aACJ;YACD,SAAS,IAAI,SAAS,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,oBAAoB;QACvB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAChC,IAAI,IAAI,CAAC,6BAA6B,EAAE;gBACpC,IAAI,CAAC,6BAA6B,CAAC,SAAS,GAAG,KAAK,CAAC;aACxD;SACJ;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC5D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,CAAC;SAC9C;QAED,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,8BAA8B,CAAC,SAA0B;QAC7D,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC,sBAAsB,CAAC;QAE/H,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YACxE,OAAO,CAAC,gCAAgC;SAC3C;QAED,qDAAqD;QACrD,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;IACrE,CAAC;IAEO,qBAAqB;QACzB,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC,sBAAsB,EAAE;YAC3G,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC/B,IAAI,CAAC,YAAY,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;gBACzC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;gBAC3B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;gBAC/B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;gBACjC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;gBACpC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;gBAC9B,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;aACvC;YACD,OAAO;SACV;QAED,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YACvD,IAAI,CAAC,IAAI,CAAC,mCAAmC,EAAE;gBAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;gBACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;oBAC1B,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACxC,IAAI,CAAC,kBAAkB,EAAE,CAAC;iBAC7B;aACJ;YAED,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;gBACpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;oBAC/B,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;iBACjD;aACJ;YAED,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,CAAC;YAEtC,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO;SACV;QAED,IAAI,CAAC,wCAAwC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEpE,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QAErC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,8BAA8B,EAAE;YACpD,IAAI,CAAC,MAAM,EAAE,CAAC;SACjB;QAED,4BAA4B;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE9C,kBAAkB;QAClB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC1B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,6BAA6B,CAAC,qBAAqB,GAAG,KAAK,CAAC;YACjE,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,SAAS;aACZ;YAED,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,KAAK,CAAC,CAAC;YAE7D,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;gBACxE,SAAS;aACZ;YAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,gBAAgB;YAChB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,SAAS,CAAC,iCAAiC,EAAE,SAAS,CAAC,gCAAgC,CAAC,EAAE;gBACxJ,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;aACtD;YAED,wBAAwB;YACxB,IAAI,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC7H,IAAI,CAAC,6BAA6B,CAAC,WAAW,GAAG,YAAY,CAAC;YAC9D,IAAI,CAAC,6BAA6B,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAChE,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,IAAI,EAAE;gBACrD,SAAS;aACZ;YAED,2CAA2C;YAC3C,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,CAAC,aAAa,KAAK,CAAC,EAAE;gBAC3D,YAAY,CAAC,kBAAkB,EAAE,CAAC;aACrC;YAED,IAAI,CAAC,YAAY,EAAE,CAAC;YAEpB,IACI,IAAI,CAAC,SAAS;gBACd,IAAI,CAAC,UAAU,GAAG,CAAC;gBACnB,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC;gBACpD,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EACvG;gBACE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE3C,IAAI,YAAY,KAAK,IAAI,EAAE;oBACvB,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;iBACjD;gBAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,mBAAmB,EAAE;oBACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBACrB;gBAED,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE;oBACvC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;wBACpB,YAAY,CAAC,6BAA6B,CAAC,iBAAiB,GAAG,KAAK,CAAC;qBACxE;yBAAM;wBACH,IAAI,IAAI,CAAC,6BAA6B,CAAC,iBAAiB,EAAE;4BACtD,YAAY,GAAG,IAAI,CAAC;yBACvB;qBACJ;oBACD,YAAY,CAAC,6BAA6B,CAAC,SAAS,GAAG,IAAI,CAAC;oBAC5D,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;iBACxC;gBAED,IAAI,CAAC,aAAa,EAAE,CAAC;aACxB;SACJ;QAED,IAAI,CAAC,uCAAuC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEnE,mBAAmB;QACnB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,oCAAoC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAChE,KAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,aAAa,EAAE,EAAE;gBACtF,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;gBAE3D,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;oBACxD,SAAS;iBACZ;gBAED,MAAM,OAAO,GAAQ,cAAc,CAAC,OAAO,CAAC;gBAC5C,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,EAAE;oBAC1C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;oBACjD,cAAc,CAAC,OAAO,EAAE,CAAC;oBACzB,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;iBAC5D;aACJ;YACD,IAAI,CAAC,mCAAmC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAClE;IACL,CAAC;IAEO,WAAW,CAAC,UAAwB,EAAE,IAAkB;QAC5D,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;YACjF,IAAI,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBACtD,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACxB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;aACjE;YAED,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAChC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAO,IAAI,CAAC,CAAC;aAC3D;SACJ;QAED,IAAI,SAAS,GAAG,UAAU,CAAC,YAAY,IAAI,UAAU,CAAC,YAAY,IAAI,IAAI,CAAC,kCAAkC,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,wBAAwB,CAAC;QAE5K,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACrD,MAAM,SAAS,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC;YAC7B,SAAS,GAAG,SAAS,IAAI,GAAG,KAAK,CAAC,CAAC;YACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;gBAC1B,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAClC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;aAC/D;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,qBAAqB,CAAC,KAAe;QACxC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,IAAI,CAAC,YAAY,EAAE;YACf,OAAO;SACV;QAED,IAAI,YAAY,CAAC,mBAAmB,EAAE;YAClC,MAAM,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,aAAa,EAAE,EAAE,WAAW,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;SACnK;aAAM;YACH,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,aAAa,EAAE,EAAE,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;SAClG;IACL,CAAC;IAEO,gBAAgB,CAAC,MAAwB,EAAE,KAAK,GAAG,IAAI;QAC3D,IAAI,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE;YACpC,MAAM,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;SAC/C;aAAM,IAAI,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE;YAC5C,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;SAChD;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,uCAAuC,EAAE,EAAE;gBACzD,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC;aAC5C;SACJ;QACD,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;SAClC;IACL,CAAC;IAEO,iBAAiB,CAAC,MAAwB;QAC9C,6DAA6D;QAC7D,IAAI,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE;YACpC,cAAc;SACjB;aAAM,IAAI,MAAM,IAAI,MAAM,CAAC,kBAAkB,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE;YAC3E,MAAM,GAAG,GAAG,MAAM,CAAC,kBAAkB,CAAC;YACtC,IAAI,GAAG,CAAC,iBAAiB,CAAC,YAAY,EAAE,EAAE;gBACtC,GAAG,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACvD;iBAAM,IAAI,CAAC,GAAG,CAAC,gBAAgB,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;gBACvD,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;iBACpF;gBACD,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;aACvB;SACJ;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBAClC,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;gBACvC,IAAI,CAAC,MAAM,EAAE,CAAC;aACjB;iBAAM;gBACH,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;aAC/C;SACJ;IACL,CAAC;IAID;;OAEG;IACI,gBAAgB,CAAC,MAAc,EAAE,SAAkB,EAAE,eAAe,GAAG,IAAI;QAC9E,IAAI,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE;YACjC,OAAO;SACV;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5B,2EAA2E;QAC3E,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC5C;QAED,WAAW;QACX,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE/C,SAAS;QACT,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,eAAe,EAAE;YAClC,IAAI,gBAAgB,GAAG,IAAI,CAAC;YAC5B,IAAI,MAAM,CAAC,mBAAmB,IAAI,MAAM,CAAC,kBAAkB,EAAE;gBACzD,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,CAAC;gBAC9D,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;oBACxC,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,GAAG,KAAK,CAAC;iBACtD;aACJ;YACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC1C,IAAI,MAAM,CAAC,mBAAmB,IAAI,MAAM,CAAC,kBAAkB,EAAE;gBACzD,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;aACjE;SACJ;QAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEvE,SAAS;QACT,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,oBAAoB;QACpB,KAAK,IAAI,wBAAwB,GAAG,CAAC,EAAE,wBAAwB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,wBAAwB,EAAE,EAAE;YAC9H,MAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAExE,IAAI,CAAC,aAAa,CAAW,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC/C;QAED,iBAAiB;QACjB,IAAI,CAAC,qCAAqC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEjE,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAExE,IAAI,MAAM,CAAC,mBAAmB,IAAI,MAAM,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;YACrE,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;SACzE;QAED,IAAI,SAAS,IAAI,SAAS,CAAC,mBAAmB,IAAI,SAAS,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;YACxF,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;SAC5E;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE;YACnE,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAyC,CAAC,CAAC;SACvF;QAED,oDAAoD;QACpD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,qCAAqC,EAAE;YAC3D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACpC;QAED,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YAEnC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;gBAChC,KAAK,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChF,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;oBAC/E,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC3D,IAAI,YAAY,CAAC,aAAa,EAAE,EAAE;wBAC9B,IAAI,CAAC,SAAS,EAAE,CAAC;wBACjB,MAAM,4BAA4B,GAAG,YAAY,CAAC,YAAY,IAAI,YAAY,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,CAAC;wBAClH,YAAY,CAAC,MAAM,CAAU,4BAA4B,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;wBACvF,UAAU,GAAG,IAAI,CAAC;qBACrB;iBACJ;gBACD,KAAK,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAE9E,IAAI,CAAC,SAAS,EAAE,CAAC;aACpB;YAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,4BAA4B,EAAE;gBAClD,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC;aAC7D;YAED,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;SACvC;QAED,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,MAAM,CAAC,kBAAkB,EAAE,YAAY,IAAI,MAAM,CAAC,YAAY,IAAI,SAAS,CAAC,eAAe,CAAC;QAE/H,iDAAiD;QACjD,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YAC7B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACjD,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAChC;QAED,IAAI,CAAC,oCAAoC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEhE,gBAAgB;QAChB,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACvE,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;SAC3C;QAED,qBAAqB;QACrB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAClC;QAED,SAAS;QACT,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEvD,IAAI,MAAM,CAAC,iBAAiB,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,CAAC,sBAAsB,EAAE;YAC/F,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;QACD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACtD,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEtD,oBAAoB;QACpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAClC;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;YACtD,0FAA0F;YAC1F,MAAM,OAAO,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,YAAa,CAAC,CAAC,CAAC,SAAS,CAAC;YAChG,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;SAC1E;QAED,qBAAqB;QACrB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,4BAA4B,EAAE;YAClD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAClC;QAED,4BAA4B;QAC5B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAE5B,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC1E,CAAC;IAEO,kBAAkB,CAAC,MAAc,EAAE,eAAe,GAAG,IAAI;QAC7D,IAAI,MAAM,CAAC,aAAa,KAAK,SAAS,CAAC,aAAa,IAAI,MAAM,CAAC,mBAAmB,EAAE;YAChF,IAAI,MAAM,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBACxD,IAAI,CAAC,mBAAmB,EAAE,CAAC;aAC9B;YACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;YAC1D,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC3D,OAAO;SACV;QAED,IAAI,MAAM,CAAC,yBAAyB,EAAE;YAClC,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;SAC7C;aAAM;YACH,cAAc;YACd,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC5D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC5D,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,CAAC;aAC5D;SACJ;QAED,2EAA2E;QAC3E,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IAEO,mBAAmB;QACvB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtE,MAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE5D,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE;gBAC3B,SAAS;aACZ;YAED,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,UAAU,CAAC,aAAa,IAAI,WAAW,GAAG,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBACxH,MAAM,MAAM,GAAY,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBAEtE,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,iCAAiC,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,gCAAgC,EAAE;oBACjI,MAAM,UAAU,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC;oBAChD,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC;oBAEjE,MAAM,eAAe,GAAG,SAAS,CAAC,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,sBAAsB,CAAC,CAAC;oBAChG,MAAM,6BAA6B,GAAG,UAAU,CAAC,wBAAwB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;oBAE7F,IAAI,eAAe,IAAI,6BAA6B,KAAK,CAAC,CAAC,EAAE;wBACzD,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,iCAAiC,EAAE;4BAChE,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;4BAChF,UAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBACvD;6BAAM,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,gCAAgC,EAAE;4BACtE,UAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBACvD;qBACJ;yBAAM,IAAI,CAAC,eAAe,IAAI,6BAA6B,GAAG,CAAC,CAAC,EAAE;wBAC/D,uCAAuC;wBAEvC,oDAAoD;wBACpD,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,gCAAgC,EAAE;4BAC/D,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;yBACnF;wBAED,+GAA+G;wBAC/G,IACI,CAAC,UAAU,CAAC,aAAa,CAAC,kBAAkB,CAAC,SAAS,CAAC,gCAAgC,EAAE,CAAC,SAAS,EAAE,EAAE;4BACnG,MAAM,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;4BAClE,OAAO,SAAS,KAAK,aAAa,CAAC;wBACvC,CAAC,CAAC;4BACF,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,gCAAgC,EAC/D;4BACE,UAAU,CAAC,wBAAwB,CAAC,MAAM,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;yBAChF;qBACJ;iBACJ;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACI,yBAAyB,CAAC,IAAY;QACzC,8EAA8E;IAClF,CAAC;IAUD,gBAAgB;IACT,QAAQ,CAAC,eAAwB;QACpC,8DAA8D;IAClE,CAAC;IAED,2CAA2C;IACpC,OAAO;QACV,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE;YACxC,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAEhI,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACpD,MAAM,UAAU,GAAG,MAAM,GAAG,gBAAgB,GAAG,MAAM,CAAC;YAEtD,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAEvD,IAAI,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,gBAAgB,CAAC,CAAC;YAC7D,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAErD,OAAO,SAAS,GAAG,CAAC,IAAI,UAAU,GAAG,aAAa,EAAE;gBAChD,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAElD,aAAa;gBACb,IAAI,CAAC,eAAe,GAAG,gBAAgB,GAAG,UAAU,CAAC;gBACrD,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;gBAChC,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAEvD,UAAU;gBACV,IAAI,IAAI,CAAC,cAAc,EAAE;oBACrB,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,CAAC;iBACpD;gBAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACjD,IAAI,CAAC,cAAc,EAAE,CAAC;gBAEtB,UAAU,EAAE,CAAC;gBACb,SAAS,IAAI,gBAAgB,CAAC;aACjC;YAED,IAAI,CAAC,gBAAgB,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;SACzD;aAAM;YACH,aAAa;YACb,MAAM,SAAS,GAAG,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;YACpJ,IAAI,CAAC,eAAe,GAAG,SAAS,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;YACnD,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEvD,UAAU;YACV,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;aAC7C;SACJ;IACL,CAAC;IAEO,MAAM;QACV,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,SAAS,EAAE;YACjD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;SACrK;IACL,CAAC;IAEO,wBAAwB,CAAC,MAAwB;QACrD,IAAI,MAAM,EAAE,kBAAkB,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE;YACpD,MAAM,CAAC,kBAAkB,CAAC,QAAQ,GAAG,KAAK,CAAC;SAC9C;QACD,IAAI,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE;YAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBAC/C,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC;gBACpD,IAAI,GAAG,EAAE;oBACL,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC;iBACxB;aACJ;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,MAAe;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,OAAO;SACV;QAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;YAC5B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;SAC/B;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,aAAa,GAAG,IAAI,EAAE,gBAAgB,GAAG,KAAK;QACxD,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO;SACV;QAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,0BAA0B,KAAK,IAAI,EAAE;YACnF,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE;YAC5B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;SAC7D;QAED,qEAAqE;QACrE,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC;QACtC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;QACpC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;QACpC,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;QAClC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAExD,UAAU;QACV,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;SAC3E;QAED,aAAa;QACb,IAAI,CAAC,gBAAgB,EAAE;YACnB,IAAI,CAAC,OAAO,EAAE,CAAC;SAClB;QAED,6BAA6B;QAC7B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;SACjB;QAED,iBAAiB;QACjB,IAAI,aAAa,EAAE;YACf,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrD,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;oBAC9E,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;oBAC/C,MAAM,CAAC,MAAM,EAAE,CAAC;oBAChB,IAAI,MAAM,CAAC,aAAa,KAAK,SAAS,CAAC,aAAa,EAAE;wBAClD,cAAc;wBACd,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;4BAC5D,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;yBACtC;qBACJ;iBACJ;aACJ;iBAAM,IAAI,IAAI,CAAC,YAAY,EAAE;gBAC1B,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;gBAC3B,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,KAAK,SAAS,CAAC,aAAa,EAAE;oBAC7D,cAAc;oBACd,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;wBACvE,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;qBACjD;iBACJ;aACJ;SACJ;QAED,gBAAgB;QAChB,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEpD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEhC,yBAAyB;QACzB,IAAI,CAAC,qCAAqC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEjE,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;QACnG,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,KAAK,CAAC,uBAAuB,CAAC,uBAAuB,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC5F,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACnC,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBACpF,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;gBAC3D,IAAI,YAAY,CAAC,aAAa,EAAE,EAAE;oBAC9B,IAAI,CAAC,SAAS,EAAE,CAAC;oBAEjB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;oBAEnE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;wBACpB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;qBAC5C;oBAED,WAAW;oBACX,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;oBAE/C,SAAS;oBACT,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAE7B,YAAY,CAAC,MAAM,CAAC,mBAAmB,KAAK,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;iBAC9F;aACJ;YACD,KAAK,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC1F,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;YACpC,IAAI,CAAC,SAAS,EAAE,CAAC;SACpB;QAED,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,mBAAmB,EAAE,YAAY,IAAI,SAAS,CAAC,eAAe,CAAC;QAElG,sBAAsB;QACtB,IAAI,CAAC,YAAY,GAAG,mBAAmB,CAAC;QACxC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,KAAK,SAAS,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACvG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;SACpD;QACD,IAAI,CAAC,oCAAoC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEhE,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACvC,IAAI,CAAC,MAAM,EAAE,CAAC;SACjB;QAED,QAAQ;QACR,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE1C,oDAAoD;QACpD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAC/C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACpC;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YACrD,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBAC9E,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC;aAC7E;SACJ;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACpB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;aACxC;YAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;SACtF;QAED,sBAAsB;QACtB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,2CAA2C;QAC3C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACvC,IAAI,CAAC,MAAM,EAAE,CAAC;SACjB;QAED,eAAe;QACf,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;QAED,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEnD,WAAW;QACX,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YAC3B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC5D,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBACvC,IAAI,IAAI,EAAE;oBACN,IAAI,CAAC,OAAO,EAAE,CAAC;iBAClB;aACJ;YAED,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;SACjC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;SACtC;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACtC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACI,eAAe;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;SAC9B;IACL,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;SAChC;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO;SACV;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;QAC5C,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;QAC5C,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,qCAAqC,CAAC,KAAK,EAAE,CAAC;QACnD,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAE7B,IAAI,CAAC,mBAAmB,GAAG,EAAc,CAAC;QAE1C,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,iJAAiJ;YACjJ,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC3C,UAAU,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;gBAC5C,UAAU,CAAC,cAAc,GAAG,IAAI,CAAC;YACrC,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,eAAe;QACf,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC1C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;QACnC,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,CAAC;QACtC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,CAAC;QACtC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;QACvC,IAAI,CAAC,mCAAmC,CAAC,OAAO,EAAE,CAAC;QACnD,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;QAE9B,wBAAwB;QACxB,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QACpD,KAAK,MAAM,OAAO,IAAI,cAAc,EAAE;YAClC,OAAO,CAAC,KAAK,EAAE,CAAC;SACnB;QACD,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAEhC,SAAS;QACT,IAAI;YACA,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAClD;QAAC,OAAO,CAAC,EAAE;YACR,MAAM,CAAC,KAAK,CAAC,sDAAsD,EAAE,CAAC,CAAC,CAAC;SAC3E;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,iBAAiB;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QAE9C,IAAI,MAAM,EAAE;YACR,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACtD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;aACvC;SACJ;QAED,2BAA2B;QAC3B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAExC,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE/B,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QAErE,kBAAkB;QAClB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAE3B,oBAAoB;QACpB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;SACnC;QACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAElC,oBAAoB;QACpB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAExC,wBAAwB;QACxB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEtC,mBAAmB;QACnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEjC,wBAAwB;QACxB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAE5C,cAAc;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAEzB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;SACrC;QAED,iBAAiB;QACjB,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QAElC,aAAa;QACb,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEpC,qBAAqB;QACrB,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACxC;QAED,IAAI,WAAW,CAAC,iBAAiB,KAAK,IAAI,EAAE;YACxC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBAChC,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;aACvF;iBAAM;gBACH,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC;aACxC;SACJ;QAED,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAElD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAChD;QAED,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,qCAAqC,CAAC,KAAK,EAAE,CAAC;QACnD,IAAI,CAAC,oCAAoC,CAAC,KAAK,EAAE,CAAC;QAClD,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,wCAAwC,CAAC,KAAK,EAAE,CAAC;QACtD,IAAI,CAAC,uCAAuC,CAAC,KAAK,EAAE,CAAC;QACrD,IAAI,CAAC,oCAAoC,CAAC,KAAK,EAAE,CAAC;QAClD,IAAI,CAAC,mCAAmC,CAAC,KAAK,EAAE,CAAC;QACjD,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;QAC9C,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,CAAC;QAC7C,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;QAC5C,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,iCAAiC,CAAC,KAAK,EAAE,CAAC;QAC/C,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;QAC9C,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,iCAAiC,CAAC,KAAK,EAAE,CAAC;QAC/C,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;QAC9C,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,2CAA2C,CAAC,KAAK,EAAE,CAAC;QACzD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAEO,YAAY,CAAwB,KAAU,EAAE,QAA4B;QAChF,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACjC,QAAQ,GAAG,QAAQ,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;QAClD,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;YAC1B,QAAQ,CAAC,IAAI,CAAC,CAAC;SAClB;QACD,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,qBAAqB;QACxB,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;YACjE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACpC,MAAM,QAAQ,GAAU,IAAK,CAAC,QAAQ,CAAC;YAEvC,IAAI,QAAQ,EAAE;gBACV,QAAQ,CAAC,eAAe,EAAE,CAAC;aAC9B;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,wBAAwB;QAC3B,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE;YACrC,MAAM,MAAM,GAAa,WAAY,CAAC,OAAO,CAAC;YAE9C,IAAI,MAAM,EAAE;gBACE,WAAY,CAAC,OAAO,GAAG,IAAI,CAAC;aACzC;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,eAAiD;QACpE,MAAM,GAAG,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC9E,MAAM,GAAG,GAAG,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACjF,eAAe,GAAG,eAAe,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACjD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAE9B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzE,OAAO;aACV;YAED,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAE5C,MAAM,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC;YACrD,MAAM,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC;YAErD,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YACvC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,OAAO;YACH,GAAG,EAAE,GAAG;YACR,GAAG,EAAE,GAAG;SACX,CAAC;IACN,CAAC;IAED,UAAU;IAEV,uDAAuD;IACvD;;;;;;;;OAQG;IACI,gBAAgB,CAAC,CAAS,EAAE,CAAS,EAAE,KAAuB,EAAE,MAAwB,EAAE,eAAe,GAAG,KAAK;QACpH,MAAM,WAAW,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,uDAAuD;IACvD;;;;;;;;;;OAUG;IACI,qBAAqB,CACxB,CAAS,EACT,CAAS,EACT,KAAuB,EACvB,MAAW,EACX,MAAwB,EACxB,eAAe,GAAG,KAAK,EACvB,oBAAoB,GAAG,KAAK;QAE5B,MAAM,WAAW,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,uDAAuD;IACvD;;;;;;OAMG;IACI,6BAA6B,CAAC,CAAS,EAAE,CAAS,EAAE,MAAe;QACtE,MAAM,WAAW,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,uDAAuD;IACvD;;;;;;;OAOG;IACI,kCAAkC,CAAC,CAAS,EAAE,CAAS,EAAE,MAAW,EAAE,MAAe;QACxF,MAAM,WAAW,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,gBAAgB;IAChB,IAAW,iBAAiB;QACxB,OAAO,KAAK,CAAC;IACjB,CAAC;IAKD;;;;;;;;OAQG;IACI,IAAI,CACP,CAAS,EACT,CAAS,EACT,SAA2C,EAC3C,SAAmB,EACnB,MAAyB,EACzB,iBAA4C;QAE5C,MAAM,IAAI,GAAG,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACtC,IAAI,IAAI,EAAE;YACN,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrB;QACD,6CAA6C;QAC7C,OAAO,IAAI,WAAW,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;;;OAOG;IACI,oBAAoB,CAAC,CAAS,EAAE,CAAS,EAAE,SAA2C,EAAE,SAAmB,EAAE,MAAyB;QACzI,MAAM,IAAI,GAAG,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACtC,IAAI,IAAI,EAAE;YACN,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrB;QACD,6CAA6C;QAC7C,OAAO,IAAI,WAAW,EAAE,CAAC;IAC7B,CAAC;IAED,uDAAuD;IACvD;;;;;;;;OAQG;IACI,WAAW,CAAC,GAAQ,EAAE,SAA2C,EAAE,SAAmB,EAAE,iBAA4C;QACvI,MAAM,WAAW,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,uDAAuD;IACvD;;;;;;;;;OASG;IACI,SAAS,CAAC,CAAS,EAAE,CAAS,EAAE,SAA2C,EAAE,MAAe,EAAE,iBAA4C;QAC7I,MAAM,WAAW,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,uDAAuD;IACvD;;;;;;OAMG;IACI,gBAAgB,CAAC,GAAQ,EAAE,SAA2C,EAAE,iBAA4C;QACvH,MAAM,WAAW,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,IAA4B,EAAE,SAAkB,EAAE,UAAkC;QAC1G,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;IACvE,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;IACnD,CAAC;IAED,QAAQ;IACR,gBAAgB;IACT,kBAAkB;QACrB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,QAAQ,CAAC,QAAQ,EAAE,CAAC;SACvB;QAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;YAC5B,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;SACtC;QAED,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACtC,SAAS,CAAC,OAAO,EAAE,CAAC;SACvB;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,eAAe,EAAE;YACvC,MAAM,CAAC,OAAO,EAAE,CAAC;SACpB;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,cAAc,EAAE;gBACzC,SAAS,CAAC,OAAO,EAAE,CAAC;aACvB;SACJ;IACL,CAAC;IAED,gBAAgB;IACT,gBAAgB;QACnB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SAC1B;QAED,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IACtE,CAAC;IAED;;;;;;OAMG;IACK,UAAU,CAAC,IAAW,EAAE,SAAiB,EAAE,MAA+B;QAC9E,IAAI,SAAS,KAAK,SAAS,EAAE;YACzB,uGAAuG;YACvG,OAAO,IAAI,CAAC;SACf;QAED,MAAM,UAAU,GAAG,EAAE,CAAC;QAEtB,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;YAClB,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;gBACzE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACzB;SACJ;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,SAAiB,EAAE,MAAwC;QAC9E,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,SAAiB,EAAE,MAAoC;QAC3E,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,SAAiB,EAAE,MAAkC;QACxE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,SAAiB,EAAE,MAAwC;QAChF,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;IAC9H,CAAC;IAED;;;;;OAKG;IACI,uBAAuB,CAAC,SAAiB,EAAE,MAA8C;QAC5F,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IACnE,CAAC;IAED;;;;;;;;OAQG;IACI,iBAAiB,CACpB,gBAAwB,EACxB,sBAAoE,IAAI,EACxE,yBAAuE,IAAI,EAC3E,2BAAyE,IAAI;QAE7E,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,wBAAwB,CAAC,CAAC;IACtI,CAAC;IAED;;;;;;;OAOG;IACI,iCAAiC,CAAC,gBAAwB,EAAE,qBAA8B,EAAE,KAAK,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI;QAC3H,IAAI,CAAC,iBAAiB,CAAC,iCAAiC,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACtH,CAAC;IAED;;;;;OAKG;IACI,6BAA6B,CAAC,KAAa;QAC9C,OAAO,IAAI,CAAC,iBAAiB,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;IACvE,CAAC;IAID,gBAAgB;IACT,iCAAiC,CAAC,KAAc;QACnD,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;IAC9C,CAAC;IAED,4IAA4I;IAC5I,IAAW,2BAA2B;QAClC,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED,IAAW,2BAA2B,CAAC,KAAc;QACjD,IAAI,IAAI,CAAC,4BAA4B,KAAK,KAAK,EAAE;YAC7C,OAAO;SACV;QAED,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;QAE1C,IAAI,CAAC,KAAK,EAAE;YACR,uBAAuB;YACvB,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;SACjE;IACL,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,IAAY,EAAE,SAAsC;QAC/E,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,OAAO;SACV;QAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;gBACnC,SAAS;aACZ;YACD,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SAC9B;IACL,CAAC;IAED;;OAEG;IACI,SAAS,CACZ,SAAwB,EACxB,SAAqE,EACrE,UAAwC,EACxC,iBAA2B,EAC3B,cAAwB,EACxB,OAAmE,EACnE,QAAwC;QAExC,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,EAAE,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QACpJ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACzC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAkBD;;OAEG;IACI,cAAc,CACjB,SAAwB,EACxB,UAAgC,EAChC,iBAA2B,EAC3B,cAAwB,EACxB,QAAwC;QAExC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,SAAS,CACV,SAAS,EACT,CAAC,IAAI,EAAE,EAAE;gBACL,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,EACD,UAAU,EACV,iBAAiB,EACjB,cAAc,EACd,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;gBACnB,MAAM,CAAC,SAAS,CAAC,CAAC;YACtB,CAAC,EACD,QAAQ,CACX,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,YAAY,CACf,GAAW,EACX,SAAqE,EACrE,UAAwC,EACxC,iBAA2B,EAC3B,cAAwB,EACxB,OAA2C,EAC3C,QAAwC;QAExC,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,EAAE,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QACjJ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACzC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,iBAAiB,CACpB,GAAW,EACX,UAAwC,EACxC,iBAA2B,EAC3B,cAAwB,EACxB,QAAwC;QAExC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,YAAY,CACb,GAAG,EACH,CAAC,IAAI,EAAE,EAAE;gBACL,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,EACD,UAAU,EACV,iBAAiB,EACjB,cAAc,EACd,CAAC,KAAK,EAAE,EAAE;gBACN,MAAM,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC,EACD,QAAQ,CACX,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,SAAS,CACZ,IAAU,EACV,SAA+C,EAC/C,UAAuC,EACvC,cAAwB,EACxB,OAAwC;QAExC,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;QAC/E,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACzC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,IAAU,EAAE,UAAuC,EAAE,cAAwB;QAC/F,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,SAAS,CACV,IAAI,EACJ,CAAC,IAAI,EAAE,EAAE;gBACL,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,EACD,UAAU,EACV,cAAc,EACd,CAAC,KAAK,EAAE,EAAE;gBACN,MAAM,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC,CACJ,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAQD,uDAAuD;IACvD;;;OAGG;IACI,gBAAgB;QACnB,MAAM,WAAW,CAAC,iCAAiC,CAAC,CAAC;IACzD,CAAC;IAED,aAAa;IAEb;;;;;OAKG;IACH,mBAAmB,CAAC,EAAU;QAC1B,OAAO,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC;IACD;;;;;OAKG;IACH,eAAe,CAAC,EAAU;QACtB,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;IACpC,CAAC;IACD;;;;;OAKG;IACH,mBAAmB,CAAC,EAAU;QAC1B,OAAO,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC;IAED;;;;;OAKG;IACH,oBAAoB,CAAC,QAAgB;QACjC,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IACD;;;;;OAKG;IACH,aAAa,CAAC,EAAU;QACpB,OAAO,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IACD;;;;;OAKG;IACH,mBAAmB,CAAC,QAAgB;QAChC,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IACD;;;;;OAKG;IACH,WAAW,CAAC,EAAU;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IACD;;;;;OAKG;IACH,YAAY,CAAC,EAAU;QACnB,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;IACjC,CAAC;IACD;;;;;OAKG;IACH,kBAAkB,CAAC,QAAgB;QAC/B,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IACD;;;;;OAKG;IACH,qBAAqB,CAAC,EAAU;QAC5B,OAAO,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;IAC1C,CAAC;IACD;;;;;OAKG;IACH,eAAe,CAAC,EAAU;QACtB,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;IACpC,CAAC;IACD;;;;;OAKG;IACH,WAAW,CAAC,EAAU;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IACD;;;;;OAKG;IACH,iBAAiB,CAAC,QAAgB;QAC9B,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IACD;;;;;OAKG;IACH,eAAe,CAAC,EAAU;QACtB,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;IACpC,CAAC;IACD;;;;;OAKG;IACH,aAAa,CAAC,EAAU;QACpB,OAAO,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IACD;;;;;OAKG;IACH,oBAAoB,CAAC,EAAU;QAC3B,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;IACzC,CAAC;IACD;;;;;OAKG;IACH,0BAA0B,CAAC,QAAgB;QACvC,OAAO,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC;IACD;;;;;OAKG;IACH,qBAAqB,CAAC,EAAU;QAC5B,OAAO,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;IAC1C,CAAC;IACD;;;;;OAKG;IACH,WAAW,CAAC,EAAU;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IACD;;;;;OAKG;IACH,gBAAgB,CAAC,EAAU;QACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACrC,CAAC;IACD;;;;;OAKG;IACH,mBAAmB,CAAC,EAAU;QAC1B,OAAO,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC;;AA3+KD,6BAA6B;AACN,kBAAY,GAAG,SAAS,CAAC,YAAY,AAAzB,CAA0B;AAC7D,2DAA2D;AACpC,iBAAW,GAAG,SAAS,CAAC,WAAW,AAAxB,CAAyB;AAC3D,mFAAmF;AAC5D,kBAAY,GAAG,SAAS,CAAC,YAAY,AAAzB,CAA0B;AAC7D,sDAAsD;AAC/B,oBAAc,GAAG,SAAS,CAAC,cAAc,AAA3B,CAA4B;AAEjE;;;GAGG;AACW,kBAAY,GAAG,GAAG,AAAN,CAAO;AACjC;;;GAGG;AACW,kBAAY,GAAG,MAAM,AAAT,CAAU", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport type { Nullable } from \"./types\";\r\nimport { Tools } from \"./Misc/tools\";\r\nimport type { IAnimatable } from \"./Animations/animatable.interface\";\r\nimport { PrecisionDate } from \"./Misc/precisionDate\";\r\nimport type { Observer } from \"./Misc/observable\";\r\nimport { Observable } from \"./Misc/observable\";\r\nimport type { ISmartArrayLike } from \"./Misc/smartArray\";\r\nimport { SmartArrayNoDuplicate, SmartArray } from \"./Misc/smartArray\";\r\nimport { StringDictionary } from \"./Misc/stringDictionary\";\r\nimport { Tags } from \"./Misc/tags\";\r\nimport type { Vector2, Vector4 } from \"./Maths/math.vector\";\r\nimport { Vector3, Matrix, TmpVectors } from \"./Maths/math.vector\";\r\nimport type { IParticleSystem } from \"./Particles/IParticleSystem\";\r\nimport { AbstractScene } from \"./abstractScene\";\r\nimport { ImageProcessingConfiguration } from \"./Materials/imageProcessingConfiguration\";\r\nimport { UniformBuffer } from \"./Materials/uniformBuffer\";\r\nimport { PickingInfo } from \"./Collisions/pickingInfo\";\r\nimport type { ICollisionCoordinator } from \"./Collisions/collisionCoordinator\";\r\nimport type { PointerEventTypes, PointerInfoPre, PointerInfo } from \"./Events/pointerEvents\";\r\nimport type { KeyboardInfoPre, KeyboardInfo } from \"./Events/keyboardEvents\";\r\nimport { ActionEvent } from \"./Actions/actionEvent\";\r\nimport { PostProcessManager } from \"./PostProcesses/postProcessManager\";\r\nimport type { IOfflineProvider } from \"./Offline/IOfflineProvider\";\r\nimport type { RenderingGroupInfo, IRenderingManagerAutoClearSetup } from \"./Rendering/renderingManager\";\r\nimport { RenderingManager } from \"./Rendering/renderingManager\";\r\nimport type {\r\n    ISceneComponent,\r\n    ISceneSerializableComponent,\r\n    SimpleStageAction,\r\n    RenderTargetsStageAction,\r\n    RenderTargetStageAction,\r\n    MeshStageAction,\r\n    EvaluateSubMeshStageAction,\r\n    PreActiveMeshStageAction,\r\n    CameraStageAction,\r\n    RenderingGroupStageAction,\r\n    RenderingMeshStageAction,\r\n    PointerMoveStageAction,\r\n    PointerUpDownStageAction,\r\n    CameraStageFrameBufferAction,\r\n} from \"./sceneComponent\";\r\nimport { Stage } from \"./sceneComponent\";\r\nimport type { Engine } from \"./Engines/engine\";\r\nimport { Constants } from \"./Engines/constants\";\r\nimport { IsWindowObjectExist } from \"./Misc/domManagement\";\r\nimport { EngineStore } from \"./Engines/engineStore\";\r\nimport type { AbstractActionManager } from \"./Actions/abstractActionManager\";\r\nimport { _WarnImport } from \"./Misc/devTools\";\r\nimport type { WebRequest } from \"./Misc/webRequest\";\r\nimport { InputManager } from \"./Inputs/scene.inputManager\";\r\nimport { PerfCounter } from \"./Misc/perfCounter\";\r\nimport type { IFileRequest } from \"./Misc/fileRequest\";\r\nimport { Color4, Color3 } from \"./Maths/math.color\";\r\nimport type { Plane } from \"./Maths/math.plane\";\r\nimport { Frustum } from \"./Maths/math.frustum\";\r\nimport { UniqueIdGenerator } from \"./Misc/uniqueIdGenerator\";\r\nimport type { LoadFileError, RequestFileError, ReadFileError } from \"./Misc/fileTools\";\r\nimport { ReadFile, RequestFile, LoadFile } from \"./Misc/fileTools\";\r\nimport type { IClipPlanesHolder } from \"./Misc/interfaces/iClipPlanesHolder\";\r\nimport type { IPointerEvent } from \"./Events/deviceInputEvents\";\r\nimport { LightConstants } from \"./Lights/lightConstants\";\r\nimport { _ObserveArray } from \"./Misc/arrayTools\";\r\nimport type { IAction } from \"./Actions/action\";\r\nimport type { AnimationPropertiesOverride } from \"./Animations/animationPropertiesOverride\";\r\nimport type { AnimationGroup } from \"./Animations/animationGroup\";\r\nimport type { Skeleton } from \"./Bones/skeleton\";\r\nimport type { Bone } from \"./Bones/bone\";\r\nimport type { Camera } from \"./Cameras/camera\";\r\nimport type { Collider } from \"./Collisions/collider\";\r\nimport type { Ray, TrianglePickingPredicate } from \"./Culling/ray\";\r\nimport type { Light } from \"./Lights/light\";\r\nimport type { PerformanceViewerCollector } from \"./Misc/PerformanceViewer/performanceViewerCollector\";\r\nimport type { MorphTarget } from \"./Morph/morphTarget\";\r\nimport type { MorphTargetManager } from \"./Morph/morphTargetManager\";\r\nimport type { PostProcess } from \"./PostProcesses/postProcess\";\r\nimport type { Material } from \"./Materials/material\";\r\nimport type { BaseTexture } from \"./Materials/Textures/baseTexture\";\r\nimport type { Geometry } from \"./Meshes/geometry\";\r\nimport type { TransformNode } from \"./Meshes/transformNode\";\r\nimport type { AbstractMesh } from \"./Meshes/abstractMesh\";\r\nimport type { MultiMaterial } from \"./Materials/multiMaterial\";\r\nimport type { Effect } from \"./Materials/effect\";\r\nimport type { RenderTargetTexture } from \"./Materials/Textures/renderTargetTexture\";\r\nimport type { Mesh } from \"./Meshes/mesh\";\r\nimport type { SubMesh } from \"./Meshes/subMesh\";\r\nimport type { Node } from \"./node\";\r\nimport type { Animation } from \"./Animations/animation\";\r\nimport type { Animatable } from \"./Animations/animatable\";\r\nimport type { Texture } from \"./Materials/Textures/texture\";\r\nimport { PointerPickingConfiguration } from \"./Inputs/pointerPickingConfiguration\";\r\nimport { Logger } from \"./Misc/logger\";\r\n\r\n/**\r\n * Define an interface for all classes that will hold resources\r\n */\r\nexport interface IDisposable {\r\n    /**\r\n     * Releases all held resources\r\n     */\r\n    dispose(): void;\r\n}\r\n\r\n/** Interface defining initialization parameters for Scene class */\r\nexport interface SceneOptions {\r\n    /**\r\n     * Defines that scene should keep up-to-date a map of geometry to enable fast look-up by uniqueId\r\n     * It will improve performance when the number of geometries becomes important.\r\n     */\r\n    useGeometryUniqueIdsMap?: boolean;\r\n\r\n    /**\r\n     * Defines that each material of the scene should keep up-to-date a map of referencing meshes for fast disposing\r\n     * It will improve performance when the number of mesh becomes important, but might consume a bit more memory\r\n     */\r\n    useMaterialMeshMap?: boolean;\r\n\r\n    /**\r\n     * Defines that each mesh of the scene should keep up-to-date a map of referencing cloned meshes for fast disposing\r\n     * It will improve performance when the number of mesh becomes important, but might consume a bit more memory\r\n     */\r\n    useClonedMeshMap?: boolean;\r\n\r\n    /** Defines if the creation of the scene should impact the engine (Eg. UtilityLayer's scene) */\r\n    virtual?: boolean;\r\n}\r\n\r\n/**\r\n * Define how the scene should favor performance over ease of use\r\n */\r\nexport enum ScenePerformancePriority {\r\n    /** Default mode. No change. Performance will be treated as less important than backward compatibility */\r\n    BackwardCompatible,\r\n    /** Some performance options will be turned on trying to strike a balance between perf and ease of use */\r\n    Intermediate,\r\n    /** Performance will be top priority */\r\n    Aggressive,\r\n}\r\n\r\n/**\r\n * Represents a scene to be rendered by the engine.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene\r\n */\r\nexport class Scene extends AbstractScene implements IAnimatable, IClipPlanesHolder {\r\n    /** The fog is deactivated */\r\n    public static readonly FOGMODE_NONE = Constants.FOGMODE_NONE;\r\n    /** The fog density is following an exponential function */\r\n    public static readonly FOGMODE_EXP = Constants.FOGMODE_EXP;\r\n    /** The fog density is following an exponential function faster than FOGMODE_EXP */\r\n    public static readonly FOGMODE_EXP2 = Constants.FOGMODE_EXP2;\r\n    /** The fog density is following a linear function. */\r\n    public static readonly FOGMODE_LINEAR = Constants.FOGMODE_LINEAR;\r\n\r\n    /**\r\n     * Gets or sets the minimum deltatime when deterministic lock step is enabled\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     */\r\n    public static MinDeltaTime = 1.0;\r\n    /**\r\n     * Gets or sets the maximum deltatime when deterministic lock step is enabled\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     */\r\n    public static MaxDeltaTime = 1000.0;\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Factory used to create the default material.\r\n     * @param scene The scene to create the material for\r\n     * @returns The default material\r\n     */\r\n    public static DefaultMaterialFactory(scene: Scene): Material {\r\n        throw _WarnImport(\"StandardMaterial\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Factory used to create the a collision coordinator.\r\n     * @returns The collision coordinator\r\n     */\r\n    public static CollisionCoordinatorFactory(): ICollisionCoordinator {\r\n        throw _WarnImport(\"DefaultCollisionCoordinator\");\r\n    }\r\n\r\n    // Members\r\n\r\n    /** @internal */\r\n    public _inputManager = new InputManager(this);\r\n\r\n    /** Define this parameter if you are using multiple cameras and you want to specify which one should be used for pointer position */\r\n    public cameraToUseForPointers: Nullable<Camera> = null;\r\n\r\n    /** @internal */\r\n    public readonly _isScene = true;\r\n\r\n    /** @internal */\r\n    public _blockEntityCollection = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean that indicates if the scene must clear the render buffer before rendering a frame\r\n     */\r\n    public autoClear = true;\r\n    /**\r\n     * Gets or sets a boolean that indicates if the scene must clear the depth and stencil buffers before rendering a frame\r\n     */\r\n    public autoClearDepthAndStencil = true;\r\n    /**\r\n     * Defines the color used to clear the render buffer (Default is (0.2, 0.2, 0.3, 1.0))\r\n     */\r\n    public clearColor: Color4 = new Color4(0.2, 0.2, 0.3, 1.0);\r\n    /**\r\n     * Defines the color used to simulate the ambient color (Default is (0, 0, 0))\r\n     */\r\n    public ambientColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * This is use to store the default BRDF lookup for PBR materials in your scene.\r\n     * It should only be one of the following (if not the default embedded one):\r\n     * * For uncorrelated BRDF (pbr.brdf.useEnergyConservation = false and pbr.brdf.useSmithVisibilityHeightCorrelated = false) : https://assets.babylonjs.com/environments/uncorrelatedBRDF.dds\r\n     * * For correlated BRDF (pbr.brdf.useEnergyConservation = false and pbr.brdf.useSmithVisibilityHeightCorrelated = true) : https://assets.babylonjs.com/environments/correlatedBRDF.dds\r\n     * * For correlated multi scattering BRDF (pbr.brdf.useEnergyConservation = true and pbr.brdf.useSmithVisibilityHeightCorrelated = true) : https://assets.babylonjs.com/environments/correlatedMSBRDF.dds\r\n     * The material properties need to be setup according to the type of texture in use.\r\n     */\r\n    public environmentBRDFTexture: BaseTexture;\r\n\r\n    /**\r\n     * Texture used in all pbr material as the reflection texture.\r\n     * As in the majority of the scene they are the same (exception for multi room and so on),\r\n     * this is easier to reference from here than from all the materials.\r\n     */\r\n    public get environmentTexture(): Nullable<BaseTexture> {\r\n        return this._environmentTexture;\r\n    }\r\n    /**\r\n     * Texture used in all pbr material as the reflection texture.\r\n     * As in the majority of the scene they are the same (exception for multi room and so on),\r\n     * this is easier to set here than in all the materials.\r\n     */\r\n    public set environmentTexture(value: Nullable<BaseTexture>) {\r\n        if (this._environmentTexture === value) {\r\n            return;\r\n        }\r\n\r\n        this._environmentTexture = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag);\r\n    }\r\n\r\n    /**\r\n     * Intensity of the environment in all pbr material.\r\n     * This dims or reinforces the IBL lighting overall (reflection and diffuse).\r\n     * As in the majority of the scene they are the same (exception for multi room and so on),\r\n     * this is easier to reference from here than from all the materials.\r\n     */\r\n    public environmentIntensity: number = 1;\r\n\r\n    /** @internal */\r\n    protected _imageProcessingConfiguration: ImageProcessingConfiguration;\r\n    /**\r\n     * Default image processing configuration used either in the rendering\r\n     * Forward main pass or through the imageProcessingPostProcess if present.\r\n     * As in the majority of the scene they are the same (exception for multi camera),\r\n     * this is easier to reference from here than from all the materials and post process.\r\n     *\r\n     * No setter as we it is a shared configuration, you can set the values instead.\r\n     */\r\n    public get imageProcessingConfiguration(): ImageProcessingConfiguration {\r\n        return this._imageProcessingConfiguration;\r\n    }\r\n\r\n    private _performancePriority = ScenePerformancePriority.BackwardCompatible;\r\n\r\n    /**\r\n     * Observable triggered when the performance priority is changed\r\n     */\r\n    public onScenePerformancePriorityChangedObservable = new Observable<ScenePerformancePriority>();\r\n    /**\r\n     * Gets or sets a value indicating how to treat performance relatively to ease of use and backward compatibility\r\n     */\r\n    public get performancePriority() {\r\n        return this._performancePriority;\r\n    }\r\n\r\n    public set performancePriority(value) {\r\n        if (value === this._performancePriority) {\r\n            return;\r\n        }\r\n\r\n        this._performancePriority = value;\r\n\r\n        switch (value) {\r\n            case ScenePerformancePriority.BackwardCompatible:\r\n                this.skipFrustumClipping = false;\r\n                this._renderingManager.maintainStateBetweenFrames = false;\r\n                this.skipPointerMovePicking = false;\r\n                this.autoClear = true;\r\n                break;\r\n            case ScenePerformancePriority.Intermediate:\r\n                this.skipFrustumClipping = false;\r\n                this._renderingManager.maintainStateBetweenFrames = false;\r\n                this.skipPointerMovePicking = true;\r\n                this.autoClear = false;\r\n                break;\r\n            case ScenePerformancePriority.Aggressive:\r\n                this.skipFrustumClipping = true;\r\n                this._renderingManager.maintainStateBetweenFrames = true;\r\n                this.skipPointerMovePicking = true;\r\n                this.autoClear = false;\r\n                break;\r\n        }\r\n\r\n        this.onScenePerformancePriorityChangedObservable.notifyObservers(value);\r\n    }\r\n\r\n    private _forceWireframe = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if all rendering must be done in wireframe\r\n     */\r\n    public set forceWireframe(value: boolean) {\r\n        if (this._forceWireframe === value) {\r\n            return;\r\n        }\r\n        this._forceWireframe = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_MiscDirtyFlag);\r\n    }\r\n    public get forceWireframe(): boolean {\r\n        return this._forceWireframe;\r\n    }\r\n\r\n    private _skipFrustumClipping = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if we should skip the frustum clipping part of the active meshes selection\r\n     */\r\n    public set skipFrustumClipping(value: boolean) {\r\n        if (this._skipFrustumClipping === value) {\r\n            return;\r\n        }\r\n        this._skipFrustumClipping = value;\r\n    }\r\n    public get skipFrustumClipping(): boolean {\r\n        return this._skipFrustumClipping;\r\n    }\r\n\r\n    private _forcePointsCloud = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if all rendering must be done in point cloud\r\n     */\r\n    public set forcePointsCloud(value: boolean) {\r\n        if (this._forcePointsCloud === value) {\r\n            return;\r\n        }\r\n        this._forcePointsCloud = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_MiscDirtyFlag);\r\n    }\r\n    public get forcePointsCloud(): boolean {\r\n        return this._forcePointsCloud;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the active clipplane 1\r\n     */\r\n    public clipPlane: Nullable<Plane>;\r\n\r\n    /**\r\n     * Gets or sets the active clipplane 2\r\n     */\r\n    public clipPlane2: Nullable<Plane>;\r\n\r\n    /**\r\n     * Gets or sets the active clipplane 3\r\n     */\r\n    public clipPlane3: Nullable<Plane>;\r\n\r\n    /**\r\n     * Gets or sets the active clipplane 4\r\n     */\r\n    public clipPlane4: Nullable<Plane>;\r\n\r\n    /**\r\n     * Gets or sets the active clipplane 5\r\n     */\r\n    public clipPlane5: Nullable<Plane>;\r\n\r\n    /**\r\n     * Gets or sets the active clipplane 6\r\n     */\r\n    public clipPlane6: Nullable<Plane>;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if animations are enabled\r\n     */\r\n    public animationsEnabled = true;\r\n\r\n    private _animationPropertiesOverride: Nullable<AnimationPropertiesOverride> = null;\r\n\r\n    /**\r\n     * Gets or sets the animation properties override\r\n     */\r\n    public get animationPropertiesOverride(): Nullable<AnimationPropertiesOverride> {\r\n        return this._animationPropertiesOverride;\r\n    }\r\n\r\n    public set animationPropertiesOverride(value: Nullable<AnimationPropertiesOverride>) {\r\n        this._animationPropertiesOverride = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if a constant deltatime has to be used\r\n     * This is mostly useful for testing purposes when you do not want the animations to scale with the framerate\r\n     */\r\n    public useConstantAnimationDeltaTime = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if the scene must keep the meshUnderPointer property updated\r\n     * Please note that it requires to run a ray cast through the scene on every frame\r\n     */\r\n    public constantlyUpdateMeshUnderPointer = false;\r\n\r\n    /**\r\n     * Defines the HTML cursor to use when hovering over interactive elements\r\n     */\r\n    public hoverCursor = \"pointer\";\r\n    /**\r\n     * Defines the HTML default cursor to use (empty by default)\r\n     */\r\n    public defaultCursor: string = \"\";\r\n    /**\r\n     * Defines whether cursors are handled by the scene.\r\n     */\r\n    public doNotHandleCursors = false;\r\n    /**\r\n     * This is used to call preventDefault() on pointer down\r\n     * in order to block unwanted artifacts like system double clicks\r\n     */\r\n    public preventDefaultOnPointerDown = true;\r\n\r\n    /**\r\n     * This is used to call preventDefault() on pointer up\r\n     * in order to block unwanted artifacts like system double clicks\r\n     */\r\n    public preventDefaultOnPointerUp = true;\r\n\r\n    // Metadata\r\n    /**\r\n     * Gets or sets user defined metadata\r\n     */\r\n    public metadata: any = null;\r\n\r\n    /**\r\n     * For internal use only. Please do not use.\r\n     */\r\n    public reservedDataStore: any = null;\r\n\r\n    /**\r\n     * Gets the name of the plugin used to load this scene (null by default)\r\n     */\r\n    public loadingPluginName: string;\r\n\r\n    /**\r\n     * Use this array to add regular expressions used to disable offline support for specific urls\r\n     */\r\n    public disableOfflineSupportExceptionRules: RegExp[] = [];\r\n\r\n    /**\r\n     * An event triggered when the scene is disposed.\r\n     */\r\n    public onDisposeObservable = new Observable<Scene>();\r\n\r\n    private _onDisposeObserver: Nullable<Observer<Scene>> = null;\r\n    /** Sets a function to be executed when this scene is disposed. */\r\n    public set onDispose(callback: () => void) {\r\n        if (this._onDisposeObserver) {\r\n            this.onDisposeObservable.remove(this._onDisposeObserver);\r\n        }\r\n        this._onDisposeObserver = this.onDisposeObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered before rendering the scene (right after animations and physics)\r\n     */\r\n    public onBeforeRenderObservable = new Observable<Scene>();\r\n\r\n    private _onBeforeRenderObserver: Nullable<Observer<Scene>> = null;\r\n    /** Sets a function to be executed before rendering this scene */\r\n    public set beforeRender(callback: Nullable<() => void>) {\r\n        if (this._onBeforeRenderObserver) {\r\n            this.onBeforeRenderObservable.remove(this._onBeforeRenderObserver);\r\n        }\r\n        if (callback) {\r\n            this._onBeforeRenderObserver = this.onBeforeRenderObservable.add(callback);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * An event triggered after rendering the scene\r\n     */\r\n    public onAfterRenderObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered after rendering the scene for an active camera (When scene.render is called this will be called after each camera)\r\n     * This is triggered for each \"sub\" camera in a Camera Rig unlike onAfterCameraRenderObservable\r\n     */\r\n    public onAfterRenderCameraObservable = new Observable<Camera>();\r\n\r\n    private _onAfterRenderObserver: Nullable<Observer<Scene>> = null;\r\n    /** Sets a function to be executed after rendering this scene */\r\n    public set afterRender(callback: Nullable<() => void>) {\r\n        if (this._onAfterRenderObserver) {\r\n            this.onAfterRenderObservable.remove(this._onAfterRenderObserver);\r\n        }\r\n\r\n        if (callback) {\r\n            this._onAfterRenderObserver = this.onAfterRenderObservable.add(callback);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * An event triggered before animating the scene\r\n     */\r\n    public onBeforeAnimationsObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered after animations processing\r\n     */\r\n    public onAfterAnimationsObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered before draw calls are ready to be sent\r\n     */\r\n    public onBeforeDrawPhaseObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered after draw calls have been sent\r\n     */\r\n    public onAfterDrawPhaseObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when the scene is ready\r\n     */\r\n    public onReadyObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered before rendering a camera\r\n     */\r\n    public onBeforeCameraRenderObservable = new Observable<Camera>();\r\n\r\n    private _onBeforeCameraRenderObserver: Nullable<Observer<Camera>> = null;\r\n    /** Sets a function to be executed before rendering a camera*/\r\n    public set beforeCameraRender(callback: () => void) {\r\n        if (this._onBeforeCameraRenderObserver) {\r\n            this.onBeforeCameraRenderObservable.remove(this._onBeforeCameraRenderObserver);\r\n        }\r\n\r\n        this._onBeforeCameraRenderObserver = this.onBeforeCameraRenderObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered after rendering a camera\r\n     * This is triggered for the full rig Camera only unlike onAfterRenderCameraObservable\r\n     */\r\n    public onAfterCameraRenderObservable = new Observable<Camera>();\r\n\r\n    private _onAfterCameraRenderObserver: Nullable<Observer<Camera>> = null;\r\n    /** Sets a function to be executed after rendering a camera*/\r\n    public set afterCameraRender(callback: () => void) {\r\n        if (this._onAfterCameraRenderObserver) {\r\n            this.onAfterCameraRenderObservable.remove(this._onAfterCameraRenderObserver);\r\n        }\r\n        this._onAfterCameraRenderObserver = this.onAfterCameraRenderObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered when active meshes evaluation is about to start\r\n     */\r\n    public onBeforeActiveMeshesEvaluationObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when active meshes evaluation is done\r\n     */\r\n    public onAfterActiveMeshesEvaluationObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when particles rendering is about to start\r\n     * Note: This event can be trigger more than once per frame (because particles can be rendered by render target textures as well)\r\n     */\r\n    public onBeforeParticlesRenderingObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when particles rendering is done\r\n     * Note: This event can be trigger more than once per frame (because particles can be rendered by render target textures as well)\r\n     */\r\n    public onAfterParticlesRenderingObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when SceneLoader.Append or SceneLoader.Load or SceneLoader.ImportMesh were successfully executed\r\n     */\r\n    public onDataLoadedObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when a camera is created\r\n     */\r\n    public onNewCameraAddedObservable = new Observable<Camera>();\r\n\r\n    /**\r\n     * An event triggered when a camera is removed\r\n     */\r\n    public onCameraRemovedObservable = new Observable<Camera>();\r\n\r\n    /**\r\n     * An event triggered when a light is created\r\n     */\r\n    public onNewLightAddedObservable = new Observable<Light>();\r\n\r\n    /**\r\n     * An event triggered when a light is removed\r\n     */\r\n    public onLightRemovedObservable = new Observable<Light>();\r\n\r\n    /**\r\n     * An event triggered when a geometry is created\r\n     */\r\n    public onNewGeometryAddedObservable = new Observable<Geometry>();\r\n\r\n    /**\r\n     * An event triggered when a geometry is removed\r\n     */\r\n    public onGeometryRemovedObservable = new Observable<Geometry>();\r\n\r\n    /**\r\n     * An event triggered when a transform node is created\r\n     */\r\n    public onNewTransformNodeAddedObservable = new Observable<TransformNode>();\r\n\r\n    /**\r\n     * An event triggered when a transform node is removed\r\n     */\r\n    public onTransformNodeRemovedObservable = new Observable<TransformNode>();\r\n\r\n    /**\r\n     * An event triggered when a mesh is created\r\n     */\r\n    public onNewMeshAddedObservable = new Observable<AbstractMesh>();\r\n\r\n    /**\r\n     * An event triggered when a mesh is removed\r\n     */\r\n    public onMeshRemovedObservable = new Observable<AbstractMesh>();\r\n\r\n    /**\r\n     * An event triggered when a skeleton is created\r\n     */\r\n    public onNewSkeletonAddedObservable = new Observable<Skeleton>();\r\n\r\n    /**\r\n     * An event triggered when a skeleton is removed\r\n     */\r\n    public onSkeletonRemovedObservable = new Observable<Skeleton>();\r\n\r\n    /**\r\n     * An event triggered when a material is created\r\n     */\r\n    public onNewMaterialAddedObservable = new Observable<Material>();\r\n\r\n    /**\r\n     * An event triggered when a multi material is created\r\n     */\r\n    public onNewMultiMaterialAddedObservable = new Observable<MultiMaterial>();\r\n\r\n    /**\r\n     * An event triggered when a material is removed\r\n     */\r\n    public onMaterialRemovedObservable = new Observable<Material>();\r\n\r\n    /**\r\n     * An event triggered when a multi material is removed\r\n     */\r\n    public onMultiMaterialRemovedObservable = new Observable<MultiMaterial>();\r\n\r\n    /**\r\n     * An event triggered when a texture is created\r\n     */\r\n    public onNewTextureAddedObservable = new Observable<BaseTexture>();\r\n\r\n    /**\r\n     * An event triggered when a texture is removed\r\n     */\r\n    public onTextureRemovedObservable = new Observable<BaseTexture>();\r\n\r\n    /**\r\n     * An event triggered when render targets are about to be rendered\r\n     * Can happen multiple times per frame.\r\n     */\r\n    public onBeforeRenderTargetsRenderObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when render targets were rendered.\r\n     * Can happen multiple times per frame.\r\n     */\r\n    public onAfterRenderTargetsRenderObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered before calculating deterministic simulation step\r\n     */\r\n    public onBeforeStepObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered after calculating deterministic simulation step\r\n     */\r\n    public onAfterStepObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when the activeCamera property is updated\r\n     */\r\n    public onActiveCameraChanged = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when the activeCameras property is updated\r\n     */\r\n    public onActiveCamerasChanged = new Observable<Scene>();\r\n\r\n    /**\r\n     * This Observable will be triggered before rendering each renderingGroup of each rendered camera.\r\n     * The RenderingGroupInfo class contains all the information about the context in which the observable is called\r\n     * If you wish to register an Observer only for a given set of renderingGroup, use the mask with a combination of the renderingGroup index elevated to the power of two (1 for renderingGroup 0, 2 for renderingrOup1, 4 for 2 and 8 for 3)\r\n     */\r\n    public onBeforeRenderingGroupObservable = new Observable<RenderingGroupInfo>();\r\n\r\n    /**\r\n     * This Observable will be triggered after rendering each renderingGroup of each rendered camera.\r\n     * The RenderingGroupInfo class contains all the information about the context in which the observable is called\r\n     * If you wish to register an Observer only for a given set of renderingGroup, use the mask with a combination of the renderingGroup index elevated to the power of two (1 for renderingGroup 0, 2 for renderingrOup1, 4 for 2 and 8 for 3)\r\n     */\r\n    public onAfterRenderingGroupObservable = new Observable<RenderingGroupInfo>();\r\n\r\n    /**\r\n     * This Observable will when a mesh has been imported into the scene.\r\n     */\r\n    public onMeshImportedObservable = new Observable<AbstractMesh>();\r\n\r\n    /**\r\n     * This Observable will when an animation file has been imported into the scene.\r\n     */\r\n    public onAnimationFileImportedObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * Gets or sets a user defined funtion to select LOD from a mesh and a camera.\r\n     * By default this function is undefined and Babylon.js will select LOD based on distance to camera\r\n     */\r\n    public customLODSelector: (mesh: AbstractMesh, camera: Camera) => Nullable<AbstractMesh>;\r\n\r\n    // Animations\r\n\r\n    /** @internal */\r\n    public _registeredForLateAnimationBindings = new SmartArrayNoDuplicate<any>(256);\r\n\r\n    // Pointers\r\n    private _pointerPickingConfiguration = new PointerPickingConfiguration();\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer down event\r\n     */\r\n    public get pointerDownPredicate() {\r\n        return this._pointerPickingConfiguration.pointerDownPredicate;\r\n    }\r\n\r\n    public set pointerDownPredicate(value) {\r\n        this._pointerPickingConfiguration.pointerDownPredicate = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer up event\r\n     */\r\n    public get pointerUpPredicate() {\r\n        return this._pointerPickingConfiguration.pointerUpPredicate;\r\n    }\r\n\r\n    public set pointerUpPredicate(value) {\r\n        this._pointerPickingConfiguration.pointerUpPredicate = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer move event\r\n     */\r\n    public get pointerMovePredicate() {\r\n        return this._pointerPickingConfiguration.pointerMovePredicate;\r\n    }\r\n\r\n    public set pointerMovePredicate(value) {\r\n        this._pointerPickingConfiguration.pointerMovePredicate = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer down event\r\n     */\r\n    public get pointerDownFastCheck() {\r\n        return this._pointerPickingConfiguration.pointerDownFastCheck;\r\n    }\r\n\r\n    public set pointerDownFastCheck(value) {\r\n        this._pointerPickingConfiguration.pointerDownFastCheck = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer up event\r\n     */\r\n    public get pointerUpFastCheck() {\r\n        return this._pointerPickingConfiguration.pointerUpFastCheck;\r\n    }\r\n\r\n    public set pointerUpFastCheck(value) {\r\n        this._pointerPickingConfiguration.pointerUpFastCheck = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer move event\r\n     */\r\n    public get pointerMoveFastCheck() {\r\n        return this._pointerPickingConfiguration.pointerMoveFastCheck;\r\n    }\r\n\r\n    public set pointerMoveFastCheck(value) {\r\n        this._pointerPickingConfiguration.pointerMoveFastCheck = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the user want to entirely skip the picking phase when a pointer move event occurs.\r\n     */\r\n    public get skipPointerMovePicking() {\r\n        return this._pointerPickingConfiguration.skipPointerMovePicking;\r\n    }\r\n\r\n    public set skipPointerMovePicking(value) {\r\n        this._pointerPickingConfiguration.skipPointerMovePicking = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the user want to entirely skip the picking phase when a pointer down event occurs.\r\n     */\r\n    public get skipPointerDownPicking() {\r\n        return this._pointerPickingConfiguration.skipPointerDownPicking;\r\n    }\r\n\r\n    public set skipPointerDownPicking(value) {\r\n        this._pointerPickingConfiguration.skipPointerDownPicking = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the user want to entirely skip the picking phase when a pointer up event occurs.  Off by default.\r\n     */\r\n    public get skipPointerUpPicking() {\r\n        return this._pointerPickingConfiguration.skipPointerUpPicking;\r\n    }\r\n\r\n    public set skipPointerUpPicking(value) {\r\n        this._pointerPickingConfiguration.skipPointerUpPicking = value;\r\n    }\r\n\r\n    /** Callback called when a pointer move is detected */\r\n    public onPointerMove?: (evt: IPointerEvent, pickInfo: PickingInfo, type: PointerEventTypes) => void;\r\n    /** Callback called when a pointer down is detected  */\r\n    public onPointerDown?: (evt: IPointerEvent, pickInfo: PickingInfo, type: PointerEventTypes) => void;\r\n    /** Callback called when a pointer up is detected  */\r\n    public onPointerUp?: (evt: IPointerEvent, pickInfo: Nullable<PickingInfo>, type: PointerEventTypes) => void;\r\n    /** Callback called when a pointer pick is detected */\r\n    public onPointerPick?: (evt: IPointerEvent, pickInfo: PickingInfo) => void;\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate faces for a pointer move event\r\n     */\r\n    public pointerMoveTrianglePredicate: ((p0: Vector3, p1: Vector3, p2: Vector3, ray: Ray) => boolean) | undefined;\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate faces for a pointer down event\r\n     */\r\n    public pointerDownTrianglePredicate: ((p0: Vector3, p1: Vector3, p2: Vector3, ray: Ray) => boolean) | undefined;\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate faces for a pointer up event\r\n     */\r\n    public pointerUpTrianglePredicate: ((p0: Vector3, p1: Vector3, p2: Vector3, ray: Ray) => boolean) | undefined;\r\n\r\n    /**\r\n     * This observable event is triggered when any ponter event is triggered. It is registered during Scene.attachControl() and it is called BEFORE the 3D engine process anything (mesh/sprite picking for instance).\r\n     * You have the possibility to skip the process and the call to onPointerObservable by setting PointerInfoPre.skipOnPointerObservable to true\r\n     */\r\n    public onPrePointerObservable = new Observable<PointerInfoPre>();\r\n\r\n    /**\r\n     * Observable event triggered each time an input event is received from the rendering canvas\r\n     */\r\n    public onPointerObservable = new Observable<PointerInfo>();\r\n\r\n    /**\r\n     * Gets the pointer coordinates without any translation (ie. straight out of the pointer event)\r\n     */\r\n    public get unTranslatedPointer(): Vector2 {\r\n        return this._inputManager.unTranslatedPointer;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the distance in pixel that you have to move to prevent some events. Default is 10 pixels\r\n     */\r\n    public static get DragMovementThreshold() {\r\n        return InputManager.DragMovementThreshold;\r\n    }\r\n\r\n    public static set DragMovementThreshold(value: number) {\r\n        InputManager.DragMovementThreshold = value;\r\n    }\r\n\r\n    /**\r\n     * Time in milliseconds to wait to raise long press events if button is still pressed. Default is 500 ms\r\n     */\r\n    public static get LongPressDelay() {\r\n        return InputManager.LongPressDelay;\r\n    }\r\n\r\n    public static set LongPressDelay(value: number) {\r\n        InputManager.LongPressDelay = value;\r\n    }\r\n\r\n    /**\r\n     * Time in milliseconds to wait to raise long press events if button is still pressed. Default is 300 ms\r\n     */\r\n    public static get DoubleClickDelay() {\r\n        return InputManager.DoubleClickDelay;\r\n    }\r\n\r\n    public static set DoubleClickDelay(value: number) {\r\n        InputManager.DoubleClickDelay = value;\r\n    }\r\n\r\n    /** If you need to check double click without raising a single click at first click, enable this flag */\r\n    public static get ExclusiveDoubleClickMode() {\r\n        return InputManager.ExclusiveDoubleClickMode;\r\n    }\r\n\r\n    public static set ExclusiveDoubleClickMode(value: boolean) {\r\n        InputManager.ExclusiveDoubleClickMode = value;\r\n    }\r\n\r\n    /**\r\n     * Bind the current view position to an effect.\r\n     * @param effect The effect to be bound\r\n     * @param variableName name of the shader variable that will hold the eye position\r\n     * @param isVector3 true to indicates that variableName is a Vector3 and not a Vector4\r\n     * @returns the computed eye position\r\n     */\r\n    public bindEyePosition(effect: Nullable<Effect>, variableName = \"vEyePosition\", isVector3 = false): Vector4 {\r\n        const eyePosition = this._forcedViewPosition ? this._forcedViewPosition : this._mirroredCameraPosition ? this._mirroredCameraPosition : this.activeCamera!.globalPosition;\r\n\r\n        const invertNormal = this.useRightHandedSystem === (this._mirroredCameraPosition != null);\r\n\r\n        TmpVectors.Vector4[0].set(eyePosition.x, eyePosition.y, eyePosition.z, invertNormal ? -1 : 1);\r\n\r\n        if (effect) {\r\n            if (isVector3) {\r\n                effect.setFloat3(variableName, TmpVectors.Vector4[0].x, TmpVectors.Vector4[0].y, TmpVectors.Vector4[0].z);\r\n            } else {\r\n                effect.setVector4(variableName, TmpVectors.Vector4[0]);\r\n            }\r\n        }\r\n\r\n        return TmpVectors.Vector4[0];\r\n    }\r\n\r\n    /**\r\n     * Update the scene ubo before it can be used in rendering processing\r\n     * @returns the scene UniformBuffer\r\n     */\r\n    public finalizeSceneUbo(): UniformBuffer {\r\n        const ubo = this.getSceneUniformBuffer();\r\n        const eyePosition = this.bindEyePosition(null);\r\n        ubo.updateFloat4(\"vEyePosition\", eyePosition.x, eyePosition.y, eyePosition.z, eyePosition.w);\r\n\r\n        ubo.update();\r\n\r\n        return ubo;\r\n    }\r\n\r\n    // Mirror\r\n    /** @internal */\r\n    public _mirroredCameraPosition: Nullable<Vector3>;\r\n\r\n    // Keyboard\r\n\r\n    /**\r\n     * This observable event is triggered when any keyboard event si raised and registered during Scene.attachControl()\r\n     * You have the possibility to skip the process and the call to onKeyboardObservable by setting KeyboardInfoPre.skipOnPointerObservable to true\r\n     */\r\n    public onPreKeyboardObservable = new Observable<KeyboardInfoPre>();\r\n\r\n    /**\r\n     * Observable event triggered each time an keyboard event is received from the hosting window\r\n     */\r\n    public onKeyboardObservable = new Observable<KeyboardInfo>();\r\n\r\n    // Coordinates system\r\n\r\n    private _useRightHandedSystem = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if the scene must use right-handed coordinates system\r\n     */\r\n    public set useRightHandedSystem(value: boolean) {\r\n        if (this._useRightHandedSystem === value) {\r\n            return;\r\n        }\r\n        this._useRightHandedSystem = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_MiscDirtyFlag);\r\n    }\r\n    public get useRightHandedSystem(): boolean {\r\n        return this._useRightHandedSystem;\r\n    }\r\n\r\n    // Deterministic lockstep\r\n    private _timeAccumulator: number = 0;\r\n    private _currentStepId: number = 0;\r\n    private _currentInternalStep: number = 0;\r\n\r\n    /**\r\n     * Sets the step Id used by deterministic lock step\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     * @param newStepId defines the step Id\r\n     */\r\n    public setStepId(newStepId: number): void {\r\n        this._currentStepId = newStepId;\r\n    }\r\n\r\n    /**\r\n     * Gets the step Id used by deterministic lock step\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     * @returns the step Id\r\n     */\r\n    public getStepId(): number {\r\n        return this._currentStepId;\r\n    }\r\n\r\n    /**\r\n     * Gets the internal step used by deterministic lock step\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     * @returns the internal step\r\n     */\r\n    public getInternalStep(): number {\r\n        return this._currentInternalStep;\r\n    }\r\n\r\n    // Fog\r\n\r\n    private _fogEnabled = true;\r\n    /**\r\n     * Gets or sets a boolean indicating if fog is enabled on this scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/environment_introduction#fog\r\n     * (Default is true)\r\n     */\r\n    public set fogEnabled(value: boolean) {\r\n        if (this._fogEnabled === value) {\r\n            return;\r\n        }\r\n        this._fogEnabled = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_MiscDirtyFlag);\r\n    }\r\n    public get fogEnabled(): boolean {\r\n        return this._fogEnabled;\r\n    }\r\n\r\n    private _fogMode = Scene.FOGMODE_NONE;\r\n    /**\r\n     * Gets or sets the fog mode to use\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/environment_introduction#fog\r\n     * | mode | value |\r\n     * | --- | --- |\r\n     * | FOGMODE_NONE | 0 |\r\n     * | FOGMODE_EXP | 1 |\r\n     * | FOGMODE_EXP2 | 2 |\r\n     * | FOGMODE_LINEAR | 3 |\r\n     */\r\n    public set fogMode(value: number) {\r\n        if (this._fogMode === value) {\r\n            return;\r\n        }\r\n        this._fogMode = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_MiscDirtyFlag);\r\n    }\r\n    public get fogMode(): number {\r\n        return this._fogMode;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the fog color to use\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/environment_introduction#fog\r\n     * (Default is Color3(0.2, 0.2, 0.3))\r\n     */\r\n    public fogColor = new Color3(0.2, 0.2, 0.3);\r\n    /**\r\n     * Gets or sets the fog density to use\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/environment_introduction#fog\r\n     * (Default is 0.1)\r\n     */\r\n    public fogDensity = 0.1;\r\n    /**\r\n     * Gets or sets the fog start distance to use\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/environment_introduction#fog\r\n     * (Default is 0)\r\n     */\r\n    public fogStart = 0;\r\n    /**\r\n     * Gets or sets the fog end distance to use\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/environment_introduction#fog\r\n     * (Default is 1000)\r\n     */\r\n    public fogEnd = 1000.0;\r\n\r\n    /**\r\n     * Flag indicating that the frame buffer binding is handled by another component\r\n     */\r\n    public get prePass(): boolean {\r\n        return !!this.prePassRenderer && this.prePassRenderer.defaultRT.enabled;\r\n    }\r\n\r\n    /**\r\n     * Flag indicating if we need to store previous matrices when rendering\r\n     */\r\n    public needsPreviousWorldMatrices = false;\r\n\r\n    // Lights\r\n    private _shadowsEnabled = true;\r\n    /**\r\n     * Gets or sets a boolean indicating if shadows are enabled on this scene\r\n     */\r\n    public set shadowsEnabled(value: boolean) {\r\n        if (this._shadowsEnabled === value) {\r\n            return;\r\n        }\r\n        this._shadowsEnabled = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_LightDirtyFlag);\r\n    }\r\n    public get shadowsEnabled(): boolean {\r\n        return this._shadowsEnabled;\r\n    }\r\n\r\n    private _lightsEnabled = true;\r\n    /**\r\n     * Gets or sets a boolean indicating if lights are enabled on this scene\r\n     */\r\n    public set lightsEnabled(value: boolean) {\r\n        if (this._lightsEnabled === value) {\r\n            return;\r\n        }\r\n        this._lightsEnabled = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_LightDirtyFlag);\r\n    }\r\n\r\n    public get lightsEnabled(): boolean {\r\n        return this._lightsEnabled;\r\n    }\r\n\r\n    private _activeCameras: Nullable<Camera[]>;\r\n    private _unObserveActiveCameras: Nullable<() => void> = null;\r\n\r\n    /** All of the active cameras added to this scene. */\r\n    public get activeCameras(): Nullable<Camera[]> {\r\n        return this._activeCameras;\r\n    }\r\n\r\n    public set activeCameras(cameras: Nullable<Camera[]>) {\r\n        if (this._unObserveActiveCameras) {\r\n            this._unObserveActiveCameras();\r\n            this._unObserveActiveCameras = null;\r\n        }\r\n\r\n        if (cameras) {\r\n            this._unObserveActiveCameras = _ObserveArray(cameras, () => {\r\n                this.onActiveCamerasChanged.notifyObservers(this);\r\n            });\r\n        }\r\n\r\n        this._activeCameras = cameras;\r\n    }\r\n\r\n    /** @internal */\r\n    public _activeCamera: Nullable<Camera>;\r\n    /** Gets or sets the current active camera */\r\n    public get activeCamera(): Nullable<Camera> {\r\n        return this._activeCamera;\r\n    }\r\n\r\n    public set activeCamera(value: Nullable<Camera>) {\r\n        if (value === this._activeCamera) {\r\n            return;\r\n        }\r\n\r\n        this._activeCamera = value;\r\n        this.onActiveCameraChanged.notifyObservers(this);\r\n    }\r\n\r\n    private _defaultMaterial: Material;\r\n\r\n    /** The default material used on meshes when no material is affected */\r\n    public get defaultMaterial(): Material {\r\n        if (!this._defaultMaterial) {\r\n            this._defaultMaterial = Scene.DefaultMaterialFactory(this);\r\n        }\r\n\r\n        return this._defaultMaterial;\r\n    }\r\n\r\n    /** The default material used on meshes when no material is affected */\r\n    public set defaultMaterial(value: Material) {\r\n        this._defaultMaterial = value;\r\n    }\r\n\r\n    // Textures\r\n    private _texturesEnabled = true;\r\n    /**\r\n     * Gets or sets a boolean indicating if textures are enabled on this scene\r\n     */\r\n    public set texturesEnabled(value: boolean) {\r\n        if (this._texturesEnabled === value) {\r\n            return;\r\n        }\r\n        this._texturesEnabled = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag);\r\n    }\r\n\r\n    public get texturesEnabled(): boolean {\r\n        return this._texturesEnabled;\r\n    }\r\n\r\n    // Physics\r\n    /**\r\n     * Gets or sets a boolean indicating if physic engines are enabled on this scene\r\n     */\r\n    public physicsEnabled = true;\r\n\r\n    // Particles\r\n    /**\r\n     * Gets or sets a boolean indicating if particles are enabled on this scene\r\n     */\r\n    public particlesEnabled = true;\r\n\r\n    // Sprites\r\n    /**\r\n     * Gets or sets a boolean indicating if sprites are enabled on this scene\r\n     */\r\n    public spritesEnabled = true;\r\n\r\n    // Skeletons\r\n    private _skeletonsEnabled = true;\r\n    /**\r\n     * Gets or sets a boolean indicating if skeletons are enabled on this scene\r\n     */\r\n    public set skeletonsEnabled(value: boolean) {\r\n        if (this._skeletonsEnabled === value) {\r\n            return;\r\n        }\r\n        this._skeletonsEnabled = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_AttributesDirtyFlag);\r\n    }\r\n\r\n    public get skeletonsEnabled(): boolean {\r\n        return this._skeletonsEnabled;\r\n    }\r\n\r\n    // Lens flares\r\n    /**\r\n     * Gets or sets a boolean indicating if lens flares are enabled on this scene\r\n     */\r\n    public lensFlaresEnabled = true;\r\n\r\n    // Collisions\r\n    /**\r\n     * Gets or sets a boolean indicating if collisions are enabled on this scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_collisions\r\n     */\r\n    public collisionsEnabled = true;\r\n\r\n    private _collisionCoordinator: ICollisionCoordinator;\r\n\r\n    /** @internal */\r\n    public get collisionCoordinator(): ICollisionCoordinator {\r\n        if (!this._collisionCoordinator) {\r\n            this._collisionCoordinator = Scene.CollisionCoordinatorFactory();\r\n            this._collisionCoordinator.init(this);\r\n        }\r\n\r\n        return this._collisionCoordinator;\r\n    }\r\n\r\n    /**\r\n     * Defines the gravity applied to this scene (used only for collisions)\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_collisions\r\n     */\r\n    public gravity = new Vector3(0, -9.807, 0);\r\n\r\n    // Postprocesses\r\n    /**\r\n     * Gets or sets a boolean indicating if postprocesses are enabled on this scene\r\n     */\r\n    public postProcessesEnabled = true;\r\n    /**\r\n     * Gets the current postprocess manager\r\n     */\r\n    public postProcessManager: PostProcessManager;\r\n\r\n    // Customs render targets\r\n    /**\r\n     * Gets or sets a boolean indicating if render targets are enabled on this scene\r\n     */\r\n    public renderTargetsEnabled = true;\r\n    /**\r\n     * Gets or sets a boolean indicating if next render targets must be dumped as image for debugging purposes\r\n     * We recommend not using it and instead rely on Spector.js: http://spector.babylonjs.com\r\n     */\r\n    public dumpNextRenderTargets = false;\r\n    /**\r\n     * The list of user defined render targets added to the scene\r\n     */\r\n    public customRenderTargets: RenderTargetTexture[] = [];\r\n\r\n    /**\r\n     * Defines if texture loading must be delayed\r\n     * If true, textures will only be loaded when they need to be rendered\r\n     */\r\n    public useDelayedTextureLoading: boolean;\r\n\r\n    /**\r\n     * Gets the list of meshes imported to the scene through SceneLoader\r\n     */\r\n    public importedMeshesFiles: string[] = [];\r\n\r\n    // Probes\r\n    /**\r\n     * Gets or sets a boolean indicating if probes are enabled on this scene\r\n     */\r\n    public probesEnabled = true;\r\n\r\n    // Offline support\r\n    /**\r\n     * Gets or sets the current offline provider to use to store scene data\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeCached\r\n     */\r\n    public offlineProvider: IOfflineProvider;\r\n\r\n    /**\r\n     * Gets or sets the action manager associated with the scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n     */\r\n    public actionManager: AbstractActionManager;\r\n\r\n    private _meshesForIntersections = new SmartArrayNoDuplicate<AbstractMesh>(256);\r\n\r\n    // Procedural textures\r\n    /**\r\n     * Gets or sets a boolean indicating if procedural textures are enabled on this scene\r\n     */\r\n    public proceduralTexturesEnabled = true;\r\n\r\n    // Private\r\n    private _engine: Engine;\r\n\r\n    // Performance counters\r\n    private _totalVertices = new PerfCounter();\r\n    /** @internal */\r\n    public _activeIndices = new PerfCounter();\r\n    /** @internal */\r\n    public _activeParticles = new PerfCounter();\r\n    /** @internal */\r\n    public _activeBones = new PerfCounter();\r\n\r\n    private _animationRatio: number;\r\n\r\n    /** @internal */\r\n    public _animationTimeLast: number;\r\n\r\n    /** @internal */\r\n    public _animationTime: number = 0;\r\n\r\n    /**\r\n     * Gets or sets a general scale for animation speed\r\n     * @see https://www.babylonjs-playground.com/#IBU2W7#3\r\n     */\r\n    public animationTimeScale: number = 1;\r\n\r\n    /** @internal */\r\n    public _cachedMaterial: Nullable<Material>;\r\n    /** @internal */\r\n    public _cachedEffect: Nullable<Effect>;\r\n    /** @internal */\r\n    public _cachedVisibility: Nullable<number>;\r\n\r\n    private _renderId = 0;\r\n    private _frameId = 0;\r\n    private _executeWhenReadyTimeoutId: Nullable<ReturnType<typeof setTimeout>> = null;\r\n    private _intermediateRendering = false;\r\n    private _defaultFrameBufferCleared = false;\r\n\r\n    private _viewUpdateFlag = -1;\r\n    private _projectionUpdateFlag = -1;\r\n\r\n    /** @internal */\r\n    public _toBeDisposed = new Array<Nullable<IDisposable>>(256);\r\n    private _activeRequests = new Array<IFileRequest>();\r\n\r\n    /** @internal */\r\n    public _pendingData = new Array();\r\n    private _isDisposed = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that all submeshes of active meshes must be rendered\r\n     * Use this boolean to avoid computing frustum clipping on submeshes (This could help when you are CPU bound)\r\n     */\r\n    public dispatchAllSubMeshesOfActiveMeshes: boolean = false;\r\n    private _activeMeshes = new SmartArray<AbstractMesh>(256);\r\n    private _processedMaterials = new SmartArray<Material>(256);\r\n    private _renderTargets = new SmartArrayNoDuplicate<RenderTargetTexture>(256);\r\n    private _materialsRenderTargets = new SmartArrayNoDuplicate<RenderTargetTexture>(256);\r\n    /** @internal */\r\n    public _activeParticleSystems = new SmartArray<IParticleSystem>(256);\r\n    private _activeSkeletons = new SmartArrayNoDuplicate<Skeleton>(32);\r\n    private _softwareSkinnedMeshes = new SmartArrayNoDuplicate<Mesh>(32);\r\n\r\n    private _renderingManager: RenderingManager;\r\n\r\n    /**\r\n     * Gets the scene's rendering manager\r\n     */\r\n    public get renderingManager(): RenderingManager {\r\n        return this._renderingManager;\r\n    }\r\n\r\n    /** @internal */\r\n    public _activeAnimatables = new Array<Animatable>();\r\n\r\n    private _transformMatrix = Matrix.Zero();\r\n    private _sceneUbo: UniformBuffer;\r\n\r\n    /** @internal */\r\n    public _viewMatrix: Matrix;\r\n    /** @internal */\r\n    public _projectionMatrix: Matrix;\r\n    /** @internal */\r\n    public _forcedViewPosition: Nullable<Vector3>;\r\n\r\n    /** @internal */\r\n    public _frustumPlanes: Plane[];\r\n    /**\r\n     * Gets the list of frustum planes (built from the active camera)\r\n     */\r\n    public get frustumPlanes(): Plane[] {\r\n        return this._frustumPlanes;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if lights must be sorted by priority (off by default)\r\n     * This is useful if there are more lights that the maximum simulteanous authorized\r\n     */\r\n    public requireLightSorting = false;\r\n\r\n    /** @internal */\r\n    public readonly useMaterialMeshMap: boolean;\r\n    /** @internal */\r\n    public readonly useClonedMeshMap: boolean;\r\n\r\n    private _externalData: StringDictionary<Object>;\r\n    private _uid: Nullable<string>;\r\n\r\n    /**\r\n     * @internal\r\n     * Backing store of defined scene components.\r\n     */\r\n    public _components: ISceneComponent[] = [];\r\n\r\n    /**\r\n     * @internal\r\n     * Backing store of defined scene components.\r\n     */\r\n    public _serializableComponents: ISceneSerializableComponent[] = [];\r\n\r\n    /**\r\n     * List of components to register on the next registration step.\r\n     */\r\n    private _transientComponents: ISceneComponent[] = [];\r\n\r\n    /**\r\n     * Registers the transient components if needed.\r\n     */\r\n    private _registerTransientComponents(): void {\r\n        // Register components that have been associated lately to the scene.\r\n        if (this._transientComponents.length > 0) {\r\n            for (const component of this._transientComponents) {\r\n                component.register();\r\n            }\r\n            this._transientComponents.length = 0;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Add a component to the scene.\r\n     * Note that the ccomponent could be registered on th next frame if this is called after\r\n     * the register component stage.\r\n     * @param component Defines the component to add to the scene\r\n     */\r\n    public _addComponent(component: ISceneComponent) {\r\n        this._components.push(component);\r\n        this._transientComponents.push(component);\r\n\r\n        const serializableComponent = component as any;\r\n        if (serializableComponent.addFromContainer && serializableComponent.serialize) {\r\n            this._serializableComponents.push(serializableComponent);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Gets a component from the scene.\r\n     * @param name defines the name of the component to retrieve\r\n     * @returns the component or null if not present\r\n     */\r\n    public _getComponent(name: string): Nullable<ISceneComponent> {\r\n        for (const component of this._components) {\r\n            if (component.name === name) {\r\n                return component;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening before camera updates.\r\n     */\r\n    public _beforeCameraUpdateStage = Stage.Create<SimpleStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening before clear the canvas.\r\n     */\r\n    public _beforeClearStage = Stage.Create<SimpleStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening before clear the canvas.\r\n     */\r\n    public _beforeRenderTargetClearStage = Stage.Create<RenderTargetStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions when collecting render targets for the frame.\r\n     */\r\n    public _gatherRenderTargetsStage = Stage.Create<RenderTargetsStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening for one camera in the frame.\r\n     */\r\n    public _gatherActiveCameraRenderTargetsStage = Stage.Create<RenderTargetsStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening during the per mesh ready checks.\r\n     */\r\n    public _isReadyForMeshStage = Stage.Create<MeshStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening before evaluate active mesh checks.\r\n     */\r\n    public _beforeEvaluateActiveMeshStage = Stage.Create<SimpleStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening during the evaluate sub mesh checks.\r\n     */\r\n    public _evaluateSubMeshStage = Stage.Create<EvaluateSubMeshStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening during the active mesh stage.\r\n     */\r\n    public _preActiveMeshStage = Stage.Create<PreActiveMeshStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening during the per camera render target step.\r\n     */\r\n    public _cameraDrawRenderTargetStage = Stage.Create<CameraStageFrameBufferAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just before the active camera is drawing.\r\n     */\r\n    public _beforeCameraDrawStage = Stage.Create<CameraStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just before a render target is drawing.\r\n     */\r\n    public _beforeRenderTargetDrawStage = Stage.Create<RenderTargetStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just before a rendering group is drawing.\r\n     */\r\n    public _beforeRenderingGroupDrawStage = Stage.Create<RenderingGroupStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just before a mesh is drawing.\r\n     */\r\n    public _beforeRenderingMeshStage = Stage.Create<RenderingMeshStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just after a mesh has been drawn.\r\n     */\r\n    public _afterRenderingMeshStage = Stage.Create<RenderingMeshStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just after a rendering group has been drawn.\r\n     */\r\n    public _afterRenderingGroupDrawStage = Stage.Create<RenderingGroupStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just after the active camera has been drawn.\r\n     */\r\n    public _afterCameraDrawStage = Stage.Create<CameraStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just after the post processing\r\n     */\r\n    public _afterCameraPostProcessStage = Stage.Create<CameraStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just after a render target has been drawn.\r\n     */\r\n    public _afterRenderTargetDrawStage = Stage.Create<RenderTargetStageAction>();\r\n    /**\r\n     * Defines the actions happening just after the post processing on a render target\r\n     */\r\n    public _afterRenderTargetPostProcessStage = Stage.Create<RenderTargetStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just after rendering all cameras and computing intersections.\r\n     */\r\n    public _afterRenderStage = Stage.Create<SimpleStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening when a pointer move event happens.\r\n     */\r\n    public _pointerMoveStage = Stage.Create<PointerMoveStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening when a pointer down event happens.\r\n     */\r\n    public _pointerDownStage = Stage.Create<PointerUpDownStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening when a pointer up event happens.\r\n     */\r\n    public _pointerUpStage = Stage.Create<PointerUpDownStageAction>();\r\n\r\n    /**\r\n     * an optional map from Geometry Id to Geometry index in the 'geometries' array\r\n     */\r\n    private _geometriesByUniqueId: Nullable<{ [uniqueId: string]: number | undefined }> = null;\r\n\r\n    /**\r\n     * Creates a new Scene\r\n     * @param engine defines the engine to use to render this scene\r\n     * @param options defines the scene options\r\n     */\r\n    constructor(engine: Engine, options?: SceneOptions) {\r\n        super();\r\n\r\n        this.activeCameras = [] as Camera[];\r\n\r\n        const fullOptions = {\r\n            useGeometryUniqueIdsMap: true,\r\n            useMaterialMeshMap: true,\r\n            useClonedMeshMap: true,\r\n            virtual: false,\r\n            ...options,\r\n        };\r\n\r\n        engine = this._engine = engine || EngineStore.LastCreatedEngine;\r\n        if (fullOptions.virtual) {\r\n            engine._virtualScenes.push(this);\r\n        } else {\r\n            EngineStore._LastCreatedScene = this;\r\n            engine.scenes.push(this);\r\n        }\r\n\r\n        this._uid = null;\r\n\r\n        this._renderingManager = new RenderingManager(this);\r\n\r\n        if (PostProcessManager) {\r\n            this.postProcessManager = new PostProcessManager(this);\r\n        }\r\n\r\n        if (IsWindowObjectExist()) {\r\n            this.attachControl();\r\n        }\r\n\r\n        // Uniform Buffer\r\n        this._createUbo();\r\n\r\n        // Default Image processing definition\r\n        if (ImageProcessingConfiguration) {\r\n            this._imageProcessingConfiguration = new ImageProcessingConfiguration();\r\n        }\r\n\r\n        this.setDefaultCandidateProviders();\r\n\r\n        if (fullOptions.useGeometryUniqueIdsMap) {\r\n            this._geometriesByUniqueId = {};\r\n        }\r\n\r\n        this.useMaterialMeshMap = fullOptions.useMaterialMeshMap;\r\n        this.useClonedMeshMap = fullOptions.useClonedMeshMap;\r\n\r\n        if (!options || !options.virtual) {\r\n            engine.onNewSceneAddedObservable.notifyObservers(this);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"Scene\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"Scene\";\r\n    }\r\n\r\n    private _defaultMeshCandidates: ISmartArrayLike<AbstractMesh> = {\r\n        data: [],\r\n        length: 0,\r\n    };\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getDefaultMeshCandidates(): ISmartArrayLike<AbstractMesh> {\r\n        this._defaultMeshCandidates.data = this.meshes;\r\n        this._defaultMeshCandidates.length = this.meshes.length;\r\n        return this._defaultMeshCandidates;\r\n    }\r\n\r\n    private _defaultSubMeshCandidates: ISmartArrayLike<SubMesh> = {\r\n        data: [],\r\n        length: 0,\r\n    };\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getDefaultSubMeshCandidates(mesh: AbstractMesh): ISmartArrayLike<SubMesh> {\r\n        this._defaultSubMeshCandidates.data = mesh.subMeshes;\r\n        this._defaultSubMeshCandidates.length = mesh.subMeshes.length;\r\n        return this._defaultSubMeshCandidates;\r\n    }\r\n\r\n    /**\r\n     * Sets the default candidate providers for the scene.\r\n     * This sets the getActiveMeshCandidates, getActiveSubMeshCandidates, getIntersectingSubMeshCandidates\r\n     * and getCollidingSubMeshCandidates to their default function\r\n     */\r\n    public setDefaultCandidateProviders(): void {\r\n        this.getActiveMeshCandidates = () => this._getDefaultMeshCandidates();\r\n        this.getActiveSubMeshCandidates = (mesh: AbstractMesh) => this._getDefaultSubMeshCandidates(mesh);\r\n        this.getIntersectingSubMeshCandidates = (mesh: AbstractMesh, localRay: Ray) => this._getDefaultSubMeshCandidates(mesh);\r\n        this.getCollidingSubMeshCandidates = (mesh: AbstractMesh, collider: Collider) => this._getDefaultSubMeshCandidates(mesh);\r\n    }\r\n\r\n    /**\r\n     * Gets the mesh that is currently under the pointer\r\n     */\r\n    public get meshUnderPointer(): Nullable<AbstractMesh> {\r\n        return this._inputManager.meshUnderPointer;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the current on-screen X position of the pointer\r\n     */\r\n    public get pointerX(): number {\r\n        return this._inputManager.pointerX;\r\n    }\r\n\r\n    public set pointerX(value: number) {\r\n        this._inputManager.pointerX = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the current on-screen Y position of the pointer\r\n     */\r\n    public get pointerY(): number {\r\n        return this._inputManager.pointerY;\r\n    }\r\n\r\n    public set pointerY(value: number) {\r\n        this._inputManager.pointerY = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the cached material (ie. the latest rendered one)\r\n     * @returns the cached material\r\n     */\r\n    public getCachedMaterial(): Nullable<Material> {\r\n        return this._cachedMaterial;\r\n    }\r\n\r\n    /**\r\n     * Gets the cached effect (ie. the latest rendered one)\r\n     * @returns the cached effect\r\n     */\r\n    public getCachedEffect(): Nullable<Effect> {\r\n        return this._cachedEffect;\r\n    }\r\n\r\n    /**\r\n     * Gets the cached visibility state (ie. the latest rendered one)\r\n     * @returns the cached visibility state\r\n     */\r\n    public getCachedVisibility(): Nullable<number> {\r\n        return this._cachedVisibility;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the current material / effect / visibility must be bind again\r\n     * @param material defines the current material\r\n     * @param effect defines the current effect\r\n     * @param visibility defines the current visibility state\r\n     * @returns true if one parameter is not cached\r\n     */\r\n    public isCachedMaterialInvalid(material: Material, effect: Effect, visibility: number = 1) {\r\n        return this._cachedEffect !== effect || this._cachedMaterial !== material || this._cachedVisibility !== visibility;\r\n    }\r\n\r\n    /**\r\n     * Gets the engine associated with the scene\r\n     * @returns an Engine\r\n     */\r\n    public getEngine(): Engine {\r\n        return this._engine;\r\n    }\r\n\r\n    /**\r\n     * Gets the total number of vertices rendered per frame\r\n     * @returns the total number of vertices rendered per frame\r\n     */\r\n    public getTotalVertices(): number {\r\n        return this._totalVertices.current;\r\n    }\r\n\r\n    /**\r\n     * Gets the performance counter for total vertices\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimize_your_scene#instrumentation\r\n     */\r\n    public get totalVerticesPerfCounter(): PerfCounter {\r\n        return this._totalVertices;\r\n    }\r\n\r\n    /**\r\n     * Gets the total number of active indices rendered per frame (You can deduce the number of rendered triangles by dividing this number by 3)\r\n     * @returns the total number of active indices rendered per frame\r\n     */\r\n    public getActiveIndices(): number {\r\n        return this._activeIndices.current;\r\n    }\r\n\r\n    /**\r\n     * Gets the performance counter for active indices\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimize_your_scene#instrumentation\r\n     */\r\n    public get totalActiveIndicesPerfCounter(): PerfCounter {\r\n        return this._activeIndices;\r\n    }\r\n\r\n    /**\r\n     * Gets the total number of active particles rendered per frame\r\n     * @returns the total number of active particles rendered per frame\r\n     */\r\n    public getActiveParticles(): number {\r\n        return this._activeParticles.current;\r\n    }\r\n\r\n    /**\r\n     * Gets the performance counter for active particles\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimize_your_scene#instrumentation\r\n     */\r\n    public get activeParticlesPerfCounter(): PerfCounter {\r\n        return this._activeParticles;\r\n    }\r\n\r\n    /**\r\n     * Gets the total number of active bones rendered per frame\r\n     * @returns the total number of active bones rendered per frame\r\n     */\r\n    public getActiveBones(): number {\r\n        return this._activeBones.current;\r\n    }\r\n\r\n    /**\r\n     * Gets the performance counter for active bones\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimize_your_scene#instrumentation\r\n     */\r\n    public get activeBonesPerfCounter(): PerfCounter {\r\n        return this._activeBones;\r\n    }\r\n\r\n    /**\r\n     * Gets the array of active meshes\r\n     * @returns an array of AbstractMesh\r\n     */\r\n    public getActiveMeshes(): SmartArray<AbstractMesh> {\r\n        return this._activeMeshes;\r\n    }\r\n\r\n    /**\r\n     * Gets the animation ratio (which is 1.0 is the scene renders at 60fps and 2 if the scene renders at 30fps, etc.)\r\n     * @returns a number\r\n     */\r\n    public getAnimationRatio(): number {\r\n        return this._animationRatio !== undefined ? this._animationRatio : 1;\r\n    }\r\n\r\n    /**\r\n     * Gets an unique Id for the current render phase\r\n     * @returns a number\r\n     */\r\n    public getRenderId(): number {\r\n        return this._renderId;\r\n    }\r\n\r\n    /**\r\n     * Gets an unique Id for the current frame\r\n     * @returns a number\r\n     */\r\n    public getFrameId(): number {\r\n        return this._frameId;\r\n    }\r\n\r\n    /** Call this function if you want to manually increment the render Id*/\r\n    public incrementRenderId(): void {\r\n        this._renderId++;\r\n    }\r\n\r\n    private _createUbo(): void {\r\n        this.setSceneUniformBuffer(this.createSceneUniformBuffer());\r\n    }\r\n\r\n    /**\r\n     * Use this method to simulate a pointer move on a mesh\r\n     * The pickResult parameter can be obtained from a scene.pick or scene.pickWithRay\r\n     * @param pickResult pickingInfo of the object wished to simulate pointer event on\r\n     * @param pointerEventInit pointer event state to be used when simulating the pointer event (eg. pointer id for multitouch)\r\n     * @returns the current scene\r\n     */\r\n    public simulatePointerMove(pickResult: PickingInfo, pointerEventInit?: PointerEventInit): Scene {\r\n        this._inputManager.simulatePointerMove(pickResult, pointerEventInit);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Use this method to simulate a pointer down on a mesh\r\n     * The pickResult parameter can be obtained from a scene.pick or scene.pickWithRay\r\n     * @param pickResult pickingInfo of the object wished to simulate pointer event on\r\n     * @param pointerEventInit pointer event state to be used when simulating the pointer event (eg. pointer id for multitouch)\r\n     * @returns the current scene\r\n     */\r\n    public simulatePointerDown(pickResult: PickingInfo, pointerEventInit?: PointerEventInit): Scene {\r\n        this._inputManager.simulatePointerDown(pickResult, pointerEventInit);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Use this method to simulate a pointer up on a mesh\r\n     * The pickResult parameter can be obtained from a scene.pick or scene.pickWithRay\r\n     * @param pickResult pickingInfo of the object wished to simulate pointer event on\r\n     * @param pointerEventInit pointer event state to be used when simulating the pointer event (eg. pointer id for multitouch)\r\n     * @param doubleTap indicates that the pointer up event should be considered as part of a double click (false by default)\r\n     * @returns the current scene\r\n     */\r\n    public simulatePointerUp(pickResult: PickingInfo, pointerEventInit?: PointerEventInit, doubleTap?: boolean): Scene {\r\n        this._inputManager.simulatePointerUp(pickResult, pointerEventInit, doubleTap);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the current pointer event is captured (meaning that the scene has already handled the pointer down)\r\n     * @param pointerId defines the pointer id to use in a multi-touch scenario (0 by default)\r\n     * @returns true if the pointer was captured\r\n     */\r\n    public isPointerCaptured(pointerId = 0): boolean {\r\n        return this._inputManager.isPointerCaptured(pointerId);\r\n    }\r\n\r\n    /**\r\n     * Attach events to the canvas (To handle actionManagers triggers and raise onPointerMove, onPointerDown and onPointerUp\r\n     * @param attachUp defines if you want to attach events to pointerup\r\n     * @param attachDown defines if you want to attach events to pointerdown\r\n     * @param attachMove defines if you want to attach events to pointermove\r\n     */\r\n    public attachControl(attachUp = true, attachDown = true, attachMove = true): void {\r\n        this._inputManager.attachControl(attachUp, attachDown, attachMove);\r\n    }\r\n\r\n    /** Detaches all event handlers*/\r\n    public detachControl() {\r\n        this._inputManager.detachControl();\r\n    }\r\n\r\n    /**\r\n     * This function will check if the scene can be rendered (textures are loaded, shaders are compiled)\r\n     * Delay loaded resources are not taking in account\r\n     * @param checkRenderTargets true to also check that the meshes rendered as part of a render target are ready (default: true)\r\n     * @returns true if all required resources are ready\r\n     */\r\n    public isReady(checkRenderTargets = true): boolean {\r\n        if (this._isDisposed) {\r\n            return false;\r\n        }\r\n\r\n        let index: number;\r\n        const engine = this.getEngine();\r\n\r\n        const currentRenderPassId = engine.currentRenderPassId;\r\n\r\n        engine.currentRenderPassId = this.activeCamera?.renderPassId ?? currentRenderPassId;\r\n\r\n        let isReady = true;\r\n\r\n        // Pending data\r\n        if (this._pendingData.length > 0) {\r\n            isReady = false;\r\n        }\r\n\r\n        // Ensures that the pre-pass renderer is enabled if it is to be enabled.\r\n        this.prePassRenderer?.update();\r\n\r\n        // OIT\r\n        if (this.useOrderIndependentTransparency && this.depthPeelingRenderer) {\r\n            isReady &&= this.depthPeelingRenderer.isReady();\r\n        }\r\n\r\n        // Meshes\r\n        if (checkRenderTargets) {\r\n            this._processedMaterials.reset();\r\n            this._materialsRenderTargets.reset();\r\n        }\r\n\r\n        for (index = 0; index < this.meshes.length; index++) {\r\n            const mesh = this.meshes[index];\r\n\r\n            if (!mesh.subMeshes || mesh.subMeshes.length === 0) {\r\n                continue;\r\n            }\r\n\r\n            // Do not stop at the first encountered \"unready\" object as we want to ensure\r\n            // all materials are starting off their compilation in parallel.\r\n            if (!mesh.isReady(true)) {\r\n                isReady = false;\r\n                continue;\r\n            }\r\n\r\n            const hardwareInstancedRendering =\r\n                mesh.hasThinInstances ||\r\n                mesh.getClassName() === \"InstancedMesh\" ||\r\n                mesh.getClassName() === \"InstancedLinesMesh\" ||\r\n                (engine.getCaps().instancedArrays && (<Mesh>mesh).instances.length > 0);\r\n            // Is Ready For Mesh\r\n            for (const step of this._isReadyForMeshStage) {\r\n                if (!step.action(mesh, hardwareInstancedRendering)) {\r\n                    isReady = false;\r\n                }\r\n            }\r\n\r\n            if (!checkRenderTargets) {\r\n                continue;\r\n            }\r\n\r\n            const mat = mesh.material || this.defaultMaterial;\r\n            if (mat) {\r\n                if (mat._storeEffectOnSubMeshes) {\r\n                    for (const subMesh of mesh.subMeshes) {\r\n                        const material = subMesh.getMaterial();\r\n                        if (material && material.hasRenderTargetTextures && material.getRenderTargetTextures != null) {\r\n                            if (this._processedMaterials.indexOf(material) === -1) {\r\n                                this._processedMaterials.push(material);\r\n\r\n                                this._materialsRenderTargets.concatWithNoDuplicate(material.getRenderTargetTextures!());\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    if (mat.hasRenderTargetTextures && mat.getRenderTargetTextures != null) {\r\n                        if (this._processedMaterials.indexOf(mat) === -1) {\r\n                            this._processedMaterials.push(mat);\r\n\r\n                            this._materialsRenderTargets.concatWithNoDuplicate(mat.getRenderTargetTextures!());\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Render targets\r\n        if (checkRenderTargets) {\r\n            for (index = 0; index < this._materialsRenderTargets.length; ++index) {\r\n                const rtt = this._materialsRenderTargets.data[index];\r\n                if (!rtt.isReadyForRendering()) {\r\n                    isReady = false;\r\n                }\r\n            }\r\n        }\r\n\r\n        // Geometries\r\n        for (index = 0; index < this.geometries.length; index++) {\r\n            const geometry = this.geometries[index];\r\n\r\n            if (geometry.delayLoadState === Constants.DELAYLOADSTATE_LOADING) {\r\n                isReady = false;\r\n            }\r\n        }\r\n\r\n        // Post-processes\r\n        if (this.activeCameras && this.activeCameras.length > 0) {\r\n            for (const camera of this.activeCameras) {\r\n                if (!camera.isReady(true)) {\r\n                    isReady = false;\r\n                }\r\n            }\r\n        } else if (this.activeCamera) {\r\n            if (!this.activeCamera.isReady(true)) {\r\n                isReady = false;\r\n            }\r\n        }\r\n\r\n        // Particles\r\n        for (const particleSystem of this.particleSystems) {\r\n            if (!particleSystem.isReady()) {\r\n                isReady = false;\r\n            }\r\n        }\r\n\r\n        // Layers\r\n        if (this.layers) {\r\n            for (const layer of this.layers) {\r\n                if (!layer.isReady()) {\r\n                    isReady = false;\r\n                }\r\n            }\r\n        }\r\n\r\n        // Effects\r\n        if (!engine.areAllEffectsReady()) {\r\n            isReady = false;\r\n        }\r\n\r\n        engine.currentRenderPassId = currentRenderPassId;\r\n\r\n        return isReady;\r\n    }\r\n\r\n    /** Resets all cached information relative to material (including effect and visibility) */\r\n    public resetCachedMaterial(): void {\r\n        this._cachedMaterial = null;\r\n        this._cachedEffect = null;\r\n        this._cachedVisibility = null;\r\n    }\r\n\r\n    /**\r\n     * Registers a function to be called before every frame render\r\n     * @param func defines the function to register\r\n     */\r\n    public registerBeforeRender(func: () => void): void {\r\n        this.onBeforeRenderObservable.add(func);\r\n    }\r\n\r\n    /**\r\n     * Unregisters a function called before every frame render\r\n     * @param func defines the function to unregister\r\n     */\r\n    public unregisterBeforeRender(func: () => void): void {\r\n        this.onBeforeRenderObservable.removeCallback(func);\r\n    }\r\n\r\n    /**\r\n     * Registers a function to be called after every frame render\r\n     * @param func defines the function to register\r\n     */\r\n    public registerAfterRender(func: () => void): void {\r\n        this.onAfterRenderObservable.add(func);\r\n    }\r\n\r\n    /**\r\n     * Unregisters a function called after every frame render\r\n     * @param func defines the function to unregister\r\n     */\r\n    public unregisterAfterRender(func: () => void): void {\r\n        this.onAfterRenderObservable.removeCallback(func);\r\n    }\r\n\r\n    private _executeOnceBeforeRender(func: () => void): void {\r\n        const execFunc = () => {\r\n            func();\r\n            setTimeout(() => {\r\n                this.unregisterBeforeRender(execFunc);\r\n            });\r\n        };\r\n        this.registerBeforeRender(execFunc);\r\n    }\r\n\r\n    /**\r\n     * The provided function will run before render once and will be disposed afterwards.\r\n     * A timeout delay can be provided so that the function will be executed in N ms.\r\n     * The timeout is using the browser's native setTimeout so time percision cannot be guaranteed.\r\n     * @param func The function to be executed.\r\n     * @param timeout optional delay in ms\r\n     */\r\n    public executeOnceBeforeRender(func: () => void, timeout?: number): void {\r\n        if (timeout !== undefined) {\r\n            setTimeout(() => {\r\n                this._executeOnceBeforeRender(func);\r\n            }, timeout);\r\n        } else {\r\n            this._executeOnceBeforeRender(func);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * This function can help adding any object to the list of data awaited to be ready in order to check for a complete scene loading.\r\n     * @param data defines the object to wait for\r\n     */\r\n    public addPendingData(data: any): void {\r\n        this._pendingData.push(data);\r\n    }\r\n\r\n    /**\r\n     * Remove a pending data from the loading list which has previously been added with addPendingData.\r\n     * @param data defines the object to remove from the pending list\r\n     */\r\n    public removePendingData(data: any): void {\r\n        const wasLoading = this.isLoading;\r\n        const index = this._pendingData.indexOf(data);\r\n\r\n        if (index !== -1) {\r\n            this._pendingData.splice(index, 1);\r\n        }\r\n\r\n        if (wasLoading && !this.isLoading) {\r\n            this.onDataLoadedObservable.notifyObservers(this);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the number of items waiting to be loaded\r\n     * @returns the number of items waiting to be loaded\r\n     */\r\n    public getWaitingItemsCount(): number {\r\n        return this._pendingData.length;\r\n    }\r\n\r\n    /**\r\n     * Returns a boolean indicating if the scene is still loading data\r\n     */\r\n    public get isLoading(): boolean {\r\n        return this._pendingData.length > 0;\r\n    }\r\n\r\n    /**\r\n     * Registers a function to be executed when the scene is ready\r\n     * @param func - the function to be executed\r\n     * @param checkRenderTargets true to also check that the meshes rendered as part of a render target are ready (default: false)\r\n     */\r\n    public executeWhenReady(func: () => void, checkRenderTargets = false): void {\r\n        this.onReadyObservable.addOnce(func);\r\n\r\n        if (this._executeWhenReadyTimeoutId !== null) {\r\n            return;\r\n        }\r\n\r\n        this._checkIsReady(checkRenderTargets);\r\n    }\r\n\r\n    /**\r\n     * Returns a promise that resolves when the scene is ready\r\n     * @param checkRenderTargets true to also check that the meshes rendered as part of a render target are ready (default: false)\r\n     * @returns A promise that resolves when the scene is ready\r\n     */\r\n    public whenReadyAsync(checkRenderTargets = false): Promise<void> {\r\n        return new Promise((resolve) => {\r\n            this.executeWhenReady(() => {\r\n                resolve();\r\n            }, checkRenderTargets);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _checkIsReady(checkRenderTargets = false) {\r\n        this._registerTransientComponents();\r\n\r\n        if (this.isReady(checkRenderTargets)) {\r\n            this.onReadyObservable.notifyObservers(this);\r\n\r\n            this.onReadyObservable.clear();\r\n            this._executeWhenReadyTimeoutId = null;\r\n            return;\r\n        }\r\n\r\n        if (this._isDisposed) {\r\n            this.onReadyObservable.clear();\r\n            this._executeWhenReadyTimeoutId = null;\r\n            return;\r\n        }\r\n\r\n        this._executeWhenReadyTimeoutId = setTimeout(() => {\r\n            // Ensure materials effects are checked outside render loops\r\n            this.incrementRenderId();\r\n            this._checkIsReady(checkRenderTargets);\r\n        }, 100);\r\n    }\r\n\r\n    /**\r\n     * Gets all animatable attached to the scene\r\n     */\r\n    public get animatables(): Animatable[] {\r\n        return this._activeAnimatables;\r\n    }\r\n\r\n    /**\r\n     * Resets the last animation time frame.\r\n     * Useful to override when animations start running when loading a scene for the first time.\r\n     */\r\n    public resetLastAnimationTimeFrame(): void {\r\n        this._animationTimeLast = PrecisionDate.Now;\r\n    }\r\n\r\n    // Matrix\r\n\r\n    /**\r\n     * Gets the current view matrix\r\n     * @returns a Matrix\r\n     */\r\n    public getViewMatrix(): Matrix {\r\n        return this._viewMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the current projection matrix\r\n     * @returns a Matrix\r\n     */\r\n    public getProjectionMatrix(): Matrix {\r\n        return this._projectionMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the current transform matrix\r\n     * @returns a Matrix made of View * Projection\r\n     */\r\n    public getTransformMatrix(): Matrix {\r\n        return this._transformMatrix;\r\n    }\r\n\r\n    /**\r\n     * Sets the current transform matrix\r\n     * @param viewL defines the View matrix to use\r\n     * @param projectionL defines the Projection matrix to use\r\n     * @param viewR defines the right View matrix to use (if provided)\r\n     * @param projectionR defines the right Projection matrix to use (if provided)\r\n     */\r\n    public setTransformMatrix(viewL: Matrix, projectionL: Matrix, viewR?: Matrix, projectionR?: Matrix): void {\r\n        // clear the multiviewSceneUbo if no viewR and projectionR are defined\r\n        if (!viewR && !projectionR && this._multiviewSceneUbo) {\r\n            this._multiviewSceneUbo.dispose();\r\n            this._multiviewSceneUbo = null;\r\n        }\r\n        if (this._viewUpdateFlag === viewL.updateFlag && this._projectionUpdateFlag === projectionL.updateFlag) {\r\n            return;\r\n        }\r\n\r\n        this._viewUpdateFlag = viewL.updateFlag;\r\n        this._projectionUpdateFlag = projectionL.updateFlag;\r\n        this._viewMatrix = viewL;\r\n        this._projectionMatrix = projectionL;\r\n\r\n        this._viewMatrix.multiplyToRef(this._projectionMatrix, this._transformMatrix);\r\n\r\n        // Update frustum\r\n        if (!this._frustumPlanes) {\r\n            this._frustumPlanes = Frustum.GetPlanes(this._transformMatrix);\r\n        } else {\r\n            Frustum.GetPlanesToRef(this._transformMatrix, this._frustumPlanes);\r\n        }\r\n\r\n        if (this._multiviewSceneUbo && this._multiviewSceneUbo.useUbo) {\r\n            this._updateMultiviewUbo(viewR, projectionR);\r\n        } else if (this._sceneUbo.useUbo) {\r\n            this._sceneUbo.updateMatrix(\"viewProjection\", this._transformMatrix);\r\n            this._sceneUbo.updateMatrix(\"view\", this._viewMatrix);\r\n            this._sceneUbo.updateMatrix(\"projection\", this._projectionMatrix);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the uniform buffer used to store scene data\r\n     * @returns a UniformBuffer\r\n     */\r\n    public getSceneUniformBuffer(): UniformBuffer {\r\n        return this._multiviewSceneUbo ? this._multiviewSceneUbo : this._sceneUbo;\r\n    }\r\n\r\n    /**\r\n     * Creates a scene UBO\r\n     * @param name name of the uniform buffer (optional, for debugging purpose only)\r\n     * @returns a new ubo\r\n     */\r\n    public createSceneUniformBuffer(name?: string): UniformBuffer {\r\n        const sceneUbo = new UniformBuffer(this._engine, undefined, false, name ?? \"scene\");\r\n        sceneUbo.addUniform(\"viewProjection\", 16);\r\n        sceneUbo.addUniform(\"view\", 16);\r\n        sceneUbo.addUniform(\"projection\", 16);\r\n        sceneUbo.addUniform(\"vEyePosition\", 4);\r\n\r\n        return sceneUbo;\r\n    }\r\n\r\n    /**\r\n     * Sets the scene ubo\r\n     * @param ubo the ubo to set for the scene\r\n     */\r\n    public setSceneUniformBuffer(ubo: UniformBuffer): void {\r\n        this._sceneUbo = ubo;\r\n        this._viewUpdateFlag = -1;\r\n        this._projectionUpdateFlag = -1;\r\n    }\r\n\r\n    /**\r\n     * Gets an unique (relatively to the current scene) Id\r\n     * @returns an unique number for the scene\r\n     */\r\n    public getUniqueId() {\r\n        return UniqueIdGenerator.UniqueId;\r\n    }\r\n\r\n    /**\r\n     * Add a mesh to the list of scene's meshes\r\n     * @param newMesh defines the mesh to add\r\n     * @param recursive if all child meshes should also be added to the scene\r\n     */\r\n    public addMesh(newMesh: AbstractMesh, recursive = false) {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n\r\n        this.meshes.push(newMesh);\r\n\r\n        newMesh._resyncLightSources();\r\n\r\n        if (!newMesh.parent) {\r\n            newMesh._addToSceneRootNodes();\r\n        }\r\n\r\n        this.onNewMeshAddedObservable.notifyObservers(newMesh);\r\n\r\n        if (recursive) {\r\n            newMesh.getChildMeshes().forEach((m) => {\r\n                this.addMesh(m);\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh for the list of scene's meshes\r\n     * @param toRemove defines the mesh to remove\r\n     * @param recursive if all child meshes should also be removed from the scene\r\n     * @returns the index where the mesh was in the mesh list\r\n     */\r\n    public removeMesh(toRemove: AbstractMesh, recursive = false): number {\r\n        const index = this.meshes.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            // Remove from the scene if mesh found\r\n            this.meshes[index] = this.meshes[this.meshes.length - 1];\r\n            this.meshes.pop();\r\n\r\n            if (!toRemove.parent) {\r\n                toRemove._removeFromSceneRootNodes();\r\n            }\r\n        }\r\n\r\n        this._inputManager._invalidateMesh(toRemove);\r\n\r\n        this.onMeshRemovedObservable.notifyObservers(toRemove);\r\n        if (recursive) {\r\n            toRemove.getChildMeshes().forEach((m) => {\r\n                this.removeMesh(m);\r\n            });\r\n        }\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Add a transform node to the list of scene's transform nodes\r\n     * @param newTransformNode defines the transform node to add\r\n     */\r\n    public addTransformNode(newTransformNode: TransformNode) {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n\r\n        if (newTransformNode.getScene() === this && newTransformNode._indexInSceneTransformNodesArray !== -1) {\r\n            // Already there?\r\n            return;\r\n        }\r\n\r\n        newTransformNode._indexInSceneTransformNodesArray = this.transformNodes.length;\r\n        this.transformNodes.push(newTransformNode);\r\n\r\n        if (!newTransformNode.parent) {\r\n            newTransformNode._addToSceneRootNodes();\r\n        }\r\n\r\n        this.onNewTransformNodeAddedObservable.notifyObservers(newTransformNode);\r\n    }\r\n\r\n    /**\r\n     * Remove a transform node for the list of scene's transform nodes\r\n     * @param toRemove defines the transform node to remove\r\n     * @returns the index where the transform node was in the transform node list\r\n     */\r\n    public removeTransformNode(toRemove: TransformNode): number {\r\n        const index = toRemove._indexInSceneTransformNodesArray;\r\n        if (index !== -1) {\r\n            if (index !== this.transformNodes.length - 1) {\r\n                const lastNode = this.transformNodes[this.transformNodes.length - 1];\r\n                this.transformNodes[index] = lastNode;\r\n                lastNode._indexInSceneTransformNodesArray = index;\r\n            }\r\n\r\n            toRemove._indexInSceneTransformNodesArray = -1;\r\n            this.transformNodes.pop();\r\n            if (!toRemove.parent) {\r\n                toRemove._removeFromSceneRootNodes();\r\n            }\r\n        }\r\n\r\n        this.onTransformNodeRemovedObservable.notifyObservers(toRemove);\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Remove a skeleton for the list of scene's skeletons\r\n     * @param toRemove defines the skeleton to remove\r\n     * @returns the index where the skeleton was in the skeleton list\r\n     */\r\n    public removeSkeleton(toRemove: Skeleton): number {\r\n        const index = this.skeletons.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            // Remove from the scene if found\r\n            this.skeletons.splice(index, 1);\r\n            this.onSkeletonRemovedObservable.notifyObservers(toRemove);\r\n\r\n            // Clean active container\r\n            this._executeActiveContainerCleanup(this._activeSkeletons);\r\n        }\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Remove a morph target for the list of scene's morph targets\r\n     * @param toRemove defines the morph target to remove\r\n     * @returns the index where the morph target was in the morph target list\r\n     */\r\n    public removeMorphTargetManager(toRemove: MorphTargetManager): number {\r\n        const index = this.morphTargetManagers.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            // Remove from the scene if found\r\n            this.morphTargetManagers.splice(index, 1);\r\n        }\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Remove a light for the list of scene's lights\r\n     * @param toRemove defines the light to remove\r\n     * @returns the index where the light was in the light list\r\n     */\r\n    public removeLight(toRemove: Light): number {\r\n        const index = this.lights.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            // Remove from meshes\r\n            for (const mesh of this.meshes) {\r\n                mesh._removeLightSource(toRemove, false);\r\n            }\r\n\r\n            // Remove from the scene if mesh found\r\n            this.lights.splice(index, 1);\r\n            this.sortLightsByPriority();\r\n\r\n            if (!toRemove.parent) {\r\n                toRemove._removeFromSceneRootNodes();\r\n            }\r\n        }\r\n        this.onLightRemovedObservable.notifyObservers(toRemove);\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Remove a camera for the list of scene's cameras\r\n     * @param toRemove defines the camera to remove\r\n     * @returns the index where the camera was in the camera list\r\n     */\r\n    public removeCamera(toRemove: Camera): number {\r\n        const index = this.cameras.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            // Remove from the scene if mesh found\r\n            this.cameras.splice(index, 1);\r\n            if (!toRemove.parent) {\r\n                toRemove._removeFromSceneRootNodes();\r\n            }\r\n        }\r\n        // Remove from activeCameras\r\n        if (this.activeCameras) {\r\n            const index2 = this.activeCameras.indexOf(toRemove);\r\n            if (index2 !== -1) {\r\n                // Remove from the scene if mesh found\r\n                this.activeCameras.splice(index2, 1);\r\n            }\r\n        }\r\n        // Reset the activeCamera\r\n        if (this.activeCamera === toRemove) {\r\n            if (this.cameras.length > 0) {\r\n                this.activeCamera = this.cameras[0];\r\n            } else {\r\n                this.activeCamera = null;\r\n            }\r\n        }\r\n        this.onCameraRemovedObservable.notifyObservers(toRemove);\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Remove a particle system for the list of scene's particle systems\r\n     * @param toRemove defines the particle system to remove\r\n     * @returns the index where the particle system was in the particle system list\r\n     */\r\n    public removeParticleSystem(toRemove: IParticleSystem): number {\r\n        const index = this.particleSystems.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.particleSystems.splice(index, 1);\r\n\r\n            // Clean active container\r\n            this._executeActiveContainerCleanup(this._activeParticleSystems);\r\n        }\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Remove a animation for the list of scene's animations\r\n     * @param toRemove defines the animation to remove\r\n     * @returns the index where the animation was in the animation list\r\n     */\r\n    public removeAnimation(toRemove: Animation): number {\r\n        const index = this.animations.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.animations.splice(index, 1);\r\n        }\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Will stop the animation of the given target\r\n     * @param target - the target\r\n     * @param animationName - the name of the animation to stop (all animations will be stopped if both this and targetMask are empty)\r\n     * @param targetMask - a function that determines if the animation should be stopped based on its target (all animations will be stopped if both this and animationName are empty)\r\n     */\r\n    public stopAnimation(target: any, animationName?: string, targetMask?: (target: any) => boolean): void {\r\n        // Do nothing as code will be provided by animation component\r\n    }\r\n\r\n    /**\r\n     * Removes the given animation group from this scene.\r\n     * @param toRemove The animation group to remove\r\n     * @returns The index of the removed animation group\r\n     */\r\n    public removeAnimationGroup(toRemove: AnimationGroup): number {\r\n        const index = this.animationGroups.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.animationGroups.splice(index, 1);\r\n        }\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Removes the given multi-material from this scene.\r\n     * @param toRemove The multi-material to remove\r\n     * @returns The index of the removed multi-material\r\n     */\r\n    public removeMultiMaterial(toRemove: MultiMaterial): number {\r\n        const index = this.multiMaterials.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.multiMaterials.splice(index, 1);\r\n        }\r\n\r\n        this.onMultiMaterialRemovedObservable.notifyObservers(toRemove);\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Removes the given material from this scene.\r\n     * @param toRemove The material to remove\r\n     * @returns The index of the removed material\r\n     */\r\n    public removeMaterial(toRemove: Material): number {\r\n        const index = toRemove._indexInSceneMaterialArray;\r\n        if (index !== -1 && index < this.materials.length) {\r\n            if (index !== this.materials.length - 1) {\r\n                const lastMaterial = this.materials[this.materials.length - 1];\r\n                this.materials[index] = lastMaterial;\r\n                lastMaterial._indexInSceneMaterialArray = index;\r\n            }\r\n\r\n            toRemove._indexInSceneMaterialArray = -1;\r\n            this.materials.pop();\r\n        }\r\n\r\n        this.onMaterialRemovedObservable.notifyObservers(toRemove);\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Removes the given action manager from this scene.\r\n     * @deprecated\r\n     * @param toRemove The action manager to remove\r\n     * @returns The index of the removed action manager\r\n     */\r\n    public removeActionManager(toRemove: AbstractActionManager): number {\r\n        const index = this.actionManagers.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.actionManagers.splice(index, 1);\r\n        }\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Removes the given texture from this scene.\r\n     * @param toRemove The texture to remove\r\n     * @returns The index of the removed texture\r\n     */\r\n    public removeTexture(toRemove: BaseTexture): number {\r\n        const index = this.textures.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.textures.splice(index, 1);\r\n        }\r\n        this.onTextureRemovedObservable.notifyObservers(toRemove);\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Adds the given light to this scene\r\n     * @param newLight The light to add\r\n     */\r\n    public addLight(newLight: Light): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.lights.push(newLight);\r\n        this.sortLightsByPriority();\r\n\r\n        if (!newLight.parent) {\r\n            newLight._addToSceneRootNodes();\r\n        }\r\n\r\n        // Add light to all meshes (To support if the light is removed and then re-added)\r\n        for (const mesh of this.meshes) {\r\n            if (mesh.lightSources.indexOf(newLight) === -1) {\r\n                mesh.lightSources.push(newLight);\r\n                mesh._resyncLightSources();\r\n            }\r\n        }\r\n\r\n        this.onNewLightAddedObservable.notifyObservers(newLight);\r\n    }\r\n\r\n    /**\r\n     * Sorts the list list based on light priorities\r\n     */\r\n    public sortLightsByPriority(): void {\r\n        if (this.requireLightSorting) {\r\n            this.lights.sort(LightConstants.CompareLightsPriority);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds the given camera to this scene\r\n     * @param newCamera The camera to add\r\n     */\r\n    public addCamera(newCamera: Camera): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n\r\n        this.cameras.push(newCamera);\r\n        this.onNewCameraAddedObservable.notifyObservers(newCamera);\r\n\r\n        if (!newCamera.parent) {\r\n            newCamera._addToSceneRootNodes();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds the given skeleton to this scene\r\n     * @param newSkeleton The skeleton to add\r\n     */\r\n    public addSkeleton(newSkeleton: Skeleton): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.skeletons.push(newSkeleton);\r\n        this.onNewSkeletonAddedObservable.notifyObservers(newSkeleton);\r\n    }\r\n\r\n    /**\r\n     * Adds the given particle system to this scene\r\n     * @param newParticleSystem The particle system to add\r\n     */\r\n    public addParticleSystem(newParticleSystem: IParticleSystem): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.particleSystems.push(newParticleSystem);\r\n    }\r\n\r\n    /**\r\n     * Adds the given animation to this scene\r\n     * @param newAnimation The animation to add\r\n     */\r\n    public addAnimation(newAnimation: Animation): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.animations.push(newAnimation);\r\n    }\r\n\r\n    /**\r\n     * Adds the given animation group to this scene.\r\n     * @param newAnimationGroup The animation group to add\r\n     */\r\n    public addAnimationGroup(newAnimationGroup: AnimationGroup): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.animationGroups.push(newAnimationGroup);\r\n    }\r\n\r\n    /**\r\n     * Adds the given multi-material to this scene\r\n     * @param newMultiMaterial The multi-material to add\r\n     */\r\n    public addMultiMaterial(newMultiMaterial: MultiMaterial): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.multiMaterials.push(newMultiMaterial);\r\n        this.onNewMultiMaterialAddedObservable.notifyObservers(newMultiMaterial);\r\n    }\r\n\r\n    /**\r\n     * Adds the given material to this scene\r\n     * @param newMaterial The material to add\r\n     */\r\n    public addMaterial(newMaterial: Material): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n\r\n        if (newMaterial.getScene() === this && newMaterial._indexInSceneMaterialArray !== -1) {\r\n            // Already there??\r\n            return;\r\n        }\r\n\r\n        newMaterial._indexInSceneMaterialArray = this.materials.length;\r\n        this.materials.push(newMaterial);\r\n        this.onNewMaterialAddedObservable.notifyObservers(newMaterial);\r\n    }\r\n\r\n    /**\r\n     * Adds the given morph target to this scene\r\n     * @param newMorphTargetManager The morph target to add\r\n     */\r\n    public addMorphTargetManager(newMorphTargetManager: MorphTargetManager): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.morphTargetManagers.push(newMorphTargetManager);\r\n    }\r\n\r\n    /**\r\n     * Adds the given geometry to this scene\r\n     * @param newGeometry The geometry to add\r\n     */\r\n    public addGeometry(newGeometry: Geometry): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n\r\n        if (this._geometriesByUniqueId) {\r\n            this._geometriesByUniqueId[newGeometry.uniqueId] = this.geometries.length;\r\n        }\r\n\r\n        this.geometries.push(newGeometry);\r\n    }\r\n\r\n    /**\r\n     * Adds the given action manager to this scene\r\n     * @deprecated\r\n     * @param newActionManager The action manager to add\r\n     */\r\n    public addActionManager(newActionManager: AbstractActionManager): void {\r\n        this.actionManagers.push(newActionManager);\r\n    }\r\n\r\n    /**\r\n     * Adds the given texture to this scene.\r\n     * @param newTexture The texture to add\r\n     */\r\n    public addTexture(newTexture: BaseTexture): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.textures.push(newTexture);\r\n        this.onNewTextureAddedObservable.notifyObservers(newTexture);\r\n    }\r\n\r\n    /**\r\n     * Switch active camera\r\n     * @param newCamera defines the new active camera\r\n     * @param attachControl defines if attachControl must be called for the new active camera (default: true)\r\n     */\r\n    public switchActiveCamera(newCamera: Camera, attachControl = true): void {\r\n        const canvas = this._engine.getInputElement();\r\n\r\n        if (!canvas) {\r\n            return;\r\n        }\r\n\r\n        if (this.activeCamera) {\r\n            this.activeCamera.detachControl();\r\n        }\r\n        this.activeCamera = newCamera;\r\n        if (attachControl) {\r\n            newCamera.attachControl();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * sets the active camera of the scene using its Id\r\n     * @param id defines the camera's Id\r\n     * @returns the new active camera or null if none found.\r\n     */\r\n    public setActiveCameraById(id: string): Nullable<Camera> {\r\n        const camera = this.getCameraById(id);\r\n\r\n        if (camera) {\r\n            this.activeCamera = camera;\r\n            return camera;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * sets the active camera of the scene using its name\r\n     * @param name defines the camera's name\r\n     * @returns the new active camera or null if none found.\r\n     */\r\n    public setActiveCameraByName(name: string): Nullable<Camera> {\r\n        const camera = this.getCameraByName(name);\r\n\r\n        if (camera) {\r\n            this.activeCamera = camera;\r\n            return camera;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * get an animation group using its name\r\n     * @param name defines the material's name\r\n     * @returns the animation group or null if none found.\r\n     */\r\n    public getAnimationGroupByName(name: string): Nullable<AnimationGroup> {\r\n        for (let index = 0; index < this.animationGroups.length; index++) {\r\n            if (this.animationGroups[index].name === name) {\r\n                return this.animationGroups[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private _getMaterial(allowMultiMaterials: boolean, predicate: (m: Material) => boolean): Nullable<Material> {\r\n        for (let index = 0; index < this.materials.length; index++) {\r\n            const material = this.materials[index];\r\n            if (predicate(material)) {\r\n                return material;\r\n            }\r\n        }\r\n        if (allowMultiMaterials) {\r\n            for (let index = 0; index < this.multiMaterials.length; index++) {\r\n                const material = this.multiMaterials[index];\r\n                if (predicate(material)) {\r\n                    return material;\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Get a material using its unique id\r\n     * @param uniqueId defines the material's unique id\r\n     * @param allowMultiMaterials determines whether multimaterials should be considered\r\n     * @returns the material or null if none found.\r\n     */\r\n    public getMaterialByUniqueID(uniqueId: number, allowMultiMaterials: boolean = false): Nullable<Material> {\r\n        return this._getMaterial(allowMultiMaterials, (m) => m.uniqueId === uniqueId);\r\n    }\r\n\r\n    /**\r\n     * get a material using its id\r\n     * @param id defines the material's Id\r\n     * @param allowMultiMaterials determines whether multimaterials should be considered\r\n     * @returns the material or null if none found.\r\n     */\r\n    public getMaterialById(id: string, allowMultiMaterials: boolean = false): Nullable<Material> {\r\n        return this._getMaterial(allowMultiMaterials, (m) => m.id === id);\r\n    }\r\n\r\n    /**\r\n     * Gets a material using its name\r\n     * @param name defines the material's name\r\n     * @param allowMultiMaterials determines whether multimaterials should be considered\r\n     * @returns the material or null if none found.\r\n     */\r\n    public getMaterialByName(name: string, allowMultiMaterials: boolean = false): Nullable<Material> {\r\n        return this._getMaterial(allowMultiMaterials, (m) => m.name === name);\r\n    }\r\n\r\n    /**\r\n     * Gets a last added material using a given id\r\n     * @param id defines the material's id\r\n     * @param allowMultiMaterials determines whether multimaterials should be considered\r\n     * @returns the last material with the given id or null if none found.\r\n     */\r\n    public getLastMaterialById(id: string, allowMultiMaterials: boolean = false): Nullable<Material> {\r\n        for (let index = this.materials.length - 1; index >= 0; index--) {\r\n            if (this.materials[index].id === id) {\r\n                return this.materials[index];\r\n            }\r\n        }\r\n        if (allowMultiMaterials) {\r\n            for (let index = this.multiMaterials.length - 1; index >= 0; index--) {\r\n                if (this.multiMaterials[index].id === id) {\r\n                    return this.multiMaterials[index];\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Get a texture using its unique id\r\n     * @param uniqueId defines the texture's unique id\r\n     * @returns the texture or null if none found.\r\n     */\r\n    public getTextureByUniqueId(uniqueId: number): Nullable<BaseTexture> {\r\n        for (let index = 0; index < this.textures.length; index++) {\r\n            if (this.textures[index].uniqueId === uniqueId) {\r\n                return this.textures[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a texture using its name\r\n     * @param name defines the texture's name\r\n     * @returns the texture or null if none found.\r\n     */\r\n    public getTextureByName(name: string): Nullable<BaseTexture> {\r\n        for (let index = 0; index < this.textures.length; index++) {\r\n            if (this.textures[index].name === name) {\r\n                return this.textures[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a camera using its Id\r\n     * @param id defines the Id to look for\r\n     * @returns the camera or null if not found\r\n     */\r\n    public getCameraById(id: string): Nullable<Camera> {\r\n        for (let index = 0; index < this.cameras.length; index++) {\r\n            if (this.cameras[index].id === id) {\r\n                return this.cameras[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a camera using its unique Id\r\n     * @param uniqueId defines the unique Id to look for\r\n     * @returns the camera or null if not found\r\n     */\r\n    public getCameraByUniqueId(uniqueId: number): Nullable<Camera> {\r\n        for (let index = 0; index < this.cameras.length; index++) {\r\n            if (this.cameras[index].uniqueId === uniqueId) {\r\n                return this.cameras[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a camera using its name\r\n     * @param name defines the camera's name\r\n     * @returns the camera or null if none found.\r\n     */\r\n    public getCameraByName(name: string): Nullable<Camera> {\r\n        for (let index = 0; index < this.cameras.length; index++) {\r\n            if (this.cameras[index].name === name) {\r\n                return this.cameras[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a bone using its Id\r\n     * @param id defines the bone's Id\r\n     * @returns the bone or null if not found\r\n     */\r\n    public getBoneById(id: string): Nullable<Bone> {\r\n        for (let skeletonIndex = 0; skeletonIndex < this.skeletons.length; skeletonIndex++) {\r\n            const skeleton = this.skeletons[skeletonIndex];\r\n            for (let boneIndex = 0; boneIndex < skeleton.bones.length; boneIndex++) {\r\n                if (skeleton.bones[boneIndex].id === id) {\r\n                    return skeleton.bones[boneIndex];\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a bone using its id\r\n     * @param name defines the bone's name\r\n     * @returns the bone or null if not found\r\n     */\r\n    public getBoneByName(name: string): Nullable<Bone> {\r\n        for (let skeletonIndex = 0; skeletonIndex < this.skeletons.length; skeletonIndex++) {\r\n            const skeleton = this.skeletons[skeletonIndex];\r\n            for (let boneIndex = 0; boneIndex < skeleton.bones.length; boneIndex++) {\r\n                if (skeleton.bones[boneIndex].name === name) {\r\n                    return skeleton.bones[boneIndex];\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a light node using its name\r\n     * @param name defines the light's name\r\n     * @returns the light or null if none found.\r\n     */\r\n    public getLightByName(name: string): Nullable<Light> {\r\n        for (let index = 0; index < this.lights.length; index++) {\r\n            if (this.lights[index].name === name) {\r\n                return this.lights[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a light node using its Id\r\n     * @param id defines the light's Id\r\n     * @returns the light or null if none found.\r\n     */\r\n    public getLightById(id: string): Nullable<Light> {\r\n        for (let index = 0; index < this.lights.length; index++) {\r\n            if (this.lights[index].id === id) {\r\n                return this.lights[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a light node using its scene-generated unique Id\r\n     * @param uniqueId defines the light's unique Id\r\n     * @returns the light or null if none found.\r\n     */\r\n    public getLightByUniqueId(uniqueId: number): Nullable<Light> {\r\n        for (let index = 0; index < this.lights.length; index++) {\r\n            if (this.lights[index].uniqueId === uniqueId) {\r\n                return this.lights[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a particle system by Id\r\n     * @param id defines the particle system Id\r\n     * @returns the corresponding system or null if none found\r\n     */\r\n    public getParticleSystemById(id: string): Nullable<IParticleSystem> {\r\n        for (let index = 0; index < this.particleSystems.length; index++) {\r\n            if (this.particleSystems[index].id === id) {\r\n                return this.particleSystems[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a geometry using its Id\r\n     * @param id defines the geometry's Id\r\n     * @returns the geometry or null if none found.\r\n     */\r\n    public getGeometryById(id: string): Nullable<Geometry> {\r\n        for (let index = 0; index < this.geometries.length; index++) {\r\n            if (this.geometries[index].id === id) {\r\n                return this.geometries[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private _getGeometryByUniqueId(uniqueId: number): Nullable<Geometry> {\r\n        if (this._geometriesByUniqueId) {\r\n            const index = this._geometriesByUniqueId[uniqueId];\r\n            if (index !== undefined) {\r\n                return this.geometries[index];\r\n            }\r\n        } else {\r\n            for (let index = 0; index < this.geometries.length; index++) {\r\n                if (this.geometries[index].uniqueId === uniqueId) {\r\n                    return this.geometries[index];\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Add a new geometry to this scene\r\n     * @param geometry defines the geometry to be added to the scene.\r\n     * @param force defines if the geometry must be pushed even if a geometry with this id already exists\r\n     * @returns a boolean defining if the geometry was added or not\r\n     */\r\n    public pushGeometry(geometry: Geometry, force?: boolean): boolean {\r\n        if (!force && this._getGeometryByUniqueId(geometry.uniqueId)) {\r\n            return false;\r\n        }\r\n\r\n        this.addGeometry(geometry);\r\n\r\n        this.onNewGeometryAddedObservable.notifyObservers(geometry);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Removes an existing geometry\r\n     * @param geometry defines the geometry to be removed from the scene\r\n     * @returns a boolean defining if the geometry was removed or not\r\n     */\r\n    public removeGeometry(geometry: Geometry): boolean {\r\n        let index;\r\n        if (this._geometriesByUniqueId) {\r\n            index = this._geometriesByUniqueId[geometry.uniqueId];\r\n            if (index === undefined) {\r\n                return false;\r\n            }\r\n        } else {\r\n            index = this.geometries.indexOf(geometry);\r\n            if (index < 0) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        if (index !== this.geometries.length - 1) {\r\n            const lastGeometry = this.geometries[this.geometries.length - 1];\r\n            if (lastGeometry) {\r\n                this.geometries[index] = lastGeometry;\r\n                if (this._geometriesByUniqueId) {\r\n                    this._geometriesByUniqueId[lastGeometry.uniqueId] = index;\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._geometriesByUniqueId) {\r\n            this._geometriesByUniqueId[geometry.uniqueId] = undefined;\r\n        }\r\n\r\n        this.geometries.pop();\r\n\r\n        this.onGeometryRemovedObservable.notifyObservers(geometry);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of geometries attached to the scene\r\n     * @returns an array of Geometry\r\n     */\r\n    public getGeometries(): Geometry[] {\r\n        return this.geometries;\r\n    }\r\n\r\n    /**\r\n     * Gets the first added mesh found of a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the mesh found or null if not found at all\r\n     */\r\n    public getMeshById(id: string): Nullable<AbstractMesh> {\r\n        for (let index = 0; index < this.meshes.length; index++) {\r\n            if (this.meshes[index].id === id) {\r\n                return this.meshes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a list of meshes using their Id\r\n     * @param id defines the Id to search for\r\n     * @returns a list of meshes\r\n     */\r\n    public getMeshesById(id: string): Array<AbstractMesh> {\r\n        return this.meshes.filter(function (m) {\r\n            return m.id === id;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Gets the first added transform node found of a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found transform node or null if not found at all.\r\n     */\r\n    public getTransformNodeById(id: string): Nullable<TransformNode> {\r\n        for (let index = 0; index < this.transformNodes.length; index++) {\r\n            if (this.transformNodes[index].id === id) {\r\n                return this.transformNodes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a transform node with its auto-generated unique Id\r\n     * @param uniqueId defines the unique Id to search for\r\n     * @returns the found transform node or null if not found at all.\r\n     */\r\n    public getTransformNodeByUniqueId(uniqueId: number): Nullable<TransformNode> {\r\n        for (let index = 0; index < this.transformNodes.length; index++) {\r\n            if (this.transformNodes[index].uniqueId === uniqueId) {\r\n                return this.transformNodes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a list of transform nodes using their Id\r\n     * @param id defines the Id to search for\r\n     * @returns a list of transform nodes\r\n     */\r\n    public getTransformNodesById(id: string): Array<TransformNode> {\r\n        return this.transformNodes.filter(function (m) {\r\n            return m.id === id;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Gets a mesh with its auto-generated unique Id\r\n     * @param uniqueId defines the unique Id to search for\r\n     * @returns the found mesh or null if not found at all.\r\n     */\r\n    public getMeshByUniqueId(uniqueId: number): Nullable<AbstractMesh> {\r\n        for (let index = 0; index < this.meshes.length; index++) {\r\n            if (this.meshes[index].uniqueId === uniqueId) {\r\n                return this.meshes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a the last added mesh using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found mesh or null if not found at all.\r\n     */\r\n    public getLastMeshById(id: string): Nullable<AbstractMesh> {\r\n        for (let index = this.meshes.length - 1; index >= 0; index--) {\r\n            if (this.meshes[index].id === id) {\r\n                return this.meshes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a the last transform node using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found mesh or null if not found at all.\r\n     */\r\n    public getLastTransformNodeById(id: string): Nullable<TransformNode> {\r\n        for (let index = this.transformNodes.length - 1; index >= 0; index--) {\r\n            if (this.transformNodes[index].id === id) {\r\n                return this.transformNodes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a the last added node (Mesh, Camera, Light) using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found node or null if not found at all\r\n     */\r\n    public getLastEntryById(id: string): Nullable<Node> {\r\n        let index: number;\r\n        for (index = this.meshes.length - 1; index >= 0; index--) {\r\n            if (this.meshes[index].id === id) {\r\n                return this.meshes[index];\r\n            }\r\n        }\r\n\r\n        for (index = this.transformNodes.length - 1; index >= 0; index--) {\r\n            if (this.transformNodes[index].id === id) {\r\n                return this.transformNodes[index];\r\n            }\r\n        }\r\n\r\n        for (index = this.cameras.length - 1; index >= 0; index--) {\r\n            if (this.cameras[index].id === id) {\r\n                return this.cameras[index];\r\n            }\r\n        }\r\n\r\n        for (index = this.lights.length - 1; index >= 0; index--) {\r\n            if (this.lights[index].id === id) {\r\n                return this.lights[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a node (Mesh, Camera, Light) using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found node or null if not found at all\r\n     */\r\n    public getNodeById(id: string): Nullable<Node> {\r\n        const mesh = this.getMeshById(id);\r\n        if (mesh) {\r\n            return mesh;\r\n        }\r\n\r\n        const transformNode = this.getTransformNodeById(id);\r\n        if (transformNode) {\r\n            return transformNode;\r\n        }\r\n\r\n        const light = this.getLightById(id);\r\n        if (light) {\r\n            return light;\r\n        }\r\n\r\n        const camera = this.getCameraById(id);\r\n        if (camera) {\r\n            return camera;\r\n        }\r\n\r\n        const bone = this.getBoneById(id);\r\n        if (bone) {\r\n            return bone;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a node (Mesh, Camera, Light) using a given name\r\n     * @param name defines the name to search for\r\n     * @returns the found node or null if not found at all.\r\n     */\r\n    public getNodeByName(name: string): Nullable<Node> {\r\n        const mesh = this.getMeshByName(name);\r\n        if (mesh) {\r\n            return mesh;\r\n        }\r\n\r\n        const transformNode = this.getTransformNodeByName(name);\r\n        if (transformNode) {\r\n            return transformNode;\r\n        }\r\n\r\n        const light = this.getLightByName(name);\r\n        if (light) {\r\n            return light;\r\n        }\r\n\r\n        const camera = this.getCameraByName(name);\r\n        if (camera) {\r\n            return camera;\r\n        }\r\n\r\n        const bone = this.getBoneByName(name);\r\n        if (bone) {\r\n            return bone;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a mesh using a given name\r\n     * @param name defines the name to search for\r\n     * @returns the found mesh or null if not found at all.\r\n     */\r\n    public getMeshByName(name: string): Nullable<AbstractMesh> {\r\n        for (let index = 0; index < this.meshes.length; index++) {\r\n            if (this.meshes[index].name === name) {\r\n                return this.meshes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a transform node using a given name\r\n     * @param name defines the name to search for\r\n     * @returns the found transform node or null if not found at all.\r\n     */\r\n    public getTransformNodeByName(name: string): Nullable<TransformNode> {\r\n        for (let index = 0; index < this.transformNodes.length; index++) {\r\n            if (this.transformNodes[index].name === name) {\r\n                return this.transformNodes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a skeleton using a given Id (if many are found, this function will pick the last one)\r\n     * @param id defines the Id to search for\r\n     * @returns the found skeleton or null if not found at all.\r\n     */\r\n    public getLastSkeletonById(id: string): Nullable<Skeleton> {\r\n        for (let index = this.skeletons.length - 1; index >= 0; index--) {\r\n            if (this.skeletons[index].id === id) {\r\n                return this.skeletons[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a skeleton using a given auto generated unique id\r\n     * @param  uniqueId defines the unique id to search for\r\n     * @returns the found skeleton or null if not found at all.\r\n     */\r\n    public getSkeletonByUniqueId(uniqueId: number): Nullable<Skeleton> {\r\n        for (let index = 0; index < this.skeletons.length; index++) {\r\n            if (this.skeletons[index].uniqueId === uniqueId) {\r\n                return this.skeletons[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a skeleton using a given id (if many are found, this function will pick the first one)\r\n     * @param id defines the id to search for\r\n     * @returns the found skeleton or null if not found at all.\r\n     */\r\n    public getSkeletonById(id: string): Nullable<Skeleton> {\r\n        for (let index = 0; index < this.skeletons.length; index++) {\r\n            if (this.skeletons[index].id === id) {\r\n                return this.skeletons[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a skeleton using a given name\r\n     * @param name defines the name to search for\r\n     * @returns the found skeleton or null if not found at all.\r\n     */\r\n    public getSkeletonByName(name: string): Nullable<Skeleton> {\r\n        for (let index = 0; index < this.skeletons.length; index++) {\r\n            if (this.skeletons[index].name === name) {\r\n                return this.skeletons[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a morph target manager  using a given id (if many are found, this function will pick the last one)\r\n     * @param id defines the id to search for\r\n     * @returns the found morph target manager or null if not found at all.\r\n     */\r\n    public getMorphTargetManagerById(id: number): Nullable<MorphTargetManager> {\r\n        for (let index = 0; index < this.morphTargetManagers.length; index++) {\r\n            if (this.morphTargetManagers[index].uniqueId === id) {\r\n                return this.morphTargetManagers[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a morph target using a given id (if many are found, this function will pick the first one)\r\n     * @param id defines the id to search for\r\n     * @returns the found morph target or null if not found at all.\r\n     */\r\n    public getMorphTargetById(id: string): Nullable<MorphTarget> {\r\n        for (let managerIndex = 0; managerIndex < this.morphTargetManagers.length; ++managerIndex) {\r\n            const morphTargetManager = this.morphTargetManagers[managerIndex];\r\n            for (let index = 0; index < morphTargetManager.numTargets; ++index) {\r\n                const target = morphTargetManager.getTarget(index);\r\n                if (target.id === id) {\r\n                    return target;\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a morph target using a given name (if many are found, this function will pick the first one)\r\n     * @param name defines the name to search for\r\n     * @returns the found morph target or null if not found at all.\r\n     */\r\n    public getMorphTargetByName(name: string): Nullable<MorphTarget> {\r\n        for (let managerIndex = 0; managerIndex < this.morphTargetManagers.length; ++managerIndex) {\r\n            const morphTargetManager = this.morphTargetManagers[managerIndex];\r\n            for (let index = 0; index < morphTargetManager.numTargets; ++index) {\r\n                const target = morphTargetManager.getTarget(index);\r\n                if (target.name === name) {\r\n                    return target;\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a post process using a given name (if many are found, this function will pick the first one)\r\n     * @param name defines the name to search for\r\n     * @returns the found post process or null if not found at all.\r\n     */\r\n    public getPostProcessByName(name: string): Nullable<PostProcess> {\r\n        for (let postProcessIndex = 0; postProcessIndex < this.postProcesses.length; ++postProcessIndex) {\r\n            const postProcess = this.postProcesses[postProcessIndex];\r\n            if (postProcess.name === name) {\r\n                return postProcess;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the given mesh is active\r\n     * @param mesh defines the mesh to look for\r\n     * @returns true if the mesh is in the active list\r\n     */\r\n    public isActiveMesh(mesh: AbstractMesh): boolean {\r\n        return this._activeMeshes.indexOf(mesh) !== -1;\r\n    }\r\n\r\n    /**\r\n     * Return a unique id as a string which can serve as an identifier for the scene\r\n     */\r\n    public get uid(): string {\r\n        if (!this._uid) {\r\n            this._uid = Tools.RandomId();\r\n        }\r\n        return this._uid;\r\n    }\r\n\r\n    /**\r\n     * Add an externally attached data from its key.\r\n     * This method call will fail and return false, if such key already exists.\r\n     * If you don't care and just want to get the data no matter what, use the more convenient getOrAddExternalDataWithFactory() method.\r\n     * @param key the unique key that identifies the data\r\n     * @param data the data object to associate to the key for this Engine instance\r\n     * @returns true if no such key were already present and the data was added successfully, false otherwise\r\n     */\r\n    public addExternalData<T extends Object>(key: string, data: T): boolean {\r\n        if (!this._externalData) {\r\n            this._externalData = new StringDictionary<Object>();\r\n        }\r\n        return this._externalData.add(key, data);\r\n    }\r\n\r\n    /**\r\n     * Get an externally attached data from its key\r\n     * @param key the unique key that identifies the data\r\n     * @returns the associated data, if present (can be null), or undefined if not present\r\n     */\r\n    public getExternalData<T>(key: string): Nullable<T> {\r\n        if (!this._externalData) {\r\n            return null;\r\n        }\r\n        return <T>this._externalData.get(key);\r\n    }\r\n\r\n    /**\r\n     * Get an externally attached data from its key, create it using a factory if it's not already present\r\n     * @param key the unique key that identifies the data\r\n     * @param factory the factory that will be called to create the instance if and only if it doesn't exists\r\n     * @returns the associated data, can be null if the factory returned null.\r\n     */\r\n    public getOrAddExternalDataWithFactory<T extends Object>(key: string, factory: (k: string) => T): T {\r\n        if (!this._externalData) {\r\n            this._externalData = new StringDictionary<Object>();\r\n        }\r\n        return <T>this._externalData.getOrAddWithFactory(key, factory);\r\n    }\r\n\r\n    /**\r\n     * Remove an externally attached data from the Engine instance\r\n     * @param key the unique key that identifies the data\r\n     * @returns true if the data was successfully removed, false if it doesn't exist\r\n     */\r\n    public removeExternalData(key: string): boolean {\r\n        return this._externalData.remove(key);\r\n    }\r\n\r\n    private _evaluateSubMesh(subMesh: SubMesh, mesh: AbstractMesh, initialMesh: AbstractMesh, forcePush: boolean): void {\r\n        if (forcePush || subMesh.isInFrustum(this._frustumPlanes)) {\r\n            for (const step of this._evaluateSubMeshStage) {\r\n                step.action(mesh, subMesh);\r\n            }\r\n\r\n            const material = subMesh.getMaterial();\r\n            if (material !== null && material !== undefined) {\r\n                // Render targets\r\n                if (material.hasRenderTargetTextures && material.getRenderTargetTextures != null) {\r\n                    if (this._processedMaterials.indexOf(material) === -1) {\r\n                        this._processedMaterials.push(material);\r\n\r\n                        this._materialsRenderTargets.concatWithNoDuplicate(material.getRenderTargetTextures!());\r\n                    }\r\n                }\r\n\r\n                // Dispatch\r\n                this._renderingManager.dispatch(subMesh, mesh, material);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clear the processed materials smart array preventing retention point in material dispose.\r\n     */\r\n    public freeProcessedMaterials(): void {\r\n        this._processedMaterials.dispose();\r\n    }\r\n\r\n    private _preventFreeActiveMeshesAndRenderingGroups = false;\r\n\r\n    /** Gets or sets a boolean blocking all the calls to freeActiveMeshes and freeRenderingGroups\r\n     * It can be used in order to prevent going through methods freeRenderingGroups and freeActiveMeshes several times to improve performance\r\n     * when disposing several meshes in a row or a hierarchy of meshes.\r\n     * When used, it is the responsibility of the user to blockfreeActiveMeshesAndRenderingGroups back to false.\r\n     */\r\n    public get blockfreeActiveMeshesAndRenderingGroups(): boolean {\r\n        return this._preventFreeActiveMeshesAndRenderingGroups;\r\n    }\r\n\r\n    public set blockfreeActiveMeshesAndRenderingGroups(value: boolean) {\r\n        if (this._preventFreeActiveMeshesAndRenderingGroups === value) {\r\n            return;\r\n        }\r\n\r\n        if (value) {\r\n            this.freeActiveMeshes();\r\n            this.freeRenderingGroups();\r\n        }\r\n\r\n        this._preventFreeActiveMeshesAndRenderingGroups = value;\r\n    }\r\n\r\n    /**\r\n     * Clear the active meshes smart array preventing retention point in mesh dispose.\r\n     */\r\n    public freeActiveMeshes(): void {\r\n        if (this.blockfreeActiveMeshesAndRenderingGroups) {\r\n            return;\r\n        }\r\n\r\n        this._activeMeshes.dispose();\r\n        if (this.activeCamera && this.activeCamera._activeMeshes) {\r\n            this.activeCamera._activeMeshes.dispose();\r\n        }\r\n        if (this.activeCameras) {\r\n            for (let i = 0; i < this.activeCameras.length; i++) {\r\n                const activeCamera = this.activeCameras[i];\r\n                if (activeCamera && activeCamera._activeMeshes) {\r\n                    activeCamera._activeMeshes.dispose();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clear the info related to rendering groups preventing retention points during dispose.\r\n     */\r\n    public freeRenderingGroups(): void {\r\n        if (this.blockfreeActiveMeshesAndRenderingGroups) {\r\n            return;\r\n        }\r\n\r\n        if (this._renderingManager) {\r\n            this._renderingManager.freeRenderingGroups();\r\n        }\r\n        if (this.textures) {\r\n            for (let i = 0; i < this.textures.length; i++) {\r\n                const texture = this.textures[i];\r\n                if (texture && (<RenderTargetTexture>texture).renderList) {\r\n                    (<RenderTargetTexture>texture).freeRenderingGroups();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _isInIntermediateRendering(): boolean {\r\n        return this._intermediateRendering;\r\n    }\r\n\r\n    /**\r\n     * Lambda returning the list of potentially active meshes.\r\n     */\r\n    public getActiveMeshCandidates: () => ISmartArrayLike<AbstractMesh>;\r\n\r\n    /**\r\n     * Lambda returning the list of potentially active sub meshes.\r\n     */\r\n    public getActiveSubMeshCandidates: (mesh: AbstractMesh) => ISmartArrayLike<SubMesh>;\r\n\r\n    /**\r\n     * Lambda returning the list of potentially intersecting sub meshes.\r\n     */\r\n    public getIntersectingSubMeshCandidates: (mesh: AbstractMesh, localRay: Ray) => ISmartArrayLike<SubMesh>;\r\n\r\n    /**\r\n     * Lambda returning the list of potentially colliding sub meshes.\r\n     */\r\n    public getCollidingSubMeshCandidates: (mesh: AbstractMesh, collider: Collider) => ISmartArrayLike<SubMesh>;\r\n\r\n    /** @internal */\r\n    public _activeMeshesFrozen = false;\r\n    /** @internal */\r\n    public _activeMeshesFrozenButKeepClipping = false;\r\n    private _skipEvaluateActiveMeshesCompletely = false;\r\n\r\n    /**\r\n     * Use this function to stop evaluating active meshes. The current list will be keep alive between frames\r\n     * @param skipEvaluateActiveMeshes defines an optional boolean indicating that the evaluate active meshes step must be completely skipped\r\n     * @param onSuccess optional success callback\r\n     * @param onError optional error callback\r\n     * @param freezeMeshes defines if meshes should be frozen (true by default)\r\n     * @param keepFrustumCulling defines if you want to keep running the frustum clipping (false by default)\r\n     * @returns the current scene\r\n     */\r\n    public freezeActiveMeshes(\r\n        skipEvaluateActiveMeshes = false,\r\n        onSuccess?: () => void,\r\n        onError?: (message: string) => void,\r\n        freezeMeshes = true,\r\n        keepFrustumCulling = false\r\n    ): Scene {\r\n        this.executeWhenReady(() => {\r\n            if (!this.activeCamera) {\r\n                onError && onError(\"No active camera found\");\r\n                return;\r\n            }\r\n\r\n            if (!this._frustumPlanes) {\r\n                this.updateTransformMatrix();\r\n            }\r\n\r\n            this._evaluateActiveMeshes();\r\n            this._activeMeshesFrozen = true;\r\n            this._activeMeshesFrozenButKeepClipping = keepFrustumCulling;\r\n            this._skipEvaluateActiveMeshesCompletely = skipEvaluateActiveMeshes;\r\n\r\n            if (freezeMeshes) {\r\n                for (let index = 0; index < this._activeMeshes.length; index++) {\r\n                    this._activeMeshes.data[index]._freeze();\r\n                }\r\n            }\r\n            onSuccess && onSuccess();\r\n        });\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Use this function to restart evaluating active meshes on every frame\r\n     * @returns the current scene\r\n     */\r\n    public unfreezeActiveMeshes(): Scene {\r\n        for (let index = 0; index < this.meshes.length; index++) {\r\n            const mesh = this.meshes[index];\r\n            if (mesh._internalAbstractMeshDataInfo) {\r\n                mesh._internalAbstractMeshDataInfo._isActive = false;\r\n            }\r\n        }\r\n\r\n        for (let index = 0; index < this._activeMeshes.length; index++) {\r\n            this._activeMeshes.data[index]._unFreeze();\r\n        }\r\n\r\n        this._activeMeshesFrozen = false;\r\n        return this;\r\n    }\r\n\r\n    private _executeActiveContainerCleanup(container: SmartArray<any>) {\r\n        const isInFastMode = this._engine.snapshotRendering && this._engine.snapshotRenderingMode === Constants.SNAPSHOTRENDERING_FAST;\r\n\r\n        if (!isInFastMode && this._activeMeshesFrozen && this._activeMeshes.length) {\r\n            return; // Do not execute in frozen mode\r\n        }\r\n\r\n        // We need to ensure we are not in the rendering loop\r\n        this.onBeforeRenderObservable.addOnce(() => container.dispose());\r\n    }\r\n\r\n    private _evaluateActiveMeshes(): void {\r\n        if (this._engine.snapshotRendering && this._engine.snapshotRenderingMode === Constants.SNAPSHOTRENDERING_FAST) {\r\n            if (this._activeMeshes.length > 0) {\r\n                this.activeCamera?._activeMeshes.reset();\r\n                this._activeMeshes.reset();\r\n                this._renderingManager.reset();\r\n                this._processedMaterials.reset();\r\n                this._activeParticleSystems.reset();\r\n                this._activeSkeletons.reset();\r\n                this._softwareSkinnedMeshes.reset();\r\n            }\r\n            return;\r\n        }\r\n\r\n        if (this._activeMeshesFrozen && this._activeMeshes.length) {\r\n            if (!this._skipEvaluateActiveMeshesCompletely) {\r\n                const len = this._activeMeshes.length;\r\n                for (let i = 0; i < len; i++) {\r\n                    const mesh = this._activeMeshes.data[i];\r\n                    mesh.computeWorldMatrix();\r\n                }\r\n            }\r\n\r\n            if (this._activeParticleSystems) {\r\n                const psLength = this._activeParticleSystems.length;\r\n                for (let i = 0; i < psLength; i++) {\r\n                    this._activeParticleSystems.data[i].animate();\r\n                }\r\n            }\r\n\r\n            this._renderingManager.resetSprites();\r\n\r\n            return;\r\n        }\r\n\r\n        if (!this.activeCamera) {\r\n            return;\r\n        }\r\n\r\n        this.onBeforeActiveMeshesEvaluationObservable.notifyObservers(this);\r\n\r\n        this.activeCamera._activeMeshes.reset();\r\n        this._activeMeshes.reset();\r\n        this._renderingManager.reset();\r\n        this._processedMaterials.reset();\r\n        this._activeParticleSystems.reset();\r\n        this._activeSkeletons.reset();\r\n        this._softwareSkinnedMeshes.reset();\r\n        this._materialsRenderTargets.reset();\r\n\r\n        for (const step of this._beforeEvaluateActiveMeshStage) {\r\n            step.action();\r\n        }\r\n\r\n        // Determine mesh candidates\r\n        const meshes = this.getActiveMeshCandidates();\r\n\r\n        // Check each mesh\r\n        const len = meshes.length;\r\n        for (let i = 0; i < len; i++) {\r\n            const mesh = meshes.data[i];\r\n            mesh._internalAbstractMeshDataInfo._currentLODIsUpToDate = false;\r\n            if (mesh.isBlocked) {\r\n                continue;\r\n            }\r\n\r\n            this._totalVertices.addCount(mesh.getTotalVertices(), false);\r\n\r\n            if (!mesh.isReady() || !mesh.isEnabled() || mesh.scaling.hasAZeroComponent) {\r\n                continue;\r\n            }\r\n\r\n            mesh.computeWorldMatrix();\r\n\r\n            // Intersections\r\n            if (mesh.actionManager && mesh.actionManager.hasSpecificTriggers2(Constants.ACTION_OnIntersectionEnterTrigger, Constants.ACTION_OnIntersectionExitTrigger)) {\r\n                this._meshesForIntersections.pushNoDuplicate(mesh);\r\n            }\r\n\r\n            // Switch to current LOD\r\n            let meshToRender = this.customLODSelector ? this.customLODSelector(mesh, this.activeCamera) : mesh.getLOD(this.activeCamera);\r\n            mesh._internalAbstractMeshDataInfo._currentLOD = meshToRender;\r\n            mesh._internalAbstractMeshDataInfo._currentLODIsUpToDate = true;\r\n            if (meshToRender === undefined || meshToRender === null) {\r\n                continue;\r\n            }\r\n\r\n            // Compute world matrix if LOD is billboard\r\n            if (meshToRender !== mesh && meshToRender.billboardMode !== 0) {\r\n                meshToRender.computeWorldMatrix();\r\n            }\r\n\r\n            mesh._preActivate();\r\n\r\n            if (\r\n                mesh.isVisible &&\r\n                mesh.visibility > 0 &&\r\n                (mesh.layerMask & this.activeCamera.layerMask) !== 0 &&\r\n                (this._skipFrustumClipping || mesh.alwaysSelectAsActiveMesh || mesh.isInFrustum(this._frustumPlanes))\r\n            ) {\r\n                this._activeMeshes.push(mesh);\r\n                this.activeCamera._activeMeshes.push(mesh);\r\n\r\n                if (meshToRender !== mesh) {\r\n                    meshToRender._activate(this._renderId, false);\r\n                }\r\n\r\n                for (const step of this._preActiveMeshStage) {\r\n                    step.action(mesh);\r\n                }\r\n\r\n                if (mesh._activate(this._renderId, false)) {\r\n                    if (!mesh.isAnInstance) {\r\n                        meshToRender._internalAbstractMeshDataInfo._onlyForInstances = false;\r\n                    } else {\r\n                        if (mesh._internalAbstractMeshDataInfo._actAsRegularMesh) {\r\n                            meshToRender = mesh;\r\n                        }\r\n                    }\r\n                    meshToRender._internalAbstractMeshDataInfo._isActive = true;\r\n                    this._activeMesh(mesh, meshToRender);\r\n                }\r\n\r\n                mesh._postActivate();\r\n            }\r\n        }\r\n\r\n        this.onAfterActiveMeshesEvaluationObservable.notifyObservers(this);\r\n\r\n        // Particle systems\r\n        if (this.particlesEnabled) {\r\n            this.onBeforeParticlesRenderingObservable.notifyObservers(this);\r\n            for (let particleIndex = 0; particleIndex < this.particleSystems.length; particleIndex++) {\r\n                const particleSystem = this.particleSystems[particleIndex];\r\n\r\n                if (!particleSystem.isStarted() || !particleSystem.emitter) {\r\n                    continue;\r\n                }\r\n\r\n                const emitter = <any>particleSystem.emitter;\r\n                if (!emitter.position || emitter.isEnabled()) {\r\n                    this._activeParticleSystems.push(particleSystem);\r\n                    particleSystem.animate();\r\n                    this._renderingManager.dispatchParticles(particleSystem);\r\n                }\r\n            }\r\n            this.onAfterParticlesRenderingObservable.notifyObservers(this);\r\n        }\r\n    }\r\n\r\n    private _activeMesh(sourceMesh: AbstractMesh, mesh: AbstractMesh): void {\r\n        if (this._skeletonsEnabled && mesh.skeleton !== null && mesh.skeleton !== undefined) {\r\n            if (this._activeSkeletons.pushNoDuplicate(mesh.skeleton)) {\r\n                mesh.skeleton.prepare();\r\n                this._activeBones.addCount(mesh.skeleton.bones.length, false);\r\n            }\r\n\r\n            if (!mesh.computeBonesUsingShaders) {\r\n                this._softwareSkinnedMeshes.pushNoDuplicate(<Mesh>mesh);\r\n            }\r\n        }\r\n\r\n        let forcePush = sourceMesh.hasInstances || sourceMesh.isAnInstance || this.dispatchAllSubMeshesOfActiveMeshes || this._skipFrustumClipping || mesh.alwaysSelectAsActiveMesh;\r\n\r\n        if (mesh && mesh.subMeshes && mesh.subMeshes.length > 0) {\r\n            const subMeshes = this.getActiveSubMeshCandidates(mesh);\r\n            const len = subMeshes.length;\r\n            forcePush = forcePush || len === 1;\r\n            for (let i = 0; i < len; i++) {\r\n                const subMesh = subMeshes.data[i];\r\n                this._evaluateSubMesh(subMesh, mesh, sourceMesh, forcePush);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the transform matrix to update from the current active camera\r\n     * @param force defines a boolean used to force the update even if cache is up to date\r\n     */\r\n    public updateTransformMatrix(force?: boolean): void {\r\n        const activeCamera = this.activeCamera;\r\n        if (!activeCamera) {\r\n            return;\r\n        }\r\n\r\n        if (activeCamera._renderingMultiview) {\r\n            const leftCamera = activeCamera._rigCameras[0];\r\n            const rightCamera = activeCamera._rigCameras[1];\r\n            this.setTransformMatrix(leftCamera.getViewMatrix(), leftCamera.getProjectionMatrix(force), rightCamera.getViewMatrix(), rightCamera.getProjectionMatrix(force));\r\n        } else {\r\n            this.setTransformMatrix(activeCamera.getViewMatrix(), activeCamera.getProjectionMatrix(force));\r\n        }\r\n    }\r\n\r\n    private _bindFrameBuffer(camera: Nullable<Camera>, clear = true) {\r\n        if (camera && camera._multiviewTexture) {\r\n            camera._multiviewTexture._bindFrameBuffer();\r\n        } else if (camera && camera.outputRenderTarget) {\r\n            camera.outputRenderTarget._bindFrameBuffer();\r\n        } else {\r\n            if (!this._engine._currentFrameBufferIsDefaultFrameBuffer()) {\r\n                this._engine.restoreDefaultFramebuffer();\r\n            }\r\n        }\r\n        if (clear) {\r\n            this._clearFrameBuffer(camera);\r\n        }\r\n    }\r\n\r\n    private _clearFrameBuffer(camera: Nullable<Camera>) {\r\n        // we assume the framebuffer currently bound is the right one\r\n        if (camera && camera._multiviewTexture) {\r\n            // no clearing\r\n        } else if (camera && camera.outputRenderTarget && !camera._renderingMultiview) {\r\n            const rtt = camera.outputRenderTarget;\r\n            if (rtt.onClearObservable.hasObservers()) {\r\n                rtt.onClearObservable.notifyObservers(this._engine);\r\n            } else if (!rtt.skipInitialClear && !camera.isRightCamera) {\r\n                if (this.autoClear) {\r\n                    this._engine.clear(rtt.clearColor || this.clearColor, !rtt._cleared, true, true);\r\n                }\r\n                rtt._cleared = true;\r\n            }\r\n        } else {\r\n            if (!this._defaultFrameBufferCleared) {\r\n                this._defaultFrameBufferCleared = true;\r\n                this._clear();\r\n            } else {\r\n                this._engine.clear(null, false, true, true);\r\n            }\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _allowPostProcessClearColor = true;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _renderForCamera(camera: Camera, rigParent?: Camera, bindFrameBuffer = true): void {\r\n        if (camera && camera._skipRendering) {\r\n            return;\r\n        }\r\n\r\n        const engine = this._engine;\r\n\r\n        // Use _activeCamera instead of activeCamera to avoid onActiveCameraChanged\r\n        this._activeCamera = camera;\r\n\r\n        if (!this.activeCamera) {\r\n            throw new Error(\"Active camera not set\");\r\n        }\r\n\r\n        // Viewport\r\n        engine.setViewport(this.activeCamera.viewport);\r\n\r\n        // Camera\r\n        this.resetCachedMaterial();\r\n        this._renderId++;\r\n\r\n        if (!this.prePass && bindFrameBuffer) {\r\n            let skipInitialClear = true;\r\n            if (camera._renderingMultiview && camera.outputRenderTarget) {\r\n                skipInitialClear = camera.outputRenderTarget.skipInitialClear;\r\n                if (this.autoClear) {\r\n                    this._defaultFrameBufferCleared = false;\r\n                    camera.outputRenderTarget.skipInitialClear = false;\r\n                }\r\n            }\r\n            this._bindFrameBuffer(this._activeCamera);\r\n            if (camera._renderingMultiview && camera.outputRenderTarget) {\r\n                camera.outputRenderTarget.skipInitialClear = skipInitialClear;\r\n            }\r\n        }\r\n\r\n        this.updateTransformMatrix();\r\n\r\n        this.onBeforeCameraRenderObservable.notifyObservers(this.activeCamera);\r\n\r\n        // Meshes\r\n        this._evaluateActiveMeshes();\r\n\r\n        // Software skinning\r\n        for (let softwareSkinnedMeshIndex = 0; softwareSkinnedMeshIndex < this._softwareSkinnedMeshes.length; softwareSkinnedMeshIndex++) {\r\n            const mesh = this._softwareSkinnedMeshes.data[softwareSkinnedMeshIndex];\r\n\r\n            mesh.applySkeleton(<Skeleton>mesh.skeleton);\r\n        }\r\n\r\n        // Render targets\r\n        this.onBeforeRenderTargetsRenderObservable.notifyObservers(this);\r\n\r\n        this._renderTargets.concatWithNoDuplicate(this._materialsRenderTargets);\r\n\r\n        if (camera.customRenderTargets && camera.customRenderTargets.length > 0) {\r\n            this._renderTargets.concatWithNoDuplicate(camera.customRenderTargets);\r\n        }\r\n\r\n        if (rigParent && rigParent.customRenderTargets && rigParent.customRenderTargets.length > 0) {\r\n            this._renderTargets.concatWithNoDuplicate(rigParent.customRenderTargets);\r\n        }\r\n\r\n        if (this.environmentTexture && this.environmentTexture.isRenderTarget) {\r\n            this._renderTargets.pushNoDuplicate(this.environmentTexture as RenderTargetTexture);\r\n        }\r\n\r\n        // Collects render targets from external components.\r\n        for (const step of this._gatherActiveCameraRenderTargetsStage) {\r\n            step.action(this._renderTargets);\r\n        }\r\n\r\n        let needRebind = false;\r\n        if (this.renderTargetsEnabled) {\r\n            this._intermediateRendering = true;\r\n\r\n            if (this._renderTargets.length > 0) {\r\n                Tools.StartPerformanceCounter(\"Render targets\", this._renderTargets.length > 0);\r\n                for (let renderIndex = 0; renderIndex < this._renderTargets.length; renderIndex++) {\r\n                    const renderTarget = this._renderTargets.data[renderIndex];\r\n                    if (renderTarget._shouldRender()) {\r\n                        this._renderId++;\r\n                        const hasSpecialRenderTargetCamera = renderTarget.activeCamera && renderTarget.activeCamera !== this.activeCamera;\r\n                        renderTarget.render(<boolean>hasSpecialRenderTargetCamera, this.dumpNextRenderTargets);\r\n                        needRebind = true;\r\n                    }\r\n                }\r\n                Tools.EndPerformanceCounter(\"Render targets\", this._renderTargets.length > 0);\r\n\r\n                this._renderId++;\r\n            }\r\n\r\n            for (const step of this._cameraDrawRenderTargetStage) {\r\n                needRebind = step.action(this.activeCamera) || needRebind;\r\n            }\r\n\r\n            this._intermediateRendering = false;\r\n        }\r\n\r\n        this._engine.currentRenderPassId = camera.outputRenderTarget?.renderPassId ?? camera.renderPassId ?? Constants.RENDERPASS_MAIN;\r\n\r\n        // Restore framebuffer after rendering to targets\r\n        if (needRebind && !this.prePass) {\r\n            this._bindFrameBuffer(this._activeCamera, false);\r\n            this.updateTransformMatrix();\r\n        }\r\n\r\n        this.onAfterRenderTargetsRenderObservable.notifyObservers(this);\r\n\r\n        // Prepare Frame\r\n        if (this.postProcessManager && !camera._multiviewTexture && !this.prePass) {\r\n            this.postProcessManager._prepareFrame();\r\n        }\r\n\r\n        // Before Camera Draw\r\n        for (const step of this._beforeCameraDrawStage) {\r\n            step.action(this.activeCamera);\r\n        }\r\n\r\n        // Render\r\n        this.onBeforeDrawPhaseObservable.notifyObservers(this);\r\n\r\n        if (engine.snapshotRendering && engine.snapshotRenderingMode === Constants.SNAPSHOTRENDERING_FAST) {\r\n            this.finalizeSceneUbo();\r\n        }\r\n        this._renderingManager.render(null, null, true, true);\r\n        this.onAfterDrawPhaseObservable.notifyObservers(this);\r\n\r\n        // After Camera Draw\r\n        for (const step of this._afterCameraDrawStage) {\r\n            step.action(this.activeCamera);\r\n        }\r\n\r\n        // Finalize frame\r\n        if (this.postProcessManager && !camera._multiviewTexture) {\r\n            // if the camera has an output render target, render the post process to the render target\r\n            const texture = camera.outputRenderTarget ? camera.outputRenderTarget.renderTarget! : undefined;\r\n            this.postProcessManager._finalizeFrame(camera.isIntermediate, texture);\r\n        }\r\n\r\n        // After post process\r\n        for (const step of this._afterCameraPostProcessStage) {\r\n            step.action(this.activeCamera);\r\n        }\r\n\r\n        // Reset some special arrays\r\n        this._renderTargets.reset();\r\n\r\n        this.onAfterCameraRenderObservable.notifyObservers(this.activeCamera);\r\n    }\r\n\r\n    private _processSubCameras(camera: Camera, bindFrameBuffer = true): void {\r\n        if (camera.cameraRigMode === Constants.RIG_MODE_NONE || camera._renderingMultiview) {\r\n            if (camera._renderingMultiview && !this._multiviewSceneUbo) {\r\n                this._createMultiviewUbo();\r\n            }\r\n            this._renderForCamera(camera, undefined, bindFrameBuffer);\r\n            this.onAfterRenderCameraObservable.notifyObservers(camera);\r\n            return;\r\n        }\r\n\r\n        if (camera._useMultiviewToSingleView) {\r\n            this._renderMultiviewToSingleView(camera);\r\n        } else {\r\n            // rig cameras\r\n            this.onBeforeCameraRenderObservable.notifyObservers(camera);\r\n            for (let index = 0; index < camera._rigCameras.length; index++) {\r\n                this._renderForCamera(camera._rigCameras[index], camera);\r\n            }\r\n        }\r\n\r\n        // Use _activeCamera instead of activeCamera to avoid onActiveCameraChanged\r\n        this._activeCamera = camera;\r\n        this.updateTransformMatrix();\r\n        this.onAfterRenderCameraObservable.notifyObservers(camera);\r\n    }\r\n\r\n    private _checkIntersections(): void {\r\n        for (let index = 0; index < this._meshesForIntersections.length; index++) {\r\n            const sourceMesh = this._meshesForIntersections.data[index];\r\n\r\n            if (!sourceMesh.actionManager) {\r\n                continue;\r\n            }\r\n\r\n            for (let actionIndex = 0; sourceMesh.actionManager && actionIndex < sourceMesh.actionManager.actions.length; actionIndex++) {\r\n                const action: IAction = sourceMesh.actionManager.actions[actionIndex];\r\n\r\n                if (action.trigger === Constants.ACTION_OnIntersectionEnterTrigger || action.trigger === Constants.ACTION_OnIntersectionExitTrigger) {\r\n                    const parameters = action.getTriggerParameter();\r\n                    const otherMesh = parameters.mesh ? parameters.mesh : parameters;\r\n\r\n                    const areIntersecting = otherMesh.intersectsMesh(sourceMesh, parameters.usePreciseIntersection);\r\n                    const currentIntersectionInProgress = sourceMesh._intersectionsInProgress.indexOf(otherMesh);\r\n\r\n                    if (areIntersecting && currentIntersectionInProgress === -1) {\r\n                        if (action.trigger === Constants.ACTION_OnIntersectionEnterTrigger) {\r\n                            action._executeCurrent(ActionEvent.CreateNew(sourceMesh, undefined, otherMesh));\r\n                            sourceMesh._intersectionsInProgress.push(otherMesh);\r\n                        } else if (action.trigger === Constants.ACTION_OnIntersectionExitTrigger) {\r\n                            sourceMesh._intersectionsInProgress.push(otherMesh);\r\n                        }\r\n                    } else if (!areIntersecting && currentIntersectionInProgress > -1) {\r\n                        //They intersected, and now they don't.\r\n\r\n                        //is this trigger an exit trigger? execute an event.\r\n                        if (action.trigger === Constants.ACTION_OnIntersectionExitTrigger) {\r\n                            action._executeCurrent(ActionEvent.CreateNew(sourceMesh, undefined, otherMesh));\r\n                        }\r\n\r\n                        //if this is an exit trigger, or no exit trigger exists, remove the id from the intersection in progress array.\r\n                        if (\r\n                            !sourceMesh.actionManager.hasSpecificTrigger(Constants.ACTION_OnIntersectionExitTrigger, (parameter) => {\r\n                                const parameterMesh = parameter.mesh ? parameter.mesh : parameter;\r\n                                return otherMesh === parameterMesh;\r\n                            }) ||\r\n                            action.trigger === Constants.ACTION_OnIntersectionExitTrigger\r\n                        ) {\r\n                            sourceMesh._intersectionsInProgress.splice(currentIntersectionInProgress, 1);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _advancePhysicsEngineStep(step: number) {\r\n        // Do nothing. Code will be replaced if physics engine component is referenced\r\n    }\r\n\r\n    /**\r\n     * User updatable function that will return a deterministic frame time when engine is in deterministic lock step mode\r\n     * @returns the frame time\r\n     */\r\n    public getDeterministicFrameTime: () => number = () => {\r\n        return this._engine.getTimeStep();\r\n    };\r\n\r\n    /** @internal */\r\n    public _animate(customDeltaTime?: number): void {\r\n        // Nothing to do as long as Animatable have not been imported.\r\n    }\r\n\r\n    /** Execute all animations (for a frame) */\r\n    public animate() {\r\n        if (this._engine.isDeterministicLockStep()) {\r\n            let deltaTime = Math.max(Scene.MinDeltaTime, Math.min(this._engine.getDeltaTime(), Scene.MaxDeltaTime)) + this._timeAccumulator;\r\n\r\n            const defaultFrameTime = this._engine.getTimeStep();\r\n            const defaultFPS = 1000.0 / defaultFrameTime / 1000.0;\r\n\r\n            let stepsTaken = 0;\r\n\r\n            const maxSubSteps = this._engine.getLockstepMaxSteps();\r\n\r\n            let internalSteps = Math.floor(deltaTime / defaultFrameTime);\r\n            internalSteps = Math.min(internalSteps, maxSubSteps);\r\n\r\n            while (deltaTime > 0 && stepsTaken < internalSteps) {\r\n                this.onBeforeStepObservable.notifyObservers(this);\r\n\r\n                // Animations\r\n                this._animationRatio = defaultFrameTime * defaultFPS;\r\n                this._animate(defaultFrameTime);\r\n                this.onAfterAnimationsObservable.notifyObservers(this);\r\n\r\n                // Physics\r\n                if (this.physicsEnabled) {\r\n                    this._advancePhysicsEngineStep(defaultFrameTime);\r\n                }\r\n\r\n                this.onAfterStepObservable.notifyObservers(this);\r\n                this._currentStepId++;\r\n\r\n                stepsTaken++;\r\n                deltaTime -= defaultFrameTime;\r\n            }\r\n\r\n            this._timeAccumulator = deltaTime < 0 ? 0 : deltaTime;\r\n        } else {\r\n            // Animations\r\n            const deltaTime = this.useConstantAnimationDeltaTime ? 16 : Math.max(Scene.MinDeltaTime, Math.min(this._engine.getDeltaTime(), Scene.MaxDeltaTime));\r\n            this._animationRatio = deltaTime * (60.0 / 1000.0);\r\n            this._animate();\r\n            this.onAfterAnimationsObservable.notifyObservers(this);\r\n\r\n            // Physics\r\n            if (this.physicsEnabled) {\r\n                this._advancePhysicsEngineStep(deltaTime);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _clear(): void {\r\n        if (this.autoClearDepthAndStencil || this.autoClear) {\r\n            this._engine.clear(this.clearColor, this.autoClear || this.forceWireframe || this.forcePointsCloud, this.autoClearDepthAndStencil, this.autoClearDepthAndStencil);\r\n        }\r\n    }\r\n\r\n    private _checkCameraRenderTarget(camera: Nullable<Camera>) {\r\n        if (camera?.outputRenderTarget && !camera?.isRigCamera) {\r\n            camera.outputRenderTarget._cleared = false;\r\n        }\r\n        if (camera?.rigCameras?.length) {\r\n            for (let i = 0; i < camera.rigCameras.length; ++i) {\r\n                const rtt = camera.rigCameras[i].outputRenderTarget;\r\n                if (rtt) {\r\n                    rtt._cleared = false;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Resets the draw wrappers cache of all meshes\r\n     * @param passId If provided, releases only the draw wrapper corresponding to this render pass id\r\n     */\r\n    public resetDrawCache(passId?: number): void {\r\n        if (!this.meshes) {\r\n            return;\r\n        }\r\n\r\n        for (const mesh of this.meshes) {\r\n            mesh.resetDrawCache(passId);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Render the scene\r\n     * @param updateCameras defines a boolean indicating if cameras must update according to their inputs (true by default)\r\n     * @param ignoreAnimations defines a boolean indicating if animations should not be executed (false by default)\r\n     */\r\n    public render(updateCameras = true, ignoreAnimations = false): void {\r\n        if (this.isDisposed) {\r\n            return;\r\n        }\r\n\r\n        if (this.onReadyObservable.hasObservers() && this._executeWhenReadyTimeoutId === null) {\r\n            this._checkIsReady();\r\n        }\r\n\r\n        this._frameId++;\r\n        this._defaultFrameBufferCleared = false;\r\n        this._checkCameraRenderTarget(this.activeCamera);\r\n        if (this.activeCameras?.length) {\r\n            this.activeCameras.forEach(this._checkCameraRenderTarget);\r\n        }\r\n\r\n        // Register components that have been associated lately to the scene.\r\n        this._registerTransientComponents();\r\n\r\n        this._activeParticles.fetchNewFrame();\r\n        this._totalVertices.fetchNewFrame();\r\n        this._activeIndices.fetchNewFrame();\r\n        this._activeBones.fetchNewFrame();\r\n        this._meshesForIntersections.reset();\r\n        this.resetCachedMaterial();\r\n\r\n        this.onBeforeAnimationsObservable.notifyObservers(this);\r\n\r\n        // Actions\r\n        if (this.actionManager) {\r\n            this.actionManager.processTrigger(Constants.ACTION_OnEveryFrameTrigger);\r\n        }\r\n\r\n        // Animations\r\n        if (!ignoreAnimations) {\r\n            this.animate();\r\n        }\r\n\r\n        // Before camera update steps\r\n        for (const step of this._beforeCameraUpdateStage) {\r\n            step.action();\r\n        }\r\n\r\n        // Update Cameras\r\n        if (updateCameras) {\r\n            if (this.activeCameras && this.activeCameras.length > 0) {\r\n                for (let cameraIndex = 0; cameraIndex < this.activeCameras.length; cameraIndex++) {\r\n                    const camera = this.activeCameras[cameraIndex];\r\n                    camera.update();\r\n                    if (camera.cameraRigMode !== Constants.RIG_MODE_NONE) {\r\n                        // rig cameras\r\n                        for (let index = 0; index < camera._rigCameras.length; index++) {\r\n                            camera._rigCameras[index].update();\r\n                        }\r\n                    }\r\n                }\r\n            } else if (this.activeCamera) {\r\n                this.activeCamera.update();\r\n                if (this.activeCamera.cameraRigMode !== Constants.RIG_MODE_NONE) {\r\n                    // rig cameras\r\n                    for (let index = 0; index < this.activeCamera._rigCameras.length; index++) {\r\n                        this.activeCamera._rigCameras[index].update();\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Before render\r\n        this.onBeforeRenderObservable.notifyObservers(this);\r\n\r\n        const engine = this.getEngine();\r\n\r\n        // Customs render targets\r\n        this.onBeforeRenderTargetsRenderObservable.notifyObservers(this);\r\n\r\n        const currentActiveCamera = this.activeCameras?.length ? this.activeCameras[0] : this.activeCamera;\r\n        if (this.renderTargetsEnabled) {\r\n            Tools.StartPerformanceCounter(\"Custom render targets\", this.customRenderTargets.length > 0);\r\n            this._intermediateRendering = true;\r\n            for (let customIndex = 0; customIndex < this.customRenderTargets.length; customIndex++) {\r\n                const renderTarget = this.customRenderTargets[customIndex];\r\n                if (renderTarget._shouldRender()) {\r\n                    this._renderId++;\r\n\r\n                    this.activeCamera = renderTarget.activeCamera || this.activeCamera;\r\n\r\n                    if (!this.activeCamera) {\r\n                        throw new Error(\"Active camera not set\");\r\n                    }\r\n\r\n                    // Viewport\r\n                    engine.setViewport(this.activeCamera.viewport);\r\n\r\n                    // Camera\r\n                    this.updateTransformMatrix();\r\n\r\n                    renderTarget.render(currentActiveCamera !== this.activeCamera, this.dumpNextRenderTargets);\r\n                }\r\n            }\r\n            Tools.EndPerformanceCounter(\"Custom render targets\", this.customRenderTargets.length > 0);\r\n            this._intermediateRendering = false;\r\n            this._renderId++;\r\n        }\r\n\r\n        this._engine.currentRenderPassId = currentActiveCamera?.renderPassId ?? Constants.RENDERPASS_MAIN;\r\n\r\n        // Restore back buffer\r\n        this.activeCamera = currentActiveCamera;\r\n        if (this._activeCamera && this._activeCamera.cameraRigMode !== Constants.RIG_MODE_CUSTOM && !this.prePass) {\r\n            this._bindFrameBuffer(this._activeCamera, false);\r\n        }\r\n        this.onAfterRenderTargetsRenderObservable.notifyObservers(this);\r\n\r\n        for (const step of this._beforeClearStage) {\r\n            step.action();\r\n        }\r\n\r\n        // Clear\r\n        this._clearFrameBuffer(this.activeCamera);\r\n\r\n        // Collects render targets from external components.\r\n        for (const step of this._gatherRenderTargetsStage) {\r\n            step.action(this._renderTargets);\r\n        }\r\n\r\n        // Multi-cameras?\r\n        if (this.activeCameras && this.activeCameras.length > 0) {\r\n            for (let cameraIndex = 0; cameraIndex < this.activeCameras.length; cameraIndex++) {\r\n                this._processSubCameras(this.activeCameras[cameraIndex], cameraIndex > 0);\r\n            }\r\n        } else {\r\n            if (!this.activeCamera) {\r\n                throw new Error(\"No camera defined\");\r\n            }\r\n\r\n            this._processSubCameras(this.activeCamera, !!this.activeCamera.outputRenderTarget);\r\n        }\r\n\r\n        // Intersection checks\r\n        this._checkIntersections();\r\n\r\n        // Executes the after render stage actions.\r\n        for (const step of this._afterRenderStage) {\r\n            step.action();\r\n        }\r\n\r\n        // After render\r\n        if (this.afterRender) {\r\n            this.afterRender();\r\n        }\r\n\r\n        this.onAfterRenderObservable.notifyObservers(this);\r\n\r\n        // Cleaning\r\n        if (this._toBeDisposed.length) {\r\n            for (let index = 0; index < this._toBeDisposed.length; index++) {\r\n                const data = this._toBeDisposed[index];\r\n                if (data) {\r\n                    data.dispose();\r\n                }\r\n            }\r\n\r\n            this._toBeDisposed.length = 0;\r\n        }\r\n\r\n        if (this.dumpNextRenderTargets) {\r\n            this.dumpNextRenderTargets = false;\r\n        }\r\n\r\n        this._activeBones.addCount(0, true);\r\n        this._activeIndices.addCount(0, true);\r\n        this._activeParticles.addCount(0, true);\r\n\r\n        this._engine.restoreDefaultFramebuffer();\r\n    }\r\n\r\n    /**\r\n     * Freeze all materials\r\n     * A frozen material will not be updatable but should be faster to render\r\n     * Note: multimaterials will not be frozen, but their submaterials will\r\n     */\r\n    public freezeMaterials(): void {\r\n        for (let i = 0; i < this.materials.length; i++) {\r\n            this.materials[i].freeze();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Unfreeze all materials\r\n     * A frozen material will not be updatable but should be faster to render\r\n     */\r\n    public unfreezeMaterials(): void {\r\n        for (let i = 0; i < this.materials.length; i++) {\r\n            this.materials[i].unfreeze();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Releases all held resources\r\n     */\r\n    public dispose(): void {\r\n        if (this.isDisposed) {\r\n            return;\r\n        }\r\n\r\n        this.beforeRender = null;\r\n        this.afterRender = null;\r\n        this.metadata = null;\r\n\r\n        this.skeletons.length = 0;\r\n        this.morphTargetManagers.length = 0;\r\n        this._transientComponents.length = 0;\r\n        this._isReadyForMeshStage.clear();\r\n        this._beforeEvaluateActiveMeshStage.clear();\r\n        this._evaluateSubMeshStage.clear();\r\n        this._preActiveMeshStage.clear();\r\n        this._cameraDrawRenderTargetStage.clear();\r\n        this._beforeCameraDrawStage.clear();\r\n        this._beforeRenderTargetDrawStage.clear();\r\n        this._beforeRenderingGroupDrawStage.clear();\r\n        this._beforeRenderingMeshStage.clear();\r\n        this._afterRenderingMeshStage.clear();\r\n        this._afterRenderingGroupDrawStage.clear();\r\n        this._afterCameraDrawStage.clear();\r\n        this._afterRenderTargetDrawStage.clear();\r\n        this._afterRenderStage.clear();\r\n        this._beforeCameraUpdateStage.clear();\r\n        this._beforeClearStage.clear();\r\n        this._gatherRenderTargetsStage.clear();\r\n        this._gatherActiveCameraRenderTargetsStage.clear();\r\n        this._pointerMoveStage.clear();\r\n        this._pointerDownStage.clear();\r\n        this._pointerUpStage.clear();\r\n\r\n        this.importedMeshesFiles = [] as string[];\r\n\r\n        if (this.stopAllAnimations) {\r\n            // Ensures that no animatable notifies a callback that could start a new animation group, constantly adding new animatables to the active list...\r\n            this._activeAnimatables.forEach((animatable) => {\r\n                animatable.onAnimationEndObservable.clear();\r\n                animatable.onAnimationEnd = null;\r\n            });\r\n            this.stopAllAnimations();\r\n        }\r\n\r\n        this.resetCachedMaterial();\r\n\r\n        // Smart arrays\r\n        if (this.activeCamera) {\r\n            this.activeCamera._activeMeshes.dispose();\r\n            this.activeCamera = null;\r\n        }\r\n        this.activeCameras = null;\r\n\r\n        this._activeMeshes.dispose();\r\n        this._renderingManager.dispose();\r\n        this._processedMaterials.dispose();\r\n        this._activeParticleSystems.dispose();\r\n        this._activeSkeletons.dispose();\r\n        this._softwareSkinnedMeshes.dispose();\r\n        this._renderTargets.dispose();\r\n        this._materialsRenderTargets.dispose();\r\n        this._registeredForLateAnimationBindings.dispose();\r\n        this._meshesForIntersections.dispose();\r\n        this._toBeDisposed.length = 0;\r\n\r\n        // Abort active requests\r\n        const activeRequests = this._activeRequests.slice();\r\n        for (const request of activeRequests) {\r\n            request.abort();\r\n        }\r\n        this._activeRequests.length = 0;\r\n\r\n        // Events\r\n        try {\r\n            this.onDisposeObservable.notifyObservers(this);\r\n        } catch (e) {\r\n            Logger.Error(\"An error occurred while calling onDisposeObservable!\", e);\r\n        }\r\n\r\n        this.detachControl();\r\n\r\n        // Detach cameras\r\n        const canvas = this._engine.getInputElement();\r\n\r\n        if (canvas) {\r\n            for (let index = 0; index < this.cameras.length; index++) {\r\n                this.cameras[index].detachControl();\r\n            }\r\n        }\r\n\r\n        // Release animation groups\r\n        this._disposeList(this.animationGroups);\r\n\r\n        // Release lights\r\n        this._disposeList(this.lights);\r\n\r\n        // Release meshes\r\n        this._disposeList(this.meshes, (item) => item.dispose(true));\r\n        this._disposeList(this.transformNodes, (item) => item.dispose(true));\r\n\r\n        // Release cameras\r\n        const cameras = this.cameras;\r\n        this._disposeList(cameras);\r\n\r\n        // Release materials\r\n        if (this._defaultMaterial) {\r\n            this._defaultMaterial.dispose();\r\n        }\r\n        this._disposeList(this.multiMaterials);\r\n        this._disposeList(this.materials);\r\n\r\n        // Release particles\r\n        this._disposeList(this.particleSystems);\r\n\r\n        // Release postProcesses\r\n        this._disposeList(this.postProcesses);\r\n\r\n        // Release textures\r\n        this._disposeList(this.textures);\r\n\r\n        // Release morph targets\r\n        this._disposeList(this.morphTargetManagers);\r\n\r\n        // Release UBO\r\n        this._sceneUbo.dispose();\r\n\r\n        if (this._multiviewSceneUbo) {\r\n            this._multiviewSceneUbo.dispose();\r\n        }\r\n\r\n        // Post-processes\r\n        this.postProcessManager.dispose();\r\n\r\n        // Components\r\n        this._disposeList(this._components);\r\n\r\n        // Remove from engine\r\n        let index = this._engine.scenes.indexOf(this);\r\n\r\n        if (index > -1) {\r\n            this._engine.scenes.splice(index, 1);\r\n        }\r\n\r\n        if (EngineStore._LastCreatedScene === this) {\r\n            if (this._engine.scenes.length > 0) {\r\n                EngineStore._LastCreatedScene = this._engine.scenes[this._engine.scenes.length - 1];\r\n            } else {\r\n                EngineStore._LastCreatedScene = null;\r\n            }\r\n        }\r\n\r\n        index = this._engine._virtualScenes.indexOf(this);\r\n\r\n        if (index > -1) {\r\n            this._engine._virtualScenes.splice(index, 1);\r\n        }\r\n\r\n        this._engine.wipeCaches(true);\r\n        this.onDisposeObservable.clear();\r\n        this.onBeforeRenderObservable.clear();\r\n        this.onAfterRenderObservable.clear();\r\n        this.onBeforeRenderTargetsRenderObservable.clear();\r\n        this.onAfterRenderTargetsRenderObservable.clear();\r\n        this.onAfterStepObservable.clear();\r\n        this.onBeforeStepObservable.clear();\r\n        this.onBeforeActiveMeshesEvaluationObservable.clear();\r\n        this.onAfterActiveMeshesEvaluationObservable.clear();\r\n        this.onBeforeParticlesRenderingObservable.clear();\r\n        this.onAfterParticlesRenderingObservable.clear();\r\n        this.onBeforeDrawPhaseObservable.clear();\r\n        this.onAfterDrawPhaseObservable.clear();\r\n        this.onBeforeAnimationsObservable.clear();\r\n        this.onAfterAnimationsObservable.clear();\r\n        this.onDataLoadedObservable.clear();\r\n        this.onBeforeRenderingGroupObservable.clear();\r\n        this.onAfterRenderingGroupObservable.clear();\r\n        this.onMeshImportedObservable.clear();\r\n        this.onBeforeCameraRenderObservable.clear();\r\n        this.onAfterCameraRenderObservable.clear();\r\n        this.onAfterRenderCameraObservable.clear();\r\n        this.onReadyObservable.clear();\r\n        this.onNewCameraAddedObservable.clear();\r\n        this.onCameraRemovedObservable.clear();\r\n        this.onNewLightAddedObservable.clear();\r\n        this.onLightRemovedObservable.clear();\r\n        this.onNewGeometryAddedObservable.clear();\r\n        this.onGeometryRemovedObservable.clear();\r\n        this.onNewTransformNodeAddedObservable.clear();\r\n        this.onTransformNodeRemovedObservable.clear();\r\n        this.onNewMeshAddedObservable.clear();\r\n        this.onMeshRemovedObservable.clear();\r\n        this.onNewSkeletonAddedObservable.clear();\r\n        this.onSkeletonRemovedObservable.clear();\r\n        this.onNewMaterialAddedObservable.clear();\r\n        this.onNewMultiMaterialAddedObservable.clear();\r\n        this.onMaterialRemovedObservable.clear();\r\n        this.onMultiMaterialRemovedObservable.clear();\r\n        this.onNewTextureAddedObservable.clear();\r\n        this.onTextureRemovedObservable.clear();\r\n        this.onPrePointerObservable.clear();\r\n        this.onPointerObservable.clear();\r\n        this.onPreKeyboardObservable.clear();\r\n        this.onKeyboardObservable.clear();\r\n        this.onActiveCameraChanged.clear();\r\n        this.onScenePerformancePriorityChangedObservable.clear();\r\n        this._isDisposed = true;\r\n    }\r\n\r\n    private _disposeList<T extends IDisposable>(items: T[], callback?: (item: T) => void): void {\r\n        const itemsCopy = items.slice(0);\r\n        callback = callback ?? ((item) => item.dispose());\r\n        for (const item of itemsCopy) {\r\n            callback(item);\r\n        }\r\n        items.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Gets if the scene is already disposed\r\n     */\r\n    public get isDisposed(): boolean {\r\n        return this._isDisposed;\r\n    }\r\n\r\n    /**\r\n     * Call this function to reduce memory footprint of the scene.\r\n     * Vertex buffers will not store CPU data anymore (this will prevent picking, collisions or physics to work correctly)\r\n     */\r\n    public clearCachedVertexData(): void {\r\n        for (let meshIndex = 0; meshIndex < this.meshes.length; meshIndex++) {\r\n            const mesh = this.meshes[meshIndex];\r\n            const geometry = (<Mesh>mesh).geometry;\r\n\r\n            if (geometry) {\r\n                geometry.clearCachedData();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * This function will remove the local cached buffer data from texture.\r\n     * It will save memory but will prevent the texture from being rebuilt\r\n     */\r\n    public cleanCachedTextureBuffer(): void {\r\n        for (const baseTexture of this.textures) {\r\n            const buffer = (<Texture>baseTexture)._buffer;\r\n\r\n            if (buffer) {\r\n                (<Texture>baseTexture)._buffer = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the world extend vectors with an optional filter\r\n     *\r\n     * @param filterPredicate the predicate - which meshes should be included when calculating the world size\r\n     * @returns {{ min: Vector3; max: Vector3 }} min and max vectors\r\n     */\r\n    public getWorldExtends(filterPredicate?: (mesh: AbstractMesh) => boolean): { min: Vector3; max: Vector3 } {\r\n        const min = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        const max = new Vector3(-Number.MAX_VALUE, -Number.MAX_VALUE, -Number.MAX_VALUE);\r\n        filterPredicate = filterPredicate || (() => true);\r\n        this.meshes.filter(filterPredicate).forEach((mesh) => {\r\n            mesh.computeWorldMatrix(true);\r\n\r\n            if (!mesh.subMeshes || mesh.subMeshes.length === 0 || mesh.infiniteDistance) {\r\n                return;\r\n            }\r\n\r\n            const boundingInfo = mesh.getBoundingInfo();\r\n\r\n            const minBox = boundingInfo.boundingBox.minimumWorld;\r\n            const maxBox = boundingInfo.boundingBox.maximumWorld;\r\n\r\n            Vector3.CheckExtends(minBox, min, max);\r\n            Vector3.CheckExtends(maxBox, min, max);\r\n        });\r\n\r\n        return {\r\n            min: min,\r\n            max: max,\r\n        };\r\n    }\r\n\r\n    // Picking\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Creates a ray that can be used to pick in the scene\r\n     * @param x defines the x coordinate of the origin (on-screen)\r\n     * @param y defines the y coordinate of the origin (on-screen)\r\n     * @param world defines the world matrix to use if you want to pick in object space (instead of world space)\r\n     * @param camera defines the camera to use for the picking\r\n     * @param cameraViewSpace defines if picking will be done in view space (false by default)\r\n     * @returns a Ray\r\n     */\r\n    public createPickingRay(x: number, y: number, world: Nullable<Matrix>, camera: Nullable<Camera>, cameraViewSpace = false): Ray {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Creates a ray that can be used to pick in the scene\r\n     * @param x defines the x coordinate of the origin (on-screen)\r\n     * @param y defines the y coordinate of the origin (on-screen)\r\n     * @param world defines the world matrix to use if you want to pick in object space (instead of world space)\r\n     * @param result defines the ray where to store the picking ray\r\n     * @param camera defines the camera to use for the picking\r\n     * @param cameraViewSpace defines if picking will be done in view space (false by default)\r\n     * @param enableDistantPicking defines if picking should handle large values for mesh position/scaling (false by default)\r\n     * @returns the current scene\r\n     */\r\n    public createPickingRayToRef(\r\n        x: number,\r\n        y: number,\r\n        world: Nullable<Matrix>,\r\n        result: Ray,\r\n        camera: Nullable<Camera>,\r\n        cameraViewSpace = false,\r\n        enableDistantPicking = false\r\n    ): Scene {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Creates a ray that can be used to pick in the scene\r\n     * @param x defines the x coordinate of the origin (on-screen)\r\n     * @param y defines the y coordinate of the origin (on-screen)\r\n     * @param camera defines the camera to use for the picking\r\n     * @returns a Ray\r\n     */\r\n    public createPickingRayInCameraSpace(x: number, y: number, camera?: Camera): Ray {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Creates a ray that can be used to pick in the scene\r\n     * @param x defines the x coordinate of the origin (on-screen)\r\n     * @param y defines the y coordinate of the origin (on-screen)\r\n     * @param result defines the ray where to store the picking ray\r\n     * @param camera defines the camera to use for the picking\r\n     * @returns the current scene\r\n     */\r\n    public createPickingRayInCameraSpaceToRef(x: number, y: number, result: Ray, camera?: Camera): Scene {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    /** @internal */\r\n    public get _pickingAvailable(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /** @internal */\r\n    public _registeredActions: number = 0;\r\n\r\n    /** Launch a ray to try to pick a mesh in the scene\r\n     * @param x position on screen\r\n     * @param y position on screen\r\n     * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @param camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n     * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n     * @returns a PickingInfo\r\n     */\r\n    public pick(\r\n        x: number,\r\n        y: number,\r\n        predicate?: (mesh: AbstractMesh) => boolean,\r\n        fastCheck?: boolean,\r\n        camera?: Nullable<Camera>,\r\n        trianglePredicate?: TrianglePickingPredicate\r\n    ): PickingInfo {\r\n        const warn = _WarnImport(\"Ray\", true);\r\n        if (warn) {\r\n            Logger.Warn(warn);\r\n        }\r\n        // Dummy info if picking as not been imported\r\n        return new PickingInfo();\r\n    }\r\n\r\n    /** Launch a ray to try to pick a mesh in the scene using only bounding information of the main mesh (not using submeshes)\r\n     * @param x position on screen\r\n     * @param y position on screen\r\n     * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @param camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n     * @returns a PickingInfo (Please note that some info will not be set like distance, bv, bu and everything that cannot be capture by only using bounding infos)\r\n     */\r\n    public pickWithBoundingInfo(x: number, y: number, predicate?: (mesh: AbstractMesh) => boolean, fastCheck?: boolean, camera?: Nullable<Camera>): Nullable<PickingInfo> {\r\n        const warn = _WarnImport(\"Ray\", true);\r\n        if (warn) {\r\n            Logger.Warn(warn);\r\n        }\r\n        // Dummy info if picking as not been imported\r\n        return new PickingInfo();\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Use the given ray to pick a mesh in the scene. A mesh triangle can be picked both from its front and back sides,\r\n     * irrespective of orientation.\r\n     * @param ray The ray to use to pick meshes\r\n     * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must have isPickable set to true\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n     * @returns a PickingInfo\r\n     */\r\n    public pickWithRay(ray: Ray, predicate?: (mesh: AbstractMesh) => boolean, fastCheck?: boolean, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo> {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Launch a ray to try to pick a mesh in the scene. A mesh triangle can be picked both from its front and back sides,\r\n     * irrespective of orientation.\r\n     * @param x X position on screen\r\n     * @param y Y position on screen\r\n     * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true\r\n     * @param camera camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n     * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n     * @returns an array of PickingInfo\r\n     */\r\n    public multiPick(x: number, y: number, predicate?: (mesh: AbstractMesh) => boolean, camera?: Camera, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo[]> {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Launch a ray to try to pick a mesh in the scene\r\n     * @param ray Ray to use\r\n     * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true\r\n     * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n     * @returns an array of PickingInfo\r\n     */\r\n    public multiPickWithRay(ray: Ray, predicate?: (mesh: AbstractMesh) => boolean, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo[]> {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    /**\r\n     * Force the value of meshUnderPointer\r\n     * @param mesh defines the mesh to use\r\n     * @param pointerId optional pointer id when using more than one pointer\r\n     * @param pickResult optional pickingInfo data used to find mesh\r\n     */\r\n    public setPointerOverMesh(mesh: Nullable<AbstractMesh>, pointerId?: number, pickResult?: Nullable<PickingInfo>): void {\r\n        this._inputManager.setPointerOverMesh(mesh, pointerId, pickResult);\r\n    }\r\n\r\n    /**\r\n     * Gets the mesh under the pointer\r\n     * @returns a Mesh or null if no mesh is under the pointer\r\n     */\r\n    public getPointerOverMesh(): Nullable<AbstractMesh> {\r\n        return this._inputManager.getPointerOverMesh();\r\n    }\r\n\r\n    // Misc.\r\n    /** @internal */\r\n    public _rebuildGeometries(): void {\r\n        for (const geometry of this.geometries) {\r\n            geometry._rebuild();\r\n        }\r\n\r\n        for (const mesh of this.meshes) {\r\n            mesh._rebuild();\r\n        }\r\n\r\n        if (this.postProcessManager) {\r\n            this.postProcessManager._rebuild();\r\n        }\r\n\r\n        for (const component of this._components) {\r\n            component.rebuild();\r\n        }\r\n\r\n        for (const system of this.particleSystems) {\r\n            system.rebuild();\r\n        }\r\n\r\n        if (this.spriteManagers) {\r\n            for (const spriteMgr of this.spriteManagers) {\r\n                spriteMgr.rebuild();\r\n            }\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuildTextures(): void {\r\n        for (const texture of this.textures) {\r\n            texture._rebuild(true);\r\n        }\r\n\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag);\r\n    }\r\n\r\n    /**\r\n     * Get from a list of objects by tags\r\n     * @param list the list of objects to use\r\n     * @param tagsQuery the query to use\r\n     * @param filter a predicate to filter for tags\r\n     * @returns\r\n     */\r\n    private _getByTags(list: any[], tagsQuery: string, filter?: (item: any) => boolean): any[] {\r\n        if (tagsQuery === undefined) {\r\n            // returns the complete list (could be done with Tags.MatchesQuery but no need to have a for-loop here)\r\n            return list;\r\n        }\r\n\r\n        const listByTags = [];\r\n\r\n        for (const i in list) {\r\n            const item = list[i];\r\n            if (Tags && Tags.MatchesQuery(item, tagsQuery) && (!filter || filter(item))) {\r\n                listByTags.push(item);\r\n            }\r\n        }\r\n\r\n        return listByTags;\r\n    }\r\n\r\n    /**\r\n     * Get a list of meshes by tags\r\n     * @param tagsQuery defines the tags query to use\r\n     * @param filter defines a predicate used to filter results\r\n     * @returns an array of Mesh\r\n     */\r\n    public getMeshesByTags(tagsQuery: string, filter?: (mesh: AbstractMesh) => boolean): AbstractMesh[] {\r\n        return this._getByTags(this.meshes, tagsQuery, filter);\r\n    }\r\n\r\n    /**\r\n     * Get a list of cameras by tags\r\n     * @param tagsQuery defines the tags query to use\r\n     * @param filter defines a predicate used to filter results\r\n     * @returns an array of Camera\r\n     */\r\n    public getCamerasByTags(tagsQuery: string, filter?: (camera: Camera) => boolean): Camera[] {\r\n        return this._getByTags(this.cameras, tagsQuery, filter);\r\n    }\r\n\r\n    /**\r\n     * Get a list of lights by tags\r\n     * @param tagsQuery defines the tags query to use\r\n     * @param filter defines a predicate used to filter results\r\n     * @returns an array of Light\r\n     */\r\n    public getLightsByTags(tagsQuery: string, filter?: (light: Light) => boolean): Light[] {\r\n        return this._getByTags(this.lights, tagsQuery, filter);\r\n    }\r\n\r\n    /**\r\n     * Get a list of materials by tags\r\n     * @param tagsQuery defines the tags query to use\r\n     * @param filter defines a predicate used to filter results\r\n     * @returns an array of Material\r\n     */\r\n    public getMaterialByTags(tagsQuery: string, filter?: (material: Material) => boolean): Material[] {\r\n        return this._getByTags(this.materials, tagsQuery, filter).concat(this._getByTags(this.multiMaterials, tagsQuery, filter));\r\n    }\r\n\r\n    /**\r\n     * Get a list of transform nodes by tags\r\n     * @param tagsQuery defines the tags query to use\r\n     * @param filter defines a predicate used to filter results\r\n     * @returns an array of TransformNode\r\n     */\r\n    public getTransformNodesByTags(tagsQuery: string, filter?: (transform: TransformNode) => boolean): TransformNode[] {\r\n        return this._getByTags(this.transformNodes, tagsQuery, filter);\r\n    }\r\n\r\n    /**\r\n     * Overrides the default sort function applied in the rendering group to prepare the meshes.\r\n     * This allowed control for front to back rendering or reversly depending of the special needs.\r\n     *\r\n     * @param renderingGroupId The rendering group id corresponding to its index\r\n     * @param opaqueSortCompareFn The opaque queue comparison function use to sort.\r\n     * @param alphaTestSortCompareFn The alpha test queue comparison function use to sort.\r\n     * @param transparentSortCompareFn The transparent queue comparison function use to sort.\r\n     */\r\n    public setRenderingOrder(\r\n        renderingGroupId: number,\r\n        opaqueSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null,\r\n        alphaTestSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null,\r\n        transparentSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null\r\n    ): void {\r\n        this._renderingManager.setRenderingOrder(renderingGroupId, opaqueSortCompareFn, alphaTestSortCompareFn, transparentSortCompareFn);\r\n    }\r\n\r\n    /**\r\n     * Specifies whether or not the stencil and depth buffer are cleared between two rendering groups.\r\n     *\r\n     * @param renderingGroupId The rendering group id corresponding to its index\r\n     * @param autoClearDepthStencil Automatically clears depth and stencil between groups if true.\r\n     * @param depth Automatically clears depth between groups if true and autoClear is true.\r\n     * @param stencil Automatically clears stencil between groups if true and autoClear is true.\r\n     */\r\n    public setRenderingAutoClearDepthStencil(renderingGroupId: number, autoClearDepthStencil: boolean, depth = true, stencil = true): void {\r\n        this._renderingManager.setRenderingAutoClearDepthStencil(renderingGroupId, autoClearDepthStencil, depth, stencil);\r\n    }\r\n\r\n    /**\r\n     * Gets the current auto clear configuration for one rendering group of the rendering\r\n     * manager.\r\n     * @param index the rendering group index to get the information for\r\n     * @returns The auto clear setup for the requested rendering group\r\n     */\r\n    public getAutoClearDepthStencilSetup(index: number): IRenderingManagerAutoClearSetup {\r\n        return this._renderingManager.getAutoClearDepthStencilSetup(index);\r\n    }\r\n\r\n    private _blockMaterialDirtyMechanism = false;\r\n\r\n    /** @internal */\r\n    public _forceBlockMaterialDirtyMechanism(value: boolean) {\r\n        this._blockMaterialDirtyMechanism = value;\r\n    }\r\n\r\n    /** Gets or sets a boolean blocking all the calls to markAllMaterialsAsDirty (ie. the materials won't be updated if they are out of sync) */\r\n    public get blockMaterialDirtyMechanism(): boolean {\r\n        return this._blockMaterialDirtyMechanism;\r\n    }\r\n\r\n    public set blockMaterialDirtyMechanism(value: boolean) {\r\n        if (this._blockMaterialDirtyMechanism === value) {\r\n            return;\r\n        }\r\n\r\n        this._blockMaterialDirtyMechanism = value;\r\n\r\n        if (!value) {\r\n            // Do a complete update\r\n            this.markAllMaterialsAsDirty(Constants.MATERIAL_AllDirtyFlag);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Will flag all materials as dirty to trigger new shader compilation\r\n     * @param flag defines the flag used to specify which material part must be marked as dirty\r\n     * @param predicate If not null, it will be used to specify if a material has to be marked as dirty\r\n     */\r\n    public markAllMaterialsAsDirty(flag: number, predicate?: (mat: Material) => boolean): void {\r\n        if (this._blockMaterialDirtyMechanism) {\r\n            return;\r\n        }\r\n\r\n        for (const material of this.materials) {\r\n            if (predicate && !predicate(material)) {\r\n                continue;\r\n            }\r\n            material.markAsDirty(flag);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadFile(\r\n        fileOrUrl: File | string,\r\n        onSuccess: (data: string | ArrayBuffer, responseURL?: string) => void,\r\n        onProgress?: (ev: ProgressEvent) => void,\r\n        useOfflineSupport?: boolean,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (request?: WebRequest, exception?: LoadFileError) => void,\r\n        onOpened?: (request: WebRequest) => void\r\n    ): IFileRequest {\r\n        const request = LoadFile(fileOrUrl, onSuccess, onProgress, useOfflineSupport ? this.offlineProvider : undefined, useArrayBuffer, onError, onOpened);\r\n        this._activeRequests.push(request);\r\n        request.onCompleteObservable.add((request) => {\r\n            this._activeRequests.splice(this._activeRequests.indexOf(request), 1);\r\n        });\r\n        return request;\r\n    }\r\n\r\n    public _loadFileAsync(\r\n        fileOrUrl: File | string,\r\n        onProgress?: (data: any) => void,\r\n        useOfflineSupport?: boolean,\r\n        useArrayBuffer?: false,\r\n        onOpened?: (request: WebRequest) => void\r\n    ): Promise<string>;\r\n\r\n    public _loadFileAsync(\r\n        fileOrUrl: File | string,\r\n        onProgress?: (data: any) => void,\r\n        useOfflineSupport?: boolean,\r\n        useArrayBuffer?: true,\r\n        onOpened?: (request: WebRequest) => void\r\n    ): Promise<ArrayBuffer>;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadFileAsync(\r\n        fileOrUrl: File | string,\r\n        onProgress?: (data: any) => void,\r\n        useOfflineSupport?: boolean,\r\n        useArrayBuffer?: boolean,\r\n        onOpened?: (request: WebRequest) => void\r\n    ): Promise<string | ArrayBuffer> {\r\n        return new Promise((resolve, reject) => {\r\n            this._loadFile(\r\n                fileOrUrl,\r\n                (data) => {\r\n                    resolve(data);\r\n                },\r\n                onProgress,\r\n                useOfflineSupport,\r\n                useArrayBuffer,\r\n                (request, exception) => {\r\n                    reject(exception);\r\n                },\r\n                onOpened\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _requestFile(\r\n        url: string,\r\n        onSuccess: (data: string | ArrayBuffer, request?: WebRequest) => void,\r\n        onProgress?: (ev: ProgressEvent) => void,\r\n        useOfflineSupport?: boolean,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (error: RequestFileError) => void,\r\n        onOpened?: (request: WebRequest) => void\r\n    ): IFileRequest {\r\n        const request = RequestFile(url, onSuccess, onProgress, useOfflineSupport ? this.offlineProvider : undefined, useArrayBuffer, onError, onOpened);\r\n        this._activeRequests.push(request);\r\n        request.onCompleteObservable.add((request) => {\r\n            this._activeRequests.splice(this._activeRequests.indexOf(request), 1);\r\n        });\r\n        return request;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _requestFileAsync(\r\n        url: string,\r\n        onProgress?: (ev: ProgressEvent) => void,\r\n        useOfflineSupport?: boolean,\r\n        useArrayBuffer?: boolean,\r\n        onOpened?: (request: WebRequest) => void\r\n    ): Promise<string | ArrayBuffer> {\r\n        return new Promise((resolve, reject) => {\r\n            this._requestFile(\r\n                url,\r\n                (data) => {\r\n                    resolve(data);\r\n                },\r\n                onProgress,\r\n                useOfflineSupport,\r\n                useArrayBuffer,\r\n                (error) => {\r\n                    reject(error);\r\n                },\r\n                onOpened\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _readFile(\r\n        file: File,\r\n        onSuccess: (data: string | ArrayBuffer) => void,\r\n        onProgress?: (ev: ProgressEvent) => any,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (error: ReadFileError) => void\r\n    ): IFileRequest {\r\n        const request = ReadFile(file, onSuccess, onProgress, useArrayBuffer, onError);\r\n        this._activeRequests.push(request);\r\n        request.onCompleteObservable.add((request) => {\r\n            this._activeRequests.splice(this._activeRequests.indexOf(request), 1);\r\n        });\r\n        return request;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _readFileAsync(file: File, onProgress?: (ev: ProgressEvent) => any, useArrayBuffer?: boolean): Promise<string | ArrayBuffer> {\r\n        return new Promise((resolve, reject) => {\r\n            this._readFile(\r\n                file,\r\n                (data) => {\r\n                    resolve(data);\r\n                },\r\n                onProgress,\r\n                useArrayBuffer,\r\n                (error) => {\r\n                    reject(error);\r\n                }\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Internal perfCollector instance used for sharing between inspector and playground.\r\n     * Marked as protected to allow sharing between prototype extensions, but disallow access at toplevel.\r\n     */\r\n    protected _perfCollector: Nullable<PerformanceViewerCollector> = null;\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * This method gets the performance collector belonging to the scene, which is generally shared with the inspector.\r\n     * @returns the perf collector belonging to the scene.\r\n     */\r\n    public getPerfCollector(): PerformanceViewerCollector {\r\n        throw _WarnImport(\"performanceViewerSceneExtension\");\r\n    }\r\n\r\n    // deprecated\r\n\r\n    /**\r\n     * Sets the active camera of the scene using its Id\r\n     * @param id defines the camera's Id\r\n     * @returns the new active camera or null if none found.\r\n     * @deprecated Please use setActiveCameraById instead\r\n     */\r\n    setActiveCameraByID(id: string): Nullable<Camera> {\r\n        return this.setActiveCameraById(id);\r\n    }\r\n    /**\r\n     * Get a material using its id\r\n     * @param id defines the material's Id\r\n     * @returns the material or null if none found.\r\n     * @deprecated Please use getMaterialById instead\r\n     */\r\n    getMaterialByID(id: string): Nullable<Material> {\r\n        return this.getMaterialById(id);\r\n    }\r\n    /**\r\n     * Gets a the last added material using a given id\r\n     * @param id defines the material's Id\r\n     * @returns the last material with the given id or null if none found.\r\n     * @deprecated Please use getLastMaterialById instead\r\n     */\r\n    getLastMaterialByID(id: string): Nullable<Material> {\r\n        return this.getLastMaterialById(id);\r\n    }\r\n\r\n    /**\r\n     * Get a texture using its unique id\r\n     * @param uniqueId defines the texture's unique id\r\n     * @returns the texture or null if none found.\r\n     * @deprecated Please use getTextureByUniqueId instead\r\n     */\r\n    getTextureByUniqueID(uniqueId: number): Nullable<BaseTexture> {\r\n        return this.getTextureByUniqueId(uniqueId);\r\n    }\r\n    /**\r\n     * Gets a camera using its Id\r\n     * @param id defines the Id to look for\r\n     * @returns the camera or null if not found\r\n     * @deprecated Please use getCameraById instead\r\n     */\r\n    getCameraByID(id: string): Nullable<Camera> {\r\n        return this.getCameraById(id);\r\n    }\r\n    /**\r\n     * Gets a camera using its unique Id\r\n     * @param uniqueId defines the unique Id to look for\r\n     * @returns the camera or null if not found\r\n     * @deprecated Please use getCameraByUniqueId instead\r\n     */\r\n    getCameraByUniqueID(uniqueId: number): Nullable<Camera> {\r\n        return this.getCameraByUniqueId(uniqueId);\r\n    }\r\n    /**\r\n     * Gets a bone using its Id\r\n     * @param id defines the bone's Id\r\n     * @returns the bone or null if not found\r\n     * @deprecated Please use getBoneById instead\r\n     */\r\n    getBoneByID(id: string): Nullable<Bone> {\r\n        return this.getBoneById(id);\r\n    }\r\n    /**\r\n     * Gets a light node using its Id\r\n     * @param id defines the light's Id\r\n     * @returns the light or null if none found.\r\n     * @deprecated Please use getLightById instead\r\n     */\r\n    getLightByID(id: string): Nullable<Light> {\r\n        return this.getLightById(id);\r\n    }\r\n    /**\r\n     * Gets a light node using its scene-generated unique Id\r\n     * @param uniqueId defines the light's unique Id\r\n     * @returns the light or null if none found.\r\n     * @deprecated Please use getLightByUniqueId instead\r\n     */\r\n    getLightByUniqueID(uniqueId: number): Nullable<Light> {\r\n        return this.getLightByUniqueId(uniqueId);\r\n    }\r\n    /**\r\n     * Gets a particle system by Id\r\n     * @param id defines the particle system Id\r\n     * @returns the corresponding system or null if none found\r\n     * @deprecated Please use getParticleSystemById instead\r\n     */\r\n    getParticleSystemByID(id: string): Nullable<IParticleSystem> {\r\n        return this.getParticleSystemById(id);\r\n    }\r\n    /**\r\n     * Gets a geometry using its Id\r\n     * @param id defines the geometry's Id\r\n     * @returns the geometry or null if none found.\r\n     * @deprecated Please use getGeometryById instead\r\n     */\r\n    getGeometryByID(id: string): Nullable<Geometry> {\r\n        return this.getGeometryById(id);\r\n    }\r\n    /**\r\n     * Gets the first added mesh found of a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the mesh found or null if not found at all\r\n     * @deprecated Please use getMeshById instead\r\n     */\r\n    getMeshByID(id: string): Nullable<AbstractMesh> {\r\n        return this.getMeshById(id);\r\n    }\r\n    /**\r\n     * Gets a mesh with its auto-generated unique Id\r\n     * @param uniqueId defines the unique Id to search for\r\n     * @returns the found mesh or null if not found at all.\r\n     * @deprecated Please use getMeshByUniqueId instead\r\n     */\r\n    getMeshByUniqueID(uniqueId: number): Nullable<AbstractMesh> {\r\n        return this.getMeshByUniqueId(uniqueId);\r\n    }\r\n    /**\r\n     * Gets a the last added mesh using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found mesh or null if not found at all.\r\n     * @deprecated Please use getLastMeshById instead\r\n     */\r\n    getLastMeshByID(id: string): Nullable<AbstractMesh> {\r\n        return this.getLastMeshById(id);\r\n    }\r\n    /**\r\n     * Gets a list of meshes using their Id\r\n     * @param id defines the Id to search for\r\n     * @returns a list of meshes\r\n     * @deprecated Please use getMeshesById instead\r\n     */\r\n    getMeshesByID(id: string): Array<AbstractMesh> {\r\n        return this.getMeshesById(id);\r\n    }\r\n    /**\r\n     * Gets the first added transform node found of a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found transform node or null if not found at all.\r\n     * @deprecated Please use getTransformNodeById instead\r\n     */\r\n    getTransformNodeByID(id: string): Nullable<TransformNode> {\r\n        return this.getTransformNodeById(id);\r\n    }\r\n    /**\r\n     * Gets a transform node with its auto-generated unique Id\r\n     * @param uniqueId defines the unique Id to search for\r\n     * @returns the found transform node or null if not found at all.\r\n     * @deprecated Please use getTransformNodeByUniqueId instead\r\n     */\r\n    getTransformNodeByUniqueID(uniqueId: number): Nullable<TransformNode> {\r\n        return this.getTransformNodeByUniqueId(uniqueId);\r\n    }\r\n    /**\r\n     * Gets a list of transform nodes using their Id\r\n     * @param id defines the Id to search for\r\n     * @returns a list of transform nodes\r\n     * @deprecated Please use getTransformNodesById instead\r\n     */\r\n    getTransformNodesByID(id: string): Array<TransformNode> {\r\n        return this.getTransformNodesById(id);\r\n    }\r\n    /**\r\n     * Gets a node (Mesh, Camera, Light) using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found node or null if not found at all\r\n     * @deprecated Please use getNodeById instead\r\n     */\r\n    getNodeByID(id: string): Nullable<Node> {\r\n        return this.getNodeById(id);\r\n    }\r\n    /**\r\n     * Gets a the last added node (Mesh, Camera, Light) using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found node or null if not found at all\r\n     * @deprecated Please use getLastEntryById instead\r\n     */\r\n    getLastEntryByID(id: string): Nullable<Node> {\r\n        return this.getLastEntryById(id);\r\n    }\r\n    /**\r\n     * Gets a skeleton using a given Id (if many are found, this function will pick the last one)\r\n     * @param id defines the Id to search for\r\n     * @returns the found skeleton or null if not found at all.\r\n     * @deprecated Please use getLastSkeletonById instead\r\n     */\r\n    getLastSkeletonByID(id: string): Nullable<Skeleton> {\r\n        return this.getLastSkeletonById(id);\r\n    }\r\n}\r\n"]}