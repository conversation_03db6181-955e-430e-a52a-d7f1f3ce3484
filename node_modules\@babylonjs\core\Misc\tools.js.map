{"version": 3, "file": "tools.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/tools.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAC;AAC/F,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD,OAAO,EACH,gBAAgB,EAChB,uBAAuB,EACvB,eAAe,EACf,QAAQ,IAAI,iBAAiB,EAC7B,SAAS,IAAI,iBAAiB,EAC9B,QAAQ,IAAI,iBAAiB,EAC7B,eAAe,GAClB,MAAM,aAAa,CAAC;AAErB,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AAKpC,OAAO,EAAE,eAAe,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;AAIzD;;GAEG;AACH,MAAM,OAAO,KAAK;IACd;;OAEG;IACI,MAAM,KAAK,OAAO;QACrB,OAAO,gBAAgB,CAAC,OAAO,CAAC;IACpC,CAAC;IAEM,MAAM,KAAK,OAAO,CAAC,KAAa;QACnC,gBAAgB,CAAC,OAAO,GAAG,KAAK,CAAC;IACrC,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,aAAa,CAAC,GAAW;QACnC,4CAA4C;QAE5C,wCAAwC;QACxC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC;SACf;QAED,mCAAmC;QACnC,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;YAC3B,OAAO,KAAK,CAAC;SAChB;QAED,sEAAsE;QACtE,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YACzB,OAAO,KAAK,CAAC;SAChB;QAED,mDAAmD;QACnD,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YACzB,OAAO,KAAK,CAAC;SAChB;QAED,2DAA2D;QAC3D,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACrC,OAAO,KAAK,CAAC;SAChB;QAED,oDAAoD;QACpD,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACvC,OAAO,IAAI,CAAC;SACf;QACD,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC1D,OAAO,IAAI,CAAC;SACf;QAED,iCAAiC;QACjC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,aAAa,CAAC,KAAa;QACzC,gBAAgB,CAAC,aAAa,GAAG,KAAK,CAAC;IAC3C,CAAC;IAEM,MAAM,KAAK,aAAa;QAC3B,OAAO,gBAAgB,CAAC,aAAa,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACI,MAAM,KAAK,mBAAmB,CAAC,IAAgC;QAClE,gBAAgB,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAChD,CAAC;IAEM,MAAM,KAAK,mBAAmB;QACjC,OAAO,gBAAgB,CAAC,mBAAmB,CAAC;IAChD,CAAC;IAeD;;OAEG;IACI,MAAM,KAAK,oBAAoB;QAClC,OAAO,gBAAgB,CAAC,oBAAoB,CAAC;IACjD,CAAC;IAEM,MAAM,KAAK,oBAAoB,CAAC,QAA0E;QAC7G,gBAAgB,CAAC,oBAAoB,GAAG,QAAQ,CAAC;IACrD,CAAC;IAED;;;;OAIG;IACI,MAAM,KAAK,YAAY;QAC1B,OAAO,gBAAgB,CAAC,YAAY,CAAC;IACzC,CAAC;IAEM,MAAM,KAAK,YAAY,CAAC,KAAoD;QAC/E,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACI,MAAM,KAAK,kBAAkB;QAChC,OAAO,WAAW,CAAC,kBAAkB,CAAC;IAC1C,CAAC;IAEM,MAAM,KAAK,kBAAkB,CAAC,KAAc;QAC/C,WAAW,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,MAAM,KAAK,yBAAyB;QACvC,OAAO,kBAAkB,CAAC,yBAAyB,CAAC;IACxD,CAAC;IAEM,MAAM,KAAK,yBAAyB,CAAC,OAAkC;QAC1E,kBAAkB,CAAC,yBAAyB,GAAG,OAAO,CAAC;IAC3D,CAAC;IAED;;;OAGG;IACH,gEAAgE;IACzD,MAAM,KAAK,eAAe;QAC7B,OAAO,WAAW,CAAC,eAAe,CAAC;IACvC,CAAC;IAED,gEAAgE;IACzD,MAAM,KAAK,eAAe,CAAC,KAAa;QAC3C,WAAW,CAAC,eAAe,GAAG,KAAK,CAAC;IACxC,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,UAAU,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAE,MAAkB,EAAE,KAAkB;QAChH,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;QACnD,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;QAErD,MAAM,QAAQ,GAAG,CAAC,QAAQ,GAAG,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QACnD,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;QACjC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;QACrC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;QACrC,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;IACzC,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa;QACjD,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,WAAW,CAAC,SAAiB;QACvC,OAAO,kBAAkB,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,YAAY,CAAC,MAAkB;QACzC,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,eAAe,CAAC,KAAa;QACvC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,UAAU,CAAC,KAAa;QAClC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,WAAW,CAAC,IAAY;QAClC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IACrC,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,aAAa,CAAC,GAAW,EAAE,wBAAwB,GAAG,KAAK;QACrE,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,IAAI,wBAAwB,EAAE;gBAC1B,OAAO,GAAG,CAAC;aACd;YACD,OAAO,EAAE,CAAC;SACb;QAED,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;IACvC,CAAC;IAQD;;;;OAIG;IACI,MAAM,CAAC,SAAS,CAAC,KAAa;QACjC,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IACnC,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,SAAS,CAAC,KAAa;QACjC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;IACnC,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,iBAAiB,CAAC,aAAqB,EAAE,QAAgB,EAAE,YAAY,GAAG,GAAG;QACvF,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACvD,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,SAAS,CACjB,IAAI,CAAC,KAAK,CACN,CAAC,CAAC,GAAG,YAAY,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,EACtF,CAAC,CAAC,GAAG,YAAY,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CACzF,CACJ,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,SAAS,CAAC,GAAQ,EAAE,mBAA6B;QAC3D,IAAI,mBAAmB,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE;YACpE,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,gBAAgB,CAAC,MAAc;QACzC,IAAI,WAAW,GAAG,SAAS,CAAC;QAE5B,wCAAwC;QACxC,IAAI,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC/C,WAAW,GAAG,OAAO,CAAC;SACzB;QAED,mCAAmC;QACnC,IACI,MAAM,CAAC,aAAa;YACpB,CAAC,MAAM,CAAC,MAAM;YACd,4CAA4C;YAC5C,CAAC,CAAC,QAAQ,IAAI,YAAY,IAAI,QAAQ,CAAC,EACzC;YACE,WAAW,GAAG,OAAO,CAAC;SACzB;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,eAAe,CAAC,GAAsB,EAAE,OAAuC;QACzF,eAAe,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAClC,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,yBAAyB,CAAC,cAAwC,EAAE,OAA0C;QACxH,OAAO,CAAC,cAAc,GAAG,cAAc,CAAC;IAC5C,CAAC;IAED,iBAAiB;IAEjB;;;;OAIG;IACI,MAAM,CAAC,QAAQ,CAAC,GAAW;QAC9B,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAChC,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,aAAa;QAC3B,OAAO,gBAAgB,CAAC,aAAa,CAAC;IAC1C,CAAC;IAEM,MAAM,KAAK,aAAa,CAAC,SAAkC;QAC9D,gBAAgB,CAAC,aAAa,GAAG,SAAS,CAAC;IAC/C,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAC,SAAS,CACnB,KAAkC,EAClC,MAAqD,EACrD,OAAoD,EACpD,eAA2C,EAC3C,QAAiB,EACjB,kBAAuC;QAEvC,OAAO,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,kBAAkB,CAAC,CAAC;IACpG,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAC,QAAQ,CAClB,GAAW,EACX,SAAqE,EACrE,UAAgC,EAChC,eAAkC,EAClC,cAAwB,EACxB,OAAyD;QAEzD,OAAO,iBAAiB,CAAC,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IACnG,CAAC;IAMD;;;;;OAKG;IACI,MAAM,CAAC,aAAa,CAAC,GAAW,EAAE,cAAc,GAAG,IAAI;QAC1D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,iBAAiB,CACb,GAAG,EACH,CAAC,IAAI,EAAE,EAAE;gBACL,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,EACD,SAAS,EACT,SAAS,EACT,cAAc,EACd,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;gBACnB,MAAM,CAAC,SAAS,CAAC,CAAC;YACtB,CAAC,CACJ,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAOD;;;;;OAKG;IACI,MAAM,CAAC,mBAAmB,CAAC,SAA2B,EAAE,gBAA0B;QACrF,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,EAAE,CAAC;SACb;QACD,0FAA0F;QAC1F,IAAI,KAAK,CAAC,aAAa,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;YACnE,mFAAmF;YACnF,yCAAyC;YAEzC,MAAM,OAAO,GAAG,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC;YACrK,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;SAChE;QAED,uBAAuB;QACvB,SAAS,GAAG,KAAK,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAEjD,IAAI,gBAAgB,EAAE;YAClB,SAAS,GAAG,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;SAC/C;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,iBAAiB,CAAC,SAAiB,EAAE,SAAqB,EAAE,OAAqD,EAAE,QAAiB;QAC9I,SAAS,GAAG,KAAK,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QACjD,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,sBAAsB,CAAC,SAAiB;QAClD,SAAS,GAAG,KAAK,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QACjD,OAAO,KAAK,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,UAAU,CAAC,SAAiB,EAAE,SAAqB,EAAE,OAAqD,EAAE,QAAiB;QACvI,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE;YACrC,IAAI;gBACA,aAAa,CAAC,SAAS,CAAC,CAAC;gBACzB,SAAS,EAAE,CAAC;aACf;YAAC,OAAO,CAAC,EAAE;gBACR,OAAO,EAAE,CAAC,0BAA0B,SAAS,aAAa,EAAE,CAAC,CAAC,CAAC;aAClE;YACD,OAAO;SACV;aAAM,IAAI,CAAC,mBAAmB,EAAE,EAAE;YAC/B,OAAO,EAAE,CAAC,uBAAuB,SAAS,mCAAmC,CAAC,CAAC;YAC/E,OAAO;SACV;QACD,MAAM,IAAI,GAAG,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;QAC/C,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QACtC,IAAI,QAAQ,EAAE;YACV,MAAM,CAAC,EAAE,GAAG,QAAQ,CAAC;SACxB;QAED,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE;YACjB,IAAI,SAAS,EAAE;gBACX,SAAS,EAAE,CAAC;aACf;QACL,CAAC,CAAC;QAEF,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE;YACnB,IAAI,OAAO,EAAE;gBACT,OAAO,CAAC,0BAA0B,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC;aACtD;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,eAAe,CAAC,SAAiB,EAAE,QAAiB;QAC9D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,UAAU,CACX,SAAS,EACT,GAAG,EAAE;gBACD,OAAO,EAAE,CAAC;YACd,CAAC,EACD,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;gBACnB,MAAM,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5C,CAAC,EACD,QAAQ,CACX,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,iBAAiB,CAAC,UAAgB,EAAE,QAA6B,EAAE,gBAA4C;QACzH,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAEhC,MAAM,OAAO,GAAiB;YAC1B,oBAAoB,EAAE,IAAI,UAAU,EAAgB;YACpD,KAAK,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE;SAC9B,CAAC;QAEF,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE;YACpB,OAAO,CAAC,oBAAoB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAC1D,CAAC,CAAC;QAEF,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE;YAClB,wCAAwC;YACxC,QAAQ,CAAO,CAAC,CAAC,MAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC;QAEF,MAAM,CAAC,UAAU,GAAG,gBAAgB,CAAC;QAErC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAEjC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,QAAQ,CAClB,IAAU,EACV,SAA8B,EAC9B,UAAuC,EACvC,cAAwB,EACxB,OAAwC;QAExC,OAAO,iBAAiB,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IACnF,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,SAAS,CAAC,OAAe;QACnC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;QACvB,MAAM,IAAI,GAAW,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,MAAM,CAAC,KAAa,EAAE,QAAQ,GAAG,CAAC;QAC5C,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACnC,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,QAAQ,CAAC,MAAW,EAAE,WAAgB,EAAE,aAAwB,EAAE,YAAuB;QACnG,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;IAC1E,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,OAAO,CAAC,GAAQ;QAC1B,KAAK,MAAM,CAAC,IAAI,GAAG,EAAE;YACjB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;gBAC9C,OAAO,KAAK,CAAC;aAChB;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,qBAAqB,CAAC,aAAqB,EAAE,MAAqE;QAC5H,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5B,aAAa,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAO,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAEtE,IAAI;gBACA,IAAI,MAAM,CAAC,MAAM,EAAE;oBACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAO,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;iBACzE;aACJ;YAAC,OAAO,CAAC,EAAE;gBACR,oBAAoB;aACvB;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,uBAAuB,CAAC,aAAqB,EAAE,MAAqE;QAC9H,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5B,aAAa,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,EAAO,KAAK,CAAC,OAAO,CAAC,CAAC;YAElE,IAAI;gBACA,IAAI,aAAa,CAAC,MAAM,EAAE;oBACtB,aAAa,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,EAAO,KAAK,CAAC,OAAO,CAAC,CAAC;iBAC5E;aACJ;YAAC,OAAO,CAAC,EAAE;gBACR,oBAAoB;aACvB;SACJ;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAC,KAAK,CAAC,eAAe,CAC/B,KAAa,EACb,MAAc,EACd,MAAc,EACd,eAAwC,EACxC,QAAQ,GAAG,WAAW,EACtB,QAAiB,EACjB,OAAgB;QAEhB,MAAM,WAAW,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;IAED;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,QAAQ,CAClB,KAAa,EACb,MAAc,EACd,IAAqB,EACrB,eAAsD,EACtD,QAAQ,GAAG,WAAW,EACtB,QAAiB,EACjB,OAAO,GAAG,KAAK,EACf,aAAa,GAAG,KAAK,EACrB,OAAgB;QAEhB,MAAM,WAAW,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;IAED,uDAAuD;IACvD;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,aAAa,CACvB,KAAa,EACb,MAAc,EACd,IAAqB,EACrB,QAAQ,GAAG,WAAW,EACtB,QAAiB,EACjB,OAAO,GAAG,KAAK,EACf,aAAa,GAAG,KAAK,EACrB,OAAgB;QAEhB,MAAM,WAAW,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,MAA2C;QACzE,OAAQ,MAA0B,CAAC,aAAa,KAAK,SAAS,CAAC;IACnE,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,MAAM,CAAC,MAA2C,EAAE,eAA+C,EAAE,QAAQ,GAAG,WAAW,EAAE,OAAgB;QAChJ,sDAAsD;QACtD,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACrD,2HAA2H;YAC3H,MAAM,CAAC,MAAM,GAAG,UAAU,QAAQ,EAAE,IAAI,EAAE,OAAO;gBAC7C,UAAU,CAAC,GAAG,EAAE;oBACZ,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5D,GAAG,GAAG,MAAM,CAAC,MAAM,EACnB,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;oBAE9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;wBAC1B,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;qBACjC;oBACD,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC9B,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;SACL;QACD,IAAI,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE;YAClC,MAAM;iBACD,aAAa,CAAC;gBACX,IAAI,EAAE,QAAQ;gBACd,OAAO;aACV,CAAC;iBACD,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;SAC9C;aAAM;YACH,MAAM,CAAC,MAAM,CACT,UAAU,IAAI;gBACV,eAAe,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC,EACD,QAAQ,EACR,OAAO,CACV,CAAC;SACL;IACL,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,YAAY,CAAC,IAAU,EAAE,QAAiB;QAC7C,2HAA2H;QAC3H,IAAI,UAAU,IAAI,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;YAC3C,IAAI,CAAC,QAAQ,EAAE;gBACX,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;gBACxB,MAAM,UAAU,GACZ,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3J,QAAQ,GAAG,aAAa,GAAG,UAAU,GAAG,MAAM,CAAC;aAClD;YACD,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAClC;aAAM;YACH,IAAI,IAAI,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;gBACpC,MAAM,GAAG,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAEtC,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAClC,IAAI,CAAC,SAAS,EAAE;oBACZ,OAAO;iBACV;gBACD,MAAM,GAAG,GAAG,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBACpD,GAAG,CAAC,MAAM,GAAG;oBACT,kDAAkD;oBAClD,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBAC7B,CAAC,CAAC;gBACF,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;gBACd,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;aAC5C;SACJ;IACL,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,0BAA0B,CAC7B,MAA2C,EAC3C,eAAwC,EACxC,QAAQ,GAAG,WAAW,EACtB,QAAiB,EACjB,OAAgB;QAEhB,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,eAAe,EAAE;YAClD,IAAI,CAAC,MAAM,CACP,MAAM,EACN,UAAU,IAAI;gBACV,IAAI,IAAI,EAAE;oBACN,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;iBACtC;gBACD,IAAI,eAAe,EAAE;oBACjB,eAAe,CAAC,EAAE,CAAC,CAAC;iBACvB;YACL,CAAC,EACD,QAAQ,EACR,OAAO,CACV,CAAC;SACL;aAAM,IAAI,eAAe,EAAE;YACxB,IAAI,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE;gBAClC,MAAM;qBACD,aAAa,CAAC;oBACX,IAAI,EAAE,QAAQ;oBACd,OAAO;iBACV,CAAC;qBACD,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;oBACX,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;oBAChC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;oBAC3B,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE;wBACpB,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;wBACjC,eAAe,CAAC,UAAoB,CAAC,CAAC;oBAC1C,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC;gBACP,OAAO;aACV;YACD,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACxD,eAAe,CAAC,WAAW,CAAC,CAAC;SAChC;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,QAAQ,CAAC,IAAU,EAAE,QAAgB;QAC/C,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;YAC5B,OAAO;SACV;QAED,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC7C,MAAM,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QACtC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QACzB,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC;QACb,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACtB,CAAC,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YAC7B,IAAI,CAAC,CAAC,aAAa,EAAE;gBACjB,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;aAClC;QACL,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,KAAK,EAAE,CAAC;QACV,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,gCAAgC,CAAC,IAAgB;QAC3D,wBAAwB;QACxB,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YAC9B,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;SAClB;aAAM,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YACrC,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;SAClB;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,6DAA6D;IACtD,MAAM,CAAC,gBAAgB,CAC1B,MAAc,EACd,MAAc,EACd,IAA8B,EAC9B,eAAwC,EACxC,QAAQ,GAAG,WAAW,EACtB,aAAa,GAAG,KAAK,EACrB,OAAgB;QAEhB,MAAM,WAAW,CAAC,iBAAiB,CAAC,CAAC;IACzC,CAAC;IAED,uDAAuD;IACvD;;;;;;;;;;;;;;;OAeG;IACH,6DAA6D;IACtD,MAAM,CAAC,qBAAqB,CAAC,MAAc,EAAE,MAAc,EAAE,IAA8B,EAAE,QAAQ,GAAG,WAAW,EAAE,OAAgB;QACxI,MAAM,WAAW,CAAC,iBAAiB,CAAC,CAAC;IACzC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,6DAA6D;IACtD,MAAM,CAAC,iCAAiC,CAC3C,MAAc,EACd,MAAc,EACd,IAA8B,EAC9B,eAAwC,EACxC,QAAQ,GAAG,WAAW,EACtB,OAAO,GAAG,CAAC,EACX,YAAY,GAAG,KAAK,EACpB,QAAiB,EACjB,aAAa,GAAG,KAAK,EACrB,mBAAmB,GAAG,KAAK,EAC3B,YAAY,GAAG,IAAI,EACnB,OAAgB;QAEhB,MAAM,WAAW,CAAC,iBAAiB,CAAC,CAAC;IACzC,CAAC;IAED,uDAAuD;IACvD;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,6DAA6D;IACtD,MAAM,CAAC,sCAAsC,CAChD,MAAc,EACd,MAAc,EACd,IAA8B,EAC9B,QAAQ,GAAG,WAAW,EACtB,OAAO,GAAG,CAAC,EACX,YAAY,GAAG,KAAK,EACpB,QAAiB,EACjB,aAAa,GAAG,KAAK,EACrB,mBAAmB,GAAG,KAAK,EAC3B,YAAY,GAAG,IAAI,EACnB,OAAgB;QAEhB,MAAM,WAAW,CAAC,iBAAiB,CAAC,CAAC;IACzC,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,QAAQ;QAClB,OAAO,UAAU,EAAE,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,QAAQ,CAAC,GAAW;QAC9B,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,YAAY,CAAC,GAAW;QAClC,OAAO,uBAAuB,CAAC,GAAG,CAAC,CAAC;IACxC,CAAC;IAyCD;;;OAGG;IACH,gEAAgE;IACzD,MAAM,KAAK,WAAW;QACzB,OAAO,MAAM,CAAC,WAAW,CAAC;IAC9B,CAAC;IAOD;;;OAGG;IACI,MAAM,CAAC,GAAG,CAAC,OAAe;QAC7B,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,IAAI,CAAC,OAAe;QAC9B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,KAAK,CAAC,OAAe;QAC/B,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,QAAQ;QACtB,OAAO,MAAM,CAAC,QAAQ,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,aAAa;QACvB,MAAM,CAAC,aAAa,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,SAAS,CAAC,KAAa;QACrC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;IAC7B,CAAC;IAyBD;;OAEG;IACI,MAAM,KAAK,mBAAmB,CAAC,KAAa;QAC/C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,2BAA2B,CAAC,KAAK,KAAK,CAAC,2BAA2B,EAAE;YACnF,KAAK,CAAC,uBAAuB,GAAG,KAAK,CAAC,cAAc,CAAC;YACrD,KAAK,CAAC,qBAAqB,GAAG,KAAK,CAAC,YAAY,CAAC;YACjD,OAAO;SACV;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,0BAA0B,CAAC,KAAK,KAAK,CAAC,0BAA0B,EAAE;YACjF,KAAK,CAAC,uBAAuB,GAAG,KAAK,CAAC,wBAAwB,CAAC;YAC/D,KAAK,CAAC,qBAAqB,GAAG,KAAK,CAAC,sBAAsB,CAAC;YAC3D,OAAO;SACV;QAED,KAAK,CAAC,uBAAuB,GAAG,KAAK,CAAC,gCAAgC,CAAC;QACvE,KAAK,CAAC,qBAAqB,GAAG,KAAK,CAAC,8BAA8B,CAAC;IACvE,CAAC;IAED,6DAA6D;IACrD,MAAM,CAAC,gCAAgC,CAAC,WAAmB,EAAE,SAAmB,IAAS,CAAC;IAElG,6DAA6D;IACrD,MAAM,CAAC,8BAA8B,CAAC,WAAmB,EAAE,SAAmB,IAAS,CAAC;IAExF,MAAM,CAAC,cAAc,CAAC,WAAmB,EAAE,SAAS,GAAG,IAAI;QAC/D,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,mBAAmB,EAAE,EAAE;gBACxB,OAAO;aACV;YACD,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,WAAW,CAAC;SAC3C;QAED,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE;YACxC,OAAO;SACV;QACD,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC;IACpD,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,WAAmB,EAAE,SAAS,GAAG,IAAI;QAC7D,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE;YACxC,OAAO;SACV;QACD,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC;QAC9C,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,GAAG,QAAQ,EAAE,WAAW,GAAG,MAAM,CAAC,CAAC;IAC1F,CAAC;IAEO,MAAM,CAAC,wBAAwB,CAAC,WAAmB,EAAE,SAAS,GAAG,IAAI;QACzE,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO;SACV;QAED,KAAK,CAAC,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAE7C,IAAI,OAAO,CAAC,IAAI,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC7B;IACL,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,WAAmB,EAAE,SAAS,GAAG,IAAI;QACvE,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO;SACV;QAED,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAE3C,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACjC,CAAC;IAYD;;OAEG;IACI,MAAM,KAAK,GAAG;QACjB,OAAO,aAAa,CAAC,GAAG,CAAC;IAC7B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,YAAY,CAAC,MAAW,EAAE,MAAM,GAAG,KAAK;QAClD,IAAI,IAAI,GAAG,IAAI,CAAC;QAEhB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE;YAChC,IAAI,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;SAChC;aAAM;YACH,IAAI,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACjE,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;aACnD;YACD,IAAI,CAAC,IAAI,EAAE;gBACP,IAAI,GAAG,OAAO,MAAM,CAAC;aACxB;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAI,KAAe,EAAE,SAA+B;QACnE,KAAK,MAAM,EAAE,IAAI,KAAK,EAAE;YACpB,IAAI,SAAS,CAAC,EAAE,CAAC,EAAE;gBACf,OAAO,EAAE,CAAC;aACb;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,gEAAgE;IACzD,MAAM,CAAC,gBAAgB,CAAC,MAAW,EAAE,MAAM,GAAG,KAAK;QACtD,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,IAAI,UAAU,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE;YAChC,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;SACrC;aAAM;YACH,IAAI,MAAM,YAAY,MAAM,EAAE;gBAC1B,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACjE,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;gBACrD,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;aAC1D;YACD,IAAI,CAAC,SAAS,EAAE;gBACZ,SAAS,GAAG,OAAO,MAAM,CAAC;aAC7B;SACJ;QAED,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QAED,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,UAAU,CAAC,KAAa;QAClC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,UAAU,CAAC,GAAG,EAAE;gBACZ,OAAO,EAAE,CAAC;YACd,CAAC,EAAE,KAAK,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,QAAQ;QAClB,IAAI,CAAC,oBAAoB,EAAE,EAAE;YACzB,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,gCAAgC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IACtE,CAAC;;AAp0CD;;;;GAIG;AACW,6BAAuB,GAAG,KAAK,CAAC;AAE9C;;;GAGG;AACW,0BAAoB,GAAG,UAAU,CAAC,oBAAoB,CAAC;AAmKrE;;;GAGG;AACW,uBAAiB,GAAG,iBAAiB,CAAC;AA+LpD;;GAEG;AACW,oBAAc,GAAG,2BAA2B,CAAC;AAgqB3D,4EAA4E;AAC5E;;GAEG;AACW,oBAAc,GACxB,OAAO,QAAQ,KAAK,QAAQ;IACxB,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE;QACJ,MAAM,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QACtC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC;QACb,OAAO,CAAC,CAAC,IAAI,CAAC;IAClB,CAAC;IACH,CAAC,CAAC,OAAO,GAAG,KAAK,UAAU,IAAI,OAAO,QAAQ,KAAK,QAAQ;QACzD,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI;QAC7C,CAAC,CAAC,GAAG,EAAE;YACD,MAAM,IAAI,KAAK,CAAC,uHAAuH,CAAC,CAAC;QAC7I,CAAC,CAAC;AAEd,OAAO;AACP;;GAEG;AACoB,kBAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AAC1D;;GAEG;AACoB,qBAAe,GAAG,MAAM,CAAC,eAAe,CAAC;AAChE;;GAEG;AACoB,qBAAe,GAAG,MAAM,CAAC,eAAe,CAAC;AAChE;;GAEG;AACoB,mBAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAC5D;;GAEG;AACoB,iBAAW,GAAG,MAAM,CAAC,WAAW,CAAC;AA6DxD;;;GAGG;AACW,yBAAmB,GAAG,mBAAmB,CAAC;AAExD,eAAe;AAEf;;GAEG;AACoB,6BAAuB,GAAG,CAAC,CAAC;AACnD;;GAEG;AACoB,iCAA2B,GAAG,CAAC,CAAC;AACvD;;GAEG;AACoB,gCAA0B,GAAG,CAAC,CAAC;AA0EtD;;GAEG;AACW,6BAAuB,GAAuD,KAAK,CAAC,gCAAgC,CAAC;AAEnI;;GAEG;AACW,2BAAqB,GAAuD,KAAK,CAAC,8BAA8B,CAAC;AA4GnI;;;;;;;GAOG;AACH,MAAM,UAAU,SAAS,CAAC,IAAY,EAAE,MAAe;IACnD,OAAO,CAAC,MAAc,EAAE,EAAE;QAChB,MAAO,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC;QACnC,MAAO,CAAC,mBAAmB,CAAC,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;IACxE,CAAC,CAAC;AACN,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,SAAS;IASlB;;;;;;OAMG;IACH;IACI;;OAEG;IACI,UAAkB,EACzB,IAAoC,EACpC,eAA2B,EAC3B,MAAM,GAAG,CAAC;QAHH,eAAU,GAAV,UAAU,CAAQ;QAKzB,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;QAChB,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,WAAW;QACd,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE;gBAClC,EAAE,IAAI,CAAC,KAAK,CAAC;gBACb,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aAClB;iBAAM;gBACH,IAAI,CAAC,SAAS,EAAE,CAAC;aACpB;SACJ;IACL,CAAC;IAED;;OAEG;IACI,SAAS;QACZ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,GAAG,CAAC,UAAkB,EAAE,EAAkC,EAAE,eAA2B,EAAE,MAAM,GAAG,CAAC;QAC7G,MAAM,IAAI,GAAG,IAAI,SAAS,CAAC,UAAU,EAAE,EAAE,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;QAEpE,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAC,gBAAgB,CAC1B,UAAkB,EAClB,gBAAwB,EACxB,EAA+B,EAC/B,QAAoB,EACpB,aAA6B,EAC7B,OAAO,GAAG,CAAC;QAEX,OAAO,SAAS,CAAC,GAAG,CAChB,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,EACxC,CAAC,IAAe,EAAE,EAAE;YAChB,IAAI,aAAa,IAAI,aAAa,EAAE,EAAE;gBAClC,IAAI,CAAC,SAAS,EAAE,CAAC;aACpB;iBAAM;gBACH,UAAU,CAAC,GAAG,EAAE;oBACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,EAAE,CAAC,EAAE;wBACvC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,GAAG,gBAAgB,GAAG,CAAC,CAAC;wBACpD,IAAI,SAAS,IAAI,UAAU,EAAE;4BACzB,MAAM;yBACT;wBACD,EAAE,CAAC,SAAS,CAAC,CAAC;wBACd,IAAI,aAAa,IAAI,aAAa,EAAE,EAAE;4BAClC,IAAI,CAAC,SAAS,EAAE,CAAC;4BACjB,MAAM;yBACT;qBACJ;oBACD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACvB,CAAC,EAAE,OAAO,CAAC,CAAC;aACf;QACL,CAAC,EACD,QAAQ,CACX,CAAC;IACN,CAAC;CACJ;AAED,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;AAChB,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;AAExC,8FAA8F;AAC9F,WAAW,CAAC,eAAe;IACvB,guHAAguH,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport type { Nullable } from \"../types\";\r\nimport { Observable } from \"./observable\";\r\nimport { GetDOMTextContent, IsNavigatorAvailable, IsWindowObjectExist } from \"./domManagement\";\r\nimport { Logger } from \"./logger\";\r\nimport { DeepCopier } from \"./deepCopier\";\r\nimport { PrecisionDate } from \"./precisionDate\";\r\nimport { _WarnImport } from \"./devTools\";\r\nimport { WebRequest } from \"./webRequest\";\r\nimport type { IFileRequest } from \"./fileRequest\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { ReadFileError } from \"./fileTools\";\r\nimport {\r\n    FileToolsOptions,\r\n    DecodeBase64UrlToBinary,\r\n    IsBase64DataUrl,\r\n    LoadFile as FileToolsLoadFile,\r\n    LoadImage as FileToolLoadImage,\r\n    ReadFile as FileToolsReadFile,\r\n    SetCorsBehavior,\r\n} from \"./fileTools\";\r\nimport type { IOfflineProvider } from \"../Offline/IOfflineProvider\";\r\nimport { TimingTools } from \"./timingTools\";\r\nimport { InstantiationTools } from \"./instantiationTools\";\r\nimport { RandomGUID } from \"./guid\";\r\nimport type { IScreenshotSize } from \"./interfaces/screenshotSize\";\r\nimport type { Engine } from \"../Engines/engine\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { IColor4Like } from \"../Maths/math.like\";\r\nimport { IsExponentOfTwo, Mix } from \"./tools.functions\";\r\n\r\ndeclare function importScripts(...urls: string[]): void;\r\n\r\n/**\r\n * Class containing a set of static utilities functions\r\n */\r\nexport class Tools {\r\n    /**\r\n     * Gets or sets the base URL to use to load assets\r\n     */\r\n    public static get BaseUrl() {\r\n        return FileToolsOptions.BaseUrl;\r\n    }\r\n\r\n    public static set BaseUrl(value: string) {\r\n        FileToolsOptions.BaseUrl = value;\r\n    }\r\n\r\n    /**\r\n     * This function checks whether a URL is absolute or not.\r\n     * It will also detect data and blob URLs\r\n     * @param url the url to check\r\n     * @returns is the url absolute or relative\r\n     */\r\n    public static IsAbsoluteUrl(url: string): boolean {\r\n        // See https://stackoverflow.com/a/38979205.\r\n\r\n        // URL is protocol-relative (= absolute)\r\n        if (url.indexOf(\"//\") === 0) {\r\n            return true;\r\n        }\r\n\r\n        // URL has no protocol (= relative)\r\n        if (url.indexOf(\"://\") === -1) {\r\n            return false;\r\n        }\r\n\r\n        // URL does not contain a dot, i.e. no TLD (= relative, possibly REST)\r\n        if (url.indexOf(\".\") === -1) {\r\n            return false;\r\n        }\r\n\r\n        // URL does not contain a single slash (= relative)\r\n        if (url.indexOf(\"/\") === -1) {\r\n            return false;\r\n        }\r\n\r\n        // The first colon comes after the first slash (= relative)\r\n        if (url.indexOf(\":\") > url.indexOf(\"/\")) {\r\n            return false;\r\n        }\r\n\r\n        // Protocol is defined before first dot (= absolute)\r\n        if (url.indexOf(\"://\") < url.indexOf(\".\")) {\r\n            return true;\r\n        }\r\n        if (url.indexOf(\"data:\") === 0 || url.indexOf(\"blob:\") === 0) {\r\n            return true;\r\n        }\r\n\r\n        // Anything else must be relative\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Sets the base URL to use to load scripts\r\n     */\r\n    public static set ScriptBaseUrl(value: string) {\r\n        FileToolsOptions.ScriptBaseUrl = value;\r\n    }\r\n\r\n    public static get ScriptBaseUrl(): string {\r\n        return FileToolsOptions.ScriptBaseUrl;\r\n    }\r\n\r\n    /**\r\n     * Sets a preprocessing function to run on a source URL before importing it\r\n     * Note that this function will execute AFTER the base URL is appended to the URL\r\n     */\r\n    public static set ScriptPreprocessUrl(func: (source: string) => string) {\r\n        FileToolsOptions.ScriptPreprocessUrl = func;\r\n    }\r\n\r\n    public static get ScriptPreprocessUrl(): (source: string) => string {\r\n        return FileToolsOptions.ScriptPreprocessUrl;\r\n    }\r\n\r\n    /**\r\n     * Enable/Disable Custom HTTP Request Headers globally.\r\n     * default = false\r\n     * @see CustomRequestHeaders\r\n     */\r\n    public static UseCustomRequestHeaders = false;\r\n\r\n    /**\r\n     * Custom HTTP Request Headers to be sent with XMLHttpRequests\r\n     * i.e. when loading files, where the server/service expects an Authorization header\r\n     */\r\n    public static CustomRequestHeaders = WebRequest.CustomRequestHeaders;\r\n\r\n    /**\r\n     * Gets or sets the retry strategy to apply when an error happens while loading an asset\r\n     */\r\n    public static get DefaultRetryStrategy() {\r\n        return FileToolsOptions.DefaultRetryStrategy;\r\n    }\r\n\r\n    public static set DefaultRetryStrategy(strategy: (url: string, request: WebRequest, retryIndex: number) => number) {\r\n        FileToolsOptions.DefaultRetryStrategy = strategy;\r\n    }\r\n\r\n    /**\r\n     * Default behavior for cors in the application.\r\n     * It can be a string if the expected behavior is identical in the entire app.\r\n     * Or a callback to be able to set it per url or on a group of them (in case of Video source for instance)\r\n     */\r\n    public static get CorsBehavior(): string | ((url: string | string[]) => string) {\r\n        return FileToolsOptions.CorsBehavior;\r\n    }\r\n\r\n    public static set CorsBehavior(value: string | ((url: string | string[]) => string)) {\r\n        FileToolsOptions.CorsBehavior = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a global variable indicating if fallback texture must be used when a texture cannot be loaded\r\n     * @ignorenaming\r\n     */\r\n    public static get UseFallbackTexture() {\r\n        return EngineStore.UseFallbackTexture;\r\n    }\r\n\r\n    public static set UseFallbackTexture(value: boolean) {\r\n        EngineStore.UseFallbackTexture = value;\r\n    }\r\n\r\n    /**\r\n     * Use this object to register external classes like custom textures or material\r\n     * to allow the loaders to instantiate them\r\n     */\r\n    public static get RegisteredExternalClasses() {\r\n        return InstantiationTools.RegisteredExternalClasses;\r\n    }\r\n\r\n    public static set RegisteredExternalClasses(classes: { [key: string]: Object }) {\r\n        InstantiationTools.RegisteredExternalClasses = classes;\r\n    }\r\n\r\n    /**\r\n     * Texture content used if a texture cannot loaded\r\n     * @ignorenaming\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static get fallbackTexture() {\r\n        return EngineStore.FallbackTexture;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static set fallbackTexture(value: string) {\r\n        EngineStore.FallbackTexture = value;\r\n    }\r\n\r\n    /**\r\n     * Read the content of a byte array at a specified coordinates (taking in account wrapping)\r\n     * @param u defines the coordinate on X axis\r\n     * @param v defines the coordinate on Y axis\r\n     * @param width defines the width of the source data\r\n     * @param height defines the height of the source data\r\n     * @param pixels defines the source byte array\r\n     * @param color defines the output color\r\n     */\r\n    public static FetchToRef(u: number, v: number, width: number, height: number, pixels: Uint8Array, color: IColor4Like): void {\r\n        const wrappedU = (Math.abs(u) * width) % width | 0;\r\n        const wrappedV = (Math.abs(v) * height) % height | 0;\r\n\r\n        const position = (wrappedU + wrappedV * width) * 4;\r\n        color.r = pixels[position] / 255;\r\n        color.g = pixels[position + 1] / 255;\r\n        color.b = pixels[position + 2] / 255;\r\n        color.a = pixels[position + 3] / 255;\r\n    }\r\n\r\n    /**\r\n     * Interpolates between a and b via alpha\r\n     * @param a The lower value (returned when alpha = 0)\r\n     * @param b The upper value (returned when alpha = 1)\r\n     * @param alpha The interpolation-factor\r\n     * @returns The mixed value\r\n     */\r\n    public static Mix(a: number, b: number, alpha: number): number {\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Tries to instantiate a new object from a given class name\r\n     * @param className defines the class name to instantiate\r\n     * @returns the new object or null if the system was not able to do the instantiation\r\n     */\r\n    public static Instantiate(className: string): any {\r\n        return InstantiationTools.Instantiate(className);\r\n    }\r\n\r\n    /**\r\n     * Polyfill for setImmediate\r\n     * @param action defines the action to execute after the current execution block\r\n     */\r\n    public static SetImmediate(action: () => void) {\r\n        TimingTools.SetImmediate(action);\r\n    }\r\n\r\n    /**\r\n     * Function indicating if a number is an exponent of 2\r\n     * @param value defines the value to test\r\n     * @returns true if the value is an exponent of 2\r\n     */\r\n    public static IsExponentOfTwo(value: number): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Returns the nearest 32-bit single precision float representation of a Number\r\n     * @param value A Number.  If the parameter is of a different type, it will get converted\r\n     * to a number or to NaN if it cannot be converted\r\n     * @returns number\r\n     */\r\n    public static FloatRound(value: number): number {\r\n        return Math.fround(value);\r\n    }\r\n\r\n    /**\r\n     * Extracts the filename from a path\r\n     * @param path defines the path to use\r\n     * @returns the filename\r\n     */\r\n    public static GetFilename(path: string): string {\r\n        const index = path.lastIndexOf(\"/\");\r\n        if (index < 0) {\r\n            return path;\r\n        }\r\n\r\n        return path.substring(index + 1);\r\n    }\r\n\r\n    /**\r\n     * Extracts the \"folder\" part of a path (everything before the filename).\r\n     * @param uri The URI to extract the info from\r\n     * @param returnUnchangedIfNoSlash Do not touch the URI if no slashes are present\r\n     * @returns The \"folder\" part of the path\r\n     */\r\n    public static GetFolderPath(uri: string, returnUnchangedIfNoSlash = false): string {\r\n        const index = uri.lastIndexOf(\"/\");\r\n        if (index < 0) {\r\n            if (returnUnchangedIfNoSlash) {\r\n                return uri;\r\n            }\r\n            return \"\";\r\n        }\r\n\r\n        return uri.substring(0, index + 1);\r\n    }\r\n\r\n    /**\r\n     * Extracts text content from a DOM element hierarchy\r\n     * Back Compat only, please use GetDOMTextContent instead.\r\n     */\r\n    public static GetDOMTextContent = GetDOMTextContent;\r\n\r\n    /**\r\n     * Convert an angle in radians to degrees\r\n     * @param angle defines the angle to convert\r\n     * @returns the angle in degrees\r\n     */\r\n    public static ToDegrees(angle: number): number {\r\n        return (angle * 180) / Math.PI;\r\n    }\r\n\r\n    /**\r\n     * Convert an angle in degrees to radians\r\n     * @param angle defines the angle to convert\r\n     * @returns the angle in radians\r\n     */\r\n    public static ToRadians(angle: number): number {\r\n        return (angle * Math.PI) / 180;\r\n    }\r\n\r\n    /**\r\n     * Smooth angle changes (kind of low-pass filter), in particular for device orientation \"shaking\"\r\n     * Use trigonometric functions to avoid discontinuity (0/360, -180/180)\r\n     * @param previousAngle defines last angle value, in degrees\r\n     * @param newAngle defines new angle value, in degrees\r\n     * @param smoothFactor defines smoothing sensitivity; min 0: no smoothing, max 1: new data ignored\r\n     * @returns the angle in degrees\r\n     */\r\n    public static SmoothAngleChange(previousAngle: number, newAngle: number, smoothFactor = 0.9): number {\r\n        const previousAngleRad = this.ToRadians(previousAngle);\r\n        const newAngleRad = this.ToRadians(newAngle);\r\n        return this.ToDegrees(\r\n            Math.atan2(\r\n                (1 - smoothFactor) * Math.sin(newAngleRad) + smoothFactor * Math.sin(previousAngleRad),\r\n                (1 - smoothFactor) * Math.cos(newAngleRad) + smoothFactor * Math.cos(previousAngleRad)\r\n            )\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Returns an array if obj is not an array\r\n     * @param obj defines the object to evaluate as an array\r\n     * @param allowsNullUndefined defines a boolean indicating if obj is allowed to be null or undefined\r\n     * @returns either obj directly if obj is an array or a new array containing obj\r\n     */\r\n    public static MakeArray(obj: any, allowsNullUndefined?: boolean): Nullable<Array<any>> {\r\n        if (allowsNullUndefined !== true && (obj === undefined || obj == null)) {\r\n            return null;\r\n        }\r\n\r\n        return Array.isArray(obj) ? obj : [obj];\r\n    }\r\n\r\n    /**\r\n     * Gets the pointer prefix to use\r\n     * @param engine defines the engine we are finding the prefix for\r\n     * @returns \"pointer\" if touch is enabled. Else returns \"mouse\"\r\n     */\r\n    public static GetPointerPrefix(engine: Engine): string {\r\n        let eventPrefix = \"pointer\";\r\n\r\n        // Check if pointer events are supported\r\n        if (IsWindowObjectExist() && !window.PointerEvent) {\r\n            eventPrefix = \"mouse\";\r\n        }\r\n\r\n        // Special Fallback MacOS Safari...\r\n        if (\r\n            engine._badDesktopOS &&\r\n            !engine._badOS &&\r\n            // And not ipad pros who claim to be macs...\r\n            !(document && \"ontouchend\" in document)\r\n        ) {\r\n            eventPrefix = \"mouse\";\r\n        }\r\n\r\n        return eventPrefix;\r\n    }\r\n\r\n    /**\r\n     * Sets the cors behavior on a dom element. This will add the required Tools.CorsBehavior to the element.\r\n     * @param url define the url we are trying\r\n     * @param element define the dom element where to configure the cors policy\r\n     * @param element.crossOrigin\r\n     */\r\n    public static SetCorsBehavior(url: string | string[], element: { crossOrigin: string | null }): void {\r\n        SetCorsBehavior(url, element);\r\n    }\r\n\r\n    /**\r\n     * Sets the referrerPolicy behavior on a dom element.\r\n     * @param referrerPolicy define the referrer policy to use\r\n     * @param element define the dom element where to configure the referrer policy\r\n     * @param element.referrerPolicy\r\n     */\r\n    public static SetReferrerPolicyBehavior(referrerPolicy: Nullable<ReferrerPolicy>, element: { referrerPolicy: string | null }): void {\r\n        element.referrerPolicy = referrerPolicy;\r\n    }\r\n\r\n    // External files\r\n\r\n    /**\r\n     * Removes unwanted characters from an url\r\n     * @param url defines the url to clean\r\n     * @returns the cleaned url\r\n     */\r\n    public static CleanUrl(url: string): string {\r\n        url = url.replace(/#/gm, \"%23\");\r\n        return url;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a function used to pre-process url before using them to load assets\r\n     */\r\n    public static get PreprocessUrl() {\r\n        return FileToolsOptions.PreprocessUrl;\r\n    }\r\n\r\n    public static set PreprocessUrl(processor: (url: string) => string) {\r\n        FileToolsOptions.PreprocessUrl = processor;\r\n    }\r\n\r\n    /**\r\n     * Loads an image as an HTMLImageElement.\r\n     * @param input url string, ArrayBuffer, or Blob to load\r\n     * @param onLoad callback called when the image successfully loads\r\n     * @param onError callback called when the image fails to load\r\n     * @param offlineProvider offline provider for caching\r\n     * @param mimeType optional mime type\r\n     * @param imageBitmapOptions optional the options to use when creating an ImageBitmap\r\n     * @returns the HTMLImageElement of the loaded image\r\n     */\r\n    public static LoadImage(\r\n        input: string | ArrayBuffer | Blob,\r\n        onLoad: (img: HTMLImageElement | ImageBitmap) => void,\r\n        onError: (message?: string, exception?: any) => void,\r\n        offlineProvider: Nullable<IOfflineProvider>,\r\n        mimeType?: string,\r\n        imageBitmapOptions?: ImageBitmapOptions\r\n    ): Nullable<HTMLImageElement> {\r\n        return FileToolLoadImage(input, onLoad, onError, offlineProvider, mimeType, imageBitmapOptions);\r\n    }\r\n\r\n    /**\r\n     * Loads a file from a url\r\n     * @param url url string, ArrayBuffer, or Blob to load\r\n     * @param onSuccess callback called when the file successfully loads\r\n     * @param onProgress callback called while file is loading (if the server supports this mode)\r\n     * @param offlineProvider defines the offline provider for caching\r\n     * @param useArrayBuffer defines a boolean indicating that date must be returned as ArrayBuffer\r\n     * @param onError callback called when the file fails to load\r\n     * @returns a file request object\r\n     */\r\n    public static LoadFile(\r\n        url: string,\r\n        onSuccess: (data: string | ArrayBuffer, responseURL?: string) => void,\r\n        onProgress?: (data: any) => void,\r\n        offlineProvider?: IOfflineProvider,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (request?: WebRequest, exception?: any) => void\r\n    ): IFileRequest {\r\n        return FileToolsLoadFile(url, onSuccess, onProgress, offlineProvider, useArrayBuffer, onError);\r\n    }\r\n\r\n    // Note that this must come first since useArrayBuffer defaults to true below.\r\n    public static LoadFileAsync(url: string, useArrayBuffer?: true): Promise<ArrayBuffer>;\r\n    public static LoadFileAsync(url: string, useArrayBuffer?: false): Promise<string>;\r\n\r\n    /**\r\n     * Loads a file from a url\r\n     * @param url the file url to load\r\n     * @param useArrayBuffer defines a boolean indicating that date must be returned as ArrayBuffer\r\n     * @returns a promise containing an ArrayBuffer corresponding to the loaded file\r\n     */\r\n    public static LoadFileAsync(url: string, useArrayBuffer = true): Promise<ArrayBuffer | string> {\r\n        return new Promise((resolve, reject) => {\r\n            FileToolsLoadFile(\r\n                url,\r\n                (data) => {\r\n                    resolve(data);\r\n                },\r\n                undefined,\r\n                undefined,\r\n                useArrayBuffer,\r\n                (request, exception) => {\r\n                    reject(exception);\r\n                }\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _DefaultCdnUrl = \"https://cdn.babylonjs.com\";\r\n\r\n    /**\r\n     * Get a script URL including preprocessing\r\n     * @param scriptUrl the script Url to process\r\n     * @param forceAbsoluteUrl force the script to be an absolute url (adding the current base url if necessary)\r\n     * @returns a modified URL to use\r\n     */\r\n    public static GetBabylonScriptURL(scriptUrl: Nullable<string>, forceAbsoluteUrl?: boolean): string {\r\n        if (!scriptUrl) {\r\n            return \"\";\r\n        }\r\n        // if the base URL was set, and the script Url is an absolute path change the default path\r\n        if (Tools.ScriptBaseUrl && scriptUrl.startsWith(Tools._DefaultCdnUrl)) {\r\n            // change the default host, which is https://cdn.babylonjs.com with the one defined\r\n            // make sure no trailing slash is present\r\n\r\n            const baseUrl = Tools.ScriptBaseUrl[Tools.ScriptBaseUrl.length - 1] === \"/\" ? Tools.ScriptBaseUrl.substring(0, Tools.ScriptBaseUrl.length - 1) : Tools.ScriptBaseUrl;\r\n            scriptUrl = scriptUrl.replace(Tools._DefaultCdnUrl, baseUrl);\r\n        }\r\n\r\n        // run the preprocessor\r\n        scriptUrl = Tools.ScriptPreprocessUrl(scriptUrl);\r\n\r\n        if (forceAbsoluteUrl) {\r\n            scriptUrl = Tools.GetAbsoluteUrl(scriptUrl);\r\n        }\r\n\r\n        return scriptUrl;\r\n    }\r\n\r\n    /**\r\n     * This function is used internally by babylon components to load a script (identified by an url). When the url returns, the\r\n     * content of this file is added into a new script element, attached to the DOM (body element)\r\n     * @param scriptUrl defines the url of the script to load\r\n     * @param onSuccess defines the callback called when the script is loaded\r\n     * @param onError defines the callback to call if an error occurs\r\n     * @param scriptId defines the id of the script element\r\n     */\r\n    public static LoadBabylonScript(scriptUrl: string, onSuccess: () => void, onError?: (message?: string, exception?: any) => void, scriptId?: string) {\r\n        scriptUrl = Tools.GetBabylonScriptURL(scriptUrl);\r\n        Tools.LoadScript(scriptUrl, onSuccess, onError);\r\n    }\r\n\r\n    /**\r\n     * Load an asynchronous script (identified by an url). When the url returns, the\r\n     * content of this file is added into a new script element, attached to the DOM (body element)\r\n     * @param scriptUrl defines the url of the script to laod\r\n     * @returns a promise request object\r\n     */\r\n    public static LoadBabylonScriptAsync(scriptUrl: string): Promise<void> {\r\n        scriptUrl = Tools.GetBabylonScriptURL(scriptUrl);\r\n        return Tools.LoadScriptAsync(scriptUrl);\r\n    }\r\n\r\n    /**\r\n     * This function is used internally by babylon components to load a script (identified by an url). When the url returns, the\r\n     * content of this file is added into a new script element, attached to the DOM (body element)\r\n     * @param scriptUrl defines the url of the script to load\r\n     * @param onSuccess defines the callback called when the script is loaded\r\n     * @param onError defines the callback to call if an error occurs\r\n     * @param scriptId defines the id of the script element\r\n     */\r\n    public static LoadScript(scriptUrl: string, onSuccess: () => void, onError?: (message?: string, exception?: any) => void, scriptId?: string) {\r\n        if (typeof importScripts === \"function\") {\r\n            try {\r\n                importScripts(scriptUrl);\r\n                onSuccess();\r\n            } catch (e) {\r\n                onError?.(`Unable to load script '${scriptUrl}' in worker`, e);\r\n            }\r\n            return;\r\n        } else if (!IsWindowObjectExist()) {\r\n            onError?.(`Cannot load script '${scriptUrl}' outside of a window or a worker`);\r\n            return;\r\n        }\r\n        const head = document.getElementsByTagName(\"head\")[0];\r\n        const script = document.createElement(\"script\");\r\n        script.setAttribute(\"type\", \"text/javascript\");\r\n        script.setAttribute(\"src\", scriptUrl);\r\n        if (scriptId) {\r\n            script.id = scriptId;\r\n        }\r\n\r\n        script.onload = () => {\r\n            if (onSuccess) {\r\n                onSuccess();\r\n            }\r\n        };\r\n\r\n        script.onerror = (e) => {\r\n            if (onError) {\r\n                onError(`Unable to load script '${scriptUrl}'`, e);\r\n            }\r\n        };\r\n\r\n        head.appendChild(script);\r\n    }\r\n\r\n    /**\r\n     * Load an asynchronous script (identified by an url). When the url returns, the\r\n     * content of this file is added into a new script element, attached to the DOM (body element)\r\n     * @param scriptUrl defines the url of the script to load\r\n     * @param scriptId defines the id of the script element\r\n     * @returns a promise request object\r\n     */\r\n    public static LoadScriptAsync(scriptUrl: string, scriptId?: string): Promise<void> {\r\n        return new Promise((resolve, reject) => {\r\n            this.LoadScript(\r\n                scriptUrl,\r\n                () => {\r\n                    resolve();\r\n                },\r\n                (message, exception) => {\r\n                    reject(exception || new Error(message));\r\n                },\r\n                scriptId\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Loads a file from a blob\r\n     * @param fileToLoad defines the blob to use\r\n     * @param callback defines the callback to call when data is loaded\r\n     * @param progressCallback defines the callback to call during loading process\r\n     * @returns a file request object\r\n     */\r\n    public static ReadFileAsDataURL(fileToLoad: Blob, callback: (data: any) => void, progressCallback: (ev: ProgressEvent) => any): IFileRequest {\r\n        const reader = new FileReader();\r\n\r\n        const request: IFileRequest = {\r\n            onCompleteObservable: new Observable<IFileRequest>(),\r\n            abort: () => reader.abort(),\r\n        };\r\n\r\n        reader.onloadend = () => {\r\n            request.onCompleteObservable.notifyObservers(request);\r\n        };\r\n\r\n        reader.onload = (e) => {\r\n            //target doesn't have result from ts 1.3\r\n            callback((<any>e.target)[\"result\"]);\r\n        };\r\n\r\n        reader.onprogress = progressCallback;\r\n\r\n        reader.readAsDataURL(fileToLoad);\r\n\r\n        return request;\r\n    }\r\n\r\n    /**\r\n     * Reads a file from a File object\r\n     * @param file defines the file to load\r\n     * @param onSuccess defines the callback to call when data is loaded\r\n     * @param onProgress defines the callback to call during loading process\r\n     * @param useArrayBuffer defines a boolean indicating that data must be returned as an ArrayBuffer\r\n     * @param onError defines the callback to call when an error occurs\r\n     * @returns a file request object\r\n     */\r\n    public static ReadFile(\r\n        file: File,\r\n        onSuccess: (data: any) => void,\r\n        onProgress?: (ev: ProgressEvent) => any,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (error: ReadFileError) => void\r\n    ): IFileRequest {\r\n        return FileToolsReadFile(file, onSuccess, onProgress, useArrayBuffer, onError);\r\n    }\r\n\r\n    /**\r\n     * Creates a data url from a given string content\r\n     * @param content defines the content to convert\r\n     * @returns the new data url link\r\n     */\r\n    public static FileAsURL(content: string): string {\r\n        const fileBlob = new Blob([content]);\r\n        const url = window.URL;\r\n        const link: string = url.createObjectURL(fileBlob);\r\n        return link;\r\n    }\r\n\r\n    /**\r\n     * Format the given number to a specific decimal format\r\n     * @param value defines the number to format\r\n     * @param decimals defines the number of decimals to use\r\n     * @returns the formatted string\r\n     */\r\n    public static Format(value: number, decimals = 2): string {\r\n        return value.toFixed(decimals);\r\n    }\r\n\r\n    /**\r\n     * Tries to copy an object by duplicating every property\r\n     * @param source defines the source object\r\n     * @param destination defines the target object\r\n     * @param doNotCopyList defines a list of properties to avoid\r\n     * @param mustCopyList defines a list of properties to copy (even if they start with _)\r\n     */\r\n    public static DeepCopy(source: any, destination: any, doNotCopyList?: string[], mustCopyList?: string[]): void {\r\n        DeepCopier.DeepCopy(source, destination, doNotCopyList, mustCopyList);\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the given object has no own property\r\n     * @param obj defines the object to test\r\n     * @returns true if object has no own property\r\n     */\r\n    public static IsEmpty(obj: any): boolean {\r\n        for (const i in obj) {\r\n            if (Object.prototype.hasOwnProperty.call(obj, i)) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Function used to register events at window level\r\n     * @param windowElement defines the Window object to use\r\n     * @param events defines the events to register\r\n     */\r\n    public static RegisterTopRootEvents(windowElement: Window, events: { name: string; handler: Nullable<(e: FocusEvent) => any> }[]): void {\r\n        for (let index = 0; index < events.length; index++) {\r\n            const event = events[index];\r\n            windowElement.addEventListener(event.name, <any>event.handler, false);\r\n\r\n            try {\r\n                if (window.parent) {\r\n                    window.parent.addEventListener(event.name, <any>event.handler, false);\r\n                }\r\n            } catch (e) {\r\n                // Silently fails...\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Function used to unregister events from window level\r\n     * @param windowElement defines the Window object to use\r\n     * @param events defines the events to unregister\r\n     */\r\n    public static UnregisterTopRootEvents(windowElement: Window, events: { name: string; handler: Nullable<(e: FocusEvent) => any> }[]): void {\r\n        for (let index = 0; index < events.length; index++) {\r\n            const event = events[index];\r\n            windowElement.removeEventListener(event.name, <any>event.handler);\r\n\r\n            try {\r\n                if (windowElement.parent) {\r\n                    windowElement.parent.removeEventListener(event.name, <any>event.handler);\r\n                }\r\n            } catch (e) {\r\n                // Silently fails...\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Dumps the current bound framebuffer\r\n     * @param width defines the rendering width\r\n     * @param height defines the rendering height\r\n     * @param engine defines the hosting engine\r\n     * @param successCallback defines the callback triggered once the data are available\r\n     * @param mimeType defines the mime type of the result\r\n     * @param fileName defines the filename to download. If present, the result will automatically be downloaded\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     * @returns a void promise\r\n     */\r\n    public static async DumpFramebuffer(\r\n        width: number,\r\n        height: number,\r\n        engine: Engine,\r\n        successCallback?: (data: string) => void,\r\n        mimeType = \"image/png\",\r\n        fileName?: string,\r\n        quality?: number\r\n    ) {\r\n        throw _WarnImport(\"DumpTools\");\r\n    }\r\n\r\n    /**\r\n     * Dumps an array buffer\r\n     * @param width defines the rendering width\r\n     * @param height defines the rendering height\r\n     * @param data the data array\r\n     * @param successCallback defines the callback triggered once the data are available\r\n     * @param mimeType defines the mime type of the result\r\n     * @param fileName defines the filename to download. If present, the result will automatically be downloaded\r\n     * @param invertY true to invert the picture in the Y dimension\r\n     * @param toArrayBuffer true to convert the data to an ArrayBuffer (encoded as `mimeType`) instead of a base64 string\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     */\r\n    public static DumpData(\r\n        width: number,\r\n        height: number,\r\n        data: ArrayBufferView,\r\n        successCallback?: (data: string | ArrayBuffer) => void,\r\n        mimeType = \"image/png\",\r\n        fileName?: string,\r\n        invertY = false,\r\n        toArrayBuffer = false,\r\n        quality?: number\r\n    ) {\r\n        throw _WarnImport(\"DumpTools\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Dumps an array buffer\r\n     * @param width defines the rendering width\r\n     * @param height defines the rendering height\r\n     * @param data the data array\r\n     * @param mimeType defines the mime type of the result\r\n     * @param fileName defines the filename to download. If present, the result will automatically be downloaded\r\n     * @param invertY true to invert the picture in the Y dimension\r\n     * @param toArrayBuffer true to convert the data to an ArrayBuffer (encoded as `mimeType`) instead of a base64 string\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     * @returns a promise that resolve to the final data\r\n     */\r\n    public static DumpDataAsync(\r\n        width: number,\r\n        height: number,\r\n        data: ArrayBufferView,\r\n        mimeType = \"image/png\",\r\n        fileName?: string,\r\n        invertY = false,\r\n        toArrayBuffer = false,\r\n        quality?: number\r\n    ): Promise<string | ArrayBuffer> {\r\n        throw _WarnImport(\"DumpTools\");\r\n    }\r\n\r\n    private static _IsOffScreenCanvas(canvas: HTMLCanvasElement | OffscreenCanvas): canvas is OffscreenCanvas {\r\n        return (canvas as OffscreenCanvas).convertToBlob !== undefined;\r\n    }\r\n\r\n    /**\r\n     * Converts the canvas data to blob.\r\n     * This acts as a polyfill for browsers not supporting the to blob function.\r\n     * @param canvas Defines the canvas to extract the data from (can be an offscreen canvas)\r\n     * @param successCallback Defines the callback triggered once the data are available\r\n     * @param mimeType Defines the mime type of the result\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     */\r\n    static ToBlob(canvas: HTMLCanvasElement | OffscreenCanvas, successCallback: (blob: Nullable<Blob>) => void, mimeType = \"image/png\", quality?: number): void {\r\n        // We need HTMLCanvasElement.toBlob for HD screenshots\r\n        if (!Tools._IsOffScreenCanvas(canvas) && !canvas.toBlob) {\r\n            //  low performance polyfill based on toDataURL (https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob)\r\n            canvas.toBlob = function (callback, type, quality) {\r\n                setTimeout(() => {\r\n                    const binStr = atob(this.toDataURL(type, quality).split(\",\")[1]),\r\n                        len = binStr.length,\r\n                        arr = new Uint8Array(len);\r\n\r\n                    for (let i = 0; i < len; i++) {\r\n                        arr[i] = binStr.charCodeAt(i);\r\n                    }\r\n                    callback(new Blob([arr]));\r\n                });\r\n            };\r\n        }\r\n        if (Tools._IsOffScreenCanvas(canvas)) {\r\n            canvas\r\n                .convertToBlob({\r\n                    type: mimeType,\r\n                    quality,\r\n                })\r\n                .then((blob) => successCallback(blob));\r\n        } else {\r\n            canvas.toBlob(\r\n                function (blob) {\r\n                    successCallback(blob);\r\n                },\r\n                mimeType,\r\n                quality\r\n            );\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Download a Blob object\r\n     * @param blob the Blob object\r\n     * @param fileName the file name to download\r\n     */\r\n    static DownloadBlob(blob: Blob, fileName?: string) {\r\n        //Creating a link if the browser have the download attribute on the a tag, to automatically start download generated image.\r\n        if (\"download\" in document.createElement(\"a\")) {\r\n            if (!fileName) {\r\n                const date = new Date();\r\n                const stringDate =\r\n                    (date.getFullYear() + \"-\" + (date.getMonth() + 1)).slice(2) + \"-\" + date.getDate() + \"_\" + date.getHours() + \"-\" + (\"0\" + date.getMinutes()).slice(-2);\r\n                fileName = \"screenshot_\" + stringDate + \".png\";\r\n            }\r\n            Tools.Download(blob, fileName);\r\n        } else {\r\n            if (blob && typeof URL !== \"undefined\") {\r\n                const url = URL.createObjectURL(blob);\r\n\r\n                const newWindow = window.open(\"\");\r\n                if (!newWindow) {\r\n                    return;\r\n                }\r\n                const img = newWindow.document.createElement(\"img\");\r\n                img.onload = function () {\r\n                    // no longer need to read the blob so it's revoked\r\n                    URL.revokeObjectURL(url);\r\n                };\r\n                img.src = url;\r\n                newWindow.document.body.appendChild(img);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Encodes the canvas data to base 64, or automatically downloads the result if `fileName` is defined.\r\n     * @param canvas The canvas to get the data from, which can be an offscreen canvas.\r\n     * @param successCallback The callback which is triggered once the data is available. If `fileName` is defined, the callback will be invoked after the download occurs, and the `data` argument will be an empty string.\r\n     * @param mimeType The mime type of the result.\r\n     * @param fileName The name of the file to download. If defined, the result will automatically be downloaded. If not defined, and `successCallback` is also not defined, the result will automatically be downloaded with an auto-generated file name.\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     */\r\n    static EncodeScreenshotCanvasData(\r\n        canvas: HTMLCanvasElement | OffscreenCanvas,\r\n        successCallback?: (data: string) => void,\r\n        mimeType = \"image/png\",\r\n        fileName?: string,\r\n        quality?: number\r\n    ): void {\r\n        if (typeof fileName === \"string\" || !successCallback) {\r\n            this.ToBlob(\r\n                canvas,\r\n                function (blob) {\r\n                    if (blob) {\r\n                        Tools.DownloadBlob(blob, fileName);\r\n                    }\r\n                    if (successCallback) {\r\n                        successCallback(\"\");\r\n                    }\r\n                },\r\n                mimeType,\r\n                quality\r\n            );\r\n        } else if (successCallback) {\r\n            if (Tools._IsOffScreenCanvas(canvas)) {\r\n                canvas\r\n                    .convertToBlob({\r\n                        type: mimeType,\r\n                        quality,\r\n                    })\r\n                    .then((blob) => {\r\n                        const reader = new FileReader();\r\n                        reader.readAsDataURL(blob);\r\n                        reader.onloadend = () => {\r\n                            const base64data = reader.result;\r\n                            successCallback(base64data as string);\r\n                        };\r\n                    });\r\n                return;\r\n            }\r\n            const base64Image = canvas.toDataURL(mimeType, quality);\r\n            successCallback(base64Image);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Downloads a blob in the browser\r\n     * @param blob defines the blob to download\r\n     * @param fileName defines the name of the downloaded file\r\n     */\r\n    public static Download(blob: Blob, fileName: string): void {\r\n        if (typeof URL === \"undefined\") {\r\n            return;\r\n        }\r\n\r\n        const url = window.URL.createObjectURL(blob);\r\n        const a = document.createElement(\"a\");\r\n        document.body.appendChild(a);\r\n        a.style.display = \"none\";\r\n        a.href = url;\r\n        a.download = fileName;\r\n        a.addEventListener(\"click\", () => {\r\n            if (a.parentElement) {\r\n                a.parentElement.removeChild(a);\r\n            }\r\n        });\r\n        a.click();\r\n        window.URL.revokeObjectURL(url);\r\n    }\r\n\r\n    /**\r\n     * Will return the right value of the noPreventDefault variable\r\n     * Needed to keep backwards compatibility to the old API.\r\n     *\r\n     * @param args arguments passed to the attachControl function\r\n     * @returns the correct value for noPreventDefault\r\n     */\r\n    public static BackCompatCameraNoPreventDefault(args: IArguments): boolean {\r\n        // is it used correctly?\r\n        if (typeof args[0] === \"boolean\") {\r\n            return args[0];\r\n        } else if (typeof args[1] === \"boolean\") {\r\n            return args[1];\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Captures a screenshot of the current rendering\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n     * @param engine defines the rendering engine\r\n     * @param camera defines the source camera\r\n     * @param size This parameter can be set to a single number or to an object with the\r\n     * following (optional) properties: precision, width, height. If a single number is passed,\r\n     * it will be used for both width and height. If an object is passed, the screenshot size\r\n     * will be derived from the parameters. The precision property is a multiplier allowing\r\n     * rendering at a higher or lower resolution\r\n     * @param successCallback defines the callback receives a single parameter which contains the\r\n     * screenshot as a string of base64-encoded characters. This string can be assigned to the\r\n     * src parameter of an <img> to display it\r\n     * @param mimeType defines the MIME type of the screenshot image (default: image/png).\r\n     * Check your browser for supported MIME types\r\n     * @param forceDownload force the system to download the image even if a successCallback is provided\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static CreateScreenshot(\r\n        engine: Engine,\r\n        camera: Camera,\r\n        size: IScreenshotSize | number,\r\n        successCallback?: (data: string) => void,\r\n        mimeType = \"image/png\",\r\n        forceDownload = false,\r\n        quality?: number\r\n    ): void {\r\n        throw _WarnImport(\"ScreenshotTools\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Captures a screenshot of the current rendering\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n     * @param engine defines the rendering engine\r\n     * @param camera defines the source camera\r\n     * @param size This parameter can be set to a single number or to an object with the\r\n     * following (optional) properties: precision, width, height. If a single number is passed,\r\n     * it will be used for both width and height. If an object is passed, the screenshot size\r\n     * will be derived from the parameters. The precision property is a multiplier allowing\r\n     * rendering at a higher or lower resolution\r\n     * @param mimeType defines the MIME type of the screenshot image (default: image/png).\r\n     * Check your browser for supported MIME types\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     * @returns screenshot as a string of base64-encoded characters. This string can be assigned\r\n     * to the src parameter of an <img> to display it\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static CreateScreenshotAsync(engine: Engine, camera: Camera, size: IScreenshotSize | number, mimeType = \"image/png\", quality?: number): Promise<string> {\r\n        throw _WarnImport(\"ScreenshotTools\");\r\n    }\r\n\r\n    /**\r\n     * Generates an image screenshot from the specified camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n     * @param engine The engine to use for rendering\r\n     * @param camera The camera to use for rendering\r\n     * @param size This parameter can be set to a single number or to an object with the\r\n     * following (optional) properties: precision, width, height. If a single number is passed,\r\n     * it will be used for both width and height. If an object is passed, the screenshot size\r\n     * will be derived from the parameters. The precision property is a multiplier allowing\r\n     * rendering at a higher or lower resolution\r\n     * @param successCallback The callback receives a single parameter which contains the\r\n     * screenshot as a string of base64-encoded characters. This string can be assigned to the\r\n     * src parameter of an <img> to display it\r\n     * @param mimeType The MIME type of the screenshot image (default: image/png).\r\n     * Check your browser for supported MIME types\r\n     * @param samples Texture samples (default: 1)\r\n     * @param antialiasing Whether antialiasing should be turned on or not (default: false)\r\n     * @param fileName A name for for the downloaded file.\r\n     * @param renderSprites Whether the sprites should be rendered or not (default: false)\r\n     * @param enableStencilBuffer Whether the stencil buffer should be enabled or not (default: false)\r\n     * @param useLayerMask if the camera's layer mask should be used to filter what should be rendered (default: true)\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static CreateScreenshotUsingRenderTarget(\r\n        engine: Engine,\r\n        camera: Camera,\r\n        size: IScreenshotSize | number,\r\n        successCallback?: (data: string) => void,\r\n        mimeType = \"image/png\",\r\n        samples = 1,\r\n        antialiasing = false,\r\n        fileName?: string,\r\n        renderSprites = false,\r\n        enableStencilBuffer = false,\r\n        useLayerMask = true,\r\n        quality?: number\r\n    ): void {\r\n        throw _WarnImport(\"ScreenshotTools\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Generates an image screenshot from the specified camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/renderToPNG\r\n     * @param engine The engine to use for rendering\r\n     * @param camera The camera to use for rendering\r\n     * @param size This parameter can be set to a single number or to an object with the\r\n     * following (optional) properties: precision, width, height. If a single number is passed,\r\n     * it will be used for both width and height. If an object is passed, the screenshot size\r\n     * will be derived from the parameters. The precision property is a multiplier allowing\r\n     * rendering at a higher or lower resolution\r\n     * @param mimeType The MIME type of the screenshot image (default: image/png).\r\n     * Check your browser for supported MIME types\r\n     * @param samples Texture samples (default: 1)\r\n     * @param antialiasing Whether antialiasing should be turned on or not (default: false)\r\n     * @param fileName A name for for the downloaded file.\r\n     * @returns screenshot as a string of base64-encoded characters. This string can be assigned\r\n     * @param renderSprites Whether the sprites should be rendered or not (default: false)\r\n     * @param enableStencilBuffer Whether the stencil buffer should be enabled or not (default: false)\r\n     * @param useLayerMask if the camera's layer mask should be used to filter what should be rendered (default: true)\r\n     * @param quality The quality of the image if lossy mimeType is used (e.g. image/jpeg, image/webp). See {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toBlob | HTMLCanvasElement.toBlob()}'s `quality` parameter.\r\n     * to the src parameter of an <img> to display it\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public static CreateScreenshotUsingRenderTargetAsync(\r\n        engine: Engine,\r\n        camera: Camera,\r\n        size: IScreenshotSize | number,\r\n        mimeType = \"image/png\",\r\n        samples = 1,\r\n        antialiasing = false,\r\n        fileName?: string,\r\n        renderSprites = false,\r\n        enableStencilBuffer = false,\r\n        useLayerMask = true,\r\n        quality?: number\r\n    ): Promise<string> {\r\n        throw _WarnImport(\"ScreenshotTools\");\r\n    }\r\n\r\n    /**\r\n     * Implementation from http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/2117523#answer-2117523\r\n     * Be aware Math.random() could cause collisions, but:\r\n     * \"All but 6 of the 128 bits of the ID are randomly generated, which means that for any two ids, there's a 1 in 2^^122 (or 5.3x10^^36) chance they'll collide\"\r\n     * @returns a pseudo random id\r\n     */\r\n    public static RandomId(): string {\r\n        return RandomGUID();\r\n    }\r\n\r\n    /**\r\n     * Test if the given uri is a base64 string\r\n     * @deprecated Please use FileTools.IsBase64DataUrl instead.\r\n     * @param uri The uri to test\r\n     * @returns True if the uri is a base64 string or false otherwise\r\n     */\r\n    public static IsBase64(uri: string): boolean {\r\n        return IsBase64DataUrl(uri);\r\n    }\r\n\r\n    /**\r\n     * Decode the given base64 uri.\r\n     * @deprecated Please use FileTools.DecodeBase64UrlToBinary instead.\r\n     * @param uri The uri to decode\r\n     * @returns The decoded base64 data.\r\n     */\r\n    public static DecodeBase64(uri: string): ArrayBuffer {\r\n        return DecodeBase64UrlToBinary(uri);\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check, jsdoc/require-param\r\n    /**\r\n     * @returns the absolute URL of a given (relative) url\r\n     */\r\n    public static GetAbsoluteUrl: (url: string) => string =\r\n        typeof document === \"object\"\r\n            ? (url) => {\r\n                  const a = document.createElement(\"a\");\r\n                  a.href = url;\r\n                  return a.href;\r\n              }\r\n            : typeof URL === \"function\" && typeof location === \"object\"\r\n              ? (url) => new URL(url, location.origin).href\r\n              : () => {\r\n                    throw new Error(\"Unable to get absolute URL. Override BABYLON.Tools.GetAbsoluteUrl to a custom implementation for the current context.\");\r\n                };\r\n\r\n    // Logs\r\n    /**\r\n     * No log\r\n     */\r\n    public static readonly NoneLogLevel = Logger.NoneLogLevel;\r\n    /**\r\n     * Only message logs\r\n     */\r\n    public static readonly MessageLogLevel = Logger.MessageLogLevel;\r\n    /**\r\n     * Only warning logs\r\n     */\r\n    public static readonly WarningLogLevel = Logger.WarningLogLevel;\r\n    /**\r\n     * Only error logs\r\n     */\r\n    public static readonly ErrorLogLevel = Logger.ErrorLogLevel;\r\n    /**\r\n     * All logs\r\n     */\r\n    public static readonly AllLogLevel = Logger.AllLogLevel;\r\n\r\n    /**\r\n     * Gets a value indicating the number of loading errors\r\n     * @ignorenaming\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static get errorsCount(): number {\r\n        return Logger.errorsCount;\r\n    }\r\n\r\n    /**\r\n     * Callback called when a new log is added\r\n     */\r\n    public static OnNewCacheEntry: (entry: string) => void;\r\n\r\n    /**\r\n     * Log a message to the console\r\n     * @param message defines the message to log\r\n     */\r\n    public static Log(message: string): void {\r\n        Logger.Log(message);\r\n    }\r\n\r\n    /**\r\n     * Write a warning message to the console\r\n     * @param message defines the message to log\r\n     */\r\n    public static Warn(message: string): void {\r\n        Logger.Warn(message);\r\n    }\r\n\r\n    /**\r\n     * Write an error message to the console\r\n     * @param message defines the message to log\r\n     */\r\n    public static Error(message: string): void {\r\n        Logger.Error(message);\r\n    }\r\n\r\n    /**\r\n     * Gets current log cache (list of logs)\r\n     */\r\n    public static get LogCache(): string {\r\n        return Logger.LogCache;\r\n    }\r\n\r\n    /**\r\n     * Clears the log cache\r\n     */\r\n    public static ClearLogCache(): void {\r\n        Logger.ClearLogCache();\r\n    }\r\n\r\n    /**\r\n     * Sets the current log level (MessageLogLevel / WarningLogLevel / ErrorLogLevel)\r\n     */\r\n    public static set LogLevels(level: number) {\r\n        Logger.LogLevels = level;\r\n    }\r\n\r\n    /**\r\n     * Checks if the window object exists\r\n     * Back Compat only, please use IsWindowObjectExist instead.\r\n     */\r\n    public static IsWindowObjectExist = IsWindowObjectExist;\r\n\r\n    // Performances\r\n\r\n    /**\r\n     * No performance log\r\n     */\r\n    public static readonly PerformanceNoneLogLevel = 0;\r\n    /**\r\n     * Use user marks to log performance\r\n     */\r\n    public static readonly PerformanceUserMarkLogLevel = 1;\r\n    /**\r\n     * Log performance to the console\r\n     */\r\n    public static readonly PerformanceConsoleLogLevel = 2;\r\n\r\n    private static _Performance: Performance;\r\n\r\n    /**\r\n     * Sets the current performance log level\r\n     */\r\n    public static set PerformanceLogLevel(level: number) {\r\n        if ((level & Tools.PerformanceUserMarkLogLevel) === Tools.PerformanceUserMarkLogLevel) {\r\n            Tools.StartPerformanceCounter = Tools._StartUserMark;\r\n            Tools.EndPerformanceCounter = Tools._EndUserMark;\r\n            return;\r\n        }\r\n\r\n        if ((level & Tools.PerformanceConsoleLogLevel) === Tools.PerformanceConsoleLogLevel) {\r\n            Tools.StartPerformanceCounter = Tools._StartPerformanceConsole;\r\n            Tools.EndPerformanceCounter = Tools._EndPerformanceConsole;\r\n            return;\r\n        }\r\n\r\n        Tools.StartPerformanceCounter = Tools._StartPerformanceCounterDisabled;\r\n        Tools.EndPerformanceCounter = Tools._EndPerformanceCounterDisabled;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private static _StartPerformanceCounterDisabled(counterName: string, condition?: boolean): void {}\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private static _EndPerformanceCounterDisabled(counterName: string, condition?: boolean): void {}\r\n\r\n    private static _StartUserMark(counterName: string, condition = true): void {\r\n        if (!Tools._Performance) {\r\n            if (!IsWindowObjectExist()) {\r\n                return;\r\n            }\r\n            Tools._Performance = window.performance;\r\n        }\r\n\r\n        if (!condition || !Tools._Performance.mark) {\r\n            return;\r\n        }\r\n        Tools._Performance.mark(counterName + \"-Begin\");\r\n    }\r\n\r\n    private static _EndUserMark(counterName: string, condition = true): void {\r\n        if (!condition || !Tools._Performance.mark) {\r\n            return;\r\n        }\r\n        Tools._Performance.mark(counterName + \"-End\");\r\n        Tools._Performance.measure(counterName, counterName + \"-Begin\", counterName + \"-End\");\r\n    }\r\n\r\n    private static _StartPerformanceConsole(counterName: string, condition = true): void {\r\n        if (!condition) {\r\n            return;\r\n        }\r\n\r\n        Tools._StartUserMark(counterName, condition);\r\n\r\n        if (console.time) {\r\n            console.time(counterName);\r\n        }\r\n    }\r\n\r\n    private static _EndPerformanceConsole(counterName: string, condition = true): void {\r\n        if (!condition) {\r\n            return;\r\n        }\r\n\r\n        Tools._EndUserMark(counterName, condition);\r\n\r\n        console.timeEnd(counterName);\r\n    }\r\n\r\n    /**\r\n     * Starts a performance counter\r\n     */\r\n    public static StartPerformanceCounter: (counterName: string, condition?: boolean) => void = Tools._StartPerformanceCounterDisabled;\r\n\r\n    /**\r\n     * Ends a specific performance counter\r\n     */\r\n    public static EndPerformanceCounter: (counterName: string, condition?: boolean) => void = Tools._EndPerformanceCounterDisabled;\r\n\r\n    /**\r\n     * Gets either window.performance.now() if supported or Date.now() else\r\n     */\r\n    public static get Now(): number {\r\n        return PrecisionDate.Now;\r\n    }\r\n\r\n    /**\r\n     * This method will return the name of the class used to create the instance of the given object.\r\n     * It will works only on Javascript basic data types (number, string, ...) and instance of class declared with the @className decorator.\r\n     * @param object the object to get the class name from\r\n     * @param isType defines if the object is actually a type\r\n     * @returns the name of the class, will be \"object\" for a custom data type not using the @className decorator\r\n     */\r\n    public static GetClassName(object: any, isType = false): string {\r\n        let name = null;\r\n\r\n        if (!isType && object.getClassName) {\r\n            name = object.getClassName();\r\n        } else {\r\n            if (object instanceof Object) {\r\n                const classObj = isType ? object : Object.getPrototypeOf(object);\r\n                name = classObj.constructor[\"__bjsclassName__\"];\r\n            }\r\n            if (!name) {\r\n                name = typeof object;\r\n            }\r\n        }\r\n        return name;\r\n    }\r\n\r\n    /**\r\n     * Gets the first element of an array satisfying a given predicate\r\n     * @param array defines the array to browse\r\n     * @param predicate defines the predicate to use\r\n     * @returns null if not found or the element\r\n     */\r\n    public static First<T>(array: Array<T>, predicate: (item: T) => boolean): Nullable<T> {\r\n        for (const el of array) {\r\n            if (predicate(el)) {\r\n                return el;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * This method will return the name of the full name of the class, including its owning module (if any).\r\n     * It will works only on Javascript basic data types (number, string, ...) and instance of class declared with the @className decorator or implementing a method getClassName():string (in which case the module won't be specified).\r\n     * @param object the object to get the class name from\r\n     * @param isType defines if the object is actually a type\r\n     * @returns a string that can have two forms: \"moduleName.className\" if module was specified when the class' Name was registered or \"className\" if there was not module specified.\r\n     * @ignorenaming\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static getFullClassName(object: any, isType = false): Nullable<string> {\r\n        let className = null;\r\n        let moduleName = null;\r\n\r\n        if (!isType && object.getClassName) {\r\n            className = object.getClassName();\r\n        } else {\r\n            if (object instanceof Object) {\r\n                const classObj = isType ? object : Object.getPrototypeOf(object);\r\n                className = classObj.constructor[\"__bjsclassName__\"];\r\n                moduleName = classObj.constructor[\"__bjsmoduleName__\"];\r\n            }\r\n            if (!className) {\r\n                className = typeof object;\r\n            }\r\n        }\r\n\r\n        if (!className) {\r\n            return null;\r\n        }\r\n\r\n        return (moduleName != null ? moduleName + \".\" : \"\") + className;\r\n    }\r\n\r\n    /**\r\n     * Returns a promise that resolves after the given amount of time.\r\n     * @param delay Number of milliseconds to delay\r\n     * @returns Promise that resolves after the given amount of time\r\n     */\r\n    public static DelayAsync(delay: number): Promise<void> {\r\n        return new Promise((resolve) => {\r\n            setTimeout(() => {\r\n                resolve();\r\n            }, delay);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Utility function to detect if the current user agent is Safari\r\n     * @returns whether or not the current user agent is safari\r\n     */\r\n    public static IsSafari(): boolean {\r\n        if (!IsNavigatorAvailable()) {\r\n            return false;\r\n        }\r\n\r\n        return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\r\n    }\r\n}\r\n\r\n/**\r\n * Use this className as a decorator on a given class definition to add it a name and optionally its module.\r\n * You can then use the Tools.getClassName(obj) on an instance to retrieve its class name.\r\n * This method is the only way to get it done in all cases, even if the .js file declaring the class is minified\r\n * @param name The name of the class, case should be preserved\r\n * @param module The name of the Module hosting the class, optional, but strongly recommended to specify if possible. Case should be preserved.\r\n * @returns a decorator function to apply on the class definition.\r\n */\r\nexport function className(name: string, module?: string): (target: Object) => void {\r\n    return (target: Object) => {\r\n        (<any>target)[\"__bjsclassName__\"] = name;\r\n        (<any>target)[\"__bjsmoduleName__\"] = module != null ? module : null;\r\n    };\r\n}\r\n\r\n/**\r\n * An implementation of a loop for asynchronous functions.\r\n */\r\nexport class AsyncLoop {\r\n    /**\r\n     * Defines the current index of the loop.\r\n     */\r\n    public index: number;\r\n    private _done: boolean;\r\n    private _fn: (asyncLoop: AsyncLoop) => void;\r\n    private _successCallback: () => void;\r\n\r\n    /**\r\n     * Constructor.\r\n     * @param iterations the number of iterations.\r\n     * @param func the function to run each iteration\r\n     * @param successCallback the callback that will be called upon successful execution\r\n     * @param offset starting offset.\r\n     */\r\n    constructor(\r\n        /**\r\n         * Defines the number of iterations for the loop\r\n         */\r\n        public iterations: number,\r\n        func: (asyncLoop: AsyncLoop) => void,\r\n        successCallback: () => void,\r\n        offset = 0\r\n    ) {\r\n        this.index = offset - 1;\r\n        this._done = false;\r\n        this._fn = func;\r\n        this._successCallback = successCallback;\r\n    }\r\n\r\n    /**\r\n     * Execute the next iteration. Must be called after the last iteration was finished.\r\n     */\r\n    public executeNext(): void {\r\n        if (!this._done) {\r\n            if (this.index + 1 < this.iterations) {\r\n                ++this.index;\r\n                this._fn(this);\r\n            } else {\r\n                this.breakLoop();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Break the loop and run the success callback.\r\n     */\r\n    public breakLoop(): void {\r\n        this._done = true;\r\n        this._successCallback();\r\n    }\r\n\r\n    /**\r\n     * Create and run an async loop.\r\n     * @param iterations the number of iterations.\r\n     * @param fn the function to run each iteration\r\n     * @param successCallback the callback that will be called upon successful execution\r\n     * @param offset starting offset.\r\n     * @returns the created async loop object\r\n     */\r\n    public static Run(iterations: number, fn: (asyncLoop: AsyncLoop) => void, successCallback: () => void, offset = 0): AsyncLoop {\r\n        const loop = new AsyncLoop(iterations, fn, successCallback, offset);\r\n\r\n        loop.executeNext();\r\n\r\n        return loop;\r\n    }\r\n\r\n    /**\r\n     * A for-loop that will run a given number of iterations synchronous and the rest async.\r\n     * @param iterations total number of iterations\r\n     * @param syncedIterations number of synchronous iterations in each async iteration.\r\n     * @param fn the function to call each iteration.\r\n     * @param callback a success call back that will be called when iterating stops.\r\n     * @param breakFunction a break condition (optional)\r\n     * @param timeout timeout settings for the setTimeout function. default - 0.\r\n     * @returns the created async loop object\r\n     */\r\n    public static SyncAsyncForLoop(\r\n        iterations: number,\r\n        syncedIterations: number,\r\n        fn: (iteration: number) => void,\r\n        callback: () => void,\r\n        breakFunction?: () => boolean,\r\n        timeout = 0\r\n    ): AsyncLoop {\r\n        return AsyncLoop.Run(\r\n            Math.ceil(iterations / syncedIterations),\r\n            (loop: AsyncLoop) => {\r\n                if (breakFunction && breakFunction()) {\r\n                    loop.breakLoop();\r\n                } else {\r\n                    setTimeout(() => {\r\n                        for (let i = 0; i < syncedIterations; ++i) {\r\n                            const iteration = loop.index * syncedIterations + i;\r\n                            if (iteration >= iterations) {\r\n                                break;\r\n                            }\r\n                            fn(iteration);\r\n                            if (breakFunction && breakFunction()) {\r\n                                loop.breakLoop();\r\n                                break;\r\n                            }\r\n                        }\r\n                        loop.executeNext();\r\n                    }, timeout);\r\n                }\r\n            },\r\n            callback\r\n        );\r\n    }\r\n}\r\n\r\nTools.Mix = Mix;\r\nTools.IsExponentOfTwo = IsExponentOfTwo;\r\n\r\n// Will only be define if Tools is imported freeing up some space when only engine is required\r\nEngineStore.FallbackTexture =\r\n    \"data:image/jpg;base64,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\";\r\n"]}