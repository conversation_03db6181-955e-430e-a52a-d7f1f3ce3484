{"version": 3, "file": "postprocess.vertex.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/ShadersWGSL/postprocess.vertex.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD,MAAM,IAAI,GAAG,yBAAyB,CAAC;AACvC,MAAM,MAAM,GAAG;;;;;;;;CAQd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAC5C,gBAAgB;AAChB,MAAM,CAAC,MAAM,uBAAuB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore\";\n\nconst name = \"postprocessVertexShader\";\nconst shader = `attribute position: vec2<f32>;uniform scale: vec2<f32>;varying vUV: vec2<f32>;const madd=vec2(0.5,0.5);\n#define CUSTOM_VERTEX_DEFINITIONS\n@vertex\nfn main(input : VertexInputs)->FragmentInputs {\n#define CUSTOM_VERTEX_MAIN_BEGIN\nvertexOutputs.vUV=(vertexInputs.position*madd+madd)*uniforms.scale;vertexOutputs.position=vec4(vertexInputs.position,0.0,1.0);\n#define CUSTOM_VERTEX_MAIN_END\n}\n`;\n// Sideeffect\nShaderStore.ShadersStoreWGSL[name] = shader;\n/** @internal */\nexport const postprocessVertexShader = { name, shader };\n"]}