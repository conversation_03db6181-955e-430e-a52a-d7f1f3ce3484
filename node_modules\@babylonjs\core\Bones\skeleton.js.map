{"version": 3, "file": "skeleton.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Bones/skeleton.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAC9B,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAInE,OAAO,EAAE,UAAU,EAAE,MAAM,kCAAkC,CAAC;AAG9D,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAKhD;;;GAGG;AACH,MAAM,OAAO,QAAQ;IAmDjB;;;OAGG;IACH,IAAW,6BAA6B;QACpC,OAAO,IAAI,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IAED,IAAW,6BAA6B,CAAC,KAAc;QACnD,IAAI,CAAC,8BAA8B,GAAG,KAAK,CAAC;QAC5C,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAID;;OAEG;IACH,IAAW,2BAA2B;QAClC,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACpC,OAAO,IAAI,CAAC,MAAM,CAAC,2BAA2B,CAAC;SAClD;QACD,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED,IAAW,2BAA2B,CAAC,KAA4C;QAC/E,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;IAC9C,CAAC;IAeD;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,6BAA6B,IAAI,IAAI,CAAC,sBAAsB,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACH;IACI,gCAAgC;IACzB,IAAY;IACnB,8BAA8B;IACvB,EAAU,EACjB,KAAY;QAHL,SAAI,GAAJ,IAAI,CAAQ;QAEZ,OAAE,GAAF,EAAE,CAAQ;QApHrB;;WAEG;QACI,UAAK,GAAW,EAAE,CAAC;QAK1B;;WAEG;QACI,0BAAqB,GAAG,KAAK,CAAC;QAQ7B,aAAQ,GAAG,IAAI,CAAC;QAGhB,0BAAqB,GAAG,IAAI,KAAK,EAAgB,CAAC;QAElD,cAAS,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAE9B,qBAAgB,GAAG,CAAC,CAAC,CAAC;QAEtB,YAAO,GAAiD,EAAE,CAAC;QAE3D,8BAAyB,GAAG,IAAI,CAAC;QAEjC,2BAAsB,GAAG,KAAK,CAAC;QAC/B,cAAS,GAAG,CAAC,CAAC;QAEtB,gBAAgB;QACT,qCAAgC,GAAG,CAAC,CAAC;QAE5C,gBAAgB;QACT,oBAAe,GAAsB,IAAI,CAAC;QAEjD,gBAAgB;QACT,qBAAgB,GAA4B,IAAI,CAAC;QAExD;;WAEG;QACI,mBAAc,GAAG,KAAK,CAAC;QAEtB,mCAA8B,GAAG,IAAI,CAAC;QActC,iCAA4B,GAA0C,IAAI,CAAC;QAsBnF,SAAS;QAET;;WAEG;QACI,8BAAyB,GAAG,IAAI,UAAU,EAAY,CAAC;QA6B1D,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAEhB,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAE3C,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE9B,uEAAuE;QACvE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC;QACrD,IAAI,CAAC,sBAAsB,GAAG,UAAU,CAAC,YAAY,IAAI,UAAU,CAAC,0BAA0B,GAAG,CAAC,CAAC;IACvG,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,UAAU;IACV;;;;OAIG;IACI,oBAAoB,CAAC,IAA4B;QACpD,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,IAAI,EAAE;gBACP,MAAM,IAAI,KAAK,CAAC,0FAA0F,CAAC,CAAC;aAC/G;YACD,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aACtB;YAED,OAAO,IAAI,CAAC,uBAAwB,CAAC;SACxC;QAED,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC3C,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC1C;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;;;OAIG;IACI,yBAAyB,CAAC,IAAkB;QAC/C,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC5D,OAAO,IAAI,CAAC,uBAAuB,CAAC;SACvC;QAED,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,QAAQ;QACX,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,UAAU;IAEV;;;;OAIG;IACI,QAAQ,CAAC,WAAqB;QACjC,IAAI,GAAG,GAAG,SAAS,IAAI,CAAC,IAAI,aAAa,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAC7D,GAAG,IAAI,uBAAuB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;QACzF,IAAI,WAAW,EAAE;YACb,GAAG,IAAI,aAAa,CAAC;YACrB,IAAI,KAAK,GAAG,IAAI,CAAC;YACjB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC7B,IAAI,KAAK,EAAE;oBACP,GAAG,IAAI,IAAI,CAAC;oBACZ,KAAK,GAAG,KAAK,CAAC;iBACjB;gBACD,GAAG,IAAI,IAAI,CAAC;aACf;YACD,GAAG,IAAI,GAAG,CAAC;SACd;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,IAAY;QAClC,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,GAAG,KAAK,EAAE,SAAS,EAAE,EAAE;YAC/E,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;gBACrC,OAAO,SAAS,CAAC;aACpB;SACJ;QACD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACI,oBAAoB,CAAC,IAAY,EAAE,IAAY,EAAE,EAAU;QAC9D,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACxD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzD,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;oBAC7B,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;iBAC3D;aACJ;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,IAAY,EAAE,YAAY,GAAG,IAAI;QACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YACzD,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;gBAC7B,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;aAC/D;SACJ;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,mDAAmD;IAClF,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,IAAY;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,MAAM,eAAe,GAA+B,EAAE,CAAC;QACvD,IAAI,IAAY,CAAC;QACjB,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;YACvB,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;SAC5C;QACD,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;;;OAOG;IACI,kBAAkB,CAAC,MAAgB,EAAE,IAAY,EAAE,iBAAiB,GAAG,KAAK;QAC/E,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;YACvD,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,GAAG,GAAG,IAAI,CAAC;QACf,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;QAEzD,0GAA0G;QAC1G,MAAM,QAAQ,GAA4B,EAAE,CAAC;QAC7C,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC;QACjC,IAAI,MAAc,CAAC;QACnB,IAAI,CAAS,CAAC;QACd,KAAK,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YACtD,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;SAClD;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE;YAC1C,MAAM,CAAC,IAAI,CAAC,oCAAoC,IAAI,CAAC,KAAK,CAAC,MAAM,2BAA2B,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;YAClH,GAAG,GAAG,KAAK,CAAC;SACf;QAED,MAAM,mBAAmB,GAAG,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,IAAI,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAEjK,KAAK,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACpC,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACtC,IAAI,UAAU,EAAE;gBACZ,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;aACxH;iBAAM;gBACH,MAAM,CAAC,IAAI,CAAC,wDAAwD,GAAG,QAAQ,CAAC,CAAC;gBACjF,GAAG,GAAG,KAAK,CAAC;aACf;SACJ;QACD,6FAA6F;QAC7F,MAAM,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,GAAG,WAAW,EAAE,KAAK,CAAC,EAAE,GAAG,WAAW,CAAC,CAAC;SACnG;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;OAEG;IACI,YAAY;QACf,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;YAC3B,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;gBACpB,IAAI,CAAC,YAAY,EAAE,CAAC;aACvB;SACJ;IACL,CAAC;IAEO,yBAAyB;QAC7B,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YACzD,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;gBAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC;gBAC9D,IAAI,GAAG,GAAG,OAAO,EAAE;oBACf,GAAG,GAAG,OAAO,CAAC;iBACjB;aACJ;SACJ;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;;;OAOG;IACI,cAAc,CAAC,IAAY,EAAE,IAAc,EAAE,UAAmB,EAAE,cAA2B;QAChG,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IACpG,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,qBAAqB,CAAC,QAAkB,EAAE,cAAc,GAAG,CAAC,EAAE,KAAa;QACrF,MAAM,UAAU,GAAG,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAErD,qDAAqD;QACrD,IAAI,CAAC,UAAU,EAAE;YACb,OAAO,IAAI,CAAC;SACf;QAED,yFAAyF;QACzF,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QAC7E,IAAI,eAAe,GAAyB,IAAI,CAAC;QAEjD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC1D,MAAM,eAAe,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAEhD,IAAI,eAAe,CAAC,SAAS,KAAK,UAAU,EAAE,IAAI,IAAI,eAAe,CAAC,OAAO,KAAK,UAAU,EAAE,EAAE,EAAE;gBAC9F,eAAe,GAAG,eAAe,CAAC;gBAClC,MAAM;aACT;SACJ;QAED,yEAAyE;QACzE,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC;QAE9C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACrD,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;YAEzC,IAAI,CAAC,UAAU,EAAE;gBACb,SAAS;aACZ;YAED,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;gBAChE,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;aACjF;SACJ;QAED,8CAA8C;QAC9C,IAAI,eAAe,EAAE;YACjB,eAAe,CAAC,UAAU,GAAG,IAAI,CAAC;SACrC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,IAAkB;QACjD,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,6BAA6B,CAAC,IAAkB;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEvD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC/C;IACL,CAAC;IAEO,yBAAyB,CAAC,YAA0B,EAAE,iBAAmC;QAC7F,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAErD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACpD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/B,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAEpC,IAAI,UAAU,EAAE;gBACZ,IAAI,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;aAC3F;iBAAM;gBACH,IAAI,iBAAiB,EAAE;oBACnB,IAAI,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;iBACjF;qBAAM;oBACH,IAAI,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;iBACzD;aACJ;YAED,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;gBACpB,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC/D,IAAI,CAAC,4BAA4B,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,YAAY,EAAE,WAAW,GAAG,EAAE,CAAC,CAAC;aAC9G;SACJ;QAED,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;IACrE,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,gBAAgB,GAAG,KAAK;QACnC,IAAI,CAAC,gBAAgB,EAAE;YACnB,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC;YACtD,IAAI,IAAI,CAAC,gBAAgB,KAAK,eAAe,EAAE;gBAC3C,OAAO;aACV;YACD,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;SAC3C;QAED,gEAAgE;QAChE,IAAI,IAAI,CAAC,gCAAgC,GAAG,CAAC,EAAE;YAC3C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;gBAC3B,IAAI,IAAI,CAAC,oBAAoB,EAAE;oBAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC;oBACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC9B,IAAI,IAAI,CAAC,kBAAkB,EAAE;wBACzB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;qBACrD;yBAAM;wBACH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;qBACjC;oBACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;iBAC/B;aACJ;SACJ;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBAExC,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAChC,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;oBACvG,IAAI,CAAC,uBAAuB,GAAG,IAAI,YAAY,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC9E,WAAW,GAAG,IAAI,CAAC;iBACtB;gBAED,IAAI,CAAC,WAAW,EAAE;oBACd,SAAS;iBACZ;gBAED,IAAI,IAAI,CAAC,qBAAqB,KAAK,IAAI,EAAE;oBACrC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;oBAElC,gBAAgB;oBAChB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;wBAC3B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;4BACnB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;4BACpC,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;4BACvD,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;yBAC1D;qBACJ;oBAED,IAAI,IAAI,CAAC,yBAAyB,EAAE;wBAChC,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;wBACjD,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC,KAAK,KAAK,YAAY,EAAE;4BAChG,IAAI,IAAI,CAAC,uBAAuB,EAAE;gCAC9B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;6BAC1C;4BAED,IAAI,CAAC,uBAAuB,GAAG,UAAU,CAAC,iBAAiB,CACvD,IAAI,CAAC,uBAAuB,EAC5B,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAC3B,CAAC,EACD,IAAI,CAAC,MAAM,EACX,KAAK,EACL,KAAK,EACL,SAAS,CAAC,4BAA4B,EACtC,SAAS,CAAC,iBAAiB,CAC9B,CAAC;yBACL;qBACJ;iBACJ;gBAED,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;gBAEzE,IAAI,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,uBAAuB,EAAE;oBAChE,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;iBACrE;aACJ;SACJ;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChB,OAAO;aACV;YAED,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;gBAC7F,IAAI,CAAC,kBAAkB,GAAG,IAAI,YAAY,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBAEzE,IAAI,IAAI,CAAC,yBAAyB,EAAE;oBAChC,IAAI,IAAI,CAAC,uBAAuB,EAAE;wBAC9B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;qBAC1C;oBAED,IAAI,CAAC,uBAAuB,GAAG,UAAU,CAAC,iBAAiB,CACvD,IAAI,CAAC,kBAAkB,EACvB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAC3B,CAAC,EACD,IAAI,CAAC,MAAM,EACX,KAAK,EACL,KAAK,EACL,SAAS,CAAC,4BAA4B,EACtC,SAAS,CAAC,iBAAiB,CAC9B,CAAC;iBACL;aACJ;YAED,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;YAE9D,IAAI,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAChE,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAChE;SACJ;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACtE,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;YAEvB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACpD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;aAC7C;SACJ;QAED,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,IAAY,EAAE,EAAW;QAClC,MAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAE3D,MAAM,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAE1D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACpD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACjC,IAAI,UAAU,GAAG,IAAI,CAAC;YAEtB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAClC,IAAI,MAAM,EAAE;gBACR,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC/C,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;aAC1C;YAED,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,aAAa,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;YACvH,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YAE5B,IAAI,MAAM,CAAC,oBAAoB,EAAE;gBAC7B,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;aACvD;YAED,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;SAC3D;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,MAAM,CAAC,OAAO,GAAG,EAAE,CAAC;YACpB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,OAAO,EAAE;gBAClC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAEtC,IAAI,KAAK,EAAE;oBACP,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;iBAC7C;aACJ;SACJ;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAErB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,aAAa,GAAG,IAAI;QACtC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACxB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAoB,EAAE,EAAE;gBAC7C,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC;gBAChC,SAAS,CAAC,aAAa,GAAG,aAAa,CAAC;YAC5C,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;QAEtC,aAAa;QACb,IAAI,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAEpC,oBAAoB;QACpB,IAAI,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACZ,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACpD;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;YACvC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;SACvC;IACL,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;SAC1E;QAED,mBAAmB,CAAC,KAAK,GAAG,EAAE,CAAC;QAE/B,mBAAmB,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAEvE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACpD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAEhC,MAAM,cAAc,GAAQ;gBACxB,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzD,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;gBACtB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,OAAO,EAAE;gBACtC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,OAAO,EAAE;gBACpC,qBAAqB,EAAE,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAE;aACrD,CAAC;YAEF,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAE/C,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;aACvC;YAED,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;aAC3C;YAED,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC/C,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;aAC7D;YAED,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;YAChC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAElC,IAAI,CAAC,MAAM,EAAE;oBACT,SAAS;iBACZ;gBAED,MAAM,KAAK,GAAQ,EAAE,CAAC;gBACtB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;gBAClB,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;gBACzB,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;gBACrB,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC1C;SACJ;QACD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,cAAmB,EAAE,KAAY;QACjD,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAC7E,IAAI,cAAc,CAAC,gBAAgB,EAAE;YACjC,QAAQ,CAAC,gBAAgB,GAAG,OAAO,CAAC,SAAS,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;SAClF;QAED,QAAQ,CAAC,qBAAqB,GAAG,cAAc,CAAC,qBAAqB,CAAC;QAEtE,IAAI,KAAa,CAAC;QAClB,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC1D,MAAM,UAAU,GAAG,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,eAAe,GAAG,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;YAC1D,IAAI,UAAU,GAAG,IAAI,CAAC;YACtB,IAAI,UAAU,CAAC,eAAe,GAAG,CAAC,CAAC,EAAE;gBACjC,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;aAC3D;YAED,MAAM,IAAI,GAAqB,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1F,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;YAE/H,IAAI,UAAU,CAAC,EAAE,KAAK,SAAS,IAAI,UAAU,CAAC,EAAE,KAAK,IAAI,EAAE;gBACvD,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;aAC3B;YAED,IAAI,UAAU,CAAC,MAAM,EAAE;gBACnB,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;aACnC;YAED,IAAI,UAAU,CAAC,QAAQ,EAAE;gBACrB,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;aACvC;YAED,IAAI,UAAU,CAAC,SAAS,EAAE;gBACtB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;aAC/D;YAED,IAAI,UAAU,CAAC,qBAAqB,KAAK,SAAS,IAAI,UAAU,CAAC,qBAAqB,KAAK,IAAI,EAAE;gBAC7F,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;gBAChC,IAAI,CAAC,uBAAuB,GAAG,UAAU,CAAC,qBAAqB,CAAC;aACnE;SACJ;QAED,+DAA+D;QAC/D,IAAI,cAAc,CAAC,MAAM,EAAE;YACvB,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC3D,MAAM,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC1C,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;aAChE;SACJ;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,WAAW,GAAG,KAAK;QAC9C,IAAI,IAAI,CAAC,yBAAyB,IAAI,WAAW,EAAE;YAC/C,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,uBAAuB,EAAE,CAAC;YACxC,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;SAC1C;IACL,CAAC;IAED;;;;OAIG;IACI,yBAAyB,CAAC,WAAW,GAAG,KAAK;QAChD,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,IAAI,UAAU,GAAqB,IAAI,CAAC;QAExC,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE;YACvC,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;SAC9D;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,SAAS;QACZ,MAAM,KAAK,GAAW,EAAE,CAAC;QACzB,MAAM,OAAO,GAAG,IAAI,KAAK,CAAU,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACtD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACpD,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAEO,UAAU,CAAC,KAAa,EAAE,KAAa,EAAE,OAAkB;QAC/D,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;YAChB,OAAO;SACV;QAED,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QAEtB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC3B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;SACvB;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACpC,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACnE;QAED,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,oBAAoB;QACvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACrB,CAAC,CAAC,oBAAoB,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;IACP,CAAC;CACJ", "sourcesContent": ["import { Bone } from \"./bone\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Vector3, Matrix, TmpVectors } from \"../Maths/math.vector\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { RawTexture } from \"../Materials/Textures/rawTexture\";\r\nimport type { Animatable } from \"../Animations/animatable\";\r\nimport type { AnimationPropertiesOverride } from \"../Animations/animationPropertiesOverride\";\r\nimport { Animation } from \"../Animations/animation\";\r\nimport { AnimationRange } from \"../Animations/animationRange\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { DeepCopier } from \"../Misc/deepCopier\";\r\nimport type { IInspectable } from \"../Misc/iInspectable\";\r\nimport type { IAnimatable } from \"../Animations/animatable.interface\";\r\nimport type { AbstractScene } from \"../abstractScene\";\r\n\r\n/**\r\n * Class used to handle skinning animations\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons\r\n */\r\nexport class Skeleton implements IAnimatable {\r\n    /**\r\n     * Defines the list of child bones\r\n     */\r\n    public bones: Bone[] = [];\r\n    /**\r\n     * Defines an estimate of the dimension of the skeleton at rest\r\n     */\r\n    public dimensionsAtRest: Vector3;\r\n    /**\r\n     * Defines a boolean indicating if the root matrix is provided by meshes or by the current skeleton (this is the default value)\r\n     */\r\n    public needInitialSkinMatrix = false;\r\n\r\n    /**\r\n     * Gets the list of animations attached to this skeleton\r\n     */\r\n    public animations: Array<Animation>;\r\n\r\n    private _scene: Scene;\r\n    private _isDirty = true;\r\n    private _transformMatrices: Float32Array;\r\n    private _transformMatrixTexture: Nullable<RawTexture>;\r\n    private _meshesWithPoseMatrix = new Array<AbstractMesh>();\r\n    private _animatables: IAnimatable[];\r\n    private _identity = Matrix.Identity();\r\n    private _synchronizedWithMesh: AbstractMesh;\r\n    private _currentRenderId = -1;\r\n\r\n    private _ranges: { [name: string]: Nullable<AnimationRange> } = {};\r\n\r\n    private _absoluteTransformIsDirty = true;\r\n\r\n    private _canUseTextureForBones = false;\r\n    private _uniqueId = 0;\r\n\r\n    /** @internal */\r\n    public _numBonesWithLinkedTransformNode = 0;\r\n\r\n    /** @internal */\r\n    public _hasWaitingData: Nullable<boolean> = null;\r\n\r\n    /** @internal */\r\n    public _parentContainer: Nullable<AbstractScene> = null;\r\n\r\n    /**\r\n     * Specifies if the skeleton should be serialized\r\n     */\r\n    public doNotSerialize = false;\r\n\r\n    private _useTextureToStoreBoneMatrices = true;\r\n    /**\r\n     * Gets or sets a boolean indicating that bone matrices should be stored as a texture instead of using shader uniforms (default is true).\r\n     * Please note that this option is not available if the hardware does not support it\r\n     */\r\n    public get useTextureToStoreBoneMatrices(): boolean {\r\n        return this._useTextureToStoreBoneMatrices;\r\n    }\r\n\r\n    public set useTextureToStoreBoneMatrices(value: boolean) {\r\n        this._useTextureToStoreBoneMatrices = value;\r\n        this._markAsDirty();\r\n    }\r\n\r\n    private _animationPropertiesOverride: Nullable<AnimationPropertiesOverride> = null;\r\n\r\n    /**\r\n     * Gets or sets the animation properties override\r\n     */\r\n    public get animationPropertiesOverride(): Nullable<AnimationPropertiesOverride> {\r\n        if (!this._animationPropertiesOverride) {\r\n            return this._scene.animationPropertiesOverride;\r\n        }\r\n        return this._animationPropertiesOverride;\r\n    }\r\n\r\n    public set animationPropertiesOverride(value: Nullable<AnimationPropertiesOverride>) {\r\n        this._animationPropertiesOverride = value;\r\n    }\r\n\r\n    /**\r\n     * List of inspectable custom properties (used by the Inspector)\r\n     * @see https://doc.babylonjs.com/toolsAndResources/inspector#extensibility\r\n     */\r\n    public inspectableCustomProperties: IInspectable[];\r\n\r\n    // Events\r\n\r\n    /**\r\n     * An observable triggered before computing the skeleton's matrices\r\n     */\r\n    public onBeforeComputeObservable = new Observable<Skeleton>();\r\n\r\n    /**\r\n     * Gets a boolean indicating that the skeleton effectively stores matrices into a texture\r\n     */\r\n    public get isUsingTextureForMatrices() {\r\n        return this.useTextureToStoreBoneMatrices && this._canUseTextureForBones;\r\n    }\r\n\r\n    /**\r\n     * Gets the unique ID of this skeleton\r\n     */\r\n    public get uniqueId(): number {\r\n        return this._uniqueId;\r\n    }\r\n\r\n    /**\r\n     * Creates a new skeleton\r\n     * @param name defines the skeleton name\r\n     * @param id defines the skeleton Id\r\n     * @param scene defines the hosting scene\r\n     */\r\n    constructor(\r\n        /** defines the skeleton name */\r\n        public name: string,\r\n        /** defines the skeleton Id */\r\n        public id: string,\r\n        scene: Scene\r\n    ) {\r\n        this.bones = [];\r\n\r\n        this._scene = scene || EngineStore.LastCreatedScene;\r\n        this._uniqueId = this._scene.getUniqueId();\r\n\r\n        this._scene.addSkeleton(this);\r\n\r\n        //make sure it will recalculate the matrix next time prepare is called.\r\n        this._isDirty = true;\r\n\r\n        const engineCaps = this._scene.getEngine().getCaps();\r\n        this._canUseTextureForBones = engineCaps.textureFloat && engineCaps.maxVertexTextureImageUnits > 0;\r\n    }\r\n\r\n    /**\r\n     * Gets the current object class name.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"Skeleton\";\r\n    }\r\n\r\n    /**\r\n     * Returns an array containing the root bones\r\n     * @returns an array containing the root bones\r\n     */\r\n    public getChildren(): Array<Bone> {\r\n        return this.bones.filter((b) => !b.getParent());\r\n    }\r\n\r\n    // Members\r\n    /**\r\n     * Gets the list of transform matrices to send to shaders (one matrix per bone)\r\n     * @param mesh defines the mesh to use to get the root matrix (if needInitialSkinMatrix === true)\r\n     * @returns a Float32Array containing matrices data\r\n     */\r\n    public getTransformMatrices(mesh: Nullable<AbstractMesh>): Float32Array {\r\n        if (this.needInitialSkinMatrix) {\r\n            if (!mesh) {\r\n                throw new Error(\"getTransformMatrices: When using the needInitialSkinMatrix flag, a mesh must be provided\");\r\n            }\r\n            if (!mesh._bonesTransformMatrices) {\r\n                this.prepare(true);\r\n            }\r\n\r\n            return mesh._bonesTransformMatrices!;\r\n        }\r\n\r\n        if (!this._transformMatrices || this._isDirty) {\r\n            this.prepare(!this._transformMatrices);\r\n        }\r\n\r\n        return this._transformMatrices;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of transform matrices to send to shaders inside a texture (one matrix per bone)\r\n     * @param mesh defines the mesh to use to get the root matrix (if needInitialSkinMatrix === true)\r\n     * @returns a raw texture containing the data\r\n     */\r\n    public getTransformMatrixTexture(mesh: AbstractMesh): Nullable<RawTexture> {\r\n        if (this.needInitialSkinMatrix && mesh._transformMatrixTexture) {\r\n            return mesh._transformMatrixTexture;\r\n        }\r\n\r\n        return this._transformMatrixTexture;\r\n    }\r\n\r\n    /**\r\n     * Gets the current hosting scene\r\n     * @returns a scene object\r\n     */\r\n    public getScene(): Scene {\r\n        return this._scene;\r\n    }\r\n\r\n    // Methods\r\n\r\n    /**\r\n     * Gets a string representing the current skeleton data\r\n     * @param fullDetails defines a boolean indicating if we want a verbose version\r\n     * @returns a string representing the current skeleton data\r\n     */\r\n    public toString(fullDetails?: boolean): string {\r\n        let ret = `Name: ${this.name}, nBones: ${this.bones.length}`;\r\n        ret += `, nAnimationRanges: ${this._ranges ? Object.keys(this._ranges).length : \"none\"}`;\r\n        if (fullDetails) {\r\n            ret += \", Ranges: {\";\r\n            let first = true;\r\n            for (const name in this._ranges) {\r\n                if (first) {\r\n                    ret += \", \";\r\n                    first = false;\r\n                }\r\n                ret += name;\r\n            }\r\n            ret += \"}\";\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    /**\r\n     * Get bone's index searching by name\r\n     * @param name defines bone's name to search for\r\n     * @returns the indice of the bone. Returns -1 if not found\r\n     */\r\n    public getBoneIndexByName(name: string): number {\r\n        for (let boneIndex = 0, cache = this.bones.length; boneIndex < cache; boneIndex++) {\r\n            if (this.bones[boneIndex].name === name) {\r\n                return boneIndex;\r\n            }\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    /**\r\n     * Create a new animation range\r\n     * @param name defines the name of the range\r\n     * @param from defines the start key\r\n     * @param to defines the end key\r\n     */\r\n    public createAnimationRange(name: string, from: number, to: number): void {\r\n        // check name not already in use\r\n        if (!this._ranges[name]) {\r\n            this._ranges[name] = new AnimationRange(name, from, to);\r\n            for (let i = 0, nBones = this.bones.length; i < nBones; i++) {\r\n                if (this.bones[i].animations[0]) {\r\n                    this.bones[i].animations[0].createRange(name, from, to);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Delete a specific animation range\r\n     * @param name defines the name of the range\r\n     * @param deleteFrames defines if frames must be removed as well\r\n     */\r\n    public deleteAnimationRange(name: string, deleteFrames = true): void {\r\n        for (let i = 0, nBones = this.bones.length; i < nBones; i++) {\r\n            if (this.bones[i].animations[0]) {\r\n                this.bones[i].animations[0].deleteRange(name, deleteFrames);\r\n            }\r\n        }\r\n        this._ranges[name] = null; // said much faster than 'delete this._range[name]'\r\n    }\r\n\r\n    /**\r\n     * Gets a specific animation range\r\n     * @param name defines the name of the range to look for\r\n     * @returns the requested animation range or null if not found\r\n     */\r\n    public getAnimationRange(name: string): Nullable<AnimationRange> {\r\n        return this._ranges[name] || null;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of all animation ranges defined on this skeleton\r\n     * @returns an array\r\n     */\r\n    public getAnimationRanges(): Nullable<AnimationRange>[] {\r\n        const animationRanges: Nullable<AnimationRange>[] = [];\r\n        let name: string;\r\n        for (name in this._ranges) {\r\n            animationRanges.push(this._ranges[name]);\r\n        }\r\n        return animationRanges;\r\n    }\r\n\r\n    /**\r\n     * Copy animation range from a source skeleton.\r\n     * This is not for a complete retargeting, only between very similar skeleton's with only possible bone length differences\r\n     * @param source defines the source skeleton\r\n     * @param name defines the name of the range to copy\r\n     * @param rescaleAsRequired defines if rescaling must be applied if required\r\n     * @returns true if operation was successful\r\n     */\r\n    public copyAnimationRange(source: Skeleton, name: string, rescaleAsRequired = false): boolean {\r\n        if (this._ranges[name] || !source.getAnimationRange(name)) {\r\n            return false;\r\n        }\r\n        let ret = true;\r\n        const frameOffset = this._getHighestAnimationFrame() + 1;\r\n\r\n        // make a dictionary of source skeleton's bones, so exact same order or doubly nested loop is not required\r\n        const boneDict: { [key: string]: Bone } = {};\r\n        const sourceBones = source.bones;\r\n        let nBones: number;\r\n        let i: number;\r\n        for (i = 0, nBones = sourceBones.length; i < nBones; i++) {\r\n            boneDict[sourceBones[i].name] = sourceBones[i];\r\n        }\r\n\r\n        if (this.bones.length !== sourceBones.length) {\r\n            Logger.Warn(`copyAnimationRange: this rig has ${this.bones.length} bones, while source as ${sourceBones.length}`);\r\n            ret = false;\r\n        }\r\n\r\n        const skelDimensionsRatio = rescaleAsRequired && this.dimensionsAtRest && source.dimensionsAtRest ? this.dimensionsAtRest.divide(source.dimensionsAtRest) : null;\r\n\r\n        for (i = 0, nBones = this.bones.length; i < nBones; i++) {\r\n            const boneName = this.bones[i].name;\r\n            const sourceBone = boneDict[boneName];\r\n            if (sourceBone) {\r\n                ret = ret && this.bones[i].copyAnimationRange(sourceBone, name, frameOffset, rescaleAsRequired, skelDimensionsRatio);\r\n            } else {\r\n                Logger.Warn(\"copyAnimationRange: not same rig, missing source bone \" + boneName);\r\n                ret = false;\r\n            }\r\n        }\r\n        // do not call createAnimationRange(), since it also is done to bones, which was already done\r\n        const range = source.getAnimationRange(name);\r\n        if (range) {\r\n            this._ranges[name] = new AnimationRange(name, range.from + frameOffset, range.to + frameOffset);\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    /**\r\n     * Forces the skeleton to go to rest pose\r\n     */\r\n    public returnToRest(): void {\r\n        for (const bone of this.bones) {\r\n            if (bone._index !== -1) {\r\n                bone.returnToRest();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _getHighestAnimationFrame(): number {\r\n        let ret = 0;\r\n        for (let i = 0, nBones = this.bones.length; i < nBones; i++) {\r\n            if (this.bones[i].animations[0]) {\r\n                const highest = this.bones[i].animations[0].getHighestFrame();\r\n                if (ret < highest) {\r\n                    ret = highest;\r\n                }\r\n            }\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    /**\r\n     * Begin a specific animation range\r\n     * @param name defines the name of the range to start\r\n     * @param loop defines if looping must be turned on (false by default)\r\n     * @param speedRatio defines the speed ratio to apply (1 by default)\r\n     * @param onAnimationEnd defines a callback which will be called when animation will end\r\n     * @returns a new animatable\r\n     */\r\n    public beginAnimation(name: string, loop?: boolean, speedRatio?: number, onAnimationEnd?: () => void): Nullable<Animatable> {\r\n        const range = this.getAnimationRange(name);\r\n\r\n        if (!range) {\r\n            return null;\r\n        }\r\n\r\n        return this._scene.beginAnimation(this, range.from, range.to, loop, speedRatio, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Convert the keyframes for a range of animation on a skeleton to be relative to a given reference frame.\r\n     * @param skeleton defines the Skeleton containing the animation range to convert\r\n     * @param referenceFrame defines the frame that keyframes in the range will be relative to\r\n     * @param range defines the name of the AnimationRange belonging to the Skeleton to convert\r\n     * @returns the original skeleton\r\n     */\r\n    public static MakeAnimationAdditive(skeleton: Skeleton, referenceFrame = 0, range: string): Nullable<Skeleton> {\r\n        const rangeValue = skeleton.getAnimationRange(range);\r\n\r\n        // We can't make a range additive if it doesn't exist\r\n        if (!rangeValue) {\r\n            return null;\r\n        }\r\n\r\n        // Find any current scene-level animatable belonging to the target that matches the range\r\n        const sceneAnimatables = skeleton._scene.getAllAnimatablesByTarget(skeleton);\r\n        let rangeAnimatable: Nullable<Animatable> = null;\r\n\r\n        for (let index = 0; index < sceneAnimatables.length; index++) {\r\n            const sceneAnimatable = sceneAnimatables[index];\r\n\r\n            if (sceneAnimatable.fromFrame === rangeValue?.from && sceneAnimatable.toFrame === rangeValue?.to) {\r\n                rangeAnimatable = sceneAnimatable;\r\n                break;\r\n            }\r\n        }\r\n\r\n        // Convert the animations belonging to the skeleton to additive keyframes\r\n        const animatables = skeleton.getAnimatables();\r\n\r\n        for (let index = 0; index < animatables.length; index++) {\r\n            const animatable = animatables[index];\r\n            const animations = animatable.animations;\r\n\r\n            if (!animations) {\r\n                continue;\r\n            }\r\n\r\n            for (let animIndex = 0; animIndex < animations.length; animIndex++) {\r\n                Animation.MakeAnimationAdditive(animations[animIndex], referenceFrame, range);\r\n            }\r\n        }\r\n\r\n        // Mark the scene-level animatable as additive\r\n        if (rangeAnimatable) {\r\n            rangeAnimatable.isAdditive = true;\r\n        }\r\n\r\n        return skeleton;\r\n    }\r\n\r\n    /** @internal */\r\n    public _markAsDirty(): void {\r\n        this._isDirty = true;\r\n        this._absoluteTransformIsDirty = true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _registerMeshWithPoseMatrix(mesh: AbstractMesh): void {\r\n        this._meshesWithPoseMatrix.push(mesh);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _unregisterMeshWithPoseMatrix(mesh: AbstractMesh): void {\r\n        const index = this._meshesWithPoseMatrix.indexOf(mesh);\r\n\r\n        if (index > -1) {\r\n            this._meshesWithPoseMatrix.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    private _computeTransformMatrices(targetMatrix: Float32Array, initialSkinMatrix: Nullable<Matrix>): void {\r\n        this.onBeforeComputeObservable.notifyObservers(this);\r\n\r\n        for (let index = 0; index < this.bones.length; index++) {\r\n            const bone = this.bones[index];\r\n            bone._childUpdateId++;\r\n            const parentBone = bone.getParent();\r\n\r\n            if (parentBone) {\r\n                bone.getLocalMatrix().multiplyToRef(parentBone.getFinalMatrix(), bone.getFinalMatrix());\r\n            } else {\r\n                if (initialSkinMatrix) {\r\n                    bone.getLocalMatrix().multiplyToRef(initialSkinMatrix, bone.getFinalMatrix());\r\n                } else {\r\n                    bone.getFinalMatrix().copyFrom(bone.getLocalMatrix());\r\n                }\r\n            }\r\n\r\n            if (bone._index !== -1) {\r\n                const mappedIndex = bone._index === null ? index : bone._index;\r\n                bone.getAbsoluteInverseBindMatrix().multiplyToArray(bone.getFinalMatrix(), targetMatrix, mappedIndex * 16);\r\n            }\r\n        }\r\n\r\n        this._identity.copyToArray(targetMatrix, this.bones.length * 16);\r\n    }\r\n\r\n    /**\r\n     * Build all resources required to render a skeleton\r\n     * @param dontCheckFrameId defines a boolean indicating if prepare should be run without checking first the current frame id (default: false)\r\n     */\r\n    public prepare(dontCheckFrameId = false): void {\r\n        if (!dontCheckFrameId) {\r\n            const currentRenderId = this.getScene().getRenderId();\r\n            if (this._currentRenderId === currentRenderId) {\r\n                return;\r\n            }\r\n            this._currentRenderId = currentRenderId;\r\n        }\r\n\r\n        // Update the local matrix of bones with linked transform nodes.\r\n        if (this._numBonesWithLinkedTransformNode > 0) {\r\n            for (const bone of this.bones) {\r\n                if (bone._linkedTransformNode) {\r\n                    const node = bone._linkedTransformNode;\r\n                    bone.position = node.position;\r\n                    if (node.rotationQuaternion) {\r\n                        bone.rotationQuaternion = node.rotationQuaternion;\r\n                    } else {\r\n                        bone.rotation = node.rotation;\r\n                    }\r\n                    bone.scaling = node.scaling;\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this.needInitialSkinMatrix) {\r\n            for (const mesh of this._meshesWithPoseMatrix) {\r\n                const poseMatrix = mesh.getPoseMatrix();\r\n\r\n                let needsUpdate = this._isDirty;\r\n                if (!mesh._bonesTransformMatrices || mesh._bonesTransformMatrices.length !== 16 * (this.bones.length + 1)) {\r\n                    mesh._bonesTransformMatrices = new Float32Array(16 * (this.bones.length + 1));\r\n                    needsUpdate = true;\r\n                }\r\n\r\n                if (!needsUpdate) {\r\n                    continue;\r\n                }\r\n\r\n                if (this._synchronizedWithMesh !== mesh) {\r\n                    this._synchronizedWithMesh = mesh;\r\n\r\n                    // Prepare bones\r\n                    for (const bone of this.bones) {\r\n                        if (!bone.getParent()) {\r\n                            const matrix = bone.getBindMatrix();\r\n                            matrix.multiplyToRef(poseMatrix, TmpVectors.Matrix[1]);\r\n                            bone._updateAbsoluteBindMatrices(TmpVectors.Matrix[1]);\r\n                        }\r\n                    }\r\n\r\n                    if (this.isUsingTextureForMatrices) {\r\n                        const textureWidth = (this.bones.length + 1) * 4;\r\n                        if (!mesh._transformMatrixTexture || mesh._transformMatrixTexture.getSize().width !== textureWidth) {\r\n                            if (mesh._transformMatrixTexture) {\r\n                                mesh._transformMatrixTexture.dispose();\r\n                            }\r\n\r\n                            mesh._transformMatrixTexture = RawTexture.CreateRGBATexture(\r\n                                mesh._bonesTransformMatrices,\r\n                                (this.bones.length + 1) * 4,\r\n                                1,\r\n                                this._scene,\r\n                                false,\r\n                                false,\r\n                                Constants.TEXTURE_NEAREST_SAMPLINGMODE,\r\n                                Constants.TEXTURETYPE_FLOAT\r\n                            );\r\n                        }\r\n                    }\r\n                }\r\n\r\n                this._computeTransformMatrices(mesh._bonesTransformMatrices, poseMatrix);\r\n\r\n                if (this.isUsingTextureForMatrices && mesh._transformMatrixTexture) {\r\n                    mesh._transformMatrixTexture.update(mesh._bonesTransformMatrices);\r\n                }\r\n            }\r\n        } else {\r\n            if (!this._isDirty) {\r\n                return;\r\n            }\r\n\r\n            if (!this._transformMatrices || this._transformMatrices.length !== 16 * (this.bones.length + 1)) {\r\n                this._transformMatrices = new Float32Array(16 * (this.bones.length + 1));\r\n\r\n                if (this.isUsingTextureForMatrices) {\r\n                    if (this._transformMatrixTexture) {\r\n                        this._transformMatrixTexture.dispose();\r\n                    }\r\n\r\n                    this._transformMatrixTexture = RawTexture.CreateRGBATexture(\r\n                        this._transformMatrices,\r\n                        (this.bones.length + 1) * 4,\r\n                        1,\r\n                        this._scene,\r\n                        false,\r\n                        false,\r\n                        Constants.TEXTURE_NEAREST_SAMPLINGMODE,\r\n                        Constants.TEXTURETYPE_FLOAT\r\n                    );\r\n                }\r\n            }\r\n\r\n            this._computeTransformMatrices(this._transformMatrices, null);\r\n\r\n            if (this.isUsingTextureForMatrices && this._transformMatrixTexture) {\r\n                this._transformMatrixTexture.update(this._transformMatrices);\r\n            }\r\n        }\r\n\r\n        this._isDirty = false;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of animatables currently running for this skeleton\r\n     * @returns an array of animatables\r\n     */\r\n    public getAnimatables(): IAnimatable[] {\r\n        if (!this._animatables || this._animatables.length !== this.bones.length) {\r\n            this._animatables = [];\r\n\r\n            for (let index = 0; index < this.bones.length; index++) {\r\n                this._animatables.push(this.bones[index]);\r\n            }\r\n        }\r\n\r\n        return this._animatables;\r\n    }\r\n\r\n    /**\r\n     * Clone the current skeleton\r\n     * @param name defines the name of the new skeleton\r\n     * @param id defines the id of the new skeleton\r\n     * @returns the new skeleton\r\n     */\r\n    public clone(name: string, id?: string): Skeleton {\r\n        const result = new Skeleton(name, id || name, this._scene);\r\n\r\n        result.needInitialSkinMatrix = this.needInitialSkinMatrix;\r\n\r\n        for (let index = 0; index < this.bones.length; index++) {\r\n            const source = this.bones[index];\r\n            let parentBone = null;\r\n\r\n            const parent = source.getParent();\r\n            if (parent) {\r\n                const parentIndex = this.bones.indexOf(parent);\r\n                parentBone = result.bones[parentIndex];\r\n            }\r\n\r\n            const bone = new Bone(source.name, result, parentBone, source.getBindMatrix().clone(), source.getRestMatrix().clone());\r\n            bone._index = source._index;\r\n\r\n            if (source._linkedTransformNode) {\r\n                bone.linkTransformNode(source._linkedTransformNode);\r\n            }\r\n\r\n            DeepCopier.DeepCopy(source.animations, bone.animations);\r\n        }\r\n\r\n        if (this._ranges) {\r\n            result._ranges = {};\r\n            for (const rangeName in this._ranges) {\r\n                const range = this._ranges[rangeName];\r\n\r\n                if (range) {\r\n                    result._ranges[rangeName] = range.clone();\r\n                }\r\n            }\r\n        }\r\n\r\n        this._isDirty = true;\r\n\r\n        result.prepare(true);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Enable animation blending for this skeleton\r\n     * @param blendingSpeed defines the blending speed to apply\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending\r\n     */\r\n    public enableBlending(blendingSpeed = 0.01) {\r\n        this.bones.forEach((bone) => {\r\n            bone.animations.forEach((animation: Animation) => {\r\n                animation.enableBlending = true;\r\n                animation.blendingSpeed = blendingSpeed;\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Releases all resources associated with the current skeleton\r\n     */\r\n    public dispose() {\r\n        this._meshesWithPoseMatrix.length = 0;\r\n\r\n        // Animations\r\n        this.getScene().stopAnimation(this);\r\n\r\n        // Remove from scene\r\n        this.getScene().removeSkeleton(this);\r\n\r\n        if (this._parentContainer) {\r\n            const index = this._parentContainer.skeletons.indexOf(this);\r\n            if (index > -1) {\r\n                this._parentContainer.skeletons.splice(index, 1);\r\n            }\r\n            this._parentContainer = null;\r\n        }\r\n\r\n        if (this._transformMatrixTexture) {\r\n            this._transformMatrixTexture.dispose();\r\n            this._transformMatrixTexture = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serialize the skeleton in a JSON object\r\n     * @returns a JSON object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.id = this.id;\r\n\r\n        if (this.dimensionsAtRest) {\r\n            serializationObject.dimensionsAtRest = this.dimensionsAtRest.asArray();\r\n        }\r\n\r\n        serializationObject.bones = [];\r\n\r\n        serializationObject.needInitialSkinMatrix = this.needInitialSkinMatrix;\r\n\r\n        for (let index = 0; index < this.bones.length; index++) {\r\n            const bone = this.bones[index];\r\n            const parent = bone.getParent();\r\n\r\n            const serializedBone: any = {\r\n                parentBoneIndex: parent ? this.bones.indexOf(parent) : -1,\r\n                index: bone.getIndex(),\r\n                name: bone.name,\r\n                id: bone.id,\r\n                matrix: bone.getBindMatrix().asArray(),\r\n                rest: bone.getRestMatrix().asArray(),\r\n                linkedTransformNodeId: bone.getTransformNode()?.id,\r\n            };\r\n\r\n            serializationObject.bones.push(serializedBone);\r\n\r\n            if (bone.length) {\r\n                serializedBone.length = bone.length;\r\n            }\r\n\r\n            if (bone.metadata) {\r\n                serializedBone.metadata = bone.metadata;\r\n            }\r\n\r\n            if (bone.animations && bone.animations.length > 0) {\r\n                serializedBone.animation = bone.animations[0].serialize();\r\n            }\r\n\r\n            serializationObject.ranges = [];\r\n            for (const name in this._ranges) {\r\n                const source = this._ranges[name];\r\n\r\n                if (!source) {\r\n                    continue;\r\n                }\r\n\r\n                const range: any = {};\r\n                range.name = name;\r\n                range.from = source.from;\r\n                range.to = source.to;\r\n                serializationObject.ranges.push(range);\r\n            }\r\n        }\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Creates a new skeleton from serialized data\r\n     * @param parsedSkeleton defines the serialized data\r\n     * @param scene defines the hosting scene\r\n     * @returns a new skeleton\r\n     */\r\n    public static Parse(parsedSkeleton: any, scene: Scene): Skeleton {\r\n        const skeleton = new Skeleton(parsedSkeleton.name, parsedSkeleton.id, scene);\r\n        if (parsedSkeleton.dimensionsAtRest) {\r\n            skeleton.dimensionsAtRest = Vector3.FromArray(parsedSkeleton.dimensionsAtRest);\r\n        }\r\n\r\n        skeleton.needInitialSkinMatrix = parsedSkeleton.needInitialSkinMatrix;\r\n\r\n        let index: number;\r\n        for (index = 0; index < parsedSkeleton.bones.length; index++) {\r\n            const parsedBone = parsedSkeleton.bones[index];\r\n            const parsedBoneIndex = parsedSkeleton.bones[index].index;\r\n            let parentBone = null;\r\n            if (parsedBone.parentBoneIndex > -1) {\r\n                parentBone = skeleton.bones[parsedBone.parentBoneIndex];\r\n            }\r\n\r\n            const rest: Nullable<Matrix> = parsedBone.rest ? Matrix.FromArray(parsedBone.rest) : null;\r\n            const bone = new Bone(parsedBone.name, skeleton, parentBone, Matrix.FromArray(parsedBone.matrix), rest, null, parsedBoneIndex);\r\n\r\n            if (parsedBone.id !== undefined && parsedBone.id !== null) {\r\n                bone.id = parsedBone.id;\r\n            }\r\n\r\n            if (parsedBone.length) {\r\n                bone.length = parsedBone.length;\r\n            }\r\n\r\n            if (parsedBone.metadata) {\r\n                bone.metadata = parsedBone.metadata;\r\n            }\r\n\r\n            if (parsedBone.animation) {\r\n                bone.animations.push(Animation.Parse(parsedBone.animation));\r\n            }\r\n\r\n            if (parsedBone.linkedTransformNodeId !== undefined && parsedBone.linkedTransformNodeId !== null) {\r\n                skeleton._hasWaitingData = true;\r\n                bone._waitingTransformNodeId = parsedBone.linkedTransformNodeId;\r\n            }\r\n        }\r\n\r\n        // placed after bones, so createAnimationRange can cascade down\r\n        if (parsedSkeleton.ranges) {\r\n            for (index = 0; index < parsedSkeleton.ranges.length; index++) {\r\n                const data = parsedSkeleton.ranges[index];\r\n                skeleton.createAnimationRange(data.name, data.from, data.to);\r\n            }\r\n        }\r\n        return skeleton;\r\n    }\r\n\r\n    /**\r\n     * Compute all node absolute matrices\r\n     * @param forceUpdate defines if computation must be done even if cache is up to date\r\n     */\r\n    public computeAbsoluteMatrices(forceUpdate = false): void {\r\n        if (this._absoluteTransformIsDirty || forceUpdate) {\r\n            this.bones[0].computeAbsoluteMatrices();\r\n            this._absoluteTransformIsDirty = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Compute all node absolute matrices\r\n     * @param forceUpdate defines if computation must be done even if cache is up to date\r\n     * @deprecated Please use computeAbsoluteMatrices instead\r\n     */\r\n    public computeAbsoluteTransforms(forceUpdate = false): void {\r\n        this.computeAbsoluteMatrices(forceUpdate);\r\n    }\r\n\r\n    /**\r\n     * Gets the root pose matrix\r\n     * @returns a matrix\r\n     */\r\n    public getPoseMatrix(): Nullable<Matrix> {\r\n        let poseMatrix: Nullable<Matrix> = null;\r\n\r\n        if (this._meshesWithPoseMatrix.length > 0) {\r\n            poseMatrix = this._meshesWithPoseMatrix[0].getPoseMatrix();\r\n        }\r\n\r\n        return poseMatrix;\r\n    }\r\n\r\n    /**\r\n     * Sorts bones per internal index\r\n     */\r\n    public sortBones(): void {\r\n        const bones: Bone[] = [];\r\n        const visited = new Array<boolean>(this.bones.length);\r\n        for (let index = 0; index < this.bones.length; index++) {\r\n            this._sortBones(index, bones, visited);\r\n        }\r\n\r\n        this.bones = bones;\r\n    }\r\n\r\n    private _sortBones(index: number, bones: Bone[], visited: boolean[]): void {\r\n        if (visited[index]) {\r\n            return;\r\n        }\r\n\r\n        visited[index] = true;\r\n\r\n        const bone = this.bones[index];\r\n        if (!bone) return;\r\n\r\n        if (bone._index === undefined) {\r\n            bone._index = index;\r\n        }\r\n\r\n        const parentBone = bone.getParent();\r\n        if (parentBone) {\r\n            this._sortBones(this.bones.indexOf(parentBone), bones, visited);\r\n        }\r\n\r\n        bones.push(bone);\r\n    }\r\n\r\n    /**\r\n     * Set the current local matrix as the restPose for all bones in the skeleton.\r\n     */\r\n    public setCurrentPoseAsRest(): void {\r\n        this.bones.forEach((b) => {\r\n            b.setCurrentPoseAsRest();\r\n        });\r\n    }\r\n}\r\n"]}