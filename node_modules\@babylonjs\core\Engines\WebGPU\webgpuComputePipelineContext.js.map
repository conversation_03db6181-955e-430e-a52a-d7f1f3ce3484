{"version": 3, "file": "webgpuComputePipelineContext.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuComputePipelineContext.ts"], "names": [], "mappings": "AAIA,gBAAgB;AAChB,MAAM,OAAO,4BAA4B;IAYrC,IAAW,OAAO;QACd,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAW,OAAO;QACd,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAKD,YAAY,MAAoB;QAC5B,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAEM,qBAAqB;QACxB,OAAO,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC;IACjC,CAAC;IAEM,OAAO,KAAU,CAAC;CAC5B", "sourcesContent": ["import type { IComputePipelineContext } from \"../../Compute/IComputePipelineContext\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { WebGPUEngine } from \"../webgpuEngine\";\r\n\r\n/** @internal */\r\nexport class WebGPUComputePipelineContext implements IComputePipelineContext {\r\n    public engine: WebGPUEngine;\r\n\r\n    public sources: {\r\n        compute: string;\r\n        rawCompute: string;\r\n    };\r\n\r\n    public stage: Nullable<GPUProgrammableStage>;\r\n\r\n    public computePipeline: GPUComputePipeline;\r\n\r\n    public get isAsync() {\r\n        return false;\r\n    }\r\n\r\n    public get isReady(): boolean {\r\n        if (this.stage) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /** @internal */\r\n    public _name: string;\r\n\r\n    constructor(engine: WebGPUEngine) {\r\n        this._name = \"unnamed\";\r\n        this.engine = engine;\r\n    }\r\n\r\n    public _getComputeShaderCode(): string | null {\r\n        return this.sources?.compute;\r\n    }\r\n\r\n    public dispose(): void {}\r\n}\r\n"]}