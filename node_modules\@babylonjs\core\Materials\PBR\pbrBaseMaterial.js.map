{"version": 3, "file": "pbrBaseMaterial.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/PBR/pbrBaseMaterial.ts"], "names": [], "mappings": ";AAAA,yDAAyD;AACzD,OAAO,EAAE,uCAAuC,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAElG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAExE,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAEpC,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAIpD,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAC/D,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAGjD,OAAO,EAAE,4BAA4B,EAAE,MAAM,8CAA8C,CAAC;AAG5F,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACpD,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;AAC7D,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAClE,OAAO,EAAE,YAAY,EAAE,MAAM,8BAA8B,CAAC;AAG5D,OAAO,EAAE,OAAO,EAAE,MAAM,kCAAkC,CAAC;AAI3D,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAGpD,OAAO,iDAAiD,CAAC;AACzD,OAAO,4BAA4B,CAAC;AACpC,OAAO,0BAA0B,CAAC;AAElC,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AACxE,OAAO,EAAE,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AAC5E,OAAO,EAAE,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AAC5E,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,0BAA0B,EAAE,MAAM,8BAA8B,CAAC;AAC1E,OAAO,EAAE,sBAAsB,EAAE,MAAM,oCAAoC,CAAC;AAC5E,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AACjF,OAAO,EACH,mBAAmB,EACnB,iBAAiB,EACjB,UAAU,EACV,YAAY,EACZ,yBAAyB,EACzB,iBAAiB,EACjB,yBAAyB,EACzB,wCAAwC,EACxC,yBAAyB,EACzB,6BAA6B,EAC7B,gCAAgC,EAChC,2BAA2B,EAC3B,iCAAiC,EACjC,uBAAuB,EACvB,yBAAyB,EACzB,qBAAqB,EACrB,0BAA0B,EAC1B,oBAAoB,EACpB,wBAAwB,EACxB,8BAA8B,GACjC,MAAM,6BAA6B,CAAC;AAErC,MAAM,yBAAyB,GAAG,EAAE,MAAM,EAAE,IAAyB,EAAE,OAAO,EAAE,IAAoC,EAAE,CAAC;AAEvH;;;GAGG;AACH,MAAM,OAAO,kBAAmB,SAAQ,eAAe;IAsMnD;;;OAGG;IACH,YAAY,kBAAuE;QAC/E,KAAK,CAAC,kBAAkB,CAAC,CAAC;QA1MvB,QAAG,GAAG,IAAI,CAAC;QAEX,gBAAW,GAAG,GAAG,CAAC;QAClB,uBAAkB,GAAG,KAAK,CAAC;QAE3B,YAAO,GAAG,KAAK,CAAC;QAChB,YAAO,GAAG,KAAK,CAAC;QAChB,YAAO,GAAG,KAAK,CAAC;QAChB,YAAO,GAAG,KAAK,CAAC;QAChB,YAAO,GAAG,KAAK,CAAC;QAChB,YAAO,GAAG,KAAK,CAAC;QAChB,QAAG,GAAG,KAAK,CAAC;QACZ,QAAG,GAAG,KAAK,CAAC;QACZ,QAAG,GAAG,KAAK,CAAC;QACZ,QAAG,GAAG,KAAK,CAAC;QACZ,QAAG,GAAG,KAAK,CAAC;QACZ,QAAG,GAAG,KAAK,CAAC;QAEZ,WAAM,GAAG,KAAK,CAAC;QACf,gBAAW,GAAG,KAAK,CAAC;QACpB,mBAAc,GAAG,CAAC,CAAC;QACnB,gBAAW,GAAG,KAAK,CAAC;QAEpB,mCAA8B,GAAG,KAAK,CAAC;QAEvC,YAAO,GAAG,KAAK,CAAC;QAChB,oBAAe,GAAG,CAAC,CAAC;QACpB,uBAAkB,GAAG,KAAK,CAAC;QAE3B,YAAO,GAAG,KAAK,CAAC;QAChB,gBAAW,GAAG,KAAK,CAAC;QACpB,oBAAe,GAAG,CAAC,CAAC;QACpB,eAAU,GAAG,KAAK,CAAC;QACnB,cAAS,GAAG,KAAK,CAAC;QAClB,iBAAY,GAAG,KAAK,CAAC;QACrB,eAAU,GAAG,KAAK,CAAC;QACnB,oBAAe,GAAG,KAAK,CAAC;QACxB,mBAAc,GAAG,KAAK,CAAC;QACvB,sBAAiB,GAAG,KAAK,CAAC;QAC1B,sBAAiB,GAAG,KAAK,CAAC;QAC1B,iBAAY,GAAG,KAAK,CAAC;QACrB,uBAAkB,GAAG,KAAK,CAAC;QAC3B,qBAAgB,GAAG,KAAK,CAAC;QAEzB,aAAQ,GAAG,KAAK,CAAC;QACjB,qBAAgB,GAAG,CAAC,CAAC;QACrB,kBAAa,GAAG,KAAK,CAAC;QAEtB,iBAAY,GAAG,KAAK,CAAC;QACrB,uBAAkB,GAAG,KAAK,CAAC;QAC3B,yBAAoB,GAAG,CAAC,CAAC;QACzB,iBAAY,GAAG,KAAK,CAAC;QAErB,oCAA+B,GAAG,KAAK,CAAC;QACxC,0BAAqB,GAAG,KAAK,CAAC;QAC9B,yBAAoB,GAAG,KAAK,CAAC;QAC7B,oBAAe,GAAG,KAAK,CAAC;QACxB,4BAAuB,GAAG,CAAC,CAAC;QAE5B,qBAAgB,GAAG,KAAK,CAAC;QACzB,kCAA6B,GAAG,KAAK,CAAC;QACtC,kCAA6B,GAAG,KAAK,CAAC;QACtC,kCAA6B,GAAG,KAAK,CAAC;QACtC,yBAAoB,GAAG,KAAK,CAAC;QAC7B,yBAAoB,GAAG,KAAK,CAAC;QAC7B,+BAA0B,GAAG,KAAK,CAAC;QACnC,iCAA4B,GAAG,CAAC,CAAC;QACjC,wCAAmC,GAAG,KAAK,CAAC;QAC5C,gBAAW,GAAG,KAAK,CAAC;QACpB,sBAAiB,GAAG,KAAK,CAAC;QAC1B,wBAAmB,GAAG,CAAC,CAAC;QAExB,oBAAe,GAAG,KAAK,CAAC;QACxB,yBAAoB,GAAG,KAAK,CAAC;QAE7B,WAAM,GAAG,KAAK,CAAC;QACf,YAAO,GAAG,KAAK,CAAC;QAChB,SAAI,GAAG,KAAK,CAAC;QACb,iBAAY,GAAG,CAAC,CAAC;QACjB,0BAAqB,GAAG,KAAK,CAAC;QAC9B,aAAQ,GAAG,KAAK,CAAC;QACjB,iBAAY,GAAG,KAAK,CAAC;QACrB,sBAAiB,GAAG,KAAK,CAAC;QAC1B,kBAAa,GAAG,IAAI,CAAC;QAErB,aAAQ,GAAG,KAAK,CAAC;QACjB,qBAAgB,GAAG,CAAC,CAAC;QACrB,2BAAsB,GAAG,KAAK,CAAC;QAC/B,kBAAa,GAAG,KAAK,CAAC;QACtB,iBAAY,GAAG,KAAK,CAAC;QAErB,eAAU,GAAG,KAAK,CAAC;QACnB,qBAAgB,GAAG,KAAK,CAAC;QACzB,4BAAuB,GAAG,KAAK,CAAC;QAChC,yBAAoB,GAAG,KAAK,CAAC;QAC7B,wBAAmB,GAAG,KAAK,CAAC;QAC5B,kCAA6B,GAAG,KAAK,CAAC;QACtC,6BAAwB,GAAG,KAAK,CAAC;QACjC,yBAAoB,GAAG,KAAK,CAAC;QAC7B,2BAAsB,GAAG,KAAK,CAAC;QAC/B,kCAA6B,GAAG,KAAK,CAAC;QACtC,wCAAmC,GAAG,KAAK,CAAC;QAC5C,gDAA2C,GAAG,KAAK,CAAC;QACpD,mBAAc,GAAG,KAAK,CAAC;QACvB,kCAA6B,GAAG,KAAK,CAAC;QACtC,qBAAgB,GAAG,KAAK,CAAC;QACzB,yBAAoB,GAAG,KAAK,CAAC;QAC7B,4BAAuB,GAAG,KAAK,CAAC;QAChC,yBAAoB,GAAG,KAAK,CAAC;QAC7B,oBAAe,GAAG,KAAK,CAAC;QACxB,mBAAc,GAAG,KAAK,CAAC;QACvB,6BAAwB,GAAG,KAAK,CAAC;QACjC,sBAAiB,GAAG,KAAK,CAAC;QAC1B,qBAAgB,GAAG,KAAK,CAAC;QAEzB,cAAS,GAAG,KAAK,CAAC;QAClB,mBAAc,GAAG,KAAK,CAAC;QACvB,mBAAc,GAAG,KAAK,CAAC;QAEvB,YAAO,GAAG,KAAK,CAAC;QAChB,uBAAkB,GAAG,KAAK,CAAC;QAC3B,6BAAwB,GAAG,CAAC,CAAC,CAAC;QAC9B,wBAAmB,GAAG,KAAK,CAAC;QAC5B,8BAAyB,GAAG,CAAC,CAAC,CAAC;QAC/B,kBAAa,GAAG,KAAK,CAAC;QACtB,wBAAmB,GAAG,CAAC,CAAC,CAAC;QACzB,mBAAc,GAAG,KAAK,CAAC;QACvB,yBAAoB,GAAG,CAAC,CAAC,CAAC;QAC1B,8BAAyB,GAAG,KAAK,CAAC;QAClC,qBAAgB,GAAG,KAAK,CAAC;QACzB,2BAAsB,GAAG,CAAC,CAAC,CAAC;QAC5B,qBAAgB,GAAG,KAAK,CAAC;QACzB,2BAAsB,GAAG,CAAC,CAAC,CAAC;QAC5B,yBAAoB,GAAG,KAAK,CAAC;QAC7B,+BAA0B,GAAG,CAAC,CAAC,CAAC;QAChC,oBAAe,GAAG,CAAC,CAAC;QAEpB,yBAAoB,GAAG,CAAC,CAAC;QACzB,iBAAY,GAAG,CAAC,CAAC;QACjB,gBAAW,GAAG,KAAK,CAAC;QACpB,2BAAsB,GAAG,KAAK,CAAC;QAE/B,sBAAiB,GAAG,KAAK,CAAC;QAE1B,iBAAY,GAAG,KAAK,CAAC;QACrB,wBAAmB,GAAG,KAAK,CAAC;QAC5B,yBAAoB,GAAG,KAAK,CAAC;QAC7B,oBAAe,GAAG,KAAK,CAAC;QACxB,0BAAqB,GAAG,CAAC,CAAC;QAC1B,yBAAoB,GAAG,KAAK,CAAC;QAE7B,oBAAe,GAAG,KAAK,CAAC;QACxB,aAAQ,GAAG,KAAK,CAAC;QACjB,8BAAyB,GAAG,KAAK,CAAC;QAClC,4BAAuB,GAAG,KAAK,CAAC;QAChC,gBAAW,GAAG,KAAK,CAAC;QACpB,qBAAgB,GAAG,KAAK,CAAC;QACzB,aAAQ,GAAG,KAAK,CAAC;QACjB,gBAAW,GAAG,KAAK,CAAC;QACpB,iBAAY,GAAG,KAAK,CAAC;QACrB,mBAAc,GAAG,KAAK,CAAC;QACvB,wBAAmB,GAAG,KAAK,CAAC;QAC5B,oBAAe,GAAG,KAAK,CAAC;QACxB,WAAM,GAAG,KAAK,CAAC;QACf,+BAA0B,GAAG,KAAK,CAAC;QACnC,wBAAmB,GAAG,KAAK,CAAC;QAC5B,aAAQ,GAAG,KAAK,CAAC;QACjB,cAAS,GAAG,KAAK,CAAC;QAClB,mCAA8B,GAAG,KAAK,CAAC;QACvC,0CAAqC,GAAG,KAAK,CAAC;QAE9C,4BAAuB,GAAG,KAAK,CAAC;QAChC,wBAAmB,GAAG,KAAK,CAAC;QAC5B,qBAAgB,GAAG,KAAK,CAAC;QACzB,gBAAW,GAAG,KAAK,CAAC;QACpB,cAAS,GAAG,KAAK,CAAC;QAClB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,cAAS,GAAG,KAAK,CAAC;QAClB,QAAG,GAAG,KAAK,CAAC;QACZ,qBAAgB,GAAG,KAAK,CAAC;QACzB,wBAAmB,GAAG,KAAK,CAAC;QAC5B,uBAAkB,GAAG,KAAK,CAAC;QAE3B,uBAAkB,GAAG,KAAK,CAAC;QAE3B,eAAU,GAAG,KAAK,CAAC;QAEnB,UAAK,GAAG,KAAK,CAAC;QAEd,uBAAkB,GAAG,KAAK,CAAC;QAE3B,cAAS,GAAG,CAAC,CAAC;QAQjB,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,KAAK;QACR,KAAK,CAAC,KAAK,EAAE,CAAC;QACd,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;QAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC9B,CAAC;CACJ;AAED;;;;;;GAMG;AACH,MAAM,OAAgB,eAAgB,SAAQ,YAAY;IAybtD;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IACD,IAAW,iBAAiB,CAAC,CAAU;QACnC,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IAC1D,CAAC;IAGD;;OAEG;IACH,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IACD,IAAW,wBAAwB,CAAC,CAAS;QACzC,IAAI,CAAC,yBAAyB,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC;IAChB,CAAC;IA2BD;;;OAGG;IACO,mCAAmC,CAAC,aAAqD;QAC/F,IAAI,aAAa,KAAK,IAAI,CAAC,6BAA6B,EAAE;YACtD,OAAO;SACV;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,6BAA6B,IAAI,IAAI,CAAC,wBAAwB,EAAE;YACrE,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;SAC/F;QAED,0CAA0C;QAC1C,IAAI,CAAC,aAAa,EAAE;YAChB,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,4BAA4B,CAAC;SACrF;aAAM;YACH,IAAI,CAAC,6BAA6B,GAAG,aAAa,CAAC;SACtD;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACpC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC3F,IAAI,CAAC,uCAAuC,EAAE,CAAC;YACnD,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IA4FD;;;;;OAKG;IACH,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAjkBvB;;;;WAIG;QACI,qBAAgB,GAAW,GAAG,CAAC;QAEtC;;;;WAIG;QACI,uBAAkB,GAAW,GAAG,CAAC;QAExC;;;;WAIG;QACI,0BAAqB,GAAW,GAAG,CAAC;QAE3C;;;;WAIG;QACI,uBAAkB,GAAW,GAAG,CAAC;QAExC;;WAEG;QACK,mBAAc,GAAY,IAAI,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAEnJ;;;WAGG;QACI,oBAAe,GAAY,KAAK,CAAC;QAExC;;;WAGG;QACI,mBAAc,GAA0B,IAAI,CAAC;QAEpD;;;WAGG;QACI,oBAAe,GAA0B,IAAI,CAAC;QAErD;;;WAGG;QACI,4BAAuB,GAAW,GAAG,CAAC;QAE7C;;;;;WAKG;QACI,4CAAuC,GAAW,eAAe,CAAC,+BAA+B,CAAC;QAEzG;;;WAGG;QACI,oBAAe,GAA0B,IAAI,CAAC;QAErD;;;WAGG;QACI,uBAAkB,GAA0B,IAAI,CAAC;QAExD;;;WAGG;QACI,qBAAgB,GAA0B,IAAI,CAAC;QAEtD;;;WAGG;QACI,yBAAoB,GAA0B,IAAI,CAAC;QAE1D;;;WAGG;QACI,qBAAgB,GAA0B,IAAI,CAAC;QAEtD;;;;WAIG;QACI,cAAS,GAAqB,IAAI,CAAC;QAE1C;;;;WAIG;QACI,eAAU,GAAqB,IAAI,CAAC;QAE3C;;;;;;;;;WASG;QACI,sBAAiB,GAAG,CAAC,CAAC;QAE7B;;;;;;;;;WASG;QACI,8BAAyB,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAElD;;;;WAIG;QACI,mDAA8C,GAAG,KAAK,CAAC;QAE9D;;;;WAIG;QACI,gCAA2B,GAA0B,IAAI,CAAC;QAEjE;;;;;;WAMG;QACI,wBAAmB,GAA0B,IAAI,CAAC;QAEzD;;;;WAIG;QACI,yBAAoB,GAA0B,IAAI,CAAC;QAE1D;;;WAGG;QACI,iBAAY,GAA0B,IAAI,CAAC;QAElD;;;WAGG;QACI,qBAAgB,GAA0B,IAAI,CAAC;QAEtD;;;WAGG;QACI,kBAAa,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3C;;;WAGG;QACI,iBAAY,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1C;;;WAGG;QACI,uBAAkB,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEhD;;;WAGG;QACI,qBAAgB,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE9C;;;WAGG;QACI,mBAAc,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE5C;;;WAGG;QACI,kBAAa,GAAG,GAAG,CAAC;QAE3B;;;WAGG;QACI,4BAAuB,GAAG,KAAK,CAAC;QAEvC;;;;WAIG;QACI,yBAAoB,GAAG,IAAI,CAAC;QAEnC;;;;WAIG;QACI,0BAAqB,GAAG,IAAI,CAAC;QAEpC;;;WAGG;QACI,+BAA0B,GAAG,KAAK,CAAC;QAE1C;;;;WAIG;QACI,0BAAqB,GAAG,IAAI,CAAC;QAEpC;;;WAGG;QACI,6CAAwC,GAAG,KAAK,CAAC;QAExD;;;WAGG;QACI,0CAAqC,GAAG,IAAI,CAAC;QAEpD;;;WAGG;QACI,0CAAqC,GAAG,KAAK,CAAC;QAErD;;;WAGG;QACI,0CAAqC,GAAG,KAAK,CAAC;QAErD;;;WAGG;QACI,+CAA0C,GAAG,KAAK,CAAC;QAE1D;;;WAGG;QACI,2BAAsB,GAAG,KAAK,CAAC;QAEtC;;;;WAIG;QACI,4CAAuC,GAAG,KAAK,CAAC;QAEvD;;;;WAIG;QACI,kBAAa,GAAG,eAAe,CAAC,qBAAqB,CAAC;QAE7D;;;;WAIG;QACI,0BAAqB,GAAG,IAAI,CAAC;QAEpC;;;WAGG;QACI,6BAAwB,GAAG,KAAK,CAAC;QAExC;;;WAGG;QACI,iBAAY,GAAG,KAAK,CAAC;QAE5B;;;WAGG;QACI,0BAAqB,GAAG,KAAK,CAAC;QAErC;;;WAGG;QACI,uBAAkB,GAAG,IAAI,CAAC;QAEjC;;;WAGG;QACI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;;WAGG;QACI,2BAAsB,GAAG,CAAC,CAAC;QAElC;;;WAGG;QACI,sBAAiB,GAAG,KAAK,CAAC;QAEjC;;;WAGG;QACI,sBAAiB,GAAG,KAAK,CAAC;QAEjC;;;WAGG;QACI,sBAAiB,GAAG,KAAK,CAAC;QAEjC;;;WAGG;QACI,iBAAY,GAAG,GAAG,CAAC;QAE1B;;;WAGG;QACI,oBAAe,GAAG,KAAK,CAAC;QAE/B;;;;WAIG;QACI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;;;WAIG;QACI,2BAAsB,GAAG,KAAK,CAAC;QAEtC;;;;;WAKG;QACI,4BAAuB,GAA0B,IAAI,CAAC;QAE7D;;;WAGG;QACI,+BAA0B,GAAG,KAAK,CAAC;QAElC,uBAAkB,GAAY,KAAK,CAAC;QAYpC,8BAAyB,GAAW,SAAS,CAAC,6BAA6B,CAAC;QAmBpF;;;WAGG;QACI,wBAAmB,GAAG,KAAK,CAAC;QAEnC;;;;;WAKG;QACI,gCAA2B,GAAG,KAAK,CAAC;QAQ3C;;WAEG;QACK,6BAAwB,GAAqD,IAAI,CAAC;QA+B1F;;WAEG;QACK,mBAAc,GAAG,IAAI,UAAU,CAAsB,EAAE,CAAC,CAAC;QAEjE;;WAEG;QACK,wBAAmB,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAElD;;WAEG;QACK,WAAM,GAAG,KAAK,CAAC;QAEvB;;WAEG;QACK,iCAA4B,GAAG,KAAK,CAAC;QAErC,eAAU,GAAG,CAAC,CAAC;QACvB;;;;;WAKG;QAEI,cAAS,GAAG,CAAC,CAAC;QAErB;;;;;;;WAOG;QACI,eAAU,GAAG,CAAC,CAAC,CAAC;QAEvB;;;;;WAKG;QACI,gBAAW,GAAG,CAAC,CAAC;QA0Cb,kCAA6B,GAAG,KAAK,CAAC;QAW5C,IAAI,CAAC,IAAI,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,yBAAyB,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,WAAW,GAAG,IAAI,2BAA2B,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,CAAC,UAAU,GAAG,IAAI,2BAA2B,CAAC,IAAI,CAAC,CAAC;QACxD,IAAI,CAAC,KAAK,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,0BAA0B,CAAC,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,IAAI,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAElD,2DAA2D;QAC3D,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC,uBAAuB,GAAG,GAAoC,EAAE;YACjE,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAE5B,IAAI,aAAa,CAAC,wBAAwB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE;gBAC7G,IAAI,CAAC,cAAc,CAAC,IAAI,CAAsB,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC1E;YAED,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;YACpD,IAAI,CAAC,4CAA4C,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEnE,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B,CAAC,CAAC;QAEF,IAAI,CAAC,uBAAuB,GAAG,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,IAAW,uBAAuB;QAC9B,IAAI,aAAa,CAAC,wBAAwB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE;YAC7G,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAc,qBAAqB;QAC/B,OAAO,CACH,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,kBAAkB;YAC7D,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,qBAAqB;YAChE,IAAI,CAAC,UAAU,EAAE,oBAAoB,CACxC,CAAC;IACN,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,IAAI,IAAI,CAAC,gCAAgC,EAAE,CAAC;IACvG,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,oBAAoB,EAAE;YACvC,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,IAAI,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,qBAAqB,CAAC,CAAC;IAC3I,CAAC;IAED;;OAEG;IACO,gCAAgC;QACtC,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,kBAAkB,CAAC;IAC3K,CAAC;IAED;;OAEG;IACO,gBAAgB;QACtB,OAAO,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC;IACzG,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAAE,YAAsB;QACjF,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACjC,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;QAEzC,IAAI,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YACrC,IAAI,WAAW,CAAC,mBAAmB,IAAI,WAAW,CAAC,4BAA4B,KAAK,YAAY,EAAE;gBAC9F,OAAO,IAAI,CAAC;aACf;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC1B,IAAI,CAAC,2BAA2B,CAAC,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACtF,OAAO,CAAC,eAAe,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;SACjF;QAED,MAAM,OAAO,GAAuB,OAAO,CAAC,eAAe,CAAC;QAC5D,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC;SACf;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,IAAI,CAAC,UAAU,CAAC,uBAAuB,GAAG,KAAK,CAAC;YAChD,IAAI,CAAC,2CAA2C,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClE,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC;YAC7E,IAAI,KAAK,CAAC,eAAe,EAAE;gBACvB,IAAI,IAAI,CAAC,cAAc,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBAC5D,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE,EAAE;wBAC7C,OAAO,KAAK,CAAC;qBAChB;iBACJ;gBAED,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBAC7D,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE,EAAE;wBAC9C,OAAO,KAAK,CAAC;qBAChB;iBACJ;gBAED,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBAC7D,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE,EAAE;wBAC9C,OAAO,KAAK,CAAC;qBAChB;iBACJ;gBAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBACvD,IAAI,iBAAiB,IAAI,aAAa,CAAC,wBAAwB,EAAE;oBAC7D,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE;wBAC3C,OAAO,KAAK,CAAC;qBAChB;oBACD,IAAI,iBAAiB,CAAC,iBAAiB,EAAE;wBACrC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE;4BAC7D,OAAO,KAAK,CAAC;yBAChB;qBACJ;yBAAM;wBACH,2CAA2C;wBAC3C,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,IAAI,iBAAiB,CAAC,kBAAkB,EAAE,EAAE,2BAA2B,EAAE;4BAC/G,OAAO,KAAK,CAAC;yBAChB;qBACJ;iBACJ;gBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,aAAa,CAAC,sBAAsB,EAAE;oBAC/D,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,EAAE;wBAC/C,OAAO,KAAK,CAAC;qBAChB;iBACJ;gBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,aAAa,CAAC,sBAAsB,EAAE;oBAC/D,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,EAAE;wBAC/C,OAAO,KAAK,CAAC;qBAChB;iBACJ;gBAED,IAAI,aAAa,CAAC,sBAAsB,EAAE;oBACtC,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACvB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,EAAE;4BAC/C,OAAO,KAAK,CAAC;yBAChB;qBACJ;yBAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;wBAClC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,EAAE,EAAE;4BACnD,OAAO,KAAK,CAAC;yBAChB;qBACJ;oBAED,IAAI,IAAI,CAAC,2BAA2B,EAAE;wBAClC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,oBAAoB,EAAE,EAAE;4BAC1D,OAAO,KAAK,CAAC;yBAChB;qBACJ;oBAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;wBAC1B,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,EAAE;4BAClD,OAAO,KAAK,CAAC;yBAChB;qBACJ;oBAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;wBAC3B,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,EAAE,EAAE;4BACnD,OAAO,KAAK,CAAC;yBAChB;qBACJ;iBACJ;gBAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,IAAI,CAAC,YAAY,IAAI,aAAa,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;oBACxH,uCAAuC;oBACvC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE;wBAC9B,OAAO,KAAK,CAAC;qBAChB;iBACJ;gBAED,IAAI,IAAI,CAAC,uBAAuB,IAAI,aAAa,CAAC,wBAAwB,EAAE;oBACxE,oBAAoB;oBACpB,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,EAAE;wBACzC,OAAO,KAAK,CAAC;qBAChB;iBACJ;aACJ;SACJ;QAED,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,qCAAqC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE5D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACxE,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,OAAO,EAAE,EAAE;gBAC/C,OAAO,KAAK,CAAC;aAChB;SACJ;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;YAC/F,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,uDAAuD,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;SACpF;QAED,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;QACtC,MAAM,aAAa,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACjD,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,CAAC,CAAC;QAEhJ,IAAI,0BAA0B,GAAG,KAAK,CAAC;QAEvC,IAAI,MAAM,EAAE;YACR,IAAI,IAAI,CAAC,0BAA0B,EAAE;gBACjC,yBAAyB,CAAC,MAAM,GAAG,MAAM,CAAC;gBAC1C,yBAAyB,CAAC,OAAO,GAAG,OAAO,CAAC;gBAC5C,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC;aAC9E;YAED,iDAAiD;YACjD,IAAI,IAAI,CAAC,sBAAsB,IAAI,cAAc,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;gBACpE,MAAM,GAAG,cAAc,CAAC;gBACxB,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAE5B,0BAA0B,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAE3C,IAAI,aAAa,EAAE;oBACf,oDAAoD;oBACpD,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC;oBAClC,OAAO,KAAK,CAAC;iBAChB;aACJ;iBAAM;gBACH,KAAK,CAAC,mBAAmB,EAAE,CAAC;gBAC5B,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAC7D;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;YAC9C,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,WAAW,CAAC,mBAAmB,GAAG,0BAA0B,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5E,WAAW,CAAC,4BAA4B,GAAG,CAAC,CAAC,YAAY,CAAC;QAE1D,IAAI,CAAC,8BAA8B,EAAE,CAAC;QAEtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACrB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE;YAC5E,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,cAAc,CAClB,IAAkB,EAClB,OAA2B,EAC3B,aAAiD,IAAI,EACrD,UAA8D,IAAI,EAClE,eAAkC,IAAI,EACtC,eAAkC,IAAI,EACtC,gBAAyB;QAEzB,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;QAElF,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YAClB,OAAO,IAAI,CAAC;SACf;QAED,OAAO,CAAC,eAAe,EAAE,CAAC;QAE1B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,YAAY;QACZ,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;QACxC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,OAAO,CAAC,oBAAoB,EAAE;YAC9B,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,sBAAsB,CAAC,CAAC;SACjE;QAED,IAAI,OAAO,CAAC,GAAG,EAAE;YACb,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;SAC9C;QACD,IAAI,OAAO,CAAC,UAAU,EAAE;YACpB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;SACrD;QACD,IAAI,OAAO,CAAC,SAAS,EAAE;YACnB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;SACpD;QACD,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1B,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC;SAC3D;QACD,IAAI,OAAO,CAAC,QAAQ,EAAE;YAClB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;SACnD;QACD,IAAI,OAAO,CAAC,YAAY,EAAE;YACtB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;SACvD;QACD,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,mBAAmB,CAAC,CAAC;SAC9D;QAED,IAAI,OAAO,CAAC,eAAe,EAAE;YACzB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,iBAAiB,CAAC,CAAC;SAC5D;QAED,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,SAAS,CAAC,CAAC;SACpD;QAED,IAAI,OAAO,CAAC,IAAI,EAAE;YACd,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,MAAM,CAAC,CAAC;SACjD;QAED,YAAY,GAAG,yBAAyB,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,sBAAsB,EAAE,YAAY,EAAE,CAAC,CAAC;QAE1G,IAAI,OAAO,CAAC,YAAY,EAAE;YACtB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,cAAc,CAAC,CAAC;SACzD;QAED,IAAI,OAAO,CAAC,6BAA6B,EAAE;YACvC,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,+BAA+B,CAAC,CAAC;SAC1E;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE;YAC1B,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,kBAAkB,CAAC,CAAC;SAC7D;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE;YAClB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,UAAU,CAAC,CAAC;SACrD;QAED,IAAI,OAAO,CAAC,MAAM,EAAE;YAChB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,QAAQ,CAAC,CAAC;SACnD;QAED,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,SAAS,CAAC,CAAC;SACpD;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE;YAClB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,UAAU,CAAC,CAAC;SACrD;QAED,IAAI,OAAO,CAAC,WAAW,EAAE;YACrB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,aAAa,CAAC,CAAC;SACxD;QAED,IAAI,OAAO,CAAC,YAAY,EAAE;YACtB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,cAAc,CAAC,CAAC;SACzD;QAED,IAAI,OAAO,CAAC,SAAS,EAAE;YACnB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;SACzC;QAED,YAAY;QACZ,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAE5C,IAAI,OAAO,CAAC,MAAM,EAAE;YAChB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SACzC;QAED,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;SAC1C;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAAE;YACvD,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE;gBACnB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;aACzC;SACJ;QAED,IAAI,OAAO,CAAC,WAAW,EAAE;YACrB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;SACxC;QAED,yBAAyB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QAC7D,6BAA6B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAChD,gCAAgC,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACzD,wCAAwC,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAEjE,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,MAAM,QAAQ,GAAG;YACb,OAAO;YACP,MAAM;YACN,gBAAgB;YAChB,cAAc;YACd,aAAa;YACb,eAAe;YACf,cAAc;YACd,oBAAoB;YACpB,6BAA6B;YAC7B,gBAAgB;YAChB,YAAY;YACZ,kBAAkB;YAClB,WAAW;YACX,WAAW;YACX,WAAW;YACX,cAAc;YACd,eAAe;YACf,eAAe;YACf,kBAAkB;YAClB,qBAAqB;YACrB,iBAAiB;YACjB,gBAAgB;YAChB,oBAAoB;YACpB,0BAA0B;YAC1B,2BAA2B;YAC3B,mBAAmB;YACnB,2BAA2B;YAC3B,YAAY;YACZ,gBAAgB;YAChB,QAAQ;YACR,cAAc;YACd,eAAe;YACf,eAAe;YACf,kBAAkB;YAClB,gBAAgB;YAChB,oBAAoB;YACpB,cAAc;YACd,2BAA2B;YAC3B,YAAY;YACZ,gBAAgB;YAChB,2BAA2B;YAC3B,mBAAmB;YACnB,oBAAoB;YACpB,0BAA0B;YAC1B,aAAa;YACb,aAAa;YACb,aAAa;YACb,iBAAiB;YACjB,iBAAiB;YACjB,cAAc;YACd,cAAc;YACd,cAAc;YACd,cAAc;YACd,eAAe;YACf,gBAAgB;YAChB,eAAe;YACf,eAAe;YACf,gBAAgB;YAChB,gBAAgB;YAChB,eAAe;YACf,eAAe;YACf,eAAe;YACf,8BAA8B;YAC9B,qBAAqB;YACrB,kBAAkB;YAClB,YAAY;YACZ,wBAAwB;YACxB,2BAA2B;SAC9B,CAAC;QAEF,MAAM,QAAQ,GAAG;YACb,eAAe;YACf,qBAAqB;YACrB,gBAAgB;YAChB,iBAAiB;YACjB,aAAa;YACb,iBAAiB;YACjB,gBAAgB;YAChB,mBAAmB;YACnB,sBAAsB;YACtB,uBAAuB;YACvB,mBAAmB;YACnB,qBAAqB;YACrB,wBAAwB;YACxB,aAAa;YACb,4BAA4B;YAC5B,oBAAoB;YACpB,cAAc;YACd,iBAAiB;YACjB,sBAAsB;SACzB,CAAC;QAEF,MAAM,cAAc,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAErD,MAAM,eAAe,GAAG,EAAE,qBAAqB,EAAE,IAAI,CAAC,sBAAsB,EAAE,2BAA2B,EAAE,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAE3I,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,YAAY,CAAC;QAC5C,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACpC,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,OAAO,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACpC,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,cAAc,CAAC;QACrD,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,SAAS,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,UAAU,CAAC,eAAe,GAAG,eAAe,CAAC;QAClD,IAAI,CAAC,2BAA2B,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAErF,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC3C,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC3C,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAE/B,IAAI,4BAA4B,EAAE;YAC9B,4BAA4B,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAChE,4BAA4B,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;SACnE;QAED,8BAA8B,CAAyB;YACnD,aAAa,EAAE,QAAQ;YACvB,mBAAmB,EAAE,cAAc;YACnC,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,OAAO;YAChB,qBAAqB,EAAE,IAAI,CAAC,sBAAsB;SACrD,CAAC,CAAC;QAEH,MAAM,WAAW,GAAoC,EAAE,CAAC;QAExD,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;SAC5H;QAED,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAC9B,UAAU,EACc;YACpB,UAAU,EAAE,OAAO;YACnB,aAAa,EAAE,QAAQ;YACvB,mBAAmB,EAAE,cAAc;YACnC,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,SAAS;YACpB,UAAU,EAAE,UAAU;YACtB,OAAO,EAAE,OAAO;YAChB,eAAe;YACf,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;YAC9C,wBAAwB,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU;YACpD,WAAW,EAAE,OAAO,CAAC,OAAO;SAC/B,EACD,MAAM,CACT,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,SAAS,CAAC;QAEvC,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,eAAe,CACnB,IAAkB,EAClB,OAA2B,EAC3B,eAAkC,IAAI,EACtC,eAAkC,IAAI,EACtC,mBAA4B,KAAK;QAEjC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,SAAS;QACT,uBAAuB,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACxG,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;QAE5B,YAAY;QACZ,0BAA0B,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAE3C,UAAU;QACV,MAAM,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,+BAA+B,CAAC;QACnG,wBAAwB,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,cAAc,IAAI,CAAC,GAAG,CAAC,CAAC;QAEtE,iCAAiC;QACjC,oBAAoB,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;QAE1C,WAAW;QACX,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACrD,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;YACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAAE;gBACvD,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;aACjC;YACD,IAAI,KAAK,CAAC,eAAe,EAAE;gBACvB,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC;gBAC3B,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC;gBAC5B,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC;gBAC5B,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC;gBAC7B,OAAO,CAAC,oBAAoB,GAAG,CAAC,CAAC;gBACjC,OAAO,CAAC,uBAAuB,GAAG,CAAC,CAAC;gBACpC,OAAO,CAAC,4BAA4B,GAAG,CAAC,CAAC;gBACzC,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC;gBAChC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC;gBACzB,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC;gBAE7B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,UAAU,EAAE;oBAC7B,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;iBACvC;gBAED,IAAI,IAAI,CAAC,cAAc,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBAC5D,yBAAyB,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;oBAClE,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;iBACxD;qBAAM;oBACH,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;iBAC1B;gBAED,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBAC7D,yBAAyB,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;oBACpE,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC;iBAC5D;qBAAM;oBACH,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;iBAC3B;gBAED,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBAC7D,yBAAyB,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;oBACpE,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC;iBAC7D;qBAAM;oBACH,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;iBAC3B;gBAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBACvD,IAAI,iBAAiB,IAAI,aAAa,CAAC,wBAAwB,EAAE;oBAC7D,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;oBAC1B,OAAO,CAAC,eAAe,GAAG,iBAAiB,CAAC,UAAU,CAAC;oBACvD,OAAO,CAAC,cAAc,GAAG,iBAAiB,CAAC,MAAM,CAAC;oBAClD,OAAO,CAAC,oBAAoB,GAAG,iBAAiB,CAAC,eAAe,CAAC;oBACjE,OAAO,CAAC,wBAAwB,GAAG,iBAAiB,CAAC,iBAAiB,CAAC;oBAEvE,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,wBAAwB,GAAG,CAAC,EAAE;wBAC7D,OAAO,CAAC,WAAW,GAAG,EAAE,GAAG,IAAI,CAAC,wBAAwB,CAAC;wBACzD,IAAI,MAAM,CAAC,SAAS,CAAC,+BAA+B,EAAE;4BAClD,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,GAAG,GAAG,CAAC;yBACnD;wBAED,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC;qBACrC;yBAAM;wBACH,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;qBACtC;oBAED,OAAO,CAAC,cAAc,GAAG,iBAAiB,CAAC,eAAe,KAAK,OAAO,CAAC,aAAa,CAAC;oBACrF,OAAO,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,MAAM,CAAC;oBACpD,OAAO,CAAC,uBAAuB,GAAG,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC;oBAE5J,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;oBACpC,OAAO,CAAC,sBAAsB,GAAG,KAAK,CAAC;oBACvC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;oBACrC,OAAO,CAAC,wBAAwB,GAAG,KAAK,CAAC;oBACzC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;oBACrC,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;oBACxC,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;oBAC9C,OAAO,CAAC,mCAAmC,GAAG,KAAK,CAAC;oBACpD,OAAO,CAAC,2CAA2C,GAAG,KAAK,CAAC;oBAE5D,QAAQ,iBAAiB,CAAC,eAAe,EAAE;wBACvC,KAAK,OAAO,CAAC,aAAa;4BACtB,OAAO,CAAC,sBAAsB,GAAG,IAAI,CAAC;4BACtC,MAAM;wBACV,KAAK,OAAO,CAAC,WAAW;4BACpB,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;4BACpC,MAAM;wBACV,KAAK,OAAO,CAAC,eAAe;4BACxB,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;4BACxC,MAAM;wBACV,KAAK,OAAO,CAAC,WAAW;4BACpB,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;4BACpC,MAAM;wBACV,KAAK,OAAO,CAAC,cAAc;4BACvB,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC;4BACvC,MAAM;wBACV,KAAK,OAAO,CAAC,oBAAoB;4BAC7B,OAAO,CAAC,6BAA6B,GAAG,IAAI,CAAC;4BAC7C,MAAM;wBACV,KAAK,OAAO,CAAC,0BAA0B;4BACnC,OAAO,CAAC,mCAAmC,GAAG,IAAI,CAAC;4BACnD,MAAM;wBACV,KAAK,OAAO,CAAC,mCAAmC;4BAC5C,OAAO,CAAC,2CAA2C,GAAG,IAAI,CAAC;4BAC3D,MAAM;wBACV,KAAK,OAAO,CAAC,UAAU,CAAC;wBACxB,KAAK,OAAO,CAAC,aAAa,CAAC;wBAC3B;4BACI,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC;4BACnC,OAAO,CAAC,6BAA6B,GAAS,iBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;4BAChG,MAAM;qBACb;oBAED,IAAI,iBAAiB,CAAC,eAAe,KAAK,OAAO,CAAC,WAAW,EAAE;wBAC3D,IAAI,iBAAiB,CAAC,iBAAiB,EAAE;4BACrC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;4BAChC,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;yBACjD;wBACD,4EAA4E;6BACvE,IAAI,iBAAiB,CAAC,MAAM,EAAE;4BAC/B,OAAO,CAAC,6BAA6B,GAAG,IAAI,CAAC;4BAC7C,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;4BACjC,IAAI,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,iBAAiB,IAAI,CAAC,EAAE;gCAChI,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;6BACxC;iCAAM;gCACH,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;6BACvC;yBACJ;qBACJ;iBACJ;qBAAM;oBACH,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;oBAC3B,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;oBACjC,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;oBACxC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;oBACrC,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;oBACpC,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;oBAC9C,OAAO,CAAC,wBAAwB,GAAG,KAAK,CAAC;oBACzC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;oBACrC,OAAO,CAAC,sBAAsB,GAAG,KAAK,CAAC;oBACvC,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;oBAC9C,OAAO,CAAC,mCAAmC,GAAG,KAAK,CAAC;oBACpD,OAAO,CAAC,2CAA2C,GAAG,KAAK,CAAC;oBAC5D,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;oBAC/B,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;oBAC9C,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;oBACjC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;oBACrC,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;oBACxC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;oBACrC,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;oBAChC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;oBAC/B,OAAO,CAAC,wBAAwB,GAAG,KAAK,CAAC;iBAC5C;gBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,aAAa,CAAC,sBAAsB,EAAE;oBAC/D,yBAAyB,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;oBACtE,OAAO,CAAC,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC;oBAC9D,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;oBACzD,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;iBACvD;qBAAM;oBACH,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;iBAC5B;gBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,aAAa,CAAC,sBAAsB,EAAE;oBAC/D,yBAAyB,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;oBACtE,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;iBAC5D;qBAAM;oBACH,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;iBAC5B;gBAED,IAAI,aAAa,CAAC,sBAAsB,EAAE;oBACtC,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACvB,yBAAyB,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;wBAC1E,OAAO,CAAC,6BAA6B,GAAG,IAAI,CAAC,qCAAqC,CAAC;wBACnF,OAAO,CAAC,6BAA6B,GAAG,CAAC,IAAI,CAAC,qCAAqC,IAAI,IAAI,CAAC,qCAAqC,CAAC;wBAClI,OAAO,CAAC,6BAA6B,GAAG,IAAI,CAAC,qCAAqC,CAAC;wBACnF,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,0CAA0C,CAAC;wBAC/E,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;qBACtC;yBAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;wBAClC,yBAAyB,CAAC,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;wBAC9E,OAAO,CAAC,+BAA+B,GAAG,IAAI,CAAC,wCAAwC,CAAC;wBACxF,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,uCAAuC,CAAC;wBAC7E,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;qBACrE;yBAAM;wBACH,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;qBAChC;oBAED,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,mBAAmB,EAAE;wBAC9D,MAAM,iBAAiB,GACnB,IAAI,CAAC,2BAA2B,KAAK,IAAI;4BACzC,IAAI,CAAC,2BAA2B,CAAC,QAAQ,KAAK,IAAI,CAAC,mBAAmB,EAAE,QAAQ;4BAChF,IAAI,CAAC,2BAA2B,CAAC,2BAA2B,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;wBAE3F,OAAO,CAAC,mCAAmC,GAAG,IAAI,CAAC,8CAA8C,IAAI,CAAC,iBAAiB,CAAC;wBACxH,IAAI,IAAI,CAAC,2BAA2B,EAAE;4BAClC,yBAAyB,CAAC,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,sBAAsB,CAAC,CAAC;4BAC7F,OAAO,CAAC,0BAA0B,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC;yBACpF;6BAAM;4BACH,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;yBACxC;wBACD,IACI,IAAI,CAAC,mBAAmB;4BACxB,CAAC,iBAAiB;4BAClB,CAAC,CAAC,IAAI,CAAC,2BAA2B,IAAI,CAAC,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,8CAA8C,CAAC,CAAC,EAClI;4BACE,yBAAyB,CAAC,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;4BAC5E,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC;yBACnE;6BAAM;4BACH,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;yBAC/B;qBACJ;yBAAM;wBACH,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;wBACrC,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;qBAC/B;oBAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;wBAC3B,yBAAyB,CAAC,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;qBACpF;yBAAM;wBACH,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;qBACnC;iBACJ;qBAAM;oBACH,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC7B,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;iBACnC;gBAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,IAAI,CAAC,YAAY,IAAI,aAAa,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;oBACxH,yBAAyB,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;oBAE9D,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,cAAc,IAAI,aAAa,CAAC,qBAAqB,EAAE;wBACjF,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;wBACxB,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,oBAAoB,CAAC;wBAClD,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC;qBAC5D;yBAAM;wBACH,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;qBAC5B;oBAED,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,wBAAwB,CAAC;iBACjE;qBAAM;oBACH,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC;oBACrB,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACzB,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC7B,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBAClC,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC;iBACzC;gBAED,IAAI,IAAI,CAAC,uBAAuB,IAAI,aAAa,CAAC,wBAAwB,EAAE;oBACxE,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC/B,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;iBACtE;qBAAM;oBACH,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;oBAChC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;iBACxC;gBAED,IAAI,IAAI,CAAC,gCAAgC,EAAE,EAAE;oBACzC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;iBAClC;qBAAM;oBACH,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;iBACnC;aACJ;YAED,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAEvD,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,qBAAqB,EAAE;gBAC9D,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;gBACxC,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;aACvC;iBAAM,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,iBAAiB,EAAE;gBACjE,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;gBACxC,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC;aACtC;iBAAM;gBACH,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC;gBACvC,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;aACvC;YAED,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAEvD,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACjD,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;aACnC;iBAAM;gBACH,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;aACpC;YAED,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,IAAI,CAAC,2BAA2B,CAAC;SACjG;QAED,IAAI,OAAO,CAAC,iBAAiB,IAAI,OAAO,CAAC,aAAa,EAAE;YACpD,OAAO,CAAC,cAAc,GAAG,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YACzF,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,mBAAmB,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,8BAA8B,CAAC;YAC3I,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;YACzD,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,sBAAsB,CAAC;YAC5E,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC;SAC5D;QAED,IAAI,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACxE,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;SAC9D;QAED,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAEtD,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAEvD,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAErD,QAAQ;QACR,IAAI,OAAO,CAAC,aAAa,EAAE;YACvB,qBAAqB,CACjB,IAAI,EACJ,KAAK,EACL,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,EACzD,OAAO,EACP,IAAI,CAAC,4BAA4B,CACpC,CAAC;YACF,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;YAC9H,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;SACvC;QAED,kDAAkD;QAClD,iCAAiC,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;QAE7H,kBAAkB;QAClB,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,kDAAkD,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEzE,UAAU;QACV,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,kBAAkB,CAAC,CAAC;QAE5H,kBAAkB;QAClB,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,IAAkB,EAAE,UAAyC,EAAE,OAA8C;QACjI,MAAM,YAAY,GAAG;YACjB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,KAAK;YACnB,GAAG,OAAO;SACb,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACjC,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;QAED,IAAI,CAAC,2BAA2B,CAAC,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACtF,MAAM,OAAO,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QACpE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAE,CAAC;QACnJ,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,yBAAyB,CAAC,MAAM,GAAG,MAAM,CAAC;YAC1C,yBAAyB,CAAC,OAAO,GAAG,IAAI,CAAC;YACzC,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC;SAC9E;QACD,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE;YAClB,IAAI,UAAU,EAAE;gBACZ,UAAU,CAAC,IAAI,CAAC,CAAC;aACpB;SACJ;aAAM;YACH,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAChC,IAAI,UAAU,EAAE;oBACZ,UAAU,CAAC,IAAI,CAAC,CAAC;iBACpB;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACI,kBAAkB;QACrB,uBAAuB;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;QAChC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAClC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;QACxC,GAAG,CAAC,UAAU,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC;QAC/C,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QACtC,GAAG,CAAC,UAAU,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;QAC9C,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;QACzC,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAChC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;QACzC,GAAG,CAAC,UAAU,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC;QAChD,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;QACzC,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;QAEvC,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QACtC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAClC,GAAG,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;QAExC,GAAG,CAAC,UAAU,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;QAClD,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAC/B,GAAG,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;QACxC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QAEnC,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAEhC,GAAG,CAAC,UAAU,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;QACjD,GAAG,CAAC,UAAU,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC;QAC/C,GAAG,CAAC,UAAU,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC;QAChD,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QACvC,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;QAExC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QAEnC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAClC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAClC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAClC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAElC,KAAK,CAAC,kBAAkB,EAAE,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,KAAa,EAAE,IAAU,EAAE,OAAgB;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,MAAM,OAAO,GAAuB,OAAO,CAAC,eAAe,CAAC;QAC5D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO;SACV;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAE9B,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QAED,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,iBAAiB;QACjB,IAAI,CAAC,oBAAoB,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACzD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE7B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,0BAA0B;QAC1B,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAErD,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhG,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,sCAAsC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE7D,gBAAgB;QAChB,IAAI,OAAO,CAAC,qBAAqB,EAAE;YAC/B,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACjD;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAE7E,QAAQ;QACR,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAEzE,IAAI,iBAAiB,GAA0B,IAAI,CAAC;QACpD,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;QAChC,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAChC,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAEjD,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,OAAO,CAAC,YAAY,CAAC,sBAAsB,EAAE;gBAC7F,mBAAmB;gBACnB,IAAI,KAAK,CAAC,eAAe,EAAE;oBACvB,IAAI,IAAI,CAAC,cAAc,IAAI,aAAa,CAAC,qBAAqB,EAAE;wBAC5D,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;wBAClG,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;qBACzD;oBAED,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;wBAC7D,GAAG,CAAC,YAAY,CACZ,eAAe,EACf,IAAI,CAAC,eAAe,CAAC,gBAAgB,EACrC,IAAI,CAAC,eAAe,CAAC,KAAK,EAC1B,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,uCAAuC,CAC/C,CAAC;wBACF,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;qBAC3D;oBAED,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;wBAC7D,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;wBACrG,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;qBAC3D;oBAED,IAAI,iBAAiB,IAAI,aAAa,CAAC,wBAAwB,EAAE;wBAC7D,GAAG,CAAC,YAAY,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,0BAA0B,EAAE,CAAC,CAAC;wBACrF,GAAG,CAAC,YAAY,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;wBAEjE,IAAU,iBAAkB,CAAC,eAAe,EAAE;4BAC1C,MAAM,WAAW,GAAgB,iBAAiB,CAAC;4BAEnD,GAAG,CAAC,aAAa,CAAC,qBAAqB,EAAE,WAAW,CAAC,mBAAmB,CAAC,CAAC;4BAC1E,GAAG,CAAC,aAAa,CAAC,iBAAiB,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;yBACrE;wBAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;4BACxB,MAAM,KAAK,GAAG,iBAAiB,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;4BAChD,GAAG,CAAC,YAAY,CAAC,0BAA0B,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;yBAC3E;wBAED,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;4BAC3B,MAAM,WAAW,GAAG,iBAAiB,CAAC,mBAAmB,CAAC;4BAC1D,IAAI,OAAO,CAAC,6BAA6B,IAAI,WAAW,EAAE;gCACtD,IAAI,OAAO,CAAC,mBAAmB,EAAE;oCAC7B,MAAM,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,CAAC;oCAC1D,GAAG,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;oCAC3D,GAAG,CAAC,aAAa,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;oCAC7D,GAAG,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;oCAC3D,GAAG,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;oCAC3D,GAAG,CAAC,aAAa,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;oCAC7D,GAAG,CAAC,aAAa,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;oCAC7D,GAAG,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;oCAC3D,GAAG,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;oCAC3D,GAAG,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;iCAC9D;qCAAM;oCACH,GAAG,CAAC,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oCACnF,GAAG,CAAC,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oCACnF,GAAG,CAAC,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oCACnF,GAAG,CAAC,YAAY,CACZ,iBAAiB,EACjB,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EACnC,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EACnC,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CACtC,CAAC;oCACF,GAAG,CAAC,YAAY,CACZ,iBAAiB,EACjB,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EACnC,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EACnC,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CACtC,CAAC;oCACF,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oCACvF,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oCACvF,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oCACvF,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;iCAC1F;6BACJ;yBACJ;wBAED,GAAG,CAAC,YAAY,CACZ,8BAA8B,EAC9B,iBAAiB,CAAC,OAAO,EAAE,CAAC,KAAK,EACjC,iBAAiB,CAAC,kBAAkB,EACpC,iBAAiB,CAAC,mBAAmB,CACxC,CAAC;qBACL;oBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,aAAa,CAAC,sBAAsB,EAAE;wBAC/D,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;wBACxG,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;qBAC7D;oBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,aAAa,CAAC,sBAAsB,EAAE;wBAC/D,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;wBACxG,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;qBAC7D;oBAED,IAAI,aAAa,CAAC,sBAAsB,EAAE;wBACtC,IAAI,IAAI,CAAC,gBAAgB,EAAE;4BACvB,GAAG,CAAC,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;4BAC1I,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;yBACjE;6BAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;4BAClC,GAAG,CAAC,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;4BACzH,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;yBACrE;wBAED,IAAI,IAAI,CAAC,2BAA2B,EAAE;4BAClC,GAAG,CAAC,YAAY,CAAC,2BAA2B,EAAE,IAAI,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;4BACzI,iBAAiB,CAAC,IAAI,CAAC,2BAA2B,EAAE,GAAG,EAAE,qBAAqB,CAAC,CAAC;yBACnF;wBAED,IAAI,IAAI,CAAC,mBAAmB,IAAI,OAAO,CAAC,WAAW,EAAE;4BACjD,GAAG,CAAC,YAAY,CAAC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;4BACjH,iBAAiB,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;yBACnE;wBAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;4BAC3B,GAAG,CAAC,YAAY,CAAC,2BAA2B,EAAE,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;4BAC3H,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,EAAE,GAAG,EAAE,qBAAqB,CAAC,CAAC;yBAC5E;qBACJ;oBAED,IAAI,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,aAAa,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;wBACxH,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBACrH,iBAAiB,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;wBAElD,IAAI,KAAK,CAAC,uBAAuB,EAAE;4BAC/B,GAAG,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;yBACrH;6BAAM;4BACH,GAAG,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;yBACrH;qBACJ;iBACJ;gBAED,aAAa;gBACb,IAAI,IAAI,CAAC,WAAW,EAAE;oBAClB,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;iBAChD;gBAED,SAAS;gBACT,IAAI,OAAO,CAAC,gBAAgB,EAAE;oBAC1B,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;oBACrG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxG,GAAG,CAAC,YAAY,CAAC,oBAAoB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAE/D,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,kBAAkB,IAAI,GAAG,CAAC;oBACvD,MAAM,UAAU,GAAG,CAAC,CAAC,CAAC,yEAAyE;oBAE/F,8FAA8F;oBAC9F,mDAAmD;oBACnD,mBAAmB;oBACnB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;oBAEhE,wDAAwD;oBACxD,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5F,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC;oBAE3C,GAAG,CAAC,YAAY,CAAC,6BAA6B,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;iBACrF;qBAAM;oBACH,GAAG,CAAC,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;iBACvF;gBAED,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,aAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;gBACtH,GAAG,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC5D,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,UAAU,EAAE,+BAA+B,EAAE;oBAC5E,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;iBAC1D;qBAAM;oBACH,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;iBACnE;gBAED,OAAO;gBACP,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;gBAC9C,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAChD,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC;gBAChF,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAEhD,GAAG,CAAC,aAAa,CAAC,oBAAoB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAE7D,SAAS;gBACT,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAE/E,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAE5D,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;aACrE;YAED,WAAW;YACX,IAAI,KAAK,CAAC,eAAe,EAAE;gBACvB,IAAI,IAAI,CAAC,cAAc,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBAC5D,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;iBACxD;gBAED,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBAC7D,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;iBAC1D;gBAED,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBAC7D,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;iBAC1D;gBAED,IAAI,iBAAiB,IAAI,aAAa,CAAC,wBAAwB,EAAE;oBAC7D,IAAI,OAAO,CAAC,oBAAoB,EAAE;wBAC9B,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;qBAC1D;yBAAM;wBACH,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,cAAc,IAAI,iBAAiB,CAAC,CAAC;wBAC3F,GAAG,CAAC,UAAU,CAAC,sBAAsB,EAAE,iBAAiB,CAAC,cAAc,IAAI,iBAAiB,CAAC,CAAC;wBAC9F,GAAG,CAAC,UAAU,CAAC,uBAAuB,EAAE,iBAAiB,CAAC,eAAe,IAAI,iBAAiB,CAAC,CAAC;qBACnG;oBAED,IAAI,OAAO,CAAC,gBAAgB,EAAE;wBAC1B,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;qBAC5E;iBACJ;gBAED,IAAI,OAAO,CAAC,eAAe,EAAE;oBACzB,GAAG,CAAC,UAAU,CAAC,wBAAwB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;iBAC1E;gBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,aAAa,CAAC,sBAAsB,EAAE;oBAC/D,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;iBAC5D;gBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,aAAa,CAAC,sBAAsB,EAAE;oBAC/D,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;iBAC5D;gBAED,IAAI,aAAa,CAAC,sBAAsB,EAAE;oBACtC,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACvB,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;qBAChE;yBAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;wBAClC,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;qBACpE;oBAED,IAAI,IAAI,CAAC,2BAA2B,EAAE;wBAClC,GAAG,CAAC,UAAU,CAAC,4BAA4B,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;qBAClF;oBAED,IAAI,IAAI,CAAC,mBAAmB,IAAI,OAAO,CAAC,WAAW,EAAE;wBACjD,GAAG,CAAC,UAAU,CAAC,oBAAoB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;qBAClE;oBAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;wBAC3B,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;qBACpE;iBACJ;gBAED,IAAI,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,aAAa,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;oBACxH,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;iBACpD;aACJ;YAED,yBAAyB;YACzB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,+BAA+B,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE;gBACxF,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACtD;YAED,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;YAClC,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEzD,aAAa;YACb,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAE/C,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SAChC;aAAM,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,8BAA8B,EAAE;YACnE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACnC;QAED,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC9B,SAAS;YACT,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC/C,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;aACrF;YAED,OAAO;YACP,IACI,CAAC,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,YAAY,CAAC;gBAC3E,iBAAiB;gBACjB,IAAI,CAAC,UAAU,CAAC,iBAAiB;gBACjC,IAAI,CAAC,cAAc;gBACnB,OAAO,CAAC,OAAO,EACjB;gBACE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;aACzB;YAED,MAAM;YACN,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YAEzD,gBAAgB;YAChB,IAAI,OAAO,CAAC,qBAAqB,EAAE;gBAC/B,yBAAyB,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;aACvD;YAED,IAAI,OAAO,CAAC,8BAA8B,EAAE;gBACxC,IAAI,CAAC,2BAA2B,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;aACrE;YAED,mBAAmB;YACnB,IAAI,CAAC,6BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE7D,aAAa;YACb,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAEnD,GAAG,CAAC,MAAM,EAAE,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,cAAc;QACjB,MAAM,OAAO,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;QAEvC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACpG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACrC;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACtC;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACtC;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAChH,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACzC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1G,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACvC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1G,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACvC;aAAM,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,UAAU,IAAI,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7H,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAC3C;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9F,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1G,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACvC;QAED,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,2BAA2B,CAAC,UAAU,IAAI,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3I,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;SAClD;QAED,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACnH,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC1C;QAED,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,UAAU,IAAI,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACtH,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAC3C;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG;IACK,qBAAqB;QACzB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,OAAO,IAAI,CAAC,kBAAkB,CAAC;SAClC;QAED,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,kBAAkB,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,MAAM,cAAc,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAEjD,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC5C;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC7C;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC7C;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAChD;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAClD;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;QAED,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;SACzD;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACjD;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAClD;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC1C;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACI,UAAU,CAAC,OAAoB;QAClC,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,OAAO,EAAE;YACjC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE;YAClC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE;YAClC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,EAAE;YACrC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE;YACnC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,OAAO,EAAE;YACvC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE;YACnC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,2BAA2B,KAAK,OAAO,EAAE;YAC9C,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,mBAAmB,KAAK,OAAO,EAAE;YACtC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,OAAO,EAAE;YACvC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE;YAC/B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE;YACnC,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACI,kBAAkB;QACrB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,mBAAmB,EAAE;YACvC,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,uBAAuB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,0BAA0B,EAAE,CAAC;QAC7E,IAAI,uBAAuB,EAAE;YACzB,uBAAuB,CAAC,OAAO,GAAG,IAAI,CAAC;SAC1C;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,kBAA4B,EAAE,oBAA8B;QACvE,IAAI,oBAAoB,EAAE;YACtB,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,sBAAsB,KAAK,IAAI,CAAC,uBAAuB,EAAE;gBACzG,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;aAC1C;YAED,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,CAAC;YAC/B,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,CAAC;YAChC,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,CAAC;YAChC,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE,CAAC;YACnC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,CAAC;YACjC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,CAAC;YACjC,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;YACrC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,CAAC;YACjC,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,CAAC;YAC5C,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;SACxC;QAED,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,6BAA6B,IAAI,IAAI,CAAC,wBAAwB,EAAE;YACrE,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;SAC/F;QAED,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;IAC5D,CAAC;;AAjvED;;GAEG;AACoB,kCAAkB,GAAG,QAAQ,CAAC,eAAe,AAA3B,CAA4B;AAErE;;GAEG;AACoB,qCAAqB,GAAG,QAAQ,CAAC,kBAAkB,AAA9B,CAA+B;AAE3E;;GAEG;AACoB,sCAAsB,GAAG,QAAQ,CAAC,mBAAmB,AAA/B,CAAgC;AAE7E;;;GAGG;AACoB,6CAA6B,GAAG,QAAQ,CAAC,0BAA0B,AAAtC,CAAuC;AAE3F;;;GAGG;AACW,+CAA+B,GAAG,CAAC,AAAJ,CAAK;AAElD;;GAEG;AACoB,qCAAqB,GAAG,CAAC,AAAJ,CAAK;AAEjD;;;GAGG;AACoB,iCAAiB,GAAG,CAAC,AAAJ,CAAK;AAE7C;;;GAGG;AACoB,qCAAqB,GAAG,CAAC,AAAJ,CAAK;AA8bvC;IADT,uCAAuC,EAAE;sEAC4B;AAgE/D;IADN,gBAAgB,CAAC,8BAA8B,CAAC;kDAC5B", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { serializeAsImageProcessingConfiguration, expandToProperty } from \"../../Misc/decorators\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport { SmartArray } from \"../../Misc/smartArray\";\r\nimport { GetEnvironmentBRDFTexture } from \"../../Misc/brdfTextureTools\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Scene } from \"../../scene\";\r\nimport type { Matrix } from \"../../Maths/math.vector\";\r\nimport { Vector4 } from \"../../Maths/math.vector\";\r\nimport { VertexBuffer } from \"../../Buffers/buffer\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport { PBRBRDFConfiguration } from \"./pbrBRDFConfiguration\";\r\nimport { PrePassConfiguration } from \"../prePassConfiguration\";\r\nimport { Color3, TmpColors } from \"../../Maths/math.color\";\r\nimport { Scalar } from \"../../Maths/math.scalar\";\r\n\r\nimport type { IImageProcessingConfigurationDefines } from \"../../Materials/imageProcessingConfiguration.defines\";\r\nimport { ImageProcessingConfiguration } from \"../../Materials/imageProcessingConfiguration\";\r\nimport type { Effect, IEffectCreationOptions } from \"../../Materials/effect\";\r\nimport type { IMaterialCompilationOptions, ICustomShaderNameResolveOptions } from \"../../Materials/material\";\r\nimport { Material } from \"../../Materials/material\";\r\nimport { MaterialPluginEvent } from \"../materialPluginEvent\";\r\nimport { MaterialDefines } from \"../../Materials/materialDefines\";\r\nimport { PushMaterial } from \"../../Materials/pushMaterial\";\r\n\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { Texture } from \"../../Materials/Textures/texture\";\r\nimport type { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\nimport type { CubeTexture } from \"../../Materials/Textures/cubeTexture\";\r\n\r\nimport { MaterialFlags } from \"../materialFlags\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport type { IAnimatable } from \"../../Animations/animatable.interface\";\r\n\r\nimport \"../../Materials/Textures/baseTexture.polynomial\";\r\nimport \"../../Shaders/pbr.fragment\";\r\nimport \"../../Shaders/pbr.vertex\";\r\n\r\nimport { EffectFallbacks } from \"../effectFallbacks\";\r\nimport { PBRClearCoatConfiguration } from \"./pbrClearCoatConfiguration\";\r\nimport { PBRIridescenceConfiguration } from \"./pbrIridescenceConfiguration\";\r\nimport { PBRAnisotropicConfiguration } from \"./pbrAnisotropicConfiguration\";\r\nimport { PBRSheenConfiguration } from \"./pbrSheenConfiguration\";\r\nimport { PBRSubSurfaceConfiguration } from \"./pbrSubSurfaceConfiguration\";\r\nimport { DetailMapConfiguration } from \"../material.detailMapConfiguration\";\r\nimport { addClipPlaneUniforms, bindClipPlane } from \"../clipPlaneMaterialHelper\";\r\nimport {\r\n    BindBonesParameters,\r\n    BindFogParameters,\r\n    BindLights,\r\n    BindLogDepth,\r\n    BindMorphTargetParameters,\r\n    BindTextureMatrix,\r\n    HandleFallbacksForShadows,\r\n    PrepareAttributesForBakedVertexAnimation,\r\n    PrepareAttributesForBones,\r\n    PrepareAttributesForInstances,\r\n    PrepareAttributesForMorphTargets,\r\n    PrepareDefinesForAttributes,\r\n    PrepareDefinesForFrameBoundValues,\r\n    PrepareDefinesForLights,\r\n    PrepareDefinesForMergedUV,\r\n    PrepareDefinesForMisc,\r\n    PrepareDefinesForMultiview,\r\n    PrepareDefinesForOIT,\r\n    PrepareDefinesForPrePass,\r\n    PrepareUniformsAndSamplersList,\r\n} from \"../materialHelper.functions\";\r\n\r\nconst onCreatedEffectParameters = { effect: null as unknown as Effect, subMesh: null as unknown as Nullable<SubMesh> };\r\n\r\n/**\r\n * Manages the defines for the PBR Material.\r\n * @internal\r\n */\r\nexport class PBRMaterialDefines extends MaterialDefines implements IImageProcessingConfigurationDefines {\r\n    public PBR = true;\r\n\r\n    public NUM_SAMPLES = \"0\";\r\n    public REALTIME_FILTERING = false;\r\n\r\n    public MAINUV1 = false;\r\n    public MAINUV2 = false;\r\n    public MAINUV3 = false;\r\n    public MAINUV4 = false;\r\n    public MAINUV5 = false;\r\n    public MAINUV6 = false;\r\n    public UV1 = false;\r\n    public UV2 = false;\r\n    public UV3 = false;\r\n    public UV4 = false;\r\n    public UV5 = false;\r\n    public UV6 = false;\r\n\r\n    public ALBEDO = false;\r\n    public GAMMAALBEDO = false;\r\n    public ALBEDODIRECTUV = 0;\r\n    public VERTEXCOLOR = false;\r\n\r\n    public BAKED_VERTEX_ANIMATION_TEXTURE = false;\r\n\r\n    public AMBIENT = false;\r\n    public AMBIENTDIRECTUV = 0;\r\n    public AMBIENTINGRAYSCALE = false;\r\n\r\n    public OPACITY = false;\r\n    public VERTEXALPHA = false;\r\n    public OPACITYDIRECTUV = 0;\r\n    public OPACITYRGB = false;\r\n    public ALPHATEST = false;\r\n    public DEPTHPREPASS = false;\r\n    public ALPHABLEND = false;\r\n    public ALPHAFROMALBEDO = false;\r\n    public ALPHATESTVALUE = \"0.5\";\r\n    public SPECULAROVERALPHA = false;\r\n    public RADIANCEOVERALPHA = false;\r\n    public ALPHAFRESNEL = false;\r\n    public LINEARALPHAFRESNEL = false;\r\n    public PREMULTIPLYALPHA = false;\r\n\r\n    public EMISSIVE = false;\r\n    public EMISSIVEDIRECTUV = 0;\r\n    public GAMMAEMISSIVE = false;\r\n\r\n    public REFLECTIVITY = false;\r\n    public REFLECTIVITY_GAMMA = false;\r\n    public REFLECTIVITYDIRECTUV = 0;\r\n    public SPECULARTERM = false;\r\n\r\n    public MICROSURFACEFROMREFLECTIVITYMAP = false;\r\n    public MICROSURFACEAUTOMATIC = false;\r\n    public LODBASEDMICROSFURACE = false;\r\n    public MICROSURFACEMAP = false;\r\n    public MICROSURFACEMAPDIRECTUV = 0;\r\n\r\n    public METALLICWORKFLOW = false;\r\n    public ROUGHNESSSTOREINMETALMAPALPHA = false;\r\n    public ROUGHNESSSTOREINMETALMAPGREEN = false;\r\n    public METALLNESSSTOREINMETALMAPBLUE = false;\r\n    public AOSTOREINMETALMAPRED = false;\r\n    public METALLIC_REFLECTANCE = false;\r\n    public METALLIC_REFLECTANCE_GAMMA = false;\r\n    public METALLIC_REFLECTANCEDIRECTUV = 0;\r\n    public METALLIC_REFLECTANCE_USE_ALPHA_ONLY = false;\r\n    public REFLECTANCE = false;\r\n    public REFLECTANCE_GAMMA = false;\r\n    public REFLECTANCEDIRECTUV = 0;\r\n\r\n    public ENVIRONMENTBRDF = false;\r\n    public ENVIRONMENTBRDF_RGBD = false;\r\n\r\n    public NORMAL = false;\r\n    public TANGENT = false;\r\n    public BUMP = false;\r\n    public BUMPDIRECTUV = 0;\r\n    public OBJECTSPACE_NORMALMAP = false;\r\n    public PARALLAX = false;\r\n    public PARALLAX_RHS = false;\r\n    public PARALLAXOCCLUSION = false;\r\n    public NORMALXYSCALE = true;\r\n\r\n    public LIGHTMAP = false;\r\n    public LIGHTMAPDIRECTUV = 0;\r\n    public USELIGHTMAPASSHADOWMAP = false;\r\n    public GAMMALIGHTMAP = false;\r\n    public RGBDLIGHTMAP = false;\r\n\r\n    public REFLECTION = false;\r\n    public REFLECTIONMAP_3D = false;\r\n    public REFLECTIONMAP_SPHERICAL = false;\r\n    public REFLECTIONMAP_PLANAR = false;\r\n    public REFLECTIONMAP_CUBIC = false;\r\n    public USE_LOCAL_REFLECTIONMAP_CUBIC = false;\r\n    public REFLECTIONMAP_PROJECTION = false;\r\n    public REFLECTIONMAP_SKYBOX = false;\r\n    public REFLECTIONMAP_EXPLICIT = false;\r\n    public REFLECTIONMAP_EQUIRECTANGULAR = false;\r\n    public REFLECTIONMAP_EQUIRECTANGULAR_FIXED = false;\r\n    public REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED = false;\r\n    public INVERTCUBICMAP = false;\r\n    public USESPHERICALFROMREFLECTIONMAP = false;\r\n    public USEIRRADIANCEMAP = false;\r\n    public USESPHERICALINVERTEX = false;\r\n    public REFLECTIONMAP_OPPOSITEZ = false;\r\n    public LODINREFLECTIONALPHA = false;\r\n    public GAMMAREFLECTION = false;\r\n    public RGBDREFLECTION = false;\r\n    public LINEARSPECULARREFLECTION = false;\r\n    public RADIANCEOCCLUSION = false;\r\n    public HORIZONOCCLUSION = false;\r\n\r\n    public INSTANCES = false;\r\n    public THIN_INSTANCES = false;\r\n    public INSTANCESCOLOR = false;\r\n\r\n    public PREPASS = false;\r\n    public PREPASS_IRRADIANCE = false;\r\n    public PREPASS_IRRADIANCE_INDEX = -1;\r\n    public PREPASS_ALBEDO_SQRT = false;\r\n    public PREPASS_ALBEDO_SQRT_INDEX = -1;\r\n    public PREPASS_DEPTH = false;\r\n    public PREPASS_DEPTH_INDEX = -1;\r\n    public PREPASS_NORMAL = false;\r\n    public PREPASS_NORMAL_INDEX = -1;\r\n    public PREPASS_NORMAL_WORLDSPACE = false;\r\n    public PREPASS_POSITION = false;\r\n    public PREPASS_POSITION_INDEX = -1;\r\n    public PREPASS_VELOCITY = false;\r\n    public PREPASS_VELOCITY_INDEX = -1;\r\n    public PREPASS_REFLECTIVITY = false;\r\n    public PREPASS_REFLECTIVITY_INDEX = -1;\r\n    public SCENE_MRT_COUNT = 0;\r\n\r\n    public NUM_BONE_INFLUENCERS = 0;\r\n    public BonesPerMesh = 0;\r\n    public BONETEXTURE = false;\r\n    public BONES_VELOCITY_ENABLED = false;\r\n\r\n    public NONUNIFORMSCALING = false;\r\n\r\n    public MORPHTARGETS = false;\r\n    public MORPHTARGETS_NORMAL = false;\r\n    public MORPHTARGETS_TANGENT = false;\r\n    public MORPHTARGETS_UV = false;\r\n    public NUM_MORPH_INFLUENCERS = 0;\r\n    public MORPHTARGETS_TEXTURE = false;\r\n\r\n    public IMAGEPROCESSING = false;\r\n    public VIGNETTE = false;\r\n    public VIGNETTEBLENDMODEMULTIPLY = false;\r\n    public VIGNETTEBLENDMODEOPAQUE = false;\r\n    public TONEMAPPING = false;\r\n    public TONEMAPPING_ACES = false;\r\n    public CONTRAST = false;\r\n    public COLORCURVES = false;\r\n    public COLORGRADING = false;\r\n    public COLORGRADING3D = false;\r\n    public SAMPLER3DGREENDEPTH = false;\r\n    public SAMPLER3DBGRMAP = false;\r\n    public DITHER = false;\r\n    public IMAGEPROCESSINGPOSTPROCESS = false;\r\n    public SKIPFINALCOLORCLAMP = false;\r\n    public EXPOSURE = false;\r\n    public MULTIVIEW = false;\r\n    public ORDER_INDEPENDENT_TRANSPARENCY = false;\r\n    public ORDER_INDEPENDENT_TRANSPARENCY_16BITS = false;\r\n\r\n    public USEPHYSICALLIGHTFALLOFF = false;\r\n    public USEGLTFLIGHTFALLOFF = false;\r\n    public TWOSIDEDLIGHTING = false;\r\n    public SHADOWFLOAT = false;\r\n    public CLIPPLANE = false;\r\n    public CLIPPLANE2 = false;\r\n    public CLIPPLANE3 = false;\r\n    public CLIPPLANE4 = false;\r\n    public CLIPPLANE5 = false;\r\n    public CLIPPLANE6 = false;\r\n    public POINTSIZE = false;\r\n    public FOG = false;\r\n    public LOGARITHMICDEPTH = false;\r\n    public CAMERA_ORTHOGRAPHIC = false;\r\n    public CAMERA_PERSPECTIVE = false;\r\n\r\n    public FORCENORMALFORWARD = false;\r\n\r\n    public SPECULARAA = false;\r\n\r\n    public UNLIT = false;\r\n\r\n    public DECAL_AFTER_DETAIL = false;\r\n\r\n    public DEBUGMODE = 0;\r\n\r\n    /**\r\n     * Initializes the PBR Material defines.\r\n     * @param externalProperties The external properties\r\n     */\r\n    constructor(externalProperties?: { [name: string]: { type: string; default: any } }) {\r\n        super(externalProperties);\r\n        this.rebuild();\r\n    }\r\n\r\n    /**\r\n     * Resets the PBR Material defines.\r\n     */\r\n    public reset(): void {\r\n        super.reset();\r\n        this.ALPHATESTVALUE = \"0.5\";\r\n        this.PBR = true;\r\n        this.NORMALXYSCALE = true;\r\n    }\r\n}\r\n\r\n/**\r\n * The Physically based material base class of BJS.\r\n *\r\n * This offers the main features of a standard PBR material.\r\n * For more information, please refer to the documentation :\r\n * https://doc.babylonjs.com/features/featuresDeepDive/materials/using/introToPBR\r\n */\r\nexport abstract class PBRBaseMaterial extends PushMaterial {\r\n    /**\r\n     * PBRMaterialTransparencyMode: No transparency mode, Alpha channel is not use.\r\n     */\r\n    public static readonly PBRMATERIAL_OPAQUE = Material.MATERIAL_OPAQUE;\r\n\r\n    /**\r\n     * PBRMaterialTransparencyMode: Alpha Test mode, pixel are discarded below a certain threshold defined by the alpha cutoff value.\r\n     */\r\n    public static readonly PBRMATERIAL_ALPHATEST = Material.MATERIAL_ALPHATEST;\r\n\r\n    /**\r\n     * PBRMaterialTransparencyMode: Pixels are blended (according to the alpha mode) with the already drawn pixels in the current frame buffer.\r\n     */\r\n    public static readonly PBRMATERIAL_ALPHABLEND = Material.MATERIAL_ALPHABLEND;\r\n\r\n    /**\r\n     * PBRMaterialTransparencyMode: Pixels are blended (according to the alpha mode) with the already drawn pixels in the current frame buffer.\r\n     * They are also discarded below the alpha cutoff threshold to improve performances.\r\n     */\r\n    public static readonly PBRMATERIAL_ALPHATESTANDBLEND = Material.MATERIAL_ALPHATESTANDBLEND;\r\n\r\n    /**\r\n     * Defines the default value of how much AO map is occluding the analytical lights\r\n     * (point spot...).\r\n     */\r\n    public static DEFAULT_AO_ON_ANALYTICAL_LIGHTS = 0;\r\n\r\n    /**\r\n     * PBRMaterialLightFalloff Physical: light is falling off following the inverse squared distance law.\r\n     */\r\n    public static readonly LIGHTFALLOFF_PHYSICAL = 0;\r\n\r\n    /**\r\n     * PBRMaterialLightFalloff gltf: light is falling off as described in the gltf moving to PBR document\r\n     * to enhance interoperability with other engines.\r\n     */\r\n    public static readonly LIGHTFALLOFF_GLTF = 1;\r\n\r\n    /**\r\n     * PBRMaterialLightFalloff Standard: light is falling off like in the standard material\r\n     * to enhance interoperability with other materials.\r\n     */\r\n    public static readonly LIGHTFALLOFF_STANDARD = 2;\r\n\r\n    /**\r\n     * Intensity of the direct lights e.g. the four lights available in your scene.\r\n     * This impacts both the direct diffuse and specular highlights.\r\n     * @internal\r\n     */\r\n    public _directIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Intensity of the emissive part of the material.\r\n     * This helps controlling the emissive effect without modifying the emissive color.\r\n     * @internal\r\n     */\r\n    public _emissiveIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Intensity of the environment e.g. how much the environment will light the object\r\n     * either through harmonics for rough material or through the reflection for shiny ones.\r\n     * @internal\r\n     */\r\n    public _environmentIntensity: number = 1.0;\r\n\r\n    /**\r\n     * This is a special control allowing the reduction of the specular highlights coming from the\r\n     * four lights of the scene. Those highlights may not be needed in full environment lighting.\r\n     * @internal\r\n     */\r\n    public _specularIntensity: number = 1.0;\r\n\r\n    /**\r\n     * This stores the direct, emissive, environment, and specular light intensities into a Vector4.\r\n     */\r\n    private _lightingInfos: Vector4 = new Vector4(this._directIntensity, this._emissiveIntensity, this._environmentIntensity, this._specularIntensity);\r\n\r\n    /**\r\n     * Debug Control allowing disabling the bump map on this material.\r\n     * @internal\r\n     */\r\n    public _disableBumpMap: boolean = false;\r\n\r\n    /**\r\n     * AKA Diffuse Texture in standard nomenclature.\r\n     * @internal\r\n     */\r\n    public _albedoTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * AKA Occlusion Texture in other nomenclature.\r\n     * @internal\r\n     */\r\n    public _ambientTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * AKA Occlusion Texture Intensity in other nomenclature.\r\n     * @internal\r\n     */\r\n    public _ambientTextureStrength: number = 1.0;\r\n\r\n    /**\r\n     * Defines how much the AO map is occluding the analytical lights (point spot...).\r\n     * 1 means it completely occludes it\r\n     * 0 mean it has no impact\r\n     * @internal\r\n     */\r\n    public _ambientTextureImpactOnAnalyticalLights: number = PBRBaseMaterial.DEFAULT_AO_ON_ANALYTICAL_LIGHTS;\r\n\r\n    /**\r\n     * Stores the alpha values in a texture.\r\n     * @internal\r\n     */\r\n    public _opacityTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Stores the reflection values in a texture.\r\n     * @internal\r\n     */\r\n    public _reflectionTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Stores the emissive values in a texture.\r\n     * @internal\r\n     */\r\n    public _emissiveTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * AKA Specular texture in other nomenclature.\r\n     * @internal\r\n     */\r\n    public _reflectivityTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Used to switch from specular/glossiness to metallic/roughness workflow.\r\n     * @internal\r\n     */\r\n    public _metallicTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Specifies the metallic scalar of the metallic/roughness workflow.\r\n     * Can also be used to scale the metalness values of the metallic texture.\r\n     * @internal\r\n     */\r\n    public _metallic: Nullable<number> = null;\r\n\r\n    /**\r\n     * Specifies the roughness scalar of the metallic/roughness workflow.\r\n     * Can also be used to scale the roughness values of the metallic texture.\r\n     * @internal\r\n     */\r\n    public _roughness: Nullable<number> = null;\r\n\r\n    /**\r\n     * In metallic workflow, specifies an F0 factor to help configuring the material F0.\r\n     * By default the indexOfrefraction is used to compute F0;\r\n     *\r\n     * This is used as a factor against the default reflectance at normal incidence to tweak it.\r\n     *\r\n     * F0 = defaultF0 * metallicF0Factor * metallicReflectanceColor;\r\n     * F90 = metallicReflectanceColor;\r\n     * @internal\r\n     */\r\n    public _metallicF0Factor = 1;\r\n\r\n    /**\r\n     * In metallic workflow, specifies an F0 color.\r\n     * By default the F90 is always 1;\r\n     *\r\n     * Please note that this factor is also used as a factor against the default reflectance at normal incidence.\r\n     *\r\n     * F0 = defaultF0_from_IOR * metallicF0Factor * metallicReflectanceColor\r\n     * F90 = metallicF0Factor;\r\n     * @internal\r\n     */\r\n    public _metallicReflectanceColor = Color3.White();\r\n\r\n    /**\r\n     * Specifies that only the A channel from _metallicReflectanceTexture should be used.\r\n     * If false, both RGB and A channels will be used\r\n     * @internal\r\n     */\r\n    public _useOnlyMetallicFromMetallicReflectanceTexture = false;\r\n\r\n    /**\r\n     * Defines to store metallicReflectanceColor in RGB and metallicF0Factor in A\r\n     * This is multiply against the scalar values defined in the material.\r\n     * @internal\r\n     */\r\n    public _metallicReflectanceTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Defines to store reflectanceColor in RGB\r\n     * This is multiplied against the scalar values defined in the material.\r\n     * If both _reflectanceTexture and _metallicReflectanceTexture textures are provided and _useOnlyMetallicFromMetallicReflectanceTexture\r\n     * is false, _metallicReflectanceTexture takes precedence and _reflectanceTexture is not used\r\n     * @internal\r\n     */\r\n    public _reflectanceTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Used to enable roughness/glossiness fetch from a separate channel depending on the current mode.\r\n     * Gray Scale represents roughness in metallic mode and glossiness in specular mode.\r\n     * @internal\r\n     */\r\n    public _microSurfaceTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Stores surface normal data used to displace a mesh in a texture.\r\n     * @internal\r\n     */\r\n    public _bumpTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Stores the pre-calculated light information of a mesh in a texture.\r\n     * @internal\r\n     */\r\n    public _lightmapTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * The color of a material in ambient lighting.\r\n     * @internal\r\n     */\r\n    public _ambientColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * AKA Diffuse Color in other nomenclature.\r\n     * @internal\r\n     */\r\n    public _albedoColor = new Color3(1, 1, 1);\r\n\r\n    /**\r\n     * AKA Specular Color in other nomenclature.\r\n     * @internal\r\n     */\r\n    public _reflectivityColor = new Color3(1, 1, 1);\r\n\r\n    /**\r\n     * The color applied when light is reflected from a material.\r\n     * @internal\r\n     */\r\n    public _reflectionColor = new Color3(1, 1, 1);\r\n\r\n    /**\r\n     * The color applied when light is emitted from a material.\r\n     * @internal\r\n     */\r\n    public _emissiveColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * AKA Glossiness in other nomenclature.\r\n     * @internal\r\n     */\r\n    public _microSurface = 0.9;\r\n\r\n    /**\r\n     * Specifies that the material will use the light map as a show map.\r\n     * @internal\r\n     */\r\n    public _useLightmapAsShadowmap = false;\r\n\r\n    /**\r\n     * This parameters will enable/disable Horizon occlusion to prevent normal maps to look shiny when the normal\r\n     * makes the reflect vector face the model (under horizon).\r\n     * @internal\r\n     */\r\n    public _useHorizonOcclusion = true;\r\n\r\n    /**\r\n     * This parameters will enable/disable radiance occlusion by preventing the radiance to lit\r\n     * too much the area relying on ambient texture to define their ambient occlusion.\r\n     * @internal\r\n     */\r\n    public _useRadianceOcclusion = true;\r\n\r\n    /**\r\n     * Specifies that the alpha is coming form the albedo channel alpha channel for alpha blending.\r\n     * @internal\r\n     */\r\n    public _useAlphaFromAlbedoTexture = false;\r\n\r\n    /**\r\n     * Specifies that the material will keeps the specular highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good example of that. When sun reflects on it you can not see what is behind.\r\n     * @internal\r\n     */\r\n    public _useSpecularOverAlpha = true;\r\n\r\n    /**\r\n     * Specifies if the reflectivity texture contains the glossiness information in its alpha channel.\r\n     * @internal\r\n     */\r\n    public _useMicroSurfaceFromReflectivityMapAlpha = false;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the roughness information in its alpha channel.\r\n     * @internal\r\n     */\r\n    public _useRoughnessFromMetallicTextureAlpha = true;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the roughness information in its green channel.\r\n     * @internal\r\n     */\r\n    public _useRoughnessFromMetallicTextureGreen = false;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the metallness information in its blue channel.\r\n     * @internal\r\n     */\r\n    public _useMetallnessFromMetallicTextureBlue = false;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the ambient occlusion information in its red channel.\r\n     * @internal\r\n     */\r\n    public _useAmbientOcclusionFromMetallicTextureRed = false;\r\n\r\n    /**\r\n     * Specifies if the ambient texture contains the ambient occlusion information in its red channel only.\r\n     * @internal\r\n     */\r\n    public _useAmbientInGrayScale = false;\r\n\r\n    /**\r\n     * In case the reflectivity map does not contain the microsurface information in its alpha channel,\r\n     * The material will try to infer what glossiness each pixel should be.\r\n     * @internal\r\n     */\r\n    public _useAutoMicroSurfaceFromReflectivityMap = false;\r\n\r\n    /**\r\n     * Defines the  falloff type used in this material.\r\n     * It by default is Physical.\r\n     * @internal\r\n     */\r\n    public _lightFalloff = PBRBaseMaterial.LIGHTFALLOFF_PHYSICAL;\r\n\r\n    /**\r\n     * Specifies that the material will keeps the reflection highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good example of that. When the street lights reflects on it you can not see what is behind.\r\n     * @internal\r\n     */\r\n    public _useRadianceOverAlpha = true;\r\n\r\n    /**\r\n     * Allows using an object space normal map (instead of tangent space).\r\n     * @internal\r\n     */\r\n    public _useObjectSpaceNormalMap = false;\r\n\r\n    /**\r\n     * Allows using the bump map in parallax mode.\r\n     * @internal\r\n     */\r\n    public _useParallax = false;\r\n\r\n    /**\r\n     * Allows using the bump map in parallax occlusion mode.\r\n     * @internal\r\n     */\r\n    public _useParallaxOcclusion = false;\r\n\r\n    /**\r\n     * Controls the scale bias of the parallax mode.\r\n     * @internal\r\n     */\r\n    public _parallaxScaleBias = 0.05;\r\n\r\n    /**\r\n     * If sets to true, disables all the lights affecting the material.\r\n     * @internal\r\n     */\r\n    public _disableLighting = false;\r\n\r\n    /**\r\n     * Number of Simultaneous lights allowed on the material.\r\n     * @internal\r\n     */\r\n    public _maxSimultaneousLights = 4;\r\n\r\n    /**\r\n     * If sets to true, x component of normal map value will be inverted (x = 1.0 - x).\r\n     * @internal\r\n     */\r\n    public _invertNormalMapX = false;\r\n\r\n    /**\r\n     * If sets to true, y component of normal map value will be inverted (y = 1.0 - y).\r\n     * @internal\r\n     */\r\n    public _invertNormalMapY = false;\r\n\r\n    /**\r\n     * If sets to true and backfaceCulling is false, normals will be flipped on the backside.\r\n     * @internal\r\n     */\r\n    public _twoSidedLighting = false;\r\n\r\n    /**\r\n     * Defines the alpha limits in alpha test mode.\r\n     * @internal\r\n     */\r\n    public _alphaCutOff = 0.4;\r\n\r\n    /**\r\n     * Enforces alpha test in opaque or blend mode in order to improve the performances of some situations.\r\n     * @internal\r\n     */\r\n    public _forceAlphaTest = false;\r\n\r\n    /**\r\n     * A fresnel is applied to the alpha of the model to ensure grazing angles edges are not alpha tested.\r\n     * And/Or occlude the blended part. (alpha is converted to gamma to compute the fresnel)\r\n     * @internal\r\n     */\r\n    public _useAlphaFresnel = false;\r\n\r\n    /**\r\n     * A fresnel is applied to the alpha of the model to ensure grazing angles edges are not alpha tested.\r\n     * And/Or occlude the blended part. (alpha stays linear to compute the fresnel)\r\n     * @internal\r\n     */\r\n    public _useLinearAlphaFresnel = false;\r\n\r\n    /**\r\n     * Specifies the environment BRDF texture used to compute the scale and offset roughness values\r\n     * from cos theta and roughness:\r\n     * http://blog.selfshadow.com/publications/s2013-shading-course/karis/s2013_pbs_epic_notes_v2.pdf\r\n     * @internal\r\n     */\r\n    public _environmentBRDFTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Force the shader to compute irradiance in the fragment shader in order to take bump in account.\r\n     * @internal\r\n     */\r\n    public _forceIrradianceInFragment = false;\r\n\r\n    private _realTimeFiltering: boolean = false;\r\n    /**\r\n     * Enables realtime filtering on the texture.\r\n     */\r\n    public get realTimeFiltering() {\r\n        return this._realTimeFiltering;\r\n    }\r\n    public set realTimeFiltering(b: boolean) {\r\n        this._realTimeFiltering = b;\r\n        this.markAsDirty(Constants.MATERIAL_TextureDirtyFlag);\r\n    }\r\n\r\n    private _realTimeFilteringQuality: number = Constants.TEXTURE_FILTERING_QUALITY_LOW;\r\n    /**\r\n     * Quality switch for realtime filtering\r\n     */\r\n    public get realTimeFilteringQuality(): number {\r\n        return this._realTimeFilteringQuality;\r\n    }\r\n    public set realTimeFilteringQuality(n: number) {\r\n        this._realTimeFilteringQuality = n;\r\n        this.markAsDirty(Constants.MATERIAL_TextureDirtyFlag);\r\n    }\r\n\r\n    /**\r\n     * Can this material render to several textures at once\r\n     */\r\n    public get canRenderToMRT() {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Force normal to face away from face.\r\n     * @internal\r\n     */\r\n    public _forceNormalForward = false;\r\n\r\n    /**\r\n     * Enables specular anti aliasing in the PBR shader.\r\n     * It will both interacts on the Geometry for analytical and IBL lighting.\r\n     * It also prefilter the roughness map based on the bump values.\r\n     * @internal\r\n     */\r\n    public _enableSpecularAntiAliasing = false;\r\n\r\n    /**\r\n     * Default configuration related to image processing available in the PBR Material.\r\n     */\r\n    @serializeAsImageProcessingConfiguration()\r\n    protected _imageProcessingConfiguration: ImageProcessingConfiguration;\r\n\r\n    /**\r\n     * Keep track of the image processing observer to allow dispose and replace.\r\n     */\r\n    private _imageProcessingObserver: Nullable<Observer<ImageProcessingConfiguration>> = null;\r\n\r\n    /**\r\n     * Attaches a new image processing configuration to the PBR Material.\r\n     * @param configuration\r\n     */\r\n    protected _attachImageProcessingConfiguration(configuration: Nullable<ImageProcessingConfiguration>): void {\r\n        if (configuration === this._imageProcessingConfiguration) {\r\n            return;\r\n        }\r\n\r\n        // Detaches observer.\r\n        if (this._imageProcessingConfiguration && this._imageProcessingObserver) {\r\n            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);\r\n        }\r\n\r\n        // Pick the scene configuration if needed.\r\n        if (!configuration) {\r\n            this._imageProcessingConfiguration = this.getScene().imageProcessingConfiguration;\r\n        } else {\r\n            this._imageProcessingConfiguration = configuration;\r\n        }\r\n\r\n        // Attaches observer.\r\n        if (this._imageProcessingConfiguration) {\r\n            this._imageProcessingObserver = this._imageProcessingConfiguration.onUpdateParameters.add(() => {\r\n                this._markAllSubMeshesAsImageProcessingDirty();\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Stores the available render targets.\r\n     */\r\n    private _renderTargets = new SmartArray<RenderTargetTexture>(16);\r\n\r\n    /**\r\n     * Sets the global ambient color for the material used in lighting calculations.\r\n     */\r\n    private _globalAmbientColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * If set to true, no lighting calculations will be applied.\r\n     */\r\n    private _unlit = false;\r\n\r\n    /**\r\n     * If sets to true, the decal map will be applied after the detail map. Else, it is applied before (default: false)\r\n     */\r\n    private _applyDecalMapAfterDetailMap = false;\r\n\r\n    private _debugMode = 0;\r\n    /**\r\n     * @internal\r\n     * This is reserved for the inspector.\r\n     * Defines the material debug mode.\r\n     * It helps seeing only some components of the material while troubleshooting.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public debugMode = 0;\r\n\r\n    /**\r\n     * @internal\r\n     * This is reserved for the inspector.\r\n     * Specify from where on screen the debug mode should start.\r\n     * The value goes from -1 (full screen) to 1 (not visible)\r\n     * It helps with side by side comparison against the final render\r\n     * This defaults to -1\r\n     */\r\n    public debugLimit = -1;\r\n\r\n    /**\r\n     * @internal\r\n     * This is reserved for the inspector.\r\n     * As the default viewing range might not be enough (if the ambient is really small for instance)\r\n     * You can use the factor to better multiply the final value.\r\n     */\r\n    public debugFactor = 1;\r\n\r\n    /**\r\n     * Defines the clear coat layer parameters for the material.\r\n     */\r\n    public readonly clearCoat: PBRClearCoatConfiguration;\r\n\r\n    /**\r\n     * Defines the iridescence layer parameters for the material.\r\n     */\r\n    public readonly iridescence: PBRIridescenceConfiguration;\r\n\r\n    /**\r\n     * Defines the anisotropic parameters for the material.\r\n     */\r\n    public readonly anisotropy: PBRAnisotropicConfiguration;\r\n\r\n    /**\r\n     * Defines the BRDF parameters for the material.\r\n     */\r\n    public readonly brdf: PBRBRDFConfiguration;\r\n\r\n    /**\r\n     * Defines the Sheen parameters for the material.\r\n     */\r\n    public readonly sheen: PBRSheenConfiguration;\r\n\r\n    /**\r\n     * Defines the SubSurface parameters for the material.\r\n     */\r\n    public readonly subSurface: PBRSubSurfaceConfiguration;\r\n\r\n    /**\r\n     * Defines additional PrePass parameters for the material.\r\n     */\r\n    public readonly prePassConfiguration: PrePassConfiguration;\r\n\r\n    /**\r\n     * Defines the detail map parameters for the material.\r\n     */\r\n    public readonly detailMap: DetailMapConfiguration;\r\n\r\n    protected _cacheHasRenderTargetTextures = false;\r\n\r\n    /**\r\n     * Instantiates a new PBRMaterial instance.\r\n     *\r\n     * @param name The material name\r\n     * @param scene The scene the material will be use in.\r\n     */\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n\r\n        this.brdf = new PBRBRDFConfiguration(this);\r\n        this.clearCoat = new PBRClearCoatConfiguration(this);\r\n        this.iridescence = new PBRIridescenceConfiguration(this);\r\n        this.anisotropy = new PBRAnisotropicConfiguration(this);\r\n        this.sheen = new PBRSheenConfiguration(this);\r\n        this.subSurface = new PBRSubSurfaceConfiguration(this);\r\n        this.detailMap = new DetailMapConfiguration(this);\r\n\r\n        // Setup the default processing configuration to the scene.\r\n        this._attachImageProcessingConfiguration(null);\r\n\r\n        this.getRenderTargetTextures = (): SmartArray<RenderTargetTexture> => {\r\n            this._renderTargets.reset();\r\n\r\n            if (MaterialFlags.ReflectionTextureEnabled && this._reflectionTexture && this._reflectionTexture.isRenderTarget) {\r\n                this._renderTargets.push(<RenderTargetTexture>this._reflectionTexture);\r\n            }\r\n\r\n            this._eventInfo.renderTargets = this._renderTargets;\r\n            this._callbackPluginEventFillRenderTargetTextures(this._eventInfo);\r\n\r\n            return this._renderTargets;\r\n        };\r\n\r\n        this._environmentBRDFTexture = GetEnvironmentBRDFTexture(this.getScene());\r\n        this.prePassConfiguration = new PrePassConfiguration();\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that current material needs to register RTT\r\n     */\r\n    public get hasRenderTargetTextures(): boolean {\r\n        if (MaterialFlags.ReflectionTextureEnabled && this._reflectionTexture && this._reflectionTexture.isRenderTarget) {\r\n            return true;\r\n        }\r\n\r\n        return this._cacheHasRenderTargetTextures;\r\n    }\r\n\r\n    /**\r\n     * Can this material render to prepass\r\n     */\r\n    public get isPrePassCapable(): boolean {\r\n        return !this.disableDepthWrite;\r\n    }\r\n\r\n    /**\r\n     * @returns the name of the material class.\r\n     */\r\n    public getClassName(): string {\r\n        return \"PBRBaseMaterial\";\r\n    }\r\n\r\n    /**\r\n     * Returns true if alpha blending should be disabled.\r\n     */\r\n    protected get _disableAlphaBlending(): boolean {\r\n        return (\r\n            this._transparencyMode === PBRBaseMaterial.PBRMATERIAL_OPAQUE ||\r\n            this._transparencyMode === PBRBaseMaterial.PBRMATERIAL_ALPHATEST ||\r\n            this.subSurface?.disableAlphaBlending\r\n        );\r\n    }\r\n\r\n    /**\r\n     * @returns whether or not this material should be rendered in alpha blend mode.\r\n     */\r\n    public needAlphaBlending(): boolean {\r\n        if (this._disableAlphaBlending) {\r\n            return false;\r\n        }\r\n\r\n        return this.alpha < 1.0 || this._opacityTexture != null || this._shouldUseAlphaFromAlbedoTexture();\r\n    }\r\n\r\n    /**\r\n     * @returns whether or not this material should be rendered in alpha test mode.\r\n     */\r\n    public needAlphaTesting(): boolean {\r\n        if (this._forceAlphaTest) {\r\n            return true;\r\n        }\r\n\r\n        if (this.subSurface?.disableAlphaBlending) {\r\n            return false;\r\n        }\r\n\r\n        return this._hasAlphaChannel() && (this._transparencyMode == null || this._transparencyMode === PBRBaseMaterial.PBRMATERIAL_ALPHATEST);\r\n    }\r\n\r\n    /**\r\n     * @returns whether or not the alpha value of the albedo texture should be used for alpha blending.\r\n     */\r\n    protected _shouldUseAlphaFromAlbedoTexture(): boolean {\r\n        return this._albedoTexture != null && this._albedoTexture.hasAlpha && this._useAlphaFromAlbedoTexture && this._transparencyMode !== PBRBaseMaterial.PBRMATERIAL_OPAQUE;\r\n    }\r\n\r\n    /**\r\n     * @returns whether or not there is a usable alpha channel for transparency.\r\n     */\r\n    protected _hasAlphaChannel(): boolean {\r\n        return (this._albedoTexture != null && this._albedoTexture.hasAlpha) || this._opacityTexture != null;\r\n    }\r\n\r\n    /**\r\n     * @returns the texture used for the alpha test.\r\n     */\r\n    public getAlphaTestTexture(): Nullable<BaseTexture> {\r\n        return this._albedoTexture;\r\n    }\r\n\r\n    /**\r\n     * Specifies that the submesh is ready to be used.\r\n     * @param mesh - BJS mesh.\r\n     * @param subMesh - A submesh of the BJS mesh.  Used to check if it is ready.\r\n     * @param useInstances - Specifies that instances should be used.\r\n     * @returns - boolean indicating that the submesh is ready or not.\r\n     */\r\n    public isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances?: boolean): boolean {\r\n        if (!this._uniformBufferLayoutBuilt) {\r\n            this.buildUniformLayout();\r\n        }\r\n\r\n        const drawWrapper = subMesh._drawWrapper;\r\n\r\n        if (drawWrapper.effect && this.isFrozen) {\r\n            if (drawWrapper._wasPreviouslyReady && drawWrapper._wasPreviouslyUsingInstances === useInstances) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!subMesh.materialDefines) {\r\n            this._callbackPluginEventGeneric(MaterialPluginEvent.GetDefineNames, this._eventInfo);\r\n            subMesh.materialDefines = new PBRMaterialDefines(this._eventInfo.defineNames);\r\n        }\r\n\r\n        const defines = <PBRMaterialDefines>subMesh.materialDefines;\r\n        if (this._isReadyForSubMesh(subMesh)) {\r\n            return true;\r\n        }\r\n\r\n        const scene = this.getScene();\r\n        const engine = scene.getEngine();\r\n\r\n        if (defines._areTexturesDirty) {\r\n            this._eventInfo.hasRenderTargetTextures = false;\r\n            this._callbackPluginEventHasRenderTargetTextures(this._eventInfo);\r\n            this._cacheHasRenderTargetTextures = this._eventInfo.hasRenderTargetTextures;\r\n            if (scene.texturesEnabled) {\r\n                if (this._albedoTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                    if (!this._albedoTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._ambientTexture && MaterialFlags.AmbientTextureEnabled) {\r\n                    if (!this._ambientTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._opacityTexture && MaterialFlags.OpacityTextureEnabled) {\r\n                    if (!this._opacityTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                const reflectionTexture = this._getReflectionTexture();\r\n                if (reflectionTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n                    if (!reflectionTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                    if (reflectionTexture.irradianceTexture) {\r\n                        if (!reflectionTexture.irradianceTexture.isReadyOrNotBlocking()) {\r\n                            return false;\r\n                        }\r\n                    } else {\r\n                        // Not ready until spherical are ready too.\r\n                        if (!reflectionTexture.sphericalPolynomial && reflectionTexture.getInternalTexture()?._sphericalPolynomialPromise) {\r\n                            return false;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                if (this._lightmapTexture && MaterialFlags.LightmapTextureEnabled) {\r\n                    if (!this._lightmapTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._emissiveTexture && MaterialFlags.EmissiveTextureEnabled) {\r\n                    if (!this._emissiveTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (MaterialFlags.SpecularTextureEnabled) {\r\n                    if (this._metallicTexture) {\r\n                        if (!this._metallicTexture.isReadyOrNotBlocking()) {\r\n                            return false;\r\n                        }\r\n                    } else if (this._reflectivityTexture) {\r\n                        if (!this._reflectivityTexture.isReadyOrNotBlocking()) {\r\n                            return false;\r\n                        }\r\n                    }\r\n\r\n                    if (this._metallicReflectanceTexture) {\r\n                        if (!this._metallicReflectanceTexture.isReadyOrNotBlocking()) {\r\n                            return false;\r\n                        }\r\n                    }\r\n\r\n                    if (this._reflectanceTexture) {\r\n                        if (!this._reflectanceTexture.isReadyOrNotBlocking()) {\r\n                            return false;\r\n                        }\r\n                    }\r\n\r\n                    if (this._microSurfaceTexture) {\r\n                        if (!this._microSurfaceTexture.isReadyOrNotBlocking()) {\r\n                            return false;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                if (engine.getCaps().standardDerivatives && this._bumpTexture && MaterialFlags.BumpTextureEnabled && !this._disableBumpMap) {\r\n                    // Bump texture cannot be not blocking.\r\n                    if (!this._bumpTexture.isReady()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._environmentBRDFTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n                    // This is blocking.\r\n                    if (!this._environmentBRDFTexture.isReady()) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        this._eventInfo.isReadyForSubMesh = true;\r\n        this._eventInfo.defines = defines;\r\n        this._eventInfo.subMesh = subMesh;\r\n        this._callbackPluginEventIsReadyForSubMesh(this._eventInfo);\r\n\r\n        if (!this._eventInfo.isReadyForSubMesh) {\r\n            return false;\r\n        }\r\n\r\n        if (defines._areImageProcessingDirty && this._imageProcessingConfiguration) {\r\n            if (!this._imageProcessingConfiguration.isReady()) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        if (!engine.getCaps().standardDerivatives && !mesh.isVerticesDataPresent(VertexBuffer.NormalKind)) {\r\n            mesh.createNormals(true);\r\n            Logger.Warn(\"PBRMaterial: Normals have been created for the mesh: \" + mesh.name);\r\n        }\r\n\r\n        const previousEffect = subMesh.effect;\r\n        const lightDisposed = defines._areLightsDisposed;\r\n        let effect = this._prepareEffect(mesh, defines, this.onCompiled, this.onError, useInstances, null, subMesh.getRenderingMesh().hasThinInstances);\r\n\r\n        let forceWasNotReadyPreviously = false;\r\n\r\n        if (effect) {\r\n            if (this._onEffectCreatedObservable) {\r\n                onCreatedEffectParameters.effect = effect;\r\n                onCreatedEffectParameters.subMesh = subMesh;\r\n                this._onEffectCreatedObservable.notifyObservers(onCreatedEffectParameters);\r\n            }\r\n\r\n            // Use previous effect while new one is compiling\r\n            if (this.allowShaderHotSwapping && previousEffect && !effect.isReady()) {\r\n                effect = previousEffect;\r\n                defines.markAsUnprocessed();\r\n\r\n                forceWasNotReadyPreviously = this.isFrozen;\r\n\r\n                if (lightDisposed) {\r\n                    // re register in case it takes more than one frame.\r\n                    defines._areLightsDisposed = true;\r\n                    return false;\r\n                }\r\n            } else {\r\n                scene.resetCachedMaterial();\r\n                subMesh.setEffect(effect, defines, this._materialContext);\r\n            }\r\n        }\r\n\r\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        defines._renderId = scene.getRenderId();\r\n        drawWrapper._wasPreviouslyReady = forceWasNotReadyPreviously ? false : true;\r\n        drawWrapper._wasPreviouslyUsingInstances = !!useInstances;\r\n\r\n        this._checkScenePerformancePriority();\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Specifies if the material uses metallic roughness workflow.\r\n     * @returns boolean specifying if the material uses metallic roughness workflow.\r\n     */\r\n    public isMetallicWorkflow(): boolean {\r\n        if (this._metallic != null || this._roughness != null || this._metallicTexture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    private _prepareEffect(\r\n        mesh: AbstractMesh,\r\n        defines: PBRMaterialDefines,\r\n        onCompiled: Nullable<(effect: Effect) => void> = null,\r\n        onError: Nullable<(effect: Effect, errors: string) => void> = null,\r\n        useInstances: Nullable<boolean> = null,\r\n        useClipPlane: Nullable<boolean> = null,\r\n        useThinInstances: boolean\r\n    ): Nullable<Effect> {\r\n        this._prepareDefines(mesh, defines, useInstances, useClipPlane, useThinInstances);\r\n\r\n        if (!defines.isDirty) {\r\n            return null;\r\n        }\r\n\r\n        defines.markAsProcessed();\r\n\r\n        const scene = this.getScene();\r\n        const engine = scene.getEngine();\r\n\r\n        // Fallbacks\r\n        const fallbacks = new EffectFallbacks();\r\n        let fallbackRank = 0;\r\n        if (defines.USESPHERICALINVERTEX) {\r\n            fallbacks.addFallback(fallbackRank++, \"USESPHERICALINVERTEX\");\r\n        }\r\n\r\n        if (defines.FOG) {\r\n            fallbacks.addFallback(fallbackRank, \"FOG\");\r\n        }\r\n        if (defines.SPECULARAA) {\r\n            fallbacks.addFallback(fallbackRank, \"SPECULARAA\");\r\n        }\r\n        if (defines.POINTSIZE) {\r\n            fallbacks.addFallback(fallbackRank, \"POINTSIZE\");\r\n        }\r\n        if (defines.LOGARITHMICDEPTH) {\r\n            fallbacks.addFallback(fallbackRank, \"LOGARITHMICDEPTH\");\r\n        }\r\n        if (defines.PARALLAX) {\r\n            fallbacks.addFallback(fallbackRank, \"PARALLAX\");\r\n        }\r\n        if (defines.PARALLAX_RHS) {\r\n            fallbacks.addFallback(fallbackRank, \"PARALLAX_RHS\");\r\n        }\r\n        if (defines.PARALLAXOCCLUSION) {\r\n            fallbacks.addFallback(fallbackRank++, \"PARALLAXOCCLUSION\");\r\n        }\r\n\r\n        if (defines.ENVIRONMENTBRDF) {\r\n            fallbacks.addFallback(fallbackRank++, \"ENVIRONMENTBRDF\");\r\n        }\r\n\r\n        if (defines.TANGENT) {\r\n            fallbacks.addFallback(fallbackRank++, \"TANGENT\");\r\n        }\r\n\r\n        if (defines.BUMP) {\r\n            fallbacks.addFallback(fallbackRank++, \"BUMP\");\r\n        }\r\n\r\n        fallbackRank = HandleFallbacksForShadows(defines, fallbacks, this._maxSimultaneousLights, fallbackRank++);\r\n\r\n        if (defines.SPECULARTERM) {\r\n            fallbacks.addFallback(fallbackRank++, \"SPECULARTERM\");\r\n        }\r\n\r\n        if (defines.USESPHERICALFROMREFLECTIONMAP) {\r\n            fallbacks.addFallback(fallbackRank++, \"USESPHERICALFROMREFLECTIONMAP\");\r\n        }\r\n\r\n        if (defines.USEIRRADIANCEMAP) {\r\n            fallbacks.addFallback(fallbackRank++, \"USEIRRADIANCEMAP\");\r\n        }\r\n\r\n        if (defines.LIGHTMAP) {\r\n            fallbacks.addFallback(fallbackRank++, \"LIGHTMAP\");\r\n        }\r\n\r\n        if (defines.NORMAL) {\r\n            fallbacks.addFallback(fallbackRank++, \"NORMAL\");\r\n        }\r\n\r\n        if (defines.AMBIENT) {\r\n            fallbacks.addFallback(fallbackRank++, \"AMBIENT\");\r\n        }\r\n\r\n        if (defines.EMISSIVE) {\r\n            fallbacks.addFallback(fallbackRank++, \"EMISSIVE\");\r\n        }\r\n\r\n        if (defines.VERTEXCOLOR) {\r\n            fallbacks.addFallback(fallbackRank++, \"VERTEXCOLOR\");\r\n        }\r\n\r\n        if (defines.MORPHTARGETS) {\r\n            fallbacks.addFallback(fallbackRank++, \"MORPHTARGETS\");\r\n        }\r\n\r\n        if (defines.MULTIVIEW) {\r\n            fallbacks.addFallback(0, \"MULTIVIEW\");\r\n        }\r\n\r\n        //Attributes\r\n        const attribs = [VertexBuffer.PositionKind];\r\n\r\n        if (defines.NORMAL) {\r\n            attribs.push(VertexBuffer.NormalKind);\r\n        }\r\n\r\n        if (defines.TANGENT) {\r\n            attribs.push(VertexBuffer.TangentKind);\r\n        }\r\n\r\n        for (let i = 1; i <= Constants.MAX_SUPPORTED_UV_SETS; ++i) {\r\n            if (defines[\"UV\" + i]) {\r\n                attribs.push(`uv${i === 1 ? \"\" : i}`);\r\n            }\r\n        }\r\n\r\n        if (defines.VERTEXCOLOR) {\r\n            attribs.push(VertexBuffer.ColorKind);\r\n        }\r\n\r\n        PrepareAttributesForBones(attribs, mesh, defines, fallbacks);\r\n        PrepareAttributesForInstances(attribs, defines);\r\n        PrepareAttributesForMorphTargets(attribs, mesh, defines);\r\n        PrepareAttributesForBakedVertexAnimation(attribs, mesh, defines);\r\n\r\n        let shaderName = \"pbr\";\r\n\r\n        const uniforms = [\r\n            \"world\",\r\n            \"view\",\r\n            \"viewProjection\",\r\n            \"vEyePosition\",\r\n            \"vLightsType\",\r\n            \"vAmbientColor\",\r\n            \"vAlbedoColor\",\r\n            \"vReflectivityColor\",\r\n            \"vMetallicReflectanceFactors\",\r\n            \"vEmissiveColor\",\r\n            \"visibility\",\r\n            \"vReflectionColor\",\r\n            \"vFogInfos\",\r\n            \"vFogColor\",\r\n            \"pointSize\",\r\n            \"vAlbedoInfos\",\r\n            \"vAmbientInfos\",\r\n            \"vOpacityInfos\",\r\n            \"vReflectionInfos\",\r\n            \"vReflectionPosition\",\r\n            \"vReflectionSize\",\r\n            \"vEmissiveInfos\",\r\n            \"vReflectivityInfos\",\r\n            \"vReflectionFilteringInfo\",\r\n            \"vMetallicReflectanceInfos\",\r\n            \"vReflectanceInfos\",\r\n            \"vMicroSurfaceSamplerInfos\",\r\n            \"vBumpInfos\",\r\n            \"vLightmapInfos\",\r\n            \"mBones\",\r\n            \"albedoMatrix\",\r\n            \"ambientMatrix\",\r\n            \"opacityMatrix\",\r\n            \"reflectionMatrix\",\r\n            \"emissiveMatrix\",\r\n            \"reflectivityMatrix\",\r\n            \"normalMatrix\",\r\n            \"microSurfaceSamplerMatrix\",\r\n            \"bumpMatrix\",\r\n            \"lightmapMatrix\",\r\n            \"metallicReflectanceMatrix\",\r\n            \"reflectanceMatrix\",\r\n            \"vLightingIntensity\",\r\n            \"logarithmicDepthConstant\",\r\n            \"vSphericalX\",\r\n            \"vSphericalY\",\r\n            \"vSphericalZ\",\r\n            \"vSphericalXX_ZZ\",\r\n            \"vSphericalYY_ZZ\",\r\n            \"vSphericalZZ\",\r\n            \"vSphericalXY\",\r\n            \"vSphericalYZ\",\r\n            \"vSphericalZX\",\r\n            \"vSphericalL00\",\r\n            \"vSphericalL1_1\",\r\n            \"vSphericalL10\",\r\n            \"vSphericalL11\",\r\n            \"vSphericalL2_2\",\r\n            \"vSphericalL2_1\",\r\n            \"vSphericalL20\",\r\n            \"vSphericalL21\",\r\n            \"vSphericalL22\",\r\n            \"vReflectionMicrosurfaceInfos\",\r\n            \"vTangentSpaceParams\",\r\n            \"boneTextureWidth\",\r\n            \"vDebugMode\",\r\n            \"morphTargetTextureInfo\",\r\n            \"morphTargetTextureIndices\",\r\n        ];\r\n\r\n        const samplers = [\r\n            \"albedoSampler\",\r\n            \"reflectivitySampler\",\r\n            \"ambientSampler\",\r\n            \"emissiveSampler\",\r\n            \"bumpSampler\",\r\n            \"lightmapSampler\",\r\n            \"opacitySampler\",\r\n            \"reflectionSampler\",\r\n            \"reflectionSamplerLow\",\r\n            \"reflectionSamplerHigh\",\r\n            \"irradianceSampler\",\r\n            \"microSurfaceSampler\",\r\n            \"environmentBrdfSampler\",\r\n            \"boneSampler\",\r\n            \"metallicReflectanceSampler\",\r\n            \"reflectanceSampler\",\r\n            \"morphTargets\",\r\n            \"oitDepthSampler\",\r\n            \"oitFrontColorSampler\",\r\n        ];\r\n\r\n        const uniformBuffers = [\"Material\", \"Scene\", \"Mesh\"];\r\n\r\n        const indexParameters = { maxSimultaneousLights: this._maxSimultaneousLights, maxSimultaneousMorphTargets: defines.NUM_MORPH_INFLUENCERS };\r\n\r\n        this._eventInfo.fallbacks = fallbacks;\r\n        this._eventInfo.fallbackRank = fallbackRank;\r\n        this._eventInfo.defines = defines;\r\n        this._eventInfo.uniforms = uniforms;\r\n        this._eventInfo.attributes = attribs;\r\n        this._eventInfo.samplers = samplers;\r\n        this._eventInfo.uniformBuffersNames = uniformBuffers;\r\n        this._eventInfo.customCode = undefined;\r\n        this._eventInfo.mesh = mesh;\r\n        this._eventInfo.indexParameters = indexParameters;\r\n        this._callbackPluginEventGeneric(MaterialPluginEvent.PrepareEffect, this._eventInfo);\r\n\r\n        PrePassConfiguration.AddUniforms(uniforms);\r\n        PrePassConfiguration.AddSamplers(samplers);\r\n        addClipPlaneUniforms(uniforms);\r\n\r\n        if (ImageProcessingConfiguration) {\r\n            ImageProcessingConfiguration.PrepareUniforms(uniforms, defines);\r\n            ImageProcessingConfiguration.PrepareSamplers(samplers, defines);\r\n        }\r\n\r\n        PrepareUniformsAndSamplersList(<IEffectCreationOptions>{\r\n            uniformsNames: uniforms,\r\n            uniformBuffersNames: uniformBuffers,\r\n            samplers: samplers,\r\n            defines: defines,\r\n            maxSimultaneousLights: this._maxSimultaneousLights,\r\n        });\r\n\r\n        const csnrOptions: ICustomShaderNameResolveOptions = {};\r\n\r\n        if (this.customShaderNameResolve) {\r\n            shaderName = this.customShaderNameResolve(shaderName, uniforms, uniformBuffers, samplers, defines, attribs, csnrOptions);\r\n        }\r\n\r\n        const join = defines.toString();\r\n        const effect = engine.createEffect(\r\n            shaderName,\r\n            <IEffectCreationOptions>{\r\n                attributes: attribs,\r\n                uniformsNames: uniforms,\r\n                uniformBuffersNames: uniformBuffers,\r\n                samplers: samplers,\r\n                defines: join,\r\n                fallbacks: fallbacks,\r\n                onCompiled: onCompiled,\r\n                onError: onError,\r\n                indexParameters,\r\n                processFinalCode: csnrOptions.processFinalCode,\r\n                processCodeAfterIncludes: this._eventInfo.customCode,\r\n                multiTarget: defines.PREPASS,\r\n            },\r\n            engine\r\n        );\r\n\r\n        this._eventInfo.customCode = undefined;\r\n\r\n        return effect;\r\n    }\r\n\r\n    private _prepareDefines(\r\n        mesh: AbstractMesh,\r\n        defines: PBRMaterialDefines,\r\n        useInstances: Nullable<boolean> = null,\r\n        useClipPlane: Nullable<boolean> = null,\r\n        useThinInstances: boolean = false\r\n    ): void {\r\n        const scene = this.getScene();\r\n        const engine = scene.getEngine();\r\n\r\n        // Lights\r\n        PrepareDefinesForLights(scene, mesh, defines, true, this._maxSimultaneousLights, this._disableLighting);\r\n        defines._needNormals = true;\r\n\r\n        // Multiview\r\n        PrepareDefinesForMultiview(scene, defines);\r\n\r\n        // PrePass\r\n        const oit = this.needAlphaBlendingForMesh(mesh) && this.getScene().useOrderIndependentTransparency;\r\n        PrepareDefinesForPrePass(scene, defines, this.canRenderToMRT && !oit);\r\n\r\n        // Order independant transparency\r\n        PrepareDefinesForOIT(scene, defines, oit);\r\n\r\n        // Textures\r\n        defines.METALLICWORKFLOW = this.isMetallicWorkflow();\r\n        if (defines._areTexturesDirty) {\r\n            defines._needUVs = false;\r\n            for (let i = 1; i <= Constants.MAX_SUPPORTED_UV_SETS; ++i) {\r\n                defines[\"MAINUV\" + i] = false;\r\n            }\r\n            if (scene.texturesEnabled) {\r\n                defines.ALBEDODIRECTUV = 0;\r\n                defines.AMBIENTDIRECTUV = 0;\r\n                defines.OPACITYDIRECTUV = 0;\r\n                defines.EMISSIVEDIRECTUV = 0;\r\n                defines.REFLECTIVITYDIRECTUV = 0;\r\n                defines.MICROSURFACEMAPDIRECTUV = 0;\r\n                defines.METALLIC_REFLECTANCEDIRECTUV = 0;\r\n                defines.REFLECTANCEDIRECTUV = 0;\r\n                defines.BUMPDIRECTUV = 0;\r\n                defines.LIGHTMAPDIRECTUV = 0;\r\n\r\n                if (engine.getCaps().textureLOD) {\r\n                    defines.LODBASEDMICROSFURACE = true;\r\n                }\r\n\r\n                if (this._albedoTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                    PrepareDefinesForMergedUV(this._albedoTexture, defines, \"ALBEDO\");\r\n                    defines.GAMMAALBEDO = this._albedoTexture.gammaSpace;\r\n                } else {\r\n                    defines.ALBEDO = false;\r\n                }\r\n\r\n                if (this._ambientTexture && MaterialFlags.AmbientTextureEnabled) {\r\n                    PrepareDefinesForMergedUV(this._ambientTexture, defines, \"AMBIENT\");\r\n                    defines.AMBIENTINGRAYSCALE = this._useAmbientInGrayScale;\r\n                } else {\r\n                    defines.AMBIENT = false;\r\n                }\r\n\r\n                if (this._opacityTexture && MaterialFlags.OpacityTextureEnabled) {\r\n                    PrepareDefinesForMergedUV(this._opacityTexture, defines, \"OPACITY\");\r\n                    defines.OPACITYRGB = this._opacityTexture.getAlphaFromRGB;\r\n                } else {\r\n                    defines.OPACITY = false;\r\n                }\r\n\r\n                const reflectionTexture = this._getReflectionTexture();\r\n                if (reflectionTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n                    defines.REFLECTION = true;\r\n                    defines.GAMMAREFLECTION = reflectionTexture.gammaSpace;\r\n                    defines.RGBDREFLECTION = reflectionTexture.isRGBD;\r\n                    defines.LODINREFLECTIONALPHA = reflectionTexture.lodLevelInAlpha;\r\n                    defines.LINEARSPECULARREFLECTION = reflectionTexture.linearSpecularLOD;\r\n\r\n                    if (this.realTimeFiltering && this.realTimeFilteringQuality > 0) {\r\n                        defines.NUM_SAMPLES = \"\" + this.realTimeFilteringQuality;\r\n                        if (engine._features.needTypeSuffixInShaderConstants) {\r\n                            defines.NUM_SAMPLES = defines.NUM_SAMPLES + \"u\";\r\n                        }\r\n\r\n                        defines.REALTIME_FILTERING = true;\r\n                    } else {\r\n                        defines.REALTIME_FILTERING = false;\r\n                    }\r\n\r\n                    defines.INVERTCUBICMAP = reflectionTexture.coordinatesMode === Texture.INVCUBIC_MODE;\r\n                    defines.REFLECTIONMAP_3D = reflectionTexture.isCube;\r\n                    defines.REFLECTIONMAP_OPPOSITEZ = defines.REFLECTIONMAP_3D && this.getScene().useRightHandedSystem ? !reflectionTexture.invertZ : reflectionTexture.invertZ;\r\n\r\n                    defines.REFLECTIONMAP_CUBIC = false;\r\n                    defines.REFLECTIONMAP_EXPLICIT = false;\r\n                    defines.REFLECTIONMAP_PLANAR = false;\r\n                    defines.REFLECTIONMAP_PROJECTION = false;\r\n                    defines.REFLECTIONMAP_SKYBOX = false;\r\n                    defines.REFLECTIONMAP_SPHERICAL = false;\r\n                    defines.REFLECTIONMAP_EQUIRECTANGULAR = false;\r\n                    defines.REFLECTIONMAP_EQUIRECTANGULAR_FIXED = false;\r\n                    defines.REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED = false;\r\n\r\n                    switch (reflectionTexture.coordinatesMode) {\r\n                        case Texture.EXPLICIT_MODE:\r\n                            defines.REFLECTIONMAP_EXPLICIT = true;\r\n                            break;\r\n                        case Texture.PLANAR_MODE:\r\n                            defines.REFLECTIONMAP_PLANAR = true;\r\n                            break;\r\n                        case Texture.PROJECTION_MODE:\r\n                            defines.REFLECTIONMAP_PROJECTION = true;\r\n                            break;\r\n                        case Texture.SKYBOX_MODE:\r\n                            defines.REFLECTIONMAP_SKYBOX = true;\r\n                            break;\r\n                        case Texture.SPHERICAL_MODE:\r\n                            defines.REFLECTIONMAP_SPHERICAL = true;\r\n                            break;\r\n                        case Texture.EQUIRECTANGULAR_MODE:\r\n                            defines.REFLECTIONMAP_EQUIRECTANGULAR = true;\r\n                            break;\r\n                        case Texture.FIXED_EQUIRECTANGULAR_MODE:\r\n                            defines.REFLECTIONMAP_EQUIRECTANGULAR_FIXED = true;\r\n                            break;\r\n                        case Texture.FIXED_EQUIRECTANGULAR_MIRRORED_MODE:\r\n                            defines.REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED = true;\r\n                            break;\r\n                        case Texture.CUBIC_MODE:\r\n                        case Texture.INVCUBIC_MODE:\r\n                        default:\r\n                            defines.REFLECTIONMAP_CUBIC = true;\r\n                            defines.USE_LOCAL_REFLECTIONMAP_CUBIC = (<any>reflectionTexture).boundingBoxSize ? true : false;\r\n                            break;\r\n                    }\r\n\r\n                    if (reflectionTexture.coordinatesMode !== Texture.SKYBOX_MODE) {\r\n                        if (reflectionTexture.irradianceTexture) {\r\n                            defines.USEIRRADIANCEMAP = true;\r\n                            defines.USESPHERICALFROMREFLECTIONMAP = false;\r\n                        }\r\n                        // Assume using spherical polynomial if the reflection texture is a cube map\r\n                        else if (reflectionTexture.isCube) {\r\n                            defines.USESPHERICALFROMREFLECTIONMAP = true;\r\n                            defines.USEIRRADIANCEMAP = false;\r\n                            if (this._forceIrradianceInFragment || this.realTimeFiltering || this._twoSidedLighting || engine.getCaps().maxVaryingVectors <= 8) {\r\n                                defines.USESPHERICALINVERTEX = false;\r\n                            } else {\r\n                                defines.USESPHERICALINVERTEX = true;\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    defines.REFLECTION = false;\r\n                    defines.REFLECTIONMAP_3D = false;\r\n                    defines.REFLECTIONMAP_SPHERICAL = false;\r\n                    defines.REFLECTIONMAP_PLANAR = false;\r\n                    defines.REFLECTIONMAP_CUBIC = false;\r\n                    defines.USE_LOCAL_REFLECTIONMAP_CUBIC = false;\r\n                    defines.REFLECTIONMAP_PROJECTION = false;\r\n                    defines.REFLECTIONMAP_SKYBOX = false;\r\n                    defines.REFLECTIONMAP_EXPLICIT = false;\r\n                    defines.REFLECTIONMAP_EQUIRECTANGULAR = false;\r\n                    defines.REFLECTIONMAP_EQUIRECTANGULAR_FIXED = false;\r\n                    defines.REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED = false;\r\n                    defines.INVERTCUBICMAP = false;\r\n                    defines.USESPHERICALFROMREFLECTIONMAP = false;\r\n                    defines.USEIRRADIANCEMAP = false;\r\n                    defines.USESPHERICALINVERTEX = false;\r\n                    defines.REFLECTIONMAP_OPPOSITEZ = false;\r\n                    defines.LODINREFLECTIONALPHA = false;\r\n                    defines.GAMMAREFLECTION = false;\r\n                    defines.RGBDREFLECTION = false;\r\n                    defines.LINEARSPECULARREFLECTION = false;\r\n                }\r\n\r\n                if (this._lightmapTexture && MaterialFlags.LightmapTextureEnabled) {\r\n                    PrepareDefinesForMergedUV(this._lightmapTexture, defines, \"LIGHTMAP\");\r\n                    defines.USELIGHTMAPASSHADOWMAP = this._useLightmapAsShadowmap;\r\n                    defines.GAMMALIGHTMAP = this._lightmapTexture.gammaSpace;\r\n                    defines.RGBDLIGHTMAP = this._lightmapTexture.isRGBD;\r\n                } else {\r\n                    defines.LIGHTMAP = false;\r\n                }\r\n\r\n                if (this._emissiveTexture && MaterialFlags.EmissiveTextureEnabled) {\r\n                    PrepareDefinesForMergedUV(this._emissiveTexture, defines, \"EMISSIVE\");\r\n                    defines.GAMMAEMISSIVE = this._emissiveTexture.gammaSpace;\r\n                } else {\r\n                    defines.EMISSIVE = false;\r\n                }\r\n\r\n                if (MaterialFlags.SpecularTextureEnabled) {\r\n                    if (this._metallicTexture) {\r\n                        PrepareDefinesForMergedUV(this._metallicTexture, defines, \"REFLECTIVITY\");\r\n                        defines.ROUGHNESSSTOREINMETALMAPALPHA = this._useRoughnessFromMetallicTextureAlpha;\r\n                        defines.ROUGHNESSSTOREINMETALMAPGREEN = !this._useRoughnessFromMetallicTextureAlpha && this._useRoughnessFromMetallicTextureGreen;\r\n                        defines.METALLNESSSTOREINMETALMAPBLUE = this._useMetallnessFromMetallicTextureBlue;\r\n                        defines.AOSTOREINMETALMAPRED = this._useAmbientOcclusionFromMetallicTextureRed;\r\n                        defines.REFLECTIVITY_GAMMA = false;\r\n                    } else if (this._reflectivityTexture) {\r\n                        PrepareDefinesForMergedUV(this._reflectivityTexture, defines, \"REFLECTIVITY\");\r\n                        defines.MICROSURFACEFROMREFLECTIVITYMAP = this._useMicroSurfaceFromReflectivityMapAlpha;\r\n                        defines.MICROSURFACEAUTOMATIC = this._useAutoMicroSurfaceFromReflectivityMap;\r\n                        defines.REFLECTIVITY_GAMMA = this._reflectivityTexture.gammaSpace;\r\n                    } else {\r\n                        defines.REFLECTIVITY = false;\r\n                    }\r\n\r\n                    if (this._metallicReflectanceTexture || this._reflectanceTexture) {\r\n                        const identicalTextures =\r\n                            this._metallicReflectanceTexture !== null &&\r\n                            this._metallicReflectanceTexture._texture === this._reflectanceTexture?._texture &&\r\n                            this._metallicReflectanceTexture.checkTransformsAreIdentical(this._reflectanceTexture);\r\n\r\n                        defines.METALLIC_REFLECTANCE_USE_ALPHA_ONLY = this._useOnlyMetallicFromMetallicReflectanceTexture && !identicalTextures;\r\n                        if (this._metallicReflectanceTexture) {\r\n                            PrepareDefinesForMergedUV(this._metallicReflectanceTexture, defines, \"METALLIC_REFLECTANCE\");\r\n                            defines.METALLIC_REFLECTANCE_GAMMA = this._metallicReflectanceTexture.gammaSpace;\r\n                        } else {\r\n                            defines.METALLIC_REFLECTANCE = false;\r\n                        }\r\n                        if (\r\n                            this._reflectanceTexture &&\r\n                            !identicalTextures &&\r\n                            (!this._metallicReflectanceTexture || (this._metallicReflectanceTexture && this._useOnlyMetallicFromMetallicReflectanceTexture))\r\n                        ) {\r\n                            PrepareDefinesForMergedUV(this._reflectanceTexture, defines, \"REFLECTANCE\");\r\n                            defines.REFLECTANCE_GAMMA = this._reflectanceTexture.gammaSpace;\r\n                        } else {\r\n                            defines.REFLECTANCE = false;\r\n                        }\r\n                    } else {\r\n                        defines.METALLIC_REFLECTANCE = false;\r\n                        defines.REFLECTANCE = false;\r\n                    }\r\n\r\n                    if (this._microSurfaceTexture) {\r\n                        PrepareDefinesForMergedUV(this._microSurfaceTexture, defines, \"MICROSURFACEMAP\");\r\n                    } else {\r\n                        defines.MICROSURFACEMAP = false;\r\n                    }\r\n                } else {\r\n                    defines.REFLECTIVITY = false;\r\n                    defines.MICROSURFACEMAP = false;\r\n                }\r\n\r\n                if (engine.getCaps().standardDerivatives && this._bumpTexture && MaterialFlags.BumpTextureEnabled && !this._disableBumpMap) {\r\n                    PrepareDefinesForMergedUV(this._bumpTexture, defines, \"BUMP\");\r\n\r\n                    if (this._useParallax && this._albedoTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                        defines.PARALLAX = true;\r\n                        defines.PARALLAX_RHS = scene.useRightHandedSystem;\r\n                        defines.PARALLAXOCCLUSION = !!this._useParallaxOcclusion;\r\n                    } else {\r\n                        defines.PARALLAX = false;\r\n                    }\r\n\r\n                    defines.OBJECTSPACE_NORMALMAP = this._useObjectSpaceNormalMap;\r\n                } else {\r\n                    defines.BUMP = false;\r\n                    defines.PARALLAX = false;\r\n                    defines.PARALLAX_RHS = false;\r\n                    defines.PARALLAXOCCLUSION = false;\r\n                    defines.OBJECTSPACE_NORMALMAP = false;\r\n                }\r\n\r\n                if (this._environmentBRDFTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n                    defines.ENVIRONMENTBRDF = true;\r\n                    defines.ENVIRONMENTBRDF_RGBD = this._environmentBRDFTexture.isRGBD;\r\n                } else {\r\n                    defines.ENVIRONMENTBRDF = false;\r\n                    defines.ENVIRONMENTBRDF_RGBD = false;\r\n                }\r\n\r\n                if (this._shouldUseAlphaFromAlbedoTexture()) {\r\n                    defines.ALPHAFROMALBEDO = true;\r\n                } else {\r\n                    defines.ALPHAFROMALBEDO = false;\r\n                }\r\n            }\r\n\r\n            defines.SPECULAROVERALPHA = this._useSpecularOverAlpha;\r\n\r\n            if (this._lightFalloff === PBRBaseMaterial.LIGHTFALLOFF_STANDARD) {\r\n                defines.USEPHYSICALLIGHTFALLOFF = false;\r\n                defines.USEGLTFLIGHTFALLOFF = false;\r\n            } else if (this._lightFalloff === PBRBaseMaterial.LIGHTFALLOFF_GLTF) {\r\n                defines.USEPHYSICALLIGHTFALLOFF = false;\r\n                defines.USEGLTFLIGHTFALLOFF = true;\r\n            } else {\r\n                defines.USEPHYSICALLIGHTFALLOFF = true;\r\n                defines.USEGLTFLIGHTFALLOFF = false;\r\n            }\r\n\r\n            defines.RADIANCEOVERALPHA = this._useRadianceOverAlpha;\r\n\r\n            if (!this.backFaceCulling && this._twoSidedLighting) {\r\n                defines.TWOSIDEDLIGHTING = true;\r\n            } else {\r\n                defines.TWOSIDEDLIGHTING = false;\r\n            }\r\n\r\n            defines.SPECULARAA = engine.getCaps().standardDerivatives && this._enableSpecularAntiAliasing;\r\n        }\r\n\r\n        if (defines._areTexturesDirty || defines._areMiscDirty) {\r\n            defines.ALPHATESTVALUE = `${this._alphaCutOff}${this._alphaCutOff % 1 === 0 ? \".\" : \"\"}`;\r\n            defines.PREMULTIPLYALPHA = this.alphaMode === Constants.ALPHA_PREMULTIPLIED || this.alphaMode === Constants.ALPHA_PREMULTIPLIED_PORTERDUFF;\r\n            defines.ALPHABLEND = this.needAlphaBlendingForMesh(mesh);\r\n            defines.ALPHAFRESNEL = this._useAlphaFresnel || this._useLinearAlphaFresnel;\r\n            defines.LINEARALPHAFRESNEL = this._useLinearAlphaFresnel;\r\n        }\r\n\r\n        if (defines._areImageProcessingDirty && this._imageProcessingConfiguration) {\r\n            this._imageProcessingConfiguration.prepareDefines(defines);\r\n        }\r\n\r\n        defines.FORCENORMALFORWARD = this._forceNormalForward;\r\n\r\n        defines.RADIANCEOCCLUSION = this._useRadianceOcclusion;\r\n\r\n        defines.HORIZONOCCLUSION = this._useHorizonOcclusion;\r\n\r\n        // Misc.\r\n        if (defines._areMiscDirty) {\r\n            PrepareDefinesForMisc(\r\n                mesh,\r\n                scene,\r\n                this._useLogarithmicDepth,\r\n                this.pointsCloud,\r\n                this.fogEnabled,\r\n                this._shouldTurnAlphaTestOn(mesh) || this._forceAlphaTest,\r\n                defines,\r\n                this._applyDecalMapAfterDetailMap\r\n            );\r\n            defines.UNLIT = this._unlit || ((this.pointsCloud || this.wireframe) && !mesh.isVerticesDataPresent(VertexBuffer.NormalKind));\r\n            defines.DEBUGMODE = this._debugMode;\r\n        }\r\n\r\n        // Values that need to be evaluated on every frame\r\n        PrepareDefinesForFrameBoundValues(scene, engine, this, defines, useInstances ? true : false, useClipPlane, useThinInstances);\r\n\r\n        // External config\r\n        this._eventInfo.defines = defines;\r\n        this._eventInfo.mesh = mesh;\r\n        this._callbackPluginEventPrepareDefinesBeforeAttributes(this._eventInfo);\r\n\r\n        // Attribs\r\n        PrepareDefinesForAttributes(mesh, defines, true, true, true, this._transparencyMode !== PBRBaseMaterial.PBRMATERIAL_OPAQUE);\r\n\r\n        // External config\r\n        this._callbackPluginEventPrepareDefines(this._eventInfo);\r\n    }\r\n\r\n    /**\r\n     * Force shader compilation\r\n     * @param mesh - Define the mesh we want to force the compilation for\r\n     * @param onCompiled - Define a callback triggered when the compilation completes\r\n     * @param options - Define the options used to create the compilation\r\n     */\r\n    public forceCompilation(mesh: AbstractMesh, onCompiled?: (material: Material) => void, options?: Partial<IMaterialCompilationOptions>): void {\r\n        const localOptions = {\r\n            clipPlane: false,\r\n            useInstances: false,\r\n            ...options,\r\n        };\r\n\r\n        if (!this._uniformBufferLayoutBuilt) {\r\n            this.buildUniformLayout();\r\n        }\r\n\r\n        this._callbackPluginEventGeneric(MaterialPluginEvent.GetDefineNames, this._eventInfo);\r\n        const defines = new PBRMaterialDefines(this._eventInfo.defineNames);\r\n        const effect = this._prepareEffect(mesh, defines, undefined, undefined, localOptions.useInstances, localOptions.clipPlane, mesh.hasThinInstances)!;\r\n        if (this._onEffectCreatedObservable) {\r\n            onCreatedEffectParameters.effect = effect;\r\n            onCreatedEffectParameters.subMesh = null;\r\n            this._onEffectCreatedObservable.notifyObservers(onCreatedEffectParameters);\r\n        }\r\n        if (effect.isReady()) {\r\n            if (onCompiled) {\r\n                onCompiled(this);\r\n            }\r\n        } else {\r\n            effect.onCompileObservable.add(() => {\r\n                if (onCompiled) {\r\n                    onCompiled(this);\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Initializes the uniform buffer layout for the shader.\r\n     */\r\n    public buildUniformLayout(): void {\r\n        // Order is important !\r\n        const ubo = this._uniformBuffer;\r\n        ubo.addUniform(\"vAlbedoInfos\", 2);\r\n        ubo.addUniform(\"vAmbientInfos\", 4);\r\n        ubo.addUniform(\"vOpacityInfos\", 2);\r\n        ubo.addUniform(\"vEmissiveInfos\", 2);\r\n        ubo.addUniform(\"vLightmapInfos\", 2);\r\n        ubo.addUniform(\"vReflectivityInfos\", 3);\r\n        ubo.addUniform(\"vMicroSurfaceSamplerInfos\", 2);\r\n        ubo.addUniform(\"vReflectionInfos\", 2);\r\n        ubo.addUniform(\"vReflectionFilteringInfo\", 2);\r\n        ubo.addUniform(\"vReflectionPosition\", 3);\r\n        ubo.addUniform(\"vReflectionSize\", 3);\r\n        ubo.addUniform(\"vBumpInfos\", 3);\r\n        ubo.addUniform(\"albedoMatrix\", 16);\r\n        ubo.addUniform(\"ambientMatrix\", 16);\r\n        ubo.addUniform(\"opacityMatrix\", 16);\r\n        ubo.addUniform(\"emissiveMatrix\", 16);\r\n        ubo.addUniform(\"lightmapMatrix\", 16);\r\n        ubo.addUniform(\"reflectivityMatrix\", 16);\r\n        ubo.addUniform(\"microSurfaceSamplerMatrix\", 16);\r\n        ubo.addUniform(\"bumpMatrix\", 16);\r\n        ubo.addUniform(\"vTangentSpaceParams\", 2);\r\n        ubo.addUniform(\"reflectionMatrix\", 16);\r\n\r\n        ubo.addUniform(\"vReflectionColor\", 3);\r\n        ubo.addUniform(\"vAlbedoColor\", 4);\r\n        ubo.addUniform(\"vLightingIntensity\", 4);\r\n\r\n        ubo.addUniform(\"vReflectionMicrosurfaceInfos\", 3);\r\n        ubo.addUniform(\"pointSize\", 1);\r\n        ubo.addUniform(\"vReflectivityColor\", 4);\r\n        ubo.addUniform(\"vEmissiveColor\", 3);\r\n        ubo.addUniform(\"vAmbientColor\", 3);\r\n\r\n        ubo.addUniform(\"vDebugMode\", 2);\r\n\r\n        ubo.addUniform(\"vMetallicReflectanceFactors\", 4);\r\n        ubo.addUniform(\"vMetallicReflectanceInfos\", 2);\r\n        ubo.addUniform(\"metallicReflectanceMatrix\", 16);\r\n        ubo.addUniform(\"vReflectanceInfos\", 2);\r\n        ubo.addUniform(\"reflectanceMatrix\", 16);\r\n\r\n        ubo.addUniform(\"vSphericalL00\", 3);\r\n        ubo.addUniform(\"vSphericalL1_1\", 3);\r\n        ubo.addUniform(\"vSphericalL10\", 3);\r\n        ubo.addUniform(\"vSphericalL11\", 3);\r\n        ubo.addUniform(\"vSphericalL2_2\", 3);\r\n        ubo.addUniform(\"vSphericalL2_1\", 3);\r\n        ubo.addUniform(\"vSphericalL20\", 3);\r\n        ubo.addUniform(\"vSphericalL21\", 3);\r\n        ubo.addUniform(\"vSphericalL22\", 3);\r\n\r\n        ubo.addUniform(\"vSphericalX\", 3);\r\n        ubo.addUniform(\"vSphericalY\", 3);\r\n        ubo.addUniform(\"vSphericalZ\", 3);\r\n        ubo.addUniform(\"vSphericalXX_ZZ\", 3);\r\n        ubo.addUniform(\"vSphericalYY_ZZ\", 3);\r\n        ubo.addUniform(\"vSphericalZZ\", 3);\r\n        ubo.addUniform(\"vSphericalXY\", 3);\r\n        ubo.addUniform(\"vSphericalYZ\", 3);\r\n        ubo.addUniform(\"vSphericalZX\", 3);\r\n\r\n        super.buildUniformLayout();\r\n    }\r\n\r\n    /**\r\n     * Binds the submesh data.\r\n     * @param world - The world matrix.\r\n     * @param mesh - The BJS mesh.\r\n     * @param subMesh - A submesh of the BJS mesh.\r\n     */\r\n    public bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void {\r\n        const scene = this.getScene();\r\n\r\n        const defines = <PBRMaterialDefines>subMesh.materialDefines;\r\n        if (!defines) {\r\n            return;\r\n        }\r\n\r\n        const effect = subMesh.effect;\r\n\r\n        if (!effect) {\r\n            return;\r\n        }\r\n\r\n        this._activeEffect = effect;\r\n\r\n        // Matrices Mesh.\r\n        mesh.getMeshUniformBuffer().bindToEffect(effect, \"Mesh\");\r\n        mesh.transferToEffect(world);\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        // Binding unconditionally\r\n        this._uniformBuffer.bindToEffect(effect, \"Material\");\r\n\r\n        this.prePassConfiguration.bindForSubMesh(this._activeEffect, scene, mesh, world, this.isFrozen);\r\n\r\n        this._eventInfo.subMesh = subMesh;\r\n        this._callbackPluginEventHardBindForSubMesh(this._eventInfo);\r\n\r\n        // Normal Matrix\r\n        if (defines.OBJECTSPACE_NORMALMAP) {\r\n            world.toNormalMatrix(this._normalMatrix);\r\n            this.bindOnlyNormalMatrix(this._normalMatrix);\r\n        }\r\n\r\n        const mustRebind = this._mustRebind(scene, effect, subMesh, mesh.visibility);\r\n\r\n        // Bones\r\n        BindBonesParameters(mesh, this._activeEffect, this.prePassConfiguration);\r\n\r\n        let reflectionTexture: Nullable<BaseTexture> = null;\r\n        const ubo = this._uniformBuffer;\r\n        if (mustRebind) {\r\n            this.bindViewProjection(effect);\r\n            reflectionTexture = this._getReflectionTexture();\r\n\r\n            if (!ubo.useUbo || !this.isFrozen || !ubo.isSync || subMesh._drawWrapper._forceRebindOnNextCall) {\r\n                // Texture uniforms\r\n                if (scene.texturesEnabled) {\r\n                    if (this._albedoTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                        ubo.updateFloat2(\"vAlbedoInfos\", this._albedoTexture.coordinatesIndex, this._albedoTexture.level);\r\n                        BindTextureMatrix(this._albedoTexture, ubo, \"albedo\");\r\n                    }\r\n\r\n                    if (this._ambientTexture && MaterialFlags.AmbientTextureEnabled) {\r\n                        ubo.updateFloat4(\r\n                            \"vAmbientInfos\",\r\n                            this._ambientTexture.coordinatesIndex,\r\n                            this._ambientTexture.level,\r\n                            this._ambientTextureStrength,\r\n                            this._ambientTextureImpactOnAnalyticalLights\r\n                        );\r\n                        BindTextureMatrix(this._ambientTexture, ubo, \"ambient\");\r\n                    }\r\n\r\n                    if (this._opacityTexture && MaterialFlags.OpacityTextureEnabled) {\r\n                        ubo.updateFloat2(\"vOpacityInfos\", this._opacityTexture.coordinatesIndex, this._opacityTexture.level);\r\n                        BindTextureMatrix(this._opacityTexture, ubo, \"opacity\");\r\n                    }\r\n\r\n                    if (reflectionTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n                        ubo.updateMatrix(\"reflectionMatrix\", reflectionTexture.getReflectionTextureMatrix());\r\n                        ubo.updateFloat2(\"vReflectionInfos\", reflectionTexture.level, 0);\r\n\r\n                        if ((<any>reflectionTexture).boundingBoxSize) {\r\n                            const cubeTexture = <CubeTexture>reflectionTexture;\r\n\r\n                            ubo.updateVector3(\"vReflectionPosition\", cubeTexture.boundingBoxPosition);\r\n                            ubo.updateVector3(\"vReflectionSize\", cubeTexture.boundingBoxSize);\r\n                        }\r\n\r\n                        if (this.realTimeFiltering) {\r\n                            const width = reflectionTexture.getSize().width;\r\n                            ubo.updateFloat2(\"vReflectionFilteringInfo\", width, Scalar.Log2(width));\r\n                        }\r\n\r\n                        if (!defines.USEIRRADIANCEMAP) {\r\n                            const polynomials = reflectionTexture.sphericalPolynomial;\r\n                            if (defines.USESPHERICALFROMREFLECTIONMAP && polynomials) {\r\n                                if (defines.SPHERICAL_HARMONICS) {\r\n                                    const preScaledHarmonics = polynomials.preScaledHarmonics;\r\n                                    ubo.updateVector3(\"vSphericalL00\", preScaledHarmonics.l00);\r\n                                    ubo.updateVector3(\"vSphericalL1_1\", preScaledHarmonics.l1_1);\r\n                                    ubo.updateVector3(\"vSphericalL10\", preScaledHarmonics.l10);\r\n                                    ubo.updateVector3(\"vSphericalL11\", preScaledHarmonics.l11);\r\n                                    ubo.updateVector3(\"vSphericalL2_2\", preScaledHarmonics.l2_2);\r\n                                    ubo.updateVector3(\"vSphericalL2_1\", preScaledHarmonics.l2_1);\r\n                                    ubo.updateVector3(\"vSphericalL20\", preScaledHarmonics.l20);\r\n                                    ubo.updateVector3(\"vSphericalL21\", preScaledHarmonics.l21);\r\n                                    ubo.updateVector3(\"vSphericalL22\", preScaledHarmonics.l22);\r\n                                } else {\r\n                                    ubo.updateFloat3(\"vSphericalX\", polynomials.x.x, polynomials.x.y, polynomials.x.z);\r\n                                    ubo.updateFloat3(\"vSphericalY\", polynomials.y.x, polynomials.y.y, polynomials.y.z);\r\n                                    ubo.updateFloat3(\"vSphericalZ\", polynomials.z.x, polynomials.z.y, polynomials.z.z);\r\n                                    ubo.updateFloat3(\r\n                                        \"vSphericalXX_ZZ\",\r\n                                        polynomials.xx.x - polynomials.zz.x,\r\n                                        polynomials.xx.y - polynomials.zz.y,\r\n                                        polynomials.xx.z - polynomials.zz.z\r\n                                    );\r\n                                    ubo.updateFloat3(\r\n                                        \"vSphericalYY_ZZ\",\r\n                                        polynomials.yy.x - polynomials.zz.x,\r\n                                        polynomials.yy.y - polynomials.zz.y,\r\n                                        polynomials.yy.z - polynomials.zz.z\r\n                                    );\r\n                                    ubo.updateFloat3(\"vSphericalZZ\", polynomials.zz.x, polynomials.zz.y, polynomials.zz.z);\r\n                                    ubo.updateFloat3(\"vSphericalXY\", polynomials.xy.x, polynomials.xy.y, polynomials.xy.z);\r\n                                    ubo.updateFloat3(\"vSphericalYZ\", polynomials.yz.x, polynomials.yz.y, polynomials.yz.z);\r\n                                    ubo.updateFloat3(\"vSphericalZX\", polynomials.zx.x, polynomials.zx.y, polynomials.zx.z);\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        ubo.updateFloat3(\r\n                            \"vReflectionMicrosurfaceInfos\",\r\n                            reflectionTexture.getSize().width,\r\n                            reflectionTexture.lodGenerationScale,\r\n                            reflectionTexture.lodGenerationOffset\r\n                        );\r\n                    }\r\n\r\n                    if (this._emissiveTexture && MaterialFlags.EmissiveTextureEnabled) {\r\n                        ubo.updateFloat2(\"vEmissiveInfos\", this._emissiveTexture.coordinatesIndex, this._emissiveTexture.level);\r\n                        BindTextureMatrix(this._emissiveTexture, ubo, \"emissive\");\r\n                    }\r\n\r\n                    if (this._lightmapTexture && MaterialFlags.LightmapTextureEnabled) {\r\n                        ubo.updateFloat2(\"vLightmapInfos\", this._lightmapTexture.coordinatesIndex, this._lightmapTexture.level);\r\n                        BindTextureMatrix(this._lightmapTexture, ubo, \"lightmap\");\r\n                    }\r\n\r\n                    if (MaterialFlags.SpecularTextureEnabled) {\r\n                        if (this._metallicTexture) {\r\n                            ubo.updateFloat3(\"vReflectivityInfos\", this._metallicTexture.coordinatesIndex, this._metallicTexture.level, this._ambientTextureStrength);\r\n                            BindTextureMatrix(this._metallicTexture, ubo, \"reflectivity\");\r\n                        } else if (this._reflectivityTexture) {\r\n                            ubo.updateFloat3(\"vReflectivityInfos\", this._reflectivityTexture.coordinatesIndex, this._reflectivityTexture.level, 1.0);\r\n                            BindTextureMatrix(this._reflectivityTexture, ubo, \"reflectivity\");\r\n                        }\r\n\r\n                        if (this._metallicReflectanceTexture) {\r\n                            ubo.updateFloat2(\"vMetallicReflectanceInfos\", this._metallicReflectanceTexture.coordinatesIndex, this._metallicReflectanceTexture.level);\r\n                            BindTextureMatrix(this._metallicReflectanceTexture, ubo, \"metallicReflectance\");\r\n                        }\r\n\r\n                        if (this._reflectanceTexture && defines.REFLECTANCE) {\r\n                            ubo.updateFloat2(\"vReflectanceInfos\", this._reflectanceTexture.coordinatesIndex, this._reflectanceTexture.level);\r\n                            BindTextureMatrix(this._reflectanceTexture, ubo, \"reflectance\");\r\n                        }\r\n\r\n                        if (this._microSurfaceTexture) {\r\n                            ubo.updateFloat2(\"vMicroSurfaceSamplerInfos\", this._microSurfaceTexture.coordinatesIndex, this._microSurfaceTexture.level);\r\n                            BindTextureMatrix(this._microSurfaceTexture, ubo, \"microSurfaceSampler\");\r\n                        }\r\n                    }\r\n\r\n                    if (this._bumpTexture && engine.getCaps().standardDerivatives && MaterialFlags.BumpTextureEnabled && !this._disableBumpMap) {\r\n                        ubo.updateFloat3(\"vBumpInfos\", this._bumpTexture.coordinatesIndex, this._bumpTexture.level, this._parallaxScaleBias);\r\n                        BindTextureMatrix(this._bumpTexture, ubo, \"bump\");\r\n\r\n                        if (scene._mirroredCameraPosition) {\r\n                            ubo.updateFloat2(\"vTangentSpaceParams\", this._invertNormalMapX ? 1.0 : -1.0, this._invertNormalMapY ? 1.0 : -1.0);\r\n                        } else {\r\n                            ubo.updateFloat2(\"vTangentSpaceParams\", this._invertNormalMapX ? -1.0 : 1.0, this._invertNormalMapY ? -1.0 : 1.0);\r\n                        }\r\n                    }\r\n                }\r\n\r\n                // Point size\r\n                if (this.pointsCloud) {\r\n                    ubo.updateFloat(\"pointSize\", this.pointSize);\r\n                }\r\n\r\n                // Colors\r\n                if (defines.METALLICWORKFLOW) {\r\n                    TmpColors.Color3[0].r = this._metallic === undefined || this._metallic === null ? 1 : this._metallic;\r\n                    TmpColors.Color3[0].g = this._roughness === undefined || this._roughness === null ? 1 : this._roughness;\r\n                    ubo.updateColor4(\"vReflectivityColor\", TmpColors.Color3[0], 1);\r\n\r\n                    const ior = this.subSurface?._indexOfRefraction ?? 1.5;\r\n                    const outsideIOR = 1; // consider air as clear coat and other layers would remap in the shader.\r\n\r\n                    // We are here deriving our default reflectance from a common value for none metallic surface.\r\n                    // Based of the schlick fresnel approximation model\r\n                    // for dielectrics.\r\n                    const f0 = Math.pow((ior - outsideIOR) / (ior + outsideIOR), 2);\r\n\r\n                    // Tweak the default F0 and F90 based on our given setup\r\n                    this._metallicReflectanceColor.scaleToRef(f0 * this._metallicF0Factor, TmpColors.Color3[0]);\r\n                    const metallicF90 = this._metallicF0Factor;\r\n\r\n                    ubo.updateColor4(\"vMetallicReflectanceFactors\", TmpColors.Color3[0], metallicF90);\r\n                } else {\r\n                    ubo.updateColor4(\"vReflectivityColor\", this._reflectivityColor, this._microSurface);\r\n                }\r\n\r\n                ubo.updateColor3(\"vEmissiveColor\", MaterialFlags.EmissiveTextureEnabled ? this._emissiveColor : Color3.BlackReadOnly);\r\n                ubo.updateColor3(\"vReflectionColor\", this._reflectionColor);\r\n                if (!defines.SS_REFRACTION && this.subSurface?._linkRefractionWithTransparency) {\r\n                    ubo.updateColor4(\"vAlbedoColor\", this._albedoColor, 1);\r\n                } else {\r\n                    ubo.updateColor4(\"vAlbedoColor\", this._albedoColor, this.alpha);\r\n                }\r\n\r\n                // Misc\r\n                this._lightingInfos.x = this._directIntensity;\r\n                this._lightingInfos.y = this._emissiveIntensity;\r\n                this._lightingInfos.z = this._environmentIntensity * scene.environmentIntensity;\r\n                this._lightingInfos.w = this._specularIntensity;\r\n\r\n                ubo.updateVector4(\"vLightingIntensity\", this._lightingInfos);\r\n\r\n                // Colors\r\n                scene.ambientColor.multiplyToRef(this._ambientColor, this._globalAmbientColor);\r\n\r\n                ubo.updateColor3(\"vAmbientColor\", this._globalAmbientColor);\r\n\r\n                ubo.updateFloat2(\"vDebugMode\", this.debugLimit, this.debugFactor);\r\n            }\r\n\r\n            // Textures\r\n            if (scene.texturesEnabled) {\r\n                if (this._albedoTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                    ubo.setTexture(\"albedoSampler\", this._albedoTexture);\r\n                }\r\n\r\n                if (this._ambientTexture && MaterialFlags.AmbientTextureEnabled) {\r\n                    ubo.setTexture(\"ambientSampler\", this._ambientTexture);\r\n                }\r\n\r\n                if (this._opacityTexture && MaterialFlags.OpacityTextureEnabled) {\r\n                    ubo.setTexture(\"opacitySampler\", this._opacityTexture);\r\n                }\r\n\r\n                if (reflectionTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n                    if (defines.LODBASEDMICROSFURACE) {\r\n                        ubo.setTexture(\"reflectionSampler\", reflectionTexture);\r\n                    } else {\r\n                        ubo.setTexture(\"reflectionSampler\", reflectionTexture._lodTextureMid || reflectionTexture);\r\n                        ubo.setTexture(\"reflectionSamplerLow\", reflectionTexture._lodTextureLow || reflectionTexture);\r\n                        ubo.setTexture(\"reflectionSamplerHigh\", reflectionTexture._lodTextureHigh || reflectionTexture);\r\n                    }\r\n\r\n                    if (defines.USEIRRADIANCEMAP) {\r\n                        ubo.setTexture(\"irradianceSampler\", reflectionTexture.irradianceTexture);\r\n                    }\r\n                }\r\n\r\n                if (defines.ENVIRONMENTBRDF) {\r\n                    ubo.setTexture(\"environmentBrdfSampler\", this._environmentBRDFTexture);\r\n                }\r\n\r\n                if (this._emissiveTexture && MaterialFlags.EmissiveTextureEnabled) {\r\n                    ubo.setTexture(\"emissiveSampler\", this._emissiveTexture);\r\n                }\r\n\r\n                if (this._lightmapTexture && MaterialFlags.LightmapTextureEnabled) {\r\n                    ubo.setTexture(\"lightmapSampler\", this._lightmapTexture);\r\n                }\r\n\r\n                if (MaterialFlags.SpecularTextureEnabled) {\r\n                    if (this._metallicTexture) {\r\n                        ubo.setTexture(\"reflectivitySampler\", this._metallicTexture);\r\n                    } else if (this._reflectivityTexture) {\r\n                        ubo.setTexture(\"reflectivitySampler\", this._reflectivityTexture);\r\n                    }\r\n\r\n                    if (this._metallicReflectanceTexture) {\r\n                        ubo.setTexture(\"metallicReflectanceSampler\", this._metallicReflectanceTexture);\r\n                    }\r\n\r\n                    if (this._reflectanceTexture && defines.REFLECTANCE) {\r\n                        ubo.setTexture(\"reflectanceSampler\", this._reflectanceTexture);\r\n                    }\r\n\r\n                    if (this._microSurfaceTexture) {\r\n                        ubo.setTexture(\"microSurfaceSampler\", this._microSurfaceTexture);\r\n                    }\r\n                }\r\n\r\n                if (this._bumpTexture && engine.getCaps().standardDerivatives && MaterialFlags.BumpTextureEnabled && !this._disableBumpMap) {\r\n                    ubo.setTexture(\"bumpSampler\", this._bumpTexture);\r\n                }\r\n            }\r\n\r\n            // OIT with depth peeling\r\n            if (this.getScene().useOrderIndependentTransparency && this.needAlphaBlendingForMesh(mesh)) {\r\n                this.getScene().depthPeelingRenderer!.bind(effect);\r\n            }\r\n\r\n            this._eventInfo.subMesh = subMesh;\r\n            this._callbackPluginEventBindForSubMesh(this._eventInfo);\r\n\r\n            // Clip plane\r\n            bindClipPlane(this._activeEffect, this, scene);\r\n\r\n            this.bindEyePosition(effect);\r\n        } else if (scene.getEngine()._features.needToAlwaysBindUniformBuffers) {\r\n            this._needToBindSceneUbo = true;\r\n        }\r\n\r\n        if (mustRebind || !this.isFrozen) {\r\n            // Lights\r\n            if (scene.lightsEnabled && !this._disableLighting) {\r\n                BindLights(scene, mesh, this._activeEffect, defines, this._maxSimultaneousLights);\r\n            }\r\n\r\n            // View\r\n            if (\r\n                (scene.fogEnabled && mesh.applyFog && scene.fogMode !== Scene.FOGMODE_NONE) ||\r\n                reflectionTexture ||\r\n                this.subSurface.refractionTexture ||\r\n                mesh.receiveShadows ||\r\n                defines.PREPASS\r\n            ) {\r\n                this.bindView(effect);\r\n            }\r\n\r\n            // Fog\r\n            BindFogParameters(scene, mesh, this._activeEffect, true);\r\n\r\n            // Morph targets\r\n            if (defines.NUM_MORPH_INFLUENCERS) {\r\n                BindMorphTargetParameters(mesh, this._activeEffect);\r\n            }\r\n\r\n            if (defines.BAKED_VERTEX_ANIMATION_TEXTURE) {\r\n                mesh.bakedVertexAnimationManager?.bind(effect, defines.INSTANCES);\r\n            }\r\n\r\n            // image processing\r\n            this._imageProcessingConfiguration!.bind(this._activeEffect);\r\n\r\n            // Log. depth\r\n            BindLogDepth(defines, this._activeEffect, scene);\r\n        }\r\n\r\n        this._afterBind(mesh, this._activeEffect, subMesh);\r\n\r\n        ubo.update();\r\n    }\r\n\r\n    /**\r\n     * Returns the animatable textures.\r\n     * If material have animatable metallic texture, then reflectivity texture will not be returned, even if it has animations.\r\n     * @returns - Array of animatable textures.\r\n     */\r\n    public getAnimatables(): IAnimatable[] {\r\n        const results = super.getAnimatables();\r\n\r\n        if (this._albedoTexture && this._albedoTexture.animations && this._albedoTexture.animations.length > 0) {\r\n            results.push(this._albedoTexture);\r\n        }\r\n\r\n        if (this._ambientTexture && this._ambientTexture.animations && this._ambientTexture.animations.length > 0) {\r\n            results.push(this._ambientTexture);\r\n        }\r\n\r\n        if (this._opacityTexture && this._opacityTexture.animations && this._opacityTexture.animations.length > 0) {\r\n            results.push(this._opacityTexture);\r\n        }\r\n\r\n        if (this._reflectionTexture && this._reflectionTexture.animations && this._reflectionTexture.animations.length > 0) {\r\n            results.push(this._reflectionTexture);\r\n        }\r\n\r\n        if (this._emissiveTexture && this._emissiveTexture.animations && this._emissiveTexture.animations.length > 0) {\r\n            results.push(this._emissiveTexture);\r\n        }\r\n\r\n        if (this._metallicTexture && this._metallicTexture.animations && this._metallicTexture.animations.length > 0) {\r\n            results.push(this._metallicTexture);\r\n        } else if (this._reflectivityTexture && this._reflectivityTexture.animations && this._reflectivityTexture.animations.length > 0) {\r\n            results.push(this._reflectivityTexture);\r\n        }\r\n\r\n        if (this._bumpTexture && this._bumpTexture.animations && this._bumpTexture.animations.length > 0) {\r\n            results.push(this._bumpTexture);\r\n        }\r\n\r\n        if (this._lightmapTexture && this._lightmapTexture.animations && this._lightmapTexture.animations.length > 0) {\r\n            results.push(this._lightmapTexture);\r\n        }\r\n\r\n        if (this._metallicReflectanceTexture && this._metallicReflectanceTexture.animations && this._metallicReflectanceTexture.animations.length > 0) {\r\n            results.push(this._metallicReflectanceTexture);\r\n        }\r\n\r\n        if (this._reflectanceTexture && this._reflectanceTexture.animations && this._reflectanceTexture.animations.length > 0) {\r\n            results.push(this._reflectanceTexture);\r\n        }\r\n\r\n        if (this._microSurfaceTexture && this._microSurfaceTexture.animations && this._microSurfaceTexture.animations.length > 0) {\r\n            results.push(this._microSurfaceTexture);\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Returns the texture used for reflections.\r\n     * @returns - Reflection texture if present.  Otherwise, returns the environment texture.\r\n     */\r\n    private _getReflectionTexture(): Nullable<BaseTexture> {\r\n        if (this._reflectionTexture) {\r\n            return this._reflectionTexture;\r\n        }\r\n\r\n        return this.getScene().environmentTexture;\r\n    }\r\n\r\n    /**\r\n     * Returns an array of the actively used textures.\r\n     * @returns - Array of BaseTextures\r\n     */\r\n    public getActiveTextures(): BaseTexture[] {\r\n        const activeTextures = super.getActiveTextures();\r\n\r\n        if (this._albedoTexture) {\r\n            activeTextures.push(this._albedoTexture);\r\n        }\r\n\r\n        if (this._ambientTexture) {\r\n            activeTextures.push(this._ambientTexture);\r\n        }\r\n\r\n        if (this._opacityTexture) {\r\n            activeTextures.push(this._opacityTexture);\r\n        }\r\n\r\n        if (this._reflectionTexture) {\r\n            activeTextures.push(this._reflectionTexture);\r\n        }\r\n\r\n        if (this._emissiveTexture) {\r\n            activeTextures.push(this._emissiveTexture);\r\n        }\r\n\r\n        if (this._reflectivityTexture) {\r\n            activeTextures.push(this._reflectivityTexture);\r\n        }\r\n\r\n        if (this._metallicTexture) {\r\n            activeTextures.push(this._metallicTexture);\r\n        }\r\n\r\n        if (this._metallicReflectanceTexture) {\r\n            activeTextures.push(this._metallicReflectanceTexture);\r\n        }\r\n\r\n        if (this._reflectanceTexture) {\r\n            activeTextures.push(this._reflectanceTexture);\r\n        }\r\n\r\n        if (this._microSurfaceTexture) {\r\n            activeTextures.push(this._microSurfaceTexture);\r\n        }\r\n\r\n        if (this._bumpTexture) {\r\n            activeTextures.push(this._bumpTexture);\r\n        }\r\n\r\n        if (this._lightmapTexture) {\r\n            activeTextures.push(this._lightmapTexture);\r\n        }\r\n\r\n        return activeTextures;\r\n    }\r\n\r\n    /**\r\n     * Checks to see if a texture is used in the material.\r\n     * @param texture - Base texture to use.\r\n     * @returns - Boolean specifying if a texture is used in the material.\r\n     */\r\n    public hasTexture(texture: BaseTexture): boolean {\r\n        if (super.hasTexture(texture)) {\r\n            return true;\r\n        }\r\n\r\n        if (this._albedoTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._ambientTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._opacityTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._reflectionTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._emissiveTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._reflectivityTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._metallicTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._metallicReflectanceTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._reflectanceTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._microSurfaceTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._bumpTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._lightmapTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Sets the required values to the prepass renderer.\r\n     * It can't be sets when subsurface scattering of this material is disabled.\r\n     * When scene have ability to enable subsurface prepass effect, it will enable.\r\n     * @returns - If prepass is enabled or not.\r\n     */\r\n    public setPrePassRenderer(): boolean {\r\n        if (!this.subSurface?.isScatteringEnabled) {\r\n            return false;\r\n        }\r\n\r\n        const subSurfaceConfiguration = this.getScene().enableSubSurfaceForPrePass();\r\n        if (subSurfaceConfiguration) {\r\n            subSurfaceConfiguration.enabled = true;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Disposes the resources of the material.\r\n     * @param forceDisposeEffect - Forces the disposal of effects.\r\n     * @param forceDisposeTextures - Forces the disposal of all textures.\r\n     */\r\n    public dispose(forceDisposeEffect?: boolean, forceDisposeTextures?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            if (this._environmentBRDFTexture && this.getScene().environmentBRDFTexture !== this._environmentBRDFTexture) {\r\n                this._environmentBRDFTexture.dispose();\r\n            }\r\n\r\n            this._albedoTexture?.dispose();\r\n            this._ambientTexture?.dispose();\r\n            this._opacityTexture?.dispose();\r\n            this._reflectionTexture?.dispose();\r\n            this._emissiveTexture?.dispose();\r\n            this._metallicTexture?.dispose();\r\n            this._reflectivityTexture?.dispose();\r\n            this._bumpTexture?.dispose();\r\n            this._lightmapTexture?.dispose();\r\n            this._metallicReflectanceTexture?.dispose();\r\n            this._reflectanceTexture?.dispose();\r\n            this._microSurfaceTexture?.dispose();\r\n        }\r\n\r\n        this._renderTargets.dispose();\r\n\r\n        if (this._imageProcessingConfiguration && this._imageProcessingObserver) {\r\n            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);\r\n        }\r\n\r\n        super.dispose(forceDisposeEffect, forceDisposeTextures);\r\n    }\r\n}\r\n"]}