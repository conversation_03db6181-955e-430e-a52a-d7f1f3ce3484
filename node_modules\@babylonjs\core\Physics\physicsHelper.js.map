{"version": 3, "file": "physicsHelper.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Physics/physicsHelper.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAG3D,OAAO,EAAE,YAAY,EAAE,MAAM,kCAAkC,CAAC;AAChE,OAAO,EAAE,cAAc,EAAE,MAAM,oCAAoC,CAAC;AACpE,OAAO,EAAE,GAAG,EAAE,MAAM,gBAAgB,CAAC;AAOrC,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAE9D,MAAM,WAAW;IACb;;;OAGG;IACH,MAAM,CAAC,oBAAoB,CAAC,IAAkB,EAAE,MAAe,EAAE,SAAkB,EAAE,MAAe,EAAE,aAAsB;QACxH,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,CAAC;QAClD,MAAM,aAAa,GAAG,MAAM,EAAE,gBAAgB,EAAE,CAAC;QACjD,IAAI,aAAa,KAAK,CAAC,EAAE;YACrB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YACvC,MAAM,GAAG,GAAG,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACrC,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,WAAW,EAAE;gBAC5B,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACjC,OAAO,IAAI,CAAC;aACf;SACJ;aAAM,IAAI,aAAa,KAAK,CAAC,EAAE;YAC5B,IAAI,CAAC,WAAY,CAAC,yBAAyB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,gBAAgB,CAAC,IAAiB,EAAE,aAAsB;QAC7D,OAAO,CACH,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,iBAAiB,CAAC,MAAM;YAC9D,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC;YACvD,IAAI,CAAC,aAAsB,EAAE,gBAAgB,EAAE,KAAK,CAAC,CACzD,CAAC;IACN,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,gBAAgB,CAAC,KAAc,EAAE,MAAe,EAAE,MAAc,EAAE,MAAc;QACnF,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACvC,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,MAAM,IAAI,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,MAAM,CAAC;IACvH,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,aAAa;IAKtB;;;OAGG;IACH,YAAY,KAAY;QANhB,aAAQ,GAAmB,EAAE,KAAK,EAAE,IAAI,OAAO,EAAE,EAAE,YAAY,EAAE,IAAI,OAAO,EAAE,EAAE,kBAAkB,EAAE,CAAC,EAAE,CAAC;QAO5G,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAErD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,MAAM,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;YACrG,OAAO;SACV;IACL,CAAC;IAED;;;;;;;OAOG;IACI,2BAA2B,CAC9B,MAAe,EACf,oBAAiE,EACjE,QAAiB,EACjB,OAAqC;QAErC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,MAAM,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAC;YAClG,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAsB,IAAI,CAAC,cAAe,CAAC,YAAY,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACpH,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAsB,IAAI,CAAC,cAAe,CAAC,SAAS,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACjH,OAAO,IAAI,CAAC;SACf;QAED,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;YAC1C,MAAM,CAAC,GAAG,oBAAoB,CAAC;YAC/B,oBAAoB,GAAG,IAAI,kCAAkC,EAAE,CAAC;YAChE,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;YAChC,oBAAoB,CAAC,QAAQ,GAAG,QAAQ,IAAI,oBAAoB,CAAC,QAAQ,CAAC;YAC1E,oBAAoB,CAAC,OAAO,GAAG,OAAO,IAAI,oBAAoB,CAAC,OAAO,CAAC;SAC1E;aAAM;YACH,WAAW,GAAG,CAAC,CAAC,CAAC,oBAAoB,CAAC,yBAAyB,IAAI,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;SACnH;QAED,MAAM,KAAK,GAAG,IAAI,2BAA2B,CAAC,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;QAEjF,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;YAC9C,MAAM,yBAAyB,GAAG,KAAK,EAAmC,CAAC;YAC3E,MAAM,SAAS,GAAqB,IAAI,CAAC,cAAe,CAAC,YAAY,EAAE,CAAC;YACxE,SAAS,CAAC,OAAO,CAAC,CAAC,QAAyB,EAAE,EAAE;gBAC5C,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE;oBACtD,OAAO;iBACV;gBAED,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;gBAE3D,IAAI,WAAW,EAAE;oBACb,yBAAyB,CAAC,IAAI,CAAC;wBAC3B,QAAQ,EAAE,QAAQ;wBAClB,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;qBAC7C,CAAC,CAAC;iBACN;YACL,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,gCAAgC,CAAC,yBAAyB,CAAC,CAAC;SACrE;aAAM;YACH,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,IAAiB,EAAE,OAAuB,EAAE,EAAE;gBAC3G,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;YAClF,CAAC,CAAC,CAAC;SACN;QAED,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAErB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;OAOG;IACI,yBAAyB,CAC5B,MAAe,EACf,oBAAiE,EACjE,QAAiB,EACjB,OAAqC;QAErC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,MAAM,CAAC,IAAI,CAAC,0FAA0F,CAAC,CAAC;YACxG,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAsB,IAAI,CAAC,cAAe,CAAC,YAAY,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACpH,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAsB,IAAI,CAAC,cAAe,CAAC,SAAS,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACjH,OAAO,IAAI,CAAC;SACf;QAED,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;YAC1C,MAAM,CAAC,GAAG,oBAAoB,CAAC;YAC/B,oBAAoB,GAAG,IAAI,kCAAkC,EAAE,CAAC;YAChE,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;YAChC,oBAAoB,CAAC,QAAQ,GAAG,QAAQ,IAAI,oBAAoB,CAAC,QAAQ,CAAC;YAC1E,oBAAoB,CAAC,OAAO,GAAG,OAAO,IAAI,oBAAoB,CAAC,OAAO,CAAC;SAC1E;aAAM;YACH,WAAW,GAAG,CAAC,CAAC,CAAC,oBAAoB,CAAC,yBAAyB,IAAI,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;SACnH;QAED,MAAM,KAAK,GAAG,IAAI,2BAA2B,CAAC,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;QAEjF,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;YAC9C,MAAM,yBAAyB,GAAG,KAAK,EAAmC,CAAC;YAC3E,MAAM,SAAS,GAAqB,IAAI,CAAC,cAAe,CAAC,YAAY,EAAE,CAAC;YACxE,SAAS,CAAC,OAAO,CAAC,CAAC,QAAyB,EAAE,EAAE;gBAC5C,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE;oBACtD,OAAO;iBACV;gBAED,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;gBAEzD,IAAI,WAAW,EAAE;oBACb,yBAAyB,CAAC,IAAI,CAAC;wBAC3B,QAAQ,EAAE,QAAQ;wBAClB,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;qBAC7C,CAAC,CAAC;iBACN;YACL,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,gCAAgC,CAAC,yBAAyB,CAAC,CAAC;SACrE;aAAM;YACH,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,IAAiB,EAAE,OAAuB,EAAE,EAAE;gBAC3G,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;YAChF,CAAC,CAAC,CAAC;SACN;QAED,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAErB,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,qBAAqB,CACzB,KAAkC,EAClC,MAAe,EACf,OAAuB,EACvB,WAAoB,EACpB,aAA2F;QAE3F,MAAM,sBAAsB,GAAG,KAAK,EAA+B,CAAC;QACpE,MAAM,MAAM,GAAqB,IAAI,CAAC,cAAe,CAAC,SAAS,EAAE,CAAC;QAClE,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE;YACvB,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,EAAE,aAAa,EAAE,EAAE;gBACjD,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,CAAC,EAAE;oBAC7D,OAAO;iBACV;gBACD,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAE7B,IAAI,WAAW,EAAE;oBACb,sBAAsB,CAAC,IAAI,CAAC;wBACxB,IAAI,EAAE,IAAI;wBACV,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;qBAC7C,CAAC,CAAC;iBACN;YACL,CAAC,CAAC,CAAC;SACN;QAED,KAAK,CAAC,6BAA6B,CAAC,sBAAsB,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;OAOG;IACI,kBAAkB,CACrB,MAAe,EACf,oBAAiE,EACjE,QAAiB,EACjB,OAAqC;QAErC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,MAAM,CAAC,IAAI,CAAC,0FAA0F,CAAC,CAAC;YACxG,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAsB,IAAI,CAAC,cAAe,CAAC,YAAY,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACpH,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAsB,IAAI,CAAC,cAAe,CAAC,SAAS,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACjH,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;YAC1C,MAAM,CAAC,GAAG,oBAAoB,CAAC;YAC/B,oBAAoB,GAAG,IAAI,kCAAkC,EAAE,CAAC;YAChE,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;YAChC,oBAAoB,CAAC,QAAQ,GAAG,QAAQ,IAAI,oBAAoB,CAAC,QAAQ,CAAC;YAC1E,oBAAoB,CAAC,OAAO,GAAG,OAAO,IAAI,oBAAoB,CAAC,OAAO,CAAC;SAC1E;QAED,MAAM,KAAK,GAAG,IAAI,8BAA8B,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,oBAAoB,CAAC,CAAC;QAElG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAErB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;OAQG;IACI,OAAO,CACV,MAAe,EACf,oBAAyD,EACzD,QAAiB,EACjB,MAAe,EACf,WAAgC;QAEhC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,MAAM,CAAC,IAAI,CAAC,0FAA0F,CAAC,CAAC;YACxG,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAsB,IAAI,CAAC,cAAe,CAAC,YAAY,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACpH,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAsB,IAAI,CAAC,cAAe,CAAC,SAAS,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACjH,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;YAC1C,MAAM,CAAC,GAAG,oBAAoB,CAAC;YAC/B,oBAAoB,GAAG,IAAI,0BAA0B,EAAE,CAAC;YACxD,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;YAChC,oBAAoB,CAAC,QAAQ,GAAG,QAAQ,IAAI,oBAAoB,CAAC,QAAQ,CAAC;YAC1E,oBAAoB,CAAC,MAAM,GAAG,MAAM,IAAI,oBAAoB,CAAC,MAAM,CAAC;YACpE,oBAAoB,CAAC,WAAW,GAAG,WAAW,IAAI,oBAAoB,CAAC,WAAW,CAAC;SACtF;QAED,MAAM,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,oBAAoB,CAAC,CAAC;QAEjF,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAErB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,MAAe,EAAE,oBAAwD,EAAE,QAAiB,EAAE,MAAe;QACvH,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,MAAM,CAAC,IAAI,CAAC,0FAA0F,CAAC,CAAC;YACxG,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAsB,IAAI,CAAC,cAAe,CAAC,YAAY,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACpH,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAsB,IAAI,CAAC,cAAe,CAAC,SAAS,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACjH,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;YAC1C,MAAM,CAAC,GAAG,oBAAoB,CAAC;YAC/B,oBAAoB,GAAG,IAAI,yBAAyB,EAAE,CAAC;YACvD,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;YAChC,oBAAoB,CAAC,QAAQ,GAAG,QAAQ,IAAI,oBAAoB,CAAC,QAAQ,CAAC;YAC1E,oBAAoB,CAAC,MAAM,GAAG,MAAM,IAAI,oBAAoB,CAAC,MAAM,CAAC;SACvE;QAED,MAAM,KAAK,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,oBAAoB,CAAC,CAAC;QAEhF,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAErB,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,mBAAmB,CAAC,IAAoB;QAC5C,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;IAClK,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,2BAA2B;IAI7B;;;;OAIG;IACH,YACY,MAAa,EACb,QAA4C;QAD5C,WAAM,GAAN,MAAM,CAAO;QACb,aAAQ,GAAR,QAAQ,CAAoC;QAThD,iBAAY,GAAY,KAAK,CAAC,CAAC,yDAAyD;QAW5F,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,kCAAkC,EAAE,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IACtF,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,OAAO;YACH,MAAM,EAAE,IAAI,CAAC,OAAO;SACvB,CAAC;IACN,CAAC;IAEO,WAAW,CAAC,IAAkB,EAAE,MAAe,EAAE,MAAe,EAAE,IAAoB;QAC1F,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACxC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAEnD,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,eAAe,GAAG,WAAW,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAEpH,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,kBAAkB,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAClE,IAAI,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YAC3C,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,UAAU,GACZ,IAAI,CAAC,QAAQ,CAAC,OAAO,KAAK,2BAA2B,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEvK,sCAAsC;QACtC,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAEnC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC/B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACzC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACI,cAAc,CAAC,IAAiB,EAAE,MAAe,EAAE,IAAoB,EAAE,aAAsB;QAClG,kEAAkE;QAClE,IAAI,WAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,aAAa,CAAC,EAAE;YACnD,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,aAA6B,CAAC;QAChD,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAClE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAClE,CAAC;IACD;;;;;;OAMG;IACI,kBAAkB,CAAC,QAAyB,EAAE,MAAe,EAAE,IAAoB;QACtF,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE;YACrB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,QAAQ,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,eAAe,EAAE;YACjG,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAsB,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACjE,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,oBAAoB,GAAG,QAAQ,CAAC,eAAe,EAAE,CAAC;QAExD,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,gCAAgC,CAAC,yBAAiE;QACrG,IAAI,IAAI,CAAC,QAAQ,CAAC,yBAAyB,EAAE;YACzC,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,yBAAyB,CAAC,CAAC;SACtE;IACL,CAAC;IAED;;;OAGG;IACI,6BAA6B,CAAC,sBAA0D;QAC3F,IAAI,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE;YACtC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;SAChE;IACL,CAAC;IACD;;;OAGG;IACI,OAAO,CAAC,QAAiB,IAAI;QAChC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,KAAK,EAAE;gBACP,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;aAC1B;iBAAM;gBACH,UAAU,CAAC,GAAG,EAAE;oBACZ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;wBACpB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;qBAC1B;gBACL,CAAC,EAAE,CAAC,CAAC,CAAC;aACT;SACJ;IACL,CAAC;IAED,iBAAiB;IAET,cAAc;QAClB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,4BAA4B,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7F,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;SAClC;IACL,CAAC;IAEO,qBAAqB,CAAC,IAAkB,EAAE,MAAe,EAAE,MAAc;QAC7E,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAEtC,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,8BAA8B;IAKhC;;;;;;OAMG;IACH,YACY,cAA6B,EAC7B,MAAa,EACb,OAAgB,EAChB,QAA4C;QAH5C,mBAAc,GAAd,cAAc,CAAe;QAC7B,WAAM,GAAN,MAAM,CAAO;QACb,YAAO,GAAP,OAAO,CAAS;QAChB,aAAQ,GAAR,QAAQ,CAAoC;QAbhD,iBAAY,GAAY,KAAK,CAAC,CAAC,6DAA6D;QAehG,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,kCAAkC,EAAE,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAElF,IAAI,CAAC,aAAa,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QAExC,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,OAAO;YACH,MAAM,EAAE,IAAI,CAAC,OAAO;SACvB,CAAC;IACN,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC3D,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,QAAiB,IAAI;QAChC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QACD,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;aAAM;YACH,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;oBACpB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;iBAC1B;YACL,CAAC,EAAE,CAAC,CAAC,CAAC;SACT;IACL,CAAC;IAEO,KAAK;QACT,8DAA8D;QAC9D,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC9E;aAAM;YACH,MAAM,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxG,IAAI,oBAAoB,EAAE;gBACtB,IAAI,CAAC,OAAO,GAAS,oBAAoB,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,iCAAiC,CAAC,CAAC;aACxG;SACJ;IACL,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,mBAAmB;IASrB;;;;;OAKG;IACH,YACY,MAAa,EACb,OAAgB,EAChB,QAAoC;QAFpC,WAAM,GAAN,MAAM,CAAO;QACb,YAAO,GAAP,OAAO,CAAS;QAChB,aAAQ,GAAR,QAAQ,CAA4B;QAhBxC,eAAU,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,sCAAsC;QAC5E,qBAAgB,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,2CAA2C;QAGvF,sBAAiB,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,qGAAqG;QAClJ,iBAAY,GAAY,KAAK,CAAC,CAAC,6DAA6D;QAahG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAuC,CAAC;QAC1F,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,0BAA0B,EAAE,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE1E,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC3F,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAEhF,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,KAAK,kBAAkB,CAAC,aAAa,EAAE;YAChE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,SAAS,EAAE,CAAC;SAC9E;QAED,IAAI,CAAC,aAAa,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QAExC,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;YAC9C,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;IACL,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,OAAO;YACH,QAAQ,EAAE,IAAI,CAAC,SAAS;SAC3B,CAAC;IACN,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC3D,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,QAAiB,IAAI;QAChC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO;SACV;QACD,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;SAC9B;aAAM;YACH,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,EAAE;oBACtC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;oBACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;iBAC9B;YACL,CAAC,EAAE,CAAC,CAAC,CAAC;SACT;IACL,CAAC;IAEO,WAAW,CAAC,MAAe,EAAE,IAAoB;QACrD,IAAI,SAAkB,CAAC;QACvB,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,KAAK,kBAAkB,CAAC,aAAa,EAAE;YAChE,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC;SACrC;aAAM;YACH,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAChD;QAED,MAAM,kBAAkB,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAElE,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QAE/C,MAAM,KAAK,GAAG,SAAS,CAAC,gBAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAE7E,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACnC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IACjD,CAAC;IAEO,eAAe,CAAC,IAAiB,EAAE,IAAoB,EAAE,aAAsB;QACnF,IAAI,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YACpC,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAExD,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACjG,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,mBAAmB,CAAC,QAAyB,EAAE,IAAoB;QACvE,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE;YACrB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,cAAc,GAAiB,QAAQ,CAAC,MAAM,CAAC;QACrD,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,EAAE;YAC/C,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,eAAe,EAAE,CAAC;QAC1C,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,KAAK;QACT,MAAM,OAAO,GAAG,mBAAmB,CAAC,QAAQ,CAAC;QAC7C,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;YAC5B,IAAI,CAAC,cAAe,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,CAAC,QAAyB,EAAE,EAAE;gBACxF,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;oBAC9C,OAAO;iBACV;gBAED,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;SACN;aAAM;YACH,KAAK;YACa,IAAI,CAAC,cAAe,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,IAAiB,EAAE,EAAE;gBAC7E,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,EAAE,aAAa,EAAE,EAAE;oBACjD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,CAAC,EAAE;wBACrD,OAAO;qBACV;oBAED,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;gBAChF,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED,iBAAiB;IAET,gBAAgB;QACpB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,cAAc,CAC3B,sBAAsB,EACtB;gBACI,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAC5B,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;aACrC,EACD,IAAI,CAAC,MAAM,CACd,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,KAAK,CAAC;SACpC;IACL,CAAC;IAEO,uBAAuB,CAAC,IAAkB;QAC9C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACjD,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACrD,CAAC;;AA7Kc,4BAAQ,GAAmB,EAAE,KAAK,EAAE,IAAI,OAAO,EAAE,EAAE,YAAY,EAAE,IAAI,OAAO,EAAE,EAAE,kBAAkB,EAAE,CAAC,EAAE,AAA/F,CAAgG;AAgL3H;;GAEG;AACH,MAAM,kBAAkB;IAUpB;;;;;OAKG;IACH,YACY,MAAa,EACb,OAAgB,EAChB,QAAmC;QAFnC,WAAM,GAAN,MAAM,CAAO;QACb,YAAO,GAAP,OAAO,CAAS;QAChB,aAAQ,GAAR,QAAQ,CAA2B;QAjBvC,eAAU,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,sCAAsC;QAG5E,sBAAiB,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,qGAAqG;QAClJ,iBAAY,GAAY,KAAK,CAAC,CAAC,6DAA6D;QAehG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAuC,CAAC;QAC1F,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,yBAAyB,EAAE,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEzE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC3F,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAEhF,IAAI,CAAC,aAAa,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QAExC,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;YAC9C,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;IACL,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,OAAO;YACH,QAAQ,EAAE,IAAI,CAAC,SAAS;SAC3B,CAAC;IACN,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC3D,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,QAAiB,IAAI;QAChC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO;SACV;QACD,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;SAC5B;aAAM;YACH,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;oBACpB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;iBAC5B;YACL,CAAC,EAAE,CAAC,CAAC,CAAC;SACT;IACL,CAAC;IAEO,WAAW,CAAC,IAAkB,EAAE,MAAe,EAAE,IAAoB;QACzE,MAAM,aAAa,GAAG,kBAAkB,CAAC,cAAc,CAAC;QACxD,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,yEAAyE;QACtI,MAAM,yBAAyB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACxD,MAAM,CAAC,aAAa,CAAC,aAAa,EAAE,yBAAyB,CAAC,CAAC;QAE/D,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,eAAe,GAAG,WAAW,CAAC,oBAAoB,CAAC,IAAI,EAAE,aAAa,EAAE,yBAAyB,EAAE,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3I,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO,KAAK,CAAC;SAChB;QACD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QAC/D,MAAM,0BAA0B,GAAG,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QAEnE,MAAM,iBAAiB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChD,YAAY,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;QAC/C,IAAI,0BAA0B,GAAG,IAAI,CAAC,QAAQ,CAAC,yBAAyB,EAAE;YACtE,iBAAiB,CAAC,aAAa,EAAE,CAAC;SACrC;QAED,IAAI,MAAc,CAAC;QACnB,IAAI,MAAc,CAAC;QACnB,IAAI,MAAc,CAAC;QAEnB,IAAI,0BAA0B,GAAG,IAAI,CAAC,QAAQ,CAAC,yBAAyB,EAAE;YACtE,MAAM,GAAG,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,0BAA0B,CAAC;YACxE,MAAM,GAAG,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC;YACpE,MAAM,GAAG,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,0BAA0B,CAAC;SAC3E;aAAM;YACH,MAAM,sBAAsB,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,SAAS,EAAE,CAAC;YAEhF,MAAM,GAAG,CAAC,sBAAsB,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,0BAA0B,CAAC;YACrG,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC;YAClE,MAAM,GAAG,CAAC,sBAAsB,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,0BAA0B,CAAC;SACxG;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAClC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAE3C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACnC,IAAI,CAAC,kBAAkB,GAAG,0BAA0B,CAAC;QACrD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,eAAe,CAAC,IAAiB,EAAE,IAAoB,EAAE,aAAsB;QACnF,IAAI,WAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,aAAa,CAAC,EAAE;YACnD,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,aAA6B,CAAC;QACtD,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAE5D,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACrG,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;IAEO,mBAAmB,CAAC,QAAyB,EAAE,IAAoB;QACvE,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE;YACrB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,QAAQ,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,eAAe,EAAE;YACjG,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAsB,CAAC;QACvD,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,EAAE;YAC/C,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,oBAAoB,GAAG,QAAQ,CAAC,eAAe,EAAE,CAAC;QACxD,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,oBAAoB,EAAE,IAAI,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,KAAK;QACT,MAAM,OAAO,GAAG,kBAAkB,CAAC,QAAQ,CAAC;QAC5C,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;YAC5B,IAAI,CAAC,cAAe,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,CAAC,QAAyB,EAAE,EAAE;gBACxF,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;oBAC9C,OAAO;iBACV;gBAED,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;SACN;aAAM;YACe,IAAI,CAAC,cAAe,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,IAAiB,EAAE,EAAE;gBAC7E,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAiB,EAAE,aAAsB,EAAE,EAAE;oBACvE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,CAAC,EAAE;wBACrD,OAAO;qBACV;oBAED,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;gBAChF,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED,iBAAiB;IAET,gBAAgB;QACpB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,cAAc,CAC3B,qBAAqB,EACrB;gBACI,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAC5B,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;aACrC,EACD,IAAI,CAAC,MAAM,CACd,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,KAAK,CAAC;SACpC;IACL,CAAC;IAEO,uBAAuB,CAAC,IAAkB;QAC9C,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAEjD,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACrD,CAAC;;AArMc,iCAAc,GAAY,OAAO,CAAC,IAAI,EAAE,AAA1B,CAA2B;AACzC,2BAAQ,GAAmB,EAAE,KAAK,EAAE,IAAI,OAAO,EAAE,EAAE,YAAY,EAAE,IAAI,OAAO,EAAE,EAAE,kBAAkB,EAAE,CAAC,EAAE,AAA/F,CAAgG;AAuM3H;;;GAGG;AACH,MAAM,OAAO,kCAAkC;IAA/C;QACI;;WAEG;QACH,WAAM,GAAW,CAAC,CAAC;QAEnB;;WAEG;QACH,aAAQ,GAAW,EAAE,CAAC;QAEtB;;WAEG;QACH,YAAO,GAAgC,2BAA2B,CAAC,QAAQ,CAAC;QAE5E;;WAEG;QACH,WAAM,GAA2C,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;IAWnF,CAAC;CAAA;AAED;;;GAGG;AACH,MAAM,OAAO,0BAA0B;IAAvC;QACI;;WAEG;QACH,WAAM,GAAW,CAAC,CAAC;QAEnB;;WAEG;QACH,aAAQ,GAAW,EAAE,CAAC;QAEtB;;WAEG;QACH,WAAM,GAAW,EAAE,CAAC;QAEpB;;WAEG;QACH,gBAAW,GAAuB,kBAAkB,CAAC,MAAM,CAAC;IAChE,CAAC;CAAA;AAED;;;GAGG;AACH,MAAM,OAAO,yBAAyB;IAAtC;QACI;;WAEG;QACH,WAAM,GAAW,CAAC,CAAC;QAEnB;;WAEG;QACH,aAAQ,GAAW,EAAE,CAAC;QAEtB;;WAEG;QACH,WAAM,GAAW,EAAE,CAAC;QAEpB;;WAEG;QACH,8BAAyB,GAAW,GAAG,CAAC;QAExC;;WAEG;QACH,+BAA0B,GAAW,CAAC,CAAC;QAEvC;;WAEG;QACH,+BAA0B,GAAW,GAAG,CAAC;QAEzC;;WAEG;QACH,2BAAsB,GAAW,IAAI,CAAC;IAC1C,CAAC;CAAA;AAED;;;GAGG;AACH,MAAM,CAAN,IAAY,2BAKX;AALD,WAAY,2BAA2B;IACnC,4EAA4E;IAC5E,qFAAQ,CAAA;IACR,uEAAuE;IACvE,iFAAM,CAAA;AACV,CAAC,EALW,2BAA2B,KAA3B,2BAA2B,QAKtC;AAED;;;GAGG;AACH,MAAM,CAAN,IAAY,kBAKX;AALD,WAAY,kBAAkB;IAC1B,wFAAwF;IACxF,+DAAM,CAAA;IACN,2HAA2H;IAC3H,6EAAa,CAAA;AACjB,CAAC,EALW,kBAAkB,KAAlB,kBAAkB,QAK7B", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { TmpVectors, Vector3 } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport { CreateSphere } from \"../Meshes/Builders/sphereBuilder\";\r\nimport { CreateCylinder } from \"../Meshes/Builders/cylinderBuilder\";\r\nimport { Ray } from \"../Culling/ray\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { PhysicsEngine as PhysicsEngineV1 } from \"./physicsEngine\";\r\nimport type { PhysicsEngine as PhysicsEngineV2 } from \"./v2/physicsEngine\";\r\nimport type { IPhysicsEngine } from \"./IPhysicsEngine\";\r\nimport type { PhysicsImpostor } from \"./v1/physicsImpostor\";\r\nimport type { PhysicsBody } from \"./v2/physicsBody\";\r\nimport { PhysicsMotionType } from \"./v2/IPhysicsEnginePlugin\";\r\n\r\nclass HelperTools {\r\n    /*\r\n     * Gets the hit contact point between a mesh and a ray. The method varies between\r\n     * the different plugin versions; V1 uses a mesh intersection, V2 uses the physics body instance/object center (to avoid a raycast and improve perf).\r\n     */\r\n    static GetContactPointToRef(mesh: AbstractMesh, origin: Vector3, direction: Vector3, result: Vector3, instanceIndex?: number): boolean {\r\n        const engine = mesh.getScene().getPhysicsEngine();\r\n        const pluginVersion = engine?.getPluginVersion();\r\n        if (pluginVersion === 1) {\r\n            const ray = new Ray(origin, direction);\r\n            const hit = ray.intersectsMesh(mesh);\r\n            if (hit.hit && hit.pickedPoint) {\r\n                result.copyFrom(hit.pickedPoint);\r\n                return true;\r\n            }\r\n        } else if (pluginVersion === 2) {\r\n            mesh.physicsBody!.getObjectCenterWorldToRef(result, instanceIndex);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Checks if a body will be affected by forces\r\n     * @param body the body to check\r\n     * @param instanceIndex for instanced bodies, the index of the instance to check\r\n     * @returns\r\n     */\r\n    static HasAppliedForces(body: PhysicsBody, instanceIndex?: number) {\r\n        return (\r\n            body.getMotionType(instanceIndex) === PhysicsMotionType.STATIC ||\r\n            (body.getMassProperties(instanceIndex)?.mass ?? 0) === 0 ||\r\n            (body.transformNode as Mesh)?.getTotalVertices() === 0\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Checks if a point is inside a cylinder\r\n     * @param point point to check\r\n     * @param origin cylinder origin on the bottom\r\n     * @param radius cylinder radius\r\n     * @param height cylinder height\r\n     * @returns\r\n     */\r\n    static IsInsideCylinder(point: Vector3, origin: Vector3, radius: number, height: number): boolean {\r\n        const distance = TmpVectors.Vector3[0];\r\n        point.subtractToRef(origin, distance);\r\n        return Math.abs(distance.x) <= radius && Math.abs(distance.z) <= radius && distance.y >= 0 && distance.y <= height;\r\n    }\r\n}\r\n\r\n/**\r\n * A helper for physics simulations\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine#further-functionality-of-the-impostor-class\r\n */\r\nexport class PhysicsHelper {\r\n    private _scene: Scene;\r\n    private _physicsEngine: Nullable<IPhysicsEngine>;\r\n    private _hitData: PhysicsHitData = { force: new Vector3(), contactPoint: new Vector3(), distanceFromOrigin: 0 };\r\n\r\n    /**\r\n     * Initializes the Physics helper\r\n     * @param scene Babylon.js scene\r\n     */\r\n    constructor(scene: Scene) {\r\n        this._scene = scene;\r\n        this._physicsEngine = this._scene.getPhysicsEngine();\r\n\r\n        if (!this._physicsEngine) {\r\n            Logger.Warn(\"Physics engine not enabled. Please enable the physics before you can use the methods.\");\r\n            return;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Applies a radial explosion impulse\r\n     * @param origin the origin of the explosion\r\n     * @param radiusOrEventOptions the radius or the options of radial explosion\r\n     * @param strength the explosion strength\r\n     * @param falloff possible options: Constant & Linear. Defaults to Constant\r\n     * @returns A physics radial explosion event, or null\r\n     */\r\n    public applyRadialExplosionImpulse(\r\n        origin: Vector3,\r\n        radiusOrEventOptions: number | PhysicsRadialExplosionEventOptions,\r\n        strength?: number,\r\n        falloff?: PhysicsRadialImpulseFalloff\r\n    ): Nullable<PhysicsRadialExplosionEvent> {\r\n        if (!this._physicsEngine) {\r\n            Logger.Warn(\"Physics engine not enabled. Please enable the physics before you call this method.\");\r\n            return null;\r\n        }\r\n\r\n        if (this._physicsEngine.getPluginVersion() === 1 && (<PhysicsEngineV1>this._physicsEngine).getImpostors().length === 0) {\r\n            return null;\r\n        }\r\n\r\n        if (this._physicsEngine.getPluginVersion() === 2 && (<PhysicsEngineV2>this._physicsEngine).getBodies().length === 0) {\r\n            return null;\r\n        }\r\n\r\n        let useCallback = false;\r\n        if (typeof radiusOrEventOptions === \"number\") {\r\n            const r = radiusOrEventOptions;\r\n            radiusOrEventOptions = new PhysicsRadialExplosionEventOptions();\r\n            radiusOrEventOptions.radius = r;\r\n            radiusOrEventOptions.strength = strength ?? radiusOrEventOptions.strength;\r\n            radiusOrEventOptions.falloff = falloff ?? radiusOrEventOptions.falloff;\r\n        } else {\r\n            useCallback = !!(radiusOrEventOptions.affectedImpostorsCallback || radiusOrEventOptions.affectedBodiesCallback);\r\n        }\r\n\r\n        const event = new PhysicsRadialExplosionEvent(this._scene, radiusOrEventOptions);\r\n\r\n        const hitData = this._hitData;\r\n        if (this._physicsEngine.getPluginVersion() === 1) {\r\n            const affectedImpostorsWithData = Array<PhysicsAffectedImpostorWithData>();\r\n            const impostors = (<PhysicsEngineV1>this._physicsEngine).getImpostors();\r\n            impostors.forEach((impostor: PhysicsImpostor) => {\r\n                if (!event.getImpostorHitData(impostor, origin, hitData)) {\r\n                    return;\r\n                }\r\n\r\n                impostor.applyImpulse(hitData.force, hitData.contactPoint);\r\n\r\n                if (useCallback) {\r\n                    affectedImpostorsWithData.push({\r\n                        impostor: impostor,\r\n                        hitData: this._copyPhysicsHitData(hitData),\r\n                    });\r\n                }\r\n            });\r\n\r\n            event.triggerAffectedImpostorsCallback(affectedImpostorsWithData);\r\n        } else {\r\n            this._applicationForBodies(event, origin, hitData, useCallback, (body: PhysicsBody, hitData: PhysicsHitData) => {\r\n                body.applyImpulse(hitData.force, hitData.contactPoint, hitData.instanceIndex);\r\n            });\r\n        }\r\n\r\n        event.dispose(false);\r\n\r\n        return event;\r\n    }\r\n\r\n    /**\r\n     * Applies a radial explosion force\r\n     * @param origin the origin of the explosion\r\n     * @param radiusOrEventOptions the radius or the options of radial explosion\r\n     * @param strength the explosion strength\r\n     * @param falloff possible options: Constant & Linear. Defaults to Constant\r\n     * @returns A physics radial explosion event, or null\r\n     */\r\n    public applyRadialExplosionForce(\r\n        origin: Vector3,\r\n        radiusOrEventOptions: number | PhysicsRadialExplosionEventOptions,\r\n        strength?: number,\r\n        falloff?: PhysicsRadialImpulseFalloff\r\n    ): Nullable<PhysicsRadialExplosionEvent> {\r\n        if (!this._physicsEngine) {\r\n            Logger.Warn(\"Physics engine not enabled. Please enable the physics before you call the PhysicsHelper.\");\r\n            return null;\r\n        }\r\n\r\n        if (this._physicsEngine.getPluginVersion() === 1 && (<PhysicsEngineV1>this._physicsEngine).getImpostors().length === 0) {\r\n            return null;\r\n        }\r\n\r\n        if (this._physicsEngine.getPluginVersion() === 2 && (<PhysicsEngineV2>this._physicsEngine).getBodies().length === 0) {\r\n            return null;\r\n        }\r\n\r\n        let useCallback = false;\r\n        if (typeof radiusOrEventOptions === \"number\") {\r\n            const r = radiusOrEventOptions;\r\n            radiusOrEventOptions = new PhysicsRadialExplosionEventOptions();\r\n            radiusOrEventOptions.radius = r;\r\n            radiusOrEventOptions.strength = strength ?? radiusOrEventOptions.strength;\r\n            radiusOrEventOptions.falloff = falloff ?? radiusOrEventOptions.falloff;\r\n        } else {\r\n            useCallback = !!(radiusOrEventOptions.affectedImpostorsCallback || radiusOrEventOptions.affectedBodiesCallback);\r\n        }\r\n\r\n        const event = new PhysicsRadialExplosionEvent(this._scene, radiusOrEventOptions);\r\n\r\n        const hitData = this._hitData;\r\n        if (this._physicsEngine.getPluginVersion() === 1) {\r\n            const affectedImpostorsWithData = Array<PhysicsAffectedImpostorWithData>();\r\n            const impostors = (<PhysicsEngineV1>this._physicsEngine).getImpostors();\r\n            impostors.forEach((impostor: PhysicsImpostor) => {\r\n                if (!event.getImpostorHitData(impostor, origin, hitData)) {\r\n                    return;\r\n                }\r\n\r\n                impostor.applyForce(hitData.force, hitData.contactPoint);\r\n\r\n                if (useCallback) {\r\n                    affectedImpostorsWithData.push({\r\n                        impostor: impostor,\r\n                        hitData: this._copyPhysicsHitData(hitData),\r\n                    });\r\n                }\r\n            });\r\n\r\n            event.triggerAffectedImpostorsCallback(affectedImpostorsWithData);\r\n        } else {\r\n            this._applicationForBodies(event, origin, hitData, useCallback, (body: PhysicsBody, hitData: PhysicsHitData) => {\r\n                body.applyForce(hitData.force, hitData.contactPoint, hitData.instanceIndex);\r\n            });\r\n        }\r\n\r\n        event.dispose(false);\r\n\r\n        return event;\r\n    }\r\n\r\n    private _applicationForBodies(\r\n        event: PhysicsRadialExplosionEvent,\r\n        origin: Vector3,\r\n        hitData: PhysicsHitData,\r\n        useCallback: boolean,\r\n        fnApplication: (body: PhysicsBody, hitData: PhysicsHitData, instanceIndex?: number) => void\r\n    ) {\r\n        const affectedBodiesWithData = Array<PhysicsAffectedBodyWithData>();\r\n        const bodies = (<PhysicsEngineV2>this._physicsEngine).getBodies();\r\n        for (const body of bodies) {\r\n            body.iterateOverAllInstances((body, instanceIndex) => {\r\n                if (!event.getBodyHitData(body, origin, hitData, instanceIndex)) {\r\n                    return;\r\n                }\r\n                fnApplication(body, hitData);\r\n\r\n                if (useCallback) {\r\n                    affectedBodiesWithData.push({\r\n                        body: body,\r\n                        hitData: this._copyPhysicsHitData(hitData),\r\n                    });\r\n                }\r\n            });\r\n        }\r\n\r\n        event.triggerAffectedBodiesCallback(affectedBodiesWithData);\r\n    }\r\n\r\n    /**\r\n     * Creates a gravitational field\r\n     * @param origin the origin of the gravitational field\r\n     * @param radiusOrEventOptions the radius or the options of radial gravitational field\r\n     * @param strength the gravitational field strength\r\n     * @param falloff possible options: Constant & Linear. Defaults to Constant\r\n     * @returns A physics gravitational field event, or null\r\n     */\r\n    public gravitationalField(\r\n        origin: Vector3,\r\n        radiusOrEventOptions: number | PhysicsRadialExplosionEventOptions,\r\n        strength?: number,\r\n        falloff?: PhysicsRadialImpulseFalloff\r\n    ): Nullable<PhysicsGravitationalFieldEvent> {\r\n        if (!this._physicsEngine) {\r\n            Logger.Warn(\"Physics engine not enabled. Please enable the physics before you call the PhysicsHelper.\");\r\n            return null;\r\n        }\r\n\r\n        if (this._physicsEngine.getPluginVersion() === 1 && (<PhysicsEngineV1>this._physicsEngine).getImpostors().length === 0) {\r\n            return null;\r\n        }\r\n\r\n        if (this._physicsEngine.getPluginVersion() === 2 && (<PhysicsEngineV2>this._physicsEngine).getBodies().length === 0) {\r\n            return null;\r\n        }\r\n\r\n        if (typeof radiusOrEventOptions === \"number\") {\r\n            const r = radiusOrEventOptions;\r\n            radiusOrEventOptions = new PhysicsRadialExplosionEventOptions();\r\n            radiusOrEventOptions.radius = r;\r\n            radiusOrEventOptions.strength = strength ?? radiusOrEventOptions.strength;\r\n            radiusOrEventOptions.falloff = falloff ?? radiusOrEventOptions.falloff;\r\n        }\r\n\r\n        const event = new PhysicsGravitationalFieldEvent(this, this._scene, origin, radiusOrEventOptions);\r\n\r\n        event.dispose(false);\r\n\r\n        return event;\r\n    }\r\n\r\n    /**\r\n     * Creates a physics updraft event\r\n     * @param origin the origin of the updraft\r\n     * @param radiusOrEventOptions the radius or the options of the updraft\r\n     * @param strength the strength of the updraft\r\n     * @param height the height of the updraft\r\n     * @param updraftMode possible options: Center & Perpendicular. Defaults to Center\r\n     * @returns A physics updraft event, or null\r\n     */\r\n    public updraft(\r\n        origin: Vector3,\r\n        radiusOrEventOptions: number | PhysicsUpdraftEventOptions,\r\n        strength?: number,\r\n        height?: number,\r\n        updraftMode?: PhysicsUpdraftMode\r\n    ): Nullable<PhysicsUpdraftEvent> {\r\n        if (!this._physicsEngine) {\r\n            Logger.Warn(\"Physics engine not enabled. Please enable the physics before you call the PhysicsHelper.\");\r\n            return null;\r\n        }\r\n\r\n        if (this._physicsEngine.getPluginVersion() === 1 && (<PhysicsEngineV1>this._physicsEngine).getImpostors().length === 0) {\r\n            return null;\r\n        }\r\n\r\n        if (this._physicsEngine.getPluginVersion() === 2 && (<PhysicsEngineV2>this._physicsEngine).getBodies().length === 0) {\r\n            return null;\r\n        }\r\n\r\n        if (typeof radiusOrEventOptions === \"number\") {\r\n            const r = radiusOrEventOptions;\r\n            radiusOrEventOptions = new PhysicsUpdraftEventOptions();\r\n            radiusOrEventOptions.radius = r;\r\n            radiusOrEventOptions.strength = strength ?? radiusOrEventOptions.strength;\r\n            radiusOrEventOptions.height = height ?? radiusOrEventOptions.height;\r\n            radiusOrEventOptions.updraftMode = updraftMode ?? radiusOrEventOptions.updraftMode;\r\n        }\r\n\r\n        const event = new PhysicsUpdraftEvent(this._scene, origin, radiusOrEventOptions);\r\n\r\n        event.dispose(false);\r\n\r\n        return event;\r\n    }\r\n\r\n    /**\r\n     * Creates a physics vortex event\r\n     * @param origin the of the vortex\r\n     * @param radiusOrEventOptions the radius or the options of the vortex\r\n     * @param strength the strength of the vortex\r\n     * @param height   the height of the vortex\r\n     * @returns a Physics vortex event, or null\r\n     * A physics vortex event or null\r\n     */\r\n    public vortex(origin: Vector3, radiusOrEventOptions: number | PhysicsVortexEventOptions, strength?: number, height?: number): Nullable<PhysicsVortexEvent> {\r\n        if (!this._physicsEngine) {\r\n            Logger.Warn(\"Physics engine not enabled. Please enable the physics before you call the PhysicsHelper.\");\r\n            return null;\r\n        }\r\n\r\n        if (this._physicsEngine.getPluginVersion() === 1 && (<PhysicsEngineV1>this._physicsEngine).getImpostors().length === 0) {\r\n            return null;\r\n        }\r\n\r\n        if (this._physicsEngine.getPluginVersion() === 2 && (<PhysicsEngineV2>this._physicsEngine).getBodies().length === 0) {\r\n            return null;\r\n        }\r\n\r\n        if (typeof radiusOrEventOptions === \"number\") {\r\n            const r = radiusOrEventOptions;\r\n            radiusOrEventOptions = new PhysicsVortexEventOptions();\r\n            radiusOrEventOptions.radius = r;\r\n            radiusOrEventOptions.strength = strength ?? radiusOrEventOptions.strength;\r\n            radiusOrEventOptions.height = height ?? radiusOrEventOptions.height;\r\n        }\r\n\r\n        const event = new PhysicsVortexEvent(this._scene, origin, radiusOrEventOptions);\r\n\r\n        event.dispose(false);\r\n\r\n        return event;\r\n    }\r\n\r\n    private _copyPhysicsHitData(data: PhysicsHitData): PhysicsHitData {\r\n        return { force: data.force.clone(), contactPoint: data.contactPoint.clone(), distanceFromOrigin: data.distanceFromOrigin, instanceIndex: data.instanceIndex };\r\n    }\r\n}\r\n\r\n/**\r\n * Represents a physics radial explosion event\r\n */\r\nclass PhysicsRadialExplosionEvent {\r\n    private _sphere: Mesh; // create a sphere, so we can get the intersecting meshes inside\r\n    private _dataFetched: boolean = false; // check if the data has been fetched. If not, do cleanup\r\n\r\n    /**\r\n     * Initializes a radial explosion event\r\n     * @param _scene BabylonJS scene\r\n     * @param _options The options for the vortex event\r\n     */\r\n    constructor(\r\n        private _scene: Scene,\r\n        private _options: PhysicsRadialExplosionEventOptions\r\n    ) {\r\n        this._options = { ...new PhysicsRadialExplosionEventOptions(), ...this._options };\r\n    }\r\n\r\n    /**\r\n     * Returns the data related to the radial explosion event (sphere).\r\n     * @returns The radial explosion event data\r\n     */\r\n    public getData(): PhysicsRadialExplosionEventData {\r\n        this._dataFetched = true;\r\n\r\n        return {\r\n            sphere: this._sphere,\r\n        };\r\n    }\r\n\r\n    private _getHitData(mesh: AbstractMesh, center: Vector3, origin: Vector3, data: PhysicsHitData): boolean {\r\n        const direction = TmpVectors.Vector3[0];\r\n        direction.copyFrom(center).subtractInPlace(origin);\r\n\r\n        const contactPoint = TmpVectors.Vector3[1];\r\n        const hasContactPoint = HelperTools.GetContactPointToRef(mesh, origin, direction, contactPoint, data.instanceIndex);\r\n\r\n        if (!hasContactPoint) {\r\n            return false;\r\n        }\r\n\r\n        const distanceFromOrigin = Vector3.Distance(origin, contactPoint);\r\n        if (distanceFromOrigin > this._options.radius) {\r\n            return false;\r\n        }\r\n\r\n        const multiplier =\r\n            this._options.falloff === PhysicsRadialImpulseFalloff.Constant ? this._options.strength : this._options.strength * (1 - distanceFromOrigin / this._options.radius);\r\n\r\n        // Direction x multiplier equals force\r\n        direction.scaleInPlace(multiplier);\r\n\r\n        data.force.copyFrom(direction);\r\n        data.contactPoint.copyFrom(contactPoint);\r\n        data.distanceFromOrigin = distanceFromOrigin;\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Returns the force and contact point of the body or false, if the body is not affected by the force/impulse.\r\n     * @param body A physics body where the transform node is an AbstractMesh\r\n     * @param origin the origin of the explosion\r\n     * @param data the data of the hit\r\n     * @param instanceIndex the instance index of the body\r\n     * @returns if there was a hit\r\n     */\r\n    public getBodyHitData(body: PhysicsBody, origin: Vector3, data: PhysicsHitData, instanceIndex?: number): boolean {\r\n        // No force will be applied in these cases, so we skip calculation\r\n        if (HelperTools.HasAppliedForces(body, instanceIndex)) {\r\n            return false;\r\n        }\r\n\r\n        const mesh = body.transformNode as AbstractMesh;\r\n        const bodyObjectCenter = body.getObjectCenterWorld(instanceIndex);\r\n        data.instanceIndex = instanceIndex;\r\n        return this._getHitData(mesh, bodyObjectCenter, origin, data);\r\n    }\r\n    /**\r\n     * Returns the force and contact point of the impostor or false, if the impostor is not affected by the force/impulse.\r\n     * @param impostor A physics imposter\r\n     * @param origin the origin of the explosion\r\n     * @param data the data of the hit\r\n     * @returns A physics force and contact point, or null\r\n     */\r\n    public getImpostorHitData(impostor: PhysicsImpostor, origin: Vector3, data: PhysicsHitData): boolean {\r\n        if (impostor.mass === 0) {\r\n            return false;\r\n        }\r\n\r\n        if (impostor.object.getClassName() !== \"Mesh\" && impostor.object.getClassName() !== \"InstancedMesh\") {\r\n            return false;\r\n        }\r\n\r\n        const mesh = impostor.object as AbstractMesh;\r\n        if (!this._intersectsWithSphere(mesh, origin, this._options.radius)) {\r\n            return false;\r\n        }\r\n\r\n        const impostorObjectCenter = impostor.getObjectCenter();\r\n\r\n        this._getHitData(mesh, impostorObjectCenter, origin, data);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Triggers affected impostors callbacks\r\n     * @param affectedImpostorsWithData defines the list of affected impostors (including associated data)\r\n     */\r\n    public triggerAffectedImpostorsCallback(affectedImpostorsWithData: Array<PhysicsAffectedImpostorWithData>) {\r\n        if (this._options.affectedImpostorsCallback) {\r\n            this._options.affectedImpostorsCallback(affectedImpostorsWithData);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Triggers affected bodies callbacks\r\n     * @param affectedBodiesWithData defines the list of affected bodies (including associated data)\r\n     */\r\n    public triggerAffectedBodiesCallback(affectedBodiesWithData: Array<PhysicsAffectedBodyWithData>) {\r\n        if (this._options.affectedBodiesCallback) {\r\n            this._options.affectedBodiesCallback(affectedBodiesWithData);\r\n        }\r\n    }\r\n    /**\r\n     * Disposes the sphere.\r\n     * @param force Specifies if the sphere should be disposed by force\r\n     */\r\n    public dispose(force: boolean = true) {\r\n        if (this._sphere) {\r\n            if (force) {\r\n                this._sphere.dispose();\r\n            } else {\r\n                setTimeout(() => {\r\n                    if (!this._dataFetched) {\r\n                        this._sphere.dispose();\r\n                    }\r\n                }, 0);\r\n            }\r\n        }\r\n    }\r\n\r\n    /*** Helpers ***/\r\n\r\n    private _prepareSphere(): void {\r\n        if (!this._sphere) {\r\n            this._sphere = CreateSphere(\"radialExplosionEventSphere\", this._options.sphere, this._scene);\r\n            this._sphere.isVisible = false;\r\n        }\r\n    }\r\n\r\n    private _intersectsWithSphere(mesh: AbstractMesh, origin: Vector3, radius: number): boolean {\r\n        this._prepareSphere();\r\n\r\n        this._sphere.position = origin;\r\n        this._sphere.scaling.setAll(radius * 2);\r\n        this._sphere._updateBoundingInfo();\r\n        this._sphere.computeWorldMatrix(true);\r\n\r\n        return this._sphere.intersectsMesh(mesh, true);\r\n    }\r\n}\r\n\r\n/**\r\n * Represents a gravitational field event\r\n */\r\nclass PhysicsGravitationalFieldEvent {\r\n    private _tickCallback: any;\r\n    private _sphere: Mesh;\r\n    private _dataFetched: boolean = false; // check if the has been fetched the data. If not, do cleanup\r\n\r\n    /**\r\n     * Initializes the physics gravitational field event\r\n     * @param _physicsHelper A physics helper\r\n     * @param _scene BabylonJS scene\r\n     * @param _origin The origin position of the gravitational field event\r\n     * @param _options The options for the vortex event\r\n     */\r\n    constructor(\r\n        private _physicsHelper: PhysicsHelper,\r\n        private _scene: Scene,\r\n        private _origin: Vector3,\r\n        private _options: PhysicsRadialExplosionEventOptions\r\n    ) {\r\n        this._options = { ...new PhysicsRadialExplosionEventOptions(), ...this._options };\r\n\r\n        this._tickCallback = () => this._tick();\r\n\r\n        this._options.strength = this._options.strength * -1;\r\n    }\r\n\r\n    /**\r\n     * Returns the data related to the gravitational field event (sphere).\r\n     * @returns A gravitational field event\r\n     */\r\n    public getData(): PhysicsGravitationalFieldEventData {\r\n        this._dataFetched = true;\r\n\r\n        return {\r\n            sphere: this._sphere,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Enables the gravitational field.\r\n     */\r\n    public enable() {\r\n        this._tickCallback.call(this);\r\n        this._scene.registerBeforeRender(this._tickCallback);\r\n    }\r\n\r\n    /**\r\n     * Disables the gravitational field.\r\n     */\r\n    public disable() {\r\n        this._scene.unregisterBeforeRender(this._tickCallback);\r\n    }\r\n\r\n    /**\r\n     * Disposes the sphere.\r\n     * @param force The force to dispose from the gravitational field event\r\n     */\r\n    public dispose(force: boolean = true) {\r\n        if (!this._sphere) {\r\n            return;\r\n        }\r\n        if (force) {\r\n            this._sphere.dispose();\r\n        } else {\r\n            setTimeout(() => {\r\n                if (!this._dataFetched) {\r\n                    this._sphere.dispose();\r\n                }\r\n            }, 0);\r\n        }\r\n    }\r\n\r\n    private _tick() {\r\n        // Since the params won't change, we fetch the event only once\r\n        if (this._sphere) {\r\n            this._physicsHelper.applyRadialExplosionForce(this._origin, this._options);\r\n        } else {\r\n            const radialExplosionEvent = this._physicsHelper.applyRadialExplosionForce(this._origin, this._options);\r\n            if (radialExplosionEvent) {\r\n                this._sphere = <Mesh>radialExplosionEvent.getData().sphere?.clone(\"radialExplosionEventSphereClone\");\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * Represents a physics updraft event\r\n */\r\nclass PhysicsUpdraftEvent {\r\n    private _physicsEngine: PhysicsEngineV1 | PhysicsEngineV2;\r\n    private _originTop: Vector3 = Vector3.Zero(); // the most upper part of the cylinder\r\n    private _originDirection: Vector3 = Vector3.Zero(); // used if the updraftMode is perpendicular\r\n    private _tickCallback: any;\r\n    private _cylinder: Mesh | undefined;\r\n    private _cylinderPosition: Vector3 = Vector3.Zero(); // to keep the cylinders position, because normally the origin is in the center and not on the bottom\r\n    private _dataFetched: boolean = false; // check if the has been fetched the data. If not, do cleanup\r\n    private static _HitData: PhysicsHitData = { force: new Vector3(), contactPoint: new Vector3(), distanceFromOrigin: 0 };\r\n    /**\r\n     * Initializes the physics updraft event\r\n     * @param _scene BabylonJS scene\r\n     * @param _origin The origin position of the updraft\r\n     * @param _options The options for the updraft event\r\n     */\r\n    constructor(\r\n        private _scene: Scene,\r\n        private _origin: Vector3,\r\n        private _options: PhysicsUpdraftEventOptions\r\n    ) {\r\n        this._physicsEngine = this._scene.getPhysicsEngine() as PhysicsEngineV1 | PhysicsEngineV2;\r\n        this._options = { ...new PhysicsUpdraftEventOptions(), ...this._options };\r\n\r\n        this._origin.addToRef(new Vector3(0, this._options.height / 2, 0), this._cylinderPosition);\r\n        this._origin.addToRef(new Vector3(0, this._options.height, 0), this._originTop);\r\n\r\n        if (this._options.updraftMode === PhysicsUpdraftMode.Perpendicular) {\r\n            this._originDirection = this._origin.subtract(this._originTop).normalize();\r\n        }\r\n\r\n        this._tickCallback = () => this._tick();\r\n\r\n        if (this._physicsEngine.getPluginVersion() === 1) {\r\n            this._prepareCylinder();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the data related to the updraft event (cylinder).\r\n     * @returns A physics updraft event\r\n     */\r\n    public getData(): PhysicsUpdraftEventData {\r\n        this._dataFetched = true;\r\n\r\n        return {\r\n            cylinder: this._cylinder,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Enables the updraft.\r\n     */\r\n    public enable() {\r\n        this._tickCallback.call(this);\r\n        this._scene.registerBeforeRender(this._tickCallback);\r\n    }\r\n\r\n    /**\r\n     * Disables the updraft.\r\n     */\r\n    public disable() {\r\n        this._scene.unregisterBeforeRender(this._tickCallback);\r\n    }\r\n\r\n    /**\r\n     * Disposes the cylinder.\r\n     * @param force Specifies if the updraft should be disposed by force\r\n     */\r\n    public dispose(force: boolean = true) {\r\n        if (!this._cylinder) {\r\n            return;\r\n        }\r\n        if (force) {\r\n            this._cylinder.dispose();\r\n            this._cylinder = undefined;\r\n        } else {\r\n            setTimeout(() => {\r\n                if (!this._dataFetched && this._cylinder) {\r\n                    this._cylinder.dispose();\r\n                    this._cylinder = undefined;\r\n                }\r\n            }, 0);\r\n        }\r\n    }\r\n\r\n    private _getHitData(center: Vector3, data: PhysicsHitData): void {\r\n        let direction: Vector3;\r\n        if (this._options.updraftMode === PhysicsUpdraftMode.Perpendicular) {\r\n            direction = this._originDirection;\r\n        } else {\r\n            direction = center.subtract(this._originTop);\r\n        }\r\n\r\n        const distanceFromOrigin = Vector3.Distance(this._origin, center);\r\n\r\n        const multiplier = this._options.strength * -1;\r\n\r\n        const force = direction.multiplyByFloats(multiplier, multiplier, multiplier);\r\n\r\n        data.force.copyFrom(force);\r\n        data.contactPoint.copyFrom(center);\r\n        data.distanceFromOrigin = distanceFromOrigin;\r\n    }\r\n\r\n    private _getBodyHitData(body: PhysicsBody, data: PhysicsHitData, instanceIndex?: number): boolean {\r\n        if (HelperTools.HasAppliedForces(body)) {\r\n            return false;\r\n        }\r\n\r\n        const center = body.getObjectCenterWorld(instanceIndex);\r\n\r\n        if (!HelperTools.IsInsideCylinder(center, this._origin, this._options.radius, this._options.height)) {\r\n            return false;\r\n        }\r\n\r\n        data.instanceIndex = instanceIndex;\r\n        this._getHitData(center, data);\r\n        return true;\r\n    }\r\n\r\n    private _getImpostorHitData(impostor: PhysicsImpostor, data: PhysicsHitData): boolean {\r\n        if (impostor.mass === 0) {\r\n            return false;\r\n        }\r\n\r\n        const impostorObject = <AbstractMesh>impostor.object;\r\n        if (!this._intersectsWithCylinder(impostorObject)) {\r\n            return false;\r\n        }\r\n\r\n        const center = impostor.getObjectCenter();\r\n        this._getHitData(center, data);\r\n        return true;\r\n    }\r\n\r\n    private _tick() {\r\n        const hitData = PhysicsUpdraftEvent._HitData;\r\n        if (this._physicsEngine.getPluginVersion() === 1) {\r\n            (<PhysicsEngineV1>this._physicsEngine).getImpostors().forEach((impostor: PhysicsImpostor) => {\r\n                if (!this._getImpostorHitData(impostor, hitData)) {\r\n                    return;\r\n                }\r\n\r\n                impostor.applyForce(hitData.force, hitData.contactPoint);\r\n            });\r\n        } else {\r\n            // V2\r\n            (<PhysicsEngineV2>this._physicsEngine).getBodies().forEach((body: PhysicsBody) => {\r\n                body.iterateOverAllInstances((body, instanceIndex) => {\r\n                    if (!this._getBodyHitData(body, hitData, instanceIndex)) {\r\n                        return;\r\n                    }\r\n\r\n                    body.applyForce(hitData.force, hitData.contactPoint, hitData.instanceIndex);\r\n                });\r\n            });\r\n        }\r\n    }\r\n\r\n    /*** Helpers ***/\r\n\r\n    private _prepareCylinder(): void {\r\n        if (!this._cylinder) {\r\n            this._cylinder = CreateCylinder(\r\n                \"updraftEventCylinder\",\r\n                {\r\n                    height: this._options.height,\r\n                    diameter: this._options.radius * 2,\r\n                },\r\n                this._scene\r\n            );\r\n            this._cylinder.isVisible = false;\r\n        }\r\n    }\r\n\r\n    private _intersectsWithCylinder(mesh: AbstractMesh): boolean {\r\n        if (!this._cylinder) {\r\n            return false;\r\n        }\r\n        this._cylinder.position = this._cylinderPosition;\r\n        return this._cylinder.intersectsMesh(mesh, true);\r\n    }\r\n}\r\n\r\n/**\r\n * Represents a physics vortex event\r\n */\r\nclass PhysicsVortexEvent {\r\n    private _physicsEngine: PhysicsEngineV1 | PhysicsEngineV2;\r\n    private _originTop: Vector3 = Vector3.Zero(); // the most upper part of the cylinder\r\n    private _tickCallback: any;\r\n    private _cylinder: Mesh;\r\n    private _cylinderPosition: Vector3 = Vector3.Zero(); // to keep the cylinders position, because normally the origin is in the center and not on the bottom\r\n    private _dataFetched: boolean = false; // check if the has been fetched the data. If not, do cleanup\r\n    private static _OriginOnPlane: Vector3 = Vector3.Zero();\r\n    private static _HitData: PhysicsHitData = { force: new Vector3(), contactPoint: new Vector3(), distanceFromOrigin: 0 };\r\n\r\n    /**\r\n     * Initializes the physics vortex event\r\n     * @param _scene The BabylonJS scene\r\n     * @param _origin The origin position of the vortex\r\n     * @param _options The options for the vortex event\r\n     */\r\n    constructor(\r\n        private _scene: Scene,\r\n        private _origin: Vector3,\r\n        private _options: PhysicsVortexEventOptions\r\n    ) {\r\n        this._physicsEngine = this._scene.getPhysicsEngine() as PhysicsEngineV1 | PhysicsEngineV2;\r\n        this._options = { ...new PhysicsVortexEventOptions(), ...this._options };\r\n\r\n        this._origin.addToRef(new Vector3(0, this._options.height / 2, 0), this._cylinderPosition);\r\n        this._origin.addToRef(new Vector3(0, this._options.height, 0), this._originTop);\r\n\r\n        this._tickCallback = () => this._tick();\r\n\r\n        if (this._physicsEngine.getPluginVersion() === 1) {\r\n            this._prepareCylinder();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the data related to the vortex event (cylinder).\r\n     * @returns The physics vortex event data\r\n     */\r\n    public getData(): PhysicsVortexEventData {\r\n        this._dataFetched = true;\r\n\r\n        return {\r\n            cylinder: this._cylinder,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Enables the vortex.\r\n     */\r\n    public enable() {\r\n        this._tickCallback.call(this);\r\n        this._scene.registerBeforeRender(this._tickCallback);\r\n    }\r\n\r\n    /**\r\n     * Disables the cortex.\r\n     */\r\n    public disable() {\r\n        this._scene.unregisterBeforeRender(this._tickCallback);\r\n    }\r\n\r\n    /**\r\n     * Disposes the sphere.\r\n     * @param force\r\n     */\r\n    public dispose(force: boolean = true) {\r\n        if (!this._cylinder) {\r\n            return;\r\n        }\r\n        if (force) {\r\n            this._cylinder.dispose();\r\n        } else {\r\n            setTimeout(() => {\r\n                if (!this._dataFetched) {\r\n                    this._cylinder.dispose();\r\n                }\r\n            }, 0);\r\n        }\r\n    }\r\n\r\n    private _getHitData(mesh: AbstractMesh, center: Vector3, data: PhysicsHitData): boolean {\r\n        const originOnPlane = PhysicsVortexEvent._OriginOnPlane;\r\n        originOnPlane.set(this._origin.x, center.y, this._origin.z); // the distance to the origin as if both objects were on a plane (Y-axis)\r\n        const originToImpostorDirection = TmpVectors.Vector3[0];\r\n        center.subtractToRef(originOnPlane, originToImpostorDirection);\r\n\r\n        const contactPoint = TmpVectors.Vector3[1];\r\n        const hasContactPoint = HelperTools.GetContactPointToRef(mesh, originOnPlane, originToImpostorDirection, contactPoint, data.instanceIndex);\r\n        if (!hasContactPoint) {\r\n            return false;\r\n        }\r\n        const distance = Vector3.Distance(contactPoint, originOnPlane);\r\n        const absoluteDistanceFromOrigin = distance / this._options.radius;\r\n\r\n        const directionToOrigin = TmpVectors.Vector3[2];\r\n        contactPoint.normalizeToRef(directionToOrigin);\r\n        if (absoluteDistanceFromOrigin > this._options.centripetalForceThreshold) {\r\n            directionToOrigin.negateInPlace();\r\n        }\r\n\r\n        let forceX: number;\r\n        let forceY: number;\r\n        let forceZ: number;\r\n\r\n        if (absoluteDistanceFromOrigin > this._options.centripetalForceThreshold) {\r\n            forceX = directionToOrigin.x * this._options.centripetalForceMultiplier;\r\n            forceY = directionToOrigin.y * this._options.updraftForceMultiplier;\r\n            forceZ = directionToOrigin.z * this._options.centripetalForceMultiplier;\r\n        } else {\r\n            const perpendicularDirection = Vector3.Cross(originOnPlane, center).normalize();\r\n\r\n            forceX = (perpendicularDirection.x + directionToOrigin.x) * this._options.centrifugalForceMultiplier;\r\n            forceY = this._originTop.y * this._options.updraftForceMultiplier;\r\n            forceZ = (perpendicularDirection.z + directionToOrigin.z) * this._options.centrifugalForceMultiplier;\r\n        }\r\n\r\n        const force = TmpVectors.Vector3[3];\r\n        force.set(forceX, forceY, forceZ);\r\n        force.scaleInPlace(this._options.strength);\r\n\r\n        data.force.copyFrom(force);\r\n        data.contactPoint.copyFrom(center);\r\n        data.distanceFromOrigin = absoluteDistanceFromOrigin;\r\n        return true;\r\n    }\r\n\r\n    private _getBodyHitData(body: PhysicsBody, data: PhysicsHitData, instanceIndex?: number): boolean {\r\n        if (HelperTools.HasAppliedForces(body, instanceIndex)) {\r\n            return false;\r\n        }\r\n\r\n        const bodyObject = body.transformNode as AbstractMesh;\r\n        const bodyCenter = body.getObjectCenterWorld(instanceIndex);\r\n\r\n        if (!HelperTools.IsInsideCylinder(bodyCenter, this._origin, this._options.radius, this._options.height)) {\r\n            return false;\r\n        }\r\n\r\n        data.instanceIndex = instanceIndex;\r\n        return this._getHitData(bodyObject, bodyCenter, data);\r\n    }\r\n\r\n    private _getImpostorHitData(impostor: PhysicsImpostor, data: PhysicsHitData): boolean {\r\n        if (impostor.mass === 0) {\r\n            return false;\r\n        }\r\n\r\n        if (impostor.object.getClassName() !== \"Mesh\" && impostor.object.getClassName() !== \"InstancedMesh\") {\r\n            return false;\r\n        }\r\n\r\n        const impostorObject = impostor.object as AbstractMesh;\r\n        if (!this._intersectsWithCylinder(impostorObject)) {\r\n            return false;\r\n        }\r\n\r\n        const impostorObjectCenter = impostor.getObjectCenter();\r\n        this._getHitData(impostorObject, impostorObjectCenter, data);\r\n        return true;\r\n    }\r\n\r\n    private _tick() {\r\n        const hitData = PhysicsVortexEvent._HitData;\r\n        if (this._physicsEngine.getPluginVersion() === 1) {\r\n            (<PhysicsEngineV1>this._physicsEngine).getImpostors().forEach((impostor: PhysicsImpostor) => {\r\n                if (!this._getImpostorHitData(impostor, hitData)) {\r\n                    return;\r\n                }\r\n\r\n                impostor.applyForce(hitData.force, hitData.contactPoint);\r\n            });\r\n        } else {\r\n            (<PhysicsEngineV2>this._physicsEngine).getBodies().forEach((body: PhysicsBody) => {\r\n                body.iterateOverAllInstances((body: PhysicsBody, instanceIndex?: number) => {\r\n                    if (!this._getBodyHitData(body, hitData, instanceIndex)) {\r\n                        return;\r\n                    }\r\n\r\n                    body.applyForce(hitData.force, hitData.contactPoint, hitData.instanceIndex);\r\n                });\r\n            });\r\n        }\r\n    }\r\n\r\n    /*** Helpers ***/\r\n\r\n    private _prepareCylinder(): void {\r\n        if (!this._cylinder) {\r\n            this._cylinder = CreateCylinder(\r\n                \"vortexEventCylinder\",\r\n                {\r\n                    height: this._options.height,\r\n                    diameter: this._options.radius * 2,\r\n                },\r\n                this._scene\r\n            );\r\n            this._cylinder.isVisible = false;\r\n        }\r\n    }\r\n\r\n    private _intersectsWithCylinder(mesh: AbstractMesh): boolean {\r\n        this._cylinder.position = this._cylinderPosition;\r\n\r\n        return this._cylinder.intersectsMesh(mesh, true);\r\n    }\r\n}\r\n\r\n/**\r\n * Options fot the radial explosion event\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine#further-functionality-of-the-impostor-class\r\n */\r\nexport class PhysicsRadialExplosionEventOptions {\r\n    /**\r\n     * The radius of the sphere for the radial explosion.\r\n     */\r\n    radius: number = 5;\r\n\r\n    /**\r\n     * The strength of the explosion.\r\n     */\r\n    strength: number = 10;\r\n\r\n    /**\r\n     * The strength of the force in correspondence to the distance of the affected object\r\n     */\r\n    falloff: PhysicsRadialImpulseFalloff = PhysicsRadialImpulseFalloff.Constant;\r\n\r\n    /**\r\n     * Sphere options for the radial explosion.\r\n     */\r\n    sphere: { segments: number; diameter: number } = { segments: 32, diameter: 1 };\r\n\r\n    /**\r\n     * Sphere options for the radial explosion.\r\n     */\r\n    affectedImpostorsCallback: (affectedImpostorsWithData: Array<PhysicsAffectedImpostorWithData>) => void;\r\n\r\n    /**\r\n     * Sphere options for the radial explosion.\r\n     */\r\n    affectedBodiesCallback: (affectedBodiesWithData: Array<PhysicsAffectedBodyWithData>) => void;\r\n}\r\n\r\n/**\r\n * Options fot the updraft event\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine#further-functionality-of-the-impostor-class\r\n */\r\nexport class PhysicsUpdraftEventOptions {\r\n    /**\r\n     * The radius of the cylinder for the vortex\r\n     */\r\n    radius: number = 5;\r\n\r\n    /**\r\n     * The strength of the updraft.\r\n     */\r\n    strength: number = 10;\r\n\r\n    /**\r\n     * The height of the cylinder for the updraft.\r\n     */\r\n    height: number = 10;\r\n\r\n    /**\r\n     * The mode for the updraft.\r\n     */\r\n    updraftMode: PhysicsUpdraftMode = PhysicsUpdraftMode.Center;\r\n}\r\n\r\n/**\r\n * Options fot the vortex event\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine#further-functionality-of-the-impostor-class\r\n */\r\nexport class PhysicsVortexEventOptions {\r\n    /**\r\n     * The radius of the cylinder for the vortex\r\n     */\r\n    radius: number = 5;\r\n\r\n    /**\r\n     * The strength of the vortex.\r\n     */\r\n    strength: number = 10;\r\n\r\n    /**\r\n     * The height of the cylinder for the vortex.\r\n     */\r\n    height: number = 10;\r\n\r\n    /**\r\n     * At which distance, relative to the radius the centripetal forces should kick in? Range: 0-1\r\n     */\r\n    centripetalForceThreshold: number = 0.7;\r\n\r\n    /**\r\n     * This multiplier determines with how much force the objects will be pushed sideways/around the vortex, when below the threshold.\r\n     */\r\n    centripetalForceMultiplier: number = 5;\r\n\r\n    /**\r\n     * This multiplier determines with how much force the objects will be pushed sideways/around the vortex, when above the threshold.\r\n     */\r\n    centrifugalForceMultiplier: number = 0.5;\r\n\r\n    /**\r\n     * This multiplier determines with how much force the objects will be pushed upwards, when in the vortex.\r\n     */\r\n    updraftForceMultiplier: number = 0.02;\r\n}\r\n\r\n/**\r\n * The strength of the force in correspondence to the distance of the affected object\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine#further-functionality-of-the-impostor-class\r\n */\r\nexport enum PhysicsRadialImpulseFalloff {\r\n    /** Defines that impulse is constant in strength across it's whole radius */\r\n    Constant,\r\n    /** Defines that impulse gets weaker if it's further from the origin */\r\n    Linear,\r\n}\r\n\r\n/**\r\n * The strength of the force in correspondence to the distance of the affected object\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine#further-functionality-of-the-impostor-class\r\n */\r\nexport enum PhysicsUpdraftMode {\r\n    /** Defines that the upstream forces will pull towards the top center of the cylinder */\r\n    Center,\r\n    /** Defines that once a impostor is inside the cylinder, it will shoot out perpendicular from the ground of the cylinder */\r\n    Perpendicular,\r\n}\r\n\r\n/**\r\n * Interface for a physics hit data\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine#further-functionality-of-the-impostor-class\r\n */\r\nexport interface PhysicsHitData {\r\n    /**\r\n     * The force applied at the contact point\r\n     */\r\n    force: Vector3;\r\n    /**\r\n     * The contact point\r\n     */\r\n    contactPoint: Vector3;\r\n    /**\r\n     * The distance from the origin to the contact point\r\n     */\r\n    distanceFromOrigin: number;\r\n    /**\r\n     * For an instanced physics body (mesh with thin instances), the index of the thin instance the hit applies to\r\n     */\r\n    instanceIndex?: number;\r\n}\r\n\r\n/**\r\n * Interface for radial explosion event data\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine#further-functionality-of-the-impostor-class\r\n */\r\nexport interface PhysicsRadialExplosionEventData {\r\n    /**\r\n     * A sphere used for the radial explosion event\r\n     */\r\n    sphere: Mesh;\r\n}\r\n\r\n/**\r\n * Interface for gravitational field event data\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine#further-functionality-of-the-impostor-class\r\n */\r\nexport interface PhysicsGravitationalFieldEventData {\r\n    /**\r\n     * A sphere mesh used for the gravitational field event\r\n     */\r\n    sphere: Mesh;\r\n}\r\n\r\n/**\r\n * Interface for updraft event data\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine#further-functionality-of-the-impostor-class\r\n */\r\nexport interface PhysicsUpdraftEventData {\r\n    /**\r\n     * A cylinder used for the updraft event\r\n     */\r\n    cylinder?: Mesh;\r\n}\r\n\r\n/**\r\n * Interface for vortex event data\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine#further-functionality-of-the-impostor-class\r\n */\r\nexport interface PhysicsVortexEventData {\r\n    /**\r\n     * A cylinder used for the vortex event\r\n     */\r\n    cylinder: Mesh;\r\n}\r\n\r\n/**\r\n * Interface for an affected physics impostor\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/physics/usingPhysicsEngine#further-functionality-of-the-impostor-class\r\n */\r\nexport interface PhysicsAffectedImpostorWithData {\r\n    /**\r\n     * The impostor affected by the effect\r\n     */\r\n    impostor: PhysicsImpostor;\r\n\r\n    /**\r\n     * The data about the hit/force from the explosion\r\n     */\r\n    hitData: PhysicsHitData;\r\n}\r\n\r\n/**\r\n * Interface for an affected physics body\r\n * @see\r\n */\r\nexport interface PhysicsAffectedBodyWithData {\r\n    /**\r\n     * The impostor affected by the effect\r\n     */\r\n    body: PhysicsBody;\r\n\r\n    /**\r\n     * The data about the hit/force from the explosion\r\n     */\r\n    hitData: PhysicsHitData;\r\n}\r\n"]}