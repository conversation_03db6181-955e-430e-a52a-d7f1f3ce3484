{"version": 3, "file": "nullEngine.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Engines/nullEngine.ts"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C,OAAO,EAAE,eAAe,EAAE,qBAAqB,EAAE,MAAM,uCAAuC,CAAC;AAE/F,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAGnD,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AACpE,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAK5D;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAA9B;QACI;;WAEG;QACI,gBAAW,GAAG,GAAG,CAAC;QACzB;;WAEG;QACI,iBAAY,GAAG,GAAG,CAAC;QAE1B;;WAEG;QACI,gBAAW,GAAG,GAAG,CAAC;QAEzB;;;WAGG;QACI,0BAAqB,GAAG,KAAK,CAAC;QAKrC;;;WAGG;QACI,qBAAgB,GAAG,CAAC,CAAC;IAMhC,CAAC;CAAA;AAED;;;GAGG;AACH,MAAM,OAAO,UAAW,SAAQ,MAAM;IAGlC;;;;OAIG;IACI,uBAAuB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC;IAC/C,CAAC;IAED;;;;OAIG;IACI,mBAAmB;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;IAC1C,CAAC;IAED;;;;;OAKG;IACI,uBAAuB;QAC1B,OAAO,GAAG,CAAC;IACf,CAAC;IAED,YAAmB,UAA6B,IAAI,iBAAiB,EAAE;QACnE,KAAK,CAAC,IAAI,CAAC,CAAC;QAEZ,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE5B,IAAI,OAAO,CAAC,qBAAqB,KAAK,SAAS,EAAE;YAC7C,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC;SACzC;QAED,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE;YAChC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;SACrC;QAED,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,EAAE;YACxC,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC;SAChC;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,uBAAuB,CAAC,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAE7E,YAAY;QACZ,gDAAgD;QAEhD,IAAI,CAAC,KAAK,GAAG;YACT,qBAAqB,EAAE,EAAE;YACzB,0BAA0B,EAAE,EAAE;YAC9B,6BAA6B,EAAE,EAAE;YACjC,cAAc,EAAE,GAAG;YACnB,qBAAqB,EAAE,GAAG;YAC1B,oBAAoB,EAAE,GAAG;YACzB,gBAAgB,EAAE,EAAE;YACpB,iBAAiB,EAAE,EAAE;YACrB,yBAAyB,EAAE,EAAE;YAC7B,uBAAuB,EAAE,EAAE;YAC3B,mBAAmB,EAAE,KAAK;YAC1B,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,aAAa,EAAE,CAAC;YAChB,WAAW,EAAE,KAAK;YAClB,sBAAsB,EAAE,KAAK;YAC7B,4BAA4B,EAAE,IAAI;YAClC,gBAAgB,EAAE,KAAK;YACvB,2BAA2B,EAAE,KAAK;YAClC,wBAAwB,EAAE,KAAK;YAC/B,YAAY,EAAE,KAAK;YACnB,2BAA2B,EAAE,KAAK;YAClC,kBAAkB,EAAE,KAAK;YACzB,gBAAgB,EAAE,KAAK;YACvB,+BAA+B,EAAE,KAAK;YACtC,sBAAsB,EAAE,KAAK;YAC7B,UAAU,EAAE,KAAK;YACjB,UAAU,EAAE,KAAK;YACjB,oBAAoB,EAAE,KAAK;YAC3B,qBAAqB,EAAE,KAAK;YAC5B,iBAAiB,EAAE,KAAK;YACxB,eAAe,EAAE,KAAK;YACtB,qBAAqB,EAAE,KAAK;YAC5B,4BAA4B,EAAE,KAAK;YACnC,cAAc,EAAE,CAAC;YACjB,WAAW,EAAE,KAAK;YAClB,kBAAkB,EAAE,KAAK;YACzB,gBAAgB,EAAE,KAAK;YACvB,qBAAqB,EAAE,KAAK;YAC5B,kBAAkB,EAAE,KAAK;YACzB,yBAAyB,EAAE,KAAK;YAChC,eAAe,EAAE,KAAK;YACtB,2BAA2B,EAAE,GAAG;YAChC,yBAAyB,EAAE,KAAK;SACnC,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG;YACb,+BAA+B,EAAE,KAAK;YACtC,yCAAyC,EAAE,KAAK;YAChD,0BAA0B,EAAE,KAAK;YACjC,qBAAqB,EAAE,KAAK;YAC5B,4BAA4B,EAAE,KAAK;YACnC,wBAAwB,EAAE,KAAK;YAC/B,gBAAgB,EAAE,KAAK;YACvB,4BAA4B,EAAE,KAAK;YACnC,UAAU,EAAE,KAAK;YACjB,aAAa,EAAE,KAAK;YACpB,iBAAiB,EAAE,KAAK;YACxB,+BAA+B,EAAE,KAAK;YACtC,WAAW,EAAE,KAAK;YAClB,YAAY,EAAE,KAAK;YACnB,6BAA6B,EAAE,KAAK;YACpC,yBAAyB,EAAE,KAAK;YAChC,sBAAsB,EAAE,KAAK;YAC7B,oBAAoB,EAAE,KAAK;YAC3B,kBAAkB,EAAE,KAAK;YACzB,sBAAsB,EAAE,KAAK;YAC7B,8BAA8B,EAAE,KAAK;YACrC,mBAAmB,EAAE,IAAI;YACzB,uBAAuB,EAAE,KAAK;YAC9B,8CAA8C,EAAE,KAAK;YACrD,0BAA0B,EAAE,KAAK;SACpC,CAAC;QAEF,MAAM,CAAC,GAAG,CAAC,eAAe,MAAM,CAAC,OAAO,gBAAgB,CAAC,CAAC;QAE1D,WAAW;QACX,MAAM,gBAAgB,GAAG,OAAO,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QAC9G,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;YAC5B,gBAAgB,CAAC,GAAG,GAAG;gBACnB,eAAe,EAAE,cAAa,CAAC;gBAC/B,eAAe,EAAE,cAAa,CAAC;aAClC,CAAC;SACL;QACD,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;YAC7B,gBAAgB,CAAC,IAAI,GAAG,cAAa,CAAC,CAAC;SAC1C;IACL,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,QAAoB;QAC1C,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAChC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC;QACtB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,OAAqB;QAC1C,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAChC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC;QACtB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,KAAkB,EAAE,UAAmB,EAAE,KAAc,EAAE,UAAmB,KAAK,IAAS,CAAC;IAExG;;;;OAIG;IACI,cAAc,CAAC,SAAS,GAAG,KAAK;QACnC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;SAC1C;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,SAAS,GAAG,KAAK;QACpC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;SAC3C;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;IACtC,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,QAAuB,EAAE,aAAsB,EAAE,cAAuB;QACvF,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;IACpC,CAAC;IAEM,mBAAmB,CAAC,eAAiC,EAAE,UAAkB,EAAE,YAAoB,EAAE,OAAe,EAAE,OAA+B;QACpJ,OAAO;YACH,gEAAgE;YAChE,wBAAwB,EAAE,IAAI;SACjC,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,eAAiC,EAAE,aAAuB;QACzE,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,eAAiC,EAAE,eAAyB;QAC7E,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,MAAc;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,MAAsC;QACtD,MAAM,GAAG,MAAM,KAAK,IAAI,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,mEAAmE;QAEvJ,IAAI,CAAC,cAAc,GAAG,MAA0B,CAAC;QACjD,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QAED,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SACzB;QACD,IAAI,MAAM,CAAC,iBAAiB,EAAE;YAC1B,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SACpD;IACL,CAAC;IAED;;;;;;;;;OASG;IACI,QAAQ,CACX,OAAgB,EAChB,UAAkB,CAAC,EACnB,KAAe,EACf,WAAW,GAAG,KAAK,EACnB,aAAuB,EACvB,OAAuB,EACvB,eAAuB,CAAC,IACnB,CAAC;IAEV;;;;;OAKG;IACI,WAAW,CAAC,OAA6B,EAAE,KAAiB;QAC/D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,OAA6B,EAAE,KAAiB;QAChE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,OAA6B,EAAE,KAAiB;QAChE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,OAA6B,EAAE,KAAiB;QAChE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,OAA6B,EAAE,KAAmB;QACnE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,OAA6B,EAAE,KAAmB;QACpE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,OAA6B,EAAE,KAAmB;QACpE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,OAA6B,EAAE,KAAmB;QACpE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,OAA6B,EAAE,KAAe;QAC1D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,OAA6B,EAAE,KAAe;QAC3D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,OAA6B,EAAE,KAAe;QAC3D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,OAA6B,EAAE,KAAe;QAC3D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,OAA6B,EAAE,QAAsB;QACpE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,OAA6B,EAAE,MAAoB;QACnE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,OAA6B,EAAE,MAAoB;QACnE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,OAA6B,EAAE,KAAa;QACxD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CAAC,OAA6B,EAAE,CAAS,EAAE,CAAS;QAChE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACI,SAAS,CAAC,OAA6B,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QAC3E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,OAA6B,EAAE,IAAY;QACtD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG;IACI,SAAS,CAAC,OAA6B,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACtF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,IAAY,EAAE,qBAA8B,KAAK;QACjE,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;YAC1B,OAAO;SACV;QAED,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,IAAI,KAAK,SAAS,CAAC,aAAa,CAAC;QAE9D,IAAI,CAAC,kBAAkB,EAAE;YACrB,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,SAAS,CAAC,aAAa,CAAC,CAAC;SACxD;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,aAA8C,EAAE,WAAuB,EAAE,MAAc,IAAS,CAAC;IAEpH;;;;OAIG;IACI,UAAU,CAAC,UAAoB;QAClC,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACpC,OAAO;SACV;QACD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAE5B,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;YACnC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;SAC3B;QAED,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;IAC9C,CAAC;IAED;;;;;;OAMG;IACI,IAAI,CAAC,YAAqB,EAAE,UAAkB,EAAE,UAAkB,EAAE,cAAuB,IAAS,CAAC;IAE5G;;;;;;OAMG;IACI,gBAAgB,CAAC,QAAgB,EAAE,UAAkB,EAAE,UAAkB,EAAE,cAAuB,IAAS,CAAC;IAEnH;;;;;;OAMG;IACI,cAAc,CAAC,QAAgB,EAAE,aAAqB,EAAE,aAAqB,EAAE,cAAuB,IAAS,CAAC;IAEvH,gBAAgB;IACN,cAAc;QACpB,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,OAAwB,IAAS,CAAC;IAEzD;;;;;;;;;;;;;;;;;;;OAmBG;IACI,aAAa,CAChB,MAAwB,EACxB,QAAiB,EACjB,OAAgB,EAChB,KAA2B,EAC3B,eAAuB,SAAS,CAAC,8BAA8B,EAC/D,SAAuD,IAAI,EAC3D,UAA+D,IAAI,EACnE,SAAmG,IAAI,EACvG,WAAsC,IAAI,EAC1C,SAA2B,IAAI,EAC/B,kBAAoC,IAAI,EACxC,QAAiB;QAEjB,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC,GAAG,CAAC,CAAC;QACrE,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAE3B,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;QAClB,OAAO,CAAC,eAAe,GAAG,CAAC,QAAQ,CAAC;QACpC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC9C,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC/C,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC1C,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC3C,IAAI,MAAM,EAAE;YACR,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;SAC3B;QAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QAEvB,IAAI,MAAM,EAAE;YACR,UAAU,CAAC,GAAG,EAAE;gBACZ,MAAM,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1C,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,kCAAkC,CAAC,OAAgB,EAAE,MAAe,EAAE,IAAiE;QAC1I,MAAM,SAAS,GAAG,IAAI,mBAAmB,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACvE,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/C,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;OAKG;IACI,yBAAyB,CAAC,IAAS,EAAE,OAA8C;QACtF,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAE9E,MAAM,WAAW,GAAgC,EAAE,CAAC;QAEpD,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YACtD,WAAW,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;YACtD,WAAW,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC;YACjH,WAAW,CAAC,qBAAqB,GAAG,WAAW,CAAC,mBAAmB,IAAI,OAAO,CAAC,qBAAqB,CAAC;YACrG,WAAW,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;YAClG,WAAW,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;SACnI;aAAM;YACH,WAAW,CAAC,eAAe,GAAY,OAAO,CAAC;YAC/C,WAAW,CAAC,mBAAmB,GAAG,IAAI,CAAC;YACvC,WAAW,CAAC,qBAAqB,GAAG,KAAK,CAAC;YAC1C,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC,wBAAwB,CAAC;YACtD,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,8BAA8B,CAAC;SACvE;QACD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC,YAAY,CAAC,CAAC;QAE9E,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;QAEnC,SAAS,CAAC,oBAAoB,GAAG,WAAW,CAAC,mBAAmB,CAAC;QACjE,SAAS,CAAC,sBAAsB,GAAG,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAEpF,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;QACpB,OAAO,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACrE,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;QAChD,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;QAEhC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1C,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;OAKG;IACI,6BAA6B,CAAC,IAAY,EAAE,OAAqC;QACpF,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAE7E,MAAM,WAAW,GAAG;YAChB,eAAe,EAAE,IAAI;YACrB,mBAAmB,EAAE,IAAI;YACzB,qBAAqB,EAAE,KAAK;YAC5B,IAAI,EAAE,SAAS,CAAC,wBAAwB;YACxC,YAAY,EAAE,SAAS,CAAC,8BAA8B;YACtD,MAAM,EAAE,SAAS,CAAC,kBAAkB;YACpC,GAAG,OAAO;SACb,CAAC;QACF,WAAW,CAAC,qBAAqB,GAAG,WAAW,CAAC,mBAAmB,IAAI,WAAW,CAAC,qBAAqB,CAAC;QAEzG,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE;YAC7F,yEAAyE;YACzE,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;SACrE;aAAM,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE;YAC7G,2EAA2E;YAC3E,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;SACrE;QAED,SAAS,CAAC,oBAAoB,GAAG,WAAW,CAAC,mBAAmB,CAAC;QACjE,SAAS,CAAC,sBAAsB,GAAG,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAEpF,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC,YAAY,CAAC,CAAC;QAC9E,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;QACzB,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;QAC1B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;QACrB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;QACtB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;QACtB,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;QACpB,OAAO,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACrE,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;QAChD,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;QAEhC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1C,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACI,yBAAyB,CAAC,YAAoB,EAAE,OAAwB;QAC3E,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACxC,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACI,gBAAgB,CACnB,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,MAAc,EACd,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI,EACpC,OAAe,SAAS,CAAC,wBAAwB,EACjD,aAAa,GAAG,CAAC,EACjB,aAAa,GAAG,KAAK;QAErB,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,qBAAqB,CAAC,GAAG,CAAC,CAAC;QACrE,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;QAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;QACnC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC;QAEvC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;SAC9B;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;;;;OASG;IACI,gBAAgB,CACnB,OAAkC,EAClC,IAA+B,EAC/B,MAAc,EACd,OAAgB,EAChB,cAAgC,IAAI,EACpC,OAAe,SAAS,CAAC,wBAAwB,EACjD,gBAAyB,KAAK;QAE9B,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;YAC3B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;YACxB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;YAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;YACnC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YACpB,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC;SAC1C;IACL,CAAC;IAED;;;;;;;OAOG;IACI,eAAe,CAAC,SAA8B,EAAE,SAAkB,EAAE,aAAsB,EAAE,cAAuB,EAAE,uBAAiC;QACzJ,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACrD;QACD,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,uBAAuB,EAAE;YAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;SACzE;IACL,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,SAA8B,EAAE,sBAAsB,GAAG,KAAK,EAAE,cAA2B;QAChH,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,IAAI,cAAc,EAAE;YAChB,cAAc,EAAE,CAAC;SACpB;QACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,yBAAyB,CAAC,QAAoB;QACjD,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAChC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC;QACtB,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC;QACpB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;;OAOG;IACI,oBAAoB,CAAC,OAAkC,EAAE,MAAyB,EAAE,OAAgB,EAAE,cAAuB,KAAK,EAAE,MAAe,IAAS,CAAC;IAEpK;;;OAGG;IACI,kBAAkB;QACrB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,QAAQ;QACX,OAAO,CAAC,CAAC;IACb,CAAC;IAED,gBAAgB;IACT,oBAAoB;QACvB,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,KAAc,IAAG,CAAC;IAEtC;;;;;OAKG;IACI,wBAAwB,CAAC,WAAwB,EAAE,OAAqB,EAAE,SAAiB,CAAC,IAAS,CAAC;IAE7G;;;;;;OAMG;IACI,yBAAyB,CAAC,YAAyB,EAAE,QAAoB,EAAE,UAAmB,EAAE,UAAmB,IAAS,CAAC;IAEpI;;OAEG;IACI,oBAAoB,CAAC,MAAc,EAAE,OAAwB;QAChE,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,OAAO,EAAE;YAC3D,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC;YACxD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,OAAe,EAAE,OAAwB;QACzD,IAAI,OAAO,GAAG,CAAC,EAAE;YACb,OAAO;SACV;QAED,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAES,aAAa,CAAC,MAAmB,IAAS,CAAC;IAErD;;OAEG;IACI,cAAc,KAAI,CAAC;IAEnB,gBAAgB,KAAU,CAAC;IAE3B,aAAa,KAAU,CAAC;IAE/B,IAAW,aAAa,CAAC,CAAS,IAAG,CAAC;IAEtC;;OAEG;IACI,sCAAsC,CACzC,OAAwB,EACxB,cAAsB,EACtB,KAAa,EACb,MAAc,EACd,IAAqB,EACrB,YAAoB,CAAC,EACrB,MAAc,CAAC,IAChB,CAAC;IAEJ;;OAEG;IACI,4BAA4B,CAAC,OAAwB,EAAE,SAA0B,EAAE,YAAoB,CAAC,EAAE,MAAc,CAAC,IAAS,CAAC;IAE1I;;OAEG;IACI,+BAA+B,CAAC,OAAwB,EAAE,SAA0B,EAAE,YAAoB,CAAC,EAAE,MAAc,CAAC,IAAS,CAAC;IAE7I;;OAEG;IACI,qBAAqB,CAAC,OAAwB,EAAE,KAAuB,EAAE,YAAoB,CAAC,EAAE,MAAc,CAAC,IAAG,CAAC;CAC7H", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { Logger } from \"../Misc/logger\";\r\nimport type { Nullable, FloatArray, IndicesArray } from \"../types\";\r\nimport { Engine } from \"../Engines/engine\";\r\nimport type { RenderTargetCreationOptions } from \"../Materials/Textures/textureCreationOptions\";\r\nimport type { VertexBuffer } from \"../Buffers/buffer\";\r\nimport { InternalTexture, InternalTextureSource } from \"../Materials/Textures/internalTexture\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Constants } from \"./constants\";\r\nimport type { IPipelineContext } from \"./IPipelineContext\";\r\nimport { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport type { IColor4Like, IViewportLike } from \"../Maths/math.like\";\r\nimport type { ISceneLike } from \"./thinEngine\";\r\nimport { PerformanceConfigurator } from \"./performanceConfigurator\";\r\nimport { DrawWrapper } from \"../Materials/drawWrapper\";\r\nimport { RenderTargetWrapper } from \"./renderTargetWrapper\";\r\nimport type { IStencilState } from \"../States/IStencilState\";\r\n\r\ndeclare const global: any;\r\n\r\n/**\r\n * Options to create the null engine\r\n */\r\nexport class NullEngineOptions {\r\n    /**\r\n     * Render width (Default: 512)\r\n     */\r\n    public renderWidth = 512;\r\n    /**\r\n     * Render height (Default: 256)\r\n     */\r\n    public renderHeight = 256;\r\n\r\n    /**\r\n     * Texture size (Default: 512)\r\n     */\r\n    public textureSize = 512;\r\n\r\n    /**\r\n     * If delta time between frames should be constant\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     */\r\n    public deterministicLockstep = false;\r\n\r\n    /** Defines the seconds between each deterministic lock step */\r\n    timeStep?: number;\r\n\r\n    /**\r\n     * Maximum about of steps between frames (Default: 4)\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     */\r\n    public lockstepMaxSteps = 4;\r\n\r\n    /**\r\n     * Make the matrix computations to be performed in 64 bits instead of 32 bits. False by default\r\n     */\r\n    useHighPrecisionMatrix?: boolean;\r\n}\r\n\r\n/**\r\n * The null engine class provides support for headless version of babylon.js.\r\n * This can be used in server side scenario or for testing purposes\r\n */\r\nexport class NullEngine extends Engine {\r\n    private _options: NullEngineOptions;\r\n\r\n    /**\r\n     * Gets a boolean indicating that the engine is running in deterministic lock step mode\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     * @returns true if engine is in deterministic lock step mode\r\n     */\r\n    public isDeterministicLockStep(): boolean {\r\n        return this._options.deterministicLockstep;\r\n    }\r\n\r\n    /**\r\n     * Gets the max steps when engine is running in deterministic lock step\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     * @returns the max steps\r\n     */\r\n    public getLockstepMaxSteps(): number {\r\n        return this._options.lockstepMaxSteps;\r\n    }\r\n\r\n    /**\r\n     * Gets the current hardware scaling level.\r\n     * By default the hardware scaling level is computed from the window device ratio.\r\n     * if level = 1 then the engine will render at the exact resolution of the canvas. If level = 0.5 then the engine will render at twice the size of the canvas.\r\n     * @returns a number indicating the current hardware scaling level\r\n     */\r\n    public getHardwareScalingLevel(): number {\r\n        return 1.0;\r\n    }\r\n\r\n    public constructor(options: NullEngineOptions = new NullEngineOptions()) {\r\n        super(null);\r\n\r\n        Engine.Instances.push(this);\r\n\r\n        if (options.deterministicLockstep === undefined) {\r\n            options.deterministicLockstep = false;\r\n        }\r\n\r\n        if (options.timeStep !== undefined) {\r\n            this._timeStep = options.timeStep;\r\n        }\r\n\r\n        if (options.lockstepMaxSteps === undefined) {\r\n            options.lockstepMaxSteps = 4;\r\n        }\r\n\r\n        this._options = options;\r\n\r\n        PerformanceConfigurator.SetMatrixPrecision(!!options.useHighPrecisionMatrix);\r\n\r\n        // Init caps\r\n        // We consider we are on a webgl1 capable device\r\n\r\n        this._caps = {\r\n            maxTexturesImageUnits: 16,\r\n            maxVertexTextureImageUnits: 16,\r\n            maxCombinedTexturesImageUnits: 32,\r\n            maxTextureSize: 512,\r\n            maxCubemapTextureSize: 512,\r\n            maxRenderTextureSize: 512,\r\n            maxVertexAttribs: 16,\r\n            maxVaryingVectors: 16,\r\n            maxFragmentUniformVectors: 16,\r\n            maxVertexUniformVectors: 16,\r\n            standardDerivatives: false,\r\n            astc: null,\r\n            pvrtc: null,\r\n            etc1: null,\r\n            etc2: null,\r\n            bptc: null,\r\n            maxAnisotropy: 0,\r\n            uintIndices: false,\r\n            fragmentDepthSupported: false,\r\n            highPrecisionShaderSupported: true,\r\n            colorBufferFloat: false,\r\n            supportFloatTexturesResolve: false,\r\n            rg11b10ufColorRenderable: false,\r\n            textureFloat: false,\r\n            textureFloatLinearFiltering: false,\r\n            textureFloatRender: false,\r\n            textureHalfFloat: false,\r\n            textureHalfFloatLinearFiltering: false,\r\n            textureHalfFloatRender: false,\r\n            textureLOD: false,\r\n            texelFetch: false,\r\n            drawBuffersExtension: false,\r\n            depthTextureExtension: false,\r\n            vertexArrayObject: false,\r\n            instancedArrays: false,\r\n            supportOcclusionQuery: false,\r\n            canUseTimestampForTimerQuery: false,\r\n            maxMSAASamples: 1,\r\n            blendMinMax: false,\r\n            canUseGLInstanceID: false,\r\n            canUseGLVertexID: false,\r\n            supportComputeShaders: false,\r\n            supportSRGBBuffers: false,\r\n            supportTransformFeedbacks: false,\r\n            textureMaxLevel: false,\r\n            texture2DArrayMaxLayerCount: 128,\r\n            disableMorphTargetTexture: false,\r\n        };\r\n\r\n        this._features = {\r\n            forceBitmapOverHTMLImageElement: false,\r\n            supportRenderAndCopyToLodForFloatTextures: false,\r\n            supportDepthStencilTexture: false,\r\n            supportShadowSamplers: false,\r\n            uniformBufferHardCheckMatrix: false,\r\n            allowTexturePrefiltering: false,\r\n            trackUbosInFrame: false,\r\n            checkUbosContentBeforeUpload: false,\r\n            supportCSM: false,\r\n            basisNeedsPOT: false,\r\n            support3DTextures: false,\r\n            needTypeSuffixInShaderConstants: false,\r\n            supportMSAA: false,\r\n            supportSSAO2: false,\r\n            supportExtendedTextureFormats: false,\r\n            supportSwitchCaseInShader: false,\r\n            supportSyncTextureRead: false,\r\n            needsInvertingBitmap: false,\r\n            useUBOBindingCache: false,\r\n            needShaderCodeInlining: false,\r\n            needToAlwaysBindUniformBuffers: false,\r\n            supportRenderPasses: true,\r\n            supportSpriteInstancing: false,\r\n            forceVertexBufferStrideAndOffsetMultiple4Bytes: false,\r\n            _collectUbosUpdatedInFrame: false,\r\n        };\r\n\r\n        Logger.Log(`Babylon.js v${Engine.Version} - Null engine`);\r\n\r\n        // Wrappers\r\n        const theCurrentGlobal = typeof self !== \"undefined\" ? self : typeof global !== \"undefined\" ? global : window;\r\n        if (typeof URL === \"undefined\") {\r\n            theCurrentGlobal.URL = {\r\n                createObjectURL: function () {},\r\n                revokeObjectURL: function () {},\r\n            };\r\n        }\r\n        if (typeof Blob === \"undefined\") {\r\n            theCurrentGlobal.Blob = function () {};\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Creates a vertex buffer\r\n     * @param vertices the data for the vertex buffer\r\n     * @returns the new WebGL static buffer\r\n     */\r\n    public createVertexBuffer(vertices: FloatArray): DataBuffer {\r\n        const buffer = new DataBuffer();\r\n        buffer.references = 1;\r\n        return buffer;\r\n    }\r\n\r\n    /**\r\n     * Creates a new index buffer\r\n     * @param indices defines the content of the index buffer\r\n     * @returns a new webGL buffer\r\n     */\r\n    public createIndexBuffer(indices: IndicesArray): DataBuffer {\r\n        const buffer = new DataBuffer();\r\n        buffer.references = 1;\r\n        return buffer;\r\n    }\r\n\r\n    /**\r\n     * Clear the current render buffer or the current render target (if any is set up)\r\n     * @param color defines the color to use\r\n     * @param backBuffer defines if the back buffer must be cleared\r\n     * @param depth defines if the depth buffer must be cleared\r\n     * @param stencil defines if the stencil buffer must be cleared\r\n     */\r\n    public clear(color: IColor4Like, backBuffer: boolean, depth: boolean, stencil: boolean = false): void {}\r\n\r\n    /**\r\n     * Gets the current render width\r\n     * @param useScreen defines if screen size must be used (or the current render target if any)\r\n     * @returns a number defining the current render width\r\n     */\r\n    public getRenderWidth(useScreen = false): number {\r\n        if (!useScreen && this._currentRenderTarget) {\r\n            return this._currentRenderTarget.width;\r\n        }\r\n\r\n        return this._options.renderWidth;\r\n    }\r\n\r\n    /**\r\n     * Gets the current render height\r\n     * @param useScreen defines if screen size must be used (or the current render target if any)\r\n     * @returns a number defining the current render height\r\n     */\r\n    public getRenderHeight(useScreen = false): number {\r\n        if (!useScreen && this._currentRenderTarget) {\r\n            return this._currentRenderTarget.height;\r\n        }\r\n\r\n        return this._options.renderHeight;\r\n    }\r\n\r\n    /**\r\n     * Set the WebGL's viewport\r\n     * @param viewport defines the viewport element to be used\r\n     * @param requiredWidth defines the width required for rendering. If not provided the rendering canvas' width is used\r\n     * @param requiredHeight defines the height required for rendering. If not provided the rendering canvas' height is used\r\n     */\r\n    public setViewport(viewport: IViewportLike, requiredWidth?: number, requiredHeight?: number): void {\r\n        this._cachedViewport = viewport;\r\n    }\r\n\r\n    public createShaderProgram(pipelineContext: IPipelineContext, vertexCode: string, fragmentCode: string, defines: string, context?: WebGLRenderingContext): WebGLProgram {\r\n        return {\r\n            // eslint-disable-next-line @typescript-eslint/naming-convention\r\n            __SPECTOR_rebuildProgram: null,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the list of webGL uniform locations associated with a specific program based on a list of uniform names\r\n     * @param pipelineContext defines the pipeline context to use\r\n     * @param uniformsNames defines the list of uniform names\r\n     * @returns an array of webGL uniform locations\r\n     */\r\n    public getUniforms(pipelineContext: IPipelineContext, uniformsNames: string[]): Nullable<WebGLUniformLocation>[] {\r\n        return [];\r\n    }\r\n\r\n    /**\r\n     * Gets the lsit of active attributes for a given webGL program\r\n     * @param pipelineContext defines the pipeline context to use\r\n     * @param attributesNames defines the list of attribute names to get\r\n     * @returns an array of indices indicating the offset of each attribute\r\n     */\r\n    public getAttributes(pipelineContext: IPipelineContext, attributesNames: string[]): number[] {\r\n        return [];\r\n    }\r\n\r\n    /**\r\n     * Binds an effect to the webGL context\r\n     * @param effect defines the effect to bind\r\n     */\r\n    public bindSamplers(effect: Effect): void {\r\n        this._currentEffect = null;\r\n    }\r\n\r\n    /**\r\n     * Activates an effect, making it the current one (ie. the one used for rendering)\r\n     * @param effect defines the effect to activate\r\n     */\r\n    public enableEffect(effect: Nullable<Effect | DrawWrapper>): void {\r\n        effect = effect !== null && DrawWrapper.IsWrapper(effect) ? effect.effect : effect; // get only the effect, we don't need a Wrapper in the WebGL engine\r\n\r\n        this._currentEffect = effect as Nullable<Effect>;\r\n        if (!effect) {\r\n            return;\r\n        }\r\n\r\n        if (effect.onBind) {\r\n            effect.onBind(effect);\r\n        }\r\n        if (effect._onBindObservable) {\r\n            effect._onBindObservable.notifyObservers(effect);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set various states to the webGL context\r\n     * @param culling defines culling state: true to enable culling, false to disable it\r\n     * @param zOffset defines the value to apply to zOffset (0 by default)\r\n     * @param force defines if states must be applied even if cache is up to date\r\n     * @param reverseSide defines if culling must be reversed (CCW if false, CW if true)\r\n     * @param cullBackFaces true to cull back faces, false to cull front faces (if culling is enabled)\r\n     * @param stencil stencil states to set\r\n     * @param zOffsetUnits defines the value to apply to zOffsetUnits (0 by default)\r\n     */\r\n    public setState(\r\n        culling: boolean,\r\n        zOffset: number = 0,\r\n        force?: boolean,\r\n        reverseSide = false,\r\n        cullBackFaces?: boolean,\r\n        stencil?: IStencilState,\r\n        zOffsetUnits: number = 0\r\n    ): void {}\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of int32\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of int32 to store\r\n     * @returns true if value was set\r\n     */\r\n    public setIntArray(uniform: WebGLUniformLocation, array: Int32Array): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of int32 (stored as vec2)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of int32 to store\r\n     * @returns true if value was set\r\n     */\r\n    public setIntArray2(uniform: WebGLUniformLocation, array: Int32Array): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of int32 (stored as vec3)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of int32 to store\r\n     * @returns true if value was set\r\n     */\r\n    public setIntArray3(uniform: WebGLUniformLocation, array: Int32Array): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of int32 (stored as vec4)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of int32 to store\r\n     * @returns true if value was set\r\n     */\r\n    public setIntArray4(uniform: WebGLUniformLocation, array: Int32Array): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of float32\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of float32 to store\r\n     * @returns true if value was set\r\n     */\r\n    public setFloatArray(uniform: WebGLUniformLocation, array: Float32Array): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of float32 (stored as vec2)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of float32 to store\r\n     * @returns true if value was set\r\n     */\r\n    public setFloatArray2(uniform: WebGLUniformLocation, array: Float32Array): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of float32 (stored as vec3)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of float32 to store\r\n     * @returns true if value was set\r\n     */\r\n    public setFloatArray3(uniform: WebGLUniformLocation, array: Float32Array): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of float32 (stored as vec4)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of float32 to store\r\n     * @returns true if value was set\r\n     */\r\n    public setFloatArray4(uniform: WebGLUniformLocation, array: Float32Array): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of number\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of number to store\r\n     * @returns true if value was set\r\n     */\r\n    public setArray(uniform: WebGLUniformLocation, array: number[]): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of number (stored as vec2)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of number to store\r\n     * @returns true if value was set\r\n     */\r\n    public setArray2(uniform: WebGLUniformLocation, array: number[]): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of number (stored as vec3)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of number to store\r\n     * @returns true if value was set\r\n     */\r\n    public setArray3(uniform: WebGLUniformLocation, array: number[]): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of number (stored as vec4)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of number to store\r\n     * @returns true if value was set\r\n     */\r\n    public setArray4(uniform: WebGLUniformLocation, array: number[]): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of float32 (stored as matrices)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param matrices defines the array of float32 to store\r\n     * @returns true if value was set\r\n     */\r\n    public setMatrices(uniform: WebGLUniformLocation, matrices: Float32Array): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a matrix (3x3)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param matrix defines the Float32Array representing the 3x3 matrix to store\r\n     * @returns true if value was set\r\n     */\r\n    public setMatrix3x3(uniform: WebGLUniformLocation, matrix: Float32Array): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a matrix (2x2)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param matrix defines the Float32Array representing the 2x2 matrix to store\r\n     * @returns true if value was set\r\n     */\r\n    public setMatrix2x2(uniform: WebGLUniformLocation, matrix: Float32Array): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a number (float)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param value defines the float number to store\r\n     * @returns true if value was set\r\n     */\r\n    public setFloat(uniform: WebGLUniformLocation, value: number): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a vec2\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @returns true if value was set\r\n     */\r\n    public setFloat2(uniform: WebGLUniformLocation, x: number, y: number): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a vec3\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @returns true if value was set\r\n     */\r\n    public setFloat3(uniform: WebGLUniformLocation, x: number, y: number, z: number): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a boolean\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param bool defines the boolean to store\r\n     * @returns true if value was set\r\n     */\r\n    public setBool(uniform: WebGLUniformLocation, bool: number): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a vec4\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @param w defines the 4th component of the value\r\n     * @returns true if value was set\r\n     */\r\n    public setFloat4(uniform: WebGLUniformLocation, x: number, y: number, z: number, w: number): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Sets the current alpha mode\r\n     * @param mode defines the mode to use (one of the Engine.ALPHA_XXX)\r\n     * @param noDepthWriteChange defines if depth writing state should remains unchanged (false by default)\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/advanced/transparent_rendering\r\n     */\r\n    public setAlphaMode(mode: number, noDepthWriteChange: boolean = false): void {\r\n        if (this._alphaMode === mode) {\r\n            return;\r\n        }\r\n\r\n        this.alphaState.alphaBlend = mode !== Constants.ALPHA_DISABLE;\r\n\r\n        if (!noDepthWriteChange) {\r\n            this.setDepthWrite(mode === Constants.ALPHA_DISABLE);\r\n        }\r\n        this._alphaMode = mode;\r\n    }\r\n\r\n    /**\r\n     * Bind webGl buffers directly to the webGL context\r\n     * @param vertexBuffers defines the vertex buffer to bind\r\n     * @param indexBuffer defines the index buffer to bind\r\n     * @param effect defines the effect associated with the vertex buffer\r\n     */\r\n    public bindBuffers(vertexBuffers: { [key: string]: VertexBuffer }, indexBuffer: DataBuffer, effect: Effect): void {}\r\n\r\n    /**\r\n     * Force the entire cache to be cleared\r\n     * You should not have to use this function unless your engine needs to share the webGL context with another engine\r\n     * @param bruteForce defines a boolean to force clearing ALL caches (including stencil, detoh and alpha states)\r\n     */\r\n    public wipeCaches(bruteForce?: boolean): void {\r\n        if (this.preventCacheWipeBetweenFrames) {\r\n            return;\r\n        }\r\n        this.resetTextureCache();\r\n        this._currentEffect = null;\r\n\r\n        if (bruteForce) {\r\n            this._currentProgram = null;\r\n\r\n            this._stencilStateComposer.reset();\r\n            this.depthCullingState.reset();\r\n            this.alphaState.reset();\r\n        }\r\n\r\n        this._cachedVertexBuffers = null;\r\n        this._cachedIndexBuffer = null;\r\n        this._cachedEffectForVertexBuffers = null;\r\n    }\r\n\r\n    /**\r\n     * Send a draw order\r\n     * @param useTriangles defines if triangles must be used to draw (else wireframe will be used)\r\n     * @param indexStart defines the starting index\r\n     * @param indexCount defines the number of index to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public draw(useTriangles: boolean, indexStart: number, indexCount: number, instancesCount?: number): void {}\r\n\r\n    /**\r\n     * Draw a list of indexed primitives\r\n     * @param fillMode defines the primitive to use\r\n     * @param indexStart defines the starting index\r\n     * @param indexCount defines the number of index to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawElementsType(fillMode: number, indexStart: number, indexCount: number, instancesCount?: number): void {}\r\n\r\n    /**\r\n     * Draw a list of unindexed primitives\r\n     * @param fillMode defines the primitive to use\r\n     * @param verticesStart defines the index of first vertex to draw\r\n     * @param verticesCount defines the count of vertices to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawArraysType(fillMode: number, verticesStart: number, verticesCount: number, instancesCount?: number): void {}\r\n\r\n    /** @internal */\r\n    protected _createTexture(): WebGLTexture {\r\n        return {};\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseTexture(texture: InternalTexture): void {}\r\n\r\n    /**\r\n     * Usually called from Texture.ts.\r\n     * Passed information to create a WebGLTexture\r\n     * @param urlArg defines a value which contains one of the following:\r\n     * * A conventional http URL, e.g. 'http://...' or 'file://...'\r\n     * * A base64 string of in-line texture data, e.g. 'data:image/jpg;base64,/...'\r\n     * * An indicator that data being passed using the buffer parameter, e.g. 'data:mytexture.jpg'\r\n     * @param noMipmap defines a boolean indicating that no mipmaps shall be generated.  Ignored for compressed textures.  They must be in the file\r\n     * @param invertY when true, image is flipped when loaded.  You probably want true. Certain compressed textures may invert this if their default is inverted (eg. ktx)\r\n     * @param scene needed for loading to the correct scene\r\n     * @param samplingMode mode with should be used sample / access the texture (Default: Texture.TRILINEAR_SAMPLINGMODE)\r\n     * @param onLoad optional callback to be called upon successful completion\r\n     * @param onError optional callback to be called upon failure\r\n     * @param buffer a source of a file previously fetched as either a base64 string, an ArrayBuffer (compressed or image format), HTMLImageElement (image format), or a Blob\r\n     * @param fallback an internal argument in case the function must be called again, due to etc1 not having alpha capabilities\r\n     * @param format internal format.  Default: RGB when extension is '.jpg' else RGBA.  Ignored for compressed textures\r\n     * @param forcedExtension defines the extension to use to pick the right loader\r\n     * @param mimeType defines an optional mime type\r\n     * @returns a InternalTexture for assignment back into BABYLON.Texture\r\n     */\r\n    public createTexture(\r\n        urlArg: Nullable<string>,\r\n        noMipmap: boolean,\r\n        invertY: boolean,\r\n        scene: Nullable<ISceneLike>,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        onLoad: Nullable<(texture: InternalTexture) => void> = null,\r\n        onError: Nullable<(message: string, exception: any) => void> = null,\r\n        buffer: Nullable<string | ArrayBuffer | ArrayBufferView | HTMLImageElement | Blob | ImageBitmap> = null,\r\n        fallback: Nullable<InternalTexture> = null,\r\n        format: Nullable<number> = null,\r\n        forcedExtension: Nullable<string> = null,\r\n        mimeType?: string\r\n    ): InternalTexture {\r\n        const texture = new InternalTexture(this, InternalTextureSource.Url);\r\n        const url = String(urlArg);\r\n\r\n        texture.url = url;\r\n        texture.generateMipMaps = !noMipmap;\r\n        texture.samplingMode = samplingMode;\r\n        texture.invertY = invertY;\r\n        texture.baseWidth = this._options.textureSize;\r\n        texture.baseHeight = this._options.textureSize;\r\n        texture.width = this._options.textureSize;\r\n        texture.height = this._options.textureSize;\r\n        if (format) {\r\n            texture.format = format;\r\n        }\r\n\r\n        texture.isReady = true;\r\n\r\n        if (onLoad) {\r\n            setTimeout(() => {\r\n                onLoad(texture);\r\n            });\r\n        }\r\n\r\n        this._internalTexturesCache.push(texture);\r\n\r\n        return texture;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _createHardwareRenderTargetWrapper(isMulti: boolean, isCube: boolean, size: number | { width: number; height: number; layers?: number }): RenderTargetWrapper {\r\n        const rtWrapper = new RenderTargetWrapper(isMulti, isCube, size, this);\r\n        this._renderTargetWrapperCache.push(rtWrapper);\r\n        return rtWrapper;\r\n    }\r\n\r\n    /**\r\n     * Creates a new render target wrapper\r\n     * @param size defines the size of the texture\r\n     * @param options defines the options used to create the texture\r\n     * @returns a new render target wrapper\r\n     */\r\n    public createRenderTargetTexture(size: any, options: boolean | RenderTargetCreationOptions): RenderTargetWrapper {\r\n        const rtWrapper = this._createHardwareRenderTargetWrapper(false, false, size);\r\n\r\n        const fullOptions: RenderTargetCreationOptions = {};\r\n\r\n        if (options !== undefined && typeof options === \"object\") {\r\n            fullOptions.generateMipMaps = options.generateMipMaps;\r\n            fullOptions.generateDepthBuffer = options.generateDepthBuffer === undefined ? true : options.generateDepthBuffer;\r\n            fullOptions.generateStencilBuffer = fullOptions.generateDepthBuffer && options.generateStencilBuffer;\r\n            fullOptions.type = options.type === undefined ? Constants.TEXTURETYPE_UNSIGNED_INT : options.type;\r\n            fullOptions.samplingMode = options.samplingMode === undefined ? Constants.TEXTURE_TRILINEAR_SAMPLINGMODE : options.samplingMode;\r\n        } else {\r\n            fullOptions.generateMipMaps = <boolean>options;\r\n            fullOptions.generateDepthBuffer = true;\r\n            fullOptions.generateStencilBuffer = false;\r\n            fullOptions.type = Constants.TEXTURETYPE_UNSIGNED_INT;\r\n            fullOptions.samplingMode = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE;\r\n        }\r\n        const texture = new InternalTexture(this, InternalTextureSource.RenderTarget);\r\n\r\n        const width = size.width || size;\r\n        const height = size.height || size;\r\n\r\n        rtWrapper._generateDepthBuffer = fullOptions.generateDepthBuffer;\r\n        rtWrapper._generateStencilBuffer = fullOptions.generateStencilBuffer ? true : false;\r\n\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.width = width;\r\n        texture.height = height;\r\n        texture.isReady = true;\r\n        texture.samples = 1;\r\n        texture.generateMipMaps = fullOptions.generateMipMaps ? true : false;\r\n        texture.samplingMode = fullOptions.samplingMode;\r\n        texture.type = fullOptions.type;\r\n\r\n        this._internalTexturesCache.push(texture);\r\n\r\n        return rtWrapper;\r\n    }\r\n\r\n    /**\r\n     * Creates a new render target wrapper\r\n     * @param size defines the size of the texture\r\n     * @param options defines the options used to create the texture\r\n     * @returns a new render target wrapper\r\n     */\r\n    public createRenderTargetCubeTexture(size: number, options?: RenderTargetCreationOptions): RenderTargetWrapper {\r\n        const rtWrapper = this._createHardwareRenderTargetWrapper(false, true, size);\r\n\r\n        const fullOptions = {\r\n            generateMipMaps: true,\r\n            generateDepthBuffer: true,\r\n            generateStencilBuffer: false,\r\n            type: Constants.TEXTURETYPE_UNSIGNED_INT,\r\n            samplingMode: Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n            format: Constants.TEXTUREFORMAT_RGBA,\r\n            ...options,\r\n        };\r\n        fullOptions.generateStencilBuffer = fullOptions.generateDepthBuffer && fullOptions.generateStencilBuffer;\r\n\r\n        if (fullOptions.type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n            // if floating point linear (gl.FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            fullOptions.samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        } else if (fullOptions.type === Constants.TEXTURETYPE_HALF_FLOAT && !this._caps.textureHalfFloatLinearFiltering) {\r\n            // if floating point linear (HALF_FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            fullOptions.samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        }\r\n\r\n        rtWrapper._generateDepthBuffer = fullOptions.generateDepthBuffer;\r\n        rtWrapper._generateStencilBuffer = fullOptions.generateStencilBuffer ? true : false;\r\n\r\n        const texture = new InternalTexture(this, InternalTextureSource.RenderTarget);\r\n        texture.baseWidth = size;\r\n        texture.baseHeight = size;\r\n        texture.width = size;\r\n        texture.height = size;\r\n        texture.isReady = true;\r\n        texture.isCube = true;\r\n        texture.samples = 1;\r\n        texture.generateMipMaps = fullOptions.generateMipMaps ? true : false;\r\n        texture.samplingMode = fullOptions.samplingMode;\r\n        texture.type = fullOptions.type;\r\n\r\n        this._internalTexturesCache.push(texture);\r\n\r\n        return rtWrapper;\r\n    }\r\n\r\n    /**\r\n     * Update the sampling mode of a given texture\r\n     * @param samplingMode defines the required sampling mode\r\n     * @param texture defines the texture to update\r\n     */\r\n    public updateTextureSamplingMode(samplingMode: number, texture: InternalTexture): void {\r\n        texture.samplingMode = samplingMode;\r\n    }\r\n\r\n    /**\r\n     * Creates a raw texture\r\n     * @param data defines the data to store in the texture\r\n     * @param width defines the width of the texture\r\n     * @param height defines the height of the texture\r\n     * @param format defines the format of the data\r\n     * @param generateMipMaps defines if the engine should generate the mip levels\r\n     * @param invertY defines if data must be stored with Y axis inverted\r\n     * @param samplingMode defines the required sampling mode (Texture.NEAREST_SAMPLINGMODE by default)\r\n     * @param compression defines the compression used (null by default)\r\n     * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_INT by default)\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n     * @returns the raw texture inside an InternalTexture\r\n     */\r\n    public createRawTexture(\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        format: number,\r\n        generateMipMaps: boolean,\r\n        invertY: boolean,\r\n        samplingMode: number,\r\n        compression: Nullable<string> = null,\r\n        type: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        creationFlags = 0,\r\n        useSRGBBuffer = false\r\n    ): InternalTexture {\r\n        const texture = new InternalTexture(this, InternalTextureSource.Raw);\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.width = width;\r\n        texture.height = height;\r\n        texture.format = format;\r\n        texture.generateMipMaps = generateMipMaps;\r\n        texture.samplingMode = samplingMode;\r\n        texture.invertY = invertY;\r\n        texture._compression = compression;\r\n        texture.type = type;\r\n        texture._useSRGBBuffer = useSRGBBuffer;\r\n\r\n        if (!this._doNotHandleContextLost) {\r\n            texture._bufferView = data;\r\n        }\r\n\r\n        return texture;\r\n    }\r\n\r\n    /**\r\n     * Update a raw texture\r\n     * @param texture defines the texture to update\r\n     * @param data defines the data to store in the texture\r\n     * @param format defines the format of the data\r\n     * @param invertY defines if data must be stored with Y axis inverted\r\n     * @param compression defines the compression used (null by default)\r\n     * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_INT by default)\r\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n     */\r\n    public updateRawTexture(\r\n        texture: Nullable<InternalTexture>,\r\n        data: Nullable<ArrayBufferView>,\r\n        format: number,\r\n        invertY: boolean,\r\n        compression: Nullable<string> = null,\r\n        type: number = Constants.TEXTURETYPE_UNSIGNED_INT,\r\n        useSRGBBuffer: boolean = false\r\n    ): void {\r\n        if (texture) {\r\n            texture._bufferView = data;\r\n            texture.format = format;\r\n            texture.invertY = invertY;\r\n            texture._compression = compression;\r\n            texture.type = type;\r\n            texture._useSRGBBuffer = useSRGBBuffer;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Binds the frame buffer to the specified texture.\r\n     * @param rtWrapper The render target wrapper to render to\r\n     * @param faceIndex The face of the texture to render to in case of cube texture\r\n     * @param requiredWidth The width of the target to render to\r\n     * @param requiredHeight The height of the target to render to\r\n     * @param forceFullscreenViewport Forces the viewport to be the entire texture/screen if true\r\n     */\r\n    public bindFramebuffer(rtWrapper: RenderTargetWrapper, faceIndex?: number, requiredWidth?: number, requiredHeight?: number, forceFullscreenViewport?: boolean): void {\r\n        if (this._currentRenderTarget) {\r\n            this.unBindFramebuffer(this._currentRenderTarget);\r\n        }\r\n        this._currentRenderTarget = rtWrapper;\r\n        this._currentFramebuffer = null;\r\n        if (this._cachedViewport && !forceFullscreenViewport) {\r\n            this.setViewport(this._cachedViewport, requiredWidth, requiredHeight);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Unbind the current render target texture from the webGL context\r\n     * @param rtWrapper defines the render target wrapper to unbind\r\n     * @param disableGenerateMipMaps defines a boolean indicating that mipmaps must not be generated\r\n     * @param onBeforeUnbind defines a function which will be called before the effective unbind\r\n     */\r\n    public unBindFramebuffer(rtWrapper: RenderTargetWrapper, disableGenerateMipMaps = false, onBeforeUnbind?: () => void): void {\r\n        this._currentRenderTarget = null;\r\n\r\n        if (onBeforeUnbind) {\r\n            onBeforeUnbind();\r\n        }\r\n        this._currentFramebuffer = null;\r\n    }\r\n\r\n    /**\r\n     * Creates a dynamic vertex buffer\r\n     * @param vertices the data for the dynamic vertex buffer\r\n     * @returns the new WebGL dynamic buffer\r\n     */\r\n    public createDynamicVertexBuffer(vertices: FloatArray): DataBuffer {\r\n        const buffer = new DataBuffer();\r\n        buffer.references = 1;\r\n        buffer.capacity = 1;\r\n        return buffer;\r\n    }\r\n\r\n    /**\r\n     * Update the content of a dynamic texture\r\n     * @param texture defines the texture to update\r\n     * @param canvas defines the canvas containing the source\r\n     * @param invertY defines if data must be stored with Y axis inverted\r\n     * @param premulAlpha defines if alpha is stored as premultiplied\r\n     * @param format defines the format of the data\r\n     */\r\n    public updateDynamicTexture(texture: Nullable<InternalTexture>, canvas: HTMLCanvasElement, invertY: boolean, premulAlpha: boolean = false, format?: number): void {}\r\n\r\n    /**\r\n     * Gets a boolean indicating if all created effects are ready\r\n     * @returns true if all effects are ready\r\n     */\r\n    public areAllEffectsReady(): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Get the current error code of the webGL context\r\n     * @returns the error code\r\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebGLRenderingContext/getError\r\n     */\r\n    public getError(): number {\r\n        return 0;\r\n    }\r\n\r\n    /** @internal */\r\n    public _getUnpackAlignement(): number {\r\n        return 1;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _unpackFlipY(value: boolean) {}\r\n\r\n    /**\r\n     * Update a dynamic index buffer\r\n     * @param indexBuffer defines the target index buffer\r\n     * @param indices defines the data to update\r\n     * @param offset defines the offset in the target index buffer where update should start\r\n     */\r\n    public updateDynamicIndexBuffer(indexBuffer: WebGLBuffer, indices: IndicesArray, offset: number = 0): void {}\r\n\r\n    /**\r\n     * Updates a dynamic vertex buffer.\r\n     * @param vertexBuffer the vertex buffer to update\r\n     * @param vertices the data used to update the vertex buffer\r\n     * @param byteOffset the byte offset of the data (optional)\r\n     * @param byteLength the byte length of the data (optional)\r\n     */\r\n    public updateDynamicVertexBuffer(vertexBuffer: WebGLBuffer, vertices: FloatArray, byteOffset?: number, byteLength?: number): void {}\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindTextureDirectly(target: number, texture: InternalTexture): boolean {\r\n        if (this._boundTexturesCache[this._activeChannel] !== texture) {\r\n            this._boundTexturesCache[this._activeChannel] = texture;\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindTexture(channel: number, texture: InternalTexture): void {\r\n        if (channel < 0) {\r\n            return;\r\n        }\r\n\r\n        this._bindTextureDirectly(0, texture);\r\n    }\r\n\r\n    protected _deleteBuffer(buffer: WebGLBuffer): void {}\r\n\r\n    /**\r\n     * Force the engine to release all cached effects. This means that next effect compilation will have to be done completely even if a similar effect was already compiled\r\n     */\r\n    public releaseEffects() {}\r\n\r\n    public displayLoadingUI(): void {}\r\n\r\n    public hideLoadingUI(): void {}\r\n\r\n    public set loadingUIText(_: string) {}\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadCompressedDataToTextureDirectly(\r\n        texture: InternalTexture,\r\n        internalFormat: number,\r\n        width: number,\r\n        height: number,\r\n        data: ArrayBufferView,\r\n        faceIndex: number = 0,\r\n        lod: number = 0\r\n    ) {}\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadDataToTextureDirectly(texture: InternalTexture, imageData: ArrayBufferView, faceIndex: number = 0, lod: number = 0): void {}\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadArrayBufferViewToTexture(texture: InternalTexture, imageData: ArrayBufferView, faceIndex: number = 0, lod: number = 0): void {}\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadImageToTexture(texture: InternalTexture, image: HTMLImageElement, faceIndex: number = 0, lod: number = 0) {}\r\n}\r\n"]}