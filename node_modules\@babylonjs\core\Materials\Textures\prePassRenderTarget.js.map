{"version": 3, "file": "prePassRenderTarget.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Materials/Textures/prePassRenderTarget.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAKxD,OAAO,EAAE,0BAA0B,EAAE,MAAM,gDAAgD,CAAC;AAG5F;;;;;;;;GAQG;AACH,MAAM,OAAO,mBAAoB,SAAQ,iBAAiB;IAyCtD,YAAmB,IAAY,EAAE,mBAAkD,EAAE,IAAS,EAAE,KAAa,EAAE,KAAa,EAAE,OAA+C;QACzK,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAzC7C;;WAEG;QACI,oCAA+B,GAAkB,EAAE,CAAC;QAqB3D;;WAEG;QACI,0BAAqB,GAAG,KAAK,CAAC;QAErC;;WAEG;QACI,YAAO,GAAY,KAAK,CAAC;QAEhC;;;WAGG;QACI,wBAAmB,GAAkC,IAAI,CAAC;QAK7D,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;IACnD,CAAC;IAED;;;OAGG;IACI,wBAAwB;QAC3B,IAAI,CAAC,0BAA0B,GAAG,IAAI,0BAA0B,CAAC,oBAAoB,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACzH,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,EAAE,CAAC;IACxD,CAAC;IAED;;;OAGG;IACI,UAAU;QACb,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACxD,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE1D,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACpC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAEtC,IAAI,KAAK,KAAK,aAAa,IAAI,MAAM,KAAK,cAAc,EAAE;YACtD,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,aAAa,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC,CAAC;YAE9D,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;SACrC;IACL,CAAC;IAED;;;;;;OAMG;IACI,WAAW,CAAC,KAAa,EAAE,OAAmC,EAAE,YAAuB;QAC1F,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAChD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,sBAAsB;QACzB,IAAI,CAAC,+BAA+B,CAAC,MAAM,GAAG,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,OAAO;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,KAAK,IAAI,KAAK,CAAC,eAAe,EAAE;YAChC,MAAM,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEhE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBACd,KAAK,CAAC,eAAe,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACxD;SACJ;QAED,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,CAAC;SAC7C;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC;SACxD;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;YACzC,IAAI,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,CAAC;SACxD;IACL,CAAC;CACJ", "sourcesContent": ["import type { IMultiRenderTargetOptions } from \"./multiRenderTarget\";\r\nimport { MultiRenderTarget } from \"./multiRenderTarget\";\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport type { RenderTargetTexture } from \"./renderTargetTexture\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { PostProcess } from \"../../PostProcesses/postProcess\";\r\nimport { ImageProcessingPostProcess } from \"../../PostProcesses/imageProcessingPostProcess\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * A multi render target designed to render the prepass.\r\n * Prepass is a scene component used to render information in multiple textures\r\n * alongside with the scene materials rendering.\r\n * Note : This is an internal class, and you should NOT need to instanciate this.\r\n * Only the `PrePassRenderer` should instanciate this class.\r\n * It is more likely that you need a regular `MultiRenderTarget`\r\n * @internal\r\n */\r\nexport class PrePassRenderTarget extends MultiRenderTarget {\r\n    /**\r\n     * @internal\r\n     */\r\n    public _beforeCompositionPostProcesses: PostProcess[] = [];\r\n    /**\r\n     * Image processing post process for composition\r\n     */\r\n    public imageProcessingPostProcess: ImageProcessingPostProcess;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _engine: Engine;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _scene: Scene;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _outputPostProcess: Nullable<PostProcess>;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _internalTextureDirty = false;\r\n\r\n    /**\r\n     * Is this render target enabled for prepass rendering\r\n     */\r\n    public enabled: boolean = false;\r\n\r\n    /**\r\n     * Render target associated with this prePassRenderTarget\r\n     * If this is `null`, it means this prePassRenderTarget is associated with the scene\r\n     */\r\n    public renderTargetTexture: Nullable<RenderTargetTexture> = null;\r\n\r\n    public constructor(name: string, renderTargetTexture: Nullable<RenderTargetTexture>, size: any, count: number, scene?: Scene, options?: IMultiRenderTargetOptions | undefined) {\r\n        super(name, size, count, scene, options);\r\n\r\n        this.renderTargetTexture = renderTargetTexture;\r\n    }\r\n\r\n    /**\r\n     * Creates a composition effect for this RT\r\n     * @internal\r\n     */\r\n    public _createCompositionEffect() {\r\n        this.imageProcessingPostProcess = new ImageProcessingPostProcess(\"prePassComposition\", 1, null, undefined, this._engine);\r\n        this.imageProcessingPostProcess._updateParameters();\r\n    }\r\n\r\n    /**\r\n     * Checks that the size of this RT is still adapted to the desired render size.\r\n     * @internal\r\n     */\r\n    public _checkSize() {\r\n        const requiredWidth = this._engine.getRenderWidth(true);\r\n        const requiredHeight = this._engine.getRenderHeight(true);\r\n\r\n        const width = this.getRenderWidth();\r\n        const height = this.getRenderHeight();\r\n\r\n        if (width !== requiredWidth || height !== requiredHeight) {\r\n            this.resize({ width: requiredWidth, height: requiredHeight });\r\n\r\n            this._internalTextureDirty = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Changes the number of render targets in this MRT\r\n     * Be careful as it will recreate all the data in the new texture.\r\n     * @param count new texture count\r\n     * @param options Specifies texture types and sampling modes for new textures\r\n     * @param textureNames Specifies the names of the textures (optional)\r\n     */\r\n    public updateCount(count: number, options?: IMultiRenderTargetOptions, textureNames?: string[]) {\r\n        super.updateCount(count, options, textureNames);\r\n        this._internalTextureDirty = true;\r\n    }\r\n\r\n    /**\r\n     * Resets the post processes chains applied to this RT.\r\n     * @internal\r\n     */\r\n    public _resetPostProcessChain() {\r\n        this._beforeCompositionPostProcesses.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Diposes this render target\r\n     */\r\n    public dispose() {\r\n        const scene = this._scene;\r\n\r\n        super.dispose();\r\n\r\n        if (scene && scene.prePassRenderer) {\r\n            const index = scene.prePassRenderer.renderTargets.indexOf(this);\r\n\r\n            if (index !== -1) {\r\n                scene.prePassRenderer.renderTargets.splice(index, 1);\r\n            }\r\n        }\r\n\r\n        if (this.imageProcessingPostProcess) {\r\n            this.imageProcessingPostProcess.dispose();\r\n        }\r\n\r\n        if (this.renderTargetTexture) {\r\n            this.renderTargetTexture._prePassRenderTarget = null;\r\n        }\r\n\r\n        if (this._outputPostProcess) {\r\n            this._outputPostProcess.autoClear = true;\r\n            this._outputPostProcess.restoreDefaultInputTexture();\r\n        }\r\n    }\r\n}\r\n"]}