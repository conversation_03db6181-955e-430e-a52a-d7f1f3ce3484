{"version": 3, "file": "weightedsound.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Audio/weightedsound.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC;;GAEG;AACH,MAAM,OAAO,aAAa;IAetB;;;;;OAKG;IACH,YAAY,IAAa,EAAE,MAAe,EAAE,OAAiB;QApB7D,8FAA8F;QACvF,SAAI,GAAY,KAAK,CAAC;QACrB,oBAAe,GAAW,GAAG,CAAC;QAC9B,oBAAe,GAAW,GAAG,CAAC;QAC9B,YAAO,GAAW,CAAC,CAAC;QAC5B,oCAAoC;QAC7B,cAAS,GAAY,KAAK,CAAC;QAClC,mCAAmC;QAC5B,aAAQ,GAAY,KAAK,CAAC;QAEzB,YAAO,GAAY,EAAE,CAAC;QACtB,aAAQ,GAAa,EAAE,CAAC;QAU5B,IAAI,MAAM,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAClE;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,wBAAwB;QACxB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC1B,SAAS,IAAI,MAAM,CAAC;SACvB;QACD,MAAM,YAAY,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC;SACpC;QACD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;YAC9B,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACpB,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB,CAAC,KAAa;QAC9C,IAAI,KAAK,KAAK,IAAI,CAAC,eAAe,EAAE;YAChC,IAAI,IAAI,CAAC,eAAe,GAAG,KAAK,EAAE;gBAC9B,MAAM,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;gBACjH,OAAO;aACV;YAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC9B,KAAK,CAAC,yBAAyB,GAAG,KAAK,CAAC;aAC3C;SACJ;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,IAAW,yBAAyB,CAAC,KAAa;QAC9C,IAAI,KAAK,KAAK,IAAI,CAAC,eAAe,EAAE;YAChC,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE;gBAC9B,MAAM,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;gBACjH,OAAO;aACV;YAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC9B,KAAK,CAAC,yBAAyB,GAAG,KAAK,CAAC;aAC3C;SACJ;IACL,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,MAAM,CAAC,KAAa;QAC3B,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,EAAE;YACxB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC9B,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAC1B;SACJ;IACL,CAAC;IAEO,QAAQ;QACZ,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC;SACrD;QACD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YAC7B,IAAI,CAAC,IAAI,EAAE,CAAC;SACf;aAAM;YACH,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;SAC1B;IACL,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,KAAK,EAAE,CAAC;SAC5C;IACL,CAAC;IAED;;OAEG;IACI,IAAI;QACP,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE,CAAC;SAC3C;IACL,CAAC;IAED;;;OAGG;IACI,IAAI,CAAC,WAAoB;QAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAClC,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3C,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAI,WAAW,IAAI,KAAK,EAAE;oBACtB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;oBACvB,MAAM;iBACT;aACJ;SACJ;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAc,CAAC,CAAC;QAChD,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE;YACjB,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;SAC1D;aAAM;YACH,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;SACzB;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;CACJ", "sourcesContent": ["import type { Sound } from \"../Audio/sound\";\r\nimport { Logger } from \"../Misc/logger\";\r\n\r\n/**\r\n * Wraps one or more Sound objects and selects one with random weight for playback.\r\n */\r\nexport class WeightedSound {\r\n    /** When true a Sound will be selected and played when the current playing Sound completes. */\r\n    public loop: boolean = false;\r\n    private _coneInnerAngle: number = 360;\r\n    private _coneOuterAngle: number = 360;\r\n    private _volume: number = 1;\r\n    /** A Sound is currently playing. */\r\n    public isPlaying: boolean = false;\r\n    /** A Sound is currently paused. */\r\n    public isPaused: boolean = false;\r\n\r\n    private _sounds: Sound[] = [];\r\n    private _weights: number[] = [];\r\n    private _currentIndex?: number;\r\n\r\n    /**\r\n     * Creates a new WeightedSound from the list of sounds given.\r\n     * @param loop When true a Sound will be selected and played when the current playing Sound completes.\r\n     * @param sounds Array of Sounds that will be selected from.\r\n     * @param weights Array of number values for selection weights; length must equal sounds, values will be normalized to 1\r\n     */\r\n    constructor(loop: boolean, sounds: Sound[], weights: number[]) {\r\n        if (sounds.length !== weights.length) {\r\n            throw new Error(\"Sounds length does not equal weights length\");\r\n        }\r\n\r\n        this.loop = loop;\r\n        this._weights = weights;\r\n        // Normalize the weights\r\n        let weightSum = 0;\r\n        for (const weight of weights) {\r\n            weightSum += weight;\r\n        }\r\n        const invWeightSum = weightSum > 0 ? 1 / weightSum : 0;\r\n        for (let i = 0; i < this._weights.length; i++) {\r\n            this._weights[i] *= invWeightSum;\r\n        }\r\n        this._sounds = sounds;\r\n        for (const sound of this._sounds) {\r\n            sound.onEndedObservable.add(() => {\r\n                this._onended();\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * The size of cone in degrees for a directional sound in which there will be no attenuation.\r\n     */\r\n    public get directionalConeInnerAngle(): number {\r\n        return this._coneInnerAngle;\r\n    }\r\n\r\n    /**\r\n     * The size of cone in degrees for a directional sound in which there will be no attenuation.\r\n     */\r\n    public set directionalConeInnerAngle(value: number) {\r\n        if (value !== this._coneInnerAngle) {\r\n            if (this._coneOuterAngle < value) {\r\n                Logger.Error(\"directionalConeInnerAngle: outer angle of the cone must be superior or equal to the inner angle.\");\r\n                return;\r\n            }\r\n\r\n            this._coneInnerAngle = value;\r\n            for (const sound of this._sounds) {\r\n                sound.directionalConeInnerAngle = value;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Size of cone in degrees for a directional sound outside of which there will be no sound.\r\n     * Listener angles between innerAngle and outerAngle will falloff linearly.\r\n     */\r\n    public get directionalConeOuterAngle(): number {\r\n        return this._coneOuterAngle;\r\n    }\r\n\r\n    /**\r\n     * Size of cone in degrees for a directional sound outside of which there will be no sound.\r\n     * Listener angles between innerAngle and outerAngle will falloff linearly.\r\n     */\r\n    public set directionalConeOuterAngle(value: number) {\r\n        if (value !== this._coneOuterAngle) {\r\n            if (value < this._coneInnerAngle) {\r\n                Logger.Error(\"directionalConeOuterAngle: outer angle of the cone must be superior or equal to the inner angle.\");\r\n                return;\r\n            }\r\n\r\n            this._coneOuterAngle = value;\r\n            for (const sound of this._sounds) {\r\n                sound.directionalConeOuterAngle = value;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Playback volume.\r\n     */\r\n    public get volume(): number {\r\n        return this._volume;\r\n    }\r\n\r\n    /**\r\n     * Playback volume.\r\n     */\r\n    public set volume(value: number) {\r\n        if (value !== this._volume) {\r\n            for (const sound of this._sounds) {\r\n                sound.setVolume(value);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _onended() {\r\n        if (this._currentIndex !== undefined) {\r\n            this._sounds[this._currentIndex].autoplay = false;\r\n        }\r\n        if (this.loop && this.isPlaying) {\r\n            this.play();\r\n        } else {\r\n            this.isPlaying = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Suspend playback\r\n     */\r\n    public pause() {\r\n        this.isPaused = true;\r\n        if (this._currentIndex !== undefined) {\r\n            this._sounds[this._currentIndex].pause();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Stop playback\r\n     */\r\n    public stop() {\r\n        this.isPlaying = false;\r\n        if (this._currentIndex !== undefined) {\r\n            this._sounds[this._currentIndex].stop();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Start playback.\r\n     * @param startOffset Position the clip head at a specific time in seconds.\r\n     */\r\n    public play(startOffset?: number) {\r\n        if (!this.isPaused) {\r\n            this.stop();\r\n            const randomValue = Math.random();\r\n            let total = 0;\r\n            for (let i = 0; i < this._weights.length; i++) {\r\n                total += this._weights[i];\r\n                if (randomValue <= total) {\r\n                    this._currentIndex = i;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n        const sound = this._sounds[this._currentIndex!];\r\n        if (sound.isReady()) {\r\n            sound.play(0, this.isPaused ? undefined : startOffset);\r\n        } else {\r\n            sound.autoplay = true;\r\n        }\r\n        this.isPlaying = true;\r\n        this.isPaused = false;\r\n    }\r\n}\r\n"]}