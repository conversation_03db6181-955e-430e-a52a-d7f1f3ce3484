{"version": 3, "file": "torusBuilder.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Meshes/Builders/torusBuilder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AACnE,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAEzE;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,UAAU,qBAAqB,CAAC,OAA0I;IAC5K,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,MAAM,SAAS,GAAG,EAAE,CAAC;IACrB,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,MAAM,GAAG,GAAG,EAAE,CAAC;IAEf,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;IACvC,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC;IAC3C,MAAM,YAAY,GAAG,CAAC,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC;IACtD,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,IAAI,UAAU,CAAC,WAAW,CAAC;IAE9G,MAAM,MAAM,GAAG,YAAY,GAAG,CAAC,CAAC;IAEhC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,YAAY,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;QAE3B,MAAM,UAAU,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QAEtE,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;QAElG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,YAAY,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;YAE/B,MAAM,UAAU,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC;YAChE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAChC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAEhC,mBAAmB;YACnB,IAAI,MAAM,GAAG,IAAI,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;YACpC,IAAI,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;YAC3C,MAAM,iBAAiB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAE5C,QAAQ,GAAG,OAAO,CAAC,oBAAoB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YAC7D,MAAM,GAAG,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAEpD,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;YAC3C,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,oBAAoB,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAEhI,wCAAwC;YACxC,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;YAC/B,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;YAE/B,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;YAEjC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;SACpC;KACJ;IAED,QAAQ;IACR,UAAU,CAAC,aAAa,CAAC,eAAe,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IAE/G,SAAS;IACT,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;IAEpC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;IACjC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;IAErB,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAM,UAAU,WAAW,CACvB,IAAY,EACZ,UAAkK,EAAE,EACpK,KAAa;IAEb,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAEpC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACnF,KAAK,CAAC,+BAA+B,GAAG,OAAO,CAAC,eAAe,CAAC;IAEhE,MAAM,UAAU,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAElD,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAEjD,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG;IACxB,gEAAgE;IAChE,WAAW;CACd,CAAC;AAEF,UAAU,CAAC,WAAW,GAAG,qBAAqB,CAAC;AAE/C,IAAI,CAAC,WAAW,GAAG,CAAC,IAAY,EAAE,QAAgB,EAAE,SAAiB,EAAE,YAAoB,EAAE,KAAa,EAAE,SAAmB,EAAE,eAAwB,EAAQ,EAAE;IAC/J,MAAM,OAAO,GAAG;QACZ,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,eAAe;QACf,SAAS;KACZ,CAAC;IAEF,OAAO,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC7C,CAAC,CAAC", "sourcesContent": ["import type { Vector4 } from \"../../Maths/math.vector\";\r\nimport { Matrix, Vector3, Vector2 } from \"../../Maths/math.vector\";\r\nimport { Mesh } from \"../mesh\";\r\nimport { VertexData } from \"../mesh.vertexData\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { CompatibilityOptions } from \"../../Compat/compatibilityOptions\";\r\n\r\n/**\r\n * Creates the VertexData for a torus\r\n * @param options an object used to set the following optional parameters for the box, required but can be empty\r\n * * diameter the diameter of the torus, optional default 1\r\n * * thickness the diameter of the tube forming the torus, optional default 0.5\r\n * * tessellation the number of prism sides, 3 for a triangular prism, optional, default 24\r\n * * sideOrientation optional and takes the values : Mesh.FRONTSIDE (default), Mesh.BACKSIDE or Mesh.DOUBLESIDE\r\n * * frontUvs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the front side, optional, default vector4 (0, 0, 1, 1)\r\n * * backUVs only usable when you create a double-sided mesh, used to choose what parts of the texture image to crop and apply on the back side, optional, default vector4 (0, 0, 1, 1)\r\n * @param options.diameter\r\n * @param options.thickness\r\n * @param options.tessellation\r\n * @param options.sideOrientation\r\n * @param options.frontUVs\r\n * @param options.backUVs\r\n * @returns the VertexData of the torus\r\n */\r\nexport function CreateTorusVertexData(options: { diameter?: number; thickness?: number; tessellation?: number; sideOrientation?: number; frontUVs?: Vector4; backUVs?: Vector4 }) {\r\n    const indices = [];\r\n    const positions = [];\r\n    const normals = [];\r\n    const uvs = [];\r\n\r\n    const diameter = options.diameter || 1;\r\n    const thickness = options.thickness || 0.5;\r\n    const tessellation = (options.tessellation || 16) | 0;\r\n    const sideOrientation = options.sideOrientation === 0 ? 0 : options.sideOrientation || VertexData.DEFAULTSIDE;\r\n\r\n    const stride = tessellation + 1;\r\n\r\n    for (let i = 0; i <= tessellation; i++) {\r\n        const u = i / tessellation;\r\n\r\n        const outerAngle = (i * Math.PI * 2.0) / tessellation - Math.PI / 2.0;\r\n\r\n        const transform = Matrix.Translation(diameter / 2.0, 0, 0).multiply(Matrix.RotationY(outerAngle));\r\n\r\n        for (let j = 0; j <= tessellation; j++) {\r\n            const v = 1 - j / tessellation;\r\n\r\n            const innerAngle = (j * Math.PI * 2.0) / tessellation + Math.PI;\r\n            const dx = Math.cos(innerAngle);\r\n            const dy = Math.sin(innerAngle);\r\n\r\n            // Create a vertex.\r\n            let normal = new Vector3(dx, dy, 0);\r\n            let position = normal.scale(thickness / 2);\r\n            const textureCoordinate = new Vector2(u, v);\r\n\r\n            position = Vector3.TransformCoordinates(position, transform);\r\n            normal = Vector3.TransformNormal(normal, transform);\r\n\r\n            positions.push(position.x, position.y, position.z);\r\n            normals.push(normal.x, normal.y, normal.z);\r\n            uvs.push(textureCoordinate.x, CompatibilityOptions.UseOpenGLOrientationForUV ? 1.0 - textureCoordinate.y : textureCoordinate.y);\r\n\r\n            // And create indices for two triangles.\r\n            const nextI = (i + 1) % stride;\r\n            const nextJ = (j + 1) % stride;\r\n\r\n            indices.push(i * stride + j);\r\n            indices.push(i * stride + nextJ);\r\n            indices.push(nextI * stride + j);\r\n\r\n            indices.push(i * stride + nextJ);\r\n            indices.push(nextI * stride + nextJ);\r\n            indices.push(nextI * stride + j);\r\n        }\r\n    }\r\n\r\n    // Sides\r\n    VertexData._ComputeSides(sideOrientation, positions, indices, normals, uvs, options.frontUVs, options.backUVs);\r\n\r\n    // Result\r\n    const vertexData = new VertexData();\r\n\r\n    vertexData.indices = indices;\r\n    vertexData.positions = positions;\r\n    vertexData.normals = normals;\r\n    vertexData.uvs = uvs;\r\n\r\n    return vertexData;\r\n}\r\n\r\n/**\r\n * Creates a torus mesh\r\n * * The parameter `diameter` sets the diameter size (float) of the torus (default 1)\r\n * * The parameter `thickness` sets the diameter size of the tube of the torus (float, default 0.5)\r\n * * The parameter `tessellation` sets the number of torus sides (positive integer, default 16)\r\n * * You can also set the mesh side orientation with the values : BABYLON.Mesh.FRONTSIDE (default), BABYLON.Mesh.BACKSIDE or BABYLON.Mesh.DOUBLESIDE\r\n * * If you create a double-sided mesh, you can choose what parts of the texture image to crop and stick respectively on the front and the back sides with the parameters `frontUVs` and `backUVs` (Vector4). Detail here : https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#side-orientation\r\n * * The mesh can be set to updatable with the boolean parameter `updatable` (default false) if its internal geometry is supposed to change once created.\r\n * @param name defines the name of the mesh\r\n * @param options defines the options used to create the mesh\r\n * @param options.diameter\r\n * @param options.thickness\r\n * @param options.tessellation\r\n * @param options.updatable\r\n * @param options.sideOrientation\r\n * @param options.frontUVs\r\n * @param options.backUVs\r\n * @param scene defines the hosting scene\r\n * @returns the torus mesh\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/creation/set#torus\r\n */\r\nexport function CreateTorus(\r\n    name: string,\r\n    options: { diameter?: number; thickness?: number; tessellation?: number; updatable?: boolean; sideOrientation?: number; frontUVs?: Vector4; backUVs?: Vector4 } = {},\r\n    scene?: Scene\r\n): Mesh {\r\n    const torus = new Mesh(name, scene);\r\n\r\n    options.sideOrientation = Mesh._GetDefaultSideOrientation(options.sideOrientation);\r\n    torus._originalBuilderSideOrientation = options.sideOrientation;\r\n\r\n    const vertexData = CreateTorusVertexData(options);\r\n\r\n    vertexData.applyToMesh(torus, options.updatable);\r\n\r\n    return torus;\r\n}\r\n\r\n/**\r\n * Class containing static functions to help procedurally build meshes\r\n * @deprecated use CreateTorus instead\r\n */\r\nexport const TorusBuilder = {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    CreateTorus,\r\n};\r\n\r\nVertexData.CreateTorus = CreateTorusVertexData;\r\n\r\nMesh.CreateTorus = (name: string, diameter: number, thickness: number, tessellation: number, scene?: Scene, updatable?: boolean, sideOrientation?: number): Mesh => {\r\n    const options = {\r\n        diameter,\r\n        thickness,\r\n        tessellation,\r\n        sideOrientation,\r\n        updatable,\r\n    };\r\n\r\n    return CreateTorus(name, options, scene);\r\n};\r\n"]}