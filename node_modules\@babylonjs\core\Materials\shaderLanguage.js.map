{"version": 3, "file": "shaderLanguage.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Materials/shaderLanguage.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,CAAN,IAAY,cAKX;AALD,WAAY,cAAc;IACtB,uCAAuC;IACvC,mDAAI,CAAA;IACJ,wCAAwC;IACxC,mDAAI,CAAA;AACR,CAAC,EALW,cAAc,KAAd,cAAc,QAKzB", "sourcesContent": ["/**\r\n * Language of the shader code\r\n */\r\nexport enum ShaderLanguage {\r\n    /** language is GLSL (used by WebGL) */\r\n    GLSL,\r\n    /** language is WGSL (used by WebGPU) */\r\n    WGSL,\r\n}\r\n"]}