{"version": 3, "file": "waveBlock.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Materials/Node/Blocks/waveBlock.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,qCAAqC,EAAE,MAAM,gDAAgD,CAAC;AAGvG,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAGxD;;GAEG;AACH,MAAM,CAAN,IAAY,aAOX;AAPD,WAAY,aAAa;IACrB,eAAe;IACf,yDAAQ,CAAA;IACR,aAAa;IACb,qDAAM,CAAA;IACN,eAAe;IACf,yDAAQ,CAAA;AACZ,CAAC,EAPW,aAAa,KAAb,aAAa,QAOxB;AAED;;GAEG;AACH,MAAM,OAAO,SAAU,SAAQ,iBAAiB;IAM5C;;;OAGG;IACH,YAAmB,IAAY;QAC3B,KAAK,CAAC,IAAI,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAVlD;;WAEG;QACI,SAAI,GAAG,aAAa,CAAC,QAAQ,CAAC;QASjC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qCAAqC,CAAC,UAAU,CAAC,CAAC;QAC9E,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qCAAqC,CAAC,YAAY,CAAC,CAAC;QAElF,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEzD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,4BAA4B,CAAC,IAAI,CAAC,qCAAqC,CAAC,MAAM,CAAC,CAAC;IACpG,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAES,WAAW,CAAC,KAA6B;QAC/C,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEhC,QAAQ,IAAI,CAAC,IAAI,EAAE;YACf,KAAK,aAAa,CAAC,QAAQ,CAAC,CAAC;gBACzB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,sBAAsB,kBAAkB,IAAI,CAAC,KAAK,CAAC,sBAAsB,MAAM,CAAC;gBACjK,MAAM;aACT;YACD,KAAK,aAAa,CAAC,MAAM,CAAC,CAAC;gBACvB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,8BAA8B,IAAI,CAAC,KAAK,CAAC,sBAAsB,OAAO,CAAC;gBACvI,MAAM;aACT;YACD,KAAK,aAAa,CAAC,QAAQ,CAAC,CAAC;gBACzB,KAAK,CAAC,iBAAiB;oBACnB,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,uBAAuB,IAAI,CAAC,KAAK,CAAC,sBAAsB,kBAAkB,IAAI,CAAC,KAAK,CAAC,sBAAsB,cAAc,CAAC;gBACnK,MAAM;aACT;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAErC,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe;QACvE,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC,IAAI,CAAC;IACzC,CAAC;CACJ;AAED,aAAa,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC", "sourcesContent": ["import { NodeMaterialBlock } from \"../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../Misc/typeStore\";\r\nimport type { Scene } from \"../../../scene\";\r\n\r\n/**\r\n * Operations supported by the Wave block\r\n */\r\nexport enum WaveBlockKind {\r\n    /** SawTooth */\r\n    SawTooth,\r\n    /** Square */\r\n    Square,\r\n    /** Triangle */\r\n    Triangle,\r\n}\r\n\r\n/**\r\n * Block used to apply wave operation to floats\r\n */\r\nexport class WaveBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Gets or sets the kibnd of wave to be applied by the block\r\n     */\r\n    public kind = WaveBlockKind.SawTooth;\r\n\r\n    /**\r\n     * Creates a new WaveBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerInput(\"input\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.BasedOnInput);\r\n\r\n        this._outputs[0]._typeConnectionSource = this._inputs[0];\r\n\r\n        this._inputs[0].excludedConnectionPointTypes.push(NodeMaterialBlockConnectionPointTypes.Matrix);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public getClassName() {\r\n        return \"WaveBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the input component\r\n     */\r\n    public get input(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const output = this._outputs[0];\r\n\r\n        switch (this.kind) {\r\n            case WaveBlockKind.SawTooth: {\r\n                state.compilationString += this._declareOutput(output, state) + ` = ${this.input.associatedVariableName} - floor(0.5 + ${this.input.associatedVariableName});\\n`;\r\n                break;\r\n            }\r\n            case WaveBlockKind.Square: {\r\n                state.compilationString += this._declareOutput(output, state) + ` = 1.0 - 2.0 * round(fract(${this.input.associatedVariableName}));\\n`;\r\n                break;\r\n            }\r\n            case WaveBlockKind.Triangle: {\r\n                state.compilationString +=\r\n                    this._declareOutput(output, state) + ` = 2.0 * abs(2.0 * (${this.input.associatedVariableName} - floor(0.5 + ${this.input.associatedVariableName}))) - 1.0;\\n`;\r\n                break;\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.kind = this.kind;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.kind = serializationObject.kind;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.WaveBlock\", WaveBlock);\r\n"]}