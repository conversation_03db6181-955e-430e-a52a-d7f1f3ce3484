{"version": 3, "file": "postProcessRenderPipelineManagerSceneComponent.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/PostProcesses/RenderPipeline/postProcessRenderPipelineManagerSceneComponent.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,uBAAuB,EAAE,MAAM,sBAAsB,CAAC;AAC/D,OAAO,EAAE,gCAAgC,EAAE,MAAM,oCAAoC,CAAC;AACtF,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAgBpC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,kCAAkC,EAAE;IACvE,GAAG,EAAE;QACD,IAAI,CAAC,IAAI,CAAC,iCAAiC,EAAE;YACzC,gDAAgD;YAChD,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,qCAAqC,CAAmD,CAAC;YACpJ,IAAI,CAAC,SAAS,EAAE;gBACZ,SAAS,GAAG,IAAI,8CAA8C,CAAC,IAAI,CAAC,CAAC;gBACrE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;aACjC;YACD,IAAI,CAAC,iCAAiC,GAAG,IAAI,gCAAgC,EAAE,CAAC;SACnF;QAED,OAAO,IAAI,CAAC,iCAAiC,CAAC;IAClD,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,OAAO,8CAA8C;IAWvD;;;OAGG;IACH,YAAY,KAAY;QAdxB;;WAEG;QACa,SAAI,GAAG,uBAAuB,CAAC,qCAAqC,CAAC;QAYjF,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,YAAY,CAAC,uBAAuB,CAAC,yDAAyD,EAAE,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAC1K,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,KAAK,CAAC,iCAAiC,EAAE;YAC9C,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,QAAQ,EAAE,CAAC;SAC3D;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,KAAK,CAAC,iCAAiC,EAAE;YAC9C,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,OAAO,EAAE,CAAC;SAC1D;IACL,CAAC;IAEO,oBAAoB;QACxB,IAAI,IAAI,CAAC,KAAK,CAAC,iCAAiC,EAAE;YAC9C,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,MAAM,EAAE,CAAC;SACzD;IACL,CAAC;CACJ", "sourcesContent": ["import type { ISceneComponent } from \"../../sceneComponent\";\r\nimport { SceneComponentConstants } from \"../../sceneComponent\";\r\nimport { PostProcessRenderPipelineManager } from \"./postProcessRenderPipelineManager\";\r\nimport { Scene } from \"../../scene\";\r\n\r\ndeclare module \"../../scene\" {\r\n    export interface Scene {\r\n        /** @internal (Backing field) */\r\n        _postProcessRenderPipelineManager: PostProcessRenderPipelineManager;\r\n\r\n        /**\r\n         * Gets the postprocess render pipeline manager\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/postProcessRenderPipeline\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/postProcesses/defaultRenderingPipeline\r\n         */\r\n        readonly postProcessRenderPipelineManager: PostProcessRenderPipelineManager;\r\n    }\r\n}\r\n\r\nObject.defineProperty(Scene.prototype, \"postProcessRenderPipelineManager\", {\r\n    get: function (this: Scene) {\r\n        if (!this._postProcessRenderPipelineManager) {\r\n            // Register the G Buffer component to the scene.\r\n            let component = this._getComponent(SceneComponentConstants.NAME_POSTPROCESSRENDERPIPELINEMANAGER) as PostProcessRenderPipelineManagerSceneComponent;\r\n            if (!component) {\r\n                component = new PostProcessRenderPipelineManagerSceneComponent(this);\r\n                this._addComponent(component);\r\n            }\r\n            this._postProcessRenderPipelineManager = new PostProcessRenderPipelineManager();\r\n        }\r\n\r\n        return this._postProcessRenderPipelineManager;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\n/**\r\n * Defines the Render Pipeline scene component responsible to rendering pipelines\r\n */\r\nexport class PostProcessRenderPipelineManagerSceneComponent implements ISceneComponent {\r\n    /**\r\n     * The component name helpful to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_POSTPROCESSRENDERPIPELINEMANAGER;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene: Scene) {\r\n        this.scene = scene;\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {\r\n        this.scene._gatherRenderTargetsStage.registerStep(SceneComponentConstants.STEP_GATHERRENDERTARGETS_POSTPROCESSRENDERPIPELINEMANAGER, this, this._gatherRenderTargets);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        if (this.scene._postProcessRenderPipelineManager) {\r\n            this.scene._postProcessRenderPipelineManager._rebuild();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disposes the component and the associated resources\r\n     */\r\n    public dispose(): void {\r\n        if (this.scene._postProcessRenderPipelineManager) {\r\n            this.scene._postProcessRenderPipelineManager.dispose();\r\n        }\r\n    }\r\n\r\n    private _gatherRenderTargets(): void {\r\n        if (this.scene._postProcessRenderPipelineManager) {\r\n            this.scene._postProcessRenderPipelineManager.update();\r\n        }\r\n    }\r\n}\r\n"]}