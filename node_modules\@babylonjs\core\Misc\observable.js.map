{"version": 3, "file": "observable.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Misc/observable.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,OAAO,UAAU;IACnB;;;;;;OAMG;IACH,YAAY,IAAY,EAAE,iBAAiB,GAAG,KAAK,EAAE,MAAY,EAAE,aAAmB;QAClF,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;OAOG;IACI,UAAU,CAAC,IAAY,EAAE,iBAAiB,GAAG,KAAK,EAAE,MAAY,EAAE,aAAmB;QACxF,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,OAAO,IAAI,CAAC;IAChB,CAAC;CAgCJ;AAED;;GAEG;AACH,MAAM,OAAO,QAAQ;IAejB;;;;;OAKG;IACH;IACI;;OAEG;IACI,QAAwD;IAC/D;;OAEG;IACI,IAAY;IACnB;;OAEG;IACI,QAAa,IAAI;QARjB,aAAQ,GAAR,QAAQ,CAAgD;QAIxD,SAAI,GAAJ,IAAI,CAAQ;QAIZ,UAAK,GAAL,KAAK,CAAY;QAhC5B,gBAAgB;QACT,wBAAmB,GAAG,KAAK,CAAC;QACnC;;WAEG;QACI,yBAAoB,GAAG,KAAK,CAAC;QAEpC;;;;WAIG;QACI,YAAO,GAAyB,IAAI,CAAC;IAqBzC,CAAC;IAEJ;;;OAGG;IACI,MAAM;QACT,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,EAAE,CAAC;SAClB;IACL,CAAC;CACJ;AAED;;;;;;;GAOG;AACH,MAAM,OAAO,UAAU;IAanB;;;;;OAKG;IACI,MAAM,CAAC,WAAW,CAAe,OAAmB,EAAE,iBAAiC;QAC1F,MAAM,UAAU,GAAG,IAAI,UAAU,EAAK,CAAC;QAEvC,OAAO;aACF,IAAI,CAAC,CAAC,GAAM,EAAE,EAAE;YACb,UAAU,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QACpC,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACX,IAAI,iBAAiB,EAAE;gBACnB,iBAAiB,CAAC,eAAe,CAAC,GAAQ,CAAC,CAAC;aAC/C;iBAAM;gBACH,MAAM,GAAG,CAAC;aACb;QACL,CAAC,CAAC,CAAC;QAEP,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACH,YACI,eAAiD;IACjD;;;OAGG;IACI,oBAAoB,KAAK;QAAzB,sBAAiB,GAAjB,iBAAiB,CAAQ;QAvD5B,eAAU,GAAG,IAAI,KAAK,EAAe,CAAC;QACtC,iCAA4B,GAAG,CAAC,CAAC;QACjC,iBAAY,GAAG,KAAK,CAAC;QAuDzB,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;QAErC,IAAI,eAAe,EAAE;YACjB,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;SAC3C;IACL,CAAC;IAoBM,GAAG,CACN,QAA8E,EAC9E,OAAe,CAAC,CAAC,EACjB,WAAW,GAAG,KAAK,EACnB,QAAa,IAAI,EACjB,qBAAqB,GAAG,KAAK;QAE7B,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,IAAI,CAAC;SACf;QAED,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACrD,QAAQ,CAAC,oBAAoB,GAAG,qBAAqB,CAAC;QAEtD,IAAI,WAAW,EAAE;YACb,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SACrC;aAAM;YACH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;SACnC;QAED,oHAAoH;QACpH,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC7C,IAAI,IAAI,CAAC,kBAAkB,KAAK,SAAS,EAAE;gBACvC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;aAC1D;SACJ;QACD,6CAA6C;QAC7C,QAAQ,CAAC,OAAO,GAAG,GAAG,EAAE;YACpB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC,CAAC;QAEF,OAAO,QAAQ,CAAC;IACpB,CAAC;IAUM,OAAO,CAAC,QAA8E;QACzF,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IACrE,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,QAA+B;QACzC,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,KAAK,CAAC;SAChB;QAED,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;QACxB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAChC,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,QAAwD,EAAE,KAAW;QACvF,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,QAAQ,CAAC,mBAAmB,EAAE;gBAC9B,SAAS;aACZ;YACD,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,IAAI,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACxE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAChC,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,QAAqB;QACzC,IAAI,QAAQ,CAAC,mBAAmB,EAAE;YAC9B,OAAO;SACV;QACD,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,QAAQ,CAAC,oBAAoB,GAAG,KAAK,CAAC;QACtC,QAAQ,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACpC,UAAU,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC3B,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC;IAED,4FAA4F;IAC5F,gDAAgD;IACxC,OAAO,CAAC,QAA+B,EAAE,aAAa,GAAG,IAAI;QACjE,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,aAAa,EAAE;gBACf,IAAI,CAAC,4BAA4B,EAAE,CAAC;aACvC;YACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACjC,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,QAAqB;QAChD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,0BAA0B,CAAC,QAAqB;QACnD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACnC,CAAC;IAED;;;;;;;;;OASG;IACI,eAAe,CAAC,SAAY,EAAE,OAAe,CAAC,CAAC,EAAE,MAAY,EAAE,aAAmB,EAAE,QAAc;QACrG,0GAA0G;QAC1G,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;SACvC;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YACzB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;QAC/B,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QACtB,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;QACpC,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAChC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC;QAClC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAE1B,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;YAC/B,IAAI,GAAG,CAAC,mBAAmB,EAAE;gBACzB,SAAS;aACZ;YAED,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,EAAE;gBACjB,IAAI,GAAG,CAAC,oBAAoB,EAAE;oBAC1B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;iBAC9B;gBAED,IAAI,GAAG,CAAC,KAAK,EAAE;oBACX,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;iBAC7E;qBAAM;oBACH,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;iBAC1D;aACJ;YACD,IAAI,KAAK,CAAC,iBAAiB,EAAE;gBACzB,OAAO,KAAK,CAAC;aAChB;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,QAAqB,EAAE,SAAY,EAAE,OAAe,CAAC,CAAC;QACxE,0GAA0G;QAC1G,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;SACvC;QACD,IAAI,QAAQ,CAAC,mBAAmB,EAAE;YAC9B,OAAO;SACV;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;QAC/B,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAEhC,IAAI,QAAQ,CAAC,oBAAoB,EAAE;YAC/B,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;SACnC;QAED,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,4BAA4B,GAAG,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACI,KAAK;QACR,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YAC3B,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YAChC,IAAI,CAAC,EAAE;gBACH,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;aACpB;SACJ;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,4BAA4B,GAAG,CAAC,CAAC;QACtC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,sBAAsB;QACzB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,MAAM,GAAG,IAAI,UAAU,EAAK,CAAC;QAEnC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAE7C,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;QAII;IACG,eAAe,CAAC,OAAe,CAAC,CAAC;QACpC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;YAC/B,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE;gBACtC,OAAO,IAAI,CAAC;aACf;SACJ;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\n\r\n/**\r\n * A class serves as a medium between the observable and its observers\r\n */\r\nexport class EventState {\r\n    /**\r\n     * Create a new EventState\r\n     * @param mask defines the mask associated with this state\r\n     * @param skipNextObservers defines a flag which will instruct the observable to skip following observers when set to true\r\n     * @param target defines the original target of the state\r\n     * @param currentTarget defines the current target of the state\r\n     */\r\n    constructor(mask: number, skipNextObservers = false, target?: any, currentTarget?: any) {\r\n        this.initialize(mask, skipNextObservers, target, currentTarget);\r\n    }\r\n\r\n    /**\r\n     * Initialize the current event state\r\n     * @param mask defines the mask associated with this state\r\n     * @param skipNextObservers defines a flag which will instruct the observable to skip following observers when set to true\r\n     * @param target defines the original target of the state\r\n     * @param currentTarget defines the current target of the state\r\n     * @returns the current event state\r\n     */\r\n    public initialize(mask: number, skipNextObservers = false, target?: any, currentTarget?: any): EventState {\r\n        this.mask = mask;\r\n        this.skipNextObservers = skipNextObservers;\r\n        this.target = target;\r\n        this.currentTarget = currentTarget;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * An Observer can set this property to true to prevent subsequent observers of being notified\r\n     */\r\n    public skipNextObservers: boolean;\r\n\r\n    /**\r\n     * Get the mask value that were used to trigger the event corresponding to this EventState object\r\n     */\r\n    public mask: number;\r\n\r\n    /**\r\n     * The object that originally notified the event\r\n     */\r\n    public target?: any;\r\n\r\n    /**\r\n     * The current object in the bubbling phase\r\n     */\r\n    public currentTarget?: any;\r\n\r\n    /**\r\n     * This will be populated with the return value of the last function that was executed.\r\n     * If it is the first function in the callback chain it will be the event data.\r\n     */\r\n    public lastReturnValue?: any;\r\n\r\n    /**\r\n     * User defined information that will be sent to observers\r\n     */\r\n    public userInfo?: any;\r\n}\r\n\r\n/**\r\n * Represent an Observer registered to a given Observable object.\r\n */\r\nexport class Observer<T> {\r\n    /** @internal */\r\n    public _willBeUnregistered = false;\r\n    /**\r\n     * Gets or sets a property defining that the observer as to be unregistered after the next notification\r\n     */\r\n    public unregisterOnNextCall = false;\r\n\r\n    /**\r\n     * this function can be used to remove the observer from the observable.\r\n     * It will be set by the observable that the observer belongs to.\r\n     * @internal\r\n     */\r\n    public _remove: Nullable<() => void> = null;\r\n\r\n    /**\r\n     * Creates a new observer\r\n     * @param callback defines the callback to call when the observer is notified\r\n     * @param mask defines the mask of the observer (used to filter notifications)\r\n     * @param scope defines the current scope used to restore the JS context\r\n     */\r\n    constructor(\r\n        /**\r\n         * Defines the callback to call when the observer is notified\r\n         */\r\n        public callback: (eventData: T, eventState: EventState) => void,\r\n        /**\r\n         * Defines the mask of the observer (used to filter notifications)\r\n         */\r\n        public mask: number,\r\n        /**\r\n         * Defines the current scope used to restore the JS context\r\n         */\r\n        public scope: any = null\r\n    ) {}\r\n\r\n    /**\r\n     * Remove the observer from its observable\r\n     * This can be used instead of using the observable's remove function.\r\n     */\r\n    public remove() {\r\n        if (this._remove) {\r\n            this._remove();\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * The Observable class is a simple implementation of the Observable pattern.\r\n *\r\n * There's one slight particularity though: a given Observable can notify its observer using a particular mask value, only the Observers registered with this mask value will be notified.\r\n * This enable a more fine grained execution without having to rely on multiple different Observable objects.\r\n * For instance you may have a given Observable that have four different types of notifications: Move (mask = 0x01), Stop (mask = 0x02), Turn Right (mask = 0X04), Turn Left (mask = 0X08).\r\n * A given observer can register itself with only Move and Stop (mask = 0x03), then it will only be notified when one of these two occurs and will never be for Turn Left/Right.\r\n */\r\nexport class Observable<T> {\r\n    private _observers = new Array<Observer<T>>();\r\n    private _numObserversMarkedAsDeleted = 0;\r\n    private _hasNotified = false;\r\n    private _lastNotifiedValue?: T;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _eventState: EventState;\r\n\r\n    private _onObserverAdded: Nullable<(observer: Observer<T>) => void>;\r\n\r\n    /**\r\n     * Create an observable from a Promise.\r\n     * @param promise a promise to observe for fulfillment.\r\n     * @param onErrorObservable an observable to notify if a promise was rejected.\r\n     * @returns the new Observable\r\n     */\r\n    public static FromPromise<T, E = Error>(promise: Promise<T>, onErrorObservable?: Observable<E>): Observable<T> {\r\n        const observable = new Observable<T>();\r\n\r\n        promise\r\n            .then((ret: T) => {\r\n                observable.notifyObservers(ret);\r\n            })\r\n            .catch((err) => {\r\n                if (onErrorObservable) {\r\n                    onErrorObservable.notifyObservers(err as E);\r\n                } else {\r\n                    throw err;\r\n                }\r\n            });\r\n\r\n        return observable;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of observers\r\n     * Note that observers that were recently deleted may still be present in the list because they are only really deleted on the next javascript tick!\r\n     */\r\n    public get observers(): Array<Observer<T>> {\r\n        return this._observers;\r\n    }\r\n\r\n    /**\r\n     * Creates a new observable\r\n     * @param onObserverAdded defines a callback to call when a new observer is added\r\n     * @param notifyIfTriggered If set to true the observable will notify when an observer was added if the observable was already triggered.\r\n     */\r\n    constructor(\r\n        onObserverAdded?: (observer: Observer<T>) => void,\r\n        /**\r\n         * If set to true the observable will notify when an observer was added if the observable was already triggered.\r\n         * This is helpful to single-state observables like the scene onReady or the dispose observable.\r\n         */\r\n        public notifyIfTriggered = false\r\n    ) {\r\n        this._eventState = new EventState(0);\r\n\r\n        if (onObserverAdded) {\r\n            this._onObserverAdded = onObserverAdded;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create a new Observer with the specified callback\r\n     * @param callback the callback that will be executed for that Observer\r\n     * @param mask the mask used to filter observers\r\n     * @param insertFirst if true the callback will be inserted at the first position, hence executed before the others ones. If false (default behavior) the callback will be inserted at the last position, executed after all the others already present.\r\n     * @param scope optional scope for the callback to be called from\r\n     * @param unregisterOnFirstCall defines if the observer as to be unregistered after the next notification\r\n     * @returns the new observer created for the callback\r\n     */\r\n    public add(callback?: null | undefined, mask?: number, insertFirst?: boolean, scope?: any, unregisterOnFirstCall?: boolean): null;\r\n    public add(callback: (eventData: T, eventState: EventState) => void, mask?: number, insertFirst?: boolean, scope?: any, unregisterOnFirstCall?: boolean): Observer<T>;\r\n    public add(\r\n        callback?: ((eventData: T, eventState: EventState) => void) | null | undefined,\r\n        mask?: number,\r\n        insertFirst?: boolean,\r\n        scope?: any,\r\n        unregisterOnFirstCall?: boolean\r\n    ): Nullable<Observer<T>>;\r\n    public add(\r\n        callback?: ((eventData: T, eventState: EventState) => void) | null | undefined,\r\n        mask: number = -1,\r\n        insertFirst = false,\r\n        scope: any = null,\r\n        unregisterOnFirstCall = false\r\n    ): Nullable<Observer<T>> {\r\n        if (!callback) {\r\n            return null;\r\n        }\r\n\r\n        const observer = new Observer(callback, mask, scope);\r\n        observer.unregisterOnNextCall = unregisterOnFirstCall;\r\n\r\n        if (insertFirst) {\r\n            this._observers.unshift(observer);\r\n        } else {\r\n            this._observers.push(observer);\r\n        }\r\n\r\n        if (this._onObserverAdded) {\r\n            this._onObserverAdded(observer);\r\n        }\r\n\r\n        // If the observable was already triggered and the observable is set to notify if triggered, notify the new observer\r\n        if (this._hasNotified && this.notifyIfTriggered) {\r\n            if (this._lastNotifiedValue !== undefined) {\r\n                this.notifyObserver(observer, this._lastNotifiedValue);\r\n            }\r\n        }\r\n        // attach the remove function to the observer\r\n        observer._remove = () => {\r\n            this.remove(observer);\r\n        };\r\n\r\n        return observer;\r\n    }\r\n\r\n    /**\r\n     * Create a new Observer with the specified callback and unregisters after the next notification\r\n     * @param callback the callback that will be executed for that Observer\r\n     * @returns the new observer created for the callback\r\n     */\r\n    public addOnce(callback?: null | undefined): null;\r\n    public addOnce(callback: (eventData: T, eventState: EventState) => void): Observer<T>;\r\n    public addOnce(callback?: ((eventData: T, eventState: EventState) => void) | null | undefined): Nullable<Observer<T>>;\r\n    public addOnce(callback?: ((eventData: T, eventState: EventState) => void) | null | undefined): Nullable<Observer<T>> {\r\n        return this.add(callback, undefined, undefined, undefined, true);\r\n    }\r\n\r\n    /**\r\n     * Remove an Observer from the Observable object\r\n     * @param observer the instance of the Observer to remove\r\n     * @returns false if it doesn't belong to this Observable\r\n     */\r\n    public remove(observer: Nullable<Observer<T>>): boolean {\r\n        if (!observer) {\r\n            return false;\r\n        }\r\n\r\n        observer._remove = null;\r\n        const index = this._observers.indexOf(observer);\r\n\r\n        if (index !== -1) {\r\n            this._deferUnregister(observer);\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Remove a callback from the Observable object\r\n     * @param callback the callback to remove\r\n     * @param scope optional scope. If used only the callbacks with this scope will be removed\r\n     * @returns false if it doesn't belong to this Observable\r\n     */\r\n    public removeCallback(callback: (eventData: T, eventState: EventState) => void, scope?: any): boolean {\r\n        for (let index = 0; index < this._observers.length; index++) {\r\n            const observer = this._observers[index];\r\n            if (observer._willBeUnregistered) {\r\n                continue;\r\n            }\r\n            if (observer.callback === callback && (!scope || scope === observer.scope)) {\r\n                this._deferUnregister(observer);\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _deferUnregister(observer: Observer<T>): void {\r\n        if (observer._willBeUnregistered) {\r\n            return;\r\n        }\r\n        this._numObserversMarkedAsDeleted++;\r\n        observer.unregisterOnNextCall = false;\r\n        observer._willBeUnregistered = true;\r\n        setTimeout(() => {\r\n            this._remove(observer);\r\n        }, 0);\r\n    }\r\n\r\n    // This should only be called when not iterating over _observers to avoid callback skipping.\r\n    // Removes an observer from the _observer Array.\r\n    private _remove(observer: Nullable<Observer<T>>, updateCounter = true): boolean {\r\n        if (!observer) {\r\n            return false;\r\n        }\r\n\r\n        const index = this._observers.indexOf(observer);\r\n\r\n        if (index !== -1) {\r\n            if (updateCounter) {\r\n                this._numObserversMarkedAsDeleted--;\r\n            }\r\n            this._observers.splice(index, 1);\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Moves the observable to the top of the observer list making it get called first when notified\r\n     * @param observer the observer to move\r\n     */\r\n    public makeObserverTopPriority(observer: Observer<T>) {\r\n        this._remove(observer, false);\r\n        this._observers.unshift(observer);\r\n    }\r\n\r\n    /**\r\n     * Moves the observable to the bottom of the observer list making it get called last when notified\r\n     * @param observer the observer to move\r\n     */\r\n    public makeObserverBottomPriority(observer: Observer<T>) {\r\n        this._remove(observer, false);\r\n        this._observers.push(observer);\r\n    }\r\n\r\n    /**\r\n     * Notify all Observers by calling their respective callback with the given data\r\n     * Will return true if all observers were executed, false if an observer set skipNextObservers to true, then prevent the subsequent ones to execute\r\n     * @param eventData defines the data to send to all observers\r\n     * @param mask defines the mask of the current notification (observers with incompatible mask (ie mask & observer.mask === 0) will not be notified)\r\n     * @param target defines the original target of the state\r\n     * @param currentTarget defines the current target of the state\r\n     * @param userInfo defines any user info to send to observers\r\n     * @returns false if the complete observer chain was not processed (because one observer set the skipNextObservers to true)\r\n     */\r\n    public notifyObservers(eventData: T, mask: number = -1, target?: any, currentTarget?: any, userInfo?: any): boolean {\r\n        // this prevents potential memory leaks - if an object is disposed but the observable doesn't get cleared.\r\n        if (this.notifyIfTriggered) {\r\n            this._hasNotified = true;\r\n            this._lastNotifiedValue = eventData;\r\n        }\r\n        if (!this._observers.length) {\r\n            return true;\r\n        }\r\n\r\n        const state = this._eventState;\r\n        state.mask = mask;\r\n        state.target = target;\r\n        state.currentTarget = currentTarget;\r\n        state.skipNextObservers = false;\r\n        state.lastReturnValue = eventData;\r\n        state.userInfo = userInfo;\r\n\r\n        for (const obs of this._observers) {\r\n            if (obs._willBeUnregistered) {\r\n                continue;\r\n            }\r\n\r\n            if (obs.mask & mask) {\r\n                if (obs.unregisterOnNextCall) {\r\n                    this._deferUnregister(obs);\r\n                }\r\n\r\n                if (obs.scope) {\r\n                    state.lastReturnValue = obs.callback.apply(obs.scope, [eventData, state]);\r\n                } else {\r\n                    state.lastReturnValue = obs.callback(eventData, state);\r\n                }\r\n            }\r\n            if (state.skipNextObservers) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Notify a specific observer\r\n     * @param observer defines the observer to notify\r\n     * @param eventData defines the data to be sent to each callback\r\n     * @param mask is used to filter observers defaults to -1\r\n     */\r\n    public notifyObserver(observer: Observer<T>, eventData: T, mask: number = -1): void {\r\n        // this prevents potential memory leaks - if an object is disposed but the observable doesn't get cleared.\r\n        if (this.notifyIfTriggered) {\r\n            this._hasNotified = true;\r\n            this._lastNotifiedValue = eventData;\r\n        }\r\n        if (observer._willBeUnregistered) {\r\n            return;\r\n        }\r\n\r\n        const state = this._eventState;\r\n        state.mask = mask;\r\n        state.skipNextObservers = false;\r\n\r\n        if (observer.unregisterOnNextCall) {\r\n            this._deferUnregister(observer);\r\n        }\r\n\r\n        observer.callback(eventData, state);\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the observable has at least one observer\r\n     * @returns true is the Observable has at least one Observer registered\r\n     */\r\n    public hasObservers(): boolean {\r\n        return this._observers.length - this._numObserversMarkedAsDeleted > 0;\r\n    }\r\n\r\n    /**\r\n     * Clear the list of observers\r\n     */\r\n    public clear(): void {\r\n        while (this._observers.length) {\r\n            const o = this._observers.pop();\r\n            if (o) {\r\n                o._remove = null;\r\n            }\r\n        }\r\n        this._onObserverAdded = null;\r\n        this._numObserversMarkedAsDeleted = 0;\r\n        this.cleanLastNotifiedState();\r\n    }\r\n\r\n    /**\r\n     * Clean the last notified state - both the internal last value and the has-notified flag\r\n     */\r\n    public cleanLastNotifiedState(): void {\r\n        this._hasNotified = false;\r\n        this._lastNotifiedValue = undefined;\r\n    }\r\n\r\n    /**\r\n     * Clone the current observable\r\n     * @returns a new observable\r\n     */\r\n    public clone(): Observable<T> {\r\n        const result = new Observable<T>();\r\n\r\n        result._observers = this._observers.slice(0);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Does this observable handles observer registered with a given mask\r\n     * @param mask defines the mask to be tested\r\n     * @returns whether or not one observer registered with the given mask is handled\r\n     **/\r\n    public hasSpecificMask(mask: number = -1): boolean {\r\n        for (const obs of this._observers) {\r\n            if (obs.mask & mask || obs.mask === mask) {\r\n                return true;\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n}\r\n"]}