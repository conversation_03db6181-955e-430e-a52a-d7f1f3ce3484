{"version": 3, "file": "stereoscopicRigMode.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/RigModes/stereoscopicRigMode.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AACnC,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AACrD,OAAO,EAAE,eAAe,EAAE,MAAM,qCAAqC,CAAC;AACtE,OAAO,EAAE,iCAAiC,EAAE,MAAM,sDAAsD,CAAC;AAEzG;;GAEG;AACH,MAAM,UAAU,sBAAsB,CAAC,MAAc;IACjD,MAAM,mBAAmB,GACrB,MAAM,CAAC,aAAa,KAAK,MAAM,CAAC,yCAAyC,IAAI,MAAM,CAAC,aAAa,KAAK,MAAM,CAAC,0CAA0C,CAAC;IAC5J,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,KAAK,MAAM,CAAC,0CAA0C,CAAC;IAC9F,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,KAAK,MAAM,CAAC,gCAAgC,CAAC;IACtF,sCAAsC;IACtC,IAAI,YAAY,EAAE;QACd,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,IAAI,GAAG,WAAW,EAAE,GAAG,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QACnH,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,IAAI,iCAAiC,CAAC,MAAM,CAAC,IAAI,GAAG,kBAAkB,EAAE,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;KACpJ;IACD,0CAA0C;SACrC;QACD,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACvI,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAC1D,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAC7B,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAC7B,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAC/B,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAClC,CAAC;KACL;AACL,CAAC", "sourcesContent": ["import { Camera } from \"../camera\";\r\nimport { Viewport } from \"../../Maths/math.viewport\";\r\nimport { PassPostProcess } from \"../../PostProcesses/passPostProcess\";\r\nimport { StereoscopicInterlacePostProcessI } from \"../../PostProcesses/stereoscopicInterlacePostProcess\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport function setStereoscopicRigMode(camera: Camera): void {\r\n    const isStereoscopicHoriz =\r\n        camera.cameraRigMode === Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL || camera.cameraRigMode === Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED;\r\n    const isCrossEye = camera.cameraRigMode === Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED;\r\n    const isInterlaced = camera.cameraRigMode === Camera.RIG_MODE_STEREOSCOPIC_INTERLACED;\r\n    // Use post-processors for interlacing\r\n    if (isInterlaced) {\r\n        camera._rigCameras[0]._rigPostProcess = new PassPostProcess(camera.name + \"_passthru\", 1.0, camera._rigCameras[0]);\r\n        camera._rigCameras[1]._rigPostProcess = new StereoscopicInterlacePostProcessI(camera.name + \"_stereoInterlace\", camera._rigCameras, false, true);\r\n    }\r\n    // Otherwise, create appropriate viewports\r\n    else {\r\n        camera._rigCameras[isCrossEye ? 1 : 0].viewport = new Viewport(0, 0, isStereoscopicHoriz ? 0.5 : 1.0, isStereoscopicHoriz ? 1.0 : 0.5);\r\n        camera._rigCameras[isCrossEye ? 0 : 1].viewport = new Viewport(\r\n            isStereoscopicHoriz ? 0.5 : 0,\r\n            isStereoscopicHoriz ? 0 : 0.5,\r\n            isStereoscopicHoriz ? 0.5 : 1.0,\r\n            isStereoscopicHoriz ? 1.0 : 0.5\r\n        );\r\n    }\r\n}\r\n"]}