{"version": 3, "file": "WebXRImageTracking.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/XR/features/WebXRImageTracking.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAEjF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AAEjD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAuDzC;;GAEG;AACH,IAAK,wBAOJ;AAPD,WAAK,wBAAwB;IACzB,6DAA6D;IAC7D,qFAAW,CAAA;IACX,8FAA8F;IAC9F,6EAAO,CAAA;IACP,gEAAgE;IAChE,+EAAQ,CAAA;AACZ,CAAC,EAPI,wBAAwB,KAAxB,wBAAwB,QAO5B;AAED;;;GAGG;AACH,MAAM,OAAO,kBAAmB,SAAQ,oBAAoB;IA+BxD;;;;OAIG;IACH,YACI,iBAAsC;IACtC;;OAEG;IACa,OAAmC;QAEnD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAFT,YAAO,GAAP,OAAO,CAA4B;QA7BvD;;;WAGG;QACI,sCAAiC,GAAuB,IAAI,UAAU,EAAE,CAAC;QAChF;;WAEG;QACI,oCAA+B,GAAmC,IAAI,UAAU,EAAE,CAAC;QAC1F;;WAEG;QACI,oCAA+B,GAAmC,IAAI,UAAU,EAAE,CAAC;QAElF,0BAAqB,GAA6B,wBAAwB,CAAC,WAAW,CAAC;QACvF,mBAAc,GAAyB,EAAE,CAAC;QAiB9C,IAAI,CAAC,mBAAmB,GAAG,gBAAgB,CAAC;IAChD,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACI,MAAM;QACT,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,EAAU;QACjC,OAAO,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,OAAO;QACV,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YACzC,YAAY,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,CAAC;QAC7C,IAAI,CAAC,iCAAiC,CAAC,KAAK,EAAE,CAAC;QAC/C,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,CAAC;IACjD,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,yBAAyB;QAClC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE;YACrD,OAAO,EAAE,CAAC;SACb;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/C,IAAI,OAAO,KAAK,CAAC,GAAG,KAAK,QAAQ,EAAE;gBAC/B,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,4BAA4B,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC3F;iBAAM;gBACH,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,+BAA+B;aACrE;QACL,CAAC,CAAC,CAAC;QAEH,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE3C,IAAI,CAAC,wBAAwB,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBACtD,OAAO;oBACH,KAAK;oBACL,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,uBAAuB;iBAClE,CAAC;YACN,CAAC,CAAC,CAAC;YAEH,OAAO;gBACH,aAAa,EAAE,IAAI,CAAC,wBAAwB;aAC/C,CAAC;SACL;QAAC,OAAO,EAAE,EAAE;YACT,KAAK,CAAC,KAAK,CAAC,kFAAkF,CAAC,CAAC;YAChG,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAES,UAAU,CAAC,QAAiB;QAClC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,IAAI,CAAC,qBAAqB,KAAK,wBAAwB,CAAC,OAAO,EAAE;YACtG,OAAO;SACV;QAED,wFAAwF;QACxF,oGAAoG;QACpG,IAAI,IAAI,CAAC,qBAAqB,KAAK,wBAAwB,CAAC,WAAW,EAAE;YACrE,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,OAAO;SACV;QAED,MAAM,mBAAmB,GAAG,QAAQ,CAAC,uBAAuB,EAAE,CAAC;QAC/D,KAAK,MAAM,MAAM,IAAI,mBAAmB,EAAE;YACtC,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC;YAEhC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YACpD,IAAI,CAAC,WAAW,EAAE;gBACd,wBAAwB;gBACxB,SAAS;aACZ;YAED,WAAW,CAAC,gBAAgB,GAAG,MAAM,CAAC;YACtC,IAAI,WAAW,CAAC,cAAc,KAAK,MAAM,CAAC,qBAAqB,EAAE;gBAC7D,WAAW,CAAC,cAAc,GAAG,MAAM,CAAC,qBAAqB,CAAC;gBAC1D,OAAO,GAAG,IAAI,CAAC;aAClB;YAED,2DAA2D;YAC3D,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAExF,IAAI,IAAI,EAAE;gBACN,MAAM,GAAG,GAAG,WAAW,CAAC,oBAAoB,CAAC;gBAC7C,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;gBACrD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,oBAAoB,EAAE;oBACpD,GAAG,CAAC,4BAA4B,EAAE,CAAC;iBACtC;gBACD,OAAO,GAAG,IAAI,CAAC;aAClB;YAED,MAAM,KAAK,GAAG,MAAM,CAAC,aAAa,CAAC;YACnC,MAAM,QAAQ,GAAG,KAAK,KAAK,UAAU,CAAC;YAEtC,IAAI,WAAW,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBACnC,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAChC,OAAO,GAAG,IAAI,CAAC;aAClB;YACD,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;aACrE;SACJ;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC3B,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,KAAK,wBAAwB,CAAC,WAAW,EAAE;YAC9H,OAAO;SACV;QAED,IAAI,CAAC,qBAAqB,GAAG,wBAAwB,CAAC,OAAO,CAAC;QAC9D,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;QACjF,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1C,IAAI,CAAC,qBAAqB,GAAG,wBAAwB,CAAC,WAAW,CAAC;YAClE,OAAO;SACV;QAED,2BAA2B;QAC3B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE;YAC/C,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,aAAa,EAAE;gBACnC,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;aAC/D;iBAAM;gBACH,MAAM,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;gBAChE,MAAM,WAAW,GAAuB;oBACpC,EAAE,EAAE,GAAG;oBACP,cAAc;oBACd,oBAAoB,EAAE,IAAI,MAAM,EAAE;oBAClC,KAAK,EAAE,cAAc,CAAC,KAAK,GAAG,cAAc,CAAC,MAAM;iBACtD,CAAC;gBACF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;gBACvC,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;aACrE;SACJ;QAED,IAAI,CAAC,qBAAqB,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB,CAAC,WAAW,CAAC;IACnI,CAAC;;AAjND;;GAEG;AACoB,uBAAI,GAAG,gBAAgB,CAAC,cAAc,AAAlC,CAAmC;AAC9D;;;;GAIG;AACoB,0BAAO,GAAG,CAAC,AAAJ,CAAK;AA2MvC,qBAAqB;AACrB,oBAAoB,CAAC,eAAe,CAChC,kBAAkB,CAAC,IAAI,EACvB,CAAC,gBAAgB,EAAE,OAAO,EAAE,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,IAAI,kBAAkB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AACnE,CAAC,EACD,kBAAkB,CAAC,OAAO,EAC1B,KAAK,CACR,CAAC", "sourcesContent": ["import { WebXRFeaturesManager, WebXRFeatureName } from \"../webXRFeaturesManager\";\r\nimport type { WebXRSessionManager } from \"../webXRSessionManager\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { WebXRAbstractFeature } from \"./WebXRAbstractFeature\";\r\nimport { Matrix } from \"../../Maths/math.vector\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n\r\n/**\r\n * Options interface for the background remover plugin\r\n */\r\nexport interface IWebXRImageTrackingOptions {\r\n    /**\r\n     * A required array with images to track\r\n     */\r\n    images: {\r\n        /**\r\n         * The source of the image. can be a URL or an image bitmap\r\n         */\r\n        src: string | ImageBitmap;\r\n        /**\r\n         * The estimated width in the real world (in meters)\r\n         */\r\n        estimatedRealWorldWidth: number; // In meters!\r\n    }[];\r\n}\r\n\r\n/**\r\n * An object representing an image tracked by the system\r\n */\r\nexport interface IWebXRTrackedImage {\r\n    /**\r\n     * The ID of this image (which is the same as the position in the array that was used to initialize the feature)\r\n     */\r\n    id: number;\r\n    /**\r\n     * Is the transformation provided emulated. If it is, the system \"guesses\" its real position. Otherwise it can be considered as exact position.\r\n     */\r\n    emulated?: boolean;\r\n    /**\r\n     * Just in case it is needed - the image bitmap that is being tracked\r\n     */\r\n    originalBitmap: ImageBitmap;\r\n    /**\r\n     * The native XR result image tracking result, untouched\r\n     */\r\n    xrTrackingResult?: XRImageTrackingResult;\r\n    /**\r\n     * Width in real world (meters)\r\n     */\r\n    realWorldWidth?: number;\r\n    /**\r\n     * A transformation matrix of this current image in the current reference space.\r\n     */\r\n    transformationMatrix: Matrix;\r\n    /**\r\n     * The width/height ratio of this image. can be used to calculate the size of the detected object/image\r\n     */\r\n    ratio?: number;\r\n}\r\n\r\n/**\r\n * Enum that describes the state of the image trackability score status for this session.\r\n */\r\nenum ImageTrackingScoreStatus {\r\n    // AR Session has not yet assessed image trackability scores.\r\n    NotReceived,\r\n    // A request to retrieve trackability scores has been sent, but no response has been received.\r\n    Waiting,\r\n    // Image trackability scores have been received for this session\r\n    Received,\r\n}\r\n\r\n/**\r\n * Image tracking for immersive AR sessions.\r\n * Providing a list of images and their estimated widths will enable tracking those images in the real world.\r\n */\r\nexport class WebXRImageTracking extends WebXRAbstractFeature {\r\n    /**\r\n     * The module's name\r\n     */\r\n    public static readonly Name = WebXRFeatureName.IMAGE_TRACKING;\r\n    /**\r\n     * The (Babylon) version of this module.\r\n     * This is an integer representing the implementation version.\r\n     * This number does not correspond to the WebXR specs version\r\n     */\r\n    public static readonly Version = 1;\r\n\r\n    /**\r\n     * This will be triggered if the underlying system deems an image untrackable.\r\n     * The index is the index of the image from the array used to initialize the feature.\r\n     */\r\n    public onUntrackableImageFoundObservable: Observable<number> = new Observable();\r\n    /**\r\n     * An image was deemed trackable, and the system will start tracking it.\r\n     */\r\n    public onTrackableImageFoundObservable: Observable<IWebXRTrackedImage> = new Observable();\r\n    /**\r\n     * The image was found and its state was updated.\r\n     */\r\n    public onTrackedImageUpdatedObservable: Observable<IWebXRTrackedImage> = new Observable();\r\n\r\n    private _trackableScoreStatus: ImageTrackingScoreStatus = ImageTrackingScoreStatus.NotReceived;\r\n    private _trackedImages: IWebXRTrackedImage[] = [];\r\n\r\n    private _originalTrackingRequest: XRTrackedImageInit[];\r\n\r\n    /**\r\n     * constructs the image tracking feature\r\n     * @param _xrSessionManager the session manager for this module\r\n     * @param options read-only options to be used in this module\r\n     */\r\n    constructor(\r\n        _xrSessionManager: WebXRSessionManager,\r\n        /**\r\n         * read-only options to be used in this module\r\n         */\r\n        public readonly options: IWebXRImageTrackingOptions\r\n    ) {\r\n        super(_xrSessionManager);\r\n        this.xrNativeFeatureName = \"image-tracking\";\r\n    }\r\n\r\n    /**\r\n     * attach this feature\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public attach(): boolean {\r\n        return super.attach();\r\n    }\r\n\r\n    /**\r\n     * detach this feature.\r\n     * Will usually be called by the features manager\r\n     *\r\n     * @returns true if successful.\r\n     */\r\n    public detach(): boolean {\r\n        return super.detach();\r\n    }\r\n\r\n    /**\r\n     * Get a tracked image by its ID.\r\n     *\r\n     * @param id the id of the image to load (position in the init array)\r\n     * @returns a trackable image, if exists in this location\r\n     */\r\n    public getTrackedImageById(id: number): Nullable<IWebXRTrackedImage> {\r\n        return this._trackedImages[id] || null;\r\n    }\r\n\r\n    /**\r\n     * Dispose this feature and all of the resources attached\r\n     */\r\n    public dispose(): void {\r\n        super.dispose();\r\n        this._trackedImages.forEach((trackedImage) => {\r\n            trackedImage.originalBitmap.close();\r\n        });\r\n        this._trackedImages.length = 0;\r\n        this.onTrackableImageFoundObservable.clear();\r\n        this.onUntrackableImageFoundObservable.clear();\r\n        this.onTrackedImageUpdatedObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Extends the session init object if needed\r\n     * @returns augmentation object fo the xr session init object.\r\n     */\r\n    public async getXRSessionInitExtension(): Promise<Partial<XRSessionInit>> {\r\n        if (!this.options.images || !this.options.images.length) {\r\n            return {};\r\n        }\r\n        const promises = this.options.images.map((image) => {\r\n            if (typeof image.src === \"string\") {\r\n                return this._xrSessionManager.scene.getEngine()._createImageBitmapFromSource(image.src);\r\n            } else {\r\n                return Promise.resolve(image.src); // resolve is probably unneeded\r\n            }\r\n        });\r\n\r\n        try {\r\n            const images = await Promise.all(promises);\r\n\r\n            this._originalTrackingRequest = images.map((image, idx) => {\r\n                return {\r\n                    image,\r\n                    widthInMeters: this.options.images[idx].estimatedRealWorldWidth,\r\n                };\r\n            });\r\n\r\n            return {\r\n                trackedImages: this._originalTrackingRequest,\r\n            };\r\n        } catch (ex) {\r\n            Tools.Error(\"Error loading images for tracking, WebXRImageTracking disabled for this session.\");\r\n            return {};\r\n        }\r\n    }\r\n\r\n    protected _onXRFrame(_xrFrame: XRFrame) {\r\n        if (!_xrFrame.getImageTrackingResults || this._trackableScoreStatus === ImageTrackingScoreStatus.Waiting) {\r\n            return;\r\n        }\r\n\r\n        // Image tracking scores may be generated a few frames after the XR Session initializes.\r\n        // If we haven't received scores yet, then kick off the task to check scores and return immediately.\r\n        if (this._trackableScoreStatus === ImageTrackingScoreStatus.NotReceived) {\r\n            this._checkScoresAsync();\r\n            return;\r\n        }\r\n\r\n        const imageTrackedResults = _xrFrame.getImageTrackingResults();\r\n        for (const result of imageTrackedResults) {\r\n            let changed = false;\r\n            const imageIndex = result.index;\r\n\r\n            const imageObject = this._trackedImages[imageIndex];\r\n            if (!imageObject) {\r\n                // something went wrong!\r\n                continue;\r\n            }\r\n\r\n            imageObject.xrTrackingResult = result;\r\n            if (imageObject.realWorldWidth !== result.measuredWidthInMeters) {\r\n                imageObject.realWorldWidth = result.measuredWidthInMeters;\r\n                changed = true;\r\n            }\r\n\r\n            // Get the pose of the image relative to a reference space.\r\n            const pose = _xrFrame.getPose(result.imageSpace, this._xrSessionManager.referenceSpace);\r\n\r\n            if (pose) {\r\n                const mat = imageObject.transformationMatrix;\r\n                Matrix.FromArrayToRef(pose.transform.matrix, 0, mat);\r\n                if (!this._xrSessionManager.scene.useRightHandedSystem) {\r\n                    mat.toggleModelMatrixHandInPlace();\r\n                }\r\n                changed = true;\r\n            }\r\n\r\n            const state = result.trackingState;\r\n            const emulated = state === \"emulated\";\r\n\r\n            if (imageObject.emulated !== emulated) {\r\n                imageObject.emulated = emulated;\r\n                changed = true;\r\n            }\r\n            if (changed) {\r\n                this.onTrackedImageUpdatedObservable.notifyObservers(imageObject);\r\n            }\r\n        }\r\n    }\r\n\r\n    private async _checkScoresAsync(): Promise<void> {\r\n        if (!this._xrSessionManager.session.getTrackedImageScores || this._trackableScoreStatus !== ImageTrackingScoreStatus.NotReceived) {\r\n            return;\r\n        }\r\n\r\n        this._trackableScoreStatus = ImageTrackingScoreStatus.Waiting;\r\n        const imageScores = await this._xrSessionManager.session.getTrackedImageScores();\r\n        if (!imageScores || imageScores.length === 0) {\r\n            this._trackableScoreStatus = ImageTrackingScoreStatus.NotReceived;\r\n            return;\r\n        }\r\n\r\n        // check the scores for all\r\n        for (let idx = 0; idx < imageScores.length; ++idx) {\r\n            if (imageScores[idx] == \"untrackable\") {\r\n                this.onUntrackableImageFoundObservable.notifyObservers(idx);\r\n            } else {\r\n                const originalBitmap = this._originalTrackingRequest[idx].image;\r\n                const imageObject: IWebXRTrackedImage = {\r\n                    id: idx,\r\n                    originalBitmap,\r\n                    transformationMatrix: new Matrix(),\r\n                    ratio: originalBitmap.width / originalBitmap.height,\r\n                };\r\n                this._trackedImages[idx] = imageObject;\r\n                this.onTrackableImageFoundObservable.notifyObservers(imageObject);\r\n            }\r\n        }\r\n\r\n        this._trackableScoreStatus = imageScores.length > 0 ? ImageTrackingScoreStatus.Received : ImageTrackingScoreStatus.NotReceived;\r\n    }\r\n}\r\n\r\n//register the plugin\r\nWebXRFeaturesManager.AddWebXRFeature(\r\n    WebXRImageTracking.Name,\r\n    (xrSessionManager, options) => {\r\n        return () => new WebXRImageTracking(xrSessionManager, options);\r\n    },\r\n    WebXRImageTracking.Version,\r\n    false\r\n);\r\n"]}